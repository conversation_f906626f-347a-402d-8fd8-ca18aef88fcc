import pymysql
import os
import sys
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed

# Configuration
DB_HOST = '127.0.0.1'
DB_USER = 'db'
DB_PASSWORD = 'db'
DB_NAME = 'db'
DB_PORT = 32779

# Get the directory of the script
SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))

LOCK_FILE = os.path.join(SCRIPT_DIR, 'lock_file.lock')
INPUT_FILE = os.path.join(SCRIPT_DIR, 'image_files.txt')
OUTPUT_FILE = os.path.join(SCRIPT_DIR, 'output_file.txt')

CHUNK_SIZE = 2
NUM_THREADS = 16  # Number of concurrent threads

def acquire_lock(lock_file):
    """Attempt to acquire a lock by creating a lock file."""
    if os.path.exists(lock_file):
        return False
    with open(lock_file, 'w') as f:
        f.write('locked')
    return True

def release_lock(lock_file):
    """Release the lock by removing the lock file."""
    if os.path.exists(lock_file):
        os.remove(lock_file)

def connect_to_db():
    """Establish a connection to the MySQL database."""
    return pymysql.connect(
        host=DB_HOST,
        user=DB_USER,
        password=DB_PASSWORD,
        database=DB_NAME,
        port=DB_PORT
    )

def process_file_paths(file_paths):
    """Process a batch of file paths and return the results."""
    results = []
    db = connect_to_db()
    cursor = db.cursor()

    try:
        for file_path in file_paths:
            file_path = file_path.strip()
            file_name = os.path.basename(file_path)

            # Search in wp_posts table
            query = """
                SELECT ID FROM wp_posts
                WHERE guid LIKE %s AND post_type = 'attachment'
            """
            cursor.execute(query, ('%' + file_path + '%',))
            attachment_id = cursor.fetchall()

            if attachment_id:
                attachment_id = attachment_id[0][0]
            else:
                # Search in wp_postmeta table
                query_meta = """
                    SELECT post_id FROM wp_postmeta
                    WHERE meta_value LIKE %s
                """
                cursor.execute(query_meta, ('%' + file_name + '%',))
                attachment_id = cursor.fetchall()
                attachment_id = attachment_id[0][0] if attachment_id else 'Not found'

            results.append(f"{file_path} : {attachment_id}")
            # sleep(0.25)

    finally:
        cursor.close()
        db.close()

    return results

def main():
    # Acquire lock
    if not acquire_lock(LOCK_FILE):
        print("Another instance of the script is already running. Exiting...")
        sys.exit(1)
    
    try:
        # Read file paths
        with open(INPUT_FILE, 'r') as input_file:
            lines = input_file.readlines()

        total_lines = len(lines)
        processed_lines = 0

        # Process file paths in chunks with threading
        with open(OUTPUT_FILE, 'w') as output_file:
            with ThreadPoolExecutor(max_workers=NUM_THREADS) as executor:
                futures = []
                for i in range(0, total_lines, CHUNK_SIZE):
                    chunk = lines[i:i + CHUNK_SIZE]
                    futures.append(executor.submit(process_file_paths, chunk))

                for future in as_completed(futures):
                    results = future.result()
                    output_file.write('\n'.join(results) + '\n')
                    processed_lines += len(results)
                    print(f"Processed {processed_lines}/{total_lines} lines.")

    finally:
        # Release lock
        release_lock(LOCK_FILE)

if __name__ == "__main__":
    main()