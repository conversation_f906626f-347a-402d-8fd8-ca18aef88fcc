from PIL import Image
import os

def convert_images_to_webp(input_file):
    # Base directory to prepend to the paths
    base_dir = 'wp-content/uploads/'

    # Read the image file paths from the input file
    with open(input_file, 'r') as file:
        image_paths = file.readlines()
    
    total_images = len(image_paths)
    
    # Process each image file path
    for index, image_path in enumerate(image_paths):
        image_path = image_path.strip()
        if not image_path:
            continue

        # Prepend the base directory to the image path
        full_image_path = os.path.join(base_dir, image_path)
        new_file_path = f"{full_image_path}.webp"

        # Check if the WebP file already exists
        if os.path.exists(new_file_path):
            print(f"Skipping {full_image_path}: WebP version already exists")
            continue

        # Open the image file
        try:
            with Image.open(full_image_path) as img:
                # Save the image as .webp with the original extension included in the filename
                img.save(new_file_path, format='webp')
                print(f"Processed {index + 1}/{total_images}: {full_image_path} -> {new_file_path}")
        except Exception as e:
            print(f"Error processing {full_image_path}: {e}")

if __name__ == "__main__":
    input_file = 'image_files.txt'  # Path to your text file with partial image paths
    convert_images_to_webp(input_file)