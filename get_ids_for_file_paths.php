<?php
// Load WordPress environment
require_once($_SERVER['DOCUMENT_ROOT'] . '/wp-load.php');

// Define the chunk size (number of lines to process per chunk)
$chunk_size = 10; // Updated chunk size
$lock_file = $_SERVER['DOCUMENT_ROOT'] . 'lock_file.lock';

function acquire_lock($lock_file) {
    if (file_exists($lock_file)) {
        return false; // Lock file exists, another instance might be running
    }
    file_put_contents($lock_file, 'locked');
    return true;
}

function release_lock($lock_file) {
    if (file_exists($lock_file)) {
        unlink($lock_file);
    }
}

function process_chunk($input_file, $output_file, $chunk_size, $start_line) {
    global $wpdb;
    $attachment_ids = array();

    // Open the input file for reading
    if ($handle = fopen($input_file, 'r')) {
        // Move to the starting line
        for ($i = 0; $i < $start_line; $i++) {
            fgets($handle);
        }

        // Read each line (file path) from the input file
        $current_line = 0;
        while (($file_path = fgets($handle)) !== false && $current_line < $chunk_size) {
            $file_path = trim($file_path);
            $file_name = basename($file_path); // Extract filename from the path

            // Prepare the file path for the query
            $file_path_esc = esc_sql($file_path);
            $file_name_esc = esc_sql($file_name);

            // Search in the wp_posts table
            $query = $wpdb->prepare(
                "SELECT ID FROM {$wpdb->posts} WHERE guid LIKE %s AND post_type = 'attachment'",
                '%' . $file_path_esc
            );
            $attachment_id = $wpdb->get_var($query);

            // If not found in wp_posts, search in wp_postmeta using filename with '%' wildcard
            if (!$attachment_id) {
                $query_meta = $wpdb->prepare(
                    "SELECT post_id FROM {$wpdb->postmeta} WHERE meta_value LIKE %s",
                    '%' . $file_name_esc . '%'
                );
                $attachment_id = $wpdb->get_var($query_meta);
            }

            // If still not found, return 'Not found'
            $attachment_ids[$file_path] = $attachment_id ? $attachment_id : 'Not found';

            $current_line++;
        }
        fclose($handle);
    } else {
        die('Error: Unable to open input file.');
    }

    // Open the output file for appending
    if ($output_handle = fopen($output_file, 'a')) {
        foreach ($attachment_ids as $file_path => $attachment_id) {
            fwrite($output_handle, $file_path . ' : ' . $attachment_id . PHP_EOL);
        }
        fclose($output_handle);
    } else {
        die('Error: Unable to open output file.');
    }

    return $current_line + $start_line; // Return the next starting line
}

// Main script logic
$input_file = $_SERVER['DOCUMENT_ROOT'] . '/image_files.txt';
$output_file = $_SERVER['DOCUMENT_ROOT'] . '/output_file.txt';

// Attempt to acquire lock
$lock_acquired = false;
while (!$lock_acquired) {
    if (acquire_lock($lock_file)) {
        $lock_acquired = true;
    } else {
        // Output error message and refresh
        echo "Script is already running. Please wait until the previous execution is complete.<br>";
        flush(); // Send the output to the browser
        sleep(5); // Wait for 5 seconds before refreshing
        header("Refresh: 5; url=get_ids_for_file_paths.php");
        exit; // Exit to prevent further execution while waiting
    }
}

// Send initial response
echo "Processing is running. Please wait while the script processes the data.<br>";
flush(); // Send the output to the browser

// Get starting line and process the chunk
$start_line = isset($_GET['start']) ? (int)$_GET['start'] : 0;
$total_lines = count(file($input_file));
$next_line = process_chunk($input_file, $output_file, $chunk_size, $start_line);

// Release lock
release_lock($lock_file);

// Send update to the browser
echo "Processed lines $start_line to $next_line of $total_lines.<br>";
flush(); // Send the output to the browser

// If there are more lines to process, redirect to this script
if ($next_line < $total_lines) {
    header("Refresh: 10; url=get_ids_for_file_paths.php?start=$next_line"); // Updated refresh time
} else {
    echo "Processing complete!";
}
?>