$(function() {

    var holidayIdField = $( "input[value='holiday']" );
    var holidayTitleField = $( "input[value='holiday_title']" );
    var holidayTypeField = $( "input[value='holiday_type']" );
    var tourCodeField = $( "input[value='tourcode']" );
    var itineraryField = $( "input[value='itinerary']" );
    var accommodationField = $( "input[value='accommodation']" );
    var startDateField = $( "input[value='startdate']" );

    jQuery(document).on("gform_post_render", function(event, form_id) {

        if(form_id !== 1) {

            // if($('.form__heading-wrapper').empty()) {
            //     $('.form').find('.gform_heading').appendTo('.form__heading-wrapper')
            // }

            $('.gfmc-column').append('<div class="form__next"><span class="button button--alt">Next <i class="fas fa-chevron-right"></i></span><div>');

            $('.form').find('.gform_footer').appendTo('.gfmc-column:last');

            if($('.gform_confirmation_wrapper').length) {
                $('.form__section').remove();
                $('.form__heading-wrapper').remove();
            }

            $('.gsection').first().addClass('active');
            $('.gsection').first().next().addClass('active');

            if($('.validation_message').length) {
                $('.validation_message').closest('.gfmc-column').addClass('active');
                $('.validation_message').closest('.gfmc-column').prev().addClass('active');
            }
        }

        if(form_id === 2) {

            holidayIdField.val($('.form__section').attr('data-id'));
            holidayTitleField.val($('.form__section').attr('data-title'));
            holidayTypeField.val($('.form__section').attr('data-type'));
            tourCodeField.val($('.tour-code').text());
            itineraryField.val($('#formItinerary').val());
            accommodationField.val($('#formAccommodation').val());
            startDateField.val($('#formStart').val());

            $('#formItinerary').on('change', function() {
                itineraryField.val($(this).val());
            });

            $('#formAccommodation').on('change', function() {
                accommodationField.val($(this).val());
            });

            $('#formStart').on('change', function() {
                startDateField.val($(this).val());
            });
        }

        if($('.validation_error').length) {
            $('.payment-choice').find('input').attr('checked', false);
        }

        $(document).find('.payment-choice').find('input').on('change', function() {

            if($(this).val().toLowerCase() !== 'bank transfer') {

                $('.form--payment').find('.gsection').show();

                $('.payment-details__notice').removeClass('active');

                $('.gfmc-row-2-column').addClass('active');
                $('.gfmc-row-2-column').prev().addClass('active');

            } else {

                $('.form--payment').find('.gsection').hide();
                $('.form--payment').find('.gfmc-column').removeClass('active');
                $('.form--payment').find('.gsection').removeClass('active');
                $('.payment-details__notice').addClass('active');
                $('.gsection').first().addClass('active');
            }

        });
    });

    gform.addFilter( 'gform_datepicker_options_pre_init', function( optionsObj, formId, fieldId ) {
        // do stuff
        optionsObj.yearRange = '-0:+5';
        return optionsObj;
    } );

    $(document).on('gform_confirmation_loaded', function(event, formId){
        if(formId === 2 || formId === 3) {
            if($('.gform_confirmation_wrapper').length) {
                $('.form__section').remove();
                $('.form__heading-wrapper').remove();
            }
        }
    });


    $(document).on('click', '.form__next .button', function() {

        var $this = $(this);
        //$(this).closest('.gfmc-column').prev().removeClass('active');
        $(this).closest('.gfmc-column').removeClass('active');

        // $(this).closest('.gfmc-column').next().addClass('active');
        // $(this).closest('.gfmc-column').next().next().addClass('active');

        if($(this).closest('.gfmc-column').next().is(':visible')) {

            $(this).closest('.gfmc-column').next().addClass('active');
            $(this).closest('.gfmc-column').next().next().addClass('active');
        } else {

            $(this).closest('.gfmc-column').next().next().next().addClass('active');
            $(this).closest('.gfmc-column').next().next().next().next().addClass('active');
        }

        $('html, body').animate({
            scrollTop: $this.closest('.gfmc-column').next().offset().top
        }, 0);

    });


    $(document).on('click', '.gsection', function() {

        if(!$(this).next().hasClass('active')) {
            $(this).next().addClass('active');
            $(this).addClass('active');
        } else {
            $(this).next().removeClass('active');
            //$(this).removeClass('active');
        }
    });


    $('#filterTrigger').on('click', function(e) {
        e.preventDefault();

        $('.filter').slideToggle();

    });

    $('.enquiry-form').on('click', function(e) {
        if (e.target !== this)
        return;

        $(this).removeClass('active');

    });


    $('.enquiry-cta__button').on('click', function(e) {

        e.preventDefault();

        $('.enquiry-form').toggleClass('active');

    });

    $('.enquiry-form__close').on('click', function() {

        $('.enquiry-form').removeClass('active');

    });




});
