// Add geotargeting to forms

document.addEventListener('DOMContentLoaded', function (e) {

    geoip_detect.get_info().then(function(record) {
        if (record.error()) {
            console.error('WARNING Geodata Error:' + record.error() );
            console.log(record.error());
            return;
        }

        // Return the name of the country
        const geoipValue = record.get_with_locales('country.isoCode', ['en']);

        const forms = document.querySelectorAll('form[id^="gform_"]');

        forms.forEach(form => {
            const geoipElement = form.querySelector('.geoip_country');
            if (geoipElement) {
                const inputElement = geoipElement.querySelector('input');
                if (inputElement) {
                    inputElement.value = geoipValue;
                }
            }
        });

    }).catch(error => {
        console.error('Error in geoip detection:', error);
    });

});