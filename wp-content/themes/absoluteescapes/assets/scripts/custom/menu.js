$(function () {
    var $masthead = $(".masthead");
    var $burgerMenu = $(".masthead__burger");
    var $mastheadNav = $(".navigation");

    $burgerMenu.on("click", function () {
        $(this).toggleClass("active");

        if ($(this).hasClass("active")) {
            $masthead.addClass("active-nav");
            $mastheadNav.addClass("active");
            $("html, body").addClass("overflow-menu");
        } else {
            $masthead.removeClass("active");
            $mastheadNav.removeClass("active");
            $("html, body").removeClass("overflow-menu");
        }
    });
    // console.log('test');
    // $('.masthead__navigation li.menu-item-has-children li.menu-item-has-children').each(function(event){
    //     console.log($(this));
    // })

    $('.navigation__close').on('click', function () {
        $masthead.removeClass("active");
        $mastheadNav.removeClass("active");
        $mastheadNav.removeClass("submenu-active");
        $("html, body").removeClass("overflow-menu");
        $burgerMenu.removeClass('active');
        $('.sub-menu').removeClass('active');
    });

    $('.navigation').find('.menu-item-has-children').append('<span class="sub-arrow"></span>');


    $('.navigation').find('.sub-menu').each(function () {
        var prevTitle = $(this).prev().text();

        $(this).prepend('<li><a class="back-link" href="#">< ' + prevTitle + '</a></li>');
    });


    $(document).on('click', '.sub-arrow', function () {
        $(this).prev().addClass('active');
        $mastheadNav.addClass("submenu-active");
        $mastheadNav.scrollTop(0);
    });

    $(document).on('click', '.back-link', function () {
        $(this).parent().parent().removeClass('active');
        if ($(this).parent().parent().parent().parent().hasClass('menu')) {
            $mastheadNav.removeClass("submenu-active");
        }
        $mastheadNav.scrollTop(0);
    });

    $('.navigation').find("a[href*='#']").on('click', function () {
        if (!$(this).hasClass('back-link')) {
            $(this).next().addClass('active');
            $mastheadNav.addClass("submenu-active");
            $mastheadNav.scrollTop(0);

        }
    });

    var footerListAnimating = false;

    $('.mastfoot').find('.menu-item-has-children > a').on('click', function (e) {
        e.preventDefault();

        if (!footerListAnimating) {
            footerListAnimating = true;

            if (!$(this).hasClass('active')) {
                $(this).addClass('active');
                $(this).next('.sub-menu').slideDown(function () {
                    footerListAnimating = false;
                });
            } else {
                $(this).removeClass('active');
                $(this).next('.sub-menu').slideUp(function () {
                    footerListAnimating = false;
                });
            }
        }
    });

    var lastScrollTop = 0;
    $(window).on("scroll", function () {

        if(!$masthead.hasClass('masthead--static')) {

            var st = $(this).scrollTop();

            if (st > 150) {
                $masthead.addClass('is-fixed');

                if (st > lastScrollTop) {
                    if($masthead.hasClass('is-visible')) {
                        $masthead.addClass('is-hidden');
                        $masthead.removeClass('is-visible');
                    }
                } else {
                    $masthead.removeClass('is-hidden');
                    $masthead.addClass('is-visible');
                }

            } else if (st < 1) {
                $masthead.removeClass('is-fixed');
                $masthead.removeClass('is-hidden');
                $masthead.removeClass('is-visible');
            }


            lastScrollTop = st;
        }
    });



});
