.route {

    &__inner {
        position: relative;
        z-index: 2;
        padding: 65px 0;

        @media only screen and (max-width: map-get($grid-breakpoints-max, lg)) {
            padding: 45px 0;
        }
    }


    &__container {
        @media only screen and (max-width: map-get($grid-breakpoints-max, sm)) {
            padding: 0 20px;
        }
    }

    .accordion {
        &__item {
            flex: 0 0 100%;
            max-width: 100%;

            &:after {
                display: block;
                width: calc(100% - 30px);
                right: 0;
                left: 0;

                margin: 0 auto;
            }
        }
    }


    &__map-wrapper {
        padding: 50px 0;
        & iframe {
            border-width: 0;
        }
    }

    .acf-map {
        height: 425px;
    }

    iframe {
        width: 100%!important;
    }
}
