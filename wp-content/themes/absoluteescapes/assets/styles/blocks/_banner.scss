.banner {
    height: 100vh;
    max-height: 1000px;
    margin-bottom: -30px;

    @media only screen and (max-width: map-get($grid-breakpoints-max, xl)) {
        max-height: 860px;
    }

    @media only screen and (max-width: map-get($grid-breakpoints-max, lg)) {
        max-height: 640px;
    }

    @media only screen and (max-width: map-get($grid-breakpoints-max, md)) {
        max-height: 520px;
    }

    &__background {
        position: relative;
        height: 100%;
        background-size: cover;
        background-position: center;
        background-repeat: no-repeat;
        overflow: hidden;

        // Gradient overlay for single slide banners
        &::before {
            content: "";
            display: block;
            position: absolute;
            top: 0;
            right: 0;
            bottom: 180px;
            left: 0;
            z-index: 1;
            width: 100%;
            height: 0;
            margin: auto 0;
        }

        &:after {
            content: "";
            display: block;
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 90px;
            background: url(../img/banner-mask.svg) center top no-repeat;
            background-size: 100% 100%;
            z-index: 2;

            @media only screen and (max-width: map-get($grid-breakpoints-max, lg)) {
                height: 60px;
            }

            @media only screen and (max-width: map-get($grid-breakpoints-max, md)) {
                height: 45px;
            }

            @media only screen and (max-width: map-get($grid-breakpoints-max, sm)) {
                height: 30px;
            }
        }
    }

    &__video {
        position: absolute;
        width: 100%;
        height: 100%;
        z-index: 0;
        padding: 0;
        pointer-events: none;
    }

    &__video iframe,
    &__video object,
    &__video embed {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        height: 56.25vw;
        width: 1000vh;
        min-height: 100%;
        min-width: 100%;
    }

    &__inner {
        display: flex;
        flex-wrap: wrap;
        align-items: center;
        position: relative;
        height: 100%;

        @media only screen and (max-width: map-get($grid-breakpoints-max, sm)) {
            align-items: flex-end;
        }
    }

    &__content {
        position: relative;
        z-index: 5;
        padding-bottom: 100px;

        @media only screen and (max-width: map-get($grid-breakpoints-max, md)) {
            max-width: 400px;
            padding-bottom: 125px;

        }
    }

    &__heading {
        margin-bottom: 40px;
        color: $white;
        font-family: $headingfontfamily;
        font-weight: 400;
        font-size: 6.6rem;
        line-height: 1.05;
        position: relative;
        z-index: 6;

        @media only screen and (max-width: map-get($grid-breakpoints-max, lg)) {
            font-size: 5rem;
        }

        @media only screen and (max-width: map-get($grid-breakpoints-max, md)) {
            margin-bottom: 20px;
            font-size: 3rem;
        }

        @media only screen and (max-width: map-get($grid-breakpoints-max, sm)) {
            font-size: 2.5rem;
        }

        // Ensure H2 in slideshow looks the same as H1
        &:is(h2) {
            font-size: 6.6rem;
            line-height: 1.05;

            @media only screen and (max-width: map-get($grid-breakpoints-max, lg)) {
                font-size: 5rem;
            }

            @media only screen and (max-width: map-get($grid-breakpoints-max, md)) {
                font-size: 3rem;
            }

            @media only screen and (max-width: map-get($grid-breakpoints-max, sm)) {
                font-size: 2.5rem;
            }
        }
    }

    &__link-wrapper {
        margin: 0 10px 15px;
    }

    &__link {
        min-width: 230px;
    }

    &__scroll-down {
        position: absolute;
        right: 0;
        bottom: 75px;
        left: 0;
        width: 120px;
        height: 60px;
        padding-top: 40px;
        margin: 0 auto;
        text-align: center;
        cursor: pointer;
        z-index: 10;

        @media only screen and (max-width: map-get($grid-breakpoints-max, lg)) {
            bottom: 45px;
        }

        @media only screen and (max-width: map-get($grid-breakpoints-max, md)) {
            bottom: 30px;
        }

        @media only screen and (max-width: map-get($grid-breakpoints-max, sm)) {
            bottom: 15px;
        }

        &:hover,
        &:focus {
            svg {
                color: $teal;
            }
        }

        svg {
            position: relative;
            z-index: 2;
            font-size: 3rem;
            transition: 300ms;
        }

        &:after {
            content: "";
            display: block;
            position: absolute;
            top: 0;
            right: 0;
            left: 0;
            z-index: 1;
            width: 0;
            height: 0;
            border-top: 0 solid transparent;
            border-right: 60px solid transparent;
            border-bottom: 60px solid $white;
            border-left: 60px solid transparent;
            margin: 0 auto;
        }
    }

    &__slideshow {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        z-index: 0;

        .flickity-viewport {
            height: 100%;
            overflow: hidden;
        }

        .flickity-slider {
            height: 100%;
            // Override Flickity's default transform for fade effect
            transform: none !important;
        }

        // Hide navigation buttons for auto-play slideshow
        .flickity-button {
            display: none;
        }

        // Hide page dots
        .flickity-page-dots {
            display: none;
        }

        // Fade effect implementation - override Flickity positioning for fade effect
        &.fade-enabled {
            .banner__slide {
                position: absolute !important;
                top: 0 !important;
                left: 0 !important;
                width: 100% !important;
                height: 100% !important;
                opacity: 0;
                transition: opacity 1s ease-in-out;
                transform: none !important;

                &.is-selected {
                    opacity: 1;
                    z-index: 1;
                }
            }
        }
    }

    &__slide {
        width: 100%;
        height: 100%;
        background-size: cover;
        background-position: center;
        background-repeat: no-repeat;
        position: relative;
        overflow: hidden;

        // Gradient overlay for slideshow slides - same as single slide banners
        &::before {
            content: "";
            display: block;
            position: absolute;
            top: 0;
            right: 0;
            bottom: 180px;
            left: 0;
            z-index: 1;
            width: 100%;
            height: 0;
            margin: auto 0;
            box-shadow: 0 0 230px 100px rgba($black, 0.55);
        }
    }





    #slideshow {
        position: absolute;
        top: 0;
        left: 0;
    }
}
