.destinations {

    a {
        text-decoration: none;

        &:hover, &:focus {
            text-decoration: none;
        }
    }

    &__inner {
        padding: 50px 0;
    }
    
    &__country {
        text-align: left;
    }

    &__row {
        margin: 0 -15px;
    }

    &__col {
        width: 33.333333%;
        padding: 0 15px;

        @media only screen and (max-width: map-get($grid-breakpoints-max, lg)) {
            width: 260px; 
            padding: 0 10px;

        }
    }

    &__carousel {
        padding-bottom: 50px;

        @media only screen and (max-width: map-get($grid-breakpoints-max, lg)) {
            padding-bottom: 30px;
        }
    }

    &__carousel-heading {
        margin-bottom: 15px;
    }

    &__link-text {
        margin: 8px 0 0;
        transition: 300ms;
    }

    &__link {
        display: block;
        position: relative;

        &:hover, &:focus {
            .destinations__link-text {
                color: $blue;
            }
        }
    }


    .flickity-button {
        top: calc(50% - 12px);
        border-color: $blue;
        background: $blue;
        color: $white;
        box-shadow: 0 0 25px rgba($black, 0.35);

        &:hover, &:focus {
            border-color: $teal;
            background: $teal;
            color: $white;
        }

        &:disabled {
            opacity: 0;
        }

        &.previous {
            left: -20px;
        }

        &.next {
            right: -20px;
        }
    }
}