/* Gravity Forms
================ */

.gform_confirmation_wrapper {
    padding: 30px 0;
    text-align: center;
    font-size: 2.4rem;
    color: $bluegrey;
}

.gform_ajax_spinner {
    display: none;
}

.gsection_title {
    margin: 0;
    font-size: 3rem;

    @media only screen and (max-width: map-get($grid-breakpoints-max, md)) {
        font-size: 2rem;
    }
}


.gform_wrapper {
    position: relative;
    width: 100%;
    max-width: 800px;
    padding: 0 0 30px;
    margin: 0 auto;

    ul {
        list-style: none;
    }

    .gform_fields {
        font-size: 0;
    }

    .gform_body {
        position: relative;
    }

    .validation_error {
        display: block;
        text-align: center;
        margin-bottom: 30px;
        color: $fail;
    }

    .validation_message {
        display: block;
        margin: 0 auto;
        text-align: center;
        font-size: 1.6rem;
        color: $fail;
    }

    ul,
    li {
        padding: 0;
        margin: 0;
    }

    .gfield_label {
        display: none;
        font-size: 1.8rem;
        color: $bluegrey;
    }

    .gfield {
        margin-bottom: 20px;

        &.show-label {
            .gfield_label {
                display: block;

            }
        }

        &.hidden_label {
            .gfield_label {
                display: none;
            }
        }
    }

    .gfield_description {
        margin-bottom: 10px;
    }

    .gform_hidden {

        margin: 0;
    }

    .gsection {
        display: block;
        padding: 25px;
        margin: 0;
        line-height: 1;
        border: 1px solid $midlightgrey;
        background: $offwhitethree;
        cursor: pointer;
    }

    .gfmc-column {
        padding: 40px 25px;
        border-right: 1px solid $midlightgrey;
        border-left: 1px solid $midlightgrey;
        line-height: 1;
    }

    .gform_footer {
        input {
            display: block;
            max-width: 190px;
            margin: 35px auto 0;
        }
    }

    .ginput_container_number {
        input {
            max-width: 70px;
        }
    }

    .choice-box {
        input[type="checkbox"],
        input[type="radio"] {
            + label {
                display: block;
                position: relative;
                padding: 10px 25px;
                margin: 0;
                box-sizing: border-box;
                border-radius: 4px;
                font-size: 2rem;
                font-weight: bold;
                line-height: 1.4;
                color: $bluegrey;
                border: 1px solid $midlightgrey;
                cursor: pointer;


                @media only screen and (max-width: map-get($grid-breakpoints-max, lg)) {
                    padding: 10px 15px;
                    font-size: 1.6rem;
                }

                &:before,
                &:after {
                    display: none;
                }
            }

            &:checked {
                + label {

                    color: $white;
                    border-color: $teal;
                    background: $teal;
                }
            }
        }

        .gfield_checkbox,
        .gfield_radio {
            display: flex;
            flex-wrap: wrap;
            justify-content: space-between;
            margin: 0 -5px;

            @media only screen and (max-width: map-get($grid-breakpoints-max, md)) {
                justify-content: flex-start;
            }

            li {
                flex: 1 1 auto;
                padding: 0 5px;
                margin: 0 0 5px;
                text-align: center;
            }
        }
    }
}

input {
    width: 100%;
    height: 50px;
    padding: 0 15px;
    border-radius: 0;
    border: 1px solid $midgrey;
    font-size: 1.8rem;
    box-sizing: border-box;
    outline: 0 !important;
    box-shadow: none !important;
}

textarea {
    width: 100%;
    height: 200px;
    padding: 15px;
    border-radius: 0;
    border: 1px solid $midgrey;
    font-size: 1.8rem;
    box-shadow: none !important;
    box-sizing: border-box;
    outline: 0 !important;
}

.gform_validation_container {
    display: none;
}


.gfield_radio,
.gfield_checkbox {
    > li {
        margin-bottom: 10px;
    }
}

input[type="radio"] {
    position: absolute;
    opacity: 0;
    left: -999999px;
    cursor: pointer;
    height: auto;
    appearance: none;

    + label {
        display: block;
        position: relative;
        padding-left: 25px;
        box-sizing: border-box;
        font-size: 1.6rem;
        line-height: 1.4;
        color: $bluegrey;

        &:before {
            content: "";
            display: inline-block;
            position: absolute;
            top: 5px;
            left: 0;
            margin: 0;
            width: 13px;
            height: 13px;
            border: 1px solid $bluegrey;
            border-radius: 50%;
            background: $white;
            box-sizing: border-box;
            cursor: pointer;
        }
    }

    &:checked {
        + label {
            &:after {
                content: "";
                display: inline-block;
                position: absolute;
                left: 4px;
                top: 9px;
                margin: 0;
                width: 5px;
                height: 5px;
                background: $bluegrey;
                border-radius: 50%;
                vertical-align: middle;
            }

            &:before {
                border-color: $bluegrey;
            }
        }
    }
}

input[type="checkbox"] {
    position: absolute;
    opacity: 0;
    left: -999999px;
    cursor: pointer;
    height: auto;

    + label {
        display: block;
        position: relative;
        padding-left: 25px;
        box-sizing: border-box;
        line-height: 1.4;
        font-size: 1.6rem;
        color: $bluegrey;

        &:before {
            content: "";
            display: inline-block;
            position: absolute;
            top: 5px;
            left: 0;
            width: 13px;
            height: 13px;
            border: 1px solid $bluegrey;
            border-radius: 0;
            box-sizing: border-box;
            cursor: pointer;
            background: $white;
        }
    }

    &:checked {
        + label {
            &:after {
                content: "";
                display: inline-block;
                position: absolute;
                bottom: auto;
                margin: auto 0;
                left: 4px;
                top: 6px;
                width: 5px;
                height: 9px;
                border: solid $bluegrey;
                border-width: 0 2px 2px 0;

                transform: rotate(45deg);
            }

            &:before {
                border-color: $black;
            }
        }
    }
}

.ginput_container_select {
    display: inline-block;
    vertical-align: middle;
    position: relative;

    &:after {
        content: "";
        display: block;
        position: absolute;
        top: -2px;
        right: 15px;
        bottom: 0;
        width: 7px;
        height: 7px;
        margin: auto 0;
        border-right: 2px solid $teal;
        border-bottom: 2px solid $teal;
        transform: rotate(45deg);
        pointer-events: none;
    }
}

.select-wrapper {
    display: inline-block;
    vertical-align: middle;
    position: relative;

    &:after {
        content: "";
        display: block;
        position: absolute;
        top: -2px;
        right: 15px;
        bottom: 0;
        width: 7px;
        height: 7px;
        margin: auto 0;
        border-right: 2px solid $teal;
        border-bottom: 2px solid $teal;
        transform: rotate(45deg);
        pointer-events: none;
    }
}

select {
    color: $bluegrey;
    padding: 0 45px 0 10px;
    margin: 0;
    text-overflow: "";
    font-size: 1.6rem;
    font-weight: 600;
    border: none;
    border: 1px solid $bluegrey;
    border-radius: 0;
    width: 100%;
    height: 40px;
    outline: 0 !important;
    appearance: none;
    /* SVG background image */
    // background: url("data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' width='100' height='100' fill='%2301a59f'><polygon points='0,0 100,0 50,50'/></svg>")
    //     no-repeat;
    // background-size: 12px;
    // background-position: calc(100% - 10px) center;
    // background-repeat: no-repeat;
    // background-color: none;
}

select::-ms-expand {
    display: none;
}
