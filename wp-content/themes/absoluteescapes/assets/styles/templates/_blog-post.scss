.blog-post {  
    position: relative;

    .share {
        padding-top: 15px;
    }

    &__layout {
        display: flex;
        gap: 60px;
        position: relative;

        @media only screen and (max-width: map-get($grid-breakpoints-max, md)) {
            flex-direction: column;
            gap: 40px;
        }
    }

    &__main {
        flex: 1;
        min-width: 0; // Prevents flex item from overflowing
    }

    &__container {
        display: flex;
    }

    .sidebar {
        width: 300px;
        flex-shrink: 0;
        padding-bottom: 50px;
        margin-left: 40px;

        @media only screen and (max-width: map-get($grid-breakpoints-max, md)) {
            width: 100%;
            padding-top: 30px;
            margin-left: 0;
        }

        &__inner {
            position: sticky;
            background-color: #eaf2f1;
            padding: 30px;
            top: 175px;
            
            &::after {
                content: '';
                position: absolute;
                bottom: -2px;
                left: 0;
                width: 100%;
                height: 17px;
                background-image: url('../img/banner-mask-small.svg');
                background-size: 100% 100%;
                background-repeat: no-repeat;
                background-position: bottom center;
                z-index: 1;
            }

            .svg-inline--fa {
                transform: scale(1.33);
                margin-right: 10px;
            }
        }

        &__spacer {
            width: 300px;
            margin-left: 40px;
            @media only screen and (max-width: map-get($grid-breakpoints-max, md)) {
                width: 100%;
                margin-left: 0;
            }
        }
    }


    &__details {
        padding: 20px 0 0;
        margin-bottom: -20px;
    }

    &__detail {
        display: inline-block;
        vertical-align: middle;
        font-family: $headingfontfamily; 
        text-transform: uppercase;
        color: $blue;

        span {
            display: inline-block;
            vertical-align: middle;
        }

        &:last-child {
            &:after {
                display: none;
            }
        }

        &:after {
            content: "•";
            display: inline-block;
            margin: 0 5px 0;
        }
    }

    .inner-container {
        max-width: 1090px;
        display: block;
        width: 100%;

        @media only screen and (min-width: map-get($grid-breakpoints-max, md)) {
            display: flex;
        }
    }

    .inner-wrapper {
        width: 100%;
    }

    iframe.instagram-media {
        margin: 30px 0!important;
        max-width: 100%!important;
    }
}