.filter-links {
    padding: 25px 0;

    @media only screen and (max-width: map-get($grid-breakpoints-max, md)) {
        padding: 15px 0;
    }

    &__text {
        padding-bottom: 15px;

        @media only screen and (max-width: map-get($grid-breakpoints-max, md)) {
            display: none;
        }
    }

    &__container {
        @media only screen and (max-width: map-get($grid-breakpoints-max, md)) {
            width: 100%;
            padding: 0;
        }
    }

    &__links {
        display: flex;
        flex-wrap: wrap;
        align-items: center;
        margin: 0 -15px;

        @media only screen and (max-width: map-get($grid-breakpoints-max, md)) {
            display: block;
            margin: 0;
        }

        @media only screen and (max-width: map-get($grid-breakpoints-max, sm)) {
            margin: 0 -5px;
        }
    }

    &__link-wrapper {
        flex: 1 0;
        padding: 0 15px;

        @media only screen and (max-width: map-get($grid-breakpoints-max, md)) {
            padding: 0;
            margin-bottom: 15px;
        }

        @media only screen and (max-width: map-get($grid-breakpoints-max, sm)) {
            padding: 0 5px;
        }
    }

    &__link {
        display: block;
        padding: 10px 15px;
        font-size: 1.8rem;
        font-family: $headingfontfamily;
        font-weight: 400;
    }
}

.text-block {
    padding: 15px 0;

    &__heading {
        font-size: 4rem;

        @include media-breakpoint-down(sm) {
            font-size: 3rem;
        }
    }
}

.holidays-results {
    &__filter-trigger-wrapper {
        display: none;

        @media only screen and (max-width: map-get($grid-breakpoints-max, lg)) {
            display: block;
            flex: 0 0 50%;
            max-width: 50%;
            padding: 0 7px;

            #filterTrigger {
                display: block;
                padding: 11px 15px;
                font-size: 1.8rem;
            }
        }

        @media only screen and (max-width: 360px) {
            flex: 0 0 100%;
            max-width: 100%;
            margin-bottom: 15px;
        }
    }

    &__filter-triggers {
        display: none;

        @media only screen and (max-width: map-get($grid-breakpoints-max, lg)) {
            display: flex;
            flex-wrap: wrap;
            margin: 0 -7px;
        }
    }

    &__inner {
        padding: 35px 0 80px;
    }

    &__sort-by-text {
        vertical-align: middle;
        margin-right: 10px;
        color: $bluegrey;

        @media only screen and (max-width: map-get($grid-breakpoints-max, lg)) {
            display: none;
        }
    }

    &__sort-by-trigger-wrapper {
        @media only screen and (max-width: map-get($grid-breakpoints-max, lg)) {
            flex: 0 0 50%;
            max-width: 50%;
            padding: 0 7px;

            .select-wrapper {
                display: block;
            }

            select {
                border-radius: 5px;
                font-family: $headingfontfamily;
                font-size: 1.8rem;
                font-weight: 500;
            }
        }

        @media only screen and (max-width: 360px) {
            flex: 0 0 100%;
            max-width: 100%;
        }
    }

    &__order {
        display: flex;
        flex-wrap: wrap;
        align-items: center;
        justify-content: space-between;
        padding-bottom: 30px;

        @media only screen and (max-width: map-get($grid-breakpoints-max, lg)) {
            border-bottom: 1px solid $midlightgrey;
            padding-bottom: 15px;
            padding-top: 30px;
            margin-bottom: 30px;
        }
    }

    &__results {
        color: $bluegrey;

        @media only screen and (max-width: map-get($grid-breakpoints-max, lg)) {
            padding: 0 20px;
        }
    }

    &__tabs {
        display: flex;
        flex-wrap: wrap;
        padding-bottom: 40px;

        @media only screen and (max-width: map-get($grid-breakpoints-max, lg)) {
            display: none;
        }
    }

    &__tab {
        flex: 0 0 50%;
        max-width: 50%;
        padding: 16px 0;
        text-align: center;
        border-top: 2px solid transparent;
        border-bottom: 2px solid $bluegrey;
        background: #efefef;
        font-size: 2.2rem;
        font-family: $headingfontfamily;
        color: $bluegrey;
        cursor: pointer;

        &:hover,
        &:focus {
            background: $white;
        }

        &.active {
            border-top: 2px solid $bluegrey;
            border-left: 2px solid $bluegrey;
            border-right: 2px solid $bluegrey;
            border-bottom: 2px solid transparent;
            background: $white;
        }
    }

    &__container {
        @media only screen and (max-width: map-get($grid-breakpoints-max, sm)) {
            padding: 0;
            width: 100%;
            max-width: 100%;

            .row {
                margin: 0;
            }
        }
    }


    .container {
        .holidays-results__col {
            @media only screen and (max-width: map-get($grid-breakpoints-max, sm)) {
                padding: 0;
            }
        }
        .holidays-results__col--filter {
            @media only screen and (max-width: map-get($grid-breakpoints-max, sm)) {
                padding: 0 20px;
            }
        }
    }

    &__posts {
        display: none;

        &.active {
            display: block;
        }
    }

    .infoBox {
        max-width: 330px;
        transform: translateX(-50%) translateY(calc(-100% - 65px)) translateZ(0) !important;
        background: $white;

        &:after {
            content: "";
            display: block;
            position: absolute;
            bottom: 0;
            right: 0;
            left: 0;
            width: 0;
            height: 0;
            margin: 0 auto;
            border-top: 16px solid $white;
            border-right: 16px solid transparent;
            border-bottom: 0 solid transparent;
            border-left: 16px solid transparent;
            transform: translateY(100%);
        }
    }

    .marker {
        &__link {
            text-decoration: none;

            &:hover,
            &:focus {
                .marker__title {
                    color: $teal;
                }
            }
        }

        &__text {
            padding: 15px;
        }

        &__items {
            display: flex;
            flex-wrap: wrap;
        }

        &__item {
            flex: 0 0 33.33333%;
            max-width: 33.33333%;
        }

        &__gallery-image {
            width: 100%;
            height: 195px;
            background-size: cover;
            background-position: center;
            background-repeat: no-repeat;
        }

        &__title {
            margin-bottom: 0;
            transition: 300ms;
        }

        &__price {
            margin-bottom: 10px;
            font-size: 1.7rem;
            color: $teal;
        }
    }

    .cat {
        display: flex;
        flex-wrap: wrap;
        align-items: center;

        &__icon {
            width: 30px;
            height: 30px;

            svg {
                font-size: 1.4rem;
            }
        }

        &__text {
            flex: 1 0;
            font-size: 1rem;
            font-family: $headingfontfamily;
            font-weight: 500;
            color: $bluegrey;
        }
    }

    &__map {
        display: none;
        padding-bottom: 35px;
        border-bottom: 1px solid #afafaf;

        &.active {
            display: block;
        }
    }
}
