<?php

/**
 * <AUTHOR> Lane Agency
 * @copyright 2019 The Lane Agency
 * @version   1.0
 */

/*
Directory
- path to the current directory
 */
define('DIR', dirname(__FILE__));

/*
General Functions
- basic theme functions
 */

include_once(DIR . '/lib/functions/general.php');
include_once(DIR . '/lib/functions/helpers.php');
include_once(DIR . '/lib/functions/post-types.php');
include_once(DIR . '/lib/class/class-header-walker.php');


// TARGET FORM CURRENCY - WIP

// if (function_exists('geoip_detect2_get_info_from_current_ip')) {
//     $data = geoip_detect2_get_info_from_current_ip()->raw;

//     add_filter( 'gform_currency', 'geoip_currency' );
//     function geoip_currency( $currency ) {
//         $data = geoip_detect2_get_info_from_current_ip()->raw;
//         return $data[ 'extra' ][ 'currency_code' ];
//     }
// }

add_filter( 'rocket_lrc_exclusions', function( $exclusions ) {

	$exclusions[] = 'holiday-blocks-wrapper__col--enquiry-form';
	$exclusions[] = 'holiday-blocks-wrapper__inner';

return $exclusions;
} );

// Enqueue scripts and styles
function enqueue_analytics_tracking() {
    wp_enqueue_script(
        'analytics-tracking',
        get_template_directory_uri() . '/assets/scripts/custom/analytics-tracking.js',
        array(), // no dependencies
        '1.0.0',
        true // load in footer
    );
}
add_action('wp_enqueue_scripts', 'enqueue_analytics_tracking');

// Enqueue scripts and styles
function enqueue_form_geo() {
    wp_enqueue_script(
        'form-geo',
        get_template_directory_uri() . '/assets/scripts/custom/form-geo.js',
        array(), // no dependencies
        '1.0.0',
        true // load in footer
    );
}
add_action('wp_enqueue_scripts', 'enqueue_form_geo');

function add_tracking_code() { ?>
<!-- Hotjar Tracking Code for Site 5290174 (Absolute Escapes) -->
<script>
    (function(h,o,t,j,a,r){
        h.hj=h.hj||function(){(h.hj.q=h.hj.q||[]).push(arguments)};
        h._hjSettings={hjid:5290174,hjsv:6};
        a=o.getElementsByTagName('head')[0];
        r=o.createElement('script');r.async=1;
        r.src=t+h._hjSettings.hjid+j+h._hjSettings.hjsv;
        a.appendChild(r);
    })(window,document,'https://static.hotjar.com/c/hotjar-','.js?sv=');
</script>
<?php }
add_action('wp_head', 'add_tracking_code');
