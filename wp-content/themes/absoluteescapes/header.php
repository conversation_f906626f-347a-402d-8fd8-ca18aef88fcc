<!DOCTYPE html>
<html class="no-js" <?php language_attributes(); ?>>
<head>
<!-- Google Tag Manager -->
<script>(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
})(window,document,'script','dataLayer','GTM-PQTBPPB');</script>
<!-- End Google Tag Manager -->
    <meta HTTP-EQUIV="Content-type" content="text/html; charset=UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=9;IE=10;IE=Edge,chrome=1"/>
    <title><?php wp_title(); ?></title>
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=0"/>
    <?php if (class_exists('AITO_Widget')) {$aito_widget = new AITO_Widget();echo $aito_widget->output_css();} ?>
    <link rel="stylesheet" href="https://use.typekit.net/fbl8jlu.css">
    <?php wp_head(); ?>
</head>

<body <?php body_class(); ?>>
<!-- Google Tag Manager (noscript) -->
<noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-PQTBPPB"
height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>
<!-- End Google Tag Manager (noscript) -->
<?php

$site_logo = get_field('logo', 'options');
$mobile_logo = get_field('logo_mobile', 'options');
$tel = get_field('tel', 'options');
$contact_link = get_field('contact_link', 'options');
// $review_embed = get_field('review_embed', 'options');
$review_embed_fallback = get_field('review_embed_fallback', 'options');
$form_id = get_field('form_id');
$form_page = get_field('form_page');

?>

<div class="page-wrapper">

    <header class="masthead <?php if ($form_page) : ?>masthead--form-page<?php endif; ?>">
        <div class="masthead__inner">

            <?php if (!empty($site_logo)) : ?>
                <div class="masthead__logo masthead__logo--desktop">
                    <a href="<?php echo home_url('/'); ?>" class="masthead__logo-link">
                        <img src="<?php echo $site_logo['url']; ?>" alt="<?php echo $site_logo['alt']; ?>"
                             class="masthead__logo-image">
                    </a>
                </div>
            <?php endif; ?>

            <?php if ($mobile_logo) : ?>
                <div class="masthead__logo masthead__logo--mobile">
                    <a href="<?php echo home_url('/'); ?>" class="masthead__logo-link">
                        <img src="<?php echo $mobile_logo['url']; ?>" alt="<?php echo $mobile_logo['alt']; ?>"
                             class="masthead__logo-image">
                    </a>
                </div>
            <?php endif; ?>

            <?php if ($form_page) : ?>
                <?php if ($form_id == 999999) : ?>
                    <?php if (have_rows('enquiry_form_details', 'options')) : ?>
                        <div class="masthead__form-header">
                            <?php while (have_rows('enquiry_form_details', 'options')) : the_row(); ?>

                                <?php if (have_rows('points')) : $k = 1; ?>

                                    <div class="masthead__header-points">
                                        <?php while (have_rows('points')) : the_row(); ?>
                                            <?php

                                            $point = get_sub_field('point');
                                            ?>

                                            <?php if ($point) : ?>

                                                <div class="masthead__header-point-wrapper">
                                                    <div class="masthead__header-point">
                                                        <span class="masthead__header-point-number"><?php echo $k; ?></span>
                                                        <span class="masthead__header-point-text"><?php echo $point; ?></span>
                                                        <?php if ($k < 3) : ?>
                                                            <span class="masthead__header-point-icon">
														<i class="fal fa-chevron-double-right"></i>
													</span>
                                                        <?php endif; ?>
                                                    </div>

                                                </div>

                                            <?php endif; ?>
                                            <?php $k++; endwhile; ?>
                                    </div>
                                <?php endif; ?>
                            <?php endwhile; ?>
                        </div>
                    <?php endif; ?>
                <?php endif; ?>

            <?php else: ?>


                <div class="masthead__navigation">
                    <nav class="masthead__nav">
                        <?php wp_nav_menu(array('theme_location' => 'header', 'container' => false, 'walker' => new Header_Walker())); ?>
                    </nav>
                </div>

                <div class="masthead__secondary-navigation">
                    <nav class="masthead__secondary-nav">
                        <?php wp_nav_menu(array('theme_location' => 'header-secondary', 'container' => false)); ?>
                    </nav>
                </div>


            <?php endif; ?>

            <div class="masthead__details">
                <?php if ($tel) : ?>
                    <div class="masthead__details-item masthead__phone">

                        <div class="masthead__link-wrapper">
                            <a href="<?php echo $tel['url']; ?>" class="masthead__link">
                                <div class="masthead__trigger">
                                    <span class="masthead__trigger-icon block"><svg class="svg-inline--fa fa-phone-alt fa-w-16" aria-hidden="true" focusable="false" data-prefix="fal" data-icon="phone-alt" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" data-fa-i2svg=""><path fill="currentColor" d="M493.09 351.3L384.7 304.8a31.36 31.36 0 0 0-36.5 8.9l-44.1 53.9A350 350 0 0 1 144.5 208l53.9-44.1a31.35 31.35 0 0 0 8.9-36.49l-46.5-108.5A31.33 31.33 0 0 0 125 .81L24.2 24.11A31.05 31.05 0 0 0 0 54.51C0 307.8 205.3 512 457.49 512A31.23 31.23 0 0 0 488 487.7L511.19 387a31.21 31.21 0 0 0-18.1-35.7zM456.89 480C222.4 479.7 32.3 289.7 32.1 55.21l99.6-23 46 107.39-72.8 59.5C153.3 302.3 209.4 358.6 313 407.2l59.5-72.8 107.39 46z"></path></svg>
                                    <span class="masthead__trigger-text block"><?php _e("Phone", 'absoluteescapes'); ?></span>
                                </div>
                                <div class="masthead__trigger-desktop">
                                    <i class="fas fa-phone"></i><?php echo $tel['title']; ?>
                                </div>
                            </a>
                        </div>
                    </div>
                <?php endif; ?>

                <?php if (!$form_page) : ?>
                    <div class="masthead__details-item masthead__form-wrapper">
                        <div class="masthead__trigger search-trigger">
                            <span class="masthead__trigger-icon block"><svg class="svg-inline--fa fa-search fa-w-16" aria-hidden="true" focusable="false" data-prefix="fal" data-icon="search" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" data-fa-i2svg=""><path fill="currentColor" d="M508.5 481.6l-129-129c-2.3-2.3-5.3-3.5-8.5-3.5h-10.3C395 312 416 262.5 416 208 416 93.1 322.9 0 208 0S0 93.1 0 208s93.1 208 208 208c54.5 0 104-21 141.1-55.2V371c0 3.2 1.3 6.2 3.5 8.5l129 129c4.7 4.7 12.3 4.7 17 0l9.9-9.9c4.7-4.7 4.7-12.3 0-17zM208 384c-97.3 0-176-78.7-176-176S110.7 32 208 32s176 78.7 176 176-78.7 176-176 176z"></path></svg>
                            <span class="masthead__trigger-text block"><?php _e("Search", 'absoluteescapes'); ?></span>
                        </div>
                        <div class="masthead__form-container">
                            <form method="GET" class="masthead__form" action="<?php echo home_url(); ?>">
                                <input type="text" name="s" placeholder="Type search term">
                                <button type="submit"><i class="fal fa-search"></i></button>
                            </form>
                            <div class="masthead__form-close">
                                <i class="fal fa-times"></i>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>

                <?php if ($contact_link) : ?>
                    <div class="masthead__details-item masthead__contact-link">
                        <div class="masthead__link-wrapper">
                            <a href="<?php echo $contact_link['url']; ?>" <?php echo ($contact_link['target']) ? 'target="_blank"' : ''; ?>
                               class="masthead__link button"><?php echo $contact_link['title']; ?></a>
                        </div>
                    </div>
                <?php endif; ?>
                <?php if (!$form_page) : ?>
                    <div class="masthead__details-item masthead__details-item--burger">
                        <div class="masthead__burger masthead__trigger">
                            <div class="masthead__burger-wrapper masthead__trigger-icon">
                                <div class="masthead__burger-menu">
                                    <div class="masthead__burger-lines"></div>
                                </div>
                            </div>
                            <span class="masthead__trigger-text block"><?php _e("Menu", 'absoluteescapes'); ?></span>
                        </div>
                    </div>
                <?php endif; ?>
            </div>


        </div>

        <?php if (class_exists('AITO_Widget')) :
            $aito_widget = new AITO_Widget(); $widget_output = $aito_widget->output_widget();
            if (!$form_page && !empty($widget_output)) : ?>
                <?php $grey_review = get_field('grey_review_bar'); ?>
                <div class="review-bar <?php echo ($grey_review) ? 'review-bar--grey' : ''; ?>">
                    <div class="review-bar__review">
                        <?php echo $widget_output; ?>
                    </div>
                </div>
            <?php endif;
            endif;  ?>
    </header><!-- .masthead -->

    <div class="navigation">
        <div class="navigation__inner">
            <span class="navigation__close"><i class="fal fa-times"></i></span>
            <nav class="navigation__nav">
                <?php wp_nav_menu(array('theme_location' => 'header-mobile', 'container' => false)); ?>
            </nav>
        </div>
    </div>

