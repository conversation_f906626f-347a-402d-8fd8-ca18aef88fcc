<?php

/**
 *  Convert Hex to RGB
 */
function hex2rgb($hex)
{
    $hex = str_replace("#", "", $hex);

    if (strlen($hex) == 3) {
        $r = hexdec(substr($hex, 0, 1) . substr($hex, 0, 1));
        $g = hexdec(substr($hex, 1, 1) . substr($hex, 1, 1));
        $b = hexdec(substr($hex, 2, 1) . substr($hex, 2, 1));
    } else {
        $r = hexdec(substr($hex, 0, 2));
        $g = hexdec(substr($hex, 2, 2));
        $b = hexdec(substr($hex, 4, 2));
    }
    $rgb = array($r, $g, $b);
    //return implode(",", $rgb); // returns the rgb values separated by commas
    return $rgb; // returns an array with the rgb values
}



/**
 * Format string to url
 */
function seoUrl($string)
{
    //Lower case everything
    $string = strtolower($string);
    //Make alphanumeric (removes all other characters)
    $string = preg_replace("/[^a-z0-9_\s-]/", "", $string);
    //Clean up multiple dashes or whitespaces
    $string = preg_replace("/[\s-]+/", " ", $string);
    //Convert whitespaces and underscore to dash
    $string = preg_replace("/[\s_]/", "-", $string);
    return $string;
}


/**
 * Converts integer into words
 */
function convert_number_to_words($number)
{

    $hyphen = '-';
    $conjunction = ' and ';
    $separator = ', ';
    $negative = 'negative ';
    $decimal = ' point ';
    $dictionary = array(
        0 => 'zero',
        1 => 'one',
        2 => 'two',
        3 => 'three',
        4 => 'four',
        5 => 'five',
        6 => 'six',
        7 => 'seven',
        8 => 'eight',
        9 => 'nine',
        10 => 'ten',
        11 => 'eleven',
        12 => 'twelve',
        13 => 'thirteen',
        14 => 'fourteen',
        15 => 'fifteen',
        16 => 'sixteen',
        17 => 'seventeen',
        18 => 'eighteen',
        19 => 'nineteen',
        20 => 'twenty',
        30 => 'thirty',
        40 => 'fourty',
        50 => 'fifty',
        60 => 'sixty',
        70 => 'seventy',
        80 => 'eighty',
        90 => 'ninety',
        100 => 'hundred',
        1000 => 'thousand',
        1000000 => 'million',
        1000000000 => 'billion',
        1000000000000 => 'trillion',
        1000000000000000 => 'quadrillion',
        1000000000000000000 => 'quintillion'
    );

    if (!is_numeric($number)) {
        return false;
    }

    if (($number >= 0 && (int)$number < 0) || (int)$number < 0 - PHP_INT_MAX) {
        // overflow
        trigger_error(
            'convert_number_to_words only accepts numbers between -' . PHP_INT_MAX . ' and ' . PHP_INT_MAX,
            E_USER_WARNING
        );
        return false;
    }

    if ($number < 0) {
        return $negative . convert_number_to_words(abs($number));
    }

    $string = $fraction = null;

    if (strpos($number, '.') !== false) {
        list($number, $fraction) = explode('.', $number);
    }

    switch (true) {
        case $number < 21:
            $string = $dictionary[$number];
            break;
        case $number < 100:
            $tens = ((int)($number / 10)) * 10;
            $units = $number % 10;
            $string = $dictionary[$tens];
            if ($units) {
                $string .= $hyphen . $dictionary[$units];
            }
            break;
        case $number < 1000:
            $hundreds = $number / 100;
            $remainder = $number % 100;
            $string = $dictionary[$hundreds] . ' ' . $dictionary[100];
            if ($remainder) {
                $string .= $conjunction . convert_number_to_words($remainder);
            }
            break;
        default:
            $baseUnit = pow(1000, floor(log($number, 1000)));
            $numBaseUnits = (int)($number / $baseUnit);
            $remainder = $number % $baseUnit;
            $string = convert_number_to_words($numBaseUnits) . ' ' . $dictionary[$baseUnit];
            if ($remainder) {
                $string .= $remainder < 100 ? $conjunction : $separator;
                $string .= convert_number_to_words($remainder);
            }
            break;
    }

    if (null !== $fraction && is_numeric($fraction)) {
        $string .= $decimal;
        $words = array();
        foreach (str_split((string)$fraction) as $number) {
            $words[] = $dictionary[$number];
        }
        $string .= implode(' ', $words);
    }

    return $string;
}



/**
 * Wrapper to pass variable to template part
 *
 * @param string $file
 * @param object|array $var
 * @param string $suffix
 */
function get_component($file, $var = [], $suffix = '')
{

    if ($var) {
        set_query_var('data', $var);
    }

    get_template_part('lib/components/' . $file, $suffix);

    if ($var) {
        set_query_var('data', '');
    }
}

function getSrcSet($id) {

    // Credit: https://gist.github.com/joshuadavidnelson/eb4650aa1ee8da9c7d731960e9402e21

    $img_srcset   = wp_get_attachment_image_srcset($id, 'full');
    $srcset_array = explode(", ", $img_srcset);
    $images  = array();
    $x = 0;
  
    foreach ($srcset_array as $set) :
  
      $split = explode(" ",$set );
  
      if (!isset($split[0], $split[1])) continue;
  
      $images[$x]['src'] = $split[0];
      $images[$x]['width'] = str_replace('w', '', $split[1]);
  
      $x++;
  
    endforeach;
    
    // sort the array, ordered by width
    usort($images, function($a, $b) {
      return $a['width'] <=> $b['width'];
    });

    // Offset values down by one, to effectively increase the image resolution used.
    // This is a quick amd simple way to improve image quality on HiDPI devices.

    // Modify the last value to remove resolution suffix (so it points to original image)
    $original = [
        'src' => preg_replace('/-\d+x\d+/', '', end($images)['src']),
        'width' => end($images)['width']
    ];

    // Get the total number of elements in the array
    $total_elements = count($images);

    // Get the original 'src' value from the last element before it's shifted
    $last_src = $images[$total_elements - 1]['src'];

    // Remove the pattern (e.g., -100x100) from the 'src' value of the last element
    $original_src = preg_replace('/-\d+x\d+/', '', $last_src);

    // Shift the 'src' values down by one key. Starting key is $start

    $start = 2;
    for ($i = $start; $i < $total_elements - 1; $i++) {
        $images[$i]['src'] = $images[$i + 1]['src'];
    }

    // Set the modified 'src' value in the last element
    $images[$total_elements - 1]['src'] = $original_src;

    // write_log($images);

    return $images;
  }


function outputSrcset($image_id, $selector) {

    $css = '';
    $srcset = getSrcSet($image_id);

    $css .= ":root {--base64-image: url('".getBase64(wp_get_attachment_image_url($image_id, 'tiny'))."');}\n";

    foreach ($srcset as $set) :

        // skip big ones
        // if ($set['width'] > 1600) continue;

        $css .= "@media only screen and (min-width: " . $set["width"] . "px) {
        ". $selector ." { background-image: url(" . $set["src"] . "),var(--base64-image) } 
        }
        ";

    endforeach;

    $mobile_small = wp_get_attachment_image_url($image_id, 'small');
    $css .= "@media only screen and (min-width: 350px) and (orientation: portrait) {
        ". $selector ." { background-image: url(" . $mobile_small . "),var(--base64-image) } 
        }
        ";

    $css = (!empty($css)) ? '<style>' . $css . '</style>' : ''; 
    echo $css;

}

function getBase64($url) {
    require_once ABSPATH . 'wp-admin/includes/file.php';
    $local_path = rtrim(get_home_path(),'/').'/'.ltrim(parse_url($url)['path'],'/');
    // write_log($local_path);
    $parts = explode('.',$local_path);
    $type = $parts[count($parts)-1];
    $prefix = 'data:image/'.$type.';base64,';
    return $prefix. base64_encode(file_get_contents($local_path));
}