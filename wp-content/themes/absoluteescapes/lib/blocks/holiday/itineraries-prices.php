<?php

/**
 * Itineraries & Prices
 */

$navigation_label = seoUrl(get_sub_field('navigation_label'));
$heading = get_sub_field('heading');
$intro_copy = get_sub_field('intro_copy');
$copy = get_sub_field('copy');
$bg_colour = seoUrl(get_sub_field('background_colour'));

?>

<section id="<?php echo $navigation_label; ?>" class="itineraries-prices <?php if($bg_colour) : ?>bg-<?php echo $bg_colour; ?><?php endif; ?>">
    <div class="itineraries-prices__inner" data-aos="fade">
        <div class="itineraries-prices__container">
            <div class="itineraries-prices__content">
                <?php if ($heading) : ?>
                    <h2 class="itineraries-prices__heading"><?php echo $heading; ?></h2>
                <?php endif; ?>
                <?php if ($intro_copy) : ?>
                    <div class="itineraries-prices__copy copy-large">
                        <?php echo $intro_copy; ?>
                    </div>
                <?php endif; ?>
                <?php if ($copy) : ?>
                    <div class="itineraries-prices__copy">
                        <?php echo $copy; ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>
        <div class="itineraries-prices__container itineraries-prices__container--table">
            <div>
                <?php if (have_rows('rows')) : ?>
                    <div class="itineraries-prices__table">
                        <div class="itineraries-prices__table-row itineraries-prices__table-row--heading">
                            <div class="itineraries-prices__table-col itineraries-prices__table-col--desktop">
                                <span><?php _e('Tour code', 'absoluteescapes'); ?></span>
                            </div>
                            <div class="itineraries-prices__table-col">
                                <span><?php _e('Itinerary', 'absoluteescapes'); ?></span>
                            </div>
                            <div class="itineraries-prices__table-col itineraries-prices__table-col--desktop">
                                <span><?php _e('Average miles', 'absoluteescapes'); ?> <span class="tooltip"><img
                                                src="<?php echo get_stylesheet_directory_uri() . '/dist/img/info.svg' ?>"
                                                alt="info"><span
                                                class="tooltip__label"><?php _e('Average miles per day', 'absoluteescapes'); ?></span></span></span>
                            </div>

                            <div class="itineraries-prices__table-col itineraries-prices__table-col--desktop">
                                <span><?php _e('Difficulty', 'absoluteescapes'); ?> <span class="tooltip"><img
                                                src="<?php echo get_stylesheet_directory_uri() . '/dist/img/info.svg' ?>"
                                                alt="info"><span
                                                class="tooltip__label"><?php _e('Level of difficulty', 'absoluteescapes'); ?></span></span></span>
                            </div>
                            <div class="itineraries-prices__table-col">
                                <span><?php _e('Price per person', 'absoluteescapes'); ?></span>
                            </div>
                        </div>
                        <div class="accordion">
                            <?php while (have_rows('rows')) : the_row(); ?>
                                <?php

                                $itinerary = get_sub_field('itinerary');
                                $avg_miles = get_sub_field('avg_miles');
                                $tour_code = get_sub_field('tour_code');
                                $difficulty = get_sub_field('difficulty');
                                $difficulty_text = get_field('difficulty_level_' . $difficulty, 'options');
                                $price = get_sub_field('price');

                                ?>
                                <div class="accordion__item">
                                    <div class="accordion__heading-wrapper">
                                        <div class="itineraries-prices__table-row">
                                            <div class="itineraries-prices__table-col itineraries-prices__table-col--desktop">
                                                <span><?php echo $tour_code ?></span>
                                            </div>
                                            <div class="itineraries-prices__table-col">
                                                <span><?php echo $itinerary ?></span>
                                            </div>
                                            <div class="itineraries-prices__table-col itineraries-prices__table-col--desktop">
                                                <span><?php echo $avg_miles ?></span>
                                            </div>
                                            <div class="itineraries-prices__table-col itineraries-prices__table-col--desktop">
                                            <span class="itineraries-prices__difficulty-score">
                                                <?php for ($d = 0; $d < 4; $d++) : ?>
                                                    <span class="itineraries-prices__difficulty-icon">
                                                        <?php if ($d < $difficulty) : ?>
                                                            <i class="fas fa-boot"></i>
                                                        <?php endif; ?>
                                                    </span>
                                                <?php endfor; ?>
                                            </span>
                                                <span class="itineraries-prices__difficulty"><?php echo $difficulty_text; ?></span>
                                            </div>
                                            <div class="itineraries-prices__table-col">
                                                <span class="text-black"><strong><?php _e('From ', 'absoluteescapes'); ?><?php echo $price ?></strong></span>
                                            </div>

                                        </div>
                                        <i class="fas fa-chevron-down"></i>
                                    </div>
                                    <div class="accordion__copy-wrapper none">
                                        <div class="itineraries-prices__table-col itineraries-prices__table-col--mobile">

                                            <div class="itineraries-prices__table-col-item">
                                                    <span><?php _e('Average miles', 'absoluteescapes'); ?> <span
                                                                class="tooltip"><img
                                                                    src="<?php echo get_stylesheet_directory_uri() . '/dist/img/info.svg' ?>"
                                                                    alt="info"><span
                                                                    class="tooltip__label"><?php _e('Average miles per day', 'absoluteescapes'); ?></span></span>:&nbsp;</span>
                                                <span><?php echo $avg_miles ?></span>
                                            </div>

                                            <div class="itineraries-prices__table-col-item">
                                                <span><?php _e('Tour code: ', 'absoluteescapes'); ?></span>
                                                <span><?php echo $tour_code ?></span>
                                            </div>

                                            <div class="itineraries-prices__table-col-item">
                                                    <span><?php _e('Difficulty', 'absoluteescapes'); ?> <span
                                                                class="tooltip"><img
                                                                    src="<?php echo get_stylesheet_directory_uri() . '/dist/img/info.svg' ?>"
                                                                    alt="info"><span
                                                                    class="tooltip__label"><?php _e('Level of difficulty', 'absoluteescapes'); ?></span></span>:&nbsp;</span>
                                                <span class="itineraries-prices__difficulty-wrapper">
                                                <span class="itineraries-prices__difficulty-score">
                                                    <?php for ($d = 0; $d < 4; $d++) : ?>
                                                        <span class="itineraries-prices__difficulty-icon">
                                                            <?php if ($d < $difficulty) : ?>
                                                                <i class="fas fa-boot"></i>
                                                            <?php endif; ?>
                                                        </span>
                                                    <?php endfor; ?>
                                                </span>
                                                <span class="itineraries-prices__difficulty"><?php echo $difficulty_text; ?></span>
                                                </span>
                                            </div>
                                        </div>

                                        <?php if (have_rows('accordion_content')) : ?>
                                            <?php while (have_rows('accordion_content')) : the_row(); ?>
                                                <?php

                                                $top_heading = get_sub_field('heading');
                                                $copy = get_sub_field('copy');
                                                $footer_copy = get_sub_field('footer_copy');

                                                ?>
                                                <div class="itineraries-prices__expanded">
                                                    <span class="accordion__close"><i class="fal fa-times"></i></span>
                                                    <div class="itineraries-prices__row flex">
                                                        <div class="itineraries-prices__expanded-col">
                                                            <div class="itineraries-prices__copy">
                                                                <?php if ($top_heading) : ?>
                                                                    <h3 class="itineraries-prices__heading"><?php echo $top_heading; ?></h3>
                                                                <?php endif; ?>
                                                                <?php if ($copy) : ?>
                                                                    <div class="itineraries-prices__copy">
                                                                        <?php echo $copy; ?>
                                                                    </div>
                                                                <?php endif; ?>
                                                            </div>
                                                        </div>
                                                        <div class="itineraries-prices__expanded-col">
                                                            <?php if (have_rows('points')) : ?>
                                                                <div class="itineraries-prices__points">
                                                                    <?php while (have_rows('points')) : the_row(); ?>
                                                                        <?php

                                                                        $number = get_sub_field('number');
                                                                        $heading = get_sub_field('heading');
                                                                        $route = get_sub_field('route');
                                                                        $distance = get_sub_field('distance');

                                                                        ?>

                                                                        <div class="itineraries-prices__point">
                                                                            <div class="itineraries-prices__number-wrapper">
                                                                                <span class="itineraries-prices__number"><?php echo $number; ?></span>
                                                                            </div>
                                                                            <div class="itineraries-prices__text-wrapper">
                                                                                <?php if ($heading) : ?>
                                                                                    <span class="itineraries-prices__point-heading text-teal"><strong><?php echo $heading; ?></strong></span>
                                                                                <?php endif; ?>
                                                                                <?php if ($route) : ?>
                                                                                    <span class="itineraries-prices__text block"><?php echo $route; ?></span>
                                                                                <?php endif; ?>
                                                                                <?php if ($distance) : ?>
                                                                                    <span class="itineraries-prices__text block"><strong><?php echo $distance; ?></strong></span>
                                                                                <?php endif; ?>
                                                                            </div>
                                                                        </div>
                                                                    <?php endwhile; ?>
                                                                </div>
                                                            <?php endif; ?>
                                                        </div>
                                                    </div>
                                                    <?php if (have_rows('prices')) : ?>
                                                        <div class="itineraries-prices__prices">
                                                            <h3 class="itineraries-prices__heading"><?php _e('Prices', 'absoluteescapes'); ?></h3>

                                                            <div class="accordion">
                                                                <?php while (have_rows('prices')) : the_row(); ?>
                                                                    <?php

                                                                    $p_row_offwhite = get_sub_field('off_white_background');
                                                                    $type_heading = get_sub_field('heading');
                                                                    $venue = get_sub_field('venue');
                                                                    $price = get_sub_field('price');
                                                                    $copy = get_sub_field('copy');

                                                                    ?>

                                                                    <div class="accordion__item">
                                                                        <div class="accordion__heading-wrapper">
                                                                            <div class="itineraries-prices__prices-row <?php if($p_row_offwhite) : ?>itineraries-prices__prices-row--offwhite<?php endif; ?>">
                                                                                <div class="itineraries-prices__prices-col">
                                                                                    <span><?php echo $type_heading; ?></span>
                                                                                </div>
                                                                                <div class="itineraries-prices__prices-col">
                                                                                    <span><?php echo $venue; ?></span>
                                                                                </div>
                                                                                <div class="itineraries-prices__prices-col">
                                                                                    <span><?php echo $price; ?></span>
                                                                                </div>

                                                                            </div>
                                                                            <i class="fas fa-chevron-down"></i>
                                                                        </div>
                                                                        <div class="accordion__copy-wrapper none">
                                                                            <div class="accordion__copy content-area">
                                                                                <?php echo $copy; ?>

                                                                                <div class="accordion__button-wrapper">
                                                                                    <a href="<?php echo get_the_permalink(543) . '?itinerary=' . seoUrl($top_heading) . '&accommodationtype=' . seoUrl($type_heading) . '&holidayid=' . get_the_ID(); ?>" class="accordion__button button">Enquire Now</a>
                                                                                </div>
                                                                            </div>
                                                                        </div>

                                                                    </div>
                                                                <?php endwhile; ?>
                                                            </div>
                                                        </div>
                                                    <?php endif; ?>
                                                    <?php if ($footer_copy) : ?>
                                                        <div class="itineraries-prices__copy itineraries-prices__copy--footer">
                                                            <?php echo $footer_copy; ?>
                                                        </div>
                                                    <?php endif; ?>
                                                </div>
                                            <?php endwhile; ?>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            <?php endwhile; ?>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</section><!-- .itineraries-prices -->
