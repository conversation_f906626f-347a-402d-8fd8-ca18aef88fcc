<?php

/**
 * Addtional days
 */

$navigation_label = seoUrl(get_sub_field('navigation_label'));
$heading = get_sub_field('heading');
$intro_copy = get_sub_field('intro_copy');
$copy = get_sub_field('copy');
$bg_colour = seoUrl(get_sub_field('background_colour'));

?>

<section id="<?php echo $navigation_label; ?>" class="additional-days <?php if($bg_colour) : ?>bg-<?php echo $bg_colour; ?><?php endif; ?>">
    <div class="additional-days__inner" data-aos="fade">
        <div class="additional-days__container">
            <div class="additional-days__content">
                <?php if($heading) : ?>
                    <h2 class="additional-days__heading"><?php echo $heading; ?></h2>
                <?php endif; ?>
                <?php if($intro_copy) : ?>
                    <div class="addtional-days__copy copy-large"><?php echo $intro_copy; ?></div>
                <?php endif; ?>
                <?php if($copy) : ?>
                    <div class="addtional-days__copy">
                        <?php echo $copy; ?>
                    </div>
                <?php endif; ?>
                <?php if(have_rows('dropdowns')) : ?>
                    <div class="overview__highlights flex accordion">
                        <?php while(have_rows('dropdowns')) : the_row(); ?>
                            <?php

                            $heading = get_sub_field('heading');
                            $image = wp_get_attachment_image(get_sub_field('image'), 'accordion');
                            $copy = get_sub_field('copy');

                            ?>

                            <div class="overview__highlight accordion__item">
                                <div class="overview__highlight-heading-wrapper flex accordion__heading-wrapper">
                                    <?php if($image) : ?>
                                        <div class="overview__highlight-image-wrapper accordion__image-wrapper">
                                            <?php echo $image; ?>
                                        </div>
                                    <?php endif; ?>
                                    <div class="overview__highlight-heading-text accordion__heading-text">
                                        <?php if($heading) : ?>
                                            <h5 class="overview__highlight-heading accordion__heading"><?php echo $heading; ?></h5>
                                        <?php endif; ?>

                                        <span class="overview__link link"><span class="link-text"><?php _e('Read more', 'absoluteescapes'); ?></span> <i class="fas fa-chevron-down"></i></span>
                                    </div>
                                </div>
                                <?php if($copy) : ?>
                                    <div class="overview__highlight-copy-wrapper none accordion__copy-wrapper">
                                        <div class="overview__highlight-copy accordion__copy content-area">
                                            <?php echo $copy; ?>
                                        </div>
                                    </div>
                                <?php endif; ?>
                            </div>

                        <?php endwhile; ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</section><!-- .additional-days -->
