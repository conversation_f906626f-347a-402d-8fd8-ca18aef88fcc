<?php

/**
 * Related Posts
 */

$posts = get_field('related_blog_posts');

if(!$posts) {

    $posts = get_posts([
        'post_type' => 'post',
        'post_status' => 'publish',
        'numberposts' => 2,
        'orderby' => 'rand',
        'order' => 'ASC'
    ]);

}

$post_cats = wp_get_post_terms(get_the_ID(), 'holiday-type', array('fields' => 'ids'));

$holidays = get_posts([
    'post_type'     => 'holiday',
    'post_status'   => 'publish',
    'numberposts'   => 2,
    'orderby' => 'rand',
    'order'    => 'ASC',
    'post__not_in' => [get_the_ID()],
    'tax_query'     => [
        [
            'taxonomy'  => 'holiday-type',
            'field'     => 'id',
            'terms'     => $post_cats,
            'operator'  => 'IN'
        ]
    ]
]);

$bg_colour = seoUrl(get_sub_field('background_colour'));

?>

<section class="related-posts <?php if($bg_colour) : ?>bg-<?php echo $bg_colour; ?><?php endif; ?>">
    <div class="related-posts__inner" data-aos="fade">
        <div class="related-posts__container">
            <div class="related-posts__content">
                <h2 class="related-posts__heading h2-large heading-light heading-underlined text-weight-regular centre"><?php _e('Related blog posts', 'absoluteescapes'); ?></h2>
                <?php if($posts) : ?>
                    <div class="inspiration__posts">
                        <?php foreach($posts as $p) : ?>
                            <?php
                            if ( get_field( 'post_listing_image', $p->ID ) ) :
                                $image = get_field( 'post_listing_image', $p->ID );
                                $thumbnail = wp_get_attachment_image( $image, 'destination');
                            else:
                                $thumbnail = get_the_post_thumbnail($p->ID, 'destination');
                            endif;
                            ?>

                            <div class="inspiration__post">
                                <a href="<?php echo get_the_permalink($p->ID); ?>">
                                <div class="row inspiration__post-row">
                                    <div class="col-8 col-md-9 inspiration__post-col">
                                        <h3 class="inspiration__heading"><?php echo get_the_title($p->ID); ?></h3>
                                        <?php if(has_excerpt($p)) : ?>
                                            <div class="inspiration__excerpt content-area">
                                                <p><?php echo get_the_excerpt($p); ?> <span class="link"><span class="link-text"><?php _e('Read more', 'absoluteescapes'); ?></span></span></p>
                                            </div>
                                        <?php endif; ?>
                                        <span class="link mobile-only"><span class="link-text"><?php _e('Read more', 'absoluteescapes'); ?></span> <i class="fas fa-chevron-right"></i></span>
                                    </div>

                                    <div class="col-4 col-md-3 inspiration__post-col">
                                        <?php if($thumbnail) : ?>
                                            <div class="inspiration__thumbnail">
                                                <?php echo $thumbnail; ?>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                </div>
                                </a>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
                <?php if($holidays) : ?>
                <h2 class="related-posts__heading h2-large heading-light heading-underlined text-weight-regular centre"><?php _e('Similar holidays', 'absoluteescapes'); ?></h2>
                    <div class="holidays__posts">
                        <?php foreach($holidays as $holiday) : ?>
                            <?php

                            $price = get_field('holiday_price', $holiday);
                            $price_suffix_short = get_field('holiday_price_suffix_short', $holiday);
                            $distance = get_field('holiday_distance', $holiday);
                            $min_duration = get_field('holiday_minimum_duration', $holiday);
                            $max_duration = get_field('holiday_maximum_duration', $holiday);
                            if($max_duration == 999) {
                                $max_duration = '';
                            }
                            $types = get_the_terms($holiday, 'holiday-type');

                            ?>

                            <div class="holidays__post">

                                    <div class="row holidays__post-row">
                                        <div class="col-12 holidays__post-col">
                                            <?php if(have_rows('holiday_gallery', $holiday)) : ?>
                                                <div class="holidays__gallery">
                                                    <?php while(have_rows('holiday_gallery', $holiday)) : the_row(); ?>
                                                        <?php

                                                        $image = wp_get_attachment_image(get_sub_field('image'), 'carousel');

                                                        ?>

                                                        <?php if($image) : ?>
                                                            <div class="holidays__image">
                                                                <?php echo $image; ?>
                                                            </div>
                                                        <?php endif; ?>

                                                    <?php endwhile; ?>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                        <div class="col-12 holidays__post-col">
                                            <a href="<?php echo get_the_permalink($holiday); ?>">
                                            <div class="holidays__post-content">
                                                <h4 class="holidays__title"><?php echo get_the_title($holiday); ?></h4>
                                                <?php if($price) : ?>
                                                    <h4 class="holidays__price h4-small text-teal text-weight-semibold">
                                                        <span><?php _e('From', 'absoluteescapes'); ?> <?php echo $price; ?> <?php echo $price_suffix_short; ?></span>
                                                    </h4>
                                                <?php endif; ?>

                                                <?php

                                                $mtypes = get_the_terms($holiday->ID, 'holiday-type');
                                                $mDays = get_field('holiday_minimum_duration', $holiday->ID);
                                                $mxDays = get_field('holiday_maximum_duration', $holiday->ID);
                                                if($mxDays == 999) {
                                                    $mxDays = '';
                                                }

                                                if($mDays === $mxDays) {
                                                    $mxDays = '';
                                                }

                                                $nights_days = get_field('holiday_day_night', $holiday->ID);
                                                $mDistance = get_field('holiday_distance', $holiday->ID);

                                                ?>

                                                <div class="marker__items">
                                                    <?php if($mtypes) : ?>
                                                        <div class="marker__item">
                                                            <?php foreach($mtypes as $type) : ?>
                                                                <?php

                                                                $icon = get_field('holiday_type_icon', $type);
                                                                $icon_fa = get_field('holiday_type_icon_fa', $type);
                                                                $colour = get_field('holiday_type_colour', $type);


                                                                if($type->parent) {
                                                                    continue;
                                                                }

                                                                ?>

                                                                <div class="holidays__type cat">
                                                                    <?php if($icon) : ?>
                                                                        <span class="holidays__icon cat__icon" style="background-color: <?php echo ($colour) ? $colour : '#3e5056'; ?>"><img src="<?php echo $icon['url']; ?>" alt="<?php echo $icon['alt']; ?>"></span>
                                                                    <?php else : ?>
                                                                        <?php if($icon_fa) : ?>
                                                                            <span class="holidays__icon cat__icon" style="background-color: <?php echo ($colour) ? $colour : '#3e5056'; ?>"><i class="<?php echo $icon_fa; ?>"></i></span>
                                                                        <?php endif; ?>
                                                                    <?php endif; ?>
                                                                    <span class="holidays__type-text cat__text"><?php echo $type->name; ?></span>
                                                                </div>
                                                            <?php endforeach; ?>
                                                        </div>
                                                    <?php endif; ?>
                                                    <?php if($mDays) : ?>
                                                        <div class="marker__item">
                                                            <div class="cat">
                                                                <span class="cat__icon" style="background-color: #3e5056;"><i class="fal fa-calendar-alt"></i></span>
                                                                <span class="cat__text"><?php echo $mDays ?><?php if($mxDays) : ?><?php echo ' - ' . $mxDays; ?><?php endif; ?><?php echo ' ' . $nights_days; ?></span>
                                                            </div>
                                                        </div>
                                                    <?php endif; ?>
                                                    <?php if($mDistance) : ?>
                                                        <div class="marker__item">
                                                            <div class="cat">
                                                                <span class="cat__icon" style="background-color: #3e5056;"><i class="fal fa-wave-sine"></i></span>
                                                                <span class="cat__text"><?php echo $mDistance; ?></span>
                                                            </div>
                                                        </div>
                                                    <?php endif; ?>
                                                </div>


                                            </div>
                                            </a>
                                        </div>
                                    </div>

                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</section><!-- .related-posts -->
