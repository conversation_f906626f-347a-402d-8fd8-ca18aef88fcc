<?php

/**
 * Banner
 */

$slides = get_sub_field('slides');
$is_slideshow = $slides && count($slides) > 1;

if ($slides) :
    foreach($slides as $slide) : ?>
        <link rel="preload" fetchpriority="high" as="image" href="<?php echo wp_get_attachment_image_url($slide['image'], 'small'); ?>">
    <?php endforeach;
endif; ?>

<section class="banner">

    <div class="banner__background">
        <style>
            [data-ae="fade"] {
                opacity: 0;
                animation: fadeIn .5s ease-in-out forwards;
            }
            @keyframes fadeIn {
                to {
                    opacity: 1;
                }
            }
            .svg-inline--fa { display: inline-block;height: 1em;vertical-align: -.125em;}
            .svg-inline--fa.fa-w-16 { width: 1em; }
            svg:not(:root).svg-inline--fa { overflow: visible; }
            .button--icon svg { font-size: 1.8rem;margin-left: 0;margin-right: 10px; }
        </style>
    <?php if ($slides && !$is_slideshow) :
        // Single slide - set as background
        $single_slide = $slides[0];
        echo outputSrcset($single_slide['image'], '.banner__background');
    endif;

    if ($is_slideshow) : ?>
        <div class="banner__slideshow">
            <?php foreach($slides as $index => $slide) : ?>
                <?php $image_url = wp_get_attachment_image_url($slide['image'], 'full'); ?>
                <div class="banner__slide" style="background-image: url('<?php echo esc_url($image_url); ?>');">
                    <div class="banner__inner">
                        <div class="container banner__container">
                            <div class="banner__content text-white centre inner-container" data-ae="fade">
                                <?php if (!empty($slide['text'])) : ?>
                                    <?php if ($index === 0) : ?>
                                        <h1 class="banner__heading"><?php echo esc_html($slide['text']); ?></h1>
                                    <?php else : ?>
                                        <h2 class="banner__heading"><?php echo esc_html($slide['text']); ?></h2>
                                    <?php endif; ?>
                                <?php endif; ?>

                                <?php if (!empty($slide['links'])) : ?>
                                    <div class="banner__links">
                                        <?php foreach($slide['links'] as $link_item) : ?>
                                            <?php if (!empty($link_item['link'])) : ?>
                                                <div class="banner__link-wrapper">
                                                    <a href="<?php echo esc_url($link_item['link']['url']); ?>"
                                                       class="button banner__link"
                                                       <?php if (!empty($link_item['link']['target'])) : ?>target="<?php echo esc_attr($link_item['link']['target']); ?>"<?php endif; ?>>
                                                        <?php echo esc_html($link_item['link']['title']); ?>
                                                    </a>
                                                </div>
                                            <?php endif; ?>
                                        <?php endforeach; ?>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
    <?php endif; ?>
        <div class="banner__inner">

            <div class="container banner__container">
                <div class="banner__content text-white centre inner-container" data-ae="fade">
                    <?php if (!$is_slideshow && $slides) : ?>
                        <?php if (!empty($slides[0]['text'])) : ?>
                            <h1 class="banner__heading"><?php echo esc_html($slides[0]['text']); ?></h1>
                        <?php endif; ?>

                        <?php if (!empty($slides[0]['links'])) : ?>
                            <div class="banner__links">
                                <?php foreach($slides[0]['links'] as $link_item) : ?>
                                    <?php if (!empty($link_item['link'])) : ?>
                                        <div class="banner__link-wrapper">
                                            <a href="<?php echo esc_url($link_item['link']['url']); ?>"
                                               class="button banner__link"
                                               <?php if (!empty($link_item['link']['target'])) : ?>target="<?php echo esc_attr($link_item['link']['target']); ?>"<?php endif; ?>>
                                                <?php echo esc_html($link_item['link']['title']); ?>
                                            </a>
                                        </div>
                                    <?php endif; ?>
                                <?php endforeach; ?>
                            </div>
                        <?php endif; ?>
                    <?php endif; ?>
                </div>
            </div>
            <div class="banner__scroll-down scroll-next">
            <svg class="svg-inline--fa fa-chevron-double-down fa-w-14" aria-hidden="true" focusable="false" data-prefix="fal" data-icon="chevron-double-down" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512" data-fa-i2svg=""><path fill="currentColor" d="M443.5 98.5l-211 211.1c-4.7 4.7-12.3 4.7-17 0L4.5 98.5c-4.7-4.7-4.7-12.3 0-17l7.1-7.1c4.7-4.7 12.3-4.7 17 0L224 269.9 419.5 74.5c4.7-4.7 12.3-4.7 17 0l7.1 7.1c4.6 4.6 4.6 12.2-.1 16.9zm0 111l-7.1-7.1c-4.7-4.7-12.3-4.7-17 0L224 397.9 28.5 202.5c-4.7-4.7-12.3-4.7-17 0l-7.1 7.1c-4.7 4.7-4.7 12.3 0 17l211 211.1c4.7 4.7 12.3 4.7 17 0l211-211.1c4.8-4.8 4.8-12.4.1-17.1z"></path></svg>
            </div>
        </div>
    </div>
</section><!-- .banner -->
