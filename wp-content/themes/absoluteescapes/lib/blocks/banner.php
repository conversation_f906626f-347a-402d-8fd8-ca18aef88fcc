<?php

/**
 * Banner
 */

$heading = get_sub_field('heading');
$image = wp_get_attachment_image_src(get_sub_field('image'), 'full');
$review_embed = get_sub_field('review_embed');
$review_embed_fallback = get_sub_field('review_embed_fallback');


/**
 *
 * Build Params to allow video to play in the background
 *
 */

$video = get_sub_field('video');

if (!empty($video)) {

    preg_match('/src="(.+?)"/', $video, $matches);
    $src = $matches[1];

    $params = array(
        'controls' => 0,
        'autohide' => 1,
        'autoplay' => 1,
        'mute' => 1,
        'rel' => 0,
        'showinfo' => 0,
        'background' => 1
    );

    $new_src = add_query_arg($params, $src);

    $video = str_replace($src, $new_src, $video);

    $attributes = 'frameborder="0"';

    $video = str_replace('></iframe>', ' ' . $attributes . '></iframe>', $video);

}

$slideshow = get_sub_field('slideshow');
$images = get_sub_field('images');

if ($slideshow) :
    if ($images) :
        foreach($images as $_image) : ?>
        <link rel="preload" fetchpriority="high" as="image" href="<?php echo wp_get_attachment_image_url($_image, 'small'); ?>">
    <?php endforeach; endif; else : ?>
        <link rel="preload" fetchpriority="high" as="image" href="<?php echo wp_get_attachment_image_url(get_sub_field('image'), 'small'); ?>">
<?php endif; ?>

<section class="banner">

    <div class="banner__background">
        <style>
            [data-ae="fade"] {
                opacity: 0;
                animation: fadeIn .5s ease-in-out forwards;
            }
            @keyframes fadeIn {
                to {
                    opacity: 1;
                }
            }
            .svg-inline--fa { display: inline-block;height: 1em;vertical-align: -.125em;}
            .svg-inline--fa.fa-w-16 { width: 1em; }
            svg:not(:root).svg-inline--fa { overflow: visible; }
            .button--icon svg { font-size: 1.8rem;margin-left: 0;margin-right: 10px; }
        </style>
    <?php if (is_array($image) && $image[0]) : 
        echo outputSrcset(get_sub_field('image'),'.banner__background');
    endif;
    if ($slideshow) : ?>
            <?php if ($images) : ?>
                <div class="banner__slideshow">
                    <?php foreach($images as $_image) : ?>
                        <div class="banner__slide slide">
                            <span class="animate out" style=""></span>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php endif; ?>
        <?php else : ?>
            <?php if ($video) : ?>
                <div class="banner__video" data-ae="fade"> <!--  data-aos="fade" -->
                    <?php echo $video; ?>
                </div>
            <?php endif; ?>
        <?php endif; ?>
        <div class="banner__inner">

            <div class="container banner__container">
                <div class="banner__content text-white centre inner-container" data-ae="fade"> <!--  data-aos="fade" -->
                    <?php if ($heading) : ?>
                        <h1 class="banner__heading"><?php echo $heading; ?></h1>
                    <?php endif; ?>
                    <?php if (have_rows('links')) : ?>
                        <div class="banner__links font-zero">
                            <?php while (have_rows('links')) : the_row(); ?>
                                <?php

                                $link = get_sub_field('link');

                                ?>

                                <?php if ($link) : ?>
                                    <div class="banner__link-wrapper inline-block vmiddle">
                                        <a href="<?php echo $link['url']; ?>" <?php echo ($link['target']) ? 'target="_blank"' : ''; ?>
                                           class="banner__link button"><?php echo $link['title']; ?><svg class="svg-inline--fa" aria-hidden="true" focusable="false" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 320 512"><path fill="currentColor" d="M285.476 272.971L91.132 467.314c-9.373 9.373-24.569 9.373-33.941 0l-22.667-22.667c-9.357-9.357-9.375-24.522-.04-33.901L188.505 256 34.484 101.255c-9.335-9.379-9.317-24.544.04-33.901l22.667-22.667c9.373-9.373 24.569-9.373 33.941 0L285.475 239.03c9.373 9.372 9.373 24.568.001 33.941z"></path></svg></a>
                                    </div>
                                <?php endif; ?>
                            <?php endwhile; ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
            <div class="banner__scroll-down scroll-next">
            <svg class="svg-inline--fa fa-chevron-double-down fa-w-14" aria-hidden="true" focusable="false" data-prefix="fal" data-icon="chevron-double-down" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512" data-fa-i2svg=""><path fill="currentColor" d="M443.5 98.5l-211 211.1c-4.7 4.7-12.3 4.7-17 0L4.5 98.5c-4.7-4.7-4.7-12.3 0-17l7.1-7.1c4.7-4.7 12.3-4.7 17 0L224 269.9 419.5 74.5c4.7-4.7 12.3-4.7 17 0l7.1 7.1c4.6 4.6 4.6 12.2-.1 16.9zm0 111l-7.1-7.1c-4.7-4.7-12.3-4.7-17 0L224 397.9 28.5 202.5c-4.7-4.7-12.3-4.7-17 0l-7.1 7.1c-4.7 4.7-4.7 12.3 0 17l211 211.1c4.7 4.7 12.3 4.7 17 0l211-211.1c4.8-4.8 4.8-12.4.1-17.1z"></path></svg>
            </div>
        </div>
    </div>
</section><!-- .banner -->
