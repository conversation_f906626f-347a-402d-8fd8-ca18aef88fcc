<?php 

/**
 * Destinations
 */

$heading = get_sub_field('heading');
$copy = get_sub_field('copy');

?>

<section class="destinations">
    <div class="destinations__inner" data-aos="fade">
        <div class="container destinations__container">
            <div class="destinations__content centre inner-container">
                <?php if($heading) : ?>
                    <h2 class="destinations__heading h2-large heading-light heading-underlined text-weight-regular"><?php echo $heading; ?></h2>
                <?php endif; ?>
                <?php if($copy) : ?>
                    <div class="destinations__copy">
                        <?php echo $copy; ?>
                    </div>
                <?php endif; ?>
            </div>
            <?php if(have_rows('regions')) : ?>
                
                <?php while(have_rows('regions')) : the_row(); ?>
                    <?php 
                    
                    $taxonomies = get_sub_field('taxonomies');
                    $country = $taxonomies[0]->name;

                    ?>
                    <div class="destinations__carousel">
                        <?php if($country) : ?>
                            <h2 class="destinations__carousel-heading"><?php echo $country; ?></h2>
                        <?php endif; ?>
                        <div class="destinations__row">
                            <?php if($taxonomies) : $i = 0; ?>
                            
                                <?php foreach($taxonomies as $taxonomy) : ?>
                                    <?php 

                                    $region_image = wp_get_attachment_image(get_field('region_image', $taxonomy), 'destination');

                                    ?>
                                    <div class="destinations__col">
                                        <div class="destinations__item">
                                            <a href="<?php echo get_term_link($taxonomy, 'holiday-regions'); ?>" class="destinations__link">
                                                <?php if($region_image) : ?>
                                                    <div class="destinations__image">
                                                        <?php echo $region_image; ?>
                                                    </div>
                                                <?php endif; ?>
                                                <h5 class="destinations__link-text"><?php echo ($i === 0) ? 'View all ' . $taxonomy->name : $taxonomy->name; ?></h5>
                                            </a>
                                        </div>
                                    </div>
                                <?php $i++; endforeach; ?>
                            
                            <?php endif; ?>
                        </div>
                        <span class="favourite-holidays__button favourite-holidays__button--prev flickity-icon flickity-icon--prev"><i class="fal fa-chevron-double-left"></i></span>
                        <span class="favourite-holidays__button favourite-holidays__button--next flickity-icon flickity-icon--next"><i class="fal fa-chevron-double-right"></i></span>
                    </div>
                <?php endwhile; ?>
                
            <?php endif; ?>

        </div>
    </div>
</section><!-- .destinations -->