<div class="enquiry-form">
    <div class="enquiry-form__inner">

        <div class="enquiry-form__close">
            <i class="far fa-times"></i>
        </div>
        <div class="enquiry-form__content">
            <h3 class="enquiry-form__heading"><?php _e('Make an enquiry', 'absoluteescapes'); ?></h3>
            <h5 class="enquiry-form__subheading"><?php echo get_the_title(); ?></h5>
            <?php

            $itinerary = get_field('holiday_itinerary');
            $accommodation_types = get_field('holiday_accommodation_types');

            ?>
            <form action="<?php echo get_the_permalink(543); ?>" method="GET" class="enquiry-form__form">
                <div class="enquiry-form__fields">
                    <?php if ($itinerary) : ?>
                        <div class="enquiry-form__field">
                            <label for="itinerary"><?php _e('Itinerary', 'absoluteescapes'); ?> <span
                                        class="tooltip"><img
                                            src="<?php echo get_stylesheet_directory_uri() . '/dist/img/info.svg' ?>"
                                            alt="info"><span
                                            class="tooltip__label"><?php _e('Number of days', 'absoluteescapes'); ?></span></span></label>
                            <div class="select-wrapper">
                                <select name="itinerary" id="itinerary">
                                    <option value="" disabled selected>Select no. of days</option>
                                    <?php foreach ($itinerary as $item) : ?>
                                        <option value="<?php echo seoUrl($item['option']); ?>"><?php echo $item['option']; ?></option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                        </div>
                    <?php endif; ?>
                    <div class="enquiry-form__field">
                        <label for="startDate"><?php _e('Start date (if known)', 'absoluteescapes'); ?> <span
                                    class="tooltip"><img
                                        src="<?php echo get_stylesheet_directory_uri() . '/dist/img/info.svg' ?>"
                                        alt="info"><span
                                        class="tooltip__label"><?php _e('Start date', 'absoluteescapes'); ?></span></span></label>
                        <div class="select-wrapper">
                            <input data-toggle="datepicker" placeholder="Select start date" name="startdate" autocomplete="off">
                        </div>
                    </div>
                    <?php if ($accommodation_types) : ?>
                        <div class="enquiry-form__field">
                            <label for="accommodationType"><?php _e('Accommodation (you can finalise this later)', 'absoluteescapes'); ?>
                                <span class="tooltip"><img
                                            src="<?php echo get_stylesheet_directory_uri() . '/dist/img/info.svg' ?>"
                                            alt="info"><span
                                            class="tooltip__label"><?php _e('Accommodation type', 'absoluteescapes'); ?></span></span></label>
                            <div class="select-wrapper">
                                <select name="accommodationtype" id="accommodationType">
                                    <option value="" disabled selected>Select accommodation type</option>
                                    <?php foreach ($accommodation_types as $accommodation_type) : ?>
                                        <option value="<?php echo seoUrl($accommodation_type['type']); ?>"><?php echo $accommodation_type['type']; ?></option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
                <?php

                $_terms = wp_list_pluck(get_the_terms(get_the_ID(), 'holiday-type'), 'term_id');
                $value = 'No';


                if (in_array(3, $_terms)) {
                    $value = 'Yes';
                }

                ?>
                <input type="hidden" name="selfdrive" value="<?= $value; ?>">

                <input type="hidden" name="holidayid" value="<?php echo get_the_ID(); ?>">
                <div class="enquiry-form__button-wrapper">
                    <button type="submit"
                            class="enquiry-form__button button button--alt"><?php _e('Enquire Now', 'absoluteescapes'); ?></button>
                </div>
            </form>

            <?php

            $tel = get_field('tel', 'options');

            ?>

            <?php if ($tel) : ?>
                <div class="enquiry-form__button-wrapper">
                    <span><?php _e("Or phone:", 'absoluteescapes'); ?></span>
                    <a href="<?php echo $tel['url']; ?>"
                       class="enquiry-form__button button button--hollow-grey"><?php echo $tel['title']; ?></a>
                </div>
            <?php endif; ?>

            <?php if (have_rows('holiday_form', 'options')) : ?>
                <div class="enquiry-form__info">
                    <?php while (have_rows('holiday_form', 'options')) : the_row(); ?>
                        <?php

                        $footer_text = get_sub_field('footer_text');
                        $review_embed = get_sub_field('review_embed');
                        $review_embed_fallback = get_sub_field('review_embed_fallback');

                        ?>

                        <div class="enquiry-form__info-content">
                            <?php if ($footer_text) : ?>
                                <div class="enquiry-form__text">
                                    <?php echo $footer_text; ?>
                                </div>
                            <?php endif; ?>
                            <div class="enquiry-form__review">
                                <?php if ($review_embed) : ?>
                                    <?php echo $review_embed; ?>
                                <?php else : ?>
                                    <img src="<?php echo $review_embed_fallback['url']; ?>"
                                         alt="<?php echo $review_embed_fallback['alt']; ?>">
                                <?php endif; ?>
                            </div>
                        </div>
                    <?php endwhile; ?>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>


<div class="enquiry-cta">
    <div class="enquiry-cta__inner">

        <?php if (have_rows('holiday_form', 'options')) : ?>
            <div class="enquiry-cta__content">
                <?php while (have_rows('holiday_form', 'options')) : the_row(); ?>
                    <?php

                    $review_embed_mob = get_sub_field('review_embed_mobile');
                    $review_embed_fallback_mob = get_sub_field('review_embed_mobile_fallback');

                    ?>

                    <div class="enquiry-cta__review">
                        <?php if ($review_embed_mob) : ?>
                            <?php echo $review_embed_mob; ?>
                        <?php else : ?>
                            <img src="<?php echo $review_embed_fallback_mob['url']; ?>"
                                 alt="<?php echo $review_embed_fallback_mob['alt']; ?>">
                        <?php endif; ?>
                    </div>
                    <div class="enquiry-cta__button-wrapper">
                        <a href="#"
                           class="enquiry-cta__button button button--alt"><?php _e('Make enquiry', 'absoluteescapes'); ?>
                            <i class="fas fa-chevron-right"></i></a>
                    </div>
                <?php endwhile; ?>
            </div>
        <?php endif; ?>
    </div>
</div>
