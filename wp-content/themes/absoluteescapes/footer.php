<?php

$back_top = get_field('back_to_top');

?>

<?php if ($back_top || is_single() || is_tax('holiday-regions') || is_tax('holiday-types')) : ?>
    <div class="back-to-top">
        <div class="back-to-top__content">
            <span><i class="fal fa-chevron-double-up"></i> <span
                        class="back-to-top__text"><?php _e('Back to top', 'absoluteescapes'); ?></span> <i
                        class="fal fa-chevron-double-up"></i></span>
        </div>
    </div>
<?php endif; ?>


<?php

$form_heading = get_field('footer_form_heading', 'options');
$form_id = get_field('footer_form_id', 'options');
$form_description = get_field('footer_form_description', 'options');
$agency_text = get_field('agency_text', 'options');
$agency_link = get_field('agency_link', 'options');
$agency_logo = get_field('agency_logo', 'options');
$logo = get_field('footer_logo', 'options');
$tel = get_field('tel', 'options');
$contact_link = get_field('contact_link', 'options');
$social_links = get_field('social_links', 'options');
$form_page = get_field('form_page');
$footer_logos = get_field('footer_logos', 'option');
?>

<footer class="mastfoot">
    <div class="mastfoot__inner">

        <div class="container mastfoot__container">

            <div class="mastfoot__row mastfoot__row--navigation row">
                <?php if (!$form_page) : ?>
                    <div class="mastfoot__navigation">
                        <nav class="mastfoot__nav">
                            <?php wp_nav_menu(array('theme_location' => 'footer', 'container' => false)); ?>
                        </nav>
                    </div>

                    <div class="mastfoot__form-wrapper">
                        <?php if ($form_heading) : ?>
                            <span class="mastfoot__form-heading text-white"><?php echo $form_heading; ?></span>
                        <?php endif; ?>
                        <?php if ($form_description) : ?>
                            <div class="mastfoot__form-description text-white">
                                <?php echo $form_description; ?>
                            </div>
                        <?php endif; ?>
                        <?php if ($form_id) : ?>
                            <div class="mastfoot__form">
                                <?php gravity_form($form_id, false, false, false, null, true, 30); ?>
                            </div>
                        <?php endif; ?>
                    </div>
                <?php endif; ?>
            </div>

        </div>

        <div class="container mastfoot__container">
            <div class="mastfoot__row mastfoot__row--contact-top">
                <a href="<?php echo home_url('/'); ?>" class="mastfoot__logo-link" style="">
                    <img src="<?php echo $logo['url']; ?>" alt="<?php echo $logo['alt']; ?>"
                         class="mastfoot__logo-image">
                </a>
                <?php if ($logo) : ?>
                    <div class="mastfoot__logos">
                        <?php
                        foreach ($footer_logos as $single):
                            $link = $single['link'];
                            $image = $single['logo'];
                            ?>
                            <div>
                                <?php if (!empty($link)): ?><a href="<?php echo $link['url']; ?>" target="_blank"
                                                               class="mastfoot__logo-link" style=""><?php endif; ?>
                                    <img src="<?php echo $image['url']; ?>" alt="<?php echo $image['alt']; ?>"
                                         class="mastfoot__logo-image">
                                    <?php if (!empty($link)): ?></a><?php endif; ?>
                            </div>
                        <?php endforeach ?>
                    </div>
                <?php endif; ?>
                <div class="mastfoot__contact">
                    <?php if ($tel) : ?>
                        <div class="mastfoot__contact-item mastfoot__phone">
                            <div class="mastfoot__link-wrapper">
                                <a href="<?php echo $tel['url']; ?>" class="mastfoot__link"><i
                                            class="fas fa-phone fa-flip-horizontal"></i><?php echo $tel['title']; ?></a>

                            </div>
                        </div>
                    <?php endif; ?>

                    <?php if ($contact_link) : ?>
                        <div class="mastfoot__contact-item mastfoot__contact-link">
                            <div class="mastfoot__link-wrapper">
                                <a href="<?php echo $contact_link['url']; ?>" <?php echo ($contact_link['target']) ? 'target="_blank"' : ''; ?>
                                   class="mastfoot__link button"><?php echo $contact_link['title']; ?></a>
                            </div>
                        </div>
                    <?php endif; ?>

                    <?php if ($social_links && !$form_page) : ?>
                        <div class="mastfoot__contact-item">
                            <div class="mastfoot__social-links">
                                <?php foreach ($social_links as $social_link) : ?>
                                    <?php

                                    $link = $social_link['link'];
                                    $icon = $social_link['icon_fa'];

                                    ?>

                                    <?php if ($link) : ?>
                                        <div class="mastfoot__social-link-wrapper inline-block vmiddle">
                                            <a href="<?php echo $link['url']; ?>" class="mastfoot__social-link"><i
                                                        class="<?php echo $icon; ?>"></i></a>
                                        </div>
                                    <?php endif; ?>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

        </div>
        <div class="container mastfoot__container">
            <div class="mastfoot__row mastfoot__row--details">
                <div class="mastfoot__col">
                    <div class="mastfoot__copyright text-white">
                        <span><?php _e('Copyright', 'absoluteescapes'); ?> <?php echo date('Y'); ?>. <?php echo bloginfo(); ?></span>
                    </div>
                </div>
                <div class="mastfoot__col">
                    <div class="mastfoot__agency text-white">
                        <span><?php echo $agency_text; ?> <?php if ($agency_link) : ?><a
                                href="<?php echo $agency_link['url']; ?>" <?php echo ($agency_link['target']) ? 'target="_blank"' : ''; ?>
                                class="mastfoot__agency-link text-white"><?php echo $agency_link['title']; ?><?php if ($agency_logo) : ?>
                                    <img src="<?php echo $agency_logo['url']; ?>"
                                         alt="<?php echo $agency_logo['alt']; ?>"><?php endif; ?>
                                </a><?php endif; ?></span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</footer><!-- .mastfoot -->

</div>


<?php 
$force_maps = 1;
 if (preg_match('(^/holidays|^/destinations)', $GLOBALS['pagenow']) === 1 || $force_maps == 1) { ?>
<script src="https://maps.googleapis.com/maps/api/js?key=<?php echo get_field('google_maps_api_key', 'options'); ?>"></script>
<?php } else { ?>
    <?php } ?>

<?php wp_footer(); ?>

<?php if (class_exists('AITO_Widget')) {$aito_widget = new AITO_Widget();echo $aito_widget->output_js();} ?>

<?php if ( is_home() || is_singular( array( 'post', 'holiday' ) ) ): ?>
    <?php get_template_part('lib/components/form-modal'); ?>
<?php endif; ?>
</body>
</html>
