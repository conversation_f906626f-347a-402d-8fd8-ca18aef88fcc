<?php

/* @wordpress-plugin
 * Plugin Name:       List Image Sizes
 * Description:       List all registered Image Sizes, with dimensions.
 * Version:           0.1
 * Author:            <PERSON>
 * Author URI:        https://theuniversal.co/
 * Text Domain:       list-image-sizes
 * Domain Path:       /languages
 */

defined( 'ABSPATH' ) || die();

/**
 * Metabox with all image sizes in Admin
 */
function sixtyseven_register_imagesizes_dashboard_widget() {
    global $wp_meta_boxes;

    wp_add_dashboard_widget(
        'imagesizes_dashboard_widget',
        'Registered image sizes',
        'sixtyseven_imagesizes_dashboard_widget_display'
    );

    $dashboard = $wp_meta_boxes['dashboard']['normal']['core'];

    $new_widget = array( 'imagesizes_dashboard_widget' => $dashboard['imagesizes_dashboard_widget'] );
    unset( $dashboard['imagesizes_dashboard_widget'] );

    $sorted_dashboard = array_merge( $new_widget, $dashboard );
    $wp_meta_boxes['dashboard']['normal']['core'] = $sorted_dashboard;
}
add_action( 'wp_dashboard_setup', 'sixtyseven_register_imagesizes_dashboard_widget' );

function sixtyseven_imagesizes_dashboard_widget_display() {
    $sizes = wp_get_registered_image_subsizes();
    echo '<ul>';
    foreach($sizes as $name => $values){
        echo '<li>';
        echo '<strong>'.$name.':</strong> ';
        echo 'width: '.$values['width'].'px, ';
        echo 'height: '.$values['height'].'px';
        if($values['crop'] === true){
            echo ', cropped';
        }
        echo '</li>';
    }
    echo '</ul>';
}