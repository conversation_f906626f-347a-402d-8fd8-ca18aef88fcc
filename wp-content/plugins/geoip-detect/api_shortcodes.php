<?php
/*
Copyright 2013-2023 Yellow Tree, Siegen, Germany
Author: <PERSON> (wp-geoip-detect| |posteo.de)

This program is free software; you can redistribute it and/or modify
it under the terms of the GNU General Public License as published by
the Free Software Foundation; either version 3 of the License, or
(at your option) any later version.

This program is distributed in the hope that it will be useful,
but WITHOUT ANY WARRANTY; without even the implied warranty of
MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
GNU General Public License for more details.

You should have received a copy of the GNU General Public License
along with this program; if not, write to the Free Software
Foundation, Inc., 51 Franklin St, Fifth Floor, Boston, MA  02110-1301  USA
*/

// API: The wordpress shortcodes are considered official API, the function names are not.

require_once(GEOIP_PLUGIN_DIR . '/shortcodes/_helpers.php');
require_once(GEOIP_PLUGIN_DIR . '/shortcodes/main.php');
require_once(GEOIP_PLUGIN_DIR . '/shortcodes/cf7.php');
require_once(GEOIP_PLUGIN_DIR . '/shortcodes/wpforms.php');
require_once(GEOIP_PLUGIN_DIR . '/shortcodes/show_if.php');
require_once(GEOIP_PLUGIN_DIR . '/shortcodes/other.php');
require_once(GEOIP_PLUGIN_DIR . '/shortcodes/select.php');
require_once(GEOIP_PLUGIN_DIR . '/shortcodes/input.php');
require_once(GEOIP_PLUGIN_DIR . '/shortcodes/flags.php');





