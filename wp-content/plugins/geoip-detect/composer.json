{"name": "yellowtree/geoip-detect", "description": "Wordpress Plugin Geolocation IP Detection: Retrieving Geo-Information using the Maxmind GeoIP2 (Lite) Database.", "type": "wordpress-plugin", "authors": [{"name": "<PERSON> (wp-geoip-detect posteo de)"}], "keywords": [], "homepage": "http://www.yellowtree.de", "license": ["GPL-3.0-or-later"], "require": {"php": ">=7.2.5", "geoip2/geoip2": "2.10.0", "maxmind-db/reader": "1.6.0", "maxmind/web-service-common": "0.7.0", "symfony/property-access": "5.4.22", "symfony/property-info": "^5.4", "symfony/string": "^5.1", "symfony/deprecation-contracts": "^2.5", "symfony/http-foundation": "5.4.22"}, "require-dev": {"roots/wordpress": "6.5.2", "wp-phpunit/wp-phpunit": "6.5.2", "yoast/phpunit-polyfills": "2.0.1", "phpunit/phpunit": "9.6.19"}, "scripts": {"test": "phpunit", "test-external": "phpunit --group external-http", "install-test": "composer install --ignore-platform-req=php", "install-prod": "composer install --prefer-dist --optimize-autoloader --no-dev", "migrate": "phpunit --migrate-configuration"}, "config": {"allow-plugins": {"roots/wordpress-core-installer": true}, "platform": {"php": "7.3"}}}