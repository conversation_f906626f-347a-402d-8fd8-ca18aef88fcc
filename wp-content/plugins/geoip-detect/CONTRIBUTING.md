# How can you contribute to this project?

Depending on your skill and interests, here is what you can give:

1. Code
2. Documentation
3. Support
4. Beta-Testing
4. Translation
5. Donation

## 1. Code

If you are a coder, and want to improve or add a feature, feel free to do so. This article is for you: [Developers](https://github.com/yellowtree/geoip-detect/wiki/Developers)

## 2. Documentation

You can always improve the [documentation](https://github.com/yellowtree/geoip-detect/wiki), for example by:
* Re-phrasing existing documentation to make it easier to understand
* Adding extra examples to show some features
* Explaining step-by-step how to set up the plugin, linking into the other documentation sections
* Documenting plugin options / filters and how they can be used

You only need a Github user, navigate to the site and click on `Edit` in the Top-Right. I will review your changes.

The documentation of PHP function is copied out of the code. If you want to modify them, please (if you can) modify them in the code repository as well (and send a pull request).

If you need inspiration of what needs better documenting, look at the [support forum](https://wordpress.org/support/plugin/geoip-detect/). Hint: You can start with the things that you were stuck yourself when you started using the plugin, most probably others will learn from it.

## 3. Beta-Testing

Beta for me means "I *think* I'm finished, but ..." - often some errors only appear in a certain environment, or I have forgotten to support some edge case / options combination.
So it would be great if you can install the beta version in your development and tell me if everything works, especially the features that have changed recently!
How? See [Beta Testing](https://github.com/yellowtree/geoip-detect/wiki/Beta_Testing)

## 4. Support

Go to the [support forum](https://wordpress.org/support/plugin/geoip-detect/) and check for [unresolved (esp. un-replied)](https://wordpress.org/support/plugin/geoip-detect/unresolved/) threads. If you can answer the question, please do so! I probably would have replied also at some point, but maybe only weeks later. The questions range from beginner to very-techical, so I'm sure if you are an active user of the plugin, some question can be replied by you.

You can subscribe to the forum by clicking on the `Subscribe` button on the top - BTW I have subscribed to the forum so I will see all requests and your replies as well. So don't worry, if I don't agree with you for some reason I will explain why ;-)

## 5. Translation

I am maintaining the German translation of the plugin. If you want to translate the plugin UI into your own mother-tongue, here is how you do it:

* Read the [General expectations for translations](https://make.wordpress.org/polyglots/handbook/translating/expectations/) first.
* Login with your wordpress user on the [wordpress platform](https://translate.wordpress.org/projects/wp-plugins/geoip-detect/)
* "Suggest" translation strings for the whole plugin. Only translations that are completed can be considered to be published.
* Then [submit a so-called PTE request](https://make.wordpress.org/polyglots/handbook/plugin-theme-authors-guide/pte-request/#sample-translation-reviewpte-request-by-a-plugintheme-author)
* After each plugin update, there might be a few plugin strings changed. So from time to time, please check these "Fuzzy" or "Untranslated" strings

## 6. Donation

This plugin is [charity-ware](https://github.com/yellowtree/geoip-detect/wiki/FAQ#what-do-you-mean-by-this-plugin-is-charity-ware):

https://www.paypal.com/cgi-bin/webscr?cmd=_s-xclick&hosted_button_id=BSYUZHS8FH3CL

# Just do it ;-)

If you have any further question or suggestion, please get in touch with me on https://github.com/yellowtree/geoip-detect/issues or privately at wp-geoip-detect posteo de
