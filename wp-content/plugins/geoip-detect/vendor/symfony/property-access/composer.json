{"name": "symfony/property-access", "type": "library", "description": "Provides functions to read and write from/to an object or array using a simple string notation", "keywords": ["property", "index", "access", "object", "array", "extraction", "injection", "reflection", "property-path"], "homepage": "https://symfony.com", "license": "MIT", "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "require": {"php": ">=7.2.5", "symfony/deprecation-contracts": "^2.1|^3", "symfony/polyfill-php80": "^1.16", "symfony/property-info": "^5.2|^6.0"}, "require-dev": {"symfony/cache": "^4.4|^5.0|^6.0"}, "suggest": {"psr/cache-implementation": "To cache access methods."}, "autoload": {"psr-4": {"Symfony\\Component\\PropertyAccess\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "minimum-stability": "dev"}