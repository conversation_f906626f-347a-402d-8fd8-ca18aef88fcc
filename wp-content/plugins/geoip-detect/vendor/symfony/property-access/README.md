PropertyAccess Component
========================

The PropertyAccess component provides functions to read and write from/to an
object or array using a simple string notation.

Resources
---------

 * [Documentation](https://symfony.com/doc/current/components/property_access.html)
 * [Contributing](https://symfony.com/doc/current/contributing/index.html)
 * [Report issues](https://github.com/symfony/symfony/issues) and
   [send Pull Requests](https://github.com/symfony/symfony/pulls)
   in the [main Symfony repository](https://github.com/symfony/symfony)
