{"name": "symfony/property-info", "type": "library", "description": "Extracts information about PHP class' properties using metadata of popular sources", "keywords": ["property", "type", "phpdoc", "symfony", "validator", "doctrine"], "homepage": "https://symfony.com", "license": "MIT", "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "require": {"php": ">=7.2.5", "symfony/deprecation-contracts": "^2.1|^3", "symfony/polyfill-php80": "^1.16", "symfony/string": "^5.1|^6.0"}, "require-dev": {"symfony/serializer": "^4.4|^5.0|^6.0", "symfony/cache": "^4.4|^5.0|^6.0", "symfony/dependency-injection": "^4.4|^5.0|^6.0", "phpdocumentor/reflection-docblock": "^3.0|^4.0|^5.0", "phpstan/phpdoc-parser": "^1.0", "doctrine/annotations": "^1.10.4|^2"}, "conflict": {"phpdocumentor/reflection-docblock": "<3.2.2", "phpdocumentor/type-resolver": "<1.4.0", "symfony/dependency-injection": "<4.4"}, "suggest": {"psr/cache-implementation": "To cache results", "symfony/doctrine-bridge": "To use Doctrine metadata", "phpdocumentor/reflection-docblock": "To use the PHPDoc", "symfony/serializer": "To use Serializer metadata"}, "autoload": {"psr-4": {"Symfony\\Component\\PropertyInfo\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "minimum-stability": "dev"}