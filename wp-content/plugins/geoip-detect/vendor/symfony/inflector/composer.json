{"name": "symfony/inflector", "type": "library", "description": "Symfony Inflector Component", "keywords": ["string", "inflection", "singularize", "pluralize", "words", "symfony"], "homepage": "https://symfony.com", "license": "MIT", "authors": [{"name": "<PERSON>", "email": "b<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "require": {"php": ">=7.1.3", "symfony/polyfill-ctype": "~1.8"}, "autoload": {"psr-4": {"Symfony\\Component\\Inflector\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "minimum-stability": "dev", "extra": {"branch-alias": {"dev-master": "4.4-dev"}}}