{"name": "geoip-detect", "version": "2.11.0", "description": "Geolocation IP Detection - Wordpress Plugin (JS)", "repository": "**************:yellowtree/geoip-detect.git", "author": "<PERSON> <<EMAIL>>", "license": "GPL-3.0-or-later", "source": ["js/frontend_full.js", "js/frontend_base.js", "js/frontend_minimal.js"], "scripts": {"clean": "rm -rf .parcel-cache", "start": "rm -f babel.config.json ; parcel --port 1234 --hmr-port 1236 --dist-dir js/dist --public-url .", "build": "rm -f babel.config.json ; rm -rf js/dist && parcel build --dist-dir js/dist --public-url .", "test": "cp tests/babel.config.json . && jest --coverage", "test-ddd": "cp tests/babel.config.json . && jest -t ddd", "update-browserlist": "browserslist --update-db"}, "private": false, "dependencies": {"@babel/runtime": "7.24.7", "emoji-flags": "1.3.0", "just-compare": "2.3.0", "just-intersect": "4.3.0", "just-safe-get": "4.2.0", "just-safe-set": "4.2.1"}, "devDependencies": {"@babel/core": "7.24.7", "@babel/plugin-transform-runtime": "7.24.7", "@babel/preset-env": "7.24.7", "babel-jest": "29.7.0", "babel-plugin-transform-class-properties": "6.24.1", "eslint": "8.57.0", "jest": "29.7.0", "jest-environment-jsdom": "^29.4.2", "parcel": "2.9.3"}, "browserslist": "> 0.2%, last 2 versions, Firefox ESR, not dead", "jest": {"transform": {"^.+\\.js$": "babel-jest"}}, "engines": {"node": ">=16.0.0 <17.0.0"}}