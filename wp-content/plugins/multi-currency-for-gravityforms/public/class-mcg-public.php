<?php
/**
 * The public-facing functionality of the plugin.
 *
 * @since      1.0.0
 *
 * @package    Mcg
 * @subpackage Mcg/public
 */

/**
 * The public-facing functionality of the plugin.
 *
 * @package    Mcg
 * @subpackage Mcg/public
 */
class Mcg_Public {

	/**
	 * The ID of this plugin.
	 *
	 * @since    1.0.0
	 * @var      string    $plugin_name    The ID of this plugin.
	 */
	private $plugin_name;

	/**
	 * The version of this plugin.
	 *
	 * @since    1.0.0
	 * @var      string    $version    The current version of this plugin.
	 */
	private $version;

	/**
	 * Initialize the class and set its properties.
	 *
	 * @since    1.0.0
	 * @param string $plugin_name       The name of the plugin.
	 * @param string $version    The version of this plugin.
	 */
	public function __construct( $plugin_name, $version ) {

		$this->plugin_name = $plugin_name;
		$this->version     = $version;

	}

	/**
	 * Register the localize Script for the public-facing side of the site.
	 *
	 * @since    1.0.0
	 */
	public static function localize_scripts() {

		$data = array(
			'exchange_rate'   => Mcg_Currency::get_currency_exchange_rate(),
			'all_currencies'  => RGCurrency::get_currencies(),
			'global_currency' => GFCommon::get_currency(),
		);

		wp_localize_script( 'mcg_public_js', 'idea_mcg', $data );
	}

}
