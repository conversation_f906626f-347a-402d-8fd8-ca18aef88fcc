import './overriden-gf-functions';
import mcg_countries from './countries';
import CompatConditionalPricing from './compat-conditional-pricing';
import CalculationField from './calculation-field';
import PaypalExpress from './compat-paypal-express';
import Stripe from './compat-stripe';

var $ = jQuery;

$( document ).ready( function () {
	CalculationField.init();
	CompatConditionalPricing.init();
	PaypalExpress.init();
	Stripe.init();
} );

var mcg = {

	/**
	 * Data structure:
	 * 1. window.idea_mcg:
	 * 	- all_currencies
	 * 	- exchange_rate
	 * 2. window.mcg_form_{form_id}
	 */

	/**
	 * On ready.
	 */
	on_ready: function () {
		if ( !idea_mcg.exchange_rate || $('body').hasClass('wp-admin') ) {
			return;
		}

		$( document.body ).trigger( 'mcg_before_ready' );
		mcg.handle_currency_field_change();
		
		gform.addFilter( 'gform_get_base_price', mcg.filter_gform_get_base_price );
		gform.addFilter( 'gform_get_shipping_price', mcg.filter_gform_get_shipping_price );
		
		$( ".gform_wrapper .ginput_container_multicurrency select" ).trigger( 'change' );
		mcg.set_currency_based_on_visitor_ip();
		gform.addAction( 'gform_input_change', mcg.save_option_value_in_localstorage );

		// Stripe compatibility.
		gform.addFilter( 'gform_stripe_currency', mcg.stripe_change_currency );

		// Coupon compatibility.
		gform.addFilter( 'gform_coupons_discount_amount', mcg.flat_coupon_currency_convert );
	},

	/**
	 * Get form's payment processing currency.
	 * 
	 * @param {int} form_id Form ID.
	 */
	get_form_payment_processing_currency: function ( form_id ) {
		return ( window[ 'mcg_form_' + form_id ] ) ? window[ 'mcg_form_' + form_id ][ 'payment_processing_currency' ] : false;
	},

	handle_currency_field_change: function () {
		$( ".gform_wrapper .ginput_container_multicurrency select" ).change( function () {
			mcg.on_currency_change( $( this ) );
		} );

		jQuery( document ).on( 'gform_page_loaded', function () {
			$( ".gform_wrapper .ginput_container_multicurrency select" ).change( function () {
				mcg.on_currency_change( $( this ) );
			} );
		} );
	},

	on_currency_change: function ( $select ) {
		var new_currency = $select.val();
		var form_id = mcg.get_form_id( $select );
		var form_data = window[ 'mcg_form_' + form_id ];
		var mode = mcg.getMode( form_id );

		var old_currency = form_data.current_currency ? form_data.current_currency : form_data.base_currency;
		form_data.current_currency = new_currency;

		// Update global currency object.
		mcg.update_gf_global_currency( new_currency, old_currency );

		// Update Each Product Price.
		if ( 'automatic' === mode ) {
			mcg.update_product_field_price_automatic( form_id, new_currency );
			mcg.update_shipping_field_price_automatic( form_id, old_currency, new_currency );
		} else {
			mcg.update_product_field_price_manual( form_id, old_currency, new_currency );
			mcg.update_shipping_field_price_manual( form_id, old_currency, new_currency );
		}
	},

	/**
	 * Get form ID from any inner element.
	 *
	 * @param {jQuery} $el Any inner element.
	 */
	get_form_id: function ( $el ) {
		var $gform_wrapper = $el.closest( ".gform_wrapper" );
		
		if ( !$gform_wrapper ) return false;

		var dom_id = $gform_wrapper.attr( "id" );
		
		if ( !dom_id ) return false;

		var form_id = dom_id.replace( 'gform_wrapper_', '' );
		return form_id;
	},

	/**
	 * Update gf_global.gf_currency_config currency.
	 * 
	 * @param {string} currency Currency.
	 */
	update_gf_global_currency: function ( new_currency, old_currency ) {
		gf_global.gf_currency_config = idea_mcg.all_currencies[ new_currency ];
		$( ".gform_wrapper input" ).trigger( 'change' );
	},

	/**
	 * Update the price which is shown to the user.
	 *
	 * @param int formId 
	 * @param string old_currency 
	 * @param string new_currency 
	 */
	update_product_field_price_automatic: function ( form_id, new_currency ) {
		$( "#gform_wrapper_" + form_id ).find( '.gfield_price' ).each( function () {

			if ( $( this ).hasClass( 'gfield--type-total' ) || $( this ).hasClass( 'gfield--input-type-price' ) ) {
				return;
			}

			if ( $( this ).hasClass( 'gfield--input-type-select' ) || $( this ).hasClass( 'gfield--input-type-radio' ) ) {
				mcg.update_select_fields_automatic( $( this ), form_id );
				return;
			}

			var form_base_currency  = window[ 'mcg_form_' + form_id ].base_currency;
			var new_currency_config = idea_mcg.all_currencies[ new_currency ];
			var new_currency_obj    = new Currency( new_currency_config );

			// if its a calculation field.
			if ( $( this ).hasClass( 'gfield--input-type-calculation' ) ) {	

				var formatted_price = $( this ).find( '.ginput_product_price' ).text();
				var flatten_price = mcg.toNumber( formatted_price, true );

				var price = mcg.flatToCurrentCurrency( flatten_price, form_id, false );
				$( this ).find( '.mcg_ginput_product_price' ).text( price );
				return;
			}

			var flattened_price   = $( this ).find( '[data-mcg-base-price]' ).data( 'mcg-base-price' );
			var amt_in_number     = mcg.unflatten( flattened_price );
			amt_in_number         = mcg.convert_currency( form_base_currency, new_currency, amt_in_number );
			amt_in_number         = Math.round( amt_in_number * 100 ) / 100; // round to 2 decimal places.
			var new_amt_formatted = new_currency_obj.toMoney( amt_in_number, true );

			_mcg.setPrice( $( this ), new_amt_formatted );
		} );
	},

	flat_coupon_currency_convert: function ( discount, couponType, couponAmount, price, totalDiscount ) {
		var form_id = mcg.get_form_id( $( ".gf_coupon_code" ) );
		var form_data = window[ 'mcg_form_' + form_id ];
		var new_currency = $( ".gform_wrapper .ginput_container_multicurrency select" ).val();
		var old_currency = form_data.base_currency;

		if ( new_currency != old_currency ) {
			if ( 'flat' == couponType ) {
				var new_discount = mcg.convert_currency( old_currency, new_currency, discount );
				return new_discount;
			} else if ( 'percentage' == couponType ) {
				var ship_price = gformGetShippingPrice( form_id );
				ship_price = mcg.convert_currency( old_currency, new_currency, ship_price );
				price = price - ship_price;
				discount = price * Number( ( couponAmount / 100 ) );
				return discount;
			}
		} else {
			return discount;
		}
	},

	/**
	 * Update the price which is shown to the user.
	 *
	 * @param int formId 
	 * @param string old_currency 
	 * @param string new_currency 
	 */
	update_product_field_price_manual: function ( formId, old_currency, new_currency ) {
		$( '#gform_wrapper_' + formId ).find( '.gfield_price' ).each( function () {
			var html_id = $( this ).attr( 'id' );
			let field_id = gf_get_input_id_by_html_id( html_id );
			var price = mcg.product_get_manual_price_in_current_currency( formId, field_id );

			var currencyObj = new Currency( gf_global.gf_currency_config );

			$( this ).find( '.ginput_product_price' ).html( currencyObj.toMoney( price ) );
			$( this ).find( 'input.ginput_product_price' ).val( currencyObj.toMoney( price ) );
			$( this ).find( `#ginput_base_price_${formId}_${field_id}` ).val( currencyObj.toMoney( price ) );
		} );
	},

	/**
	 * Update the shipping price span - which is shown to the user.
	 *
	 * @param int formId 
	 * @param string old_currency
	 * @param string new_currency 
	 */
	update_shipping_field_price_manual: function ( formId, old_currency, new_currency ) {
		var $shipping_price_input = $( '#gform_wrapper_' + formId ).find( '.ginput_shipping_price' );
		var shiping_rates = window[ 'mcg_form_' + formId ][ 'manual_shipping' ];

		if ( !$shipping_price_input || !shiping_rates ) {
			return;
		}

		if ( typeof shiping_rates[ new_currency ] === 'undefined' ) {
			return;
		}

		var currencyObj = new Currency( gf_global.gf_currency_config );

		$shipping_price_input.val( currencyObj.toMoney( shiping_rates[ new_currency ] ) );
	},

	/**
	 * Update values of the dropdown and radio fields.
	 *
	 * @param {jQuery} $field Field jQuery object.
	 * @param {int}    formId Form ID.
	 */
	update_select_fields_automatic: function ( $field, formId ) {
		if ( $field.hasClass( 'gfield--type-quantity' ) ) {
			return;
		}

		$field.find( 'option, input[type=radio]' ).each( function () {
			var flat_amount_in_base_currency = $( this ).data( 'mcg-base-price' );
			var base_currency                = window[ 'mcg_form_' + formId ][ 'base_currency' ];
			var current_currency             = window[ 'mcg_form_' + formId ][ 'current_currency' ];
			var new_currency_obj             = new Currency( idea_mcg[ "all_currencies" ][ current_currency ] );
			
			var new_amt = mcg.convert_currency( base_currency, current_currency, mcg.unflatten( flat_amount_in_base_currency ) );
			var new_amt_number = new_currency_obj.toNumber( new_amt ); 
			new_amt_number = Math.round( new_amt_number * 100 ) / 100; // round to 2 decimal places.
			
			// option has value is this format: 'Choice1|1,00.00', replace all text after '|' with new_amt.
			var new_option_value = $( this ).val().split( '|' )[ 0 ] + '|' + new_amt_number;
			$( this ).val( new_option_value );
		} )
	},

	/**
	 * Update the shipping price span and input values.
	 *
	 * @param int formId 
	 * @param string old_currency
	 * @param string new_currency 
	 */
	update_shipping_field_price_automatic: function ( formId, old_currency, new_currency ) {
		
		$( '.gfield--type-shipping' ).each( function () {
			
			if ( $( this ).hasClass( 'gfield--input-type-singleshipping' ) ) {
				var flat_amount_in_base_currency = $( this ).find( '[data-mcg-base-price]' ).data( 'mcg-base-price' );
				var new_val = mcg.flatToCurrentCurrency( flat_amount_in_base_currency, formId, false );
				$( this ).find( '[data-mcg-base-price]' ).val( new_val )
			} else if ( $( this ).hasClass( 'gfield--input-type-select' ) || $( this ).hasClass( 'gfield--input-type-radio' ) ) {
				mcg.update_select_fields_automatic( $( this ), formId );
			}

		} );
	},

	/**
	 * Convert currency.
	 * 
	 * @param {string} from  From Currency.
	 * @param {string} to    To Currency.
	 * @param {total}  total Amount to be converted.
	 */
	convert_currency: function ( from, to, total ) {
		if ( isNaN( total ) ) {
			var from_currency = new Currency( idea_mcg[ "all_currencies" ][ from ] );
			total = from_currency.toNumber( total );
		}

		if ( 'USD' === to ) {
			return total / idea_mcg.exchange_rate.rates[ from ];
		}

		// Convert to USD
		return total * idea_mcg.exchange_rate.rates[ to ] / idea_mcg.exchange_rate.rates[ from ];
	},

	/**
	 * Get currency code from Currency name.
	 * Example: for name 'Australian Dollar' output will be 'AUD'.
	 * 
	 * @param {string} name Currency Name
	 */
	find_currency_code_by_name: function ( name ) {
		for ( var i in idea_mcg.all_currencies ) {
			var currency = idea_mcg.all_currencies[ i ];
			if ( currency.name === name ) {
				return i;
			}
		}
	},

	/**
	 * Get user define product price.
	 * @param int form_id 
	 * @param string current_currency 
	 */
	get_user_define_price: function ( form_id, current_currency ) {
		var amt = 0;
		$( "#gform_wrapper_" + form_id ).find( '.gfield_price' ).each( function () {
			var old_amt = 0;
			if ( $( this ).find( '.ginput_container_product_price input.ginput_amount' ).length ) {
				var old_currency_config = idea_mcg.all_currencies[ current_currency ];
				var existing_html_price = $( this ).find( '.ginput_container_product_price input.ginput_amount' ).val();
				var old_currency_obj = new Currency( old_currency_config );

				old_amt = old_currency_obj.toNumber( existing_html_price );
			}
			amt = amt + old_amt;
		} );
		return amt;
	},

	// -----------------------------------------
	// ------------- MANUAL MODE----------------
	// -----------------------------------------

	gformCalculateProductPrice: function ( form_id, productFieldId ) {
		var price = mcg.gformGetBasePrice_manual( form_id, productFieldId );
		var quantity = gformGetProductQuantity( form_id, productFieldId );
	
		//calculating options if quantity is more than 0 (a product was selected).
		if ( quantity > 0 ) {	
			//setting global variable if quantity is more than 0 (a product was selected). Will be used when calculating total
			_anyProductSelected = true;
		}

		price = price * quantity;

		price = gformRoundPrice( price );

		return price;
	},

	gformGetBasePrice_manual: function ( form_id, productFieldId ) {
		var suffix = "_" + form_id + "_" + productFieldId;
		var price = 0;
		var productField = jQuery( "#ginput_base_price" + suffix + ", .gfield_donation" + suffix + " input[type=\"text\"], .gfield_product" + suffix + " .ginput_amount" );
		
		if ( productField.length > 0 ) {

			price = mcg.product_get_manual_price_in_current_currency( form_id, productFieldId );

			//If field is hidden by conditional logic, don't count it for the total
			if ( gformIsHidden( productField ) ) {
				price = 0;
			}
		}

		return price;
	},

	getManualShipping: function ( form_id ) {
		var manual_shipping = window[ 'mcg_form_' + form_id ][ 'manual_shipping' ];
		var current_currency = window[ 'mcg_form_' + form_id ][ 'current_currency' ];
		var shipping = manual_shipping ? manual_shipping[ current_currency ] : 0;
		return shipping;
	},

	/**
	 * Return manual price for the given product field ID.
	 * 
	 * @param {int} form_id        Form ID.
	 * @param {int} productFieldId Product Field ID.
	 */
	product_get_manual_price_in_current_currency: function ( form_id, productFieldId ) {
		// var flattened_price = $( this ).find( '[id^=ginput_base_price_]' ).data( 'mcg-base-price' );
		var product_manual_price = window[ 'mcg_form_' + form_id ][ 'manual_prices' ][ productFieldId ];
		var current_currency = window[ 'mcg_form_' + form_id ][ 'current_currency' ];
		var price = product_manual_price ? product_manual_price[ current_currency ] : 0;
		return price;
	},

	/**
	 * Change currency for stripe.
	 *
	 * @param {string} old_currency Old Currency before conversion by MCG.
	 * @param {int}    form_id      Form ID.
	 */
	stripe_change_currency: function ( old_currency, form_id ) {
		// If multi-currency field exists then return the selected currency.
		if ( $( '#gform_' + form_id + ' .ginput_container_multicurrency' ).length ) {
			return $( '#gform_' + form_id + ' .ginput_container_multicurrency' ).find( 'select' ).val()
		} else {
			return old_currency;
		}
	},
	
	/**
	 * Set country based on customer's country.
	 */
	set_currency_based_on_visitor_ip: function () {
		if ( mcg.is_resumed_page_load() || gform.applyFilters( 'mcg_skip_geo_ip_currency', false ) ) {
			return;
		}

		$( ".gform_wrapper" ).each( function () {
			var form_id = mcg.get_form_id( $( this ) );
			
			if ( !form_id ) return;
			
			var is_geo_auto_currency = window[ 'mcg_form_' + form_id ].is_geo_auto_currency;

			if ( 1 != is_geo_auto_currency ) {
				return;
			}

			var country_code = false;
			try {
				country_code = localStorage.getItem( 'mcg_country_code' );
			} catch ( err ) { }

			// Fetch country code if not in cache.
			if ( !country_code ) {
				jQuery.get( "https://api.ipregistry.co/?key=tryout" ).done( function ( data ) {
					if ( !data ) {
						return;
					}

					var country_code = data?.location?.country?.code
					mcg.set_currency_from_country_code( country_code );
					try {
						localStorage.setItem( 'mcg_country_code', country_code );
						$( window ).trigger( 'mcg_country_code_set' );
					} catch ( err ) {
						console.log( "Can't set localstorage" );
					}
				} );
			} else {
				mcg.set_currency_from_country_code( country_code );
			}
		} );
	},

	/**
	 * Select the currency from the country code passed. 
	 * Example: If IN is passed as country_code, it will select INR currency.
	 * 
	 * @param {string} country_code Country Code example: IN.
	 */
	set_currency_from_country_code: function ( country_code ) {
		jQuery.each( mcg_countries, function ( key, val ) {
			if ( val.abbreviation == country_code ) {
				jQuery( ".ginput_container_multicurrency select" ).find( "option[value=" + val.currency + "]" ).prop( "selected", true ).change();
			}
		} );
	},

	/**
	 * If there is a valudation error or its a resumed submission then return true.
	 *
	 * @returns bool.
	 */
	is_resumed_page_load: function () {
		return undefined !== jQuery( ".ginput_container_multicurrency select" ).attr( 'data-resumed' );
	},

	/**
	 * Convert decimal and thousand seperator when currency changes.
	 * Example: If we are going from USD to EUR, we need to change 
	 * all commas to dots and all dots to commas.
	 * 
	 * @param string money          Amount to be converted.
	 * @param object from_currency  From Currency object.
	 * @param object to_currency    To Currency object.
	 */
	convert_currency_format: function ( money, from_currency, to_currency ) {
		if ( typeof from_currency === 'string' ) {
			from_currency = new Currency( idea_mcg[ "all_currencies" ][ from_currency ] );
		}

		if ( typeof to_currency === 'string' ) {
			to_currency = new Currency( idea_mcg[ "all_currencies" ][ to_currency ] );
		}

		var number = from_currency.toNumber( money );

		if ( ',' === to_currency.currency.decimal_separator ) {
			number = new String( number );
			number = number.replace( '.', ',' );
		}

		return to_currency.toMoney( number );
	},

	filter_gform_get_base_price: function ( price, formId, productFieldId ) {
		var suffix = "_" + formId + "_" + productFieldId;			

		var $field = $( '#field' + suffix );
		// if its a user defined product price, dont change anything.
		if ( $field.hasClass( 'gfield--input-type-price' ) ) {
			return price;
		}

		// convert price to current price.
		if ( formId && window[ 'mcg_form_' + formId ] ) {
			if ( 'manual' === window[ 'mcg_form_' + formId ].mode ) {
				var price = mcg.product_get_manual_price_in_current_currency( formId, productFieldId );
				return parseFloat( price );
			} else {
				var price = mcg.get_product_price_flattened( $( `.gfield_product${suffix}` ) );
				var current_currency = mcg.get_current_currency( formId );
				var base_currency = window[ 'mcg_form_' + formId ].base_currency;
				price = mcg.convert_currency( base_currency, current_currency, mcg.unflatten( price ) );
			}

			return parseFloat(price);
		}

		return mcg.unflatten( price );
	},

	filter_gform_get_shipping_price: function ( shipping_price, formId ) {
		var shippingField = jQuery(".gfield_shipping_" + formId + " input[readonly], .gfield_shipping_" + formId + " select, .gfield_shipping_" + formId + " input:checked");
		var shipping = 0;

		var mode = mcg.getMode( formId );
		if( shippingField.length == 1 && !gformIsHidden(shippingField) ) {
			if ( 'manual' === mode ) { 
				shipping = mcg.getManualShipping( formId );
			} else {
				var $el = $( `#gform_8 .gfield_shipping` ).find( 'option:selected, input[type=radio]:checked, input[type=text].ginput_shipping_price' );
				shipping = mcg.unflatten( $el.data( 'mcg-base-price' ) );
				shipping = mcg.convert_currency( window[ 'mcg_form_' + formId ].base_currency, mcg.get_current_currency( formId ), shipping );
			}
		}

		if ( isNaN( shipping ) ) {
			return 0;
		}

		// max 2 decimal places.
		shipping = Math.round( shipping * 100 ) / 100;
		return parseFloat( shipping );
	},

	/**
	 * Returns the flattened price of the product. In case of the radio/dropdown field, it returns the price of the selected option.
	 * 
	 * @param {int} price 
	 */
	get_product_price_flattened: function ( $product_field ) { 
		var price = false;

		$product_field = $product_field.filter( ':not(.gfield--type-quantity)' );

		// if there are multiple fields and one of them is `.gfield--type-option` then use that.
		if ( $product_field.length > 1 && $product_field.hasClass( 'gfield--type-option' ) ) {
			$product_field = $product_field.filter( '.gfield--type-option' );
		}
		
		if ( $product_field.hasClass( 'gfield--input-type-radio' ) ) {
			var $selected_radio = $product_field.find( 'input[type="radio"]:checked' );
			price = $selected_radio.data( 'mcg-base-price' );
		} else if ( $product_field.hasClass( 'gfield--input-type-select' ) ) {
			var $selected_option = $product_field.find( 'option:selected' );
			price = $selected_option.data( 'mcg-base-price' );
		} else if ( $product_field.hasClass( 'gfield--input-type-calculation' ) ) {
			var formatted_price = $product_field.find( '.ginput_product_price' ).text();
			price = mcg.flatten_formatted_price( formatted_price );
		} else if ( $product_field.hasClass( 'gfield--input-type-checkbox' ) ) { 
			var $selected_checkbox = $product_field.find( 'input[type="checkbox"]:checked' );
			$selected_checkbox.each( function () {
				price += $( this ).data( 'mcg-base-price' );
			} );
		} else {
			var $input = $product_field.find( '[data-mcg-base-price]' );
			if ( $input.length ) {
				price = $input.data( 'mcg-base-price' );
			}
		}
		
		return gform.applyFilters( 'mcg_get_product_price_flattened', price, $product_field );
	},

	/**
	 * Flatten the amount. I.e. convert it to cents.
	 *
	 * @param {price} price Price.
	 * @param {string} from_currency From Currency.
	 *
	 * @returns Flattened amount.
	 */
	flatten: function ( price, from_currency ) {
		if ( !from_currency ) {
			var $mcg_select = $( '.ginput_container_multicurrency select' );
			from_currency = $mcg_select.val();
		}

		var currency = new Currency( idea_mcg[ "all_currencies" ][ from_currency ] );
		var number   = currency.toNumber( price );

		return number * 100;
	},

	flatten_formatted_price: function ( formatted_price ) {
		var currency = new Currency( gf_global.gf_currency_config );
		var number   = currency.toNumber( formatted_price );

		return number * 100;
	},

	/**
	 * Unfltten the amount.
	 * 
	 * @param {int} flat_amt Flatt amount. Ex: 1905 
	 * @returns float, unflat amount ex: 19.05
	 */
	unflatten: function ( flat_amt ) {
		return flat_amt / 100;
	},

	/**
	 * Get current currency.
	 *
	 * @param {*} $form_id 
	 */
	get_current_currency: function ( form_id ) {
		var $mcg_select = $( ` #gform_${form_id} .ginput_container_multicurrency select` );
		return $mcg_select.val();
	},

	/**
	 * To fix the problem when back button is pressed after changing curency and saving form
	 * we need to save the value of selected dropdown and radio fields in localstorage so we 
	 * can restore it on back button.
	 * 
	 * Since the value of dropdown and options are updated (because of currency change) browser doesn't automatically
	 * restore the value of dropdown and radio fields.
	 */
	save_option_value_in_localstorage: function () {
		// save value of selected dropdown and radio fields in localstorage so we can restore it on back button.
		$( '.gfield--input-type-select, .gfield--input-type-radio' ).each( function () {
			var $selected_option = $( this ).find( 'option:selected, input[type=radio]:checked' );
			if ( $selected_option.length ) {
				var val = $selected_option.val();
				var field_id = gf_get_input_id_by_html_id( $( this ).attr( 'id' ) );
				var form_id = mcg.get_form_id( $( this ) );
				var key = `mcg_input_${form_id}_${field_id}`;
				localStorage.setItem( key, val );
			}
		} );
	},

	/**
	 * Restore the value of dropdown and radio fields from localstorage when back button is pressed.
	 */
	restore_value_of_dropdown_and_radio_fields: function () {
		// restore value of selected dropdown and radio fields from localstorage.
		$( '.gfield--input-type-select, .gfield--input-type-radio' ).each( function () {
			var field_id = gf_get_input_id_by_html_id( $( this ).attr( 'id' ) );
			var form_id = mcg.get_form_id( $( this ) );
			var key = `mcg_input_${form_id}_${field_id}`;
			var val = localStorage.getItem( key );
			if ( val ) {
				mcg.select_field_option_by_first_part( $( this ), val );
			}
		} );
	},

	/**
	 * Select the option of dropdown and radio fields by matching the first part only (before |).
	 * We ignore the second part because it is the price which is different for each currency.
	 *
	 * @param jQuery Object $field Field.
	 * @param string val Whole value of the option to be selected, not just the first part.
	 */
	select_field_option_by_first_part: function ( $field, val ) { 
		val = val.split( '|' )[ 0 ];
		
		// loop through all options and select the one which matches the value.
		$field.find('option, input[type=radio]').each(function() {
			var thisval = $(this).val();
			thisval = thisval.split('|')[0];
			if (thisval == val) {
				if ($(this).is('option')) {
					$(this).prop('selected', true);
				} else if ($(this).is('input')) {
					$(this).prop('checked', true);
				}
			}
		});
	},

	/**
	 * Pass the flat amount and get the price in current currency.
	 *
	 * @param int flat_amt 
	 * @param int form_id 
	 *
	 * @returns 
	 */
	flatToCurrentCurrency: function( flat_amt, form_id, return_number = false ) {
		var current_currency = _mcg.get_current_currency( form_id );
		var base_currency = window[ 'mcg_form_' + form_id ].base_currency;
		var price = _mcg.convert_currency( base_currency, current_currency, _mcg.unflatten( flat_amt ) );
		price = Math.round( price * 100 ) / 100; // round to 2 decimal places.

		if ( return_number ) {
			return price;
		}

		var currencyObj = new Currency( idea_mcg[ "all_currencies" ][ current_currency ] );
		return currencyObj.toMoney( price, true );
	},

	setPrice: function ( $field, new_amt_formatted, update_values = true ) {
		if ( ! $field.find( 'span.ginput_product_price' ).length ) {
			$field.find( '.ginput_container_singleproduct .ginput_product_price_label' ).after('<span class="ginput_product_price"></span>');
			$field.find( '.ginput_container_singleproduct [id^=ginput_base_price_]' ).hide();
		}

		$field.find( '.ginput_product_price' ).html( new_amt_formatted );
		
		if ( update_values ) {
			$field.find( '.ginput_container_product_price input.ginput_amount' ).val( new_amt_formatted );
			$field.find( '.ginput_product_price' ).val( new_amt_formatted );
			$field.find( `[id^=ginput_base_price_]` ).val( new_amt_formatted );
		}
	},

	/**
	 * Intelligently convert the formatted price to number.
	 */
	toNumber: function(formatted_price, return_flattened = false) {
		
		if ( typeof formatted_price === 'number' ) { 
			return formatted_price;
		}

		// Remove any non-digit characters from the formatted price.
		var number = formatted_price.replace(/[^0-9.\-/,]+/g, '');

		// These currencies have comma as decimal separator
		if (formatted_price.includes('Kč') || formatted_price.includes('zł') || formatted_price.includes('€') || formatted_price.includes('Kr') || formatted_price.includes('Ft') || formatted_price.includes('R$') ) {
			// Remove any thousands separators (spaces or ')
			number = number.replace(/[\s'/.]/g, '');
			number = number.replace(/[/,]/g, '.'); // convert comma to dot because Euro ¯\_(ツ)_/¯. 
		} else if (formatted_price.includes('pyб') || formatted_price.includes('CZK')) {
			// Remove any thousands separators (spaces)
			number = number.replace(/\s/g, '');
		} else if ( formatted_price.includes('Fr.') || formatted_price.includes('CHF') ) {
			// Remove any thousands separators (')
			number = number.replace(/'/g, '');
		} else {
			// Remove any thousands separators (,)
			number = number.replace(/,/g, '');
		} 

		number = parseFloat( number ).toFixed( 2 );
		
		return return_flattened ? _mcg.flatten( number ) : number;
	},

	/**
	 * Get mode - 'automatic' or 'manual'.
	 * 
	 * @param {int} form_id Form ID.
	 */
	getMode: function( form_id ) {
		return ( window[ 'mcg_form_' + form_id ] ) ? window[ 'mcg_form_' + form_id ][ 'mode' ] : false;
	},
};

$( document ).ready( function () {
	mcg.on_ready();
} );

$( window ).on( 'pageshow', function () {
	if (window.performance && window.performance.navigation.type === window.performance.navigation.TYPE_BACK_FORWARD) {
		$( ".gform_wrapper .ginput_container_multicurrency select" ).trigger( 'change' );
		mcg.restore_value_of_dropdown_and_radio_fields();
	}
});

window._mcg = mcg;
