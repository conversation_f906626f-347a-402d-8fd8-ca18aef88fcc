jQuery( function () {
	
	/**
	 * Overriding GravityForms' function because we want to change the output of this function.
	 * When product's prices are calculated, this function is called. 
	 * This need to change the output of this to fix the problem where value of the total is 
	 * incorrect because of curreny format difference in EUR.
	 * 
	 * Todo: ask GravityForms to add a filter to the output of this function and get rid of this copy.
	 * 
	 * @param {int} formId         Form ID.
	 * @param {int} productFieldId Product Field ID
	 * @returns {float} Bade Price of the given product ID. 
	*/
	gformGetBasePrice = function ( formId, productFieldId ) {
		var suffix = "_" + formId + "_" + productFieldId;
		var price = 0;
		var productField = jQuery( "#ginput_base_price" + suffix + ", .gfield_donation" + suffix + " input[type=\"text\"], .gfield_product" + suffix + " .ginput_amount" );
		if ( productField.length > 0 ) {
			price = productField.val();

			//If field is hidden by conditional logic, don't count it for the total
			if ( gformIsHidden( productField ) ) {
				price = 0;
			}
		}
		else {
			productField = jQuery( ".gfield_product" + suffix + " select, .gfield_product" + suffix + " input:checked, .gfield_donation" + suffix + " select, .gfield_donation" + suffix + " input:checked" );
			var val = productField.val();
			if ( val ) {
				val = val.split( "|" );
				price = val.length > 1 ? val[ 1 ] : 0;
			}

			//If field is hidden by conditional logic, don't count it for the total
			if ( gformIsHidden( productField ) )
				price = 0;

		}

		var c = new Currency( gf_global.gf_currency_config );
		price = c.toNumber( price );
		price === false ? 0 : price;

		return gform.applyFilters( 'gform_get_base_price', price, formId, productFieldId );
	}

	gformGetShippingPrice = function(formId){
		var shippingField = jQuery(".gfield_shipping_" + formId + " input[readonly], .gfield_shipping_" + formId + " select, .gfield_shipping_" + formId + " input:checked");
		var shipping = 0;
		if(shippingField.length == 1 && !gformIsHidden(shippingField)){
			if(shippingField.attr("readonly"))
				shipping = shippingField.val();
			else
				shipping = gformGetPrice(shippingField.val());
		}
	
		return gform.applyFilters( 'gform_get_shipping_price', gformToNumber( shipping ), formId );
	}
} );