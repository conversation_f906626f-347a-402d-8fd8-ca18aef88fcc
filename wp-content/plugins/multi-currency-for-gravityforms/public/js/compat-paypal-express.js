const PaypalExpress = {
	init: function () {
		// Compatiblity with Paypal Express.
		PaypalExpress.maybe_change_decimals_for_paypal_express();
		PaypalExpress.refresh_currency_for_paypal_express();
	},

	/**
	 * If paypal is enabled then change the decimals to 1.
	 * @returns 
	 */
	maybe_change_decimals_for_paypal_express: function () {
		if ( !jQuery( '.gfield--type-paypal' ).length ) {
			return;
		}

		// if paypal is enabled then change the decimals to 1.
		for (const [key, value] of Object.entries(idea_mcg.all_currencies)) {
			if ( idea_mcg.all_currencies[key].decimals >= 2 ) {
				idea_mcg.all_currencies[key].decimals = 1;
			}
		}

		idea_mcg.all_currencies[ 'TWD' ].decimals = 0;
	},
	
	/**
	 * Refresh the currency for paypal express so that it can pick the new currency.
	 */
	refresh_currency_for_paypal_express: function () {
		if ( !gform.ppcp ) {
			return;
		}

		window.setTimeout( function () {
			jQuery( '.gform_wrapper' ).each( function () {
				var formId = mcg.get_form_id( jQuery( this ) );
				let manager = gform.ppcp.stateManager[ formId ];
				let dirty = [ 'currency' ];
				manager.stateChangeCallback( manager.isInitialChange, dirty, manager.state, manager.prevState )
			} );
		}, 3000 );
	},
}

export default PaypalExpress;