const CompatConditionalPricing = {
	init: function () { 
		if ( 'undefined' === typeof GWConditionalPricing ) {
			return;
		}

		if ( ! jQuery( '.gfield--type-multicurrency' ).length ) {
			return;
		}

		// Run this before mcg.filter_gform_get_base_price. Pass the conditional price.
		gform.addFilter( 'mcg_get_product_price_flattened', CompatConditionalPricing.updateBasePrice, 5 );

		/**
		 * Convert the prices before its saved by Conditional Pricing.
		 *
		 * args = {gwcp, formId, input,inputId,isReset, productId}
		 */
		gform.addFilter( 'gpcp_price', function ( origPrice, args ) {
			var new_amt = _mcg.flatToCurrentCurrency( origPrice*100, args.formId, true );		
			return new_amt;
		} );

		gform.addFilter( 'mcg_calculation_skip_update_price', CompatConditionalPricing.skipUpdatePrice, 10 );
	},

	skipUpdatePrice: function ( skip ) {
		// Prevent possible loop by saving the execution time and then returning true if
		// the function is called again within 50 ms.
		window.gwcp_price_last_execution = window.gwcp_price_last_execution || 0;

		console.log('diff', Date.now() - window.gwcp_price_last_execution );
		if ( Date.now() - window.gwcp_price_last_execution < 100 ) {
			return true;
		}

		if ( !jQuery( '.gfield--input-type-calculation' ).length ) {
			return true;
		}

		window.gwcp_price_last_execution = Date.now();
	},

	/**
	 * Change the base price of the product field based on the conditional pricing.
	 *
	 * Code inspired from updatePricing() function of Conditional Pricing
	 * (gwconditionalpricing/scripts/gwconditionalpricing.js)
	 * 
	 * @param {*} fieldId 
	 * @param {*} productId 
	 *
	 * @returns float|false
	 */
	getConditionalPrice: function ( formId, productFieldId ) {
		if ( ! GWConditionalPricing || ! GWConditionalPricing.getFormElement ) {
			return false;
		}

		let gfcp = GWConditionalPricing.getFormElement( formId ).data( 'gwcp' );
		let price = false;

		if ( ! gfcp || ! gfcp._pricingLogic || ! productFieldId ) {
			return price;
		}

		for ( var productId in gfcp._pricingLogic ) {
			// only run for one product.
			if ( productId !== productFieldId ) {
				continue;
			}

			if ( !gfcp._pricingLogic.hasOwnProperty( productId ) ) {
				continue;
			}

			var pricingLevels = gfcp._pricingLogic[ productId ];

			// This can happen if they delete a product from the form without removing the conditional pricing rules.
			if ( !pricingLevels || pricingLevels[ 0 ] === null ) {
				continue;
			}

			for ( var i = 0; i < pricingLevels.length; i++ ) {

				var pricingLevel = pricingLevels[ i ],
					isMatch = gfcp.isMatch( gfcp._formId, pricingLevel.conditionalLogic );

				if ( !isMatch ) {
					continue;
				}

				return pricingLevel.price;
			}

			// if no matching pricing level was found, set back to basePrice.
			return price;
		}
	},

	/**
	 * Change the base price of the product field based on the conditional pricing.
	 *
	 * Code inspired from updatePricing() function of Conditional Pricing
	 * (gwconditionalpricing/scripts/gwconditionalpricing.js)
	 *
	 * @param {*} price 
	 * @param {*} $product_field 
	 * @returns 
	 */
	updateBasePrice: function( price, $product_field ) {
		var formId = _mcg.get_form_id( $product_field );
		var productFieldId = gf_get_input_id_by_html_id( $product_field.attr( 'id' ) );
		
		var conditionalPrice = CompatConditionalPricing.getConditionalPrice( formId, productFieldId );
		if ( ! conditionalPrice ) {
			return price;
		}

		// if a matching pricing level was found, set the price to the new price after currency conversion
		return conditionalPrice * 100;
	},

}

export default CompatConditionalPricing;