const CalculationField = {
	/**
	 * Init.
	 */
	init: function () {
		gform.addAction( 'gform_input_change', CalculationField.update_product_prices_on_input_change, 12 );
		CalculationField.add_duplicate_pricing_label();
		jQuery( '.gform_wrapper form' ).on( 'submit', CalculationField.set_calculation_field_on_submit );
		gform.addFilter( 'gform_merge_tag_value_pre_calculation', CalculationField.filter_merge_tag_value_pre_calculation );
	},

	/**
	 * Change product prices when value of any input field changes (gform_input_change).
	 */
	update_product_prices_on_input_change: function ( elem, formId, fieldId ) {
		var $form = jQuery( elem ).closest( '.gform_wrapper' );
		var mode    = _mcg.getMode( formId );
		var $select = $form.find( '.ginput_container_multicurrency select' );

		if ( !$select ) {
			return;
		}

		var new_currency = $select.val();

		// Update Each Product Price.
		if ( 'automatic' !== mode ) {
			return;
		}

		if ( gform.applyFilters( 'mcg_calculation_skip_update_price', false, fieldId, formId, new_currency ) ) {
			return;
		}

		_mcg.update_product_field_price_automatic( formId, new_currency );
	},

	/**
	 * Add duplicate pricing label for calculation field with class `mcg_ginput_product_price`.
	*/
	add_duplicate_pricing_label: function () {
		jQuery( '.gfield--input-type-calculation' ).each( function () {
			if ( 0 === jQuery( this ).find( '.mcg_ginput_product_price' ).length ) {
				let $price = jQuery( this ).find( '.ginput_product_price' );
				$price.after( '<label class="mcg_ginput_product_price gform-field-label"></label>' );
				$price.hide();
			}
		} );
	},

	/**
	 * Set calculation field on submit.
	 */
	set_calculation_field_on_submit: function () {
		jQuery(this).find('.gfield--input-type-calculation' ).each( function () {
			var suffix = jQuery(this).attr( 'id' ).replace( 'field', '' ); // suffix is like: _[formid]_[fieldid]
			let $mcg_price = jQuery( this ).find( '.mcg_ginput_product_price' );
			let price = $mcg_price.text();
			jQuery( this ).find( '#ginput_base_price' + suffix ).val( price );
		} );
	},

	/**
	 * Change currency before the value is used for calculation.
	 *
	 * @param {*} value 
	 * @param {*} match 
	 * @param {*} isVisible 
	 * @param {*} formulaField 
	 * @param {*} formId 
	 * @returns 
	 */
	filter_merge_tag_value_pre_calculation: function ( value, match, isVisible, formulaField, formId ) {
		return _mcg.toNumber( value );
	}
}

export default CalculationField;