const Stripe = {
	init: function () {
		// This is tempoaray code, remove it when GF has officially added the filter.
		gform.addFilter('gform_stripe_update_payment_amount', function( config ) {
			var currency = _mcg.get_current_currency( formId )
			config.currency = currency.toLowerCase();
			return config;
		} );
		
		gform.addFilter( 'gform_stripe_payment_element_updated_payment_information', function ( paymentInformation, initPaymentInfo, feedId, formId ) {
			var currency = _mcg.get_current_currency( formId )

			if ( !currency ) {
				return paymentInformation; 
			}

			paymentInformation.currency = currency.toLowerCase();
			return paymentInformation;
		} );
	
	},
}

export default Stripe;