=== Multi-Currency For GravityForms ===
Contributors: ideawp
Donate link: http://ideawp.com
Tags: multicurrency, gravityforms, payment, stripe, multi-currency
Requires at least: 3.0.1
Tested up to: 6.5
Stable tag: 1.2.1
License: GPLv2 or later
License URI: http://www.gnu.org/licenses/gpl-2.0.html

Multi-Currency For Gravity Forms enables seamless currency conversion, allowing customers to view and complete transactions in their local currency. With automatic or manual mode options, support for all currencies, and real-time exchange rate updates, it's the ultimate Multi-currency solution for Gravity Forms.

== Description ==

Multi-Currency For Gravity Forms is a powerful Gravity Forms plugin that seamlessly integrates with Gravity Forms, enabling you to accept payments in multiple currencies. With features like a currency switcher, automatic or manual conversion modes, support for all currencies, and real-time exchange rate updates, it simplifies global e-commerce and helps you expand your business worldwide.

With Multi-Currency For Gravity Forms, you can effortlessly tap into new markets and cater to customers from all around the world. No longer will language barriers or currency conversions stand in the way of your global success. Whether you’re selling physical products, digital downloads, or services, we have you covered.


== Installation ==

1. Upload `multi-currency-for-gf` to the `/wp-content/plugins/` directory
1. Activate the plugin through the 'Plugins' menu in WordPress

== Frequently Asked Questions ==

= What is the Multi-Currency for Gravity Forms plugin? =
The Multi-Currency for Gravity Forms is a WordPress plugin designed to facilitate easy and efficient handling of international transactions. It provides automatic currency conversion, manual pricing in different currencies, and compatibility with major payment gateways.

= How does the automatic currency rate conversion work? =
The plugin integrates with the openexchangerates.org API to provide real-time currency conversion. This ensures all transactions are processed at the most accurate and up-to-date exchange rates.

= Can I set my own prices in different currencies? =
Yes, the plugin allows you to manually set prices in each currency. This gives you the flexibility to tailor your pricing strategy to different markets.



== Screenshots ==


== Changelog ==  
# Version 1.8.1 | 2024-05-04  
[fix] Bug related to Stripe compatibility    
pl
# Version 1.8.0 | 2024-04-30  
[new] Compatiblity with Stripe Elements  
[new] Compatibility with E-commerce Fields by GravityWiz  

== Upgrade Notice ==