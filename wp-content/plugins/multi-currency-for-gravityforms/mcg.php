<?php
/**
 * Plugin Name: Multi-Currency for Gravity Forms
 * Plugin URI:  https://ideawp.com/plugin/multi-currency-for-gravityforms/
 * Description: Multicurrency for GravityForms.
 * Version: 1.8.1
 * Update URI: https://api.freemius.com
 * Author: IdeaWP
 * Author URI:  https://ideawp.com/plugin/multi-currency-for-gravityforms/
 * License: GPL-2.0+
 * License URI: http://www.gnu.org/licenses/gpl-2.0.txt
 * Text Domain: idea_mcg
 * Domain Path: /languages
 *
 * @package MCG
 */

// If this file is called directly, abort.
if ( ! defined( 'WPINC' ) ) {
	die;
}

/**
 * Constants
 */
define( 'MCG_PLUGIN_VERSION', '1.8.1' );
define( 'MCG_PATH', plugin_dir_path( __FILE__ ) );
define( 'MCG_URL', plugin_dir_url( __FILE__ ) );
define( 'MCG_FILE', __FILE__ );

/**
 * The core plugin class that is used to define internationalization,
 */
require plugin_dir_path( __FILE__ ) . 'includes/class-mcg.php';

/**
 * Begins execution of the plugin.
 */
function mcg_run() {
	$GLOBALS['mcg'] = new Mcg();
}

mcg_run();
