# Copyright (C) 2024 idea_mcg
# This file is distributed under the same license as the idea_mcg package.
msgid ""
msgstr ""
"Project-Id-Version: idea_mcg\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"POT-Creation-Date: 2024-05-04 13:45+0000\n"
"X-Poedit-Basepath: ..\n"
"X-Poedit-KeywordsList: __;_e;_ex:1,2c;_n:1,2;_n_noop:1,2;_nx:1,2,4c;_nx_noop:1,2,3c;_x:1,2c;esc_attr__;esc_attr_e;esc_attr_x:1,2c;esc_html__;esc_html_e;esc_html_x:1,2c\n"
"X-Poedit-SearchPath-0: .\n"
"X-Poedit-SearchPathExcluded-0: *.js\n"
"X-Poedit-SourceCharset: UTF-8\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#: includes/class-mcg-gf-addon.php:173
msgid "Tutorial"
msgstr ""

#: includes/class-mcg-gf-addon.php:174
msgid "Read Documentation"
msgstr ""

#: includes/class-mcg-gf-addon.php:176
msgid "Need Help?"
msgstr ""

#: includes/class-mcg-gf-addon.php:177
msgid "Submit a Support Ticket"
msgstr ""

#. translators: link of Open exchange rates site.
#: includes/class-mcg-gf-addon.php:181
msgid "%1$sYou can get it for free from %2$shere.%3$s"
msgstr ""

#: includes/class-mcg-gf-addon.php:184
msgid "API Settings"
msgstr ""

#: includes/class-mcg-gf-addon.php:189
msgid "Open Exchange Rates API"
msgstr ""

#: includes/class-mcg-gf-addon.php:210
msgid "Multi Currency Settings"
msgstr ""

#: includes/class-mcg-gf-addon.php:213
msgid "Form Base Currency"
msgstr ""

#: includes/class-mcg-gf-addon.php:216
msgid "Base currency for the form"
msgstr ""

#: includes/class-mcg-gf-addon.php:221
msgid "Auto currency selection"
msgstr ""

#: includes/class-mcg-gf-addon.php:226
msgid "Automatically set visitor's currency based on their country/IP"
msgstr ""

#: includes/class-mcg-gf-addon.php:228
msgid "Select currency based on visitor's IP."
msgstr ""

#: includes/class-mcg-gf-addon.php:234
msgid "Process Payment in"
msgstr ""

#: includes/class-mcg-gf-addon.php:238
msgid "If your payment gateway doesn't support multi-currencies, you can choose \"Form's base currency\" option. Whatever amount the user selects in his currency will be converted into the base currency and then sent to payment gateway for the payment."
msgstr ""

#: includes/class-mcg-gf-addon.php:241
msgid "User's selected currency."
msgstr ""

#: includes/class-mcg-gf-addon.php:245
msgid "Form's base currency."
msgstr ""

#: includes/class-mcg-gf-addon.php:320
msgid "Automatic"
msgstr ""

#: includes/class-mcg-gf-addon.php:330
msgid "Manual"
msgstr ""

#: includes/class-mcg-gf-addon.php:334
msgid "Please set the manual prices in Product field(s)."
msgstr ""

#: includes/class-mcg-gf-addon.php:352
msgid "All currencies"
msgstr ""

#: includes/class-mcg-gf-addon.php:362
msgid "Specific Currencies"
msgstr ""

#: includes/class-mcg-gf-addon.php:457
msgid "Base Price"
msgstr ""

#. translators: |n is used for line break
#: includes/class-mcg-gf-addon.php:459
msgid "Enter the base price for this product. |n|n Multi-Currency: In case of Automatic mode, you must enter price in the base currency of form. |n|n For manual mode, you can ignore this field and set prices in the \"Multi-currency Manual Prices\" section above"
msgstr ""

#. translators: |n is used for line break
#: includes/class-mcg-gf-addon.php:463
msgid "Multicurrency Mode"
msgstr ""

#: includes/class-mcg-gf-addon.php:464
msgid "Automatic: Automatic realtime currency exchange rate by openexchangerate.org API.|n|n Manual: You can set the prices for each currency manually in product field."
msgstr ""

#: includes/class-mcg-gf-addon.php:467
msgid "Multicurrency Manual Prices"
msgstr ""

#: includes/class-mcg-gf-addon.php:468
msgid "Set price of the product in each currency."
msgstr ""

#: includes/class-mcg-multicurrency-gf-field.php:43
msgid "Currency"
msgstr ""

#: includes/class-mcg-multicurrency-gf-field.php:54
msgid "Currency Selector"
msgstr ""

#: includes/class-mcg.php:112
msgid "Settings"
msgstr ""

#: includes/class-mcg.php:122
msgid "Support"
msgstr ""

#. translators: link of mcg plugin.
#: includes/class-mcg.php:213
msgid "Multi-Currency for Gravity Forms plugin requires the %1$sOpen Exchange Rates%2$s API to work"
msgstr ""
