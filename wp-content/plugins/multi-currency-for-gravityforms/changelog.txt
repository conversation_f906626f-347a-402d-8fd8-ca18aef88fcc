# Version 1.8.1 | 2024-05-04
[fix] Bug related to Stripe compatibility    

# Version 1.8.0 | 2024-04-30  
[new] Compatiblity with Stripe Elements  
[new] Compatibility with E-commerce Fields by GravityWiz  

# Version 1.7.0 | 2024-03-16  
[new] Compatibility with Conditional pricing by GravityWiz  
[new] Compatiblity with Paypal Express addon  
[fix] Fatal error on form submission in some cases  

# Version 1.6.0 | 2024-01-20
[new] Add support for calculation field
[new] Add support for quantity field
[fix] Fix incorrect value in the merge tag of the total field
[fix] Fix bugs in form edit page when the global currency setting is SF Rand

# Version 1.5.0 | 2023-12-27  
[new] Add support for select and radio product field  
[update] Freemius SDK  
[fix] Fix Euro currency format issue  
[fix] Bug where product and shipping cost would concatenate instead of adding  
[fix] Fix bug where shipping is not updated for automatic currency mode  
[fix] fix hidden product and problem with shipping  
[fix] Refactor code to handle multiple products and shipping fields  

# Version 1.4.1 | 2021-06-24
 * [fix] Euro issue
 * [fix] Changed Lithuania currency code
 * [fix] swicthing currency from . decial to comma decimal
 * [fix] Gravity forms 2.5 compatibility

# Version 1.4.0 | 2020-10-24
 * [new] 'mcg_convertor' shortcode added
 * [update] Support for user define price & coupons
 * [fix] Headers already sent issue
 * [fix] NaN shipping amount issue

# Version 1.3.0 | 2020-08-26
 * [new] form settings - Payment Processing Currency
 * [new] Added support for Payment gateways which do not support multi-currency
 * [update] Added pot file
 * [fix] Fix admin JS function issue
 * [fix] Fix issue where final amount was incorrect for multiple manual mode products

# Version 1.2.0 | 2020-08-08
 * [fix] Admin currency correction
 * [fix] Fix newly added MCG field default mode

# Version 1.1.0 | 2020-07-28
 * [fix] MCG field missing JS issue

# Version 1.0.0 | 2020-07-20
 * [new] Initial release
