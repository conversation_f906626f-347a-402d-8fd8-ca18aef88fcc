<?php

/**
 * License class.
 */
class Mcg_Licence {
    /**
     * Instance.
     *
     * @var Mcg_Licence
     */
    public static $instance;

    /**
     * Freemius.
     *
     * @var freemius object.
     */
    public static $freemius;

    /**
     * Get instance.
     *
     * @return self
     */
    public static function get_instance() {
        if ( !isset( self::$instance ) ) {
            self::$instance = new self();
            self::$instance->init_licence();
            self::$instance->change_icon();
        }
        return self::$instance;
    }

    /**
     * Init
     */
    public function init_licence() {
        // Include Freemius SDK.
        require_once MCG_PATH . 'includes/vendor/freemius/wordpress-sdk/start.php';
        self::$freemius = fs_dynamic_init( array(
            'id'              => '13361',
            'slug'            => 'multi-currency-for-gravityforms',
            'premium_slug'    => 'multi-currency-for-gravityforms',
            'type'            => 'plugin',
            'public_key'      => 'pk_0732077b925de824c58ad27d68e32',
            'is_premium'      => true,
            'is_premium_only' => true,
            'has_addons'      => false,
            'has_paid_plans'  => true,
            'menu'            => array(
                'first-path' => 'admin.php?page=gf_settings&subview=multi-currency-gf',
            ),
            'is_live'         => true,
        ) );
        /**
         * Multi-currency for GravityForms Freemius loaded.
         *
         * @since 2.0.0
         */
        do_action( 'mcg_fs_loaded' );
    }

    /**
     * Chnage Icon.
     *
     * @return void
     */
    public function change_icon() {
        self::$freemius->add_filter( 'plugin_icon', function () {
            return dirname( dirname( __FILE__ ) ) . '/images/logo.jpg';
        } );
    }

    /**
     * Is license active
     *
     * @return bool
     */
    public function is_license_active() {
        return self::$freemius->can_use_premium_code();
    }

}
