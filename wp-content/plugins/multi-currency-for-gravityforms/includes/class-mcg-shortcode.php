<?php
/**
 * Manage shortcodes
 *
 * @link       http://ideawp.com
 * @since      1.4.0
 *
 * @package    Mcg
 * @subpackage Mcg/includes
 */

/**
 * Manage shortcodes
 */
class Mcg_Shortcode {

	/**
	 * Initialize.
	 *
	 * @return void
	 */
	public static function run() {
		add_shortcode( 'mcg_convertor', array( __CLASS__, 'mcg_convertor_shortcode' ) );
		add_action( 'init', array( __CLASS__, 'mcg_save_ip_base_currency' ) );
	}

	/**
	 * MCG convertor function
	 *
	 * @param array $atts Get shortcode parameter.
	 * @return string
	 */
	public static function mcg_convertor_shortcode( $atts ) {
		$default_atts = array(
			'amount'           => '',
			'default_currency' => '',
			'to_currency'      => '',
		);

		$atts = shortcode_atts( $default_atts, $atts, 'mcg_convertor' );

		$amount           = $atts['amount'];
		$default_currency = $atts['default_currency'];
		$to_currency      = $atts['to_currency'];

		$convert_price = '';
		if ( $amount && $default_currency ) {
			if ( ! $to_currency ) {
				if ( isset( $_COOKIE['mcg_ip_base_currency'] ) ) {
					$to_currency = filter_input( INPUT_COOKIE, 'mcg_ip_base_currency' );
				} else {
					$vis_ip      = self::mcg_get_vis_ip();
					$ipdetails   = @json_decode( file_get_contents( 'http://www.geoplugin.net/json.gp?ip=' . $vis_ip ) );
					$to_currency = trim( $ipdetails->geoplugin_currencyCode ); //phpcs:ignore
				}
			}
			$to_currency = $to_currency ? $to_currency : $default_currency;

			$new_amt       = Mcg_Currency::convert_currency( $default_currency, $to_currency, $amount );
			$convert_price = self::mcg_to_money( $new_amt, $to_currency );
		}

		return $convert_price;
	}

	/**
	 * Get visitor IP address
	 *
	 * @return float IP address
	 */
	public static function mcg_get_vis_ip() {
		if ( ! empty( $_SERVER['HTTP_CLIENT_IP'] ) ) {
			$ip = filter_input( INPUT_SERVER, 'HTTP_CLIENT_IP' );
		} elseif ( ! empty( $_SERVER['HTTP_X_FORWARDED_FOR'] ) ) {
			$ip = filter_input( INPUT_SERVER, 'HTTP_X_FORWARDED_FOR' );
		} else {
			$ip = filter_input( INPUT_SERVER, 'REMOTE_ADDR' );
		}
		return $ip;
	}

	/**
	 * MCG get formatted money.
	 *
	 * @param int    $number Get amount.
	 * @param string $to_currency Get convert currency code.
	 * @param bool   $do_encode Encode or not.
	 * @return string
	 */
	public static function mcg_to_money( $number, $to_currency, $do_encode = false ) {
		if ( false === $number ) {
			return '';
		}

		$negative = '';
		if ( strpos( strval( $number ), '-' ) !== false ) {
			$negative = '-';
			$number   = floatval( substr( $number, 1 ) );
		}

		$currencies = RGCurrency::get_currencies();
		$currency   = isset( $currencies[ $to_currency ] ) ? $currencies[ $to_currency ] : '';

		if ( $currency ) {
			$money = number_format( $number, $currency['decimals'], $currency['decimal_separator'], $currency['thousand_separator'] );
			if ( '0.00' == $money ) {
				$negative = '';
			}
			$symbol_left  = ! empty( $currency['symbol_left'] ) ? $currency['symbol_left'] . $currency['symbol_padding'] : '';
			$symbol_right = ! empty( $currency['symbol_right'] ) ? $currency['symbol_padding'] . $currency['symbol_right'] : '';
			if ( $do_encode ) {
				$symbol_left  = html_entity_decode( $symbol_left );
				$symbol_right = html_entity_decode( $symbol_right );
			}
			return $negative . $symbol_left . $money . $symbol_right;
		} else {
			return $to_currency . ' ' . round( $number, 2 );
		}
	}

	/**
	 * Save currency base on IP
	 *
	 * @return void
	 */
	public static function mcg_save_ip_base_currency() {
		if ( ! isset( $_COOKIE['mcg_ip_base_currency'] ) ) {
			$vis_ip      = self::mcg_get_vis_ip();
			$ipdetails   = @json_decode( file_get_contents( 'http://www.geoplugin.net/json.gp?ip=' . $vis_ip ) );
			if ( is_object( $ipdetails ) && isset( $ipdetails->geoplugin_currencyCode ) ) {
				$to_currency = trim( $ipdetails->geoplugin_currencyCode ); //phpcs:ignore

				if ( ! headers_sent() ) {
					setcookie( 'mcg_ip_base_currency', $to_currency, time() + ( 86400 * 15 ), '/' );
				}
			}
		}
	}

}
