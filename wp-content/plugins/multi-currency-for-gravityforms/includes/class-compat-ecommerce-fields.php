<?php
/**
 * Comaptibility with Gravity Forms eCommerce Fields by GravityWiz
 *
 * @link       http://ideawp.com
 * @since      1.0.0
 *
 * @package    Mcg
 * @subpackage Mcg/includes
 */

/**
 * Comaptibility with Gravity Forms eCommerce Fields
 *
 * @since      1.8.0
 * @package    Mcg
 * @subpackage Mcg/includes
 */
class Mcg_Compat_Ecom_Fields {
	/**
	 * Init.
	 */
	public static function run() {
		add_action( 'init', array( __CLASS__, 'hooks' ) );
	}

	/**
	 * Hooks.
	 *
	 * @return void
	 */
	public static function hooks() {
		if ( ! class_exists( 'GP_Ecommerce_Fields' ) ) {
			return;
		}

		add_filter(
			'gform_merge_tag_filter',
			function( $value, $merge_tag, $options, $field, $text ) {
				add_filter( 'gform_currency', array( __CLASS__, 'change_currency' ) );
				return $value;
			},
			9,
			5
		);

		add_filter(
			'gform_merge_tag_filter',
			function( $value, $merge_tag, $options, $field, $text ) {
				remove_filter( 'gform_currency', array( __CLASS__, 'change_currency' ) );
				return $value;
			},
			11,
			5
		);
	}

	/**
	 * Change global currency to user selected currency.
	 *
	 * @param string $global_currency Global currency.
	 *
	 * @return string
	 */
	public static function change_currency( $global_currency ) {
		// todo test with AJAX too.
		$form_id = filter_input( INPUT_POST, 'gform_submit' );

		if ( empty( $form_id ) ) {
			return $global_currency;
		}

		$form     = GFAPI::get_form( $form_id );
		$currency = Mcg_Currency::get_users_selected_currency( $form );

		return ! empty( $currency ) ? $currency : $global_currency;
	}
}
