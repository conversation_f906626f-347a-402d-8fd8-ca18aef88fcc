<?php
/**
 * The file that defines the plugin addon class
 *
 * @link       http://ideawp.com
 * @since      1.0.0
 *
 * @package    Mcg
 * @subpackage Mcg/includes
 */

GFForms::include_addon_framework();

/**
 * The plugin addon class.
 *
 * @since      1.0.0
 * @package    Mcg
 * @subpackage Mcg/includes
 */
class Mcg_GF_Addon extends GFAddOn {

	/**
	 * Version
	 *
	 * @var string
	 */
	protected $_version = MCG_PLUGIN_VERSION;

	/**
	 * Min GF version
	 *
	 * @var string
	 */
	protected $_min_gravityforms_version = '1.9';

	/**
	 * Slug
	 *
	 * @var string
	 */
	protected $_slug = 'multi-currency-gf';

	/**
	 * Plugin path.
	 *
	 * @var string
	 */
	protected $_path = 'multi-currency-for-gf/mcg.php';

	/**
	 * Full path.
	 *
	 * @var string
	 */
	protected $_full_path = __FILE__;

	/**
	 * Title.
	 *
	 * @var string
	 */
	protected $_title = 'Multi-currency for Gravity Form';

	/**
	 * Short title.
	 *
	 * @var string
	 */
	protected $_short_title = 'Multi-currency';

	/**
	 * Instance.
	 *
	 * @var Object
	 */
	private static $_instance = null;

	/**
	 * Get instance.
	 *
	 * @return Object self object.
	 */
	public static function get_instance() {
		if ( null == self::$_instance ) {
			self::$_instance = new Mcg_GF_Addon();
		}

		return self::$_instance;
	}

	/**
	 * Init.
	 *
	 * @return void
	 */
	public function init() {
		parent::init();
		add_action( 'gform_field_standard_settings', array( $this, 'add_custom_standard_settings' ), 10, 2 );
		add_action( 'gform_editor_js', array( $this, 'editor_script' ) );
		add_action( 'gform_tooltips', array( $this, 'add_tooltips' ) );
		add_filter( 'gform_pre_render', array( $this, 'add_form_data_for_js' ), 10, 3 );
		add_filter( 'gform_admin_pre_render', array( $this, 'add_form_data_for_js' ), 10, 3 );
	}

	/**
	 * Return the scripts which should be enqueued.
	 *
	 * @return array
	 */
	public function scripts() {
		$scripts = array(
			array(
				'handle'   => 'mcg_admin_js',
				'src'      => MCG_URL . 'admin/js/mcg-admin.js',
				'version'  => $this->_version,
				'callback' => array( 'Mcg_Admin', 'localize_scripts' ),
				'enqueue'  => array(
					array(
						'admin_page' => array( 'form_editor', 'form_settings', 'plugin_settings', 'plugin_page', 'entry_view', 'entry_detail', 'results' ),
					),
				),
			),
			array(
				'handle'   => 'mcg_public_js',
				'src'      => MCG_URL . 'assets/public/js/main.js',
				'version'  => $this->_version,
				'callback' => array( 'Mcg_Public', 'localize_scripts' ),
				'enqueue'  => array(
					array( 'field_types' => array( 'multicurrency' ) ),
				),
			),
		);
		return array_merge( parent::scripts(), $scripts );
	}

	/**
	 * Return the stylesheets which should be enqueued.
	 *
	 * @return array
	 */
	public function styles() {
		$styles = array(
			array(
				'handle'  => 'mcg_admin_style',
				'src'     => MCG_URL . 'admin/css/mcg-admin.css',
				'version' => $this->_version,
				'enqueue' => array(
					array(
						'admin_page' => array( 'form_editor', 'form_settings', 'plugin_settings', 'plugin_page', 'entry_view', 'entry_detail', 'results' ),
					),
				),
			),
			array(
				'handle'  => 'mcg_public_style',
				'src'     => MCG_URL . 'public/css/mcg-public.css',
				'version' => $this->_version,
				'enqueue' => array(
					array( 'field_types' => array( 'multicurrency' ) ),
				),
			),
		);
		return array_merge( parent::styles(), $styles );
	}

	/**
	 * Plugin settings.
	 *
	 * @return array
	 */
	public function plugin_settings_fields() {
		$description  = '<ul style="margin-bottom: 25px;background: #d6edf6;padding: 14px 10px;border-left: 3px solid;"><li>';
		$description .= '<strong style="min-width: 90px; display: inline-block;">' . esc_html__( 'Tutorial', 'idea_mcg' ) . '</strong>';
		$description .= '<a href="https://ideawp.com/docs/multi-currency-for-gravityforms/" target="_blank" style="color: #0c2b37;">' . esc_html__( 'Read Documentation', 'idea_mcg' ) . '</a>';
		$description .= '</li><li>';
		$description .= '<strong style="min-width: 90px; display: inline-block;">' . esc_html__( 'Need Help?', 'idea_mcg' ) . '</strong>';
		$description .= '<a href="https://ideawp.freshdesk.com/support/tickets/new?ticket_form=ask_a_question" target="_blank" style="color: #0c2b37;">' . esc_html__( 'Submit a Support Ticket', 'idea_mcg' ) . '</a>';
		$description .= '</li></ul>';

		/* translators: link of Open exchange rates site. */
		$field_desc = sprintf( esc_html__( '%1$sYou can get it for free from %2$shere.%3$s', 'idea_mcg' ), '<small>', '<a href="https://openexchangerates.org/signup/free" target="_BLANK">', '</a></small>' );
		return array(
			array(
				'title'       => esc_html__( 'API Settings', 'idea_mcg' ),
				'description' => $description,
				'fields'      => array(
					array(
						'name'              => 'open_exchange_app_id',
						'label'             => esc_html__( 'Open Exchange Rates API', 'idea_mcg' ),
						'description'       => $field_desc,
						'type'              => 'text',
						'class'             => 'medium',
						'feedback_callback' => array( $this, 'is_valid_api' ),
					),
				),
			),
		);
	}

	/**
	 * Form settings fields
	 *
	 * @param array $form Form.
	 *
	 * @return array $fields Fields.
	 */
	public function form_settings_fields( $form ) {
		return array(
			array(
				'title'  => esc_html__( 'Multi Currency Settings', 'idea_mcg' ),
				'fields' => array(
					array(
						'label'   => esc_html__( 'Form Base Currency', 'idea_mcg' ),
						'type'    => 'select',
						'name'    => 'base_currency',
						'tooltip' => esc_html__( 'Base currency for the form', 'idea_mcg' ),
						'class'   => 'medium',
						'choices' => $this->get_currency_choices(),
					),
					array(
						'label'   => esc_html__( 'Auto currency selection', 'idea_mcg' ),
						'type'    => 'checkbox',
						'name'    => 'auto_currency_selection',
						'choices' => array(
							array(
								'label'         => esc_html__( 'Automatically set visitor\'s currency based on their country/IP', 'idea_mcg' ),
								'name'          => 'is_geo_auto_currency',
								'tooltip'       => esc_html__( 'Select currency based on visitor\'s IP.', 'idea_mcg' ),
								'default_value' => 1,
							),
						),
					),
					array(
						'label'         => esc_html__( 'Process Payment in', 'idea_mcg' ),
						'type'          => 'select',
						'name'          => 'payment_processing_currency',
						'default_value' => 'user_currency',
						'tooltip'       => esc_html__( 'If your payment gateway doesn\'t support multi-currencies, you can choose "Form\'s base currency" option. Whatever amount the user selects in his currency will be converted into the base currency and then sent to payment gateway for the payment.', 'idea_mcg' ),
						'choices'       => array(
							array(
								'label' => esc_html__( 'User\'s selected currency.', 'idea_mcg' ),
								'value' => 'user_currency',
							),
							array(
								'label' => esc_html__( 'Form\'s base currency.', 'idea_mcg' ),
								'value' => 'forms_base_currency',
							),
						),
					),
				),
			),
		);
	}

	/**
	 * Get currency choices to be passed to fields in form_settings_fields.
	 *
	 * @return array
	 */
	public function get_currency_choices() {
		$all_currencies = RGCurrency::get_currencies();
		$choices        = array();

		foreach ( $all_currencies as $key => $currency ) {
			$choices[] = array(
				'label' => $currency['name'],
				'value' => $key,
			);
		}

		return $choices;
	}

	/**
	 * Is API key valid.
	 *
	 * @param string $value API key.
	 *
	 * @return bool
	 */
	public function is_valid_api( $value ) {
		$url           = sprintf( 'https://openexchangerates.org/api/latest.json?app_id=%s', $value );
		$response      = wp_remote_get( $url );
		$response_code = wp_remote_retrieve_response_code( $response );

		if ( is_array( $response ) && ! is_wp_error( $response ) && 200 == $response_code ) {
			return true;
		} else {
			return false;
		}
	}

	/**
	 * Custom standard settings.
	 *
	 * @param int $position Position.
	 * @param int $form_id  Form ID.
	 *
	 * @return void
	 */
	public function add_custom_standard_settings( $position, $form_id ) {
		$all_currencies = RGCurrency::get_currencies();
		if ( 25 === $position ) {
			?>
			<li class="multi_currency_exchange_mode_setting field_setting">
				<label for="field_admin_label" class="section_label">
					<?php esc_html_e( 'Multi-currency Mode', 'gravityforms' ); ?>
					<?php gform_tooltip( 'multi_currency_mode' ); ?>
				</label>

				<div>
					<input 
						type="radio" 
						id='multi_currency_mode_automatic' 
						name='multi_currency_mode' 
						size="10" 
						value='automatic' 
						onclick="SetFieldProperty('multi_currency_mode', jQuery('[name=multi_currency_mode]:checked').val() );"
						/>
					<label class='inline' for="multi_currency_mode_automatic"><?php esc_html_e( 'Automatic', 'idea_mcg' ); ?></label>

					<input 
						type="radio" 
						id='multi_currency_mode_manual' 
						name='multi_currency_mode' 
						size="10" 
						value='manual'
						onclick="SetFieldProperty('multi_currency_mode', jQuery('[name=multi_currency_mode]:checked').val() );"
						/>
					<label class='inline' for="multi_currency_mode_manual"><?php esc_html_e( 'Manual', 'idea_mcg' ); ?></label>
				</div>

				<div class="multi_currency_mode_manual_msg">
					<?php esc_html_e( 'Please set the manual prices in Product field(s).', 'idea_mcg' ); ?>
				</div>
			</li>

			<li class="multi_currency_currency_selection field_setting">
				<label for="field_admin_label" class="section_label">
					<?php esc_html_e( 'Select Currency', 'gravityforms' ); ?>
					<?php gform_tooltip( 'multi_currency_select' ); ?>
				</label>
				<div>
					<input 
						type="radio" 
						id='multi_currency_select_all' 
						name='multi_currency_select'
						size="10" 
						value='all' 
						onclick="SetFieldProperty('multi_currency_select', jQuery('[name=multi_currency_select]:checked').val() );"
						/>
					<label class='inline' for="multi_currency_select_all"> <?php esc_html_e( 'All currencies', 'idea_mcg' ); ?></label>

					<input
						type='radio'
						id='multi_currency_select_specific'
						name='multi_currency_select'
						size='10'
						value='specific'
						onclick="SetFieldProperty('multi_currency_select', jQuery('[name=multi_currency_select]:checked').val() );"
						/>
					<label class='inline' for='multi_currency_select_specific'><?php esc_html_e( 'Specific Currencies', 'idea_mcg' ); ?></label>					
				</div>
			</li>

			<li class='multi_currency_currency_list_settings field_setting'>
				<?php
				$html = '';
				foreach ( $all_currencies as $currency_key => $currency ) {
					$html .= sprintf(
						'<div>
							<input type="checkbox" class="multi_currency_currency_list" id="mcg_currency_list_%s" data-value="%s" size="10"> 
							<label class="inline multi_currency_currency_list_label" for="mcg_currency_list_%s">%s</label> 
						</div>',
						esc_html( $currency_key ),
						esc_html( $currency_key ),
						esc_html( $currency_key ),
						esc_html( $currency['name'] )
					);
				}
				echo $html; // phpcs:ignore -- already escaped
				?>
			</li>

			<?php
		} elseif ( 37 === $position ) {
			?>
			<li class="multi_currency_manual_prices_settings field_setting">
				<label for="field_admin_label" class="section_label">
					<?php esc_html_e( 'Multi-currency Manual Prices', 'gravityforms' ); ?>
					<?php gform_tooltip( 'multi_currency_manual_prices' ); ?>
				</label>
				<div>
					<?php
					$html = '';
					foreach ( $all_currencies as $currency_key => $currency ) {
						$html .= sprintf(
							'<div class="mcg_manual_currency_price_wrap mcg_manual_currency_price_wrap_%s">
								<label class="inline mcg_manual_currency_label" for="mcg_manual_currency_price_%s">%s</label> 
								<input type="text" class="mcg_manual_currency_field mcg_manual_currency_price_%s" data-value="%s" size="10" id="mcg_manual_currency_price_%s"> 
							</div>',
							esc_html( $currency_key ),
							esc_html( $currency_key ),
							esc_html( $currency['name'] ),
							esc_html( $currency_key ),
							esc_html( $currency_key ),
							esc_html( $currency_key )
						);
					}
					echo $html; // phpcs:ignore
					?>
				</div>
			</li>
			<?php
		}
	}

	/**
	 * JS code.
	 *
	 * @return void
	 */
	public function editor_script() {
		?>
		<script type='text/javascript'>
			// @todo move this to JS file

			// @todo maybe make this dynamic. Show this field only when the mode is manual.
			// Add manual prices settings for Product field.
			fieldSettings.product += ', .multi_currency_manual_prices_settings';
			fieldSettings.shipping += ', .multi_currency_manual_prices_settings';

			//adding setting to fields of type "text"
			jQuery(document).on('gform_load_field_settings', function(event, field, form){
				if( ! field.multi_currency_mode ) {
					field.multi_currency_mode = 'automatic';
				}
				jQuery( '#multi_currency_mode_' + field.multi_currency_mode ).prop( 'checked', true );

				if( ! field.multi_currency_select ) {
					field.multi_currency_select = 'all';
				}
				jQuery( '#multi_currency_select_' + field.multi_currency_select ).prop( 'checked', true );
			});
		</script>
		<?php
	}

	/**
	 * Set tooltips
	 *
	 * @param array $tooltips Tooptips.
	 *
	 * @return array
	 */
	public function add_tooltips( $tooltips ) {
		$tooltips['form_field_base_price'] = '<h6>' . esc_html__( 'Base Price', 'idea_mcg' ) . '</h6>';
		/* translators: |n is used for line break */
		$tooltips['form_field_base_price'] .= esc_html__( 'Enter the base price for this product. |n|n Multi-Currency: In case of Automatic mode, you must enter price in the base currency of form. |n|n For manual mode, you can ignore this field and set prices in the "Multi-currency Manual Prices" section above', 'idea_mcg' );
		$tooltips['form_field_base_price']  = str_replace( '|n', '<br>', $tooltips['form_field_base_price'] );

		/* translators: |n is used for line break */
		$tooltips['multi_currency_mode']  = '<h6>' . esc_html__( 'Multicurrency Mode', 'idea_mcg' ) . '</h6>';
		$tooltips['multi_currency_mode'] .= esc_html__( 'Automatic: Automatic realtime currency exchange rate by openexchangerate.org API.|n|n Manual: You can set the prices for each currency manually in product field.', 'idea_mcg' );
		$tooltips['multi_currency_mode']  = str_replace( '|n', '<br>', $tooltips['multi_currency_mode'] );

		$tooltips['multi_currency_manual_prices']  = '<h6>' . esc_html__( 'Multicurrency Manual Prices', 'idea_mcg' ) . '</h6> ';
		$tooltips['multi_currency_manual_prices'] .= esc_html__( 'Set price of the product in each currency.', 'idea_mcg' );

		return $tooltips;
	}

	/**
	 * Add form data to be used in JS.
	 *
	 * @param array $form         GF Form.
	 * @param bool  $ajax         AJAX.
	 * @param array $field_values Values.
	 *
	 * @return void
	 */
	public function add_form_data_for_js( $form, $ajax = false, $field_values = false ) {
		global $mcg;
		// phpcs:ignore
		if ( strpos( $_SERVER['REQUEST_URI'], '/wp-json/' ) !== false || is_admin() ) { 
			// If form is loaded in Gutenberg or backend then don't send JS data.
			return $form;
		}

		$form_data = array(
			'form_id' => $form['id'],
		);

		$has_multi_currancy = false;
		$has_product_field  = false;
		$has_shipping_field = false;
		$mode               = '';
		$product_fields     = array();
		$manual_prices      = array();
		$shipping_fields    = array();
		$shipping_prices    = array();

		foreach ( $form['fields'] as $field ) {
			if ( 'multicurrency' === $field->type ) {
				$has_multi_currancy = true;
				$mode               = isset( $field->multi_currency_mode ) ? $field->multi_currency_mode : 'automatic';
			}

			if ( 'product' === $field->type ) {
				$product_fields[]  = $field;
				$has_product_field = true;
			}

			if ( 'shipping' === $field->type ) {
				$shipping_fields[]  = $field;
				$has_shipping_field = true;
			}
		}

		if ( ! empty( $product_fields ) ) {
			$manual_prices = $this->determine_manual_prices( $product_fields );
		}

		if ( ! empty( $shipping_fields ) ) {
			$shipping_prices = $this->determine_manual_prices( $shipping_fields );
			// Get the first element of array as there can be only one shipping field.
			$shipping_prices = count( $shipping_prices ) ? array_values( $shipping_prices )[0] : array();
		}

		$form_data['has_multi_currancy']          = $has_multi_currancy;
		$form_data['has_product_field']           = $has_product_field;
		$form_data['mode']                        = $mode;
		$form_data['manual_prices']               = apply_filters( 'mcg_manual_prices', $manual_prices, $form );
		$form_data['manual_shipping']             = apply_filters( 'mcg_manual_shipping', $shipping_prices, $form );
		$form_data['base_currency']               = $mcg->addon->get_form_base_currency( $form );
		$form_data['is_geo_auto_currency']        = $mcg->addon->get_form_is_geo_auto_currency( $form );
		$form_data['shipping_cost']               = $mcg->addon->get_shipping_amount( $form );
		$form_data['payment_processing_currency'] = $mcg->addon->get_form_payment_processing_currency( $form );

		echo sprintf(
			'<script>
				var mcg_form_%d = %s
			</script>
			',
			esc_html( $form['id'] ),
			wp_json_encode( $form_data )
		);

		return $form;
	}

	/**
	 * Determine the manual prices for each field.
	 *
	 * @param array $product_fields Array of Gravity form fields.
	 *
	 * @return array
	 */
	public function determine_manual_prices( $product_fields ) {
		$currencies = array();

		foreach ( $product_fields as $product_field ) {
			$currencies[ $product_field->id ] = array();
			foreach ( $product_field as $key => $value ) {
				if ( false !== strpos( $key, 'multi_currency_manual_price_' ) ) {
					$currency                                      = str_replace( 'multi_currency_manual_price_', '', $key );
					$currencies[ $product_field->id ][ $currency ] = $value;
				}
			}
		}

		return $currencies;
	}

	/**
	 * Get base currency for the form.
	 *
	 * @param array $form GF Form.
	 *
	 * @return string
	 */
	public function get_form_base_currency( $form ) {
		$setting          = $this->get_form_settings( $form );
		$currency_option  = get_option( 'rg_gforms_currency' );
		$default_currency = ! empty( $currency_option ) ? $currency_option : 'USD';

		// Cannot call GFCommon::get_currency() here as it causes an infinite loop through the `gform_currency` filter.
		return isset( $setting['base_currency'] ) ? $setting['base_currency'] : $default_currency;
	}

	/**
	 * Get auto currency selection option for the form.
	 *
	 * @param array $form GF Form.
	 *
	 * @return string
	 */
	public function get_form_is_geo_auto_currency( $form ) {
		$setting = $this->get_form_settings( $form );

		return isset( $setting['is_geo_auto_currency'] ) ? $setting['is_geo_auto_currency'] : '1';
	}

	/**
	 * Get payment processing currency for the form.
	 *
	 * @param array $form Gravity form's form object.
	 *
	 * @return string.
	 */
	public function get_form_payment_processing_currency( $form ) {
		$setting = $this->get_form_settings( $form );

		return isset( $setting['payment_processing_currency'] ) ? $setting['payment_processing_currency'] : 'user_currency';

	}

	/**
	 * Get shipping amount from the form.
	 *
	 * @param array $form GravityForms' Form object.
	 *
	 * @return int|bool
	 */
	public static function get_shipping_amount( $form ) {
		global $mcg;

		foreach ( $form['fields'] as $field ) {
			if ( 'shipping' === $field->type ) {
				$currency     = $mcg->addon->get_form_base_currency( $form );
				$shipping_amt = GFCommon::to_number( $field->basePrice, $currency );
				return $shipping_amt;
			}
		}

		return 0;
	}

	/**
	 * Return the plugin's icon for the plugin/form settings menu.
	 *
	 * @since 2.5
	 *
	 * @return string
	 */
	public function get_menu_icon() {
		return 'gform-icon--dollar';
	}
}
