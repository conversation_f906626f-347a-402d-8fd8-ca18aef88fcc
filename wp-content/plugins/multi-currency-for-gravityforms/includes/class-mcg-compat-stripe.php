<?php
/**
 * Plugin Core
 *
 * @link       http://ideawp.com
 * @since      1.0.0
 *
 * @package    Mcg
 * @subpackage Mcg/includes
 */

/**
 * Core Class
 *
 * @since      1.0.0
 * @package    Mcg
 * @subpackage Mcg/includes
 */
class Mcg_Compat_Stripe {
	/**
	 * Run.
	 *
	 * @return void
	 */
	public static function run() {
		add_filter( 'gform_stripe_charge_pre_create', array( __CLASS__, 'change_currency_for_stripe' ), 10, 5 );
	}

	/**
	 * Change currency for stripe.
	 *
	 * @param array $charge          Charge.
	 * @param array $feed            Feed.
	 * @param array $submission_data Submission Data.
	 * @param array $form            Form.
	 * @param array $entry           Entry.
	 *
	 * @return $charge Updated charge
	 */
	public static function change_currency_for_stripe( $charge, $feed, $submission_data, $form, $entry ) {
		$action = filter_input( INPUT_POST, 'action' );
		if ( empty( $action ) || 'gfstripe_validate_form' !== $action ) {
			return $charge;
		}

		// get the form id.
		$form_id = filter_input( INPUT_POST, 'form_id' );
		if ( empty( $form_id ) ) {
			return $charge;
		}

		// get the form.
		$form = GFAPI::get_form( $form_id );
		if ( empty( $form ) ) {
			return $charge;
		}

		$user_currency      = Mcg_Currency::get_users_selected_currency( $form );
		$charge['currency'] = $user_currency;

		return $charge;
	}
}
