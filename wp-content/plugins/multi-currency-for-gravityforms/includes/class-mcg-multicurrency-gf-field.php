<?php
/**
 * Create MCG currency field.
 *
 * @link       http://ideawp.com
 * @since      1.0.0
 *
 * @package    Mcg
 * @subpackage Mcg/includes
 */

if ( ! class_exists( 'GFForms' ) ) {
	die();
}

/**
 * Multicurrency field class.
 */
class Mcg_MultiCurrency_GF_Field extends GF_Field {

	/**
	 * Constructor
	 *
	 * @param array $data Data.
	 */
	public function __construct( $data = array() ) {
		parent::__construct( $data );
	}

	/**
	 * Type of field.
	 *
	 * @var string
	 */
	public $type = 'multicurrency';

	/**
	 * Return the field title, for use in the form editor.
	 *
	 * @return string
	 */
	public function get_form_editor_field_title() {
		return esc_attr__( 'Currency', 'idea_mcg' );
	}

	/**
	 * Assign the field button to the Advanced Fields group.
	 *
	 * @return array
	 */
	public function get_form_editor_button() {
		return array(
			'group' => 'pricing_fields',
			'text'  => __( 'Currency Selector', 'idea_mcg' ),
		);
	}

	/**
	 * The settings which should be available on the field in the form editor.
	 *
	 * @return array
	 */
	public function get_form_editor_field_settings() {
		return array(
			'label_setting',
			'description_setting',
			'rules_setting',
			'placeholder_setting',
			'input_class_setting',
			'css_class_setting',
			'size_setting',
			'admin_label_setting',
			'default_value_setting',
			'visibility_setting',
			'conditional_logic_field_setting',
			'prepopulate_field_setting',
			'multi_currency_exchange_mode_setting',
			'multi_currency_currency_selection',
			'multi_currency_currency_list_settings',
		);
	}

	/**
	 * Enable this field for use with conditional logic.
	 *
	 * @return bool
	 */
	public function is_conditional_logic_supported() {
		return false;
	}

	/**
	 * The scripts to be included in the form editor.
	 *
	 * @return string
	 */
	public function get_form_editor_inline_script_on_page_render() {
		// @todo check if this is needed?
		// Set the default field label for the simple type field.
		$script = sprintf( "function SetDefaultValues_multicurrency(field) { field.label = '%s';}", $this->get_form_editor_field_title() ) . PHP_EOL;

		// Initialize the fields custom settings.
		$script .= "jQuery(document).bind('gform_load_field_settings', function (event, field, form) {
		           var inputClass = field.inputClass == undefined ? '' : field.inputClass;
		           jQuery('#input_class_setting').val(inputClass);
		           });" . PHP_EOL;

		// Saving the simple setting.
		$script .= "function SetInputClassSetting(value) {SetFieldProperty('inputClass', value);}" . PHP_EOL;

		return $script;
	}

	/**
	 * Define the fields inner markup.
	 *
	 * @param array        $form  The Form Object currently being processed.
	 * @param string|array $value The field value. From default/dynamic population, $_POST, or a resumed incomplete submission.
	 * @param null|array   $entry Null or the Entry Object currently being edited.
	 *
	 * @return string
	 */
	public function get_field_input( $form, $value = '', $entry = null ) {
		$id              = absint( $this->id );
		$form_id         = absint( $form['id'] );
		$is_entry_detail = $this->is_entry_detail();
		$is_form_editor  = $this->is_form_editor();

		// Prepare the value of the input ID attribute.
		$field_id = $is_entry_detail || $is_form_editor || 0 === $form_id ? "input_$id" : 'input_' . $form_id . "_$id";

		$currency = esc_attr( $value );
		// Get the value of the input_class property for the current field.
		$input_class = $this->inputClass;

		// Prepare the input classes.
		$size         = $this->size;
		$class_suffix = $is_entry_detail ? '_admin' : '';
		$class        = $size . $class_suffix . ' ' . $input_class;

		// Prepare the other input attributes.
		$tabindex              = $this->get_tabindex();
		$placeholder_attribute = $this->get_field_placeholder_attribute();
		$required_attribute    = $this->isRequired ? 'aria-required="true"' : '';
		$invalid_attribute     = $this->failed_validation ? 'aria-invalid="true"' : 'aria-invalid="false"';
		$disabled_text         = $is_form_editor ? 'disabled="disabled"' : '';

		$options = $this->get_options_html( $form, $currency );
		$resumed = ! empty( $currency ) ? 'data-resumed' : '';

		// Prepare the input tag for this field.
		$input = "<select name='input_{$id}' id='{$id}' value='{$currency}' class='{$class}' {$tabindex} {$placeholder_attribute} {$required_attribute} {$invalid_attribute} {$disabled_text} {$resumed}> {$options} </select>";
		return sprintf( "<div class='ginput_container ginput_container_%s'>%s</div>", $this->type, $input );
	}

	/**
	 * Get options HTML.
	 *
	 * @param array  $form  Form.
	 * @param string $value Selected Value.
	 *
	 * @return string
	 */
	public function get_options_html( $form, $value ) {
		global $mcg;
		$currency_field = Mcg_Currency::form_get_mcg_field( $form );

		if ( ! $currency_field ) {
			return '';
		}

		$global_currency = GFCommon::get_currency();
		$all_currencies  = RGCurrency::get_currencies();
		$form_currency   = $mcg->addon->get_form_base_currency( $form );
		$currency_value  = '';
		$options         = '';
		$selection_mode  = $currency_field->multi_currency_select;

		if ( $value ) {
			$currency_value = $value;
		} elseif ( $form_currency ) {
			$currency_value = $form_currency;
		} else {
			$currency_value = $global_currency;
		}

		$currency_list = (array) $currency_field->multi_currency_currency_list;

		foreach ( $all_currencies as $key => $currency ) {
			if ( 'specific' === $selection_mode && ! in_array( $key, $currency_list, true ) ) {
				continue;
			}

			$options .= sprintf( "<option value='%s' %s >%s</option>", $key, selected( $currency_value, $key, false ), $currency['name'] );
		}
		return $options;
	}

}
