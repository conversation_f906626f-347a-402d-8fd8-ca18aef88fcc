<?php
/**
 * Manage currency field data
 *
 * @link       http://ideawp.com
 * @since      1.0.0
 *
 * @package    Mcg
 * @subpackage Mcg/includes
 */

/**
 * All currency conversion related logics and functions. This is the core class where multi-currency magic happens.
 *
 * # How does it all work?
 *
 * Admin:
 * - When form is saved product prices are saved in "Global currency".
 *
 * Frontend:
 * - when form is visited, on `gform_pre_render` hook, we change the product prices back to form's base currency.
 * - When form is submitted then we change all product prices into global currency in JS.
 *
 * @since      1.0.0
 * @package    Mcg
 * @subpackage Mcg/includes
 */
class Mcg_Currency {

	/**
	 * In GF there is no way to get the current form which is being printed,
	 * so in `gform_pre_render` we save the form data in this variable.
	 *
	 * @var array.
	 */
	public static $current_form;

	/**
	 * Initialize.
	 *
	 * @return void
	 */
	public static function run() {
		add_filter( 'gform_currencies', array( __CLASS__, 'add_missing_currencies' ) );

		add_filter( 'gform_pre_render', array( __CLASS__, 'set_current_form' ), 11, 3 );
		add_filter( 'gform_disable_form_settings_sanitization', array( __CLASS__, 'set_current_form_while_saving' ), 10, 1 );
		add_filter( 'gform_currency', array( __CLASS__, 'set_global_currency_for_form' ) );
		add_filter( 'gform_currency_pre_save_entry', array( __CLASS__, 'maybe_change_form_currency' ), 10, 2 );
		add_filter( 'gform_field_content', array( __CLASS__, 'modify_field_html_add_flatten_price_to_product_field' ), 10, 5 );

		add_filter( 'gform_merge_tag_filter', array( __CLASS__, 'change_currency_in_mergetags' ), 10, 6 );

		// Change products values before payment.
		add_filter( 'gform_product_info', array( __CLASS__, 'change_product_field' ), 10, 3 );

		// Fix checksum (state validation issue).
		add_filter( 'gform_field_validation', array( __CLASS__, 'mcg_override_state_validation' ), 10, 5 );

		// When form fields are saved in the admin.
		add_filter( 'gform_form_update_meta', array( __CLASS__, 'admin_save_product_prices_in_global_currency_format' ), 10, 3 );
	}

	/**
	 * Change Product fields when form is submitted.
	 *
	 * @param array $product_info Product Info.
	 * @param array $form         GF Form object.
	 * @param array $lead         Lead object.
	 *
	 * @return array $product_info Updated product Info.
	 */
	public static function change_product_field( $product_info, $form, $lead ) {
		global $mcg;

		$mcg_field        = self::form_get_mcg_field( $form );
		$base_currency    = $mcg->addon->get_form_base_currency( $form );
		$payment_currency = $mcg->addon->get_form_payment_processing_currency( $form );

		if ( empty( $base_currency ) || empty( $mcg_field ) ) {
			return $product_info;
		}

		$mode = self::get_multi_currency_mode( $form );

		/*
		If 'Process Payment Currency' setting is 'Forms base currency' then we don't need
		to do any currency conversion, only format conversion. Because the default amount is already in base currency.
		This applies only for automatic conversion mode. For manual mode we still need to
		change the amount.
		*/
		if ( 'forms_base_currency' === $payment_currency && 'automatic' === $mode ) {
			return self::change_product_info_automatic_form_base_currency( $product_info, $form, $lead );
		}

		/**
		 * Handle calculation field.
		 */
		$calculation_field_ids = self::form_has_calculation_field( $form );
		if ( 'automatic' === $mode && ! empty( $calculation_field_ids ) ) {
			foreach ( $product_info['products'] as $product_field_id => $product ) {
				if ( ! in_array( $product_field_id, $calculation_field_ids, true ) ) {
					continue;
				}

				$product_info['products'][ $product_field_id ]['price'] = filter_input( INPUT_POST, "input_{$product_field_id}_2" );
			}
		}

		// Todo remove. This is handled in JS now, this isn't needed anymore.
		/* if ( 'manual' === $mode ) {
			$product_info = self::change_product_info_manual( $product_info, $form, $lead );
		} elseif ( 'automatic' === $mode ) {
			$product_info = self::change_product_info_automatic( $product_info, $form, $lead );
		} */

		return $product_info;
	}


	/**
	 * Set current form in self::$current_form. It will be used later to change the form's currency on the frontend.
	 *
	 * @param form $form Form.
	 * @param bool $ajax Ajax.
	 * @param bool $field_values Field values.
	 *
	 * @return $form
	 */
	public static function set_current_form( $form, $ajax = false, $field_values = false ) {
		if ( empty( $form ) || empty( $form['id'] ) ) {
			return;
		}

		self::$current_form = $form;

		return $form;
	}

	/**
	 * Set value of self::$current_form when form is saved in admin.
	 *
	 * We do not have any intention of changing the sanitization behavior of GF.
	 * We just need to get the form data when form is saved, so we update the
	 * self::$current_form variable which would be used later to change the global
	 * currency.
	 *
	 * If we dont do this then GF_Field::sanitize_settings_choices() function will use the global
	 * currency object while sanitization and will mess up the data of larger numbers.
	 * Example 11.600,00 € will be changed in to $11.6.
	 *
	 * @param bool $disable_form_settings_sanitization Disable form settings sanitization.
	 *
	 * @return bool
	 */
	public static function set_current_form_while_saving( $disable_form_settings_sanitization ) {
		$gf_ajax = wp_doing_ajax() && ( isset( $_POST['action'] ) && 'form_editor_save_form' === $_POST['action'] );

		// If this is not a form save request then return.
		$gform_export = filter_input( INPUT_POST, 'gform_export', FILTER_VALIDATE_BOOLEAN );
		if ( ! $gf_ajax && $gform_export ) {
			return $disable_form_settings_sanitization;
		}

		$gform_meta = $gf_ajax ? rgpost( 'data' ) : rgpost( 'gform_meta' );
		if ( empty( $gform_meta ) ) {
			return $disable_form_settings_sanitization;
		}

		$gform_meta = stripslashes( $gform_meta );
		$gform_meta = nl2br( $gform_meta );
		$gform_meta = json_decode( $gform_meta, true );

		// This is not exactly the form object but very close and could do the trick.
		self::$current_form = $gform_meta;

		return $disable_form_settings_sanitization;
	}


	/**
	 * Change currency of the total field in the merge tags.
	 *
	 * @param value    $value     Value.
	 * @param input_id $input_id  Input ID.
	 * @param string   $modifier  Modifier.
	 * @param GFFiend  $field     Field.
	 * @param string   $raw_value Raw Value.
	 * @param string   $format    Format.
	 *
	 * @return void
	 */
	public static function change_currency_in_mergetags( $value, $input_id, $modifier, $field, $raw_value, $format ) {
		// if field is not a total or product field then return.
		if ( ! in_array( $field->type, array( 'total' ), true ) ) {
			return $value;
		}

		$input_value = filter_input( INPUT_POST, 'input_' . $field->id );
		if ( ! empty( $input_value ) ) {
			return $input_value;
		}

		// todo maybe all the code below this is not usedful anymore.
		$form_id   = $field->formId;
		$form      = GFAPI::get_form( $form_id );
		$mcg_field = self::form_get_mcg_field( $form );

		// If this is not a multi currency form then return.
		if ( empty( $mcg_field ) ) {
			return $value;
		}

		global $mcg;

		// $value is in global currency, convert it to form's base currency.
		$global_currency = GFCommon::get_currency();
		$base_currency   = $mcg->addon->get_form_base_currency( $form );
		$new_curr        = self::convert_currency_format( $value, $global_currency, $base_currency );

		return $new_curr;
	}

	/**
	 * Set global currency based on form's base currency.
	 *
	 * @param string $global_currency Global Currency.
	 *
	 * @return string.
	 */
	public static function set_global_currency_for_form( $global_currency ) {
		if ( empty( self::$current_form ) ) {
			return $global_currency;
		}

		global $mcg;

		$base_currency = $mcg->addon->get_form_base_currency( self::$current_form );

		if ( empty( $base_currency ) ) {
			return $global_currency;
		}

		return $base_currency;
	}

	/**
	 * Change product info for automatic mode and when payement need to be proccessed in base currency.
	 *
	 * @param array $product_info Product Info.
	 * @param array $form Form.
	 * @param array $lead Lead data.
	 *
	 * @return array
	 */
	public static function change_product_info_automatic_form_base_currency( $product_info, $form, $lead ) {
		global $mcg;

		$mcg_field           = self::form_get_mcg_field( $form );
		$global_currency     = GFCommon::get_currency();
		$field_id            = $mcg_field['id'];
		$user_currency       = $lead[ $field_id ];
		$forms_base_currency = $mcg->addon->get_form_base_currency( $form );

		foreach ( $product_info['products'] as &$product ) {
			if ( empty( $product['price'] ) ) {
				continue;
			}

			$currency         = new RGCurrency( $user_currency );
			$number           = $currency->to_number( $product['price'] );
			$product['price'] = self::convert_currency( $user_currency, $forms_base_currency, $number );
		}

		if ( ! empty( $product_info['shipping']['price'] ) ) {
			$currency                          = new RGCurrency( $user_currency );
			$number                            = $currency->to_number( $product_info['shipping']['price'] );
			$product_info['shipping']['price'] = self::convert_currency( $user_currency, $forms_base_currency, $number );
		}

		return $product_info;
	}

	/**
	 * Change product info for manual mode.
	 *
	 * @param array $product_info  Product Info.
	 * @param array $form          GF form.
	 * @param array $lead          Lead array.
	 *
	 * @return array
	 */
	public static function change_product_info_manual( $product_info, $form, $lead ) {
		return $product_info;
		global $mcg;
		$mcg_field          = self::form_get_mcg_field( $form );
		$global_currency    = GFCommon::get_currency();
		$field_id           = $mcg_field['id'];
		$new_currency       = $lead[ $field_id ];
		$old_currency       = $mcg->addon->get_form_base_currency( $form );
		$payment_currency   = $mcg->addon->get_form_payment_processing_currency( $form );
		$active_price_field = self::get_active_price_field( $form, $lead );
		$currency           = new RGCurrency( $global_currency );

		// This check ensures that there is an active price field.
		if ( empty( $active_price_field ) ) {
			return $product_info;
		}

		$user_define_price = array();
		foreach ( $form['fields'] as $field ) {
			if ( ( 'product' == $field['type'] ) && ( 'price' == $field['inputType'] ) ) {
				$user_define_price[] = $field['id'];
			}
		}
		$total = 0;

		// @todo are we considering quantity here?
		foreach ( $product_info['products']  as $field_id => &$product ) {
			$field = self::get_form_field_by_id( $field_id, $form );

			if ( empty( $field ) ) {
				continue;
			}

			if ( in_array( $field_id, $user_define_price ) ) {
				$set_price        = $currency->to_number( $product['price'] );
				$product['price'] = $set_price;

				$total += $set_price;
			} elseif ( ! isset( $product['isCoupon'] ) ) {
				$new_amt = $field[ 'multi_currency_manual_price_' . $new_currency ];

				if ( empty( $new_amt ) ) {
					continue;
				}
				$product['price'] = $new_amt;

				$total += $new_amt;
			}
		}

		if ( isset( $product_info['shipping']['id'] ) ) {
			$shipping_field = self::get_form_field_by_id( $product_info['shipping']['id'], $form );
			$amount         = $shipping_field[ 'multi_currency_manual_price_' . $new_currency ];

			$product_info['shipping']['price'] = $amount;
		}

		if ( class_exists( 'GFCoupons' ) ) {
			$gfcoupon     = GFCoupons::get_instance();
			$coupon_codes = $gfcoupon->get_submitted_coupon_codes( $form, $lead );
			if ( $coupon_codes ) {
				$coupons   = $gfcoupon->get_coupons_by_codes( $coupon_codes, $form );
				$discounts = $gfcoupon->get_discounts( $coupons, $total, $discount_total, $lead );

				foreach ( $coupons as $coupon ) {
					$coupon_price = $currency->to_number( $discounts[ $coupon['code'] ]['discount'] );
					if ( 'flat' == $discounts[ $coupon['code'] ]['type'] ) {
						$new_coupon_price = self::convert_currency( $old_currency, $new_currency, $coupon_price );
						$new_coupon_price = round( $new_coupon_price, 2 );

						$product_info['products'][ $coupon['code'] ]['price'] = - $new_coupon_price;
					} else {
						$product_info['products'][ $coupon['code'] ]['price'] = - $coupon_price;
					}
				}
			}
		}

		/*
		If form's Payment processing currency is form's base currency then we need to switch the
		amount back to the base currency.
		*/
		if ( 'forms_base_currency' === $payment_currency ) {
			foreach ( $product_info['products']  as &$product ) {
				$product['price'] = self::convert_currency( $new_currency, $old_currency, $product['price'] );
			}
		}

		$product_info = apply_filters( 'mcg_product_info_manual_mode', $product_info, $new_currency, $form, $lead );

		return $product_info;
	}

	/**
	 * Get active price field.
	 *
	 * @param array $form Get form data.
	 * @param array $lead User submission data.
	 * @return array
	 */
	public static function get_active_price_field( $form, $lead ) {
		$product_field_ids = array();
		$non_empty_fields  = array();

		// Get all product fields.
		foreach ( $form['fields'] as $field ) {
			if ( 'product' === $field->type ) {
				$product_field_ids[] = $field->id;
			}
		}

		// Get all numeric & non-emtpy fields.
		foreach ( $lead  as $key => $value ) {
			$exploded_id   = explode( '.', $key );
			$id_first_part = $exploded_id[0];

			if ( is_numeric( $id_first_part ) && ! empty( $value ) ) {
				$non_empty_fields[] = $id_first_part;
			}
		}

		$non_empty_fields = array_unique( $non_empty_fields );

		// The common fields between non-empty and product is the result.
		$active_product_ids = array_intersect( $non_empty_fields, $product_field_ids );

		// Get objects from IDs.
		$result = array();
		foreach ( $form['fields'] as $field ) {
			if ( in_array( $field->id, $active_product_ids ) ) {
				$result[] = $field;
			}
		}

		return ( is_array( $result ) && ! empty( $result ) ) ? $result[0] : false;
	}

	/**
	 * Change product info Automatic mode.
	 *
	 * @param array $product_info Product Info.
	 * @param array $form         GF Form object.
	 * @param array $lead         Lead object.
	 *
	 * @return array
	 */
	public static function change_product_info_automatic( $product_info, $form, $lead ) {
		global $mcg;

		return $product_info;

		$mcg_field       = self::form_get_mcg_field( $form );
		$global_currency = GFCommon::get_currency();
		$base_currency   = $mcg->addon->get_form_base_currency( $form );
		$field_id        = $mcg_field['id'];
		$user_currency   = $lead[ $field_id ];
		$currency        = new RGCurrency( $user_currency );

		// create array of user defined prices.
		$user_define_price = array();
		foreach ( $form['fields'] as $field ) {
			if ( ( 'product' === $field['type'] ) && ( 'price' === $field['inputType'] ) ) {
				$user_define_price[] = $field['id'];
			}
		}

		$total = 0;
		foreach ( $product_info['products']  as $key => &$product ) {
			if ( in_array( $key, $user_define_price ) ) {
				$set_price        = $currency->to_number( $product['price'] );
				$product['price'] = $set_price;

				$total += $set_price;
			} elseif ( ! isset( $product['isCoupon'] ) ) {
				$number = $currency->to_number( $product['price'] );

				if ( ! $user_currency ) {
					continue;
				}

				$new_amt = self::convert_currency( $old_currency, $user_currency, $number );
				$new_amt = round( $new_amt, 2 );

				$product['price'] = $new_amt;

				$total += $new_amt;
			}
		}

		if ( $product_info['shipping']['price'] ) {
			$shipping_price = $product_info['shipping']['price'];
			$number         = $currency->to_number( $product['price'] );
			$new_amt        = self::convert_currency( $old_currency, $user_currency, $shipping_price );
			$new_amt        = round( $new_amt, 2 );

			$product_info['shipping']['price'] = $new_amt;
		}

		if ( class_exists( 'GFCoupons' ) ) {
			$gfcoupon     = GFCoupons::get_instance();
			$coupon_codes = $gfcoupon->get_submitted_coupon_codes( $form, $lead );
			if ( $coupon_codes ) {
				$coupons   = $gfcoupon->get_coupons_by_codes( $coupon_codes, $form );
				$discounts = $gfcoupon->get_discounts( $coupons, $total, $discount_total, $lead );

				foreach ( $coupons as $coupon ) {
					$coupon_price = $currency->to_number( $discounts[ $coupon['code'] ]['discount'] );
					if ( 'flat' === $discounts[ $coupon['code'] ]['type'] ) {
						$new_coupon_price = self::convert_currency( $old_currency, $user_currency, $coupon_price );
						$new_coupon_price = round( $new_coupon_price, 2 );

						$product_info['products'][ $coupon['code'] ]['price'] = - $new_coupon_price;
					} else {
						$product_info['products'][ $coupon['code'] ]['price'] = - $coupon_price;
					}
				}
			}
		}

		return $product_info;
	}

	/**
	 * Change Form Currency.
	 *
	 * @param string $old_currency Old currency.
	 * @param array  $form         GF Form object.
	 *
	 * @return string
	 */
	public static function maybe_change_form_currency( $old_currency, $form ) {
		global $mcg;

		$mcg_field           = self::form_get_mcg_field( $form );
		$processing_currency = $mcg->addon->get_form_payment_processing_currency( $form );
		$form_base_currency  = $mcg->addon->get_form_base_currency( $form );

		if ( empty( $mcg_field ) ) {
			return $old_currency;
		}

		// If the payment processing currency is "form's base currency" then return form's base currency.
		if ( 'forms_base_currency' === $processing_currency ) {
			return $form_base_currency;
		}

		$field_id  = $mcg_field['id'];
		$input_key = sprintf( 'input_%s', $field_id );
		$currency  = isset( $_POST[$input_key] ) ? $_POST[$input_key] : $old_currency;

		return $currency ? $currency : $old_currency;
	}

	/**
	 * Fetch multicurrency field from form.
	 *
	 * @param array $form Form.
	 *
	 * @return array|false
	 */
	public static function form_get_mcg_field( $form ) {
		if ( empty( $form['fields'] ) ) {
			return false;
		}

		foreach ( $form['fields'] as $field ) {
			if ( 'multicurrency' === $field['type'] ) {
				return $field;
			}
		}

		return false;
	}


	/**
	 * Add missing currencies.
	 *
	 * @param array $currencies Currencies List.
	 *
	 * @return array $currencies Updated Currencies List.
	 */
	public static function add_missing_currencies( $currencies ) {
		$currencies['INR'] = array(
			'name'               => __( 'Indian Rupee', 'gravityforms' ),
			'symbol_left'        => '₹',
			'code'               => 'INR',
			'symbol_right'       => '',
			'symbol_padding'     => ' ',
			'thousand_separator' => ',',
			'decimal_separator'  => '.',
			'decimals'           => 2,
		);

		return $currencies;
	}

	/**
	 * Fetch Currency for given form ID.
	 *
	 * @return array|bool Currency exchange data.
	 */
	public static function get_currency_exchange_rate() {
		global $mcg;

		$api = $mcg->addon->get_plugin_setting( 'open_exchange_app_id' );

		if ( ! $api ) {
			return false;
		}

		$exchange_rate_cache = get_transient( 'mcg_currency_exchange_rate' );

		if ( ! empty( $exchange_rate_cache ) ) {
			return $exchange_rate_cache;
		}

		$url           = sprintf( 'https://openexchangerates.org/api/latest.json?app_id=%s', $api );
		$response      = wp_remote_get( $url );
		$response_code = wp_remote_retrieve_response_code( $response );

		if ( is_array( $response ) && ! is_wp_error( $response ) && 200 == $response_code ) {
			$rates      = $response['body'];
			$rates      = json_decode( $rates, true );
			$expiration = apply_filters( 'mcg_curency_exchange_cache_interval', 12 * HOUR_IN_SECONDS );
			set_transient( 'mcg_currency_exchange_rate', $rates, $expiration );

			return $rates;
		}
	}

	/**
	 * To be run on `gform_entry_post_save` action.
	 * Not being used currently but kept for future reference.
	 *
	 * @param object $lead Lead.
	 * @param array  $form  Form.
	 *
	 * @return object
	 */
	public static function maybe_change_lead_amount( $lead, $form ) {
		global $mcg;
		// Get total field.
		$total_field    = self::get_total_field( $form, $lead );
		$total_field_id = $total_field->id;
		$total_value    = $lead[ $total_field_id ];

		// Convert total to the new currency.
		$mcg_field          = self::form_get_mcg_field( $form );
		$new_currency       = $lead[ $mcg_field['id'] ];
		$form_base_currency = $mcg->addon->get_form_base_currency( $form );
		$new_amt            = self::convert_currency( $form_base_currency, $new_currency, $total_value );
		$new_amt            = round( $new_amt, 2 );

		$lead['payment_amount']  = $new_amt;
		$lead[ $total_field_id ] = $new_amt;

		return $lead;
	}

	/**
	 * Get multi currency mode.
	 *
	 * @param array $form Gravityforms form object.
	 *
	 * @return string|null
	 */
	public static function get_multi_currency_mode( $form ) {
		$mcg_field = self::form_get_mcg_field( $form );
		return isset( $mcg_field->multi_currency_mode ) ? $mcg_field->multi_currency_mode : 'automatic';
	}

	/**
	 * Get the "Total" field from the form.
	 *
	 * @param array $form Form.
	 *
	 * @return Object|false
	 */
	public static function get_total_field( $form ) {
		$total_field_id = false;

		foreach ( $form['fields'] as $field ) {
			if ( 'total' === $field->type ) {
				return $field;
			}
		}
		return false;
	}

	/**
	 * Get field by ID.
	 *
	 * @param int   $field_id Field ID.
	 * @param array $form     Form.
	 *
	 * @return array|false
	 */
	public static function get_form_field_by_id( $field_id, $form ) {
		$field_id = intval( $field_id );
		foreach ( $form['fields'] as $field ) {
			if ( $field_id === $field['id'] ) {
				return $field;
			}
		}
		return false;
	}

	/**
	 * Convert Currency.
	 *
	 * @param string $from_currency From currency.
	 * @param string $to_currency   To currency.
	 * @param float  $total         Amount to be converted.
	 *
	 * @return float
	 */
	public static function convert_currency( $from_currency, $to_currency, $total ) {
		$rates = self::get_currency_exchange_rate();

		if ( empty( $rates ) ) {
			return $from_currency;
		}

		if ( 'USD' === $to_currency ) {
			return floatval( $total / $rates['rates'][ $from_currency ] );
		}

		return floatval( $total * $rates['rates'][ $to_currency ] / $rates['rates'][ $from_currency ] );
	}

	/**
	 * Save product prices in a common format i.e. flattened (cents).
	 *
	 * This is to avoid number format issue. For example: 9,00 € maybe be considered as 9 in Euro,
	 * but 900 in USD.
	 *
	 * So we convert all prices into a common format (flattened) and then convert it back to the original as needed.
	 *
	 * @param array  $form_meta Form meta data.
	 * @param int    $form_id   Form ID.
	 * @param string $meta_name Meta Name.
	 *
	 * @return array
	 */
	public static function admin_save_product_prices_in_global_currency_format( $form_meta, $form_id, $meta_name ) {
		global $mcg;

		$mcg_field          = self::form_get_mcg_field( $form_meta );
		$form_base_currency = $mcg->addon->get_form_base_currency( $form_meta );
		$to_currency        = GFCommon::get_currency();

		if ( empty( $mcg_field ) ) {
			return $form_meta;
		}

		// loop through all product fields.
		foreach ( $form_meta['fields'] as &$field ) {
			if ( 'product' !== $field->type && 'shipping' !== $field->type && 'option' !== $field->type ) {
				continue;
			}

			$field->mcg_base_price_flattened = self::flatten_price( $field->basePrice, $form_base_currency );
			$field->basePrice                = self::convert_currency_format( $field->basePrice, $form_base_currency, $to_currency );

			if ( empty( $field->choices ) ) {
				continue;
			}

			foreach ( $field->choices as &$choice ) {
				if ( empty( $choice['price'] ) ) {
					continue;
				}

				$choice['mcg_base_price_flattened'] = self::flatten_price( $choice['price'], $form_base_currency );
			}
		}

		return $form_meta;
	}

	/**
	 * Save product choices in global currency format.
	 *
	 * @param array  $provided_form_meta Form meta.
	 * @param int    $form_id            Form ID.
	 * @param [type] $meta_name          Meta Name.
	 *
	 * @return array
	 */
	public static function save_product_choice_prices_in_global_currency_format( $provided_form_meta, $form_id, $meta_name ) {
		global $mcg;

		$mcg_field          = self::form_get_mcg_field( $provided_form_meta );
		$form_base_currency = $mcg->addon->get_form_base_currency( $provided_form_meta );
		$to_currency        = GFCommon::get_currency();

		if ( empty( $mcg_field ) || empty( $_POST['gform_meta'] ) ) {
			return $provided_form_meta;
		}

		$form_json = $_POST['gform_meta'];
		$form_json = stripslashes( $form_json );
		$form_json = nl2br( $form_json );

		// Convert form meta JSON to array.
		$original_form_meta = json_decode( $form_json, true );
		$original_form_meta = GFFormsModel::convert_field_objects( $original_form_meta );

		foreach ( $original_form_meta['fields'] as $field_key => $field ) {
			if ( 'product' !== $field->type ) {
				continue;
			}

			if ( empty( $field->choices ) ) {
				continue;
			}

			foreach ( $field->choices as &$choice ) {
				if ( ! empty( $choice['price'] ) ) {
					$choice['price'] = self::convert_currency_format( $choice['price'], $form_base_currency, $to_currency );
				}
			}

			/**
			 * We modify the original form meta (that we get from $_POST) because that
			 * is intact value without any processing.
			 * And then copy the modified choices value to provided form meta.
			 */
			$provided_form_meta['fields'][ $field_key ]->choices = $field->choices;
		}

		return $provided_form_meta;
	}

	/**
	 * Convert currency format.
	 *
	 * @param string $money Formatted number, example: $4.00,00.
	 * @param string $from  From Currency, example: USD.
	 * @param string $to    To currency, example: EUR.
	 *
	 * @return string
	 */
	public static function convert_currency_format( $money, $from, $to ) {
		$from_currency = new RGCurrency( $from );
		$to_currency   = new RGCurrency( $to );
		$number = $from_currency->to_number( $money );
		return $to_currency->to_money( $number );
	}

	/**
	 * Get number from money.
	 *
	 * @param string $money Money.
	 * @param string $from From currency.
	 *
	 * @return int|float
	 */
	public static function get_number_from_money( $money, $from ) {
		$from_currency = new RGCurrency( $from );
		return $from_currency->to_number( $money );
	}

	/**
	 * Experimental helper function to format number into a non decimal format.
	 * Example: 50.5 will become 5050 (by removing decimal and add a zero padding.)
	 * This format will work with both US system and Euro system (1,000.00 vs 1.000,00).
	 *
	 * @param int    $money         Money.
	 * @param string $from_currency From currency.
	 *
	 * @return int
	 */
	public static function flatten_price( $money, $from_currency ) {
		$number = self::get_number_from_money( $money, $from_currency );
		return $number * 100;
	}

	/**
	 * Unflatten the number.
	 *
	 * @param int $number Number.
	 *
	 * @return int
	 */
	public static function unflatten_price( $number ) {
		return $number / 100;
	}

	/**
	 * Gravity forms performs a checksum check in form_display.php:validate_field() function
	 * This check fails for manual currency because we change the value of the product field
	 * in this function override the validation, add our own validation to ensure the amount
	 * being charged is correct.
	 *
	 * @param array  $validation Validation.
	 * @param array  $value      Value of the field.
	 * @param Object $form       Form object.
	 * @param Object $field      Field object.
	 * @param string $context    Conext.
	 *
	 * @return array
	 */
	public static function mcg_override_state_validation( $validation, $value, $form, $field, $context ) {
		global $mcg;

		// If form doesnt have MCG field then return.
		$mcg_field = self::form_get_mcg_field( $form );
		if ( empty( $mcg_field ) || ! is_a( $mcg_field, 'GF_Field' ) ) {
			return $validation;
		}

		// If field is not product or shipping, return.
		if ( ! in_array( $field->type, array( 'product', 'shipping', 'option', 'total' ), true ) ) {
			return $validation;
		}

		if ( 'automatic' === self::get_multi_currency_mode( $form ) || 'shipping' === $field->type || is_string( $value ) ) {
			/**
			 * Todo - later.
			 *
			 * This is a temporary fix for the checksump validation problem.
			 * Lets manually calculate the total and handle validation manually for automatic. Check failed_state_validation in form_display.php.
			 */

			if (
				__( 'Please enter a valid value.', 'gravityforms' ) === $validation['message']
				||
				__( 'Invalid selection. Please select from the available choices.', 'gravityforms' ) === $validation['message']
				||
				preg_match( '/Submitted value (.*) does not match.*/', $validation['message'] )
				) {
					$validation['is_valid'] = true;
					return $validation;
			}
		}

		if ( is_string( $value ) ) {
			return $validation;
		}

		$global_currency = GFCommon::get_currency();
		$posted_price    = $value[ $field->id . '.2' ];
		$posted_qty      = $value[ $field->id . '.3' ];
		$posted_currency = self::get_users_selected_currency( $form );
		$posted_number   = self::get_number_from_money( $posted_price, $posted_currency );

		if ( empty( $field[ 'multi_currency_manual_price_' . $posted_currency ] ) ) {
			return $validation;
		}

		$price = $field[ 'multi_currency_manual_price_' . $posted_currency ];

		// Mark as valid.
		if ( intval( $price ) === intval( $price ) ) {
			$validation['is_valid'] = true;
		}

		return $validation;
	}

	/**
	 * Append currency in flatten version to a custom data attribute (data-mcg-base-price)
	 * to the product field when it is viewing
	 *
	 * @param string   $field_content Field Content.
	 * @param GF_Field $field         Field object.
	 * @param string   $value         Value.
	 * @param int      $x             Unknown.
	 * @param int      $form_id       Form ID.
	 *
	 * @return stirng
	 */
	public static function modify_field_html_add_flatten_price_to_product_field( $field_content, $field, $value, $x, $form_id ) {
		if ( 'product' !== $field->type && 'shipping' !== $field->type && 'option' !== $field->type ) {
			return $field_content;
		}

		$dom_map = array(
			array(
				'type'      => 'product',
				'inputType' => 'singleproduct',
				'selector'  => '.ginput_product_price',
				'multiple'  => false,
			),
			array(
				'type'      => 'product',
				'inputType' => 'radio',
				'selector'  => '.gfield-choice-input',
				'multiple'  => true,
			),
			array(
				'type'      => 'product',
				'inputType' => 'select',
				'selector'  => 'option',
				'multiple'  => true,
			),
			array(
				'type'      => 'product',
				'inputType' => 'hiddenproduct',
				'selector'  => '.ginput_amount',
				'multiple'  => false,
			),
			array(
				'type'      => 'shipping',
				'inputType' => 'singleshipping',
				'selector'  => '.ginput_shipping_price',
				'multiple'  => false,
			),
			array(
				'type'      => 'shipping',
				'inputType' => 'select',
				'selector'  => 'option',
				'multiple'  => true,
			),
			array(
				'type'      => 'shipping',
				'inputType' => 'radio',
				'selector'  => '.gfield-choice-input',
				'multiple'  => true,
			),
			array(
				'type'      => 'option',
				'inputType' => 'checkbox',
				'selector'  => '.gfield-choice-input',
				'multiple'  => true,
			),
			array(
				'type'      => 'option',
				'inputType' => 'select',
				'selector'  => 'option',
				'multiple'  => true,
			),
			array(
				'type'      => 'option',
				'inputType' => 'radio',
				'selector'  => '.gfield-choice-input',
				'multiple'  => true,
			),
		);

		$dom = str_get_html( $field_content );

		if ( ! $dom ) {
			return $field_content;
		}
		foreach ( $dom_map as $map ) {
			if ( $map['inputType'] !== $field->inputType || $field->type !== $map['type'] ) {
				continue;
			}

			$placeholder_flag = false;
			foreach ( $dom->find( $map['selector'] ) as $key => &$input ) {
				if ( ! $map['multiple'] ) {
					$input->setAttribute( 'data-mcg-base-price', $field->mcg_base_price_flattened );
				} else {
					// if tag is <option> and has class 'gf_placeholder' then we need to decrease the key by 1 to offset the placeholder.
					if ( 'option' === $input->tag && 'gf_placeholder' === $input->class ) {
						$placeholder_flag = true;
						continue;
					}

					$choice_price = (int) self::get_flat_price_from_choices( $placeholder_flag ? --$key : $key, $field['choices'] );
					$input->setAttribute( 'data-mcg-base-price', $choice_price );
				}
			}
		}

		return $dom->outertext;
	}

	/**
	 * Get flattened price from choices.
	 *
	 * @param string $choice_index Choice index.
	 * @param array  $choices      Choices.
	 *
	 * @return string|false
	 */
	public static function get_flat_price_from_choices( $choice_index, $choices ) {
		if ( empty( $choices ) || empty( $choices[ $choice_index ] ) ) {
			return false;
		}

		return ! empty( $choices[ $choice_index ]['mcg_base_price_flattened'] ) ? $choices[ $choice_index ]['mcg_base_price_flattened'] : 0;
	}

	/**
	 * Get users selected currency from the posted data.
	 *
	 * @param array $form Form.
	 *
	 * @return string
	 */
	public static function get_users_selected_currency( $form ) {
		$mcg_field = self::form_get_mcg_field( $form );
		$field_id  = $mcg_field['id'];
		$currency  = filter_input( INPUT_POST, 'input_' . $field_id );
		return $currency;
	}

	/**
	 * Check if form has a calculation field.
	 *
	 * @param array $form Form.
	 *
	 * @return bool
	 */
	public static function form_has_calculation_field( $form ) {
		$calculation_field_ids = array();

		foreach ( $form['fields'] as $field ) {
			if ( 'calculation' === $field->inputType ) {
				$calculation_field_ids[] = $field->id;
			}
		}

		return $calculation_field_ids;
	}
}
