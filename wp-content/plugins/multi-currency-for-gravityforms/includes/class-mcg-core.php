<?php
/**
 * Plugin Core
 *
 * @link       http://ideawp.com
 * @since      1.0.0
 *
 * @package    Mcg
 * @subpackage Mcg/includes
 */

/**
 * Core Class
 *
 * @since      1.0.0
 * @package    Mcg
 * @subpackage Mcg/includes
 */
class Mcg_Core {

	/**
	 * Plugin links array.
	 *
	 * @var array $args plugin links.
	 */
	private static $args;

	/**
	 * Call functions
	 *
	 * @param array $args pluign links array.
	 * @return void
	 */
	public static function run( $args ) {
		self::$args = $args;

		// Add link to settings page.
		add_filter( 'plugin_action_links', array( __CLASS__, 'modify_plugin_action_links' ), 10, 2 );
		add_filter( 'network_admin_plugin_action_links', array( __CLASS__, 'modify_plugin_action_links' ), 10, 2 );
	}

	/**
	 * Add Settings link to plugins area.
	 *
	 * @since 1.0.0
	 *
	 * @param array  $links Links array in which we would prepend our link.
	 * @param string $file  Current plugin basename.
	 * @return array Processed links.
	 */
	public static function modify_plugin_action_links( $links, $file ) {
		$args = self::$args;

		$my_plugin = plugin_basename( $args['plugin_file'] );
		if ( $my_plugin != $file ) {
			return $links;
		}

		$add_links = array();
		foreach ( $args['plugin_page_link'] as $key => $value ) {
			$links_key               = $args['plugin_prefix'] . '-' . $key;
			$add_links[ $links_key ] = '<a href="' . esc_url( $value['url'] ) . '" target="_BLANK">' . $value['text'] . '</a>';
		}

		// Add a few links to the existing links array.
		return array_merge( $add_links, $links );
	}

}
