<?php
/**
 * The file that defines the core plugin class
 *
 * @link       http://ideawp.com
 * @since      1.0.0
 *
 * @package    Mcg
 * @subpackage Mcg/includes
 */

/**
 * The core plugin class.
 *
 * @since      1.0.0
 * @package    Mcg
 * @subpackage Mcg/includes
 */
class Mcg {

	/**
	 * The unique identifier of this plugin.
	 *
	 * @since    1.0.0
	 * @var      string    $plugin_name    The string used to uniquely identify this plugin.
	 */
	protected $plugin_name;

	/**
	 * The current version of the plugin.
	 *
	 * @since    1.0.0
	 * @var      string    $version    The current version of the plugin.
	 */
	protected $version;

	/**
	 * Object for Mcg_GF_Addon.
	 *
	 * @var Mcg_GF_Addon
	 */
	public $addon;

	/**
	 * Define the core functionality of the plugin.
	 *
	 * Set the plugin name and the plugin version that can be used throughout the plugin.
	 * Load the dependencies, define the locale, and set the hooks for the admin area and
	 * the public-facing side of the site.
	 *
	 * @since    1.0.0
	 */
	public function __construct() {
		if ( defined( 'MCG_PLUGIN_VERSION' ) ) {
			$this->version = MCG_PLUGIN_VERSION;
		} else {
			$this->version = '1.0.0';
		}
		$this->plugin_name = 'mcg';

		$this->load_dependencies();
		$this->set_locale();
	}

	/**
	 * Load the required dependencies for this plugin.
	 *
	 * Include the following files that make up the plugin:
	 *
	 * - Mcg_I18n. Defines internationalization functionality.
	 * - Mcg_Admin. Defines all hooks for the admin area.
	 * - Mcg_Public. Defines all hooks for the public side of the site.
	 *
	 * Create an instance of the loader which will be used to register the hooks
	 * with WordPress.
	 *
	 * @since    1.0.0
	 */
	private function load_dependencies() {
		require_once plugin_dir_path( dirname( __FILE__ ) ) . 'includes/class-mcg-license.php';
		$license = Mcg_Licence::get_instance();

		require_once plugin_dir_path( dirname( __FILE__ ) ) . 'admin/class-mcg-admin.php';

		if ( ! $license->is_license_active() ) {
			return;
		}

		require_once MCG_PATH . 'public/class-mcg-public.php';
		require_once MCG_PATH . 'includes/class-mcg-core.php';
		require_once MCG_PATH . 'includes/class-mcg-compat-stripe.php';
		require_once MCG_PATH . 'includes/class-compat-ecommerce-fields.php';

		if ( ! function_exists( 'file_get_html' ) ) {
			require_once plugin_dir_path( dirname( __FILE__ ) ) . 'includes/vendor/simple_html_dom.php';
		}

		/**
		 * Responsible for all currency conversion related function.
		 */
		require_once plugin_dir_path( dirname( __FILE__ ) ) . 'includes/class-mcg-currency.php';

		require_once plugin_dir_path( dirname( __FILE__ ) ) . 'includes/class-mcg-shortcode.php';

		Mcg_Currency::run();
		Mcg_Core::run(
			array(
				'plugin_file'      => MCG_FILE,
				'plugin_prefix'    => 'idea_mcg',
				'plugin_page_link' => array(
					'settings' => array(
						'text' => esc_html__( 'Settings', 'idea_mcg' ),
						'url'  => add_query_arg(
							array(
								'page'    => 'gf_settings',
								'subview' => 'multi-currency-gf',
							),
							admin_url( 'admin.php' )
						),
					),
					'support' => array(
						'text' => esc_html__( 'Support', 'idea_mcg' ),
						'url'  => 'https://ideawp.com/support/',
					),
				),
			)
		);

		Mcg_Shortcode::run();
		Mcg_Compat_Stripe::run();
		Mcg_Compat_Ecom_Fields::run();

		add_action( 'gform_loaded', array( $this, 'run_gf_addon' ), 5 );

		// Admin error msg.
		add_action( 'gform_admin_error_messages', array( $this, 'mcg_api_error_msg' ) );
	}

	/**
	 * Define the locale for this plugin for internationalization.
	 *
	 * @since    1.0.0
	 */
	private function set_locale() {
		add_action( 'plugins_loaded', array( $this, 'load_plugin_textdomain' ) );
	}

	/**
	 * Load the plugin text domain for translation.
	 *
	 * @since    1.0.0
	 */
	public function load_plugin_textdomain() {
		load_plugin_textdomain(
			'idea_mcg',
			false,
			dirname( plugin_basename( __FILE__ ) ) . '/languages/'
		);
	}

	/**
	 * The name of the plugin used to uniquely identify it within the context of
	 * WordPress and to define internationalization functionality.
	 *
	 * @since     1.0.0
	 * @return    string    The name of the plugin.
	 */
	public function get_plugin_name() {
		return $this->plugin_name;
	}


	/**
	 * Retrieve the version number of the plugin.
	 *
	 * @since     1.0.0
	 * @return    string    The version number of the plugin.
	 */
	public function get_version() {
		return $this->version;
	}

	/**
	 * Boostrap GF addon.
	 *
	 * @return void
	 */
	public function run_gf_addon() {
		if ( ! method_exists( 'GFForms', 'include_addon_framework' ) ) {
			return;
		}

		require_once plugin_dir_path( dirname( __FILE__ ) ) . 'includes/class-mcg-gf-addon.php';
		require_once plugin_dir_path( dirname( __FILE__ ) ) . 'includes/class-mcg-multicurrency-gf-field.php';
		GFAddOn::register( 'Mcg_GF_Addon' );
		GF_Fields::register( new Mcg_MultiCurrency_GF_Field() );
		$this->addon = Mcg_GF_Addon::get_instance();
	}

	/**
	 * Mcg Api not enter notice.
	 *
	 * @param array $messages Get pre define messages.
	 * @return array Fallack notice.
	 */
	public function mcg_api_error_msg( $messages ) {
		global $mcg;

		$api = $mcg->addon->get_plugin_setting( 'open_exchange_app_id' );
		if ( ! $api ) {
			$url = site_url( 'wp-admin/admin.php?page=gf_settings&subview=multi-currency-gf' );
			/* translators: link of mcg plugin. */
			$error = sprintf( __( 'Multi-Currency for Gravity Forms plugin requires the %1$sOpen Exchange Rates%2$s API to work', 'idea_mcg' ), "<a href='$url'>", '</a>' );

			$messages[] = '<strong>Important: </strong>' . $error;
		}
		return $messages;
	}

}
