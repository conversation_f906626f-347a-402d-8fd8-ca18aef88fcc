<?php
/**
 * The admin-specific functionality of the plugin.
 *
 * @link       http://example.com
 * @since      1.0.0
 *
 * @package    Mcg
 * @subpackage Mcg/admin
 */

/**
 * The admin-specific functionality of the plugin.
 *
 * @package    Mcg
 * @subpackage Mcg/admin
 */
class Mcg_Admin {

	/**
	 * The ID of this plugin.
	 *
	 * @since    1.0.0
	 * @var      string    $plugin_name    The ID of this plugin.
	 */
	private $plugin_name;

	/**
	 * The version of this plugin.
	 *
	 * @since    1.0.0
	 * @var      string    $version    The current version of this plugin.
	 */
	private $version;

	/**
	 * Initialize the class and set its properties.
	 *
	 * @since 1.0.0
	 * @param string $plugin_name The name of this plugin.
	 * @param string $version     The version of this plugin.
	 */
	public function __construct( $plugin_name, $version ) {
		$this->plugin_name = $plugin_name;
		$this->version     = $version;
	}

	/**
	 * Register the JavaScript for the admin area.
	 *
	 * @since    1.0.0
	 */
	public static function localize_scripts() {

		$data = array(
			'exchange_rate'   => Mcg_Currency::get_currency_exchange_rate(),
			'all_currencies'  => RGCurrency::get_currencies(),
			'global_currency' => GFCommon::get_currency(),
		);

		wp_localize_script( 'mcg_admin_js', 'mcg_admin_data', $data );

	}

}
