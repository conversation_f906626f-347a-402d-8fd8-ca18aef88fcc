(function( $ ) {
	'use strict';

	var mcg_admin = {

		on_ready: function () {
			if ( ! $( '.gform_wrapper' ).length ) {
				return;
			}

			mcg_admin.change_global_currency();
			mcg_admin.set_initial_values();
			mcg_admin.handle_price_field_change();
			mcg_admin.handle_manual_price_field_visiblity();
			mcg_admin.watch_currency_mode_change();
			mcg_admin.toggle_manual_price_notice();
			mcg_admin.handle_currency_selection();
			mcg_admin.handle_specific_currency_checkbox();
			mcg_admin.change_currency_for_newly_added_fields();
			mcg_admin.toggle_currency_selection_field();
			mcg_admin.prevent_multiple_currency_switcher_fields();

			document.addEventListener( "gform/form_editor/setting_selected", function () { 
				mcg_admin.handle_price_field_change();
				mcg_admin.toggle_currency_selection_field();
				mcg_admin.handle_manual_price_field_visiblity();
				mcg_admin.handle_shipping_field();
			} );
		},

		/**
		 * Get current form's base currency.
		 *
		 * @returns string
		 */
		get_base_currency: function () {
			return form[ 'multi-currency-gf' ][ 'base_currency' ] ? form[ 'multi-currency-gf' ][ 'base_currency' ] : mcg_admin_data.global_currency;
		},

		/**
		 * Change global data structure gf_global.gf_currency_config
		 * This data structure is used through out the page by Gravity forms and MCG.
		 */
		change_global_currency: function () {
			if ( !form[ 'multi-currency-gf' ] ) {
				return;
			}
			var base_currency = mcg_admin.get_base_currency();

			gf_global.gf_currency_config = mcg_admin_data.all_currencies[ base_currency ];

			// Replace this function because the one built by GF is neither dynamic nor has hooks.
			// Only option to replace the functionality of this function is to re-define it.
			window.GetCurrentCurrency = function() {
				var currency = new Currency( gf_global.gf_currency_config );
				return currency;
			}

			var new_currency_obj = GetCurrentCurrency();
			var old_currency_obj = new Currency( mcg_admin_data.all_currencies[ mcg_admin_data.global_currency ] );
			
			// change currency for total field
			var new_money = new_currency_obj.toMoney( 0 );
			$( '.ginput_total' ).html( new_money ).val( new_money );

			// change currency for other product fields
			form.fields.forEach( field => {
				var $field = $( '#field_' + field.id );
				if ( field[ "type" ] == "hiddenproduct" || field[ "inputType" ] == "singleproduct" || field[ "inputType" ] == "singleshipping" || field[ "inputType" ] == "calculation" ) {
					var flatten_price = mcg_admin.get_product_price_flattened( $field );
					var currency_amt = mcg_admin.flat_to_current_currency( flatten_price, false );
					$field.find( ".ginput_product_price" ).html( currency_amt ).val( currency_amt );
				}

				if ( field[ "inputType" ] == "radio" ) {
					// Todo needs testing.
					$.each( field.choices, function ( index, choice ) { 
						var price = mcg_admin.convert_currency_format( choice.price, old_currency_obj, new_currency_obj );
						choice.price = price;
						$field.find( 'radio_choice_price_' + index ).html( price );
					} );
				}
			} );
			
		},

		/**
		 * Change currency for newly added fields.
		 * By default the currency is site global, we change it to the form currency.
		 */
		change_currency_for_newly_added_fields: function () {
			jQuery( document ).on( 'gform_field_added', function ( event, form, field ) {
				var cur   = GetCurrentCurrency();
				var money = cur.toMoney( 0 );
				$( `#field_${field.id} .ginput_total, #field_${field.id} .ginput_product_price, #field_${field.id} .ginput_shipping_price ` ).html( money ).val( money )
			} );
		},

		/**
		 * Save currency specific 'manual' prices.
		 * 
		 * Data is saved by SetFieldProperty function provided by Gravity forms.
		 */
		handle_price_field_change: function () {
			$( document ).on( 'change', '.mcg_manual_currency_field', function (  ) {
				var currency_key = jQuery( this ).data( 'value' );
				SetFieldProperty( 'multi_currency_manual_price_' + currency_key, jQuery( this ).val() );
			} );
		},

		/**
		 * Set initial values for:
		 * 1. Manual currency price in product field.
		 * 2. Selected currencies checkboxes for 'specific' selection.
		 * 
		 * This function is also called when a field is clicked.
		 */
		set_initial_values: function () {
			jQuery( document ).on( 'gform_load_field_settings', function ( event, field, form ) {

				/**
				 * Empty default value for manual currency price so that it doesn't show the currency
				 * of the previously selected product/shipping field
				 */
				jQuery( "[id^=mcg_manual_currency_price_]" ).val( '' );
				for( var i in field ) {
					if( i.includes( "multi_currency_manual_price_" ) ) {
						var currency = i.replace( "multi_currency_manual_price_", "" );
						var field_class = ".mcg_manual_currency_price_" + currency;
						jQuery( field_class ).val( field[ i ] );
					}
				}

				if ( field.multi_currency_currency_list ) {
					for ( i in field.multi_currency_currency_list ) {
						var currency = field.multi_currency_currency_list[ i ];
						$( '#mcg_currency_list_' + currency ).prop( 'checked', true );
					}
				}

				// @todo This is not aligned with the name of function. 
				// Maybe either change function name or create a seperate function

				// Show or hide manual prices based on selected currencies.
				window.setTimeout( function () {
					mcg_admin.handle_manual_price_field_visiblity();
				}, 10 );
			});
		},

		/**
		 * Handle visiblity of manual currency prices based on selected currecnies.
		 * example: if currency selection is 'specific' and only USD and INR are enabled, then 
		 * hide price field for other currencies and show only USD and INR field.
		 *
		 * Price fields are inside Product field.
		 */
		handle_manual_price_field_visiblity: function () {
			var mcg_field = mcg_admin.get_mcg_field();

			// If no MCG field is there in the form, then hide all manual prices.
			if ( ! mcg_field ) {
				$( ".multi_currency_manual_prices_settings" ).hide();
				return;
			}

			// If current selected field is not product and shipping then hide manual price setting.
			var selectedField = GetSelectedField();
			if ( ! selectedField || ! [ 'product', 'shipping' ].includes( selectedField.type ) ) {
				$( ".multi_currency_manual_prices_settings" ).hide();
				return;
			}

			var currency_select     = mcg_field.multi_currency_select ? mcg_field.multi_currency_select : 'all';
			var multi_currency_mode = mcg_field.multi_currency_mode ? mcg_field.multi_currency_mode : 'automatic';

			if ( multi_currency_mode == 'automatic' ) {
				$( ".multi_currency_manual_prices_settings" ).hide();
				return;
			} else {
				$( ".multi_currency_manual_prices_settings" ).show();
			}

			if ( 'all' === currency_select ) {
				$( ".mcg_manual_currency_price_wrap" ).show();
			} else {
				// Hide all currencies, then show only the enabled currencies.
				$( ".mcg_manual_currency_price_wrap" ).hide();

				if ( Array.isArray( mcg_field.multi_currency_currency_list ) ) {
					mcg_field.multi_currency_currency_list.forEach( currency => {
						$( ".mcg_manual_currency_price_wrap_" + currency ).show()
					} );
				}
			}
		},

		/**
		 * Watch currency mode change.
		 */
		watch_currency_mode_change: function () {
			$( '[name="multi_currency_mode"]' ).change( function () {
				mcg_admin.toggle_manual_price_notice( $( this ).val() );
			}); 
		},

		/**
		 * Show or hide Manual currency notice if mode is manual.
		 * 
		 * @param {string} mode Conversion Mode - 'automatic' or 'manual'
		 */
		toggle_manual_price_notice: function( mode ) {
			if ( ! mode ) {
				var mcg_field = mcg_admin.get_mcg_field();
				mode          = ( mcg_field && mcg_field.multi_currency_mode ) ? mcg_field.multi_currency_mode : 'automatic';
			}

			if ( 'automatic' == mode ) {
				$( ".multi_currency_mode_manual_msg" ).hide();
			} else {
				$( ".multi_currency_mode_manual_msg" ).show();
			}
		},

		/**
		 * Return the multi currency field.
		 */
		get_mcg_field: function () {
			return form.fields.find( field => 'multicurrency' === field.type );
		},

		/**
		 * Show or hide the currecny selection list based on section i.e. 'all' or 'specific'.
		 * 
		 * If curency selection is 'specific' then show the currency checkboxes.
		 * else hide it.
		 */
		handle_currency_selection: function () {

			$( document ).on( 'change', "[name=multi_currency_select]", function () {
				mcg_admin.toggle_currency_selection_field();
			} );
		},

		/**
		 * If curency selection is 'specific' then show the currency checkboxes.
		 * else hide it.
		 */
		toggle_currency_selection_field: function () {
			var selection = jQuery( '[name=multi_currency_select]:checked' ).val();
			if ( selection === 'all' ) {
				$( '.multi_currency_currency_list_settings' ).hide()
			} else {
				$( '.multi_currency_currency_list_settings' ).show()
			}
		},

		/**
		 * Save values of the curency selection checkboxes.
		 */
		handle_specific_currency_checkbox: function () {
			$( document ).on( 'change', '.multi_currency_currency_list', function () {
				var selected_currencies = [];

				jQuery( ".multi_currency_currency_list" ).each( function () {
					if ( jQuery( this ).is( ":checked" ) ) {
						selected_currencies.push( jQuery( this ).data( "value" ) );
					}
				} );

				SetFieldProperty( 'multi_currency_currency_list', selected_currencies );				
			} );
		},

		/**
		 * Handle click on the shipping field.
		 */
		handle_shipping_field: function () {
			var selected_field = GetSelectedField();
			var mcg_field = mcg_admin.get_mcg_field();

			if ( !selected_field || 'shipping' !== selected_field.type || ! mcg_field ) {
				return;
			}

			if ( 'specific' === mcg_field.multi_currency_select ) {
				$( '.gfield_shipping  .multi_currency_manual_prices_settings' ).show();
				$( '.gfield_shipping .mcg_manual_currency_price_wrap' ).hide();

				if ( ! Array.isArray( mcg_field.multi_currency_currency_list ) ) {
					return;
				}

				mcg_field.multi_currency_currency_list.forEach( currency => {
					$( '.gfield_shipping .mcg_manual_currency_price_wrap_' + currency ).show();
				} );

			} else {
				$( '.gfield_shipping  .multi_currency_manual_prices_settings' ).show();
				$( '.gfield_shipping  .mcg_manual_currency_price_wrap' ).show();
			}
		},

		/**
		 * Convert decimal and thousand seperator when currency changes.
		 * Example: If we are going from USD to EUR, we need to change 
		 * all commas to dots and all dots to commas.
		 * 
		 * @param string money          Amount to be converted.
		 * @param object from_currency  From Currency object.
		 * @param object to_currency    To Currency object.
		 */
		convert_currency_format: function ( money, from_currency, to_currency ) {
			if ( typeof from_currency === 'string' ) {
				from_currency = new Currency( idea_mcg[ "all_currencies" ][ from_currency ] );
			}

			if ( typeof to_currency === 'string' ) {
				to_currency = new Currency( idea_mcg[ "all_currencies" ][ to_currency ] );
			}

			var number = from_currency.toNumber( money );

			if ( ',' === to_currency.currency.decimal_separator ) {
				number = new String( number );
				number = number.replace( '.', ',' );
			}

			return to_currency.toMoney( number );
		},

		/**
		 * Prevent multiple multicurrency fields from being added. 
		 */
		prevent_multiple_currency_switcher_fields: function () {
			gform.addFilter('gform_form_editor_can_field_be_added', function (canFieldBeAdded, type) {
				if (type !== 'multicurrency') {
					return canFieldBeAdded;
				}
			 
				if ( GetFieldsByType(['multicurrency']).length >= 1 ) {
					alert( wp.i18n.__( 'Sorry, you can\'t add more than one currency switcher field.', 'idea_mcg' ) );
					return false;
				} 
			 
				return canFieldBeAdded;
			});
		},

		/**
		 * Returns the flattened price of the product. In case of the radio/dropdown field, it returns the price of the selected option.
		 * 
		 * @param {int} price 
		 */
		get_product_price_flattened: function ( $product_field ) { 
			var price = false;

			$product_field = $product_field.filter( ':not(.gfield--type-quantity)' );

			// if there are multiple fields and one of them is `.gfield--type-option` then use that.
			if ( $product_field.length > 1 && $product_field.hasClass( 'gfield--type-option' ) ) {
				$product_field = $product_field.filter( '.gfield--type-option' );
			}
			
			if ( $product_field.hasClass( 'gfield--input-type-radio' ) ) {
				var $selected_radio = $product_field.find( 'input[type="radio"]:checked' );
				price = $selected_radio.data( 'mcg-base-price' );
			} else if ( $product_field.hasClass( 'gfield--input-type-select' ) ) {
				var $selected_option = $product_field.find( 'option:selected' );
				price = $selected_option.data( 'mcg-base-price' );
			} else if ( $product_field.hasClass( 'gfield--input-type-calculation' ) ) {
				var formatted_price = $product_field.find( '.ginput_product_price' ).text();
				price = mcg.flatten_formatted_price( formatted_price );
			} else if ( $product_field.hasClass( 'gfield--input-type-checkbox' ) ) { 
				var $selected_checkbox = $product_field.find( 'input[type="checkbox"]:checked' );
				$selected_checkbox.each( function () {
					price += $( this ).data( 'mcg-base-price' );
				} );
			} else {
				var $input = $product_field.find( '[data-mcg-base-price]' );
				if ( $input.length ) {
					price = $input.data( 'mcg-base-price' );
				}
			}
			
			return price;
		},

		/**
		 * Pass the flat amount and get the price in current currency.
		 *
		 * @param int flat_amt 
		 * @param int form_id 
		 *
		 * @returns 
		 */
		flat_to_current_currency: function ( flat_amt, return_number = false ) {
			var base_currency = mcg_admin.get_base_currency();
			var amount        = mcg_admin.unflatten( flat_amt );
			amount            = Math.round( amount * 100 ) / 100; // round to 2 decimal places.

			if ( return_number ) {
				return amount;
			}

			var currencyObj = new Currency( idea_mcg[ "all_currencies" ][ base_currency ] );
			return currencyObj.toMoney( amount, true );
		},

		/**
		 * Unflatten the amount.
		 * 
		 * @param {int} flat_amt Flatt amount. Ex: 1905 
		 * @returns float, unflat amount ex: 19.05
		 */
		unflatten: function ( flat_amt ) {
			return flat_amt / 100;
		},
		
	}

	$( document ).ready( function () {
		mcg_admin.on_ready();
	} )

	window.mcg_admin = mcg_admin;
})( jQuery );
