/******/ (() => { // webpackBootstrap
/******/ 	var __webpack_modules__ = ({

/***/ "./public/js/calculation-field.js":
/*!****************************************!*\
  !*** ./public/js/calculation-field.js ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
const CalculationField = {
  /**
   * Init.
   */
  init: function () {
    gform.addAction('gform_input_change', CalculationField.update_product_prices_on_input_change, 12);
    CalculationField.add_duplicate_pricing_label();
    jQuery('.gform_wrapper form').on('submit', CalculationField.set_calculation_field_on_submit);
    gform.addFilter('gform_merge_tag_value_pre_calculation', CalculationField.filter_merge_tag_value_pre_calculation);
  },
  /**
   * Change product prices when value of any input field changes (gform_input_change).
   */
  update_product_prices_on_input_change: function (elem, formId, fieldId) {
    var $form = jQuery(elem).closest('.gform_wrapper');
    var mode = _mcg.getMode(formId);
    var $select = $form.find('.ginput_container_multicurrency select');
    if (!$select) {
      return;
    }
    var new_currency = $select.val();

    // Update Each Product Price.
    if ('automatic' !== mode) {
      return;
    }
    if (gform.applyFilters('mcg_calculation_skip_update_price', false, fieldId, formId, new_currency)) {
      return;
    }
    _mcg.update_product_field_price_automatic(formId, new_currency);
  },
  /**
   * Add duplicate pricing label for calculation field with class `mcg_ginput_product_price`.
  */
  add_duplicate_pricing_label: function () {
    jQuery('.gfield--input-type-calculation').each(function () {
      if (0 === jQuery(this).find('.mcg_ginput_product_price').length) {
        let $price = jQuery(this).find('.ginput_product_price');
        $price.after('<label class="mcg_ginput_product_price gform-field-label"></label>');
        $price.hide();
      }
    });
  },
  /**
   * Set calculation field on submit.
   */
  set_calculation_field_on_submit: function () {
    jQuery(this).find('.gfield--input-type-calculation').each(function () {
      var suffix = jQuery(this).attr('id').replace('field', ''); // suffix is like: _[formid]_[fieldid]
      let $mcg_price = jQuery(this).find('.mcg_ginput_product_price');
      let price = $mcg_price.text();
      jQuery(this).find('#ginput_base_price' + suffix).val(price);
    });
  },
  /**
   * Change currency before the value is used for calculation.
   *
   * @param {*} value 
   * @param {*} match 
   * @param {*} isVisible 
   * @param {*} formulaField 
   * @param {*} formId 
   * @returns 
   */
  filter_merge_tag_value_pre_calculation: function (value, match, isVisible, formulaField, formId) {
    return _mcg.toNumber(value);
  }
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (CalculationField);

/***/ }),

/***/ "./public/js/compat-conditional-pricing.js":
/*!*************************************************!*\
  !*** ./public/js/compat-conditional-pricing.js ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
const CompatConditionalPricing = {
  init: function () {
    if ('undefined' === typeof GWConditionalPricing) {
      return;
    }
    if (!jQuery('.gfield--type-multicurrency').length) {
      return;
    }

    // Run this before mcg.filter_gform_get_base_price. Pass the conditional price.
    gform.addFilter('mcg_get_product_price_flattened', CompatConditionalPricing.updateBasePrice, 5);

    /**
     * Convert the prices before its saved by Conditional Pricing.
     *
     * args = {gwcp, formId, input,inputId,isReset, productId}
     */
    gform.addFilter('gpcp_price', function (origPrice, args) {
      var new_amt = _mcg.flatToCurrentCurrency(origPrice * 100, args.formId, true);
      return new_amt;
    });
    gform.addFilter('mcg_calculation_skip_update_price', CompatConditionalPricing.skipUpdatePrice, 10);
  },
  skipUpdatePrice: function (skip) {
    // Prevent possible loop by saving the execution time and then returning true if
    // the function is called again within 50 ms.
    window.gwcp_price_last_execution = window.gwcp_price_last_execution || 0;
    console.log('diff', Date.now() - window.gwcp_price_last_execution);
    if (Date.now() - window.gwcp_price_last_execution < 100) {
      return true;
    }
    if (!jQuery('.gfield--input-type-calculation').length) {
      return true;
    }
    window.gwcp_price_last_execution = Date.now();
  },
  /**
   * Change the base price of the product field based on the conditional pricing.
   *
   * Code inspired from updatePricing() function of Conditional Pricing
   * (gwconditionalpricing/scripts/gwconditionalpricing.js)
   * 
   * @param {*} fieldId 
   * @param {*} productId 
   *
   * @returns float|false
   */
  getConditionalPrice: function (formId, productFieldId) {
    if (!GWConditionalPricing || !GWConditionalPricing.getFormElement) {
      return false;
    }
    let gfcp = GWConditionalPricing.getFormElement(formId).data('gwcp');
    let price = false;
    if (!gfcp || !gfcp._pricingLogic || !productFieldId) {
      return price;
    }
    for (var productId in gfcp._pricingLogic) {
      // only run for one product.
      if (productId !== productFieldId) {
        continue;
      }
      if (!gfcp._pricingLogic.hasOwnProperty(productId)) {
        continue;
      }
      var pricingLevels = gfcp._pricingLogic[productId];

      // This can happen if they delete a product from the form without removing the conditional pricing rules.
      if (!pricingLevels || pricingLevels[0] === null) {
        continue;
      }
      for (var i = 0; i < pricingLevels.length; i++) {
        var pricingLevel = pricingLevels[i],
          isMatch = gfcp.isMatch(gfcp._formId, pricingLevel.conditionalLogic);
        if (!isMatch) {
          continue;
        }
        return pricingLevel.price;
      }

      // if no matching pricing level was found, set back to basePrice.
      return price;
    }
  },
  /**
   * Change the base price of the product field based on the conditional pricing.
   *
   * Code inspired from updatePricing() function of Conditional Pricing
   * (gwconditionalpricing/scripts/gwconditionalpricing.js)
   *
   * @param {*} price 
   * @param {*} $product_field 
   * @returns 
   */
  updateBasePrice: function (price, $product_field) {
    var formId = _mcg.get_form_id($product_field);
    var productFieldId = gf_get_input_id_by_html_id($product_field.attr('id'));
    var conditionalPrice = CompatConditionalPricing.getConditionalPrice(formId, productFieldId);
    if (!conditionalPrice) {
      return price;
    }

    // if a matching pricing level was found, set the price to the new price after currency conversion
    return conditionalPrice * 100;
  }
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (CompatConditionalPricing);

/***/ }),

/***/ "./public/js/compat-paypal-express.js":
/*!********************************************!*\
  !*** ./public/js/compat-paypal-express.js ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
const PaypalExpress = {
  init: function () {
    // Compatiblity with Paypal Express.
    PaypalExpress.maybe_change_decimals_for_paypal_express();
    PaypalExpress.refresh_currency_for_paypal_express();
  },
  /**
   * If paypal is enabled then change the decimals to 1.
   * @returns 
   */
  maybe_change_decimals_for_paypal_express: function () {
    if (!jQuery('.gfield--type-paypal').length) {
      return;
    }

    // if paypal is enabled then change the decimals to 1.
    for (const [key, value] of Object.entries(idea_mcg.all_currencies)) {
      if (idea_mcg.all_currencies[key].decimals >= 2) {
        idea_mcg.all_currencies[key].decimals = 1;
      }
    }
    idea_mcg.all_currencies['TWD'].decimals = 0;
  },
  /**
   * Refresh the currency for paypal express so that it can pick the new currency.
   */
  refresh_currency_for_paypal_express: function () {
    if (!gform.ppcp) {
      return;
    }
    window.setTimeout(function () {
      jQuery('.gform_wrapper').each(function () {
        var formId = mcg.get_form_id(jQuery(this));
        let manager = gform.ppcp.stateManager[formId];
        let dirty = ['currency'];
        manager.stateChangeCallback(manager.isInitialChange, dirty, manager.state, manager.prevState);
      });
    }, 3000);
  }
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (PaypalExpress);

/***/ }),

/***/ "./public/js/compat-stripe.js":
/*!************************************!*\
  !*** ./public/js/compat-stripe.js ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
const Stripe = {
  init: function () {
    // This is tempoaray code, remove it when GF has officially added the filter.
    gform.addFilter('gform_stripe_update_payment_amount', function (config) {
      var currency = _mcg.get_current_currency(formId);
      config.currency = currency.toLowerCase();
      return config;
    });
    gform.addFilter('gform_stripe_payment_element_updated_payment_information', function (paymentInformation, initPaymentInfo, feedId, formId) {
      var currency = _mcg.get_current_currency(formId);
      if (!currency) {
        return paymentInformation;
      }
      paymentInformation.currency = currency.toLowerCase();
      return paymentInformation;
    });
  }
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Stripe);

/***/ }),

/***/ "./public/js/countries.js":
/*!********************************!*\
  !*** ./public/js/countries.js ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({
  'Afghanistan': {
    'abbreviation': 'AF',
    'currency': 'USD'
  },
  'Albania': {
    'abbreviation': 'AL',
    'currency': 'USD'
  },
  'Algeria': {
    'abbreviation': 'DZ',
    'currency': 'DZD'
  },
  'American Samoa': {
    'abbreviation': 'AS',
    'currency': 'USD'
  },
  'Andorra': {
    'abbreviation': 'AD',
    'currency': 'EUR'
  },
  'Angola': {
    'abbreviation': 'AO',
    'currency': 'USD'
  },
  'Anguilla': {
    'abbreviation': 'AI',
    'currency': 'USD'
  },
  'Antarctica': {
    'abbreviation': 'AQ',
    'currency': 'USD'
  },
  'Antigua And Barbuda': {
    'abbreviation': 'AG',
    'currency': 'XCD'
  },
  'Argentina': {
    'abbreviation': 'AR',
    'currency': 'ARS'
  },
  'Armenia': {
    'abbreviation': 'AM',
    'currency': 'AMD'
  },
  'Aruba': {
    'abbreviation': 'AW',
    'currency': 'AWG'
  },
  'Ascension Island': {
    'abbreviation': 'AC',
    'currency': 'USD'
  },
  'Australia': {
    'abbreviation': 'AU',
    'currency': 'AUD'
  },
  'Austria': {
    'abbreviation': 'AT',
    'currency': 'EUR'
  },
  'Azerbaijan': {
    'abbreviation': 'AZ',
    'currency': 'USD'
  },
  'Bahamas': {
    'abbreviation': 'BS',
    'currency': 'USD'
  },
  'Bahrain': {
    'abbreviation': 'BH',
    'currency': 'BHD'
  },
  'Bangladesh': {
    'abbreviation': 'BD',
    'currency': 'BDT'
  },
  'Barbados': {
    'abbreviation': 'BB',
    'currency': 'BBD'
  },
  'Belarus': {
    'abbreviation': 'BY',
    'currency': 'USD'
  },
  'Belgium': {
    'abbreviation': 'BE',
    'currency': 'EUR'
  },
  'Belize': {
    'abbreviation': 'BZ',
    'currency': 'BZD'
  },
  'Benin': {
    'abbreviation': 'BJ',
    'currency': 'USD'
  },
  'Bermuda': {
    'abbreviation': 'BM',
    'currency': 'USD'
  },
  'Bhutan': {
    'abbreviation': 'BT',
    'currency': 'BTN'
  },
  'Bolivia': {
    'abbreviation': 'BO',
    'currency': 'USD'
  },
  'Bosnia And Herzegowina': {
    'abbreviation': 'BA',
    'currency': 'USD'
  },
  'Botswana': {
    'abbreviation': 'BW',
    'currency': 'BWP'
  },
  'Bouvet Island': {
    'abbreviation': 'BV',
    'currency': 'USD'
  },
  'Brazil': {
    'abbreviation': 'BR',
    'currency': 'BRL'
  },
  'British Indian Ocean Territory': {
    'abbreviation': 'IO',
    'currency': 'USD'
  },
  'Brunei Darussalam': {
    'abbreviation': 'BN',
    'currency': 'BND'
  },
  'Bulgaria': {
    'abbreviation': 'BG',
    'currency': 'BGN'
  },
  'Burkina Faso': {
    'abbreviation': 'BF',
    'currency': 'USD'
  },
  'Burundi': {
    'abbreviation': 'BI',
    'currency': 'USD'
  },
  'Cambodia': {
    'abbreviation': 'KH',
    'currency': 'USD'
  },
  'Cameroon': {
    'abbreviation': 'CM',
    'currency': 'XAF'
  },
  'Canada': {
    'abbreviation': 'CA',
    'currency': 'CAD'
  },
  'Cape Verde': {
    'abbreviation': 'CV',
    'currency': 'USD'
  },
  'Cayman Islands': {
    'abbreviation': 'KY',
    'currency': 'USD'
  },
  'Central African Republic': {
    'abbreviation': 'CF',
    'currency': 'USD'
  },
  'Chad': {
    'abbreviation': 'TD',
    'currency': 'USD'
  },
  'Chile': {
    'abbreviation': 'CL',
    'currency': 'USD'
  },
  'China': {
    'abbreviation': 'CN',
    'currency': 'CNY'
  },
  'Christmas Island': {
    'abbreviation': 'CX',
    'currency': 'USD'
  },
  'Cocos (Keeling) Islands': {
    'abbreviation': 'CC',
    'currency': 'USD'
  },
  'Colombia': {
    'abbreviation': 'CO',
    'currency': 'COP'
  },
  'Comoros': {
    'abbreviation': 'KM',
    'currency': 'USD'
  },
  'Congo': {
    'abbreviation': 'CG',
    'currency': 'USD'
  },
  "Congo, Democratic People's Republic": {
    'abbreviation': 'CD',
    'currency': 'USD'
  },
  'Cook Islands': {
    'abbreviation': 'CK',
    'currency': 'NZD'
  },
  'Costa Rica': {
    'abbreviation': 'CR',
    'currency': 'CRC'
  },
  "Cote d'Ivoire": {
    'abbreviation': 'CI',
    'currency': 'XOF'
  },
  'Croatia (local name: Hrvatska)': {
    'abbreviation': 'HR',
    'currency': 'HRK'
  },
  'Cuba': {
    'abbreviation': 'CU',
    'currency': 'USD'
  },
  'Cyprus': {
    'abbreviation': 'CY',
    'currency': 'EUR'
  },
  'Czech Republic': {
    'abbreviation': 'CZ',
    'currency': 'CZK'
  },
  'Denmark': {
    'abbreviation': 'DK',
    'currency': 'DKK'
  },
  'Djibouti': {
    'abbreviation': 'DJ',
    'currency': 'USD'
  },
  'Dominica': {
    'abbreviation': 'DM',
    'currency': 'XCD'
  },
  'Dominican Republic': {
    'abbreviation': 'DO',
    'currency': 'DOP'
  },
  'East Timor': {
    'abbreviation': 'TP',
    'currency': 'USD'
  },
  'Ecuador': {
    'abbreviation': 'EC',
    'currency': 'USD'
  },
  'Egypt': {
    'abbreviation': 'EG',
    'currency': 'EGP'
  },
  'El Salvador': {
    'abbreviation': 'SV',
    'currency': 'USD'
  },
  'Equatorial Guinea': {
    'abbreviation': 'GQ',
    'currency': 'USD'
  },
  'Eritrea': {
    'abbreviation': 'ER',
    'currency': 'ERN'
  },
  'Estonia': {
    'abbreviation': 'EE',
    'currency': 'EUR'
  },
  'Ethiopia': {
    'abbreviation': 'ET',
    'currency': 'ETB'
  },
  'Falkland Islands (Malvinas)': {
    'abbreviation': 'FK',
    'currency': 'FKP'
  },
  'Faroe Islands': {
    'abbreviation': 'FO',
    'currency': 'DKK'
  },
  'Fiji': {
    'abbreviation': 'FJ',
    'currency': 'FJD'
  },
  'Finland': {
    'abbreviation': 'FI',
    'currency': 'EUR'
  },
  'France': {
    'abbreviation': 'FR',
    'currency': 'EUR'
  },
  'French Guiana': {
    'abbreviation': 'GF',
    'currency': 'EUR'
  },
  'French Polynesia': {
    'abbreviation': 'PF',
    'currency': 'USD'
  },
  'French Southern Territories': {
    'abbreviation': 'TF',
    'currency': 'EUR'
  },
  'Gabon': {
    'abbreviation': 'GA',
    'currency': 'USD'
  },
  'Gambia': {
    'abbreviation': 'GM',
    'currency': 'GMD'
  },
  'Georgia (Sakartvelo)': {
    'abbreviation': 'GE',
    'currency': 'USD'
  },
  'Germany': {
    'abbreviation': 'DE',
    'currency': 'EUR'
  },
  'Ghana': {
    'abbreviation': 'GH',
    'currency': 'GHC'
  },
  'Gibraltar': {
    'abbreviation': 'GI',
    'currency': 'GBP'
  },
  'Greece': {
    'abbreviation': 'GR',
    'currency': 'EUR'
  },
  'Greenland': {
    'abbreviation': 'GL',
    'currency': 'DKK'
  },
  'Grenada': {
    'abbreviation': 'GD',
    'currency': 'XCD'
  },
  'Guadeloupe': {
    'abbreviation': 'GP',
    'currency': 'USD'
  },
  'Guam': {
    'abbreviation': 'GU',
    'currency': 'USD'
  },
  'Guatemala': {
    'abbreviation': 'GT',
    'currency': 'GTQ'
  },
  'Guernsey': {
    'abbreviation': 'GG',
    'currency': 'GBP'
  },
  'Guinea': {
    'abbreviation': 'GN',
    'currency': 'USD'
  },
  'Guinea-Bissau': {
    'abbreviation': 'GW',
    'currency': 'USD'
  },
  'Guyana': {
    'abbreviation': 'GY',
    'currency': 'GYD'
  },
  'Haiti': {
    'abbreviation': 'HT',
    'currency': 'USD'
  },
  'Heard And Mc Donald Islands': {
    'abbreviation': 'HM',
    'currency': 'USD'
  },
  'Honduras': {
    'abbreviation': 'HN',
    'currency': 'HNL'
  },
  'Hong Kong': {
    'abbreviation': 'HK',
    'currency': 'HKD'
  },
  'Hungary': {
    'abbreviation': 'HU',
    'currency': 'HUF'
  },
  'Iceland': {
    'abbreviation': 'IS',
    'currency': 'ISK'
  },
  'India': {
    'abbreviation': 'IN',
    'currency': 'INR'
  },
  'Indonesia': {
    'abbreviation': 'ID',
    'currency': 'IDR'
  },
  'Iran (Islamic Republic Of)': {
    'abbreviation': 'IR',
    'currency': 'IRR'
  },
  'Iraq': {
    'abbreviation': 'IQ',
    'currency': 'USD'
  },
  'Ireland': {
    'abbreviation': 'IE',
    'currency': 'EUR'
  },
  'Isle of Man': {
    'abbreviation': 'IM',
    'currency': 'GBP'
  },
  'Israel': {
    'abbreviation': 'IL',
    'currency': 'ILS'
  },
  'Italy': {
    'abbreviation': 'IT',
    'currency': 'EUR'
  },
  'Jamaica': {
    'abbreviation': 'JM',
    'currency': 'JMD'
  },
  'Japan': {
    'abbreviation': 'JP',
    'currency': 'JPY'
  },
  'Jersey (Island)': {
    'abbreviation': 'JE',
    'currency': 'GBP'
  },
  'Jordan': {
    'abbreviation': 'JO',
    'currency': 'JOD'
  },
  'Kazakhstan': {
    'abbreviation': 'KZ',
    'currency': 'USD'
  },
  'Kenya': {
    'abbreviation': 'KE',
    'currency': 'KES'
  },
  'Kiribati': {
    'abbreviation': 'KI',
    'currency': 'USD'
  },
  "Korea, Democratic People's Republic Of": {
    'abbreviation': 'KP',
    'currency': 'USD'
  },
  'Korea, Republic Of': {
    'abbreviation': 'KR',
    'currency': 'USD'
  },
  'Kuwait': {
    'abbreviation': 'KW',
    'currency': 'KWD'
  },
  'Kyrgyzstan': {
    'abbreviation': 'KG',
    'currency': 'USD'
  },
  "Lao People's Democratic Republic": {
    'abbreviation': 'LA',
    'currency': 'USD'
  },
  'Latvia': {
    'abbreviation': 'LV',
    'currency': 'USD'
  },
  'Lebanon': {
    'abbreviation': 'LB',
    'currency': 'LBP'
  },
  'Lesotho': {
    'abbreviation': 'LS',
    'currency': 'ZAR'
  },
  'Liberia': {
    'abbreviation': 'LR',
    'currency': 'USD'
  },
  'Libyan Arab Jamahiriya': {
    'abbreviation': 'LY',
    'currency': 'USD'
  },
  'Liechtenstein': {
    'abbreviation': 'LI',
    'currency': 'CHF'
  },
  'Lithuania': {
    'abbreviation': 'LT',
    'currency': 'EUR'
  },
  'Luxembourg': {
    'abbreviation': 'LU',
    'currency': 'EUR'
  },
  'Macau': {
    'abbreviation': 'MO',
    'currency': 'MOP'
  },
  'Macedonia, The Former Yugoslav Republic Of': {
    'abbreviation': 'MK',
    'currency': 'MKD'
  },
  'Madagascar': {
    'abbreviation': 'MG',
    'currency': 'USD'
  },
  'Malawi': {
    'abbreviation': 'MW',
    'currency': 'MWK'
  },
  'Malaysia': {
    'abbreviation': 'MY',
    'currency': 'MYR'
  },
  'Maldives': {
    'abbreviation': 'MV',
    'currency': 'USD'
  },
  'Mali': {
    'abbreviation': 'ML',
    'currency': 'USD'
  },
  'Malta': {
    'abbreviation': 'MT',
    'currency': 'EUR'
  },
  'Marshall Islands': {
    'abbreviation': 'MH',
    'currency': 'USD'
  },
  'Martinique': {
    'abbreviation': 'MQ',
    'currency': 'EUR'
  },
  'Mauritania': {
    'abbreviation': 'MR',
    'currency': 'USD'
  },
  'Mauritius': {
    'abbreviation': 'MU',
    'currency': 'MUR'
  },
  'Mayotte': {
    'abbreviation': 'YT',
    'currency': 'EUR'
  },
  'Mexico': {
    'abbreviation': 'MX',
    'currency': 'MXN'
  },
  'Micronesia, Federated States Of': {
    'abbreviation': 'FM',
    'currency': 'USD'
  },
  'Moldova, Republic Of': {
    'abbreviation': 'MD',
    'currency': 'USD'
  },
  'Monaco': {
    'abbreviation': 'MC',
    'currency': 'EUR'
  },
  'Mongolia': {
    'abbreviation': 'MN',
    'currency': 'USD'
  },
  'Montserrat': {
    'abbreviation': 'MS',
    'currency': 'XCD'
  },
  'Morocco': {
    'abbreviation': 'MA',
    'currency': 'MAD'
  },
  'Mozambique': {
    'abbreviation': 'MZ',
    'currency': 'USD'
  },
  'Myanmar': {
    'abbreviation': 'MM',
    'currency': 'USD'
  },
  'Namibia': {
    'abbreviation': 'NA',
    'currency': 'NAD'
  },
  'Nauru': {
    'abbreviation': 'NR',
    'currency': 'AUD'
  },
  'Nepal': {
    'abbreviation': 'NP',
    'currency': 'NPR'
  },
  'Netherlands': {
    'abbreviation': 'NL',
    'currency': 'EUR'
  },
  'Netherlands Antilles': {
    'abbreviation': 'AN',
    'currency': 'ANG'
  },
  'New Caledonia': {
    'abbreviation': 'NC',
    'currency': 'USD'
  },
  'New Zealand': {
    'abbreviation': 'NZ',
    'currency': 'NZD'
  },
  'Nicaragua': {
    'abbreviation': 'NI',
    'currency': 'NIO'
  },
  'Niger': {
    'abbreviation': 'NE',
    'currency': 'USD'
  },
  'Nigeria': {
    'abbreviation': 'NG',
    'currency': 'NGN'
  },
  'Niue': {
    'abbreviation': 'NU',
    'currency': 'NZD'
  },
  'Norfolk Island': {
    'abbreviation': 'NF',
    'currency': 'AUD'
  },
  'Northern Mariana Islands': {
    'abbreviation': 'MP',
    'currency': 'USD'
  },
  'Norway': {
    'abbreviation': 'NO',
    'currency': 'NOK'
  },
  'Oman': {
    'abbreviation': 'OM',
    'currency': 'OMR'
  },
  'Pakistan': {
    'abbreviation': 'PK',
    'currency': 'PKR'
  },
  'Palau': {
    'abbreviation': 'PW',
    'currency': 'USD'
  },
  'Palestinian Territories': {
    'abbreviation': 'PS',
    'currency': 'USD'
  },
  'Panama': {
    'abbreviation': 'PA',
    'currency': 'PAB'
  },
  'Papua New Guinea': {
    'abbreviation': 'PG',
    'currency': 'PGK'
  },
  'Paraguay': {
    'abbreviation': 'PY',
    'currency': 'PYG'
  },
  'Peru': {
    'abbreviation': 'PE',
    'currency': 'USD'
  },
  'Philippines': {
    'abbreviation': 'PH',
    'currency': 'PHP'
  },
  'Pitcairn': {
    'abbreviation': 'PN',
    'currency': 'USD'
  },
  'Poland': {
    'abbreviation': 'PL',
    'currency': 'PLN'
  },
  'Portugal': {
    'abbreviation': 'PT',
    'currency': 'EUR'
  },
  'Puerto Rico': {
    'abbreviation': 'PR',
    'currency': 'USD'
  },
  'Qatar': {
    'abbreviation': 'QA',
    'currency': 'QAR'
  },
  'Reunion': {
    'abbreviation': 'RE',
    'currency': 'USD'
  },
  'Romania': {
    'abbreviation': 'RO',
    'currency': 'USD'
  },
  'Russia': {
    'abbreviation': 'RU',
    'currency': 'RUB'
  },
  'Rwanda': {
    'abbreviation': 'RW',
    'currency': 'USD'
  },
  'Saint Kitts And Nevis': {
    'abbreviation': 'KN',
    'currency': 'XCD'
  },
  'Saint Lucia': {
    'abbreviation': 'LC',
    'currency': 'XCD'
  },
  'Saint Vincent And The Grenadines': {
    'abbreviation': 'VC',
    'currency': 'XCD'
  },
  'Samoa': {
    'abbreviation': 'WS',
    'currency': 'WST'
  },
  'San Marino': {
    'abbreviation': 'SM',
    'currency': 'EUR'
  },
  'Sao Tome And Principe': {
    'abbreviation': 'ST',
    'currency': 'USD'
  },
  'Saudi Arabia': {
    'abbreviation': 'SA',
    'currency': 'SAR'
  },
  'Senegal': {
    'abbreviation': 'SN',
    'currency': 'USD'
  },
  'Serbia and Montenegro': {
    'abbreviation': 'CS',
    'currency': 'EUR'
  },
  'Seychelles': {
    'abbreviation': 'SC',
    'currency': 'USD'
  },
  'Sierra Leone': {
    'abbreviation': 'SL',
    'currency': 'USD'
  },
  'Singapore': {
    'abbreviation': 'SG',
    'currency': 'SGD'
  },
  'Slovakia (Slovak Republic)': {
    'abbreviation': 'SK',
    'currency': 'USD'
  },
  'Slovenia': {
    'abbreviation': 'SI',
    'currency': 'EUR'
  },
  'Solomon Islands': {
    'abbreviation': 'SB',
    'currency': 'SBD'
  },
  'Somalia': {
    'abbreviation': 'SO',
    'currency': 'USD'
  },
  'South Africa': {
    'abbreviation': 'ZA',
    'currency': 'ZAR'
  },
  'South Georgia And The South Sandwich Islands': {
    'abbreviation': 'GS',
    'currency': 'USD'
  },
  'Spain': {
    'abbreviation': 'ES',
    'currency': 'EUR'
  },
  'Sri Lanka': {
    'abbreviation': 'LK',
    'currency': 'LKR'
  },
  'St. Helena': {
    'abbreviation': 'SH',
    'currency': 'USD'
  },
  'St. Pierre And Miquelon': {
    'abbreviation': 'PM',
    'currency': 'USD'
  },
  'Sudan': {
    'abbreviation': 'SD',
    'currency': 'USD'
  },
  'Suriname': {
    'abbreviation': 'SR',
    'currency': 'USD'
  },
  'Svalbard And Jan Mayen Islands': {
    'abbreviation': 'SJ',
    'currency': 'USD'
  },
  'Swaziland': {
    'abbreviation': 'SZ',
    'currency': 'SZL'
  },
  'Sweden': {
    'abbreviation': 'SE',
    'currency': 'SEK'
  },
  'Switzerland': {
    'abbreviation': 'CH',
    'currency': 'CHF'
  },
  'Syrian Arab Republic': {
    'abbreviation': 'SY',
    'currency': 'SYP'
  },
  'Taiwan': {
    'abbreviation': 'TW',
    'currency': 'TWD'
  },
  'Tajikistan': {
    'abbreviation': 'TJ',
    'currency': 'USD'
  },
  'Tanzania, United Republic Of': {
    'abbreviation': 'TZ',
    'currency': 'TZS'
  },
  'Thailand': {
    'abbreviation': 'TH',
    'currency': 'THB'
  },
  'Togo': {
    'abbreviation': 'TG',
    'currency': 'USD'
  },
  'Tokelau': {
    'abbreviation': 'TK',
    'currency': 'USD'
  },
  'Tonga': {
    'abbreviation': 'TO',
    'currency': 'TOP'
  },
  'Trinidad And Tobago': {
    'abbreviation': 'TT',
    'currency': 'TTD'
  },
  'Tunisia': {
    'abbreviation': 'TN',
    'currency': 'TND'
  },
  'Turkey': {
    'abbreviation': 'TR',
    'currency': 'USD'
  },
  'Turkmenistan': {
    'abbreviation': 'TM',
    'currency': 'USD'
  },
  'Turks And Caicos Islands': {
    'abbreviation': 'TC',
    'currency': 'USD'
  },
  'Tuvalu': {
    'abbreviation': 'TV',
    'currency': 'USD'
  },
  'U.S. Minor Outlying Islands': {
    'abbreviation': 'UM',
    'currency': 'USD'
  },
  'Uganda': {
    'abbreviation': 'UG',
    'currency': 'UGX'
  },
  'Ukraine': {
    'abbreviation': 'UA',
    'currency': 'USD'
  },
  'United Arab Emirates': {
    'abbreviation': 'AE',
    'currency': 'AED'
  },
  'United Kingdom': {
    'abbreviation': 'GB',
    'currency': 'GBP'
  },
  'United States': {
    'abbreviation': 'US',
    'currency': 'USD'
  },
  'Uruguay': {
    'abbreviation': 'UY',
    'currency': 'USD'
  },
  'Uzbekistan': {
    'abbreviation': 'UZ',
    'currency': 'USD'
  },
  'Vanuatu': {
    'abbreviation': 'VU',
    'currency': 'VUV'
  },
  'Vatican City State (Holy See)': {
    'abbreviation': 'VA',
    'currency': 'USD'
  },
  'Venezuela': {
    'abbreviation': 'VE',
    'currency': 'VEB'
  },
  'Viet Nam': {
    'abbreviation': 'VN',
    'currency': 'USD'
  },
  'Virgin Islands (British)': {
    'abbreviation': 'VG',
    'currency': 'USD'
  },
  'Virgin Islands (U.S.)': {
    'abbreviation': 'VI',
    'currency': 'USD'
  },
  'Wallis And Futuna Islands': {
    'abbreviation': 'WF',
    'currency': 'USD'
  },
  'Western Sahara': {
    'abbreviation': 'EH',
    'currency': 'USD'
  },
  'Yemen': {
    'abbreviation': 'YE',
    'currency': 'USD'
  },
  'Zambia': {
    'abbreviation': 'ZM',
    'currency': 'USD'
  },
  'Zimbabwe': {
    'abbreviation': 'ZW',
    'currency': 'USD'
  }
});

/***/ }),

/***/ "./public/js/overriden-gf-functions.js":
/*!*********************************************!*\
  !*** ./public/js/overriden-gf-functions.js ***!
  \*********************************************/
/***/ (() => {

jQuery(function () {
  /**
   * Overriding GravityForms' function because we want to change the output of this function.
   * When product's prices are calculated, this function is called. 
   * This need to change the output of this to fix the problem where value of the total is 
   * incorrect because of curreny format difference in EUR.
   * 
   * Todo: ask GravityForms to add a filter to the output of this function and get rid of this copy.
   * 
   * @param {int} formId         Form ID.
   * @param {int} productFieldId Product Field ID
   * @returns {float} Bade Price of the given product ID. 
  */
  gformGetBasePrice = function (formId, productFieldId) {
    var suffix = "_" + formId + "_" + productFieldId;
    var price = 0;
    var productField = jQuery("#ginput_base_price" + suffix + ", .gfield_donation" + suffix + " input[type=\"text\"], .gfield_product" + suffix + " .ginput_amount");
    if (productField.length > 0) {
      price = productField.val();

      //If field is hidden by conditional logic, don't count it for the total
      if (gformIsHidden(productField)) {
        price = 0;
      }
    } else {
      productField = jQuery(".gfield_product" + suffix + " select, .gfield_product" + suffix + " input:checked, .gfield_donation" + suffix + " select, .gfield_donation" + suffix + " input:checked");
      var val = productField.val();
      if (val) {
        val = val.split("|");
        price = val.length > 1 ? val[1] : 0;
      }

      //If field is hidden by conditional logic, don't count it for the total
      if (gformIsHidden(productField)) price = 0;
    }
    var c = new Currency(gf_global.gf_currency_config);
    price = c.toNumber(price);
    price === false ? 0 : price;
    return gform.applyFilters('gform_get_base_price', price, formId, productFieldId);
  };
  gformGetShippingPrice = function (formId) {
    var shippingField = jQuery(".gfield_shipping_" + formId + " input[readonly], .gfield_shipping_" + formId + " select, .gfield_shipping_" + formId + " input:checked");
    var shipping = 0;
    if (shippingField.length == 1 && !gformIsHidden(shippingField)) {
      if (shippingField.attr("readonly")) shipping = shippingField.val();else shipping = gformGetPrice(shippingField.val());
    }
    return gform.applyFilters('gform_get_shipping_price', gformToNumber(shipping), formId);
  };
});

/***/ })

/******/ 	});
/************************************************************************/
/******/ 	// The module cache
/******/ 	var __webpack_module_cache__ = {};
/******/ 	
/******/ 	// The require function
/******/ 	function __webpack_require__(moduleId) {
/******/ 		// Check if module is in cache
/******/ 		var cachedModule = __webpack_module_cache__[moduleId];
/******/ 		if (cachedModule !== undefined) {
/******/ 			return cachedModule.exports;
/******/ 		}
/******/ 		// Create a new module (and put it into the cache)
/******/ 		var module = __webpack_module_cache__[moduleId] = {
/******/ 			// no module.id needed
/******/ 			// no module.loaded needed
/******/ 			exports: {}
/******/ 		};
/******/ 	
/******/ 		// Execute the module function
/******/ 		__webpack_modules__[moduleId](module, module.exports, __webpack_require__);
/******/ 	
/******/ 		// Return the exports of the module
/******/ 		return module.exports;
/******/ 	}
/******/ 	
/************************************************************************/
/******/ 	/* webpack/runtime/compat get default export */
/******/ 	(() => {
/******/ 		// getDefaultExport function for compatibility with non-harmony modules
/******/ 		__webpack_require__.n = (module) => {
/******/ 			var getter = module && module.__esModule ?
/******/ 				() => (module['default']) :
/******/ 				() => (module);
/******/ 			__webpack_require__.d(getter, { a: getter });
/******/ 			return getter;
/******/ 		};
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/define property getters */
/******/ 	(() => {
/******/ 		// define getter functions for harmony exports
/******/ 		__webpack_require__.d = (exports, definition) => {
/******/ 			for(var key in definition) {
/******/ 				if(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {
/******/ 					Object.defineProperty(exports, key, { enumerable: true, get: definition[key] });
/******/ 				}
/******/ 			}
/******/ 		};
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/hasOwnProperty shorthand */
/******/ 	(() => {
/******/ 		__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/make namespace object */
/******/ 	(() => {
/******/ 		// define __esModule on exports
/******/ 		__webpack_require__.r = (exports) => {
/******/ 			if(typeof Symbol !== 'undefined' && Symbol.toStringTag) {
/******/ 				Object.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });
/******/ 			}
/******/ 			Object.defineProperty(exports, '__esModule', { value: true });
/******/ 		};
/******/ 	})();
/******/ 	
/************************************************************************/
var __webpack_exports__ = {};
// This entry need to be wrapped in an IIFE because it need to be in strict mode.
(() => {
"use strict";
/*!***************************!*\
  !*** ./public/js/main.js ***!
  \***************************/
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _overriden_gf_functions__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./overriden-gf-functions */ "./public/js/overriden-gf-functions.js");
/* harmony import */ var _overriden_gf_functions__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_overriden_gf_functions__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var _countries__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./countries */ "./public/js/countries.js");
/* harmony import */ var _compat_conditional_pricing__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./compat-conditional-pricing */ "./public/js/compat-conditional-pricing.js");
/* harmony import */ var _calculation_field__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./calculation-field */ "./public/js/calculation-field.js");
/* harmony import */ var _compat_paypal_express__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./compat-paypal-express */ "./public/js/compat-paypal-express.js");
/* harmony import */ var _compat_stripe__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./compat-stripe */ "./public/js/compat-stripe.js");






var $ = jQuery;
$(document).ready(function () {
  _calculation_field__WEBPACK_IMPORTED_MODULE_3__["default"].init();
  _compat_conditional_pricing__WEBPACK_IMPORTED_MODULE_2__["default"].init();
  _compat_paypal_express__WEBPACK_IMPORTED_MODULE_4__["default"].init();
  _compat_stripe__WEBPACK_IMPORTED_MODULE_5__["default"].init();
});
var mcg = {
  /**
   * Data structure:
   * 1. window.idea_mcg:
   * 	- all_currencies
   * 	- exchange_rate
   * 2. window.mcg_form_{form_id}
   */

  /**
   * On ready.
   */
  on_ready: function () {
    if (!idea_mcg.exchange_rate || $('body').hasClass('wp-admin')) {
      return;
    }
    $(document.body).trigger('mcg_before_ready');
    mcg.handle_currency_field_change();
    gform.addFilter('gform_get_base_price', mcg.filter_gform_get_base_price);
    gform.addFilter('gform_get_shipping_price', mcg.filter_gform_get_shipping_price);
    $(".gform_wrapper .ginput_container_multicurrency select").trigger('change');
    mcg.set_currency_based_on_visitor_ip();
    gform.addAction('gform_input_change', mcg.save_option_value_in_localstorage);

    // Stripe compatibility.
    gform.addFilter('gform_stripe_currency', mcg.stripe_change_currency);

    // Coupon compatibility.
    gform.addFilter('gform_coupons_discount_amount', mcg.flat_coupon_currency_convert);
  },
  /**
   * Get form's payment processing currency.
   * 
   * @param {int} form_id Form ID.
   */
  get_form_payment_processing_currency: function (form_id) {
    return window['mcg_form_' + form_id] ? window['mcg_form_' + form_id]['payment_processing_currency'] : false;
  },
  handle_currency_field_change: function () {
    $(".gform_wrapper .ginput_container_multicurrency select").change(function () {
      mcg.on_currency_change($(this));
    });
    jQuery(document).on('gform_page_loaded', function () {
      $(".gform_wrapper .ginput_container_multicurrency select").change(function () {
        mcg.on_currency_change($(this));
      });
    });
  },
  on_currency_change: function ($select) {
    var new_currency = $select.val();
    var form_id = mcg.get_form_id($select);
    var form_data = window['mcg_form_' + form_id];
    var mode = mcg.getMode(form_id);
    var old_currency = form_data.current_currency ? form_data.current_currency : form_data.base_currency;
    form_data.current_currency = new_currency;

    // Update global currency object.
    mcg.update_gf_global_currency(new_currency, old_currency);

    // Update Each Product Price.
    if ('automatic' === mode) {
      mcg.update_product_field_price_automatic(form_id, new_currency);
      mcg.update_shipping_field_price_automatic(form_id, old_currency, new_currency);
    } else {
      mcg.update_product_field_price_manual(form_id, old_currency, new_currency);
      mcg.update_shipping_field_price_manual(form_id, old_currency, new_currency);
    }
  },
  /**
   * Get form ID from any inner element.
   *
   * @param {jQuery} $el Any inner element.
   */
  get_form_id: function ($el) {
    var $gform_wrapper = $el.closest(".gform_wrapper");
    if (!$gform_wrapper) return false;
    var dom_id = $gform_wrapper.attr("id");
    if (!dom_id) return false;
    var form_id = dom_id.replace('gform_wrapper_', '');
    return form_id;
  },
  /**
   * Update gf_global.gf_currency_config currency.
   * 
   * @param {string} currency Currency.
   */
  update_gf_global_currency: function (new_currency, old_currency) {
    gf_global.gf_currency_config = idea_mcg.all_currencies[new_currency];
    $(".gform_wrapper input").trigger('change');
  },
  /**
   * Update the price which is shown to the user.
   *
   * @param int formId 
   * @param string old_currency 
   * @param string new_currency 
   */
  update_product_field_price_automatic: function (form_id, new_currency) {
    $("#gform_wrapper_" + form_id).find('.gfield_price').each(function () {
      if ($(this).hasClass('gfield--type-total') || $(this).hasClass('gfield--input-type-price')) {
        return;
      }
      if ($(this).hasClass('gfield--input-type-select') || $(this).hasClass('gfield--input-type-radio')) {
        mcg.update_select_fields_automatic($(this), form_id);
        return;
      }
      var form_base_currency = window['mcg_form_' + form_id].base_currency;
      var new_currency_config = idea_mcg.all_currencies[new_currency];
      var new_currency_obj = new Currency(new_currency_config);

      // if its a calculation field.
      if ($(this).hasClass('gfield--input-type-calculation')) {
        var formatted_price = $(this).find('.ginput_product_price').text();
        var flatten_price = mcg.toNumber(formatted_price, true);
        var price = mcg.flatToCurrentCurrency(flatten_price, form_id, false);
        $(this).find('.mcg_ginput_product_price').text(price);
        return;
      }
      var flattened_price = $(this).find('[data-mcg-base-price]').data('mcg-base-price');
      var amt_in_number = mcg.unflatten(flattened_price);
      amt_in_number = mcg.convert_currency(form_base_currency, new_currency, amt_in_number);
      amt_in_number = Math.round(amt_in_number * 100) / 100; // round to 2 decimal places.
      var new_amt_formatted = new_currency_obj.toMoney(amt_in_number, true);
      _mcg.setPrice($(this), new_amt_formatted);
    });
  },
  flat_coupon_currency_convert: function (discount, couponType, couponAmount, price, totalDiscount) {
    var form_id = mcg.get_form_id($(".gf_coupon_code"));
    var form_data = window['mcg_form_' + form_id];
    var new_currency = $(".gform_wrapper .ginput_container_multicurrency select").val();
    var old_currency = form_data.base_currency;
    if (new_currency != old_currency) {
      if ('flat' == couponType) {
        var new_discount = mcg.convert_currency(old_currency, new_currency, discount);
        return new_discount;
      } else if ('percentage' == couponType) {
        var ship_price = gformGetShippingPrice(form_id);
        ship_price = mcg.convert_currency(old_currency, new_currency, ship_price);
        price = price - ship_price;
        discount = price * Number(couponAmount / 100);
        return discount;
      }
    } else {
      return discount;
    }
  },
  /**
   * Update the price which is shown to the user.
   *
   * @param int formId 
   * @param string old_currency 
   * @param string new_currency 
   */
  update_product_field_price_manual: function (formId, old_currency, new_currency) {
    $('#gform_wrapper_' + formId).find('.gfield_price').each(function () {
      var html_id = $(this).attr('id');
      let field_id = gf_get_input_id_by_html_id(html_id);
      var price = mcg.product_get_manual_price_in_current_currency(formId, field_id);
      var currencyObj = new Currency(gf_global.gf_currency_config);
      $(this).find('.ginput_product_price').html(currencyObj.toMoney(price));
      $(this).find('input.ginput_product_price').val(currencyObj.toMoney(price));
      $(this).find(`#ginput_base_price_${formId}_${field_id}`).val(currencyObj.toMoney(price));
    });
  },
  /**
   * Update the shipping price span - which is shown to the user.
   *
   * @param int formId 
   * @param string old_currency
   * @param string new_currency 
   */
  update_shipping_field_price_manual: function (formId, old_currency, new_currency) {
    var $shipping_price_input = $('#gform_wrapper_' + formId).find('.ginput_shipping_price');
    var shiping_rates = window['mcg_form_' + formId]['manual_shipping'];
    if (!$shipping_price_input || !shiping_rates) {
      return;
    }
    if (typeof shiping_rates[new_currency] === 'undefined') {
      return;
    }
    var currencyObj = new Currency(gf_global.gf_currency_config);
    $shipping_price_input.val(currencyObj.toMoney(shiping_rates[new_currency]));
  },
  /**
   * Update values of the dropdown and radio fields.
   *
   * @param {jQuery} $field Field jQuery object.
   * @param {int}    formId Form ID.
   */
  update_select_fields_automatic: function ($field, formId) {
    if ($field.hasClass('gfield--type-quantity')) {
      return;
    }
    $field.find('option, input[type=radio]').each(function () {
      var flat_amount_in_base_currency = $(this).data('mcg-base-price');
      var base_currency = window['mcg_form_' + formId]['base_currency'];
      var current_currency = window['mcg_form_' + formId]['current_currency'];
      var new_currency_obj = new Currency(idea_mcg["all_currencies"][current_currency]);
      var new_amt = mcg.convert_currency(base_currency, current_currency, mcg.unflatten(flat_amount_in_base_currency));
      var new_amt_number = new_currency_obj.toNumber(new_amt);
      new_amt_number = Math.round(new_amt_number * 100) / 100; // round to 2 decimal places.

      // option has value is this format: 'Choice1|1,00.00', replace all text after '|' with new_amt.
      var new_option_value = $(this).val().split('|')[0] + '|' + new_amt_number;
      $(this).val(new_option_value);
    });
  },
  /**
   * Update the shipping price span and input values.
   *
   * @param int formId 
   * @param string old_currency
   * @param string new_currency 
   */
  update_shipping_field_price_automatic: function (formId, old_currency, new_currency) {
    $('.gfield--type-shipping').each(function () {
      if ($(this).hasClass('gfield--input-type-singleshipping')) {
        var flat_amount_in_base_currency = $(this).find('[data-mcg-base-price]').data('mcg-base-price');
        var new_val = mcg.flatToCurrentCurrency(flat_amount_in_base_currency, formId, false);
        $(this).find('[data-mcg-base-price]').val(new_val);
      } else if ($(this).hasClass('gfield--input-type-select') || $(this).hasClass('gfield--input-type-radio')) {
        mcg.update_select_fields_automatic($(this), formId);
      }
    });
  },
  /**
   * Convert currency.
   * 
   * @param {string} from  From Currency.
   * @param {string} to    To Currency.
   * @param {total}  total Amount to be converted.
   */
  convert_currency: function (from, to, total) {
    if (isNaN(total)) {
      var from_currency = new Currency(idea_mcg["all_currencies"][from]);
      total = from_currency.toNumber(total);
    }
    if ('USD' === to) {
      return total / idea_mcg.exchange_rate.rates[from];
    }

    // Convert to USD
    return total * idea_mcg.exchange_rate.rates[to] / idea_mcg.exchange_rate.rates[from];
  },
  /**
   * Get currency code from Currency name.
   * Example: for name 'Australian Dollar' output will be 'AUD'.
   * 
   * @param {string} name Currency Name
   */
  find_currency_code_by_name: function (name) {
    for (var i in idea_mcg.all_currencies) {
      var currency = idea_mcg.all_currencies[i];
      if (currency.name === name) {
        return i;
      }
    }
  },
  /**
   * Get user define product price.
   * @param int form_id 
   * @param string current_currency 
   */
  get_user_define_price: function (form_id, current_currency) {
    var amt = 0;
    $("#gform_wrapper_" + form_id).find('.gfield_price').each(function () {
      var old_amt = 0;
      if ($(this).find('.ginput_container_product_price input.ginput_amount').length) {
        var old_currency_config = idea_mcg.all_currencies[current_currency];
        var existing_html_price = $(this).find('.ginput_container_product_price input.ginput_amount').val();
        var old_currency_obj = new Currency(old_currency_config);
        old_amt = old_currency_obj.toNumber(existing_html_price);
      }
      amt = amt + old_amt;
    });
    return amt;
  },
  // -----------------------------------------
  // ------------- MANUAL MODE----------------
  // -----------------------------------------

  gformCalculateProductPrice: function (form_id, productFieldId) {
    var price = mcg.gformGetBasePrice_manual(form_id, productFieldId);
    var quantity = gformGetProductQuantity(form_id, productFieldId);

    //calculating options if quantity is more than 0 (a product was selected).
    if (quantity > 0) {
      //setting global variable if quantity is more than 0 (a product was selected). Will be used when calculating total
      _anyProductSelected = true;
    }
    price = price * quantity;
    price = gformRoundPrice(price);
    return price;
  },
  gformGetBasePrice_manual: function (form_id, productFieldId) {
    var suffix = "_" + form_id + "_" + productFieldId;
    var price = 0;
    var productField = jQuery("#ginput_base_price" + suffix + ", .gfield_donation" + suffix + " input[type=\"text\"], .gfield_product" + suffix + " .ginput_amount");
    if (productField.length > 0) {
      price = mcg.product_get_manual_price_in_current_currency(form_id, productFieldId);

      //If field is hidden by conditional logic, don't count it for the total
      if (gformIsHidden(productField)) {
        price = 0;
      }
    }
    return price;
  },
  getManualShipping: function (form_id) {
    var manual_shipping = window['mcg_form_' + form_id]['manual_shipping'];
    var current_currency = window['mcg_form_' + form_id]['current_currency'];
    var shipping = manual_shipping ? manual_shipping[current_currency] : 0;
    return shipping;
  },
  /**
   * Return manual price for the given product field ID.
   * 
   * @param {int} form_id        Form ID.
   * @param {int} productFieldId Product Field ID.
   */
  product_get_manual_price_in_current_currency: function (form_id, productFieldId) {
    // var flattened_price = $( this ).find( '[id^=ginput_base_price_]' ).data( 'mcg-base-price' );
    var product_manual_price = window['mcg_form_' + form_id]['manual_prices'][productFieldId];
    var current_currency = window['mcg_form_' + form_id]['current_currency'];
    var price = product_manual_price ? product_manual_price[current_currency] : 0;
    return price;
  },
  /**
   * Change currency for stripe.
   *
   * @param {string} old_currency Old Currency before conversion by MCG.
   * @param {int}    form_id      Form ID.
   */
  stripe_change_currency: function (old_currency, form_id) {
    // If multi-currency field exists then return the selected currency.
    if ($('#gform_' + form_id + ' .ginput_container_multicurrency').length) {
      return $('#gform_' + form_id + ' .ginput_container_multicurrency').find('select').val();
    } else {
      return old_currency;
    }
  },
  /**
   * Set country based on customer's country.
   */
  set_currency_based_on_visitor_ip: function () {
    if (mcg.is_resumed_page_load() || gform.applyFilters('mcg_skip_geo_ip_currency', false)) {
      return;
    }
    $(".gform_wrapper").each(function () {
      var form_id = mcg.get_form_id($(this));
      if (!form_id) return;
      var is_geo_auto_currency = window['mcg_form_' + form_id].is_geo_auto_currency;
      if (1 != is_geo_auto_currency) {
        return;
      }
      var country_code = false;
      try {
        country_code = localStorage.getItem('mcg_country_code');
      } catch (err) {}

      // Fetch country code if not in cache.
      if (!country_code) {
        jQuery.get("https://api.ipregistry.co/?key=tryout").done(function (data) {
          if (!data) {
            return;
          }
          var country_code = data?.location?.country?.code;
          mcg.set_currency_from_country_code(country_code);
          try {
            localStorage.setItem('mcg_country_code', country_code);
            $(window).trigger('mcg_country_code_set');
          } catch (err) {
            console.log("Can't set localstorage");
          }
        });
      } else {
        mcg.set_currency_from_country_code(country_code);
      }
    });
  },
  /**
   * Select the currency from the country code passed. 
   * Example: If IN is passed as country_code, it will select INR currency.
   * 
   * @param {string} country_code Country Code example: IN.
   */
  set_currency_from_country_code: function (country_code) {
    jQuery.each(_countries__WEBPACK_IMPORTED_MODULE_1__["default"], function (key, val) {
      if (val.abbreviation == country_code) {
        jQuery(".ginput_container_multicurrency select").find("option[value=" + val.currency + "]").prop("selected", true).change();
      }
    });
  },
  /**
   * If there is a valudation error or its a resumed submission then return true.
   *
   * @returns bool.
   */
  is_resumed_page_load: function () {
    return undefined !== jQuery(".ginput_container_multicurrency select").attr('data-resumed');
  },
  /**
   * Convert decimal and thousand seperator when currency changes.
   * Example: If we are going from USD to EUR, we need to change 
   * all commas to dots and all dots to commas.
   * 
   * @param string money          Amount to be converted.
   * @param object from_currency  From Currency object.
   * @param object to_currency    To Currency object.
   */
  convert_currency_format: function (money, from_currency, to_currency) {
    if (typeof from_currency === 'string') {
      from_currency = new Currency(idea_mcg["all_currencies"][from_currency]);
    }
    if (typeof to_currency === 'string') {
      to_currency = new Currency(idea_mcg["all_currencies"][to_currency]);
    }
    var number = from_currency.toNumber(money);
    if (',' === to_currency.currency.decimal_separator) {
      number = new String(number);
      number = number.replace('.', ',');
    }
    return to_currency.toMoney(number);
  },
  filter_gform_get_base_price: function (price, formId, productFieldId) {
    var suffix = "_" + formId + "_" + productFieldId;
    var $field = $('#field' + suffix);
    // if its a user defined product price, dont change anything.
    if ($field.hasClass('gfield--input-type-price')) {
      return price;
    }

    // convert price to current price.
    if (formId && window['mcg_form_' + formId]) {
      if ('manual' === window['mcg_form_' + formId].mode) {
        var price = mcg.product_get_manual_price_in_current_currency(formId, productFieldId);
        return parseFloat(price);
      } else {
        var price = mcg.get_product_price_flattened($(`.gfield_product${suffix}`));
        var current_currency = mcg.get_current_currency(formId);
        var base_currency = window['mcg_form_' + formId].base_currency;
        price = mcg.convert_currency(base_currency, current_currency, mcg.unflatten(price));
      }
      return parseFloat(price);
    }
    return mcg.unflatten(price);
  },
  filter_gform_get_shipping_price: function (shipping_price, formId) {
    var shippingField = jQuery(".gfield_shipping_" + formId + " input[readonly], .gfield_shipping_" + formId + " select, .gfield_shipping_" + formId + " input:checked");
    var shipping = 0;
    var mode = mcg.getMode(formId);
    if (shippingField.length == 1 && !gformIsHidden(shippingField)) {
      if ('manual' === mode) {
        shipping = mcg.getManualShipping(formId);
      } else {
        var $el = $(`#gform_8 .gfield_shipping`).find('option:selected, input[type=radio]:checked, input[type=text].ginput_shipping_price');
        shipping = mcg.unflatten($el.data('mcg-base-price'));
        shipping = mcg.convert_currency(window['mcg_form_' + formId].base_currency, mcg.get_current_currency(formId), shipping);
      }
    }
    if (isNaN(shipping)) {
      return 0;
    }

    // max 2 decimal places.
    shipping = Math.round(shipping * 100) / 100;
    return parseFloat(shipping);
  },
  /**
   * Returns the flattened price of the product. In case of the radio/dropdown field, it returns the price of the selected option.
   * 
   * @param {int} price 
   */
  get_product_price_flattened: function ($product_field) {
    var price = false;
    $product_field = $product_field.filter(':not(.gfield--type-quantity)');

    // if there are multiple fields and one of them is `.gfield--type-option` then use that.
    if ($product_field.length > 1 && $product_field.hasClass('gfield--type-option')) {
      $product_field = $product_field.filter('.gfield--type-option');
    }
    if ($product_field.hasClass('gfield--input-type-radio')) {
      var $selected_radio = $product_field.find('input[type="radio"]:checked');
      price = $selected_radio.data('mcg-base-price');
    } else if ($product_field.hasClass('gfield--input-type-select')) {
      var $selected_option = $product_field.find('option:selected');
      price = $selected_option.data('mcg-base-price');
    } else if ($product_field.hasClass('gfield--input-type-calculation')) {
      var formatted_price = $product_field.find('.ginput_product_price').text();
      price = mcg.flatten_formatted_price(formatted_price);
    } else if ($product_field.hasClass('gfield--input-type-checkbox')) {
      var $selected_checkbox = $product_field.find('input[type="checkbox"]:checked');
      $selected_checkbox.each(function () {
        price += $(this).data('mcg-base-price');
      });
    } else {
      var $input = $product_field.find('[data-mcg-base-price]');
      if ($input.length) {
        price = $input.data('mcg-base-price');
      }
    }
    return gform.applyFilters('mcg_get_product_price_flattened', price, $product_field);
  },
  /**
   * Flatten the amount. I.e. convert it to cents.
   *
   * @param {price} price Price.
   * @param {string} from_currency From Currency.
   *
   * @returns Flattened amount.
   */
  flatten: function (price, from_currency) {
    if (!from_currency) {
      var $mcg_select = $('.ginput_container_multicurrency select');
      from_currency = $mcg_select.val();
    }
    var currency = new Currency(idea_mcg["all_currencies"][from_currency]);
    var number = currency.toNumber(price);
    return number * 100;
  },
  flatten_formatted_price: function (formatted_price) {
    var currency = new Currency(gf_global.gf_currency_config);
    var number = currency.toNumber(formatted_price);
    return number * 100;
  },
  /**
   * Unfltten the amount.
   * 
   * @param {int} flat_amt Flatt amount. Ex: 1905 
   * @returns float, unflat amount ex: 19.05
   */
  unflatten: function (flat_amt) {
    return flat_amt / 100;
  },
  /**
   * Get current currency.
   *
   * @param {*} $form_id 
   */
  get_current_currency: function (form_id) {
    var $mcg_select = $(` #gform_${form_id} .ginput_container_multicurrency select`);
    return $mcg_select.val();
  },
  /**
   * To fix the problem when back button is pressed after changing curency and saving form
   * we need to save the value of selected dropdown and radio fields in localstorage so we 
   * can restore it on back button.
   * 
   * Since the value of dropdown and options are updated (because of currency change) browser doesn't automatically
   * restore the value of dropdown and radio fields.
   */
  save_option_value_in_localstorage: function () {
    // save value of selected dropdown and radio fields in localstorage so we can restore it on back button.
    $('.gfield--input-type-select, .gfield--input-type-radio').each(function () {
      var $selected_option = $(this).find('option:selected, input[type=radio]:checked');
      if ($selected_option.length) {
        var val = $selected_option.val();
        var field_id = gf_get_input_id_by_html_id($(this).attr('id'));
        var form_id = mcg.get_form_id($(this));
        var key = `mcg_input_${form_id}_${field_id}`;
        localStorage.setItem(key, val);
      }
    });
  },
  /**
   * Restore the value of dropdown and radio fields from localstorage when back button is pressed.
   */
  restore_value_of_dropdown_and_radio_fields: function () {
    // restore value of selected dropdown and radio fields from localstorage.
    $('.gfield--input-type-select, .gfield--input-type-radio').each(function () {
      var field_id = gf_get_input_id_by_html_id($(this).attr('id'));
      var form_id = mcg.get_form_id($(this));
      var key = `mcg_input_${form_id}_${field_id}`;
      var val = localStorage.getItem(key);
      if (val) {
        mcg.select_field_option_by_first_part($(this), val);
      }
    });
  },
  /**
   * Select the option of dropdown and radio fields by matching the first part only (before |).
   * We ignore the second part because it is the price which is different for each currency.
   *
   * @param jQuery Object $field Field.
   * @param string val Whole value of the option to be selected, not just the first part.
   */
  select_field_option_by_first_part: function ($field, val) {
    val = val.split('|')[0];

    // loop through all options and select the one which matches the value.
    $field.find('option, input[type=radio]').each(function () {
      var thisval = $(this).val();
      thisval = thisval.split('|')[0];
      if (thisval == val) {
        if ($(this).is('option')) {
          $(this).prop('selected', true);
        } else if ($(this).is('input')) {
          $(this).prop('checked', true);
        }
      }
    });
  },
  /**
   * Pass the flat amount and get the price in current currency.
   *
   * @param int flat_amt 
   * @param int form_id 
   *
   * @returns 
   */
  flatToCurrentCurrency: function (flat_amt, form_id, return_number = false) {
    var current_currency = _mcg.get_current_currency(form_id);
    var base_currency = window['mcg_form_' + form_id].base_currency;
    var price = _mcg.convert_currency(base_currency, current_currency, _mcg.unflatten(flat_amt));
    price = Math.round(price * 100) / 100; // round to 2 decimal places.

    if (return_number) {
      return price;
    }
    var currencyObj = new Currency(idea_mcg["all_currencies"][current_currency]);
    return currencyObj.toMoney(price, true);
  },
  setPrice: function ($field, new_amt_formatted, update_values = true) {
    if (!$field.find('span.ginput_product_price').length) {
      $field.find('.ginput_container_singleproduct .ginput_product_price_label').after('<span class="ginput_product_price"></span>');
      $field.find('.ginput_container_singleproduct [id^=ginput_base_price_]').hide();
    }
    $field.find('.ginput_product_price').html(new_amt_formatted);
    if (update_values) {
      $field.find('.ginput_container_product_price input.ginput_amount').val(new_amt_formatted);
      $field.find('.ginput_product_price').val(new_amt_formatted);
      $field.find(`[id^=ginput_base_price_]`).val(new_amt_formatted);
    }
  },
  /**
   * Intelligently convert the formatted price to number.
   */
  toNumber: function (formatted_price, return_flattened = false) {
    if (typeof formatted_price === 'number') {
      return formatted_price;
    }

    // Remove any non-digit characters from the formatted price.
    var number = formatted_price.replace(/[^0-9.\-/,]+/g, '');

    // These currencies have comma as decimal separator
    if (formatted_price.includes('Kč') || formatted_price.includes('zł') || formatted_price.includes('€') || formatted_price.includes('Kr') || formatted_price.includes('Ft') || formatted_price.includes('R$')) {
      // Remove any thousands separators (spaces or ')
      number = number.replace(/[\s'/.]/g, '');
      number = number.replace(/[/,]/g, '.'); // convert comma to dot because Euro ¯\_(ツ)_/¯. 
    } else if (formatted_price.includes('pyб') || formatted_price.includes('CZK')) {
      // Remove any thousands separators (spaces)
      number = number.replace(/\s/g, '');
    } else if (formatted_price.includes('Fr.') || formatted_price.includes('CHF')) {
      // Remove any thousands separators (')
      number = number.replace(/'/g, '');
    } else {
      // Remove any thousands separators (,)
      number = number.replace(/,/g, '');
    }
    number = parseFloat(number).toFixed(2);
    return return_flattened ? _mcg.flatten(number) : number;
  },
  /**
   * Get mode - 'automatic' or 'manual'.
   * 
   * @param {int} form_id Form ID.
   */
  getMode: function (form_id) {
    return window['mcg_form_' + form_id] ? window['mcg_form_' + form_id]['mode'] : false;
  }
};
$(document).ready(function () {
  mcg.on_ready();
});
$(window).on('pageshow', function () {
  if (window.performance && window.performance.navigation.type === window.performance.navigation.TYPE_BACK_FORWARD) {
    $(".gform_wrapper .ginput_container_multicurrency select").trigger('change');
    mcg.restore_value_of_dropdown_and_radio_fields();
  }
});
window._mcg = mcg;
})();

/******/ })()
;
//# sourceMappingURL=main.js.map