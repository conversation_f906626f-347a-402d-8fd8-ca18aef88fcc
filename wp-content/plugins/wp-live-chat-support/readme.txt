=== 3CX Free Live Chat, Calls & WhatsApp ===
Contributors: WP-LiveChat, wordpress3cx, wp3cx
Tags: live chat, free live chat, live help, live support, wordpress live chat
Requires at least: 5.3
Tested up to: 6.5
Requires PHP: 5.4
Stable tag: 10.0.14
License: GPLv2
License URI: https://www.gnu.org/licenses/gpl-2.0.html
Text Domain: wp-live-chat-support

Chat with your website visitors in real-time for free! Engage with your customers and increase sales.

== Description ==

Increase your online conversions with the free [3CX Live Chat](https://www.3cx.com/signup/live-chat/?src=livechatwpplugin) plugin. Chat and call with your website visitors in real-time, and not only!

The plugin includes a complete communications system for your business - for free. Call, chat, conference and collaborate with your teams and customers from your office, home or smartphone. 

Zero cost business communications from one central hub. Receive WhatsApp, Facebook and SMS messages on the same dashboard as your live chats and calls.

Tried and tested with more than 30,000 active installations, 3CX Live Chat is a reliable free live chat solution for WordPress. 

= NO HIDDEN COSTS =

The plugin is [free](https://www.3cx.com/signup/live-chat/?src=livechatwpplugin), forever. It’s not a free trial and we don’t ask you for your credit card details. 
Simply create an account on the 3CX.com website, receive your account details, login and start chatting!

= VOICE, WHATSAPP & FACEBOOK! =
* Receive and respond to WhatsApp messages 
* Facebook and Text messaging too!
* Free calls – voice calls directly to and from the browser
* Answer, transfer and resolve customer queries on one platform across multiple agents
* Move to a new cloud phone system for free

= YOUR #1 LIVE CHAT SOLUTION =
* Website Live Chat (and Call)
* Inbound WhatsApp, Facebook, Text
* Video Conferencing
* 3CX Talk / Meet Links
* Mobile & Desktop Apps
* Hosted on Cloud
* Choose your SIP Trunk
* Ring Group
* Auto Attendant

= Video: 3CX Free Live Chat, Calls & WhatsApp =
[youtube https://www.youtube.com/watch?v=mtqHP_ef-2c]

= Video: How to Set Up 3CX Free and Live Chat =
[youtube https://www.youtube.com/watch?v=M6b23DJ2b90]

== Installation ==
Get 3CX
1. Download and install the WordPress Plugin
2. Sign up [here](https://www.3cx.com/signup/live-chat/?src=livechatwpplugin) for 3CX
3. With the credentials you receive login to the Web Client 
4. Go to Office Settings > Voice & Chat > Add Live Chat
5. Customize your live chat bubble
6. Start answering chats or calls from the 3CX Web Client.

= Read our guides on =
[Setting up Live Chat](https://www.3cx.com/docs/manual/live-chat/)

[youtube http://youtube.com/watch?v=onawKT7y5P8]

== Frequently Asked Questions ==

= How do I get 3CX Free? =
3CX Free is an easy to setup and manage communications platform that handles live chat as well as calls, video conferencing and team communications. Ideal for small to medium sized businesses. It is available in the cloud and offered for free.
1. Sign up for a 3CX account [here](https://www.3cx.com/signup/live-chat/?src=livechatwpplugin)
2. Install the WordPress Plugin on your website
3. With the credentials you receive login to the Web Client 
4. Go to Admin > Voice & Chat > Add Live Chat
5. Copy and paste the 3CX Live Chat URL

= Will 3CX Live Chat plugin remain free forever? =
Yes! The 3CX Live Chat plugin will remain free forever. 
With 3CX Free you get free live chat limited to 10 users. 
With 3CX Dedicated (hosted or self-managed) you get live chat included free for unlimited users.

== Screenshots ==

1. Customer UI of 3CX Live Chat
2. 3CX Web Client agent's UI
3. 3CX Live Chat video call on 3CX PBX channel
4. Getting Started configuration wizard

== Upgrade Notice ==

= 8.1.x =
Huge update with over 300 changes that include: Bugfixes, Vulnerability Fixes & Plugin Security, Plugin Optimization & Rebranding Updates. We suggest you remove any existing installations of this plugin and install 8.1.x fresh.

== Changelog ==

= 10.0.14 - 2024-07-04 =
* Limit incomplete installation warnings to 1

= 10.0.13 - 2024-05-17 =
* Update install URLs
* Added warnings when configuration is not complete

= 10.0.12 - 2024-04-10 =
* Fixed install warning on Wordpress 6.5

= 10.0.11 - 2024-02-12 =
* Fixed install with WP CLI

= 10.0.10 - 2023-07-20 =
* Updated Translations 
* Issue where call button still displayed despite "Enable calls to a queue" being disabled resolved
* Addressed the issue where calls received through Live Chat were being routed to the 'End Call'
* Added functionality to expose offline greetings message when offline form is being used
* Issue when a web visitor clicks on the checkbox to accept the GDPR rules now fixed
* Calls are now working correctly when chat is disabled
* Alignment of chat template placeholders has been fixed
* Fixed shift + enter to move to a new line when typing

= 10.0.9 - 2023-06-30 =
* Changed signup URL

= 10.0.8 - 2023-03-10 =
* Added reset configuration
* UI improvements

= 10.0.7 - 2023-03-08 =
* Added parameters to 3CX StartUP signup URL for easier activation
* Added StartUP callback for auto configuration

= 10.0.6 - 2023-01-03 =
* 3CX StartUP sign up form
* Show settings page when plugin is activated and not yet configured

= 10.0.5 - 2022-09-19 =
* LiveChat UI updated
* Multiple issues fixed

= 10.0.4 - 2022-06-29 =
* Fixed 3CX Talk URL - trailing ":" removed when no port specified

= 10.0.3 - 2022-06-06 =
* Fixed Callus Issues
* Default Agent's Photo Changes

= 10.0.2 - 2022-05-30 =
* Updated translations
* Added link to 3CX StartUP

= 10.0.1 - 2022-05-18 =
* Fixed Optional Caption Issue

= 10.0.0 - 2022-05-16 =
* New release with 3CX StartUP support

= 9.4.3 - 2022-04-28 =
* Fixed security issue - Thanks to Moucadel Matthieu
* Fixed issue when migrating from old versions - wplc_upgrade_tables_to_utf8mb4 error
* Improved custom bootstrap.css load

= 9.4.2 - 2022-04-15 =
* Fixed issue with path inclusion

= 9.4.1 - 2021-11-03 =
* Fixed issue on sound notification played even if it is disabled on settings.
* Fixed issue on Operation Hours related to caching.
* Fixed issue on Operating Hours in case that server is configured in +/- time zones more than 5.
* Fixed issue on “New Message” shown when session terminated by the user.
* Fixed issue on “Phone Only” mode in case that “Allow Chat” is disabled on PBX.

= 9.4.0 - 2021-08-06 =
* Improved file attachment handling for agent/visitor. 
* Fixed Gutenberg block functionality.
* Improved agent's chat page user interface.
* Improved agent's chat page to support multiple windows or tabs. 
* Added to agent's chat the capability to watch chat messages before joining. 
* Added translations for agent join chat messages.
* Improved quick responses interface on agent’s view. 
* Added user's selected department to agent's view. 
* Removed bootstrap.js library to prevent conflicts.
* Fixed emoji icons overflow on visitors' chat.
* Fixed default agent’s name on “Standalone - No 3CX” mode. 
* Fixed attachment size when agent sent file to visitor.
* Added an option to block IP of a visitor from the agent’s view. 
* Reduced callus.js file size. 
* Fixed issue on operating hours when cache module is enabled on server.
* Added new feature which allows visitors to make a call before their authorization (3CX mode only)
* Added support for Google Analytics events (first interaction,chat initialized, close chat, offline message submission).
* Fixed conflict with Font Awesome library of other Plugins or Themes.
* Fixed chat history to load the transcript with the correct agent name.
* Fixed chat history to use timezone as it is configured on WordPress general settings.
* Added preview of image attachments on visitor's chat.
* Visual improvements on user interface. 
* Improved error messages when an error occurred when the visitor is sending an attachment.
* Visitor’s chat loading after scroll when on mobile devices to improve Google check rating.
* Fixed remove attachments on deletion without database preservation.
* Fixed datetimes in machines local time at chat history data and exports.
* Added chat state ( minimized ) memory during navigation and page refresh.
* Fixed issue when clicking on chat conversation list more than once.
* Fixed issue with the name of the agent shown on conversations. 
* Fixed visual issue on how emojis are shown to the agents. 
* Fixed issue when a message with a huge number of characters is sent. 
* Fixed date time stamp in case of “Chat session ended”.
* Fixed issue on attaching sequentially attachments with size that takes time to be processed. 
* Fixed issue on chat window state preservation in case of navigating on different pages. 

= 9.3.1 - 2021-03-03 =
* Fixed issue with periodically redirections to chat page when in admin.

= 9.3.0 - 2021-02-12 =
* Styling improvements on mobile mode.
* Added the functionality of theme picker on Code-Generator and WordPress plugin.
* Added the option to set the system language.
* Revamped styling/functionality of chat rating on “Standalone - No 3CX” mode.
* Disabled maximize video button on not supported devices.
* Enabled the configuration of text on more functional areas.
* Enabled phone-only mode on Code-Generator.
* Updated to show only the agent's first name after taking the ownership on 3CX mode.
* Added the support of CDN on Code-Generator.
* Added “New” badge on “Standalone - No 3CX” mode for incoming chats.
* Added the logic to adapt chat messages text color based on background color.
* Enabled the configuration of images on Code-Generator.
* Enabled the option to disable offline functionality on Code-Generator.
* Refreshed translations.
* Security Fixes.
* Fixed issue with typing indicator on 3CX mode.
* Fixed issue with space not working in case that themes/plugins use smooth scrolling.
* Fixed issue with default agent image not being svg.
* Fixed issue with popout configuration for mobile mode.
* Fixed issue with passing parameters on popout window.
* Fixed issue with multisite WordPress installation.
* Fixed issue with special characters on chat window.
* Fixed issue not showing ending message in case that session terminated
* Prevent reloading the popped-out window by clicking on the minimized chat bubble.
* Fixed issue not showing exception in case of a non-valid attachment uploaded by the visitor.

= 9.2.1 - 2020-12-23 =
* Adjusted default popout behavior on 3CX mode.
* Fixed “New Message” tab notification to be shown only after the visitor engaged on chat.
* Fixed special characters set on welcome message not shown properly on popout window for 3CX mode.
* Fixed issue of not passing all parameters to the popout window for 3CX mode.
* Security fixes.

= 9.2.0 - 2020-12-15 =
* Revamped offline form to be of conversation style.
* Added greeting functionality that can be configured for Online and Offline mode.
* Added indicator on minimized bubble for unread messages.
* Added a variable for the visitor name that can be used for messages, e.g Welcome message.
* Fixed issue with chat not fit on screen when video activated.
* Fixed spacing on “is typing”
* Improved the visual presentation custom fields to the agents on “Standard - No 3CX” mode.
* Fixed issue with flickering screen when switching between conversations
* Adjusted UI to hide the arrow from consecutive conversations when they are coming from the same end.
* Fixed issue with offline form not shown on “Standard - No 3CX” mode when specific times are set on Chat Operating Hours options.
* Fixed issue with hanging in case that offline form failed to submit.
* Fixed issue with agent faulty refresh on “Standard - No 3CX” mode.
* Fixed issue with file attachment send remains hanging on “Standard - No 3CX” mode.
* Fixed issue with chat ended message not being configurable.
* Improved the handling of chats in case of inactivity or missed chats on “Standard - No 3CX” mode.
* Fixed issue of not hiding the minimized bubble in case of Phone Only mode when configuration is not valid or offline.
* Fixed issue with animation repetition on each user action.
* Fixed issue of not properly wrapping date/time inside the chat message box.
* Fixed issue of not resizing properly the chat window on iOS and Safari browser.
* Fixed issue with default visitor name configuration.
* Fixed issue with scrolling on Android devices.
* Fixed issue with chat window in caset that is configured using the percentage.
* Updated incoming chat sound notification.
* Fixed issue with avatar background color not respecting the chat color configuration.
* Fixed issue on plugin editor with  "number of chat rings" validation.
* Fixed issues with “is typing”  indicator flickering.
* Fixed styling issue on department selection.
* Adjusted status indicator on agents chat conversions for “Standard - No 3CX” mode.
* Added the option to “Ignore Queue ownership” on “3CX” mode.
* Added the ability to configure the profile picture prior taking ownership.
* Improved “Chat Operating Hours” configuration page.
* Fixed drop-downs to be of the same width.
* Added support for Automated first response for “3CX” mode.

= 9.1.2 - 2020-11-18 =
* Hot Fix settings validation when on Hosted Mode

= 9.1.1 - 2020-11-18 =
* Removed "On premise" mode.
* Removed Popout window chat for "Hosted" mode.
* Privacy policy settings are dynamically rendered based on settings.
* Added functionality to set the default agent's name which will be displayed before an agent join the chat.
* Changed the available button icons.
* Improved settings validations.
* Fixed operating hours validation excluding validation of not enabled days.
* Added Phone only mode for 3CX PBX integrated mode.
* Improved data exports.
* Added Phone column in offline messages view.
* Added new slide animation when chat is positioned left.
* Fixed ringing on new chat in order to respect the corresponding setting.
* Database tables upgrade to utf8mb4. ( Only 3CX Live Chat plugin's tables)
* Fixed conflict with other plugins or themes due to jQuery.validation.
* UI fixes and improvements.
* Improved Getting started wizard.

= 9.1.0 - 2020-10-20 =
* Added: New chatbox design.
* Improved: Getting started wizard.
* Improved: Default chat settings.
* Improved: Chat pop out mode.
* Removed: Chat tab chatbox style.
* Removed: Emojis
* Removed: "Only Phone", and "Video and Chat" modes when on 3CX integrated mode.
* Fixed number inputs functionality in settings
* Fixed security vulnerabilities.
* Fixed business hours save issue after upgrade from version 8.x.x
* Fixed WordPress agents automatic redirects to chat page.
* Fixed chat completion flow on server error.
* Fixed html encoded characters on Client chat

= 9.0.24 - 2020-09-15 =
* Added phone field in offline message email notification.
* Fix chat client initialization for logged in users.
* UI improvements and responsive fixes.

= 9.0.23 - 2020-09-11 =
* Code cleanup.
* Improvement in 3CX Hosted Chat functionality.

= 9.0.22 - 2020-09-10 =
* Fix dashboard online visitors / agents update.
* Fix agent's status report.
* Fix bug in 3CX Hosted Chat socket channel.

= 9.0.21 - 2020-09-09 =
* Added chat pop out to new window functionality.
* UI optimization for mobile devices.

= 9.0.20 - 2020-09-04 =
* Fix offline message success form. Message loads from settings.
* Fix unicode characters support in tools view .
* CSS improvements
* Support page new design.
* Improved 3CX Hosted Chat.

= 9.0.19 - 2020-09-01 =
* Fix Gravatar url constuction.
* Fix agent's set online top bar button functionality.
* Fix PHP warnings on empty fields.

= 9.0.18 - 2020-08-31 =
* Getting started wizard new design.
* Fix auto redirects to chat page on admin.
* Fix offline form validation.

= 9.0.17 - 2020-08-11 =
* Dashboard page new design.
* Agent chat page new design.
* Visitor's chat new design.
* Fix Visitor's chat display names after refresh page.

= 9.0.16 - 2020-08-10 =
* Fix default transcript email template.
* Fix offline email notification.
* Fix Set online top bar checkbox.

= 9.0.15 - 2020-08-06 =
* Fix broken "Getting Started" wizard.
* Fix blank settings page issue.

= 9.0.14 - 2020-08-05 =
* Added WordPress 5.5 compatibility fixes.
* Fix multiple chat listings after chat server disconnection.

= 9.0.13 - 2020-08-03 =
* Fix bug on visitors' chat initialization.

= 9.0.12 - 2020-07-31 =
* Fix utf8 encoding on settings json.

= 9.0.11 - 2020-07-30 =
* Fix error on update plugin process.

= 9.0.10 - 2020-07-29 =
* Fix mixed content issues on visitors' chat.

= 9.0.9 - 2020-07-29 =
* Fix avatar broken image.
* Added chat notifications ringtone and browser notification when not live chat window is active ( available only with 3CX servers usage ).
* Added font, isolated from external css, on visitor's chat.
* Fix offline message bug on 3CX PBX Integration mode.
* Added support of setting up texts in settings on 3CX PBX Integration mode.

= 9.0.8 - 2020-07-28 =
* Fix bug on display business hours.
* Added message on agent's chat view when chat is offline due to business hours settings.
* Fix bug on agent's chats list with broken image link.
* Fix chat notifications ringtone and browser notification.

= 9.0.6 - 2020-07-27 =
* Fix bug which appears when visitor had multiple active tabs with active chat.
* Fix bug "TcxFa is not defined" on websites front end.
* Fix broken image after remove logo in settings.
* Fix display of pages without title in settings lists.
* Fix "Current activity" counter on dashboard.
* Improved 3CX Chat server functionality.

= 9.0.5 - 2020-07-22 =
* Fix bug responsible for faulty roles migration from older version.
* Fix chat list rendering on agent online - offline toggle.

= 9.0.4 - 2020-07-21 =
* Fix UTF8 characters on settings.
* Fix bug on ajax calls due to trailing slash.
* Fix bug on received files load on chat client.
* Added missing functions prefix to isolate them from external calls.

= 9.0.3 - 2020-07-20 =
* Fix font-awesome icons conflict with other themes and/or plugins.
* Fix bug which creates interferences in post pages with checkboxes.
* Fix broken font-family on chat client.

= 9.0.2 - 2020-07-17 =
* Fix validation error texts.
* Fix activation process after update from old version.

= 9.0.1 - 2020-07-17 =
* Fix warning about PHP constants for PHP versions older than 5.6
* Changed default chat height to 50% from 70%

= 9.0.0 - 2020-07-17 =
* Full plugin refactor with NEW component based software architecture.
* Added new 3CX cloud chat servers integration.
* Added new feature for integration with 3CX PBX.
* Added new first time usage "Getting start" wizard.
* Added validations and conflicts checks in the plugin's settings.
* New chat client for visitors developed with Vue js web component.
* New agent chat interface unified for both on-Premise and 3CX cloud chat servers.
* Tools, Webhooks, Departments and Custom fields management moved under new section.
* New improved setup process for business hours.
* New improved setup process for page base settings like "Excluded chat pages".
* New chat server for on-premise installations with significantly reduced ajax requests quantity and footprint.
* Missed chats and History moved to new section Chat History with search functionality.
* Removed GDPR search functionality.
* Removed Triggers functionality.
* Removed all actions and filters.
* Removed ready to use themes functionality.

= 8.2.0 - 2020-07-11 =
* Fixed VULN: Fixed XSS vulnerability within quick responses in agent chat.
* Fixed VULN: Fixed XSS vulnerability within posts and pages render in agent direct to page modal.

= 8.1.9 - 2020-06-09 =
* Bugfix: Fixed chat icon url on admin.
* Bugfix: Custom theme colors.

= 8.1.8 - 2020-05-28 =
* Bugfix: Offline messages delete not worked
* Bugfix: Load bootstrap js sourcemap failed
* Bugfix: Urls Fixed for linux compatibility

= 8.1.7 - 2020-02-05 =
* Bugfix: added some missing translation strings
* Bugfix: HTML email body in auto responder not parsed correctly
* Improvement: added "Debug mode", extra logs added to PHP error log
* Improvement: reduced ajax requests footprint, removing unneeded fields
* Improvement: added confirmation for history delete

= 8.1.6 - 2020-01-16 =
* Bugfix: more optimizations to avoid lots of 403 errors when PHP session expires
* Bugfix: custom style color picker adds an extra # breaking color code
* Improvement: improved performance in some timed events
