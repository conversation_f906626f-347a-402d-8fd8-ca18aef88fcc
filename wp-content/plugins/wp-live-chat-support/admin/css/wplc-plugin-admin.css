.wplc_pages_scrollbox {
  width: 600px;
  border: 1px solid #aaa;
  height: 200px;
  overflow-y: scroll;
  padding: 4px;
}

.wplc_box_disabled {
  opacity: 0.5;
}

.wplc_subscribe_box {
  background-color: rgb(5, 151, 212);
  width: 400px;
  padding: 40px 40px 10px 40px;
  margin: 16px 0 16px 0;
}

.wplc_activation_box {
  background-color: rgb(5, 151, 212);
  width: 400px;
  padding: 10px 40px 10px 40px;
  margin: 16px 0 16px 0;
}

.wplc_subscribe_title {
  text-align: center;
  color: #fff;
  font-weight: 700;
  margin: 0;
  font-size: 2.5em;
}

.wplc_subscribe_subtitle {
  text-align: center;
  color: #fff;
  margin: 12px 0 16px 0;
  font-size: 1.2em;
}

.wplc_subscribe_button {
  margin-bottom: 30px;
  padding: 12px;
  width: 100%;
  height: 48px;
  border-radius: 3px;
  text-align: center;
  font-size: 14px;
  font-weight: 500;
  background-size: 24px;
  background-color: #fff;
  display: flex;
  justify-content: center;  
  text-decoration: none;
  box-sizing: border-box;
  line-height: 24px;
  color: #373737;
}

.wplc_subscribe_button:hover {
  border: 1px solid rgb(0 0 0 / 25%);
  box-shadow: 0 -1px 0 rgba(0, 0, 0, 0.04), 0 1px 1px rgba(0, 0, 0, 0.45);
  color: #373737;
}

.wplc_subscribe_or {
  text-align: center;
  color: #fff;
  width: 100%;
  text-align: center;
  line-height: 0;
  display: flex;
  margin: 24px 0;
  justify-content: center;  
}

.wplc_subscribe_or span {
  color: #fff;
  font-size: 0.8em;
  padding: 0 8px 0 8px;
}

.wplc_subscribe_or .xcx-line {
  border-bottom: 1px solid #ffffff99;
  flex: 1;
  padding: 0 10px;
  box-sizing: border-box;
  text-align: center;
  line-height: 0;
}

#wplc_reset_config {
  margin-top:24px;
}