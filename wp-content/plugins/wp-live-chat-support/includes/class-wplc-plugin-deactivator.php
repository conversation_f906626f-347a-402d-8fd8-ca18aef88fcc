<?php

/**
 * Fired during plugin deactivation
 *
 * @link       https://www.3cx.com
 * @since      10.0.0
 *
 * @package    wplc_Plugin
 * @subpackage wplc_Plugin/includes
 */

/**
 * Fired during plugin deactivation.
 *
 * This class defines all code necessary to run during the plugin's deactivation.
 *
 * @since      10.0.0
 * @package    wplc_Plugin
 * @subpackage wplc_Plugin/includes
 * <AUTHOR> <<EMAIL>>
 */
class wplc_Plugin_Deactivator {

	/**
	 * Short Description. (use period)
	 *
	 * Long Description.
	 *
	 * @since    1.0.0
	 */
	public static function deactivate() {

	}

}
