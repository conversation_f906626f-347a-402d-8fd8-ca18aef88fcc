table.wp-list-table {
	width: 100%;
	max-width: 100%;
	table-layout: auto;
	overflow-x: auto;
	-webkit-overflow-scrolling: touch;
	-ms-overflow-style: -ms-autohiding-scrollbar;
}

.wp-list-table pre {
	white-space: pre-wrap;
	margin: 0;
}

.wp-list-table code {
	background: transparent;
	padding: 0;
	font-size: 12px;
}

.wp-list-table td.column-crontrol_status {
	white-space: nowrap;
}

.wp-list-table td.column-cb,
.wp-list-table tbody th {
	border-left: 4px solid transparent;
}

.wp-list-table tr.crontrol-no-action th,
.wp-list-table tr.crontrol-warning th {
	border-color: #dba617;
}

.wp-list-table tr.crontrol-error th {
	border-color: #d63638;
}

.wp-list-table tr.crontrol-paused th {
	border-color: #8c8f94;
}

.wp-list-table tr.crontrol-paused:not(.crontrol-no-action) .column-crontrol_next,
.wp-list-table tr.crontrol-paused:not(.crontrol-no-action) .column-crontrol_schedule,
.wp-list-table tr.crontrol-paused:not(.crontrol-no-action) .column-crontrol_actions {
	text-decoration: line-through;
}

.wp-list-table .column-crontrol_icon .dashicons,
.wp-list-table .check-column .dashicons {
	margin-left: 6px;
	font-size: 15px;
}

.wp-list-table .column-crontrol_icon .dashicons {
	margin-top: 2px;
}

.wp-list-table .qm-icon-edit {
	display: none;
}

.status-crontrol-warning,
.wp-list-table tr.crontrol-no-action td.column-crontrol_status,
.wp-list-table tr.crontrol-warning td.column-crontrol_status {
	color: #bd8600;
}

.status-crontrol-error,
.wp-list-table tr.crontrol-error td.column-crontrol_status {
	color: #d63638;
}

.status-crontrol-check .dashicons,
.status-crontrol-paused .dashicons {
	background: #646970;
	border-radius: 50%;
	color: #fff;
	font-size: 14px;
	height: 14px;
	line-height: 14px;
	margin-top: 2px;
	padding: 1px;
	width: 14px;
}

.column-crontrol_time,
.column-crontrol_ran,
.column-crontrol_next {
	white-space: nowrap;
}

.row-actions .crontrol-in-use {
	color: #646970;
}

.form-field input[type="number"] {
	width: 100px;
}

.crontrol-edit-event-url .crontrol-event-standard,
.crontrol-edit-event-url .crontrol-event-php,
.crontrol-edit-event-standard .crontrol-event-url,
.crontrol-edit-event-standard .crontrol-event-php,
.crontrol-edit-event-php .crontrol-event-url,
.crontrol-edit-event-php .crontrol-event-standard {
	display: none;
}
