{"name": "johnbillion/wp-crontrol", "description": "Take control of the cron events on your WordPress website", "homepage": "https://wp-crontrol.com", "license": "GPL-2.0-or-later", "type": "wordpress-plugin", "authors": [{"name": "<PERSON>", "homepage": "https://johnblackbourn.com/"}, {"name": "<PERSON>", "homepage": "http://scompt.com/"}], "config": {"sort-packages": true, "preferred-install": "dist", "prepend-autoloader": false, "classmap-authoritative": true, "allow-plugins": {"composer/installers": true, "dealerdirect/phpcodesniffer-composer-installer": true, "roots/wordpress-core-installer": true}}, "require-dev": {"codeception/module-asserts": "^1.0", "codeception/module-db": "^1.0", "codeception/module-webdriver": "^1.0", "codeception/util-universalframework": "^1.0", "dealerdirect/phpcodesniffer-composer-installer": "^0.7.0", "johnbillion/plugin-infrastructure": "dev-trunk", "lucatume/wp-browser": "3.2.1", "phpcompatibility/phpcompatibility-wp": "2.1.4", "phpstan/phpstan": "^1.7", "roots/wordpress-core-installer": "1.100.0", "roots/wordpress-full": "*", "szepeviktor/phpstan-wordpress": "1.3.4", "wp-coding-standards/wpcs": "2.3.0"}, "require": {"php": ">=7.4", "composer/installers": "^1.0 || ^2.0"}, "autoload": {"classmap": ["src"]}, "extra": {"wordpress-install-dir": "vendor/wordpress/wordpress"}, "scripts": {"build-vendor": ["build-vendor"], "test": ["@composer validate --strict --no-check-lock", "@test:phpstan", "@test:phpcs", "@test:start", "@test:acceptance", "@test:stop"], "test:acceptance": ["acceptance-tests"], "test:destroy": ["tests-destroy"], "test:phpcs": ["phpcs -nps --colors --report-code --report-summary --report-width=80 --cache=tests/cache/phpcs56.json --basepath=./ --standard=phpcs56.xml", "phpcs -nps --colors --report-code --report-summary --report-width=80 --cache=tests/cache/phpcs.json --basepath=./ ."], "test:phpstan": ["codecept build", "phpstan analyze --memory-limit=1024M"], "test:start": ["tests-start"], "test:stop": ["tests-stop"]}, "support": {"issues": "https://github.com/johnbillion/wp-crontrol/issues", "forum": "https://wordpress.org/support/plugin/wp-crontrol", "source": "https://github.com/johnbillion/wp-crontrol"}, "funding": [{"type": "github", "url": "https://github.com/sponsors/johnbillion"}]}