/*Widgets*/
/*Widgets*/
/*Widgets*/
/*Widgets*/
/*Widgets*/
.clear {
	clear:both !important;
	margin:0 !important;
	padding:0 !important;
}
.widget-contest-gallery-div {
	width:47px !important;
	height:47px !important;
	float:left !important;
	margin:0 2px 2px 0 !important;
}
.widget-contest-gallery-div a{
	outline:none !important;
}
.widget-contest-gallery-img {
	border:none !important;
	margin:0 !important;
	padding:0 !important;
	width:47px !important;
	-webkit-backface-visibility: hidden;
	-moz-backface-visibility: hidden;
  transform: translate3d(0px,0px,0px);
  filter: brightness(1.01);
}
.widget-contest-classic-div {
	margin:0 0 2px 0 !important;
}
.widget-contest-classic-div a{
	outline:none !important;
}
.widget-contest-classic-img {
	border:none !important;
	margin:0 !important;
	padding:0 !important;
	width:100% !important;
}
.widget-contest-rank {
	margin-bottom:5px !important;
	font-size:14px;
	line-height:1.2;
}
.widget-contest-rank-num {
	float:left !important;
	font-size:36px;
}
.widget-contest-rank-num .widget-contest-rank-pos {

}
.widget-contest-rank-num .widget-contest-rank-pos a{
	text-decoration:none !important;
	outline:none !important;
}
.widget-contest-rank-info {
	padding:0 0 0 30px !important;
}
.widget-contest-rank-info div{
}
.widget-contest-rank-info div span{
	font-weight:bold !important;
}
.widget-contest-rank-info div span a{
	text-decoration:none !important;
	outline:none !important;
}
.widget-contest-rank-info div.widget-contest-votes {
	font-weight:bold !important;
}
.widget-photo-thumb img, .widget-contest-classic-div img{
	border:none !important;
	box-shadow:none !important;
	border-radius: 0px !important;

}
.widget-photo-thumb, .widget-contest-classic-div .photo-thumb img {
	filter: alpha(opacity=100) !important;
	opacity: 1.0 !important;
	-webkit-transition: all 1s ease;
     -moz-transition: all 1s ease;
       -o-transition: all 1s ease;
      -ms-transition: all 1s ease;
          transition: all 1s ease;
	-webkit-backface-visibility: hidden;
	-moz-backface-visibility: hidden;
  transform: translate3d(0px,0px,0px);
  filter: brightness(1.01);
}
.widget-photo-thumb:hover, .widget-contest-classic-div .photo-thumb img:hover {
	filter: alpha(opacity=50) !important;
	opacity: 0.5 !important;
	-webkit-transition: all 1s ease;
     -moz-transition: all 1s ease;
       -o-transition: all 1s ease;
      -ms-transition: all 1s ease;
          transition: all 1s ease;
	-webkit-backface-visibility: hidden;
	-moz-backface-visibility: hidden;
  transform: translate3d(0px,0px,0px);
  filter: brightness(1.01);
}
/*//Buddypress*/
.bp-photo-contest-activity a > img{
	display:block;
	margin-bottom:5px;
}
