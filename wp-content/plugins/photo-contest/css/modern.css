/*modern*/
.pcmodern {
	overflow:hidden !important;
	/*cursor:pointer;*/

}
.pcmodern a{
	color:#FFFFFF !important;
	text-decoration:none !important;
	box-shadow:none !important;
	border:none;

}
.pcmodern img {
	opacity: 1;
	overflow:hidden !important;
	-webkit-transform: scale(1);
    -moz-transform: scale(1);
    -ms-transform: scale(1);
    -o-transform: scale(1);
    transform: scale(1);
	-webkit-backface-visibility: hidden;
    -webkit-transition: -webkit-transform .5s ease;
    -moz-transition: -moz-transform .5s ease;
	max-height:100% !important;
	max-width:100% !important;
	height:auto !important;
    transition: all .5s ease;
	margin:0 !important;
	padding:0 !important;
	border:none !important;
	outline:none !important;

}
.pcmodern:hover img {
    -webkit-transform: scale(1.05);
    -moz-transform: scale(1.05);
    -ms-transform: scale(1.05);
    -o-transform: scale(1.05);
    transform: scale(1.05);
	opacity: .2;
    -webkit-backface-visibility: hidden;
    -webkit-transition: -webkit-transform .5s ease;
    -moz-transition: -moz-transform .5s ease;
    transition: all .5s ease;

}
.modern-top-box {
	position:absolute;
	padding:10px 10px ;
	left: -600px;
	top:0px;
	color: #fff;
	text-align:left;
	width: calc(100% - 4px);
	box-sizing: border-box;
	cursor:pointer;
  -moz-box-sizing: border-box;
  -webkit-box-sizing: border-box;
	-webkit-transition: all 0.4s ease;
     -moz-transition: all 0.4s ease;
       -o-transition: all 0.4s ease;
      -ms-transition: all 0.4s ease;
          transition: all 0.4s ease;
	}
.modern-top-box .font20{
	font-size:20px !important;
	}
.modern-top-box .font18{
	font-size:18px !important;
	}
.modern-top-box .font16{
	font-size:16px !important;
	}
.modern-top-box .font14{
	font-size:14px !important;
	}
.modern-top-box .font12{
	font-size:12px !important;
	}
.modern-top-box .font11{
	font-size:11px !important;
	}
.modern-top-box .font10{
	font-size:10px !important;
	}
.pcmodern:hover .modern-top-box{
	left: 0px;
  -webkit-backface-visibility: hidden;
	-moz-backface-visibility: hidden;
  transform: translate3d(0px,0px,0px);
  filter: brightness(1.01);
}
.modern-top-box-title{
	text-transform:uppercase;
	font-weight: 500;
	margin-bottom:5px;
	text-shadow: 0px 1px 0px rgba(51,51,51,0.1);
	-webkit-font-smoothing: antialiased;
}
.modern-top-box-author, .modern-top-box-cat,.modern-top-box-votes{
	text-transform:uppercase;
	font-weight: 300;
}
.modern-bottom-box {
	position:absolute;
	padding:10px 10px ;
	bottom: -200px;
	font-size:12px;
	color: #fff;
	width: 100%;
	box-sizing: border-box;
  -moz-box-sizing: border-box;
  -webkit-box-sizing: border-box;
	-webkit-transition: all 0.5s ease;
     -moz-transition: all 0.5s ease;
       -o-transition: all 0.5s ease;
      -ms-transition: all 0.5s ease;
          transition: all 0.5s ease;
		  text-align: right;
	}

.pcmodern:hover .modern-bottom-box{
	bottom: 0px;
	right:0px;
  -webkit-backface-visibility: hidden;
	-moz-backface-visibility: hidden;
  transform: translate3d(0px,0px,0px);
  filter: brightness(1.01);
}
.modern-bottom-box .fa-stack
{
    -ms-transform: rotateY(0deg); /* IE 9 */
    -webkit-transform: rotateY(0deg); /* Safari */
	transform: rotateY(0deg);
	transition:0.3s;
	cursor:pointer;
}
.modern-bottom-box .fa-stack:hover
{   -ms-transform: rotateY(180deg); /* IE 9 */
    -webkit-transform: rotateY(180deg); /* Safari */
	transform: rotateY(180deg);
	transition: 0.6s;
	transform-style: preserve-3d;
	color:#fff;
}
.modern-bottom-box a{
	display:inline !important;
	float:none !important;

}
.modern-top-linkicon {
	position:absolute;
	padding:12px 10px ;
	top: -200px;
	font-size:12px;
	color: #fff;
	width: 100%;
	box-sizing: border-box;
  -moz-box-sizing: border-box;
  -webkit-box-sizing: border-box;
	-webkit-transition: all 0.5s ease;
     -moz-transition: all 0.5s ease;
       -o-transition: all 0.5s ease;
      -ms-transition: all 0.5s ease;
          transition: all 0.5s ease;
		  text-align: right;
	}

.pcmodern:hover .modern-top-linkicon{
	top: 0px;
	right:0px;
  -webkit-backface-visibility: hidden;
	-moz-backface-visibility: hidden;
  transform: translate3d(0px,0px,0px);
  filter: brightness(1.01);
}
.modern-top-linkicon a{
	display:inline !important;
	float:none !important;

}
.pc-modern-button{
	width:100%;
	background-color:#FFFFFF;
	box-sizing:border-box;
	text-align:center;
	padding:10px;
	border-radius: 5px;
	cursor:pointer;
	line-height:1;
	text-transform:uppercase;
}
.modern-image-box {
	overflow:hidden !important;
	cursor:pointer;
	line-height:0;
	box-sizing: border-box;
  -moz-box-sizing: border-box;
  -webkit-box-sizing: border-box;
}
.pc-modern-button a {
	text-decoration: none;
}
.modern-image-box img{
	width:100%;
}
.modern-lightbox-box {
	opacity: 0;
    filter: alpha(opacity=0); /* For IE8 and earlier */
	position:absolute;
	color: #fff;
	width: calc(100% - 4px);
	box-sizing: border-box;
	margin-left:2px;
	cursor:pointer;
	-moz-box-sizing: border-box;
	-webkit-box-sizing: border-box;
	-webkit-transition: all 0.9s ease;
	-moz-transition: all 0.9s ease;
	-o-transition: all 0.9s ease;
	-ms-transition: all 0.9s ease;
	transition: all 0.9s ease;
}
.modern-lightbox-box.pc-fa1{
	top: calc(50% - 46px);
}
.modern-lightbox-box.pc-fa2{
	top: calc(50% - 52px);
}
.modern-lightbox-box.pc-fa3{
	top: calc(50% - 35px);
}
.modern-lightbox-box.pc-fa4{
	top: calc(50% - 26px);
}
.modern-lightbox-box.pc-fa5{
	top: calc(50% - 26px);
}
.modern-lightbox-box a {
	display:block;
	margin-left:auto;
	margin-right:auto;
	text-align:center !important;
	text-decoration:none !important;
	border:none !important;
	box-shadow:none !important;
}
.pcmodern:hover .modern-lightbox-box{
	opacity: 1;
    filter: alpha(opacity=100); /* For IE8 and earlier */
  -webkit-backface-visibility: hidden;
	-moz-backface-visibility: hidden;
  transform: translate3d(0px,0px,0px);
  -webkit-transition: all 1.1s ease;
     -moz-transition: all 1.1s ease;
       -o-transition: all 1.1s ease;
      -ms-transition: all 1.1s ease;
          transition: all 1.1s ease;
  filter: brightness(1.01);
}
.gallery-wrap .modern-full{
	-moz-box-sizing: border-box !important;
	-webkit-box-sizing: border-box !important;
	box-sizing: border-box !important;
	position:relative !important;
	text-align:center;
	margin:0 auto 20px !important;
}
.gallery-wrap .modern-full img{
	max-width:100%;
}
.gallery-wrap .modern-half {
	float:left !important;
	width: calc(50% - 5px) !important;
	-moz-box-sizing: border-box !important;
	-webkit-box-sizing: border-box !important;
	box-sizing: border-box !important;
	position:relative !important;
	margin:0 4px 4px 0!important;
}

.gallery-wrap .modern-third {
	float:left !important;
	width: calc(33.4% - 5px) !important;
	-moz-box-sizing: border-box !important;
	-webkit-box-sizing: border-box !important;
	box-sizing: border-box !important;
	position:relative !important;
	margin:0 4px 4px 0!important;
	display: block;

}
.gallery-wrap .modern-fourth {
	float:left !important;
	width: calc(25% - 4px) !important;
	-moz-box-sizing: border-box !important;
	-webkit-box-sizing: border-box !important;
	box-sizing: border-box !important;
	position:relative !important;
	margin:0 4px 4px 0!important;
	display: block;
}

.gallery-wrap .modern-fifth {
	float:left !important;
	width: calc(20% - 4px) !important;
	-moz-box-sizing: border-box !important;
	-webkit-box-sizing: border-box !important;
	box-sizing: border-box !important;
	position:relative !important;
	margin:0 4px 4px 0!important;
	display: block;
}
.gallery-wrap .modern-half a, .gallery-wrap .modern-third a, .gallery-wrap .modern-fourth a, .gallery-wrap .modern-half a img, .gallery-wrap .modern-third a img, .gallery-wrap .modern-fourth a img {
	float:left;
	margin:0 !important;
	width:100% !important;
}
.gallery-wrap .modern-half img {
	width:100% !important;
}
.gallery-wrap .modern-full img, .gallery-wrap .modern-half img, .gallery-wrap .modern-third img, .gallery-wrap .modern-fourth img, .gallery-wrap .modern-fifth img{
	box-shadow:none !important;
	}
.modern-half.pcmobile, .modern-full.pcmobile, .padb, .fifty, .formbreak {
	display:none !important;
}
.pcmodern .pc-searchicon{
	display:block;
	text-align:center;
	margin:auto;
	width:70px;
	height:70px;
	background: url("../assets/icons/search2.png") no-repeat center;

}
.pcmodern .pc-searchicon2{
	display:block;
	text-align:center;
	margin:auto;
	width:90px;
	height:90px;
	background: url("../assets/icons/search.png") no-repeat center;

}
.pcmodern .pc-searchicon3{
	display:block;
	text-align:center;
	margin:auto;
	width:50px;
	height:50px;
	background: url("../assets/icons/search3.png") no-repeat center;

}
.pcmodern .pc-loveicon{
	display:inline-block;
	width:40px;
	height:40px;
	margin:0 4px 0 0;
	background: url("../assets/icons/love40.png") no-repeat center;
	-ms-transform: rotate(0deg); /* IE 9 */
    -webkit-transform: rotate(0deg); /* Safari */
	transform: rotate(0deg);
	transition:0.6s;
	cursor:pointer;
}
.pcmodern .pc-loveicon2{
	display:inline-block;
	width:50px;
	height:50px;
	margin:0 4px 0 0;
	background: url("../assets/icons/love50.png") no-repeat center;
	-ms-transform: rotate(0deg); /* IE 9 */
    -webkit-transform: rotate(0deg); /* Safari */
	transform: rotate(0deg);
	transition:0.6s;
	cursor:pointer;
}
.pcmodern .pc-loveicon3{
	display:inline-block;
	width:30px;
	height:30px;
	margin:0 4px 0 0;
	background: url("../assets/icons/love30.png") no-repeat center;
	-ms-transform: rotate(0deg); /* IE 9 */
    -webkit-transform: rotate(0deg); /* Safari */
	transform: rotate(0deg);
	transition:0.6s;
	cursor:pointer;
}
.pcmodern .pc-rateicon{
	display:inline-block;
	width:40px;
	height:40px;
	margin:0 4px 0 0;
	background: url("../assets/icons/star40.png") no-repeat center;
	-ms-transform: rotate(0deg); /* IE 9 */
    -webkit-transform: rotate(0deg); /* Safari */
	transform: rotate(0deg);
	transition:0.6s;
	cursor:pointer;
}
.pcmodern .pc-rateicon2{
	display:inline-block;
	width:50px;
	height:50px;
	margin:0 4px 0 0;
	background: url("../assets/icons/star50.png") no-repeat center;
	-ms-transform: rotate(0deg); /* IE 9 */
    -webkit-transform: rotate(0deg); /* Safari */
	transform: rotate(0deg);
	transition:0.6s;
	cursor:pointer;
}
.pcmodern .pc-rateicon3{
	display:inline-block;
	width:30px;
	height:30px;
	margin:0 4px 0 0;
	background: url("../assets/icons/star30.png") no-repeat center;
	-ms-transform: rotate(0deg); /* IE 9 */
    -webkit-transform: rotate(0deg); /* Safari */
	transform: rotate(0deg);
	transition:0.6s;
	cursor:pointer;
}
.pcmodern .pc-linkicon{
	display:inline-block;
	width:40px;
	height:40px;
	margin:0;
	background: url("../assets/icons/link40.png") no-repeat center;
	-ms-transform: rotate(0deg); /* IE 9 */
    -webkit-transform: rotate(0deg); /* Safari */
	transform: rotate(0deg);
	transition:0.6s;
	cursor:pointer;
}
.pcmodern .pc-linkicon2{
	display:inline-block;
	width:50px;
	height:50px;
	margin:0 4px 0 0;
	background: url("../assets/icons/link50.png") no-repeat center;
	-ms-transform: rotate(0deg); /* IE 9 */
    -webkit-transform: rotate(0deg); /* Safari */
	transform: rotate(0deg);
	transition:0.6s;
	cursor:pointer;
}
.pcmodern .pc-linkicon3{
	display:inline-block;
	width:30px;
	height:30px;
	margin:0 4px 0 0;
	background: url("../assets/icons/link30.png") no-repeat center;
	-ms-transform: rotate(0deg); /* IE 9 */
    -webkit-transform: rotate(0deg); /* Safari */
	transform: rotate(0deg);
	transition:0.6s;
	cursor:pointer;
}
.modern-bottom-box .pc-loveicon:hover,
.modern-bottom-box .pc-loveicon2:hover,
.modern-bottom-box .pc-loveicon3:hover,
.modern-top-linkicon .pc-linkicon:hover,
.modern-top-linkicon .pc-linkicon2:hover,
.modern-top-linkicon .pc-linkicon3:hover,
.modern-bottom-box .pc-rateicon:hover,
.modern-bottom-box .pc-rateicon2:hover,
.modern-bottom-box .pc-rateicon3:hover
{   -ms-transform: rotate(360deg); /* IE 9 */
    -webkit-transform: rotate(360deg); /* Safari */
	transform: rotate(360deg);
	transition: 0.3s;
	transform-style: preserve-3d;
	color:#fff;
}
@media (max-width: 800px) {
.modern-full{
	box-sizing: border-box;
  -moz-box-sizing: border-box;
  -webkit-box-sizing: border-box;
}
.modern-full img{
	width:100%;
}
.modern-half.pcmobile{
	display:block !important;
}
.modern-full.pcdesktop, .modern-half.pcdesktop, .modern-third.pcdesktop, .modern-fourth.pcdesktop, .modern-fifth.pcdesktop {
	display:none !important;
}
.gallery-wrap .modern-full img {
	max-height:none !important;
}
}
@media (max-width: 500px) {
.modern-half.pcmobile{
	display:none !important;
}
.modern-full.pcmobile{
	display:block !important;
}
}
