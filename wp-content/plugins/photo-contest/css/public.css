@-webkit-keyframes pcpluginfadein { from { opacity:0; } to { opacity:1; } }
@-moz-keyframes pcpluginfadein { from { opacity:0; } to { opacity:1; } }
@keyframes pcpluginfadein { from { opacity:0; } to { opacity:1; } }
.pc-small{
	font-size:12px !important;
}
.form-white-back{
	background-color:#FFFFFF;
}
.g-recaptcha{
	margin:auto;
	display:block;
	width:304px;
}
@-moz-document url-prefix() {
    .g-recaptcha{
	padding-top:15px;
}
}
.upload-label{
	text-align:left;
	width:100%;
	box-sizing: border-box;
  -moz-box-sizing: border-box;
  -webkit-box-sizing: border-box;
  margin-top:5px;
}
#looklikeform{
	width:165px;
	font-size:12px;
	height:24px !important;
	max-height:24px !important;
	line-height: 24px;
	white-space: nowrap;
	display:inline-block;
	overflow:hidden;
	box-sizing: border-box;
  -moz-box-sizing: border-box;
  -webkit-box-sizing: border-box;
  margin:auto 0;
  padding:0 5px;

}
.pcfontsize, .pcfontsize input, .pcfontsize select, .pcfontsize label {
	font-size:13px !important;
}
.pcfontsizebig, .pcfontsizebig input, .pcfontsizebig select, .pcfontsizebig label {
	font-size:14px !important;
}
.pcfontsizebigger, .pcfontsizebigger input, .pcfontsizebigger select, .pcfontsizebigger label {
	font-size:15px !important;
}
.pc_displaynone {
	display:none !important;
}
.pc_visiblenone {
	visibility:hidden;
}
.pc_marginbottom{
	height:20px;
}
.photo-contest-animation, .pc-menu-animation {
	opacity:0;  /* make things invisible upon start */
	-webkit-animation:pcpluginfadein ease-in 1;  /* call our keyframe named fadeIn, use animattion ease-in and repeat it only 1 time */
	-moz-animation:pcpluginfadein ease-in 1;
	animation:pcpluginfadein ease-in 1;

	-webkit-animation-fill-mode:forwards;  /* this makes sure that after animation is done we remain at the last keyframe value (opacity: 1)*/
	-moz-animation-fill-mode:forwards;
	animation-fill-mode:forwards;

	-webkit-animation-duration:0.8s;
	-moz-animation-duration:0.8s;
	animation-duration:0.8s;

}
.pc-menu-animation{
	-webkit-animation-delay: 0.5s;
   -moz-animation-delay: 0.5s;
    animation-delay: 0.5s;
}
.photo-contest-animation{
	-webkit-animation-delay: 1s;
   -moz-animation-delay: 1s;
    animation-delay: 1s;
}
.photo-contest{
	color:#555555;
	max-width:1065px !important;
	margin:0 auto !important;
	-webkit-animation-delay: 1s;
	-moz-animation-delay: 1s;
	animation-delay: 1s;
	background-color:#FFFFFF;
	min-height:600px;
}
.photo-contest a{
	background:none !important;
	-webkit-box-shadow: none !important;
	-moz-box-shadow: none !important;
	box-shadow: 0 0 0 0 #FFF !important;
	border:none !important;
	outline:none !important;

}
.photo-contest a img{
	padding:0;
	margin:0;

}
.photo-contest label strong{
	font-weight:400;
}

.photo-contest td, .photo-contest table{
	background:none !important;
	color:#757575;
	text-align:left;
}
.photo-contest form{
	margin:0;
	padding:0;
	line-height:1;
}

.photo-contest input, .photo-contest select, .photo-contest textarea{
	font-family: inherit;
	padding:6px;
	background: #FFF;
}
.photo-contest .under-image-bar-left-div-right  input{
	height:auto !important;
	background: #FFF !important;
}
.photo-form-error {
	color:red !important;
	margin-bottom:5px !important;
}
.photo-contest fieldset{
	border:none !important;
}

/*Gallery*/
/*Gallery*/
/*Gallery*/
/*Gallery*/
/*Gallery*/
/*Gallery*/
.gallery-wrap {
	float:left !important;
	width: 100%;
	color:#575757 !important;
	line-height:1.4 !important;
}
.gallery-wrap.plusthree {
	width: calc(100% + 2px) !important;
}
.gallery-wrap.plusfive {
	width: calc(100% + 4px) !important;
}
.gallery-wrap.plussix {
	width: calc(100% + 4px) !important;
}
.gallery-wrap .classic img{
	filter: alpha(opacity=100) !important;
	opacity: 1.0 !important;
	-webkit-transition: all 1s ease;
     -moz-transition: all 1s ease;
       -o-transition: all 1s ease;
      -ms-transition: all 1s ease;
          transition: all 1s ease;
   -webkit-backface-visibility: hidden;
	-moz-backface-visibility: hidden;
  transform: translate3d(0px,0px,0px);
  filter: brightness(1.01);
    box-shadow:none !important;
	border: none !important;
	border-radius: 0 !important;
	max-width:100%;
}
.gallery-wrap .imagebox{
	border-top:1px solid  #e7e7e7;
	border-left:1px solid  #e7e7e7;
	border-right:1px solid  #e7e7e7;
	text-align:center;
}
.gallery-wrap .imagebox img{
	max-height:100%;
	max-width:100%;
	margin: auto;
}
.gallery-wrap .classic img:hover{
	filter: alpha(opacity=50) !important;
	opacity: 0.5 !important;
  -webkit-backface-visibility: hidden;
	-moz-backface-visibility: hidden;
  transform: translate3d(0px,0px,0px);
  filter: brightness(1.01);
}
.photo_vote, .jury_vote, .jury_vote_undo  {
	cursor:pointer !important;
}
.email_verify{
	cursor:pointer !important;
}
.valid-email{
	max-width:513px !important;
	margin:0 auto -10px!important;
}
.clear {
	clear:both !important;
	margin:0 !important;
	padding:0 !important;
}
.gallery-list-grid {
	float:left !important;
	text-align:left !important;
	max-width:200px !important;
	margin-left:2px !important;
	margin-top:2px !important;
	padding:0 !important;
}
.gallery-votes {
	font-size:91% !important;
	border: #e7e7e7 solid 1px !important;
	margin:0 0 2px 0px !important;
	background-color:#f2f2f2 !important;
}
.gallery-votes span{
	display:block;
	margin:0 2px 0 2px !important;
	padding:4px !important;
	float:right !important;
}
.gallery-votes span.left{
	display:block !important;
	margin:0 2px 0 2px !important;
	padding:4px !important;
	float:left !important;
}
.gallery-votes span span{
	margin:0 !important;
	padding:0 !important;
	float:none !important;
	display:inline !important;
}
.pop .gallery-votes span span{
	filter: alpha(opacity=90) !important;
	opacity: 0.9 !important;
}
.pop .gallery-votes span{
	filter: alpha(opacity=90) !important;
	opacity: 0.9 !important;
}
.pop:hover .gallery-votes span{
	filter: alpha(opacity=100) !important;
	opacity: 1.0 !important;
}
.pop .gallery-votes span.right{
	float:right !important;
	cursor:pointer !important;
}
.zip .gallery-votes span span{
	filter: alpha(opacity=90) !important;
	opacity: 0.9 !important;
}
.zip .gallery-votes span{
	filter: alpha(opacity=90) !important;
	opacity: 0.9 !important;
}
.zip:hover .gallery-votes span{
	filter: alpha(opacity=100) !important;
	opacity: 1.0 !important;
}
.zip .gallery-votes span.right{
	float:right !important;
	cursor:pointer !important;
}
.pc-pagination {
    display: inline-block;
}
.contest-pagination{
	text-align:center;
}
.pc-pagination span {
    color: black;
    padding: 6px 14px;
    text-decoration: none;
    transition: background-color .3s;
    border: 1px solid #ddd;
    margin: 0 4px;
	display:inline-block;
	box-sizing: border-box;
  -moz-box-sizing: border-box;
  -webkit-box-sizing: border-box;
  cursor:pointer;
  	margin:4px 4px 0 4px;

}
.pc-pagination span a{
    color: #787878;
}

.pc-pagination span.active {
    background-color: #38b3e5;
    color: white;
    border: 1px solid #38b3e5;
}

.pc-pagination span:hover:not(.active) {background-color: #ddd;}
.gallery-wrap .gallery-title-autor{
	background-color:#ffffff !important;
	border-left:#e7e7e7 1px solid !important;
	border-right:#e7e7e7 1px solid !important;
	padding:10px 5px !important;
	line-height: 1.3 !important;
}
.gallery-wrap .gallery-title-autor .author{
	text-align:center;
	font-weight:400;
	text-transform:uppercase !important;
}
.gallery-wrap .gallery-title-autor .pc-title{
	overflow:hidden !important;
	font-weight:400;
	text-align:center;
	margin-top:8px;

}
.gallery-wrap .gallery-title-autor .pc-title a{
	text-decoration:none !important;
	color:#575757 !important
}
.gallery-wrap .gallery-title-autor .pc-title a:hover{
	text-decoration:underline !important;
	color:#9A9A9A !important
}
.pc-gal-select{
	width: calc(33.3% - 1%) !important;
	margin-right:1%;
	margin-bottom: 5px !important;
	float:left;

}
.pc-gal-select-last{
	width: calc(33.4%) !important;
	margin-right:0px;
	margin-bottom: 10px !important;
	float:left;

}
.pc-gal-select2 {
    width: calc(50% - 1%) !important;
    margin-right: 1%;
    float: left;
}
.pc-gal-select-last2{
	width: calc(50%) !important;
	margin-right:0px;
	float:left;

}
.pc-gal-select3{
	margin-bottom: 10px !important;
}
/*Image Detail Page*/
/*Image Detail Page*/
/*Image Detail Page*/
/*Image Detail Page*/
/*Image Detail Page*/
.photo-contest-image {
	position:relative !important;
	text-align:center !important;
	margin:0;
	padding:0;
	line-height:1 !important;
}
.photo-contest-image a{
	display:block;
}
.photo-contest-image img{
	height: auto !important;
    width: auto !important;
    max-width: 100% !important;
    max-height: 800px !important;
	margin-top: 0 !important;
	margin-bottom: 0 !important;
	display:inline-block !important;
}
.photo-contest-image img{
	 -webkit-box-shadow: none !important;
     box-shadow: none !important;
}
.contest-detail-info {
	margin:10px 0 0px 0 !important;
	border:1px solid #d9d9d9 !important;
	padding:10px 10px !important;
	position:relative !important;
	text-align:center;
	background-color:#FFFFFF;

}
.contest-detail-vote{
	text-align:center !important;
	padding:4px 0px !important;
	min-width:100px !important;
	max-width:300px !important;
	background-color:#0085f6 !important;
	color:#fff !important;
	margin:auto !important;
	font-weight:bold !important;
}
.contest-detail-vote.fin{
	background-color: #C00 !important;
}
.contest-detail-vote.thx{
	background-color: #336600 !important;
}
.contest-detail-vote a{
	display:block !important;
	width:100% !important;
	color:#fff  !important;
	text-decoration:none  !important;
}
.contest-detail-vote a:hover{
	color:#ccc !important;
}
.previous_photo, .next_photo{
	position: absolute;
	top: 49%;
	transform: translate(0, -50%);
	filter: alpha(opacity=70) !important;
	opacity: 0.7 !important;
	-moz-transition: opacity .9s ease-out;
    -webkit-transition: opacity .9s ease-out;
    -o-transition: opacity .9s ease-out;
}
.next_photo{
	right: 0px;
}
.previous_photo{
	left: 0px;
}
.previous_photo img, .next_photo img{
	border:#fff 1px solid;
	moz-transition: border .9s ease-out;
    -webkit-transition: border .9s ease-out;
    -o-transition: border .9s ease-out;
}
.photo-contest-image:hover .previous_photo, .photo-contest-image:hover .next_photo{
	filter: alpha(opacity=100) !important;
	opacity: 1.0 !important;
	-moz-transition: opacity .9s ease-out;
    -webkit-transition: opacity .9s ease-out;
    -o-transition: opacity .9s ease-out;

}
.photo-contest-image:hover .previous_photo img, .photo-contest-image:hover .next_photo img{
	border:#858585 1px solid;
	moz-transition: border .9s ease-out;
    -webkit-transition: border .9s ease-out;
    -o-transition: border .9s ease-out;
}

.pc-image-info-box {
	width:100%;
	max-width:100%;
	box-sizing: border-box;
	padding:0px;
	margin:5px 0 0 0;
	line-height:1.4;
	/*background-color:#F8F8F8;*/
}
.pc-image-info-box hr {
	padding:0 !important;
	margin:10px 0 !important;
	width:100% !important;
	border:none !important;
	box-shadow:none !important;
}
.pc-image-info-box .pc-image-info-box-text {
	width: 100%;
	margin:0;
	padding:0;
	float:left;
	vertical-align:top;
}
.pc-main-title,
.pc-main-author,
.pc-main-votes{
    font-size: 1.4em;
}
.pc-main-others{
	margin-bottom:10px;
}
.pc-main-title span,
.pc-main-author span,
.pc-main-others span,
.pc-main-votes span{
	text-transform:uppercase;
	font-weight:400;
	color:#2A2A2A;
}
.pc-main-votes{
	margin-bottom:10px;
}
.pc-image-info-box .pc-image-info-box-button {
	width:100%;
	margin:0 0 15px 0;
	float:right;
	vertical-align:top;
	cursor:pointer;
}
.pc-image-info-box .pc-image-info-box-button .pc-image-info-box-button-btn {
	width:100%;
	max-width:100%;
	box-sizing: border-box;
	padding:14px 10px 15px;
	color:#FFFFFF;
	text-align:center;
	text-transform:uppercase;
	font-weight:400;
}
.pc-image-info-box .pc-image-info-box-button .pc-image-info-box-button-btn-r {
	width:100%;
	max-width:100%;
	box-sizing: border-box;
	padding:8px 8px 7px;
	color:#FFFFFF;
	text-align:center;
	text-transform:uppercase;
	font-weight:400;
}
.pc-image-info-box .pc-image-info-box-button .pc-image-info-box-button-btn a {
	color:#FFFFFF;
	text-decoration: none;
}
.pc-image-info-box .pc-image-info-box-button .pc-image-info-box-button-btn-text {
	font-size:1.4em;
	color:#FFFFFF;
	text-shadow: 0 1px 1px #4d4d4d;
}
.pc-image-info-box .pc-image-info-box-button .pc-image-info-box-button-btn-text .fa {
}
.pc-image-info-box-button-btn.redb {
	background-color: #af0000;
	cursor: default;
}
.pc-image-info-box-button-btn.greenb {
	background-color: #390;
	cursor: default;
}
.pc-image-info-box-button-btn.greyb {
	background-color: #666;
	cursor: default;
}
input.pc-comment-button {
	width:100%;
	max-width:100%;
	box-sizing: border-box;
	border:none !important;
	background-color:#666 !important;
	margin-top:10px !important;
	color:#FFF;
}
input.pc-comment-button:hover {
	background-color:#888 !important;
}
.pc-hiddenlink {
	width:100%;
	margin-top:10px;
}
.pc-image-info-box .pc-image-info-box-button .pc-image-info-box-button-btn2 {
	width:100%;
	max-width:100%;
	box-sizing: border-box;
	padding:5px 10px 4px;
	color:#FFFFFF;
	text-align:center;
	text-transform:uppercase;
	font-weight:400;
	background-color:#FFCC00;
	margin-bottom:5px;
}
.pc-image-info-box .pc-image-info-box-button .pc-image-info-box-button-btn3 {
	width:100%;
	max-width:100%;
	box-sizing: border-box;
	padding:5px 10px 4px;
	color:#000000;
	text-align:center;
	text-transform:uppercase;
	font-weight:400;
	background-color:#ff5d5d;
	margin-bottom:5px;
}
.pc-jury-info-box {
	width:100%;
	text-align:center;
	background-color:#F2F2F2;
	padding:20px;
	margin-top:5px;
}
/*Upload Page*/
/*Upload Page*/
/*Upload Page*/
/*Upload Page*/
/*Upload Page*/

.contest-info-bar{
	border:#d9d9d9 1px solid !important;
	color:#555555 !important;
	padding:13px 20px !important;
	margin-bottom:5px;
}
.contest-info-bar-green{
	border:#d9d9d9 1px solid !important;
	color:#555555 !important;
	padding:10px 20px!important;
	background-color:#FFFFFF;
	margin-bottom:5px;
}
.contest-upload-form-box{
	margin:0 0 15px 0;
	background-color:#FFFFFF;
}
.contest-message-box{
	margin:5px 0 15px 0 !important;
	background-color:#FFFFFF;
	border: 1px solid #d8d8d8 !important;
    padding: 10px 20px !important;
}
.contest-upload-form-box form{
/*	max-width:750px !important;*/
	line-height:1.7;
}
.contest-upload-form-box h4{
	font-weight:bold !important;
	margin:0 !important;
	padding:1px !important;
}
.contest-upload-form-box .contest-button{
	margin:auto;
}
.contest-small-font{
	font-size:91% !important;
	margin:0 0 3px 0!important;
}
.contest-red-color{
	color:#CC0000 !important;
}

/*Rules and Prizes*/
/*Rules and Prizes*/
/*Rules and Prizes*/
/*Rules and Prizes*/
/*Rules and Prizes*/

.contest-rules {
	margin:0px 0 15px 0 !important;
	background-color:#FFFFFF;
	border:1px solid #d9d9d9 !important;
	padding:20px !important;
	position:relative !important;
	color:#000000 !important;
}
.contest-rules ul{
	padding:20px;
}
.contest-rules img.alignright{
	margin:7px 0 20px 20px !important;
}
.contest-rules img.alignleft{
	margin:0 20px 20px 0!important;
}
.contest-rules h1, .contest-rules h2, .pc-profile-box h1, .pc-profile-box h2 {
	margin-left:-21px;
	margin-bottom:20px;
	margin-top:0 !important;
	padding:3px 25px 5px;
	background-color: #777e85;
	display:inline-block;
	vertical-align: top;
	font-size:19px !important;

	line-height:24px !important;
	color:#FFFFFF !important;
	font-family: 'Oswald', Segoe UI, Arial !important;
	font-weight:200;
}
.contest-rules h3, .contest-rules h4 {
	font-family: 'Oswald', Segoe UI, Arial !important;
	margin:20px 0;
}

/*Top10*/
/*Top10*/
/*Top10*/
/*Top10*/
/*Top10*/
.top10wrap{
	margin:auto;
	line-height:1;
	text-align:center;
	border:1px solid #d9d9d9 !important;
	padding-top:20px;
	background-color:#FFFFFF;
}
.firstbox{
	width:220px;
	max-height:215px;
	overflow:hidden;
	border:1px solid #d9d9d9;
	padding:15px;
	box-sizing: content-box !important;
	display: inline-block;
}
.firstbox_number{
	width:102px;
	height:80px;
	float:left;
	text-align:center;
	font-size:55px !important;
	color:#FFFFFF;
	padding-top:22px;
	box-sizing: content-box !important;
	filter: alpha(opacity=100) !important;
	opacity: 1.0 !important;
	-webkit-transition: all 1s ease;
     -moz-transition: all 1s ease;
       -o-transition: all 1s ease;
      -ms-transition: all 1s ease;
          transition: all 1s ease;
}
.firstbox_image{
	width:102px;
	height:102px;
	float:right;
	filter: alpha(opacity=100) !important;
	opacity: 1.0 !important;
	-webkit-transition: all 1s ease;
     -moz-transition: all 1s ease;
       -o-transition: all 1s ease;
      -ms-transition: all 1s ease;
          transition: all 1s ease;
    -webkit-backface-visibility: hidden;
	-moz-backface-visibility: hidden;
  transform: translate3d(0px,0px,0px);
  filter: brightness(1.01);
}
.firstbox_image img{
	max-width:100% !important;
	max-height:100% !important;
	border:none !important;
	padding:0 !important;
	margin:0 !important;
}
.firstbox_image:hover, .firstbox_number:hover{
	cursor:pointer;
	filter: alpha(opacity=50) !important;
	opacity: 0.5 !important;
	-webkit-transition: all 1s ease;
     -moz-transition: all 1s ease;
       -o-transition: all 1s ease;
      -ms-transition: all 1s ease;
          transition: all 1s ease;
	-webkit-backface-visibility: hidden;
	-moz-backface-visibility: hidden;
  transform: translate3d(0px,0px,0px);
  filter: brightness(1.01);
}
.firstbox_author{
	text-transform:uppercase;
	color:#333333;
	width:100%;
	text-align:center;
	margin-top:15px;
}
.firstbox_title{
	color:#333333;
	width:100%;
	text-align:center;
	margin-top:8px;
}
.firstbox_count{
	color:#FFF;
	width:100%;
	text-align:center;
}
.firstbox_count span{
	background-color:#575757;
	padding:5px 10px;
	display:inline-block;
	margin:12px auto 0px;
}
.firstbox_share{
	font-size:16px !important;
	color:#333333;
	font-weight:bold;
	width:100%;
	text-align:center;
	margin-top:12px;
	margin-bottom:10px;
}
.firstbox_share a:link, .firstbox_share a:visited, .firstbox_share a:hover, .firstbox_share a:active{

	color:#333333 !important;
}


.pc-comment-bottom-box {
	display:table;
	margin:0 0 5px 0;
	width:100%;
	max-width:1200px;
	line-height:1;
}

.pc-comment-form-box table {
	width:100% !important;
	padding:0 !important;
	margin:0 !important;
	border:none !important;
	border-collapse:collapse !important;
	line-height:1.5 !important;

}
.pc-comment-form-box table td{
	padding:0 !important;
	margin:0 !important;
	border:none !important;
	vertical-align:top !important;
	background:none !important;
}
.pc-comment-form-box table tr{
	padding:0 !important;
	margin:0 !important;
	border:none !important;
	background:none !important;
}
.pc-comment-list-box {
	display: table-row;
	padding-left:10px;
	background-color:#F5F5F5;
}
.pc-comment-list-box.pc-comment-odd {
	background-color:#fbfbfb;
}
.pc-comment-list-box .pc-avatar-comments-list {
	width:54px;
	display:table-cell;
	vertical-align:top;
}
.pc-comment-list-box .pc-right-comments-list {
	display:table-cell;
	vertical-align:top;
	padding:10px 10px 10px 10px;
}
.pc-right-comments-list .pc-autor-comments-list {
	padding-bottom:8px;
	font-size:1em;
}
.pc-right-comments-list .pc-autor-comments-list span{
	display:inline-block;
	min-width:80px;
	font-weight: 400;

}
.pc-right-comments-list .pc-text-comments-list {
	padding-left:10px;
	font-size:1.1em;
}
.pc-comment-list-box .pc-avatar-comments-list img{
	width:44px;
	height:44px;
	margin:10px 0px 10px 10px !important;
	vertical-align:top !important;
}
.pc-comment-form-box {
	background-color:#FFF;
}
.pc-comment-form-author {
	padding:3px 0 0 0;
}
 .pc-comment-form-avatar {
	width:25px;
	height:25px;
	padding-right:10px;
}
.pc-comment-form-textarea {
	padding:10px 0 0 0;
	background-color:#FFFFFF

}
.pc-comment-form-textarea textarea{
	padding:10px;
	border:1px solid #d9d9d9;
	box-sizing: border-box;
	width:100% !important;
	min-height:100px;
	background-color:#FFFFFF
}
.pc-form-title{
	margin:0;
	padding:0 0 10px 0;
}
.pc-form-label{
	padding:0 0 5px 0;
}
.pc-form-input{
	margin:0 0 10px 0;
	width:100%;
}
.pc-form-input input{
	padding:10px;
	border:1px solid #d9d9d9;
	width:250px;
}
.pc-form-warning{
	padding:10px 10px 10px 0;
}
.halfform{
	width:50%;
	float:left;
	padding-right:20px;
	box-sizing: border-box;
  -moz-box-sizing: border-box;
  -webkit-box-sizing: border-box;
}
.halfform.pc-lev-pad{
	padding-right:0px;
}

.uploadimage{
	padding:5px;
	background-color:#FFFFFF;
	margin:20px 0 10px 0;
	box-sizing: border-box;
  -moz-box-sizing: border-box;
  -webkit-box-sizing: border-box;
  max-width:100%
}
.uploadimage{
	width:100%;
	text-align:center;
}
.contest-vote-button .fa{
	color: #FFF !important;
}
.bp-photo-contest-comment img{
	width:100px;
	float: left;
	margin: 0px 15px 15px 0px;
}
.contest-small-font-2{
	font-size:small;
}
/*Profile-section*/
/*Profile-section*/
/*Profile-section*/
/*Profile-section*/
/*Profile-section*/
/*Profile-section*/
.pc-profile-box {
	margin:0px 0 15px 0 !important;
	background-color:#FFFFFF;
	border:1px solid #d9d9d9 !important;
	padding:20px !important;
	position:relative !important;
	color:#000000 !important;
}
.pc-edit-thumb{
	text-align:center !important;
	margin:auto !important;
	border:none;
	padding:0;
}
.pc-edit-thumb img{
	max-width:100%;
	width:100%;
}
.pc-edit-image{
	text-align:center !important;
	margin:auto !important;
	border:none;
	padding:10px 0 0 0;
	text-transform: uppercase;
}


/*Share-Page*/
/*Share-Page*/
/*Share-Page*/
/*Share-Page*/
/*Share-Page*/
/*Share-Page*/
.pc-share-page {
	margin:0px 0 15px 0 !important;
	background-color:#FFFFFF;
	border:1px solid #d9d9d9 !important;
	padding:25px !important;
	position:relative !important;
	color:#000000 !important;
}
.pc-thank-you{
	width:100%;
	margin:20px auto;
	font-size:28px;
	text-align:center;
	text-transform:uppercase;
}
.pc-thank-you span{
	display:block;
	padding:10px;
	border:1px solid #d9d9d9 !important;
	color:#333;
	margin:auto;
	max-width:513px;
	background-color:#f2f2f2;
}
.pc-control-text{
	width:100%;
	margin:10px auto;
	font-size:16px;
	text-align:center;
}
.pc-control-text.pc-bigger{
	font-size:18px;
}
.pc-share-input, .pc-share-icons{
	max-width:513px;
	margin:5px auto;
	text-align:center;
}
.pc-control-img{
	width:100%;
	max-width:513px;
	margin:auto;
	text-align:center;
	padding-top:3px;
}
.pc-control-img .pc-shared-image{
	width:100%;
	max-width:513px;
}
.pc-control-img a img{
	margin: 0 auto;
	display:block;
	background-repeat: no-repeat;
	background-size: cover;
  background-position: center;

}
.pc-share-input input{
	width:100%;
	max-width:476px;
	padding:11px 12px !important;
	border:1px solid #d9d9d9 !important;
	box-sizing: border-box;
	font-size:0.8em !important;
}
ul.pc-social {
 overflow: auto!important;
 margin:auto !important;
 padding:0!important;
 display:inline-block;
 list-style:none !important;
}

ul.pc-social  li {
 list-style-type: none !important;
 list-style-image:none !important;
 float: left;
 margin:5px 5px 0 0!important;
}
ul.pc-social  li:last-child {
 margin:5px 0 0 0!important;
}
ul.pc-social  li a{
 text-decoration:none!important;
}

ul.pc-social  li i {
 background: #205D7A;
 color: #fff;
 width: 32px!important;
 font-size: 16px;
 text-align: center;
 padding-top: 9px;
 padding-bottom: 9px;
 cursor:pointer;
}
ul.pc-social li i {
 transition: all 0.2s ease-in-out;
}
ul.pc-social li i:hover {
	background:#333;
    border-radius: 28px;
}
ul.pc-social .fa-facebook {
 background:#3b5998;
}
ul.pc-social .fa-linkedin {
 background:#007bb6;
}
ul.pc-social .fa-twitter {
 background:#00aced;
}
ul.pc-social .fa-google-plus {
 background:#dd4b39;
}
ul.pc-social .fa-pinterest {
 background:#d01d15;
}
ul.pc-social .fa-tumblr {
 background:#34465d;
}
ul.pc-social .fa-reddit {
 background:#ed1c24;
}
ul.pc-social .fa-delicious {
 background:#3399ff;
}
ul.pc-social .fa-digg {
 background:#286091;
}
ul.pc-social .fa-stumbleupon {
 background:#eb4924;
}
ul.pc-social .fa-500px {
 background:#151515;
}
ul.pc-social .fa-linkedin {
 background:#007bb6;
}
ul.pc-social .fa-qrcode {
 background:#333;
 cursor:pointer;
}
ul.pc-social .fa-whatsapp {
 background:#0d9f16;
}
.wp-social-login-provider-list a{
	display:inline-block !important;
}
.pc-spacer{
	height:10px !important;
}
.pc-star-center{
	text-align:center;
}
.p-rating{
	margin:auto;
	padding:0 0 0 17px;
	width:270px;
	text-align:center;
}
.pc-social-wiget, .wp-social-login-provider-list{
	margin:0px;
	padding:5px 0 5px 0;
}
.pc-users-list{
	margin:5px auto;
}
#branding{
	display:none !important;
}
.pc-hide, .pc-load{
	display:none;
}
.pc-table-cell{
	display:table-cell;
	vertical-align:middle;
	height:44px;
	padding-bottom:2px;
}
.pc-faicons{
	font-size:1.8em !important;
}
.form-group select, .form-group input{
	margin:0 !important;
}
.contest-upload-form-box .form-group, .halfform .form-group, .pc-profile-box .form-group{
	width:100% !important;
}
.contest-detail-info.pcmobile {
	display:none;
}
.rc-rating {
  overflow: hidden;
  display: inline-block;
  position: relative;
  font-size: 1.7em;
  color: #ffffff;
}
.rc-rating-star {
  padding: 0 2px;
  margin: 0;
  cursor: pointer;
  display: block;
  float: right;
}
.rc-rating-star:after {
  position: relative;
  font-family: FontAwesome;
  content:'\f006';
}
.rc-rating-star.checked ~ .rc-rating-star:after,
.rc-rating-star.checked:after {
  content:'\f005';
}

.rc-rating:hover .rc-rating-star:after {content:'\f006';}

.rc-rating-star:hover ~ .rc-rating-star:after,
.rc-rating-star:hover:after {
  content:'\f005' !important;
}
.prewphoto{
  width: 100%;
  text-align: center;
	padding: 20px 0;
}
.prewphoto img{
  margin: auto;
  max-width: 400px;
	max-height: 300px;
}
.photo-contest .form-group {
    padding: 0px !important;
}
.photo-contest .input-group .input-group-btn {
    padding: 0 0px;
}
@media (max-width: 1100px) {
#recaptcha1, #recaptcha2  {transform:scale(0.90);-webkit-transform:scale(0.90);transform-origin:0 0;-webkit-transform-origin:0 0;}
.formbreak {
	display:block !important;
	clear: both;
}
.halfform{
	width:100%;
	float:none;
	padding-right:0px !important;
}
	}

@media (max-width: 800px) {

.pc-gal-select{
	width: 100% !important;
	margin-right:0;
	float:left;

}
.pc-gal-select-last{
	width: 100% !important;
	margin-right:0;
	float:left;

}
.pc-gal-select2 {
	width: 100% !important;
	margin-right:0;
    float: left;
}
.pc-gal-select-last2{
	width: 100% !important;
	margin-right:0;
	float:left;

}
.gallery-wrap.plusthree {
	width: calc(100% + 3px) !important;
}
.gallery-wrap.plusfive {
	width: calc(100% + 2px) !important;
}
.gallery-wrap.plussix {
	width: calc(100% + 3px) !important;
}
.contest-upload-form-box{
	width:100%;
	box-sizing: border-box;
  -moz-box-sizing: border-box;
  -webkit-box-sizing: border-box;
}

.padb {
	height:1px;
	display:block !important;
}
.bp-photo-contest-comment img{
	width:100px;
	float: none;
}
.g-recaptcha{
	width:272px;
}
#rc-imageselect, .g-recaptcha{transform:scale(0.90);-webkit-transform:scale(0.90);transform-origin:0 0;-webkit-transform-origin:0 0;}
#recaptcha1, #recaptcha2  {transform:scale(0.80);-webkit-transform:scale(0.80);transform-origin:0 0;-webkit-transform-origin:0 0;}
	}
@media (max-width: 500px) {
.contest-detail-info.pcdesktop {
	display:none !important;
}
.gallery-wrap.plusthree {
	width: calc(100% + 1px) !important;
}
.gallery-wrap.plusfive {
	width: calc(100% + 1px) !important;
}
.gallery-wrap.plussix {
	width: calc(100% + 1px) !important;
}
.contest-rules img{
	float:none !important;
	max-width:100% !important;
	width:100% !important;
	margin:0 0 20px 0 !important;
	padding:0 !important;
	box-sizing: border-box;
  -moz-box-sizing: border-box;
  -webkit-box-sizing: border-box;

}

.contest-detail-info.pcmobile{
	display:block !important;
}

.halfform{
	width:100%;
	float:none;
}

.contest-upload-form-box form {
}
.contest-upload-form-box input {
}
.contest-rules img.alignright, .contest-rules img.alignleft{
	margin:0 0 20px 0 !important;
}
.uploadimage{
	margin:10px auto;
	width:100%;
	box-sizing: border-box;
  -moz-box-sizing: border-box;
  -webkit-box-sizing: border-box;
}
.pc-styled-button, .contest-button {
	width:100% !important;
	box-sizing: border-box;
  -moz-box-sizing: border-box;
  -webkit-box-sizing: border-box;
  margin:0 0 0 0 !important;
}
.g-recaptcha{
	width:218px;
}
#rc-imageselect, .g-recaptcha {transform:scale(0.72);-webkit-transform:scale(0.72);transform-origin:0 0;-webkit-transform-origin:0 0;}
#recaptcha1, #recaptcha2  {transform:scale(0.60);-webkit-transform:scale(0.60);transform-origin:0 0;-webkit-transform-origin:0 0;}
}
@media (max-width: 340px) {
.firstbox{
	width:calc(100% - 32px);
}
.g-recaptcha{
	width:197px;
}
#rc-imageselect, .g-recaptcha, #recaptcha1, #recaptcha2  {transform:scale(0.65);-webkit-transform:scale(0.65);transform-origin:0 0;-webkit-transform-origin:0 0;}
}
@media (max-width: 500px) {
.rc-rating-star {
    font-size: 16px;
}
}
