/*! Forms Plus: CSS.1.2.2 - ' + '2016-01-10 */
.modern-p-form .p-block-description,
.modern-p-form .p-block-title {
  display: block;
  margin: 5px 0;
}
.modern-p-form .p-block-title {
  font-size: 18px;
}
.modern-p-form .p-block-title + .p-block-description {
  margin-top: 10px;
}
.modern-p-form .panel,
.modern-p-form .alert {
  -webkit-border-radius: 0;
  border-radius: 0;
}
.modern-p-form .panel-heading {
  border-top-left-radius: 0;
  border-top-right-radius: 0;
}
.modern-p-form .panel-heading ~ .panel-body {
  border-top-left-radius: 0;
  border-top-right-radius: 0;
}
.modern-p-form .panel-body {
  -webkit-border-radius: 0;
  border-radius: 0;
}
.modern-p-form .p-sm-row {
  margin: 0 -5px;
}
.modern-p-form .p-sm-row > .p-col {
  padding-left: 5px;
  padding-right: 5px;
}
.modern-p-form .p-md-row {
  margin: 0 -10px;
}
.modern-p-form .p-md-row > .p-col {
  display: block;
  padding-left: 10px;
  padding-right: 10px;
}
.modern-p-form .panel-fp {
  border-color: #e0e0e0;
}
.modern-p-form .panel-fp > .panel-heading {
  background-color: #f5f5f5;
  border-bottom: 1px solid #e0e0e0;
}
.modern-p-form .panel-fp > .panel-body {
  background-color: #fff;
}
.modern-p-form .progress-bar-fp {
  background-color: #333;
}
.modern-p-form .p-buttons {
  padding: 15px 0 0;
}
.modern-p-form .p-buttons .pc-btn {
  min-width: 90px;
}
.modern-p-form .pc-btn {
  padding: 0 18px;
  color: #fff;
  background-color: #333;
  -webkit-border-radius: 0;
  border-radius: 0;
  font-size: 18px;
  line-height: 44px;
  border: 0 none;
}
.modern-p-form .pc-btn,
.modern-p-form .p-check-block:after,
.modern-p-form .p-radio-color .p-color-block,
.modern-p-form .p-field-cb,
.modern-p-form .form-control,
.modern-p-form .input-group-btn .pc-btn,
.modern-p-form .input-group-addon .p-addon-bg,
.modern-p-form .input-group-icon,
.modern-p-form .p-select-arrow,
.modern-p-form .p-popup .p-form,
.modern-p-form .p-rating .p-field-cancel,
.modern-p-form .nav-tabs > li > label {
  -webkit-transition: all 0.2s linear 0s;
  -moz-transition: all 0.2s linear 0s;
  -o-transition: all 0.2s linear 0s;
  -ms-transition: all 0.2s linear 0s;
  transition: all 0.2s linear 0s;
}
.modern-p-form .pc-btn.p-active-btn,
.modern-p-form .pc-btn:hover,
.modern-p-form .pc-btn:focus {
  /*background-color: #474747;*/
}
.modern-p-form .pc-btn + .pc-btn {
  margin-left: 6px;
}
.modern-p-form .pc-btn-lg {
  font-size: 22px;
  line-height: 52px;
}
.modern-p-form .pc-btn-sm {
  font-size: 16px;
  line-height: 24px;
  padding: 3px 12px 5px;
}
.modern-p-form .pc-btn-xs {
  font-size: 13px;
  line-height: 14px;
  padding: 0 8px 2px;
}
.modern-p-form .p-social-btn {
  padding: 0;
  width: 44px;
  font-size: 24px;
}
.modern-p-form .pc-btn-group .pc-btn + .pc-btn {
  margin-left: 1px;
}
.modern-p-form .p-show-block,
.modern-p-form .p-check-next,
.modern-p-form .p-show-block-content {
  display: none;
}
.modern-p-form .p-form-sg,
.modern-p-form .p-form-cg {
  zoom: 1;
}
.modern-p-form .p-form-sg:before,
.modern-p-form .p-form-cg:before,
.modern-p-form .p-form-sg:after,
.modern-p-form .p-form-cg:after {
  content: "";
  display: table;
}
.modern-p-form .p-form-sg:after,
.modern-p-form .p-form-cg:after {
  clear: both;
}
.modern-p-form .p-switch,
.modern-p-form .p-radioswitch {
  margin: 10px 0;
}
.modern-p-form .radio,
.modern-p-form .checkbox,
.modern-p-form .p-switch,
.modern-p-form .p-radioswitch {
  margin-top: 10px;
}
.modern-p-form .radio label,
.modern-p-form .checkbox label,
.modern-p-form .p-switch label,
.modern-p-form .p-radioswitch label {
  padding-left: 0;
  line-height: 24px;
  margin: 0;
  cursor: pointer;
  color: #666;
}
.modern-p-form .radio label a,
.modern-p-form .checkbox label a,
.modern-p-form .p-switch label a,
.modern-p-form .p-radioswitch label a {
  color: #333;
  text-decoration: underline;
}
.modern-p-form .radio label a:hover,
.modern-p-form .checkbox label a:hover,
.modern-p-form .p-switch label a:hover,
.modern-p-form .p-radioswitch label a:hover {
  text-decoration: none;
}
.modern-p-form .p-check-active-icon,
.modern-p-form .p-check-icon {
  width: 24px;
  height: 24px;
}
.modern-p-form .p-check-active-icon,
.modern-p-form .p-check-icon,
.modern-p-form .p-switch-icon {
  display: inline-block;
  vertical-align: top;
  cursor: pointer;
  margin-right: 5px;
  position: relative;
}
.modern-p-form .p-check-active-icon {
  display: none;
  color: #333;
}
.modern-p-form .p-check-block {
  border: 2px solid #666;
  background-color: #fff;
}
.modern-p-form .p-check-block,
.modern-p-form .p-file-wrap [type="file"],
.modern-p-form .p-preview-bg,
.modern-p-form .p-no-preview,
.modern-p-form .p-preview,
.modern-p-form .p-preview * {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
}
.modern-p-form .p-check-block:after {
  content: "";
  opacity: 0;
  -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=0)";
  filter: alpha(opacity=0);
}
.modern-p-form .p-check-block:after,
.modern-p-form .p-switch-icon:after {
  width: 14px;
  height: 14px;
  position: absolute;
  left: 3px;
  top: 3px;
  background-color: #333;
  border: 0 none;
}
.modern-p-form .p-check-middle {
  font-size: 20px;
  line-height: 24px;
  text-align: center;
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
}
.modern-p-form .p-check-icon .p-check-middle {
  color: #666;
}
.modern-p-form .p-check-big-icons .p-radioswitch + .p-radioswitch,
.modern-p-form .p-check-big-icons .p-switch + .p-switch,
.modern-p-form .p-check-big-icons .radio + .radio,
.modern-p-form .p-check-big-icons .checkbox + .checkbox {
  margin-top: 10px;
}
.modern-p-form .p-check-big-icons .p-check-icon {
  margin-right: 15px;
  margin-left: 10px;
}
.modern-p-form .p-check-big-icons .p-check-middle {
  left: -10px;
  right: -10px;
  top: -10px;
  font-size: 32px;
  line-height: 44px;
}
.modern-p-form .p-switch-icon {
  width: 48px;
  height: 24px;
  border: 2px solid #666;
  background-color: #fff;
}
.modern-p-form .p-switch-icon:before {
  content: attr(data-unchecked);
  font-size: 13px;
  line-height: 13px;
  color: #666;
  text-align: right;
  width: 23px;
  height: 13px;
  position: absolute;
  left: 16px;
  top: 2px;
}
.modern-p-form .p-switch-icon:before,
.modern-p-form .p-switch-icon:after {
  -webkit-transition: left 0.2s linear 0s;
  -moz-transition: left 0.2s linear 0s;
  -o-transition: left 0.2s linear 0s;
  -ms-transition: left 0.2s linear 0s;
  transition: left 0.2s linear 0s;
}
.modern-p-form .p-switch-icon:after {
  content: "";
}
.modern-p-form .p-check-next:checked + .p-check-container .p-check-active-icon,
.modern-p-form :checked ~ .p-check-active-icon {
  display: inline-block;
}
.modern-p-form .p-check-next:checked + .p-check-container .p-check-active-icon + .p-check-icon,
.modern-p-form :checked ~ .p-check-active-icon + .p-check-icon {
  display: none;
}
.modern-p-form .p-check-next:checked + .p-check-container .p-check-icon .p-check-middle,
.modern-p-form :checked ~ .p-check-icon .p-check-middle {
  color: #333;
}
.modern-p-form .p-check-next:checked + .p-check-container .p-check-icon .p-check-block,
.modern-p-form :checked ~ .p-check-icon .p-check-block {
  border-color: #333;
}
.modern-p-form .p-check-next:checked + .p-check-container .p-check-icon .p-check-block:after,
.modern-p-form :checked ~ .p-check-icon .p-check-block:after {
  opacity: 1;
  -ms-filter: none;
  filter: none;
}
.modern-p-form .p-check-next:checked + .p-check-container .p-switch-icon,
.modern-p-form :checked ~ .p-switch-icon {
  border-color: #333;
}
.modern-p-form .p-check-next:checked + .p-check-container .p-switch-icon:before,
.modern-p-form :checked ~ .p-switch-icon:before {
  content: attr(data-checked);
  color: #333;
  text-align: left;
  left: 5px;
}
.modern-p-form .p-check-next:checked + .p-check-container .p-switch-icon:after,
.modern-p-form :checked ~ .p-switch-icon:after {
  left: 27px;
}
.modern-p-form .p-check-next[type="radio"] + .p-check-container .p-check-block,
.modern-p-form .p-check-next[type="radio"] + .p-check-container .p-check-block:after {
  -webkit-border-radius: 50%;
  border-radius: 50%;
}
.modern-p-form .checkbox label:hover .p-check-active-icon,
.modern-p-form .radio label:hover .p-check-active-icon,
.modern-p-form .p-check-next + .p-check-container:hover .p-check-active-icon,
.modern-p-form .checkbox label:hover .p-check-icon .p-check-middle,
.modern-p-form .radio label:hover .p-check-icon .p-check-middle,
.modern-p-form .p-check-next + .p-check-container:hover .p-check-icon .p-check-middle {
  color: #525252;
}
.modern-p-form .checkbox label:hover .p-check-icon .p-check-block,
.modern-p-form .radio label:hover .p-check-icon .p-check-block,
.modern-p-form .p-check-next + .p-check-container:hover .p-check-icon .p-check-block {
  border-color: #525252;
}
.modern-p-form .checkbox [type="checkbox"],
.modern-p-form .radio [type="radio"],
.modern-p-form .p-switch [type="checkbox"],
.modern-p-form .p-radioswitch [type="radio"] {
  opacity: 0;
  -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=0)";
  filter: alpha(opacity=0);
  position: absolute;
  margin: 0;
  width: 24px;
  height: 24px;
}
.modern-p-form .radio .p-check-block,
.modern-p-form .radio .p-check-block:after {
  -webkit-border-radius: 50%;
  border-radius: 50%;
}
.modern-p-form .p-switch,
.modern-p-form .p-radioswitch {
  margin: 10px 0;
}
.modern-p-form .p-switch label,
.modern-p-form .p-radioswitch label {
  margin: 0;
  cursor: pointer;
}
.modern-p-form .p-switch label:hover .p-switch-icon,
.modern-p-form .p-radioswitch label:hover .p-switch-icon {
  border-color: #525252;
}
.modern-p-form .p-radioswitch .p-switch-icon,
.modern-p-form .p-radioswitch .p-switch-icon:after {
  -webkit-border-radius: 12px;
  border-radius: 12px;
}
.modern-p-form .p-radioswitch + .p-radioswitch,
.modern-p-form .p-switch + .p-switch,
.modern-p-form .radio + .radio,
.modern-p-form .checkbox + .checkbox {
  margin-top: -2px;
}
.modern-p-form .pt-form-inline .p-radioswitch,
.modern-p-form .pt-form-inline .p-switch,
.modern-p-form .pt-form-inline .radio,
.modern-p-form .pt-form-inline .checkbox {
  display: inline-block;
  vertical-align: middle;
  margin-top: 0;
  margin-bottom: 0;
  padding: 10px 0;
}
.modern-p-form .pt-form-inline .p-radioswitch,
.modern-p-form .pt-form-inline .p-switch,
.modern-p-form .pt-form-inline .radio,
.modern-p-form .pt-form-inline .checkbox {
  margin-right: 20px;
}
.modern-p-form .pt-form-inline .p-radioswitch:last-child,
.modern-p-form .pt-form-inline .p-switch:last-child,
.modern-p-form .pt-form-inline .radio:last-child,
.modern-p-form .pt-form-inline .checkbox:last-child {
  margin-right: 0;
}
.modern-p-form .p-picture-pick .p-preview-name {
  background-color: rgba(0,0,0,0.7);
}
.modern-p-form .p-picture-pick .p-preview-name,
.modern-p-form .p-block-pick .p-check-container {
  -webkit-transition: all 0.3s linear 0s;
  -moz-transition: all 0.3s linear 0s;
  -o-transition: all 0.3s linear 0s;
  -ms-transition: all 0.3s linear 0s;
  transition: all 0.3s linear 0s;
}
.modern-p-form .p-picture-pick [type="radio"],
.modern-p-form .p-picture-pick [type="checkbox"] {
  display: none;
}
.modern-p-form .p-picture-pick [type="radio"] ~ .p-check-icon .p-check-block,
.modern-p-form .p-picture-pick [type="radio"] ~ .p-check-icon .p-check-block:after {
  -webkit-border-radius: 50%;
  border-radius: 50%;
}
.modern-p-form .p-picture-pick .p-check-icon {
  position: absolute;
  left: 0;
  top: 0;
  z-index: 100;
}
.modern-p-form .p-picture-pick .p-check-icon .p-check-block {
  background-color: transparent !important;
  border-color: transparent !important;
}
.modern-p-form .p-picture-pick :checked ~ .p-preview-name {
  background-color: rgba(31,31,31,0.7);
}
.modern-p-form .p-picture-pick:hover .p-preview-name {
  background-color: rgba(51,51,51,0.7);
}
.modern-p-form .p-block-pick label {
  width: 100%;
  cursor: pointer;
}
.modern-p-form .p-block-pick .p-block-title,
.modern-p-form .p-block-pick .p-block-description {
  padding-right: 5px;
  padding-left: 5px;
}
.modern-p-form .p-block-pick .p-check-container,
.modern-p-form .p-block-pick .row {
  display: block;
}
.modern-p-form .p-block-pick :checked ~ .p-check-container {
  background-color: #e6e6e6;
}
.modern-p-form .p-block-pick:hover .p-check-container {
  background-color: #f0f0f0;
}
.modern-p-form .p-check-input .p-switch-icon,
.modern-p-form .p-check-input .p-check-icon {
  margin-top: 10px;
}
.modern-p-form .p-check-input .form-group {
  display: inline-block;
  vertical-align: top;
  margin-bottom: 0;
}
.modern-p-form .p-show-block-heading {
  display: block;
  padding-left: 0;
  line-height: 24px;
  margin: 0 0 15px;
  cursor: pointer;
  color: #666;
  font-size: 16px;
}
.modern-p-form .p-show-block-heading a {
  color: #333;
  text-decoration: underline;
}
.modern-p-form .p-show-block-heading a:hover {
  text-decoration: none;
}
.modern-p-form .p-show-block-heading hr {
  margin: 13px 0 0;
}
.modern-p-form .p-show-block-heading > .pull-left .p-label {
  margin-right: 12px;
}
.modern-p-form .p-show-block-heading > .pull-right .p-label {
  margin-left: 12px;
}
.modern-p-form .p-show-block-heading > .pull-right .p-check-icon,
.modern-p-form .p-show-block-heading > .pull-right .p-check-active-icon {
  margin-right: 0;
  margin-left: 5px;
}
.modern-p-form .p-show-block-content {
  margin-bottom: 15px;
}
.modern-p-form .p-show-block:checked + .p-show-block-heading:hover,
.modern-p-form .p-show-block-heading:hover {
  color: #525252;
}
.modern-p-form .p-show-block:checked + .p-show-block-heading {
  color: #333;
}
.modern-p-form .p-show-block:checked + .p-show-block-heading + .p-show-block-content {
  display: block;
}
.modern-p-form .p-color-block {
  width: 32px;
  height: 32px;
  display: inline-block;
  vertical-align: top;
}
.modern-p-form .p-radio-color {
  float: left;
  padding: 1px;
  margin: 6px 0;
}
.modern-p-form .p-radio-color label {
  margin: 0;
  line-height: 32px;
  cursor: pointer;
}
.modern-p-form .p-radio-color .p-color-block {
  border: 1px solid #d9d9d9;
}
.modern-p-form .p-radio-color [type="checkbox"],
.modern-p-form .p-radio-color [type="radio"] {
  display: none;
}
.modern-p-form .p-radio-color [type="checkbox"]:checked ~ .p-color-block,
.modern-p-form .p-radio-color [type="radio"]:checked ~ .p-color-block {
  border-color: #333;
  border-width: 2px;
  -webkit-box-shadow: 0 0 5px #000 inset, 0 0 2px #000;
  box-shadow: 0 0 5px #000 inset, 0 0 2px #000;
}
.modern-p-form .p-form-colorpick {
  zoom: 1;
  margin: 6px -1px;
}
.modern-p-form .p-form-colorpick:before,
.modern-p-form .p-form-colorpick:after {
  content: "";
  display: table;
}
.modern-p-form .p-form-colorpick:after {
  clear: both;
}
.modern-p-form .p-form-colorpick .p-radio-color {
  float: left;
  padding: 1px;
  margin: 0;
}
.modern-p-form {
  zoom: 1;
  cursor: default;
  font-size: 14px;
  line-height: 1.2;
  color: #666;
  text-align: left;
}
.modern-p-form:before,
.modern-p-form:after {
  content: "";
  display: table;
}
.modern-p-form:after {
  clear: both;
}
.modern-p-form .p-form {
  zoom: 1;
  position: relative;
  z-index: 0;
}
.modern-p-form .p-form:before,
.modern-p-form .p-form:after {
  content: "";
  display: table;
}
.modern-p-form .p-form:after {
  clear: both;
}
.modern-p-form .p-form-bg,
.modern-p-form .p-form {
  background: #fff;
}
.modern-p-form .p-sm-offs {
  margin-bottom: 5px;
}
.modern-p-form .p-table {
  display: table;
  border-collapse: separate;
  width: 100%;
}
.modern-p-form .p-cell,
.modern-p-form .p-min-cell {
  display: table-cell;
  vertical-align: middle;
}
.modern-p-form .p-min-cell {
  width: 1%;
  white-space: nowrap;
}
.modern-p-form .p-price-column {
  width: 120px;
  text-align: center;
}
.modern-p-form .p-action-column {
  width: 50px;
  text-align: center;
  min-height: 1px;
}
.modern-p-form .p-total-block {
  zoom: 1;
  white-space: nowrap;
  font-size: 17px;
  line-height: 26px;
}
.modern-p-form .p-total-block:before,
.modern-p-form .p-total-block:after {
  content: "";
  display: table;
}
.modern-p-form .p-total-block:after {
  clear: both;
}
.modern-p-form .p-total-block .p-price-column {
  float: left;
}
.modern-p-form .p-colored-text {
  color: #333;
}
.modern-p-form .p-alt-colored-text {
  color: #333;
}
.modern-p-form .p-colored-link {
  cursor: pointer;
  color: #333;
  text-decoration: none;
}
.modern-p-form .p-colored-link,
.modern-p-form .p-action-link {
  -webkit-transition: all 0.1s linear 0s;
  -moz-transition: all 0.1s linear 0s;
  -o-transition: all 0.1s linear 0s;
  -ms-transition: all 0.1s linear 0s;
  transition: all 0.1s linear 0s;
}
.modern-p-form .p-colored-link:hover {
  color: #474747;
}
.modern-p-form .p-action-link {
  cursor: pointer;
  color: #525252;
  text-decoration: none;
}
.modern-p-form .p-action-link:hover {
  color: #474747;
}
.modern-p-form .p-social-bar {
  zoom: 1;
  margin: -3px;
}
.modern-p-form .p-social-bar:before,
.modern-p-form .p-social-bar:after {
  content: "";
  display: table;
}
.modern-p-form .p-social-bar:after {
  clear: both;
}
.modern-p-form .p-social-bar .p-social-btn {
  margin: 3px;
  float: left;
}
.modern-p-form hr {
  border-color: #e0e0e0;
}
.modern-p-form hr.p-flat {
  margin-top: 15px;
  border-top: 8px solid #f5f5f5;
}
.modern-p-form .p-table > thead > tr > td,
.modern-p-form .p-table > tbody > tr > td,
.modern-p-form .p-table > tfoot > tr > td,
.modern-p-form .p-table > thead > tr > th,
.modern-p-form .p-table > tbody > tr > th,
.modern-p-form .p-table > tfoot > tr > th {
  border-color: #e0e0e0;
}
.modern-p-form .p-table.table-striped > thead {
  background-color: #f5f5f5;
  border: 1px solid #e0e0e0;
}
.modern-p-form .p-table.table-striped > tbody > tr:nth-of-type(odd) {
  background-color: transparent;
}
.modern-p-form .p-table.table-striped > tbody > tr:nth-of-type(even) {
  background-color: #f5f5f5;
}
.modern-p-form .p-table.table-striped > tbody > tr > td,
.modern-p-form .p-table.table-striped > tfoot > tr > td,
.modern-p-form .p-table.table-striped > tbody > tr > th,
.modern-p-form .p-table.table-striped > tfoot > tr > th {
  border: 0 none;
}
.modern-p-form .p-table.table-bordered {
  border: 1px solid #e0e0e0;
}
.modern-p-form .p-table.table-bordered > thead > tr > td,
.modern-p-form .p-table.table-bordered > tbody > tr > td,
.modern-p-form .p-table.table-bordered > tfoot > tr > td,
.modern-p-form .p-table.table-bordered > thead > tr > th,
.modern-p-form .p-table.table-bordered > tbody > tr > th,
.modern-p-form .p-table.table-bordered > tfoot > tr > th {
  border: 1px solid #e0e0e0;
}
.modern-p-form .p-table.table-bordered > thead > tr > td,
.modern-p-form .p-table.table-bordered > thead > tr > th {
  border-bottom-width: 2px;
}
.modern-p-form label {
  font-weight: normal;
  margin-bottom: 8px;
  color: #666;
}
.modern-p-form textarea.form-control {
  height: 106px !important;
  min-height: 46px;

  resize: vertical;
}
.modern-p-form textarea.form-control:focus {
  height: 126px;
}
.modern-p-form textarea.form-control.resizenone:focus {
  height: 106px;
}
.modern-p-form select.form-control {
  cursor: pointer;
}
.modern-p-form select.form-control[multiple],
.modern-p-form select.form-control[size] {
  height: auto;
  padding: 0;
}
.modern-p-form select.form-control option {
  line-height: 20px;
  padding: 3px 12px;
  background-color: #fff;
}
.modern-p-form .p-visibility-5 {
  opacity: 0.5;
  -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=50)";
  filter: alpha(opacity=50);
}
.modern-p-form .p-visibility-6 {
  opacity: 0.6;
  -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=60)";
  filter: alpha(opacity=60);
}
.modern-p-form .p-visibility-7 {
  opacity: 0.7;
  -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=70)";
  filter: alpha(opacity=70);
}
.modern-p-form .p-visibility-8 {
  opacity: 0.8;
  -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=80)";
  filter: alpha(opacity=80);
}
.modern-p-form .p-visibility-9 {
  opacity: 0.9;
  -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=90)";
  filter: alpha(opacity=90);
}
.modern-p-form .p-icon-1x {
  font-size: 68px;
  line-height: 88px;
}
.modern-p-form .p-icon-2x {
  font-size: 112px;
  line-height: 158px;
}
.modern-p-form .p-icon-3x {
  font-size: 158px;
  line-height: 248px;
}

.modern-p-form .p-form-with-back {
  position: relative;
}
.modern-p-form .p-form-with-back .p-form-content {
  min-height: 200px;
}
.modern-p-form .p-form-back-wrap {
  margin-bottom: 15px;
}
.modern-p-form .p-form-back {
  border: 0 none;
  min-width: 100%;
  min-height: 120px;
}
@media (min-width: 768px) {
  .modern-p-form .p-form-back-wrap {
    margin-bottom: 0;
    position: absolute;
    left: 0;
    top: 0;
    right: 0;
    bottom: 0;
  }
  .modern-p-form .p-shadowed .p-form-with-back,
  .modern-p-form .p-inset .p-form-with-back {
    margin-left: -20px;
    margin-right: -20px;
  }
  .modern-p-form .p-bordered .p-form-with-back {
    margin-left: -19px;
    margin-right: -19px;
  }
  .modern-p-form .p-form-back {
    min-height: 100%;
  }
  .modern-p-form .p-form-with-back .p-form-content {
    zoom: 1;
    position: relative;
    padding: 10px 20px 0;
  }
  .modern-p-form .p-form-with-back .p-form-content:before,
  .modern-p-form .p-form-with-back .p-form-content:after {
    content: "";
    display: table;
  }
  .modern-p-form .p-form-with-back .p-form-content:after {
    clear: both;
  }
  .modern-p-form .p-form-with-back .p-form-content .p-form-bg {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
  }
}
.modern-p-form .fa {
  position: relative;
}
.modern-p-form .p-title .fa-list,
.modern-p-form .p-subtitle .fa-list {
  top: 1px;
}
.modern-p-form .p-title .fa-comment,
.modern-p-form .p-subtitle .fa-comment,
.modern-p-form .p-title .fa-comment-o,
.modern-p-form .p-subtitle .fa-comment-o,
.modern-p-form .p-title .fa-comments,
.modern-p-form .p-subtitle .fa-comments,
.modern-p-form .p-title .fa-comments-o,
.modern-p-form .p-subtitle .fa-comments-o {
  top: -2px;
}
.modern-p-form .form-group .fa-plus {
  top: 1px;
}
.modern-p-form .form-group .fa-envelope-o,
.modern-p-form .form-group .fa-envelope,
.modern-p-form .form-group .fa-file-text-o,
.modern-p-form .form-group .fa-file-text,
.modern-p-form .form-group .fa-search,
.modern-p-form .form-group .fa-cc-amex,
.modern-p-form .form-group .fa-cc-stripe,
.modern-p-form .form-group .fa-cc-discover,
.modern-p-form .form-group .fa-cc-visa,
.modern-p-form .form-group .fa-cc-mastercard,
.modern-p-form .form-group .fa-credit-card,
.modern-p-form .form-group .fa-cc-paypal,
.modern-p-form .form-group .fa-male,
.modern-p-form .form-group .fa-female {
  top: -1px;
}
.modern-p-form .form-group .fa-pencil-square-o,
.modern-p-form .form-group .fa-pencil-square,
.modern-p-form .form-group .fa-search {
  left: 1px;
}
.modern-p-form .form-group .fa-map-marker {
  top: -2px;
}
.modern-p-form .form-group {
  margin-bottom: 15px;
  position: relative;
}
.modern-p-form .p-label-offs {
  padding-top: 30px;
}
.modern-p-form .form-control,
.modern-p-form .input-group-addon {
  -webkit-border-radius: 0;
  border-radius: 0;
}
.modern-p-form .p-field-cb {
  background-color: #fff;
  border: 1px solid #ccc;
}
.modern-p-form .p-field-cb {
  position: absolute;
  left: 0;
  bottom: 0;
  width: 100%;
  height: 100%;
}
.modern-p-form .form-control {
  -webkit-box-shadow: none;
  box-shadow: none;
  height: 44px !important;
  padding: 11px 12px;
  line-height: 20px;
  border: 1px solid #ccc;
  color: #666;
  background-color: #fff;
  outline: none;
}
.modern-p-form .form-control::-webkit-input-placeholder {
  color: #8c8c8c;
  font-size: 13px;
}
.modern-p-form .form-control::-moz-placeholder {
  color: #8c8c8c;
  font-size: 13px;
}
.modern-p-form .form-control:-moz-placeholder {
  color: #8c8c8c;
  font-size: 13px;
}
.modern-p-form .form-control:-ms-input-placeholder {
  color: #8c8c8c;
  font-size: 13px;
}
.modern-p-form .form-control[type="password"] {
  font-family: "Helvetica Neue", Helvetica, Arial, sans-serif;
}
.modern-p-form .form-control:focus,
.modern-p-form .form-control:hover,
.modern-p-form .input-group:focus .form-control,
.modern-p-form .input-group:hover .form-control,
.modern-p-form .p-file-wrap [type="file"]:hover ~ .input-group .form-control {
  border-color: #333;
}
.modern-p-form .form-control:focus ~ .input-group-icon,
.modern-p-form .form-control:hover ~ .input-group-icon,
.modern-p-form .input-group:focus .form-control ~ .input-group-icon,
.modern-p-form .input-group:hover .form-control ~ .input-group-icon,
.modern-p-form .p-file-wrap [type="file"]:hover ~ .input-group .form-control ~ .input-group-icon,
.modern-p-form .form-control:focus ~ .p-select-arrow,
.modern-p-form .form-control:hover ~ .p-select-arrow,
.modern-p-form .input-group:focus .form-control ~ .p-select-arrow,
.modern-p-form .input-group:hover .form-control ~ .p-select-arrow,
.modern-p-form .p-file-wrap [type="file"]:hover ~ .input-group .form-control ~ .p-select-arrow,
.modern-p-form .form-control:focus ~ .input-group-addon .p-addon-bg,
.modern-p-form .form-control:hover ~ .input-group-addon .p-addon-bg,
.modern-p-form .input-group:focus .form-control ~ .input-group-addon .p-addon-bg,
.modern-p-form .input-group:hover .form-control ~ .input-group-addon .p-addon-bg,
.modern-p-form .p-file-wrap [type="file"]:hover ~ .input-group .form-control ~ .input-group-addon .p-addon-bg {
  border-color: #474747;
  background-color: #333;
  color: #fff;
}
.modern-p-form .form-control:focus ~ .p-field-cb,
.modern-p-form .form-control:hover ~ .p-field-cb,
.modern-p-form .input-group:focus .form-control ~ .p-field-cb,
.modern-p-form .input-group:hover .form-control ~ .p-field-cb,
.modern-p-form .p-file-wrap [type="file"]:hover ~ .input-group .form-control ~ .p-field-cb {
  border-color: #333;
}
.modern-p-form .form-control:focus ~ .p-tooltip {
  display: block;
}
.modern-p-form .p-form-chars {
  margin: -4px -2px 0;
}
.modern-p-form .p-form-chars .p-field-chars-1 {
  width: 38px;
  display: inline-block;
  padding: 4px 2px 0;
}
.modern-p-form .p-form-chars .p-field-chars-2 {
  width: 51px;
  display: inline-block;
  padding: 4px 2px 0;
}
.modern-p-form .p-form-chars .p-field-chars-3 {
  width: 64px;
  display: inline-block;
  padding: 4px 2px 0;
}
.modern-p-form .p-form-chars .p-field-chars-4 {
  width: 77px;
  display: inline-block;
  padding: 4px 2px 0;
}
.modern-p-form .p-form-chars .p-field-chars-5 {
  width: 90px;
  display: inline-block;
  padding: 4px 2px 0;
}
.modern-p-form .p-form-chars .p-field-chars-6 {
  width: 103px;
  display: inline-block;
  padding: 4px 2px 0;
}
.modern-p-form .input-group {
  width: 100%;
  margin-bottom: 0;
}
.modern-p-form .input-group .form-control {
  background-color: transparent !important;
  border-color: transparent !important;
}
.modern-p-form .input-group-icon,
.modern-p-form .p-select-arrow,
.modern-p-form .input-group-addon,
.modern-p-form .input-group-btn {
  font-size: 18px;
  z-index: 2;
  position: relative;
}
.modern-p-form .input-group-addon,
.modern-p-form .input-group-icon,
.modern-p-form .p-select-arrow {
  color: #666;
}
.modern-p-form .input-group-btn .pc-btn,
.modern-p-form .input-group-addon .p-addon-bg,
.modern-p-form .input-group-icon,
.modern-p-form .p-select-arrow {
  display: block;
  line-height: 42px;
  text-align: center;
}
.modern-p-form .input-group-addon .p-addon-bg,
.modern-p-form .input-group-btn {
  min-width: 44px;
}
.modern-p-form .input-group-addon .p-addon-bg,
.modern-p-form .input-group-icon,
.modern-p-form .p-select-arrow {
  background-color: #f2f2f2;
  border: 1px solid #ccc;
}
.modern-p-form .input-group-btn,
.modern-p-form .input-group-addon {
  padding: 0;
}
.modern-p-form .input-group-addon {
  border: 0 none;
  background-color: transparent;
}
.modern-p-form .input-group-addon .p-addon-bg {
  padding: 0 8px;
}
.modern-p-form .input-group-icon,
.modern-p-form .p-select-arrow {
  width: 44px;
  height: 44px;
}
.modern-p-form .p-has-icon .form-control {
  padding-left: 56px !important;
}
.modern-p-form .p-has-icon .input-group-icon {
  position: absolute;
  left: 0;
  top: 0;
}
.modern-p-form .p-custom-arrow .p-select-arrow {
  pointer-events: none;
  position: absolute;
  top: 0;
  right: 0;
}
.modern-p-form .p-custom-arrow select {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  padding-right: 0;
}
@media screen and (-webkit-min-device-pixel-ratio: 0) {
  .modern-p-form .p-custom-arrow select {
    padding-right: 56px;
  }
}
.modern-p-form .input-group-btn .pc-btn {
  border: 0 none;
  background-color: #666;
  margin: 0 !important;
  min-width: 100%;
  line-height: 44px;
}
.modern-p-form .input-group-btn .pc-btn:hover,
.modern-p-form .input-group-btn .pc-btn:focus,
.modern-p-form .p-file-wrap [type="file"]:hover ~ .input-group .input-group-btn .pc-btn {
  background-color: #474747;
}
.modern-p-form .p-tight {
  margin-bottom: 15px;
}
.modern-p-form .p-tight .form-group {
  margin-bottom: 0;
}
.modern-p-form .p-file-wrap {
  position: relative;
  z-index: 1;
}
.modern-p-form .p-file-wrap [type="file"] {
  opacity: 0;
  -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=0)";
  filter: alpha(opacity=0);
  cursor: pointer;
  z-index: 5;
}
.modern-p-form .p-file-wrap ~ .input-group [type="text"] {
  background-color: #fff;
}

.modern-p-form .p-preview-bg {
  display: none;
  opacity: 0;
  -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=0)";
  filter: alpha(opacity=0);
  background-color: #000;
  z-index: 30;
}
.modern-p-form .p-preview-name {
  position: absolute;
  left: 0;
  bottom: 0;
  width: 100%;
  padding: 5px 10px;
  z-index: 40;
}
.modern-p-form .p-preview {

  z-index: 20;
}
.modern-p-form .p-no-preview {
  background-color: #e6e6e6;
  border: 1px solid #d9d9d9;
  z-index: 10;
}
.modern-p-form .p-picture-solo {
  position: relative;
  padding-top: 56%;
  z-index: 1;
  color: #fff;
  cursor: pointer;
  display: block;
}
.modern-p-form .p-picture-solo .p-middle-text {
  position: absolute;
  left: 0;
  top: 50%;
  width: 100%;
  padding: 0 10px;
  margin-top: -32px;
  font-size: 48px;
  line-height: 64px;
  text-align: center;
}
.modern-p-form .form-inline {
  margin-left: -15px;
  margin-right: -15px;
}
.modern-p-form .form-inline .form-group {
  margin-right: 15px;
  margin-left: 15px;
}
.modern-p-form .form-inline .p-rating {
  display: inline-block;
  vertical-align: top;
}
.modern-p-form .form-inline > label,
.modern-p-form .form-inline .form-group > label {
  line-height: 44px;
  font-size: 16px;
  margin: 0;
}
.modern-p-form .form-inline > label + .p-rating,
.modern-p-form .form-inline .form-group > label + .p-rating {
  margin-left: 8px;
}
.modern-p-form .pt-form-c-group .radio:first-child,
.modern-p-form .pt-form-panel .radio:first-child,
.modern-p-form .pt-form-cross-panel .radio:first-child,
.modern-p-form .pt-form-c-group .checkbox:first-child,
.modern-p-form .pt-form-panel .checkbox:first-child,
.modern-p-form .pt-form-cross-panel .checkbox:first-child,
.modern-p-form .pt-form-c-group .p-radioswitch:first-child,
.modern-p-form .pt-form-panel .p-radioswitch:first-child,
.modern-p-form .pt-form-cross-panel .p-radioswitch:first-child,
.modern-p-form .pt-form-c-group .p-switch:first-child,
.modern-p-form .pt-form-panel .p-switch:first-child,
.modern-p-form .pt-form-cross-panel .p-switch:first-child {
  margin-top: 0;
}
.modern-p-form .pt-form-c-group .radio:last-child,
.modern-p-form .pt-form-panel .radio:last-child,
.modern-p-form .pt-form-cross-panel .radio:last-child,
.modern-p-form .pt-form-c-group .checkbox:last-child,
.modern-p-form .pt-form-panel .checkbox:last-child,
.modern-p-form .pt-form-cross-panel .checkbox:last-child,
.modern-p-form .pt-form-c-group .p-radioswitch:last-child,
.modern-p-form .pt-form-panel .p-radioswitch:last-child,
.modern-p-form .pt-form-cross-panel .p-radioswitch:last-child,
.modern-p-form .pt-form-c-group .p-switch:last-child,
.modern-p-form .pt-form-panel .p-switch:last-child,
.modern-p-form .pt-form-cross-panel .p-switch:last-child {
  margin-bottom: 0;
}
.modern-p-form .pt-form-panel,
.modern-p-form .pt-form-cross-panel {
  border: 1px solid #ccc;
  background-color: transparent;
  padding: 9px;
}
.modern-p-form .pt-form-cross-panel {
  margin-left: 12px;
  padding-left: 0;
}
.modern-p-form .pt-form-cross-panel .radio,
.modern-p-form .pt-form-cross-panel .checkbox {
  margin-left: -12px;
}
.modern-p-form .pt-form-cross-panel .p-radioswitch,
.modern-p-form .pt-form-cross-panel .p-switch {
  margin-left: -24px;
}
.modern-p-form .pt-form-cross-panel.p-form-sg {
  margin-left: 24px;
}
.modern-p-form .p-fixed {
  position: fixed;
  z-index: 1100;
}
.modern-p-form .p-absolute {
  position: absolute;
}
.modern-p-form .p-pos-left,
.modern-p-form .p-pos-right {
  top: 50%;
  -webkit-transform: translateY(-50%);
  -moz-transform: translateY(-50%);
  -o-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  transform: translateY(-50%);
}
.modern-p-form .p-pos-left {
  left: 0;
}
.modern-p-form .p-pos-right {
  right: 0;
}
.modern-p-form .p-pos-top-left {
  left: 15px;
  top: 0;
}
.modern-p-form .p-pos-top-right {
  top: 0;
  right: 15px;
}
.modern-p-form .p-pos-top-center,
.modern-p-form .p-pos-bottom-center {
  -webkit-transform: translateX(-50%);
  -moz-transform: translateX(-50%);
  -o-transform: translateX(-50%);
  -ms-transform: translateX(-50%);
  transform: translateX(-50%);
  left: 50%;
}
.modern-p-form .p-pos-top-center {
  top: 0;
}
.modern-p-form .p-pos-bottom-center {
  bottom: 0;
}
.modern-p-form .p-pos-bottom-left {
  left: 15px;
  bottom: 0;
}
.modern-p-form .p-pos-bottom-right {
  right: 15px;
  bottom: 0;
}
.modern-p-form .p-popup-form {
  display: none;
}
.modern-p-form .p-hide-form {
  position: absolute;
  top: 5px;
  right: 5px;
  font-size: 16px;
  line-height: 0;
}
.modern-p-form .p-popup {
  position: absolute;
  left: 15px;
  top: 100px;
  right: 15px;
  height: 0;
  z-index: 2000;
}
.modern-p-form .p-popup-bg {
  position: fixed;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  z-index: 1999;
  background-color: #000;
  opacity: 0.3;
  -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=30)";
  filter: alpha(opacity=30);
}
.modern-p-form .p-btn-pannel label {
  margin: 0;
}
.modern-p-form .p-show-check:checked ~ .p-popup-form {
  display: inline-block;
}
.modern-p-form .p-show-check:checked ~ .p-popup-bg {
  opacity: 0;
  -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=0)";
  filter: alpha(opacity=0);
  top: -100%;
  -webkit-transition: opacity 0.2s linear 0s, top 0s linear 0.2s;
  -moz-transition: opacity 0.2s linear 0s, top 0s linear 0.2s;
  -o-transition: opacity 0.2s linear 0s, top 0s linear 0.2s;
  -ms-transition: opacity 0.2s linear 0s, top 0s linear 0.2s;
  transition: opacity 0.2s linear 0s, top 0s linear 0.2s;
}
.modern-p-form .p-show-check:checked ~ .p-action-block {
  display: none;
}
.modern-p-form .p-show-check:checked ~ .p-popup {
  display: block;
}
.modern-p-form .p-show-check:checked ~ .p-popup .p-form {
  top: -100px;
  margin-top: -100px;
  -webkit-transform: translateY(-100%);
  -moz-transform: translateY(-100%);
  -o-transform: translateY(-100%);
  -ms-transform: translateY(-100%);
  transform: translateY(-100%);
}
.modern-p-form .p-rating {
  zoom: 1;
  white-space: nowrap;
  font-size: 21px;
  line-height: 44px;
}
.modern-p-form .p-rating:before,
.modern-p-form .p-rating:after {
  content: "";
  display: table;
}
.modern-p-form .p-rating:after {
  clear: both;
}
.modern-p-form .p-rating:hover .p-field-cancel {
  opacity: 1;
  -ms-filter: none;
  filter: none;
}
.modern-p-form .p-rating .p-field-cancel {
  font-size: 12px;
  line-height: 1.2;
  color: #999;
  opacity: 0;
  -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=0)";
  filter: alpha(opacity=0);
}
.modern-p-form .p-rating label {
  float: right;
  cursor: pointer;
  margin: 0;
  padding: 0 2px;
  text-align: center;
}
.modern-p-form .p-rating .fa-thumbs-up,
.modern-p-form .p-rating .fa-thumbs-o-up {
  width: 20px;
}
.modern-p-form .p-rating .p-rating-icon {
  color: #999;
}
.modern-p-form .p-rating .p-rating-active-icon {
  display: none;
  color: #333;
}
.modern-p-form .p-rating .p-rating-active-icon + .p-rating-icon {
  color: #999;
}
.modern-p-form .p-rating input[type="radio"] ~ label:hover .p-rating-icon,
.modern-p-form .p-rating input[type="radio"] ~ label:hover ~ label .p-rating-icon,
.modern-p-form .p-rating [type="radio"]:checked ~ label .p-rating-icon {
  color: #333;
}
.modern-p-form .p-rating input[type="radio"] ~ label:hover .p-rating-active-icon,
.modern-p-form .p-rating input[type="radio"] ~ label:hover ~ label .p-rating-active-icon,
.modern-p-form .p-rating [type="radio"]:checked ~ label .p-rating-active-icon {
  display: inline-block;
}
.modern-p-form .p-rating input[type="radio"] ~ label:hover .p-rating-active-icon + .p-rating-icon,
.modern-p-form .p-rating input[type="radio"] ~ label:hover ~ label .p-rating-active-icon + .p-rating-icon,
.modern-p-form .p-rating [type="radio"]:checked ~ label .p-rating-active-icon + .p-rating-icon {
  display: none;
}
.modern-p-form .p-rating input[type="radio"] ~ label.p-field-cancel:hover {
  color: #333;
}
.modern-p-form .p-rating input[type="radio"] ~ label.p-field-cancel:hover ~ label .p-rating-icon,
.modern-p-form .p-rating .p-rating-cancel:checked ~ label .p-rating-icon,
.modern-p-form .p-rating *:hover [type="radio"] ~ label .p-rating-icon {
  color: #999;
}
.modern-p-form .p-rating input[type="radio"] ~ label.p-field-cancel:hover ~ label .p-rating-active-icon,
.modern-p-form .p-rating .p-rating-cancel:checked ~ label .p-rating-active-icon,
.modern-p-form .p-rating *:hover [type="radio"] ~ label .p-rating-active-icon {
  display: none;
}
.modern-p-form .p-rating input[type="radio"] ~ label.p-field-cancel:hover ~ label .p-rating-active-icon + .p-rating-icon,
.modern-p-form .p-rating .p-rating-cancel:checked ~ label .p-rating-active-icon + .p-rating-icon,
.modern-p-form .p-rating *:hover [type="radio"] ~ label .p-rating-active-icon + .p-rating-icon {
  display: inline-block;
}
.modern-p-form .p-rating [type="radio"] {
  display: none;
}
.modern-p-form .p-form-tiny {
  margin-left: auto;
  margin-right: auto;
  width: 280px;
}
.modern-p-form .p-form-tiny .col-xs-1,
.modern-p-form .p-form-tiny .col-sm-1,
.modern-p-form .p-form-tiny .col-md-1,
.modern-p-form .p-form-tiny .col-lg-1,
.modern-p-form .p-form-tiny .col-xs-2,
.modern-p-form .p-form-tiny .col-sm-2,
.modern-p-form .p-form-tiny .col-md-2,
.modern-p-form .p-form-tiny .col-lg-2,
.modern-p-form .p-form-tiny .col-xs-3,
.modern-p-form .p-form-tiny .col-sm-3,
.modern-p-form .p-form-tiny .col-md-3,
.modern-p-form .p-form-tiny .col-lg-3,
.modern-p-form .p-form-tiny .col-xs-4,
.modern-p-form .p-form-tiny .col-sm-4,
.modern-p-form .p-form-tiny .col-md-4,
.modern-p-form .p-form-tiny .col-lg-4,
.modern-p-form .p-form-tiny .col-xs-5,
.modern-p-form .p-form-tiny .col-sm-5,
.modern-p-form .p-form-tiny .col-md-5,
.modern-p-form .p-form-tiny .col-lg-5,
.modern-p-form .p-form-tiny .col-xs-6,
.modern-p-form .p-form-tiny .col-sm-6,
.modern-p-form .p-form-tiny .col-md-6,
.modern-p-form .p-form-tiny .col-lg-6,
.modern-p-form .p-form-tiny .col-xs-7,
.modern-p-form .p-form-tiny .col-sm-7,
.modern-p-form .p-form-tiny .col-md-7,
.modern-p-form .p-form-tiny .col-lg-7,
.modern-p-form .p-form-tiny .col-xs-8,
.modern-p-form .p-form-tiny .col-sm-8,
.modern-p-form .p-form-tiny .col-md-8,
.modern-p-form .p-form-tiny .col-lg-8,
.modern-p-form .p-form-tiny .col-xs-9,
.modern-p-form .p-form-tiny .col-sm-9,
.modern-p-form .p-form-tiny .col-md-9,
.modern-p-form .p-form-tiny .col-lg-9,
.modern-p-form .p-form-tiny .col-xs-10,
.modern-p-form .p-form-tiny .col-sm-10,
.modern-p-form .p-form-tiny .col-md-10,
.modern-p-form .p-form-tiny .col-lg-10,
.modern-p-form .p-form-tiny .col-xs-11,
.modern-p-form .p-form-tiny .col-sm-11,
.modern-p-form .p-form-tiny .col-md-11,
.modern-p-form .p-form-tiny .col-lg-11,
.modern-p-form .p-form-tiny .col-xs-12,
.modern-p-form .p-form-tiny .col-sm-12,
.modern-p-form .p-form-tiny .col-md-12,
.modern-p-form .p-form-tiny .col-lg-12 {
  width: 100%;
}
@media (min-width: 430px) {
  .modern-p-form .p-form-xs {
    margin-left: auto;
    margin-right: auto;
    width: 400px;
  }
  .modern-p-form .p-form-xs .col-sm-1,
  .modern-p-form .p-form-xs .col-md-1,
  .modern-p-form .p-form-xs .col-lg-1,
  .modern-p-form .p-form-xs .col-sm-2,
  .modern-p-form .p-form-xs .col-md-2,
  .modern-p-form .p-form-xs .col-lg-2,
  .modern-p-form .p-form-xs .col-sm-3,
  .modern-p-form .p-form-xs .col-md-3,
  .modern-p-form .p-form-xs .col-lg-3,
  .modern-p-form .p-form-xs .col-sm-4,
  .modern-p-form .p-form-xs .col-md-4,
  .modern-p-form .p-form-xs .col-lg-4,
  .modern-p-form .p-form-xs .col-sm-5,
  .modern-p-form .p-form-xs .col-md-5,
  .modern-p-form .p-form-xs .col-lg-5,
  .modern-p-form .p-form-xs .col-sm-6,
  .modern-p-form .p-form-xs .col-md-6,
  .modern-p-form .p-form-xs .col-lg-6,
  .modern-p-form .p-form-xs .col-sm-7,
  .modern-p-form .p-form-xs .col-md-7,
  .modern-p-form .p-form-xs .col-lg-7,
  .modern-p-form .p-form-xs .col-sm-8,
  .modern-p-form .p-form-xs .col-md-8,
  .modern-p-form .p-form-xs .col-lg-8,
  .modern-p-form .p-form-xs .col-sm-9,
  .modern-p-form .p-form-xs .col-md-9,
  .modern-p-form .p-form-xs .col-lg-9,
  .modern-p-form .p-form-xs .col-sm-10,
  .modern-p-form .p-form-xs .col-md-10,
  .modern-p-form .p-form-xs .col-lg-10,
  .modern-p-form .p-form-xs .col-sm-11,
  .modern-p-form .p-form-xs .col-md-11,
  .modern-p-form .p-form-xs .col-lg-11,
  .modern-p-form .p-form-xs .col-sm-12,
  .modern-p-form .p-form-xs .col-md-12,
  .modern-p-form .p-form-xs .col-lg-12 {
    width: 100%;
  }
}
@media (max-width: 429px) {
  .modern-p-form .p-form-xs .col-xs-1 {
    width: 100%;
  }
  .modern-p-form .p-form-xs .col-xs-2 {
    width: 100%;
  }
  .modern-p-form .p-form-xs .col-xs-3 {
    width: 100%;
  }
  .modern-p-form .p-form-xs .col-xs-4 {
    width: 100%;
  }
  .modern-p-form .p-form-xs .col-xs-5 {
    width: 100%;
  }
  .modern-p-form .p-form-xs .col-xs-6 {
    width: 100%;
  }
  .modern-p-form .p-form-xs .col-xs-7 {
    width: 100%;
  }
  .modern-p-form .p-form-xs .col-xs-8 {
    width: 100%;
  }
  .modern-p-form .p-form-xs .col-xs-9 {
    width: 100%;
  }
  .modern-p-form .p-form-xs .col-xs-10 {
    width: 100%;
  }
  .modern-p-form .p-form-xs .col-xs-11 {
    width: 100%;
  }
  .modern-p-form .p-form-xs .col-xs-12 {
    width: 100%;
  }
}
@media (min-width: 768px) {
  .modern-p-form .p-form-sm {
    margin-left: auto;
    margin-right: auto;
    width: 720px;
  }
}
@media (min-width: 992px) {
  .modern-p-form .p-form-md {
    width: 940px;
  }
}
@media (min-width: 1200px) {
  .modern-p-form .p-form-lg {
    margin-left: auto;
    margin-right: auto;
    width: 1140px;
  }
}
.modern-p-form .p-field-value {
  font-size: 14px;
  line-height: 34px;
  padding: 5px 0;
}
.modern-p-form .p-value-label {
  margin-right: 5px;
}
.modern-p-form .p-value-text {
  font-size: 16px;
}
.modern-p-form label.p-label-required:after {
  content: "*";
  margin-left: 3px;
}
.modern-p-form .input-group-state {
  display: table-cell;
  vertical-align: top;
  width: 0;
  height: 0;
  font-size: 11px;
  color: #8c8c8c;
}
.modern-p-form .input-group-state .p-position {
  display: block;
  z-index: 3;
  width: 0;
  height: 0;
  position: relative;
  left: -5px;
  top: 3px;
}
.modern-p-form .input-group-state,
.modern-p-form .input-group-state * {
  pointer-events: none;
}
.modern-p-form .input-group-state .p-text {
  position: absolute;
  top: 0;
  right: 0;
}
.modern-p-form .input-group-state .p-text > * {
  display: none;
}
.modern-p-form .form-control[readonly] {
  cursor: default;
}
.modern-p-form .form-control[disabled] {
  cursor: not-allowed;
}
.modern-p-form .form-control[readonly],
.modern-p-form .form-control[disabled] {
  pointer-events: none;
  background-color: #f2f2f2;
}
.modern-p-form .form-control:required ~ .input-group-state .p-required-text,
.modern-p-form .form-control[data-rule-required] ~ .input-group-state .p-required-text {
  display: inline-block;
}
.modern-p-form .p-custom-arrow .input-group-state .p-position {
  left: -48px;
}
.modern-p-form .p-field-readonly,
.modern-p-form .p-field-disabled,
.modern-p-form .p-field-readonly *,
.modern-p-form .p-field-disabled * {
  pointer-events: none;
}
.modern-p-form .p-field-readonly .p-switch-icon,
.modern-p-form .p-field-disabled .p-switch-icon,
.modern-p-form .p-field-readonly .p-check-icon .p-check-block,
.modern-p-form .p-field-disabled .p-check-icon .p-check-block,
.modern-p-form .p-field-readonly .p-field-cb,
.modern-p-form .p-field-disabled .p-field-cb {
  background-color: #f2f2f2;
}
.modern-p-form .p-field-readonly .p-no-preview,
.modern-p-form .p-field-disabled .p-no-preview {
  background-color: #f2f2f2;
}
.modern-p-form .p-field-readonly .p-rating .p-rating-icon,
.modern-p-form .p-field-disabled .p-rating .p-rating-icon {
  color: #f2f2f2;
}
.modern-p-form .p-field-readonly .p-field-cancel,
.modern-p-form .p-field-disabled .p-field-cancel {
  display: none;
}
.modern-p-form .p-field-sub-text {
  display: block;
  font-size: 13px;
  line-height: 15px;
  margin-top: 3px;
}
.modern-p-form .p-error-text {
  color: #b80000;
}
.modern-p-form .p-field-error .input-group-state .p-error-text,
.modern-p-form .p-html-validate .form-control:invalid ~ .input-group-state .p-error-text,
.modern-p-form .p-html-validate .p-file-wrap [type="file"]:invalid ~ .input-group .input-group-state .p-error-text {
  display: inline-block;
}
.modern-p-form .p-field-error .input-group-state .p-error-text ~ .p-required-text,
.modern-p-form .p-html-validate .form-control:invalid ~ .input-group-state .p-error-text ~ .p-required-text,
.modern-p-form .p-html-validate .p-file-wrap [type="file"]:invalid ~ .input-group .input-group-state .p-error-text ~ .p-required-text {
  display: none;
}
.modern-p-form .p-field-error .input-group-state .p-required-text,
.modern-p-form .p-html-validate .form-control:invalid ~ .input-group-state .p-required-text,
.modern-p-form .p-html-validate .p-file-wrap [type="file"]:invalid ~ .input-group .input-group-state .p-required-text {
  color: #b80000;
}
.modern-p-form .p-field-error .p-switch-icon,
.modern-p-form .p-field-error .p-check-icon .p-check-block,
.modern-p-form .p-validate-highlight .p-field-error .p-field-cb,
.modern-p-form .p-html-validate.p-validate-highlight .form-control:focus:invalid,
.modern-p-form .p-html-validate.p-validate-highlight .form-control:focus:invalid ~ .p-field-cb,
.modern-p-form .p-html-validate.p-validate-highlight .p-file-wrap [type="file"]:focus:invalid ~ .input-group .p-field-cb {
  background-color: #fff0f0;
}
.modern-p-form .p-field-error .p-no-preview {
  background-color: #fff0f0;
}
.modern-p-form .alert-error {
  background-color: #fff0f0;
  border-color: #ffd6d6;
  color: #b80000;
}
.modern-p-form .p-valid-text {
  color: #2b995d;
}
.modern-p-form .p-field-valid .input-group-state .p-valid-text,
.modern-p-form .p-html-validate .form-control:valid ~ .input-group-state .p-valid-text,
.modern-p-form .p-html-validate .p-file-wrap [type="file"]:valid ~ .input-group .input-group-state .p-valid-text {
  display: inline-block;
}
.modern-p-form .p-field-valid .input-group-state .p-valid-text ~ .p-required-text,
.modern-p-form .p-html-validate .form-control:valid ~ .input-group-state .p-valid-text ~ .p-required-text,
.modern-p-form .p-html-validate .p-file-wrap [type="file"]:valid ~ .input-group .input-group-state .p-valid-text ~ .p-required-text {
  display: none;
}
.modern-p-form .p-field-valid .input-group-state .p-required-text,
.modern-p-form .p-html-validate .form-control:valid ~ .input-group-state .p-required-text,
.modern-p-form .p-html-validate .p-file-wrap [type="file"]:valid ~ .input-group .input-group-state .p-required-text {
  color: #2b995d;
}
.modern-p-form .p-field-valid .p-switch-icon,
.modern-p-form .p-field-valid .p-check-icon .p-check-block,
.modern-p-form .p-validate-highlight .p-field-valid .p-field-cb,
.modern-p-form .p-html-validate.p-validate-highlight .form-control:focus:valid,
.modern-p-form .p-html-validate.p-validate-highlight .form-control:focus:valid ~ .p-field-cb,
.modern-p-form .p-html-validate.p-validate-highlight .p-file-wrap [type="file"]:focus:valid ~ .input-group .p-field-cb {
  background-color: #f3fcf7;
}
.modern-p-form .p-field-valid .p-no-preview {
  background-color: #f3fcf7;
}
.modern-p-form .alert-valid {
  background-color: #f3fcf7;
  border-color: #dff6ea;
  color: #2b995d;
}
.modern-p-form .p-form-steps-wrap {
  position: relative;
  z-index: 1;
  margin-bottom: 30px;
  overflow: hidden;
}
.modern-p-form .p-form-steps {
  position: relative;
  width: 100%;
  padding: 0 20px;
  margin: 0;
  display: table;
  border-collapse: separate;
}
.modern-p-form .p-form-steps:before {
  left: 0;
  width: 100%;
  background-color: #ccc;
}
.modern-p-form .p-form-steps:before,
.modern-p-form .p-form-steps > li.active .p-step:before {
  content: "";
  position: absolute;
  top: 50%;
  margin-top: -2px;
  height: 4px;
}
.modern-p-form .p-form-steps > li {
  display: table-cell;
  vertical-align: top;
  text-align: center;
}
.modern-p-form .p-form-steps > li:first-child .p-step {
  margin-left: 0;
}
.modern-p-form .p-form-steps > li:last-child .p-step {
  margin-right: 0;
}
.modern-p-form .p-form-steps > li.active .p-step {
  color: #fff;
  display: inline-block;
}
.modern-p-form .p-form-steps > li.active ~ li .p-step {
  color: #666;
}
.modern-p-form .p-form-steps > li.active .p-step:before {
  right: 100%;
  width: 10000px;
  background-color: #141414;
}
.modern-p-form .p-form-steps > li.active ~ li .p-step-text {
  background-color: #ccc;
}
.modern-p-form .p-step {
  position: relative;
  font-size: 24px;
  line-height: 44px;
  color: #474747;
  text-align: center;
  text-decoration: none;
  display: none;
}
.modern-p-form .p-step .p-step-text {
  position: relative;
  display: inline-block;
  z-index: 20;
  padding: 0 15px;
  min-width: 120px;
  background-color: #141414;
}
.modern-p-form .p-js-show-step .p-step:hover,
.modern-p-form a.p-step:hover {
  cursor: pointer;
  color: #fff;
}
.modern-p-form .p-steps-icons .p-step {
  display: inline-block;
  margin-left: 15px;
  margin-right: 15px;
}
.modern-p-form .p-steps-icons .p-step .p-step-text {
  min-width: 44px;
}
@media (min-width: 768px) {
  .modern-p-form .p-steps-icons .p-step .p-step-text {
    min-width: 120px;
  }
  .modern-p-form .p-step {
    display: inline-block;
    margin-left: 15px;
    margin-right: 15px;
  }
  .modern-p-form .p-form-steps > li:first-child .p-step {
    margin-left: 0;
  }
  .modern-p-form .p-form-steps > li:last-child .p-step {
    margin-right: 0;
  }
}
@media (min-width: 992px) {
  .modern-p-form .p-form-steps .p-step {
    margin-left: 25px;
    margin-right: 25px;
  }
}
.modern-p-form .tab-content {
  padding-top: 20px;
}
.modern-p-form .nav-tabs {
  border-color: #d9d9d9;
  background-color: transparent;
}
.modern-p-form .nav-tabs > li > label {
  padding: 10px 25px;
  font-size: 18px;
  line-height: 28px;
  color: #333;
  text-align: center;
  margin: 0;
  cursor: pointer;
  position: relative;
}
.modern-p-form .nav-tabs > li > label:hover,
.modern-p-form .nav-tabs > li > label:focus {
  background-color: #474747;
  color: #fff;
  font-size: 18px;
}
.modern-p-form .p-tab-sel {
  display: none;
}
.modern-p-form .p-tab-sel:nth-child(1):checked ~ .nav-tabs > li:nth-child(1) > label,
.modern-p-form .p-tab-sel:nth-child(2):checked ~ .nav-tabs > li:nth-child(2) > label,
.modern-p-form .p-tab-sel:nth-child(3):checked ~ .nav-tabs > li:nth-child(3) > label,
.modern-p-form .p-tab-sel:nth-child(4):checked ~ .nav-tabs > li:nth-child(4) > label,
.modern-p-form .p-tab-sel:nth-child(5):checked ~ .nav-tabs > li:nth-child(5) > label,
.modern-p-form .p-tab-sel:nth-child(1):checked ~ .nav-tabs > li:nth-child(1) > label:hover,
.modern-p-form .p-tab-sel:nth-child(2):checked ~ .nav-tabs > li:nth-child(2) > label:hover,
.modern-p-form .p-tab-sel:nth-child(3):checked ~ .nav-tabs > li:nth-child(3) > label:hover,
.modern-p-form .p-tab-sel:nth-child(4):checked ~ .nav-tabs > li:nth-child(4) > label:hover,
.modern-p-form .p-tab-sel:nth-child(5):checked ~ .nav-tabs > li:nth-child(5) > label:hover,
.modern-p-form .p-tab-sel:nth-child(1):checked ~ .nav-tabs > li:nth-child(1) > label:focus,
.modern-p-form .p-tab-sel:nth-child(2):checked ~ .nav-tabs > li:nth-child(2) > label:focus,
.modern-p-form .p-tab-sel:nth-child(3):checked ~ .nav-tabs > li:nth-child(3) > label:focus,
.modern-p-form .p-tab-sel:nth-child(4):checked ~ .nav-tabs > li:nth-child(4) > label:focus,
.modern-p-form .p-tab-sel:nth-child(5):checked ~ .nav-tabs > li:nth-child(5) > label:focus {
  background-color: #333;
  color: #fff;
  font-size: 18px;
  cursor: default;
}
.modern-p-form .p-tab-sel:nth-child(1):checked ~ .tab-content .tab-pane:nth-child(1),
.modern-p-form .p-tab-sel:nth-child(2):checked ~ .tab-content .tab-pane:nth-child(2),
.modern-p-form .p-tab-sel:nth-child(3):checked ~ .tab-content .tab-pane:nth-child(3),
.modern-p-form .p-tab-sel:nth-child(4):checked ~ .tab-content .tab-pane:nth-child(4),
.modern-p-form .p-tab-sel:nth-child(5):checked ~ .tab-content .tab-pane:nth-child(5) {
  display: block;
}
.modern-p-form .p-title,
.modern-p-form .p-subtitle {
  color: #333;
  clear: both;
  zoom: 1;
}
.modern-p-form .p-title:before,
.modern-p-form .p-subtitle:before,
.modern-p-form .p-title:after,
.modern-p-form .p-subtitle:after {
  content: "";
  display: table;
}
.modern-p-form .p-title:after,
.modern-p-form .p-subtitle:after {
  clear: both;
}
.modern-p-form .p-no-offs {
  margin-top: 0 !important;
}
.modern-p-form > .p-title:first-child,
.modern-p-form > .p-subtitle:first-child {
  margin-top: 0;
}
.modern-p-form .p-title-line,
.modern-p-form .p-title-step-line,
.modern-p-form .p-title-side {
  display: block;
  background-color: #333;
  color: #fff;
}
.modern-p-form .p-title-line,
.modern-p-form .p-title-step-line {
  padding: 3px 15px 5px;
}
.modern-p-form .p-title-side {
  padding: 3px 25px 5px;
}
.modern-p-form .p-title-step-line {
  margin: 0 8px;
}
.modern-p-form .text-center > .p-title-step-line,
.modern-p-form .text-left > .p-title-step-line,
.modern-p-form .text-right > .p-title-step-line,
.modern-p-form .text-center > .p-title-side,
.modern-p-form .text-left > .p-title-side,
.modern-p-form .text-right > .p-title-side,
.modern-p-form .text-center > .p-title-line,
.modern-p-form .text-left > .p-title-line,
.modern-p-form .text-right > .p-title-line {
  display: inline-block;
  vertical-align: top;
}
.modern-p-form.text-left > .p-title-side,
.modern-p-form .p-inset.text-left > .p-title-side,
.modern-p-form .p-shadowed.text-left > .p-title-side,
.modern-p-form .p-bordered.text-left > .p-title-side,
.modern-p-form .p-subtitle.text-left > .p-title-side,
.modern-p-form .p-inset .p-subtitle.text-left > .p-title-side,
.modern-p-form .p-shadowed .p-subtitle.text-left > .p-title-side,
.modern-p-form .p-bordered .p-subtitle.text-left > .p-title-side {
  padding-right: 15px;
}
.modern-p-form.text-right > .p-title-side,
.modern-p-form .p-inset.text-right > .p-title-side,
.modern-p-form .p-shadowed.text-right > .p-title-side,
.modern-p-form .p-bordered.text-right > .p-title-side,
.modern-p-form .p-subtitle.text-right > .p-title-side,
.modern-p-form .p-inset .p-subtitle.text-right > .p-title-side,
.modern-p-form .p-shadowed .p-subtitle.text-right > .p-title-side,
.modern-p-form .p-bordered .p-subtitle.text-right > .p-title-side {
  padding-left: 15px;
}
.modern-p-form .p-title {
  margin-bottom: 30px;
  font-size: 30px;
  line-height: 36px;
}
.modern-p-form .p-title .p-title-side {
  padding-top: 20px;
  padding-bottom: 10px;
}
.modern-p-form .p-title .p-title-step-line,
.modern-p-form .p-title .p-title-line {
  padding-top: 8px;
  padding-bottom: 10px;
}
.modern-p-form .p-title.text-center > .p-title-side {
  padding-left: 35px;
  padding-right: 35px;
}
.modern-p-form .p-title.text-left > .p-title-side {
  padding-left: 35px;
}
.modern-p-form .p-title.text-right > .p-title-side {
  padding-right: 35px;
}
.modern-p-form .p-subtitle {
  margin: 15px 0 20px;
  font-size: 19px;
  line-height: 24px;
}
.modern-p-form .p-tooltip {
  display: none;
  position: absolute;
  left: 50%;
  bottom: 100%;
  margin-left: -100px;
  min-width: 200px;
  max-width: 100%;
  font-size: 14px;
  line-height: 1.2;
  color: #fff;
  text-align: center;
  z-index: 100;
}
.modern-p-form .p-tooltip .p-tooltip-content {
  position: relative;
  background-color: #141414;
  padding: 5px;
  display: inline-block;
  text-align: left;
  margin-bottom: 8px;
  max-width: 100%;
  -webkit-border-radius: 2px;
  border-radius: 2px;
}
.modern-p-form .p-tooltip .p-tooltip-content img {
  max-width: 100%;
}
.modern-p-form .p-tooltip .p-tooltip-content:after {
  content: "";
  position: absolute;
  left: 50%;
  top: 100%;
  margin-left: -8px;
  border-color: rgba(20,20,20,0);
  border-style: solid;
  border-width: 8px 8px;
  border-top-color: #141414;
}
.modern-p-form .p-tooltip.p-tooltip-bottom,
.modern-p-form .p-tooltip.p-tooltip-bottom-left,
.modern-p-form .p-tooltip.p-tooltip-bottom-right {
  bottom: auto;
  top: 100%;
}
.modern-p-form .p-tooltip.p-tooltip-bottom .p-tooltip-content,
.modern-p-form .p-tooltip.p-tooltip-bottom-left .p-tooltip-content,
.modern-p-form .p-tooltip.p-tooltip-bottom-right .p-tooltip-content {
  margin-bottom: 0;
  margin-top: 8px;
}
.modern-p-form .p-tooltip.p-tooltip-bottom .p-tooltip-content:after,
.modern-p-form .p-tooltip.p-tooltip-bottom-left .p-tooltip-content:after,
.modern-p-form .p-tooltip.p-tooltip-bottom-right .p-tooltip-content:after {
  top: auto;
  bottom: 100%;
  border-top-color: rgba(20,20,20,0);
  border-bottom-color: #141414;
}
.modern-p-form .p-tooltip.p-tooltip-bottom-left .p-tooltip-content:after,
.modern-p-form .p-tooltip.p-tooltip-top-left .p-tooltip-content:after,
.modern-p-form .p-tooltip.p-tooltip-bottom-right .p-tooltip-content:after,
.modern-p-form .p-tooltip.p-tooltip-top-right .p-tooltip-content:after {
  margin-left: 0;
}
.modern-p-form .p-tooltip.p-tooltip-bottom-left,
.modern-p-form .p-tooltip.p-tooltip-top-left {
  text-align: left;
  left: 0;
  margin: 0;
}
.modern-p-form .p-tooltip.p-tooltip-bottom-left .p-tooltip-content:after,
.modern-p-form .p-tooltip.p-tooltip-top-left .p-tooltip-content:after {
  left: 14px;
}
.modern-p-form .p-tooltip.p-tooltip-bottom-right,
.modern-p-form .p-tooltip.p-tooltip-top-right {
  text-align: right;
  left: auto;
  right: 0;
  margin: 0;
}
.modern-p-form .p-tooltip.p-tooltip-bottom-right .p-tooltip-content:after,
.modern-p-form .p-tooltip.p-tooltip-top-right .p-tooltip-content:after {
  left: auto;
  right: 14px;
}
.modern-p-form .p-inset .p-buttons,
.modern-p-form .p-shadowed .p-buttons,
.modern-p-form .p-bordered .p-buttons {
  padding-right: 0;
}
.modern-p-form .p-inset .p-form-steps-wrap .p-form-steps,
.modern-p-form .p-shadowed .p-form-steps-wrap .p-form-steps,
.modern-p-form .p-bordered .p-form-steps-wrap .p-form-steps {
  padding-right: 0;
  width: auto;
}
.modern-p-form .p-inset,
.modern-p-form .p-shadowed,
.modern-p-form .p-bordered {
  padding: 20px;
}
.modern-p-form .p-inset .p-title .p-title-side,
.modern-p-form .p-shadowed .p-title .p-title-side,
.modern-p-form .p-bordered .p-title .p-title-side {
  margin-left: 15px;
  margin-right: 15px;
}
.modern-p-form .p-inset .p-subtitle .p-title-side,
.modern-p-form .p-shadowed .p-subtitle .p-title-side,
.modern-p-form .p-bordered .p-subtitle .p-title-side {
  padding-left: 20px;
  padding-right: 20px;
}
.modern-p-form .p-inset > .p-title:first-child .p-title-line,
.modern-p-form .p-shadowed > .p-title:first-child .p-title-line,
.modern-p-form .p-bordered > .p-title:first-child .p-title-line {
  margin-top: -5px;
}
.modern-p-form .p-inset > .p-title:first-child .p-title-step-line,
.modern-p-form .p-shadowed > .p-title:first-child .p-title-step-line,
.modern-p-form .p-bordered > .p-title:first-child .p-title-step-line {
  margin-top: -12px;
}
.modern-p-form .p-inset .p-title-step-line,
.modern-p-form .p-shadowed .p-title-step-line {
  padding-left: 12px;
  padding-right: 12px;
  margin-left: -12px;
  margin-right: -12px;
}
.modern-p-form .p-inset .p-title .p-title-line,
.modern-p-form .p-shadowed .p-title .p-title-line {
  padding-left: 20px;
  padding-right: 20px;
}
.modern-p-form .p-inset > .p-title:first-child .p-title-side,
.modern-p-form .p-shadowed > .p-title:first-child .p-title-side {
  margin-top: -20px;
  padding-top: 28px;
}
.modern-p-form .p-inset .p-subtitle .p-title-side,
.modern-p-form .p-shadowed .p-subtitle .p-title-side,
.modern-p-form .p-inset .p-title .p-title-line,
.modern-p-form .p-shadowed .p-title .p-title-line {
  margin-left: -20px;
  margin-right: -20px;
}
.modern-p-form .p-inset .p-buttons,
.modern-p-form .p-shadowed .p-buttons {
  margin-bottom: -20px;
}
.modern-p-form .p-inset .nav-tabs,
.modern-p-form .p-shadowed .nav-tabs {
  margin-left: -20px;
  margin-right: -20px;
  margin-top: -20px;
}
.modern-p-form .p-inset .p-form-steps-wrap,
.modern-p-form .p-shadowed .p-form-steps-wrap {
  margin-left: -20px;
}
.modern-p-form .p-shadowed {
  -webkit-box-shadow: 0 0 10px #000;
  box-shadow: 0 0 10px #000;
}
.modern-p-form .p-bordered {
  border: 1px solid #d9d9d9;
}
.modern-p-form .p-bordered .p-title-step-line {
  padding-left: 13px;
  padding-right: 13px;
  margin-left: -13px;
  margin-right: -13px;
}
.modern-p-form .p-bordered .p-title .p-title-line {
  padding-left: 21px;
  padding-right: 21px;
}
.modern-p-form .p-bordered > .p-title:first-child .p-title-side {
  margin-top: -21px;
  padding-top: 29px;
}
.modern-p-form .p-bordered .p-subtitle .p-title-side,
.modern-p-form .p-bordered .p-title .p-title-line {
  margin-left: -21px;
  margin-right: -21px;
}
.modern-p-form .p-bordered .p-buttons {
  margin-bottom: -21px;
}
.modern-p-form .p-bordered .nav-tabs {
  margin-left: -21px;
  margin-right: -21px;
  margin-top: -21px;
}
.modern-p-form .p-bordered .p-form-steps-wrap {
  margin-left: -21px;
}
.modern-p-form .p-shadowed.p-tabs-offset > .nav-tabs + .tab-content,
.modern-p-form .p-bordered.p-tabs-offset > .nav-tabs + .tab-content,
.modern-p-form .p-inset.p-tabs-offset > .nav-tabs + .tab-content {
  padding: 20px;
  background-color: #fff;
  position: relative;
}
.modern-p-form .p-tabs-offset {
  margin-top: 58px;
  position: relative;
  padding: 0;
}
.modern-p-form .p-tabs-offset > .nav-tabs {
  position: absolute;
  left: 0;
  bottom: 100%;
  margin: 0;
  border-color: transparent;
}
.modern-p-form .p-tabs-offset > .nav-tabs > li > label {
  background-color: #474747;
  color: #fff;
}
.modern-p-form .p-tabs-offset > .nav-tabs > li > label:hover,
.modern-p-form .p-tabs-offset > .nav-tabs > li > label:focus {
  background-color: #333;
  color: #fff;
}
.modern-p-form .p-tabs-offset .p-tab-sel:nth-child(1):checked ~ .nav-tabs > li:nth-child(1) > label,
.modern-p-form .p-tabs-offset .p-tab-sel:nth-child(2):checked ~ .nav-tabs > li:nth-child(2) > label,
.modern-p-form .p-tabs-offset .p-tab-sel:nth-child(3):checked ~ .nav-tabs > li:nth-child(3) > label,
.modern-p-form .p-tabs-offset .p-tab-sel:nth-child(4):checked ~ .nav-tabs > li:nth-child(4) > label,
.modern-p-form .p-tabs-offset .p-tab-sel:nth-child(5):checked ~ .nav-tabs > li:nth-child(5) > label,
.modern-p-form .p-tabs-offset .p-tab-sel:nth-child(1):checked ~ .nav-tabs > li:nth-child(1) > label:hover,
.modern-p-form .p-tabs-offset .p-tab-sel:nth-child(2):checked ~ .nav-tabs > li:nth-child(2) > label:hover,
.modern-p-form .p-tabs-offset .p-tab-sel:nth-child(3):checked ~ .nav-tabs > li:nth-child(3) > label:hover,
.modern-p-form .p-tabs-offset .p-tab-sel:nth-child(4):checked ~ .nav-tabs > li:nth-child(4) > label:hover,
.modern-p-form .p-tabs-offset .p-tab-sel:nth-child(5):checked ~ .nav-tabs > li:nth-child(5) > label:hover,
.modern-p-form .p-tabs-offset .p-tab-sel:nth-child(1):checked ~ .nav-tabs > li:nth-child(1) > label:focus,
.modern-p-form .p-tabs-offset .p-tab-sel:nth-child(2):checked ~ .nav-tabs > li:nth-child(2) > label:focus,
.modern-p-form .p-tabs-offset .p-tab-sel:nth-child(3):checked ~ .nav-tabs > li:nth-child(3) > label:focus,
.modern-p-form .p-tabs-offset .p-tab-sel:nth-child(4):checked ~ .nav-tabs > li:nth-child(4) > label:focus,
.modern-p-form .p-tabs-offset .p-tab-sel:nth-child(5):checked ~ .nav-tabs > li:nth-child(5) > label:focus {
  background-color: #fff;
  color: #333;
  z-index: 2;
}
.modern-p-form .p-tabs-offset.p-shadowed {
  -webkit-box-shadow: none;
  box-shadow: none;
}
.modern-p-form .p-tabs-offset.p-shadowed > .nav-tabs {
  -webkit-box-shadow: 0 0 10px #000;
  box-shadow: 0 0 10px #000;
}
.modern-p-form .p-tabs-offset.p-shadowed > .nav-tabs + .tab-content {
  -webkit-box-shadow: 0 0 10px #000;
  box-shadow: 0 0 10px #000;
}
.modern-p-form .p-tabs-offset.p-bordered {
  border: 0 none;
}
.modern-p-form .p-tabs-offset.p-bordered > .nav-tabs {
  border-left: 1px solid #d9d9d9;
  border-top: 1px solid #d9d9d9;
  border-right: 1px solid #d9d9d9;
}
.modern-p-form .p-tabs-offset.p-bordered > .nav-tabs + .tab-content {
  border: 1px solid #d9d9d9;
}
.modern-p-form .p-tabs-offset.p-bordered > .nav-tabs > li > label {
  border-bottom: 1px solid #d9d9d9;
  border-right: 1px solid transparent;
  padding-bottom: 11px;
  margin-bottom: -1px;
}
.modern-p-form .p-tabs-offset.p-bordered .p-tab-sel:nth-child(1):checked ~ .nav-tabs > li:nth-child(1) > label,
.modern-p-form .p-tabs-offset.p-bordered .p-tab-sel:nth-child(2):checked ~ .nav-tabs > li:nth-child(2) > label,
.modern-p-form .p-tabs-offset.p-bordered .p-tab-sel:nth-child(3):checked ~ .nav-tabs > li:nth-child(3) > label,
.modern-p-form .p-tabs-offset.p-bordered .p-tab-sel:nth-child(4):checked ~ .nav-tabs > li:nth-child(4) > label,
.modern-p-form .p-tabs-offset.p-bordered .p-tab-sel:nth-child(5):checked ~ .nav-tabs > li:nth-child(5) > label {
  border-bottom-color: transparent;
  padding-bottom: 10px;
  margin: -1px;
  border: 1px solid #d9d9d9;
  border-bottom: 0 none;
  padding-bottom: 12px;
}
.modern-p-form .p-tabs-offset.p-bordered .p-tab-sel:nth-child(1):checked ~ .nav-tabs > li:nth-child(1):last-child > label,
.modern-p-form .p-tabs-offset.p-bordered .p-tab-sel:nth-child(2):checked ~ .nav-tabs > li:nth-child(2):last-child > label,
.modern-p-form .p-tabs-offset.p-bordered .p-tab-sel:nth-child(3):checked ~ .nav-tabs > li:nth-child(3):last-child > label,
.modern-p-form .p-tabs-offset.p-bordered .p-tab-sel:nth-child(4):checked ~ .nav-tabs > li:nth-child(4):last-child > label,
.modern-p-form .p-tabs-offset.p-bordered .p-tab-sel:nth-child(5):checked ~ .nav-tabs > li:nth-child(5):last-child > label {
  border-right-color: #d9d9d9;
}
.modern-p-form .p-steps-offset {
  margin-top: 39px;
  position: relative;
  padding-top: 44px;
}
.modern-p-form .p-steps-offset > .p-form-steps-wrap:first-child {
  position: absolute;
  left: 0;
  bottom: 100%;
  margin: 0 0 -24px;
}
.modern-p-form .p-steps-offset > .p-form-steps-wrap:first-child .p-form-steps {
  padding-right: 0;
}
.modern-p-form .p-steps-offset.p-shadowed > .p-form-steps-wrap:first-child {
  padding-top: 15px;
  padding-right: 15px;
}
.modern-p-form .p-steps-offset.p-shadowed > .p-form-steps-wrap:first-child .p-step:after {
  content: "";
  position: absolute;
  left: 1px;
  top: 1px;
  right: 1px;
  height: 15px;
  -webkit-box-shadow: 0 0 10px 0 #000;
  box-shadow: 0 0 10px 0 #000;
}
.modern-p-form .p-steps-offset.p-bordered .p-form-steps-wrap {
  margin-left: -1px;
  margin-bottom: -23px;
}
.modern-p-form .p-alt-color,
.modern-p-form .p-alt-color label {
  color: #333;
}
.modern-p-form .p-alt-back,
.modern-p-form .p-alt-back .p-form-bg {
  background-color: #fafafa;
}
.modern-p-form .p-alt-back.p-tabs-offset > .nav-tabs + .tab-content {
  background-color: #fafafa;
}
.modern-p-form .p-alt-back.p-tabs-offset .p-tab-sel:nth-child(1):checked ~ .nav-tabs > li:nth-child(1) > label,
.modern-p-form .p-alt-back.p-tabs-offset .p-tab-sel:nth-child(2):checked ~ .nav-tabs > li:nth-child(2) > label,
.modern-p-form .p-alt-back.p-tabs-offset .p-tab-sel:nth-child(3):checked ~ .nav-tabs > li:nth-child(3) > label,
.modern-p-form .p-alt-back.p-tabs-offset .p-tab-sel:nth-child(4):checked ~ .nav-tabs > li:nth-child(4) > label,
.modern-p-form .p-alt-back.p-tabs-offset .p-tab-sel:nth-child(5):checked ~ .nav-tabs > li:nth-child(5) > label,
.modern-p-form .p-alt-back.p-tabs-offset .p-tab-sel:nth-child(1):checked ~ .nav-tabs > li:nth-child(1) > label:hover,
.modern-p-form .p-alt-back.p-tabs-offset .p-tab-sel:nth-child(2):checked ~ .nav-tabs > li:nth-child(2) > label:hover,
.modern-p-form .p-alt-back.p-tabs-offset .p-tab-sel:nth-child(3):checked ~ .nav-tabs > li:nth-child(3) > label:hover,
.modern-p-form .p-alt-back.p-tabs-offset .p-tab-sel:nth-child(4):checked ~ .nav-tabs > li:nth-child(4) > label:hover,
.modern-p-form .p-alt-back.p-tabs-offset .p-tab-sel:nth-child(5):checked ~ .nav-tabs > li:nth-child(5) > label:hover,
.modern-p-form .p-alt-back.p-tabs-offset .p-tab-sel:nth-child(1):checked ~ .nav-tabs > li:nth-child(1) > label:focus,
.modern-p-form .p-alt-back.p-tabs-offset .p-tab-sel:nth-child(2):checked ~ .nav-tabs > li:nth-child(2) > label:focus,
.modern-p-form .p-alt-back.p-tabs-offset .p-tab-sel:nth-child(3):checked ~ .nav-tabs > li:nth-child(3) > label:focus,
.modern-p-form .p-alt-back.p-tabs-offset .p-tab-sel:nth-child(4):checked ~ .nav-tabs > li:nth-child(4) > label:focus,
.modern-p-form .p-alt-back.p-tabs-offset .p-tab-sel:nth-child(5):checked ~ .nav-tabs > li:nth-child(5) > label:focus {
  background-color: #fafafa;
}
.modern-p-form .p-alt-back .p-table.table-striped > tbody > tr:nth-of-type(even),
.modern-p-form .p-alt-back .p-table.table-striped > thead {
  background-color: #f2f2f2;
}
.modern-p-form .p-alt-back .panel-fp > .panel-heading {
  background-color: #f2f2f2;
}
.modern-p-form .p-alt-back .panel-fp > .panel-body {
  background-color: #fafafa;
}
.modern-p-form .p-alt-back hr.p-flat {
  border-color: #f2f2f2;
}
.photo-contest input{
	margin:0;
}
