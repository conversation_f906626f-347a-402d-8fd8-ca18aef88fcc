"use strict";(self.webpackChunkgravityforms=self.webpackChunkgravityforms||[]).push([[963],{1868:function(e,t,a){a.r(t);var n=a(5210),o=a(8349),s=a(5518),i=a(564),r=a.n(i),c=a(9843),u=a.n(c),l=a(4318),d=a.n(l),f=a(5872),m=a.n(f),p=a(1057),g=a(2142),v=a(2867),w=a(8114),C=a(5155),k=a(5196),_=a.n(k),z=a(1047),R=o.React.useState,E=o.React.useEffect;t.default=function(e){var t,a="mock_endpoint"===(null===(t=e.endpoints)||void 0===t||null===(t=t.validate_license)||void 0===t?void 0:t.action),i=R(!0),c=(0,n.Z)(i,2),l=c[0],f=c[1],k=(0,z.default)((function(e){return e.innerDialogOpen})),b=(0,z.default)((function(e){return e.activeStep})),S=(0,z.default)((function(e){return e.isOpen})),y={closeOnMaskClick:!1,closeButtonTitle:e.i18n.close_button,customCloseButtonClasses:["gform-setup-wizard--exit-button"],customWrapperClasses:["gform-setup-wizard","gform-setup-wizard--step-".concat((0,z.default)((function(e){return e.activeStep}))),"gform-setup-wizard--inner-dialog-".concat((0,z.default)((function(e){return e.innerDialogOpen})))],customMaskClasses:[!l&&"gform-setup-wizard--not-fullscreen"],id:"gform-setup-wizard",isOpen:S,lockBody:!0,onCloseAfterAnimation:function(){var t=s.cookieStorage.get(e.data.options.invalidKeyCookieName);a||5===b||e.data.options.isSettingsPage&&t||(window.location.href=e.data.dashboard_url),(0,s.trigger)({event:"gform/video/pauseAll",native:!1,data:{}})},position:l?"fixed":"absolute",mode:"container",zIndex:100001};return E((function(){S&&document.body.classList.add("gform-setup-wizard--open")}),[S]),o.React.createElement(u(),y,o.React.createElement(d(),{customClasses:["gform-setup-wizard__nav-bar"]},o.React.createElement(_(),{activeStep:(0,z.default)((function(e){return e.activeStep})),customClasses:["gform-setup-wizard__steps"],numSteps:5,spacing:[8,0,0]})),o.React.createElement(m(),{x:1030,customClasses:["gform-setup-wizard__content-mask"],setDisplay:!1}),!k&&o.React.createElement(r(),{ariaLabel:e.i18n.toggle_fullscreen,circular:!0,customClasses:["gform-setup-wizard__fullscreen-toggle"],icon:l?"contract":"expand",onClick:function(){f(!l)},type:"white"}),o.React.createElement(p.default,e),o.React.createElement(g.default,e),o.React.createElement(v.default,e),o.React.createElement(w.default,e),o.React.createElement(C.default,e))}}}]);