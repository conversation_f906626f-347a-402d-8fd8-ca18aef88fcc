"use strict";(self.webpackChunkgravityforms=self.webpackChunkgravityforms||[]).push([[830],{8114:function(e,t,n){n.r(t);var r=n(7063),i=n(9801),a=n(5210),o=n(9509),c=n.n(o),s=n(8349),u=n(2036),l=n(5518),f=n(9608),p=n.n(f),m=n(5872),d=n.n(m),v=n(564),g=n.n(v),h=n(4216),b=n.n(h),y=n(6172),w=n.n(y),_=n(1047),x=n(6134);function O(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function k(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?O(Object(n),!0).forEach((function(t){(0,r.Z)(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):O(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var P=s.React.useState;t.default=function(e){var t,n=e.endpoints,r=e.i18n,o=P(!1),f=(0,a.Z)(o,2),m=f[0],v=f[1],h=P(!1),y=(0,a.Z)(h,2),O=y[0],z=y[1],C=(0,_.default)((function(e){return e.activeStep})),j=(0,_.default)((function(e){return e.setActiveStepNext})),E=(0,_.default)((function(e){return e.setDataCollection})),T="mock_endpoint"===(null==n||null===(t=n.save_prefs)||void 0===t?void 0:t.action),R=r.help_improve_copy,D=r.help_improve_title,S=r.no_thanks_button,Z=r.yes_button,N=r.previous,A=function(){var e=(0,i.Z)(c().mark((function e(){var t,r,i,a,o,s,f=arguments;return c().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(f.length>0&&void 0!==f[0]&&f[0]?(z(!0),E(!0)):(v(!0),E(!1)),(t=_.default.getData()).formTypes=t.formTypes.filter((function(e){return e.initialChecked})).map((function(e){return e.value})),t.services=t.services.filter((function(e){return e.initialChecked})).map((function(e){return e.value})),r={baseUrl:p(),method:"POST",body:k({},t)},!T){e.next=14;break}return(0,l.consoleInfo)("Mock endpoint, data that would have been sent is:"),(0,l.consoleInfo)(r),e.next=11,new Promise((function(e){return setTimeout(e,1e3)}));case 11:j(),e.next=23;break;case 14:return a=Date.now(),e.next=17,(0,u.ZP)("save_prefs",n,r);case 17:if(o=e.sent,!((s=Date.now()-a)<600)){e.next=22;break}return e.next=22,new Promise((function(e){return setTimeout(e,600-s)}));case 22:null!=o&&null!==(i=o.data)&&void 0!==i&&i.success&&j();case 23:return e.next=25,new Promise((function(e){return setTimeout((function(){v(!1),z(!1),e()}),200)}));case 25:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}(),I={ref:(0,x.useFocusTrap)(4===C),className:(0,s.classnames)({"gform-setup-wizard__screen":!0,"gform-setup-wizard__screen--step-4":!0}),"aria-hidden":4!==C};return s.React.createElement("div",I,s.React.createElement("div",{className:"gform-setup-wizard__content"},s.React.createElement(b(),{content:D,customClasses:["gform-typography--md-size-display-sm"],spacing:{"":3,md:5},size:"display-xs",weight:"medium",tagName:"h2"}),s.React.createElement(w(),{content:R,asHtml:!0,spacing:{"":5,md:8},size:"text-md",weight:"regular"}),s.React.createElement(d(),{x:850,customClasses:["gform-setup-wizard__footer"],display:"flex"},s.React.createElement(g(),{size:"size-height-xl",type:"white",icon:"arrow-narrow-left",iconPrefix:"gform-common-icon",onClick:(0,_.default)((function(e){return e.setActiveStepPrevious})),ariaLabel:N}),s.React.createElement(d(),{customClasses:["gform-setup-wizard__nav-next-alt"],display:"flex"},s.React.createElement(g(),{activeText:S,label:S,onClick:function(){A()},active:m,activeType:"loader",disabled:m,iconPosition:"trailing",size:"size-height-xl",type:"white"}),s.React.createElement(g(),{label:Z,activeText:Z,customClasses:["gform-setup-wizard__data-button"],size:"size-height-xl",active:O,activeType:"loader",disabled:O,iconPosition:"trailing",onClick:function(){A(!0)}})))))}}}]);