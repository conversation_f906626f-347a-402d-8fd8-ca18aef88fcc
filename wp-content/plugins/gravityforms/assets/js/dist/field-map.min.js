!function(){"use strict";function e(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function t(e){return t="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},t(e)}function n(e){var n=function(e,n){if("object"!=t(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var a=r.call(e,n||"default");if("object"!=t(a))return a;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===n?String:Number)(e)}(e,"string");return"symbol"==t(n)?n:String(n)}function r(e,t){for(var r=0;r<t.length;r++){var a=t[r];a.enumerable=a.enumerable||!1,a.configurable=!0,"value"in a&&(a.writable=!0),Object.defineProperty(e,n(a.key),a)}}function a(e,t,n){return t&&r(e.prototype,t),n&&r(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e}function i(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function o(e,n){if(n&&("object"===t(n)||"function"==typeof n))return n;if(void 0!==n)throw new TypeError("Derived constructors may only return object or undefined");return i(e)}function c(e){return c=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},c(e)}function u(e,t){return u=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},u(e,t)}function p(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&u(e,t)}function l(e,t,r){return(t=n(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function s(e,t,n){return t=c(t),o(e,g()?Reflect.construct(t,n||[],c(e).constructor):t.apply(e,n))}function g(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(e){}return(g=function(){return!!e})()}var m=wp.element,f=m.Component,d=(m.Fragment,wp.i18n.__,function(t){function n(){return e(this,n),s(this,n,arguments)}return p(n,t),a(n,[{key:"render",value:function(){return this.props.tooltip?React.createElement("button",{type:"button",className:"gf_tooltip tooltip","aria-label":this.props.tooltip},React.createElement("i",{className:"gform-icon gform-icon--question-mark","aria-hidden":"true"})):null}}]),n}(f));function h(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function y(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?h(Object(n),!0).forEach((function(t){l(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):h(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function v(e,t,n){return t=c(t),o(e,_()?Reflect.construct(t,n||[],c(e).constructor):t.apply(e,n))}function _(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(e){}return(_=function(){return!!e})()}var b=wp.element,k=b.Component,R=(b.Fragment,wp.i18n.__),O=function(t){function n(){return e(this,n),v(this,n,arguments)}return p(n,t),a(n,[{key:"componentDidMount",value:function(){var e=this;this.$input=jQuery(this.input),"undefined"!=typeof form&&(this.mergeTagsObj=new gfMergeTagsObj(form,this.$input)),this.$input.on("propertychange",(function(t){e.props.updateMapping(y(y({},e.props.mapping),{},{custom_value:t.target.value}),e.props.index)}))}},{key:"componentWillUnmount",value:function(){this.$input.off("propertychange"),void 0!==this.mergeTagsObj&&this.mergeTagsObj.destroy()}},{key:"render",value:function(){var e=this,t=this.props.mergeTagSupport?"gform-settings-generic-map__custom gform-settings-input__container--with-merge-tag":"gform-settings-generic-map__custom",n=this.props.mergeTagSupport?"merge-tag-support mt-position-right":"";return React.createElement("span",{className:t},React.createElement("input",{ref:function(t){return e.input=t},id:this.props.fieldId,type:"text",className:n,value:this.props.mapping.custom_value,placeholder:this.props.valueField.placeholder,onChange:function(t){return e.props.updateMapping(y(y({},e.props.mapping),{},{custom_value:t.target.value}),e.props.index)}}),React.createElement("button",{className:"gform-settings-generic-map__reset",onClick:function(t){t.preventDefault(),e.props.updateMapping(y(y({},e.props.mapping),{},{value:"",custom_value:""}),e.props.index)}},React.createElement("span",{className:"screen-reader-text"},R("Remove Custom Value","gravityforms"))))}}]),n}(k);function E(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function w(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?E(Object(n),!0).forEach((function(t){l(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):E(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function M(e,t,n){return t=c(t),o(e,j()?Reflect.construct(t,n||[],c(e).constructor):t.apply(e,n))}function j(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(e){}return(j=function(){return!!e})()}var C=wp.element,S=C.Component,N=C.Fragment,I=wp.i18n.__,P=function(t){function n(){return e(this,n),M(this,n,arguments)}return p(n,t),a(n,[{key:"renderRequiredSpan",value:function(){var e=this.props.choice,t=this.getKeyInputId();return e.required?React.createElement("span",{className:"required",id:t},"*"):null}},{key:"render",value:function(){var e=this.props,t=e.isInvalid,n=e.index;return React.createElement("tr",{className:"gform-settings-generic-map__row"},React.createElement("td",{className:"gform-settings-generic-map__column gform-settings-generic-map__column--key"},this.getKeyInput(n)),React.createElement("td",{className:"gform-settings-generic-map__column gform-settings-generic-map__column--value"},this.getValueInput()),React.createElement("td",{className:"gform-settings-generic-map__column gform-settings-generic-map__column--error"},t&&React.createElement("svg",{width:"22",height:"22",fill:"none",xmlns:"http://www.w3.org/2000/svg"},React.createElement("path",{d:"M11 22C4.9249 22 0 17.0751 0 11S4.9249 0 11 0s11 4.9249 11 11-4.9249 11-11 11z",fill:"#E54C3B"}),React.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M9.9317 5.0769a.1911.1911 0 00-.1909.2006l.3708 7.4158a.8895.8895 0 001.7768 0l.3708-7.4158a.1911.1911 0 00-.1909-.2006H9.9317zm2.3375 10.5769c0 .701-.5682 1.2693-1.2692 1.2693-.701 0-1.2692-.5683-1.2692-1.2693 0-.7009.5682-1.2692 1.2692-1.2692.701 0 1.2692.5683 1.2692 1.2692z",fill:"#fff"}))),React.createElement("td",{className:"gform-settings-generic-map__column gform-settings-generic-map__column--buttons"},this.getAddButton(),this.getDeleteButton()))}},{key:"getValueInputId",value:function(){var e=this.props,t=e.inputId,n=e.inputType,r=e.index,a=e.mapping;switch(n){case"generic_map":case"dynamic_field_map":return"".concat(t,"_custom_value_").concat(r);default:return"".concat(t,"_").concat(a.key)}}},{key:"getKeyInputId",value:function(){var e=this.props,t=e.inputId,n=e.inputType,r=e.index,a=e.mapping;switch(n){case"generic_map":case"dynamic_field_map":return"".concat(t,"_custom_key_").concat(r);default:return"".concat(t,"_").concat(a.key,"_key")}}},{key:"getKeyInput",value:function(e){var t=this.props,n=t.choice,r=t.keyField,a=t.index,i=t.mapping,o=t.updateMapping,c=r.choices,u=r.display_all,p=r.placeholder,l=this.getKeyInputId();return n.required||u?React.createElement(N,null,React.createElement("label",null,n.label," ",this.renderRequiredSpan()," "),React.createElement(d,{tooltip:n.tooltip})):"gf_custom"===i.key?React.createElement("span",{className:"gform-settings-generic-map__custom"},React.createElement("input",{id:l,type:"text",value:i.custom_key,placeholder:p,onChange:function(e){return o(w(w({},i),{},{custom_key:e.target.value}),a)}}),c.length>0&&React.createElement("button",{className:"gform-settings-generic-map__reset",onClick:function(e){e.preventDefault(),o(w(w({},i),{},{key:"",custom_key:""}),a)}},React.createElement("span",{className:"screen-reader-text"},I("Remove Custom Key","gravityforms")))):React.createElement("select",{id:l,value:i.key,onChange:function(e){return o(w(w({},i),{},{key:e.target.value}),a)}},this.getKeyOptions(e))}},{key:"getKeyOptions",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=!(arguments.length>2&&void 0!==arguments[2])||arguments[2],r=!(arguments.length>3&&void 0!==arguments[3])||arguments[3],a=this.props,i=a.keyField,o=a.mappedChoices,c=a.mapping,u=i.allow_custom,p=i.allow_duplicates;t||(t=i.choices);var l=t.map((function(e){return e.name||e.value})),s="select-".concat(e,"-optiongroup-"),g=[];!l.includes("")&&n&&g.push(React.createElement("option",{key:"".concat(s,"-default"),value:"",disabled:!1},I("Select a Field","gravityforms")));for(var m=0;m<t.length;m++){var f=t[m],d=f.name||f.value;if(!f.required){var h=o.includes(d)&&d!==c.key&&!p;f.choices&&f.choices.length>0?g.push(React.createElement("optgroup",{label:f.label,key:"".concat(s,"-").concat(m)},this.getKeyOptions("".concat(e,".").concat(m),f.choices,!1,!1))):g.push(React.createElement("option",{key:"".concat(s,"-").concat(m),value:f.value,disabled:h},f.label))}}return u&&!l.includes("gf_custom")&&r&&g.push(React.createElement("option",{key:"".concat(s,"-custom"),value:"gf_custom",disabled:!1},I("Add Custom Key","gravityforms"))),g}},{key:"getValueInput",value:function(){var e=this.props,t=e.choice,n=e.index,r=e.isInvalid,a=e.mapping,i=e.updateMapping,o=e.valueField,c=e.mergeTagSupport,u=t.required,p=this.getValueInputId();return"gf_custom"===a.value?React.createElement(O,{choice:t,index:n,isInvalid:r,mapping:a,updateMapping:i,valueField:o,mergeTagSupport:c,fieldId:p}," "):React.createElement("select",{id:p,disabled:""===a.key||!a.key,value:a.value,onChange:function(e){return i(w(w({},a),{},{value:e.target.value}),n)},className:r?"gform-settings-generic-map__value--invalid":"",required:u},this.getValueOptions().map((function(e){return e.choices&&e.choices.length>0?React.createElement("optgroup",{key:e.label,label:e.label},e.choices.map((function(e){return React.createElement("option",{key:e.value,value:e.value},e.label)}))):React.createElement("option",{key:e.value,value:e.value},e.label)})))}},{key:"getValueOptions",value:function(){var e=this.props,t=e.choice,n=e.valueField,r=n.allow_custom,a=t.name&&n.choice_keys&&n.choice_keys[t.name]?n.choice_keys[t.name]:"default",i=t.choices||n.choices[a];i||(i=[]);var o=i.map((function(e){return e.value}));return r&&!o.includes("gf_custom")&&i.push({label:I("Add Custom Value","gravityforms"),value:"gf_custom",disabled:!1}),gform.applyFilters("gform_generic_map_field_choices",i,this.props.choice,{mapping:this.props.mapping,mappedChoices:this.props.mappedChoices,isInvalid:this.props.isInvalid,keyField:this.props.keyField,valueField:this.props.valueField,canAdd:this.props.canAdd,canDelete:this.props.canDelete,index:this.props.index,inputId:this.props.inputId,inputType:this.props.inputType,mergeTagSupport:this.props.mergeTagSupport})}},{key:"getAddButton",value:function(){var e=this.props,t=e.canAdd,n=e.addMapping,r=e.index;return t?React.createElement("button",{className:"add_field_choice gform-st-icon gform-st-icon--circle-plus gform-settings-generic-map__button gform-settings-generic-map__button--add",onClick:function(e){e.preventDefault(),n(r)}},React.createElement("span",{className:"screen-reader-text"},I("Add","gravityforms"))):null}},{key:"getDeleteButton",value:function(){var e=this.props,t=e.canDelete,n=e.deleteMapping,r=e.index;return t?React.createElement("button",{className:"delete_field_choice gform-st-icon gform-st-icon--circle-minus gform-settings-generic-map__button gform-settings-generic-map__button--delete",onClick:function(e){e.preventDefault(),n(r)}},React.createElement("span",{className:"screen-reader-text"},I("Delete","gravityforms"))):null}}]),n}(S);function F(e,t,n){return t=c(t),o(e,x()?Reflect.construct(t,n||[],c(e).constructor):t.apply(e,n))}function x(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(e){}return(x=function(){return!!e})()}var D=wp.element,T=D.Component,q=D.render,B=function(t){function n(){var t;return e(this,n),(t=F(this,n,arguments)).state={mapping:JSON.parse(document.querySelector('[name="'.concat(t.props.input,'"]')).value)},t.addMapping=t.addMapping.bind(i(t)),t.deleteMapping=t.deleteMapping.bind(i(t)),t.getMapping=t.getMapping.bind(i(t)),t.updateMapping=t.updateMapping.bind(i(t)),t}return p(n,t),a(n,[{key:"componentDidMount",value:function(){this.populateRequiredMappings(),0===this.getRequiredChoices().length&&this.getMapping().length<1&&this.addMapping(0)}},{key:"addMapping",value:function(e){var t=this.props.keyField,n=t.allow_custom,r=t.choices,a=this.getMapping(),i=0===r.length&&n?"gf_custom":"";a.splice(e+1,0,{key:i,custom_key:"",value:"",custom_value:""}),this.setMapping(a)}},{key:"deleteMapping",value:function(e){var t=this.getMapping();t.splice(e,1),this.setMapping(t)}},{key:"getMapping",value:function(){return this.state.mapping}},{key:"setMapping",value:function(e){var t=this.props.input;this.setState({mapping:e}),document.querySelector('[name="'.concat(t,'"]')).value=JSON.stringify(e)}},{key:"updateMapping",value:function(e,t){var n=this.getMapping();e.key||(e.value=""),n[t]=e,this.setMapping(n)}},{key:"getChoice",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];t||(t=this.props.keyField.choices);for(var n=0;n<t.length;n++){var r=t[n];if((r.name||r.value)===e)return t[n];if(r.choices){var a=this.getChoice(e,r.choices);if(a)return a}}return!1}},{key:"getMappedChoices",value:function(){return this.getMapping().filter((function(e){return e.key&&"gf_custom"!==e.key})).map((function(e){return e.key}))}},{key:"getRequiredChoices",value:function(){for(var e=this.props.keyField,t=e.choices,n=e.display_all,r=[],a=0;a<t.length;a++){var i=t[a];if((i.required||n)&&r.push(i.name||i.value),i.choices)for(var o=0;o<i.choices.length;o++){var c=i.choices[o];(c.required||n)&&r.push(c.name||c.value)}}return r}},{key:"populateRequiredMappings",value:function(){for(var e=this.getMapping(),t=this.getRequiredChoices(),n=e.map((function(e){return e.key})),r=0;r<t.length;r++)n.includes(t[r])||e.push({key:t[r],custom_key:"",value:"",custom_value:""});for(var a=0;a<e.length;a++)if(""===e[a].value){var i=this.getChoice(e[a].key);i&&"default_value"in i&&(e[a].value=i.default_value)}this.setMapping(e)}},{key:"countKeyFieldChoices",value:function(){for(var e=this.props.keyField.choices,t=0,n=0;n<e.length;n++)e[n].choices?t+=e[n].choices.length:t++;return t}},{key:"render",value:function(){var e=this,t=this.props,n=t.keyField,r=t.invalidChoices,a=t.limit,i=t.valueField,o=t.input,c=t.inputType,u=t.mergeTagSupport,p=this.getMapping(),l=this.countKeyFieldChoices();return React.createElement("table",{className:"gform-settings-generic-map__table",cellSpacing:"0",cellPadding:"0"},React.createElement("tbody",null,React.createElement("tr",{className:"gform-settings-generic-map__row"},React.createElement("th",{className:"gform-settings-generic-map__column gform-settings-generic-map__column--heading gform-settings-generic-map__column--key"},n.title),React.createElement("th",{className:"gform-settings-generic-map__column gform-settings-generic-map__column--heading gform-settings-generic-map__column--value"},i.title),React.createElement("th",{className:"gform-settings-generic-map__column gform-settings-generic-map__column--heading gform-settings-generic-map__column--error"}),React.createElement("th",{className:"gform-settings-generic-map__column gform-settings-generic-map__column--heading gform-settings-generic-map__column--buttons"})),p.map((function(t,s){var g=e.getChoice(t.key);return React.createElement(P,{key:s,mapping:t,choice:g,mappedChoices:e.getMappedChoices(),isInvalid:t.key&&r.includes(t.key),keyField:n,valueField:i,canAdd:n.allow_custom&&(0===a||p.length<=a)||!n.allow_custom&&p.length<l,canDelete:p.length>1&&!g.required&&!n.display_all,addMapping:e.addMapping,deleteMapping:e.deleteMapping,updateMapping:e.updateMapping,index:s,inputId:o,inputType:c,mergeTagSupport:u})}))))}}]),n}(T);window.initializeFieldMap=function(e,t){q(React.createElement(B,t),document.getElementById(e))}}();