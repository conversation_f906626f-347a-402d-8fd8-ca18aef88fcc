<?php return array('hash_map' => array('admin-components.min.js' => array('version' => 'd3da429245df2304052f25a717f81f33', 'file' => 'admin-components.min.js'), 'field-map.min.js' => array('version' => 'e77eb20133d901fe111e6d657c4c91ee', 'file' => 'field-map.min.js'), 'libraries.min.js' => array('version' => '6c0405e13e9ffc0d2b84693dc42b2b7e', 'file' => 'libraries.min.js'), 'react-utils.min.js' => array('version' => 'bbbfda45ea7581abbbc9eb3813419642', 'file' => 'react-utils.min.js'), 'scripts-admin.min.js' => array('version' => '0d2a28828c0f16792ec10b3ace8829fe', 'file' => 'scripts-admin.min.js'), 'scripts-theme.min.js' => array('version' => 'bab19fd84843dabc070e73326d787910', 'file' => 'scripts-theme.min.js'), 'utils.min.js' => array('version' => '50c7bea9c2320e16728e44ae9fde5f26', 'file' => 'utils.min.js'), 'vendor-admin.min.js' => array('version' => '8136dae29cd48bfcaf5e37fffe3905fe', 'file' => 'vendor-admin.min.js'), 'vendor-theme.min.js' => array('version' => '54e7080aa7a02c83aa61fae430b9d869', 'file' => 'vendor-theme.min.js')));