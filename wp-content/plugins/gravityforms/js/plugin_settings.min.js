!function(t){t(document).ready(function(){gform.adminUtils.handleUnsavedChanges("#gform-settings")});var n=t('div[id="gform_setting_reset"]'),i=t('input[name="_gform_setting_public_key"]'),o=t('input[name="_gform_setting_private_key"]'),r=t('input[name="_gform_setting_reset"]');window.loadRecaptcha=function(){var e=t("#recaptcha"),a=t("#gform-settings-save"),c=t('input[name="_gform_setting_type"]:checked').val();if(window.___grecaptcha_cfg.clients={},window.___grecaptcha_cfg.count=0,e.html(""),r.val(1),t("#recpatcha .gform-settings-field__feedback").remove(),i.val()&&o.val()){switch(a.prop("disabled",!0),grecaptcha.render("recaptcha",{sitekey:i.val(),size:"invisible"===c?c:"",badge:"inline","error-callback":function(){},callback:function(){a.prop("disabled",!1)}}),c){case"checkbox":t('#gforms_checkbox_recaptcha_message, label[for="reset"]').show();break;case"invisible":t('#gforms_checkbox_recaptcha_message, label[for="reset"]').hide()}n.show(),"invisible"===c&&grecaptcha.execute()}else a.prop("disabled",!1),n.hide()},i.on("change",loadRecaptcha),o.on("change",loadRecaptcha),t('input[name="_gform_setting_type"]').on("change",function(){loadRecaptcha()})}(jQuery);