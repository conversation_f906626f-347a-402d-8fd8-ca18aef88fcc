function initLayoutEditor(g){g.fn.setGroupId=function(e){return this.attr("data-groupId",e),this.each(function(){var t=F(g(this));t&&(t.layoutGroupId=e)}),this},g.fn.setGridColumnSpan=function(e){var i;return null===e?this.css("grid-column","auto / auto"):(this.css("grid-column","span {0}".gformFormat(e)),this.each(function(){var t;g(this).hasClass("spacer")?(t=g(this).prev(".gfield"),(i=F(t)).layoutSpacerGridColumnSpan=e):(i=F(g(this)))&&(i.layoutGridColumnSpan=e)})),this},g.fn.getGridColumnSpan=function(){var t;if(void 0!==this.css("gridColumnStart"))return t=parseInt(this.css("gridColumnStart").split(" ")[1]),isNaN(t)&&void 0!==m?m:t},g.fn.resizeGroup=function(t){z(t)},String.prototype.gformFormat||(String.prototype.gformFormat=function(){var i=arguments;return this.replace(/{(\d+)}/g,function(t,e){return void 0!==i[e]?i[e]:t})});var r,n,t,a=g("#form_editor_fields_container"),c=g(".gform_editor"),h=g("#gform_fields"),e=g("#no-fields"),o=g("#no-fields-drop"),d=g(".editor-sidebar"),E=g(".gfield-field-action"),l=M(),p=null,s=".add-buttons button",m=getComputedStyle(h[0])["grid-template-columns"].split(" ").length,_=m/4,u=null,f=!1;function v(t,e){var i='<li data-js-field-loading-placeholder><div class="dropzone__loader"><div class="dropzone__loader-item dropzone__loader-label"></div><div class="dropzone__loader-item dropzone__loader-content"></div></div></li>';void 0!==e?0===e?g("#gform_fields").prepend(i):g("#gform_fields").children().eq(e-1).after(i):jQuery("#field_submit")?jQuery(i).insertBefore(jQuery("#field_submit")):g("#gform_fields").append(i),g("[data-js-field-loading-placeholder]").setGridColumnSpan(m),g("#form_editor_fields_container").addClass("dropzone-loader-visible"),G(g("[data-js-field-loading-placeholder]"),k(!1).data("target"),k(!1).data("where"))}function C(){g("#form_editor_fields_container").removeClass("dropzone-loader-visible"),g("[data-js-field-loading-placeholder]").remove()}function b(i){i.hasClass("ui-draggable")&&i.draggable("destroy").resizable("destroy"),i.draggable({helper:"clone",zIndex:999,handle:".gfield-drag",create:function(t,e){var i,r;W(g(this))||((r=!!(r=g(this).attr("id").replace("field_",""))&&GetFieldById(r))&&r.layoutGroupId&&!c.hasClass("gform_legacy_markup")?i=r.layoutGroupId:S(g(this),!1)||(i=S()),g(this).setGroupId(i))},start:function(t,e){h.addClass("dragging"),a.addClass("droppable"),(p=g(this)).addClass("placeholder")},drag:function(t,e){e.helper.width(p.width()).height(p.height()).setGridColumnSpan(null),helperLeft=gform.tools.isRtl()?e.position.left+e.helper.outerWidth():e.position.left,x(0,e,e.position.top,helperLeft)},stop:function(t,e){h.removeClass("dragging"),a.removeClass("droppable"),p.removeClass("placeholder"),M().removeClass("hovering"),k().data("target")&&G(p,k().data("target"),k().data("where")),k().remove(),e.helper.remove()}}).resizable({handles:"e, w",start:function(t,e){"1"===gf_legacy.is_legacy?(i.resizable("option","minWidth",e.size.width),i.resizable("option","maxWidth",e.size.width),alert(gf_vars.alertLegacyMode)):(u=null,h.addClass("resizing"))},resize:function(t,e){var i,r,o,n,a,d,l,s,f;"1"!==gf_legacy.is_legacy&&(f=h.outerWidth()/m,r=(i=e.element).outerWidth(),r=Math.max(_,Math.round(r/f)),f=i.getGridColumnSpan(),o=w(S(i)),d=i,l=1===(l=(l=o).not(".spacer")).length||l.last()[0]===d[0],d=o.filter(".spacer"),n=l&&!d.length?null:i.next(),null===u&&(u=1<o.length?f+(a=n?I(n):0):m),_="gform_editor_submit_container"===e.element.data("fieldClass")?1:m/4,f=u,"gform_editor_submit_container"===i.next().data("fieldClass")?f=u-1:1<o.length&&!l&&(f=u-_),s=_,f=f,r=Math.max(s,Math.min(f,r)),g().add(e.helper).add(e.element).css("width","auto").css("left","auto").setGridColumnSpan(r),n&&(a=u-r,n.css("width","auto").setGridColumnSpan(a)),r==m||r==u?Q(d):l&&!d.length&&I(o)<m&&(f=S(s=i),e=1,f=g('<div class="spacer gfield"></div>').setGroupId(f).setGridColumnSpan(e),s.after(f)))},stop:function(){"1"!==gf_legacy.is_legacy&&h.removeClass("resizing")}})}function y(t){t.on("mousedown touchstart",function(){gform.tools.trigger("gform/flyout/close-all"),g(this).attr("title","")}).draggable({helper:"clone",revert:function(){return!1},cancel:!1,appendTo:h,containment:"document",start:function(t,e){if(i(),a.addClass("droppable"),1==gf_vars.currentlyAddingField)return!1;e.helper.width(g(this).width()).height(g(this).height()),h.addClass("dragging"),(p=g(this).clone()).addClass("placeholder"),g(this).addClass("fieldPlaceholder")},drag:function(t,e){var i,r;form.fields.length&&(i=+e.position.top+e.helper.outerHeight()/2,r=+e.position.left+e.helper.outerWidth()/2,x(0,e,i,r))},stop:function(t,e){g(this).removeClass("fieldPlaceholder"),a.removeClass("droppable"),h.removeClass("dragging");var i=!1;!form.fields.length&&f?(f=!1,i=H(e.helper.data("type"))):form.fields.length&&k(!1).data("target")&&(i=H(e.helper.data("type"))),i||(k(!1).remove(),p.remove(),p=null),g(this).attr("title",g(this).attr("data-description"))}}).on("click keypress",function(){p=null})}function x(t,s,f,u){M().removeClass("hovering"),function(t,e){o=(gform.tools.isRtl()?h:a).offset().left;var i=h.offset(),r=i.top-a.offset().top,i=i.left-o,o=E.outerWidth()||null,n=-r+o,o=-i+a.outerWidth()-d.outerWidth()-o,r=-r+a.outerHeight(),i=-i;return n<e&&e<r&&i<t&&t<o}(u,f)?f<0?k().css({top:-30,left:0,height:"4px",width:h.outerWidth()}).data({where:"top",target:M().first()}):f>h.outerHeight()?"gform_editor_submit_container"!==M().last().data("field-class")&&"gform_editor_submit_container"!==M().last().prev().data("field-class")&&k().css({top:h.outerHeight()-14,left:0,height:"4px",width:h.outerWidth()}).data({where:"bottom",target:M().last()}):M().not(s.helper).not(this).each(function(){var t=g(this),e=t.position(),i={top:e.top,right:e.left+t.outerWidth(),bottom:e.top+t.outerHeight(),left:e.left};if(d=u,(a=f)<(l=i).bottom&&a>l.top&&d<l.right&&d>l.left){t.addClass("hovering"),W(t)&&(e=(t=t.prev()).position(),n="right");var r,o,n=function(t,e,i,r,o){var n=i.left+r/2,r=i.right-r/2,a=i.top+o/5,o=i.bottom-o/5;{if(e>i.top&&e<a)return"top";if(e<i.bottom&&o<e)return"bottom";if(t>i.left&&t<n)return"left";if(t<i.right&&r<t)return"right"}return"center"}(u,f,i,t.outerWidth(),t.outerHeight()),a=w(S(t),!1),d=a.length>=m/_,l=(S(t)===S(s.helper)&&(d=!1),function(t,e){var i,r,o;if(r=S(e),t=S(t.helper),i=w(r),r===t)return!0;W(e)?e=(o=e).prev():W(e.next())&&!1!==i.index(e.next())&&(o=e.next());r=(o?o.getGridColumnSpan():null)||(j(i)?m/(i.length+1):e.getGridColumnSpan()/2);if(parseInt(r)<3)return!1}(s,t));if("gform_editor_submit_container"===t.data("field-class")){if(gform.tools.isRtl()&&("left"===n||"bottom"===n))return;if("right"===n||"bottom"===n)return}if("left"===n||"right"===n){if("bottom"===t.data("field-position"))return;if(!function(t,e){if(c.hasClass("gform_legacy_markup"))return;if(t.hasClass("gpage")||t.hasClass("gsection")||t.hasClass("gform_hidden"))return;if(e.hasClass("gpage")||e.hasClass("gsection")||e.hasClass("gform_hidden")||"hidden"===e.data("type"))return;if(e.is("button")&&-1!==g.inArray(e.val().toLowerCase(),["page","section"]))return;return 1}(t,p))return;if(d||!1===l)return}if(!("bottom"===n&&0<a.filter('[data-field-class="gform_editor_submit_container"]').length))switch(k().data({where:n,target:t}),o=0<t.parents(".gform-compact-view").length?(r=10,6):(r=30,26),n){case"left":return k().css({top:e.top,left:e.left-10,height:t.outerHeight(),width:"4px"}),!1;case"right":return k().css({top:e.top,left:e.left+t.outerWidth()+6,right:"auto",height:t.outerHeight(),width:"4px"}),!1;case"bottom":return k().css({top:e.top+t.outerHeight()+o,left:0,height:"4px",width:"100%"}),!1;case"top":return k().css({top:e.top-r,left:0,height:"4px",width:"100%"}),!1}}}):k(!1).remove()}function G(t,e,i){var r,o,n,a,d,l,s,f;e&&!e.hasClass("gform_button")&&(d=S(t),s=w(l=S(e)),W(e)?e=(f=e).prev():(W(e.next())||0<e.next().filter("[data-js-field-loading-placeholder]").length)&&!1!==s.index(e.next())&&(f=e.next()),a="left"===i||"right"===i,f&&a&&(r=f.getGridColumnSpan(),Q(f),s=w(l)),"top"==i?e=s.first():"bottom"==i&&(e=s.last()),f=gform.tools.isRtl()?"right":"left","top"==i||i==f?t.insertBefore(e):t.insertAfter(e),a?(r?(n=t,o=r):n=(j(s)?(o=m/(s.length+1),s):(o=(r=e.getGridColumnSpan())/2,e)).add(t),parseInt(o)==o?n.setGridColumnSpan(o):(i=Math.floor(o),f=Math.ceil(o),t.setGridColumnSpan(i),e.setGridColumnSpan(f))):(l=S(),t.setGridColumnSpan(m)),t.setGroupId(l),z(d))}function S(t,e){var i;return i=(i=void 0!==t?t.attr("data-groupId"):i)||!e&&void 0!==e?i:"xxxxxxxx".replace(/[xy]/g,function(t){var e=16*Math.random()|0;return("x"==t?e:3&e|8).toString(16)})}function w(t,e){return e||void 0===e?M().filter('[data-groupId="{0}"]'.gformFormat(t)).not(".ui-draggable-dragging"):M().filter('[data-groupId="{0}"]'.gformFormat(t)).not(".ui-draggable-dragging").not(".spacer")}function I(t){var e=0;return t.each(function(){e+=g(this).getGridColumnSpan()}),e}function j(t){var e,i;return 0===t.length?i=!0:(e=t.first().getGridColumnSpan(),i=!0,t.each(function(){if(g(this).getGridColumnSpan()!==e)return i=!1}),i)}function z(t){var t=w(t),e=m/t.length,i=t.filter(".spacer");t[0]===i[0]&&0<t.length&&Q(i),t.setGridColumnSpan(e)}function A(){SetSubmitLocation("bottom"),jQuery("#field_submit").attr("data-field-position","bottom"),jQuery('input[name="submit_location"][value="bottom"]').prop("checked",!0)}function Q(t){t.setGridColumnSpan(0).remove()}function W(t){return 0<t.filter(".spacer").length}function F(t){t=t.attr("id"),t=!(!t||-1===t.indexOf("field_"))&&String(t).replace("field_","");return!!t&&GetFieldById(t)}function H(t){return StartAddField(t,Math.max(0,h.children().index(p)))}function i(){M().removeClass("field_selected"),g(".sidebar").tabs("option","active",0),HideSettings()}function M(){return h.find(".gfield")}function k(t){t=void 0===t;var e=g("#indicator");return!e.length&&t&&(e=g('<div id="indicator"></div>'),h.append(e)),e}b(l),"1"!==window.gf_legacy.is_legacy&&l.length&&(t=function(){var t=[],e=[],i=l[0].offsetTop;return l.each(function(){i!==this.offsetTop&&e.length&&(t.push(e),e=[]),e.push({el:this,groupId:this.dataset.groupid}),i=this.offsetTop}),t}(),n=[],t.forEach(function(t){var e,i,r=[],o=!1;t.forEach(function(t){-1!==n.indexOf(t.groupId)&&(o=!0),r.push(t.groupId)}),r.every(function(t,e,i){return t===i[0]})&&!o||(e=t,i=S(),e.forEach(function(t){g(t.el).setGroupId(i)})),n.push(t[0].groupId)})),"inline"===g("#field_submit").data("field-position")&&(t=jQuery("#field_submit").prev().attr("data-groupid"),jQuery("#field_submit").setGroupId(t)),y(g(s)),e.droppable({accept:s,activate:function(t,e){o.show(),g(this).addClass("ready")},over:function(){g(this).addClass("hovering"),o.addClass("hovering")},out:function(){g(this).removeClass("hovering"),o.removeClass("hovering")},drop:function(){f=!0,g(this).removeClass("hovering"),o.removeClass("hovering")},deactivate:function(){g(this).removeClass("ready")}}),a.on("click",function(){i()}),g(document).on("gform_field_added",function(t,e,i){var r=g("#field_"+i.id),r=(null===p?(r.setGroupId(S()),"inline"==jQuery("#field_submit").data("field-position")&&A()):(G(r,k().data("target"),k().data("where")),p.remove(),p=null),a.hasClass("form_editor_fields_no_fields")&&(gform.simplebar.initializeInstance(a[0]),setTimeout(function(){o.hide(),a.removeClass("form_editor_fields_no_fields")},200)),k().remove(),b(r),"page"===i.type&&(A(),jQuery('input[name="submit_location"][value="inline"]').prop("disabled",!0),SetFieldAccessibilityWarning("submit_location_setting","below")),0<!jQuery("#field_submit").length&&StartAddField("submit",Math.max(0,h.children().index(p)+1)),new Event("gform/layout_editor/field_modified"));document.dispatchEvent(r)}),g(document).on("gform_field_deleted",function(t,e,i){r=S(g("#field_"+i)),HasPageField()||(jQuery('input[name="submit_location"][value="inline"]').prop("disabled",!1),jQuery(".submit_location_setting").prev(".gform-alert--notice").remove());i=new Event("gform/layout_editor/gform_field_deleted");document.dispatchEvent(i)}),gform.addAction("gform_after_field_removed",function(t,e){z(r)}),gform.addAction("gform_field_duplicated",function(t,e,i,r){w(S(g("#field_"+r))).last().after(i),i.setGridColumnSpan(m).setGroupId(S()),b(i)}),gform.addAction("gform_after_refresh_field_preview",function(t){b(g("#field_"+t))}),gform.addAction("gform_form_saving_action_element_after_reload",function(t,e,i,r,o){g(i).hasClass("gfield")&&b(g('[data-js-reload="'+r+'"]')),g(i).hasClass("editor-sidebar")&&y(g(s))}),gform.addAction("gform_form_saving_action_editor_has_new_components",function(t,e,i,r,o){y(g(s))}),gform.addAction("gform_before_get_field_markup",function(t,e,i){v(0,i)}),gform.addAction("gform_after_get_field_markup",function(t,e,i){C()}),gform.addAction("gform_after_get_field_markup",function(t,e,i){b(jQuery("#field_submit"))}),gform.addAction("gform_before_field_duplicated",function(t){t=g("#field_"+t);v(0,h.children().index(t)+1)}),gform.addAction("gform_field_duplicated",function(){C()}),gform.addAction("gform_before_refresh_field_preview",function(t){jQuery("#field_"+t).addClass("loading")}),gform.addAction("gform_after_refresh_field_preview",function(t){jQuery("#field_"+t).removeClass("loading")})}initLayoutEditor(jQuery);