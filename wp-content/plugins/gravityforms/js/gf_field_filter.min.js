!function(a){var n,f,s,g,c,u,d,m;function h(){var e="<div class='gform-field-filter'>";return e+(function(){var e,t,r=[];for(r.push("<select class='gform-filter-field' name='f[]' >"),e=0;e<s.length;e++)t=function e(t,r){r=r||0;var i,o,l,n,f,s,a,g,c,u="",d=[],m="&nbsp;&nbsp;&nbsp;&nbsp;";o=t.key;if(t.group){for(f=t.filters.length,n=[],g=t.isNestable?r+1:r,i=0;i<f;i++)(s=t.filters[i]).group?(a=e(s,g),n.push(a)):(c=m.repeat(g),l=c+s.text,a=s.key,u=b(a)?'disabled="disabled"':"",n.push('<option {0} value="{1}">{2}</option>'.gformFormat(u,a,l)));c=m.repeat(r),r=c+t.text,t.isNestable?d.push('<optgroup label="{0}"></optgroup>{1}'.gformFormat(r,n.join(""))):d.push('<optgroup label="{0}">{1}</optgroup>'.gformFormat(r,n.join("")))}else u=t.preventMultiple&&b(o)?"disabled='disabled'":"",l=t.text,d.push('<option {0} value="{1}">{2}</option>'.gformFormat(u,o,l));return d.join("")}(s[e]),r.push(t);return r.push("</select>"),r.push("<input type='hidden' class='gform-filter-type' name='t[]' value='' >"),r.join("")}()+r()+i()+(e="",e=d?(e+="<button class='gform-add add_field_choice gform-st-icon gform-st-icon--circle-plus' title='{0}'"+"></button>".gformFormat(gf_vars.addFieldFilter))+"<button class='gform-remove delete_field_choice gform-st-icon gform-st-icon--circle-minus' title='"+gf_vars.removeFieldFilter+"'></button>":e))+"</div>"}function p(e){var t=a(e),r=o(t.siblings(".gform-filter-field").val());r&&t.siblings(".gform-filter-value").replaceWith(i(r,e.value)),l(),window.gformInitDatepicker&&gformInitDatepicker()}function v(e){var t=o(e.value);t&&((e=a(e)).siblings(".gform-filter-value").replaceWith(i(t)),e.siblings(".gform-filter-type").val(t.type),e.siblings(".gform-filter-operator").replaceWith(r(t)),e.siblings(".gform-filter-operator").change()),l()}function b(e){e=e.toString();var r=[];return a(".gform-filter-field :selected").each(function(e,t){r[e]=a(t).val()}),-1<a.inArray(e,r)}function r(e){var t,r,i="<select name='o[]' class='gform-filter-operator'>";if(e)for(t=0;t<e.operators.length;t++)r=e.operators[t],i+='<option value="{0}">{1}</option>'.gformFormat(r,gf_vars[f[r]]);return i+="</select>"}function i(e,t){var r,i,o,l,n,f="",s="gform-filter-value";if(e&&void 0!==e.cssClass&&(s+=" "+e.cssClass),e&&e.values&&"contains"!=t){for(void 0!==e.placeholder&&(f+='<option value="">{0}</option>'.gformFormat(e.placeholder)),r=0;r<e.values.length;r++)i=e.values[r].value,o=e.values[r].text,e.values[r].operators&&-1===a.inArray(t,e.values[r].operators)||(f+='<option value="{0}">{1}</option>'.gformFormat(i,o));l="<select name='v[]' class='{0}'>{1}</select>".gformFormat(s,f)}else n=e&&void 0!==e.placeholder?"placeholder='{0}'".gformFormat(e.placeholder):"",l="<input type='text' value='' name='v[]' class='{0}' {1}/>".gformFormat(s,n);return l}function o(e,t){var r;if(e){t=t||s;for(var i=0;i<t.length;i++){if(e==t[i].key)return t[i];if(t[i].group&&(r=o(e,t[i].filters)))return r}}}function F(){var e;u&&(e=a("#gform-field-filters"),a(".gform-field-filter").length<=1?a(n).hasClass("ui-resizable")&&n.resizable("destroy"):e.get(0).scrollHeight>n.height()||n.height()>=m?(n.css({"min-height":m+"px","border-bottom":"5px double #DDD"}).resizable({handles:"s",minHeight:m}),e.css("min-height",m)):n.css({"min-height":"","border-bottom":""}))}function y(){var e="",e=(e+="<div id='gform-no-filters' >"+gf_vars.addFieldFilter)+("<button class='gform-add add_field_choice gform-st-icon gform-st-icon--circle-plus' title='{0}'"+"></div>".gformFormat(gf_vars.addFieldFilter));a("#gform-field-filters").html(e),u&&(n.css({"min-height":"","border-bottom":""}),n.height(80),a("#gform-field-filters").css("min-height",""))}function l(){a("select.gform-filter-field option").removeAttr("disabled"),a("select.gform-filter-field").each(function(e){var t=o(this.value);void 0!==t&&t.preventMultiple&&b(this.value)&&a("select.gform-filter-field option[value='"+this.value+"']:not(:selected)").attr("disabled","disabled")})}function _(e){e='<select name="mode"><option value="all" {0}>{1}</option><option value="any" {2}>{3}</option></select>'.gformFormat(t("all",e),gf_vars.all,t("any",e),gf_vars.any);return gf_vars.filterAndAny.gformFormat(e)}function t(e,t){return e==t?'selected="selected"':""}function j(e){e=a(e),e=e.is("button")?e.parent():e;e.after(h()),e.next("div").find(".gform-filter-field").change().find(".gform-filter-operator").change(),1==a(".gform-field-filter").length&&e.after(_()),F()}function k(e){a(e).parent().remove(),0==a(".gform-field-filter").length&&y(),l(),F()}a.fn.gfFilterUI=function(e,t,r,i){e=e,t=t,r=r,i=i,(n=a(this)).css("position","relative").html('<div id="gform-field-filters"></div>'),u=void 0!==(m=i)&&0<m,f={is:"is",isnot:"isNot",">":"greaterThan","<":"lessThan",contains:"contains",starts_with:"startsWith",ends_with:"endsWith"},gf_vars.baseUrl,s=e,g=t&&t.filters?t.filters:[],c=t&&t.mode?t.mode:"all",d=!(void 0!==r&&!r);var o,l=g;if(n.on("change",".gform-filter-field",function(){v(this)}),n.on("click","#gform-no-filters",function(){if(a(".gform-field-filter").length==0)j(this);a(this).remove()}),n.on("click",".gform-add",function(e){j(this);e.preventDefault()}),n.on("click",".gform-remove",function(){k(this)}),n.on("change",".gform-filter-operator",function(){p(this,this.value)}),void 0===l||0==l.length)y();else{for("off"!=c&&a("#gform-field-filters").append(_(c)),o=0;o<l.length;o++)a("#gform-field-filters").append(h());a(".gform-filter-field").each(function(e){e=l[e].field;jQuery(this).val(e),v(this)}),a(".gform-filter-operator").each(function(e){e=l[e].operator;jQuery(this).val(e),p(this,this.value)}),a(".gform-filter-value").each(function(e){e=l[e].value;jQuery(this).val(e),jQuery(this).change()}),F()}return this},String.prototype.gformFormat||(String.prototype.gformFormat=function(){var r=arguments;return this.replace(/{(\d+)}/g,function(e,t){return void 0!==r[t]?r[t]:e})})}((window.gfFilterUI=window.gfFilterUI||{},jQuery));