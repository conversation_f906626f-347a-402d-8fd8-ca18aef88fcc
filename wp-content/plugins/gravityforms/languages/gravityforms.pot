# Copyright (C) 2024 Gravity Forms
# This file is distributed under the GPL-2.0+.
msgid ""
msgstr ""
"Project-Id-Version: Gravity Forms 2.8.14\n"
"Report-Msgid-Bugs-To: https://gravityforms.com/support\n"
"Last-Translator: Gravity Forms <<EMAIL>>\n"
"Language-Team: Gravity Forms <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"POT-Creation-Date: 2024-07-10T16:11:10+00:00\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"X-Generator: WP-CLI 2.10.0\n"
"X-Domain: gravityforms\n"

#. Plugin Name of the plugin
#. Author of the plugin
#: gravityforms.php:1561
#: gravityforms.php:1622
#: gravityforms.php:2548
#: includes/system-status/class-gf-system-report.php:364
#: includes/system-status/class-gf-update.php:207
#: assets/js/src/admin/block-editor/blocks/form/edit.js:835
msgid "Gravity Forms"
msgstr ""

#. Plugin URI of the plugin
#. Author URI of the plugin
msgid "https://gravityforms.com"
msgstr ""

#. Description of the plugin
msgid "Easily create web forms and manage form entries within the WordPress admin."
msgstr ""

#: common.php:649
#: common.php:983
#: form_detail.php:2971
msgid "Insert Merge Tag"
msgstr ""

#: common.php:711
#: js.php:1507
msgid "All Submitted Fields"
msgstr ""

#: common.php:784
msgid "All Pricing Fields"
msgstr ""

#: common.php:793
#: form_detail.php:2972
#: js.php:1507
msgid "User IP Address"
msgstr ""

#: common.php:796
#: common.php:800
#: entry_detail.php:1168
#: form_detail.php:863
#: form_detail.php:1872
#: form_detail.php:2973
#: form_detail.php:2974
#: includes/addon/class-gf-payment-addon.php:3023
#: includes/fields/class-gf-field-date.php:13
#: js.php:807
#: js.php:1507
msgid "Date"
msgstr ""

#: common.php:804
#: form_detail.php:2975
msgid "Embed Post/Page Id"
msgstr ""

#: common.php:808
#: form_detail.php:2976
msgid "Embed Post/Page Title"
msgstr ""

#: common.php:810
#: form_detail.php:2977
#: includes/class-personal-data.php:685
msgid "Embed URL"
msgstr ""

#: common.php:811
#: entry_detail.php:1308
#: entry_list.php:875
#: export.php:1120
#: forms_model.php:6550
#: select_columns.php:196
msgid "Entry Id"
msgstr ""

#: common.php:812
msgid "Entry URL"
msgstr ""

#: common.php:813
msgid "Form Id"
msgstr ""

#: common.php:814
#: form_list.php:46
#: form_settings.php:114
#: gravityforms.php:4647
#: includes/addon/class-gf-addon.php:3029
#: includes/settings/fields/class-generic-map.php:587
#: includes/template-library/config/class-gf-template-library-config.php:90
#: js.php:1507
#: tooltips.php:24
#: assets/js/src/admin/block-editor/blocks/form/edit.js:300
msgid "Form Title"
msgstr ""

#: common.php:815
#: form_detail.php:2978
msgid "HTTP User Agent"
msgstr ""

#: common.php:816
#: form_detail.php:2979
msgid "HTTP Referer URL"
msgstr ""

#: common.php:819
#: export.php:1128
msgid "Post Id"
msgstr ""

#: common.php:822
msgid "Post Edit URL"
msgstr ""

#: common.php:828
#: form_detail.php:2980
msgid "User Display Name"
msgstr ""

#: common.php:830
#: form_detail.php:2981
msgid "User Email"
msgstr ""

#: common.php:831
#: form_detail.php:2982
msgid "User Login"
msgstr ""

#: common.php:844
msgid "Required form fields"
msgstr ""

#: common.php:848
msgid "Optional form fields"
msgstr ""

#: common.php:852
msgid "Pricing form fields"
msgstr ""

#: common.php:856
#: common.php:4682
#: form_detail.php:1600
#: form_detail.php:1601
#: includes/fields/class-gf-field-radio.php:372
#: includes/setup-wizard/config/class-gf-setup-wizard-config-i18n.php:92
#: includes/setup-wizard/config/class-gf-setup-wizard-config.php:192
#: includes/setup-wizard/config/class-gf-setup-wizard-config.php:242
#: includes/setup-wizard/config/class-gf-setup-wizard-config.php:307
#: includes/template-library/templates/templates.php:6847
#: includes/template-library/templates/templates.php:11044
#: includes/template-library/templates/templates.php:11157
msgid "Other"
msgstr ""

#: common.php:860
#: common.php:1008
#: form_detail.php:511
#: form_detail.php:1846
msgid "Custom"
msgstr ""

#: common.php:950
msgid "Select image size"
msgstr ""

#: common.php:951
msgid "Thumbnail"
msgstr ""

#: common.php:952
msgid "Thumbnail - Left Aligned"
msgstr ""

#: common.php:953
msgid "Thumbnail - Centered"
msgstr ""

#: common.php:954
msgid "Thumbnail - Right Aligned"
msgstr ""

#: common.php:956
#: form_detail.php:916
#: form_detail.php:1611
#: form_display.php:3772
#: includes/fields/class-gf-field.php:2630
#: assets/js/src/admin/block-editor/blocks/form/edit.js:409
msgid "Medium"
msgstr ""

#: common.php:957
msgid "Medium - Left Aligned"
msgstr ""

#: common.php:958
msgid "Medium - Centered"
msgstr ""

#: common.php:959
msgid "Medium - Right Aligned"
msgstr ""

#: common.php:961
#: form_detail.php:917
#: form_detail.php:1611
#: includes/fields/class-gf-field.php:2631
#: assets/js/src/admin/block-editor/blocks/form/edit.js:410
msgid "Large"
msgstr ""

#: common.php:962
msgid "Large - Left Aligned"
msgstr ""

#: common.php:963
msgid "Large - Centered"
msgstr ""

#: common.php:964
msgid "Large - Right Aligned"
msgstr ""

#: common.php:966
msgid "Full Size"
msgstr ""

#: common.php:967
msgid "Full Size - Left Aligned"
msgstr ""

#: common.php:968
msgid "Full Size - Centered"
msgstr ""

#: common.php:969
msgid "Full Size - Right Aligned"
msgstr ""

#: common.php:984
msgid "Allowable form fields"
msgstr ""

#. translators: %s: relative time from now, used for generic date comparisons. "1 day ago", or "20 seconds ago"
#: common.php:1446
#: common.php:3275
msgid "%s ago"
msgstr ""

#: common.php:2261
msgid "Cannot send email because the TO address is invalid."
msgstr ""

#: common.php:2268
msgid "Cannot send email because there is no SUBJECT and no MESSAGE."
msgstr ""

#: common.php:2275
msgid "Cannot send email because the FROM address is invalid."
msgstr ""

#: common.php:3123
msgid "Gravity Forms requires WordPress %s or greater. You must upgrade WordPress in order to use Gravity Forms"
msgstr ""

#: common.php:3288
msgid "%1$s at %2$s"
msgstr ""

#. Translators: link to the "Edit Post" page for this post.
#: common.php:3819
msgid "You can <a href=\"%s\">edit this post</a> from the post page."
msgstr ""

#: common.php:3842
msgid "Pricing fields are not editable"
msgstr ""

#: common.php:3943
#: common.php:4001
msgid "Preview this form"
msgstr ""

#: common.php:3950
#: gravityforms.php:5652
#: assets/js/src/admin/block-editor/blocks/form/edit.js:618
msgid "Preview"
msgstr ""

#: common.php:4108
msgid "There was an problem while verifying your file."
msgstr ""

#: common.php:4113
msgid "Sorry, this file extension is not permitted for security reasons."
msgstr ""

#: common.php:4116
msgid "Sorry, this file type is not permitted for security reasons."
msgstr ""

#: common.php:4486
msgid "Akismet Spam Filter"
msgstr ""

#: common.php:5350
msgid "New row added."
msgstr ""

#: common.php:5351
msgid "Row removed"
msgstr ""

#: common.php:5352
msgid "The form has been saved.  The content contains the link to return and complete the form."
msgstr ""

#: common.php:5366
#: common.php:6097
#: form_list.php:150
#: form_list.php:607
#: includes/addon/class-gf-feed-addon.php:2571
#: includes/class-confirmation.php:156
#: includes/class-confirmation.php:1064
#: includes/config/items/class-gf-config-admin-i18n.php:40
#: includes/license/class-gf-license-api-response.php:157
#: js.php:289
#: js.php:389
#: notification.php:910
#: notification.php:1492
msgid "Active"
msgstr ""

#: common.php:5367
#: form_list.php:146
#: form_list.php:610
#: includes/addon/class-gf-feed-addon.php:2574
#: includes/class-confirmation.php:152
#: includes/class-confirmation.php:1067
#: includes/config/items/class-gf-config-admin-i18n.php:39
#: js.php:289
#: js.php:391
#: notification.php:906
#: notification.php:1495
msgid "Inactive"
msgstr ""

#: common.php:5368
#: common.php:5421
#: form_detail.php:1662
#: includes/form-editor/save-form/config/class-gf-form-editor-form-save-config.php:44
#: select_columns.php:276
msgid "Save"
msgstr ""

#: common.php:5369
#: entry_detail.php:1402
#: gravityforms.php:4655
#: includes/webapi/webapi.php:446
#: includes/webapi/webapi.php:539
#: includes/webapi/webapi.php:618
msgid "Update"
msgstr ""

#: common.php:5370
#: form_display.php:336
#: form_display.php:1373
#: form_display.php:4170
#: includes/setup-wizard/config/class-gf-setup-wizard-config-i18n.php:51
#: includes/template-library/templates/templates.php:2516
#: includes/template-library/templates/templates.php:2712
#: includes/template-library/templates/templates.php:3140
#: includes/template-library/templates/templates.php:3431
#: includes/template-library/templates/templates.php:3627
#: includes/template-library/templates/templates.php:4007
#: includes/template-library/templates/templates.php:4294
#: includes/template-library/templates/templates.php:4490
#: includes/template-library/templates/templates.php:4897
#: includes/template-library/templates/templates.php:5438
#: includes/template-library/templates/templates.php:5797
#: includes/template-library/templates/templates.php:6037
#: includes/template-library/templates/templates.php:6573
#: includes/template-library/templates/templates.php:6904
#: includes/template-library/templates/templates.php:7177
#: includes/template-library/templates/templates.php:7833
#: includes/template-library/templates/templates.php:8064
#: js.php:648
msgid "Previous"
msgstr ""

#: common.php:5371
msgid "Select a format"
msgstr ""

#: common.php:5372
msgid "Column"
msgstr ""

#: common.php:5373
msgid "5 of %d items shown. Edit field to view all"
msgstr ""

#: common.php:5374
#: export.php:435
#: export.php:558
#: includes/fields/class-gf-field-checkbox.php:170
#: includes/fields/class-gf-field-checkbox.php:708
msgid "Select All"
msgstr ""

#: common.php:5375
msgid "Enter a value"
msgstr ""

#: common.php:5376
msgid "Untitled Form"
msgstr ""

#: common.php:5377
#: includes/template-library/templates/templates.php:363
msgid "We would love to hear from you! Please fill out this form and we will get in touch with you shortly."
msgstr ""

#: common.php:5378
#: common.php:5429
#: forms_model.php:6954
msgid "Thanks for contacting us! We will get in touch with you shortly."
msgstr ""

#: common.php:5379
#: form_display.php:1723
#: form_list.php:326
#: gravityforms.php:3195
#: includes/fields/class-gf-field-submit.php:158
#: includes/settings/fields/class-button.php:50
#: includes/template-library/templates/templates.php:22
#: includes/template-library/templates/templates.php:367
#: includes/template-library/templates/templates.php:1751
#: includes/template-library/templates/templates.php:2280
#: includes/template-library/templates/templates.php:3235
#: includes/template-library/templates/templates.php:4098
#: includes/template-library/templates/templates.php:6147
#: includes/template-library/templates/templates.php:8616
#: includes/template-library/templates/templates.php:9174
#: includes/template-library/templates/templates.php:10274
msgid "Submit"
msgstr ""

#: common.php:5380
msgid "The submit button for this form"
msgstr ""

#: common.php:5381
#: includes/settings/fields/class-notification-routing.php:350
msgid "Loading..."
msgstr ""

#: common.php:5382
msgid "this field if"
msgstr ""

#: common.php:5383
msgid "this section if"
msgstr ""

#: common.php:5384
msgid "this page if"
msgstr ""

#: common.php:5385
msgid "this form button if"
msgstr ""

#: common.php:5386
#: js.php:277
msgid "Show"
msgstr ""

#: common.php:5387
msgid "Hide"
msgstr ""

#: common.php:5388
#: includes/logging/logging.php:365
#: includes/settings/fields/class-checkbox-and-select.php:50
msgid "Enable"
msgstr ""

#: common.php:5389
msgid "Disable"
msgstr ""

#: common.php:5390
#: includes/addon/class-gf-payment-addon.php:2603
#: includes/addon/class-gf-payment-addon.php:2648
#: includes/webapi/webapi.php:559
#: includes/wizard/steps/class-gf-installation-wizard-step-background-updates.php:110
msgid "Enabled"
msgstr ""

#: common.php:5391
#: includes/wizard/steps/class-gf-installation-wizard-step-background-updates.php:110
msgid "Disabled"
msgstr ""

#: common.php:5392
msgid "Configure"
msgstr ""

#: common.php:5393
#: export.php:685
#: includes/addon/class-gf-payment-addon.php:2541
#: includes/addon/class-gf-payment-addon.php:2543
#: tooltips.php:104
#: tooltips.php:121
#: tooltips.php:144
msgid "Conditional Logic"
msgstr ""

#: common.php:5394
msgid "Conditional logic allows you to change what the user sees depending on the fields they select."
msgstr ""

#: common.php:5399
msgid "Adding conditional logic to the form submit button could cause usability problems for some users and negatively impact the accessibility of your form. Learn more about button conditional logic in our %1$sdocumentation%2$s."
msgstr ""

#: common.php:5403
#: includes/class-confirmation.php:235
#: includes/class-confirmation.php:285
#: includes/class-confirmation.php:1190
#: includes/embed-form/config/class-gf-embed-config.php:179
#: includes/fields/class-gf-field-page.php:12
#: js.php:82
msgid "Page"
msgstr ""

#: common.php:5404
#: form_detail.php:774
msgid "Next Button"
msgstr ""

#: common.php:5405
msgid "Submit Button"
msgstr ""

#: common.php:5406
msgctxt "Conditional Logic"
msgid "All"
msgstr ""

#: common.php:5407
msgctxt "Conditional Logic"
msgid "Any"
msgstr ""

#: common.php:5408
msgid "of the following match:"
msgstr ""

#: common.php:5409
#: includes/addon/class-gf-addon.php:4068
#: includes/settings/fields/class-notification-routing.php:76
#: includes/settings/fields/class-notification-routing.php:227
msgid "is"
msgstr ""

#: common.php:5410
#: includes/addon/class-gf-addon.php:4072
#: includes/settings/fields/class-notification-routing.php:77
#: includes/settings/fields/class-notification-routing.php:228
msgid "is not"
msgstr ""

#: common.php:5411
#: includes/addon/class-gf-addon.php:4076
#: includes/settings/fields/class-notification-routing.php:78
#: includes/settings/fields/class-notification-routing.php:229
msgid "greater than"
msgstr ""

#: common.php:5412
#: includes/addon/class-gf-addon.php:4080
#: includes/settings/fields/class-notification-routing.php:79
#: includes/settings/fields/class-notification-routing.php:230
msgid "less than"
msgstr ""

#: common.php:5413
#: includes/addon/class-gf-addon.php:4084
#: includes/settings/fields/class-notification-routing.php:80
#: includes/settings/fields/class-notification-routing.php:231
msgid "contains"
msgstr ""

#: common.php:5414
#: includes/addon/class-gf-addon.php:4088
#: includes/settings/fields/class-notification-routing.php:81
#: includes/settings/fields/class-notification-routing.php:232
msgid "starts with"
msgstr ""

#: common.php:5415
#: includes/addon/class-gf-addon.php:4092
#: includes/settings/fields/class-notification-routing.php:82
#: includes/settings/fields/class-notification-routing.php:233
msgid "ends with"
msgstr ""

#: common.php:5416
msgid "Empty (no choices selected)"
msgstr ""

#: common.php:5418
msgid "This form has legacy markup enabled and doesn’t support field resizing within the editor. Please disable legacy markup in the form settings to enable live resizing."
msgstr ""

#: common.php:5419
msgid "Use this confirmation if"
msgstr ""

#: common.php:5420
msgid "Send this notification if"
msgstr ""

#: common.php:5422
msgid "Saving..."
msgstr ""

#: common.php:5423
msgid "Are you sure you wish to cancel these changes?"
msgstr ""

#: common.php:5424
msgid "There was an issue saving this confirmation."
msgstr ""

#: common.php:5425
msgid "Are you sure you wish to delete this confirmation?"
msgstr ""

#: common.php:5426
#: form_settings.php:1221
msgid "There was an issue deleting this confirmation."
msgstr ""

#: common.php:5427
msgid "There are unsaved changes to the current confirmation. Would you like to discard these changes?"
msgstr ""

#: common.php:5428
msgid "Untitled Confirmation"
msgstr ""

#: common.php:5430
msgid "Please select a page."
msgstr ""

#: common.php:5431
msgid "Please enter a URL."
msgstr ""

#: common.php:5432
msgid "Please enter a confirmation name."
msgstr ""

#: common.php:5433
msgid "Warning! Deleting this field will also delete all entry data associated with it. 'Cancel' to stop. 'OK' to delete."
msgstr ""

#: common.php:5434
msgid "Warning! You're about to delete this field. 'Cancel' to stop. 'OK' to delete."
msgstr ""

#: common.php:5436
msgid "Warning! This form contains conditional logic dependent upon this field. Deleting this field will deactivate those conditional logic rules and also delete all entry data associated with the field. 'OK' to delete, 'Cancel' to abort."
msgstr ""

#: common.php:5437
msgid "This form contains conditional logic dependent upon this choice. Are you sure you want to delete this choice? 'OK' to delete, 'Cancel' to abort."
msgstr ""

#: common.php:5438
msgid "This form contains conditional logic dependent upon this choice. Are you sure you want to modify this choice? 'OK' to delete, 'Cancel' to abort."
msgstr ""

#: common.php:5439
msgid "This form contains conditional logic dependent upon this field. Are you sure you want to mark this field as Admin Only? 'OK' to confirm, 'Cancel' to abort."
msgstr ""

#: common.php:5441
#: includes/merge-tags/config/class-gf-merge-tags-config-i18n.php:27
msgid "Insert Merge Tags"
msgstr ""

#: common.php:5450
msgid "Add a condition"
msgstr ""

#: common.php:5451
msgid "Remove a condition"
msgstr ""

#: common.php:5452
msgid "{0} of the following match:"
msgstr ""

#: common.php:5454
msgid "Custom Choices"
msgstr ""

#: common.php:5455
msgid "Predefined Choices"
msgstr ""

#. translators: {field_title} and {field_type} should not be translated , they are variables
#: common.php:5458
msgid "{field_label} - {field_type}, jump to this field's settings"
msgstr ""

#: common.php:5470
msgid "ID: "
msgstr ""

#. Translators: This string is a list of name prefixes/honorifics.  If the language you are translating into doesn't have equivalents, just provide a list with as many or few prefixes as your language has.
#: common.php:5477
msgid "Mr., Mrs., Miss, Ms., Mx., Dr., Prof., Rev."
msgstr ""

#: common.php:5504
#: js.php:66
msgid "To use conditional logic, please create a field that supports conditional logic."
msgstr ""

#: common.php:5507
msgid "add another rule"
msgstr ""

#: common.php:5508
msgid "remove this rule"
msgstr ""

#: common.php:5919
msgid "Any form field"
msgstr ""

#: common.php:6026
#: includes/addon/class-gf-addon.php:3025
#: includes/settings/fields/class-generic-map.php:571
msgid "Entry ID"
msgstr ""

#: common.php:6030
#: export.php:1121
#: forms_model.php:6556
#: includes/addon/class-gf-addon.php:3026
#: includes/settings/fields/class-generic-map.php:575
#: select_columns.php:197
msgid "Entry Date"
msgstr ""

#: common.php:6032
#: common.php:6065
#: includes/fields/class-gf-field-date.php:894
#: includes/fields/class-gf-field-date.php:1088
msgid "yyyy-mm-dd"
msgstr ""

#: common.php:6036
msgid "Starred"
msgstr ""

#: common.php:6050
#: includes/class-personal-data.php:684
#: tooltips.php:166
msgid "IP Address"
msgstr ""

#: common.php:6054
msgid "Source URL"
msgstr ""

#: common.php:6058
#: export.php:1127
#: forms_model.php:6562
#: select_columns.php:200
msgid "Payment Status"
msgstr ""

#: common.php:6063
#: export.php:1126
#: forms_model.php:6568
#: select_columns.php:203
msgid "Payment Date"
msgstr ""

#: common.php:6069
#: export.php:1125
#: forms_model.php:6571
#: includes/addon/class-gf-payment-addon.php:2497
#: includes/addon/class-gf-payment-addon.php:2502
#: select_columns.php:202
msgid "Payment Amount"
msgstr ""

#: common.php:6073
msgid "Transaction ID"
msgstr ""

#: common.php:6077
#: entry_detail.php:1324
#: forms_model.php:6574
#: includes/webapi/includes/class-gf-api-keys-table.php:28
#: includes/webapi/webapi.php:401
#: select_columns.php:204
msgid "User"
msgstr ""

#: common.php:6093
msgid "Authorized"
msgstr ""

#: common.php:6094
msgid "Paid"
msgstr ""

#: common.php:6095
msgid "Processing"
msgstr ""

#: common.php:6096
msgid "Failed"
msgstr ""

#: common.php:6098
#: includes/config/items/class-gf-config-multifile.php:36
msgid "Cancelled"
msgstr ""

#: common.php:6099
#: includes/locking/class-gf-locking.php:206
msgid "Pending"
msgstr ""

#: common.php:6100
msgid "Refunded"
msgstr ""

#: common.php:6101
msgid "Voided"
msgstr ""

#: common.php:6968
msgid "Visible"
msgstr ""

#: common.php:6970
msgid "Default option. The field is visible when viewing the form."
msgstr ""

#: common.php:6973
#: form_detail.php:671
#: form_detail.php:753
#: form_detail.php:860
#: form_detail.php:1387
#: form_detail.php:2134
#: form_detail.php:2168
#: includes/fields/class-gf-field-hidden.php:13
msgid "Hidden"
msgstr ""

#: common.php:6975
msgid "The field is hidden when viewing the form. Useful when you require the functionality of this field but do not want the user to be able to see this field."
msgstr ""

#: common.php:6978
msgid "Administrative"
msgstr ""

#: common.php:6980
msgid "The field is only visible when administering submitted entries. The field is not visible or functional when viewing the form."
msgstr ""

#: common.php:7011
#: form_detail.php:2518
msgid "Visibility"
msgstr ""

#: common.php:7011
msgid "Select the visibility for this field."
msgstr ""

#: currency.php:153
msgid "U.S. Dollar"
msgstr ""

#: currency.php:163
msgid "Pound Sterling"
msgstr ""

#: currency.php:173
msgid "Euro"
msgstr ""

#: currency.php:183
msgid "Australian Dollar"
msgstr ""

#: currency.php:193
msgid "Brazilian Real"
msgstr ""

#: currency.php:203
msgid "Canadian Dollar"
msgstr ""

#: currency.php:213
msgid "Czech Koruna"
msgstr ""

#: currency.php:223
msgid "Danish Krone"
msgstr ""

#: currency.php:233
msgid "Hong Kong Dollar"
msgstr ""

#: currency.php:243
msgid "Hungarian Forint"
msgstr ""

#: currency.php:253
msgid "Israeli New Sheqel"
msgstr ""

#: currency.php:263
msgid "Japanese Yen"
msgstr ""

#: currency.php:273
msgid "Malaysian Ringgit"
msgstr ""

#: currency.php:283
msgid "Mexican Peso"
msgstr ""

#: currency.php:293
msgid "Norwegian Krone"
msgstr ""

#: currency.php:303
msgid "New Zealand Dollar"
msgstr ""

#: currency.php:313
msgid "Philippine Peso"
msgstr ""

#: currency.php:323
msgid "Polish Zloty"
msgstr ""

#: currency.php:333
msgid "Russian Ruble"
msgstr ""

#: currency.php:343
msgid "Singapore Dollar"
msgstr ""

#: currency.php:353
msgid "South African Rand"
msgstr ""

#: currency.php:363
msgid "Swedish Krona"
msgstr ""

#: currency.php:373
msgid "Swiss Franc"
msgstr ""

#: currency.php:384
msgid "Taiwan New Dollar"
msgstr ""

#: currency.php:394
msgid "Thai Baht"
msgstr ""

#: currency.php:448
#: includes/setup-wizard/config/class-gf-setup-wizard-config-i18n.php:84
#: includes/wizard/steps/class-gf-installation-wizard-step-settings.php:28
msgid "Select a Currency"
msgstr ""

#: entry_detail.php:44
#: entry_detail.php:633
msgid "Entry"
msgstr ""

#: entry_detail.php:52
#: form_settings.php:1125
#: notification.php:111
#: notification.php:231
#: notification.php:849
msgid "Notifications"
msgstr ""

#: entry_detail.php:60
#: entry_detail.php:844
msgid "Notes"
msgstr ""

#: entry_detail.php:68
#: entry_detail.php:1126
msgid "Subscription Details"
msgstr ""

#: entry_detail.php:68
#: entry_detail.php:1126
#: includes/template-library/templates/templates.php:6934
msgid "Payment Details"
msgstr ""

#: entry_detail.php:75
msgid "Print entry"
msgstr ""

#: entry_detail.php:245
msgid "Oops! We couldn't find your entry. Please try again"
msgstr ""

#: entry_detail.php:324
msgid "%s: Unchecked \"%s\""
msgstr ""

#: entry_detail.php:324
msgid "%s: Checked \"%s\""
msgstr ""

#: entry_detail.php:389
msgid "You don't have adequate permission to delete notes."
msgstr ""

#: entry_detail.php:398
msgid "You don't have adequate permission to trash entries."
msgstr ""

#: entry_detail.php:412
msgid "You don't have adequate permission to restore entries."
msgstr ""

#: entry_detail.php:436
#: entry_list.php:1447
#: entry_list.php:1486
#: form_list.php:883
msgid "You don't have adequate permission to delete entries."
msgstr ""

#: entry_detail.php:471
msgid "Would you like to delete this file? 'Cancel' to stop. 'OK' to delete"
msgstr ""

#: entry_detail.php:482
msgid "Ajax error while deleting field."
msgstr ""

#: entry_detail.php:548
#: entry_list.php:1791
msgid "You must select at least one type of notification to resend."
msgstr ""

#: entry_detail.php:566
msgid "Notifications were resent successfully."
msgstr ""

#: entry_detail.php:610
msgid "Entry Updated."
msgstr ""

#: entry_detail.php:735
msgid "Details"
msgstr ""

#: entry_detail.php:820
msgid " Bulk action"
msgstr ""

#: entry_detail.php:822
msgid " Bulk action "
msgstr ""

#: entry_detail.php:823
#: form_detail.php:1664
#: includes/addon/class-gf-feed-addon.php:1743
#: includes/addon/class-gf-feed-addon.php:1790
#: includes/class-confirmation.php:1099
#: includes/editor-button/config/class-gf-editor-config.php:56
#: includes/fields/class-gf-field.php:1621
#: includes/settings/config/class-gf-settings-config-i18n.php:51
#: notification.php:1536
#: assets/js/src/legacy/admin/settings/field-map/mapping.js:371
msgid "Delete"
msgstr ""

#: entry_detail.php:826
#: entry_list.php:222
#: includes/settings/config/class-gf-settings-config-i18n.php:35
msgid "Apply"
msgstr ""

#: entry_detail.php:896
msgid "added"
msgstr ""

#: entry_detail.php:913
msgid "Add Note"
msgstr ""

#: entry_detail.php:927
msgid "Also email this note to"
msgstr ""

#: entry_detail.php:935
msgid "Subject:"
msgstr ""

#: entry_detail.php:965
#: print-entry.php:199
msgid "Entry # "
msgstr ""

#: entry_detail.php:983
msgid "show empty fields"
msgstr ""

#: entry_detail.php:1152
#: form_list.php:534
msgid "Status"
msgstr ""

#: entry_detail.php:1168
msgid "Start Date"
msgstr ""

#: entry_detail.php:1185
msgid "Subscription Id"
msgstr ""

#: entry_detail.php:1185
#: export.php:1124
#: forms_model.php:6565
#: select_columns.php:201
msgid "Transaction Id"
msgstr ""

#: entry_detail.php:1203
#: includes/addon/class-gf-payment-addon.php:2449
#: includes/addon/class-gf-payment-addon.php:2453
msgid "Recurring Amount"
msgstr ""

#: entry_detail.php:1203
#: includes/addon/class-gf-payment-addon.php:2340
msgid "Amount"
msgstr ""

#: entry_detail.php:1265
msgid "Include Notes"
msgstr ""

#: entry_detail.php:1269
#: entry_list.php:1365
#: entry_list.php:2207
msgid "Print"
msgstr ""

#: entry_detail.php:1309
msgid "Submitted on"
msgstr ""

#: entry_detail.php:1313
msgid "Updated"
msgstr ""

#: entry_detail.php:1318
#: export.php:1130
#: forms_model.php:6553
#: includes/addon/class-gf-addon.php:3027
#: includes/settings/fields/class-generic-map.php:579
#: select_columns.php:198
msgid "User IP"
msgstr ""

#: entry_detail.php:1330
msgid "Embed Url"
msgstr ""

#: entry_detail.php:1337
msgid "Edit Post"
msgstr ""

#: entry_detail.php:1360
#: entry_list.php:1236
#: entry_list.php:1350
msgid "Not Spam"
msgstr ""

#: entry_detail.php:1366
#: entry_detail.php:1377
msgid "You are about to delete this entry. 'Cancel' to stop, 'OK' to delete."
msgstr ""

#: entry_detail.php:1366
#: entry_detail.php:1377
#: entry_list.php:1213
#: entry_list.php:1245
#: entry_list.php:1345
#: entry_list.php:1353
msgid "Delete Permanently"
msgstr ""

#: entry_detail.php:1375
#: entry_list.php:1207
#: entry_list.php:1344
#: form_list.php:515
#: form_list.php:673
msgid "Restore"
msgstr ""

#: entry_detail.php:1386
msgid "Move to Trash"
msgstr ""

#: entry_detail.php:1392
msgid "Mark as Spam"
msgstr ""

#: entry_detail.php:1402
#: gravityforms.php:5336
#: gravityforms.php:5614
#: includes/addon/class-gf-feed-addon.php:1788
#: includes/class-confirmation.php:1097
#: includes/webapi/includes/class-gf-api-keys-table.php:68
#: notification.php:1534
msgid "Edit"
msgstr ""

#: entry_detail.php:1416
#: form_detail.php:1651
#: form_detail.php:1663
#: includes/addon/class-gf-results.php:305
#: includes/config/items/class-gf-config-multifile.php:34
#: includes/embed-form/config/class-gf-embed-config-i18n.php:54
#: includes/form-editor/save-form/config/class-gf-form-editor-form-save-config.php:42
#: includes/locking/class-gf-locking.php:201
#: includes/template-library/config/class-gf-template-library-config.php:105
#: select_columns.php:277
#: assets/js/src/admin/block-editor/blocks/form/edit.js:132
msgid "Cancel"
msgstr ""

#: entry_detail.php:1444
msgid "You cannot resend notifications for this entry because this form does not currently have any notifications configured."
msgstr ""

#: entry_detail.php:1446
#: entry_list.php:2144
msgid "Configure Notifications"
msgstr ""

#: entry_detail.php:1462
#: entry_list.php:2164
#: notification.php:206
msgid "Send To"
msgstr ""

#: entry_detail.php:1468
msgid "Resend"
msgstr ""

#: entry_detail.php:1470
#: entry_list.php:2171
msgid "Resending..."
msgstr ""

#: entry_list.php:47
#: entry_list.php:1504
msgid "%s restored from the Trash."
msgstr ""

#: entry_list.php:48
#: entry_list.php:62
#: entry_list.php:1476
msgid "1 entry"
msgstr ""

#: entry_list.php:61
msgid "%s permanently deleted."
msgstr ""

#: entry_list.php:79
msgid "1 entry moved to the Trash. %sUndo%s"
msgstr ""

#: entry_list.php:91
msgid "You don't have any active forms. Let's go %screate one%s"
msgstr ""

#: entry_list.php:216
msgid "Default Filter"
msgstr ""

#: entry_list.php:217
msgid "Pagination"
msgstr ""

#: entry_list.php:218
msgid "Number of entries per page:"
msgstr ""

#: entry_list.php:219
msgid "Display Mode"
msgstr ""

#: entry_list.php:261
#: form_detail.php:1842
msgid "Standard"
msgstr ""

#: entry_list.php:265
msgid "Full Width"
msgstr ""

#: entry_list.php:337
msgid "Search"
msgstr ""

#: entry_list.php:471
msgctxt "Entry List"
msgid "All"
msgstr ""

#: entry_list.php:479
msgctxt "Entry List"
msgid "Unread"
msgstr ""

#: entry_list.php:487
msgctxt "Entry List"
msgid "Starred"
msgstr ""

#: entry_list.php:495
#: entry_list.php:1276
#: entry_list.php:1368
msgid "Spam"
msgstr ""

#: entry_list.php:502
#: entry_list.php:1285
#: entry_list.php:1371
#: form_list.php:708
msgid "Trash"
msgstr ""

#: entry_list.php:886
msgid "Select Entry Table Columns"
msgstr ""

#: entry_list.php:886
msgid "click to select columns to display"
msgstr ""

#: entry_list.php:1027
msgid "View this entry"
msgstr ""

#: entry_list.php:1147
msgid "This form does not have any unread entries matching the search criteria."
msgstr ""

#: entry_list.php:1147
msgid "This form does not have any unread entries."
msgstr ""

#: entry_list.php:1151
msgid "This form does not have any starred entries matching the search criteria."
msgstr ""

#: entry_list.php:1151
msgid "This form does not have any starred entries."
msgstr ""

#: entry_list.php:1155
msgid "This form does not have any spam."
msgstr ""

#: entry_list.php:1159
msgid "This form does not have any entries in the trash matching the search criteria."
msgstr ""

#: entry_list.php:1159
msgid "This form does not have any entries in the trash."
msgstr ""

#: entry_list.php:1163
msgid "This form does not have any entries matching the search criteria."
msgstr ""

#: entry_list.php:1163
msgid "This form does not have any entries yet."
msgstr ""

#: entry_list.php:1199
#: entry_list.php:1230
#: entry_list.php:1263
#: includes/addon/class-gf-payment-addon.php:3300
#: includes/addon/class-gf-payment-addon.php:3301
msgid "View"
msgstr ""

#: entry_list.php:1236
msgid "Mark this entry as not spam"
msgstr ""

#: entry_list.php:1268
msgid "Mark read"
msgstr ""

#: entry_list.php:1268
msgid "Mark this entry as unread"
msgstr ""

#: entry_list.php:1268
msgid "Mark unread"
msgstr ""

#: entry_list.php:1276
msgid "Mark this entry as spam"
msgstr ""

#: entry_list.php:1285
msgid "Move this entry to the trash"
msgstr ""

#: entry_list.php:1359
msgid "Mark as Read"
msgstr ""

#: entry_list.php:1360
msgid "Mark as Unread"
msgstr ""

#: entry_list.php:1361
msgid "Add Star"
msgstr ""

#: entry_list.php:1362
msgid "Remove Star"
msgstr ""

#: entry_list.php:1364
#: entry_list.php:1748
#: entry_list.php:2102
#: entry_list.php:2169
msgid "Resend Notifications"
msgstr ""

#: entry_list.php:1404
msgid "WARNING! This operation cannot be undone. Empty trash? 'Ok' to empty trash. 'Cancel' to abort."
msgstr ""

#: entry_list.php:1404
msgid "WARNING! This operation cannot be undone. Permanently delete all spam? 'Ok' to delete. 'Cancel' to abort."
msgstr ""

#: entry_list.php:1405
msgid "Empty Trash"
msgstr ""

#: entry_list.php:1405
msgid "Delete All Spam"
msgstr ""

#: entry_list.php:1445
msgid "Entry deleted."
msgstr ""

#: entry_list.php:1476
msgid "%d entries"
msgstr ""

#: entry_list.php:1484
msgid "%s deleted."
msgstr ""

#: entry_list.php:1494
msgid "%s moved to Trash."
msgstr ""

#: entry_list.php:1496
msgid "You don't have adequate permissions to trash entries."
msgstr ""

#: entry_list.php:1506
msgid "You don't have adequate permissions to restore entries."
msgstr ""

#: entry_list.php:1513
msgid "%s restored from the spam."
msgstr ""

#: entry_list.php:1518
msgid "%s marked as spam."
msgstr ""

#: entry_list.php:1523
msgid "%s marked as read."
msgstr ""

#: entry_list.php:1528
msgid "%s marked as unread."
msgstr ""

#: entry_list.php:1533
msgid "%s starred."
msgstr ""

#: entry_list.php:1538
msgid "%s unstarred."
msgstr ""

#: entry_list.php:1683
msgid "Ajax error while setting entry property"
msgstr ""

#: entry_list.php:1740
msgid "Please select at least one entry."
msgstr ""

#: entry_list.php:1754
#: entry_list.php:2108
msgid "Print Entries"
msgstr ""

#: entry_list.php:1816
msgid "Notifications for %s were resent successfully."
msgstr ""

#: entry_list.php:1818
msgid "entry"
msgstr ""

#: entry_list.php:1818
msgid "entries"
msgstr ""

#: entry_list.php:1938
msgid "All %s{0}%s entries on this page are selected."
msgstr ""

#: entry_list.php:1939
msgid "Select all %s{0}%s entries."
msgstr ""

#: entry_list.php:1940
msgid "All %s{0}%s entries have been selected."
msgstr ""

#: entry_list.php:1941
msgid "Clear selection"
msgstr ""

#: entry_list.php:2029
msgid "Entry List"
msgstr ""

#: entry_list.php:2094
msgid "Please select at least one entry..."
msgstr ""

#: entry_list.php:2142
msgid "You cannot resend notifications for these entries because this form does not currently have any notifications configured."
msgstr ""

#: entry_list.php:2148
msgid "Specify which notifications you would like to resend for the selected entries."
msgstr ""

#: entry_list.php:2162
msgid "You may override the default notification settings by entering a comma delimited list of emails to which the selected notifications should be sent."
msgstr ""

#: entry_list.php:2180
msgid "Close Window"
msgstr ""

#: entry_list.php:2195
msgid "Print all of the selected entries at once."
msgstr ""

#: entry_list.php:2199
msgid "Include notes"
msgstr ""

#: entry_list.php:2204
msgid "Add page break between entries"
msgstr ""

#: export.php:19
msgid "Please select the forms to be exported"
msgstr ""

#: export.php:334
msgid "Forms could not be imported. Please make sure your files have the .json extension, and that they were generated by the %sGravity Forms Export form%s tool."
msgstr ""

#: export.php:340
msgid "Forms could not be imported. Your export file is not compatible with your current version of Gravity Forms."
msgstr ""

#: export.php:342
msgid "forms"
msgstr ""

#: export.php:342
msgid "form"
msgstr ""

#: export.php:343
#: includes/config/items/class-gf-config-admin-i18n.php:43
#: assets/js/src/admin/block-editor/blocks/form/edit.js:224
msgid "Edit Form"
msgstr ""

#: export.php:344
msgid "Gravity Forms imported %d %s successfully"
msgstr ""

#: export.php:354
#: export.php:1187
msgid "Import Forms"
msgstr ""

#: export.php:359
msgid "Select the Gravity Forms export files you would like to import. Please make sure your files have the .json extension, and that they were generated by the %sGravity Forms Export form%s tool. When you click the import button below, Gravity Forms will import the forms."
msgstr ""

#: export.php:369
#: tooltips.php:147
msgid "Select Files"
msgstr ""

#: export.php:375
msgid "Import"
msgstr ""

#: export.php:421
#: export.php:1179
msgid "Export Forms"
msgstr ""

#: export.php:424
msgid "Select the forms you would like to export. When you click the download button below, Gravity Forms will create a JSON file for you to save to your computer. Once you've saved the download file, you can use the Import tool to import the forms."
msgstr ""

#: export.php:429
msgid "Select Forms"
msgstr ""

#: export.php:435
#: export.php:558
#: includes/fields/class-gf-field-checkbox.php:181
#: includes/fields/class-gf-field-checkbox.php:719
msgid "Deselect All"
msgstr ""

#: export.php:465
#: export.php:718
msgid "Download Export File"
msgstr ""

#: export.php:543
msgid "Ajax error while selecting a form"
msgstr ""

#: export.php:567
msgid "Export entries if {0} of the following match:"
msgstr ""

#: export.php:576
msgid "Please select the fields to be exported"
msgstr ""

#: export.php:637
#: export.php:1170
msgid "Export Entries"
msgstr ""

#: export.php:640
msgid "Select a form below to export entries. Once you have selected a form you may select the fields you would like to export and then define optional filters for field values and the date range. When you click the download button below, Gravity Forms will create a CSV file for you to save to your computer."
msgstr ""

#: export.php:646
#: gravityforms.php:6026
#: widget.php:142
#: assets/js/src/admin/block-editor/blocks/form/edit.js:101
msgid "Select a Form"
msgstr ""

#: export.php:651
msgid "Select a form"
msgstr ""

#: export.php:676
msgid "Select Fields"
msgstr ""

#: export.php:695
msgid "Select Date Range"
msgstr ""

#: export.php:701
msgid "Start"
msgstr ""

#: export.php:706
msgid "End"
msgstr ""

#: export.php:710
msgid "Date Range is optional, if no date range is selected all entries will be exported."
msgstr ""

#: export.php:720
msgid "Exporting entries. Progress:"
msgstr ""

#: export.php:1119
msgid "Created By (User Id)"
msgstr ""

#: export.php:1122
msgid "Date Updated"
msgstr ""

#: export.php:1123
#: forms_model.php:6559
#: includes/addon/class-gf-addon.php:3028
#: includes/settings/fields/class-generic-map.php:583
#: select_columns.php:199
msgid "Source Url"
msgstr ""

#: export.php:1129
msgid "User Agent"
msgstr ""

#: export.php:1311
msgid "The PHP readfile function is not available, please contact the web host."
msgstr ""

#: forms_model.php:1274
#: includes/save-form/class-gf-form-crud-handler.php:401
msgid "Admin Notification"
msgstr ""

#: forms_model.php:1298
msgid "User Notification"
msgstr ""

#: forms_model.php:1479
msgid "Notification not found"
msgstr ""

#: forms_model.php:1511
msgid "Confirmation not found"
msgstr ""

#: forms_model.php:1751
#: forms_model.php:1778
#: forms_model.php:1830
#: forms_model.php:1863
#: forms_model.php:1899
#: forms_model.php:8204
#: includes/api.php:131
#: includes/api.php:158
#: includes/api.php:180
#: includes/api.php:212
#: includes/api.php:328
#: includes/api.php:389
#: includes/api.php:410
#: includes/api.php:443
#: includes/api.php:726
#: includes/api.php:760
#: includes/api.php:810
#: includes/api.php:1198
#: includes/api.php:1321
#: includes/api.php:1497
#: includes/api.php:1552
#: includes/api.php:1658
#: includes/api.php:2061
#: includes/api.php:2097
#: includes/api.php:2140
msgid "Submissions are currently blocked due to an upgrade in progress"
msgstr ""

#: forms_model.php:2824
msgid "WordPress successfully passed the notification email to the sending server."
msgstr ""

#: forms_model.php:2830
msgid "WordPress was unable to send the notification email."
msgstr ""

#. translators: Notification name followed by its ID. e.g. Admin Notification (ID: 5d4c0a2a37204).
#: forms_model.php:2853
msgid "%1$s (ID: %2$s)"
msgstr ""

#: forms_model.php:2917
#: includes/legacy/forms_model_legacy.php:514
msgid "You don't have adequate permission to edit entries."
msgstr ""

#: forms_model.php:2935
#: includes/legacy/forms_model_legacy.php:549
msgid "An error prevented the entry for this form submission being saved. Please contact support."
msgstr ""

#: forms_model.php:3105
msgid "Error while saving field values: %s"
msgstr ""

#: forms_model.php:3127
msgid "Error while saving calculation field values: %s"
msgstr ""

#: forms_model.php:3146
msgid "Error while saving total field values: %s"
msgstr ""

#: forms_model.php:6661
#: forms_model.php:6669
#: forms_model.php:6674
#: form_settings.php:276
msgid "(Required)"
msgstr ""

#: forms_model.php:6887
#: forms_model.php:6951
msgid "Default Confirmation"
msgstr ""

#: forms_model.php:6916
msgid "Save and Continue Confirmation"
msgstr ""

#: forms_model.php:6921
msgid "Link to continue editing later"
msgstr ""

#: forms_model.php:6922
msgid "Please use the following link to return and complete this form from any computer."
msgstr ""

#: forms_model.php:6923
msgid "Note: This link will expire after 30 days."
msgstr ""

#: forms_model.php:6924
msgid "Enter your email address if you would like to receive the link via email."
msgstr ""

#: forms_model.php:6935
msgid "Save and Continue Email Sent Confirmation"
msgstr ""

#: forms_model.php:6940
msgid "Success!"
msgstr ""

#: forms_model.php:6941
msgid "The link was sent to the following email address:"
msgstr ""

#: forms_model.php:8196
msgid "Updating the id property is not supported"
msgstr ""

#: forms_model.php:8200
msgid "%s is not a valid feed property"
msgstr ""

#: forms_model.php:8213
msgid "Feed meta should be an associative array or JSON"
msgstr ""

#: forms_model.php:8222
#: includes/api.php:2117
msgid "There was an error while updating feed id %s"
msgstr ""

#: forms_model.php:8226
#: includes/api.php:2078
msgid "Feed id %s not found"
msgstr ""

#: form_detail.php:148
#: form_detail.php:479
msgid "General"
msgstr ""

#: form_detail.php:151
#: form_detail.php:2051
msgid "Appearance"
msgstr ""

#: form_detail.php:154
#: form_detail.php:1308
#: form_detail.php:2270
#: assets/js/src/admin/block-editor/blocks/form/edit.js:610
msgid "Advanced"
msgstr ""

#: form_detail.php:181
msgid "Return to form list"
msgstr ""

#: form_detail.php:228
#: form_detail.php:239
#: includes/form-editor/save-form/config/class-gf-form-editor-form-save-config.php:40
msgid "Save Form"
msgstr ""

#: form_detail.php:242
#: includes/embed-form/config/class-gf-embed-config-i18n.php:53
#: includes/form-editor/save-form/config/class-gf-form-editor-form-save-config.php:39
msgid "Saving"
msgstr ""

#: form_detail.php:280
msgid "Browser Icon"
msgstr ""

#: form_detail.php:285
msgid "Hmmm, you seem to be using an unsupported browser. To get the most out of the Gravity Forms editing experience you’ll need to switch to a supported browser."
msgstr ""

#: form_detail.php:314
msgid "Pagination Options"
msgstr ""

#: form_detail.php:314
msgid "Manage pagination options"
msgstr ""

#: form_detail.php:315
msgid "Start Paging"
msgstr ""

#: form_detail.php:333
msgid "Simply drag and drop the fields or elements you want in this form."
msgstr ""

#: form_detail.php:336
msgid "Last page options"
msgstr ""

#: form_detail.php:336
msgid "Manage last page options"
msgstr ""

#: form_detail.php:337
msgid "End Paging"
msgstr ""

#: form_detail.php:346
msgid "You have successfully saved your form!"
msgstr ""

#: form_detail.php:348
msgid "What would you like to do next?"
msgstr ""

#: form_detail.php:351
msgid "Preview this Form"
msgstr ""

#: form_detail.php:356
msgid "Setup Email Notifications for this Form"
msgstr ""

#: form_detail.php:361
msgid "Continue Editing this Form"
msgstr ""

#: form_detail.php:365
msgid "Return to Form List"
msgstr ""

#: form_detail.php:392
msgid "Search a form field by name"
msgstr ""

#: form_detail.php:393
msgid "Search for a field"
msgstr ""

#: form_detail.php:397
msgid "Add Fields"
msgstr ""

#: form_detail.php:398
#: tooltips.php:165
msgid "Field Settings"
msgstr ""

#: form_detail.php:404
msgid "Custom settings"
msgstr ""

#: form_detail.php:417
msgid "Drag a field to the left to start building your form and then start configuring it."
msgstr ""

#: form_detail.php:432
msgid "No Matching Fields"
msgstr ""

#: form_detail.php:456
msgid "No field selected"
msgstr ""

#: form_detail.php:484
#: tooltips.php:113
msgid "Progress Indicator"
msgstr ""

#: form_detail.php:489
msgid "Progress Bar"
msgstr ""

#: form_detail.php:491
msgid "Steps"
msgstr ""

#: form_detail.php:493
#: form_detail.php:1763
msgid "None"
msgstr ""

#: form_detail.php:499
#: tooltips.php:114
msgid "Progress Bar Style"
msgstr ""

#: form_detail.php:503
msgid "Blue"
msgstr ""

#: form_detail.php:504
msgid "Gray"
msgstr ""

#: form_detail.php:505
#: form_detail.php:1600
msgid "Green"
msgstr ""

#: form_detail.php:506
msgid "Orange"
msgstr ""

#: form_detail.php:507
msgid "Red"
msgstr ""

#: form_detail.php:508
msgid "Gradient: Spring"
msgstr ""

#: form_detail.php:509
msgid "Gradient: Blues"
msgstr ""

#: form_detail.php:510
msgid "Gradient: Rainbow"
msgstr ""

#: form_detail.php:516
msgid "Text Color"
msgstr ""

#: form_detail.php:522
#: form_detail.php:934
msgid "Background Color"
msgstr ""

#: form_detail.php:529
#: tooltips.php:115
msgid "Page Names"
msgstr ""

#: form_detail.php:540
msgid "Display completed progress bar on confirmation"
msgstr ""

#: form_detail.php:547
msgid "Completion Text"
msgstr ""

#: form_detail.php:554
#: form_detail.php:805
msgid "Previous Button"
msgstr ""

#: form_detail.php:560
#: form_detail.php:779
#: form_detail.php:811
msgid "Default"
msgstr ""

#: form_detail.php:563
#: form_detail.php:609
#: form_detail.php:782
#: form_detail.php:814
msgid "Image"
msgstr ""

#: form_detail.php:567
msgid "Button Text:"
msgstr ""

#: form_detail.php:574
#: form_detail.php:793
#: form_detail.php:825
msgid "Image Path:"
msgstr ""

#: form_detail.php:594
#: tooltips.php:42
#: tooltips.php:43
msgid "Field Label"
msgstr ""

#: form_detail.php:602
msgid "Submit Input Type"
msgstr ""

#: form_detail.php:606
#: includes/class-confirmation.php:231
#: includes/class-confirmation.php:1187
#: includes/template-library/templates/templates.php:1593
#: includes/template-library/templates/templates.php:5972
#: includes/template-library/templates/templates.php:11212
#: js.php:975
#: assets/js/src/admin/block-editor/blocks/form/edit.js:453
#: assets/js/src/admin/block-editor/blocks/form/edit.js:515
#: assets/js/src/admin/block-editor/blocks/form/edit.js:548
#: assets/js/src/admin/block-editor/blocks/form/edit.js:584
msgid "Text"
msgstr ""

#: form_detail.php:614
msgid "Submit Button Text"
msgstr ""

#: form_detail.php:620
msgid "Submit Button Image URL"
msgstr ""

#: form_detail.php:630
msgid "Checkbox Label"
msgstr ""

#: form_detail.php:640
#: form_detail.php:1212
#: includes/fields/class-gf-field-post-image.php:169
#: includes/fields/class-gf-field-post-image.php:171
#: includes/fields/class-gf-field-post-image.php:226
#: includes/fields/class-gf-field-post-image.php:234
#: includes/setup-wizard/config/class-gf-setup-wizard-config-i18n.php:93
#: includes/system-status/class-gf-update.php:53
#: includes/template-library/templates/templates.php:1599
#: includes/template-library/templates/templates.php:5978
#: includes/template-library/templates/templates.php:11218
#: includes/webapi/includes/class-gf-api-keys-table.php:27
#: includes/webapi/webapi.php:395
#: js.php:975
msgid "Description"
msgstr ""

#: form_detail.php:650
msgid "Product Field Mapping"
msgstr ""

#: form_detail.php:662
#: form_detail.php:680
#: form_detail.php:716
#: form_detail.php:731
#: form_detail.php:746
#: form_detail.php:847
#: form_detail.php:878
#: form_detail.php:1090
msgid "Field Type"
msgstr ""

#: form_detail.php:667
msgid "Single Product"
msgstr ""

#: form_detail.php:668
#: form_detail.php:686
#: form_detail.php:721
#: form_detail.php:736
#: form_detail.php:752
#: form_detail.php:855
#: form_detail.php:884
#: form_detail.php:1094
#: includes/fields/class-gf-field-select.php:22
msgid "Drop Down"
msgstr ""

#: form_detail.php:669
#: form_detail.php:687
#: form_detail.php:723
#: form_detail.php:738
#: form_detail.php:859
#: form_detail.php:886
#: form_detail.php:1096
#: includes/fields/class-gf-field-radio.php:22
msgid "Radio Buttons"
msgstr ""

#: form_detail.php:670
#: form_detail.php:737
msgid "User Defined Price"
msgstr ""

#: form_detail.php:672
msgid "Calculation"
msgstr ""

#: form_detail.php:685
msgid "Single Method"
msgstr ""

#: form_detail.php:695
#: form_detail.php:1541
#: includes/fields/class-gf-field-calculation.php:107
#: includes/fields/class-gf-field-singleproduct.php:153
#: includes/fields/class-gf-field-singleproduct.php:163
#: includes/fields/class-gf-field-singleproduct.php:164
#: includes/fields/class-gf-field-singleproduct.php:172
#: includes/orders/summaries/class-gf-order-summary.php:65
#: includes/template-library/templates/templates.php:2350
#: includes/template-library/templates/templates.php:2411
#: includes/template-library/templates/templates.php:3305
#: includes/template-library/templates/templates.php:3368
#: includes/template-library/templates/templates.php:4168
#: includes/template-library/templates/templates.php:4231
#: js.php:888
#: js.php:948
msgid "Price"
msgstr ""

#: form_detail.php:706
msgid "Disable quantity field"
msgstr ""

#: form_detail.php:722
#: form_detail.php:858
#: form_detail.php:885
#: form_detail.php:1095
#: includes/fields/class-gf-field-checkbox.php:35
msgid "Checkboxes"
msgstr ""

#: form_detail.php:751
#: form_detail.php:857
#: includes/fields/class-gf-field-number.php:13
#: js.php:787
msgid "Number"
msgstr ""

#: form_detail.php:762
#: includes/class-confirmation.php:875
#: tooltips.php:133
msgid "Content"
msgstr ""

#: form_detail.php:786
#: form_detail.php:818
msgid "Text:"
msgstr ""

#: form_detail.php:838
msgid "Disable default margins"
msgstr ""

#: form_detail.php:852
#: form_detail.php:2712
#: tooltips.php:138
msgid "Standard Fields"
msgstr ""

#: form_detail.php:853
#: form_detail.php:883
msgid "Single line text"
msgstr ""

#: form_detail.php:854
#: includes/fields/class-gf-field-textarea.php:13
msgid "Paragraph Text"
msgstr ""

#: form_detail.php:856
#: form_detail.php:887
#: form_detail.php:1097
#: includes/fields/class-gf-field-multiselect.php:30
msgid "Multi Select"
msgstr ""

#: form_detail.php:862
#: form_detail.php:2730
#: tooltips.php:139
msgid "Advanced Fields"
msgstr ""

#: form_detail.php:864
#: includes/fields/class-gf-field-time.php:43
#: js.php:812
msgid "Time"
msgstr ""

#: form_detail.php:865
#: includes/fields/class-gf-field-phone.php:38
#: includes/template-library/templates/templates.php:737
#: includes/template-library/templates/templates.php:5750
#: includes/template-library/templates/templates.php:6427
#: js.php:796
msgid "Phone"
msgstr ""

#: form_detail.php:866
#: includes/fields/class-gf-field-website.php:13
#: includes/template-library/templates/templates.php:9443
#: js.php:817
msgid "Website"
msgstr ""

#: form_detail.php:867
#: includes/addon/class-gf-payment-addon.php:2728
#: includes/fields/class-gf-field-email.php:13
#: includes/template-library/templates/templates.php:157
#: includes/template-library/templates/templates.php:733
#: includes/template-library/templates/templates.php:1457
#: includes/template-library/templates/templates.php:1928
#: includes/template-library/templates/templates.php:2662
#: includes/template-library/templates/templates.php:3577
#: includes/template-library/templates/templates.php:4440
#: includes/template-library/templates/templates.php:6370
#: includes/template-library/templates/templates.php:7510
#: includes/template-library/templates/templates.php:8415
#: includes/template-library/templates/templates.php:8797
#: includes/template-library/templates/templates.php:9355
#: includes/template-library/templates/templates.php:10447
#: includes/template-library/templates/templates.php:10796
#: js.php:780
msgid "Email"
msgstr ""

#: form_detail.php:868
#: includes/fields/class-gf-field-fileupload.php:109
msgid "File Upload"
msgstr ""

#: form_detail.php:869
#: includes/fields/class-gf-field-list.php:36
#: js.php:659
msgid "List"
msgstr ""

#: form_detail.php:899
#: includes/class-confirmation.php:874
#: settings.php:1017
msgid "Type"
msgstr ""

#: form_detail.php:903
msgid "Really Simple CAPTCHA"
msgstr ""

#: form_detail.php:904
msgid "Math Challenge"
msgstr ""

#: form_detail.php:912
#: form_detail.php:1611
#: assets/js/src/admin/block-editor/blocks/form/edit.js:405
msgid "Size"
msgstr ""

#: form_detail.php:915
#: form_detail.php:1611
#: includes/fields/class-gf-field.php:2629
#: assets/js/src/admin/block-editor/blocks/form/edit.js:408
msgid "Small"
msgstr ""

#: form_detail.php:925
msgid "Font Color"
msgstr ""

#: form_detail.php:946
msgid "Theme"
msgstr ""

#: form_detail.php:950
msgid "Light"
msgstr ""

#: form_detail.php:951
msgid "Dark"
msgstr ""

#: form_detail.php:960
msgid "Badge Position"
msgstr ""

#: form_detail.php:964
msgid "Bottom Right"
msgstr ""

#: form_detail.php:965
msgid "Bottom Left"
msgstr ""

#: form_detail.php:966
msgid "Inline"
msgstr ""

#: form_detail.php:975
#: tooltips.php:48
msgid "Custom Field Name"
msgstr ""

#: form_detail.php:981
msgid "Existing"
msgstr ""

#: form_detail.php:985
msgid "New"
msgstr ""

#: form_detail.php:990
msgid "Select an existing custom field"
msgstr ""

#: form_detail.php:1007
#: tooltips.php:123
msgid "Post Status"
msgstr ""

#: form_detail.php:1012
msgid "Draft"
msgstr ""

#: form_detail.php:1013
msgid "Pending Review"
msgstr ""

#: form_detail.php:1014
msgid "Published"
msgstr ""

#: form_detail.php:1028
msgid "Default Post Author"
msgstr ""

#: form_detail.php:1038
msgid "Use logged in user as author"
msgstr ""

#: form_detail.php:1050
#: tooltips.php:125
msgid "Post Format"
msgstr ""

#: form_detail.php:1078
#: js.php:628
#: tooltips.php:122
#: tooltips.php:128
msgid "Post Category"
msgstr ""

#: form_detail.php:1106
#: includes/fields/class-gf-field-post-category.php:12
msgid "Category"
msgstr ""

#: form_detail.php:1111
msgid "All Categories"
msgstr ""

#: form_detail.php:1114
msgid "Select Categories"
msgstr ""

#: form_detail.php:1136
msgid "Display placeholder"
msgstr ""

#: form_detail.php:1142
msgid "Placeholder Label"
msgstr ""

#: form_detail.php:1150
#: form_detail.php:1168
msgid "Content Template"
msgstr ""

#: form_detail.php:1153
#: form_detail.php:1171
#: form_detail.php:1186
msgid "Create content template"
msgstr ""

#: form_detail.php:1201
msgid "Image Metadata"
msgstr ""

#: form_detail.php:1203
#: includes/fields/class-gf-field-post-image.php:145
#: includes/fields/class-gf-field-post-image.php:147
#: includes/fields/class-gf-field-post-image.php:223
#: includes/fields/class-gf-field-post-image.php:231
msgid "Alternative Text"
msgstr ""

#: form_detail.php:1206
#: form_list.php:535
#: gravityforms.php:2567
#: includes/fields/class-gf-field-post-image.php:153
#: includes/fields/class-gf-field-post-image.php:155
#: includes/fields/class-gf-field-post-image.php:224
#: includes/fields/class-gf-field-post-image.php:232
#: includes/fields/class-gf-field-post-title.php:13
#: widget.php:138
msgid "Title"
msgstr ""

#: form_detail.php:1209
#: includes/fields/class-gf-field-post-image.php:161
#: includes/fields/class-gf-field-post-image.php:163
#: includes/fields/class-gf-field-post-image.php:225
#: includes/fields/class-gf-field-post-image.php:233
msgid "Caption"
msgstr ""

#: form_detail.php:1220
msgid "Featured Image"
msgstr ""

#: form_detail.php:1222
#: tooltips.php:131
msgid "Set as Featured Image"
msgstr ""

#: form_detail.php:1234
#: tooltips.php:53
msgid "Address Type"
msgstr ""

#: form_detail.php:1249
#: tooltips.php:88
msgid "Address Fields"
msgstr ""

#: form_detail.php:1260
#: includes/addon/class-gf-payment-addon.php:2732
#: includes/fields/class-gf-field-address.php:182
#: includes/fields/class-gf-field-address.php:473
#: includes/settings/fields/class-field-select.php:150
#: includes/settings/fields/class-generic-map.php:347
msgid "State"
msgstr ""

#: form_detail.php:1264
#: includes/fields/class-gf-field-address.php:479
msgid "Postal Code"
msgstr ""

#: form_detail.php:1272
msgid "Default %s"
msgstr ""

#: form_detail.php:1284
#: tooltips.php:56
msgid "Default Country"
msgstr ""

#: form_detail.php:1303
msgid "Name Format"
msgstr ""

#: form_detail.php:1307
msgid "Extended"
msgstr ""

#: form_detail.php:1317
#: tooltips.php:86
msgid "Name Fields"
msgstr ""

#: form_detail.php:1329
#: tooltips.php:52
msgid "Date Input Type"
msgstr ""

#: form_detail.php:1333
msgid "Date Field"
msgstr ""

#: form_detail.php:1334
msgid "Date Picker"
msgstr ""

#: form_detail.php:1335
msgid "Date Drop Down"
msgstr ""

#: form_detail.php:1341
msgid "No Icon"
msgstr ""

#: form_detail.php:1344
msgid "Calendar Icon"
msgstr ""

#: form_detail.php:1347
msgid "Custom Icon"
msgstr ""

#: form_detail.php:1351
msgid "Image Path: "
msgstr ""

#: form_detail.php:1355
msgid "Preview this form to see your custom icon."
msgstr ""

#: form_detail.php:1364
msgid "Date Format"
msgstr ""

#: form_detail.php:1382
msgid "Date Format Placement"
msgstr ""

#: form_detail.php:1385
#: form_detail.php:2125
#: form_detail.php:2145
#: form_detail.php:2155
#: form_detail.php:2166
#: form_settings.php:201
#: form_settings.php:218
#: form_settings.php:234
msgid "Below inputs"
msgstr ""

#: form_detail.php:1386
#: form_detail.php:2125
#: form_detail.php:2146
#: form_detail.php:2155
#: form_detail.php:2167
#: form_settings.php:205
#: form_settings.php:222
#: form_settings.php:238
msgid "Above inputs"
msgstr ""

#: form_detail.php:1388
#: form_detail.php:2076
#: form_detail.php:2087
#: js.php:218
#: tooltips.php:91
msgid "Placeholder"
msgstr ""

#: form_detail.php:1396
msgid "Customize Fields"
msgstr ""

#: form_detail.php:1409
msgid "Allowed file extensions"
msgstr ""

#: form_detail.php:1415
msgid "Separated with commas (i.e. jpg, gif, png, pdf)"
msgstr ""

#: form_detail.php:1422
msgid "Multiple Files"
msgstr ""

#: form_detail.php:1426
#: tooltips.php:68
msgid "Enable Multi-File Upload"
msgstr ""

#: form_detail.php:1435
#: tooltips.php:69
msgid "Maximum Number of Files"
msgstr ""

#: form_detail.php:1449
#: tooltips.php:70
msgid "Maximum File Size"
msgstr ""

#: form_detail.php:1456
msgid "Maximum allowed on this server: %sMB"
msgstr ""

#: form_detail.php:1465
msgid "Columns"
msgstr ""

#: form_detail.php:1468
msgid "Enable multiple columns"
msgstr ""

#: form_detail.php:1481
#: tooltips.php:51
msgid "Maximum Rows"
msgstr ""

#: form_detail.php:1493
#: tooltips.php:66
msgid "Time Format"
msgstr ""

#: form_detail.php:1497
msgid "12 hour"
msgstr ""

#: form_detail.php:1498
msgid "24 hour"
msgstr ""

#: form_detail.php:1508
msgid "Phone Format"
msgstr ""

#: form_detail.php:1527
#: includes/form-editor/choices-ui/config/class-gf-choices-ui-config-i18n.php:33
msgid "Choices"
msgstr ""

#: form_detail.php:1534
msgid "Edit Choices"
msgstr ""

#: form_detail.php:1539
#: form_detail.php:2325
msgid "Label"
msgstr ""

#: form_detail.php:1540
#: form_detail.php:2325
#: includes/settings/fields/class-generic-map.php:184
msgid "Value"
msgstr ""

#: form_detail.php:1546
#: includes/addon/class-gf-payment-addon.php:2533
msgid "Options"
msgstr ""

#: form_detail.php:1558
msgid "Show Values"
msgstr ""

#: form_detail.php:1564
msgid "Bulk Add / Predefined Choices"
msgstr ""

#: form_detail.php:1565
msgid "Select a category and customize the predefined choices or paste your own list to bulk add choices."
msgstr ""

#: form_detail.php:1568
msgid "Add Bulk Choices"
msgstr ""

#. Translators: This string is a list of genders.  If the language you are translating into doesn't have equivalents, just provide a list with as many or few genders as your language has.
#: form_detail.php:1586
msgid "Male, Female, Non-binary, Agender, My gender is not listed, Prefer not to answer"
msgstr ""

#: form_detail.php:1591
msgid "Countries"
msgstr ""

#: form_detail.php:1592
msgid "U.S. States"
msgstr ""

#: form_detail.php:1593
msgid "Canadian Province/Territory"
msgstr ""

#: form_detail.php:1594
msgid "Continents"
msgstr ""

#: form_detail.php:1594
msgid "Africa"
msgstr ""

#: form_detail.php:1594
#: includes/fields/class-gf-field-address.php:626
msgid "Antarctica"
msgstr ""

#: form_detail.php:1594
msgid "Asia"
msgstr ""

#: form_detail.php:1594
#: includes/fields/class-gf-field-address.php:631
msgid "Australia"
msgstr ""

#: form_detail.php:1594
msgid "Europe"
msgstr ""

#: form_detail.php:1594
msgid "North America"
msgstr ""

#: form_detail.php:1594
msgid "South America"
msgstr ""

#: form_detail.php:1595
#: includes/template-library/templates/templates.php:6646
msgid "Gender"
msgstr ""

#: form_detail.php:1596
#: includes/template-library/templates/templates.php:6727
msgid "Age"
msgstr ""

#: form_detail.php:1596
msgid "Under 18"
msgstr ""

#: form_detail.php:1596
msgid "18-24"
msgstr ""

#: form_detail.php:1596
#: includes/template-library/templates/templates.php:6742
msgid "25-34"
msgstr ""

#: form_detail.php:1596
#: includes/template-library/templates/templates.php:6748
msgid "35-44"
msgstr ""

#: form_detail.php:1596
#: includes/template-library/templates/templates.php:6754
msgid "45-54"
msgstr ""

#: form_detail.php:1596
#: includes/template-library/templates/templates.php:6760
msgid "55-64"
msgstr ""

#: form_detail.php:1596
msgid "65 or Above"
msgstr ""

#: form_detail.php:1596
#: form_detail.php:1598
#: form_detail.php:1601
#: includes/template-library/templates/templates.php:6685
msgid "Prefer Not to Answer"
msgstr ""

#: form_detail.php:1597
msgid "Marital Status"
msgstr ""

#: form_detail.php:1597
msgid "Single"
msgstr ""

#: form_detail.php:1597
msgid "Married"
msgstr ""

#: form_detail.php:1597
msgid "Divorced"
msgstr ""

#: form_detail.php:1597
msgid "Widowed"
msgstr ""

#: form_detail.php:1597
msgid "Separated"
msgstr ""

#: form_detail.php:1597
msgid "Domestic Partnership"
msgstr ""

#: form_detail.php:1598
msgid "Employment"
msgstr ""

#: form_detail.php:1598
msgid "Employed Full-Time"
msgstr ""

#: form_detail.php:1598
msgid "Employed Part-Time"
msgstr ""

#: form_detail.php:1598
msgid "Self-employed"
msgstr ""

#: form_detail.php:1598
msgid "Not employed but looking for work"
msgstr ""

#: form_detail.php:1598
msgid "Not employed and not looking for work"
msgstr ""

#: form_detail.php:1598
msgid "Homemaker"
msgstr ""

#: form_detail.php:1598
msgid "Retired"
msgstr ""

#: form_detail.php:1598
msgid "Student"
msgstr ""

#: form_detail.php:1599
msgid "Job Type"
msgstr ""

#: form_detail.php:1599
msgid "Full-Time"
msgstr ""

#: form_detail.php:1599
msgid "Part-Time"
msgstr ""

#: form_detail.php:1599
msgid "Per Diem"
msgstr ""

#: form_detail.php:1599
msgid "Employee"
msgstr ""

#: form_detail.php:1599
msgid "Temporary"
msgstr ""

#: form_detail.php:1599
msgid "Contract"
msgstr ""

#: form_detail.php:1599
msgid "Intern"
msgstr ""

#: form_detail.php:1599
msgid "Seasonal"
msgstr ""

#: form_detail.php:1600
msgid "Industry"
msgstr ""

#: form_detail.php:1600
msgid "Accounting/Finance"
msgstr ""

#: form_detail.php:1600
msgid "Advertising/Public Relations"
msgstr ""

#: form_detail.php:1600
msgid "Aerospace/Aviation"
msgstr ""

#: form_detail.php:1600
msgid "Arts/Entertainment/Publishing"
msgstr ""

#: form_detail.php:1600
msgid "Automotive"
msgstr ""

#: form_detail.php:1600
msgid "Banking/Mortgage"
msgstr ""

#: form_detail.php:1600
msgid "Business Development"
msgstr ""

#: form_detail.php:1600
msgid "Business Opportunity"
msgstr ""

#: form_detail.php:1600
msgid "Clerical/Administrative"
msgstr ""

#: form_detail.php:1600
msgid "Construction/Facilities"
msgstr ""

#: form_detail.php:1600
msgid "Consumer Goods"
msgstr ""

#: form_detail.php:1600
msgid "Customer Service"
msgstr ""

#: form_detail.php:1600
msgid "Education/Training"
msgstr ""

#: form_detail.php:1600
msgid "Energy/Utilities"
msgstr ""

#: form_detail.php:1600
#: includes/template-library/templates/templates.php:5528
msgid "Engineering"
msgstr ""

#: form_detail.php:1600
msgid "Government/Military"
msgstr ""

#: form_detail.php:1600
msgid "Healthcare"
msgstr ""

#: form_detail.php:1600
msgid "Hospitality/Travel"
msgstr ""

#: form_detail.php:1600
msgid "Human Resources"
msgstr ""

#: form_detail.php:1600
msgid "Installation/Maintenance"
msgstr ""

#: form_detail.php:1600
msgid "Insurance"
msgstr ""

#: form_detail.php:1600
msgid "Internet"
msgstr ""

#: form_detail.php:1600
msgid "Job Search Aids"
msgstr ""

#: form_detail.php:1600
msgid "Law Enforcement/Security"
msgstr ""

#: form_detail.php:1600
msgid "Legal"
msgstr ""

#: form_detail.php:1600
msgid "Management/Executive"
msgstr ""

#: form_detail.php:1600
msgid "Manufacturing/Operations"
msgstr ""

#: form_detail.php:1600
#: includes/template-library/templates/templates.php:5522
#: includes/template-library/templates/templates.php:11020
msgid "Marketing"
msgstr ""

#: form_detail.php:1600
msgid "Non-Profit/Volunteer"
msgstr ""

#: form_detail.php:1600
msgid "Pharmaceutical/Biotech"
msgstr ""

#: form_detail.php:1600
msgid "Professional Services"
msgstr ""

#: form_detail.php:1600
msgid "QA/Quality Control"
msgstr ""

#: form_detail.php:1600
msgid "Real Estate"
msgstr ""

#: form_detail.php:1600
msgid "Restaurant/Food Service"
msgstr ""

#: form_detail.php:1600
#: includes/template-library/templates/templates.php:11026
msgid "Retail"
msgstr ""

#: form_detail.php:1600
#: includes/template-library/templates/templates.php:5516
msgid "Sales"
msgstr ""

#: form_detail.php:1600
msgid "Science/Research"
msgstr ""

#: form_detail.php:1600
msgid "Skilled Labor"
msgstr ""

#: form_detail.php:1600
#: includes/template-library/templates/templates.php:11038
msgid "Technology"
msgstr ""

#: form_detail.php:1600
msgid "Telecommunications"
msgstr ""

#: form_detail.php:1600
msgid "Transportation/Logistics"
msgstr ""

#: form_detail.php:1601
#: includes/setup-wizard/config/class-gf-setup-wizard-config.php:234
#: includes/template-library/templates/templates.php:10990
msgid "Education"
msgstr ""

#: form_detail.php:1601
msgid "High School"
msgstr ""

#: form_detail.php:1601
msgid "Associate Degree"
msgstr ""

#: form_detail.php:1601
msgid "Bachelor's Degree"
msgstr ""

#: form_detail.php:1601
msgid "Graduate or Professional Degree"
msgstr ""

#: form_detail.php:1601
msgid "Some College"
msgstr ""

#: form_detail.php:1602
msgid "Days of the Week"
msgstr ""

#: form_detail.php:1602
msgid "Sunday"
msgstr ""

#: form_detail.php:1602
#: includes/template-library/templates/templates.php:5612
msgid "Monday"
msgstr ""

#: form_detail.php:1602
#: includes/template-library/templates/templates.php:5618
msgid "Tuesday"
msgstr ""

#: form_detail.php:1602
#: includes/template-library/templates/templates.php:5624
msgid "Wednesday"
msgstr ""

#: form_detail.php:1602
#: includes/template-library/templates/templates.php:5630
msgid "Thursday"
msgstr ""

#: form_detail.php:1602
#: includes/template-library/templates/templates.php:5636
msgid "Friday"
msgstr ""

#: form_detail.php:1602
msgid "Saturday"
msgstr ""

#: form_detail.php:1603
msgid "Months of the Year"
msgstr ""

#: form_detail.php:1603
#: includes/config/items/class-gf-config-i18n.php:37
msgid "January"
msgstr ""

#: form_detail.php:1603
#: includes/config/items/class-gf-config-i18n.php:38
msgid "February"
msgstr ""

#: form_detail.php:1603
#: includes/config/items/class-gf-config-i18n.php:39
msgid "March"
msgstr ""

#: form_detail.php:1603
#: includes/config/items/class-gf-config-i18n.php:40
msgid "April"
msgstr ""

#: form_detail.php:1603
#: includes/addon/class-gf-payment-addon.php:3167
#: includes/config/items/class-gf-config-i18n.php:41
msgid "May"
msgstr ""

#: form_detail.php:1603
#: includes/config/items/class-gf-config-i18n.php:42
msgid "June"
msgstr ""

#: form_detail.php:1603
#: includes/config/items/class-gf-config-i18n.php:43
msgid "July"
msgstr ""

#: form_detail.php:1603
#: includes/config/items/class-gf-config-i18n.php:44
msgid "August"
msgstr ""

#: form_detail.php:1603
#: includes/config/items/class-gf-config-i18n.php:45
msgid "September"
msgstr ""

#: form_detail.php:1603
#: includes/config/items/class-gf-config-i18n.php:46
msgid "October"
msgstr ""

#: form_detail.php:1603
#: includes/config/items/class-gf-config-i18n.php:47
msgid "November"
msgstr ""

#: form_detail.php:1603
#: includes/config/items/class-gf-config-i18n.php:48
msgid "December"
msgstr ""

#: form_detail.php:1604
msgid "How Often"
msgstr ""

#: form_detail.php:1604
msgid "Every day"
msgstr ""

#: form_detail.php:1604
msgid "Once a week"
msgstr ""

#: form_detail.php:1604
msgid "2 to 3 times a week"
msgstr ""

#: form_detail.php:1604
msgid "Once a month"
msgstr ""

#: form_detail.php:1604
msgid "2 to 3 times a month"
msgstr ""

#: form_detail.php:1604
msgid "Less than once a month"
msgstr ""

#: form_detail.php:1605
msgid "How Long"
msgstr ""

#: form_detail.php:1605
msgid "Less than a month"
msgstr ""

#: form_detail.php:1605
msgid "1-6 months"
msgstr ""

#: form_detail.php:1605
msgid "1-3 years"
msgstr ""

#: form_detail.php:1605
msgid "Over 3 years"
msgstr ""

#: form_detail.php:1605
msgid "Never used"
msgstr ""

#: form_detail.php:1606
msgid "Satisfaction"
msgstr ""

#: form_detail.php:1606
msgid "Very Satisfied"
msgstr ""

#: form_detail.php:1606
msgid "Satisfied"
msgstr ""

#: form_detail.php:1606
#: includes/template-library/templates/templates.php:9511
#: includes/template-library/templates/templates.php:10045
msgid "Neutral"
msgstr ""

#: form_detail.php:1606
msgid "Unsatisfied"
msgstr ""

#: form_detail.php:1606
msgid "Very Unsatisfied"
msgstr ""

#: form_detail.php:1607
msgid "Importance"
msgstr ""

#: form_detail.php:1607
msgid "Very Important"
msgstr ""

#: form_detail.php:1607
msgid "Important"
msgstr ""

#: form_detail.php:1607
msgid "Somewhat Important"
msgstr ""

#: form_detail.php:1607
msgid "Not Important"
msgstr ""

#: form_detail.php:1608
msgid "Agreement"
msgstr ""

#: form_detail.php:1608
msgid "Strongly Agree"
msgstr ""

#: form_detail.php:1608
#: includes/template-library/templates/templates.php:10051
msgid "Agree"
msgstr ""

#: form_detail.php:1608
#: includes/template-library/templates/templates.php:10039
msgid "Disagree"
msgstr ""

#: form_detail.php:1608
msgid "Strongly Disagree"
msgstr ""

#: form_detail.php:1609
msgid "Comparison"
msgstr ""

#: form_detail.php:1609
msgid "Much Better"
msgstr ""

#: form_detail.php:1609
msgid "Somewhat Better"
msgstr ""

#: form_detail.php:1609
msgid "About the Same"
msgstr ""

#: form_detail.php:1609
msgid "Somewhat Worse"
msgstr ""

#: form_detail.php:1609
msgid "Much Worse"
msgstr ""

#: form_detail.php:1610
msgid "Would You"
msgstr ""

#: form_detail.php:1610
msgid "Definitely"
msgstr ""

#: form_detail.php:1610
msgid "Probably"
msgstr ""

#: form_detail.php:1610
msgid "Not Sure"
msgstr ""

#: form_detail.php:1610
msgid "Probably Not"
msgstr ""

#: form_detail.php:1610
msgid "Definitely Not"
msgstr ""

#: form_detail.php:1611
msgid "Extra Small"
msgstr ""

#: form_detail.php:1611
msgid "Extra Large"
msgstr ""

#: form_detail.php:1650
msgid "Insert Choices"
msgstr ""

#: form_detail.php:1655
msgid "Save as new custom choice"
msgstr ""

#: form_detail.php:1659
msgid "Save as"
msgstr ""

#: form_detail.php:1660
msgid "Enter name"
msgstr ""

#: form_detail.php:1660
msgid "enter name"
msgstr ""

#: form_detail.php:1692
msgid "Enable \"Select All\" choice"
msgstr ""

#: form_detail.php:1707
msgid "Enable \"other\" choice"
msgstr ""

#: form_detail.php:1718
msgid "Enable Email Confirmation"
msgstr ""

#: form_detail.php:1728
msgid "Password Fields"
msgstr ""

#: form_detail.php:1741
msgid "Enable Password Visibility Toggle"
msgstr ""

#: form_detail.php:1748
msgid "Enable Password Strength"
msgstr ""

#: form_detail.php:1759
msgid "Minimum Strength"
msgstr ""

#: form_detail.php:1764
msgid "Short"
msgstr ""

#: form_detail.php:1765
msgid "Bad"
msgstr ""

#: form_detail.php:1766
msgid "Good"
msgstr ""

#: form_detail.php:1767
#: form_display.php:3772
msgid "Strong"
msgstr ""

#: form_detail.php:1777
#: tooltips.php:63
msgid "Number Format"
msgstr ""

#: form_detail.php:1783
#: includes/system-status/class-gf-system-report.php:880
#: includes/wizard/steps/class-gf-installation-wizard-step-settings.php:20
#: tooltips.php:155
msgid "Currency"
msgstr ""

#: form_detail.php:1792
#: tooltips.php:81
msgid "Sub-Labels"
msgstr ""

#: form_detail.php:1807
msgid "Supported Credit Cards"
msgstr ""

#: form_detail.php:1833
#: tooltips.php:137
msgid "Input Mask"
msgstr ""

#: form_detail.php:1853
msgid "Enter a custom mask"
msgstr ""

#: form_detail.php:1854
msgid "Custom Mask Instructions"
msgstr ""

#: form_detail.php:1854
#: gravityforms.php:1830
#: includes/class-gf-osdxp.php:30
#: includes/class-gf-osdxp.php:283
#: includes/class-gf-osdxp.php:284
msgid "Help"
msgstr ""

#: form_detail.php:1860
msgid "Usage"
msgstr ""

#: form_detail.php:1862
msgid "Use a '9' to indicate a numerical character."
msgstr ""

#: form_detail.php:1863
msgid "Use a lower case 'a' to indicate an alphabetical character."
msgstr ""

#: form_detail.php:1864
msgid "Use an asterisk '*' to indicate any alphanumeric character."
msgstr ""

#: form_detail.php:1865
msgid "Use a question mark '?' to indicate optional characters. Note: All characters after the question mark will be optional."
msgstr ""

#: form_detail.php:1866
msgid "All other characters are literal values and will be displayed automatically."
msgstr ""

#: form_detail.php:1869
msgid "Examples"
msgstr ""

#: form_detail.php:1873
#: form_detail.php:1880
#: form_detail.php:1887
#: form_detail.php:1894
#: form_detail.php:1901
msgid "Mask"
msgstr ""

#: form_detail.php:1875
#: form_detail.php:1882
#: form_detail.php:1889
#: form_detail.php:1896
#: form_detail.php:1903
msgid "Valid Input"
msgstr ""

#: form_detail.php:1879
msgid "Social Security Number"
msgstr ""

#: form_detail.php:1886
msgid "Course Code"
msgstr ""

#: form_detail.php:1893
#: includes/wizard/steps/class-gf-installation-wizard-step-license-key.php:53
msgid "License Key"
msgstr ""

#: form_detail.php:1900
msgid "Zip Code w/ Optional Plus Four"
msgstr ""

#: form_detail.php:1912
msgid "Select a Mask"
msgstr ""

#: form_detail.php:1931
#: tooltips.php:50
msgid "Maximum Characters"
msgstr ""

#: form_detail.php:1943
msgid "Range"
msgstr ""

#: form_detail.php:1950
msgid "Min"
msgstr ""

#: form_detail.php:1956
msgid "Max"
msgstr ""

#: form_detail.php:1972
#: tooltips.php:77
msgid "Enable Calculation"
msgstr ""

#: form_detail.php:1980
#: tooltips.php:78
msgid "Formula"
msgstr ""

#: form_detail.php:1993
msgid "The formula appears to be valid."
msgstr ""

#: form_detail.php:1993
msgid "There appears to be a problem with the formula."
msgstr ""

#: form_detail.php:1993
msgid "Validate Formula"
msgstr ""

#: form_detail.php:1997
#: tooltips.php:79
msgid "Rounding"
msgstr ""

#: form_detail.php:2006
msgid "Do not round"
msgstr ""

#: form_detail.php:2021
msgid "Rules"
msgstr ""

#: form_detail.php:2027
#: includes/settings/class-settings.php:901
#: includes/template-library/config/class-gf-template-library-config.php:92
msgid "Required"
msgstr ""

#: form_detail.php:2036
#: tooltips.php:74
msgid "No Duplicates"
msgstr ""

#: form_detail.php:2057
#: form_settings.php:290
#: tooltips.php:100
msgid "CSS Class Name"
msgstr ""

#: form_detail.php:2080
#: form_detail.php:2091
msgid "Placeholder text is not supported when using the Rich Text Editor."
msgstr ""

#: form_detail.php:2099
#: tooltips.php:92
msgid "Placeholders"
msgstr ""

#: form_detail.php:2114
#: form_settings.php:175
msgid "Left aligned"
msgstr ""

#: form_detail.php:2117
#: form_settings.php:179
msgid "Right aligned"
msgstr ""

#: form_detail.php:2121
#: form_settings.php:171
msgid "Top aligned"
msgstr ""

#: form_detail.php:2129
msgid "Field Label Visibility"
msgstr ""

#: form_detail.php:2133
msgid "Visible (%s)"
msgstr ""

#: form_detail.php:2138
#: form_settings.php:187
#: tooltips.php:27
#: tooltips.php:83
msgid "Description Placement"
msgstr ""

#: form_detail.php:2144
#: form_detail.php:2165
msgid "Use Form Setting (%s)"
msgstr ""

#: form_detail.php:2159
#: form_settings.php:230
#: tooltips.php:29
#: tooltips.php:84
msgid "Sub-Label Placement"
msgstr ""

#: form_detail.php:2175
msgid "Custom Validation Message"
msgstr ""

#: form_detail.php:2187
msgid "Submit Button Width"
msgstr ""

#: form_detail.php:2191
msgid "Auto"
msgstr ""

#: form_detail.php:2194
msgid "Fill Container"
msgstr ""

#: form_detail.php:2211
msgid "Submit Button Location"
msgstr ""

#: form_detail.php:2215
msgid "End of the form"
msgstr ""

#: form_detail.php:2218
msgid "End of the last row"
msgstr ""

#: form_detail.php:2225
msgid "Custom CSS Class"
msgstr ""

#: form_detail.php:2238
msgid "Enable enhanced user interface"
msgstr ""

#: form_detail.php:2251
#: tooltips.php:85
msgid "Field Size"
msgstr ""

#: form_detail.php:2286
msgid "Admin Field Label"
msgstr ""

#: form_detail.php:2300
#: form_detail.php:2310
#: js.php:194
#: tooltips.php:89
msgid "Default Value"
msgstr ""

#: form_detail.php:2320
msgid "Prefix Choices"
msgstr ""

#: form_detail.php:2336
msgid "Enable Autocomplete"
msgstr ""

#: form_detail.php:2346
#: tooltips.php:90
msgid "Default Values"
msgstr ""

#: form_detail.php:2361
msgid "Display option to use the values submitted in different field"
msgstr ""

#: form_detail.php:2367
msgid "To activate this option, please add a field to be used as the source."
msgstr ""

#: form_detail.php:2373
#: tooltips.php:94
msgid "Option Label"
msgstr ""

#: form_detail.php:2378
#: tooltips.php:95
msgid "Source Field"
msgstr ""

#: form_detail.php:2388
msgid "Activated by default"
msgstr ""

#: form_detail.php:2402
msgid "Language"
msgstr ""

#: form_detail.php:2407
msgid "Arabic"
msgstr ""

#: form_detail.php:2408
msgid "Afrikaans"
msgstr ""

#: form_detail.php:2409
msgid "Amharic"
msgstr ""

#: form_detail.php:2410
msgid "Armenian"
msgstr ""

#: form_detail.php:2411
msgid "Azerbaijani"
msgstr ""

#: form_detail.php:2412
msgid "Basque"
msgstr ""

#: form_detail.php:2413
msgid "Bengali"
msgstr ""

#: form_detail.php:2414
msgid "Bulgarian"
msgstr ""

#: form_detail.php:2415
msgid "Catalan"
msgstr ""

#: form_detail.php:2416
msgid "Chinese (Hong Kong)"
msgstr ""

#: form_detail.php:2417
msgid "Chinese (Simplified)"
msgstr ""

#: form_detail.php:2418
msgid "Chinese (Traditional)"
msgstr ""

#: form_detail.php:2419
msgid "Croatian"
msgstr ""

#: form_detail.php:2420
msgid "Czech"
msgstr ""

#: form_detail.php:2421
msgid "Danish"
msgstr ""

#: form_detail.php:2422
msgid "Dutch"
msgstr ""

#: form_detail.php:2423
msgid "English (UK)"
msgstr ""

#: form_detail.php:2424
msgid "English (US)"
msgstr ""

#: form_detail.php:2425
msgid "Estonian"
msgstr ""

#: form_detail.php:2426
msgid "Filipino"
msgstr ""

#: form_detail.php:2427
msgid "Finnish"
msgstr ""

#: form_detail.php:2428
msgid "French"
msgstr ""

#: form_detail.php:2429
msgid "French (Canadian)"
msgstr ""

#: form_detail.php:2430
msgid "Galician"
msgstr ""

#: form_detail.php:2431
msgid "Georgian"
msgstr ""

#: form_detail.php:2432
msgid "German"
msgstr ""

#: form_detail.php:2433
msgid "German (Austria)"
msgstr ""

#: form_detail.php:2434
msgid "German (Switzerland)"
msgstr ""

#: form_detail.php:2435
msgid "Greek"
msgstr ""

#: form_detail.php:2436
msgid "Gujarati"
msgstr ""

#: form_detail.php:2437
msgid "Hebrew"
msgstr ""

#: form_detail.php:2438
msgid "Hindi"
msgstr ""

#: form_detail.php:2439
msgid "Hungarian"
msgstr ""

#: form_detail.php:2440
msgid "Icelandic"
msgstr ""

#: form_detail.php:2441
msgid "Indonesian"
msgstr ""

#: form_detail.php:2442
msgid "Italian"
msgstr ""

#: form_detail.php:2443
msgid "Japanese"
msgstr ""

#: form_detail.php:2444
msgid "Kannada"
msgstr ""

#: form_detail.php:2445
msgid "Korean"
msgstr ""

#: form_detail.php:2446
msgid "Laothian"
msgstr ""

#: form_detail.php:2447
msgid "Latvian"
msgstr ""

#: form_detail.php:2448
msgid "Lithuanian"
msgstr ""

#: form_detail.php:2449
msgid "Malay"
msgstr ""

#: form_detail.php:2450
msgid "Malayalam"
msgstr ""

#: form_detail.php:2451
msgid "Marathi"
msgstr ""

#: form_detail.php:2452
msgid "Mongolian"
msgstr ""

#: form_detail.php:2453
msgid "Norwegian"
msgstr ""

#: form_detail.php:2454
msgid "Persian"
msgstr ""

#: form_detail.php:2455
msgid "Polish"
msgstr ""

#: form_detail.php:2456
msgid "Portuguese"
msgstr ""

#: form_detail.php:2457
msgid "Portuguese (Brazil)"
msgstr ""

#: form_detail.php:2458
msgid "Portuguese (Portugal)"
msgstr ""

#: form_detail.php:2459
msgid "Romanian"
msgstr ""

#: form_detail.php:2460
msgid "Russian"
msgstr ""

#: form_detail.php:2461
msgid "Serbian"
msgstr ""

#: form_detail.php:2462
msgid "Sinhalese"
msgstr ""

#: form_detail.php:2463
msgid "Slovak"
msgstr ""

#: form_detail.php:2464
msgid "Slovenian"
msgstr ""

#: form_detail.php:2465
msgid "Spanish"
msgstr ""

#: form_detail.php:2466
msgid "Spanish (Latin America)"
msgstr ""

#: form_detail.php:2467
msgid "Swahili"
msgstr ""

#: form_detail.php:2468
msgid "Swedish"
msgstr ""

#: form_detail.php:2469
msgid "Tamil"
msgstr ""

#: form_detail.php:2470
msgid "Telugu"
msgstr ""

#: form_detail.php:2471
msgid "Thai"
msgstr ""

#: form_detail.php:2472
msgid "Turkish"
msgstr ""

#: form_detail.php:2473
msgid "Ukrainian"
msgstr ""

#: form_detail.php:2474
msgid "Urdu"
msgstr ""

#: form_detail.php:2475
msgid "Vietnamese"
msgstr ""

#: form_detail.php:2476
msgid "Zulu"
msgstr ""

#: form_detail.php:2485
#: tooltips.php:34
msgid "Add Icon URL"
msgstr ""

#: form_detail.php:2495
#: tooltips.php:35
msgid "Delete Icon URL"
msgstr ""

#: form_detail.php:2505
msgid "Enable Password Input"
msgstr ""

#: form_detail.php:2512
#: tooltips.php:64
msgid "Force SSL"
msgstr ""

#: form_detail.php:2535
msgid "Use the Rich Text Editor"
msgstr ""

#: form_detail.php:2542
msgid "Allow field to be populated dynamically"
msgstr ""

#: form_detail.php:2596
msgid "Enable Conditional Logic"
msgstr ""

#: form_detail.php:2608
msgid "Enable Page Conditional Logic"
msgstr ""

#: form_detail.php:2620
msgid "Enable Submit Button Conditional Logic"
msgstr ""

#: form_detail.php:2628
msgid "Enable Next Button Conditional Logic"
msgstr ""

#: form_detail.php:2747
#: tooltips.php:140
msgid "Post Fields"
msgstr ""

#: form_detail.php:2760
#: tooltips.php:141
msgid "Pricing Fields"
msgstr ""

#: form_detail.php:2860
#: includes/fields/class-gf-field.php:341
msgid "Add a %s field to your form."
msgstr ""

#. Translators: 1. Opening <a> tag with link to the form export page, 2. closing <a> tag, 3. Opening <a> tag for documentation link, 4. Closing <a> tag.
#: form_detail.php:3254
msgid "If you continue to encounter this error, you can %1$sexport your form%2$s to include in your support request. You can also disable AJAX saving for this form. %3$sLearn more%4$s."
msgstr ""

#: form_detail.php:3265
#: form_detail.php:3266
#: form_detail.php:3383
#: form_detail.php:3384
msgid "Dismiss notification"
msgstr ""

#: form_detail.php:3294
msgid "This form has legacy markup enabled, which may prevent some new features from functioning."
msgstr ""

#: form_detail.php:3299
#: form_settings.php:683
msgid "Learn more about form legacy markup"
msgstr ""

#: form_detail.php:3301
#: form_detail.php:3378
#: form_settings.php:685
msgid "Learn More"
msgstr ""

#: form_detail.php:3370
msgid "This form uses deprecated Ready Classes. Adding columns is easier than ever with the new Drag and Drop Layout Editor."
msgstr ""

#: form_detail.php:3376
#: js.php:429
msgid "Working with Columns in the Form Editor in Gravity Forms 2.5"
msgstr ""

#: form_display.php:330
msgid "Review Form"
msgstr ""

#: form_display.php:1132
msgid "Sorry. You must be logged in to view this form."
msgstr ""

#. Translators: the text or symbol that indicates a field is required
#: form_display.php:1265
msgid "\"%s\" indicates required fields"
msgstr ""

#: form_display.php:1294
msgid "Save and Continue link used is expired or invalid."
msgstr ""

#: form_display.php:1372
#: form_display.php:4169
msgid "Previous Page"
msgstr ""

#: form_display.php:1433
msgid "This iframe contains the logic required to handle Ajax powered Gravity Forms."
msgstr ""

#: form_display.php:2001
msgid "Spam Filter"
msgstr ""

#: form_display.php:2002
msgid "This entry has been flagged as spam."
msgstr ""

#. translators: Variable is a complete sentence containing the reason the entry was marked as spam.
#: form_display.php:2005
msgid "Reason: %s"
msgstr ""

#: form_display.php:2376
msgid "At least one field must be filled out"
msgstr ""

#: form_display.php:2445
msgid "This date has already been taken. Please select a new date."
msgstr ""

#: form_display.php:2449
msgid "This field requires a unique entry and the values you entered have already been used."
msgstr ""

#: form_display.php:2450
msgid "This field requires a unique entry and '%s' has already been used"
msgstr ""

#: form_display.php:2479
msgid "Please enter a valid value."
msgstr ""

#: form_display.php:2479
msgid "Invalid selection. Please select from the available choices."
msgstr ""

#: form_display.php:2607
msgid "The text entered contains invalid characters."
msgstr ""

#: form_display.php:3041
msgid "All choices are selected."
msgstr ""

#: form_display.php:3042
msgid "All choices are unselected."
msgstr ""

#: form_display.php:3701
msgid "No results matched"
msgstr ""

#: form_display.php:3755
#: form_display.php:4422
msgid "of"
msgstr ""

#: form_display.php:3755
msgid "max characters"
msgstr ""

#: form_display.php:3772
#: includes/fields/class-gf-field-password.php:193
msgid "Strength indicator"
msgstr ""

#: form_display.php:3772
msgid "Mismatch"
msgstr ""

#: form_display.php:3772
msgid "Password strength unknown"
msgstr ""

#: form_display.php:3772
msgid "Weak"
msgstr ""

#: form_display.php:3772
msgid "Very weak"
msgstr ""

#: form_display.php:4175
msgid "Next Page"
msgstr ""

#: form_display.php:4176
#: includes/setup-wizard/config/class-gf-setup-wizard-config-i18n.php:50
#: includes/template-library/templates/templates.php:2511
#: includes/template-library/templates/templates.php:2707
#: includes/template-library/templates/templates.php:3426
#: includes/template-library/templates/templates.php:3622
#: includes/template-library/templates/templates.php:4289
#: includes/template-library/templates/templates.php:4485
#: includes/template-library/templates/templates.php:5433
#: includes/template-library/templates/templates.php:5792
#: includes/template-library/templates/templates.php:6568
#: includes/template-library/templates/templates.php:6899
#: includes/template-library/templates/templates.php:7828
#: includes/wizard/steps/class-gf-installation-wizard-step.php:102
#: js.php:646
msgid "Next"
msgstr ""

#: form_display.php:4422
msgid "Step"
msgstr ""

#: form_display.php:4529
msgid "Sorry. This form is no longer accepting new submissions."
msgstr ""

#: form_display.php:4550
msgid "This form is not yet available."
msgstr ""

#: form_display.php:4556
msgid "Sorry. This form is no longer available."
msgstr ""

#: form_display.php:4714
msgid "Send Link"
msgstr ""

#: form_display.php:4715
#: notification.php:293
msgid "Please enter a valid email address."
msgstr ""

#: form_display.php:4716
msgid "Email Address"
msgstr ""

#: form_display.php:5080
msgid "Oops! We could not locate your form."
msgstr ""

#: form_display.php:5111
msgid "Your form was not submitted. Please try again in a few minutes."
msgstr ""

#: form_display.php:5113
msgid "There was a problem with your submission."
msgstr ""

#: form_display.php:5113
msgid "Please review the fields below."
msgstr ""

#: form_list.php:53
#: form_settings.php:154
#: includes/template-library/config/class-gf-template-library-config.php:89
#: tooltips.php:25
#: assets/js/src/admin/block-editor/blocks/form/edit.js:306
msgid "Form Description"
msgstr ""

#: form_list.php:64
#: includes/template-library/config/class-gf-template-library-config.php:128
msgid "Create Form"
msgstr ""

#: form_list.php:93
msgid "WARNING: You are about to delete this form and ALL entries associated with it. "
msgstr ""

#: form_list.php:93
msgid "Cancel to stop, OK to delete."
msgstr ""

#: form_list.php:140
#: includes/class-confirmation.php:146
#: notification.php:900
msgid "Ajax error while updating form"
msgstr ""

#: form_list.php:166
msgid "WARNING: You are about to delete these forms and ALL entries associated with them. "
msgstr ""

#: form_list.php:166
#: form_list.php:170
#: includes/addon/class-gf-feed-addon.php:1790
#: notification.php:1536
msgid "'Cancel' to stop, 'OK' to delete."
msgstr ""

#: form_list.php:168
msgid "Are you sure you would like to reset the Views for the selected forms? "
msgstr ""

#: form_list.php:168
msgid "'Cancel' to stop, 'OK' to reset."
msgstr ""

#: form_list.php:170
msgid "WARNING: You are about to delete ALL entries associated with the selected forms. "
msgstr ""

#: form_list.php:183
#: gravityforms.php:1779
#: gravityforms.php:1784
#: gravityforms.php:5576
#: includes/class-gf-osdxp.php:236
#: includes/class-gf-osdxp.php:237
#: includes/class-personal-data.php:893
msgid "Forms"
msgstr ""

#: form_list.php:185
#: includes/addon/class-gf-feed-addon.php:1348
#: includes/addon/class-gf-feed-addon.php:2609
#: includes/class-confirmation.php:1216
#: notification.php:1641
msgid "Add New"
msgstr ""

#: form_list.php:200
msgid "Search Forms"
msgstr ""

#: form_list.php:215
#: form_list.php:235
#: includes/template-library/config/class-gf-template-library-config.php:100
msgid "There was an issue creating your form."
msgstr ""

#: form_list.php:227
#: form_settings.php:1415
msgid "Please enter a form title."
msgstr ""

#: form_list.php:239
#: form_settings.php:1419
#: includes/template-library/config/class-gf-template-library-config.php:99
msgid "Please enter a unique form title."
msgstr ""

#: form_list.php:300
msgid "Create a New Form"
msgstr ""

#: form_list.php:300
msgid "Provide a title and a description for this form"
msgstr ""

#: form_list.php:316
msgid "Creating Form..."
msgstr ""

#: form_list.php:351
msgid "Saved! Redirecting..."
msgstr ""

#: form_list.php:436
msgctxt "Form List"
msgid "All"
msgstr ""

#: form_list.php:437
msgctxt "Form List"
msgid "Active"
msgstr ""

#: form_list.php:438
msgctxt "Form List"
msgid "Inactive"
msgstr ""

#: form_list.php:439
msgctxt "Form List"
msgid "Trash"
msgstr ""

#: form_list.php:516
#: form_list.php:681
msgid "Delete permanently"
msgstr ""

#: form_list.php:520
msgid "Mark as Active"
msgstr ""

#: form_list.php:521
msgid "Mark as Inactive"
msgstr ""

#: form_list.php:522
msgid "Reset Views"
msgstr ""

#: form_list.php:523
msgid "Permanently Delete Entries"
msgstr ""

#: form_list.php:524
msgid "Move to trash"
msgstr ""

#: form_list.php:536
msgid "ID"
msgstr ""

#: form_list.php:537
#: gravityforms.php:1794
#: gravityforms.php:5366
#: gravityforms.php:5625
msgid "Entries"
msgstr ""

#: form_list.php:538
msgid "Views"
msgstr ""

#: form_list.php:539
msgid "Conversion"
msgstr ""

#: form_list.php:699
#: includes/addon/class-gf-feed-addon.php:1789
#: includes/class-confirmation.php:1098
#: includes/editor-button/config/class-gf-editor-config.php:55
#: includes/fields/class-gf-field.php:1600
#: notification.php:1535
msgid "Duplicate"
msgstr ""

#: form_list.php:709
msgid "Move this form to the trash"
msgstr ""

#: form_list.php:734
msgid "No forms were found for your search query. %sView all forms%s."
msgstr ""

#: form_list.php:739
msgid "There are no forms in the trash."
msgstr ""

#: form_list.php:741
msgid "You don't have any forms. Let's go %screate one%s!"
msgstr ""

#: form_list.php:765
#: form_list.php:814
msgid "Form moved to the trash."
msgstr ""

#: form_list.php:768
#: form_list.php:817
msgid "You don't have adequate permission to trash forms."
msgstr ""

#: form_list.php:775
msgid "Form restored."
msgstr ""

#: form_list.php:778
msgid "You don't have adequate permission to restore forms."
msgstr ""

#: form_list.php:785
msgid "Form deleted."
msgstr ""

#: form_list.php:788
msgid "You don't have adequate permission to delete forms."
msgstr ""

#: form_list.php:795
#: form_list.php:826
msgid "Form duplicated."
msgstr ""

#: form_list.php:798
#: form_list.php:829
msgid "You don't have adequate permission to duplicate forms."
msgstr ""

#: form_list.php:848
msgid "%s form moved to the trash."
msgid_plural "%s forms moved to the trash."
msgstr[0] ""
msgstr[1] ""

#: form_list.php:850
msgid "You don't have adequate permissions to trash forms."
msgstr ""

#: form_list.php:856
msgid "%s form restored."
msgid_plural "%s forms restored."
msgstr[0] ""
msgstr[1] ""

#: form_list.php:858
msgid "You don't have adequate permissions to restore forms."
msgstr ""

#: form_list.php:864
msgid "%s form deleted."
msgid_plural "%s forms deleted."
msgstr[0] ""
msgstr[1] ""

#: form_list.php:866
msgid "You don't have adequate permissions to delete forms."
msgstr ""

#: form_list.php:874
msgid "Views for %s form have been reset."
msgid_plural "Views for %s forms have been reset."
msgstr[0] ""
msgstr[1] ""

#: form_list.php:881
msgid "Entries for %s form have been deleted."
msgid_plural "Entries for %s forms have been deleted."
msgstr[0] ""
msgstr[1] ""

#: form_list.php:891
msgid "%s form has been marked as active."
msgid_plural "%s forms have been marked as active."
msgstr[0] ""
msgstr[1] ""

#: form_list.php:897
msgid "%s form has been marked as inactive."
msgid_plural "%s forms have been marked as inactive."
msgstr[0] ""
msgstr[1] ""

#: form_settings.php:44
#: form_settings.php:1118
#: includes/class-confirmation.php:96
#: includes/class-confirmation.php:210
msgid "Confirmations"
msgstr ""

#: form_settings.php:109
msgid "Form Basics"
msgstr ""

#: form_settings.php:142
msgid "The form title you have entered has already been used. Please enter a unique form title."
msgstr ""

#: form_settings.php:161
msgid "Form Layout"
msgstr ""

#: form_settings.php:166
msgid "Label Placement"
msgstr ""

#: form_settings.php:213
#: tooltips.php:28
msgid "Validation Message Placement"
msgstr ""

#: form_settings.php:246
#: tooltips.php:169
msgid "Validation Summary"
msgstr ""

#: form_settings.php:252
msgid "Required Field Indicator"
msgstr ""

#: form_settings.php:259
msgid "Text: (Required)"
msgstr ""

#: form_settings.php:263
msgid "Asterisk: *"
msgstr ""

#: form_settings.php:267
msgid "Custom:"
msgstr ""

#: form_settings.php:275
msgid "Custom Required Indicator"
msgstr ""

#: form_settings.php:296
msgid "Form Button"
msgstr ""

#: form_settings.php:301
msgid "Form button settings are now located in the form editor! To edit the button settings, go to the form editor and click on the submit button."
msgstr ""

#: form_settings.php:306
msgid "Save and Continue"
msgstr ""

#: form_settings.php:311
msgid "Enable Save and Continue"
msgstr ""

#: form_settings.php:316
msgid "Link Text"
msgstr ""

#: form_settings.php:317
msgid "Save & Continue"
msgstr ""

#: form_settings.php:335
msgid "Restrictions"
msgstr ""

#: form_settings.php:340
msgid "Limit number of entries"
msgstr ""

#: form_settings.php:345
msgid "Enable entry limit"
msgstr ""

#: form_settings.php:352
msgid "Number of Entries"
msgstr ""

#: form_settings.php:370
msgid "total entries"
msgstr ""

#: form_settings.php:374
msgid "per day"
msgstr ""

#: form_settings.php:378
msgid "per week"
msgstr ""

#: form_settings.php:382
msgid "per month"
msgstr ""

#: form_settings.php:386
msgid "per year"
msgstr ""

#: form_settings.php:396
msgid "Entry Limit Reached Message"
msgstr ""

#: form_settings.php:412
#: form_settings.php:417
#: tooltips.php:20
msgid "Schedule Form"
msgstr ""

#: form_settings.php:424
msgid "Schedule Start Date/Time"
msgstr ""

#: form_settings.php:437
msgid "Schedule Form End Date/Time"
msgstr ""

#: form_settings.php:450
msgid "Form Pending Message"
msgstr ""

#: form_settings.php:464
msgid "Form Expired Message"
msgstr ""

#: form_settings.php:481
#: form_settings.php:486
#: tooltips.php:110
msgid "Require user to be logged in"
msgstr ""

#: form_settings.php:493
#: tooltips.php:111
msgid "Require Login Message"
msgstr ""

#: form_settings.php:510
msgid "Form Options"
msgstr ""

#: form_settings.php:515
msgid "Anti-spam honeypot"
msgstr ""

#: form_settings.php:523
msgid "If the honeypot flags a submission as spam:"
msgstr ""

#: form_settings.php:534
msgid "Do not create an entry"
msgstr ""

#: form_settings.php:538
msgid "Create an entry and mark it as spam"
msgstr ""

#: form_settings.php:546
msgid "Animated transitions"
msgstr ""

#: form_settings.php:557
msgid "Enable legacy markup"
msgstr ""

#: form_settings.php:580
#: includes/class-confirmation.php:385
#: notification.php:546
msgid "Legacy Settings"
msgstr ""

#: form_settings.php:678
msgid "Legacy markup is incompatible with many new features, including the Orbital Theme."
msgstr ""

#: form_settings.php:965
#: form_settings.php:1132
#: includes/class-personal-data.php:176
#: includes/class-personal-data.php:578
msgid "Personal Data"
msgstr ""

#: form_settings.php:1111
#: includes/addon/class-gf-addon.php:1649
#: includes/class-gf-osdxp.php:245
#: includes/class-gf-osdxp.php:246
#: assets/js/src/admin/block-editor/blocks/form/edit.js:245
#: assets/js/src/admin/block-editor/blocks/form/edit.js:289
msgid "Form Settings"
msgstr ""

#: form_settings.php:1219
msgid "Confirmation deleted."
msgstr ""

#: form_settings.php:1322
msgid "Save and Continue Email"
msgstr ""

#: form_settings.php:1327
msgid "Thank you for saving {form_title}. Please use the unique link below to return to the form from any computer. <br /><br /> {save_link} <br /><br /> Remember that the link will expire after 30 days so please return via the provided link to complete your form submission."
msgstr ""

#: gravityforms.php:1578
msgid "Create Forms"
msgstr ""

#: gravityforms.php:1579
msgid "Delete Forms"
msgstr ""

#: gravityforms.php:1580
msgid "Edit Forms"
msgstr ""

#: gravityforms.php:1581
msgid "Preview Forms"
msgstr ""

#: gravityforms.php:1582
msgid "View Entries"
msgstr ""

#: gravityforms.php:1583
msgid "Edit Entries"
msgstr ""

#: gravityforms.php:1584
msgid "Delete Entries"
msgstr ""

#: gravityforms.php:1585
msgid "View Entry Notes"
msgstr ""

#: gravityforms.php:1586
msgid "Edit Entry Notes"
msgstr ""

#: gravityforms.php:1587
#: gravityforms.php:1813
#: includes/class-gf-osdxp.php:28
#: includes/class-gf-osdxp.php:254
#: includes/class-gf-osdxp.php:255
msgid "Import/Export"
msgstr ""

#: gravityforms.php:1588
msgid "View Plugin Settings"
msgstr ""

#: gravityforms.php:1589
msgid "Edit Plugin Settings"
msgstr ""

#: gravityforms.php:1590
msgid "Manage Updates"
msgstr ""

#: gravityforms.php:1591
msgid "Manage Add-Ons"
msgstr ""

#: gravityforms.php:1592
msgid "View System Status"
msgstr ""

#: gravityforms.php:1593
#: settings.php:166
#: settings.php:257
#: settings.php:271
msgid "Uninstall Gravity Forms"
msgstr ""

#: gravityforms.php:1594
msgid "Logging Settings"
msgstr ""

#: gravityforms.php:1595
msgid "REST API Settings"
msgstr ""

#: gravityforms.php:1667
msgid "Upload folder is not writable. Export and file upload features will not be functional."
msgstr ""

#: gravityforms.php:1775
msgid "Update Available"
msgstr ""

#: gravityforms.php:1784
msgid "Forms - Gravity Forms"
msgstr ""

#: gravityforms.php:1789
msgid "New Form - Gravity Forms"
msgstr ""

#: gravityforms.php:1789
#: gravityforms.php:5678
#: includes/template-library/config/class-gf-template-library-config.php:132
msgid "New Form"
msgstr ""

#: gravityforms.php:1794
msgid "Entries - Gravity Forms"
msgstr ""

#: gravityforms.php:1808
msgid "Settings - Gravity Forms"
msgstr ""

#: gravityforms.php:1808
#: gravityforms.php:2273
#: gravityforms.php:5350
#: gravityforms.php:5636
#: includes/addon/class-gf-addon.php:4521
#: includes/addon/class-gf-addon.php:4799
#: includes/addon/class-gf-addon.php:4972
#: includes/addon/class-gf-addon.php:5405
#: includes/class-gf-osdxp.php:27
#: includes/fields/class-gf-field.php:1645
#: includes/system-status/class-gf-update.php:75
#: settings.php:1258
msgid "Settings"
msgstr ""

#: gravityforms.php:1813
msgid "Import/Export - Gravity Forms"
msgstr ""

#: gravityforms.php:1819
msgid "Add-Ons - Gravity Forms"
msgstr ""

#: gravityforms.php:1819
#: includes/class-gf-osdxp.php:29
#: includes/class-gf-osdxp.php:264
#: includes/class-gf-osdxp.php:265
#: includes/system-status/class-gf-system-report.php:369
msgid "Add-Ons"
msgstr ""

#: gravityforms.php:1825
msgid "System Status - Gravity Forms"
msgstr ""

#: gravityforms.php:1825
#: includes/class-gf-osdxp.php:31
#: includes/class-gf-osdxp.php:274
#: includes/class-gf-osdxp.php:275
msgid "System Status"
msgstr ""

#: gravityforms.php:1830
msgid "Help - Gravity Forms"
msgstr ""

#: gravityforms.php:1953
msgid "%1$s &lsaquo; %2$s &#8212; WordPress"
msgstr ""

#: gravityforms.php:2207
msgid "Add Gravity Form"
msgstr ""

#: gravityforms.php:2207
msgid "Add Form"
msgstr ""

#: gravityforms.php:2222
msgid "Please select a form"
msgstr ""

#. translators: 1: The name of the add-on, 2: version number.
#: gravityforms.php:2308
msgid "This version of the %1$s is not compatible with the version of Gravity Forms that is installed. Upgrade this add-on to version %2$s or greater to avoid compatibility issues and potential loss of data."
msgstr ""

#. translators: 1: plugin name, 2: open <a> tag, 3: version number, 4: close </a> tag
#: gravityforms.php:2347
msgid "There is a new version of %1$s available. %2$sView version %3$s details%4$s. "
msgstr ""

#. translators: 1: plugin name, 2: version number, 3: changelog URL
#. translators: 1: plugin name, 2: version number, 3: changelog URL
#: gravityforms.php:2351
#: gravityforms.php:2368
msgid "<a class=\"thickbox open-plugin-details-modal\" aria-label=\"View %1$s version %2$s details\" href=\"%3$s\">"
msgstr ""

#. translators: 1: plugin name, 2: open <a> tag, 3: version number, 4: close </a> tag, 5: open <a> tag 6. close </a> tag
#: gravityforms.php:2364
msgid "There is a new version of %1$s available. %2$sView version %3$s details%4$s or %5$supdate now%6$s. "
msgstr ""

#. translators: 1: upgrade URL, 2: plugin name
#: gravityforms.php:2377
msgid "<a href=\"%1$s\" class=\"update-link\" aria-label=\"Update %2$s now\">"
msgstr ""

#: gravityforms.php:2391
msgid "%sRegister%s your copy of Gravity Forms to receive access to automatic upgrades and support. Need a license key? %sPurchase one now%s."
msgstr ""

#: gravityforms.php:2438
msgid "IMPORTANT: As this is a major update, we strongly recommend creating a backup of your site before updating."
msgstr ""

#. translators: %s: version number
#: gravityforms.php:2458
msgid "The versions of the following add-ons you're running haven't been tested with Gravity Forms %s. Please update them or confirm compatibility before updating Gravity Forms, or you may experience issues:"
msgstr ""

#: gravityforms.php:2520
msgid "Oops!! Something went wrong. %sPlease try again or %scontact us%s."
msgstr ""

#: gravityforms.php:2569
msgid "Unread"
msgstr ""

#: gravityforms.php:2571
#: includes/addon/class-gf-payment-addon.php:2810
#: includes/fields/class-gf-field-total.php:24
#: includes/orders/summaries/views/view-order-summary.php:60
#: includes/orders/summaries/views/view-pricing-fields-html.php:61
#: includes/orders/summaries/views/view-pricing-fields-text.php:25
#: includes/template-library/templates/templates.php:2113
#: includes/template-library/templates/templates.php:2991
#: includes/template-library/templates/templates.php:3907
#: includes/template-library/templates/templates.php:4770
#: includes/template-library/templates/templates.php:7093
#: includes/template-library/templates/templates.php:7972
#: js.php:911
msgid "Total"
msgstr ""

#: gravityforms.php:2591
msgid "Last Entry: %s"
msgstr ""

#: gravityforms.php:2594
msgid "View All Entries"
msgstr ""

#: gravityforms.php:2606
msgid "View All Forms"
msgstr ""

#: gravityforms.php:2613
msgid "You don't have any forms. Let's go %screate one %s!"
msgstr ""

#: gravityforms.php:2646
msgid "There is an update available for Gravity Forms. %sView Details%s"
msgstr ""

#: gravityforms.php:2649
msgid "Dismiss"
msgstr ""

#: gravityforms.php:3185
msgid "Please select a form."
msgstr ""

#: gravityforms.php:3186
msgid "Failed to load the preview for this form."
msgstr ""

#: gravityforms.php:3564
msgid "Logging disabled."
msgstr ""

#: gravityforms.php:3566
msgid "Unable to disable logging."
msgstr ""

#: gravityforms.php:3590
msgid "Finished"
msgstr ""

#: gravityforms.php:3662
msgid "Add-On browser is currently unavailable. Please try again later."
msgstr ""

#: gravityforms.php:4085
msgid "There was an error while resending the notifications."
msgstr ""

#: gravityforms.php:4091
msgid "No notifications have been selected. Please select a notification to be sent."
msgstr ""

#: gravityforms.php:4095
msgid "The %sSend To%s email address provided is not valid."
msgstr ""

#: gravityforms.php:4692
#: gravityforms.php:4708
msgid "Oops! There was an error saving the form title. Please refresh the page and try again."
msgstr ""

#: gravityforms.php:4787
msgid "Select a different form"
msgstr ""

#: gravityforms.php:4807
msgid "Search forms"
msgstr ""

#: gravityforms.php:4811
msgid "Search for form"
msgstr ""

#: gravityforms.php:5337
#: gravityforms.php:5338
msgid "Editor"
msgstr ""

#: gravityforms.php:5351
msgid "Edit settings for this form"
msgstr ""

#: gravityforms.php:5367
msgid "View entries generated by this form"
msgstr ""

#: gravityforms.php:5553
#: widget.php:38
#: assets/js/src/admin/block-editor/blocks/form/edit.js:293
#: assets/js/src/admin/block-editor/blocks/form/index.js:24
msgid "Form"
msgstr ""

#: gravityforms.php:5591
msgid "Recent"
msgstr ""

#: gravityforms.php:5667
msgid "All Forms"
msgstr ""

#: gravityforms.php:5810
msgid "Auto-updates unavailable."
msgstr ""

#: gravityforms.php:5837
msgid "Please register your copy of Gravity Forms to enable automatic updates."
msgstr ""

#: gravityforms.php:5889
#: includes/locking/class-gf-locking.php:209
msgid "Error"
msgstr ""

#: gravityforms.php:6042
msgid "Select a form below to add it to your post or page."
msgstr ""

#: gravityforms.php:6043
msgid "Select a form from the list to add it to your post or page."
msgstr ""

#: gravityforms.php:6047
msgid "Can't find your form? Make sure it is active."
msgstr ""

#: gravityforms.php:6051
#: widget.php:158
msgid "Display form title"
msgstr ""

#: gravityforms.php:6056
msgid "Whether or not to display the form title."
msgstr ""

#: gravityforms.php:6059
#: widget.php:160
msgid "Display form description"
msgstr ""

#: gravityforms.php:6064
msgid "Whether or not to display the form description."
msgstr ""

#: gravityforms.php:6067
#: widget.php:167
msgid "Enable Ajax"
msgstr ""

#: gravityforms.php:6071
msgid "Specify whether or not to use Ajax to submit the form."
msgstr ""

#: gravityforms.php:6077
msgid "Specify the starting tab index for the fields of this form."
msgstr ""

#: gravityforms.php:6092
msgid "Select an action"
msgstr ""

#: gravityforms.php:6104
msgid "Select an action for this shortcode. Actions are added by some add-ons."
msgstr ""

#: gravityforms.php:6190
msgid "Gravity Forms logging is currently enabled. "
msgstr ""

#: gravityforms.php:6191
msgid "If you currently have a support ticket open, please do not disable logging until the Support Team has reviewed your logs. "
msgstr ""

#: gravityforms.php:6192
msgid "Since logs may contain sensitive information, please ensure that you only leave it enabled for as long as it is needed for troubleshooting. "
msgstr ""

#: gravityforms.php:6194
msgid "Once troubleshooting is complete, %1$sclick here to disable logging and permanently delete your log files.%2$s "
msgstr ""

#: gravityforms.php:6300
msgid "Forms per page"
msgstr ""

#. translators: 1: The table name 2: the URL with further details
#: gravityforms.php:6613
msgid "An outdated add-on or custom code is attempting to access the %1$s table which is not valid in this version of Gravity Forms. Update your add-ons and custom code to prevent loss of form data. Further details: %2$s"
msgstr ""

#: help.php:30
#: includes/template-library/templates/templates.php:9044
msgid "How can we help you?"
msgstr ""

#: help.php:33
msgid "Please review the %sdocumentation%s first. If you still can't find the answer %sopen a support ticket%s and we will be happy to answer your questions and assist you with any problems."
msgstr ""

#: help.php:38
msgid "Search Our Documentation"
msgstr ""

#: help.php:46
msgid "User Documentation"
msgstr ""

#: help.php:50
msgid "Creating a Form"
msgstr ""

#: help.php:55
msgid "Embedding a Form"
msgstr ""

#: help.php:60
msgid "Reviewing Form Submissions"
msgstr ""

#: help.php:65
msgid "Configuring Confirmations"
msgstr ""

#: help.php:71
msgid "Configuring Notifications"
msgstr ""

#: help.php:81
msgid "Developer Documentation"
msgstr ""

#: help.php:85
msgid "Discover the Gravity Forms API"
msgstr ""

#: help.php:90
msgid "API Functions"
msgstr ""

#: help.php:95
msgid "REST API"
msgstr ""

#: help.php:100
msgid "Add-On Framework"
msgstr ""

#: help.php:105
msgid "GFAddOn"
msgstr ""

#: help.php:115
msgid "Designer Documentation"
msgstr ""

#: help.php:119
msgid "CSS Selectors"
msgstr ""

#: help.php:124
msgid "CSS Targeting Examples"
msgstr ""

#: help.php:129
msgid "CSS Ready Classes"
msgstr ""

#: help.php:134
msgid "gform_field_css_class"
msgstr ""

#: help.php:139
msgid "gform_noconflict_styles"
msgstr ""

#: includes/addon/class-gf-addon.php:660
msgid "Required Gravity Forms Add-On is missing: %s."
msgstr ""

#: includes/addon/class-gf-addon.php:668
msgid "Required Gravity Forms Add-On \"%s\" does not meet minimum version requirement: %s."
msgstr ""

#: includes/addon/class-gf-addon.php:688
msgid "Required WordPress plugin is missing: %s."
msgstr ""

#: includes/addon/class-gf-addon.php:698
msgid "Current PHP version (%s) does not meet minimum PHP version requirement (%s)."
msgstr ""

#: includes/addon/class-gf-addon.php:715
msgid "Required PHP extension missing: %s"
msgstr ""

#: includes/addon/class-gf-addon.php:722
msgid "Required PHP extension \"%s\" does not meet minimum version requirement: %s."
msgstr ""

#: includes/addon/class-gf-addon.php:737
msgid "Required PHP function missing: %s"
msgstr ""

#: includes/addon/class-gf-addon.php:750
msgid "Current WordPress version (%s) does not meet minimum WordPress version requirement (%s)."
msgstr ""

#: includes/addon/class-gf-addon.php:789
msgid "%s is not able to run because your WordPress environment has not met the minimum requirements."
msgstr ""

#: includes/addon/class-gf-addon.php:790
msgid "Please resolve the following issues to use %s:"
msgstr ""

#: includes/addon/class-gf-addon.php:1595
msgid "GF Add-Ons"
msgstr ""

#: includes/addon/class-gf-addon.php:1652
#: includes/addon/class-gf-addon.php:4984
#: includes/addon/class-gf-addon.php:5339
#: settings.php:1296
msgid "Uninstall"
msgstr ""

#: includes/addon/class-gf-addon.php:1655
msgid "Add-On Page"
msgstr ""

#: includes/addon/class-gf-addon.php:1658
msgid "Add-On Settings"
msgstr ""

#: includes/addon/class-gf-addon.php:1663
msgid "Results Page"
msgstr ""

#: includes/addon/class-gf-addon.php:1682
msgid "Gravity Forms Add-Ons"
msgstr ""

#: includes/addon/class-gf-addon.php:1797
#: includes/addon/class-gf-addon.php:4860
#: includes/addon/class-gf-addon.php:5165
#: includes/addon/class-gf-feed-addon.php:1823
msgid "%s Settings"
msgstr ""

#: includes/addon/class-gf-addon.php:1997
#: includes/addon/class-gf-addon.php:2071
msgid "Field could not be rendered."
msgstr ""

#: includes/addon/class-gf-addon.php:2086
msgid "Field type '%s' has not been implemented"
msgstr ""

#: includes/addon/class-gf-addon.php:2294
msgid "%s settings updated."
msgstr ""

#: includes/addon/class-gf-addon.php:2300
#: includes/settings/class-settings.php:992
msgid "There was an error while saving your settings."
msgstr ""

#: includes/addon/class-gf-addon.php:2814
msgid "Please add a %s field to your form."
msgstr ""

#: includes/addon/class-gf-addon.php:2907
#: assets/js/src/legacy/admin/settings/field-map/mapping.js:219
msgid "Add Custom Key"
msgstr ""

#: includes/addon/class-gf-addon.php:2917
#: includes/settings/fields/class-select-custom.php:94
#: assets/js/src/legacy/admin/settings/field-map/mapping.js:302
msgid "Add Custom Value"
msgstr ""

#: includes/addon/class-gf-addon.php:2938
#: includes/settings/fields/class-select-custom.php:150
msgid "Reset"
msgstr ""

#: includes/addon/class-gf-addon.php:2960
#: includes/settings/fields/class-field-map.php:40
msgid "Form Field"
msgstr ""

#: includes/addon/class-gf-addon.php:2978
#: includes/settings/fields/class-field-map.php:35
#: js.php:244
#: js.php:279
msgid "Field"
msgstr ""

#: includes/addon/class-gf-addon.php:3010
#: includes/settings/fields/class-field-select.php:71
#: includes/settings/fields/class-generic-map.php:538
#: notification.php:216
#: assets/js/src/legacy/admin/settings/field-map/mapping.js:158
msgid "Select a Field"
msgstr ""

#: includes/addon/class-gf-addon.php:3017
#: includes/settings/fields/class-field-select.php:82
#: includes/settings/fields/class-generic-map.php:547
msgid "Select a %s Field"
msgstr ""

#: includes/addon/class-gf-addon.php:3066
#: includes/addon/class-gf-addon.php:3073
#: includes/addon/class-gf-addon.php:3093
#: includes/settings/fields/class-field-select.php:237
#: includes/settings/fields/class-generic-map.php:505
msgid "Full"
msgstr ""

#: includes/addon/class-gf-addon.php:3080
#: includes/settings/fields/class-field-select.php:248
#: includes/settings/fields/class-generic-map.php:482
msgid "Selected"
msgstr ""

#: includes/addon/class-gf-addon.php:3429
msgid "Update Settings"
msgstr ""

#: includes/addon/class-gf-addon.php:3765
#: includes/fields/class-gf-field-calculation.php:32
#: includes/fields/class-gf-field-hiddenproduct.php:50
#: includes/fields/class-gf-field-radio.php:90
#: includes/fields/class-gf-field-repeater.php:598
#: includes/fields/class-gf-field-singleproduct.php:57
#: includes/fields/class-gf-field-website.php:68
#: includes/fields/class-gf-field.php:825
#: includes/settings/fields/class-base.php:784
msgid "This field is required."
msgstr ""

#: includes/addon/class-gf-addon.php:3810
msgid "Validation Error"
msgstr ""

#: includes/addon/class-gf-addon.php:4309
msgid "Unable to render form settings."
msgstr ""

#: includes/addon/class-gf-addon.php:4647
#: includes/addon/class-gf-addon.php:4653
msgid "You don't have adequate permission to view this page"
msgstr ""

#: includes/addon/class-gf-addon.php:4815
msgid "This add-on needs to be updated. Please contact the developer."
msgstr ""

#: includes/addon/class-gf-addon.php:4829
#: includes/addon/class-gf-addon.php:5002
#: includes/addon/class-gf-addon.php:5123
msgid "%s has been successfully uninstalled. It can be re-activated from the %splugins page%s."
msgstr ""

#: includes/addon/class-gf-addon.php:5013
#: includes/addon/class-gf-addon.php:5027
#: includes/addon/class-gf-addon.php:5337
msgid "Uninstall %s"
msgstr ""

#: includes/addon/class-gf-addon.php:5019
msgid "Warning"
msgstr ""

#: includes/addon/class-gf-addon.php:5234
msgid "You don't have sufficient permissions to update the settings."
msgstr ""

#: includes/addon/class-gf-addon.php:5302
msgid "This operation deletes ALL %s settings."
msgstr ""

#: includes/addon/class-gf-addon.php:5332
#: includes/addon/class-gf-addon.php:5399
msgid "%s"
msgstr ""

#: includes/addon/class-gf-addon.php:5350
msgid "Uninstall %s Add-On"
msgstr ""

#: includes/addon/class-gf-addon.php:5369
msgid "Uninstall Add-On"
msgstr ""

#: includes/addon/class-gf-addon.php:5400
msgid "To continue uninstalling this add-on click the settings button."
msgstr ""

#: includes/addon/class-gf-addon.php:5414
msgid "%sThis operation deletes ALL %s settings%s. If you continue, you will NOT be able to retrieve these settings."
msgstr ""

#: includes/addon/class-gf-addon.php:5418
msgid "Warning! ALL %s settings will be deleted. This cannot be undone. 'OK' to delete, 'Cancel' to stop"
msgstr ""

#: includes/addon/class-gf-addon.php:5444
msgid "You don't have adequate permission to uninstall this add-on: "
msgstr ""

#: includes/addon/class-gf-addon.php:5548
#: includes/addon/class-gf-auto-upgrade.php:66
msgid "Gravity Forms %s is required. Activate it now or %spurchase it today!%s"
msgstr ""

#: includes/addon/class-gf-addon.php:5561
msgid "Some features of the add-on are not available on the current version of Gravity Forms. Please update to the latest Gravity Forms version for full compatibility."
msgstr ""

#. translators: 1: Add-on title
#: includes/addon/class-gf-addon.php:6133
msgid "Some features of the %1$s Add-on are not available on this version of Gravity Forms. Please update to the latest version for full compatibility."
msgstr ""

#: includes/addon/class-gf-auto-upgrade.php:198
msgid "Oops!! Something went wrong.%sPlease try again or %scontact us%s."
msgstr ""

#: includes/addon/class-gf-auto-upgrade.php:258
#: includes/addon/class-gf-auto-upgrade.php:310
msgid "View version %s details"
msgstr ""

#: includes/addon/class-gf-auto-upgrade.php:260
#: includes/addon/class-gf-auto-upgrade.php:312
msgid "There is a new version of %1$s available. %s."
msgstr ""

#: includes/addon/class-gf-auto-upgrade.php:268
#: includes/addon/class-gf-auto-upgrade.php:324
msgid "Your version of %s is up to date."
msgstr ""

#: includes/addon/class-gf-auto-upgrade.php:300
#: includes/system-status/class-gf-update.php:117
#: includes/system-status/class-gf-update.php:194
msgid "%sRegister%s your copy of Gravity Forms to receive access to automatic updates and support. Need a license key? %sPurchase one now%s."
msgstr ""

#: includes/addon/class-gf-auto-upgrade.php:316
msgid "You can update to the latest version automatically or download the update and install it manually. %sUpdate Automatically%s %sDownload Update%s"
msgstr ""

#: includes/addon/class-gf-feed-addon.php:1025
msgid "Copy 1"
msgstr ""

#: includes/addon/class-gf-feed-addon.php:1027
msgid "Copy %d"
msgstr ""

#. translators: %1$s represents the missing table, %2$s is the opening link tag, %3$s is the closing link tag.
#: includes/addon/class-gf-feed-addon.php:1068
msgid "The table `%1$s` does not exist. Please visit the %2$sForms > System Status%3$s page and click the \"Re-run database upgrade\" link (under the Database section) to create the missing table."
msgstr ""

#: includes/addon/class-gf-feed-addon.php:1266
#: includes/addon/class-gf-payment-addon.php:3754
#: includes/settings/class-settings.php:1499
#: includes/webapi/webapi.php:1060
#: includes/webapi/webapi.php:1068
#: includes/webapi/webapi.php:1100
msgid "Access denied."
msgstr ""

#: includes/addon/class-gf-feed-addon.php:1353
msgid "%s Feeds"
msgstr ""

#: includes/addon/class-gf-feed-addon.php:1467
msgid "Unable to render feed settings."
msgstr ""

#: includes/addon/class-gf-feed-addon.php:1486
msgid "Feed Settings"
msgstr ""

#: includes/addon/class-gf-feed-addon.php:1588
msgid "You don't have sufficient permissions to update the form settings."
msgstr ""

#: includes/addon/class-gf-feed-addon.php:1636
msgid "Feed updated successfully."
msgstr ""

#: includes/addon/class-gf-feed-addon.php:1650
msgid "There was an error updating this feed. Please review all errors below and try again."
msgstr ""

#: includes/addon/class-gf-feed-addon.php:1790
msgid "WARNING: You are about to delete this item."
msgstr ""

#: includes/addon/class-gf-feed-addon.php:1806
msgid "You don't have any feeds configured. Let's go %screate one%s!"
msgstr ""

#: includes/addon/class-gf-feed-addon.php:1826
msgid "To get started, please configure your %s."
msgstr ""

#: includes/addon/class-gf-feed-addon.php:1888
#: includes/settings/fields/class-conditional-logic.php:45
msgid "Process this feed if"
msgstr ""

#: includes/addon/class-gf-feed-addon.php:1909
#: includes/settings/fields/class-conditional-logic.php:50
msgid "Enable Condition"
msgstr ""

#: includes/addon/class-gf-feed-addon.php:1963
msgid "Invalid value"
msgstr ""

#: includes/addon/class-gf-feed-addon.php:2049
msgid "Process %s feed only when payment is received."
msgstr ""

#: includes/addon/class-gf-feed-addon.php:2061
#: includes/addon/class-gf-feed-addon.php:2064
msgid "Post Payment Actions"
msgstr ""

#: includes/addon/class-gf-feed-addon.php:2064
msgid "Select which actions should only occur after payment has been received."
msgstr ""

#: includes/addon/class-gf-feed-addon.php:2462
#: settings.php:1024
msgid "Checkbox"
msgstr ""

#: includes/addon/class-gf-feed-addon.php:2477
msgid "feed"
msgstr ""

#: includes/addon/class-gf-feed-addon.php:2478
msgid "feeds"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:1017
msgid "Payment failed to be captured. Reason: %s"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:1059
msgid "Initial payment"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:1066
msgid "%s has been captured successfully. Amount: %s. Transaction Id: %s"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:1071
msgid "Failed to capture %s. Reason: %s."
msgstr ""

#: includes/addon/class-gf-payment-addon.php:1084
msgid "Subscription failed to be created. Reason: %s"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:1414
msgid "options: "
msgstr ""

#: includes/addon/class-gf-payment-addon.php:1644
msgid "This webhook has already been processed (Event Id: %s)"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:1810
msgid "Payment is pending. Amount: %s. Transaction Id: %s."
msgstr ""

#: includes/addon/class-gf-payment-addon.php:1841
msgid "Payment has been authorized. Amount: %s. Transaction Id: %s."
msgstr ""

#: includes/addon/class-gf-payment-addon.php:1876
msgid "Payment has been completed. Amount: %s. Transaction Id: %s."
msgstr ""

#: includes/addon/class-gf-payment-addon.php:1934
msgid "Payment has been refunded. Amount: %s. Transaction Id: %s."
msgstr ""

#: includes/addon/class-gf-payment-addon.php:1988
msgid "Payment has failed. Amount: %s."
msgstr ""

#: includes/addon/class-gf-payment-addon.php:2007
msgid "Authorization has been voided. Transaction Id: %s"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:2038
msgid "Subscription has been created. Subscription Id: %s."
msgstr ""

#: includes/addon/class-gf-payment-addon.php:2092
msgid "Subscription has been paid. Amount: %s. Subscription Id: %s"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:2147
msgid "Subscription payment has failed. Amount: %s. Subscription Id: %s."
msgstr ""

#: includes/addon/class-gf-payment-addon.php:2202
msgid "Subscription has been cancelled. Subscription Id: %s."
msgstr ""

#: includes/addon/class-gf-payment-addon.php:2243
msgid "Subscription has expired. Subscriber Id: %s"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:2338
#: includes/addon/class-gf-payment-addon.php:2414
#: includes/addon/class-gf-payment-addon.php:2418
#: includes/class-confirmation.php:873
#: includes/fields/class-gf-field-name.php:58
#: includes/template-library/templates/templates.php:33
#: includes/template-library/templates/templates.php:1346
#: includes/template-library/templates/templates.php:1805
#: includes/template-library/templates/templates.php:2345
#: includes/template-library/templates/templates.php:2406
#: includes/template-library/templates/templates.php:2547
#: includes/template-library/templates/templates.php:3300
#: includes/template-library/templates/templates.php:3363
#: includes/template-library/templates/templates.php:3462
#: includes/template-library/templates/templates.php:4163
#: includes/template-library/templates/templates.php:4226
#: includes/template-library/templates/templates.php:4325
#: includes/template-library/templates/templates.php:6247
#: includes/template-library/templates/templates.php:8308
#: includes/template-library/templates/templates.php:8673
#: includes/template-library/templates/templates.php:9232
#: includes/template-library/templates/templates.php:10332
#: includes/template-library/templates/templates.php:10682
#: js.php:667
#: js.php:888
#: notification.php:235
#: notification.php:1302
msgid "Name"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:2339
#: includes/addon/class-gf-payment-addon.php:2422
#: includes/addon/class-gf-payment-addon.php:2436
msgid "Transaction Type"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:2347
#: includes/addon/class-gf-payment-addon.php:2434
msgid "Subscription"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:2350
#: includes/addon/class-gf-payment-addon.php:2431
msgid "Products and Services"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:2353
msgid "Donations"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:2358
msgid "Unsupported transaction type"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:2365
#: includes/addon/class-gf-payment-addon.php:2699
#: includes/addon/class-gf-payment-addon.php:2707
msgid "Form Total"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:2402
msgid "You must add a Credit Card field to your form before creating a feed. Let's go %sadd one%s!"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:2418
msgid "Enter a feed name to uniquely identify this setup."
msgstr ""

#: includes/addon/class-gf-payment-addon.php:2427
msgid "Select a transaction type"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:2436
msgid "Select a transaction type."
msgstr ""

#: includes/addon/class-gf-payment-addon.php:2441
msgid "Subscription Settings"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:2453
msgid "Select which field determines the recurring payment amount, or select 'Form Total' to use the total of all pricing fields as the recurring amount."
msgstr ""

#: includes/addon/class-gf-payment-addon.php:2457
#: includes/addon/class-gf-payment-addon.php:2459
msgid "Billing Cycle"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:2459
msgid "Select your billing cycle.  This determines how often the recurring payment should occur."
msgstr ""

#: includes/addon/class-gf-payment-addon.php:2463
#: includes/addon/class-gf-payment-addon.php:2471
msgid "Recurring Times"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:2467
msgid "infinite"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:2471
msgid "Select how many times the recurring payment should be made.  The default is to bill the customer until the subscription is canceled."
msgstr ""

#: includes/addon/class-gf-payment-addon.php:2475
msgid "Setup Fee"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:2481
#: includes/orders/factories/class-gf-order-factory.php:188
#: includes/orders/factories/class-gf-order-factory.php:202
msgid "Trial"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:2484
msgid "Trial Period"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:2484
msgid "Enable a trial period.  The user's recurring payment will not begin until after this trial period."
msgstr ""

#: includes/addon/class-gf-payment-addon.php:2489
msgid "Products &amp; Services Settings"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:2502
msgid "Select which field determines the payment amount, or select 'Form Total' to use the total of all pricing fields as the payment amount."
msgstr ""

#: includes/addon/class-gf-payment-addon.php:2507
msgid "Other Settings"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:2522
#: includes/addon/class-gf-payment-addon.php:2525
msgid "Billing Information"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:2525
msgid "Map your Form Fields to the available listed fields."
msgstr ""

#: includes/addon/class-gf-payment-addon.php:2543
msgid "When conditions are enabled, form submissions will only be sent to the payment gateway when the conditions are met. When disabled, all form submissions will be sent to the payment gateway."
msgstr ""

#: includes/addon/class-gf-payment-addon.php:2664
msgid "Enter an amount"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:2716
msgid "Sample Option"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:2729
#: includes/fields/class-gf-field-address.php:37
#: includes/settings/fields/class-field-select.php:147
#: includes/settings/fields/class-generic-map.php:344
#: includes/template-library/templates/templates.php:5230
#: includes/template-library/templates/templates.php:6471
#: includes/template-library/templates/templates.php:8854
#: js.php:725
msgid "Address"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:2730
#: includes/settings/fields/class-field-select.php:148
#: includes/settings/fields/class-generic-map.php:345
msgid "Address 2"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:2731
#: includes/fields/class-gf-field-address.php:211
#: includes/settings/fields/class-field-select.php:149
#: includes/settings/fields/class-generic-map.php:346
#: includes/template-library/templates/templates.php:615
#: includes/template-library/templates/templates.php:2772
#: includes/template-library/templates/templates.php:2859
#: includes/template-library/templates/templates.php:3687
#: includes/template-library/templates/templates.php:3774
#: includes/template-library/templates/templates.php:4550
#: includes/template-library/templates/templates.php:4637
#: includes/template-library/templates/templates.php:5252
#: includes/template-library/templates/templates.php:6493
#: includes/template-library/templates/templates.php:7747
#: includes/template-library/templates/templates.php:8876
#: js.php:740
msgid "City"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:2733
#: includes/settings/fields/class-field-select.php:151
#: includes/settings/fields/class-generic-map.php:348
msgid "Zip"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:2734
#: includes/fields/class-gf-field-address.php:215
#: includes/settings/fields/class-field-select.php:152
#: includes/settings/fields/class-generic-map.php:349
#: includes/template-library/templates/templates.php:634
#: includes/template-library/templates/templates.php:2790
#: includes/template-library/templates/templates.php:2877
#: includes/template-library/templates/templates.php:3705
#: includes/template-library/templates/templates.php:3792
#: includes/template-library/templates/templates.php:4568
#: includes/template-library/templates/templates.php:4655
#: includes/template-library/templates/templates.php:5270
#: includes/template-library/templates/templates.php:6511
#: includes/template-library/templates/templates.php:7762
#: includes/template-library/templates/templates.php:8894
#: js.php:755
msgid "Country"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:2752
msgid "day(s)"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:2753
msgid "week(s)"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:2754
msgid "month(s)"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:2755
msgid "year(s)"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:2764
msgid "Select a product field"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:2780
msgctxt "toolbar label"
msgid "Sales"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:2781
msgctxt "metabox title"
msgid "Filter"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:2807
msgid "Today"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:2808
msgid "Yesterday"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:2809
msgid "Last 30 Days"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:2826
msgid "orders"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:2827
msgid "subscriptions"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:2835
msgid "There aren't any transactions that match your criteria."
msgstr ""

#: includes/addon/class-gf-payment-addon.php:2954
#: includes/addon/class-gf-payment-addon.php:2963
msgid "Revenue"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:2959
msgid "Orders"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:2960
msgid "Subscriptions"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:2961
msgid "Recurring Payments"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:2962
msgid "Refunds"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:2985
#: includes/addon/class-gf-payment-addon.php:2986
msgid "Week"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:3003
#: includes/addon/class-gf-payment-addon.php:3004
#: includes/fields/class-gf-field-creditcard.php:332
#: includes/fields/class-gf-field-creditcard.php:453
#: includes/fields/class-gf-field-date.php:748
#: includes/fields/class-gf-field-date.php:979
#: includes/fields/class-gf-field-date.php:1133
#: js.php:316
#: js.php:1059
#: js.php:1064
#: js.php:1065
msgid "Month"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:3021
#: includes/addon/class-gf-payment-addon.php:3024
#: includes/fields/class-gf-field-date.php:772
#: includes/fields/class-gf-field-date.php:982
#: includes/fields/class-gf-field-date.php:1138
#: js.php:318
#: js.php:1060
#: js.php:1066
#: js.php:1067
msgid "Day"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:3163
msgid "Jan"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:3164
msgid "Feb"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:3165
msgid "Mar"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:3166
msgid "Apr"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:3168
msgid "Jun"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:3169
msgid "Jul"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:3170
msgid "Aug"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:3171
msgid "Sep"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:3172
msgid "Oct"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:3173
msgid "Nov"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:3174
msgid "Dec"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:3293
msgid "Daily"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:3294
msgid "Weekly"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:3295
msgid "Monthly"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:3301
msgid "Select how you would like the sales data to be displayed."
msgstr ""

#: includes/addon/class-gf-payment-addon.php:3311
msgctxt "regarding a payment method"
msgid "Any"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:3322
#: includes/template-library/templates/templates.php:3034
#: includes/template-library/templates/templates.php:4814
#: includes/template-library/templates/templates.php:4848
msgid "Payment Method"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:3443
msgid "Warning! This subscription will be canceled. This cannot be undone. 'OK' to cancel subscription, 'Cancel' to stop"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:3445
msgid "Canceled"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:3446
msgid "The subscription could not be canceled. Please try again later."
msgstr ""

#: includes/addon/class-gf-payment-addon.php:3712
msgid "Cancel Subscription"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:3843
msgid "sale"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:3844
msgid "sales"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:3869
msgid "There hasn't been any sales in the specified date range."
msgstr ""

#: includes/addon/class-gf-payment-addon.php:3892
msgid "1 item"
msgid_plural "%s items"
msgstr[0] ""
msgstr[1] ""

#: includes/addon/class-gf-payment-addon.php:3909
msgid "Go to the first page"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:3916
msgid "Go to the previous page"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:3926
msgctxt "paging"
msgid "%1$s of %2$s"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:3931
msgid "Go to the next page"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:3940
msgid "Go to the last page"
msgstr ""

#: includes/addon/class-gf-results.php:21
msgid "Results Filters"
msgstr ""

#: includes/addon/class-gf-results.php:82
msgid "Error retrieving results. If the problem persists, please contact support."
msgstr ""

#: includes/addon/class-gf-results.php:128
#: includes/addon/class-gf-results.php:158
msgid "View results generated by this form"
msgstr ""

#: includes/addon/class-gf-results.php:150
#: includes/addon/class-gf-results.php:156
msgid "Results"
msgstr ""

#: includes/addon/class-gf-results.php:252
msgid "Include results if"
msgstr ""

#: includes/addon/class-gf-results.php:259
msgid "Start date"
msgstr ""

#: includes/addon/class-gf-results.php:261
#: includes/addon/class-gf-results.php:267
#: includes/settings/fields/class-date-time.php:145
msgid "Open Date Picker"
msgstr ""

#: includes/addon/class-gf-results.php:265
msgid "End date"
msgstr ""

#: includes/addon/class-gf-results.php:286
msgid "Apply filters"
msgstr ""

#: includes/addon/class-gf-results.php:289
msgid "Clear"
msgstr ""

#: includes/addon/class-gf-results.php:315
msgid "This form does not have any fields that can be used for results"
msgstr ""

#: includes/addon/class-gf-results.php:326
#: includes/addon/class-gf-results.php:648
msgid "Total Score"
msgstr ""

#: includes/addon/class-gf-results.php:326
msgid "Scores are weighted calculations. Items ranked higher are given a greater score than items that are ranked lower. The total score for each item is the sum of the weighted scores."
msgstr ""

#: includes/addon/class-gf-results.php:327
#: includes/addon/class-gf-results.php:651
msgid "Aggregate Rank"
msgstr ""

#: includes/addon/class-gf-results.php:327
msgid "The aggregate rank is the overall rank for all entries based on the weighted scores for each item."
msgstr ""

#: includes/addon/class-gf-results.php:328
msgid "Date Range"
msgstr ""

#: includes/addon/class-gf-results.php:328
msgid "Date Range is optional, if no date range is specified it will be ignored."
msgstr ""

#: includes/addon/class-gf-results.php:329
msgid "Filters"
msgstr ""

#: includes/addon/class-gf-results.php:329
msgid "Narrow the results by adding filters. Note that some field types support more options than others."
msgstr ""

#: includes/addon/class-gf-results.php:330
msgid "Average Row Score"
msgstr ""

#: includes/addon/class-gf-results.php:330
msgid "The average (mean) score for each row: the sum of all the scores for each row divided by the total number of entries."
msgstr ""

#: includes/addon/class-gf-results.php:331
msgid "Average Global Score"
msgstr ""

#: includes/addon/class-gf-results.php:331
msgid "The average (mean) score for the whole field. The sum of the total scores divided by the number of entries."
msgstr ""

#: includes/addon/class-gf-results.php:332
#: includes/addon/class-gf-results.php:575
msgid "Average Score"
msgstr ""

#: includes/addon/class-gf-results.php:332
msgid "The average (mean) score: The sum of the scores divided by the number of entries."
msgstr ""

#: includes/addon/class-gf-results.php:360
msgid "No results."
msgstr ""

#: includes/addon/class-gf-results.php:385
msgid "There was an error while processing the entries. Please contact support."
msgstr ""

#: includes/addon/class-gf-results.php:399
msgid "Entries processed: %1$d of %2$d"
msgstr ""

#: includes/addon/class-gf-results.php:416
msgid "No results"
msgstr ""

#: includes/addon/class-gf-results.php:475
#: includes/addon/class-gf-results.php:482
#: includes/addon/class-gf-results.php:497
msgid "No entries for this field"
msgstr ""

#: includes/addon/class-gf-results.php:504
msgid "Choice"
msgstr ""

#: includes/addon/class-gf-results.php:504
#: includes/addon/class-gf-results.php:534
msgid "Frequency"
msgstr ""

#: includes/addon/class-gf-results.php:631
msgid "Average global score"
msgstr ""

#: includes/addon/class-gf-results.php:633
msgid "Average score"
msgstr ""

#: includes/addon/class-gf-results.php:645
msgid "Item"
msgstr ""

#: includes/addon/class-gf-results.php:681
msgid "Latest values:"
msgstr ""

#: includes/addon/class-gf-results.php:689
msgid "Show more"
msgstr ""

#: includes/api.php:136
msgid "Form with id: %s not found"
msgstr ""

#: includes/api.php:216
#: includes/api.php:447
msgid "Invalid form object"
msgstr ""

#: includes/api.php:228
#: includes/webapi/v2/includes/controllers/class-controller-form-feeds.php:207
#: includes/webapi/webapi.php:1342
msgid "Missing form id"
msgstr ""

#: includes/api.php:248
#: includes/api.php:2150
#: includes/webapi/v2/includes/class-results-cache.php:477
#: includes/webapi/v2/includes/controllers/class-controller-form-field-filters.php:46
#: includes/webapi/v2/includes/controllers/class-controller-forms.php:152
msgid "Form not found"
msgstr ""

#: includes/api.php:258
msgid "Error updating form"
msgstr ""

#: includes/api.php:265
msgid "Error updating form confirmations"
msgstr ""

#: includes/api.php:273
msgid "Error updating form notifications"
msgstr ""

#: includes/api.php:284
msgid "Error updating title"
msgstr ""

#: includes/api.php:335
msgid "Property key incorrect"
msgstr ""

#: includes/api.php:414
msgid "Invalid form objects"
msgstr ""

#: includes/api.php:451
msgid "The form title is missing"
msgstr ""

#: includes/api.php:455
msgid "The form fields are missing"
msgstr ""

#: includes/api.php:506
msgid "There was a problem while inserting the form"
msgstr ""

#: includes/api.php:693
#: includes/api.php:704
msgid "Entry with id %s not found"
msgstr ""

#: includes/api.php:826
#: includes/legacy/forms_model_legacy.php:2031
msgid "Missing entry id"
msgstr ""

#: includes/api.php:832
#: includes/legacy/forms_model_legacy.php:2037
msgid "Entry not found"
msgstr ""

#: includes/api.php:847
#: includes/api.php:1216
#: includes/legacy/forms_model_legacy.php:2051
#: includes/legacy/forms_model_legacy.php:2273
msgid "The form for this entry does not exist"
msgstr ""

#: includes/api.php:993
#: includes/legacy/forms_model_legacy.php:2118
msgid "There was a problem while updating the entry properties"
msgstr ""

#: includes/api.php:1103
#: includes/api.php:1155
#: includes/legacy/forms_model_legacy.php:2158
#: includes/legacy/forms_model_legacy.php:2194
msgid "There was a problem while updating the field values"
msgstr ""

#: includes/api.php:1135
#: includes/legacy/forms_model_legacy.php:2145
msgid "There was a problem while updating one of the input values for the entry"
msgstr ""

#: includes/api.php:1206
#: includes/legacy/forms_model_legacy.php:2263
msgid "The entry object must be an array"
msgstr ""

#: includes/api.php:1212
#: includes/legacy/forms_model_legacy.php:2269
msgid "The form id must be specified"
msgstr ""

#: includes/api.php:1260
#: includes/legacy/forms_model_legacy.php:2313
msgid "There was a problem while inserting the entry properties"
msgstr ""

#: includes/api.php:1326
msgid "Invalid entry id: %s"
msgstr ""

#: includes/api.php:1476
#: includes/api.php:1574
msgid "Note not found"
msgstr ""

#: includes/api.php:1501
msgid "Invalid entry"
msgstr ""

#: includes/api.php:1505
msgid "Invalid or empty note"
msgstr ""

#: includes/api.php:1525
msgid "Invalid note"
msgstr ""

#: includes/api.php:1556
msgid "Invalid note format"
msgstr ""

#: includes/api.php:1568
msgid "Missing note id"
msgstr ""

#: includes/api.php:1679
#: includes/api.php:1688
msgid "There was an error while processing the form:"
msgstr ""

#: includes/api.php:1759
msgid "You must be logged in to use this form."
msgstr ""

#: includes/api.php:1836
msgid "Field not found."
msgstr ""

#: includes/api.php:1841
msgid "Field does not support validation."
msgstr ""

#: includes/api.php:1865
msgid "Your form could not be found"
msgstr ""

#: includes/api.php:1869
msgid "Your form doesn't have any fields."
msgstr ""

#: includes/api.php:2018
msgid "Feed not found"
msgstr ""

#: includes/api.php:2074
msgid "There was an error while deleting feed id %s"
msgstr ""

#: includes/api.php:2159
msgid "There was an error while inserting a feed"
msgstr ""

#: includes/api.php:2190
msgid "The %s table does not exist."
msgstr ""

#: includes/class-confirmation.php:214
msgid "Confirmation Name"
msgstr ""

#: includes/class-confirmation.php:225
msgid "Confirmation Type"
msgstr ""

#: includes/class-confirmation.php:239
#: includes/class-confirmation.php:1193
msgid "Redirect"
msgstr ""

#: includes/class-confirmation.php:247
#: notification.php:432
msgid "Message"
msgstr ""

#: includes/class-confirmation.php:263
msgid "Auto-Formatting"
msgstr ""

#: includes/class-confirmation.php:268
#: notification.php:445
msgid "Disable auto-formatting"
msgstr ""

#: includes/class-confirmation.php:301
msgid "Redirect URL"
msgstr ""

#: includes/class-confirmation.php:317
msgid "You must specify a valid Redirect URL."
msgstr ""

#: includes/class-confirmation.php:325
msgid "Pass Field Data via Query String"
msgstr ""

#: includes/class-confirmation.php:338
msgid "Sample: phone={Phone:1}&email={Email:2}"
msgstr ""

#: includes/class-confirmation.php:346
#: notification.php:471
msgid "Enable conditional logic"
msgstr ""

#: includes/class-confirmation.php:355
msgid "Save Confirmation"
msgstr ""

#: includes/class-confirmation.php:527
msgid "Your confirmation message appears to contain a merge tag as the value for an HTML attribute. Depending on the attribute and field type, this might be a security risk. %sFurther details%s"
msgstr ""

#: includes/class-confirmation.php:541
msgid "Confirmation Settings"
msgstr ""

#: includes/class-confirmation.php:625
msgid "Save &amp; Continue Link"
msgstr ""

#: includes/class-confirmation.php:628
msgid "Save &amp; Continue Token"
msgstr ""

#: includes/class-confirmation.php:634
msgid "Save &amp; Continue Email Input"
msgstr ""

#: includes/class-confirmation.php:1099
msgid "WARNING: You are about to delete this confirmation."
msgstr ""

#: includes/class-confirmation.php:1099
msgid "\\'Cancel\\' to stop, \\'OK\\' to delete."
msgstr ""

#: includes/class-confirmation.php:1156
msgid "<em>This page does not exist.</em>"
msgstr ""

#: includes/class-gf-osdxp.php:73
#: includes/class-gf-osdxp.php:122
msgid "There was an error while validating your license key. Gravity Forms will continue to work, but automatic upgrades will not be available. Please contact support to resolve this issue."
msgstr ""

#: includes/class-gf-osdxp.php:75
msgid "Valid"
msgstr ""

#: includes/class-gf-osdxp.php:78
msgid "Invalid or Expired."
msgstr ""

#: includes/class-gf-osdxp.php:130
msgid "Valid Key : Your license key has been successfully validated."
msgstr ""

#: includes/class-gf-osdxp.php:138
msgid "Invalid Key - an Enterprise license is required."
msgstr ""

#: includes/class-gf-osdxp.php:140
msgid "Invalid or Expired Key - Please make sure you have entered the correct value and that your key is not expired."
msgstr ""

#: includes/class-gf-osdxp.php:183
msgid "License successfully removed."
msgstr ""

#. translators: 1: version number 2: open link tag 3: closing link tag.
#: includes/class-gf-upgrade.php:293
msgid "Gravity Forms is currently upgrading the database to version %1$s. For sites with a large number of entries this may take a long time. Check the %2$sSystem Status%3$s page for further details."
msgstr ""

#: includes/class-gf-upgrade.php:741
msgid "Queued for upgrade."
msgstr ""

#: includes/class-gf-upgrade.php:836
msgid "Migrating forms."
msgstr ""

#: includes/class-gf-upgrade.php:895
msgid "Forms migrated."
msgstr ""

#: includes/class-gf-upgrade.php:926
msgid "Entry details migrated."
msgstr ""

#. translators: %s: the database error
#. translators: %s: the database error
#: includes/class-gf-upgrade.php:976
#: includes/class-gf-upgrade.php:1002
msgid "Error Migrating Entry Headers: %s"
msgstr ""

#: includes/class-gf-upgrade.php:1015
msgid "Migrating leads. Step 1/3 Migrating entry headers. %d rows remaining."
msgstr ""

#. translators: %s: the database error
#. translators: %s: the database error
#: includes/class-gf-upgrade.php:1058
#: includes/class-gf-upgrade.php:1083
msgid "Error Migrating Entry Details: %s"
msgstr ""

#: includes/class-gf-upgrade.php:1097
msgid "Migrating leads. Step 2/3 Migrating entry details. %d rows remaining."
msgstr ""

#. translators: %s: the database error
#. translators: %s: the database error
#: includes/class-gf-upgrade.php:1143
#: includes/class-gf-upgrade.php:1167
msgid "Error Migrating Entry Meta: %s"
msgstr ""

#: includes/class-gf-upgrade.php:1183
msgid "Migrating leads. Step 3/3 Migrating entry meta. %d rows remaining."
msgstr ""

#: includes/class-gf-upgrade.php:1202
msgid "Migrating incomplete submissions."
msgstr ""

#. translators: %s: the database error
#: includes/class-gf-upgrade.php:1229
msgid "Error Migrating incomplete submissions: %s"
msgstr ""

#: includes/class-gf-upgrade.php:1244
msgid "Migrating entry notes."
msgstr ""

#: includes/class-gf-upgrade.php:1746
msgid "There appears to be an issue with one of the Gravity Forms database tables. Please get in touch with support."
msgstr ""

#: includes/class-gf-upgrade.php:1787
msgid "There appears to be an issue with the data in the Gravity Forms database tables. Please get in touch with support."
msgstr ""

#. translators: %s: the add-on name
#: includes/class-gf-upgrade.php:2153
msgid "The %s is not compatible with this version of Gravity Forms. See the plugins list for further details."
msgstr ""

#. translators: %d: the number of outdated add-ons
#: includes/class-gf-upgrade.php:2156
msgid "There are %d add-ons installed that are not compatible with this version of Gravity Forms. See the plugins list for further details."
msgstr ""

#: includes/class-personal-data.php:80
msgid "General Settings"
msgstr ""

#: includes/class-personal-data.php:85
msgid "Prevent the storage of IP addresses during form submission"
msgstr ""

#: includes/class-personal-data.php:91
#: tooltips.php:167
msgid "Retention Policy"
msgstr ""

#: includes/class-personal-data.php:96
msgid "Retain entries indefinitely"
msgstr ""

#: includes/class-personal-data.php:100
msgid "Trash entries automatically"
msgstr ""

#: includes/class-personal-data.php:104
#: includes/class-personal-data.php:112
msgid "Warning: this will affect all entries that are older than the number of days specified."
msgstr ""

#: includes/class-personal-data.php:108
msgid "Delete entries permanently automatically"
msgstr ""

#: includes/class-personal-data.php:119
msgid "Number of days to retain entries before trashing/deleting:"
msgstr ""

#: includes/class-personal-data.php:136
msgid "Form entries must be retained for at least one day."
msgstr ""

#: includes/class-personal-data.php:145
msgid "Exporting and Erasing Data"
msgstr ""

#: includes/class-personal-data.php:150
msgid "Enable integration with the WordPress tools for exporting and erasing personal data."
msgstr ""

#: includes/class-personal-data.php:155
msgid "You must add an email address field to the form in order to enable this setting."
msgstr ""

#: includes/class-personal-data.php:161
msgid "Identification Field"
msgstr ""

#: includes/class-personal-data.php:257
msgid "Created By"
msgstr ""

#: includes/class-personal-data.php:304
msgid "Fields"
msgstr ""

#: includes/class-personal-data.php:305
msgid "Export"
msgstr ""

#: includes/class-personal-data.php:306
msgid "Erase"
msgstr ""

#: includes/class-personal-data.php:324
msgid "Select/Deselect All"
msgstr ""

#: includes/class-personal-data.php:430
msgid "Other Data"
msgstr ""

#: includes/class-personal-data.php:686
msgid "Browser details"
msgstr ""

#: includes/class-personal-data.php:986
msgid "Draft Forms (Save and Continue Later)"
msgstr ""

#. translators: deleted text
#: includes/class-personal-data.php:1295
msgid "[deleted]"
msgstr ""

#: includes/config/items/class-gf-config-admin-i18n.php:44
#: includes/embed-form/config/class-gf-embed-config-i18n.php:45
#: includes/templates/edit-shortcode-form.tpl.php:18
msgid "Insert Form"
msgstr ""

#: includes/config/items/class-gf-config-block-editor.php:31
msgid "Add Block To Page"
msgstr ""

#: includes/config/items/class-gf-config-block-editor.php:32
msgid "Click or drag the Gravity Forms Block into the page to insert the form you selected. %1$sLearn More.%2$s"
msgstr ""

#: includes/config/items/class-gf-config-i18n.php:28
msgid "Mo"
msgstr ""

#: includes/config/items/class-gf-config-i18n.php:29
msgid "Tu"
msgstr ""

#: includes/config/items/class-gf-config-i18n.php:30
msgid "We"
msgstr ""

#: includes/config/items/class-gf-config-i18n.php:31
msgid "Th"
msgstr ""

#: includes/config/items/class-gf-config-i18n.php:32
msgid "Fr"
msgstr ""

#: includes/config/items/class-gf-config-i18n.php:33
msgid "Sa"
msgstr ""

#: includes/config/items/class-gf-config-i18n.php:34
msgid "Su"
msgstr ""

#: includes/config/items/class-gf-config-i18n.php:54
msgid "Select date"
msgstr ""

#: includes/config/items/class-gf-config-multifile.php:26
msgid "This type of file is not allowed. Must be one of the following: "
msgstr ""

#: includes/config/items/class-gf-config-multifile.php:27
#: includes/fields/class-gf-field-fileupload.php:439
msgid "Delete this file"
msgstr ""

#: includes/config/items/class-gf-config-multifile.php:28
msgid "in progress"
msgstr ""

#: includes/config/items/class-gf-config-multifile.php:29
msgid "File exceeds size limit"
msgstr ""

#: includes/config/items/class-gf-config-multifile.php:30
msgid "This type of file is not allowed."
msgstr ""

#: includes/config/items/class-gf-config-multifile.php:31
msgid "Maximum number of files reached"
msgstr ""

#: includes/config/items/class-gf-config-multifile.php:32
msgid "There was a problem while saving the file on the server"
msgstr ""

#: includes/config/items/class-gf-config-multifile.php:33
msgid "Please wait for the uploading to complete"
msgstr ""

#: includes/config/items/class-gf-config-multifile.php:35
msgid "Cancel this upload"
msgstr ""

#: includes/editor-button/config/class-gf-editor-config.php:40
msgid "Editor Preferences"
msgstr ""

#: includes/editor-button/config/class-gf-editor-config.php:41
msgid "Close button"
msgstr ""

#: includes/editor-button/config/class-gf-editor-config.php:42
msgid "Change options related to the form editor."
msgstr ""

#: includes/editor-button/config/class-gf-editor-config.php:43
msgid "Compact View"
msgstr ""

#: includes/editor-button/config/class-gf-editor-config.php:44
msgid "Simplify the preview of form fields for a more streamlined editing experience."
msgstr ""

#: includes/editor-button/config/class-gf-editor-config.php:45
msgid "Show Field IDs"
msgstr ""

#: includes/editor-button/config/class-gf-editor-config.php:46
msgid "Show the ID of each field in Compact View."
msgstr ""

#: includes/editor-button/config/class-gf-editor-config.php:57
msgid "Dropdown menu button"
msgstr ""

#: includes/editor-button/dom/class-gf-editor-button.php:22
#: includes/editor-button/dom/class-gf-editor-button.php:23
msgid "Open editor preferences"
msgstr ""

#: includes/embed-form/config/class-gf-embed-config-i18n.php:38
msgid "Embed Form"
msgstr ""

#: includes/embed-form/config/class-gf-embed-config-i18n.php:39
#: assets/js/src/admin/block-editor/blocks/form/edit.js:759
msgid "Form ID: %s"
msgstr ""

#: includes/embed-form/config/class-gf-embed-config-i18n.php:40
msgid "Add to Existing Content"
msgstr ""

#: includes/embed-form/config/class-gf-embed-config-i18n.php:41
msgid "%1$sAdd to Existing Content:%2$s %3$s"
msgstr ""

#. Translators: singular post type name (e.g. 'post').
#: includes/embed-form/config/class-gf-embed-config-i18n.php:42
#: includes/settings/fields/class-post-select.php:139
msgid "Select a %s"
msgstr ""

#: includes/embed-form/config/class-gf-embed-config-i18n.php:43
msgid "Select a post"
msgstr ""

#: includes/embed-form/config/class-gf-embed-config-i18n.php:44
msgid "Search all %ss"
msgstr ""

#: includes/embed-form/config/class-gf-embed-config-i18n.php:46
msgid "Create New"
msgstr ""

#: includes/embed-form/config/class-gf-embed-config-i18n.php:47
msgid "%1$sCreate New:%2$s %3$s"
msgstr ""

#: includes/embed-form/config/class-gf-embed-config-i18n.php:48
msgid "Enter %s Name"
msgstr ""

#: includes/embed-form/config/class-gf-embed-config-i18n.php:49
msgid "Create"
msgstr ""

#: includes/embed-form/config/class-gf-embed-config-i18n.php:50
msgid "Unsaved Changes"
msgstr ""

#: includes/embed-form/config/class-gf-embed-config-i18n.php:51
msgid "Oops! You have unsaved changes in the form, before you can continue with embedding it please save your changes."
msgstr ""

#: includes/embed-form/config/class-gf-embed-config-i18n.php:52
msgid "Save Changes"
msgstr ""

#: includes/embed-form/config/class-gf-embed-config-i18n.php:55
msgid "Close this dialog and return to form editor."
msgstr ""

#: includes/embed-form/config/class-gf-embed-config-i18n.php:56
msgid "Not Using the Block Editor?"
msgstr ""

#: includes/embed-form/config/class-gf-embed-config-i18n.php:57
msgid "Copy and paste the shortcode within your page builder."
msgstr ""

#: includes/embed-form/config/class-gf-embed-config-i18n.php:58
msgid "Copy Shortcode"
msgstr ""

#: includes/embed-form/config/class-gf-embed-config-i18n.php:59
msgid "Copied"
msgstr ""

#: includes/embed-form/config/class-gf-embed-config-i18n.php:60
msgid "%1$sLearn more%2$s about the shortcode."
msgstr ""

#: includes/embed-form/config/class-gf-embed-config.php:180
msgid "Post"
msgstr ""

#: includes/embed-form/dom/class-gf-embed-button.php:21
msgid "Embed"
msgstr ""

#: includes/fields/class-gf-field-address.php:48
msgid "Allows users to enter a physical address."
msgstr ""

#: includes/fields/class-gf-field-address.php:183
msgid "Zip Code"
msgstr ""

#: includes/fields/class-gf-field-address.php:205
#: includes/template-library/templates/templates.php:603
#: includes/template-library/templates/templates.php:2760
#: includes/template-library/templates/templates.php:2847
#: includes/template-library/templates/templates.php:3675
#: includes/template-library/templates/templates.php:3762
#: includes/template-library/templates/templates.php:4538
#: includes/template-library/templates/templates.php:4625
#: includes/template-library/templates/templates.php:5240
#: includes/template-library/templates/templates.php:6481
#: includes/template-library/templates/templates.php:7737
#: includes/template-library/templates/templates.php:8864
#: js.php:730
msgid "Street Address"
msgstr ""

#: includes/fields/class-gf-field-address.php:207
#: includes/template-library/templates/templates.php:609
#: includes/template-library/templates/templates.php:2766
#: includes/template-library/templates/templates.php:2853
#: includes/template-library/templates/templates.php:3681
#: includes/template-library/templates/templates.php:3768
#: includes/template-library/templates/templates.php:4544
#: includes/template-library/templates/templates.php:4631
#: includes/template-library/templates/templates.php:5246
#: includes/template-library/templates/templates.php:6487
#: includes/template-library/templates/templates.php:7742
#: includes/template-library/templates/templates.php:8870
#: js.php:735
msgid "Address Line 2"
msgstr ""

#: includes/fields/class-gf-field-address.php:466
#: includes/fields/class-gf-field-phone.php:339
msgid "International"
msgstr ""

#: includes/fields/class-gf-field-address.php:467
#: includes/template-library/templates/templates.php:2784
#: includes/template-library/templates/templates.php:2871
#: includes/template-library/templates/templates.php:3699
#: includes/template-library/templates/templates.php:3786
#: includes/template-library/templates/templates.php:4562
#: includes/template-library/templates/templates.php:4649
#: includes/template-library/templates/templates.php:5264
#: includes/template-library/templates/templates.php:6505
#: includes/template-library/templates/templates.php:7757
#: includes/template-library/templates/templates.php:8888
#: js.php:750
msgid "ZIP / Postal Code"
msgstr ""

#: includes/fields/class-gf-field-address.php:468
msgid "State / Province / Region"
msgstr ""

#: includes/fields/class-gf-field-address.php:471
#: includes/fields/class-gf-field-address.php:853
msgid "United States"
msgstr ""

#: includes/fields/class-gf-field-address.php:472
msgid "ZIP Code"
msgstr ""

#: includes/fields/class-gf-field-address.php:478
msgid "Canadian"
msgstr ""

#: includes/fields/class-gf-field-address.php:480
msgid "Province"
msgstr ""

#: includes/fields/class-gf-field-address.php:618
msgid "Afghanistan"
msgstr ""

#: includes/fields/class-gf-field-address.php:619
msgid "Åland Islands"
msgstr ""

#: includes/fields/class-gf-field-address.php:620
msgid "Albania"
msgstr ""

#: includes/fields/class-gf-field-address.php:621
msgid "Algeria"
msgstr ""

#: includes/fields/class-gf-field-address.php:622
#: includes/fields/class-gf-field-address.php:919
#: includes/fields/class-gf-field-address.php:993
msgid "American Samoa"
msgstr ""

#: includes/fields/class-gf-field-address.php:623
msgid "Andorra"
msgstr ""

#: includes/fields/class-gf-field-address.php:624
msgid "Angola"
msgstr ""

#: includes/fields/class-gf-field-address.php:625
msgid "Anguilla"
msgstr ""

#: includes/fields/class-gf-field-address.php:627
msgid "Antigua and Barbuda"
msgstr ""

#: includes/fields/class-gf-field-address.php:628
msgid "Argentina"
msgstr ""

#: includes/fields/class-gf-field-address.php:629
msgid "Armenia"
msgstr ""

#: includes/fields/class-gf-field-address.php:630
msgid "Aruba"
msgstr ""

#: includes/fields/class-gf-field-address.php:632
msgid "Austria"
msgstr ""

#: includes/fields/class-gf-field-address.php:633
msgid "Azerbaijan"
msgstr ""

#: includes/fields/class-gf-field-address.php:634
msgid "Bahamas"
msgstr ""

#: includes/fields/class-gf-field-address.php:635
msgid "Bahrain"
msgstr ""

#: includes/fields/class-gf-field-address.php:636
msgid "Bangladesh"
msgstr ""

#: includes/fields/class-gf-field-address.php:637
msgid "Barbados"
msgstr ""

#: includes/fields/class-gf-field-address.php:638
msgid "Belarus"
msgstr ""

#: includes/fields/class-gf-field-address.php:639
msgid "Belgium"
msgstr ""

#: includes/fields/class-gf-field-address.php:640
msgid "Belize"
msgstr ""

#: includes/fields/class-gf-field-address.php:641
msgid "Benin"
msgstr ""

#: includes/fields/class-gf-field-address.php:642
msgid "Bermuda"
msgstr ""

#: includes/fields/class-gf-field-address.php:643
msgid "Bhutan"
msgstr ""

#: includes/fields/class-gf-field-address.php:644
msgid "Bolivia"
msgstr ""

#: includes/fields/class-gf-field-address.php:645
msgid "Bonaire, Sint Eustatius and Saba"
msgstr ""

#: includes/fields/class-gf-field-address.php:646
msgid "Bosnia and Herzegovina"
msgstr ""

#: includes/fields/class-gf-field-address.php:647
msgid "Botswana"
msgstr ""

#: includes/fields/class-gf-field-address.php:648
msgid "Bouvet Island"
msgstr ""

#: includes/fields/class-gf-field-address.php:649
msgid "Brazil"
msgstr ""

#: includes/fields/class-gf-field-address.php:650
msgid "British Indian Ocean Territory"
msgstr ""

#: includes/fields/class-gf-field-address.php:651
msgid "Brunei Darussalam"
msgstr ""

#: includes/fields/class-gf-field-address.php:652
msgid "Bulgaria"
msgstr ""

#: includes/fields/class-gf-field-address.php:653
msgid "Burkina Faso"
msgstr ""

#: includes/fields/class-gf-field-address.php:654
msgid "Burundi"
msgstr ""

#: includes/fields/class-gf-field-address.php:655
msgid "Cabo Verde"
msgstr ""

#: includes/fields/class-gf-field-address.php:656
msgid "Cambodia"
msgstr ""

#: includes/fields/class-gf-field-address.php:657
msgid "Cameroon"
msgstr ""

#: includes/fields/class-gf-field-address.php:658
msgid "Canada"
msgstr ""

#: includes/fields/class-gf-field-address.php:659
msgid "Cayman Islands"
msgstr ""

#: includes/fields/class-gf-field-address.php:660
msgid "Central African Republic"
msgstr ""

#: includes/fields/class-gf-field-address.php:661
msgid "Chad"
msgstr ""

#: includes/fields/class-gf-field-address.php:662
msgid "Chile"
msgstr ""

#: includes/fields/class-gf-field-address.php:663
msgid "China"
msgstr ""

#: includes/fields/class-gf-field-address.php:664
msgid "Christmas Island"
msgstr ""

#: includes/fields/class-gf-field-address.php:665
msgid "Cocos Islands"
msgstr ""

#: includes/fields/class-gf-field-address.php:666
msgid "Colombia"
msgstr ""

#: includes/fields/class-gf-field-address.php:667
msgid "Comoros"
msgstr ""

#: includes/fields/class-gf-field-address.php:668
msgid "Congo, Democratic Republic of the"
msgstr ""

#: includes/fields/class-gf-field-address.php:669
msgid "Congo"
msgstr ""

#: includes/fields/class-gf-field-address.php:670
msgid "Cook Islands"
msgstr ""

#: includes/fields/class-gf-field-address.php:671
msgid "Costa Rica"
msgstr ""

#: includes/fields/class-gf-field-address.php:672
msgid "Côte d'Ivoire"
msgstr ""

#: includes/fields/class-gf-field-address.php:673
msgid "Croatia"
msgstr ""

#: includes/fields/class-gf-field-address.php:674
msgid "Cuba"
msgstr ""

#: includes/fields/class-gf-field-address.php:675
msgid "Curaçao"
msgstr ""

#: includes/fields/class-gf-field-address.php:676
msgid "Cyprus"
msgstr ""

#: includes/fields/class-gf-field-address.php:677
msgid "Czechia"
msgstr ""

#: includes/fields/class-gf-field-address.php:678
msgid "Denmark"
msgstr ""

#: includes/fields/class-gf-field-address.php:679
msgid "Djibouti"
msgstr ""

#: includes/fields/class-gf-field-address.php:680
msgid "Dominica"
msgstr ""

#: includes/fields/class-gf-field-address.php:681
msgid "Dominican Republic"
msgstr ""

#: includes/fields/class-gf-field-address.php:682
msgid "Ecuador"
msgstr ""

#: includes/fields/class-gf-field-address.php:683
msgid "Egypt"
msgstr ""

#: includes/fields/class-gf-field-address.php:684
msgid "El Salvador"
msgstr ""

#: includes/fields/class-gf-field-address.php:685
msgid "Equatorial Guinea"
msgstr ""

#: includes/fields/class-gf-field-address.php:686
msgid "Eritrea"
msgstr ""

#: includes/fields/class-gf-field-address.php:687
msgid "Estonia"
msgstr ""

#: includes/fields/class-gf-field-address.php:688
msgid "Eswatini"
msgstr ""

#: includes/fields/class-gf-field-address.php:689
msgid "Ethiopia"
msgstr ""

#: includes/fields/class-gf-field-address.php:690
msgid "Falkland Islands"
msgstr ""

#: includes/fields/class-gf-field-address.php:691
msgid "Faroe Islands"
msgstr ""

#: includes/fields/class-gf-field-address.php:692
msgid "Fiji"
msgstr ""

#: includes/fields/class-gf-field-address.php:693
msgid "Finland"
msgstr ""

#: includes/fields/class-gf-field-address.php:694
msgid "France"
msgstr ""

#: includes/fields/class-gf-field-address.php:695
msgid "French Guiana"
msgstr ""

#: includes/fields/class-gf-field-address.php:696
msgid "French Polynesia"
msgstr ""

#: includes/fields/class-gf-field-address.php:697
msgid "French Southern Territories"
msgstr ""

#: includes/fields/class-gf-field-address.php:698
msgid "Gabon"
msgstr ""

#: includes/fields/class-gf-field-address.php:699
msgid "Gambia"
msgstr ""

#: includes/fields/class-gf-field-address.php:700
msgctxt "Country"
msgid "Georgia"
msgstr ""

#: includes/fields/class-gf-field-address.php:701
msgid "Germany"
msgstr ""

#: includes/fields/class-gf-field-address.php:702
msgid "Ghana"
msgstr ""

#: includes/fields/class-gf-field-address.php:703
msgid "Gibraltar"
msgstr ""

#: includes/fields/class-gf-field-address.php:704
msgid "Greece"
msgstr ""

#: includes/fields/class-gf-field-address.php:705
msgid "Greenland"
msgstr ""

#: includes/fields/class-gf-field-address.php:706
msgid "Grenada"
msgstr ""

#: includes/fields/class-gf-field-address.php:707
msgid "Guadeloupe"
msgstr ""

#: includes/fields/class-gf-field-address.php:708
#: includes/fields/class-gf-field-address.php:929
#: includes/fields/class-gf-field-address.php:1003
msgid "Guam"
msgstr ""

#: includes/fields/class-gf-field-address.php:709
msgid "Guatemala"
msgstr ""

#: includes/fields/class-gf-field-address.php:710
msgid "Guernsey"
msgstr ""

#: includes/fields/class-gf-field-address.php:711
msgid "Guinea"
msgstr ""

#: includes/fields/class-gf-field-address.php:712
msgid "Guinea-Bissau"
msgstr ""

#: includes/fields/class-gf-field-address.php:713
msgid "Guyana"
msgstr ""

#: includes/fields/class-gf-field-address.php:714
msgid "Haiti"
msgstr ""

#: includes/fields/class-gf-field-address.php:715
msgid "Heard Island and McDonald Islands"
msgstr ""

#: includes/fields/class-gf-field-address.php:716
msgid "Holy See"
msgstr ""

#: includes/fields/class-gf-field-address.php:717
msgid "Honduras"
msgstr ""

#: includes/fields/class-gf-field-address.php:718
msgid "Hong Kong"
msgstr ""

#: includes/fields/class-gf-field-address.php:719
msgid "Hungary"
msgstr ""

#: includes/fields/class-gf-field-address.php:720
msgid "Iceland"
msgstr ""

#: includes/fields/class-gf-field-address.php:721
msgid "India"
msgstr ""

#: includes/fields/class-gf-field-address.php:722
msgid "Indonesia"
msgstr ""

#: includes/fields/class-gf-field-address.php:723
msgid "Iran"
msgstr ""

#: includes/fields/class-gf-field-address.php:724
msgid "Iraq"
msgstr ""

#: includes/fields/class-gf-field-address.php:725
msgid "Ireland"
msgstr ""

#: includes/fields/class-gf-field-address.php:726
msgid "Isle of Man"
msgstr ""

#: includes/fields/class-gf-field-address.php:727
msgid "Israel"
msgstr ""

#: includes/fields/class-gf-field-address.php:728
msgid "Italy"
msgstr ""

#: includes/fields/class-gf-field-address.php:729
msgid "Jamaica"
msgstr ""

#: includes/fields/class-gf-field-address.php:730
msgid "Japan"
msgstr ""

#: includes/fields/class-gf-field-address.php:731
msgid "Jersey"
msgstr ""

#: includes/fields/class-gf-field-address.php:732
msgid "Jordan"
msgstr ""

#: includes/fields/class-gf-field-address.php:733
msgid "Kazakhstan"
msgstr ""

#: includes/fields/class-gf-field-address.php:734
msgid "Kenya"
msgstr ""

#: includes/fields/class-gf-field-address.php:735
msgid "Kiribati"
msgstr ""

#: includes/fields/class-gf-field-address.php:736
msgid "Korea, Democratic People's Republic of"
msgstr ""

#: includes/fields/class-gf-field-address.php:737
msgid "Korea, Republic of"
msgstr ""

#: includes/fields/class-gf-field-address.php:738
msgid "Kuwait"
msgstr ""

#: includes/fields/class-gf-field-address.php:739
msgid "Kyrgyzstan"
msgstr ""

#: includes/fields/class-gf-field-address.php:740
msgid "Lao People's Democratic Republic"
msgstr ""

#: includes/fields/class-gf-field-address.php:741
msgid "Latvia"
msgstr ""

#: includes/fields/class-gf-field-address.php:742
msgid "Lebanon"
msgstr ""

#: includes/fields/class-gf-field-address.php:743
msgid "Lesotho"
msgstr ""

#: includes/fields/class-gf-field-address.php:744
msgid "Liberia"
msgstr ""

#: includes/fields/class-gf-field-address.php:745
msgid "Libya"
msgstr ""

#: includes/fields/class-gf-field-address.php:746
msgid "Liechtenstein"
msgstr ""

#: includes/fields/class-gf-field-address.php:747
msgid "Lithuania"
msgstr ""

#: includes/fields/class-gf-field-address.php:748
msgid "Luxembourg"
msgstr ""

#: includes/fields/class-gf-field-address.php:749
msgid "Macao"
msgstr ""

#: includes/fields/class-gf-field-address.php:750
msgid "Madagascar"
msgstr ""

#: includes/fields/class-gf-field-address.php:751
msgid "Malawi"
msgstr ""

#: includes/fields/class-gf-field-address.php:752
msgid "Malaysia"
msgstr ""

#: includes/fields/class-gf-field-address.php:753
msgid "Maldives"
msgstr ""

#: includes/fields/class-gf-field-address.php:754
msgid "Mali"
msgstr ""

#: includes/fields/class-gf-field-address.php:755
msgid "Malta"
msgstr ""

#: includes/fields/class-gf-field-address.php:756
msgid "Marshall Islands"
msgstr ""

#: includes/fields/class-gf-field-address.php:757
msgid "Martinique"
msgstr ""

#: includes/fields/class-gf-field-address.php:758
msgid "Mauritania"
msgstr ""

#: includes/fields/class-gf-field-address.php:759
msgid "Mauritius"
msgstr ""

#: includes/fields/class-gf-field-address.php:760
msgid "Mayotte"
msgstr ""

#: includes/fields/class-gf-field-address.php:761
msgid "Mexico"
msgstr ""

#: includes/fields/class-gf-field-address.php:762
msgid "Micronesia"
msgstr ""

#: includes/fields/class-gf-field-address.php:763
msgid "Moldova"
msgstr ""

#: includes/fields/class-gf-field-address.php:764
msgid "Monaco"
msgstr ""

#: includes/fields/class-gf-field-address.php:765
msgid "Mongolia"
msgstr ""

#: includes/fields/class-gf-field-address.php:766
msgid "Montenegro"
msgstr ""

#: includes/fields/class-gf-field-address.php:767
msgid "Montserrat"
msgstr ""

#: includes/fields/class-gf-field-address.php:768
msgid "Morocco"
msgstr ""

#: includes/fields/class-gf-field-address.php:769
msgid "Mozambique"
msgstr ""

#: includes/fields/class-gf-field-address.php:770
msgid "Myanmar"
msgstr ""

#: includes/fields/class-gf-field-address.php:771
msgid "Namibia"
msgstr ""

#: includes/fields/class-gf-field-address.php:772
msgid "Nauru"
msgstr ""

#: includes/fields/class-gf-field-address.php:773
msgid "Nepal"
msgstr ""

#: includes/fields/class-gf-field-address.php:774
msgid "Netherlands"
msgstr ""

#: includes/fields/class-gf-field-address.php:775
msgid "New Caledonia"
msgstr ""

#: includes/fields/class-gf-field-address.php:776
msgid "New Zealand"
msgstr ""

#: includes/fields/class-gf-field-address.php:777
msgid "Nicaragua"
msgstr ""

#: includes/fields/class-gf-field-address.php:778
msgid "Niger"
msgstr ""

#: includes/fields/class-gf-field-address.php:779
msgid "Nigeria"
msgstr ""

#: includes/fields/class-gf-field-address.php:780
msgid "Niue"
msgstr ""

#: includes/fields/class-gf-field-address.php:781
msgid "Norfolk Island"
msgstr ""

#: includes/fields/class-gf-field-address.php:782
msgid "North Macedonia"
msgstr ""

#: includes/fields/class-gf-field-address.php:783
#: includes/fields/class-gf-field-address.php:954
#: includes/fields/class-gf-field-address.php:1028
msgid "Northern Mariana Islands"
msgstr ""

#: includes/fields/class-gf-field-address.php:784
msgid "Norway"
msgstr ""

#: includes/fields/class-gf-field-address.php:785
msgid "Oman"
msgstr ""

#: includes/fields/class-gf-field-address.php:786
msgid "Pakistan"
msgstr ""

#: includes/fields/class-gf-field-address.php:787
msgid "Palau"
msgstr ""

#: includes/fields/class-gf-field-address.php:788
msgid "Palestine, State of"
msgstr ""

#: includes/fields/class-gf-field-address.php:789
msgid "Panama"
msgstr ""

#: includes/fields/class-gf-field-address.php:790
msgid "Papua New Guinea"
msgstr ""

#: includes/fields/class-gf-field-address.php:791
msgid "Paraguay"
msgstr ""

#: includes/fields/class-gf-field-address.php:792
msgid "Peru"
msgstr ""

#: includes/fields/class-gf-field-address.php:793
msgid "Philippines"
msgstr ""

#: includes/fields/class-gf-field-address.php:794
msgid "Pitcairn"
msgstr ""

#: includes/fields/class-gf-field-address.php:795
msgid "Poland"
msgstr ""

#: includes/fields/class-gf-field-address.php:796
msgid "Portugal"
msgstr ""

#: includes/fields/class-gf-field-address.php:797
#: includes/fields/class-gf-field-address.php:959
#: includes/fields/class-gf-field-address.php:1033
msgid "Puerto Rico"
msgstr ""

#: includes/fields/class-gf-field-address.php:798
msgid "Qatar"
msgstr ""

#: includes/fields/class-gf-field-address.php:799
msgid "Réunion"
msgstr ""

#: includes/fields/class-gf-field-address.php:800
msgid "Romania"
msgstr ""

#: includes/fields/class-gf-field-address.php:801
msgid "Russian Federation"
msgstr ""

#: includes/fields/class-gf-field-address.php:802
msgid "Rwanda"
msgstr ""

#: includes/fields/class-gf-field-address.php:803
msgid "Saint Barthélemy"
msgstr ""

#: includes/fields/class-gf-field-address.php:804
msgid "Saint Helena, Ascension and Tristan da Cunha"
msgstr ""

#: includes/fields/class-gf-field-address.php:805
msgid "Saint Kitts and Nevis"
msgstr ""

#: includes/fields/class-gf-field-address.php:806
msgid "Saint Lucia"
msgstr ""

#: includes/fields/class-gf-field-address.php:807
msgid "Saint Martin"
msgstr ""

#: includes/fields/class-gf-field-address.php:808
msgid "Saint Pierre and Miquelon"
msgstr ""

#: includes/fields/class-gf-field-address.php:809
msgid "Saint Vincent and the Grenadines"
msgstr ""

#: includes/fields/class-gf-field-address.php:810
msgid "Samoa"
msgstr ""

#: includes/fields/class-gf-field-address.php:811
msgid "San Marino"
msgstr ""

#: includes/fields/class-gf-field-address.php:812
msgid "Sao Tome and Principe"
msgstr ""

#: includes/fields/class-gf-field-address.php:813
msgid "Saudi Arabia"
msgstr ""

#: includes/fields/class-gf-field-address.php:814
msgid "Senegal"
msgstr ""

#: includes/fields/class-gf-field-address.php:815
msgid "Serbia"
msgstr ""

#: includes/fields/class-gf-field-address.php:816
msgid "Seychelles"
msgstr ""

#: includes/fields/class-gf-field-address.php:817
msgid "Sierra Leone"
msgstr ""

#: includes/fields/class-gf-field-address.php:818
msgid "Singapore"
msgstr ""

#: includes/fields/class-gf-field-address.php:819
msgid "Sint Maarten"
msgstr ""

#: includes/fields/class-gf-field-address.php:820
msgid "Slovakia"
msgstr ""

#: includes/fields/class-gf-field-address.php:821
msgid "Slovenia"
msgstr ""

#: includes/fields/class-gf-field-address.php:822
msgid "Solomon Islands"
msgstr ""

#: includes/fields/class-gf-field-address.php:823
msgid "Somalia"
msgstr ""

#: includes/fields/class-gf-field-address.php:824
msgid "South Africa"
msgstr ""

#: includes/fields/class-gf-field-address.php:825
msgctxt "Country"
msgid "South Georgia and the South Sandwich Islands"
msgstr ""

#: includes/fields/class-gf-field-address.php:826
msgid "South Sudan"
msgstr ""

#: includes/fields/class-gf-field-address.php:827
msgid "Spain"
msgstr ""

#: includes/fields/class-gf-field-address.php:828
msgid "Sri Lanka"
msgstr ""

#: includes/fields/class-gf-field-address.php:829
msgid "Sudan"
msgstr ""

#: includes/fields/class-gf-field-address.php:830
msgid "Suriname"
msgstr ""

#: includes/fields/class-gf-field-address.php:831
msgid "Svalbard and Jan Mayen"
msgstr ""

#: includes/fields/class-gf-field-address.php:832
msgid "Sweden"
msgstr ""

#: includes/fields/class-gf-field-address.php:833
msgid "Switzerland"
msgstr ""

#: includes/fields/class-gf-field-address.php:834
msgid "Syria Arab Republic"
msgstr ""

#: includes/fields/class-gf-field-address.php:835
msgid "Taiwan"
msgstr ""

#: includes/fields/class-gf-field-address.php:836
msgid "Tajikistan"
msgstr ""

#: includes/fields/class-gf-field-address.php:837
msgid "Tanzania, the United Republic of"
msgstr ""

#: includes/fields/class-gf-field-address.php:838
msgid "Thailand"
msgstr ""

#: includes/fields/class-gf-field-address.php:839
msgid "Timor-Leste"
msgstr ""

#: includes/fields/class-gf-field-address.php:840
msgid "Togo"
msgstr ""

#: includes/fields/class-gf-field-address.php:841
msgid "Tokelau"
msgstr ""

#: includes/fields/class-gf-field-address.php:842
msgid "Tonga"
msgstr ""

#: includes/fields/class-gf-field-address.php:843
msgid "Trinidad and Tobago"
msgstr ""

#: includes/fields/class-gf-field-address.php:844
msgid "Tunisia"
msgstr ""

#: includes/fields/class-gf-field-address.php:845
msgid "Türkiye"
msgstr ""

#: includes/fields/class-gf-field-address.php:846
msgid "Turkmenistan"
msgstr ""

#: includes/fields/class-gf-field-address.php:847
msgid "Turks and Caicos Islands"
msgstr ""

#: includes/fields/class-gf-field-address.php:848
msgid "Tuvalu"
msgstr ""

#: includes/fields/class-gf-field-address.php:849
msgid "Uganda"
msgstr ""

#: includes/fields/class-gf-field-address.php:850
msgid "Ukraine"
msgstr ""

#: includes/fields/class-gf-field-address.php:851
msgid "United Arab Emirates"
msgstr ""

#: includes/fields/class-gf-field-address.php:852
msgid "United Kingdom"
msgstr ""

#: includes/fields/class-gf-field-address.php:854
msgid "Uruguay"
msgstr ""

#: includes/fields/class-gf-field-address.php:855
msgid "US Minor Outlying Islands"
msgstr ""

#: includes/fields/class-gf-field-address.php:856
msgid "Uzbekistan"
msgstr ""

#: includes/fields/class-gf-field-address.php:857
msgid "Vanuatu"
msgstr ""

#: includes/fields/class-gf-field-address.php:858
msgid "Venezuela"
msgstr ""

#: includes/fields/class-gf-field-address.php:859
msgid "Viet Nam"
msgstr ""

#: includes/fields/class-gf-field-address.php:860
msgid "Virgin Islands, British"
msgstr ""

#: includes/fields/class-gf-field-address.php:861
msgid "Virgin Islands, U.S."
msgstr ""

#: includes/fields/class-gf-field-address.php:862
msgid "Wallis and Futuna"
msgstr ""

#: includes/fields/class-gf-field-address.php:863
msgid "Western Sahara"
msgstr ""

#: includes/fields/class-gf-field-address.php:864
msgid "Yemen"
msgstr ""

#: includes/fields/class-gf-field-address.php:865
msgid "Zambia"
msgstr ""

#: includes/fields/class-gf-field-address.php:866
msgid "Zimbabwe"
msgstr ""

#: includes/fields/class-gf-field-address.php:917
#: includes/fields/class-gf-field-address.php:991
msgid "Alabama"
msgstr ""

#: includes/fields/class-gf-field-address.php:918
#: includes/fields/class-gf-field-address.php:992
msgid "Alaska"
msgstr ""

#: includes/fields/class-gf-field-address.php:920
#: includes/fields/class-gf-field-address.php:994
msgid "Arizona"
msgstr ""

#: includes/fields/class-gf-field-address.php:921
#: includes/fields/class-gf-field-address.php:995
msgid "Arkansas"
msgstr ""

#: includes/fields/class-gf-field-address.php:922
#: includes/fields/class-gf-field-address.php:996
msgid "California"
msgstr ""

#: includes/fields/class-gf-field-address.php:923
#: includes/fields/class-gf-field-address.php:997
msgid "Colorado"
msgstr ""

#: includes/fields/class-gf-field-address.php:924
#: includes/fields/class-gf-field-address.php:998
msgid "Connecticut"
msgstr ""

#: includes/fields/class-gf-field-address.php:925
#: includes/fields/class-gf-field-address.php:999
msgid "Delaware"
msgstr ""

#: includes/fields/class-gf-field-address.php:926
#: includes/fields/class-gf-field-address.php:1000
msgid "District of Columbia"
msgstr ""

#: includes/fields/class-gf-field-address.php:927
#: includes/fields/class-gf-field-address.php:1001
msgid "Florida"
msgstr ""

#: includes/fields/class-gf-field-address.php:928
#: includes/fields/class-gf-field-address.php:1002
msgctxt "US State"
msgid "Georgia"
msgstr ""

#: includes/fields/class-gf-field-address.php:930
#: includes/fields/class-gf-field-address.php:1004
msgid "Hawaii"
msgstr ""

#: includes/fields/class-gf-field-address.php:931
#: includes/fields/class-gf-field-address.php:1005
msgid "Idaho"
msgstr ""

#: includes/fields/class-gf-field-address.php:932
#: includes/fields/class-gf-field-address.php:1006
msgid "Illinois"
msgstr ""

#: includes/fields/class-gf-field-address.php:933
#: includes/fields/class-gf-field-address.php:1007
msgid "Indiana"
msgstr ""

#: includes/fields/class-gf-field-address.php:934
#: includes/fields/class-gf-field-address.php:1008
msgid "Iowa"
msgstr ""

#: includes/fields/class-gf-field-address.php:935
#: includes/fields/class-gf-field-address.php:1009
msgid "Kansas"
msgstr ""

#: includes/fields/class-gf-field-address.php:936
#: includes/fields/class-gf-field-address.php:1010
msgid "Kentucky"
msgstr ""

#: includes/fields/class-gf-field-address.php:937
#: includes/fields/class-gf-field-address.php:1011
msgid "Louisiana"
msgstr ""

#: includes/fields/class-gf-field-address.php:938
#: includes/fields/class-gf-field-address.php:1012
msgid "Maine"
msgstr ""

#: includes/fields/class-gf-field-address.php:939
#: includes/fields/class-gf-field-address.php:1013
msgid "Maryland"
msgstr ""

#: includes/fields/class-gf-field-address.php:940
#: includes/fields/class-gf-field-address.php:1014
msgid "Massachusetts"
msgstr ""

#: includes/fields/class-gf-field-address.php:941
#: includes/fields/class-gf-field-address.php:1015
msgid "Michigan"
msgstr ""

#: includes/fields/class-gf-field-address.php:942
#: includes/fields/class-gf-field-address.php:1016
msgid "Minnesota"
msgstr ""

#: includes/fields/class-gf-field-address.php:943
#: includes/fields/class-gf-field-address.php:1017
msgid "Mississippi"
msgstr ""

#: includes/fields/class-gf-field-address.php:944
#: includes/fields/class-gf-field-address.php:1018
msgid "Missouri"
msgstr ""

#: includes/fields/class-gf-field-address.php:945
#: includes/fields/class-gf-field-address.php:1019
msgid "Montana"
msgstr ""

#: includes/fields/class-gf-field-address.php:946
#: includes/fields/class-gf-field-address.php:1020
msgid "Nebraska"
msgstr ""

#: includes/fields/class-gf-field-address.php:947
#: includes/fields/class-gf-field-address.php:1021
msgid "Nevada"
msgstr ""

#: includes/fields/class-gf-field-address.php:948
#: includes/fields/class-gf-field-address.php:1022
msgid "New Hampshire"
msgstr ""

#: includes/fields/class-gf-field-address.php:949
#: includes/fields/class-gf-field-address.php:1023
msgid "New Jersey"
msgstr ""

#: includes/fields/class-gf-field-address.php:950
#: includes/fields/class-gf-field-address.php:1024
msgid "New Mexico"
msgstr ""

#: includes/fields/class-gf-field-address.php:951
#: includes/fields/class-gf-field-address.php:1025
msgid "New York"
msgstr ""

#: includes/fields/class-gf-field-address.php:952
#: includes/fields/class-gf-field-address.php:1026
msgid "North Carolina"
msgstr ""

#: includes/fields/class-gf-field-address.php:953
#: includes/fields/class-gf-field-address.php:1027
msgid "North Dakota"
msgstr ""

#: includes/fields/class-gf-field-address.php:955
#: includes/fields/class-gf-field-address.php:1029
msgid "Ohio"
msgstr ""

#: includes/fields/class-gf-field-address.php:956
#: includes/fields/class-gf-field-address.php:1030
msgid "Oklahoma"
msgstr ""

#: includes/fields/class-gf-field-address.php:957
#: includes/fields/class-gf-field-address.php:1031
msgid "Oregon"
msgstr ""

#: includes/fields/class-gf-field-address.php:958
#: includes/fields/class-gf-field-address.php:1032
msgid "Pennsylvania"
msgstr ""

#: includes/fields/class-gf-field-address.php:960
#: includes/fields/class-gf-field-address.php:1034
msgid "Rhode Island"
msgstr ""

#: includes/fields/class-gf-field-address.php:961
#: includes/fields/class-gf-field-address.php:1035
msgid "South Carolina"
msgstr ""

#: includes/fields/class-gf-field-address.php:962
#: includes/fields/class-gf-field-address.php:1036
msgid "South Dakota"
msgstr ""

#: includes/fields/class-gf-field-address.php:963
#: includes/fields/class-gf-field-address.php:1037
msgid "Tennessee"
msgstr ""

#: includes/fields/class-gf-field-address.php:964
#: includes/fields/class-gf-field-address.php:1038
msgid "Texas"
msgstr ""

#: includes/fields/class-gf-field-address.php:965
#: includes/fields/class-gf-field-address.php:1039
msgid "Utah"
msgstr ""

#: includes/fields/class-gf-field-address.php:966
#: includes/fields/class-gf-field-address.php:1040
msgid "U.S. Virgin Islands"
msgstr ""

#: includes/fields/class-gf-field-address.php:967
#: includes/fields/class-gf-field-address.php:1041
msgid "Vermont"
msgstr ""

#: includes/fields/class-gf-field-address.php:968
#: includes/fields/class-gf-field-address.php:1042
msgid "Virginia"
msgstr ""

#: includes/fields/class-gf-field-address.php:969
#: includes/fields/class-gf-field-address.php:1043
msgid "Washington"
msgstr ""

#: includes/fields/class-gf-field-address.php:970
#: includes/fields/class-gf-field-address.php:1044
msgid "West Virginia"
msgstr ""

#: includes/fields/class-gf-field-address.php:971
#: includes/fields/class-gf-field-address.php:1045
msgid "Wisconsin"
msgstr ""

#: includes/fields/class-gf-field-address.php:972
#: includes/fields/class-gf-field-address.php:1046
msgid "Wyoming"
msgstr ""

#: includes/fields/class-gf-field-address.php:973
#: includes/fields/class-gf-field-address.php:1047
msgid "Armed Forces Americas"
msgstr ""

#: includes/fields/class-gf-field-address.php:974
#: includes/fields/class-gf-field-address.php:1048
msgid "Armed Forces Europe"
msgstr ""

#: includes/fields/class-gf-field-address.php:975
#: includes/fields/class-gf-field-address.php:1049
msgid "Armed Forces Pacific"
msgstr ""

#: includes/fields/class-gf-field-address.php:1060
msgid "Alberta"
msgstr ""

#: includes/fields/class-gf-field-address.php:1061
msgid "British Columbia"
msgstr ""

#: includes/fields/class-gf-field-address.php:1062
msgid "Manitoba"
msgstr ""

#: includes/fields/class-gf-field-address.php:1063
msgid "New Brunswick"
msgstr ""

#: includes/fields/class-gf-field-address.php:1064
msgid "Newfoundland and Labrador"
msgstr ""

#: includes/fields/class-gf-field-address.php:1065
msgid "Northwest Territories"
msgstr ""

#: includes/fields/class-gf-field-address.php:1066
msgid "Nova Scotia"
msgstr ""

#: includes/fields/class-gf-field-address.php:1067
msgid "Nunavut"
msgstr ""

#: includes/fields/class-gf-field-address.php:1068
msgid "Ontario"
msgstr ""

#: includes/fields/class-gf-field-address.php:1069
msgid "Prince Edward Island"
msgstr ""

#: includes/fields/class-gf-field-address.php:1070
msgid "Quebec"
msgstr ""

#: includes/fields/class-gf-field-address.php:1071
msgid "Saskatchewan"
msgstr ""

#: includes/fields/class-gf-field-address.php:1072
msgid "Yukon"
msgstr ""

#: includes/fields/class-gf-field-calculation.php:35
#: includes/fields/class-gf-field-hiddenproduct.php:53
#: includes/fields/class-gf-field-number.php:132
#: includes/fields/class-gf-field-singleproduct.php:60
msgid "Please enter a valid quantity"
msgstr ""

#: includes/fields/class-gf-field-calculation.php:149
#: includes/fields/class-gf-field-hiddenproduct.php:116
#: includes/fields/class-gf-field-singleproduct.php:217
msgid "Qty: "
msgstr ""

#: includes/fields/class-gf-field-calculation.php:149
#: includes/fields/class-gf-field-hiddenproduct.php:120
#: includes/fields/class-gf-field-singleproduct.php:221
msgid "Price: "
msgstr ""

#: includes/fields/class-gf-field-captcha.php:52
#: js.php:870
msgid "CAPTCHA"
msgstr ""

#: includes/fields/class-gf-field-captcha.php:63
msgid "Adds a captcha field to your form to help protect your website from spam and bot abuse."
msgstr ""

#: includes/fields/class-gf-field-captcha.php:115
msgid "The reCAPTCHA v2 field is not supported in Conversational Forms and will be removed, but will continue to work as expected in other contexts."
msgstr ""

#. Translators: 1. Opening <a> tag with link to the Forms > Settings > reCAPTCHA page. 2. closing <a> tag.
#: includes/fields/class-gf-field-captcha.php:125
msgid "To use reCAPTCHA v2 you must configure the site and secret keys on the %1$sreCAPTCHA Settings%2$s page."
msgstr ""

#: includes/fields/class-gf-field-captcha.php:172
#: includes/fields/class-gf-field-captcha.php:208
msgid "The CAPTCHA wasn't entered correctly. Go back and try it again."
msgstr ""

#: includes/fields/class-gf-field-captcha.php:248
msgid "The reCAPTCHA was invalid. Go back and try it again."
msgstr ""

#: includes/fields/class-gf-field-captcha.php:431
msgid "To use the reCAPTCHA field you must do the following:"
msgstr ""

#: includes/fields/class-gf-field-captcha.php:431
msgid "Sign up%s for an API key pair for your site."
msgstr ""

#: includes/fields/class-gf-field-captcha.php:431
msgid "Enter your reCAPTCHA site and secret keys in the %sreCAPTCHA Settings%s."
msgstr ""

#: includes/fields/class-gf-field-captcha.php:435
msgid "An example of reCAPTCHA"
msgstr ""

#: includes/fields/class-gf-field-checkbox.php:47
msgid "Allows users to select one or many checkboxes."
msgstr ""

#: includes/fields/class-gf-field-checkbox.php:826
#: includes/fields/class-gf-field-radio.php:185
msgid "%d of %d items shown. Edit field to view all"
msgstr ""

#: includes/fields/class-gf-field-consent.php:90
#: includes/template-library/templates/templates.php:1588
#: includes/template-library/templates/templates.php:5967
#: includes/template-library/templates/templates.php:11207
#: js.php:974
#: js.php:975
msgid "Consent"
msgstr ""

#: includes/fields/class-gf-field-consent.php:101
msgid "Offers a “yes/no” consent checkbox and a detailed description of what is being consented to."
msgstr ""

#: includes/fields/class-gf-field-consent.php:480
#: includes/fields/class-gf-field-consent.php:551
#: includes/template-library/templates/templates.php:1608
#: includes/template-library/templates/templates.php:5987
#: includes/template-library/templates/templates.php:11227
#: js.php:985
msgid "Checked"
msgstr ""

#: includes/fields/class-gf-field-consent.php:480
msgid "Not Checked"
msgstr ""

#: includes/fields/class-gf-field-creditcard.php:13
#: includes/template-library/templates/templates.php:2157
#: includes/template-library/templates/templates.php:3042
#: includes/template-library/templates/templates.php:3092
#: includes/template-library/templates/templates.php:3951
#: includes/template-library/templates/templates.php:7128
#: includes/template-library/templates/templates.php:8015
#: js.php:763
msgid "Credit Card"
msgstr ""

#: includes/fields/class-gf-field-creditcard.php:24
msgid "Allows users to enter credit card information."
msgstr ""

#: includes/fields/class-gf-field-creditcard.php:112
msgid "Please enter your credit card information."
msgstr ""

#: includes/fields/class-gf-field-creditcard.php:118
msgid "Please enter your card's security code."
msgstr ""

#: includes/fields/class-gf-field-creditcard.php:121
msgid "Invalid credit card number."
msgstr ""

#: includes/fields/class-gf-field-creditcard.php:124
msgid "is not supported. Please enter one of the supported credit cards."
msgstr ""

#: includes/fields/class-gf-field-creditcard.php:199
msgid "This page is unsecured. Do not enter a real credit card number! Use this field only for testing purposes. "
msgstr ""

#: includes/fields/class-gf-field-creditcard.php:288
msgid "Supported Credit Cards:"
msgstr ""

#: includes/fields/class-gf-field-creditcard.php:303
#: includes/fields/class-gf-field-creditcard.php:397
msgid "Only digits are allowed"
msgstr ""

#: includes/fields/class-gf-field-creditcard.php:304
#: includes/template-library/templates/templates.php:4823
#: js.php:766
msgid "Card Number"
msgstr ""

#: includes/fields/class-gf-field-creditcard.php:328
#: includes/template-library/templates/templates.php:4828
#: js.php:768
#: js.php:1127
msgid "Expiration Date"
msgstr ""

#: includes/fields/class-gf-field-creditcard.php:345
#: includes/fields/class-gf-field-creditcard.php:484
#: includes/fields/class-gf-field-date.php:803
#: includes/fields/class-gf-field-date.php:985
#: includes/fields/class-gf-field-date.php:1143
#: js.php:320
#: js.php:1061
#: js.php:1068
#: js.php:1069
msgid "Year"
msgstr ""

#: includes/fields/class-gf-field-creditcard.php:395
#: includes/template-library/templates/templates.php:4833
#: js.php:770
msgid "Security Code"
msgstr ""

#: includes/fields/class-gf-field-creditcard.php:417
#: includes/template-library/templates/templates.php:3970
#: includes/template-library/templates/templates.php:4843
#: js.php:772
#: js.php:1131
msgid "Cardholder Name"
msgstr ""

#: includes/fields/class-gf-field-creditcard.php:611
#: js.php:767
#: js.php:1126
msgid "Expiration Month"
msgstr ""

#: includes/fields/class-gf-field-creditcard.php:616
#: js.php:769
#: js.php:1128
msgid "Expiration Year"
msgstr ""

#: includes/fields/class-gf-field-date.php:24
msgid "Allows users to enter a date."
msgstr ""

#: includes/fields/class-gf-field-date.php:133
msgid "Please enter a valid date in the format (%s)."
msgstr ""

#: includes/fields/class-gf-field-date.php:133
msgid "Please enter a valid date."
msgstr ""

#: includes/fields/class-gf-field-date.php:859
msgid "mm/dd/yyyy"
msgstr ""

#: includes/fields/class-gf-field-date.php:861
msgid "MM slash DD slash YYYY"
msgstr ""

#: includes/fields/class-gf-field-date.php:866
msgid "dd/mm/yyyy"
msgstr ""

#: includes/fields/class-gf-field-date.php:868
msgid "DD slash MM slash YYYY"
msgstr ""

#: includes/fields/class-gf-field-date.php:873
msgid "dd-mm-yyyy"
msgstr ""

#: includes/fields/class-gf-field-date.php:875
msgid "DD dash MM dash YYYY"
msgstr ""

#: includes/fields/class-gf-field-date.php:880
msgid "dd.mm.yyyy"
msgstr ""

#: includes/fields/class-gf-field-date.php:882
msgid "DD dot MM dot YYYY"
msgstr ""

#: includes/fields/class-gf-field-date.php:887
msgid "yyyy/mm/dd"
msgstr ""

#: includes/fields/class-gf-field-date.php:889
msgid "YYYY slash MM slash DD"
msgstr ""

#: includes/fields/class-gf-field-date.php:896
msgid "YYYY dash MM dash DD"
msgstr ""

#: includes/fields/class-gf-field-date.php:901
msgid "yyyy.mm.dd"
msgstr ""

#: includes/fields/class-gf-field-date.php:903
msgid "YYYY dot MM dot DD"
msgstr ""

#: includes/fields/class-gf-field-date.php:930
msgctxt "Abbreviation: Month"
msgid "MM"
msgstr ""

#: includes/fields/class-gf-field-date.php:933
msgctxt "Abbreviation: Day"
msgid "DD"
msgstr ""

#: includes/fields/class-gf-field-date.php:936
msgctxt "Abbreviation: Year"
msgid "YYYY"
msgstr ""

#: includes/fields/class-gf-field-donation.php:50
#: includes/fields/class-gf-field-price.php:46
msgid "Please enter a valid amount."
msgstr ""

#: includes/fields/class-gf-field-email.php:24
msgid "Allows users to enter a valid email address."
msgstr ""

#: includes/fields/class-gf-field-email.php:105
msgid "The email address entered is invalid, please check the formatting (e.g. <EMAIL>)."
msgstr ""

#: includes/fields/class-gf-field-email.php:110
msgid "Your emails do not match."
msgstr ""

#: includes/fields/class-gf-field-email.php:149
#: includes/template-library/templates/templates.php:164
#: includes/template-library/templates/templates.php:785
#: includes/template-library/templates/templates.php:1466
#: includes/template-library/templates/templates.php:1937
#: includes/template-library/templates/templates.php:5179
#: includes/template-library/templates/templates.php:6379
#: includes/template-library/templates/templates.php:7453
#: includes/template-library/templates/templates.php:7671
#: includes/template-library/templates/templates.php:8424
#: includes/template-library/templates/templates.php:8806
#: includes/template-library/templates/templates.php:10455
#: includes/template-library/templates/templates.php:10805
#: js.php:1099
#: notification.php:212
msgid "Enter Email"
msgstr ""

#: includes/fields/class-gf-field-email.php:151
#: includes/template-library/templates/templates.php:170
#: includes/template-library/templates/templates.php:793
#: includes/template-library/templates/templates.php:1472
#: includes/template-library/templates/templates.php:1943
#: includes/template-library/templates/templates.php:5186
#: includes/template-library/templates/templates.php:6385
#: includes/template-library/templates/templates.php:7459
#: includes/template-library/templates/templates.php:7676
#: includes/template-library/templates/templates.php:8430
#: includes/template-library/templates/templates.php:8812
#: includes/template-library/templates/templates.php:10461
#: includes/template-library/templates/templates.php:10812
#: js.php:1104
msgid "Confirm Email"
msgstr ""

#: includes/fields/class-gf-field-fileupload.php:120
msgid "Allows users to upload a file."
msgstr ""

#: includes/fields/class-gf-field-fileupload.php:172
#: includes/fields/class-gf-field-fileupload.php:184
#: includes/upload.php:117
msgid "File exceeds size limit. Maximum file size: %dMB"
msgstr ""

#: includes/fields/class-gf-field-fileupload.php:176
msgid "There was an error while uploading the file. Error code: %d"
msgstr ""

#: includes/fields/class-gf-field-fileupload.php:200
#: includes/fields/class-gf-field-fileupload.php:216
#: includes/upload.php:105
msgid "The uploaded file type is not allowed."
msgstr ""

#: includes/fields/class-gf-field-fileupload.php:222
#: includes/upload.php:123
msgid "The uploaded file type is not allowed. Must be one of the following: %s"
msgstr ""

#: includes/fields/class-gf-field-fileupload.php:265
msgid "Accepted file types: %s"
msgstr ""

#. translators: %s is replaced with a numeric string representing the maximum file size
#: includes/fields/class-gf-field-fileupload.php:270
msgid "Max. file size: %s"
msgstr ""

#. translators: %s is replaced with a numeric string representing the maximum number of files
#: includes/fields/class-gf-field-fileupload.php:275
msgid "Max. files: %s"
msgstr ""

#: includes/fields/class-gf-field-fileupload.php:313
#: includes/fields/class-gf-field-fileupload.php:336
msgid "Allowed Files"
msgstr ""

#: includes/fields/class-gf-field-fileupload.php:342
msgid "Drop files here or"
msgstr ""

#: includes/fields/class-gf-field-fileupload.php:343
msgid "Select files"
msgstr ""

#: includes/fields/class-gf-field-fileupload.php:402
msgid "Download file"
msgstr ""

#: includes/fields/class-gf-field-fileupload.php:403
#: includes/fields/class-gf-field-fileupload.php:430
msgid "Delete file"
msgstr ""

#: includes/fields/class-gf-field-fileupload.php:404
msgid "View file"
msgstr ""

#: includes/fields/class-gf-field-fileupload.php:748
msgid "%d files"
msgstr ""

#: includes/fields/class-gf-field-fileupload.php:763
#: includes/fields/class-gf-field-post-image.php:204
#: includes/fields/class-gf-field-post-image.php:230
msgid "View the image"
msgstr ""

#: includes/fields/class-gf-field-fileupload.php:822
msgid "Click to view"
msgstr ""

#: includes/fields/class-gf-field-hidden.php:24
msgid "Stores information that should not be visible to the user but can be processed and saved with the user submission."
msgstr ""

#: includes/fields/class-gf-field-html.php:12
msgid "HTML"
msgstr ""

#: includes/fields/class-gf-field-html.php:23
msgid "Places a block of free form HTML anywhere in your form."
msgstr ""

#: includes/fields/class-gf-field-html.php:56
msgid "HTML Content"
msgstr ""

#: includes/fields/class-gf-field-html.php:57
msgid "This is a content placeholder. HTML content is not displayed in the form admin. Preview this form to view the content."
msgstr ""

#: includes/fields/class-gf-field-list.php:47
msgid "Allows the user to add/remove additional rows of information per field."
msgstr ""

#: includes/fields/class-gf-field-list.php:224
msgid "Remove row {0}"
msgstr ""

#: includes/fields/class-gf-field-list.php:229
#: includes/fields/class-gf-field-list.php:410
msgid "Add another row"
msgstr ""

#: includes/fields/class-gf-field-list.php:229
#: includes/webapi/webapi.php:446
#: assets/js/src/legacy/admin/settings/field-map/mapping.js:351
msgid "Add"
msgstr ""

#: includes/fields/class-gf-field-list.php:230
msgid "Remove"
msgstr ""

#: includes/fields/class-gf-field-list.php:410
msgid "Add a new row"
msgstr ""

#: includes/fields/class-gf-field-list.php:411
msgid "Remove this row"
msgstr ""

#: includes/fields/class-gf-field-multiselect.php:41
msgid "Allows users to select multiple options available in the multi select box."
msgstr ""

#: includes/fields/class-gf-field-multiselect.php:153
msgid "Click to select..."
msgstr ""

#: includes/fields/class-gf-field-name.php:69
msgid "Allows users to enter their name in the format you have specified."
msgstr ""

#: includes/fields/class-gf-field-name.php:274
#: includes/template-library/templates/templates.php:37
#: includes/template-library/templates/templates.php:476
#: includes/template-library/templates/templates.php:1356
#: includes/template-library/templates/templates.php:1815
#: includes/template-library/templates/templates.php:2556
#: includes/template-library/templates/templates.php:3471
#: includes/template-library/templates/templates.php:4334
#: includes/template-library/templates/templates.php:5054
#: includes/template-library/templates/templates.php:6257
#: includes/template-library/templates/templates.php:7345
#: includes/template-library/templates/templates.php:7568
#: includes/template-library/templates/templates.php:8318
#: includes/template-library/templates/templates.php:8683
#: includes/template-library/templates/templates.php:9242
#: includes/template-library/templates/templates.php:10341
#: includes/template-library/templates/templates.php:10692
#: js.php:1009
msgid "Prefix"
msgstr ""

#: includes/fields/class-gf-field-name.php:275
#: includes/fields/class-gf-field-name.php:396
#: includes/template-library/templates/templates.php:89
#: includes/template-library/templates/templates.php:528
#: includes/template-library/templates/templates.php:1394
#: includes/template-library/templates/templates.php:1867
#: includes/template-library/templates/templates.php:2608
#: includes/template-library/templates/templates.php:3523
#: includes/template-library/templates/templates.php:4386
#: includes/template-library/templates/templates.php:5107
#: includes/template-library/templates/templates.php:6309
#: includes/template-library/templates/templates.php:7383
#: includes/template-library/templates/templates.php:7605
#: includes/template-library/templates/templates.php:8356
#: includes/template-library/templates/templates.php:8735
#: includes/template-library/templates/templates.php:9294
#: includes/template-library/templates/templates.php:10393
#: includes/template-library/templates/templates.php:10730
#: js.php:1017
msgid "First"
msgstr ""

#: includes/fields/class-gf-field-name.php:276
#: includes/template-library/templates/templates.php:96
#: includes/template-library/templates/templates.php:535
#: includes/template-library/templates/templates.php:1400
#: includes/template-library/templates/templates.php:1873
#: includes/template-library/templates/templates.php:2614
#: includes/template-library/templates/templates.php:3529
#: includes/template-library/templates/templates.php:4392
#: includes/template-library/templates/templates.php:5114
#: includes/template-library/templates/templates.php:6315
#: includes/template-library/templates/templates.php:7389
#: includes/template-library/templates/templates.php:7610
#: includes/template-library/templates/templates.php:8362
#: includes/template-library/templates/templates.php:8741
#: includes/template-library/templates/templates.php:9300
#: includes/template-library/templates/templates.php:10399
#: includes/template-library/templates/templates.php:10737
#: js.php:1029
msgid "Middle"
msgstr ""

#: includes/fields/class-gf-field-name.php:277
#: includes/fields/class-gf-field-name.php:397
#: includes/template-library/templates/templates.php:103
#: includes/template-library/templates/templates.php:542
#: includes/template-library/templates/templates.php:1407
#: includes/template-library/templates/templates.php:1880
#: includes/template-library/templates/templates.php:2621
#: includes/template-library/templates/templates.php:3536
#: includes/template-library/templates/templates.php:4399
#: includes/template-library/templates/templates.php:5121
#: includes/template-library/templates/templates.php:6322
#: includes/template-library/templates/templates.php:7396
#: includes/template-library/templates/templates.php:7616
#: includes/template-library/templates/templates.php:8369
#: includes/template-library/templates/templates.php:8748
#: includes/template-library/templates/templates.php:9307
#: includes/template-library/templates/templates.php:10406
#: includes/template-library/templates/templates.php:10744
#: js.php:1036
msgid "Last"
msgstr ""

#: includes/fields/class-gf-field-name.php:278
#: includes/template-library/templates/templates.php:110
#: includes/template-library/templates/templates.php:549
#: includes/template-library/templates/templates.php:1413
#: includes/template-library/templates/templates.php:1886
#: includes/template-library/templates/templates.php:2627
#: includes/template-library/templates/templates.php:3542
#: includes/template-library/templates/templates.php:4405
#: includes/template-library/templates/templates.php:5128
#: includes/template-library/templates/templates.php:6328
#: includes/template-library/templates/templates.php:7402
#: includes/template-library/templates/templates.php:7621
#: includes/template-library/templates/templates.php:8375
#: includes/template-library/templates/templates.php:8754
#: includes/template-library/templates/templates.php:9313
#: includes/template-library/templates/templates.php:10412
#: includes/template-library/templates/templates.php:10751
#: js.php:1041
msgid "Suffix"
msgstr ""

#: includes/fields/class-gf-field-number.php:24
msgid "Allows users to enter a number."
msgstr ""

#: includes/fields/class-gf-field-number.php:129
msgid "Please enter a valid quantity. Quantity cannot contain decimals."
msgstr ""

#: includes/fields/class-gf-field-number.php:199
msgid "Please enter a number from %1$s to %2$s."
msgstr ""

#: includes/fields/class-gf-field-number.php:201
msgid "Please enter a number greater than or equal to %s."
msgstr ""

#: includes/fields/class-gf-field-number.php:203
msgid "Please enter a number less than or equal to %s."
msgstr ""

#: includes/fields/class-gf-field-number.php:205
msgid "Please enter a valid number."
msgstr ""

#: includes/fields/class-gf-field-option.php:29
#: js.php:918
msgid "Option"
msgstr ""

#: includes/fields/class-gf-field-option.php:40
msgid "Allows users to select options for products created by a product field."
msgstr ""

#: includes/fields/class-gf-field-page.php:23
msgid "Allows multi-page forms."
msgstr ""

#: includes/fields/class-gf-field-page.php:51
msgid "Page Break"
msgstr ""

#: includes/fields/class-gf-field-password.php:14
#: includes/template-library/templates/templates.php:10540
#: js.php:825
msgid "Password"
msgstr ""

#: includes/fields/class-gf-field-password.php:25
msgid "Allows the user to enter a password and confirm it.  The password will be masked with blobs or asterisks."
msgstr ""

#: includes/fields/class-gf-field-password.php:92
msgid "Your passwords do not match."
msgstr ""

#: includes/fields/class-gf-field-password.php:104
msgid "Your password does not meet the required strength. %sHint: To make it stronger, use upper and lower case letters, numbers and symbols like ! \" ? $ %% ^ & )."
msgstr ""

#: includes/fields/class-gf-field-password.php:212
#: includes/template-library/templates/templates.php:10548
#: js.php:1115
msgid "Enter Password"
msgstr ""

#: includes/fields/class-gf-field-password.php:215
#: includes/template-library/templates/templates.php:10553
#: js.php:1116
msgid "Confirm Password"
msgstr ""

#: includes/fields/class-gf-field-password.php:225
#: includes/fields/class-gf-field-password.php:226
msgid "Show Password"
msgstr ""

#: includes/fields/class-gf-field-password.php:225
#: includes/fields/class-gf-field-password.php:226
msgid "Hide Password"
msgstr ""

#: includes/fields/class-gf-field-phone.php:49
msgid "Allows users to enter a phone number."
msgstr ""

#: includes/fields/class-gf-field-phone.php:180
#: js.php:1498
msgid "Phone format:"
msgstr ""

#: includes/fields/class-gf-field-post-category.php:23
msgid "Allows the user to select a category for the post they are creating."
msgstr ""

#: includes/fields/class-gf-field-post-content.php:14
msgid "Body"
msgstr ""

#: includes/fields/class-gf-field-post-content.php:25
msgid "Allows users to submit the body content for a post."
msgstr ""

#: includes/fields/class-gf-field-post-custom-field.php:12
msgid "Custom Field"
msgstr ""

#: includes/fields/class-gf-field-post-custom-field.php:23
msgid "Allows users to submit data that is used as a custom field value for a post."
msgstr ""

#: includes/fields/class-gf-field-post-excerpt.php:12
msgid "Excerpt"
msgstr ""

#: includes/fields/class-gf-field-post-excerpt.php:23
msgid "Allows users to submit data that is then used as the excerpt of a post."
msgstr ""

#: includes/fields/class-gf-field-post-image.php:32
#: js.php:862
msgid "Post Image"
msgstr ""

#: includes/fields/class-gf-field-post-image.php:43
msgid "Allows users to upload an image that is added to the Media Library and Gallery for the post that is created."
msgstr ""

#: includes/fields/class-gf-field-post-image.php:115
msgid "Accepted file types: %s."
msgstr ""

#: includes/fields/class-gf-field-post-image.php:128
msgid "delete"
msgstr ""

#: includes/fields/class-gf-field-post-image.php:132
#: js.php:830
msgid "File"
msgstr ""

#: includes/fields/class-gf-field-post-tags.php:12
msgid "Tags"
msgstr ""

#: includes/fields/class-gf-field-post-tags.php:23
msgid "Allows users to submit the tags for a post."
msgstr ""

#: includes/fields/class-gf-field-post-tags.php:90
#: includes/fields/class-gf-field-text.php:127
msgid "Separate tags with commas"
msgstr ""

#: includes/fields/class-gf-field-post-title.php:24
msgid "Allows users to submit the title for a post."
msgstr ""

#: includes/fields/class-gf-field-product.php:13
#: includes/orders/summaries/class-gf-order-summary.php:62
msgid "Product"
msgstr ""

#: includes/fields/class-gf-field-product.php:24
msgid "Allows the creation of products in the form."
msgstr ""

#: includes/fields/class-gf-field-quantity.php:29
#: includes/fields/class-gf-field-singleproduct.php:133
#: includes/fields/class-gf-field.php:2754
#: includes/template-library/templates/templates.php:2355
#: includes/template-library/templates/templates.php:2416
#: includes/template-library/templates/templates.php:3310
#: includes/template-library/templates/templates.php:3373
#: includes/template-library/templates/templates.php:4173
#: includes/template-library/templates/templates.php:4236
#: js.php:888
#: js.php:959
msgid "Quantity"
msgstr ""

#: includes/fields/class-gf-field-quantity.php:40
msgid "Allows a quantity to be specified for product field."
msgstr ""

#: includes/fields/class-gf-field-radio.php:33
#: includes/fields/class-gf-field-select.php:33
msgid "Allows users to select one option from a list."
msgstr ""

#: includes/fields/class-gf-field-radio.php:287
msgid "Other Choice, please specify"
msgstr ""

#: includes/fields/class-gf-field-repeater.php:26
msgid "Repeater"
msgstr ""

#: includes/fields/class-gf-field-repeater.php:379
msgid "Are you sure you want to remove this item?"
msgstr ""

#: includes/fields/class-gf-field-section.php:12
msgid "Section"
msgstr ""

#: includes/fields/class-gf-field-section.php:23
msgid "Adds a content separator to your form to help organize groups of fields. This is a visual element and does not collect any data."
msgstr ""

#: includes/fields/class-gf-field-shipping.php:26
#: includes/template-library/templates/templates.php:2926
#: includes/template-library/templates/templates.php:3841
#: includes/template-library/templates/templates.php:4704
#: js.php:900
msgid "Shipping"
msgstr ""

#: includes/fields/class-gf-field-shipping.php:37
msgid "Allows a shipping fee to be added to the form total."
msgstr ""

#: includes/fields/class-gf-field-text.php:13
msgid "Single Line Text"
msgstr ""

#: includes/fields/class-gf-field-text.php:24
msgid "Allows users to submit a single line of text."
msgstr ""

#: includes/fields/class-gf-field-text.php:84
#: includes/fields/class-gf-field-textarea.php:207
msgid "The text entered exceeds the maximum number of characters."
msgstr ""

#: includes/fields/class-gf-field-textarea.php:24
msgid "Allows users to submit multiple lines of text."
msgstr ""

#: includes/fields/class-gf-field-time.php:54
msgid "Allows users to submit a time as hours and minutes."
msgstr ""

#: includes/fields/class-gf-field-time.php:190
msgid "Please enter a valid time."
msgstr ""

#: includes/fields/class-gf-field-time.php:286
#: includes/fields/class-gf-field-time.php:325
#: js.php:324
#: js.php:327
msgid "HH"
msgstr ""

#: includes/fields/class-gf-field-time.php:287
#: includes/fields/class-gf-field-time.php:334
msgctxt "Abbreviation: Minutes"
msgid "MM"
msgstr ""

#: includes/fields/class-gf-field-time.php:306
msgid "AM"
msgstr ""

#: includes/fields/class-gf-field-time.php:307
msgid "PM"
msgstr ""

#: includes/fields/class-gf-field-time.php:314
#: js.php:1084
msgid "AM/PM"
msgstr ""

#: includes/fields/class-gf-field-time.php:327
msgid "Hours"
msgstr ""

#: includes/fields/class-gf-field-time.php:336
msgid "Minutes"
msgstr ""

#: includes/fields/class-gf-field-total.php:177
msgid "Submitted value (%s) does not match expected value (%s)."
msgstr ""

#: includes/fields/class-gf-field-website.php:24
msgid "Allows users to enter a website URL."
msgstr ""

#: includes/fields/class-gf-field-website.php:74
msgid "Please enter a valid Website URL (e.g. https://gravityforms.com)."
msgstr ""

#. Translators: comma-separated list of the labels of missing fields.
#: includes/fields/class-gf-field.php:881
msgid "Please complete the following fields: %s."
msgstr ""

#: includes/fields/class-gf-field.php:1584
msgid "duplicate this field"
msgstr ""

#: includes/fields/class-gf-field.php:1611
msgid "delete this field"
msgstr ""

#: includes/fields/class-gf-field.php:1635
msgid "jump to this field's settings"
msgstr ""

#: includes/fields/class-gf-field.php:1658
msgid "Move"
msgstr ""

#: includes/fields/class-gf-field.php:1667
msgid "ID: %s"
msgstr ""

#: includes/form-editor/choices-ui/config/class-gf-choices-ui-config-i18n.php:32
msgid "Define the choices for this field. If the field type supports it you will also be able to select the default choice(s) to the left of the choice."
msgstr ""

#: includes/form-editor/choices-ui/config/class-gf-choices-ui-config-i18n.php:34
msgid "Expand the Choices window"
msgstr ""

#: includes/form-editor/save-form/config/class-gf-form-editor-form-save-config.php:34
msgid "Form Updated"
msgstr ""

#: includes/form-editor/save-form/config/class-gf-form-editor-form-save-config.php:35
msgid "View Form"
msgstr ""

#: includes/form-editor/save-form/config/class-gf-form-editor-form-save-config.php:36
msgid "An error occurred while saving the form."
msgstr ""

#: includes/form-editor/save-form/config/class-gf-form-editor-form-save-config.php:37
msgid "Request failed due to a network error. Please check your internet connection."
msgstr ""

#: includes/form-editor/save-form/config/class-gf-form-editor-form-save-config.php:38
msgid "Form was updated successfully."
msgstr ""

#: includes/form-editor/save-form/config/class-gf-form-editor-form-save-config.php:41
msgid "Saved"
msgstr ""

#: includes/form-editor/save-form/config/class-gf-form-editor-form-save-config.php:43
#: includes/setup-wizard/config/class-gf-setup-wizard-config-i18n.php:52
#: includes/template-library/config/class-gf-template-library-config.php:94
#: assets/js/src/admin/block-editor/blocks/form/edit.js:133
#: assets/js/src/admin/block-editor/copy-paste-styles.js:128
msgid "Close"
msgstr ""

#: includes/form-editor/save-form/config/class-gf-form-editor-form-save-config.php:46
msgid "Save Error."
msgstr ""

#: includes/honeypot/class-gf-honeypot-handler.php:45
msgid "Honeypot Spam Filter"
msgstr ""

#: includes/honeypot/class-gf-honeypot-handler.php:45
msgid "Failed Honeypot Validation."
msgstr ""

#: includes/honeypot/class-gf-honeypot-handler.php:190
msgid "This field is for validation purposes and should be left unchanged."
msgstr ""

#: includes/legacy/forms_model_legacy.php:2333
msgid "There was a problem while inserting one of the input values for the entry"
msgstr ""

#: includes/legacy/forms_model_legacy.php:2342
msgid "There was a problem while inserting the field values"
msgstr ""

#: includes/libraries/gf-background-process.php:629
msgid "Every %d Minutes"
msgstr ""

#: includes/license/class-gf-license-api-response.php:43
#: includes/setup-wizard/endpoints/class-gf-setup-wizard-endpoint-validate-license.php:45
msgid "The license is invalid."
msgstr ""

#: includes/license/class-gf-license-api-response.php:147
msgid "Sites Exceeded"
msgstr ""

#: includes/license/class-gf-license-api-response.php:152
msgid "Invalid"
msgstr ""

#: includes/license/class-gf-license-api-response.php:154
msgid "Expired"
msgstr ""

#: includes/license/class-gf-license-api-response.php:222
msgid "Manage"
msgstr ""

#: includes/license/class-gf-license-api-response.php:229
#: includes/template-library/config/class-gf-template-library-config.php:113
msgid "Upgrade"
msgstr ""

#: includes/license/class-gf-license-api-response.php:299
#: tests/unit-tests/license/test-license-api-response.php:495
#: tests/unit-tests/license/test-license-api-response.php:511
msgid "Expired On"
msgstr ""

#: includes/license/class-gf-license-api-response.php:306
#: tests/unit-tests/license/test-license-api-response.php:523
msgid "Renews On"
msgstr ""

#: includes/license/class-gf-license-api-response.php:309
#: tests/unit-tests/license/test-license-api-response.php:533
#: tests/unit-tests/license/test-license-api-response.php:542
#: tests/unit-tests/license/test-license-api-response.php:551
msgid "Expires On"
msgstr ""

#: includes/license/class-gf-license-api-response.php:321
#: tests/unit-tests/license/test-license-api-response.php:420
msgid "Does not expire"
msgstr ""

#. translators: %1s and %2s are link tag markup
#: includes/license/class-gf-license-statuses.php:42
msgid "The license key entered is incorrect; please visit the %1$sGravity Forms website%2$s to verify your license."
msgstr ""

#: includes/license/class-gf-license-statuses.php:48
msgid "Your license key has been successfully validated."
msgstr ""

#. translators: %1s and %2s are link tag markup
#: includes/license/class-gf-license-statuses.php:51
msgid "The license key entered has been revoked; please check its status in your %1$sGravity Forms account.%2$s"
msgstr ""

#: includes/license/class-gf-license-statuses.php:55
msgid "This license key has already been activated on its maximum number of sites; please upgrade your license."
msgstr ""

#: includes/license/class-gf-license-statuses.php:56
msgid "This license key does not support multisite installations. Please use a different license."
msgstr ""

#. translators: %1s and %2s are link tag markup
#: includes/license/class-gf-license-statuses.php:59
msgid "This license key has expired; please visit your %1$sGravity Forms account%2$s to manage your license."
msgstr ""

#: includes/locking/class-gf-locking.php:199
msgid "This page is currently locked. Click on the \"Request Control\" button to let %s know you'd like to take over."
msgstr ""

#: includes/locking/class-gf-locking.php:200
#: includes/locking/locking.php:21
msgid "Accept"
msgstr ""

#: includes/locking/class-gf-locking.php:202
msgid "%s is currently editing"
msgstr ""

#: includes/locking/class-gf-locking.php:203
msgid "%s has taken over and is currently editing."
msgstr ""

#: includes/locking/class-gf-locking.php:204
msgid "%s has requested permission to take over control."
msgstr ""

#: includes/locking/class-gf-locking.php:205
#: includes/locking/class-gf-locking.php:285
msgid "You now have control"
msgstr ""

#: includes/locking/class-gf-locking.php:207
msgid "No response"
msgstr ""

#: includes/locking/class-gf-locking.php:208
msgid "Request again"
msgstr ""

#: includes/locking/class-gf-locking.php:210
msgid "Your request was rejected"
msgstr ""

#: includes/locking/class-gf-locking.php:290
msgid "Your request has been sent to %s."
msgstr ""

#: includes/locking/class-gf-locking.php:490
msgid "Take Over"
msgstr ""

#: includes/locking/class-gf-locking.php:491
msgid "Request Control"
msgstr ""

#: includes/locking/class-gf-locking.php:508
msgid "Reject Request"
msgstr ""

#: includes/locking/locking.php:20
msgid "This form is currently locked. Click on the \"Request Control\" button to let %s know you'd like to take over."
msgstr ""

#: includes/locking/locking.php:22
msgid "%s is currently editing this form"
msgstr ""

#: includes/locking/locking.php:23
msgid "%s has taken over and is currently editing this form."
msgstr ""

#: includes/locking/locking.php:24
msgid "%s has requested permission to take over control of this form."
msgstr ""

#: includes/locking/locking.php:61
msgid "This entry is currently locked. Click on the \"Request Control\" button to let %s know you'd like to take over."
msgstr ""

#: includes/locking/locking.php:62
msgid "%s is currently editing this entry"
msgstr ""

#: includes/locking/locking.php:63
msgid "%s has taken over and is currently editing this entry."
msgstr ""

#: includes/locking/locking.php:64
msgid "%s has requested permission to take over control of this entry."
msgstr ""

#: includes/locking/locking.php:107
msgid "These form settings are currently locked. Click on the \"Request Control\" button to let %s know you'd like to take over."
msgstr ""

#: includes/locking/locking.php:110
#: includes/locking/locking.php:152
msgid "%s has requested permission to take over control of these settings."
msgstr ""

#: includes/locking/locking.php:149
msgid "These settings are currently locked. Click on the \"Request Control\" button to let %s know you'd like to take over."
msgstr ""

#: includes/logging/logging.php:219
msgid "Log file could not be deleted."
msgstr ""

#: includes/logging/logging.php:222
msgid "Invalid log file."
msgstr ""

#: includes/logging/logging.php:228
msgid "Log file was successfully deleted."
msgstr ""

#: includes/logging/logging.php:290
msgid "Logging assists in tracking down issues by logging debug and error messages in Gravity Forms Core and Gravity Forms Add-Ons. Important information may be included in the logging messages, including API usernames, passwords and credit card numbers. Logging is intended only to be used temporarily while trying to track down issues. Once the issue is identified and resolved, it should be disabled."
msgstr ""

#: includes/logging/logging.php:307
msgid "Plugin Logging Settings"
msgstr ""

#: includes/logging/logging.php:440
msgid "view log"
msgstr ""

#: includes/logging/logging.php:441
msgid "delete log"
msgstr ""

#: includes/logging/logging.php:454
msgid "Enable logging and log all messages"
msgstr ""

#: includes/logging/logging.php:473
msgid "Enable logging"
msgstr ""

#: includes/logging/logging.php:509
msgid "and log all messages"
msgstr ""

#: includes/logging/logging.php:527
msgid "and log only error messages"
msgstr ""

#: includes/merge-tags/config/class-gf-merge-tags-config-i18n.php:28
msgid "Search Merge Tags"
msgstr ""

#: includes/orders/factories/class-gf-order-factory.php:136
msgid "Trial Discount"
msgstr ""

#: includes/orders/factories/class-gf-order-factory.php:188
#: includes/orders/factories/class-gf-order-factory.php:202
msgid "Free Trial"
msgstr ""

#: includes/orders/summaries/class-gf-order-summary.php:61
msgid "Order"
msgstr ""

#: includes/orders/summaries/class-gf-order-summary.php:63
msgid "Qty"
msgstr ""

#: includes/orders/summaries/class-gf-order-summary.php:64
msgid "Unit Price"
msgstr ""

#: includes/orders/summaries/views/view-order-summary.php:48
#: includes/orders/summaries/views/view-pricing-fields-html.php:44
#: includes/orders/summaries/views/view-pricing-fields-text.php:23
msgid "Sub Total"
msgstr ""

#: includes/save-form/class-gf-form-crud-handler.php:404
msgid "New submission from"
msgstr ""

#: includes/save-form/endpoints/class-gf-save-form-endpoint-admin.php:190
msgid "Please enter a unique form title, this title is used for an existing form."
msgstr ""

#: includes/save-form/endpoints/class-gf-save-form-endpoint-admin.php:192
msgid "There was an error while saving your form."
msgstr ""

#: includes/save-form/endpoints/class-gf-save-form-endpoint-admin.php:192
msgid "Please %1$scontact our support team%2$s."
msgstr ""

#: includes/settings/class-settings.php:832
msgid "Toggle %s Section"
msgstr ""

#: includes/settings/class-settings.php:988
msgid "Save Settings"
msgstr ""

#: includes/settings/class-settings.php:991
#: settings.php:1090
msgid "Settings updated."
msgstr ""

#: includes/settings/config/class-gf-settings-config-i18n.php:28
msgid "Loading"
msgstr ""

#: includes/settings/config/class-gf-settings-config-i18n.php:36
msgid "Hex"
msgstr ""

#: includes/settings/config/class-gf-settings-config-i18n.php:41
msgid "swatch"
msgstr ""

#: includes/settings/config/class-gf-settings-config-i18n.php:46
msgid "Click to upload"
msgstr ""

#: includes/settings/config/class-gf-settings-config-i18n.php:47
msgid "or drag and drop"
msgstr ""

#: includes/settings/config/class-gf-settings-config-i18n.php:48
msgid "max."
msgstr ""

#: includes/settings/config/class-gf-settings-config-i18n.php:49
msgid "or"
msgstr ""

#: includes/settings/config/class-gf-settings-config-i18n.php:50
msgid "Replace"
msgstr ""

#: includes/settings/fields/class-checkbox.php:325
#: includes/settings/fields/class-checkbox.php:373
#: includes/settings/fields/class-radio.php:204
#: includes/settings/fields/class-select-custom.php:220
#: includes/settings/fields/class-select.php:275
#: includes/settings/fields/class-select.php:303
msgid "Invalid selection."
msgstr ""

#: includes/settings/fields/class-date-time.php:180
msgid "Date must not include HTML tags."
msgstr ""

#: includes/settings/fields/class-date-time.php:186
msgid "You must select a valid hour."
msgstr ""

#: includes/settings/fields/class-date-time.php:192
msgid "You must select a valid minute."
msgstr ""

#: includes/settings/fields/class-date-time.php:198
msgid "You must select either am or pm."
msgstr ""

#: includes/settings/fields/class-field-select.php:145
#: includes/settings/fields/class-generic-map.php:342
msgid "First Name"
msgstr ""

#: includes/settings/fields/class-field-select.php:145
#: includes/settings/fields/class-generic-map.php:342
msgid "Name (First)"
msgstr ""

#: includes/settings/fields/class-field-select.php:146
#: includes/settings/fields/class-generic-map.php:343
msgid "Last Name"
msgstr ""

#: includes/settings/fields/class-field-select.php:146
#: includes/settings/fields/class-generic-map.php:343
msgid "Name (Last)"
msgstr ""

#: includes/settings/fields/class-field-select.php:147
#: includes/settings/fields/class-generic-map.php:344
msgid "Address (Street Address)"
msgstr ""

#: includes/settings/fields/class-field-select.php:148
#: includes/settings/fields/class-generic-map.php:345
msgid "Address (Address Line 2)"
msgstr ""

#: includes/settings/fields/class-field-select.php:149
#: includes/settings/fields/class-generic-map.php:346
msgid "Address (City)"
msgstr ""

#: includes/settings/fields/class-field-select.php:150
#: includes/settings/fields/class-generic-map.php:347
msgid "Address (State / Province)"
msgstr ""

#: includes/settings/fields/class-field-select.php:151
#: includes/settings/fields/class-generic-map.php:348
msgid "Address (Zip / Postal Code)"
msgstr ""

#: includes/settings/fields/class-field-select.php:152
#: includes/settings/fields/class-generic-map.php:349
msgid "Address (Country)"
msgstr ""

#: includes/settings/fields/class-generic-map.php:178
msgid "Key"
msgstr ""

#: includes/settings/fields/class-generic-map.php:181
msgid "Custom Key"
msgstr ""

#: includes/settings/fields/class-generic-map.php:187
msgid "Custom Value"
msgstr ""

#: includes/settings/fields/class-generic-map.php:258
msgid "No mapping fields are available."
msgstr ""

#: includes/settings/fields/class-generic-map.php:462
msgid "Full Address"
msgstr ""

#: includes/settings/fields/class-generic-map.php:472
msgid "Full Name"
msgstr ""

#: includes/settings/fields/class-generic-map.php:558
msgid "Form Fields"
msgstr ""

#: includes/settings/fields/class-generic-map.php:568
msgid "Entry Properties"
msgstr ""

#: includes/settings/fields/class-generic-map.php:609
msgid "Entry Meta"
msgstr ""

#: includes/settings/fields/class-notification-routing.php:52
msgid "To use notification routing, your form must have a field supported by conditional logic."
msgstr ""

#: includes/settings/fields/class-notification-routing.php:132
#: includes/settings/fields/class-notification-routing.php:238
msgid "Add Another Rule"
msgstr ""

#: includes/settings/fields/class-notification-routing.php:142
#: includes/settings/fields/class-notification-routing.php:149
#: includes/settings/fields/class-notification-routing.php:243
msgid "Remove This Rule"
msgstr ""

#: includes/settings/fields/class-notification-routing.php:161
#: includes/settings/fields/class-notification-routing.php:224
msgid "Send to"
msgstr ""

#: includes/settings/fields/class-notification-routing.php:163
#: includes/settings/fields/class-notification-routing.php:225
msgid "if"
msgstr ""

#: includes/settings/fields/class-notification-routing.php:180
msgid "Please enter a valid email address for all highlighted routing rules above."
msgstr ""

#: includes/settings/fields/class-notification-routing.php:379
#: includes/settings/fields/class-notification-routing.php:586
msgid "Enter value"
msgstr ""

#: includes/settings/fields/class-post-select.php:51
msgid "The requested post type %s does not exist."
msgstr ""

#. Translators: plural post type name (e.g. 'post's).
#: includes/settings/fields/class-post-select.php:158
msgid "Search all %s"
msgstr ""

#: includes/settings/fields/class-select-custom.php:90
msgid "Add Custom"
msgstr ""

#: includes/settings/fields/class-text.php:117
#: includes/settings/fields/class-textarea.php:213
msgid "The text you have entered is not valid. For security reasons, some characters are not allowed. "
msgstr ""

#: includes/settings/fields/class-text.php:120
#: includes/settings/fields/class-textarea.php:216
msgid "Fix it"
msgstr ""

#: includes/setup-wizard/config/class-gf-setup-wizard-config-i18n.php:53
msgid "Invalid License Key"
msgstr ""

#: includes/setup-wizard/config/class-gf-setup-wizard-config-i18n.php:54
msgid "Back To Dashboard"
msgstr ""

#: includes/setup-wizard/config/class-gf-setup-wizard-config-i18n.php:55
msgid "Toggle Fullscreen"
msgstr ""

#: includes/setup-wizard/config/class-gf-setup-wizard-config-i18n.php:58
#: includes/wizard/class-gf-installation-wizard.php:97
msgid "Welcome to Gravity Forms"
msgstr ""

#: includes/setup-wizard/config/class-gf-setup-wizard-config-i18n.php:59
msgid "Thank you for choosing Gravity Forms. We know you’re going to love our form builder and all it has to offer!"
msgstr ""

#: includes/setup-wizard/config/class-gf-setup-wizard-config-i18n.php:60
msgid "Create surveys and quizzes"
msgstr ""

#: includes/setup-wizard/config/class-gf-setup-wizard-config-i18n.php:61
msgid "Accept online payments"
msgstr ""

#: includes/setup-wizard/config/class-gf-setup-wizard-config-i18n.php:62
msgid "Build custom business solutions"
msgstr ""

#: includes/setup-wizard/config/class-gf-setup-wizard-config-i18n.php:63
msgid "Enter License Key"
msgstr ""

#: includes/setup-wizard/config/class-gf-setup-wizard-config-i18n.php:64
msgid "Paste your license key here"
msgstr ""

#: includes/setup-wizard/config/class-gf-setup-wizard-config-i18n.php:65
msgid "Enter your license key below to enable Gravity Forms."
msgstr ""

#: includes/setup-wizard/config/class-gf-setup-wizard-config-i18n.php:66
msgid "Activate License"
msgstr ""

#: includes/setup-wizard/config/class-gf-setup-wizard-config-i18n.php:67
msgid "License Key Validated"
msgstr ""

#: includes/setup-wizard/config/class-gf-setup-wizard-config-i18n.php:68
msgid "Checking License"
msgstr ""

#: includes/setup-wizard/config/class-gf-setup-wizard-config-i18n.php:69
msgid "Get 20% Off Gravity Forms!"
msgstr ""

#: includes/setup-wizard/config/class-gf-setup-wizard-config-i18n.php:70
msgid "To continue installation enter your email below and get 20% off any new license."
msgstr ""

#: includes/setup-wizard/config/class-gf-setup-wizard-config-i18n.php:71
msgid "Email address"
msgstr ""

#: includes/setup-wizard/config/class-gf-setup-wizard-config-i18n.php:72
msgid "Get the Discount"
msgstr ""

#: includes/setup-wizard/config/class-gf-setup-wizard-config-i18n.php:73
msgid "I agree to the handling and storage of my data and to receive marketing communications from Gravity Forms."
msgstr ""

#: includes/setup-wizard/config/class-gf-setup-wizard-config-i18n.php:76
msgid "Let's get you set up!"
msgstr ""

#: includes/setup-wizard/config/class-gf-setup-wizard-config-i18n.php:77
msgid "Configure Gravity Forms to work in the way that you want."
msgstr ""

#: includes/setup-wizard/config/class-gf-setup-wizard-config-i18n.php:78
msgid "Hide license information"
msgstr ""

#: includes/setup-wizard/config/class-gf-setup-wizard-config-i18n.php:79
msgid "If you're installing Gravity Forms for a client, enable this setting to hide the license information."
msgstr ""

#: includes/setup-wizard/config/class-gf-setup-wizard-config-i18n.php:80
msgid "Enable automatic updates"
msgstr ""

#: includes/setup-wizard/config/class-gf-setup-wizard-config-i18n.php:81
msgid "Recommended"
msgstr ""

#: includes/setup-wizard/config/class-gf-setup-wizard-config-i18n.php:82
msgid "Feature Disabled"
msgstr ""

#: includes/setup-wizard/config/class-gf-setup-wizard-config-i18n.php:83
msgid "We recommend you enable this feature to ensure Gravity Forms runs smoothly."
msgstr ""

#: includes/setup-wizard/config/class-gf-setup-wizard-config-i18n.php:87
msgid "Personalize your Gravity Forms experience"
msgstr ""

#: includes/setup-wizard/config/class-gf-setup-wizard-config-i18n.php:88
msgid "Tell us about your site and how you’d like to use Gravity Forms."
msgstr ""

#: includes/setup-wizard/config/class-gf-setup-wizard-config-i18n.php:89
msgid "How would you best describe your website?"
msgstr ""

#: includes/setup-wizard/config/class-gf-setup-wizard-config-i18n.php:90
msgid "What types of forms do you want to create?"
msgstr ""

#: includes/setup-wizard/config/class-gf-setup-wizard-config-i18n.php:91
msgid "Do you want to integrate your forms with any of these services?"
msgstr ""

#: includes/setup-wizard/config/class-gf-setup-wizard-config-i18n.php:96
msgid "Help Make Gravity Forms Better!"
msgstr ""

#. translators: placeholders are markup to create a link.
#: includes/setup-wizard/config/class-gf-setup-wizard-config-i18n.php:98
#: settings.php:646
msgid "We love improving the form building experience for everyone in our community. By enabling data collection, you can help us learn more about how our customers use Gravity Forms. %1$sLearn more...%2$s"
msgstr ""

#: includes/setup-wizard/config/class-gf-setup-wizard-config-i18n.php:103
msgid "Ready to Create Your First Form?"
msgstr ""

#: includes/setup-wizard/config/class-gf-setup-wizard-config-i18n.php:104
msgid "Watch the video below to help you get started with Gravity Forms, or jump straight in and begin your form building journey!"
msgstr ""

#: includes/setup-wizard/config/class-gf-setup-wizard-config.php:93
#: includes/template-library/templates/templates.php:9804
#: includes/template-library/templates/templates.php:9842
msgid "Contact Form"
msgstr ""

#: includes/setup-wizard/config/class-gf-setup-wizard-config.php:104
msgid "Conversational Form"
msgstr ""

#: includes/setup-wizard/config/class-gf-setup-wizard-config.php:115
msgid "Survey"
msgstr ""

#: includes/setup-wizard/config/class-gf-setup-wizard-config.php:126
#: includes/template-library/templates/templates.php:9789
#: includes/template-library/templates/templates.php:9824
msgid "Payment Form"
msgstr ""

#: includes/setup-wizard/config/class-gf-setup-wizard-config.php:137
msgid "Subscription Form"
msgstr ""

#: includes/setup-wizard/config/class-gf-setup-wizard-config.php:148
#: includes/template-library/templates/templates.php:1739
#: includes/template-library/templates/templates.php:1745
#: includes/template-library/templates/templates.php:9799
#: includes/template-library/templates/templates.php:9836
msgid "Donation Form"
msgstr ""

#: includes/setup-wizard/config/class-gf-setup-wizard-config.php:159
msgid "Customer Service Form"
msgstr ""

#: includes/setup-wizard/config/class-gf-setup-wizard-config.php:170
msgid "Registration Form"
msgstr ""

#: includes/setup-wizard/config/class-gf-setup-wizard-config.php:181
msgid "Custom Form"
msgstr ""

#: includes/setup-wizard/config/class-gf-setup-wizard-config.php:207
msgid "Select a Website Type"
msgstr ""

#: includes/setup-wizard/config/class-gf-setup-wizard-config.php:214
msgid "Blog"
msgstr ""

#: includes/setup-wizard/config/class-gf-setup-wizard-config.php:218
msgid "Personal Business/Services"
msgstr ""

#: includes/setup-wizard/config/class-gf-setup-wizard-config.php:222
msgid "Small/Medium Business"
msgstr ""

#: includes/setup-wizard/config/class-gf-setup-wizard-config.php:226
msgid "Enterprise"
msgstr ""

#: includes/setup-wizard/config/class-gf-setup-wizard-config.php:230
msgid "eCommerce"
msgstr ""

#: includes/setup-wizard/config/class-gf-setup-wizard-config.php:238
msgid "Nonprofit"
msgstr ""

#: includes/setup-wizard/config/class-gf-setup-wizard-config.php:252
msgid "Email Marketing Platform"
msgstr ""

#: includes/setup-wizard/config/class-gf-setup-wizard-config.php:263
msgid "CRM"
msgstr ""

#: includes/setup-wizard/config/class-gf-setup-wizard-config.php:274
msgid "Payment Processor"
msgstr ""

#: includes/setup-wizard/config/class-gf-setup-wizard-config.php:285
msgid "Anti Spam Services"
msgstr ""

#: includes/setup-wizard/config/class-gf-setup-wizard-config.php:296
msgid "Accounting Software"
msgstr ""

#: includes/setup-wizard/endpoints/class-gf-setup-wizard-endpoint-save-prefs.php:135
msgid "Preferences updated."
msgstr ""

#: includes/splash-page/class-gf-splash-page.php:112
msgid "About"
msgstr ""

#: includes/splash-page/class-gf-splash-page.php:166
msgid "About %s"
msgstr ""

#: includes/splash-page/gf_splash.php:4
msgid "Edit Forms with Ease in Gravity Forms 2.8!"
msgstr ""

#: includes/splash-page/gf_splash.php:5
msgid "The new Compact View makes it a cinch to edit long forms!"
msgstr ""

#: includes/splash-page/gf_splash.php:6
#: includes/splash-page/gf_splash.php:89
msgid "Get started with a new form"
msgstr ""

#: includes/splash-page/gf_splash.php:7
#: includes/splash-page/gf_splash.php:90
msgid "Get Started"
msgstr ""

#: includes/splash-page/gf_splash.php:11
msgid "Read reviews of Gravity Forms on G2"
msgstr ""

#: includes/splash-page/gf_splash.php:12
msgid "G2 logo"
msgstr ""

#: includes/splash-page/gf_splash.php:20
msgid "4.7 Stars"
msgstr ""

#: includes/splash-page/gf_splash.php:27
msgid "Form Editor Compact View"
msgstr ""

#: includes/splash-page/gf_splash.php:28
msgid "Our new compact view makes it easier than ever to edit your forms! If you have long forms, you no longer have to scroll for ages to find the field you’re looking for. The compact view gives you a bird’s eye view of your form, so you can quickly find the fields you need to edit."
msgstr ""

#: includes/splash-page/gf_splash.php:28
msgid "Read more about Compact View"
msgstr ""

#: includes/splash-page/gf_splash.php:28
#: includes/splash-page/gf_splash.php:65
msgid "Read More"
msgstr ""

#: includes/splash-page/gf_splash.php:31
msgid "Screenshot of the compact view in Gravity Forms 2.8"
msgstr ""

#: includes/splash-page/gf_splash.php:49
msgid "Icon of color swatches"
msgstr ""

#: includes/splash-page/gf_splash.php:58
msgid "Icon of a database"
msgstr ""

#: includes/splash-page/gf_splash.php:64
msgid "Orbital Form Styling"
msgstr ""

#: includes/splash-page/gf_splash.php:65
msgid "You might have noticed that we recently added a new setting so that you can use the beautiful and customizable Orbital form theme everywhere on your site, including shortcodes! Soon you’ll see Orbital in more places, and you’ll find more ways to customize it."
msgstr ""

#: includes/splash-page/gf_splash.php:65
msgid "Read more about styling your forms"
msgstr ""

#: includes/splash-page/gf_splash.php:66
msgid "Performance Improvements"
msgstr ""

#: includes/splash-page/gf_splash.php:67
msgid "We are always striving to improve the performance of Gravity Forms. In this release, you’ll notice smaller CSS files so that you don’t have to sacrifice performance to have good-looking forms."
msgstr ""

#: includes/splash-page/gf_splash.php:84
msgid "Ready to get started?"
msgstr ""

#: includes/splash-page/gf_splash.php:87
msgid "We believe there's a better way to manage your data and forms. Are you ready to create a form? Let's go!"
msgstr ""

#: includes/system-status/class-gf-system-report.php:69
msgid "The following is a system report containing useful technical information for troubleshooting issues. If you need further help after viewing the report, click on the \"Copy System Report\" button below to copy the report and paste it in your message to support."
msgstr ""

#: includes/system-status/class-gf-system-report.php:243
msgid "complete."
msgstr ""

#: includes/system-status/class-gf-system-report.php:247
msgid "Current status: %s"
msgstr ""

#: includes/system-status/class-gf-system-report.php:260
msgid "Upgrading Gravity Forms"
msgstr ""

#: includes/system-status/class-gf-system-report.php:262
msgid "Do not close or navigate away from this page until the upgrade is 100% complete."
msgstr ""

#: includes/system-status/class-gf-system-report.php:347
msgid "Unexpected content in the response."
msgstr ""

#: includes/system-status/class-gf-system-report.php:349
msgid "Response code: %s"
msgstr ""

#: includes/system-status/class-gf-system-report.php:360
msgid "Gravity Forms Environment"
msgstr ""

#: includes/system-status/class-gf-system-report.php:374
msgid "Database"
msgstr ""

#: includes/system-status/class-gf-system-report.php:379
msgid "Translations"
msgstr ""

#: includes/system-status/class-gf-system-report.php:384
msgid "Log Files"
msgstr ""

#: includes/system-status/class-gf-system-report.php:389
msgid "Scheduled (Cron) Events Log"
msgstr ""

#: includes/system-status/class-gf-system-report.php:396
msgid "WordPress Environment"
msgstr ""

#: includes/system-status/class-gf-system-report.php:400
#: notification.php:977
#: notification.php:1586
msgid "WordPress"
msgstr ""

#: includes/system-status/class-gf-system-report.php:404
msgid "Home URL"
msgstr ""

#: includes/system-status/class-gf-system-report.php:409
msgid "Site URL"
msgstr ""

#: includes/system-status/class-gf-system-report.php:414
msgid "REST API Base URL"
msgstr ""

#: includes/system-status/class-gf-system-report.php:419
msgid "WordPress Version"
msgstr ""

#: includes/system-status/class-gf-system-report.php:428
msgid "The Gravity Forms support agreement requires WordPress %s or greater. This site must be upgraded in order to be eligible for support."
msgstr ""

#: includes/system-status/class-gf-system-report.php:436
msgid "Gravity Forms requires WordPress %s or greater. You must upgrade WordPress in order to use Gravity Forms."
msgstr ""

#: includes/system-status/class-gf-system-report.php:443
msgid "WordPress Multisite"
msgstr ""

#: includes/system-status/class-gf-system-report.php:445
#: includes/system-status/class-gf-system-report.php:456
#: includes/system-status/class-gf-system-report.php:462
#: includes/system-status/class-gf-system-report.php:468
#: includes/system-status/class-gf-system-report.php:474
#: includes/system-status/class-gf-system-report.php:480
#: includes/system-status/class-gf-system-report.php:487
#: includes/system-status/class-gf-system-report.php:582
#: includes/system-status/class-gf-system-report.php:594
#: includes/system-status/class-gf-system-report.php:600
#: includes/system-status/class-gf-system-report.php:865
#: includes/system-status/class-gf-system-report.php:876
#: includes/system-status/class-gf-system-report.php:887
#: includes/system-status/class-gf-system-report.php:893
#: includes/template-library/templates/templates.php:9709
#: includes/wizard/steps/class-gf-installation-wizard-step-settings.php:71
msgid "Yes"
msgstr ""

#: includes/system-status/class-gf-system-report.php:445
#: includes/system-status/class-gf-system-report.php:456
#: includes/system-status/class-gf-system-report.php:462
#: includes/system-status/class-gf-system-report.php:468
#: includes/system-status/class-gf-system-report.php:474
#: includes/system-status/class-gf-system-report.php:480
#: includes/system-status/class-gf-system-report.php:487
#: includes/system-status/class-gf-system-report.php:582
#: includes/system-status/class-gf-system-report.php:588
#: includes/system-status/class-gf-system-report.php:594
#: includes/system-status/class-gf-system-report.php:600
#: includes/system-status/class-gf-system-report.php:865
#: includes/system-status/class-gf-system-report.php:876
#: includes/system-status/class-gf-system-report.php:887
#: includes/system-status/class-gf-system-report.php:893
#: includes/template-library/templates/templates.php:9715
#: includes/wizard/steps/class-gf-installation-wizard-step-settings.php:72
msgid "No"
msgstr ""

#: includes/system-status/class-gf-system-report.php:449
msgid "WordPress Memory Limit"
msgstr ""

#: includes/system-status/class-gf-system-report.php:454
msgid "WordPress Debug Mode"
msgstr ""

#: includes/system-status/class-gf-system-report.php:460
msgid "WordPress Debug Log"
msgstr ""

#: includes/system-status/class-gf-system-report.php:466
msgid "WordPress Script Debug Mode"
msgstr ""

#: includes/system-status/class-gf-system-report.php:472
msgid "WordPress Cron"
msgstr ""

#: includes/system-status/class-gf-system-report.php:478
msgid "WordPress Alternate Cron"
msgstr ""

#: includes/system-status/class-gf-system-report.php:484
msgid "Background tasks"
msgstr ""

#: includes/system-status/class-gf-system-report.php:495
msgid "Active Theme"
msgstr ""

#: includes/system-status/class-gf-system-report.php:500
msgid "Active Plugins"
msgstr ""

#: includes/system-status/class-gf-system-report.php:505
msgid "Network Active Plugins"
msgstr ""

#: includes/system-status/class-gf-system-report.php:512
msgid "Server Environment"
msgstr ""

#: includes/system-status/class-gf-system-report.php:516
msgid "Web Server"
msgstr ""

#: includes/system-status/class-gf-system-report.php:520
msgid "Software"
msgstr ""

#: includes/system-status/class-gf-system-report.php:525
msgid "Port"
msgstr ""

#: includes/system-status/class-gf-system-report.php:530
msgid "Document Root"
msgstr ""

#: includes/system-status/class-gf-system-report.php:537
msgid "PHP"
msgstr ""

#: includes/system-status/class-gf-system-report.php:541
#: includes/system-status/class-gf-system-report.php:621
#: includes/system-status/class-gf-system-report.php:838
msgid "Version"
msgstr ""

#: includes/system-status/class-gf-system-report.php:547
msgid "Recommended: PHP 7.3 or higher."
msgstr ""

#: includes/system-status/class-gf-system-report.php:550
msgid "Memory Limit"
msgstr ""

#: includes/system-status/class-gf-system-report.php:555
msgid "Maximum Execution Time"
msgstr ""

#: includes/system-status/class-gf-system-report.php:560
msgid "Maximum File Upload Size"
msgstr ""

#: includes/system-status/class-gf-system-report.php:565
msgid "Maximum File Uploads"
msgstr ""

#: includes/system-status/class-gf-system-report.php:570
msgid "Maximum Post Size"
msgstr ""

#: includes/system-status/class-gf-system-report.php:575
msgid "Maximum Input Variables"
msgstr ""

#: includes/system-status/class-gf-system-report.php:580
msgid "cURL Enabled"
msgstr ""

#: includes/system-status/class-gf-system-report.php:582
#: includes/system-status/class-gf-system-report.php:583
msgid "version"
msgstr ""

#: includes/system-status/class-gf-system-report.php:586
msgid "OpenSSL"
msgstr ""

#: includes/system-status/class-gf-system-report.php:592
msgid "Mcrypt Enabled"
msgstr ""

#: includes/system-status/class-gf-system-report.php:598
msgid "Mbstring Enabled"
msgstr ""

#: includes/system-status/class-gf-system-report.php:604
msgid "Loaded Extensions"
msgstr ""

#: includes/system-status/class-gf-system-report.php:612
msgid "Database Server"
msgstr ""

#: includes/system-status/class-gf-system-report.php:616
msgid "Database Management System"
msgstr ""

#: includes/system-status/class-gf-system-report.php:627
msgid "Gravity Forms requires MySQL 5 or above."
msgstr ""

#: includes/system-status/class-gf-system-report.php:630
msgid "Database Character Set"
msgstr ""

#: includes/system-status/class-gf-system-report.php:635
msgid "Database Collation"
msgstr ""

#: includes/system-status/class-gf-system-report.php:642
msgid "Date and Time"
msgstr ""

#: includes/system-status/class-gf-system-report.php:646
msgid "WordPress (Local) Timezone"
msgstr ""

#: includes/system-status/class-gf-system-report.php:651
msgid "MySQL - Universal time (UTC)"
msgstr ""

#: includes/system-status/class-gf-system-report.php:656
msgid "MySQL - Local time"
msgstr ""

#: includes/system-status/class-gf-system-report.php:661
msgid "PHP - Universal time (UTC)"
msgstr ""

#: includes/system-status/class-gf-system-report.php:666
msgid "PHP - Local time"
msgstr ""

#: includes/system-status/class-gf-system-report.php:845
#: includes/system-status/class-gf-system-report.php:1233
msgid "New version %s available."
msgstr ""

#: includes/system-status/class-gf-system-report.php:850
msgid "Upload folder"
msgstr ""

#: includes/system-status/class-gf-system-report.php:855
msgid "Upload folder permissions"
msgstr ""

#: includes/system-status/class-gf-system-report.php:857
msgid "Writable"
msgstr ""

#: includes/system-status/class-gf-system-report.php:857
msgid "Not writable"
msgstr ""

#: includes/system-status/class-gf-system-report.php:860
msgid "File uploads, entry exports, and logging will not function properly."
msgstr ""

#: includes/system-status/class-gf-system-report.php:863
#: tooltips.php:149
msgid "Output CSS"
msgstr ""

#: includes/system-status/class-gf-system-report.php:869
msgid "Default Theme"
msgstr ""

#: includes/system-status/class-gf-system-report.php:874
#: includes/wizard/steps/class-gf-installation-wizard-step-settings.php:44
#: tooltips.php:151
msgid "No-Conflict Mode"
msgstr ""

#: includes/system-status/class-gf-system-report.php:885
msgid "Background updates"
msgstr ""

#: includes/system-status/class-gf-system-report.php:891
msgid "REST API v2"
msgstr ""

#: includes/system-status/class-gf-system-report.php:897
msgid "Orbital Style Filter"
msgstr ""

#: includes/system-status/class-gf-system-report.php:932
#: includes/system-status/class-gf-system-report.php:1029
msgid "Database Version"
msgstr ""

#: includes/system-status/class-gf-system-report.php:976
msgid "Table does not exist"
msgstr ""

#: includes/system-status/class-gf-system-report.php:982
msgid "Table has incorrect auto-increment settings."
msgstr ""

#: includes/system-status/class-gf-system-report.php:988
msgid "Table has not been upgraded successfully."
msgstr ""

#: includes/system-status/class-gf-system-report.php:1004
msgid "WARNING! Re-running the upgrade process is only recommended if you are currently experiencing issues with your database. This process may take several minutes to complete. 'OK' to upgrade. 'Cancel' to abort."
msgstr ""

#: includes/system-status/class-gf-system-report.php:1011
msgid "Current Status: %s"
msgstr ""

#: includes/system-status/class-gf-system-report.php:1013
msgid "%s%% complete."
msgstr ""

#: includes/system-status/class-gf-system-report.php:1016
msgid "Automatic background migration is disabled but the database needs to be upgraded to version %s. %s"
msgstr ""

#: includes/system-status/class-gf-system-report.php:1017
msgid "Force the migration manually"
msgstr ""

#: includes/system-status/class-gf-system-report.php:1019
msgid "The database is currently being upgraded to version %s. %s"
msgstr ""

#: includes/system-status/class-gf-system-report.php:1021
msgid "As this site doesn't support background tasks the upgrade process will take longer than usual and the status will change infrequently."
msgstr ""

#: includes/system-status/class-gf-system-report.php:1023
msgid "Force the upgrade"
msgstr ""

#: includes/system-status/class-gf-system-report.php:1045
msgid "Upgrade database"
msgstr ""

#: includes/system-status/class-gf-system-report.php:1050
msgid "Your database version is out of date."
msgstr ""

#: includes/system-status/class-gf-system-report.php:1062
#: includes/system-status/class-gf-system-report.php:1078
msgid "Re-run database upgrade"
msgstr ""

#: includes/system-status/class-gf-system-report.php:1067
msgid "Database upgrade failed."
msgstr ""

#: includes/system-status/class-gf-system-report.php:1067
msgid "There are issues with your database."
msgstr ""

#: includes/system-status/class-gf-system-report.php:1083
msgid "Database upgraded successfully."
msgstr ""

#: includes/system-status/class-gf-system-report.php:1083
msgid "Your database is up-to-date."
msgstr ""

#: includes/system-status/class-gf-system-report.php:1083
msgid "Warning: downgrading Gravity Forms is not recommended."
msgstr ""

#: includes/system-status/class-gf-system-report.php:1239
msgid "Your system does not meet the minimum requirements for this Add-On (%d errors)."
msgstr ""

#: includes/system-status/class-gf-system-report.php:1259
#: includes/system-status/class-gf-system-report.php:1266
#: includes/system-status/class-gf-system-report.php:1279
#: includes/system-status/class-gf-system-report.php:1342
#: includes/system-status/class-gf-system-report.php:1350
#: includes/system-status/class-gf-system-report.php:1524
#: includes/system-status/class-gf-system-report.php:1525
#: includes/system-status/class-gf-system-report.php:1540
#: includes/system-status/class-gf-system-report.php:1541
msgid "by"
msgstr ""

#: includes/system-status/class-gf-system-report.php:1538
msgid "Parent"
msgstr ""

#: includes/system-status/class-gf-system-report.php:1659
msgid "Site Locale"
msgstr ""

#. translators: %d: The ID of the currently logged in user.
#: includes/system-status/class-gf-system-report.php:1668
msgid "User (ID: %d) Locale"
msgstr ""

#: includes/system-status/class-gf-system-status.php:62
msgid "System Report"
msgstr ""

#: includes/system-status/class-gf-system-status.php:70
#: includes/system-status/class-gf-update.php:47
msgid "Updates"
msgstr ""

#: includes/system-status/class-gf-update.php:30
msgid "You don't have permissions to view this page"
msgstr ""

#: includes/system-status/class-gf-update.php:51
msgid "Plugin"
msgstr ""

#: includes/system-status/class-gf-update.php:84
msgid "Visit plugin page"
msgstr ""

#: includes/system-status/class-gf-update.php:92
msgid "There is a new version of %s available. "
msgstr ""

#: includes/system-status/class-gf-update.php:99
msgid "%1$sView version %2$s details %3$s. "
msgstr ""

#: includes/system-status/class-gf-update.php:105
msgid "%1$sView version %2$s details %3$s or %4$supdate now%5$s."
msgstr ""

#: includes/system-status/class-gf-update.php:174
msgid "Your version of Gravity Forms is up to date."
msgstr ""

#: includes/system-status/class-gf-update.php:183
#: includes/system-status/class-gf-update.php:192
msgid "There is a new version of Gravity Forms available."
msgstr ""

#: includes/system-status/class-gf-update.php:184
msgid "You can update to the latest version automatically or download the update and install it manually."
msgstr ""

#: includes/template-library/config/class-gf-template-library-config.php:91
msgid "Enter the form title"
msgstr ""

#: includes/template-library/config/class-gf-template-library-config.php:93
msgid "Use Template"
msgstr ""

#. translators: title of template
#: includes/template-library/config/class-gf-template-library-config.php:96
msgid "Use Template %s"
msgstr ""

#: includes/template-library/config/class-gf-template-library-config.php:97
msgid "Creating Form"
msgstr ""

#: includes/template-library/config/class-gf-template-library-config.php:98
msgid "Please enter a valid form title."
msgstr ""

#: includes/template-library/config/class-gf-template-library-config.php:101
msgid "Import failed."
msgstr ""

#: includes/template-library/config/class-gf-template-library-config.php:102
msgid "Close."
msgstr ""

#. translators: title of template
#: includes/template-library/config/class-gf-template-library-config.php:104
msgid "Preview %s"
msgstr ""

#: includes/template-library/config/class-gf-template-library-config.php:106
msgid "Blank Form"
msgstr ""

#: includes/template-library/config/class-gf-template-library-config.php:107
msgid "Create Blank Form"
msgstr ""

#: includes/template-library/config/class-gf-template-library-config.php:108
msgid "New Blank Form"
msgstr ""

#: includes/template-library/config/class-gf-template-library-config.php:109
msgid "A new blank form"
msgstr ""

#: includes/template-library/config/class-gf-template-library-config.php:110
msgid "A form description goes here"
msgstr ""

#: includes/template-library/config/class-gf-template-library-config.php:111
msgid "Explore Form Templates"
msgstr ""

#: includes/template-library/config/class-gf-template-library-config.php:112
msgid "Quickly create an amazing form by using a pre-made template, or start from scratch to tailor your form to your specific needs."
msgstr ""

#: includes/template-library/config/class-gf-template-library-config.php:133
msgid "A new form"
msgstr ""

#: includes/template-library/templates/templates.php:5
msgid "A simple and basic contact form with only two fields"
msgstr ""

#: includes/template-library/templates/templates.php:6
#: includes/template-library/templates/templates.php:17
msgid "Simple Contact Form"
msgstr ""

#: includes/template-library/templates/templates.php:18
msgid "Please get in contact using the form below..."
msgstr ""

#: includes/template-library/templates/templates.php:42
#: includes/template-library/templates/templates.php:481
#: includes/template-library/templates/templates.php:1369
#: includes/template-library/templates/templates.php:1820
#: includes/template-library/templates/templates.php:2561
#: includes/template-library/templates/templates.php:3476
#: includes/template-library/templates/templates.php:4339
#: includes/template-library/templates/templates.php:5059
#: includes/template-library/templates/templates.php:6262
#: includes/template-library/templates/templates.php:7358
#: includes/template-library/templates/templates.php:7580
#: includes/template-library/templates/templates.php:8331
#: includes/template-library/templates/templates.php:8688
#: includes/template-library/templates/templates.php:9247
#: includes/template-library/templates/templates.php:10346
#: includes/template-library/templates/templates.php:10705
msgid "Mr."
msgstr ""

#: includes/template-library/templates/templates.php:48
#: includes/template-library/templates/templates.php:487
#: includes/template-library/templates/templates.php:1373
#: includes/template-library/templates/templates.php:1826
#: includes/template-library/templates/templates.php:2567
#: includes/template-library/templates/templates.php:3482
#: includes/template-library/templates/templates.php:4345
#: includes/template-library/templates/templates.php:5065
#: includes/template-library/templates/templates.php:6268
#: includes/template-library/templates/templates.php:7362
#: includes/template-library/templates/templates.php:7584
#: includes/template-library/templates/templates.php:8335
#: includes/template-library/templates/templates.php:8694
#: includes/template-library/templates/templates.php:9253
#: includes/template-library/templates/templates.php:10352
#: includes/template-library/templates/templates.php:10709
msgid "Mrs."
msgstr ""

#: includes/template-library/templates/templates.php:54
#: includes/template-library/templates/templates.php:493
#: includes/template-library/templates/templates.php:1365
#: includes/template-library/templates/templates.php:1832
#: includes/template-library/templates/templates.php:2573
#: includes/template-library/templates/templates.php:3488
#: includes/template-library/templates/templates.php:4351
#: includes/template-library/templates/templates.php:5071
#: includes/template-library/templates/templates.php:6274
#: includes/template-library/templates/templates.php:7354
#: includes/template-library/templates/templates.php:7576
#: includes/template-library/templates/templates.php:8327
#: includes/template-library/templates/templates.php:8700
#: includes/template-library/templates/templates.php:9259
#: includes/template-library/templates/templates.php:10358
#: includes/template-library/templates/templates.php:10701
msgid "Miss"
msgstr ""

#: includes/template-library/templates/templates.php:60
#: includes/template-library/templates/templates.php:499
#: includes/template-library/templates/templates.php:1377
#: includes/template-library/templates/templates.php:1838
#: includes/template-library/templates/templates.php:2579
#: includes/template-library/templates/templates.php:3494
#: includes/template-library/templates/templates.php:4357
#: includes/template-library/templates/templates.php:5077
#: includes/template-library/templates/templates.php:6280
#: includes/template-library/templates/templates.php:7366
#: includes/template-library/templates/templates.php:7588
#: includes/template-library/templates/templates.php:8339
#: includes/template-library/templates/templates.php:8706
#: includes/template-library/templates/templates.php:9265
#: includes/template-library/templates/templates.php:10364
#: includes/template-library/templates/templates.php:10713
msgid "Ms."
msgstr ""

#: includes/template-library/templates/templates.php:66
#: includes/template-library/templates/templates.php:505
#: includes/template-library/templates/templates.php:1361
#: includes/template-library/templates/templates.php:1844
#: includes/template-library/templates/templates.php:2585
#: includes/template-library/templates/templates.php:3500
#: includes/template-library/templates/templates.php:4363
#: includes/template-library/templates/templates.php:5083
#: includes/template-library/templates/templates.php:6286
#: includes/template-library/templates/templates.php:7350
#: includes/template-library/templates/templates.php:7572
#: includes/template-library/templates/templates.php:8323
#: includes/template-library/templates/templates.php:8712
#: includes/template-library/templates/templates.php:9271
#: includes/template-library/templates/templates.php:10370
#: includes/template-library/templates/templates.php:10697
msgid "Dr."
msgstr ""

#: includes/template-library/templates/templates.php:72
#: includes/template-library/templates/templates.php:511
#: includes/template-library/templates/templates.php:1381
#: includes/template-library/templates/templates.php:1850
#: includes/template-library/templates/templates.php:2591
#: includes/template-library/templates/templates.php:3506
#: includes/template-library/templates/templates.php:4369
#: includes/template-library/templates/templates.php:5089
#: includes/template-library/templates/templates.php:6292
#: includes/template-library/templates/templates.php:7370
#: includes/template-library/templates/templates.php:7592
#: includes/template-library/templates/templates.php:8343
#: includes/template-library/templates/templates.php:8718
#: includes/template-library/templates/templates.php:9277
#: includes/template-library/templates/templates.php:10376
#: includes/template-library/templates/templates.php:10717
msgid "Prof."
msgstr ""

#: includes/template-library/templates/templates.php:78
#: includes/template-library/templates/templates.php:517
#: includes/template-library/templates/templates.php:1385
#: includes/template-library/templates/templates.php:1856
#: includes/template-library/templates/templates.php:2597
#: includes/template-library/templates/templates.php:3512
#: includes/template-library/templates/templates.php:4375
#: includes/template-library/templates/templates.php:5095
#: includes/template-library/templates/templates.php:6298
#: includes/template-library/templates/templates.php:7374
#: includes/template-library/templates/templates.php:7596
#: includes/template-library/templates/templates.php:8347
#: includes/template-library/templates/templates.php:8724
#: includes/template-library/templates/templates.php:9283
#: includes/template-library/templates/templates.php:10382
#: includes/template-library/templates/templates.php:10721
msgid "Rev."
msgstr ""

#: includes/template-library/templates/templates.php:212
msgid "Comments"
msgstr ""

#: includes/template-library/templates/templates.php:237
#: includes/template-library/templates/templates.php:1145
msgid "Please let us know what's on your mind. Have a question for us? Ask away."
msgstr ""

#: includes/template-library/templates/templates.php:269
#: includes/template-library/templates/templates.php:1260
#: includes/template-library/templates/templates.php:1671
#: includes/template-library/templates/templates.php:2217
#: includes/template-library/templates/templates.php:3165
#: includes/template-library/templates/templates.php:4032
#: includes/template-library/templates/templates.php:4922
#: includes/template-library/templates/templates.php:6062
#: includes/template-library/templates/templates.php:7203
#: includes/template-library/templates/templates.php:8088
#: includes/template-library/templates/templates.php:8545
#: includes/template-library/templates/templates.php:9109
#: includes/template-library/templates/templates.php:10209
#: includes/template-library/templates/templates.php:10604
#: includes/template-library/templates/templates.php:11284
msgid "Save and Continue Later"
msgstr ""

#: includes/template-library/templates/templates.php:301
#: includes/template-library/templates/templates.php:1301
msgid "We have received your inquiry"
msgstr ""

#: includes/template-library/templates/templates.php:302
#: includes/template-library/templates/templates.php:1302
msgid "<p>Hi there {Name (First):1.3},</p><p>Thank you for getting in touch. We have received your inquiry and will get back to you within one business day.</p>"
msgstr ""

#: includes/template-library/templates/templates.php:313
#: includes/template-library/templates/templates.php:1313
#: includes/template-library/templates/templates.php:1711
#: includes/template-library/templates/templates.php:3209
#: includes/template-library/templates/templates.php:4073
#: includes/template-library/templates/templates.php:4967
#: includes/template-library/templates/templates.php:6122
#: includes/template-library/templates/templates.php:7254
#: tests/unit-tests/save-form/test-form-crud-handler.php:152
#: tests/unit-tests/save-form/test-form-crud-handler.php:282
#: tests/unit-tests/save-form/test-form-crud-handler.php:584
#: tests/unit-tests/save-form/test-form-crud-handler.php:699
msgid "New submission from {form_title}"
msgstr ""

#: includes/template-library/templates/templates.php:326
#: includes/template-library/templates/templates.php:1291
#: includes/template-library/templates/templates.php:7232
#: includes/template-library/templates/templates.php:9138
#: includes/template-library/templates/templates.php:10238
#: tests/unit-tests/save-form/test-form-crud-handler.php:176
#: tests/unit-tests/save-form/test-form-crud-handler.php:295
#: tests/unit-tests/save-form/test-form-crud-handler.php:597
#: tests/unit-tests/save-form/test-form-crud-handler.php:809
#: tests/unit-tests/save-form/test-form-crud-handler.php:852
#: tests/unit-tests/save-form/test-form-crud-handler.php:912
#: tests/unit-tests/save-form/test-form-crud-handler.php:955
msgid "Thank you for contacting us! We will get in touch with you shortly."
msgstr ""

#: includes/template-library/templates/templates.php:339
msgid "Hi there {Name (First):1.3}. We received the following information from you and will respond to your inquiry as quickly as possible."
msgstr ""

#: includes/template-library/templates/templates.php:350
msgid "An advanced contact form."
msgstr ""

#: includes/template-library/templates/templates.php:351
#: includes/template-library/templates/templates.php:362
msgid "Advanced Contact Form"
msgstr ""

#: includes/template-library/templates/templates.php:375
#: includes/template-library/templates/templates.php:8263
#: includes/template-library/templates/templates.php:8624
msgid "Next Steps: Sync an Email Add-On"
msgstr ""

#: includes/template-library/templates/templates.php:384
#: includes/template-library/templates/templates.php:8271
msgid "To get the most out of your form, we suggest that you sync this form with an email add-on. To learn more about your email add-on options, visit the following page (https://www.gravityforms.com/the-8-best-email-plugins-for-wordpress-in-2020/). Important: Delete this tip before you publish the form."
msgstr ""

#: includes/template-library/templates/templates.php:428
msgid "About You"
msgstr ""

#: includes/template-library/templates/templates.php:472
#: includes/template-library/templates/templates.php:5045
#: includes/template-library/templates/templates.php:7335
msgid "Your Name"
msgstr ""

#: includes/template-library/templates/templates.php:597
msgid "Your Address"
msgstr ""

#: includes/template-library/templates/templates.php:621
#: includes/template-library/templates/templates.php:2778
#: includes/template-library/templates/templates.php:2865
#: includes/template-library/templates/templates.php:3693
#: includes/template-library/templates/templates.php:3780
#: includes/template-library/templates/templates.php:4556
#: includes/template-library/templates/templates.php:4643
#: includes/template-library/templates/templates.php:5258
#: includes/template-library/templates/templates.php:6499
#: includes/template-library/templates/templates.php:7752
#: includes/template-library/templates/templates.php:8882
#: js.php:745
msgid "State / Province"
msgstr ""

#: includes/template-library/templates/templates.php:628
msgid "Zip / Postal Code"
msgstr ""

#: includes/template-library/templates/templates.php:687
msgid "We would love to chat with you. How can we get in touch?"
msgstr ""

#: includes/template-library/templates/templates.php:688
msgid "How Can We Reach You?"
msgstr ""

#: includes/template-library/templates/templates.php:730
msgid "Preferred Method of Contact"
msgstr ""

#: includes/template-library/templates/templates.php:777
#: includes/template-library/templates/templates.php:5171
msgid "Your Email Address"
msgstr ""

#: includes/template-library/templates/templates.php:840
#: includes/template-library/templates/templates.php:5309
msgid "Your Phone"
msgstr ""

#: includes/template-library/templates/templates.php:893
msgid "Best Time to Call You"
msgstr ""

#: includes/template-library/templates/templates.php:897
msgid "Select A Time"
msgstr ""

#: includes/template-library/templates/templates.php:901
msgid "12:00 am"
msgstr ""

#: includes/template-library/templates/templates.php:905
msgid "12:30 am"
msgstr ""

#: includes/template-library/templates/templates.php:909
msgid "1:00 am"
msgstr ""

#: includes/template-library/templates/templates.php:913
msgid "1:30 am"
msgstr ""

#: includes/template-library/templates/templates.php:917
msgid "2:00 am"
msgstr ""

#: includes/template-library/templates/templates.php:921
msgid "2:30 am"
msgstr ""

#: includes/template-library/templates/templates.php:925
msgid "3:00 am"
msgstr ""

#: includes/template-library/templates/templates.php:929
msgid "3:30 am"
msgstr ""

#: includes/template-library/templates/templates.php:933
msgid "4:00 am"
msgstr ""

#: includes/template-library/templates/templates.php:937
msgid "4:30 am"
msgstr ""

#: includes/template-library/templates/templates.php:941
msgid "5:00 am"
msgstr ""

#: includes/template-library/templates/templates.php:945
msgid "5:30 am"
msgstr ""

#: includes/template-library/templates/templates.php:949
msgid "6:00 am"
msgstr ""

#: includes/template-library/templates/templates.php:953
msgid "6:30 am"
msgstr ""

#: includes/template-library/templates/templates.php:957
msgid "7:00 am"
msgstr ""

#: includes/template-library/templates/templates.php:961
msgid "7:30 am"
msgstr ""

#: includes/template-library/templates/templates.php:965
msgid "8:00 am"
msgstr ""

#: includes/template-library/templates/templates.php:969
msgid "8:30 am"
msgstr ""

#: includes/template-library/templates/templates.php:973
msgid "9:00 am"
msgstr ""

#: includes/template-library/templates/templates.php:977
msgid "9:30 am"
msgstr ""

#: includes/template-library/templates/templates.php:981
msgid "10:00 am"
msgstr ""

#: includes/template-library/templates/templates.php:985
msgid "10:30 am"
msgstr ""

#: includes/template-library/templates/templates.php:989
msgid "11:00 am"
msgstr ""

#: includes/template-library/templates/templates.php:993
msgid "11:30 am"
msgstr ""

#: includes/template-library/templates/templates.php:997
msgid "12:00 pm"
msgstr ""

#: includes/template-library/templates/templates.php:1001
msgid "12:30 pm"
msgstr ""

#: includes/template-library/templates/templates.php:1005
msgid "1:00 pm"
msgstr ""

#: includes/template-library/templates/templates.php:1009
msgid "1:30 pm"
msgstr ""

#: includes/template-library/templates/templates.php:1013
msgid "2:00 pm"
msgstr ""

#: includes/template-library/templates/templates.php:1017
msgid "2:30 pm"
msgstr ""

#: includes/template-library/templates/templates.php:1021
msgid "3:00 pm"
msgstr ""

#: includes/template-library/templates/templates.php:1025
msgid "3:30 pm"
msgstr ""

#: includes/template-library/templates/templates.php:1029
msgid "4:00 pm"
msgstr ""

#: includes/template-library/templates/templates.php:1033
msgid "4:30 pm"
msgstr ""

#: includes/template-library/templates/templates.php:1037
msgid "5:00 pm"
msgstr ""

#: includes/template-library/templates/templates.php:1041
msgid "5:30 pm"
msgstr ""

#: includes/template-library/templates/templates.php:1045
msgid "6:00 pm"
msgstr ""

#: includes/template-library/templates/templates.php:1049
msgid "6:30 pm"
msgstr ""

#: includes/template-library/templates/templates.php:1053
msgid "7:00 pm"
msgstr ""

#: includes/template-library/templates/templates.php:1057
msgid "7:30 pm"
msgstr ""

#: includes/template-library/templates/templates.php:1061
msgid "8:00 pm"
msgstr ""

#: includes/template-library/templates/templates.php:1065
msgid "8:30 pm"
msgstr ""

#: includes/template-library/templates/templates.php:1069
msgid "9:00 pm"
msgstr ""

#: includes/template-library/templates/templates.php:1073
msgid "9:30 pm"
msgstr ""

#: includes/template-library/templates/templates.php:1077
msgid "10:00 pm"
msgstr ""

#: includes/template-library/templates/templates.php:1081
msgid "10:30 pm"
msgstr ""

#: includes/template-library/templates/templates.php:1085
msgid "11:00 pm"
msgstr ""

#: includes/template-library/templates/templates.php:1089
msgid "11:30 pm"
msgstr ""

#: includes/template-library/templates/templates.php:1146
msgid "What's on your mind?"
msgstr ""

#: includes/template-library/templates/templates.php:1189
msgid "Your Comments/Questions"
msgstr ""

#: includes/template-library/templates/templates.php:1326
msgid "A form that allows your users to enter a contest"
msgstr ""

#: includes/template-library/templates/templates.php:1327
msgid "Contest Entry Form"
msgstr ""

#: includes/template-library/templates/templates.php:1333
msgid "Form Template Library: Contest Entry Form"
msgstr ""

#: includes/template-library/templates/templates.php:1334
msgid "Enter our competition today to be in with a chance of winning..."
msgstr ""

#: includes/template-library/templates/templates.php:1339
msgid "Enter!"
msgstr ""

#: includes/template-library/templates/templates.php:1516
msgid "The answer is..."
msgstr ""

#: includes/template-library/templates/templates.php:1525
msgid "Answer A"
msgstr ""

#: includes/template-library/templates/templates.php:1531
msgid "Answer B"
msgstr ""

#: includes/template-library/templates/templates.php:1537
msgid "Answer C"
msgstr ""

#: includes/template-library/templates/templates.php:1579
msgid "Competition Terms and Conditions"
msgstr ""

#: includes/template-library/templates/templates.php:1604
msgid "<strong>I agree to the competition terms and conditions.</strong>"
msgstr ""

#: includes/template-library/templates/templates.php:1615
#: includes/template-library/templates/templates.php:5994
msgid "Terms and conditions placeholder."
msgstr ""

#: includes/template-library/templates/templates.php:1699
msgid "You have successfully entered our competition"
msgstr ""

#: includes/template-library/templates/templates.php:1700
msgid "<p>Hi there {Name (First):1.3},</p><p>Thank you for getting in touch and entering our competition. Keep an eye on your inbox as winners will be contacted via email.</p><p>Good Luck!</p>"
msgstr ""

#: includes/template-library/templates/templates.php:1725
msgid "Thank you for entering our competition! The winners will be contacted via email."
msgstr ""

#: includes/template-library/templates/templates.php:1738
msgid "A donation form for multiple purposes"
msgstr ""

#: includes/template-library/templates/templates.php:1746
msgid "Help us provide care and support for vulnerable adults."
msgstr ""

#: includes/template-library/templates/templates.php:1758
#: includes/template-library/templates/templates.php:2288
#: includes/template-library/templates/templates.php:6155
#: includes/template-library/templates/templates.php:7288
msgid "Next Steps: Install a Payment Add-On"
msgstr ""

#: includes/template-library/templates/templates.php:1767
msgid "To accept donations via this form you will need to install one of our payment add-ons. To learn more about your payment add-on options, visit the following page (https://www.gravityforms.com/blog/payment-add-ons). Important: Delete this tip before you publish the form."
msgstr ""

#: includes/template-library/templates/templates.php:1985
msgid "Donation Amount"
msgstr ""

#: includes/template-library/templates/templates.php:1995
msgid "Choose how much you would like to donate."
msgstr ""

#: includes/template-library/templates/templates.php:2012
msgid "10 USD"
msgstr ""

#: includes/template-library/templates/templates.php:2018
msgid "50 USD"
msgstr ""

#: includes/template-library/templates/templates.php:2024
msgid "250 USD"
msgstr ""

#: includes/template-library/templates/templates.php:2030
msgid "Other amount"
msgstr ""

#: includes/template-library/templates/templates.php:2057
msgid "Other Amount"
msgstr ""

#: includes/template-library/templates/templates.php:2161
#: includes/template-library/templates/templates.php:3096
#: includes/template-library/templates/templates.php:7132
#: includes/template-library/templates/templates.php:8019
msgid "Replace this field with a field specific to your payment gateway whenever possible."
msgstr ""

#: includes/template-library/templates/templates.php:2244
msgid "Thank you for your contribution! We appreciate your support."
msgstr ""

#: includes/template-library/templates/templates.php:2254
msgid "You have received a new donation."
msgstr ""

#: includes/template-library/templates/templates.php:2267
#: includes/template-library/templates/templates.php:2275
msgid "A form that allows you to sell products and let your customers pay via different payment gateways"
msgstr ""

#: includes/template-library/templates/templates.php:2268
#: includes/template-library/templates/templates.php:2274
msgid "eCommerce Form"
msgstr ""

#: includes/template-library/templates/templates.php:2297
#: includes/template-library/templates/templates.php:6164
#: includes/template-library/templates/templates.php:7297
msgid "To accept payments on this form you will need to install one of our payment add-ons. To learn more about your payment add-on options, visit the following page (https://www.gravityforms.com/blog/payment-add-ons). Important: Delete this tip before you publish the form."
msgstr ""

#: includes/template-library/templates/templates.php:2337
#: includes/template-library/templates/templates.php:3292
#: includes/template-library/templates/templates.php:4155
msgid "My Super Awesome Product"
msgstr ""

#: includes/template-library/templates/templates.php:2362
#: includes/template-library/templates/templates.php:3316
#: includes/template-library/templates/templates.php:4179
msgid "This is my super awesome product. It's the best, so everyone should buy it!"
msgstr ""

#: includes/template-library/templates/templates.php:2398
#: includes/template-library/templates/templates.php:3355
#: includes/template-library/templates/templates.php:4218
msgid "Another Amazing Product"
msgstr ""

#: includes/template-library/templates/templates.php:2423
#: includes/template-library/templates/templates.php:3379
#: includes/template-library/templates/templates.php:4242
msgid "If you loved the first product, you're really going to love this one. Don't miss out, order yours while they're still in stock."
msgstr ""

#: includes/template-library/templates/templates.php:2459
msgid "Subtotal"
msgstr ""

#: includes/template-library/templates/templates.php:2751
#: includes/template-library/templates/templates.php:3666
#: includes/template-library/templates/templates.php:4529
msgid "Billing Address"
msgstr ""

#: includes/template-library/templates/templates.php:2838
#: includes/template-library/templates/templates.php:3753
#: includes/template-library/templates/templates.php:4616
msgid "Shipping Address"
msgstr ""

#: includes/template-library/templates/templates.php:2950
#: includes/template-library/templates/templates.php:3864
#: includes/template-library/templates/templates.php:4727
msgid "Standard Shipping"
msgstr ""

#: includes/template-library/templates/templates.php:2956
#: includes/template-library/templates/templates.php:3870
#: includes/template-library/templates/templates.php:4733
msgid "Express Shipping"
msgstr ""

#: includes/template-library/templates/templates.php:2962
#: includes/template-library/templates/templates.php:3876
#: includes/template-library/templates/templates.php:4739
msgid "Overnight Shipping"
msgstr ""

#: includes/template-library/templates/templates.php:3048
msgid "PayPal"
msgstr ""

#: includes/template-library/templates/templates.php:3199
#: includes/template-library/templates/templates.php:4063
#: includes/template-library/templates/templates.php:4957
msgid "Thank you for shopping with us! Your payment was successfully completed."
msgstr ""

#: includes/template-library/templates/templates.php:3222
#: includes/template-library/templates/templates.php:3229
msgid "Stripe Checkout Form"
msgstr ""

#: includes/template-library/templates/templates.php:3223
#: includes/template-library/templates/templates.php:3230
msgid "A form that allows you to sell products and let your customers pay via Stripe"
msgstr ""

#: includes/template-library/templates/templates.php:3246
msgid "Next Steps: Install the Stripe Add-On"
msgstr ""

#: includes/template-library/templates/templates.php:3254
msgid "To accept payments on this form you will need to install the Stripe payment add-on. Important: Delete this tip before you publish the form."
msgstr ""

#: includes/template-library/templates/templates.php:3960
msgid "Card Details"
msgstr ""

#: includes/template-library/templates/templates.php:3965
#: includes/template-library/templates/templates.php:4838
#: js.php:771
msgid "Card Type"
msgstr ""

#: includes/template-library/templates/templates.php:4085
#: includes/template-library/templates/templates.php:4092
msgid "PayPal Checkout Form"
msgstr ""

#: includes/template-library/templates/templates.php:4086
#: includes/template-library/templates/templates.php:4093
msgid "A form that allows you to sell products and let your customers pay via PayPal"
msgstr ""

#: includes/template-library/templates/templates.php:4109
msgid "Next Steps: Install the PayPal Checkout Add-On"
msgstr ""

#: includes/template-library/templates/templates.php:4117
msgid "To accept payments on this form you will need to install the PayPal Checkout payment add-on. Important: Delete this tip before you publish the form."
msgstr ""

#: includes/template-library/templates/templates.php:4980
msgid "Allow your users to apply for a job"
msgstr ""

#: includes/template-library/templates/templates.php:4981
#: includes/template-library/templates/templates.php:4988
msgid "Employment Application Form"
msgstr ""

#: includes/template-library/templates/templates.php:4994
msgid "Submit Application"
msgstr ""

#: includes/template-library/templates/templates.php:5002
msgid "Your Personal Information"
msgstr ""

#: includes/template-library/templates/templates.php:5354
msgid "Best Time To Call You"
msgstr ""

#: includes/template-library/templates/templates.php:5362
msgid "Mornings"
msgstr ""

#: includes/template-library/templates/templates.php:5368
msgid "Early Afternoon"
msgstr ""

#: includes/template-library/templates/templates.php:5374
msgid "Late Afternoon"
msgstr ""

#: includes/template-library/templates/templates.php:5380
msgid "Early Evening"
msgstr ""

#: includes/template-library/templates/templates.php:5387
msgid "When is the best time for us to reach you via telephone?"
msgstr ""

#: includes/template-library/templates/templates.php:5465
#: includes/template-library/templates/templates.php:5508
msgid "Position You're Applying For"
msgstr ""

#: includes/template-library/templates/templates.php:5534
msgid "IT/Technical"
msgstr ""

#: includes/template-library/templates/templates.php:5540
msgid "Clerical/Accounting"
msgstr ""

#: includes/template-library/templates/templates.php:5546
msgid "Facilities Maintenance"
msgstr ""

#: includes/template-library/templates/templates.php:5590
msgid "Hours You Are Available for Work"
msgstr ""

#: includes/template-library/templates/templates.php:5597
msgid "Please tell us what hours you are available for work each day of the week."
msgstr ""

#: includes/template-library/templates/templates.php:5667
msgid "Previous Employment"
msgstr ""

#: includes/template-library/templates/templates.php:5710
msgid "Your Previous Employers"
msgstr ""

#: includes/template-library/templates/templates.php:5717
msgid "Please list your previous employers, the dates you worked and the position you held"
msgstr ""

#: includes/template-library/templates/templates.php:5732
msgid "Employer"
msgstr ""

#: includes/template-library/templates/templates.php:5738
msgid "Dates"
msgstr ""

#: includes/template-library/templates/templates.php:5744
msgid "Position"
msgstr ""

#: includes/template-library/templates/templates.php:5824
msgid "More About You"
msgstr ""

#: includes/template-library/templates/templates.php:5867
msgid "Tell Us About Yourself"
msgstr ""

#: includes/template-library/templates/templates.php:5911
msgid "Upload Your Resume"
msgstr ""

#: includes/template-library/templates/templates.php:5918
msgid "Upload your resume in .pdf, .doc or .docx format"
msgstr ""

#: includes/template-library/templates/templates.php:5958
msgid "Terms and Conditions"
msgstr ""

#: includes/template-library/templates/templates.php:5983
msgid "<strong>I agree to the terms and conditions.</strong>"
msgstr ""

#: includes/template-library/templates/templates.php:6098
msgid "Thank you for submitting your application! We will get in touch with you shortly."
msgstr ""

#: includes/template-library/templates/templates.php:6110
msgid "We have received your application."
msgstr ""

#: includes/template-library/templates/templates.php:6111
msgid "<p>Hi {Name (First):1.3},</p><p>Thank you for submitting your application. We are in the process of reviewing it and will get in touch with you shortly.</p>"
msgstr ""

#: includes/template-library/templates/templates.php:6134
msgid "Let your users book tickets for an event"
msgstr ""

#: includes/template-library/templates/templates.php:6135
#: includes/template-library/templates/templates.php:6141
#: includes/template-library/templates/templates.php:9814
#: includes/template-library/templates/templates.php:9854
msgid "Event Registration Form"
msgstr ""

#: includes/template-library/templates/templates.php:6142
msgid "Please complete this form to register for the event."
msgstr ""

#: includes/template-library/templates/templates.php:6204
msgid "Contact Details"
msgstr ""

#: includes/template-library/templates/templates.php:6603
msgid "Event Details"
msgstr ""

#: includes/template-library/templates/templates.php:6655
msgid "Male"
msgstr ""

#: includes/template-library/templates/templates.php:6661
msgid "Female"
msgstr ""

#: includes/template-library/templates/templates.php:6667
msgid "Non-binary"
msgstr ""

#: includes/template-library/templates/templates.php:6673
msgid "Agender"
msgstr ""

#: includes/template-library/templates/templates.php:6679
msgid "My gender isn't listed"
msgstr ""

#: includes/template-library/templates/templates.php:6736
msgid "16-24"
msgstr ""

#: includes/template-library/templates/templates.php:6766
msgid "65+"
msgstr ""

#: includes/template-library/templates/templates.php:6808
msgid "How did you hear about this event?"
msgstr ""

#: includes/template-library/templates/templates.php:6817
#: includes/template-library/templates/templates.php:11139
msgid "Social Media"
msgstr ""

#: includes/template-library/templates/templates.php:6823
msgid "Google"
msgstr ""

#: includes/template-library/templates/templates.php:6829
#: includes/template-library/templates/templates.php:11151
msgid "Word of Mouth"
msgstr ""

#: includes/template-library/templates/templates.php:6835
msgid "Refer a Friend"
msgstr ""

#: includes/template-library/templates/templates.php:6841
msgid "Past Participant"
msgstr ""

#: includes/template-library/templates/templates.php:6977
msgid "Ticket Type"
msgstr ""

#: includes/template-library/templates/templates.php:7004
msgid "Early Bird Ticket"
msgstr ""

#: includes/template-library/templates/templates.php:7010
msgid "Premium Ticket"
msgstr ""

#: includes/template-library/templates/templates.php:7016
msgid "VIP Ticket"
msgstr ""

#: includes/template-library/templates/templates.php:7045
msgid "Number of tickets needed"
msgstr ""

#: includes/template-library/templates/templates.php:7242
msgid "We have received your registration."
msgstr ""

#: includes/template-library/templates/templates.php:7243
msgid "<p>Hi there {Name (First):1.3},</p><p>Thank you for registering for our event. We look forward to seeing you!</p>"
msgstr ""

#: includes/template-library/templates/templates.php:7267
msgid "Allow your users to purchase a gift certificate"
msgstr ""

#: includes/template-library/templates/templates.php:7268
msgid "Gift Certificate Form"
msgstr ""

#: includes/template-library/templates/templates.php:7274
msgid "Gift Certificate Order Form"
msgstr ""

#: includes/template-library/templates/templates.php:7275
msgid "Purchase a gift certificate today for your nearest and dearest..."
msgstr ""

#: includes/template-library/templates/templates.php:7280
msgid "Buy Now"
msgstr ""

#: includes/template-library/templates/templates.php:7444
msgid "Your Email"
msgstr ""

#: includes/template-library/templates/templates.php:7501
msgid "How would you like the gift certificate delivered?"
msgstr ""

#: includes/template-library/templates/templates.php:7516
msgid "Mail"
msgstr ""

#: includes/template-library/templates/templates.php:7558
msgid "Name of Recipient"
msgstr ""

#: includes/template-library/templates/templates.php:7662
msgid "Email of Recipient"
msgstr ""

#: includes/template-library/templates/templates.php:7727
msgid "Address of Recipient"
msgstr ""

#: includes/template-library/templates/templates.php:7863
msgid "Add a message to your gift certificate..."
msgstr ""

#: includes/template-library/templates/templates.php:7907
msgid "Gift Certificate Amount"
msgstr ""

#: includes/template-library/templates/templates.php:7933
msgid "$30"
msgstr ""

#: includes/template-library/templates/templates.php:7939
msgid "$50"
msgstr ""

#: includes/template-library/templates/templates.php:7945
msgid "$100"
msgstr ""

#: includes/template-library/templates/templates.php:8143
#: includes/template-library/templates/templates.php:8204
msgid "<p>Dear {Your Name (First):1.3} </p><p>Thank you for making a gift certificate purchase.</p><p>A {Gift Certificate Amount:9} gift certificate will now be emailed to {Name of Recipient (First):3.3} {Name of Recipient (Last):3.6}.</p><p>We will email you a receipt for your purchase shortly.</p>"
msgstr ""

#: includes/template-library/templates/templates.php:8174
msgid "<p>Gift Certificate ID: <strong>000GIFT{entry_id}</strong></p><p>Dear {Name of Recipient (First):3.3},</p><p>We are delighted to send you this gift certificate worth {Gift Certificate Amount:9} from {Your Name (First):1.3} {Your Name (Last):1.6}.</p><p>Here's a message from them to you...</p><p><blockquote>{Add a message to your gift certificate...:17}</blockquote></p><p>We look forward to seeing you in-store to redeem your gift certificate!</p>"
msgstr ""

#: includes/template-library/templates/templates.php:8226
msgid "Thank you for making a gift certificate purchase! You should receive an email from us shortly with more information."
msgstr ""

#: includes/template-library/templates/templates.php:8242
msgid "Let users sign up to your newsletter"
msgstr ""

#: includes/template-library/templates/templates.php:8243
msgid "Newsletter Signup Form"
msgstr ""

#: includes/template-library/templates/templates.php:8249
msgid "Form Template Library: Newsletter Signup Form"
msgstr ""

#: includes/template-library/templates/templates.php:8250
msgid "If you want to keep up to date with what's happening on the blog, sign up for our newsletter!"
msgstr ""

#: includes/template-library/templates/templates.php:8255
msgid "Keep me up to date!"
msgstr ""

#: includes/template-library/templates/templates.php:8471
msgid "Privacy"
msgstr ""

#: includes/template-library/templates/templates.php:8479
msgid "I agree with the storage and handling of my data by this website. - <a target=\"_blank\" href=\"#\" rel=\"noopener noreferrer\">Privacy Policy</a> <abbr class=\"wpgdprc-required\" title=\"You need to accept this checkbox.\">*</abbr>"
msgstr ""

#: includes/template-library/templates/templates.php:8480
msgid "I agree with the storage and handling of my data by this website."
msgstr ""

#: includes/template-library/templates/templates.php:8488
msgid "I agree with the storage and handling of my data by this website. - <a target=\"_blank\" href=\"#\" rel=\"noopener noreferrer\">Privacy  Policy</a> <abbr class=\"wpgdprc-required\" title=\"You need to accept this checkbox.\">*</abbr>"
msgstr ""

#: includes/template-library/templates/templates.php:8579
msgid "Thank you for signing up. Be on the lookout for our monthly newsletter!"
msgstr ""

#: includes/template-library/templates/templates.php:8603
msgid "Helps users ask for a quote for a certain service or product you are selling on your website"
msgstr ""

#: includes/template-library/templates/templates.php:8604
#: includes/template-library/templates/templates.php:8610
#: includes/template-library/templates/templates.php:9809
#: includes/template-library/templates/templates.php:9848
msgid "Request a Quote Form"
msgstr ""

#: includes/template-library/templates/templates.php:8611
msgid "Please fill out the information below and we will be in touch shortly with your personalized quote."
msgstr ""

#: includes/template-library/templates/templates.php:8633
msgid "To get the most out of your form, we suggest that you sync this form with an email add-on. To learn more about your email add-on options, visit the following page: (https://www.gravityforms.com/the-8-best-email-plugins-for-wordpress-in-2020). Important: Delete this tip before you publish the form."
msgstr ""

#: includes/template-library/templates/templates.php:8942
msgid "Please select the service/s you require..."
msgstr ""

#: includes/template-library/templates/templates.php:8950
#: includes/template-library/templates/templates.php:8983
msgid "Landscape Gardening"
msgstr ""

#: includes/template-library/templates/templates.php:8956
#: includes/template-library/templates/templates.php:8988
msgid "Ground Maintenance"
msgstr ""

#: includes/template-library/templates/templates.php:8962
#: includes/template-library/templates/templates.php:8993
msgid "Tree Surgery Services"
msgstr ""

#: includes/template-library/templates/templates.php:8968
#: includes/template-library/templates/templates.php:8998
msgid "Fencing"
msgstr ""

#: includes/template-library/templates/templates.php:8974
#: includes/template-library/templates/templates.php:9003
msgid "Clearance"
msgstr ""

#: includes/template-library/templates/templates.php:9161
msgid "Get feedback about your product using a survey form"
msgstr ""

#: includes/template-library/templates/templates.php:9162
#: includes/template-library/templates/templates.php:9168
#: includes/template-library/templates/templates.php:9794
#: includes/template-library/templates/templates.php:9830
msgid "Survey Form"
msgstr ""

#: includes/template-library/templates/templates.php:9169
msgid "Tell us what you think about Acme Products..."
msgstr ""

#: includes/template-library/templates/templates.php:9182
msgid "Next Steps: Install the Survey Add-On"
msgstr ""

#: includes/template-library/templates/templates.php:9191
msgid "This form requires the Gravity Forms Survey Add-On. Important: Delete this tip before you publish the form."
msgstr ""

#: includes/template-library/templates/templates.php:9399
msgid "Company Name"
msgstr ""

#: includes/template-library/templates/templates.php:9486
msgid "Rate Acme Products"
msgstr ""

#: includes/template-library/templates/templates.php:9499
msgid "Terrible"
msgstr ""

#: includes/template-library/templates/templates.php:9505
msgid "Not so great"
msgstr ""

#: includes/template-library/templates/templates.php:9517
msgid "Pretty good"
msgstr ""

#: includes/template-library/templates/templates.php:9523
msgid "Excellent"
msgstr ""

#: includes/template-library/templates/templates.php:9531
#: includes/template-library/templates/templates.php:9639
#: includes/template-library/templates/templates.php:9723
#: includes/template-library/templates/templates.php:9862
#: includes/template-library/templates/templates.php:9964
#: includes/template-library/templates/templates.php:10065
#: includes/template-library/templates/templates.php:10134
msgid "First row"
msgstr ""

#: includes/template-library/templates/templates.php:9535
#: includes/template-library/templates/templates.php:9643
#: includes/template-library/templates/templates.php:9727
#: includes/template-library/templates/templates.php:9866
#: includes/template-library/templates/templates.php:9968
#: includes/template-library/templates/templates.php:10069
#: includes/template-library/templates/templates.php:10138
msgid "Second row"
msgstr ""

#: includes/template-library/templates/templates.php:9539
#: includes/template-library/templates/templates.php:9647
#: includes/template-library/templates/templates.php:9731
#: includes/template-library/templates/templates.php:9870
#: includes/template-library/templates/templates.php:9972
#: includes/template-library/templates/templates.php:10073
#: includes/template-library/templates/templates.php:10142
msgid "Third row"
msgstr ""

#: includes/template-library/templates/templates.php:9543
#: includes/template-library/templates/templates.php:9651
#: includes/template-library/templates/templates.php:9735
#: includes/template-library/templates/templates.php:9874
#: includes/template-library/templates/templates.php:9976
#: includes/template-library/templates/templates.php:10077
#: includes/template-library/templates/templates.php:10146
msgid "Fourth row"
msgstr ""

#: includes/template-library/templates/templates.php:9547
#: includes/template-library/templates/templates.php:9655
#: includes/template-library/templates/templates.php:9739
#: includes/template-library/templates/templates.php:9878
#: includes/template-library/templates/templates.php:9980
#: includes/template-library/templates/templates.php:10081
#: includes/template-library/templates/templates.php:10150
msgid "Fifth row"
msgstr ""

#: includes/template-library/templates/templates.php:9588
msgid "How many sites do you have Acme Products installed on?"
msgstr ""

#: includes/template-library/templates/templates.php:9601
msgid "Just one"
msgstr ""

#: includes/template-library/templates/templates.php:9607
msgid "2-5"
msgstr ""

#: includes/template-library/templates/templates.php:9613
msgid "6-10"
msgstr ""

#: includes/template-library/templates/templates.php:9619
msgid "11-25"
msgstr ""

#: includes/template-library/templates/templates.php:9625
msgid "26-100"
msgstr ""

#: includes/template-library/templates/templates.php:9631
msgid "100+"
msgstr ""

#: includes/template-library/templates/templates.php:9696
msgid "Do you use Acme Products to sell products or services?"
msgstr ""

#: includes/template-library/templates/templates.php:9780
msgid "What types of forms have you created with Acme Products?"
msgstr ""

#: includes/template-library/templates/templates.php:9919
msgid "Rank these add-ons based on how useful they are to you."
msgstr ""

#: includes/template-library/templates/templates.php:9932
msgid "Stripe"
msgstr ""

#: includes/template-library/templates/templates.php:9938
msgid "MailChimp"
msgstr ""

#: includes/template-library/templates/templates.php:9944
msgid "Zapier"
msgstr ""

#: includes/template-library/templates/templates.php:9950
msgid "Surveys"
msgstr ""

#: includes/template-library/templates/templates.php:9956
msgid "Dropbox"
msgstr ""

#: includes/template-library/templates/templates.php:10020
msgid "Acme Products fulfils all my form needs"
msgstr ""

#: includes/template-library/templates/templates.php:10033
msgid "Strongly disagree"
msgstr ""

#: includes/template-library/templates/templates.php:10057
msgid "Strongly agree"
msgstr ""

#: includes/template-library/templates/templates.php:10120
msgid "How could Acme Products be improved?"
msgstr ""

#: includes/template-library/templates/templates.php:10261
msgid "Let your users register to your website easily"
msgstr ""

#: includes/template-library/templates/templates.php:10262
#: includes/template-library/templates/templates.php:10268
msgid "User Registration Form"
msgstr ""

#: includes/template-library/templates/templates.php:10269
msgid "Please complete the following form to register on our site. Thanks."
msgstr ""

#: includes/template-library/templates/templates.php:10282
msgid "Next Steps: Install the User Registration Add-On"
msgstr ""

#: includes/template-library/templates/templates.php:10291
msgid "This form requires the Gravity Forms User Registration Add-On. Important: Delete this tip before you publish the form."
msgstr ""

#: includes/template-library/templates/templates.php:10504
msgid "Username"
msgstr ""

#: includes/template-library/templates/templates.php:10637
msgid "Thank you for registering! You should receive an email from us shortly containing your account information."
msgstr ""

#: includes/template-library/templates/templates.php:10661
msgid "Helps your users register to a webinar"
msgstr ""

#: includes/template-library/templates/templates.php:10662
#: includes/template-library/templates/templates.php:10668
msgid "Webinar Registration Form"
msgstr ""

#: includes/template-library/templates/templates.php:10669
msgid "Register for our latest webinar..."
msgstr ""

#: includes/template-library/templates/templates.php:10674
msgid "Register Today"
msgstr ""

#: includes/template-library/templates/templates.php:10858
msgid "Company Website"
msgstr ""

#: includes/template-library/templates/templates.php:10904
msgid "Position / Job Title"
msgstr ""

#: includes/template-library/templates/templates.php:10951
msgid "Industry Type"
msgstr ""

#: includes/template-library/templates/templates.php:10960
msgid "Advertising"
msgstr ""

#: includes/template-library/templates/templates.php:10966
msgid "Agriculture"
msgstr ""

#: includes/template-library/templates/templates.php:10972
msgid "Banking"
msgstr ""

#: includes/template-library/templates/templates.php:10978
msgid "Construction"
msgstr ""

#: includes/template-library/templates/templates.php:10984
msgid "Creatives"
msgstr ""

#: includes/template-library/templates/templates.php:10996
msgid "Entertainment"
msgstr ""

#: includes/template-library/templates/templates.php:11002
msgid "Fashion"
msgstr ""

#: includes/template-library/templates/templates.php:11008
msgid "Finance"
msgstr ""

#: includes/template-library/templates/templates.php:11014
msgid "Hospitality"
msgstr ""

#: includes/template-library/templates/templates.php:11032
msgid "Services"
msgstr ""

#: includes/template-library/templates/templates.php:11083
msgid "Do you have any questions you would like to ask our speakers?"
msgstr ""

#: includes/template-library/templates/templates.php:11130
msgid "How did you hear about this webinar?"
msgstr ""

#: includes/template-library/templates/templates.php:11145
msgid "Advertisement"
msgstr ""

#: includes/template-library/templates/templates.php:11198
msgid "Want to keep up-to-date with our latest news and announcements?"
msgstr ""

#: includes/template-library/templates/templates.php:11315
msgid "Thank you for registering for our webinar! Keep an eye out for an email from us containing more information."
msgstr ""

#: includes/templates/edit-shortcode-form.tpl.php:11
#: widget.php:163
msgid "Advanced Options"
msgstr ""

#: includes/templates/edit-shortcode-form.tpl.php:17
msgid "Update Form"
msgstr ""

#: includes/upload.php:29
#: includes/upload.php:57
msgid "Failed to upload file."
msgstr ""

#: includes/upload.php:134
#: includes/upload.php:265
#: includes/upload.php:275
msgid "Upload unsuccessful"
msgstr ""

#: includes/upload.php:198
msgid "Failed to open temp directory."
msgstr ""

#: includes/upload.php:224
#: includes/upload.php:248
msgid "Failed to open input stream."
msgstr ""

#: includes/upload.php:231
#: includes/upload.php:254
msgid "Failed to open output stream."
msgstr ""

#: includes/upload.php:234
msgid "Failed to move uploaded file."
msgstr ""

#: includes/webapi/includes/class-gf-api-keys-table.php:29
#: includes/webapi/webapi.php:418
msgid "Permissions"
msgstr ""

#: includes/webapi/includes/class-gf-api-keys-table.php:30
#: includes/webapi/webapi.php:428
msgid "Last Access"
msgstr ""

#: includes/webapi/includes/class-gf-api-keys-table.php:80
#: includes/webapi/webapi.php:1088
msgid "Never Accessed"
msgstr ""

#: includes/webapi/includes/class-gf-api-keys-table.php:94
msgid "You don't have any API keys. Let's go %1$screate one%2$s!"
msgstr ""

#: includes/webapi/v2/class-gf-rest-authentication.php:354
msgid "Consumer secret is invalid."
msgstr ""

#. translators: %s: amount of errors
#: includes/webapi/v2/class-gf-rest-authentication.php:474
msgid "Missing OAuth parameter %s"
msgid_plural "Missing OAuth parameters %s"
msgstr[0] ""
msgstr[1] ""

#: includes/webapi/v2/class-gf-rest-authentication.php:520
msgid "Consumer key is invalid."
msgstr ""

#: includes/webapi/v2/class-gf-rest-authentication.php:568
msgid "Invalid signature - failed to sort parameters."
msgstr ""

#: includes/webapi/v2/class-gf-rest-authentication.php:577
msgid "Invalid signature - signature method is invalid."
msgstr ""

#: includes/webapi/v2/class-gf-rest-authentication.php:587
msgid "Invalid signature - provided signature does not match."
msgstr ""

#: includes/webapi/v2/class-gf-rest-authentication.php:669
msgid "Invalid timestamp."
msgstr ""

#: includes/webapi/v2/class-gf-rest-authentication.php:679
msgid "Invalid nonce - nonce has already been used."
msgstr ""

#: includes/webapi/v2/class-gf-rest-authentication.php:749
msgid "The API key provided does not have read permissions."
msgstr ""

#: includes/webapi/v2/class-gf-rest-authentication.php:757
msgid "The API key provided does not have write permissions."
msgstr ""

#: includes/webapi/v2/class-gf-rest-authentication.php:764
msgid "Unknown request method."
msgstr ""

#: includes/webapi/v2/class-gf-rest-authentication.php:804
msgid "Gravity Forms API. Use a consumer key in the username field and a consumer secret in the password field."
msgstr ""

#: includes/webapi/v2/includes/controllers/class-controller-entries.php:94
#: includes/webapi/v2/includes/controllers/class-controller-entries.php:181
#: includes/webapi/v2/includes/controllers/class-controller-entry-notes.php:57
msgid "Invalid entry id."
msgstr ""

#: includes/webapi/v2/includes/controllers/class-controller-entries.php:199
msgid "The entry has already been deleted."
msgstr ""

#: includes/webapi/v2/includes/controllers/class-controller-entries.php:336
#: includes/webapi/v2/includes/controllers/class-controller-entry-notes.php:213
#: includes/webapi/v2/includes/controllers/class-controller-form-entries.php:231
#: includes/webapi/v2/includes/controllers/class-controller-notes.php:337
msgid "Missing entry JSON"
msgstr ""

#: includes/webapi/v2/includes/controllers/class-controller-entry-notes.php:86
#: includes/webapi/v2/includes/controllers/class-controller-notes.php:122
msgid "Error retrieving notes."
msgstr ""

#: includes/webapi/v2/includes/controllers/class-controller-entry-notifications.php:61
msgid "Form not found."
msgstr ""

#: includes/webapi/v2/includes/controllers/class-controller-entry-properties.php:54
#: includes/webapi/webapi.php:1563
msgid "No property values were found in the request body"
msgstr ""

#: includes/webapi/v2/includes/controllers/class-controller-entry-properties.php:57
#: includes/webapi/webapi.php:1565
msgid "Property values should be sent as an array"
msgstr ""

#: includes/webapi/v2/includes/controllers/class-controller-entry-properties.php:74
#: includes/webapi/webapi.php:1498
msgid "Entry updated successfully"
msgstr ""

#: includes/webapi/v2/includes/controllers/class-controller-entry-properties.php:113
msgid "Missing Key Value Pairs JSON"
msgstr ""

#: includes/webapi/v2/includes/controllers/class-controller-feed-properties.php:55
msgid "Feed updated successfully"
msgstr ""

#: includes/webapi/v2/includes/controllers/class-controller-feed-properties.php:70
#: includes/webapi/v2/includes/controllers/class-controller-feeds.php:173
msgid "Invalid JSON. Properties should be sent as key value pairs."
msgstr ""

#: includes/webapi/v2/includes/controllers/class-controller-feed-properties.php:101
#: includes/webapi/v2/includes/controllers/class-controller-form-feeds.php:257
msgid "Unique identifier for the feed."
msgstr ""

#: includes/webapi/v2/includes/controllers/class-controller-feed-properties.php:106
#: includes/webapi/v2/includes/controllers/class-controller-form-feeds.php:262
msgid "The Form ID for the feed."
msgstr ""

#: includes/webapi/v2/includes/controllers/class-controller-feed-properties.php:110
msgid "Indicates if the feed is active or inactive."
msgstr ""

#: includes/webapi/v2/includes/controllers/class-controller-feed-properties.php:114
msgid "The position of the feed on the feeds list page and when processed; for add-ons which support feed ordering."
msgstr ""

#: includes/webapi/v2/includes/controllers/class-controller-feed-properties.php:118
#: includes/webapi/v2/includes/controllers/class-controller-form-feeds.php:268
msgid "The JSON string containing the feed meta."
msgstr ""

#: includes/webapi/v2/includes/controllers/class-controller-feed-properties.php:122
#: includes/webapi/v2/includes/controllers/class-controller-form-feeds.php:273
msgid "The add-on the feed belongs to."
msgstr ""

#: includes/webapi/v2/includes/controllers/class-controller-feeds.php:108
#: includes/webapi/v2/includes/controllers/class-controller-feeds.php:138
#: includes/webapi/v2/includes/controllers/class-controller-feeds.php:223
msgid "Invalid feed id."
msgstr ""

#: includes/webapi/v2/includes/controllers/class-controller-form-entries.php:305
msgid "Unique identifier for the resource."
msgstr ""

#: includes/webapi/v2/includes/controllers/class-controller-form-entries.php:310
msgid "The Form ID for the entry."
msgstr ""

#: includes/webapi/v2/includes/controllers/class-controller-form-entries.php:316
msgid "The date the entry was created, in UTC."
msgstr ""

#: includes/webapi/v2/includes/controllers/class-controller-form-entries.php:321
msgid "The date the entry was updated, in UTC."
msgstr ""

#: includes/webapi/v2/includes/controllers/class-controller-form-entries.php:326
msgid "Whether the entry is starred."
msgstr ""

#: includes/webapi/v2/includes/controllers/class-controller-form-entries.php:331
msgid "Whether the entry has been read."
msgstr ""

#: includes/webapi/v2/includes/controllers/class-controller-form-entries.php:336
msgid "The IP address of the entry creator."
msgstr ""

#: includes/webapi/v2/includes/controllers/class-controller-form-entries.php:341
msgid "The URL where the form was embedded."
msgstr ""

#: includes/webapi/v2/includes/controllers/class-controller-form-entries.php:346
msgid "The user agent string for the browser used to submit the entry."
msgstr ""

#: includes/webapi/v2/includes/controllers/class-controller-form-entries.php:351
msgid "The status of the payment, if applicable."
msgstr ""

#: includes/webapi/v2/includes/controllers/class-controller-form-entries.php:356
msgid "The date of the payment, if applicable."
msgstr ""

#: includes/webapi/v2/includes/controllers/class-controller-form-entries.php:361
msgid "The amount of the payment, if applicable."
msgstr ""

#: includes/webapi/v2/includes/controllers/class-controller-form-entries.php:366
msgid "The payment method for the payment, if applicable."
msgstr ""

#: includes/webapi/v2/includes/controllers/class-controller-form-entries.php:371
msgid "The transaction ID for the payment, if applicable."
msgstr ""

#: includes/webapi/v2/includes/controllers/class-controller-form-entries.php:376
msgid "Whether the transaction has been fulfilled, if applicable."
msgstr ""

#: includes/webapi/v2/includes/controllers/class-controller-form-entries.php:381
msgid "The user ID of the entry submitter."
msgstr ""

#: includes/webapi/v2/includes/controllers/class-controller-form-entries.php:386
msgid "The type of the transaction, if applicable."
msgstr ""

#: includes/webapi/v2/includes/controllers/class-controller-form-entries.php:391
msgid "The status of the entry."
msgstr ""

#: includes/webapi/v2/includes/controllers/class-controller-form-feeds.php:192
msgid "Missing feed JSON"
msgstr ""

#: includes/webapi/v2/includes/controllers/class-controller-form-feeds.php:212
msgid "Missing add-on slug"
msgstr ""

#: includes/webapi/v2/includes/controllers/class-controller-form-feeds.php:217
msgid "Missing feed meta"
msgstr ""

#: includes/webapi/v2/includes/controllers/class-controller-form-submissions.php:145
msgid "The input values."
msgstr ""

#: includes/webapi/v2/includes/controllers/class-controller-form-submissions.php:149
msgid "The field values."
msgstr ""

#: includes/webapi/v2/includes/controllers/class-controller-forms.php:238
msgid "Invalid form id."
msgstr ""

#: includes/webapi/v2/includes/controllers/class-controller-forms.php:257
msgid "The form has already been deleted."
msgstr ""

#: includes/webapi/v2/includes/controllers/class-controller-forms.php:399
msgid "The Form object must be sent as a JSON string in the request body with the content-type header set to application/json."
msgstr ""

#: includes/webapi/v2/includes/controllers/class-controller-notes.php:78
#: includes/webapi/v2/includes/controllers/class-controller-notes.php:225
msgid "Invalid note id."
msgstr ""

#: includes/webapi/webapi.php:420
msgid "Read"
msgstr ""

#: includes/webapi/webapi.php:421
msgid "Write"
msgstr ""

#: includes/webapi/webapi.php:422
msgid "Read/Write"
msgstr ""

#: includes/webapi/webapi.php:434
msgid "Consumer Key"
msgstr ""

#: includes/webapi/webapi.php:440
msgid "Consumer Secret"
msgstr ""

#: includes/webapi/webapi.php:456
msgid "Gravity Forms API Settings"
msgstr ""

#: includes/webapi/webapi.php:529
#: includes/webapi/webapi.php:550
msgid "The Gravity Forms API allows developers to interact with this install via a JSON REST API."
msgstr ""

#: includes/webapi/webapi.php:533
msgid "Requirements check"
msgstr ""

#: includes/webapi/webapi.php:554
msgid "Enable access to the API"
msgstr ""

#: includes/webapi/webapi.php:565
msgid "Authentication ( API version 2 )"
msgstr ""

#: includes/webapi/webapi.php:567
msgid "Create an API Key below to use the REST API version 2. Alternatively, you can use cookie authentication which is supported for logged in users. %sVisit our documentation pages%s for more information."
msgstr ""

#: includes/webapi/webapi.php:572
msgid "API Keys"
msgstr ""

#: includes/webapi/webapi.php:578
msgid "Authentication ( API version 1 )"
msgstr ""

#: includes/webapi/webapi.php:580
msgid "Configure your API Key below to use the REST API version 1. Alternatively, you can use cookie authentication which is supported for logged in users. %sVisit our documentation pages%s for more information."
msgstr ""

#: includes/webapi/webapi.php:585
msgid "Public API Key"
msgstr ""

#: includes/webapi/webapi.php:593
msgid "Private API Key"
msgstr ""

#: includes/webapi/webapi.php:601
msgid "QR Code"
msgstr ""

#: includes/webapi/webapi.php:607
msgid "Impersonate account"
msgstr ""

#: includes/webapi/webapi.php:684
msgid "Permalinks are not in the correct format."
msgstr ""

#: includes/webapi/webapi.php:689
msgid "Change the %sWordPress Permalink Settings%s from default to any of the other options to get started."
msgstr ""

#: includes/webapi/webapi.php:697
msgid "Show/hide QR Code"
msgstr ""

#: includes/webapi/webapi.php:1076
msgid "Unable to retrieve key."
msgstr ""

#: includes/webapi/webapi.php:1116
msgid "You must provide a description."
msgstr ""

#: includes/webapi/webapi.php:1123
msgid "API Key successfully updated."
msgstr ""

#: includes/webapi/webapi.php:1125
msgid "Make sure you have copied the consumer key and secret below. They will not be available once you leave this page."
msgstr ""

#: includes/webapi/webapi.php:1127
msgid "Unable to save API key."
msgstr ""

#: includes/webapi/webapi.php:1132
msgid "Unable to process request."
msgstr ""

#: includes/webapi/webapi.php:1280
msgid "Feeds deleted successfully: %d"
msgstr ""

#: includes/webapi/webapi.php:1319
msgid "Feeds updated: %d"
msgstr ""

#: includes/webapi/webapi.php:1436
msgid "Forms deleted successfully: %d"
msgstr ""

#: includes/webapi/webapi.php:1498
msgid "Entries updated successfully"
msgstr ""

#: includes/webapi/webapi.php:1527
#: includes/webapi/webapi.php:1558
msgid "Success"
msgstr ""

#: includes/webapi/webapi.php:1619
msgid "Forms updated successfully"
msgstr ""

#: includes/webapi/webapi.php:1619
msgid "Form updated successfully"
msgstr ""

#: includes/webapi/webapi.php:1656
msgid "Entries deleted successfully: %d"
msgstr ""

#: includes/webapi/webapi.php:2358
msgid "Not authorized"
msgstr ""

#: includes/webapi/webapi.php:2363
msgid "Permission denied"
msgstr ""

#: includes/webapi/webapi.php:2368
msgid "Forbidden"
msgstr ""

#: includes/webapi/webapi.php:2373
msgid "Bad request"
msgstr ""

#: includes/webapi/webapi.php:2378
msgid "Not found"
msgstr ""

#: includes/webapi/webapi.php:2383
msgid "Not implemented"
msgstr ""

#: includes/webapi/webapi.php:2388
msgid "Internal Error"
msgstr ""

#: includes/wizard/steps/class-gf-installation-wizard-step-background-updates.php:18
msgid "Gravity Forms will download important bug fixes, security enhancements and plugin updates automatically. Updates are extremely important to the security of your WordPress site."
msgstr ""

#: includes/wizard/steps/class-gf-installation-wizard-step-background-updates.php:24
msgid "This feature is activated by default unless you opt to disable it below. We only recommend disabling background updates if you intend on managing updates manually."
msgstr ""

#: includes/wizard/steps/class-gf-installation-wizard-step-background-updates.php:35
msgid "Updates will only be available if you have entered a valid License Key"
msgstr ""

#: includes/wizard/steps/class-gf-installation-wizard-step-background-updates.php:46
msgid "Keep background updates enabled"
msgstr ""

#: includes/wizard/steps/class-gf-installation-wizard-step-background-updates.php:52
msgid "Turn off background updates"
msgstr ""

#: includes/wizard/steps/class-gf-installation-wizard-step-background-updates.php:58
msgid "Are you sure?"
msgstr ""

#: includes/wizard/steps/class-gf-installation-wizard-step-background-updates.php:61
msgid "By disabling background updates your site may not get critical bug fixes and security enhancements. We only recommend doing this if you are experienced at managing a WordPress site and accept the risks involved in manually keeping your WordPress site updated."
msgstr ""

#: includes/wizard/steps/class-gf-installation-wizard-step-background-updates.php:66
msgid "I understand and accept the risk of not enabling background updates."
msgstr ""

#: includes/wizard/steps/class-gf-installation-wizard-step-background-updates.php:94
msgid "Background Updates"
msgstr ""

#: includes/wizard/steps/class-gf-installation-wizard-step-background-updates.php:102
msgid "Please accept the terms."
msgstr ""

#: includes/wizard/steps/class-gf-installation-wizard-step-complete.php:12
msgid "Congratulations! Click the 'Create A Form' button to get started."
msgstr ""

#: includes/wizard/steps/class-gf-installation-wizard-step-complete.php:19
msgid "Installation Complete"
msgstr ""

#: includes/wizard/steps/class-gf-installation-wizard-step-complete.php:23
msgid "Create A Form"
msgstr ""

#: includes/wizard/steps/class-gf-installation-wizard-step-license-key.php:21
msgid "Enter your Gravity Forms License Key below.  Your key unlocks access to automatic updates, the add-on installer, and support.  You can find your key on the My Account page on the %sGravity Forms%s site."
msgstr ""

#: includes/wizard/steps/class-gf-installation-wizard-step-license-key.php:25
msgid "Enter Your License Key"
msgstr ""

#: includes/wizard/steps/class-gf-installation-wizard-step-license-key.php:39
msgid "If you don't enter a valid license key, you will not be able to update Gravity Forms when important bug fixes and security enhancements are released. This can be a serious security risk for your site."
msgstr ""

#: includes/wizard/steps/class-gf-installation-wizard-step-license-key.php:44
msgid "I understand the risks of not providing a valid license key."
msgstr ""

#: includes/wizard/steps/class-gf-installation-wizard-step-license-key.php:62
msgid "Please enter a valid license key."
msgstr ""

#: includes/wizard/steps/class-gf-installation-wizard-step-license-key.php:68
msgid "Invalid or Expired Key : Please make sure you have entered the correct value and that your key is not expired."
msgstr ""

#: includes/wizard/steps/class-gf-installation-wizard-step-license-key.php:75
msgid "Please accept the terms"
msgstr ""

#: includes/wizard/steps/class-gf-installation-wizard-step-settings.php:47
#: includes/wizard/steps/class-gf-installation-wizard-step-settings.php:58
msgid "On"
msgstr ""

#: includes/wizard/steps/class-gf-installation-wizard-step-settings.php:48
#: includes/wizard/steps/class-gf-installation-wizard-step-settings.php:59
msgid "Off"
msgstr ""

#: includes/wizard/steps/class-gf-installation-wizard-step-settings.php:50
msgid "Set this to ON to prevent extraneous scripts and styles from being printed on Gravity Forms admin pages, reducing conflicts with other plugins and themes."
msgstr ""

#: includes/wizard/steps/class-gf-installation-wizard-step-settings.php:55
#: settings.php:567
msgid "Toolbar Menu"
msgstr ""

#: includes/wizard/steps/class-gf-installation-wizard-step-settings.php:61
msgid "Set this to ON to display the Forms menu in the WordPress top toolbar. The Forms menu will display the latest ten forms recently opened in the form editor."
msgstr ""

#: includes/wizard/steps/class-gf-installation-wizard-step-settings.php:68
#: settings.php:624
#: tooltips.php:156
msgid "Akismet Integration"
msgstr ""

#: includes/wizard/steps/class-gf-installation-wizard-step-settings.php:74
#: settings.php:625
#: tooltips.php:156
msgid "Protect your form entries from spam using Akismet."
msgstr ""

#: includes/wizard/steps/class-gf-installation-wizard-step-settings.php:84
msgid "Global Settings"
msgstr ""

#: includes/wizard/steps/class-gf-installation-wizard-step.php:106
msgid "Back"
msgstr ""

#: js.php:11
msgid "Delete this custom choice list? 'OK' to delete, 'Cancel' to abort."
msgstr ""

#: js.php:25
msgid "Item has been deleted."
msgstr ""

#: js.php:32
msgid "Please enter name."
msgstr ""

#: js.php:36
msgid "This custom choice name is already in use. Please enter another name."
msgstr ""

#: js.php:55
msgid "Item has been saved."
msgstr ""

#: js.php:117
msgid "Completed"
msgstr ""

#: js.php:146
msgid "Select a category"
msgstr ""

#: js.php:164
msgid "Parameter Name:"
msgstr ""

#: js.php:169
msgid "Parameter Name"
msgstr ""

#: js.php:190
msgid "Default Value:"
msgstr ""

#: js.php:214
msgid "Placeholder:"
msgstr ""

#: js.php:240
msgid "Autocomplete Attribute:"
msgstr ""

#: js.php:244
#: tooltips.php:97
msgid "Autocomplete Attribute"
msgstr ""

#: js.php:271
msgid "Sub-Label:"
msgstr ""

#: js.php:279
msgid "Custom Sub-Label"
msgstr ""

#: js.php:312
#: js.php:315
#: js.php:329
msgid "MM"
msgstr ""

#: js.php:317
msgid "DD"
msgstr ""

#: js.php:319
msgid "YYYY"
msgstr ""

#: js.php:328
#: js.php:1082
msgid "Hour"
msgstr ""

#: js.php:330
#: js.php:1083
msgid "Minute"
msgstr ""

#: js.php:362
msgid "Same as previous"
msgstr ""

#: js.php:429
msgid "is no longer necessary."
msgstr ""

#: js.php:429
#: js.php:1547
#: js.php:1596
msgid "Learn more"
msgstr ""

#: js.php:465
msgid "This field is not associated with a product. Please add a Product Field to the form."
msgstr ""

#: js.php:483
msgid "Deleted Field"
msgstr ""

#: js.php:514
msgid "Column 1"
msgstr ""

#: js.php:514
msgid "Column 2"
msgstr ""

#: js.php:514
msgid "Column 3"
msgstr ""

#: js.php:529
msgid "The form title you have entered is already taken. Please enter a unique form title"
msgstr ""

#: js.php:535
msgid "Please enter a Title for this form. When adding the form to a page or post, you will have the option to hide the title."
msgstr ""

#: js.php:546
msgid "Your form currently has one or more pages without any fields in it. Blank pages are a result of Page Breaks that are positioned as the first or last field in the form or right after each other. Please adjust your Page Breaks and try again."
msgstr ""

#: js.php:554
msgid ""
"Your form currently has a product field with a blank label.\n"
"Please enter a label for all product fields."
msgstr ""

#: js.php:563
msgid ""
"Your form currently has an option field without a product field.\n"
"You must add a product field to your form."
msgstr ""

#: js.php:618
msgid "You are about to move this form to the trash. 'Cancel' to stop, 'OK' to move to trash."
msgstr ""

#: js.php:636
msgid "Section Break"
msgstr ""

#: js.php:652
msgid "HTML Block"
msgstr ""

#: js.php:678
#: js.php:692
#: js.php:706
#: js.php:993
msgid "Untitled"
msgstr ""

#: js.php:681
#: js.php:697
#: js.php:698
#: js.php:717
#: js.php:718
msgid "First Choice"
msgstr ""

#: js.php:681
#: js.php:697
#: js.php:698
#: js.php:717
#: js.php:718
msgid "Second Choice"
msgstr ""

#: js.php:681
#: js.php:697
#: js.php:698
#: js.php:717
#: js.php:718
msgid "Third Choice"
msgstr ""

#: js.php:835
msgid "Hidden Field"
msgstr ""

#: js.php:839
msgid "Post Title"
msgstr ""

#: js.php:843
msgid "Post Body"
msgstr ""

#: js.php:847
msgid "Post Excerpt"
msgstr ""

#: js.php:852
msgid "Post Tags"
msgstr ""

#: js.php:859
msgid "Post Custom Field"
msgstr ""

#: js.php:878
msgid "Product Name"
msgstr ""

#: js.php:924
msgid "First Option"
msgstr ""

#: js.php:924
msgid "Second Option"
msgstr ""

#: js.php:924
msgid "Third Option"
msgstr ""

#: js.php:935
msgid "Donation"
msgstr ""

#: js.php:979
msgid "I agree to the privacy policy."
msgstr ""

#: js.php:980
msgid "Enter consent agreement text here.  The Consent Field will store this agreement text with the form entry in order to track what the user has consented to."
msgstr ""

#: js.php:1165
msgid "Only one reCAPTCHA field can be added to the form"
msgstr ""

#: js.php:1172
msgid "Only one Shipping field can be added to the form"
msgstr ""

#: js.php:1179
msgid "Only one Post Content field can be added to the form"
msgstr ""

#: js.php:1185
msgid "Only one Post Title field can be added to the form"
msgstr ""

#: js.php:1191
msgid "Only one Post Excerpt field can be added to the form"
msgstr ""

#: js.php:1197
msgid "Only one credit card field can be added to the form"
msgstr ""

#: js.php:1204
msgid "You must add a product field to the form first"
msgstr ""

#: js.php:1240
msgid "Ajax error while adding field"
msgstr ""

#: js.php:1371
msgid "Ajax error while changing input type"
msgstr ""

#: js.php:1439
#: js.php:1472
msgid "Add choice"
msgstr ""

#: js.php:1442
#: js.php:1475
msgid "Delete choice"
msgstr ""

#: js.php:1507
msgid "Select a field"
msgstr ""

#: js.php:1526
msgid "The Multi Select field type is hard to use for people who cannot use a mouse. Please select a different field type to improve the accessibility of your form."
msgstr ""

#: js.php:1527
msgid "Users can enter a date in the field without using the date picker. Display the date format so they know what is the specified format."
msgstr ""

#: js.php:1528
msgid "The datepicker is not accessible for users who rely on the keyboard or screen reader. Please select a different input type to improve the accessibility of your form."
msgstr ""

#: js.php:1529
msgid "The Enhanced User Interface is not accessible for screen reader users and people who cannot use a mouse."
msgstr ""

#: js.php:1530
msgid "Hiding the label can make it difficult for users to fill out your form. Please keep the label visible to improve the accessibility of your form."
msgstr ""

#: js.php:1531
msgid "The image button is not accessible for users who rely on a screen reader. Please use a text button to improve the accessibility of your form."
msgstr ""

#. translators: 1. Open abbr tag 2. Close abbr tag
#: js.php:1535
msgid "To better comply with %1$sWCAG%2$s, we use the placeholder or description as a hidden label for screen readers."
msgstr ""

#: js.php:1551
msgid "This field has accessibility issues."
msgstr ""

#. translators: 1. Open abbr tag 2. Close abbr tag
#: js.php:1588
msgid "An empty label violates %1$sWCAG%2$s. Please use descriptive text for your label. To hide the label, use the \"Field Label Visibility\" setting."
msgstr ""

#: js.php:1600
msgid "This field has errors."
msgstr ""

#: js.php:1618
msgid "The submit button can't be placed inline on multi-page forms."
msgstr ""

#: js.php:1619
msgid "If a valid image url is not present a text-only submit button will be used."
msgstr ""

#: notification.php:187
msgid "Warning! Using a third-party email in the From Email field may prevent your notification from being delivered. It is best to use an email with the same domain as your website. %sMore details in our documentation.%s"
msgstr ""

#: notification.php:220
msgid "Configure Routing"
msgstr ""

#: notification.php:241
msgid "Email Service"
msgstr ""

#: notification.php:249
#: notification.php:1307
msgid "Event"
msgstr ""

#: notification.php:258
msgid "Send To Email"
msgstr ""

#: notification.php:300
msgid "Send To Field"
msgstr ""

#: notification.php:304
msgid "Your form does not have an email field. Add an email field to your form and try again."
msgstr ""

#: notification.php:334
msgid "Please select an Email Address field."
msgstr ""

#: notification.php:354
#: tooltips.php:13
msgid "From Name"
msgstr ""

#: notification.php:361
msgid "From Email"
msgstr ""

#: notification.php:369
msgid "Please enter a valid email address or merge tag in the From Email field."
msgstr ""

#: notification.php:375
#: tooltips.php:14
msgid "Reply To"
msgstr ""

#: notification.php:381
msgid "Please enter a valid email address or merge tag in the Reply To field."
msgstr ""

#: notification.php:387
msgid "CC"
msgstr ""

#: notification.php:407
msgid "Please enter a valid email address or merge tag in the CC field."
msgstr ""

#: notification.php:413
msgid "BCC"
msgstr ""

#: notification.php:419
msgid "Please enter a valid email address or merge tag in the BCC field."
msgstr ""

#: notification.php:425
#: notification.php:1303
msgid "Subject"
msgstr ""

#: notification.php:439
msgid "Auto-formatting"
msgstr ""

#: notification.php:451
#: tooltips.php:17
msgid "Attachments"
msgstr ""

#: notification.php:457
msgid "Attach uploaded fields to notification"
msgstr ""

#: notification.php:477
msgid "Update Notification"
msgstr ""

#: notification.php:707
msgid "Save & Continue Link"
msgstr ""

#: notification.php:711
msgid "Save & Continue Token"
msgstr ""

#: notification.php:948
msgid "Notification deleted."
msgstr ""

#: notification.php:950
msgid "There was an issue deleting this notification."
msgstr ""

#: notification.php:956
msgid "Notification duplicated."
msgstr ""

#: notification.php:958
msgid "There was an issue duplicating this notification."
msgstr ""

#: notification.php:1004
msgid "Form is submitted"
msgstr ""

#: notification.php:1006
msgid "Form is saved"
msgstr ""

#: notification.php:1007
msgid "Save and continue email is requested"
msgstr ""

#: notification.php:1311
msgid "Service"
msgstr ""

#: notification.php:1536
msgid "WARNING: You are about to delete this notification."
msgstr ""

#: notification.php:1591
msgid "Undefined Service"
msgstr ""

#: notification.php:1622
msgid "This form doesn't have any notifications. Let's go %screate one%s."
msgstr ""

#: preview.php:22
msgid "You don't have adequate permission to preview forms."
msgstr ""

#: preview.php:50
msgid "Form Preview - Gravity Forms"
msgstr ""

#: preview.php:111
msgid "display grid"
msgstr ""

#: preview.php:112
msgid "show structure"
msgstr ""

#: preview.php:115
msgid "Form Preview"
msgstr ""

#: preview.php:119
msgid "Note: This is a simple form preview. This form may display differently when added to your page based on normal inheritance from parent theme styles."
msgstr ""

#: preview.php:119
msgid "dismiss"
msgstr ""

#: print-entry.php:27
msgid "You don't have adequate permission to view entries."
msgstr ""

#: print-entry.php:176
msgid "Form Id and Entry Id are required parameters."
msgstr ""

#: print-entry.php:199
msgid "Bulk Print"
msgstr ""

#: print-entry.php:237
msgid "close window"
msgstr ""

#: print-entry.php:237
msgid "Print Preview"
msgstr ""

#: select_columns.php:48
msgid "Oops! We could not locate your form. Please try again."
msgstr ""

#: select_columns.php:211
msgid "Active Columns"
msgstr ""

#: select_columns.php:224
msgid "Inactive Columns"
msgstr ""

#: settings.php:172
msgid "You don't have adequate permission to uninstall Gravity Forms."
msgstr ""

#: settings.php:246
msgid "Gravity Forms has been successfully uninstalled. It can be re-activated from the %splugins page%s."
msgstr ""

#: settings.php:261
msgid "This operation deletes ALL Gravity Forms settings. If you continue, you will NOT be able to retrieve these settings."
msgstr ""

#: settings.php:272
msgid "Warning! ALL Gravity Forms data, including form entries will be deleted. This cannot be undone. 'OK' to delete, 'Cancel' to stop"
msgstr ""

#: settings.php:335
msgid "%s uninstalled. It can be re-activated from the %splugins page%s."
msgstr ""

#: settings.php:402
msgid "A valid license key is required for access to automatic plugin upgrades and product support."
msgstr ""

#: settings.php:406
msgid "License key is managed by the administrator of this network"
msgstr ""

#: settings.php:411
msgid "Support License Key"
msgstr ""

#: settings.php:417
msgid "Paste Your License Key Here"
msgstr ""

#: settings.php:443
msgid "Your license key was not updated. "
msgstr ""

#: settings.php:480
msgid "Your License Details"
msgstr ""

#: settings.php:492
msgid "Default Currency"
msgstr ""

#: settings.php:497
msgid "Select the default currency for your forms. This is used for product fields, credit card fields and others."
msgstr ""

#: settings.php:512
msgid "Logging"
msgstr ""

#: settings.php:513
msgid "Enable if you would like logging within Gravity Forms. Logging allows you to easily debug the inner workings of Gravity Forms to solve any possible issues. "
msgstr ""

#: settings.php:519
msgid "Enable Logging"
msgstr ""

#: settings.php:536
msgid "Default Form Theme"
msgstr ""

#: settings.php:544
#: assets/js/src/admin/block-editor/blocks/form/edit.js:332
msgid "Gravity Forms 2.5 Theme"
msgstr ""

#: settings.php:549
msgid "Orbital Theme (Recommended)"
msgstr ""

#: settings.php:554
msgid "This theme will be used by default everywhere forms are embedded on your site."
msgstr ""

#: settings.php:554
msgid "Learn more about form theme and style settings"
msgstr ""

#: settings.php:554
msgid "Learn more about form theme and style settings."
msgstr ""

#: settings.php:568
msgid "Enable to display the forms menu in the WordPress top toolbar. The forms menu will display the ten forms recently opened in the form editor."
msgstr ""

#: settings.php:574
msgid "Enable Toolbar Menu"
msgstr ""

#: settings.php:586
msgid "Automatic Background Updates"
msgstr ""

#: settings.php:587
msgid "Enable to allow Gravity Forms to download and install bug fixes and security updates automatically in the background. Requires a valid license key."
msgstr ""

#: settings.php:593
msgid "Enable Automatic Background Updates"
msgstr ""

#: settings.php:605
#: settings.php:612
msgid "No Conflict Mode"
msgstr ""

#: settings.php:606
msgid "Enable to prevent extraneous scripts and styles from being printed on a Gravity Forms admin pages, reducing conflicts with other plugins and themes."
msgstr ""

#: settings.php:632
msgid "Enable Akismet Integration"
msgstr ""

#: settings.php:645
msgid "Data Collection"
msgstr ""

#: settings.php:653
msgid "Enable Data Collection"
msgstr ""

#: settings.php:675
msgid "Output Default CSS"
msgstr ""

#: settings.php:677
msgid "Enable this option to output the default form CSS. Disable it if you plan to create your own CSS in a child theme. Note: after Gravity Forms 2.8, this setting will no longer appear on the settings page. If you previously had it enabled, you will need to use the %sgform_disable_css%s filter to disable it."
msgstr ""

#: settings.php:687
msgid "Disable CSS"
msgstr ""

#: settings.php:735
msgid "Please enter a valid license key to see details."
msgstr ""

#: settings.php:752
msgid "Days Left"
msgstr ""

#: settings.php:759
#: settings.php:769
msgid "License Type"
msgstr ""

#: settings.php:760
#: settings.php:772
msgid "License Status"
msgstr ""

#: settings.php:761
#: settings.php:785
msgid "Purchase Date"
msgstr ""

#: settings.php:762
#: settings.php:788
msgid "License Activations"
msgstr ""

#: settings.php:918
msgid "Settings: General"
msgstr ""

#: settings.php:985
msgid "reCAPTCHA Settings"
msgstr ""

#: settings.php:988
msgid "Gravity Forms integrates with reCAPTCHA, a free CAPTCHA service that uses an advanced risk analysis engine and adaptive challenges to keep automated software from engaging in abusive activities on your site. "
msgstr ""

#: settings.php:989
msgid "Please note, only v2 keys are supported and checkbox keys are not compatible with invisible reCAPTCHA."
msgstr ""

#: settings.php:990
msgid "These settings are required only if you decide to use the reCAPTCHA field."
msgstr ""

#: settings.php:991
msgid "Get your reCAPTCHA Keys."
msgstr ""

#: settings.php:997
msgid "Site Key"
msgstr ""

#: settings.php:1007
msgid "Secret Key"
msgstr ""

#: settings.php:1028
msgid "Invisible"
msgstr ""

#: settings.php:1035
msgid "Validate Keys"
msgstr ""

#: settings.php:1070
#: settings.php:1091
msgid "reCAPTCHA keys are invalid."
msgstr ""

#: settings.php:1141
msgid "Please complete the reCAPTCHA widget to validate your reCAPTCHA keys:"
msgstr ""

#: settings.php:1259
msgid "reCAPTCHA"
msgstr ""

#: tooltips.php:9
msgid "Send To Email Address"
msgstr ""

#: tooltips.php:9
msgid "Enter the email address you would like the notification email sent to."
msgstr ""

#: tooltips.php:10
#: tooltips.php:37
msgid "Disable Auto-Formatting"
msgstr ""

#: tooltips.php:10
msgid "When enabled, auto-formatting will insert paragraph breaks automatically. Disable auto-formatting when using HTML to create email notification content."
msgstr ""

#: tooltips.php:11
msgid "Routing"
msgstr ""

#: tooltips.php:11
msgid "Allows notification to be sent to different email addresses depending on values selected in the form."
msgstr ""

#: tooltips.php:12
msgid "From Email Address"
msgstr ""

#: tooltips.php:12
msgid "Enter an authorized email address you would like the notification email sent from. To avoid deliverability issues, always use your site domain in the from email."
msgstr ""

#: tooltips.php:13
msgid "Enter the name you would like the notification email sent from, or select the name from available name fields."
msgstr ""

#: tooltips.php:14
msgid "Enter the email address you would like to be used as the reply to address for the notification email."
msgstr ""

#: tooltips.php:15
msgid "Carbon Copy Addresses"
msgstr ""

#: tooltips.php:15
msgid "Enter a comma separated list of email addresses you would like to receive a CC of the notification email."
msgstr ""

#: tooltips.php:16
msgid "Blind Carbon Copy Addresses"
msgstr ""

#: tooltips.php:16
msgid "Enter a comma separated list of email addresses you would like to receive a BCC of the notification email."
msgstr ""

#: tooltips.php:17
msgid "When enabled, any files uploaded to File Upload fields will be attached to the notification email."
msgstr ""

#: tooltips.php:18
msgid "Limit Form Activity"
msgstr ""

#: tooltips.php:18
msgid "Limit the number of entries a form can generate and/or schedule a time period the form is active."
msgstr ""

#: tooltips.php:19
msgid "Limit Number of Entries"
msgstr ""

#: tooltips.php:19
msgid "Enter a number in the input box below to limit the number of entries allowed for this form. The form will become inactive when that number is reached."
msgstr ""

#: tooltips.php:20
msgid "Schedule a time period the form is active."
msgstr ""

#: tooltips.php:21
msgid "Enable Anti-spam honeypot"
msgstr ""

#: tooltips.php:21
msgid "Enables the honeypot spam protection technique, which is an alternative to the reCAPTCHA field."
msgstr ""

#: tooltips.php:22
msgid "Enable Animation"
msgstr ""

#: tooltips.php:22
msgid "Check this option to enable a sliding animation when displaying/hiding conditional logic fields."
msgstr ""

#: tooltips.php:23
msgid "Legacy Markup"
msgstr ""

#: tooltips.php:23
msgid "Check this option to enable Gravity Forms' legacy markup. This will hinder the accessibility of your form."
msgstr ""

#: tooltips.php:24
msgid "Enter the title of your form."
msgstr ""

#: tooltips.php:25
msgid "Enter a description for your form. This may be used for user instructions."
msgstr ""

#: tooltips.php:26
msgid "Form Label Placement"
msgstr ""

#: tooltips.php:26
msgid "Select the default label placement.  Labels can be top aligned above a field, left aligned to the left of a field, or right aligned to the right of a field. This is a global label placement setting."
msgstr ""

#: tooltips.php:27
msgid "Select the default description placement.  Descriptions can be placed above the field inputs or below the field inputs. This setting can be overridden in the appearance settings for each field."
msgstr ""

#: tooltips.php:28
msgid "Select the default validation message placement.  Validation messages can be placed above the field inputs or below the field inputs."
msgstr ""

#: tooltips.php:29
msgid "Select the default sub-label placement.  Sub-labels can be placed above the field inputs or below the field inputs. This setting can be overridden in the appearance settings for each field."
msgstr ""

#: tooltips.php:30
msgid "Required Indicator"
msgstr ""

#: tooltips.php:31
msgid "Form Button Text"
msgstr ""

#: tooltips.php:31
msgid "Enter the text you would like to appear on the form submit button."
msgstr ""

#: tooltips.php:32
msgid "Form Button Image"
msgstr ""

#: tooltips.php:32
msgid "Enter the path to an image you would like to use as the form submit button."
msgstr ""

#: tooltips.php:33
msgid "Form CSS Class Name"
msgstr ""

#: tooltips.php:33
msgid "Enter the CSS class name you would like to use in order to override the default styles for this form."
msgstr ""

#: tooltips.php:34
msgid "Enter the URL of a custom image to replace the default 'add item' icon. A maximum size of 16px by 16px is recommended"
msgstr ""

#: tooltips.php:35
msgid "Enter the URL of a custom image to replace the default 'delete item' icon. A maximum size of 16px by 16px is recommended"
msgstr ""

#: tooltips.php:36
msgid "Confirmation Message Text"
msgstr ""

#: tooltips.php:36
msgid "Enter the text you would like the user to see on the confirmation page of this form."
msgstr ""

#: tooltips.php:37
msgid "When enabled, auto-formatting will insert paragraph breaks automatically. Disable auto-formatting when using HTML to create the confirmation content."
msgstr ""

#: tooltips.php:38
msgid "Redirect Form to Page"
msgstr ""

#: tooltips.php:38
msgid "Select the page you would like the user to be redirected to after they have submitted the form."
msgstr ""

#: tooltips.php:39
msgid "Redirect Form to URL"
msgstr ""

#: tooltips.php:39
msgid "Enter the URL of the webpage you would like the user to be redirected to after they have submitted the form."
msgstr ""

#. Translators: %s: Link to article about query strings.
#: tooltips.php:41
msgid "Pass Data Via Query String"
msgstr ""

#. Translators: %s: Link to article about query strings.
#: tooltips.php:41
msgid "To pass field data to the confirmation page, build a Query String using the 'Insert Merge Tag' drop down. %s..more info on querystrings &raquo;%s"
msgstr ""

#: tooltips.php:42
msgid "Enter the label of the form field.  This is the field title the user will see when filling out the form."
msgstr ""

#: tooltips.php:43
msgid "Enter the label for this HTML block. It will help you identify your HTML blocks in the form editor, but it will not be displayed on the form."
msgstr ""

#: tooltips.php:44
msgid "Disable Default Margins"
msgstr ""

#: tooltips.php:44
msgid "When enabled, margins are added to properly align the HTML content with other form fields."
msgstr ""

#: tooltips.php:45
msgid "reCAPTCHA Theme"
msgstr ""

#: tooltips.php:45
msgid "Select the visual theme for the reCAPTCHA field from the available options to better match your site design."
msgstr ""

#: tooltips.php:46
msgid "CAPTCHA Type"
msgstr ""

#: tooltips.php:46
msgid "Select the type of CAPTCHA you would like to use."
msgstr ""

#: tooltips.php:47
msgid "CAPTCHA Badge Position"
msgstr ""

#: tooltips.php:47
msgid "Select the position of the badge containing the links to Google's privacy policy and terms."
msgstr ""

#: tooltips.php:48
msgid "Select the custom field name from available existing custom fields, or enter a new custom field name."
msgstr ""

#: tooltips.php:49
msgid "Field type"
msgstr ""

#: tooltips.php:49
msgid "Select the type of field from the available form fields."
msgstr ""

#: tooltips.php:50
msgid "Enter the maximum number of characters that this field is allowed to have."
msgstr ""

#: tooltips.php:51
msgid "Enter the maximum number of rows that users are allowed to add."
msgstr ""

#: tooltips.php:52
msgid "Select the type of inputs you would like to use for the date field. Date Picker will let users select a date from a calendar. Date Field will let users free type the date."
msgstr ""

#: tooltips.php:53
msgid "Select the type of address you would like to use."
msgstr ""

#: tooltips.php:54
msgid "Default State"
msgstr ""

#: tooltips.php:54
msgid "Select the state you would like to be selected by default when the form gets displayed."
msgstr ""

#: tooltips.php:55
msgid "Default Province"
msgstr ""

#: tooltips.php:55
msgid "Select the province you would like to be selected by default when the form gets displayed."
msgstr ""

#: tooltips.php:56
msgid "Select the country you would like to be selected by default when the form gets displayed."
msgstr ""

#: tooltips.php:57
msgid "Hide Country"
msgstr ""

#: tooltips.php:57
msgid "For addresses that only apply to one country, you can choose to not display the country drop down. Entries will still be recorded with the selected country."
msgstr ""

#: tooltips.php:58
msgid "Hide Address Line 2"
msgstr ""

#: tooltips.php:58
msgid "Check this box to prevent the extra address input (Address Line 2) from being displayed in the form."
msgstr ""

#: tooltips.php:59
msgid "Hide State Field"
msgstr ""

#: tooltips.php:59
msgid "Check this box to prevent the State field from being displayed in the form."
msgstr ""

#: tooltips.php:60
msgid "Hide Province Field"
msgstr ""

#: tooltips.php:60
msgid "Check this box to prevent Province field from being displayed in the form."
msgstr ""

#: tooltips.php:61
msgid "Hide State/Province/Region"
msgstr ""

#: tooltips.php:61
msgid "Check this box to prevent the State/Province/Region from being displayed in the form."
msgstr ""

#: tooltips.php:62
msgid "Field Name Format"
msgstr ""

#: tooltips.php:62
msgid "Select the format you would like to use for the Name field.  There are 3 options, Normal which includes First and Last Name, Extended which adds Prefix and Suffix, or Simple which is a single input field."
msgstr ""

#: tooltips.php:63
msgid "Select the format of numbers that are allowed in this field. You have the option to use a comma or a dot as the decimal separator."
msgstr ""

#: tooltips.php:64
msgid "Check this box to prevent this field from being displayed in a non-secure page (i.e. not https://). It will redirect the page to the same URL, but starting with https:// instead. This option requires a properly configured SSL certificate."
msgstr ""

#: tooltips.php:65
msgid "Field Date Format"
msgstr ""

#: tooltips.php:65
msgid "Select the format you would like to use for the date input."
msgstr ""

#: tooltips.php:66
msgid "Select the format you would like to use for the time field.  Available options are 12 hour (i.e. 8:30 pm) and 24 hour (i.e. 20:30)."
msgstr ""

#: tooltips.php:67
msgid "Allowed File Extensions"
msgstr ""

#: tooltips.php:67
msgid "Enter the allowed file extensions for file uploads.  This will limit the type of files a user may upload."
msgstr ""

#: tooltips.php:68
msgid "Select this option to enable multiple files to be uploaded for this field."
msgstr ""

#: tooltips.php:69
msgid "Specify the maximum number of files that can be uploaded using this field. Leave blank for unlimited. Note that the actual number of files permitted may be limited by this server's specifications and configuration."
msgstr ""

#: tooltips.php:70
msgid "Specify the maximum file size in megabytes allowed for each of the files."
msgstr ""

#: tooltips.php:71
msgid "Phone Number Format"
msgstr ""

#: tooltips.php:71
msgid "Select the format you would like to use for the phone input.  Available options are domestic US/CANADA style phone number and international long format phone number."
msgstr ""

#: tooltips.php:72
msgid "Field Description"
msgstr ""

#: tooltips.php:72
msgid "Enter the description for the form field.  This will be displayed to the user and provide some direction on how the field should be filled out or selected."
msgstr ""

#: tooltips.php:73
msgid "Required Field"
msgstr ""

#: tooltips.php:73
msgid "Select this option to make the form field required.  A required field will prevent the form from being submitted if it is not filled out or selected."
msgstr ""

#: tooltips.php:74
msgid "Select this option to limit user input to unique values only.  This will require that a value entered in a field does not currently exist in the entry database for that field."
msgstr ""

#: tooltips.php:75
msgid "Hide Field Label"
msgstr ""

#: tooltips.php:75
msgid "Select this option to hide the field label in the form."
msgstr ""

#: tooltips.php:76
msgid "Number Range"
msgstr ""

#: tooltips.php:76
msgid "Enter the minimum and maximum values for this form field.  This will require that the value entered by the user must fall within this range."
msgstr ""

#: tooltips.php:77
msgid "Enabling calculations will allow the value of this field to be dynamically calculated based on a mathematical formula."
msgstr ""

#: tooltips.php:78
msgid "Specify a mathematical formula. The result of this formula will be dynamically populated as the value for this field."
msgstr ""

#: tooltips.php:79
msgid "Specify how many decimal places the number should be rounded to."
msgstr ""

#: tooltips.php:80
msgid "Admin Label"
msgstr ""

#: tooltips.php:80
msgid "Enter the admin label of the form field.  Entering a value in this field will override the Field Label when displayed in the Gravity Forms administration tool."
msgstr ""

#: tooltips.php:81
msgid "Enter values in this setting to override the Sub-Label for each field."
msgstr ""

#: tooltips.php:82
msgid "Label Visibility"
msgstr ""

#: tooltips.php:82
msgid "Select the label visibility for this field.  Labels can either inherit the form setting or be hidden."
msgstr ""

#: tooltips.php:83
msgid "Select the description placement.  Descriptions can be placed above the field inputs or below the field inputs."
msgstr ""

#: tooltips.php:84
msgid "Select the sub-label placement.  Sub-labels can be placed above the field inputs or below the field inputs."
msgstr ""

#: tooltips.php:85
msgid "Select a form field size from the available options. This will set the width of the field. Please note: if using a paragraph field, the size applies only to the height of the field."
msgstr ""

#: tooltips.php:86
msgid "Select the fields you'd like to use in this Name field and customize the Sub-Labels by entering new ones."
msgstr ""

#: tooltips.php:87
msgid "Name Prefix Choices"
msgstr ""

#: tooltips.php:87
msgid "Add Choices to this field. You can mark a choice as selected by default by using the radio buttons on the left."
msgstr ""

#: tooltips.php:88
msgid "Select the fields you'd like to use in this Address Field and customize the Sub-Labels by entering new ones."
msgstr ""

#: tooltips.php:89
#: tooltips.php:90
msgid "If you would like to pre-populate the value of a field, enter it here."
msgstr ""

#: tooltips.php:91
msgid "The Placeholder will not be submitted along with the form. Use the Placeholder to give a hint at the expected value or format."
msgstr ""

#: tooltips.php:92
msgid "Placeholders will not be submitted along with the form. Use Placeholders to give a hint at the expected value or format."
msgstr ""

#: tooltips.php:93
msgid "Use Values Submitted in a Different Field"
msgstr ""

#: tooltips.php:93
msgid "Activate this option to allow users to skip this field and submit the values entered in the associated field. For example, this is useful for shipping and billing address fields."
msgstr ""

#: tooltips.php:94
msgid "Enter the label to be displayed next to the check box. For example, &quot;same as shipping address&quot;."
msgstr ""

#: tooltips.php:95
msgid "Select the field to be used as the source for the values for this field."
msgstr ""

#: tooltips.php:96
msgid "Activated by Default"
msgstr ""

#: tooltips.php:96
msgid "Select this setting to display the option as activated by default when the form first loads."
msgstr ""

#: tooltips.php:97
msgid "Select this setting to let browsers help a user fill in a field with autocomplete.  You can enter a single autocomplete attribute or multiple attributes separated with a space.  Learn more about autocomplete in the %s accessibility documentation %s."
msgstr ""

#: tooltips.php:98
msgid "Validation Message"
msgstr ""

#: tooltips.php:98
msgid "If you would like to override the default error validation for a field, enter it here.  This message will be displayed if there is an error with this field when the user submits the form."
msgstr ""

#: tooltips.php:99
msgid "reCAPTCHA Language"
msgstr ""

#: tooltips.php:99
msgid "Select the language you would like to use for the reCAPTCHA display from the available options."
msgstr ""

#: tooltips.php:100
msgid "Enter the CSS class name you would like to use in order to override the default styles for this field."
msgstr ""

#: tooltips.php:102
msgid "Field Choices"
msgstr ""

#: tooltips.php:102
msgid "Define the choices for this field. If the field type supports it you will also be able to select the default choice(s) using a radio or checkbox located to the left of the choice."
msgstr ""

#: tooltips.php:103
msgid "Enable Choice Values"
msgstr ""

#: tooltips.php:103
msgid "Check this option to specify a value for each choice. Choice values are not displayed to the user viewing the form, but are accessible to administrators when viewing the entry."
msgstr ""

#: tooltips.php:104
msgid "Create rules to dynamically display or hide this field based on values from another field."
msgstr ""

#. Translators: %s: Link to Chosen jQuery framework.
#: tooltips.php:106
msgid "Enable Enhanced UI"
msgstr ""

#. Translators: %s: Link to Chosen jQuery framework.
#: tooltips.php:106
msgid "By selecting this option, the %s jQuery script will be applied to this field, enabling search capabilities to Drop Down fields and a more user-friendly interface for Multi Select fields."
msgstr ""

#: tooltips.php:107
msgid "Checkbox Text"
msgstr ""

#: tooltips.php:107
msgid "Text of the consent checkbox."
msgstr ""

#: tooltips.php:108
msgid "\"Select All\" Choice"
msgstr ""

#: tooltips.php:108
msgid "Check this option to add a \"Select All\" checkbox before the checkbox choices to allow users to check all the checkboxes with one click."
msgstr ""

#: tooltips.php:109
msgid "\"Other\" Choice"
msgstr ""

#: tooltips.php:109
msgid "Check this option to add a text input as the final choice of your radio button field. This allows the user to specify a value that is not a predefined choice."
msgstr ""

#: tooltips.php:110
msgid "Check this option to require a user to be logged in to view this form."
msgstr ""

#: tooltips.php:111
msgid "Enter a message to be displayed to users who are not logged in (shortcodes and HTML are supported)."
msgstr ""

#: tooltips.php:112
msgid "Page Conditional Logic"
msgstr ""

#: tooltips.php:112
msgid "Create rules to dynamically display or hide this page based on values from another field."
msgstr ""

#: tooltips.php:113
msgid "Select which type of visual progress indicator you would like to display.  Progress Bar, Steps or None."
msgstr ""

#: tooltips.php:114
msgid "Select which progress bar style you would like to use.  Select custom to choose your own text and background color."
msgstr ""

#: tooltips.php:115
msgid "Name each of the pages on your form.  Page names are displayed with the selected progress indicator."
msgstr ""

#: tooltips.php:116
msgid "Next Button Text"
msgstr ""

#: tooltips.php:116
msgid "Enter the text you would like to appear on the page next button."
msgstr ""

#: tooltips.php:117
msgid "Next Button Image"
msgstr ""

#: tooltips.php:117
msgid "Enter the path to an image you would like to use as the page next button."
msgstr ""

#: tooltips.php:118
msgid "Previous Button Text"
msgstr ""

#: tooltips.php:118
msgid "Enter the text you would like to appear on the page previous button."
msgstr ""

#: tooltips.php:119
msgid "Previous Button Image"
msgstr ""

#: tooltips.php:119
msgid "Enter the path to an image you would like to use as the page previous button."
msgstr ""

#: tooltips.php:120
msgid "Next Button Conditional Logic"
msgstr ""

#: tooltips.php:120
msgid "Create rules to dynamically display or hide the page's Next Button based on values from another field."
msgstr ""

#: tooltips.php:121
msgid "Create rules to dynamically display or hide the submit button based on values from another field."
msgstr ""

#: tooltips.php:122
msgid "Select which categories are displayed. You can choose to display all of them or select individual ones."
msgstr ""

#: tooltips.php:123
msgid "Select the post status that will be used for the post that is created by the form entry."
msgstr ""

#: tooltips.php:124
msgid "Post Author"
msgstr ""

#: tooltips.php:124
msgid "Select the author that will be used for the post that is created by the form entry."
msgstr ""

#: tooltips.php:125
msgid "Select the post format that will be used for the post that is created by the form entry."
msgstr ""

#: tooltips.php:126
msgid "Post Content Template"
msgstr ""

#: tooltips.php:126
msgid "Check this option to format and insert merge tags into the Post Content."
msgstr ""

#: tooltips.php:127
msgid "Post Title Template"
msgstr ""

#: tooltips.php:127
msgid "Check this option to format and insert merge tags into the Post Title."
msgstr ""

#: tooltips.php:128
msgid "Select the category that will be used for the post that is created by the form entry."
msgstr ""

#: tooltips.php:129
msgid "Use Current User as Author"
msgstr ""

#: tooltips.php:129
msgid "Selecting this option will set the post author to the WordPress user that submitted the form."
msgstr ""

#: tooltips.php:130
msgid "Image Meta"
msgstr ""

#: tooltips.php:130
msgid "Select one or more image metadata field to be displayed along with the image upload field. They enable users to enter additional information about the uploaded image."
msgstr ""

#: tooltips.php:131
msgid "Check this option to set this image as the post's Featured Image."
msgstr ""

#: tooltips.php:132
msgid "Incoming Field Data"
msgstr ""

#: tooltips.php:132
msgid "Check this option to enable data to be passed to the form and pre-populate this field dynamically. Data can be passed via Query Strings, Shortcode and/or Hooks."
msgstr ""

#: tooltips.php:133
msgid "Enter the content (Text or HTML) to be displayed on the form."
msgstr ""

#: tooltips.php:134
msgid "Base Price"
msgstr ""

#: tooltips.php:134
msgid "Enter the base price for this product."
msgstr ""

#: tooltips.php:135
msgid "Disable Quantity"
msgstr ""

#: tooltips.php:135
msgid "Disables the quantity field.  A quantity of 1 will be assumed or you can add a Quantity field to your form from the Pricing Fields."
msgstr ""

#: tooltips.php:136
msgid "Product Field"
msgstr ""

#: tooltips.php:136
msgid "Select which Product this field is tied to."
msgstr ""

#: tooltips.php:137
msgid "Input masks provide a visual guide allowing users to more easily enter data in a specific format such as dates and phone numbers."
msgstr ""

#: tooltips.php:138
msgid "Standard Fields provide basic form functionality."
msgstr ""

#: tooltips.php:139
msgid "Advanced Fields are for specific uses.  They enable advanced formatting of regularly used fields such as Name, Email, Address, etc."
msgstr ""

#: tooltips.php:140
msgid "Post Fields allow you to add fields to your form that create Post Drafts in WordPress from the submitted data."
msgstr ""

#: tooltips.php:141
msgid "Pricing fields allow you to add fields to your form that calculate pricing for selling goods and services."
msgstr ""

#: tooltips.php:142
msgid "Export Selected Form"
msgstr ""

#: tooltips.php:142
msgid "Select the form you would like to export entry data from. You may only export data from one form at a time."
msgstr ""

#: tooltips.php:143
msgid "Export Selected Forms"
msgstr ""

#: tooltips.php:143
msgid "Select the forms you would like to export."
msgstr ""

#: tooltips.php:144
msgid "Filter the entries by adding conditions."
msgstr ""

#: tooltips.php:145
msgid "Export Selected Fields"
msgstr ""

#: tooltips.php:145
msgid "Select the fields you would like to include in the export."
msgstr ""

#: tooltips.php:146
msgid "Export Date Range"
msgstr ""

#: tooltips.php:146
msgid "Select a date range. Setting a range will limit the export to entries submitted during that date range. If no range is set, all entries will be exported."
msgstr ""

#: tooltips.php:147
msgid "Click the file selection button to upload a Gravity Forms export file from your computer. Please make sure your file has the .json extension, and that it was generated by the Gravity Forms Export tool."
msgstr ""

#: tooltips.php:148
msgid "Settings License Key"
msgstr ""

#: tooltips.php:148
msgid "Your Gravity Forms support license key is used to verify your support package, enable automatic updates and receive support."
msgstr ""

#: tooltips.php:149
msgid "Select yes or no to enable or disable CSS output.  Setting this to no will disable the standard Gravity Forms CSS from being included in your theme."
msgstr ""

#: tooltips.php:150
msgid "Output HTML5"
msgstr ""

#: tooltips.php:150
msgid "Select yes or no to enable or disable HTML5 output. Setting this to no will disable the standard Gravity Forms HTML5 form field output."
msgstr ""

#: tooltips.php:151
msgid "Select On or Off to enable or disable no-conflict mode. Setting this to On will prevent extraneous scripts and styles from being printed on Gravity Forms admin pages, reducing conflicts with other plugins and themes."
msgstr ""

#: tooltips.php:152
msgid "reCAPTCHA Site Key"
msgstr ""

#: tooltips.php:152
msgid "Enter your reCAPTCHA Site Key, if you do not have a key you can register for one at the provided link.  reCAPTCHA is a free service."
msgstr ""

#: tooltips.php:153
msgid "reCAPTCHA Secret Key"
msgstr ""

#: tooltips.php:153
msgid "Enter your reCAPTCHA Secret Key, if you do not have a key you can register for one at the provided link.  reCAPTCHA is a free service."
msgstr ""

#: tooltips.php:154
msgid "reCAPTCHA Type"
msgstr ""

#: tooltips.php:154
msgid "Select the type of reCAPTCHA you would like to use."
msgstr ""

#: tooltips.php:155
msgid "Please select the currency for your location.  Currency is used for pricing fields and price calculations."
msgstr ""

#: tooltips.php:157
msgid "Entries Conversion"
msgstr ""

#: tooltips.php:157
msgid "Conversion is the percentage of form views that generated an entry. If a form was viewed twice, and one entry was generated, the conversion will be 50%."
msgstr ""

#: tooltips.php:158
msgid "Tab Index Start Value"
msgstr ""

#: tooltips.php:158
#: widget.php:172
msgid "If you have other forms on the page (i.e. Comments Form), specify a higher tabindex start value so that your Gravity Form does not end up with the same tabindices as your other forms. To disable the tabindex, enter 0 (zero)."
msgstr ""

#: tooltips.php:159
msgid "Override Notifications"
msgstr ""

#: tooltips.php:159
msgid "Enter a comma separated list of email addresses you would like to receive the selected notification emails."
msgstr ""

#: tooltips.php:160
msgid "Progress Bar Confirmation Display"
msgstr ""

#: tooltips.php:160
msgid "Check this box if you would like the progress bar to display with the confirmation text."
msgstr ""

#: tooltips.php:161
msgid "Progress Bar Completion Text"
msgstr ""

#: tooltips.php:161
msgid "Enter text to display at the top of the progress bar."
msgstr ""

#: tooltips.php:162
msgid "Use Rich Text Editor"
msgstr ""

#: tooltips.php:162
msgid "Check this box if you would like to use the rich text editor for this field."
msgstr ""

#: tooltips.php:163
msgid "Enable Personal Data Tools"
msgstr ""

#: tooltips.php:163
msgid "Check this box if you would like to include data from this form when exporting or erasing personal data on this site."
msgstr ""

#: tooltips.php:164
msgid "Identification"
msgstr ""

#: tooltips.php:164
msgid "Select the field which will be used to identify the owner of the personal data."
msgstr ""

#: tooltips.php:165
msgid "Select the fields which will be included when exporting or erasing personal data."
msgstr ""

#: tooltips.php:166
msgid "Check this box if you would like to prevent the IP address from being stored during form submission."
msgstr ""

#: tooltips.php:167
msgid "Use these settings to keep entries only as long as they are needed. Trash or delete entries automatically older than the specified number of days. The minimum number of days allowed is one. This is to ensure that all entry processing is complete before deleting/trashing. The number of days setting is a minimum, not an exact period of time. The trashing/deleting occurs during the daily cron task so some entries may appear to remain up to a day longer than expected."
msgstr ""

#: tooltips.php:168
msgid "Password Visibility Toggle"
msgstr ""

#: tooltips.php:168
msgid "Check this box to add a toggle allowing the user to see the password they are entering in."
msgstr ""

#: tooltips.php:169
msgid "Enable to show a summary that lists validation errors on top of the form."
msgstr ""

#: widget.php:34
msgid "Gravity Forms Widget"
msgstr ""

#: widget.php:135
msgid "Contact Us"
msgstr ""

#: widget.php:169
msgid "Disable script output"
msgstr ""

#: widget.php:170
msgid "Tab Index Start"
msgstr ""

#: assets/js/src/admin/block-editor/blocks/form/edit.js:130
msgid "Restore Defaults"
msgstr ""

#: assets/js/src/admin/block-editor/blocks/form/edit.js:131
msgid "This will restore your form styles back to their default values and cannot be undone. Are you sure you want to continue?"
msgstr ""

#: assets/js/src/admin/block-editor/blocks/form/edit.js:141
msgid "Restore Default Styles"
msgstr ""

#: assets/js/src/admin/block-editor/blocks/form/edit.js:313
msgid "Form Styles"
msgstr ""

#: assets/js/src/admin/block-editor/blocks/form/edit.js:328
msgid "Form Theme"
msgstr ""

#: assets/js/src/admin/block-editor/blocks/form/edit.js:331
msgid "Inherit from default (%s)"
msgstr ""

#: assets/js/src/admin/block-editor/blocks/form/edit.js:333
msgid "Orbital Theme"
msgstr ""

#: assets/js/src/admin/block-editor/blocks/form/edit.js:343
msgid "Reset Defaults"
msgstr ""

#: assets/js/src/admin/block-editor/blocks/form/edit.js:381
msgid "Form style options are not available for forms that use %1$slegacy mode%2$s."
msgstr ""

#: assets/js/src/admin/block-editor/blocks/form/edit.js:386
msgid "Input Styles"
msgstr ""

#: assets/js/src/admin/block-editor/blocks/form/edit.js:419
msgid "Border Radius"
msgstr ""

#: assets/js/src/admin/block-editor/blocks/form/edit.js:420
#: assets/js/src/admin/block-editor/blocks/form/edit.js:504
#: assets/js/src/admin/block-editor/blocks/form/edit.js:537
msgid "In pixels."
msgstr ""

#: assets/js/src/admin/block-editor/blocks/form/edit.js:431
#: assets/js/src/admin/block-editor/blocks/form/edit.js:573
msgid "Background"
msgstr ""

#: assets/js/src/admin/block-editor/blocks/form/edit.js:442
msgid "Border"
msgstr ""

#: assets/js/src/admin/block-editor/blocks/form/edit.js:464
msgid "Accent"
msgstr ""

#: assets/js/src/admin/block-editor/blocks/form/edit.js:495
msgid "Label Styles"
msgstr ""

#: assets/js/src/admin/block-editor/blocks/form/edit.js:503
#: assets/js/src/admin/block-editor/blocks/form/edit.js:536
msgid "Font Size"
msgstr ""

#: assets/js/src/admin/block-editor/blocks/form/edit.js:528
msgid "Description Styles"
msgstr ""

#: assets/js/src/admin/block-editor/blocks/form/edit.js:561
msgid "Button Styles"
msgstr ""

#: assets/js/src/admin/block-editor/blocks/form/edit.js:624
msgid "AJAX"
msgstr ""

#: assets/js/src/admin/block-editor/blocks/form/edit.js:630
msgid "Field Values"
msgstr ""

#: assets/js/src/admin/block-editor/blocks/form/edit.js:639
msgid "Tabindex"
msgstr ""

#: assets/js/src/admin/block-editor/blocks/form/edit.js:666
msgid "Learn more about configuring your form to use Orbital."
msgstr ""

#: assets/js/src/admin/block-editor/blocks/form/edit.js:692
#: assets/js/src/admin/block-editor/blocks/form/edit.js:716
#: assets/js/src/admin/block-editor/blocks/form/edit.js:726
#: assets/js/src/admin/block-editor/blocks/form/edit.js:735
msgid "Colors"
msgstr ""

#: assets/js/src/admin/block-editor/blocks/form/edit.js:700
msgid "The accent color is used for aspects such as checkmarks and dropdown choices."
msgstr ""

#: assets/js/src/admin/block-editor/blocks/form/edit.js:741
msgid "The background color is used for various form elements, such as buttons and progress bars."
msgstr ""

#: assets/js/src/admin/block-editor/blocks/form/edit.js:829
msgid "The selected form has been deleted or trashed. Please select a new form."
msgstr ""

#: assets/js/src/admin/block-editor/blocks/form/edit.js:849
msgid "You must have at least one form to use the block."
msgstr ""

#: assets/js/src/admin/block-editor/blocks/form/index.js:25
msgid "Select and display one of your forms."
msgstr ""

#: assets/js/src/admin/block-editor/copy-paste-styles.js:22
msgid "Copy Form Styles"
msgstr ""

#: assets/js/src/admin/block-editor/copy-paste-styles.js:28
msgid "Paste Form Styles"
msgstr ""

#: assets/js/src/admin/block-editor/copy-paste-styles.js:69
msgid "Invalid Form Styles"
msgstr ""

#: assets/js/src/admin/block-editor/copy-paste-styles.js:69
msgid "Please ensure the form styles you are trying to paste are in the correct format."
msgstr ""

#: assets/js/src/admin/block-editor/copy-paste-styles.js:126
msgid "OK"
msgstr ""

#: assets/js/src/admin/block-editor/copy-paste-styles.js:152
msgid "Copy / Paste Not Available"
msgstr ""

#: assets/js/src/admin/block-editor/copy-paste-styles.js:152
msgid "Copy and paste functionality requires a secure connection. Reload this page using an HTTPS URL and try again."
msgstr ""

#: assets/js/src/admin/block-editor/copy-paste-styles.js:169
msgid "Paste Not Available"
msgstr ""

#: assets/js/src/admin/block-editor/copy-paste-styles.js:169
msgid "Your browser does not have permission to paste from the clipboard. <p>Please navigate to <strong>about:config</strong> and change the preference <strong>dom.events.asyncClipboard.readText</strong> to <strong>true</strong>."
msgstr ""

#: assets/js/src/legacy/admin/settings/components/MppingValueField.js:46
msgid "Remove Custom Value"
msgstr ""

#: assets/js/src/legacy/admin/settings/field-map/mapping.js:104
msgid "Remove Custom Key"
msgstr ""
