{"plugins": {"ffc21030-519a-4853-8cea-49f959e82731": {"id": "plugin:def67a2c1ddd6df2353e4772b6fd4e5b", "title": "Additional Variation Images Gallery for WooCommerce", "type": "plugin", "icon": "", "exclusions": ["/jquery-?[0-9.](.*)(.min|.slim|.slim.min)?.js", "/jquery-migrate(.min)?.js", "/woo-variation-gallery/assets/js/slick.min.js", "/woo-variation-gallery/assets/js/frontend.min.js", "/wp-includes/js/underscore.min.js", "variation_custom_fields"], "is_default": 0, "condition": "woo-variation-gallery/woo-variation-gallery.php", "created_at": **********, "updated_at": "2023-02-15T04:32:16.000000Z", "icon_url": ""}, "0e7dc253-acd0-4421-877f-a7101d848717": {"id": "plugin:4b82cc7379d46c6272f5d556bb264eec", "title": "Advanced Ads", "type": "plugin", "icon": "", "exclusions": ["/jquery-?[0-9.](.*)(.min|.slim|.slim.min)?.js", "/jquery-migrate(.min)?.js", "/uploads/(.*).js", "advanced_ads_ready", "advadsCfpQueue", "adsbygoogle", "adservice.google", "/advanced-ads(.*)", "advads_items", "advads_tracking_ads"], "is_default": 0, "condition": "advanced-ads/advanced-ads.php", "created_at": **********, "updated_at": "2023-02-15T04:32:17.000000Z", "icon_url": ""}, "a4d5c058-9120-47ee-977c-f30f83fb1a75": {"id": "plugin:a076fbb79772f497349a76ee74a7f708", "title": "All-in-one Compliance for GDPR / CCPA Cookie Consent", "type": "plugin", "icon": "", "exclusions": ["iubenda_cs.js", "var _iub"], "is_default": 0, "condition": "iubenda-cookie-law-solution/iubenda_cookie_solution.php", "created_at": **********, "updated_at": "2023-02-15T04:32:17.000000Z", "icon_url": ""}, "88457e92-0f24-458e-b7b5-bab59074ebef": {"title": "Amelia", "condition": "ameliabooking/ameliabooking.php", "exclusions": ["/wp-content/plugins/ameliabooking/(.*).js", "var hasAmeliaEvent"], "icon_url": "", "type": "plugin", "id": "plugin:93ea6597c3cbd06e93a46b9f5368732d", "is_default": 0, "created_at": 1714415106}, "79a5082f-821a-4a4b-a0e3-caaf22cf3f75": {"id": "plugin:9bdceaa1bb89135730a3b2aa4db94c22", "title": "AMO Team Showcase", "type": "plugin", "icon": "", "exclusions": ["/jquery-?[0-9.](.*)(.min|.slim|.slim.min)?.js", "/jquery-migrate(.min)?.js", "/amo-team-showcase/public/js/wookmark.js", "/amo-team-showcase/public/js/amo-team-showcase-public.js", "/wp-includes/js/imagesloaded.min.js", "amoTeamVars"], "is_default": 0, "condition": "amo-team-showcase/amo-team-showcase.php", "created_at": **********, "updated_at": "2023-02-15T04:32:16.000000Z", "icon_url": ""}, "90c7fd90-3adf-4cb7-8a92-845bdbd95d27": {"title": "Anti-Spam by CleanTalk - Prevent console error", "condition": "cleantalk-spam-protect/cleantalk.php", "exclusions": ["ctPublicFunctions", "ctPublic"], "icon_url": "", "type": "plugin", "id": "plugin:ed2ade77cd44e21b1703b093c002a903", "is_default": 0, "created_at": 1699370420}, "e054f840-700a-4549-bbba-485473a53f71": {"title": "AnWP Football Leagues - Calendar Widget", "condition": "football-leagues-by-anwppro/anwp-football-leagues.php", "exclusions": ["\\/jquery(-migrate)?-?([0-9.]+)?(.min|.slim|.slim.min)?.js(\\?(.*))?( |'|\"|>)", "/football-leagues-by-anwppro/(.*)", "/football-leagues-by-anwppro-premium-premium/(.*)", "window.AnWPFLPro", "window.AnWPFLTabulator", "/elementor/assets/lib/flatpickr/flatpickr.min.js"], "icon_url": "", "type": "plugin", "id": "plugin:e7581089f33fdf0a970d5c5deb16ff50", "is_default": 0, "created_at": 1708535321}, "1d058cae-4460-4354-bab3-a96445650bd8": {"id": "plugin:b739df50f3f5bf400075f17dca652517", "title": "AnyWhere Elementor Pro", "type": "plugin", "icon": "", "exclusions": ["/jquery-?[0-9.](.*)(.min|.slim|.slim.min)?.js", "/jquery-migrate(.min)?.js", "/anywhere-elementor-pro/build/index.js"], "is_default": 0, "condition": "anywhere-elementor-pro/anywhere-elementor-pro.php", "created_at": **********, "updated_at": "2023-02-15T04:32:16.000000Z", "icon_url": ""}, "8a1614c7-55b1-4b6d-88e5-6e8ddc630dae": {"id": "plugin:46741e77eaf4d13a0c80be6b86379758", "title": "Astra - Pro Addon", "type": "plugin", "icon": "", "exclusions": ["/astra-addon/astra-addon-(.*).js"], "is_default": 0, "condition": "astra-addon/astra-addon.php", "created_at": **********, "updated_at": "2023-02-15T04:32:17.000000Z", "icon_url": ""}, "37d96403-f9ea-4481-b2f8-374d7c93e61a": {"id": "plugin:ccd87807930a1856717fd276c336db9a", "title": "Beaver Builder", "type": "plugin", "icon": "", "exclusions": ["/jquery-?[0-9.](.*)(.min|.slim|.slim.min)?.js", "/jquery-migrate(.min)?.js", "/uploads/bb-plugin/", "/bb-plugin/js/yui3.min.js", "/wp-includes/js/imagesloaded.min.js", "/bb-plugin/js/fl-slideshow.min.js"], "is_default": 0, "condition": "beaver-builder-lite-version/fl-builder.php", "created_at": **********, "updated_at": "2023-02-15T04:32:17.000000Z", "icon_url": ""}, "f83dbf3b-783e-4ef9-9b18-8a469ca7102d": {"title": "Bloom", "condition": "bloom/bloom.php", "exclusions": ["/jquery-?[0-9.](.*)(.min|.slim|.slim.min)?.js", "/jquery-migrate(.min)?.js", "/bloom/"], "icon_url": "", "type": "plugin", "id": "plugin:e57cdfbc09f4e0f7445c279d9f580bdd", "is_default": 0, "created_at": 1685189070}, "135aadd2-cd4a-44ae-8dcf-801f3f2316c0": {"id": "plugin:055ef01accbad6378e3d1a4965600964", "title": "Booked", "type": "plugin", "icon": "", "exclusions": ["/booked/", "/js/jquery/ui/datepicker.min.js"], "is_default": 0, "condition": "booked/booked.php", "created_at": **********, "updated_at": "2023-02-15T04:32:16.000000Z", "icon_url": ""}, "ddfee9b0-e5a9-4d3f-8c21-b999cbb61c33": {"id": "plugin:492f9b0d55f3bf07c68e915ea1dfb72a", "title": "Bookly", "type": "plugin", "icon": "", "exclusions": ["/jquery-?[0-9.](.*)(.min|.slim|.slim.min)?.js", "/jquery-migrate(.min)?.js", "/bookly-responsive-appointment-booking-tool/frontend/", "window.bookly"], "is_default": 0, "condition": "bookly-responsive-appointment-booking-tool/main.php", "created_at": **********, "updated_at": "2023-02-15T04:32:16.000000Z", "icon_url": ""}, "7a013fd6-0881-4dbe-8e93-33edefe7f717": {"title": "<PERSON><PERSON><PERSON><PERSON>", "condition": "borlabs-cookie/borlabs-cookie.php", "exclusions": ["/jquery-?[0-9.](.*)(.min|.slim|.slim.min)?.js", "/jquery-migrate(.min)?.js", "borlabsCookieConfig", "borlabs-cookie.min.js", "borlabsCookie<PERSON><PERSON><PERSON><PERSON><PERSON>er", "BorlabsCookieBox", "allFbWidgets", "/borlabs-cookie/assets/javascript/", "borlabs-cookie-config"], "icon_url": "", "type": "plugin", "id": "plugin:e1ec2daca513de476bd3dae79366e9ab", "is_default": 0, "created_at": 1702497952}, "3999e680-4049-4494-945c-768cecc1a2c4": {"id": "plugin:43019d66af7b41e65bb602c01e10c6a0", "title": "Brizy", "type": "plugin", "icon": "", "exclusions": ["/jquery-?[0-9.](.*)(.min|.slim|.slim.min)?.js", "/wp-includes/js/jquery/jquery-migrate.min.js", "/brizy/public/editor-build/(.*)-wp/editor/js/group-(.*).js", "/brizy/public/editor-build/(.*)-wp/editor/js/preview.js", "Brizy.emit"], "is_default": 0, "condition": "brizy/brizy.php", "created_at": **********, "updated_at": "2023-02-15T04:32:17.000000Z", "icon_url": ""}, "af4d9357-3def-42a1-86b7-419553444b4d": {"id": "plugin:5c317f9f244597d8f236ecb7d8e41752", "title": "Carousel Upsells and Related Product for WooCommerce", "type": "plugin", "icon": "", "exclusions": ["/carousel-upsells-and-related-product-for-woocommerce/assets/js/glide.min.js", "carusel_poduct_related"], "is_default": 0, "condition": "carousel-upsells-and-related-product-for-woocommerce/ffxf-woo-glide-related-and-upsells.php", "created_at": **********, "updated_at": "2023-02-15T04:32:16.000000Z", "icon_url": ""}, "1d866d90-5451-43ba-a4d1-75b64f9235e1": {"id": "plugin:0a3a29603ebac8fe0808f64f5c8edbb2", "title": "clickskeks.at Cookiebanner", "type": "plugin", "icon": "", "exclusions": ["clickskeks"], "is_default": 0, "condition": "clickskeks/index.php", "created_at": **********, "updated_at": "2023-02-15T04:32:17.000000Z", "icon_url": ""}, "c09695cc-2387-4416-881a-c0b392188a26": {"title": "<PERSON><PERSON><PERSON><PERSON>", "condition": "complianz-gdpr/complianz-gpdr.php", "exclusions": ["/jquery-?[0-9.](.*)(.min|.slim|.slim.min)?.js", "/jquery-migrate(.min)?.js", "complianz"], "icon_url": "", "type": "plugin", "id": "plugin:a766f95208154cd69a3e15150a42f325", "is_default": 0, "created_at": 1685188788}, "bfb36984-e2a1-40ba-a8cd-f29b0b6f720f": {"title": "Complianz Premium", "condition": "complianz-gdpr-premium/complianz-gpdr-premium.php", "exclusions": ["\\/jquery(-migrate)?-?([0-9.]+)?(.min|.slim|.slim.min)?.js(\\?(.*))?( |'|\"|>|$)", "complianz"], "icon_url": "", "type": "plugin", "id": "plugin:89d54385816dd2d5ae92cfda9d95bbfd", "is_default": 0, "created_at": 1712163685}, "b5e146fc-0b7c-4c6c-a631-8da246d3bd89": {"title": "Conerstone Builder - Fix mobile menu", "condition": "cornerstone/cornerstone.php", "exclusions": ["\\/jquery(-migrate)?-?([0-9.]+)?(.min|.slim|.slim.min)?.js(\\?(.*))?( |'|\"|>)", "/cornerstone/assets/js/site/cs-classic.(.*).js"], "icon_url": "", "type": "plugin", "id": "plugin:f8f8c81535b5e0073aa3c56b6dd3df5b", "is_default": 0, "created_at": 1708614275}, "368bc394-b74e-4b62-b359-cd967f78c6ea": {"title": "ConsentMagic Pro - Show popup without user interaction", "condition": "consent-magic-pro/consent-magic-pro.php", "exclusions": ["\\/jquery(-migrate)?-?([0-9.]+)?(.min|.slim|.slim.min)?.js(\\?(.*))?( |'|\"|>|$)", "/wp-content/plugins/consent-magic-pro/js/cs-public.min.js", "/wp-content/plugins/pixelyoursite-pro/(.*)"], "icon_url": "", "type": "plugin", "id": "plugin:9e0701a214f49a057f17b00e39df2e7e", "is_default": 0, "created_at": 1711026041}, "7e551e3b-fbe4-4235-87a9-b476bc9e2020": {"id": "plugin:949b1b923d51d10a2fb67a2a39d166b3", "title": "Content Egg", "type": "plugin", "icon": "", "exclusions": ["/jquery-?[0-9.](.*)(.min|.slim|.slim.min)?.js", "/jquery-migrate(.min)?.js", "/content-egg/res/js/morrisjs/morris.min.js", "/content-egg/res/js/morrisjs/raphael.min.js", "Morris.Area"], "is_default": 0, "condition": "content-egg/content-egg.php", "created_at": **********, "updated_at": "2023-02-15T04:32:17.000000Z", "icon_url": ""}, "7fbca6c5-9239-4550-a755-6f041f867a57": {"title": "Cookie Notice & Compliance for GDPR / CCPA", "condition": "cookie-notice/cookie-notice.php", "exclusions": ["/jquery-?[0-9.](.*)(.min|.slim|.slim.min)?.js", "/jquery-migrate(.min)?.js", "/cookie-notice/", "var cnArgs"], "icon_url": "", "type": "plugin", "id": "plugin:213d0f883ae27aefb3a7937656bbd11e", "is_default": 0, "created_at": 1681379736}, "26722567-fe35-44b5-a5a7-fb0f3a38c3f2": {"id": "plugin:1d10ad30bbcf0fd4b26e9625a07abcfc", "title": "Cookiebot CMP", "type": "plugin", "icon": "", "exclusions": ["consent.cookiebot.com"], "is_default": 0, "condition": "cookiebot/cookiebot.php", "created_at": **********, "updated_at": "2023-02-15T04:32:16.000000Z", "icon_url": ""}, "74110e36-89e5-440d-bec3-7133da3277c2": {"id": "plugin:edcf103293ceab711e999d419d038ca1", "title": "Coupon Referral Program", "type": "plugin", "icon": "", "exclusions": ["/jquery-?[0-9.](.*)(.min|.slim|.slim.min)?.js", "/jquery-migrate(.min)?.js", "/coupon-referral-program/", "/wp-includes/js/jquery/ui/draggable.min.js"], "is_default": 0, "condition": "coupon-referral-program/coupon-referral-program.php", "created_at": **********, "updated_at": "2023-02-15T04:32:17.000000Z", "icon_url": ""}, "408fa396-20f2-4b1f-820d-52882af281cc": {"title": "CozyStay Core - Fix background images", "condition": "cozystay-core/cozystay-core.php", "exclusions": ["\\/jquery(-migrate)?-?([0-9.]+)?(.min|.slim|.slim.min)?.js(\\?(.*))?( |'|\"|>)", "/cozystay-core/assets/scripts/front/parallax-bundle.min.js"], "icon_url": "", "type": "plugin", "id": "plugin:872b5eef05c1fc3b00cda07ee966938d", "is_default": 0, "created_at": 1711999893}, "ed63e02f-d6c5-481a-bcb9-aae15f72aa21": {"id": "plugin:c3e26264dcfd25802805b4fd1a2a449c", "title": "Crisp - Live Chat and Chatbot", "type": "plugin", "icon": "", "exclusions": ["/jquery-?[0-9.](.*)(.min|.slim|.slim.min)?.js", "/jquery-migrate(.min)?.js", "CRISP_RUNTIME_CONFIG", "l.js"], "is_default": 0, "condition": "crisp/crisp.php", "created_at": **********, "updated_at": "2023-02-15T04:32:16.000000Z", "icon_url": ""}, "a8eb622b-279c-4f84-86e0-785e9497add7": {"id": "plugin:6e4bf949e12f0bebfefb48f6c316102a", "title": "Custom Twitter Feeds pro", "type": "plugin", "icon": "", "exclusions": ["/custom-twitter-feeds-pro/js/ctf-scripts.min.js"], "is_default": 0, "condition": "custom-twitter-feeds-pro/custom-twitter-feed.php", "created_at": **********, "updated_at": "2023-02-15T04:32:16.000000Z", "icon_url": ""}, "1624ef15-e25b-406d-bdf9-d4b78d7a59e7": {"title": "Customer Reviews for WooCommerce Plugin", "condition": "customer-reviews-woocommerce/ivole.php", "exclusions": ["/jquery-?[0-9.](.*)(.min|.slim|.slim.min)?.js", "/jquery-migrate(.min)?.js", "/customer-reviews-woocommerce/js/colcade.js"], "icon_url": "", "type": "plugin", "id": "plugin:1f893b343e72ce55e6c9013fbda172fa", "is_default": 0, "created_at": 1704734788}, "b0614843-afed-4377-9d8d-e869221be331": {"title": "<PERSON><PERSON><PERSON>", "condition": "depicter/depicter.php", "exclusions": ["/depicter/"], "icon_url": "", "type": "plugin", "id": "plugin:77c42a041f1c40d128f4bb3714a6d20d", "is_default": 0, "created_at": 1713878881}, "396cc03a-8946-4ee8-ab15-7e48261df79a": {"title": "Divi - Carousel Module 2.0", "condition": "dg-divi-carousel", "exclusions": ["\\/jquery(-migrate)?-?([0-9.]+)?(.min|.slim|.slim.min)?.js(\\?(.*))?( |'|\"|>)", "/wp-content/plugins/dg-divi-carousel/"], "icon_url": "", "type": "plugin", "id": "plugin:fcd789b7d02699f89720aa5ff3627912", "is_default": 0, "created_at": 1709126476}, "c15fe3b3-0eaa-48f9-bf04-0e778b1f8c63": {"title": "Divi - Supreme", "condition": "supreme-modules-for-divi/supreme-modules-for-divi.php", "exclusions": ["/jquery-?[0-9.](.*)(.min|.slim|.slim.min)?.js", "/jquery-migrate(.min)?.js", ".dipi_preloader_wrapper_outer", "/Divi/js/scripts.min.js", "/Divi/js/custom.unified.js", "/js/magnific-popup.js", "var DIVI", "/supreme-modules-for-divi/"], "icon_url": "", "type": "plugin", "id": "plugin:b5489ae4d8b949f536d6dd2e5b0c1a95", "is_default": 0, "created_at": 1679738701}, "dd31451b-989a-4517-b02a-e2c2e2023366": {"id": "plugin:5caed322df984bbfd3ecb506cf12b688", "title": "Divi Den Pro", "type": "plugin", "icon": "", "exclusions": ["/jquery-?[0-9.](.*)(.min|.slim|.slim.min)?.js", "/jquery-migrate(.min)?.js", "/ddpro/build/freddie/js/freddieScriptPageTransition.js", "/ddpro/build/freddie/js/freddieScriptsHeaders.js", "/ddpro/build/freddie/js/freddieScriptsContents.js", "/ddpro/build/freddie/js/gsap/gsap.min.js"], "is_default": 0, "condition": "ddpro/ddpro.php", "created_at": **********, "updated_at": "2023-02-15T04:32:16.000000Z", "icon_url": ""}, "2b006370-cc90-42c1-9656-a30fbfbc91c6": {"id": "plugin:402d9b241b04934dd30f32e7ba490e63", "title": "Divi Mobile", "type": "plugin", "icon": "", "exclusions": ["/jquery-?[0-9.](.*)(.min|.slim|.slim.min)?.js", "/jquery-migrate(.min)?.js", "divi-menu", "dm_nav"], "is_default": 0, "condition": "divi-mobile/divi-mobile.php", "created_at": **********, "updated_at": "2023-02-15T04:32:17.000000Z", "icon_url": ""}, "666bbed9-7ce3-457c-ad6e-b5e056d05010": {"title": "<PERSON><PERSON> Overlays", "condition": "divi-overlays/divi-overlays.php", "exclusions": ["/jquery-?[0-9.](.*)(.min|.slim|.slim.min)?.js", "/jquery-migrate(.min)?.js", ".dipi_preloader_wrapper_outer", "/Divi/js/scripts.min.js", "/Divi/js/custom.unified.js", "var DIVI", "/divi-overlays/"], "icon_url": "", "type": "plugin", "id": "plugin:1c828c93d87298d2a27c76e13d0880ba", "is_default": 0, "created_at": 1679738664}, "42a404af-7792-44be-9baa-565dc3baf25d": {"title": "<PERSON><PERSON>", "condition": "divi-pixel/divi-pixel.php", "exclusions": ["/jquery-?[0-9.](.*)(.min|.slim|.slim.min)?.js", "/jquery-migrate(.min)?.js", ".dipi_preloader_wrapper_outer", "/divi-pixel/dist/public/js/hamburger.min.js", "/divi-pixel/dist/vendor/js/easypiechart.js"], "icon_url": "", "type": "plugin", "id": "plugin:dd2494945a487a6cc74d3ab1b2137ccb", "is_default": 0, "created_at": 1718642717}, "85f5c099-c481-4c38-bbb9-8b76113bdfd5": {"title": "Divi Supreme Pro", "condition": "supreme-modules-pro-for-divi/supreme-modules-pro-for-divi.php", "exclusions": ["/jquery-?[0-9.](.*)(.min|.slim|.slim.min)?.js", "/jquery-migrate(.min)?.js", "elm.style.display", "/supreme-modules-pro-for-divi/includes/modules/ImageCarousel/frontend.min.js", "/supreme-modules-pro-for-divi/public/js/swiper-bundle.min.js"], "icon_url": "", "type": "plugin", "id": "plugin:3d5f0ae7751702fd1bb490fcf991a334", "is_default": 0, "created_at": 1695118162}, "0b78d762-c7be-45de-9aae-0c5078ec0619": {"id": "plugin:b5fc0101608d0b0627268dc49e3e1f8f", "title": "Divi <PERSON>l<PERSON>", "type": "plugin", "icon": "", "exclusions": ["/jquery-?[0-9.](.*)(.min|.slim|.slim.min)?.js", "/jquery-migrate(.min)?.js", "jQuery('.preloader')", "/divi-toolbox/assets/js/toolbox-scripts.js"], "is_default": 0, "condition": "divi-toolbox/divi-toolbox.php", "created_at": **********, "updated_at": "2023-02-15T04:32:16.000000Z", "icon_url": ""}, "31a34440-1383-40c4-8920-effcf99f2165": {"title": "Dracula Dark Mode", "condition": "dracula-dark-mode-premium/plugin.php", "exclusions": ["\\/jquery(-migrate)?-?([0-9.]+)?(.min|.slim|.slim.min)?.js(\\?(.*))?( |'|\"|>|$)", "/wp-content/plugins/dracula-dark-mode-premium/assets/js/dark-mode.js", "/wp-content/plugins/dracula-dark-mode-premium/assets/js/frontend.js", "/wp-includes/js/dist/vendor/react-dom.min.js", "/wp-includes/js/dist/vendor/react.min.js", "/wp-includes/js/dist/api-fetch.min.js", "/wp-includes/js/dist/hooks.min.js", "/wp-includes/js/dist/i18n.min.js", "draculaDarkMode"], "icon_url": "", "type": "plugin", "id": "plugin:5e8a5d8d3830136d84d0f3676f1bf5a5", "is_default": 0, "created_at": 1704210236}, "8baba0f8-4449-47d8-a87d-9b6a116e7684": {"title": "Dynamic Pricing & Discounts Lite for WooCommerce", "condition": "woo-dynamic-pricing-discounts-lite/dynamic-pricing-discounts-lite-for-woocommerce.php", "exclusions": ["/woo-dynamic-pricing-discounts-lite/assets/OwlCarousel/dist/owl.carousel.min.js", "/jquery-?[0-9.](.*)(.min|.slim|.slim.min)?.js", "/jquery-migrate(.min)?.js"], "icon_url": "", "type": "plugin", "id": "plugin:185062bf08db42515488e1853d1f4917", "is_default": 0, "created_at": 1677857247}, "0fa129f2-5869-4db7-9bf0-69e4b3549ee2": {"id": "plugin:27478327aa44075a86176fad95640d76", "title": "Dynamic Product Gallery for WooCommerce", "type": "plugin", "icon": "", "exclusions": ["/jquery-?[0-9.](.*)(.min|.slim|.slim.min)?.js", "/jquery-migrate(.min)?.js", "/woocommerce-dynamic-gallery/", "a3revWCDynamicGallery"], "is_default": 0, "condition": "woocommerce-dynamic-gallery/wc_dynamic_gallery_woocommerce.php", "created_at": **********, "updated_at": "2023-02-15T04:32:17.000000Z", "icon_url": ""}, "9958c6be-2f67-4791-95c3-a7ad96d599c0": {"title": "Dynamic Product Gallery Premium for WooCommerce", "condition": "woocommerce-dynamic-gallery-pro/wc_dynamic_gallery_woocommerce.php", "exclusions": ["/jquery-?([0-9.]+)?(.min|.slim|.slim.min)?.js(\\?(.*))?$", "/jquery-migrate(.*)(.min|.slim|.slim.min)?.js(\\?(.*))?$", "/woocommerce-dynamic-gallery-pro/assets/js/mygallery/jquery.a3-dgallery.js", "settings_defaults_", "a3revWCDynamicGallery"], "icon_url": "", "type": "plugin", "id": "plugin:11dc02a76019f00422f4ac85f47f5135", "is_default": 0, "created_at": 1683208937}, "ce26ed21-1be5-481f-80d4-31edfac6d890": {"title": "Dynamic.ooo - Dynamic Content for Elementor", "condition": "dynamic-content-for-elementor/dynamic-content-for-elementor.php", "exclusions": ["/jquery-?[0-9.](.*)(.min|.slim|.slim.min)?.js", "/jquery-migrate(.min)?.js", "/elementor/", "/elementor-pro/", "/wp-includes/js/imagesloaded.min.js", "ElementorProFrontendConfig", "elementorFrontendConfig", "/dynamic-content-for-elementor/assets/", "/wp-includes/js/dist/hooks.min.js", "/wp-includes/js/dist/i18n.min.js"], "icon_url": "", "type": "plugin", "id": "plugin:d2bd5b348abe016bdc722acce7755062", "is_default": 0, "created_at": 1683355024}, "6a3161a6-1a6f-457a-a9ee-54b376981927": {"id": "plugin:ee1dec033c6481a77fe88de5bef1a02d", "title": "Easy Table of Contents", "type": "plugin", "icon": "", "exclusions": ["/easy-table-of-contents/assets/js/front.min.js"], "is_default": 0, "condition": "easy-table-of-contents/easy-table-of-contents.php", "created_at": **********, "updated_at": "2023-02-15T04:32:16.000000Z", "icon_url": ""}, "25a534a0-2c1f-4cf4-9e19-7941bc032b3a": {"title": "Ecwid Ecommerce Shopping Cart", "condition": "ecwid-shopping-cart/ecwid-shopping-cart.php", "exclusions": ["/jquery-?[0-9.](.*)(.min|.slim|.slim.min)?.js", "/jquery-migrate(.min)?.js", "app.ecwid.com/script.js", "/ecwid-shopping-cart/js/frontend.js", "/ecwid-shopping-cart/js/static-page.js", "ecwidParamswindow.ec", "jQuery.mobile", "xSearch", "xCategoriesV2", "xProductBrowser", "Ecwid.init"], "icon_url": "", "type": "plugin", "id": "plugin:41c8f5f0ab00cb39654aedd783d194e0", "is_default": 0, "created_at": 1704734846}, "a0d681db-991a-4220-8f05-c54a4857aa42": {"title": "Element Pack Pro", "condition": "bdthemes-element-pack/bdthemes-element-pack.php", "exclusions": ["\\/jquery(-migrate)?-?([0-9.]+)?(.min|.slim|.slim.min)?.js(\\?(.*))?( |'|\"|>|$)", "/bdthemes-element-pack/assets/js/modules/ep-static-carousel.min.js", "/bdthemes-element-pack/assets/js/modules/ep-custom-carousel.min.js", "/bdthemes-element-pack/assets/js/modules/ep-slideshow.min.js", "/bdthemes-element-pack/assets/js/modules/ep-product-carousel.min.js", "/bdthemes-element-pack/assets/js/modules/ep-stacker.min.js", "/bdthemes-element-pack/assets/js/bdt-uikit.min.js", "/bdthemes-element-pack/assets/js/common/helper.min.js", "/bdthemes-element-pack/assets/vendor/js/ScrollTrigger.min.js", "/bdthemes-element-pack/assets/vendor/js/gsap.min.js"], "icon_url": "", "type": "plugin", "id": "plugin:ced76c4da718a667537dc6c9d5e0244d", "is_default": 0, "created_at": 1715791619}, "40464325-5bae-4a20-bc97-553499e09a73": {"title": "<PERSON><PERSON><PERSON>", "condition": "elementor/elementor.php", "exclusions": ["/jquery-?[0-9.](.*)(.min|.slim|.slim.min)?.js", "/jquery-migrate(.min)?.js", "/elementor/", "/elementor-pro/", "/wp-includes/js/imagesloaded.min.js", "ElementorProFrontendConfig", "elementorFrontendConfig", "/happy-elementor-addons-pro/", "/header-footer-elementor/inc/js/frontend.js", "/wp-includes/js/jquery/ui/core.min.js", "/wp-includes/js/dist/api-fetch.min.js", "/wp-includes/js/dist/hooks.min.js", "/wp-includes/js/dist/i18n.min.js"], "icon_url": "", "type": "plugin", "id": "plugin:1d15783218a3137bec4ee8df5353e218", "is_default": 0, "created_at": 1694425872}, "ac86b64c-c80a-4053-894d-6caa8b4fdce8": {"title": "Elementor  Loop Carrousel", "condition": "elementor/elementor.php", "exclusions": ["/jquery-?[0-9.](.*)(.min|.slim|.slim.min)?.js", "/jquery-migrate(.min)?.js", "/wp-includes/js/jquery/ui/core.min.js", "/wp-includes/js/dist/api-fetch.min.js", "/wp-includes/js/dist/hooks.min.js", "/wp-includes/js/dist/i18n.min.js"], "icon_url": "", "type": "plugin", "id": "plugin:0eb8d0bbd1c7ca05ac133ea634f676b7", "is_default": 0, "created_at": 1694442056}, "a8fc9fd1-f6d3-41eb-abe9-ef176de6d7e2": {"title": "Elementor - <PERSON><PERSON> Background Images", "condition": "elementor/elementor.php", "exclusions": ["lazyloadRunObserver"], "icon_url": "", "type": "plugin", "id": "plugin:bf248cb2876558452a566c5dd89262b8", "is_default": 0, "created_at": 1694453032}, "1948aff5-7850-4979-91fa-0ce181484508": {"id": "plugin:de658ae6c2d05a5a4a947efecf5e0c16", "title": "Elementor Custom Skin", "type": "plugin", "icon": "", "exclusions": ["/jquery-?[0-9.](.*)(.min|.slim|.slim.min)?.js", "/jquery-migrate(.min)?.js", "/elementor/", "/elementor-pro/", "ElementorProFrontendConfig", "elementorFrontendConfig", "/ele-custom-skin(.*)/assets/js/"], "is_default": 0, "condition": "ele-custom-skin/ele-custom-skin.php", "created_at": **********, "updated_at": "2023-02-15T04:32:16.000000Z", "icon_url": ""}, "25708299-c596-4416-8ad8-740aee0f2752": {"title": "Elementor Pro", "condition": "elementor-pro/elementor-pro.php", "exclusions": ["/jquery-?[0-9.](.*)(.min|.slim|.slim.min)?.js", "/jquery-migrate(.min)?.js", "/elementor/", "/elementor-pro/", "/wp-includes/js/imagesloaded.min.js", "ElementorProFrontendConfig", "elementorFrontendConfig", "/happy-elementor-addons-pro/", "/header-footer-elementor/inc/js/frontend.js"], "icon_url": "", "type": "plugin", "id": "plugin:030ad23e3851ed7adfa7b9b6c13cf5a6", "is_default": 0, "created_at": 1679490021}, "f34dd874-4b34-41e6-a31c-ece3c1efbffc": {"id": "plugin:305581ad4294a30eeb1247982f626005", "title": "Elementor Pro - SmartMenus", "type": "plugin", "icon": "", "exclusions": ["/jquery-?[0-9.](.*)(.min|.slim|.slim.min)?.js", "/jquery-migrate(.min)?.js", "/elementor-pro/assets/lib/smartmenus/jquery.smartmenus.min.js", "/elementor-pro/assets/js/preloaded-elements-handlers.min.js"], "is_default": 0, "condition": "elementor-pro/elementor-pro.php", "created_at": **********, "updated_at": "2023-02-15T04:32:16.000000Z", "icon_url": ""}, "111cc8b2-0be9-438b-9aa3-7f1e301a5697": {"id": "plugin:5ae9f5d99224d5ebc7d0c0bbf5b99787", "title": "Elementor Pro - User <PERSON>", "type": "plugin", "icon": "", "exclusions": ["/wp-includes/js/underscore.min.js", "/wp-includes/js/jquery/ui/core.min.js", "/wp-includes/js/backbone.min.js", "elementorAdminBarConfig", "elementorCommonConfig", "elementorWebCliConfig", "elementorDevToolsConfig"], "is_default": 0, "condition": "elementor-pro/elementor-pro.php", "created_at": **********, "updated_at": "2023-02-15T04:32:17.000000Z", "icon_url": ""}, "c1713bf8-e0c3-4cb5-8b3f-841b06aa87ed": {"title": "ElementsKit Lite - Megamenu", "condition": "elementskit-lite/elementskit-lite.php", "exclusions": ["/jquery-?[0-9.](.*)(.min|.slim|.slim.min)?.js", "/jquery-migrate(.min)?.js", "/elementor/", "/elementor-pro/", "ElementorProFrontendConfig", "elementorFrontendConfig", "/elementskit(.*)/"], "icon_url": "", "type": "plugin", "id": "plugin:b91fed2fc4478dbb7067742c1ee4691b", "is_default": 0, "created_at": 1683270249}, "236c2a9e-9668-4a80-9b66-d2222d812dd6": {"title": "Elfsight Slider CC", "condition": "elfsight-slider-cc/elfsight-slider-cc.php", "exclusions": ["/elfsight-slider-cc/assets/elfsight-slider.js"], "icon_url": "", "type": "plugin", "id": "plugin:b0e259c84df5e120d6113dd0eef9da4b", "is_default": 0, "created_at": 1718660424}, "706ff35d-8e33-4b04-a986-346faff704e9": {"id": "plugin:417192424139d89fb2a5b1ee1f2b9613", "title": "Essential Addons for Elementor", "type": "plugin", "icon": "", "exclusions": ["/jquery-?[0-9.](.*)(.min|.slim|.slim.min)?.js", "/jquery-migrate(.min)?.js", "/elementor/", "/elementor-pro/", "ElementorProFrontendConfig", "elementorFrontendConfig", "/essential-addons(-for)?-elementor(-lite)?/.*(.min)?.js"], "is_default": 0, "condition": "essential-addons-for-elementor-lite/essential_adons_elementor.php", "created_at": **********, "updated_at": "2023-02-15T04:32:16.000000Z", "icon_url": ""}, "b38556dc-2bc4-430f-9c55-7191cf7773db": {"id": "plugin:057757d0593ad9d2dc58124f0077a5df", "title": "Essential Addons for Elementor Pro", "type": "plugin", "icon": "", "exclusions": ["/jquery-?[0-9.](.*)(.min|.slim|.slim.min)?.js", "/jquery-migrate(.min)?.js", "/elementor/", "/elementor-pro/", "ElementorProFrontendConfig", "elementorFrontendConfig", "/essential-addons(-for)?-elementor(-lite)?/.*(.min)?.js"], "is_default": 0, "condition": "essential-addons-elementor/essential_adons_elementor.php", "created_at": **********, "updated_at": "2023-02-15T04:32:16.000000Z", "icon_url": ""}, "719b988f-0d60-40c2-ba46-88f943119cb1": {"id": "plugin:c8e6c490f6438f566ade600c33531a85", "title": "Essential Grid", "type": "plugin", "icon": "", "exclusions": ["/jquery-?[0-9.](.*)(.min|.slim|.slim.min)?.js", "/jquery-migrate(.min)?.js", "/essential-grid/", "lightboxOptions"], "is_default": 0, "condition": "essential-grid/essential-grid.php", "created_at": **********, "updated_at": "2023-02-15T04:32:17.000000Z", "icon_url": ""}, "47d3d2ab-160d-4b07-ac25-8250b9e6a951": {"id": "plugin:dafd19fa48a1fef890dc4348052fcb75", "title": "EventON Lite", "type": "plugin", "icon": "", "exclusions": ["/jquery-?[0-9.](.*)(.min|.slim|.slim.min)?.js", "/jquery-migrate(.min)?.js", "/eventon-lite/assets/js/(.*)"], "is_default": 0, "condition": "eventon-lite/eventon.php", "created_at": **********, "updated_at": "2023-02-15T04:32:16.000000Z", "icon_url": ""}, "219968d7-cf1f-4ee0-917a-db35dcc93a3e": {"id": "plugin:49ff465628fc3cb6d7f23ff81d9b8339", "title": "FacetWP", "type": "plugin", "icon": "", "exclusions": ["/facetwp/assets/js/dist/front.min.js", "window.FWP_"], "is_default": 0, "condition": "facetwp/index.php", "created_at": **********, "updated_at": "2023-02-15T04:32:17.000000Z", "icon_url": ""}, "acce5701-5917-48d5-83ab-3e626aa420c5": {"id": "plugin:78a1c88a56fa957c802074b6418c6fac", "title": "FacetWP - Flyout menu", "type": "plugin", "icon": "", "exclusions": ["/facetwp-flyout/assets/js/front.js"], "is_default": 0, "condition": "facetwp-flyout/facetwp-flyout.php", "created_at": **********, "updated_at": "2023-02-15T04:32:17.000000Z", "icon_url": ""}, "081461cf-dbd0-42f3-9557-10cdc16cf145": {"title": "FiboFilters Premium", "condition": "fibofilters-pro/fibofilters.php", "exclusions": ["\\/jquery(-migrate)?-?([0-9.]+)?(.min|.slim|.slim.min)?.js(\\?(.*))?( |'|\"|>|$)", "/fibofilters-pro/build/front-pro/front.js"], "icon_url": "", "type": "plugin", "id": "plugin:d9f62725f1470d35c3f220645bc2e473", "is_default": 0, "created_at": 1709923289}, "f2a5b95f-1a22-46d2-8b72-42a53e46ae3f": {"id": "plugin:5d7555892a3a9968fde3fa3a335fc3d8", "title": "FiboSearch - Ajax Search for WooCommerce", "type": "plugin", "icon": "", "exclusions": ["/wp-includes/js/jquery/jquery.js", "/wp-includes/js/jquery/jquery-migrate.js", "/ajax-search-for-woocommerce-premium/assets/js/search.js"], "is_default": 0, "condition": "ajax-search-for-woocommerce/ajax-search-for-woocommerce.php", "created_at": **********, "updated_at": "2023-02-15T04:32:16.000000Z", "icon_url": ""}, "7d1404e9-be76-449c-87aa-919557abe82d": {"id": "plugin:ad46179e8b0584abaf52056b846da227", "title": "FlexBlock", "type": "plugin", "icon": "", "exclusions": ["/jquery-?[0-9.](.*)(.min|.slim|.slim.min)?.js", "/flo-flex-builder/dist/flex-public.min.js", "flexDebug"], "is_default": 0, "condition": "flo-flex-builder/flo-flex-builder.php", "created_at": **********, "updated_at": "2023-02-15T04:32:17.000000Z", "icon_url": ""}, "b9c6a2dc-b226-4e2c-b85e-29ee55b8f751": {"id": "plugin:567a2b15db30ef6bd4904e4317139aac", "title": "Fluent Forms", "type": "plugin", "icon": "", "exclusions": ["/jquery-?[0-9.](.*)(.min|.slim|.slim.min)?.js", "/fluentform/public/js/(.*).js", "/fluentformpro/public/js/(.*).js"], "is_default": 0, "condition": "fluentform/fluentform.php", "created_at": **********, "updated_at": "2023-02-15T04:32:16.000000Z", "icon_url": ""}, "10874f8a-2855-4e80-bc46-e484589b76a8": {"id": "plugin:e78c153103f698b2b34892332d6b3b62", "title": "Flying Images", "type": "plugin", "icon": "", "exclusions": ["flyingImages"], "is_default": 0, "condition": "nazy-load/flying-images.php", "created_at": **********, "updated_at": "2023-02-15T04:32:17.000000Z", "icon_url": ""}, "53958499-dacd-41d6-93bc-1534fe7d9eda": {"id": "plugin:5a0e40a6c5783856893b803189de1404", "title": "FooGallery Premium", "type": "plugin", "icon": "", "exclusions": ["/jquery-?[0-9.](.*)(.min|.slim|.slim.min)?.js", "/jquery-migrate(.min)?.js", "/plugins/foobox-image-lightbox/free/js/foobox.free.min.js", "/plugins/foogallery-premium/pro/extensions/default-templates/shared/js/foogallery.min.js"], "is_default": 0, "condition": "foogallery-premium/foogallery.php", "created_at": **********, "updated_at": "2023-02-15T04:32:16.000000Z", "icon_url": ""}, "7870d704-2024-4622-838c-fad37d5c6753": {"id": "plugin:ebb5efb57b19ae60e734e456ca2df3f8", "title": "Formidable Forms", "type": "plugin", "icon": "", "exclusions": ["/jquery-?[0-9.](.*)(.min|.slim|.slim.min)?.js", "/jquery-migrate(.min)?.js", "frmSigs", "/formidable-signature/js/frm.signature.min.js"], "is_default": 0, "condition": "formidable/formidable.php", "created_at": **********, "updated_at": "2023-02-15T04:32:17.000000Z", "icon_url": ""}, "3f24f9b3-e59e-400f-a784-999f93f60fab": {"id": "plugin:0b08523445b8869a67ca40e777704692", "title": "Forminator", "type": "plugin", "icon": "", "exclusions": ["/wp-includes/js/jquery/jquery.min.js", "/wp-includes/js/jquery/jquery-migrate.min.js", "forminatorFront", "/forminator/build/front/front.multi.min.js", "/forminator/assets/js/library/jquery.validate.min.js", "/forminator/assets/forminator-ui/js/forminator-form.min.js", "/forminator/assets/forminator-ui/js/select2.full.min.js", "/wp-includes/js/jquery/ui/datepicker.min.js", "/wp-includes/js/dist/vendor/moment.min.js"], "is_default": 0, "condition": "forminator/forminator.php", "created_at": **********, "updated_at": "2023-02-15T04:32:17.000000Z", "icon_url": ""}, "3a600ded-3454-48fb-9811-46afa2ab3c05": {"id": "plugin:5759bf0d47ac3457485314b381a9b528", "title": "GDPR Cookie Compliance", "type": "plugin", "icon": "", "exclusions": ["/wp-includes/js/jquery/jquery.min.js", "/wp-includes/js/jquery/jquery-migrate.min.js", "/gdpr-cookie-compliance/dist/scripts/main.js"], "is_default": 0, "condition": "gdpr-cookie-compliance/moove-gdpr.php", "created_at": **********, "updated_at": "2023-02-15T04:32:17.000000Z", "icon_url": ""}, "e21aaa5e-7354-471d-ab86-85f99f48830c": {"id": "plugin:ffb40036ab0583218561de7c28c6bd9b", "title": "GDPR <PERSON><PERSON>sent", "type": "plugin", "icon": "", "exclusions": ["/wp-includes/js/jquery/jquery.min.js", "/wp-includes/js/jquery/jquery-migrate.min.js", "/public/js/cookie-law-info-public.js", "Cli_Data"], "is_default": 0, "condition": "webtoffee-gdpr-cookie-consent/cookie-law-info.php", "created_at": **********, "updated_at": "2023-02-15T04:32:17.000000Z", "icon_url": ""}, "c11ca4ea-1a23-49ee-9a6b-1e549de50ea2": {"id": "plugin:3877953c5ec1e66db92ad844ae8ebafc", "title": "Getwid - Gutenberg Blocks", "type": "plugin", "icon": "", "exclusions": ["/jquery-?[0-9.](.*)(.min|.slim|.slim.min)?.js", "/wp-includes/js/jquery/jquery-migrate.min.js", "/wp-includes/js/jquery/ui/tabs.min.js", "/wp-includes/js/jquery/ui/core.min.js"], "is_default": 0, "condition": "getwid/getwid.php", "created_at": **********, "updated_at": "2023-02-15T04:32:17.000000Z", "icon_url": ""}, "79b33eb9-bc92-4d8a-afd8-1c61e70bed8f": {"title": "GiveWP", "condition": "give/give.php", "exclusions": ["\\/jquery(-migrate)?-?([0-9.]+)?(.min|.slim|.slim.min)?.js(\\?(.*))?( |'|\"|>)", "/give/assets/dist/js/give.js", "/wp-includes/js/dist/api-fetch.min.js", "/wp-includes/js/dist/hooks.min.js", "/wp-includes/js/dist/i18n.min.js"], "icon_url": "", "type": "plugin", "id": "plugin:0fb3af06fc5c710a670220b054c292c5", "is_default": 0, "created_at": 1706652232}, "3773bb33-b168-4f68-9963-512da24ac4da": {"title": "GoodLayers Core", "condition": "goodlayers-core/goodlayers-core.php", "exclusions": ["/jquery-?[0-9.](.*)(.min|.slim|.slim.min)?.js", "/jquery-migrate(.min)?.js", "/goodlayers-core/plugins/script.js", "/goodlayers-core/include/js/page-builder.js"], "icon_url": "", "type": "plugin", "id": "plugin:5f124509a56ca2e0644246919ec70434", "is_default": 0, "created_at": 1704734904}, "2adabe9b-6e03-4a67-959f-492813d40f69": {"title": "Google Tag Manager for WooCommerce PRO", "condition": "gtm-ecommerce-woo-pro/gtm-ecommerce-woo-pro.php", "exclusions": ["/gtm-ecommerce-woo-pro/assets/gtm-ecommerce-woo-pro.js"], "icon_url": "", "type": "plugin", "id": "plugin:e54fd4313007b6fedf8ab4df0e05277a", "is_default": 0, "created_at": 1691218549}, "7bf261ac-89d4-492f-8a3d-1c5809b9579a": {"title": "Gravity Forms", "condition": "gravityforms/gravityforms.php", "exclusions": ["/jquery-?[0-9.](.*)(.min|.slim|.slim.min)?.js", "/gravityforms/", "gform", "recaptcha", "/wp-includes/js/jquery/jquery-migrate.min.js", "/wp-includes/js/plupload/plupload.min.js", "/wp-includes/js/plupload/moxie.min.js"], "icon_url": "", "type": "plugin", "id": "plugin:7a1c1606d094f6bff83c0ee155908367", "is_default": 0, "created_at": 1694457903}, "fbaf4a19-f675-441a-b75c-7fd748a59827": {"title": "Gravity Forms Page Transitions", "condition": "gp-page-transitions/gp-page-transitions.php", "exclusions": ["\\/jquery(-migrate)?-?([0-9.]+)?(.min|.slim|.slim.min)?.js(\\?(.*))?( |'|\"|>|$)", "/plugins/gp-page-transitions/js/"], "icon_url": "", "type": "plugin", "id": "plugin:53ef8d0ee1f9fdf0bbf87676e449eef5", "is_default": 0, "created_at": 1713987341}, "12340193-5c35-4b0d-b0bc-bea690cf1cae": {"id": "plugin:2f3112dd98c39aeb6bde618c9026a29f", "title": "Green Forms", "type": "plugin", "icon": "", "exclusions": ["/jquery-?[0-9.](.*)(.min|.slim|.slim.min)?.js", "leform.min.js", "leform_customjs_handlers", "leform_ajax_url"], "is_default": 0, "condition": "green-forms/green-forms.php", "created_at": **********, "updated_at": "2023-02-15T04:32:17.000000Z", "icon_url": ""}, "4d20b427-47cf-4cdf-91c7-ff53602d3b2a": {"id": "plugin:0eb769dddc58f998e913345841b1d0b3", "title": "GTM4WP", "type": "plugin", "icon": "", "exclusions": ["/jquery-?[0-9.](.*)(.min|.slim|.slim.min)?.js", "/wp-includes/js/jquery/jquery-migrate.min.js", "dataLayer"], "is_default": 0, "condition": "duracelltomi-google-tag-manager/duracelltomi-google-tag-manager-for-wordpress.php", "created_at": **********, "updated_at": "2023-02-15T04:32:17.000000Z", "icon_url": ""}, "a2b3c237-728c-49d6-882a-e3885444e9b4": {"id": "plugin:3b085ccda851ccf129d9506462f0cd65", "title": "GTranslate", "type": "plugin", "icon": "", "exclusions": ["translate.google.com", "googleTranslateElementInit"], "is_default": 0, "condition": "gtranslate/gtranslate.php", "created_at": **********, "updated_at": "2023-02-15T04:32:17.000000Z", "icon_url": ""}, "79dfc946-6b96-4b6d-bcaf-8cae6330581a": {"title": "GTranslate - Show language switcher on page load", "condition": "gtranslate/gtranslate.php", "exclusions": ["window.gtranslateSettings", "/wp-content/plugins/gtranslate/js/dwf.js"], "icon_url": "", "type": "plugin", "id": "plugin:0aedfaa0f543c47c4b0fcf57307c6691", "is_default": 0, "created_at": 1719940839}, "40cd9336-e73f-4a61-a7f6-27ec1ca6892c": {"id": "plugin:e1f17c1eac230219e6fec3ac07406a0d", "title": "HBook", "type": "plugin", "icon": "", "exclusions": ["/jquery-?[0-9.](.*)(.min|.slim|.slim.min)?.js", "/jquery-migrate(.min)?.js", "/hbook/", "hb_booking_form_data", "hb_max_date"], "is_default": 0, "condition": "hbook/hbook.php", "created_at": **********, "updated_at": "2023-02-15T04:32:17.000000Z", "icon_url": ""}, "192224c7-df72-4e3d-8f13-3206ac5b2e90": {"title": "Helper - OpenAI Chatbot for WordPress", "condition": "/helper/index.php", "exclusions": ["/wp-includes/js/jquery/jquery.min.js", "/helper/js/helper.min.js", "mdpHelper"], "icon_url": "", "type": "plugin", "id": "plugin:07311d992a8a9d6af91e4766d2cb9ac9", "is_default": 0, "created_at": 1691599768}, "99cfbb2e-5678-42c2-928f-5cb09d7e43da": {"id": "plugin:269c5766fba124a838ac012a4b5a1a13", "title": "HUSKY - Products Filter for WooCommerce", "type": "plugin", "icon": "", "exclusions": ["/jquery-?[0-9.](.*)(.min|.slim|.slim.min)?.js", "/jquery-migrate(.min)?.js", "/woocommerce-products-filter/(.*)", "woof"], "is_default": 0, "condition": "woocommerce-products-filter/index.php", "created_at": **********, "updated_at": "2023-02-15T04:32:17.000000Z", "icon_url": ""}, "3a964eac-4aeb-49a7-9fd6-4c31b2645dee": {"id": "plugin:d457acfec0f86aac6733f1446f4fc94f", "title": "Instagram Feed Pro", "type": "plugin", "icon": "", "exclusions": ["/jquery-?[0-9.](.*)(.min|.slim|.slim.min)?.js", "/jquery-migrate(.min)?.js", "/instagram-feed-pro/js/sbi-scripts.min.js", "sb_instagram_js_options"], "is_default": 0, "condition": "instagram-feed-pro/instagram-feed.php", "created_at": **********, "updated_at": "2023-02-15T04:32:16.000000Z", "icon_url": ""}, "8f9f4788-1b8d-468b-b291-1fcbd48618b9": {"id": "plugin:513b0c0ec8e12130af9b4bbbb17d7275", "title": "Interactive Geo Maps", "type": "plugin", "icon": "", "exclusions": ["/jquery-?[0-9.](.*)(.min|.slim|.slim.min)?.js", "/jquery-migrate(.min)?.js", "/interactive-geo-maps-premium/assets/public/map-service/app.min.js", "/interactive-geo-maps/assets/public/map-service/app.js", "iMapsData", "cdn.amcharts.com/lib/"], "is_default": 0, "condition": "interactive-geo-maps/interactive-geo-maps.php", "created_at": **********, "updated_at": "2023-02-15T04:32:17.000000Z", "icon_url": ""}, "cf650ca3-afa0-4a13-9e4f-f7cca19abac6": {"title": "Ivory Search", "condition": "add-search-to-menu/add-search-to-menu.php", "exclusions": ["/jquery-?[0-9.](.*)(.min|.slim|.slim.min)?.js", "/add-search-to-menu/public/js/ivory-search.min.js"], "icon_url": "", "type": "plugin", "id": "plugin:7430e9d2c985ae0a3339a7b7e1acc318", "is_default": 0, "created_at": 1677852711}, "17a5dd54-1f5e-4d57-a56d-a8a970651954": {"id": "plugin:1c8a1e1ba89a601f88654f094139b469", "title": "JetBlocks", "type": "plugin", "icon": "", "exclusions": ["/jquery-?[0-9.](.*)(.min|.slim|.slim.min)?.js", "/jquery-migrate(.min)?.js", "/jet-blocks/assets/js/jet-blocks.min.js"], "is_default": 0, "condition": "jet-blocks/jet-blocks.php", "created_at": **********, "updated_at": "2023-02-15T04:32:17.000000Z", "icon_url": ""}, "b6f78c90-9823-459a-8dc1-1257e5f0d4e3": {"id": "plugin:dac9ec782180f33d0fcc7c4e0e569b9d", "title": "JetBlog", "type": "plugin", "icon": "", "exclusions": ["/jquery-?[0-9.](.*)(.min|.slim|.slim.min)?.js", "/jquery-migrate(.min)?.js", "/elementor-pro/", "/elementor/", "/jet-blog/", "ElementorProFrontendConfig", "elementorFrontendConfig", "hasJetBlogPlaylist"], "is_default": 0, "condition": "jet-blog/jet-blog.php", "created_at": **********, "updated_at": "2023-02-15T04:32:17.000000Z", "icon_url": ""}, "e17173d8-6ef8-4e27-ba0f-379c9aea7eda": {"id": "plugin:0fe8895d1da5247b73a5d0b482df4ac4", "title": "JetElements", "type": "plugin", "icon": "", "exclusions": ["/jquery-?[0-9.](.*)(.min|.slim|.slim.min)?.js", "/jquery-migrate(.min)?.js", "/elementor/", "/elementor-pro/", "ElementorProFrontendConfig", "elementorFrontendConfig", "/jet-elements/", "hasJetBlogPlaylist", "jetElements", "/wp-includes/js/jquery/ui/"], "is_default": 0, "condition": "jet-elements/jet-elements.php", "created_at": **********, "updated_at": "2023-02-15T04:32:16.000000Z", "icon_url": ""}, "cfd77501-9c9e-4f11-b994-0178a592fc08": {"title": "JetEngine", "condition": "jet-engine/jet-engine.php", "exclusions": ["/jet-engine/"], "icon_url": "", "type": "plugin", "id": "plugin:bc998e71546860c8c7f70c45a6c18972", "is_default": 0, "created_at": 1686208296}, "36fed829-a2d9-41b5-94f6-2c3b9f07a94e": {"id": "plugin:433a3173f2d06f7d02c7b91c06ed215d", "title": "JetMenu", "type": "plugin", "icon": "", "exclusions": ["/jquery-?[0-9.](.*)(.min|.slim|.slim.min)?.js", "/jquery-migrate(.min)?.js", "/elementor-pro/", "/elementor/", "/jet-blog/assets/js/lib/slick/slick.min.js", "/jet-elements/", "/jet-menu/", "elementorFrontendConfig", "ElementorProFrontendConfig", "hasJetBlogPlaylist", "JetEngineSettings", "jetMenuPublicSettings", "/jet-reviews/assets/js/lib/vue.min.js"], "is_default": 0, "condition": "jet-menu/jet-menu.php", "created_at": **********, "updated_at": "2023-02-15T04:32:17.000000Z", "icon_url": ""}, "580cfed7-f034-4e39-bc7a-cdd01923ce87": {"id": "plugin:51714cfdb43f231a1c93e7cffb1007ab", "title": "JetPopup", "type": "plugin", "icon": "", "exclusions": ["/jet-popup/assets/js/lib/anime-js/anime.min.js", "/jet-popup/assets/js/jet-popup-frontend.js", "/jet-woo-builder/", "var jetPopupData"], "is_default": 0, "condition": "jet-popup/jet-popup.php", "created_at": **********, "updated_at": "2023-02-15T04:32:17.000000Z", "icon_url": ""}, "d9cd2730-12e2-42d7-a082-6f0efcd466cc": {"id": "plugin:df3bc4b6a8a9f28a5ba24cb7496bbc72", "title": "JetProductGallery", "type": "plugin", "icon": "", "exclusions": ["/jquery-?[0-9.](.*)(.min|.slim|.slim.min)?.js", "/jquery-migrate(.min)?.js", "/jet-woo-product-gallery/assets/lib/swiper/swiper.min.js", "/jet-woo-product-gallery/assets/js/jet-woo-product-gallery.min.js"], "is_default": 0, "condition": "jet-woo-product-gallery/jet-woo-product-gallery.php", "created_at": **********, "updated_at": "2023-02-15T04:32:17.000000Z", "icon_url": ""}, "544aa37b-4d52-4182-99e8-abe23585da14": {"id": "plugin:5921fc95965ac7fccb7296957bd9abff", "title": "JetReviews", "type": "plugin", "icon": "", "exclusions": ["/jquery-?[0-9.](.*)(.min|.slim|.slim.min)?.js", "/jquery-migrate(.min)?.js", "/jet-reviews/assets/js/jet-reviews-frontend.js", "/jet-reviews/assets/js/lib/vue.min.js", "jetReviewsWidget"], "is_default": 0, "condition": "jet-reviews/jet-reviews.php", "created_at": **********, "updated_at": "2023-02-15T04:32:16.000000Z", "icon_url": ""}, "963c7804-eede-4570-bd63-67066588b758": {"title": "JetSearch - Search results popup", "condition": "jet-search/jet-search.php", "exclusions": ["\\/jquery(-migrate)?-?([0-9.]+)?(.min|.slim|.slim.min)?.js(\\?(.*))?( |'|\"|>|$)", "/jet-search/assets/lib/chosen/chosen.jquery.min.js", "/jet-search/assets/js/jet-search.js", "/jet-search/assets/lib/jet-plugins/jet-plugins.js"], "icon_url": "", "type": "plugin", "id": "plugin:13e8760379022187de982df2226a5be4", "is_default": 0, "created_at": 1708541675}, "a8460089-34b4-4f8f-8694-5d92fa48aa82": {"title": "JetSmartFilters", "condition": "jet-smart-filters/jet-smart-filters.php", "exclusions": ["jetOffcanvasInitialized"], "icon_url": "", "type": "plugin", "id": "plugin:0cd63a514de1f1acb88e2bde65c4bc8d", "is_default": 0, "created_at": 1694447256}, "fe3c4915-6f8b-49b7-aa9c-c97b264d9f12": {"id": "plugin:a346b60514ef52afeffc6e2ef2793da3", "title": "JetSticky", "type": "plugin", "icon": "", "exclusions": ["/jetsticky-for-elementor/"], "is_default": 0, "condition": "jetsticky-for-elementor/jetsticky-for-elementor.php", "created_at": **********, "updated_at": "2023-02-15T04:32:17.000000Z", "icon_url": ""}, "853479dc-fff5-47cf-9a60-bf9b00f71fc4": {"title": "JetTabs for Elementor", "condition": "jet-tabs/jet-tabs.php", "exclusions": ["\\/jquery(-migrate)?-?([0-9.]+)?(.min|.slim|.slim.min)?.js(\\?(.*))?( |'|\"|>|$)", "/elementor/", "/elementor-pro/", "/wp-includes/js/imagesloaded.min.js", "ElementorProFrontendConfig", "elementorFrontendConfig", "/wp-content/plugins/jet-tabs/assets/js/jet-tabs-frontend.min.js"], "icon_url": "", "type": "plugin", "id": "plugin:d887a6ffea25d759e8aecdb6b78917f7", "is_default": 0, "created_at": 1697130971}, "b7f89562-230c-4f63-8360-7aad6df31e02": {"id": "plugin:829272546b040d5aaeeeaf976b6cd4ec", "title": "JetTricks", "type": "plugin", "icon": "", "exclusions": ["/jet-tricks/"], "is_default": 0, "condition": "jet-tricks/jet-tricks.php", "created_at": **********, "updated_at": "2023-02-15T04:32:17.000000Z", "icon_url": ""}, "90451e2a-553a-4496-9fa4-cffedbe69d43": {"id": "plugin:f44f3dbd09149f57db370e4132b057db", "title": "JetWoo Widgets For Elementor", "type": "plugin", "icon": "", "exclusions": ["/jquery-?[0-9.](.*)(.min|.slim|.slim.min)?.js", "/jquery-migrate(.min)?.js", "/elementor/", "/elementor-pro/", "ElementorProFrontendConfig", "elementorFrontendConfig", "/jetwoo-widgets-for-elementor/assets/js/jet-woo-widgets.js", "/jet-woo-builder/assets/js/jet-woo-builder.min.js", "/jet-woo-builder/assets/js/frontend.min.js", "/wp-includes/js/imagesloaded.min.js"], "is_default": 0, "condition": "jet-woo-builder/jet-woo-builder.php", "created_at": **********, "updated_at": "2023-02-15T04:32:16.000000Z", "icon_url": ""}, "944718c2-48c6-4f53-aaa2-460d07e033cc": {"id": "plugin:ed318a971f8a047bed5b02ad546a9c18", "title": "JetWooBuilder", "type": "plugin", "icon": "", "exclusions": ["/jquery-?[0-9.](.*)(.min|.slim|.slim.min)?.js", "/jquery-migrate(.min)?.js", "/wp-includes/js/dist/hooks.min.js", "/wp-includes/js/dist/i18n.min.js", "/elementor-pro/", "/elementor/", "elementorFrontendConfig", "ElementorProFrontendConfig", "JetEngineSettings", "/jet-woo-builder/", "/jet-woo-builder-custom-quantity-selectors-main/assets/js/main.js"], "is_default": 0, "condition": "jet-woo-builder/jet-woo-builder.php", "created_at": **********, "updated_at": "2023-02-15T04:32:17.000000Z", "icon_url": ""}, "f5b62f59-1dae-4cdf-ac3f-f5e846fc9918": {"id": "plugin:9562e253cd4ac2b1e1f70e32cb4e32dc", "title": "Layer Slider", "type": "plugin", "icon": "", "exclusions": ["/jquery-?[0-9.](.*)(.min|.slim|.slim.min)?.js", "/jquery-migrate(.min)?.js", "/LayerSlider/assets/static/layerslider/js/layerslider.utils.js", "/LayerSlider/assets/static/layerslider/js/layerslider.kreaturamedia.jquery.js", "/LayerSlider/assets/static/layerslider/js/layerslider.transitions.js", "initLayerSlider"], "is_default": 0, "condition": "LayerSlider/layerslider.php", "created_at": **********, "updated_at": "2023-02-15T04:32:16.000000Z", "icon_url": ""}, "e2be718d-83a2-4fa0-bc98-f0df52be3dc1": {"id": "plugin:1425e2735306796fe1539d9184a77e10", "title": "LoftLoader Pro", "type": "plugin", "icon": "", "exclusions": ["/jquery-?[0-9.](.*)(.min|.slim|.slim.min)?.js", "/jquery-migrate(.min)?.js", "loftloader.min.js"], "is_default": 0, "condition": "loftloader-pro/loftloader-pro.php", "created_at": **********, "updated_at": "2023-02-15T04:32:17.000000Z", "icon_url": ""}, "cfa18552-656e-453c-9e14-c07522dc6598": {"id": "plugin:0bf67b8ba84771e1a367fe24590ef09c", "title": "MailUp for WordPress", "type": "plugin", "icon": "", "exclusions": ["/mailup-email-and-newsletter-subscription-form/public/js/mailup-public.js", "mailup-js-extra"], "is_default": 0, "condition": "mailup-email-and-newsletter-subscription-form/mailup.php", "created_at": **********, "updated_at": "2023-02-15T04:32:16.000000Z", "icon_url": ""}, "c5330c66-ba7b-45b1-87e4-ce590ab005dd": {"id": "plugin:10d0de28911c5f66463b9c8783f8148a", "title": "Maintenance", "type": "plugin", "icon": "", "exclusions": ["/jquery-?[0-9.](.*)(.min|.slim|.slim.min)?.js", "/jquery-migrate(.min)?.js", "/maintenance/"], "is_default": 0, "condition": "maintenance/maintenance.php", "created_at": **********, "updated_at": "2023-02-15T04:32:16.000000Z", "icon_url": ""}, "62f6663a-a15f-4bca-b9b5-79f1770e2c5a": {"title": "MapifyLite- Show map on page load", "condition": "mapifylite/mapify_lite.php", "exclusions": ["\\/jquery(-migrate)?-?([0-9.]+)?(.min|.slim|.slim.min)?.js(\\?(.*))?( |'|\"|>|$)", "/plugins/mapifylite/assets/vendor/leaflet-1.7.1/leaflet.js", "/plugins/mapifylite/assets/vendor/leaflet/markercluster/leaflet.markercluster.js", "/plugins/mapifylite/assets/vendor/leaflet/locatecontrol/L.Control.Locate.min.js", "/plugins/mapifylite/assets/vendor/tooltip.js", "/plugins/mapifylite/assets/js/dist/bundle.js", "var map"], "icon_url": "", "type": "plugin", "id": "plugin:48c30c0f37b64accf88126d4ef58e17b", "is_default": 0, "created_at": 1720028033}, "267aef71-afa0-4848-b6f8-3e1ca15c3a23": {"id": "plugin:9460789bdfe77425c895f130991a4cb4", "title": "Maps Marker Pro", "type": "plugin", "icon": "", "exclusions": ["/maps-marker-pro/js/mapsmarkerpro.js", "var mapsMarkerPro"], "is_default": 0, "condition": "maps-marker-pro/maps-marker-pro.php", "created_at": **********, "updated_at": "2023-02-15T04:32:17.000000Z", "icon_url": ""}, "b5a88b63-e906-49b3-8134-420139915ea6": {"title": "Master Popups", "condition": "master-popups/master-popups.php", "exclusions": ["/jquery-?[0-9.](.*)(.min|.slim|.slim.min)?.js", "/jquery-migrate(.min)?.js", "/plugins/master-popups/assets/public/js/master-popups-libs.min.js"], "icon_url": "", "type": "plugin", "id": "plugin:8fd7bd2c8b0a33e62798573abd12be63", "is_default": 0, "created_at": 1681305305}, "4be41549-bf4d-4c49-8346-0f8a3b88fdba": {"id": "plugin:de888634cc4bd51576eed319d5a528fd", "title": "Master Slider", "type": "plugin", "icon": "", "exclusions": ["masterslider"], "is_default": 0, "condition": "master-slider/master-slider.php", "created_at": **********, "updated_at": "2023-02-15T04:32:17.000000Z", "icon_url": ""}, "087bee18-7cd1-4c10-8acc-19e1b7c4f4cd": {"id": "plugin:b71309a89bf3c8b558b6fca5d6531919", "title": "Max Mega Menu", "type": "plugin", "icon": "", "exclusions": ["/jquery-?[0-9.](.*)(.min|.slim|.slim.min)?.js", "/jquery-migrate(.min)?.js", "/wp-includes/js/hoverIntent.min.js", "/megamenu/js/maxmegamenu.js", "var megamenu"], "is_default": 0, "condition": "megamenu/megamenu.php", "created_at": **********, "updated_at": "2023-02-15T04:32:16.000000Z", "icon_url": ""}, "cf22f0b9-01f4-409e-8a93-ad6743095abd": {"id": "plugin:94a7bae84ef2816494be4af66c577bfc", "title": "<PERSON><PERSON>", "type": "plugin", "icon": "", "exclusions": ["/jquery-?[0-9.](.*)(.min|.slim|.slim.min)?.js", "/jquery-migrate(.min)?.js", "/ml-slider/", "var metaslider"], "is_default": 0, "condition": "ml-slider/ml-slider.php", "created_at": **********, "updated_at": "2023-02-15T04:32:16.000000Z", "icon_url": ""}, "bb2cbc0d-20da-415e-8218-c17f1db53579": {"title": "Monarch", "condition": "monarch/monarch.php", "exclusions": ["/jquery-?[0-9.](.*)(.min|.slim|.slim.min)?.js", "/jquery-migrate(.min)?.js", "dt-place-monarch-icons"], "icon_url": "", "type": "plugin", "id": "plugin:b7335d6d6d5c5134ea10ac2d9b04226c", "is_default": 0, "created_at": 1677853476}, "a98d6a80-4610-4ede-bd33-c3e15bed0a95": {"id": "plugin:4f8651262425ef6d7c223c68a2ec2063", "title": "Monster Insights", "type": "plugin", "icon": "", "exclusions": ["__gtagTracker", "monsterinsights_frontend", "/google-analytics-for-wordpress/assets/js/frontend-gtag.min.js"], "is_default": 0, "condition": "google-analytics-for-wordpress/googleanalytics.php", "created_at": **********, "updated_at": "2023-02-15T04:32:16.000000Z", "icon_url": ""}, "26e1a36e-7a03-449e-8ecc-e42c41ca291c": {"id": "plugin:f8fc66b302dec2327bdec0434a3b275b", "title": "Motion.page", "type": "plugin", "icon": "", "exclusions": ["/motionpage/core/includes/assets/js/(.*)", "/motionpage/core/includes/assets/js/gsap/(.*)", "/motionpage/assets/js/(.*)", "/motionpage/assets/js/gsap/(.*)"], "is_default": 0, "condition": "motionpage/motionpage.php", "created_at": **********, "updated_at": "2023-02-15T04:32:16.000000Z", "icon_url": ""}, "459fd663-7f80-45fd-8308-d3484981e161": {"title": "Ninja Forms", "condition": "ninja-forms/ninja-forms.php", "exclusions": ["/jquery-?[0-9.](.*)(.min|.slim|.slim.min)?.js", "/jquery-migrate(.min)?.js", "/wp-includes/js/underscore.min.js", "/wp-includes/js/backbone.min.js", "/wp-includes/js/jquery/ui/core.min.js", "/ninja-forms/assets/js/min/front-end-deps.js", "/ninja-forms/assets/js/min/front-end.js", "nf-"], "icon_url": "", "type": "plugin", "id": "plugin:1d3ae9c1a96d5062616968b81eef319d", "is_default": 0, "created_at": 1703190792}, "c97440dd-7592-40e7-8c98-dac20d39808f": {"id": "plugin:723a588dcd49285ea9f7404e2379b47f", "title": "Ninja Tables", "type": "plugin", "icon": "", "exclusions": ["/jquery-?[0-9.](.*)(.min|.slim|.slim.min)?.js", "/jquery-migrate(.min)?.js", "/ninja-tables(.*)/assets/", "/ninja-tables(.*)/public/", "/wp-includes/js/dist/vendor/moment.min.js", "ninja_table_instance_", "ninja_filter_", "ninja_table_ready_init_table_id"], "is_default": 0, "condition": "ninja-tables/ninja-tables.php", "created_at": **********, "updated_at": "2023-02-15T04:32:17.000000Z", "icon_url": ""}, "ebc01f06-8c35-47fa-9c63-8959c5cbd915": {"id": "plugin:f41a52ab1dd50a81cd3a5e341af0007c", "title": "NotificationX", "type": "plugin", "icon": "", "exclusions": ["/jquery-?[0-9.](.*)(.min|.slim|.slim.min)?.js", "/jquery-migrate(.min)?.js", "/notificationx/assets/public/js/(.*).js", "notificationXArr"], "is_default": 0, "condition": "notificationx/notificationx.php", "created_at": **********, "updated_at": "2023-02-15T04:32:17.000000Z", "icon_url": ""}, "3aaf4a1b-591a-48b7-8d60-e6d65c0f94c9": {"id": "plugin:be8fc72a8b8e8eb5958be13737cff47c", "title": "Ocean Elementor Widgets", "type": "plugin", "icon": "", "exclusions": ["/jquery-?[0-9.](.*)(.min|.slim|.slim.min)?.js", "/jquery-migrate(.min)?.js", "/elementor/", "/elementor-pro/", "ElementorProFrontendConfig", "elementorFrontendConfig", "/ocean-elementor-widgets/"], "is_default": 0, "condition": "ocean-elementor-widgets/ocean-elementor-widgets.php", "created_at": **********, "updated_at": "2023-02-15T04:32:17.000000Z", "icon_url": ""}, "f053a7d3-62b0-4312-b0ca-6ff63380eb0b": {"id": "plugin:1a14afe852fefe7b22ccad3893672a29", "title": "One Click Accessibility", "type": "plugin", "icon": "", "exclusions": ["/jquery-?[0-9.](.*)(.min|.slim|.slim.min)?.js", "/jquery-migrate(.min)?.js", "/pojo-accessibility/assets/js/app.min.js"], "is_default": 0, "condition": "pojo-accessibility/pojo-accessibility.php", "created_at": **********, "updated_at": "2023-02-15T04:32:16.000000Z", "icon_url": ""}, "61e11800-48a9-4354-8a4c-ac9a2b4b033a": {"id": "plugin:b9ed0fe6f2cdbd305691a6b857f4b3dc", "title": "OoohBoi Steroids for Elementor", "type": "plugin", "icon": "", "exclusions": ["/jquery-?[0-9.](.*)(.min|.slim|.slim.min)?.js", "/jquery-migrate(.min)?.js", "/elementor/", "/elementor-pro/", "ElementorProFrontendConfig", "elementorFrontendConfig", "/ooohboi-steroids-for-elementor/"], "is_default": 0, "condition": "ooohboi-steroids-for-elementor/ooohboi-steroids.php", "created_at": **********, "updated_at": "2023-02-15T04:32:17.000000Z", "icon_url": ""}, "560b78a2-f051-490b-9dc6-a602dece0d81": {"id": "plugin:f1ff18a3e04c4e0995fca9cabffe57a7", "title": "Optimole", "type": "plugin", "icon": "", "exclusions": ["optimoleData"], "is_default": 0, "condition": "optimole-wp/optimole-wp.php", "created_at": **********, "updated_at": "2023-02-15T04:32:16.000000Z", "icon_url": ""}, "b0fda458-4bf2-41e9-a159-60d4bb6102a6": {"id": "plugin:48ec18bd3f59772d98f85dddab75e305", "title": "OSM - OpenStreetMap", "type": "plugin", "icon": "", "exclusions": ["/jquery-?[0-9.](.*)(.min|.slim|.slim.min)?.js", "/jquery-migrate(.min)?.js", "/osm/js/OL/", "/osm/js/osm-v3-plugin-lib.js", "vectorM"], "is_default": 0, "condition": "osm/osm.php", "created_at": **********, "updated_at": "2023-02-15T04:32:16.000000Z", "icon_url": ""}, "3743ed67-e0d1-4243-bb1f-ee48a445dd10": {"id": "plugin:9eacf0b76484af0259cd788f4923f20c", "title": "OxyExtras", "type": "plugin", "icon": "", "exclusions": ["vime", "vime.esm.js"], "is_default": 0, "condition": "oxyextras/plugin.php", "created_at": **********, "updated_at": "2023-02-15T04:32:16.000000Z", "icon_url": ""}, "52a21e27-af5b-4476-8d6d-54c323fd1443": {"id": "plugin:e852555c4b4789d78a96d76f503b3262", "title": "Oxygen Builder", "type": "plugin", "icon": "", "exclusions": ["/jquery-?[0-9.](.*)(.min|.slim|.slim.min)?.js", "/jquery-migrate(.min)?.js", "/component-framework/vendor/aos/aos.js", "AOS.init", "oxygen_init_pro_menu", "oxy-pro-menu-show-dropdown", "oxy-shape-divider", "oxygenVSBInitToggleJs"], "is_default": 0, "condition": "oxygen/functions.php", "created_at": **********, "updated_at": "2023-02-15T04:32:16.000000Z", "icon_url": ""}, "ebd282b9-e175-4b83-90a2-fe12389ccd11": {"id": "plugin:6f9419e58ec86c94e7698aaaaf9dc715", "title": "PageLoader by Bonfire", "type": "plugin", "icon": "", "exclusions": ["/jquery-?[0-9.](.*)(.min|.slim|.slim.min)?.js", "/jquery-migrate(.min)?.js", "/pageloader-by-bonfire/pageloader.js", "bonfire-pageloader-overlay"], "is_default": 0, "condition": "pageloader-by-bonfire/pageloader-by-bonfire.php", "created_at": **********, "updated_at": "2023-02-15T04:32:17.000000Z", "icon_url": ""}, "0baf6353-aa70-41a9-a3bc-d89870a5839b": {"id": "plugin:3a4c4518eb0f60108ab4934fab27d335", "title": "PDF Embedder", "type": "plugin", "icon": "", "exclusions": ["/jquery-?[0-9.](.*)(.min|.slim|.slim.min)?.js", "/jquery-migrate(.min)?.js", "/pdf-embedder/js/(.*).js"], "is_default": 0, "condition": "pdf-embedder/pdf_embedder.php", "created_at": **********, "updated_at": "2023-02-15T04:32:16.000000Z", "icon_url": ""}, "cba51ec1-87cc-44eb-b12b-d652e3446507": {"id": "plugin:4f90aca5957cccbf623e3bbc31afa204", "title": "Perfect Brands for WooCommerce", "type": "plugin", "icon": "", "exclusions": ["/jquery-?[0-9.](.*)(.min|.slim|.slim.min)?.js", "/jquery-migrate(.min)?.js", "/perfect-woocommerce-brands/assets/lib/slick/slick.min.js", "/perfect-woocommerce-brands/assets/js/functions-frontend.min.js"], "is_default": 0, "condition": "perfect-woocommerce-brands/perfect-woocommerce-brands.php", "created_at": **********, "updated_at": "2023-02-15T04:32:16.000000Z", "icon_url": ""}, "808e7a4e-025c-4fae-bf82-de3716e5eb0d": {"id": "plugin:84fe214646388d7c021dda25003ac165", "title": "Photonic", "type": "plugin", "icon": "", "exclusions": ["/photonic/include/js/front-end/module/photonic-baguettebox.min.js"], "is_default": 0, "condition": "photonic/photonic.php", "created_at": **********, "updated_at": "2023-02-15T04:32:17.000000Z", "icon_url": ""}, "936aff7d-a8ac-4473-a7b5-4b1611b6c557": {"id": "plugin:6838be282f853f71be282783cb1c162b", "title": "Pixel Caffein", "type": "plugin", "icon": "", "exclusions": ["/jquery-?[0-9.](.*)(.min|.slim|.slim.min)?.js", "/pixel-caffeine/build/frontend.js", "aepc_pixel"], "is_default": 0, "condition": "pixel-caffeine/pixel-caffeine.php", "created_at": **********, "updated_at": "2023-02-15T04:32:16.000000Z", "icon_url": ""}, "ab3a4ef5-2732-4b46-a7c3-17b4e9405cd1": {"id": "plugin:afe0eb7c64d4556a7111c56dd8c4d307", "title": "Pixel Manager for WooCommerce", "type": "plugin", "icon": "", "exclusions": ["/jquery-?[0-9.](.*)(.min|.slim|.slim.min)?.js", "/woocommerce-pixel-manager-pro/js/public/", "wpm"], "is_default": 0, "condition": "woocommerce-pixel-manager-pro/woocommerce-pixel-manager.php", "created_at": **********, "updated_at": "2023-02-15T04:32:16.000000Z", "icon_url": ""}, "2774f964-50b2-425e-8a30-0a02f421b7e4": {"id": "plugin:38d4b2986868f543639cd1ebc3e510aa", "title": "Popup Builder", "type": "plugin", "icon": "", "exclusions": ["/jquery-?[0-9.](.*)(.min|.slim|.slim.min)?.js", "/jquery-migrate(.min)?.js", "/popup-builder/public/js/(.*).js", "/popupbuilder-exit-intent/public/javascript/ExitIntent.js", "var sgpbPublicUrl", "SGPB_POPUP_PARAMS"], "is_default": 0, "condition": "popup-builder/popup-builder.php", "created_at": **********, "updated_at": "2023-02-15T04:32:16.000000Z", "icon_url": ""}, "82dbb1e5-31f9-43d7-b522-52819aa49ba5": {"id": "plugin:786717922362642f34a7ff58e919bd95", "title": "Popup Maker", "type": "plugin", "icon": "", "exclusions": ["/jquery-?[0-9.](.*)(.min|.slim|.slim.min)?.js", "/jquery-migrate(.min)?.js", "/wp-includes/js/jquery/ui/core.min.js", "/pum/pum-site-scripts.js", "pum", "/plugins/popup-maker/assets/js/site.min.js"], "is_default": 0, "condition": "popup-maker/popup-maker.php", "created_at": **********, "updated_at": "2023-02-15T04:32:16.000000Z", "icon_url": ""}, "48e08305-1f1c-4ef4-9f13-24af1b155abc": {"id": "plugin:17831cbb64e469c7f66224c8c63d0a58", "title": "PowerPack Addons for Elementor", "type": "plugin", "icon": "", "exclusions": ["/powerpack-lite-for-elementor/assets/js/min/frontend.min.js"], "is_default": 0, "condition": "powerpack-lite-for-elementor/powerpack-lite-elementor.php", "created_at": **********, "updated_at": "2023-02-15T04:32:16.000000Z", "icon_url": ""}, "e037ae2f-e36b-4f1a-ada8-eb8fde6746f2": {"id": "plugin:80e1283ea1afead3ca904fad792643c5", "title": "Preloader Plus", "type": "plugin", "icon": "", "exclusions": ["/jquery-?[0-9.](.*)(.min|.slim|.slim.min)?.js", "/wp-includes/js/jquery/jquery-migrate.min.js", "/preloader-plus/(.*)"], "is_default": 0, "condition": "preloader-plus/preloader-plus.php", "created_at": **********, "updated_at": "2023-02-15T04:32:16.000000Z", "icon_url": ""}, "90fed478-e89a-455c-8371-836bee58fb81": {"title": "Premium Addons for Elementor", "condition": "premium-addons-for-elementor/premium-addons-for-elementor.php", "exclusions": ["/jquery-?[0-9.](.*)(.min|.slim|.slim.min)?.js", "/jquery-migrate(.min)?.js", "/premium-addons-for-elementor/assets/frontend/min-js/premium-addons.min.js", "/premium-addons-for-elementor/assets/frontend/min-js/isotope.min.js", "/premium-addons-elementor/pa-frontend-(.*).min.js", "/premium-addons-for-elementor/assets/frontend/min-js/slick.min.js", "/premium-addons-pro/assets/frontend/min-js/tooltipster.min.js", "window.scopes_array", "lottie.min.js", "/premium-addons-for-elementor/assets/frontend/min-js/premium-nav-menu.min.js"], "icon_url": "", "type": "plugin", "id": "plugin:debbfbbcbdf9ffb465bbc40008d99f02", "is_default": 0, "created_at": 1693315214}, "7e43c261-77be-48fc-b25a-8953a654ae85": {"id": "plugin:100a0382fcf3d1b6b22da928bce46ea8", "title": "Presto Player", "type": "plugin", "icon": "", "exclusions": ["/wp-includes/js/dist/vendor/regenerator-runtime.min.js", "/presto-player/dist/components/web-components/web-components.esm.js", "/presto-player/src/player/player-static.js", "var player", "/wp-includes/js/dist/api-fetch.min.js", "/wp-includes/js/dist/hooks.min.js", "/wp-includes/js/dist/i18n.min.js"], "is_default": 0, "condition": "presto-player/presto-player.php", "created_at": **********, "updated_at": "2023-02-15T04:32:17.000000Z", "icon_url": ""}, "3d6f509f-d040-4279-9a2f-a4e6eae1e9df": {"id": "plugin:c0fce700121492b566517e68301db382", "title": "Price Based on Country for WooCommerce Pro", "type": "plugin", "icon": "", "exclusions": ["/woocommerce-product-price-based-on-countries/assets/js/ajax-geolocation.min.js", "/woocommerce-price-based-country-pro-addon/assets/js/currency-switcher.min.js", "add-to-cart.min.js", "cart-fragments.min.js"], "is_default": 0, "condition": "woocommerce-price-based-country-pro-addon/woocommerce-price-based-country-pro-addon.php", "created_at": **********, "updated_at": "2023-02-15T04:32:17.000000Z", "icon_url": ""}, "b970bc8a-bcbe-4d08-8ac0-a6853ab3f036": {"id": "plugin:f1ecfe258440b371124999ca3bfbfff3", "title": "Prime Slider", "type": "plugin", "icon": "", "exclusions": ["/plugins/bdthemes-prime-slider-lite/assets/js/bdt-uikit.min.js"], "is_default": 0, "condition": "bdthemes-prime-slider-lite/bdthemes-prime-slider.php", "created_at": **********, "updated_at": "2023-02-15T04:32:16.000000Z", "icon_url": ""}, "e5458963-f167-4b68-88e4-77dd39af2842": {"id": "plugin:29ea8f4dd72f5a5c5927917fb0665a05", "title": "PRO Elements", "type": "plugin", "icon": "", "exclusions": ["/jquery-?[0-9.](.*)(.min|.slim|.slim.min)?.js", "/jquery-migrate(.min)?.js", "/pro-elements/"], "is_default": 0, "condition": "pro-elements/pro-elements.php", "created_at": **********, "updated_at": "2023-02-15T04:32:16.000000Z", "icon_url": ""}, "b119ad72-f498-4576-bb71-9e6d8c37b68f": {"title": "Product Filter by WBW (for WooCommerce)", "condition": "woo-product-filter/woo-product-filter.php", "exclusions": ["/jquery-?([0-9.]+)?(.min|.slim|.slim.min)?.js(\\?(.*))?$", "/jquery-migrate(.*)(.min|.slim|.slim.min)?.js(\\?(.*))?$", "/wp-content/plugins/woo-product-filter/modules/woofilters/js/frontend.woofilters.js", "/wp-includes/js/jquery/ui/mouse.min.js", "/wp-includes/js/jquery/ui/core.min.js", "/wp-includes/js/jquery/ui/slider.min.js"], "icon_url": "", "type": "plugin", "id": "plugin:e57ad91e0d94fab011353af544873743", "is_default": 0, "created_at": **********}, "7b779aca-e497-4da3-8e51-fa12837d15ab": {"title": "Product Filters for WooCommerce", "condition": "woocommerce-product-filters/woocommerce-product-filters.php", "exclusions": ["/jquery-?[0-9.](.*)(.min|.slim|.slim.min)?.js", "/jquery-migrate(.min)?.js", "/woocommerce-product-filters/", "/woocommerce/assets/js/accounting/accounting.min.js", "/wp-includes/js/jquery/ui/", "wcpf-load-project", "WCPFData"], "icon_url": "", "type": "plugin", "id": "plugin:719469f1c977f7109d3d6ee21ecd5a16", "is_default": 0, "created_at": **********}, "2b2a51f2-bd47-4591-92d8-4a690bce5d99": {"id": "plugin:a898898b2b0ea2cd82e20a6d3a3aa47b", "title": "Product Gallery Slider for WooCommerce", "type": "plugin", "icon": "", "exclusions": ["/jquery-?[0-9.](.*)(.min|.slim|.slim.min)?.js", "/jquery-migrate(.min)?.js", "/twist/assets/js/slick.min.js", "wpgs-public-js-after"], "is_default": 0, "condition": "twist/twist.php", "created_at": **********, "updated_at": "2023-02-15T04:32:17.000000Z", "icon_url": ""}, "062c5be3-e5ea-4958-9619-44e3410f237e": {"title": "Product Video Gallery for Woocommerce", "condition": "product-video-gallery-slider-for-woocommerce/product-video-gallery-slider-for-woocommerce.php", "exclusions": ["/jquery-?[0-9.](.*)(.min|.slim|.slim.min)?.js", "/jquery-migrate(.min)?.js", "/product-video-gallery-slider-for-woocommerce/public/js/nickx.front.js"], "icon_url": "", "type": "plugin", "id": "plugin:bbc653a91f0635cd2edb0b741aa62b85", "is_default": 0, "created_at": 1693512477}, "34d225a4-688c-476b-846b-420774160d6b": {"id": "plugin:e0b123d324c6fc85b8682660c34f8829", "title": "Rank Math SEO", "type": "plugin", "icon": "", "exclusions": ["local_ga_js"], "is_default": 0, "condition": "seo-by-rank-math/rank-math.php", "created_at": **********, "updated_at": "2023-02-15T04:32:16.000000Z", "icon_url": ""}, "02305ca0-2c2a-4eef-a802-b8a942771ecc": {"title": "Retainful", "condition": "retainful-next-order-coupon-for-woocommerce/retainful-next-order-coupon-for-woocommerce.php", "exclusions": ["/jquery-?[0-9.](.*)(.min|.slim|.slim.min)?.js", "/jquery-migrate(.min)?.js", "/retainful-next-order-coupon-for-woocommerce/src/premium/assets/js/atc-popup.min.js", "/retainful-next-order-coupon-for-woocommerce/src/premium/assets/js/exit-intent-popup.js", "retainful.com", "rnoc-add-to-cart-js-before", "rnoc_redirect_coupon_popup"], "icon_url": "", "type": "plugin", "id": "plugin:f9fdee19ba6aed961d96d86e1521a761", "is_default": 0, "created_at": 1679331508}, "b4055250-5813-400f-b663-d390fee989e4": {"title": "Revolution Slider", "condition": "revslider/revslider.php", "exclusions": ["/jquery-?[0-9.](.*)(.min|.slim|.slim.min)?.js", "/jquery-migrate(.min)?.js", "/revslider/public/assets/js/", "/revslider/sr6/assets/js/", "/revslider-(.*)-addon/", "setREVStartSize", "rev_slider_", "revslider_", "window.RS_MODULES", "/revslider/public/js/libs/tptools.js", "/revslider/public/js/sr7.js", "SR7"], "icon_url": "", "type": "plugin", "id": "plugin:d6a4d07d1b4022d886df52322dcd8a6f", "is_default": 0, "created_at": 1714049967}, "0b4e061a-b366-4d5a-a00e-bded4b107133": {"title": "Royal Elementor Addons", "condition": "royal-elementor-addons/wpr-addons.php", "exclusions": ["\\/jquery(-migrate)?-?([0-9.]+)?(.min|.slim|.slim.min)?.js(\\?(.*))?( |'|\"|>)", "/royal-elementor-addons/assets/js/frontend.min.js", "/royal-elementor-addons/assets/js/lib/jarallax/jarallax.min.js"], "icon_url": "", "type": "plugin", "id": "plugin:c4b464373716d7ac8e3cfb019aaa6102", "is_default": 0, "created_at": 1688124503}, "1b19ec89-171a-4f85-8c4b-b1bcfc6b1433": {"id": "plugin:5e3f85d8c82cc184b945415d1a862601", "title": "Sassy Social Share", "type": "plugin", "icon": "", "exclusions": ["/jquery-?[0-9.](.*)(.min|.slim|.slim.min)?.js", "/jquery-migrate(.min)?.js", "/sassy-social-share/public/js/sassy-social-share-public.js", "heateorSssLoadEvent"], "is_default": 0, "condition": "sassy-social-share/sassy-social-share.php", "created_at": **********, "updated_at": "2023-02-15T04:32:16.000000Z", "icon_url": ""}, "f5899925-2ab7-43b6-abc4-51b76d664ca6": {"title": "Scrollsequence", "condition": "scrollsequence-pro/scrollsequence-pro.php", "exclusions": ["/jquery-?[0-9.](.*)(.min|.slim|.slim.min)?.js", "/jquery-migrate(.min)?.js", "/scrollsequence-pro/public/js/gsap__premium_only.js", "/scrollsequence-pro/public/js/gsap-scrolltrigger__premium_only.js", "/scrollsequence-pro/public/js/ssq-lib__premium_only.js", "scrollsequence-input-script"], "icon_url": "", "type": "plugin", "id": "plugin:3df51830b6b80668fc342c8dcea495cf", "is_default": 0, "created_at": 1678111131}, "043827fc-3df8-45d6-9cd2-14fbe962987a": {"id": "plugin:9d60b5d2de4d828b78c7b088024377d6", "title": "ShiftNav Pro - Responsive Mobile Menu", "type": "plugin", "icon": "", "exclusions": ["/jquery-?[0-9.](.*)(.min|.slim|.slim.min)?.js", "/jquery-migrate(.min)?.js", "/shiftnav-pro/assets/js/shiftnav(.*).js"], "is_default": 0, "condition": "shiftnav-pro/shiftnav.php", "created_at": **********, "updated_at": "2023-02-15T04:32:16.000000Z", "icon_url": ""}, "bcbaf240-e76f-4620-b7d5-4852c46d4be7": {"id": "plugin:56279bd768c8f27ad1972b6774738bcf", "title": "ShiftNav – Responsive Mobile Menu", "type": "plugin", "icon": "", "exclusions": ["/jquery-?[0-9.](.*)(.min|.slim|.slim.min)?.js", "/jquery-migrate(.min)?.js", "/shiftnav-responsive-mobile-menu/"], "is_default": 0, "condition": "shiftnav-responsive-mobile-menu/shiftnav-responsive-mobile-menu.php", "created_at": **********, "updated_at": "2023-02-15T04:32:16.000000Z", "icon_url": ""}, "bbe0543b-b5af-467b-a90d-e2975d892d8d": {"title": "Short Pixel Adaptive Image", "condition": "shortpixel-adaptive-images/short-pixel-ai.php", "exclusions": ["/jquery-?[0-9.](.*)(.min|.slim|.slim.min)?.js", "/jquery-migrate(.min)?.js", "/shortpixel-adaptive-images/assets/js/ai(.*).min.js", "spai_settings"], "icon_url": "", "type": "plugin", "id": "plugin:1bc7ac87d8ab2301a1b904919dc7a798", "is_default": 0, "created_at": 1704734942}, "543ab43a-6bd3-4948-a09e-3b95e7c9209d": {"title": "Showcase IDX", "condition": "showcase-idx/showcaseidx.php", "exclusions": ["/jquery-?[0-9.](.*)(.min|.slim|.slim.min)?.js", "/jquery-migrate(.min)?.js", "SIDX", "search.showcaseidx.com/js/app-(.*).js", "cdn.shortpixel.ai"], "icon_url": "", "type": "plugin", "id": "plugin:ce4dea8ddc3caa8d00e95cec3202d32a", "is_default": 0, "created_at": 1704734988}, "a16bcab7-1169-46b9-a425-b091478d8312": {"id": "plugin:3ea7d9f75ad03620b0bce2517bd5b8d1", "title": "Side Cart WooCommerce", "type": "plugin", "icon": "", "exclusions": ["/jquery-?[0-9.](.*)(.min|.slim|.slim.min)?.js", "/jquery-migrate(.min)?.js", "/plugins/woocommerce/assets/js/frontend/cart-fragments.min.js", "/plugins/woocommerce/assets/js/frontend/add-to-cart.min.js", "/plugins/woocommerce/assets/js/jquery-blockui/jquery.blockUI.min.js", "/plugins/woocommerce/assets/js/js-cookie/js.cookie.min.js"], "is_default": 0, "condition": "side-cart-woocommerce/xoo-wsc-main.php", "created_at": **********, "updated_at": "2023-02-15T04:32:16.000000Z", "icon_url": ""}, "521f748e-cf09-4019-bd28-b52daef5f16f": {"id": "plugin:7843983bf90dbae16c6e889382c71b23", "title": "Simple Banner", "type": "plugin", "icon": "", "exclusions": ["/wp-includes/js/jquery/jquery.min.js", "/simple-banner/", "simpleBannerScriptParams"], "is_default": 0, "condition": "simple-banner/simple-banner.php", "created_at": **********, "updated_at": "2023-02-15T04:32:17.000000Z", "icon_url": ""}, "76026e82-d9cf-482c-b480-55c57693a184": {"id": "plugin:b6825f971d35a5515d095564a2e40936", "title": "Site Kit by Google", "type": "plugin", "icon": "", "exclusions": ["google-analytics.com/analytics.js", "ga\\( '", "ga\\('", "/gtag/js", "gtag\\(", "/gtm.js"], "is_default": 0, "condition": "google-site-kit/google-site-kit.php", "created_at": **********, "updated_at": "2023-02-15T04:32:16.000000Z", "icon_url": ""}, "4f9be7dd-fa19-401c-a5a5-81654990f9c4": {"title": "SiteOrigin Widgets Bundle - Load images", "condition": "so-widgets-bundle/so-widgets-bundle.php", "exclusions": ["\\/jquery(-migrate)?-?([0-9.]+)?(.min|.slim|.slim.min)?.js(\\?(.*))?( |'|\"|>|$)", "/so-widgets-bundle/js/slider/jquery.slider.min.js", "/so-widgets-bundle/js/jquery.cycle.min.js", "/so-widgets-bundle/js/jquery.cycle.swipe.min.js", "/so-widgets-bundle/js/sow.jquery.fittext.min.js", "/so-widgets-bundle/js/lib/jquery.fitvids.min.js", "/siteorigin-panels/js/styling.min.js", "siteorigin-panels-before-js", "page_id"], "icon_url": "", "type": "plugin", "id": "plugin:3968480c93d7f92ed1cb78a54b47fc9f", "is_default": 0, "created_at": 1716234178}, "79c458d7-f7f7-4072-919d-ad337bde6ae3": {"title": "<PERSON><PERSON>", "condition": "slick-menu/slick-menu.php", "exclusions": ["/jquery-?[0-9.](.*)(.min|.slim|.slim.min)?.js", "/jquery-migrate(.min)?.js", "/slick-menu/"], "icon_url": "", "type": "plugin", "id": "plugin:5ce5dfecdc9d3292c69a1413bbd17d3c", "is_default": 0, "created_at": 1704735027}, "99cf4942-49ea-4687-b156-405ed1ce1cfa": {"title": "Slide Anything", "condition": "slide-anything/slide-anything.php", "exclusions": ["\\/jquery(-migrate)?-?([0-9.]+)?(.min|.slim|.slim.min)?.js(\\?(.*))?( |'|\"|>)", "/slide-anything/owl-carousel/owl.carousel.min.js", "owl_goto.trigger"], "icon_url": "", "type": "plugin", "id": "plugin:fff874cc48b80940210228c975df395c", "is_default": 0, "created_at": 1702907355}, "dd0ea584-0c99-4c30-b46d-da35b94c9f0a": {"title": "Slider by <PERSON><PERSON><PERSON><PERSON>", "condition": "soliloquy-lite/soliloquy-lite.php", "exclusions": ["/jquery-?[0-9.](.*)(.min|.slim|.slim.min)?.js", "/jquery-migrate(.min)?.js", "soliloquy-min.js", "soliloquy_slider"], "icon_url": "", "type": "plugin", "id": "plugin:ffb5f8cc0ea17886dbd05f601ca3eec0", "is_default": 0, "created_at": 1704735068}, "ecb2fffc-d289-4f68-ae94-71131f17deee": {"title": "Smart Slider 3", "condition": "smart-slider-3/smart-slider-3.php", "exclusions": ["/jquery-?[0-9.](.*)(.min|.slim|.slim.min)?.js", "/jquery-migrate(.min)?.js", "/smart-slider-3/(.*).js", "_N2"], "icon_url": "", "type": "plugin", "id": "plugin:2f373822dceb191c31c8ad2183d51869", "is_default": 0, "created_at": 1704735119}, "284ede43-bf80-419e-8414-46c26cb746f0": {"title": "Smart Slider 3 Pro", "condition": "nextend-smart-slider3-pro/nextend-smart-slider3-pro.php", "exclusions": ["/jquery-?[0-9.](.*)(.min|.slim|.slim.min)?.js", "/jquery-migrate(.min)?.js", "/SmartSlider3/(.*).js", "_N2"], "icon_url": "", "type": "plugin", "id": "plugin:720ec9fbbd8a52ce525959a68a5310b3", "is_default": 0, "created_at": 1704735152}, "be70034f-def5-4771-9b3f-662eb218da2a": {"title": "Spectra - Show Slider Images", "condition": "ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php", "exclusions": ["\\/jquery(-migrate)?-?([0-9.]+)?(.min|.slim|.slim.min)?.js(\\?(.*))?( |'|\"|>|$)", "/ultimate-addons-for-gutenberg/assets/js/spectra-animations.min.js", "/ultimate-addons-for-gutenberg/assets/js/post.min.js", "/ultimate-addons-for-gutenberg/assets/js/aos.min.js", "/slick.min.js", "/imagesloaded.min.js", "UAGBPostCarousel"], "icon_url": "", "type": "plugin", "id": "plugin:af16af31f83a874b9d0a9570d9c15ff7", "is_default": 0, "created_at": 1706824485}, "c3334c29-4160-42df-a1ce-f8aeeaee8668": {"title": "Super Socializer", "condition": "super-socializer/super_socializer.php", "exclusions": ["theChamp", "/jquery-?[0-9.](.*)(.min|.slim|.slim.min)?.js", "/jquery-migrate(.min)?.js"], "icon_url": "", "type": "plugin", "id": "plugin:660defe26748470c3a47366cd4012579", "is_default": 0, "created_at": 1677853633}, "f18b4242-fe3f-4e2b-bfc9-ac2fb3939a90": {"title": "Superfly Menu", "condition": "superfly-menu/main.php", "exclusions": ["/jquery-?[0-9.](.*)(.min|.slim|.slim.min)?.js", "/jquery-migrate((.*?)(.min?)?).js", "/superfly-menu/includes/vendor/looks_awesome/icon_manager/js/md5.js", "/superfly-menu/includes/vendor/looks_awesome/icon_manager/js/util.js", "/superfly-menu/js/public.min.js", "window.SFM_is_mobile", "var SFM_skew_disabled", "var SFM_template"], "icon_url": "", "type": "plugin", "id": "plugin:ccb15175093bc6c437b78797f0698a7b", "is_default": 0, "created_at": 1684301673}, "1a05c00a-8562-45bc-80e8-987a4574b1c9": {"title": "Symplr Ads", "condition": "symplr-ads/symplr-plugin.php", "exclusions": ["/symplr-ads/", "cdns.symplr.de"], "icon_url": "", "type": "plugin", "id": "plugin:3b6d39e28a87e86c4659491e2368ff61", "is_default": 0, "created_at": 1692199959}, "858d2d7f-bdac-4d27-ba26-baa9ace96ba4": {"id": "plugin:7d93008296bb5c7c43d4cba185ed2632", "title": "<PERSON><PERSON> Responsive Tabs", "type": "plugin", "icon": "", "exclusions": ["/jquery-?[0-9.](.*)(.min|.slim|.slim.min)?.js", "/jquery-migrate(.min)?.js", "/tabby-responsive-tabs/js/tabby.js", "RESPONSIVEUI"], "is_default": 0, "condition": "tabby-responsive-tabs/tabby-responsive-tabs.php", "created_at": **********, "updated_at": "2023-02-15T04:32:16.000000Z", "icon_url": ""}, "e44a5c93-bb77-4624-a121-d846905137ea": {"id": "plugin:2f563bbb7e92363ec3fb2989a1c7dffe", "title": "The Plus Addons for Elementor", "type": "plugin", "icon": "", "exclusions": ["/jquery-?[0-9.](.*)(.min|.slim|.slim.min)?.js", "/wp-includes/js/jquery/jquery-migrate.min.js", "/theplus-addons/(.*)"], "is_default": 0, "condition": "the-plus-addons-for-elementor-page-builder/theplus_elementor_addon.php", "created_at": **********, "updated_at": "2023-02-15T04:32:16.000000Z", "icon_url": ""}, "2bbac961-b000-4802-ae97-d52472ec6750": {"title": "The Plus Addons for Elementor Premium", "condition": "theplus_elementor_addon/theplus_elementor_addon.php", "exclusions": ["\\/jquery(-migrate)?-?([0-9.]+)?(.min|.slim|.slim.min)?.js(\\?(.*))?( |'|\"|>)", "/wp-content/plugins/theplus_elementor_addon/", "/elementor/", "/elementor-pro/", "/wp-includes/js/imagesloaded.min.js", "ElementorProFrontendConfig", "elementorFrontendConfig"], "icon_url": "", "type": "plugin", "id": "plugin:6e127deaaeefbe57ff945b1f9e274518", "is_default": 0, "created_at": 1688143611}, "c3b5d7fd-a1e0-4aca-a0b6-405018a37266": {"title": "The Post Grid", "condition": "the-post-grid/", "exclusions": ["\\/jquery(-migrate)?-?([0-9.]+)?(.min|.slim|.slim.min)?.js(\\?(.*))?( |'|\"|>)", "the-post-grid"], "icon_url": "", "type": "plugin", "id": "plugin:93c3354976453458cf67f8ec0fdfce5a", "is_default": 0, "created_at": 1720011146}, "666b45e8-749c-4140-bd17-e1cd589e03ee": {"id": "plugin:b9c418b47c986935a1151ab9b42f8971", "title": "ThemeREX Addons", "type": "plugin", "icon": "", "exclusions": ["/jquery-?[0-9.](.*)(.min|.slim|.slim.min)?.js", "/wp-includes/js/jquery/jquery-migrate.min.js", "/trx_addons/js/__scripts-full.js", "/trx_addons/components/cpt/layouts/shortcodes/menu/superfish.min.js"], "is_default": 0, "condition": "trx_addons/trx_addons.php", "created_at": **********, "updated_at": "2023-02-15T04:32:16.000000Z", "icon_url": ""}, "a750cada-eb58-4df4-9966-21c8a69332ba": {"id": "plugin:75af9efe22c5cc776636266feb55adf1", "title": "Thrive <PERSON>", "type": "plugin", "icon": "", "exclusions": ["/jquery-?[0-9.](.*)(.min|.slim|.slim.min)?.js", "/jquery-migrate(.min)?.js"], "is_default": 0, "condition": "thrive-visual-editor/thrive-visual-editor.php", "created_at": **********, "updated_at": "2023-02-15T04:32:17.000000Z", "icon_url": ""}, "13714cde-680e-4ca4-8607-c35952d6a5f2": {"title": "Thrive Comments", "condition": "thrive-comments/thrive-comments.php", "exclusions": ["/jquery-?[0-9.](.*)(.min|.slim|.slim.min)?.js", "/jquery-migrate(.min)?.js", "/thrive-leads/", "window.TL_Const", "var ml=", "/thrive-comments/assets/js/", "ThriveComments", "/wp-includes/js/underscore.min.js", "/wp-includes/js/backbone.min.js"], "icon_url": "", "type": "plugin", "id": "plugin:6a72d06ca2f6a888b0d9d5ea93af2edc", "is_default": 0, "created_at": 1677852974}, "aa7ca898-499f-4f04-b419-3de199996969": {"id": "plugin:b84d82c02cade64ade00712b9c5652aa", "title": "<PERSON>hr<PERSON> Leads", "type": "plugin", "icon": "", "exclusions": ["/jquery-?[0-9.](.*)(.min|.slim|.slim.min)?.js", "/jquery-migrate(.min)?.js", "/thrive-leads/", "window.TL_Const", "var ml=", "/thrive-comments/assets/js/", "ThriveComments", "/wp-includes/js/underscore.min.js", "/wp-includes/js/backbone.min.js"], "is_default": 0, "condition": "thrive-leads/thrive-leads.php", "created_at": **********, "updated_at": "2023-02-15T04:32:16.000000Z", "icon_url": ""}, "9eee297a-8241-4ef2-af97-46074bd0898c": {"id": "plugin:5bb61b0559b0a3fd578315b553451327", "title": "Thrive Quiz Builder", "type": "plugin", "icon": "", "exclusions": ["/jquery-?[0-9.](.*)(.min|.slim|.slim.min)?.js", "/jquery-migrate(.min)?.js"], "is_default": 0, "condition": "thrive-quiz-builder/thrive-quiz-builder.php", "created_at": **********, "updated_at": "2023-02-15T04:32:16.000000Z", "icon_url": ""}, "c2f3ec2a-5b09-4845-aa95-84841783fbfc": {"id": "plugin:a7f3e5206abff19ca7cf142260181738", "title": "<PERSON><PERSON><PERSON>", "type": "plugin", "icon": "", "exclusions": ["/jquery-?[0-9.](.*)(.min|.slim|.slim.min)?.js", "/jquery-migrate(.min)?.js", "/thrive-ultimatum/", "var TVE_Ult_"], "is_default": 0, "condition": "thrive-ultimatum/thrive-ultimatum.php", "created_at": **********, "updated_at": "2023-02-15T04:32:17.000000Z", "icon_url": ""}, "d48b8d2f-e071-4a30-840a-306154a115a0": {"id": "plugin:f57be2014b6a489d053f8367fa6c0f9f", "title": "<PERSON><PERSON><PERSON>", "type": "plugin", "icon": "", "exclusions": ["document.tidioChatCode"], "is_default": 0, "condition": "tidio-live-chat/tidio-elements.php", "created_at": **********, "updated_at": "2023-02-15T04:32:17.000000Z", "icon_url": ""}, "cb6d33ed-7eb4-4ff9-9ad0-7fc54fbecf6f": {"id": "plugin:c9e0485ec256d4a6a8d92a98c18d76fc", "title": "Toolset Blocks", "type": "plugin", "icon": "", "exclusions": ["/jquery-?[0-9.](.*)(.min|.slim|.slim.min)?.js", "/jquery-migrate(.min)?.js", "toolsetCommonEs.fontToHead", "toolsetCommonEs.styleToHead", "/toolset-blocks/vendor/toolset/blocks/public/js/frontend.js", "/toolset-blocks/vendor/toolset/common-es/public/toolset-common-es-frontend.js", "/toolset-blocks/public/js/views-frontend.js", "/wp-includes/js/underscore.min.js"], "is_default": 0, "condition": "toolset-blocks/wp-views.php", "created_at": **********, "updated_at": "2023-02-15T04:32:16.000000Z", "icon_url": ""}, "4bac6350-0925-49fb-904a-372f22fd6baf": {"id": "plugin:71beda322b37f7fc7d456822493cb972", "title": "Top Bar Pro", "type": "plugin", "icon": "", "exclusions": ["/jquery-?[0-9.](.*)(.min|.slim|.slim.min)?.js", "/jquery-migrate(.min)?.js", "/plugins/topbar-pro/js/tpbr_front.min.js", "/plugins/topbar-pro/js/jquery.cookie.js"], "is_default": 0, "condition": "topbar-pro/topbar_pro.php", "created_at": **********, "updated_at": "2023-02-15T04:32:16.000000Z", "icon_url": ""}, "033486e7-0ddc-4915-a848-31504d00448e": {"title": "Twenty20 Image Before-After", "condition": "twenty20/ttwenty.php", "exclusions": ["/twenty20/assets/js/(.*).js", "/jquery-?[0-9.](.*)(.min|.slim|.slim.min)?.js", "/jquery-migrate(.min)?.js", "twentytwenty-container"], "icon_url": "", "type": "plugin", "id": "plugin:23441bba9d3602bc932d697c7cb8aa1f", "is_default": 0, "created_at": 1677858089}, "bef2147e-2d0b-431d-ac29-5e8430c0d809": {"title": "Typing Effect", "condition": "animated-typing-effect/typingeffect.php", "exclusions": ["\\/jquery(-migrate)?-?([0-9.]+)?(.min|.slim|.slim.min)?.js(\\?(.*))?( |'|\"|>|$)", "/plugins/animated-typing-effect/assets/js/typed.js", "/plugins/animated-typing-effect/assets/js/typed.fe.js"], "icon_url": "", "type": "plugin", "id": "plugin:e4e1a3e63d09a28dcb20577efbcb5a48", "is_default": 0, "created_at": 1711400446}, "7675a34d-006e-4672-99d5-a81e1b8e47f9": {"id": "plugin:3d59cc34167a7f8123e66b627148e0b7", "title": "UberMenu", "type": "plugin", "icon": "", "exclusions": ["/jquery-?[0-9.](.*)(.min|.slim|.slim.min)?.js", "/jquery-migrate(.min)?.js", "/ubermenu/assets/js/ubermenu.min.js"], "is_default": 0, "condition": "ubermenu/ubermenu.php", "created_at": **********, "updated_at": "2023-02-15T04:32:17.000000Z", "icon_url": ""}, "8233178a-e7b3-43ce-b193-bd0d9c960933": {"id": "plugin:86424c46157c1c7e2e1571055813beee", "title": "Ultimate Addons for Elementor", "type": "plugin", "icon": "", "exclusions": ["/jquery-?[0-9.](.*)(.min|.slim|.slim.min)?.js", "/jquery-migrate(.min)?.js", "/ultimate-elementor/assets/lib/slick/slick.min.js", "/ultimate-elementor/assets/min-js/uael-frontend.min.js", "/ultimate-elementor/assets/lib/isotope/isotope.min.js", "/ultimate-elementor/assets/lib/jquery-element-resize/jquery_resize.min.js", "/ultimate-elementor/assets/lib/fancybox/jquery_fancybox.min.js", "/ultimate-elementor/assets/lib/justifiedgallery/justifiedgallery.min.js", "/elementor-pro/assets/js/frontend.min.js", "/wp-includes/js/imagesloaded.min.js", "/js_composer/assets/js/dist/js_composer_front.min.js", "/elementor/assets/lib/swiper/swiper.min.js", "/nasa-core/assets/js/min/jquery.slick.min.js", "/elementor/", "/elementor-pro/", "ElementorProFrontendConfig", "elementorFrontendConfig"], "is_default": 0, "condition": "ultimate-elementor/ultimate-elementor.php", "created_at": **********, "updated_at": "2023-02-15T04:32:16.000000Z", "icon_url": ""}, "8bddf484-6c78-4147-a4e6-d3039904e5f6": {"title": "Ultimate Addons for Elementor - Mobile Menu", "condition": "ultimate-elementor/ultimate-elementor.php", "exclusions": ["/jquery-?([0-9.]+)?(.min|.slim|.slim.min)?.js(\\?(.*))?$", "/jquery-migrate(.*)(.min|.slim|.slim.min)?.js(\\?(.*))?$", "/ultimate-elementor/assets/js/uael-nav-menu.js", "/ultimate-elementor/assets/min-js/uael-nav-menu.min.js"], "icon_url": "", "type": "plugin", "id": "plugin:70cfade3a7adbb54196f6acccc5a176f", "is_default": 0, "created_at": 1694771327}, "028504f7-b1cd-4318-8a6d-ce186197e89d": {"id": "plugin:0f0a91f0c454021a5ff9fc25c3ed419f", "title": "Ultimate Addons for WPBakery Page Builder", "type": "plugin", "icon": "", "exclusions": ["/jquery-?[0-9.](.*)(.min|.slim|.slim.min)?.js", "/jquery-migrate(.min)?.js", "/Ultimate_VC_Addons/assets/"], "is_default": 0, "condition": "Ultimate_VC_Addons/Ultimate_VC_Addons.php", "created_at": **********, "updated_at": "2023-02-15T04:32:17.000000Z", "icon_url": ""}, "5754af3d-a6af-49eb-b731-bdd82f26dc4e": {"id": "plugin:34db8636812bad84c8aea037c2ddc8c2", "title": "Ultimate Responsive Image Slider", "type": "plugin", "icon": "", "exclusions": ["/jquery-?[0-9.](.*)(.min|.slim|.slim.min)?.js", "/jquery-migrate(.min)?.js", "sliderPro"], "is_default": 0, "condition": "ultimate-responsive-image-slider/ultimate-responsive-image-slider.php", "created_at": **********, "updated_at": "2023-02-15T04:32:16.000000Z", "icon_url": ""}, "78ae882d-ae31-4179-b677-8893814938c7": {"title": "Unlimited Elements for Elementor Premium - Slider", "condition": "unlimited-elements-for-elementor-premium/unlimited-elements-pro.php", "exclusions": ["/jquery-?[0-9.](.*)(.min|.slim|.slim.min)?.js", "/jquery-migrate(.min)?.js", "/elementor/", "/elementor-pro/", "ElementorProFrontendConfig", "elementorFrontendConfig", "/unlimited-elements-for-elementor-premium/", "uc_"], "icon_url": "", "type": "plugin", "id": "plugin:17260bc347b8c29bee0010e9ec164184", "is_default": 0, "created_at": 1683270377}, "051cbfbb-7ad2-4f06-a493-3cf423a80904": {"id": "plugin:6717ef5673a956bc08ca4a5117065d53", "title": "Variation Swatches for WooCommerce", "type": "plugin", "icon": "", "exclusions": ["/jquery-?[0-9.](.*)(.min|.slim|.slim.min)?.js", "/jquery-migrate(.min)?.js", "/jquery/ui/", "/woo-variation-swatches/", "/woo-variation-swatches-pro/", "underscore.min.js"], "is_default": 0, "condition": "woo-variation-swatches/woo-variation-swatches.php", "created_at": **********, "updated_at": "2023-02-15T04:32:16.000000Z", "icon_url": ""}, "6879caba-7224-4eca-bcb2-370785b495ea": {"id": "plugin:57597b7683e01892932083413f085134", "title": "Web Accessibility By accessiBe", "type": "plugin", "icon": "", "exclusions": ["/jquery-?[0-9.](.*)(.min|.slim|.slim.min)?.js", "/jquery-migrate(.min)?.js", "/accessibe/", "acsbJS"], "is_default": 0, "condition": "accessibe/accessiebe.php", "created_at": **********, "updated_at": "2023-02-15T04:32:17.000000Z", "icon_url": ""}, "bd6732fe-4c2b-40a1-9035-8464057e2da5": {"title": "WooCommerce - Cart Fragments", "condition": "woocommerce/woocommerce.php", "exclusions": ["/wp-includes/js/jquery/jquery.min.js", "/woocommerce/assets/js/frontend/cart-fragments.min.js", "/woocommerce/assets/js/js-cookie/js.cookie.min.js"], "icon_url": "", "type": "plugin", "id": "plugin:201de694c6fc28c8d580a3b2ca484218", "is_default": 0, "created_at": 1680937567}, "d044900d-07e1-4533-9516-33106efcb259": {"title": "WooCommerce - Product description", "condition": "woocommerce/woocommerce.php", "exclusions": ["/plugins/woocommerce/assets/js/frontend/single-product.min.js", "/jquery-?[0-9.](.*)(.min|.slim|.slim.min)?.js", "/jquery-migrate(.min)?.js"], "icon_url": "", "type": "plugin", "id": "plugin:a82644b4c9417ea3a240939a73344700", "is_default": 0, "created_at": 1679309756}, "bff953b1-2213-4666-8112-76a84a3cc207": {"title": "WooCommerce - Product Gallery", "condition": "woocommerce/woocommerce.php", "exclusions": ["/jquery-?[0-9.](.*)(.min|.slim|.slim.min)?.js", "/jquery-migrate(.min)?.js", "/woocommerce/?(.*)/assets/js/zoom/jquery.zoom(.min)?.js", "/woocommerce/?(.*)/assets/js/photoswipe/", "/woocommerce/?(.*)/assets/js/flexslider/jquery.flexslider(.min)?.js", "/woocommerce/?(.*)/assets/js/frontend/single-product(.min)?.js", "wc_single_product_params"], "icon_url": "", "type": "plugin", "id": "plugin:7665868ff97c265628f376523a4f9ecc", "is_default": 0, "created_at": 1686579689}, "016e6ddf-c6e7-49ec-bd3f-2585d9e45895": {"title": "WooCommerce - Select2 library", "condition": "woocommerce/woocommerce.php", "exclusions": ["/jquery-?[0-9.](.*)(.min|.slim|.slim.min)?.js", "/jquery-migrate(.min)?.js", "/woocommerce/assets/js/select2/select2(.*).js"], "icon_url": "", "type": "plugin", "id": "plugin:4bca670bd5d55dd24b17fb0193b0891e", "is_default": 0, "created_at": 1681459540}, "b9b2c6d7-944f-4ae3-ae04-c9e2204b9dab": {"id": "plugin:9165c768e978d6ad3f696db8c78ccbb2", "title": "WooCommerce Attribute Swatches", "type": "plugin", "icon": "", "exclusions": ["/jquery-?[0-9.](.*)(.min|.slim|.slim.min)?.js", "/jquery-migrate(.min)?.js", "/iconic-woo-attribute-swatches-premium/assets/frontend/js/main.min.js", "/iconic-woo-attribute-swatches-premium/assets/vendor/flickity/flickity.pkgd.min.js", "iconic_was_vars"], "is_default": 0, "condition": "iconic-woo-attribute-swatches-premium/iconic-woo-attribute-swatches.php", "created_at": **********, "updated_at": "2023-02-15T04:32:17.000000Z", "icon_url": ""}, "4506c5e9-7349-44c9-9967-34370c83facb": {"title": "WooCommerce Bookings", "condition": "woocommerce-bookings/woocommerce-bookings.php", "exclusions": ["/wp-includes/js/jquery/jquery.min.js", "/wp-includes/js/jquery/jquery-migrate.min.js", "/woocommerce-bookings/dist/frontend.js", "/wp-includes/js/dist/date.min.js", "/wp-includes/js/dist/vendor/moment.min.js", "/wp-includes/js/jquery/ui/datepicker.min.js", "/wp-includes/js/underscore.min.js", "/woocommerce/assets/js/jquery-blockui/jquery.blockUI.min.js", "/wp-includes/js/dist/hooks.min.js"], "icon_url": "", "type": "plugin", "id": "plugin:14dec6d289b9977fa3a74116feecebcc", "is_default": 0, "created_at": 1693998405}, "fec9cd04-c358-45da-a1a8-1668b964016b": {"id": "plugin:456f3b849ba3b6647246aca9d7cdaed5", "title": "WooCommerce Product Reviews Pro", "type": "plugin", "icon": "", "exclusions": ["/jquery-?[0-9.](.*)(.min|.slim|.slim.min)?.js", "/jquery-migrate(.min)?.js", "wc_product_reviews_pro", "/woocommerce-product-reviews-pro/assets/js/frontend/wc-product-reviews-pro-frontend.min.js", "/woocommerce/assets/js/jquery-tiptip/jquery.tipTip.min.js"], "is_default": 0, "condition": "woocommerce-product-reviews-pro/woocommerce-product-reviews-pro.php", "created_at": **********, "updated_at": "2023-02-15T04:32:16.000000Z", "icon_url": ""}, "551a9399-627f-4978-9bae-5cc8e0aefc82": {"id": "plugin:c8577e74eef3b082fb6403760d53f68c", "title": "WooCommerce TM Extra Product Options", "type": "plugin", "icon": "", "exclusions": ["/woocommerce-tm-extra-product-options/assets/js/epo.min.js", "/jquery-?[0-9.](.*)(.min|.slim|.slim.min)?.js", "/jquery-migrate(.min)?.js", "/wp-includes/js/jquery/ui/core.min.js", "/wp-includes/js/jquery/ui/mouse.min.js", "/wp-includes/js/jquery/ui/slider.min.js", "/wp-includes/js/underscore.min.js", "/wp-includes/js/wp-util.min.js", "/wp-includes/js/dist/hooks.js", "/wp-includes/js/dist/i18n.js"], "is_default": 0, "condition": "woocommerce-tm-extra-product-options/tm-woo-extra-product-options.php", "created_at": **********, "updated_at": "2023-02-15T04:32:16.000000Z", "icon_url": ""}, "3cac4650-6a52-448e-8e48-e99a772a59a2": {"id": "plugin:c6e6cab8c80fa3fe57d609f72d2d5c56", "title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "plugin", "icon": "", "exclusions": ["/woolementor/assets/third-party/slick/slick.min.js", "/woolentor-addons", "woolentor_addons"], "is_default": 0, "condition": "woolentor-addons/woolentor_addons_elementor.php", "created_at": **********, "updated_at": "2023-02-15T04:32:16.000000Z", "icon_url": ""}, "21af073d-d2ec-4d46-bbda-2c69f87f3f98": {"title": "Woolentor - Fix product gallery", "condition": "woolentor-addons/woolentor_addons_elementor.php", "exclusions": ["\\/jquery(-migrate)?-?([0-9.]+)?(.min|.slim|.slim.min)?.js(\\?(.*))?( |'|\"|>)", ".woolentor-learg-img", "/woolentor-addons/assets/js/slick.min.js", "/woolentor-addons-pro/assets/lib/js/tippy.min.js"], "icon_url": "", "type": "plugin", "id": "plugin:c5315e06dfc353cd57f85a60abe4e320", "is_default": 0, "created_at": 1700061004}, "67aeb4cb-1a00-4d10-a00c-34888b4c0dba": {"title": "WooThumbs for WooCommerce", "condition": "woothumbs-premium/woothumbs-premium.php", "exclusions": ["/wp-includes/js/dist/hooks.min.js", "/wp-includes/js/underscore.min.js", "/wp-includes/js/wp-embed.min.js", "/wp-includes/js/wp-util.min.js", "/woothumbs-premium/(.*)"], "icon_url": "", "type": "plugin", "id": "plugin:b97b1d3f627769e1dd8305aa25af993c", "is_default": 0, "created_at": 1679065404}, "c7a14763-88d0-4344-a6af-e0a8dc5fa8d5": {"id": "plugin:31b9e812a025e5750a6ef0980ee7d2db", "title": "WordPress Mega Menu – QuadMenu", "type": "plugin", "icon": "", "exclusions": ["/jquery-?[0-9.](.*)(.min|.slim|.slim.min)?.js", "/jquery-migrate(.min)?.js", "/quadmenu", "#private-menu", "#public-menu"], "is_default": 0, "condition": "quadmenu/quadmenu.php", "created_at": **********, "updated_at": "2023-02-15T04:32:16.000000Z", "icon_url": ""}, "f699fdbd-84a7-4f24-b729-3e4a4f83a4dd": {"id": "plugin:601f8fc7d10cad1c2ec2949c0d9b1651", "title": "WP Armour", "type": "plugin", "icon": "", "exclusions": ["/jquery-?[0-9.](.*)(.min|.slim|.slim.min)?.js", "/jquery-migrate(.min)?.js", "/honeypot/includes/js/wpa.js", "/wp-armour-extended/includes/js/wpae.js", "wpa_hidden_field", "wpa_add_test"], "is_default": 0, "condition": "wp-armour-extended/wp-armour-extended.php", "created_at": **********, "updated_at": "2023-02-15T04:32:17.000000Z", "icon_url": ""}, "6a28aae5-ef91-43fc-8204-92e3a25642b4": {"id": "plugin:5728f3b9856dfe37a36ab15b0a637198", "title": "WP Go Maps", "type": "plugin", "icon": "", "exclusions": ["/jquery-?[0-9.](.*)(.min|.slim|.slim.min)?.js", "/jquery-migrate(.min)?.js", "maps.googleapis.com"], "is_default": 0, "condition": "wp-google-maps/wpGoogleMaps.php", "created_at": **********, "updated_at": "2023-02-15T04:32:16.000000Z", "icon_url": ""}, "c9b991d1-a653-404b-be85-e276b1814e7d": {"title": "WP Google Map Pro", "condition": "wp-google-map-gold/wp-google-map-gold.php", "exclusions": ["/wp-includes/js/jquery/jquery.min.js", "/wp-includes/js/jquery/jquery-migrate.min.js", "maps.google.com/maps/api/js", "/wp-includes/js/masonry.min.js", "/wp-google-map-gold/(.*)"], "icon_url": "", "type": "plugin", "id": "plugin:dc12a9b7b9c4c7ce3c532b6b377739f2", "is_default": 0, "created_at": 1677858391}, "1f8f9fbf-fbf0-4e3a-b77c-af0fa47e950d": {"id": "plugin:87f572f5f0ec143a8fceba77d0616197", "title": "WP Google Maps Pro", "type": "plugin", "icon": "", "exclusions": ["/jquery-?[0-9.](.*)(.min|.slim|.slim.min)?.js", "/jquery-migrate(.min)?.js", "/wp-google-maps(.*)", "maps.googleapis.com", "mgl_", "wpgmza"], "is_default": 0, "condition": "wp-google-maps-pro/wp-google-maps-pro.php", "created_at": **********, "updated_at": "2023-02-15T04:32:17.000000Z", "icon_url": ""}, "41e4b98c-e63f-4800-a478-02592562322b": {"id": "plugin:3735ca768ede98b25795f4cb057ff4ed", "title": "WP iCal Availability", "type": "plugin", "icon": "", "exclusions": ["/wp-ical-availability/js/custom-select.js", "/jquery-?[0-9.](.*)(.min|.slim|.slim.min)?.js", "/jquery-migrate(.min)?.js", "/wp-ical-availability/"], "is_default": 0, "condition": "wp-ical-availability/wp-ical-availability.php", "created_at": **********, "updated_at": "2023-02-15T04:32:17.000000Z", "icon_url": ""}, "2b06c5b8-dc32-4bb0-8504-3a9f1c3a1ec0": {"title": "WP MapIt", "condition": "wp-mapit/wp_mapit.php", "exclusions": ["/jquery-?[0-9.](.*)(.min|.slim|.slim.min)?.js", "/jquery-migrate(.min)?.js", "/wp-mapit/wp_mapit/js/leaflet.js", "/wp-mapit/wp_mapit/js/wp_mapit_multipin.js"], "icon_url": "", "type": "plugin", "id": "plugin:ba8d1c7f294a3f5b593556eb3b0bc7d9", "is_default": 0, "created_at": 1679331261}, "2039eafd-1c11-4e21-a61b-30857f291ae3": {"id": "plugin:927b8bf7806f2d287559b86a0b455a59", "title": "WP Responsive Menu", "type": "plugin", "icon": "", "exclusions": ["/jquery-?[0-9.](.*)(.min|.slim|.slim.min)?.js", "/jquery-migrate(.min)?.js", "/wp-responsive-menu/(.*)"], "is_default": 0, "condition": "wp-responsive-menu/wp-responsive-menu.php", "created_at": **********, "updated_at": "2023-02-15T04:32:16.000000Z", "icon_url": ""}, "e06be942-cccd-4b2d-a268-06dc78f0b820": {"title": "WP Search with Algolia", "condition": "wp-search-with-algolia/algolia.php", "exclusions": ["/wp-search-with-algolia/js/algoliasearch/dist/algoliasearch-lite.umd.js", "/wp-search-with-algolia/js/autocomplete-noconflict.js", "/wp-search-with-algolia/js/autocomplete.js/dist/autocomplete.min.js", "var algolia"], "icon_url": "", "type": "plugin", "id": "plugin:43267e659d599fbb6b42c719b49bb7a7", "is_default": 0, "created_at": 1677857180}, "4fc2a7ae-b9e6-410e-93cf-e6d1962add6a": {"title": "WP Smart Preloader", "condition": "wp-smart-preloader/wp-preloader.php", "exclusions": ["\\/jquery(-migrate)?-?([0-9.]+)?(.min|.slim|.slim.min)?.js(\\?(.*))?( |'|\"|>)", "/wp-smart-preloader/assets/js/wsp-main-script(.min)?.js"], "icon_url": "", "type": "plugin", "id": "plugin:4fb90fc73fc2b5d1e37ea2dadfd3cef3", "is_default": 0, "created_at": 1711125833}, "7ecf40ce-2bcd-412c-bb01-9e71fecf6be8": {"title": "WP Store Locator", "condition": "wp-store-locator/wp-store-locator.php", "exclusions": ["/jquery-?[0-9.](.*)(.min|.slim|.slim.min)?.js", "/jquery-migrate(.min)?.js", "/wp-store-locator/", "/wp-includes/js/underscore.min.js", "maps.google.com"], "icon_url": "", "type": "plugin", "id": "plugin:d37bb5054a24471ca1675d9ab49d01b0", "is_default": 0, "created_at": 1704735183}, "7a1d19a2-3a48-40ab-8051-f642fc63ce2d": {"title": "WP Ultimate Post Grid", "condition": "wp-ultimate-post-grid/wp-ultimate-post-grid.php", "exclusions": ["\\/jquery(-migrate)?-?([0-9.]+)?(.min|.slim|.slim.min)?.js(\\?(.*))?$", "/wp-ultimate-post-grid/dist/public.js", "/wp-ultimate-post-grid-premium/dist/public-premium.js", "wpupg_grid_args"], "icon_url": "", "type": "plugin", "id": "plugin:e24341fef49bd64b89682d583218c108", "is_default": 0, "created_at": 1686597940}, "76c86163-ddf3-4113-b620-de9d5058f505": {"title": "WPBakery Page Builder", "condition": "js_composer/js_composer.php", "exclusions": ["\\/jquery(-migrate)?-?([0-9.]+)?(.min|.slim|.slim.min)?.js(\\?(.*))?( |'|\"|>|$)", "/js_composer/assets/js/dist/js_composer_front.min.js"], "icon_url": "", "type": "plugin", "id": "plugin:517d7d24da9a7072ed389d0fb30374a0", "is_default": 0, "created_at": 1704404852}, "0b8ff2c0-c3cd-4ec1-b7f5-c7751de6101b": {"title": "WPBakery Page Builder - Carousel", "condition": "js_composer/js_composer.php", "exclusions": ["\\/jquery(-migrate)?-?([0-9.]+)?(.min|.slim|.slim.min)?.js(\\?(.*))?( |'|\"|>|$)", "/js_composer/assets/lib/vc_carousel/js/vc_carousel.min.js"], "icon_url": "", "type": "plugin", "id": "plugin:76336ed3f04df091e669f89d908ef2ed", "is_default": 0, "created_at": 1704405212}, "4f5e5b98-c326-4b9f-9ada-3b257862132c": {"id": "plugin:1ec7138c950c355e7af60d49c81139fc", "title": "wpDataTables", "type": "plugin", "icon": "", "exclusions": ["/jquery-?[0-9.](.*)(.min|.slim|.slim.min)?.js", "/jquery-migrate(.min)?.js", "/wpdatatables/", "highcharts"], "is_default": 0, "condition": "wpdatatables/wpdatatables.php", "created_at": **********, "updated_at": "2023-02-15T04:32:16.000000Z", "icon_url": ""}, "18be1b8c-0bc4-4a01-abc6-a127aff380c6": {"id": "plugin:c62ca58ea081c1271de8dadfa7daac69", "title": "WPForms", "type": "plugin", "icon": "", "exclusions": ["/wpforms-offline-forms/assets/js/wpforms-offline-forms.min.js", "wpforms-offline-forms-js-extra", "wpformsRecaptchaLoad"], "is_default": 0, "condition": "wpforms/wpforms.php", "created_at": **********, "updated_at": "2023-02-15T04:32:16.000000Z", "icon_url": ""}, "138b2894-25d2-47ce-b33d-cbf1256d8f45": {"title": "WPForms - Loader GIF", "condition": "wpforms/wpforms.php", "exclusions": ["\\/jquery(-migrate)?-?([0-9.]+)?(.min|.slim|.slim.min)?.js(\\?(.*))?( |'|\"|>|$)", "/wpforms-conversational-forms/assets/js/conversational-forms.es5.min.js"], "icon_url": "", "type": "plugin", "id": "plugin:03dc6ae2848dd60e1d4f4f86015c22f0", "is_default": 0, "created_at": 1715863299}, "8a3cacb6-81bd-456a-a1cc-a4025f8e5234": {"id": "plugin:0992ac952c0a05bb35e18b1d5744d346", "title": "WPForms Lite", "type": "plugin", "icon": "", "exclusions": ["wpformsRecaptchaLoad", "/wpforms-offline-forms/assets/js/wpforms-offline-forms.min.js", "wpforms-offline-forms-js-extra"], "is_default": 0, "condition": "wpforms-lite/wpforms.php", "created_at": **********, "updated_at": "2023-02-15T04:32:16.000000Z", "icon_url": ""}, "98bbd410-5b01-4244-a8eb-715765180328": {"title": "XL WooCommerce Sales Triggers", "condition": "xl-woocommerce-sales-triggers/xl-woocommerce-sales-triggers.php", "exclusions": ["/xl-woocommerce-sales-triggers/assets/js/wcst_combined.min.js", "/jquery-?[0-9.](.*)(.min|.slim|.slim.min)?.js", "/jquery-migrate(.min)?.js"], "icon_url": "", "type": "plugin", "id": "plugin:ba9e526ddb0157e69757530c6b18b714", "is_default": 0, "created_at": 1677856813}, "bf9f9620-dd0e-4e6f-9a45-4eb78a148f42": {"id": "plugin:58663fc781232169e865f6fe7cf1afaa", "title": "YITH WooCommerce Ajax Product Filter", "type": "plugin", "icon": "", "exclusions": ["/jquery-?[0-9.](.*)(.min|.slim|.slim.min)?.js", "/jquery-migrate(.min)?.js", "/yith-woocommerce-ajax-navigation/assets/js/yith-wcan-shortcodes.min.js"], "is_default": 0, "condition": "yith-woocommerce-ajax-navigation/init.php", "created_at": **********, "updated_at": "2023-02-15T04:32:17.000000Z", "icon_url": ""}, "b053279d-e07c-438c-bb3e-3a1f4f5d7c5e": {"id": "plugin:68b637fd247e40c8e135e4771d739b07", "title": "YITH WooCommerce AJAX Product Filter Premium", "type": "plugin", "icon": "", "exclusions": ["/jquery-?[0-9.](.*)(.min|.slim|.slim.min)?.js", "/jquery-migrate(.min)?.js", "/yith-woocommerce-ajax-product-filter-premium/assets/js/yith-wcan-shortcodes.min.js"], "is_default": 0, "condition": "yith-woocommerce-ajax-product-filter-premium/init.php", "created_at": **********, "updated_at": "2023-02-15T04:32:16.000000Z", "icon_url": ""}, "62056165-8bd9-4ff0-b21f-e4ed0ae45fae": {"title": "YITH WooCommerce Points and Rewards", "condition": "yith-woocommerce-points-and-rewards-premium/init.php", "exclusions": ["\\/jquery(-migrate)?-?([0-9.]+)?(.min|.slim|.slim.min)?.js(\\?(.*))?( |'|\"|>|$)", "/yith-woocommerce-points-and-rewards-premium/assets/js/frontend.min.js", "/woo-variation-swatches-pro/assets/js/add-to-cart-variation.min.js"], "icon_url": "", "type": "plugin", "id": "plugin:4acc87d4eb72c86cdea76d180b61a098", "is_default": 0, "created_at": 1709917756}, "d94dbbf3-bcab-4e47-9fbb-6b3a7cf92787": {"title": "Yotpo Social Reviews for Woocommerce", "condition": "yotpo-social-reviews-for-woocommerce/wc_yotpo.php", "exclusions": ["/yotpo-social-reviews-for-woocommerce/assets/js/headerScript.js"], "icon_url": "", "type": "plugin", "id": "plugin:45ab742b3fccbd04d7bc973c8582be87", "is_default": 0, "created_at": 1680686421}, "51dccf53-5cc7-4283-9ab1-01d34c6cce22": {"title": "Zoho SalesIQ", "condition": "zoho-salesiq/index.php", "exclusions": ["zoho.salesiq"], "icon_url": "", "type": "plugin", "id": "plugin:b96c3865575068aac82c973eb3e3c52a", "is_default": 0, "created_at": 1713536671}}, "themes": {"9aeea459-91d3-44b6-8a26-b883dca8b402": {"title": "Agensy - Load page without User Interaction", "condition": "agensy", "exclusions": ["\\/jquery(-migrate)?-?([0-9.]+)?(.min|.slim|.slim.min)?.js(\\?(.*))?( |'|\"|>|$)", "/themes/agensy/js/scripts.js", "/themes/agensy/js/scripts-single.js", "/themes/agensy/js/wow.min.js", "/themes/agensy/js/TweenMax.min.js", "/themes/agensy/js/swiper.min.js", "/plugins/visualcomposer/assets/lib/bower/isotope/dist/isotope.pkgd.min.js", "/wp-includes/js/imagesloaded.min.js"], "icon_url": "", "type": "theme", "id": "theme:7ab7dfeb0db9c0c74c020be318c2e6d9", "is_default": 0, "created_at": 1707317936}, "9c623554-5834-4669-9e96-1b894c1939b2": {"id": "theme:0193ea55fce2ada93b262f2824008c0f", "title": "Andaman", "type": "theme", "icon": "", "exclusions": ["/jquery-?[0-9.](.*)(.min|.slim|.slim.min)?.js", "/jquery-migrate(.min)?.js", "/andaman/assets/js/", "/wp-andaman-plugins/shortcodes/vc_extend/"], "is_default": 0, "condition": "andaman", "created_at": **********, "updated_at": "2023-02-15T04:32:17.000000Z", "icon_url": ""}, "7374c5a5-69f8-460f-b44f-dee884a824cd": {"id": "theme:24cbda63f1b898ade5562ab4ec6d97a5", "title": "Artale", "type": "theme", "icon": "", "exclusions": ["/artale-elementor/assets/js/modulobox.js", "/artale-elementor/assets/js/artale-elementor.js", "/artale/js/jquery-stellar.js", "/artale/js/core/artale-plugins.js", "/artale/js/core/artale-custom.js", "var loader"], "is_default": 0, "condition": "artale", "created_at": **********, "updated_at": "2023-02-15T04:32:16.000000Z", "icon_url": ""}, "23b01203-2a70-4394-9326-d59824def2d7": {"title": "Ashe Pro Premium", "condition": "ashe-pro-premium", "exclusions": ["\\/jquery(-migrate)?-?([0-9.]+)?(.min|.slim|.slim.min)?.js(\\?(.*))?( |'|\"|>|$)", "/ashe-pro-premium/"], "icon_url": "", "type": "theme", "id": "theme:4791da3cbbd1ed86253a087f0287aeb4", "is_default": 0, "created_at": 1704912793}, "03a9cc62-c167-447d-beb2-65c76c96b056": {"title": "Astra", "condition": "astra", "exclusions": ["/astra/assets/js/minified/frontend.min.js", "replace\\(/woocommerce-no-js/,"], "icon_url": "", "type": "theme", "id": "theme:3cce5f3eaf76e098ba8e28f7bbba3f92", "is_default": 0, "created_at": 1712608792}, "2a2b54cb-8e1d-49d2-bfca-93eee231e470": {"id": "theme:72a8d63e59c10bdf512a62b862d143a7", "title": "Astra - Carousel", "type": "theme", "icon": "", "exclusions": ["var astra", "/astra/assets/js/minified/style.min.js"], "is_default": 0, "condition": "astra", "created_at": **********, "updated_at": "2023-02-15T04:32:16.000000Z", "icon_url": ""}, "7783361f-66fc-4d95-a054-4e9545bb5b48": {"title": "<PERSON><PERSON><PERSON>", "condition": "g5plus-auteur", "exclusions": ["/jquery-?[0-9.](.*)(.min|.slim|.slim.min)?.js", "/jquery-migrate(.min)?.js", "/g5plus-auteur/assets/js/core.min.js", "/g5plus-auteur/assets/vendors/", "/auteur-framework/libs/smart-framework/assets/vendors/perfect-scrollbar/js/perfect-scrollbar.jquery.min.js", "/wp-includes/js/imagesloaded.min.js"], "icon_url": "", "type": "theme", "id": "theme:6207fe478e269e7547bda70a46607a49", "is_default": 0, "created_at": 1679737107}, "4c618038-8fc7-4d48-8d41-a32da14e5c1e": {"title": "AutoTrader", "condition": "autotrader", "exclusions": ["/jquery-?[0-9.](.*)(.min|.slim|.slim.min)?.js", "/jquery-migrate(.min)?.js", "/autotrader/(.*).js"], "icon_url": "", "type": "theme", "id": "theme:046dfeee2b77390c53e0e7f93b6a3792", "is_default": 0, "created_at": 1679736741}, "18f04f23-35a0-4c45-8cb6-a91d57ca1790": {"id": "theme:835da12f43373029659f766920e81b47", "title": "Avada - Animations & mobile-specific actions", "type": "theme", "icon": "", "exclusions": ["/jquery-?[0-9.](.*)(.min|.slim|.slim.min)?.js", "/jquery-migrate(.min)?.js", "/uploads/fusion-scripts/(.*).js", "window.off_canvas_", "/plugins/fusion-builder/", "/plugins/fusion-core/", "/Avada/includes/"], "is_default": 0, "condition": "Avada", "created_at": **********, "updated_at": "2023-02-15T04:32:16.000000Z", "icon_url": ""}, "90135867-1b95-498b-80d6-f5dbf2f6b318": {"title": "Avada - FAQ shortcode", "condition": "Avada", "exclusions": ["/jquery-?[0-9.](.*)(.min|.slim|.slim.min)?.js", "/jquery-migrate(.min)?.js", "/plugins/fusion-core/js/min/avada-faqs.js"], "icon_url": "", "type": "theme", "id": "theme:b084708c80d8582546e5430219aa4670", "is_default": 0, "created_at": 1678277160}, "d23b5bb1-1d7f-4109-bf69-b20a2be2d337": {"id": "theme:5e7a2248e1a53d9bb27b187deb541248", "title": "Avada - Fusion carousel", "type": "theme", "icon": "", "exclusions": ["/jquery-?[0-9.](.*)(.min|.slim|.slim.min)?.js", "/jquery-migrate(.min)?.js", "/includes/lib/assets/min/js/library/jquery.carouFredSel.js", "/includes/lib/assets/min/js/general/fusion-carousel.js", "fusionCarouselVars"], "is_default": 0, "condition": "Avada", "created_at": **********, "updated_at": "2023-02-15T04:32:17.000000Z", "icon_url": ""}, "98dfa8c1-e72a-4cef-a0b2-8f0c322490fc": {"id": "theme:f16fb109027f4994a7649a8b1663e6f7", "title": "Avada - Fusion form", "type": "theme", "icon": "", "exclusions": ["/jquery-?[0-9.](.*)(.min|.slim|.slim.min)?.js", "/jquery-migrate(.min)?.js", "/fusion-builder/assets/js/min/general/fusion-form.js", "/fusion-builder/assets/js/min/general/fusion-form-logics.js", "/includes/lib/assets/min/js/library/cssua.js", "/includes/lib/assets/min/js/general/fusion.js", "/includes/lib/assets/min/js/library/modernizr.js"], "is_default": 0, "condition": "Avada", "created_at": **********, "updated_at": "2023-02-15T04:32:16.000000Z", "icon_url": ""}, "026801fa-af67-47ba-b966-347693f0585f": {"id": "theme:33f50696d353d8bd4eb59ff6e8f44c97", "title": "Avada - Fusion grid gallery", "type": "theme", "icon": "", "exclusions": ["/jquery-?[0-9.](.*)(.min|.slim|.slim.min)?.js", "/jquery-migrate(.min)?.js", "/fusion-builder/assets/js/min/general/fusion-gallery.js", "/includes/lib/assets/min/js/library/imagesLoaded.js", "/includes/lib/assets/min/js/library/isotope.js", "/includes/lib/assets/min/js/library/packery.js", "/includes/lib/assets/min/js/library/lazysizes.js"], "is_default": 0, "condition": "Avada", "created_at": **********, "updated_at": "2023-02-15T04:32:16.000000Z", "icon_url": ""}, "c8349314-15a3-481e-973b-e4d936e4420e": {"id": "theme:56fa9993a573540c83eda9c49fae5e3c", "title": "Avada - Fusion slider", "type": "theme", "icon": "", "exclusions": ["/jquery-?[0-9.](.*)(.min|.slim|.slim.min)?.js", "/jquery-migrate(.min)?.js", "/fusion-core/js/min/avada-fusion-slider.js", "/Avada/includes/"], "is_default": 0, "condition": "Avada", "created_at": **********, "updated_at": "2023-02-15T04:32:16.000000Z", "icon_url": ""}, "7a04bc85-0fec-4487-ae05-bb2e5d8d0420": {"title": "Avada - Load Portfolio on pageload", "condition": "Avada", "exclusions": ["/jquery-?[0-9.](.*)(.min|.slim|.slim.min)?.js", "/jquery-migrate(.min)?.js", "/library/packery.js", "/library/isotope.js", "/library/imagesLoaded.js", "/general/fusion-lightbox.js", "/fusion-core/js/min/avada-portfolio.js"], "icon_url": "", "type": "theme", "id": "theme:c3f0ed4d94499b68c77d95db37d1d399", "is_default": 0, "created_at": 1696601814}, "55bd510c-78aa-49d5-8304-8be2ee2ab0da": {"id": "theme:2189c1c769d65cfc2182e4822847071b", "title": "Avada - Mobile menu", "type": "theme", "icon": "", "exclusions": ["/jquery-?[0-9.](.*)(.min|.slim|.slim.min)?.js", "/jquery-migrate(.min)?.js", "/assets/min/js/general/avada-menu.js", "/includes/lib/assets/min/js/library/modernizr.js", "/includes/lib/assets/min/js/library/jquery.easing.js"], "is_default": 0, "condition": "Avada", "created_at": **********, "updated_at": "2023-02-15T04:32:17.000000Z", "icon_url": ""}, "ddf00672-c35c-4b68-aeca-925e68bf12b2": {"id": "theme:97a185f08af70c39c7e221faab0f73eb", "title": "Avada - OffCanvas", "type": "theme", "icon": "", "exclusions": ["/jquery-?[0-9.](.*)(.min|.slim|.slim.min)?.js", "/jquery-migrate(.min)?.js", "/uploads/fusion-scripts/(.*).min.js", "window.off_canvas_"], "is_default": 0, "condition": "Avada", "created_at": **********, "updated_at": "2023-02-15T04:32:16.000000Z", "icon_url": ""}, "17544cc3-9d3a-4611-bc5d-44d04e2786fa": {"title": "Avada - Show the Portfolio grid on page load", "condition": "Avada", "exclusions": ["/jquery-?[0-9.](.*)(.min|.slim|.slim.min)?.js", "/jquery-migrate(.min)?.js", "/includes/lib/assets/min/js/library/imagesLoaded.js", "/includes/lib/assets/min/js/library/isotope.js", "/includes/lib/assets/min/js/library/lazysizes.js", "/includes/lib/assets/min/js/library/modernizr.js", "/includes/lib/assets/min/js/library/packery.js", "/fusion-core/js/min/avada-portfolio.js", "avadaPortfolioVars"], "icon_url": "", "type": "theme", "id": "theme:9d1a9b0c2ca20fca764a82f197b962fd", "is_default": 0, "created_at": 1696601823}, "c6c3347c-14e0-4766-afa5-df33a47f5a5a": {"id": "theme:3ff44421b404c5efffa25e78e479e4ea", "title": "Avada - Sticky menu", "type": "theme", "icon": "", "exclusions": ["/jquery-?[0-9.](.*)(.min|.slim|.slim.min)?.js", "/jquery-migrate(.min)?.js", "/Avada/assets/min/js/general/avada-menu.js"], "is_default": 0, "condition": "Avada", "created_at": **********, "updated_at": "2023-02-15T04:32:17.000000Z", "icon_url": ""}, "232596c1-6a6a-4fe8-a5c3-a60fa74a9456": {"id": "theme:0d727d80bb132f17c737e55883fe4be0", "title": "Avada - WooCommerce product gallery", "type": "theme", "icon": "", "exclusions": ["/jquery-?[0-9.](.*)(.min|.slim|.slim.min)?.js", "/jquery-migrate(.min)?.js", "/assets/min/js/general/avada-woo-product-images.js", "/includes/lib/assets/min/js/library/jquery.flexslider.js"], "is_default": 0, "condition": "Avada", "created_at": **********, "updated_at": "2023-02-15T04:32:17.000000Z", "icon_url": ""}, "95ece7e4-3b19-45e5-aa28-14f833c9afca": {"title": "Ave<PERSON>", "condition": "avesa", "exclusions": ["/jquery-?[0-9.](.*)(.min|.slim|.slim.min)?.js", "/jquery-migrate(.min)?.js", "/avesa/js/main.js", "/avesa/js/isotope.js", "/sw_core/js/slick.min.js", "/avesa/js/bootstrap-datetimepicker.min.js", "/avesa/js/bootstrap.min.js"], "icon_url": "", "type": "theme", "id": "theme:aad8bfcc594eec02e3b0d635198dee5e", "is_default": 0, "created_at": 1679737993}, "f2bfe477-4e45-4e52-a7d9-4d0ba3a92258": {"title": "<PERSON><PERSON>", "condition": "besa", "exclusions": ["/jquery-?[0-9.](.*)(.min|.slim|.slim.min)?.js", "/besa/js/"], "icon_url": "", "type": "theme", "id": "theme:c238e89523c46ca28b08e401f42f6ccc", "is_default": 0, "created_at": 1679738204}, "bd94908c-8138-4995-986b-47ec66494bdd": {"title": "BeTheme", "condition": "betheme", "exclusions": ["/jquery-?[0-9.](.*)(.min|.slim|.slim.min)?.js", "/betheme/", "mfn", "/wp-includes/js/jquery/ui/tabs.min.js", "/wp-includes/js/jquery/ui/core.min.js"], "icon_url": "", "type": "theme", "id": "theme:b99156eb9eeb357c0a70bd3bda6861cc", "is_default": 0, "created_at": 1679738639}, "190d2f1a-72a6-40ca-b08a-5c7ee7b0a6a5": {"title": "Bosa Online Education - Fixes animations and preloader", "condition": "bosa-online-education", "exclusions": ["\\/jquery(-migrate)?-?([0-9.]+)?(.min|.slim|.slim.min)?.js(\\?(.*))?( |'|\"|>)", "/bosa/assets/js/jquery.slicknav.min.js", "/bosa/assets/slick/slick.min.js", "/bosa/assets/js/navigation.js", "/bosa/assets/js/custom.min.js", "/bosa/assets/js/theia-sticky-sidebar.min.js"], "icon_url": "", "type": "theme", "id": "theme:5430bf7b83c83a3687b3b7b437e961b0", "is_default": 0, "created_at": 1708371742}, "6fb2b9d7-6ecc-4260-999c-938fbebdbf01": {"id": "theme:de8504b73ea228d0ea9bbce69752092e", "title": "Bridge", "type": "theme", "icon": "", "exclusions": ["/jquery-?[0-9.](.*)(.min|.slim|.slim.min)?.js", "/bridge-creative/bridge/js/", "/wp-includes/js/"], "is_default": 0, "condition": "bridge", "created_at": **********, "updated_at": "2023-02-15T04:32:16.000000Z", "icon_url": ""}, "1dd63427-b4c9-4596-b952-ac711e3637f9": {"title": "Bridge - Load elements without user interaction", "condition": "bridge", "exclusions": ["\\/jquery(-migrate)?-?([0-9.]+)?(.min|.slim|.slim.min)?.js(\\?(.*))?( |'|\"|>|$)", "/wp-content/themes/bridge/js/default.min.js", "/wp-content/themes/bridge/js/plugins.js", "/wp-content/themes/bridge/js/default_dynamic.js", "/wp-content/themes/bridge/js/jquery.touchSwipe.min.js"], "icon_url": "", "type": "theme", "id": "theme:ad9b810efd365ad9a27987d2912b94cd", "is_default": 0, "created_at": 1710252260}, "65698b6b-85dd-41ef-8fd7-718f1e983dba": {"title": "Car Dealer", "condition": "cardealer", "exclusions": ["/jquery-?[0-9.](.*)(.min|.slim|.slim.min)?.js", "/jquery-migrate(.min)?.js", "/wp-includes/js/jquery/(.*)", "/cardealer/js/(.*)"], "icon_url": "", "type": "theme", "id": "theme:b1111424fff61af8d1e152dcdd6810f6", "is_default": 0, "created_at": 1679737517}, "3927d724-5a0a-402b-a838-858d30b54ea9": {"title": "Cardea - Show Page Content on Load", "condition": "cardea-wp", "exclusions": ["\\/jquery(-migrate)?-?([0-9.]+)?(.min|.slim|.slim.min)?.js(\\?(.*))?( |'|\"|>|$)", "/themes/cardea-wp/js/main.js", "/themes/cardea-wp/js/jquery.sticky.js", "/themes/cardea-wp/js/jquery.fitvids.js", "/themes/cardea-wp/js/jquery.smartmenus.min.js"], "icon_url": "", "type": "theme", "id": "theme:d65d1a8303b0c7508278884520e4bec7", "is_default": 0, "created_at": 1698677525}, "087fb457-a09d-4140-84bd-c9bc1e8195b7": {"title": "CheerUp", "condition": "cheerup", "exclusions": ["/cheerup/js/jquery.sticky-sidebar.js", "/cheerup/js/object-fit-images.js", "/cheerup/js/jquery.fitvids.js", "/cheerup/js/jquery.mfp-lightbox.js", "/cheerup/js/ie-polyfills.js", "/cheerup/js/theme.js", "/wp-includes/js/imagesloaded.min.js", "/wp-includes/js/jquery/jquery-migrate.min.js", "/wp-includes/js/jquery/jquery.min.js"], "icon_url": "", "type": "theme", "id": "theme:13bcf562f45afb245dc4f76fecfba6d6", "is_default": 0, "created_at": 1696429398}, "eb86aedb-91e6-480c-b76c-756ac1da41be": {"title": "Clover", "condition": "clover-theme", "exclusions": ["/jquery-?[0-9.](.*)(.min|.slim|.slim.min)?.js", "/jquery-migrate(.min)?.js", "/clover-theme/"], "icon_url": "", "type": "theme", "id": "theme:89372f3d9321ae09c94488592084da29", "is_default": 0, "created_at": 1679738878}, "6e90b649-5736-497f-9bc6-515900cfea8a": {"title": "Divi - Animations", "condition": "<PERSON><PERSON>", "exclusions": ["/jquery-?[0-9.](.*)(.min|.slim|.slim.min)?.js", "/jquery-migrate(.min)?.js", ".dipi_preloader_wrapper_outer", "/Divi/js/scripts.min.js", "/Divi/js/custom.unified.js", "/js/magnific-popup.js", "var DIVI"], "icon_url": "", "type": "theme", "id": "theme:c0abf30dba4ff13db836d1b01685953a", "is_default": 0, "created_at": 1679737389}, "70916c43-4e02-4932-b6aa-91a1815bc755": {"title": "Divi - Background video", "condition": "<PERSON><PERSON>", "exclusions": ["/jquery-?[0-9.](.*)(.min|.slim|.slim.min)?.js", "/jquery-migrate.min.js", "/Divi/js/custom.unified.js", "/js/mediaelement/(.*)", "mejs"], "icon_url": "", "type": "theme", "id": "theme:c7edea41ae6716291e2d32a2ab429209", "is_default": 0, "created_at": 1679738240}, "08531785-9818-4e30-903e-564637a2ad7a": {"title": "Divi - Counter module", "condition": "<PERSON><PERSON>", "exclusions": ["/jquery-?[0-9.](.*)(.min|.slim|.slim.min)?.js", "/jquery-migrate(.min)?.js", ".dipi_preloader_wrapper_outer", "/Divi/js/scripts.min.js", "/Divi/js/custom.unified.js", "/js/magnific-popup.js", "var DIVI", "/Divi/includes/builder/feature/dynamic-assets/assets/js/easypiechart.js"], "icon_url": "", "type": "theme", "id": "theme:2c46b9f5a770f260c3f7115bb330b2d5", "is_default": 0, "created_at": 1679736810}, "1d63dd7c-7bc7-4629-b03d-0437e177af32": {"title": "Divi - Load Animated Elements on Page Load", "condition": "<PERSON><PERSON>", "exclusions": ["\\/jquery(-migrate)?-?([0-9.]+)?(.min|.slim|.slim.min)?.js(\\?(.*))?( |'|\"|>|$)", "/Divi/js/scripts.min.js", "/Divi/includes/builder/feature/dynamic-assets/assets/js/easypiechart.js", "/Divi/includes/builder/feature/dynamic-assets/assets/js/salvattore.js"], "icon_url": "", "type": "theme", "id": "theme:5df38c80278da90f725c16d68ea25aa1", "is_default": 0, "created_at": 1719498170}, "b996762a-84ef-440d-a089-73a187936fbf": {"title": "Divi - Mobile menu", "condition": "<PERSON><PERSON>", "exclusions": ["/jquery-?[0-9.](.*)(.min|.slim|.slim.min)?.js", "/jquery-migrate.min.js", "/Divi/js/scripts.min.js", "/Divi/js/custom.unified.js"], "icon_url": "", "type": "theme", "id": "theme:b9116994f4e4b9b9fa574440c00d2f0d", "is_default": 0, "created_at": 1679738580}, "ae096e1e-9c36-46ad-a3d1-c26ea507276b": {"title": "Divi - Sticky elements", "condition": "<PERSON><PERSON>", "exclusions": ["/jquery-?[0-9.](.*)(.min|.slim|.slim.min)?.js", "/jquery-migrate(.min)?.js", ".dipi_preloader_wrapper_outer", "/Divi/js/scripts.min.js", "/Divi/includes/builder/feature/dynamic-assets/assets/js/sticky-elements.js", "var DIVI"], "icon_url": "", "type": "theme", "id": "theme:8b62db03c90245f3e690335b079b05dc", "is_default": 0, "created_at": 1679737191}, "349f31f0-dd10-41d3-b0a4-9c5df64879f8": {"title": "Divi - Sticky menu", "condition": "<PERSON><PERSON>", "exclusions": ["/jquery-?[0-9.](.*)(.min|.slim|.slim.min)?.js", "/js/jquery/jquery-migrate.min.js", "/Divi/js/scripts.min.js", "/Divi/includes/builder/feature/dynamic-assets/assets/js/magnific-popup.js", "jqueryParams", "firstHeader"], "icon_url": "", "type": "theme", "id": "theme:b7b84aca0f0dc6a1ced31d38626c50ea", "is_default": 0, "created_at": 1679738821}, "59563458-5f04-4959-b3e2-53e49e169d67": {"title": "Divi - WooCommerce Single Product Images", "condition": "<PERSON><PERSON>", "exclusions": ["/Divi/js/scripts.min.js"], "icon_url": "", "type": "theme", "id": "theme:f9c5bdba8b39fc877b41dea00fa756f9", "is_default": 0, "created_at": 1684342262}, "6426539e-4e43-4fef-ab5a-3eb7b2a8b057": {"title": "<PERSON><PERSON><PERSON>", "condition": "<PERSON><PERSON>ra", "exclusions": ["/jquery-?[0-9.](.*)(.min|.slim|.slim.min)?.js", "/jquery-migrate.min.js", "/eikra/assets/js/", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "icon_url": "", "type": "theme", "id": "theme:55d2581ad975eb6325bc97fc3d3b0cb8", "is_default": 0, "created_at": 1679738450}, "59020bd6-069f-4f2d-afa2-fbdefa03211c": {"title": "<PERSON><PERSON><PERSON>", "condition": "ekko", "exclusions": ["/jquery-?[0-9.](.*)(.min|.slim|.slim.min)?.js", "/jquery-migrate(.min)?.js", "/ekko/"], "icon_url": "", "type": "theme", "id": "theme:2c06f4a1949f8ba4e77042a47674fd9e", "is_default": 0, "created_at": 1679737803}, "d82f5cdd-c5d3-4596-94dc-1e25aaff2083": {"title": "<PERSON><PERSON><PERSON>", "condition": "elessi-theme", "exclusions": ["/jquery-?[0-9.](.*)(.min|.slim|.slim.min)?.js", "/jquery-migrate(.min)?.js", "/elessi-theme/assets/js/min/functions.min.js", "/elessi-theme/assets/js/min/main.min.js"], "icon_url": "", "type": "theme", "id": "theme:d5d2b7fda7b8a2b5b91d430f7602e230", "is_default": 0, "created_at": 1679737773}, "f0587c21-54d0-429d-8efe-18a93dacb18d": {"id": "theme:5fc04cc678cb54567aedb51027933002", "title": "Enfold", "type": "theme", "icon": "", "exclusions": ["/jquery-?[0-9.](.*)(.min|.slim|.slim.min)?.js", "/jquery-migrate(.min)?.js", "/dynamic_avia/avia-footer-scripts-(.*).js", "var avia_is_mobile"], "is_default": 0, "condition": "enfold", "created_at": **********, "updated_at": "2023-02-15T04:32:17.000000Z", "icon_url": ""}, "c93ee1ee-3956-4278-9ee9-1a0968753e86": {"title": "Enfold - Fix hamburger menu", "condition": "enfold", "exclusions": ["\\/jquery(-migrate)?-?([0-9.]+)?(.min|.slim|.slim.min)?.js(\\?(.*))?( |'|\"|>)", "/enfold/js/avia-snippet-hamburger-menu.js", "/enfold/js/avia.js", "/enfold/js/shortcodes.js", "/enfold/js/waypoints/waypoints.js"], "icon_url": "", "type": "theme", "id": "theme:2b0c22c5169b94c2eabb125d18915246", "is_default": 0, "created_at": 1715090218}, "cc0550cb-918e-419d-b4f2-1809cf666dbb": {"id": "theme:eb759a03d0ca292c948f09d004a2963f", "title": "Enfold - LayerSlider", "type": "theme", "icon": "", "exclusions": ["/jquery-?[0-9.](.*)(.min|.slim|.slim.min)?.js", "/jquery-migrate(.min)?.js", "layerslider"], "is_default": 0, "condition": "enfold", "created_at": **********, "updated_at": "2023-02-15T04:32:17.000000Z", "icon_url": ""}, "ec883654-4f63-4fae-a3ef-923dcbc2426d": {"title": "Enfold - Shortcodes", "condition": "enfold", "exclusions": ["/jquery-?[0-9.](.*)(.min|.slim|.slim.min)?.js", "/jquery-migrate(.min)?.js", "/enfold/js/avia(.min)?.js", "/enfold/js/shortcodes(.min)?.js", "/enfold/config-templatebuilder/avia-shortcodes/", "/enfold/js/avia-compat(.min)?.js", "/enfold/js/waypoints/waypoints.min.js", "/enfold/js/avia-snippet-(.*).js", "/enfold/js/avia-js(.min)?.js", "/enfold/js/aviapopup/jquery.magnific-popup(.min)?.js"], "icon_url": "", "type": "theme", "id": "theme:072fc4077d7071791d774d6ddbf5dc2a", "is_default": 0, "created_at": 1712954619}, "fe3546f0-be3e-4173-8992-a7f6f203b82f": {"title": "Envision", "condition": "envision", "exclusions": ["/jquery-?[0-9.](.*)(.min|.slim|.slim.min)?.js", "/jquery-migrate(.min)?.js", "/envision/lib/js/app.min.js", "var CloudFwOp"], "is_default": 1, "icon_url": "", "type": "theme", "id": "theme:fc5f7d69b646ed95835badc0fc23bc11", "created_at": 1679737494}, "d997b942-19de-4710-9c81-79d3c65cbd76": {"id": "theme:047f009f2a1f4cdf2088c46be47e385b", "title": "<PERSON><PERSON><PERSON>", "type": "theme", "icon": "", "exclusions": ["/jquery-?[0-9.](.*)(.min|.slim|.slim.min)?.js", "/jquery-migrate(.min)?.js", "/gt3-themes-core/", "/uploads/gt3-assets/js/(.*)", "/wp-includes/js/imagesloaded.min.js"], "is_default": 0, "condition": "ewe<PERSON>", "created_at": **********, "updated_at": "2023-02-15T04:32:16.000000Z", "icon_url": ""}, "53d29aec-8ae4-4273-b748-f5bd52dfe177": {"title": "<PERSON><PERSON>", "condition": "<PERSON>vis", "exclusions": ["/jquery-?[0-9.](.*)(.min|.slim|.slim.min)?.js", "/jquery-migrate(.min)?.js", "/farvis/"], "icon_url": "", "type": "theme", "id": "theme:26bff2f3a6ff6347d35edf5c77a35687", "is_default": 0, "created_at": 1679737972}, "fb01246b-a5f8-4021-b514-c02cf55e80bd": {"title": "Flatsome", "condition": "flatsome", "exclusions": ["/jquery-?[0-9.](.*)(.min|.slim|.slim.min)?.js", "/jquery-migrate(.min)?.js", "/flatsome/assets/js/flatsome.js", "/flatsome/assets/libs/packery.pkgd.min.js", "/flatsome/assets/js/woocommerce.js"], "icon_url": "", "type": "theme", "id": "theme:26fb1cf80f074ca199d8a7e94c5fc796", "is_default": 0, "created_at": 1679738732}, "4d65dc12-9ce7-4171-94a7-9821fd95240e": {"id": "theme:28a6f8b3319c107a34603be0f01a4bcf", "title": "Flatsome - Google map", "type": "theme", "icon": "", "exclusions": ["/jquery-?[0-9.](.*)(.min|.slim|.slim.min)?.js", "/jquery-migrate(.min)?.js", "maps.googleapis.com", "google.maps.LatLng", "/wp-includes/js/hoverIntent.min.js"], "is_default": 0, "condition": "flatsome", "created_at": **********, "updated_at": "2023-02-15T04:32:16.000000Z", "icon_url": ""}, "890e15b6-c66b-4a9e-9b7d-55417df94916": {"title": "Flatsome - Images", "condition": "flatsome", "exclusions": ["/jquery-?[0-9.](.*)(.min|.slim|.slim.min)?.js", "/jquery-migrate(.min)?.js", "/flatsome/inc/integrations/wp-rocket/flatsome-wp-rocket.js", "/flatsome/assets/js/flatsome.js", "/flatsome/inc/extensions/flatsome-lazy-load/flatsome-lazy-load.js"], "icon_url": "", "type": "theme", "id": "theme:f2d60aad9f2f5395e3e145cf8f8ab165", "is_default": 0, "created_at": 1679737691}, "cb54d070-8ee0-4c35-9fa9-b2bac73ccf39": {"title": "Frida", "condition": "frida", "exclusions": ["/wp-includes/js/jquery/jquery.min.js", "/wp-includes/js/jquery/jquery-migrate.min.js", "/frida/"], "icon_url": "", "type": "theme", "id": "theme:109ddf56796a5133e12279f3daa5ff62", "is_default": 0, "created_at": 1679738013}, "435fe79f-47ba-422e-aca6-cea566f6b8a1": {"title": "Gardena Theme", "condition": "gardena", "exclusions": ["\\/jquery(-migrate)?-?([0-9.]+)?(.min|.slim|.slim.min)?.js(\\?(.*))?( |'|\"|>|$)", "/gardena/"], "icon_url": "", "type": "theme", "id": "theme:993e6f0fc44b9e55c0a565b84a449340", "is_default": 0, "created_at": 1713905793}, "9e78539a-03d9-442b-ab94-dd3b7a9658e4": {"id": "theme:80a330247d61d729fcd78dc01de6ed2f", "title": "GeneratePress - Mobile menu", "type": "theme", "icon": "", "exclusions": ["/generatepress/assets/js/menu.min.js", "generatepressMenu", "/gp-premium/menu-plus/functions/js/offside.min.js"], "is_default": 0, "condition": "generatepress", "created_at": **********, "updated_at": "2023-02-15T04:32:17.000000Z", "icon_url": ""}, "02d47d01-56f5-4801-b319-cff1707dd59d": {"title": "Harmuny - Modern WordPress Blog Theme", "condition": "harmuny", "exclusions": ["\\/jquery(-migrate)?-?([0-9.]+)?(.min|.slim|.slim.min)?.js(\\?(.*))?( |'|\"|>)", "/harmuny/"], "icon_url": "", "type": "theme", "id": "theme:1ff9662c2a3e3221052cbe229feed18c", "is_default": 0, "created_at": **********}, "6373bbb2-877c-4075-b6e4-7c58d686b25c": {"title": "HealthFirst - Prevent console errors", "condition": "healthfirst", "exclusions": ["\\/jquery(-migrate)?-?([0-9.]+)?(.min|.slim|.slim.min)?.js(\\?(.*))?( |'|\"|>|$)", "/wp-includes/js/hoverIntent.min.js", "/wp-content/plugins/healthfirst-core/assets/js/healthfirst-core.min.js", "/wp-content/plugins/healthfirst-core/assets/plugins/modernizr/modernizr.js", "/wp-content/plugins/healthfirst-core/assets/plugins/perfect-scrollbar/perfect-scrollbar.jquery.min.js", "/wp-content/themes/healthfirst/assets/js/main.min.js", "/wp-content/themes/healthfirst/assets/plugins/waitforimages/jquery.waitforimages.js"], "icon_url": "", "type": "theme", "id": "theme:d244b3c692f8d023048207dbe9eb84da", "is_default": 0, "created_at": **********}, "2c72e7e6-cb77-44e9-af87-d5c42ae6db52": {"title": "Honor - WPBakery fix", "condition": "honor", "exclusions": ["/honor/js/__scripts.js", "HONOR_STORAGE", "/js_composer/"], "icon_url": "", "type": "theme", "id": "theme:9f228373ff4d172655dbf5cb3b1bc23a", "is_default": 0, "created_at": **********}, "9309d1d3-1035-4a2c-8ced-075bc3ff9957": {"title": "HotelMaster", "condition": "hotelmaster", "exclusions": ["/wp-includes/js/jquery/jquery.min.js", "/wp-includes/js/jquery/jquery-migrate.min.js", "/hotelmaster/javascript/gdlr-script.js", "/hotelmaster/plugins/dl-menu/modernizr.custom.js", "/hotelmaster/plugins/dl-menu/jquery.dlmenu.js", "/hotelmaster/plugins/superfish/js/superfish.js", "/hotelmaster/plugins/jquery.easing.js"], "icon_url": "", "type": "theme", "id": "theme:978eaddad3b1047e479407b6d92197aa", "is_default": 0, "created_at": 1679738595}, "4a09f745-cbb1-47c8-b50a-c8014d5d1335": {"id": "theme:e813a548bceac6765a1cdf2316f1a6ab", "title": "HotelMaster - Blog", "type": "theme", "icon": "", "exclusions": ["/wp-includes/js/masonry.min.js", "/gp-premium/blog/functions/js/scripts.min.js", "/wp-includes/js/imagesloaded.min.js"], "is_default": 0, "condition": "hotelmaster", "created_at": **********, "updated_at": "2023-02-15T04:32:16.000000Z", "icon_url": ""}, "f2d8f704-ec68-4278-9ca2-885daa0c1ce5": {"id": "theme:398a264e302e42640553681e8759cd07", "title": "HotelMaster - Masonry", "type": "theme", "icon": "", "exclusions": ["/gp-premium/menu-plus/functions/js/offside.min.js", "offSide"], "is_default": 0, "condition": "hotelmaster", "created_at": **********, "updated_at": "2023-02-15T04:32:16.000000Z", "icon_url": ""}, "92b2e258-1f44-41c1-b1bd-f117f61ee49d": {"title": "<PERSON><PERSON> - Fix masonry grid", "condition": "jannah", "exclusions": ["/wp-includes/js/jquery/jquery.min.js", "/wp-includes/js/masonry.min.js", "/wp-includes/js/jquery/jquery.masonry.min.js", "tie-"], "icon_url": "", "type": "theme", "id": "theme:35f7f183089309f52046377ca65e905a", "is_default": 0, "created_at": 1699642920}, "df52436c-53d6-461a-b81a-cd0b21680524": {"title": "JNews", "condition": "jnews", "exclusions": ["/jquery-?[0-9.](.*)(.min|.slim|.slim.min)?.js", "/jquery-migrate.min.js", "/jnews/assets/js/", "jnews", "jfla"], "icon_url": "", "type": "theme", "id": "theme:5d90e451984f9d894b1aabb0d00f30a2", "is_default": 0, "created_at": 1679738860}, "87ed69a2-3295-4fad-a82e-eeb02925a5dc": {"title": "Jobify", "condition": "jobify", "exclusions": ["/jquery-?[0-9.](.*)(.min|.slim|.slim.min)?.js", "/jquery-migrate(.min)?.js", "/jobify/js/jobify.min.js", "/jobify/js/select2.full.min.js"], "icon_url": "", "type": "theme", "id": "theme:592006aa4562a6915e344e5e2a09e5ee", "is_default": 0, "created_at": 1704735224}, "28a0b1ea-8d2f-4931-a48a-166b8df8a773": {"title": "JOYN", "condition": "joyn", "exclusions": ["/jquery-?[0-9.](.*)(.min|.slim|.slim.min)?.js", "/jquery-migrate(.min)?.js", "/swift-framework/includes/page-builder/frontend-assets/js/lib/modernizr-custom.js", "/swift-framework/includes/page-builder/frontend-assets/js/spb-functions.min.js", "/swift-framework/includes/swift-slider/assets/js/swift-slider.min.js", "/swift-framework/public/js/lib/imagesloaded.pkgd.min.js", "/joyn/js/owl.carousel.min.js", "/joyn/js/theme-scripts.js", "/joyn/js/functions.js"], "icon_url": "", "type": "theme", "id": "theme:c395470ad2d4d681836cd942bbb03120", "is_default": 0, "created_at": 1679738527}, "542be60a-2346-4740-9a41-8a580c4f013c": {"title": "Juno Toys", "condition": "<PERSON><PERSON><PERSON><PERSON>", "exclusions": ["/jquery-?[0-9.](.*)(.min|.slim|.slim.min)?.js", "/jquery-migrate.min.js", "/junotoys/fw/js/core.init.js", "/junotoys/fw/js/core.utils.js", "/junotoys/fw/js/superfish.js", "/junotoys/fw/js/swiper/swiper.js", "/trx_utils/shortcodes/theme.shortcodes.js", "/wp-includes/js/jquery/ui/(.*)"], "icon_url": "", "type": "theme", "id": "theme:2acab38e8356d36355bb81d931e7fba4", "is_default": 0, "created_at": 1679737788}, "06167710-10c7-446e-a08b-ce676e444102": {"title": "Jupiter", "condition": "jupiter", "exclusions": ["/jquery-?[0-9.](.*)(.min|.slim|.slim.min)?.js", "/jquery-migrate(.min)?.js", "/jupiter/", "/wp-includes/js/underscore.min.js", "WebFont.load"], "icon_url": "", "type": "theme", "id": "theme:89c5c30498c2989611f9044be006197c", "is_default": 0, "created_at": 1679738430}, "5d042e1f-7e62-4ec4-ba31-30d396004522": {"title": "JupiterX", "condition": "jupiterx", "exclusions": ["/jquery-?[0-9.](.*)(.min|.slim|.slim.min)?.js", "/jquery-migrate(.min)?.js", "/jupiterx/(.*).js", "/wp-includes/js/underscore.min.js", "WebFont.load"], "icon_url": "", "type": "theme", "id": "theme:b06632962a4948d4944fd8d79ffbfceb", "is_default": 0, "created_at": 1679737312}, "c4b030ea-66a1-4729-85bf-a484e373a316": {"title": "Kadence", "condition": "kadence", "exclusions": ["/kadence/assets/js/navigation.min.js", "mobile_menu_breakpoint", "kadenceConfig"], "icon_url": "", "type": "theme", "id": "theme:4b7907ee68218db279648da9bf7102d1", "is_default": 0, "created_at": 1704735260}, "20c605b4-3e3a-4bb0-a5e5-a08e2cb0f31f": {"title": "<PERSON><PERSON>", "condition": "kalium", "exclusions": ["/jquery-?[0-9.](.*)(.min|.slim|.slim.min)?.js", "/jquery-migrate(.min)?.js", "/kalium/assets/js/main.min.js", "mobile_menu_breakpoint", "var _k"], "icon_url": "", "type": "theme", "id": "theme:1fcb99a1ab06e1e36635365ed3e59ce5", "is_default": 0, "created_at": 1679737406}, "e1e04a7d-635a-4e28-83d9-e345ce40e354": {"title": "<PERSON><PERSON>", "condition": "kava", "exclusions": ["/jquery-?[0-9.](.*)(.min|.slim|.slim.min)?.js", "/jquery-migrate(.min)?.js", "/kava/assets/js/theme-script.js"], "icon_url": "", "type": "theme", "id": "theme:359d67efbf530c998245225dd3245a88", "is_default": 0, "created_at": 1679738609}, "e833c36e-ee89-4924-b608-3f28327c2f85": {"title": "<PERSON>", "condition": "lay", "exclusions": ["/jquery-?[0-9.](.*)(.min|.slim|.slim.min)?.js", "/wp-includes/js/backbone.min.js", "/wp-includes/js/underscore.min.js", "/lay/", "/laytheme-carousel/", "window.laytheme"], "icon_url": "", "type": "theme", "id": "theme:7c718c6da874ea6e4b27c6d70bc4e7e8", "is_default": 0, "created_at": 1679737453}, "2df2ef47-a833-4711-ba54-48dc62586f37": {"id": "theme:85d9922ac61ed833fd047a67029df8e5", "title": "LazaNews", "type": "theme", "icon": "", "exclusions": ["/jquery-?[0-9.](.*)(.min|.slim|.slim.min)?.js", "/jquery-migrate(.min)?.js", "/jquery.custom.js"], "is_default": 0, "condition": "lazanews", "created_at": **********, "updated_at": "2023-02-15T04:32:16.000000Z", "icon_url": ""}, "9443304a-34e9-4700-a03a-5f8f62f83ed1": {"title": "Listeo", "condition": "listeo", "exclusions": ["/jquery-?[0-9.](.*)(.min|.slim|.slim.min)?.js", "/jquery-migrate(.min)?.js", "/listeo/js/"], "icon_url": "", "type": "theme", "id": "theme:db7da585545001f5ae614a2810f08f3a", "is_default": 0, "created_at": 1679737244}, "77afa73c-c4a8-42a8-aaee-43f6a761364e": {"title": "ListingPro", "condition": "listingpro", "exclusions": ["/jquery-?[0-9.](.*)(.min|.slim|.slim.min)?.js", "/jquery-migrate(.min)?.js", "/listingpro/assets/lib/jquerym.menu/js/jquery.mmenu.min.all.js", "/listingpro/assets/lib/Magnific-Popup-master/jquery.magnific-popup.min.js", "/listingpro/assets/js/select2.full.min.js", "/listingpro/assets/js/jquery.city-autocomplete.js", "/listingpro/assets/js/chosen.jquery.min.js", "/listingpro/assets/lib/bootstrap/js/bootstrap-slider.js", "/listingpro/assets/js/jquery-ui.js", "/listingpro/assets/js/mapbox.js", "/listingpro/assets/js/main.js", "/listingpro/assets/js/leaflet.markercluster.js", "maps"], "icon_url": "", "type": "theme", "id": "theme:0b365e43dfc65d2b1b70fac6510c7c9c", "is_default": 0, "created_at": 1679737938}, "01b369b5-b578-4314-8e95-40b67a41d75a": {"title": "Master Study", "condition": "masterstudy", "exclusions": ["/jquery-?[0-9.](.*)(.min|.slim|.slim.min)?.js", "/jquery-migrate(.min)?.js", "/masterstudy/assets/js/custom.js", "/masterstudy/assets/vendors/jquery.fancybox.min.js", "/masterstudy/assets/js/select2.full.min.js"], "icon_url": "", "type": "theme", "id": "theme:0ee224c20e4ef7d546733d933db598f2", "is_default": 0, "created_at": 1704735299}, "6b000cc7-d33b-4109-9c39-6119a5d81cde": {"title": "Maya", "condition": "maya", "exclusions": ["/jquery-?[0-9.](.*)(.min|.slim|.slim.min)?.js", "/jquery-migrate(.min)?.js", "/maya/js/jquery.mobilemenu.js", "/maya/js/jquery.custom.js", "/maya/core/includes/js/jquery.tipsy.js"], "icon_url": "", "type": "theme", "id": "theme:719fe28004fcdd81a820602924aa8074", "is_default": 0, "created_at": 1679737916}, "ef3c76d6-1041-473d-81ea-a5a6e8c86735": {"title": "MH Magazine", "condition": "mh-magazine", "exclusions": ["/jquery-?[0-9.](.*)(.min|.slim|.slim.min)?.js", "/jquery-migrate(.min)?.js", "/mh-magazine/"], "icon_url": "", "type": "theme", "id": "theme:2a0cd6efc2f46be69de61712729a2ec9", "is_default": 0, "created_at": 1679737743}, "9cbb2777-5524-43b8-af36-692b27452c0d": {"title": "Minimog", "condition": "minimog", "exclusions": ["/jquery-?[0-9.](.*)(.min|.slim|.slim.min)?.js", "/jquery-migrate(.min)?.js", "/minimog/"], "icon_url": "", "type": "theme", "id": "theme:61c0c235042359ee7d2a9035e79a7da2", "is_default": 0, "created_at": 1679738135}, "7eb81c74-8062-4a6c-bf66-b7c5bc160141": {"title": "<PERSON><PERSON><PERSON>", "condition": "moozo-elementor", "exclusions": ["/jquery-?[0-9.](.*)(.min|.slim|.slim.min)?.js", "/jquery-migrate(.min)?.js", "/moozo-elementor/assets/js/countdown.js", "/moozo-elementor/assets/vendor/countdown/countdown.min.js"], "icon_url": "", "type": "theme", "id": "theme:2ddb538c8e6b6c766fffd0d5c861fd82", "is_default": 0, "created_at": 1679738623}, "e90b7bfa-9ff6-4e1a-bf29-6207d55fdd39": {"title": "Motor", "condition": "motor", "exclusions": ["/jquery-?[0-9.](.*)(.min|.slim|.slim.min)?.js", "/jquery-migrate(.min)?.js", "/motor/js/"], "icon_url": "", "type": "theme", "id": "theme:b33538179f5661a86cbe327a1793e199", "is_default": 0, "created_at": 1683973354}, "445e625a-f955-41fa-84de-65d9ea19be07": {"title": "My Listing", "condition": "my-listing", "exclusions": ["/jquery-?[0-9.](.*)(.min|.slim|.slim.min)?.js", "/jquery-migrate(.min)?.js", "/my-listing/assets/", "/wp-includes/js/dist/vendor/moment.min.js", "maps.googleapis.com", "MyListing", "_Explore_Settings"], "icon_url": "", "type": "theme", "id": "theme:afacb777229ddf5cabceacc64948057d", "is_default": 0, "created_at": 1679737725}, "00b8cd2d-2781-4fbb-ac5d-00750ba94ac9": {"title": "Neve - Mobile menu", "condition": "neve", "exclusions": ["/neve/assets/js/build/modern/frontend.js"], "icon_url": "", "type": "theme", "id": "theme:5ae731cc06dd9284f8172675a6fe81ab", "is_default": 0, "created_at": 1679738714}, "483abc54-f1fc-47dc-bfc1-a269c7d1c849": {"title": "Newspaper - Images", "condition": "Newspaper", "exclusions": ["/jquery-?[0-9.](.*)(.min|.slim|.slim.min)?.js", "/jquery-migrate(.min)?.js", "tagdiv_theme.min.js", "tdBlocksArray", "/wp-includes/js/underscore.min.js", "/td-cloud-library/assets/js/", "/npm/slick-carousel@1.8.1/slick/slick.min.js", "tdb-gallery-wrap", "tdBlocksArray", "tdb_"], "icon_url": "", "type": "theme", "id": "theme:649ff22527bac2b1c8e0115cd3851d53", "is_default": 0, "created_at": 1695805761}, "50db8d14-d421-4237-be14-a6f7b5c11ec5": {"title": "Newspaper - Slider & YouTube", "condition": "Newspaper", "exclusions": ["/jquery-?[0-9.](.*)(.min|.slim|.slim.min)?.js", "/jquery-migrate(.min)?.js", "tagdiv_theme.min.js", "tdBlocksArray", "tdb_globals", "td_youtube_list_ids", "iosSlider", "/td-cloud-library/assets/js/js_files_for_front.min.js", "/wp-includes/js/underscore.min.js", "/td-cloud-library/assets/js/", "/npm/slick-carousel@1.8.1/slick/slick.min.js", "tdb-gallery-wrap", "tdBlocksArray", "tdb_"], "icon_url": "", "type": "theme", "id": "theme:7e2eeee57ae458c5959342eda6526bf1", "is_default": 0, "created_at": 1695805747}, "514f4c30-2b67-4648-960e-dfe1cc401ca5": {"title": "<PERSON><PERSON>", "condition": "niva", "exclusions": ["/jquery-?[0-9.](.*)(.min|.slim|.slim.min)?.js", "/jquery-migrate(.min)?.js", "/niva/js/", "/sweetthemes-framework/js/", "mt_typed"], "icon_url": "", "type": "theme", "id": "theme:c4838f73a344b829ed626635e210dcf4", "is_default": 0, "created_at": 1679738256}, "e67ee504-c3b1-455e-88ab-1fae8c830652": {"title": "OceanWP", "condition": "oceanwp", "exclusions": ["/jquery-?[0-9.](.*)(.min|.slim|.slim.min)?.js", "/jquery-migrate(.min)?.js", "/oceanwp/"], "icon_url": "", "type": "theme", "id": "theme:db37af4b7d12695d37d9256313a5f37a", "is_default": 0, "created_at": 1679737552}, "e44e240a-8765-4f4f-b67e-d54e4b727506": {"title": "OceanWP - Mobile menu", "condition": "oceanwp", "exclusions": ["/jquery-?[0-9.](.*)(.min|.slim|.slim.min)?.js", "/jquery-migrate(.min)?.js", "/ocean-side-panel/assets/js/side-panel.min.js", "/oceanwp/assets/js/theme.vanilla.min.js"], "icon_url": "", "type": "theme", "id": "theme:9babbcd52b2ce558d299a06cd1130a11", "is_default": 0, "created_at": 1679738463}, "897b0100-958d-4a02-b6b2-1e753e9869f4": {"title": "PenNews", "condition": "pennews", "exclusions": ["/jquery-?[0-9.](.*)(.min|.slim|.slim.min)?.js", "/jquery-migrate(.min)?.js", "/pennews/js/script.min.js", "/pennews/js/script.lib.min.js"], "icon_url": "", "type": "theme", "id": "theme:02811fa00bc1471bb5be0457ce0ee005", "is_default": 0, "created_at": 1704735339}, "edd3ba03-e0fd-4b6e-911b-60b29f3471bf": {"title": "Pharmacy Mentor", "condition": "pharmacymentor", "exclusions": ["/jquery-?[0-9.](.*)(.min|.slim|.slim.min)?.js", "/wp-includes/js/jquery/jquery-migrate.min.js", "/pharmacymentor/"], "icon_url": "", "type": "theme", "id": "theme:3188aaf1ef2c39937450f2a14ebb1174", "is_default": 0, "created_at": 1679738214}, "725415ff-cc76-45cc-a131-3170e5aa30fc": {"title": "Porto", "condition": "porto", "exclusions": ["/jquery-?[0-9.](.*)(.min|.slim|.slim.min)?.js", "/jquery-migrate(.min)?.js", "/wp-includes/js/jquery/ui/", "/porto/js/theme.js", "/porto/js/theme.min.js"], "icon_url": "", "type": "theme", "id": "theme:8493f398f200c8dffe60d46439dd3360", "is_default": 0, "created_at": 1679737758}, "2c19bcec-f3ff-4873-bfd7-db6bc0f6433c": {"title": "Porto - Owl Carousel", "condition": "porto", "exclusions": ["\\/jquery(-migrate)?-?([0-9.]+)?(.min|.slim|.slim.min)?.js(\\?(.*))?( |'|\"|>)", "/wp-includes/js/jquery/ui/", "/porto/js/theme(|.min).js", "/porto/js/libs/owl.carousel(|.min).js", "/porto/js/theme-async(|.min).js", "/prettyPhoto/jquery.prettyPhoto(|.min).js"], "icon_url": "", "type": "theme", "id": "theme:f34f5199fb7fca78852cd3fb7758f178", "is_default": 0, "created_at": 1687527252}, "5b11ec89-1cb2-4793-8b77-79e917e810a0": {"title": "Pro Theme - Fix menu and accordions", "condition": "pro", "exclusions": ["\\/jquery(-migrate)?-?([0-9.]+)?(.min|.slim|.slim.min)?.js(\\?(.*))?( |'|\"|>)", "/pro/cornerstone/assets/js/site/cs-classic.(.*).js"], "icon_url": "", "type": "theme", "id": "theme:8f30d70dd2d9a0386445aef8fdd534a9", "is_default": 0, "created_at": 1708100602}, "31347ccb-f69e-4cd3-bd47-b80ce14ac76e": {"title": "ProPhoto", "condition": "prophoto7", "exclusions": ["/jquery-?[0-9.](.*)(.min|.slim|.slim.min)?.js", "/prophoto7/js/bundle.front.js", "/wp-includes/js/underscore.min.js", "PROPHOTO"], "icon_url": "", "type": "theme", "id": "theme:a5836a56c4472fade4dc6ebfe2281554", "is_default": 0, "created_at": 1679737425}, "a8208c04-865c-49aa-ab96-41e378d391c8": {"title": "Publisher", "condition": "publisher", "exclusions": ["/jquery-?[0-9.](.*)(.min|.slim|.slim.min)?.js", "/wp-includes/js/jquery/jquery-migrate.min.js", "/publisher/js/"], "icon_url": "", "type": "theme", "id": "theme:32c73be0cb175da278c8e2af0811b0d1", "is_default": 0, "created_at": 1679738169}, "a0d69f3d-1356-4a1d-a600-2f2f788b8a9a": {"title": "REHub", "condition": "rehub-theme", "exclusions": ["/jquery-?[0-9.](.*)(.min|.slim|.slim.min)?.js", "/jquery-migrate(.min)?.js", "/rehub-theme/js/custom_floatpanel.js"], "icon_url": "", "type": "theme", "id": "theme:321af1febb74f488cf911380893739b2", "is_default": 0, "created_at": 1679738502}, "8b74bbe5-7f32-42df-908f-78c99a8cad82": {"title": "Rey", "condition": "rey", "exclusions": ["/jquery-?[0-9.](.*)(.min|.slim|.slim.min)?.js", "/jquery-migrate(.min)?.js", "/rey/scripts-(.*).js", "/rey-core/assets/js/"], "icon_url": "", "type": "theme", "id": "theme:e46567cd0f3ec9b37e7230dc87eac367", "is_default": 0, "created_at": 1679737011}, "5c9115d3-bcbd-49d6-8feb-4880d2b82bfe": {"title": "Rife Free", "condition": "rife-free", "exclusions": ["/rife-free/js/script.min.js", "/rife-free/js/isotope.pkgd.min.js", "/jquery-?[0-9.](.*)(.min|.slim|.slim.min)?.js", "/jquery-migrate(.min)?.js", "var reJS"], "icon_url": "", "type": "theme", "id": "theme:74a449954de79625eccc6750e87af8f6", "is_default": 0, "created_at": 1679738107}, "6542b2fd-1f91-4862-aa18-11eecc02faaf": {"title": "<PERSON><PERSON><PERSON>", "condition": "roisin", "exclusions": ["/jquery-?[0-9.](.*)(.min|.slim|.slim.min)?.js", "/jquery-migrate(.min)?.js", "/themes/roisin/assets/js/main.min.js", "/plugins/roisin-core/assets/js/roisin-core.min.js", "/wp-includes/js/hoverIntent.min.js"], "icon_url": "", "type": "theme", "id": "theme:8f38fe58034772931110930b91cb6797", "is_default": 0, "created_at": 1685964523}, "5a192ad9-d150-4aa0-8efc-d68131cb7a37": {"title": "Sahifa - Mobile Menu", "condition": "<PERSON>hifa", "exclusions": ["\\/jquery(-migrate)?-?([0-9.]+)?(.min|.slim|.slim.min)?.js(\\?(.*))?( |'|\"|>)", "/themes/sahifa/js/ilightbox.packed.js", "/translate_a/element.js"], "icon_url": "", "type": "theme", "id": "theme:06ebe49f4c1e5b04cece831f8bb198a3", "is_default": 0, "created_at": 1687540363}, "7ae9d978-d63c-4a58-beb3-418bebb5b23c": {"title": "Salient", "condition": "salient", "exclusions": ["/jquery-?[0-9.](.*)(.min|.slim|.slim.min)?.js", "/jquery-migrate(.min)?.js", "/salient/", "winW > bodyW"], "icon_url": "", "type": "theme", "id": "theme:b3e12d57ac23897be1bb2c673e3fc761", "is_default": 0, "created_at": 1704735382}, "35aaa6c5-4a37-4161-b504-fb3ebc4b1148": {"title": "Salient - Nectar slider", "condition": "salient", "exclusions": ["/salient-nectar-slider/js/nectar-slider.js", "/jquery-?[0-9.](.*)(.min|.slim|.slim.min)?.js", "/jquery-migrate(.min)?.js"], "icon_url": "", "type": "theme", "id": "theme:d612db1dd8dc76faa6a36a9ebfd336dc", "is_default": 0, "created_at": 1704735415}, "84c95206-3e59-4eb3-a0c9-e2231a1c0a48": {"title": "SEO Lounge", "condition": "se<PERSON><PERSON>e", "exclusions": ["\\/jquery(-migrate)?-?([0-9.]+)?(.min|.slim|.slim.min)?.js(\\?(.*))?$", "/js_composer/assets/js/dist/js_composer_front.min.js", "/seolounge/js/radiantthemes-custom.js", "/seolounge/js/radiantthemes-core.min.js"], "icon_url": "", "type": "theme", "id": "theme:3d72b779d9c4ba6b51cc5b245b141433", "is_default": 0, "created_at": 1691695809}, "cd44aa56-088a-40dd-bf1e-f835efa68626": {"title": "Shoptimizer", "condition": "shoptimizer", "exclusions": ["/jquery-?[0-9.](.*)(.min|.slim|.slim.min)?.js", "/jquery-migrate(.min)?.js", "/shoptimizer/assets/js/lazyload-bg.js", "/shoptimizer/assets/js/main.min.js"], "icon_url": "", "type": "theme", "id": "theme:6c32b43f4da639e5901574fac6b7d387", "is_default": 0, "created_at": 1679737143}, "d2c48a48-430a-4eea-bc05-99b66f1f6a7b": {"title": "SmartMag", "condition": "smart-mag", "exclusions": ["/smart-mag/js/lazyload.js"], "icon_url": "", "type": "theme", "id": "theme:23d6b7878bd0087addb067db3fa39864", "is_default": 0, "created_at": 1679738490}, "dd0d9133-ef17-4dac-b174-9f25d535838f": {"title": "Soledad", "condition": "soledad", "exclusions": ["/jquery-?[0-9.](.*)(.min|.slim|.slim.min)?.js", "/jquery-migrate(.min)?.js", "/soledad/js/main.js", "/soledad/js/more-post.js", "/soledad/js/libs-script.min.js"], "icon_url": "", "type": "theme", "id": "theme:d3f78b26c2d11c99230171cc6378d06e", "is_default": 0, "created_at": 1679737664}, "3b85dd6e-9534-477e-9b15-940d0e155c8d": {"title": "Spacious - Mobile Menu", "condition": "spacious", "exclusions": ["\\/jquery(-migrate)?-?([0-9.]+)?(.min|.slim|.slim.min)?.js(\\?(.*))?( |'|\"|>|$)", "/spacious/js/navigation.js"], "icon_url": "", "type": "theme", "id": "theme:d423c1f002b10b8682ee24d616b19c9c", "is_default": 0, "created_at": 1703192854}, "d657dc56-5c04-439a-8987-401f89a65bf9": {"title": "Stockholm", "condition": "stockholm", "exclusions": ["/jquery-?[0-9.](.*)(.min|.slim|.slim.min)?.js", "/jquery-migrate(.min)?.js", "/stockholm/js/"], "icon_url": "", "type": "theme", "id": "theme:fcfff492e00727b63cf5dff9f59bc2a4", "is_default": 0, "created_at": 1679738378}, "121f9b1b-d3cd-4dde-915e-0b348abf6687": {"title": "Storefront", "condition": "storefront", "exclusions": ["/storefront/assets/js/navigation.min.js"], "icon_url": "", "type": "theme", "id": "theme:f0dca7e4eaedf573d4664be249845942", "is_default": 0, "created_at": 1679738793}, "41cfc83f-ff02-4a35-a3b7-e92db213b224": {"title": "StreamTube", "condition": "streamtube", "exclusions": ["/jquery-?[0-9.](.*)(.min|.slim|.slim.min)?.js", "/jquery-migrate(.min)?.js", "/streamtube/", "/streamtube-core/"], "icon_url": "", "type": "theme", "id": "theme:9772ccddd470688f6bc6aee86e34d29b", "is_default": 0, "created_at": 1679331431}, "c31366fe-9045-4767-a405-52a11e08b82e": {"title": "Sydney - Load elements on page load", "condition": "sydney-pro-ii", "exclusions": ["\\/jquery(-migrate)?-?([0-9.]+)?(.min|.slim|.slim.min)?.js(\\?(.*))?( |'|\"|>|$)", "/wp-content/themes/sydney-pro-ii/js/scripts.js", "/wp-content/themes/sydney-pro-ii/js/functions.min.js", "/wp-content/themes/sydney-pro-ii/js/elementor.js", "/wp-content/themes/sydney-pro-ii/js/hero-slider.js", "/wp-content/plugins/sydney-toolbox/js/main.js"], "icon_url": "", "type": "theme", "id": "theme:1fb15693856451537e331adeaf2c7d6f", "is_default": 0, "created_at": 1711977131}, "aec0a548-4c6b-400a-80ed-19a49e0faef0": {"title": "The7", "condition": "dt-the7", "exclusions": ["/jquery-?[0-9.](.*)(.min|.slim|.slim.min)?.js", "/wp-includes/js/jquery/jquery-migrate.min.js", "loader-removed", "/Ultimate_VC_Addons/assets/min-js/", "/dt-the7/", "/js_composer/"], "icon_url": "", "type": "theme", "id": "theme:7934c689fd20e30b6bfc69bb9d46cb63", "is_default": 0, "created_at": 1679737892}, "538354ff-d69f-40be-b0cc-df3790599dd2": {"title": "TheGem", "condition": "thegem", "exclusions": ["/jquery-?[0-9.](.*)(.min|.slim|.slim.min)?.js", "/jquery-migrate(.min)?.js", "/thegem/js/", "gemSettings", "thegemSlideshow", "tgpLazyItemsOptions"], "icon_url": "", "type": "theme", "id": "theme:153c54fe73897da838ce39152b1db5a8", "is_default": 0, "created_at": 1679738477}, "84d95a79-270c-4223-b459-bb49c6acfaf1": {"title": "Theme Electiman - Mobile Menu", "condition": "electiman", "exclusions": ["\\/jquery(-migrate)?-?([0-9.]+)?(.min|.slim|.slim.min)?.js(\\?(.*))?( |'|\"|>|$)", "/themes/electiman/assets/js/navigation.js", "/themes/electiman/assets/js/theme-pluginjs.js", "/themes/electiman/assets/js/theme.js", "/themes/electiman/assets/js/slick.min.js", "/themes/electiman/venobox/venobox.min.js", "/themes/electiman/assets/js/owl.carousel.min.js", "/wp-includes/js/imagesloaded.min.js"], "icon_url": "", "type": "theme", "id": "theme:a59c888391c869ed4f3417c02d71fe15", "is_default": 0, "created_at": 1702923332}, "3a59bf59-4fe5-4690-8ab7-33e6a976e2e3": {"title": "Thrive Theme Builder", "condition": "thrive-theme", "exclusions": ["/jquery-?[0-9.](.*)(.min|.slim|.slim.min)?.js", "/jquery-migrate(.min)?.js", "/thrive-visual-editor/editor/js/dist/modules/(.*).js", "TVE_Event_Manager_Registered_Callbacks", "ThriveGlobal", "TCB_Front", "TL_Front", "TVE_Ult", "thrive-", "thrive_", "tve_", "tve-"], "icon_url": "", "type": "theme", "id": "theme:7492fc8f8a90ad7ef680d9c560da2b0f", "is_default": 0, "created_at": 1710767440}, "68f2de3b-e2b8-4edf-b82f-93fd7834c65f": {"title": "Total", "condition": "Total", "exclusions": ["/jquery-?[0-9.](.*)(.min|.slim|.slim.min)?.js", "/jquery-migrate(.min)?.js", "/Total/assets/js/total.min.js"], "icon_url": "", "type": "theme", "id": "theme:96b0141273eabab320119c467cdcaf17", "is_default": 0, "created_at": 1679737571}, "6dc1cb35-6b50-4da0-9834-dddf169edaa6": {"id": "theme:7c37c885d7fecf788f635734f99e8610", "title": "<PERSON><PERSON><PERSON>", "type": "theme", "icon": "", "exclusions": ["/jquery-?[0-9.](.*)(.min|.slim|.slim.min)?.js", "/jquery-migrate(.min)?.js", "/townhub-add-ons/assets/js/(.*)", "/wp-includes/js/dist/vendor/react.js", "/wp-includes/js/dist/vendor/react-dom.js"], "is_default": 0, "condition": "townhub", "created_at": **********, "updated_at": "2023-02-15T04:32:17.000000Z", "icon_url": ""}, "f484f86b-e316-4871-9322-dee3925349fe": {"title": "Travel Monster - Owl Carousel", "condition": "travel-monster", "exclusions": ["\\/jquery(-migrate)?-?([0-9.]+)?(.min|.slim|.slim.min)?.js(\\?(.*))?( |'|\"|>)", "/wp-content/plugins/wp-travel-engine/assets/lib/owl-carousel(.*)/owl.carousel(|.min).js", "var isRtl"], "icon_url": "", "type": "theme", "id": "theme:d3e8da87b3affd399205438fbc8a4f05", "is_default": 0, "created_at": 1690822771}, "3ec96c3f-a6bf-4748-9b7e-78864bd24add": {"title": "uDesign - Mobile Menu", "condition": "u-design", "exclusions": ["\\/jquery(-migrate)?-?([0-9.]+)?(.min|.slim|.slim.min)?.js(\\?(.*))?( |'|\"|>|$)", "/wp-content/themes/u-design/assets/js/theme.min.js", "/wp-content/themes/u-design/framework/assets/js/framework.min.js", "/wp-content/themes/u-design/framework/assets/js/framework-async.min.js"], "icon_url": "", "type": "theme", "id": "theme:31ce70b0a02f8720a86d993816676943", "is_default": 0, "created_at": 1699292981}, "cb523239-27cc-461e-973d-c984a83223ac": {"title": "uDesign - Show Page Content on Load", "condition": "u-design", "exclusions": ["\\/jquery(-migrate)?-?([0-9.]+)?(.min|.slim|.slim.min)?.js(\\?(.*))?( |'|\"|>|$)", "/wp-content/themes/u-design/assets/js/theme.min.js", "/wp-content/themes/u-design/framework/assets/js/framework.min.js"], "icon_url": "", "type": "theme", "id": "theme:c55edd40ad3f9321da577dad70bb130c", "is_default": 0, "created_at": 1698778683}, "14cb0a85-8bee-491e-99d7-5f20a07f4bdd": {"title": "Uncode", "condition": "uncode", "exclusions": ["/jquery-?[0-9.](.*)(.min|.slim|.slim.min)?.js", "/jquery-migrate(.min)?.js", "/uncode/library/js/init(.min)?.js", "/uncode/library/js/plugins(.min)?.js", "/uncode/library/js/app(.min)?.js", "/uncode/library/js/woocommerce-uncode(.min)?.js", "/wp-includes/js/mediaelement/mediaelement-and-player.min.js", "initHeader", "initBox", "fixMenuHeight", "initRow"], "icon_url": "", "type": "theme", "id": "theme:18ba19b98aefbb6c0fde6c6bf92e9cfc", "is_default": 0, "created_at": 1679738154}, "96aa49eb-6372-4b4c-b70d-f29dede8a8f2": {"title": "Utouch - Load menu on page load", "condition": "utouch", "exclusions": ["/wp-includes/js/jquery/jquery-migrate.min.js", "/wp-includes/js/jquery/jquery.min.js", "/utouch/js/main.js", "/utouch/js/swiper.jquery.min.js", "/utouch/js/fitvids.js", "/utouch/js/theme-plugins.js", "/utouch/js/crum-mega-menu.js"], "icon_url": "", "type": "theme", "id": "theme:18284bf26abf49a1d5d60b3fb34e4c2d", "is_default": 0, "created_at": 1714156159}, "a1fbf155-720a-4704-9794-d6749ad6df59": {"title": "Vivo theme - Fix blank page", "condition": "vivo", "exclusions": ["\\/jquery(-migrate)?-?([0-9.]+)?(.min|.slim|.slim.min)?.js(\\?(.*))?( |'|\"|>)", "/vivo/framework/assets/js/bt_framework_misc.js"], "icon_url": "", "type": "theme", "id": "theme:a140e640fed504586e24e7c0df30376b", "is_default": 0, "created_at": 1704308662}, "5acb5d19-caa6-4deb-b7db-0051df4c3c3b": {"title": "Voxel Theme", "condition": "voxel", "exclusions": ["\\/jquery(-migrate)?-?([0-9.]+)?(.min|.slim|.slim.min)?.js(\\?(.*))?( |'|\"|>)", "/voxel/", "/elementor/", "/elementor-pro/", "/wp-includes/js/imagesloaded.min.js", "ElementorProFrontendConfig", "elementorFrontendConfig"], "icon_url": "", "type": "theme", "id": "theme:240a2be0a2b66b01f83abdcd83da7c7c", "is_default": 0, "created_at": 1720444241}, "88e96479-1aa2-4adc-8f07-20bc0368a63f": {"title": "Werkstatt", "condition": "werkstatt", "exclusions": ["/jquery-?[0-9.](.*)(.min|.slim|.slim.min)?.js", "/jquery-migrate(.min)?.js", "/js/underscore.min.js", "/werkstatt/assets/js/vendor.min.js", "/werkstatt/assets/js/fullscreen.min.js", "/werkstatt/assets/js/app.min.js"], "icon_url": "", "type": "theme", "id": "theme:38faa29db5a07b8fef6aee9cc11cafec", "is_default": 0, "created_at": 1679737639}, "60c4110b-a960-4d44-b619-6d79514dbf75": {"title": "Woodmart", "condition": "woodmart", "exclusions": ["/jquery-?[0-9.](.*)(.min|.slim|.slim.min)?.js", "/jquery-migrate(.min)?.js", "/wp-includes/js/imagesloaded.min.js", "/woodmart/js/scripts/wc/", "/woodmart/js/scripts/global/", "/woodmart/js/libs/owl.carousel.min.js", "/woodmart/js/libs/owl.carousel.js", "/woodmart/js/libs/slick.js", "/woodmart/js/libs/autocomplete.min.js"], "icon_url": "", "type": "theme", "id": "theme:06338f13cb89e5309ad2eb7e4d457be4", "is_default": 0, "created_at": 1695633901}, "a4547b5b-10ab-407c-969c-269fddec07b8": {"title": "Woodmart - Cart Fragments", "condition": "woodmart", "exclusions": ["\\/jquery(-migrate)?-?([0-9.]+)?(.min|.slim|.slim.min)?.js(\\?(.*))?( |'|\"|>)", "/woocommerce/assets/js/frontend/cart-fragments.min.js", "/woocommerce/assets/js/js-cookie/js.cookie.min.js", "/woodmart/js/scripts/wc/updateCartFragmentsFix.js"], "icon_url": "", "type": "theme", "id": "theme:591f4f1b2e86b1e987cd8789df3ffce3", "is_default": 0, "created_at": 1700584689}, "97066e39-027a-4cd6-9152-7b6b53f365f5": {"title": "Woodmart - Mobile Menu", "condition": "woodmart", "exclusions": ["\\/jquery(-migrate)?-?([0-9.]+)?(.min|.slim|.slim.min)?.js(\\?(.*))?( |'|\"|>|$)", "/themes/woodmart/js/scripts/menu/mobileNavigation.min.js", "/themes/woodmart/js/scripts/global/helpers.min.js"], "icon_url": "", "type": "theme", "id": "theme:d79a3941e2f12fb93ffc980ebeb1d7f4", "is_default": 0, "created_at": 1711745013}, "119ebd1c-6b46-4f07-8d6a-3498d9c8814f": {"title": "XStore", "condition": "xstore", "exclusions": ["/jquery-?[0-9.](.*)(.min|.slim|.slim.min)?.js", "/jquery-migrate(.min)?.js", "/xstore/js/", "/et-core-plugin/packages/st-woo-swatches/public/js/frontend.min.js"], "icon_url": "", "type": "theme", "id": "theme:3de9d9ba385200548f177d9c704ae92a", "is_default": 0, "created_at": 1679738190}, "9a7a548c-07a1-4dff-93fc-6e8230b67853": {"id": "theme:119d329456073aa10969d7cbd9760f28", "title": "YOOtheme Pro", "type": "theme", "icon": "", "exclusions": ["/jquery-?[0-9.](.*)(.min|.slim|.slim.min)?.js", "/jquery-migrate(.min)?.js", "uikit.min.js"], "is_default": 0, "condition": "yootheme", "created_at": **********, "updated_at": "2023-02-15T04:32:17.000000Z", "icon_url": ""}, "f74c499e-b7d9-4590-8671-379f51f468c8": {"title": "<PERSON><PERSON>", "condition": "zeen", "exclusions": ["/jquery-?[0-9.](.*)(.min|.slim|.slim.min)?.js", "/jquery-migrate(.min)?.js", "/zeen/assets/js/", "/js.cookie.min.js"], "icon_url": "", "type": "theme", "id": "theme:acb52844f996627788836366404a3245", "is_default": 0, "created_at": 1679737859}}, "scripts": {"dd0c5a5b-ec56-49f9-9aa4-89e1e3a6a28a": {"title": "Amazon Ads", "exclusions": ["amazon-adsystem.com"], "icon_url": "", "type": "script", "id": "script:b82a5936d8ea0745016caeb71629ae5d", "is_default": 0, "created_at": 1681390276}, "980edf32-c64b-4370-bf23-c62b079e71c3": {"title": "Google AdSense", "exclusions": ["adsbygoogle"], "icon_url": "", "type": "script", "id": "script:0206e6040c8ff64b8d6ee5fef2ce1c90", "is_default": 0, "created_at": 1681377840}, "2499bb90-0753-4b2b-9bd4-1525f94c7437": {"title": "Google Analytics", "exclusions": ["google-analytics.com/analytics.js", "ga\\( '", "ga\\('"], "icon_url": "", "type": "script", "id": "script:d86cf69a8b82547a94ca3f6a307cf9a6", "is_default": 0, "created_at": 1681388311}, "6f460036-3106-4b8c-9951-d32de9b1258f": {"title": "Google Maps", "exclusions": ["maps.googleapis.com", "maps.google.com"], "icon_url": "", "type": "script", "id": "script:4d60ab2c6d11d753267484006c23e54c", "is_default": 0, "created_at": 1681390259}, "ac2d5720-9418-468c-80a0-3874ee743c0f": {"title": "Google Optimize", "exclusions": ["a,s,y,n,c,h,i,d,e", "googleoptimize.com/optimize.js", "async-hide"], "icon_url": "", "type": "script", "id": "script:031a0cece38c4739df67f910dcabf1bd", "is_default": 0, "created_at": 1681390261}, "122e6ebb-51fd-477f-97fb-559593f1a48b": {"title": "Google Recaptcha", "exclusions": ["recaptcha"], "icon_url": "", "type": "script", "id": "script:032cb16577cbf07bc7c02bac83bd936d", "is_default": 0, "created_at": 1681390264}, "219277ae-b2ac-4d42-913d-eaea40985295": {"title": "Google Tag Manager", "exclusions": ["/gtag/js", "gtag\\(", "/gtm.js", "async-hide"], "icon_url": "", "type": "script", "id": "script:1d3c65b2b03ef35e14df6b163ea3a1f6", "is_default": 0, "created_at": 1681390266}, "f632e3f4-20e6-471e-a78d-86afbea63586": {"title": "HubSpot", "exclusions": ["/jquery-?[0-9.](.*)(.min|.slim|.slim.min)?.js", "/jquery-migrate(.min)?.js", "js(.*).hsforms.net", "hbspt.forms.create"], "icon_url": "", "type": "script", "id": "script:de4bd8ef4675ebb85a055955de76d0ee", "is_default": 0, "created_at": 1713282413}, "5d606add-ffb8-4a06-b295-5f722710fbfd": {"title": "<PERSON><PERSON>", "exclusions": ["widget.refari.co", "refari"], "icon_url": "", "type": "script", "id": "script:a705e197b13b47e72a105c923e044358", "is_default": 0, "created_at": 1683797056}, "742ec14a-27a1-4789-b9c8-a9c3a3cf7042": {"title": "Reviews.io", "exclusions": ["/carousel-inline-iframeless/dist.js", "carouselInlineWidget"], "icon_url": "", "type": "script", "id": "script:4df445c576f45889506ba175a4c39fdc", "is_default": 0, "created_at": 1684389426}, "05d3eb78-f574-49be-95e1-3f11714005d1": {"id": "script:ce7566d1d08cc094b74cf283cf9c56a5", "title": "Stripe", "type": "script", "icon": "", "exclusions": ["js.stripe.com"], "is_default": 0, "condition": "", "created_at": **********, "updated_at": "2023-02-15T04:32:16.000000Z", "icon_url": ""}, "0a37e528-8718-49f7-a26d-059aa29f867d": {"title": "Trustindex", "exclusions": ["cdn.trustindex.io/loader.js", "cdn.trustindex.io/loader-cert.js"], "icon_url": "", "type": "script", "id": "script:1d38d6195597e8bb81966870f0a4f939", "is_default": 0, "created_at": 1713359486}, "ce9a3865-9efa-4e98-ae10-4f842a4ecc22": {"title": "Typeform", "exclusions": ["/next/embed.js"], "icon_url": "", "type": "script", "id": "script:cd3889ae3b96f891186ae270dbbcc9bb", "is_default": 0, "created_at": 1709838856}, "b56bf06c-2f8c-4757-b536-a689fb0e75f9": {"title": "Typekit", "exclusions": ["typekit"], "icon_url": "", "type": "script", "id": "script:7815e38b93e3b500a632681bd594bd61", "is_default": 0, "created_at": 1681390268}, "49c38c0a-43b9-4237-88cb-57ddd519f0ad": {"title": "Venatus Media", "exclusions": ["/ad-manager.min.js", "__vm_add"], "icon_url": "", "type": "script", "id": "script:abe11528732aed9a19a97e73b242faa5", "is_default": 0, "created_at": 1681390272}, "2c1d0998-8ab5-478c-8eb9-9e375b46363e": {"title": "Wistia", "exclusions": ["fast.wistia.com", "/assets/external/E-v1.js"], "icon_url": "", "type": "script", "id": "script:9a0111f8c3186c1cb3113587c660c041", "is_default": 0, "created_at": 1711395219}, "0627fe24-7e9d-400f-b064-d98bec2ba85e": {"id": "script:dbd1875130c71eb4b2ef768ad18d820c", "title": "Yandex Ads", "type": "script", "icon": "", "exclusions": ["yandex.ru", "window.yaContextCb"], "is_default": 0, "condition": "", "created_at": **********, "updated_at": "2023-02-15T04:32:16.000000Z", "icon_url": ""}}}