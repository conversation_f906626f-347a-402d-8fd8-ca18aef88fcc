# Copyright (C) 2024 WP Media
# This file is distributed under the same license as the WP Rocket plugin.
# Translators:
# <AUTHOR> <EMAIL>, 2019
# <AUTHOR> <EMAIL>, 2023
# <PERSON> <<EMAIL>>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: WP Rocket 3.16-alpha1\n"
"Report-Msgid-Bugs-To: https://wordpress.org/support/plugin/wp-rocket\n"
"POT-Creation-Date: 2024-04-29T20:40:45+00:00\n"
"PO-Revision-Date: 2019-08-26 15:14+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2024\n"
"Language-Team: Romanian (Romania) (https://app.transifex.com/wp-media/teams/18133/ro_RO/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: ro_RO\n"
"Plural-Forms: nplurals=3; plural=(n==1?0:(((n%100>19)||((n%100==0)&&(n!=0)))?2:1));\n"
"X-Domain: rocket\n"
"X-Generator: WP-CLI 2.4.0\n"

#. Plugin Name of the plugin
msgid "WP Rocket"
msgstr "WP Rocket"

#. Plugin URI of the plugin
msgid "https://wp-rocket.me"
msgstr "https://wp-rocket.me"

#. Description of the plugin
msgid "The best WordPress performance plugin."
msgstr "Cel mai bun modul Wordpress pentru performanță."

#. Author of the plugin
msgid "WP Media"
msgstr "WP Media"

#. Author URI of the plugin
msgid "https://wp-media.me"
msgstr "https://wp-media.me"

#. Translators: %s = Hosting name.
#: inc/3rd-party/hosting/flywheel.php:18
#: inc/3rd-party/hosting/wp-serveur.php:27 inc/deprecated/3.6.php:697
#: inc/deprecated/3.6.php:997 inc/deprecated/3.9.php:22
#: inc/ThirdParty/Hostings/Cloudways.php:90
#: inc/ThirdParty/Hostings/Dreampress.php:44
#: inc/ThirdParty/Hostings/Godaddy.php:63
#: inc/ThirdParty/Hostings/O2Switch.php:49
#: inc/ThirdParty/Hostings/OneCom.php:137
#: inc/ThirdParty/Hostings/Pressidium.php:49
#: inc/ThirdParty/Hostings/ProIsp.php:51 inc/ThirdParty/Hostings/Savvii.php:50
#: inc/ThirdParty/Hostings/WPEngine.php:47
#: inc/ThirdParty/Hostings/WPXCloud.php:51
msgid ""
"Your site is hosted on %s, we have enabled Varnish auto-purge for "
"compatibility."
msgstr ""
"Site-ul tău este găzduit pe %s, am activat ștergerea automată Varnish pentru"
" compatibilitate."

#: inc/Addon/Cloudflare/API/Client.php:129
msgid "Cloudflare did not provide any reply. Please try again later."
msgstr "Cloudflare nu a dat niciun răspuns. Te rog reîncearcă mai târziu."

#: inc/Addon/Cloudflare/API/Client.php:135
msgid "Cloudflare unexpected response"
msgstr "Răspuns neașteptat de la Cloudflare"

#: inc/Addon/Cloudflare/API/Client.php:143
msgid "Missing Cloudflare result."
msgstr "Lipsește rezultatul Cloudflare."

#: inc/Addon/Cloudflare/API/Client.php:194 inc/deprecated/3.5.php:112
#: inc/deprecated/3.5.php:169
msgid "Incorrect Cloudflare email address or API key."
msgstr "Adresă email sau cheie API Cloudflare incorectă."

#. translators: %1$s = opening link; %2$s = closing link
#: inc/Addon/Cloudflare/API/Client.php:198
#: inc/Addon/Cloudflare/API/Client.php:212
#: inc/Addon/Cloudflare/Cloudflare.php:75
#: inc/Addon/Cloudflare/Cloudflare.php:108
#: inc/classes/subscriber/Tools/class-detect-missing-tags-subscriber.php:157
#: inc/deprecated/3.5.php:92 inc/deprecated/3.5.php:116
#: inc/deprecated/3.5.php:129 inc/deprecated/3.5.php:157
#: inc/deprecated/3.5.php:173
msgid "Read the %1$sdocumentation%2$s for further guidance."
msgstr "Citește %1$sdocumentația%2$s pentru îndrumări suplimentare."

#. translators: Documentation exists in EN, FR; use localized URL if
#. applicable.
#: inc/Addon/Cloudflare/API/Client.php:200
#: inc/Addon/Cloudflare/API/Client.php:214
#: inc/Addon/Cloudflare/Auth/APIKey.php:63
#: inc/Addon/Cloudflare/Cloudflare.php:77
#: inc/Addon/Cloudflare/Cloudflare.php:110 inc/deprecated/3.5.php:81
#: inc/deprecated/3.5.php:94 inc/deprecated/3.5.php:118
#: inc/deprecated/3.5.php:131 inc/deprecated/3.5.php:159
#: inc/deprecated/3.5.php:175 inc/deprecated/3.5.php:208
msgid ""
"https://docs.wp-rocket.me/article/18-using-wp-rocket-with-"
"cloudflare/?utm_source=wp_plugin&utm_medium=wp_rocket#add-on"
msgstr ""
"https://docs.wp-rocket.me/article/18-using-wp-rocket-with-"
"cloudflare/?utm_source=wp_plugin&utm_medium=wp_rocket#add-on"

#: inc/Addon/Cloudflare/API/Client.php:208 inc/deprecated/3.5.php:125
msgid "Incorrect Cloudflare Zone ID."
msgstr "ID zonă Cloudflare incorect."

#. translators: %1$s = opening link; %2$s = closing link
#: inc/Addon/Cloudflare/Auth/APIKey.php:61
msgid ""
"Cloudflare email and/or API key are not set. Read the %1$sdocumentation%2$s "
"for further guidance."
msgstr ""
"Emailul și/sau cheia API Cloudflare nu sunt setate. Pentru îndrumări "
"suplimentare, citește %1$sdocumentația%2$s."

#: inc/Addon/Cloudflare/Cloudflare.php:71 inc/deprecated/3.5.php:88
msgid "Missing Cloudflare Zone ID."
msgstr "Lipsește ID-ul zonei Cloudflare."

#: inc/Addon/Cloudflare/Cloudflare.php:104 inc/deprecated/3.5.php:153
msgid "It looks like your domain is not set up on Cloudflare."
msgstr "Se pare că domeniul tău nu este configurat pe Cloudflare."

#: inc/Addon/Cloudflare/Cloudflare.php:200
msgid "days"
msgstr "zile"

#: inc/Addon/Cloudflare/Cloudflare.php:203
msgid "seconds"
msgstr "secunde"

#: inc/Addon/Cloudflare/Cloudflare.php:205
msgid "minutes"
msgstr "minute"

#: inc/Addon/Cloudflare/Cloudflare.php:207
msgid "hours"
msgstr "ore"

#. translators: %1$s = <strong>, %2$s = </strong>, %3$s = CloudFare API return
#. message.
#: inc/Addon/Cloudflare/Subscriber.php:232
#: inc/Addon/Cloudflare/Subscriber.php:261
msgid "%1$sWP Rocket:%2$s %3$s"
msgstr "%1$sWP Rocket:%2$s %3$s"

#. translators: %1$s = <strong>, %2$s = </strong>.
#: inc/Addon/Cloudflare/Subscriber.php:250
msgid "%1$sWP Rocket:%2$s Cloudflare cache successfully purged."
msgstr "%1$sWP Rocket:%2$s cache-ul Cloudflare a fost șters cu succes."

#. translators: %s is the message returned by the CloudFlare API.
#: inc/Addon/Cloudflare/Subscriber.php:311
msgid "Cloudflare development mode error: %s"
msgstr "Eroare mod dezvoltare Cloudflare: %s"

#. translators: %s is the message returned by the CloudFlare API.
#: inc/Addon/Cloudflare/Subscriber.php:318
msgid "Cloudflare development mode %s"
msgstr "Mod dezvoltare Cloudflare %s"

#. translators: %s is the message returned by the CloudFlare API.
#: inc/Addon/Cloudflare/Subscriber.php:337
msgid "Cloudflare cache level error: %s"
msgstr "Eroare nivel de cache Cloudflare: %s"

#: inc/Addon/Cloudflare/Subscriber.php:344
msgctxt "Cloudflare caching level"
msgid "standard"
msgstr "standard"

#. translators: %s is the caching level returned by the CloudFlare API.
#: inc/Addon/Cloudflare/Subscriber.php:350
msgid "Cloudflare cache level set to %s"
msgstr "Nivel de cache Cloudflare setat la %s"

#. translators: %s is the message returned by the CloudFlare API.
#: inc/Addon/Cloudflare/Subscriber.php:368
msgid "Cloudflare minification error: %s"
msgstr "Eroare de minificare Cloudflare: %s"

#. translators: %s is the message returned by the CloudFlare API.
#: inc/Addon/Cloudflare/Subscriber.php:375
msgid "Cloudflare minification %s"
msgstr "Minificare Cloudflare %s"

#. translators: %s is the message returned by the CloudFlare API.
#: inc/Addon/Cloudflare/Subscriber.php:393
msgid "Cloudflare rocket loader error: %s"
msgstr "Eroare rocket loader Cloudflare: %s"

#. translators: %s is the message returned by the CloudFlare API.
#: inc/Addon/Cloudflare/Subscriber.php:400
msgid "Cloudflare rocket loader %s"
msgstr "Rocket loader Cloudflare %s"

#. translators: %s is the message returned by the CloudFlare API.
#: inc/Addon/Cloudflare/Subscriber.php:418
msgid "Cloudflare browser cache error: %s"
msgstr "Eroare cache navigator Cloudflare: %s"

#. translators: %s is the message returned by the CloudFlare API.
#: inc/Addon/Cloudflare/Subscriber.php:425
msgid "Cloudflare browser cache set to %s"
msgstr "Cache-ul navigatorului Cloudflare este setat la %s"

#. translators: %1$s = strong opening tag, %2$s = strong closing tag.
#: inc/Addon/Cloudflare/Subscriber.php:536
msgid "%1$sWP Rocket:%2$s Optimal settings activated for Cloudflare:"
msgstr "%1$sWP Rocket:%2$s setările optime sunt activate pentru Cloudflare:"

#. translators: %1$s = strong opening tag, %2$s = strong closing tag.
#: inc/Addon/Cloudflare/Subscriber.php:545
msgid ""
"%1$sWP Rocket:%2$s Optimal settings deactivated for Cloudflare, reverted to "
"previous settings:"
msgstr ""
"%1$sWP Rocket:%2$s setările optime sunt dezactivate pentru Cloudflare, am "
"revenit la setările anterioare:"

#: inc/Addon/Cloudflare/Subscriber.php:661 inc/admin/options.php:165
#: inc/classes/subscriber/Tools/class-detect-missing-tags-subscriber.php:148
msgid "WP Rocket: "
msgstr "WP Rocket: "

#. translators: %s is the error message returned by the API.
#: inc/Addon/Sucuri/Subscriber.php:96
msgid "Sucuri cache purge error: %s"
msgstr "Eroare ștergere cache Sucuri: %s"

#: inc/Addon/Sucuri/Subscriber.php:101
msgid ""
"The Sucuri cache is being cleared. Note that it may take up to two minutes "
"for it to be fully flushed."
msgstr ""
"Cache-ul Sucuri este șters acum. Reține că s-ar putea să dureze până la două"
" minute pentru a fi șters complet."

#: inc/Addon/Sucuri/Subscriber.php:218
msgid "Sucuri firewall API key was not found."
msgstr "Cheia API pentru firewall-ul Sucuri nu a fost găsită."

#: inc/Addon/Sucuri/Subscriber.php:231
msgid "Sucuri firewall API key is invalid."
msgstr "Cheia API pentru firewall-ul Sucuri este invalidă."

#. translators: %s is an error message.
#: inc/Addon/Sucuri/Subscriber.php:286
msgid "Error when contacting Sucuri firewall API. Error message was: %s"
msgstr ""
"Eroare la stabilirea contactului cu API-ul firewall-ului Sucuri. Mesajul de "
"eroare a fost: %s"

#: inc/Addon/Sucuri/Subscriber.php:301
msgid "Could not get a response from the Sucuri firewall API."
msgstr "Nu am primit un răspuns de la API-ului firewall-ului Sucuri."

#: inc/Addon/Sucuri/Subscriber.php:316
msgid "Got an invalid response from the Sucuri firewall API."
msgstr "Am primit un răspuns invalid de la API-ului firewall-ului Sucuri."

#: inc/Addon/Sucuri/Subscriber.php:330
msgid "The Sucuri firewall API returned an unknown error."
msgstr "API-ului firewall-ului Sucuri a returnat o eroare necunoscută."

#. translators: %s is an error message.
#: inc/Addon/Sucuri/Subscriber.php:334
msgid "The Sucuri firewall API returned the following error: %s"
msgid_plural "The Sucuri firewall API returned the following errors: %s"
msgstr[0] "API-ului firewall-ului Sucuri a returnat următoarea eroare: %s"
msgstr[1] "API-ului firewall-ului Sucuri a returnat următoarele erori: %s"
msgstr[2] "API-ului firewall-ului Sucuri a returnat următoarele erori: %s"

#. Translators: %1$s = plugin name(s), %2$s = opening <a> tag, %3$s = closing
#. </a> tag.
#: inc/Addon/WebP/AdminSubscriber.php:93
msgid ""
"You are using %1$s to serve WebP images so you do not need to enable this "
"option. %2$sMore info%3$s %4$s If you prefer to have WP Rocket serve WebP "
"for you instead, please disable WebP display in %1$s."
msgid_plural ""
"You are using %1$s to serve WebP images so you do not need to enable this "
"option. %2$sMore info%3$s %4$s If you prefer to have WP Rocket serve WebP "
"for you instead, please disable WebP display in %1$s."
msgstr[0] ""
"Folosești %1$s pentru a servi imagini WebP, deci nu trebuie să activezi "
"această opțiune. %2$sMai multe informații%3$s %4$s Dacă, în schimb, preferi "
"ca WP Rocket să-ți servească imaginile WebP, te rog dezactivează afișarea "
"WebP în %1$s."
msgstr[1] ""
"Folosești %1$s pentru a servi imagini WebP, deci nu trebuie să activezi "
"această opțiune. %2$sMai multe informații%3$s %4$s Dacă, în schimb, preferi "
"ca WP Rocket să-ți servească imaginile WebP, te rog dezactivează afișarea "
"WebP în %1$s."
msgstr[2] ""
"Folosești %1$s pentru a servi imagini WebP, deci nu trebuie să activezi "
"această opțiune. %2$sMai multe informații%3$s %4$s Dacă, în schimb, preferi "
"ca WP Rocket să-ți servească imaginile WebP, te rog dezactivează afișarea "
"WebP în %1$s."

#: inc/Addon/WebP/AdminSubscriber.php:105
msgid "WebP cache is disabled by filter."
msgstr "Cache-ul WebP este dezactivat printr-un filtru."

#. Translators: %1$s = plugin name(s), %2$s = opening <a> tag, %3$s = closing
#. </a> tag.
#: inc/Addon/WebP/AdminSubscriber.php:115
#: inc/Addon/WebP/AdminSubscriber.php:141
msgid ""
"You are using %1$s to convert images to WebP. If you want WP Rocket to serve"
" them for you, activate this option. %2$sMore info%3$s"
msgid_plural ""
"You are using %1$s to convert images to WebP. If you want WP Rocket to serve"
" them for you, activate this option. %2$sMore info%3$s"
msgstr[0] ""
"Folosești %1$s pentru a converti imaginile în WebP. Dacă vrei ca WP Rocket "
"să le servească pentru tine, activează această opțiune. %2$sMai multe "
"informații%3$s"
msgstr[1] ""
"Folosești %1$s pentru a converti imaginile în WebP. Dacă vrei ca WP Rocket "
"să le servească pentru tine, activează această opțiune. %2$sMai multe "
"informații%3$s"
msgstr[2] ""
"Folosești %1$s pentru a converti imaginile în WebP. Dacă vrei ca WP Rocket "
"să le servească pentru tine, activează această opțiune. %2$sMai multe "
"informații%3$s"

#. Translators: %1$s = plugin name(s), %2$s = opening <a> tag, %3$s = closing
#. </a> tag.
#: inc/Addon/WebP/AdminSubscriber.php:127
#: inc/Addon/WebP/AdminSubscriber.php:153
msgid ""
"You are using %1$s to convert images to WebP. WP Rocket will create separate"
" cache files to serve your WebP images. %2$sMore info%3$s"
msgid_plural ""
"You are using %1$s to convert images to WebP. WP Rocket will create separate"
" cache files to serve your WebP images. %2$sMore info%3$s"
msgstr[0] ""
"Folosești %1$s pentru a converti imaginile în WebP. WP Rocket va crea "
"fișiere cache separate pentru a-ți servi imaginile WebP. %2$sMai multe "
"informații%3$s"
msgstr[1] ""
"Folosești %1$s pentru a converti imaginile în WebP. WP Rocket va crea "
"fișiere cache separate pentru a-ți servi imaginile WebP. %2$sMai multe "
"informații%3$s"
msgstr[2] ""
"Folosești %1$s pentru a converti imaginile în WebP. WP Rocket va crea "
"fișiere cache separate pentru a-ți servi imaginile WebP. %2$sMai multe "
"informații%3$s"

#. Translators: %1$s = opening <a> tag, %2$s = closing </a> tag.
#: inc/Addon/WebP/AdminSubscriber.php:173
msgid ""
"%5$sWe have not detected any compatible WebP plugin!%6$s%4$s If you don’t "
"already have WebP images on your site consider using %3$sImagify%2$s or "
"another supported plugin. %1$sMore info%2$s %4$s If you are not using WebP "
"do not enable this option."
msgstr ""
"%5$sNu am detectat niciun modul care este compatibil cu WebP!%6$s%4$s Dacă "
"nu servești deja imagini WebP pe site-ul tău, poate ar trebui să folosești "
"%3$sImagify%2$s sau un alt modul. %1$sMai multe informații%2$s %4$s Dacă nu "
"vrei să folosești WebP, nu activa această opțiune."

#: inc/Addon/WebP/AdminSubscriber.php:185
msgid "WP Rocket will create separate cache files to serve your WebP images."
msgstr ""
"WP Rocket va crea separat fișiere cache pentru a-ți servi imaginile WebP."

#: inc/admin/admin.php:18 inc/common/admin-bar.php:273
#: inc/deprecated/deprecated.php:1787
msgid "Support"
msgstr "Suport"

#: inc/admin/admin.php:20
msgid "Docs"
msgstr "Documentații"

#: inc/admin/admin.php:22 inc/common/admin-bar.php:261
msgid "FAQ"
msgstr "Întrebări frecvente"

#: inc/admin/admin.php:24 inc/common/admin-bar.php:70
msgid "Settings"
msgstr "Setări"

#: inc/admin/admin.php:96 inc/admin/admin.php:117 inc/deprecated/3.5.php:898
#: inc/Engine/Cache/AdminSubscriber.php:134
msgid "Clear this cache"
msgstr "Șterge acest cache"

#: inc/admin/admin.php:402
msgid "Settings import failed: you do not have the permissions to do this."
msgstr "Importul setărilor a eșuat: nu ai permisiunea de a face asta."

#: inc/admin/admin.php:406
msgid "Settings import failed: no file uploaded."
msgstr "Importul setărilor a eșuat: niciun fișier încărcat."

#: inc/admin/admin.php:410
msgid "Settings import failed: incorrect filename."
msgstr "Importul setărilor a eșuat: nume fișier incorect."

#: inc/admin/admin.php:421
msgid "Settings import failed: incorrect filetype."
msgstr "Importul setărilor a eșuat: tip de fișier incorect."

#: inc/admin/admin.php:431
msgid "Settings import failed: "
msgstr "Importul setărilor a eșuat: "

#: inc/admin/admin.php:447
msgid "Settings import failed: unexpected file content."
msgstr "Importul setărilor a eșuat: conținut neașteptat pentru fișier."

#: inc/admin/admin.php:492
msgid "Settings imported and saved."
msgstr "Setările au fost importate și salvate."

#: inc/admin/options.php:102 inc/Engine/Admin/Settings/Page.php:571
msgid "Excluded CSS Files"
msgstr "Fișiere CSS excluse"

#: inc/admin/options.php:103 inc/Engine/Admin/Settings/Page.php:725
msgid "Excluded Inline JavaScript"
msgstr "JavaScript în-linie exclus"

#: inc/admin/options.php:104 inc/Engine/Admin/Settings/Page.php:743
#: inc/Engine/Admin/Settings/Page.php:776
#: inc/Engine/Admin/Settings/Page.php:823
msgid "Excluded JavaScript Files"
msgstr "Fișiere JavaScript excluse"

#: inc/admin/options.php:105
msgid "Defer JavaScript Files"
msgstr "Fișiere Întârzie JavaScript"

#: inc/admin/options.php:106
msgid "Excluded Delay JavaScript Files"
msgstr "Fișiere Întârzie JavaScript excluse"

#: inc/admin/options.php:107 inc/Engine/Admin/Settings/Page.php:1229
msgid "Never Cache URL(s)"
msgstr "Nu memora niciodată în cache URL-uri"

#: inc/admin/options.php:108 inc/Engine/Admin/Settings/Page.php:1243
msgid "Never Cache User Agent(s)"
msgstr "Nu memora niciodată în cache agentul (agenții) utilizator"

#: inc/admin/options.php:109 inc/Engine/Admin/Settings/Page.php:1249
msgid "Always Purge URL(s)"
msgstr "Curăță întotdeauna URL-ul (URL-urile)"

#: inc/admin/options.php:110 inc/Engine/Admin/Settings/Page.php:1530
msgid "Exclude files from CDN"
msgstr "Exclude fișiere din CDN"

#. translators: 1 and 2 can be anything.
#: inc/admin/options.php:131
msgid "%1$s: <em>%2$s</em>"
msgstr "%1$s: <em>%2$s</em>"

#: inc/admin/options.php:141
msgid "The following pattern is invalid and has been removed:"
msgid_plural "The following patterns are invalid and have been removed:"
msgstr[0] "Următorul model este invalid și a fost înlăturat:"
msgstr[1] "Următoarele modele sunt invalide și au fost înlăturate:"
msgstr[2] "Următoarele modele sunt invalide și au fost înlăturate:"

#: inc/admin/options.php:157
msgid "More info"
msgstr "Mai multe informații"

#: inc/admin/ui/meta-boxes.php:37 inc/admin/ui/notices.php:671
#: inc/common/admin-bar.php:91 inc/common/admin-bar.php:154
#: views/settings/page-sections/dashboard.php:150
msgid "Clear cache"
msgstr "Șterge cache"

#. translators: %1$s WP Rocket plugin name; %2$s = file name.
#: inc/admin/ui/notices.php:30 inc/admin/ui/notices.php:43
msgid ""
"<strong>%1$s</strong> has not been deactivated due to missing writing permissions.<br>\n"
"Make <strong>%2$s</strong> writeable and retry deactivation, or force deactivation now:"
msgstr ""
"<strong>%1$s</strong> nu a fost dezactivat deoarece lipsesc permisiunile de scriere.<br>\n"
"Fă ca <strong>%2$s</strong> să poată fi scris și reîncearcă dezactivarea sau forțează dezactivarea acum:"

#. translators: %s is WP Rocket plugin name.
#: inc/admin/ui/notices.php:97
msgid ""
"<strong>%s</strong>: One or more plugins have been enabled or disabled, "
"clear the cache if they affect the front end of your site."
msgstr ""
"<strong>%s</strong>: unul sau mai multe module au fost activate sau "
"dezactivate, șterge cache-ul dacă ele afectează partea din față a site-ului."

#. translators: %s is WP Rocket plugin name.
#: inc/admin/ui/notices.php:141
msgid ""
"<strong>%s</strong>: The following plugins are not compatible with this "
"plugin and may cause unexpected results:"
msgstr ""
"<strong>%s</strong>: următoarele module nu sunt compatibile cu acest modul "
"și pot duce la rezultate neașteptate:"

#: inc/admin/ui/notices.php:147
msgid "Deactivate"
msgstr "Dezactivează"

#: inc/admin/ui/notices.php:189
msgid ""
"WP Rocket Footer JS is not an official add-on. It prevents some options in "
"WP Rocket from working correctly. Please deactivate it if you have problems."
msgstr ""
"WP Rocket Footer JS nu este un supliment oficial. El împiedică unele opțiuni"
" din WP Rocket să funcționeze corect. Te rog dezactivează-l dacă ai "
"probleme."

#. translators: %1$s = opening link tag, %2$s = closing link tag.
#: inc/admin/ui/notices.php:229
msgid ""
"Endurance Cache is currently enabled, which will conflict with WP Rocket "
"Cache. Please set the Endurance Cache cache level to Off (Level 0) on the "
"%1$sSettings > General%2$s page to prevent any issues."
msgstr ""
"În prezent, Endurance Cache este activat și va intra în conflict cu cache-ul"
" WP Rocket. Te rog setează nivelul de cache la Oprit (nivel 0) pentru "
"Endurance Cache în pagina %1$sSetări > Generale%2$s pentru a nu avea "
"probleme."

#. translators: %1$s WP Rocket plugin name; %2$s = opening link; %3$s =
#. closing link
#: inc/admin/ui/notices.php:250
msgid ""
"%1$s: A custom permalink structure is required for the plugin to work "
"properly. %2$sGo to permalinks settings%3$s"
msgstr ""
"%1$s: pentru ca modulul să funcționeze cum trebuie, este necesară o "
"structură personalizată pentru legăturile permanente. %2$sDu-te la setări "
"Legături permanente%3$s"

#. translators: %s = plugin name.
#: inc/admin/ui/notices.php:297
msgid ""
"%s could not modify the .htaccess file due to missing writing permissions."
msgstr ""
"%s nu a putut modifica fișierul .htaccess deoarece lipsesc permisiunile de "
"scriere."

#. translators: This is a doc title! %1$s = opening link; %2$s = closing link
#: inc/admin/ui/notices.php:303 inc/admin/ui/notices.php:788
msgid "Troubleshoot: %1$sHow to make system files writeable%2$s"
msgstr ""
"Depanare: %1$scum să faci ca fișierele de sistem să poată fi scrise%2$s"

#. translators: Documentation exists in EN, DE, FR, ES, IT; use loaclised URL
#. if applicable
#: inc/admin/ui/notices.php:305 inc/admin/ui/notices.php:790
msgid ""
"https://docs.wp-rocket.me/article/626-how-to-make-system-files-htaccess-wp-"
"config-writeable/?utm_source=wp_plugin&utm_medium=wp_rocket"
msgstr ""
"https://docs.wp-rocket.me/article/626-how-to-make-system-files-htaccess-wp-"
"config-writeable/?utm_source=wp_plugin&utm_medium=wp_rocket"

#: inc/admin/ui/notices.php:311
msgid ""
"Don’t worry, WP Rocket’s page caching and settings will still function "
"correctly."
msgstr ""
"Nu-ți face griji, cache-ul paginilor și setările WP Rocket vor funcționa "
"corect."

#: inc/admin/ui/notices.php:311
msgid ""
"For optimal performance, adding the following lines into your .htaccess is "
"recommended (not required):"
msgstr ""
"Pentru o performanță optimă, se recomandă adăugarea următoarelor linii în "
"fișierul .htaccess (dat nu sunt obligatorii):"

#. translators: %1$s = plugin name, %2$s + %3$s = opening links, %4$s =
#. closing link
#: inc/admin/ui/notices.php:458
msgid ""
"%1$s is good to go! %2$sTest your load time%4$s, or visit your "
"%3$ssettings%4$s."
msgstr ""
"%1$s este un început bun! %2$sTestează timpul de încărcare%4$ssau mergi la "
"%3$sSetări%4$s."

#: inc/admin/ui/notices.php:499
msgid ""
"Would you allow WP Rocket to collect non-sensitive diagnostic data from this"
" website?"
msgstr ""
"Vrei să-i permiți modulului WP Rocket să colecteze de pe acest site date de "
"diagnosticare care nu sunt sensibile?"

#: inc/admin/ui/notices.php:500
msgid "This would help us to improve WP Rocket for you in the future."
msgstr "Acest lucru ne ajută să îmbunătățim WP Rocket în viitor."

#. translators: button text, click will expand data collection preview
#: inc/admin/ui/notices.php:506
msgid "What info will we collect?"
msgstr "Ce informații vom colecta?"

#: inc/admin/ui/notices.php:511
msgid ""
"Below is a detailed view of all data WP Rocket will collect if granted "
"permission. WP Rocket will never transmit any domain names or email "
"addresses (except for license validation), IP addresses, or third-party API "
"keys."
msgstr ""
"Mai jos este o listă detaliată a tuturor datelor pe care WP Rocket le va "
"colecta dacă îți dai acceptul. WP Rocket nu va divulga niciodată nume de "
"domenii sau adrese de email (cu excepția validării licenței), adrese IP sau "
"chei API de la părți terțe."

#. translators: button text for data collection opt-in
#: inc/admin/ui/notices.php:520
msgid "Yes, allow"
msgstr "Da, permit"

#. translators: button text for data collection opt-in
#: inc/admin/ui/notices.php:523
msgid "No, thanks"
msgstr "Nu, mulțumesc"

#: inc/admin/ui/notices.php:562
msgid "Thank you!"
msgstr "Îți mulțumim!"

#: inc/admin/ui/notices.php:567
msgid "WP Rocket now collects these metrics from your website:"
msgstr "Acum WP Rocket colectează aceste valori de pe site-ul tău web:"

#. translators: %s = plugin name.
#: inc/admin/ui/notices.php:605
msgid "%s: Cache cleared."
msgstr "%s: cache șters."

#. translators: %s = plugin name.
#: inc/admin/ui/notices.php:612
msgid "%s: Post cache cleared."
msgstr "%s: cache articol șters."

#. translators: %s = plugin name.
#: inc/admin/ui/notices.php:619
msgid "%s: Term cache cleared."
msgstr "%s: cache termen șters."

#. translators: %s = plugin name).
#: inc/admin/ui/notices.php:626
msgid "%s: User cache cleared."
msgstr "%s: cache utilizator șters."

#: inc/admin/ui/notices.php:662 inc/Engine/License/views/promo-banner.php:36
#: inc/Engine/License/views/renewal-expired-banner-ocd-disabled.php:32
#: inc/Engine/License/views/renewal-expired-banner-ocd.php:42
#: inc/Engine/License/views/renewal-expired-banner.php:30
#: views/settings/page-sections/dashboard.php:46
msgid "Dismiss this notice"
msgstr "Respinge această notificare"

#: inc/admin/ui/notices.php:682 inc/Engine/Saas/Admin/AdminBar.php:80
#: inc/Engine/Saas/Admin/AdminBar.php:198
msgid "Clear Used CSS"
msgstr "Șterge CSS-ul utilizat"

#: inc/admin/ui/notices.php:685
msgid "Stop Preload"
msgstr "Oprește pre-încărcarea"

#: inc/admin/ui/notices.php:691
msgid "Turn on Remove Unused CSS"
msgstr "Activează Înlătură CSS-ul neutilizat"

#: inc/admin/ui/notices.php:697
msgid "Enable “Separate Cache Files for Mobile Devices” now"
msgstr "Activează „Fișiere cache separate pentru dispozitive mobile” acum"

#: inc/admin/ui/notices.php:715
msgid "Force deactivation "
msgstr "Forțează dezactivarea"

#: inc/admin/ui/notices.php:745
msgid "The following code should have been written to this file:"
msgstr "Codul următor ar fi trebuit să poată fi scris în acest fișier:"

#. translators: %s = plugin name.
#: inc/admin/ui/notices.php:776
msgid "%s cannot configure itself due to missing writing permissions."
msgstr ""
"%s nu se poate configura singur deoarece lipsesc permisiunile de scriere."

#. translators: %s = file/folder name
#: inc/admin/ui/notices.php:782
msgid "Affected file/folder: %s"
msgstr "Fișier/dosar afectat: %s"

#: inc/classes/admin/class-logs.php:124
msgid "The debug file could not be deleted."
msgstr "Fișierul de depanare nu a putut fi șters."

#. Translators: %1$s = Plugin name, %2$s = Plugin version.
#: inc/classes/class-wp-rocket-requirements-check.php:147
msgid "To function properly, %1$s %2$s requires at least:"
msgstr "Pentru a funcționa cum trebuie, %1$s%2$s necesită cel puțin:"

#. Translators: %1$s = PHP version required.
#: inc/classes/class-wp-rocket-requirements-check.php:151
msgid ""
"PHP %1$s. To use this WP Rocket version, please ask your web host how to "
"upgrade your server to PHP %1$s or higher."
msgstr ""
"PHP %1$s. Pentru a folosi această versiune a WP Rocket, întreabă-ți gazda "
"web cum să actualizezi serverul la PHP %1$s sau la o versiune ulterioară. "

#. Translators: %1$s = WordPress version required.
#: inc/classes/class-wp-rocket-requirements-check.php:156
msgid ""
"WordPress %1$s. To use this WP Rocket version, please upgrade WordPress to "
"version %1$s or higher."
msgstr ""
"WordPress %1$s. Pentru a folosi această versiune a WP Rocket, te rog "
"actualizează WordPress la versiunea %1$s sau la una ulterioară."

#: inc/classes/class-wp-rocket-requirements-check.php:159
msgid ""
"If you are not able to upgrade, you can rollback to the previous version by "
"using the button below."
msgstr ""
"Dacă nu reușești să faci actualizarea, poți restaura o versiune anterioară "
"folosind butonul de mai jos."

#. Translators: %s = Previous plugin version.
#: inc/classes/class-wp-rocket-requirements-check.php:161
#: inc/deprecated/deprecated.php:1947
msgid "Re-install version %s"
msgstr "Reinstalează versiunea %s"

#. translators: %s is the plugin name.
#: inc/classes/class-wp-rocket-requirements-check.php:203
#: inc/classes/class-wp-rocket-requirements-check.php:214
#: inc/deprecated/3.11.php:236 inc/deprecated/3.11.php:251
#: inc/Engine/Plugin/UpdaterSubscriber.php:483
#: inc/Engine/Plugin/UpdaterSubscriber.php:497
msgid "%s Update Rollback"
msgstr "Actualizează restaurarea %s"

#. translators: %1$s = missing tags;
#: inc/classes/subscriber/Tools/class-detect-missing-tags-subscriber.php:151
msgid ""
"Failed to detect the following requirement in your theme: closing %1$s."
msgid_plural ""
"Failed to detect the following requirements in your theme: closing %1$s."
msgstr[0] "Detectarea următoarei cerințe în tema ta a eșuat: închid %1$s."
msgstr[1] "Detectarea următoarele cerințe în tema ta a eșuat: închid %1$s."
msgstr[2] "Detectarea următoarele cerințe în tema ta a eșuat: închid %1$s."

#. translators: Documentation exists in EN, FR; use localized URL if
#. applicable.
#: inc/classes/subscriber/Tools/class-detect-missing-tags-subscriber.php:159
msgid ""
"https://docs.wp-rocket.me/article/99-pages-not-cached-or-minify-cssjs-not-"
"working/?utm_source=wp_plugin&utm_medium=wp_rocket#theme"
msgstr ""
"https://docs.wp-rocket.me/article/99-pages-not-cached-or-minify-cssjs-not-"
"working/?utm_source=wp_plugin&utm_medium=wp_rocket#theme"

#: inc/common/admin-bar.php:91 inc/common/admin-bar.php:154
#: views/settings/page-sections/dashboard.php:150
msgid "Clear and preload cache"
msgstr "Șterge și pre-încarcă cache-ul"

#: inc/common/admin-bar.php:131 inc/functions/i18n.php:20
msgid "All languages"
msgstr "Toate limbile"

#: inc/common/admin-bar.php:178
msgid "Clear this post"
msgstr "Șterge acest articol"

#: inc/common/admin-bar.php:192
msgid "Purge this URL"
msgstr "Curăță acest URL"

#: inc/common/admin-bar.php:212
msgid "Purge Sucuri cache"
msgstr "Șterge cache-ul Sucuri"

#: inc/common/admin-bar.php:236 views/settings/fields/rocket-cdn.php:55
msgid "Purge RocketCDN cache"
msgstr "Șterge cache-ul RocketCDN"

#: inc/common/admin-bar.php:249 views/settings/partials/documentation.php:14
msgid "Documentation"
msgstr "Documentație"

#: inc/deprecated/3.2.php:52 views/settings/page-sections/imagify.php:36
msgid "Activate Imagify"
msgstr "Activează Imagify"

#: inc/deprecated/3.2.php:52
msgid "Install Imagify for Free"
msgstr "Instalează Imagify gratuit"

#: inc/deprecated/3.2.php:67
msgid ""
"Speed up your website and boost your SEO by reducing image file sizes "
"without losing quality with Imagify."
msgstr ""
"Cu Imagify îți accelerezi site-ul și îmbunătățești SEO prin reducerea "
"dimensiunilor fișierelor imagine fără a pierde calitatea imaginilor."

#: inc/deprecated/3.2.php:72
msgid "More details"
msgstr "Mai multe detalii"

#. translators: %d is the number of pages preloaded.
#: inc/deprecated/3.2.php:228
msgid "Sitemap preload: %d pages have been cached."
msgstr "Pre-încărcare hărți site: %d de pagini au fost memorate în cache."

#. translators: %d = Number of pages preloaded.
#: inc/deprecated/3.2.php:261
msgid ""
"Sitemap preload: %d uncached pages have now been preloaded. (refresh to see "
"progress)"
msgstr ""
"Pre-încărcare hărți site: %d de pagini nememorate în cache au fost pre-"
"încărcate acum. (reîmprospătează pentru a vedea progresul)"

#. translators: %s is an URL.
#: inc/deprecated/3.3.php:761 inc/deprecated/3.3.php:770
#: inc/Engine/Plugin/UpdaterApiTools.php:32
msgid ""
"An unexpected error occurred. Something may be wrong with WP-Rocket.me or "
"this server&#8217;s configuration. If you continue to have problems, <a "
"href=\"%s\">contact support</a>."
msgstr ""
"A apărut o eroare neașteptată. Este posibil să fie ceva greșit la WP-"
"Rocket.me sau la configurarea acestui server. Dacă ai probleme în "
"continuare, <a href=\"%s\">contactează suportul</a>."

#: inc/deprecated/3.4.php:18
msgid "Choose a domain from the list"
msgstr "Alege un domeniu din listă"

#: inc/deprecated/3.4.php:31
msgid "No domain available in your Cloudflare account"
msgstr "Nu este disponibil niciun domeniu în contul tău Cloudflare"

#: inc/deprecated/3.5.php:71 inc/deprecated/3.5.php:195
msgid ""
"Curl is disabled on your server. Please ask your host to enable it. This is "
"required for the Cloudflare Add-on to work correctly."
msgstr ""
"curl este dezactivat pe serverul tău. Te rog cere-i serviciului tău de "
"găzduire să-l activeze. El este necesar pentru ca suplimentul Cloudflare să "
"funcționeze corect."

#. translators: %1$s = opening link; %2$s = closing link
#: inc/deprecated/3.5.php:79
msgid ""
"Cloudflare email, API key and Zone ID are not set. Read the "
"%1$sdocumentation%2$s for further guidance."
msgstr ""
"Emailul, cheia API și ID-ul zonei Cloudflare nu sunt setate. Pentru "
"îndrumări suplimentare, citește %1$sdocumentația%2$s."

#. translators: %1$s = opening link; %2$s = closing link
#: inc/deprecated/3.5.php:206
msgid ""
"Cloudflare email and API key are not set. Read the %1$sdocumentation%2$s for"
" further guidance."
msgstr ""
"Emailul și cheia API Cloudflare nu sunt setate. Pentru îndrumări "
"suplimentare, citește %1$sdocumentația%2$s."

#: inc/deprecated/3.5.php:271
msgid "Connection to Cloudflare failed"
msgstr "Conectarea la Cloudflare a eșuat"

#. translators: %s = CloudFare API return message.
#: inc/deprecated/3.5.php:587
msgid "<strong>WP Rocket:</strong> %s"
msgstr "<strong>WP Rocket:</strong> %s"

#: inc/deprecated/3.5.php:592
msgid "<strong>WP Rocket:</strong> Cloudflare cache successfully purged."
msgstr ""
"<strong>WP Rocket:</strong> cache-ul Cloudflare a fost șters cu succes."

#: inc/deprecated/3.5.php:858 inc/Engine/HealthCheck/HealthCheck.php:81
msgid ""
"The following scheduled event failed to run. This may indicate the CRON "
"system is not running properly, which can prevent some WP Rocket features "
"from working as intended:"
msgid_plural ""
"The following scheduled events failed to run. This may indicate the CRON "
"system is not running properly, which can prevent some WP Rocket features "
"from working as intended:"
msgstr[0] ""
"Următorul eveniment programat a eșuat în timpul rulării. Acest lucru poate "
"indica că sistemul CRON nu funcționează corect și poate împiedica unele "
"funcționalități WP Rocket să lucreze așa cum ar trebui:"
msgstr[1] ""
"Următoarele evenimente programate au eșuat în timpul rulării. Acest lucru "
"poate indica că sistemul CRON nu funcționează corect și poate împiedica "
"unele funcționalități WP Rocket să lucreze așa cum ar trebui:"
msgstr[2] ""
"Următoarele evenimente programate au eșuat în timpul rulării. Acest lucru "
"poate indica că sistemul CRON nu funcționează corect și poate împiedica "
"unele funcționalități WP Rocket să lucreze așa cum ar trebui:"

#: inc/deprecated/3.5.php:867 inc/Engine/HealthCheck/HealthCheck.php:88
msgid "Please contact your host to check if CRON is working."
msgstr ""
"Te rog contactează serviciul tău de găzduire pentru a verifica dacă CRON "
"funcționează."

#: inc/deprecated/3.10.php:163
msgid "OPcache purge failed."
msgstr "Ștergerea OPcache a eșuat."

#: inc/deprecated/3.10.php:168
msgid "OPcache successfully purged"
msgstr "OPcache șters cu succes"

#: inc/deprecated/3.11.php:81
msgid "Yoast SEO XML sitemap"
msgstr "Hărți site XML Yoast SEO"

#. translators: %s = Name of the plugin.
#. translators: %s = plugin name, e.g. Yoast SEO.
#: inc/deprecated/3.11.php:83 inc/deprecated/3.12.php:58
#: inc/deprecated/3.12.php:87 inc/deprecated/3.12.php:186
#: inc/deprecated/3.12.php:253 inc/deprecated/3.12.php:342
msgid ""
"We automatically detected the sitemap generated by the %s plugin. You can "
"check the option to preload it."
msgstr ""
"Am detectat automat hărțile site generate de modulul %s. Poți bifa opțiunea "
"de a le pre-încărca."

#. translators: 1 and 3 are link openings, 2 is a link closing.
#: inc/deprecated/3.11.php:279 inc/Engine/Plugin/UpdaterSubscriber.php:520
msgid "%1$sReturn to WP Rocket%2$s or %3$sgo to Plugins page%2$s"
msgstr "%1$sÎntoarce-te la WP Rocket%2$s sau %3$smergi la pagina Module%2$s"

#: inc/deprecated/3.12.php:56
msgid "All in One SEO XML sitemap"
msgstr "Hărți site XML All in One SEO"

#: inc/deprecated/3.12.php:85
msgid "Rank Math XML sitemap"
msgstr "Hărți site XML Rank Math"

#: inc/deprecated/3.12.php:184
msgid "SEOPress XML sitemap"
msgstr "Hărți site SEOPress"

#: inc/deprecated/3.12.php:251
msgid "The SEO Framework XML sitemap"
msgstr "Hărți site XML The SEO Framework"

#: inc/deprecated/3.12.php:332
msgid "Jetpack XML Sitemaps"
msgstr "Hărți site XML Jetpack"

#: inc/deprecated/3.12.php:334
msgid "Preload the sitemap from the Jetpack plugin"
msgstr "Pre-încarcă hărțile site din modulul Jetpack"

#: inc/deprecated/3.15.php:28
#: inc/Engine/Admin/Metaboxes/PostEditOptionsSubscriber.php:74
msgid "WP Rocket Options"
msgstr "Opțiuni WP Rocket"

#: inc/deprecated/3.15.php:57 views/metaboxes/post_edit_options.php:19
msgid "Never cache this page"
msgstr "Nu memora niciodată în cache această pagină"

#: inc/deprecated/3.15.php:61 views/metaboxes/post_edit_options.php:23
msgid "Activate these options on this post:"
msgstr "Activează aceste opțiuni pentru acest articol:"

#. translators: %s is the name of the option.
#: inc/deprecated/3.15.php:81
#: inc/Engine/Admin/Metaboxes/PostEditOptionsSubscriber.php:121
msgid "Activate first the %s option."
msgstr "Activează mai întâi opțiunea %s."

#. translators: %1$s = opening strong tag, %2$s = closing strong tag.
#: inc/deprecated/3.15.php:97 views/metaboxes/post_edit_options.php:38
msgid ""
"%1$sNote:%2$s None of these options will be applied if this post has been "
"excluded from cache in the global cache settings."
msgstr ""
"%1$sNotă:%2$s niciuna dintre aceste opțiuni nu va fi aplicată dacă acest "
"articol a fost exclus din cache în setările globale privind cache-ul."

#: inc/deprecated/deprecated.php:240
msgid "<strong>JS</strong> files with Deferred Loading JavaScript"
msgstr "Fișiere <strong>JS</strong> cu încărcare JavaScript întârziată"

#: inc/deprecated/deprecated.php:293
msgid "Add URL"
msgstr "Adaugă URL"

#: inc/deprecated/deprecated.php:1245
#: inc/Engine/Admin/Settings/Settings.php:451
msgid "Settings saved."
msgstr "Setări salvate."

#: inc/deprecated/deprecated.php:1277 views/settings/fields/import-form.php:22
msgid ""
"Before you can upload your import file, you will need to fix the following "
"error:"
msgstr ""
"Înainte de a-ți putea încărca fișierul de import, trebuie să corectezi "
"următoarea eroare:"

#. translators: %s is the maximum upload size set on the current server.
#: inc/deprecated/deprecated.php:1288 views/settings/fields/import-form.php:35
msgid "Choose a file from your computer (maximum size: %s)"
msgstr "Alege un fișier din computer (dimensiune maximă: %s)"

#: inc/deprecated/deprecated.php:1294 inc/Engine/Admin/Settings/Render.php:422
msgid "Upload file and import settings"
msgstr "Încarcă fișierul și importă setările"

#: inc/deprecated/deprecated.php:1385
msgid "Your Cloudflare credentials are valid."
msgstr "Datele tale de conectare Cloudflare sunt valide."

#: inc/deprecated/deprecated.php:1389
msgid "Your Cloudflare credentials are invalid!"
msgstr "Datele tale de conectare Cloudflare sunt invalide!"

#: inc/deprecated/deprecated.php:1453
msgid "Save and optimize"
msgstr "Salvează și optimizează"

#: inc/deprecated/deprecated.php:1453
msgid "Optimize"
msgstr "Optimizează"

#: inc/deprecated/deprecated.php:1464 inc/deprecated/deprecated.php:1666
msgctxt "screen-reader-text"
msgid "Note:"
msgstr "Notă:"

#: inc/deprecated/deprecated.php:1470
msgctxt "screen-reader-text"
msgid "Performance tip:"
msgstr "Tip de performanță"

#: inc/deprecated/deprecated.php:1476
msgctxt "screen-reader-text"
msgid "Third-party feature detected:"
msgstr "Funcționalitate terță detectată: "

#: inc/deprecated/deprecated.php:1488 inc/deprecated/deprecated.php:1672
msgctxt "screen-reader-text"
msgid "Warning:"
msgstr "Avertizare:"

#: inc/deprecated/deprecated.php:1501
msgctxt "button text"
msgid "Download settings"
msgstr "Descarcă setările"

#: inc/deprecated/deprecated.php:1531
msgid "Replace site's hostname with:"
msgstr "Înlocuiește numele gazdei site-ului cu:"

#: inc/deprecated/deprecated.php:1550 inc/deprecated/deprecated.php:1587
#: inc/deprecated/deprecated.php:1615 views/settings/fields/cnames.php:41
#: views/settings/fields/cnames.php:75 views/settings/fields/cnames.php:105
msgid "reserved for"
msgstr "rezervat pentru"

#: inc/deprecated/deprecated.php:1552 inc/deprecated/deprecated.php:1589
#: inc/deprecated/deprecated.php:1617 views/settings/fields/cnames.php:44
#: views/settings/fields/cnames.php:78 views/settings/fields/cnames.php:108
msgid "All files"
msgstr "Toate fișierele"

#: inc/deprecated/deprecated.php:1564 inc/deprecated/deprecated.php:1594
#: inc/deprecated/deprecated.php:1622 views/settings/fields/cnames.php:56
#: views/settings/fields/cnames.php:90 views/settings/fields/cnames.php:120
msgid "Images"
msgstr "Imagini"

#: inc/deprecated/deprecated.php:1635 views/settings/fields/cnames.php:132
msgid "Add CNAME"
msgstr "Adaugă CNAME"

#: inc/deprecated/deprecated.php:1706
msgid "Watch the video"
msgstr "Urmărește videoul"

#: inc/deprecated/deprecated.php:1771
msgid "Basic"
msgstr "De bază"

#: inc/deprecated/deprecated.php:1772
msgid "Static Files"
msgstr "Fișiere statice"

#: inc/deprecated/deprecated.php:1773 inc/Engine/Admin/Settings/Page.php:1500
#: inc/Engine/Admin/Settings/Page.php:1511
#: inc/Engine/CDN/Admin/Subscriber.php:28
msgid "CDN"
msgstr "CDN"

#: inc/deprecated/deprecated.php:1774
msgid "Advanced"
msgstr "Avansat"

#: inc/deprecated/deprecated.php:1775 inc/Engine/Admin/Settings/Page.php:1345
msgid "Database"
msgstr "Bază de date"

#: inc/deprecated/deprecated.php:1776 inc/Engine/Admin/Settings/Page.php:1051
msgid "Preload"
msgstr "Pre-încărcare"

#: inc/deprecated/deprecated.php:1786
#: inc/Engine/Admin/Settings/Subscriber.php:170
#: views/settings/page-sections/tools.php:33
msgid "Tools"
msgstr "Unelte"

#: inc/deprecated/deprecated.php:1789 inc/Engine/Admin/Settings/Page.php:357
#: views/settings/page-sections/dashboard.php:80
msgid "License"
msgstr "Licență"

#. Translators: %1$s = Plugin name, %2$s = Plugin version, %3$s = PHP version
#. required.
#: inc/deprecated/deprecated.php:1944
msgid ""
"%1$s %2$s requires at least PHP %3$s to function properly. To use this "
"version, please ask your web host how to upgrade your server to PHP %3$s or "
"higher. If you are not able to upgrade, you can rollback to the previous "
"version by using the button below."
msgstr ""
"Pentru a funcționa cum trebuie, %1$s%2$s necesită cel puțin PHP %3$s. Pentru"
" a folosi această versiune, te rog întreabă gazda ta web cum poți actualiza "
"serverul la PHP %3$s sau la o versiune ulterioară. Dacă nu reușești să faci "
"actualizarea, poți restaura o versiune anterioară folosind butonul de mai "
"jos."

#. translators: 1: PHP class name, 2: version number, 3: replacement class
#. name.
#: inc/deprecated/DeprecatedClassTrait.php:54
msgid ""
"The called class %1$s is <strong>deprecated</strong> since version %2$s! Use"
" %3$s instead."
msgstr ""
"Clasa numită %1$s este <strong>învechită</strong> începând cu versiunea "
"%2$s! Folosește în schimb %3$s.  "

#. translators: 1: PHP class name, 2: version number.
#: inc/deprecated/DeprecatedClassTrait.php:65
msgid ""
"The called class %1$s is <strong>deprecated</strong> since version %2$s!"
msgstr ""
"Clasa numită %1$s este <strong>învechită</strong> începând cu versiunea "
"%2$s! "

#: inc/deprecated/Engine/Addon/FacebookTracking/Subscriber.php:92
#: inc/deprecated/Engine/Addon/GoogleTracking/Subscriber.php:137
#: inc/Engine/Admin/Database/Subscriber.php:79
msgid "weekly"
msgstr "săptămânal"

#: inc/Engine/Admin/Database/Optimization.php:30
#: inc/Engine/Admin/Settings/Page.php:1389
msgid "Revisions"
msgstr "Revizii"

#: inc/Engine/Admin/Database/Optimization.php:31
#: inc/Engine/Admin/Settings/Page.php:1399
msgid "Auto Drafts"
msgstr "Ciorne automate"

#: inc/Engine/Admin/Database/Optimization.php:32
#: inc/Engine/Admin/Settings/Page.php:1409
msgid "Trashed Posts"
msgstr "Articole aruncate la gunoi"

#: inc/Engine/Admin/Database/Optimization.php:33
#: inc/Engine/Admin/Settings/Page.php:1419
msgid "Spam Comments"
msgstr "Comentarii spam"

#: inc/Engine/Admin/Database/Optimization.php:34
#: inc/Engine/Admin/Settings/Page.php:1429
msgid "Trashed Comments"
msgstr "Comentarii aruncate la gunoi"

#: inc/Engine/Admin/Database/Optimization.php:35
msgid "Transients"
msgstr "Tranzienți"

#: inc/Engine/Admin/Database/Optimization.php:36
msgid "Tables"
msgstr "Tabele"

#: inc/Engine/Admin/Database/Subscriber.php:85
msgid "monthly"
msgstr "lunar"

#: inc/Engine/Admin/Database/Subscriber.php:194
msgid "Database optimization process is running"
msgstr "Procesul de optimizare a bazei de date rulează acum"

#: inc/Engine/Admin/Database/Subscriber.php:224
msgid ""
"Database optimization process is complete. Everything was already optimized!"
msgstr ""
"Procesul de optimizare a bazei de date s-a finalizat. Totul a fost deja "
"optimizat!"

#: inc/Engine/Admin/Database/Subscriber.php:227
msgid ""
"Database optimization process is complete. List of optimized items below:"
msgstr ""
"Procesul de optimizare a bazei de date s-a finalizat. Mai jos este lista "
"elementelor optimizate:"

#. translators: %1$d = number of items optimized, %2$s = type of optimization
#: inc/Engine/Admin/Database/Subscriber.php:235
msgid "%1$d %2$s optimized."
msgstr "%1$d (de) %2$s optimizate (optimizați)."

#. translators: %1$s = <strong>, %2$s = </strong>, %3$s = <a>, %4$s = </a>.
#: inc/Engine/Admin/DomainChange/Subscriber.php:143
msgid ""
"%1$sWP Rocket:%2$s We detected that the website domain has changed. The "
"configuration files must be regenerated for the page cache and all other "
"optimizations to work as intended. %3$sLearn More%4$s"
msgstr ""
"%1$sWP Rocket:%2$s am detectat că ai schimbat domeniul pentru site-u tău "
"web. Pentru ca cache-ul paginilor și toate celelalte optimizări să "
"funcționeze așa cum trebuie, trebuie să fie regenerate fișierele de "
"configurare. %3$sÎnvață mai multe%4$s"

#: inc/Engine/Admin/DomainChange/Subscriber.php:171
msgid "Regenerate WP Rocket configuration files now"
msgstr "Regenerează acum fișierele de configurare WP Rocket"

#: inc/Engine/Admin/Settings/Page.php:223
msgid "Save Changes"
msgstr "Salvează modificările"

#: inc/Engine/Admin/Settings/Page.php:223
msgid "Validate License"
msgstr "Validează licența"

#: inc/Engine/Admin/Settings/Page.php:279
#: inc/Engine/Admin/Settings/Page.php:280 inc/functions/admin.php:550
msgid "Unavailable"
msgstr "Indisponibilă"

#: inc/Engine/Admin/Settings/Page.php:374
msgid "API key"
msgstr "Cheie API"

#: inc/Engine/Admin/Settings/Page.php:389
msgid "Email address"
msgstr "Adresă email"

#: inc/Engine/Admin/Settings/Page.php:415
msgid "Dashboard"
msgstr "Panou control"

#: inc/Engine/Admin/Settings/Page.php:416
msgid "Get help, account info"
msgstr "Primești ajutor, informații despre cont"

#: inc/Engine/Admin/Settings/Page.php:425
msgid "My Status"
msgstr "Starea mea"

#: inc/Engine/Admin/Settings/Page.php:435 views/settings/page.php:75
msgid "Rocket Analytics"
msgstr "Analitice Rocket"

#. translators: %1$s = opening <a> tag, %2$s = closing </a> tag.
#: inc/Engine/Admin/Settings/Page.php:437
msgid ""
"I agree to share anonymous data with the development team to help improve WP"
" Rocket. %1$sWhat info will we collect?%2$s"
msgstr ""
"Sunt de acord să partajez date anonime cu echipa de dezvoltare pentru a "
"ajuta la îmbunătățirea WP Rocket. %1$sCe informații vom colecta?%2$s"

#: inc/Engine/Admin/Settings/Page.php:489
msgid "File Optimization"
msgstr "Optimizare fișiere"

#: inc/Engine/Admin/Settings/Page.php:490
msgid "Optimize CSS & JS"
msgstr "Optimizează CSS și JS"

#. translators: %1$s = type of minification (HTML, CSS or JS), %2$s = “WP
#. Rocket”.
#: inc/Engine/Admin/Settings/Page.php:498
msgid ""
"%1$s Minification is currently activated in <strong>Autoptimize</strong>. If"
" you want to use %2$s’s minification, disable this option in Autoptimize."
msgstr ""
"%1$s În prezent, minificarea este activată în <strong>Autoptimize</strong>. "
"Dacă vrei să folosești minificarea oferită de  %2$s, dezactivează această "
"opțiune în Autoptimize."

#: inc/Engine/Admin/Settings/Page.php:508
msgid "CSS Files"
msgstr "Fișiere CSS"

#: inc/Engine/Admin/Settings/Page.php:517
msgid "JavaScript Files"
msgstr "Fișiere JavaScript"

#. translators: %1$s = type of minification (HTML, CSS or JS), %2$s = “WP
#. Rocket”.
#: inc/Engine/Admin/Settings/Page.php:524
msgid ""
"%1$s Minification is currently activated in <strong>Autoptimize</strong>. If"
" you want to use %2$s’s minification, disable those options in Autoptimize."
msgstr ""
"%1$sMinificarea este activată acum în <strong>Autoptimize</strong>. Dacă "
"vrei să folosești minificarea %2$s, dezactivează acele opțiuni în "
"Autoptimize."

#: inc/Engine/Admin/Settings/Page.php:529
msgid ""
"If you have problems after activating this option, copy and paste the "
"default exclusions to quickly resolve issues:"
msgstr ""
"Dacă ai probleme după activarea acestei opțiuni, copiază și plasează "
"excluderile implicite pentru a rezolva rapid problemele:"

#. translators: %1$s = opening </a> tag, %2$s = closing </a> tag.
#: inc/Engine/Admin/Settings/Page.php:533
msgid ""
"Also, please check our %1$sdocumentation%2$s for a list of compatibility "
"exclusions."
msgstr ""
"De asemenea, te rog să consulți %1$sdocumentația%2$s noastră pentru a vedea "
"o listă cu excluderile la compatibilitate."

#: inc/Engine/Admin/Settings/Page.php:538
msgid ""
"Internal scripts are excluded by default to prevent issues. Remove them to "
"take full advantage of this option."
msgstr ""
"Scripturile interne sunt excluse implicit pentru a împiedica apariția unor "
"probleme. Înlătură-le ca să beneficiezi din plin de această opțiune."

#. translators: %1$s = opening </a> tag, %2$s = closing </a> tag.
#: inc/Engine/Admin/Settings/Page.php:541
msgid ""
"If this causes trouble, restore the default exclusions, found %1$shere%2$s"
msgstr ""
"Dacă apar probleme, restaurează excluderile implicite, le găsești "
"%1$saici%2$s"

#: inc/Engine/Admin/Settings/Page.php:550
msgid "Minify CSS files"
msgstr "Minifică fișierele CSS"

#: inc/Engine/Admin/Settings/Page.php:551
msgid "Minify CSS removes whitespace and comments to reduce the file size."
msgstr ""
"Minifică CSS înlătură spațiile goale și comentariile pentru a reduce "
"dimensiunea fișierului."

#: inc/Engine/Admin/Settings/Page.php:564
#: inc/Engine/Admin/Settings/Page.php:628
#: inc/Engine/Admin/Settings/Page.php:694
#: inc/Engine/Admin/Settings/Page.php:718
msgid "This could break things!"
msgstr "Asta ar putea strica unele lucruri!"

#: inc/Engine/Admin/Settings/Page.php:565
#: inc/Engine/Admin/Settings/Page.php:629
#: inc/Engine/Admin/Settings/Page.php:695
#: inc/Engine/Admin/Settings/Page.php:719
msgid ""
"If you notice any errors on your website after having activated this "
"setting, just deactivate it again, and your site will be back to normal."
msgstr ""
"Dacă după activarea acestei setări observi erori pe site-ul tău, trebuie "
"doar s-o dezactivezi și site-ul va reveni la normal."

#: inc/Engine/Admin/Settings/Page.php:566
msgid "Activate minify CSS"
msgstr "Activează minifică CSS"

#: inc/Engine/Admin/Settings/Page.php:572
msgid ""
"Specify URLs of CSS files to be excluded from minification (one per line)."
msgstr ""
"Specifică URL-urile fișierelor CSS care vor fi excluse din minificare (câte "
"unul pe un rând)."

#: inc/Engine/Admin/Settings/Page.php:573
msgid ""
"<strong>Internal:</strong> The domain part of the URL will be stripped "
"automatically. Use (.*).css wildcards to exclude all CSS files located at a "
"specific path."
msgstr ""
"<strong>Interne:</strong> Partea din URL care conține domeniul va fi "
"eliminată automat. Folosește metacaracterele (.*).css pentru a exclude toate"
" fișierele CSS localizate pe o anumită cale."

#. translators: %1$s = opening <a> tag, %2$s = closing </a> tag.
#: inc/Engine/Admin/Settings/Page.php:575
msgid ""
"<strong>3rd Party:</strong> Use either the full URL path or only the domain "
"name, to exclude external CSS. %1$sMore info%2$s"
msgstr ""
"<strong>Parte terță:</strong> pentru a exclude CSS-ul extern, folosește "
"calea completă a URL-ului sau doar numele de domeniu. %1$sMai multe "
"informații%2$s"

#: inc/Engine/Admin/Settings/Page.php:588
#: inc/Engine/Admin/Settings/Page.php:610
msgid "Optimize CSS delivery"
msgstr "Optimizează livrarea CSS"

#: inc/Engine/Admin/Settings/Page.php:593
msgid ""
"Optimize CSS delivery eliminates render-blocking CSS on your website. Only "
"one method can be selected. Remove Unused CSS is recommended for optimal "
"performance, but limited only to the users with active license."
msgstr ""
"Optimizează livrarea CSS elimină CSS-ul care blochează randarea pe site-ul "
"tău. Poate fi selectată numai o singură metodă. Pentru o performanță optimă,"
" este recomandată Înlătură CSS-ul neutilizat, dar este disponibilă numai "
"pentru utilizatorii care au o licență activă."

#: inc/Engine/Admin/Settings/Page.php:593
msgid ""
"Optimize CSS delivery eliminates render-blocking CSS on your website. Only "
"one method can be selected. Remove Unused CSS is recommended for optimal "
"performance."
msgstr ""
"Optimizează livrarea CSS elimină CSS-ul care blochează randarea pe site-ul "
"tău. Poate fi selectată numai o singură metodă. Este recomandată Înlătură "
"CSS-ul neutilizat pentru o performanță optimă."

#. translators: %1$s = opening <a> tag, %2$s = closing </a> tag.
#: inc/Engine/Admin/Settings/Page.php:603
msgid ""
"Optimize CSS Delivery features are disabled on local environments. %1$sLearn"
" more%2$s"
msgstr ""
"Funcționalitățile pentru Optimizează livrarea CSS sunt dezactivate în "
"mediile locale. %1$sAflă mai multe%2$s"

#: inc/Engine/Admin/Settings/Page.php:623
#: inc/Engine/Optimization/RUCSS/Admin/OptionSubscriber.php:74
msgid "Remove Unused CSS"
msgstr "Înlătură CSS-ul neutilizat"

#. translators: %1$s = opening <a> tag, %2$s = closing </a> tag.
#: inc/Engine/Admin/Settings/Page.php:626
msgid ""
"Removes unused CSS per page and helps to reduce page size and HTTP requests."
" Recommended for best performance. Test thoroughly! %1$sMore info%2$s"
msgstr ""
"Înlătură CSS-ul neutilizat per pagină, te ajută să reduci dimensiunile "
"paginilor și cererile HTTP. Este recomandat pentru o performanță mai bună. "
"Testează în mod aprofundat! %1$sMai multe informații%2$s"

#: inc/Engine/Admin/Settings/Page.php:630
msgid "Activate Remove Unused CSS"
msgstr "Activează Înlătură CSS-ul neutilizat"

#: inc/Engine/Admin/Settings/Page.php:636
msgid "CSS safelist"
msgstr "Listă sigură CSS"

#: inc/Engine/Admin/Settings/Page.php:637
msgid ""
"Specify CSS filenames, IDs or classes that should not be removed (one per "
"line)."
msgstr ""
"Specifică numele fișierelor CSS, ID-urile sau clasele care nu ar trebui "
"înlăturate (câte unul per linie)."

#: inc/Engine/Admin/Settings/Page.php:652
#: inc/Engine/CriticalPath/Admin/Subscriber.php:200
msgid "Load CSS asynchronously"
msgstr "Încarcă fișierele CSS asincron"

#. translators: %1$s = plugin name.
#: inc/Engine/Admin/Settings/Page.php:655
msgctxt "WP Critical CSS compatibility"
msgid ""
"Load CSS asynchronously is currently handled by the %1$s plugin. If you want"
" to use WP Rocket’s load CSS asynchronously option, disable the %1$s plugin."
msgstr ""
"În prezent, modulul %1$s gestionează Încarcă CSS asincron. Dacă vrei să "
"folosești opțiunea Încarcă fișierele CSS asincron oferită de WP Rocket, "
"dezactivează modulul %1$s."

#. translators: %1$s = opening <a> tag, %2$s = closing </a> tag.
#: inc/Engine/Admin/Settings/Page.php:657
msgid ""
"Generates critical path CSS and loads CSS asynchronously. %1$sMore info%2$s"
msgstr ""
"Generează CSS pe cale critică și încarcă fișierele CSS asincron. %1$sMai "
"multe informații%2$s"

#: inc/Engine/Admin/Settings/Page.php:663
msgid "Fallback critical CSS"
msgstr "CSS critic de rezervă"

#. translators: %1$s = opening <a> tag, %2$s = closing </a> tag.
#: inc/Engine/Admin/Settings/Page.php:665
msgid ""
"Provides a fallback if auto-generated critical path CSS is incomplete. "
"%1$sMore info%2$s"
msgstr ""
"Oferă o alternativă dacă CSS-ul de cale critic generat automat este "
"incomplet. %1$sMai multe informații%2$s"

#: inc/Engine/Admin/Settings/Page.php:680
msgid "Minify JavaScript files"
msgstr "Minifică fișierele JavaScript"

#: inc/Engine/Admin/Settings/Page.php:681
msgid ""
"Minify JavaScript removes whitespace and comments to reduce the file size."
msgstr ""
"Minifică JavaScript înlătură spațiile goale și comentariile pentru a reduce "
"dimensiunea fișierului."

#: inc/Engine/Admin/Settings/Page.php:696
msgid "Activate minify JavaScript"
msgstr "Activează minifică JavaScript"

#: inc/Engine/Admin/Settings/Page.php:701
msgid ""
"Combine JavaScript files <em>(Enable Minify JavaScript files to select)</em>"
msgstr ""
"Combină fișierele JavaScript <em>(activează Minifică fișierele JavaScript "
"pentru a selecta)</em>"

#. translators: %1$s = opening <a> tag, %2$s = closing </a> tag.
#: inc/Engine/Admin/Settings/Page.php:703
msgid ""
"Combine JavaScript files combines your site’s internal, 3rd party and inline"
" JS reducing HTTP requests. Not recommended if your site uses HTTP/2. "
"%1$sMore info%2$s"
msgstr ""
"Combină fișierele JavaScript îmbină resursele interne ale site-ului, "
"resursele terțe și JS-ul în-line, reducând cererile HTTP. Nu este recomandat"
" dacă site-ul tău folosește HTTP/2. %1$sMai multe informații%2$s"

#. translators: %1$s = opening <a> tag, %2$s = closing </a> tag.
#: inc/Engine/Admin/Settings/Page.php:704
msgid ""
"For compatibility and best results, this option is disabled when delay "
"javascript execution is enabled."
msgstr ""
"Pentru compatibilitate și rezultate foarte bune, această opțiune este "
"dezactivată când este activată opțiunea întârzie executarea javascript."

#: inc/Engine/Admin/Settings/Page.php:720
msgid "Activate combine JavaScript"
msgstr "Activează combină JavaScript"

#. translators: %1$s = opening <a> tag, %2$s = closing </a> tag.
#: inc/Engine/Admin/Settings/Page.php:727
msgid ""
"Specify patterns of inline JavaScript to be excluded from concatenation (one"
" per line). %1$sMore info%2$s"
msgstr ""
"Specifică structurile JavaScript în-line care să fie excluse din concatenare"
" (câte una pe un rând). %1$sMai multe informații%2$s"

#: inc/Engine/Admin/Settings/Page.php:744
msgid ""
"Specify URLs of JavaScript files to be excluded from minification and "
"concatenation (one per line)."
msgstr ""
"Specifică URL-urile fișierelor JavaScript care să fie excluse din minificare"
" și concatenare (câte unul pe un rând)."

#: inc/Engine/Admin/Settings/Page.php:745
msgid ""
"<strong>Internal:</strong> The domain part of the URL will be stripped "
"automatically. Use (.*).js wildcards to exclude all JS files located at a "
"specific path."
msgstr ""
"<strong>Interne:</strong> Partea din URL care conține domeniul va fi "
"eliminată automat. Folosește metacaracterele (.*).js pentru a exclude toate "
"fișierele JS localizate pe o anumită cale."

#. translators: %1$s = opening <a> tag, %2$s = closing </a> tag.
#: inc/Engine/Admin/Settings/Page.php:747
msgid ""
"<strong>3rd Party:</strong> Use either the full URL path or only the domain "
"name, to exclude external JS. %1$sMore info%2$s"
msgstr ""
"<strong>Părți terțe:</strong> pentru a exclude JS-ul extern, folosește fie "
"calea completă a URL-ului ori fie numai numele de domeniu. %1$sMai multe "
"informații%2$s"

#: inc/Engine/Admin/Settings/Page.php:763
#: inc/Engine/Optimization/DeferJS/AdminSubscriber.php:76
msgid "Load JavaScript deferred"
msgstr "Încarcă JavaScript întârziat"

#. translators: %1$s = opening <a> tag, %2$s = closing </a> tag.
#: inc/Engine/Admin/Settings/Page.php:765
msgid ""
"Load JavaScript deferred eliminates render-blocking JS on your site and can "
"improve load time. %1$sMore info%2$s"
msgstr ""
"Încarcă JavaScript întârziat elimină JS-ul care blochează randarea pe site-"
"ul tău și poate îmbunătăți timpul de încărcare. %1$sMai multe informații%2$s"

#. translators: %1$s = opening <a> tag, %2$s = closing </a> tag.
#: inc/Engine/Admin/Settings/Page.php:778
msgid ""
"Specify URLs or keywords of JavaScript files to be excluded from defer (one "
"per line). %1$sMore info%2$s"
msgstr ""
"Specifică URL-urile sau cuvintele cheie ale fișierelor JavaScript care să "
"fie excluse de la amânare (câte unul pe fiecare linie). %1$sMai multe "
"informații%2$s"

#: inc/Engine/Admin/Settings/Page.php:794
#: inc/Engine/Optimization/DelayJS/Admin/Subscriber.php:210
msgid "Delay JavaScript execution"
msgstr "Întârzie executarea JavaScript"

#. translators: %1$s = opening <a> tag, %2$s = closing </a> tag.
#: inc/Engine/Admin/Settings/Page.php:796
msgid ""
"Improves performance by delaying the loading of JavaScript files until user "
"interaction (e.g. scroll, click). %1$sMore info%2$s"
msgstr ""
"Îmbunătățește performanța prin întârzierea încărcării fișierelor JavaScript "
"până la interacțiunea cu utilizatorul (de exemplu: derulează, dă clic). "
"%1$sMai multe informații%2$s"

#: inc/Engine/Admin/Settings/Page.php:805
msgid "One-click exclusions"
msgstr "Excluderi cu un singur clic"

#: inc/Engine/Admin/Settings/Page.php:806
msgid ""
"When using the Delay JavaScript Execution, you might experience delay "
"loading elements located in the viewport that need to appear immediately - "
"e.g. slider, header, menu."
msgstr ""
"Când folosești Întârzie executarea JavaScript, este posibil să ai o "
"întârziere la încărcarea elementelor situate în spațiul vizibil care ar "
"trebui să apară imediat - de exemplu, carusel, antet, meniu."

#: inc/Engine/Admin/Settings/Page.php:807
msgid ""
"If you need instant visibility, click below on files that should NOT be "
"delayed. This selection will help users interact with the elements straight "
"away."
msgstr ""
"Dacă trebuie să ai o vizibilitate imediată, dă clic mai jos pe fișierele "
"care NU vrei să fie întârziate. Această selectare va ajuta utilizatorii să "
"interacționeze imediat cu aceste elemente."

#: inc/Engine/Admin/Settings/Page.php:824
msgid ""
"Specify URLs or keywords that can identify inline or JavaScript files to be "
"excluded from delaying execution (one per line)."
msgstr ""
"Specifică URL-urile sau cuvintele cheie care pot identifica fișierele în-"
"line sau JavaScript care să fie excluse din întârzierea executării (câte "
"unul pe o linie)."

#: inc/Engine/Admin/Settings/Page.php:856
msgid "Media"
msgstr "Media"

#: inc/Engine/Admin/Settings/Page.php:857
msgid "LazyLoad, image dimensions"
msgstr "Încărcare lentă, dimensiuni imagini"

#: inc/Engine/Admin/Settings/Page.php:866
msgid "Autoptimize"
msgstr "Autoptimize"

#: inc/Engine/Admin/Settings/Page.php:920
msgid "LazyLoad"
msgstr "Încărcare lentă"

#. translators: %1$s = opening <a> tag, %2$s = closing </a> tag.
#: inc/Engine/Admin/Settings/Page.php:923
msgid ""
"It can improve actual and perceived loading time as images, iframes, and "
"videos will be loaded only as they enter (or about to enter) the viewport "
"and reduces the number of HTTP requests. %1$sMore Info%2$s"
msgstr ""
"Acesta poate îmbunătăți timpul de încărcare real și perceput, deoarece "
"imaginile, iframe-urile și videourile vor fi încărcate numai în momentul în "
"care intră (sau urmează să intre) în fereastra de vizualizare, astfel se "
"reduce numărul de cereri HTTP. %1$sMai multe informații%2$s"

#. translators: %1$s = “WP Rocket”, %2$s = a list of plugin names.
#: inc/Engine/Admin/Settings/Page.php:930
msgid ""
"LazyLoad is currently activated in %2$s. If you want to use WP Rocket’s "
"LazyLoad, disable this option in %2$s."
msgstr ""
"Încărcarea lentă este activată acum în %2$s. Dacă vrei să folosești "
"încărcarea lentă a imaginilor de la WP Rocket, dezactivează această opțiune "
"în %2$s."

#: inc/Engine/Admin/Settings/Page.php:933
msgid "Image Dimensions"
msgstr "Dimensiuni imagini"

#. translators: %1$s = opening <a> tag, %2$s = closing </a> tag.
#: inc/Engine/Admin/Settings/Page.php:936
msgid ""
"Add missing width and height attributes to images. Helps prevent layout "
"shifts and improve the reading experience for your visitors. %1$sMore "
"info%2$s"
msgstr ""
"Adaugă atributele care lipsesc pentru lățimea și înălțimea imaginilor. "
"Împiedică schimbările la aranjamente și îmbunătățește experiența de "
"vizualizare a vizitatorilor tăi. %1$sMai multe informații%2$s"

#: inc/Engine/Admin/Settings/Page.php:955
msgid "Enable for images"
msgstr "Activează pentru imagini"

#. translators: %1$s = “WP Rocket”, %2$s = a list of plugin names.
#: inc/Engine/Admin/Settings/Page.php:967
msgid ""
"LazyLoad for images is currently activated in %2$s. If you want to use "
"%1$s’s LazyLoad, disable this option in %2$s."
msgstr ""
"Încărcarea lentă a imaginilor este activată acum în %2$s. Dacă vrei să "
"folosești încărcarea lentă a imaginilor de la %1$s, dezactivează această "
"opțiune în %2$s."

#: inc/Engine/Admin/Settings/Page.php:975
msgid "Enable for CSS background images"
msgstr "Activează pentru imagini de fundal CSS"

#: inc/Engine/Admin/Settings/Page.php:990
msgid "Enable for iframes and videos"
msgstr "Activează pentru iframe-uri și videouri"

#: inc/Engine/Admin/Settings/Page.php:1005
msgid "Replace YouTube iframe with preview image"
msgstr "Înlocuiește iframe-ul YouTube cu imaginea de previzualizare"

#. translators: %1$s = “WP Rocket”, %2$s = a list of plugin or themes names.
#: inc/Engine/Admin/Settings/Page.php:1007
msgid "Replace YouTube iframe with preview image is not compatible with %2$s."
msgstr ""
"Înlocuiește iframe-ul YouTube cu imaginea de previzualizare nu este o "
"acțiune compatibilă cu %2$s."

#. translators: %1$s = “WP Rocket”, %2$s = a list of plugin or themes names.
#: inc/Engine/Admin/Settings/Page.php:1007
msgid ""
"This can significantly improve your loading time if you have a lot of "
"YouTube videos on a page."
msgstr ""
"Îți poate îmbunătăți semnificativ timpul de încărcare dacă ai o mulțime de "
"videouri YouTube pe o pagină."

#: inc/Engine/Admin/Settings/Page.php:1022
msgid "Excluded images or iframes"
msgstr "Imagini sau iframe-uri excluse"

#. translators: %1$s = opening <a> tag, %2$s = closing </a> tag.
#: inc/Engine/Admin/Settings/Page.php:1024
msgid ""
"Specify keywords (e.g. image filename, CSS filename, CSS class, domain) from"
" the image or iframe code to be excluded (one per line). %1$sMore info%2$s"
msgstr ""
"Specifică cuvintele cheie (de exemplu, nume fișier imagine, nume fișier CSS,"
" clasă CSS, domeniu) din imagine sau codurile iframe care trebuie excluse "
"(câte unul pe un rând). %1$sMai multe informații%2$s"

#: inc/Engine/Admin/Settings/Page.php:1032
msgid "Add missing image dimensions"
msgstr "Adaugă dimensiunile care lipsesc pentru imagini"

#: inc/Engine/Admin/Settings/Page.php:1052
msgid "Generate cache files, preload fonts"
msgstr "Generează fișiere cache, pre-încarcă fonturi"

#: inc/Engine/Admin/Settings/Page.php:1064
msgid "Preload Cache"
msgstr "Pre-încarcă cache"

#. translators: %1$s = opening <a> tag, %2$s = closing </a> tag.
#: inc/Engine/Admin/Settings/Page.php:1067
msgid ""
"When you enable preloading WP Rocket will automatically detect your sitemaps"
" and save all URLs to the database. The plugin will make sure that your "
"cache is always preloaded."
msgstr ""
"Dacă activezi preîncărcarea, WP Rocket va detecta automat hărțile site și va"
" salva toate URL-urile în baza de date. Modulul asigură că cache-ul este "
"mereu preîncărcat."

#: inc/Engine/Admin/Settings/Page.php:1075
msgid "Preload Links"
msgstr "Pre-încarcă legături"

#. translators: %1$s = opening <a> tag, %2$s = closing </a> tag.
#: inc/Engine/Admin/Settings/Page.php:1078
msgid ""
"Link preloading improves the perceived load time by downloading a page when "
"a user hovers over the link. %1$sMore info%2$s"
msgstr ""
"Pre-încărcarea legăturilor îmbunătățește timpul de încărcare perceput prin "
"descărcarea unei pagini atunci când un utilizator trece peste legătură. "
"%1$sMai multe informații%2$s"

#: inc/Engine/Admin/Settings/Page.php:1086
msgid "Prefetch DNS Requests"
msgstr "Pre-aduce cereri DNS"

#: inc/Engine/Admin/Settings/Page.php:1088
msgid ""
"DNS prefetching can make external files load faster, especially on mobile "
"networks"
msgstr ""
"Pre-aducerea DNS poate face ca fișierele externe să se încarce mai rapid, în"
" special în rețelele pentru mobil"

#: inc/Engine/Admin/Settings/Page.php:1093
msgid "Preload Fonts"
msgstr "Pre-încarcă fonturi"

#. translators: %1$s = opening <a> tag, %2$s = closing </a> tag.
#: inc/Engine/Admin/Settings/Page.php:1096
msgid ""
"Improves performance by helping browsers discover fonts in CSS files. "
"%1$sMore info%2$s"
msgstr ""
"Îmbunătățește performanța ajutând navigatoarele să descopere fonturi în "
"fișierele CSS. %1$sMai multe informații%2$s"

#: inc/Engine/Admin/Settings/Page.php:1110
msgid "Activate Preloading"
msgstr "Activează pre-încărcarea"

#: inc/Engine/Admin/Settings/Page.php:1121
msgid "Exclude URLs"
msgstr "Exclude URL-urile"

#. translators: %1$s = opening <a> tag, %2$s = closing </a> tag.
#: inc/Engine/Admin/Settings/Page.php:1126
msgid ""
"Specify URLs to be excluded from the preload feature (one per line). "
"%1$sMore info%2$s"
msgstr ""
"Specifică URL-urile care să fie excluse din funcționalitatea preîncărcare "
"(câte unul pe un rând). %1$sMai multe informații%2$s"

#: inc/Engine/Admin/Settings/Page.php:1137
msgid "URLs to prefetch"
msgstr "URL-uri de pre-adus"

#: inc/Engine/Admin/Settings/Page.php:1138
msgid ""
"Specify external hosts to be prefetched (no <code>http:</code>, one per "
"line)"
msgstr ""
"Specifică gazdele externe care să fie pre-aduse (fără <code>http:</code>, "
"una pe linie)"

#: inc/Engine/Admin/Settings/Page.php:1147
msgid "Fonts to preload"
msgstr "Fonturi pentru pre-încărcat"

#: inc/Engine/Admin/Settings/Page.php:1148
msgid ""
"Specify urls of the font files to be preloaded (one per line). Fonts must be"
" hosted on your own domain, or the domain you have specified on the CDN tab."
msgstr ""
"Specifică URL-urile fișierelor cu fonturi care trebuie preîncărcate (câte "
"unul pe o linie). Fonturile trebuie să fie găzduite pe propriu domeniu sau "
"pe domeniul specificat în fila CDN."

#: inc/Engine/Admin/Settings/Page.php:1149
msgid ""
"The domain part of the URL will be stripped automatically.<br/>Allowed font "
"extensions: otf, ttf, svg, woff, woff2."
msgstr ""
"Partea din URL care conține domeniul va fi eliminată automat.<br/>Extensii "
"de font permise: otf, ttf, svg, woff, woff2."

#: inc/Engine/Admin/Settings/Page.php:1158
msgid "Enable link preloading"
msgstr "Activează pre-încărcarea legăturilor"

#: inc/Engine/Admin/Settings/Page.php:1177
msgid "Advanced Rules"
msgstr "Reguli avansate"

#: inc/Engine/Admin/Settings/Page.php:1178
msgid "Fine-tune cache rules"
msgstr "Ajustează fin regulile cache"

#: inc/Engine/Admin/Settings/Page.php:1191
msgid ""
"Sensitive pages like custom login/logout URLs should be excluded from cache."
msgstr ""
"Paginile sensibile, cum ar fi URL-urile personalizate de "
"autentificare/dezautentificare, ar trebui să fie excluse din cache."

#: inc/Engine/Admin/Settings/Page.php:1194
msgctxt "plugin name"
msgid "WooCommerce"
msgstr "WooCommerce"

#: inc/Engine/Admin/Settings/Page.php:1196
msgctxt "plugin name"
msgid "Easy Digital Downloads"
msgstr "Easy Digital Downloads"

#: inc/Engine/Admin/Settings/Page.php:1198
msgctxt "plugin name"
msgid "iThemes Exchange"
msgstr "iThemes Exchange"

#: inc/Engine/Admin/Settings/Page.php:1200
msgctxt "plugin name"
msgid "Jigoshop"
msgstr "Jigoshop"

#: inc/Engine/Admin/Settings/Page.php:1202
msgctxt "plugin name"
msgid "WP-Shop"
msgstr "WP-Shop"

#. translators: %1$s = opening <a> tag, %2$s = plugin name, %3$s closing </a>
#. tag.
#: inc/Engine/Admin/Settings/Page.php:1208
msgid ""
"<br>Cart, checkout and \"my account\" pages set in "
"<strong>%1$s%2$s%3$s</strong> will be detected and never cached by default."
msgstr ""
"<br>Paginile coș de cumpărături, finalizare comandă și „contul meu” setate "
"în <strong>%1$s%2$s%3$s</strong> vor fi detectate și implicit nu sunt "
"memorate niciodată în cache."

#: inc/Engine/Admin/Settings/Page.php:1218
msgid "Cache Lifespan"
msgstr "Durată de viață cache"

#. translators: %1$s = opening <a> tag, %2$s = closing </a> tag.
#: inc/Engine/Admin/Settings/Page.php:1221
msgid ""
"Cache files older than the specified lifespan will be deleted.<br>Enable "
"%1$spreloading%2$s for the cache to be rebuilt automatically after lifespan "
"expiration."
msgstr ""
"Fișierele cache mai vechi decât durata de viață specificată vor fi șterse. "
"<br>Activează %1$spre-încărcarea%2$s pentru cache-ul care să fie reconstruit"
" automat după expirarea duratei de viață."

#: inc/Engine/Admin/Settings/Page.php:1237
msgid "Never Cache Cookies"
msgstr "Nu memora niciodată în cache cookie-uri"

#: inc/Engine/Admin/Settings/Page.php:1255
msgid "Cache Query String(s)"
msgstr "Memorează în cache șirul (șirurile) de interogare"

#. translators: %1$s = opening <a> tag, %2$s = closing </a> tag.
#: inc/Engine/Admin/Settings/Page.php:1258
msgid ""
"%1$sCache for query strings%2$s enables you to force caching for specific "
"GET parameters."
msgstr ""
"%1$sCache pentru șiruri de interogare%2$s îți permite să forțezi cache-ul "
"pentru parametrii GET specifici."

#: inc/Engine/Admin/Settings/Page.php:1269
msgid ""
"Specify time after which the global cache is cleared<br>(0 = unlimited )"
msgstr ""
"Specifică timpul după care cache-ul global este șters<br>(0 = nelimitat)"

#. translators: %1$s = opening <a> tag, %2$s = closing </a> tag.
#: inc/Engine/Admin/Settings/Page.php:1271
msgid ""
"Reduce lifespan to 10 hours or less if you notice issues that seem to appear"
" periodically. %1$sWhy?%2$s"
msgstr ""
"Reduci durata de viață la 10 ore sau mai puțin dacă remarci probleme care "
"par să apară periodic. %1$sDe ce?%2$s"

#: inc/Engine/Admin/Settings/Page.php:1277
#: inc/Engine/License/views/promo-banner.php:30
#: inc/Engine/License/views/renewal-soon-banner.php:13
msgid "Hours"
msgstr "Ore"

#: inc/Engine/Admin/Settings/Page.php:1278
#: inc/Engine/License/views/promo-banner.php:29
#: inc/Engine/License/views/renewal-soon-banner.php:12
msgid "Days"
msgstr "Zile"

#: inc/Engine/Admin/Settings/Page.php:1283
msgid ""
"Specify URLs of pages or posts that should never be cached (one per line)"
msgstr ""
"Specifică URL-urile paginilor sau articolelor care nu ar trebui să fie "
"niciodată memorate în cache (unul pe linie)"

#: inc/Engine/Admin/Settings/Page.php:1284
#: inc/Engine/Admin/Settings/Page.php:1312
msgid ""
"The domain part of the URL will be stripped automatically.<br>Use (.*) "
"wildcards to address multiple URLs under a given path."
msgstr ""
"Partea din URL care conține domeniul va fi eliminată automat.<br>Folosește "
"metacaracterele (.*) pentru a scrie adresa mai multor URL-uri pentru o cale "
"dată."

#: inc/Engine/Admin/Settings/Page.php:1293
msgid ""
"Specify full or partial IDs of cookies that, when set in the visitor's "
"browser, should prevent a page from getting cached (one per line)"
msgstr ""
"Specifică ID-uri complete sau parțiale pentru cookie-urile care, atunci când"
" sunt setate în navigatorul vizitatorilor, ar trebui să împiedice păstrarea "
"în memoria cache a unei pagini (câte unul pe o linie)"

#: inc/Engine/Admin/Settings/Page.php:1301
msgid ""
"Specify user agent strings that should never see cached pages (one per line)"
msgstr ""
"Specifică șirurile de agenți utilizator care nu ar trebui să vadă niciodată "
"pagini memorate în cache (unul pe linie)"

#: inc/Engine/Admin/Settings/Page.php:1302
msgid "Use (.*) wildcards to detect parts of UA strings."
msgstr "Folosește metacaractere (.*) pentru a detecta părți din șirurile UA."

#: inc/Engine/Admin/Settings/Page.php:1311
msgid ""
"Specify URLs you always want purged from cache whenever you update any post "
"or page (one per line)"
msgstr ""
"Specifică URL-urile care vrei să fie curățate întotdeauna din cache ori de "
"câte ori actualizezi un articol sau o pagină (unul pe linie)"

#: inc/Engine/Admin/Settings/Page.php:1320
msgid "Specify query strings for caching (one per line)"
msgstr ""
"Specifică șirurile de interogare pentru memorare în cache (unul pe linie)"

#: inc/Engine/Admin/Settings/Page.php:1346
msgid "Optimize, reduce bloat"
msgstr "Optimizează, reduce dimensiunea"

#: inc/Engine/Admin/Settings/Page.php:1353
msgid "Post Cleanup"
msgstr "Curățare articole"

#: inc/Engine/Admin/Settings/Page.php:1355
msgid ""
"Post revisions and drafts will be permanently deleted. Do not use this "
"option if you need to retain revisions or drafts."
msgstr ""
"Reviziile și ciornele articolelor vor fi șterse definitiv. Nu folosi această"
" opțiune dacă trebuie să păstrezi reviziile sau ciornele."

#: inc/Engine/Admin/Settings/Page.php:1360
msgid "Comments Cleanup"
msgstr "Curățare comentarii"

#: inc/Engine/Admin/Settings/Page.php:1362
msgid "Spam and trashed comments will be permanently deleted."
msgstr ""
"Comentariile spam și comentariile aruncate la gunoi vor fi șterse definitiv."

#: inc/Engine/Admin/Settings/Page.php:1366
msgid "Transients Cleanup"
msgstr "Curățare tranzienți"

#: inc/Engine/Admin/Settings/Page.php:1368
msgid ""
"Transients are temporary options; they are safe to remove. They will be "
"automatically regenerated as your plugins require them."
msgstr ""
"Tranzienții sunt opțiuni temporare; pot fi înlăturați fără probleme. Vor fi "
"regenerați automat pe măsură ce modulele tale îi cer."

#: inc/Engine/Admin/Settings/Page.php:1372
msgid "Database Cleanup"
msgstr "Curățare bază de date"

#: inc/Engine/Admin/Settings/Page.php:1374
msgid "Reduces overhead of database tables"
msgstr "Reduce costul de funcționare a tabelelor din baza de date"

#: inc/Engine/Admin/Settings/Page.php:1378
msgid "Automatic Cleanup"
msgstr "Ștergere automată"

#. translators: %s is the number of revisions found in the database. It's a
#. formatted number, don't use %d.
#: inc/Engine/Admin/Settings/Page.php:1391
msgid "%s revision in your database."
msgid_plural "%s revisions in your database."
msgstr[0] "%s revizie în baza ta de date."
msgstr[1] "%s revizii în baza ta de date."
msgstr[2] "%s de revizii în baza ta de date."

#. translators: %s is the number of revisions found in the database. It's a
#. formatted number, don't use %d.
#: inc/Engine/Admin/Settings/Page.php:1401
msgid "%s draft in your database."
msgid_plural "%s drafts in your database."
msgstr[0] "%s ciornă în baza ta de date."
msgstr[1] "%s ciorne în baza ta de date."
msgstr[2] "%s de ciorne în baza ta de date."

#. translators: %s is the number of revisions found in the database. It's a
#. formatted number, don't use %d.
#: inc/Engine/Admin/Settings/Page.php:1411
msgid "%s trashed post in your database."
msgid_plural "%s trashed posts in your database."
msgstr[0] "%s articol aruncat la gunoi în baza ta de date."
msgstr[1] "%s articole aruncate la gunoi în baza ta de date."
msgstr[2] "%s de articole aruncate la gunoi în baza ta de date."

#. translators: %s is the number of revisions found in the database. It's a
#. formatted number, don't use %d.
#: inc/Engine/Admin/Settings/Page.php:1421
msgid "%s spam comment in your database."
msgid_plural "%s spam comments in your database."
msgstr[0] "%s comentariu spam în baza ta de date."
msgstr[1] "%s comentarii spam în baza ta de date."
msgstr[2] "%s de comentarii spam în baza ta de date."

#. translators: %s is the number of revisions found in the database. It's a
#. formatted number, don't use %d.
#: inc/Engine/Admin/Settings/Page.php:1431
msgid "%s trashed comment in your database."
msgid_plural "%s trashed comments in your database."
msgstr[0] "%s comentariu aruncat la gunoi în baza ta de date."
msgstr[1] "%s comentarii aruncate la gunoi în baza ta de date."
msgstr[2] "%s de comentarii aruncate la gunoi în baza ta de date."

#: inc/Engine/Admin/Settings/Page.php:1439
msgid "All transients"
msgstr "Toți tranzienții"

#. translators: %s is the number of revisions found in the database. It's a
#. formatted number, don't use %d.
#: inc/Engine/Admin/Settings/Page.php:1441
msgid "%s transient in your database."
msgid_plural "%s transients in your database."
msgstr[0] "%s tranzient în baza ta de date."
msgstr[1] "%s tranzienți în baza ta de date."
msgstr[2] "%s de tranzienți în baza ta de date."

#: inc/Engine/Admin/Settings/Page.php:1449
msgid "Optimize Tables"
msgstr "Optimizează tabelele"

#. translators: %s is the number of revisions found in the database. It's a
#. formatted number, don't use %d.
#: inc/Engine/Admin/Settings/Page.php:1451
msgid "%s table to optimize in your database."
msgid_plural "%s tables to optimize in your database."
msgstr[0] "%s tabel de optimizat în baza ta de date."
msgstr[1] "%s tabele de optimizat în baza ta de date."
msgstr[2] "%s de tabele de optimizat în baza ta de date."

#: inc/Engine/Admin/Settings/Page.php:1462
msgid "Schedule Automatic Cleanup"
msgstr "Programează ștergerea automată"

#: inc/Engine/Admin/Settings/Page.php:1474
msgid "Frequency"
msgstr "Frecvență"

#: inc/Engine/Admin/Settings/Page.php:1482
msgid "Daily"
msgstr "Zilnic"

#: inc/Engine/Admin/Settings/Page.php:1483
msgid "Weekly"
msgstr "Săptămânal"

#: inc/Engine/Admin/Settings/Page.php:1484
msgid "Monthly"
msgstr "Lunar"

#: inc/Engine/Admin/Settings/Page.php:1501
msgid "Integrate your CDN"
msgstr "Integrează-ți CDN-ul"

#: inc/Engine/Admin/Settings/Page.php:1513
msgid ""
"All URLs of static files (CSS, JS, images) will be rewritten to the CNAME(s)"
" you provide."
msgstr ""
"Toate URL-urile fișierelor statice (CSS, JS, imagini) vor fi rescrise în "
"CNAME-ul furnizat (CNAME-urile furnizate)."

#. translators: %1$s = opening link tag, %2$s = closing link tag.
#: inc/Engine/Admin/Settings/Page.php:1515
msgid ""
"Not required for services like Cloudflare and Sucuri. Please see our "
"available %1$sAdd-ons%2$s."
msgstr ""
"Nu este necesar pentru servicii cum ar fi Cloudflare și Sucuri. Te rog vezi "
"%1$ssuplimentele%2$s noastre disponibile."

#. translators: %1$s = opening em tag, %2$l = list of add-on name(s), %3$s =
#. closing em tag.
#: inc/Engine/Admin/Settings/Page.php:1561
msgid ""
"%1$s%2$l Add-on%3$s is currently enabled. Configuration of the CDN settings "
"is not required for %2$l to work on your site."
msgid_plural ""
"%1$s%2$l Add-ons%3$s are currently enabled. Configuration of the CDN "
"settings is not required for %2$l to work on your site."
msgstr[0] ""
"În prezent, este activat %1$s%2$l supliment%3$s. Configurarea setărilor CDN "
"nu este necesară pentru ca %2$l să funcționeze pe site-ul tău."
msgstr[1] ""
"În prezent, sunt activate %1$s%2$l suplimente%3$s. Configurarea setărilor "
"CDN nu este necesară pentru ca %2$l să funcționeze pe site-ul tău."
msgstr[2] ""
"În prezent, sunt activate %1$s%2$l de suplimente%3$s. Configurarea setărilor"
" CDN nu este necesară pentru ca %2$l să funcționeze pe site-ul tău."

#: inc/Engine/Admin/Settings/Page.php:1587
msgid "Enable Content Delivery Network"
msgstr "Activează rețeaua de livrare a conținutului"

#: inc/Engine/Admin/Settings/Page.php:1596
#: inc/Engine/CDN/RocketCDN/AdminPageSubscriber.php:159
msgid "CDN CNAME(s)"
msgstr "CNAME (CNAME-uri) CDN"

#: inc/Engine/Admin/Settings/Page.php:1597
#: inc/Engine/CDN/RocketCDN/AdminPageSubscriber.php:160
msgid "Specify the CNAME(s) below"
msgstr "Specifică CNAME-ul (CNAME-urile) mai jos"

#: inc/Engine/Admin/Settings/Page.php:1604
msgid ""
"Specify URL(s) of files that should not get served via CDN (one per line)."
msgstr ""
"Specifică URL-urile fișierelor care nu ar trebui să fie servite prin CDN "
"(unul pe linie)."

#: inc/Engine/Admin/Settings/Page.php:1605
msgid ""
"The domain part of the URL will be stripped automatically.<br>Use (.*) "
"wildcards to exclude all files of a given file type located at a specific "
"path."
msgstr ""
"Partea din URL care conține domeniul va fi eliminată automat.<br>Folosește "
"metacaracterele (.*) pentru a exclude toate fișierele unui tip de fișier dat"
" localizate pe o anumită cale."

#: inc/Engine/Admin/Settings/Page.php:1628
#: inc/Engine/Admin/Settings/Page.php:1636
msgid "Heartbeat"
msgstr "Heartbeat"

#: inc/Engine/Admin/Settings/Page.php:1629
msgid "Control WordPress Heartbeat API"
msgstr "Controlează WordPress Heartbeat API"

#: inc/Engine/Admin/Settings/Page.php:1637
msgid ""
"Reducing or disabling the Heartbeat API’s activity can help save some of "
"your server’s resources."
msgstr ""
"Reducând sau dezactivând activitățile API-ului Heartbeat te poate ajuta să "
"nu mai folosești o parte din resursele serverului tău."

#: inc/Engine/Admin/Settings/Page.php:1643
msgid "Reduce or disable Heartbeat activity"
msgstr "Reduce sau dezactivează activitățile Heartbeat"

#: inc/Engine/Admin/Settings/Page.php:1644
msgid ""
"Reducing activity will change Heartbeat frequency from one hit each minute "
"to one hit every 2 minutes."
msgstr ""
"Reducerea activității va modifica frecvența Heartbeat de la o vizită în "
"fiecare minut la o vizită la fiecare 2 minute."

#: inc/Engine/Admin/Settings/Page.php:1644
msgid ""
"Disabling Heartbeat entirely may break plugins and themes using this API."
msgstr ""
"Dezactivarea completă a Heartbeat poate întrerupe funcționarea modulelor și "
"temelor când de folosește acest API."

#: inc/Engine/Admin/Settings/Page.php:1658
msgid "Do not limit"
msgstr "Nu limita"

#: inc/Engine/Admin/Settings/Page.php:1659
msgid "Reduce activity"
msgstr "Reduce activitatea"

#: inc/Engine/Admin/Settings/Page.php:1660
msgid "Disable"
msgstr "Dezactivează"

#: inc/Engine/Admin/Settings/Page.php:1668
msgid "Control Heartbeat"
msgstr "Controlează Heartbeat"

#: inc/Engine/Admin/Settings/Page.php:1677
msgid "Behavior in backend"
msgstr "Comportament în partea administrativă a sitului"

#: inc/Engine/Admin/Settings/Page.php:1684
msgid "Behavior in post editor"
msgstr "Comportament în editor de articole"

#: inc/Engine/Admin/Settings/Page.php:1690
msgid "Behavior in frontend"
msgstr "Comportament în partea din față a sitului"

#: inc/Engine/Admin/Settings/Page.php:1709
#: views/settings/page-sections/tutorials.php:39
msgid "Add-ons"
msgstr "Suplimente"

#: inc/Engine/Admin/Settings/Page.php:1710
msgid "Add more features"
msgstr "Adaugă mai multe funcționalități"

#: inc/Engine/Admin/Settings/Page.php:1717
msgid "One-click Rocket Add-ons"
msgstr "Suplimente Rocket cu un singur clic"

#: inc/Engine/Admin/Settings/Page.php:1718
msgid ""
"One-Click Add-ons are features extending available options without "
"configuration needed. Switch the option \"on\" to enable from this screen."
msgstr ""
"Suplimentele cu un singur clic sunt funcționalități care extind opțiunile "
"disponibile fără a fi necesară o configurare. Comută opțiunea pe „pornit” "
"pentru a le activa din acest ecran."

#: inc/Engine/Admin/Settings/Page.php:1728
msgid "Rocket Add-ons"
msgstr "Suplimente Rocket"

#: inc/Engine/Admin/Settings/Page.php:1729
msgid "Rocket Add-ons are complementary features extending available options."
msgstr ""
"Suplimentele Rocket sunt funcționalități complementare care extind opțiunile"
" disponibile."

#: inc/Engine/Admin/Settings/Page.php:1740
msgid "User Cache"
msgstr "Cache utilizatori"

#: inc/Engine/Admin/Settings/Page.php:1746
msgid ""
"If you need to create a dedicated set of cache files for each logged-in "
"WordPress user, you must activate this add-on."
msgstr ""
"Dacă vrei să creezi un set dedicat de fișiere cache pentru fiecare "
"utilizator WordPress autentificat, trebuie să activezi acest supliment."

#. translators: %1$s = opening <a> tag, %2$s = closing </a> tag.
#: inc/Engine/Admin/Settings/Page.php:1748
msgid ""
"User cache is great when you have user-specific or restricted content on "
"your website.<br>%1$sLearn more%2$s"
msgstr ""
"Cache-ul pentru utilizatori este foarte bun când ai un anumit conținut "
"pentru utilizatori sau un conținut restricționat pe site.<br>%1$sÎnvață mai "
"mult%2$s"

#: inc/Engine/Admin/Settings/Page.php:1761
#: inc/Engine/Admin/Settings/Page.php:1930
msgid "Cloudflare"
msgstr "Cloudflare"

#: inc/Engine/Admin/Settings/Page.php:1767
msgid "Integrate your Cloudflare account with this add-on."
msgstr "Integrează-ți contul Cloudflare cu acest supliment."

#: inc/Engine/Admin/Settings/Page.php:1768
msgid ""
"Provide your account email, global API key, and domain to use options such "
"as clearing the Cloudflare cache and enabling optimal settings with WP "
"Rocket."
msgstr ""
"Furnizează emailul contului, cheia API globală și domeniul pentru a folosi "
"câteva opțiuni, cum ar fi ștergerea cache-ului Cloudflare și activarea "
"setărilor optime cu WP Rocket."

#. translators: %1$s = opening span tag, %2$s = closing span tag.
#: inc/Engine/Admin/Settings/Page.php:1771
msgid ""
"%1$sPlanning on using Automatic Platform Optimization (APO)?%2$s Just "
"activate the official Cloudflare plugin and configure it. WP Rocket will "
"automatically enable compatibility."
msgstr ""
"%1$sPlănuiești să folosești Automatic Platform Optimization (APO)?%2$s Doar "
"activează modulul oficial Cloudflare și configurează-l. WP Rocket va activa "
"automat compatibilitatea."

#: inc/Engine/Admin/Settings/Page.php:1819
msgid "Varnish"
msgstr "Varnish"

#: inc/Engine/Admin/Settings/Page.php:1825
msgid "If Varnish runs on your server, you must activate this add-on."
msgstr ""
"Dacă Varnish rulează pe serverul tău, trebuie să activezi acest supliment."

#. translators: %1$s = opening <a> tag, %2$s = closing </a> tag.
#: inc/Engine/Admin/Settings/Page.php:1827
msgid ""
"Varnish cache will be purged each time WP Rocket clears its cache to ensure "
"content is always up-to-date.<br>%1$sLearn more%2$s"
msgstr ""
"Cache-ul Varnish va fi șters de fiecare dată când WP Rocket își șterge "
"cache-ul, astfel conținutul este întotdeauna actualizat.<br>%1$sAflă mai "
"mult%2$s"

#: inc/Engine/Admin/Settings/Page.php:1862
msgid "WebP Compatibility"
msgstr "Compatibilitate cu WebP"

#: inc/Engine/Admin/Settings/Page.php:1868
msgid "Improve browser compatibility for WebP images."
msgstr "Îmbunătățește compatibilitatea navigatoarelor cu imaginile WebP."

#. translators: %1$s and %3$s = opening <a> tag, %2$s = closing </a> tag.
#: inc/Engine/Admin/Settings/Page.php:1872
msgid ""
"Enable this option if you would like WP Rocket to serve WebP images to "
"compatible browsers. Please note that WP Rocket cannot create WebP images "
"for you. To create WebP images we recommend %1$sImagify%2$s. %3$sMore "
"info%2$s"
msgstr ""
"Activează această opțiune dacă vrei ca WP Rocket să servească imagini WebP "
"în navigatoarele compatibile. Reține că WP Rocket nu poate crea imagini WebP"
" pentru tine. Pentru a crea imagini WebP recomandăm %1$sImagify%2$s. %3$sMai"
" multe informații%2$s"

#: inc/Engine/Admin/Settings/Page.php:1892
msgid "Clear the Sucuri cache when WP Rocket’s cache is cleared."
msgstr "Șterge cache-ul Sucuri când cache-ul WP Rocket este șters."

#: inc/Engine/Admin/Settings/Page.php:1895
msgid ""
"Provide your API key to clear the Sucuri cache when WP Rocket’s cache is "
"cleared."
msgstr ""
"Furnizează cheia ta API pentru a șterge cache-ul Sucuri când cache-ul WP "
"Rocket este șters."

#: inc/Engine/Admin/Settings/Page.php:1903
#: inc/Engine/Admin/Settings/Page.php:2047
msgid "Sucuri"
msgstr "Sucuri"

#: inc/Engine/Admin/Settings/Page.php:1909
msgid "Synchronize Sucuri cache with this add-on."
msgstr "Sincronizează cache-ul Sucuri cu acest supliment."

#: inc/Engine/Admin/Settings/Page.php:1947
msgid "Cloudflare credentials"
msgstr "Date de conectare Cloudflare"

#: inc/Engine/Admin/Settings/Page.php:1956
msgid "Cloudflare settings"
msgstr "Setări Cloudflare"

#: inc/Engine/Admin/Settings/Page.php:1970
msgctxt "Cloudflare"
msgid "Global API key:"
msgstr "Cheie API globală:"

#: inc/Engine/Admin/Settings/Page.php:1971
msgctxt "Cloudflare"
msgid "Find your API key"
msgstr "Găsește-ți cheia API"

#: inc/Engine/Admin/Settings/Page.php:1983
msgctxt "Cloudflare"
msgid "Account email"
msgstr "Email cont"

#: inc/Engine/Admin/Settings/Page.php:1992
msgctxt "Cloudflare"
msgid "Zone ID"
msgstr "ID zonă"

#: inc/Engine/Admin/Settings/Page.php:2002
msgid "Development mode"
msgstr "Mod dezvoltare"

#. translators: %1$s = link opening tag, %2$s = link closing tag.
#: inc/Engine/Admin/Settings/Page.php:2004
msgid ""
"Temporarily activate development mode on your website. This setting will "
"automatically turn off after 3 hours. %1$sLearn more%2$s"
msgstr ""
"Activează temporar modul dezvoltare pe site-ul tău web. Această setare va fi"
" oprită automat după 3 ore. %1$sAflă mai mult%2$s"

#: inc/Engine/Admin/Settings/Page.php:2012
msgid "Optimal settings"
msgstr "Setări optime"

#: inc/Engine/Admin/Settings/Page.php:2013
msgid ""
"Automatically enhances your Cloudflare configuration for speed, performance "
"grade and compatibility."
msgstr ""
"Îți îmbunătățește automat configurarea Cloudflare pentru viteză, grad de "
"performanță și compatibilitate."

#: inc/Engine/Admin/Settings/Page.php:2021
msgid "Relative protocol"
msgstr "Protocol relativ"

#: inc/Engine/Admin/Settings/Page.php:2022
msgid ""
"Should only be used with Cloudflare's flexible SSL feature. URLs of static "
"files (CSS, JS, images) will be rewritten to use // instead of http:// or "
"https://."
msgstr ""
"Ar trebui să fie folosit numai cu funcționalitatea SSL flexibil de la "
"Cloudflare. URL-urile fișierelor statice (CSS, JS, imagini) vor fi rescrise "
"pentru a folosi // în loc de http:// sau https://."

#: inc/Engine/Admin/Settings/Page.php:2060
msgid "Sucuri credentials"
msgstr "Date de conectare Sucuri"

#: inc/Engine/Admin/Settings/Page.php:2073
msgctxt "Sucuri"
msgid ""
"Firewall API key (for plugin), must be in format {32 characters}/{32 "
"characters}:"
msgstr ""
"Cheia API pentru firewall (a modulului) trebuie să aibă formatul {32 de "
"caractere}/{32 de caractere}:"

#: inc/Engine/Admin/Settings/Page.php:2074
msgctxt "Sucuri"
msgid "Find your API key"
msgstr "Găsește-ți cheia API"

#: inc/Engine/Admin/Settings/Settings.php:361
msgid ""
"Sucuri Add-on: The API key for the Sucuri firewall must be in format "
"<code>{32 characters}/{32 characters}</code>."
msgstr ""
"Supliment Sucuri: cheia API pentru firewall-ul Sucuri trebuie să fie în "
"formatul <code>{32 de caractere}/{32 de caractere}</code>."

#: inc/Engine/Admin/Settings/Settings.php:667
msgid ""
"Sorry! Adding /(.*) in Advanced Rules > Never Cache URL(s) was not saved "
"because it disables caching and optimizations for every page on your site."
msgstr ""
"Regret! Nu am salvat adăugarea  /(.*) în Reguli avansate > Nu memora "
"niciodată în cache URL-urile deoarece dezactivează cache-ul și optimizările "
"pentru fiecare pagină de pe site-ul tău."

#: inc/Engine/Admin/Settings/Subscriber.php:171
msgid "Import, Export, Rollback"
msgstr "Import, export, restaurare"

#: inc/Engine/Admin/Settings/Subscriber.php:196
#: views/settings/page-sections/imagify.php:14
msgid "Image Optimization"
msgstr "Optimizare imagini"

#: inc/Engine/Admin/Settings/Subscriber.php:197
msgid "Compress your images"
msgstr "Comprimă-ți imaginile"

#: inc/Engine/Admin/Settings/Subscriber.php:214
#: views/settings/page-sections/tutorials.php:48
msgid "Tutorials"
msgstr "Tutoriale"

#: inc/Engine/Admin/Settings/Subscriber.php:215
msgid "Getting started and how to videos"
msgstr "Noțiuni de bază și cum să vezi videourile"

#: inc/Engine/Cache/PurgeExpired/Subscriber.php:75
msgid "WP Rocket Expired Cache Interval"
msgstr "Interval de cache expirat WP Rocket"

#: inc/Engine/Cache/WPCache.php:337
msgid "WP_CACHE value"
msgstr "Valoare WP_CACHE"

#: inc/Engine/Cache/WPCache.php:354
msgid "Cache"
msgstr "Cache"

#: inc/Engine/Cache/WPCache.php:358
msgid ""
"The WP_CACHE constant needs to be set to true for WP Rocket cache to work "
"properly"
msgstr ""
"Pentru ca WP Rocket să funcționeze așa cum trebuie, constanta WP_CACHE "
"trebuie să fie setată la „true”."

#: inc/Engine/Cache/WPCache.php:367
msgid "WP_CACHE is set to true"
msgstr "WP_CACHE este setată la „true”"

#: inc/Engine/Cache/WPCache.php:375
msgid "WP_CACHE is not set"
msgstr "WP_CACHE nu este setată"

#: inc/Engine/Cache/WPCache.php:383
msgid "WP_CACHE is set to false"
msgstr "WP_CACHE este setată la „false”"

#: inc/Engine/CDN/RocketCDN/AdminPageSubscriber.php:90
msgid "Next Billing Date"
msgstr "Următoarea dată de facturare"

#: inc/Engine/CDN/RocketCDN/AdminPageSubscriber.php:99
msgid "No Subscription"
msgstr "Niciun abonament"

#: inc/Engine/CDN/RocketCDN/AdminPageSubscriber.php:135
msgid "Your RocketCDN subscription is currently active."
msgstr "Abonamentul tău RocketCDN este în prezent activ."

#. translators: %1$s = opening <code> tag, %2$s = CDN URL, %3$s = closing
#. </code> tag.
#: inc/Engine/CDN/RocketCDN/AdminPageSubscriber.php:141
msgid "To use RocketCDN, replace your CNAME with %1$s%2$s%3$s."
msgstr "Pentru a folosi RocketCDN, înlocuiește CNAME cu %1$s%2$s%3$s."

#. translators: %1$is = opening link tag, %2$s = closing link tag.
#: inc/Engine/CDN/RocketCDN/AdminPageSubscriber.php:152
#: inc/Engine/CDN/RocketCDN/NoticesSubscriber.php:230
#: inc/Engine/CDN/RocketCDN/NoticesSubscriber.php:334
msgid "%1$sMore Info%2$s"
msgstr "%1$sMai multe informații%2$s"

#: inc/Engine/CDN/RocketCDN/APIClient.php:134
msgid ""
"We could not fetch the current price because RocketCDN API returned an "
"unexpected error code."
msgstr ""
"Nu am putut să preluăm prețul actual deoarece RocketCDN API a returnat un "
"cod de eroare neașteptat."

#: inc/Engine/CDN/RocketCDN/APIClient.php:140
msgid "RocketCDN is not available at the moment. Please retry later."
msgstr ""
"RocketCDN nu este disponibil în acest moment. Te rog încearcă mai târziu."

#: inc/Engine/CDN/RocketCDN/APIClient.php:177
msgid "RocketCDN cache purge failed: Missing identifier parameter."
msgstr ""
"Ștergerea cache-ului RocketCDN a eșuat: lipsește parametrul de identificare."

#: inc/Engine/CDN/RocketCDN/APIClient.php:186
msgid "RocketCDN cache purge failed: Missing user token."
msgstr ""
"Ștergerea cache-ului RocketCDN a eșuat: lipsește tokenul utilizatorului."

#: inc/Engine/CDN/RocketCDN/APIClient.php:212
msgid ""
"RocketCDN cache purge failed: The API returned an unexpected response code."
msgstr ""
"Ștergerea cache-ului RocketCDN a eșuat: API-ul a returnat un cod de răspuns "
"neașteptat."

#: inc/Engine/CDN/RocketCDN/APIClient.php:221
msgid "RocketCDN cache purge failed: The API returned an empty response."
msgstr ""
"Ștergerea cache-ului RocketCDN a eșuat: API-ul a returnat un răspuns gol."

#: inc/Engine/CDN/RocketCDN/APIClient.php:230
msgid "RocketCDN cache purge failed: The API returned an unexpected response."
msgstr ""
"Ștergerea cache-ului RocketCDN a eșuat: API-ul a returnat un răspuns "
"neașteptat."

#. translators: %s = message returned by the API.
#: inc/Engine/CDN/RocketCDN/APIClient.php:239
msgid "RocketCDN cache purge failed: %s."
msgstr "Ștergerea cache-ului RocketCDN a eșuat: %s."

#: inc/Engine/CDN/RocketCDN/APIClient.php:247
msgid "RocketCDN cache purge successful."
msgstr "Ștergerea cache-ului RocketCDN s-a făcut cu succes."

#: inc/Engine/CDN/RocketCDN/RESTSubscriber.php:137
#: tests/Integration/inc/Engine/CDN/RocketCDN/RESTSubscriber/enable.php:72
#: tests/Integration/inc/Engine/CDN/RocketCDN/RESTSubscriber/registerEnableRoute.php:302
msgid "RocketCDN enabled"
msgstr "RocketCDN activat"

#: inc/Engine/CDN/RocketCDN/RESTSubscriber.php:160
#: tests/Integration/inc/Engine/CDN/RocketCDN/RESTSubscriber/disable.php:31
#: tests/Integration/inc/Engine/CDN/RocketCDN/RESTSubscriber/registerDisableRoute.php:147
msgid "RocketCDN disabled"
msgstr "RocketCDN dezactivat"

#. Translators: %s = date formatted using date_i18n() and get_option(
#. 'date_format' ).
#: inc/Engine/CDN/RocketCDN/views/cta-big.php:27
msgid "Valid until %s only!"
msgstr "Valid numai până la %s!"

#: inc/Engine/CDN/RocketCDN/views/cta-big.php:36
msgid "Speed up your website thanks to:"
msgstr "Accelerează-ți site-ul web cu ajutorul:"

#. translators: %1$s = opening strong tag, %2$s = closing strong tag.
#: inc/Engine/CDN/RocketCDN/views/cta-big.php:42
msgid ""
"High performance Content Delivery Network (CDN) with %1$sunlimited "
"bandwidth%2$s"
msgstr ""
"Rețea pentru livrarea conținutului (CDN) de înaltă performanță, cu "
"%1$slățime de bandă nelimitată%2$s"

#. translators: %1$s = opening strong tag, %2$s = closing strong tag.
#: inc/Engine/CDN/RocketCDN/views/cta-big.php:48
msgid ""
"Easy configuration: the %1$sbest CDN settings%2$s are automatically applied"
msgstr ""
"Configurare ușoară: %1$scele mai bune setări pentru CDN%2$s sunt aplicate "
"automat"

#. translators: %1$s = opening strong tag, %2$s = closing strong tag.
#: inc/Engine/CDN/RocketCDN/views/cta-big.php:54
msgid ""
"WP Rocket integration: the CDN option is %1$sautomatically configured%2$s in"
" our plugin"
msgstr ""
"Integrare WP Rocket: opțiunea CDN este %1$sconfigurată automat%2$s în "
"modulul nostru"

#: inc/Engine/CDN/RocketCDN/views/cta-big.php:58
msgid "Learn more about RocketCDN"
msgstr "Află mai multe despre RocketCDN"

#. translators: %1$s = discounted price, %2$s = regular price.
#: inc/Engine/CDN/RocketCDN/views/cta-big.php:65
msgid ""
"*$%1$s/month for 12 months then $%2$s/month. You can cancel your "
"subscription at any time."
msgstr ""
"*%1$s $/lună în primele 12 luni, apoi %2$s $/lună. Poți anula oricând "
"abonamentul."

#: inc/Engine/CDN/RocketCDN/views/cta-big.php:86
msgid "Billed monthly"
msgstr "Facturare lunară"

#: inc/Engine/CDN/RocketCDN/views/cta-big.php:87
msgid "Get Started"
msgstr "Începe"

#: inc/Engine/CDN/RocketCDN/views/cta-big.php:92
msgid "Reduce this banner"
msgstr "Micșorează acest banner"

#: inc/Engine/CDN/RocketCDN/views/cta-small.php:17
msgid ""
"Speed up your website with RocketCDN, WP Rocket’s Content Delivery Network."
msgstr ""
"Accelerează-ți site-ul web cu RocketCDN, rețeaua de livrare conținut oferită"
" de WP Rocket."

#: inc/Engine/CDN/RocketCDN/views/cta-small.php:20
#: inc/Engine/CDN/RocketCDN/views/promote-notice.php:13
msgid "Learn More"
msgstr "Află mai mult"

#: inc/Engine/CDN/RocketCDN/views/dashboard-status.php:23
msgid "RocketCDN is unavailable on local domains and staging sites."
msgstr ""
"RocketCDN nu este disponibil pentru domenii locale și site-uri în pregătire "
"sau în testare."

#: inc/Engine/CDN/RocketCDN/views/dashboard-status.php:32
msgid "Get RocketCDN"
msgstr "Ia RocketCDN"

#: inc/Engine/CDN/RocketCDN/views/promote-notice.php:11
msgid "New!"
msgstr "Nou!"

#: inc/Engine/CDN/RocketCDN/views/promote-notice.php:12
msgid ""
"Speed up your website with RocketCDN, WP Rocket’s Content Delivery Network!"
msgstr ""
"Accelerează-ți site-ul web cu RocketCDN, rețeaua de livrare conținut oferită"
" de WP Rocket!"

#: inc/Engine/Common/JobManager/Cron/Subscriber.php:180
msgid "WP Rocket process pending jobs"
msgstr "WP Rocket procesează sarcini în așteptare"

#: inc/Engine/Common/JobManager/Cron/Subscriber.php:194
msgid "WP Rocket clear failed jobs"
msgstr "WP Rocket șterge sarcinile eșuate"

#: inc/Engine/Common/JobManager/Cron/Subscriber.php:206
msgid "WP Rocket process on submit jobs"
msgstr "WP Rocket procesează trimiterea sarcinilor"

#: inc/Engine/Common/Queue/RUCSSQueueRunner.php:265
msgid "Every minute"
msgstr "În fiecare minut"

#: inc/Engine/CriticalPath/Admin/Admin.php:264
msgid "Regenerate Critical Path CSS"
msgstr "Regenerează CSS-ul de cale critic"

#: inc/Engine/CriticalPath/Admin/Post.php:144
#: views/cpcss/metabox/generate.php:47
msgid "Generate Specific CPCSS"
msgstr "Generează un anumit CSS de cale critic"

#: inc/Engine/CriticalPath/Admin/Post.php:145
#: views/cpcss/metabox/generate.php:45
msgid "Regenerate specific CPCSS"
msgstr "Regenerează un anumit CSS de cale critic"

#: inc/Engine/CriticalPath/Admin/Post.php:216
msgid "This feature is not available for non-public post types."
msgstr ""
"Această funcționalitate este disponibilă pentru tipurile de articol care nu "
"sunt publice."

#: inc/Engine/CriticalPath/Admin/Post.php:219
msgid "%l to use this feature."
msgstr "%l pentru a folosi această funcționalitate."

#. translators: %s = post type.
#: inc/Engine/CriticalPath/Admin/Post.php:222
msgid "Publish the %s"
msgstr "Publică %s"

#. translators: %s = post type.
#: inc/Engine/CriticalPath/Admin/Post.php:223
msgid "Enable Load CSS asynchronously in WP Rocket settings"
msgstr "Activează Încarcă fișierele CSS asincron în setările WP Rocket"

#: inc/Engine/CriticalPath/Admin/Post.php:224
msgid "Enable Load CSS asynchronously in the options above"
msgstr "Activează Încarcă fișierele CSS asincron în opțiunile de mai sus"

#. translators: %1$s = type of content, %2$s = error message.
#: inc/Engine/CriticalPath/APIClient.php:64
msgid "Critical CSS for %1$s not generated. Error: %2$s"
msgstr "CSS-ul critic pentru %1$s nu a fost generat. Eroare: %2$s"

#. translators: %s = item URL.
#: inc/Engine/CriticalPath/APIClient.php:170
msgid ""
"Critical CSS for %1$s on mobile not generated. Error: The API returned an "
"empty response."
msgstr ""
"CSS-ul critic pentru %1$s nu a fost generat pentru dispozitive mobile. "
"Eroare: API-ul a returnat un răspuns gol."

#. translators: %s = item URL.
#: inc/Engine/CriticalPath/APIClient.php:173
msgid ""
"Critical CSS for %1$s not generated. Error: The API returned an empty "
"response."
msgstr ""
"CSS-ul critic pentru %1$s nu a fost generat. Eroare: API-ul a returnat un "
"răspuns gol."

#. translators: %s = item URL.
#: inc/Engine/CriticalPath/APIClient.php:185
msgid "Critical CSS for %1$s on mobile not generated."
msgstr ""
"CSS-ul critic pentru %1$s nu a fost generat pentru dispozitive mobile."

#. translators: %s = item URL.
#. translators: %1$s = item URL or item type.
#: inc/Engine/CriticalPath/APIClient.php:187
#: inc/Engine/CriticalPath/ProcessorService.php:194
msgid "Critical CSS for %1$s not generated."
msgstr "CSS-ul critic pentru %1$s nu a fost generat."

#. translators: %s = URL.
#: inc/Engine/CriticalPath/APIClient.php:195
msgid ""
"Critical CSS for %1$s on mobile not generated. Error: The API returned an "
"invalid response code."
msgstr ""
"CSS-ul critic pentru %1$s nu a fost generat pentru dispozitive mobile. "
"Eroare: API-ul a returnat un cod de răspuns invalid."

#. translators: %s = URL.
#: inc/Engine/CriticalPath/APIClient.php:197
msgid ""
"Critical CSS for %1$s not generated. Error: The API returned an invalid "
"response code."
msgstr ""
"CSS-ul critic pentru %1$s nu a fost generat. Eroare: API-ul a returnat un "
"cod de răspuns invalid."

#. translators: %1$s = error message.
#: inc/Engine/CriticalPath/APIClient.php:205
#: inc/Engine/CriticalPath/ProcessorService.php:201
msgid "Error: %1$s"
msgstr "Eroare: %1$s"

#: inc/Engine/CriticalPath/CriticalCSSSubscriber.php:177
msgid "Critical CSS generation is currently running."
msgstr "Generarea CSS-ului critic rulează acum."

#. Translators: %1$s = opening link tag, %2$s = closing link tag.
#: inc/Engine/CriticalPath/CriticalCSSSubscriber.php:182
msgid "Go to the %1$sWP Rocket settings%2$s page to track progress."
msgstr "Mergi la pagina %1$sSetări WP Rocket%2$s pentru a urmări progresul."

#. Translators: %1$d = number of critical CSS generated, %2$d = total number
#. of critical CSS to generate.
#: inc/Engine/CriticalPath/CriticalCSSSubscriber.php:397
msgid ""
"Critical CSS generation is currently running: %1$d of %2$d page types "
"completed. (Refresh this page to view progress)"
msgstr ""
"Generarea CSS-ului critic rulează acum: %1$d din %2$d tipuri de pagină au "
"fost finalizate. (Reîmprospătează această pagină pentru a vedea progresul.)"

#. Translators: %1$d = number of critical CSS generated, %2$d = total number
#. of critical CSS to generate.
#: inc/Engine/CriticalPath/CriticalCSSSubscriber.php:473
msgid "Critical CSS generation finished for %1$d of %2$d page types."
msgstr ""
"Generarea CSS-ului critic s-a terminat pentru %1$d din %2$d tipuri de "
"pagină."

#: inc/Engine/CriticalPath/CriticalCSSSubscriber.php:480
msgid "Critical CSS generation encountered one or more errors."
msgstr "Generarea CSS-ului critic a întâmpinat una sau mai multe erori."

#: inc/Engine/CriticalPath/CriticalCSSSubscriber.php:480
msgid "Learn more."
msgstr "Află mai mult."

#. translators: %1$ = opening bold tag, %2$ = closing bold tag.
#: inc/Engine/CriticalPath/CriticalCSSSubscriber.php:841
msgid ""
"We highly recommend the %1$supdated Remove Unused CSS%2$s for a better CSS "
"optimization. Load CSS Asynchronously is always available as a back-up."
msgstr ""
"Recomandăm cu insistență %1$ssă folosești Înlătură CSS-ul neutilizat%2$s "
"pentru o optimizare mai bună a CSS-ului. Încarcă asincron CSS-ul este mereu "
"disponibilă ca o alternativă de rezervă."

#: inc/Engine/CriticalPath/CriticalCSSSubscriber.php:846
msgid "Stay with the old option"
msgstr "Rămâi la opțiunea veche"

#. translators: %s = item URL.
#: inc/Engine/CriticalPath/DataManager.php:68
msgid ""
"Critical CSS for %1$s on mobile not generated. Error: The destination folder"
" could not be created."
msgstr ""
"CSS-ul critic pentru %1$s pe dispozitive mobile nu a fost generat. Eroare: "
"dosarul de destinație nu a putut fi creat."

#. translators: %s = item URL.
#: inc/Engine/CriticalPath/DataManager.php:71
msgid ""
"Critical CSS for %1$s not generated. Error: The destination folder could not"
" be created."
msgstr ""
"CSS-ul critic pentru %1$s nu a fost generat. Eroare: dosarul de destinație "
"nu a putut fi creat."

#: inc/Engine/CriticalPath/DataManager.php:106
msgid "Critical CSS file for mobile does not exist"
msgstr "Fișierul CSS critic pentru dispozitive mobile nu există"

#: inc/Engine/CriticalPath/DataManager.php:108
msgid "Critical CSS file does not exist"
msgstr "Fișierul CSS critic nu există"

#: inc/Engine/CriticalPath/DataManager.php:120
msgid "Critical CSS file for mobile cannot be deleted"
msgstr "Fișierul CSS critic pentru dispozitive mobile nu poate fi șters"

#: inc/Engine/CriticalPath/DataManager.php:122
msgid "Critical CSS file cannot be deleted"
msgstr "Fișierul CSS critic nu poate fi șters"

#. translators: %1$s = item URL or item type.
#: inc/Engine/CriticalPath/ProcessorService.php:187
msgid "Mobile Critical CSS for %1$s not generated."
msgstr "CSS-ul critic mobil pentru %1$s nu a fost generat."

#. translators: %1$s = Item URL or item type.
#: inc/Engine/CriticalPath/ProcessorService.php:228
msgid "Critical CSS for %s in progress."
msgstr "CSS-ul critic pentru %s este în desfășurare."

#. translators: %1$s = Item URL or item type.
#: inc/Engine/CriticalPath/ProcessorService.php:262
msgid "Mobile Critical CSS for %s generated."
msgstr "CSS-ul critic mobil pentru %s a fost generat."

#. translators: %1$s = Item URL or item type.
#: inc/Engine/CriticalPath/ProcessorService.php:273
msgid "Critical CSS for %s generated."
msgstr "CSS-ul critic pentru %s a fost generat."

#: inc/Engine/CriticalPath/ProcessorService.php:295
msgid "Critical CSS file deleted successfully."
msgstr "Fișierul CSS critic a fost șters cu succes."

#. translators: %1$s = Item URL or item type.
#: inc/Engine/CriticalPath/ProcessorService.php:317
msgid "Mobile Critical CSS for %1$s timeout. Please retry a little later."
msgstr ""
"CSS-ul critic mobil pentru %1$s a expirat. Te rog încearcă un pic mai "
"târziu."

#. translators: %1$s = Item URL or item type.
#: inc/Engine/CriticalPath/ProcessorService.php:330
msgid "Critical CSS for %1$s timeout. Please retry a little later."
msgstr ""
"CSS-ul critic pentru %1$s a expirat. Te rog încearcă un pic mai târziu."

#: inc/Engine/CriticalPath/RESTWP.php:141
msgid "Mobile CPCSS generation not enabled."
msgstr ""
"Generarea CSS-ului de cale critic pentru dispozitive mobile nu este "
"activată."

#: inc/Engine/CriticalPath/RESTWPPost.php:36
#: inc/Engine/CriticalPath/RESTWPPost.php:69
msgid "Requested post does not exist."
msgstr "Articolul cerut nu există."

#: inc/Engine/CriticalPath/RESTWPPost.php:46
msgid "Cannot generate CPCSS for unpublished post."
msgstr "Nu pot genera CSS-ul de cale critic pentru articole nepublicate."

#: inc/Engine/HealthCheck/HealthCheck.php:142
msgid "Scheduled Cache Purge"
msgstr "Ștergere cache programată"

#: inc/Engine/HealthCheck/HealthCheck.php:143
msgid "Scheduled Database Optimization"
msgstr "Optimizare bază de date programată"

#: inc/Engine/HealthCheck/HealthCheck.php:144
msgid "Database Optimization Process"
msgstr "Proces de optimizare a bazei de date"

#: inc/Engine/HealthCheck/HealthCheck.php:145
msgctxt "noun"
msgid "Preload"
msgstr "Pre-încărcare"

#: inc/Engine/HealthCheck/HealthCheck.php:146
msgid "Critical Path CSS Generation Process"
msgstr "Proces de generare CSS de cale critic"

#. translators: %1$s = <strong>, %2$s = price, %3$s = </strong>.
#: inc/Engine/License/Renewal.php:76
msgid "Renew before it is too late, you will only pay %1$s%2$s%3$s!"
msgstr ""
"Reînnoiește înainte de a fi prea târziu, vei plăti numai %1$s%2$s%3$s!"

#. translators: %1$s = <strong>, %2$s = discount, %3$s = </strong>,%4$s =
#. <strong>, %5$s = price, %6$s=</strong>.
#: inc/Engine/License/Renewal.php:85
msgid ""
"Renew with a %1$s%2$s discount%3$s before it is too late, you will only pay "
"%4$s%5$s%6$s!"
msgstr ""
"Reînnoiește cu o %2$sreducere%3$s de %1$s înainte de a fi prea târziu, vei "
"plăti numai %4$s%5$s%6$s!"

#. translators: %1$s = <strong>, %2$s = </strong>, %3$s = price.
#: inc/Engine/License/Renewal.php:139
msgid "Renew your license for 1 year now at %1$s%3$s%2$s."
msgstr "Reînnoiește-ți licența acum pentru încă 1 an, plătești %1$s%3$s%2$s."

#. translators: %1$s = <strong>, %2$s = </strong>, %3$s = discount percentage,
#. %4$s = price.
#: inc/Engine/License/Renewal.php:152
msgid ""
"Renew your license for 1 year now and get %1$s%3$s OFF%2$s immediately: you "
"will only pay %1$s%4$s%2$s!"
msgstr ""
"Reînnoiește-ți licența pentru încă 1 an și beneficiezi imediat de o "
"%3$sREDUCERE%2$s de %1$s: vei plăți numai %1$s%4$s%2$s!"

#. translators: %1$s = <strong>, %2$s = </strong>, %3$s = discount price.
#: inc/Engine/License/Renewal.php:218
msgid "Renew before it is too late, you will pay %1$s%3$s%2$s."
msgstr ""
"Reînnoiește înainte de a fi prea târziu, vei plăti numai %1$s%3$s%2$s."

#. translators: %1$s = <strong>, %2$s = discount percentage, %3$s = </strong>,
#. %4$s = discount price.
#: inc/Engine/License/Renewal.php:227
msgid ""
"Renew with a %1$s%2$s discount%3$s before it is too late, you will only pay "
"%1$s%4$s%3$s!"
msgstr ""
"Reînnoiește licența cu o %1$sreducere de %2$s%3$s înainte de a fi prea "
"târziu, vei plăti numai %1$s%4$s%3$s!"

#. translators: %1$s = <a>, %2$s = </a>.
#: inc/Engine/License/Renewal.php:542
msgid ""
"You need a valid license to continue using this feature. %1$sRenew now%2$s "
"before losing access."
msgstr ""
"Pentru a folosi în continuare această funcționalitate, ai nevoie de o "
"licență validă. %1$sReînnoiește acum%2$s înainte de a pierde accesul."

#. translators: %1$s = <a>, %2$s = </a>.
#: inc/Engine/License/Renewal.php:563
msgid "You need an active license to enable this option. %1$sRenew now%2$s."
msgstr ""
"Pentru a activa această opțiune, ai nevoie de o licență activă. "
"%1$sReînnoiește acum%2$s."

#. translators: %1$s = <a>, %2$s = </a>.
#: inc/Engine/License/Renewal.php:591
msgid "You need an active license to enable this option. %1$sMore info%2$s."
msgstr ""
"Pentru a activa această opțiune, ai nevoie de o licență activă. %1$sMai "
"multe infomații%2$s."

#. translators: %1$s = promotion name, %2$s = <br>, %3$s = <strong>, %4$s =
#. promotion discount percentage, %5$s = </strong>.
#: inc/Engine/License/Upgrade.php:251
msgid ""
"Take advantage of %1$s to speed up more websites:%2$s get a %3$s%4$s off%5$s"
" for %3$supgrading your license to Plus or Infinite!%5$s"
msgid_plural ""
"Take advantage of %1$s to speed up more websites:%2$s get a %3$s%4$s off%5$s"
" for %3$supgrading your license to Infinite!%5$s"
msgstr[0] ""
"Profită de %1$s ca să accelerezi mai multe site-uri web:%2$s ai o "
"%3$sreducere de %4$s%5$s ca să-ți actualizezi licența la Plus sau la "
"Infinit! %5$s"
msgstr[1] ""
"Profită de %1$s ca să accelerezi mai multe site-uri web:%2$s ai o "
"%3$sreducere de %4$s%5$s ca să-ți actualizezi licența la Infinit! %5$s"
msgstr[2] ""
"Profită de %1$s ca să accelerezi mai multe site-uri web:%2$s ai o "
"%3$sreducere de %4$s%5$s ca să-ți actualizezi licența la Infinit! %5$s"

#: inc/Engine/License/Upgrade.php:382 inc/Engine/License/Upgrade.php:405
msgid "Unlimited"
msgstr "Nelimitate"

#. translators: %s = promotion discount percentage.
#: inc/Engine/License/views/promo-banner.php:16
msgid "%s off"
msgstr "Reducere de %s"

#. translators: %s = promotion name.
#: inc/Engine/License/views/promo-banner.php:21
msgid "%s promotion is live!"
msgstr "Promoția %s este în desfășurare!"

#: inc/Engine/License/views/promo-banner.php:27
msgid "Hurry Up! Deal ends in:"
msgstr "Grăbește-te! Oferta se încheie în:"

#: inc/Engine/License/views/promo-banner.php:31
#: inc/Engine/License/views/renewal-soon-banner.php:14
msgid "Minutes"
msgstr "Minute"

#: inc/Engine/License/views/promo-banner.php:32
#: inc/Engine/License/views/renewal-soon-banner.php:15
msgid "Seconds"
msgstr "Secunde"

#: inc/Engine/License/views/promo-banner.php:34
#: inc/Engine/License/views/upgrade-section.php:11
msgid "Upgrade now"
msgstr "Actualizează acum"

#: inc/Engine/License/views/renewal-expired-banner-ocd-disabled.php:11
msgid "The Optimize CSS Delivery feature is disabled."
msgstr "Funcționalitatea Optimizează livrarea CSS este dezactivată."

#: inc/Engine/License/views/renewal-expired-banner-ocd-disabled.php:15
msgid ""
"You can no longer use the Remove Unused CSS or Load CSS asynchronously "
"options."
msgstr ""
"Nu mai poți să folosești opțiunile Înlătură CSS-ul neutilizat sau Încarcă "
"fișierele CSS asincron."

#. translators: %1$s = <strong>, %2$s = </strong>.
#: inc/Engine/License/views/renewal-expired-banner-ocd-disabled.php:20
msgid ""
"You need an %1$sactive license%2$s to keep optimizing your CSS delivery, "
"which addresses a PageSpeed Insights recommendation and improves your page "
"performance."
msgstr ""
"Ai nevoie de o %1$slicență activă%2$s pentru a optimiza în continuare "
"livrarea CSS-ului, o funcționalitate recomandată de PageSpeed Insights care "
"îmbunătățește performanțele paginilor."

#: inc/Engine/License/views/renewal-expired-banner-ocd-disabled.php:29
#: inc/Engine/License/views/renewal-expired-banner-ocd.php:39
#: inc/Engine/License/views/renewal-expired-banner.php:27
#: inc/Engine/License/views/renewal-soon-banner.php:31
msgid "Renew now"
msgstr "Reînnoiește acum"

#: inc/Engine/License/views/renewal-expired-banner-ocd.php:11
msgid "You will soon lose access to some features."
msgstr "În curând, nu vei mai avea acces la unele funcționalități."

#. translators: %1$s = <strong>, %2$s = </strong>.
#: inc/Engine/License/views/renewal-expired-banner-ocd.php:18
msgid ""
"You need an %1$sactive license to continue optimizing your CSS delivery%2$s."
msgstr ""
"Ai nevoie de o %1$slicență activă pentru a optimiza în continuare livrarea "
"CSS-ului%2$s."

#: inc/Engine/License/views/renewal-expired-banner-ocd.php:24
msgid ""
"The Remove Unused CSS and Load CSS asynchronously features are great options"
" to address the PageSpeed Insights recommendations and improve your website "
"performance."
msgstr ""
"Funcționalitățile Înlătură CSS-ul neutilizat și Încarcă fișierele CSS "
"asincron sunt opțiuni excelente pentru a satisface recomandările PageSpeed "
"Insights și pentru a îmbunătăți performanța pe site."

#. translators: %1$s = <strong>, %2$s = </strong>, %3$s = date.
#: inc/Engine/License/views/renewal-expired-banner-ocd.php:29
msgid "These features will be %1$sautomatically disabled on %3$s%2$s."
msgstr "Aceste funcționalități vor fi %1$sdezactivate automat pe %3$s%2$s."

#: inc/Engine/License/views/renewal-expired-banner.php:11
msgid "Your WP Rocket license is expired!"
msgstr "Licența ta WP Rocket a expirat!"

#. translators: %1$s = <strong>, %2$s = </strong>.
#: inc/Engine/License/views/renewal-expired-banner.php:18
msgid ""
"Your website could be much faster if it could take advantage of our %1$snew "
"features and enhancements%2$s. 🚀"
msgstr ""
"Site-ul tău web ar putea fi mult mai rapid dacă profiți de %1$snoile noastre"
" funcționalități și îmbunătățiri%2$s. 🚀"

#. translators: %1$s = <strong>, %2$s = </strong>.
#: inc/Engine/License/views/renewal-soon-banner.php:22
msgid ""
"Your %1$sWP Rocket license is about to expire%2$s: you will soon lose access"
" to product updates and support."
msgstr ""
"%1$sLicența ta WP este aproape de expirare%2$s: în curând nu vei mai avea "
"acces la actualizări și suport."

#: inc/Engine/License/views/upgrade-popin.php:12
msgid "Speed Up More Websites"
msgstr "Accelerezi mai multe site-uri web"

#. translators: %1$s = opening strong tag, %2$s = closing strong tag.
#: inc/Engine/License/views/upgrade-popin.php:19
msgid ""
"You can use WP Rocket on more websites by upgrading your license. To "
"upgrade, simply pay the %1$sprice difference%2$s between your current and "
"new licenses, as shown below."
msgstr ""
"Prin actualizarea licenței, poți folosi WP Rocket pe mai multe site-uri web."
" Pentru a o actualiza, plătești doar %1$sdiferența de preț%2$s între cea "
"deținută acum și licențele noi, așa cum arătăm mai jos."

#. translators: %1$s = opening strong tag, %2$s = closing strong tag.
#: inc/Engine/License/views/upgrade-popin.php:25
msgid ""
"%1$sN.B.%2$s: Upgrading your license does not change your expiration date"
msgstr ""
"%1$sAtenție%2$s: actualizarea licenței nu îți va modifica data de expirare"

#. translators: %s = price.
#: inc/Engine/License/views/upgrade-popin.php:35
msgid "Save $%s"
msgstr "Economisești %s $"

#. translators: %s = number of websites.
#: inc/Engine/License/views/upgrade-popin.php:48
msgid "%s websites"
msgstr "%s site-uri web"

#. translators: %s = license name.
#: inc/Engine/License/views/upgrade-popin.php:54
msgid "Upgrade to %s"
msgstr "Actualizează la %s"

#: inc/Engine/License/views/upgrade-section.php:11
msgid ""
"You can use WP Rocket on more websites by upgrading your license (you will "
"only pay the price difference between your current and new licenses)."
msgstr ""
"Prin actualizarea licenței, poți folosi WP Rocket pe mai multe site-uri web "
"(vei plăti doar diferența de preț între cea deținută acum și licențele noi)."

#. translators: %1$s = plugin name.
#: inc/Engine/Media/AboveTheFold/Admin/Controller.php:143
msgid "%1$s: Critical images cleared!"
msgstr "%1$s: am șters imaginile critice!"

#: inc/Engine/Media/Lazyload/AdminSubscriber.php:62
msgid "LazyLoad for images"
msgstr "Încărcare lentă pentru imagini"

#: inc/Engine/Media/Lazyload/AdminSubscriber.php:63
msgid "LazyLoad for iframes/videos"
msgstr "Încărcare lentă pentru iframe-uri/videouri"

#: inc/Engine/Media/Lazyload/CSS/Admin/Subscriber.php:48
msgid "LazyLoad CSS backgrounds"
msgstr "Fundaluri pentru încărcare lentă CSS"

#: inc/Engine/Optimization/DelayJS/Admin/SiteList.php:195
msgid "Analytics & Ads"
msgstr "Analitice și reclame"

#: inc/Engine/Optimization/DelayJS/Admin/SiteList.php:200
msgid "Plugins"
msgstr "Module"

#: inc/Engine/Optimization/DelayJS/Admin/SiteList.php:205
msgid "Themes"
msgstr "Teme"

#: inc/Engine/Optimization/DynamicLists/DynamicLists.php:101
msgid ""
"You need an active license to get the latest version of the lists from our "
"server."
msgstr ""
"Trebuie să ai o licență activă ca să obții ultima versiune a listelor de pe "
"serverul nostru."

#: inc/Engine/Optimization/DynamicLists/DynamicLists.php:116
msgid "Could not get updated lists from server."
msgstr "Nu am putut să preiau listele actualizate de pe server."

#: inc/Engine/Optimization/DynamicLists/DynamicLists.php:125
msgid "Lists are up to date."
msgstr "Listele sunt actualizate."

#: inc/Engine/Optimization/DynamicLists/DynamicLists.php:134
msgid "Could not update lists."
msgstr "Nu am putut să actualizez listele."

#: inc/Engine/Optimization/DynamicLists/DynamicLists.php:143
msgid "Lists are successfully updated."
msgstr "Am actualizat cu succes listele."

#: inc/Engine/Optimization/DynamicLists/ServiceProvider.php:72
msgid "Default Lists"
msgstr "Liste implicite"

#: inc/Engine/Optimization/DynamicLists/ServiceProvider.php:78
msgid "Delay JavaScript Execution Exclusion Lists"
msgstr "Listă cu excluderi pentru Întârzie executarea JavaScript"

#: inc/Engine/Optimization/DynamicLists/ServiceProvider.php:84
msgid "Incompatible plugins Lists"
msgstr "Liste cu module incompatibile"

#: inc/Engine/Optimization/Minify/AdminSubscriber.php:65
msgid "Minify/combine JavaScript"
msgstr "Minifică/combină JavaScript"

#: inc/Engine/Optimization/Minify/CSS/AdminSubscriber.php:150
msgid "Minify CSS"
msgstr "Minifică CSS-ul"

#. translators: %1$s = plugin name, %2$s = table name, %3$s = <a> open tag,
#. %4$s = </a> closing tag.
#: inc/Engine/Optimization/RUCSS/Admin/Settings.php:253
msgid ""
"%1$s: Could not create the %2$s table in the database which is necessary for"
" the Remove Unused CSS feature to work. Please reach out to %3$sour "
"support%4$s."
msgstr ""
"%1$s: nu am putut să creez tabelul %2$s în baza de date, el este necesar "
"pentru ca funcționalitatea Înlătură CSS-ul neutilizat să funcționeze. Te rog"
" să contactezi  %3$ssuportul nostru%4$s."

#. translators: %1$s = plugin name.
#: inc/Engine/Optimization/RUCSS/Admin/Subscriber.php:274
msgid "%1$s: Used CSS cache cleared!"
msgstr "%1$s: cache-ul pentru CSS-ul utilizat a fost șters!"

#. translators: %1$s = plugin name.
#: inc/Engine/Preload/Admin/Settings.php:57
msgid ""
"%1$s: The preload service is now active. After the initial preload it will "
"continue to cache all your pages whenever they are purged. No further action"
" is needed."
msgstr ""
"%1$s: acum serviciul preîncărcare este activ. După preîncărcarea inițială, "
"el va memora în continuare în cache toate paginile, ori de câte ori sunt "
"șterse. Nu este necesară nicio acțiune suplimentară."

#: inc/Engine/Preload/Cron/Subscriber.php:138
msgid "WP Rocket Preload pending jobs"
msgstr "Sarcini în așteptare pentru preîncărcare WP Rocket"

#: inc/Engine/Preload/Cron/Subscriber.php:166
msgid "WP Rocket Preload revert stuck failed jobs"
msgstr "Preîncărcare WP Rocket revine la sarcinile blocate și eșuate"

#: inc/Engine/Saas/Admin/AdminBar.php:73
#: inc/Engine/Saas/Admin/AdminBar.php:194
msgid "Clear Critical Images"
msgstr "Șterge imaginile critice"

#: inc/Engine/Saas/Admin/AdminBar.php:160
msgid "Clear Critical Images of this URL"
msgstr "Șterge imaginile critice pentru acest URL"

#: inc/Engine/Saas/Admin/AdminBar.php:163
msgid "Clear Used CSS of this URL"
msgstr "Șterge CSS-ul folosit pentru acest URL"

#: inc/Engine/Saas/Admin/AdminBar.php:193
msgid "Critical Images Cache"
msgstr "Cache imagini critice"

#: inc/Engine/Saas/Admin/AdminBar.php:197
msgid "Remove Used CSS Cache"
msgstr "Cache Înlătură CSS-ul neutilizat"

#. translators: %1$s = plugin name, %2$s = number of seconds.
#: inc/Engine/Saas/Admin/Notices.php:104
msgid ""
"%1$s: Please wait %2$s seconds. The Remove Unused CSS service is processing "
"your pages, the plugin is optimizing LCP and the images above the fold."
msgstr ""
"%1$s: te rog să aștepți %2$s secunde. Serviciul Înlătură CSS-ul neutilizat "
"îți procesează paginile, iar modulul optimizează LCP și imaginile de "
"deasupra, înainte de derulare."

#. translators: %1$s = plugin name, %2$s = number of URLs, %3$s = number of
#. seconds.
#: inc/Engine/Saas/Admin/Notices.php:147
msgid ""
"%1$s: The LCP element has been optimized, and the images above the fold were excluded from lazyload. The Used CSS of your homepage has been processed.\n"
"\t\t\t WP Rocket will continue to generate Used CSS for up to %2$s URLs per %3$s second(s)."
msgstr ""
"%1$s: elementul LCP a fost optimizat și imaginile de deasupra, înainte de derulare, au fost excluse din încărcarea lentă. CSS-ul folosit în prima ta pagină a fost procesat.\n"
"\t\t\t WP Rocket va continua să genereze CSS-ul folosit pentru până la %2$s URL-uri per %3$s secundă (secunde)."

#. translators: %1$s = opening link tag, %2$s = closing link tag.
#: inc/Engine/Saas/Admin/Notices.php:160
msgid "We suggest enabling %1$sPreload%2$s for the fastest results."
msgstr ""
"Pentru rezultate mult mai rapide, sugerăm să activezi %1$spreîncărcarea%2$s."

#. translators: %1$s = opening link tag, %2$s = closing link tag.
#: inc/Engine/Saas/Admin/Notices.php:170
msgid "To learn more about the process check our %1$sdocumentation%2$s."
msgstr ""
"Pentru a afla mai multe despre acest proces, %1$sconsultă documentația "
"noastră%2$s."

#: inc/Engine/Saas/Admin/Notices.php:236
msgid ""
"We couldn't generate the used CSS because you're using a nulled version of "
"WP Rocket. You need an active license to use the Remove Unused CSS feature "
"and further improve your website's performance."
msgstr ""
"Nu am putut să generez CSS-ul folosit deoarece folosești o versiune nevalidă"
" a WP Rocket. Pentru a folosi funcționalitatea Înlătură CSS-ul neutilizat și"
" pentru a îmbunătăți și mai mult performanța site-ului, ai nevoie de o "
"licență activă."

#. translators: %1$s = promo percentage.
#: inc/Engine/Saas/Admin/Notices.php:239
msgid "Click here to get a WP Rocket single license at %1$s off!"
msgstr ""
"Dă clic aici pentru a obține o singură licență WP Rocket cu o reducere de "
"%1$s!"

#. translators: %1$s = <a> open tag, %2$s = </a> closing tag.
#: inc/Engine/Saas/Admin/Notices.php:292
msgid ""
"It seems a security plugin or the server's firewall prevents WP Rocket from "
"accessing the SaaS features. IPs listed %1$shere in our documentation%2$s "
"should be added to your allowlists:"
msgstr ""
"Se pare că un modul pentru securitate sau firewall-ul serverului împiedică "
"modulul WP Rocket să acceseze funcționalitățile SaaS. IP-urile afișate "
"%1$saici în documentația noastră%2$s trebuie să fie adăugate la listele tale"
" cu permisiuni:"

#: inc/Engine/Saas/Admin/Notices.php:297
msgid "- In the security plugin, if you are using one"
msgstr "- În modulul pentru securitate, dacă folosești unul"

#: inc/Engine/Saas/Admin/Notices.php:298
msgid "- In the server's firewall. Your host can help you with this"
msgstr ""
"- În firewall-ul serverului. Serviciul tău de găzduire te poate ajuta cu "
"asta"

#: inc/functions/admin.php:21
msgid ""
"There seems to be an issue validating your license. Please see the error "
"message below."
msgid_plural ""
"There seems to be an issue validating your license. You can see the error "
"messages below."
msgstr[0] ""
"Se pare că există o problemă de validare cu licența ta. Te rog vezi mai jos "
"mesajul de eroare."
msgstr[1] ""
"Se pare că există o problemă de validare cu licența ta. Poți vedea mai jos "
"mesajele de eroare."
msgstr[2] ""
"Se pare că există o problemă de validare cu licența ta. Poți vedea mai jos "
"mesajele de eroare."

#: inc/functions/admin.php:361
msgid "Server type:"
msgstr "Tip de server:"

#: inc/functions/admin.php:370
msgid "PHP version number:"
msgstr "Număr versiune PHP:"

#: inc/functions/admin.php:379
msgid "WordPress version number:"
msgstr "Număr versiune WordPress:"

#: inc/functions/admin.php:388
msgid "WordPress multisite:"
msgstr "Multi-site WordPress:"

#: inc/functions/admin.php:397
msgid "Current theme:"
msgstr "Tema actuală:"

#: inc/functions/admin.php:406
msgid "Current site language:"
msgstr "Limba actuală a site-ului:"

#: inc/functions/admin.php:415
msgid "Active plugins:"
msgstr "Module active:"

#: inc/functions/admin.php:418
msgid "Plugin names of all active plugins"
msgstr "Numele tuturor modulelor active"

#: inc/functions/admin.php:424
msgid "Anonymized WP Rocket settings:"
msgstr "Setări WP Rocket făcute anonime:"

#: inc/functions/admin.php:427
msgid "Which WP Rocket settings are active"
msgstr "Care setări WP Rocket sunt active"

#: inc/functions/admin.php:433
msgid "WP Rocket license type"
msgstr "Tip de licență WP Rocket"

#: inc/functions/options.php:429
msgid "The provided license data are not valid."
msgstr "Datele furnizate pentru licență nu sunt valide."

#. Translators: %1$s = opening link tag, %2$s = closing link tag.
#: inc/functions/options.php:432
msgid "To resolve, please %1$scontact support%2$s."
msgstr "Pentru a rezolva, te rog %1$scontactează suportul%2$s."

#. Translators: %1$s = opening em tag, %2$s = closing em tag, %3$s = opening
#. link tag, %4$s closing link tag.
#: inc/functions/options.php:491 inc/functions/options.php:530
msgid ""
"License validation failed. Our server could not resolve the request from "
"your website."
msgstr ""
"Validarea licenței a eșuat. Serverul nostru nu a putut rezolva cererea de pe"
" site-ul tău web."

#. Translators: %1$s = opening em tag, %2$s = closing em tag, %3$s = opening
#. link tag, %4$s closing link tag.
#: inc/functions/options.php:491
msgid ""
"Try clicking %1$sValidate License%2$s below. If the error persists, follow "
"%3$sthis guide%4$s."
msgstr ""
"Încearcă să dai clic mai jos pe %1$sValidează licența%2$s. Dacă eroarea "
"persistă, %3$surmează acest ghid%4$s."

#. Translators: %1$s = opening link tag, %2$s = closing link tag.
#: inc/functions/options.php:507
msgid ""
"License validation failed. You may be using a nulled version of the plugin. "
"Please do the following:"
msgstr ""
"Validarea licenței a eșuat. Probabil folosești o versiune a modulului "
"nevalidă. Te rog să faci următoarele:"

#. Translators: %1$s = opening link tag, %2$s = closing link tag.
#: inc/functions/options.php:507 inc/functions/options.php:549
msgid "Login to your WP Rocket %1$saccount%2$s"
msgstr "Autentifică-te în %1$scontul%2$s tău WP Rocket"

#. Translators: %1$s = opening link tag, %2$s = closing link tag.
#: inc/functions/options.php:507 inc/functions/options.php:549
msgid "Download the zip file"
msgstr "Descarcă fișierul zip"

#. Translators: %1$s = opening link tag, %2$s = closing link tag.
#: inc/functions/options.php:507 inc/functions/options.php:549
msgid "Reinstall"
msgstr "Reinstalează"

#. Translators: %1$s = opening link tag, %2$s = closing link tag.
#: inc/functions/options.php:507
msgid ""
"If you do not have a WP Rocket account, please %1$spurchase a license%2$s."
msgstr "Dacă nu ai un cont WP Rocket, te rog %1$scumpără o licență%2$s."

#. Translators: %1$s = opening link tag, %2$s = closing link tag.
#: inc/functions/options.php:515
msgid ""
"License validation failed. This user account does not exist in our database."
msgstr ""
"Validarea licenței a eșuat. Acest cont de utilizator nu există în baza "
"noastră de date."

#. Translators: %1$s = opening link tag, %2$s = closing link tag.
#: inc/functions/options.php:515
msgid "To resolve, please contact support."
msgstr "Pentru a rezolva, te rog contactează suportul."

#. Translators: %1$s = opening link tag, %2$s = closing link tag.
#: inc/functions/options.php:523
msgid "License validation failed. This user account is blocked."
msgstr "Validarea licenței a eșuat. Acest cont de utilizator este blocat."

#. Translators: %1$s = opening link tag, %2$s = closing link tag.
#: inc/functions/options.php:523
msgid "Please see %1$sthis guide%2$s for more info."
msgstr "Pentru mai multe informații, te rog vezi %1$sacest ghid%2$s."

#. Translators: %1$s = opening em tag, %2$s = closing em tag, %3$s = opening
#. link tag, %4$s closing link tag.
#: inc/functions/options.php:530
msgid ""
"Try clicking %1$sSave Changes%2$s below. If the error persists, follow "
"%3$sthis guide%4$s."
msgstr ""
"Încearcă să dai clic pe %1$sSalvează modificările%2$s mai jos. Dacă eroarea "
"persistă, urmează %3$sacest ghid%4$s."

#. Translators: %1$s = opening link tag, %2$s = closing link tag.
#: inc/functions/options.php:543
msgid "Your license is not valid."
msgstr "Licența ta nu este validă."

#. Translators: %1$s = opening link tag, %2$s = closing link tag.
#: inc/functions/options.php:543
msgid "Make sure you have an active %1$sWP Rocket license%2$s."
msgstr "Asigură-te că ai o %1$slicență WP Rocket%2$s activă."

#. Translators: %1$s = opening link tag, %2$s = closing link tag, %3$s =
#. opening link tag.
#: inc/functions/options.php:545
msgid "You have added as many sites as your current license allows."
msgstr "Ai adăugat numărul de site-uri pe care le permite licența ta actuală."

#. Translators: %1$s = opening link tag, %2$s = closing link tag, %3$s =
#. opening link tag.
#: inc/functions/options.php:545
msgid ""
"Upgrade your %1$saccount%2$s or %3$stransfer your license%2$s to this "
"domain."
msgstr ""
"Actualizează-ți %1$scontul%2$s sau %3$stransferă-ți licența%2$s la acest "
"domeniu."

#. Translators: %1$s = opening link tag, %2$s = closing link tag.
#: inc/functions/options.php:547
msgid "This website is not allowed."
msgstr "Acest site web nu este permis."

#. Translators: %1$s = opening link tag, %2$s = closing link tag.
#: inc/functions/options.php:547
msgid "Please %1$scontact support%2$s."
msgstr "Te rog %1$scontactează suportul%2$s."

#. Translators: %1$s = opening link tag, %2$s = closing link tag.
#: inc/functions/options.php:549
msgid "This license key is not recognized."
msgstr "Această cheie de licență nu este recunoscută."

#. Translators: %1$s = opening link tag, %2$s = closing link tag.
#: inc/functions/options.php:549
msgid "If the issue persists, please %1$scontact support%2$s."
msgstr "Dacă problema persistă, te rog %1$scontactează suportul%2$s."

#. Translators: %s = error message returned.
#: inc/functions/options.php:555
msgid "License validation failed: %s"
msgstr "Validarea licenței a eșuat: %s"

#: inc/Logger/Logger.php:227 inc/Logger/Logger.php:257
msgid "The log file does not exist."
msgstr "Fișierul jurnal nu există."

#: inc/Logger/Logger.php:233 inc/Logger/Logger.php:263
msgid "The log file could not be read."
msgstr "Fișierul jurnal nu a putut fi citit."

#: inc/Logger/Logger.php:250
msgid "The logs are not saved into a file."
msgstr "Jurnalele nu sunt salvate într-un fișier."

#. Translators: %s = Hosting name.
#: inc/ThirdParty/Hostings/Cloudways.php:82
msgid ""
"Varnish auto-purge will be automatically enabled once Varnish is enabled on "
"your %s server."
msgstr ""
"Ștergerea automată Varnish va fi activată automat pe serverul tău %s după ce"
" Varnish este activat. "

#. translators: %1$s = opening link tag, %2$s = closing link tag.
#: inc/ThirdParty/Hostings/Kinsta.php:159
msgid ""
"Your installation seems to be missing core Kinsta files managing Cache "
"clearing, which will prevent your Kinsta installation and WP Rocket from "
"working correctly. Please get in touch with Kinsta support through your "
"%1$sMyKinsta%2$s account to resolve this issue."
msgstr ""
"Se pare că în instalarea ta lipsesc fișierele Kinsta de bază care "
"administrează ștergerea cache-ului. Din acest motiv, instalările Kinsta și "
"WP Rocket nu vor funcționa corect. Pentru a rezolva această problemă, te rog"
" contactează suportul Kinsta prin contul tău %1$sMyKinsta%2$s."

#. translators: %1$s = plugin name.
#: inc/ThirdParty/Plugins/CDN/Cloudflare.php:127
msgid ""
"%1$s: Cloudflare's HTTP/2 Server Push is incompatible with the features of "
"Remove Unused CSS and Combine CSS files. We strongly recommend disabling it."
msgstr ""
"%1$s: HTTP/2 Server Push de la Cloudflare este incompatibil cu "
"funcționalitățile Înlătură CSS-ul neutilizat și Combină fișierele CSS. "
"Recomandăm insistent să îl dezactivezi."

#: inc/ThirdParty/Plugins/CDN/Cloudflare.php:170
msgid ""
"Your site is using the official Cloudflare plugin. We have enabled "
"Cloudflare auto-purge for compatibility. If you have APO activated, it is "
"also compatible."
msgstr ""
"Site-ul tău folosește modulul oficial Cloudflare. Am activat ștergerea "
"automată Cloudflare pentru compatibilitate. Dacă ai APO activat, este și el "
"compatibil."

#: inc/ThirdParty/Plugins/CDN/Cloudflare.php:171
msgid ""
"Cloudflare cache will be purged each time WP Rocket clears its cache to "
"ensure content is always up-to-date."
msgstr ""
"Cache-ul Cloudflare va fi șters de fiecare dată când WP Rocket își șterge "
"cache-ul pentru a garanta că conținutul este mereu actualizat."

#. Translators: %1$s = strong opening tag, %2$s = strong closing tag.
#: inc/ThirdParty/Plugins/CDN/Cloudflare.php:215
msgid ""
"%1$sWP Rocket:%2$s You are using \"Dynamic Cookies Cache\". Cloudflare APO "
"is not yet compatible with that feature."
msgstr ""
"%1$sWP Rocket:%2$s folosești „Cache cookie-uri dinamice”. Cloudflare APO nu "
"este încă compatibil cu această funcționalitate."

#. Translators:%1$s = opening <a> tag, %2$s = closing </a> tag.
#: inc/ThirdParty/Plugins/CDN/Cloudflare.php:221
msgid ""
"You should either disable Cloudflare APO or check with the theme/plugin "
"requiring the use of “Dynamic Cookies Cache” developers for an alternative "
"way to be page-cache friendly. %1$sMore info%2$s"
msgstr ""
"Trebuie fie să dezactivezi Cloudflare APO, fie să verifici dacă tema/modulul"
" poate să folosească „Cache cookie-uri dinamice” ca o cale alternativă de a "
"fi prietenoasă cu cache-ul paginilor.%1$sMai multe informații%2$s"

#. Translators: %1$s = strong opening tag, %2$s = strong closing tag, %3$s =
#. opening <a> tag, %4$s = closing </a> tag, %5$s = opening <a> tag.
#: inc/ThirdParty/Plugins/CDN/Cloudflare.php:272
msgid ""
"%1$sWP Rocket:%2$s You are using \"Separate cache files for mobile "
"devices\". You need to activate \"Cache by Device Type\" %3$ssetting%5$s on "
"Cloudflare APO to serve the right version of the cache. %4$sMore info%5$s"
msgstr ""
"%1$sWP Rocket:%2$s folosești „Fișiere cache separate pentru dispozitive "
"mobile”. Trebuie să activezi %3$ssetarea%5$s „Cache după tip de diapozitiv” "
"pe Cloudflare APO ca să servești versiunea corectă de cache. %4$sMai multe "
"informații%5$s"

#. Translators: %1$s = strong opening tag, %2$s = strong closing tag.
#: inc/ThirdParty/Plugins/CDN/Cloudflare.php:293
msgid ""
"%1$sWP Rocket:%2$s You have \"Cache by Device Type\" enabled on Cloudflare "
"APO. If you judge it necessary for the website to have a different cache on "
"mobile and desktop, we suggest you enable our “Separate Cache Files for "
"Mobiles Devices” to ensure the generated cache is accurate."
msgstr ""
"%1$sWP Rocket:%2$s ai activat „Cache după tip de diapozitiv” pe Cloudflare "
"APO. Dacă consideri că este necesar ca site-ul tău să aibă cache-uri "
"diferite pentru mobil și desktop, îți sugerăm să activezi „Fișiere cache "
"separate pentru dispozitive mobile” ca să te asiguri de generarea corectă a "
"cache-ului."

#. translators: %1$s is WP Rocket plugin name, %2$s is opening <a> tag, %3$s
#. is closing </a> tag.
#: inc/ThirdParty/Plugins/ModPagespeed.php:102
msgid ""
"<strong>%1$s</strong>: Mod PageSpeed is not compatible with this plugin and "
"may cause unexpected results. %2$sMore Info%3$s"
msgstr ""
"<strong>%1$s</strong>: extensia PageSpeed nu este compatibilă cu acest modul"
" și poate duce la rezultate neașteptate. %2$sMai multe informații%3$s"

#. Translators: %1$s is an opening <strong> tag; %2$s is a closing </strong>
#. tag
#: inc/ThirdParty/Plugins/Optimization/Autoptimize.php:75
msgid ""
"%1$sWP Rocket: %2$sWe have detected that Autoptimize's JavaScript "
"Aggregation feature is enabled. WP Rocket's Delay JavaScript Execution will "
"not be applied to the file it creates. We suggest disabling %1$sJavaScript "
"Aggregation%2$s to take full advantage of Delay JavaScript Execution."
msgstr ""
"%1$sWP Rocket: %2$sAm detectat că este activată funcționalitatea JavaScript "
"Aggregation a modulului Autoptimize. Executarea funcționalității Întârzie "
"JavaScript a modulului WP Rocket nu va fi aplicată fișierelor pe care le "
"creează. Sugerăm să dezactivezi %1$sJavaScript Aggregation%2$s pentru a "
"beneficia din plic de Întârzie JavaScript."

#. Translators: %1$s is an opening <strong> tag; %2$s is a closing </strong>
#. tag
#: inc/ThirdParty/Plugins/Optimization/Autoptimize.php:130
msgid ""
"%1$sWP Rocket: %2$sWe have detected that Autoptimize's Aggregate Inline CSS "
"feature is enabled. WP Rocket's Load CSS Asynchronously will not work "
"correctly. We suggest disabling %1$sAggregate Inline CSS%2$s to take full "
"advantage of Load CSS Asynchronously Execution."
msgstr ""
"%1$sWP Rocket: %2$sAm detectat că este activată funcționalitatea Aggregate "
"Inline CSS a modulului Autoptimize. Funcționalitatea Încarcă  fișierele CSS "
"asincron a modulului WP Rocket nu va funcționa corect. Sugerăm să "
"dezactivezi %1$sAggregate Inline CSS%2$s pentru a beneficia din plic de "
"Încarcă fișierele CSS asincron."

#. translators: %1$s = opening <a> tag, %2$s = closing </a> tag.
#: inc/ThirdParty/Plugins/Optimization/Ezoic.php:45
msgid ""
"This plugin blocks WP Rocket's caching and optimizations. Deactivate it and "
"use %1$sEzoic's nameserver integration%2$s instead."
msgstr ""
"Acest modul blochează cache-ul și optimizările WP Rocket. Dezactivează-l și "
"folosește-l în loc %1$sEzoic's nameserver integration%2$s."

#. Translators: %s = Plugin name.
#: inc/ThirdParty/Plugins/Optimization/Hummingbird.php:78
msgctxt "Hummingbird notice"
msgid ""
"Please deactivate the following %s option which conflicts with WP Rocket "
"features:"
msgid_plural ""
"Please deactivate the following %s options which conflict with WP Rocket "
"features:"
msgstr[0] ""
"Te rog dezactivează %s opțiune care intră în conflict cu funcționalitățile "
"WP Rocket:"
msgstr[1] ""
"Te rog dezactivează următoarele %s opțiuni care intră în conflict cu "
"funcționalitățile WP Rocket:"
msgstr[2] ""
"Te rog dezactivează următoarele %s de opțiuni care intră în conflict cu "
"funcționalitățile WP Rocket:"

#. Translators: %1$s = Plugin name, %2$s = <em>, %3$s = </em>.
#: inc/ThirdParty/Plugins/Optimization/Hummingbird.php:151
msgctxt "Hummingbird notice"
msgid ""
"%1$s %2$sdisable emoji%3$s conflicts with WP Rockets %2$sdisable emoji%3$s"
msgstr ""
"%2$sDezactivează emoticoanele%3$s %1$s intră în conflict cu %2$sdezactivează"
" emoticoanele%3$s WP Rockets"

#. Translators: %1$s = Plugin name, %2$s = <em>, %3$s = </em>.
#: inc/ThirdParty/Plugins/Optimization/Hummingbird.php:187
msgctxt "Hummingbird notice"
msgid ""
"%1$s %2$sGZIP compression%3$s conflicts with WP Rocket %2$sGZIP "
"compression%3$s"
msgstr ""
"%2$sCompresia GZIP%3$s %1$s intră în conflict cu %2$scompresia GZIP%3$s WP "
"Rocket"

#. Translators: %1$s = Plugin name, %2$s = <em>, %3$s = </em>.
#: inc/ThirdParty/Plugins/Optimization/Hummingbird.php:223
msgctxt "Hummingbird notice"
msgid ""
"%1$s %2$sbrowser caching%3$s conflicts with WP Rocket %2$sbrowser "
"caching%3$s"
msgstr ""
"%2$scache-ul navigatorului%3$s %1$s intră în conflict cu %2$scache-ul "
"navigatorului%3$s WP Rocket"

#. Translators: %1$s = Plugin name, %2$s = <em>, %3$s = </em>.
#: inc/ThirdParty/Plugins/Optimization/Hummingbird.php:255
msgctxt "Hummingbird notice"
msgid ""
"%1$s %2$spage caching%3$s conflicts with WP Rocket %2$spage caching%3$s"
msgstr ""
"%2$sCache-ul paginilor%3$s %1$s intră în conflict cu %2$scache-ul "
"paginilor%3$s WP Rocket"

#. Translators: %1$s = Plugin name, %2$s = <em>, %3$s = </em>.
#: inc/ThirdParty/Plugins/Optimization/Hummingbird.php:289
msgctxt "Hummingbird notice"
msgid ""
"%1$s %2$sasset optimization%3$s conflicts with WP Rocket %2$sfile "
"optimization%3$s"
msgstr ""
"%2$sOptimizarea resurselor%3$s %1$s intră în conflict cu %2$soptimizarea "
"resurselor%3$s WP Rocket"

#: inc/ThirdParty/Plugins/Optimization/Perfmatters.php:38
msgid ""
"Remove Unused CSS is currently activated in Perfmatters. If you want to use "
"WP Rocket's Remove Unused CSS feature, disable this option in Perfmatters."
msgstr ""
"În prezent, Înlătură CSS-ul neutilizat este activat în Perfmatters. Dacă "
"vrei să folosești funcționalitatea Înlătură CSS-ul neutilizat oferită de WP "
"Rocket, dezactivează această opțiune în Perfmatters."

#: inc/ThirdParty/Plugins/Optimization/RapidLoad.php:39
msgid ""
"Automated unused CSS removal is currently activated in RapidLoad Power-Up "
"for Autoptimize. If you want to use WP Rocket's Remove Unused CSS feature, "
"disable the  RapidLoad Power-Up for Autoptimize plugin."
msgstr ""
"În prezent, este activată Înlătură automat CSS-ul neutilizat în RapidLoad "
"Power-Up for Autoptimize. Dacă vrei să folosești funcționalitatea Înlătură "
"CSS-ul neutilizat oferită de WP Rocket, dezactivează modulul RapidLoad "
"Power-Up for Autoptimize."

#. translators: %1$s = plugin name.
#: inc/ThirdParty/Plugins/Optimization/WPMeteor.php:42
msgid ""
"Delay JS is currently activated in %1$s. If you want to use WP Rocket’s "
"delay JS, disable %1$s"
msgstr ""
"Acum, opțiunea Întârzie JS este activată în %1$s. Dacă vrei să folosești "
"Întârzie JS de la WP Rocket, dezactivează %1$s"

#: inc/ThirdParty/Plugins/Smush.php:108 inc/ThirdParty/Plugins/Smush.php:126
msgid "Smush"
msgstr "Smush"

#: inc/ThirdParty/Themes/Avada.php:107
msgid "Avada"
msgstr "Avada"

#: inc/ThirdParty/Themes/Divi.php:293
msgid ""
"Your Divi template was updated. Clear the Used CSS if the layout, design or "
"CSS styles were changed."
msgstr ""
"Șablonul Divi a fost actualizat. Șterge CSS-ul folosit dacă aranjamentul, "
"designul sau stilurile CSS au fost modificate."

#: views/cpcss/activate-cpcss-mobile.php:13
msgid "Load CSS asynchronously for mobile"
msgstr "Încarcă fișierele CSS asincron pentru dispozitive mobile"

#: views/cpcss/activate-cpcss-mobile.php:14
msgid ""
"Your website currently uses the same Critical Path CSS for both desktop and "
"mobile."
msgstr ""
"În prezent, site-ul tău folosește același CSS de cale critic atât pentru "
"desktop cât și pentru dispozitive mobile."

#: views/cpcss/activate-cpcss-mobile.php:15
msgid "Click the button to enable mobile-specific CPCSS for your site."
msgstr ""
"Dă clic pe buton pentru a activa CSS-ul de cale critic specific pentru "
"dispozitive mobile pe site-ul tău."

#. translators: %1$s = opening link tag, %2$s = closing link tag.
#: views/cpcss/activate-cpcss-mobile.php:20
#: views/settings/enable-google-fonts.php:19
#: views/settings/mobile-cache.php:20
msgid ""
"This is a one-time action and this button will be removed afterwards. "
"%1$sMore info%2$s"
msgstr ""
"Aceasta este o acțiune unică și acest buton va fi înlăturat ulterior. "
"%1$sMai multe informații%2$s"

#. translators: %1$s = opening link tag, %2$s = closing link tag.
#: views/cpcss/activate-cpcss-mobile.php:30
msgid ""
"Your site is now using mobile-specific critical path CSS. %1$sMore info%2$s"
msgstr ""
"Acum site-ul tău folosește CSS-ul de cale critic specific pentru dispozitive"
" mobile. %1$sMai multe informații%2$s"

#: views/cpcss/activate-cpcss-mobile.php:39
msgid "Generate Mobile Specific CPCSS"
msgstr "Generează CSS-ul de cale critic specific pentru dispozitive mobile"

#: views/cpcss/metabox/container.php:19
msgid "Critical Path CSS"
msgstr "CSS de cale critic"

#. translators: %1$s = opening link tag, %2$s = closing link tag.
#: views/cpcss/metabox/generate.php:23
msgid "Generate specific Critical Path CSS for this post. %1$sMore info%2$s"
msgstr ""
"Generează un anumit CSS de cale critic pentru acest articol. %1$sMai multe "
"informații%2$s"

#. translators: %1$s = opening link tag, %2$s = closing link tag.
#: views/cpcss/metabox/generate.php:33
msgid "This post uses specific Critical Path CSS. %1$sMore info%2$s"
msgstr ""
"Acest articol folosește un anumit CSS de cale critic. %1$sMai multe "
"informații%2$s"

#: views/cpcss/metabox/generate.php:56
msgid "Revert back to the default CPCSS"
msgstr "Revino la CSS-ul de cale critic"

#: views/deactivation-intent/form.php:21
msgid "Facing an issue?"
msgstr "Întâmpini o problemă?"

#: views/deactivation-intent/form.php:24
msgid ""
"It is not always necessary to deactivate WP Rocket when facing any issues. "
"Most of them can be fixed by deactivating only some options."
msgstr ""
"De obicei, nu este necesar să dezactivezi WP Rocket când întâmpini probleme."
" Cele mai multe dintre ele pot fi rezolvate doar prin dezactivarea unor "
"opțiuni."

#. translators: %1$s = opening strong tag, %2$s = closing strong tag.
#: views/deactivation-intent/form.php:29
msgid ""
"Our advice? Instead of deactivating WP Rocket, use our %1$sSafe Mode%2$s to "
"quickly disable LazyLoad, File Optimization, and CDN options. Then check to "
"see if your issue is resolved."
msgstr ""
"Vrei sfatul nostru? În loc să dezactivezi WP Rocket, folosește %1$smodul în "
"siguranță%2$s pentru a dezactiva rapid opțiunile încărcare lentă, optimizare"
" fișiere și CDN. Apoi verifică dacă problema este rezolvată."

#: views/deactivation-intent/form.php:35
msgid "Do you want to use our Safe Mode to troubleshoot WP Rocket?"
msgstr "Vrei să folosești modul în siguranță pentru a depana WP Rocket?"

#. translators: %1$s = opening strong tag, %2$s = closing strong tag.
#: views/deactivation-intent/form.php:55
msgid "Yes, apply \"%1$sSafe Mode%2$s\""
msgstr "Da, aplică „%1$sMod în siguranță%2$s”"

#. translators: %1$s = opening strong tag, %2$s = closing strong tag.
#: views/deactivation-intent/form.php:68
msgid ""
"and export WP Rocket settings %1$s(Recommended as current settings will be "
"deleted)%2$s"
msgstr ""
"și exportă setările WP Rocket %1$s(exportul este recomandat deoarece "
"setările curente vor fi șterse)%2$s"

#: views/deactivation-intent/form.php:76
msgid "No, deactivate and snooze this message for"
msgstr "Nu, dezactivează și amână acest mesaj pentru"

#: views/deactivation-intent/form.php:78
msgid "1 day"
msgstr "o zi"

#: views/deactivation-intent/form.php:79
msgid "7 days"
msgstr "7 zile"

#: views/deactivation-intent/form.php:80
msgid "30 days"
msgstr "30 de zile"

#: views/deactivation-intent/form.php:81
msgid "Forever"
msgstr "totdeauna"

#: views/deactivation-intent/form.php:87
msgid "Cancel"
msgstr "Anulează"

#: views/deactivation-intent/form.php:88
msgid "Confirm"
msgstr "Confirmă"

#. translators: %1$s = <strong>, %2$s = plugin version, %3$s = </strong>, %4$s
#. = <a>, %5$s = </a>, %6$s = <a>.
#: views/plugins/update-renewal-expired-notice.php:27
msgid ""
" %1$sWP Rocket %2$s%3$s is available. %4$sLearn more%5$s about the updates "
"and enhancements of this major version. You need an active license to use "
"them on your website, don’t miss out! %6$sRenew Now%5$s"
msgstr ""
"Este disponibilă %1$sversiunea %2$s pentru WP Rocket %3$s. %4$sAflă mai "
"multe%5$s despre actualizările și îmbunătățirile din această versiune "
"importantă. Pentru a le folosi pe site-ul tău, ai nevoie de o licență "
"activă, deci nu rata actualizarea! %6$sReînnoiește acum%5$s"

#: views/settings/dynamic-lists-update.php:14
msgid "Update Inclusion and Exclusion Lists"
msgstr "Actualizează listele cu includeri și excluderi"

#. translators: %1$s = opening link tag, %2$s = closing link tag.
#: views/settings/dynamic-lists-update.php:19
msgid ""
"Compatibility lists are downloaded automatically every week. Click the "
"button if you want to update them manually. %1$sMore info%2$s"
msgstr ""
"Listele cu compatibilități sunt descărcate automat în fiecare săptămână. Dă "
"clic pe buton dacă vrei să le actualizezi manual. %1$sMai multe "
"informații%2$s"

#: views/settings/dynamic-lists-update.php:29
msgid "Update lists"
msgstr "Actualizează listele"

#: views/settings/enable-google-fonts.php:13
msgid "Enable Google Font Optimization"
msgstr "Activează optimizarea Fonturilor Google"

#: views/settings/enable-google-fonts.php:14
msgid ""
"Improves font performance and combines multiple font requests to reduce the "
"number of HTTP requests."
msgstr ""
"Îmbunătățește performanța fonturilor și combină mai multe cereri de fonturi "
"pentru a reduce numărul de cereri HTTP."

#. translators: %1$s = opening link tag, %2$s = closing link tag.
#: views/settings/enable-google-fonts.php:29
msgid ""
"Google Fonts Optimization is now enabled for your site. %1$sMore info%2$s"
msgstr ""
"Optimizarea Fonturilor Google este activată acum pentru site-ul tău. %1$sMai"
" multe informații%2$s"

#: views/settings/enable-google-fonts.php:38
msgid "Optimize Google Fonts"
msgstr "Optimizează fonturile Google"

#: views/settings/fields/cache-lifespan.php:26
msgid "Clear cache after"
msgstr "Șterge cache după"

#: views/settings/fields/cnames.php:58 views/settings/fields/cnames.php:92
#: views/settings/fields/cnames.php:122
msgid "CSS & JavaScript"
msgstr "CSS și JavaScript"

#: views/settings/fields/cnames.php:59 views/settings/fields/cnames.php:93
#: views/settings/fields/cnames.php:123
msgid "JavaScript"
msgstr "JavaScript"

#: views/settings/fields/cnames.php:60 views/settings/fields/cnames.php:94
#: views/settings/fields/cnames.php:124
msgid "CSS"
msgstr "CSS"

#: views/settings/fields/import-form.php:29
msgid "Import settings"
msgstr "Setări import"

#: views/settings/fields/one-click-addon.php:43
#: views/settings/fields/rocket-addon.php:43
msgid "Add-on status"
msgstr "Stare supliment"

#: views/settings/fields/rocket-addon.php:72
msgid "Modify options"
msgstr "Modifică opțiunile"

#: views/settings/fields/rocket-cdn.php:29
#: views/settings/fields/rocket-cdn.php:41
msgid "CDN CNAME"
msgstr "CNAME CDN"

#. translators: %s is a "Learn more" link.
#: views/settings/fields/rocket-cdn.php:62
msgid "Purges RocketCDN cached resources for your website. %s"
msgstr ""
"Șterge resursele memorate în cache-ul RocketCDN pentru site-ul tău web. %s"

#. translators: %s is a "Learn more" link.
#: views/settings/fields/rocket-cdn.php:63
#: views/settings/page-sections/cloudflare.php:36
#: views/settings/page-sections/sucuri.php:37
msgid "Learn more"
msgstr "Află mai mult"

#: views/settings/fields/rocket-cdn.php:72
msgid "Clear all RocketCDN cache files"
msgstr "Șterge toate fișierele cache RocketCDN"

#: views/settings/mobile-cache.php:11
msgid "Mobile Cache"
msgstr "Cache pentru mobil"

#: views/settings/mobile-cache.php:14
msgid "Speed your site for mobile visitors."
msgstr "Accelerează-ți site-ul pentru vizitatorii de pe dispozitive mobile."

#: views/settings/mobile-cache.php:28
msgid "Mobile Cache is now enabled for your site."
msgstr "Cache-ul pentru dispozitive mobile este activat acum pe site-ul tău."

#: views/settings/mobile-cache.php:34
msgid "Enable Mobile Cache"
msgstr "Activează cache-ul pentru dispozitive mobile"

#: views/settings/page-sections/cloudflare.php:27
msgid "Cloudflare Cache"
msgstr "Cache Cloudflare"

#. translators: %s is a "Learn more" link.
#: views/settings/page-sections/cloudflare.php:35
#: views/settings/page-sections/sucuri.php:36
msgid "Purges cached resources for your website. %s"
msgstr "Șterge resursele memorate în cache pentru site-ul tău web. %s"

#. translators: %s is a "Learn more" link.
#: views/settings/page-sections/cloudflare.php:36
msgid "https://support.cloudflare.com/hc/en-us/articles/200169246"
msgstr "https://support.cloudflare.com/hc/en-us/articles/200169246"

#: views/settings/page-sections/cloudflare.php:45
msgid "Clear all Cloudflare cache files"
msgstr "Șterge toate fișierele cache Cloudflare"

#: views/settings/page-sections/dashboard.php:38
msgid "Congratulations!"
msgstr "Felicitări!"

#: views/settings/page-sections/dashboard.php:40
msgid "WP Rocket is now activated and already working for you."
msgstr "Acum WP Rocket este activat și deja lucrează pentru tine."

#: views/settings/page-sections/dashboard.php:42
msgid "Your website should be loading faster now!"
msgstr "Site-ul tău web ar trebui să se încarce mai rapid acum!"

#: views/settings/page-sections/dashboard.php:44
msgid ""
"To guarantee fast websites, WP Rocket automatically applies 80% of web "
"performance best practices."
msgstr ""
"Pentru a garanta site-uri web rapide, WP Rocket aplică automat 80% din cele "
"mai bune practici de performanță pe web."

#: views/settings/page-sections/dashboard.php:44
msgid ""
"We also enable options that provide immediate benefits to your website."
msgstr ""
"De asemenea, avem opțiuni care oferă avantaje imediate pentru site-ul tău "
"web."

#: views/settings/page-sections/dashboard.php:45
msgid "Continue to the options to further optimize your site!"
msgstr "Continuă cu opțiunile pentru a-ți optimiza în continuare site-ul!"

#: views/settings/page-sections/dashboard.php:62
msgid "My Account"
msgstr "Contul meu"

#: views/settings/page-sections/dashboard.php:68
msgid "Refresh info"
msgstr "Reîmprospătează informațiile"

#: views/settings/page-sections/dashboard.php:86
msgid "with"
msgstr "cu"

#: views/settings/page-sections/dashboard.php:100
msgid "Expiration Date"
msgstr "Data expirării"

#: views/settings/page-sections/dashboard.php:110
msgid "View my account"
msgstr "Vezi contul meu"

#: views/settings/page-sections/dashboard.php:137
msgid "Quick Actions"
msgstr "Acțiuni rapide"

#: views/settings/page-sections/dashboard.php:144
msgid "Remove all cached files"
msgstr "Înlătură toate fișierele cache"

#: views/settings/page-sections/dashboard.php:164
#: views/settings/page-sections/dashboard.php:170
msgid "Regenerate Critical CSS"
msgstr "Regenerează CSS critic"

#: views/settings/page-sections/dashboard.php:196
msgid "Frequently Asked Questions"
msgstr "Întrebări frecvente"

#: views/settings/page-sections/dashboard.php:210
msgid "Still cannot find a solution?"
msgstr "Încă nu poți găsi o soluție?"

#: views/settings/page-sections/dashboard.php:211
msgid ""
"Submit a ticket and get help from our friendly and knowledgeable Rocketeers."
msgstr ""
"Trimite un tichet și primești ajutor de la membrii prietenoși și pricepuți "
"WP Rocket."

#: views/settings/page-sections/dashboard.php:219
msgid "Ask support"
msgstr "Contactează suportul"

#: views/settings/page-sections/database.php:25
msgid "Backup your database before you run a cleanup!"
msgstr "Fă o copie de siguranță a bazei de date înainte de a rula o curățare!"

#: views/settings/page-sections/database.php:26
msgid ""
"Once a database optimization has been performed, there is no way to undo it."
msgstr ""
"După ce a fost efectuată o optimizare a bazei de date, nu există nicio "
"modalitate de a o anula."

#: views/settings/page-sections/database.php:28
msgid "Save Changes and Optimize"
msgstr "Salvează modificările și optimizează"

#. Translators: %1$s = <strong>, %2$s = </strong>, %3$s = <span
#. class="imagify-name">, %4$s = </span>.
#: views/settings/page-sections/imagify.php:21
msgid ""
"%1$sWP ROCKET%2$s created %3$sIMAGIFY%4$s %1$sfor best-in-class image "
"optimization.%2$s"
msgstr ""
"%1$sWP ROCKET%2$s a creat %3$sIMAGIFY%4$s %1$spentru optimizarea de înaltă "
"clasă a imaginilor.%2$s"

#: views/settings/page-sections/imagify.php:24
msgid ""
"Compress image to make your website faster, all while maintaining image "
"quality."
msgstr ""
"Comprimă imaginile pentru a-ți face site-ul mai rapid, menținând calitatea "
"imaginilor."

#: views/settings/page-sections/imagify.php:25
msgid "More on Imagify:"
msgstr "Mai multe despre Imagify:"

#: views/settings/page-sections/imagify.php:27
msgid "Imagify Plugin Page"
msgstr "Pagină modul Imagify"

#: views/settings/page-sections/imagify.php:28
msgid "Imagify Website"
msgstr "Site web Imagify"

#: views/settings/page-sections/imagify.php:29
msgid "Review of Image Compression Plugins"
msgstr "Examinează modulele care comprimă imagini"

#: views/settings/page-sections/imagify.php:38
msgid "Install Imagify"
msgstr "Instalează Imagify"

#: views/settings/page-sections/license.php:22
msgid "WP Rocket was not able to automatically validate your license."
msgstr "WP Rocket nu a putut să-ți valideze automat licența."

#. translators: %1$s = tutorial URL, %2$s = support URL.
#: views/settings/page-sections/license.php:29
msgid "Follow this %1$s, or contact %2$s to get the engine started."
msgstr "Vezi acest %1$ssau contactează %2$s pentru a porni motorul."

#. translators: %1$s = <a href=", %2$s =  tutorial href,  %3$s =  "
#. target="_blank">,  %4$s =  </a>.
#: views/settings/page-sections/license.php:32
msgid "%1$s%2$s%3$stutorial%4$s"
msgstr "%1$s%2$s%3$stutorial%4$s"

#: views/settings/page-sections/license.php:34
msgid ""
"https://docs.wp-rocket.me/article/100-resolving-problems-with-license-"
"validation/?utm_source=wp_plugin&utm_medium=wp_rocket"
msgstr ""
"https://docs.wp-rocket.me/article/100-resolving-problems-with-license-"
"validation/?utm_source=wp_plugin&utm_medium=wp_rocket"

#. translators: %1$s = <a href=", %2$s =  support href,  %3$s =  "
#. target="_blank">,  %4$s =  </a>.
#: views/settings/page-sections/license.php:40
msgid "%1$s%2$s%3$ssupport%4$s"
msgstr "%1$s%2$s%3$ssuport%4$s"

#: views/settings/page-sections/sucuri.php:46
msgid "Clear all Sucuri cache files"
msgstr "Șterge toate fișierele cache Sucuri"

#. translators: %1$s = formatted file size, %2$s = formatted number of entries
#. (don't use %2$d).
#: views/settings/page-sections/tools.php:20
msgid "Files size: %1$s. Number of entries: %2$s."
msgstr "Dimensiune fișier: %1$s. Număr de intrări: %2$s."

#. translators: %1$s = opening <a> tag, %2$s = closing </a> tag.
#: views/settings/page-sections/tools.php:23
msgid "%1$sDownload the file%2$s."
msgstr "%1$sDescarcă fișierul%2$s."

#. translators: %1$s = opening <a> tag, %2$s = closing </a> tag.
#: views/settings/page-sections/tools.php:26
msgid "%1$sDelete the file%2$s."
msgstr "%1$sȘterge fișierul%2$s."

#: views/settings/page-sections/tools.php:37
msgid "Export settings"
msgstr "Setări export"

#: views/settings/page-sections/tools.php:38
msgid "Download a backup file of your settings"
msgstr "Descarcă un fișier cu copia de siguranță a setărilor"

#: views/settings/page-sections/tools.php:46
msgid "Download settings"
msgstr "Descarcă setările"

#: views/settings/page-sections/tools.php:60
msgid "Rollback"
msgstr "Restaurează"

#. translators: %s = WP Rocket version number.
#: views/settings/page-sections/tools.php:64
msgid "Has version %s caused an issue on your website?"
msgstr "Versiunea %s a provocat o problemă pe site-ul tău?"

#. translators: %s = <br>.
#: views/settings/page-sections/tools.php:69
msgid ""
"You can rollback to the previous major version here.%sThen send us a support"
" request."
msgstr ""
"Aici poți restaura la o versiune majoră anterioară.%sApoi trimite-ne o "
"cerere de suport."

#. translators: %s = WP Rocket previous version.
#: views/settings/page-sections/tools.php:80
msgid "Reinstall version %s"
msgstr "Reinstalează versiune %s"

#: views/settings/page-sections/tools.php:106
msgid "Debug mode"
msgstr "Mod depanare"

#: views/settings/page-sections/tools.php:111
msgid "Create a debug log file."
msgstr "Creează un fișier jurnal de depanare."

#: views/settings/page-sections/tutorials.php:13
#: views/settings/partials/getting-started.php:18
msgid "Getting Started"
msgstr "Noțiuni de bază"

#: views/settings/page-sections/tutorials.php:15
msgid "Getting Started with WP Rocket"
msgstr "Începe cu WP Rocket"

#: views/settings/page-sections/tutorials.php:16
#: views/settings/partials/getting-started.php:12
msgid "Finding the Best Settings for Your Site"
msgstr "Îți găsește cele mai bune setări pentru site"

#: views/settings/page-sections/tutorials.php:17
#: views/settings/partials/getting-started.php:13
msgid "How to Check if WP Rocket is Caching Your Site"
msgstr "Cum să verifici dacă WP Rocket îți memorează în cache site-ul"

#: views/settings/page-sections/tutorials.php:18
#: views/settings/partials/getting-started.php:14
msgid "How to Measure the Speed of Your Site"
msgstr "Cum să măsori viteza site-ului"

#: views/settings/page-sections/tutorials.php:19
msgid "How Preloading Works"
msgstr "Cum funcționează pre-încărcarea"

#: views/settings/page-sections/tutorials.php:23
msgid "Passing the Core Web vitals"
msgstr "Trecerea factorilor din Core Web vitals"

#: views/settings/page-sections/tutorials.php:25
msgid "How to improve LCP with WP Rocket"
msgstr "Cum îmbunătățești LCP cu WP Rocket"

#: views/settings/page-sections/tutorials.php:26
msgid "How to improve FID with WP Rocket"
msgstr "Cum îmbunătățești FID cu WP Rocket"

#: views/settings/page-sections/tutorials.php:27
msgid "How to improve CLS with WP Rocket"
msgstr "Cum îmbunătățești CLS cu WP Rocket"

#: views/settings/page-sections/tutorials.php:31
msgid "Troubleshooting"
msgstr "Depanare"

#: views/settings/page-sections/tutorials.php:33
msgid "Troubleshooting Display Issues with File Optimization"
msgstr "Depanarea problemelor de afișare cu optimizarea fișierelor"

#: views/settings/page-sections/tutorials.php:34
msgid "How to Find the Right JavaScript to Exclude"
msgstr "Cum să găsești JavaScript-ul care poate fi exclus"

#: views/settings/page-sections/tutorials.php:35
msgid "How External Content Slows Your Site"
msgstr "Cum îți încetinește site-ul conținutul extern"

#: views/settings/page-sections/tutorials.php:41
msgid "Set Up the Cloudflare Add-on"
msgstr "Inițializează suplimentele Cloudflare"

#: views/settings/page.php:16
msgid "WP Rocket Settings"
msgstr "Setări WP Rocket"

#. translators: %s = Plugin version number.
#: views/settings/page.php:30
msgid "version %s"
msgstr "versiunea %s"

#: views/settings/page.php:60
msgid "Show Sidebar"
msgstr "Arată bara laterală"

#. translators: %1$s = <strong>, %2$s = </strong>.
#: views/settings/page.php:82
msgid ""
"Below is a detailed view of all data WP Rocket will collect %1$sif granted "
"permission.%2$s"
msgstr ""
"Mai jos, sunt detaliate toate datele pe care WP Rocket le va colecta "
"%1$sdacă îi dai permisiunea%2$s."

#: views/settings/page.php:88
msgid ""
"WP Rocket will never transmit any domain names or email addresses (except "
"for license validation), IP addresses, or third-party API keys."
msgstr ""
"WP Rocket nu va divulga niciodată nume de domenii sau adrese de email (cu "
"excepția validării licenței), adrese IP sau chei API de la părți terțe."

#: views/settings/page.php:90
msgid "Activate Rocket analytics"
msgstr "Activează analitice Rocket"

#: views/settings/partials/documentation.php:15
msgid "It is a great starting point to fix some of the most common issues."
msgstr ""
"Este un punct de plecare bun pentru a rezolva unele dintre cele mai "
"frecvente probleme."

#: views/settings/partials/documentation.php:22
msgid "Read the documentation"
msgstr "Citește documentația"

#: views/settings/partials/getting-started.php:11
msgid "What WP Rocket Does For You By Default"
msgstr "Ce face implicit WP Rocket pentru tine"

#: views/settings/partials/sidebar.php:12
msgid "How to correctly measure your website’s loading time"
msgstr "Cum să măsori corect timpul de încărcare al site-ului tău web"

#: views/settings/partials/sidebar.php:14
msgid "Check our tutorial and learn how to measure the speed of your site."
msgstr "Vezi tutorialul nostru și învață cum să măsori viteza site-ului."

#: views/settings/partials/sidebar.php:15
#: views/settings/partials/sidebar.php:24
msgid "Read our guide"
msgstr "Citește ghidul nostru"

#: views/settings/partials/sidebar.php:18
msgid "Learn about optimal WP Rocket settings for mobile."
msgstr "Află care sunt setările optime WP Rocket pentru dispozitive mobile."

#: views/settings/partials/sidebar.php:27
msgid "Test and Improve Google Core Web Vitals for WordPress."
msgstr "Testează și îmbunătățește Core Web Vitals (Google) pentru WordPress."

#: views/settings/partials/sidebar.php:28
msgid "Read more"
msgstr "Citește mai mult"

#: views/settings/partials/sidebar.php:33
msgid "You have not activated logged-in user cache."
msgstr "Nu ai activat cache-ul pentru utilizatorii autentificați."

#: views/settings/partials/sidebar.php:34
msgid ""
"Use a private browser to check your website's speed and visual appearance."
msgstr ""
"Folosește un navigator privat pentru a verifica viteza și aspectul vizual "
"pentru site-ul tău web."

#: views/settings/sections/addons-container.php:24
#: views/settings/sections/fields-container.php:28
msgid "Need Help?"
msgstr "Ai nevoie de ajutor?"
