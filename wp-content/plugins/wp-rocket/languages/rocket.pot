# Copyright (C) 2024 WP Media
# This file is distributed under the same license as the WP Rocket plugin.
msgid ""
msgstr ""
"Project-Id-Version: WP Rocket 3.16-alpha2\n"
"Report-Msgid-Bugs-To: http://wp-rocket.me/\n"
"Last-Translator: WP Media (http://wp-rocket.me/)\n"
"Language-Team: WP Media (http://www.transifex.com/projects/p/wp-media/) <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"POT-Creation-Date: 2024-05-06T13:28:35+03:00\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"X-Generator: WP-CLI 2.7.1\n"
"X-Domain: rocket\n"

#. Plugin Name of the plugin
msgid "WP Rocket"
msgstr ""

#. Plugin URI of the plugin
msgid "https://wp-rocket.me"
msgstr ""

#. Description of the plugin
msgid "The best WordPress performance plugin."
msgstr ""

#. Author of the plugin
msgid "WP Media"
msgstr ""

#. Author URI of the plugin
msgid "https://wp-media.me"
msgstr ""

#. Translators: %s = Hosting name.
#: inc/3rd-party/hosting/flywheel.php:18
#: inc/3rd-party/hosting/wp-serveur.php:27
#: inc/deprecated/3.6.php:697
#: inc/deprecated/3.6.php:997
#: inc/deprecated/3.9.php:22
#: inc/ThirdParty/Hostings/Cloudways.php:90
#: inc/ThirdParty/Hostings/Dreampress.php:44
#: inc/ThirdParty/Hostings/Godaddy.php:63
#: inc/ThirdParty/Hostings/O2Switch.php:49
#: inc/ThirdParty/Hostings/OneCom.php:137
#: inc/ThirdParty/Hostings/Pressidium.php:49
#: inc/ThirdParty/Hostings/ProIsp.php:51
#: inc/ThirdParty/Hostings/Savvii.php:50
#: inc/ThirdParty/Hostings/WPEngine.php:47
#: inc/ThirdParty/Hostings/WPXCloud.php:51
msgid "Your site is hosted on %s, we have enabled Varnish auto-purge for compatibility."
msgstr ""

#: inc/Addon/Cloudflare/API/Client.php:129
msgid "Cloudflare did not provide any reply. Please try again later."
msgstr ""

#: inc/Addon/Cloudflare/API/Client.php:135
msgid "Cloudflare unexpected response"
msgstr ""

#: inc/Addon/Cloudflare/API/Client.php:143
msgid "Missing Cloudflare result."
msgstr ""

#: inc/Addon/Cloudflare/API/Client.php:194
#: inc/deprecated/3.5.php:112
#: inc/deprecated/3.5.php:169
msgid "Incorrect Cloudflare email address or API key."
msgstr ""

#. translators: %1$s = opening link; %2$s = closing link
#: inc/Addon/Cloudflare/API/Client.php:198
#: inc/Addon/Cloudflare/API/Client.php:212
#: inc/Addon/Cloudflare/Cloudflare.php:75
#: inc/Addon/Cloudflare/Cloudflare.php:108
#: inc/classes/subscriber/Tools/class-detect-missing-tags-subscriber.php:157
#: inc/deprecated/3.5.php:92
#: inc/deprecated/3.5.php:116
#: inc/deprecated/3.5.php:129
#: inc/deprecated/3.5.php:157
#: inc/deprecated/3.5.php:173
msgid "Read the %1$sdocumentation%2$s for further guidance."
msgstr ""

#. translators: Documentation exists in EN, FR; use localized URL if applicable.
#: inc/Addon/Cloudflare/API/Client.php:200
#: inc/Addon/Cloudflare/API/Client.php:214
#: inc/Addon/Cloudflare/Auth/APIKey.php:63
#: inc/Addon/Cloudflare/Cloudflare.php:77
#: inc/Addon/Cloudflare/Cloudflare.php:110
#: inc/deprecated/3.5.php:81
#: inc/deprecated/3.5.php:94
#: inc/deprecated/3.5.php:118
#: inc/deprecated/3.5.php:131
#: inc/deprecated/3.5.php:159
#: inc/deprecated/3.5.php:175
#: inc/deprecated/3.5.php:208
msgid "https://docs.wp-rocket.me/article/18-using-wp-rocket-with-cloudflare/?utm_source=wp_plugin&utm_medium=wp_rocket#add-on"
msgstr ""

#: inc/Addon/Cloudflare/API/Client.php:208
#: inc/deprecated/3.5.php:125
msgid "Incorrect Cloudflare Zone ID."
msgstr ""

#. translators: %1$s = opening link; %2$s = closing link
#: inc/Addon/Cloudflare/Auth/APIKey.php:61
msgid "Cloudflare email and/or API key are not set. Read the %1$sdocumentation%2$s for further guidance."
msgstr ""

#: inc/Addon/Cloudflare/Cloudflare.php:71
#: inc/deprecated/3.5.php:88
msgid "Missing Cloudflare Zone ID."
msgstr ""

#: inc/Addon/Cloudflare/Cloudflare.php:104
#: inc/deprecated/3.5.php:153
msgid "It looks like your domain is not set up on Cloudflare."
msgstr ""

#: inc/Addon/Cloudflare/Cloudflare.php:200
msgid "days"
msgstr ""

#: inc/Addon/Cloudflare/Cloudflare.php:203
msgid "seconds"
msgstr ""

#: inc/Addon/Cloudflare/Cloudflare.php:205
msgid "minutes"
msgstr ""

#: inc/Addon/Cloudflare/Cloudflare.php:207
msgid "hours"
msgstr ""

#. translators: %1$s = <strong>, %2$s = </strong>, %3$s = CloudFare API return message.
#: inc/Addon/Cloudflare/Subscriber.php:232
#: inc/Addon/Cloudflare/Subscriber.php:261
msgid "%1$sWP Rocket:%2$s %3$s"
msgstr ""

#. translators: %1$s = <strong>, %2$s = </strong>.
#: inc/Addon/Cloudflare/Subscriber.php:250
msgid "%1$sWP Rocket:%2$s Cloudflare cache successfully purged."
msgstr ""

#. translators: %s is the message returned by the CloudFlare API.
#: inc/Addon/Cloudflare/Subscriber.php:311
msgid "Cloudflare development mode error: %s"
msgstr ""

#. translators: %s is the message returned by the CloudFlare API.
#: inc/Addon/Cloudflare/Subscriber.php:318
msgid "Cloudflare development mode %s"
msgstr ""

#. translators: %s is the message returned by the CloudFlare API.
#: inc/Addon/Cloudflare/Subscriber.php:337
msgid "Cloudflare cache level error: %s"
msgstr ""

#: inc/Addon/Cloudflare/Subscriber.php:344
msgctxt "Cloudflare caching level"
msgid "standard"
msgstr ""

#. translators: %s is the caching level returned by the CloudFlare API.
#: inc/Addon/Cloudflare/Subscriber.php:350
msgid "Cloudflare cache level set to %s"
msgstr ""

#. translators: %s is the message returned by the CloudFlare API.
#: inc/Addon/Cloudflare/Subscriber.php:368
msgid "Cloudflare minification error: %s"
msgstr ""

#. translators: %s is the message returned by the CloudFlare API.
#: inc/Addon/Cloudflare/Subscriber.php:375
msgid "Cloudflare minification %s"
msgstr ""

#. translators: %s is the message returned by the CloudFlare API.
#: inc/Addon/Cloudflare/Subscriber.php:393
msgid "Cloudflare rocket loader error: %s"
msgstr ""

#. translators: %s is the message returned by the CloudFlare API.
#: inc/Addon/Cloudflare/Subscriber.php:400
msgid "Cloudflare rocket loader %s"
msgstr ""

#. translators: %s is the message returned by the CloudFlare API.
#: inc/Addon/Cloudflare/Subscriber.php:418
msgid "Cloudflare browser cache error: %s"
msgstr ""

#. translators: %s is the message returned by the CloudFlare API.
#: inc/Addon/Cloudflare/Subscriber.php:425
msgid "Cloudflare browser cache set to %s"
msgstr ""

#. translators: %1$s = strong opening tag, %2$s = strong closing tag.
#: inc/Addon/Cloudflare/Subscriber.php:536
msgid "%1$sWP Rocket:%2$s Optimal settings activated for Cloudflare:"
msgstr ""

#. translators: %1$s = strong opening tag, %2$s = strong closing tag.
#: inc/Addon/Cloudflare/Subscriber.php:545
msgid "%1$sWP Rocket:%2$s Optimal settings deactivated for Cloudflare, reverted to previous settings:"
msgstr ""

#: inc/Addon/Cloudflare/Subscriber.php:661
#: inc/admin/options.php:165
#: inc/classes/subscriber/Tools/class-detect-missing-tags-subscriber.php:148
msgid "WP Rocket: "
msgstr ""

#. translators: %s is the error message returned by the API.
#: inc/Addon/Sucuri/Subscriber.php:96
msgid "Sucuri cache purge error: %s"
msgstr ""

#: inc/Addon/Sucuri/Subscriber.php:101
msgid "The Sucuri cache is being cleared. Note that it may take up to two minutes for it to be fully flushed."
msgstr ""

#: inc/Addon/Sucuri/Subscriber.php:218
msgid "Sucuri firewall API key was not found."
msgstr ""

#: inc/Addon/Sucuri/Subscriber.php:231
msgid "Sucuri firewall API key is invalid."
msgstr ""

#. translators: %s is an error message.
#: inc/Addon/Sucuri/Subscriber.php:286
msgid "Error when contacting Sucuri firewall API. Error message was: %s"
msgstr ""

#: inc/Addon/Sucuri/Subscriber.php:301
msgid "Could not get a response from the Sucuri firewall API."
msgstr ""

#: inc/Addon/Sucuri/Subscriber.php:316
msgid "Got an invalid response from the Sucuri firewall API."
msgstr ""

#: inc/Addon/Sucuri/Subscriber.php:330
msgid "The Sucuri firewall API returned an unknown error."
msgstr ""

#. translators: %s is an error message.
#: inc/Addon/Sucuri/Subscriber.php:334
msgid "The Sucuri firewall API returned the following error: %s"
msgid_plural "The Sucuri firewall API returned the following errors: %s"
msgstr[0] ""
msgstr[1] ""

#. Translators: %1$s = plugin name(s), %2$s = opening <a> tag, %3$s = closing </a> tag.
#: inc/Addon/WebP/AdminSubscriber.php:93
msgid "You are using %1$s to serve WebP images so you do not need to enable this option. %2$sMore info%3$s %4$s If you prefer to have WP Rocket serve WebP for you instead, please disable WebP display in %1$s."
msgid_plural "You are using %1$s to serve WebP images so you do not need to enable this option. %2$sMore info%3$s %4$s If you prefer to have WP Rocket serve WebP for you instead, please disable WebP display in %1$s."
msgstr[0] ""
msgstr[1] ""

#: inc/Addon/WebP/AdminSubscriber.php:105
msgid "WebP cache is disabled by filter."
msgstr ""

#. Translators: %1$s = plugin name(s), %2$s = opening <a> tag, %3$s = closing </a> tag.
#: inc/Addon/WebP/AdminSubscriber.php:115
#: inc/Addon/WebP/AdminSubscriber.php:141
msgid "You are using %1$s to convert images to WebP. If you want WP Rocket to serve them for you, activate this option. %2$sMore info%3$s"
msgid_plural "You are using %1$s to convert images to WebP. If you want WP Rocket to serve them for you, activate this option. %2$sMore info%3$s"
msgstr[0] ""
msgstr[1] ""

#. Translators: %1$s = plugin name(s), %2$s = opening <a> tag, %3$s = closing </a> tag.
#: inc/Addon/WebP/AdminSubscriber.php:127
#: inc/Addon/WebP/AdminSubscriber.php:153
msgid "You are using %1$s to convert images to WebP. WP Rocket will create separate cache files to serve your WebP images. %2$sMore info%3$s"
msgid_plural "You are using %1$s to convert images to WebP. WP Rocket will create separate cache files to serve your WebP images. %2$sMore info%3$s"
msgstr[0] ""
msgstr[1] ""

#. Translators: %1$s = opening <a> tag, %2$s = closing </a> tag.
#: inc/Addon/WebP/AdminSubscriber.php:173
msgid "%5$sWe have not detected any compatible WebP plugin!%6$s%4$s If you don’t already have WebP images on your site consider using %3$sImagify%2$s or another supported plugin. %1$sMore info%2$s %4$s If you are not using WebP do not enable this option."
msgstr ""

#: inc/Addon/WebP/AdminSubscriber.php:185
msgid "WP Rocket will create separate cache files to serve your WebP images."
msgstr ""

#: inc/admin/admin.php:18
#: inc/common/admin-bar.php:273
#: inc/deprecated/deprecated.php:1787
msgid "Support"
msgstr ""

#: inc/admin/admin.php:20
msgid "Docs"
msgstr ""

#: inc/admin/admin.php:22
#: inc/common/admin-bar.php:261
msgid "FAQ"
msgstr ""

#: inc/admin/admin.php:24
#: inc/common/admin-bar.php:70
msgid "Settings"
msgstr ""

#: inc/admin/admin.php:96
#: inc/admin/admin.php:117
#: inc/deprecated/3.5.php:898
#: inc/Engine/Cache/AdminSubscriber.php:134
msgid "Clear this cache"
msgstr ""

#: inc/admin/admin.php:402
msgid "Settings import failed: you do not have the permissions to do this."
msgstr ""

#: inc/admin/admin.php:406
msgid "Settings import failed: no file uploaded."
msgstr ""

#: inc/admin/admin.php:410
msgid "Settings import failed: incorrect filename."
msgstr ""

#: inc/admin/admin.php:421
msgid "Settings import failed: incorrect filetype."
msgstr ""

#: inc/admin/admin.php:431
msgid "Settings import failed: "
msgstr ""

#: inc/admin/admin.php:447
msgid "Settings import failed: unexpected file content."
msgstr ""

#: inc/admin/admin.php:492
msgid "Settings imported and saved."
msgstr ""

#: inc/admin/options.php:102
#: inc/Engine/Admin/Settings/Page.php:571
msgid "Excluded CSS Files"
msgstr ""

#: inc/admin/options.php:103
#: inc/Engine/Admin/Settings/Page.php:725
msgid "Excluded Inline JavaScript"
msgstr ""

#: inc/admin/options.php:104
#: inc/Engine/Admin/Settings/Page.php:743
#: inc/Engine/Admin/Settings/Page.php:776
#: inc/Engine/Admin/Settings/Page.php:823
msgid "Excluded JavaScript Files"
msgstr ""

#: inc/admin/options.php:105
msgid "Defer JavaScript Files"
msgstr ""

#: inc/admin/options.php:106
msgid "Excluded Delay JavaScript Files"
msgstr ""

#: inc/admin/options.php:107
#: inc/Engine/Admin/Settings/Page.php:1229
msgid "Never Cache URL(s)"
msgstr ""

#: inc/admin/options.php:108
#: inc/Engine/Admin/Settings/Page.php:1243
msgid "Never Cache User Agent(s)"
msgstr ""

#: inc/admin/options.php:109
#: inc/Engine/Admin/Settings/Page.php:1249
msgid "Always Purge URL(s)"
msgstr ""

#: inc/admin/options.php:110
#: inc/Engine/Admin/Settings/Page.php:1530
msgid "Exclude files from CDN"
msgstr ""

#. translators: 1 and 2 can be anything.
#: inc/admin/options.php:131
msgid "%1$s: <em>%2$s</em>"
msgstr ""

#: inc/admin/options.php:141
msgid "The following pattern is invalid and has been removed:"
msgid_plural "The following patterns are invalid and have been removed:"
msgstr[0] ""
msgstr[1] ""

#: inc/admin/options.php:157
msgid "More info"
msgstr ""

#: inc/admin/ui/meta-boxes.php:37
#: inc/admin/ui/notices.php:671
#: inc/common/admin-bar.php:91
#: inc/common/admin-bar.php:154
#: views/settings/page-sections/dashboard.php:150
msgid "Clear cache"
msgstr ""

#. translators: %1$s WP Rocket plugin name; %2$s = file name.
#: inc/admin/ui/notices.php:30
#: inc/admin/ui/notices.php:43
msgid ""
"<strong>%1$s</strong> has not been deactivated due to missing writing permissions.<br>\n"
"Make <strong>%2$s</strong> writeable and retry deactivation, or force deactivation now:"
msgstr ""

#. translators: %s is WP Rocket plugin name.
#: inc/admin/ui/notices.php:97
msgid "<strong>%s</strong>: One or more plugins have been enabled or disabled, clear the cache if they affect the front end of your site."
msgstr ""

#. translators: %s is WP Rocket plugin name.
#: inc/admin/ui/notices.php:141
msgid "<strong>%s</strong>: The following plugins are not compatible with this plugin and may cause unexpected results:"
msgstr ""

#: inc/admin/ui/notices.php:147
msgid "Deactivate"
msgstr ""

#: inc/admin/ui/notices.php:189
msgid "WP Rocket Footer JS is not an official add-on. It prevents some options in WP Rocket from working correctly. Please deactivate it if you have problems."
msgstr ""

#. translators: %1$s = opening link tag, %2$s = closing link tag.
#: inc/admin/ui/notices.php:229
msgid "Endurance Cache is currently enabled, which will conflict with WP Rocket Cache. Please set the Endurance Cache cache level to Off (Level 0) on the %1$sSettings > General%2$s page to prevent any issues."
msgstr ""

#. translators: %1$s WP Rocket plugin name; %2$s = opening link; %3$s = closing link
#: inc/admin/ui/notices.php:250
msgid "%1$s: A custom permalink structure is required for the plugin to work properly. %2$sGo to permalinks settings%3$s"
msgstr ""

#. translators: %s = plugin name.
#: inc/admin/ui/notices.php:297
msgid "%s could not modify the .htaccess file due to missing writing permissions."
msgstr ""

#. translators: This is a doc title! %1$s = opening link; %2$s = closing link
#: inc/admin/ui/notices.php:303
#: inc/admin/ui/notices.php:788
msgid "Troubleshoot: %1$sHow to make system files writeable%2$s"
msgstr ""

#. translators: Documentation exists in EN, DE, FR, ES, IT; use loaclised URL if applicable
#: inc/admin/ui/notices.php:305
#: inc/admin/ui/notices.php:790
msgid "https://docs.wp-rocket.me/article/626-how-to-make-system-files-htaccess-wp-config-writeable/?utm_source=wp_plugin&utm_medium=wp_rocket"
msgstr ""

#: inc/admin/ui/notices.php:311
msgid "Don’t worry, WP Rocket’s page caching and settings will still function correctly."
msgstr ""

#: inc/admin/ui/notices.php:311
msgid "For optimal performance, adding the following lines into your .htaccess is recommended (not required):"
msgstr ""

#. translators: %1$s = plugin name, %2$s + %3$s = opening links, %4$s = closing link
#: inc/admin/ui/notices.php:458
msgid "%1$s is good to go! %2$sTest your load time%4$s, or visit your %3$ssettings%4$s."
msgstr ""

#: inc/admin/ui/notices.php:499
msgid "Would you allow WP Rocket to collect non-sensitive diagnostic data from this website?"
msgstr ""

#: inc/admin/ui/notices.php:500
msgid "This would help us to improve WP Rocket for you in the future."
msgstr ""

#. translators: button text, click will expand data collection preview
#: inc/admin/ui/notices.php:506
msgid "What info will we collect?"
msgstr ""

#: inc/admin/ui/notices.php:511
msgid "Below is a detailed view of all data WP Rocket will collect if granted permission. WP Rocket will never transmit any domain names or email addresses (except for license validation), IP addresses, or third-party API keys."
msgstr ""

#. translators: button text for data collection opt-in
#: inc/admin/ui/notices.php:520
msgid "Yes, allow"
msgstr ""

#. translators: button text for data collection opt-in
#: inc/admin/ui/notices.php:523
msgid "No, thanks"
msgstr ""

#: inc/admin/ui/notices.php:562
msgid "Thank you!"
msgstr ""

#: inc/admin/ui/notices.php:567
msgid "WP Rocket now collects these metrics from your website:"
msgstr ""

#. translators: %s = plugin name.
#: inc/admin/ui/notices.php:605
msgid "%s: Cache cleared."
msgstr ""

#. translators: %s = plugin name.
#: inc/admin/ui/notices.php:612
msgid "%s: Post cache cleared."
msgstr ""

#. translators: %s = plugin name.
#: inc/admin/ui/notices.php:619
msgid "%s: Term cache cleared."
msgstr ""

#. translators: %s = plugin name).
#: inc/admin/ui/notices.php:626
msgid "%s: User cache cleared."
msgstr ""

#: inc/admin/ui/notices.php:662
#: inc/Engine/License/views/promo-banner.php:36
#: inc/Engine/License/views/renewal-expired-banner-ocd-disabled.php:32
#: inc/Engine/License/views/renewal-expired-banner-ocd.php:42
#: inc/Engine/License/views/renewal-expired-banner.php:30
#: views/settings/page-sections/dashboard.php:46
msgid "Dismiss this notice"
msgstr ""

#: inc/admin/ui/notices.php:682
#: inc/Engine/Saas/Admin/AdminBar.php:84
#: inc/Engine/Saas/Admin/AdminBar.php:202
msgid "Clear Used CSS"
msgstr ""

#: inc/admin/ui/notices.php:685
msgid "Stop Preload"
msgstr ""

#: inc/admin/ui/notices.php:691
msgid "Turn on Remove Unused CSS"
msgstr ""

#: inc/admin/ui/notices.php:697
msgid "Enable “Separate Cache Files for Mobile Devices” now"
msgstr ""

#: inc/admin/ui/notices.php:715
msgid "Force deactivation "
msgstr ""

#: inc/admin/ui/notices.php:745
msgid "The following code should have been written to this file:"
msgstr ""

#. translators: %s = plugin name.
#: inc/admin/ui/notices.php:776
msgid "%s cannot configure itself due to missing writing permissions."
msgstr ""

#. translators: %s = file/folder name
#: inc/admin/ui/notices.php:782
msgid "Affected file/folder: %s"
msgstr ""

#: inc/classes/admin/class-logs.php:124
msgid "The debug file could not be deleted."
msgstr ""

#. Translators: %1$s = Plugin name, %2$s = Plugin version.
#: inc/classes/class-wp-rocket-requirements-check.php:147
msgid "To function properly, %1$s %2$s requires at least:"
msgstr ""

#. Translators: %1$s = PHP version required.
#: inc/classes/class-wp-rocket-requirements-check.php:151
msgid "PHP %1$s. To use this WP Rocket version, please ask your web host how to upgrade your server to PHP %1$s or higher."
msgstr ""

#. Translators: %1$s = WordPress version required.
#: inc/classes/class-wp-rocket-requirements-check.php:156
msgid "WordPress %1$s. To use this WP Rocket version, please upgrade WordPress to version %1$s or higher."
msgstr ""

#: inc/classes/class-wp-rocket-requirements-check.php:159
msgid "If you are not able to upgrade, you can rollback to the previous version by using the button below."
msgstr ""

#. Translators: %s = Previous plugin version.
#: inc/classes/class-wp-rocket-requirements-check.php:161
#: inc/deprecated/deprecated.php:1947
msgid "Re-install version %s"
msgstr ""

#. translators: %s is the plugin name.
#: inc/classes/class-wp-rocket-requirements-check.php:203
#: inc/classes/class-wp-rocket-requirements-check.php:214
#: inc/deprecated/3.11.php:236
#: inc/deprecated/3.11.php:251
#: inc/Engine/Plugin/UpdaterSubscriber.php:483
#: inc/Engine/Plugin/UpdaterSubscriber.php:497
msgid "%s Update Rollback"
msgstr ""

#. translators: %1$s = missing tags;
#: inc/classes/subscriber/Tools/class-detect-missing-tags-subscriber.php:151
msgid "Failed to detect the following requirement in your theme: closing %1$s."
msgid_plural "Failed to detect the following requirements in your theme: closing %1$s."
msgstr[0] ""
msgstr[1] ""

#. translators: Documentation exists in EN, FR; use localized URL if applicable.
#: inc/classes/subscriber/Tools/class-detect-missing-tags-subscriber.php:159
msgid "https://docs.wp-rocket.me/article/99-pages-not-cached-or-minify-cssjs-not-working/?utm_source=wp_plugin&utm_medium=wp_rocket#theme"
msgstr ""

#: inc/common/admin-bar.php:91
#: inc/common/admin-bar.php:154
#: views/settings/page-sections/dashboard.php:150
msgid "Clear and preload cache"
msgstr ""

#: inc/common/admin-bar.php:131
#: inc/functions/i18n.php:20
msgid "All languages"
msgstr ""

#: inc/common/admin-bar.php:178
msgid "Clear this post"
msgstr ""

#: inc/common/admin-bar.php:192
msgid "Purge this URL"
msgstr ""

#: inc/common/admin-bar.php:212
msgid "Purge Sucuri cache"
msgstr ""

#: inc/common/admin-bar.php:236
#: views/settings/fields/rocket-cdn.php:55
msgid "Purge RocketCDN cache"
msgstr ""

#: inc/common/admin-bar.php:249
#: views/settings/partials/documentation.php:14
msgid "Documentation"
msgstr ""

#: inc/deprecated/3.2.php:52
#: views/settings/page-sections/imagify.php:36
msgid "Activate Imagify"
msgstr ""

#: inc/deprecated/3.2.php:52
msgid "Install Imagify for Free"
msgstr ""

#: inc/deprecated/3.2.php:67
msgid "Speed up your website and boost your SEO by reducing image file sizes without losing quality with Imagify."
msgstr ""

#: inc/deprecated/3.2.php:72
msgid "More details"
msgstr ""

#. translators: %d is the number of pages preloaded.
#: inc/deprecated/3.2.php:228
msgid "Sitemap preload: %d pages have been cached."
msgstr ""

#. translators: %d = Number of pages preloaded.
#: inc/deprecated/3.2.php:261
msgid "Sitemap preload: %d uncached pages have now been preloaded. (refresh to see progress)"
msgstr ""

#. translators: %s is an URL.
#: inc/deprecated/3.3.php:761
#: inc/deprecated/3.3.php:770
#: inc/Engine/Plugin/UpdaterApiTools.php:32
msgid "An unexpected error occurred. Something may be wrong with WP-Rocket.me or this server&#8217;s configuration. If you continue to have problems, <a href=\"%s\">contact support</a>."
msgstr ""

#: inc/deprecated/3.4.php:18
msgid "Choose a domain from the list"
msgstr ""

#: inc/deprecated/3.4.php:31
msgid "No domain available in your Cloudflare account"
msgstr ""

#: inc/deprecated/3.5.php:71
#: inc/deprecated/3.5.php:195
msgid "Curl is disabled on your server. Please ask your host to enable it. This is required for the Cloudflare Add-on to work correctly."
msgstr ""

#. translators: %1$s = opening link; %2$s = closing link
#: inc/deprecated/3.5.php:79
msgid "Cloudflare email, API key and Zone ID are not set. Read the %1$sdocumentation%2$s for further guidance."
msgstr ""

#. translators: %1$s = opening link; %2$s = closing link
#: inc/deprecated/3.5.php:206
msgid "Cloudflare email and API key are not set. Read the %1$sdocumentation%2$s for further guidance."
msgstr ""

#: inc/deprecated/3.5.php:271
msgid "Connection to Cloudflare failed"
msgstr ""

#. translators: %s = CloudFare API return message.
#: inc/deprecated/3.5.php:587
msgid "<strong>WP Rocket:</strong> %s"
msgstr ""

#: inc/deprecated/3.5.php:592
msgid "<strong>WP Rocket:</strong> Cloudflare cache successfully purged."
msgstr ""

#: inc/deprecated/3.5.php:858
#: inc/Engine/HealthCheck/HealthCheck.php:81
msgid "The following scheduled event failed to run. This may indicate the CRON system is not running properly, which can prevent some WP Rocket features from working as intended:"
msgid_plural "The following scheduled events failed to run. This may indicate the CRON system is not running properly, which can prevent some WP Rocket features from working as intended:"
msgstr[0] ""
msgstr[1] ""

#: inc/deprecated/3.5.php:867
#: inc/Engine/HealthCheck/HealthCheck.php:88
msgid "Please contact your host to check if CRON is working."
msgstr ""

#: inc/deprecated/3.10.php:163
msgid "OPcache purge failed."
msgstr ""

#: inc/deprecated/3.10.php:168
msgid "OPcache successfully purged"
msgstr ""

#: inc/deprecated/3.11.php:81
msgid "Yoast SEO XML sitemap"
msgstr ""

#. translators: %s = Name of the plugin.
#. translators: %s = plugin name, e.g. Yoast SEO.
#: inc/deprecated/3.11.php:83
#: inc/deprecated/3.12.php:58
#: inc/deprecated/3.12.php:87
#: inc/deprecated/3.12.php:186
#: inc/deprecated/3.12.php:253
#: inc/deprecated/3.12.php:342
msgid "We automatically detected the sitemap generated by the %s plugin. You can check the option to preload it."
msgstr ""

#. translators: 1 and 3 are link openings, 2 is a link closing.
#: inc/deprecated/3.11.php:279
#: inc/Engine/Plugin/UpdaterSubscriber.php:520
msgid "%1$sReturn to WP Rocket%2$s or %3$sgo to Plugins page%2$s"
msgstr ""

#: inc/deprecated/3.12.php:56
msgid "All in One SEO XML sitemap"
msgstr ""

#: inc/deprecated/3.12.php:85
msgid "Rank Math XML sitemap"
msgstr ""

#: inc/deprecated/3.12.php:184
msgid "SEOPress XML sitemap"
msgstr ""

#: inc/deprecated/3.12.php:251
msgid "The SEO Framework XML sitemap"
msgstr ""

#: inc/deprecated/3.12.php:332
msgid "Jetpack XML Sitemaps"
msgstr ""

#: inc/deprecated/3.12.php:334
msgid "Preload the sitemap from the Jetpack plugin"
msgstr ""

#: inc/deprecated/3.15.php:28
#: inc/Engine/Admin/Metaboxes/PostEditOptionsSubscriber.php:74
msgid "WP Rocket Options"
msgstr ""

#: inc/deprecated/3.15.php:57
#: views/metaboxes/post_edit_options.php:19
msgid "Never cache this page"
msgstr ""

#: inc/deprecated/3.15.php:61
#: views/metaboxes/post_edit_options.php:23
msgid "Activate these options on this post:"
msgstr ""

#. translators: %s is the name of the option.
#: inc/deprecated/3.15.php:81
#: inc/Engine/Admin/Metaboxes/PostEditOptionsSubscriber.php:121
msgid "Activate first the %s option."
msgstr ""

#. translators: %1$s = opening strong tag, %2$s = closing strong tag.
#: inc/deprecated/3.15.php:97
#: views/metaboxes/post_edit_options.php:38
msgid "%1$sNote:%2$s None of these options will be applied if this post has been excluded from cache in the global cache settings."
msgstr ""

#: inc/deprecated/deprecated.php:240
msgid "<strong>JS</strong> files with Deferred Loading JavaScript"
msgstr ""

#: inc/deprecated/deprecated.php:293
msgid "Add URL"
msgstr ""

#: inc/deprecated/deprecated.php:1245
#: inc/Engine/Admin/Settings/Settings.php:451
msgid "Settings saved."
msgstr ""

#: inc/deprecated/deprecated.php:1277
#: views/settings/fields/import-form.php:22
msgid "Before you can upload your import file, you will need to fix the following error:"
msgstr ""

#. translators: %s is the maximum upload size set on the current server.
#: inc/deprecated/deprecated.php:1288
#: views/settings/fields/import-form.php:35
msgid "Choose a file from your computer (maximum size: %s)"
msgstr ""

#: inc/deprecated/deprecated.php:1294
#: inc/Engine/Admin/Settings/Render.php:422
msgid "Upload file and import settings"
msgstr ""

#: inc/deprecated/deprecated.php:1385
msgid "Your Cloudflare credentials are valid."
msgstr ""

#: inc/deprecated/deprecated.php:1389
msgid "Your Cloudflare credentials are invalid!"
msgstr ""

#: inc/deprecated/deprecated.php:1453
msgid "Save and optimize"
msgstr ""

#: inc/deprecated/deprecated.php:1453
msgid "Optimize"
msgstr ""

#: inc/deprecated/deprecated.php:1464
#: inc/deprecated/deprecated.php:1666
msgctxt "screen-reader-text"
msgid "Note:"
msgstr ""

#: inc/deprecated/deprecated.php:1470
msgctxt "screen-reader-text"
msgid "Performance tip:"
msgstr ""

#: inc/deprecated/deprecated.php:1476
msgctxt "screen-reader-text"
msgid "Third-party feature detected:"
msgstr ""

#: inc/deprecated/deprecated.php:1488
#: inc/deprecated/deprecated.php:1672
msgctxt "screen-reader-text"
msgid "Warning:"
msgstr ""

#: inc/deprecated/deprecated.php:1501
msgctxt "button text"
msgid "Download settings"
msgstr ""

#: inc/deprecated/deprecated.php:1531
msgid "Replace site's hostname with:"
msgstr ""

#: inc/deprecated/deprecated.php:1550
#: inc/deprecated/deprecated.php:1587
#: inc/deprecated/deprecated.php:1615
#: views/settings/fields/cnames.php:41
#: views/settings/fields/cnames.php:75
#: views/settings/fields/cnames.php:105
msgid "reserved for"
msgstr ""

#: inc/deprecated/deprecated.php:1552
#: inc/deprecated/deprecated.php:1589
#: inc/deprecated/deprecated.php:1617
#: views/settings/fields/cnames.php:44
#: views/settings/fields/cnames.php:78
#: views/settings/fields/cnames.php:108
msgid "All files"
msgstr ""

#: inc/deprecated/deprecated.php:1564
#: inc/deprecated/deprecated.php:1594
#: inc/deprecated/deprecated.php:1622
#: views/settings/fields/cnames.php:56
#: views/settings/fields/cnames.php:90
#: views/settings/fields/cnames.php:120
msgid "Images"
msgstr ""

#: inc/deprecated/deprecated.php:1635
#: views/settings/fields/cnames.php:132
msgid "Add CNAME"
msgstr ""

#: inc/deprecated/deprecated.php:1706
msgid "Watch the video"
msgstr ""

#: inc/deprecated/deprecated.php:1771
msgid "Basic"
msgstr ""

#: inc/deprecated/deprecated.php:1772
msgid "Static Files"
msgstr ""

#: inc/deprecated/deprecated.php:1773
#: inc/Engine/Admin/Settings/Page.php:1500
#: inc/Engine/Admin/Settings/Page.php:1511
#: inc/Engine/CDN/Admin/Subscriber.php:28
msgid "CDN"
msgstr ""

#: inc/deprecated/deprecated.php:1774
msgid "Advanced"
msgstr ""

#: inc/deprecated/deprecated.php:1775
#: inc/Engine/Admin/Settings/Page.php:1345
msgid "Database"
msgstr ""

#: inc/deprecated/deprecated.php:1776
#: inc/Engine/Admin/Settings/Page.php:1051
msgid "Preload"
msgstr ""

#: inc/deprecated/deprecated.php:1786
#: inc/Engine/Admin/Settings/Subscriber.php:171
#: views/settings/page-sections/tools.php:33
msgid "Tools"
msgstr ""

#: inc/deprecated/deprecated.php:1789
#: inc/Engine/Admin/Settings/Page.php:357
#: views/settings/page-sections/dashboard.php:80
msgid "License"
msgstr ""

#. Translators: %1$s = Plugin name, %2$s = Plugin version, %3$s = PHP version required.
#: inc/deprecated/deprecated.php:1944
msgid "%1$s %2$s requires at least PHP %3$s to function properly. To use this version, please ask your web host how to upgrade your server to PHP %3$s or higher. If you are not able to upgrade, you can rollback to the previous version by using the button below."
msgstr ""

#. translators: 1: PHP class name, 2: version number, 3: replacement class name.
#: inc/deprecated/DeprecatedClassTrait.php:54
msgid "The called class %1$s is <strong>deprecated</strong> since version %2$s! Use %3$s instead."
msgstr ""

#. translators: 1: PHP class name, 2: version number.
#: inc/deprecated/DeprecatedClassTrait.php:65
msgid "The called class %1$s is <strong>deprecated</strong> since version %2$s!"
msgstr ""

#: inc/deprecated/Engine/Addon/FacebookTracking/Subscriber.php:92
#: inc/deprecated/Engine/Addon/GoogleTracking/Subscriber.php:137
#: inc/Engine/Admin/Database/Subscriber.php:79
msgid "weekly"
msgstr ""

#: inc/Engine/Admin/Database/Optimization.php:30
#: inc/Engine/Admin/Settings/Page.php:1389
msgid "Revisions"
msgstr ""

#: inc/Engine/Admin/Database/Optimization.php:31
#: inc/Engine/Admin/Settings/Page.php:1399
msgid "Auto Drafts"
msgstr ""

#: inc/Engine/Admin/Database/Optimization.php:32
#: inc/Engine/Admin/Settings/Page.php:1409
msgid "Trashed Posts"
msgstr ""

#: inc/Engine/Admin/Database/Optimization.php:33
#: inc/Engine/Admin/Settings/Page.php:1419
msgid "Spam Comments"
msgstr ""

#: inc/Engine/Admin/Database/Optimization.php:34
#: inc/Engine/Admin/Settings/Page.php:1429
msgid "Trashed Comments"
msgstr ""

#: inc/Engine/Admin/Database/Optimization.php:35
msgid "Transients"
msgstr ""

#: inc/Engine/Admin/Database/Optimization.php:36
msgid "Tables"
msgstr ""

#: inc/Engine/Admin/Database/Subscriber.php:85
msgid "monthly"
msgstr ""

#: inc/Engine/Admin/Database/Subscriber.php:194
msgid "Database optimization process is running"
msgstr ""

#: inc/Engine/Admin/Database/Subscriber.php:224
msgid "Database optimization process is complete. Everything was already optimized!"
msgstr ""

#: inc/Engine/Admin/Database/Subscriber.php:227
msgid "Database optimization process is complete. List of optimized items below:"
msgstr ""

#. translators: %1$d = number of items optimized, %2$s = type of optimization
#: inc/Engine/Admin/Database/Subscriber.php:235
msgid "%1$d %2$s optimized."
msgstr ""

#. translators: %1$s = <strong>, %2$s = </strong>, %3$s = <a>, %4$s = </a>.
#: inc/Engine/Admin/DomainChange/Subscriber.php:143
msgid "%1$sWP Rocket:%2$s We detected that the website domain has changed. The configuration files must be regenerated for the page cache and all other optimizations to work as intended. %3$sLearn More%4$s"
msgstr ""

#: inc/Engine/Admin/DomainChange/Subscriber.php:171
msgid "Regenerate WP Rocket configuration files now"
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:223
msgid "Save Changes"
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:223
msgid "Validate License"
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:279
#: inc/Engine/Admin/Settings/Page.php:280
#: inc/functions/admin.php:550
msgid "Unavailable"
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:374
msgid "API key"
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:389
msgid "Email address"
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:415
msgid "Dashboard"
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:416
msgid "Get help, account info"
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:425
msgid "My Status"
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:435
#: views/settings/page.php:75
msgid "Rocket Analytics"
msgstr ""

#. translators: %1$s = opening <a> tag, %2$s = closing </a> tag.
#: inc/Engine/Admin/Settings/Page.php:437
msgid "I agree to share anonymous data with the development team to help improve WP Rocket. %1$sWhat info will we collect?%2$s"
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:489
msgid "File Optimization"
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:490
msgid "Optimize CSS & JS"
msgstr ""

#. translators: %1$s = type of minification (HTML, CSS or JS), %2$s = “WP Rocket”.
#: inc/Engine/Admin/Settings/Page.php:498
msgid "%1$s Minification is currently activated in <strong>Autoptimize</strong>. If you want to use %2$s’s minification, disable this option in Autoptimize."
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:508
msgid "CSS Files"
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:517
msgid "JavaScript Files"
msgstr ""

#. translators: %1$s = type of minification (HTML, CSS or JS), %2$s = “WP Rocket”.
#: inc/Engine/Admin/Settings/Page.php:524
msgid "%1$s Minification is currently activated in <strong>Autoptimize</strong>. If you want to use %2$s’s minification, disable those options in Autoptimize."
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:529
msgid "If you have problems after activating this option, copy and paste the default exclusions to quickly resolve issues:"
msgstr ""

#. translators: %1$s = opening </a> tag, %2$s = closing </a> tag.
#: inc/Engine/Admin/Settings/Page.php:533
msgid "Also, please check our %1$sdocumentation%2$s for a list of compatibility exclusions."
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:538
msgid "Internal scripts are excluded by default to prevent issues. Remove them to take full advantage of this option."
msgstr ""

#. translators: %1$s = opening </a> tag, %2$s = closing </a> tag.
#: inc/Engine/Admin/Settings/Page.php:541
msgid "If this causes trouble, restore the default exclusions, found %1$shere%2$s"
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:550
msgid "Minify CSS files"
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:551
msgid "Minify CSS removes whitespace and comments to reduce the file size."
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:564
#: inc/Engine/Admin/Settings/Page.php:628
#: inc/Engine/Admin/Settings/Page.php:694
#: inc/Engine/Admin/Settings/Page.php:718
msgid "This could break things!"
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:565
#: inc/Engine/Admin/Settings/Page.php:629
#: inc/Engine/Admin/Settings/Page.php:695
#: inc/Engine/Admin/Settings/Page.php:719
msgid "If you notice any errors on your website after having activated this setting, just deactivate it again, and your site will be back to normal."
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:566
msgid "Activate minify CSS"
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:572
msgid "Specify URLs of CSS files to be excluded from minification (one per line)."
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:573
msgid "<strong>Internal:</strong> The domain part of the URL will be stripped automatically. Use (.*).css wildcards to exclude all CSS files located at a specific path."
msgstr ""

#. translators: %1$s = opening <a> tag, %2$s = closing </a> tag.
#: inc/Engine/Admin/Settings/Page.php:575
msgid "<strong>3rd Party:</strong> Use either the full URL path or only the domain name, to exclude external CSS. %1$sMore info%2$s"
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:588
#: inc/Engine/Admin/Settings/Page.php:610
msgid "Optimize CSS delivery"
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:593
msgid "Optimize CSS delivery eliminates render-blocking CSS on your website. Only one method can be selected. Remove Unused CSS is recommended for optimal performance, but limited only to the users with active license."
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:593
msgid "Optimize CSS delivery eliminates render-blocking CSS on your website. Only one method can be selected. Remove Unused CSS is recommended for optimal performance."
msgstr ""

#. translators: %1$s = opening <a> tag, %2$s = closing </a> tag.
#: inc/Engine/Admin/Settings/Page.php:603
msgid "Optimize CSS Delivery features are disabled on local environments. %1$sLearn more%2$s"
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:623
#: inc/Engine/Optimization/RUCSS/Admin/OptionSubscriber.php:74
msgid "Remove Unused CSS"
msgstr ""

#. translators: %1$s = opening <a> tag, %2$s = closing </a> tag.
#: inc/Engine/Admin/Settings/Page.php:626
msgid "Removes unused CSS per page and helps to reduce page size and HTTP requests. Recommended for best performance. Test thoroughly! %1$sMore info%2$s"
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:630
msgid "Activate Remove Unused CSS"
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:636
msgid "CSS safelist"
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:637
msgid "Specify CSS filenames, IDs or classes that should not be removed (one per line)."
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:652
#: inc/Engine/CriticalPath/Admin/Subscriber.php:200
msgid "Load CSS asynchronously"
msgstr ""

#. translators: %1$s = plugin name.
#: inc/Engine/Admin/Settings/Page.php:655
msgctxt "WP Critical CSS compatibility"
msgid "Load CSS asynchronously is currently handled by the %1$s plugin. If you want to use WP Rocket’s load CSS asynchronously option, disable the %1$s plugin."
msgstr ""

#. translators: %1$s = opening <a> tag, %2$s = closing </a> tag.
#: inc/Engine/Admin/Settings/Page.php:657
msgid "Generates critical path CSS and loads CSS asynchronously. %1$sMore info%2$s"
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:663
msgid "Fallback critical CSS"
msgstr ""

#. translators: %1$s = opening <a> tag, %2$s = closing </a> tag.
#: inc/Engine/Admin/Settings/Page.php:665
msgid "Provides a fallback if auto-generated critical path CSS is incomplete. %1$sMore info%2$s"
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:680
msgid "Minify JavaScript files"
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:681
msgid "Minify JavaScript removes whitespace and comments to reduce the file size."
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:696
msgid "Activate minify JavaScript"
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:701
msgid "Combine JavaScript files <em>(Enable Minify JavaScript files to select)</em>"
msgstr ""

#. translators: %1$s = opening <a> tag, %2$s = closing </a> tag.
#: inc/Engine/Admin/Settings/Page.php:703
msgid "Combine JavaScript files combines your site’s internal, 3rd party and inline JS reducing HTTP requests. Not recommended if your site uses HTTP/2. %1$sMore info%2$s"
msgstr ""

#. translators: %1$s = opening <a> tag, %2$s = closing </a> tag.
#: inc/Engine/Admin/Settings/Page.php:704
msgid "For compatibility and best results, this option is disabled when delay javascript execution is enabled."
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:720
msgid "Activate combine JavaScript"
msgstr ""

#. translators: %1$s = opening <a> tag, %2$s = closing </a> tag.
#: inc/Engine/Admin/Settings/Page.php:727
msgid "Specify patterns of inline JavaScript to be excluded from concatenation (one per line). %1$sMore info%2$s"
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:744
msgid "Specify URLs of JavaScript files to be excluded from minification and concatenation (one per line)."
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:745
msgid "<strong>Internal:</strong> The domain part of the URL will be stripped automatically. Use (.*).js wildcards to exclude all JS files located at a specific path."
msgstr ""

#. translators: %1$s = opening <a> tag, %2$s = closing </a> tag.
#: inc/Engine/Admin/Settings/Page.php:747
msgid "<strong>3rd Party:</strong> Use either the full URL path or only the domain name, to exclude external JS. %1$sMore info%2$s"
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:763
#: inc/Engine/Optimization/DeferJS/AdminSubscriber.php:76
msgid "Load JavaScript deferred"
msgstr ""

#. translators: %1$s = opening <a> tag, %2$s = closing </a> tag.
#: inc/Engine/Admin/Settings/Page.php:765
msgid "Load JavaScript deferred eliminates render-blocking JS on your site and can improve load time. %1$sMore info%2$s"
msgstr ""

#. translators: %1$s = opening <a> tag, %2$s = closing </a> tag.
#: inc/Engine/Admin/Settings/Page.php:778
msgid "Specify URLs or keywords of JavaScript files to be excluded from defer (one per line). %1$sMore info%2$s"
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:794
#: inc/Engine/Optimization/DelayJS/Admin/Subscriber.php:210
msgid "Delay JavaScript execution"
msgstr ""

#. translators: %1$s = opening <a> tag, %2$s = closing </a> tag.
#: inc/Engine/Admin/Settings/Page.php:796
msgid "Improves performance by delaying the loading of JavaScript files until user interaction (e.g. scroll, click). %1$sMore info%2$s"
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:805
msgid "One-click exclusions"
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:806
msgid "When using the Delay JavaScript Execution, you might experience delay loading elements located in the viewport that need to appear immediately - e.g. slider, header, menu."
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:807
msgid "If you need instant visibility, click below on files that should NOT be delayed. This selection will help users interact with the elements straight away."
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:824
msgid "Specify URLs or keywords that can identify inline or JavaScript files to be excluded from delaying execution (one per line)."
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:856
msgid "Media"
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:857
msgid "LazyLoad, image dimensions"
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:866
msgid "Autoptimize"
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:920
msgid "LazyLoad"
msgstr ""

#. translators: %1$s = opening <a> tag, %2$s = closing </a> tag.
#: inc/Engine/Admin/Settings/Page.php:923
msgid "It can improve actual and perceived loading time as images, iframes, and videos will be loaded only as they enter (or about to enter) the viewport and reduces the number of HTTP requests. %1$sMore Info%2$s"
msgstr ""

#. translators: %1$s = “WP Rocket”, %2$s = a list of plugin names.
#: inc/Engine/Admin/Settings/Page.php:930
msgid "LazyLoad is currently activated in %2$s. If you want to use WP Rocket’s LazyLoad, disable this option in %2$s."
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:933
msgid "Image Dimensions"
msgstr ""

#. translators: %1$s = opening <a> tag, %2$s = closing </a> tag.
#: inc/Engine/Admin/Settings/Page.php:936
msgid "Add missing width and height attributes to images. Helps prevent layout shifts and improve the reading experience for your visitors. %1$sMore info%2$s"
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:955
msgid "Enable for images"
msgstr ""

#. translators: %1$s = “WP Rocket”, %2$s = a list of plugin names.
#: inc/Engine/Admin/Settings/Page.php:967
msgid "LazyLoad for images is currently activated in %2$s. If you want to use %1$s’s LazyLoad, disable this option in %2$s."
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:975
msgid "Enable for CSS background images"
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:990
msgid "Enable for iframes and videos"
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:1005
msgid "Replace YouTube iframe with preview image"
msgstr ""

#. translators: %1$s = “WP Rocket”, %2$s = a list of plugin or themes names.
#: inc/Engine/Admin/Settings/Page.php:1007
msgid "Replace YouTube iframe with preview image is not compatible with %2$s."
msgstr ""

#. translators: %1$s = “WP Rocket”, %2$s = a list of plugin or themes names.
#: inc/Engine/Admin/Settings/Page.php:1007
msgid "This can significantly improve your loading time if you have a lot of YouTube videos on a page."
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:1022
msgid "Excluded images or iframes"
msgstr ""

#. translators: %1$s = opening <a> tag, %2$s = closing </a> tag.
#: inc/Engine/Admin/Settings/Page.php:1024
msgid "Specify keywords (e.g. image filename, CSS filename, CSS class, domain) from the image or iframe code to be excluded (one per line). %1$sMore info%2$s"
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:1032
msgid "Add missing image dimensions"
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:1052
msgid "Generate cache files, preload fonts"
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:1064
msgid "Preload Cache"
msgstr ""

#. translators: %1$s = opening <a> tag, %2$s = closing </a> tag.
#: inc/Engine/Admin/Settings/Page.php:1067
msgid "When you enable preloading WP Rocket will automatically detect your sitemaps and save all URLs to the database. The plugin will make sure that your cache is always preloaded."
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:1075
msgid "Preload Links"
msgstr ""

#. translators: %1$s = opening <a> tag, %2$s = closing </a> tag.
#: inc/Engine/Admin/Settings/Page.php:1078
msgid "Link preloading improves the perceived load time by downloading a page when a user hovers over the link. %1$sMore info%2$s"
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:1086
msgid "Prefetch DNS Requests"
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:1088
msgid "DNS prefetching can make external files load faster, especially on mobile networks"
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:1093
msgid "Preload Fonts"
msgstr ""

#. translators: %1$s = opening <a> tag, %2$s = closing </a> tag.
#: inc/Engine/Admin/Settings/Page.php:1096
msgid "Improves performance by helping browsers discover fonts in CSS files. %1$sMore info%2$s"
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:1110
msgid "Activate Preloading"
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:1121
msgid "Exclude URLs"
msgstr ""

#. translators: %1$s = opening <a> tag, %2$s = closing </a> tag.
#: inc/Engine/Admin/Settings/Page.php:1126
msgid "Specify URLs to be excluded from the preload feature (one per line). %1$sMore info%2$s"
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:1137
msgid "URLs to prefetch"
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:1138
msgid "Specify external hosts to be prefetched (no <code>http:</code>, one per line)"
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:1147
msgid "Fonts to preload"
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:1148
msgid "Specify urls of the font files to be preloaded (one per line). Fonts must be hosted on your own domain, or the domain you have specified on the CDN tab."
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:1149
msgid "The domain part of the URL will be stripped automatically.<br/>Allowed font extensions: otf, ttf, svg, woff, woff2."
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:1158
msgid "Enable link preloading"
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:1177
msgid "Advanced Rules"
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:1178
msgid "Fine-tune cache rules"
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:1191
msgid "Sensitive pages like custom login/logout URLs should be excluded from cache."
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:1194
msgctxt "plugin name"
msgid "WooCommerce"
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:1196
msgctxt "plugin name"
msgid "Easy Digital Downloads"
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:1198
msgctxt "plugin name"
msgid "iThemes Exchange"
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:1200
msgctxt "plugin name"
msgid "Jigoshop"
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:1202
msgctxt "plugin name"
msgid "WP-Shop"
msgstr ""

#. translators: %1$s = opening <a> tag, %2$s = plugin name, %3$s closing </a> tag.
#: inc/Engine/Admin/Settings/Page.php:1208
msgid "<br>Cart, checkout and \"my account\" pages set in <strong>%1$s%2$s%3$s</strong> will be detected and never cached by default."
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:1218
msgid "Cache Lifespan"
msgstr ""

#. translators: %1$s = opening <a> tag, %2$s = closing </a> tag.
#: inc/Engine/Admin/Settings/Page.php:1221
msgid "Cache files older than the specified lifespan will be deleted.<br>Enable %1$spreloading%2$s for the cache to be rebuilt automatically after lifespan expiration."
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:1237
msgid "Never Cache Cookies"
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:1255
msgid "Cache Query String(s)"
msgstr ""

#. translators: %1$s = opening <a> tag, %2$s = closing </a> tag.
#: inc/Engine/Admin/Settings/Page.php:1258
msgid "%1$sCache for query strings%2$s enables you to force caching for specific GET parameters."
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:1269
msgid "Specify time after which the global cache is cleared<br>(0 = unlimited )"
msgstr ""

#. translators: %1$s = opening <a> tag, %2$s = closing </a> tag.
#: inc/Engine/Admin/Settings/Page.php:1271
msgid "Reduce lifespan to 10 hours or less if you notice issues that seem to appear periodically. %1$sWhy?%2$s"
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:1277
#: inc/Engine/License/views/promo-banner.php:30
#: inc/Engine/License/views/renewal-soon-banner.php:13
msgid "Hours"
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:1278
#: inc/Engine/License/views/promo-banner.php:29
#: inc/Engine/License/views/renewal-soon-banner.php:12
msgid "Days"
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:1283
msgid "Specify URLs of pages or posts that should never be cached (one per line)"
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:1284
#: inc/Engine/Admin/Settings/Page.php:1312
msgid "The domain part of the URL will be stripped automatically.<br>Use (.*) wildcards to address multiple URLs under a given path."
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:1293
msgid "Specify full or partial IDs of cookies that, when set in the visitor's browser, should prevent a page from getting cached (one per line)"
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:1301
msgid "Specify user agent strings that should never see cached pages (one per line)"
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:1302
msgid "Use (.*) wildcards to detect parts of UA strings."
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:1311
msgid "Specify URLs you always want purged from cache whenever you update any post or page (one per line)"
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:1320
msgid "Specify query strings for caching (one per line)"
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:1346
msgid "Optimize, reduce bloat"
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:1353
msgid "Post Cleanup"
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:1355
msgid "Post revisions and drafts will be permanently deleted. Do not use this option if you need to retain revisions or drafts."
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:1360
msgid "Comments Cleanup"
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:1362
msgid "Spam and trashed comments will be permanently deleted."
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:1366
msgid "Transients Cleanup"
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:1368
msgid "Transients are temporary options; they are safe to remove. They will be automatically regenerated as your plugins require them."
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:1372
msgid "Database Cleanup"
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:1374
msgid "Reduces overhead of database tables"
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:1378
msgid "Automatic Cleanup"
msgstr ""

#. translators: %s is the number of revisions found in the database. It's a formatted number, don't use %d.
#: inc/Engine/Admin/Settings/Page.php:1391
msgid "%s revision in your database."
msgid_plural "%s revisions in your database."
msgstr[0] ""
msgstr[1] ""

#. translators: %s is the number of revisions found in the database. It's a formatted number, don't use %d.
#: inc/Engine/Admin/Settings/Page.php:1401
msgid "%s draft in your database."
msgid_plural "%s drafts in your database."
msgstr[0] ""
msgstr[1] ""

#. translators: %s is the number of revisions found in the database. It's a formatted number, don't use %d.
#: inc/Engine/Admin/Settings/Page.php:1411
msgid "%s trashed post in your database."
msgid_plural "%s trashed posts in your database."
msgstr[0] ""
msgstr[1] ""

#. translators: %s is the number of revisions found in the database. It's a formatted number, don't use %d.
#: inc/Engine/Admin/Settings/Page.php:1421
msgid "%s spam comment in your database."
msgid_plural "%s spam comments in your database."
msgstr[0] ""
msgstr[1] ""

#. translators: %s is the number of revisions found in the database. It's a formatted number, don't use %d.
#: inc/Engine/Admin/Settings/Page.php:1431
msgid "%s trashed comment in your database."
msgid_plural "%s trashed comments in your database."
msgstr[0] ""
msgstr[1] ""

#: inc/Engine/Admin/Settings/Page.php:1439
msgid "All transients"
msgstr ""

#. translators: %s is the number of revisions found in the database. It's a formatted number, don't use %d.
#: inc/Engine/Admin/Settings/Page.php:1441
msgid "%s transient in your database."
msgid_plural "%s transients in your database."
msgstr[0] ""
msgstr[1] ""

#: inc/Engine/Admin/Settings/Page.php:1449
msgid "Optimize Tables"
msgstr ""

#. translators: %s is the number of revisions found in the database. It's a formatted number, don't use %d.
#: inc/Engine/Admin/Settings/Page.php:1451
msgid "%s table to optimize in your database."
msgid_plural "%s tables to optimize in your database."
msgstr[0] ""
msgstr[1] ""

#: inc/Engine/Admin/Settings/Page.php:1462
msgid "Schedule Automatic Cleanup"
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:1474
msgid "Frequency"
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:1482
msgid "Daily"
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:1483
msgid "Weekly"
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:1484
msgid "Monthly"
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:1501
msgid "Integrate your CDN"
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:1513
msgid "All URLs of static files (CSS, JS, images) will be rewritten to the CNAME(s) you provide."
msgstr ""

#. translators: %1$s = opening link tag, %2$s = closing link tag.
#: inc/Engine/Admin/Settings/Page.php:1515
msgid "Not required for services like Cloudflare and Sucuri. Please see our available %1$sAdd-ons%2$s."
msgstr ""

#. translators: %1$s = opening em tag, %2$l = list of add-on name(s), %3$s = closing em tag.
#: inc/Engine/Admin/Settings/Page.php:1561
msgid "%1$s%2$l Add-on%3$s is currently enabled. Configuration of the CDN settings is not required for %2$l to work on your site."
msgid_plural "%1$s%2$l Add-ons%3$s are currently enabled. Configuration of the CDN settings is not required for %2$l to work on your site."
msgstr[0] ""
msgstr[1] ""

#: inc/Engine/Admin/Settings/Page.php:1587
msgid "Enable Content Delivery Network"
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:1596
#: inc/Engine/CDN/RocketCDN/AdminPageSubscriber.php:159
msgid "CDN CNAME(s)"
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:1597
#: inc/Engine/CDN/RocketCDN/AdminPageSubscriber.php:160
msgid "Specify the CNAME(s) below"
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:1604
msgid "Specify URL(s) of files that should not get served via CDN (one per line)."
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:1605
msgid "The domain part of the URL will be stripped automatically.<br>Use (.*) wildcards to exclude all files of a given file type located at a specific path."
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:1628
#: inc/Engine/Admin/Settings/Page.php:1636
msgid "Heartbeat"
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:1629
msgid "Control WordPress Heartbeat API"
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:1637
msgid "Reducing or disabling the Heartbeat API’s activity can help save some of your server’s resources."
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:1643
msgid "Reduce or disable Heartbeat activity"
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:1644
msgid "Reducing activity will change Heartbeat frequency from one hit each minute to one hit every 2 minutes."
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:1644
msgid "Disabling Heartbeat entirely may break plugins and themes using this API."
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:1658
msgid "Do not limit"
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:1659
msgid "Reduce activity"
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:1660
msgid "Disable"
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:1668
msgid "Control Heartbeat"
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:1677
msgid "Behavior in backend"
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:1684
msgid "Behavior in post editor"
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:1690
msgid "Behavior in frontend"
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:1709
#: views/settings/page-sections/tutorials.php:39
msgid "Add-ons"
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:1710
msgid "Add more features"
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:1717
msgid "One-click Rocket Add-ons"
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:1718
msgid "One-Click Add-ons are features extending available options without configuration needed. Switch the option \"on\" to enable from this screen."
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:1728
msgid "Rocket Add-ons"
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:1729
msgid "Rocket Add-ons are complementary features extending available options."
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:1740
msgid "User Cache"
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:1746
msgid "If you need to create a dedicated set of cache files for each logged-in WordPress user, you must activate this add-on."
msgstr ""

#. translators: %1$s = opening <a> tag, %2$s = closing </a> tag.
#: inc/Engine/Admin/Settings/Page.php:1748
msgid "User cache is great when you have user-specific or restricted content on your website.<br>%1$sLearn more%2$s"
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:1761
#: inc/Engine/Admin/Settings/Page.php:1930
msgid "Cloudflare"
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:1767
msgid "Integrate your Cloudflare account with this add-on."
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:1768
msgid "Provide your account email, global API key, and domain to use options such as clearing the Cloudflare cache and enabling optimal settings with WP Rocket."
msgstr ""

#. translators: %1$s = opening span tag, %2$s = closing span tag.
#: inc/Engine/Admin/Settings/Page.php:1771
msgid "%1$sPlanning on using Automatic Platform Optimization (APO)?%2$s Just activate the official Cloudflare plugin and configure it. WP Rocket will automatically enable compatibility."
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:1819
msgid "Varnish"
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:1825
msgid "If Varnish runs on your server, you must activate this add-on."
msgstr ""

#. translators: %1$s = opening <a> tag, %2$s = closing </a> tag.
#: inc/Engine/Admin/Settings/Page.php:1827
msgid "Varnish cache will be purged each time WP Rocket clears its cache to ensure content is always up-to-date.<br>%1$sLearn more%2$s"
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:1862
msgid "WebP Compatibility"
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:1868
msgid "Improve browser compatibility for WebP images."
msgstr ""

#. translators: %1$s and %3$s = opening <a> tag, %2$s = closing </a> tag.
#: inc/Engine/Admin/Settings/Page.php:1872
msgid "Enable this option if you would like WP Rocket to serve WebP images to compatible browsers. Please note that WP Rocket cannot create WebP images for you. To create WebP images we recommend %1$sImagify%2$s. %3$sMore info%2$s"
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:1892
msgid "Clear the Sucuri cache when WP Rocket’s cache is cleared."
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:1895
msgid "Provide your API key to clear the Sucuri cache when WP Rocket’s cache is cleared."
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:1903
#: inc/Engine/Admin/Settings/Page.php:2047
msgid "Sucuri"
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:1909
msgid "Synchronize Sucuri cache with this add-on."
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:1947
msgid "Cloudflare credentials"
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:1956
msgid "Cloudflare settings"
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:1970
msgctxt "Cloudflare"
msgid "Global API key:"
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:1971
msgctxt "Cloudflare"
msgid "Find your API key"
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:1983
msgctxt "Cloudflare"
msgid "Account email"
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:1992
msgctxt "Cloudflare"
msgid "Zone ID"
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:2002
msgid "Development mode"
msgstr ""

#. translators: %1$s = link opening tag, %2$s = link closing tag.
#: inc/Engine/Admin/Settings/Page.php:2004
msgid "Temporarily activate development mode on your website. This setting will automatically turn off after 3 hours. %1$sLearn more%2$s"
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:2012
msgid "Optimal settings"
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:2013
msgid "Automatically enhances your Cloudflare configuration for speed, performance grade and compatibility."
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:2021
msgid "Relative protocol"
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:2022
msgid "Should only be used with Cloudflare's flexible SSL feature. URLs of static files (CSS, JS, images) will be rewritten to use // instead of http:// or https://."
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:2060
msgid "Sucuri credentials"
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:2073
msgctxt "Sucuri"
msgid "Firewall API key (for plugin), must be in format {32 characters}/{32 characters}:"
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:2074
msgctxt "Sucuri"
msgid "Find your API key"
msgstr ""

#. translators: %1$s: opening strong tag, %2$s: closing strong tag, %3$s: opening a tag, %4$s: option a tag, %5$s: opening a tag.
#: inc/Engine/Admin/Settings/Page.php:2295
msgid "%1$sWP Rocket:%2$s the plugin has been updated to the 3.16 version. Our brand new feature %3$sOptimize critical images%5$s is automatically activated now! Also, the Cache tab was removed but the existing features will remain working, %4$ssee more here%5$s."
msgstr ""

#: inc/Engine/Admin/Settings/Settings.php:361
msgid "Sucuri Add-on: The API key for the Sucuri firewall must be in format <code>{32 characters}/{32 characters}</code>."
msgstr ""

#: inc/Engine/Admin/Settings/Settings.php:667
msgid "Sorry! Adding /(.*) in Advanced Rules > Never Cache URL(s) was not saved because it disables caching and optimizations for every page on your site."
msgstr ""

#: inc/Engine/Admin/Settings/Subscriber.php:172
msgid "Import, Export, Rollback"
msgstr ""

#: inc/Engine/Admin/Settings/Subscriber.php:197
#: views/settings/page-sections/imagify.php:14
msgid "Image Optimization"
msgstr ""

#: inc/Engine/Admin/Settings/Subscriber.php:198
msgid "Compress your images"
msgstr ""

#: inc/Engine/Admin/Settings/Subscriber.php:215
#: views/settings/page-sections/tutorials.php:48
msgid "Tutorials"
msgstr ""

#: inc/Engine/Admin/Settings/Subscriber.php:216
msgid "Getting started and how to videos"
msgstr ""

#: inc/Engine/Cache/PurgeExpired/Subscriber.php:75
msgid "WP Rocket Expired Cache Interval"
msgstr ""

#: inc/Engine/Cache/WPCache.php:337
msgid "WP_CACHE value"
msgstr ""

#: inc/Engine/Cache/WPCache.php:354
msgid "Cache"
msgstr ""

#: inc/Engine/Cache/WPCache.php:358
msgid "The WP_CACHE constant needs to be set to true for WP Rocket cache to work properly"
msgstr ""

#: inc/Engine/Cache/WPCache.php:367
msgid "WP_CACHE is set to true"
msgstr ""

#: inc/Engine/Cache/WPCache.php:375
msgid "WP_CACHE is not set"
msgstr ""

#: inc/Engine/Cache/WPCache.php:383
msgid "WP_CACHE is set to false"
msgstr ""

#: inc/Engine/CDN/RocketCDN/AdminPageSubscriber.php:90
msgid "Next Billing Date"
msgstr ""

#: inc/Engine/CDN/RocketCDN/AdminPageSubscriber.php:99
msgid "No Subscription"
msgstr ""

#: inc/Engine/CDN/RocketCDN/AdminPageSubscriber.php:135
msgid "Your RocketCDN subscription is currently active."
msgstr ""

#. translators: %1$s = opening <code> tag, %2$s = CDN URL, %3$s = closing </code> tag.
#: inc/Engine/CDN/RocketCDN/AdminPageSubscriber.php:141
msgid "To use RocketCDN, replace your CNAME with %1$s%2$s%3$s."
msgstr ""

#. translators: %1$is = opening link tag, %2$s = closing link tag.
#: inc/Engine/CDN/RocketCDN/AdminPageSubscriber.php:152
#: inc/Engine/CDN/RocketCDN/NoticesSubscriber.php:230
#: inc/Engine/CDN/RocketCDN/NoticesSubscriber.php:334
msgid "%1$sMore Info%2$s"
msgstr ""

#: inc/Engine/CDN/RocketCDN/APIClient.php:134
msgid "We could not fetch the current price because RocketCDN API returned an unexpected error code."
msgstr ""

#: inc/Engine/CDN/RocketCDN/APIClient.php:140
msgid "RocketCDN is not available at the moment. Please retry later."
msgstr ""

#: inc/Engine/CDN/RocketCDN/APIClient.php:177
msgid "RocketCDN cache purge failed: Missing identifier parameter."
msgstr ""

#: inc/Engine/CDN/RocketCDN/APIClient.php:186
msgid "RocketCDN cache purge failed: Missing user token."
msgstr ""

#: inc/Engine/CDN/RocketCDN/APIClient.php:212
msgid "RocketCDN cache purge failed: The API returned an unexpected response code."
msgstr ""

#: inc/Engine/CDN/RocketCDN/APIClient.php:221
msgid "RocketCDN cache purge failed: The API returned an empty response."
msgstr ""

#: inc/Engine/CDN/RocketCDN/APIClient.php:230
msgid "RocketCDN cache purge failed: The API returned an unexpected response."
msgstr ""

#. translators: %s = message returned by the API.
#: inc/Engine/CDN/RocketCDN/APIClient.php:239
msgid "RocketCDN cache purge failed: %s."
msgstr ""

#: inc/Engine/CDN/RocketCDN/APIClient.php:247
msgid "RocketCDN cache purge successful."
msgstr ""

#: inc/Engine/CDN/RocketCDN/RESTSubscriber.php:137
#: tests/Integration/inc/Engine/CDN/RocketCDN/RESTSubscriber/enable.php:72
#: tests/Integration/inc/Engine/CDN/RocketCDN/RESTSubscriber/registerEnableRoute.php:302
msgid "RocketCDN enabled"
msgstr ""

#: inc/Engine/CDN/RocketCDN/RESTSubscriber.php:160
#: tests/Integration/inc/Engine/CDN/RocketCDN/RESTSubscriber/disable.php:31
#: tests/Integration/inc/Engine/CDN/RocketCDN/RESTSubscriber/registerDisableRoute.php:147
msgid "RocketCDN disabled"
msgstr ""

#. Translators: %s = date formatted using date_i18n() and get_option( 'date_format' ).
#: inc/Engine/CDN/RocketCDN/views/cta-big.php:27
msgid "Valid until %s only!"
msgstr ""

#: inc/Engine/CDN/RocketCDN/views/cta-big.php:36
msgid "Speed up your website thanks to:"
msgstr ""

#. translators: %1$s = opening strong tag, %2$s = closing strong tag.
#: inc/Engine/CDN/RocketCDN/views/cta-big.php:42
msgid "High performance Content Delivery Network (CDN) with %1$sunlimited bandwidth%2$s"
msgstr ""

#. translators: %1$s = opening strong tag, %2$s = closing strong tag.
#: inc/Engine/CDN/RocketCDN/views/cta-big.php:48
msgid "Easy configuration: the %1$sbest CDN settings%2$s are automatically applied"
msgstr ""

#. translators: %1$s = opening strong tag, %2$s = closing strong tag.
#: inc/Engine/CDN/RocketCDN/views/cta-big.php:54
msgid "WP Rocket integration: the CDN option is %1$sautomatically configured%2$s in our plugin"
msgstr ""

#: inc/Engine/CDN/RocketCDN/views/cta-big.php:58
msgid "Learn more about RocketCDN"
msgstr ""

#. translators: %1$s = discounted price, %2$s = regular price.
#: inc/Engine/CDN/RocketCDN/views/cta-big.php:65
msgid "*$%1$s/month for 12 months then $%2$s/month. You can cancel your subscription at any time."
msgstr ""

#: inc/Engine/CDN/RocketCDN/views/cta-big.php:86
msgid "Billed monthly"
msgstr ""

#: inc/Engine/CDN/RocketCDN/views/cta-big.php:87
msgid "Get Started"
msgstr ""

#: inc/Engine/CDN/RocketCDN/views/cta-big.php:92
msgid "Reduce this banner"
msgstr ""

#: inc/Engine/CDN/RocketCDN/views/cta-small.php:17
msgid "Speed up your website with RocketCDN, WP Rocket’s Content Delivery Network."
msgstr ""

#: inc/Engine/CDN/RocketCDN/views/cta-small.php:20
#: inc/Engine/CDN/RocketCDN/views/promote-notice.php:13
msgid "Learn More"
msgstr ""

#: inc/Engine/CDN/RocketCDN/views/dashboard-status.php:23
msgid "RocketCDN is unavailable on local domains and staging sites."
msgstr ""

#: inc/Engine/CDN/RocketCDN/views/dashboard-status.php:32
msgid "Get RocketCDN"
msgstr ""

#: inc/Engine/CDN/RocketCDN/views/promote-notice.php:11
msgid "New!"
msgstr ""

#: inc/Engine/CDN/RocketCDN/views/promote-notice.php:12
msgid "Speed up your website with RocketCDN, WP Rocket’s Content Delivery Network!"
msgstr ""

#: inc/Engine/Common/JobManager/Cron/Subscriber.php:185
msgid "WP Rocket process pending jobs"
msgstr ""

#: inc/Engine/Common/JobManager/Cron/Subscriber.php:204
msgid "WP Rocket clear failed jobs"
msgstr ""

#: inc/Engine/Common/JobManager/Cron/Subscriber.php:221
msgid "WP Rocket process on submit jobs"
msgstr ""

#: inc/Engine/Common/Queue/RUCSSQueueRunner.php:265
msgid "Every minute"
msgstr ""

#: inc/Engine/CriticalPath/Admin/Admin.php:264
msgid "Regenerate Critical Path CSS"
msgstr ""

#: inc/Engine/CriticalPath/Admin/Post.php:144
#: views/cpcss/metabox/generate.php:47
msgid "Generate Specific CPCSS"
msgstr ""

#: inc/Engine/CriticalPath/Admin/Post.php:145
#: views/cpcss/metabox/generate.php:45
msgid "Regenerate specific CPCSS"
msgstr ""

#: inc/Engine/CriticalPath/Admin/Post.php:216
msgid "This feature is not available for non-public post types."
msgstr ""

#: inc/Engine/CriticalPath/Admin/Post.php:219
msgid "%l to use this feature."
msgstr ""

#. translators: %s = post type.
#: inc/Engine/CriticalPath/Admin/Post.php:222
msgid "Publish the %s"
msgstr ""

#. translators: %s = post type.
#: inc/Engine/CriticalPath/Admin/Post.php:223
msgid "Enable Load CSS asynchronously in WP Rocket settings"
msgstr ""

#: inc/Engine/CriticalPath/Admin/Post.php:224
msgid "Enable Load CSS asynchronously in the options above"
msgstr ""

#. translators: %1$s = type of content, %2$s = error message.
#: inc/Engine/CriticalPath/APIClient.php:64
msgid "Critical CSS for %1$s not generated. Error: %2$s"
msgstr ""

#. translators: %s = item URL.
#: inc/Engine/CriticalPath/APIClient.php:170
msgid "Critical CSS for %1$s on mobile not generated. Error: The API returned an empty response."
msgstr ""

#. translators: %s = item URL.
#: inc/Engine/CriticalPath/APIClient.php:173
msgid "Critical CSS for %1$s not generated. Error: The API returned an empty response."
msgstr ""

#. translators: %s = item URL.
#: inc/Engine/CriticalPath/APIClient.php:185
msgid "Critical CSS for %1$s on mobile not generated."
msgstr ""

#. translators: %s = item URL.
#. translators: %1$s = item URL or item type.
#: inc/Engine/CriticalPath/APIClient.php:187
#: inc/Engine/CriticalPath/ProcessorService.php:194
msgid "Critical CSS for %1$s not generated."
msgstr ""

#. translators: %s = URL.
#: inc/Engine/CriticalPath/APIClient.php:195
msgid "Critical CSS for %1$s on mobile not generated. Error: The API returned an invalid response code."
msgstr ""

#. translators: %s = URL.
#: inc/Engine/CriticalPath/APIClient.php:197
msgid "Critical CSS for %1$s not generated. Error: The API returned an invalid response code."
msgstr ""

#. translators: %1$s = error message.
#: inc/Engine/CriticalPath/APIClient.php:205
#: inc/Engine/CriticalPath/ProcessorService.php:201
msgid "Error: %1$s"
msgstr ""

#: inc/Engine/CriticalPath/CriticalCSSSubscriber.php:177
msgid "Critical CSS generation is currently running."
msgstr ""

#. Translators: %1$s = opening link tag, %2$s = closing link tag.
#: inc/Engine/CriticalPath/CriticalCSSSubscriber.php:182
msgid "Go to the %1$sWP Rocket settings%2$s page to track progress."
msgstr ""

#. Translators: %1$d = number of critical CSS generated, %2$d = total number of critical CSS to generate.
#: inc/Engine/CriticalPath/CriticalCSSSubscriber.php:397
msgid "Critical CSS generation is currently running: %1$d of %2$d page types completed. (Refresh this page to view progress)"
msgstr ""

#. Translators: %1$d = number of critical CSS generated, %2$d = total number of critical CSS to generate.
#: inc/Engine/CriticalPath/CriticalCSSSubscriber.php:473
msgid "Critical CSS generation finished for %1$d of %2$d page types."
msgstr ""

#: inc/Engine/CriticalPath/CriticalCSSSubscriber.php:480
msgid "Critical CSS generation encountered one or more errors."
msgstr ""

#: inc/Engine/CriticalPath/CriticalCSSSubscriber.php:480
msgid "Learn more."
msgstr ""

#. translators: %1$ = opening bold tag, %2$ = closing bold tag.
#: inc/Engine/CriticalPath/CriticalCSSSubscriber.php:841
msgid "We highly recommend the %1$supdated Remove Unused CSS%2$s for a better CSS optimization. Load CSS Asynchronously is always available as a back-up."
msgstr ""

#: inc/Engine/CriticalPath/CriticalCSSSubscriber.php:846
msgid "Stay with the old option"
msgstr ""

#. translators: %s = item URL.
#: inc/Engine/CriticalPath/DataManager.php:68
msgid "Critical CSS for %1$s on mobile not generated. Error: The destination folder could not be created."
msgstr ""

#. translators: %s = item URL.
#: inc/Engine/CriticalPath/DataManager.php:71
msgid "Critical CSS for %1$s not generated. Error: The destination folder could not be created."
msgstr ""

#: inc/Engine/CriticalPath/DataManager.php:106
msgid "Critical CSS file for mobile does not exist"
msgstr ""

#: inc/Engine/CriticalPath/DataManager.php:108
msgid "Critical CSS file does not exist"
msgstr ""

#: inc/Engine/CriticalPath/DataManager.php:120
msgid "Critical CSS file for mobile cannot be deleted"
msgstr ""

#: inc/Engine/CriticalPath/DataManager.php:122
msgid "Critical CSS file cannot be deleted"
msgstr ""

#. translators: %1$s = item URL or item type.
#: inc/Engine/CriticalPath/ProcessorService.php:187
msgid "Mobile Critical CSS for %1$s not generated."
msgstr ""

#. translators: %1$s = Item URL or item type.
#: inc/Engine/CriticalPath/ProcessorService.php:228
msgid "Critical CSS for %s in progress."
msgstr ""

#. translators: %1$s = Item URL or item type.
#: inc/Engine/CriticalPath/ProcessorService.php:262
msgid "Mobile Critical CSS for %s generated."
msgstr ""

#. translators: %1$s = Item URL or item type.
#: inc/Engine/CriticalPath/ProcessorService.php:273
msgid "Critical CSS for %s generated."
msgstr ""

#: inc/Engine/CriticalPath/ProcessorService.php:295
msgid "Critical CSS file deleted successfully."
msgstr ""

#. translators: %1$s = Item URL or item type.
#: inc/Engine/CriticalPath/ProcessorService.php:317
msgid "Mobile Critical CSS for %1$s timeout. Please retry a little later."
msgstr ""

#. translators: %1$s = Item URL or item type.
#: inc/Engine/CriticalPath/ProcessorService.php:330
msgid "Critical CSS for %1$s timeout. Please retry a little later."
msgstr ""

#: inc/Engine/CriticalPath/RESTWP.php:141
msgid "Mobile CPCSS generation not enabled."
msgstr ""

#: inc/Engine/CriticalPath/RESTWPPost.php:36
#: inc/Engine/CriticalPath/RESTWPPost.php:69
msgid "Requested post does not exist."
msgstr ""

#: inc/Engine/CriticalPath/RESTWPPost.php:46
msgid "Cannot generate CPCSS for unpublished post."
msgstr ""

#: inc/Engine/HealthCheck/HealthCheck.php:142
msgid "Scheduled Cache Purge"
msgstr ""

#: inc/Engine/HealthCheck/HealthCheck.php:143
msgid "Scheduled Database Optimization"
msgstr ""

#: inc/Engine/HealthCheck/HealthCheck.php:144
msgid "Database Optimization Process"
msgstr ""

#: inc/Engine/HealthCheck/HealthCheck.php:145
msgctxt "noun"
msgid "Preload"
msgstr ""

#: inc/Engine/HealthCheck/HealthCheck.php:146
msgid "Critical Path CSS Generation Process"
msgstr ""

#. translators: %1$s = <strong>, %2$s = price, %3$s = </strong>.
#: inc/Engine/License/Renewal.php:76
msgid "Renew before it is too late, you will only pay %1$s%2$s%3$s!"
msgstr ""

#. translators: %1$s = <strong>, %2$s = discount, %3$s = </strong>,%4$s = <strong>, %5$s = price, %6$s=</strong>.
#: inc/Engine/License/Renewal.php:85
msgid "Renew with a %1$s%2$s discount%3$s before it is too late, you will only pay %4$s%5$s%6$s!"
msgstr ""

#. translators: %1$s = <strong>, %2$s = </strong>, %3$s = price.
#: inc/Engine/License/Renewal.php:139
msgid "Renew your license for 1 year now at %1$s%3$s%2$s."
msgstr ""

#. translators: %1$s = <strong>, %2$s = </strong>, %3$s = discount percentage, %4$s = price.
#: inc/Engine/License/Renewal.php:152
msgid "Renew your license for 1 year now and get %1$s%3$s OFF%2$s immediately: you will only pay %1$s%4$s%2$s!"
msgstr ""

#. translators: %1$s = <strong>, %2$s = </strong>, %3$s = discount price.
#: inc/Engine/License/Renewal.php:218
msgid "Renew before it is too late, you will pay %1$s%3$s%2$s."
msgstr ""

#. translators: %1$s = <strong>, %2$s = discount percentage, %3$s = </strong>, %4$s = discount price.
#: inc/Engine/License/Renewal.php:227
msgid "Renew with a %1$s%2$s discount%3$s before it is too late, you will only pay %1$s%4$s%3$s!"
msgstr ""

#. translators: %1$s = <a>, %2$s = </a>.
#: inc/Engine/License/Renewal.php:542
msgid "You need a valid license to continue using this feature. %1$sRenew now%2$s before losing access."
msgstr ""

#. translators: %1$s = <a>, %2$s = </a>.
#: inc/Engine/License/Renewal.php:563
msgid "You need an active license to enable this option. %1$sRenew now%2$s."
msgstr ""

#. translators: %1$s = <a>, %2$s = </a>.
#: inc/Engine/License/Renewal.php:591
msgid "You need an active license to enable this option. %1$sMore info%2$s."
msgstr ""

#. translators: %1$s = promotion name, %2$s = <br>, %3$s = <strong>, %4$s = promotion discount percentage, %5$s = </strong>.
#: inc/Engine/License/Upgrade.php:251
msgid "Take advantage of %1$s to speed up more websites:%2$s get a %3$s%4$s off%5$s for %3$supgrading your license to Plus or Infinite!%5$s"
msgid_plural "Take advantage of %1$s to speed up more websites:%2$s get a %3$s%4$s off%5$s for %3$supgrading your license to Infinite!%5$s"
msgstr[0] ""
msgstr[1] ""

#: inc/Engine/License/Upgrade.php:382
#: inc/Engine/License/Upgrade.php:405
msgid "Unlimited"
msgstr ""

#. translators: %s = promotion discount percentage.
#: inc/Engine/License/views/promo-banner.php:16
msgid "%s off"
msgstr ""

#. translators: %s = promotion name.
#: inc/Engine/License/views/promo-banner.php:21
msgid "%s promotion is live!"
msgstr ""

#: inc/Engine/License/views/promo-banner.php:27
msgid "Hurry Up! Deal ends in:"
msgstr ""

#: inc/Engine/License/views/promo-banner.php:31
#: inc/Engine/License/views/renewal-soon-banner.php:14
msgid "Minutes"
msgstr ""

#: inc/Engine/License/views/promo-banner.php:32
#: inc/Engine/License/views/renewal-soon-banner.php:15
msgid "Seconds"
msgstr ""

#: inc/Engine/License/views/promo-banner.php:34
#: inc/Engine/License/views/upgrade-section.php:11
msgid "Upgrade now"
msgstr ""

#: inc/Engine/License/views/renewal-expired-banner-ocd-disabled.php:11
msgid "The Optimize CSS Delivery feature is disabled."
msgstr ""

#: inc/Engine/License/views/renewal-expired-banner-ocd-disabled.php:15
msgid "You can no longer use the Remove Unused CSS or Load CSS asynchronously options."
msgstr ""

#. translators: %1$s = <strong>, %2$s = </strong>.
#: inc/Engine/License/views/renewal-expired-banner-ocd-disabled.php:20
msgid "You need an %1$sactive license%2$s to keep optimizing your CSS delivery, which addresses a PageSpeed Insights recommendation and improves your page performance."
msgstr ""

#: inc/Engine/License/views/renewal-expired-banner-ocd-disabled.php:29
#: inc/Engine/License/views/renewal-expired-banner-ocd.php:39
#: inc/Engine/License/views/renewal-expired-banner.php:27
#: inc/Engine/License/views/renewal-soon-banner.php:31
msgid "Renew now"
msgstr ""

#: inc/Engine/License/views/renewal-expired-banner-ocd.php:11
msgid "You will soon lose access to some features."
msgstr ""

#. translators: %1$s = <strong>, %2$s = </strong>.
#: inc/Engine/License/views/renewal-expired-banner-ocd.php:18
msgid "You need an %1$sactive license to continue optimizing your CSS delivery%2$s."
msgstr ""

#: inc/Engine/License/views/renewal-expired-banner-ocd.php:24
msgid "The Remove Unused CSS and Load CSS asynchronously features are great options to address the PageSpeed Insights recommendations and improve your website performance."
msgstr ""

#. translators: %1$s = <strong>, %2$s = </strong>, %3$s = date.
#: inc/Engine/License/views/renewal-expired-banner-ocd.php:29
msgid "These features will be %1$sautomatically disabled on %3$s%2$s."
msgstr ""

#: inc/Engine/License/views/renewal-expired-banner.php:11
msgid "Your WP Rocket license is expired!"
msgstr ""

#. translators: %1$s = <strong>, %2$s = </strong>.
#: inc/Engine/License/views/renewal-expired-banner.php:18
msgid "Your website could be much faster if it could take advantage of our %1$snew features and enhancements%2$s. 🚀"
msgstr ""

#. translators: %1$s = <strong>, %2$s = </strong>.
#: inc/Engine/License/views/renewal-soon-banner.php:22
msgid "Your %1$sWP Rocket license is about to expire%2$s: you will soon lose access to product updates and support."
msgstr ""

#: inc/Engine/License/views/upgrade-popin.php:12
msgid "Speed Up More Websites"
msgstr ""

#. translators: %1$s = opening strong tag, %2$s = closing strong tag.
#: inc/Engine/License/views/upgrade-popin.php:19
msgid "You can use WP Rocket on more websites by upgrading your license. To upgrade, simply pay the %1$sprice difference%2$s between your current and new licenses, as shown below."
msgstr ""

#. translators: %1$s = opening strong tag, %2$s = closing strong tag.
#: inc/Engine/License/views/upgrade-popin.php:25
msgid "%1$sN.B.%2$s: Upgrading your license does not change your expiration date"
msgstr ""

#. translators: %s = price.
#: inc/Engine/License/views/upgrade-popin.php:35
msgid "Save $%s"
msgstr ""

#. translators: %s = number of websites.
#: inc/Engine/License/views/upgrade-popin.php:48
msgid "%s websites"
msgstr ""

#. translators: %s = license name.
#: inc/Engine/License/views/upgrade-popin.php:54
msgid "Upgrade to %s"
msgstr ""

#: inc/Engine/License/views/upgrade-section.php:11
msgid "You can use WP Rocket on more websites by upgrading your license (you will only pay the price difference between your current and new licenses)."
msgstr ""

#. translators: %1$s = plugin name.
#: inc/Engine/Media/AboveTheFold/Admin/Controller.php:143
msgid "%1$s: Critical images cleared!"
msgstr ""

#: inc/Engine/Media/AboveTheFold/AJAX/Controller.php:131
msgid "Script error"
msgstr ""

#: inc/Engine/Media/AboveTheFold/AJAX/Controller.php:134
msgid "Script timeout"
msgstr ""

#: inc/Engine/Media/Lazyload/AdminSubscriber.php:62
msgid "LazyLoad for images"
msgstr ""

#: inc/Engine/Media/Lazyload/AdminSubscriber.php:63
msgid "LazyLoad for iframes/videos"
msgstr ""

#: inc/Engine/Media/Lazyload/CSS/Admin/Subscriber.php:48
msgid "LazyLoad CSS backgrounds"
msgstr ""

#: inc/Engine/Optimization/DelayJS/Admin/SiteList.php:195
msgid "Analytics & Ads"
msgstr ""

#: inc/Engine/Optimization/DelayJS/Admin/SiteList.php:200
msgid "Plugins"
msgstr ""

#: inc/Engine/Optimization/DelayJS/Admin/SiteList.php:205
msgid "Themes"
msgstr ""

#: inc/Engine/Optimization/DynamicLists/DynamicLists.php:101
msgid "You need an active license to get the latest version of the lists from our server."
msgstr ""

#: inc/Engine/Optimization/DynamicLists/DynamicLists.php:116
msgid "Could not get updated lists from server."
msgstr ""

#: inc/Engine/Optimization/DynamicLists/DynamicLists.php:125
msgid "Lists are up to date."
msgstr ""

#: inc/Engine/Optimization/DynamicLists/DynamicLists.php:134
msgid "Could not update lists."
msgstr ""

#: inc/Engine/Optimization/DynamicLists/DynamicLists.php:143
msgid "Lists are successfully updated."
msgstr ""

#: inc/Engine/Optimization/DynamicLists/ServiceProvider.php:72
msgid "Default Lists"
msgstr ""

#: inc/Engine/Optimization/DynamicLists/ServiceProvider.php:78
msgid "Delay JavaScript Execution Exclusion Lists"
msgstr ""

#: inc/Engine/Optimization/DynamicLists/ServiceProvider.php:84
msgid "Incompatible plugins Lists"
msgstr ""

#: inc/Engine/Optimization/Minify/AdminSubscriber.php:65
msgid "Minify/combine JavaScript"
msgstr ""

#: inc/Engine/Optimization/Minify/CSS/AdminSubscriber.php:150
msgid "Minify CSS"
msgstr ""

#. translators: %1$s = plugin name, %2$s = table name, %3$s = <a> open tag, %4$s = </a> closing tag.
#: inc/Engine/Optimization/RUCSS/Admin/Settings.php:253
msgid "%1$s: Could not create the %2$s table in the database which is necessary for the Remove Unused CSS feature to work. Please reach out to %3$sour support%4$s."
msgstr ""

#. translators: %1$s = plugin name.
#: inc/Engine/Optimization/RUCSS/Admin/Subscriber.php:274
msgid "%1$s: Used CSS cache cleared!"
msgstr ""

#. translators: %1$s = plugin name.
#: inc/Engine/Preload/Admin/Settings.php:57
msgid "%1$s: The preload service is now active. After the initial preload it will continue to cache all your pages whenever they are purged. No further action is needed."
msgstr ""

#: inc/Engine/Preload/Cron/Subscriber.php:138
msgid "WP Rocket Preload pending jobs"
msgstr ""

#: inc/Engine/Preload/Cron/Subscriber.php:166
msgid "WP Rocket Preload revert stuck failed jobs"
msgstr ""

#: inc/Engine/Saas/Admin/AdminBar.php:77
#: inc/Engine/Saas/Admin/AdminBar.php:198
msgid "Clear Critical Images"
msgstr ""

#: inc/Engine/Saas/Admin/AdminBar.php:164
msgid "Clear Critical Images of this URL"
msgstr ""

#: inc/Engine/Saas/Admin/AdminBar.php:167
msgid "Clear Used CSS of this URL"
msgstr ""

#: inc/Engine/Saas/Admin/AdminBar.php:197
msgid "Critical Images Cache"
msgstr ""

#: inc/Engine/Saas/Admin/AdminBar.php:201
msgid "Remove Used CSS Cache"
msgstr ""

#. translators: %1$s = plugin name, %2$s = number of seconds.
#: inc/Engine/Saas/Admin/Notices.php:104
msgid "%1$s: Please wait %2$s seconds. The Remove Unused CSS service is processing your pages, the plugin is optimizing LCP and the images above the fold."
msgstr ""

#. translators: %1$s = plugin name, %2$s = number of URLs, %3$s = number of seconds.
#: inc/Engine/Saas/Admin/Notices.php:147
msgid ""
"%1$s: The LCP element has been optimized, and the images above the fold were excluded from lazyload. The Used CSS of your homepage has been processed.\n"
"\t\t\t WP Rocket will continue to generate Used CSS for up to %2$s URLs per %3$s second(s)."
msgstr ""

#. translators: %1$s = opening link tag, %2$s = closing link tag.
#: inc/Engine/Saas/Admin/Notices.php:170
msgid "We suggest enabling %1$sPreload%2$s for the fastest results."
msgstr ""

#. translators: %1$s = opening link tag, %2$s = closing link tag.
#: inc/Engine/Saas/Admin/Notices.php:180
msgid "To learn more about the process check our %1$sdocumentation%2$s."
msgstr ""

#: inc/Engine/Saas/Admin/Notices.php:246
msgid "We couldn't generate the used CSS because you're using a nulled version of WP Rocket. You need an active license to use the Remove Unused CSS feature and further improve your website's performance."
msgstr ""

#. translators: %1$s = promo percentage.
#: inc/Engine/Saas/Admin/Notices.php:249
msgid "Click here to get a WP Rocket single license at %1$s off!"
msgstr ""

#. translators: %1$s = <a> open tag, %2$s = </a> closing tag.
#: inc/Engine/Saas/Admin/Notices.php:302
msgid "It seems a security plugin or the server's firewall prevents WP Rocket from accessing the SaaS features. IPs listed %1$shere in our documentation%2$s should be added to your allowlists:"
msgstr ""

#: inc/Engine/Saas/Admin/Notices.php:307
msgid "- In the security plugin, if you are using one"
msgstr ""

#: inc/Engine/Saas/Admin/Notices.php:308
msgid "- In the server's firewall. Your host can help you with this"
msgstr ""

#: inc/functions/admin.php:21
msgid "There seems to be an issue validating your license. Please see the error message below."
msgid_plural "There seems to be an issue validating your license. You can see the error messages below."
msgstr[0] ""
msgstr[1] ""

#: inc/functions/admin.php:361
msgid "Server type:"
msgstr ""

#: inc/functions/admin.php:370
msgid "PHP version number:"
msgstr ""

#: inc/functions/admin.php:379
msgid "WordPress version number:"
msgstr ""

#: inc/functions/admin.php:388
msgid "WordPress multisite:"
msgstr ""

#: inc/functions/admin.php:397
msgid "Current theme:"
msgstr ""

#: inc/functions/admin.php:406
msgid "Current site language:"
msgstr ""

#: inc/functions/admin.php:415
msgid "Active plugins:"
msgstr ""

#: inc/functions/admin.php:418
msgid "Plugin names of all active plugins"
msgstr ""

#: inc/functions/admin.php:424
msgid "Anonymized WP Rocket settings:"
msgstr ""

#: inc/functions/admin.php:427
msgid "Which WP Rocket settings are active"
msgstr ""

#: inc/functions/admin.php:433
msgid "WP Rocket license type"
msgstr ""

#: inc/functions/options.php:429
msgid "The provided license data are not valid."
msgstr ""

#. Translators: %1$s = opening link tag, %2$s = closing link tag.
#: inc/functions/options.php:432
msgid "To resolve, please %1$scontact support%2$s."
msgstr ""

#. Translators: %1$s = opening em tag, %2$s = closing em tag, %3$s = opening link tag, %4$s closing link tag.
#: inc/functions/options.php:491
#: inc/functions/options.php:530
msgid "License validation failed. Our server could not resolve the request from your website."
msgstr ""

#. Translators: %1$s = opening em tag, %2$s = closing em tag, %3$s = opening link tag, %4$s closing link tag.
#: inc/functions/options.php:491
msgid "Try clicking %1$sValidate License%2$s below. If the error persists, follow %3$sthis guide%4$s."
msgstr ""

#. Translators: %1$s = opening link tag, %2$s = closing link tag.
#: inc/functions/options.php:507
msgid "License validation failed. You may be using a nulled version of the plugin. Please do the following:"
msgstr ""

#. Translators: %1$s = opening link tag, %2$s = closing link tag.
#: inc/functions/options.php:507
#: inc/functions/options.php:549
msgid "Login to your WP Rocket %1$saccount%2$s"
msgstr ""

#. Translators: %1$s = opening link tag, %2$s = closing link tag.
#: inc/functions/options.php:507
#: inc/functions/options.php:549
msgid "Download the zip file"
msgstr ""

#. Translators: %1$s = opening link tag, %2$s = closing link tag.
#: inc/functions/options.php:507
#: inc/functions/options.php:549
msgid "Reinstall"
msgstr ""

#. Translators: %1$s = opening link tag, %2$s = closing link tag.
#: inc/functions/options.php:507
msgid "If you do not have a WP Rocket account, please %1$spurchase a license%2$s."
msgstr ""

#. Translators: %1$s = opening link tag, %2$s = closing link tag.
#: inc/functions/options.php:515
msgid "License validation failed. This user account does not exist in our database."
msgstr ""

#. Translators: %1$s = opening link tag, %2$s = closing link tag.
#: inc/functions/options.php:515
msgid "To resolve, please contact support."
msgstr ""

#. Translators: %1$s = opening link tag, %2$s = closing link tag.
#: inc/functions/options.php:523
msgid "License validation failed. This user account is blocked."
msgstr ""

#. Translators: %1$s = opening link tag, %2$s = closing link tag.
#: inc/functions/options.php:523
msgid "Please see %1$sthis guide%2$s for more info."
msgstr ""

#. Translators: %1$s = opening em tag, %2$s = closing em tag, %3$s = opening link tag, %4$s closing link tag.
#: inc/functions/options.php:530
msgid "Try clicking %1$sSave Changes%2$s below. If the error persists, follow %3$sthis guide%4$s."
msgstr ""

#. Translators: %1$s = opening link tag, %2$s = closing link tag.
#: inc/functions/options.php:543
msgid "Your license is not valid."
msgstr ""

#. Translators: %1$s = opening link tag, %2$s = closing link tag.
#: inc/functions/options.php:543
msgid "Make sure you have an active %1$sWP Rocket license%2$s."
msgstr ""

#. Translators: %1$s = opening link tag, %2$s = closing link tag, %3$s = opening link tag.
#: inc/functions/options.php:545
msgid "You have added as many sites as your current license allows."
msgstr ""

#. Translators: %1$s = opening link tag, %2$s = closing link tag, %3$s = opening link tag.
#: inc/functions/options.php:545
msgid "Upgrade your %1$saccount%2$s or %3$stransfer your license%2$s to this domain."
msgstr ""

#. Translators: %1$s = opening link tag, %2$s = closing link tag.
#: inc/functions/options.php:547
msgid "This website is not allowed."
msgstr ""

#. Translators: %1$s = opening link tag, %2$s = closing link tag.
#: inc/functions/options.php:547
msgid "Please %1$scontact support%2$s."
msgstr ""

#. Translators: %1$s = opening link tag, %2$s = closing link tag.
#: inc/functions/options.php:549
msgid "This license key is not recognized."
msgstr ""

#. Translators: %1$s = opening link tag, %2$s = closing link tag.
#: inc/functions/options.php:549
msgid "If the issue persists, please %1$scontact support%2$s."
msgstr ""

#. Translators: %s = error message returned.
#: inc/functions/options.php:555
msgid "License validation failed: %s"
msgstr ""

#: inc/Logger/Logger.php:227
#: inc/Logger/Logger.php:257
msgid "The log file does not exist."
msgstr ""

#: inc/Logger/Logger.php:233
#: inc/Logger/Logger.php:263
msgid "The log file could not be read."
msgstr ""

#: inc/Logger/Logger.php:250
msgid "The logs are not saved into a file."
msgstr ""

#. Translators: %s = Hosting name.
#: inc/ThirdParty/Hostings/Cloudways.php:82
msgid "Varnish auto-purge will be automatically enabled once Varnish is enabled on your %s server."
msgstr ""

#. translators: %1$s = opening link tag, %2$s = closing link tag.
#: inc/ThirdParty/Hostings/Kinsta.php:159
msgid "Your installation seems to be missing core Kinsta files managing Cache clearing, which will prevent your Kinsta installation and WP Rocket from working correctly. Please get in touch with Kinsta support through your %1$sMyKinsta%2$s account to resolve this issue."
msgstr ""

#. translators: %1$s = plugin name.
#: inc/ThirdParty/Plugins/CDN/Cloudflare.php:127
msgid "%1$s: Cloudflare's HTTP/2 Server Push is incompatible with the features of Remove Unused CSS and Combine CSS files. We strongly recommend disabling it."
msgstr ""

#: inc/ThirdParty/Plugins/CDN/Cloudflare.php:170
msgid "Your site is using the official Cloudflare plugin. We have enabled Cloudflare auto-purge for compatibility. If you have APO activated, it is also compatible."
msgstr ""

#: inc/ThirdParty/Plugins/CDN/Cloudflare.php:171
msgid "Cloudflare cache will be purged each time WP Rocket clears its cache to ensure content is always up-to-date."
msgstr ""

#. Translators: %1$s = strong opening tag, %2$s = strong closing tag.
#: inc/ThirdParty/Plugins/CDN/Cloudflare.php:215
msgid "%1$sWP Rocket:%2$s You are using \"Dynamic Cookies Cache\". Cloudflare APO is not yet compatible with that feature."
msgstr ""

#. Translators:%1$s = opening <a> tag, %2$s = closing </a> tag.
#: inc/ThirdParty/Plugins/CDN/Cloudflare.php:221
msgid "You should either disable Cloudflare APO or check with the theme/plugin requiring the use of “Dynamic Cookies Cache” developers for an alternative way to be page-cache friendly. %1$sMore info%2$s"
msgstr ""

#. Translators: %1$s = strong opening tag, %2$s = strong closing tag, %3$s = opening <a> tag, %4$s = closing </a> tag, %5$s = opening <a> tag.
#: inc/ThirdParty/Plugins/CDN/Cloudflare.php:272
msgid "%1$sWP Rocket:%2$s You are using \"Separate cache files for mobile devices\". You need to activate \"Cache by Device Type\" %3$ssetting%5$s on Cloudflare APO to serve the right version of the cache. %4$sMore info%5$s"
msgstr ""

#. Translators: %1$s = strong opening tag, %2$s = strong closing tag.
#: inc/ThirdParty/Plugins/CDN/Cloudflare.php:293
msgid "%1$sWP Rocket:%2$s You have \"Cache by Device Type\" enabled on Cloudflare APO. If you judge it necessary for the website to have a different cache on mobile and desktop, we suggest you enable our “Separate Cache Files for Mobiles Devices” to ensure the generated cache is accurate."
msgstr ""

#. translators: %1$s is WP Rocket plugin name, %2$s is opening <a> tag, %3$s is closing </a> tag.
#: inc/ThirdParty/Plugins/ModPagespeed.php:102
msgid "<strong>%1$s</strong>: Mod PageSpeed is not compatible with this plugin and may cause unexpected results. %2$sMore Info%3$s"
msgstr ""

#. Translators: %1$s is an opening <strong> tag; %2$s is a closing </strong> tag
#: inc/ThirdParty/Plugins/Optimization/Autoptimize.php:75
msgid "%1$sWP Rocket: %2$sWe have detected that Autoptimize's JavaScript Aggregation feature is enabled. WP Rocket's Delay JavaScript Execution will not be applied to the file it creates. We suggest disabling %1$sJavaScript Aggregation%2$s to take full advantage of Delay JavaScript Execution."
msgstr ""

#. Translators: %1$s is an opening <strong> tag; %2$s is a closing </strong> tag
#: inc/ThirdParty/Plugins/Optimization/Autoptimize.php:130
msgid "%1$sWP Rocket: %2$sWe have detected that Autoptimize's Aggregate Inline CSS feature is enabled. WP Rocket's Load CSS Asynchronously will not work correctly. We suggest disabling %1$sAggregate Inline CSS%2$s to take full advantage of Load CSS Asynchronously Execution."
msgstr ""

#. translators: %1$s = opening <a> tag, %2$s = closing </a> tag.
#: inc/ThirdParty/Plugins/Optimization/Ezoic.php:45
msgid "This plugin blocks WP Rocket's caching and optimizations. Deactivate it and use %1$sEzoic's nameserver integration%2$s instead."
msgstr ""

#. Translators: %s = Plugin name.
#: inc/ThirdParty/Plugins/Optimization/Hummingbird.php:78
msgctxt "Hummingbird notice"
msgid "Please deactivate the following %s option which conflicts with WP Rocket features:"
msgid_plural "Please deactivate the following %s options which conflict with WP Rocket features:"
msgstr[0] ""
msgstr[1] ""

#. Translators: %1$s = Plugin name, %2$s = <em>, %3$s = </em>.
#: inc/ThirdParty/Plugins/Optimization/Hummingbird.php:151
msgctxt "Hummingbird notice"
msgid "%1$s %2$sdisable emoji%3$s conflicts with WP Rockets %2$sdisable emoji%3$s"
msgstr ""

#. Translators: %1$s = Plugin name, %2$s = <em>, %3$s = </em>.
#: inc/ThirdParty/Plugins/Optimization/Hummingbird.php:187
msgctxt "Hummingbird notice"
msgid "%1$s %2$sGZIP compression%3$s conflicts with WP Rocket %2$sGZIP compression%3$s"
msgstr ""

#. Translators: %1$s = Plugin name, %2$s = <em>, %3$s = </em>.
#: inc/ThirdParty/Plugins/Optimization/Hummingbird.php:223
msgctxt "Hummingbird notice"
msgid "%1$s %2$sbrowser caching%3$s conflicts with WP Rocket %2$sbrowser caching%3$s"
msgstr ""

#. Translators: %1$s = Plugin name, %2$s = <em>, %3$s = </em>.
#: inc/ThirdParty/Plugins/Optimization/Hummingbird.php:255
msgctxt "Hummingbird notice"
msgid "%1$s %2$spage caching%3$s conflicts with WP Rocket %2$spage caching%3$s"
msgstr ""

#. Translators: %1$s = Plugin name, %2$s = <em>, %3$s = </em>.
#: inc/ThirdParty/Plugins/Optimization/Hummingbird.php:289
msgctxt "Hummingbird notice"
msgid "%1$s %2$sasset optimization%3$s conflicts with WP Rocket %2$sfile optimization%3$s"
msgstr ""

#: inc/ThirdParty/Plugins/Optimization/Perfmatters.php:38
msgid "Remove Unused CSS is currently activated in Perfmatters. If you want to use WP Rocket's Remove Unused CSS feature, disable this option in Perfmatters."
msgstr ""

#: inc/ThirdParty/Plugins/Optimization/RapidLoad.php:39
msgid "Automated unused CSS removal is currently activated in RapidLoad Power-Up for Autoptimize. If you want to use WP Rocket's Remove Unused CSS feature, disable the  RapidLoad Power-Up for Autoptimize plugin."
msgstr ""

#. translators: %1$s = plugin name.
#: inc/ThirdParty/Plugins/Optimization/WPMeteor.php:42
msgid "Delay JS is currently activated in %1$s. If you want to use WP Rocket’s delay JS, disable %1$s"
msgstr ""

#: inc/ThirdParty/Plugins/Smush.php:108
#: inc/ThirdParty/Plugins/Smush.php:126
msgid "Smush"
msgstr ""

#: inc/ThirdParty/Themes/Avada.php:107
msgid "Avada"
msgstr ""

#: inc/ThirdParty/Themes/Divi.php:293
msgid "Your Divi template was updated. Clear the Used CSS if the layout, design or CSS styles were changed."
msgstr ""

#: views/cpcss/activate-cpcss-mobile.php:13
msgid "Load CSS asynchronously for mobile"
msgstr ""

#: views/cpcss/activate-cpcss-mobile.php:14
msgid "Your website currently uses the same Critical Path CSS for both desktop and mobile."
msgstr ""

#: views/cpcss/activate-cpcss-mobile.php:15
msgid "Click the button to enable mobile-specific CPCSS for your site."
msgstr ""

#. translators: %1$s = opening link tag, %2$s = closing link tag.
#: views/cpcss/activate-cpcss-mobile.php:20
#: views/settings/enable-google-fonts.php:19
#: views/settings/mobile-cache.php:20
msgid "This is a one-time action and this button will be removed afterwards. %1$sMore info%2$s"
msgstr ""

#. translators: %1$s = opening link tag, %2$s = closing link tag.
#: views/cpcss/activate-cpcss-mobile.php:30
msgid "Your site is now using mobile-specific critical path CSS. %1$sMore info%2$s"
msgstr ""

#: views/cpcss/activate-cpcss-mobile.php:39
msgid "Generate Mobile Specific CPCSS"
msgstr ""

#: views/cpcss/metabox/container.php:19
msgid "Critical Path CSS"
msgstr ""

#. translators: %1$s = opening link tag, %2$s = closing link tag.
#: views/cpcss/metabox/generate.php:23
msgid "Generate specific Critical Path CSS for this post. %1$sMore info%2$s"
msgstr ""

#. translators: %1$s = opening link tag, %2$s = closing link tag.
#: views/cpcss/metabox/generate.php:33
msgid "This post uses specific Critical Path CSS. %1$sMore info%2$s"
msgstr ""

#: views/cpcss/metabox/generate.php:56
msgid "Revert back to the default CPCSS"
msgstr ""

#: views/deactivation-intent/form.php:21
msgid "Facing an issue?"
msgstr ""

#: views/deactivation-intent/form.php:24
msgid "It is not always necessary to deactivate WP Rocket when facing any issues. Most of them can be fixed by deactivating only some options."
msgstr ""

#. translators: %1$s = opening strong tag, %2$s = closing strong tag.
#: views/deactivation-intent/form.php:29
msgid "Our advice? Instead of deactivating WP Rocket, use our %1$sSafe Mode%2$s to quickly disable LazyLoad, File Optimization, and CDN options. Then check to see if your issue is resolved."
msgstr ""

#: views/deactivation-intent/form.php:35
msgid "Do you want to use our Safe Mode to troubleshoot WP Rocket?"
msgstr ""

#. translators: %1$s = opening strong tag, %2$s = closing strong tag.
#: views/deactivation-intent/form.php:55
msgid "Yes, apply \"%1$sSafe Mode%2$s\""
msgstr ""

#. translators: %1$s = opening strong tag, %2$s = closing strong tag.
#: views/deactivation-intent/form.php:68
msgid "and export WP Rocket settings %1$s(Recommended as current settings will be deleted)%2$s"
msgstr ""

#: views/deactivation-intent/form.php:76
msgid "No, deactivate and snooze this message for"
msgstr ""

#: views/deactivation-intent/form.php:78
msgid "1 day"
msgstr ""

#: views/deactivation-intent/form.php:79
msgid "7 days"
msgstr ""

#: views/deactivation-intent/form.php:80
msgid "30 days"
msgstr ""

#: views/deactivation-intent/form.php:81
msgid "Forever"
msgstr ""

#: views/deactivation-intent/form.php:87
msgid "Cancel"
msgstr ""

#: views/deactivation-intent/form.php:88
msgid "Confirm"
msgstr ""

#. translators: %1$s = <strong>, %2$s = plugin version, %3$s = </strong>, %4$s = <a>, %5$s = </a>, %6$s = <a>.
#: views/plugins/update-renewal-expired-notice.php:27
msgid " %1$sWP Rocket %2$s%3$s is available. %4$sLearn more%5$s about the updates and enhancements of this major version. You need an active license to use them on your website, don’t miss out! %6$sRenew Now%5$s"
msgstr ""

#: views/settings/dynamic-lists-update.php:14
msgid "Update Inclusion and Exclusion Lists"
msgstr ""

#. translators: %1$s = opening link tag, %2$s = closing link tag.
#: views/settings/dynamic-lists-update.php:19
msgid "Compatibility lists are downloaded automatically every week. Click the button if you want to update them manually. %1$sMore info%2$s"
msgstr ""

#: views/settings/dynamic-lists-update.php:29
msgid "Update lists"
msgstr ""

#: views/settings/enable-google-fonts.php:13
msgid "Enable Google Font Optimization"
msgstr ""

#: views/settings/enable-google-fonts.php:14
msgid "Improves font performance and combines multiple font requests to reduce the number of HTTP requests."
msgstr ""

#. translators: %1$s = opening link tag, %2$s = closing link tag.
#: views/settings/enable-google-fonts.php:29
msgid "Google Fonts Optimization is now enabled for your site. %1$sMore info%2$s"
msgstr ""

#: views/settings/enable-google-fonts.php:38
msgid "Optimize Google Fonts"
msgstr ""

#: views/settings/fields/cache-lifespan.php:26
msgid "Clear cache after"
msgstr ""

#: views/settings/fields/cnames.php:58
#: views/settings/fields/cnames.php:92
#: views/settings/fields/cnames.php:122
msgid "CSS & JavaScript"
msgstr ""

#: views/settings/fields/cnames.php:59
#: views/settings/fields/cnames.php:93
#: views/settings/fields/cnames.php:123
msgid "JavaScript"
msgstr ""

#: views/settings/fields/cnames.php:60
#: views/settings/fields/cnames.php:94
#: views/settings/fields/cnames.php:124
msgid "CSS"
msgstr ""

#: views/settings/fields/import-form.php:29
msgid "Import settings"
msgstr ""

#: views/settings/fields/one-click-addon.php:43
#: views/settings/fields/rocket-addon.php:43
msgid "Add-on status"
msgstr ""

#: views/settings/fields/rocket-addon.php:72
msgid "Modify options"
msgstr ""

#: views/settings/fields/rocket-cdn.php:29
#: views/settings/fields/rocket-cdn.php:41
msgid "CDN CNAME"
msgstr ""

#. translators: %s is a "Learn more" link.
#: views/settings/fields/rocket-cdn.php:62
msgid "Purges RocketCDN cached resources for your website. %s"
msgstr ""

#. translators: %s is a "Learn more" link.
#: views/settings/fields/rocket-cdn.php:63
#: views/settings/page-sections/cloudflare.php:36
#: views/settings/page-sections/sucuri.php:37
msgid "Learn more"
msgstr ""

#: views/settings/fields/rocket-cdn.php:72
msgid "Clear all RocketCDN cache files"
msgstr ""

#: views/settings/mobile-cache.php:11
msgid "Mobile Cache"
msgstr ""

#: views/settings/mobile-cache.php:14
msgid "Speed your site for mobile visitors."
msgstr ""

#: views/settings/mobile-cache.php:28
msgid "Mobile Cache is now enabled for your site."
msgstr ""

#: views/settings/mobile-cache.php:34
msgid "Enable Mobile Cache"
msgstr ""

#: views/settings/page-sections/cloudflare.php:27
msgid "Cloudflare Cache"
msgstr ""

#. translators: %s is a "Learn more" link.
#: views/settings/page-sections/cloudflare.php:35
#: views/settings/page-sections/sucuri.php:36
msgid "Purges cached resources for your website. %s"
msgstr ""

#. translators: %s is a "Learn more" link.
#: views/settings/page-sections/cloudflare.php:36
msgid "https://support.cloudflare.com/hc/en-us/articles/200169246"
msgstr ""

#: views/settings/page-sections/cloudflare.php:45
msgid "Clear all Cloudflare cache files"
msgstr ""

#: views/settings/page-sections/dashboard.php:38
msgid "Congratulations!"
msgstr ""

#: views/settings/page-sections/dashboard.php:40
msgid "WP Rocket is now activated and already working for you."
msgstr ""

#: views/settings/page-sections/dashboard.php:42
msgid "Your website should be loading faster now!"
msgstr ""

#: views/settings/page-sections/dashboard.php:44
msgid "To guarantee fast websites, WP Rocket automatically applies 80% of web performance best practices."
msgstr ""

#: views/settings/page-sections/dashboard.php:44
msgid "We also enable options that provide immediate benefits to your website."
msgstr ""

#: views/settings/page-sections/dashboard.php:45
msgid "Continue to the options to further optimize your site!"
msgstr ""

#: views/settings/page-sections/dashboard.php:62
msgid "My Account"
msgstr ""

#: views/settings/page-sections/dashboard.php:68
msgid "Refresh info"
msgstr ""

#: views/settings/page-sections/dashboard.php:86
msgid "with"
msgstr ""

#: views/settings/page-sections/dashboard.php:100
msgid "Expiration Date"
msgstr ""

#: views/settings/page-sections/dashboard.php:110
msgid "View my account"
msgstr ""

#: views/settings/page-sections/dashboard.php:137
msgid "Quick Actions"
msgstr ""

#: views/settings/page-sections/dashboard.php:144
msgid "Remove all cached files"
msgstr ""

#: views/settings/page-sections/dashboard.php:164
#: views/settings/page-sections/dashboard.php:170
msgid "Regenerate Critical CSS"
msgstr ""

#: views/settings/page-sections/dashboard.php:196
msgid "Frequently Asked Questions"
msgstr ""

#: views/settings/page-sections/dashboard.php:210
msgid "Still cannot find a solution?"
msgstr ""

#: views/settings/page-sections/dashboard.php:211
msgid "Submit a ticket and get help from our friendly and knowledgeable Rocketeers."
msgstr ""

#: views/settings/page-sections/dashboard.php:219
msgid "Ask support"
msgstr ""

#: views/settings/page-sections/database.php:25
msgid "Backup your database before you run a cleanup!"
msgstr ""

#: views/settings/page-sections/database.php:26
msgid "Once a database optimization has been performed, there is no way to undo it."
msgstr ""

#: views/settings/page-sections/database.php:28
msgid "Save Changes and Optimize"
msgstr ""

#. Translators: %1$s = <strong>, %2$s = </strong>, %3$s = <span class="imagify-name">, %4$s = </span>.
#: views/settings/page-sections/imagify.php:21
msgid "%1$sWP ROCKET%2$s created %3$sIMAGIFY%4$s %1$sfor best-in-class image optimization.%2$s"
msgstr ""

#: views/settings/page-sections/imagify.php:24
msgid "Compress image to make your website faster, all while maintaining image quality."
msgstr ""

#: views/settings/page-sections/imagify.php:25
msgid "More on Imagify:"
msgstr ""

#: views/settings/page-sections/imagify.php:27
msgid "Imagify Plugin Page"
msgstr ""

#: views/settings/page-sections/imagify.php:28
msgid "Imagify Website"
msgstr ""

#: views/settings/page-sections/imagify.php:29
msgid "Review of Image Compression Plugins"
msgstr ""

#: views/settings/page-sections/imagify.php:38
msgid "Install Imagify"
msgstr ""

#: views/settings/page-sections/license.php:22
msgid "WP Rocket was not able to automatically validate your license."
msgstr ""

#. translators: %1$s = tutorial URL, %2$s = support URL.
#: views/settings/page-sections/license.php:29
msgid "Follow this %1$s, or contact %2$s to get the engine started."
msgstr ""

#. translators: %1$s = <a href=", %2$s =  tutorial href,  %3$s =  " target="_blank">,  %4$s =  </a>.
#: views/settings/page-sections/license.php:32
msgid "%1$s%2$s%3$stutorial%4$s"
msgstr ""

#: views/settings/page-sections/license.php:34
msgid "https://docs.wp-rocket.me/article/100-resolving-problems-with-license-validation/?utm_source=wp_plugin&utm_medium=wp_rocket"
msgstr ""

#. translators: %1$s = <a href=", %2$s =  support href,  %3$s =  " target="_blank">,  %4$s =  </a>.
#: views/settings/page-sections/license.php:40
msgid "%1$s%2$s%3$ssupport%4$s"
msgstr ""

#: views/settings/page-sections/sucuri.php:46
msgid "Clear all Sucuri cache files"
msgstr ""

#. translators: %1$s = formatted file size, %2$s = formatted number of entries (don't use %2$d).
#: views/settings/page-sections/tools.php:20
msgid "Files size: %1$s. Number of entries: %2$s."
msgstr ""

#. translators: %1$s = opening <a> tag, %2$s = closing </a> tag.
#: views/settings/page-sections/tools.php:23
msgid "%1$sDownload the file%2$s."
msgstr ""

#. translators: %1$s = opening <a> tag, %2$s = closing </a> tag.
#: views/settings/page-sections/tools.php:26
msgid "%1$sDelete the file%2$s."
msgstr ""

#: views/settings/page-sections/tools.php:37
msgid "Export settings"
msgstr ""

#: views/settings/page-sections/tools.php:38
msgid "Download a backup file of your settings"
msgstr ""

#: views/settings/page-sections/tools.php:46
msgid "Download settings"
msgstr ""

#: views/settings/page-sections/tools.php:60
msgid "Rollback"
msgstr ""

#. translators: %s = WP Rocket version number.
#: views/settings/page-sections/tools.php:64
msgid "Has version %s caused an issue on your website?"
msgstr ""

#. translators: %s = <br>.
#: views/settings/page-sections/tools.php:69
msgid "You can rollback to the previous major version here.%sThen send us a support request."
msgstr ""

#. translators: %s = WP Rocket previous version.
#: views/settings/page-sections/tools.php:80
msgid "Reinstall version %s"
msgstr ""

#: views/settings/page-sections/tools.php:106
msgid "Debug mode"
msgstr ""

#: views/settings/page-sections/tools.php:111
msgid "Create a debug log file."
msgstr ""

#: views/settings/page-sections/tutorials.php:13
#: views/settings/partials/getting-started.php:18
msgid "Getting Started"
msgstr ""

#: views/settings/page-sections/tutorials.php:15
msgid "Getting Started with WP Rocket"
msgstr ""

#: views/settings/page-sections/tutorials.php:16
#: views/settings/partials/getting-started.php:12
msgid "Finding the Best Settings for Your Site"
msgstr ""

#: views/settings/page-sections/tutorials.php:17
#: views/settings/partials/getting-started.php:13
msgid "How to Check if WP Rocket is Caching Your Site"
msgstr ""

#: views/settings/page-sections/tutorials.php:18
#: views/settings/partials/getting-started.php:14
msgid "How to Measure the Speed of Your Site"
msgstr ""

#: views/settings/page-sections/tutorials.php:19
msgid "How Preloading Works"
msgstr ""

#: views/settings/page-sections/tutorials.php:23
msgid "Passing the Core Web vitals"
msgstr ""

#: views/settings/page-sections/tutorials.php:25
msgid "How to improve LCP with WP Rocket"
msgstr ""

#: views/settings/page-sections/tutorials.php:26
msgid "How to improve FID with WP Rocket"
msgstr ""

#: views/settings/page-sections/tutorials.php:27
msgid "How to improve CLS with WP Rocket"
msgstr ""

#: views/settings/page-sections/tutorials.php:31
msgid "Troubleshooting"
msgstr ""

#: views/settings/page-sections/tutorials.php:33
msgid "Troubleshooting Display Issues with File Optimization"
msgstr ""

#: views/settings/page-sections/tutorials.php:34
msgid "How to Find the Right JavaScript to Exclude"
msgstr ""

#: views/settings/page-sections/tutorials.php:35
msgid "How External Content Slows Your Site"
msgstr ""

#: views/settings/page-sections/tutorials.php:41
msgid "Set Up the Cloudflare Add-on"
msgstr ""

#: views/settings/page.php:16
msgid "WP Rocket Settings"
msgstr ""

#. translators: %s = Plugin version number.
#: views/settings/page.php:30
msgid "version %s"
msgstr ""

#: views/settings/page.php:60
msgid "Show Sidebar"
msgstr ""

#. translators: %1$s = <strong>, %2$s = </strong>.
#: views/settings/page.php:82
msgid "Below is a detailed view of all data WP Rocket will collect %1$sif granted permission.%2$s"
msgstr ""

#: views/settings/page.php:88
msgid "WP Rocket will never transmit any domain names or email addresses (except for license validation), IP addresses, or third-party API keys."
msgstr ""

#: views/settings/page.php:90
msgid "Activate Rocket analytics"
msgstr ""

#: views/settings/partials/documentation.php:15
msgid "It is a great starting point to fix some of the most common issues."
msgstr ""

#: views/settings/partials/documentation.php:22
msgid "Read the documentation"
msgstr ""

#: views/settings/partials/getting-started.php:11
msgid "What WP Rocket Does For You By Default"
msgstr ""

#: views/settings/partials/sidebar.php:12
msgid "How to correctly measure your website’s loading time"
msgstr ""

#: views/settings/partials/sidebar.php:14
msgid "Check our tutorial and learn how to measure the speed of your site."
msgstr ""

#: views/settings/partials/sidebar.php:15
#: views/settings/partials/sidebar.php:24
msgid "Read our guide"
msgstr ""

#: views/settings/partials/sidebar.php:18
msgid "Learn about optimal WP Rocket settings for mobile."
msgstr ""

#: views/settings/partials/sidebar.php:27
msgid "Test and Improve Google Core Web Vitals for WordPress."
msgstr ""

#: views/settings/partials/sidebar.php:28
msgid "Read more"
msgstr ""

#: views/settings/partials/sidebar.php:33
msgid "You have not activated logged-in user cache."
msgstr ""

#: views/settings/partials/sidebar.php:34
msgid "Use a private browser to check your website's speed and visual appearance."
msgstr ""

#: views/settings/sections/addons-container.php:24
#: views/settings/sections/fields-container.php:28
msgid "Need Help?"
msgstr ""
