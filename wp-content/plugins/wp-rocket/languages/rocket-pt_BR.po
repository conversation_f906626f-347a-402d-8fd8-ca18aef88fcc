# Copyright (C) 2024 WP Media
# This file is distributed under the same license as the WP Rocket plugin.
# Translators:
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2019
# <PERSON> <<EMAIL>>, 2019
# <PERSON> <<EMAIL>>, 2019
# <AUTHOR> <EMAIL>, 2022
# <PERSON><PERSON><PERSON>, 2023
# Fabio <PERSON> <<EMAIL>>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: WP Rocket 3.16-alpha1\n"
"Report-Msgid-Bugs-To: https://wordpress.org/support/plugin/wp-rocket\n"
"POT-Creation-Date: 2024-04-29T20:40:45+00:00\n"
"PO-Revision-Date: 2019-08-26 15:14+0000\n"
"Last-Translator: Fabio <PERSON> <<EMAIL>>, 2024\n"
"Language-Team: Portuguese (Brazil) (https://app.transifex.com/wp-media/teams/18133/pt_BR/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: pt_BR\n"
"Plural-Forms: nplurals=3; plural=(n == 0 || n == 1) ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"
"X-Domain: rocket\n"
"X-Generator: WP-CLI 2.4.0\n"

#. Plugin Name of the plugin
msgid "WP Rocket"
msgstr "WP Rocket"

#. Plugin URI of the plugin
msgid "https://wp-rocket.me"
msgstr "https://wp-rocket.me"

#. Description of the plugin
msgid "The best WordPress performance plugin."
msgstr "O melhor plugin de desempenho para WordPress."

#. Author of the plugin
msgid "WP Media"
msgstr "Mídia do WP"

#. Author URI of the plugin
msgid "https://wp-media.me"
msgstr "https://wp-media.me"

#. Translators: %s = Hosting name.
#: inc/3rd-party/hosting/flywheel.php:18
#: inc/3rd-party/hosting/wp-serveur.php:27 inc/deprecated/3.6.php:697
#: inc/deprecated/3.6.php:997 inc/deprecated/3.9.php:22
#: inc/ThirdParty/Hostings/Cloudways.php:90
#: inc/ThirdParty/Hostings/Dreampress.php:44
#: inc/ThirdParty/Hostings/Godaddy.php:63
#: inc/ThirdParty/Hostings/O2Switch.php:49
#: inc/ThirdParty/Hostings/OneCom.php:137
#: inc/ThirdParty/Hostings/Pressidium.php:49
#: inc/ThirdParty/Hostings/ProIsp.php:51 inc/ThirdParty/Hostings/Savvii.php:50
#: inc/ThirdParty/Hostings/WPEngine.php:47
#: inc/ThirdParty/Hostings/WPXCloud.php:51
msgid ""
"Your site is hosted on %s, we have enabled Varnish auto-purge for "
"compatibility."
msgstr ""
"Seu site está hospedado em %s, habilitamos a auto-limpeza do Varnish para "
"compatibilidade."

#: inc/Addon/Cloudflare/API/Client.php:129
msgid "Cloudflare did not provide any reply. Please try again later."
msgstr "O Cloudflare não forneceu nenhuma resposta. Tente mais tarde."

#: inc/Addon/Cloudflare/API/Client.php:135
msgid "Cloudflare unexpected response"
msgstr "Resposta inesperada do Cloudflare"

#: inc/Addon/Cloudflare/API/Client.php:143
msgid "Missing Cloudflare result."
msgstr "Faltando o resultado do Cloudflare"

#: inc/Addon/Cloudflare/API/Client.php:194 inc/deprecated/3.5.php:112
#: inc/deprecated/3.5.php:169
msgid "Incorrect Cloudflare email address or API key."
msgstr "Endereço de e-mail ou chave da API incorretos do Cloudflare"

#. translators: %1$s = opening link; %2$s = closing link
#: inc/Addon/Cloudflare/API/Client.php:198
#: inc/Addon/Cloudflare/API/Client.php:212
#: inc/Addon/Cloudflare/Cloudflare.php:75
#: inc/Addon/Cloudflare/Cloudflare.php:108
#: inc/classes/subscriber/Tools/class-detect-missing-tags-subscriber.php:157
#: inc/deprecated/3.5.php:92 inc/deprecated/3.5.php:116
#: inc/deprecated/3.5.php:129 inc/deprecated/3.5.php:157
#: inc/deprecated/3.5.php:173
msgid "Read the %1$sdocumentation%2$s for further guidance."
msgstr "Leia a %1$sdocumentação%2$s para orientações adicionais."

#. translators: Documentation exists in EN, FR; use localized URL if
#. applicable.
#: inc/Addon/Cloudflare/API/Client.php:200
#: inc/Addon/Cloudflare/API/Client.php:214
#: inc/Addon/Cloudflare/Auth/APIKey.php:63
#: inc/Addon/Cloudflare/Cloudflare.php:77
#: inc/Addon/Cloudflare/Cloudflare.php:110 inc/deprecated/3.5.php:81
#: inc/deprecated/3.5.php:94 inc/deprecated/3.5.php:118
#: inc/deprecated/3.5.php:131 inc/deprecated/3.5.php:159
#: inc/deprecated/3.5.php:175 inc/deprecated/3.5.php:208
msgid ""
"https://docs.wp-rocket.me/article/18-using-wp-rocket-with-"
"cloudflare/?utm_source=wp_plugin&utm_medium=wp_rocket#add-on"
msgstr ""
"https://docs.wp-rocket.me/article/18-using-wp-rocket-with-"
"cloudflare/?utm_source=wp_plugin&utm_medium=wp_rocket#add-on"

#: inc/Addon/Cloudflare/API/Client.php:208 inc/deprecated/3.5.php:125
msgid "Incorrect Cloudflare Zone ID."
msgstr "ID incorreto da Zona do Cloudflare"

#. translators: %1$s = opening link; %2$s = closing link
#: inc/Addon/Cloudflare/Auth/APIKey.php:61
msgid ""
"Cloudflare email and/or API key are not set. Read the %1$sdocumentation%2$s "
"for further guidance."
msgstr ""
"O e-mail e/ou chave da API do Cloudflare não estão definidos. Leia a "
"%1$sdocumentação%2$s para maior orientação."

#: inc/Addon/Cloudflare/Cloudflare.php:71 inc/deprecated/3.5.php:88
msgid "Missing Cloudflare Zone ID."
msgstr "Falta o ID da Zona do Cloudflare"

#: inc/Addon/Cloudflare/Cloudflare.php:104 inc/deprecated/3.5.php:153
msgid "It looks like your domain is not set up on Cloudflare."
msgstr "Parece que o seu domínio não está configurado no Cloudflare"

#: inc/Addon/Cloudflare/Cloudflare.php:200
msgid "days"
msgstr "dias"

#: inc/Addon/Cloudflare/Cloudflare.php:203
msgid "seconds"
msgstr "segundos"

#: inc/Addon/Cloudflare/Cloudflare.php:205
msgid "minutes"
msgstr "minutos"

#: inc/Addon/Cloudflare/Cloudflare.php:207
msgid "hours"
msgstr "horas"

#. translators: %1$s = <strong>, %2$s = </strong>, %3$s = CloudFare API return
#. message.
#: inc/Addon/Cloudflare/Subscriber.php:232
#: inc/Addon/Cloudflare/Subscriber.php:261
msgid "%1$sWP Rocket:%2$s %3$s"
msgstr "%1$sWP Rocket:%2$s %3$s"

#. translators: %1$s = <strong>, %2$s = </strong>.
#: inc/Addon/Cloudflare/Subscriber.php:250
msgid "%1$sWP Rocket:%2$s Cloudflare cache successfully purged."
msgstr "%1$sWP Rocket:%2$s Cache do Cloudflare esvaziado com sucesso."

#. translators: %s is the message returned by the CloudFlare API.
#: inc/Addon/Cloudflare/Subscriber.php:311
msgid "Cloudflare development mode error: %s"
msgstr "Erro no modo de desenvolvimento do Cloudflare: %s"

#. translators: %s is the message returned by the CloudFlare API.
#: inc/Addon/Cloudflare/Subscriber.php:318
msgid "Cloudflare development mode %s"
msgstr "Modo de desenvolvimento do Cloudflare %s"

#. translators: %s is the message returned by the CloudFlare API.
#: inc/Addon/Cloudflare/Subscriber.php:337
msgid "Cloudflare cache level error: %s"
msgstr "Erro no nível de Cache do Cloudflare: %s"

#: inc/Addon/Cloudflare/Subscriber.php:344
msgctxt "Cloudflare caching level"
msgid "standard"
msgstr "padrão"

#. translators: %s is the caching level returned by the CloudFlare API.
#: inc/Addon/Cloudflare/Subscriber.php:350
msgid "Cloudflare cache level set to %s"
msgstr "Nível de cache do Cloudflare definido para %s"

#. translators: %s is the message returned by the CloudFlare API.
#: inc/Addon/Cloudflare/Subscriber.php:368
msgid "Cloudflare minification error: %s"
msgstr "Erro de minificação do Cloudflare: %s"

#. translators: %s is the message returned by the CloudFlare API.
#: inc/Addon/Cloudflare/Subscriber.php:375
msgid "Cloudflare minification %s"
msgstr "Minificação do Cloudflare %s"

#. translators: %s is the message returned by the CloudFlare API.
#: inc/Addon/Cloudflare/Subscriber.php:393
msgid "Cloudflare rocket loader error: %s"
msgstr "Erro no rocket loader do Cloudflare: %s"

#. translators: %s is the message returned by the CloudFlare API.
#: inc/Addon/Cloudflare/Subscriber.php:400
msgid "Cloudflare rocket loader %s"
msgstr "Rocket loader do Cloudflare %s"

#. translators: %s is the message returned by the CloudFlare API.
#: inc/Addon/Cloudflare/Subscriber.php:418
msgid "Cloudflare browser cache error: %s"
msgstr "Erro do cache de navegador no Cloudflare: %s"

#. translators: %s is the message returned by the CloudFlare API.
#: inc/Addon/Cloudflare/Subscriber.php:425
msgid "Cloudflare browser cache set to %s"
msgstr "Cache do navegador do Cloudflare definido para %s"

#. translators: %1$s = strong opening tag, %2$s = strong closing tag.
#: inc/Addon/Cloudflare/Subscriber.php:536
msgid "%1$sWP Rocket:%2$s Optimal settings activated for Cloudflare:"
msgstr ""
"%1$sWP Rocket:%2$s Configurações otimizadas ativadas para o Cloudflare:"

#. translators: %1$s = strong opening tag, %2$s = strong closing tag.
#: inc/Addon/Cloudflare/Subscriber.php:545
msgid ""
"%1$sWP Rocket:%2$s Optimal settings deactivated for Cloudflare, reverted to "
"previous settings:"
msgstr ""
"%1$sWP Rocket:%2$s Configurações otimizadas desativadas para o Cloudflare, "
"revertidas para as configurações anteriores:"

#: inc/Addon/Cloudflare/Subscriber.php:661 inc/admin/options.php:165
#: inc/classes/subscriber/Tools/class-detect-missing-tags-subscriber.php:148
msgid "WP Rocket: "
msgstr "WP Rocket: "

#. translators: %s is the error message returned by the API.
#: inc/Addon/Sucuri/Subscriber.php:96
msgid "Sucuri cache purge error: %s"
msgstr "Erro esvaziando o cache Sucuri: %s"

#: inc/Addon/Sucuri/Subscriber.php:101
msgid ""
"The Sucuri cache is being cleared. Note that it may take up to two minutes "
"for it to be fully flushed."
msgstr ""
"O cache Sucuri está sendo esvaziado. Note que pode levar até dois minutos "
"para ele estar totalmente vazio."

#: inc/Addon/Sucuri/Subscriber.php:218
msgid "Sucuri firewall API key was not found."
msgstr "A chave da API Sucuri não foi encontrada."

#: inc/Addon/Sucuri/Subscriber.php:231
msgid "Sucuri firewall API key is invalid."
msgstr "A chave da API Sucuri é inválida."

#. translators: %s is an error message.
#: inc/Addon/Sucuri/Subscriber.php:286
msgid "Error when contacting Sucuri firewall API. Error message was: %s"
msgstr "Erro contactando a API de Firewall Sucuri. A mensagem de erro foi: %s"

#: inc/Addon/Sucuri/Subscriber.php:301
msgid "Could not get a response from the Sucuri firewall API."
msgstr "Não foi possível obter resposta da API de Firewall Sucuri."

#: inc/Addon/Sucuri/Subscriber.php:316
msgid "Got an invalid response from the Sucuri firewall API."
msgstr "Resposta inválida da API de Firewall Sucuri."

#: inc/Addon/Sucuri/Subscriber.php:330
msgid "The Sucuri firewall API returned an unknown error."
msgstr "A API de Firewall Sucuri retornou um erro desconhecido."

#. translators: %s is an error message.
#: inc/Addon/Sucuri/Subscriber.php:334
msgid "The Sucuri firewall API returned the following error: %s"
msgid_plural "The Sucuri firewall API returned the following errors: %s"
msgstr[0] "A API do firewall Sucuri retornou o seguinte erro: %s"
msgstr[1] "A API do firewall Sucuri retornou os seguintes erros: %s"
msgstr[2] "A API do firewall Sucuri retornou os seguintes erros: %s"

#. Translators: %1$s = plugin name(s), %2$s = opening <a> tag, %3$s = closing
#. </a> tag.
#: inc/Addon/WebP/AdminSubscriber.php:93
msgid ""
"You are using %1$s to serve WebP images so you do not need to enable this "
"option. %2$sMore info%3$s %4$s If you prefer to have WP Rocket serve WebP "
"for you instead, please disable WebP display in %1$s."
msgid_plural ""
"You are using %1$s to serve WebP images so you do not need to enable this "
"option. %2$sMore info%3$s %4$s If you prefer to have WP Rocket serve WebP "
"for you instead, please disable WebP display in %1$s."
msgstr[0] ""
"Você está usando %1$s para servir imagens WebP e por isso não precisa ativar"
" esta opção. %2$sMais informações%3$s %4$s Se preferir usar o WP Rocket para"
" servir WebP para você, desative a exibição de WebP em %1$s."
msgstr[1] ""
"Você está usando %1$s para servir imagens WebP e por isso não precisa ativar"
" esta opção. %2$sMais informações%3$s %4$s Se preferir usar o WP Rocket para"
" servir WebP para você, desative a exibição de WebP em %1$s."
msgstr[2] ""
"Você está usando %1$s para servir imagens WebP e por isso não precisa ativar"
" esta opção. %2$sMais informações%3$s %4$s Se preferir usar o WP Rocket para"
" servir WebP para você, desative a exibição de WebP em %1$s."

#: inc/Addon/WebP/AdminSubscriber.php:105
msgid "WebP cache is disabled by filter."
msgstr "O cache de WebP está desativado por filtro."

#. Translators: %1$s = plugin name(s), %2$s = opening <a> tag, %3$s = closing
#. </a> tag.
#: inc/Addon/WebP/AdminSubscriber.php:115
#: inc/Addon/WebP/AdminSubscriber.php:141
msgid ""
"You are using %1$s to convert images to WebP. If you want WP Rocket to serve"
" them for you, activate this option. %2$sMore info%3$s"
msgid_plural ""
"You are using %1$s to convert images to WebP. If you want WP Rocket to serve"
" them for you, activate this option. %2$sMore info%3$s"
msgstr[0] ""
"Você está usando %1$s para converter imagens para WebP. Se deseja que o WP "
"Rocket sirva elas para você, ative esta opção. %2$sMais informações%3$s"
msgstr[1] ""
"Você está usando %1$s para converter imagens para WebP. Se deseja que o WP "
"Rocket sirva elas para você, ative esta opção. %2$sMais informações%3$s"
msgstr[2] ""
"Você está usando %1$s para converter imagens para WebP. Se deseja que o WP "
"Rocket sirva elas para você, ative esta opção. %2$sMais informações%3$s"

#. Translators: %1$s = plugin name(s), %2$s = opening <a> tag, %3$s = closing
#. </a> tag.
#: inc/Addon/WebP/AdminSubscriber.php:127
#: inc/Addon/WebP/AdminSubscriber.php:153
msgid ""
"You are using %1$s to convert images to WebP. WP Rocket will create separate"
" cache files to serve your WebP images. %2$sMore info%3$s"
msgid_plural ""
"You are using %1$s to convert images to WebP. WP Rocket will create separate"
" cache files to serve your WebP images. %2$sMore info%3$s"
msgstr[0] ""
"Você está usando %1$s para converter imagens para WebP. O WP Rocket irá "
"criar arquivos separados de cache para servir suas imagens WebP. %2$sMais "
"informações%3$s"
msgstr[1] ""
"Você está usando %1$s para converter imagens para WebP. O WP Rocket irá "
"criar arquivos separados de cache para servir suas imagens WebP. %2$sMais "
"informações%3$s"
msgstr[2] ""
"Você está usando %1$s para converter imagens para WebP. O WP Rocket irá "
"criar arquivos separados de cache para servir suas imagens WebP. %2$sMais "
"informações%3$s"

#. Translators: %1$s = opening <a> tag, %2$s = closing </a> tag.
#: inc/Addon/WebP/AdminSubscriber.php:173
msgid ""
"%5$sWe have not detected any compatible WebP plugin!%6$s%4$s If you don’t "
"already have WebP images on your site consider using %3$sImagify%2$s or "
"another supported plugin. %1$sMore info%2$s %4$s If you are not using WebP "
"do not enable this option."
msgstr ""
"%5$sNão detectamos nenhu plugin WebP compatível!%6$s%4$sSe você ainda não "
"tem imagens WebP no seu site, considere usar o %3$sImagify%2$s ou outro "
"plugin suportado. %1$sMais informações%2$s %4$sSe você não estiver usando "
"WebP não ative esta opção."

#: inc/Addon/WebP/AdminSubscriber.php:185
msgid "WP Rocket will create separate cache files to serve your WebP images."
msgstr ""
"O WP Rocket irá criar arquivos de cache separados para servir as suas "
"imagens WebP."

#: inc/admin/admin.php:18 inc/common/admin-bar.php:273
#: inc/deprecated/deprecated.php:1787
msgid "Support"
msgstr "Suporte"

#: inc/admin/admin.php:20
msgid "Docs"
msgstr "Documentação"

#: inc/admin/admin.php:22 inc/common/admin-bar.php:261
msgid "FAQ"
msgstr "Perguntas Frequentes"

#: inc/admin/admin.php:24 inc/common/admin-bar.php:70
msgid "Settings"
msgstr "Configurações"

#: inc/admin/admin.php:96 inc/admin/admin.php:117 inc/deprecated/3.5.php:898
#: inc/Engine/Cache/AdminSubscriber.php:134
msgid "Clear this cache"
msgstr "Limpar este cache"

#: inc/admin/admin.php:402
msgid "Settings import failed: you do not have the permissions to do this."
msgstr ""
"A importação das configurações falhou: você não tem permissões para fazer "
"isso."

#: inc/admin/admin.php:406
msgid "Settings import failed: no file uploaded."
msgstr "A importação das configurações falhou: nenhum arquivo foi enviado."

#: inc/admin/admin.php:410
msgid "Settings import failed: incorrect filename."
msgstr "A importação das configurações falhou: nome incorreto de arquivo."

#: inc/admin/admin.php:421
msgid "Settings import failed: incorrect filetype."
msgstr "A importação das configurações falhou: tipo de arquivo incorreto."

#: inc/admin/admin.php:431
msgid "Settings import failed: "
msgstr "A importação das configurações falhou:"

#: inc/admin/admin.php:447
msgid "Settings import failed: unexpected file content."
msgstr ""
"A importação das configurações falhou: arquivo com conteúdo inesperado."

#: inc/admin/admin.php:492
msgid "Settings imported and saved."
msgstr "Configurações importadas e salvas."

#: inc/admin/options.php:102 inc/Engine/Admin/Settings/Page.php:571
msgid "Excluded CSS Files"
msgstr "Arquivos CSS Excluídos"

#: inc/admin/options.php:103 inc/Engine/Admin/Settings/Page.php:725
msgid "Excluded Inline JavaScript"
msgstr "JavaScript Inline Excluído"

#: inc/admin/options.php:104 inc/Engine/Admin/Settings/Page.php:743
#: inc/Engine/Admin/Settings/Page.php:776
#: inc/Engine/Admin/Settings/Page.php:823
msgid "Excluded JavaScript Files"
msgstr "Arquivos JavaScript Excluídos"

#: inc/admin/options.php:105
msgid "Defer JavaScript Files"
msgstr "Adiar arquivos JavaScript"

#: inc/admin/options.php:106
msgid "Excluded Delay JavaScript Files"
msgstr "Arquivos JavaScript excluídos do retraso"

#: inc/admin/options.php:107 inc/Engine/Admin/Settings/Page.php:1229
msgid "Never Cache URL(s)"
msgstr "URL(s) Jamais em Cache"

#: inc/admin/options.php:108 inc/Engine/Admin/Settings/Page.php:1243
msgid "Never Cache User Agent(s)"
msgstr "User Agent(s) Jamais em Cache"

#: inc/admin/options.php:109 inc/Engine/Admin/Settings/Page.php:1249
msgid "Always Purge URL(s)"
msgstr "Sempre Esvaziar URL(s)"

#: inc/admin/options.php:110 inc/Engine/Admin/Settings/Page.php:1530
msgid "Exclude files from CDN"
msgstr "Excluir arquivos da CDN"

#. translators: 1 and 2 can be anything.
#: inc/admin/options.php:131
msgid "%1$s: <em>%2$s</em>"
msgstr "%1$s: <em>%2$s</em>"

#: inc/admin/options.php:141
msgid "The following pattern is invalid and has been removed:"
msgid_plural "The following patterns are invalid and have been removed:"
msgstr[0] "O seguinte padrão é inválido e foi removido:"
msgstr[1] "Os seguintes padrões são inválidos e foram removidos:"
msgstr[2] "Os seguintes padrões são inválidos e foram removidos:"

#: inc/admin/options.php:157
msgid "More info"
msgstr "Mais informações"

#: inc/admin/ui/meta-boxes.php:37 inc/admin/ui/notices.php:671
#: inc/common/admin-bar.php:91 inc/common/admin-bar.php:154
#: views/settings/page-sections/dashboard.php:150
msgid "Clear cache"
msgstr "Limpar o cache"

#. translators: %1$s WP Rocket plugin name; %2$s = file name.
#: inc/admin/ui/notices.php:30 inc/admin/ui/notices.php:43
msgid ""
"<strong>%1$s</strong> has not been deactivated due to missing writing permissions.<br>\n"
"Make <strong>%2$s</strong> writeable and retry deactivation, or force deactivation now:"
msgstr ""
"<strong>%1$s</strong> não foi desativado por falta de permissões de escrita.<br>\n"
"Ative a permissão de escrita para <strong>%2$s</strong> e tente novamente, ou forçe a desativação agora:"

#. translators: %s is WP Rocket plugin name.
#: inc/admin/ui/notices.php:97
msgid ""
"<strong>%s</strong>: One or more plugins have been enabled or disabled, "
"clear the cache if they affect the front end of your site."
msgstr ""
"<strong>%s</strong>: Um ou mais plugins foram ativados ou desativados, limpe"
" o cache se eles afetam a interface do seu site."

#. translators: %s is WP Rocket plugin name.
#: inc/admin/ui/notices.php:141
msgid ""
"<strong>%s</strong>: The following plugins are not compatible with this "
"plugin and may cause unexpected results:"
msgstr ""
"<strong>%s</strong>: Os plugins a seguir não são compatíveis com este plugin"
" e podem causar resultados inesperados:"

#: inc/admin/ui/notices.php:147
msgid "Deactivate"
msgstr "Desativar"

#: inc/admin/ui/notices.php:189
msgid ""
"WP Rocket Footer JS is not an official add-on. It prevents some options in "
"WP Rocket from working correctly. Please deactivate it if you have problems."
msgstr ""
"WP Rocket Footer JS não é um complemento oficial. Ele impede algumas opções "
"no WP Rocket de funcionarem corretamente. Por favor desative-o se tiver "
"problemas."

#. translators: %1$s = opening link tag, %2$s = closing link tag.
#: inc/admin/ui/notices.php:229
msgid ""
"Endurance Cache is currently enabled, which will conflict with WP Rocket "
"Cache. Please set the Endurance Cache cache level to Off (Level 0) on the "
"%1$sSettings > General%2$s page to prevent any issues."
msgstr ""
"O Cache Endurance está ativo no momento, e pode entrar em conflito com o WP "
"Rocket Cache. Por favor defina o nível de cache do Endurance Cache para "
"Inativo (Level 0) na página %1$sConfigurações > Geral%2$s para evitar "
"quaisquer problemas."

#. translators: %1$s WP Rocket plugin name; %2$s = opening link; %3$s =
#. closing link
#: inc/admin/ui/notices.php:250
msgid ""
"%1$s: A custom permalink structure is required for the plugin to work "
"properly. %2$sGo to permalinks settings%3$s"
msgstr ""
"%1$s: É obrigatória uma estrutura personalizada de links permanentes para o "
"plugin funcionar corretamente. %2$sVá para as configurações de links "
"permanentes%3$s"

#. translators: %s = plugin name.
#: inc/admin/ui/notices.php:297
msgid ""
"%s could not modify the .htaccess file due to missing writing permissions."
msgstr ""
"%s não pôde alterar o arquivo .htaccess devido à falta de permissões de "
"escrita."

#. translators: This is a doc title! %1$s = opening link; %2$s = closing link
#: inc/admin/ui/notices.php:303 inc/admin/ui/notices.php:788
msgid "Troubleshoot: %1$sHow to make system files writeable%2$s"
msgstr ""
"Solução de Problemas: %1$sComo ativar a permissão escrita nos arquivos do "
"sistema%2$s"

#. translators: Documentation exists in EN, DE, FR, ES, IT; use loaclised URL
#. if applicable
#: inc/admin/ui/notices.php:305 inc/admin/ui/notices.php:790
msgid ""
"https://docs.wp-rocket.me/article/626-how-to-make-system-files-htaccess-wp-"
"config-writeable/?utm_source=wp_plugin&utm_medium=wp_rocket"
msgstr ""
"https://docs.wp-rocket.me/article/626-how-to-make-system-files-htaccess-wp-"
"config-writeable/?utm_source=wp_plugin&utm_medium=wp_rocket"

#: inc/admin/ui/notices.php:311
msgid ""
"Don’t worry, WP Rocket’s page caching and settings will still function "
"correctly."
msgstr ""
"Não se preocupe, o cache de páginas e as configurações do WP Rocket vão "
"continuar funcionando corretamente."

#: inc/admin/ui/notices.php:311
msgid ""
"For optimal performance, adding the following lines into your .htaccess is "
"recommended (not required):"
msgstr ""
"Para um ótimo desempenho, é recomendado adicionar as linhas a seguir ao seu "
"arquivo .htaccess (não obrigatório):"

#. translators: %1$s = plugin name, %2$s + %3$s = opening links, %4$s =
#. closing link
#: inc/admin/ui/notices.php:458
msgid ""
"%1$s is good to go! %2$sTest your load time%4$s, or visit your "
"%3$ssettings%4$s."
msgstr ""
"%1$s está pronto! %2$sTeste o seu tempo de carregamento%4$s ou visite as "
"suas %3$sconfigurações%4$s."

#: inc/admin/ui/notices.php:499
msgid ""
"Would you allow WP Rocket to collect non-sensitive diagnostic data from this"
" website?"
msgstr ""
"Você permite que o WP Rocket colete dados não-sensíveis de diagnóstico deste"
" site?"

#: inc/admin/ui/notices.php:500
msgid "This would help us to improve WP Rocket for you in the future."
msgstr "Isto pode nos ajudar a melhorar o WP Rocket para você no futuro."

#. translators: button text, click will expand data collection preview
#: inc/admin/ui/notices.php:506
msgid "What info will we collect?"
msgstr "Que informações iremos coletar?"

#: inc/admin/ui/notices.php:511
msgid ""
"Below is a detailed view of all data WP Rocket will collect if granted "
"permission. WP Rocket will never transmit any domain names or email "
"addresses (except for license validation), IP addresses, or third-party API "
"keys."
msgstr ""
"Abaixo está uma visão detalhada de todos os dados que WP Rocket irá coletar "
"se você der permissão. O WP Rocket nunca irá transmitir quaisquer nomes de "
"domínio ou endereços de e-mail (exceto para validação de licença), endereços"
" IP ou chaves de API de terceiros."

#. translators: button text for data collection opt-in
#: inc/admin/ui/notices.php:520
msgid "Yes, allow"
msgstr "Sim, permitir"

#. translators: button text for data collection opt-in
#: inc/admin/ui/notices.php:523
msgid "No, thanks"
msgstr "Não, obrigado"

#: inc/admin/ui/notices.php:562
msgid "Thank you!"
msgstr "Obrigado!"

#: inc/admin/ui/notices.php:567
msgid "WP Rocket now collects these metrics from your website:"
msgstr "O WP Rocket agora coleta estas métricas do seu website:"

#. translators: %s = plugin name.
#: inc/admin/ui/notices.php:605
msgid "%s: Cache cleared."
msgstr "%s: Cache limpo."

#. translators: %s = plugin name.
#: inc/admin/ui/notices.php:612
msgid "%s: Post cache cleared."
msgstr "%s: Cache do post limpo."

#. translators: %s = plugin name.
#: inc/admin/ui/notices.php:619
msgid "%s: Term cache cleared."
msgstr "%s: Cache de termos limpo."

#. translators: %s = plugin name).
#: inc/admin/ui/notices.php:626
msgid "%s: User cache cleared."
msgstr "%s: Cache de usuário limpo."

#: inc/admin/ui/notices.php:662 inc/Engine/License/views/promo-banner.php:36
#: inc/Engine/License/views/renewal-expired-banner-ocd-disabled.php:32
#: inc/Engine/License/views/renewal-expired-banner-ocd.php:42
#: inc/Engine/License/views/renewal-expired-banner.php:30
#: views/settings/page-sections/dashboard.php:46
msgid "Dismiss this notice"
msgstr "Descartar essa notificação"

#: inc/admin/ui/notices.php:682 inc/Engine/Saas/Admin/AdminBar.php:80
#: inc/Engine/Saas/Admin/AdminBar.php:198
msgid "Clear Used CSS"
msgstr "Limpar CSS usado"

#: inc/admin/ui/notices.php:685
msgid "Stop Preload"
msgstr "Parar o Pré-carregamento"

#: inc/admin/ui/notices.php:691
msgid "Turn on Remove Unused CSS"
msgstr "Ativar Remoção do CSS Não Usado"

#: inc/admin/ui/notices.php:697
msgid "Enable “Separate Cache Files for Mobile Devices” now"
msgstr "Ativar \"Arquivos Separados de Cache para Dispositivos Móveis\" agora"

#: inc/admin/ui/notices.php:715
msgid "Force deactivation "
msgstr "Forçar desativação "

#: inc/admin/ui/notices.php:745
msgid "The following code should have been written to this file:"
msgstr "O código a seguir deveria ter sido escrito neste arquivo:"

#. translators: %s = plugin name.
#: inc/admin/ui/notices.php:776
msgid "%s cannot configure itself due to missing writing permissions."
msgstr ""
"%s não pode se auto-configurar devido à falta de permissões de escrita."

#. translators: %s = file/folder name
#: inc/admin/ui/notices.php:782
msgid "Affected file/folder: %s"
msgstr "Arquivo/pasta afetado: %s"

#: inc/classes/admin/class-logs.php:124
msgid "The debug file could not be deleted."
msgstr "Não foi possível excluir o arquivo de depuração."

#. Translators: %1$s = Plugin name, %2$s = Plugin version.
#: inc/classes/class-wp-rocket-requirements-check.php:147
msgid "To function properly, %1$s %2$s requires at least:"
msgstr "Para funcionar corretamente o %1$s %2$s requer ao menos:"

#. Translators: %1$s = PHP version required.
#: inc/classes/class-wp-rocket-requirements-check.php:151
msgid ""
"PHP %1$s. To use this WP Rocket version, please ask your web host how to "
"upgrade your server to PHP %1$s or higher."
msgstr ""
"PHP %1$s. Para usar esta versão do WP Rocket, pergunte ao seu provedor como "
"atualizar o seu servidor para o PHP %1$s ou acima."

#. Translators: %1$s = WordPress version required.
#: inc/classes/class-wp-rocket-requirements-check.php:156
msgid ""
"WordPress %1$s. To use this WP Rocket version, please upgrade WordPress to "
"version %1$s or higher."
msgstr ""
"WordPress %1$s. Para usar esta versão do WP Rocket, por favor atualize seu "
"WordPress para a versão %1$s ou acima."

#: inc/classes/class-wp-rocket-requirements-check.php:159
msgid ""
"If you are not able to upgrade, you can rollback to the previous version by "
"using the button below."
msgstr ""
"Se não puder atualizar, você ainda pode voltar para a versão anterior usando"
" o botão abaixo."

#. Translators: %s = Previous plugin version.
#: inc/classes/class-wp-rocket-requirements-check.php:161
#: inc/deprecated/deprecated.php:1947
msgid "Re-install version %s"
msgstr "Reinstalar a versão %s"

#. translators: %s is the plugin name.
#: inc/classes/class-wp-rocket-requirements-check.php:203
#: inc/classes/class-wp-rocket-requirements-check.php:214
#: inc/deprecated/3.11.php:236 inc/deprecated/3.11.php:251
#: inc/Engine/Plugin/UpdaterSubscriber.php:483
#: inc/Engine/Plugin/UpdaterSubscriber.php:497
msgid "%s Update Rollback"
msgstr "Reversão da Atualização %s"

#. translators: %1$s = missing tags;
#: inc/classes/subscriber/Tools/class-detect-missing-tags-subscriber.php:151
msgid ""
"Failed to detect the following requirement in your theme: closing %1$s."
msgid_plural ""
"Failed to detect the following requirements in your theme: closing %1$s."
msgstr[0] "Falha ao detectar o seguinte requisito no seu tema: fechando %1$s."
msgstr[1] ""
"Falha ao detectar os seguintes requisitos no seu tema: fechando %1$s."
msgstr[2] ""
"Falha ao detectar os seguintes requisitos no seu tema: fechando %1$s."

#. translators: Documentation exists in EN, FR; use localized URL if
#. applicable.
#: inc/classes/subscriber/Tools/class-detect-missing-tags-subscriber.php:159
msgid ""
"https://docs.wp-rocket.me/article/99-pages-not-cached-or-minify-cssjs-not-"
"working/?utm_source=wp_plugin&utm_medium=wp_rocket#theme"
msgstr ""
"https://docs.wp-rocket.me/article/99-pages-not-cached-or-minify-cssjs-not-"
"working/?utm_source=wp_plugin&utm_medium=wp_rocket#theme"

#: inc/common/admin-bar.php:91 inc/common/admin-bar.php:154
#: views/settings/page-sections/dashboard.php:150
msgid "Clear and preload cache"
msgstr "Limpar e pré-carregar o cache"

#: inc/common/admin-bar.php:131 inc/functions/i18n.php:20
msgid "All languages"
msgstr "Todos os idiomas"

#: inc/common/admin-bar.php:178
msgid "Clear this post"
msgstr "Limpar este Post"

#: inc/common/admin-bar.php:192
msgid "Purge this URL"
msgstr "Esvaziar este URL"

#: inc/common/admin-bar.php:212
msgid "Purge Sucuri cache"
msgstr "Limpar cache Sucuri"

#: inc/common/admin-bar.php:236 views/settings/fields/rocket-cdn.php:55
msgid "Purge RocketCDN cache"
msgstr "Esvaziar o cache da RocketCDN"

#: inc/common/admin-bar.php:249 views/settings/partials/documentation.php:14
msgid "Documentation"
msgstr "Documentação"

#: inc/deprecated/3.2.php:52 views/settings/page-sections/imagify.php:36
msgid "Activate Imagify"
msgstr "Ativar o Imagify"

#: inc/deprecated/3.2.php:52
msgid "Install Imagify for Free"
msgstr "Instale Gratuitamente o Imagify"

#: inc/deprecated/3.2.php:67
msgid ""
"Speed up your website and boost your SEO by reducing image file sizes "
"without losing quality with Imagify."
msgstr ""
"Acelere o seu site e turbine o seu SEO reduzindo o tamanho dos arquivos das "
"imagem sem perder qualidade com o Imagify."

#: inc/deprecated/3.2.php:72
msgid "More details"
msgstr "Mais detalhes"

#. translators: %d is the number of pages preloaded.
#: inc/deprecated/3.2.php:228
msgid "Sitemap preload: %d pages have been cached."
msgstr "Pré-carregamento do sitemap: %d páginas foram armazenadas em cache."

#. translators: %d = Number of pages preloaded.
#: inc/deprecated/3.2.php:261
msgid ""
"Sitemap preload: %d uncached pages have now been preloaded. (refresh to see "
"progress)"
msgstr ""
"Pré-carregamento do sitemap: %d páginas sem cache foram pré-carregadas. "
"(atualize a página para ver o progresso)"

#. translators: %s is an URL.
#: inc/deprecated/3.3.php:761 inc/deprecated/3.3.php:770
#: inc/Engine/Plugin/UpdaterApiTools.php:32
msgid ""
"An unexpected error occurred. Something may be wrong with WP-Rocket.me or "
"this server&#8217;s configuration. If you continue to have problems, <a "
"href=\"%s\">contact support</a>."
msgstr ""
"Ocorreu um erro inesperado. Algo pode estar errado com o WP-Rocket.me ou nas"
" configurações deste servidor. Se continuar a ter problemas <a "
"href=“%s”>contate o suporte</a>."

#: inc/deprecated/3.4.php:18
msgid "Choose a domain from the list"
msgstr "Escolha um domínio da lista"

#: inc/deprecated/3.4.php:31
msgid "No domain available in your Cloudflare account"
msgstr "Nenhum domínio disponível na sua conta do Cloudflare"

#: inc/deprecated/3.5.php:71 inc/deprecated/3.5.php:195
msgid ""
"Curl is disabled on your server. Please ask your host to enable it. This is "
"required for the Cloudflare Add-on to work correctly."
msgstr ""
"O Curl está desativado no seu servidor. Peça ao seu provedor para ativá-lo. "
"Isso é necessário para que o complemento do Cloudflare funcione "
"corretamente."

#. translators: %1$s = opening link; %2$s = closing link
#: inc/deprecated/3.5.php:79
msgid ""
"Cloudflare email, API key and Zone ID are not set. Read the "
"%1$sdocumentation%2$s for further guidance."
msgstr ""
"O e-mail, chave da API e ID da Zona não foram definidos. Leia a "
"%1$sdocumentação%2$spara mais assistência."

#. translators: %1$s = opening link; %2$s = closing link
#: inc/deprecated/3.5.php:206
msgid ""
"Cloudflare email and API key are not set. Read the %1$sdocumentation%2$s for"
" further guidance."
msgstr ""
"O e-mail e a chave da API do Cloudflare nào estão definidos. Leia a "
"%1$sdocumentação%2$s para mais assistência."

#: inc/deprecated/3.5.php:271
msgid "Connection to Cloudflare failed"
msgstr "Falha na conexão com o Cloudflare"

#. translators: %s = CloudFare API return message.
#: inc/deprecated/3.5.php:587
msgid "<strong>WP Rocket:</strong> %s"
msgstr "<strong>WP Rocket:</strong> %s"

#: inc/deprecated/3.5.php:592
msgid "<strong>WP Rocket:</strong> Cloudflare cache successfully purged."
msgstr ""
"<strong>WP Rocket:</strong> O cache do Cloudflare foi limpo com sucesso."

#: inc/deprecated/3.5.php:858 inc/Engine/HealthCheck/HealthCheck.php:81
msgid ""
"The following scheduled event failed to run. This may indicate the CRON "
"system is not running properly, which can prevent some WP Rocket features "
"from working as intended:"
msgid_plural ""
"The following scheduled events failed to run. This may indicate the CRON "
"system is not running properly, which can prevent some WP Rocket features "
"from working as intended:"
msgstr[0] ""
"O seguinte evento agendado falhou em ser executado. Isso pode indicar que o "
"sistema CRON não está funcionando corretamente, o que pode impedir algumas "
"funções do WP Rocket de funcionarem como esperado:"
msgstr[1] ""
"Os seguintes eventos agendados falharam em ser executados. Isso pode indicar"
" que o sistema CRON não está funcionando corretamente, o que pode impedir "
"algumas funções do WP Rocket de funcionarem como esperado:"
msgstr[2] ""
"Os seguintes eventos agendados falharam em ser executados. Isso pode indicar"
" que o sistema CRON não está funcionando corretamente, o que pode impedir "
"algumas funções do WP Rocket de funcionarem como esperado:"

#: inc/deprecated/3.5.php:867 inc/Engine/HealthCheck/HealthCheck.php:88
msgid "Please contact your host to check if CRON is working."
msgstr ""
"Entre em contato com o seu host para verificar se o CRON está funcionando."

#: inc/deprecated/3.10.php:163
msgid "OPcache purge failed."
msgstr "A limpeza do OPcache falhou"

#: inc/deprecated/3.10.php:168
msgid "OPcache successfully purged"
msgstr "O OPcache foi esvaziado com sucesso"

#: inc/deprecated/3.11.php:81
msgid "Yoast SEO XML sitemap"
msgstr "Sitemap XML do Yoast SEO"

#. translators: %s = Name of the plugin.
#. translators: %s = plugin name, e.g. Yoast SEO.
#: inc/deprecated/3.11.php:83 inc/deprecated/3.12.php:58
#: inc/deprecated/3.12.php:87 inc/deprecated/3.12.php:186
#: inc/deprecated/3.12.php:253 inc/deprecated/3.12.php:342
msgid ""
"We automatically detected the sitemap generated by the %s plugin. You can "
"check the option to preload it."
msgstr ""
"Detectamos automaticamente o sitemap gerado pelo plugin %s. Você pode marcar"
" a opção para pré-carregá-lo."

#. translators: 1 and 3 are link openings, 2 is a link closing.
#: inc/deprecated/3.11.php:279 inc/Engine/Plugin/UpdaterSubscriber.php:520
msgid "%1$sReturn to WP Rocket%2$s or %3$sgo to Plugins page%2$s"
msgstr "%1$sRetorne ao%2$sdo WP Rocket ou %3$svá para a página de Plugins%2$s"

#: inc/deprecated/3.12.php:56
msgid "All in One SEO XML sitemap"
msgstr "Sitemap XML do All in One SEO"

#: inc/deprecated/3.12.php:85
msgid "Rank Math XML sitemap"
msgstr "Sitemap XML do Rank Math"

#: inc/deprecated/3.12.php:184
msgid "SEOPress XML sitemap"
msgstr "Sitemap XML do SEOPress"

#: inc/deprecated/3.12.php:251
msgid "The SEO Framework XML sitemap"
msgstr "Sitemap XML do The SEO Framework"

#: inc/deprecated/3.12.php:332
msgid "Jetpack XML Sitemaps"
msgstr "Sitemaps XML do Jetpack"

#: inc/deprecated/3.12.php:334
msgid "Preload the sitemap from the Jetpack plugin"
msgstr "Pré-carregar o sitemap do plugin Jetpack"

#: inc/deprecated/3.15.php:28
#: inc/Engine/Admin/Metaboxes/PostEditOptionsSubscriber.php:74
msgid "WP Rocket Options"
msgstr "Opções do WP Rocket"

#: inc/deprecated/3.15.php:57 views/metaboxes/post_edit_options.php:19
msgid "Never cache this page"
msgstr "Nunca armazenar esta página em cache"

#: inc/deprecated/3.15.php:61 views/metaboxes/post_edit_options.php:23
msgid "Activate these options on this post:"
msgstr "Ativar estas opções neste post:"

#. translators: %s is the name of the option.
#: inc/deprecated/3.15.php:81
#: inc/Engine/Admin/Metaboxes/PostEditOptionsSubscriber.php:121
msgid "Activate first the %s option."
msgstr "Ative primeiro a opção %s."

#. translators: %1$s = opening strong tag, %2$s = closing strong tag.
#: inc/deprecated/3.15.php:97 views/metaboxes/post_edit_options.php:38
msgid ""
"%1$sNote:%2$s None of these options will be applied if this post has been "
"excluded from cache in the global cache settings."
msgstr ""
"%1$sNota:%2$s Nenhuma destas opções será aplicada se este post tiver sido "
"excluido do cache nas configurações globais de cache."

#: inc/deprecated/deprecated.php:240
msgid "<strong>JS</strong> files with Deferred Loading JavaScript"
msgstr "Arquivos <strong>JS</strong> com Carregamento Adiado de JavaScript"

#: inc/deprecated/deprecated.php:293
msgid "Add URL"
msgstr "Adicionar URL"

#: inc/deprecated/deprecated.php:1245
#: inc/Engine/Admin/Settings/Settings.php:451
msgid "Settings saved."
msgstr "Configurações salvas."

#: inc/deprecated/deprecated.php:1277 views/settings/fields/import-form.php:22
msgid ""
"Before you can upload your import file, you will need to fix the following "
"error:"
msgstr ""
"Antes de poder enviar seu arquivo de importação você precisa corrigir o "
"seguinte erro:"

#. translators: %s is the maximum upload size set on the current server.
#: inc/deprecated/deprecated.php:1288 views/settings/fields/import-form.php:35
msgid "Choose a file from your computer (maximum size: %s)"
msgstr "Escolha um arquivo do seu computador (tamanho máximo: %s)"

#: inc/deprecated/deprecated.php:1294 inc/Engine/Admin/Settings/Render.php:422
msgid "Upload file and import settings"
msgstr "Enviar e importar configurações"

#: inc/deprecated/deprecated.php:1385
msgid "Your Cloudflare credentials are valid."
msgstr "Suas credenciais do Cloudflare são válidas."

#: inc/deprecated/deprecated.php:1389
msgid "Your Cloudflare credentials are invalid!"
msgstr "Suas credenciais do Cloudflare são inválidas!"

#: inc/deprecated/deprecated.php:1453
msgid "Save and optimize"
msgstr "Salvar e otimizar"

#: inc/deprecated/deprecated.php:1453
msgid "Optimize"
msgstr "Otimizar"

#: inc/deprecated/deprecated.php:1464 inc/deprecated/deprecated.php:1666
msgctxt "screen-reader-text"
msgid "Note:"
msgstr "Nota:"

#: inc/deprecated/deprecated.php:1470
msgctxt "screen-reader-text"
msgid "Performance tip:"
msgstr "Dica de desempenho:"

#: inc/deprecated/deprecated.php:1476
msgctxt "screen-reader-text"
msgid "Third-party feature detected:"
msgstr "Recurso de terceiro detectado:"

#: inc/deprecated/deprecated.php:1488 inc/deprecated/deprecated.php:1672
msgctxt "screen-reader-text"
msgid "Warning:"
msgstr "Alerta:"

#: inc/deprecated/deprecated.php:1501
msgctxt "button text"
msgid "Download settings"
msgstr "Baixar as configurações"

#: inc/deprecated/deprecated.php:1531
msgid "Replace site's hostname with:"
msgstr "Substituir o nome do host do site por:"

#: inc/deprecated/deprecated.php:1550 inc/deprecated/deprecated.php:1587
#: inc/deprecated/deprecated.php:1615 views/settings/fields/cnames.php:41
#: views/settings/fields/cnames.php:75 views/settings/fields/cnames.php:105
msgid "reserved for"
msgstr "reservado para"

#: inc/deprecated/deprecated.php:1552 inc/deprecated/deprecated.php:1589
#: inc/deprecated/deprecated.php:1617 views/settings/fields/cnames.php:44
#: views/settings/fields/cnames.php:78 views/settings/fields/cnames.php:108
msgid "All files"
msgstr "Todos os arquivos"

#: inc/deprecated/deprecated.php:1564 inc/deprecated/deprecated.php:1594
#: inc/deprecated/deprecated.php:1622 views/settings/fields/cnames.php:56
#: views/settings/fields/cnames.php:90 views/settings/fields/cnames.php:120
msgid "Images"
msgstr "Imagens"

#: inc/deprecated/deprecated.php:1635 views/settings/fields/cnames.php:132
msgid "Add CNAME"
msgstr "Adicionar CNAME"

#: inc/deprecated/deprecated.php:1706
msgid "Watch the video"
msgstr "Assista ao vídeo"

#: inc/deprecated/deprecated.php:1771
msgid "Basic"
msgstr "Básico"

#: inc/deprecated/deprecated.php:1772
msgid "Static Files"
msgstr "Arquivos Estáticos"

#: inc/deprecated/deprecated.php:1773 inc/Engine/Admin/Settings/Page.php:1500
#: inc/Engine/Admin/Settings/Page.php:1511
#: inc/Engine/CDN/Admin/Subscriber.php:28
msgid "CDN"
msgstr "CDN"

#: inc/deprecated/deprecated.php:1774
msgid "Advanced"
msgstr "Avançado"

#: inc/deprecated/deprecated.php:1775 inc/Engine/Admin/Settings/Page.php:1345
msgid "Database"
msgstr "Banco de Dados"

#: inc/deprecated/deprecated.php:1776 inc/Engine/Admin/Settings/Page.php:1051
msgid "Preload"
msgstr "Pré-Carregar"

#: inc/deprecated/deprecated.php:1786
#: inc/Engine/Admin/Settings/Subscriber.php:170
#: views/settings/page-sections/tools.php:33
msgid "Tools"
msgstr "Ferramentas"

#: inc/deprecated/deprecated.php:1789 inc/Engine/Admin/Settings/Page.php:357
#: views/settings/page-sections/dashboard.php:80
msgid "License"
msgstr "Licença"

#. Translators: %1$s = Plugin name, %2$s = Plugin version, %3$s = PHP version
#. required.
#: inc/deprecated/deprecated.php:1944
msgid ""
"%1$s %2$s requires at least PHP %3$s to function properly. To use this "
"version, please ask your web host how to upgrade your server to PHP %3$s or "
"higher. If you are not able to upgrade, you can rollback to the previous "
"version by using the button below."
msgstr ""
"%1$s %2$s requer pelo menos o PHP %3$s para funcionar corretamente. Para "
"usar esta versão, por favor pergunte ao seu provedor de hospedagem como "
"atualizar o seu servidor para o PHP %3$s ou acima. Se você não é capaz de "
"atualizar, pode reverter o plugin para a versão anterior usando o botão "
"abaixo."

#. translators: 1: PHP class name, 2: version number, 3: replacement class
#. name.
#: inc/deprecated/DeprecatedClassTrait.php:54
msgid ""
"The called class %1$s is <strong>deprecated</strong> since version %2$s! Use"
" %3$s instead."
msgstr ""
"A classe solicitda %1$s está <strong>obsolet</strong> desde a versão %2$s! "
"Use %3$s ao invés dela."

#. translators: 1: PHP class name, 2: version number.
#: inc/deprecated/DeprecatedClassTrait.php:65
msgid ""
"The called class %1$s is <strong>deprecated</strong> since version %2$s!"
msgstr ""
"A classe solicitada %1$s está <strong>obsoleta</strong> desde a versão %2$s!"

#: inc/deprecated/Engine/Addon/FacebookTracking/Subscriber.php:92
#: inc/deprecated/Engine/Addon/GoogleTracking/Subscriber.php:137
#: inc/Engine/Admin/Database/Subscriber.php:79
msgid "weekly"
msgstr "semanalmente"

#: inc/Engine/Admin/Database/Optimization.php:30
#: inc/Engine/Admin/Settings/Page.php:1389
msgid "Revisions"
msgstr "Revisões"

#: inc/Engine/Admin/Database/Optimization.php:31
#: inc/Engine/Admin/Settings/Page.php:1399
msgid "Auto Drafts"
msgstr "Rascunhos Automáticos"

#: inc/Engine/Admin/Database/Optimization.php:32
#: inc/Engine/Admin/Settings/Page.php:1409
msgid "Trashed Posts"
msgstr "Posts na Lixeira"

#: inc/Engine/Admin/Database/Optimization.php:33
#: inc/Engine/Admin/Settings/Page.php:1419
msgid "Spam Comments"
msgstr "Comentários Spam"

#: inc/Engine/Admin/Database/Optimization.php:34
#: inc/Engine/Admin/Settings/Page.php:1429
msgid "Trashed Comments"
msgstr "Comentários na Lixeira"

#: inc/Engine/Admin/Database/Optimization.php:35
msgid "Transients"
msgstr "Transientes"

#: inc/Engine/Admin/Database/Optimization.php:36
msgid "Tables"
msgstr "Tabelas"

#: inc/Engine/Admin/Database/Subscriber.php:85
msgid "monthly"
msgstr "mensalmente"

#: inc/Engine/Admin/Database/Subscriber.php:194
msgid "Database optimization process is running"
msgstr "O processo de otimização do banco de dados está sendo executado"

#: inc/Engine/Admin/Database/Subscriber.php:224
msgid ""
"Database optimization process is complete. Everything was already optimized!"
msgstr ""
"O processo de otimização do banco de dados está concluído. Tudo já foi "
"otimizado!"

#: inc/Engine/Admin/Database/Subscriber.php:227
msgid ""
"Database optimization process is complete. List of optimized items below:"
msgstr ""
"O processo de otimização de banco de dados está concluído. Abaixo a lista "
"dos itens otimizados:"

#. translators: %1$d = number of items optimized, %2$s = type of optimization
#: inc/Engine/Admin/Database/Subscriber.php:235
msgid "%1$d %2$s optimized."
msgstr "%1$d %2$s otimizado."

#. translators: %1$s = <strong>, %2$s = </strong>, %3$s = <a>, %4$s = </a>.
#: inc/Engine/Admin/DomainChange/Subscriber.php:143
msgid ""
"%1$sWP Rocket:%2$s We detected that the website domain has changed. The "
"configuration files must be regenerated for the page cache and all other "
"optimizations to work as intended. %3$sLearn More%4$s"
msgstr ""
"%1$sWP Rocket:%2$s Detectamos que o domínio do site foi alterado. Os "
"arquivos de configuração precisam ser regenerados para que o cache de "
"páginas e todas as outras otimizações funcionem como esperado. %3$sSaiba "
"mais%4$s"

#: inc/Engine/Admin/DomainChange/Subscriber.php:171
msgid "Regenerate WP Rocket configuration files now"
msgstr "Regenerar os arquivos de configuração do WP Rocket agora"

#: inc/Engine/Admin/Settings/Page.php:223
msgid "Save Changes"
msgstr "Salvar Alterações"

#: inc/Engine/Admin/Settings/Page.php:223
msgid "Validate License"
msgstr "Validar a licença"

#: inc/Engine/Admin/Settings/Page.php:279
#: inc/Engine/Admin/Settings/Page.php:280 inc/functions/admin.php:550
msgid "Unavailable"
msgstr "Indisponível"

#: inc/Engine/Admin/Settings/Page.php:374
msgid "API key"
msgstr "Chave da API"

#: inc/Engine/Admin/Settings/Page.php:389
msgid "Email address"
msgstr "Endereço de e-mail"

#: inc/Engine/Admin/Settings/Page.php:415
msgid "Dashboard"
msgstr "Painel"

#: inc/Engine/Admin/Settings/Page.php:416
msgid "Get help, account info"
msgstr "Obter ajuda, informações da conta"

#: inc/Engine/Admin/Settings/Page.php:425
msgid "My Status"
msgstr "Meu Status"

#: inc/Engine/Admin/Settings/Page.php:435 views/settings/page.php:75
msgid "Rocket Analytics"
msgstr "Analytics do Rocket"

#. translators: %1$s = opening <a> tag, %2$s = closing </a> tag.
#: inc/Engine/Admin/Settings/Page.php:437
msgid ""
"I agree to share anonymous data with the development team to help improve WP"
" Rocket. %1$sWhat info will we collect?%2$s"
msgstr ""
"Concordo em compartilhar informações anônimas com a equipe de "
"desenvolvimento para ajudar a melhorar o WP Rocket. %1$sQuais informações "
"iremos coletar?%2$s"

#: inc/Engine/Admin/Settings/Page.php:489
msgid "File Optimization"
msgstr "Otimizar Arquivos"

#: inc/Engine/Admin/Settings/Page.php:490
msgid "Optimize CSS & JS"
msgstr "Otimizar CSS e JS"

#. translators: %1$s = type of minification (HTML, CSS or JS), %2$s = “WP
#. Rocket”.
#: inc/Engine/Admin/Settings/Page.php:498
msgid ""
"%1$s Minification is currently activated in <strong>Autoptimize</strong>. If"
" you want to use %2$s’s minification, disable this option in Autoptimize."
msgstr ""
"%1$s A Minificação está ativa no momento no <strong>Autoptimize</strong>. Se"
" deseja usar a minificação do , desative esta opção no Autoptimize.%2$s"

#: inc/Engine/Admin/Settings/Page.php:508
msgid "CSS Files"
msgstr "Arquivos CSS"

#: inc/Engine/Admin/Settings/Page.php:517
msgid "JavaScript Files"
msgstr "Arquivos JavaScript"

#. translators: %1$s = type of minification (HTML, CSS or JS), %2$s = “WP
#. Rocket”.
#: inc/Engine/Admin/Settings/Page.php:524
msgid ""
"%1$s Minification is currently activated in <strong>Autoptimize</strong>. If"
" you want to use %2$s’s minification, disable those options in Autoptimize."
msgstr ""
"No momento a Minificação %1$s está ativada no <strong>Autoptimize</strong>. "
"Se deseja usar a minificação do %2$s, desabilite esta opção no Autoptimize."

#: inc/Engine/Admin/Settings/Page.php:529
msgid ""
"If you have problems after activating this option, copy and paste the "
"default exclusions to quickly resolve issues:"
msgstr ""
"Se tiver problemas após ativar esta opção, copie e cole as exclusões padrão "
"para resolver problemas rapidamente:"

#. translators: %1$s = opening </a> tag, %2$s = closing </a> tag.
#: inc/Engine/Admin/Settings/Page.php:533
msgid ""
"Also, please check our %1$sdocumentation%2$s for a list of compatibility "
"exclusions."
msgstr ""
"Verifique também a nossa %1$sdocumentação%2$s para uma lista de exclusões de"
" compatibilidade."

#: inc/Engine/Admin/Settings/Page.php:538
msgid ""
"Internal scripts are excluded by default to prevent issues. Remove them to "
"take full advantage of this option."
msgstr ""
"Scripts internos são excluídos por padrão para evitar problemas. Remova-os "
"para tirar vantagem total desta opção."

#. translators: %1$s = opening </a> tag, %2$s = closing </a> tag.
#: inc/Engine/Admin/Settings/Page.php:541
msgid ""
"If this causes trouble, restore the default exclusions, found %1$shere%2$s"
msgstr ""
"Se isso causar problemas, restaure as exclusões padrão, encontradas "
"%1$saqui%2$s"

#: inc/Engine/Admin/Settings/Page.php:550
msgid "Minify CSS files"
msgstr "Minificar os arquivos CSS"

#: inc/Engine/Admin/Settings/Page.php:551
msgid "Minify CSS removes whitespace and comments to reduce the file size."
msgstr ""
"Minificar o CSS remove espaços em branco e comentários para reduzir o "
"tamanho do arquivo."

#: inc/Engine/Admin/Settings/Page.php:564
#: inc/Engine/Admin/Settings/Page.php:628
#: inc/Engine/Admin/Settings/Page.php:694
#: inc/Engine/Admin/Settings/Page.php:718
msgid "This could break things!"
msgstr "Isto poderia quebrar coisas!"

#: inc/Engine/Admin/Settings/Page.php:565
#: inc/Engine/Admin/Settings/Page.php:629
#: inc/Engine/Admin/Settings/Page.php:695
#: inc/Engine/Admin/Settings/Page.php:719
msgid ""
"If you notice any errors on your website after having activated this "
"setting, just deactivate it again, and your site will be back to normal."
msgstr ""
"Se notar quaisquer erros no seu site após ter ativado esta configuração, "
"basta desativá-la novamente e o seu site voltará ao normal."

#: inc/Engine/Admin/Settings/Page.php:566
msgid "Activate minify CSS"
msgstr "Ativar minificar o CSS"

#: inc/Engine/Admin/Settings/Page.php:572
msgid ""
"Specify URLs of CSS files to be excluded from minification (one per line)."
msgstr ""
"Especifique os URLs dos arquivos de CSS a serem excluídos da minificação (um"
" por linha)."

#: inc/Engine/Admin/Settings/Page.php:573
msgid ""
"<strong>Internal:</strong> The domain part of the URL will be stripped "
"automatically. Use (.*).css wildcards to exclude all CSS files located at a "
"specific path."
msgstr ""
"<strong>Interno:</strong> A parte do domínio do URL será automaticamente "
"limpa. Use coringas (.*).css para excluir todos os arquivos CSS localizados "
"em um caminho específico."

#. translators: %1$s = opening <a> tag, %2$s = closing </a> tag.
#: inc/Engine/Admin/Settings/Page.php:575
msgid ""
"<strong>3rd Party:</strong> Use either the full URL path or only the domain "
"name, to exclude external CSS. %1$sMore info%2$s"
msgstr ""
"<strong>Terceiros:</strong> Use o caminho completo do URL ou apenas o nome "
"do domínio, para excluir CSS externo. %1$sMais informações%2$s"

#: inc/Engine/Admin/Settings/Page.php:588
#: inc/Engine/Admin/Settings/Page.php:610
msgid "Optimize CSS delivery"
msgstr "Otimizar a entrega do CSS"

#: inc/Engine/Admin/Settings/Page.php:593
msgid ""
"Optimize CSS delivery eliminates render-blocking CSS on your website. Only "
"one method can be selected. Remove Unused CSS is recommended for optimal "
"performance, but limited only to the users with active license."
msgstr ""
"Otimizar a entrega do CSS elimina o CSS que bloqueia a renderização no seu "
"site. Apenas um método pode ser selecionado. Remover o CSS não usado é "
"recomendado para um desempenho ótimo, mas limitado aos usuários com uma "
"licença ativa."

#: inc/Engine/Admin/Settings/Page.php:593
msgid ""
"Optimize CSS delivery eliminates render-blocking CSS on your website. Only "
"one method can be selected. Remove Unused CSS is recommended for optimal "
"performance."
msgstr ""
"Otimizar a entrega do CSS elimina CSS que bloqueia a renderizaçào no seu "
"site. Apenas um método pode ser selecionado. Remover o CSS não usado é "
"recomendado para um desempenho ótimo."

#. translators: %1$s = opening <a> tag, %2$s = closing </a> tag.
#: inc/Engine/Admin/Settings/Page.php:603
msgid ""
"Optimize CSS Delivery features are disabled on local environments. %1$sLearn"
" more%2$s"
msgstr ""
"Os recursos de otimização da entrega do CSS estão desativados em ambientes "
"locais. %1$sSaiba mais%2$s"

#: inc/Engine/Admin/Settings/Page.php:623
#: inc/Engine/Optimization/RUCSS/Admin/OptionSubscriber.php:74
msgid "Remove Unused CSS"
msgstr "Remover CSS não utilizado"

#. translators: %1$s = opening <a> tag, %2$s = closing </a> tag.
#: inc/Engine/Admin/Settings/Page.php:626
msgid ""
"Removes unused CSS per page and helps to reduce page size and HTTP requests."
" Recommended for best performance. Test thoroughly! %1$sMore info%2$s"
msgstr ""
"Remove o CSS não usado por página e ajuda a reduzir o tamanho das páginas e "
"as requisições HTTP. Recomendado para melhor desempenho. Teste "
"extensivamente! %1$sMais informações%2$s"

#: inc/Engine/Admin/Settings/Page.php:630
msgid "Activate Remove Unused CSS"
msgstr "Ativa a remoção do CSS não usado"

#: inc/Engine/Admin/Settings/Page.php:636
msgid "CSS safelist"
msgstr "Lista de CSS seguro"

#: inc/Engine/Admin/Settings/Page.php:637
msgid ""
"Specify CSS filenames, IDs or classes that should not be removed (one per "
"line)."
msgstr ""
"Especifique nomes de arquivos CSS, IDs ou classes que não deverão ser "
"removidos (um por linha)."

#: inc/Engine/Admin/Settings/Page.php:652
#: inc/Engine/CriticalPath/Admin/Subscriber.php:200
msgid "Load CSS asynchronously"
msgstr "Carregar o CSS de forma assíncrona"

#. translators: %1$s = plugin name.
#: inc/Engine/Admin/Settings/Page.php:655
msgctxt "WP Critical CSS compatibility"
msgid ""
"Load CSS asynchronously is currently handled by the %1$s plugin. If you want"
" to use WP Rocket’s load CSS asynchronously option, disable the %1$s plugin."
msgstr ""
"No momento o carregar CSS de forma assíncrona está sendo manipulado pelo "
"plugin %1$s. Se deseja usar a opção de carregar o CSS de forma assíncrona do"
" WP Rocket, desative o plugin %1$s."

#. translators: %1$s = opening <a> tag, %2$s = closing </a> tag.
#: inc/Engine/Admin/Settings/Page.php:657
msgid ""
"Generates critical path CSS and loads CSS asynchronously. %1$sMore info%2$s"
msgstr ""
"Gera o CSS do caminho crítico e carrega o CSS de forma assíncrona. %1$sMais "
"informações%2$s"

#: inc/Engine/Admin/Settings/Page.php:663
msgid "Fallback critical CSS"
msgstr "CSS crítico opcional"

#. translators: %1$s = opening <a> tag, %2$s = closing </a> tag.
#: inc/Engine/Admin/Settings/Page.php:665
msgid ""
"Provides a fallback if auto-generated critical path CSS is incomplete. "
"%1$sMore info%2$s"
msgstr ""
"Fornece uma opção se o caminho de CSS crítico gerado automaticamente estiver"
" incompleto. %1$sMais informações%2$s"

#: inc/Engine/Admin/Settings/Page.php:680
msgid "Minify JavaScript files"
msgstr "Minificar os arquivos JavaScript"

#: inc/Engine/Admin/Settings/Page.php:681
msgid ""
"Minify JavaScript removes whitespace and comments to reduce the file size."
msgstr ""
"Minificar o JavaScript remove os espaços em branco e comentários para "
"reduzir o tamanho do arquivo."

#: inc/Engine/Admin/Settings/Page.php:696
msgid "Activate minify JavaScript"
msgstr "Ativar minificar o JavaScript"

#: inc/Engine/Admin/Settings/Page.php:701
msgid ""
"Combine JavaScript files <em>(Enable Minify JavaScript files to select)</em>"
msgstr ""
"Combinar os arquivos JavaScript <em>(Ative Minificar arquivos JavaScript "
"para selecionar)</em>"

#. translators: %1$s = opening <a> tag, %2$s = closing </a> tag.
#: inc/Engine/Admin/Settings/Page.php:703
msgid ""
"Combine JavaScript files combines your site’s internal, 3rd party and inline"
" JS reducing HTTP requests. Not recommended if your site uses HTTP/2. "
"%1$sMore info%2$s"
msgstr ""
"Combinar arquivos JavaScript combina o JS interno, inline e de terceiros "
"reduzindo as chamadas HTTP. Não é recomendado se o seu site usa HTTP/2. "
"%1$sMais informações%2$s"

#. translators: %1$s = opening <a> tag, %2$s = closing </a> tag.
#: inc/Engine/Admin/Settings/Page.php:704
msgid ""
"For compatibility and best results, this option is disabled when delay "
"javascript execution is enabled."
msgstr ""
"Para compatibilidade e melhores resultados, esta opção está desativada "
"quando a execução retrasada de javascript estiver ativa."

#: inc/Engine/Admin/Settings/Page.php:720
msgid "Activate combine JavaScript"
msgstr "Ativar combinar o JavaScript"

#. translators: %1$s = opening <a> tag, %2$s = closing </a> tag.
#: inc/Engine/Admin/Settings/Page.php:727
msgid ""
"Specify patterns of inline JavaScript to be excluded from concatenation (one"
" per line). %1$sMore info%2$s"
msgstr ""
"Especifique padrões de JavaScript inline a serem excluídos da concatenação "
"(um por linha). %1$sMais informações%2$s"

#: inc/Engine/Admin/Settings/Page.php:744
msgid ""
"Specify URLs of JavaScript files to be excluded from minification and "
"concatenation (one per line)."
msgstr ""
"Especifique URLs de arquivos JavaScript a serem excluídos da minificação e "
"concatenação (um por linha)."

#: inc/Engine/Admin/Settings/Page.php:745
msgid ""
"<strong>Internal:</strong> The domain part of the URL will be stripped "
"automatically. Use (.*).js wildcards to exclude all JS files located at a "
"specific path."
msgstr ""
"<strong>Interno:</strong> A parte do domínio do URL será removida "
"automaticamente. Use wildcards (.*).js para excluir todos os arquivos JS "
"localizados em um caminho específico."

#. translators: %1$s = opening <a> tag, %2$s = closing </a> tag.
#: inc/Engine/Admin/Settings/Page.php:747
msgid ""
"<strong>3rd Party:</strong> Use either the full URL path or only the domain "
"name, to exclude external JS. %1$sMore info%2$s"
msgstr ""
"<strong>Terceiros</strong> Use o caminho completo do URL ou apenas o "
"domínio, para excluir JS externo. %1$sMais Informações%2$s"

#: inc/Engine/Admin/Settings/Page.php:763
#: inc/Engine/Optimization/DeferJS/AdminSubscriber.php:76
msgid "Load JavaScript deferred"
msgstr "Adiar o carregamento do JavaScript"

#. translators: %1$s = opening <a> tag, %2$s = closing </a> tag.
#: inc/Engine/Admin/Settings/Page.php:765
msgid ""
"Load JavaScript deferred eliminates render-blocking JS on your site and can "
"improve load time. %1$sMore info%2$s"
msgstr ""
"Adiar o carregamento do JavaScript elimina JS bloqueando a renderização no "
"seu site e pode melhorar o tempo de carregamento. %1$sMais informações%2$s"

#. translators: %1$s = opening <a> tag, %2$s = closing </a> tag.
#: inc/Engine/Admin/Settings/Page.php:778
msgid ""
"Specify URLs or keywords of JavaScript files to be excluded from defer (one "
"per line). %1$sMore info%2$s"
msgstr ""
"Especifique URLs ou palavras-chave de arquivos JavaScript a serem excluídos "
"do atraso (um por linha). %1$sMais informações%2$s"

#: inc/Engine/Admin/Settings/Page.php:794
#: inc/Engine/Optimization/DelayJS/Admin/Subscriber.php:210
msgid "Delay JavaScript execution"
msgstr "Atrasa a execução do JavaScript"

#. translators: %1$s = opening <a> tag, %2$s = closing </a> tag.
#: inc/Engine/Admin/Settings/Page.php:796
msgid ""
"Improves performance by delaying the loading of JavaScript files until user "
"interaction (e.g. scroll, click). %1$sMore info%2$s"
msgstr ""
"Melhora o desempenho atrasando o carregamento dos arquivos JavaScript até a "
"interação com o usuário (como rolar ou clicar) %1$sMais informações%2$s"

#: inc/Engine/Admin/Settings/Page.php:805
msgid "One-click exclusions"
msgstr "Exclusões de um clique"

#: inc/Engine/Admin/Settings/Page.php:806
msgid ""
"When using the Delay JavaScript Execution, you might experience delay "
"loading elements located in the viewport that need to appear immediately - "
"e.g. slider, header, menu."
msgstr ""
"Ao usar Atrasar a Execução do JavaScript você pode experimentar um atraso no"
" carregamento dos elementos localizados na área de visualização que deveriam"
" aparecer imediatametne - por exemplo slider, header e menu."

#: inc/Engine/Admin/Settings/Page.php:807
msgid ""
"If you need instant visibility, click below on files that should NOT be "
"delayed. This selection will help users interact with the elements straight "
"away."
msgstr ""
"Se precisa de visualização instantânea, clique abaixo nos arquivos que NÃO "
"devem ser atrasados. Esta seleção irá ajudar os usuários a interagirem com "
"os elementos imediatamente."

#: inc/Engine/Admin/Settings/Page.php:824
msgid ""
"Specify URLs or keywords that can identify inline or JavaScript files to be "
"excluded from delaying execution (one per line)."
msgstr ""
"Especifique URLs ou palavras-chave que podem identificar arquivos inline ou "
"JavaScript a serem excluídos do adiamento da execução (um por linha)."

#: inc/Engine/Admin/Settings/Page.php:856
msgid "Media"
msgstr "Mídia"

#: inc/Engine/Admin/Settings/Page.php:857
msgid "LazyLoad, image dimensions"
msgstr "LazyLoad, dimensões da imagem"

#: inc/Engine/Admin/Settings/Page.php:866
msgid "Autoptimize"
msgstr "Autoptimize"

#: inc/Engine/Admin/Settings/Page.php:920
msgid "LazyLoad"
msgstr "LazyLoad"

#. translators: %1$s = opening <a> tag, %2$s = closing </a> tag.
#: inc/Engine/Admin/Settings/Page.php:923
msgid ""
"It can improve actual and perceived loading time as images, iframes, and "
"videos will be loaded only as they enter (or about to enter) the viewport "
"and reduces the number of HTTP requests. %1$sMore Info%2$s"
msgstr ""
"Pode melhorar o tempo real e percebido de carregamento pois imagens, iframes"
" e vídeos serão carregados apenas quando entrarem (ou estiverem para entrar)"
" na área visualizada na tela, e reduz o número de requisições HTTP. %1$sMais"
" Informações%2$s"

#. translators: %1$s = “WP Rocket”, %2$s = a list of plugin names.
#: inc/Engine/Admin/Settings/Page.php:930
msgid ""
"LazyLoad is currently activated in %2$s. If you want to use WP Rocket’s "
"LazyLoad, disable this option in %2$s."
msgstr ""
"No momento o LazyLoad do %2$s está ativo. Se deseja usar o LazyLoad do WP "
"Rocket, desative esta opção no %2$s."

#: inc/Engine/Admin/Settings/Page.php:933
msgid "Image Dimensions"
msgstr "Dimensões da imagem"

#. translators: %1$s = opening <a> tag, %2$s = closing </a> tag.
#: inc/Engine/Admin/Settings/Page.php:936
msgid ""
"Add missing width and height attributes to images. Helps prevent layout "
"shifts and improve the reading experience for your visitors. %1$sMore "
"info%2$s"
msgstr ""
"Adicione atributos largura e altura faltando a imagens. Ajuda a evitar "
"mudanças de layout e melhora a experiência de leitura para os seus "
"visitantes %1$sMais informações%2$s"

#: inc/Engine/Admin/Settings/Page.php:955
msgid "Enable for images"
msgstr "Habilitar para imagens"

#. translators: %1$s = “WP Rocket”, %2$s = a list of plugin names.
#: inc/Engine/Admin/Settings/Page.php:967
msgid ""
"LazyLoad for images is currently activated in %2$s. If you want to use "
"%1$s’s LazyLoad, disable this option in %2$s."
msgstr ""
"No momento o LazyLoad para imagens do %2$s está ativo. Se deseja usar o "
"LazyLoad de %1$s, desative esta opção no %2$s."

#: inc/Engine/Admin/Settings/Page.php:975
msgid "Enable for CSS background images"
msgstr "Ativar para as imagens de fundo do CSS"

#: inc/Engine/Admin/Settings/Page.php:990
msgid "Enable for iframes and videos"
msgstr "Habilitar para iframes e vídeos"

#: inc/Engine/Admin/Settings/Page.php:1005
msgid "Replace YouTube iframe with preview image"
msgstr "Substituir o iframe do YouTube pela imagem de pré-visualização"

#. translators: %1$s = “WP Rocket”, %2$s = a list of plugin or themes names.
#: inc/Engine/Admin/Settings/Page.php:1007
msgid "Replace YouTube iframe with preview image is not compatible with %2$s."
msgstr ""
"Substituir o iframe do YouTube com a imagem de pré-visualização não é "
"compatível com %2$s."

#. translators: %1$s = “WP Rocket”, %2$s = a list of plugin or themes names.
#: inc/Engine/Admin/Settings/Page.php:1007
msgid ""
"This can significantly improve your loading time if you have a lot of "
"YouTube videos on a page."
msgstr ""
"Isso pode melhorar de forma significativa o tempo de carregamento se você "
"tem muitos vídeos do YouTube em uma página."

#: inc/Engine/Admin/Settings/Page.php:1022
msgid "Excluded images or iframes"
msgstr "Imagens e iFrames excluídos"

#. translators: %1$s = opening <a> tag, %2$s = closing </a> tag.
#: inc/Engine/Admin/Settings/Page.php:1024
msgid ""
"Specify keywords (e.g. image filename, CSS filename, CSS class, domain) from"
" the image or iframe code to be excluded (one per line). %1$sMore info%2$s"
msgstr ""
"Especifique palavras-chave (ex: nomes de arquivo de imagens ou de CSS, "
"classes de CSS ou domínios) a serem excluídas do código da imagem ou do "
"iFrame (um por linha). %1$sMais informações%2$s"

#: inc/Engine/Admin/Settings/Page.php:1032
msgid "Add missing image dimensions"
msgstr "Adicionar dimensões de imagens faltando"

#: inc/Engine/Admin/Settings/Page.php:1052
msgid "Generate cache files, preload fonts"
msgstr "Gera os arquivos de cache, pré-carrega as fontes"

#: inc/Engine/Admin/Settings/Page.php:1064
msgid "Preload Cache"
msgstr "Pré-carregar o cache"

#. translators: %1$s = opening <a> tag, %2$s = closing </a> tag.
#: inc/Engine/Admin/Settings/Page.php:1067
msgid ""
"When you enable preloading WP Rocket will automatically detect your sitemaps"
" and save all URLs to the database. The plugin will make sure that your "
"cache is always preloaded."
msgstr ""
"Quando você ativa o pré-carregamento o WP Rocket irá detectar "
"automaticamente os seus sitemaps e salvar todos os URLs no banco de dados. O"
" plugin irá certificar-se que o seu cache será sempre pré-carregado."

#: inc/Engine/Admin/Settings/Page.php:1075
msgid "Preload Links"
msgstr "Pré-carregar Links"

#. translators: %1$s = opening <a> tag, %2$s = closing </a> tag.
#: inc/Engine/Admin/Settings/Page.php:1078
msgid ""
"Link preloading improves the perceived load time by downloading a page when "
"a user hovers over the link. %1$sMore info%2$s"
msgstr ""
"O pré-carregamento de link melhora o tempo percebido de carregamento ao "
"carregar uma página quando um usuário passa o mouse sobre o link. %1$sMais "
"informações%2$s"

#: inc/Engine/Admin/Settings/Page.php:1086
msgid "Prefetch DNS Requests"
msgstr "Pré-captura das Requisições de DNS"

#: inc/Engine/Admin/Settings/Page.php:1088
msgid ""
"DNS prefetching can make external files load faster, especially on mobile "
"networks"
msgstr ""
"A pré-captura do DNS pode carregar mais rápido arquivos externos, "
"especialmente em redes móveis"

#: inc/Engine/Admin/Settings/Page.php:1093
msgid "Preload Fonts"
msgstr "Pré-carregar as fontes"

#. translators: %1$s = opening <a> tag, %2$s = closing </a> tag.
#: inc/Engine/Admin/Settings/Page.php:1096
msgid ""
"Improves performance by helping browsers discover fonts in CSS files. "
"%1$sMore info%2$s"
msgstr ""
"Melhora o desempenho ao ajudar os naveggadores a descobrirem fontes em "
"arquivos CSS. %1$sMais informações%2$s"

#: inc/Engine/Admin/Settings/Page.php:1110
msgid "Activate Preloading"
msgstr "Ativar o Pré-Carregamento"

#: inc/Engine/Admin/Settings/Page.php:1121
msgid "Exclude URLs"
msgstr "Excluir URLs"

#. translators: %1$s = opening <a> tag, %2$s = closing </a> tag.
#: inc/Engine/Admin/Settings/Page.php:1126
msgid ""
"Specify URLs to be excluded from the preload feature (one per line). "
"%1$sMore info%2$s"
msgstr ""
"Especifique URLs a serem excluídos do recurso de pré-carregamento (um por "
"linha). %1$sMais informações%2$s"

#: inc/Engine/Admin/Settings/Page.php:1137
msgid "URLs to prefetch"
msgstr "URLs a pré-capturar"

#: inc/Engine/Admin/Settings/Page.php:1138
msgid ""
"Specify external hosts to be prefetched (no <code>http:</code>, one per "
"line)"
msgstr ""
"Especifique os servidores externos a serem pré-carregados (sem "
"<code>http:</code>, um por linha)"

#: inc/Engine/Admin/Settings/Page.php:1147
msgid "Fonts to preload"
msgstr "Fontes a pré-carregar"

#: inc/Engine/Admin/Settings/Page.php:1148
msgid ""
"Specify urls of the font files to be preloaded (one per line). Fonts must be"
" hosted on your own domain, or the domain you have specified on the CDN tab."
msgstr ""
"Especifique URLs dos arquivos das fontes a serem pré-carregadas (um por "
"linha). As fontes devem ser hospedadas no seu próprio domínio, ou o domínio "
"especificado na aba CDN."

#: inc/Engine/Admin/Settings/Page.php:1149
msgid ""
"The domain part of the URL will be stripped automatically.<br/>Allowed font "
"extensions: otf, ttf, svg, woff, woff2."
msgstr ""
"A parte do domínio do URL será automaticametne removida. <br/>Extensões de "
"fonte permitidas: otf, ttf, svg, woff, woff2."

#: inc/Engine/Admin/Settings/Page.php:1158
msgid "Enable link preloading"
msgstr "Ativar o pré-carregamento dos links"

#: inc/Engine/Admin/Settings/Page.php:1177
msgid "Advanced Rules"
msgstr "Regras Avançadas"

#: inc/Engine/Admin/Settings/Page.php:1178
msgid "Fine-tune cache rules"
msgstr "Ajuste fino das regras de cache"

#: inc/Engine/Admin/Settings/Page.php:1191
msgid ""
"Sensitive pages like custom login/logout URLs should be excluded from cache."
msgstr ""
"URLs de páginas dinâmicas como personalizadas de login/logout devem ser "
"excluídas do cache."

#: inc/Engine/Admin/Settings/Page.php:1194
msgctxt "plugin name"
msgid "WooCommerce"
msgstr "WooCommerce"

#: inc/Engine/Admin/Settings/Page.php:1196
msgctxt "plugin name"
msgid "Easy Digital Downloads"
msgstr "Easy Digital Downloads"

#: inc/Engine/Admin/Settings/Page.php:1198
msgctxt "plugin name"
msgid "iThemes Exchange"
msgstr "iThemes Exchange"

#: inc/Engine/Admin/Settings/Page.php:1200
msgctxt "plugin name"
msgid "Jigoshop"
msgstr "Jigoshop"

#: inc/Engine/Admin/Settings/Page.php:1202
msgctxt "plugin name"
msgid "WP-Shop"
msgstr "WP-Shop"

#. translators: %1$s = opening <a> tag, %2$s = plugin name, %3$s closing </a>
#. tag.
#: inc/Engine/Admin/Settings/Page.php:1208
msgid ""
"<br>Cart, checkout and \"my account\" pages set in "
"<strong>%1$s%2$s%3$s</strong> will be detected and never cached by default."
msgstr ""
"<br>As páginas do carrinho, check-out e “minha conta” definidas no "
"<strong>%1$s%2$s%3$s</strong> serão detectadas e por padrão nunca serão "
"armazenadas em cache."

#: inc/Engine/Admin/Settings/Page.php:1218
msgid "Cache Lifespan"
msgstr "Vida útil do cache"

#. translators: %1$s = opening <a> tag, %2$s = closing </a> tag.
#: inc/Engine/Admin/Settings/Page.php:1221
msgid ""
"Cache files older than the specified lifespan will be deleted.<br>Enable "
"%1$spreloading%2$s for the cache to be rebuilt automatically after lifespan "
"expiration."
msgstr ""
"Arquivos de cache mais antigos do que a vida útil especificada serão "
"excluídos. <br>Ative o %1$spré-carregamento%2$s para o cache ser "
"reconstruído automaticamente após a expiração da sua vida útil."

#: inc/Engine/Admin/Settings/Page.php:1237
msgid "Never Cache Cookies"
msgstr "Cookies Jamais em Cache"

#: inc/Engine/Admin/Settings/Page.php:1255
msgid "Cache Query String(s)"
msgstr "String(s) de Consulta em Cache"

#. translators: %1$s = opening <a> tag, %2$s = closing </a> tag.
#: inc/Engine/Admin/Settings/Page.php:1258
msgid ""
"%1$sCache for query strings%2$s enables you to force caching for specific "
"GET parameters."
msgstr ""
"%1$sStrings de consulta em cache%2$s permite forçar o cache de certos "
"parâmetros GET."

#: inc/Engine/Admin/Settings/Page.php:1269
msgid ""
"Specify time after which the global cache is cleared<br>(0 = unlimited )"
msgstr ""
"Especifique o tempo após o qual o Cache Global é limpo<br>(0=ilimitado)"

#. translators: %1$s = opening <a> tag, %2$s = closing </a> tag.
#: inc/Engine/Admin/Settings/Page.php:1271
msgid ""
"Reduce lifespan to 10 hours or less if you notice issues that seem to appear"
" periodically. %1$sWhy?%2$s"
msgstr ""
"Reduza a vida útil para 10 horas ou menos se notar erros que parecem surgir "
"periodicamente. %1$sPor que?%2$s"

#: inc/Engine/Admin/Settings/Page.php:1277
#: inc/Engine/License/views/promo-banner.php:30
#: inc/Engine/License/views/renewal-soon-banner.php:13
msgid "Hours"
msgstr "Horas"

#: inc/Engine/Admin/Settings/Page.php:1278
#: inc/Engine/License/views/promo-banner.php:29
#: inc/Engine/License/views/renewal-soon-banner.php:12
msgid "Days"
msgstr "Dias"

#: inc/Engine/Admin/Settings/Page.php:1283
msgid ""
"Specify URLs of pages or posts that should never be cached (one per line)"
msgstr ""
"Especifique os URLs de páginas ou posts que deseja que jamais sejam "
"armazenados em cache (um por linha)"

#: inc/Engine/Admin/Settings/Page.php:1284
#: inc/Engine/Admin/Settings/Page.php:1312
msgid ""
"The domain part of the URL will be stripped automatically.<br>Use (.*) "
"wildcards to address multiple URLs under a given path."
msgstr ""
"A parte do domínio do URL será removida automaticamente.<br>Use wildcards "
"(.*) para se referir a múltiplos URLs em um dado caminho."

#: inc/Engine/Admin/Settings/Page.php:1293
msgid ""
"Specify full or partial IDs of cookies that, when set in the visitor's "
"browser, should prevent a page from getting cached (one per line)"
msgstr ""
"Especifique ID's completas ou parciais de cookies que, quando vistos no "
"navegador do visitante, devem evitar uma página de ser armazenada em cache "
"(uma por linha)"

#: inc/Engine/Admin/Settings/Page.php:1301
msgid ""
"Specify user agent strings that should never see cached pages (one per line)"
msgstr ""
"Especifique strings de agentes de usuário que nunca devem ter páginas "
"armazenadas em cache (uma por linha)"

#: inc/Engine/Admin/Settings/Page.php:1302
msgid "Use (.*) wildcards to detect parts of UA strings."
msgstr ""
"Use wildcards (.*) para detectar partes de strings de agentes de usuário."

#: inc/Engine/Admin/Settings/Page.php:1311
msgid ""
"Specify URLs you always want purged from cache whenever you update any post "
"or page (one per line)"
msgstr ""
"Especifique os URLs que deseja esvaziar cache sempre que atualizar qualquer "
"post ou página (um por linha)"

#: inc/Engine/Admin/Settings/Page.php:1320
msgid "Specify query strings for caching (one per line)"
msgstr ""
"Especifique parâmetros de consulta para armazenamento em cache (um por "
"linha)"

#: inc/Engine/Admin/Settings/Page.php:1346
msgid "Optimize, reduce bloat"
msgstr "Otimize, reduza o excesso"

#: inc/Engine/Admin/Settings/Page.php:1353
msgid "Post Cleanup"
msgstr "Limpeza de Post"

#: inc/Engine/Admin/Settings/Page.php:1355
msgid ""
"Post revisions and drafts will be permanently deleted. Do not use this "
"option if you need to retain revisions or drafts."
msgstr ""
"As revisões e rascunhos de posts serão excluídos permanentemente. Não use "
"esta opção se precisa manter as revisões e rascunhos."

#: inc/Engine/Admin/Settings/Page.php:1360
msgid "Comments Cleanup"
msgstr "Limpeza de Comentários"

#: inc/Engine/Admin/Settings/Page.php:1362
msgid "Spam and trashed comments will be permanently deleted."
msgstr "Os comentários spam e na lixeira serão excluídos permanentemente."

#: inc/Engine/Admin/Settings/Page.php:1366
msgid "Transients Cleanup"
msgstr "Limpeza de Transientes"

#: inc/Engine/Admin/Settings/Page.php:1368
msgid ""
"Transients are temporary options; they are safe to remove. They will be "
"automatically regenerated as your plugins require them."
msgstr ""
"Transientes são opções temporárias e é seguro removê-los. Serão regenerados "
"automaticamente quando seus plugins precisarem deles."

#: inc/Engine/Admin/Settings/Page.php:1372
msgid "Database Cleanup"
msgstr "Limpeza do Banco de Dados"

#: inc/Engine/Admin/Settings/Page.php:1374
msgid "Reduces overhead of database tables"
msgstr "Reduz o excesso de informações nas tabelas do banco de dados"

#: inc/Engine/Admin/Settings/Page.php:1378
msgid "Automatic Cleanup"
msgstr "Limpeza automática"

#. translators: %s is the number of revisions found in the database. It's a
#. formatted number, don't use %d.
#: inc/Engine/Admin/Settings/Page.php:1391
msgid "%s revision in your database."
msgid_plural "%s revisions in your database."
msgstr[0] "%s revisão no seu banco de dados."
msgstr[1] "%s revisões no seu banco de dados."
msgstr[2] "%s revisões no seu banco de dados."

#. translators: %s is the number of revisions found in the database. It's a
#. formatted number, don't use %d.
#: inc/Engine/Admin/Settings/Page.php:1401
msgid "%s draft in your database."
msgid_plural "%s drafts in your database."
msgstr[0] "%s rascunho no seu banco de dados."
msgstr[1] "%s rascunhos no seu banco de dados."
msgstr[2] "%s rascunhos no seu banco de dados."

#. translators: %s is the number of revisions found in the database. It's a
#. formatted number, don't use %d.
#: inc/Engine/Admin/Settings/Page.php:1411
msgid "%s trashed post in your database."
msgid_plural "%s trashed posts in your database."
msgstr[0] "%s post na lixeira no seu banco de dados."
msgstr[1] "%s posts na lixeira no seu banco de dados."
msgstr[2] "%s posts na lixeira no seu banco de dados."

#. translators: %s is the number of revisions found in the database. It's a
#. formatted number, don't use %d.
#: inc/Engine/Admin/Settings/Page.php:1421
msgid "%s spam comment in your database."
msgid_plural "%s spam comments in your database."
msgstr[0] "%s comentário spam no seu banco de dados."
msgstr[1] "%s comentários spam no seu banco de dados:"
msgstr[2] "%s comentários spam no seu banco de dados:"

#. translators: %s is the number of revisions found in the database. It's a
#. formatted number, don't use %d.
#: inc/Engine/Admin/Settings/Page.php:1431
msgid "%s trashed comment in your database."
msgid_plural "%s trashed comments in your database."
msgstr[0] "%s comentário na lixeira no seu banco de dados."
msgstr[1] "%s comentários na lixeira no seu banco de dados."
msgstr[2] "%s comentários na lixeira no seu banco de dados."

#: inc/Engine/Admin/Settings/Page.php:1439
msgid "All transients"
msgstr "Todos os transientes"

#. translators: %s is the number of revisions found in the database. It's a
#. formatted number, don't use %d.
#: inc/Engine/Admin/Settings/Page.php:1441
msgid "%s transient in your database."
msgid_plural "%s transients in your database."
msgstr[0] "%s transiente no seu banco de dados."
msgstr[1] "%s transientes no seu banco de dados."
msgstr[2] "%s transientes no seu banco de dados."

#: inc/Engine/Admin/Settings/Page.php:1449
msgid "Optimize Tables"
msgstr "Otimizar Tabelas"

#. translators: %s is the number of revisions found in the database. It's a
#. formatted number, don't use %d.
#: inc/Engine/Admin/Settings/Page.php:1451
msgid "%s table to optimize in your database."
msgid_plural "%s tables to optimize in your database."
msgstr[0] "%s tabela a otimizar no seu banco de dados."
msgstr[1] "%s tabelas a otimizar no seu banco de dados."
msgstr[2] "%s tabelas a otimizar no seu banco de dados."

#: inc/Engine/Admin/Settings/Page.php:1462
msgid "Schedule Automatic Cleanup"
msgstr "Agendar Limpeza Automática"

#: inc/Engine/Admin/Settings/Page.php:1474
msgid "Frequency"
msgstr "Frequência"

#: inc/Engine/Admin/Settings/Page.php:1482
msgid "Daily"
msgstr "Diária"

#: inc/Engine/Admin/Settings/Page.php:1483
msgid "Weekly"
msgstr "Semanal"

#: inc/Engine/Admin/Settings/Page.php:1484
msgid "Monthly"
msgstr "Mensal"

#: inc/Engine/Admin/Settings/Page.php:1501
msgid "Integrate your CDN"
msgstr "Integrar a sua CDN"

#: inc/Engine/Admin/Settings/Page.php:1513
msgid ""
"All URLs of static files (CSS, JS, images) will be rewritten to the CNAME(s)"
" you provide."
msgstr ""
"Todos os URLs de arquivos estáticos (CSS, JS, imagens) serão reescritos "
"no(s) CNAME(s) que você fornecer."

#. translators: %1$s = opening link tag, %2$s = closing link tag.
#: inc/Engine/Admin/Settings/Page.php:1515
msgid ""
"Not required for services like Cloudflare and Sucuri. Please see our "
"available %1$sAdd-ons%2$s."
msgstr ""
"Não necessário para serviços como Cloudflare e Sucuri. Por favor veja os "
"%1$scomplementos%2$s disponíveis."

#. translators: %1$s = opening em tag, %2$l = list of add-on name(s), %3$s =
#. closing em tag.
#: inc/Engine/Admin/Settings/Page.php:1561
msgid ""
"%1$s%2$l Add-on%3$s is currently enabled. Configuration of the CDN settings "
"is not required for %2$l to work on your site."
msgid_plural ""
"%1$s%2$l Add-ons%3$s are currently enabled. Configuration of the CDN "
"settings is not required for %2$l to work on your site."
msgstr[0] ""
"%1$sComplemento %2$l %3$s está ativo no momento. Não é necessário configurar"
" CDN para %2$l funcionar no seu site."
msgstr[1] ""
"%1$sComplementos %2$l %3$s estão ativos no momento. Não é necessário "
"configurar CDN para %2$l funcionar no seu site."
msgstr[2] ""
"%1$sComplementos %2$l %3$s estão ativos no momento. Não é necessário "
"configurar CDN para %2$l funcionar no seu site."

#: inc/Engine/Admin/Settings/Page.php:1587
msgid "Enable Content Delivery Network"
msgstr "Ativar a CDN"

#: inc/Engine/Admin/Settings/Page.php:1596
#: inc/Engine/CDN/RocketCDN/AdminPageSubscriber.php:159
msgid "CDN CNAME(s)"
msgstr "CNAME(s) da CDN"

#: inc/Engine/Admin/Settings/Page.php:1597
#: inc/Engine/CDN/RocketCDN/AdminPageSubscriber.php:160
msgid "Specify the CNAME(s) below"
msgstr "Especifique abaixo a(s) CNAME(s)"

#: inc/Engine/Admin/Settings/Page.php:1604
msgid ""
"Specify URL(s) of files that should not get served via CDN (one per line)."
msgstr ""
"Especifique URL(s) de arquivos que não devem ser servidos pela CDN (um por "
"linha)."

#: inc/Engine/Admin/Settings/Page.php:1605
msgid ""
"The domain part of the URL will be stripped automatically.<br>Use (.*) "
"wildcards to exclude all files of a given file type located at a specific "
"path."
msgstr ""
"A parte do domínio do URL será removida automaticamente.<br>Use wildcards "
"(.*) para excluir todos os arquivos de um dado tipo de arquivo localizado em"
" um caminho específico."

#: inc/Engine/Admin/Settings/Page.php:1628
#: inc/Engine/Admin/Settings/Page.php:1636
msgid "Heartbeat"
msgstr "Heartbeat"

#: inc/Engine/Admin/Settings/Page.php:1629
msgid "Control WordPress Heartbeat API"
msgstr "Controla a API Heartbeat do WordPress"

#: inc/Engine/Admin/Settings/Page.php:1637
msgid ""
"Reducing or disabling the Heartbeat API’s activity can help save some of "
"your server’s resources."
msgstr ""
"Reduzir ou desabilitar a atividade da API Hartbeat pode economizar alguns "
"recursos do seu servidor."

#: inc/Engine/Admin/Settings/Page.php:1643
msgid "Reduce or disable Heartbeat activity"
msgstr "Reduz ou desabilita a atividade Heartbeat"

#: inc/Engine/Admin/Settings/Page.php:1644
msgid ""
"Reducing activity will change Heartbeat frequency from one hit each minute "
"to one hit every 2 minutes."
msgstr ""
"Reduzir a atividade modifica a frequência do Heartbeat de um hit por minuto "
"para um hit a cada 2 minutos."

#: inc/Engine/Admin/Settings/Page.php:1644
msgid ""
"Disabling Heartbeat entirely may break plugins and themes using this API."
msgstr ""
"Desabilitar totalmente Heartbeat pode quebrar plugins e temas que usem esta "
"API."

#: inc/Engine/Admin/Settings/Page.php:1658
msgid "Do not limit"
msgstr "Não limitar"

#: inc/Engine/Admin/Settings/Page.php:1659
msgid "Reduce activity"
msgstr "Reduzir atividade"

#: inc/Engine/Admin/Settings/Page.php:1660
msgid "Disable"
msgstr "Desativar"

#: inc/Engine/Admin/Settings/Page.php:1668
msgid "Control Heartbeat"
msgstr "Controlar Heartbeat"

#: inc/Engine/Admin/Settings/Page.php:1677
msgid "Behavior in backend"
msgstr "Comportamento no painel"

#: inc/Engine/Admin/Settings/Page.php:1684
msgid "Behavior in post editor"
msgstr "Comportamento no editor de post"

#: inc/Engine/Admin/Settings/Page.php:1690
msgid "Behavior in frontend"
msgstr "Comportamento na interface"

#: inc/Engine/Admin/Settings/Page.php:1709
#: views/settings/page-sections/tutorials.php:39
msgid "Add-ons"
msgstr "Complementos"

#: inc/Engine/Admin/Settings/Page.php:1710
msgid "Add more features"
msgstr "Adicione mais recursos"

#: inc/Engine/Admin/Settings/Page.php:1717
msgid "One-click Rocket Add-ons"
msgstr "Complementos de um clique do Rocket"

#: inc/Engine/Admin/Settings/Page.php:1718
msgid ""
"One-Click Add-ons are features extending available options without "
"configuration needed. Switch the option \"on\" to enable from this screen."
msgstr ""
"Complementos de um clique são recursos que extendem as opções disponíveis "
"sem a necessidade de configurações. Ative a opção para ativar a partir desta"
" tela."

#: inc/Engine/Admin/Settings/Page.php:1728
msgid "Rocket Add-ons"
msgstr "Complementos do Rocket"

#: inc/Engine/Admin/Settings/Page.php:1729
msgid "Rocket Add-ons are complementary features extending available options."
msgstr ""
"Os Complementos do Rocket são recursos complementares extendendo as opções "
"disponíveis."

#: inc/Engine/Admin/Settings/Page.php:1740
msgid "User Cache"
msgstr "Cache de Usuário"

#: inc/Engine/Admin/Settings/Page.php:1746
msgid ""
"If you need to create a dedicated set of cache files for each logged-in "
"WordPress user, you must activate this add-on."
msgstr ""
"Se você precisar criar um conjunto de arquivos de cache para cada usuário "
"que fizer o acesso como usuário do WordPress, você precisa ativar este "
"complemento."

#. translators: %1$s = opening <a> tag, %2$s = closing </a> tag.
#: inc/Engine/Admin/Settings/Page.php:1748
msgid ""
"User cache is great when you have user-specific or restricted content on "
"your website.<br>%1$sLearn more%2$s"
msgstr ""
"O cache de usuários é excelente quando você tem conteúdo específico do "
"usuário ou restrito no seu site. <br>%1$sSaiba mais%2$s"

#: inc/Engine/Admin/Settings/Page.php:1761
#: inc/Engine/Admin/Settings/Page.php:1930
msgid "Cloudflare"
msgstr "Cloudflare"

#: inc/Engine/Admin/Settings/Page.php:1767
msgid "Integrate your Cloudflare account with this add-on."
msgstr "Integre a sua conta Cloudflare com este complemento."

#: inc/Engine/Admin/Settings/Page.php:1768
msgid ""
"Provide your account email, global API key, and domain to use options such "
"as clearing the Cloudflare cache and enabling optimal settings with WP "
"Rocket."
msgstr ""
"Forneça o e-mail, a chave global da API e o domínio da sua conta para usar "
"opções como esvaziar o cache do Cloudflare e ativar as configurações "
"otimizadas com o WP Rocket."

#. translators: %1$s = opening span tag, %2$s = closing span tag.
#: inc/Engine/Admin/Settings/Page.php:1771
msgid ""
"%1$sPlanning on using Automatic Platform Optimization (APO)?%2$s Just "
"activate the official Cloudflare plugin and configure it. WP Rocket will "
"automatically enable compatibility."
msgstr ""
"%1$sPlanejando usar a Otimização Automática da Plataforma (OAP)?%2$s Basta "
"ativar e configurar o plugin oficial do Cloudflare. O WP Rocket irá ativar a"
" compatibilidade automaticamente."

#: inc/Engine/Admin/Settings/Page.php:1819
msgid "Varnish"
msgstr "Varnish"

#: inc/Engine/Admin/Settings/Page.php:1825
msgid "If Varnish runs on your server, you must activate this add-on."
msgstr "Você deve ativar este complemento se roda o Varnish no seu servidor."

#. translators: %1$s = opening <a> tag, %2$s = closing </a> tag.
#: inc/Engine/Admin/Settings/Page.php:1827
msgid ""
"Varnish cache will be purged each time WP Rocket clears its cache to ensure "
"content is always up-to-date.<br>%1$sLearn more%2$s"
msgstr ""
"O cache Varnish será esvaziado sempre que o WP Rocket esvaziar o seu cache "
"para garantir que o conteúdo esteja sempre atualizado.<br>%1$sSaiba mais%2$s"

#: inc/Engine/Admin/Settings/Page.php:1862
msgid "WebP Compatibility"
msgstr "Compatibilidade com WebP"

#: inc/Engine/Admin/Settings/Page.php:1868
msgid "Improve browser compatibility for WebP images."
msgstr "Melhora a compatibilidade com os navegadores para imagens WebP."

#. translators: %1$s and %3$s = opening <a> tag, %2$s = closing </a> tag.
#: inc/Engine/Admin/Settings/Page.php:1872
msgid ""
"Enable this option if you would like WP Rocket to serve WebP images to "
"compatible browsers. Please note that WP Rocket cannot create WebP images "
"for you. To create WebP images we recommend %1$sImagify%2$s. %3$sMore "
"info%2$s"
msgstr ""
"Ative esta opção se desejar que o WP Rocket sirva imagens WebP aos "
"navegadores compatíveis. Note que o WP Rocket não pode criar imagens WebP "
"para você. Para criar imagens WebP nós recomendamos o %1$sImagify%2$s. "
"%3$sMais informações%2$s"

#: inc/Engine/Admin/Settings/Page.php:1892
msgid "Clear the Sucuri cache when WP Rocket’s cache is cleared."
msgstr "Esvazia o cache Sucuri quando o cache do WP Rocket é esvaziado."

#: inc/Engine/Admin/Settings/Page.php:1895
msgid ""
"Provide your API key to clear the Sucuri cache when WP Rocket’s cache is "
"cleared."
msgstr ""
"Forneça a sua chave da API para limpar o cache Sucuri quando o cache do WP "
"Rocket é limpo."

#: inc/Engine/Admin/Settings/Page.php:1903
#: inc/Engine/Admin/Settings/Page.php:2047
msgid "Sucuri"
msgstr "Sucuri"

#: inc/Engine/Admin/Settings/Page.php:1909
msgid "Synchronize Sucuri cache with this add-on."
msgstr "Sincronize o cache Sucuri com este complemento."

#: inc/Engine/Admin/Settings/Page.php:1947
msgid "Cloudflare credentials"
msgstr "Credenciais do Cloudflare"

#: inc/Engine/Admin/Settings/Page.php:1956
msgid "Cloudflare settings"
msgstr "Configurações do Cloudflare"

#: inc/Engine/Admin/Settings/Page.php:1970
msgctxt "Cloudflare"
msgid "Global API key:"
msgstr "Chave global da API:"

#: inc/Engine/Admin/Settings/Page.php:1971
msgctxt "Cloudflare"
msgid "Find your API key"
msgstr "Encontre a sua chave da API"

#: inc/Engine/Admin/Settings/Page.php:1983
msgctxt "Cloudflare"
msgid "Account email"
msgstr "E-mail da Conta"

#: inc/Engine/Admin/Settings/Page.php:1992
msgctxt "Cloudflare"
msgid "Zone ID"
msgstr "ID de Zona"

#: inc/Engine/Admin/Settings/Page.php:2002
msgid "Development mode"
msgstr "Modo de desenvolvimento"

#. translators: %1$s = link opening tag, %2$s = link closing tag.
#: inc/Engine/Admin/Settings/Page.php:2004
msgid ""
"Temporarily activate development mode on your website. This setting will "
"automatically turn off after 3 hours. %1$sLearn more%2$s"
msgstr ""
"Ativa temporariamente o modo de desenvolvimento no seu site. Esta opção se "
"desativa automaticamente após 3 horas. %1$sSaiba mais%2$s"

#: inc/Engine/Admin/Settings/Page.php:2012
msgid "Optimal settings"
msgstr "Configurações ótimas"

#: inc/Engine/Admin/Settings/Page.php:2013
msgid ""
"Automatically enhances your Cloudflare configuration for speed, performance "
"grade and compatibility."
msgstr ""
"Melhora automaticamente as suas configurações no Cloudflare para velocidade,"
" desempenho e compatibilidade."

#: inc/Engine/Admin/Settings/Page.php:2021
msgid "Relative protocol"
msgstr "Protocolo relativo"

#: inc/Engine/Admin/Settings/Page.php:2022
msgid ""
"Should only be used with Cloudflare's flexible SSL feature. URLs of static "
"files (CSS, JS, images) will be rewritten to use // instead of http:// or "
"https://."
msgstr ""
"Só deve ser usado com a função de SSL flexível do Cloudflare. URLs de "
"arquivos estáticos (CSS, JS, imagens) serão reescritos para usar // ao invés"
" de http:// ou https://."

#: inc/Engine/Admin/Settings/Page.php:2060
msgid "Sucuri credentials"
msgstr "Credenciais Sucuri"

#: inc/Engine/Admin/Settings/Page.php:2073
msgctxt "Sucuri"
msgid ""
"Firewall API key (for plugin), must be in format {32 characters}/{32 "
"characters}:"
msgstr ""
"Chave da API de Firewall (para o plugin), deve ser no formato  {32 "
"caracteres}/{32 caracteres}:"

#: inc/Engine/Admin/Settings/Page.php:2074
msgctxt "Sucuri"
msgid "Find your API key"
msgstr "Encontre a sua chave da API"

#: inc/Engine/Admin/Settings/Settings.php:361
msgid ""
"Sucuri Add-on: The API key for the Sucuri firewall must be in format "
"<code>{32 characters}/{32 characters}</code>."
msgstr ""
"Complemento Sucuri. A chave da API para o firewall Sucuri precisa estar no "
"formato <code>{32 caracteres}/{32 caracteres}</code>."

#: inc/Engine/Admin/Settings/Settings.php:667
msgid ""
"Sorry! Adding /(.*) in Advanced Rules > Never Cache URL(s) was not saved "
"because it disables caching and optimizations for every page on your site."
msgstr ""
"Adicionando /(*) em Regras avançadas > Nunca armazenar URL(s) em cache não "
"foi salva porque ela desativa o armazenamento em cache e as otimizações para"
" cada página do seu site."

#: inc/Engine/Admin/Settings/Subscriber.php:171
msgid "Import, Export, Rollback"
msgstr "Importar, Exportar, Reverter"

#: inc/Engine/Admin/Settings/Subscriber.php:196
#: views/settings/page-sections/imagify.php:14
msgid "Image Optimization"
msgstr "Otimizar Imagens"

#: inc/Engine/Admin/Settings/Subscriber.php:197
msgid "Compress your images"
msgstr "Comprima as suas imagens"

#: inc/Engine/Admin/Settings/Subscriber.php:214
#: views/settings/page-sections/tutorials.php:48
msgid "Tutorials"
msgstr "Tutoriais"

#: inc/Engine/Admin/Settings/Subscriber.php:215
msgid "Getting started and how to videos"
msgstr "Comece a usar e vídeos didáticos"

#: inc/Engine/Cache/PurgeExpired/Subscriber.php:75
msgid "WP Rocket Expired Cache Interval"
msgstr "Intervalo de cache expirado do WP Rocket"

#: inc/Engine/Cache/WPCache.php:337
msgid "WP_CACHE value"
msgstr "Valor de WP_CACHE"

#: inc/Engine/Cache/WPCache.php:354
msgid "Cache"
msgstr "Cache"

#: inc/Engine/Cache/WPCache.php:358
msgid ""
"The WP_CACHE constant needs to be set to true for WP Rocket cache to work "
"properly"
msgstr ""
"A constante WP_Cache precisa ser definida como true para que o cache do WP "
"Rocket funcione corretamente"

#: inc/Engine/Cache/WPCache.php:367
msgid "WP_CACHE is set to true"
msgstr "WP_CACHE está definido como true"

#: inc/Engine/Cache/WPCache.php:375
msgid "WP_CACHE is not set"
msgstr "WP_CACHE não está definido"

#: inc/Engine/Cache/WPCache.php:383
msgid "WP_CACHE is set to false"
msgstr "WP_CACHE está definido como false"

#: inc/Engine/CDN/RocketCDN/AdminPageSubscriber.php:90
msgid "Next Billing Date"
msgstr "Próxima data de cobrança"

#: inc/Engine/CDN/RocketCDN/AdminPageSubscriber.php:99
msgid "No Subscription"
msgstr "Nenhuma assinatura"

#: inc/Engine/CDN/RocketCDN/AdminPageSubscriber.php:135
msgid "Your RocketCDN subscription is currently active."
msgstr "A sua assinatura da RocketCDN está ativa."

#. translators: %1$s = opening <code> tag, %2$s = CDN URL, %3$s = closing
#. </code> tag.
#: inc/Engine/CDN/RocketCDN/AdminPageSubscriber.php:141
msgid "To use RocketCDN, replace your CNAME with %1$s%2$s%3$s."
msgstr "Para usar a RocketCDN, substitua seu CNAME com %1$s%2$s%3$s."

#. translators: %1$is = opening link tag, %2$s = closing link tag.
#: inc/Engine/CDN/RocketCDN/AdminPageSubscriber.php:152
#: inc/Engine/CDN/RocketCDN/NoticesSubscriber.php:230
#: inc/Engine/CDN/RocketCDN/NoticesSubscriber.php:334
msgid "%1$sMore Info%2$s"
msgstr "%1$sMais informações%2$s"

#: inc/Engine/CDN/RocketCDN/APIClient.php:134
msgid ""
"We could not fetch the current price because RocketCDN API returned an "
"unexpected error code."
msgstr ""
"Não foi possível caoletar o preço atual porque a API do RocketCDN retornou "
"um código de erro inesperado."

#: inc/Engine/CDN/RocketCDN/APIClient.php:140
msgid "RocketCDN is not available at the moment. Please retry later."
msgstr ""
"A RocketCDN não está disponível no momento. Tente novamente mais tarde."

#: inc/Engine/CDN/RocketCDN/APIClient.php:177
msgid "RocketCDN cache purge failed: Missing identifier parameter."
msgstr ""
"Falha esvaziando o cache da RocketCDN: faltando parâmetro identificador."

#: inc/Engine/CDN/RocketCDN/APIClient.php:186
msgid "RocketCDN cache purge failed: Missing user token."
msgstr "Falha esvaziando o cache da RocketCDN: token de usuário faltando."

#: inc/Engine/CDN/RocketCDN/APIClient.php:212
msgid ""
"RocketCDN cache purge failed: The API returned an unexpected response code."
msgstr ""
"Falha esvaziando o cache da RocketCDN: a API retornou um código inesperado "
"de resposta."

#: inc/Engine/CDN/RocketCDN/APIClient.php:221
msgid "RocketCDN cache purge failed: The API returned an empty response."
msgstr ""
"Falha esvaziando o cache da RocketCDN: a API retornou uma resposta vazia."

#: inc/Engine/CDN/RocketCDN/APIClient.php:230
msgid "RocketCDN cache purge failed: The API returned an unexpected response."
msgstr ""
"Falha esvaziando o cache da RocketCDN: a API retornou uma resposta "
"inesperada."

#. translators: %s = message returned by the API.
#: inc/Engine/CDN/RocketCDN/APIClient.php:239
msgid "RocketCDN cache purge failed: %s."
msgstr "Falha esvaziando o cache da RocketCDN: %s."

#: inc/Engine/CDN/RocketCDN/APIClient.php:247
msgid "RocketCDN cache purge successful."
msgstr "O cache da RocketCDN foi esvaziado com suesso."

#: inc/Engine/CDN/RocketCDN/RESTSubscriber.php:137
#: tests/Integration/inc/Engine/CDN/RocketCDN/RESTSubscriber/enable.php:72
#: tests/Integration/inc/Engine/CDN/RocketCDN/RESTSubscriber/registerEnableRoute.php:302
msgid "RocketCDN enabled"
msgstr "RocketCDN ativa"

#: inc/Engine/CDN/RocketCDN/RESTSubscriber.php:160
#: tests/Integration/inc/Engine/CDN/RocketCDN/RESTSubscriber/disable.php:31
#: tests/Integration/inc/Engine/CDN/RocketCDN/RESTSubscriber/registerDisableRoute.php:147
msgid "RocketCDN disabled"
msgstr "RocketCDN inativa"

#. Translators: %s = date formatted using date_i18n() and get_option(
#. 'date_format' ).
#: inc/Engine/CDN/RocketCDN/views/cta-big.php:27
msgid "Valid until %s only!"
msgstr "Válido apenas até %s! "

#: inc/Engine/CDN/RocketCDN/views/cta-big.php:36
msgid "Speed up your website thanks to:"
msgstr "Acelere o seu site graças a:"

#. translators: %1$s = opening strong tag, %2$s = closing strong tag.
#: inc/Engine/CDN/RocketCDN/views/cta-big.php:42
msgid ""
"High performance Content Delivery Network (CDN) with %1$sunlimited "
"bandwidth%2$s"
msgstr ""
"Rede de Entrega de Conteúdo (CDN) de alto desempenho com %1$slargura de "
"banda ilimitada%2$s"

#. translators: %1$s = opening strong tag, %2$s = closing strong tag.
#: inc/Engine/CDN/RocketCDN/views/cta-big.php:48
msgid ""
"Easy configuration: the %1$sbest CDN settings%2$s are automatically applied"
msgstr ""
"Configuração fácil: as %1$smelhores configurações da CDN%2$s são aplicadas "
"automaticamente"

#. translators: %1$s = opening strong tag, %2$s = closing strong tag.
#: inc/Engine/CDN/RocketCDN/views/cta-big.php:54
msgid ""
"WP Rocket integration: the CDN option is %1$sautomatically configured%2$s in"
" our plugin"
msgstr ""
"Integração do WP Rocket: a opção da CDN é %1$sconfigurada "
"automaticamente%2$s no nosso plugin"

#: inc/Engine/CDN/RocketCDN/views/cta-big.php:58
msgid "Learn more about RocketCDN"
msgstr "Saiba mais sobre a RocketCDN"

#. translators: %1$s = discounted price, %2$s = regular price.
#: inc/Engine/CDN/RocketCDN/views/cta-big.php:65
msgid ""
"*$%1$s/month for 12 months then $%2$s/month. You can cancel your "
"subscription at any time."
msgstr ""
"*$%1$s/mês por 12 meses e então $%2$s/mês. Você pode cancelar a sua "
"assinatura a qualquer momento."

#: inc/Engine/CDN/RocketCDN/views/cta-big.php:86
msgid "Billed monthly"
msgstr "Cobrados mensalmente"

#: inc/Engine/CDN/RocketCDN/views/cta-big.php:87
msgid "Get Started"
msgstr "Começar"

#: inc/Engine/CDN/RocketCDN/views/cta-big.php:92
msgid "Reduce this banner"
msgstr "Reduzir este banner"

#: inc/Engine/CDN/RocketCDN/views/cta-small.php:17
msgid ""
"Speed up your website with RocketCDN, WP Rocket’s Content Delivery Network."
msgstr ""
"Acelere o seu site com a RocketCDN, a Rede de Distribuição de Conteúdo do WP"
" Rocket."

#: inc/Engine/CDN/RocketCDN/views/cta-small.php:20
#: inc/Engine/CDN/RocketCDN/views/promote-notice.php:13
msgid "Learn More"
msgstr "Saiba mais"

#: inc/Engine/CDN/RocketCDN/views/dashboard-status.php:23
msgid "RocketCDN is unavailable on local domains and staging sites."
msgstr "A RocketCDN não funciona em domínios locais e sites de staging."

#: inc/Engine/CDN/RocketCDN/views/dashboard-status.php:32
msgid "Get RocketCDN"
msgstr "Obtenha a RocketCDN"

#: inc/Engine/CDN/RocketCDN/views/promote-notice.php:11
msgid "New!"
msgstr "Novidade!"

#: inc/Engine/CDN/RocketCDN/views/promote-notice.php:12
msgid ""
"Speed up your website with RocketCDN, WP Rocket’s Content Delivery Network!"
msgstr ""
"Acelere o seu site com a RocketCDN, a Rede de Distribuição de Conteúdo do WP"
" Rocket!"

#: inc/Engine/Common/JobManager/Cron/Subscriber.php:180
msgid "WP Rocket process pending jobs"
msgstr "O WP Rocket processa os jobs pendentes"

#: inc/Engine/Common/JobManager/Cron/Subscriber.php:194
msgid "WP Rocket clear failed jobs"
msgstr "O WP Rocket limpa os jobs que falharem"

#: inc/Engine/Common/JobManager/Cron/Subscriber.php:206
msgid "WP Rocket process on submit jobs"
msgstr "O WP Rocket processa os jobs no envio"

#: inc/Engine/Common/Queue/RUCSSQueueRunner.php:265
msgid "Every minute"
msgstr "A cada minuto"

#: inc/Engine/CriticalPath/Admin/Admin.php:264
msgid "Regenerate Critical Path CSS"
msgstr "Regenerar o Caminho do CSS Crítico"

#: inc/Engine/CriticalPath/Admin/Post.php:144
#: views/cpcss/metabox/generate.php:47
msgid "Generate Specific CPCSS"
msgstr "Gerar CPCSS específico"

#: inc/Engine/CriticalPath/Admin/Post.php:145
#: views/cpcss/metabox/generate.php:45
msgid "Regenerate specific CPCSS"
msgstr "Regenerar CPCSS específico"

#: inc/Engine/CriticalPath/Admin/Post.php:216
msgid "This feature is not available for non-public post types."
msgstr "Este recurso não está disponível para tipos não-públicos de post."

#: inc/Engine/CriticalPath/Admin/Post.php:219
msgid "%l to use this feature."
msgstr "%l para usar este recurso."

#. translators: %s = post type.
#: inc/Engine/CriticalPath/Admin/Post.php:222
msgid "Publish the %s"
msgstr "Publicar o %s"

#. translators: %s = post type.
#: inc/Engine/CriticalPath/Admin/Post.php:223
msgid "Enable Load CSS asynchronously in WP Rocket settings"
msgstr "Ativa o carregamento assíncrono do CSS nas configurações do WP Rocket"

#: inc/Engine/CriticalPath/Admin/Post.php:224
msgid "Enable Load CSS asynchronously in the options above"
msgstr "Ativa o carregamento assíncrono do CSS nas opções acima"

#. translators: %1$s = type of content, %2$s = error message.
#: inc/Engine/CriticalPath/APIClient.php:64
msgid "Critical CSS for %1$s not generated. Error: %2$s"
msgstr "O CSS crítico para %1$s não foi gerado. Erro: %2$s"

#. translators: %s = item URL.
#: inc/Engine/CriticalPath/APIClient.php:170
msgid ""
"Critical CSS for %1$s on mobile not generated. Error: The API returned an "
"empty response."
msgstr ""
"O CSS crítico de %1$s para dispositivos móveis não foi gerado. Erro: a API "
"retornou uma resposta vazia."

#. translators: %s = item URL.
#: inc/Engine/CriticalPath/APIClient.php:173
msgid ""
"Critical CSS for %1$s not generated. Error: The API returned an empty "
"response."
msgstr ""
"O CSS crítico de %1$s não foi gerado. Erro: a API retornou uma resposta "
"vazia."

#. translators: %s = item URL.
#: inc/Engine/CriticalPath/APIClient.php:185
msgid "Critical CSS for %1$s on mobile not generated."
msgstr "O CSS crítico de %1$s para dispositivos móveis não foi gerado."

#. translators: %s = item URL.
#. translators: %1$s = item URL or item type.
#: inc/Engine/CriticalPath/APIClient.php:187
#: inc/Engine/CriticalPath/ProcessorService.php:194
msgid "Critical CSS for %1$s not generated."
msgstr "O CSS crítico para %1$s não foi gerado. "

#. translators: %s = URL.
#: inc/Engine/CriticalPath/APIClient.php:195
msgid ""
"Critical CSS for %1$s on mobile not generated. Error: The API returned an "
"invalid response code."
msgstr ""
"O CSS crítico de %1$s para dispositivos móveis não foi gerado. Erro: a API "
"retornou um código de resposta inválido."

#. translators: %s = URL.
#: inc/Engine/CriticalPath/APIClient.php:197
msgid ""
"Critical CSS for %1$s not generated. Error: The API returned an invalid "
"response code."
msgstr ""
"O CSS crítico para %1$snão foi gerado. Erro: a API retornou um código "
"inválido de resposta. "

#. translators: %1$s = error message.
#: inc/Engine/CriticalPath/APIClient.php:205
#: inc/Engine/CriticalPath/ProcessorService.php:201
msgid "Error: %1$s"
msgstr "Erro: %1$s"

#: inc/Engine/CriticalPath/CriticalCSSSubscriber.php:177
msgid "Critical CSS generation is currently running."
msgstr "A geração do CSS crítico está sendo executada."

#. Translators: %1$s = opening link tag, %2$s = closing link tag.
#: inc/Engine/CriticalPath/CriticalCSSSubscriber.php:182
msgid "Go to the %1$sWP Rocket settings%2$s page to track progress."
msgstr ""
"Vá até as %1$sConfigurações do WP Rocket%2$s para acompanhar o progresso."

#. Translators: %1$d = number of critical CSS generated, %2$d = total number
#. of critical CSS to generate.
#: inc/Engine/CriticalPath/CriticalCSSSubscriber.php:397
msgid ""
"Critical CSS generation is currently running: %1$d of %2$d page types "
"completed. (Refresh this page to view progress)"
msgstr ""
"A geração do CSS crítico está sendo executada: %1$d de %2$d tipos de páginas"
" concluídos. (Atualize a página para ver o progresso)"

#. Translators: %1$d = number of critical CSS generated, %2$d = total number
#. of critical CSS to generate.
#: inc/Engine/CriticalPath/CriticalCSSSubscriber.php:473
msgid "Critical CSS generation finished for %1$d of %2$d page types."
msgstr "Geração de CSS crítico concluída para %1$d de %2$d tipos de página."

#: inc/Engine/CriticalPath/CriticalCSSSubscriber.php:480
msgid "Critical CSS generation encountered one or more errors."
msgstr "A geração de CSS crítico encontrou um ou mais erros."

#: inc/Engine/CriticalPath/CriticalCSSSubscriber.php:480
msgid "Learn more."
msgstr "Saiba mais."

#. translators: %1$ = opening bold tag, %2$ = closing bold tag.
#: inc/Engine/CriticalPath/CriticalCSSSubscriber.php:841
msgid ""
"We highly recommend the %1$supdated Remove Unused CSS%2$s for a better CSS "
"optimization. Load CSS Asynchronously is always available as a back-up."
msgstr ""
"Recomendamos o %1$sRemover o CSS não usado atualizado%2$s para uma melhor "
"otimização do CSS. Carregar o CSS de Forma Assíncrona sempre está disponível"
" como um backup."

#: inc/Engine/CriticalPath/CriticalCSSSubscriber.php:846
msgid "Stay with the old option"
msgstr "Ficar com a opoção antiga"

#. translators: %s = item URL.
#: inc/Engine/CriticalPath/DataManager.php:68
msgid ""
"Critical CSS for %1$s on mobile not generated. Error: The destination folder"
" could not be created."
msgstr ""
"O CSS crítico para %1$s para dispositivos móveis não foi gerado. Erro: o "
"diretório de destino não pôde ser criado."

#. translators: %s = item URL.
#: inc/Engine/CriticalPath/DataManager.php:71
msgid ""
"Critical CSS for %1$s not generated. Error: The destination folder could not"
" be created."
msgstr ""
"O CSS crítico para %1$s não foi gerado. Erro: o diretório de destino não "
"pôde ser criado."

#: inc/Engine/CriticalPath/DataManager.php:106
msgid "Critical CSS file for mobile does not exist"
msgstr "O arquivo de CSS crítico para dispositivos móveis não existe"

#: inc/Engine/CriticalPath/DataManager.php:108
msgid "Critical CSS file does not exist"
msgstr "O arquivo do CSS crítico não existe"

#: inc/Engine/CriticalPath/DataManager.php:120
msgid "Critical CSS file for mobile cannot be deleted"
msgstr ""
"O arquivo do CSS crítico para dispositivos móveis não pode ser excluído"

#: inc/Engine/CriticalPath/DataManager.php:122
msgid "Critical CSS file cannot be deleted"
msgstr "O arquivo do CSS crítico não pode ser excluído"

#. translators: %1$s = item URL or item type.
#: inc/Engine/CriticalPath/ProcessorService.php:187
msgid "Mobile Critical CSS for %1$s not generated."
msgstr "O CSS crítico de %1$s para dispositivos móveis não foi gerado"

#. translators: %1$s = Item URL or item type.
#: inc/Engine/CriticalPath/ProcessorService.php:228
msgid "Critical CSS for %s in progress."
msgstr "O CSS crítico para %s está em execução."

#. translators: %1$s = Item URL or item type.
#: inc/Engine/CriticalPath/ProcessorService.php:262
msgid "Mobile Critical CSS for %s generated."
msgstr "Gerado o CSS crítico de %s para dispositivos móveis."

#. translators: %1$s = Item URL or item type.
#: inc/Engine/CriticalPath/ProcessorService.php:273
msgid "Critical CSS for %s generated."
msgstr "Foi gerado o CSS crítico para %s."

#: inc/Engine/CriticalPath/ProcessorService.php:295
msgid "Critical CSS file deleted successfully."
msgstr "O arquivo de CSS crítico foi excluído com sucesso."

#. translators: %1$s = Item URL or item type.
#: inc/Engine/CriticalPath/ProcessorService.php:317
msgid "Mobile Critical CSS for %1$s timeout. Please retry a little later."
msgstr ""
"O tempo de execução para o CSS crítico para dispositivos móveis de %1$s foi "
"ultrapassado. Tente um pouco mais tarde."

#. translators: %1$s = Item URL or item type.
#: inc/Engine/CriticalPath/ProcessorService.php:330
msgid "Critical CSS for %1$s timeout. Please retry a little later."
msgstr ""
"O tempo de execução para o CSS crítico de %1$s foi ultrapassado. Tente um "
"pouco mais tarde."

#: inc/Engine/CriticalPath/RESTWP.php:141
msgid "Mobile CPCSS generation not enabled."
msgstr "A geração do CPCSS para dispositivos móveis não está ativa."

#: inc/Engine/CriticalPath/RESTWPPost.php:36
#: inc/Engine/CriticalPath/RESTWPPost.php:69
msgid "Requested post does not exist."
msgstr "O post solicitado não existe."

#: inc/Engine/CriticalPath/RESTWPPost.php:46
msgid "Cannot generate CPCSS for unpublished post."
msgstr "Não foi possível erar o CPCSS para post não publicado."

#: inc/Engine/HealthCheck/HealthCheck.php:142
msgid "Scheduled Cache Purge"
msgstr "Esvaziamento agendado do cache"

#: inc/Engine/HealthCheck/HealthCheck.php:143
msgid "Scheduled Database Optimization"
msgstr "Otimização agendada do banco de dados"

#: inc/Engine/HealthCheck/HealthCheck.php:144
msgid "Database Optimization Process"
msgstr "Processo de otimização do banco de dados"

#: inc/Engine/HealthCheck/HealthCheck.php:145
msgctxt "noun"
msgid "Preload"
msgstr "Pré-Carregar"

#: inc/Engine/HealthCheck/HealthCheck.php:146
msgid "Critical Path CSS Generation Process"
msgstr "Processo de geração do CSS do caminho crítico"

#. translators: %1$s = <strong>, %2$s = price, %3$s = </strong>.
#: inc/Engine/License/Renewal.php:76
msgid "Renew before it is too late, you will only pay %1$s%2$s%3$s!"
msgstr "Renove antes que seja tarde, você irá pagar apenas %1$s%2$s%3$s!"

#. translators: %1$s = <strong>, %2$s = discount, %3$s = </strong>,%4$s =
#. <strong>, %5$s = price, %6$s=</strong>.
#: inc/Engine/License/Renewal.php:85
msgid ""
"Renew with a %1$s%2$s discount%3$s before it is too late, you will only pay "
"%4$s%5$s%6$s!"
msgstr ""
"Renove com um desconto %1$s%2$s de %3$s antes que seja tarde, você irá pagar"
" apenas %4$s%5$s%6$s!"

#. translators: %1$s = <strong>, %2$s = </strong>, %3$s = price.
#: inc/Engine/License/Renewal.php:139
msgid "Renew your license for 1 year now at %1$s%3$s%2$s."
msgstr "Renove agora a sua licença por 1 ano em%1$s%3$s%2$s."

#. translators: %1$s = <strong>, %2$s = </strong>, %3$s = discount percentage,
#. %4$s = price.
#: inc/Engine/License/Renewal.php:152
msgid ""
"Renew your license for 1 year now and get %1$s%3$s OFF%2$s immediately: you "
"will only pay %1$s%4$s%2$s!"
msgstr ""
"Renove agora a sua licença por 1 ano e obtenha %1$s%3$sGRÁTIS%2$s "
"imediatamente: você irá pagar apenas %1$s%4$s%2$s!"

#. translators: %1$s = <strong>, %2$s = </strong>, %3$s = discount price.
#: inc/Engine/License/Renewal.php:218
msgid "Renew before it is too late, you will pay %1$s%3$s%2$s."
msgstr "Renove antes que seja tarde demais, você pagará %1$s%3$s%2$s."

#. translators: %1$s = <strong>, %2$s = discount percentage, %3$s = </strong>,
#. %4$s = discount price.
#: inc/Engine/License/Renewal.php:227
msgid ""
"Renew with a %1$s%2$s discount%3$s before it is too late, you will only pay "
"%1$s%4$s%3$s!"
msgstr ""
"Renove com %1$s%2$s de desconto %3$s antes que seja tarde demais, você só "
"pagará %1$s%4$s%3$s!"

#. translators: %1$s = <a>, %2$s = </a>.
#: inc/Engine/License/Renewal.php:542
msgid ""
"You need a valid license to continue using this feature. %1$sRenew now%2$s "
"before losing access."
msgstr ""
"Você precisa de uma licença válida para continuar usando este recurso. "
"%1$sRenove agora%2$s antes de perder o acesso."

#. translators: %1$s = <a>, %2$s = </a>.
#: inc/Engine/License/Renewal.php:563
msgid "You need an active license to enable this option. %1$sRenew now%2$s."
msgstr ""
"Você precisa de uma licença ativa para ativar esta opção. %1$sRenove "
"agora%2$s."

#. translators: %1$s = <a>, %2$s = </a>.
#: inc/Engine/License/Renewal.php:591
msgid "You need an active license to enable this option. %1$sMore info%2$s."
msgstr ""
"Você precisa de uma licença ativa para ativar esta opção. %1$sMais "
"informações%2$s."

#. translators: %1$s = promotion name, %2$s = <br>, %3$s = <strong>, %4$s =
#. promotion discount percentage, %5$s = </strong>.
#: inc/Engine/License/Upgrade.php:251
msgid ""
"Take advantage of %1$s to speed up more websites:%2$s get a %3$s%4$s off%5$s"
" for %3$supgrading your license to Plus or Infinite!%5$s"
msgid_plural ""
"Take advantage of %1$s to speed up more websites:%2$s get a %3$s%4$s off%5$s"
" for %3$supgrading your license to Infinite!%5$s"
msgstr[0] ""
"Beneficie-se do %1$s para acelerar mais websites: %2$s te dá %3$s %4$s de "
"desconto %5$s para %3$s atualizando a sua licença para Infinita!%5$s"
msgstr[1] ""
"Beneficie-se do %1$s para acelerar mais websites:%2$s te dá %3$s%4$s de "
"desconto%5$s para %3$satualizando a sua licença para Infinita!%5$s"
msgstr[2] ""
"Beneficie-se do %1$s para acelerar mais websites:%2$s te dá %3$s%4$s de "
"desconto%5$s para %3$satualizando a sua licença para Infinita!%5$s"

#: inc/Engine/License/Upgrade.php:382 inc/Engine/License/Upgrade.php:405
msgid "Unlimited"
msgstr "Ilimitado"

#. translators: %s = promotion discount percentage.
#: inc/Engine/License/views/promo-banner.php:16
msgid "%s off"
msgstr "%sdesligado"

#. translators: %s = promotion name.
#: inc/Engine/License/views/promo-banner.php:21
msgid "%s promotion is live!"
msgstr "Promoção %sao vivo!"

#: inc/Engine/License/views/promo-banner.php:27
msgid "Hurry Up! Deal ends in:"
msgstr "Corra! A oferta termina em:"

#: inc/Engine/License/views/promo-banner.php:31
#: inc/Engine/License/views/renewal-soon-banner.php:14
msgid "Minutes"
msgstr "Minutos"

#: inc/Engine/License/views/promo-banner.php:32
#: inc/Engine/License/views/renewal-soon-banner.php:15
msgid "Seconds"
msgstr "Segundos"

#: inc/Engine/License/views/promo-banner.php:34
#: inc/Engine/License/views/upgrade-section.php:11
msgid "Upgrade now"
msgstr "Atualize agora"

#: inc/Engine/License/views/renewal-expired-banner-ocd-disabled.php:11
msgid "The Optimize CSS Delivery feature is disabled."
msgstr "O recurso de Otimizar a Entrega do CSS está desativado."

#: inc/Engine/License/views/renewal-expired-banner-ocd-disabled.php:15
msgid ""
"You can no longer use the Remove Unused CSS or Load CSS asynchronously "
"options."
msgstr ""
"Você não pode mais usar as opções de Remover o CSS Não Usado ou Carregar o "
"CSS de forma assíncrona."

#. translators: %1$s = <strong>, %2$s = </strong>.
#: inc/Engine/License/views/renewal-expired-banner-ocd-disabled.php:20
msgid ""
"You need an %1$sactive license%2$s to keep optimizing your CSS delivery, "
"which addresses a PageSpeed Insights recommendation and improves your page "
"performance."
msgstr ""
"Você precisa de uma %1$slicença ativa%2$s para manter a otimização da "
"entrega do seu CSS, que aciona uma recomendação do PageSpeed Insights e "
"melhora o desempenho da sua página."

#: inc/Engine/License/views/renewal-expired-banner-ocd-disabled.php:29
#: inc/Engine/License/views/renewal-expired-banner-ocd.php:39
#: inc/Engine/License/views/renewal-expired-banner.php:27
#: inc/Engine/License/views/renewal-soon-banner.php:31
msgid "Renew now"
msgstr "Renove agora"

#: inc/Engine/License/views/renewal-expired-banner-ocd.php:11
msgid "You will soon lose access to some features."
msgstr "Em breve você perderá o acesso a alguns recursos."

#. translators: %1$s = <strong>, %2$s = </strong>.
#: inc/Engine/License/views/renewal-expired-banner-ocd.php:18
msgid ""
"You need an %1$sactive license to continue optimizing your CSS delivery%2$s."
msgstr ""
"Você precisa de uma %1$slicença ativa para continuar otimizando a entrega do"
" seu CSS%2$s."

#: inc/Engine/License/views/renewal-expired-banner-ocd.php:24
msgid ""
"The Remove Unused CSS and Load CSS asynchronously features are great options"
" to address the PageSpeed Insights recommendations and improve your website "
"performance."
msgstr ""
"Os recursos de Remover o CSS Não Usado e Carregar o CSS de forma assíncrona "
"são ótimas opções para acionar recomendações do PageSpeed Insights e "
"melhorar o desempenho do seu site."

#. translators: %1$s = <strong>, %2$s = </strong>, %3$s = date.
#: inc/Engine/License/views/renewal-expired-banner-ocd.php:29
msgid "These features will be %1$sautomatically disabled on %3$s%2$s."
msgstr "Estes recursos serão %1$s desativados automaticamente em %3$s%2$s."

#: inc/Engine/License/views/renewal-expired-banner.php:11
msgid "Your WP Rocket license is expired!"
msgstr "A sua licença do WP Rocket está expirada!"

#. translators: %1$s = <strong>, %2$s = </strong>.
#: inc/Engine/License/views/renewal-expired-banner.php:18
msgid ""
"Your website could be much faster if it could take advantage of our %1$snew "
"features and enhancements%2$s. 🚀"
msgstr ""
"O seu site pode ser muito mais rápido ao tirar vantagem dos nossos %1$snovos"
" recursos e melhorias%2$s. 🚀"

#. translators: %1$s = <strong>, %2$s = </strong>.
#: inc/Engine/License/views/renewal-soon-banner.php:22
msgid ""
"Your %1$sWP Rocket license is about to expire%2$s: you will soon lose access"
" to product updates and support."
msgstr ""
"A sua %1$slicença do WP Rocket está para expirar%2$s: em breve você perderá "
"o acesso às atualizações do produto e ao suporte."

#: inc/Engine/License/views/upgrade-popin.php:12
msgid "Speed Up More Websites"
msgstr "Acelere mais websites"

#. translators: %1$s = opening strong tag, %2$s = closing strong tag.
#: inc/Engine/License/views/upgrade-popin.php:19
msgid ""
"You can use WP Rocket on more websites by upgrading your license. To "
"upgrade, simply pay the %1$sprice difference%2$s between your current and "
"new licenses, as shown below."
msgstr ""
"Você pode usar o WP Rocket em mais sites atualizando a sua licença. Para "
"atualizar, asta pagar a diferença de preço de %1$s para %2$s entre a sua "
"licença atual e a nova, como mostrado abaixo."

#. translators: %1$s = opening strong tag, %2$s = closing strong tag.
#: inc/Engine/License/views/upgrade-popin.php:25
msgid ""
"%1$sN.B.%2$s: Upgrading your license does not change your expiration date"
msgstr ""
"%1$s N.B. %2$s: Atualizando a sua licença não altera a sua data de expiração"
"  "

#. translators: %s = price.
#: inc/Engine/License/views/upgrade-popin.php:35
msgid "Save $%s"
msgstr "Salvar $ %s"

#. translators: %s = number of websites.
#: inc/Engine/License/views/upgrade-popin.php:48
msgid "%s websites"
msgstr "%s websites"

#. translators: %s = license name.
#: inc/Engine/License/views/upgrade-popin.php:54
msgid "Upgrade to %s"
msgstr "Atualize para %s"

#: inc/Engine/License/views/upgrade-section.php:11
msgid ""
"You can use WP Rocket on more websites by upgrading your license (you will "
"only pay the price difference between your current and new licenses)."
msgstr ""
"Você pode usar o WP Rocket em mais sites atualizando a sua licença (você só "
"paga a diferença de preço entre a sua licença atual e a nova)."

#. translators: %1$s = plugin name.
#: inc/Engine/Media/AboveTheFold/Admin/Controller.php:143
msgid "%1$s: Critical images cleared!"
msgstr "%1$s: Imagens críticas esvaziadas!"

#: inc/Engine/Media/Lazyload/AdminSubscriber.php:62
msgid "LazyLoad for images"
msgstr "LazyLoad para imagens"

#: inc/Engine/Media/Lazyload/AdminSubscriber.php:63
msgid "LazyLoad for iframes/videos"
msgstr "LazyLoad para iframes/vídeos"

#: inc/Engine/Media/Lazyload/CSS/Admin/Subscriber.php:48
msgid "LazyLoad CSS backgrounds"
msgstr "LazyLoad nos fundos do CSS"

#: inc/Engine/Optimization/DelayJS/Admin/SiteList.php:195
msgid "Analytics & Ads"
msgstr "Analíticas e Anúncios"

#: inc/Engine/Optimization/DelayJS/Admin/SiteList.php:200
msgid "Plugins"
msgstr "Plugins"

#: inc/Engine/Optimization/DelayJS/Admin/SiteList.php:205
msgid "Themes"
msgstr "Temas"

#: inc/Engine/Optimization/DynamicLists/DynamicLists.php:101
msgid ""
"You need an active license to get the latest version of the lists from our "
"server."
msgstr ""
"Você precisa de uma licença ativa para obter a última versão das listas do "
"nosso servidor."

#: inc/Engine/Optimization/DynamicLists/DynamicLists.php:116
msgid "Could not get updated lists from server."
msgstr "Não foi possível obter as listas atualizadas do servidor."

#: inc/Engine/Optimization/DynamicLists/DynamicLists.php:125
msgid "Lists are up to date."
msgstr "As listas estão atualizadas."

#: inc/Engine/Optimization/DynamicLists/DynamicLists.php:134
msgid "Could not update lists."
msgstr "Não foi possível atualizar as listas."

#: inc/Engine/Optimization/DynamicLists/DynamicLists.php:143
msgid "Lists are successfully updated."
msgstr "As listas foram atualizadas com sucesso."

#: inc/Engine/Optimization/DynamicLists/ServiceProvider.php:72
msgid "Default Lists"
msgstr "Listas Padrão"

#: inc/Engine/Optimization/DynamicLists/ServiceProvider.php:78
msgid "Delay JavaScript Execution Exclusion Lists"
msgstr "Listas de Exclusão do Retraso na Execução do JavaScript"

#: inc/Engine/Optimization/DynamicLists/ServiceProvider.php:84
msgid "Incompatible plugins Lists"
msgstr "Lista de plugins incompatíveis"

#: inc/Engine/Optimization/Minify/AdminSubscriber.php:65
msgid "Minify/combine JavaScript"
msgstr "Minificar/combinar o JavaSCript"

#: inc/Engine/Optimization/Minify/CSS/AdminSubscriber.php:150
msgid "Minify CSS"
msgstr "Minificar o CSS"

#. translators: %1$s = plugin name, %2$s = table name, %3$s = <a> open tag,
#. %4$s = </a> closing tag.
#: inc/Engine/Optimization/RUCSS/Admin/Settings.php:253
msgid ""
"%1$s: Could not create the %2$s table in the database which is necessary for"
" the Remove Unused CSS feature to work. Please reach out to %3$sour "
"support%4$s."
msgstr ""
"%1$s: Não conseguiu criar a tabela %2$s no banco de dados, que é necessária "
"para que o recurso Remoção do CSS não usado possa funcionar. Entre em "
"contato com o %3$snosso suporte%4$s. "

#. translators: %1$s = plugin name.
#: inc/Engine/Optimization/RUCSS/Admin/Subscriber.php:274
msgid "%1$s: Used CSS cache cleared!"
msgstr "%1$s: Cache do CSS usado limpo!"

#. translators: %1$s = plugin name.
#: inc/Engine/Preload/Admin/Settings.php:57
msgid ""
"%1$s: The preload service is now active. After the initial preload it will "
"continue to cache all your pages whenever they are purged. No further action"
" is needed."
msgstr ""
"%1$s: O serviço de pré-carregamento está ativo agora. Após o pré-"
"carregamento inicial ele continuará a armazenar todas as suas páginas em "
"cache cada vez que ele for esvaziado. Nenhuma outra ação é necessária."

#: inc/Engine/Preload/Cron/Subscriber.php:138
msgid "WP Rocket Preload pending jobs"
msgstr "Trabalhos pendentes do Pré-carregamento do WP Rocket"

#: inc/Engine/Preload/Cron/Subscriber.php:166
msgid "WP Rocket Preload revert stuck failed jobs"
msgstr "O pré-carregamento do WP Rocket reverte jobs empacados que falharam"

#: inc/Engine/Saas/Admin/AdminBar.php:73
#: inc/Engine/Saas/Admin/AdminBar.php:194
msgid "Clear Critical Images"
msgstr "Esvaziar Imagens Críticas"

#: inc/Engine/Saas/Admin/AdminBar.php:160
msgid "Clear Critical Images of this URL"
msgstr "Esvaziar as Imagens Críticas deste URL"

#: inc/Engine/Saas/Admin/AdminBar.php:163
msgid "Clear Used CSS of this URL"
msgstr "Limpar o CSS usado deste URL"

#: inc/Engine/Saas/Admin/AdminBar.php:193
msgid "Critical Images Cache"
msgstr "Cache de Imagens Críticas"

#: inc/Engine/Saas/Admin/AdminBar.php:197
msgid "Remove Used CSS Cache"
msgstr "Remover o cache de CSS usado"

#. translators: %1$s = plugin name, %2$s = number of seconds.
#: inc/Engine/Saas/Admin/Notices.php:104
msgid ""
"%1$s: Please wait %2$s seconds. The Remove Unused CSS service is processing "
"your pages, the plugin is optimizing LCP and the images above the fold."
msgstr ""
"%1$s: Aguarde %2$s segundos. O serviço de remoção do CSS não usado está "
"processando as suas páginas, e o plugin está otimizando o LCP e as imagens "
"acima da dobra."

#. translators: %1$s = plugin name, %2$s = number of URLs, %3$s = number of
#. seconds.
#: inc/Engine/Saas/Admin/Notices.php:147
msgid ""
"%1$s: The LCP element has been optimized, and the images above the fold were excluded from lazyload. The Used CSS of your homepage has been processed.\n"
"\t\t\t WP Rocket will continue to generate Used CSS for up to %2$s URLs per %3$s second(s)."
msgstr ""
"%1$s: O elemento LCP foi otimizado e as imagens acima da dobra foram excluídas do lazyload. O CSS Usado de sua página inicial foi processado.\n"
"\t\t\t O WP Rocket continuará gerando CSS Usado para até %2$s URLs a cada %3$s segundo(s)."

#. translators: %1$s = opening link tag, %2$s = closing link tag.
#: inc/Engine/Saas/Admin/Notices.php:160
msgid "We suggest enabling %1$sPreload%2$s for the fastest results."
msgstr ""
"Sugerimos ativar o %1$sPré-carregamento%2$s para resultados mais rápidos."

#. translators: %1$s = opening link tag, %2$s = closing link tag.
#: inc/Engine/Saas/Admin/Notices.php:170
msgid "To learn more about the process check our %1$sdocumentation%2$s."
msgstr ""
"Para aprender mais sobre o processo veja a nossa %1$sdocumentação%2$s."

#: inc/Engine/Saas/Admin/Notices.php:236
msgid ""
"We couldn't generate the used CSS because you're using a nulled version of "
"WP Rocket. You need an active license to use the Remove Unused CSS feature "
"and further improve your website's performance."
msgstr ""
"Não foi possível gerar o CSS não usado porque você está usando uma versão "
"anulada registrada do WP Rocket. Você precisa de uma licença ativa para usar"
" o recurso de Remover CSS Não Usado e melhorar ainda mais o desempenho do "
"seu site."

#. translators: %1$s = promo percentage.
#: inc/Engine/Saas/Admin/Notices.php:239
msgid "Click here to get a WP Rocket single license at %1$s off!"
msgstr ""
"Clique aqui para obter uma licença única do WP Rocket com %1$s de desconto!"

#. translators: %1$s = <a> open tag, %2$s = </a> closing tag.
#: inc/Engine/Saas/Admin/Notices.php:292
msgid ""
"It seems a security plugin or the server's firewall prevents WP Rocket from "
"accessing the SaaS features. IPs listed %1$shere in our documentation%2$s "
"should be added to your allowlists:"
msgstr ""
"Parece que um plugin de segurança ou o firewall do servidor está impedindo o"
" WP Rocket de acessar os recursos SaaS. Os IPs listados %1$saqui em nossa "
"documentação%2$s devem ser adicionados à sua lista de permitidos:"

#: inc/Engine/Saas/Admin/Notices.php:297
msgid "- In the security plugin, if you are using one"
msgstr "- No plugin de segurança, se estiver usando um."

#: inc/Engine/Saas/Admin/Notices.php:298
msgid "- In the server's firewall. Your host can help you with this"
msgstr ""
"- No firewall do servidor. Sua compania de hospedagem poderá te ajudar com "
"isso"

#: inc/functions/admin.php:21
msgid ""
"There seems to be an issue validating your license. Please see the error "
"message below."
msgid_plural ""
"There seems to be an issue validating your license. You can see the error "
"messages below."
msgstr[0] ""
"Parece que houve um problema validando a sua licensa. Por favor veja as "
"mensagens de erro abaixo."
msgstr[1] ""
"Parece que houve um problema validando a sua licença. Por favor veja a "
"mensagem de erro abaixo."
msgstr[2] ""
"Parece que houve um problema validando a sua licença. Por favor veja a "
"mensagem de erro abaixo."

#: inc/functions/admin.php:361
msgid "Server type:"
msgstr "Tipo de servidor:"

#: inc/functions/admin.php:370
msgid "PHP version number:"
msgstr "Número da versão do PHP:"

#: inc/functions/admin.php:379
msgid "WordPress version number:"
msgstr "Número da versão do WordPress:"

#: inc/functions/admin.php:388
msgid "WordPress multisite:"
msgstr "WordPress Multisite:"

#: inc/functions/admin.php:397
msgid "Current theme:"
msgstr "Tema atual:"

#: inc/functions/admin.php:406
msgid "Current site language:"
msgstr "Idioma atual do site:"

#: inc/functions/admin.php:415
msgid "Active plugins:"
msgstr "Plugins ativos:"

#: inc/functions/admin.php:418
msgid "Plugin names of all active plugins"
msgstr "Nomes de todos os plugins ativos"

#: inc/functions/admin.php:424
msgid "Anonymized WP Rocket settings:"
msgstr "Configurações anonimizadas do WP Rocket:"

#: inc/functions/admin.php:427
msgid "Which WP Rocket settings are active"
msgstr "Quais configurações do WP Rocket estão ativas"

#: inc/functions/admin.php:433
msgid "WP Rocket license type"
msgstr "Tipo de licença do WP Rocket"

#: inc/functions/options.php:429
msgid "The provided license data are not valid."
msgstr "Os dados fornecidos de licensa não são válidos."

#. Translators: %1$s = opening link tag, %2$s = closing link tag.
#: inc/functions/options.php:432
msgid "To resolve, please %1$scontact support%2$s."
msgstr "Para resolver, %1$scontate o suporte%2$s."

#. Translators: %1$s = opening em tag, %2$s = closing em tag, %3$s = opening
#. link tag, %4$s closing link tag.
#: inc/functions/options.php:491 inc/functions/options.php:530
msgid ""
"License validation failed. Our server could not resolve the request from "
"your website."
msgstr ""
"Falha na validação da licença. Nosso servidor não pôde resolver a "
"solicitação do seu website."

#. Translators: %1$s = opening em tag, %2$s = closing em tag, %3$s = opening
#. link tag, %4$s closing link tag.
#: inc/functions/options.php:491
msgid ""
"Try clicking %1$sValidate License%2$s below. If the error persists, follow "
"%3$sthis guide%4$s."
msgstr ""
"Experimente clicar em %1$sValidar a Licença%2$s abaixo. Se o erro persistir,"
" siga %3$seste guia%4$s."

#. Translators: %1$s = opening link tag, %2$s = closing link tag.
#: inc/functions/options.php:507
msgid ""
"License validation failed. You may be using a nulled version of the plugin. "
"Please do the following:"
msgstr ""
"Falha na validação da licença. Você pode estar usando uma versão "
"desatualizada do plugin. Faça o seguinte:"

#. Translators: %1$s = opening link tag, %2$s = closing link tag.
#: inc/functions/options.php:507 inc/functions/options.php:549
msgid "Login to your WP Rocket %1$saccount%2$s"
msgstr "Conecte-se à sua %1$sconta%2$s do WP Rocket"

#. Translators: %1$s = opening link tag, %2$s = closing link tag.
#: inc/functions/options.php:507 inc/functions/options.php:549
msgid "Download the zip file"
msgstr "Baixe o arquivo zip"

#. Translators: %1$s = opening link tag, %2$s = closing link tag.
#: inc/functions/options.php:507 inc/functions/options.php:549
msgid "Reinstall"
msgstr "Reinstalar"

#. Translators: %1$s = opening link tag, %2$s = closing link tag.
#: inc/functions/options.php:507
msgid ""
"If you do not have a WP Rocket account, please %1$spurchase a license%2$s."
msgstr "Se você não tem uma conta do WP Rocket, %1$scompre uma licença%2$s."

#. Translators: %1$s = opening link tag, %2$s = closing link tag.
#: inc/functions/options.php:515
msgid ""
"License validation failed. This user account does not exist in our database."
msgstr ""
"A validação da licença falhou: Esta conta de usuário não existe em nosso "
"banco de dados."

#. Translators: %1$s = opening link tag, %2$s = closing link tag.
#: inc/functions/options.php:515
msgid "To resolve, please contact support."
msgstr "Contacte o suporte para solucionar."

#. Translators: %1$s = opening link tag, %2$s = closing link tag.
#: inc/functions/options.php:523
msgid "License validation failed. This user account is blocked."
msgstr "A validação da licença falhou. Esta conta de usuário está bloqueada."

#. Translators: %1$s = opening link tag, %2$s = closing link tag.
#: inc/functions/options.php:523
msgid "Please see %1$sthis guide%2$s for more info."
msgstr "Veja %1$seste guia%2$spara mais informações."

#. Translators: %1$s = opening em tag, %2$s = closing em tag, %3$s = opening
#. link tag, %4$s closing link tag.
#: inc/functions/options.php:530
msgid ""
"Try clicking %1$sSave Changes%2$s below. If the error persists, follow "
"%3$sthis guide%4$s."
msgstr ""
"Experimente clicar abaixo em %1$sSalvar Alterações%2$s. Se o erro persistir,"
" siga %3$seste guia%4$s."

#. Translators: %1$s = opening link tag, %2$s = closing link tag.
#: inc/functions/options.php:543
msgid "Your license is not valid."
msgstr "Sua licença não é válida."

#. Translators: %1$s = opening link tag, %2$s = closing link tag.
#: inc/functions/options.php:543
msgid "Make sure you have an active %1$sWP Rocket license%2$s."
msgstr "Certifique-se de ter uma %1$slicença ativa do WP Rocket%2$s."

#. Translators: %1$s = opening link tag, %2$s = closing link tag, %3$s =
#. opening link tag.
#: inc/functions/options.php:545
msgid "You have added as many sites as your current license allows."
msgstr ""
"Você já adicionou o máximo de sites permitidos para a sua licença atual."

#. Translators: %1$s = opening link tag, %2$s = closing link tag, %3$s =
#. opening link tag.
#: inc/functions/options.php:545
msgid ""
"Upgrade your %1$saccount%2$s or %3$stransfer your license%2$s to this "
"domain."
msgstr ""
"Atualize sua %1$sconta%2$s ou %3$stransfira a sua licença%2$s para este "
"domínio."

#. Translators: %1$s = opening link tag, %2$s = closing link tag.
#: inc/functions/options.php:547
msgid "This website is not allowed."
msgstr "Este website não é permitido."

#. Translators: %1$s = opening link tag, %2$s = closing link tag.
#: inc/functions/options.php:547
msgid "Please %1$scontact support%2$s."
msgstr "Por favor %1$scontacte o suporte%2$s."

#. Translators: %1$s = opening link tag, %2$s = closing link tag.
#: inc/functions/options.php:549
msgid "This license key is not recognized."
msgstr "Esta chave de licença não foi reconhecida."

#. Translators: %1$s = opening link tag, %2$s = closing link tag.
#: inc/functions/options.php:549
msgid "If the issue persists, please %1$scontact support%2$s."
msgstr "Se o problema persistir, %1$scontacte o suporte%2$s."

#. Translators: %s = error message returned.
#: inc/functions/options.php:555
msgid "License validation failed: %s"
msgstr "A validação da licença falhou: %s"

#: inc/Logger/Logger.php:227 inc/Logger/Logger.php:257
msgid "The log file does not exist."
msgstr "O arquivo de registro não existe."

#: inc/Logger/Logger.php:233 inc/Logger/Logger.php:263
msgid "The log file could not be read."
msgstr "Não foi possível ler o arquivo de registro."

#: inc/Logger/Logger.php:250
msgid "The logs are not saved into a file."
msgstr "Os registros não foram salvos em um arquivo."

#. Translators: %s = Hosting name.
#: inc/ThirdParty/Hostings/Cloudways.php:82
msgid ""
"Varnish auto-purge will be automatically enabled once Varnish is enabled on "
"your %s server."
msgstr ""
"O auto-esvaziamento do Varnish será ativado automaticamente quando o Varnish"
" for ativado no seu servidor %s."

#. translators: %1$s = opening link tag, %2$s = closing link tag.
#: inc/ThirdParty/Hostings/Kinsta.php:159
msgid ""
"Your installation seems to be missing core Kinsta files managing Cache "
"clearing, which will prevent your Kinsta installation and WP Rocket from "
"working correctly. Please get in touch with Kinsta support through your "
"%1$sMyKinsta%2$s account to resolve this issue."
msgstr ""
"Alguns arquivos do núcleo do Kinsta responsáveis pela limpeza de Chache "
"parecem estar faltando na sua instalação, o que irá impedir que a sua "
"instalação no Kinsta e o WP Rocket funcionem corretamente. Entre em contato "
"com o suporte Kinsta em sua conta %1$sMyKinsta%2$s para resolver este "
"problema."

#. translators: %1$s = plugin name.
#: inc/ThirdParty/Plugins/CDN/Cloudflare.php:127
msgid ""
"%1$s: Cloudflare's HTTP/2 Server Push is incompatible with the features of "
"Remove Unused CSS and Combine CSS files. We strongly recommend disabling it."
msgstr ""
"%1$s: O Push do Servidor HTTP/2 do Cloudflare é incompatível com os recursos"
" de Remover o CSS Não Utilizado e Combinar arquivos de CSS. Recomendamos "
"desativá-los."

#: inc/ThirdParty/Plugins/CDN/Cloudflare.php:170
msgid ""
"Your site is using the official Cloudflare plugin. We have enabled "
"Cloudflare auto-purge for compatibility. If you have APO activated, it is "
"also compatible."
msgstr ""
"Seu site está usando o plugin oficial do Cloudflare. Ativamos o esvaziamento"
" automático do Cloudflare para compatibilidade. Se você tem o OAP ativado, "
"ele também é compatível."

#: inc/ThirdParty/Plugins/CDN/Cloudflare.php:171
msgid ""
"Cloudflare cache will be purged each time WP Rocket clears its cache to "
"ensure content is always up-to-date."
msgstr ""
"O cache do Cloudflare será esvaziado sempre que o WP Rocket esvaziar o seu "
"cache, para garantir que o conteúdo está sempre atualizado."

#. Translators: %1$s = strong opening tag, %2$s = strong closing tag.
#: inc/ThirdParty/Plugins/CDN/Cloudflare.php:215
msgid ""
"%1$sWP Rocket:%2$s You are using \"Dynamic Cookies Cache\". Cloudflare APO "
"is not yet compatible with that feature."
msgstr ""
"%1$sWP Rocket:%2$s Se estiver usando \"Cache Dinâmico de Cookies\", o OAP do"
" Cloudflare ainda não é compatível com este recurso."

#. Translators:%1$s = opening <a> tag, %2$s = closing </a> tag.
#: inc/ThirdParty/Plugins/CDN/Cloudflare.php:221
msgid ""
"You should either disable Cloudflare APO or check with the theme/plugin "
"requiring the use of “Dynamic Cookies Cache” developers for an alternative "
"way to be page-cache friendly. %1$sMore info%2$s"
msgstr ""
"Você deve ou desativar o OAP do Cloudflare, ou verificar com o desenvolvedor"
" do seu tema/plugin requerendo o uso de \"Cache Dinâmico de Cookies\" por "
"uma forma alternativa de ser amigável ao cache de páginas. %1$sMais "
"informações%2$s"

#. Translators: %1$s = strong opening tag, %2$s = strong closing tag, %3$s =
#. opening <a> tag, %4$s = closing </a> tag, %5$s = opening <a> tag.
#: inc/ThirdParty/Plugins/CDN/Cloudflare.php:272
msgid ""
"%1$sWP Rocket:%2$s You are using \"Separate cache files for mobile "
"devices\". You need to activate \"Cache by Device Type\" %3$ssetting%5$s on "
"Cloudflare APO to serve the right version of the cache. %4$sMore info%5$s"
msgstr ""
"%1$sWP Rocket:%2$s Você está usando \"arquivos de cache separados para "
"dispositivos móveis\". É preciso que ativee a %3$sconfiguração%5$s \"Cache "
"por Tipo de Dispositivo\" no OAP do Cloudflare para servir a versão correta "
"do cache. %4$sMais informações%5$s"

#. Translators: %1$s = strong opening tag, %2$s = strong closing tag.
#: inc/ThirdParty/Plugins/CDN/Cloudflare.php:293
msgid ""
"%1$sWP Rocket:%2$s You have \"Cache by Device Type\" enabled on Cloudflare "
"APO. If you judge it necessary for the website to have a different cache on "
"mobile and desktop, we suggest you enable our “Separate Cache Files for "
"Mobiles Devices” to ensure the generated cache is accurate."
msgstr ""
"%1$sWP Rocket:%2$s Você tem o \"Cache por Tipo de Dispositivo\" ativo no OAP"
" do Cloudflare. Se julgar necessário que o site tenha um cache separado para"
" dispositivos móveis e desktop, sugerimos que ative nosso \"Arquivos "
"Separados de Cache para Dispositivos Móveis\" para garantir que o cache "
"gerado é preciso."

#. translators: %1$s is WP Rocket plugin name, %2$s is opening <a> tag, %3$s
#. is closing </a> tag.
#: inc/ThirdParty/Plugins/ModPagespeed.php:102
msgid ""
"<strong>%1$s</strong>: Mod PageSpeed is not compatible with this plugin and "
"may cause unexpected results. %2$sMore Info%3$s"
msgstr ""
"<strong>%1$s</strong>: Mod PageSpeed não é compatível com este plugin e pode"
" causar resultados inesperados. %2$sMais informações%3$s"

#. Translators: %1$s is an opening <strong> tag; %2$s is a closing </strong>
#. tag
#: inc/ThirdParty/Plugins/Optimization/Autoptimize.php:75
msgid ""
"%1$sWP Rocket: %2$sWe have detected that Autoptimize's JavaScript "
"Aggregation feature is enabled. WP Rocket's Delay JavaScript Execution will "
"not be applied to the file it creates. We suggest disabling %1$sJavaScript "
"Aggregation%2$s to take full advantage of Delay JavaScript Execution."
msgstr ""
"%1$sWP Rocket: %2$sDetectamos que o recurso de Agregação de JavaScript do "
"Autoptimize está ativo. O Retraso na execução de JavaScript do WP Rocket não"
" será aplicado ao arquivo que ele cria. Sugerimos desativar a %1$sAgregação "
"de JavaScript%2$s para tirar vantagem do Retraso na execução de JavaScript."

#. Translators: %1$s is an opening <strong> tag; %2$s is a closing </strong>
#. tag
#: inc/ThirdParty/Plugins/Optimization/Autoptimize.php:130
msgid ""
"%1$sWP Rocket: %2$sWe have detected that Autoptimize's Aggregate Inline CSS "
"feature is enabled. WP Rocket's Load CSS Asynchronously will not work "
"correctly. We suggest disabling %1$sAggregate Inline CSS%2$s to take full "
"advantage of Load CSS Asynchronously Execution."
msgstr ""
"%1$sWP Rocket: %2$sDetectamos que o Agregar o CSS inline do Autoptimize está"
" ativa. O Carregamento assíncrono de CSS do WP Rocket não irá funcionar "
"corretamente. Sugerimos desativar a %1$sAgregar o CSS inline%2$s para tirar "
"vantagem da Execução assíncrona do carregamento  de CSS."

#. translators: %1$s = opening <a> tag, %2$s = closing </a> tag.
#: inc/ThirdParty/Plugins/Optimization/Ezoic.php:45
msgid ""
"This plugin blocks WP Rocket's caching and optimizations. Deactivate it and "
"use %1$sEzoic's nameserver integration%2$s instead."
msgstr ""
"Este plugin bloqueia o cache e otimizações do WP Rocket. Deative-o e use a "
"%1$sintegração Ezoic de nameserver%2$s ao invés dele."

#. Translators: %s = Plugin name.
#: inc/ThirdParty/Plugins/Optimization/Hummingbird.php:78
msgctxt "Hummingbird notice"
msgid ""
"Please deactivate the following %s option which conflicts with WP Rocket "
"features:"
msgid_plural ""
"Please deactivate the following %s options which conflict with WP Rocket "
"features:"
msgstr[0] ""
"Desative a seguinte opção %s que está causando conflitos com funções do WP "
"Rocket:"
msgstr[1] ""
"Desative as seguintes opções %s que estão causando conflitos com funções do "
"WP Rocket:"
msgstr[2] ""
"Desative as seguintes opções %s que estão causando conflitos com funções do "
"WP Rocket:"

#. Translators: %1$s = Plugin name, %2$s = <em>, %3$s = </em>.
#: inc/ThirdParty/Plugins/Optimization/Hummingbird.php:151
msgctxt "Hummingbird notice"
msgid ""
"%1$s %2$sdisable emoji%3$s conflicts with WP Rockets %2$sdisable emoji%3$s"
msgstr ""
"A %3$s de desabilitar emojis do %1$s %2$s está gerando conflito com a %2$s "
"de desabilitar emoji %3$s do WP Rocket"

#. Translators: %1$s = Plugin name, %2$s = <em>, %3$s = </em>.
#: inc/ThirdParty/Plugins/Optimization/Hummingbird.php:187
msgctxt "Hummingbird notice"
msgid ""
"%1$s %2$sGZIP compression%3$s conflicts with WP Rocket %2$sGZIP "
"compression%3$s"
msgstr ""
"A %3$s de compressão GZIP do %1$s %2$s está gerando conflito com a %3$s de "
"compressão GZIP %2$s do WP Rocket"

#. Translators: %1$s = Plugin name, %2$s = <em>, %3$s = </em>.
#: inc/ThirdParty/Plugins/Optimization/Hummingbird.php:223
msgctxt "Hummingbird notice"
msgid ""
"%1$s %2$sbrowser caching%3$s conflicts with WP Rocket %2$sbrowser "
"caching%3$s"
msgstr ""
"A %3$s de cache do navegador do %1$s %2$s está gerando conflito com a %3$s "
"de cache do navegador %2$s  do WP Rocket"

#. Translators: %1$s = Plugin name, %2$s = <em>, %3$s = </em>.
#: inc/ThirdParty/Plugins/Optimization/Hummingbird.php:255
msgctxt "Hummingbird notice"
msgid ""
"%1$s %2$spage caching%3$s conflicts with WP Rocket %2$spage caching%3$s"
msgstr ""
"A %3$s de cache de páginas do %1$s %2$s está gerando conflito com a %3$s de "
"cache de páginas %2$s do WP Rocket"

#. Translators: %1$s = Plugin name, %2$s = <em>, %3$s = </em>.
#: inc/ThirdParty/Plugins/Optimization/Hummingbird.php:289
msgctxt "Hummingbird notice"
msgid ""
"%1$s %2$sasset optimization%3$s conflicts with WP Rocket %2$sfile "
"optimization%3$s"
msgstr ""
"A %3$s de otimização de recursos do %1$s %2$s está gerando conflito com a "
"%3$s de otimização de recursos %2$s do WP Rocket"

#: inc/ThirdParty/Plugins/Optimization/Perfmatters.php:38
msgid ""
"Remove Unused CSS is currently activated in Perfmatters. If you want to use "
"WP Rocket's Remove Unused CSS feature, disable this option in Perfmatters."
msgstr ""
"Remover o CSS Não-Usado no momento está ativado no Perfmatters. Se deseja "
"usar o recurso de Remover o CSS Não-Usado do WP Rocket, desative esta opção "
"no Perfmatters."

#: inc/ThirdParty/Plugins/Optimization/RapidLoad.php:39
msgid ""
"Automated unused CSS removal is currently activated in RapidLoad Power-Up "
"for Autoptimize. If you want to use WP Rocket's Remove Unused CSS feature, "
"disable the  RapidLoad Power-Up for Autoptimize plugin."
msgstr ""
"A remoção automatizada do CSS não usado no momento está ativada no RapidLoad"
" Power-Up para o Autoptimize. Se deseja usar o recurso de Remover o CSS Não-"
"Usado do WP Rocket, desative o plugin RapidLoad Power-Up para o Autoptimize."

#. translators: %1$s = plugin name.
#: inc/ThirdParty/Plugins/Optimization/WPMeteor.php:42
msgid ""
"Delay JS is currently activated in %1$s. If you want to use WP Rocket’s "
"delay JS, disable %1$s"
msgstr ""
"Atrasar o JS está ativo no momento em %1$s. Se deseja usar o atrasar JS do "
"WP Rocket, desative %1$s"

#: inc/ThirdParty/Plugins/Smush.php:108 inc/ThirdParty/Plugins/Smush.php:126
msgid "Smush"
msgstr "Smush"

#: inc/ThirdParty/Themes/Avada.php:107
msgid "Avada"
msgstr "Avada"

#: inc/ThirdParty/Themes/Divi.php:293
msgid ""
"Your Divi template was updated. Clear the Used CSS if the layout, design or "
"CSS styles were changed."
msgstr ""
"Seu modelo Divi foi atualizado. Limpe o CSS Usado caso o layout, o design ou"
" os estilos CSS tiverem sido alterados."

#: views/cpcss/activate-cpcss-mobile.php:13
msgid "Load CSS asynchronously for mobile"
msgstr "Carrega o CSS de forma assíncrona em dispositivos móveis"

#: views/cpcss/activate-cpcss-mobile.php:14
msgid ""
"Your website currently uses the same Critical Path CSS for both desktop and "
"mobile."
msgstr ""
"O seu site atualmente usa o mesmo CSS de caminho crítico para desktop e "
"dispositivos móveis."

#: views/cpcss/activate-cpcss-mobile.php:15
msgid "Click the button to enable mobile-specific CPCSS for your site."
msgstr ""
"Clique no botão para ativar o CPCSS específico para dispositivos móveis no "
"seu site."

#. translators: %1$s = opening link tag, %2$s = closing link tag.
#: views/cpcss/activate-cpcss-mobile.php:20
#: views/settings/enable-google-fonts.php:19
#: views/settings/mobile-cache.php:20
msgid ""
"This is a one-time action and this button will be removed afterwards. "
"%1$sMore info%2$s"
msgstr ""
"Esta é uma ação única e este botão será removido em seguida. %1$sMais "
"informações%2$s"

#. translators: %1$s = opening link tag, %2$s = closing link tag.
#: views/cpcss/activate-cpcss-mobile.php:30
msgid ""
"Your site is now using mobile-specific critical path CSS. %1$sMore info%2$s"
msgstr ""
"O seu site agora esta usando CSS de caminho crítico específico para "
"dispositivos móveis. %1$sMais informações%2$s"

#: views/cpcss/activate-cpcss-mobile.php:39
msgid "Generate Mobile Specific CPCSS"
msgstr "Gerar CPCSS específico para dispositivos móveis"

#: views/cpcss/metabox/container.php:19
msgid "Critical Path CSS"
msgstr "CSS do caminho crítico"

#. translators: %1$s = opening link tag, %2$s = closing link tag.
#: views/cpcss/metabox/generate.php:23
msgid "Generate specific Critical Path CSS for this post. %1$sMore info%2$s"
msgstr ""
"Gera o CSS de caminho crítico específico para este post. %1$sMais "
"informações%2$s"

#. translators: %1$s = opening link tag, %2$s = closing link tag.
#: views/cpcss/metabox/generate.php:33
msgid "This post uses specific Critical Path CSS. %1$sMore info%2$s"
msgstr ""
"Este post usa CSS de caminho crítico específico. %1$sMais informações%2$s "

#: views/cpcss/metabox/generate.php:56
msgid "Revert back to the default CPCSS"
msgstr "Reverter ao CPCSS padrão"

#: views/deactivation-intent/form.php:21
msgid "Facing an issue?"
msgstr "Está com um problema?"

#: views/deactivation-intent/form.php:24
msgid ""
"It is not always necessary to deactivate WP Rocket when facing any issues. "
"Most of them can be fixed by deactivating only some options."
msgstr ""
"Nem sempre é necessário desativar o WP Rocket ao encontrar problemas. A "
"maioria deles pode ser corrigida desativando apenas algumas opções."

#. translators: %1$s = opening strong tag, %2$s = closing strong tag.
#: views/deactivation-intent/form.php:29
msgid ""
"Our advice? Instead of deactivating WP Rocket, use our %1$sSafe Mode%2$s to "
"quickly disable LazyLoad, File Optimization, and CDN options. Then check to "
"see if your issue is resolved."
msgstr ""
"Nosso conselho? Ao invés de desativar o WP Rocket, use o nosso %1$sModo "
"Seguro%2$s para desativar rapidamente as opções de Lazy Load, a Otimização "
"de Arquivos e CDN. Verifique então se o seu problema foi resolvido."

#: views/deactivation-intent/form.php:35
msgid "Do you want to use our Safe Mode to troubleshoot WP Rocket?"
msgstr "Deseja usar o Modo Seguro para resolver problemas com o WP Rocket?"

#. translators: %1$s = opening strong tag, %2$s = closing strong tag.
#: views/deactivation-intent/form.php:55
msgid "Yes, apply \"%1$sSafe Mode%2$s\""
msgstr "Sim, aplique o \"%1$sModo Seguro%2$s\""

#. translators: %1$s = opening strong tag, %2$s = closing strong tag.
#: views/deactivation-intent/form.php:68
msgid ""
"and export WP Rocket settings %1$s(Recommended as current settings will be "
"deleted)%2$s"
msgstr ""
"e exporte as configurações do WP Rocket %1$s (Recomendado já que as "
"configurações atuais serão apagadas) %2$s"

#: views/deactivation-intent/form.php:76
msgid "No, deactivate and snooze this message for"
msgstr "Não, desative e não mostre esta mensagem por "

#: views/deactivation-intent/form.php:78
msgid "1 day"
msgstr "1 dia"

#: views/deactivation-intent/form.php:79
msgid "7 days"
msgstr "7 dias"

#: views/deactivation-intent/form.php:80
msgid "30 days"
msgstr "30 dias"

#: views/deactivation-intent/form.php:81
msgid "Forever"
msgstr "Para sempre"

#: views/deactivation-intent/form.php:87
msgid "Cancel"
msgstr "Cancelar"

#: views/deactivation-intent/form.php:88
msgid "Confirm"
msgstr "Confirmar"

#. translators: %1$s = <strong>, %2$s = plugin version, %3$s = </strong>, %4$s
#. = <a>, %5$s = </a>, %6$s = <a>.
#: views/plugins/update-renewal-expired-notice.php:27
msgid ""
" %1$sWP Rocket %2$s%3$s is available. %4$sLearn more%5$s about the updates "
"and enhancements of this major version. You need an active license to use "
"them on your website, don’t miss out! %6$sRenew Now%5$s"
msgstr ""
"O %1$sWP Rocket%2$s %3$s está disponível. %4$sSaiba mais%5$s sobre as "
"atualizações e melhorias nesta atualização de versão. Você precisa de uma "
"licença ativa para usá-la no seu website, não perca! %6$sRenove agora%5$s"

#: views/settings/dynamic-lists-update.php:14
msgid "Update Inclusion and Exclusion Lists"
msgstr "Atualizar as Listas de Inclusão e Exclusão"

#. translators: %1$s = opening link tag, %2$s = closing link tag.
#: views/settings/dynamic-lists-update.php:19
msgid ""
"Compatibility lists are downloaded automatically every week. Click the "
"button if you want to update them manually. %1$sMore info%2$s"
msgstr ""
"As listas de compatibilidade são baixadas automaticamente a cada semana. "
"Clique o botão se quiser atualizá-las manualmente. %1$sMais informações%2$s"

#: views/settings/dynamic-lists-update.php:29
msgid "Update lists"
msgstr "Atualizar as listas"

#: views/settings/enable-google-fonts.php:13
msgid "Enable Google Font Optimization"
msgstr "Ativar a otimização das Fontes Google"

#: views/settings/enable-google-fonts.php:14
msgid ""
"Improves font performance and combines multiple font requests to reduce the "
"number of HTTP requests."
msgstr ""
"Melhora o desempenho das fontes e combina múltiplas requisições de fonte "
"para reduzir o número de requisições HTTP."

#. translators: %1$s = opening link tag, %2$s = closing link tag.
#: views/settings/enable-google-fonts.php:29
msgid ""
"Google Fonts Optimization is now enabled for your site. %1$sMore info%2$s"
msgstr ""
"A otimização das fontes Google está ativada neste site. %1$sMais "
"informações%2$s"

#: views/settings/enable-google-fonts.php:38
msgid "Optimize Google Fonts"
msgstr "Otimizar Fontes Google"

#: views/settings/fields/cache-lifespan.php:26
msgid "Clear cache after"
msgstr "Limpar o cache após"

#: views/settings/fields/cnames.php:58 views/settings/fields/cnames.php:92
#: views/settings/fields/cnames.php:122
msgid "CSS & JavaScript"
msgstr "CSS e JavaScript"

#: views/settings/fields/cnames.php:59 views/settings/fields/cnames.php:93
#: views/settings/fields/cnames.php:123
msgid "JavaScript"
msgstr "JavaScript"

#: views/settings/fields/cnames.php:60 views/settings/fields/cnames.php:94
#: views/settings/fields/cnames.php:124
msgid "CSS"
msgstr "CSS"

#: views/settings/fields/import-form.php:29
msgid "Import settings"
msgstr "Importar configurações"

#: views/settings/fields/one-click-addon.php:43
#: views/settings/fields/rocket-addon.php:43
msgid "Add-on status"
msgstr "Status do complemento"

#: views/settings/fields/rocket-addon.php:72
msgid "Modify options"
msgstr "Modificar opções"

#: views/settings/fields/rocket-cdn.php:29
#: views/settings/fields/rocket-cdn.php:41
msgid "CDN CNAME"
msgstr "CNAME do CDN"

#. translators: %s is a "Learn more" link.
#: views/settings/fields/rocket-cdn.php:62
msgid "Purges RocketCDN cached resources for your website. %s"
msgstr "Esvazia os recursos no cache da RocketCDN para o seu site. %s"

#. translators: %s is a "Learn more" link.
#: views/settings/fields/rocket-cdn.php:63
#: views/settings/page-sections/cloudflare.php:36
#: views/settings/page-sections/sucuri.php:37
msgid "Learn more"
msgstr "Saiba mais"

#: views/settings/fields/rocket-cdn.php:72
msgid "Clear all RocketCDN cache files"
msgstr "Limpar todos os arquivos do cache da RocketCDN"

#: views/settings/mobile-cache.php:11
msgid "Mobile Cache"
msgstr "Cache Móvel"

#: views/settings/mobile-cache.php:14
msgid "Speed your site for mobile visitors."
msgstr "Acelere o seu site para visitantes em dispositivos móveis."

#: views/settings/mobile-cache.php:28
msgid "Mobile Cache is now enabled for your site."
msgstr "O Cache de Dispositivos Móveis agora está ativado no seu site."

#: views/settings/mobile-cache.php:34
msgid "Enable Mobile Cache"
msgstr "Ativar Cache de Dispositivos Móveis"

#: views/settings/page-sections/cloudflare.php:27
msgid "Cloudflare Cache"
msgstr "Cache do Cloudflare"

#. translators: %s is a "Learn more" link.
#: views/settings/page-sections/cloudflare.php:35
#: views/settings/page-sections/sucuri.php:36
msgid "Purges cached resources for your website. %s"
msgstr "Esvazia os ítens em cache no seu site. %s"

#. translators: %s is a "Learn more" link.
#: views/settings/page-sections/cloudflare.php:36
msgid "https://support.cloudflare.com/hc/en-us/articles/200169246"
msgstr "https://support.cloudflare.com/hc/en-us/articles/200169246"

#: views/settings/page-sections/cloudflare.php:45
msgid "Clear all Cloudflare cache files"
msgstr "Limpar todos os arquivos do cache do Cloudflare"

#: views/settings/page-sections/dashboard.php:38
msgid "Congratulations!"
msgstr "Parabéns!"

#: views/settings/page-sections/dashboard.php:40
msgid "WP Rocket is now activated and already working for you."
msgstr "O WP Rocket está ativo e já está trabalhando para você."

#: views/settings/page-sections/dashboard.php:42
msgid "Your website should be loading faster now!"
msgstr "Seu site já deve estar carregando mais rápido!"

#: views/settings/page-sections/dashboard.php:44
msgid ""
"To guarantee fast websites, WP Rocket automatically applies 80% of web "
"performance best practices."
msgstr ""
"Para garantir sites rápidos o WP Rocket aplica automaticamente 80% das "
"melhores p'raticas de desempenho web."

#: views/settings/page-sections/dashboard.php:44
msgid ""
"We also enable options that provide immediate benefits to your website."
msgstr ""
"Também ativamos opções que permitem benefícios imediatos para o seu site."

#: views/settings/page-sections/dashboard.php:45
msgid "Continue to the options to further optimize your site!"
msgstr "Continue para as opções para otimizar ainda mais o seu site!"

#: views/settings/page-sections/dashboard.php:62
msgid "My Account"
msgstr "Minha Conta"

#: views/settings/page-sections/dashboard.php:68
msgid "Refresh info"
msgstr "Atualizar informações"

#: views/settings/page-sections/dashboard.php:86
msgid "with"
msgstr "com"

#: views/settings/page-sections/dashboard.php:100
msgid "Expiration Date"
msgstr "Data de Expiração"

#: views/settings/page-sections/dashboard.php:110
msgid "View my account"
msgstr "Ver a minha conta"

#: views/settings/page-sections/dashboard.php:137
msgid "Quick Actions"
msgstr "Ações Rápidas"

#: views/settings/page-sections/dashboard.php:144
msgid "Remove all cached files"
msgstr "Remover todos os arquivos em cache"

#: views/settings/page-sections/dashboard.php:164
#: views/settings/page-sections/dashboard.php:170
msgid "Regenerate Critical CSS"
msgstr "Regenerar CSS Crítico"

#: views/settings/page-sections/dashboard.php:196
msgid "Frequently Asked Questions"
msgstr "Dúvidas Frequentes"

#: views/settings/page-sections/dashboard.php:210
msgid "Still cannot find a solution?"
msgstr "Ainda não consegue achar uma solução?"

#: views/settings/page-sections/dashboard.php:211
msgid ""
"Submit a ticket and get help from our friendly and knowledgeable Rocketeers."
msgstr ""
"Envie um ticket e obtenha ajuda dos nossos amigos Rocketeers experientes."

#: views/settings/page-sections/dashboard.php:219
msgid "Ask support"
msgstr "Solicite suporte"

#: views/settings/page-sections/database.php:25
msgid "Backup your database before you run a cleanup!"
msgstr "Faça um backup do seu banco de dados antes de executar uma limpeza!"

#: views/settings/page-sections/database.php:26
msgid ""
"Once a database optimization has been performed, there is no way to undo it."
msgstr ""
"Uma vez que a otimização do banco de dados é feita, não há como desfazê-la."

#: views/settings/page-sections/database.php:28
msgid "Save Changes and Optimize"
msgstr "Salvar alterações e otimizar"

#. Translators: %1$s = <strong>, %2$s = </strong>, %3$s = <span
#. class="imagify-name">, %4$s = </span>.
#: views/settings/page-sections/imagify.php:21
msgid ""
"%1$sWP ROCKET%2$s created %3$sIMAGIFY%4$s %1$sfor best-in-class image "
"optimization.%2$s"
msgstr ""
"%1$sWP ROCKET%2$s criou %3$sIMAGIFY%4$s %1$spara melhor otimização de "
"imagens.%2$s"

#: views/settings/page-sections/imagify.php:24
msgid ""
"Compress image to make your website faster, all while maintaining image "
"quality."
msgstr ""
"Comprime imagens para tornar seu site mais rápido, mantendo a qualidade da "
"imagem."

#: views/settings/page-sections/imagify.php:25
msgid "More on Imagify:"
msgstr "Mais sobre o Imagify:"

#: views/settings/page-sections/imagify.php:27
msgid "Imagify Plugin Page"
msgstr "Página do Plugin Imagify"

#: views/settings/page-sections/imagify.php:28
msgid "Imagify Website"
msgstr "Website do Imagify"

#: views/settings/page-sections/imagify.php:29
msgid "Review of Image Compression Plugins"
msgstr "Análise dos Plugins de Compressão de Imagem"

#: views/settings/page-sections/imagify.php:38
msgid "Install Imagify"
msgstr "Instalar o Imagify"

#: views/settings/page-sections/license.php:22
msgid "WP Rocket was not able to automatically validate your license."
msgstr "O WP Rocket não conseguiu validar automaticamente a sua licença."

#. translators: %1$s = tutorial URL, %2$s = support URL.
#: views/settings/page-sections/license.php:29
msgid "Follow this %1$s, or contact %2$s to get the engine started."
msgstr "Siga este %1$s, ou entre em contat com %2$s para iniciar o mecanismo."

#. translators: %1$s = <a href=", %2$s =  tutorial href,  %3$s =  "
#. target="_blank">,  %4$s =  </a>.
#: views/settings/page-sections/license.php:32
msgid "%1$s%2$s%3$stutorial%4$s"
msgstr "%1$s%2$s%3$stutorial%4$s"

#: views/settings/page-sections/license.php:34
msgid ""
"https://docs.wp-rocket.me/article/100-resolving-problems-with-license-"
"validation/?utm_source=wp_plugin&utm_medium=wp_rocket"
msgstr ""
"https://docs.wp-rocket.me/article/100-resolving-problems-with-license-"
"validation/?utm_source=wp_plugin&utm_medium=wp_rocket"

#. translators: %1$s = <a href=", %2$s =  support href,  %3$s =  "
#. target="_blank">,  %4$s =  </a>.
#: views/settings/page-sections/license.php:40
msgid "%1$s%2$s%3$ssupport%4$s"
msgstr "%1$s%2$s%3$ssuporte%4$s"

#: views/settings/page-sections/sucuri.php:46
msgid "Clear all Sucuri cache files"
msgstr "Limpar todos os arquivos do cache Sucuri"

#. translators: %1$s = formatted file size, %2$s = formatted number of entries
#. (don't use %2$d).
#: views/settings/page-sections/tools.php:20
msgid "Files size: %1$s. Number of entries: %2$s."
msgstr "Tamanho do arquivo: %1$s. Número de entradas: %2$s."

#. translators: %1$s = opening <a> tag, %2$s = closing </a> tag.
#: views/settings/page-sections/tools.php:23
msgid "%1$sDownload the file%2$s."
msgstr "%1$sBaixar o arquivo%2$s."

#. translators: %1$s = opening <a> tag, %2$s = closing </a> tag.
#: views/settings/page-sections/tools.php:26
msgid "%1$sDelete the file%2$s."
msgstr "%1$sExcluir o arquivo%2$s."

#: views/settings/page-sections/tools.php:37
msgid "Export settings"
msgstr "Exportar as configurações"

#: views/settings/page-sections/tools.php:38
msgid "Download a backup file of your settings"
msgstr "Baixar um backup das suas configurações"

#: views/settings/page-sections/tools.php:46
msgid "Download settings"
msgstr "Baixar as configurações"

#: views/settings/page-sections/tools.php:60
msgid "Rollback"
msgstr "Reverter"

#. translators: %s = WP Rocket version number.
#: views/settings/page-sections/tools.php:64
msgid "Has version %s caused an issue on your website?"
msgstr "A versão %s causou algum problema no seu site?"

#. translators: %s = <br>.
#: views/settings/page-sections/tools.php:69
msgid ""
"You can rollback to the previous major version here.%sThen send us a support"
" request."
msgstr ""
"Você pode voltar atrás para a última versão estável aqui. %sEnvie em seguida"
" um pedido de suporte."

#. translators: %s = WP Rocket previous version.
#: views/settings/page-sections/tools.php:80
msgid "Reinstall version %s"
msgstr "Reinstalar a versão %s"

#: views/settings/page-sections/tools.php:106
msgid "Debug mode"
msgstr "Módo de depuração"

#: views/settings/page-sections/tools.php:111
msgid "Create a debug log file."
msgstr "Criar um arquivo de registro de depuração."

#: views/settings/page-sections/tutorials.php:13
#: views/settings/partials/getting-started.php:18
msgid "Getting Started"
msgstr "Comece a usar"

#: views/settings/page-sections/tutorials.php:15
msgid "Getting Started with WP Rocket"
msgstr "Começando a usar o WP Rocket"

#: views/settings/page-sections/tutorials.php:16
#: views/settings/partials/getting-started.php:12
msgid "Finding the Best Settings for Your Site"
msgstr "Encontrando as melhores configurações para o seu site"

#: views/settings/page-sections/tutorials.php:17
#: views/settings/partials/getting-started.php:13
msgid "How to Check if WP Rocket is Caching Your Site"
msgstr "Como verificar se o WP Rocket está fazendo cache do seu site"

#: views/settings/page-sections/tutorials.php:18
#: views/settings/partials/getting-started.php:14
msgid "How to Measure the Speed of Your Site"
msgstr "Como medir a velocidade do seu site"

#: views/settings/page-sections/tutorials.php:19
msgid "How Preloading Works"
msgstr "Como funciona o pré-carregamento"

#: views/settings/page-sections/tutorials.php:23
msgid "Passing the Core Web vitals"
msgstr "Passando os vitais do Core Web"

#: views/settings/page-sections/tutorials.php:25
msgid "How to improve LCP with WP Rocket"
msgstr "Como melhorar o LCP com o WP Rocket"

#: views/settings/page-sections/tutorials.php:26
msgid "How to improve FID with WP Rocket"
msgstr "Como melhorar o FID com o WP Rocket"

#: views/settings/page-sections/tutorials.php:27
msgid "How to improve CLS with WP Rocket"
msgstr "Como melhorar o CLS com o WP Rocket"

#: views/settings/page-sections/tutorials.php:31
msgid "Troubleshooting"
msgstr "Solução de problemas"

#: views/settings/page-sections/tutorials.php:33
msgid "Troubleshooting Display Issues with File Optimization"
msgstr "Resolvendo problemas de visualização com a otimização de arquivos"

#: views/settings/page-sections/tutorials.php:34
msgid "How to Find the Right JavaScript to Exclude"
msgstr "Como encontrar o JavaScript correto a excluir"

#: views/settings/page-sections/tutorials.php:35
msgid "How External Content Slows Your Site"
msgstr "Como o conteúdo externo deixa seus ite mais lento"

#: views/settings/page-sections/tutorials.php:41
msgid "Set Up the Cloudflare Add-on"
msgstr "Configure o complemento do Cloudflare"

#: views/settings/page.php:16
msgid "WP Rocket Settings"
msgstr "Configurações do WP Rocket"

#. translators: %s = Plugin version number.
#: views/settings/page.php:30
msgid "version %s"
msgstr "versão %s"

#: views/settings/page.php:60
msgid "Show Sidebar"
msgstr "Exibir Barra Lateral"

#. translators: %1$s = <strong>, %2$s = </strong>.
#: views/settings/page.php:82
msgid ""
"Below is a detailed view of all data WP Rocket will collect %1$sif granted "
"permission.%2$s"
msgstr ""
"Abaixo está uma visão detalhada de todos os dados que o WP Rocket irá "
"coletar %1$sse tiver a sua permissão%2$s"

#: views/settings/page.php:88
msgid ""
"WP Rocket will never transmit any domain names or email addresses (except "
"for license validation), IP addresses, or third-party API keys."
msgstr ""
"O WP Rocket jamais irá transmitir quaisquer nomes de domínio e endereços de "
"e-mail (exceto para a validação da licença), endereços IP ou chaves de API "
"de terceiros."

#: views/settings/page.php:90
msgid "Activate Rocket analytics"
msgstr "Ativar analytics do Rocket"

#: views/settings/partials/documentation.php:15
msgid "It is a great starting point to fix some of the most common issues."
msgstr "É um excelente começo solucionar alguns dos problemas mais comuns."

#: views/settings/partials/documentation.php:22
msgid "Read the documentation"
msgstr "Ler a documentação"

#: views/settings/partials/getting-started.php:11
msgid "What WP Rocket Does For You By Default"
msgstr "O que o WP Rocket faz para você por padrão"

#: views/settings/partials/sidebar.php:12
msgid "How to correctly measure your website’s loading time"
msgstr "Como medir corretamente o tempo de carregamento do seu site"

#: views/settings/partials/sidebar.php:14
msgid "Check our tutorial and learn how to measure the speed of your site."
msgstr "Veja o nosso tutorial e aprenda como medir a velocidade do seu site."

#: views/settings/partials/sidebar.php:15
#: views/settings/partials/sidebar.php:24
msgid "Read our guide"
msgstr "Leia o nosso guia"

#: views/settings/partials/sidebar.php:18
msgid "Learn about optimal WP Rocket settings for mobile."
msgstr ""
"Aprenda sobre as configurações ótimas do WP Rocket para dispositivos móveis."

#: views/settings/partials/sidebar.php:27
msgid "Test and Improve Google Core Web Vitals for WordPress."
msgstr "Teste e melhore os Google Core Web Vitals para WordPress."

#: views/settings/partials/sidebar.php:28
msgid "Read more"
msgstr "Leia mais"

#: views/settings/partials/sidebar.php:33
msgid "You have not activated logged-in user cache."
msgstr "Você não ativou o cache para usuários conectados."

#: views/settings/partials/sidebar.php:34
msgid ""
"Use a private browser to check your website's speed and visual appearance."
msgstr ""
"Use uma janela privativa do navegador para verificar a velocidade e "
"aparência visual do seu site."

#: views/settings/sections/addons-container.php:24
#: views/settings/sections/fields-container.php:28
msgid "Need Help?"
msgstr "Precisa de Ajuda?"
