# Copyright (C) 2024 WP Media
# This file is distributed under the same license as the WP Rocket plugin.
# Translators:
# FX <PERSON>énard <<EMAIL>>, 2023
# <AUTHOR> <EMAIL>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: WP Rocket 3.16-alpha2\n"
"Report-Msgid-Bugs-To: http://wp-rocket.me/\n"
"POT-Creation-Date: 2024-05-06T13:28:35+03:00\n"
"PO-Revision-Date: 2019-08-26 15:14+0000\n"
"Last-Translator: Romain • WP Rocket <<EMAIL>>, 2024\n"
"Language-Team: French (France) (https://app.transifex.com/wp-media/teams/18133/fr_FR/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: fr_FR\n"
"Plural-Forms: nplurals=3; plural=(n == 0 || n == 1) ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"
"X-Domain: rocket\n"
"X-Generator: WP-CLI 2.7.1\n"

#. Plugin Name of the plugin
msgid "WP Rocket"
msgstr "WP Rocket"

#. Plugin URI of the plugin
msgid "https://wp-rocket.me"
msgstr "https://wp-rocket.me"

#. Description of the plugin
msgid "The best WordPress performance plugin."
msgstr "La meilleure extension de performance pour WordPress."

#. Author of the plugin
msgid "WP Media"
msgstr "WP Media"

#. Author URI of the plugin
msgid "https://wp-media.me"
msgstr "https://wp-media.me"

#. Translators: %s = Hosting name.
#: inc/3rd-party/hosting/flywheel.php:18
#: inc/3rd-party/hosting/wp-serveur.php:27 inc/deprecated/3.6.php:697
#: inc/deprecated/3.6.php:997 inc/deprecated/3.9.php:22
#: inc/ThirdParty/Hostings/Cloudways.php:90
#: inc/ThirdParty/Hostings/Dreampress.php:44
#: inc/ThirdParty/Hostings/Godaddy.php:63
#: inc/ThirdParty/Hostings/O2Switch.php:49
#: inc/ThirdParty/Hostings/OneCom.php:137
#: inc/ThirdParty/Hostings/Pressidium.php:49
#: inc/ThirdParty/Hostings/ProIsp.php:51 inc/ThirdParty/Hostings/Savvii.php:50
#: inc/ThirdParty/Hostings/WPEngine.php:47
#: inc/ThirdParty/Hostings/WPXCloud.php:51
msgid ""
"Your site is hosted on %s, we have enabled Varnish auto-purge for "
"compatibility."
msgstr ""
"Votre site est hébergé chez %s, nous avons activé l'auto-purge Varnish pour "
"la compatibilité."

#: inc/Addon/Cloudflare/API/Client.php:129
msgid "Cloudflare did not provide any reply. Please try again later."
msgstr "Cloudflare n'a fourni aucune réponse. Veuillez réessayer plus tard."

#: inc/Addon/Cloudflare/API/Client.php:135
msgid "Cloudflare unexpected response"
msgstr "Réponse inattendue de Cloudflare"

#: inc/Addon/Cloudflare/API/Client.php:143
msgid "Missing Cloudflare result."
msgstr "Résultat de Cloudflare manquant."

#: inc/Addon/Cloudflare/API/Client.php:194 inc/deprecated/3.5.php:112
#: inc/deprecated/3.5.php:169
msgid "Incorrect Cloudflare email address or API key."
msgstr "L’email ou l’API key Cloudflare incorrect."

#. translators: %1$s = opening link; %2$s = closing link
#: inc/Addon/Cloudflare/API/Client.php:198
#: inc/Addon/Cloudflare/API/Client.php:212
#: inc/Addon/Cloudflare/Cloudflare.php:75
#: inc/Addon/Cloudflare/Cloudflare.php:108
#: inc/classes/subscriber/Tools/class-detect-missing-tags-subscriber.php:157
#: inc/deprecated/3.5.php:92 inc/deprecated/3.5.php:116
#: inc/deprecated/3.5.php:129 inc/deprecated/3.5.php:157
#: inc/deprecated/3.5.php:173
msgid "Read the %1$sdocumentation%2$s for further guidance."
msgstr "Lisez la %1$sdocumentation%2$s pour plus d'indication."

#. translators: Documentation exists in EN, FR; use localized URL if
#. applicable.
#: inc/Addon/Cloudflare/API/Client.php:200
#: inc/Addon/Cloudflare/API/Client.php:214
#: inc/Addon/Cloudflare/Auth/APIKey.php:63
#: inc/Addon/Cloudflare/Cloudflare.php:77
#: inc/Addon/Cloudflare/Cloudflare.php:110 inc/deprecated/3.5.php:81
#: inc/deprecated/3.5.php:94 inc/deprecated/3.5.php:118
#: inc/deprecated/3.5.php:131 inc/deprecated/3.5.php:159
#: inc/deprecated/3.5.php:175 inc/deprecated/3.5.php:208
msgid ""
"https://docs.wp-rocket.me/article/18-using-wp-rocket-with-"
"cloudflare/?utm_source=wp_plugin&utm_medium=wp_rocket#add-on"
msgstr ""
"https://fr.docs.wp-rocket.me/article/247-utiliser-wp-rocket-avec-"
"cloudflare/?utm_source=wp_plugin&utm_medium=wp_rocket#add-on"

#: inc/Addon/Cloudflare/API/Client.php:208 inc/deprecated/3.5.php:125
msgid "Incorrect Cloudflare Zone ID."
msgstr "Le Zone ID de Cloudflare est incorrect."

#. translators: %1$s = opening link; %2$s = closing link
#: inc/Addon/Cloudflare/Auth/APIKey.php:61
msgid ""
"Cloudflare email and/or API key are not set. Read the %1$sdocumentation%2$s "
"for further guidance."
msgstr ""
"L'email Cloudflare et/ou l’API Key ne sont pas définis. Lisez la "
"%1$sdocumentation%2$s pour plus d'informations."

#: inc/Addon/Cloudflare/Cloudflare.php:71 inc/deprecated/3.5.php:88
msgid "Missing Cloudflare Zone ID."
msgstr "Il manque le Zone ID de Cloudflare "

#: inc/Addon/Cloudflare/Cloudflare.php:104 inc/deprecated/3.5.php:153
msgid "It looks like your domain is not set up on Cloudflare."
msgstr "Il semble que votre domaine ne soit pas configuré sur Cloudflare."

#: inc/Addon/Cloudflare/Cloudflare.php:200
msgid "days"
msgstr "jours"

#: inc/Addon/Cloudflare/Cloudflare.php:203
msgid "seconds"
msgstr "secondes"

#: inc/Addon/Cloudflare/Cloudflare.php:205
msgid "minutes"
msgstr "minutes"

#: inc/Addon/Cloudflare/Cloudflare.php:207
msgid "hours"
msgstr "heures"

#. translators: %1$s = <strong>, %2$s = </strong>, %3$s = CloudFare API return
#. message.
#: inc/Addon/Cloudflare/Subscriber.php:232
#: inc/Addon/Cloudflare/Subscriber.php:261
msgid "%1$sWP Rocket:%2$s %3$s"
msgstr "%1$sWP Rocket :%2$s %3$s"

#. translators: %1$s = <strong>, %2$s = </strong>.
#: inc/Addon/Cloudflare/Subscriber.php:250
msgid "%1$sWP Rocket:%2$s Cloudflare cache successfully purged."
msgstr "%1$sWP Rocket :%2$s le cache de Cloudflare a été purgé avec succès."

#. translators: %s is the message returned by the CloudFlare API.
#: inc/Addon/Cloudflare/Subscriber.php:311
msgid "Cloudflare development mode error: %s"
msgstr "Erreur du mode développement de CloudFlare : %s"

#. translators: %s is the message returned by the CloudFlare API.
#: inc/Addon/Cloudflare/Subscriber.php:318
msgid "Cloudflare development mode %s"
msgstr "Mode développement de CloudFlare %s"

#. translators: %s is the message returned by the CloudFlare API.
#: inc/Addon/Cloudflare/Subscriber.php:337
msgid "Cloudflare cache level error: %s"
msgstr "Erreur du niveau de cache de CloudFlare : %s"

#: inc/Addon/Cloudflare/Subscriber.php:344
msgctxt "Cloudflare caching level"
msgid "standard"
msgstr "standard"

#. translators: %s is the caching level returned by the CloudFlare API.
#: inc/Addon/Cloudflare/Subscriber.php:350
msgid "Cloudflare cache level set to %s"
msgstr "Niveau de cache de Cloudflare défini à %s"

#. translators: %s is the message returned by the CloudFlare API.
#: inc/Addon/Cloudflare/Subscriber.php:368
msgid "Cloudflare minification error: %s"
msgstr "Erreur de la minification CloudFlare : %s"

#. translators: %s is the message returned by the CloudFlare API.
#: inc/Addon/Cloudflare/Subscriber.php:375
msgid "Cloudflare minification %s"
msgstr "Minification CloudFlare %s"

#. translators: %s is the message returned by the CloudFlare API.
#: inc/Addon/Cloudflare/Subscriber.php:393
msgid "Cloudflare rocket loader error: %s"
msgstr "Erreur du Rocket Loader de CloudFlare : %s"

#. translators: %s is the message returned by the CloudFlare API.
#: inc/Addon/Cloudflare/Subscriber.php:400
msgid "Cloudflare rocket loader %s"
msgstr "Rocket Loader de CloudFlare %s"

#. translators: %s is the message returned by the CloudFlare API.
#: inc/Addon/Cloudflare/Subscriber.php:418
msgid "Cloudflare browser cache error: %s"
msgstr "Erreur du cache navigateur CloudFlare : %s"

#. translators: %s is the message returned by the CloudFlare API.
#: inc/Addon/Cloudflare/Subscriber.php:425
msgid "Cloudflare browser cache set to %s"
msgstr "Cache navigateur de Cloudflare réglé sur %s "

#. translators: %1$s = strong opening tag, %2$s = strong closing tag.
#: inc/Addon/Cloudflare/Subscriber.php:536
msgid "%1$sWP Rocket:%2$s Optimal settings activated for Cloudflare:"
msgstr "%1$sWP Rocket :%2$s Réglages optimaux activés pour Cloudflare :"

#. translators: %1$s = strong opening tag, %2$s = strong closing tag.
#: inc/Addon/Cloudflare/Subscriber.php:545
msgid ""
"%1$sWP Rocket:%2$s Optimal settings deactivated for Cloudflare, reverted to "
"previous settings:"
msgstr ""
"%1$sWP Rocket :%2$s Réglages optimaux désactivés pour Cloudflare, retour aux"
" réglages précédents :"

#: inc/Addon/Cloudflare/Subscriber.php:661 inc/admin/options.php:165
#: inc/classes/subscriber/Tools/class-detect-missing-tags-subscriber.php:148
msgid "WP Rocket: "
msgstr "WP Rocket :"

#. translators: %s is the error message returned by the API.
#: inc/Addon/Sucuri/Subscriber.php:96
msgid "Sucuri cache purge error: %s"
msgstr "Erreur lors de la purge du cache Sucuri : %s"

#: inc/Addon/Sucuri/Subscriber.php:101
msgid ""
"The Sucuri cache is being cleared. Note that it may take up to two minutes "
"for it to be fully flushed."
msgstr ""
"Le cache Sucuri est en train d'être effacé. Notez que le vidage complet peut"
" prendre jusqu'à deux minutes."

#: inc/Addon/Sucuri/Subscriber.php:218
msgid "Sucuri firewall API key was not found."
msgstr "L’API Key du Firewall Sucuri est introuvable."

#: inc/Addon/Sucuri/Subscriber.php:231
msgid "Sucuri firewall API key is invalid."
msgstr "L’API Key du Firewall Sucuri n'est pas valide."

#. translators: %s is an error message.
#: inc/Addon/Sucuri/Subscriber.php:286
msgid "Error when contacting Sucuri firewall API. Error message was: %s"
msgstr ""
"Erreur lors du contact avec l’API Key du Firewall Sucuri. Message d’erreur "
"reçu : %s"

#: inc/Addon/Sucuri/Subscriber.php:301
msgid "Could not get a response from the Sucuri firewall API."
msgstr "Impossible d’obtenir une réponse de l’API Key du Firewall Sucuri."

#: inc/Addon/Sucuri/Subscriber.php:316
msgid "Got an invalid response from the Sucuri firewall API."
msgstr "Réponse invalide de l’API Key du Firewall Sucuri."

#: inc/Addon/Sucuri/Subscriber.php:330
msgid "The Sucuri firewall API returned an unknown error."
msgstr "L'API Key du Firewall Sucuri a retourné une erreur inconnue."

#. translators: %s is an error message.
#: inc/Addon/Sucuri/Subscriber.php:334
msgid "The Sucuri firewall API returned the following error: %s"
msgid_plural "The Sucuri firewall API returned the following errors: %s"
msgstr[0] "L'API Key du Firewall Sucuri a retourné cette erreur : %s"
msgstr[1] "L'API Key du Firewall Sucuri a retourné ces erreurs : %s"
msgstr[2] "L'API Key du Firewall Sucuri a retourné ces erreurs : %s"

#. Translators: %1$s = plugin name(s), %2$s = opening <a> tag, %3$s = closing
#. </a> tag.
#: inc/Addon/WebP/AdminSubscriber.php:93
msgid ""
"You are using %1$s to serve WebP images so you do not need to enable this "
"option. %2$sMore info%3$s %4$s If you prefer to have WP Rocket serve WebP "
"for you instead, please disable WebP display in %1$s."
msgid_plural ""
"You are using %1$s to serve WebP images so you do not need to enable this "
"option. %2$sMore info%3$s %4$s If you prefer to have WP Rocket serve WebP "
"for you instead, please disable WebP display in %1$s."
msgstr[0] ""
"Vous utilisez %1$s pour servir les images WebP, vous n'avez donc pas besoin "
"d'activer cette option. %2$sPlus d'infos%3$s %4$s Si vous préférez que WP "
"Rocket serve les WebP pour vous, veuillez désactiver l’affichage des WebP de"
" %1$s. "
msgstr[1] ""
"Vous utilisez %1$s pour servir les images WebP, vous n'avez donc pas besoin "
"d'activer cette option. %2$sPlus d'infos%3$s %4$s Si vous préférez que WP "
"Rocket serve les WebP pour vous, veuillez désactiver l’affichage des WebP de"
" %1$s. "
msgstr[2] ""
"Vous utilisez %1$s pour servir les images WebP, vous n'avez donc pas besoin "
"d'activer cette option. %2$sPlus d'infos%3$s %4$s Si vous préférez que WP "
"Rocket serve les WebP pour vous, veuillez désactiver l’affichage des WebP de"
" %1$s. "

#: inc/Addon/WebP/AdminSubscriber.php:105
msgid "WebP cache is disabled by filter."
msgstr "Le cache WebP est désactivé par un filtre."

#. Translators: %1$s = plugin name(s), %2$s = opening <a> tag, %3$s = closing
#. </a> tag.
#: inc/Addon/WebP/AdminSubscriber.php:115
#: inc/Addon/WebP/AdminSubscriber.php:141
msgid ""
"You are using %1$s to convert images to WebP. If you want WP Rocket to serve"
" them for you, activate this option. %2$sMore info%3$s"
msgid_plural ""
"You are using %1$s to convert images to WebP. If you want WP Rocket to serve"
" them for you, activate this option. %2$sMore info%3$s"
msgstr[0] ""
"Vous utilisez %1$s pour convertir les images en WebP. Si vous voulez que WP "
"Rocket les serve pour vous, activez cette option. %2$sPlus d'infos%3$s"
msgstr[1] ""
"Vous utilisez %1$s pour convertir les images en WebP. Si vous voulez que WP "
"Rocket les serve pour vous, activez cette option. %2$sPlus d'infos%3$s"
msgstr[2] ""
"Vous utilisez %1$s pour convertir les images en WebP. Si vous voulez que WP "
"Rocket les serve pour vous, activez cette option. %2$sPlus d'infos%3$s"

#. Translators: %1$s = plugin name(s), %2$s = opening <a> tag, %3$s = closing
#. </a> tag.
#: inc/Addon/WebP/AdminSubscriber.php:127
#: inc/Addon/WebP/AdminSubscriber.php:153
msgid ""
"You are using %1$s to convert images to WebP. WP Rocket will create separate"
" cache files to serve your WebP images. %2$sMore info%3$s"
msgid_plural ""
"You are using %1$s to convert images to WebP. WP Rocket will create separate"
" cache files to serve your WebP images. %2$sMore info%3$s"
msgstr[0] ""
"Vous utilisez %1$s pour convertir les images en WebP. WP Rocket créera des "
"fichiers cache séparés pour servir vos images WebP. %2$sPlus d'infos%3$s"
msgstr[1] ""
"Vous utilisez %1$s pour convertir les images en WebP. WP Rocket créera des "
"fichiers cache séparés pour servir vos images WebP. %2$sPlus d'infos%3$s"
msgstr[2] ""
"Vous utilisez %1$s pour convertir les images en WebP. WP Rocket créera des "
"fichiers cache séparés pour servir vos images WebP. %2$sPlus d'infos%3$s"

#. Translators: %1$s = opening <a> tag, %2$s = closing </a> tag.
#: inc/Addon/WebP/AdminSubscriber.php:173
msgid ""
"%5$sWe have not detected any compatible WebP plugin!%6$s%4$s If you don’t "
"already have WebP images on your site consider using %3$sImagify%2$s or "
"another supported plugin. %1$sMore info%2$s %4$s If you are not using WebP "
"do not enable this option."
msgstr ""
"%5$sNous n'avons pas détecté de plugin WebP compatible !%6$s%4$s Si vous "
"n'avez pas encore d'images WebP sur votre site, envisagez d'utiliser "
"%3$sImagify%2$s ou une autre extension supportée. %1$sPlus d'infos%2$s %4$s "
"Si vous n'utilisez pas WebP, n'activez pas cette option."

#: inc/Addon/WebP/AdminSubscriber.php:185
msgid "WP Rocket will create separate cache files to serve your WebP images."
msgstr ""
"WP Rocket créera des fichiers cache séparés pour servir vos images WebP."

#: inc/admin/admin.php:18 inc/common/admin-bar.php:273
#: inc/deprecated/deprecated.php:1787
msgid "Support"
msgstr "Support"

#: inc/admin/admin.php:20
msgid "Docs"
msgstr "Docs"

#: inc/admin/admin.php:22 inc/common/admin-bar.php:261
msgid "FAQ"
msgstr "FAQ"

#: inc/admin/admin.php:24 inc/common/admin-bar.php:70
msgid "Settings"
msgstr "Réglages"

#: inc/admin/admin.php:96 inc/admin/admin.php:117 inc/deprecated/3.5.php:898
#: inc/Engine/Cache/AdminSubscriber.php:134
msgid "Clear this cache"
msgstr "Vider ce cache"

#: inc/admin/admin.php:402
msgid "Settings import failed: you do not have the permissions to do this."
msgstr ""
"L'import des réglages a échoué : Vous n'avez pas les permissions suffisantes"
" pour faire ceci."

#: inc/admin/admin.php:406
msgid "Settings import failed: no file uploaded."
msgstr "L'import des réglages a échoué : aucun fichier téléversé."

#: inc/admin/admin.php:410
msgid "Settings import failed: incorrect filename."
msgstr "L'import des réglages a échoué : nom de fichier incorrect."

#: inc/admin/admin.php:421
msgid "Settings import failed: incorrect filetype."
msgstr "L'import des réglages a échoué : type de fichier incorrect."

#: inc/admin/admin.php:431
msgid "Settings import failed: "
msgstr "L'import des réglages a échoué :"

#: inc/admin/admin.php:447
msgid "Settings import failed: unexpected file content."
msgstr "L'import des réglages a échoué : type de fichier incorrect."

#: inc/admin/admin.php:492
msgid "Settings imported and saved."
msgstr "Réglages importés et sauvegardés."

#: inc/admin/options.php:102 inc/Engine/Admin/Settings/Page.php:571
msgid "Excluded CSS Files"
msgstr "Fichiers CSS à exclure"

#: inc/admin/options.php:103 inc/Engine/Admin/Settings/Page.php:725
msgid "Excluded Inline JavaScript"
msgstr "JavaScript inline exclu"

#: inc/admin/options.php:104 inc/Engine/Admin/Settings/Page.php:743
#: inc/Engine/Admin/Settings/Page.php:776
#: inc/Engine/Admin/Settings/Page.php:823
msgid "Excluded JavaScript Files"
msgstr "Fichiers JavaScript exclus"

#: inc/admin/options.php:105
msgid "Defer JavaScript Files"
msgstr "Charger le JavaScript en différé"

#: inc/admin/options.php:106
msgid "Excluded Delay JavaScript Files"
msgstr "Fichiers JavaScript exclus du report de l'éxecution"

#: inc/admin/options.php:107 inc/Engine/Admin/Settings/Page.php:1229
msgid "Never Cache URL(s)"
msgstr "Ne jamais mettre en cache ces URL(s)"

#: inc/admin/options.php:108 inc/Engine/Admin/Settings/Page.php:1243
msgid "Never Cache User Agent(s)"
msgstr "Ne jamais mettre en cache ces User Agents"

#: inc/admin/options.php:109 inc/Engine/Admin/Settings/Page.php:1249
msgid "Always Purge URL(s)"
msgstr "Toujours purger ces URL(s)"

#: inc/admin/options.php:110 inc/Engine/Admin/Settings/Page.php:1530
msgid "Exclude files from CDN"
msgstr "Exclure des fichiers du CDN"

#. translators: 1 and 2 can be anything.
#: inc/admin/options.php:131
msgid "%1$s: <em>%2$s</em>"
msgstr "%1$s: <em>%2$s</em>"

#: inc/admin/options.php:141
msgid "The following pattern is invalid and has been removed:"
msgid_plural "The following patterns are invalid and have been removed:"
msgstr[0] "Le pattern suivant est invalide et a été supprimé :"
msgstr[1] "Les patterns suivants sont invalides et ont été supprimés :"
msgstr[2] "Les patterns suivants sont invalides et ont été supprimés :"

#: inc/admin/options.php:157
msgid "More info"
msgstr "Plus d'infos"

#: inc/admin/ui/meta-boxes.php:37 inc/admin/ui/notices.php:671
#: inc/common/admin-bar.php:91 inc/common/admin-bar.php:154
#: views/settings/page-sections/dashboard.php:150
msgid "Clear cache"
msgstr "Vider le cache"

#. translators: %1$s WP Rocket plugin name; %2$s = file name.
#: inc/admin/ui/notices.php:30 inc/admin/ui/notices.php:43
msgid ""
"<strong>%1$s</strong> has not been deactivated due to missing writing permissions.<br>\n"
"Make <strong>%2$s</strong> writeable and retry deactivation, or force deactivation now:"
msgstr ""
"<strong>%1$s</strong> n'a pas été désactivé en raisons de l'absence des permissions d'écriture.<br>\n"
"Rendez <strong>%2$s</strong> inscriptible et réessayez de désactiver, ou forcez la désactivation maintenant :"

#. translators: %s is WP Rocket plugin name.
#: inc/admin/ui/notices.php:97
msgid ""
"<strong>%s</strong>: One or more plugins have been enabled or disabled, "
"clear the cache if they affect the front end of your site."
msgstr ""
"<strong>%s</strong>: Une ou plusieurs extensions ont été activées ou "
"désactivées, videz le cache si elles influent sur le front de votre site."

#. translators: %s is WP Rocket plugin name.
#: inc/admin/ui/notices.php:141
msgid ""
"<strong>%s</strong>: The following plugins are not compatible with this "
"plugin and may cause unexpected results:"
msgstr ""
"<strong>%s</strong>: Les extensions suivantes ne sont pas compatibles avec "
"WP Rocket et vont générer des résultats inattendus :"

#: inc/admin/ui/notices.php:147
msgid "Deactivate"
msgstr "Désactiver"

#: inc/admin/ui/notices.php:189
msgid ""
"WP Rocket Footer JS is not an official add-on. It prevents some options in "
"WP Rocket from working correctly. Please deactivate it if you have problems."
msgstr ""
"WP Rocket Footer JS n'est pas un add-on officiel. Il empêche certaines "
"options de WP Rocket de fonctionner correctement. Veuillez le désactiver si "
"vous avez des problèmes."

#. translators: %1$s = opening link tag, %2$s = closing link tag.
#: inc/admin/ui/notices.php:229
msgid ""
"Endurance Cache is currently enabled, which will conflict with WP Rocket "
"Cache. Please set the Endurance Cache cache level to Off (Level 0) on the "
"%1$sSettings > General%2$s page to prevent any issues."
msgstr ""
"Endurance Cache est actuellement activé, et sera en conflit avec le cache de"
" WP Rocket. Veuillez régler Endurance Cache sur Off (Niveau 0) sur la page "
"%1$sRéglages > Général%2$s pour éviter tout problème."

#. translators: %1$s WP Rocket plugin name; %2$s = opening link; %3$s =
#. closing link
#: inc/admin/ui/notices.php:250
msgid ""
"%1$s: A custom permalink structure is required for the plugin to work "
"properly. %2$sGo to permalinks settings%3$s"
msgstr ""
"%1$s: Une structure de permalien personnalisée est requise pour que WP "
"Rocket fonctionne. %2$sRendez-vous aux réglages des permaliens%3$s"

#. translators: %s = plugin name.
#: inc/admin/ui/notices.php:297
msgid ""
"%s could not modify the .htaccess file due to missing writing permissions."
msgstr ""
"%s n'a pas pu modifier le fichier .htaccess en raison d'un défaut de "
"permissions en écriture."

#. translators: This is a doc title! %1$s = opening link; %2$s = closing link
#: inc/admin/ui/notices.php:303 inc/admin/ui/notices.php:788
msgid "Troubleshoot: %1$sHow to make system files writeable%2$s"
msgstr ""
"Pour résoudre le problème : %1$sComment rendre les fichiers systèmes "
"accessible à l'écriture%2$s"

#. translators: Documentation exists in EN, DE, FR, ES, IT; use loaclised URL
#. if applicable
#: inc/admin/ui/notices.php:305 inc/admin/ui/notices.php:790
msgid ""
"https://docs.wp-rocket.me/article/626-how-to-make-system-files-htaccess-wp-"
"config-writeable/?utm_source=wp_plugin&utm_medium=wp_rocket"
msgstr ""
"https://fr.docs.wp-rocket.me/article/945-fichiers-systemes-accessible-"
"ecriture/?utm_source=wp_plugin&utm_medium=wp_rocket"

#: inc/admin/ui/notices.php:311
msgid ""
"Don’t worry, WP Rocket’s page caching and settings will still function "
"correctly."
msgstr ""
"Ne vous inquiétez pas, la mise en cache des pages et les réglages de WP "
"Rocket fonctionneront toujours correctement."

#: inc/admin/ui/notices.php:311
msgid ""
"For optimal performance, adding the following lines into your .htaccess is "
"recommended (not required):"
msgstr ""
"Pour des performances optimales, il est recommandé d'ajouter les lignes "
"suivantes dans votre fichier .htaccess (pas obligatoire) :"

#. translators: %1$s = plugin name, %2$s + %3$s = opening links, %4$s =
#. closing link
#: inc/admin/ui/notices.php:458
msgid ""
"%1$s is good to go! %2$sTest your load time%4$s, or visit your "
"%3$ssettings%4$s."
msgstr ""
"%1$s est prêt à décoller ! %2$sTestez votre temps de chargement%4$s, ou "
"jetez un oeil aux %3$sréglages%4$s."

#: inc/admin/ui/notices.php:499
msgid ""
"Would you allow WP Rocket to collect non-sensitive diagnostic data from this"
" website?"
msgstr ""
"Autorisez-vous WP Rocket à collecter les données de diagnostic non sensibles"
" de votre site web ?"

#: inc/admin/ui/notices.php:500
msgid "This would help us to improve WP Rocket for you in the future."
msgstr "Cela nous aiderait à améliorer WP Rocket pour vous dans le futur."

#. translators: button text, click will expand data collection preview
#: inc/admin/ui/notices.php:506
msgid "What info will we collect?"
msgstr "Quelles infos collecterons-nous ?"

#: inc/admin/ui/notices.php:511
msgid ""
"Below is a detailed view of all data WP Rocket will collect if granted "
"permission. WP Rocket will never transmit any domain names or email "
"addresses (except for license validation), IP addresses, or third-party API "
"keys."
msgstr ""
"Vous trouverez ci-dessous une vue détaillée de toutes les données que WP "
"Rocket collectera si l'autorisation lui est accordée. WP Rocket ne "
"transmettra jamais de noms de domaine ou d'adresses email (sauf pour la "
"validation de licence), d'adresses IP ou de clés API tierces."

#. translators: button text for data collection opt-in
#: inc/admin/ui/notices.php:520
msgid "Yes, allow"
msgstr "Oui, j'autorise"

#. translators: button text for data collection opt-in
#: inc/admin/ui/notices.php:523
msgid "No, thanks"
msgstr "Non, merci"

#: inc/admin/ui/notices.php:562
msgid "Thank you!"
msgstr "Merci !"

#: inc/admin/ui/notices.php:567
msgid "WP Rocket now collects these metrics from your website:"
msgstr "WP Rocket collecte désormais ces données sur votre site web :"

#. translators: %s = plugin name.
#: inc/admin/ui/notices.php:605
msgid "%s: Cache cleared."
msgstr "%s : Cache vidé."

#. translators: %s = plugin name.
#: inc/admin/ui/notices.php:612
msgid "%s: Post cache cleared."
msgstr "%s : Cache Article vidé."

#. translators: %s = plugin name.
#: inc/admin/ui/notices.php:619
msgid "%s: Term cache cleared."
msgstr "%s : Cache Terme vidé."

#. translators: %s = plugin name).
#: inc/admin/ui/notices.php:626
msgid "%s: User cache cleared."
msgstr "%s : Cache Utilisateur vidé."

#: inc/admin/ui/notices.php:662 inc/Engine/License/views/promo-banner.php:36
#: inc/Engine/License/views/renewal-expired-banner-ocd-disabled.php:32
#: inc/Engine/License/views/renewal-expired-banner-ocd.php:42
#: inc/Engine/License/views/renewal-expired-banner.php:30
#: views/settings/page-sections/dashboard.php:46
msgid "Dismiss this notice"
msgstr "Ne pas tenir compte de cet avertissement."

#: inc/admin/ui/notices.php:682 inc/Engine/Saas/Admin/AdminBar.php:84
#: inc/Engine/Saas/Admin/AdminBar.php:202
msgid "Clear Used CSS"
msgstr "Purger le CSS utilisé"

#: inc/admin/ui/notices.php:685
msgid "Stop Preload"
msgstr "Arrêter le préchargement"

#: inc/admin/ui/notices.php:691
msgid "Turn on Remove Unused CSS"
msgstr "Activer l'option Supprimer les ressources CSS inutilisées"

#: inc/admin/ui/notices.php:697
msgid "Enable “Separate Cache Files for Mobile Devices” now"
msgstr ""
"Activer “Créer un fichier de cache à part pour les mobiles” maintenant"

#: inc/admin/ui/notices.php:715
msgid "Force deactivation "
msgstr "Forcer la désactivation"

#: inc/admin/ui/notices.php:745
msgid "The following code should have been written to this file:"
msgstr "Le code suivant aurait dû être écrit sur ce fichier :"

#. translators: %s = plugin name.
#: inc/admin/ui/notices.php:776
msgid "%s cannot configure itself due to missing writing permissions."
msgstr ""
"%s ne peut pas se configurer seul en raison de l'absence de permissions en "
"écriture."

#. translators: %s = file/folder name
#: inc/admin/ui/notices.php:782
msgid "Affected file/folder: %s"
msgstr "Fichier/dossier affecté : %s"

#: inc/classes/admin/class-logs.php:124
msgid "The debug file could not be deleted."
msgstr "Le fichier de debug n'a pas pu être suppprimé."

#. Translators: %1$s = Plugin name, %2$s = Plugin version.
#: inc/classes/class-wp-rocket-requirements-check.php:147
msgid "To function properly, %1$s %2$s requires at least:"
msgstr "Pour fonctionner correctement, nécessite au moins %1$s %2$s :"

#. Translators: %1$s = PHP version required.
#: inc/classes/class-wp-rocket-requirements-check.php:151
msgid ""
"PHP %1$s. To use this WP Rocket version, please ask your web host how to "
"upgrade your server to PHP %1$s or higher."
msgstr ""
"PHP %1$s. Pour utiliser cette version de WP Rocket, demandez à votre "
"hébergeur comment mettre à niveau votre serveur vers PHP %1$s ou supérieur."

#. Translators: %1$s = WordPress version required.
#: inc/classes/class-wp-rocket-requirements-check.php:156
msgid ""
"WordPress %1$s. To use this WP Rocket version, please upgrade WordPress to "
"version %1$s or higher."
msgstr ""
"WordPress %1$s. Pour utiliser cette version de WP Rocket, mettez à jour "
"WordPress à la version %1$s ou plus récente."

#: inc/classes/class-wp-rocket-requirements-check.php:159
msgid ""
"If you are not able to upgrade, you can rollback to the previous version by "
"using the button below."
msgstr ""
"Si vous n'êtes pas en mesure de faire l'upgrade, vous pouvez restaurer WP "
"Rocket à sa version précédente en cliquant ci-dessous."

#. Translators: %s = Previous plugin version.
#: inc/classes/class-wp-rocket-requirements-check.php:161
#: inc/deprecated/deprecated.php:1947
msgid "Re-install version %s"
msgstr "Réinstaller la version %s"

#. translators: %s is the plugin name.
#: inc/classes/class-wp-rocket-requirements-check.php:203
#: inc/classes/class-wp-rocket-requirements-check.php:214
#: inc/deprecated/3.11.php:236 inc/deprecated/3.11.php:251
#: inc/Engine/Plugin/UpdaterSubscriber.php:483
#: inc/Engine/Plugin/UpdaterSubscriber.php:497
msgid "%s Update Rollback"
msgstr "Retour à la version %s"

#. translators: %1$s = missing tags;
#: inc/classes/subscriber/Tools/class-detect-missing-tags-subscriber.php:151
msgid ""
"Failed to detect the following requirement in your theme: closing %1$s."
msgid_plural ""
"Failed to detect the following requirements in your theme: closing %1$s."
msgstr[0] ""
"Impossible de détecter l'exigence suivante dans votre thème : fermeture de "
"%1$s."
msgstr[1] ""
"Impossible de détecter les exigences suivantes dans votre thème : fermeture "
"de %1$s."
msgstr[2] ""
"Impossible de détecter les exigences suivantes dans votre thème : fermeture "
"de %1$s."

#. translators: Documentation exists in EN, FR; use localized URL if
#. applicable.
#: inc/classes/subscriber/Tools/class-detect-missing-tags-subscriber.php:159
msgid ""
"https://docs.wp-rocket.me/article/99-pages-not-cached-or-minify-cssjs-not-"
"working/?utm_source=wp_plugin&utm_medium=wp_rocket#theme"
msgstr ""
"https://fr.docs.wp-rocket.me/article/264-les-pages-ne-sont-pas-mises-en-"
"cache-ou-la-minification-css-et-js-ne-fonctionne-"
"pas/?utm_source=wp_plugin&utm_medium=wp_rocket#theme"

#: inc/common/admin-bar.php:91 inc/common/admin-bar.php:154
#: views/settings/page-sections/dashboard.php:150
msgid "Clear and preload cache"
msgstr "Vider et précharger le cache"

#: inc/common/admin-bar.php:131 inc/functions/i18n.php:20
msgid "All languages"
msgstr "Toutes les langues"

#: inc/common/admin-bar.php:178
msgid "Clear this post"
msgstr "Vider cet article"

#: inc/common/admin-bar.php:192
msgid "Purge this URL"
msgstr "Purger cette URL"

#: inc/common/admin-bar.php:212
msgid "Purge Sucuri cache"
msgstr "Vider cache Sucuri"

#: inc/common/admin-bar.php:236 views/settings/fields/rocket-cdn.php:55
msgid "Purge RocketCDN cache"
msgstr "Purger le cache de RocketCDN"

#: inc/common/admin-bar.php:249 views/settings/partials/documentation.php:14
msgid "Documentation"
msgstr "Documentation"

#: inc/deprecated/3.2.php:52 views/settings/page-sections/imagify.php:36
msgid "Activate Imagify"
msgstr "Activer Imagify"

#: inc/deprecated/3.2.php:52
msgid "Install Imagify for Free"
msgstr "Installer Imagify gratuitement"

#: inc/deprecated/3.2.php:67
msgid ""
"Speed up your website and boost your SEO by reducing image file sizes "
"without losing quality with Imagify."
msgstr ""
"Accélérez votre site Internet et améliorez votre référencement en réduisant "
"le poids de vos image sans perte de qualité avec Imagify."

#: inc/deprecated/3.2.php:72
msgid "More details"
msgstr "Plus de détails"

#. translators: %d is the number of pages preloaded.
#: inc/deprecated/3.2.php:228
msgid "Sitemap preload: %d pages have been cached."
msgstr "Préchargement du Sitemap : %d pages ont été mises en cache."

#. translators: %d = Number of pages preloaded.
#: inc/deprecated/3.2.php:261
msgid ""
"Sitemap preload: %d uncached pages have now been preloaded. (refresh to see "
"progress)"
msgstr ""
"Préchargement du Sitemap : %d pages ont été pré-chargées.  (rafraîchir pour "
"voir la progression)"

#. translators: %s is an URL.
#: inc/deprecated/3.3.php:761 inc/deprecated/3.3.php:770
#: inc/Engine/Plugin/UpdaterApiTools.php:32
msgid ""
"An unexpected error occurred. Something may be wrong with WP-Rocket.me or "
"this server&#8217;s configuration. If you continue to have problems, <a "
"href=\"%s\">contact support</a>."
msgstr ""
"Une erreur inattendue s’est produite. Quelque chose ne va pas avec WP-"
"Rocket.me ou avec la configuration de ce serveur. Si vous continuez à avoir "
"des problèmes, <a href=\"%s\">contactez notre support</a>."

#: inc/deprecated/3.4.php:18
msgid "Choose a domain from the list"
msgstr "Choisissez un domaine dans la liste"

#: inc/deprecated/3.4.php:31
msgid "No domain available in your Cloudflare account"
msgstr "Aucun domaine disponible dans votre compte CloudFlare"

#: inc/deprecated/3.5.php:71 inc/deprecated/3.5.php:195
msgid ""
"Curl is disabled on your server. Please ask your host to enable it. This is "
"required for the Cloudflare Add-on to work correctly."
msgstr ""
"Curl est désactivé sur votre serveur. Veuillez demander à votre hébergeur de"
" l'activer. Ceci est nécessaire pour que l’add-on Cloudflare fonctionne "
"correctement."

#. translators: %1$s = opening link; %2$s = closing link
#: inc/deprecated/3.5.php:79
msgid ""
"Cloudflare email, API key and Zone ID are not set. Read the "
"%1$sdocumentation%2$s for further guidance."
msgstr ""
"L'email Cloudflare, l’API Key et le Zone ID ne sont pas définis. Lisez la "
"documentation %1$sdocumentation%2$s pour plus d'informations."

#. translators: %1$s = opening link; %2$s = closing link
#: inc/deprecated/3.5.php:206
msgid ""
"Cloudflare email and API key are not set. Read the %1$sdocumentation%2$s for"
" further guidance."
msgstr ""
"L'email Cloudflare et l’API Key ne sont pas définis. Lisez la "
"%1$sdocumentation%2$s pour plus d'informations."

#: inc/deprecated/3.5.php:271
msgid "Connection to Cloudflare failed"
msgstr "La connexion à CloudFlare a échoué"

#. translators: %s = CloudFare API return message.
#: inc/deprecated/3.5.php:587
msgid "<strong>WP Rocket:</strong> %s"
msgstr "<strong>WP Rocket :</strong> %s"

#: inc/deprecated/3.5.php:592
msgid "<strong>WP Rocket:</strong> Cloudflare cache successfully purged."
msgstr ""
"<strong>WP Rocket :</strong> Le cache Cloudflare a été purgé avec succès."

#: inc/deprecated/3.5.php:858 inc/Engine/HealthCheck/HealthCheck.php:81
msgid ""
"The following scheduled event failed to run. This may indicate the CRON "
"system is not running properly, which can prevent some WP Rocket features "
"from working as intended:"
msgid_plural ""
"The following scheduled events failed to run. This may indicate the CRON "
"system is not running properly, which can prevent some WP Rocket features "
"from working as intended:"
msgstr[0] ""
"L’événement planifié suivant n'a pas eu lieu. Cela peut indiquer que le "
"système CRON ne fonctionne pas correctement, ce qui peut empêcher certaines "
"fonctions de WP Rocket de fonctionner comme prévu :"
msgstr[1] ""
"Les événements planifiés suivants n'ont pas eu lieu. Cela peut indiquer que "
"le système CRON ne fonctionne pas correctement, ce qui peut empêcher "
"certaines fonctions de WP Rocket de fonctionner comme prévu :"
msgstr[2] ""
"Les événements planifiés suivants n'ont pas eu lieu. Cela peut indiquer que "
"le système CRON ne fonctionne pas correctement, ce qui peut empêcher "
"certaines fonctions de WP Rocket de fonctionner comme prévu :"

#: inc/deprecated/3.5.php:867 inc/Engine/HealthCheck/HealthCheck.php:88
msgid "Please contact your host to check if CRON is working."
msgstr "Veuillez contacter votre hébergeur pour vérifier si CRON fonctionne."

#: inc/deprecated/3.10.php:163
msgid "OPcache purge failed."
msgstr "La purge OPcache a échouée."

#: inc/deprecated/3.10.php:168
msgid "OPcache successfully purged"
msgstr "OPcache purgé avec succès "

#: inc/deprecated/3.11.php:81
msgid "Yoast SEO XML sitemap"
msgstr "Sitemap XML de Yoast SEO"

#. translators: %s = Name of the plugin.
#. translators: %s = plugin name, e.g. Yoast SEO.
#: inc/deprecated/3.11.php:83 inc/deprecated/3.12.php:58
#: inc/deprecated/3.12.php:87 inc/deprecated/3.12.php:186
#: inc/deprecated/3.12.php:253 inc/deprecated/3.12.php:342
msgid ""
"We automatically detected the sitemap generated by the %s plugin. You can "
"check the option to preload it."
msgstr ""
"Nous avons automatiquement détecté le sitemap généré par l'extension %s. "
"Vous pouvez cocher l'option pour l'inclure dans le préchargement."

#. translators: 1 and 3 are link openings, 2 is a link closing.
#: inc/deprecated/3.11.php:279 inc/Engine/Plugin/UpdaterSubscriber.php:520
msgid "%1$sReturn to WP Rocket%2$s or %3$sgo to Plugins page%2$s"
msgstr "%1$sRetour à WP Rocket%2$s ou %3$sà la page des Plugins%2$s"

#: inc/deprecated/3.12.php:56
msgid "All in One SEO XML sitemap"
msgstr "Sitemap XML de All in One SEO"

#: inc/deprecated/3.12.php:85
msgid "Rank Math XML sitemap"
msgstr "Sitemap XML de Rank Math"

#: inc/deprecated/3.12.php:184
msgid "SEOPress XML sitemap"
msgstr "Sitemap XML de SEOPress"

#: inc/deprecated/3.12.php:251
msgid "The SEO Framework XML sitemap"
msgstr "Sitemap XML de SEO Framework"

#: inc/deprecated/3.12.php:332
msgid "Jetpack XML Sitemaps"
msgstr "Sitemaps XML de Jetpack"

#: inc/deprecated/3.12.php:334
msgid "Preload the sitemap from the Jetpack plugin"
msgstr "Précharger le sitemap de l'extension Jetpack"

#: inc/deprecated/3.15.php:28
#: inc/Engine/Admin/Metaboxes/PostEditOptionsSubscriber.php:74
msgid "WP Rocket Options"
msgstr "Options WP Rocket"

#: inc/deprecated/3.15.php:57 views/metaboxes/post_edit_options.php:19
msgid "Never cache this page"
msgstr "Ne jamais mettre en cache cette page"

#: inc/deprecated/3.15.php:61 views/metaboxes/post_edit_options.php:23
msgid "Activate these options on this post:"
msgstr "Activer ces options sur cette page :"

#. translators: %s is the name of the option.
#: inc/deprecated/3.15.php:81
#: inc/Engine/Admin/Metaboxes/PostEditOptionsSubscriber.php:121
msgid "Activate first the %s option."
msgstr "Activer d’abord l’option de %s."

#. translators: %1$s = opening strong tag, %2$s = closing strong tag.
#: inc/deprecated/3.15.php:97 views/metaboxes/post_edit_options.php:38
msgid ""
"%1$sNote:%2$s None of these options will be applied if this post has been "
"excluded from cache in the global cache settings."
msgstr ""
"%1$sRemarque :%2$s Ces options ne seront pas appliquées si vous avez exclu "
"cette page du cache dans les réglages généraux de WP Rocket."

#: inc/deprecated/deprecated.php:240
msgid "<strong>JS</strong> files with Deferred Loading JavaScript"
msgstr "Fichiers <strong>JS</strong> en chargement différé"

#: inc/deprecated/deprecated.php:293
msgid "Add URL"
msgstr "Ajouter une URL"

#: inc/deprecated/deprecated.php:1245
#: inc/Engine/Admin/Settings/Settings.php:451
msgid "Settings saved."
msgstr "Réglages enregistrés."

#: inc/deprecated/deprecated.php:1277 views/settings/fields/import-form.php:22
msgid ""
"Before you can upload your import file, you will need to fix the following "
"error:"
msgstr ""
"Avant de pouvoir envoyer votre fichier à importer, vous devez régler les "
"erreurs suivantes :"

#. translators: %s is the maximum upload size set on the current server.
#: inc/deprecated/deprecated.php:1288 views/settings/fields/import-form.php:35
msgid "Choose a file from your computer (maximum size: %s)"
msgstr "Choisissez un fichier sur votre ordinateur (taille maximum : %s)"

#: inc/deprecated/deprecated.php:1294 inc/Engine/Admin/Settings/Render.php:422
msgid "Upload file and import settings"
msgstr "Envoyer le fichier et importer les réglages"

#: inc/deprecated/deprecated.php:1385
msgid "Your Cloudflare credentials are valid."
msgstr "Vos accès CloudFlare sont valides."

#: inc/deprecated/deprecated.php:1389
msgid "Your Cloudflare credentials are invalid!"
msgstr "Vos accès CloudFlare sont invalides !"

#: inc/deprecated/deprecated.php:1453
msgid "Save and optimize"
msgstr "Sauvegarder et optimiser"

#: inc/deprecated/deprecated.php:1453
msgid "Optimize"
msgstr "Optimiser"

#: inc/deprecated/deprecated.php:1464 inc/deprecated/deprecated.php:1666
msgctxt "screen-reader-text"
msgid "Note:"
msgstr "Note :"

#: inc/deprecated/deprecated.php:1470
msgctxt "screen-reader-text"
msgid "Performance tip:"
msgstr "Performance :"

#: inc/deprecated/deprecated.php:1476
msgctxt "screen-reader-text"
msgid "Third-party feature detected:"
msgstr "Autre fonctionnalité détectée :"

#: inc/deprecated/deprecated.php:1488 inc/deprecated/deprecated.php:1672
msgctxt "screen-reader-text"
msgid "Warning:"
msgstr "Attention : "

#: inc/deprecated/deprecated.php:1501
msgctxt "button text"
msgid "Download settings"
msgstr "Télécharger les réglages"

#: inc/deprecated/deprecated.php:1531
msgid "Replace site's hostname with:"
msgstr "Remplacer le nom d’hôte du site par :"

#: inc/deprecated/deprecated.php:1550 inc/deprecated/deprecated.php:1587
#: inc/deprecated/deprecated.php:1615 views/settings/fields/cnames.php:41
#: views/settings/fields/cnames.php:75 views/settings/fields/cnames.php:105
msgid "reserved for"
msgstr "reservé à / aux"

#: inc/deprecated/deprecated.php:1552 inc/deprecated/deprecated.php:1589
#: inc/deprecated/deprecated.php:1617 views/settings/fields/cnames.php:44
#: views/settings/fields/cnames.php:78 views/settings/fields/cnames.php:108
msgid "All files"
msgstr "Tous les fichiers"

#: inc/deprecated/deprecated.php:1564 inc/deprecated/deprecated.php:1594
#: inc/deprecated/deprecated.php:1622 views/settings/fields/cnames.php:56
#: views/settings/fields/cnames.php:90 views/settings/fields/cnames.php:120
msgid "Images"
msgstr "Images"

#: inc/deprecated/deprecated.php:1635 views/settings/fields/cnames.php:132
msgid "Add CNAME"
msgstr "Ajouter un CNAME"

#: inc/deprecated/deprecated.php:1706
msgid "Watch the video"
msgstr "Voir la vidéo"

#: inc/deprecated/deprecated.php:1771
msgid "Basic"
msgstr "Options de base"

#: inc/deprecated/deprecated.php:1772
msgid "Static Files"
msgstr "Fichiers Statiques"

#: inc/deprecated/deprecated.php:1773 inc/Engine/Admin/Settings/Page.php:1500
#: inc/Engine/Admin/Settings/Page.php:1511
#: inc/Engine/CDN/Admin/Subscriber.php:28
msgid "CDN"
msgstr "CDN"

#: inc/deprecated/deprecated.php:1774
msgid "Advanced"
msgstr "Options avancées"

#: inc/deprecated/deprecated.php:1775 inc/Engine/Admin/Settings/Page.php:1345
msgid "Database"
msgstr "Base de données"

#: inc/deprecated/deprecated.php:1776 inc/Engine/Admin/Settings/Page.php:1051
msgid "Preload"
msgstr "Préchargement"

#: inc/deprecated/deprecated.php:1786
#: inc/Engine/Admin/Settings/Subscriber.php:171
#: views/settings/page-sections/tools.php:33
msgid "Tools"
msgstr "Outils"

#: inc/deprecated/deprecated.php:1789 inc/Engine/Admin/Settings/Page.php:357
#: views/settings/page-sections/dashboard.php:80
msgid "License"
msgstr "Licence"

#. Translators: %1$s = Plugin name, %2$s = Plugin version, %3$s = PHP version
#. required.
#: inc/deprecated/deprecated.php:1944
msgid ""
"%1$s %2$s requires at least PHP %3$s to function properly. To use this "
"version, please ask your web host how to upgrade your server to PHP %3$s or "
"higher. If you are not able to upgrade, you can rollback to the previous "
"version by using the button below."
msgstr ""
"%1$s %2$s requiert au moins PHP %3$s pour fonctionner correctement. Pour "
"utiliser cette version, demandez à votre hébergeur de mettre à jour votre "
"serveur vers PHP %3$s au minimum. Si cette mise à jour n'est pas possible, "
"vous pouvez revenir à la version précédente en utilisant le bouton ci-"
"dessous."

#. translators: 1: PHP class name, 2: version number, 3: replacement class
#. name.
#: inc/deprecated/DeprecatedClassTrait.php:54
msgid ""
"The called class %1$s is <strong>deprecated</strong> since version %2$s! Use"
" %3$s instead."
msgstr ""
"La class nommée %1$s est <strong>obsolète</strong> depuis la version %2$s ! "
"Utilisez %3$s à la place."

#. translators: 1: PHP class name, 2: version number.
#: inc/deprecated/DeprecatedClassTrait.php:65
msgid ""
"The called class %1$s is <strong>deprecated</strong> since version %2$s!"
msgstr ""
"La class nommée %1$s est <strong>obsolète</strong> depuis la version %2$s !"

#: inc/deprecated/Engine/Addon/FacebookTracking/Subscriber.php:92
#: inc/deprecated/Engine/Addon/GoogleTracking/Subscriber.php:137
#: inc/Engine/Admin/Database/Subscriber.php:79
msgid "weekly"
msgstr "hebdomadaire"

#: inc/Engine/Admin/Database/Optimization.php:30
#: inc/Engine/Admin/Settings/Page.php:1389
msgid "Revisions"
msgstr "Révisions"

#: inc/Engine/Admin/Database/Optimization.php:31
#: inc/Engine/Admin/Settings/Page.php:1399
msgid "Auto Drafts"
msgstr "Brouillons auto"

#: inc/Engine/Admin/Database/Optimization.php:32
#: inc/Engine/Admin/Settings/Page.php:1409
msgid "Trashed Posts"
msgstr "Contenus dans la corbeille"

#: inc/Engine/Admin/Database/Optimization.php:33
#: inc/Engine/Admin/Settings/Page.php:1419
msgid "Spam Comments"
msgstr "Commentaires indésirables"

#: inc/Engine/Admin/Database/Optimization.php:34
#: inc/Engine/Admin/Settings/Page.php:1429
msgid "Trashed Comments"
msgstr "Commentaires à la corbeille"

#: inc/Engine/Admin/Database/Optimization.php:35
msgid "Transients"
msgstr "Transients"

#: inc/Engine/Admin/Database/Optimization.php:36
msgid "Tables"
msgstr "Tables"

#: inc/Engine/Admin/Database/Subscriber.php:85
msgid "monthly"
msgstr "mensuel"

#: inc/Engine/Admin/Database/Subscriber.php:194
msgid "Database optimization process is running"
msgstr "L'optimisation de la base de données est en cours"

#: inc/Engine/Admin/Database/Subscriber.php:224
msgid ""
"Database optimization process is complete. Everything was already optimized!"
msgstr ""
"L'optimisation de la base de donnée est terminée. Tout était déjà optimisé !"

#: inc/Engine/Admin/Database/Subscriber.php:227
msgid ""
"Database optimization process is complete. List of optimized items below:"
msgstr ""
"L'optimisation de la base de donnée est terminée. Voici la liste des "
"optimisations effectuées :"

#. translators: %1$d = number of items optimized, %2$s = type of optimization
#: inc/Engine/Admin/Database/Subscriber.php:235
msgid "%1$d %2$s optimized."
msgstr "%1$d %2$s optimisé(s)."

#. translators: %1$s = <strong>, %2$s = </strong>, %3$s = <a>, %4$s = </a>.
#: inc/Engine/Admin/DomainChange/Subscriber.php:143
msgid ""
"%1$sWP Rocket:%2$s We detected that the website domain has changed. The "
"configuration files must be regenerated for the page cache and all other "
"optimizations to work as intended. %3$sLearn More%4$s"
msgstr ""
"%1$sWP Rocket :%2$s Nous avons détecté que le nom de domaine a changé. Les "
"fichiers de configuration doivent être regénérés pour que le cache des pages"
" et les autres optimisations fonctionnent comme prévu. %3$sEn savoir "
"plus%4$s"

#: inc/Engine/Admin/DomainChange/Subscriber.php:171
msgid "Regenerate WP Rocket configuration files now"
msgstr "Regénérer les fichiers de configuration de WP Rocket maintenant"

#: inc/Engine/Admin/Settings/Page.php:223
msgid "Save Changes"
msgstr "Enregistrer les modifications"

#: inc/Engine/Admin/Settings/Page.php:223
msgid "Validate License"
msgstr "Valider la licence"

#: inc/Engine/Admin/Settings/Page.php:279
#: inc/Engine/Admin/Settings/Page.php:280 inc/functions/admin.php:550
msgid "Unavailable"
msgstr "Indisponible"

#: inc/Engine/Admin/Settings/Page.php:374
msgid "API key"
msgstr "Clé API"

#: inc/Engine/Admin/Settings/Page.php:389
msgid "Email address"
msgstr "Adresse e-mail"

#: inc/Engine/Admin/Settings/Page.php:415
msgid "Dashboard"
msgstr "Tableau de bord"

#: inc/Engine/Admin/Settings/Page.php:416
msgid "Get help, account info"
msgstr "Aide, info du compte"

#: inc/Engine/Admin/Settings/Page.php:425
msgid "My Status"
msgstr "Ma situation"

#: inc/Engine/Admin/Settings/Page.php:435 views/settings/page.php:75
msgid "Rocket Analytics"
msgstr "Rocket Analytics"

#. translators: %1$s = opening <a> tag, %2$s = closing </a> tag.
#: inc/Engine/Admin/Settings/Page.php:437
msgid ""
"I agree to share anonymous data with the development team to help improve WP"
" Rocket. %1$sWhat info will we collect?%2$s"
msgstr ""
"J'accepte de partager des données anonymes avec l'équipe de développement "
"pour aider à améliorer WP Rocket. %1$sQuelles informations collecterons-nous"
" ?%2$s"

#: inc/Engine/Admin/Settings/Page.php:489
msgid "File Optimization"
msgstr "Optimisation des fichiers"

#: inc/Engine/Admin/Settings/Page.php:490
msgid "Optimize CSS & JS"
msgstr "Optimisez CSS & JS"

#. translators: %1$s = type of minification (HTML, CSS or JS), %2$s = “WP
#. Rocket”.
#: inc/Engine/Admin/Settings/Page.php:498
msgid ""
"%1$s Minification is currently activated in <strong>Autoptimize</strong>. If"
" you want to use %2$s’s minification, disable this option in Autoptimize."
msgstr ""
"La minification %1$s est actuellement activée dans "
"<strong>Autoptimize</strong>. Si vous souhaitez utiliser la minification de "
"%2$s, désactivez ces options dans Autoptimize."

#: inc/Engine/Admin/Settings/Page.php:508
msgid "CSS Files"
msgstr "Fichiers CSS"

#: inc/Engine/Admin/Settings/Page.php:517
msgid "JavaScript Files"
msgstr "Fichiers JavaScript"

#. translators: %1$s = type of minification (HTML, CSS or JS), %2$s = “WP
#. Rocket”.
#: inc/Engine/Admin/Settings/Page.php:524
msgid ""
"%1$s Minification is currently activated in <strong>Autoptimize</strong>. If"
" you want to use %2$s’s minification, disable those options in Autoptimize."
msgstr ""
"La minification %1$s est actuellement activée dans "
"<strong>Autoptimize</strong>. Si vous souhaitez utiliser la minification de "
"%2$s, désactivez ces options dans Autoptimize."

#: inc/Engine/Admin/Settings/Page.php:529
msgid ""
"If you have problems after activating this option, copy and paste the "
"default exclusions to quickly resolve issues:"
msgstr ""
"Si vous rencontrez des problèmes après avoir activé cette option, copiez et "
"collez les exclusions par défaut pour résoudre rapidement les problèmes :"

#. translators: %1$s = opening </a> tag, %2$s = closing </a> tag.
#: inc/Engine/Admin/Settings/Page.php:533
msgid ""
"Also, please check our %1$sdocumentation%2$s for a list of compatibility "
"exclusions."
msgstr ""
"Veuillez également consulter notre %1$sdocumentation%2$s pour une liste "
"d'exclusions de compatibilité."

#: inc/Engine/Admin/Settings/Page.php:538
msgid ""
"Internal scripts are excluded by default to prevent issues. Remove them to "
"take full advantage of this option."
msgstr ""
"Les scripts internes sont exclus par défaut pour éviter tout problème. "
"Supprimez-les pour profiter pleinement de cette option."

#. translators: %1$s = opening </a> tag, %2$s = closing </a> tag.
#: inc/Engine/Admin/Settings/Page.php:541
msgid ""
"If this causes trouble, restore the default exclusions, found %1$shere%2$s"
msgstr ""
"En cas de problème, rétablissez les exclusions par défaut que vous trouverez"
" %1$sici%2$s"

#: inc/Engine/Admin/Settings/Page.php:550
msgid "Minify CSS files"
msgstr "Minifier les fichiers CSS"

#: inc/Engine/Admin/Settings/Page.php:551
msgid "Minify CSS removes whitespace and comments to reduce the file size."
msgstr ""
"Supprime les espace et les commentaires afin de réduire la taille des "
"fichiers CSS."

#: inc/Engine/Admin/Settings/Page.php:564
#: inc/Engine/Admin/Settings/Page.php:628
#: inc/Engine/Admin/Settings/Page.php:694
#: inc/Engine/Admin/Settings/Page.php:718
msgid "This could break things!"
msgstr "Ceci pourrait casser des choses sur votre site !"

#: inc/Engine/Admin/Settings/Page.php:565
#: inc/Engine/Admin/Settings/Page.php:629
#: inc/Engine/Admin/Settings/Page.php:695
#: inc/Engine/Admin/Settings/Page.php:719
msgid ""
"If you notice any errors on your website after having activated this "
"setting, just deactivate it again, and your site will be back to normal."
msgstr ""
"Si vous remarquez des problèmes sur votre site Web après avoir activé cette "
"option, il vous suffit simplement de la désactiver et votre site sera de "
"nouveau normal."

#: inc/Engine/Admin/Settings/Page.php:566
msgid "Activate minify CSS"
msgstr "Activer la minification CSS"

#: inc/Engine/Admin/Settings/Page.php:572
msgid ""
"Specify URLs of CSS files to be excluded from minification (one per line)."
msgstr ""
"Indiquez l’URL des fichiers CSS à exclure de la minification (une par ligne)"

#: inc/Engine/Admin/Settings/Page.php:573
msgid ""
"<strong>Internal:</strong> The domain part of the URL will be stripped "
"automatically. Use (.*).css wildcards to exclude all CSS files located at a "
"specific path."
msgstr ""
"<strong>Interne :</strong>Le nom de domaine sera supprimé automatiquement de"
" l'URL. Utilisez les expressions régulières (.*).css afin d’exclure tous les"
" fichiers CSS pour un chemin donné."

#. translators: %1$s = opening <a> tag, %2$s = closing </a> tag.
#: inc/Engine/Admin/Settings/Page.php:575
msgid ""
"<strong>3rd Party:</strong> Use either the full URL path or only the domain "
"name, to exclude external CSS. %1$sMore info%2$s"
msgstr ""
"<strong>Tiers :</strong> Utilisez le chemin complet de l'URL, ou seulement "
"le nom de domaine, pour exclure les fichiers CSS externes. %1$sPlus "
"d'infos%2$s"

#: inc/Engine/Admin/Settings/Page.php:588
#: inc/Engine/Admin/Settings/Page.php:610
msgid "Optimize CSS delivery"
msgstr "Optimiser le chargement du CSS"

#: inc/Engine/Admin/Settings/Page.php:593
msgid ""
"Optimize CSS delivery eliminates render-blocking CSS on your website. Only "
"one method can be selected. Remove Unused CSS is recommended for optimal "
"performance, but limited only to the users with active license."
msgstr ""
"L'Optimisation du Chargement CSS élimine les CSS bloquant le rendu de votre "
"site Web. Une seule méthode peut être sélectionnée. La méthode Supprimer les"
" CSS inutilisés est recommandée pour des performances optimales."

#: inc/Engine/Admin/Settings/Page.php:593
msgid ""
"Optimize CSS delivery eliminates render-blocking CSS on your website. Only "
"one method can be selected. Remove Unused CSS is recommended for optimal "
"performance."
msgstr ""
"L'Optimisation du Chargement CSS élimine les CSS bloquant le rendu sur votre"
" site Web. Une seule méthode peut être sélectionnée. La méthode Supprimer "
"les CSS inutilisés est recommandée pour des performances optimales."

#. translators: %1$s = opening <a> tag, %2$s = closing </a> tag.
#: inc/Engine/Admin/Settings/Page.php:603
msgid ""
"Optimize CSS Delivery features are disabled on local environments. %1$sLearn"
" more%2$s"
msgstr ""
"Les options Optimiser le chargement CSS sont désactivées en local. %1$sEn "
"savoir plus%2$s"

#: inc/Engine/Admin/Settings/Page.php:623
#: inc/Engine/Optimization/RUCSS/Admin/OptionSubscriber.php:74
msgid "Remove Unused CSS"
msgstr "Supprimer les ressources CSS inutilisées"

#. translators: %1$s = opening <a> tag, %2$s = closing </a> tag.
#: inc/Engine/Admin/Settings/Page.php:626
msgid ""
"Removes unused CSS per page and helps to reduce page size and HTTP requests."
" Recommended for best performance. Test thoroughly! %1$sMore info%2$s"
msgstr ""
"Supprime le CSS inutilisé par page et contribue à réduire la taille des "
"pages et les requêtes HTTP. Recommandé pour de meilleures performances. "
"Testez-le rigoureusement ! %1$sPlus d'infos%2$s"

#: inc/Engine/Admin/Settings/Page.php:630
msgid "Activate Remove Unused CSS"
msgstr "Activer Supprimer les ressources CSS inutilisées"

#: inc/Engine/Admin/Settings/Page.php:636
msgid "CSS safelist"
msgstr "Liste des exceptions CSS"

#: inc/Engine/Admin/Settings/Page.php:637
msgid ""
"Specify CSS filenames, IDs or classes that should not be removed (one per "
"line)."
msgstr ""
"Indiquez les noms de fichiers CSS, les ID ou les classes qui ne doivent pas "
"être supprimés (un par ligne)."

#: inc/Engine/Admin/Settings/Page.php:652
#: inc/Engine/CriticalPath/Admin/Subscriber.php:200
msgid "Load CSS asynchronously"
msgstr "Chargement asynchrone du CSS"

#. translators: %1$s = plugin name.
#: inc/Engine/Admin/Settings/Page.php:655
msgctxt "WP Critical CSS compatibility"
msgid ""
"Load CSS asynchronously is currently handled by the %1$s plugin. If you want"
" to use WP Rocket’s load CSS asynchronously option, disable the %1$s plugin."
msgstr ""
"Le Chargement asynchrone du CSS est actuellement géré par le plugin %1$s. Si"
" vous souhaitez utiliser l'option de chargement asynchrone du CSS de WP "
"Rocket, désactivez le plugin %1$s."

#. translators: %1$s = opening <a> tag, %2$s = closing </a> tag.
#: inc/Engine/Admin/Settings/Page.php:657
msgid ""
"Generates critical path CSS and loads CSS asynchronously. %1$sMore info%2$s"
msgstr ""
"Génère le Critical Path CSS et charge les CSS de manière asynchrone. "
"%1$sPlus d'infos%2$s"

#: inc/Engine/Admin/Settings/Page.php:663
msgid "Fallback critical CSS"
msgstr "Critical CSS de secours"

#. translators: %1$s = opening <a> tag, %2$s = closing </a> tag.
#: inc/Engine/Admin/Settings/Page.php:665
msgid ""
"Provides a fallback if auto-generated critical path CSS is incomplete. "
"%1$sMore info%2$s"
msgstr ""
"Fourni un critical path CSS de secours si celui auto-généré est "
"incomplet.%1$sPlus d'infos%2$s"

#: inc/Engine/Admin/Settings/Page.php:680
msgid "Minify JavaScript files"
msgstr "Minifier les fichiers JS"

#: inc/Engine/Admin/Settings/Page.php:681
msgid ""
"Minify JavaScript removes whitespace and comments to reduce the file size."
msgstr ""
"Supprime les espace et les commentaires afin de réduire la taille des "
"fichiers JS."

#: inc/Engine/Admin/Settings/Page.php:696
msgid "Activate minify JavaScript"
msgstr "Activer la minification JavaScript"

#: inc/Engine/Admin/Settings/Page.php:701
msgid ""
"Combine JavaScript files <em>(Enable Minify JavaScript files to select)</em>"
msgstr ""
"Combiner les fichiers JavaScript <em>(activez la minification JS pour "
"pouvoir séléctionner)</em>"

#. translators: %1$s = opening <a> tag, %2$s = closing </a> tag.
#: inc/Engine/Admin/Settings/Page.php:703
msgid ""
"Combine JavaScript files combines your site’s internal, 3rd party and inline"
" JS reducing HTTP requests. Not recommended if your site uses HTTP/2. "
"%1$sMore info%2$s"
msgstr ""
"Combine vos fichiers JavaScript, les fichiers tiers et le inline JS en un "
"seul fichier, réduisant le nombre de requêtes HTTP. Ces réglages ne sont pas"
" recommandés si votre site utilise HTTP/2. %1$sPlus d'infos%2$s"

#. translators: %1$s = opening <a> tag, %2$s = closing </a> tag.
#: inc/Engine/Admin/Settings/Page.php:704
msgid ""
"For compatibility and best results, this option is disabled when delay "
"javascript execution is enabled."
msgstr ""
"Pour des raisons de compatibilité et pour obtenir les meilleurs résultats, "
"cette option est désactivée lorsque l'option Reporter l'exécution JavaScript"
" est activée."

#: inc/Engine/Admin/Settings/Page.php:720
msgid "Activate combine JavaScript"
msgstr "Activer la combinaison JavaScript"

#. translators: %1$s = opening <a> tag, %2$s = closing </a> tag.
#: inc/Engine/Admin/Settings/Page.php:727
msgid ""
"Specify patterns of inline JavaScript to be excluded from concatenation (one"
" per line). %1$sMore info%2$s"
msgstr ""
"Indiquez les patterns du code JavaScript inline à exclure de la "
"concaténation (un par ligne).%1$sPlus d'infos%2$s"

#: inc/Engine/Admin/Settings/Page.php:744
msgid ""
"Specify URLs of JavaScript files to be excluded from minification and "
"concatenation (one per line)."
msgstr ""
"Indiquez l’URL des fichiers JavaScript à exclure de la minification et de la"
" concaténation (une par ligne)"

#: inc/Engine/Admin/Settings/Page.php:745
msgid ""
"<strong>Internal:</strong> The domain part of the URL will be stripped "
"automatically. Use (.*).js wildcards to exclude all JS files located at a "
"specific path."
msgstr ""
"<strong>Interne :</strong>Le nom de domaine sera supprimé automatiquement de"
" l'URL. Utilisez les expressions régulières (.*).js afin d’exclure tous les "
"fichiers JS pour un chemin donné."

#. translators: %1$s = opening <a> tag, %2$s = closing </a> tag.
#: inc/Engine/Admin/Settings/Page.php:747
msgid ""
"<strong>3rd Party:</strong> Use either the full URL path or only the domain "
"name, to exclude external JS. %1$sMore info%2$s"
msgstr ""
"<strong>Tiers :</strong> Utilisez le chemin complet de l'URL, ou seulement "
"le nom de domaine, pour exclure les fichiers JS externes. %1$sPlus "
"d'infos%2$s"

#: inc/Engine/Admin/Settings/Page.php:763
#: inc/Engine/Optimization/DeferJS/AdminSubscriber.php:76
msgid "Load JavaScript deferred"
msgstr "Charger le JavaScript en différé"

#. translators: %1$s = opening <a> tag, %2$s = closing </a> tag.
#: inc/Engine/Admin/Settings/Page.php:765
msgid ""
"Load JavaScript deferred eliminates render-blocking JS on your site and can "
"improve load time. %1$sMore info%2$s"
msgstr ""
"Élimine le JS bloquant le rendu de votre site pour un meilleur temps de "
"chargement perçu. %1$sPlus d'infos%2$s"

#. translators: %1$s = opening <a> tag, %2$s = closing </a> tag.
#: inc/Engine/Admin/Settings/Page.php:778
msgid ""
"Specify URLs or keywords of JavaScript files to be excluded from defer (one "
"per line). %1$sMore info%2$s"
msgstr ""
"Indiquez les URLs ou des mots-clés des fichiers JavaScript à exclure du "
"chargement différé (une par ligne). %1$sPlus d'infos%2$s"

#: inc/Engine/Admin/Settings/Page.php:794
#: inc/Engine/Optimization/DelayJS/Admin/Subscriber.php:210
msgid "Delay JavaScript execution"
msgstr "Reporter l'exécution JavaScript"

#. translators: %1$s = opening <a> tag, %2$s = closing </a> tag.
#: inc/Engine/Admin/Settings/Page.php:796
msgid ""
"Improves performance by delaying the loading of JavaScript files until user "
"interaction (e.g. scroll, click). %1$sMore info%2$s"
msgstr ""
"Améliore les performances en retardant le chargement des fichiers JavaScript"
" jusqu'à l'interaction avec l'utilisateur (ex: défilement, clic). %1$sPlus "
"d'infos%2$s"

#: inc/Engine/Admin/Settings/Page.php:805
msgid "One-click exclusions"
msgstr "Exclusions en un clic"

#: inc/Engine/Admin/Settings/Page.php:806
msgid ""
"When using the Delay JavaScript Execution, you might experience delay "
"loading elements located in the viewport that need to appear immediately - "
"e.g. slider, header, menu."
msgstr ""
"Lorsque vous utilisez l'option Reporter l’exécution du JavaScript, il se "
"peut que le chargement d'éléments qui doivent apparaître immédiatement soit "
"retardé - ex. : un slider, un en-tête, un menu."

#: inc/Engine/Admin/Settings/Page.php:807
msgid ""
"If you need instant visibility, click below on files that should NOT be "
"delayed. This selection will help users interact with the elements straight "
"away."
msgstr ""
"Si vous avez besoin d'une visibilité immédiate, cliquez ci-dessous sur les "
"fichiers qui ne doivent PAS être reportés. Cela permettra aux utilisateurs "
"d'interagir immédiatement avec ces éléments."

#: inc/Engine/Admin/Settings/Page.php:824
msgid ""
"Specify URLs or keywords that can identify inline or JavaScript files to be "
"excluded from delaying execution (one per line)."
msgstr ""
"Indiquez les URLs ou les mots-clés qui permettent d'identifier les fichiers "
"JavaScript ou le JS inline à exclure du report d'exécution (un par ligne)."

#: inc/Engine/Admin/Settings/Page.php:856
msgid "Media"
msgstr "Média"

#: inc/Engine/Admin/Settings/Page.php:857
msgid "LazyLoad, image dimensions"
msgstr "LazyLoad, dimensions des images"

#: inc/Engine/Admin/Settings/Page.php:866
msgid "Autoptimize"
msgstr "Autoptimize"

#: inc/Engine/Admin/Settings/Page.php:920
msgid "LazyLoad"
msgstr "LazyLoad"

#. translators: %1$s = opening <a> tag, %2$s = closing </a> tag.
#: inc/Engine/Admin/Settings/Page.php:923
msgid ""
"It can improve actual and perceived loading time as images, iframes, and "
"videos will be loaded only as they enter (or about to enter) the viewport "
"and reduces the number of HTTP requests. %1$sMore Info%2$s"
msgstr ""
"Cela peut améliorer le temps de chargement réel et perçu car les images, les"
" iframes et les vidéos ne seront chargées que lorsqu'elles entreront (ou sur"
" le point d'entrer) dans la fenêtre. Réduit le nombre de requêtes "
"HTTP.%1$sPlus d'infos%2$s"

#. translators: %1$s = “WP Rocket”, %2$s = a list of plugin names.
#: inc/Engine/Admin/Settings/Page.php:930
msgid ""
"LazyLoad is currently activated in %2$s. If you want to use WP Rocket’s "
"LazyLoad, disable this option in %2$s."
msgstr ""
"Le Lazyload est actuellement activé dans %2$s. Si vous souhaitez utiliser le"
" Lazyload de WP Rocket, désactivez cette option dans %2$s."

#: inc/Engine/Admin/Settings/Page.php:933
msgid "Image Dimensions"
msgstr "Dimensions des images"

#. translators: %1$s = opening <a> tag, %2$s = closing </a> tag.
#: inc/Engine/Admin/Settings/Page.php:936
msgid ""
"Add missing width and height attributes to images. Helps prevent layout "
"shifts and improve the reading experience for your visitors. %1$sMore "
"info%2$s"
msgstr ""
"Ajoute les attributs de largeur et de hauteur manquants aux images. Permet "
"d'éviter les changements de mise en page et d'améliorer l'expérience de "
"lecture de vos visiteurs. %1$sPlus d'infos%2$s"

#: inc/Engine/Admin/Settings/Page.php:955
msgid "Enable for images"
msgstr "Activer pour les images"

#. translators: %1$s = “WP Rocket”, %2$s = a list of plugin names.
#: inc/Engine/Admin/Settings/Page.php:967
msgid ""
"LazyLoad for images is currently activated in %2$s. If you want to use "
"%1$s’s LazyLoad, disable this option in %2$s."
msgstr ""
"Le Lazyload est actuellement activé dans %2$s. Si vous souhaitez utiliser le"
" Lazyload de %1$s, désactivez cette option dans %2$s."

#: inc/Engine/Admin/Settings/Page.php:975
msgid "Enable for CSS background images"
msgstr "Activer pour les images background CSS"

#: inc/Engine/Admin/Settings/Page.php:990
msgid "Enable for iframes and videos"
msgstr "Activer sur les iframes et vidéos"

#: inc/Engine/Admin/Settings/Page.php:1005
msgid "Replace YouTube iframe with preview image"
msgstr "Remplacer l'iframe Youtube par une image d'aperçu"

#. translators: %1$s = “WP Rocket”, %2$s = a list of plugin or themes names.
#: inc/Engine/Admin/Settings/Page.php:1007
msgid "Replace YouTube iframe with preview image is not compatible with %2$s."
msgstr ""
"Remplacer l'iframe Youtube par une image d’aperçu n’est pas compatible avec "
"%2$s."

#. translators: %1$s = “WP Rocket”, %2$s = a list of plugin or themes names.
#: inc/Engine/Admin/Settings/Page.php:1007
msgid ""
"This can significantly improve your loading time if you have a lot of "
"YouTube videos on a page."
msgstr ""
"Cela peut considérablement améliorer votre chargement si vous avez beaucoup "
"de vidéos Youtube sur une page."

#: inc/Engine/Admin/Settings/Page.php:1022
msgid "Excluded images or iframes"
msgstr "Images ou iframes exclues"

#. translators: %1$s = opening <a> tag, %2$s = closing </a> tag.
#: inc/Engine/Admin/Settings/Page.php:1024
msgid ""
"Specify keywords (e.g. image filename, CSS filename, CSS class, domain) from"
" the image or iframe code to be excluded (one per line). %1$sMore info%2$s"
msgstr ""
"Indiquez les mots-clés (ex: nom de fichier image, classe CSS, domaine) des "
"images ou le code de l'iframe à exclure (une par ligne). %1$sPlus "
"d'infos%2$s"

#: inc/Engine/Admin/Settings/Page.php:1032
msgid "Add missing image dimensions"
msgstr "Ajouter les dimensions d'image manquantes"

#: inc/Engine/Admin/Settings/Page.php:1052
msgid "Generate cache files, preload fonts"
msgstr "Générez le cache, préchargez les polices"

#: inc/Engine/Admin/Settings/Page.php:1064
msgid "Preload Cache"
msgstr "Préchargement du cache"

#. translators: %1$s = opening <a> tag, %2$s = closing </a> tag.
#: inc/Engine/Admin/Settings/Page.php:1067
msgid ""
"When you enable preloading WP Rocket will automatically detect your sitemaps"
" and save all URLs to the database. The plugin will make sure that your "
"cache is always preloaded."
msgstr ""
"Lorsque vous activez le préchargement, WP Rocket détecte automatiquement vos"
" sitemaps et enregistre toutes les URLs dans la base de données. L'extension"
" s'assurera que votre cache est toujours préchargé."

#: inc/Engine/Admin/Settings/Page.php:1075
msgid "Preload Links"
msgstr "Préchargement des liens"

#. translators: %1$s = opening <a> tag, %2$s = closing </a> tag.
#: inc/Engine/Admin/Settings/Page.php:1078
msgid ""
"Link preloading improves the perceived load time by downloading a page when "
"a user hovers over the link. %1$sMore info%2$s"
msgstr ""
"Le préchargement des liens améliore le temps de chargement perçu en "
"téléchargeant une page lorsqu'un utilisateur survole le lien. %1$sPlus "
"d'info%2$s"

#: inc/Engine/Admin/Settings/Page.php:1086
msgid "Prefetch DNS Requests"
msgstr "Préchargement des requêtes DNS"

#: inc/Engine/Admin/Settings/Page.php:1088
msgid ""
"DNS prefetching can make external files load faster, especially on mobile "
"networks"
msgstr ""
"Le préchargement des requêtes DNS peut permettre aux ressources externes de "
"charger plus rapidement, surtout sur les réseaux mobiles."

#: inc/Engine/Admin/Settings/Page.php:1093
msgid "Preload Fonts"
msgstr "Préchargement des polices"

#. translators: %1$s = opening <a> tag, %2$s = closing </a> tag.
#: inc/Engine/Admin/Settings/Page.php:1096
msgid ""
"Improves performance by helping browsers discover fonts in CSS files. "
"%1$sMore info%2$s"
msgstr ""
"Améliore les performances en aidant les navigateurs à découvrir les polices "
"contenues dans les fichiers CSS. %1$sPlus d'infos%2$s"

#: inc/Engine/Admin/Settings/Page.php:1110
msgid "Activate Preloading"
msgstr "Activer le préchargement"

#: inc/Engine/Admin/Settings/Page.php:1121
msgid "Exclude URLs"
msgstr "URLs à exclure"

#. translators: %1$s = opening <a> tag, %2$s = closing </a> tag.
#: inc/Engine/Admin/Settings/Page.php:1126
msgid ""
"Specify URLs to be excluded from the preload feature (one per line). "
"%1$sMore info%2$s"
msgstr ""
"Indiquez les URL à exclure du préchargement (une par ligne). %1$sPlus "
"d'infos%2$s"

#: inc/Engine/Admin/Settings/Page.php:1137
msgid "URLs to prefetch"
msgstr "URLs à précharger"

#: inc/Engine/Admin/Settings/Page.php:1138
msgid ""
"Specify external hosts to be prefetched (no <code>http:</code>, one per "
"line)"
msgstr ""
"Indiquez les hôtes externes à précharger (sans <code>http:</code>, un par "
"ligne)"

#: inc/Engine/Admin/Settings/Page.php:1147
msgid "Fonts to preload"
msgstr "Polices à précharger"

#: inc/Engine/Admin/Settings/Page.php:1148
msgid ""
"Specify urls of the font files to be preloaded (one per line). Fonts must be"
" hosted on your own domain, or the domain you have specified on the CDN tab."
msgstr ""
"Indiquez les URLs des fichiers de police à précharger (une par ligne). Les "
"polices doivent être hébergées sur votre propre domaine, ou le domaine que "
"vous avez spécifié dans l'onglet CDN."

#: inc/Engine/Admin/Settings/Page.php:1149
msgid ""
"The domain part of the URL will be stripped automatically.<br/>Allowed font "
"extensions: otf, ttf, svg, woff, woff2."
msgstr ""
"Le nom de domaine sera supprimé automatiquement de l’URL.<br/>Extensions de "
"police autorisées : otf, ttf, svg, woff, woff2."

#: inc/Engine/Admin/Settings/Page.php:1158
msgid "Enable link preloading"
msgstr "Activer le préchargement des liens"

#: inc/Engine/Admin/Settings/Page.php:1177
msgid "Advanced Rules"
msgstr "Règles avancées"

#: inc/Engine/Admin/Settings/Page.php:1178
msgid "Fine-tune cache rules"
msgstr "Affinez les règles du cache"

#: inc/Engine/Admin/Settings/Page.php:1191
msgid ""
"Sensitive pages like custom login/logout URLs should be excluded from cache."
msgstr ""
"Les pages sensibles telles que les URLs de connexion / déconnexion "
"personnalisées doivent être exclues du cache."

#: inc/Engine/Admin/Settings/Page.php:1194
msgctxt "plugin name"
msgid "WooCommerce"
msgstr "WooCommerce"

#: inc/Engine/Admin/Settings/Page.php:1196
msgctxt "plugin name"
msgid "Easy Digital Downloads"
msgstr "Easy Digital Downloads"

#: inc/Engine/Admin/Settings/Page.php:1198
msgctxt "plugin name"
msgid "iThemes Exchange"
msgstr "iThemes Exchange"

#: inc/Engine/Admin/Settings/Page.php:1200
msgctxt "plugin name"
msgid "Jigoshop"
msgstr "Jigoshop"

#: inc/Engine/Admin/Settings/Page.php:1202
msgctxt "plugin name"
msgid "WP-Shop"
msgstr "WP-Shop"

#. translators: %1$s = opening <a> tag, %2$s = plugin name, %3$s closing </a>
#. tag.
#: inc/Engine/Admin/Settings/Page.php:1208
msgid ""
"<br>Cart, checkout and \"my account\" pages set in "
"<strong>%1$s%2$s%3$s</strong> will be detected and never cached by default."
msgstr ""
"<br>Les pages panier, commande et \"mon compte\" réglées dans "
"<strong>%1$s%2$s%3$s</strong> seront automatiquement détectées et exclues du"
" cache par défaut."

#: inc/Engine/Admin/Settings/Page.php:1218
msgid "Cache Lifespan"
msgstr "Délai de nettoyage du cache"

#. translators: %1$s = opening <a> tag, %2$s = closing </a> tag.
#: inc/Engine/Admin/Settings/Page.php:1221
msgid ""
"Cache files older than the specified lifespan will be deleted.<br>Enable "
"%1$spreloading%2$s for the cache to be rebuilt automatically after lifespan "
"expiration."
msgstr ""
"Les fichiers de cache plus anciens que le délai de purge seront "
"supprimés.<br>Activez %1$sle préchargement%2$s pour que le cache soit "
"rechargé automatiquement après ce délai."

#: inc/Engine/Admin/Settings/Page.php:1237
msgid "Never Cache Cookies"
msgstr "Ne jamais mettre en cache ces Cookies"

#: inc/Engine/Admin/Settings/Page.php:1255
msgid "Cache Query String(s)"
msgstr "Cacher les Query String(s)"

#. translators: %1$s = opening <a> tag, %2$s = closing </a> tag.
#: inc/Engine/Admin/Settings/Page.php:1258
msgid ""
"%1$sCache for query strings%2$s enables you to force caching for specific "
"GET parameters."
msgstr ""
"%1$sCacher les Query Strings%2$s vous permet de forcer la mise en cache de "
"paramètres GET spécifiques."

#: inc/Engine/Admin/Settings/Page.php:1269
msgid ""
"Specify time after which the global cache is cleared<br>(0 = unlimited )"
msgstr ""
"Indiquez le temps après lequel le cache global doit être vidé<br>(0 = "
"illimité)"

#. translators: %1$s = opening <a> tag, %2$s = closing </a> tag.
#: inc/Engine/Admin/Settings/Page.php:1271
msgid ""
"Reduce lifespan to 10 hours or less if you notice issues that seem to appear"
" periodically. %1$sWhy?%2$s"
msgstr ""
"Réduisez ce délai à 10 heures ou moins si vous remarquez des problèmes "
"apparaissant seulement périodiquement.%1$sPourquoi ?%2$s"

#: inc/Engine/Admin/Settings/Page.php:1277
#: inc/Engine/License/views/promo-banner.php:30
#: inc/Engine/License/views/renewal-soon-banner.php:13
msgid "Hours"
msgstr "Heures"

#: inc/Engine/Admin/Settings/Page.php:1278
#: inc/Engine/License/views/promo-banner.php:29
#: inc/Engine/License/views/renewal-soon-banner.php:12
msgid "Days"
msgstr "Jours"

#: inc/Engine/Admin/Settings/Page.php:1283
msgid ""
"Specify URLs of pages or posts that should never be cached (one per line)"
msgstr ""
"Indiquez les URLs des pages ou des articles qui doivent être exclues de la "
"mise en cache (une par ligne)"

#: inc/Engine/Admin/Settings/Page.php:1284
#: inc/Engine/Admin/Settings/Page.php:1312
msgid ""
"The domain part of the URL will be stripped automatically.<br>Use (.*) "
"wildcards to address multiple URLs under a given path."
msgstr ""
"Le nom de domaine sera supprimé automatiquement de l'URL .<br>Utilisez les "
"expressions régulières (.*) pour exclure plusieurs URLs pour un chemin "
"donné."

#: inc/Engine/Admin/Settings/Page.php:1293
msgid ""
"Specify full or partial IDs of cookies that, when set in the visitor's "
"browser, should prevent a page from getting cached (one per line)"
msgstr ""
"Indiquez les IDs des cookies qui, lorsqu'ils sont déposés dans le navigateur"
" du visiteur, devraient empêcher la mise en cache de la page (un par ligne)"

#: inc/Engine/Admin/Settings/Page.php:1301
msgid ""
"Specify user agent strings that should never see cached pages (one per line)"
msgstr ""
"Indiquez les chaînes de user agent qui ne devraient jamais voir les pages "
"mises en cache (une par ligne)"

#: inc/Engine/Admin/Settings/Page.php:1302
msgid "Use (.*) wildcards to detect parts of UA strings."
msgstr ""
"Utilisez les expressions régulières (.*) détecter les parties des chaînes "
"des UA."

#: inc/Engine/Admin/Settings/Page.php:1311
msgid ""
"Specify URLs you always want purged from cache whenever you update any post "
"or page (one per line)"
msgstr ""
"Indiquez les URLs dont vous voulez systématiquement vider le cache lorsque "
"vous mettez à jour n'importe quel article ou page (une par ligne)"

#: inc/Engine/Admin/Settings/Page.php:1320
msgid "Specify query strings for caching (one per line)"
msgstr ""
"Indiquez les query strings qui peuvent être mises en cache (une par ligne)"

#: inc/Engine/Admin/Settings/Page.php:1346
msgid "Optimize, reduce bloat"
msgstr "Optimisez & nettoyez"

#: inc/Engine/Admin/Settings/Page.php:1353
msgid "Post Cleanup"
msgstr "Nettoyage des contenus"

#: inc/Engine/Admin/Settings/Page.php:1355
msgid ""
"Post revisions and drafts will be permanently deleted. Do not use this "
"option if you need to retain revisions or drafts."
msgstr ""
"Les révisions et les brouillons seront supprimés définitivement. N'utilisez "
"pas cette option si vous souhaitez conserver vos révisions et brouillons."

#: inc/Engine/Admin/Settings/Page.php:1360
msgid "Comments Cleanup"
msgstr "Nettoyage des commentaires"

#: inc/Engine/Admin/Settings/Page.php:1362
msgid "Spam and trashed comments will be permanently deleted."
msgstr "Les Spams et les commentaires mis à la corbeille seront supprimés"

#: inc/Engine/Admin/Settings/Page.php:1366
msgid "Transients Cleanup"
msgstr "Nettoyage des transients"

#: inc/Engine/Admin/Settings/Page.php:1368
msgid ""
"Transients are temporary options; they are safe to remove. They will be "
"automatically regenerated as your plugins require them."
msgstr ""
"Les transients sont des options temporaires, leur suppression est sans "
"risque. Elles seront automatiquement régénérées si vos plugins en ont "
"besoin."

#: inc/Engine/Admin/Settings/Page.php:1372
msgid "Database Cleanup"
msgstr "Nettoyage de la base de données"

#: inc/Engine/Admin/Settings/Page.php:1374
msgid "Reduces overhead of database tables"
msgstr "Réduit les tables de la base de données"

#: inc/Engine/Admin/Settings/Page.php:1378
msgid "Automatic Cleanup"
msgstr "Nettoyage automatique"

#. translators: %s is the number of revisions found in the database. It's a
#. formatted number, don't use %d.
#: inc/Engine/Admin/Settings/Page.php:1391
msgid "%s revision in your database."
msgid_plural "%s revisions in your database."
msgstr[0] "%s révision dans votre base de données."
msgstr[1] "%s révisions dans votre base de données."
msgstr[2] "%s révisions dans votre base de données."

#. translators: %s is the number of revisions found in the database. It's a
#. formatted number, don't use %d.
#: inc/Engine/Admin/Settings/Page.php:1401
msgid "%s draft in your database."
msgid_plural "%s drafts in your database."
msgstr[0] "%s brouillon dans votre base de données."
msgstr[1] "%s brouillons dans votre base de données."
msgstr[2] "%s brouillons dans votre base de données."

#. translators: %s is the number of revisions found in the database. It's a
#. formatted number, don't use %d.
#: inc/Engine/Admin/Settings/Page.php:1411
msgid "%s trashed post in your database."
msgid_plural "%s trashed posts in your database."
msgstr[0] "%s contenu à la corbeille dans votre base de données."
msgstr[1] "%s contenus à la corbeille dans votre base de données."
msgstr[2] "%s contenus à la corbeille dans votre base de données."

#. translators: %s is the number of revisions found in the database. It's a
#. formatted number, don't use %d.
#: inc/Engine/Admin/Settings/Page.php:1421
msgid "%s spam comment in your database."
msgid_plural "%s spam comments in your database."
msgstr[0] "%s commentaire indésirable dans votre base de données."
msgstr[1] "%s commentaires indésirables dans votre base de données."
msgstr[2] "%s commentaires indésirables dans votre base de données."

#. translators: %s is the number of revisions found in the database. It's a
#. formatted number, don't use %d.
#: inc/Engine/Admin/Settings/Page.php:1431
msgid "%s trashed comment in your database."
msgid_plural "%s trashed comments in your database."
msgstr[0] "%s commentaire mis à la corbeille dans votre base de données."
msgstr[1] "%s commentaires mis à la corbeille dans votre base de données."
msgstr[2] "%s commentaires mis à la corbeille dans votre base de données."

#: inc/Engine/Admin/Settings/Page.php:1439
msgid "All transients"
msgstr "Tous les transients"

#. translators: %s is the number of revisions found in the database. It's a
#. formatted number, don't use %d.
#: inc/Engine/Admin/Settings/Page.php:1441
msgid "%s transient in your database."
msgid_plural "%s transients in your database."
msgstr[0] "%s transient dans votre base de données."
msgstr[1] "%s transients dans votre base de données."
msgstr[2] "%s transients dans votre base de données."

#: inc/Engine/Admin/Settings/Page.php:1449
msgid "Optimize Tables"
msgstr "Optimiser les tables"

#. translators: %s is the number of revisions found in the database. It's a
#. formatted number, don't use %d.
#: inc/Engine/Admin/Settings/Page.php:1451
msgid "%s table to optimize in your database."
msgid_plural "%s tables to optimize in your database."
msgstr[0] "%s table à optimiser dans votre base de données."
msgstr[1] "%s tables à optimiser dans votre base de données."
msgstr[2] "%s tables à optimiser dans votre base de données."

#: inc/Engine/Admin/Settings/Page.php:1462
msgid "Schedule Automatic Cleanup"
msgstr "Planifier le nettoyage automatique"

#: inc/Engine/Admin/Settings/Page.php:1474
msgid "Frequency"
msgstr "Fréquence"

#: inc/Engine/Admin/Settings/Page.php:1482
msgid "Daily"
msgstr "Journalier"

#: inc/Engine/Admin/Settings/Page.php:1483
msgid "Weekly"
msgstr "Hebdomadaire"

#: inc/Engine/Admin/Settings/Page.php:1484
msgid "Monthly"
msgstr "Mensuel"

#: inc/Engine/Admin/Settings/Page.php:1501
msgid "Integrate your CDN"
msgstr "Intégrez votre CDN"

#: inc/Engine/Admin/Settings/Page.php:1513
msgid ""
"All URLs of static files (CSS, JS, images) will be rewritten to the CNAME(s)"
" you provide."
msgstr ""
"Toutes les URLs de vos fichiers statiques (CSS, JS, images) seront ré-"
"écrites avec le CNAME fourni."

#. translators: %1$s = opening link tag, %2$s = closing link tag.
#: inc/Engine/Admin/Settings/Page.php:1515
msgid ""
"Not required for services like Cloudflare and Sucuri. Please see our "
"available %1$sAdd-ons%2$s."
msgstr ""
"Non requis pour des services comme Cloudflare et Sucuri. Veuillez consulter "
"nos %1$sAdd-ons%2$s disponibles."

#. translators: %1$s = opening em tag, %2$l = list of add-on name(s), %3$s =
#. closing em tag.
#: inc/Engine/Admin/Settings/Page.php:1561
msgid ""
"%1$s%2$l Add-on%3$s is currently enabled. Configuration of the CDN settings "
"is not required for %2$l to work on your site."
msgid_plural ""
"%1$s%2$l Add-ons%3$s are currently enabled. Configuration of the CDN "
"settings is not required for %2$l to work on your site."
msgstr[0] ""
"%1$sL’Add-on %2$l%3$s est activé. La configuration des paramètres CDN n’est "
"pas nécessaire pour que %2$l fonctionne sur votre site."
msgstr[1] ""
"%1$s Les Add-ons %2$l %3$s  sont actuellement activés. La configuration des "
"paramètres CDN n’est pas nécessaire pour que %2$l fonctionnent sur votre "
"site."
msgstr[2] ""
"%1$s Les Add-ons %2$l %3$s  sont actuellement activés. La configuration des "
"paramètres CDN n’est pas nécessaire pour que %2$l fonctionnent sur votre "
"site."

#: inc/Engine/Admin/Settings/Page.php:1587
msgid "Enable Content Delivery Network"
msgstr "Activer le Content Delivery Network."

#: inc/Engine/Admin/Settings/Page.php:1596
#: inc/Engine/CDN/RocketCDN/AdminPageSubscriber.php:159
msgid "CDN CNAME(s)"
msgstr "CNAME(s) CDN"

#: inc/Engine/Admin/Settings/Page.php:1597
#: inc/Engine/CDN/RocketCDN/AdminPageSubscriber.php:160
msgid "Specify the CNAME(s) below"
msgstr "Indiquez le(s) CNAME(s) ci-dessous"

#: inc/Engine/Admin/Settings/Page.php:1604
msgid ""
"Specify URL(s) of files that should not get served via CDN (one per line)."
msgstr ""
"Indiquez les URLs des fichiers qui ne doivent pas être servies par le CDN "
"(une par ligne)."

#: inc/Engine/Admin/Settings/Page.php:1605
msgid ""
"The domain part of the URL will be stripped automatically.<br>Use (.*) "
"wildcards to exclude all files of a given file type located at a specific "
"path."
msgstr ""
"Le nom de domaine sera supprimé automatiquement de l'URL .<br>Utilisez les "
"expressions régulières  (.*) pour exclure plusieurs URLs pour un chemin "
"donné."

#: inc/Engine/Admin/Settings/Page.php:1628
#: inc/Engine/Admin/Settings/Page.php:1636
msgid "Heartbeat"
msgstr "Heartbeat"

#: inc/Engine/Admin/Settings/Page.php:1629
msgid "Control WordPress Heartbeat API"
msgstr "Contrôlez l'API WordPress Heartbeat"

#: inc/Engine/Admin/Settings/Page.php:1637
msgid ""
"Reducing or disabling the Heartbeat API’s activity can help save some of "
"your server’s resources."
msgstr ""
"Réduire ou désactiver l’activité de l’API Heartbeat peut permettre "
"d’économiser certaines ressources de votre serveur."

#: inc/Engine/Admin/Settings/Page.php:1643
msgid "Reduce or disable Heartbeat activity"
msgstr "Réduire ou désactiver l'activité Heartbeat"

#: inc/Engine/Admin/Settings/Page.php:1644
msgid ""
"Reducing activity will change Heartbeat frequency from one hit each minute "
"to one hit every 2 minutes."
msgstr ""
"La réduction de l'activité fera passer la fréquence Heartbeat d'un coup "
"toutes les minutes à un coup toutes les 2 minutes."

#: inc/Engine/Admin/Settings/Page.php:1644
msgid ""
"Disabling Heartbeat entirely may break plugins and themes using this API."
msgstr ""
"La désactivation complète de Heartbeat peut endommager les plugins et les "
"thèmes utilisant cette API."

#: inc/Engine/Admin/Settings/Page.php:1658
msgid "Do not limit"
msgstr "Ne pas limiter"

#: inc/Engine/Admin/Settings/Page.php:1659
msgid "Reduce activity"
msgstr "Réduire l'activité"

#: inc/Engine/Admin/Settings/Page.php:1660
msgid "Disable"
msgstr "Désactiver"

#: inc/Engine/Admin/Settings/Page.php:1668
msgid "Control Heartbeat"
msgstr "Contrôler Heartbeat"

#: inc/Engine/Admin/Settings/Page.php:1677
msgid "Behavior in backend"
msgstr "Comportement en Backend"

#: inc/Engine/Admin/Settings/Page.php:1684
msgid "Behavior in post editor"
msgstr "Comportement dans l’éditeur d'article"

#: inc/Engine/Admin/Settings/Page.php:1690
msgid "Behavior in frontend"
msgstr "Comportement en Frontend"

#: inc/Engine/Admin/Settings/Page.php:1709
#: views/settings/page-sections/tutorials.php:39
msgid "Add-ons"
msgstr "Add-ons"

#: inc/Engine/Admin/Settings/Page.php:1710
msgid "Add more features"
msgstr "Ajoutez des fonctionnalités"

#: inc/Engine/Admin/Settings/Page.php:1717
msgid "One-click Rocket Add-ons"
msgstr "One-click Rocket Add-ons"

#: inc/Engine/Admin/Settings/Page.php:1718
msgid ""
"One-Click Add-ons are features extending available options without "
"configuration needed. Switch the option \"on\" to enable from this screen."
msgstr ""
"Ce sont des fonctionnalités complémentaires étendant les option déjà "
"disponibles sans besoin de configuration. Mettez l'option sur \"ON\" pour "
"l'activer depuis cet écran."

#: inc/Engine/Admin/Settings/Page.php:1728
msgid "Rocket Add-ons"
msgstr "Rocket Add-ons"

#: inc/Engine/Admin/Settings/Page.php:1729
msgid "Rocket Add-ons are complementary features extending available options."
msgstr ""
"Ce sont de simples fonctionnalités complémentaires étendant les option déjà "
"disponibles."

#: inc/Engine/Admin/Settings/Page.php:1740
msgid "User Cache"
msgstr "Cache utilisateur"

#: inc/Engine/Admin/Settings/Page.php:1746
msgid ""
"If you need to create a dedicated set of cache files for each logged-in "
"WordPress user, you must activate this add-on."
msgstr ""
"Si vous avez besoin de créer un ensemble de fichiers de cache dédiés à "
"chaque utilisateur connecté à WordPress, vous devez activer cet add-on."

#. translators: %1$s = opening <a> tag, %2$s = closing </a> tag.
#: inc/Engine/Admin/Settings/Page.php:1748
msgid ""
"User cache is great when you have user-specific or restricted content on "
"your website.<br>%1$sLearn more%2$s"
msgstr ""
"Le cache utilisateur est parfait si vous avez du contenu spécifique ou "
"restreint pour les utilisateurs de votre site. <br>%1$sEn savoir plus%2$s"

#: inc/Engine/Admin/Settings/Page.php:1761
#: inc/Engine/Admin/Settings/Page.php:1930
msgid "Cloudflare"
msgstr "CloudFlare"

#: inc/Engine/Admin/Settings/Page.php:1767
msgid "Integrate your Cloudflare account with this add-on."
msgstr "Intégrez votre compte Cloudflare avec cet add-on"

#: inc/Engine/Admin/Settings/Page.php:1768
msgid ""
"Provide your account email, global API key, and domain to use options such "
"as clearing the Cloudflare cache and enabling optimal settings with WP "
"Rocket."
msgstr ""
"Entrez l’adresse e-mail, la clé API globale et le domaine de votre compte "
"CloudFlare pour pouvoir purger le cache Cloudflare et bénéficier des "
"réglages optimaux pour WP Rocket."

#. translators: %1$s = opening span tag, %2$s = closing span tag.
#: inc/Engine/Admin/Settings/Page.php:1771
msgid ""
"%1$sPlanning on using Automatic Platform Optimization (APO)?%2$s Just "
"activate the official Cloudflare plugin and configure it. WP Rocket will "
"automatically enable compatibility."
msgstr ""
"%1$sVous envisagez d'utiliser Automatic Platform Optimization (APO) ?%2$s Il"
" suffit d'activer le plugin officiel Cloudflare et de le configurer. WP "
"Rocket activera automatiquement la compatibilité."

#: inc/Engine/Admin/Settings/Page.php:1819
msgid "Varnish"
msgstr "Varnish"

#: inc/Engine/Admin/Settings/Page.php:1825
msgid "If Varnish runs on your server, you must activate this add-on."
msgstr "Si votre serveur utilise Varnish, vous devez activer cet add-on"

#. translators: %1$s = opening <a> tag, %2$s = closing </a> tag.
#: inc/Engine/Admin/Settings/Page.php:1827
msgid ""
"Varnish cache will be purged each time WP Rocket clears its cache to ensure "
"content is always up-to-date.<br>%1$sLearn more%2$s"
msgstr ""
"Le cache Varnish sera purgé à chaque fois que WP Rocket purgera son cache "
"pour vous assurer que le contenu soit toujours à jour.<br>%1$sEn savoir "
"plus%2$s"

#: inc/Engine/Admin/Settings/Page.php:1862
msgid "WebP Compatibility"
msgstr "Compatibilité WebP"

#: inc/Engine/Admin/Settings/Page.php:1868
msgid "Improve browser compatibility for WebP images."
msgstr "Améliore la compatibilité des navigateurs pour les images WebP."

#. translators: %1$s and %3$s = opening <a> tag, %2$s = closing </a> tag.
#: inc/Engine/Admin/Settings/Page.php:1872
msgid ""
"Enable this option if you would like WP Rocket to serve WebP images to "
"compatible browsers. Please note that WP Rocket cannot create WebP images "
"for you. To create WebP images we recommend %1$sImagify%2$s. %3$sMore "
"info%2$s"
msgstr ""
"Activez cette option si vous souhaitez que WP Rocket serve les images WebP "
"aux navigateurs compatibles. Veuillez noter que WP Rocket ne peut pas créer "
"d'images WebP pour vous. Pour créer des images WebP, nous vous recommandons "
"%1$sImagify%2$s. %3$sPlus d'infos%2$s"

#: inc/Engine/Admin/Settings/Page.php:1892
msgid "Clear the Sucuri cache when WP Rocket’s cache is cleared."
msgstr "Effacez le cache Sucuri lorsque le cache de WP Rocket est effacé."

#: inc/Engine/Admin/Settings/Page.php:1895
msgid ""
"Provide your API key to clear the Sucuri cache when WP Rocket’s cache is "
"cleared."
msgstr ""
"Fournissez votre clé API pour vider le cache Sucuri lorsque le cache de WP "
"Rocket est effacé."

#: inc/Engine/Admin/Settings/Page.php:1903
#: inc/Engine/Admin/Settings/Page.php:2047
msgid "Sucuri"
msgstr "Sucuri"

#: inc/Engine/Admin/Settings/Page.php:1909
msgid "Synchronize Sucuri cache with this add-on."
msgstr "Synchronisez le cache Sucuri avec cet add-on."

#: inc/Engine/Admin/Settings/Page.php:1947
msgid "Cloudflare credentials"
msgstr "Accès CloudFlare"

#: inc/Engine/Admin/Settings/Page.php:1956
msgid "Cloudflare settings"
msgstr "Réglages CloudFlare"

#: inc/Engine/Admin/Settings/Page.php:1970
msgctxt "Cloudflare"
msgid "Global API key:"
msgstr "Clé API globale :"

#: inc/Engine/Admin/Settings/Page.php:1971
msgctxt "Cloudflare"
msgid "Find your API key"
msgstr "Trouver votre clé API"

#: inc/Engine/Admin/Settings/Page.php:1983
msgctxt "Cloudflare"
msgid "Account email"
msgstr "E-mail du compte"

#: inc/Engine/Admin/Settings/Page.php:1992
msgctxt "Cloudflare"
msgid "Zone ID"
msgstr "Zone ID"

#: inc/Engine/Admin/Settings/Page.php:2002
msgid "Development mode"
msgstr "Mode développement"

#. translators: %1$s = link opening tag, %2$s = link closing tag.
#: inc/Engine/Admin/Settings/Page.php:2004
msgid ""
"Temporarily activate development mode on your website. This setting will "
"automatically turn off after 3 hours. %1$sLearn more%2$s"
msgstr ""
"Active temporairement le mode développement sur votre site. Ce réglage se "
"désactivera automatiquement après 3 heures. %1$sEn savoir plus%2$s"

#: inc/Engine/Admin/Settings/Page.php:2012
msgid "Optimal settings"
msgstr "Réglages optimaux"

#: inc/Engine/Admin/Settings/Page.php:2013
msgid ""
"Automatically enhances your Cloudflare configuration for speed, performance "
"grade and compatibility."
msgstr ""
"Améliore automatiquement votre configuration Cloudflare pour le temps de "
"chargement, les notes de performance et la compatibilité."

#: inc/Engine/Admin/Settings/Page.php:2021
msgid "Relative protocol"
msgstr "Protocol relatif"

#: inc/Engine/Admin/Settings/Page.php:2022
msgid ""
"Should only be used with Cloudflare's flexible SSL feature. URLs of static "
"files (CSS, JS, images) will be rewritten to use // instead of http:// or "
"https://."
msgstr ""
"Ne devrait être utilisé qu’avec la fonction Flexible SSL de CloudFlare. Les "
"URLs de vos fichiers statiques (CSS, JS, images) seront ré-écrites pour "
"utiliser // au lieu de http:// ou https:// ."

#: inc/Engine/Admin/Settings/Page.php:2060
msgid "Sucuri credentials"
msgstr "Accès Sucuri"

#: inc/Engine/Admin/Settings/Page.php:2073
msgctxt "Sucuri"
msgid ""
"Firewall API key (for plugin), must be in format {32 characters}/{32 "
"characters}:"
msgstr ""
"La clé API du pare-feu (pour cette extension) doit être au format {32 "
"caractères}/{32 caractères} :"

#: inc/Engine/Admin/Settings/Page.php:2074
msgctxt "Sucuri"
msgid "Find your API key"
msgstr "Trouver votre clé API"

#. translators: %1$s: opening strong tag, %2$s: closing strong tag, %3$s:
#. opening a tag, %4$s: option a tag, %5$s: opening a tag.
#: inc/Engine/Admin/Settings/Page.php:2295
msgid ""
"%1$sWP Rocket:%2$s the plugin has been updated to the 3.16 version. Our "
"brand new feature %3$sOptimize critical images%5$s is automatically "
"activated now! Also, the Cache tab was removed but the existing features "
"will remain working, %4$ssee more here%5$s."
msgstr ""
"%1$sWP Rocket :%2$s le plugin a été mis à jour vers la version 3.16. Notre "
"nouvelle fonctionnalité %3$sOptimiser les images essentielles%5$s est "
"automatiquement activée ! L'onglet Cache a également été supprimé, mais les "
"fonctionnalités habituelles sont toujours présentes, %4$sen savoir plus%5$s."

#: inc/Engine/Admin/Settings/Settings.php:361
msgid ""
"Sucuri Add-on: The API key for the Sucuri firewall must be in format "
"<code>{32 characters}/{32 characters}</code>."
msgstr ""
"Add-on Sucuri : La clé API pour le Sucuri firewall doit être au format "
"<code>{32 caractères}/{32 caractères}</code>."

#: inc/Engine/Admin/Settings/Settings.php:667
msgid ""
"Sorry! Adding /(.*) in Advanced Rules > Never Cache URL(s) was not saved "
"because it disables caching and optimizations for every page on your site."
msgstr ""
"Désolé ! L'ajout de /(.*) dans Règles avancées > Ne jamais mettre en cache "
"ces URL(s) n'a pas été sauvegardé car cette valeur désactive le cache et les"
" optimisations sur toutes les pages du site."

#: inc/Engine/Admin/Settings/Subscriber.php:172
msgid "Import, Export, Rollback"
msgstr "Importez, exportez, restaurez"

#: inc/Engine/Admin/Settings/Subscriber.php:197
#: views/settings/page-sections/imagify.php:14
msgid "Image Optimization"
msgstr "Optimisation des images"

#: inc/Engine/Admin/Settings/Subscriber.php:198
msgid "Compress your images"
msgstr "Compressez vos images"

#: inc/Engine/Admin/Settings/Subscriber.php:215
#: views/settings/page-sections/tutorials.php:48
msgid "Tutorials"
msgstr "Tutoriels"

#: inc/Engine/Admin/Settings/Subscriber.php:216
msgid "Getting started and how to videos"
msgstr "Prise en main et tutos vidéos"

#: inc/Engine/Cache/PurgeExpired/Subscriber.php:75
msgid "WP Rocket Expired Cache Interval"
msgstr "Intervalle de cache de WP Rocket expiré"

#: inc/Engine/Cache/WPCache.php:337
msgid "WP_CACHE value"
msgstr "valeur de WP_CACHE"

#: inc/Engine/Cache/WPCache.php:354
msgid "Cache"
msgstr "Cache"

#: inc/Engine/Cache/WPCache.php:358
msgid ""
"The WP_CACHE constant needs to be set to true for WP Rocket cache to work "
"properly"
msgstr ""
"La constante WP_CACHE doit être définie à true pour que le cache de WP "
"Rocket fonctionne correctement"

#: inc/Engine/Cache/WPCache.php:367
msgid "WP_CACHE is set to true"
msgstr "WP_CACHE est réglée sur true"

#: inc/Engine/Cache/WPCache.php:375
msgid "WP_CACHE is not set"
msgstr "WP_CACHE n’est pas définie"

#: inc/Engine/Cache/WPCache.php:383
msgid "WP_CACHE is set to false"
msgstr "WP_CACHE est réglée sur false"

#: inc/Engine/CDN/RocketCDN/AdminPageSubscriber.php:90
msgid "Next Billing Date"
msgstr "Prochaine date de facturation"

#: inc/Engine/CDN/RocketCDN/AdminPageSubscriber.php:99
msgid "No Subscription"
msgstr "Pas d'abonnement"

#: inc/Engine/CDN/RocketCDN/AdminPageSubscriber.php:135
msgid "Your RocketCDN subscription is currently active."
msgstr "Votre abonnement RocketCDN est actuellement actif."

#. translators: %1$s = opening <code> tag, %2$s = CDN URL, %3$s = closing
#. </code> tag.
#: inc/Engine/CDN/RocketCDN/AdminPageSubscriber.php:141
msgid "To use RocketCDN, replace your CNAME with %1$s%2$s%3$s."
msgstr "Pour utiliser RocketCDN, remplacez votre CNAME par %1$s%2$s%3$s."

#. translators: %1$is = opening link tag, %2$s = closing link tag.
#: inc/Engine/CDN/RocketCDN/AdminPageSubscriber.php:152
#: inc/Engine/CDN/RocketCDN/NoticesSubscriber.php:230
#: inc/Engine/CDN/RocketCDN/NoticesSubscriber.php:334
msgid "%1$sMore Info%2$s"
msgstr "%1$sPlus d'infos%2$s"

#: inc/Engine/CDN/RocketCDN/APIClient.php:134
msgid ""
"We could not fetch the current price because RocketCDN API returned an "
"unexpected error code."
msgstr ""
"Nous n'avons pas pu récupérer le prix actuel car l'API RocketCDN a renvoyé "
"un code d'erreur inattendu."

#: inc/Engine/CDN/RocketCDN/APIClient.php:140
msgid "RocketCDN is not available at the moment. Please retry later."
msgstr ""
"RocketCDN n'est pas disponible pour le moment. Veuillez réessayer plus tard."

#: inc/Engine/CDN/RocketCDN/APIClient.php:177
msgid "RocketCDN cache purge failed: Missing identifier parameter."
msgstr ""
"La purge du cache de RocketCDN a échoué : Paramètre d'identification "
"manquant."

#: inc/Engine/CDN/RocketCDN/APIClient.php:186
msgid "RocketCDN cache purge failed: Missing user token."
msgstr "La purge du cache de RocketCDN a échoué : Jeton utilisateur manquant."

#: inc/Engine/CDN/RocketCDN/APIClient.php:212
msgid ""
"RocketCDN cache purge failed: The API returned an unexpected response code."
msgstr ""
"La purge du cache de RocketCDN a échoué : L'API a renvoyé un code réponse "
"inattendu."

#: inc/Engine/CDN/RocketCDN/APIClient.php:221
msgid "RocketCDN cache purge failed: The API returned an empty response."
msgstr ""
"La purge du cache de RocketCDN a échoué : L'API a renvoyé une réponse vide."

#: inc/Engine/CDN/RocketCDN/APIClient.php:230
msgid "RocketCDN cache purge failed: The API returned an unexpected response."
msgstr ""
"La purge du cache de RocketCDN a échoué : L'API a renvoyé une réponse "
"inattendue."

#. translators: %s = message returned by the API.
#: inc/Engine/CDN/RocketCDN/APIClient.php:239
msgid "RocketCDN cache purge failed: %s."
msgstr "La purge du cache de RocketCDN a échoué : %s."

#: inc/Engine/CDN/RocketCDN/APIClient.php:247
msgid "RocketCDN cache purge successful."
msgstr "Purge du cache de RocketCDN réussie."

#: inc/Engine/CDN/RocketCDN/RESTSubscriber.php:137
#: tests/Integration/inc/Engine/CDN/RocketCDN/RESTSubscriber/enable.php:72
#: tests/Integration/inc/Engine/CDN/RocketCDN/RESTSubscriber/registerEnableRoute.php:302
msgid "RocketCDN enabled"
msgstr "RocketCDN activé"

#: inc/Engine/CDN/RocketCDN/RESTSubscriber.php:160
#: tests/Integration/inc/Engine/CDN/RocketCDN/RESTSubscriber/disable.php:31
#: tests/Integration/inc/Engine/CDN/RocketCDN/RESTSubscriber/registerDisableRoute.php:147
msgid "RocketCDN disabled"
msgstr "RocketCDN désactivé"

#. Translators: %s = date formatted using date_i18n() and get_option(
#. 'date_format' ).
#: inc/Engine/CDN/RocketCDN/views/cta-big.php:27
msgid "Valid until %s only!"
msgstr "Valable uniquement jusqu'au %s !"

#: inc/Engine/CDN/RocketCDN/views/cta-big.php:36
msgid "Speed up your website thanks to:"
msgstr "Accélérez votre site web grâce à :"

#. translators: %1$s = opening strong tag, %2$s = closing strong tag.
#: inc/Engine/CDN/RocketCDN/views/cta-big.php:42
msgid ""
"High performance Content Delivery Network (CDN) with %1$sunlimited "
"bandwidth%2$s"
msgstr ""
"CDN (Content Delivery Network) de haute performance à %1$sbande passante "
"illimitée%2$s"

#. translators: %1$s = opening strong tag, %2$s = closing strong tag.
#: inc/Engine/CDN/RocketCDN/views/cta-big.php:48
msgid ""
"Easy configuration: the %1$sbest CDN settings%2$s are automatically applied"
msgstr ""
"Configuration simple : les %1$smeilleurs réglages CDN%2$s sont "
"automatiquement appliqués"

#. translators: %1$s = opening strong tag, %2$s = closing strong tag.
#: inc/Engine/CDN/RocketCDN/views/cta-big.php:54
msgid ""
"WP Rocket integration: the CDN option is %1$sautomatically configured%2$s in"
" our plugin"
msgstr ""
"Intégration à WP Rocket : l'option CDN est %1$sautomatiquement "
"configurée%2$s dans notre plugin"

#: inc/Engine/CDN/RocketCDN/views/cta-big.php:58
msgid "Learn more about RocketCDN"
msgstr "En savoir plus sur RocketCDN"

#. translators: %1$s = discounted price, %2$s = regular price.
#: inc/Engine/CDN/RocketCDN/views/cta-big.php:65
msgid ""
"*$%1$s/month for 12 months then $%2$s/month. You can cancel your "
"subscription at any time."
msgstr ""
"*%1$s$/mois pour 12 mois puis %2$s$/mois. Vous pouvez résilier votre "
"abonnement à tout moment."

#: inc/Engine/CDN/RocketCDN/views/cta-big.php:86
msgid "Billed monthly"
msgstr "Facturation mensuelle"

#: inc/Engine/CDN/RocketCDN/views/cta-big.php:87
msgid "Get Started"
msgstr "S'abonner"

#: inc/Engine/CDN/RocketCDN/views/cta-big.php:92
msgid "Reduce this banner"
msgstr "Réduire cette bannière"

#: inc/Engine/CDN/RocketCDN/views/cta-small.php:17
msgid ""
"Speed up your website with RocketCDN, WP Rocket’s Content Delivery Network."
msgstr "Accélérez votre site web grâce à RocketCDN, le CDN de WP Rocket."

#: inc/Engine/CDN/RocketCDN/views/cta-small.php:20
#: inc/Engine/CDN/RocketCDN/views/promote-notice.php:13
msgid "Learn More"
msgstr "En savoir plus"

#: inc/Engine/CDN/RocketCDN/views/dashboard-status.php:23
msgid "RocketCDN is unavailable on local domains and staging sites."
msgstr "RocketCDN n'est pas disponible en local et sur les sites tests."

#: inc/Engine/CDN/RocketCDN/views/dashboard-status.php:32
msgid "Get RocketCDN"
msgstr "Obtenir RocketCDN"

#: inc/Engine/CDN/RocketCDN/views/promote-notice.php:11
msgid "New!"
msgstr "Nouveau !"

#: inc/Engine/CDN/RocketCDN/views/promote-notice.php:12
msgid ""
"Speed up your website with RocketCDN, WP Rocket’s Content Delivery Network!"
msgstr "Accélérez votre site web grâce à RocketCDN, le CDN de WP Rocket."

#: inc/Engine/Common/JobManager/Cron/Subscriber.php:185
msgid "WP Rocket process pending jobs"
msgstr "WP Rocket traite les tâches en attente"

#: inc/Engine/Common/JobManager/Cron/Subscriber.php:204
msgid "WP Rocket clear failed jobs"
msgstr "WP Rocket efface les tâches qui ont échoué"

#: inc/Engine/Common/JobManager/Cron/Subscriber.php:221
msgid "WP Rocket process on submit jobs"
msgstr "WP Rocket traite les tâches en cours"

#: inc/Engine/Common/Queue/RUCSSQueueRunner.php:265
msgid "Every minute"
msgstr "Chaque minute"

#: inc/Engine/CriticalPath/Admin/Admin.php:264
msgid "Regenerate Critical Path CSS"
msgstr "Régénérer Critical path CSS"

#: inc/Engine/CriticalPath/Admin/Post.php:144
#: views/cpcss/metabox/generate.php:47
msgid "Generate Specific CPCSS"
msgstr "Générer un CPCSS spécifique"

#: inc/Engine/CriticalPath/Admin/Post.php:145
#: views/cpcss/metabox/generate.php:45
msgid "Regenerate specific CPCSS"
msgstr "Regénérer un CPCSS spécifique"

#: inc/Engine/CriticalPath/Admin/Post.php:216
msgid "This feature is not available for non-public post types."
msgstr ""
"Cette fonctionnalité n'est pas disponible pour les publications qui ne sont "
"pas publiques."

#: inc/Engine/CriticalPath/Admin/Post.php:219
msgid "%l to use this feature."
msgstr "%l pour utiliser cette fonction."

#. translators: %s = post type.
#: inc/Engine/CriticalPath/Admin/Post.php:222
msgid "Publish the %s"
msgstr "Publié le %s"

#. translators: %s = post type.
#: inc/Engine/CriticalPath/Admin/Post.php:223
msgid "Enable Load CSS asynchronously in WP Rocket settings"
msgstr "Activez Chargement Asynchrone du CSS dans les réglages de WP Rocket"

#: inc/Engine/CriticalPath/Admin/Post.php:224
msgid "Enable Load CSS asynchronously in the options above"
msgstr "Activez Chargement Asynchrone du CSS dans les options ci-dessus"

#. translators: %1$s = type of content, %2$s = error message.
#: inc/Engine/CriticalPath/APIClient.php:64
msgid "Critical CSS for %1$s not generated. Error: %2$s"
msgstr "Le Critical CSS pour %1$s n'a pas été généré. Erreur : %2$s"

#. translators: %s = item URL.
#: inc/Engine/CriticalPath/APIClient.php:170
msgid ""
"Critical CSS for %1$s on mobile not generated. Error: The API returned an "
"empty response."
msgstr ""
"Le Critical CSS pour %1$s n'a pas été généré. Erreur : L'API a renvoyé une "
"réponse vide."

#. translators: %s = item URL.
#: inc/Engine/CriticalPath/APIClient.php:173
msgid ""
"Critical CSS for %1$s not generated. Error: The API returned an empty "
"response."
msgstr ""
"Le Critical CSS pour %1$s n'a pas été généré. Erreur : L'API a renvoyé une "
"réponse vide."

#. translators: %s = item URL.
#: inc/Engine/CriticalPath/APIClient.php:185
msgid "Critical CSS for %1$s on mobile not generated."
msgstr "Le Critical CSS pour %1$s sur mobile n'a pas été généré."

#. translators: %s = item URL.
#. translators: %1$s = item URL or item type.
#: inc/Engine/CriticalPath/APIClient.php:187
#: inc/Engine/CriticalPath/ProcessorService.php:194
msgid "Critical CSS for %1$s not generated."
msgstr "Le Critical CSS pour %1$s n'a pas été généré."

#. translators: %s = URL.
#: inc/Engine/CriticalPath/APIClient.php:195
msgid ""
"Critical CSS for %1$s on mobile not generated. Error: The API returned an "
"invalid response code."
msgstr ""
"Le Critical CSS pour %1$s sur mobile n'a pas été généré. Erreur : L'API a "
"renvoyé une réponse vide."

#. translators: %s = URL.
#: inc/Engine/CriticalPath/APIClient.php:197
msgid ""
"Critical CSS for %1$s not generated. Error: The API returned an invalid "
"response code."
msgstr ""
"Le Critical CSS pour %1$s n'a pas été généré. Erreur : L'API a renvoyé une "
"réponse vide."

#. translators: %1$s = error message.
#: inc/Engine/CriticalPath/APIClient.php:205
#: inc/Engine/CriticalPath/ProcessorService.php:201
msgid "Error: %1$s"
msgstr "Erreur : %1$s"

#: inc/Engine/CriticalPath/CriticalCSSSubscriber.php:177
msgid "Critical CSS generation is currently running."
msgstr "La génération du Critical CSS est en cours."

#. Translators: %1$s = opening link tag, %2$s = closing link tag.
#: inc/Engine/CriticalPath/CriticalCSSSubscriber.php:182
msgid "Go to the %1$sWP Rocket settings%2$s page to track progress."
msgstr ""
"Rendez-vous aux %1$sréglages de WP Rocket%2$s pour suivre la progression."

#. Translators: %1$d = number of critical CSS generated, %2$d = total number
#. of critical CSS to generate.
#: inc/Engine/CriticalPath/CriticalCSSSubscriber.php:397
msgid ""
"Critical CSS generation is currently running: %1$d of %2$d page types "
"completed. (Refresh this page to view progress)"
msgstr ""
"La génération du Critical CSS est en cours : %1$d de %2$d de types de page "
"complétés. (Rafraîchir pour voir la progression)"

#. Translators: %1$d = number of critical CSS generated, %2$d = total number
#. of critical CSS to generate.
#: inc/Engine/CriticalPath/CriticalCSSSubscriber.php:473
msgid "Critical CSS generation finished for %1$d of %2$d page types."
msgstr ""
"La génération du Critical CSS est en terminée : %1$d de %2$d de types de "
"page."

#: inc/Engine/CriticalPath/CriticalCSSSubscriber.php:480
msgid "Critical CSS generation encountered one or more errors."
msgstr "La génération du CSS critique a rencontré une ou plusieurs erreur(s)."

#: inc/Engine/CriticalPath/CriticalCSSSubscriber.php:480
msgid "Learn more."
msgstr "En savoir plus."

#. translators: %1$ = opening bold tag, %2$ = closing bold tag.
#: inc/Engine/CriticalPath/CriticalCSSSubscriber.php:841
msgid ""
"We highly recommend the %1$supdated Remove Unused CSS%2$s for a better CSS "
"optimization. Load CSS Asynchronously is always available as a back-up."
msgstr ""
"Nous recommandons vivement %1$sSupprimer les ressources CSS inutilisées%2$s "
"pour une meilleure optimisation CSS. Le Chargement Asynchrone du CSS est "
"toujours disponible comme solution de secours."

#: inc/Engine/CriticalPath/CriticalCSSSubscriber.php:846
msgid "Stay with the old option"
msgstr "Garder l'ancienne option"

#. translators: %s = item URL.
#: inc/Engine/CriticalPath/DataManager.php:68
msgid ""
"Critical CSS for %1$s on mobile not generated. Error: The destination folder"
" could not be created."
msgstr ""
"Le Critical CSS pour %1$s pour mobile n'a pas été généré. Erreur : Le "
"dossier de destination n'a pas pu être créé."

#. translators: %s = item URL.
#: inc/Engine/CriticalPath/DataManager.php:71
msgid ""
"Critical CSS for %1$s not generated. Error: The destination folder could not"
" be created."
msgstr ""
"Le Critical CSS pour %1$s n'a pas été généré. Erreur : Le dossier de "
"destination n'a pas pu être créé."

#: inc/Engine/CriticalPath/DataManager.php:106
msgid "Critical CSS file for mobile does not exist"
msgstr "Le fichier Critical CSS pour mobile n’existe pas"

#: inc/Engine/CriticalPath/DataManager.php:108
msgid "Critical CSS file does not exist"
msgstr "Le fichier Critical CSS n’existe pas"

#: inc/Engine/CriticalPath/DataManager.php:120
msgid "Critical CSS file for mobile cannot be deleted"
msgstr "Le fichier Critical CSS pour mobile ne peut être supprimé"

#: inc/Engine/CriticalPath/DataManager.php:122
msgid "Critical CSS file cannot be deleted"
msgstr "Le fichier Critical CSS ne peut être supprimé"

#. translators: %1$s = item URL or item type.
#: inc/Engine/CriticalPath/ProcessorService.php:187
msgid "Mobile Critical CSS for %1$s not generated."
msgstr "Le Critical CSS mobile pour %1$s n'a pas été généré."

#. translators: %1$s = Item URL or item type.
#: inc/Engine/CriticalPath/ProcessorService.php:228
msgid "Critical CSS for %s in progress."
msgstr "Critical CSS pour %s en cours."

#. translators: %1$s = Item URL or item type.
#: inc/Engine/CriticalPath/ProcessorService.php:262
msgid "Mobile Critical CSS for %s generated."
msgstr "Critical CSS mobile pour %s généré."

#. translators: %1$s = Item URL or item type.
#: inc/Engine/CriticalPath/ProcessorService.php:273
msgid "Critical CSS for %s generated."
msgstr "Critical CSS pour %s généré."

#: inc/Engine/CriticalPath/ProcessorService.php:295
msgid "Critical CSS file deleted successfully."
msgstr "Le fichier Critical CSS a bien été supprimé."

#. translators: %1$s = Item URL or item type.
#: inc/Engine/CriticalPath/ProcessorService.php:317
msgid "Mobile Critical CSS for %1$s timeout. Please retry a little later."
msgstr ""
"Timeout pour le Critical CSS mobile de%1$s. Veuillez réessayer un peu plus "
"tard."

#. translators: %1$s = Item URL or item type.
#: inc/Engine/CriticalPath/ProcessorService.php:330
msgid "Critical CSS for %1$s timeout. Please retry a little later."
msgstr ""
"Timeout pour le Critical CSS de%1$s. Veuillez réessayer un peu plus tard."

#: inc/Engine/CriticalPath/RESTWP.php:141
msgid "Mobile CPCSS generation not enabled."
msgstr "Génération du Critical CSS mobile non activée"

#: inc/Engine/CriticalPath/RESTWPPost.php:36
#: inc/Engine/CriticalPath/RESTWPPost.php:69
msgid "Requested post does not exist."
msgstr "Le post demandé n'existe pas."

#: inc/Engine/CriticalPath/RESTWPPost.php:46
msgid "Cannot generate CPCSS for unpublished post."
msgstr "Impossible de générer un CPCSS pour un post non publié."

#: inc/Engine/HealthCheck/HealthCheck.php:142
msgid "Scheduled Cache Purge"
msgstr "Purge de cache planifiée"

#: inc/Engine/HealthCheck/HealthCheck.php:143
msgid "Scheduled Database Optimization"
msgstr "Optimisation de la base de données planifiée"

#: inc/Engine/HealthCheck/HealthCheck.php:144
msgid "Database Optimization Process"
msgstr "Optimisation de la base de données"

#: inc/Engine/HealthCheck/HealthCheck.php:145
msgctxt "noun"
msgid "Preload"
msgstr "Préchargement"

#: inc/Engine/HealthCheck/HealthCheck.php:146
msgid "Critical Path CSS Generation Process"
msgstr "La génération du Critical Path CSS"

#. translators: %1$s = <strong>, %2$s = price, %3$s = </strong>.
#: inc/Engine/License/Renewal.php:76
msgid "Renew before it is too late, you will only pay %1$s%2$s%3$s!"
msgstr ""
"Renouvelez avant qu'il ne soit trop tard, vous ne payerez que %1$s%3$s%2$s."

#. translators: %1$s = <strong>, %2$s = discount, %3$s = </strong>,%4$s =
#. <strong>, %5$s = price, %6$s=</strong>.
#: inc/Engine/License/Renewal.php:85
msgid ""
"Renew with a %1$s%2$s discount%3$s before it is too late, you will only pay "
"%4$s%5$s%6$s!"
msgstr ""
"Renouvelez maintenant avant qu'il ne soit trop tard et profitez d'une "
"%1$sréduction de %2$s %3$s, vous ne paierez que %4$s%6$s%5$s !"

#. translators: %1$s = <strong>, %2$s = </strong>, %3$s = price.
#: inc/Engine/License/Renewal.php:139
msgid "Renew your license for 1 year now at %1$s%3$s%2$s."
msgstr "Renouvelez votre licence pour 1 an maintenant à %1$s%3$s%2$s."

#. translators: %1$s = <strong>, %2$s = </strong>, %3$s = discount percentage,
#. %4$s = price.
#: inc/Engine/License/Renewal.php:152
msgid ""
"Renew your license for 1 year now and get %1$s%3$s OFF%2$s immediately: you "
"will only pay %1$s%4$s%2$s!"
msgstr ""
"Renouvelez votre licence pour 1 an et bénéficiez d’une réduction immédiate "
"%1$sde %3$s%2$s: vous ne paierez que %1$s%4$s%2$s!"

#. translators: %1$s = <strong>, %2$s = </strong>, %3$s = discount price.
#: inc/Engine/License/Renewal.php:218
msgid "Renew before it is too late, you will pay %1$s%3$s%2$s."
msgstr "Renouvelez avant qu'il ne soit trop tard, vous payerez %1$s%3$s%2$s."

#. translators: %1$s = <strong>, %2$s = discount percentage, %3$s = </strong>,
#. %4$s = discount price.
#: inc/Engine/License/Renewal.php:227
msgid ""
"Renew with a %1$s%2$s discount%3$s before it is too late, you will only pay "
"%1$s%4$s%3$s!"
msgstr ""
"Renouvelez maintenant et profitez d'une %1$sréduction de %2$s %3$savant "
"qu'il ne soit trop tard, vous ne paierez que %1$s%4$s%3$s !"

#. translators: %1$s = <a>, %2$s = </a>.
#: inc/Engine/License/Renewal.php:542
msgid ""
"You need a valid license to continue using this feature. %1$sRenew now%2$s "
"before losing access."
msgstr ""
"Vous devez détenir une licence valide pour continuer à utiliser cette "
"option. %1$sRenouveler maintenant%2$s avant de perdre l'accès."

#. translators: %1$s = <a>, %2$s = </a>.
#: inc/Engine/License/Renewal.php:563
msgid "You need an active license to enable this option. %1$sRenew now%2$s."
msgstr ""
"Vous devez détenir une licence active pour activer cette option. "
"%1$sRenouveler maintenant%2$s."

#. translators: %1$s = <a>, %2$s = </a>.
#: inc/Engine/License/Renewal.php:591
msgid "You need an active license to enable this option. %1$sMore info%2$s."
msgstr ""
"Vous devez détenir une licence active pour activer cette option. %1$sPlus "
"d'infos%2$s."

#. translators: %1$s = promotion name, %2$s = <br>, %3$s = <strong>, %4$s =
#. promotion discount percentage, %5$s = </strong>.
#: inc/Engine/License/Upgrade.php:251
msgid ""
"Take advantage of %1$s to speed up more websites:%2$s get a %3$s%4$s off%5$s"
" for %3$supgrading your license to Plus or Infinite!%5$s"
msgid_plural ""
"Take advantage of %1$s to speed up more websites:%2$s get a %3$s%4$s off%5$s"
" for %3$supgrading your license to Infinite!%5$s"
msgstr[0] ""
"Profitez de%1$s pour accélérez plus de sites : %2$s bénéficiez de %3$s%4$s "
"de réduction %5$s sur la %3$s mise à niveau de votre licence vers Plus ou "
"Infinite ! %5$s"
msgstr[1] ""
"Profitez de %1$s pour accélérez plus de sites : %2$s bénéficiez de %3$s%4$s "
"de réduction %5$s sur la %3$s mise à niveau de votre licence vers Infinite !"
" %5$s"
msgstr[2] ""
"Profitez de %1$s pour accélérez plus de sites : %2$s bénéficiez de %3$s%4$s "
"de réduction %5$s sur la %3$s mise à niveau de votre licence vers Infinite !"
" %5$s"

#: inc/Engine/License/Upgrade.php:382 inc/Engine/License/Upgrade.php:405
msgid "Unlimited"
msgstr "Illimité"

#. translators: %s = promotion discount percentage.
#: inc/Engine/License/views/promo-banner.php:16
msgid "%s off"
msgstr "- %s"

#. translators: %s = promotion name.
#: inc/Engine/License/views/promo-banner.php:21
msgid "%s promotion is live!"
msgstr "La promo %s est en ligne !"

#: inc/Engine/License/views/promo-banner.php:27
msgid "Hurry Up! Deal ends in:"
msgstr "Dépêchez-vous ! Le deal se termine dans :"

#: inc/Engine/License/views/promo-banner.php:31
#: inc/Engine/License/views/renewal-soon-banner.php:14
msgid "Minutes"
msgstr "Minutes"

#: inc/Engine/License/views/promo-banner.php:32
#: inc/Engine/License/views/renewal-soon-banner.php:15
msgid "Seconds"
msgstr "secondes"

#: inc/Engine/License/views/promo-banner.php:34
#: inc/Engine/License/views/upgrade-section.php:11
msgid "Upgrade now"
msgstr "Mettez à niveau maintenant"

#: inc/Engine/License/views/renewal-expired-banner-ocd-disabled.php:11
msgid "The Optimize CSS Delivery feature is disabled."
msgstr "L'option Optimiser le chargement du CSS est désactivée."

#: inc/Engine/License/views/renewal-expired-banner-ocd-disabled.php:15
msgid ""
"You can no longer use the Remove Unused CSS or Load CSS asynchronously "
"options."
msgstr ""
"Vous ne pouvez plus utiliser les options Supprimer les ressources CSS "
"inutilisées ou Chargement asynchrone du CSS."

#. translators: %1$s = <strong>, %2$s = </strong>.
#: inc/Engine/License/views/renewal-expired-banner-ocd-disabled.php:20
msgid ""
"You need an %1$sactive license%2$s to keep optimizing your CSS delivery, "
"which addresses a PageSpeed Insights recommendation and improves your page "
"performance."
msgstr ""
"Vous avez besoin d'une %1$slicence active%2$s pour continuer à optimiser le "
"chargement de votre CSS, ce qui répond à une recommandation de PageSpeed "
"Insights et améliore les performances de vos pages."

#: inc/Engine/License/views/renewal-expired-banner-ocd-disabled.php:29
#: inc/Engine/License/views/renewal-expired-banner-ocd.php:39
#: inc/Engine/License/views/renewal-expired-banner.php:27
#: inc/Engine/License/views/renewal-soon-banner.php:31
msgid "Renew now"
msgstr "Renouveler maintenant"

#: inc/Engine/License/views/renewal-expired-banner-ocd.php:11
msgid "You will soon lose access to some features."
msgstr "Vous perdrez bientôt l'accès à certaines options."

#. translators: %1$s = <strong>, %2$s = </strong>.
#: inc/Engine/License/views/renewal-expired-banner-ocd.php:18
msgid ""
"You need an %1$sactive license to continue optimizing your CSS delivery%2$s."
msgstr ""
"Vous avez besoin d'une %1$slicence active pour continuer à optimiser le "
"chargement de votre CSS%2$s."

#: inc/Engine/License/views/renewal-expired-banner-ocd.php:24
msgid ""
"The Remove Unused CSS and Load CSS asynchronously features are great options"
" to address the PageSpeed Insights recommendations and improve your website "
"performance."
msgstr ""
"Les options Supprimer les ressources CSS inutilisées et Chargement "
"Asynchrone du CSS sont des options intéressantes pour répondre aux "
"recommandations de PageSpeed Insights et améliorer les performances de votre"
" site Web."

#. translators: %1$s = <strong>, %2$s = </strong>, %3$s = date.
#: inc/Engine/License/views/renewal-expired-banner-ocd.php:29
msgid "These features will be %1$sautomatically disabled on %3$s%2$s."
msgstr "Ces options seront %1$sautomatiquement désactivées le %3$s%2$s."

#: inc/Engine/License/views/renewal-expired-banner.php:11
msgid "Your WP Rocket license is expired!"
msgstr "Votre licence WP Rocket est expirée !"

#. translators: %1$s = <strong>, %2$s = </strong>.
#: inc/Engine/License/views/renewal-expired-banner.php:18
msgid ""
"Your website could be much faster if it could take advantage of our %1$snew "
"features and enhancements%2$s. 🚀"
msgstr ""
"Votre site web pourrait être beaucoup plus rapide s'il pouvait profiter de "
"nos %1$s nouvelles fonctionnalités et améliorations%2$s.🚀"

#. translators: %1$s = <strong>, %2$s = </strong>.
#: inc/Engine/License/views/renewal-soon-banner.php:22
msgid ""
"Your %1$sWP Rocket license is about to expire%2$s: you will soon lose access"
" to product updates and support."
msgstr ""
"Votre %1$slicence WP Rocket est sur le point d'expirer%2$s : vous perdrez "
"bientôt l'accès aux mises à jour et au support."

#: inc/Engine/License/views/upgrade-popin.php:12
msgid "Speed Up More Websites"
msgstr "Accélérez plus de sites web"

#. translators: %1$s = opening strong tag, %2$s = closing strong tag.
#: inc/Engine/License/views/upgrade-popin.php:19
msgid ""
"You can use WP Rocket on more websites by upgrading your license. To "
"upgrade, simply pay the %1$sprice difference%2$s between your current and "
"new licenses, as shown below."
msgstr ""
"Vous pouvez utiliser WP Rocket sur plus de sites en mettant à niveau votre "
"licence. Pour cela, il vous suffit de payer la %1$sdifférence de prix%2$s "
"entre votre licence actuelle et la nouvelle, comme indiqué ci-dessous."

#. translators: %1$s = opening strong tag, %2$s = closing strong tag.
#: inc/Engine/License/views/upgrade-popin.php:25
msgid ""
"%1$sN.B.%2$s: Upgrading your license does not change your expiration date"
msgstr ""
"%1$sN.B.%2$s: La mise à niveau de votre licence ne modifie pas votre date "
"d'expiration"

#. translators: %s = price.
#: inc/Engine/License/views/upgrade-popin.php:35
msgid "Save $%s"
msgstr "Économisez %s$"

#. translators: %s = number of websites.
#: inc/Engine/License/views/upgrade-popin.php:48
msgid "%s websites"
msgstr "%s sites"

#. translators: %s = license name.
#: inc/Engine/License/views/upgrade-popin.php:54
msgid "Upgrade to %s"
msgstr "Mettez à niveau vers %s"

#: inc/Engine/License/views/upgrade-section.php:11
msgid ""
"You can use WP Rocket on more websites by upgrading your license (you will "
"only pay the price difference between your current and new licenses)."
msgstr ""
"Vous pouvez utiliser WP Rocket sur un plus grand nombre de sites web en "
"mettant à niveau votre licence (vous ne paierez que la différence de prix "
"entre votre licence actuelle et la nouvelle)."

#. translators: %1$s = plugin name.
#: inc/Engine/Media/AboveTheFold/Admin/Controller.php:143
msgid "%1$s: Critical images cleared!"
msgstr "%1$s : Images Critiques effacées !"

#: inc/Engine/Media/AboveTheFold/AJAX/Controller.php:131
msgid "Script error"
msgstr "Erreur du script"

#: inc/Engine/Media/AboveTheFold/AJAX/Controller.php:134
msgid "Script timeout"
msgstr "Timeout du script"

#: inc/Engine/Media/Lazyload/AdminSubscriber.php:62
msgid "LazyLoad for images"
msgstr "LazyLoad sur les images"

#: inc/Engine/Media/Lazyload/AdminSubscriber.php:63
msgid "LazyLoad for iframes/videos"
msgstr "LazyLoad sur les iframes et vidéos"

#: inc/Engine/Media/Lazyload/CSS/Admin/Subscriber.php:48
msgid "LazyLoad CSS backgrounds"
msgstr "LazyLoad background CSS"

#: inc/Engine/Optimization/DelayJS/Admin/SiteList.php:195
msgid "Analytics & Ads"
msgstr "Analyses & publicités"

#: inc/Engine/Optimization/DelayJS/Admin/SiteList.php:200
msgid "Plugins"
msgstr "Extensions"

#: inc/Engine/Optimization/DelayJS/Admin/SiteList.php:205
msgid "Themes"
msgstr "Thèmes"

#: inc/Engine/Optimization/DynamicLists/DynamicLists.php:101
msgid ""
"You need an active license to get the latest version of the lists from our "
"server."
msgstr ""
"Vous devez avoir une licence active pour obtenir la dernière version des "
"listes de notre serveur."

#: inc/Engine/Optimization/DynamicLists/DynamicLists.php:116
msgid "Could not get updated lists from server."
msgstr "Impossible d'obtenir les listes mises à jour depuis le serveur."

#: inc/Engine/Optimization/DynamicLists/DynamicLists.php:125
msgid "Lists are up to date."
msgstr "Les listes sont à jour."

#: inc/Engine/Optimization/DynamicLists/DynamicLists.php:134
msgid "Could not update lists."
msgstr "Impossible de mettre à jour les listes."

#: inc/Engine/Optimization/DynamicLists/DynamicLists.php:143
msgid "Lists are successfully updated."
msgstr "Les listes ont été mises à jour avec succès."

#: inc/Engine/Optimization/DynamicLists/ServiceProvider.php:72
msgid "Default Lists"
msgstr "Listes de défaut"

#: inc/Engine/Optimization/DynamicLists/ServiceProvider.php:78
msgid "Delay JavaScript Execution Exclusion Lists"
msgstr "Listes d'exclusion pour Reporter l'exécution JavaScript"

#: inc/Engine/Optimization/DynamicLists/ServiceProvider.php:84
msgid "Incompatible plugins Lists"
msgstr "Listes des extensions incompatibles"

#: inc/Engine/Optimization/Minify/AdminSubscriber.php:65
msgid "Minify/combine JavaScript"
msgstr "Minifier / Combiner le JavaScript"

#: inc/Engine/Optimization/Minify/CSS/AdminSubscriber.php:150
msgid "Minify CSS"
msgstr "Minifier les fichiers CSS"

#. translators: %1$s = plugin name, %2$s = table name, %3$s = <a> open tag,
#. %4$s = </a> closing tag.
#: inc/Engine/Optimization/RUCSS/Admin/Settings.php:253
msgid ""
"%1$s: Could not create the %2$s table in the database which is necessary for"
" the Remove Unused CSS feature to work. Please reach out to %3$sour "
"support%4$s."
msgstr ""
"%1$s: Impossible de créer la %2$s table nécessaire au fonctionnement de "
"Supprimer les Ressources CSS inutilisées dans la base de données. "
"%3$sVeuillez contacter notre support%4$s."

#. translators: %1$s = plugin name.
#: inc/Engine/Optimization/RUCSS/Admin/Subscriber.php:274
msgid "%1$s: Used CSS cache cleared!"
msgstr "%1$s: Cache du CSS utilisé vidée !"

#. translators: %1$s = plugin name.
#: inc/Engine/Preload/Admin/Settings.php:57
msgid ""
"%1$s: The preload service is now active. After the initial preload it will "
"continue to cache all your pages whenever they are purged. No further action"
" is needed."
msgstr ""
"%1$s: Le Préchargement est maintenant actif. Après le préchargement initial,"
" il continuera à mettre en cache toutes vos pages chaque fois qu'elles "
"seront purgées. Aucune autre action n'est nécessaire."

#: inc/Engine/Preload/Cron/Subscriber.php:138
msgid "WP Rocket Preload pending jobs"
msgstr "Tâches en attente de l'option Préchargement de WP Rocket"

#: inc/Engine/Preload/Cron/Subscriber.php:166
msgid "WP Rocket Preload revert stuck failed jobs"
msgstr ""
"Le Prechargement de WP Rocket rétablit les tâches bloquées qui ont échoué"

#: inc/Engine/Saas/Admin/AdminBar.php:77
#: inc/Engine/Saas/Admin/AdminBar.php:198
msgid "Clear Critical Images"
msgstr "Effacer les images critiques"

#: inc/Engine/Saas/Admin/AdminBar.php:164
msgid "Clear Critical Images of this URL"
msgstr "Effacer les images critiques pour cette URL"

#: inc/Engine/Saas/Admin/AdminBar.php:167
msgid "Clear Used CSS of this URL"
msgstr "Nettoyer le CSS utilisé pour cet URL"

#: inc/Engine/Saas/Admin/AdminBar.php:197
msgid "Critical Images Cache"
msgstr "Cache des images essentielles"

#: inc/Engine/Saas/Admin/AdminBar.php:201
msgid "Remove Used CSS Cache"
msgstr "Supprimer le cache du CSS utilisé"

#. translators: %1$s = plugin name, %2$s = number of seconds.
#: inc/Engine/Saas/Admin/Notices.php:104
msgid ""
"%1$s: Please wait %2$s seconds. The Remove Unused CSS service is processing "
"your pages, the plugin is optimizing LCP and the images above the fold."
msgstr ""
"%1$s : Veuillez patienter %2$s scrondes. La Suppression des ressources CSS "
"inutilisées traite vos pages, le plugin optimise le LCP et les images au-"
"dessus de la ligne de flottaison."

#. translators: %1$s = plugin name, %2$s = number of URLs, %3$s = number of
#. seconds.
#: inc/Engine/Saas/Admin/Notices.php:147
msgid ""
"%1$s: The LCP element has been optimized, and the images above the fold were excluded from lazyload. The Used CSS of your homepage has been processed.\n"
"\t\t\t WP Rocket will continue to generate Used CSS for up to %2$s URLs per %3$s second(s)."
msgstr ""
"%1$s : L'élément LCP a été optimisé, et les images au-dessus de la ligne de flottaison ont été exclues du lazyload. Le CSS utilisé de votre page d'accueil a été traité.\n"
"\t\t\tWP Rocket continuera à générer les CSS utilisés pour un maximum de %2$s URL par %3$s seconde(s)."

#. translators: %1$s = opening link tag, %2$s = closing link tag.
#: inc/Engine/Saas/Admin/Notices.php:170
msgid "We suggest enabling %1$sPreload%2$s for the fastest results."
msgstr ""
"Nous vous suggérons d'activer le %1$sPréchargement%2$s pour obtenir des "
"résultats plus rapides."

#. translators: %1$s = opening link tag, %2$s = closing link tag.
#: inc/Engine/Saas/Admin/Notices.php:180
msgid "To learn more about the process check our %1$sdocumentation%2$s."
msgstr ""
"Pour en savoir plus sur le processus, consultez notre %1$sdocumentation%2$s."

#: inc/Engine/Saas/Admin/Notices.php:246
msgid ""
"We couldn't generate the used CSS because you're using a nulled version of "
"WP Rocket. You need an active license to use the Remove Unused CSS feature "
"and further improve your website's performance."
msgstr ""
"Nous n'avons pas pu générer le CSS utilisé car vous utilisez une version "
"piratée de WP Rocket. Vous avez besoin d'une licence active pour utiliser "
"l'option Supprimer les CSS inutilisés et améliorer davantage les "
"performances de votre site Web."

#. translators: %1$s = promo percentage.
#: inc/Engine/Saas/Admin/Notices.php:249
msgid "Click here to get a WP Rocket single license at %1$s off!"
msgstr ""
"Cliquez ici pour obtenir une licence Single de WP Rocket à moins %1$s !"

#. translators: %1$s = <a> open tag, %2$s = </a> closing tag.
#: inc/Engine/Saas/Admin/Notices.php:302
msgid ""
"It seems a security plugin or the server's firewall prevents WP Rocket from "
"accessing the SaaS features. IPs listed %1$shere in our documentation%2$s "
"should be added to your allowlists:"
msgstr ""
"Il semble qu'un plugin de sécurité ou le pare-feu du serveur empêche WP "
"Rocket d'accéder aux options SaaS. Les IP répertoriées %1$sdans notre "
"documentation%2$s doivent être autorisées :"

#: inc/Engine/Saas/Admin/Notices.php:307
msgid "- In the security plugin, if you are using one"
msgstr "- Dans le plugin de sécurité, si vous en utilisez un"

#: inc/Engine/Saas/Admin/Notices.php:308
msgid "- In the server's firewall. Your host can help you with this"
msgstr ""
"- Dans le pare-feu du serveur. Votre hébergeur peut vous aider à le faire"

#: inc/functions/admin.php:21
msgid ""
"There seems to be an issue validating your license. Please see the error "
"message below."
msgid_plural ""
"There seems to be an issue validating your license. You can see the error "
"messages below."
msgstr[0] ""
"Il semble y avoir un problème avec la validation de votre licence. Vous "
"pouvez voir le message d'erreur ci-dessous."
msgstr[1] ""
"Il semble y avoir un problème avec la validation de votre licence. Vous "
"pouvez voir les messages d'erreur ci-dessous."
msgstr[2] ""
"Il semble y avoir un problème avec la validation de votre licence. Vous "
"pouvez voir les messages d'erreur ci-dessous."

#: inc/functions/admin.php:361
msgid "Server type:"
msgstr "Type de serveur :"

#: inc/functions/admin.php:370
msgid "PHP version number:"
msgstr "Version PHP :"

#: inc/functions/admin.php:379
msgid "WordPress version number:"
msgstr "Version de WordPress :"

#: inc/functions/admin.php:388
msgid "WordPress multisite:"
msgstr "WordPress Multisite :"

#: inc/functions/admin.php:397
msgid "Current theme:"
msgstr "Thème actif :"

#: inc/functions/admin.php:406
msgid "Current site language:"
msgstr "Langage du site :"

#: inc/functions/admin.php:415
msgid "Active plugins:"
msgstr "Extensions actives :"

#: inc/functions/admin.php:418
msgid "Plugin names of all active plugins"
msgstr "Noms des toutes les extensions actives"

#: inc/functions/admin.php:424
msgid "Anonymized WP Rocket settings:"
msgstr "Réglages de WP Rocket anonymes :"

#: inc/functions/admin.php:427
msgid "Which WP Rocket settings are active"
msgstr "Quels sont les réglages de WP Rocket activés"

#: inc/functions/admin.php:433
msgid "WP Rocket license type"
msgstr "Type de licence WP Rocket"

#: inc/functions/options.php:429
msgid "The provided license data are not valid."
msgstr "Les données de licence fournies ne sont pas valides."

#. Translators: %1$s = opening link tag, %2$s = closing link tag.
#: inc/functions/options.php:432
msgid "To resolve, please %1$scontact support%2$s."
msgstr "Pour résoudre ce problème, %1$sveuillez contacter le support%2$s."

#. Translators: %1$s = opening em tag, %2$s = closing em tag, %3$s = opening
#. link tag, %4$s closing link tag.
#: inc/functions/options.php:491 inc/functions/options.php:530
msgid ""
"License validation failed. Our server could not resolve the request from "
"your website."
msgstr ""
"La validation de la licence a échoué. Notre serveur n'a pas pu résoudre la "
"demande de votre site Web.."

#. Translators: %1$s = opening em tag, %2$s = closing em tag, %3$s = opening
#. link tag, %4$s closing link tag.
#: inc/functions/options.php:491
msgid ""
"Try clicking %1$sValidate License%2$s below. If the error persists, follow "
"%3$sthis guide%4$s."
msgstr ""
"Essayez de cliquer sur %1$sValider la license%2$s ci-dessous. Si l’erreur "
"persiste, suivez les instructions de %3$sce guide%4$s."

#. Translators: %1$s = opening link tag, %2$s = closing link tag.
#: inc/functions/options.php:507
msgid ""
"License validation failed. You may be using a nulled version of the plugin. "
"Please do the following:"
msgstr ""
"La validation de la licence a échoué. Vous utilisez peut-être une version "
"cackée du plugin. Veuillez faire la chose suivante :"

#. Translators: %1$s = opening link tag, %2$s = closing link tag.
#: inc/functions/options.php:507 inc/functions/options.php:549
msgid "Login to your WP Rocket %1$saccount%2$s"
msgstr "Connectez-vous à votre %1$scompte%2$s WP Rocket"

#. Translators: %1$s = opening link tag, %2$s = closing link tag.
#: inc/functions/options.php:507 inc/functions/options.php:549
msgid "Download the zip file"
msgstr "Télécharger le fichier zip."

#. Translators: %1$s = opening link tag, %2$s = closing link tag.
#: inc/functions/options.php:507 inc/functions/options.php:549
msgid "Reinstall"
msgstr "Réinstaller"

#. Translators: %1$s = opening link tag, %2$s = closing link tag.
#: inc/functions/options.php:507
msgid ""
"If you do not have a WP Rocket account, please %1$spurchase a license%2$s."
msgstr ""
"Si vous ne possédez pas de compte WP Rocket, veuillez %1$sacheter une "
"licence%2$s."

#. Translators: %1$s = opening link tag, %2$s = closing link tag.
#: inc/functions/options.php:515
msgid ""
"License validation failed. This user account does not exist in our database."
msgstr ""
"La validation de la licence a échoué. Ce compte utilisateur n'existe pas "
"dans notre base de données."

#. Translators: %1$s = opening link tag, %2$s = closing link tag.
#: inc/functions/options.php:515
msgid "To resolve, please contact support."
msgstr "Pour résoudre ce problème, veuillez contacter le support."

#. Translators: %1$s = opening link tag, %2$s = closing link tag.
#: inc/functions/options.php:523
msgid "License validation failed. This user account is blocked."
msgstr ""
"La validation de la licence a échoué. Ce compte utilisateur est bloqué."

#. Translators: %1$s = opening link tag, %2$s = closing link tag.
#: inc/functions/options.php:523
msgid "Please see %1$sthis guide%2$s for more info."
msgstr "Veuillez consulter %1$sce guide%2$s pour plus d’informations."

#. Translators: %1$s = opening em tag, %2$s = closing em tag, %3$s = opening
#. link tag, %4$s closing link tag.
#: inc/functions/options.php:530
msgid ""
"Try clicking %1$sSave Changes%2$s below. If the error persists, follow "
"%3$sthis guide%4$s."
msgstr ""
"Essayez de cliquer sur %1$sEnregistrer les modifications%2$s ci-dessous. Si "
"l’erreur persiste, suivez les instructions de %3$sce guide%4$s."

#. Translators: %1$s = opening link tag, %2$s = closing link tag.
#: inc/functions/options.php:543
msgid "Your license is not valid."
msgstr "Votre licence n’est pas correcte."

#. Translators: %1$s = opening link tag, %2$s = closing link tag.
#: inc/functions/options.php:543
msgid "Make sure you have an active %1$sWP Rocket license%2$s."
msgstr "Assurez-vous que vous avez une %1$slicence WP Rocket%2$s active."

#. Translators: %1$s = opening link tag, %2$s = closing link tag, %3$s =
#. opening link tag.
#: inc/functions/options.php:545
msgid "You have added as many sites as your current license allows."
msgstr ""
"Vous avez ajouté autant de sites que votre licence actuelle vous le permet."

#. Translators: %1$s = opening link tag, %2$s = closing link tag, %3$s =
#. opening link tag.
#: inc/functions/options.php:545
msgid ""
"Upgrade your %1$saccount%2$s or %3$stransfer your license%2$s to this "
"domain."
msgstr ""
"Mettez à niveau votre %1$scompte%2$s ou %3$stransfèrez votre licence%2$s "
"vers ce domaine."

#. Translators: %1$s = opening link tag, %2$s = closing link tag.
#: inc/functions/options.php:547
msgid "This website is not allowed."
msgstr "Ce site n'est pas autorisé."

#. Translators: %1$s = opening link tag, %2$s = closing link tag.
#: inc/functions/options.php:547
msgid "Please %1$scontact support%2$s."
msgstr "Veuillez %1$scontacter le support%2$s."

#. Translators: %1$s = opening link tag, %2$s = closing link tag.
#: inc/functions/options.php:549
msgid "This license key is not recognized."
msgstr "Cette clé de licence n'est pas reconnue."

#. Translators: %1$s = opening link tag, %2$s = closing link tag.
#: inc/functions/options.php:549
msgid "If the issue persists, please %1$scontact support%2$s."
msgstr "Si le problème persiste, veuillez %1$scontacter le support%2$s."

#. Translators: %s = error message returned.
#: inc/functions/options.php:555
msgid "License validation failed: %s"
msgstr "La validation de la licence a échoué : %s"

#: inc/Logger/Logger.php:227 inc/Logger/Logger.php:257
msgid "The log file does not exist."
msgstr "Le fichier de log n'existe pas."

#: inc/Logger/Logger.php:233 inc/Logger/Logger.php:263
msgid "The log file could not be read."
msgstr "Le fichier de log n'a pu être lu."

#: inc/Logger/Logger.php:250
msgid "The logs are not saved into a file."
msgstr "Les logs ne sont pas enregistrés dans un fichier."

#. Translators: %s = Hosting name.
#: inc/ThirdParty/Hostings/Cloudways.php:82
msgid ""
"Varnish auto-purge will be automatically enabled once Varnish is enabled on "
"your %s server."
msgstr ""
"L'auto-purge du Varnish sera automatiquement activée dès que Varnish sera "
"activé sur votre serveur %s."

#. translators: %1$s = opening link tag, %2$s = closing link tag.
#: inc/ThirdParty/Hostings/Kinsta.php:159
msgid ""
"Your installation seems to be missing core Kinsta files managing Cache "
"clearing, which will prevent your Kinsta installation and WP Rocket from "
"working correctly. Please get in touch with Kinsta support through your "
"%1$sMyKinsta%2$s account to resolve this issue."
msgstr ""
"Il semble qu'il manque à votre installation des fichiers de base de Kinsta "
"gérant la suppression du cache, ce qui empêchera votre installation Kinsta "
"et WP Rocket de fonctionner correctement. Veuillez contacter l'assistance "
"Kinsta par le biais de votre compte %1$sMyKinsta%2$s pour résoudre ce "
"problème."

#. translators: %1$s = plugin name.
#: inc/ThirdParty/Plugins/CDN/Cloudflare.php:127
msgid ""
"%1$s: Cloudflare's HTTP/2 Server Push is incompatible with the features of "
"Remove Unused CSS and Combine CSS files. We strongly recommend disabling it."
msgstr ""
"%1$s: Le Server Push HTTP/2 de Cloudflare est incompatible avec les options "
"Supprimer les ressources CSS inutilisées et Combiner les fichier CSS. Nous "
"vous recommandons vivement de la désactiver."

#: inc/ThirdParty/Plugins/CDN/Cloudflare.php:170
msgid ""
"Your site is using the official Cloudflare plugin. We have enabled "
"Cloudflare auto-purge for compatibility. If you have APO activated, it is "
"also compatible."
msgstr ""
"Votre site utilise le plugin officiel Cloudflare. Nous avons activé la purge"
" automatique de Cloudflare pour des raisons de compatibilité. Si vous avez "
"activé APO, c'est également compatible."

#: inc/ThirdParty/Plugins/CDN/Cloudflare.php:171
msgid ""
"Cloudflare cache will be purged each time WP Rocket clears its cache to "
"ensure content is always up-to-date."
msgstr ""
"Le cache Varnish sera purgé à chaque fois que WP Rocket purgera son cache "
"pour que le contenu soit toujours à jour."

#. Translators: %1$s = strong opening tag, %2$s = strong closing tag.
#: inc/ThirdParty/Plugins/CDN/Cloudflare.php:215
msgid ""
"%1$sWP Rocket:%2$s You are using \"Dynamic Cookies Cache\". Cloudflare APO "
"is not yet compatible with that feature."
msgstr ""
"%1$sWP Rocket :%2$s Vous utilisez \" le cache des cookies dynamiques\". "
"Cloudflare APO n'est pas encore compatible avec cette fonctionnalité."

#. Translators:%1$s = opening <a> tag, %2$s = closing </a> tag.
#: inc/ThirdParty/Plugins/CDN/Cloudflare.php:221
msgid ""
"You should either disable Cloudflare APO or check with the theme/plugin "
"requiring the use of “Dynamic Cookies Cache” developers for an alternative "
"way to be page-cache friendly. %1$sMore info%2$s"
msgstr ""
"Vous devez soit désactiver Cloudflare APO, soit vérifier auprès des "
"développeurs du thème/plugin nécessitant l'utilisation de \"cache des "
"cookies dynamiques\" qu'il existe une autre solution pour respecter la mise "
"en cache des pages. %1$sPlus d'infos%2$s"

#. Translators: %1$s = strong opening tag, %2$s = strong closing tag, %3$s =
#. opening <a> tag, %4$s = closing </a> tag, %5$s = opening <a> tag.
#: inc/ThirdParty/Plugins/CDN/Cloudflare.php:272
msgid ""
"%1$sWP Rocket:%2$s You are using \"Separate cache files for mobile "
"devices\". You need to activate \"Cache by Device Type\" %3$ssetting%5$s on "
"Cloudflare APO to serve the right version of the cache. %4$sMore info%5$s"
msgstr ""
"%1$sWP Rocket :%2$s Vous utilisez l'option \"Créer un fichier de cache à "
"part pour les mobiles\". Vous devez %3$sl'option%5$s \"Cache by Device "
"Type\" sur Cloudflare APO pour servir la bonne version du cache. %4$sPlus "
"d'infos%5$s"

#. Translators: %1$s = strong opening tag, %2$s = strong closing tag.
#: inc/ThirdParty/Plugins/CDN/Cloudflare.php:293
msgid ""
"%1$sWP Rocket:%2$s You have \"Cache by Device Type\" enabled on Cloudflare "
"APO. If you judge it necessary for the website to have a different cache on "
"mobile and desktop, we suggest you enable our “Separate Cache Files for "
"Mobiles Devices” to ensure the generated cache is accurate."
msgstr ""
"%1$sWP Rocket :%2$s Vous avez activé la fonction \"Cache by Device Type\" "
"sur Cloudflare APO. Si vous jugez nécessaire que le site web ait un cache "
"différent pour les mobiles et les ordinateurs, nous vous suggérons d'activer"
" notre option \"Créer un fichier de cache à part pour les mobiles\" afin de "
"garantir la bonne version du cache."

#. translators: %1$s is WP Rocket plugin name, %2$s is opening <a> tag, %3$s
#. is closing </a> tag.
#: inc/ThirdParty/Plugins/ModPagespeed.php:102
msgid ""
"<strong>%1$s</strong>: Mod PageSpeed is not compatible with this plugin and "
"may cause unexpected results. %2$sMore Info%3$s"
msgstr ""
"<strong>%1$s</strong>: Mod PageSpeed n'est pas compatible avec ce plugin et "
"peut entraîner des résultats inattendus. %2$sPlus d'infos%3$s"

#. Translators: %1$s is an opening <strong> tag; %2$s is a closing </strong>
#. tag
#: inc/ThirdParty/Plugins/Optimization/Autoptimize.php:75
msgid ""
"%1$sWP Rocket: %2$sWe have detected that Autoptimize's JavaScript "
"Aggregation feature is enabled. WP Rocket's Delay JavaScript Execution will "
"not be applied to the file it creates. We suggest disabling %1$sJavaScript "
"Aggregation%2$s to take full advantage of Delay JavaScript Execution."
msgstr ""
"%1$sWP Rocket : %2$sNous avons détecté que l'option Concaténer les fichiers "
"JS d'Autoptimize est activée. L'option Reporter l'exécution JavaScript de WP"
" Rocket ne sera pas appliquée au fichier qu'elle crée. Nous vous suggérons "
"de désactiver %1$sConcaténer les fichiers JS%2$s  pour profiter pleinement "
"de Reporter l'exécution JavaScript."

#. Translators: %1$s is an opening <strong> tag; %2$s is a closing </strong>
#. tag
#: inc/ThirdParty/Plugins/Optimization/Autoptimize.php:130
msgid ""
"%1$sWP Rocket: %2$sWe have detected that Autoptimize's Aggregate Inline CSS "
"feature is enabled. WP Rocket's Load CSS Asynchronously will not work "
"correctly. We suggest disabling %1$sAggregate Inline CSS%2$s to take full "
"advantage of Load CSS Asynchronously Execution."
msgstr ""
"%1$sWP Rocket : %2$sNous avons détecté que l'option Concaténer le CSS inline"
" d'Autoptimize est activée. L'option Chargement Asychrone du CSS de WP "
"Rocket ne sera pas appliquée au fichier qu'elle crée. Nous vous suggérons de"
" désactiver %1$sConcaténer le CSS inline%2$s  pour profiter pleinement du "
"Chargement Asychrone du CSS."

#. translators: %1$s = opening <a> tag, %2$s = closing </a> tag.
#: inc/ThirdParty/Plugins/Optimization/Ezoic.php:45
msgid ""
"This plugin blocks WP Rocket's caching and optimizations. Deactivate it and "
"use %1$sEzoic's nameserver integration%2$s instead."
msgstr ""
"Ce plugin empêche la mise en cache et les optimisations de WP Rocket. "
"Désactivez-le et utilisez %1$sl'intégration du serveur de noms d'Ezoic%2$s à"
" la place."

#. Translators: %s = Plugin name.
#: inc/ThirdParty/Plugins/Optimization/Hummingbird.php:78
msgctxt "Hummingbird notice"
msgid ""
"Please deactivate the following %s option which conflicts with WP Rocket "
"features:"
msgid_plural ""
"Please deactivate the following %s options which conflict with WP Rocket "
"features:"
msgstr[0] ""
"Veuillez désactiver %s option qui entre en conflit avec les fonctions de WP "
"Rocket :"
msgstr[1] ""
"Veuillez désactiver les %s options suivantes qui entrent en conflit avec les"
" fonctions de WP Rocket :"
msgstr[2] ""
"Veuillez désactiver les %s options suivantes qui entrent en conflit avec les"
" fonctions de WP Rocket :"

#. Translators: %1$s = Plugin name, %2$s = <em>, %3$s = </em>.
#: inc/ThirdParty/Plugins/Optimization/Hummingbird.php:151
msgctxt "Hummingbird notice"
msgid ""
"%1$s %2$sdisable emoji%3$s conflicts with WP Rockets %2$sdisable emoji%3$s"
msgstr ""
"%2$sDésactiver les emojis%3$s de %1$s est en conflit avec %2$sDésactiver les"
" emojis%3$s de WP Rocket"

#. Translators: %1$s = Plugin name, %2$s = <em>, %3$s = </em>.
#: inc/ThirdParty/Plugins/Optimization/Hummingbird.php:187
msgctxt "Hummingbird notice"
msgid ""
"%1$s %2$sGZIP compression%3$s conflicts with WP Rocket %2$sGZIP "
"compression%3$s"
msgstr ""
"%2$sLa compression Gzip%3$s de %1$s est en conflit avec %2$sla compression "
"Gzip%3$s de WP Rocket"

#. Translators: %1$s = Plugin name, %2$s = <em>, %3$s = </em>.
#: inc/ThirdParty/Plugins/Optimization/Hummingbird.php:223
msgctxt "Hummingbird notice"
msgid ""
"%1$s %2$sbrowser caching%3$s conflicts with WP Rocket %2$sbrowser "
"caching%3$s"
msgstr ""
"%2$sLe cache navigateur %3$s de %1$s est en conflit avec %2$sle cache "
"navigateur %3$s de WP Rocket"

#. Translators: %1$s = Plugin name, %2$s = <em>, %3$s = </em>.
#: inc/ThirdParty/Plugins/Optimization/Hummingbird.php:255
msgctxt "Hummingbird notice"
msgid ""
"%1$s %2$spage caching%3$s conflicts with WP Rocket %2$spage caching%3$s"
msgstr ""
"%2$sLa mise en cache de page %3$s de %1$s est en conflit avec %2$sla mise en"
" cache de page %3$s de WP Rocket"

#. Translators: %1$s = Plugin name, %2$s = <em>, %3$s = </em>.
#: inc/ThirdParty/Plugins/Optimization/Hummingbird.php:289
msgctxt "Hummingbird notice"
msgid ""
"%1$s %2$sasset optimization%3$s conflicts with WP Rocket %2$sfile "
"optimization%3$s"
msgstr ""
"%2$sL'optimisation des fichiers %3$s de %1$s est en conflit avec "
"%2$sl'optimisation des fichiers%3$s de WP Rocket"

#: inc/ThirdParty/Plugins/Optimization/Perfmatters.php:38
msgid ""
"Remove Unused CSS is currently activated in Perfmatters. If you want to use "
"WP Rocket's Remove Unused CSS feature, disable this option in Perfmatters."
msgstr ""
"La Suppression du CSS inutilisé est actuellement activée dans Perfmatters. "
"Si vous souhaitez utiliser l'option Suppression du CSS inutilisé de WP "
"Rocket, désactivez cette option dans Perfmatters."

#: inc/ThirdParty/Plugins/Optimization/RapidLoad.php:39
msgid ""
"Automated unused CSS removal is currently activated in RapidLoad Power-Up "
"for Autoptimize. If you want to use WP Rocket's Remove Unused CSS feature, "
"disable the  RapidLoad Power-Up for Autoptimize plugin."
msgstr ""
"La suppression automatique des CSS inutilisés est actuellement activée dans "
"RapidLoad Power-Up pour Autoptimize. Si vous souhaitez utiliser l'option de "
"suppression des CSS inutilisés de WP Rocket, désactivez le plugin RapidLoad "
"Power-Up pour Autoptimize."

#. translators: %1$s = plugin name.
#: inc/ThirdParty/Plugins/Optimization/WPMeteor.php:42
msgid ""
"Delay JS is currently activated in %1$s. If you want to use WP Rocket’s "
"delay JS, disable %1$s"
msgstr ""
"Le report de l'exécution des JS est activée dans %1$s. Si vous voulez "
"utiliser le Report de l’exécution JavaScript de WP Rocket, désactivez %1$s"

#: inc/ThirdParty/Plugins/Smush.php:108 inc/ThirdParty/Plugins/Smush.php:126
msgid "Smush"
msgstr "Smush"

#: inc/ThirdParty/Themes/Avada.php:107
msgid "Avada"
msgstr "Avada"

#: inc/ThirdParty/Themes/Divi.php:293
msgid ""
"Your Divi template was updated. Clear the Used CSS if the layout, design or "
"CSS styles were changed."
msgstr ""
"Votre modèle Divi a été mis à jour. Purge le CSS utilisé si la mise en page,"
" le design ou les styles CSS ont été changés."

#: views/cpcss/activate-cpcss-mobile.php:13
msgid "Load CSS asynchronously for mobile"
msgstr "Chargement asynchrone du CSS pour mobile"

#: views/cpcss/activate-cpcss-mobile.php:14
msgid ""
"Your website currently uses the same Critical Path CSS for both desktop and "
"mobile."
msgstr ""
"Votre site web utilise actuellement le même Critical Path CSS pour les "
"ordinateurs de bureau et les mobiles."

#: views/cpcss/activate-cpcss-mobile.php:15
msgid "Click the button to enable mobile-specific CPCSS for your site."
msgstr ""
"Cliquez sur le bouton pour activer le CPCSS spécifique aux mobiles pour "
"votre site."

#. translators: %1$s = opening link tag, %2$s = closing link tag.
#: views/cpcss/activate-cpcss-mobile.php:20
#: views/settings/enable-google-fonts.php:19
#: views/settings/mobile-cache.php:20
msgid ""
"This is a one-time action and this button will be removed afterwards. "
"%1$sMore info%2$s"
msgstr ""
"Il s'agit d'une action unique et ce bouton sera supprimé par la suite. "
"%1$sPlus d'info%2$s"

#. translators: %1$s = opening link tag, %2$s = closing link tag.
#: views/cpcss/activate-cpcss-mobile.php:30
msgid ""
"Your site is now using mobile-specific critical path CSS. %1$sMore info%2$s"
msgstr ""
"Votre site utilise un Critical Path CSS spécifique pour mobile. %1$sPlus "
"d'infos%2$s"

#: views/cpcss/activate-cpcss-mobile.php:39
msgid "Generate Mobile Specific CPCSS"
msgstr "Générer un CPCSS spécifique aux mobiles"

#: views/cpcss/metabox/container.php:19
msgid "Critical Path CSS"
msgstr "Critical Path CSS"

#. translators: %1$s = opening link tag, %2$s = closing link tag.
#: views/cpcss/metabox/generate.php:23
msgid "Generate specific Critical Path CSS for this post. %1$sMore info%2$s"
msgstr ""
"Générer un Critical Path CSS spécifique pour ce post. %1$sPlus d'infos%2$s"

#. translators: %1$s = opening link tag, %2$s = closing link tag.
#: views/cpcss/metabox/generate.php:33
msgid "This post uses specific Critical Path CSS. %1$sMore info%2$s"
msgstr "Ce post utilise un Critical Path CSS spécifique. %1$sPlus d'infos%2$s"

#: views/cpcss/metabox/generate.php:56
msgid "Revert back to the default CPCSS"
msgstr "Revenir au CPCSS par défaut"

#: views/deactivation-intent/form.php:21
msgid "Facing an issue?"
msgstr "Vous rencontrez un problème ?"

#: views/deactivation-intent/form.php:24
msgid ""
"It is not always necessary to deactivate WP Rocket when facing any issues. "
"Most of them can be fixed by deactivating only some options."
msgstr ""
"Il n'est pas toujours nécessaire de désactiver WP Rocket lorsque vous "
"rencontrez des problèmes. La plupart d'entre eux peuvent être résolus en "
"désactivant seulement certaines options."

#. translators: %1$s = opening strong tag, %2$s = closing strong tag.
#: views/deactivation-intent/form.php:29
msgid ""
"Our advice? Instead of deactivating WP Rocket, use our %1$sSafe Mode%2$s to "
"quickly disable LazyLoad, File Optimization, and CDN options. Then check to "
"see if your issue is resolved."
msgstr ""
"Notre conseil ? Au lieu de désactiver WP Rocket, utilisez notre "
"%1$sConfiguration sans conflit%2$s pour désactiver rapidement les options "
"LazyLoad, Optimisation des Fichiers et CDN. Vérifiez ensuite si votre "
"problème est résolu."

#: views/deactivation-intent/form.php:35
msgid "Do you want to use our Safe Mode to troubleshoot WP Rocket?"
msgstr ""
"Voulez-vous utiliser notre Configuration sans conflit pour dépanner WP "
"Rocket ?"

#. translators: %1$s = opening strong tag, %2$s = closing strong tag.
#: views/deactivation-intent/form.php:55
msgid "Yes, apply \"%1$sSafe Mode%2$s\""
msgstr "Oui, appliquer la %1$sConfiguration sans conflit%2$s"

#. translators: %1$s = opening strong tag, %2$s = closing strong tag.
#: views/deactivation-intent/form.php:68
msgid ""
"and export WP Rocket settings %1$s(Recommended as current settings will be "
"deleted)%2$s"
msgstr ""
"et exporter les réglages de WP Rocket %1$s(recommandé car les réglages "
"actuels seront supprimés)%2$s"

#: views/deactivation-intent/form.php:76
msgid "No, deactivate and snooze this message for"
msgstr "Non, désactiver et mettre en veilleuse ce message pour"

#: views/deactivation-intent/form.php:78
msgid "1 day"
msgstr "1 jour"

#: views/deactivation-intent/form.php:79
msgid "7 days"
msgstr "7 jours"

#: views/deactivation-intent/form.php:80
msgid "30 days"
msgstr "30 jours"

#: views/deactivation-intent/form.php:81
msgid "Forever"
msgstr "toujours"

#: views/deactivation-intent/form.php:87
msgid "Cancel"
msgstr "Annuler"

#: views/deactivation-intent/form.php:88
msgid "Confirm"
msgstr "Confirmer"

#. translators: %1$s = <strong>, %2$s = plugin version, %3$s = </strong>, %4$s
#. = <a>, %5$s = </a>, %6$s = <a>.
#: views/plugins/update-renewal-expired-notice.php:27
msgid ""
" %1$sWP Rocket %2$s%3$s is available. %4$sLearn more%5$s about the updates "
"and enhancements of this major version. You need an active license to use "
"them on your website, don’t miss out! %6$sRenew Now%5$s"
msgstr ""
" %1$sWP Rocket %2$s%3$s est disponible. %4$sEn savoir plus%5$s sur les mises"
" à jour et les améliorations de cette version majeure. Vous avez besoin "
"d'une licence active pour les utiliser sur votre site, ne manquez pas cette "
"occasion ! %6$sRenouveler maintenant%5$s"

#: views/settings/dynamic-lists-update.php:14
msgid "Update Inclusion and Exclusion Lists"
msgstr "Mise à jour des listes d'inclusion et d'exclusion"

#. translators: %1$s = opening link tag, %2$s = closing link tag.
#: views/settings/dynamic-lists-update.php:19
msgid ""
"Compatibility lists are downloaded automatically every week. Click the "
"button if you want to update them manually. %1$sMore info%2$s"
msgstr ""
"Les listes de compatibilité sont téléchargées automatiquement chaque "
"semaine. Cliquez sur le bouton si vous souhaitez les mettre à jour "
"manuellement. %1$sPlus d'infos%2$s"

#: views/settings/dynamic-lists-update.php:29
msgid "Update lists"
msgstr "Mettre à jour les listes"

#: views/settings/enable-google-fonts.php:13
msgid "Enable Google Font Optimization"
msgstr "Active l’Optimisation des Google Fonts"

#: views/settings/enable-google-fonts.php:14
msgid ""
"Improves font performance and combines multiple font requests to reduce the "
"number of HTTP requests."
msgstr ""
"Améliore les performances des polices et combine plusieurs requêtes de "
"polices pour réduire le nombre de requêtes HTTP."

#. translators: %1$s = opening link tag, %2$s = closing link tag.
#: views/settings/enable-google-fonts.php:29
msgid ""
"Google Fonts Optimization is now enabled for your site. %1$sMore info%2$s"
msgstr ""
"L’Optimization des Google Fonts est désormais activée sur votre site. "
"%1$sPlus d'infos%2$s"

#: views/settings/enable-google-fonts.php:38
msgid "Optimize Google Fonts"
msgstr "Optimiser les Google Fonts"

#: views/settings/fields/cache-lifespan.php:26
msgid "Clear cache after"
msgstr "Vider le cache après"

#: views/settings/fields/cnames.php:58 views/settings/fields/cnames.php:92
#: views/settings/fields/cnames.php:122
msgid "CSS & JavaScript"
msgstr "CSS & JavaScript"

#: views/settings/fields/cnames.php:59 views/settings/fields/cnames.php:93
#: views/settings/fields/cnames.php:123
msgid "JavaScript"
msgstr "Javascript"

#: views/settings/fields/cnames.php:60 views/settings/fields/cnames.php:94
#: views/settings/fields/cnames.php:124
msgid "CSS"
msgstr "CSS"

#: views/settings/fields/import-form.php:29
msgid "Import settings"
msgstr "Importer les réglages"

#: views/settings/fields/one-click-addon.php:43
#: views/settings/fields/rocket-addon.php:43
msgid "Add-on status"
msgstr "État de l'Add-on"

#: views/settings/fields/rocket-addon.php:72
msgid "Modify options"
msgstr "Modifier les options"

#: views/settings/fields/rocket-cdn.php:29
#: views/settings/fields/rocket-cdn.php:41
msgid "CDN CNAME"
msgstr "CNAME CDN"

#. translators: %s is a "Learn more" link.
#: views/settings/fields/rocket-cdn.php:62
msgid "Purges RocketCDN cached resources for your website. %s"
msgstr "Purge les ressources mises en cache de votre RocketCDN. %s"

#. translators: %s is a "Learn more" link.
#: views/settings/fields/rocket-cdn.php:63
#: views/settings/page-sections/cloudflare.php:36
#: views/settings/page-sections/sucuri.php:37
msgid "Learn more"
msgstr "En savoir plus"

#: views/settings/fields/rocket-cdn.php:72
msgid "Clear all RocketCDN cache files"
msgstr "Purge tous les fichiers mis en cache de RocketCDN"

#: views/settings/mobile-cache.php:11
msgid "Mobile Cache"
msgstr "Cache mobile"

#: views/settings/mobile-cache.php:14
msgid "Speed your site for mobile visitors."
msgstr "Accélérez votre site pour vos visiteurs mobile."

#: views/settings/mobile-cache.php:28
msgid "Mobile Cache is now enabled for your site."
msgstr "Le Cache Mobile est maintenant activé pour votre site."

#: views/settings/mobile-cache.php:34
msgid "Enable Mobile Cache"
msgstr "Activer le Cache mobile"

#: views/settings/page-sections/cloudflare.php:27
msgid "Cloudflare Cache"
msgstr "Cache de CloudFlare"

#. translators: %s is a "Learn more" link.
#: views/settings/page-sections/cloudflare.php:35
#: views/settings/page-sections/sucuri.php:36
msgid "Purges cached resources for your website. %s"
msgstr "Purge les ressources mises en cache de votre site web. %s"

#. translators: %s is a "Learn more" link.
#: views/settings/page-sections/cloudflare.php:36
msgid "https://support.cloudflare.com/hc/en-us/articles/200169246"
msgstr "https://support.cloudflare.com/hc/en-us/articles/200169246"

#: views/settings/page-sections/cloudflare.php:45
msgid "Clear all Cloudflare cache files"
msgstr "Vider le cache CloudFlare"

#: views/settings/page-sections/dashboard.php:38
msgid "Congratulations!"
msgstr "Félicitations !"

#: views/settings/page-sections/dashboard.php:40
msgid "WP Rocket is now activated and already working for you."
msgstr "WP Rocket est maintenant activé et travaille déjà pour vous."

#: views/settings/page-sections/dashboard.php:42
msgid "Your website should be loading faster now!"
msgstr "Votre site devrait être déjà plus rapide !"

#: views/settings/page-sections/dashboard.php:44
msgid ""
"To guarantee fast websites, WP Rocket automatically applies 80% of web "
"performance best practices."
msgstr ""
"Pour des sites web plus rapides, WP Rocket applique automatiquement 80 % des"
" bonnes pratiques en matière de performance web."

#: views/settings/page-sections/dashboard.php:44
msgid ""
"We also enable options that provide immediate benefits to your website."
msgstr ""
"Nous activons aussi les options qui profitent immédiatement à votre site."

#: views/settings/page-sections/dashboard.php:45
msgid "Continue to the options to further optimize your site!"
msgstr ""
"Continuez avec les options si vous souhaitez optimiser encore plus votre "
"site !"

#: views/settings/page-sections/dashboard.php:62
msgid "My Account"
msgstr "Mon compte"

#: views/settings/page-sections/dashboard.php:68
msgid "Refresh info"
msgstr "Actualiser"

#: views/settings/page-sections/dashboard.php:86
msgid "with"
msgstr "avec"

#: views/settings/page-sections/dashboard.php:100
msgid "Expiration Date"
msgstr "Date d'expiration"

#: views/settings/page-sections/dashboard.php:110
msgid "View my account"
msgstr "Mon compte"

#: views/settings/page-sections/dashboard.php:137
msgid "Quick Actions"
msgstr "Actions rapides"

#: views/settings/page-sections/dashboard.php:144
msgid "Remove all cached files"
msgstr "Supprimer tous les fichiers du cache"

#: views/settings/page-sections/dashboard.php:164
#: views/settings/page-sections/dashboard.php:170
msgid "Regenerate Critical CSS"
msgstr "Régénérer le Critical CSS"

#: views/settings/page-sections/dashboard.php:196
msgid "Frequently Asked Questions"
msgstr "Foire aux questions"

#: views/settings/page-sections/dashboard.php:210
msgid "Still cannot find a solution?"
msgstr "Toujours pas trouvé de solution ?"

#: views/settings/page-sections/dashboard.php:211
msgid ""
"Submit a ticket and get help from our friendly and knowledgeable Rocketeers."
msgstr ""
"Envoyez un ticket ci-dessous et obtenez l'aide de nos sympathiques "
"Rocketeers."

#: views/settings/page-sections/dashboard.php:219
msgid "Ask support"
msgstr "Demandez au Support"

#: views/settings/page-sections/database.php:25
msgid "Backup your database before you run a cleanup!"
msgstr "Faites une sauvegarde de votre base de données avant tout nettoyage !"

#: views/settings/page-sections/database.php:26
msgid ""
"Once a database optimization has been performed, there is no way to undo it."
msgstr "Toute optimisation faite de la base de donnée est irréversible."

#: views/settings/page-sections/database.php:28
msgid "Save Changes and Optimize"
msgstr "Sauvegarder et optimiser"

#. Translators: %1$s = <strong>, %2$s = </strong>, %3$s = <span
#. class="imagify-name">, %4$s = </span>.
#: views/settings/page-sections/imagify.php:21
msgid ""
"%1$sWP ROCKET%2$s created %3$sIMAGIFY%4$s %1$sfor best-in-class image "
"optimization.%2$s"
msgstr ""
"%1$sWP ROCKET%2$s a créé %3$sIMAGIFY%4$s %1$spour une optimisation d'image "
"de première classe.%2$s"

#: views/settings/page-sections/imagify.php:24
msgid ""
"Compress image to make your website faster, all while maintaining image "
"quality."
msgstr ""
"Compressez vos images pour rendre votre site Web plus rapide, tout en "
"maintenant leurs qualités."

#: views/settings/page-sections/imagify.php:25
msgid "More on Imagify:"
msgstr "Plus sur Imagify :"

#: views/settings/page-sections/imagify.php:27
msgid "Imagify Plugin Page"
msgstr "Page du plugin"

#: views/settings/page-sections/imagify.php:28
msgid "Imagify Website"
msgstr "Site web Imagify"

#: views/settings/page-sections/imagify.php:29
msgid "Review of Image Compression Plugins"
msgstr "Comparatif des plugins de compression d'images"

#: views/settings/page-sections/imagify.php:38
msgid "Install Imagify"
msgstr "Installer Imagify"

#: views/settings/page-sections/license.php:22
msgid "WP Rocket was not able to automatically validate your license."
msgstr ""
"WP Rocket n'a pas été en mesure de valider automatiquement votre licence."

#. translators: %1$s = tutorial URL, %2$s = support URL.
#: views/settings/page-sections/license.php:29
msgid "Follow this %1$s, or contact %2$s to get the engine started."
msgstr "Suivez ce %1$s, ou contactez %2$s pour pourvoir démarrer la machine."

#. translators: %1$s = <a href=", %2$s =  tutorial href,  %3$s =  "
#. target="_blank">,  %4$s =  </a>.
#: views/settings/page-sections/license.php:32
msgid "%1$s%2$s%3$stutorial%4$s"
msgstr "%1$s%2$s%3$stutoriel%4$s"

#: views/settings/page-sections/license.php:34
msgid ""
"https://docs.wp-rocket.me/article/100-resolving-problems-with-license-"
"validation/?utm_source=wp_plugin&utm_medium=wp_rocket"
msgstr ""
"https://fr.docs.wp-rocket.me/article/257-resoudre-les-problemes-de-"
"validation-de-licence/?utm_source=wp_plugin&utm_medium=wp_rocket"

#. translators: %1$s = <a href=", %2$s =  support href,  %3$s =  "
#. target="_blank">,  %4$s =  </a>.
#: views/settings/page-sections/license.php:40
msgid "%1$s%2$s%3$ssupport%4$s"
msgstr "%1$s%2$s%3$ssupport%4$s"

#: views/settings/page-sections/sucuri.php:46
msgid "Clear all Sucuri cache files"
msgstr "Supprimer tous les fichiers du cache Sucuri"

#. translators: %1$s = formatted file size, %2$s = formatted number of entries
#. (don't use %2$d).
#: views/settings/page-sections/tools.php:20
msgid "Files size: %1$s. Number of entries: %2$s."
msgstr "Taille des fichiers : %1$s. Nombre d’entrées : %2$s."

#. translators: %1$s = opening <a> tag, %2$s = closing </a> tag.
#: views/settings/page-sections/tools.php:23
msgid "%1$sDownload the file%2$s."
msgstr "%1$sTélécharger le fichier%2$s."

#. translators: %1$s = opening <a> tag, %2$s = closing </a> tag.
#: views/settings/page-sections/tools.php:26
msgid "%1$sDelete the file%2$s."
msgstr "%1$sSupprimer le fichier%2$s."

#: views/settings/page-sections/tools.php:37
msgid "Export settings"
msgstr "Exporter les réglages"

#: views/settings/page-sections/tools.php:38
msgid "Download a backup file of your settings"
msgstr "Télécharger un fichier de sauvegarde de vos réglages"

#: views/settings/page-sections/tools.php:46
msgid "Download settings"
msgstr "Télécharger les réglages"

#: views/settings/page-sections/tools.php:60
msgid "Rollback"
msgstr "Restauration"

#. translators: %s = WP Rocket version number.
#: views/settings/page-sections/tools.php:64
msgid "Has version %s caused an issue on your website?"
msgstr "La version %s vous pose des problèmes ?"

#. translators: %s = <br>.
#: views/settings/page-sections/tools.php:69
msgid ""
"You can rollback to the previous major version here.%sThen send us a support"
" request."
msgstr ""
"Vous pouvez restaurez la version majeure précédente. %sPuis envoyez-nous une"
" demande de support."

#. translators: %s = WP Rocket previous version.
#: views/settings/page-sections/tools.php:80
msgid "Reinstall version %s"
msgstr "Réinstaller la version %s"

#: views/settings/page-sections/tools.php:106
msgid "Debug mode"
msgstr "Debug mode"

#: views/settings/page-sections/tools.php:111
msgid "Create a debug log file."
msgstr "Créez un fichier log de debug."

#: views/settings/page-sections/tutorials.php:13
#: views/settings/partials/getting-started.php:18
msgid "Getting Started"
msgstr "Prise en main"

#: views/settings/page-sections/tutorials.php:15
msgid "Getting Started with WP Rocket"
msgstr "Premiers pas avec WP Rocket"

#: views/settings/page-sections/tutorials.php:16
#: views/settings/partials/getting-started.php:12
msgid "Finding the Best Settings for Your Site"
msgstr "Trouver les meilleurs réglages pour votre site"

#: views/settings/page-sections/tutorials.php:17
#: views/settings/partials/getting-started.php:13
msgid "How to Check if WP Rocket is Caching Your Site"
msgstr "Comment vérifier si WP Rocket met en cache votre site"

#: views/settings/page-sections/tutorials.php:18
#: views/settings/partials/getting-started.php:14
msgid "How to Measure the Speed of Your Site"
msgstr "Comment mesurer la vitesse de votre site"

#: views/settings/page-sections/tutorials.php:19
msgid "How Preloading Works"
msgstr "Comment fonctionne le préchargement"

#: views/settings/page-sections/tutorials.php:23
msgid "Passing the Core Web vitals"
msgstr "Réussir les Core Web vitals"

#: views/settings/page-sections/tutorials.php:25
msgid "How to improve LCP with WP Rocket"
msgstr "Comment améliorer le LCP avec WP Rocket"

#: views/settings/page-sections/tutorials.php:26
msgid "How to improve FID with WP Rocket"
msgstr "Comment améliorer le FID avec WP Rocket"

#: views/settings/page-sections/tutorials.php:27
msgid "How to improve CLS with WP Rocket"
msgstr "Comment améliorer le CLS avec WP Rocket"

#: views/settings/page-sections/tutorials.php:31
msgid "Troubleshooting"
msgstr "Dépannage"

#: views/settings/page-sections/tutorials.php:33
msgid "Troubleshooting Display Issues with File Optimization"
msgstr ""
"Résolution des problèmes d'affichage liés à l'Optimisation des Fichiers"

#: views/settings/page-sections/tutorials.php:34
msgid "How to Find the Right JavaScript to Exclude"
msgstr "Comment trouver le bon fichier JS à exclure"

#: views/settings/page-sections/tutorials.php:35
msgid "How External Content Slows Your Site"
msgstr "Comment le contenu externe ralentit votre site"

#: views/settings/page-sections/tutorials.php:41
msgid "Set Up the Cloudflare Add-on"
msgstr "Configurer l’add-on Cloudflare"

#: views/settings/page.php:16
msgid "WP Rocket Settings"
msgstr "Réglages de WP Rocket"

#. translators: %s = Plugin version number.
#: views/settings/page.php:30
msgid "version %s"
msgstr "version %s"

#: views/settings/page.php:60
msgid "Show Sidebar"
msgstr "Afficher la barre latérale"

#. translators: %1$s = <strong>, %2$s = </strong>.
#: views/settings/page.php:82
msgid ""
"Below is a detailed view of all data WP Rocket will collect %1$sif granted "
"permission.%2$s"
msgstr ""
"Vous trouverez ci-dessous les données collectées par WP Rocket%1$ssi vous "
"nous en donnez l'autorisation.%2$s"

#: views/settings/page.php:88
msgid ""
"WP Rocket will never transmit any domain names or email addresses (except "
"for license validation), IP addresses, or third-party API keys."
msgstr ""
"WP Rocket ne transmettra jamais de noms de domaine ou d'adresses email (sauf"
" pour la validation de licence), d'adresses IP ou de clés API tierces."

#: views/settings/page.php:90
msgid "Activate Rocket analytics"
msgstr "Activer Rocket Analytics"

#: views/settings/partials/documentation.php:15
msgid "It is a great starting point to fix some of the most common issues."
msgstr ""
"C'est un excellent point de départ pour résoudre certains des problèmes les "
"plus fréquents."

#: views/settings/partials/documentation.php:22
msgid "Read the documentation"
msgstr "Lire la documentation"

#: views/settings/partials/getting-started.php:11
msgid "What WP Rocket Does For You By Default"
msgstr "Ce que WP Rocket fait pour vous par défaut"

#: views/settings/partials/sidebar.php:12
msgid "How to correctly measure your website’s loading time"
msgstr "Comment bien mesurer le temps de chargement de votre site Web"

#: views/settings/partials/sidebar.php:14
msgid "Check our tutorial and learn how to measure the speed of your site."
msgstr "Consultez notre tuto et apprenez à mesurer la vitesse de votre site."

#: views/settings/partials/sidebar.php:15
#: views/settings/partials/sidebar.php:24
msgid "Read our guide"
msgstr "Lire notre guide"

#: views/settings/partials/sidebar.php:18
msgid "Learn about optimal WP Rocket settings for mobile."
msgstr "Découvrez les réglages optimaux de WP Rocket pour les mobiles."

#: views/settings/partials/sidebar.php:27
msgid "Test and Improve Google Core Web Vitals for WordPress."
msgstr "Test et amélioration des Google Core Web Vitals pour WordPress."

#: views/settings/partials/sidebar.php:28
msgid "Read more"
msgstr "En savoir plus"

#: views/settings/partials/sidebar.php:33
msgid "You have not activated logged-in user cache."
msgstr "Vous n'avez pas activé le cache utilisateur."

#: views/settings/partials/sidebar.php:34
msgid ""
"Use a private browser to check your website's speed and visual appearance."
msgstr ""
"Utilisez un navigateur privé pour vérifier la vitesse et l’aspect visuel de "
"votre site Web."

#: views/settings/sections/addons-container.php:24
#: views/settings/sections/fields-container.php:28
msgid "Need Help?"
msgstr "Besoin d'aide ?"
