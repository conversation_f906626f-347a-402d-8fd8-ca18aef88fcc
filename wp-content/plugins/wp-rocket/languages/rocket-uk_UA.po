msgid ""
msgstr ""
"Project-Id-Version: WP Rocket 3.4\n"
"Report-Msgid-Bugs-To: http://wp-rocket.me/\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2020\n"
"Language-Team: Ukrainian (Ukraine) (https://www.transifex.com/wp-media/teams/18133/uk_UA/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"POT-Creation-Date: 2020-01-15 16:02+0200\n"
"PO-Revision-Date: 2019-08-26 15:14+0000\n"
"Language: uk_UA\n"
"Plural-Forms: nplurals=4; plural=(n % 1 == 0 && n % 10 == 1 && n % 100 != 11 ? 0 : n % 1 == 0 && n % 10 >= 2 && n % 10 <= 4 && (n % 100 < 12 || n % 100 > 14) ? 1 : n % 1 == 0 && (n % 10 ==0 || (n % 10 >=5 && n % 10 <=9) || (n % 100 >=11 && n % 100 <=14 )) ? 2: 3);\n"
"X-Generator: Poedit 2.2.4\n"
"X-Poedit-Basepath: ..\n"
"X-Poedit-KeywordsList: __;_e;_x:1,2c;_ex:1,2c;_n:1,2;_nx:1,2,4c;_n_noop:1,2;_nx_noop:1,2,3c;esc_attr__;esc_html__;esc_attr_e;esc_html_e;esc_attr_x:1,2c;esc_html_x:1,2c\n"
"X-Poedit-SearchPath-0: .\n"
"X-Poedit-SearchPathExcluded-0: assets\n"
"X-Poedit-SearchPathExcluded-1: src\n"
"X-Poedit-SearchPathExcluded-2: vendor\n"
"X-Poedit-SearchPathExcluded-3: node_modules\n"
"X-Poedit-SearchPathExcluded-4: tests\n"
"X-Poedit-SourceCharset: UTF-8\n"
"X-Textdomain-Support: yes\n"

#: inc/3rd-party/hosting/flywheel.php:18
#: inc/3rd-party/hosting/pressidium.php:18
#: inc/3rd-party/hosting/wp-serveur.php:27
#: inc/ThirdParty/Hostings/Cloudways.php:90
#: inc/ThirdParty/Hostings/Dreampress.php:44
#: inc/ThirdParty/Hostings/Godaddy.php:63
#: inc/ThirdParty/Hostings/O2Switch.php:49
#: inc/ThirdParty/Hostings/OneCom.php:137
#: inc/ThirdParty/Hostings/ProIsp.php:51
#: inc/ThirdParty/Hostings/Savvii.php:50
#: inc/ThirdParty/Hostings/WPEngine.php:47
#: inc/ThirdParty/Hostings/WPXCloud.php:51
#: inc/deprecated/3.6.php:697
#: inc/deprecated/3.6.php:997
#: inc/deprecated/3.9.php:22
#, php-format
msgid "Your site is hosted on %s, we have enabled Varnish auto-purge for compatibility."
msgstr "Ваш сайт розміщено на %s, ми включили Varnish автоочищення для сумісності."

#: inc/deprecated/3.12.php:332
msgid "Jetpack XML Sitemaps"
msgstr "Jetpack XML Sitemaps"

#: inc/deprecated/3.12.php:334
msgid "Preload the sitemap from the Jetpack plugin"
msgstr "Презавантаження карти сайту з плагіна Jetpack"

#: inc/deprecated/3.11.php:83
#: inc/deprecated/3.12.php:58
#: inc/deprecated/3.12.php:87
#: inc/deprecated/3.12.php:186
#: inc/deprecated/3.12.php:253
#: inc/deprecated/3.12.php:342
#, php-format
msgid "We automatically detected the sitemap generated by the %s plugin. You can check the option to preload it."
msgstr "Ми автоматично визначили карту сайту, створену плагіном %s. Ви можете перевірити можливість попередньої завантаження."

#: inc/deprecated/3.12.php:56
msgid "All in One SEO XML sitemap"
msgstr "All in One SEO XML sitemap"

#: inc/deprecated/3.12.php:85
msgid "Rank Math XML sitemap"
msgstr "Ранг Math XML карти сайту"

#: inc/deprecated/3.12.php:184
msgid "SEOPress XML sitemap"
msgstr "Мапа сайту SEOPress XML"

#: inc/deprecated/3.12.php:251
msgid "The SEO Framework XML sitemap"
msgstr "SEO Framework XML карта сайту"

#: inc/deprecated/3.11.php:81
msgid "Yoast SEO XML sitemap"
msgstr "Yoast SEO XML sitemap"

#: inc/admin/admin.php:18
#: inc/common/admin-bar.php:255
#: inc/deprecated/deprecated.php:1787
msgid "Support"
msgstr "Підтримка"

#: inc/admin/admin.php:20
msgid "Docs"
msgstr "Документи"

#: inc/admin/admin.php:22
#: inc/common/admin-bar.php:243
msgid "FAQ"
msgstr "Часті питання"

#: inc/admin/admin.php:24
#: inc/common/admin-bar.php:63
msgid "Settings"
msgstr "Налаштування"

#: inc/Engine/Cache/AdminSubscriber.php:118
#: inc/admin/admin.php:96
#: inc/admin/admin.php:118
#: inc/deprecated/3.5.php:898
msgid "Clear this cache"
msgstr "Очистити цей кеш"

#: inc/Engine/Plugin/UpdaterSubscriber.php:472
#: inc/Engine/Plugin/UpdaterSubscriber.php:486
#: inc/classes/class-wp-rocket-requirements-check.php:203
#: inc/classes/class-wp-rocket-requirements-check.php:214
#: inc/deprecated/3.11.php:236
#: inc/deprecated/3.11.php:251
#, php-format
msgid "%s Update Rollback"
msgstr "%s Відкат оновлення"

#: inc/Engine/Plugin/UpdaterSubscriber.php:509
#: inc/deprecated/3.11.php:279
#, php-format
msgid "%1$sReturn to WP Rocket%2$s or %3$sgo to Plugins page%2$s"
msgstr "%1$sПовернутися до WP Rocket%2$s або %3$sперейти на сторінку плагінів%2$s"

#: inc/admin/admin.php:458
msgid "Settings import failed: you do not have the permissions to do this."
msgstr "Помилка імпорту параметрів: у вас немає прав для цього."

#: inc/admin/admin.php:462
msgid "Settings import failed: no file uploaded."
msgstr "Помилка імпорту параметрів: файл не завантажено."

#: inc/admin/admin.php:466
msgid "Settings import failed: incorrect filename."
msgstr "Помилка імпорту параметрів: неправильне ім'я файлу."

#: inc/admin/admin.php:477
msgid "Settings import failed: incorrect filetype."
msgstr "Помилка імпорту параметрів: неправильний тип файлу."

#: inc/admin/admin.php:487
msgid "Settings import failed: "
msgstr "Помилка імпорту налаштувань: "

#: inc/admin/admin.php:503
msgid "Settings import failed: unexpected file content."
msgstr "Помилка імпорту параметрів: несподіваний вміст файлу."

#: inc/admin/admin.php:533
msgid "Settings imported and saved."
msgstr "Параметри імпортовані та збережені."

#: inc/Addon/Cloudflare/Subscriber.php:632
#: inc/admin/options.php:184
#: inc/classes/subscriber/Tools/class-detect-missing-tags-subscriber.php:148
msgid "WP Rocket: "
msgstr "WP Rocket:"

#: inc/Addon/Cloudflare/Subscriber.php:297
#, php-format
msgid "Cloudflare development mode error: %s"
msgstr "Помилка режиму розробки Cloudflare: %s"

#: inc/Addon/Cloudflare/Subscriber.php:304
#, php-format
msgid "Cloudflare development mode %s"
msgstr "Режим розробки Cloudflare %s"

#: inc/Addon/Cloudflare/Subscriber.php:321
#, php-format
msgid "Cloudflare cache level error: %s"
msgstr "Помилка рівня кешу Cloudflare: %s"

#: inc/Addon/Cloudflare/Subscriber.php:334
#, php-format
msgid "Cloudflare cache level set to %s"
msgstr "Рівень кешу Cloudflare встановлено на %s"

#: inc/Addon/Cloudflare/Subscriber.php:350
#, php-format
msgid "Cloudflare minification error: %s"
msgstr "Помилка мініфікації Cloudflare: %s"

#: inc/Addon/Cloudflare/Subscriber.php:357
#, php-format
msgid "Cloudflare minification %s"
msgstr "Мініфікація Cloudflare %s"

#: inc/Addon/Cloudflare/Subscriber.php:373
#, php-format
msgid "Cloudflare rocket loader error: %s"
msgstr "Помилка ракетного завантажувача Cloudflare: %s"

#: inc/Addon/Cloudflare/Subscriber.php:380
#, php-format
msgid "Cloudflare rocket loader %s"
msgstr "Cloudflare ракетний завантажувач %s"

#: inc/Addon/Cloudflare/Subscriber.php:396
#, php-format
msgid "Cloudflare browser cache error: %s"
msgstr "Помилка кешу браузера Cloudflare: %s"

#: inc/Engine/Admin/Settings/Page.php:708
#: inc/admin/options.php:124
msgid "Excluded CSS Files"
msgstr "Виключені CSS файли"

#: inc/Engine/Admin/Settings/Page.php:862
#: inc/admin/options.php:125
msgid "Excluded Inline JavaScript"
msgstr "Виключені Inline JavaScript"

#: inc/Engine/Admin/Settings/Page.php:880
#: inc/Engine/Admin/Settings/Page.php:913
#: inc/Engine/Admin/Settings/Page.php:960
#: inc/admin/options.php:126
msgid "Excluded JavaScript Files"
msgstr "Виключені JavaScript файли"

#: inc/Engine/Admin/Settings/Page.php:1329
#: inc/admin/options.php:129
msgid "Never Cache URL(s)"
msgstr "Ніколи не кешувати URL(и)"

#: inc/Engine/Admin/Settings/Page.php:1343
#: inc/admin/options.php:130
msgid "Never Cache User Agent(s)"
msgstr "Некешуємі User-Agent(и)"

#: inc/Engine/Admin/Settings/Page.php:1349
#: inc/admin/options.php:131
msgid "Always Purge URL(s)"
msgstr "Always Purge URL(и)"

#: inc/Engine/Admin/Settings/Page.php:1616
#: inc/admin/options.php:132
msgid "Exclude files from CDN"
msgstr "Виключити файли з CDN"

#: inc/admin/options.php:160
msgid "The following pattern is invalid and has been removed:"
msgid_plural "The following patterns are invalid and have been removed:"
msgstr[0] "Цей шаблон не працює, його було видалено:"
msgstr[1] "Ці шаблони не працюють, їх було видалено:"
msgstr[2] "Ці шаблони не працюють, їх було видалено:"
msgstr[3] "Ці шаблони не працюють, їх було видалено:"

#: inc/admin/ui/meta-boxes.php:37
#: inc/admin/ui/notices.php:748
#: inc/common/admin-bar.php:84
#: inc/common/admin-bar.php:136
#: views/settings/page-sections/dashboard.php:150
msgid "Clear cache"
msgstr "Очистити кеш"

#: inc/admin/ui/meta-boxes.php:67
msgid "WP Rocket Options"
msgstr "WP Rocket опції"

#: inc/admin/ui/meta-boxes.php:96
msgid "Never cache this page"
msgstr "Ніколи не кешуйте цю сторінку"

#: inc/admin/ui/meta-boxes.php:100
msgid "Activate these options on this post:"
msgstr "Активувати ці параметри в цьому записі:"

#: inc/admin/ui/meta-boxes.php:103
msgid "LazyLoad for images"
msgstr "LazyLoad для зображень"

#: inc/admin/ui/meta-boxes.php:104
msgid "LazyLoad for iframes/videos"
msgstr "LazyLoad для iframes/відео"

#: inc/admin/ui/meta-boxes.php:105
msgid "Minify/combine CSS"
msgstr "Зменшити/об'єднати CSS"

#: inc/admin/ui/meta-boxes.php:107
msgid "Minify/combine JS"
msgstr "Зменшити/об'єднати JS"

#: inc/Engine/Admin/Settings/Page.php:1586
#: inc/Engine/Admin/Settings/Page.php:1597
#: inc/admin/ui/meta-boxes.php:108
#: inc/deprecated/deprecated.php:1773
msgid "CDN"
msgstr "CDN"

#: inc/admin/ui/meta-boxes.php:110
msgid "Defer JS"
msgstr "Defer JS"

#: inc/admin/ui/meta-boxes.php:117
#, php-format
msgid "Activate first the %s option."
msgstr "Активуйте спочатку %s параметр."

#: inc/admin/ui/meta-boxes.php:133
#, php-format
msgid "%1$sNote:%2$s None of these options will be applied if this post has been excluded from cache in the global cache settings."
msgstr "%1$sNote:%2$s Жоден з цих параметрів не буде застосовано, якщо цю публікацію виключено з кешу в налаштуваннях глобального кешу."

#: inc/admin/ui/notices.php:31
#: inc/admin/ui/notices.php:44
#, php-format
msgid ""
"<strong>%1$s</strong> has not been deactivated due to missing writing permissions.<br>\n"
"Make <strong>%2$s</strong> writeable and retry deactivation, or force deactivation now:"
msgstr ""
"<strong>%1$s</strong> не може бути відключено через помилку прав запису.<br>\n"
"Дозвольте запис в <strong>%2$s</strong> і повторіть спробу, або відключіть насильно:"

#: inc/admin/ui/notices.php:97
#, php-format
msgid "<strong>%s</strong>: One or more plugins have been enabled or disabled, clear the cache if they affect the front end of your site."
msgstr "<strong>%s</strong>: Один або кілька плагінів були відключені або видалені. Очистіть кеш, щоб стерти їх залишки."

#: inc/admin/ui/notices.php:218
#, php-format
msgid "<strong>%s</strong>: The following plugins are not compatible with this plugin and may cause unexpected results:"
msgstr "<strong>%s</strong>: Наступні плагіни несумісні з цим плагіном і можуть призвести до несподіваних результатів:"

#: inc/admin/ui/notices.php:224
msgid "Deactivate"
msgstr "Вимкнути"

#: inc/admin/ui/notices.php:266
msgid "WP Rocket Footer JS is not an official add-on. It prevents some options in WP Rocket from working correctly. Please deactivate it if you have problems."
msgstr "WP Rocket Footer JS не є офіційним доповненням. Це запобігає правильній роботі деяких опцій WP Rocket. Вимкніть його, якщо у вас виникли проблеми."

#: inc/admin/ui/notices.php:306
#, php-format
msgid "Endurance Cache is currently enabled, which will conflict with WP Rocket Cache. Please set the Endurance Cache cache level to Off (Level 0) on the %1$sSettings > General%2$s page to prevent any issues."
msgstr "Зараз активовано Endurance Cache, це викличе конфлікти з WP Rocket Cache. Щоб цьому запобігти, будь ласка, переключіть Endurance Cache на Off (Level 0) на сторінці %1$sSettings > General%2$s. "

#: inc/admin/ui/notices.php:327
#, php-format
msgid "%1$s: A custom permalink structure is required for the plugin to work properly. %2$sGo to permalinks settings%3$s"
msgstr "%1$s: Для нормальної роботи плагіна потрібна власна структура постійних посилань. %2$sПерейдіть до налаштувань permalinks%3$s"

#: inc/admin/ui/notices.php:374
#, php-format
msgid "%s could not modify the .htaccess file due to missing writing permissions."
msgstr "%s не вдалося внести зміни до файлу .htaccess через відсутність прав запису."

#: inc/admin/ui/notices.php:380
#: inc/admin/ui/notices.php:843
#, php-format
msgid "Troubleshoot: %1$sHow to make system files writeable%2$s"
msgstr "Виправлення неполадок:%1$sЯк зробити системні файли для запису%2$s"

#: inc/admin/ui/notices.php:382
#: inc/admin/ui/notices.php:845
msgid "https://docs.wp-rocket.me/article/626-how-to-make-system-files-htaccess-wp-config-writeable/?utm_source=wp_plugin&utm_medium=wp_rocket"
msgstr "https://docs.wp-rocket.me/article/626-how-to-make-system-files-htaccess-wp-config-writeable/?utm_source=wp_plugin&utm_medium=wp_rocket"

#: inc/admin/ui/notices.php:388
msgid "Don’t worry, WP Rocket’s page caching and settings will still function correctly."
msgstr "Не хвилюйтеся, кешування сторінок та налаштування WP Rocket будуть і надалі працювати коректно."

#: inc/admin/ui/notices.php:388
msgid "For optimal performance, adding the following lines into your .htaccess is recommended (not required):"
msgstr "Для покращення роботи, ми рекомендуємо (це не вимагається) додати наступні рядки до .htaccess:"

#: inc/admin/ui/notices.php:535
#, php-format
msgid "%1$s is good to go! %2$sTest your load time%4$s, or visit your %3$ssettings%4$s."
msgstr "%1$s працює відмінно! %2$sПеревірте швидкість завантаження%4$s, або перейдіть в %3$sнастройки%4$s."

#: inc/admin/ui/notices.php:576
msgid "Would you allow WP Rocket to collect non-sensitive diagnostic data from this website?"
msgstr "Чи дозволили б WP Rocket збирати нечутливі діагностичні дані з цього сайту?"

#: inc/admin/ui/notices.php:577
msgid "This would help us to improve WP Rocket for you in the future."
msgstr "Це допоможе нам у майбутньому поліпшити роботу WP Rocket."

#: inc/admin/ui/notices.php:583
msgid "What info will we collect?"
msgstr "Яку інформацію ми збираємо?"

#: inc/admin/ui/notices.php:588
msgid "Below is a detailed view of all data WP Rocket will collect if granted permission. WP Rocket will never transmit any domain names or email addresses (except for license validation), IP addresses, or third-party API keys."
msgstr "Нижче наведено детальний перегляд всіх даних, які WP Rocket збиратиме, якщо надано дозвіл. WP Rocket ніколи не передаватиме будь-які доменні імена або адреси електронної пошти (крім перевірки ліцензії), IP-адреси або сторонні ключі API."

#: inc/admin/ui/notices.php:597
msgid "Yes, allow"
msgstr "Так, дозволити"

#: inc/admin/ui/notices.php:600
msgid "No, thanks"
msgstr "Ні, дякую"

#: inc/admin/ui/notices.php:639
msgid "Thank you!"
msgstr "Дякую!"

#: inc/admin/ui/notices.php:644
msgid "WP Rocket now collects these metrics from your website:"
msgstr "Зараз WP Rocket збирає такі дані з вашого веб-сайту:"

#: inc/admin/ui/notices.php:682
#, php-format
msgid "%s: Cache cleared."
msgstr "%s: кеш очищено."

#: inc/admin/ui/notices.php:689
#, php-format
msgid "%s: Post cache cleared."
msgstr "%s: кеш записів очищено."

#: inc/admin/ui/notices.php:696
#, php-format
msgid "%s: Term cache cleared."
msgstr "%s: Term кеш очищено."

#: inc/admin/ui/notices.php:703
#, php-format
msgid "%s: User cache cleared."
msgstr "%s: кеш користувачів очищено."

#: inc/Engine/HealthCheck/HealthCheck.php:82
#: inc/deprecated/3.5.php:858
msgid "The following scheduled event failed to run. This may indicate the CRON system is not running properly, which can prevent some WP Rocket features from working as intended:"
msgid_plural "The following scheduled events failed to run. This may indicate the CRON system is not running properly, which can prevent some WP Rocket features from working as intended:"
msgstr[0] "Нижчезазначене заплановане завдання не було виконано. Причиною можуть бути проблеми в системі СRON, які блокують роботу деяких функцій WP Rocket:"
msgstr[1] "Нижчезазначені заплановані завдання не було виконано. Причиною можуть бути проблеми в системі СRON, які блокують роботу деяких функцій WP Rocket:"
msgstr[2] "Нижчезазначені заплановані завдання не було виконано. Причиною можуть бути проблеми в системі СRON, які блокують роботу деяких функцій WP Rocket:"
msgstr[3] "Нижчезазначені заплановані завдання не було виконано. Причиною можуть бути проблеми в системі СRON, які блокують роботу деяких функцій WP Rocket:"

#: inc/Engine/HealthCheck/HealthCheck.php:88
#: inc/deprecated/3.5.php:867
msgid "Please contact your host to check if CRON is working."
msgstr "Будь ласка, зв'яжіться з вашим хостером, щоб перевірити, чи працює CRON."

#: inc/admin/ui/notices.php:751
msgid "Stop Preload"
msgstr "Зупинити попереднє завантаження"

#: inc/admin/ui/notices.php:781
msgid "Force deactivation "
msgstr "Примусова деактивація "

#: inc/admin/ui/notices.php:800
msgid "The following code should have been written to this file:"
msgstr "Наступний код повинен був бути написаний у цьому файлі:"

#: inc/admin/ui/notices.php:831
#, php-format
msgid "%s cannot configure itself due to missing writing permissions."
msgstr "%sне можна налаштувати автоматично через відсутність дозволів на запис."

#: inc/admin/ui/notices.php:837
#, php-format
msgid "Affected file/folder: %s"
msgstr "Проблемний файл/папка: %s"

#: inc/Engine/Admin/Database/Optimization.php:30
#: inc/Engine/Admin/Settings/Page.php:1475
msgid "Revisions"
msgstr "Редакції"

#: inc/Engine/Admin/Database/Optimization.php:31
#: inc/Engine/Admin/Settings/Page.php:1485
msgid "Auto Drafts"
msgstr "Авто чернетки"

#: inc/Engine/Admin/Database/Optimization.php:32
#: inc/Engine/Admin/Settings/Page.php:1495
msgid "Trashed Posts"
msgstr "Видалені записи"

#: inc/Engine/Admin/Database/Optimization.php:33
#: inc/Engine/Admin/Settings/Page.php:1505
msgid "Spam Comments"
msgstr "Спам коментарі"

#: inc/Engine/Admin/Database/Optimization.php:34
#: inc/Engine/Admin/Settings/Page.php:1515
msgid "Trashed Comments"
msgstr "Видалені коментарі"

#: inc/Engine/Admin/Database/Optimization.php:35
msgid "Transients"
msgstr "Транзакції"

#: inc/Engine/Admin/Database/Optimization.php:36
msgid "Tables"
msgstr "Таблиці"

#: inc/classes/admin/class-logs.php:124
msgid "The debug file could not be deleted."
msgstr "Файл налагодження неможливо видалити."

#: inc/Engine/Admin/Settings/Page.php:201
msgid "Save Changes"
msgstr "Зберегти Зміни"

#: inc/Engine/Admin/Settings/Page.php:201
msgid "Validate License"
msgstr "Перевірити Ліцензію"

#: inc/Engine/Admin/Settings/Page.php:257
#: inc/Engine/Admin/Settings/Page.php:258
msgid "Unavailable"
msgstr "Невідомо"

#: inc/Engine/Admin/Settings/Page.php:352
#: inc/deprecated/deprecated.php:1789
#: views/settings/page-sections/dashboard.php:80
msgid "License"
msgstr "Ліцензія"

#: inc/Engine/Admin/Settings/Page.php:369
msgid "API key"
msgstr "Ключ API"

#: inc/Engine/Admin/Settings/Page.php:384
msgid "Email address"
msgstr "Електронна пошта"

#: inc/Engine/Admin/Settings/Page.php:410
msgid "Dashboard"
msgstr "Майстерня"

#: inc/Engine/Admin/Settings/Page.php:411
msgid "Get help, account info"
msgstr "Довідка, інфо про аккаунт"

#: inc/Engine/Admin/Settings/Page.php:420
msgid "My Status"
msgstr "Мій статус"

#: inc/Engine/Admin/Settings/Page.php:430
#: views/settings/page.php:75
msgid "Rocket Analytics"
msgstr "Rocket аналітика"

#: inc/Engine/Admin/Settings/Page.php:432
#, php-format
msgid "I agree to share anonymous data with the development team to help improve WP Rocket. %1$sWhat info will we collect?%2$s"
msgstr "Я згоден поділитися анонімними даними з командою розробників, для поліпшення WP Rocket. %1$sЯку інформацію ми збираємо?%2$s"

#: inc/Engine/Admin/Settings/Page.php:456
#: inc/Engine/Cache/WPCache.php:354
msgid "Cache"
msgstr "Кеш"

#: inc/Engine/Admin/Settings/Page.php:457
msgid "Basic cache options"
msgstr "Основні опції кешу"

#: inc/Engine/Admin/Settings/Page.php:464
msgid "Mobile Cache"
msgstr "Мобільний кеш"

#: inc/Engine/Admin/Settings/Page.php:466
msgid "Speed up your site for mobile visitors."
msgstr "Прискорити свій сайт для мобільних відвідувачів."

#: inc/Engine/Admin/Settings/Page.php:471
msgid "We detected you use a plugin that requires a separate cache for mobile, and automatically enabled this option for compatibility."
msgstr "Ми виявили, що ви використовуєте плагін, для якого потрібен окремий кеш для мобільних пристроїв, і автоматично включений цей параметр для сумісності."

#: inc/Engine/Admin/Settings/Page.php:475
msgid "User Cache"
msgstr "Кеш користувача"

#: inc/Engine/Admin/Settings/Page.php:478
#, php-format
msgid "%1$sUser cache%2$s is great when you have user-specific or restricted content on your website."
msgstr "%1$sКеш користувачів%2$s корисний, коли зміст сайту для користувачів відрізняється від гостьового."

#: inc/Engine/Admin/Settings/Page.php:486
msgid "Cache Lifespan"
msgstr "Термін дії кешу"

#: inc/Engine/Admin/Settings/Page.php:489
#, php-format
msgid "Cache files older than the specified lifespan will be deleted.<br>Enable %1$spreloading%2$s for the cache to be rebuilt automatically after lifespan expiration."
msgstr "Закешовані файли, які старіші за Термін дії кешу, буде видалено.<br>Включіть %1$sПрелоадінг%2$s, щоб кеш створювався заново автоматично після того, як закінчиться термін його дії."

#: inc/Engine/Admin/Settings/Page.php:503
msgid "Enable caching for logged-in WordPress users"
msgstr "Увімкніть кешування для зареєстрованих користувачів WordPress"

#: inc/Engine/Admin/Settings/Page.php:511
msgid "Enable caching for mobile devices"
msgstr "Увімкнути кешування для мобільних пристроїв"

#: inc/Engine/Admin/Settings/Page.php:526
msgid "Separate cache files for mobile devices"
msgstr "Окремі файли кешу для мобільних пристроїв"

#: inc/Engine/Admin/Settings/Page.php:528
#, php-format
msgid "Most modern themes are responsive and should work without a separate cache. Enable this only if you have a dedicated mobile theme or plugin. %1$sMore info%2$s"
msgstr "Більшість сучасних тем є адаптивними і повинні працювати без окремого кешу. Активуйте це тільки, якщо ви використовуєте окрему тему або плагін для мобільних пристроїв. %1$sДетальніше%2$s"

#: inc/Engine/Admin/Settings/Page.php:544
msgid "Specify time after which the global cache is cleared<br>(0 = unlimited )"
msgstr "Вкажіть час, після якого глобальний кеш буде очищено<br>(0 = необмежений)"

#: inc/Engine/Admin/Settings/Page.php:546
#, php-format
msgid "Reduce lifespan to 10 hours or less if you notice issues that seem to appear periodically. %1$sWhy?%2$s"
msgstr "Скоротіть термін служби до 10 годин або менше, якщо ви помітите, що на сайті періодично виникають проблеми. %1$sЧому?%2$s"

#: inc/Engine/License/views/promo-banner.php:31
#: inc/Engine/License/views/renewal-soon-banner.php:14
msgid "Minutes"
msgstr "Хвилини"

#: inc/Engine/Admin/Settings/Page.php:552
#: inc/Engine/License/views/promo-banner.php:30
#: inc/Engine/License/views/renewal-soon-banner.php:13
msgid "Hours"
msgstr "Години"

#: inc/Engine/Admin/Settings/Page.php:553
#: inc/Engine/License/views/promo-banner.php:29
#: inc/Engine/License/views/renewal-soon-banner.php:12
msgid "Days"
msgstr "Дні"

#: inc/Engine/Admin/Settings/Page.php:602
msgid "File Optimization"
msgstr "Оптимізація файлів"

#: inc/Engine/Admin/Settings/Page.php:603
msgid "Optimize CSS & JS"
msgstr "Оптимізуйте CSS & JS"

#: inc/Engine/Admin/Settings/Page.php:611
#: inc/Engine/Admin/Settings/Page.php:637
#, php-format
msgid "%1$s Minification is currently activated in <strong>Autoptimize</strong>. If you want to use %2$s’s minification, disable those options in Autoptimize."
msgstr "%1$s Minification зараз активовано в режимі <strong>Autoptimize</strong>. Якщо ви хочете використовувати %2$s minification, вимкніть ці параметри у параметрі Autoptimize."

#: inc/Engine/Admin/Settings/Page.php:621
msgid "CSS Files"
msgstr "CSS Файли"

#: inc/Engine/Admin/Settings/Page.php:630
msgid "JavaScript Files"
msgstr "Файли JavaScript"

#: inc/Engine/Admin/Settings/Page.php:663
msgid "Minify CSS files"
msgstr "Зменшення CSS файли"

#: inc/Engine/Admin/Settings/Page.php:664
msgid "Minify CSS removes whitespace and comments to reduce the file size."
msgstr "Зменшення CSS видаляє пробіли та коментарі, щоб зменшити розмір файлу."

#: inc/Engine/Admin/Settings/Page.php:677
#: inc/Engine/Admin/Settings/Page.php:701
#: inc/Engine/Admin/Settings/Page.php:765
#: inc/Engine/Admin/Settings/Page.php:831
#: inc/Engine/Admin/Settings/Page.php:855
msgid "This could break things!"
msgstr "Це може зламати речі!"

#: inc/Engine/Admin/Settings/Page.php:678
#: inc/Engine/Admin/Settings/Page.php:702
#: inc/Engine/Admin/Settings/Page.php:766
#: inc/Engine/Admin/Settings/Page.php:832
#: inc/Engine/Admin/Settings/Page.php:856
msgid "If you notice any errors on your website after having activated this setting, just deactivate it again, and your site will be back to normal."
msgstr "Якщо ви помітили помилки на своєму веб-сайті після активації цього налаштування, просто деактивуйте його знову, і ваш сайт повернеться до нормального стану."

#: inc/Engine/Admin/Settings/Page.php:679
msgid "Activate minify CSS"
msgstr "Активувати мінімізацію CSS"

#: inc/Engine/Admin/Settings/Page.php:684
msgid "Combine CSS files <em>(Enable Minify CSS files to select)</em>"
msgstr "Об'єднати файли CSS <em>(увімкнути мінімізацію файлів CSS для вибору)</em>"

#: inc/Engine/Admin/Settings/Page.php:686
#, php-format
msgid "Combine CSS merges all your files into 1, reducing HTTP requests. Not recommended if your site uses HTTP/2. %1$sMore info%2$s"
msgstr "Об'єднати CSS об'єднує всі ваші файли в 1, зменшуючи HTTP-запити. Не рекомендується, якщо ваш сайт використовує HTTP/2. %1$sБольше інформації%2$s"

#: inc/Engine/Admin/Settings/Page.php:703
msgid "Activate combine CSS"
msgstr "Активувати об`єднати CSS"

#: inc/Engine/Admin/Settings/Page.php:709
msgid "Specify URLs of CSS files to be excluded from minification and concatenation (one per line)."
msgstr "Вкажіть URL-адреси CSS-файлів, які потрібно виключити з мінімізації та об'єднання (по одному на рядок)."

#: inc/Engine/Admin/Settings/Page.php:725
#: inc/Engine/Admin/Settings/Page.php:747
msgid "Optimize CSS delivery"
msgstr "Оптимізуйте доставку CSS"

#: inc/Engine/Admin/Settings/Page.php:800
msgid "Fallback critical CSS"
msgstr "Резервний критичний CSS"

#: inc/Engine/Admin/Settings/Page.php:802
#, php-format
msgid "Provides a fallback if auto-generated critical path CSS is incomplete. %1$sMore info%2$s"
msgstr "Забезпечує запасний варіант, якщо автоматично створений критичний шлях CSS є неповним. %1$sДетальніше%2$s"

#: inc/Engine/Admin/Settings/Page.php:817
msgid "Minify JavaScript files"
msgstr "Зменшити файли JavaScript"

#: inc/Engine/Admin/Settings/Page.php:818
msgid "Minify JavaScript removes whitespace and comments to reduce the file size."
msgstr "Зменшити JavaScript видаляє пробіли та коментарі, щоб зменшити розмір файлу."

#: inc/Engine/Admin/Settings/Page.php:833
msgid "Activate minify JavaScript"
msgstr "Активувати стиснення JavaScript"

#: inc/Engine/Admin/Settings/Page.php:838
msgid "Combine JavaScript files <em>(Enable Minify JavaScript files to select)</em>"
msgstr "Об'єднати файли JavaScript <em>(Увімкнути Мінімізацію файлів JavaScript для вибору)</em>"

#: inc/Engine/Admin/Settings/Page.php:840
#, php-format
msgid "Combine JavaScript files combines your site’s internal, 3rd party and inline JS reducing HTTP requests. Not recommended if your site uses HTTP/2. %1$sMore info%2$s"
msgstr "Об'єднати файли JavaScript поєднує внутрішні, треті сторони та вбудовані JS, що знижують HTTP-запити. Не рекомендується, якщо ваш сайт використовує HTTP / 2. %1$sБольше інформації%2$s"

#: inc/Engine/Admin/Settings/Page.php:857
msgid "Activate combine JavaScript"
msgstr "Активувати об`єднати JavaScript"

#: inc/Engine/Admin/Settings/Page.php:864
#, php-format
msgid "Specify patterns of inline JavaScript to be excluded from concatenation (one per line). %1$sMore info%2$s"
msgstr "Вкажіть шаблони вбудованого JavaScript, які будуть виключені з об'єднання (по одному на рядок). %1$sДетальніше%2$s"

#: inc/Engine/Admin/Settings/Page.php:881
msgid "Specify URLs of JavaScript files to be excluded from minification and concatenation (one per line)."
msgstr "Вкажіть URL-адреси файлів JavaScript, які потрібно виключити зі зменшення та об'єднання (по одному на рядок)."

#: inc/Engine/Admin/Settings/Page.php:882
msgid "<strong>Internal:</strong> The domain part of the URL will be stripped automatically. Use (.*).js wildcards to exclude all JS files located at a specific path."
msgstr "<strong>Примітка:</strong> Доменне ім'я буде видалено автоматично. Використовуйте маску (.*).js для виключення всіх JS файлів в заданому шляху."

#: inc/Engine/Admin/Settings/Page.php:884
#, php-format
msgid "<strong>3rd Party:</strong> Use either the full URL path or only the domain name, to exclude external JS. %1$sMore info%2$s"
msgstr "<strong>Сторонні модулі:</strong> Використовуйте повний шлях або доменне ім'я щоб виключити зовнішні JS. %1$sДетальніше%2$s"

#: inc/Engine/Admin/Settings/Page.php:900
msgid "Load JavaScript deferred"
msgstr "Відкладене завантаження JavaScript"

#: inc/Engine/Admin/Settings/Page.php:902
#, php-format
msgid "Load JavaScript deferred eliminates render-blocking JS on your site and can improve load time. %1$sMore info%2$s"
msgstr "Відкладене завантаження JavaScript усуває можливість блокування JS на вашому сайті та покращує час завантаження. %1$sДетальніше%2$s"

#: inc/Engine/Admin/Settings/Page.php:993
msgid "Media"
msgstr "Медіафайли"

#: inc/Engine/Admin/Settings/Page.php:1003
msgid "Autoptimize"
msgstr "Autoptimize"

#: inc/Engine/Admin/Settings/Page.php:1048
msgid "LazyLoad"
msgstr "LazyLoad"

#: inc/Engine/Admin/Settings/Page.php:1051
#, php-format
msgid "It can improve actual and perceived loading time as images, iframes, and videos will be loaded only as they enter (or about to enter) the viewport and reduces the number of HTTP requests. %1$sMore Info%2$s"
msgstr "Вона може покращити фактичний і сприйнятий час завантаження - зображення, фрейми, та відео буде завантажено лише після того, як вони ввійдуть до вікна перегляду та скоротять кількість запитів HTTP. %1$sБільше інформації %2$s"

#: inc/Engine/Admin/Settings/Page.php:1928
#, php-format
msgid "Enable this option if you would like WP Rocket to serve WebP images to compatible browsers. Please note that WP Rocket cannot create WebP images for you. To create WebP images we recommend %1$sImagify%2$s. %3$sMore info%2$s"
msgstr "Активуйте цю опцію, якщо ви хочете, щоб WP Rocket виводив зображення WebP у браузерах, які підтримують цей формат. Зверніть увагу, що WP Rocket не може створити WebP зображення для вас. Для їх безпосереднього створення ми рекомендуємо %1$sImagify%2$s. %3$sБільше інформації%2$s"

#: inc/Engine/Admin/Settings/Page.php:1083
msgid "Enable for images"
msgstr "Увімкнути для зображень"

#: inc/Engine/Admin/Settings/Page.php:1103
msgid "Enable for iframes and videos"
msgstr "Увімкнути iframes і відео"

#: inc/Engine/Admin/Settings/Page.php:1118
msgid "Replace YouTube iframe with preview image"
msgstr "Замініть YouTube iframe попереднім зображенням"

#: inc/Engine/Admin/Settings/Page.php:1120
msgid "This can significantly improve your loading time if you have a lot of YouTube videos on a page."
msgstr "Це може значно поліпшити час завантаження, якщо на сторінці є багато відео YouTube."

#: inc/Engine/Admin/Settings/Page.php:1164
#: inc/deprecated/deprecated.php:1776
msgid "Preload"
msgstr "Презавантаження"

#: inc/Engine/Admin/Settings/Page.php:1199
msgid "Prefetch DNS Requests"
msgstr "Оптимізація DNS запитів"

#: inc/Engine/Admin/Settings/Page.php:1201
msgid "DNS prefetching can make external files load faster, especially on mobile networks"
msgstr "Попередня вибірка DNS може прискорювати завантаження зовнішніх файлів, особливо в мобільних мережах"

#: inc/Engine/Admin/Settings/Page.php:1223
msgid "Activate Preloading"
msgstr "Активувати Презавантаження"

#: inc/Engine/Admin/Settings/Page.php:1250
msgid "URLs to prefetch"
msgstr "URL для кешування"

#: inc/Engine/Admin/Settings/Page.php:1251
msgid "Specify external hosts to be prefetched (no <code>http:</code>, one per line)"
msgstr "Вкажіть зовнішні вузли, які потрібно попередньо завантажувати (не <code>http:</code> один на рядок)"

#: inc/Engine/Admin/Settings/Page.php:1290
msgid "Advanced Rules"
msgstr "Розширені правила"

#: inc/Engine/Admin/Settings/Page.php:1291
msgid "Fine-tune cache rules"
msgstr "Розширені кеш правила"

#: inc/Engine/Admin/Settings/Page.php:1302
msgid "Sensitive pages like custom login/logout URLs should be excluded from cache."
msgstr "Чутливі сторінки, такі як користувацькі URL-адреси для входу/виходу, повинні бути виключені з кешу."

#: inc/Engine/Admin/Settings/Page.php:1305
msgctxt "plugin name"
msgid "WooCommerce"
msgstr "WooCommerce"

#: inc/Engine/Admin/Settings/Page.php:1307
msgctxt "plugin name"
msgid "Easy Digital Downloads"
msgstr "Easy Digital Downloads"

#: inc/Engine/Admin/Settings/Page.php:1309
msgctxt "plugin name"
msgid "iThemes Exchange"
msgstr "iThemes Exchange"

#: inc/Engine/Admin/Settings/Page.php:1311
msgctxt "plugin name"
msgid "Jigoshop"
msgstr "Jigoshop"

#: inc/Engine/Admin/Settings/Page.php:1313
msgctxt "plugin name"
msgid "WP-Shop"
msgstr "WP-Магазин"

#: inc/Engine/Admin/Settings/Page.php:1319
#, php-format
msgid "<br>Cart, checkout and \"my account\" pages set in <strong>%1$s%2$s%3$s</strong> will be detected and never cached by default."
msgstr "<br>Кошик, оформлення замовлення і кабінет покупця встановлюються в <strong>%1$s%2$s%3$s</strong> визначаються автоматично, їх вказувати не потрібно."

#: inc/Engine/Admin/Settings/Page.php:1337
msgid "Never Cache Cookies"
msgstr "Ніколи не кешувати файли cookie"

#: inc/Engine/Admin/Settings/Page.php:1355
msgid "Cache Query String(s)"
msgstr "Строки запиту кешу"

#: inc/Engine/Admin/Settings/Page.php:1358
#, php-format
msgid "%1$sCache for query strings%2$s enables you to force caching for specific GET parameters."
msgstr "%1$sCache для запитів%2$s дозволяє примусово кешувати певні параметри GET."

#: inc/Engine/Admin/Settings/Page.php:1369
msgid "Specify URLs of pages or posts that should never be cached (one per line)"
msgstr "Вкажіть URL-адреси сторінок або публікацій, які ніколи не повинні кешуватися (по одному на рядок)"

#: inc/Engine/Admin/Settings/Page.php:1370
#: inc/Engine/Admin/Settings/Page.php:1398
msgid "The domain part of the URL will be stripped automatically.<br>Use (.*) wildcards to address multiple URLs under a given path."
msgstr "Частина URL домену буде видалена автоматично.<br>Використовуйте символи (. *) Для адреси декількох URL-адрес у заданому шляху."

#: inc/Engine/Admin/Settings/Page.php:1387
msgid "Specify user agent strings that should never see cached pages (one per line)"
msgstr "Вкажіть рядки агента користувача, які ніколи не повинні бачити кешовані сторінки (по одному на рядок)"

#: inc/Engine/Admin/Settings/Page.php:1388
msgid "Use (.*) wildcards to detect parts of UA strings."
msgstr "Використовуйте символи (. *) Для виявлення частин рядків UA."

#: inc/Engine/Admin/Settings/Page.php:1397
msgid "Specify URLs you always want purged from cache whenever you update any post or page (one per line)"
msgstr "Вкажіть URL-адреси, які завжди потрібно очищати від кешу, коли ви оновлюєте будь-яку публікацію або сторінку (по одному на рядок)"

#: inc/Engine/Admin/Settings/Page.php:1406
msgid "Specify query strings for caching (one per line)"
msgstr "Вкажіть рядки запитів для кешування (по одному на рядок)"

#: inc/Engine/Admin/Settings/Page.php:1431
#: inc/deprecated/deprecated.php:1775
msgid "Database"
msgstr "База даних"

#: inc/Engine/Admin/Settings/Page.php:1432
msgid "Optimize, reduce bloat"
msgstr "Оптимізуйте, зменшіть розмір"

#: inc/Engine/Admin/Settings/Page.php:1439
msgid "Post Cleanup"
msgstr "Очистка записів"

#: inc/Engine/Admin/Settings/Page.php:1441
msgid "Post revisions and drafts will be permanently deleted. Do not use this option if you need to retain revisions or drafts."
msgstr "Перевірки публікації та чернетки видаляються назавжди. Не використовуйте цей параметр, якщо потрібно зберегти зміни або чернетки."

#: inc/Engine/Admin/Settings/Page.php:1446
msgid "Comments Cleanup"
msgstr "Очищення коментарів"

#: inc/Engine/Admin/Settings/Page.php:1448
msgid "Spam and trashed comments will be permanently deleted."
msgstr "Спам та видалені коментарі буде видалено назавжди."

#: inc/Engine/Admin/Settings/Page.php:1452
msgid "Transients Cleanup"
msgstr "Очищення транзакцій"

#: inc/Engine/Admin/Settings/Page.php:1454
msgid "Transients are temporary options; they are safe to remove. They will be automatically regenerated as your plugins require them."
msgstr "Транзакції це тимчасові налаштування, їх можна смело удалять. Вони будуть створені автоматично, якщо потребуються плагину."

#: inc/Engine/Admin/Settings/Page.php:1458
msgid "Database Cleanup"
msgstr "Очищення бази даних"

#: inc/Engine/Admin/Settings/Page.php:1460
msgid "Reduces overhead of database tables"
msgstr "Зменшує накладні витрати на таблиці бази даних"

#: inc/Engine/Admin/Settings/Page.php:1477
#, php-format
msgid "%s revision in your database."
msgid_plural "%s revisions in your database."
msgstr[0] "%s ревізія у вашій базі даних."
msgstr[1] "%s ревізій у вашій базі даних."
msgstr[2] "%s ревізій у вашій базі даних."
msgstr[3] "%s ревізій у вашій базі даних."

#: inc/Engine/Admin/Settings/Page.php:1487
#, php-format
msgid "%s draft in your database."
msgid_plural "%s drafts in your database."
msgstr[0] "%s чернетка у вашій базі даних."
msgstr[1] "%s чернеток у вашій базі даних."
msgstr[2] "%s чернеток у вашій базі даних."
msgstr[3] "%s чернеток у вашій базі даних."

#: inc/Engine/Admin/Settings/Page.php:1497
#, php-format
msgid "%s trashed post in your database."
msgid_plural "%s trashed posts in your database."
msgstr[0] "%s видалений запис у вашій базі даних."
msgstr[1] "%s видалених записів у вашій базі даних."
msgstr[2] "%s видалених записів у вашій базі даних."
msgstr[3] "%s видалених записів у вашій базі даних."

#: inc/Engine/Admin/Settings/Page.php:1507
#, php-format
msgid "%s spam comment in your database."
msgid_plural "%s spam comments in your database."
msgstr[0] "%s спамний коментар у вашій базі даних."
msgstr[1] "%s спамних коментарів у вашій базі даних."
msgstr[2] "%s спамних коментарів у вашій базі даних."
msgstr[3] "%s спамних коментарів у вашій базі даних."

#: inc/Engine/Admin/Settings/Page.php:1517
#, php-format
msgid "%s trashed comment in your database."
msgid_plural "%s trashed comments in your database."
msgstr[0] "%s видалений коментар у вашій базі даних."
msgstr[1] "%s видалених коментарів у вашій базі даних."
msgstr[2] "%s видалених коментарів у вашій базі даних."
msgstr[3] "%s видалених коментарів у вашій базі даних."

#: inc/Engine/Admin/Settings/Page.php:1525
msgid "All transients"
msgstr "Всі перехідні"

#: inc/Engine/Admin/Settings/Page.php:1527
#, php-format
msgid "%s transient in your database."
msgid_plural "%s transients in your database."
msgstr[0] "У вашій базі даних %s тимчасові дані."
msgstr[1] "У вашій базі даних %s тимчасових даних."
msgstr[2] "У вашій базі даних %s тимчасових даних."
msgstr[3] "У вашій базі даних %s тимчасових даних."

#: inc/Engine/Admin/Settings/Page.php:1535
msgid "Optimize Tables"
msgstr "Оптимізувати таблиці"

#: inc/Engine/Admin/Settings/Page.php:1537
#, php-format
msgid "%s table to optimize in your database."
msgid_plural "%s tables to optimize in your database."
msgstr[0] "%s таблиця, яку можна оптимізувати, у вашій базі даних."
msgstr[1] "%s таблиць, які можна оптимізувати, у вашій базі даних."
msgstr[2] "%s таблиць, які можна оптимізувати, у вашій базі даних."
msgstr[3] "%s таблиць, які можна оптимізувати, у вашій базі даних."

#: inc/Engine/Admin/Settings/Page.php:1548
msgid "Schedule Automatic Cleanup"
msgstr "Розклад автоматичного очищення"

#: inc/Engine/Admin/Settings/Page.php:1560
msgid "Frequency"
msgstr "Частота"

#: inc/Engine/Admin/Settings/Page.php:1568
msgid "Daily"
msgstr "Щоденно"

#: inc/Engine/Admin/Settings/Page.php:1569
msgid "Weekly"
msgstr "Щотижня"

#: inc/Engine/Admin/Settings/Page.php:1570
msgid "Monthly"
msgstr "Щомісяця"

#: inc/Engine/Admin/Settings/Page.php:1587
msgid "Integrate your CDN"
msgstr "Інтегруйте свій CDN"

#: inc/Engine/Admin/Settings/Page.php:1599
msgid "All URLs of static files (CSS, JS, images) will be rewritten to the CNAME(s) you provide."
msgstr "Всі URL(и) статичних файлів (CSS, JS, images) будуть переписані на надані вами CNAME-адреси."

#: inc/Engine/Admin/Settings/Page.php:1601
#, php-format
msgid "Not required for services like Cloudflare and Sucuri. Please see our available %1$sAdd-ons%2$s."
msgstr "Не потрібен для таких послуг, як Cloudflare і Sucuri. Перегляньте наші доступні %1$sДодатки%2$s."

#: inc/Engine/Admin/Settings/Page.php:1667
msgid "Enable Content Delivery Network"
msgstr "Увімкнути мережу доставки вмісту"

#: inc/Engine/Admin/Settings/Page.php:1676
#: inc/Engine/CDN/RocketCDN/AdminPageSubscriber.php:159
msgid "CDN CNAME(s)"
msgstr "CDN CNAME(и)"

#: inc/Engine/Admin/Settings/Page.php:1677
#: inc/Engine/CDN/RocketCDN/AdminPageSubscriber.php:160
msgid "Specify the CNAME(s) below"
msgstr "Укажіть нижче CNAME(и)"

#: inc/Engine/Admin/Settings/Page.php:1684
msgid "Specify URL(s) of files that should not get served via CDN (one per line)."
msgstr "Вкажіть URL-адреси файлів, які не можна подавати за допомогою CDN (по одному на рядок)."

#: inc/Engine/Admin/Settings/Page.php:1685
msgid "The domain part of the URL will be stripped automatically.<br>Use (.*) wildcards to exclude all files of a given file type located at a specific path."
msgstr "Частина URL домену буде видалена автоматично.<br>Використовуйте символи (. *) Для виключення всіх файлів певного типу файлів, розташованих на певному шляху."

#: inc/Engine/Admin/Settings/Page.php:1708
#: inc/Engine/Admin/Settings/Page.php:1716
msgid "Heartbeat"
msgstr "Серцебиття"

#: inc/Engine/Admin/Settings/Page.php:1709
msgid "Control WordPress Heartbeat API"
msgstr "Керуйте WordPress Heartbeat API"

#: inc/Engine/Admin/Settings/Page.php:1717
msgid "Reducing or disabling the Heartbeat API’s activity can help save some of your server’s resources."
msgstr "Зменшення або вимкнення активності API Heartbeat може допомогти зберегти деякі ресурси вашого сервера."

#: inc/Engine/Admin/Settings/Page.php:1723
msgid "Reduce or disable Heartbeat activity"
msgstr "Зменшити або відключити Серцебиття активність"

#: inc/Engine/Admin/Settings/Page.php:1724
msgid "Reducing activity will change Heartbeat frequency from one hit each minute to one hit every 2 minutes."
msgstr "Зменшення активності змінить частоту серцевих скорочень з одного удару кожну хвилину на один удар кожні 2 хвилини."

#: inc/Engine/Admin/Settings/Page.php:1724
msgid "Disabling Heartbeat entirely may break plugins and themes using this API."
msgstr "Вимкнення функції Heartbeat цілком може призвести до порушення плагінів і тем за допомогою цього API."

#: inc/Engine/Admin/Settings/Page.php:1738
msgid "Do not limit"
msgstr "Не обмежується"

#: inc/Engine/Admin/Settings/Page.php:1739
msgid "Reduce activity"
msgstr "Знизити активність"

#: inc/Engine/Admin/Settings/Page.php:1740
msgid "Disable"
msgstr "Відключити"

#: inc/Engine/Admin/Settings/Page.php:1748
msgid "Control Heartbeat"
msgstr "Контроль серцебиття"

#: inc/Engine/Admin/Settings/Page.php:1757
msgid "Behavior in backend"
msgstr "Поведінка в бекенд"

#: inc/Engine/Admin/Settings/Page.php:1764
msgid "Behavior in post editor"
msgstr "Поведінка в редакторі записів"

#: inc/Engine/Admin/Settings/Page.php:1770
msgid "Behavior in frontend"
msgstr "Поведінка в фронтенд"

#: inc/Engine/Admin/Settings/Page.php:1787
#: views/settings/page-sections/tutorials.php:39
msgid "Add-ons"
msgstr "Додатки"

#: inc/Engine/Admin/Settings/Page.php:1788
msgid "Add more features"
msgstr "Додайте інші функції"

#: inc/Engine/Admin/Settings/Page.php:1795
msgid "One-click Rocket Add-ons"
msgstr "Швидкі Rocket Додатки"

#: inc/Engine/Admin/Settings/Page.php:1796
msgid "One-Click Add-ons are features extending available options without configuration needed. Switch the option \"on\" to enable from this screen."
msgstr "Додатки з одним натисканням - це функції, що розширюють доступні опції без необхідної конфігурації. Щоб увімкнути цей екран, увімкніть \"on\" параметр."

#: inc/Engine/Admin/Settings/Page.php:1806
msgid "Rocket Add-ons"
msgstr "Rocket Надбудови"

#: inc/Engine/Admin/Settings/Page.php:1807
msgid "Rocket Add-ons are complementary features extending available options."
msgstr "Ракетні Додатки доповнюють додаткові можливості."

#: inc/Engine/Admin/Settings/Page.php:1875
msgid "Varnish"
msgstr "Varnish"

#: inc/Engine/Admin/Settings/Page.php:1881
msgid "If Varnish runs on your server, you must activate this add-on."
msgstr "Якщо на вашому сервері запускається Varnish, необхідно активувати цей додаток."

#: inc/Engine/Admin/Settings/Page.php:1883
#, php-format
msgid "Varnish cache will be purged each time WP Rocket clears its cache to ensure content is always up-to-date.<br>%1$sLearn more%2$s"
msgstr "Varnish кеш очищується разом з іншим кешем, щоб підтримувати актуальність контенту.<br>%1$sДетальніше%2$s"

#: inc/Engine/Admin/Settings/Page.php:1817
#: inc/Engine/Admin/Settings/Page.php:1986
msgid "Cloudflare"
msgstr "Cloudflare"

#: inc/Engine/Admin/Settings/Page.php:1823
msgid "Integrate your Cloudflare account with this add-on."
msgstr "Інтегруйте свій обліковий запис Cloudflare з цим додатком."

#: inc/Engine/Admin/Settings/Page.php:1824
msgid "Provide your account email, global API key, and domain to use options such as clearing the Cloudflare cache and enabling optimal settings with WP Rocket."
msgstr "Надайте електронну пошту облікового запису, глобальний ключ API та домен для використання таких параметрів, як очищення кешу Cloudflare та забезпечення оптимальних параметрів за допомогою WP Rocket."

#: inc/Engine/Admin/Settings/Page.php:1948
msgid "Clear the Sucuri cache when WP Rocket’s cache is cleared."
msgstr "Очистіть кеш Sucuri, коли WP Rocket кеш буде очищено."

#: inc/Engine/Admin/Settings/Page.php:1951
msgid "Provide your API key to clear the Sucuri cache when WP Rocket’s cache is cleared."
msgstr "Надайте ключ API, щоб очистити кеш Sucuri, коли кеш WP Rocket очищено."

#: inc/Engine/Admin/Settings/Page.php:1959
#: inc/Engine/Admin/Settings/Page.php:2103
msgid "Sucuri"
msgstr "Sucuri"

#: inc/Engine/Admin/Settings/Page.php:1965
msgid "Synchronize Sucuri cache with this add-on."
msgstr "Синхронізуйте кеш Sucuri з цим додатком."

#: inc/Engine/Admin/Settings/Page.php:2003
msgid "Cloudflare credentials"
msgstr "Cloudflare облікові дані"

#: inc/Engine/Admin/Settings/Page.php:2012
msgid "Cloudflare settings"
msgstr "Cloudflare опції"

#: inc/Engine/Admin/Settings/Page.php:2026
msgctxt "Cloudflare"
msgid "Global API key:"
msgstr "Глобальний API ключ:"

#: inc/Engine/Admin/Settings/Page.php:2027
msgctxt "Cloudflare"
msgid "Find your API key"
msgstr "Знайдіть ваш API ключ"

#: inc/Engine/Admin/Settings/Page.php:2039
msgctxt "Cloudflare"
msgid "Account email"
msgstr "Email облікового запису"

#: inc/Engine/Admin/Settings/Page.php:2048
msgctxt "Cloudflare"
msgid "Zone ID"
msgstr "ID Зони"

#: inc/Engine/Admin/Settings/Page.php:2058
msgid "Development mode"
msgstr "Режим розробки"

#: inc/Engine/Admin/Settings/Page.php:2060
#, php-format
msgid "Temporarily activate development mode on your website. This setting will automatically turn off after 3 hours. %1$sLearn more%2$s"
msgstr "Тимчасово активуйте режим розробки на своєму веб-сайті. Ця настройка автоматично вимкнеться через 3 години. %1$sДокладніше%2$s"

#: inc/Engine/Admin/Settings/Page.php:2068
msgid "Optimal settings"
msgstr "Оптимальні опції"

#: inc/Engine/Admin/Settings/Page.php:2069
msgid "Automatically enhances your Cloudflare configuration for speed, performance grade and compatibility."
msgstr "Автоматично покращує вашу конфігурацію Cloudflare для швидкості, якості продуктивності та сумісності."

#: inc/Engine/Admin/Settings/Page.php:2077
msgid "Relative protocol"
msgstr "Відносний протокол"

#: inc/Engine/Admin/Settings/Page.php:2078
msgid "Should only be used with Cloudflare's flexible SSL feature. URLs of static files (CSS, JS, images) will be rewritten to use // instead of http:// or https://."
msgstr "Потрібно використовувати тільки Cloudflare's flexible SSL. URL адреси статичних файлів (CSS, JS, зображення) будуть переписані для використання // замість http:// або https://."

#: inc/Engine/Admin/Settings/Page.php:2116
msgid "Sucuri credentials"
msgstr "Sucuri облікові дані"

#: inc/Engine/Admin/Settings/Page.php:2130
msgctxt "Sucuri"
msgid "Find your API key"
msgstr "Знайдіть ваш API ключ"

#: inc/Engine/Admin/Settings/Render.php:422
#: inc/deprecated/deprecated.php:1294
msgid "Upload file and import settings"
msgstr "Завантажити налаштування файлу та імпортувати"

#: inc/Engine/Admin/Settings/Settings.php:362
msgid "Sucuri Add-on: The API key for the Sucuri firewall must be in format <code>{32 characters}/{32 characters}</code>."
msgstr "Додаток Sucuri: Ключ API для брандмауера Sucuri повинен мати формат <code>{32 characters}/{32 characters}</code> ."

#: inc/Engine/Admin/Settings/Settings.php:452
#: inc/deprecated/deprecated.php:1245
msgid "Settings saved."
msgstr "Налаштування збережено."

#: inc/classes/class-wp-rocket-requirements-check.php:147
#, php-format
msgid "To function properly, %1$s %2$s requires at least:"
msgstr "Щоб правильно функціонувати,%1$s %2$s вимагає принаймні:"

#: inc/classes/class-wp-rocket-requirements-check.php:151
#, php-format
msgid "PHP %1$s. To use this WP Rocket version, please ask your web host how to upgrade your server to PHP %1$s or higher."
msgstr "PHP %1$s Щоб скористатися цією версією WP Rocket, зверніться до веб-хосту, як оновити ваш сервер до PHP %1$s або вище."

#: inc/classes/class-wp-rocket-requirements-check.php:156
#, php-format
msgid "WordPress %1$s. To use this WP Rocket version, please upgrade WordPress to version %1$s or higher."
msgstr "WordPress %1$s. Щоб використовувати цю версію WP Rocket, оновіть WordPress версії%1$s або вище."

#: inc/classes/class-wp-rocket-requirements-check.php:159
msgid "If you are not able to upgrade, you can rollback to the previous version by using the button below."
msgstr "Якщо ви не можете оновити, можна повернутись до попередньої версії за допомогою кнопки нижче."

#: inc/classes/class-wp-rocket-requirements-check.php:161
#: inc/deprecated/deprecated.php:1947
#, php-format
msgid "Re-install version %s"
msgstr "Перевстановіть версію %s"

#: inc/classes/logger/class-logger.php:260
#: inc/classes/logger/class-logger.php:292
msgid "The log file does not exist."
msgstr "Файл журналу не існує."

#: inc/classes/logger/class-logger.php:266
#: inc/classes/logger/class-logger.php:298
msgid "The log file could not be read."
msgstr "Файл журналу неможливо прочитати."

#: inc/classes/logger/class-logger.php:285
msgid "The logs are not saved into a file."
msgstr "Журнали не зберігаються у файл."

#: inc/Engine/CriticalPath/APIClient.php:64
#, php-format
msgid "Critical CSS for %1$s not generated. Error: %2$s"
msgstr "Критичний CSS для %1$s не створено. Помилка:%2$s"

#: inc/Engine/CriticalPath/ProcessorService.php:273
#, php-format
msgid "Critical CSS for %s generated."
msgstr "Створено критичний CSS для %s."

#: inc/Engine/Cache/PurgeExpired/Subscriber.php:75
msgid "WP Rocket Expired Cache Interval"
msgstr "Строк збереження кешу WP Rocket"

#: inc/Addon/WebP/AdminSubscriber.php:105
msgid "WebP cache is disabled by filter."
msgstr "WebP кеш деактивовано фільтром."

#: inc/Addon/WebP/AdminSubscriber.php:115
#: inc/Addon/WebP/AdminSubscriber.php:141
#, php-format
msgid "You are using %1$s to convert images to WebP. If you want WP Rocket to serve them for you, activate this option. %2$sMore info%3$s"
msgid_plural "You are using %1$s to convert images to WebP. If you want WP Rocket to serve them for you, activate this option. %2$sMore info%3$s"
msgstr[0] "Ви використовуєте %1$s для конвертації зображень в WebP формат. Якщо ви хочете, щоб WP Rocket забезпечував їх, активуйте цю опцію. %2$sДетальніше%3$s"
msgstr[1] "Ви використовуєте %1$s для конвертації зображень в WebP формат. Якщо ви хочете, щоб WP Rocket забезпечував їх, активуйте цю опцію. %2$sДетальніше%3$s"
msgstr[2] "Ви використовуєте %1$s для конвертації зображень в WebP формат. Якщо ви хочете, щоб WP Rocket забезпечував їх, активуйте цю опцію. %2$sДетальніше%3$s"
msgstr[3] "Ви використовуєте %1$s для конвертації зображень в WebP формат. Якщо ви хочете, щоб WP Rocket забезпечував їх, активуйте цю опцію. %2$sДетальніше%3$s"

#: inc/Addon/WebP/AdminSubscriber.php:127
#: inc/Addon/WebP/AdminSubscriber.php:153
#, php-format
msgid "You are using %1$s to convert images to WebP. WP Rocket will create separate cache files to serve your WebP images. %2$sMore info%3$s"
msgid_plural "You are using %1$s to convert images to WebP. WP Rocket will create separate cache files to serve your WebP images. %2$sMore info%3$s"
msgstr[0] "Ви використовуєте %1$s для конвертації зображень в WebP формат. WP Rocket створить окремий кеш, щоб забезпечити використання WebP зображень. %2$sДетальніше%3$s"
msgstr[1] "Ви використовуєте %1$s для конвертації зображень в WebP формат. WP Rocket створить окремий кеш, щоб забезпечити використання WebP зображень. %2$sДетальніше%3$s"
msgstr[2] "Ви використовуєте %1$s для конвертації зображень в WebP формат. WP Rocket створить окремий кеш, щоб забезпечити використання WebP зображень. %2$sДетальніше%3$s"
msgstr[3] "Ви використовуєте %1$s для конвертації зображень в WebP формат. WP Rocket створить окремий кеш, щоб забезпечити використання WebP зображень. %2$sДетальніше%3$s"

#: inc/Addon/WebP/AdminSubscriber.php:185
msgid "WP Rocket will create separate cache files to serve your WebP images."
msgstr "WP Rocket створить окремий кеш, щоб забезпечити використання WebP зображень."

#: inc/Engine/CriticalPath/CriticalCSSSubscriber.php:176
msgid "Critical CSS generation is currently running."
msgstr "Зараз іде генерація Критичного CSS."

#: inc/Engine/CriticalPath/CriticalCSSSubscriber.php:181
#, php-format
msgid "Go to the %1$sWP Rocket settings%2$s page to track progress."
msgstr "Перейдіть на сторнінку %1$sНалаштування WP Rocket %2$s, щоб слідкувати за прогресом. "

#: inc/Engine/CriticalPath/CriticalCSSSubscriber.php:396
#, php-format
msgid "Critical CSS generation is currently running: %1$d of %2$d page types completed. (Refresh this page to view progress)"
msgstr "Виконується створення критичних CSS: %1$d з %2$d типів сторінок уже створено. (Оновити сторінку, щоб побачити процес)"

#: inc/Engine/CriticalPath/CriticalCSSSubscriber.php:472
#, php-format
msgid "Critical CSS generation finished for %1$d of %2$d page types."
msgstr "Створення критичних CSS завершено для %1$d з %2$d типів сторінок."

#: inc/Engine/CriticalPath/CriticalCSSSubscriber.php:479
msgid "Critical CSS generation encountered one or more errors."
msgstr "Критичне CSS генерування зіткнулося з однією або декількома помилками."

#: inc/Engine/CriticalPath/CriticalCSSSubscriber.php:479
msgid "Learn more."
msgstr "Вивчити більше."

#: inc/Engine/Admin/Database/Subscriber.php:79
#: inc/deprecated/Engine/Addon/FacebookTracking/Subscriber.php:92
#: inc/deprecated/Engine/Addon/GoogleTracking/Subscriber.php:137
msgid "weekly"
msgstr "щотижня"

#: inc/classes/subscriber/Tools/class-detect-missing-tags-subscriber.php:151
#, php-format
msgid "Failed to detect the following requirement in your theme: closing %1$s."
msgid_plural "Failed to detect the following requirements in your theme: closing %1$s."
msgstr[0] "Не вдалося виявити наступну вимогу вашої теми: закриваємо %1$s."
msgstr[1] "Не вдалося виявити наступні вимоги вашої теми: закриваємо %1$s."
msgstr[2] "Не вдалося виявити наступні вимоги вашої теми: закриваємо %1$s."
msgstr[3] "Не вдалося виявити наступні вимоги вашої теми: закриваємо %1$s."

#: inc/Addon/Cloudflare/API/Client.php:190
#: inc/Addon/Cloudflare/API/Client.php:204
#: inc/Addon/Cloudflare/Cloudflare.php:74
#: inc/Addon/Cloudflare/Cloudflare.php:109
#: inc/classes/subscriber/Tools/class-detect-missing-tags-subscriber.php:157
#: inc/deprecated/3.5.php:92
#: inc/deprecated/3.5.php:116
#: inc/deprecated/3.5.php:129
#: inc/deprecated/3.5.php:157
#: inc/deprecated/3.5.php:173
#, php-format
msgid "Read the %1$sdocumentation%2$s for further guidance."
msgstr "Прочитайте %1$sдокументацію%2$s для подальших вказівок."

#: inc/classes/subscriber/Tools/class-detect-missing-tags-subscriber.php:159
msgid "https://docs.wp-rocket.me/article/99-pages-not-cached-or-minify-cssjs-not-working/?utm_source=wp_plugin&utm_medium=wp_rocket#theme"
msgstr "https://docs.wp-rocket.me/article/99-pages-not-cached-or-minify-cssjs-not-working/?utm_source=wp_plugin&utm_medium=wp_rocket#theme"

#: inc/Engine/Admin/Database/Subscriber.php:85
msgid "monthly"
msgstr "щомісячно"

#: inc/Engine/Admin/Database/Subscriber.php:194
msgid "Database optimization process is running"
msgstr "Процес оптимізації баз даних працює"

#: inc/Engine/Admin/Database/Subscriber.php:224
msgid "Database optimization process is complete. Everything was already optimized!"
msgstr "Процес оптимізації баз даних завершено. Все вже було оптимізовано!"

#: inc/Engine/Admin/Database/Subscriber.php:227
msgid "Database optimization process is complete. List of optimized items below:"
msgstr "Процес оптимізації баз даних завершено. Список оптимізованих елементів нижче:"

#: inc/Engine/Admin/Database/Subscriber.php:235
#, php-format
msgid "%1$d %2$s optimized."
msgstr "%1$d %2$s оптимізовано."

#: inc/Engine/Admin/Settings/Subscriber.php:168
#: inc/deprecated/deprecated.php:1786
#: views/settings/page-sections/tools.php:33
msgid "Tools"
msgstr "Інструменти"

#: inc/Engine/Admin/Settings/Subscriber.php:169
msgid "Import, Export, Rollback"
msgstr "Імпорт, Експорт, Відкат"

#: inc/Engine/Admin/Settings/Subscriber.php:194
#: views/settings/page-sections/imagify.php:14
msgid "Image Optimization"
msgstr "Оптимізація зображень"

#: inc/Engine/Admin/Settings/Subscriber.php:195
msgid "Compress your images"
msgstr "Стиснути зображення"

#: inc/Engine/Admin/Settings/Subscriber.php:212
#: views/settings/page-sections/tutorials.php:48
msgid "Tutorials"
msgstr "Підручники"

#: inc/Engine/Admin/Settings/Subscriber.php:213
msgid "Getting started and how to videos"
msgstr "Початок роботи та допоміжні відео"

#: inc/ThirdParty/Plugins/Optimization/Hummingbird.php:78
#, php-format
msgctxt "Hummingbird notice"
msgid "Please deactivate the following %s option which conflicts with WP Rocket features:"
msgid_plural "Please deactivate the following %s options which conflict with WP Rocket features:"
msgstr[0] "Будь ласка, деактивуйте таку %s опцію, яка конфліктує з функціоналом WP Rocket:"
msgstr[1] "Будь ласка, деактивуйте такі %s опції, які конфліктують з функціоналом WP Rocket:"
msgstr[2] "Будь ласка, деактивуйте такі %s опції, які конфліктують з функціоналом WP Rocket:"
msgstr[3] "Будь ласка, деактивуйте такі %s опції, які конфліктують з функціоналом WP Rocket:"

#: inc/ThirdParty/Plugins/Optimization/Hummingbird.php:151
#, php-format
msgctxt "Hummingbird notice"
msgid "%1$s %2$sdisable emoji%3$s conflicts with WP Rockets %2$sdisable emoji%3$s"
msgstr "%1$s %2$sвідключення emoji%3$s конфліктує з функцією WP Rocket %2$sвідключення emoji%3$s"

#: inc/ThirdParty/Plugins/Optimization/Hummingbird.php:187
#, php-format
msgctxt "Hummingbird notice"
msgid "%1$s %2$sGZIP compression%3$s conflicts with WP Rocket %2$sGZIP compression%3$s"
msgstr "%1$s %2$sGZIP-компресія%3$s конфліктує з WP Rocket %2$sGZIP-компресією%3$s"

#: inc/ThirdParty/Plugins/Optimization/Hummingbird.php:223
#, php-format
msgctxt "Hummingbird notice"
msgid "%1$s %2$sbrowser caching%3$s conflicts with WP Rocket %2$sbrowser caching%3$s"
msgstr "%1$s %2$s браузерне кешування %3$s конфліктує з WP Rocket %2$s браузерним кешуванням %3$s"

#: inc/ThirdParty/Plugins/Optimization/Hummingbird.php:255
#, php-format
msgctxt "Hummingbird notice"
msgid "%1$s %2$spage caching%3$s conflicts with WP Rocket %2$spage caching%3$s"
msgstr "%1$s %2$s кешування сторінок %3$s конфліктує з WP Rocket %2$s кешуванням сторінок %3$s"

#: inc/ThirdParty/Plugins/Optimization/Hummingbird.php:289
#, php-format
msgctxt "Hummingbird notice"
msgid "%1$s %2$sasset optimization%3$s conflicts with WP Rocket %2$sfile optimization%3$s"
msgstr "%1$s %2$s оптимізація ресурсів %3$s конфліктує з Оптимізацією файлів %2$s WP Rocket %3$s"

#: inc/ThirdParty/Plugins/Smush.php:108
#: inc/ThirdParty/Plugins/Smush.php:126
msgid "Smush"
msgstr "Smush"

#: inc/Addon/Sucuri/Subscriber.php:95
#, php-format
msgid "Sucuri cache purge error: %s"
msgstr "Помилка видалення кешу Sucuri: %s"

#: inc/Addon/Sucuri/Subscriber.php:100
msgid "The Sucuri cache is being cleared. Note that it may take up to two minutes for it to be fully flushed."
msgstr "Кеш Sucuri очищається. Зверніть увагу, що для заповнення може знадобитися до двох хвилин."

#: inc/Addon/Sucuri/Subscriber.php:217
msgid "Sucuri firewall API key was not found."
msgstr "Ключ API брандмауера Sucuri не знайдено."

#: inc/Addon/Sucuri/Subscriber.php:230
msgid "Sucuri firewall API key is invalid."
msgstr "Ключ API брандмауера Sucuri недійсний."

#: inc/Addon/Sucuri/Subscriber.php:285
#, php-format
msgid "Error when contacting Sucuri firewall API. Error message was: %s"
msgstr "Помилка під час звернення до API брандмауера Sucuri. Повідомлення про помилку було: %s"

#: inc/Addon/Sucuri/Subscriber.php:300
msgid "Could not get a response from the Sucuri firewall API."
msgstr "Не вдалося отримати відповідь від API брандмауера Sucuri."

#: inc/Addon/Sucuri/Subscriber.php:315
msgid "Got an invalid response from the Sucuri firewall API."
msgstr "Отримано недійсний відповідь від API брандмауера Sucuri."

#: inc/Addon/Sucuri/Subscriber.php:329
msgid "The Sucuri firewall API returned an unknown error."
msgstr "API брандмауера Sucuri повернув невідома помилка."

#: inc/Addon/Sucuri/Subscriber.php:333
#, php-format
msgid "The Sucuri firewall API returned the following error: %s"
msgid_plural "The Sucuri firewall API returned the following errors: %s"
msgstr[0] "API файєрволлу Sucuri видав таку помилку: %s"
msgstr[1] "API файєрволлу Sucuri видав такі помилки: %s"
msgstr[2] "API файєрволлу Sucuri видав такі помилки: %s"
msgstr[3] "API файєрволлу Sucuri видав такі помилки: %s"

#: inc/Engine/Plugin/UpdaterApiTools.php:32
#: inc/deprecated/3.3.php:761
#: inc/deprecated/3.3.php:770
#, php-format
msgid "An unexpected error occurred. Something may be wrong with WP-Rocket.me or this server&#8217;s configuration. If you continue to have problems, <a href=\"%s\">contact support</a>."
msgstr "Виникла несподівана помилка. Щось може бути неправильним з WP-Rocket.me або конфігурацією цього сервера. Якщо у вас виникнуть проблеми, <a href=\"%s\">зверніться до служби підтримки</a> ."

#: inc/common/admin-bar.php:124
#: inc/functions/i18n.php:41
#: inc/functions/i18n.php:51
msgid "All languages"
msgstr "Всі мови"

#: inc/common/admin-bar.php:160
msgid "Clear this post"
msgstr "Очистити цей запис"

#: inc/common/admin-bar.php:174
msgid "Purge this URL"
msgstr "Очистіть цю URL-адресу"

#: inc/Engine/CriticalPath/Admin/Admin.php:264
msgid "Regenerate Critical Path CSS"
msgstr "Перестворити критичні CSS"

#: inc/common/admin-bar.php:194
msgid "Purge Sucuri cache"
msgstr "Purge Sucuri кеш"

#: inc/common/admin-bar.php:231
#: views/settings/partials/documentation.php:14
msgid "Documentation"
msgstr "Документація"

#: inc/deprecated/3.10.php:163
msgid "OPcache purge failed."
msgstr "Не вдалося оновити OPcache кеш."

#: inc/deprecated/3.10.php:168
msgid "OPcache successfully purged"
msgstr "OPcache успішно оновлено"

#: inc/deprecated/3.5.php:587
#, php-format
msgid "<strong>WP Rocket:</strong> %s"
msgstr "<strong>WP Rocket:</strong> %s"

#: inc/deprecated/3.5.php:592
msgid "<strong>WP Rocket:</strong> Cloudflare cache successfully purged."
msgstr "<strong>WP Rocket:</strong> кеш Cloudflare успішно очищено."

#: inc/deprecated/3.2.php:52
#: views/settings/page-sections/imagify.php:36
msgid "Activate Imagify"
msgstr "Активувати Imagify"

#: inc/deprecated/3.2.php:52
msgid "Install Imagify for Free"
msgstr "Установіть Imagify безкоштовно"

#: inc/deprecated/3.2.php:67
msgid "Speed up your website and boost your SEO by reducing image file sizes without losing quality with Imagify."
msgstr "Прискорити ваш сайт і збільшити ваш SEO за рахунок зменшення розмірів файлів зображень без втрати якості за допомогою Imagify."

#: inc/deprecated/3.2.php:72
msgid "More details"
msgstr "Детальніше"

#: inc/deprecated/3.2.php:228
#, php-format
msgid "Sitemap preload: %d pages have been cached."
msgstr "Попереднє завантаження мапи сайту: %d сторінок було закешовано."

#: inc/deprecated/3.2.php:261
#, php-format
msgid "Sitemap preload: %d uncached pages have now been preloaded. (refresh to see progress)"
msgstr "Попереднє завантаження Sitemap: попередньо закешовано %d сторінок. (оновити, щоб побачити прогрес)"

#: inc/deprecated/3.4.php:18
msgid "Choose a domain from the list"
msgstr "Виберіть домен зі списку"

#: inc/deprecated/3.4.php:31
msgid "No domain available in your Cloudflare account"
msgstr "Домен недоступний у вашому Cloudflare записі"

#: inc/deprecated/deprecated.php:240
msgid "<strong>JS</strong> files with Deferred Loading JavaScript"
msgstr "<strong>JS</strong> файли з Відкладеного завантаження JavaScript"

#: inc/deprecated/deprecated.php:293
msgid "Add URL"
msgstr "Додати URL"

#: inc/deprecated/deprecated.php:1277
#: views/settings/fields/import-form.php:22
msgid "Before you can upload your import file, you will need to fix the following error:"
msgstr "Перш ніж ви зможете завантажити файл імпорту, необхідно буде виправити наступну помилку:"

#: inc/deprecated/deprecated.php:1288
#: views/settings/fields/import-form.php:35
#, php-format
msgid "Choose a file from your computer (maximum size: %s)"
msgstr "Виберіть файл з вашого пк (максимальний розмір: %s)"

#: inc/deprecated/deprecated.php:1385
msgid "Your Cloudflare credentials are valid."
msgstr "Ваші облікові дані Cloudflare дійсні."

#: inc/deprecated/deprecated.php:1389
msgid "Your Cloudflare credentials are invalid!"
msgstr "Ваші облікові дані Cloudflare недійсні!"

#: inc/deprecated/deprecated.php:1453
msgid "Save and optimize"
msgstr "Зберегти та оптимізувати"

#: inc/deprecated/deprecated.php:1453
msgid "Optimize"
msgstr "Оптимізувати"

#: inc/deprecated/deprecated.php:1464
#: inc/deprecated/deprecated.php:1666
msgctxt "screen-reader-text"
msgid "Note:"
msgstr "Примітка:"

#: inc/deprecated/deprecated.php:1470
msgctxt "screen-reader-text"
msgid "Performance tip:"
msgstr "Продуктивність поради:"

#: inc/deprecated/deprecated.php:1476
msgctxt "screen-reader-text"
msgid "Third-party feature detected:"
msgstr "Виявлено сторонній плагін:"

#: inc/deprecated/deprecated.php:1488
#: inc/deprecated/deprecated.php:1672
msgctxt "screen-reader-text"
msgid "Warning:"
msgstr "Увага:"

#: inc/deprecated/deprecated.php:1501
msgctxt "button text"
msgid "Download settings"
msgstr "Завантажити налаштування"

#: inc/deprecated/deprecated.php:1531
msgid "Replace site's hostname with:"
msgstr "Замінити ім'я хосту сайту на:"

#: inc/deprecated/deprecated.php:1550
#: inc/deprecated/deprecated.php:1587
#: inc/deprecated/deprecated.php:1615
#: views/settings/fields/cnames.php:41
#: views/settings/fields/cnames.php:75
#: views/settings/fields/cnames.php:105
msgid "reserved for"
msgstr "зарезервовано для"

#: inc/deprecated/deprecated.php:1552
#: inc/deprecated/deprecated.php:1589
#: inc/deprecated/deprecated.php:1617
#: views/settings/fields/cnames.php:44
#: views/settings/fields/cnames.php:78
#: views/settings/fields/cnames.php:108
msgid "All files"
msgstr "Усі файли"

#: inc/deprecated/deprecated.php:1564
#: inc/deprecated/deprecated.php:1594
#: inc/deprecated/deprecated.php:1622
#: views/settings/fields/cnames.php:56
#: views/settings/fields/cnames.php:90
#: views/settings/fields/cnames.php:120
msgid "Images"
msgstr "Зображення"

#: inc/deprecated/deprecated.php:1635
#: views/settings/fields/cnames.php:132
msgid "Add CNAME"
msgstr "Додати CNAME"

#: inc/deprecated/deprecated.php:1706
msgid "Watch the video"
msgstr "Переглянути відео"

#: inc/deprecated/deprecated.php:1771
msgid "Basic"
msgstr "Основний"

#: inc/deprecated/deprecated.php:1772
msgid "Static Files"
msgstr "Статичні файли"

#: inc/deprecated/deprecated.php:1774
msgid "Advanced"
msgstr "Розширений"

#: inc/deprecated/deprecated.php:1944
#, php-format
msgid "%1$s %2$s requires at least PHP %3$s to function properly. To use this version, please ask your web host how to upgrade your server to PHP %3$s or higher. If you are not able to upgrade, you can rollback to the previous version by using the button below."
msgstr "%1$s %2$s треба наявності версії PHP не нижче %3$s Щоб скористатися цією функцією, зверніться до хостера з проханням підвищити версію PHP до %3$s і вище. В іншому випадку, використовуйте стару версію плагіна."

#: inc/functions/admin.php:21
msgid "There seems to be an issue validating your license. Please see the error message below."
msgid_plural "There seems to be an issue validating your license. You can see the error messages below."
msgstr[0] "Здається, виникла проблема з валідацією вашої ліцензії. Помилку зазначено нижче."
msgstr[1] "Здається, виникла проблема з валідацією вашої ліцензії. Помилки зазначені нижче."
msgstr[2] "Здається, виникла проблема з валідацією вашої ліцензії. Помилки зазначені нижче."
msgstr[3] "Здається, виникла проблема з валідацією вашої ліцензії. Помилки зазначені нижче."

#: inc/functions/admin.php:361
msgid "Server type:"
msgstr "Тип сервера:"

#: inc/functions/admin.php:370
msgid "PHP version number:"
msgstr "Номер версії PHP:"

#: inc/functions/admin.php:379
msgid "WordPress version number:"
msgstr "Номер версії WordPress:"

#: inc/functions/admin.php:388
msgid "WordPress multisite:"
msgstr "WordPress мультисайт:"

#: inc/functions/admin.php:397
msgid "Current theme:"
msgstr "Поточна тема:"

#: inc/functions/admin.php:406
msgid "Current site language:"
msgstr "Мова поточного сайту:"

#: inc/functions/admin.php:415
msgid "Active plugins:"
msgstr "Активні плагіни:"

#: inc/functions/admin.php:418
msgid "Plugin names of all active plugins"
msgstr "Імена плагінів всіх активних плагінів"

#: inc/functions/admin.php:424
msgid "Anonymized WP Rocket settings:"
msgstr "Анонімні WP Rocket опції:"

#: inc/functions/admin.php:427
msgid "Which WP Rocket settings are active"
msgstr "Які настройки WP Rocket є активні"

#: inc/deprecated/3.5.php:71
#: inc/deprecated/3.5.php:195
msgid "Curl is disabled on your server. Please ask your host to enable it. This is required for the Cloudflare Add-on to work correctly."
msgstr "Curl деактивовано на вашому сервері. Будь ласка, попросіть вашого хостера ввімкнути його. Це потрібно, щоб Додаток Cloudflare працював коректно."

#: inc/deprecated/3.5.php:79
#, php-format
msgid "Cloudflare email, API key and Zone ID are not set. Read the %1$sdocumentation%2$s for further guidance."
msgstr "Імейл, зареєстрований в Cloudflare, API key та Zone ID не вказані. Читайте %1$sдокументацію%2$s для подальших інструкцій."

#: inc/Addon/Cloudflare/API/Client.php:192
#: inc/Addon/Cloudflare/API/Client.php:206
#: inc/Addon/Cloudflare/Auth/APIKey.php:63
#: inc/Addon/Cloudflare/Cloudflare.php:76
#: inc/Addon/Cloudflare/Cloudflare.php:111
#: inc/deprecated/3.5.php:81
#: inc/deprecated/3.5.php:94
#: inc/deprecated/3.5.php:118
#: inc/deprecated/3.5.php:131
#: inc/deprecated/3.5.php:159
#: inc/deprecated/3.5.php:175
#: inc/deprecated/3.5.php:208
msgid "https://docs.wp-rocket.me/article/18-using-wp-rocket-with-cloudflare/?utm_source=wp_plugin&utm_medium=wp_rocket#add-on"
msgstr "https://docs.wp-rocket.me/article/18-using-wp-rocket-with-cloudflare/?utm_source=wp_plugin&utm_medium=wp_rocket#add-on"

#: inc/Addon/Cloudflare/Cloudflare.php:70
#: inc/deprecated/3.5.php:88
msgid "Missing Cloudflare Zone ID."
msgstr "Не вказано Cloudflare Zone ID."

#: inc/Addon/Cloudflare/API/Client.php:186
#: inc/deprecated/3.5.php:112
#: inc/deprecated/3.5.php:169
msgid "Incorrect Cloudflare email address or API key."
msgstr "Невірний імейл або API ключ Cloudflare."

#: inc/Addon/Cloudflare/API/Client.php:200
#: inc/deprecated/3.5.php:125
msgid "Incorrect Cloudflare Zone ID."
msgstr "Невірне значення Cloudflare Zone ID."

#: inc/Addon/Cloudflare/Cloudflare.php:105
#: inc/deprecated/3.5.php:153
msgid "It looks like your domain is not set up on Cloudflare."
msgstr "Здається, що ваш домен не налаштовано у Cloudflare."

#: inc/deprecated/3.5.php:206
#, php-format
msgid "Cloudflare email and API key are not set. Read the %1$sdocumentation%2$s for further guidance."
msgstr "Імейл, зареєстрований в Cloudflare, API key та Zone ID не вказані. Читайте %1$sдокументацію%2$s для подальших інструкцій."

#: inc/deprecated/3.5.php:271
msgid "Connection to Cloudflare failed"
msgstr "Не вдалося підключитися до Cloudflare"

#: inc/functions/options.php:491
#: inc/functions/options.php:530
msgid "License validation failed. Our server could not resolve the request from your website."
msgstr "Помилка перевірки ліцензії. Наш сервер не може вирішити запит з вашого веб-сайту."

#: inc/functions/options.php:530
#, php-format
msgid "Try clicking %1$sSave Changes%2$s below. If the error persists, follow %3$sthis guide%4$s."
msgstr "Спробуйте натиснути%1$sSave Changes %2$s нижче. Якщо помилка не зникає, перейдіть %3$sв цю інструкцію%4$s."

#: inc/functions/options.php:507
msgid "License validation failed. You may be using a nulled version of the plugin. Please do the following:"
msgstr "Помилка перевірки ліцензії. Можливо, ви використовуєте нульову версію плагіна. Будь ласка, виконайте такі дії:"

#: inc/functions/options.php:507
#: inc/functions/options.php:549
#, php-format
msgid "Login to your WP Rocket %1$saccount%2$s"
msgstr "Увійдіть у свій WP Rocket%1$sаккаунт%2$s"

#: inc/functions/options.php:507
#: inc/functions/options.php:549
msgid "Download the zip file"
msgstr "Завантажте zip файл"

#: inc/functions/options.php:507
#: inc/functions/options.php:549
msgid "Reinstall"
msgstr "Перевстановити"

#: inc/functions/options.php:507
#, php-format
msgid "If you do not have a WP Rocket account, please %1$spurchase a license%2$s."
msgstr "Якщо у вас немає облікового запису WP Rocket, будь ласка,%1$sкупіть ліцензію%2$s."

#: inc/functions/options.php:515
msgid "License validation failed. This user account does not exist in our database."
msgstr "Помилка перевірки ліцензії. Цей обліковий запис користувача не існує в нашій базі даних."

#: inc/functions/options.php:515
msgid "To resolve, please contact support."
msgstr "Щоб вирішити, зверніться до служби підтримки."

#: inc/functions/options.php:523
#, php-format
msgid "Please see %1$sthis guide%2$s for more info."
msgstr "Дивіться %1$sце керівництво%2$s для отримання додаткової інформації."

#: inc/functions/options.php:543
msgid "Your license is not valid."
msgstr "Ваша ліцензія недійсна."

#: inc/functions/options.php:543
#, php-format
msgid "Make sure you have an active %1$sWP Rocket license%2$s."
msgstr "Переконайтеся, що у вас є активна %1$sWP Rocket ліцензія%2$s."

#: inc/functions/options.php:545
msgid "You have added as many sites as your current license allows."
msgstr "Ви додали стільки сайтів, скільки дозволяє поточна ліцензія."

#: inc/functions/options.php:545
#, php-format
msgid "Upgrade your %1$saccount%2$s or %3$stransfer your license%2$s to this domain."
msgstr "Оновлення %1$sаккаунту%2$s або %3$sтрансфер ліцензії%2$s до цього домену."

#: inc/functions/options.php:547
msgid "This website is not allowed."
msgstr "Цей веб-сайт заборонений."

#: inc/functions/options.php:547
#, php-format
msgid "Please %1$scontact support%2$s."
msgstr "Будь ласка,%1$sзверніться в службу підтримки%2$s."

#: inc/functions/options.php:549
msgid "This license key is not recognized."
msgstr "Цей ліцензійний ключ не розпізнається."

#: inc/functions/options.php:549
#, php-format
msgid "If the issue persists, please %1$scontact support%2$s."
msgstr "Якщо проблема не зникне,%1$sзверніться до служби підтримки%2$s."

#: inc/functions/options.php:555
#, php-format
msgid "License validation failed: %s"
msgstr "Помилка перевірки ліцензії: %s"

#: inc/vendors/classes/class-imagify-partner.php:531
msgid "Plugin installed successfully."
msgstr "Плагін встановлений успішно."

#: inc/vendors/classes/class-imagify-partner.php:532
msgid "Sorry, you are not allowed to install plugins on this site."
msgstr "На жаль, вам не дозволяється встановлювати плагіни на цьому сайті."

#: inc/vendors/classes/class-imagify-partner.php:533
msgid "Sorry, you are not allowed to do that."
msgstr "На жаль, вам не дозволено це робити."

#: inc/vendors/classes/class-imagify-partner.php:534
msgid "Plugin install failed."
msgstr "Не вдалося встановити плагін."

#: inc/vendors/classes/class-imagify-partner.php:535
msgid "Go back"
msgstr "Повернутися назад"

#: views/deactivation-intent/form.php:87
msgid "Cancel"
msgstr "Скасувати"

#: views/settings/fields/cache-lifespan.php:26
msgid "Clear cache after"
msgstr "Очистити кеш після"

#: views/settings/fields/cnames.php:58
#: views/settings/fields/cnames.php:92
#: views/settings/fields/cnames.php:122
msgid "CSS & JavaScript"
msgstr "CSS & JavaScript"

#: views/settings/fields/cnames.php:59
#: views/settings/fields/cnames.php:93
#: views/settings/fields/cnames.php:123
msgid "JavaScript"
msgstr "JavaScript"

#: views/settings/fields/cnames.php:60
#: views/settings/fields/cnames.php:94
#: views/settings/fields/cnames.php:124
msgid "CSS"
msgstr "CSS"

#: views/settings/fields/import-form.php:29
msgid "Import settings"
msgstr "Імпорт опції"

#: views/settings/fields/one-click-addon.php:43
#: views/settings/fields/rocket-addon.php:43
msgid "Add-on status"
msgstr "Стан Додатку"

#: views/settings/fields/rocket-addon.php:72
msgid "Modify options"
msgstr "Змінити параметри"

#: views/settings/page-sections/cloudflare.php:27
msgid "Cloudflare Cache"
msgstr "Cloudflare Кеш"

#: views/settings/page-sections/cloudflare.php:35
#: views/settings/page-sections/sucuri.php:36
#, php-format
msgid "Purges cached resources for your website. %s"
msgstr "Очищає кешовані ресурси для вашого веб-сайту. %s"

#: views/settings/page-sections/cloudflare.php:36
msgid "https://support.cloudflare.com/hc/en-us/articles/200169246"
msgstr "https://support.cloudflare.com/hc/en-us/articles/200169246"

#: views/settings/fields/rocket-cdn.php:63
#: views/settings/page-sections/cloudflare.php:36
#: views/settings/page-sections/sucuri.php:37
msgid "Learn more"
msgstr "Дізнатись більше"

#: views/settings/page-sections/cloudflare.php:45
msgid "Clear all Cloudflare cache files"
msgstr "Видалити всі файли Cloudflare кешу"

#: views/settings/page-sections/dashboard.php:38
msgid "Congratulations!"
msgstr "Вітаю!"

#: views/settings/page-sections/dashboard.php:40
msgid "WP Rocket is now activated and already working for you."
msgstr "WP Rocket тепер активована і вже працює для вас."

#: views/settings/page-sections/dashboard.php:42
msgid "Your website should be loading faster now!"
msgstr "Ваш веб-сайт зараз завантажується швидше!"

#: views/settings/page-sections/dashboard.php:44
msgid "We also enable options that provide immediate benefits to your website."
msgstr "Ми також активуємо варіанти, які надають негайні вигоди для вашого веб-сайту."

#: views/settings/page-sections/dashboard.php:45
msgid "Continue to the options to further optimize your site!"
msgstr "Перейдіть до опцій для подальшої оптимізації свого сайту!"

#: views/settings/page-sections/dashboard.php:62
msgid "My Account"
msgstr "Мій аккаунт"

#: views/settings/page-sections/dashboard.php:68
msgid "Refresh info"
msgstr "Оновити інфо"

#: views/settings/page-sections/dashboard.php:100
msgid "Expiration Date"
msgstr "Дата закінчення"

#: views/settings/page-sections/dashboard.php:110
msgid "View my account"
msgstr "Переглянути Мій аккаунт"

#: views/settings/page-sections/dashboard.php:137
msgid "Quick Actions"
msgstr "Швидкі дії"

#: views/settings/page-sections/dashboard.php:144
msgid "Remove all cached files"
msgstr "Видалити всі кешовані файли"

#: views/settings/page-sections/dashboard.php:164
#: views/settings/page-sections/dashboard.php:170
msgid "Regenerate Critical CSS"
msgstr "Перестворити критичні CSS"

#: views/settings/page-sections/dashboard.php:205
msgid "Frequently Asked Questions"
msgstr "Часті Питання"

#: views/settings/page-sections/dashboard.php:219
msgid "Still cannot find a solution?"
msgstr "Ще не можете знайти рішення?"

#: views/settings/page-sections/dashboard.php:220
msgid "Submit a ticket and get help from our friendly and knowledgeable Rocketeers."
msgstr "Надішліть тікет і отримайте допомогу від наших дружніх і обізнаних Rocketeers."

#: views/settings/page-sections/dashboard.php:228
msgid "Ask support"
msgstr "Запитайте підтримку"

#: views/settings/page-sections/database.php:25
msgid "Backup your database before you run a cleanup!"
msgstr "Резервує вашу базу даних перед тим, як запустити очищення!"

#: views/settings/page-sections/database.php:26
msgid "Once a database optimization has been performed, there is no way to undo it."
msgstr "Після того, як оптимізація бази даних була виконана, її неможливо скасувати."

#: views/settings/page-sections/imagify.php:21
#, php-format
msgid "%1$sWP ROCKET%2$s created %3$sIMAGIFY%4$s %1$sfor best-in-class image optimization.%2$s"
msgstr "%1$sWP ROCKET%2$s створено%3$sIMAGIFY%4$s %1$sдля найкращої оптимізації зображень.%2$s"

#: views/settings/page-sections/imagify.php:24
msgid "Compress image to make your website faster, all while maintaining image quality."
msgstr "Стисніть зображення, щоб зробити ваш веб-сайт швидшим, зберігаючи якість зображення."

#: views/settings/page-sections/imagify.php:25
msgid "More on Imagify:"
msgstr "Більше про Imagify:"

#: views/settings/page-sections/imagify.php:27
msgid "Imagify Plugin Page"
msgstr "Imagify сторінка плагіна"

#: views/settings/page-sections/imagify.php:28
msgid "Imagify Website"
msgstr "Imagify вебсайт"

#: views/settings/page-sections/imagify.php:29
msgid "Review of Image Compression Plugins"
msgstr "Огляд плагінів Стиснення зображень"

#: views/settings/page-sections/imagify.php:38
msgid "Install Imagify"
msgstr "Встановити Imagify"

#: views/settings/page-sections/license.php:22
msgid "WP Rocket was not able to automatically validate your license."
msgstr "WP Rocket не зміг автоматично перевірити вашу ліцензію."

#: views/settings/page-sections/license.php:34
msgid "https://docs.wp-rocket.me/article/100-resolving-problems-with-license-validation/?utm_source=wp_plugin&utm_medium=wp_rocket"
msgstr "https://docs.wp-rocket.me/article/100-resolving-problems-with-license-validation/?utm_source=wp_plugin&utm_medium=wp_rocket"

#: views/settings/page-sections/sucuri.php:46
msgid "Clear all Sucuri cache files"
msgstr "Видалити всі файли Sucuri кешу"

#: views/settings/page-sections/tools.php:20
#, php-format
msgid "Files size: %1$s. Number of entries: %2$s."
msgstr "Розмір файлів: %1$s. Кількість записів: %2$s."

#: views/settings/page-sections/tools.php:23
#, php-format
msgid "%1$sDownload the file%2$s."
msgstr "%1$sЗавантажити файл%2$s."

#: views/settings/page-sections/tools.php:26
#, php-format
msgid "%1$sDelete the file%2$s."
msgstr "%1$sВидалити файл%2$s."

#: views/settings/page-sections/tools.php:37
msgid "Export settings"
msgstr "Експортувати налаштування"

#: views/settings/page-sections/tools.php:38
msgid "Download a backup file of your settings"
msgstr "Завантажте резервну копію Ваших налаштувань"

#: views/settings/page-sections/tools.php:46
msgid "Download settings"
msgstr "Завантажити налаштування"

#: views/settings/page-sections/tools.php:60
msgid "Rollback"
msgstr "Відкат"

#: views/settings/page-sections/tools.php:64
#, php-format
msgid "Has version %s caused an issue on your website?"
msgstr "Чи є проблема з версією %s на вашому веб-сайті?"

#: views/settings/page-sections/tools.php:80
#, php-format
msgid "Reinstall version %s"
msgstr "Перевстановити версію %s"

#: views/settings/page-sections/tools.php:106
msgid "Debug mode"
msgstr "Режим налагодження"

#: views/settings/page-sections/tools.php:111
msgid "Create a debug log file."
msgstr "Створіть файл журналу налагодження."

#: views/settings/page-sections/tutorials.php:13
#: views/settings/partials/getting-started.php:18
msgid "Getting Started"
msgstr "Розпочнемо"

#: views/settings/page-sections/tutorials.php:15
msgid "Getting Started with WP Rocket"
msgstr "Розпочнемо з WP Rocket"

#: views/settings/page-sections/tutorials.php:16
#: views/settings/partials/getting-started.php:12
msgid "Finding the Best Settings for Your Site"
msgstr "Пошук найкращих налаштувань для Вашого сайту"

#: views/settings/page-sections/tutorials.php:17
#: views/settings/partials/getting-started.php:13
msgid "How to Check if WP Rocket is Caching Your Site"
msgstr "Як перевірити, чи WP Rocket кешує ваш сайт"

#: views/settings/page-sections/tutorials.php:18
#: views/settings/partials/getting-started.php:14
msgid "How to Measure the Speed of Your Site"
msgstr "Як виміряти швидкість вашого сайту"

#: views/settings/page-sections/tutorials.php:33
msgid "Troubleshooting Display Issues with File Optimization"
msgstr "Вирішення проблем з відображенням через Оптимізацію файлів"

#: views/settings/page-sections/tutorials.php:34
msgid "How to Find the Right JavaScript to Exclude"
msgstr "Як знайти правильний JavaScript для виключення"

#: views/settings/page-sections/tutorials.php:35
msgid "How External Content Slows Your Site"
msgstr "Як зовнішній контент уповільнює Ваш сайт"

#: views/settings/page-sections/tutorials.php:19
msgid "How Preloading Works"
msgstr "Як працює Предзавантаження"

#: views/settings/page-sections/tutorials.php:41
msgid "Set Up the Cloudflare Add-on"
msgstr "Налаштувати Додаток Cloudflare"

#: views/settings/page.php:16
msgid "WP Rocket Settings"
msgstr "WP Rocket Налаштування"

#: views/settings/page.php:30
#, php-format
msgid "version %s"
msgstr "версія %s"

#: views/settings/page.php:60
msgid "Show Sidebar"
msgstr "Показати сайдбар"

#: views/settings/page.php:87
msgid "WP Rocket will never transmit any domain names or email addresses (except for license validation), IP addresses, or third-party API keys."
msgstr "WP Rocket ніколи не буде передавати будь-які доменні імена або адреси електронної пошти (крім перевірки ліцензії), IP-адреси або ключі API сторонніх виробників."

#: views/settings/page.php:89
msgid "Activate Rocket analytics"
msgstr "Активувати Rocket analytics"

#: views/settings/partials/documentation.php:15
msgid "It is a great starting point to fix some of the most common issues."
msgstr "Це чудова відправна точка для виправлення деяких найбільш поширених проблем."

#: views/settings/partials/documentation.php:22
msgid "Read the documentation"
msgstr "Прочитайте документацію"

#: views/settings/partials/getting-started.php:11
msgid "What WP Rocket Does For You By Default"
msgstr "Що WP Rocket для вас робить одразу після активації:"

#: views/settings/partials/sidebar.php:12
msgid "How to correctly measure your website’s loading time"
msgstr "Як правильно виміряти час завантаження веб-сайту"

#: views/settings/partials/sidebar.php:14
msgid "Check our tutorial and learn how to measure the speed of your site."
msgstr "Перегляньте наш підручник і дізнайтеся, як виміряти швидкість вашого сайту."

#: views/settings/partials/sidebar.php:15
#: views/settings/partials/sidebar.php:24
msgid "Read our guide"
msgstr "Прочитайте наш гід"

#: views/settings/partials/sidebar.php:28
msgid "Read more"
msgstr "Читати далі"

#: views/settings/partials/sidebar.php:33
msgid "You have not activated logged-in user cache."
msgstr "Ви не активували кеш для зареєстрованих користувачів."

#: views/settings/partials/sidebar.php:34
msgid "Use a private browser to check your website's speed and visual appearance."
msgstr "Використовуйте приватний веб-переглядач, щоб перевірити швидкість і візуальність вашої веб-сторінки."

#: views/settings/sections/addons-container.php:24
#: views/settings/sections/fields-container.php:28
msgid "Need Help?"
msgstr "Потрібна допомога?"

#: inc/Addon/Cloudflare/API/Client.php:129
msgid "Cloudflare did not provide any reply. Please try again later."
msgstr ""

#: inc/Addon/Cloudflare/Auth/APIKey.php:61
#, php-format
msgid "Cloudflare email and/or API key are not set. Read the %1$sdocumentation%2$s for further guidance."
msgstr ""

#: inc/Addon/Cloudflare/Cloudflare.php:205
msgid "days"
msgstr ""

#: inc/Addon/Cloudflare/Cloudflare.php:208
msgid "seconds"
msgstr ""

#: inc/Addon/Cloudflare/Cloudflare.php:210
msgid "minutes"
msgstr ""

#: inc/Addon/Cloudflare/Cloudflare.php:212
msgid "hours"
msgstr ""

#: inc/Addon/Cloudflare/Subscriber.php:224
#: inc/Addon/Cloudflare/Subscriber.php:253
#, php-format
msgid "%1$sWP Rocket:%2$s %3$s"
msgstr ""

#: inc/Addon/Cloudflare/Subscriber.php:242
#, php-format
msgid "%1$sWP Rocket:%2$s Cloudflare cache successfully purged."
msgstr ""

#: inc/Addon/Cloudflare/Subscriber.php:328
msgctxt "Cloudflare caching level"
msgid "standard"
msgstr ""

#: inc/Addon/Cloudflare/Subscriber.php:403
#, php-format
msgid "Cloudflare browser cache set to %s"
msgstr ""

#: inc/Addon/Cloudflare/Subscriber.php:512
#, php-format
msgid "%1$sWP Rocket:%2$s Optimal settings activated for Cloudflare:"
msgstr ""

#: inc/Addon/Cloudflare/Subscriber.php:521
#, php-format
msgid "%1$sWP Rocket:%2$s Optimal settings deactivated for Cloudflare, reverted to previous settings:"
msgstr ""

#: inc/Addon/WebP/AdminSubscriber.php:93
#, php-format
msgid "You are using %1$s to serve WebP images so you do not need to enable this option. %2$sMore info%3$s %4$s If you prefer to have WP Rocket serve WebP for you instead, please disable WebP display in %1$s."
msgid_plural "You are using %1$s to serve WebP images so you do not need to enable this option. %2$sMore info%3$s %4$s If you prefer to have WP Rocket serve WebP for you instead, please disable WebP display in %1$s."
msgstr[0] ""
msgstr[1] ""
msgstr[2] ""
msgstr[3] ""

#: inc/Addon/WebP/AdminSubscriber.php:173
#, php-format
msgid "%5$sWe have not detected any compatible WebP plugin!%6$s%4$s If you don’t already have WebP images on your site consider using %3$sImagify%2$s or another supported plugin. %1$sMore info%2$s %4$s If you are not using WebP do not enable this option."
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:642
msgid "If you have problems after activating this option, copy and paste the default exclusions to quickly resolve issues:"
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:646
#, php-format
msgid "Also, please check our %1$sdocumentation%2$s for a list of compatibility exclusions."
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:651
msgid "Internal scripts are excluded by default to prevent issues. Remove them to take full advantage of this option."
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:654
#, php-format
msgid "If this causes trouble, restore the default exclusions, found %1$shere%2$s"
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:687
msgid "For compatibility and best results, this option is disabled when Remove unused CSS is enabled."
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:710
msgid "<strong>Internal:</strong> The domain part of the URL will be stripped automatically. Use (.*).css wildcards to exclude all CSS files located at a specific path."
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:712
#, php-format
msgid "<strong>3rd Party:</strong> Use either the full URL path or only the domain name, to exclude external CSS. %1$sMore info%2$s"
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:730
msgid "Optimize CSS delivery eliminates render-blocking CSS on your website. Only one method can be selected. Remove Unused CSS is recommended for optimal performance, but limited only to the users with active license."
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:730
msgid "Optimize CSS delivery eliminates render-blocking CSS on your website. Only one method can be selected. Remove Unused CSS is recommended for optimal performance."
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:740
#, php-format
msgid "Optimize CSS Delivery features are disabled on local environments. %1$sLearn more%2$s"
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:760
#: inc/admin/ui/meta-boxes.php:106
msgid "Remove Unused CSS"
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:763
#, php-format
msgid "Removes unused CSS per page and helps to reduce page size and HTTP requests. Recommended for best performance. Test thoroughly! %1$sMore info%2$s"
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:767
msgid "Activate Remove Unused CSS"
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:773
msgid "CSS safelist"
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:774
msgid "Specify CSS filenames, IDs or classes that should not be removed (one per line)."
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:789
#: inc/admin/ui/meta-boxes.php:109
msgid "Load CSS asynchronously"
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:792
#, php-format
msgctxt "WP Critical CSS compatibility"
msgid "Load CSS asynchronously is currently handled by the %1$s plugin. If you want to use WP Rocket’s load CSS asynchronously option, disable the %1$s plugin."
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:794
#, php-format
msgid "Generates critical path CSS and loads CSS asynchronously. %1$sMore info%2$s"
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:841
msgid "For compatibility and best results, this option is disabled when delay javascript execution is enabled."
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:915
#, php-format
msgid "Specify URLs or keywords of JavaScript files to be excluded from defer (one per line). %1$sMore info%2$s"
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:931
#: inc/admin/ui/meta-boxes.php:111
msgid "Delay JavaScript execution"
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:933
#, php-format
msgid "Improves performance by delaying the loading of JavaScript files until user interaction (e.g. scroll, click). %1$sMore info%2$s"
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:942
msgid "One-click exclusions"
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:943
msgid "When using the Delay JavaScript Execution, you might experience delay loading elements located in the viewport that need to appear immediately - e.g. slider, header, menu."
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:944
msgid "If you need instant visibility, click below on files that should NOT be delayed. This selection will help users interact with the elements straight away."
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:961
msgid "Specify URLs or keywords that can identify inline or JavaScript files to be excluded from delaying execution (one per line)."
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:994
msgid "LazyLoad, image dimensions"
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:1058
#, php-format
msgid "LazyLoad is currently activated in %2$s. If you want to use WP Rocket’s LazyLoad, disable this option in %2$s."
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:1061
msgid "Image Dimensions"
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:1064
#, php-format
msgid "Add missing width and height attributes to images. Helps prevent layout shifts and improve the reading experience for your visitors. %1$sMore info%2$s"
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:1095
#, php-format
msgid "LazyLoad for images is currently activated in %2$s. If you want to use %1$s’s LazyLoad, disable this option in %2$s."
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:1120
#, php-format
msgid "Replace YouTube iframe with preview image is not compatible with %2$s."
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:1135
msgid "Excluded images or iframes"
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:1137
#, php-format
msgid "Specify keywords (e.g. image filename, CSS class, domain) from the image or iframe code to be excluded (one per line). %1$sMore info%2$s"
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:1145
msgid "Add missing image dimensions"
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:1165
msgid "Generate cache files, preload fonts"
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:1177
msgid "Preload Cache"
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:1180
msgid "When you enable preloading WP Rocket will automatically detect your sitemaps and save all URLs to the database. The plugin will make sure that your cache is always preloaded."
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:1188
msgid "Preload Links"
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:1191
#, php-format
msgid "Link preloading improves the perceived load time by downloading a page when a user hovers over the link. %1$sMore info%2$s"
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:1206
msgid "Preload Fonts"
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:1209
#, php-format
msgid "Improves performance by helping browsers discover fonts in CSS files. %1$sMore info%2$s"
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:1234
msgid "Exclude URLs"
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:1239
#, php-format
msgid "Specify URLs to be excluded from the preload feature (one per line). %1$sMore info%2$s"
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:1260
msgid "Fonts to preload"
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:1261
msgid "Specify urls of the font files to be preloaded (one per line). Fonts must be hosted on your own domain, or the domain you have specified on the CDN tab."
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:1262
msgid "The domain part of the URL will be stripped automatically.<br/>Allowed font extensions: otf, ttf, svg, woff, woff2."
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:1271
msgid "Enable link preloading"
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:1379
msgid "Specify full or partial IDs of cookies that, when set in the visitor's browser, should prevent a page from getting cached (one per line)"
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:1464
msgid "Automatic Cleanup"
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:1642
msgid "%1$s%2$l Add-on%3$s is currently enabled. Configuration of the CDN settings is not required for %2$l to work on your site."
msgid_plural "%1$s%2$l Add-ons%3$s are currently enabled. Configuration of the CDN settings is not required for %2$l to work on your site."
msgstr[0] ""
msgstr[1] ""
msgstr[2] ""
msgstr[3] ""

#: inc/Engine/Admin/Settings/Page.php:1827
#, php-format
msgid "%1$sPlanning on using Automatic Platform Optimization (APO)?%2$s Just activate the official Cloudflare plugin and configure it. WP Rocket will automatically enable compatibility."
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:1918
msgid "WebP Compatibility"
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:1924
msgid "Improve browser compatibility for WebP images."
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:2129
msgctxt "Sucuri"
msgid "Firewall API key (for plugin), must be in format {32 characters}/{32 characters}:"
msgstr ""

#: inc/Engine/Admin/Settings/Settings.php:668
msgid "Sorry! Adding /(.*) in Advanced Rules > Never Cache URL(s) was not saved because it disables caching and optimizations for every page on your site."
msgstr ""

#: inc/Engine/CDN/RocketCDN/APIClient.php:134
msgid "We could not fetch the current price because RocketCDN API returned an unexpected error code."
msgstr ""

#: inc/Engine/CDN/RocketCDN/APIClient.php:140
msgid "RocketCDN is not available at the moment. Please retry later."
msgstr ""

#: inc/Engine/CDN/RocketCDN/APIClient.php:177
msgid "RocketCDN cache purge failed: Missing identifier parameter."
msgstr ""

#: inc/Engine/CDN/RocketCDN/APIClient.php:186
msgid "RocketCDN cache purge failed: Missing user token."
msgstr ""

#: inc/Engine/CDN/RocketCDN/APIClient.php:212
msgid "RocketCDN cache purge failed: The API returned an unexpected response code."
msgstr ""

#: inc/Engine/CDN/RocketCDN/APIClient.php:221
msgid "RocketCDN cache purge failed: The API returned an empty response."
msgstr ""

#: inc/Engine/CDN/RocketCDN/APIClient.php:230
msgid "RocketCDN cache purge failed: The API returned an unexpected response."
msgstr ""

#: inc/Engine/CDN/RocketCDN/APIClient.php:239
#, php-format
msgid "RocketCDN cache purge failed: %s."
msgstr ""

#: inc/Engine/CDN/RocketCDN/APIClient.php:247
msgid "RocketCDN cache purge successful."
msgstr ""

#: inc/Engine/CDN/RocketCDN/AdminPageSubscriber.php:90
msgid "Next Billing Date"
msgstr ""

#: inc/Engine/CDN/RocketCDN/AdminPageSubscriber.php:99
msgid "No Subscription"
msgstr ""

#: inc/Engine/CDN/RocketCDN/AdminPageSubscriber.php:135
msgid "Your RocketCDN subscription is currently active."
msgstr ""

#: inc/Engine/CDN/RocketCDN/AdminPageSubscriber.php:141
#, php-format
msgid "To use RocketCDN, replace your CNAME with %1$s%2$s%3$s."
msgstr ""

#: inc/Engine/CDN/RocketCDN/AdminPageSubscriber.php:152
#: inc/Engine/CDN/RocketCDN/NoticesSubscriber.php:230
#: inc/Engine/CDN/RocketCDN/NoticesSubscriber.php:334
#, php-format
msgid "%1$sMore Info%2$s"
msgstr ""

#: inc/Engine/CDN/RocketCDN/RESTSubscriber.php:137
msgid "RocketCDN enabled"
msgstr ""

#: inc/Engine/CDN/RocketCDN/RESTSubscriber.php:160
msgid "RocketCDN disabled"
msgstr ""

#: inc/Engine/CDN/RocketCDN/views/cta-big.php:27
#, php-format
msgid "Valid until %s only!"
msgstr ""

#: inc/Engine/CDN/RocketCDN/views/cta-big.php:36
msgid "Speed up your website thanks to:"
msgstr ""

#: inc/Engine/CDN/RocketCDN/views/cta-big.php:42
#, php-format
msgid "High performance Content Delivery Network (CDN) with %1$sunlimited bandwidth%2$s"
msgstr ""

#: inc/Engine/CDN/RocketCDN/views/cta-big.php:48
#, php-format
msgid "Easy configuration: the %1$sbest CDN settings%2$s are automatically applied"
msgstr ""

#: inc/Engine/CDN/RocketCDN/views/cta-big.php:54
#, php-format
msgid "WP Rocket integration: the CDN option is %1$sautomatically configured%2$s in our plugin"
msgstr ""

#: inc/Engine/CDN/RocketCDN/views/cta-big.php:58
msgid "Learn more about RocketCDN"
msgstr ""

#: inc/Engine/CDN/RocketCDN/views/cta-big.php:65
#, php-format
msgid "*$%1$s/month for 12 months then $%2$s/month. You can cancel your subscription at any time."
msgstr ""

#: inc/Engine/CDN/RocketCDN/views/cta-big.php:86
msgid "Billed monthly"
msgstr ""

#: inc/Engine/CDN/RocketCDN/views/cta-big.php:87
msgid "Get Started"
msgstr ""

#: inc/Engine/CDN/RocketCDN/views/cta-big.php:92
msgid "Reduce this banner"
msgstr ""

#: inc/Engine/CDN/RocketCDN/views/cta-small.php:17
msgid "Speed up your website with RocketCDN, WP Rocket’s Content Delivery Network."
msgstr ""

#: inc/Engine/CDN/RocketCDN/views/cta-small.php:20
#: inc/Engine/CDN/RocketCDN/views/promote-notice.php:13
msgid "Learn More"
msgstr ""

#: inc/Engine/CDN/RocketCDN/views/dashboard-status.php:23
msgid "RocketCDN is unavailable on local domains and staging sites."
msgstr ""

#: inc/Engine/CDN/RocketCDN/views/dashboard-status.php:32
msgid "Get RocketCDN"
msgstr ""

#: inc/Engine/CDN/RocketCDN/views/promote-notice.php:11
msgid "New!"
msgstr ""

#: inc/Engine/CDN/RocketCDN/views/promote-notice.php:12
msgid "Speed up your website with RocketCDN, WP Rocket’s Content Delivery Network!"
msgstr ""

#: inc/Engine/Cache/WPCache.php:337
msgid "WP_CACHE value"
msgstr ""

#: inc/Engine/Cache/WPCache.php:358
msgid "The WP_CACHE constant needs to be set to true for WP Rocket cache to work properly"
msgstr ""

#: inc/Engine/Cache/WPCache.php:367
msgid "WP_CACHE is set to true"
msgstr ""

#: inc/Engine/Cache/WPCache.php:375
msgid "WP_CACHE is not set"
msgstr ""

#: inc/Engine/Cache/WPCache.php:383
msgid "WP_CACHE is set to false"
msgstr ""

#: inc/Engine/Common/Queue/RUCSSQueueRunner.php:253
msgid "Every minute"
msgstr ""

#: inc/Engine/CriticalPath/APIClient.php:170
#, php-format
msgid "Critical CSS for %1$s on mobile not generated. Error: The API returned an empty response."
msgstr ""

#: inc/Engine/CriticalPath/APIClient.php:173
#, php-format
msgid "Critical CSS for %1$s not generated. Error: The API returned an empty response."
msgstr ""

#: inc/Engine/CriticalPath/APIClient.php:185
#, php-format
msgid "Critical CSS for %1$s on mobile not generated."
msgstr ""

#: inc/Engine/CriticalPath/APIClient.php:187
#: inc/Engine/CriticalPath/ProcessorService.php:194
#, php-format
msgid "Critical CSS for %1$s not generated."
msgstr ""

#: inc/Engine/CriticalPath/APIClient.php:195
#, php-format
msgid "Critical CSS for %1$s on mobile not generated. Error: The API returned an invalid response code."
msgstr ""

#: inc/Engine/CriticalPath/APIClient.php:197
#, php-format
msgid "Critical CSS for %1$s not generated. Error: The API returned an invalid response code."
msgstr ""

#: inc/Engine/CriticalPath/APIClient.php:205
#: inc/Engine/CriticalPath/ProcessorService.php:201
#, php-format
msgid "Error: %1$s"
msgstr ""

#: inc/Engine/CriticalPath/Admin/Post.php:144
#: views/cpcss/metabox/generate.php:47
msgid "Generate Specific CPCSS"
msgstr ""

#: inc/Engine/CriticalPath/Admin/Post.php:145
#: views/cpcss/metabox/generate.php:45
msgid "Regenerate specific CPCSS"
msgstr ""

#: inc/Engine/CriticalPath/Admin/Post.php:216
msgid "This feature is not available for non-public post types."
msgstr ""

#: inc/Engine/CriticalPath/Admin/Post.php:219
msgid "%l to use this feature."
msgstr ""

#: inc/Engine/CriticalPath/Admin/Post.php:222
#, php-format
msgid "Publish the %s"
msgstr ""

#: inc/Engine/CriticalPath/Admin/Post.php:223
msgid "Enable Load CSS asynchronously in WP Rocket settings"
msgstr ""

#: inc/Engine/CriticalPath/Admin/Post.php:224
msgid "Enable Load CSS asynchronously in the options above"
msgstr ""

#: inc/Engine/CriticalPath/CriticalCSSSubscriber.php:840
#, php-format
msgid "We highly recommend the %1$supdated Remove Unused CSS%2$s for a better CSS optimization. Load CSS Asynchronously is always available as a back-up."
msgstr ""

#: inc/Engine/CriticalPath/CriticalCSSSubscriber.php:845
msgid "Stay with the old option"
msgstr ""

#: inc/Engine/CriticalPath/DataManager.php:68
#, php-format
msgid "Critical CSS for %1$s on mobile not generated. Error: The destination folder could not be created."
msgstr ""

#: inc/Engine/CriticalPath/DataManager.php:71
#, php-format
msgid "Critical CSS for %1$s not generated. Error: The destination folder could not be created."
msgstr ""

#: inc/Engine/CriticalPath/DataManager.php:106
msgid "Critical CSS file for mobile does not exist"
msgstr ""

#: inc/Engine/CriticalPath/DataManager.php:108
msgid "Critical CSS file does not exist"
msgstr ""

#: inc/Engine/CriticalPath/DataManager.php:120
msgid "Critical CSS file for mobile cannot be deleted"
msgstr ""

#: inc/Engine/CriticalPath/DataManager.php:122
msgid "Critical CSS file cannot be deleted"
msgstr ""

#: inc/Engine/CriticalPath/ProcessorService.php:187
#, php-format
msgid "Mobile Critical CSS for %1$s not generated."
msgstr ""

#: inc/Engine/CriticalPath/ProcessorService.php:228
#, php-format
msgid "Critical CSS for %s in progress."
msgstr ""

#: inc/Engine/CriticalPath/ProcessorService.php:262
#, php-format
msgid "Mobile Critical CSS for %s generated."
msgstr ""

#: inc/Engine/CriticalPath/ProcessorService.php:295
msgid "Critical CSS file deleted successfully."
msgstr ""

#: inc/Engine/CriticalPath/ProcessorService.php:317
#, php-format
msgid "Mobile Critical CSS for %1$s timeout. Please retry a little later."
msgstr ""

#: inc/Engine/CriticalPath/ProcessorService.php:330
#, php-format
msgid "Critical CSS for %1$s timeout. Please retry a little later."
msgstr ""

#: inc/Engine/CriticalPath/RESTWP.php:141
msgid "Mobile CPCSS generation not enabled."
msgstr ""

#: inc/Engine/CriticalPath/RESTWPPost.php:36
#: inc/Engine/CriticalPath/RESTWPPost.php:69
msgid "Requested post does not exist."
msgstr ""

#: inc/Engine/CriticalPath/RESTWPPost.php:46
msgid "Cannot generate CPCSS for unpublished post."
msgstr ""

#: inc/Engine/HealthCheck/HealthCheck.php:142
msgid "Scheduled Cache Purge"
msgstr ""

#: inc/Engine/HealthCheck/HealthCheck.php:143
msgid "Scheduled Database Optimization"
msgstr ""

#: inc/Engine/HealthCheck/HealthCheck.php:144
msgid "Database Optimization Process"
msgstr ""

#: inc/Engine/HealthCheck/HealthCheck.php:145
msgctxt "noun"
msgid "Preload"
msgstr ""

#: inc/Engine/HealthCheck/HealthCheck.php:146
msgid "Critical Path CSS Generation Process"
msgstr ""

#: inc/Engine/License/Renewal.php:76
#, php-format
msgid "Renew before it is too late, you will only pay %1$s%2$s%3$s!"
msgstr ""

#: inc/Engine/License/Renewal.php:85
#, php-format
msgid "Renew with a %1$s%2$s discount%3$s before it is too late, you will only pay %4$s%5$s%6$s!"
msgstr ""

#: inc/Engine/License/Renewal.php:139
#, php-format
msgid "Renew your license for 1 year now at %1$s%3$s%2$s."
msgstr ""

#: inc/Engine/License/Renewal.php:152
#, php-format
msgid "Renew your license for 1 year now and get %1$s%3$s OFF%2$s immediately: you will only pay %1$s%4$s%2$s!"
msgstr ""

#: inc/Engine/License/Renewal.php:218
#, php-format
msgid "Renew before it is too late, you will pay %1$s%3$s%2$s."
msgstr ""

#: inc/Engine/License/Renewal.php:227
#, php-format
msgid "Renew with a %1$s%2$s discount%3$s before it is too late, you will only pay %1$s%4$s%3$s!"
msgstr ""

#: inc/Engine/License/Renewal.php:546
#, php-format
msgid "You need a valid license to continue using this feature. %1$sRenew now%2$s before losing access."
msgstr ""

#: inc/Engine/License/Renewal.php:567
#, php-format
msgid "You need an active license to enable this option. %1$sRenew now%2$s."
msgstr ""

#: inc/Engine/License/Renewal.php:595
#, php-format
msgid "You need an active license to enable this option. %1$sMore info%2$s."
msgstr ""

#: inc/Engine/License/Upgrade.php:252
#, php-format
msgid "Take advantage of %1$s to speed up more websites:%2$s get a %3$s%4$s off%5$s for %3$supgrading your license to Plus or Infinite!%5$s"
msgid_plural "Take advantage of %1$s to speed up more websites:%2$s get a %3$s%4$s off%5$s for %3$supgrading your license to Infinite!%5$s"
msgstr[0] ""
msgstr[1] ""
msgstr[2] ""
msgstr[3] ""

#: inc/Engine/License/Upgrade.php:382
#: inc/Engine/License/Upgrade.php:405
msgid "Unlimited"
msgstr ""

#: inc/Engine/License/views/promo-banner.php:16
#, php-format
msgid "%s off"
msgstr ""

#: inc/Engine/License/views/promo-banner.php:21
#, php-format
msgid "%s promotion is live!"
msgstr ""

#: inc/Engine/License/views/promo-banner.php:27
msgid "Hurry Up! Deal ends in:"
msgstr ""

#: inc/Engine/License/views/promo-banner.php:32
#: inc/Engine/License/views/renewal-soon-banner.php:15
msgid "Seconds"
msgstr ""

#: inc/Engine/License/views/promo-banner.php:34
#: inc/Engine/License/views/upgrade-section.php:11
msgid "Upgrade now"
msgstr ""

#: inc/Engine/License/views/promo-banner.php:36
#: inc/Engine/License/views/renewal-expired-banner-ocd-disabled.php:32
#: inc/Engine/License/views/renewal-expired-banner-ocd.php:42
#: inc/Engine/License/views/renewal-expired-banner.php:30
#: inc/admin/ui/notices.php:739
#: views/settings/page-sections/dashboard.php:46
msgid "Dismiss this notice"
msgstr ""

#: inc/Engine/License/views/renewal-expired-banner-ocd-disabled.php:11
msgid "The Optimize CSS Delivery feature is disabled."
msgstr ""

#: inc/Engine/License/views/renewal-expired-banner-ocd-disabled.php:15
msgid "You can no longer use the Remove Unused CSS or Load CSS asynchronously options."
msgstr ""

#: inc/Engine/License/views/renewal-expired-banner-ocd-disabled.php:20
#, php-format
msgid "You need an %1$sactive license%2$s to keep optimizing your CSS delivery, which addresses a PageSpeed Insights recommendation and improves your page performance."
msgstr ""

#: inc/Engine/License/views/renewal-expired-banner-ocd-disabled.php:29
#: inc/Engine/License/views/renewal-expired-banner-ocd.php:39
#: inc/Engine/License/views/renewal-expired-banner.php:27
#: inc/Engine/License/views/renewal-soon-banner.php:31
msgid "Renew now"
msgstr ""

#: inc/Engine/License/views/renewal-expired-banner-ocd.php:11
msgid "You will soon lose access to some features"
msgstr ""

#: inc/Engine/License/views/renewal-expired-banner-ocd.php:18
#, php-format
msgid "You need an %1$sactive license to continue optimizing your CSS delivery%2$s."
msgstr ""

#: inc/Engine/License/views/renewal-expired-banner-ocd.php:24
msgid "The Remove Unused CSS and Load CSS asynchronously features are great options to address the PageSpeed Insights recommendations and improve your website performance."
msgstr ""

#: inc/Engine/License/views/renewal-expired-banner-ocd.php:29
#, php-format
msgid "They will be %1$sautomatically disabled on %3$s%2$s."
msgstr ""

#: inc/Engine/License/views/renewal-expired-banner.php:11
msgid "Your WP Rocket license is expired!"
msgstr ""

#: inc/Engine/License/views/renewal-expired-banner.php:18
#, php-format
msgid "Your website could be much faster if it could take advantage of our %1$snew features and enhancements%2$s. 🚀"
msgstr ""

#: inc/Engine/License/views/renewal-soon-banner.php:22
#, php-format
msgid "Your %1$sWP Rocket license is about to expire%2$s: you will soon lose access to product updates and support."
msgstr ""

#: inc/Engine/License/views/upgrade-popin.php:12
msgid "Speed Up More Websites"
msgstr ""

#: inc/Engine/License/views/upgrade-popin.php:19
#, php-format
msgid "You can use WP Rocket on more websites by upgrading your license. To upgrade, simply pay the %1$sprice difference%2$s between your current and new licenses, as shown below."
msgstr ""

#: inc/Engine/License/views/upgrade-popin.php:25
#, php-format
msgid "%1$sN.B.%2$s: Upgrading your license does not change your expiration date"
msgstr ""

#: inc/Engine/License/views/upgrade-popin.php:35
#, php-format
msgid "Save $%s"
msgstr ""

#: inc/Engine/License/views/upgrade-popin.php:48
#, php-format
msgid "%s websites"
msgstr ""

#: inc/Engine/License/views/upgrade-popin.php:54
#, php-format
msgid "Upgrade to %s"
msgstr ""

#: inc/Engine/License/views/upgrade-section.php:11
msgid "You can use WP Rocket on more websites by upgrading your license (you will only pay the price difference between your current and new licenses)."
msgstr ""

#: inc/Engine/Optimization/DelayJS/Admin/SiteList.php:195
msgid "Analytics & Ads"
msgstr ""

#: inc/Engine/Optimization/DelayJS/Admin/SiteList.php:200
msgid "Plugins"
msgstr ""

#: inc/Engine/Optimization/DelayJS/Admin/SiteList.php:205
msgid "Themes"
msgstr ""

#: inc/Engine/Optimization/DynamicLists/DynamicLists.php:101
msgid "You need an active license to get the latest version of the lists from our server."
msgstr ""

#: inc/Engine/Optimization/DynamicLists/DynamicLists.php:115
msgid "Could not get updated lists from server."
msgstr ""

#: inc/Engine/Optimization/DynamicLists/DynamicLists.php:124
msgid "Lists are up to date."
msgstr ""

#: inc/Engine/Optimization/DynamicLists/DynamicLists.php:133
msgid "Could not update lists."
msgstr ""

#: inc/Engine/Optimization/DynamicLists/DynamicLists.php:142
msgid "Lists are successfully updated."
msgstr ""

#: inc/Engine/Optimization/DynamicLists/ServiceProvider.php:52
msgid "Default Lists"
msgstr ""

#: inc/Engine/Optimization/DynamicLists/ServiceProvider.php:58
msgid "Delay JavaScript Execution Exclusion Lists"
msgstr ""

#: inc/Engine/Optimization/RUCSS/Admin/Settings.php:131
#: views/settings/page-sections/dashboard.php:188
msgid "Clear Used CSS"
msgstr ""

#: inc/Engine/Optimization/RUCSS/Admin/Settings.php:219
#, php-format
msgid "%1$s: Please wait %2$s seconds. The Remove Unused CSS service is processing your pages."
msgstr ""

#: inc/Engine/Optimization/RUCSS/Admin/Settings.php:264
#, php-format
msgid "%1$s: The Used CSS of your homepage has been processed. WP Rocket will continue to generate Used CSS for up to %2$s URLs per %3$s second(s)."
msgstr ""

#: inc/Engine/Optimization/RUCSS/Admin/Settings.php:273
#, php-format
msgid "We suggest enabling %1$sPreload%2$s for the fastest results."
msgstr ""

#: inc/Engine/Optimization/RUCSS/Admin/Settings.php:283
#, php-format
msgid "To learn more about the process check our %1$sdocumentation%2$s."
msgstr ""

#: inc/Engine/Optimization/RUCSS/Admin/Settings.php:481
msgid "We couldn't generate the used CSS because you're using a nulled version of WP Rocket. You need an active license to use the Remove Unused CSS feature and further improve your website's performance."
msgstr ""

#: inc/Engine/Optimization/RUCSS/Admin/Settings.php:484
#, php-format
msgid "Click here to get a WP Rocket single license at %1$s off!"
msgstr ""

#: inc/Engine/Optimization/RUCSS/Admin/Settings.php:521
#, php-format
msgid "Could not create the %2$s table in the database which is necessary for the Remove Unused CSS feature to work. Please reach out to <a href=\"%3$s\">our support</a>."
msgstr ""

#: inc/Engine/Optimization/RUCSS/Admin/Subscriber.php:322
#, php-format
msgid "%1$s: Used CSS option is not enabled!"
msgstr ""

#: inc/Engine/Optimization/RUCSS/Admin/Subscriber.php:343
#, php-format
msgid "%1$s: Used CSS cache cleared!"
msgstr ""

#: inc/Engine/Optimization/RUCSS/Controller/UsedCSS.php:776
msgid "Clear Used CSS of this URL"
msgstr ""

#: inc/Engine/Optimization/RUCSS/Cron/Subscriber.php:160
msgid "WP Rocket Remove Unused CSS pending jobs"
msgstr ""

#: inc/Engine/Optimization/RUCSS/Cron/Subscriber.php:174
msgid "WP Rocket clear Remove Unused CSS failed jobs"
msgstr ""

#: inc/Engine/Preload/Admin/Settings.php:57
#, php-format
msgid "%1$s: The preload service is now active. After the initial preload it will continue to cache all your pages whenever they are purged. No further action is needed."
msgstr ""

#: inc/Engine/Preload/Cron/Subscriber.php:138
msgid "WP Rocket Preload pending jobs"
msgstr ""

#: inc/Engine/Preload/Cron/Subscriber.php:166
msgid "WP Rocket Preload revert stuck failed jobs"
msgstr ""

#: inc/ThirdParty/Hostings/Cloudways.php:82
#, php-format
msgid "Varnish auto-purge will be automatically enabled once Varnish is enabled on your %s server."
msgstr ""

#: inc/ThirdParty/Hostings/Kinsta.php:158
#, php-format
msgid "Your installation seems to be missing core Kinsta files managing Cache clearing, which will prevent your Kinsta installation and WP Rocket from working correctly. Please get in touch with Kinsta support through your %1$sMyKinsta%2$s account to resolve this issue."
msgstr ""

#: inc/ThirdParty/Plugins/CDN/Cloudflare.php:114
#, php-format
msgid "%1$s: Cloudflare's HTTP/2 Server Push is incompatible with the features of Remove Unused CSS and Combine CSS files. We strongly recommend disabling it."
msgstr ""

#: inc/ThirdParty/Plugins/CDN/Cloudflare.php:157
msgid "Your site is using the official Cloudflare plugin. We have enabled Cloudflare auto-purge for compatibility. If you have APO activated, it is also compatible."
msgstr ""

#: inc/ThirdParty/Plugins/CDN/Cloudflare.php:158
msgid "Cloudflare cache will be purged each time WP Rocket clears its cache to ensure content is always up-to-date."
msgstr ""

#: inc/ThirdParty/Plugins/CDN/Cloudflare.php:202
#, php-format
msgid "%1$sWP Rocket:%2$s You are using \"Dynamic Cookies Cache\". Cloudflare APO is not yet compatible with that feature."
msgstr ""

#: inc/ThirdParty/Plugins/CDN/Cloudflare.php:208
#, php-format
msgid "You should either disable Cloudflare APO or check with the theme/plugin requiring the use of “Dynamic Cookies Cache” developers for an alternative way to be page-cache friendly. %1$sMore info%2$s"
msgstr ""

#: inc/ThirdParty/Plugins/CDN/Cloudflare.php:259
#, php-format
msgid "%1$sWP Rocket:%2$s You are using \"Separate cache files for mobile devices\". You need to activate \"Cache by Device Type\" %3$ssetting%5$s on Cloudflare APO to serve the right version of the cache. %4$sMore info%5$s"
msgstr ""

#: inc/ThirdParty/Plugins/CDN/Cloudflare.php:280
#, php-format
msgid "%1$sWP Rocket:%2$s You have \"Cache by Device Type\" enabled on Cloudflare APO. If you judge it necessary for the website to have a different cache on mobile and desktop, we suggest you enable our “Separate Cache Files for Mobiles Devices” to ensure the generated cache is accurate."
msgstr ""

#: inc/ThirdParty/Plugins/ModPagespeed.php:102
#, php-format
msgid "<strong>%1$s</strong>: Mod PageSpeed is not compatible with this plugin and may cause unexpected results. %2$sMore Info%3$s"
msgstr ""

#: inc/ThirdParty/Plugins/Optimization/Autoptimize.php:76
#, php-format
msgid "%1$sWP Rocket: %2$sWe have detected that Autoptimize's JavaScript Aggregation feature is enabled. WP Rocket's Delay JavaScript Execution will not be applied to the file it creates. We suggest disabling %1$sJavaScript Aggregation%2$s to take full advantage of Delay JavaScript Execution."
msgstr ""

#: inc/ThirdParty/Plugins/Optimization/Autoptimize.php:131
#, php-format
msgid "%1$sWP Rocket: %2$sWe have detected that Autoptimize's Aggregate Inline CSS feature is enabled. WP Rocket's Load CSS Asynchronously will not work correctly. We suggest disabling %1$sAggregate Inline CSS%2$s to take full advantage of Load CSS Asynchronously Execution."
msgstr ""

#: inc/ThirdParty/Plugins/Optimization/Ezoic.php:45
#, php-format
msgid "This plugin blocks WP Rocket's caching and optimizations. Deactivate it and use %1$sEzoic's nameserver integration%2$s instead."
msgstr ""

#: inc/ThirdParty/Plugins/Optimization/Perfmatters.php:38
msgid "Remove Unused CSS is currently activated in Perfmatters. If you want to use WP Rocket's Remove Unused CSS feature, disable this option in Perfmatters."
msgstr ""

#: inc/ThirdParty/Plugins/Optimization/RapidLoad.php:39
msgid "Automated unused CSS removal is currently activated in RapidLoad Power-Up for Autoptimize. If you want to use WP Rocket's Remove Unused CSS feature, disable the  RapidLoad Power-Up for Autoptimize plugin."
msgstr ""

#: inc/ThirdParty/Plugins/Optimization/WPMeteor.php:42
#, php-format
msgid "Delay JS is currently activated in %1$s. If you want to use WP Rocket’s delay JS, disable %1$s"
msgstr ""

#: inc/ThirdParty/Themes/Avada.php:116
msgid "Avada"
msgstr ""

#: inc/admin/options.php:127
msgid "Defer JavaScript Files"
msgstr ""

#: inc/admin/options.php:128
msgid "Excluded Delay JavaScript Files"
msgstr ""

#: inc/admin/options.php:150
#, php-format
msgid "%1$s: <em>%2$s</em>"
msgstr ""

#: inc/admin/options.php:176
msgid "More info"
msgstr ""

#: inc/admin/ui/notices.php:757
msgid "Turn on Remove Unused CSS"
msgstr ""

#: inc/admin/ui/notices.php:763
msgid "Enable “Separate Cache Files for Mobile Devices” now"
msgstr ""

#: inc/classes/dependencies/wp-media/background-processing/wp-background-process.php:447
#, php-format
msgid "Every %d Minutes"
msgstr ""

#: inc/common/admin-bar.php:84
#: inc/common/admin-bar.php:136
#: views/settings/page-sections/dashboard.php:150
msgid "Clear and preload cache"
msgstr ""

#: inc/common/admin-bar.php:218
#: views/settings/fields/rocket-cdn.php:55
msgid "Purge RocketCDN cache"
msgstr ""

#: inc/deprecated/DeprecatedClassTrait.php:54
#, php-format
msgid "The called class %1$s is <strong>deprecated</strong> since version %2$s! Use %3$s instead."
msgstr ""

#: inc/deprecated/DeprecatedClassTrait.php:65
#, php-format
msgid "The called class %1$s is <strong>deprecated</strong> since version %2$s!"
msgstr ""

#: inc/functions/options.php:429
msgid "The provided license data are not valid."
msgstr ""

#: inc/functions/options.php:432
#, php-format
msgid "To resolve, please %1$scontact support%2$s."
msgstr ""

#: inc/functions/options.php:491
#, php-format
msgid "Try clicking %1$sValidate License%2$s below. If the error persists, follow %3$sthis guide%4$s."
msgstr ""

#: inc/functions/options.php:523
msgid "License validation failed. This user account is blocked."
msgstr ""

#: views/cpcss/activate-cpcss-mobile.php:13
msgid "Load CSS asynchronously for mobile"
msgstr ""

#: views/cpcss/activate-cpcss-mobile.php:14
msgid "Your website currently uses the same Critical Path CSS for both desktop and mobile."
msgstr ""

#: views/cpcss/activate-cpcss-mobile.php:15
msgid "Click the button to enable mobile-specific CPCSS for your site."
msgstr ""

#: views/cpcss/activate-cpcss-mobile.php:20
#: views/settings/enable-google-fonts.php:19
#, php-format
msgid "This is a one-time action and this button will be removed afterwards. %1$sMore info%2$s"
msgstr ""

#: views/cpcss/activate-cpcss-mobile.php:30
#, php-format
msgid "Your site is now using mobile-specific critical path CSS. %1$sMore info%2$s"
msgstr ""

#: views/cpcss/activate-cpcss-mobile.php:39
msgid "Generate Mobile Specific CPCSS"
msgstr ""

#: views/cpcss/metabox/container.php:19
msgid "Critical Path CSS"
msgstr ""

#: views/cpcss/metabox/generate.php:23
#, php-format
msgid "Generate specific Critical Path CSS for this post. %1$sMore info%2$s"
msgstr ""

#: views/cpcss/metabox/generate.php:33
#, php-format
msgid "This post uses specific Critical Path CSS. %1$sMore info%2$s"
msgstr ""

#: views/cpcss/metabox/generate.php:56
msgid "Revert back to the default CPCSS"
msgstr ""

#: views/deactivation-intent/form.php:21
msgid "Facing an issue?"
msgstr ""

#: views/deactivation-intent/form.php:24
msgid "It is not always necessary to deactivate WP Rocket when facing any issues. Most of them can be fixed by deactivating only some options."
msgstr ""

#: views/deactivation-intent/form.php:29
#, php-format
msgid "Our advice? Instead of deactivating WP Rocket, use our %1$sSafe Mode%2$s to quickly disable LazyLoad, File Optimization, and CDN options. Then check to see if your issue is resolved."
msgstr ""

#: views/deactivation-intent/form.php:35
msgid "Do you want to use our Safe Mode to troubleshoot WP Rocket?"
msgstr ""

#: views/deactivation-intent/form.php:55
#, php-format
msgid "Yes, apply \"%1$sSafe Mode%2$s\""
msgstr ""

#: views/deactivation-intent/form.php:68
#, php-format
msgid "and export WP Rocket settings %1$s(Recommended as current settings will be deleted)%2$s"
msgstr ""

#: views/deactivation-intent/form.php:76
msgid "No, deactivate and snooze this message for"
msgstr ""

#: views/deactivation-intent/form.php:78
msgid "1 day"
msgstr ""

#: views/deactivation-intent/form.php:79
msgid "7 days"
msgstr ""

#: views/deactivation-intent/form.php:80
msgid "30 days"
msgstr ""

#: views/deactivation-intent/form.php:81
msgid "Forever"
msgstr ""

#: views/deactivation-intent/form.php:88
msgid "Confirm"
msgstr ""

#: views/settings/dynamic-lists-update.php:14
msgid "Update Inclusion and Exclusion Lists"
msgstr ""

#: views/settings/dynamic-lists-update.php:19
#, php-format
msgid "Compatibility lists are downloaded automatically every week. Click the button if you want to update them manually. %1$sMore info%2$s"
msgstr ""

#: views/settings/dynamic-lists-update.php:29
msgid "Update lists"
msgstr ""

#: views/settings/enable-google-fonts.php:13
msgid "Enable Google Font Optimization"
msgstr ""

#: views/settings/enable-google-fonts.php:14
msgid "Improves font performance and combines multiple font requests to reduce the number of HTTP requests."
msgstr ""

#: views/settings/enable-google-fonts.php:29
#, php-format
msgid "Google Fonts Optimization is now enabled for your site. %1$sMore info%2$s"
msgstr ""

#: views/settings/enable-google-fonts.php:38
msgid "Optimize Google Fonts"
msgstr ""

#: views/settings/fields/rocket-cdn.php:29
#: views/settings/fields/rocket-cdn.php:41
msgid "CDN CNAME"
msgstr ""

#: views/settings/fields/rocket-cdn.php:62
#, php-format
msgid "Purges RocketCDN cached resources for your website. %s"
msgstr ""

#: views/settings/fields/rocket-cdn.php:72
msgid "Clear all RocketCDN cache files"
msgstr ""

#: views/settings/page-sections/dashboard.php:44
#, php-format
msgid "To guarantee fast websites, WP Rocket automatically applies 80% of web performance best practices."
msgstr ""

#: views/settings/page-sections/dashboard.php:86
msgid "with"
msgstr ""

#: views/settings/page-sections/dashboard.php:182
msgid "Remove Used CSS Cache"
msgstr ""

#: views/settings/page-sections/database.php:28
msgid "Save Changes and Optimize"
msgstr ""

#: views/settings/page-sections/license.php:29
#, php-format
msgid "Follow this %1$s, or contact %2$s to get the engine started."
msgstr ""

#: views/settings/page-sections/license.php:32
#, php-format
msgid "%1$s%2$s%3$stutorial%4$s"
msgstr ""

#: views/settings/page-sections/license.php:40
#, php-format
msgid "%1$s%2$s%3$ssupport%4$s"
msgstr ""

#: views/settings/page-sections/tools.php:69
#, php-format
msgid "You can rollback to the previous major version here.%sThen send us a support request."
msgstr ""

#: views/settings/page-sections/tutorials.php:23
msgid "Passing the Core Web vitals"
msgstr ""

#: views/settings/page-sections/tutorials.php:25
msgid "How to improve LCP with WP Rocket"
msgstr ""

#: views/settings/page-sections/tutorials.php:26
msgid "How to improve FID with WP Rocket"
msgstr ""

#: views/settings/page-sections/tutorials.php:27
msgid "How to improve CLS with WP Rocket"
msgstr ""

#: views/settings/page-sections/tutorials.php:31
msgid "Troubleshooting"
msgstr ""

#: views/settings/page.php:82
#, php-format
msgid "Below is a detailed view of all data WP Rocket will collect %1$sif granted permission.%2$s"
msgstr ""

#: views/settings/partials/sidebar.php:18
msgid "Learn about optimal WP Rocket settings for mobile."
msgstr ""

#: views/settings/partials/sidebar.php:27
msgid "Test and Improve Google Core Web Vitals for WordPress."
msgstr ""
