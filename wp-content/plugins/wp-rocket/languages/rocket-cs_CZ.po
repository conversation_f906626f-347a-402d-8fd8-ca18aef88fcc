msgid ""
msgstr ""
"Project-Id-Version: WP Rocket 3.4\n"
"Report-Msgid-Bugs-To: http://wp-rocket.me/\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2020\n"
"Language-Team: Czech (Czech Republic) (https://www.transifex.com/wp-media/teams/18133/cs_CZ/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"POT-Creation-Date: 2020-05-28 13:07-0400\n"
"PO-Revision-Date: 2019-08-26 15:14+0000\n"
"Language: cs_CZ\n"
"Plural-Forms: nplurals=4; plural=(n == 1 && n % 1 == 0) ? 0 : (n >= 2 && n <= 4 && n % 1 == 0) ? 1: (n % 1 != 0 ) ? 2 : 3;\n"
"X-Generator: Poedit 1.8.11\n"
"X-Poedit-Basepath: ..\n"
"X-Poedit-KeywordsList: __;_e;_x:1,2c;_ex:1,2c;_n:1,2;_nx:1,2,4c;_n_noop:1,2;_nx_noop:1,2,3c;esc_attr__;esc_html__;esc_attr_e;esc_html_e;esc_attr_x:1,2c;esc_html_x:1,2c\n"
"X-Poedit-SearchPath-0: .\n"
"X-Poedit-SearchPathExcluded-0: assets\n"
"X-Poedit-SearchPathExcluded-1: src\n"
"X-Poedit-SearchPathExcluded-2: vendor\n"
"X-Poedit-SearchPathExcluded-3: node_modules\n"
"X-Poedit-SearchPathExcluded-4: tests\n"
"X-Poedit-SourceCharset: UTF-8\n"
"X-Textdomain-Support: yes\n"

#: inc/3rd-party/hosting/flywheel.php:18
#: inc/3rd-party/hosting/pressidium.php:18
#: inc/3rd-party/hosting/wp-serveur.php:27
#: inc/ThirdParty/Hostings/Cloudways.php:90
#: inc/ThirdParty/Hostings/Dreampress.php:44
#: inc/ThirdParty/Hostings/Godaddy.php:63
#: inc/ThirdParty/Hostings/O2Switch.php:49
#: inc/ThirdParty/Hostings/OneCom.php:137
#: inc/ThirdParty/Hostings/ProIsp.php:51
#: inc/ThirdParty/Hostings/Savvii.php:50
#: inc/ThirdParty/Hostings/WPEngine.php:47
#: inc/ThirdParty/Hostings/WPXCloud.php:51
#: inc/deprecated/3.6.php:697
#: inc/deprecated/3.6.php:997
#: inc/deprecated/3.9.php:22
#, php-format
msgid "Your site is hosted on %s, we have enabled Varnish auto-purge for compatibility."
msgstr "Váš web je hostován u %s, z důvodu kompatibility bylo aktivováno automatické vyprázdnění Varnish mezipaměti."

#: inc/deprecated/3.12.php:332
msgid "Jetpack XML Sitemaps"
msgstr "Jetpack XML mapy webu"

#: inc/deprecated/3.12.php:334
msgid "Preload the sitemap from the Jetpack plugin"
msgstr "Předběžně načítat mapu webu z pluginu Jetpack"

#: inc/deprecated/3.11.php:83
#: inc/deprecated/3.12.php:58
#: inc/deprecated/3.12.php:87
#: inc/deprecated/3.12.php:186
#: inc/deprecated/3.12.php:253
#: inc/deprecated/3.12.php:342
#, php-format
msgid "We automatically detected the sitemap generated by the %s plugin. You can check the option to preload it."
msgstr "Automaticky byl detekován soubor mapy webu vygenerovaný pluginem %s. Můžete použít možnost předběžného načtení."

#: inc/deprecated/3.12.php:56
msgid "All in One SEO XML sitemap"
msgstr "All in One SEO XML mapy stránek"

#: inc/deprecated/3.12.php:85
msgid "Rank Math XML sitemap"
msgstr "Rank Math XML mapa webu"

#: inc/deprecated/3.12.php:184
msgid "SEOPress XML sitemap"
msgstr "SEOPress XML mapa webu"

#: inc/deprecated/3.12.php:251
msgid "The SEO Framework XML sitemap"
msgstr "The SEO Framework XML mapa webu"

#: inc/deprecated/3.11.php:81
msgid "Yoast SEO XML sitemap"
msgstr "Yoast SEO XML mapa webu"

#: inc/Addon/Cloudflare/API/Client.php:186
#: inc/deprecated/3.5.php:112
#: inc/deprecated/3.5.php:169
msgid "Incorrect Cloudflare email address or API key."
msgstr "Chybná Cloudflare emailová adresa nebo API klíč."

#: inc/Addon/Cloudflare/API/Client.php:190
#: inc/Addon/Cloudflare/API/Client.php:204
#: inc/Addon/Cloudflare/Cloudflare.php:74
#: inc/Addon/Cloudflare/Cloudflare.php:109
#: inc/classes/subscriber/Tools/class-detect-missing-tags-subscriber.php:157
#: inc/deprecated/3.5.php:92
#: inc/deprecated/3.5.php:116
#: inc/deprecated/3.5.php:129
#: inc/deprecated/3.5.php:157
#: inc/deprecated/3.5.php:173
#, php-format
msgid "Read the %1$sdocumentation%2$s for further guidance."
msgstr "Další pokyny naleznete v %1$sdokumentaci%2$s."

#: inc/Addon/Cloudflare/API/Client.php:192
#: inc/Addon/Cloudflare/API/Client.php:206
#: inc/Addon/Cloudflare/Auth/APIKey.php:63
#: inc/Addon/Cloudflare/Cloudflare.php:76
#: inc/Addon/Cloudflare/Cloudflare.php:111
#: inc/deprecated/3.5.php:81
#: inc/deprecated/3.5.php:94
#: inc/deprecated/3.5.php:118
#: inc/deprecated/3.5.php:131
#: inc/deprecated/3.5.php:159
#: inc/deprecated/3.5.php:175
#: inc/deprecated/3.5.php:208
msgid "https://docs.wp-rocket.me/article/18-using-wp-rocket-with-cloudflare/?utm_source=wp_plugin&utm_medium=wp_rocket#add-on"
msgstr "https://docs.wp-rocket.me/article/18-using-wp-rocket-with-cloudflare/?utm_source=wp_plugin&utm_medium=wp_rocket#add-on"

#: inc/Addon/Cloudflare/API/Client.php:200
#: inc/deprecated/3.5.php:125
msgid "Incorrect Cloudflare Zone ID."
msgstr "Neplatné Cloudflare Zone ID."

#: inc/Addon/Cloudflare/Auth/APIKey.php:61
#, php-format
msgid "Cloudflare email and/or API key are not set. Read the %1$sdocumentation%2$s for further guidance."
msgstr "Cloudflare email a / nebo API klíč nejsou nastaveny. Podrobnější informace naleznete v %1$sdokumentaci%2$s."

#: inc/Addon/Cloudflare/Cloudflare.php:70
#: inc/deprecated/3.5.php:88
msgid "Missing Cloudflare Zone ID."
msgstr "Chybí Cloudflare Zone ID."

#: inc/Addon/Cloudflare/Cloudflare.php:105
#: inc/deprecated/3.5.php:153
msgid "It looks like your domain is not set up on Cloudflare."
msgstr "Tato doména není v Cloudflare nastavena."

#: inc/deprecated/3.5.php:587
#, php-format
msgid "<strong>WP Rocket:</strong> %s"
msgstr "<strong>WP Rocket:</strong> %s"

#: inc/deprecated/3.5.php:592
msgid "<strong>WP Rocket:</strong> Cloudflare cache successfully purged."
msgstr "<strong>WP Rocket:</strong> Cloudflare mezipaměť byla úspěšně vyprázdněna."

#: inc/Addon/Cloudflare/Subscriber.php:632
#: inc/admin/options.php:184
#: inc/classes/subscriber/Tools/class-detect-missing-tags-subscriber.php:148
msgid "WP Rocket: "
msgstr "WP Rocket: "

#: inc/Addon/Cloudflare/Subscriber.php:297
#, php-format
msgid "Cloudflare development mode error: %s"
msgstr "Cloudflare chyba režimu pro vývojáře: %s"

#: inc/Addon/Cloudflare/Subscriber.php:304
#, php-format
msgid "Cloudflare development mode %s"
msgstr "Cloudflare režim pro vývojáře %s"

#: inc/Addon/Cloudflare/Subscriber.php:321
#, php-format
msgid "Cloudflare cache level error: %s"
msgstr "Cloudflare chyba úrovně mezipaměti: %s"

#: inc/Addon/Cloudflare/Subscriber.php:334
#, php-format
msgid "Cloudflare cache level set to %s"
msgstr "Cloudflare úroveň mezipaměti byla nastavena na hodnotu %s"

#: inc/Addon/Cloudflare/Subscriber.php:350
#, php-format
msgid "Cloudflare minification error: %s"
msgstr "Cloudflare chyba zmenšení: %s"

#: inc/Addon/Cloudflare/Subscriber.php:357
#, php-format
msgid "Cloudflare minification %s"
msgstr "Cloudflare zmenšení: %s"

#: inc/Addon/Cloudflare/Subscriber.php:373
#, php-format
msgid "Cloudflare rocket loader error: %s"
msgstr "Chyba Cloudflare RocketLoaderu: %s"

#: inc/Addon/Cloudflare/Subscriber.php:380
#, php-format
msgid "Cloudflare rocket loader %s"
msgstr "Cloudflare RocketLoader %s"

#: inc/Addon/Cloudflare/Subscriber.php:396
#, php-format
msgid "Cloudflare browser cache error: %s"
msgstr "Cloudflare chyba mezipaměti prohlížeče: %s"

#: inc/Engine/Admin/Database/Subscriber.php:79
#: inc/deprecated/Engine/Addon/FacebookTracking/Subscriber.php:92
#: inc/deprecated/Engine/Addon/GoogleTracking/Subscriber.php:137
msgid "weekly"
msgstr "týdně"

#: inc/Engine/Admin/Settings/Page.php:201
msgid "Save Changes"
msgstr "Uložit změny"

#: inc/Engine/Admin/Settings/Page.php:201
msgid "Validate License"
msgstr "Ověřit licenci"

#: inc/Engine/Admin/Settings/Page.php:257
#: inc/Engine/Admin/Settings/Page.php:258
msgid "Unavailable"
msgstr "Nedostupné"

#: inc/Engine/Admin/Settings/Page.php:352
#: inc/deprecated/deprecated.php:1789
#: views/settings/page-sections/dashboard.php:80
msgid "License"
msgstr "Licence"

#: inc/Engine/Admin/Settings/Page.php:369
msgid "API key"
msgstr "API klíč"

#: inc/Engine/Admin/Settings/Page.php:384
msgid "Email address"
msgstr "Emailová adresa"

#: inc/Engine/Admin/Settings/Page.php:410
msgid "Dashboard"
msgstr "Nástěnka"

#: inc/Engine/Admin/Settings/Page.php:411
msgid "Get help, account info"
msgstr "Nápověda, informace o účtu"

#: inc/Engine/Admin/Settings/Page.php:420
msgid "My Status"
msgstr "Stav"

#: inc/Engine/Admin/Settings/Page.php:430
#: views/settings/page.php:75
msgid "Rocket Analytics"
msgstr "Diagnostika raket"

#: inc/Engine/Admin/Settings/Page.php:432
#, php-format
msgid "I agree to share anonymous data with the development team to help improve WP Rocket. %1$sWhat info will we collect?%2$s"
msgstr "Souhlasím se sdílením anonymních dat s vývojovým týmem, které pomohou zlepšit WP Rocket. %1$sJaké data budou shromažďována?%2$s"

#: inc/Engine/Admin/Settings/Page.php:456
#: inc/Engine/Cache/WPCache.php:354
msgid "Cache"
msgstr "Mezipaměť"

#: inc/Engine/Admin/Settings/Page.php:457
msgid "Basic cache options"
msgstr "Základní nastavení mezipaměti"

#: inc/Engine/Admin/Settings/Page.php:464
msgid "Mobile Cache"
msgstr "Mezipaměť pro mobilní zařízení"

#: inc/Engine/Admin/Settings/Page.php:466
msgid "Speed up your site for mobile visitors."
msgstr "Zrychlete svůj web pro mobilní návštěvníky."

#: inc/Engine/Admin/Settings/Page.php:471
msgid "We detected you use a plugin that requires a separate cache for mobile, and automatically enabled this option for compatibility."
msgstr "Byl zaznamenán aktivní plugin vyžadující samostatnou mezipaměť pro mobilní zařízení, a pro zajištění kompatibility bylo toto nastavení automaticky aktivováno."

#: inc/Engine/Admin/Settings/Page.php:475
msgid "User Cache"
msgstr "Uživatelská mezipaměť"

#: inc/Engine/Admin/Settings/Page.php:478
#, php-format
msgid "%1$sUser cache%2$s is great when you have user-specific or restricted content on your website."
msgstr "%1$sUživatelská mezipaměť%2$s je skvěla, pokud máte na webu uživatelský nebo omezený obsah."

#: inc/Engine/Admin/Settings/Page.php:486
msgid "Cache Lifespan"
msgstr "Životnost mezipaměti"

#: inc/Engine/Admin/Settings/Page.php:489
#, php-format
msgid "Cache files older than the specified lifespan will be deleted.<br>Enable %1$spreloading%2$s for the cache to be rebuilt automatically after lifespan expiration."
msgstr "Soubory mezipaměti starší než zadané životnosti budou odstraněny.<br>Povolte %1$spřednačítání%2$s pro automatickou přegenerování mezipaměti po vypršení její životnosti."

#: inc/Engine/Admin/Settings/Page.php:503
msgid "Enable caching for logged-in WordPress users"
msgstr "Povolit ukládání do mezipaměti pro přihlášené uživatele WordPressu"

#: inc/Engine/Admin/Settings/Page.php:511
msgid "Enable caching for mobile devices"
msgstr "Povolit ukládání do mezipaměti pro mobilní zařízení"

#: inc/Engine/Admin/Settings/Page.php:526
msgid "Separate cache files for mobile devices"
msgstr "Oddělit soubory mezipaměti pro mobilní zařízení"

#: inc/Engine/Admin/Settings/Page.php:528
#, php-format
msgid "Most modern themes are responsive and should work without a separate cache. Enable this only if you have a dedicated mobile theme or plugin. %1$sMore info%2$s"
msgstr "Většina moderních šablon je responzivní a měla by správně fungovat bez samostatné mezipaměti. Tuto možnost aktivujte jen v případě, že máte samostatnou šablonu nebo speciální plugin pro mobilní zařízení. %1$sVíce informací%2$s"

#: inc/Engine/Admin/Settings/Page.php:544
msgid "Specify time after which the global cache is cleared<br>(0 = unlimited )"
msgstr "Zadejte čas, po kterém se globální mezipaměť vymaže<br>(0 = neomezeně)"

#: inc/Engine/Admin/Settings/Page.php:546
#, php-format
msgid "Reduce lifespan to 10 hours or less if you notice issues that seem to appear periodically. %1$sWhy?%2$s"
msgstr "Snižte životnost na 10 nebo méně hodin pokud si všimnete opakujících se problémů. %1$sProč?%2$s"

#: inc/Engine/License/views/promo-banner.php:31
#: inc/Engine/License/views/renewal-soon-banner.php:14
msgid "Minutes"
msgstr "Minuty"

#: inc/Engine/Admin/Settings/Page.php:552
#: inc/Engine/License/views/promo-banner.php:30
#: inc/Engine/License/views/renewal-soon-banner.php:13
msgid "Hours"
msgstr "Hodiny"

#: inc/Engine/Admin/Settings/Page.php:553
#: inc/Engine/License/views/promo-banner.php:29
#: inc/Engine/License/views/renewal-soon-banner.php:12
msgid "Days"
msgstr "Dny"

#: inc/Engine/Admin/Settings/Page.php:602
msgid "File Optimization"
msgstr "Optimalizace souborů"

#: inc/Engine/Admin/Settings/Page.php:603
msgid "Optimize CSS & JS"
msgstr "Optimalizace CSS a JS"

#: inc/Engine/Admin/Settings/Page.php:611
#: inc/Engine/Admin/Settings/Page.php:637
#, php-format
msgid "%1$s Minification is currently activated in <strong>Autoptimize</strong>. If you want to use %2$s’s minification, disable those options in Autoptimize."
msgstr "Zmenšování %1$s je aktuálně aktivováno v pluginu <strong>Autoptimize</strong>. Chcete-li použít zmenšování od %2$s, deaktivujte tyto možnosti v pluginu Autoptimize."

#: inc/Engine/Admin/Settings/Page.php:621
msgid "CSS Files"
msgstr "Soubory CSS"

#: inc/Engine/Admin/Settings/Page.php:630
msgid "JavaScript Files"
msgstr "Soubory JavaScriptu"

#: views/settings/enable-google-fonts.php:38
msgid "Optimize Google Fonts"
msgstr "Optimalizovat písma Google Fonts"

#: inc/Engine/Admin/Settings/Page.php:663
msgid "Minify CSS files"
msgstr "Zmenšit soubory CSS"

#: inc/Engine/Admin/Settings/Page.php:664
msgid "Minify CSS removes whitespace and comments to reduce the file size."
msgstr "Zmenšení CSS odstraní mezery a komentáře pro zmenšení velikosti."

#: inc/Engine/Admin/Settings/Page.php:677
#: inc/Engine/Admin/Settings/Page.php:701
#: inc/Engine/Admin/Settings/Page.php:765
#: inc/Engine/Admin/Settings/Page.php:831
#: inc/Engine/Admin/Settings/Page.php:855
msgid "This could break things!"
msgstr "Toto nastavení by mohlo rozbít web!"

#: inc/Engine/Admin/Settings/Page.php:678
#: inc/Engine/Admin/Settings/Page.php:702
#: inc/Engine/Admin/Settings/Page.php:766
#: inc/Engine/Admin/Settings/Page.php:832
#: inc/Engine/Admin/Settings/Page.php:856
msgid "If you notice any errors on your website after having activated this setting, just deactivate it again, and your site will be back to normal."
msgstr "Pokud si všimnete jakýchkoliv chyb na svém webu po zapnutí tohoto nastavení, stačí aktivované nastavení deaktivovat, a váš web bude zase normálně fungovat."

#: inc/Engine/Admin/Settings/Page.php:679
msgid "Activate minify CSS"
msgstr "Aktivovat zmenšení CSS"

#: inc/Engine/Admin/Settings/Page.php:684
msgid "Combine CSS files <em>(Enable Minify CSS files to select)</em>"
msgstr "Kombinovat soubory CSS <em>(nejprve musíte povolit zmenšení souborů CSS)</em>"

#: inc/Engine/Admin/Settings/Page.php:686
#, php-format
msgid "Combine CSS merges all your files into 1, reducing HTTP requests. Not recommended if your site uses HTTP/2. %1$sMore info%2$s"
msgstr "Zkombinuje CSS soubory do 1 souboru, sníží počet HTTP požadavků. Nedoporučuje se pokud váš web používá HTTP/2. %1$sVíce informací%2$s"

#: inc/Engine/Admin/Settings/Page.php:703
msgid "Activate combine CSS"
msgstr "Aktivovat kombinování CSS"

#: inc/Engine/Admin/Settings/Page.php:708
#: inc/admin/options.php:124
msgid "Excluded CSS Files"
msgstr "Vyloučené CSS soubory"

#: inc/Engine/Admin/Settings/Page.php:709
msgid "Specify URLs of CSS files to be excluded from minification and concatenation (one per line)."
msgstr "Určete URL adresy CSS souborů, které mají být vyloučeny ze zmenšování a spojení (jedna adresa na řádek)."

#: inc/Engine/Admin/Settings/Page.php:725
#: inc/Engine/Admin/Settings/Page.php:747
msgid "Optimize CSS delivery"
msgstr "Optimalizovat doručování CSS"

#: inc/Engine/Admin/Settings/Page.php:800
msgid "Fallback critical CSS"
msgstr "Záložní kritická cesta CSS"

#: inc/Engine/Admin/Settings/Page.php:802
#, php-format
msgid "Provides a fallback if auto-generated critical path CSS is incomplete. %1$sMore info%2$s"
msgstr "Poskytuje zálohu pokud je automaticky generovaná kritická cesta k CSS nekompletní. %1$sVíce informací%2$s"

#: inc/Engine/Admin/Settings/Page.php:817
msgid "Minify JavaScript files"
msgstr "Zmenšit soubory JS"

#: inc/Engine/Admin/Settings/Page.php:818
msgid "Minify JavaScript removes whitespace and comments to reduce the file size."
msgstr "Zmenšení JavaScriptu odstraní mezery a komentáře pro zmenšení velikosti."

#: inc/Engine/Admin/Settings/Page.php:833
msgid "Activate minify JavaScript"
msgstr "Aktivovat zmenšení JavaScriptu"

#: inc/Engine/Admin/Settings/Page.php:838
msgid "Combine JavaScript files <em>(Enable Minify JavaScript files to select)</em>"
msgstr "Kombinovat soubory JavaScriptu <em>(nejprve musíte povolit zmenšení souborů JS)</em>"

#: inc/Engine/Admin/Settings/Page.php:840
#, php-format
msgid "Combine JavaScript files combines your site’s internal, 3rd party and inline JS reducing HTTP requests. Not recommended if your site uses HTTP/2. %1$sMore info%2$s"
msgstr "Zkombinování JavaScriptu interních souborů, skriptů vložených v kódu a kódu třetích stran, sníží počet HTTP požadavků. Nedoporučuje se pokud web používá HTTP/2. %1$sVíce informací%2$s"

#: inc/Engine/Admin/Settings/Page.php:857
msgid "Activate combine JavaScript"
msgstr "Aktivovat kombinování JavaScriptu"

#: inc/Engine/Admin/Settings/Page.php:862
#: inc/admin/options.php:125
msgid "Excluded Inline JavaScript"
msgstr "Vyloučený vložený JavaScript"

#: inc/Engine/Admin/Settings/Page.php:864
#, php-format
msgid "Specify patterns of inline JavaScript to be excluded from concatenation (one per line). %1$sMore info%2$s"
msgstr "Určete znaky vloženého JavaScriptu, který má být vyloučen ze slučování (jeden na řádek). %1$s Více informací%2$s"

#: inc/Engine/Admin/Settings/Page.php:880
#: inc/Engine/Admin/Settings/Page.php:913
#: inc/Engine/Admin/Settings/Page.php:960
#: inc/admin/options.php:126
msgid "Excluded JavaScript Files"
msgstr "Vyloučené soubory JavaScriptu"

#: inc/Engine/Admin/Settings/Page.php:881
msgid "Specify URLs of JavaScript files to be excluded from minification and concatenation (one per line)."
msgstr "Určete URL adresy souborů JavaScriptu, které mají být vyloučeny ze zmenšování a spojení (jednu adresa na řádek)"

#: inc/Engine/Admin/Settings/Page.php:882
msgid "<strong>Internal:</strong> The domain part of the URL will be stripped automatically. Use (.*).js wildcards to exclude all JS files located at a specific path."
msgstr "<strong>Interní:</strong>Doménová část URL adresy bude automaticky odstraněna. Použijte (.*).js hvězdičku pro vyloučení všech JS souborů umístěných v konkrétní cestě."

#: inc/Engine/Admin/Settings/Page.php:884
#, php-format
msgid "<strong>3rd Party:</strong> Use either the full URL path or only the domain name, to exclude external JS. %1$sMore info%2$s"
msgstr "<strong>3. strany:</strong> Zadejte buďto kompletní URL adresu, nebo jen doménu, pro vyloučení externího JS. %1$sVíce informací%2$s"

#: inc/Engine/Admin/Settings/Page.php:900
msgid "Load JavaScript deferred"
msgstr "Načítat JavaScriptové soubory se zpožděním"

#: inc/Engine/Admin/Settings/Page.php:902
#, php-format
msgid "Load JavaScript deferred eliminates render-blocking JS on your site and can improve load time. %1$sMore info%2$s"
msgstr "Načítání JavaScriptu se zpožděním, z webu odstraní JS blokující vykreslení obsahu, a může zlepšit rychlost jeho načítání. %1$sVíce informací%2$s"

#: inc/Engine/Admin/Settings/Page.php:993
msgid "Media"
msgstr "Média"

#: inc/ThirdParty/Themes/Avada.php:116
msgid "Avada"
msgstr "Avada"

#: inc/Engine/Admin/Settings/Page.php:1003
msgid "Autoptimize"
msgstr "Autoptimize"

#: inc/Engine/Admin/Settings/Page.php:1048
msgid "LazyLoad"
msgstr "LazyLoad"

#: inc/Engine/Admin/Settings/Page.php:1051
#, php-format
msgid "It can improve actual and perceived loading time as images, iframes, and videos will be loaded only as they enter (or about to enter) the viewport and reduces the number of HTTP requests. %1$sMore Info%2$s"
msgstr "LazyLoad může vylepšit reálnou a vizuální rychlost načítání, protože obrázky, iframy a videa se budou načítat jen jakmile se objeví (nebo se budou mít za chvíli objevit) v zobrazovaném prostoru, a bude tím snížen počet HTTP požadavků. %1$sVíce informací%2$s"

#: inc/Engine/Admin/Settings/Page.php:1058
#, php-format
msgid "LazyLoad is currently activated in %2$s. If you want to use WP Rocket’s LazyLoad, disable this option in %2$s."
msgstr "LazyLoad je aktuálně aktivováno v %2$s. Pokud chcete použít WP Rocket LazyLoad, zakažte tuto možnost v %2$s."

#: inc/Engine/Admin/Settings/Page.php:1928
#, php-format
msgid "Enable this option if you would like WP Rocket to serve WebP images to compatible browsers. Please note that WP Rocket cannot create WebP images for you. To create WebP images we recommend %1$sImagify%2$s. %3$sMore info%2$s"
msgstr "Tuto možnost povolte, pokud chcete, aby WP Rocket poskytovalo WebP obrázky kompatibilním prohlížečům. Berte na vědomí, že WP Rocket nemůže vytvářet WebP obrázky. Pro vytvoření WebP obrázků můžete použít např. plugin %1$sImagify%2$s. %3$sVíce informací%2$s"

#: inc/Engine/Admin/Settings/Page.php:1083
msgid "Enable for images"
msgstr "Povolit pro obrázky"

#: inc/Engine/Admin/Settings/Page.php:1095
#, php-format
msgid "LazyLoad for images is currently activated in %2$s. If you want to use %1$s’s LazyLoad, disable this option in %2$s."
msgstr "LazyLoad pro obrázky je aktuálně aktivováno v %2$s. Pokud chcete použít od LazyLoad %1$s, zakažte tuto možnost v %2$s."

#: inc/Engine/Admin/Settings/Page.php:1103
msgid "Enable for iframes and videos"
msgstr "Povolit pro iframe a videa"

#: inc/Engine/Admin/Settings/Page.php:1118
msgid "Replace YouTube iframe with preview image"
msgstr "Nahraďte iframe YouTube za náhledový obrázek"

#: inc/Engine/Admin/Settings/Page.php:1120
msgid "This can significantly improve your loading time if you have a lot of YouTube videos on a page."
msgstr "Toto nastavení může výrazně zkrátit dobu načítání, pokud máte na stránce mnoho videí z YouTube."

#: inc/Engine/Admin/Settings/Page.php:1164
#: inc/deprecated/deprecated.php:1776
msgid "Preload"
msgstr "Předběžné načítání"

#: inc/Engine/Admin/Settings/Page.php:1165
msgid "Generate cache files, preload fonts"
msgstr "Generuje soubory mezipaměti, přednačítá písma"

#: inc/Engine/Admin/Settings/Page.php:1177
msgid "Preload Cache"
msgstr "Přednačítat mezipaměť"

#: inc/Engine/Admin/Settings/Page.php:1199
msgid "Prefetch DNS Requests"
msgstr "Předběžné požadavky DNS"

#: inc/Engine/Admin/Settings/Page.php:1201
msgid "DNS prefetching can make external files load faster, especially on mobile networks"
msgstr "Předběžné požadavky DNS mohou zrychlit předběžné načítání externích souborů, především na mobilních sítích"

#: inc/Engine/Admin/Settings/Page.php:1206
msgid "Preload Fonts"
msgstr "Přednačítat písma"

#: inc/Engine/Admin/Settings/Page.php:1209
#, php-format
msgid "Improves performance by helping browsers discover fonts in CSS files. %1$sMore info%2$s"
msgstr "Zlepšuje výkon tím, že pomáhá prohlížečům nalézat písma v souborech CSS. %1$sVíce informací%2$s"

#: inc/Engine/Admin/Settings/Page.php:1223
msgid "Activate Preloading"
msgstr "Aktivovat předběžné načítání"

#: inc/Engine/Admin/Settings/Page.php:1250
msgid "URLs to prefetch"
msgstr "URL adresy pro přednačítání"

#: inc/Engine/Admin/Settings/Page.php:1251
msgid "Specify external hosts to be prefetched (no <code>http:</code>, one per line)"
msgstr "Zadejte externí hostitele, kteří se mají předběžně načítat (bez <code>http:</code>, pište jednoho na řádek)"

#: inc/Engine/Admin/Settings/Page.php:1260
msgid "Fonts to preload"
msgstr "Písma k předběžnému načítání"

#: inc/Engine/Admin/Settings/Page.php:1262
msgid "The domain part of the URL will be stripped automatically.<br/>Allowed font extensions: otf, ttf, svg, woff, woff2."
msgstr "Doménová část URL adresy bude automaticky odstraněna.<br/>Povolené přípony písem: otf, ttf, svg, woff, woff2."

#: inc/Engine/Admin/Settings/Page.php:1290
msgid "Advanced Rules"
msgstr "Pokročilá pravidla"

#: inc/Engine/Admin/Settings/Page.php:1291
msgid "Fine-tune cache rules"
msgstr "Vyladění pravidel mezipaměti"

#: inc/Engine/Admin/Settings/Page.php:1302
msgid "Sensitive pages like custom login/logout URLs should be excluded from cache."
msgstr "Citlivé stránky jako URL pro vlastní přihlášení/odhlášení by neměly být ukládány do mezipaměti."

#: inc/Engine/Admin/Settings/Page.php:1305
msgctxt "plugin name"
msgid "WooCommerce"
msgstr "WooCommerce"

#: inc/Engine/Admin/Settings/Page.php:1307
msgctxt "plugin name"
msgid "Easy Digital Downloads"
msgstr "Easy Digital Downloads"

#: inc/Engine/Admin/Settings/Page.php:1309
msgctxt "plugin name"
msgid "iThemes Exchange"
msgstr "iThemes Exchange"

#: inc/Engine/Admin/Settings/Page.php:1311
msgctxt "plugin name"
msgid "Jigoshop"
msgstr "Jigoshop"

#: inc/Engine/Admin/Settings/Page.php:1313
msgctxt "plugin name"
msgid "WP-Shop"
msgstr "WP-Shop"

#: inc/Engine/Admin/Settings/Page.php:1319
#, php-format
msgid "<br>Cart, checkout and \"my account\" pages set in <strong>%1$s%2$s%3$s</strong> will be detected and never cached by default."
msgstr "<br>Stránky jako např. košík, pokladna a „můj účet“, nastavené v <strong>%1$s%2$s%3$s</strong>, budou detekovány a ve výchozím nastavení jim nebude generována mezipaměť."

#: inc/Engine/Admin/Settings/Page.php:1329
#: inc/admin/options.php:129
msgid "Never Cache URL(s)"
msgstr "Nevytvářet mezipaměť pro tyto URL"

#: inc/Engine/Admin/Settings/Page.php:1337
msgid "Never Cache Cookies"
msgstr "Cookies pro zakázání vytváření mezipaměti"

#: inc/Engine/Admin/Settings/Page.php:1343
#: inc/admin/options.php:130
msgid "Never Cache User Agent(s)"
msgstr "User Agent nepřijímající soubory mezipaměti"

#: inc/Engine/Admin/Settings/Page.php:1349
#: inc/admin/options.php:131
msgid "Always Purge URL(s)"
msgstr "URL u kterých se má vždy vyprázdnit mezipaměť"

#: inc/Engine/Admin/Settings/Page.php:1355
msgid "Cache Query String(s)"
msgstr "Ukládání řetězců dotazů do mezipaměti"

#: inc/Engine/Admin/Settings/Page.php:1358
#, php-format
msgid "%1$sCache for query strings%2$s enables you to force caching for specific GET parameters."
msgstr "%1$sMezipaměť pro řetězce dotazu%2$s vám umožní vytvářet mezipaměť pro konkrétní parametry typu GET."

#: inc/Engine/Admin/Settings/Page.php:1369
msgid "Specify URLs of pages or posts that should never be cached (one per line)"
msgstr "Určete URL adresy stránek, nebo příspěvků, které by nikdy neměly mít vytvořenou mezipaměť (jednu na řádek)"

#: inc/Engine/Admin/Settings/Page.php:1370
#: inc/Engine/Admin/Settings/Page.php:1398
msgid "The domain part of the URL will be stripped automatically.<br>Use (.*) wildcards to address multiple URLs under a given path."
msgstr "Doménová část URL adresy bude automaticky odstraněna.<br>Použijte (.*) hvězdičky pro vyloučení více URL adres umístěných v konkrétní cestě."

#: inc/Engine/Admin/Settings/Page.php:1387
msgid "Specify user agent strings that should never see cached pages (one per line)"
msgstr "Zadejte řetězce user-agent, kterým by nikdy neměly být poskytnuty stránky z mezipaměti (jeden na řádek)"

#: inc/Engine/Admin/Settings/Page.php:1388
msgid "Use (.*) wildcards to detect parts of UA strings."
msgstr "Použijte (.*) hvězdičku pro určení částí UA řetězců."

#: inc/Engine/Admin/Settings/Page.php:1397
msgid "Specify URLs you always want purged from cache whenever you update any post or page (one per line)"
msgstr "Určete URL adresy, kterým chcete vymazat mezipaměť vždy, když bude provedena aktualizace jakéhokoliv příspěvku nebo stránky (jednu na řádek)"

#: inc/Engine/Admin/Settings/Page.php:1406
msgid "Specify query strings for caching (one per line)"
msgstr "Zadejte řetězce dotazů pro ukládání do mezipaměti (jeden na řádek)"

#: inc/Engine/Admin/Settings/Page.php:1431
#: inc/deprecated/deprecated.php:1775
msgid "Database"
msgstr "Databáze"

#: inc/Engine/Admin/Settings/Page.php:1432
msgid "Optimize, reduce bloat"
msgstr "Optimalizace, zeštíhlení"

#: inc/Engine/Admin/Settings/Page.php:1439
msgid "Post Cleanup"
msgstr "Čištění příspěvků"

#: inc/Engine/Admin/Settings/Page.php:1441
msgid "Post revisions and drafts will be permanently deleted. Do not use this option if you need to retain revisions or drafts."
msgstr "Starší verze příspěvků a koncepty budou trvale odstraněny. Nepoužívejte tuto volbu pokud potřebujete zachovat starší verze nebo koncepty."

#: inc/Engine/Admin/Settings/Page.php:1446
msgid "Comments Cleanup"
msgstr "Čištění komentářů"

#: inc/Engine/Admin/Settings/Page.php:1448
msgid "Spam and trashed comments will be permanently deleted."
msgstr "Komentářový spam a komentáře uložené v koši budou trvale odstraněny."

#: inc/Engine/Admin/Settings/Page.php:1452
msgid "Transients Cleanup"
msgstr "Čištění dočasných dat databáze"

#: inc/Engine/Admin/Settings/Page.php:1454
msgid "Transients are temporary options; they are safe to remove. They will be automatically regenerated as your plugins require them."
msgstr "Zde jsou zobrazena dočasná data databáze; s klidem je můžete odstranit. Pokud je pluginy budou zase potřebovat, vygenerují se znovu."

#: inc/Engine/Admin/Settings/Page.php:1458
msgid "Database Cleanup"
msgstr "Čištění databáze"

#: inc/Engine/Admin/Settings/Page.php:1460
msgid "Reduces overhead of database tables"
msgstr "Snižuje možnost přetěžování tabulek databáze"

#: inc/Engine/Admin/Database/Optimization.php:30
#: inc/Engine/Admin/Settings/Page.php:1475
msgid "Revisions"
msgstr "Starší verze"

#: inc/Engine/Admin/Settings/Page.php:1477
#, php-format
msgid "%s revision in your database."
msgid_plural "%s revisions in your database."
msgstr[0] "V databázi je %s starší verze stránky/příspěvku."
msgstr[1] "%s starší verze stránek/příspěvků jsou v databázi."
msgstr[2] "%s starší verze stránek/příspěvků jsou v databázi."
msgstr[3] "%s starších verzí stránek/příspěvků je v databázi."

#: inc/Engine/Admin/Database/Optimization.php:31
#: inc/Engine/Admin/Settings/Page.php:1485
msgid "Auto Drafts"
msgstr "Automatické koncepty"

#: inc/Engine/Admin/Settings/Page.php:1487
#, php-format
msgid "%s draft in your database."
msgid_plural "%s drafts in your database."
msgstr[0] "V databázi je %s automatický koncept."
msgstr[1] "%s automatické koncepty jsou v databázi."
msgstr[2] "%s automatické koncepty jsou v databázi."
msgstr[3] "%s automatických konceptů je v databázi."

#: inc/Engine/Admin/Database/Optimization.php:32
#: inc/Engine/Admin/Settings/Page.php:1495
msgid "Trashed Posts"
msgstr "Odstraněné příspěvky"

#: inc/Engine/Admin/Settings/Page.php:1497
#, php-format
msgid "%s trashed post in your database."
msgid_plural "%s trashed posts in your database."
msgstr[0] "V databázi je %s odstraněný příspěvek."
msgstr[1] "%s odstraněné příspěvky jsou v databázi."
msgstr[2] "%s odstraněné příspěvky jsou v databázi."
msgstr[3] "%s odstraněných příspěvků je v databázi."

#: inc/Engine/Admin/Database/Optimization.php:33
#: inc/Engine/Admin/Settings/Page.php:1505
msgid "Spam Comments"
msgstr "Komentáře označené jako spam"

#: inc/Engine/Admin/Settings/Page.php:1507
#, php-format
msgid "%s spam comment in your database."
msgid_plural "%s spam comments in your database."
msgstr[0] "V databázi je %s komentářový spam."
msgstr[1] "%s komentářové spamy jsou v databázi."
msgstr[2] "%s komentářové spamy jsou v databázi."
msgstr[3] "%s komentářových spamů je v databázi."

#: inc/Engine/Admin/Database/Optimization.php:34
#: inc/Engine/Admin/Settings/Page.php:1515
msgid "Trashed Comments"
msgstr "Odstraněné komentáře"

#: inc/Engine/Admin/Settings/Page.php:1517
#, php-format
msgid "%s trashed comment in your database."
msgid_plural "%s trashed comments in your database."
msgstr[0] "V databázi je %s odstraněný komentář."
msgstr[1] "V databázi jsou %s odstraněné komentáře."
msgstr[2] "V databázi je %s odstraněných komentářů."
msgstr[3] "V databázi je %s odstraněných komentářů."

#: inc/Engine/Admin/Settings/Page.php:1525
msgid "All transients"
msgstr "Veškerá dočasná data"

#: inc/Engine/Admin/Settings/Page.php:1527
#, php-format
msgid "%s transient in your database."
msgid_plural "%s transients in your database."
msgstr[0] "V databázi jsou %s vypršené dočasné dat."
msgstr[1] "V databázi jsou %s vypršené dočasné data."
msgstr[2] "V databázi je %s vypršených dočasných dat."
msgstr[3] "V databázi je %s vypršených dočasných dat."

#: inc/Engine/Admin/Settings/Page.php:1535
msgid "Optimize Tables"
msgstr "Optimalizovat tabulky"

#: inc/Engine/Admin/Settings/Page.php:1537
#, php-format
msgid "%s table to optimize in your database."
msgid_plural "%s tables to optimize in your database."
msgstr[0] "V databázi je %s tabulka, kterou je potřeba optimalizovat."
msgstr[1] "%s tabulky v databázi je potřeba optimalizovat."
msgstr[2] "%s tabulky v databázi je potřeba optimalizovat."
msgstr[3] "%s tabulek v databázi je potřeba optimalizovat."

#: inc/Engine/Admin/Settings/Page.php:1548
msgid "Schedule Automatic Cleanup"
msgstr "Naplánovat automatické čištění"

#: inc/Engine/Admin/Settings/Page.php:1560
msgid "Frequency"
msgstr "Frekvence"

#: inc/Engine/Admin/Settings/Page.php:1568
msgid "Daily"
msgstr "Denně"

#: inc/Engine/Admin/Settings/Page.php:1569
msgid "Weekly"
msgstr "Týdně"

#: inc/Engine/Admin/Settings/Page.php:1570
msgid "Monthly"
msgstr "Měsíčně"

#: inc/Engine/Admin/Settings/Page.php:1586
#: inc/Engine/Admin/Settings/Page.php:1597
#: inc/admin/ui/meta-boxes.php:108
#: inc/deprecated/deprecated.php:1773
msgid "CDN"
msgstr "CDN"

#: inc/Engine/Admin/Settings/Page.php:1587
msgid "Integrate your CDN"
msgstr "Integrace CDN"

#: inc/Engine/Admin/Settings/Page.php:1599
msgid "All URLs of static files (CSS, JS, images) will be rewritten to the CNAME(s) you provide."
msgstr "Všechny URL adresy statických souborů (obrázky, CSS, JS) budou přepsány pro níže zadané CNAME(s)."

#: inc/Engine/Admin/Settings/Page.php:1601
#, php-format
msgid "Not required for services like Cloudflare and Sucuri. Please see our available %1$sAdd-ons%2$s."
msgstr "Není nutné pro služby jako je Cloudflare nebo Sucuri. Podívejte se na dostupné %1$srozšíření%2$s."

#: inc/Engine/Admin/Settings/Page.php:1616
#: inc/admin/options.php:132
msgid "Exclude files from CDN"
msgstr "Vyloučení souborů z CDN"

#: inc/Engine/Admin/Settings/Page.php:1667
msgid "Enable Content Delivery Network"
msgstr "Povolit Content Delivery Network"

#: inc/Engine/Admin/Settings/Page.php:1676
#: inc/Engine/CDN/RocketCDN/AdminPageSubscriber.php:159
msgid "CDN CNAME(s)"
msgstr "CDN CNAME(s)"

#: inc/Engine/Admin/Settings/Page.php:1677
#: inc/Engine/CDN/RocketCDN/AdminPageSubscriber.php:160
msgid "Specify the CNAME(s) below"
msgstr "Níže určete CNAME(s)"

#: inc/Engine/Admin/Settings/Page.php:1684
msgid "Specify URL(s) of files that should not get served via CDN (one per line)."
msgstr "Zadejte URL adresy souborů, které by neměly být se neměly zobrazovat přes CDN (jednu na řádek)."

#: inc/Engine/Admin/Settings/Page.php:1685
msgid "The domain part of the URL will be stripped automatically.<br>Use (.*) wildcards to exclude all files of a given file type located at a specific path."
msgstr "Doménová část URL adresy bude automaticky odstraněna.<br>Použijte (.*) hvězdičky pro vyloučení všech souborů, daného typu, umístěných v konkrétní cestě."

#: inc/Engine/Admin/Settings/Page.php:1708
#: inc/Engine/Admin/Settings/Page.php:1716
msgid "Heartbeat"
msgstr "Heartbeat"

#: inc/Engine/Admin/Settings/Page.php:1709
msgid "Control WordPress Heartbeat API"
msgstr "Správa WordPress Heartbeat API"

#: inc/Engine/Admin/Settings/Page.php:1717
msgid "Reducing or disabling the Heartbeat API’s activity can help save some of your server’s resources."
msgstr "Omezení nebo zakázání aktivity Heartbeat API může snížit zatěžování serveru."

#: inc/Engine/Admin/Settings/Page.php:1723
msgid "Reduce or disable Heartbeat activity"
msgstr "Správa Heartbeat aktivity"

#: inc/Engine/Admin/Settings/Page.php:1724
msgid "Reducing activity will change Heartbeat frequency from one hit each minute to one hit every 2 minutes."
msgstr "Omezení aktivity změní frekvenci Heartbeat, z jednou za minutu na jednou za 2 minuty."

#: inc/Engine/Admin/Settings/Page.php:1724
msgid "Disabling Heartbeat entirely may break plugins and themes using this API."
msgstr "Úplné vypnutí Heartbeat může znefunkčnit pluginy a šablony používající toto API."

#: inc/Engine/Admin/Settings/Page.php:1738
msgid "Do not limit"
msgstr "Neomezovat"

#: inc/Engine/Admin/Settings/Page.php:1739
msgid "Reduce activity"
msgstr "Omezit aktivitu"

#: inc/Engine/Admin/Settings/Page.php:1740
msgid "Disable"
msgstr "Vypnout"

#: inc/Engine/Admin/Settings/Page.php:1748
msgid "Control Heartbeat"
msgstr "Ovládat Heartbeat"

#: inc/Engine/Admin/Settings/Page.php:1757
msgid "Behavior in backend"
msgstr "Chování v administraci"

#: inc/Engine/Admin/Settings/Page.php:1764
msgid "Behavior in post editor"
msgstr "Chování v editoru stránek/příspěvků"

#: inc/Engine/Admin/Settings/Page.php:1770
msgid "Behavior in frontend"
msgstr "Chování ve veřejné části webu"

#: inc/Engine/Admin/Settings/Page.php:1787
#: views/settings/page-sections/tutorials.php:39
msgid "Add-ons"
msgstr "Rozšíření"

#: inc/Engine/Admin/Settings/Page.php:1788
msgid "Add more features"
msgstr "Přidání dalších funkcí"

#: inc/Engine/Admin/Settings/Page.php:1795
msgid "One-click Rocket Add-ons"
msgstr "Rocket rozšíření na jedno kliknutí"

#: inc/Engine/Admin/Settings/Page.php:1796
msgid "One-Click Add-ons are features extending available options without configuration needed. Switch the option \"on\" to enable from this screen."
msgstr "Rocket rozšíření na jedno kliknutí jsou funkce rozšiřující dostupné možnosti bez nutnosti další konfigurace. Přepněte tlačítko na „zap“ pro aktivaci z této stránky."

#: inc/Engine/Admin/Settings/Page.php:1806
msgid "Rocket Add-ons"
msgstr "Rocket rozšíření"

#: inc/Engine/Admin/Settings/Page.php:1807
msgid "Rocket Add-ons are complementary features extending available options."
msgstr "Rocket rozšíření jsou doplňkové funkce rozšiřující dostupné možnosti."

#: inc/Engine/Admin/Settings/Page.php:1817
#: inc/Engine/Admin/Settings/Page.php:1986
msgid "Cloudflare"
msgstr "Cloudflare"

#: inc/Engine/Admin/Settings/Page.php:1823
msgid "Integrate your Cloudflare account with this add-on."
msgstr "Integrujte svůj účet Cloudflare tímto rozšířením."

#: inc/Engine/Admin/Settings/Page.php:1824
msgid "Provide your account email, global API key, and domain to use options such as clearing the Cloudflare cache and enabling optimal settings with WP Rocket."
msgstr "Zadejte email účtu, globální API klíč, a doménu, abyste mohli využívat možnosti jako je vymazání mezipaměti Cloudflare a aktivovat optimální nastavení pro WP Rocket."

#: inc/Engine/Admin/Settings/Page.php:1875
msgid "Varnish"
msgstr "Varnish"

#: inc/Engine/Admin/Settings/Page.php:1881
msgid "If Varnish runs on your server, you must activate this add-on."
msgstr "Pokud na vašem serveru používáte Varnish, musíte aktivovat toto rozšíření."

#: inc/Engine/Admin/Settings/Page.php:1883
#, php-format
msgid "Varnish cache will be purged each time WP Rocket clears its cache to ensure content is always up-to-date.<br>%1$sLearn more%2$s"
msgstr "Mezipaměť Varnish bude pročištěna vždy současně s čištěním mezipaměti WP Rocket, aby byla vždy zajištěna aktuálnost obsahu.<br>%1$sVíce informací%2$s"

#: inc/Engine/Admin/Settings/Page.php:1948
msgid "Clear the Sucuri cache when WP Rocket’s cache is cleared."
msgstr "Vymaže společně mezipaměť Sucuri i WP Rocket."

#: inc/Engine/Admin/Settings/Page.php:1951
msgid "Provide your API key to clear the Sucuri cache when WP Rocket’s cache is cleared."
msgstr "Zadejte svůj API klíč pro společné čištění mezipaměti WP Rocket i Sucuri."

#: inc/Engine/Admin/Settings/Page.php:1959
#: inc/Engine/Admin/Settings/Page.php:2103
msgid "Sucuri"
msgstr "Sucuri"

#: inc/Engine/Admin/Settings/Page.php:1965
msgid "Synchronize Sucuri cache with this add-on."
msgstr "Synchronizujte mezipaměť Sucuri pomoci tohoto rozšíření."

#: inc/Engine/Admin/Settings/Page.php:2003
msgid "Cloudflare credentials"
msgstr "Údaje ke Cloudflare"

#: inc/Engine/Admin/Settings/Page.php:2012
msgid "Cloudflare settings"
msgstr "Cloudflare nastavení"

#: inc/Engine/Admin/Settings/Page.php:2026
msgctxt "Cloudflare"
msgid "Global API key:"
msgstr "Globální klíč API:"

#: inc/Engine/Admin/Settings/Page.php:2027
msgctxt "Cloudflare"
msgid "Find your API key"
msgstr "Nemůžete najít svůj API klíč?"

#: inc/Engine/Admin/Settings/Page.php:2039
msgctxt "Cloudflare"
msgid "Account email"
msgstr "Přihlašovací email"

#: inc/Engine/Admin/Settings/Page.php:2048
msgctxt "Cloudflare"
msgid "Zone ID"
msgstr "ID zóny"

#: inc/Engine/Admin/Settings/Page.php:2058
msgid "Development mode"
msgstr "Vývojový režim"

#: inc/Engine/Admin/Settings/Page.php:2060
#, php-format
msgid "Temporarily activate development mode on your website. This setting will automatically turn off after 3 hours. %1$sLearn more%2$s"
msgstr "Dočasně na webu zapne vývojový režim. Po 3 hodinách se toto nastavení automaticky vypne. %1$sVíce informací%2$s"

#: inc/Engine/Admin/Settings/Page.php:2068
msgid "Optimal settings"
msgstr "Optimální nastavení"

#: inc/Engine/Admin/Settings/Page.php:2069
msgid "Automatically enhances your Cloudflare configuration for speed, performance grade and compatibility."
msgstr "Automaticky vylepší konfiguraci služby Cloudflare pro vyšší rychlost, lepší výkon a kompatibilitu."

#: inc/Engine/Admin/Settings/Page.php:2077
msgid "Relative protocol"
msgstr "Relativní protokol"

#: inc/Engine/Admin/Settings/Page.php:2078
msgid "Should only be used with Cloudflare's flexible SSL feature. URLs of static files (CSS, JS, images) will be rewritten to use // instead of http:// or https://."
msgstr "Mělo by být použito jen s Cloudflare funkcí „flexible SSL“. URL adresy statických souborů (CSS, JS, obrázky) budou používat „//“ místo „http://“ nebo „https://“."

#: inc/Engine/Admin/Settings/Page.php:2116
msgid "Sucuri credentials"
msgstr "Sucuri údaje"

#: inc/Engine/Admin/Settings/Page.php:2130
msgctxt "Sucuri"
msgid "Find your API key"
msgstr "Nemůžete najít svůj API klíč?"

#: inc/Engine/Admin/Settings/Render.php:422
#: inc/deprecated/deprecated.php:1294
msgid "Upload file and import settings"
msgstr "Nahrát soubor a importovat nastavení"

#: inc/Engine/Admin/Settings/Settings.php:362
msgid "Sucuri Add-on: The API key for the Sucuri firewall must be in format <code>{32 characters}/{32 characters}</code>."
msgstr "Sucuri Rozšíření: API klíč pro Sucuri firewall musí být ve formátu<code>{32 znaků}/{32 znaků}</code>."

#: inc/Engine/Admin/Settings/Settings.php:452
#: inc/deprecated/deprecated.php:1245
msgid "Settings saved."
msgstr "Nastavení bylo uloženo."

#: inc/Engine/Admin/Settings/Subscriber.php:168
#: inc/deprecated/deprecated.php:1786
#: views/settings/page-sections/tools.php:33
msgid "Tools"
msgstr "Nástroje"

#: inc/Engine/Admin/Settings/Subscriber.php:169
msgid "Import, Export, Rollback"
msgstr "Import, export, návrat k předchozí verzi"

#: inc/Engine/Admin/Settings/Subscriber.php:194
#: views/settings/page-sections/imagify.php:14
msgid "Image Optimization"
msgstr "Optimalizace obrázků"

#: inc/Engine/Admin/Settings/Subscriber.php:195
msgid "Compress your images"
msgstr "Zmenší velikost obrázků"

#: inc/Engine/Admin/Settings/Subscriber.php:212
#: views/settings/page-sections/tutorials.php:48
msgid "Tutorials"
msgstr "Návody"

#: inc/Engine/Admin/Settings/Subscriber.php:213
msgid "Getting started and how to videos"
msgstr "Videa s návody nejen pro začátečníky"

#: inc/Engine/CDN/RocketCDN/APIClient.php:177
msgid "RocketCDN cache purge failed: Missing identifier parameter."
msgstr "Vymazání mezipaměti RocketCDN se nezdařilo: Chybí parametr identifikátoru."

#: inc/Engine/CDN/RocketCDN/APIClient.php:186
msgid "RocketCDN cache purge failed: Missing user token."
msgstr "Vymazání mezipaměti RocketCDN se nezdařilo: Chybí uživatelský token."

#: inc/Engine/CDN/RocketCDN/APIClient.php:212
msgid "RocketCDN cache purge failed: The API returned an unexpected response code."
msgstr "Vymazání mezipaměti RocketCDN se nezdařilo: API vrátilo kód neočekávané odpovědi."

#: inc/Engine/CDN/RocketCDN/APIClient.php:221
msgid "RocketCDN cache purge failed: The API returned an empty response."
msgstr "Vymazání mezipaměti RocketCDN se nezdařilo: API vrátilo prázdnou odpověď."

#: inc/Engine/CDN/RocketCDN/APIClient.php:230
msgid "RocketCDN cache purge failed: The API returned an unexpected response."
msgstr "Vymazání mezipaměti RocketCDN se nezdařilo: API vrátilo neočekávanou odpověď."

#: inc/Engine/CDN/RocketCDN/APIClient.php:239
#, php-format
msgid "RocketCDN cache purge failed: %s."
msgstr "Vymazání mezipaměti RocketCDN se nezdařilo: %s."

#: inc/Engine/CDN/RocketCDN/APIClient.php:247
msgid "RocketCDN cache purge successful."
msgstr "Vyčištění mezipaměti RocketCDN bylo úspěšně dokončeno."

#: inc/Engine/CDN/RocketCDN/AdminPageSubscriber.php:90
msgid "Next Billing Date"
msgstr "Datum příští fakturace"

#: inc/Engine/CDN/RocketCDN/AdminPageSubscriber.php:99
msgid "No Subscription"
msgstr "Žádné předplatné"

#: inc/Engine/CDN/RocketCDN/AdminPageSubscriber.php:135
msgid "Your RocketCDN subscription is currently active."
msgstr "Vaše předplatné pro RocketCDN je aktuálně aktivní."

#: inc/Engine/CDN/RocketCDN/AdminPageSubscriber.php:141
#, php-format
msgid "To use RocketCDN, replace your CNAME with %1$s%2$s%3$s."
msgstr "Chcete-li používat RocketCDN, své stávající CNAME nahraďte %1$s%2$s%3$s."

#: inc/Engine/CDN/RocketCDN/AdminPageSubscriber.php:152
#: inc/Engine/CDN/RocketCDN/NoticesSubscriber.php:230
#: inc/Engine/CDN/RocketCDN/NoticesSubscriber.php:334
#, php-format
msgid "%1$sMore Info%2$s"
msgstr "%1$sVíce informací%2$s"

#: inc/Engine/CDN/RocketCDN/RESTSubscriber.php:137
msgid "RocketCDN enabled"
msgstr "RocketCDN zapnuto"

#: inc/Engine/CDN/RocketCDN/RESTSubscriber.php:160
msgid "RocketCDN disabled"
msgstr "RocketCDN vypnuto"

#: inc/Engine/Cache/AdminSubscriber.php:118
#: inc/admin/admin.php:96
#: inc/admin/admin.php:118
#: inc/deprecated/3.5.php:898
msgid "Clear this cache"
msgstr "Vymazat tuto mezipaměť"

#: inc/Engine/CriticalPath/APIClient.php:173
#, php-format
msgid "Critical CSS for %1$s not generated. Error: The API returned an empty response."
msgstr "Kritické CSS pro %1$s nebylo vygenerováno. Chyba: API vrátilo prázdnou odpověď."

#: inc/Engine/CriticalPath/APIClient.php:187
#: inc/Engine/CriticalPath/ProcessorService.php:194
#, php-format
msgid "Critical CSS for %1$s not generated."
msgstr "Kritické CSS pro %1$s nebylo vygenerováno."

#: inc/Engine/CriticalPath/APIClient.php:197
#, php-format
msgid "Critical CSS for %1$s not generated. Error: The API returned an invalid response code."
msgstr "Kritické CSS pro %1$s nebylo vygenerováno. Chyba: API vrátilo neplatný kód odpovědi."

#: inc/Engine/CriticalPath/APIClient.php:205
#: inc/Engine/CriticalPath/ProcessorService.php:201
#, php-format
msgid "Error: %1$s"
msgstr "Chyba: %1$s"

#: inc/Engine/CriticalPath/Admin/Post.php:144
#: views/cpcss/metabox/generate.php:47
msgid "Generate Specific CPCSS"
msgstr "Generovat konkrétní CPCSS"

#: inc/Engine/CriticalPath/Admin/Post.php:145
#: views/cpcss/metabox/generate.php:45
msgid "Regenerate specific CPCSS"
msgstr "Znovu vygenerovat specifické CPCSS"

#: inc/Engine/CriticalPath/Admin/Post.php:219
msgid "%l to use this feature."
msgstr "%l pro použití této funkce."

#: inc/Engine/CriticalPath/Admin/Post.php:222
#, php-format
msgid "Publish the %s"
msgstr "Publikovat %s"

#: inc/Engine/CriticalPath/APIClient.php:64
#, php-format
msgid "Critical CSS for %1$s not generated. Error: %2$s"
msgstr "Kritické CSS pro %1$s nebylo vygenerováno. Chyba: %2$s"

#: inc/Engine/CriticalPath/ProcessorService.php:273
#, php-format
msgid "Critical CSS for %s generated."
msgstr "Kritické CSS pro %s bylo vygenerováno."

#: inc/Engine/CriticalPath/CriticalCSSSubscriber.php:176
msgid "Critical CSS generation is currently running."
msgstr "Právě běží generování kritického CSS."

#: inc/Engine/CriticalPath/CriticalCSSSubscriber.php:181
#, php-format
msgid "Go to the %1$sWP Rocket settings%2$s page to track progress."
msgstr "Přejděte na %1$s nastavení WP Rocket%2$s a sledujte průběh."

#: inc/Engine/CriticalPath/CriticalCSSSubscriber.php:396
#, php-format
msgid "Critical CSS generation is currently running: %1$d of %2$d page types completed. (Refresh this page to view progress)"
msgstr "Právě probíhá generování kritického CSS: %1$d z %2$d typů obsahu bylo dokončen. (Pro zobrazení pokroku aktualizujte tuto stránku)"

#: inc/Engine/CriticalPath/CriticalCSSSubscriber.php:472
#, php-format
msgid "Critical CSS generation finished for %1$d of %2$d page types."
msgstr "Generování kritického CSS bylo dokončeno pro %1$d z %2$d typů obsahu."

#: inc/Engine/CriticalPath/CriticalCSSSubscriber.php:479
msgid "Critical CSS generation encountered one or more errors."
msgstr "Během generování kritického CSS nastala jedna nebo více chyb."

#: inc/Engine/CriticalPath/CriticalCSSSubscriber.php:479
msgid "Learn more."
msgstr "Více informací."

#: inc/Engine/CriticalPath/DataManager.php:108
msgid "Critical CSS file does not exist"
msgstr "Soubor kritického CSS neexistuje"

#: inc/Engine/CriticalPath/DataManager.php:122
msgid "Critical CSS file cannot be deleted"
msgstr "Soubor kritického CSS nelze smazat."

#: inc/Engine/CriticalPath/ProcessorService.php:228
#, php-format
msgid "Critical CSS for %s in progress."
msgstr "Probíhá generování kritického CSS pro %s."

#: inc/Engine/CriticalPath/ProcessorService.php:295
msgid "Critical CSS file deleted successfully."
msgstr "Soubor kritického CSS byl úspěšně smazán."

#: inc/Engine/CriticalPath/ProcessorService.php:330
#, php-format
msgid "Critical CSS for %1$s timeout. Please retry a little later."
msgstr "Během vytváření kritického CSS pro %1$s vypršel časový limit. Zkuste to později."

#: inc/Engine/CriticalPath/RESTWPPost.php:36
#: inc/Engine/CriticalPath/RESTWPPost.php:69
msgid "Requested post does not exist."
msgstr "Požadovaný příspěvek neexistuje."

#: inc/Engine/CriticalPath/RESTWPPost.php:46
msgid "Cannot generate CPCSS for unpublished post."
msgstr "Pro nepublikovaný příspěvek nelze generovat CPCSS."

#: inc/Engine/HealthCheck/HealthCheck.php:82
#: inc/deprecated/3.5.php:858
msgid "The following scheduled event failed to run. This may indicate the CRON system is not running properly, which can prevent some WP Rocket features from working as intended:"
msgid_plural "The following scheduled events failed to run. This may indicate the CRON system is not running properly, which can prevent some WP Rocket features from working as intended:"
msgstr[0] "Nepodařilo se spustit následující plánovanou událost. Systém CRON pravděpodobně nepracuje správně, což může zabránit správnému chodu některých funkcí WP Rocket:"
msgstr[1] "Nepodařilo se spustit následující plánované události. Systém CRON pravděpodobně nepracuje správně, což může zabránit správnému chodu některých funkcí WP Rocket:"
msgstr[2] "Nepodařilo se spustit následující plánované události. Systém CRON pravděpodobně nepracuje správně, což může zabránit správnému chodu některých funkcí WP Rocket:"
msgstr[3] "Nepodařilo se spustit následující plánované události. Systém CRON pravděpodobně nepracuje správně, což může zabránit správnému chodu některých funkcí WP Rocket:"

#: inc/Engine/HealthCheck/HealthCheck.php:88
#: inc/deprecated/3.5.php:867
msgid "Please contact your host to check if CRON is working."
msgstr "Obraťte se na poskytovatele hostingu a zjistěte, zda je CRON funkční."

#: inc/Engine/HealthCheck/HealthCheck.php:142
msgid "Scheduled Cache Purge"
msgstr "Naplánováno vyčištění mezipaměti"

#: inc/Engine/HealthCheck/HealthCheck.php:143
msgid "Scheduled Database Optimization"
msgstr "Naplánována optimalizace databáze"

#: inc/Engine/HealthCheck/HealthCheck.php:144
msgid "Database Optimization Process"
msgstr "Probíhá optimalizace databáze"

#: inc/Engine/HealthCheck/HealthCheck.php:145
msgctxt "noun"
msgid "Preload"
msgstr "Předběžné načítání"

#: inc/Engine/HealthCheck/HealthCheck.php:146
msgid "Critical Path CSS Generation Process"
msgstr "Proces generování cesty pro kritické CSS"

#: inc/ThirdParty/Plugins/Optimization/Hummingbird.php:78
#, php-format
msgctxt "Hummingbird notice"
msgid "Please deactivate the following %s option which conflicts with WP Rocket features:"
msgid_plural "Please deactivate the following %s options which conflict with WP Rocket features:"
msgstr[0] "Deaktivujte následující možnost %s, která je v konfliktu s funkcemi WP Rocket:"
msgstr[1] "Deaktivujte následující možnosti %s, které jsou v konfliktu s funkcemi WP Rocket:"
msgstr[2] "Deaktivujte následující možnosti %s, které jsou v konfliktu s funkcemi WP Rocket:"
msgstr[3] "Deaktivujte následující možnosti %s, které jsou v konfliktu s funkcemi WP Rocket:"

#: inc/ThirdParty/Plugins/Optimization/Hummingbird.php:151
#, php-format
msgctxt "Hummingbird notice"
msgid "%1$s %2$sdisable emoji%3$s conflicts with WP Rockets %2$sdisable emoji%3$s"
msgstr "%1$s %2$szakázání smajlíků%3$s je v konfliktu s WP Rocket %2$szakázáním smajlíků%3$s"

#: inc/ThirdParty/Plugins/Optimization/Hummingbird.php:187
#, php-format
msgctxt "Hummingbird notice"
msgid "%1$s %2$sGZIP compression%3$s conflicts with WP Rocket %2$sGZIP compression%3$s"
msgstr "%1$s %2$sGZIP komprese%3$s je v konfliktu s WP Rocket %2$sGZIP kompresí%3$s"

#: inc/ThirdParty/Plugins/Optimization/Hummingbird.php:223
#, php-format
msgctxt "Hummingbird notice"
msgid "%1$s %2$sbrowser caching%3$s conflicts with WP Rocket %2$sbrowser caching%3$s"
msgstr "%1$s %2$scachování prohlížeče%3$s je v konfliktu s WP Rocket %2$smezipamětí prohlížeče%3$s"

#: inc/ThirdParty/Plugins/Optimization/Hummingbird.php:255
#, php-format
msgctxt "Hummingbird notice"
msgid "%1$s %2$spage caching%3$s conflicts with WP Rocket %2$spage caching%3$s"
msgstr "%1$s %2$scachování stránek%3$s je v konfliktu s WP Rocket %2$sukládáním stránek do mezipaměti%3$s"

#: inc/ThirdParty/Plugins/Optimization/Hummingbird.php:289
#, php-format
msgctxt "Hummingbird notice"
msgid "%1$s %2$sasset optimization%3$s conflicts with WP Rocket %2$sfile optimization%3$s"
msgstr "%1$s %2$sprostředek optimalizace%3$s je v konfliktu s WP Rocket %2$soptimalizací souborů%3$s"

#: inc/ThirdParty/Plugins/Smush.php:108
#: inc/ThirdParty/Plugins/Smush.php:126
msgid "Smush"
msgstr "Smush"

#: inc/admin/admin.php:18
#: inc/common/admin-bar.php:255
#: inc/deprecated/deprecated.php:1787
msgid "Support"
msgstr "Podpora"

#: inc/admin/admin.php:20
msgid "Docs"
msgstr "Dokumentace"

#: inc/admin/admin.php:22
#: inc/common/admin-bar.php:243
msgid "FAQ"
msgstr "Časté dotazy"

#: inc/admin/admin.php:24
#: inc/common/admin-bar.php:63
msgid "Settings"
msgstr "Nastavení"

#: inc/Engine/Plugin/UpdaterSubscriber.php:472
#: inc/Engine/Plugin/UpdaterSubscriber.php:486
#: inc/classes/class-wp-rocket-requirements-check.php:203
#: inc/classes/class-wp-rocket-requirements-check.php:214
#: inc/deprecated/3.11.php:236
#: inc/deprecated/3.11.php:251
#, php-format
msgid "%s Update Rollback"
msgstr "Obnovení předchozí verze %s"

#: inc/Engine/Plugin/UpdaterSubscriber.php:509
#: inc/deprecated/3.11.php:279
#, php-format
msgid "%1$sReturn to WP Rocket%2$s or %3$sgo to Plugins page%2$s"
msgstr "%1$sZpět k WP Rocket%2$s nebo %3$spřejít na stránku pluginů%2$s"

#: inc/admin/admin.php:458
msgid "Settings import failed: you do not have the permissions to do this."
msgstr "Selhal import nastavení: pro tuto akci nemáte dostatečné oprávnění."

#: inc/admin/admin.php:462
msgid "Settings import failed: no file uploaded."
msgstr "Selhal import nastavení: žádný soubor nebyl nahrán."

#: inc/admin/admin.php:466
msgid "Settings import failed: incorrect filename."
msgstr "Selhal import nastavení: nesprávný název souboru."

#: inc/admin/admin.php:477
msgid "Settings import failed: incorrect filetype."
msgstr "Selhal import nastavení: nesprávný typ souboru."

#: inc/admin/admin.php:487
msgid "Settings import failed: "
msgstr "Selhal import nastavení: "

#: inc/admin/admin.php:503
msgid "Settings import failed: unexpected file content."
msgstr "Selhal import nastavení: nesprávný typ souboru."

#: inc/admin/admin.php:533
msgid "Settings imported and saved."
msgstr "Nastavení bylo importováno a uloženo."

#: inc/admin/options.php:160
msgid "The following pattern is invalid and has been removed:"
msgid_plural "The following patterns are invalid and have been removed:"
msgstr[0] "Následující vzorek je neplatný a byl odebrán:"
msgstr[1] "Následující vzorek jsou neplatné a byly odebrány:"
msgstr[2] "Následující vzorek jsou neplatné a byly odebrány:"
msgstr[3] "Následující vzorek jsou neplatné a byly odebrány:"

#: inc/admin/ui/meta-boxes.php:37
#: inc/admin/ui/notices.php:748
#: inc/common/admin-bar.php:84
#: inc/common/admin-bar.php:136
#: views/settings/page-sections/dashboard.php:150
msgid "Clear cache"
msgstr "Vymazat mezipaměť"

#: inc/admin/ui/meta-boxes.php:67
msgid "WP Rocket Options"
msgstr "Možnosti WP Rocket"

#: inc/admin/ui/meta-boxes.php:96
msgid "Never cache this page"
msgstr "Nikdy tuto stránku neukládat do mezipaměti"

#: inc/admin/ui/meta-boxes.php:100
msgid "Activate these options on this post:"
msgstr "Aktivujte možnosti jen pro tento příspěvek:"

#: inc/admin/ui/meta-boxes.php:103
msgid "LazyLoad for images"
msgstr "LazyLoad pro obrázky"

#: inc/admin/ui/meta-boxes.php:104
msgid "LazyLoad for iframes/videos"
msgstr "LazyLoad pro iframe/videa"

#: inc/admin/ui/meta-boxes.php:105
msgid "Minify/combine CSS"
msgstr "Zmenšit/zkombinovat CSS"

#: inc/admin/ui/meta-boxes.php:107
msgid "Minify/combine JS"
msgstr "Zmenšit/zkombinovat JS"

#: inc/admin/ui/meta-boxes.php:110
msgid "Defer JS"
msgstr "Pozdržet JS"

#: inc/admin/ui/meta-boxes.php:117
#, php-format
msgid "Activate first the %s option."
msgstr "Nejprve aktivujte možnost %s."

#: inc/admin/ui/meta-boxes.php:133
#, php-format
msgid "%1$sNote:%2$s None of these options will be applied if this post has been excluded from cache in the global cache settings."
msgstr "%1$sPoznámka:%2$s Žádné z těchto nastavení nebude použito, pokud jste tomuto příspěvku zakázali ukládání do mezipaměti globálním nastavením WP Rocket."

#: inc/admin/ui/notices.php:31
#: inc/admin/ui/notices.php:44
#, php-format
msgid ""
"<strong>%1$s</strong> has not been deactivated due to missing writing permissions.<br>\n"
"Make <strong>%2$s</strong> writeable and retry deactivation, or force deactivation now:"
msgstr ""
"<strong>%1$s</strong> nebyl deaktivován z důvodu chybějících práv k zápisu.<br>\n"
"Nastavte souboru <strong>%2$s</strong> práva pro zápis a zkuste plugin opět deaktivovat, nebo proveďte vynucenou deaktivaci:"

#: inc/admin/ui/notices.php:97
#, php-format
msgid "<strong>%s</strong>: One or more plugins have been enabled or disabled, clear the cache if they affect the front end of your site."
msgstr "<strong>%s</strong>: Jeden nebo více pluginů bylo aktivováno nebo deaktivováno. Pokud mají vlil na veřejnou část webu, pak raději vymažte mezipaměť."

#: inc/admin/ui/notices.php:218
#, php-format
msgid "<strong>%s</strong>: The following plugins are not compatible with this plugin and may cause unexpected results:"
msgstr "<strong>%s</strong>: Následující pluginy nejsou kompatibilní s tímto pluginem a mohou způsobit neočekávané výsledky:"

#: inc/admin/ui/notices.php:224
msgid "Deactivate"
msgstr "Deaktivovat"

#: inc/admin/ui/notices.php:266
msgid "WP Rocket Footer JS is not an official add-on. It prevents some options in WP Rocket from working correctly. Please deactivate it if you have problems."
msgstr "WP Rocket Footer JS je neoficiální doplněk. Některá nastavení v kombinaci s tímto doplňkem správně nefungují. Pokud se vyskytnou problémy s WP Rocket, deaktivujte jej prosím."

#: inc/admin/ui/notices.php:306
#, php-format
msgid "Endurance Cache is currently enabled, which will conflict with WP Rocket Cache. Please set the Endurance Cache cache level to Off (Level 0) on the %1$sSettings > General%2$s page to prevent any issues."
msgstr "Nyní je aktivní Endurance Cache, která bude v konfliktu s mezipamětí WP Rocket. Nastavte úroveň Endurance Cache na Off (úroveň 0) na stránce %1$sSettings > General %2$s, aby se zabránilo problémům."

#: inc/admin/ui/notices.php:327
#, php-format
msgid "%1$s: A custom permalink structure is required for the plugin to work properly. %2$sGo to permalinks settings%3$s"
msgstr "%1$s: Pro správnou funkci pluginu je vyžadována vlastní struktura trvalých odkazů. %2$sPřejděte na nastavení trvalých odkazů%3$s"

#: inc/admin/ui/notices.php:374
#, php-format
msgid "%s could not modify the .htaccess file due to missing writing permissions."
msgstr "%s nemohlo upravit soubor .htaccess kvůli omezených práv pro zápis."

#: inc/admin/ui/notices.php:380
#: inc/admin/ui/notices.php:843
#, php-format
msgid "Troubleshoot: %1$sHow to make system files writeable%2$s"
msgstr "Odstraňování problémů: %1$sJakudělit systémovým souborům práva k zápisu%2$s"

#: inc/admin/ui/notices.php:382
#: inc/admin/ui/notices.php:845
msgid "https://docs.wp-rocket.me/article/626-how-to-make-system-files-htaccess-wp-config-writeable/?utm_source=wp_plugin&utm_medium=wp_rocket"
msgstr "https://docs.wp-rocket.me/article/626-how-to-make-system-files-htaccess-wp-config-writeable/?utm_source=wp_plugin&utm_medium=wp_rocket"

#: inc/admin/ui/notices.php:388
msgid "Don’t worry, WP Rocket’s page caching and settings will still function correctly."
msgstr "Nebojte se, nastavení a mezipaměť WP Rocket bude pořád správně fungovat."

#: inc/admin/ui/notices.php:388
msgid "For optimal performance, adding the following lines into your .htaccess is recommended (not required):"
msgstr "Pro optimální funkčnost byste měli přidat následující řádky do souboru .htaccess (nepovinné):"

#: inc/admin/ui/notices.php:535
#, php-format
msgid "%1$s is good to go! %2$sTest your load time%4$s, or visit your %3$ssettings%4$s."
msgstr "Plugin %1$s je připraven! %2$sOtestujte si rychlost načítání%4$s, nebo přejděte na %3$snastavení%4$s."

#: inc/admin/ui/notices.php:576
msgid "Would you allow WP Rocket to collect non-sensitive diagnostic data from this website?"
msgstr "Chcete WP Rocket povolit sběr diagnostických dat z tohoto webu?"

#: inc/admin/ui/notices.php:577
msgid "This would help us to improve WP Rocket for you in the future."
msgstr "Pomůže to do budoucna zlepšit WP Rocket podle Vás, uživatelů."

#: inc/admin/ui/notices.php:583
msgid "What info will we collect?"
msgstr "Jaké údaje budou shromažďovány?"

#: inc/admin/ui/notices.php:588
msgid "Below is a detailed view of all data WP Rocket will collect if granted permission. WP Rocket will never transmit any domain names or email addresses (except for license validation), IP addresses, or third-party API keys."
msgstr "Níže je podrobné zobrazení všech údajů, které bude v případě povolení oprávnění WP Rocket shromažďovat. WP Rocket nikdy nebude posílat názvy domén nebo emailové adresy (kromě ověření licence), IP adresy nebo API klíče třetích stran."

#: inc/admin/ui/notices.php:597
msgid "Yes, allow"
msgstr "Ano, povolit"

#: inc/admin/ui/notices.php:600
msgid "No, thanks"
msgstr "Ne"

#: inc/admin/ui/notices.php:639
msgid "Thank you!"
msgstr "Děkujeme Vám!"

#: inc/admin/ui/notices.php:644
msgid "WP Rocket now collects these metrics from your website:"
msgstr "WP Rocket nyní z tohoto webu shromažďuje následující údaje:"

#: inc/admin/ui/notices.php:682
#, php-format
msgid "%s: Cache cleared."
msgstr "%s: Mezipaměť byla vymazána."

#: inc/admin/ui/notices.php:689
#, php-format
msgid "%s: Post cache cleared."
msgstr "%s: Mezipaměť příspěvku byla vymazána."

#: inc/admin/ui/notices.php:696
#, php-format
msgid "%s: Term cache cleared."
msgstr "%s: Mezipaměť položky byla vymazána."

#: inc/admin/ui/notices.php:703
#, php-format
msgid "%s: User cache cleared."
msgstr "%s: Uživatelská mezipaměť byla vymazána."

#: inc/admin/ui/notices.php:751
msgid "Stop Preload"
msgstr "Zastavit prednačítání"

#: inc/admin/ui/notices.php:781
msgid "Force deactivation "
msgstr "Vynutit vypnutí "

#: inc/admin/ui/notices.php:800
msgid "The following code should have been written to this file:"
msgstr "Následující kód by měl být zapsán do tohoto souboru:"

#: inc/admin/ui/notices.php:831
#, php-format
msgid "%s cannot configure itself due to missing writing permissions."
msgstr "%s se nemůže nastavit kvůli chybějícím oprávněním k zápisu."

#: inc/admin/ui/notices.php:837
#, php-format
msgid "Affected file/folder: %s"
msgstr "Ovlivněný soubor/složka: %s"

#: inc/Engine/Admin/Database/Optimization.php:35
msgid "Transients"
msgstr "Dočasná data"

#: inc/Engine/Admin/Database/Optimization.php:36
msgid "Tables"
msgstr "Tabulky"

#: inc/classes/admin/class-logs.php:124
msgid "The debug file could not be deleted."
msgstr "Soubor ladění se nepodařilo vymazat."

#: inc/classes/class-wp-rocket-requirements-check.php:147
#, php-format
msgid "To function properly, %1$s %2$s requires at least:"
msgstr "Pro správnou funkčnost %1$s %2$s je vyžadováno alespoň:"

#: inc/classes/class-wp-rocket-requirements-check.php:151
#, php-format
msgid "PHP %1$s. To use this WP Rocket version, please ask your web host how to upgrade your server to PHP %1$s or higher."
msgstr "PHP %1$s. Pokud chcete používat tuto verzi WP Rocket, požádejte svůj webhosting o pomoc s upgradem serveru na PHP %1$s nebo vyšší."

#: inc/classes/class-wp-rocket-requirements-check.php:156
#, php-format
msgid "WordPress %1$s. To use this WP Rocket version, please upgrade WordPress to version %1$s or higher."
msgstr "WordPress %1$s. Pokud chcete používat tuto verzi WP Rocket, tak musíte aktualizovat WordPress na verzi %1$s nebo vyšší."

#: inc/classes/class-wp-rocket-requirements-check.php:159
msgid "If you are not able to upgrade, you can rollback to the previous version by using the button below."
msgstr "Pokud nemáte možnost přechodu na požadovanou verzi, můžete WP Rocket přeinstalovat na předchozí verzi, pomoci níže zobrazeného tlačítka."

#: inc/classes/class-wp-rocket-requirements-check.php:161
#: inc/deprecated/deprecated.php:1947
#, php-format
msgid "Re-install version %s"
msgstr "Přeinstalovat verzi %s"

#: inc/classes/logger/class-logger.php:260
#: inc/classes/logger/class-logger.php:292
msgid "The log file does not exist."
msgstr "Soubor protokolu neexistuje."

#: inc/classes/logger/class-logger.php:266
#: inc/classes/logger/class-logger.php:298
msgid "The log file could not be read."
msgstr "Soubor protokolu nelze číst."

#: inc/classes/logger/class-logger.php:285
msgid "The logs are not saved into a file."
msgstr "Protokoly se neukládají do souboru."

#: inc/Engine/Cache/PurgeExpired/Subscriber.php:75
msgid "WP Rocket Expired Cache Interval"
msgstr "Interval pro vypršení mezipaměti WP Rocket"

#: inc/Addon/WebP/AdminSubscriber.php:105
msgid "WebP cache is disabled by filter."
msgstr "Mezipaměť WebP je zakázána filtrem."

#: inc/Addon/WebP/AdminSubscriber.php:115
#: inc/Addon/WebP/AdminSubscriber.php:141
#, php-format
msgid "You are using %1$s to convert images to WebP. If you want WP Rocket to serve them for you, activate this option. %2$sMore info%3$s"
msgid_plural "You are using %1$s to convert images to WebP. If you want WP Rocket to serve them for you, activate this option. %2$sMore info%3$s"
msgstr[0] "Pro převod obrázků do formátu WebP používáte plugin %1$s . WP Rocket vytvoří samostatné soubory mezipaměti, které budou poskytovat WebP obrázky. Pokud chcete, aby je poskytoval WP Rocket, aktivujte tuto možnost. %2$sVíce informací%3$s"
msgstr[1] "Pro převod obrázků do formátu WebP používáte plugin %1$s . WP Rocket vytvoří samostatné soubory mezipaměti, které budou poskytovat WebP obrázky. Pokud chcete, aby je poskytoval WP Rocket, aktivujte tuto možnost. %2$sVíce informací%3$s"
msgstr[2] "Pro převod obrázků do formátu WebP používáte plugin %1$s . WP Rocket vytvoří samostatné soubory mezipaměti, které budou poskytovat WebP obrázky. Pokud chcete, aby je poskytoval WP Rocket, aktivujte tuto možnost. %2$sVíce informací%3$s"
msgstr[3] "Pro převod obrázků do formátu WebP používáte plugin %1$s . WP Rocket vytvoří samostatné soubory mezipaměti, které budou poskytovat WebP obrázky. Pokud chcete, aby je poskytoval WP Rocket, aktivujte tuto možnost. %2$sVíce informací%3$s"

#: inc/Addon/WebP/AdminSubscriber.php:127
#: inc/Addon/WebP/AdminSubscriber.php:153
#, php-format
msgid "You are using %1$s to convert images to WebP. WP Rocket will create separate cache files to serve your WebP images. %2$sMore info%3$s"
msgid_plural "You are using %1$s to convert images to WebP. WP Rocket will create separate cache files to serve your WebP images. %2$sMore info%3$s"
msgstr[0] "Pro převod obrázků do formátu WebP používáte plugin %1$s . WP Rocket vytvoří samostatné soubory mezipaměti, které budou poskytovat WebP obrázky. %2$sVíce informací%3$s"
msgstr[1] "Pro převod obrázků do formátu WebP používáte plugin %1$s . WP Rocket vytvoří samostatné soubory mezipaměti, které budou poskytovat WebP obrázky. %2$sVíce informací%3$s"
msgstr[2] "Pro převod obrázků do formátu WebP používáte plugin %1$s . WP Rocket vytvoří samostatné soubory mezipaměti, které budou poskytovat WebP obrázky. %2$sVíce informací%3$s"
msgstr[3] "Pro převod obrázků do formátu WebP používáte plugin %1$s . WP Rocket vytvoří samostatné soubory mezipaměti, které budou poskytovat WebP obrázky. %2$sVíce informací%3$s"

#: inc/Addon/WebP/AdminSubscriber.php:185
msgid "WP Rocket will create separate cache files to serve your WebP images."
msgstr "WP Rocket vytvoří samostatné soubory mezipaměti, které budou poskytovat WebP obrázky."

#: inc/classes/subscriber/Tools/class-detect-missing-tags-subscriber.php:151
#, php-format
msgid "Failed to detect the following requirement in your theme: closing %1$s."
msgid_plural "Failed to detect the following requirements in your theme: closing %1$s."
msgstr[0] "Nepodařilo se identifikovat následující požadavek šablony: uzavírací značky %1$s."
msgstr[1] "Nepodařilo se identifikovat následující požadavky šablony: uzavírací značky %1$s."
msgstr[2] "Nepodařilo se identifikovat následující požadavky šablony: uzavírací značky %1$s."
msgstr[3] "Nepodařilo se identifikovat následující požadavky šablony: uzavírací značky %1$s."

#: inc/classes/subscriber/Tools/class-detect-missing-tags-subscriber.php:159
msgid "https://docs.wp-rocket.me/article/99-pages-not-cached-or-minify-cssjs-not-working/?utm_source=wp_plugin&utm_medium=wp_rocket#theme"
msgstr "https://docs.wp-rocket.me/article/99-pages-not-cached-or-minify-cssjs-not-working/?utm_source=wp_plugin&utm_medium=wp_rocket#theme"

#: inc/Engine/Admin/Database/Subscriber.php:85
msgid "monthly"
msgstr "měsíčně"

#: inc/Engine/Admin/Database/Subscriber.php:194
msgid "Database optimization process is running"
msgstr "Probíhá optimalizace databáze"

#: inc/Engine/Admin/Database/Subscriber.php:224
msgid "Database optimization process is complete. Everything was already optimized!"
msgstr "Optimalizace databáze byla dokončena. Všechno již bylo optimalizováno!"

#: inc/Engine/Admin/Database/Subscriber.php:227
msgid "Database optimization process is complete. List of optimized items below:"
msgstr "Optimalizace databáze byla dokončena. Níže je zobrazen seznam optimalizovaných položek:"

#: inc/Engine/Admin/Database/Subscriber.php:235
#, php-format
msgid "%1$d %2$s optimized."
msgstr "Optimalizováno %1$d %2$s."

#: inc/Addon/Sucuri/Subscriber.php:95
#, php-format
msgid "Sucuri cache purge error: %s"
msgstr "Sucuri chyba vyprázdnění mezipaměti: %s"

#: inc/Addon/Sucuri/Subscriber.php:100
msgid "The Sucuri cache is being cleared. Note that it may take up to two minutes for it to be fully flushed."
msgstr "Čištění mezipaměti Sucuri bylo zahájeno. Nezapomeňte, že tato akce může trvat až dvě minuty."

#: inc/Addon/Sucuri/Subscriber.php:217
msgid "Sucuri firewall API key was not found."
msgstr "Sucuri API klíč firewallu nebyl nalezen."

#: inc/Addon/Sucuri/Subscriber.php:230
msgid "Sucuri firewall API key is invalid."
msgstr "Sucuri API klíč firewallu je neplatný."

#: inc/Addon/Sucuri/Subscriber.php:285
#, php-format
msgid "Error when contacting Sucuri firewall API. Error message was: %s"
msgstr "Chyba při kontaktování Sucuri firewall API. Chybová zpráva je následující: %s"

#: inc/Addon/Sucuri/Subscriber.php:300
msgid "Could not get a response from the Sucuri firewall API."
msgstr "Nepodařilo se dostat odpověď od Sucuri firewall API."

#: inc/Addon/Sucuri/Subscriber.php:315
msgid "Got an invalid response from the Sucuri firewall API."
msgstr "Byla přijata neplatná odpověď od Sucuri firewall API."

#: inc/Addon/Sucuri/Subscriber.php:329
msgid "The Sucuri firewall API returned an unknown error."
msgstr "Sucuri firewall API vrátilo neznámou chybu."

#: inc/Addon/Sucuri/Subscriber.php:333
#, php-format
msgid "The Sucuri firewall API returned the following error: %s"
msgid_plural "The Sucuri firewall API returned the following errors: %s"
msgstr[0] "Sucuri firewall API vrátilo následující chybu: %s"
msgstr[1] "Sucuri firewall API vrátilo následující chyby: %s"
msgstr[2] "Sucuri firewall API vrátilo následující chyby: %s"
msgstr[3] "Sucuri firewall API vrátilo následující chyby: %s"

#: inc/Engine/Plugin/UpdaterApiTools.php:32
#: inc/deprecated/3.3.php:761
#: inc/deprecated/3.3.php:770
#, php-format
msgid "An unexpected error occurred. Something may be wrong with WP-Rocket.me or this server&#8217;s configuration. If you continue to have problems, <a href=\"%s\">contact support</a>."
msgstr "Vyskytla se neočekávaná chyba. Chyba může být na straně WP-Rocket.me nebo v nastavení vašeho serveru. Pokud máte nadále problémy, <a href=\"%s\">kontaktujte technickou podporu</a>."

#: inc/common/admin-bar.php:124
#: inc/functions/i18n.php:41
#: inc/functions/i18n.php:51
msgid "All languages"
msgstr "Všechny jazyky"

#: inc/common/admin-bar.php:160
msgid "Clear this post"
msgstr "Vymazat mezipaměť tohoto příspěvku"

#: inc/common/admin-bar.php:174
msgid "Purge this URL"
msgstr "Vymazat mezipaměť pro tuto URL"

#: inc/Engine/CriticalPath/Admin/Admin.php:264
msgid "Regenerate Critical Path CSS"
msgstr "Znovu vygenerovat kritické CSS"

#: inc/common/admin-bar.php:194
msgid "Purge Sucuri cache"
msgstr "Vyprázdnit Sucuri mezipaměť"

#: inc/common/admin-bar.php:218
#: views/settings/fields/rocket-cdn.php:55
msgid "Purge RocketCDN cache"
msgstr "Vyčistit mezipaměť RocketCDN"

#: inc/common/admin-bar.php:231
#: views/settings/partials/documentation.php:14
msgid "Documentation"
msgstr "Dokumentace"

#: inc/deprecated/3.10.php:163
msgid "OPcache purge failed."
msgstr "Vyprázdnění OPcache se nezdařilo."

#: inc/deprecated/3.10.php:168
msgid "OPcache successfully purged"
msgstr "OPcache byla úspěšně vyprázdněna"

#: inc/deprecated/3.2.php:52
#: views/settings/page-sections/imagify.php:36
msgid "Activate Imagify"
msgstr "Aktivovat Imagify"

#: inc/deprecated/3.2.php:52
msgid "Install Imagify for Free"
msgstr "Zdarma instalovat Imagify"

#: inc/deprecated/3.2.php:67
msgid "Speed up your website and boost your SEO by reducing image file sizes without losing quality with Imagify."
msgstr "Zrychlete si web a zlepšete si SEO optimalizací obrázků bez ztráty kvality pomoci pluginu Imagify."

#: inc/deprecated/3.2.php:72
msgid "More details"
msgstr "Zobrazit podrobnosti"

#: inc/deprecated/3.2.php:228
#, php-format
msgid "Sitemap preload: %d pages have been cached."
msgstr "Předběžné načítání map webu: %d stránek bylo uloženo do mezipaměti."

#: inc/deprecated/3.2.php:261
#, php-format
msgid "Sitemap preload: %d uncached pages have now been preloaded. (refresh to see progress)"
msgstr "Předběžné načítání map webu: %d neuložených stránek v mezipaměti bylo předběžně načteno. (obnovte pro zobrazení postupu)"

#: inc/deprecated/3.4.php:18
msgid "Choose a domain from the list"
msgstr "Vyberte doménu ze seznamu"

#: inc/deprecated/3.4.php:31
msgid "No domain available in your Cloudflare account"
msgstr "V účtu Cloudflare není k dispozici žádná doména"

#: inc/deprecated/3.5.php:71
#: inc/deprecated/3.5.php:195
msgid "Curl is disabled on your server. Please ask your host to enable it. This is required for the Cloudflare Add-on to work correctly."
msgstr "cURL funkce je zakázána. Požádejte technickou podporu hostingu, aby cURL aktivovala. Tato funkce je nezbytná pro správnou funkčnost Cloudflare rozšíření."

#: inc/deprecated/3.5.php:79
#, php-format
msgid "Cloudflare email, API key and Zone ID are not set. Read the %1$sdocumentation%2$s for further guidance."
msgstr "Cloudflare email, API klíč a Zone ID nejsou nastaveny. Další pokyny naleznete v %1$sdokumentaci%2$s."

#: inc/deprecated/3.5.php:206
#, php-format
msgid "Cloudflare email and API key are not set. Read the %1$sdocumentation%2$s for further guidance."
msgstr "Cloudflare email a API klíč nejsou nastaveny. Další pokyny naleznete v %1$sdokumentaci%2$s."

#: inc/deprecated/3.5.php:271
msgid "Connection to Cloudflare failed"
msgstr "Připojení ke Cloudflare selhalo"

#: inc/deprecated/DeprecatedClassTrait.php:54
#, php-format
msgid "The called class %1$s is <strong>deprecated</strong> since version %2$s! Use %3$s instead."
msgstr "Volaná třída %1$s je <strong>zastaralá</strong> od verze %2$s! Místo toho použijte třídu %3$s."

#: inc/deprecated/DeprecatedClassTrait.php:65
#, php-format
msgid "The called class %1$s is <strong>deprecated</strong> since version %2$s!"
msgstr "Volaná třída %1$s je <strong>zastaralá</strong> od verze %2$s!"

#: inc/deprecated/deprecated.php:240
msgid "<strong>JS</strong> files with Deferred Loading JavaScript"
msgstr "<strong>JS</strong> soubory s odloženým načítáním jazyka JavaScript"

#: inc/deprecated/deprecated.php:293
msgid "Add URL"
msgstr "Přidat URL"

#: inc/deprecated/deprecated.php:1277
#: views/settings/fields/import-form.php:22
msgid "Before you can upload your import file, you will need to fix the following error:"
msgstr "Před nahráním souboru s importem budete ještě muset vyřešit následující chybu:"

#: inc/deprecated/deprecated.php:1288
#: views/settings/fields/import-form.php:35
#, php-format
msgid "Choose a file from your computer (maximum size: %s)"
msgstr "Zvolte soubor ze svého počítače (maximální velikost: %s)"

#: inc/deprecated/deprecated.php:1385
msgid "Your Cloudflare credentials are valid."
msgstr "Vaše přihlašovací údaje ke Cloudflare jsou platné."

#: inc/deprecated/deprecated.php:1389
msgid "Your Cloudflare credentials are invalid!"
msgstr "Vaše přihlašovací údaje ke Cloudflare jsou neplatné!"

#: inc/deprecated/deprecated.php:1453
msgid "Save and optimize"
msgstr "Uložit a optimalizovat"

#: inc/deprecated/deprecated.php:1453
msgid "Optimize"
msgstr "Optimalizovat"

#: inc/deprecated/deprecated.php:1464
#: inc/deprecated/deprecated.php:1666
msgctxt "screen-reader-text"
msgid "Note:"
msgstr "Poznámka:"

#: inc/deprecated/deprecated.php:1470
msgctxt "screen-reader-text"
msgid "Performance tip:"
msgstr "Výkonnostní tip:"

#: inc/deprecated/deprecated.php:1476
msgctxt "screen-reader-text"
msgid "Third-party feature detected:"
msgstr "Zjištěna funkce třetích stran:"

#: inc/deprecated/deprecated.php:1488
#: inc/deprecated/deprecated.php:1672
msgctxt "screen-reader-text"
msgid "Warning:"
msgstr "Varování:"

#: inc/deprecated/deprecated.php:1501
msgctxt "button text"
msgid "Download settings"
msgstr "Stáhnout nastavení"

#: inc/deprecated/deprecated.php:1531
msgid "Replace site's hostname with:"
msgstr "Nahraďte název hostitelského webu pomocí:"

#: inc/deprecated/deprecated.php:1550
#: inc/deprecated/deprecated.php:1587
#: inc/deprecated/deprecated.php:1615
#: views/settings/fields/cnames.php:41
#: views/settings/fields/cnames.php:75
#: views/settings/fields/cnames.php:105
msgid "reserved for"
msgstr "rezervováno pro"

#: inc/deprecated/deprecated.php:1552
#: inc/deprecated/deprecated.php:1589
#: inc/deprecated/deprecated.php:1617
#: views/settings/fields/cnames.php:44
#: views/settings/fields/cnames.php:78
#: views/settings/fields/cnames.php:108
msgid "All files"
msgstr "Všechny soubory"

#: inc/deprecated/deprecated.php:1564
#: inc/deprecated/deprecated.php:1594
#: inc/deprecated/deprecated.php:1622
#: views/settings/fields/cnames.php:56
#: views/settings/fields/cnames.php:90
#: views/settings/fields/cnames.php:120
msgid "Images"
msgstr "Obrázky"

#: inc/deprecated/deprecated.php:1635
#: views/settings/fields/cnames.php:132
msgid "Add CNAME"
msgstr "Přidat CNAME"

#: inc/deprecated/deprecated.php:1706
msgid "Watch the video"
msgstr "Přehrát video"

#: inc/deprecated/deprecated.php:1771
msgid "Basic"
msgstr "Základní"

#: inc/deprecated/deprecated.php:1772
msgid "Static Files"
msgstr "Statické soubory"

#: inc/deprecated/deprecated.php:1774
msgid "Advanced"
msgstr "Pokročilé"

#: inc/deprecated/deprecated.php:1944
#, php-format
msgid "%1$s %2$s requires at least PHP %3$s to function properly. To use this version, please ask your web host how to upgrade your server to PHP %3$s or higher. If you are not able to upgrade, you can rollback to the previous version by using the button below."
msgstr "Pro správnou funkčnost %1$s verze %2$s je vyžadováno PHP %3$s a novější. Chcete-li tuto verzi použít, požádejte svůj webhosting o pomoc se změnou PHP na verzi %3$s nebo novější. Pokud nemůžete přejít na novější verzi, můžete se vrátit zpět k předchozí verzi pomocí níže zobrazeného tlačítka."

#: inc/functions/admin.php:21
msgid "There seems to be an issue validating your license. Please see the error message below."
msgid_plural "There seems to be an issue validating your license. You can see the error messages below."
msgstr[0] "Během ověřování licence nastala chyba. Přečtěte si následující chybovou zprávu."
msgstr[1] "Během ověřování licence nastala chyba. Přečtěte si následující chybové zprávy."
msgstr[2] "Během ověřování licence nastala chyba. Přečtěte si následující chybové zprávy."
msgstr[3] "Během ověřování licence nastala chyba. Přečtěte si následující chybové zprávy."

#: inc/functions/admin.php:361
msgid "Server type:"
msgstr "Typ serveru:"

#: inc/functions/admin.php:370
msgid "PHP version number:"
msgstr "Verze PHP:"

#: inc/functions/admin.php:379
msgid "WordPress version number:"
msgstr "Verze WordPressu:"

#: inc/functions/admin.php:388
msgid "WordPress multisite:"
msgstr "Síť webů WordPressu:"

#: inc/functions/admin.php:397
msgid "Current theme:"
msgstr "Aktuální šablona:"

#: inc/functions/admin.php:406
msgid "Current site language:"
msgstr "Aktuální jazyk webu:"

#: inc/functions/admin.php:415
msgid "Active plugins:"
msgstr "Aktivované pluginy:"

#: inc/functions/admin.php:418
msgid "Plugin names of all active plugins"
msgstr "Názvy všech aktivovaných pluginů"

#: inc/functions/admin.php:424
msgid "Anonymized WP Rocket settings:"
msgstr "Anonymizované WP Rocket nastavení:"

#: inc/functions/admin.php:427
msgid "Which WP Rocket settings are active"
msgstr "Jaká nastavení pluginu WP Rocket jsou aktivní"

#: inc/functions/options.php:491
#: inc/functions/options.php:530
msgid "License validation failed. Our server could not resolve the request from your website."
msgstr "Ověřování licence se nezdařilo. Nepodařilo se navázat spojeni mezi web a našim serverem."

#: inc/functions/options.php:530
#, php-format
msgid "Try clicking %1$sSave Changes%2$s below. If the error persists, follow %3$sthis guide%4$s."
msgstr "Zkuste níže kliknout na %1$sUložit změny%2$s. Pokud problém přetrvává, postupujte podle %3$stohoto návodu%4$s."

#: inc/functions/options.php:507
msgid "License validation failed. You may be using a nulled version of the plugin. Please do the following:"
msgstr "Ověření licence se nezdařilo. Možná používáte nulovou verzi tohoto pluginu. Postupujte podle následujících kroků:"

#: inc/functions/options.php:507
#: inc/functions/options.php:549
#, php-format
msgid "Login to your WP Rocket %1$saccount%2$s"
msgstr "Přihlaste se ke svému WP Rocket %1$súčtu%2$s"

#: inc/functions/options.php:507
#: inc/functions/options.php:549
msgid "Download the zip file"
msgstr "Stáhněte soubor zip"

#: inc/functions/options.php:507
#: inc/functions/options.php:549
msgid "Reinstall"
msgstr "Přeinstalujte plugin"

#: inc/functions/options.php:507
#, php-format
msgid "If you do not have a WP Rocket account, please %1$spurchase a license%2$s."
msgstr "Pokud nemáte účet u WP Rocket, %1$skupte si licenci%2$s."

#: inc/functions/options.php:515
msgid "License validation failed. This user account does not exist in our database."
msgstr "Ověřování licence se nezdařilo. Tento uživatelský účet v naši databázi neexistuje."

#: inc/functions/options.php:515
msgid "To resolve, please contact support."
msgstr "Kontaktujte, prosím, technickou podporu."

#: inc/functions/options.php:523
#, php-format
msgid "Please see %1$sthis guide%2$s for more info."
msgstr "Pro více informací se podívejte na %1$stento návod%2$s."

#: inc/functions/options.php:543
msgid "Your license is not valid."
msgstr "Vaše licence je neplatná."

#: inc/functions/options.php:543
#, php-format
msgid "Make sure you have an active %1$sWP Rocket license%2$s."
msgstr "Ujistěte se, že máte aktivní %1$sWP Rocket licenci%2$s."

#: inc/functions/options.php:545
msgid "You have added as many sites as your current license allows."
msgstr "Přidali jste maximální počet webů ke svým zakoupeným licencím."

#: inc/functions/options.php:545
#, php-format
msgid "Upgrade your %1$saccount%2$s or %3$stransfer your license%2$s to this domain."
msgstr "Upgradujte %1$súčet%2$s, nebo%3$spřeveďte licenci%2$s na tuto doménu."

#: inc/functions/options.php:547
msgid "This website is not allowed."
msgstr "Tato webová stránka není povolena."

#: inc/functions/options.php:547
#, php-format
msgid "Please %1$scontact support%2$s."
msgstr "%1$sKontaktujte technickou podporu%2$s."

#: inc/functions/options.php:549
msgid "This license key is not recognized."
msgstr "Licenční klíč nebyl rozpoznán."

#: inc/functions/options.php:549
#, php-format
msgid "If the issue persists, please %1$scontact support%2$s."
msgstr "Pokud problém přetrvává, neváhejte %1$skontaktovat technickou podporu%2$s."

#: inc/functions/options.php:555
#, php-format
msgid "License validation failed: %s"
msgstr "Ověření licence se nezdařilo: %s"

#: inc/vendors/classes/class-imagify-partner.php:531
msgid "Plugin installed successfully."
msgstr "Plugin byl úspěšně nainstalován."

#: inc/vendors/classes/class-imagify-partner.php:532
msgid "Sorry, you are not allowed to install plugins on this site."
msgstr "Nemáte dostatečné oprávnění pro instalaci pluginů na tomto webu."

#: inc/vendors/classes/class-imagify-partner.php:533
msgid "Sorry, you are not allowed to do that."
msgstr "Pro tuto akci nemáte dostatečné oprávnění."

#: inc/vendors/classes/class-imagify-partner.php:534
msgid "Plugin install failed."
msgstr "Instalace pluginu selhala."

#: inc/vendors/classes/class-imagify-partner.php:535
msgid "Go back"
msgstr "Zpět"

#: views/deactivation-intent/form.php:87
msgid "Cancel"
msgstr "Zrušit"

#: views/cpcss/metabox/container.php:19
msgid "Critical Path CSS"
msgstr "Cesta ke kritickému CSS"

#: views/cpcss/metabox/generate.php:23
#, php-format
msgid "Generate specific Critical Path CSS for this post. %1$sMore info%2$s"
msgstr "Vygeneruje konkrétní cesty kritického CSS pro tento příspěvek. %1$sVíce informací%2$s"

#: views/cpcss/metabox/generate.php:33
#, php-format
msgid "This post uses specific Critical Path CSS. %1$sMore info%2$s"
msgstr "Tento příspěvek používá konkrétní cesty kritického CSS. %1$sVíce informací%2$s"

#: views/cpcss/metabox/generate.php:56
msgid "Revert back to the default CPCSS"
msgstr "Vrátit se zpět k výchozímu CPCSS"

#: views/settings/fields/cache-lifespan.php:26
msgid "Clear cache after"
msgstr "Vyčistit mezipaměť po"

#: views/settings/fields/cnames.php:58
#: views/settings/fields/cnames.php:92
#: views/settings/fields/cnames.php:122
msgid "CSS & JavaScript"
msgstr "CSS a JavaScript"

#: views/settings/fields/cnames.php:59
#: views/settings/fields/cnames.php:93
#: views/settings/fields/cnames.php:123
msgid "JavaScript"
msgstr "JavaScript"

#: views/settings/fields/cnames.php:60
#: views/settings/fields/cnames.php:94
#: views/settings/fields/cnames.php:124
msgid "CSS"
msgstr "CSS"

#: views/settings/fields/import-form.php:29
msgid "Import settings"
msgstr "Import nastavení"

#: views/settings/fields/one-click-addon.php:43
#: views/settings/fields/rocket-addon.php:43
msgid "Add-on status"
msgstr "Stav rozšíření"

#: views/settings/fields/rocket-addon.php:72
msgid "Modify options"
msgstr "Upravit nastavení"

#: views/settings/fields/rocket-cdn.php:29
#: views/settings/fields/rocket-cdn.php:41
msgid "CDN CNAME"
msgstr "CDN CNAME"

#: views/settings/fields/rocket-cdn.php:62
#, php-format
msgid "Purges RocketCDN cached resources for your website. %s"
msgstr "Vyčistí prostředky mezipaměti RocketCDN pro váš web. %s"

#: views/settings/fields/rocket-cdn.php:63
#: views/settings/page-sections/cloudflare.php:36
#: views/settings/page-sections/sucuri.php:37
msgid "Learn more"
msgstr "Více informací"

#: views/settings/fields/rocket-cdn.php:72
msgid "Clear all RocketCDN cache files"
msgstr "Vymazat všechny soubory mezipaměti RocketCDN"

#: views/settings/page-sections/cloudflare.php:27
msgid "Cloudflare Cache"
msgstr "Cloudflare mezipaměť"

#: views/settings/page-sections/cloudflare.php:35
#: views/settings/page-sections/sucuri.php:36
#, php-format
msgid "Purges cached resources for your website. %s"
msgstr "Vyprázdní zdroje mezipaměti pro váš web. %s"

#: views/settings/page-sections/cloudflare.php:36
msgid "https://support.cloudflare.com/hc/en-us/articles/200169246"
msgstr "https://support.cloudflare.com/hc/en-us/articles/200169246"

#: views/settings/page-sections/cloudflare.php:45
msgid "Clear all Cloudflare cache files"
msgstr "Vymzata veškeré soubory Cloudflare mezipaměti"

#: views/settings/page-sections/dashboard.php:38
msgid "Congratulations!"
msgstr "Gratulujeme!"

#: views/settings/page-sections/dashboard.php:40
msgid "WP Rocket is now activated and already working for you."
msgstr "WP Rocket je nyní aktivní a již zahájilo proces optimalizace."

#: views/settings/page-sections/dashboard.php:42
msgid "Your website should be loading faster now!"
msgstr "Nyní by se váš web měl načítat rychleji!"

#: views/settings/page-sections/dashboard.php:44
msgid "We also enable options that provide immediate benefits to your website."
msgstr "Dále jsou také aktivována nastavení s okamžitými výhodami pro váš web."

#: views/settings/page-sections/dashboard.php:45
msgid "Continue to the options to further optimize your site!"
msgstr "Pokračujte na nastavení pro další optimalizaci webu!"

#: views/settings/page-sections/dashboard.php:62
msgid "My Account"
msgstr "Účet"

#: views/settings/page-sections/dashboard.php:68
msgid "Refresh info"
msgstr "Aktualizovat"

#: views/settings/page-sections/dashboard.php:100
msgid "Expiration Date"
msgstr "Datum vypršení platnosti"

#: views/settings/page-sections/dashboard.php:110
msgid "View my account"
msgstr "Zobrazit můj účet"

#: views/settings/page-sections/dashboard.php:137
msgid "Quick Actions"
msgstr "Rychlé akce"

#: views/settings/page-sections/dashboard.php:144
msgid "Remove all cached files"
msgstr "Odstranit veškeré soubory mezipaměti"

#: views/settings/page-sections/dashboard.php:164
#: views/settings/page-sections/dashboard.php:170
msgid "Regenerate Critical CSS"
msgstr "Znovu vygenerovat kritické CSS"

#: views/settings/page-sections/dashboard.php:205
msgid "Frequently Asked Questions"
msgstr "Často kladené dotazy"

#: views/settings/page-sections/dashboard.php:219
msgid "Still cannot find a solution?"
msgstr "Pořád nemůžete najít řešení?"

#: views/settings/page-sections/dashboard.php:220
msgid "Submit a ticket and get help from our friendly and knowledgeable Rocketeers."
msgstr "Napište na technickou podporu, kde se vám budou věnovat naši odborníci."

#: views/settings/page-sections/dashboard.php:228
msgid "Ask support"
msgstr "Zeptat se podpory"

#: views/settings/page-sections/database.php:25
msgid "Backup your database before you run a cleanup!"
msgstr "Před spuštěním čištění databáze proveďte její zálohu!"

#: views/settings/page-sections/database.php:26
msgid "Once a database optimization has been performed, there is no way to undo it."
msgstr "Jakmile provedete optimalizaci databáze, neexistuje žádný způsob, jak provedené změny vrátit zpět."

#: views/settings/page-sections/database.php:28
msgid "Save Changes and Optimize"
msgstr "Uložit změny a optimalizovat"

#: views/settings/page-sections/imagify.php:21
#, php-format
msgid "%1$sWP ROCKET%2$s created %3$sIMAGIFY%4$s %1$sfor best-in-class image optimization.%2$s"
msgstr "%1$sWP ROCKET%2$s vytvořilo plugin %3$sIMAGIFY%4$s %1$spro nejlepší možnou optimalizaci obrázků.%2$s"

#: views/settings/page-sections/imagify.php:24
msgid "Compress image to make your website faster, all while maintaining image quality."
msgstr "Zrychlete svůj web zmenšením obrázků bez ztráty jejich kvality."

#: views/settings/page-sections/imagify.php:25
msgid "More on Imagify:"
msgstr "Více o Imagify:"

#: views/settings/page-sections/imagify.php:27
msgid "Imagify Plugin Page"
msgstr "Stránka v adresáři pluginů"

#: views/settings/page-sections/imagify.php:28
msgid "Imagify Website"
msgstr "Web Imagify"

#: views/settings/page-sections/imagify.php:29
msgid "Review of Image Compression Plugins"
msgstr "Porovnání kompresních pluginů"

#: views/settings/page-sections/imagify.php:38
msgid "Install Imagify"
msgstr "Instalovat Imagify"

#: views/settings/page-sections/license.php:22
msgid "WP Rocket was not able to automatically validate your license."
msgstr "WP Rocket se nepodařilo automaticky ověřit vaši licenci."

#: views/settings/page-sections/license.php:29
#, php-format
msgid "Follow this %1$s, or contact %2$s to get the engine started."
msgstr "Postupujte podle tohoto %1$s nebo se obraťte na %2$s, abyste nastartovali motor."

#: views/settings/page-sections/license.php:32
#, php-format
msgid "%1$s%2$s%3$stutorial%4$s"
msgstr "%1$s%2$s%3$snávod%4$s"

#: views/settings/page-sections/license.php:34
msgid "https://docs.wp-rocket.me/article/100-resolving-problems-with-license-validation/?utm_source=wp_plugin&utm_medium=wp_rocket"
msgstr "https://docs.wp-rocket.me/article/100-resolving-problems-with-license-validation/?utm_source=wp_plugin&utm_medium=wp_rocket"

#: views/settings/page-sections/license.php:40
#, php-format
msgid "%1$s%2$s%3$ssupport%4$s"
msgstr "%1$s%2$s%3$stechnická podpora%4$s"

#: views/settings/page-sections/sucuri.php:46
msgid "Clear all Sucuri cache files"
msgstr "Vzmazat veškeré soubory mezipaměti Sucuri"

#: views/settings/page-sections/tools.php:20
#, php-format
msgid "Files size: %1$s. Number of entries: %2$s."
msgstr "Velikost souboru: %1$s. Počet záznamů: %2$s."

#: views/settings/page-sections/tools.php:23
#, php-format
msgid "%1$sDownload the file%2$s."
msgstr "%1$sStáhnout soubor%2$s."

#: views/settings/page-sections/tools.php:26
#, php-format
msgid "%1$sDelete the file%2$s."
msgstr "%1$sOdstranit soubor%2$s."

#: views/settings/page-sections/tools.php:37
msgid "Export settings"
msgstr "Export nastavení"

#: views/settings/page-sections/tools.php:38
msgid "Download a backup file of your settings"
msgstr "Stáhněte si soubor se zálohou nastavení"

#: views/settings/page-sections/tools.php:46
msgid "Download settings"
msgstr "Stáhnout nastavení"

#: views/settings/page-sections/tools.php:60
msgid "Rollback"
msgstr "Návrat k předchozí verzi"

#: views/settings/page-sections/tools.php:64
#, php-format
msgid "Has version %s caused an issue on your website?"
msgstr "Způsobila vám verze %s nějaké potíže?"

#: views/settings/page-sections/tools.php:69
#, php-format
msgid "You can rollback to the previous major version here.%sThen send us a support request."
msgstr "Můžete se vrátit zpět na předchozí hlavní verzi zde.%sPoté kontaktujte naší podporu."

#: views/settings/page-sections/tools.php:80
#, php-format
msgid "Reinstall version %s"
msgstr "Přeinstalovat na verzi %s"

#: views/settings/page-sections/tools.php:106
msgid "Debug mode"
msgstr "Režim ladění"

#: views/settings/page-sections/tools.php:111
msgid "Create a debug log file."
msgstr "Vytvoření souboru záznamu ladění."

#: views/settings/page-sections/tutorials.php:13
#: views/settings/partials/getting-started.php:18
msgid "Getting Started"
msgstr "Začínáme"

#: views/settings/page-sections/tutorials.php:15
msgid "Getting Started with WP Rocket"
msgstr "Začínáme s WP Rocket"

#: views/settings/page-sections/tutorials.php:16
#: views/settings/partials/getting-started.php:12
msgid "Finding the Best Settings for Your Site"
msgstr "Hledání nejlepšího nastavení pro váš web"

#: views/settings/page-sections/tutorials.php:17
#: views/settings/partials/getting-started.php:13
msgid "How to Check if WP Rocket is Caching Your Site"
msgstr "Jak zjistit jestli WP Rocket cachuje váš web"

#: views/settings/page-sections/tutorials.php:18
#: views/settings/partials/getting-started.php:14
msgid "How to Measure the Speed of Your Site"
msgstr "Jak měřit rychlost vašeho webu"

#: views/settings/page-sections/tutorials.php:33
msgid "Troubleshooting Display Issues with File Optimization"
msgstr "Řešení potíží se zobrazováním obsahu při optimalizaci souborů"

#: views/settings/page-sections/tutorials.php:34
msgid "How to Find the Right JavaScript to Exclude"
msgstr "Jak najít ten pravý JavaScript pro vyloučení"

#: views/settings/page-sections/tutorials.php:35
msgid "How External Content Slows Your Site"
msgstr "Jak externí obsah zpomaluje váš web"

#: views/settings/page-sections/tutorials.php:19
msgid "How Preloading Works"
msgstr "Jak funguje předběžné načítání"

#: views/settings/page-sections/tutorials.php:41
msgid "Set Up the Cloudflare Add-on"
msgstr "Nastavení rozšíření pro Cloudflare"

#: views/settings/page.php:16
msgid "WP Rocket Settings"
msgstr "WP Rocket nastavení"

#: views/settings/page.php:30
#, php-format
msgid "version %s"
msgstr "verze %s"

#: views/settings/page.php:60
msgid "Show Sidebar"
msgstr "Zobrazit postranní panel"

#: views/settings/page.php:87
msgid "WP Rocket will never transmit any domain names or email addresses (except for license validation), IP addresses, or third-party API keys."
msgstr "WP Rocket nikdy nebude posílat názvy domén nebo emailové adresy (kromě ověření licence), IP adresy nebo API klíče třetích stran."

#: views/settings/page.php:89
msgid "Activate Rocket analytics"
msgstr "Aktivovat shromažďování dat"

#: views/settings/partials/documentation.php:15
msgid "It is a great starting point to fix some of the most common issues."
msgstr "Ideální první krok pro řešení nejčastějších problémů."

#: views/settings/partials/documentation.php:22
msgid "Read the documentation"
msgstr "Přečíst dokumentaci"

#: views/settings/partials/getting-started.php:11
msgid "What WP Rocket Does For You By Default"
msgstr "Co dělá WP Rocket ve výchozím nastavení"

#: views/settings/partials/sidebar.php:12
msgid "How to correctly measure your website’s loading time"
msgstr "Jak správně měřit rychlost načítání webu"

#: views/settings/partials/sidebar.php:14
msgid "Check our tutorial and learn how to measure the speed of your site."
msgstr "Podívejte se na návody a naučte se měřit rychlost vašeho webu."

#: views/settings/partials/sidebar.php:15
#: views/settings/partials/sidebar.php:24
msgid "Read our guide"
msgstr "Zobrazit návod"

#: views/settings/partials/sidebar.php:28
msgid "Read more"
msgstr "Více informací"

#: views/settings/partials/sidebar.php:33
msgid "You have not activated logged-in user cache."
msgstr "Mezipaměť pro přihlášené uživatele není aktivní."

#: views/settings/partials/sidebar.php:34
msgid "Use a private browser to check your website's speed and visual appearance."
msgstr "Pro zjištění rychlosti a vizuálních změn webu použijte anonymní okno prohlížeče."

#: inc/Engine/CDN/RocketCDN/views/cta-big.php:27
#, php-format
msgid "Valid until %s only!"
msgstr "Platí jen do %s!"

#: inc/Engine/CDN/RocketCDN/views/cta-big.php:36
msgid "Speed up your website thanks to:"
msgstr "Zrychlete web díky:"

#: inc/Engine/CDN/RocketCDN/views/cta-big.php:48
#, php-format
msgid "Easy configuration: the %1$sbest CDN settings%2$s are automatically applied"
msgstr "Snadná konfigurace: Automaticky se použije %1$snejvhodnější nastavení CDN%2$s."

#: inc/Engine/CDN/RocketCDN/views/cta-big.php:54
#, php-format
msgid "WP Rocket integration: the CDN option is %1$sautomatically configured%2$s in our plugin"
msgstr "Integrace WP Rocket: Možnost CDN je %1$sautomaticky nakonfigurovaná%2$s v našem pluginu"

#: inc/Engine/CDN/RocketCDN/views/cta-big.php:87
msgid "Get Started"
msgstr "Začínáme"

#: inc/Engine/CDN/RocketCDN/views/cta-big.php:58
msgid "Learn more about RocketCDN"
msgstr "Další informace o RocketCDN"

#: inc/Engine/CDN/RocketCDN/views/cta-big.php:92
msgid "Reduce this banner"
msgstr "Zmenšit tento banner"

#: inc/Engine/CDN/RocketCDN/views/cta-small.php:17
msgid "Speed up your website with RocketCDN, WP Rocket’s Content Delivery Network."
msgstr "Zrychlete svůj web s RocketCDN, vlastní Content Delivery Network od WP Rocket."

#: inc/Engine/CDN/RocketCDN/views/cta-small.php:20
#: inc/Engine/CDN/RocketCDN/views/promote-notice.php:13
msgid "Learn More"
msgstr "Více informací"

#: inc/Engine/CDN/RocketCDN/views/dashboard-status.php:23
msgid "RocketCDN is unavailable on local domains and staging sites."
msgstr "RocketCDN není k dispozici lokální domény ani pro testovací prostředí."

#: inc/Engine/CDN/RocketCDN/views/dashboard-status.php:32
msgid "Get RocketCDN"
msgstr "Získejte RocketCDN"

#: inc/Engine/CDN/RocketCDN/views/promote-notice.php:11
msgid "New!"
msgstr "Nové!"

#: inc/Engine/CDN/RocketCDN/views/promote-notice.php:12
msgid "Speed up your website with RocketCDN, WP Rocket’s Content Delivery Network!"
msgstr "Zrychlete svůj web s RocketCDN, vlastní Content Delivery Network od WP Rocket!"

#: views/settings/sections/addons-container.php:24
#: views/settings/sections/fields-container.php:28
msgid "Need Help?"
msgstr "Potřebujete pomoc?"

#: inc/Addon/Cloudflare/API/Client.php:129
msgid "Cloudflare did not provide any reply. Please try again later."
msgstr ""

#: inc/Addon/Cloudflare/Cloudflare.php:205
msgid "days"
msgstr ""

#: inc/Addon/Cloudflare/Cloudflare.php:208
msgid "seconds"
msgstr ""

#: inc/Addon/Cloudflare/Cloudflare.php:210
msgid "minutes"
msgstr ""

#: inc/Addon/Cloudflare/Cloudflare.php:212
msgid "hours"
msgstr ""

#: inc/Addon/Cloudflare/Subscriber.php:224
#: inc/Addon/Cloudflare/Subscriber.php:253
#, php-format
msgid "%1$sWP Rocket:%2$s %3$s"
msgstr ""

#: inc/Addon/Cloudflare/Subscriber.php:242
#, php-format
msgid "%1$sWP Rocket:%2$s Cloudflare cache successfully purged."
msgstr ""

#: inc/Addon/Cloudflare/Subscriber.php:328
msgctxt "Cloudflare caching level"
msgid "standard"
msgstr ""

#: inc/Addon/Cloudflare/Subscriber.php:403
#, php-format
msgid "Cloudflare browser cache set to %s"
msgstr ""

#: inc/Addon/Cloudflare/Subscriber.php:512
#, php-format
msgid "%1$sWP Rocket:%2$s Optimal settings activated for Cloudflare:"
msgstr ""

#: inc/Addon/Cloudflare/Subscriber.php:521
#, php-format
msgid "%1$sWP Rocket:%2$s Optimal settings deactivated for Cloudflare, reverted to previous settings:"
msgstr ""

#: inc/Addon/WebP/AdminSubscriber.php:93
#, php-format
msgid "You are using %1$s to serve WebP images so you do not need to enable this option. %2$sMore info%3$s %4$s If you prefer to have WP Rocket serve WebP for you instead, please disable WebP display in %1$s."
msgid_plural "You are using %1$s to serve WebP images so you do not need to enable this option. %2$sMore info%3$s %4$s If you prefer to have WP Rocket serve WebP for you instead, please disable WebP display in %1$s."
msgstr[0] ""
msgstr[1] ""
msgstr[2] ""
msgstr[3] ""

#: inc/Addon/WebP/AdminSubscriber.php:173
#, php-format
msgid "%5$sWe have not detected any compatible WebP plugin!%6$s%4$s If you don’t already have WebP images on your site consider using %3$sImagify%2$s or another supported plugin. %1$sMore info%2$s %4$s If you are not using WebP do not enable this option."
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:642
msgid "If you have problems after activating this option, copy and paste the default exclusions to quickly resolve issues:"
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:646
#, php-format
msgid "Also, please check our %1$sdocumentation%2$s for a list of compatibility exclusions."
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:651
msgid "Internal scripts are excluded by default to prevent issues. Remove them to take full advantage of this option."
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:654
#, php-format
msgid "If this causes trouble, restore the default exclusions, found %1$shere%2$s"
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:687
msgid "For compatibility and best results, this option is disabled when Remove unused CSS is enabled."
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:710
msgid "<strong>Internal:</strong> The domain part of the URL will be stripped automatically. Use (.*).css wildcards to exclude all CSS files located at a specific path."
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:712
#, php-format
msgid "<strong>3rd Party:</strong> Use either the full URL path or only the domain name, to exclude external CSS. %1$sMore info%2$s"
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:730
msgid "Optimize CSS delivery eliminates render-blocking CSS on your website. Only one method can be selected. Remove Unused CSS is recommended for optimal performance, but limited only to the users with active license."
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:730
msgid "Optimize CSS delivery eliminates render-blocking CSS on your website. Only one method can be selected. Remove Unused CSS is recommended for optimal performance."
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:740
#, php-format
msgid "Optimize CSS Delivery features are disabled on local environments. %1$sLearn more%2$s"
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:760
#: inc/admin/ui/meta-boxes.php:106
msgid "Remove Unused CSS"
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:763
#, php-format
msgid "Removes unused CSS per page and helps to reduce page size and HTTP requests. Recommended for best performance. Test thoroughly! %1$sMore info%2$s"
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:767
msgid "Activate Remove Unused CSS"
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:773
msgid "CSS safelist"
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:774
msgid "Specify CSS filenames, IDs or classes that should not be removed (one per line)."
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:789
#: inc/admin/ui/meta-boxes.php:109
msgid "Load CSS asynchronously"
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:792
#, php-format
msgctxt "WP Critical CSS compatibility"
msgid "Load CSS asynchronously is currently handled by the %1$s plugin. If you want to use WP Rocket’s load CSS asynchronously option, disable the %1$s plugin."
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:794
#, php-format
msgid "Generates critical path CSS and loads CSS asynchronously. %1$sMore info%2$s"
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:841
msgid "For compatibility and best results, this option is disabled when delay javascript execution is enabled."
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:915
#, php-format
msgid "Specify URLs or keywords of JavaScript files to be excluded from defer (one per line). %1$sMore info%2$s"
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:931
#: inc/admin/ui/meta-boxes.php:111
msgid "Delay JavaScript execution"
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:933
#, php-format
msgid "Improves performance by delaying the loading of JavaScript files until user interaction (e.g. scroll, click). %1$sMore info%2$s"
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:942
msgid "One-click exclusions"
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:943
msgid "When using the Delay JavaScript Execution, you might experience delay loading elements located in the viewport that need to appear immediately - e.g. slider, header, menu."
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:944
msgid "If you need instant visibility, click below on files that should NOT be delayed. This selection will help users interact with the elements straight away."
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:961
msgid "Specify URLs or keywords that can identify inline or JavaScript files to be excluded from delaying execution (one per line)."
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:994
msgid "LazyLoad, image dimensions"
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:1061
msgid "Image Dimensions"
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:1064
#, php-format
msgid "Add missing width and height attributes to images. Helps prevent layout shifts and improve the reading experience for your visitors. %1$sMore info%2$s"
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:1120
#, php-format
msgid "Replace YouTube iframe with preview image is not compatible with %2$s."
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:1135
msgid "Excluded images or iframes"
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:1137
#, php-format
msgid "Specify keywords (e.g. image filename, CSS class, domain) from the image or iframe code to be excluded (one per line). %1$sMore info%2$s"
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:1145
msgid "Add missing image dimensions"
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:1180
msgid "When you enable preloading WP Rocket will automatically detect your sitemaps and save all URLs to the database. The plugin will make sure that your cache is always preloaded."
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:1188
msgid "Preload Links"
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:1191
#, php-format
msgid "Link preloading improves the perceived load time by downloading a page when a user hovers over the link. %1$sMore info%2$s"
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:1234
msgid "Exclude URLs"
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:1239
#, php-format
msgid "Specify URLs to be excluded from the preload feature (one per line). %1$sMore info%2$s"
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:1261
msgid "Specify urls of the font files to be preloaded (one per line). Fonts must be hosted on your own domain, or the domain you have specified on the CDN tab."
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:1271
msgid "Enable link preloading"
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:1379
msgid "Specify full or partial IDs of cookies that, when set in the visitor's browser, should prevent a page from getting cached (one per line)"
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:1464
msgid "Automatic Cleanup"
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:1642
msgid "%1$s%2$l Add-on%3$s is currently enabled. Configuration of the CDN settings is not required for %2$l to work on your site."
msgid_plural "%1$s%2$l Add-ons%3$s are currently enabled. Configuration of the CDN settings is not required for %2$l to work on your site."
msgstr[0] ""
msgstr[1] ""
msgstr[2] ""
msgstr[3] ""

#: inc/Engine/Admin/Settings/Page.php:1827
#, php-format
msgid "%1$sPlanning on using Automatic Platform Optimization (APO)?%2$s Just activate the official Cloudflare plugin and configure it. WP Rocket will automatically enable compatibility."
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:1918
msgid "WebP Compatibility"
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:1924
msgid "Improve browser compatibility for WebP images."
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:2129
msgctxt "Sucuri"
msgid "Firewall API key (for plugin), must be in format {32 characters}/{32 characters}:"
msgstr ""

#: inc/Engine/Admin/Settings/Settings.php:668
msgid "Sorry! Adding /(.*) in Advanced Rules > Never Cache URL(s) was not saved because it disables caching and optimizations for every page on your site."
msgstr ""

#: inc/Engine/CDN/RocketCDN/APIClient.php:134
msgid "We could not fetch the current price because RocketCDN API returned an unexpected error code."
msgstr ""

#: inc/Engine/CDN/RocketCDN/APIClient.php:140
msgid "RocketCDN is not available at the moment. Please retry later."
msgstr ""

#: inc/Engine/CDN/RocketCDN/views/cta-big.php:42
#, php-format
msgid "High performance Content Delivery Network (CDN) with %1$sunlimited bandwidth%2$s"
msgstr ""

#: inc/Engine/CDN/RocketCDN/views/cta-big.php:65
#, php-format
msgid "*$%1$s/month for 12 months then $%2$s/month. You can cancel your subscription at any time."
msgstr ""

#: inc/Engine/CDN/RocketCDN/views/cta-big.php:86
msgid "Billed monthly"
msgstr ""

#: inc/Engine/Cache/WPCache.php:337
msgid "WP_CACHE value"
msgstr ""

#: inc/Engine/Cache/WPCache.php:358
msgid "The WP_CACHE constant needs to be set to true for WP Rocket cache to work properly"
msgstr ""

#: inc/Engine/Cache/WPCache.php:367
msgid "WP_CACHE is set to true"
msgstr ""

#: inc/Engine/Cache/WPCache.php:375
msgid "WP_CACHE is not set"
msgstr ""

#: inc/Engine/Cache/WPCache.php:383
msgid "WP_CACHE is set to false"
msgstr ""

#: inc/Engine/Common/Queue/RUCSSQueueRunner.php:253
msgid "Every minute"
msgstr ""

#: inc/Engine/CriticalPath/APIClient.php:170
#, php-format
msgid "Critical CSS for %1$s on mobile not generated. Error: The API returned an empty response."
msgstr ""

#: inc/Engine/CriticalPath/APIClient.php:185
#, php-format
msgid "Critical CSS for %1$s on mobile not generated."
msgstr ""

#: inc/Engine/CriticalPath/APIClient.php:195
#, php-format
msgid "Critical CSS for %1$s on mobile not generated. Error: The API returned an invalid response code."
msgstr ""

#: inc/Engine/CriticalPath/Admin/Post.php:216
msgid "This feature is not available for non-public post types."
msgstr ""

#: inc/Engine/CriticalPath/Admin/Post.php:223
msgid "Enable Load CSS asynchronously in WP Rocket settings"
msgstr ""

#: inc/Engine/CriticalPath/Admin/Post.php:224
msgid "Enable Load CSS asynchronously in the options above"
msgstr ""

#: inc/Engine/CriticalPath/CriticalCSSSubscriber.php:840
#, php-format
msgid "We highly recommend the %1$supdated Remove Unused CSS%2$s for a better CSS optimization. Load CSS Asynchronously is always available as a back-up."
msgstr ""

#: inc/Engine/CriticalPath/CriticalCSSSubscriber.php:845
msgid "Stay with the old option"
msgstr ""

#: inc/Engine/CriticalPath/DataManager.php:68
#, php-format
msgid "Critical CSS for %1$s on mobile not generated. Error: The destination folder could not be created."
msgstr ""

#: inc/Engine/CriticalPath/DataManager.php:71
#, php-format
msgid "Critical CSS for %1$s not generated. Error: The destination folder could not be created."
msgstr ""

#: inc/Engine/CriticalPath/DataManager.php:106
msgid "Critical CSS file for mobile does not exist"
msgstr ""

#: inc/Engine/CriticalPath/DataManager.php:120
msgid "Critical CSS file for mobile cannot be deleted"
msgstr ""

#: inc/Engine/CriticalPath/ProcessorService.php:187
#, php-format
msgid "Mobile Critical CSS for %1$s not generated."
msgstr ""

#: inc/Engine/CriticalPath/ProcessorService.php:262
#, php-format
msgid "Mobile Critical CSS for %s generated."
msgstr ""

#: inc/Engine/CriticalPath/ProcessorService.php:317
#, php-format
msgid "Mobile Critical CSS for %1$s timeout. Please retry a little later."
msgstr ""

#: inc/Engine/CriticalPath/RESTWP.php:141
msgid "Mobile CPCSS generation not enabled."
msgstr ""

#: inc/Engine/License/Renewal.php:76
#, php-format
msgid "Renew before it is too late, you will only pay %1$s%2$s%3$s!"
msgstr ""

#: inc/Engine/License/Renewal.php:85
#, php-format
msgid "Renew with a %1$s%2$s discount%3$s before it is too late, you will only pay %4$s%5$s%6$s!"
msgstr ""

#: inc/Engine/License/Renewal.php:139
#, php-format
msgid "Renew your license for 1 year now at %1$s%3$s%2$s."
msgstr ""

#: inc/Engine/License/Renewal.php:152
#, php-format
msgid "Renew your license for 1 year now and get %1$s%3$s OFF%2$s immediately: you will only pay %1$s%4$s%2$s!"
msgstr ""

#: inc/Engine/License/Renewal.php:218
#, php-format
msgid "Renew before it is too late, you will pay %1$s%3$s%2$s."
msgstr ""

#: inc/Engine/License/Renewal.php:227
#, php-format
msgid "Renew with a %1$s%2$s discount%3$s before it is too late, you will only pay %1$s%4$s%3$s!"
msgstr ""

#: inc/Engine/License/Renewal.php:546
#, php-format
msgid "You need a valid license to continue using this feature. %1$sRenew now%2$s before losing access."
msgstr ""

#: inc/Engine/License/Renewal.php:567
#, php-format
msgid "You need an active license to enable this option. %1$sRenew now%2$s."
msgstr ""

#: inc/Engine/License/Renewal.php:595
#, php-format
msgid "You need an active license to enable this option. %1$sMore info%2$s."
msgstr ""

#: inc/Engine/License/Upgrade.php:252
#, php-format
msgid "Take advantage of %1$s to speed up more websites:%2$s get a %3$s%4$s off%5$s for %3$supgrading your license to Plus or Infinite!%5$s"
msgid_plural "Take advantage of %1$s to speed up more websites:%2$s get a %3$s%4$s off%5$s for %3$supgrading your license to Infinite!%5$s"
msgstr[0] ""
msgstr[1] ""
msgstr[2] ""
msgstr[3] ""

#: inc/Engine/License/Upgrade.php:382
#: inc/Engine/License/Upgrade.php:405
msgid "Unlimited"
msgstr ""

#: inc/Engine/License/views/promo-banner.php:16
#, php-format
msgid "%s off"
msgstr ""

#: inc/Engine/License/views/promo-banner.php:21
#, php-format
msgid "%s promotion is live!"
msgstr ""

#: inc/Engine/License/views/promo-banner.php:27
msgid "Hurry Up! Deal ends in:"
msgstr ""

#: inc/Engine/License/views/promo-banner.php:32
#: inc/Engine/License/views/renewal-soon-banner.php:15
msgid "Seconds"
msgstr ""

#: inc/Engine/License/views/promo-banner.php:34
#: inc/Engine/License/views/upgrade-section.php:11
msgid "Upgrade now"
msgstr ""

#: inc/Engine/License/views/promo-banner.php:36
#: inc/Engine/License/views/renewal-expired-banner-ocd-disabled.php:32
#: inc/Engine/License/views/renewal-expired-banner-ocd.php:42
#: inc/Engine/License/views/renewal-expired-banner.php:30
#: inc/admin/ui/notices.php:739
#: views/settings/page-sections/dashboard.php:46
msgid "Dismiss this notice"
msgstr ""

#: inc/Engine/License/views/renewal-expired-banner-ocd-disabled.php:11
msgid "The Optimize CSS Delivery feature is disabled."
msgstr ""

#: inc/Engine/License/views/renewal-expired-banner-ocd-disabled.php:15
msgid "You can no longer use the Remove Unused CSS or Load CSS asynchronously options."
msgstr ""

#: inc/Engine/License/views/renewal-expired-banner-ocd-disabled.php:20
#, php-format
msgid "You need an %1$sactive license%2$s to keep optimizing your CSS delivery, which addresses a PageSpeed Insights recommendation and improves your page performance."
msgstr ""

#: inc/Engine/License/views/renewal-expired-banner-ocd-disabled.php:29
#: inc/Engine/License/views/renewal-expired-banner-ocd.php:39
#: inc/Engine/License/views/renewal-expired-banner.php:27
#: inc/Engine/License/views/renewal-soon-banner.php:31
msgid "Renew now"
msgstr ""

#: inc/Engine/License/views/renewal-expired-banner-ocd.php:11
msgid "You will soon lose access to some features"
msgstr ""

#: inc/Engine/License/views/renewal-expired-banner-ocd.php:18
#, php-format
msgid "You need an %1$sactive license to continue optimizing your CSS delivery%2$s."
msgstr ""

#: inc/Engine/License/views/renewal-expired-banner-ocd.php:24
msgid "The Remove Unused CSS and Load CSS asynchronously features are great options to address the PageSpeed Insights recommendations and improve your website performance."
msgstr ""

#: inc/Engine/License/views/renewal-expired-banner-ocd.php:29
#, php-format
msgid "They will be %1$sautomatically disabled on %3$s%2$s."
msgstr ""

#: inc/Engine/License/views/renewal-expired-banner.php:11
msgid "Your WP Rocket license is expired!"
msgstr ""

#: inc/Engine/License/views/renewal-expired-banner.php:18
#, php-format
msgid "Your website could be much faster if it could take advantage of our %1$snew features and enhancements%2$s. 🚀"
msgstr ""

#: inc/Engine/License/views/renewal-soon-banner.php:22
#, php-format
msgid "Your %1$sWP Rocket license is about to expire%2$s: you will soon lose access to product updates and support."
msgstr ""

#: inc/Engine/License/views/upgrade-popin.php:12
msgid "Speed Up More Websites"
msgstr ""

#: inc/Engine/License/views/upgrade-popin.php:19
#, php-format
msgid "You can use WP Rocket on more websites by upgrading your license. To upgrade, simply pay the %1$sprice difference%2$s between your current and new licenses, as shown below."
msgstr ""

#: inc/Engine/License/views/upgrade-popin.php:25
#, php-format
msgid "%1$sN.B.%2$s: Upgrading your license does not change your expiration date"
msgstr ""

#: inc/Engine/License/views/upgrade-popin.php:35
#, php-format
msgid "Save $%s"
msgstr ""

#: inc/Engine/License/views/upgrade-popin.php:48
#, php-format
msgid "%s websites"
msgstr ""

#: inc/Engine/License/views/upgrade-popin.php:54
#, php-format
msgid "Upgrade to %s"
msgstr ""

#: inc/Engine/License/views/upgrade-section.php:11
msgid "You can use WP Rocket on more websites by upgrading your license (you will only pay the price difference between your current and new licenses)."
msgstr ""

#: inc/Engine/Optimization/DelayJS/Admin/SiteList.php:195
msgid "Analytics & Ads"
msgstr ""

#: inc/Engine/Optimization/DelayJS/Admin/SiteList.php:200
msgid "Plugins"
msgstr ""

#: inc/Engine/Optimization/DelayJS/Admin/SiteList.php:205
msgid "Themes"
msgstr ""

#: inc/Engine/Optimization/DynamicLists/DynamicLists.php:101
msgid "You need an active license to get the latest version of the lists from our server."
msgstr ""

#: inc/Engine/Optimization/DynamicLists/DynamicLists.php:115
msgid "Could not get updated lists from server."
msgstr ""

#: inc/Engine/Optimization/DynamicLists/DynamicLists.php:124
msgid "Lists are up to date."
msgstr ""

#: inc/Engine/Optimization/DynamicLists/DynamicLists.php:133
msgid "Could not update lists."
msgstr ""

#: inc/Engine/Optimization/DynamicLists/DynamicLists.php:142
msgid "Lists are successfully updated."
msgstr ""

#: inc/Engine/Optimization/DynamicLists/ServiceProvider.php:52
msgid "Default Lists"
msgstr ""

#: inc/Engine/Optimization/DynamicLists/ServiceProvider.php:58
msgid "Delay JavaScript Execution Exclusion Lists"
msgstr ""

#: inc/Engine/Optimization/RUCSS/Admin/Settings.php:131
#: views/settings/page-sections/dashboard.php:188
msgid "Clear Used CSS"
msgstr ""

#: inc/Engine/Optimization/RUCSS/Admin/Settings.php:219
#, php-format
msgid "%1$s: Please wait %2$s seconds. The Remove Unused CSS service is processing your pages."
msgstr ""

#: inc/Engine/Optimization/RUCSS/Admin/Settings.php:264
#, php-format
msgid "%1$s: The Used CSS of your homepage has been processed. WP Rocket will continue to generate Used CSS for up to %2$s URLs per %3$s second(s)."
msgstr ""

#: inc/Engine/Optimization/RUCSS/Admin/Settings.php:273
#, php-format
msgid "We suggest enabling %1$sPreload%2$s for the fastest results."
msgstr ""

#: inc/Engine/Optimization/RUCSS/Admin/Settings.php:283
#, php-format
msgid "To learn more about the process check our %1$sdocumentation%2$s."
msgstr ""

#: inc/Engine/Optimization/RUCSS/Admin/Settings.php:481
msgid "We couldn't generate the used CSS because you're using a nulled version of WP Rocket. You need an active license to use the Remove Unused CSS feature and further improve your website's performance."
msgstr ""

#: inc/Engine/Optimization/RUCSS/Admin/Settings.php:484
#, php-format
msgid "Click here to get a WP Rocket single license at %1$s off!"
msgstr ""

#: inc/Engine/Optimization/RUCSS/Admin/Settings.php:521
#, php-format
msgid "Could not create the %2$s table in the database which is necessary for the Remove Unused CSS feature to work. Please reach out to <a href=\"%3$s\">our support</a>."
msgstr ""

#: inc/Engine/Optimization/RUCSS/Admin/Subscriber.php:322
#, php-format
msgid "%1$s: Used CSS option is not enabled!"
msgstr ""

#: inc/Engine/Optimization/RUCSS/Admin/Subscriber.php:343
#, php-format
msgid "%1$s: Used CSS cache cleared!"
msgstr ""

#: inc/Engine/Optimization/RUCSS/Controller/UsedCSS.php:776
msgid "Clear Used CSS of this URL"
msgstr ""

#: inc/Engine/Optimization/RUCSS/Cron/Subscriber.php:160
msgid "WP Rocket Remove Unused CSS pending jobs"
msgstr ""

#: inc/Engine/Optimization/RUCSS/Cron/Subscriber.php:174
msgid "WP Rocket clear Remove Unused CSS failed jobs"
msgstr ""

#: inc/Engine/Preload/Admin/Settings.php:57
#, php-format
msgid "%1$s: The preload service is now active. After the initial preload it will continue to cache all your pages whenever they are purged. No further action is needed."
msgstr ""

#: inc/Engine/Preload/Cron/Subscriber.php:138
msgid "WP Rocket Preload pending jobs"
msgstr ""

#: inc/Engine/Preload/Cron/Subscriber.php:166
msgid "WP Rocket Preload revert stuck failed jobs"
msgstr ""

#: inc/ThirdParty/Hostings/Cloudways.php:82
#, php-format
msgid "Varnish auto-purge will be automatically enabled once Varnish is enabled on your %s server."
msgstr ""

#: inc/ThirdParty/Hostings/Kinsta.php:158
#, php-format
msgid "Your installation seems to be missing core Kinsta files managing Cache clearing, which will prevent your Kinsta installation and WP Rocket from working correctly. Please get in touch with Kinsta support through your %1$sMyKinsta%2$s account to resolve this issue."
msgstr ""

#: inc/ThirdParty/Plugins/CDN/Cloudflare.php:114
#, php-format
msgid "%1$s: Cloudflare's HTTP/2 Server Push is incompatible with the features of Remove Unused CSS and Combine CSS files. We strongly recommend disabling it."
msgstr ""

#: inc/ThirdParty/Plugins/CDN/Cloudflare.php:157
msgid "Your site is using the official Cloudflare plugin. We have enabled Cloudflare auto-purge for compatibility. If you have APO activated, it is also compatible."
msgstr ""

#: inc/ThirdParty/Plugins/CDN/Cloudflare.php:158
msgid "Cloudflare cache will be purged each time WP Rocket clears its cache to ensure content is always up-to-date."
msgstr ""

#: inc/ThirdParty/Plugins/CDN/Cloudflare.php:202
#, php-format
msgid "%1$sWP Rocket:%2$s You are using \"Dynamic Cookies Cache\". Cloudflare APO is not yet compatible with that feature."
msgstr ""

#: inc/ThirdParty/Plugins/CDN/Cloudflare.php:208
#, php-format
msgid "You should either disable Cloudflare APO or check with the theme/plugin requiring the use of “Dynamic Cookies Cache” developers for an alternative way to be page-cache friendly. %1$sMore info%2$s"
msgstr ""

#: inc/ThirdParty/Plugins/CDN/Cloudflare.php:259
#, php-format
msgid "%1$sWP Rocket:%2$s You are using \"Separate cache files for mobile devices\". You need to activate \"Cache by Device Type\" %3$ssetting%5$s on Cloudflare APO to serve the right version of the cache. %4$sMore info%5$s"
msgstr ""

#: inc/ThirdParty/Plugins/CDN/Cloudflare.php:280
#, php-format
msgid "%1$sWP Rocket:%2$s You have \"Cache by Device Type\" enabled on Cloudflare APO. If you judge it necessary for the website to have a different cache on mobile and desktop, we suggest you enable our “Separate Cache Files for Mobiles Devices” to ensure the generated cache is accurate."
msgstr ""

#: inc/ThirdParty/Plugins/ModPagespeed.php:102
#, php-format
msgid "<strong>%1$s</strong>: Mod PageSpeed is not compatible with this plugin and may cause unexpected results. %2$sMore Info%3$s"
msgstr ""

#: inc/ThirdParty/Plugins/Optimization/Autoptimize.php:76
#, php-format
msgid "%1$sWP Rocket: %2$sWe have detected that Autoptimize's JavaScript Aggregation feature is enabled. WP Rocket's Delay JavaScript Execution will not be applied to the file it creates. We suggest disabling %1$sJavaScript Aggregation%2$s to take full advantage of Delay JavaScript Execution."
msgstr ""

#: inc/ThirdParty/Plugins/Optimization/Autoptimize.php:131
#, php-format
msgid "%1$sWP Rocket: %2$sWe have detected that Autoptimize's Aggregate Inline CSS feature is enabled. WP Rocket's Load CSS Asynchronously will not work correctly. We suggest disabling %1$sAggregate Inline CSS%2$s to take full advantage of Load CSS Asynchronously Execution."
msgstr ""

#: inc/ThirdParty/Plugins/Optimization/Ezoic.php:45
#, php-format
msgid "This plugin blocks WP Rocket's caching and optimizations. Deactivate it and use %1$sEzoic's nameserver integration%2$s instead."
msgstr ""

#: inc/ThirdParty/Plugins/Optimization/Perfmatters.php:38
msgid "Remove Unused CSS is currently activated in Perfmatters. If you want to use WP Rocket's Remove Unused CSS feature, disable this option in Perfmatters."
msgstr ""

#: inc/ThirdParty/Plugins/Optimization/RapidLoad.php:39
msgid "Automated unused CSS removal is currently activated in RapidLoad Power-Up for Autoptimize. If you want to use WP Rocket's Remove Unused CSS feature, disable the  RapidLoad Power-Up for Autoptimize plugin."
msgstr ""

#: inc/ThirdParty/Plugins/Optimization/WPMeteor.php:42
#, php-format
msgid "Delay JS is currently activated in %1$s. If you want to use WP Rocket’s delay JS, disable %1$s"
msgstr ""

#: inc/admin/options.php:127
msgid "Defer JavaScript Files"
msgstr ""

#: inc/admin/options.php:128
msgid "Excluded Delay JavaScript Files"
msgstr ""

#: inc/admin/options.php:150
#, php-format
msgid "%1$s: <em>%2$s</em>"
msgstr ""

#: inc/admin/options.php:176
msgid "More info"
msgstr ""

#: inc/admin/ui/notices.php:757
msgid "Turn on Remove Unused CSS"
msgstr ""

#: inc/admin/ui/notices.php:763
msgid "Enable “Separate Cache Files for Mobile Devices” now"
msgstr ""

#: inc/classes/dependencies/wp-media/background-processing/wp-background-process.php:447
#, php-format
msgid "Every %d Minutes"
msgstr ""

#: inc/common/admin-bar.php:84
#: inc/common/admin-bar.php:136
#: views/settings/page-sections/dashboard.php:150
msgid "Clear and preload cache"
msgstr ""

#: inc/functions/options.php:429
msgid "The provided license data are not valid."
msgstr ""

#: inc/functions/options.php:432
#, php-format
msgid "To resolve, please %1$scontact support%2$s."
msgstr ""

#: inc/functions/options.php:491
#, php-format
msgid "Try clicking %1$sValidate License%2$s below. If the error persists, follow %3$sthis guide%4$s."
msgstr ""

#: inc/functions/options.php:523
msgid "License validation failed. This user account is blocked."
msgstr ""

#: views/cpcss/activate-cpcss-mobile.php:13
msgid "Load CSS asynchronously for mobile"
msgstr ""

#: views/cpcss/activate-cpcss-mobile.php:14
msgid "Your website currently uses the same Critical Path CSS for both desktop and mobile."
msgstr ""

#: views/cpcss/activate-cpcss-mobile.php:15
msgid "Click the button to enable mobile-specific CPCSS for your site."
msgstr ""

#: views/cpcss/activate-cpcss-mobile.php:20
#: views/settings/enable-google-fonts.php:19
#, php-format
msgid "This is a one-time action and this button will be removed afterwards. %1$sMore info%2$s"
msgstr ""

#: views/cpcss/activate-cpcss-mobile.php:30
#, php-format
msgid "Your site is now using mobile-specific critical path CSS. %1$sMore info%2$s"
msgstr ""

#: views/cpcss/activate-cpcss-mobile.php:39
msgid "Generate Mobile Specific CPCSS"
msgstr ""

#: views/deactivation-intent/form.php:21
msgid "Facing an issue?"
msgstr ""

#: views/deactivation-intent/form.php:24
msgid "It is not always necessary to deactivate WP Rocket when facing any issues. Most of them can be fixed by deactivating only some options."
msgstr ""

#: views/deactivation-intent/form.php:29
#, php-format
msgid "Our advice? Instead of deactivating WP Rocket, use our %1$sSafe Mode%2$s to quickly disable LazyLoad, File Optimization, and CDN options. Then check to see if your issue is resolved."
msgstr ""

#: views/deactivation-intent/form.php:35
msgid "Do you want to use our Safe Mode to troubleshoot WP Rocket?"
msgstr ""

#: views/deactivation-intent/form.php:55
#, php-format
msgid "Yes, apply \"%1$sSafe Mode%2$s\""
msgstr ""

#: views/deactivation-intent/form.php:68
#, php-format
msgid "and export WP Rocket settings %1$s(Recommended as current settings will be deleted)%2$s"
msgstr ""

#: views/deactivation-intent/form.php:76
msgid "No, deactivate and snooze this message for"
msgstr ""

#: views/deactivation-intent/form.php:78
msgid "1 day"
msgstr ""

#: views/deactivation-intent/form.php:79
msgid "7 days"
msgstr ""

#: views/deactivation-intent/form.php:80
msgid "30 days"
msgstr ""

#: views/deactivation-intent/form.php:81
msgid "Forever"
msgstr ""

#: views/deactivation-intent/form.php:88
msgid "Confirm"
msgstr ""

#: views/settings/dynamic-lists-update.php:14
msgid "Update Inclusion and Exclusion Lists"
msgstr ""

#: views/settings/dynamic-lists-update.php:19
#, php-format
msgid "Compatibility lists are downloaded automatically every week. Click the button if you want to update them manually. %1$sMore info%2$s"
msgstr ""

#: views/settings/dynamic-lists-update.php:29
msgid "Update lists"
msgstr ""

#: views/settings/enable-google-fonts.php:13
msgid "Enable Google Font Optimization"
msgstr ""

#: views/settings/enable-google-fonts.php:14
msgid "Improves font performance and combines multiple font requests to reduce the number of HTTP requests."
msgstr ""

#: views/settings/enable-google-fonts.php:29
#, php-format
msgid "Google Fonts Optimization is now enabled for your site. %1$sMore info%2$s"
msgstr ""

#: views/settings/page-sections/dashboard.php:44
#, php-format
msgid "To guarantee fast websites, WP Rocket automatically applies 80% of web performance best practices."
msgstr ""

#: views/settings/page-sections/dashboard.php:86
msgid "with"
msgstr ""

#: views/settings/page-sections/dashboard.php:182
msgid "Remove Used CSS Cache"
msgstr ""

#: views/settings/page-sections/tutorials.php:23
msgid "Passing the Core Web vitals"
msgstr ""

#: views/settings/page-sections/tutorials.php:25
msgid "How to improve LCP with WP Rocket"
msgstr ""

#: views/settings/page-sections/tutorials.php:26
msgid "How to improve FID with WP Rocket"
msgstr ""

#: views/settings/page-sections/tutorials.php:27
msgid "How to improve CLS with WP Rocket"
msgstr ""

#: views/settings/page-sections/tutorials.php:31
msgid "Troubleshooting"
msgstr ""

#: views/settings/page.php:82
#, php-format
msgid "Below is a detailed view of all data WP Rocket will collect %1$sif granted permission.%2$s"
msgstr ""

#: views/settings/partials/sidebar.php:18
msgid "Learn about optimal WP Rocket settings for mobile."
msgstr ""

#: views/settings/partials/sidebar.php:27
msgid "Test and Improve Google Core Web Vitals for WordPress."
msgstr ""
