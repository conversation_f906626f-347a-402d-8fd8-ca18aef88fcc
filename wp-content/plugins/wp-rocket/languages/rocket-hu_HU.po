msgid ""
msgstr ""
"Project-Id-Version: WP Rocket 3.11\n"
"Report-Msgid-Bugs-To: http://wp-rocket.me/\n"
"Last-Translator: <PERSON>, 2022\n"
"Language-Team: Hungarian (Hungary) (https://www.transifex.com/wp-media/teams/18133/hu_HU/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"POT-Creation-Date: 2022-07-28 14:01-0400\n"
"PO-Revision-Date: 2019-08-26 15:14+0000\n"
"Language: hu_HU\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Generator: Poedit 1.8.11\n"
"X-Poedit-Basepath: ..\n"
"X-Poedit-KeywordsList: __;_e;_x:1,2c;_ex:1,2c;_n:1,2;_nx:1,2,4c;_n_noop:1,2;_nx_noop:1,2,3c;esc_attr__;esc_html__;esc_attr_e;esc_html_e;esc_attr_x:1,2c;esc_html_x:1,2c\n"
"X-Poedit-SearchPath-0: .\n"
"X-Poedit-SearchPathExcluded-0: assets\n"
"X-Poedit-SearchPathExcluded-1: src\n"
"X-Poedit-SearchPathExcluded-2: vendor\n"
"X-Poedit-SearchPathExcluded-3: node_modules\n"
"X-Poedit-SearchPathExcluded-4: tests\n"
"X-Poedit-SearchPathExcluded-5: inc/Dependencies\n"
"X-Poedit-SourceCharset: UTF-8\n"
"X-Textdomain-Support: yes\n"

#: inc/3rd-party/hosting/flywheel.php:18
#: inc/3rd-party/hosting/pressidium.php:18
#: inc/3rd-party/hosting/wp-serveur.php:27
#: inc/ThirdParty/Hostings/Cloudways.php:90
#: inc/ThirdParty/Hostings/Dreampress.php:44
#: inc/ThirdParty/Hostings/Godaddy.php:63
#: inc/ThirdParty/Hostings/O2Switch.php:49
#: inc/ThirdParty/Hostings/OneCom.php:137
#: inc/ThirdParty/Hostings/ProIsp.php:51
#: inc/ThirdParty/Hostings/Savvii.php:50
#: inc/ThirdParty/Hostings/WPEngine.php:47
#: inc/ThirdParty/Hostings/WPXCloud.php:51
#: inc/deprecated/3.6.php:697
#: inc/deprecated/3.6.php:997
#: inc/deprecated/3.9.php:22
#, php-format
msgid "Your site is hosted on %s, we have enabled Varnish auto-purge for compatibility."
msgstr "Az oldalad a %stárhelyen van, bekapcsoltuk a Varnish gyorsítótár automata ürítését kompatibilitási okokból."

#: inc/deprecated/3.12.php:332
msgid "Jetpack XML Sitemaps"
msgstr "Jetpack XML Oldaltérképek"

#: inc/deprecated/3.12.php:334
msgid "Preload the sitemap from the Jetpack plugin"
msgstr "Oldaltérkép előtöltése a Jetpack pluginból"

#: inc/deprecated/3.11.php:83
#: inc/deprecated/3.12.php:58
#: inc/deprecated/3.12.php:87
#: inc/deprecated/3.12.php:186
#: inc/deprecated/3.12.php:253
#: inc/deprecated/3.12.php:342
#, php-format
msgid "We automatically detected the sitemap generated by the %s plugin. You can check the option to preload it."
msgstr "Megtaláltuk az oldaltérképet, amit %s bővítmény készített. Ha szeretnéd ezt előtölteni akkor pipáld be az erre való opciót."

#: inc/deprecated/3.12.php:56
msgid "All in One SEO XML sitemap"
msgstr "All in One SEO XML oldaltérkép"

#: inc/deprecated/3.12.php:85
msgid "Rank Math XML sitemap"
msgstr "Rank Math XML oldaltérkép"

#: inc/deprecated/3.12.php:184
msgid "SEOPress XML sitemap"
msgstr "SEOPress XML oldaltérkép"

#: inc/deprecated/3.12.php:251
msgid "The SEO Framework XML sitemap"
msgstr "The SEO Framework XML oldaltérkép"

#: inc/Addon/Cloudflare/API/Client.php:129
msgid "Cloudflare did not provide any reply. Please try again later."
msgstr "A Cloudflare semmilyen választ nem küldött. Kérjük próbáld újra később."

#: inc/Addon/Cloudflare/API/Client.php:186
#: inc/deprecated/3.5.php:112
#: inc/deprecated/3.5.php:169
msgid "Incorrect Cloudflare email address or API key."
msgstr "Téves Cloudflare email cím vagy API kulcs."

#: inc/Addon/Cloudflare/API/Client.php:190
#: inc/Addon/Cloudflare/API/Client.php:204
#: inc/Addon/Cloudflare/Cloudflare.php:74
#: inc/Addon/Cloudflare/Cloudflare.php:109
#: inc/classes/subscriber/Tools/class-detect-missing-tags-subscriber.php:157
#: inc/deprecated/3.5.php:92
#: inc/deprecated/3.5.php:116
#: inc/deprecated/3.5.php:129
#: inc/deprecated/3.5.php:157
#: inc/deprecated/3.5.php:173
#, php-format
msgid "Read the %1$sdocumentation%2$s for further guidance."
msgstr "Olvasd el a  %1$sdokumentációt %2$s további segítségért. "

#: inc/Addon/Cloudflare/API/Client.php:192
#: inc/Addon/Cloudflare/API/Client.php:206
#: inc/Addon/Cloudflare/Auth/APIKey.php:63
#: inc/Addon/Cloudflare/Cloudflare.php:76
#: inc/Addon/Cloudflare/Cloudflare.php:111
#: inc/deprecated/3.5.php:81
#: inc/deprecated/3.5.php:94
#: inc/deprecated/3.5.php:118
#: inc/deprecated/3.5.php:131
#: inc/deprecated/3.5.php:159
#: inc/deprecated/3.5.php:175
#: inc/deprecated/3.5.php:208
msgid "https://docs.wp-rocket.me/article/18-using-wp-rocket-with-cloudflare/?utm_source=wp_plugin&utm_medium=wp_rocket#add-on"
msgstr "https://docs.wp-rocket.me/article/18-using-wp-rocket-with-cloudflare/?utm_source=wp_plugin&utm_medium=wp_rocket#add-on"

#: inc/Addon/Cloudflare/API/Client.php:200
#: inc/deprecated/3.5.php:125
msgid "Incorrect Cloudflare Zone ID."
msgstr "Hibás Cloudflare Zóna azonosító azaz Zone ID"

#: inc/Addon/Cloudflare/Auth/APIKey.php:61
#, php-format
msgid "Cloudflare email and/or API key are not set. Read the %1$sdocumentation%2$s for further guidance."
msgstr "Nincs beállítva a Cloudflare email vagy API kulcs. Olvasd el a %1$s dokumentációt %2$s további részletekért."

#: inc/Addon/Cloudflare/Cloudflare.php:70
#: inc/deprecated/3.5.php:88
msgid "Missing Cloudflare Zone ID."
msgstr "Hiányzó Cloudflare zóna azonosító."

#: inc/Addon/Cloudflare/Cloudflare.php:105
#: inc/deprecated/3.5.php:153
msgid "It looks like your domain is not set up on Cloudflare."
msgstr "Úgy tűnik hogy a domain neved nincs beállítva a Cloudflare oldalán."

#: inc/deprecated/3.5.php:587
#, php-format
msgid "<strong>WP Rocket:</strong> %s"
msgstr "<strong>WP Rocket:</strong> %s"

#: inc/deprecated/3.5.php:592
msgid "<strong>WP Rocket:</strong> Cloudflare cache successfully purged."
msgstr "<strong>WP Rocket:</strong> Cloudflare gyorsítótár sikeresen ürítve."

#: inc/Addon/Cloudflare/Subscriber.php:632
#: inc/admin/options.php:184
#: inc/classes/subscriber/Tools/class-detect-missing-tags-subscriber.php:148
msgid "WP Rocket: "
msgstr "WP Rocket: "

#: inc/Addon/Cloudflare/Subscriber.php:297
#, php-format
msgid "Cloudflare development mode error: %s"
msgstr "Cloudflare fejlesztői üzemmód hiba: %s"

#: inc/Addon/Cloudflare/Subscriber.php:304
#, php-format
msgid "Cloudflare development mode %s"
msgstr "Cloudflare fejlesztői üzemmód %s"

#: inc/Addon/Cloudflare/Subscriber.php:321
#, php-format
msgid "Cloudflare cache level error: %s"
msgstr "Cloudflare gyorsítótár szint hiba:%s"

#: inc/Addon/Cloudflare/Subscriber.php:334
#, php-format
msgid "Cloudflare cache level set to %s"
msgstr "Cloudflare gyorsítótár szint beállítva %s-re"

#: inc/Addon/Cloudflare/Subscriber.php:350
#, php-format
msgid "Cloudflare minification error: %s"
msgstr "Cloudflare minification hiba: %s"

#: inc/Addon/Cloudflare/Subscriber.php:357
#, php-format
msgid "Cloudflare minification %s"
msgstr "Cloudflare minification %s"

#: inc/Addon/Cloudflare/Subscriber.php:373
#, php-format
msgid "Cloudflare rocket loader error: %s"
msgstr "Cloudflare rocket loader hiba: %s"

#: inc/Addon/Cloudflare/Subscriber.php:380
#, php-format
msgid "Cloudflare rocket loader %s"
msgstr "Cloudflare rocket loader %s"

#: inc/Addon/Cloudflare/Subscriber.php:396
#, php-format
msgid "Cloudflare browser cache error: %s"
msgstr "Cloudflare böngésző gyorsítótár hiba: %s"

#: inc/Addon/Sucuri/Subscriber.php:95
#, php-format
msgid "Sucuri cache purge error: %s"
msgstr "Sucuri gyorsítótár ürítési hiba: %s"

#: inc/Addon/Sucuri/Subscriber.php:100
msgid "The Sucuri cache is being cleared. Note that it may take up to two minutes for it to be fully flushed."
msgstr "A Sucuri gyorsítótár ürítése folyamatban. Tipp: ez akár két percet is igénybe vehet."

#: inc/Addon/Sucuri/Subscriber.php:217
msgid "Sucuri firewall API key was not found."
msgstr "A Sucuri tűzfal API kulcsa nem található."

#: inc/Addon/Sucuri/Subscriber.php:230
msgid "Sucuri firewall API key is invalid."
msgstr "A Sucuri tűzfal API kulcsa érvénytelen."

#: inc/Addon/Sucuri/Subscriber.php:285
#, php-format
msgid "Error when contacting Sucuri firewall API. Error message was: %s"
msgstr "Nem sikerült elérni a Sucuri tűzfal API-ját. A hibaüzenet: %s"

#: inc/Addon/Sucuri/Subscriber.php:300
msgid "Could not get a response from the Sucuri firewall API."
msgstr "Nem kaptunk választ a Sucuri tűzfal API-jától."

#: inc/Addon/Sucuri/Subscriber.php:315
msgid "Got an invalid response from the Sucuri firewall API."
msgstr "Érvénytelen válaaszt kaptunk a Sucuri tűzfal API-jától."

#: inc/Addon/Sucuri/Subscriber.php:329
msgid "The Sucuri firewall API returned an unknown error."
msgstr "A Sucuri tűzfal API-ja ismeretlen hibát jelzett."

#: inc/Addon/Sucuri/Subscriber.php:333
#, php-format
msgid "The Sucuri firewall API returned the following error: %s"
msgid_plural "The Sucuri firewall API returned the following errors: %s"
msgstr[0] "A Sucuri tűzfal API-ja az alábbi hibát jelezte: %s"
msgstr[1] "A Sucuri tűzfal API-ja az alábbi hibákat jelezte.%s"

#: inc/Engine/Admin/Database/Optimization.php:30
#: inc/Engine/Admin/Settings/Page.php:1475
msgid "Revisions"
msgstr "Változatok"

#: inc/Engine/Admin/Database/Optimization.php:31
#: inc/Engine/Admin/Settings/Page.php:1485
msgid "Auto Drafts"
msgstr "Automatikus piszkozatok"

#: inc/Engine/Admin/Database/Optimization.php:32
#: inc/Engine/Admin/Settings/Page.php:1495
msgid "Trashed Posts"
msgstr "Lomtárba helyezett bejegyzések"

#: inc/Engine/Admin/Database/Optimization.php:33
#: inc/Engine/Admin/Settings/Page.php:1505
msgid "Spam Comments"
msgstr "Spam kommentek"

#: inc/Engine/Admin/Database/Optimization.php:34
#: inc/Engine/Admin/Settings/Page.php:1515
msgid "Trashed Comments"
msgstr "Lomtárba helyezett hozzászólások"

#: inc/Engine/Admin/Database/Optimization.php:35
msgid "Transients"
msgstr "Tranziens-ek"

#: inc/Engine/Admin/Database/Optimization.php:36
msgid "Tables"
msgstr "Táblák"

#: inc/Engine/Admin/Database/Subscriber.php:79
#: inc/deprecated/Engine/Addon/FacebookTracking/Subscriber.php:92
#: inc/deprecated/Engine/Addon/GoogleTracking/Subscriber.php:137
msgid "weekly"
msgstr "heti"

#: inc/Engine/Admin/Database/Subscriber.php:85
msgid "monthly"
msgstr "havi"

#: inc/Engine/Admin/Database/Subscriber.php:194
msgid "Database optimization process is running"
msgstr "Adatbázis optimizálás folyamatban"

#: inc/Engine/Admin/Database/Subscriber.php:224
msgid "Database optimization process is complete. Everything was already optimized!"
msgstr "Adatbázis optimizálás kész. Már minden optimizálva van!"

#: inc/Engine/Admin/Database/Subscriber.php:227
msgid "Database optimization process is complete. List of optimized items below:"
msgstr "Adatbázis optimizálás kész. Az alábbi elemek lettek optimizálva:"

#: inc/Engine/Admin/Database/Subscriber.php:235
#, php-format
msgid "%1$d %2$s optimized."
msgstr "%1$d %2$s optimizálva lett."

#: inc/Engine/Admin/Settings/Page.php:201
msgid "Save Changes"
msgstr "Módosítások mentése"

#: inc/Engine/Admin/Settings/Page.php:201
msgid "Validate License"
msgstr "Licenc ellenőrzése"

#: inc/Engine/Admin/Settings/Page.php:257
#: inc/Engine/Admin/Settings/Page.php:258
msgid "Unavailable"
msgstr "Nem elérhető"

#: inc/Engine/Admin/Settings/Page.php:352
#: inc/deprecated/deprecated.php:1789
#: views/settings/page-sections/dashboard.php:80
msgid "License"
msgstr "Licenc"

#: inc/Engine/Admin/Settings/Page.php:369
msgid "API key"
msgstr "API kulcs"

#: inc/Engine/Admin/Settings/Page.php:384
msgid "Email address"
msgstr "Email cím"

#: inc/Engine/Admin/Settings/Page.php:410
msgid "Dashboard"
msgstr "Vezérlőpult"

#: inc/Engine/Admin/Settings/Page.php:411
msgid "Get help, account info"
msgstr "Segítség, fiók-információ"

#: inc/Engine/Admin/Settings/Page.php:420
msgid "My Status"
msgstr "Állapotom"

#: inc/Engine/Admin/Settings/Page.php:430
#: views/settings/page.php:75
msgid "Rocket Analytics"
msgstr "Rocket Analytics"

#: inc/Engine/Admin/Settings/Page.php:432
#, php-format
msgid "I agree to share anonymous data with the development team to help improve WP Rocket. %1$sWhat info will we collect?%2$s"
msgstr "Hozzájárulok ahhoz, hogy névtelen adatokat osszunk meg a fejlesztőcsapat felé, segítve ezzel a WP Rocket továbbfejlesztését. %1$sMilyen adatokat gyűjtünk?%2$s"

#: inc/Engine/Admin/Settings/Page.php:456
#: inc/Engine/Cache/WPCache.php:354
msgid "Cache"
msgstr "Gyorsítótár"

#: inc/Engine/Admin/Settings/Page.php:457
msgid "Basic cache options"
msgstr "Alapvető gyorsítótár beállítások"

#: inc/Engine/Admin/Settings/Page.php:464
msgid "Mobile Cache"
msgstr "Mobil gyorsítótár"

#: inc/Engine/Admin/Settings/Page.php:466
msgid "Speed up your site for mobile visitors."
msgstr "Gyorsítsd fel az oldaladat a mobilról érkező látogatók számára"

#: inc/Engine/Admin/Settings/Page.php:471
msgid "We detected you use a plugin that requires a separate cache for mobile, and automatically enabled this option for compatibility."
msgstr "Azt vettük észre, hogy olyan bővítményt használsz ami miatt kötelező külön gyorsítótárat használnod a mobileszközökhöz és így automatikusan be is kapcsoltuk ezt a lehetőséget a kompatibilitás érdekében."

#: inc/Engine/Admin/Settings/Page.php:475
msgid "User Cache"
msgstr "Bejelentkezett felhasználói gyorsítótár"

#: inc/Engine/Admin/Settings/Page.php:478
#, php-format
msgid "%1$sUser cache%2$s is great when you have user-specific or restricted content on your website."
msgstr "%1$sA felhasználói gyorsítótár%2$s nagyszerű lehetőség, ha vannak csak (bejelentkezett) felhasználók számára elérhető, vagy zárolt tartalmak az oldaladon."

#: inc/Engine/Admin/Settings/Page.php:486
msgid "Cache Lifespan"
msgstr "Gyorsítótár élettartama"

#: inc/Engine/Admin/Settings/Page.php:489
#, php-format
msgid "Cache files older than the specified lifespan will be deleted.<br>Enable %1$spreloading%2$s for the cache to be rebuilt automatically after lifespan expiration."
msgstr "Törlésre kerülnek az olyan gyorsítótárbeli fájlok amik ettől a megadott élettartamnál régebbiek.<br>Engedélyezd %1$saz előtöltést vagyis a preloadingot%2$s annak érdekében, hogy a gyorsítótárat automatikusan újraépíthessük ha lejárt az élettartama."

#: inc/Engine/Admin/Settings/Page.php:503
msgid "Enable caching for logged-in WordPress users"
msgstr "Gyorsítótárazás bekapcsolása bejelentkezett WordPress felhasználók számára is"

#: inc/Engine/Admin/Settings/Page.php:511
msgid "Enable caching for mobile devices"
msgstr "Gyorsítótárazás engedélyezése mobileszközök számára"

#: inc/Engine/Admin/Settings/Page.php:526
msgid "Separate cache files for mobile devices"
msgstr "Külön gyorsítótár-fájlok fenntartása a mobileszközökhöz"

#: inc/Engine/Admin/Settings/Page.php:528
#, php-format
msgid "Most modern themes are responsive and should work without a separate cache. Enable this only if you have a dedicated mobile theme or plugin. %1$sMore info%2$s"
msgstr "A legtöbb modern téma már reszponzív és ezért működnie kellene külön gyorsítótár nélkül is. Ezért csak akkor kapcsold ezt be, ha a rendes témádon felül van egy KÜLÖN mobiltémád vagy bővítményed is. %1$sTovábbi információ%2$s"

#: inc/Engine/Admin/Settings/Page.php:544
msgid "Specify time after which the global cache is cleared<br>(0 = unlimited )"
msgstr "Add meg, hogy mennyi idő után legyen ürítve a globális gyorsítótár<br>(0=soha)"

#: inc/Engine/Admin/Settings/Page.php:546
#, php-format
msgid "Reduce lifespan to 10 hours or less if you notice issues that seem to appear periodically. %1$sWhy?%2$s"
msgstr "Csökkentsd 10 órára vagy kevesebbre az élettartamot, ha olyan problémákat tapasztalsz ami csak időnként, de rendszeresen előfordul. %1$sMiért?%2$s"

#: inc/Engine/Admin/Settings/Page.php:552
#: inc/Engine/License/views/promo-banner.php:30
#: inc/Engine/License/views/renewal-soon-banner.php:13
msgid "Hours"
msgstr "Órák"

#: inc/Engine/Admin/Settings/Page.php:553
#: inc/Engine/License/views/promo-banner.php:29
#: inc/Engine/License/views/renewal-soon-banner.php:12
msgid "Days"
msgstr "Napok"

#: inc/Engine/Admin/Settings/Page.php:602
msgid "File Optimization"
msgstr "Fájl optimizálás"

#: inc/Engine/Admin/Settings/Page.php:603
msgid "Optimize CSS & JS"
msgstr "CSS & JS optimizálás"

#: inc/Engine/Admin/Settings/Page.php:621
msgid "CSS Files"
msgstr "CSS fájlok"

#: inc/Engine/Admin/Settings/Page.php:611
#: inc/Engine/Admin/Settings/Page.php:637
#, php-format
msgid "%1$s Minification is currently activated in <strong>Autoptimize</strong>. If you want to use %2$s’s minification, disable those options in Autoptimize."
msgstr "%1$s a minification jelenleg aktiválva van a <strong>Autoptimize</strong>-ban. Ha szeretnéd használni a %2$sminification-jét, akkor tiltsd le az erre vonatkozó opciókat az Autoptimize-ban először."

#: inc/Engine/Admin/Settings/Page.php:630
msgid "JavaScript Files"
msgstr "JavaScript fájlok"

#: inc/Engine/Admin/Settings/Page.php:663
msgid "Minify CSS files"
msgstr "CSS fájlok miniatűrizálása"

#: inc/Engine/Admin/Settings/Page.php:664
msgid "Minify CSS removes whitespace and comments to reduce the file size."
msgstr "A CSS miniatűrizálás kiszedi a felesleges szóközöket és kommenteket, ezáltal csökkenti a fájlméretet."

#: inc/Engine/Admin/Settings/Page.php:677
#: inc/Engine/Admin/Settings/Page.php:701
#: inc/Engine/Admin/Settings/Page.php:765
#: inc/Engine/Admin/Settings/Page.php:831
#: inc/Engine/Admin/Settings/Page.php:855
msgid "This could break things!"
msgstr "Ez elronthat dolgokat az oldalon!"

#: inc/Engine/Admin/Settings/Page.php:678
#: inc/Engine/Admin/Settings/Page.php:702
#: inc/Engine/Admin/Settings/Page.php:766
#: inc/Engine/Admin/Settings/Page.php:832
#: inc/Engine/Admin/Settings/Page.php:856
msgid "If you notice any errors on your website after having activated this setting, just deactivate it again, and your site will be back to normal."
msgstr "Ha bármilyen hibát tapasztalsz a weblapodon miután bekapcsoltad ezt az opciót, akkor egyszerűen kapcsold ki itt és az oldalad újra rendesen fog működni."

#: inc/Engine/Admin/Settings/Page.php:679
msgid "Activate minify CSS"
msgstr "CSS miniatűrizálás bekapcsolása"

#: inc/Engine/Admin/Settings/Page.php:684
msgid "Combine CSS files <em>(Enable Minify CSS files to select)</em>"
msgstr "CSS fájlok összevonása<em>(Kapcsold be a CSS miniatűrizálást először)</em>"

#: inc/Engine/Admin/Settings/Page.php:686
#, php-format
msgid "Combine CSS merges all your files into 1, reducing HTTP requests. Not recommended if your site uses HTTP/2. %1$sMore info%2$s"
msgstr "A CSS egyesítése funkció összevonja a CSS fájljaidat egy darabbá, csökkentve a HTTP kéréseket. Nem ajánlott ha az oldalad HTTP/2-t használ. %1$sTovábbi információ%2$s"

#: inc/Engine/Admin/Settings/Page.php:687
msgid "For compatibility and best results, this option is disabled when Remove unused CSS is enabled."
msgstr "Kompatibilitási okokból ez az opció le van tiltva, ha a Nem-használt CSS eltávolítása engedélyezve van."

#: inc/Engine/Admin/Settings/Page.php:703
msgid "Activate combine CSS"
msgstr "CSS egyesítés bekapcsolása"

#: inc/Engine/Admin/Settings/Page.php:708
#: inc/admin/options.php:124
msgid "Excluded CSS Files"
msgstr "Kizárt CSS fájlok"

#: inc/Engine/Admin/Settings/Page.php:709
msgid "Specify URLs of CSS files to be excluded from minification and concatenation (one per line)."
msgstr "Add meg itt azon CSS fájlok URLjeit amik ki lesznek hagyva a miniatűrizálásból és összevonásból (soronként egyet írj)."

#: inc/Engine/Admin/Settings/Page.php:710
msgid "<strong>Internal:</strong> The domain part of the URL will be stripped automatically. Use (.*).css wildcards to exclude all CSS files located at a specific path."
msgstr "<strong>Belső:</strong> Az URL-ből a domain-részz automatikusan ki lesz dobva. Ezért használj (.*).css wildcard jelöléseket ha minden CSS fájlt szeretnéd kizárni egy bizonyos útvonalon."

#: inc/Engine/Admin/Settings/Page.php:712
#, php-format
msgid "<strong>3rd Party:</strong> Use either the full URL path or only the domain name, to exclude external CSS. %1$sMore info%2$s"
msgstr "<strong>Külsős forrás:</strong> Vagy a teljes elérési útvonalat (URL-t) add meg, vagy csak a domain nevet ahhoz, hogy kizárhasd a külső féltől származó CSS-t. %1$sTovábbi információ%2$s"

#: inc/Engine/Admin/Settings/Page.php:725
#: inc/Engine/Admin/Settings/Page.php:747
msgid "Optimize CSS delivery"
msgstr "CSS kiküldés optimizálása"

#: inc/Engine/Admin/Settings/Page.php:730
msgid "Optimize CSS delivery eliminates render-blocking CSS on your website. Only one method can be selected. Remove Unused CSS is recommended for optimal performance, but limited only to the users with active license."
msgstr "Az optimizált CSS kiküldés megszünteti a renderelést lassító CSS kódokat az oldaladon. Csak az egyik módszer használható. A Nem-használt CSS kódok eltávolítása opciót javasoljuk inkább a jobb teljesítmény érdekében, de az csak az aktív licensszel rendelkező felhasználóknak elérhető."

#: inc/Engine/Admin/Settings/Page.php:730
msgid "Optimize CSS delivery eliminates render-blocking CSS on your website. Only one method can be selected. Remove Unused CSS is recommended for optimal performance."
msgstr "A CSS-küldés optimizáció megszünteti az úgynevezett render-blocking CSS problémákat a weboldalon, amikor valamelyik CSS fájl feldolgozására várni kellene, mielőtt az oldal betöltése folytatódhatna. Csak egy módszer választható. A \"Távolítsa el a nem-használt CSS kódokat\" opciót ajánljuk a legjobb teljesítmény érdekében."

#: inc/Engine/Admin/Settings/Page.php:740
#, php-format
msgid "Optimize CSS Delivery features are disabled on local environments. %1$sLearn more%2$s"
msgstr "Az optimizált CSS kiküldés funkciók nem elérhetőek helyi környezetben. %1$sTudj meg többet%2$s"

#: inc/Engine/Admin/Settings/Page.php:763
#, php-format
msgid "Removes unused CSS per page and helps to reduce page size and HTTP requests. Recommended for best performance. Test thoroughly! %1$sMore info%2$s"
msgstr "Eltávolítja a használaton kívüli CSS-t oldalanként és ezáltal segít csökkenteni az oldal méretét és a HTTP kérések számát. Ajánlott funkció a legjobb teljesítmény érdekében, azonban alaposan teszteld! %1$sTovábbi információ%2$s"

#: inc/Engine/Admin/Settings/Page.php:767
msgid "Activate Remove Unused CSS"
msgstr "Aktiváld a nem-használt CSS eltávolító funkciót"

#: inc/Engine/Admin/Settings/Page.php:773
msgid "CSS safelist"
msgstr "Védett/biztonságos CSS kódok listája"

#: inc/Engine/Admin/Settings/Page.php:774
msgid "Specify CSS filenames, IDs or classes that should not be removed (one per line)."
msgstr "Add meg a CSS fájlok fájlneveit vagy az azokban szereplő ID-ket vagy class-ek nevét, amiket nem szabad eltávolítanunk ezen funkció keretében. (Minden sorba egyet írj)"

#: inc/Engine/Admin/Settings/Page.php:789
#: inc/admin/ui/meta-boxes.php:109
msgid "Load CSS asynchronously"
msgstr "Aszinkron CSS betöltés"

#: inc/Engine/Admin/Settings/Page.php:792
#, php-format
msgctxt "WP Critical CSS compatibility"
msgid "Load CSS asynchronously is currently handled by the %1$s plugin. If you want to use WP Rocket’s load CSS asynchronously option, disable the %1$s plugin."
msgstr "Az aszinkron CSS betöltést jelenleg a %1$s bővítmény végzi. Ha szeretnéd erre a célra inkább a WP Rocket aszinkron CSS funkcióját használni akkor tiltsd le az %1$s bővítményt."

#: inc/Engine/Admin/Settings/Page.php:794
#, php-format
msgid "Generates critical path CSS and loads CSS asynchronously. %1$sMore info%2$s"
msgstr "Létrehozza a kritikus útvonalú CSS-eket és aszinkron módon tölti be a CSS-eket. %1$sTovábbi információ%2$s"

#: inc/Engine/Admin/Settings/Page.php:800
msgid "Fallback critical CSS"
msgstr "Tartalék kritikus CSS"

#: inc/Engine/Admin/Settings/Page.php:802
#, php-format
msgid "Provides a fallback if auto-generated critical path CSS is incomplete. %1$sMore info%2$s"
msgstr "Tartalék lehetőséget biztosít arra az esetre, ha az automatikusan generált kritikus útvonalú CSS lista nem megfelelő, vagy hiányos. %1$sTovábbi információ%2$s"

#: inc/Engine/Admin/Settings/Page.php:817
msgid "Minify JavaScript files"
msgstr "JavaScript fájlok miniatűrizálása"

#: inc/Engine/Admin/Settings/Page.php:818
msgid "Minify JavaScript removes whitespace and comments to reduce the file size."
msgstr "A Javascript miniatűrizálás kiszedi a felesleges szóközöket és kommenteket, ezáltal csökkenti a fájlméretet."

#: inc/Engine/Admin/Settings/Page.php:833
msgid "Activate minify JavaScript"
msgstr "Javascript miniatűrizálás bekapcsolása"

#: inc/Engine/Admin/Settings/Page.php:838
msgid "Combine JavaScript files <em>(Enable Minify JavaScript files to select)</em>"
msgstr "Javascript fájlok összevonása<em>(Kapcsold be a Javasript miniatűrizálást is ehhez)</em>"

#: inc/Engine/Admin/Settings/Page.php:840
#, php-format
msgid "Combine JavaScript files combines your site’s internal, 3rd party and inline JS reducing HTTP requests. Not recommended if your site uses HTTP/2. %1$sMore info%2$s"
msgstr "A Javascript fájlok egyesítése funkció összevonja az oldaladon lévő helyi, a harmadik féltől származó és a beágyazott fájljaidat egyetlen darabbá, csökkentve a HTTP kéréseket. Ez nem ajánlott ha az oldalad HTTP/2-t használ.  %1$sbővebb információ%2$s"

#: inc/Engine/Admin/Settings/Page.php:841
msgid "For compatibility and best results, this option is disabled when delay javascript execution is enabled."
msgstr "A legjobb kompatibilitás érdekében ez az opció le van tiltva, ha a javascript késleltetett futtatása funkció engedélyezve van."

#: inc/Engine/Admin/Settings/Page.php:857
msgid "Activate combine JavaScript"
msgstr "Aktiváld a Javascript fájlok összevonását"

#: inc/Engine/Admin/Settings/Page.php:862
#: inc/admin/options.php:125
msgid "Excluded Inline JavaScript"
msgstr "Kihagyott beágyazott JavaScript kódok"

#: inc/Engine/Admin/Settings/Page.php:864
#, php-format
msgid "Specify patterns of inline JavaScript to be excluded from concatenation (one per line). %1$sMore info%2$s"
msgstr "Adj meg beágyazott JavaScript mintákat amiket kizárunk az összevonásból. (soronként egyet) %1$sTovábbi információ%2$s"

#: inc/Engine/Admin/Settings/Page.php:880
#: inc/Engine/Admin/Settings/Page.php:913
#: inc/Engine/Admin/Settings/Page.php:960
#: inc/admin/options.php:126
msgid "Excluded JavaScript Files"
msgstr "Kihagyott javascript fájlok"

#: inc/Engine/Admin/Settings/Page.php:881
msgid "Specify URLs of JavaScript files to be excluded from minification and concatenation (one per line)."
msgstr "Itt adhatod meg azon JavaScript fájlok elérési útvonalát (URL-t) amiket ki szeretnél zárni a miniatűrizálásból és összevonásból. (Soronként egyet írj)"

#: inc/Engine/Admin/Settings/Page.php:882
msgid "<strong>Internal:</strong> The domain part of the URL will be stripped automatically. Use (.*).js wildcards to exclude all JS files located at a specific path."
msgstr "<strong>Helyi:</strong> Az URL domain-része automatikusan le lesz vágva. Használj helyettesítőkarakterket (.*).js ha minden JS fájlt ki szeretnél zárni az adott elérési útvonalon."

#: inc/Engine/Admin/Settings/Page.php:884
#, php-format
msgid "<strong>3rd Party:</strong> Use either the full URL path or only the domain name, to exclude external JS. %1$sMore info%2$s"
msgstr "<strong> Idegen forrásból származó:</strong> Adj meg vagy teljes URL címet vagy csak a domain nevet, hogy kizárd a külső forrásból származó JS. %1$sBővebb információ%2$s"

#: inc/Engine/Admin/Settings/Page.php:900
msgid "Load JavaScript deferred"
msgstr "JavaScript párhuzamos betöltése"

#: inc/Engine/Admin/Settings/Page.php:902
#, php-format
msgid "Load JavaScript deferred eliminates render-blocking JS on your site and can improve load time. %1$sMore info%2$s"
msgstr "Ha párhuzamosan töltődik be a JavaScript (deferred), akkor a böngészőnek nem kell megvárnia az egyes JavaScript kód lefutását, hanem azzal egyidőben az oldal többi részét már betölti, ezáltal javulhat a betöltési idő. %1$sBővebb információ%2$s"

#: inc/Engine/Admin/Settings/Page.php:915
#, php-format
msgid "Specify URLs or keywords of JavaScript files to be excluded from defer (one per line). %1$sMore info%2$s"
msgstr "Adj meg JavaScript fájlokhoz vezető URL-eket vagy kulcsszavakat amiket ki szeretnél zárni a párhuzamos betöltésből. (Soronként egyet írj). %1$sBővebb információ%2$s"

#: inc/Engine/Admin/Settings/Page.php:931
#: inc/admin/ui/meta-boxes.php:111
msgid "Delay JavaScript execution"
msgstr "Késleltesd a JavaScript futtatását"

#: inc/Engine/Admin/Settings/Page.php:933
#, php-format
msgid "Improves performance by delaying the loading of JavaScript files until user interaction (e.g. scroll, click). %1$sMore info%2$s"
msgstr "Javítja a teljesítményt azáltal hogy késlelteti a JavaScript fájlok betöltését egészen addig amíg nem történik valamilyen felhasználói interakció (pl. kattintás, görgetés). %1$sBővebb információ%2$s"

#: inc/Engine/Admin/Settings/Page.php:961
msgid "Specify URLs or keywords that can identify inline or JavaScript files to be excluded from delaying execution (one per line)."
msgstr "Adj meg URL-eket vagy kulcsszavakat amikkel beazonosíthatóak a kódba ágyazott JavaScript fájlok vagy kódrészletek, amiket ki szeretnél zárni a késleltetett betöltésből. (soronként egy)"

#: inc/Engine/Admin/Settings/Page.php:993
msgid "Media"
msgstr "Média"

#: inc/Engine/Admin/Settings/Page.php:994
msgid "LazyLoad, image dimensions"
msgstr "LazyLoad, képméretek"

#: inc/Engine/Admin/Settings/Page.php:1003
msgid "Autoptimize"
msgstr "Autoptimize"

#: inc/Engine/Admin/Settings/Page.php:1048
msgid "LazyLoad"
msgstr "LazyLoad"

#: inc/Engine/Admin/Settings/Page.php:1051
#, php-format
msgid "It can improve actual and perceived loading time as images, iframes, and videos will be loaded only as they enter (or about to enter) the viewport and reduces the number of HTTP requests. %1$sMore Info%2$s"
msgstr "Javítani tudja a valódi és vélt betöltési időt mivel a képek, iframek és videók csak akkor lesznek betöltve amikor belépnek (vagy be fognak lépni) a látható területre és csökkenti a HTTP kérések számát. %1$sBővebb információ%2$s"

#: inc/Engine/Admin/Settings/Page.php:1058
#, php-format
msgid "LazyLoad is currently activated in %2$s. If you want to use WP Rocket’s LazyLoad, disable this option in %2$s."
msgstr "LazyLoad jelenleg aktiválva van a(z) %2$s-ben. Ha szeretnéd inkább a WP Rocket LazyLoad-ját használni akkor tiltsd le ezt az opciót a  %2$s-ben."

#: inc/Engine/Admin/Settings/Page.php:1061
msgid "Image Dimensions"
msgstr "Képméretek"

#: inc/Engine/Admin/Settings/Page.php:1064
#, php-format
msgid "Add missing width and height attributes to images. Helps prevent layout shifts and improve the reading experience for your visitors. %1$sMore info%2$s"
msgstr "Automatikusan adjuk hozzá a szélesség és magasság attribútomokat a képekhez, ahonnan ezek hiányoznak. Segít megelőzni az oldalszerkezet elmozdulását és ezáltal javítja az olvasási élményt a látogatóid számára. %1$sBővebb információ%2$s"

#: inc/Engine/Admin/Settings/Page.php:1083
msgid "Enable for images"
msgstr "Engedélyezés képek számára"

#: inc/Engine/Admin/Settings/Page.php:1095
#, php-format
msgid "LazyLoad for images is currently activated in %2$s. If you want to use %1$s’s LazyLoad, disable this option in %2$s."
msgstr "A LazyLoad képekhez engedélyezve van itt: %2$s. Ha inkább a %1$s LazyLoad-ját szeretnéd használni akkor tiltsd le ezt az opciót itt: %2$s."

#: inc/Engine/Admin/Settings/Page.php:1103
msgid "Enable for iframes and videos"
msgstr "Engedélyezés iframe-k és videók számára"

#: inc/Engine/Admin/Settings/Page.php:1118
msgid "Replace YouTube iframe with preview image"
msgstr "Cseréld ki a YouTube iframe-eket egy előnézeti képpel"

#: inc/Engine/Admin/Settings/Page.php:1120
#, php-format
msgid "Replace YouTube iframe with preview image is not compatible with %2$s."
msgstr "A YouTube iframe előnézeti képre való kicserélése nem kompatibilis ezzel: %2$s."

#: inc/Engine/Admin/Settings/Page.php:1120
msgid "This can significantly improve your loading time if you have a lot of YouTube videos on a page."
msgstr "Ez jelentősen javíthatja a betöltési idődet, ha sok YouTube videód van egy oldalon."

#: inc/Engine/Admin/Settings/Page.php:1135
msgid "Excluded images or iframes"
msgstr "Kizárt/kihagyott képek és iframek"

#: inc/Engine/Admin/Settings/Page.php:1137
#, php-format
msgid "Specify keywords (e.g. image filename, CSS class, domain) from the image or iframe code to be excluded (one per line). %1$sMore info%2$s"
msgstr "Adj meg kulcsszavakat (pl. képek fájlneveit, CSS osztályokat, domain neveket) a kép vagy iframe kódjából a kizáráshoz/kihagyáshoz (soronként egyet írj). %1$sBővebb információ%2$s"

#: inc/Engine/Admin/Settings/Page.php:1145
msgid "Add missing image dimensions"
msgstr "Hiányzó képméretek beírása"

#: inc/Engine/Admin/Settings/Page.php:1164
#: inc/deprecated/deprecated.php:1776
msgid "Preload"
msgstr "Előtöltés"

#: inc/Engine/Admin/Settings/Page.php:1165
msgid "Generate cache files, preload fonts"
msgstr "Gyorsítótár fájlok generálása valamint betűtípus fájlok előtöltése"

#: inc/Engine/Admin/Settings/Page.php:1177
msgid "Preload Cache"
msgstr "Gyorsítótár előtöltése"

#: inc/Engine/Admin/Settings/Page.php:1188
msgid "Preload Links"
msgstr "Linkek előtöltése"

#: inc/Engine/Admin/Settings/Page.php:1191
#, php-format
msgid "Link preloading improves the perceived load time by downloading a page when a user hovers over the link. %1$sMore info%2$s"
msgstr "A linkek előtöltése javítja a felhasználók számára látható betöltési időt azáltal, hogy már akkor letölti az oldalt amikor a felhasználó ráviszi az egerét a linkre. %1$sTovábbi információ%2$s"

#: inc/Engine/Admin/Settings/Page.php:1199
msgid "Prefetch DNS Requests"
msgstr "DNS kérések előtöltése"

#: inc/Engine/Admin/Settings/Page.php:1201
msgid "DNS prefetching can make external files load faster, especially on mobile networks"
msgstr "A DNS előtöltéssel a külső forrásból származó fájlok gyorsabban töltődhetnek be, különösen mobilhálózatokon"

#: inc/Engine/Admin/Settings/Page.php:1206
msgid "Preload Fonts"
msgstr "Betűtípusok előtöltése"

#: inc/Engine/Admin/Settings/Page.php:1209
#, php-format
msgid "Improves performance by helping browsers discover fonts in CSS files. %1$sMore info%2$s"
msgstr "Javítja a teljesítményt azáltal, hogy segít a böngészőknek megtalálni a CSS fájlokban szereplő betűtípusokat.%1$sTovábbi információ%2$s"

#: inc/Engine/Admin/Settings/Page.php:1223
msgid "Activate Preloading"
msgstr "Előtöltés aktiválása"

#: inc/Engine/Admin/Settings/Page.php:1250
msgid "URLs to prefetch"
msgstr "Előtöltendő URL-ek"

#: inc/Engine/Admin/Settings/Page.php:1251
msgid "Specify external hosts to be prefetched (no <code>http:</code>, one per line)"
msgstr "Add meg itt azokat a külső szervereket amiket szeretnél előtölteni (ne írj<code>http:</code>-t, soronként egyet írj)"

#: inc/Engine/Admin/Settings/Page.php:1260
msgid "Fonts to preload"
msgstr "Előtöltendő betűtípusok"

#: inc/Engine/Admin/Settings/Page.php:1261
msgid "Specify urls of the font files to be preloaded (one per line). Fonts must be hosted on your own domain, or the domain you have specified on the CDN tab."
msgstr "Add meg azon betútípus fájlok URL-jeit amiket elő szeretnél tölteni (soronként egyet írj). A betűtípusoknak muszáj a saját domaineden lenniük, vagy azon a domainen amit a CDN fülön adtál meg."

#: inc/Engine/Admin/Settings/Page.php:1262
msgid "The domain part of the URL will be stripped automatically.<br/>Allowed font extensions: otf, ttf, svg, woff, woff2."
msgstr "Az URL-ből a domain rész automatikusan törölve lesz. <br/>Támogatott font kiterjesztések:  otf, ttf, svg, woff, woff2."

#: inc/Engine/Admin/Settings/Page.php:1271
msgid "Enable link preloading"
msgstr "Link előtöltés engedélyezése"

#: inc/Engine/Admin/Settings/Page.php:1290
msgid "Advanced Rules"
msgstr "Haladó beállítások"

#: inc/Engine/Admin/Settings/Page.php:1291
msgid "Fine-tune cache rules"
msgstr "Gyorsítótár szabályok finomhangolása"

#: inc/Engine/Admin/Settings/Page.php:1302
msgid "Sensitive pages like custom login/logout URLs should be excluded from cache."
msgstr "Az érzékeny adatokkal dolgozó oldalakat, mint pl. az egyéni bejelentkező/kijelentkező URL-eket célszerű kizárni a gyorsítótárazásból."

#: inc/Engine/Admin/Settings/Page.php:1305
msgctxt "plugin name"
msgid "WooCommerce"
msgstr "WooCommerce"

#: inc/Engine/Admin/Settings/Page.php:1307
msgctxt "plugin name"
msgid "Easy Digital Downloads"
msgstr "Easy Digital Downloads"

#: inc/Engine/Admin/Settings/Page.php:1309
msgctxt "plugin name"
msgid "iThemes Exchange"
msgstr "iThemes Exchange"

#: inc/Engine/Admin/Settings/Page.php:1311
msgctxt "plugin name"
msgid "Jigoshop"
msgstr "Jigoshop"

#: inc/Engine/Admin/Settings/Page.php:1313
msgctxt "plugin name"
msgid "WP-Shop"
msgstr "WP-Shop"

#: inc/Engine/Admin/Settings/Page.php:1319
#, php-format
msgid "<br>Cart, checkout and \"my account\" pages set in <strong>%1$s%2$s%3$s</strong> will be detected and never cached by default."
msgstr "<br>Kosár, fizetés és \"saját fiókom\" oldalak beállítva itt: <strong>%1$s%2$s%3$s</strong> automatikusan érzékelve vannak és nem lesznek soha gyorsítótárazva alapbeállításként."

#: inc/Engine/Admin/Settings/Page.php:1329
#: inc/admin/options.php:129
msgid "Never Cache URL(s)"
msgstr "Soha ne gyorsítótárazzuk az alábbi URL(eke)t"

#: inc/Engine/Admin/Settings/Page.php:1337
msgid "Never Cache Cookies"
msgstr "Soha ne gyorsítótárazzuk ezeket a sütiket"

#: inc/Engine/Admin/Settings/Page.php:1343
#: inc/admin/options.php:130
msgid "Never Cache User Agent(s)"
msgstr "Soha ne gyorsítótárazzunk az alábbi user agent-eknél"

#: inc/Engine/Admin/Settings/Page.php:1349
#: inc/admin/options.php:131
msgid "Always Purge URL(s)"
msgstr "Mindig ürítsd az alábbi URL(ek) gyorsítótárát"

#: inc/Engine/Admin/Settings/Page.php:1355
msgid "Cache Query String(s)"
msgstr "Gyorsítótár lekérő sztring(ek)"

#: inc/Engine/Admin/Settings/Page.php:1358
#, php-format
msgid "%1$sCache for query strings%2$s enables you to force caching for specific GET parameters."
msgstr "%1$sGyorsítótár az alábbi lekérő sztringekhez%2$s lehetővé teszi számodra hogy kényszerítsd a gyorsítótárazást bizonyos GET paraméterek esetén."

#: inc/Engine/Admin/Settings/Page.php:1369
msgid "Specify URLs of pages or posts that should never be cached (one per line)"
msgstr "Add meg bejegyzések vagy oldalak URL-jeit amiket soha nem szeretnél gyorsítótárazni (soronként egyet)"

#: inc/Engine/Admin/Settings/Page.php:1370
#: inc/Engine/Admin/Settings/Page.php:1398
msgid "The domain part of the URL will be stripped automatically.<br>Use (.*) wildcards to address multiple URLs under a given path."
msgstr "Az URL domain-része automatikusan törölve lesz. <br>Használj (.*) wildcard-okat hogy több URL-t is érvényesíthess egy elérési útvonal alatt."

#: inc/Engine/Admin/Settings/Page.php:1379
msgid "Specify full or partial IDs of cookies that, when set in the visitor's browser, should prevent a page from getting cached (one per line)"
msgstr "Adj meg teljesen vagy részleges ID-t azon cookiekból, amik ha be vannak állíva a látogató böngészőjében akkor meg szretnéd akadályozni az adott oldal gyorsítótárazását (soronként egyet írj)"

#: inc/Engine/Admin/Settings/Page.php:1387
msgid "Specify user agent strings that should never see cached pages (one per line)"
msgstr "Adj meg user agent string-eket itt, amiknél nem szeretnéd ha gyorítótárazás lenne. (soronként egy)"

#: inc/Engine/Admin/Settings/Page.php:1388
msgid "Use (.*) wildcards to detect parts of UA strings."
msgstr "Használj (.*) wildcardokat hogy UA sztringek részleteit ismerd fel. "

#: inc/Engine/Admin/Settings/Page.php:1397
msgid "Specify URLs you always want purged from cache whenever you update any post or page (one per line)"
msgstr "Adj meg URL-eket amiket mindig üríteni szeretnél a gyorsítótárazásból amikor frissítessz bármilyen bejegyzést vagy oldalt. (soronként egyet)"

#: inc/Engine/Admin/Settings/Page.php:1406
msgid "Specify query strings for caching (one per line)"
msgstr "Adj meg lekérdező sztringeket a gyorsítótárazáshoz (soronként egyet)"

#: inc/Engine/Admin/Settings/Page.php:1431
#: inc/deprecated/deprecated.php:1775
msgid "Database"
msgstr "Adatbázis"

#: inc/Engine/Admin/Settings/Page.php:1432
msgid "Optimize, reduce bloat"
msgstr "Optimizálás, szemetek törlése"

#: inc/Engine/Admin/Settings/Page.php:1439
msgid "Post Cleanup"
msgstr "Bejegyzés takarítás"

#: inc/Engine/Admin/Settings/Page.php:1441
msgid "Post revisions and drafts will be permanently deleted. Do not use this option if you need to retain revisions or drafts."
msgstr "A bejegyzés változatok és piszkozatok végleg törölve lesznek. Ne használd ezt az opciót, ha meg szeretnéd tartani a bejegyzések előző verziót vagy a piszkozatokat."

#: inc/Engine/Admin/Settings/Page.php:1446
msgid "Comments Cleanup"
msgstr "Kommentek takarítása"

#: inc/Engine/Admin/Settings/Page.php:1448
msgid "Spam and trashed comments will be permanently deleted."
msgstr "A spam és lomtárba helyezett kommentek végleg törölve lesznek"

#: inc/Engine/Admin/Settings/Page.php:1452
msgid "Transients Cleanup"
msgstr "Tranziensek törlése"

#: inc/Engine/Admin/Settings/Page.php:1454
msgid "Transients are temporary options; they are safe to remove. They will be automatically regenerated as your plugins require them."
msgstr "A tranziensek ideiglenes opciók, biztonságosan törölhetőek. Automatikusan újra lesznek generálva ha a pluginjaidnak kellenek."

#: inc/Engine/Admin/Settings/Page.php:1458
msgid "Database Cleanup"
msgstr "Adatbázis takarítás"

#: inc/Engine/Admin/Settings/Page.php:1460
msgid "Reduces overhead of database tables"
msgstr "Törli a felesleges adatokat az adatbázis táblából"

#: inc/Engine/Admin/Settings/Page.php:1464
msgid "Automatic Cleanup"
msgstr "Automatikus takarítás"

#: inc/Engine/Admin/Settings/Page.php:1477
#, php-format
msgid "%s revision in your database."
msgid_plural "%s revisions in your database."
msgstr[0] "%s változat van az adatbázisodban."
msgstr[1] "%s változat van az adatbázisodban."

#: inc/Engine/Admin/Settings/Page.php:1487
#, php-format
msgid "%s draft in your database."
msgid_plural "%s drafts in your database."
msgstr[0] "%spiszkozat van az adatbázisodban."
msgstr[1] "%s piszkozat van az adatbázisodban."

#: inc/Engine/Admin/Settings/Page.php:1497
#, php-format
msgid "%s trashed post in your database."
msgid_plural "%s trashed posts in your database."
msgstr[0] "%s törölt bejegyzés van az adatbázisodban."
msgstr[1] "%s törölt bejegyzés van az adatbázisodban."

#: inc/Engine/Admin/Settings/Page.php:1507
#, php-format
msgid "%s spam comment in your database."
msgid_plural "%s spam comments in your database."
msgstr[0] "%sspam komment van az adatbázisodban."
msgstr[1] "%s spam komment van az adatbázisodban."

#: inc/Engine/Admin/Settings/Page.php:1517
#, php-format
msgid "%s trashed comment in your database."
msgid_plural "%s trashed comments in your database."
msgstr[0] "%slomtárba helyezett komment van az adatbázisodban."
msgstr[1] "%slomtárba helyezett komment van az adatbázisodban."

#: inc/Engine/Admin/Settings/Page.php:1525
msgid "All transients"
msgstr "Minden tranziens"

#: inc/Engine/Admin/Settings/Page.php:1527
#, php-format
msgid "%s transient in your database."
msgid_plural "%s transients in your database."
msgstr[0] "%stranziens van az adatbázisodban."
msgstr[1] "%s tranziens van az adatbázisodban."

#: inc/Engine/Admin/Settings/Page.php:1535
msgid "Optimize Tables"
msgstr "Táblák optimizálása"

#: inc/Engine/Admin/Settings/Page.php:1537
#, php-format
msgid "%s table to optimize in your database."
msgid_plural "%s tables to optimize in your database."
msgstr[0] "%s táblát kell optimizálni az adatbázisodban."
msgstr[1] "%s táblát kell optimizálni az adatbázisodban."

#: inc/Engine/Admin/Settings/Page.php:1548
msgid "Schedule Automatic Cleanup"
msgstr "Ütemezd az automatikus takarítást"

#: inc/Engine/Admin/Settings/Page.php:1560
msgid "Frequency"
msgstr "Gyakoriság"

#: inc/Engine/Admin/Settings/Page.php:1568
msgid "Daily"
msgstr "Naponta"

#: inc/Engine/Admin/Settings/Page.php:1569
msgid "Weekly"
msgstr "Hetente"

#: inc/Engine/Admin/Settings/Page.php:1570
msgid "Monthly"
msgstr "Havonta"

#: inc/Engine/Admin/Settings/Page.php:1586
#: inc/Engine/Admin/Settings/Page.php:1597
#: inc/admin/ui/meta-boxes.php:108
#: inc/deprecated/deprecated.php:1773
msgid "CDN"
msgstr "CDN"

#: inc/Engine/Admin/Settings/Page.php:1587
msgid "Integrate your CDN"
msgstr "Integráld a CDN-edet"

#: inc/Engine/Admin/Settings/Page.php:1599
msgid "All URLs of static files (CSS, JS, images) will be rewritten to the CNAME(s) you provide."
msgstr "Minden statikus fájl (CSS, JS, képek) URL-je át lesz írva arra a CNAME(ek)-re amit itt megadsz."

#: inc/Engine/Admin/Settings/Page.php:1601
#, php-format
msgid "Not required for services like Cloudflare and Sucuri. Please see our available %1$sAdd-ons%2$s."
msgstr "Nem szükséges olyan szolgáltatók esetén mint a Cloudflare és Sucuri. Kérlek nézd meg az elérhető %1$skiegészítőinket%2$s."

#: inc/Engine/Admin/Settings/Page.php:1616
#: inc/admin/options.php:132
msgid "Exclude files from CDN"
msgstr "Zárj ki fájlokat a CDN-ről"

#: inc/Engine/Admin/Settings/Page.php:1642
msgid "%1$s%2$l Add-on%3$s is currently enabled. Configuration of the CDN settings is not required for %2$l to work on your site."
msgid_plural "%1$s%2$l Add-ons%3$s are currently enabled. Configuration of the CDN settings is not required for %2$l to work on your site."
msgstr[0] "%1$s%2$l Kiegészítő%3$s van jelenleg engedélyezve. A CDN beállítása nem szükséges a %2$l -hez hogy működjön az oldaladon"
msgstr[1] "%1$s%2$l Kiegészítők%3$svannak jelenleg engedélyezve. A CDN beállítása nem szükséges a %2$l -hez hogy működjön az oldaladon"

#: inc/Engine/Admin/Settings/Page.php:1667
msgid "Enable Content Delivery Network"
msgstr "Engedélyezd a CDN-t"

#: inc/Engine/Admin/Settings/Page.php:1676
#: inc/Engine/CDN/RocketCDN/AdminPageSubscriber.php:159
msgid "CDN CNAME(s)"
msgstr "CDN CNAME(ek)"

#: inc/Engine/Admin/Settings/Page.php:1677
#: inc/Engine/CDN/RocketCDN/AdminPageSubscriber.php:160
msgid "Specify the CNAME(s) below"
msgstr "Add meg a CNAME(eket) alább"

#: inc/Engine/Admin/Settings/Page.php:1684
msgid "Specify URL(s) of files that should not get served via CDN (one per line)."
msgstr "Add meg azon fájl(ok) URL-jeit amit nem szeretnél a CDN-en keresztül kiszolgálni (soronként egyet)."

#: inc/Engine/Admin/Settings/Page.php:1685
msgid "The domain part of the URL will be stripped automatically.<br>Use (.*) wildcards to exclude all files of a given file type located at a specific path."
msgstr "Az URL domain-része automatikusan törölve lesz. <br>Használj (.*) wildcard-okat hogy minden fájlt kizárj egy bizonyos fájltípusból a megadott elérési útvonalon."

#: inc/Engine/Admin/Settings/Page.php:1708
#: inc/Engine/Admin/Settings/Page.php:1716
msgid "Heartbeat"
msgstr "Heartbeat"

#: inc/Engine/Admin/Settings/Page.php:1709
msgid "Control WordPress Heartbeat API"
msgstr "Vezéreld a WordPress Heartbeat API-t"

#: inc/Engine/Admin/Settings/Page.php:1717
msgid "Reducing or disabling the Heartbeat API’s activity can help save some of your server’s resources."
msgstr "A Heartbeat API aktivitásának csökkentése vagy letiltása segíthet némiképp spórolni a szervered erőforrásaival."

#: inc/Engine/Admin/Settings/Page.php:1723
msgid "Reduce or disable Heartbeat activity"
msgstr "Csökkentsd vagy tiltsd le a Heartbeat működését"

#: inc/Engine/Admin/Settings/Page.php:1724
msgid "Reducing activity will change Heartbeat frequency from one hit each minute to one hit every 2 minutes."
msgstr "Az aktivitás csökkentése megváltoztatja a Heartbeat frekvenciáját percenkénti egy ütemről, 2 percenként egyre."

#: inc/Engine/Admin/Settings/Page.php:1724
msgid "Disabling Heartbeat entirely may break plugins and themes using this API."
msgstr "Ha teljesen letiltod a Heartbeat-et, akkor működésképtelenné válhat néhány bővítmény és téma am iezt az API-t használja."

#: inc/Engine/Admin/Settings/Page.php:1738
msgid "Do not limit"
msgstr "Ne legyen korlátozva"

#: inc/Engine/Admin/Settings/Page.php:1739
msgid "Reduce activity"
msgstr "Aktivitás csökkentése"

#: inc/Engine/Admin/Settings/Page.php:1740
msgid "Disable"
msgstr "Letiltás"

#: inc/Engine/Admin/Settings/Page.php:1748
msgid "Control Heartbeat"
msgstr "Heartbeat szabályzása"

#: inc/Engine/Admin/Settings/Page.php:1757
msgid "Behavior in backend"
msgstr "Viselkedés a backenden"

#: inc/Engine/Admin/Settings/Page.php:1764
msgid "Behavior in post editor"
msgstr "Viselkedés a bejegyzés-szerkesztőben"

#: inc/Engine/Admin/Settings/Page.php:1770
msgid "Behavior in frontend"
msgstr "Viselkedés a frontenden"

#: inc/Engine/Admin/Settings/Page.php:1787
#: views/settings/page-sections/tutorials.php:39
msgid "Add-ons"
msgstr "Bővítmények"

#: inc/Engine/Admin/Settings/Page.php:1788
msgid "Add more features"
msgstr "Több funkció hozzáadása"

#: inc/Engine/Admin/Settings/Page.php:1795
msgid "One-click Rocket Add-ons"
msgstr "Egy kattintásos Rocket bővítmények"

#: inc/Engine/Admin/Settings/Page.php:1796
msgid "One-Click Add-ons are features extending available options without configuration needed. Switch the option \"on\" to enable from this screen."
msgstr "Az egykattintásos bővítmények olyan funkciók amik kibővítik az elérhető lehetőségeidet úgy, hogy semmilyen konfigurációt nem igényelnek. Egyszerűen állítsd \"be\" állásra a kapcsolót a bekapcsoláshoz ezen a képernyőn."

#: inc/Engine/Admin/Settings/Page.php:1806
msgid "Rocket Add-ons"
msgstr "Rocket bővítmények"

#: inc/Engine/Admin/Settings/Page.php:1807
msgid "Rocket Add-ons are complementary features extending available options."
msgstr "A Rocket bővítmények ajándék funkciók, amik plusz funkciókat nyújtanak a Rocket szolgáltatásain felül."

#: inc/Engine/Admin/Settings/Page.php:1817
#: inc/Engine/Admin/Settings/Page.php:1986
msgid "Cloudflare"
msgstr "Cloudflare"

#: inc/Engine/Admin/Settings/Page.php:1823
msgid "Integrate your Cloudflare account with this add-on."
msgstr "Integráld a Cloudflare fiókodat ezen bővítménnyel."

#: inc/Engine/Admin/Settings/Page.php:1824
msgid "Provide your account email, global API key, and domain to use options such as clearing the Cloudflare cache and enabling optimal settings with WP Rocket."
msgstr "Add meg a fiókodhoz tartozó email címet, a global API kulcsot és a domaint olyan funkciók eléréséhez, mint pl. a Cloudflare gyosítótár ürítése és hogy optimálisra beállíthassuk az együttműködését a WP Rockettel."

#: inc/Engine/Admin/Settings/Page.php:1875
msgid "Varnish"
msgstr "Varnish"

#: inc/Engine/Admin/Settings/Page.php:1881
msgid "If Varnish runs on your server, you must activate this add-on."
msgstr "Ha a Varnish üzemel a szervereden akkor mindenképp engedélyezned kell ezt a bővítményt."

#: inc/Engine/Admin/Settings/Page.php:1883
#, php-format
msgid "Varnish cache will be purged each time WP Rocket clears its cache to ensure content is always up-to-date.<br>%1$sLearn more%2$s"
msgstr "A Varnish gyorsítótár minden alkalommal törölve lesz, amikor a WP Rocket üríti a saját gyorsítótárát, ezáltal biztosítva hogy mindig naprakész legyen a tartalom.<br>%1$sBővebb információ%2$s"

#: inc/Engine/Admin/Settings/Page.php:1918
msgid "WebP Compatibility"
msgstr "WebP kompatibilitás"

#: inc/Engine/Admin/Settings/Page.php:1924
msgid "Improve browser compatibility for WebP images."
msgstr "Javítja a böngészők kompatibitását a WebP bővítményekhez"

#: inc/Engine/Admin/Settings/Page.php:1928
#, php-format
msgid "Enable this option if you would like WP Rocket to serve WebP images to compatible browsers. Please note that WP Rocket cannot create WebP images for you. To create WebP images we recommend %1$sImagify%2$s. %3$sMore info%2$s"
msgstr "Engedélyezd ezt az opciót ha szeretnéd hogy a WP Rocket szolgálja fel a WebP képeket az ezzel kompatibilis böngészőknek. Ne felejtsd hogy a WP Rocket nem tud létrehozni WebP képeket Neked. WebP képek létrehozásához az  %1$sImagify%2$s-t ajánljuk. %3$sBővebb információ%2$s"

#: inc/Engine/Admin/Settings/Page.php:1948
msgid "Clear the Sucuri cache when WP Rocket’s cache is cleared."
msgstr "Ürítsd a Sucuri gyorsítótárt amikor a WP Rocket gyorsítótára ürítve van."

#: inc/Engine/Admin/Settings/Page.php:1951
msgid "Provide your API key to clear the Sucuri cache when WP Rocket’s cache is cleared."
msgstr "Add meg az API kulcsodat hogy üríteni tudjuk a Sucuri gyorsítótáradat is, amikor a WP Rocket gyorsítótára ürítve lesz."

#: inc/Engine/Admin/Settings/Page.php:1959
#: inc/Engine/Admin/Settings/Page.php:2103
msgid "Sucuri"
msgstr "Sucuri"

#: inc/Engine/Admin/Settings/Page.php:1965
msgid "Synchronize Sucuri cache with this add-on."
msgstr "Szinkronizáld a Sucuri gyorsítótáradat ezzel a bővítménnyel."

#: inc/Engine/Admin/Settings/Page.php:2003
msgid "Cloudflare credentials"
msgstr "Cloudflare belépési adatok"

#: inc/Engine/Admin/Settings/Page.php:2012
msgid "Cloudflare settings"
msgstr "Cloudflare beállítások"

#: inc/Engine/Admin/Settings/Page.php:2026
msgctxt "Cloudflare"
msgid "Global API key:"
msgstr "Globális API kulcs"

#: inc/Engine/Admin/Settings/Page.php:2027
msgctxt "Cloudflare"
msgid "Find your API key"
msgstr "Találd meg az API kulcsodat"

#: inc/Engine/Admin/Settings/Page.php:2039
msgctxt "Cloudflare"
msgid "Account email"
msgstr "A fiókhoz tartozó email"

#: inc/Engine/Admin/Settings/Page.php:2048
msgctxt "Cloudflare"
msgid "Zone ID"
msgstr "Zóna azonosító"

#: inc/Engine/Admin/Settings/Page.php:2058
msgid "Development mode"
msgstr "Fejlesztői üzemmód"

#: inc/Engine/Admin/Settings/Page.php:2060
#, php-format
msgid "Temporarily activate development mode on your website. This setting will automatically turn off after 3 hours. %1$sLearn more%2$s"
msgstr "Aktiválhatod ideiglenesen a fejlesztői módot a weboldaladon. Ez 3 óra múlva magától kikapcsol. %1$sBővebb információ%2$s"

#: inc/Engine/Admin/Settings/Page.php:2068
msgid "Optimal settings"
msgstr "Optimális beállítások"

#: inc/Engine/Admin/Settings/Page.php:2069
msgid "Automatically enhances your Cloudflare configuration for speed, performance grade and compatibility."
msgstr "Automatikusan javítja a Cloudflare beállításaidat a legjobb sebesség, teljesítmény, osztályzat és kompatibilitás érdekében."

#: inc/Engine/Admin/Settings/Page.php:2077
msgid "Relative protocol"
msgstr "Relatív protokoll"

#: inc/Engine/Admin/Settings/Page.php:2078
msgid "Should only be used with Cloudflare's flexible SSL feature. URLs of static files (CSS, JS, images) will be rewritten to use // instead of http:// or https://."
msgstr "Ezt csak a Cloudflare flexible SSL funkciójával szabad használni. A statikus fájlok URLjei (pl. CSS, JS, és képek) át lesznek írva úgy, hogy // t használjanak a http:// vagy https:// helyett."

#: inc/Engine/Admin/Settings/Page.php:2116
msgid "Sucuri credentials"
msgstr "Sucuri belépési adatok"

#: inc/Engine/Admin/Settings/Page.php:2130
msgctxt "Sucuri"
msgid "Find your API key"
msgstr "Találd meg az API kulcsodat"

#: inc/Engine/Admin/Settings/Render.php:422
#: inc/deprecated/deprecated.php:1294
msgid "Upload file and import settings"
msgstr "Fájl feltöltés és importálás beállítások"

#: inc/Engine/Admin/Settings/Settings.php:362
msgid "Sucuri Add-on: The API key for the Sucuri firewall must be in format <code>{32 characters}/{32 characters}</code>."
msgstr "Sucuri bővítmény: a Sucuri tűzfalhoz tartozó API kulcsnak az alábbi formátumban kell lennie: <code>{32 karakter}/{32 karakter}</code>"

#: inc/Engine/Admin/Settings/Settings.php:452
#: inc/deprecated/deprecated.php:1245
msgid "Settings saved."
msgstr "Beállítások elmentve."

#: inc/Engine/Admin/Settings/Settings.php:668
msgid "Sorry! Adding /(.*) in Advanced Rules > Never Cache URL(s) was not saved because it disables caching and optimizations for every page on your site."
msgstr "Elnézést, a /(.*) beállítást nem tudtuk elmenteni a haladó szabályok > Soha Ne Gyorsítótárazd Ezeket az URL-eket menüben mert ez letiltaná a gyorsítótárazást és az optimalizálást az egész weboldaladon."

#: inc/Engine/Admin/Settings/Subscriber.php:168
#: inc/deprecated/deprecated.php:1786
#: views/settings/page-sections/tools.php:33
msgid "Tools"
msgstr "Eszközök"

#: inc/Engine/Admin/Settings/Subscriber.php:169
msgid "Import, Export, Rollback"
msgstr "Import, Export, Visszaállítás"

#: inc/Engine/Admin/Settings/Subscriber.php:194
#: views/settings/page-sections/imagify.php:14
msgid "Image Optimization"
msgstr "Kép optimalizálás"

#: inc/Engine/Admin/Settings/Subscriber.php:195
msgid "Compress your images"
msgstr "Képek tömörítése"

#: inc/Engine/Admin/Settings/Subscriber.php:212
#: views/settings/page-sections/tutorials.php:48
msgid "Tutorials"
msgstr "Útmutatók"

#: inc/Engine/Admin/Settings/Subscriber.php:213
msgid "Getting started and how to videos"
msgstr "Tananyagok, segítő videók"

#: inc/Engine/CDN/RocketCDN/APIClient.php:134
msgid "We could not fetch the current price because RocketCDN API returned an unexpected error code."
msgstr "Nem tudtuk lekérdezni az aktuális árat mert a RocketCDN API váratlan hibakódot adott vissza."

#: inc/Engine/CDN/RocketCDN/APIClient.php:140
msgid "RocketCDN is not available at the moment. Please retry later."
msgstr "A RocketCDN nem elérhető jelenleg. Kérlek próbáld újra később."

#: inc/Engine/CDN/RocketCDN/APIClient.php:177
msgid "RocketCDN cache purge failed: Missing identifier parameter."
msgstr "RocketCDN gyorsítótár ürítés sikertelen: hiányzó azonosító paraméter."

#: inc/Engine/CDN/RocketCDN/APIClient.php:186
msgid "RocketCDN cache purge failed: Missing user token."
msgstr "RocketCDN gyorsítótár ürítés sikertelen: hiányzó felhasználói token."

#: inc/Engine/CDN/RocketCDN/APIClient.php:212
msgid "RocketCDN cache purge failed: The API returned an unexpected response code."
msgstr "RocketCDN gyorsítótár ürítés sikertelen: az API váratlan hibakódot adott vissza."

#: inc/Engine/CDN/RocketCDN/APIClient.php:221
msgid "RocketCDN cache purge failed: The API returned an empty response."
msgstr "RocketCDN gyorsítótár ürítés sikertelen: az API üres választ küldött."

#: inc/Engine/CDN/RocketCDN/APIClient.php:230
msgid "RocketCDN cache purge failed: The API returned an unexpected response."
msgstr "RocketCDN gyorsítótár ürítés sikertelen: az API váratlan választ küldött."

#: inc/Engine/CDN/RocketCDN/APIClient.php:239
#, php-format
msgid "RocketCDN cache purge failed: %s."
msgstr "RocketCDN gyorsítótár ürítés sikertelen: %s."

#: inc/Engine/CDN/RocketCDN/APIClient.php:247
msgid "RocketCDN cache purge successful."
msgstr "RocketCDN gyorsítótár ürítés sikeres volt."

#: inc/Engine/CDN/RocketCDN/AdminPageSubscriber.php:90
msgid "Next Billing Date"
msgstr "Következő számla dátuma:"

#: inc/Engine/CDN/RocketCDN/AdminPageSubscriber.php:99
msgid "No Subscription"
msgstr "Nincs előfizetésed"

#: inc/Engine/CDN/RocketCDN/AdminPageSubscriber.php:135
msgid "Your RocketCDN subscription is currently active."
msgstr "A RocketCDN előfizetésed aktív"

#: inc/Engine/CDN/RocketCDN/AdminPageSubscriber.php:141
#, php-format
msgid "To use RocketCDN, replace your CNAME with %1$s%2$s%3$s."
msgstr "A RocketCDN használatához kérlek cseréld le a CNAME-det erre: %1$s%2$s%3$s."

#: inc/Engine/CDN/RocketCDN/AdminPageSubscriber.php:152
#: inc/Engine/CDN/RocketCDN/NoticesSubscriber.php:230
#: inc/Engine/CDN/RocketCDN/NoticesSubscriber.php:334
#, php-format
msgid "%1$sMore Info%2$s"
msgstr "%1$sBővebb információ%2$s"

#: inc/Engine/CDN/RocketCDN/RESTSubscriber.php:137
msgid "RocketCDN enabled"
msgstr "RocketCDN engedélyezve"

#: inc/Engine/CDN/RocketCDN/RESTSubscriber.php:160
msgid "RocketCDN disabled"
msgstr "RocketCDN letiltva"

#: inc/Engine/CDN/RocketCDN/views/cta-big.php:27
#, php-format
msgid "Valid until %s only!"
msgstr "Csak %s -ig érvényes!"

#: inc/Engine/CDN/RocketCDN/views/cta-big.php:36
msgid "Speed up your website thanks to:"
msgstr "Gyorsítsd fel a weboldaladat köszönhetően: "

#: inc/Engine/CDN/RocketCDN/views/cta-big.php:48
#, php-format
msgid "Easy configuration: the %1$sbest CDN settings%2$s are automatically applied"
msgstr "Könnyű konfigurálás: a %1$slegjobb CDN beállítások%2$sautomatikusan alkalmazva lesznek"

#: inc/Engine/CDN/RocketCDN/views/cta-big.php:54
#, php-format
msgid "WP Rocket integration: the CDN option is %1$sautomatically configured%2$s in our plugin"
msgstr "WP Rocket integráció: a CDN opció %1$sautomatikusan be lesz állítva%2$s a bővítményünkben"

#: inc/Engine/CDN/RocketCDN/views/cta-big.php:58
msgid "Learn more about RocketCDN"
msgstr "Tudj meg többet a RocketCDN-ről"

#: inc/Engine/CDN/RocketCDN/views/cta-big.php:65
#, php-format
msgid "*$%1$s/month for 12 months then $%2$s/month. You can cancel your subscription at any time."
msgstr "*$%1$s/hónap 12 hónapon keresztül, ezután $%2$s/hónap. Bármikor lemondhatod az előfizetést."

#: inc/Engine/CDN/RocketCDN/views/cta-big.php:86
msgid "Billed monthly"
msgstr "Havonta számlázva"

#: inc/Engine/CDN/RocketCDN/views/cta-big.php:87
msgid "Get Started"
msgstr "Kezdjük el"

#: inc/Engine/CDN/RocketCDN/views/cta-big.php:92
msgid "Reduce this banner"
msgstr "Zárd be ezt a feliratot"

#: inc/Engine/CDN/RocketCDN/views/cta-small.php:17
msgid "Speed up your website with RocketCDN, WP Rocket’s Content Delivery Network."
msgstr "Gyorsítsd fel a weboldaladat a RocketCDN-nel, a WP Rocket Content Delivery Network-jével."

#: inc/Engine/CDN/RocketCDN/views/cta-small.php:20
#: inc/Engine/CDN/RocketCDN/views/promote-notice.php:13
msgid "Learn More"
msgstr "Tudj meg többet"

#: inc/Engine/CDN/RocketCDN/views/dashboard-status.php:23
msgid "RocketCDN is unavailable on local domains and staging sites."
msgstr "RocketCDN nem elérhető helyi domaineken és tesztoldalakon."

#: inc/Engine/CDN/RocketCDN/views/dashboard-status.php:32
msgid "Get RocketCDN"
msgstr "Fizess elő a RocketCDN-re"

#: inc/Engine/CDN/RocketCDN/views/promote-notice.php:11
msgid "New!"
msgstr "Újdonság!"

#: inc/Engine/CDN/RocketCDN/views/promote-notice.php:12
msgid "Speed up your website with RocketCDN, WP Rocket’s Content Delivery Network!"
msgstr "Gyorsítsd fel a weboldaladat a RocketCDN-nel, a WP Rocket Content Delivery Network-jével!"

#: inc/Engine/Cache/AdminSubscriber.php:118
#: inc/admin/admin.php:96
#: inc/admin/admin.php:118
#: inc/deprecated/3.5.php:898
msgid "Clear this cache"
msgstr "Ürítsd ezt a gyorsítótárat"

#: inc/Engine/Cache/PurgeExpired/Subscriber.php:75
msgid "WP Rocket Expired Cache Interval"
msgstr "WP Rocket gyorsítótár lejárat időköze"

#: inc/Engine/Cache/WPCache.php:337
msgid "WP_CACHE value"
msgstr "WP_CACHE értéke"

#: inc/Engine/Cache/WPCache.php:358
msgid "The WP_CACHE constant needs to be set to true for WP Rocket cache to work properly"
msgstr "A WP_CACHE konstanst true -ra kell állítani ahhoz hogy a WP ROCKET megfelelően működjön"

#: inc/Engine/Cache/WPCache.php:367
msgid "WP_CACHE is set to true"
msgstr "WP_CACHE igazra van állítva"

#: inc/Engine/Cache/WPCache.php:375
msgid "WP_CACHE is not set"
msgstr "WP_CACHE nincs beállítva"

#: inc/Engine/Cache/WPCache.php:383
msgid "WP_CACHE is set to false"
msgstr "WP_CACHE false -ra van állítva"

#: inc/Engine/Common/Queue/RUCSSQueueRunner.php:253
msgid "Every minute"
msgstr "Minden percben"

#: inc/Engine/CriticalPath/APIClient.php:64
#, php-format
msgid "Critical CSS for %1$s not generated. Error: %2$s"
msgstr "Critical CSS a %1$s számára nincs létrehozva. Hibakód: %2$s"

#: inc/Engine/CriticalPath/APIClient.php:170
#, php-format
msgid "Critical CSS for %1$s on mobile not generated. Error: The API returned an empty response."
msgstr "Critical CSS a %1$s számára mobilhoz nincs létrehozva. HIbakód: az API üres választ adott."

#: inc/Engine/CriticalPath/APIClient.php:173
#, php-format
msgid "Critical CSS for %1$s not generated. Error: The API returned an empty response."
msgstr "Critical CSS a %1$s számára nincs létrehozva. Hibakód: az API üres választ adott."

#: inc/Engine/CriticalPath/APIClient.php:185
#, php-format
msgid "Critical CSS for %1$s on mobile not generated."
msgstr "Critical CSS a %1$s számára mobilon nincs létrehozva."

#: inc/Engine/CriticalPath/APIClient.php:187
#: inc/Engine/CriticalPath/ProcessorService.php:194
#, php-format
msgid "Critical CSS for %1$s not generated."
msgstr "Critical CSS a %1$s számára nincs létrehozva."

#: inc/Engine/CriticalPath/APIClient.php:195
#, php-format
msgid "Critical CSS for %1$s on mobile not generated. Error: The API returned an invalid response code."
msgstr "Critical CSS a %1$s  számára mobilon nincs létrehozva. Hibakód: az API érvénytelen választ adott."

#: inc/Engine/CriticalPath/APIClient.php:197
#, php-format
msgid "Critical CSS for %1$s not generated. Error: The API returned an invalid response code."
msgstr "Critical CSS a %1$s számára nincs létrehozva. Hibakód: az API érvénytelen választ adott."

#: inc/Engine/CriticalPath/APIClient.php:205
#: inc/Engine/CriticalPath/ProcessorService.php:201
#, php-format
msgid "Error: %1$s"
msgstr "Hiba: %1$s"

#: inc/Engine/CriticalPath/Admin/Admin.php:264
msgid "Regenerate Critical Path CSS"
msgstr "Kritikus útvonalak CSS újragenerálása"

#: inc/Engine/CriticalPath/Admin/Post.php:144
#: views/cpcss/metabox/generate.php:47
msgid "Generate Specific CPCSS"
msgstr "Generálj bizonyos CPCSS-eket"

#: inc/Engine/CriticalPath/Admin/Post.php:145
#: views/cpcss/metabox/generate.php:45
msgid "Regenerate specific CPCSS"
msgstr "Generáld újra bizonyos CPCSS-eket"

#: inc/Engine/CriticalPath/Admin/Post.php:216
msgid "This feature is not available for non-public post types."
msgstr "Ez a funkció nem elérhető a nem publikus bejegyzés típusoknál."

#: inc/Engine/CriticalPath/Admin/Post.php:219
msgid "%l to use this feature."
msgstr "%l a funkció használatához."

#: inc/Engine/CriticalPath/Admin/Post.php:222
#, php-format
msgid "Publish the %s"
msgstr "Publikáld a %s-t"

#: inc/Engine/CriticalPath/Admin/Post.php:223
msgid "Enable Load CSS asynchronously in WP Rocket settings"
msgstr "Engedélyezd a CSS aszinkron betöltését a WP Rocket beállításaiban"

#: inc/Engine/CriticalPath/Admin/Post.php:224
msgid "Enable Load CSS asynchronously in the options above"
msgstr "Engedélyezd a CSS aszinkron betöltését a fenti opciókban"

#: inc/Engine/CriticalPath/CriticalCSSSubscriber.php:176
msgid "Critical CSS generation is currently running."
msgstr "A kritikus CSS generálása épp zajlik."

#: inc/Engine/CriticalPath/CriticalCSSSubscriber.php:181
#, php-format
msgid "Go to the %1$sWP Rocket settings%2$s page to track progress."
msgstr "Menj a %1$sWP Rocket beállításai%2$s oldalra hogy kövesd a folyamatot."

#: inc/Engine/CriticalPath/CriticalCSSSubscriber.php:396
#, php-format
msgid "Critical CSS generation is currently running: %1$d of %2$d page types completed. (Refresh this page to view progress)"
msgstr "Kritikus CSS generálása jelenleg zajlik: %1$d/%2$d oldaltípusok kész. (Frissítsd ezt az oldalt hogy kövesd a folyamatot)"

#: inc/Engine/CriticalPath/CriticalCSSSubscriber.php:472
#, php-format
msgid "Critical CSS generation finished for %1$d of %2$d page types."
msgstr "A kritikus CSS generálása kész az %1$d/%2$d oldaltípusokhoz."

#: inc/Engine/CriticalPath/CriticalCSSSubscriber.php:479
msgid "Critical CSS generation encountered one or more errors."
msgstr "A kritikus CSS generálása egy vagy több hibát talált."

#: inc/Engine/CriticalPath/CriticalCSSSubscriber.php:479
msgid "Learn more."
msgstr "Tudj meg többet."

#: inc/Engine/CriticalPath/DataManager.php:68
#, php-format
msgid "Critical CSS for %1$s on mobile not generated. Error: The destination folder could not be created."
msgstr "Kritikus CSS a %1$s számára mobilon nem lett létrehozva. Hibakód: a célmappa nem volt létrehozható."

#: inc/Engine/CriticalPath/DataManager.php:71
#, php-format
msgid "Critical CSS for %1$s not generated. Error: The destination folder could not be created."
msgstr "Kritikus CSS a %1$s számára nem lett létrehozva. Hibakód: a célmappa nem volt létrehozható."

#: inc/Engine/CriticalPath/DataManager.php:106
msgid "Critical CSS file for mobile does not exist"
msgstr "Kritikus CSS fájl mobil számára nem létezik."

#: inc/Engine/CriticalPath/DataManager.php:108
msgid "Critical CSS file does not exist"
msgstr "A kritikus CSS fájl nem létezik"

#: inc/Engine/CriticalPath/DataManager.php:120
msgid "Critical CSS file for mobile cannot be deleted"
msgstr "Kritikus CSS fájl mobilon nem törölhető"

#: inc/Engine/CriticalPath/DataManager.php:122
msgid "Critical CSS file cannot be deleted"
msgstr "Kritikus CSS fájl nem törölhető"

#: inc/Engine/CriticalPath/ProcessorService.php:187
#, php-format
msgid "Mobile Critical CSS for %1$s not generated."
msgstr "Mobile Critical CSS a %1$s számára nem lett létrehozva. "

#: inc/Engine/CriticalPath/ProcessorService.php:228
#, php-format
msgid "Critical CSS for %s in progress."
msgstr "Critical CSS generálása %s számára folyamatban. "

#: inc/Engine/CriticalPath/ProcessorService.php:262
#, php-format
msgid "Mobile Critical CSS for %s generated."
msgstr "Mobil kritikus CSS %s számára létrehozva. "

#: inc/Engine/CriticalPath/ProcessorService.php:273
#, php-format
msgid "Critical CSS for %s generated."
msgstr "Kritikus CSS %s számára létrehozva."

#: inc/Engine/CriticalPath/ProcessorService.php:295
msgid "Critical CSS file deleted successfully."
msgstr "Kritikus CSS fájl sikeresen törölve."

#: inc/Engine/CriticalPath/ProcessorService.php:317
#, php-format
msgid "Mobile Critical CSS for %1$s timeout. Please retry a little later."
msgstr "Mobil kritikus CSS a %1$s számára: időtúllépés történt. Kérlek kicsit később próbáld újra."

#: inc/Engine/CriticalPath/ProcessorService.php:330
#, php-format
msgid "Critical CSS for %1$s timeout. Please retry a little later."
msgstr "Kritikus CSS a %1$s számára: időtúllépés történt. Kérlek kicsit később próbáld újra."

#: inc/Engine/CriticalPath/RESTWP.php:141
msgid "Mobile CPCSS generation not enabled."
msgstr "Mobil CPCSS generálás nincs engedélyezve."

#: inc/Engine/CriticalPath/RESTWPPost.php:36
#: inc/Engine/CriticalPath/RESTWPPost.php:69
msgid "Requested post does not exist."
msgstr "A kért bejegyzés nem létezik."

#: inc/Engine/CriticalPath/RESTWPPost.php:46
msgid "Cannot generate CPCSS for unpublished post."
msgstr "Nem lehet CPCSS -t generálni a nem publikált bejegyzésekhez."

#: inc/Engine/HealthCheck/HealthCheck.php:82
#: inc/deprecated/3.5.php:858
msgid "The following scheduled event failed to run. This may indicate the CRON system is not running properly, which can prevent some WP Rocket features from working as intended:"
msgid_plural "The following scheduled events failed to run. This may indicate the CRON system is not running properly, which can prevent some WP Rocket features from working as intended:"
msgstr[0] "Az alábbi ütemezett esemény nem tudott elindulni. Ez CRON rendszerhibára utal, ami megakadályozza a WP Rocket bizonyos funkcióinak megfelelő működését:"
msgstr[1] "Az alábbi ütemezett események nem tudtak elindulni. Ez CRON rendszerhibára utal, ami megakadályozza a WP Rocket bizonyos funkcióinak megfelelő működését:"

#: inc/Engine/HealthCheck/HealthCheck.php:88
#: inc/deprecated/3.5.php:867
msgid "Please contact your host to check if CRON is working."
msgstr "Kérlek vedd fel a kapcsolatot a tárhelyszolgáltatóddal, hogy kiderítsék, megfelelően működik-e a CRON."

#: inc/Engine/HealthCheck/HealthCheck.php:142
msgid "Scheduled Cache Purge"
msgstr "Ütemezett gyorsítótár ürítés"

#: inc/Engine/HealthCheck/HealthCheck.php:143
msgid "Scheduled Database Optimization"
msgstr "Ütemezett adatbázis optimizálás"

#: inc/Engine/HealthCheck/HealthCheck.php:144
msgid "Database Optimization Process"
msgstr "Adatbázis optimizálás állapota"

#: inc/Engine/HealthCheck/HealthCheck.php:145
msgctxt "noun"
msgid "Preload"
msgstr "Előtöltés"

#: inc/Engine/HealthCheck/HealthCheck.php:146
msgid "Critical Path CSS Generation Process"
msgstr "Kritikus útvonalú CSS generálásának állapota"

#: inc/Engine/License/Renewal.php:546
#, php-format
msgid "You need a valid license to continue using this feature. %1$sRenew now%2$s before losing access."
msgstr "Érvényes licenszre van szükséged hogy tovább használhasd ezt a funckiót. %1$sÚjítsd meg most%2$s hogy ne veszítsd el a hozzáférést!"

#: inc/Engine/License/Renewal.php:567
#, php-format
msgid "You need an active license to enable this option. %1$sRenew now%2$s."
msgstr "Érvényes licenszre van szükséged ezen funkció engedélyezéséhez.%1$sÚjítsd meg most%2$s"

#: inc/Engine/License/Renewal.php:595
#, php-format
msgid "You need an active license to enable this option. %1$sMore info%2$s."
msgstr "Érvényes licenszre van szükséged ezen funkció engedélyezéséhez. %1$sBővebb információ%2$s"

#: inc/Engine/License/Upgrade.php:252
#, php-format
msgid "Take advantage of %1$s to speed up more websites:%2$s get a %3$s%4$s off%5$s for %3$supgrading your license to Plus or Infinite!%5$s"
msgid_plural "Take advantage of %1$s to speed up more websites:%2$s get a %3$s%4$s off%5$s for %3$supgrading your license to Infinite!%5$s"
msgstr[0] "Használd ki a%1$s-t hogy mégtöbb weboldalt felgyorsíthass: %2$s szerezz egy %3$s%4$s kedvezményt %5$s ig%3$s, válts Plus előfizetésre! %5$s"
msgstr[1] "Használd ki a %1$s -t hogy mégtöbb weboldalt felgyorsíthass: %2$sszerezz %3$s%4$s kedvezményt %5$s ig%3$s, válts Infinite előfizetésre! %5$s"

#: inc/Engine/License/Upgrade.php:382
#: inc/Engine/License/Upgrade.php:405
msgid "Unlimited"
msgstr "Korlátlan"

#: inc/Engine/License/views/promo-banner.php:16
#, php-format
msgid "%s off"
msgstr "%skedvezmény"

#: inc/Engine/License/views/promo-banner.php:21
#, php-format
msgid "%s promotion is live!"
msgstr "%s akció!"

#: inc/Engine/License/views/promo-banner.php:27
msgid "Hurry Up! Deal ends in:"
msgstr "Siess! Az ajánlat csak eddig tart:"

#: inc/Engine/License/views/promo-banner.php:31
#: inc/Engine/License/views/renewal-soon-banner.php:14
msgid "Minutes"
msgstr "Perc"

#: inc/Engine/License/views/promo-banner.php:32
#: inc/Engine/License/views/renewal-soon-banner.php:15
msgid "Seconds"
msgstr "Másodperc"

#: inc/Engine/License/views/promo-banner.php:34
#: inc/Engine/License/views/upgrade-section.php:11
msgid "Upgrade now"
msgstr "Válts most"

#: inc/Engine/License/views/promo-banner.php:36
#: inc/Engine/License/views/renewal-expired-banner-ocd-disabled.php:32
#: inc/Engine/License/views/renewal-expired-banner-ocd.php:42
#: inc/Engine/License/views/renewal-expired-banner.php:30
#: inc/admin/ui/notices.php:739
#: views/settings/page-sections/dashboard.php:46
msgid "Dismiss this notice"
msgstr "Tüntesd el ezt az értesítést"

#: inc/Engine/License/views/renewal-expired-banner-ocd-disabled.php:11
msgid "The Optimize CSS Delivery feature is disabled."
msgstr "Az optimizált CSS kiküldés funkció le van tiltva."

#: inc/Engine/License/views/renewal-expired-banner-ocd-disabled.php:15
msgid "You can no longer use the Remove Unused CSS or Load CSS asynchronously options."
msgstr "Nem használhatod többé a Nem-használt CSS eltávolítása vagy a CSS aszinkron betöltése opciókat."

#: inc/Engine/License/views/renewal-expired-banner-ocd-disabled.php:20
#, php-format
msgid "You need an %1$sactive license%2$s to keep optimizing your CSS delivery, which addresses a PageSpeed Insights recommendation and improves your page performance."
msgstr "%1$sÉrvényes licenszre%2$s van szükség a CSS kézbesítés optimizáláshoz, ami javítja a PageSpeed Insight ajánlásokat és javítja az oldalad teljesítményét."

#: inc/Engine/License/views/renewal-expired-banner-ocd-disabled.php:29
#: inc/Engine/License/views/renewal-expired-banner-ocd.php:39
#: inc/Engine/License/views/renewal-expired-banner.php:27
#: inc/Engine/License/views/renewal-soon-banner.php:31
msgid "Renew now"
msgstr "Újítsd meg most"

#: inc/Engine/License/views/renewal-expired-banner-ocd.php:11
msgid "You will soon lose access to some features"
msgstr "Hamarosan elveszted az alábbi funkciókat"

#: inc/Engine/License/views/renewal-expired-banner-ocd.php:18
#, php-format
msgid "You need an %1$sactive license to continue optimizing your CSS delivery%2$s."
msgstr "Szükség van %1$sérvényes licenszre hogy ezentúl is optimizálva legyen kézbesítve a CSS-ed%2$s."

#: inc/Engine/License/views/renewal-expired-banner-ocd.php:24
msgid "The Remove Unused CSS and Load CSS asynchronously features are great options to address the PageSpeed Insights recommendations and improve your website performance."
msgstr "A nem-használt CSS és az aszinkron CSS betöltés funkciók nagyszerű lehetőségek a PageSpeed Insight ajánlások javítására, valamint javítják a weboldalad teljesítményét."

#: inc/Engine/License/views/renewal-expired-banner-ocd.php:29
#, php-format
msgid "They will be %1$sautomatically disabled on %3$s%2$s."
msgstr "Ezek %1$sautomatikusan le lesnek tiltva %3$s%2$s-kor."

#: inc/Engine/License/views/renewal-expired-banner.php:11
msgid "Your WP Rocket license is expired!"
msgstr "A WP Rocket licenszed lejárt!"

#: inc/Engine/License/views/renewal-soon-banner.php:22
#, php-format
msgid "Your %1$sWP Rocket license is about to expire%2$s: you will soon lose access to product updates and support."
msgstr "A %1$sWP Rocket licenszed hamarosan lejár%2$s: hamarosan elveszted a hozzáférést a frissítésekhez és a támogatáshoz."

#: inc/Engine/License/Renewal.php:227
#, php-format
msgid "Renew with a %1$s%2$s discount%3$s before it is too late, you will only pay %1$s%4$s%3$s!"
msgstr "Újítsd meg most egy %1$s%2$skedvezménnyel%3$s mielőtt túl késő, csak %1$s%4$s%3$s-t kellene fizetned!"

#: inc/Engine/License/views/upgrade-popin.php:12
msgid "Speed Up More Websites"
msgstr "Gyorsíts fel mégtöbb weboldalt!"

#: inc/Engine/License/views/upgrade-popin.php:19
#, php-format
msgid "You can use WP Rocket on more websites by upgrading your license. To upgrade, simply pay the %1$sprice difference%2$s between your current and new licenses, as shown below."
msgstr "A WP Rocket-et több weboldalon is használhatod ha bővíted a licenszedet. Ehhez csak fizess %1$sárkülönbözetet%2$s a mostani és az új licenszed között, ahogy lejjebb látható:"

#: inc/Engine/License/views/upgrade-popin.php:25
#, php-format
msgid "%1$sN.B.%2$s: Upgrading your license does not change your expiration date"
msgstr "%1$sTipp:%2$s: A licenszed megújítása nem hosszabbítja meg a lejárati dátumot"

#: inc/Engine/License/views/upgrade-popin.php:35
#, php-format
msgid "Save $%s"
msgstr "Spórolj $%s-t"

#: inc/Engine/License/views/upgrade-popin.php:48
#, php-format
msgid "%s websites"
msgstr "%s weboldalak"

#: inc/Engine/License/views/upgrade-popin.php:54
#, php-format
msgid "Upgrade to %s"
msgstr "Válts %s-ra"

#: inc/Engine/License/views/upgrade-section.php:11
msgid "You can use WP Rocket on more websites by upgrading your license (you will only pay the price difference between your current and new licenses)."
msgstr "A WP Rocket-et több weboldalon is használhatod ha bővíted a licenszedet. (Csak az árkülönbözetet kell fizetned a mostani és az új licenszed között)"

#: inc/Engine/Optimization/RUCSS/Admin/Settings.php:131
#: views/settings/page-sections/dashboard.php:188
msgid "Clear Used CSS"
msgstr "Ürítsd a használt CSS-ek gyorsítótárát"

#: inc/Engine/Optimization/RUCSS/Admin/Settings.php:219
#, php-format
msgid "%1$s: Please wait %2$s seconds. The Remove Unused CSS service is processing your pages."
msgstr "%1$s: Kérlek várj %2$s másodpercet. A Nemhasznált CSS törlése szolgáltatás épp feldolgozza az oldalaidat."

#: inc/Engine/Optimization/RUCSS/Admin/Settings.php:264
#, php-format
msgid "%1$s: The Used CSS of your homepage has been processed. WP Rocket will continue to generate Used CSS for up to %2$s URLs per %3$s second(s)."
msgstr "%1$s: A kezdőlapodon felhasznált CSS-ek fel lettek dolgozva. A WP Rocket folytatja a felhasznált CSS-ek feldolgozását, %2$s URL/%3$smásodpercenként."

#: inc/Engine/Optimization/RUCSS/Admin/Settings.php:283
#, php-format
msgid "To learn more about the process check our %1$sdocumentation%2$s."
msgstr "Hogy megtudhass többet a folyamatról, nézd meg a %1$sdokumentációnkat%2$s."

#: inc/Engine/Optimization/RUCSS/Admin/Settings.php:481
msgid "We couldn't generate the used CSS because you're using a nulled version of WP Rocket. You need an active license to use the Remove Unused CSS feature and further improve your website's performance."
msgstr "Nem tudtuk létrehozni a felhasznált CSS-ek listáját mert kalózverziót használsz a WP Rocket-ből. Aktív előfizetésre van szükség ahhoz, hogy a nemhasznált CSS-ek törlése funkciót használhasd, valamint hogy továbbfejleszthesd a weboldalad teljesítményét."

#: inc/Engine/Optimization/RUCSS/Admin/Settings.php:484
#, php-format
msgid "Click here to get a WP Rocket single license at %1$s off!"
msgstr "Kattints ide és megvásárolhatod a WP Rocket single licenszét %1$skedvezménnyel!"

#: inc/Engine/Optimization/RUCSS/Admin/Subscriber.php:322
#, php-format
msgid "%1$s: Used CSS option is not enabled!"
msgstr "%1$sA felhasznált CSS opció nincs engedélyezve!"

#: inc/Engine/Optimization/RUCSS/Admin/Subscriber.php:343
#, php-format
msgid "%1$s: Used CSS cache cleared!"
msgstr "%1$s: A felhasznált CSS gyorsítótára ürítve!"

#: inc/Engine/Optimization/RUCSS/Controller/UsedCSS.php:776
msgid "Clear Used CSS of this URL"
msgstr "Ürítsd ezen URL felhasznált CSS-eit"

#: inc/Engine/Optimization/RUCSS/Cron/Subscriber.php:160
msgid "WP Rocket Remove Unused CSS pending jobs"
msgstr "WP Rocket Nem-használt CSS eltávolítása sorban álló feladatok"

#: inc/Engine/Plugin/UpdaterApiTools.php:32
#: inc/deprecated/3.3.php:761
#: inc/deprecated/3.3.php:770
#, php-format
msgid "An unexpected error occurred. Something may be wrong with WP-Rocket.me or this server&#8217;s configuration. If you continue to have problems, <a href=\"%s\">contact support</a>."
msgstr "Váratlan hiba történt. Valami hiba lehet a WP-Rocket.me -vel vagy ezen szerver konfigurációjával. Ha továbbra is problémákat tapasztalsz, <a href=\"%s\">vedd fel a támogatásunkkal a kapcsolatot</a>."

#: inc/Engine/Plugin/UpdaterSubscriber.php:472
#: inc/Engine/Plugin/UpdaterSubscriber.php:486
#: inc/classes/class-wp-rocket-requirements-check.php:203
#: inc/classes/class-wp-rocket-requirements-check.php:214
#: inc/deprecated/3.11.php:236
#: inc/deprecated/3.11.php:251
#, php-format
msgid "%s Update Rollback"
msgstr "%s Régi verzióra visszaállítás"

#: inc/Engine/Plugin/UpdaterSubscriber.php:509
#: inc/deprecated/3.11.php:279
#, php-format
msgid "%1$sReturn to WP Rocket%2$s or %3$sgo to Plugins page%2$s"
msgstr "%1$sVálts vissza a WP Rocket %2$s vagy %3$s lépj a Bővítmények oldalra%2$s"

#: inc/ThirdParty/Hostings/Cloudways.php:82
#, php-format
msgid "Varnish auto-purge will be automatically enabled once Varnish is enabled on your %s server."
msgstr "Varnish automata-ürítés magától engedélyezve lesz, amint engedélyezed a Varnish-t a %sszerveren."

#: inc/ThirdParty/Hostings/Kinsta.php:158
#, php-format
msgid "Your installation seems to be missing core Kinsta files managing Cache clearing, which will prevent your Kinsta installation and WP Rocket from working correctly. Please get in touch with Kinsta support through your %1$sMyKinsta%2$s account to resolve this issue."
msgstr "Úgy tűnik, fontos Kinsta fájlok hiányoznak a rendszeredből, amik a gyorsítótár ürítéséért felelnének, ezért a Kinsta és a WP Rocket nem tud együttműködni. Kérlek vedd fel a kapcsolatot a Kinsta támogatással a %1$sMyKinsta%2$sfiókodon keresztül hogy megoldd ezt a problémát. "

#: inc/ThirdParty/Plugins/ModPagespeed.php:102
#, php-format
msgid "<strong>%1$s</strong>: Mod PageSpeed is not compatible with this plugin and may cause unexpected results. %2$sMore Info%3$s"
msgstr "<strong>%1$s</strong>: Mod PageSpeed nem kompatibilis ezzel a bővítménnyel és váratlan problémákat okozhat. %2$sBővebb információ%3$s"

#: inc/ThirdParty/Plugins/Optimization/Autoptimize.php:76
#, php-format
msgid "%1$sWP Rocket: %2$sWe have detected that Autoptimize's JavaScript Aggregation feature is enabled. WP Rocket's Delay JavaScript Execution will not be applied to the file it creates. We suggest disabling %1$sJavaScript Aggregation%2$s to take full advantage of Delay JavaScript Execution."
msgstr "%1$sWP Rocket:%2$s Úgy érzékeljük hogy engedélyezve van az Autoptmize JavaScript Aggregáció funkciója. A WP Rocket Késleltetett JavaScript Futtatás funkciója nem lesz alkalmazva arra a fájlra, amit ez létrehoz. Javasoljuk hogy tiltsd le a %1$sJavascript aggregációt%2$s hogy kihasználhasd a mi Késleltetett Javascript Futtatás funkciónkat."

#: inc/ThirdParty/Plugins/Optimization/Autoptimize.php:131
#, php-format
msgid "%1$sWP Rocket: %2$sWe have detected that Autoptimize's Aggregate Inline CSS feature is enabled. WP Rocket's Load CSS Asynchronously will not work correctly. We suggest disabling %1$sAggregate Inline CSS%2$s to take full advantage of Load CSS Asynchronously Execution."
msgstr "%1$sWP Rocket: %2$sÚgy érzékeljük hogy  engedélyezve van az Autoptimize Beágyazott CSS Aggregáció funkciója. A WP Rocket CSS aszinkron betöltése nem fog megfelelően működni így. Javasoljuk hogy tiltsd le a %1$sBeágyazott CSS Aggregáció%2$s funkcióját hogy kihasználhasd a mi CSS aszinkron betöltés funkciónkat."

#: inc/ThirdParty/Plugins/Optimization/Ezoic.php:45
#, php-format
msgid "This plugin blocks WP Rocket's caching and optimizations. Deactivate it and use %1$sEzoic's nameserver integration%2$s instead."
msgstr "Ez a bővítmény blokkolja a WP Rocket gyorsítótárazását és egyéb optimalizációit. Deaktiváld és használd az %1$sEzoic névszerver integrációt%2$shelyette."

#: inc/ThirdParty/Plugins/Optimization/Hummingbird.php:78
#, php-format
msgctxt "Hummingbird notice"
msgid "Please deactivate the following %s option which conflicts with WP Rocket features:"
msgid_plural "Please deactivate the following %s options which conflict with WP Rocket features:"
msgstr[0] "Kérlek deaktiváld az alábbi %sopciót amik összeférhetetlenek a WP Rocket funkcióival:"
msgstr[1] "Kérlek deaktiváld az alábbi %s opciókat amik összeférhetetlenek a WP Rocket funkcióival:"

#: inc/ThirdParty/Plugins/Optimization/Hummingbird.php:151
#, php-format
msgctxt "Hummingbird notice"
msgid "%1$s %2$sdisable emoji%3$s conflicts with WP Rockets %2$sdisable emoji%3$s"
msgstr "%1$s %2$sdisable emoji%3$s összeférhetetlen a  WP Rocket %2$sdisable emoji%3$s funkciójával"

#: inc/ThirdParty/Plugins/Optimization/Hummingbird.php:187
#, php-format
msgctxt "Hummingbird notice"
msgid "%1$s %2$sGZIP compression%3$s conflicts with WP Rocket %2$sGZIP compression%3$s"
msgstr "%1$s %2$sGZIP compression%3$s összeférhetetlen a WP Rocket %2$sGZIP compression%3$s-ével"

#: inc/ThirdParty/Plugins/Optimization/Hummingbird.php:223
#, php-format
msgctxt "Hummingbird notice"
msgid "%1$s %2$sbrowser caching%3$s conflicts with WP Rocket %2$sbrowser caching%3$s"
msgstr "%1$s %2$sbrowser caching%3$s összeférhetetlen a WP Rocket %2$sbrowser caching%3$s-ével"

#: inc/ThirdParty/Plugins/Optimization/Hummingbird.php:255
#, php-format
msgctxt "Hummingbird notice"
msgid "%1$s %2$spage caching%3$s conflicts with WP Rocket %2$spage caching%3$s"
msgstr "%1$s %2$spage caching%3$s összeférhetetlen a WP Rocket %2$spage caching%3$s-ével"

#: inc/ThirdParty/Plugins/Optimization/Hummingbird.php:289
#, php-format
msgctxt "Hummingbird notice"
msgid "%1$s %2$sasset optimization%3$s conflicts with WP Rocket %2$sfile optimization%3$s"
msgstr "%1$s %2$sasset optimization%3$s összeférhetetlen a WP Rocket %2$sfile optimization%3$s-ével"

#: inc/ThirdParty/Plugins/Optimization/WPMeteor.php:42
#, php-format
msgid "Delay JS is currently activated in %1$s. If you want to use WP Rocket’s delay JS, disable %1$s"
msgstr "A késleltetett JS engedélyezve van a %1$s-ban. Ha a WP Rocket késleltetett JS-ét akarod használni akkor tiltsd le %1$s-ban."

#: inc/deprecated/3.11.php:81
msgid "Yoast SEO XML sitemap"
msgstr "Yoast SEO XML oldaltérkép"

#: inc/ThirdParty/Plugins/Smush.php:108
#: inc/ThirdParty/Plugins/Smush.php:126
msgid "Smush"
msgstr "Smush"

#: inc/ThirdParty/Themes/Avada.php:116
msgid "Avada"
msgstr "Avada"

#: inc/admin/admin.php:18
#: inc/common/admin-bar.php:255
#: inc/deprecated/deprecated.php:1787
msgid "Support"
msgstr "Támogatás"

#: inc/admin/admin.php:20
msgid "Docs"
msgstr "Dokumentáció"

#: inc/admin/admin.php:22
#: inc/common/admin-bar.php:243
msgid "FAQ"
msgstr "GYIK"

#: inc/admin/admin.php:24
#: inc/common/admin-bar.php:63
msgid "Settings"
msgstr "Beállítások"

#: inc/admin/admin.php:458
msgid "Settings import failed: you do not have the permissions to do this."
msgstr "Beállítások importálása sikertelen: nincs jogosultságod ehhez."

#: inc/admin/admin.php:462
msgid "Settings import failed: no file uploaded."
msgstr "Beállítások importálása sikertelen: nem lett fájl feltöltve."

#: inc/admin/admin.php:466
msgid "Settings import failed: incorrect filename."
msgstr "Beállítások importálása sikertelen: hibás fájlnév."

#: inc/admin/admin.php:477
msgid "Settings import failed: incorrect filetype."
msgstr "Beállítások importálása sikertelen: hibás fájltípus."

#: inc/admin/admin.php:487
msgid "Settings import failed: "
msgstr "Beállítások importálása sikertelen: "

#: inc/admin/admin.php:503
msgid "Settings import failed: unexpected file content."
msgstr "Beállítások importálása sikertelen: váratlan fájl tartalom."

#: inc/admin/admin.php:533
msgid "Settings imported and saved."
msgstr "Beállítások importálva és elmentve."

#: inc/admin/options.php:127
msgid "Defer JavaScript Files"
msgstr "Késleltetett javascript fájlok (Defer)"

#: inc/admin/options.php:128
msgid "Excluded Delay JavaScript Files"
msgstr "Késleltetésből kizárt JavaScript fájlok"

#: inc/admin/options.php:150
#, php-format
msgid "%1$s: <em>%2$s</em>"
msgstr "%1$s: <em>%2$s</em>"

#: inc/admin/options.php:160
msgid "The following pattern is invalid and has been removed:"
msgid_plural "The following patterns are invalid and have been removed:"
msgstr[0] "Az alábbi minta érvénytelen, és ezért törölve lett:"
msgstr[1] "Az alábbi minták érvénytelenek, és ezért törölve lettek:"

#: inc/admin/options.php:176
msgid "More info"
msgstr "Bővebb információ"

#: inc/admin/ui/meta-boxes.php:37
#: inc/admin/ui/notices.php:748
#: inc/common/admin-bar.php:84
#: inc/common/admin-bar.php:136
#: views/settings/page-sections/dashboard.php:150
msgid "Clear cache"
msgstr "Gyorsítótár ürítése"

#: inc/admin/ui/meta-boxes.php:67
msgid "WP Rocket Options"
msgstr "WP Rocket beállításai"

#: inc/admin/ui/meta-boxes.php:96
msgid "Never cache this page"
msgstr "Soha ne gyorsítótárazd ezt az oldalt"

#: inc/admin/ui/meta-boxes.php:100
msgid "Activate these options on this post:"
msgstr "Aktiváld ezeket az opciókat ezen a bejegyzésen:"

#: inc/admin/ui/meta-boxes.php:103
msgid "LazyLoad for images"
msgstr "LazyLoad képek számára"

#: inc/admin/ui/meta-boxes.php:104
msgid "LazyLoad for iframes/videos"
msgstr "LazyLoad iframe-k/videók számára"

#: inc/admin/ui/meta-boxes.php:105
msgid "Minify/combine CSS"
msgstr "CSS miniatűrizálása/egyesítése"

#: inc/Engine/Admin/Settings/Page.php:760
#: inc/admin/ui/meta-boxes.php:106
msgid "Remove Unused CSS"
msgstr "Nem-használt CSS kódok eltávolítása"

#: inc/admin/ui/meta-boxes.php:107
msgid "Minify/combine JS"
msgstr "JS miniatűrizálása/egyesítése"

#: inc/admin/ui/meta-boxes.php:110
msgid "Defer JS"
msgstr "JS késleltetése (defer)"

#: inc/admin/ui/meta-boxes.php:117
#, php-format
msgid "Activate first the %s option."
msgstr "Aktiváld először a %sopciót"

#: inc/admin/ui/meta-boxes.php:133
#, php-format
msgid "%1$sNote:%2$s None of these options will be applied if this post has been excluded from cache in the global cache settings."
msgstr "%1$sMegjegyzés:%2$s Ezek az opciók nem lesznek érvénybe léptetve, ha ez a bejegyzés ki lett zárva a gyorsítótárazásból a globális gyorsítótár beállításokban."

#: inc/admin/ui/notices.php:31
#: inc/admin/ui/notices.php:44
#, php-format
msgid ""
"<strong>%1$s</strong> has not been deactivated due to missing writing permissions.<br>\n"
"Make <strong>%2$s</strong> writeable and retry deactivation, or force deactivation now:"
msgstr ""
"<strong>%1$s</strong>nem lett deaktiválva a hiányzó írási jogosultságok miatt.<br>\n"
"Tedd <strong>%2$s</strong>-t írhatóvá és próbáld újra a deaktiválásd, vagy kényszerítsd a deaktiválást most:"

#: inc/admin/ui/notices.php:97
#, php-format
msgid "<strong>%s</strong>: One or more plugins have been enabled or disabled, clear the cache if they affect the front end of your site."
msgstr "<strong>%s</strong>: egy vagy több bővítmény engedélyezve lett vagy letiltva, ürítsd a gyorsítótárt ha ezek befolyásolják a weboldalad megjelenését."

#: inc/admin/ui/notices.php:218
#, php-format
msgid "<strong>%s</strong>: The following plugins are not compatible with this plugin and may cause unexpected results:"
msgstr "<strong>%s</strong>: Az alábbi bővítmények nem kompatibilisek ezzel a bővítménnyel és ezért váratlan dolgokat okozhatnak:"

#: inc/admin/ui/notices.php:224
msgid "Deactivate"
msgstr "Deaktiválás"

#: inc/admin/ui/notices.php:266
msgid "WP Rocket Footer JS is not an official add-on. It prevents some options in WP Rocket from working correctly. Please deactivate it if you have problems."
msgstr "WP Rocket Footer JS nem egy hivatalos bővítmény. Akadályozza bizonyos WP Rocket opciók működését. Kérlek deaktiváld ha problémákat tapasztalsz."

#: inc/admin/ui/notices.php:306
#, php-format
msgid "Endurance Cache is currently enabled, which will conflict with WP Rocket Cache. Please set the Endurance Cache cache level to Off (Level 0) on the %1$sSettings > General%2$s page to prevent any issues."
msgstr "Endurance Cache jelenleg engedélyezve van, ami ütközik a WP Rocket gyorsítótárával. Kérlek állítsd az Endurance Cache gyorsítótár-szintjét OFF-ra (0-ás szint) a %1$sBeállítások > Általános %2$soldalon hogy megelőzd a problémákat."

#: inc/admin/ui/notices.php:327
#, php-format
msgid "%1$s: A custom permalink structure is required for the plugin to work properly. %2$sGo to permalinks settings%3$s"
msgstr "%1$s: Egyéni permalink struktúra szükséges a bővítmény megfelelő működéséhez. %2$sMenj a permalink beállításokhoz%3$s"

#: inc/admin/ui/notices.php:374
#, php-format
msgid "%s could not modify the .htaccess file due to missing writing permissions."
msgstr "%s Nem lehet módosítani a .htaccess fájlt a hiányzó írási jogosultságok miatt."

#: inc/admin/ui/notices.php:380
#: inc/admin/ui/notices.php:843
#, php-format
msgid "Troubleshoot: %1$sHow to make system files writeable%2$s"
msgstr "Hibaelhárítás: %1$sHogyan tedd írhatóvá a rendszerfájlokat%2$s"

#: inc/admin/ui/notices.php:382
#: inc/admin/ui/notices.php:845
msgid "https://docs.wp-rocket.me/article/626-how-to-make-system-files-htaccess-wp-config-writeable/?utm_source=wp_plugin&utm_medium=wp_rocket"
msgstr "https://docs.wp-rocket.me/article/626-how-to-make-system-files-htaccess-wp-config-writeable/?utm_source=wp_plugin&utm_medium=wp_rocket"

#: inc/admin/ui/notices.php:388
msgid "Don’t worry, WP Rocket’s page caching and settings will still function correctly."
msgstr "Ne aggódj, a WP Rocket oldal-gyorsítótárazása és beállításai így is megfelelően fognak működni."

#: inc/admin/ui/notices.php:388
msgid "For optimal performance, adding the following lines into your .htaccess is recommended (not required):"
msgstr "Az optimális teljesítmény érdekében javasoljuk, hogy vedd fel az alábbi sorokat a .htaccess fájlodba (nem kötelező):"

#: inc/admin/ui/notices.php:535
#, php-format
msgid "%1$s is good to go! %2$sTest your load time%4$s, or visit your %3$ssettings%4$s."
msgstr "%1$s használatra kész! %2$s Nézd meg a betöltési idődet%4$s, vagy látogasd meg a %3$sbeállításaidat%4$s."

#: inc/admin/ui/notices.php:576
msgid "Would you allow WP Rocket to collect non-sensitive diagnostic data from this website?"
msgstr "Megengeded a WP Rocket-nek, hogy névtelen, nem-érzékeny diagnosztikai adatokat gyűjtsünk a weboldaladról?"

#: inc/admin/ui/notices.php:577
msgid "This would help us to improve WP Rocket for you in the future."
msgstr "Ez segít nekünk abban, hogy jobbá tehessük a WP Rocketet a jövőben."

#: inc/admin/ui/notices.php:583
msgid "What info will we collect?"
msgstr "Milyen adatokat gyűjtünk?"

#: inc/admin/ui/notices.php:588
msgid "Below is a detailed view of all data WP Rocket will collect if granted permission. WP Rocket will never transmit any domain names or email addresses (except for license validation), IP addresses, or third-party API keys."
msgstr "Alább felsoroltuk részletesen, hogym ilyen adatokat fog gyűjteni a WP Rocket, ha megadod az engedélyt. A WP Rocket soha nem fog továbbítani domain neveket vagy email címeket  (kivéve licensz érvényesítéshez), IP címeket vagy külső félhez tartozó API kulcsokat."

#: inc/admin/ui/notices.php:597
msgid "Yes, allow"
msgstr "Igen, engedélyezés"

#: inc/admin/ui/notices.php:600
msgid "No, thanks"
msgstr "Nem, köszönöm"

#: inc/admin/ui/notices.php:639
msgid "Thank you!"
msgstr "Köszönjük!"

#: inc/admin/ui/notices.php:644
msgid "WP Rocket now collects these metrics from your website:"
msgstr "A WP Rocket most ezeket az adatokat gyűjti a weboldaladról:"

#: inc/admin/ui/notices.php:682
#, php-format
msgid "%s: Cache cleared."
msgstr "%s: Gyorsítótár ürítve."

#: inc/admin/ui/notices.php:689
#, php-format
msgid "%s: Post cache cleared."
msgstr "%s: Bejegyzések gyorsítótára ürítve."

#: inc/admin/ui/notices.php:696
#, php-format
msgid "%s: Term cache cleared."
msgstr "%s kifejezés gyorsítótár ürítve."

#: inc/admin/ui/notices.php:703
#, php-format
msgid "%s: User cache cleared."
msgstr "%s: Felhasználói gyorsítótár ürítve."

#: inc/admin/ui/notices.php:751
msgid "Stop Preload"
msgstr "Előtöltés megállítása"

#: inc/admin/ui/notices.php:781
msgid "Force deactivation "
msgstr "Kényszerített deaktiválás"

#: inc/admin/ui/notices.php:800
msgid "The following code should have been written to this file:"
msgstr "Az alábbi kódnak kellene bekerülnie ebbe a fájlba:"

#: inc/admin/ui/notices.php:831
#, php-format
msgid "%s cannot configure itself due to missing writing permissions."
msgstr "%snem tudja önmagát beállítani a hiányzó írási jogosultság miatt."

#: inc/admin/ui/notices.php:837
#, php-format
msgid "Affected file/folder: %s"
msgstr "Érintett fájl/mappa: %s"

#: inc/classes/admin/class-logs.php:124
msgid "The debug file could not be deleted."
msgstr "A hibakeresési fájl törlése nem sikerült."

#: inc/classes/class-wp-rocket-requirements-check.php:147
#, php-format
msgid "To function properly, %1$s %2$s requires at least:"
msgstr "A megfelő működés érdekében, a %1$s%2$s-hez szükséges legalább:"

#: inc/classes/class-wp-rocket-requirements-check.php:151
#, php-format
msgid "PHP %1$s. To use this WP Rocket version, please ask your web host how to upgrade your server to PHP %1$s or higher."
msgstr "PHP %1$s. Ahhoz hogy ezt a WP Rocket verziót használni tudd, kérd meg a tárhelyszolgáltatódat, hogy frissítse a szerveren a PHP-t  %1$s vagy újabb verzióra."

#: inc/classes/class-wp-rocket-requirements-check.php:156
#, php-format
msgid "WordPress %1$s. To use this WP Rocket version, please upgrade WordPress to version %1$s or higher."
msgstr "WordPress %1$s. Ahhoz hogy ezt a WP Rocket verziót használni tudd, kérlek frissítsd a WordPress-t %1$svagy újabb verzióra."

#: inc/classes/class-wp-rocket-requirements-check.php:159
msgid "If you are not able to upgrade, you can rollback to the previous version by using the button below."
msgstr "Ha nem lehetséges a frissítés, akkor visszaállíthatod az előzőt verziót az alábbi gombbal:"

#: inc/classes/class-wp-rocket-requirements-check.php:161
#: inc/deprecated/deprecated.php:1947
#, php-format
msgid "Re-install version %s"
msgstr "%sverzió újratelepítése"

#: inc/classes/dependencies/wp-media/background-processing/wp-background-process.php:447
#, php-format
msgid "Every %d Minutes"
msgstr "Minden %d percben"

#: inc/classes/logger/class-logger.php:260
#: inc/classes/logger/class-logger.php:292
msgid "The log file does not exist."
msgstr "A naplófájl nem létezik."

#: inc/classes/logger/class-logger.php:266
#: inc/classes/logger/class-logger.php:298
msgid "The log file could not be read."
msgstr "A naplófájl nem olvasható."

#: inc/classes/logger/class-logger.php:285
msgid "The logs are not saved into a file."
msgstr "A naplók nincsenek fájlba elmentve."

#: inc/Addon/WebP/AdminSubscriber.php:93
#, php-format
msgid "You are using %1$s to serve WebP images so you do not need to enable this option. %2$sMore info%3$s %4$s If you prefer to have WP Rocket serve WebP for you instead, please disable WebP display in %1$s."
msgid_plural "You are using %1$s to serve WebP images so you do not need to enable this option. %2$sMore info%3$s %4$s If you prefer to have WP Rocket serve WebP for you instead, please disable WebP display in %1$s."
msgstr[0] "A %1$s -t használod a WebP képek kiszolgálására, így nem szükséges ezt az opciót bejelölnöd. %2$sMore info%3$s %4$s Ha azt szeretnéd hogy ehelyett a WP Rocket szolgálja ki a WebP képeket, akkor tiltsd le a WebP megjelenítést a %1$s-ben."
msgstr[1] "A %1$s -t használod a WebP képek kiszolgálására, így nem szükséges ezt az opciót bejelölnöd. %2$sMore info%3$s %4$s Ha azt szeretnéd hogy ehelyett a WP Rocket szolgálja ki a WebP képeket, akkor tiltsd le a WebP megjelenítést a %1$s-ben."

#: inc/Addon/WebP/AdminSubscriber.php:105
msgid "WebP cache is disabled by filter."
msgstr "A WebP gyorsítótár le van tiltva szűrő által."

#: inc/Addon/WebP/AdminSubscriber.php:115
#: inc/Addon/WebP/AdminSubscriber.php:141
#, php-format
msgid "You are using %1$s to convert images to WebP. If you want WP Rocket to serve them for you, activate this option. %2$sMore info%3$s"
msgid_plural "You are using %1$s to convert images to WebP. If you want WP Rocket to serve them for you, activate this option. %2$sMore info%3$s"
msgstr[0] "A %1$s-t használod a képek WebP-re konvertálásához. Ha szeretnéd hogy ehelyett a WP Rocket végezze ezt akkor akkor aktiváld ezt az opciót. %2$sTovábi információ%3$s"
msgstr[1] "A %1$s -t használod a képek WebP-re konvertálásához. Ha szeretnéd hogy ehelyett a WP Rocket végezze ezt akkor akkor aktiváld ezt az opciót. %2$sTovábi információ%3$s"

#: inc/Addon/WebP/AdminSubscriber.php:127
#: inc/Addon/WebP/AdminSubscriber.php:153
#, php-format
msgid "You are using %1$s to convert images to WebP. WP Rocket will create separate cache files to serve your WebP images. %2$sMore info%3$s"
msgid_plural "You are using %1$s to convert images to WebP. WP Rocket will create separate cache files to serve your WebP images. %2$sMore info%3$s"
msgstr[0] "Jelenleg a %1$s használod képek WebP-re konvertálásához. A WP Rocket külön gyorsítótár fájlokat fog csinálni a WebP képeid kiszolgálására. %2$sBővebb információ%3$s"
msgstr[1] "Jelenleg a %1$s-t használod képek WebP-re konvertálásához. A WP Rocket külön gyorsítótár fájlokat fog csinálni a WebP képeid kiszolgálására. %2$sBővebb információ%3$s"

#: inc/Addon/WebP/AdminSubscriber.php:173
#, php-format
msgid "%5$sWe have not detected any compatible WebP plugin!%6$s%4$s If you don’t already have WebP images on your site consider using %3$sImagify%2$s or another supported plugin. %1$sMore info%2$s %4$s If you are not using WebP do not enable this option."
msgstr "%5$sNem találtunk semmilyen kompatibilis WebP bővítményt! %6$s%4$sHa nincsenek még WebP képek az oldaladon, fontold meg az %3$sImagify%2$s vagy más támogatott bővítmény használatát! %1$sBővebb információ%2$s%4$s Ha nem használsz WebP-t akkor ne engedélyezd ez az opciót."

#: inc/Addon/WebP/AdminSubscriber.php:185
msgid "WP Rocket will create separate cache files to serve your WebP images."
msgstr "A WP Rocket külön gyorsítótár fájlokat fog létrehozni a WebP képeid kiszolgálására."

#: inc/classes/subscriber/Tools/class-detect-missing-tags-subscriber.php:151
#, php-format
msgid "Failed to detect the following requirement in your theme: closing %1$s."
msgid_plural "Failed to detect the following requirements in your theme: closing %1$s."
msgstr[0] "Nem találtuk meg az alábbi követelményt a témádban: lezáró %1$s."
msgstr[1] "Nem találtuk meg az alábbi követelményeket a témádban: lezáró %1$s."

#: inc/classes/subscriber/Tools/class-detect-missing-tags-subscriber.php:159
msgid "https://docs.wp-rocket.me/article/99-pages-not-cached-or-minify-cssjs-not-working/?utm_source=wp_plugin&utm_medium=wp_rocket#theme"
msgstr "https://docs.wp-rocket.me/article/99-pages-not-cached-or-minify-cssjs-not-working/?utm_source=wp_plugin&utm_medium=wp_rocket#theme"

#: inc/common/admin-bar.php:124
#: inc/functions/i18n.php:41
#: inc/functions/i18n.php:51
msgid "All languages"
msgstr "Minden nyelv"

#: inc/common/admin-bar.php:160
msgid "Clear this post"
msgstr "Ürítsd ezen bejegyzést a gyorsítótárból"

#: inc/common/admin-bar.php:174
msgid "Purge this URL"
msgstr "Ürítsd ezt az URL-t a gyorsítótárból"

#: inc/common/admin-bar.php:194
msgid "Purge Sucuri cache"
msgstr "Sucuri gyorsítótár ürítése"

#: inc/common/admin-bar.php:218
#: views/settings/fields/rocket-cdn.php:55
msgid "Purge RocketCDN cache"
msgstr "Ürítsd a RocketCDN gyorsítótárat"

#: inc/common/admin-bar.php:231
#: views/settings/partials/documentation.php:14
msgid "Documentation"
msgstr "Dokumentáció"

#: inc/deprecated/3.10.php:163
msgid "OPcache purge failed."
msgstr "OPcache ürítése sikertelen."

#: inc/deprecated/3.10.php:168
msgid "OPcache successfully purged"
msgstr "OPcache sikeresen ürítve"

#: inc/deprecated/3.2.php:52
#: views/settings/page-sections/imagify.php:36
msgid "Activate Imagify"
msgstr "Aktiváld az Imagify-t"

#: inc/deprecated/3.2.php:52
msgid "Install Imagify for Free"
msgstr "Telepítsd az Imagify-t ingyen"

#: inc/deprecated/3.2.php:67
msgid "Speed up your website and boost your SEO by reducing image file sizes without losing quality with Imagify."
msgstr "Gyorsítsd fel a weboldaladat és javítsd a SEO-dat a képméretek csökkentésével, méghozzá minőségvesztés nélkül az Imagify-vel."

#: inc/deprecated/3.2.php:72
msgid "More details"
msgstr "Bővebb információ"

#: inc/deprecated/3.2.php:228
#, php-format
msgid "Sitemap preload: %d pages have been cached."
msgstr "Oldaltérkép előtöltve: %d oldal lett gyorsítótárazva."

#: inc/deprecated/3.2.php:261
#, php-format
msgid "Sitemap preload: %d uncached pages have now been preloaded. (refresh to see progress)"
msgstr "Oldaltérkép előtöltés: %d eddig gyorsítótárazatlan oldal most már elő van töltve. (frissítsd az oldalt a folyamat követéséhez)"

#: inc/deprecated/3.4.php:18
msgid "Choose a domain from the list"
msgstr "Válassz domain nevet a listáról"

#: inc/deprecated/3.4.php:31
msgid "No domain available in your Cloudflare account"
msgstr "Nem érhető el domain a Cloudflare fiókodban"

#: inc/deprecated/3.5.php:71
#: inc/deprecated/3.5.php:195
msgid "Curl is disabled on your server. Please ask your host to enable it. This is required for the Cloudflare Add-on to work correctly."
msgstr "A Curl le van tiltva a szervereden. Kérd meg a tárhelyszolgáltatódat, hogy engedélyezze. Erre feltétlenül szükség van a Cloudflare bővítmény működéséhez."

#: inc/deprecated/3.5.php:79
#, php-format
msgid "Cloudflare email, API key and Zone ID are not set. Read the %1$sdocumentation%2$s for further guidance."
msgstr "Cloudflare email, API kulcs és zónaazonosító nincs beállítva. Olvasd el a %1$sdokumentációt%2$s bővebb útmutatásért."

#: inc/deprecated/3.5.php:206
#, php-format
msgid "Cloudflare email and API key are not set. Read the %1$sdocumentation%2$s for further guidance."
msgstr "Cloudflare email és API kulcs nincs beállítva. Olvasd el a %1$sdokumentációt%2$s bővebb útmutatásért."

#: inc/deprecated/3.5.php:271
msgid "Connection to Cloudflare failed"
msgstr "Sikertelen a csatlakozás a Cloudflare-hez."

#: inc/deprecated/DeprecatedClassTrait.php:54
#, php-format
msgid "The called class %1$s is <strong>deprecated</strong> since version %2$s! Use %3$s instead."
msgstr "A hivatkozott osztály %1$s , <strong>deprecated azaz elavult</strong> a %2$s verzió óta! Használd a %3$s -t helyette."

#: inc/deprecated/DeprecatedClassTrait.php:65
#, php-format
msgid "The called class %1$s is <strong>deprecated</strong> since version %2$s!"
msgstr "A hivatkozott osztály %1$s, <strong>deprecated azaz elavult</strong> a %2$s verzió óta!"

#: inc/deprecated/deprecated.php:240
msgid "<strong>JS</strong> files with Deferred Loading JavaScript"
msgstr "<strong>JS</strong> fájlok a késleltetett JavaScript betöltés használatával"

#: inc/deprecated/deprecated.php:293
msgid "Add URL"
msgstr "URL hozzáadása"

#: inc/deprecated/deprecated.php:1277
#: views/settings/fields/import-form.php:22
msgid "Before you can upload your import file, you will need to fix the following error:"
msgstr "Mielőtt feltöltheted az importálandó fájlodat, ki kell javítanod az alábbi hibát:"

#: inc/deprecated/deprecated.php:1288
#: views/settings/fields/import-form.php:35
#, php-format
msgid "Choose a file from your computer (maximum size: %s)"
msgstr "Válassz egy fájlt a gépedről (maximum méret: %s)"

#: inc/deprecated/deprecated.php:1385
msgid "Your Cloudflare credentials are valid."
msgstr "A Cloudflare belépési adataid érvényesek."

#: inc/deprecated/deprecated.php:1389
msgid "Your Cloudflare credentials are invalid!"
msgstr "A Cloudflare belépési adataid érvénytelenek!"

#: inc/deprecated/deprecated.php:1453
msgid "Save and optimize"
msgstr "Mentés és optimizálás"

#: inc/deprecated/deprecated.php:1453
msgid "Optimize"
msgstr "Optimizálás"

#: inc/deprecated/deprecated.php:1464
#: inc/deprecated/deprecated.php:1666
msgctxt "screen-reader-text"
msgid "Note:"
msgstr "Megjegyzés:"

#: inc/deprecated/deprecated.php:1470
msgctxt "screen-reader-text"
msgid "Performance tip:"
msgstr "Teljesítmény tipp:"

#: inc/deprecated/deprecated.php:1476
msgctxt "screen-reader-text"
msgid "Third-party feature detected:"
msgstr "Idegen féltől származó funkció észlelve:"

#: inc/deprecated/deprecated.php:1488
#: inc/deprecated/deprecated.php:1672
msgctxt "screen-reader-text"
msgid "Warning:"
msgstr "Figyelmeztetés:"

#: inc/deprecated/deprecated.php:1501
msgctxt "button text"
msgid "Download settings"
msgstr "Letöltési beállítások"

#: inc/deprecated/deprecated.php:1531
msgid "Replace site's hostname with:"
msgstr "Cseréld ki a weboldal címét erre:"

#: inc/deprecated/deprecated.php:1550
#: inc/deprecated/deprecated.php:1587
#: inc/deprecated/deprecated.php:1615
#: views/settings/fields/cnames.php:41
#: views/settings/fields/cnames.php:75
#: views/settings/fields/cnames.php:105
msgid "reserved for"
msgstr "fenntartva erre:"

#: inc/deprecated/deprecated.php:1552
#: inc/deprecated/deprecated.php:1589
#: inc/deprecated/deprecated.php:1617
#: views/settings/fields/cnames.php:44
#: views/settings/fields/cnames.php:78
#: views/settings/fields/cnames.php:108
msgid "All files"
msgstr "Minden fájl"

#: inc/deprecated/deprecated.php:1564
#: inc/deprecated/deprecated.php:1594
#: inc/deprecated/deprecated.php:1622
#: views/settings/fields/cnames.php:56
#: views/settings/fields/cnames.php:90
#: views/settings/fields/cnames.php:120
msgid "Images"
msgstr "Képek"

#: inc/deprecated/deprecated.php:1635
#: views/settings/fields/cnames.php:132
msgid "Add CNAME"
msgstr "CNAME hozzáadása"

#: inc/deprecated/deprecated.php:1706
msgid "Watch the video"
msgstr "Nézd meg a videót"

#: inc/deprecated/deprecated.php:1771
msgid "Basic"
msgstr "Egyszerű"

#: inc/deprecated/deprecated.php:1772
msgid "Static Files"
msgstr "Statikus fájlok"

#: inc/deprecated/deprecated.php:1774
msgid "Advanced"
msgstr "Haladó"

#: inc/deprecated/deprecated.php:1944
#, php-format
msgid "%1$s %2$s requires at least PHP %3$s to function properly. To use this version, please ask your web host how to upgrade your server to PHP %3$s or higher. If you are not able to upgrade, you can rollback to the previous version by using the button below."
msgstr "A %1$s %2$s minimum PHP%3$s verziót igényelnek a megfelelő működéshez. Kérlek kérd meg a tárhelyszolgáltatódat hogy frissítse a webszerveredet PHP %3$s vagy újabb verzióra. Ha nem lehetséges a frissítése akkor visszaállhatsz a korábbi WP Rocket verzióra az alábbi gomb megnyomásával."

#: inc/functions/admin.php:21
msgid "There seems to be an issue validating your license. Please see the error message below."
msgid_plural "There seems to be an issue validating your license. You can see the error messages below."
msgstr[0] "Probléma merült fel a licenszed érvényesítése közben. Kérlek tekintsd meg az alábbi hibaüzenetet:"
msgstr[1] "Problémák merültek fel a licenszed érvényesítése közben. Kérlek tekintsd meg az alábbi hibaüzenetet:"

#: inc/functions/admin.php:361
msgid "Server type:"
msgstr "Szerver típusa:"

#: inc/functions/admin.php:370
msgid "PHP version number:"
msgstr "PHP verziószám:"

#: inc/functions/admin.php:379
msgid "WordPress version number:"
msgstr "WordPress verziószám:"

#: inc/functions/admin.php:388
msgid "WordPress multisite:"
msgstr "WordPress multisite:"

#: inc/functions/admin.php:397
msgid "Current theme:"
msgstr "Jelenlegi téma:"

#: inc/functions/admin.php:406
msgid "Current site language:"
msgstr "Jelenlegi oldal nyelv:"

#: inc/functions/admin.php:415
msgid "Active plugins:"
msgstr "Aktív bővítmények:"

#: inc/functions/admin.php:418
msgid "Plugin names of all active plugins"
msgstr "Aktív bővítmények nevei:"

#: inc/functions/admin.php:424
msgid "Anonymized WP Rocket settings:"
msgstr "Anonimizált WP Rocket beállítások:"

#: inc/functions/admin.php:427
msgid "Which WP Rocket settings are active"
msgstr "Melyik WP Rocket beállítások aktívak"

#: inc/functions/options.php:429
msgid "The provided license data are not valid."
msgstr "A megadott licensz adatok nem érvényesek."

#: inc/functions/options.php:432
#, php-format
msgid "To resolve, please %1$scontact support%2$s."
msgstr "A megoldáshoz, kérlek %1$skeresd az ügyfélszolgálatunkat%2$s."

#: inc/functions/options.php:491
#: inc/functions/options.php:530
msgid "License validation failed. Our server could not resolve the request from your website."
msgstr "A licensz érvényesítése sikertelen. A szerverünk nem tudja feldolgozni a weboldaladtól érkező kérést."

#: inc/functions/options.php:491
#, php-format
msgid "Try clicking %1$sValidate License%2$s below. If the error persists, follow %3$sthis guide%4$s."
msgstr "Próbálj a %1$sLicensz érvényesítése%2$s gombra kattintani alább. Ha a hiba most is fennáll, akkor %3$skövesd ezt a leírást%4$s."

#: inc/functions/options.php:507
msgid "License validation failed. You may be using a nulled version of the plugin. Please do the following:"
msgstr "A licensz érvényesítése sikertelen. Lehet hogy nem-licenszelt verziót használsz ebből a bővítményből. Kérlek tedd az alábbiakat:"

#: inc/functions/options.php:507
#: inc/functions/options.php:549
#, php-format
msgid "Login to your WP Rocket %1$saccount%2$s"
msgstr "Jelentkezz be a WP Rocket %1$sfiókodba%2$s"

#: inc/functions/options.php:507
#: inc/functions/options.php:549
msgid "Download the zip file"
msgstr "Töltsd le a zip fájlt"

#: inc/functions/options.php:507
#: inc/functions/options.php:549
msgid "Reinstall"
msgstr "Újratelepítés"

#: inc/functions/options.php:507
#, php-format
msgid "If you do not have a WP Rocket account, please %1$spurchase a license%2$s."
msgstr "Ha nincs WP Rocket fiókod, kérlek %1$svásárolj licenszt%2$s."

#: inc/functions/options.php:515
msgid "License validation failed. This user account does not exist in our database."
msgstr "A licensz érvényesítése sikertelen. Ez a felhasználói fiók nem létezik az adatbázisunkban."

#: inc/functions/options.php:515
msgid "To resolve, please contact support."
msgstr "A megoldáshoz kérlek vedd fel a kapcsolatot az ügyfélszolgálatunkkal."

#: inc/functions/options.php:523
msgid "License validation failed. This user account is blocked."
msgstr "Licensz érvényesítése sikertelen. Ez a felhasználói fiók le van tiltva."

#: inc/functions/options.php:523
#, php-format
msgid "Please see %1$sthis guide%2$s for more info."
msgstr "Kérlek kövesd %1$sezt a leírást %2$s bővebb információért."

#: inc/functions/options.php:530
#, php-format
msgid "Try clicking %1$sSave Changes%2$s below. If the error persists, follow %3$sthis guide%4$s."
msgstr "Kattints a %1$sVáltozások mentése%2$s gombra. Ha a továbbra is fennáll, kövesd %3$sezt a leírást%4$s."

#: inc/functions/options.php:543
msgid "Your license is not valid."
msgstr "A licenszed érvénytelen."

#: inc/functions/options.php:543
#, php-format
msgid "Make sure you have an active %1$sWP Rocket license%2$s."
msgstr "Ellenőrizd, hogy van-e érvényes %1$sWP Rocket licenszed%2$s."

#: inc/functions/options.php:545
msgid "You have added as many sites as your current license allows."
msgstr "Már felvettél annyi weboldalt, amennyid a jelenlegi licenszed lehetővé tett."

#: inc/functions/options.php:545
#, php-format
msgid "Upgrade your %1$saccount%2$s or %3$stransfer your license%2$s to this domain."
msgstr "Bővítsd a %1$sfiókodat%2$s vagy %3$shelyezd át%2$s a licenszedet erre a domainre."

#: inc/functions/options.php:547
msgid "This website is not allowed."
msgstr "Ez a weboldal nincs engedélyezve."

#: inc/functions/options.php:547
#, php-format
msgid "Please %1$scontact support%2$s."
msgstr "Kérlek %1$svedd fel a kapcsolatot az ügyfélszolgálatunkkal%2$s."

#: inc/functions/options.php:549
msgid "This license key is not recognized."
msgstr "Ez a licensz kulcs ismeretlen."

#: inc/functions/options.php:549
#, php-format
msgid "If the issue persists, please %1$scontact support%2$s."
msgstr "Ha a probléama továbbra is fennáll, kérlek %1$svedd fel a kapcsolatot az ügyfélszolgálatunkkal%2$s."

#: inc/functions/options.php:555
#, php-format
msgid "License validation failed: %s"
msgstr "Licensz érvényesítés sikertelen: %s"

#: inc/vendors/classes/class-imagify-partner.php:531
msgid "Plugin installed successfully."
msgstr "Bővítmény sikeresen telepítve."

#: inc/vendors/classes/class-imagify-partner.php:532
msgid "Sorry, you are not allowed to install plugins on this site."
msgstr "Elnézést, nincs jogosultságod bővítményeket telepíteni erre a weboldalra."

#: inc/vendors/classes/class-imagify-partner.php:533
msgid "Sorry, you are not allowed to do that."
msgstr "Elnézést, nincs jogosultságod ezt tenni."

#: inc/vendors/classes/class-imagify-partner.php:534
msgid "Plugin install failed."
msgstr "Bővítmény telepítése sikertelen."

#: inc/vendors/classes/class-imagify-partner.php:535
msgid "Go back"
msgstr "Vissza"

#: views/cpcss/activate-cpcss-mobile.php:13
msgid "Load CSS asynchronously for mobile"
msgstr "CSS aszinkron betöltés mobilhoz"

#: views/cpcss/activate-cpcss-mobile.php:14
msgid "Your website currently uses the same Critical Path CSS for both desktop and mobile."
msgstr "A weboldalad jelenleg ugyanazt a kritikus útvonalú CSS-t használja mind asztali mind mobil nézet számára."

#: views/cpcss/activate-cpcss-mobile.php:15
msgid "Click the button to enable mobile-specific CPCSS for your site."
msgstr "Kattints a gombra hogy engedélyezhesd a mobil-specifikus CPCSS-t az oldaladhoz."

#: views/cpcss/activate-cpcss-mobile.php:20
#: views/settings/enable-google-fonts.php:19
#, php-format
msgid "This is a one-time action and this button will be removed afterwards. %1$sMore info%2$s"
msgstr "Ez egy egyszeri művelet és a gomb el fog tűnni utána. %1$sBővebb információ%2$s."

#: views/cpcss/activate-cpcss-mobile.php:30
#, php-format
msgid "Your site is now using mobile-specific critical path CSS. %1$sMore info%2$s"
msgstr "A weboldalad most már mobil-specifikus kritikus útvonalú CSS-t használ. %1$sBővebb információ%2$s"

#: views/cpcss/activate-cpcss-mobile.php:39
msgid "Generate Mobile Specific CPCSS"
msgstr "Generáld a mobil-specifikus CPCSS-t"

#: views/cpcss/metabox/container.php:19
msgid "Critical Path CSS"
msgstr "Kritikus útvonalú CSS"

#: views/cpcss/metabox/generate.php:23
#, php-format
msgid "Generate specific Critical Path CSS for this post. %1$sMore info%2$s"
msgstr "Generálj specifikus kritikus útvonalú CSS-t ezen bejegyzéshez. %1$sBővebb információ%2$s"

#: views/cpcss/metabox/generate.php:33
#, php-format
msgid "This post uses specific Critical Path CSS. %1$sMore info%2$s"
msgstr "Ez a bejegyzés specifikus kritikus útvonalú CSS-t használ. %1$sBővebb információ%2$s"

#: views/cpcss/metabox/generate.php:56
msgid "Revert back to the default CPCSS"
msgstr "Visszaváltás az alapértelmezett CPCSS-re"

#: views/deactivation-intent/form.php:21
msgid "Facing an issue?"
msgstr "Problémát tapasztalsz?"

#: views/deactivation-intent/form.php:24
msgid "It is not always necessary to deactivate WP Rocket when facing any issues. Most of them can be fixed by deactivating only some options."
msgstr "Nem mindig szükséges deaktiválni a WP Rocket-et, ha problémát tapasztalsz. A legtöbb megoldható csupán néhány opció kikapcsolásával."

#: views/deactivation-intent/form.php:29
#, php-format
msgid "Our advice? Instead of deactivating WP Rocket, use our %1$sSafe Mode%2$s to quickly disable LazyLoad, File Optimization, and CDN options. Then check to see if your issue is resolved."
msgstr "A mi tippünk: ahelyett hogy deaktiválnád a WP Rocket-et, használd a %1$scsökkentett módunkat%2$s hogy gyorsan letilthasd a LazyLoad-ot, a Fájl Optimizálást és a CDN beállításokat. És ezután nézd meg, hogy megoldódott-e a problémád."

#: views/deactivation-intent/form.php:35
msgid "Do you want to use our Safe Mode to troubleshoot WP Rocket?"
msgstr "Szeretnéd használni a csökkentett módunkat a WP Rocket hibaelhárításához?"

#: views/deactivation-intent/form.php:55
#, php-format
msgid "Yes, apply \"%1$sSafe Mode%2$s\""
msgstr "Igen, kapcsold be a \"%1$sCsökkentett Mód%2$s\"-ot"

#: views/deactivation-intent/form.php:76
msgid "No, deactivate and snooze this message for"
msgstr "Nem, deaktiváld a bővítmény és tüntesd el ezt az üzenetet ennyi időre"

#: views/deactivation-intent/form.php:78
msgid "1 day"
msgstr "1 nap"

#: views/deactivation-intent/form.php:79
msgid "7 days"
msgstr "7 nap"

#: views/deactivation-intent/form.php:80
msgid "30 days"
msgstr "30 nap"

#: views/deactivation-intent/form.php:81
msgid "Forever"
msgstr "Örökre"

#: views/deactivation-intent/form.php:87
msgid "Cancel"
msgstr "Mégse"

#: views/deactivation-intent/form.php:88
msgid "Confirm"
msgstr "Megerősítés"

#: views/settings/enable-google-fonts.php:13
msgid "Enable Google Font Optimization"
msgstr "Engedélyezd a Google Font optimizálást"

#: views/settings/enable-google-fonts.php:14
msgid "Improves font performance and combines multiple font requests to reduce the number of HTTP requests."
msgstr "Javítja a betűtípus-betöltés teljesítményét és összevonja a különböző betűtípusok lekérését hogy csökkentse a HTTP kérések számát."

#: views/settings/enable-google-fonts.php:29
#, php-format
msgid "Google Fonts Optimization is now enabled for your site. %1$sMore info%2$s"
msgstr "A Google Font optimizálás engedélyezve van az oldalad számára. %1$sBővebb információ%2$s."

#: views/settings/enable-google-fonts.php:38
msgid "Optimize Google Fonts"
msgstr "Optimizáld a Google Font-okat"

#: views/settings/fields/cache-lifespan.php:26
msgid "Clear cache after"
msgstr "Ürítsd a gyorsítótárat ennyi idő után:"

#: views/settings/fields/cnames.php:58
#: views/settings/fields/cnames.php:92
#: views/settings/fields/cnames.php:122
msgid "CSS & JavaScript"
msgstr "CSS & JavaScript"

#: views/settings/fields/cnames.php:59
#: views/settings/fields/cnames.php:93
#: views/settings/fields/cnames.php:123
msgid "JavaScript"
msgstr "JavaScript"

#: views/settings/fields/cnames.php:60
#: views/settings/fields/cnames.php:94
#: views/settings/fields/cnames.php:124
msgid "CSS"
msgstr "CSS"

#: views/settings/fields/import-form.php:29
msgid "Import settings"
msgstr "Beállítások importálása"

#: views/settings/fields/one-click-addon.php:43
#: views/settings/fields/rocket-addon.php:43
msgid "Add-on status"
msgstr "Bővítmény állapota"

#: views/settings/fields/rocket-addon.php:72
msgid "Modify options"
msgstr "Beállítások módosítása"

#: views/settings/fields/rocket-cdn.php:29
#: views/settings/fields/rocket-cdn.php:41
msgid "CDN CNAME"
msgstr "CDN CNAME"

#: views/settings/fields/rocket-cdn.php:62
#, php-format
msgid "Purges RocketCDN cached resources for your website. %s"
msgstr "Üríti a RocketCDN által gyorsítótárazott erőforrásokat a weboldaladhoz.%s"

#: views/settings/fields/rocket-cdn.php:63
#: views/settings/page-sections/cloudflare.php:36
#: views/settings/page-sections/sucuri.php:37
msgid "Learn more"
msgstr "Tudj meg többet."

#: views/settings/fields/rocket-cdn.php:72
msgid "Clear all RocketCDN cache files"
msgstr "Töröld az összes RocketCDN gyorsítótár fájlt"

#: views/settings/page-sections/cloudflare.php:27
msgid "Cloudflare Cache"
msgstr "Cloudflare Cache"

#: views/settings/page-sections/cloudflare.php:35
#: views/settings/page-sections/sucuri.php:36
#, php-format
msgid "Purges cached resources for your website. %s"
msgstr "Üríti az oldaladhoz tartozó gyorsítótárazott erőforrásokat. %s"

#: views/settings/page-sections/cloudflare.php:36
msgid "https://support.cloudflare.com/hc/en-us/articles/200169246"
msgstr "https://support.cloudflare.com/hc/en-us/articles/200169246"

#: views/settings/page-sections/cloudflare.php:45
msgid "Clear all Cloudflare cache files"
msgstr "Üríti az összes Cloudflare -s gyorsítótárazott fájlt"

#: views/settings/page-sections/dashboard.php:38
msgid "Congratulations!"
msgstr "Gratulálunk!"

#: views/settings/page-sections/dashboard.php:40
msgid "WP Rocket is now activated and already working for you."
msgstr "A WP Rocket most már aktiválva van, és már dolgozik is Neked."

#: views/settings/page-sections/dashboard.php:42
msgid "Your website should be loading faster now!"
msgstr "A weboldalad most már gyorsabb lett!"

#: views/settings/page-sections/dashboard.php:44
#, php-format
msgid "To guarantee fast websites, WP Rocket automatically applies 80% of web performance best practices."
msgstr "Garantáljuk a gyors weboldalak! Ezért a WP Rocket már alapból, magától alkalmazza a web-teljesítmény gyorsítási tippek 80%-át."

#: views/settings/page-sections/dashboard.php:44
msgid "We also enable options that provide immediate benefits to your website."
msgstr "Továbbá olyan opciókat is engedélyezünk amik azonnali előnyöket nyújtanak a weboldaladnak."

#: views/settings/page-sections/dashboard.php:45
msgid "Continue to the options to further optimize your site!"
msgstr "Lépj tovább a beállításokba hogy tovább optimizálhasd az oldaladat!"

#: views/settings/page-sections/dashboard.php:62
msgid "My Account"
msgstr "Fiókom"

#: views/settings/page-sections/dashboard.php:68
msgid "Refresh info"
msgstr "Frissítés"

#: views/settings/page-sections/dashboard.php:86
msgid "with"
msgstr "vele:"

#: views/settings/page-sections/dashboard.php:100
msgid "Expiration Date"
msgstr "Lejárat dátuma:"

#: views/settings/page-sections/dashboard.php:110
msgid "View my account"
msgstr "Fiókom megtekintése"

#: views/settings/page-sections/dashboard.php:137
msgid "Quick Actions"
msgstr "Gyors műveletek"

#: views/settings/page-sections/dashboard.php:144
msgid "Remove all cached files"
msgstr "Töröld az összes gyorsítótárazott fájlt"

#: views/settings/page-sections/dashboard.php:164
#: views/settings/page-sections/dashboard.php:170
msgid "Regenerate Critical CSS"
msgstr "Kritikus útvonalú CSS újragenerálása"

#: views/settings/page-sections/dashboard.php:182
msgid "Remove Used CSS Cache"
msgstr "Töröld a felhasznált CSS-ek gyorsítótárát"

#: views/settings/page-sections/dashboard.php:205
msgid "Frequently Asked Questions"
msgstr "Gyakori kérdések"

#: views/settings/page-sections/dashboard.php:219
msgid "Still cannot find a solution?"
msgstr "Nem találtál megoldást?"

#: views/settings/page-sections/dashboard.php:220
msgid "Submit a ticket and get help from our friendly and knowledgeable Rocketeers."
msgstr "Küldj egy levelet és segítséget kapsz a barátságos és okos Rakétáinktól"

#: views/settings/page-sections/dashboard.php:228
msgid "Ask support"
msgstr "Kérdezd meg az ügyfélszolgálatot"

#: views/settings/page-sections/database.php:25
msgid "Backup your database before you run a cleanup!"
msgstr "Mentsd le az adatbázisodat mielőtt karbantartást indítassz!"

#: views/settings/page-sections/database.php:26
msgid "Once a database optimization has been performed, there is no way to undo it."
msgstr "Amint az adatbázisoptimizálás kész, nincs lehetőség visszavonni."

#: views/settings/page-sections/database.php:28
msgid "Save Changes and Optimize"
msgstr "Mentsd a változásokat és optimizálás indítása"

#: views/settings/page-sections/imagify.php:21
#, php-format
msgid "%1$sWP ROCKET%2$s created %3$sIMAGIFY%4$s %1$sfor best-in-class image optimization.%2$s"
msgstr "%1$sWP ROCKET%2$s megalkotta az %3$sIMAGIFY%4$s %1$s-t a legjobb kép optimizáláshoz.%2$s"

#: views/settings/page-sections/imagify.php:24
msgid "Compress image to make your website faster, all while maintaining image quality."
msgstr "Tömörítsd a képeket hogy gyorsabbá tedd a weboldaladat, mindezd úgy hogy a képek minősége nem változik."

#: views/settings/page-sections/imagify.php:25
msgid "More on Imagify:"
msgstr "Bővebben az Imagify-ről:"

#: views/settings/page-sections/imagify.php:27
msgid "Imagify Plugin Page"
msgstr "Imagify bővítmény oldal"

#: views/settings/page-sections/imagify.php:28
msgid "Imagify Website"
msgstr "Imagify weboldal"

#: views/settings/page-sections/imagify.php:29
msgid "Review of Image Compression Plugins"
msgstr "Képtömörítő bővítmények áttekintése"

#: views/settings/page-sections/imagify.php:38
msgid "Install Imagify"
msgstr "Imagify telepítése"

#: views/settings/page-sections/license.php:22
msgid "WP Rocket was not able to automatically validate your license."
msgstr "WP Rocket nem tudta automatikusan érvényesíteni a licenszedet"

#: views/settings/page-sections/license.php:29
#, php-format
msgid "Follow this %1$s, or contact %2$s to get the engine started."
msgstr "Kövesd ezt%1$s, vagy vedd fel a kapcsolatot%2$s a kezdéshez."

#: views/settings/page-sections/license.php:32
#, php-format
msgid "%1$s%2$s%3$stutorial%4$s"
msgstr "%1$s%2$s%3$sleírás%4$s"

#: views/settings/page-sections/license.php:34
msgid "https://docs.wp-rocket.me/article/100-resolving-problems-with-license-validation/?utm_source=wp_plugin&utm_medium=wp_rocket"
msgstr "https://docs.wp-rocket.me/article/100-resolving-problems-with-license-validation/?utm_source=wp_plugin&utm_medium=wp_rocket"

#: views/settings/page-sections/license.php:40
#, php-format
msgid "%1$s%2$s%3$ssupport%4$s"
msgstr "%1$s%2$s%3$stámogatás%4$s"

#: views/settings/page-sections/sucuri.php:46
msgid "Clear all Sucuri cache files"
msgstr "Ürítsd a Sucuri gyorsítótár fájlokat"

#: views/settings/page-sections/tools.php:20
#, php-format
msgid "Files size: %1$s. Number of entries: %2$s."
msgstr "Fájlok mérete: %1$s. Bejegyzések száma: %2$s."

#: views/settings/page-sections/tools.php:23
#, php-format
msgid "%1$sDownload the file%2$s."
msgstr "%1$sTöltsd le a fájlt %2$s."

#: views/settings/page-sections/tools.php:26
#, php-format
msgid "%1$sDelete the file%2$s."
msgstr "%1$sTöröld a fájlt%2$s."

#: views/settings/page-sections/tools.php:37
msgid "Export settings"
msgstr "Beállítások exportálása"

#: views/settings/page-sections/tools.php:38
msgid "Download a backup file of your settings"
msgstr "Tölts le egy tartalék másolatot a beállításaidról"

#: views/settings/page-sections/tools.php:46
msgid "Download settings"
msgstr "Letöltési beállítások"

#: views/settings/page-sections/tools.php:60
msgid "Rollback"
msgstr "Visszaállás korábbi verzióra"

#: views/settings/page-sections/tools.php:64
#, php-format
msgid "Has version %s caused an issue on your website?"
msgstr "Problémát okozott a %s-s verzió a weboldaladon?"

#: views/settings/page-sections/tools.php:69
#, php-format
msgid "You can rollback to the previous major version here.%sThen send us a support request."
msgstr "Visszatérhetsz a korábbi főverzióra itt. %s Utána írj nekünk levelet."

#: views/settings/page-sections/tools.php:80
#, php-format
msgid "Reinstall version %s"
msgstr "Telepítsd újra a %s verziót"

#: views/settings/page-sections/tools.php:106
msgid "Debug mode"
msgstr "Hibakereső üzemmód"

#: views/settings/page-sections/tools.php:111
msgid "Create a debug log file."
msgstr "Hibakereső naplófájl létrehozása"

#: views/settings/page-sections/tutorials.php:13
#: views/settings/partials/getting-started.php:18
msgid "Getting Started"
msgstr "Kezdjük el"

#: views/settings/page-sections/tutorials.php:15
msgid "Getting Started with WP Rocket"
msgstr "Vegyük használatba a WP Rocket-et"

#: views/settings/page-sections/tutorials.php:16
#: views/settings/partials/getting-started.php:12
msgid "Finding the Best Settings for Your Site"
msgstr "Találjuk meg a legjobb beállításokat a weboldaladhoz"

#: views/settings/page-sections/tutorials.php:17
#: views/settings/partials/getting-started.php:13
msgid "How to Check if WP Rocket is Caching Your Site"
msgstr "Hogyan győződjünk meg arról hogy a WP Rocket gyorsítótárazza az oldaladat"

#: views/settings/page-sections/tutorials.php:18
#: views/settings/partials/getting-started.php:14
msgid "How to Measure the Speed of Your Site"
msgstr "Hogyan mérjük le az oldalad sebességét"

#: views/settings/page-sections/tutorials.php:19
msgid "How Preloading Works"
msgstr "Hogyan működik az előtöltés"

#: views/settings/page-sections/tutorials.php:23
msgid "Passing the Core Web vitals"
msgstr "Az alapvető web mutatók teljesítése"

#: views/settings/page-sections/tutorials.php:25
msgid "How to improve LCP with WP Rocket"
msgstr "Hogyan javítsuk az LCP-t a WP Rocket-tel"

#: views/settings/page-sections/tutorials.php:26
msgid "How to improve FID with WP Rocket"
msgstr "Hogyan javítsuk az FID-t a WP Rocket-tel"

#: views/settings/page-sections/tutorials.php:27
msgid "How to improve CLS with WP Rocket"
msgstr "Hogyan javítsuk az CLS-t a WP Rocket-tel"

#: views/settings/page-sections/tutorials.php:31
msgid "Troubleshooting"
msgstr "Hibaelhárítás"

#: views/settings/page-sections/tutorials.php:33
msgid "Troubleshooting Display Issues with File Optimization"
msgstr "Megjelenési hibák javítássa a fájloptimizálással"

#: views/settings/page-sections/tutorials.php:34
msgid "How to Find the Right JavaScript to Exclude"
msgstr "Hogyan találjuk meg, melyik JavaScript fájlt kell kizárni"

#: views/settings/page-sections/tutorials.php:35
msgid "How External Content Slows Your Site"
msgstr "Hogyan lassítja le a külső tartalom a weboldaladat"

#: views/settings/page-sections/tutorials.php:41
msgid "Set Up the Cloudflare Add-on"
msgstr "Állítsd be a Cloudflare bővítményt"

#: views/settings/page.php:16
msgid "WP Rocket Settings"
msgstr "WP Rocket beállítások"

#: views/settings/page.php:30
#, php-format
msgid "version %s"
msgstr "verzió %s"

#: views/settings/page.php:60
msgid "Show Sidebar"
msgstr "Oldalsáv megjelenítése"

#: views/settings/page.php:82
#, php-format
msgid "Below is a detailed view of all data WP Rocket will collect %1$sif granted permission.%2$s"
msgstr "Alább részletes listát látsz mindazon adatokról amiket a WP Rocket gyűjteni fog ha %1$smegadod az engedélyt.%2$s"

#: views/settings/page.php:87
msgid "WP Rocket will never transmit any domain names or email addresses (except for license validation), IP addresses, or third-party API keys."
msgstr "A WP Rocket soha nem fog továbbítani domain neveket email címeket  (kivéve a licensz-ellenőrzéshez), IP címeket vagy külső félhez tartozó API kulcsokat."

#: views/settings/page.php:89
msgid "Activate Rocket analytics"
msgstr "Aktiváld a Rocket analitikát"

#: views/settings/partials/documentation.php:15
msgid "It is a great starting point to fix some of the most common issues."
msgstr "Ez egy nagyszerű kezdőpont a leggyakoribb hibák elhárításához"

#: views/settings/partials/documentation.php:22
msgid "Read the documentation"
msgstr "Olvasd el a dokumentációt"

#: views/settings/partials/getting-started.php:11
msgid "What WP Rocket Does For You By Default"
msgstr "Mit tesz alapból a WP Rocket Számodra"

#: views/settings/partials/sidebar.php:12
msgid "How to correctly measure your website’s loading time"
msgstr "Hogyan mérjük le pontosan a weboldal betöltési idejét"

#: views/settings/partials/sidebar.php:14
msgid "Check our tutorial and learn how to measure the speed of your site."
msgstr "Tekintsd meg a bemutatónkat és tudd meg, hogyan mérheted a weboldalad sebességét"

#: views/settings/partials/sidebar.php:15
#: views/settings/partials/sidebar.php:24
msgid "Read our guide"
msgstr "Olvasd el a leírásunkat"

#: views/settings/partials/sidebar.php:18
msgid "Learn about optimal WP Rocket settings for mobile."
msgstr "Tudj meg többet a WP Rocket mobilhoz való beállításáról"

#: views/settings/partials/sidebar.php:27
msgid "Test and Improve Google Core Web Vitals for WordPress."
msgstr "Teszteld és fejleszd a Google Core Web Vital értékeket WordPress számára"

#: views/settings/partials/sidebar.php:28
msgid "Read more"
msgstr "Tudj meg többet"

#: views/settings/partials/sidebar.php:33
msgid "You have not activated logged-in user cache."
msgstr "Nem aktiváltad a bejelentkezett felhasználók gyorsítótárát"

#: views/settings/partials/sidebar.php:34
msgid "Use a private browser to check your website's speed and visual appearance."
msgstr "Használj privát böngésző üzemmódot hogy megtekintsd a weboldalad sebességét és kinézetét a gyorsítótárazással"

#: views/settings/sections/addons-container.php:24
#: views/settings/sections/fields-container.php:28
msgid "Need Help?"
msgstr "Segítségre van szükséged?"

#: inc/Addon/Cloudflare/Cloudflare.php:205
msgid "days"
msgstr ""

#: inc/Addon/Cloudflare/Cloudflare.php:208
msgid "seconds"
msgstr ""

#: inc/Addon/Cloudflare/Cloudflare.php:210
msgid "minutes"
msgstr ""

#: inc/Addon/Cloudflare/Cloudflare.php:212
msgid "hours"
msgstr ""

#: inc/Addon/Cloudflare/Subscriber.php:224
#: inc/Addon/Cloudflare/Subscriber.php:253
#, php-format
msgid "%1$sWP Rocket:%2$s %3$s"
msgstr ""

#: inc/Addon/Cloudflare/Subscriber.php:242
#, php-format
msgid "%1$sWP Rocket:%2$s Cloudflare cache successfully purged."
msgstr ""

#: inc/Addon/Cloudflare/Subscriber.php:328
msgctxt "Cloudflare caching level"
msgid "standard"
msgstr ""

#: inc/Addon/Cloudflare/Subscriber.php:403
#, php-format
msgid "Cloudflare browser cache set to %s"
msgstr ""

#: inc/Addon/Cloudflare/Subscriber.php:512
#, php-format
msgid "%1$sWP Rocket:%2$s Optimal settings activated for Cloudflare:"
msgstr ""

#: inc/Addon/Cloudflare/Subscriber.php:521
#, php-format
msgid "%1$sWP Rocket:%2$s Optimal settings deactivated for Cloudflare, reverted to previous settings:"
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:642
msgid "If you have problems after activating this option, copy and paste the default exclusions to quickly resolve issues:"
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:646
#, php-format
msgid "Also, please check our %1$sdocumentation%2$s for a list of compatibility exclusions."
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:651
msgid "Internal scripts are excluded by default to prevent issues. Remove them to take full advantage of this option."
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:654
#, php-format
msgid "If this causes trouble, restore the default exclusions, found %1$shere%2$s"
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:942
msgid "One-click exclusions"
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:943
msgid "When using the Delay JavaScript Execution, you might experience delay loading elements located in the viewport that need to appear immediately - e.g. slider, header, menu."
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:944
msgid "If you need instant visibility, click below on files that should NOT be delayed. This selection will help users interact with the elements straight away."
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:1180
msgid "When you enable preloading WP Rocket will automatically detect your sitemaps and save all URLs to the database. The plugin will make sure that your cache is always preloaded."
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:1234
msgid "Exclude URLs"
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:1239
#, php-format
msgid "Specify URLs to be excluded from the preload feature (one per line). %1$sMore info%2$s"
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:1827
#, php-format
msgid "%1$sPlanning on using Automatic Platform Optimization (APO)?%2$s Just activate the official Cloudflare plugin and configure it. WP Rocket will automatically enable compatibility."
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:2129
msgctxt "Sucuri"
msgid "Firewall API key (for plugin), must be in format {32 characters}/{32 characters}:"
msgstr ""

#: inc/Engine/CDN/RocketCDN/views/cta-big.php:42
#, php-format
msgid "High performance Content Delivery Network (CDN) with %1$sunlimited bandwidth%2$s"
msgstr ""

#: inc/Engine/CriticalPath/CriticalCSSSubscriber.php:840
#, php-format
msgid "We highly recommend the %1$supdated Remove Unused CSS%2$s for a better CSS optimization. Load CSS Asynchronously is always available as a back-up."
msgstr ""

#: inc/Engine/CriticalPath/CriticalCSSSubscriber.php:845
msgid "Stay with the old option"
msgstr ""

#: inc/Engine/License/Renewal.php:76
#, php-format
msgid "Renew before it is too late, you will only pay %1$s%2$s%3$s!"
msgstr ""

#: inc/Engine/License/Renewal.php:85
#, php-format
msgid "Renew with a %1$s%2$s discount%3$s before it is too late, you will only pay %4$s%5$s%6$s!"
msgstr ""

#: inc/Engine/License/Renewal.php:139
#, php-format
msgid "Renew your license for 1 year now at %1$s%3$s%2$s."
msgstr ""

#: inc/Engine/License/Renewal.php:152
#, php-format
msgid "Renew your license for 1 year now and get %1$s%3$s OFF%2$s immediately: you will only pay %1$s%4$s%2$s!"
msgstr ""

#: inc/Engine/License/Renewal.php:218
#, php-format
msgid "Renew before it is too late, you will pay %1$s%3$s%2$s."
msgstr ""

#: inc/Engine/License/views/renewal-expired-banner.php:18
#, php-format
msgid "Your website could be much faster if it could take advantage of our %1$snew features and enhancements%2$s. 🚀"
msgstr ""

#: inc/Engine/Optimization/DelayJS/Admin/SiteList.php:195
msgid "Analytics & Ads"
msgstr ""

#: inc/Engine/Optimization/DelayJS/Admin/SiteList.php:200
msgid "Plugins"
msgstr ""

#: inc/Engine/Optimization/DelayJS/Admin/SiteList.php:205
msgid "Themes"
msgstr ""

#: inc/Engine/Optimization/DynamicLists/DynamicLists.php:101
msgid "You need an active license to get the latest version of the lists from our server."
msgstr ""

#: inc/Engine/Optimization/DynamicLists/DynamicLists.php:115
msgid "Could not get updated lists from server."
msgstr ""

#: inc/Engine/Optimization/DynamicLists/DynamicLists.php:124
msgid "Lists are up to date."
msgstr ""

#: inc/Engine/Optimization/DynamicLists/DynamicLists.php:133
msgid "Could not update lists."
msgstr ""

#: inc/Engine/Optimization/DynamicLists/DynamicLists.php:142
msgid "Lists are successfully updated."
msgstr ""

#: inc/Engine/Optimization/DynamicLists/ServiceProvider.php:52
msgid "Default Lists"
msgstr ""

#: inc/Engine/Optimization/DynamicLists/ServiceProvider.php:58
msgid "Delay JavaScript Execution Exclusion Lists"
msgstr ""

#: inc/Engine/Optimization/RUCSS/Admin/Settings.php:273
#, php-format
msgid "We suggest enabling %1$sPreload%2$s for the fastest results."
msgstr ""

#: inc/Engine/Optimization/RUCSS/Admin/Settings.php:521
#, php-format
msgid "Could not create the %2$s table in the database which is necessary for the Remove Unused CSS feature to work. Please reach out to <a href=\"%3$s\">our support</a>."
msgstr ""

#: inc/Engine/Optimization/RUCSS/Cron/Subscriber.php:174
msgid "WP Rocket clear Remove Unused CSS failed jobs"
msgstr ""

#: inc/Engine/Preload/Admin/Settings.php:57
#, php-format
msgid "%1$s: The preload service is now active. After the initial preload it will continue to cache all your pages whenever they are purged. No further action is needed."
msgstr ""

#: inc/Engine/Preload/Cron/Subscriber.php:138
msgid "WP Rocket Preload pending jobs"
msgstr ""

#: inc/Engine/Preload/Cron/Subscriber.php:166
msgid "WP Rocket Preload revert stuck failed jobs"
msgstr ""

#: inc/ThirdParty/Plugins/CDN/Cloudflare.php:114
#, php-format
msgid "%1$s: Cloudflare's HTTP/2 Server Push is incompatible with the features of Remove Unused CSS and Combine CSS files. We strongly recommend disabling it."
msgstr ""

#: inc/ThirdParty/Plugins/CDN/Cloudflare.php:157
msgid "Your site is using the official Cloudflare plugin. We have enabled Cloudflare auto-purge for compatibility. If you have APO activated, it is also compatible."
msgstr ""

#: inc/ThirdParty/Plugins/CDN/Cloudflare.php:158
msgid "Cloudflare cache will be purged each time WP Rocket clears its cache to ensure content is always up-to-date."
msgstr ""

#: inc/ThirdParty/Plugins/CDN/Cloudflare.php:202
#, php-format
msgid "%1$sWP Rocket:%2$s You are using \"Dynamic Cookies Cache\". Cloudflare APO is not yet compatible with that feature."
msgstr ""

#: inc/ThirdParty/Plugins/CDN/Cloudflare.php:208
#, php-format
msgid "You should either disable Cloudflare APO or check with the theme/plugin requiring the use of “Dynamic Cookies Cache” developers for an alternative way to be page-cache friendly. %1$sMore info%2$s"
msgstr ""

#: inc/ThirdParty/Plugins/CDN/Cloudflare.php:259
#, php-format
msgid "%1$sWP Rocket:%2$s You are using \"Separate cache files for mobile devices\". You need to activate \"Cache by Device Type\" %3$ssetting%5$s on Cloudflare APO to serve the right version of the cache. %4$sMore info%5$s"
msgstr ""

#: inc/ThirdParty/Plugins/CDN/Cloudflare.php:280
#, php-format
msgid "%1$sWP Rocket:%2$s You have \"Cache by Device Type\" enabled on Cloudflare APO. If you judge it necessary for the website to have a different cache on mobile and desktop, we suggest you enable our “Separate Cache Files for Mobiles Devices” to ensure the generated cache is accurate."
msgstr ""

#: inc/ThirdParty/Plugins/Optimization/Perfmatters.php:38
msgid "Remove Unused CSS is currently activated in Perfmatters. If you want to use WP Rocket's Remove Unused CSS feature, disable this option in Perfmatters."
msgstr ""

#: inc/ThirdParty/Plugins/Optimization/RapidLoad.php:39
msgid "Automated unused CSS removal is currently activated in RapidLoad Power-Up for Autoptimize. If you want to use WP Rocket's Remove Unused CSS feature, disable the  RapidLoad Power-Up for Autoptimize plugin."
msgstr ""

#: inc/admin/ui/notices.php:757
msgid "Turn on Remove Unused CSS"
msgstr ""

#: inc/admin/ui/notices.php:763
msgid "Enable “Separate Cache Files for Mobile Devices” now"
msgstr ""

#: inc/common/admin-bar.php:84
#: inc/common/admin-bar.php:136
#: views/settings/page-sections/dashboard.php:150
msgid "Clear and preload cache"
msgstr ""

#: views/deactivation-intent/form.php:68
#, php-format
msgid "and export WP Rocket settings %1$s(Recommended as current settings will be deleted)%2$s"
msgstr ""

#: views/settings/dynamic-lists-update.php:14
msgid "Update Inclusion and Exclusion Lists"
msgstr ""

#: views/settings/dynamic-lists-update.php:19
#, php-format
msgid "Compatibility lists are downloaded automatically every week. Click the button if you want to update them manually. %1$sMore info%2$s"
msgstr ""

#: views/settings/dynamic-lists-update.php:29
msgid "Update lists"
msgstr ""
