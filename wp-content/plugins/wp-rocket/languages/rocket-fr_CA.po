msgid ""
msgstr ""
"Project-Id-Version: WP Rocket 3.12\n"
"Report-Msgid-Bugs-To: http://wp-rocket.me/\n"
"Last-Translator: <PERSON><PERSON><PERSON><PERSON> <j<PERSON><PERSON><PERSON><PERSON>@s2bsolution.com>, 2022\n"
"Language-Team: French (Canada) (https://www.transifex.com/wp-media/teams/18133/fr_CA/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"POT-Creation-Date: 2022-10-18 09:38-0400\n"
"PO-Revision-Date: 2019-08-26 15:14+0000\n"
"Language: fr_CA\n"
"Plural-Forms: nplurals=3; plural=(n == 0 || n == 1) ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"
"X-Generator: Poedit 1.8.11\n"
"X-Poedit-Basepath: ..\n"
"X-Poedit-KeywordsList: __;_e;_x:1,2c;_ex:1,2c;_n:1,2;_nx:1,2,4c;_n_noop:1,2;_nx_noop:1,2,3c;esc_attr__;esc_html__;esc_attr_e;esc_html_e;esc_attr_x:1,2c;esc_html_x:1,2c\n"
"X-Poedit-SearchPath-0: .\n"
"X-Poedit-SearchPathExcluded-0: assets\n"
"X-Poedit-SearchPathExcluded-1: src\n"
"X-Poedit-SearchPathExcluded-2: vendor\n"
"X-Poedit-SearchPathExcluded-3: node_modules\n"
"X-Poedit-SearchPathExcluded-4: tests\n"
"X-Poedit-SearchPathExcluded-5: inc/Dependencies\n"
"X-Poedit-SourceCharset: UTF-8\n"
"X-Textdomain-Support: yes\n"

#: inc/3rd-party/hosting/flywheel.php:18
#: inc/3rd-party/hosting/pressidium.php:18
#: inc/3rd-party/hosting/wp-serveur.php:27
#: inc/ThirdParty/Hostings/Cloudways.php:90
#: inc/ThirdParty/Hostings/Dreampress.php:44
#: inc/ThirdParty/Hostings/Godaddy.php:63
#: inc/ThirdParty/Hostings/O2Switch.php:49
#: inc/ThirdParty/Hostings/OneCom.php:137
#: inc/ThirdParty/Hostings/ProIsp.php:51
#: inc/ThirdParty/Hostings/Savvii.php:50
#: inc/ThirdParty/Hostings/WPEngine.php:47
#: inc/ThirdParty/Hostings/WPXCloud.php:51
#: inc/deprecated/3.6.php:697
#: inc/deprecated/3.6.php:997
#: inc/deprecated/3.9.php:22
#, php-format
msgid "Your site is hosted on %s, we have enabled Varnish auto-purge for compatibility."
msgstr "Votre site est hébergé chez %s, nous avons activé la purge automatisée de Varnish pour la compatibilité."

#: inc/Addon/Cloudflare/API/Client.php:129
msgid "Cloudflare did not provide any reply. Please try again later."
msgstr "Oups, Cloudflare n'a pas répondu. Veuillez essayer de nouveau plus tard."

#: inc/Addon/Cloudflare/API/Client.php:186
#: inc/deprecated/3.5.php:112
#: inc/deprecated/3.5.php:169
msgid "Incorrect Cloudflare email address or API key."
msgstr "Courriel et clé d’API Cloudflare incorrects."

#: inc/Addon/Cloudflare/API/Client.php:190
#: inc/Addon/Cloudflare/API/Client.php:204
#: inc/Addon/Cloudflare/Cloudflare.php:74
#: inc/Addon/Cloudflare/Cloudflare.php:109
#: inc/classes/subscriber/Tools/class-detect-missing-tags-subscriber.php:157
#: inc/deprecated/3.5.php:92
#: inc/deprecated/3.5.php:116
#: inc/deprecated/3.5.php:129
#: inc/deprecated/3.5.php:157
#: inc/deprecated/3.5.php:173
#, php-format
msgid "Read the %1$sdocumentation%2$s for further guidance."
msgstr "Lisez la %1$sdocumentation%2$s pour de l’aide additionnelle."

#: inc/Addon/Cloudflare/API/Client.php:192
#: inc/Addon/Cloudflare/API/Client.php:206
#: inc/Addon/Cloudflare/Auth/APIKey.php:63
#: inc/Addon/Cloudflare/Cloudflare.php:76
#: inc/Addon/Cloudflare/Cloudflare.php:111
#: inc/deprecated/3.5.php:81
#: inc/deprecated/3.5.php:94
#: inc/deprecated/3.5.php:118
#: inc/deprecated/3.5.php:131
#: inc/deprecated/3.5.php:159
#: inc/deprecated/3.5.php:175
#: inc/deprecated/3.5.php:208
msgid "https://docs.wp-rocket.me/article/18-using-wp-rocket-with-cloudflare/?utm_source=wp_plugin&utm_medium=wp_rocket#add-on"
msgstr "https://docs.wp-rocket.me/article/18-using-wp-rocket-with-cloudflare/?utm_source=wp_plugin&utm_medium=wp_rocket#add-on"

#: inc/Addon/Cloudflare/API/Client.php:200
#: inc/deprecated/3.5.php:125
msgid "Incorrect Cloudflare Zone ID."
msgstr "Zone iD de Cloudflare incorrecte."

#: inc/Addon/Cloudflare/Auth/APIKey.php:61
#, php-format
msgid "Cloudflare email and/or API key are not set. Read the %1$sdocumentation%2$s for further guidance."
msgstr "Le courriel ou la clé d'API de Cloudflare ne sont pas configurés. Lire la %1$sdocumentation%2$s pour plus d'information."

#: inc/Addon/Cloudflare/Cloudflare.php:70
#: inc/deprecated/3.5.php:88
msgid "Missing Cloudflare Zone ID."
msgstr "Zone ID de Cloudflare manquant."

#: inc/Addon/Cloudflare/Cloudflare.php:105
#: inc/deprecated/3.5.php:153
msgid "It looks like your domain is not set up on Cloudflare."
msgstr "On dirait que votre domaine n'est pas configuré dans Cloudflare."

#: inc/deprecated/3.5.php:587
#, php-format
msgid "<strong>WP Rocket:</strong> %s"
msgstr "<strong>WP Rocket :</strong> %s"

#: inc/deprecated/3.5.php:592
msgid "<strong>WP Rocket:</strong> Cloudflare cache successfully purged."
msgstr "<strong>WP Rocket:</strong> Cache de Cloudflare purgé avec succès."

#: inc/Addon/Cloudflare/Subscriber.php:632
#: inc/admin/options.php:184
#: inc/classes/subscriber/Tools/class-detect-missing-tags-subscriber.php:148
msgid "WP Rocket: "
msgstr "WP Rocket : "

#: inc/Addon/Cloudflare/Subscriber.php:297
#, php-format
msgid "Cloudflare development mode error: %s"
msgstr "Erreur du mode développement de Cloudflare : %s"

#: inc/Addon/Cloudflare/Subscriber.php:304
#, php-format
msgid "Cloudflare development mode %s"
msgstr "Mode développement de Cloudflare %s"

#: inc/Addon/Cloudflare/Subscriber.php:321
#, php-format
msgid "Cloudflare cache level error: %s"
msgstr "Erreur du niveau de cache de Cloudflare : %s"

#: inc/Addon/Cloudflare/Subscriber.php:334
#, php-format
msgid "Cloudflare cache level set to %s"
msgstr "Niveau de cache de Cloudflare défini à %s"

#: inc/Addon/Cloudflare/Subscriber.php:350
#, php-format
msgid "Cloudflare minification error: %s"
msgstr "Erreur de la minification Cloudflare : %s"

#: inc/Addon/Cloudflare/Subscriber.php:357
#, php-format
msgid "Cloudflare minification %s"
msgstr "Minification Cloudflare %s"

#: inc/Addon/Cloudflare/Subscriber.php:373
#, php-format
msgid "Cloudflare rocket loader error: %s"
msgstr "Erreur du Rocket Loader de Cloudflare : %s"

#: inc/Addon/Cloudflare/Subscriber.php:380
#, php-format
msgid "Cloudflare rocket loader %s"
msgstr "Rocket Loader de Cloudflare %s"

#: inc/Addon/Cloudflare/Subscriber.php:396
#, php-format
msgid "Cloudflare browser cache error: %s"
msgstr "Erreur du cache navigateur Cloudflare : %s"

#: inc/Addon/Sucuri/Subscriber.php:95
#, php-format
msgid "Sucuri cache purge error: %s"
msgstr "Erreur lors de la purge du cache Sucuri : %s"

#: inc/Addon/Sucuri/Subscriber.php:100
msgid "The Sucuri cache is being cleared. Note that it may take up to two minutes for it to be fully flushed."
msgstr "Le cache Sucuri est en cours de purge. Notez que ceci peut prendre jusqu'à deux minutes avant d'être complété."

#: inc/Addon/Sucuri/Subscriber.php:217
msgid "Sucuri firewall API key was not found."
msgstr "La clé d'API du pare-feu Sucuri n'a pu être trouvée."

#: inc/Addon/Sucuri/Subscriber.php:230
msgid "Sucuri firewall API key is invalid."
msgstr "La clé d'API du pare-feu Sucuri est invalide."

#: inc/Addon/Sucuri/Subscriber.php:285
#, php-format
msgid "Error when contacting Sucuri firewall API. Error message was: %s"
msgstr "Erreur lors de la communication avec l'API du pare-feu Sucuri. Le message d'erreur était : %s"

#: inc/Addon/Sucuri/Subscriber.php:300
msgid "Could not get a response from the Sucuri firewall API."
msgstr "Impossible d'obtenir une réponse de l'API du pare-feu Sucuri."

#: inc/Addon/Sucuri/Subscriber.php:315
msgid "Got an invalid response from the Sucuri firewall API."
msgstr "Reçu une réponse invalide de l'API du pare-feu Sucuri."

#: inc/Addon/Sucuri/Subscriber.php:329
msgid "The Sucuri firewall API returned an unknown error."
msgstr "L'API du pare-feu Sucuri a retourné une erreur inconnue."

#: inc/Addon/Sucuri/Subscriber.php:333
#, php-format
msgid "The Sucuri firewall API returned the following error: %s"
msgid_plural "The Sucuri firewall API returned the following errors: %s"
msgstr[0] "L'API du pare-feu Sucuri a retourné l'erreur suivante : %s"
msgstr[1] "L'API du pare-feu Sucuri a retourné les erreurs suivantes : %s"
msgstr[2] "L'API du pare-feu Sucuri a retourné les erreurs suivantes : %s"

#: inc/Engine/Admin/Database/Optimization.php:30
#: inc/Engine/Admin/Settings/Page.php:1475
msgid "Revisions"
msgstr "Révisions"

#: inc/Engine/Admin/Database/Optimization.php:31
#: inc/Engine/Admin/Settings/Page.php:1485
msgid "Auto Drafts"
msgstr "Brouillons automatiques"

#: inc/Engine/Admin/Database/Optimization.php:32
#: inc/Engine/Admin/Settings/Page.php:1495
msgid "Trashed Posts"
msgstr "Contenus dans la corbeille"

#: inc/Engine/Admin/Database/Optimization.php:33
#: inc/Engine/Admin/Settings/Page.php:1505
msgid "Spam Comments"
msgstr "Commentaires indésirables"

#: inc/Engine/Admin/Database/Optimization.php:34
#: inc/Engine/Admin/Settings/Page.php:1515
msgid "Trashed Comments"
msgstr "Commentaires à la corbeille"

#: inc/Engine/Admin/Database/Optimization.php:35
msgid "Transients"
msgstr "Transients"

#: inc/Engine/Admin/Database/Optimization.php:36
msgid "Tables"
msgstr "Tables"

#: inc/Engine/Admin/Database/Subscriber.php:79
#: inc/deprecated/Engine/Addon/FacebookTracking/Subscriber.php:92
#: inc/deprecated/Engine/Addon/GoogleTracking/Subscriber.php:137
msgid "weekly"
msgstr "hebdomadaire"

#: inc/Engine/Admin/Database/Subscriber.php:85
msgid "monthly"
msgstr "mensuel"

#: inc/Engine/Admin/Database/Subscriber.php:194
msgid "Database optimization process is running"
msgstr "L'optimisation de la base de données est en cours"

#: inc/Engine/Admin/Database/Subscriber.php:224
msgid "Database optimization process is complete. Everything was already optimized!"
msgstr "L'optimisation de la base de donnée est terminée. Tout était déjà optimisé!"

#: inc/Engine/Admin/Database/Subscriber.php:227
msgid "Database optimization process is complete. List of optimized items below:"
msgstr "L'optimisation de la base de donnée est terminée. Voici la liste des optimisations effectuées :"

#: inc/Engine/Admin/Database/Subscriber.php:235
#, php-format
msgid "%1$d %2$s optimized."
msgstr "%1$d %2$s optimisé(s)."

#: inc/Engine/Admin/Settings/Page.php:201
msgid "Save Changes"
msgstr "Enregistrer les modifications"

#: inc/Engine/Admin/Settings/Page.php:201
msgid "Validate License"
msgstr "Valider la licence"

#: inc/Engine/Admin/Settings/Page.php:257
#: inc/Engine/Admin/Settings/Page.php:258
msgid "Unavailable"
msgstr "Indisponible"

#: inc/Engine/Admin/Settings/Page.php:352
#: inc/deprecated/deprecated.php:1789
#: views/settings/page-sections/dashboard.php:80
msgid "License"
msgstr "Licence"

#: inc/Engine/Admin/Settings/Page.php:369
msgid "API key"
msgstr "Clé d’API"

#: inc/Engine/Admin/Settings/Page.php:384
msgid "Email address"
msgstr "Adresse courriel"

#: inc/Engine/Admin/Settings/Page.php:410
msgid "Dashboard"
msgstr "Tableau de bord"

#: inc/Engine/Admin/Settings/Page.php:411
msgid "Get help, account info"
msgstr "Aide, info du compte"

#: inc/Engine/Admin/Settings/Page.php:420
msgid "My Status"
msgstr "Ma situation"

#: inc/Engine/Admin/Settings/Page.php:430
#: views/settings/page.php:75
msgid "Rocket Analytics"
msgstr "Analytiques Rocket"

#: inc/Engine/Admin/Settings/Page.php:432
#, php-format
msgid "I agree to share anonymous data with the development team to help improve WP Rocket. %1$sWhat info will we collect?%2$s"
msgstr "J'accepte de partager des données anonymes avec l'équipe de développement pour aider à améliorer WP Rocket. %1$sQuelles informations recueillerons-nous?%2$s"

#: inc/Engine/Admin/Settings/Page.php:456
#: inc/Engine/Cache/WPCache.php:354
msgid "Cache"
msgstr "Cache"

#: inc/Engine/Admin/Settings/Page.php:457
msgid "Basic cache options"
msgstr "Options de base de la cache"

#: inc/Engine/Admin/Settings/Page.php:464
msgid "Mobile Cache"
msgstr "Cache mobile"

#: inc/Engine/Admin/Settings/Page.php:466
msgid "Speed up your site for mobile visitors."
msgstr "Accélérez votre site pour vos visiteurs mobile."

#: inc/Engine/Admin/Settings/Page.php:471
msgid "We detected you use a plugin that requires a separate cache for mobile, and automatically enabled this option for compatibility."
msgstr "Nous avons détecté que vous utilisiez une extension qui nécessite un cache séparé pour mobile, et avons automatiquement activé cette option pour assurer la compatibilité."

#: inc/Engine/Admin/Settings/Page.php:475
msgid "User Cache"
msgstr "Cache utilisateur"

#: inc/Engine/Admin/Settings/Page.php:478
#, php-format
msgid "%1$sUser cache%2$s is great when you have user-specific or restricted content on your website."
msgstr "%1$sLe cache utilisateur%2$s est parfait si vous avez du contenu spécifique ou restreint pour vos utilisateurs sur votre site."

#: inc/Engine/Admin/Settings/Page.php:486
msgid "Cache Lifespan"
msgstr "Délai de nettoyage du cache"

#: inc/Engine/Admin/Settings/Page.php:489
#, php-format
msgid "Cache files older than the specified lifespan will be deleted.<br>Enable %1$spreloading%2$s for the cache to be rebuilt automatically after lifespan expiration."
msgstr "Les fichiers en cache plus vieux que la durée de vie spécifiée seront effacés.<br>Activer %1$spré-chargement %2$s pour que le cache soit généré de nouveau automatiquement après l'expiration de la durée de vie."

#: inc/Engine/Admin/Settings/Page.php:503
msgid "Enable caching for logged-in WordPress users"
msgstr "Activer la mise en cache pour les utilisateurs WordPress connectés"

#: inc/Engine/Admin/Settings/Page.php:511
msgid "Enable caching for mobile devices"
msgstr "Activer la mise en cache pour les appareils mobiles"

#: inc/Engine/Admin/Settings/Page.php:526
msgid "Separate cache files for mobile devices"
msgstr "Créer un fichier de cache distinct pour les appareils mobiles"

#: inc/Engine/Admin/Settings/Page.php:528
#, php-format
msgid "Most modern themes are responsive and should work without a separate cache. Enable this only if you have a dedicated mobile theme or plugin. %1$sMore info%2$s"
msgstr "La majorité des thèmes sont responsives et devraient fonctionner sans cache séparée. Activer ceci seulement si vous avez un thème mobile dédié ou une extension. %1$sPlus d'info%2$s"

#: inc/Engine/Admin/Settings/Page.php:544
msgid "Specify time after which the global cache is cleared<br>(0 = unlimited )"
msgstr "Indiquez le nombre d’heures après quoi le cache global doit être vidé<br>(0 = illimité)"

#: inc/Engine/Admin/Settings/Page.php:546
#, php-format
msgid "Reduce lifespan to 10 hours or less if you notice issues that seem to appear periodically. %1$sWhy?%2$s"
msgstr "Réduisez le délai à 10 heures ou moins si vous remarquez des problèmes apparaissant de façon intermittente. %1$sPourquoi ?%2$s"

#: inc/Engine/Admin/Settings/Page.php:552
#: inc/Engine/License/views/promo-banner.php:30
#: inc/Engine/License/views/renewal-soon-banner.php:13
msgid "Hours"
msgstr "Heures"

#: inc/Engine/Admin/Settings/Page.php:553
#: inc/Engine/License/views/promo-banner.php:29
#: inc/Engine/License/views/renewal-soon-banner.php:12
msgid "Days"
msgstr "Jours"

#: inc/Engine/Admin/Settings/Page.php:602
msgid "File Optimization"
msgstr "Optimisation des fichiers"

#: inc/Engine/Admin/Settings/Page.php:603
msgid "Optimize CSS & JS"
msgstr "Optimiser le CSS et le JS"

#: inc/Engine/Admin/Settings/Page.php:621
msgid "CSS Files"
msgstr "Fichiers CSS"

#: inc/Engine/Admin/Settings/Page.php:611
#: inc/Engine/Admin/Settings/Page.php:637
#, php-format
msgid "%1$s Minification is currently activated in <strong>Autoptimize</strong>. If you want to use %2$s’s minification, disable those options in Autoptimize."
msgstr "La minification %1$s est actuellement activée dans <strong>Autoptimize</strong>. Si vous souhaitez utiliser la minification de %2$s, désactivez ces options dans Autoptimize."

#: inc/Engine/Admin/Settings/Page.php:630
msgid "JavaScript Files"
msgstr "Fichiers JavaScript"

#: inc/Engine/Admin/Settings/Page.php:663
msgid "Minify CSS files"
msgstr "Minifier les fichiers CSS"

#: inc/Engine/Admin/Settings/Page.php:664
msgid "Minify CSS removes whitespace and comments to reduce the file size."
msgstr "Minifier le CSS supprime les espaces et les commentaires afin de réduire le poids des fichiers."

#: inc/Engine/Admin/Settings/Page.php:677
#: inc/Engine/Admin/Settings/Page.php:701
#: inc/Engine/Admin/Settings/Page.php:765
#: inc/Engine/Admin/Settings/Page.php:831
#: inc/Engine/Admin/Settings/Page.php:855
msgid "This could break things!"
msgstr "Ceci pourrait briser des choses!"

#: inc/Engine/Admin/Settings/Page.php:678
#: inc/Engine/Admin/Settings/Page.php:702
#: inc/Engine/Admin/Settings/Page.php:766
#: inc/Engine/Admin/Settings/Page.php:832
#: inc/Engine/Admin/Settings/Page.php:856
msgid "If you notice any errors on your website after having activated this setting, just deactivate it again, and your site will be back to normal."
msgstr "Si vous remarquez des erreurs sur votre site après avoir activé cette option, il vous suffit de la désactiver et votre site sera de retour à la normale."

#: inc/Engine/Admin/Settings/Page.php:679
msgid "Activate minify CSS"
msgstr "Activer la minification CSS"

#: inc/Engine/Admin/Settings/Page.php:684
msgid "Combine CSS files <em>(Enable Minify CSS files to select)</em>"
msgstr "Combiner les fichiers CSS <em>(activez la minification CSS pour sélectionner)</em>"

#: inc/Engine/Admin/Settings/Page.php:686
#, php-format
msgid "Combine CSS merges all your files into 1, reducing HTTP requests. Not recommended if your site uses HTTP/2. %1$sMore info%2$s"
msgstr "Combiner le CSS condense tous vos fichiers en un seul, réduisant le nombre de requêtes HTTP. Ces réglages ne sont pas recommandés si votre site utilise HTTP/2. %1$sPlus d'infos%2$s"

#: inc/Engine/Admin/Settings/Page.php:687
msgid "For compatibility and best results, this option is disabled when Remove unused CSS is enabled."
msgstr "Pour des raisons de compatibilité et pour obtenir les meilleurs résultats, cette option est désactivée lorsque l'option Supprimer les ressources CSS inutilisées est activée."

#: inc/Engine/Admin/Settings/Page.php:703
msgid "Activate combine CSS"
msgstr "Activer la combinaison CSS"

#: inc/Engine/Admin/Settings/Page.php:708
#: inc/admin/options.php:124
msgid "Excluded CSS Files"
msgstr "Fichiers CSS à exclure"

#: inc/Engine/Admin/Settings/Page.php:709
msgid "Specify URLs of CSS files to be excluded from minification and concatenation (one per line)."
msgstr "Indiquez l’URL des fichiers CSS à exclure de la minification et de la concaténation (une par ligne)"

#: inc/Engine/Admin/Settings/Page.php:710
msgid "<strong>Internal:</strong> The domain part of the URL will be stripped automatically. Use (.*).css wildcards to exclude all CSS files located at a specific path."
msgstr "<strong>Interne :</strong> Le nom de domaine sera supprimé automatiquement de l'URL. Utilisez les expressions régulières  (.*).css afin d'exclure tous les fichiers CSS pour un chemin donné."

#: inc/Engine/Admin/Settings/Page.php:712
#, php-format
msgid "<strong>3rd Party:</strong> Use either the full URL path or only the domain name, to exclude external CSS. %1$sMore info%2$s"
msgstr "<strong>Tierce-partie :</strong> Utilisez soit l'URL complet ou seulement le nom de domaine afin d'exclure le CSS externe.. %1$sPlus d'info%2$s"

#: inc/Engine/Admin/Settings/Page.php:725
#: inc/Engine/Admin/Settings/Page.php:747
msgid "Optimize CSS delivery"
msgstr "Optimiser le chargement du CSS"

#: inc/Engine/Admin/Settings/Page.php:730
msgid "Optimize CSS delivery eliminates render-blocking CSS on your website. Only one method can be selected. Remove Unused CSS is recommended for optimal performance, but limited only to the users with active license."
msgstr "L'Optimisation du Chargement CSS élimine les CSS bloquant le rendu de votre site Web. Une seule méthode peut être sélectionnée. La méthode Supprimer les CSS inutilisés est recommandée pour des performances optimales."

#: inc/Engine/Admin/Settings/Page.php:730
msgid "Optimize CSS delivery eliminates render-blocking CSS on your website. Only one method can be selected. Remove Unused CSS is recommended for optimal performance."
msgstr "L'Optimisation du Chargement CSS élimine les CSS bloquant le rendu sur votre site Web. Une seule méthode peut être sélectionnée. La méthode Supprimer les CSS inutilisés est recommandée pour des performances optimales."

#: inc/Engine/Admin/Settings/Page.php:740
#, php-format
msgid "Optimize CSS Delivery features are disabled on local environments. %1$sLearn more%2$s"
msgstr "Les options Optimiser le chargement CSS sont désactivées en local. %1$sEn savoir plus%2$s"

#: inc/Engine/Admin/Settings/Page.php:760
#: inc/admin/ui/meta-boxes.php:106
msgid "Remove Unused CSS"
msgstr "Retirer CSS inutilisé"

#: inc/Engine/Admin/Settings/Page.php:763
#, php-format
msgid "Removes unused CSS per page and helps to reduce page size and HTTP requests. Recommended for best performance. Test thoroughly! %1$sMore info%2$s"
msgstr "Supprime le CSS inutilisé par page et contribue à réduire la taille des pages et les requêtes HTTP. Recommandé pour de meilleures performances. Testez-le rigoureusement ! %1$sPlus d'infos%2$s"

#: inc/Engine/Admin/Settings/Page.php:767
msgid "Activate Remove Unused CSS"
msgstr "Activer le retrait du CSS inutilisé"

#: inc/Engine/Admin/Settings/Page.php:773
msgid "CSS safelist"
msgstr "Liste sécuritaire CSS"

#: inc/Engine/Admin/Settings/Page.php:774
msgid "Specify CSS filenames, IDs or classes that should not be removed (one per line)."
msgstr "Spécifiez les fichiers CSS, les IDs ou les classes qui ne devraient pas être retirés (un par ligne)."

#: inc/Engine/Admin/Settings/Page.php:789
#: inc/admin/ui/meta-boxes.php:109
msgid "Load CSS asynchronously"
msgstr "Chargement asynchrone du CSS"

#: inc/Engine/Admin/Settings/Page.php:792
#, php-format
msgctxt "WP Critical CSS compatibility"
msgid "Load CSS asynchronously is currently handled by the %1$s plugin. If you want to use WP Rocket’s load CSS asynchronously option, disable the %1$s plugin."
msgstr "Le Chargement asynchrone du CSS est actuellement géré par le plugin %1$s. Si vous souhaitez utiliser l'option de chargement asynchrone du CSS de WP Rocket, désactivez le plugin %1$s."

#: inc/Engine/Admin/Settings/Page.php:794
#, php-format
msgid "Generates critical path CSS and loads CSS asynchronously. %1$sMore info%2$s"
msgstr "Génère le Critical Path CSS et charge les CSS de manière asynchrone. %1$sPlus d'infos%2$s"

#: inc/Engine/Admin/Settings/Page.php:800
msgid "Fallback critical CSS"
msgstr "CSS critique de secours"

#: inc/Engine/Admin/Settings/Page.php:802
#, php-format
msgid "Provides a fallback if auto-generated critical path CSS is incomplete. %1$sMore info%2$s"
msgstr "Fournit une option de secours si le CSS critique auto-généré est incomplet.%1$sPlus d'infos%2$s"

#: inc/Engine/Admin/Settings/Page.php:817
msgid "Minify JavaScript files"
msgstr "Minifier les fichiers JS"

#: inc/Engine/Admin/Settings/Page.php:818
msgid "Minify JavaScript removes whitespace and comments to reduce the file size."
msgstr "Minifier le JavaScript supprime les espace et les commentaires afin de réduire le poids des fichiers."

#: inc/Engine/Admin/Settings/Page.php:833
msgid "Activate minify JavaScript"
msgstr "Activer la minification JavaScript"

#: inc/Engine/Admin/Settings/Page.php:838
msgid "Combine JavaScript files <em>(Enable Minify JavaScript files to select)</em>"
msgstr "Combiner les fichiers JavaScript <em>(activez la minification JavaScript pour sélectionner)</em>"

#: inc/Engine/Admin/Settings/Page.php:840
#, php-format
msgid "Combine JavaScript files combines your site’s internal, 3rd party and inline JS reducing HTTP requests. Not recommended if your site uses HTTP/2. %1$sMore info%2$s"
msgstr "Combine vos fichiers JavaScript, les fichiers tiers et le inline JS en un seul fichier, réduisant le nombre de requêtes HTTP. Ces réglages ne sont pas recommandés si votre site utilise HTTP/2.%1$sPlus d'infos%2$s"

#: inc/Engine/Admin/Settings/Page.php:841
msgid "For compatibility and best results, this option is disabled when delay javascript execution is enabled."
msgstr "Pour assurer la compatibilité et obtenir de meilleurs résultats, cette option est désactivée lorsque le délai du javascript est activé."

#: inc/Engine/Admin/Settings/Page.php:857
msgid "Activate combine JavaScript"
msgstr "Activer la combinaison JavaScript"

#: inc/Engine/Admin/Settings/Page.php:862
#: inc/admin/options.php:125
msgid "Excluded Inline JavaScript"
msgstr "JavaScript inline exclu"

#: inc/Engine/Admin/Settings/Page.php:864
#, php-format
msgid "Specify patterns of inline JavaScript to be excluded from concatenation (one per line). %1$sMore info%2$s"
msgstr "Indiquez les patterns du code JavaScript inline à exclure de la concaténation (un par ligne).%1$sMore info%2$s"

#: inc/Engine/Admin/Settings/Page.php:880
#: inc/Engine/Admin/Settings/Page.php:913
#: inc/Engine/Admin/Settings/Page.php:960
#: inc/admin/options.php:126
msgid "Excluded JavaScript Files"
msgstr "Fichiers JavaScript exclus"

#: inc/Engine/Admin/Settings/Page.php:881
msgid "Specify URLs of JavaScript files to be excluded from minification and concatenation (one per line)."
msgstr "Indiquez l’URL des fichiers JavaScript à exclure de la minification et de la concaténation (une par ligne)"

#: inc/Engine/Admin/Settings/Page.php:882
msgid "<strong>Internal:</strong> The domain part of the URL will be stripped automatically. Use (.*).js wildcards to exclude all JS files located at a specific path."
msgstr "<strong>Interne :</strong>Le nom de domaine sera supprimé automatiquement de l’URL. Utilisez les expressions régulières (.*).js afin d’exclure tous les fichiers JS pour un chemin donné."

#: inc/Engine/Admin/Settings/Page.php:884
#, php-format
msgid "<strong>3rd Party:</strong> Use either the full URL path or only the domain name, to exclude external JS. %1$sMore info%2$s"
msgstr "<strong>Tierce-partie :</strong> Utilisez soit l'URL complet ou seulement le nom de domaine afin d'exclure le JS externe. %1$sPlus d'info%2$s"

#: inc/Engine/Admin/Settings/Page.php:900
msgid "Load JavaScript deferred"
msgstr "Charger les fichiers JavaScript en différé"

#: inc/Engine/Admin/Settings/Page.php:902
#, php-format
msgid "Load JavaScript deferred eliminates render-blocking JS on your site and can improve load time. %1$sMore info%2$s"
msgstr "Charger les fichiers JavaScript en différé élimine le blocage de l’affichage de votre site dû au JS et peux améliorer le temps de chargement perçu. %1$sPlus d'infos%2$s"

#: inc/Engine/Admin/Settings/Page.php:915
#, php-format
msgid "Specify URLs or keywords of JavaScript files to be excluded from defer (one per line). %1$sMore info%2$s"
msgstr "Spécifiez les URLs ou mots-clés des fichiers JavaScript à être exclus du chargement différé (un par ligne). %1$sPlus d'info%2$s"

#: inc/Engine/Admin/Settings/Page.php:931
#: inc/admin/ui/meta-boxes.php:111
msgid "Delay JavaScript execution"
msgstr "Retarder l'exécution du JavaScript"

#: inc/Engine/Admin/Settings/Page.php:933
#, php-format
msgid "Improves performance by delaying the loading of JavaScript files until user interaction (e.g. scroll, click). %1$sMore info%2$s"
msgstr "Améliore les performances en retardant le chargement des fichiers JavaScript jusqu'à une interaction de l'utilisateur (ie. défiler, cliquer). %1$sPlus d'info%2$s"

#: inc/Engine/Admin/Settings/Page.php:961
msgid "Specify URLs or keywords that can identify inline or JavaScript files to be excluded from delaying execution (one per line)."
msgstr "Spécifiez les URLs ou mots-clés qui peuvent identifier le JavaScript en ligne ou  en fichiers à être exclus de l'exécution retardée (un par ligne)."

#: inc/Engine/Admin/Settings/Page.php:993
msgid "Media"
msgstr "Média"

#: inc/Engine/Admin/Settings/Page.php:994
msgid "LazyLoad, image dimensions"
msgstr "LazyLoad, dimensions des images"

#: inc/Engine/Admin/Settings/Page.php:1003
msgid "Autoptimize"
msgstr "Autoptimize"

#: inc/Engine/Admin/Settings/Page.php:1048
msgid "LazyLoad"
msgstr "Chargement différé"

#: inc/Engine/Admin/Settings/Page.php:1051
#, php-format
msgid "It can improve actual and perceived loading time as images, iframes, and videos will be loaded only as they enter (or about to enter) the viewport and reduces the number of HTTP requests. %1$sMore Info%2$s"
msgstr "Peut améliorer le temps de chargement réel et perçu car les images, iframes et vidéos ne seront chargées que lorsqu'elles entreront (ou seront sur le point d'entrer) dans la zone visible. Réduit le nombre de requêtes HTTP. %1$sPlus d'informations.%2$s"

#: inc/Engine/Admin/Settings/Page.php:1058
#, php-format
msgid "LazyLoad is currently activated in %2$s. If you want to use WP Rocket’s LazyLoad, disable this option in %2$s."
msgstr "LazyLoad est présentement activé dans %2$s. Si vous désirez utiliser le LazyLoad de WP Rocket, désactivez cette option dans %2$s."

#: inc/Engine/Admin/Settings/Page.php:1061
msgid "Image Dimensions"
msgstr "Dimensions de l'image"

#: inc/Engine/Admin/Settings/Page.php:1064
#, php-format
msgid "Add missing width and height attributes to images. Helps prevent layout shifts and improve the reading experience for your visitors. %1$sMore info%2$s"
msgstr "Ajoutez les attributs manquants de hauteur et largeur aux images. Ceci aide à prévenir les sauts de mise en page et améliorer l'expérience de lecture pour vos visiteurs. %1$sPlus d'info%2$s"

#: inc/Engine/Admin/Settings/Page.php:1083
msgid "Enable for images"
msgstr "Activer pour les images"

#: inc/Engine/Admin/Settings/Page.php:1095
#, php-format
msgid "LazyLoad for images is currently activated in %2$s. If you want to use %1$s’s LazyLoad, disable this option in %2$s."
msgstr "LazyLoad pour les images est présentement activé dans %2$s. Si vous désirez utiliser le LazyLoad de %1$s, désactivez cette option dans %2$s."

#: inc/Engine/Admin/Settings/Page.php:1103
msgid "Enable for iframes and videos"
msgstr "Activer pour les iframes et vidéos"

#: inc/Engine/Admin/Settings/Page.php:1118
msgid "Replace YouTube iframe with preview image"
msgstr "Remplacer l'iframe YouTube par une image d'aperçu"

#: inc/Engine/Admin/Settings/Page.php:1120
#, php-format
msgid "Replace YouTube iframe with preview image is not compatible with %2$s."
msgstr "Le remplacement du iFrame YouTube par une image d'aperçu n'est pas compatible avec %2$s."

#: inc/Engine/Admin/Settings/Page.php:1120
msgid "This can significantly improve your loading time if you have a lot of YouTube videos on a page."
msgstr "Ceci peut considérablement améliorer votre vitesse de chargement si vous avez beaucoup de vidéos YouTube sur une page."

#: inc/Engine/Admin/Settings/Page.php:1135
msgid "Excluded images or iframes"
msgstr "Exclure les images ou iFrames"

#: inc/Engine/Admin/Settings/Page.php:1137
#, php-format
msgid "Specify keywords (e.g. image filename, CSS class, domain) from the image or iframe code to be excluded (one per line). %1$sMore info%2$s"
msgstr "Spécifiez les mots-clés (ie. nom de fichier d'image, classe CSS, nom de domaine) de l'image ou du code iFrame à être exclu (un par ligne). %1$sPlus d'info%2$s"

#: inc/Engine/Admin/Settings/Page.php:1145
msgid "Add missing image dimensions"
msgstr "Ajouter les dimensions manquantes de l'image"

#: inc/Engine/Admin/Settings/Page.php:1164
#: inc/deprecated/deprecated.php:1776
msgid "Preload"
msgstr "Préchargement"

#: inc/Engine/Admin/Settings/Page.php:1165
msgid "Generate cache files, preload fonts"
msgstr "Générer les fichiers de cache, précharger les fontes"

#: inc/Engine/Admin/Settings/Page.php:1177
msgid "Preload Cache"
msgstr "Précharger la cache"

#: inc/Engine/Admin/Settings/Page.php:1180
msgid "When you enable preloading WP Rocket will automatically detect your sitemaps and save all URLs to the database. The plugin will make sure that your cache is always preloaded."
msgstr "Lorsque vous activez le préchargement, WP Rocket détecte automatiquement vos sitemaps et enregistre toutes les URLs dans la base de données. L'extension s'assurera que votre cache est toujours préchargé."

#: inc/Engine/Admin/Settings/Page.php:1188
msgid "Preload Links"
msgstr "Précharger les liens"

#: inc/Engine/Admin/Settings/Page.php:1191
#, php-format
msgid "Link preloading improves the perceived load time by downloading a page when a user hovers over the link. %1$sMore info%2$s"
msgstr "Le pré-chargement de liens améliore la perception du temps de chargement du site en téléchargeant une page lorsque l'usager survole un hyperlien. %1$sPlus d'info%2$s"

#: inc/Engine/Admin/Settings/Page.php:1199
msgid "Prefetch DNS Requests"
msgstr "Préchargement des requêtes DNS"

#: inc/Engine/Admin/Settings/Page.php:1201
msgid "DNS prefetching can make external files load faster, especially on mobile networks"
msgstr "Le préchargement des requêtes DNS peut permettre aux ressources externes de charger plus rapidement, surtout sur les réseaux mobiles."

#: inc/Engine/Admin/Settings/Page.php:1206
msgid "Preload Fonts"
msgstr "Précharger les fontes"

#: inc/Engine/Admin/Settings/Page.php:1209
#, php-format
msgid "Improves performance by helping browsers discover fonts in CSS files. %1$sMore info%2$s"
msgstr "Améliore les performances en aidant les navigateurs à découvrir les fontes dans les ficheirs CSS. %1$sPlus d'info%2$s"

#: inc/Engine/Admin/Settings/Page.php:1223
msgid "Activate Preloading"
msgstr "Activer le pré-chargement"

#: inc/Engine/Admin/Settings/Page.php:1234
msgid "Exclude URLs"
msgstr "URLs à exclure"

#: inc/Engine/Admin/Settings/Page.php:1239
#, php-format
msgid "Specify URLs to be excluded from the preload feature (one per line). %1$sMore info%2$s"
msgstr "Indiquez les URL à exclure du préchargement (une par ligne). %1$sPlus d'infos%2$s"

#: inc/Engine/Admin/Settings/Page.php:1250
msgid "URLs to prefetch"
msgstr "URLs à précharger"

#: inc/Engine/Admin/Settings/Page.php:1251
msgid "Specify external hosts to be prefetched (no <code>http:</code>, one per line)"
msgstr "Indiquez les hôtes externes à précharger (sans <code>http:</code>, un par ligne)"

#: inc/Engine/Admin/Settings/Page.php:1260
msgid "Fonts to preload"
msgstr "Fontes à précharger"

#: inc/Engine/Admin/Settings/Page.php:1261
msgid "Specify urls of the font files to be preloaded (one per line). Fonts must be hosted on your own domain, or the domain you have specified on the CDN tab."
msgstr "Spécifiez les URLs des fichiers de fontes à être pré-chargées (un par ligne). Les fontes doivent être hébergées sur votre propre domaine, ou le domaine spécifié dans l'onglet CDN."

#: inc/Engine/Admin/Settings/Page.php:1262
msgid "The domain part of the URL will be stripped automatically.<br/>Allowed font extensions: otf, ttf, svg, woff, woff2."
msgstr "La portion domaine de l'URL sera retirée automatiquement.<br/>Extensions de fontes permises : otf, ttf, svg, woff, woff2."

#: inc/Engine/Admin/Settings/Page.php:1271
msgid "Enable link preloading"
msgstr "Activer le préchargement des liens"

#: inc/Engine/Admin/Settings/Page.php:1290
msgid "Advanced Rules"
msgstr "Règles avancées"

#: inc/Engine/Admin/Settings/Page.php:1291
msgid "Fine-tune cache rules"
msgstr "Affiner les règles du cache"

#: inc/Engine/Admin/Settings/Page.php:1302
msgid "Sensitive pages like custom login/logout URLs should be excluded from cache."
msgstr "Les pages sensibles telles que les URLs de connexion / déconnexion personnalisés doivent être exclus du cache."

#: inc/Engine/Admin/Settings/Page.php:1305
msgctxt "plugin name"
msgid "WooCommerce"
msgstr "WooCommerce"

#: inc/Engine/Admin/Settings/Page.php:1307
msgctxt "plugin name"
msgid "Easy Digital Downloads"
msgstr "Easy Digital Downloads"

#: inc/Engine/Admin/Settings/Page.php:1309
msgctxt "plugin name"
msgid "iThemes Exchange"
msgstr "iThemes Exchange"

#: inc/Engine/Admin/Settings/Page.php:1311
msgctxt "plugin name"
msgid "Jigoshop"
msgstr "Jigoshop"

#: inc/Engine/Admin/Settings/Page.php:1313
msgctxt "plugin name"
msgid "WP-Shop"
msgstr "WP-Shop"

#: inc/Engine/Admin/Settings/Page.php:1319
#, php-format
msgid "<br>Cart, checkout and \"my account\" pages set in <strong>%1$s%2$s%3$s</strong> will be detected and never cached by default."
msgstr "<br>Les pages panier, commande et \"mon compte\" réglées dans <strong>%1$s%2$s%3$s</strong> seront automatiquement détectées et exclues du cache par défaut."

#: inc/Engine/Admin/Settings/Page.php:1329
#: inc/admin/options.php:129
msgid "Never Cache URL(s)"
msgstr "Ne jamais mettre en cache ces URL(s)"

#: inc/Engine/Admin/Settings/Page.php:1337
msgid "Never Cache Cookies"
msgstr "Ne jamais mettre en cache ces témoins"

#: inc/Engine/Admin/Settings/Page.php:1343
#: inc/admin/options.php:130
msgid "Never Cache User Agent(s)"
msgstr "Ne jamais mettre en cache ces agents utilisateurs"

#: inc/Engine/Admin/Settings/Page.php:1349
#: inc/admin/options.php:131
msgid "Always Purge URL(s)"
msgstr "Toujours purger ces URL(s)"

#: inc/Engine/Admin/Settings/Page.php:1355
msgid "Cache Query String(s)"
msgstr "Cacher les “Query String(s)”"

#: inc/Engine/Admin/Settings/Page.php:1358
#, php-format
msgid "%1$sCache for query strings%2$s enables you to force caching for specific GET parameters."
msgstr "%1$sCacher les “Query Strings”%2$s vous permet de forcer la mise en cache de paramètres GET spécifiques."

#: inc/Engine/Admin/Settings/Page.php:1369
msgid "Specify URLs of pages or posts that should never be cached (one per line)"
msgstr "Indiquez les URLs des pages ou articles qui doivent être exclus de la mise en cache (un par ligne)"

#: inc/Engine/Admin/Settings/Page.php:1370
#: inc/Engine/Admin/Settings/Page.php:1398
msgid "The domain part of the URL will be stripped automatically.<br>Use (.*) wildcards to address multiple URLs under a given path."
msgstr "Le nom de domaine sera supprimé automatiquement de l'URL .<br>Utilisez les expressions régulières (.*) pour exclure plusieurs URLs pour un chemin donné."

#: inc/Engine/Admin/Settings/Page.php:1379
msgid "Specify full or partial IDs of cookies that, when set in the visitor's browser, should prevent a page from getting cached (one per line)"
msgstr "Spécifiez les IDs complets ou partiels de témoins qui, lorsque définis dans le navigateur du visiteur, devraient prévenir une page d'être mise en cache (un par ligne)."

#: inc/Engine/Admin/Settings/Page.php:1387
msgid "Specify user agent strings that should never see cached pages (one per line)"
msgstr "Indiquez les chaînes des agents utilisateurs qui ne devraient jamais voir les pages mises en cache (une par ligne)"

#: inc/Engine/Admin/Settings/Page.php:1388
msgid "Use (.*) wildcards to detect parts of UA strings."
msgstr "Utilisez les expressions régulières (.*) pour détecter les parties des chaînes des agents utilisateurs."

#: inc/Engine/Admin/Settings/Page.php:1397
msgid "Specify URLs you always want purged from cache whenever you update any post or page (one per line)"
msgstr "Indiquez les URLs dont vous voulez systématiquement vider le cache lorsque vous mettez à jour n'importe quel article ou page (une par ligne)"

#: inc/Engine/Admin/Settings/Page.php:1406
msgid "Specify query strings for caching (one per line)"
msgstr "Indiquez les “query strings” qui peuvent être mises en cache (une par ligne)"

#: inc/Engine/Admin/Settings/Page.php:1431
#: inc/deprecated/deprecated.php:1775
msgid "Database"
msgstr "Base de données"

#: inc/Engine/Admin/Settings/Page.php:1432
msgid "Optimize, reduce bloat"
msgstr "Optimiser & nettoyer"

#: inc/Engine/Admin/Settings/Page.php:1439
msgid "Post Cleanup"
msgstr "Nettoyage des contenus"

#: inc/Engine/Admin/Settings/Page.php:1441
msgid "Post revisions and drafts will be permanently deleted. Do not use this option if you need to retain revisions or drafts."
msgstr "Les révisions et les brouillons seront supprimés définitivement. N'utilisez pas cette option si vous devez conserver vos révisions et brouillons."

#: inc/Engine/Admin/Settings/Page.php:1446
msgid "Comments Cleanup"
msgstr "Nettoyage des commentaires"

#: inc/Engine/Admin/Settings/Page.php:1448
msgid "Spam and trashed comments will be permanently deleted."
msgstr "Les Spams et les commentaires mis à la corbeille seront supprimés"

#: inc/Engine/Admin/Settings/Page.php:1452
msgid "Transients Cleanup"
msgstr "Nettoyage des transients"

#: inc/Engine/Admin/Settings/Page.php:1454
msgid "Transients are temporary options; they are safe to remove. They will be automatically regenerated as your plugins require them."
msgstr "Les “transients” sont des options temporaires, leur suppression est sans danger. Elles seront automatiquement régénérées si vos extensions en ont besoin."

#: inc/Engine/Admin/Settings/Page.php:1458
msgid "Database Cleanup"
msgstr "Nettoyage de la base de données"

#: inc/Engine/Admin/Settings/Page.php:1460
msgid "Reduces overhead of database tables"
msgstr "Récupère l’espace inutilisé des tables de la base de données"

#: inc/Engine/Admin/Settings/Page.php:1464
msgid "Automatic Cleanup"
msgstr "Nettoyage automatique"

#: inc/Engine/Admin/Settings/Page.php:1477
#, php-format
msgid "%s revision in your database."
msgid_plural "%s revisions in your database."
msgstr[0] "%s révision dans votre base de données."
msgstr[1] "%s révisions dans votre base de données."
msgstr[2] "%s révisions dans votre base de données."

#: inc/Engine/Admin/Settings/Page.php:1487
#, php-format
msgid "%s draft in your database."
msgid_plural "%s drafts in your database."
msgstr[0] "%s brouillon dans votre base de données."
msgstr[1] "%s brouillons dans votre base de données."
msgstr[2] "%s brouillons dans votre base de données."

#: inc/Engine/Admin/Settings/Page.php:1497
#, php-format
msgid "%s trashed post in your database."
msgid_plural "%s trashed posts in your database."
msgstr[0] "%s articles à la corbeille dans votre base de données."
msgstr[1] "%sarticles à la corbeille dans votre base de données."
msgstr[2] "%sarticles à la corbeille dans votre base de données."

#: inc/Engine/Admin/Settings/Page.php:1507
#, php-format
msgid "%s spam comment in your database."
msgid_plural "%s spam comments in your database."
msgstr[0] "%s commentaire de SPAM dans votre base de données."
msgstr[1] "%s commentaires de SPAM dans votre base de données."
msgstr[2] "%s commentaires de SPAM dans votre base de données."

#: inc/Engine/Admin/Settings/Page.php:1517
#, php-format
msgid "%s trashed comment in your database."
msgid_plural "%s trashed comments in your database."
msgstr[0] "%s commentaire à la corbeille dans votre base de données."
msgstr[1] "%s commentaires à la corbeille dans votre base de données."
msgstr[2] "%s commentaires à la corbeille dans votre base de données."

#: inc/Engine/Admin/Settings/Page.php:1525
msgid "All transients"
msgstr "Tous les transients"

#: inc/Engine/Admin/Settings/Page.php:1527
#, php-format
msgid "%s transient in your database."
msgid_plural "%s transients in your database."
msgstr[0] "%s transient dans votre base de données."
msgstr[1] "%s transients dans votre base de données."
msgstr[2] "%s transients dans votre base de données."

#: inc/Engine/Admin/Settings/Page.php:1535
msgid "Optimize Tables"
msgstr "Optimiser les tables"

#: inc/Engine/Admin/Settings/Page.php:1537
#, php-format
msgid "%s table to optimize in your database."
msgid_plural "%s tables to optimize in your database."
msgstr[0] "%s table à optimiser dans votre base de données."
msgstr[1] "%s tables à optimiser dans votre base de données."
msgstr[2] "%s tables à optimiser dans votre base de données."

#: inc/Engine/Admin/Settings/Page.php:1548
msgid "Schedule Automatic Cleanup"
msgstr "Planifier le nettoyage automatique"

#: inc/Engine/Admin/Settings/Page.php:1560
msgid "Frequency"
msgstr "Fréquence"

#: inc/Engine/Admin/Settings/Page.php:1568
msgid "Daily"
msgstr "Quotidien"

#: inc/Engine/Admin/Settings/Page.php:1569
msgid "Weekly"
msgstr "Hebdomadaire"

#: inc/Engine/Admin/Settings/Page.php:1570
msgid "Monthly"
msgstr "Mensuel"

#: inc/Engine/Admin/Settings/Page.php:1586
#: inc/Engine/Admin/Settings/Page.php:1597
#: inc/admin/ui/meta-boxes.php:108
#: inc/deprecated/deprecated.php:1773
msgid "CDN"
msgstr "CDN"

#: inc/Engine/Admin/Settings/Page.php:1587
msgid "Integrate your CDN"
msgstr "Intégrer votre CDN"

#: inc/Engine/Admin/Settings/Page.php:1599
msgid "All URLs of static files (CSS, JS, images) will be rewritten to the CNAME(s) you provide."
msgstr "Tous les URLs de vos fichiers statiques (CSS, JS, images) seront ré-écrits avec le CNAME fourni."

#: inc/Engine/Admin/Settings/Page.php:1601
#, php-format
msgid "Not required for services like Cloudflare and Sucuri. Please see our available %1$sAdd-ons%2$s."
msgstr "Non requis pour les services tels que Cloudflare et Sucuri. Veuillez consulter nos %1$sAjouts%2$s disponibles."

#: inc/Engine/Admin/Settings/Page.php:1616
#: inc/admin/options.php:132
msgid "Exclude files from CDN"
msgstr "Exclure des fichiers du CDN"

#: inc/Engine/Admin/Settings/Page.php:1642
msgid "%1$s%2$l Add-on%3$s is currently enabled. Configuration of the CDN settings is not required for %2$l to work on your site."
msgid_plural "%1$s%2$l Add-ons%3$s are currently enabled. Configuration of the CDN settings is not required for %2$l to work on your site."
msgstr[0] "%1$s%2$l Add-on%3$sest présentement activé. s. La configuration des réglages CDN n'est pas requise pour que %2$l fonctionne sur votre site."
msgstr[1] "%1$s%2$l Add-ons%3$s sont présentement activés. La configuration des réglages CDN n'est pas requise pour que %2$l fonctionne sur votre site."
msgstr[2] "%1$s%2$l Add-ons%3$s sont présentement activés. La configuration des réglages CDN n'est pas requise pour que %2$l fonctionne sur votre site."

#: inc/Engine/Admin/Settings/Page.php:1667
msgid "Enable Content Delivery Network"
msgstr "Activer le Content Delivery Network."

#: inc/Engine/Admin/Settings/Page.php:1676
#: inc/Engine/CDN/RocketCDN/AdminPageSubscriber.php:159
msgid "CDN CNAME(s)"
msgstr "CNAME(s) du CDN"

#: inc/Engine/Admin/Settings/Page.php:1677
#: inc/Engine/CDN/RocketCDN/AdminPageSubscriber.php:160
msgid "Specify the CNAME(s) below"
msgstr "Indiquez le(s) CNAME(s) ci-dessous"

#: inc/Engine/Admin/Settings/Page.php:1684
msgid "Specify URL(s) of files that should not get served via CDN (one per line)."
msgstr "Indiquez les URLs des fichiers qui ne doivent pas être servies par le CDN (une par ligne)."

#: inc/Engine/Admin/Settings/Page.php:1685
msgid "The domain part of the URL will be stripped automatically.<br>Use (.*) wildcards to exclude all files of a given file type located at a specific path."
msgstr "Le nom de domaine sera supprimé automatiquement de l'URL .<br>Utilisez les expressions régulières (.*) pour exclure plusieurs URLs pour un chemin donné."

#: inc/Engine/Admin/Settings/Page.php:1708
#: inc/Engine/Admin/Settings/Page.php:1716
msgid "Heartbeat"
msgstr "Battement de coeur"

#: inc/Engine/Admin/Settings/Page.php:1709
msgid "Control WordPress Heartbeat API"
msgstr "Contrôler l'API du battement de coeur de WordPress"

#: inc/Engine/Admin/Settings/Page.php:1717
msgid "Reducing or disabling the Heartbeat API’s activity can help save some of your server’s resources."
msgstr "Réduire ou désactiver l'API du signal de battement de coeur peut aider à économier des ressources serveur."

#: inc/Engine/Admin/Settings/Page.php:1723
msgid "Reduce or disable Heartbeat activity"
msgstr "Réduire ou désactiver le signal de battement de coeur"

#: inc/Engine/Admin/Settings/Page.php:1724
msgid "Reducing activity will change Heartbeat frequency from one hit each minute to one hit every 2 minutes."
msgstr "Réduire l'activité changera la fréquence de battement de coeur de une pulsation par minute à une pulsation aux 2 minutes."

#: inc/Engine/Admin/Settings/Page.php:1724
msgid "Disabling Heartbeat entirely may break plugins and themes using this API."
msgstr "Désactiver le battement de coeur complètement peut briser les extensions et thèmes utilisant cet API."

#: inc/Engine/Admin/Settings/Page.php:1738
msgid "Do not limit"
msgstr "Ne pas limiter"

#: inc/Engine/Admin/Settings/Page.php:1739
msgid "Reduce activity"
msgstr "Réduire l'activité"

#: inc/Engine/Admin/Settings/Page.php:1740
msgid "Disable"
msgstr "Désactiver"

#: inc/Engine/Admin/Settings/Page.php:1748
msgid "Control Heartbeat"
msgstr "Contrôler le battement de coeur"

#: inc/Engine/Admin/Settings/Page.php:1757
msgid "Behavior in backend"
msgstr "Comportement dans le tableau de bord"

#: inc/Engine/Admin/Settings/Page.php:1764
msgid "Behavior in post editor"
msgstr "Comportement dans l'éditeur"

#: inc/Engine/Admin/Settings/Page.php:1770
msgid "Behavior in frontend"
msgstr "Comportement sur le site"

#: inc/Engine/Admin/Settings/Page.php:1787
#: views/settings/page-sections/tutorials.php:39
msgid "Add-ons"
msgstr "Ajouts"

#: inc/Engine/Admin/Settings/Page.php:1788
msgid "Add more features"
msgstr "Ajouter des fonctionnalités"

#: inc/Engine/Admin/Settings/Page.php:1795
msgid "One-click Rocket Add-ons"
msgstr "Ajouts Rockets en un clic"

#: inc/Engine/Admin/Settings/Page.php:1796
msgid "One-Click Add-ons are features extending available options without configuration needed. Switch the option \"on\" to enable from this screen."
msgstr "Les ajouts en un clic offrent des fonctionnalités complémentaires en augmentant les options déjà disponibles sans besoin de configuration. Mettez l'option à \"ON\" pour l'activer depuis cet écran."

#: inc/Engine/Admin/Settings/Page.php:1806
msgid "Rocket Add-ons"
msgstr "Ajouts Rocket"

#: inc/Engine/Admin/Settings/Page.php:1807
msgid "Rocket Add-ons are complementary features extending available options."
msgstr "Les ajouts Rockets offrent des fonctionnalités complémentaires augmentant les options déjà disponibles."

#: inc/Engine/Admin/Settings/Page.php:1817
#: inc/Engine/Admin/Settings/Page.php:1986
msgid "Cloudflare"
msgstr "Cloudflare"

#: inc/Engine/Admin/Settings/Page.php:1823
msgid "Integrate your Cloudflare account with this add-on."
msgstr "Intégrez votre compte Cloudflare avec cet add-on"

#: inc/Engine/Admin/Settings/Page.php:1824
msgid "Provide your account email, global API key, and domain to use options such as clearing the Cloudflare cache and enabling optimal settings with WP Rocket."
msgstr "Entrez l’adresse courriel, la clé d’API globale et le domaine de votre compte Cloudflare afin d’ajouter l’option de purger le cache Cloudflare et activer les réglages optimaux pour fonctionner avec WP Rocket."

#: inc/Engine/Admin/Settings/Page.php:1875
msgid "Varnish"
msgstr "Varnish"

#: inc/Engine/Admin/Settings/Page.php:1881
msgid "If Varnish runs on your server, you must activate this add-on."
msgstr "Si votre serveur utilise Varnish, vous devez activer cet add-on"

#: inc/Engine/Admin/Settings/Page.php:1883
#, php-format
msgid "Varnish cache will be purged each time WP Rocket clears its cache to ensure content is always up-to-date.<br>%1$sLearn more%2$s"
msgstr "Le cache Varnish sera purgé à chaque fois que WP Rocket purgera son cache pour vous assurer que le contenu soit toujours à jour.<br>%1$sEn savoir plus%2$s"

#: inc/Engine/Admin/Settings/Page.php:1918
msgid "WebP Compatibility"
msgstr "Compatibilité WebP"

#: inc/Engine/Admin/Settings/Page.php:1924
msgid "Improve browser compatibility for WebP images."
msgstr "Améliore la compatibilité des navigateurs pour les images WebP."

#: inc/Engine/Admin/Settings/Page.php:1928
#, php-format
msgid "Enable this option if you would like WP Rocket to serve WebP images to compatible browsers. Please note that WP Rocket cannot create WebP images for you. To create WebP images we recommend %1$sImagify%2$s. %3$sMore info%2$s"
msgstr "Activez cette option si vous désirez que WP Rocket serve les images WebP au navigateurs compatibles. Veuillez noter que WP Rocket ne peut pas créer d'images WebP pour vous. Pour créez des images WebP nous recommandons %1$sImagify%2$s. %3$sPlus d'info %2$s"

#: inc/Engine/Admin/Settings/Page.php:1948
msgid "Clear the Sucuri cache when WP Rocket’s cache is cleared."
msgstr "Vider le cache Sucuri quand le cache de WP Rocket est vidé."

#: inc/Engine/Admin/Settings/Page.php:1951
msgid "Provide your API key to clear the Sucuri cache when WP Rocket’s cache is cleared."
msgstr "Fournir votre clé d'API afin de vider le cache Sucuri quand le cache de WP Rocket sera vidé."

#: inc/Engine/Admin/Settings/Page.php:1959
#: inc/Engine/Admin/Settings/Page.php:2103
msgid "Sucuri"
msgstr "Sucuri"

#: inc/Engine/Admin/Settings/Page.php:1965
msgid "Synchronize Sucuri cache with this add-on."
msgstr "Synchronisez le cache Sucuri avec cet add-on."

#: inc/Engine/Admin/Settings/Page.php:2003
msgid "Cloudflare credentials"
msgstr "Accès Cloudflare"

#: inc/Engine/Admin/Settings/Page.php:2012
msgid "Cloudflare settings"
msgstr "Réglages Cloudflare"

#: inc/Engine/Admin/Settings/Page.php:2026
msgctxt "Cloudflare"
msgid "Global API key:"
msgstr "Clé d’API globale :"

#: inc/Engine/Admin/Settings/Page.php:2027
msgctxt "Cloudflare"
msgid "Find your API key"
msgstr "Trouver votre clé d’API"

#: inc/Engine/Admin/Settings/Page.php:2039
msgctxt "Cloudflare"
msgid "Account email"
msgstr "Courriel du compte"

#: inc/Engine/Admin/Settings/Page.php:2048
msgctxt "Cloudflare"
msgid "Zone ID"
msgstr "Identifiant de zone"

#: inc/Engine/Admin/Settings/Page.php:2058
msgid "Development mode"
msgstr "Mode développement"

#: inc/Engine/Admin/Settings/Page.php:2060
#, php-format
msgid "Temporarily activate development mode on your website. This setting will automatically turn off after 3 hours. %1$sLearn more%2$s"
msgstr "Active temporairement le mode développement sur votre site. Ce réglage se désactivera automatiquement après 3 heures. %1$sEn savoir plus%2$s"

#: inc/Engine/Admin/Settings/Page.php:2068
msgid "Optimal settings"
msgstr "Réglages optimaux"

#: inc/Engine/Admin/Settings/Page.php:2069
msgid "Automatically enhances your Cloudflare configuration for speed, performance grade and compatibility."
msgstr "Améliore automatiquement votre configuration Cloudflare pour la rapidité de chargement, les notes de performance et la compatibilité."

#: inc/Engine/Admin/Settings/Page.php:2077
msgid "Relative protocol"
msgstr "Relatif au protocole"

#: inc/Engine/Admin/Settings/Page.php:2078
msgid "Should only be used with Cloudflare's flexible SSL feature. URLs of static files (CSS, JS, images) will be rewritten to use // instead of http:// or https://."
msgstr "Ne devrait être utilisé qu’avec la fonction Flexible SSL de Cloudflare. Les URLs de vos fichiers statiques (CSS, JS, images) seront ré-écrits pour utiliser // au lieu de http:// ou https://."

#: inc/Engine/Admin/Settings/Page.php:2116
msgid "Sucuri credentials"
msgstr "Accès Sucuri"

#: inc/Engine/Admin/Settings/Page.php:2130
msgctxt "Sucuri"
msgid "Find your API key"
msgstr "Trouver votre clé d’API"

#: inc/Engine/Admin/Settings/Render.php:422
#: inc/deprecated/deprecated.php:1294
msgid "Upload file and import settings"
msgstr "Envoyer le fichier et importer les réglages"

#: inc/Engine/Admin/Settings/Settings.php:362
msgid "Sucuri Add-on: The API key for the Sucuri firewall must be in format <code>{32 characters}/{32 characters}</code>."
msgstr "Ajout Sucuri : la clé d'API pour le pare-feu Sucuri doit être au format <code>{32 caractères}/{32 caractères}</code>."

#: inc/Engine/Admin/Settings/Settings.php:452
#: inc/deprecated/deprecated.php:1245
msgid "Settings saved."
msgstr "Réglages sauvegardés."

#: inc/Engine/Admin/Settings/Settings.php:668
msgid "Sorry! Adding /(.*) in Advanced Rules > Never Cache URL(s) was not saved because it disables caching and optimizations for every page on your site."
msgstr "Désolé ! L'ajout de /(.*) dans Règles avancées > Ne jamais mettre en cache ces URL(s) n'a pas été sauvegardé car cette valeur désactive le cache et les optimisations sur toutes les pages du site."

#: inc/Engine/Admin/Settings/Subscriber.php:168
#: inc/deprecated/deprecated.php:1786
#: views/settings/page-sections/tools.php:33
msgid "Tools"
msgstr "Outils"

#: inc/Engine/Admin/Settings/Subscriber.php:169
msgid "Import, Export, Rollback"
msgstr "Import, export, restauration"

#: inc/Engine/Admin/Settings/Subscriber.php:194
#: views/settings/page-sections/imagify.php:14
msgid "Image Optimization"
msgstr "Optimisation des images"

#: inc/Engine/Admin/Settings/Subscriber.php:195
msgid "Compress your images"
msgstr "Compresser vos images"

#: inc/Engine/Admin/Settings/Subscriber.php:212
#: views/settings/page-sections/tutorials.php:48
msgid "Tutorials"
msgstr "Tutoriels"

#: inc/Engine/Admin/Settings/Subscriber.php:213
msgid "Getting started and how to videos"
msgstr "Bien démarrer et vidéos d'assistance"

#: inc/Engine/CDN/RocketCDN/APIClient.php:134
msgid "We could not fetch the current price because RocketCDN API returned an unexpected error code."
msgstr "Nous n'avons pas pu récupérer le prix actuel car l'API RocketCDN a renvoyé un code d'erreur inattendu."

#: inc/Engine/CDN/RocketCDN/APIClient.php:140
msgid "RocketCDN is not available at the moment. Please retry later."
msgstr "RocketCDN n'est pas disponible pour le moment. Veuillez réessayer plus tard."

#: inc/Engine/CDN/RocketCDN/APIClient.php:177
msgid "RocketCDN cache purge failed: Missing identifier parameter."
msgstr "La purge du cache RocketCDN a échoué : identifiant de réglage manquant."

#: inc/Engine/CDN/RocketCDN/APIClient.php:186
msgid "RocketCDN cache purge failed: Missing user token."
msgstr "La purge du cache RocketCDN a échoué : identifiant d'utilisateur manquant."

#: inc/Engine/CDN/RocketCDN/APIClient.php:212
msgid "RocketCDN cache purge failed: The API returned an unexpected response code."
msgstr "La purge du cache RocketCDN a échoué : l'API a retourné un code de réponse inattendu."

#: inc/Engine/CDN/RocketCDN/APIClient.php:221
msgid "RocketCDN cache purge failed: The API returned an empty response."
msgstr "La purge du cache RocketCDN a échoué : l'API a retourné une réponse vide."

#: inc/Engine/CDN/RocketCDN/APIClient.php:230
msgid "RocketCDN cache purge failed: The API returned an unexpected response."
msgstr "La purge du cache RocketCDN a échoué : l'API a retourné une réponse inattendue."

#: inc/Engine/CDN/RocketCDN/APIClient.php:239
#, php-format
msgid "RocketCDN cache purge failed: %s."
msgstr "La purge du cache RocketCDN a échoué : %s."

#: inc/Engine/CDN/RocketCDN/APIClient.php:247
msgid "RocketCDN cache purge successful."
msgstr "La purge du cache de RocketCDN a été effectuée avec succès."

#: inc/Engine/CDN/RocketCDN/AdminPageSubscriber.php:90
msgid "Next Billing Date"
msgstr "Prochaine date de facturation"

#: inc/Engine/CDN/RocketCDN/AdminPageSubscriber.php:99
msgid "No Subscription"
msgstr "Aucun abonnement"

#: inc/Engine/CDN/RocketCDN/AdminPageSubscriber.php:135
msgid "Your RocketCDN subscription is currently active."
msgstr "Votre abonnement à RocketCDN est présentement activé."

#: inc/Engine/CDN/RocketCDN/AdminPageSubscriber.php:141
#, php-format
msgid "To use RocketCDN, replace your CNAME with %1$s%2$s%3$s."
msgstr "Pour utiliser RocketCDN, remplacer votre CNAME avec %1$s%2$s%3$s."

#: inc/Engine/CDN/RocketCDN/AdminPageSubscriber.php:152
#: inc/Engine/CDN/RocketCDN/NoticesSubscriber.php:230
#: inc/Engine/CDN/RocketCDN/NoticesSubscriber.php:334
#, php-format
msgid "%1$sMore Info%2$s"
msgstr "%1$sPlus d'Info%2$s"

#: inc/Engine/CDN/RocketCDN/RESTSubscriber.php:137
msgid "RocketCDN enabled"
msgstr "RocketCDN activé"

#: inc/Engine/CDN/RocketCDN/RESTSubscriber.php:160
msgid "RocketCDN disabled"
msgstr "RocketCDN désactivé"

#: inc/Engine/CDN/RocketCDN/views/cta-big.php:27
#, php-format
msgid "Valid until %s only!"
msgstr "Valide seulement jusqu'à %s!"

#: inc/Engine/CDN/RocketCDN/views/cta-big.php:36
msgid "Speed up your website thanks to:"
msgstr "Accélérez votre site web grâce à :"

#: inc/Engine/CDN/RocketCDN/views/cta-big.php:42
#, php-format
msgid "High performance Content Delivery Network (CDN) with %1$sunlimited bandwidth%2$s"
msgstr "CDN (Content Delivery Network) de haute performance à %1$sbande passante illimitée%2$s"

#: inc/Engine/CDN/RocketCDN/views/cta-big.php:48
#, php-format
msgid "Easy configuration: the %1$sbest CDN settings%2$s are automatically applied"
msgstr "Configuration simplifiée : les %1$smeilleurs réglages CDN%2$s sont automatiquement appliqués"

#: inc/Engine/CDN/RocketCDN/views/cta-big.php:54
#, php-format
msgid "WP Rocket integration: the CDN option is %1$sautomatically configured%2$s in our plugin"
msgstr "Intégration WP Rocket : l'option CDN est %1$sautomatiquement configurée%2$s dans notre extension"

#: inc/Engine/CDN/RocketCDN/views/cta-big.php:58
msgid "Learn more about RocketCDN"
msgstr "En savoir plus sur RocketCDN"

#: inc/Engine/CDN/RocketCDN/views/cta-big.php:65
#, php-format
msgid "*$%1$s/month for 12 months then $%2$s/month. You can cancel your subscription at any time."
msgstr "*%1$s$/mois pour 12 mois puis %2$s$/mois. Vous pouvez résilier votre abonnement à tout moment."

#: inc/Engine/CDN/RocketCDN/views/cta-big.php:86
msgid "Billed monthly"
msgstr "Facturation mensuelle"

#: inc/Engine/CDN/RocketCDN/views/cta-big.php:87
msgid "Get Started"
msgstr "Démarrez"

#: inc/Engine/CDN/RocketCDN/views/cta-big.php:92
msgid "Reduce this banner"
msgstr "Réduire cette bannière"

#: inc/Engine/CDN/RocketCDN/views/cta-small.php:17
msgid "Speed up your website with RocketCDN, WP Rocket’s Content Delivery Network."
msgstr "Accélérez votre site web avec RocketCDN, le réseau de livraison de contenu de WP Rocket."

#: inc/Engine/CDN/RocketCDN/views/cta-small.php:20
#: inc/Engine/CDN/RocketCDN/views/promote-notice.php:13
msgid "Learn More"
msgstr "En savoir plus"

#: inc/Engine/CDN/RocketCDN/views/dashboard-status.php:23
msgid "RocketCDN is unavailable on local domains and staging sites."
msgstr "RocketCDN est indisponible pour les domaines locaux et les sites de test."

#: inc/Engine/CDN/RocketCDN/views/dashboard-status.php:32
msgid "Get RocketCDN"
msgstr "Obtenir RocketCDN"

#: inc/Engine/CDN/RocketCDN/views/promote-notice.php:11
msgid "New!"
msgstr "Nouveau!"

#: inc/Engine/CDN/RocketCDN/views/promote-notice.php:12
msgid "Speed up your website with RocketCDN, WP Rocket’s Content Delivery Network!"
msgstr "Accélérez votre site web avec RocketCDN, le réseau de livraison de contenu de WP Rocket."

#: inc/Engine/Cache/AdminSubscriber.php:118
#: inc/admin/admin.php:96
#: inc/admin/admin.php:118
#: inc/deprecated/3.5.php:898
msgid "Clear this cache"
msgstr "Vider ce cache"

#: inc/Engine/Cache/PurgeExpired/Subscriber.php:75
msgid "WP Rocket Expired Cache Interval"
msgstr "Intervalle d'expiration du cache WP Rocket"

#: inc/Engine/Cache/WPCache.php:337
msgid "WP_CACHE value"
msgstr "Valeur de WP_CACHE"

#: inc/Engine/Cache/WPCache.php:358
msgid "The WP_CACHE constant needs to be set to true for WP Rocket cache to work properly"
msgstr "La constante WP_CACHE doit être à \"true\" pour que le cache de WP Rocket fonctionne correctement"

#: inc/Engine/Cache/WPCache.php:367
msgid "WP_CACHE is set to true"
msgstr "WP_CACHE est à \"true\""

#: inc/Engine/Cache/WPCache.php:375
msgid "WP_CACHE is not set"
msgstr "WP_CACHE n'est pas défini"

#: inc/Engine/Cache/WPCache.php:383
msgid "WP_CACHE is set to false"
msgstr "WP_CACHE est à \"false\""

#: inc/Engine/Common/Queue/RUCSSQueueRunner.php:253
msgid "Every minute"
msgstr "Chaque minute"

#: inc/Engine/CriticalPath/APIClient.php:64
#, php-format
msgid "Critical CSS for %1$s not generated. Error: %2$s"
msgstr "Le CSS critique pour %1$s n'a pas été généré. Erreur : %2$s"

#: inc/Engine/CriticalPath/APIClient.php:170
#, php-format
msgid "Critical CSS for %1$s on mobile not generated. Error: The API returned an empty response."
msgstr "CSS critique aux mobiles pour %1$s non-généré. Erreur : l'API a retourné une réponse vide."

#: inc/Engine/CriticalPath/APIClient.php:173
#, php-format
msgid "Critical CSS for %1$s not generated. Error: The API returned an empty response."
msgstr "CSS critique pour %1$s non généré. Erreur : l'API a retourné une réponse vide."

#: inc/Engine/CriticalPath/APIClient.php:185
#, php-format
msgid "Critical CSS for %1$s on mobile not generated."
msgstr "CSS critique aux mobiles pour %1$s non-généré."

#: inc/Engine/CriticalPath/APIClient.php:187
#: inc/Engine/CriticalPath/ProcessorService.php:194
#, php-format
msgid "Critical CSS for %1$s not generated."
msgstr "CSS critique pour %1$s non-généré."

#: inc/Engine/CriticalPath/APIClient.php:195
#, php-format
msgid "Critical CSS for %1$s on mobile not generated. Error: The API returned an invalid response code."
msgstr "CSS critique aux mobiles pour %1$s non-généré. Erreur : l'API a retourné un code de réponse invalide."

#: inc/Engine/CriticalPath/APIClient.php:197
#, php-format
msgid "Critical CSS for %1$s not generated. Error: The API returned an invalid response code."
msgstr "CSS critique pour %1$s non-généré. Erreur : l'API a retourné un code de réponse invalide"

#: inc/Engine/CriticalPath/APIClient.php:205
#: inc/Engine/CriticalPath/ProcessorService.php:201
#, php-format
msgid "Error: %1$s"
msgstr "Erreur : %1$s"

#: inc/Engine/CriticalPath/Admin/Admin.php:264
msgid "Regenerate Critical Path CSS"
msgstr "Régénérer le CSS critique"

#: inc/Engine/CriticalPath/Admin/Post.php:144
#: views/cpcss/metabox/generate.php:47
msgid "Generate Specific CPCSS"
msgstr "Générer le CPCSS spécifique"

#: inc/Engine/CriticalPath/Admin/Post.php:145
#: views/cpcss/metabox/generate.php:45
msgid "Regenerate specific CPCSS"
msgstr "Regénérer le CPCSS spécifique"

#: inc/Engine/CriticalPath/Admin/Post.php:216
msgid "This feature is not available for non-public post types."
msgstr "Cette fonctionnalité n'est pas disponible pour les publications qui ne sont pas publiques."

#: inc/Engine/CriticalPath/Admin/Post.php:219
msgid "%l to use this feature."
msgstr "%l pour utiliser cette fonction."

#: inc/Engine/CriticalPath/Admin/Post.php:222
#, php-format
msgid "Publish the %s"
msgstr "Publier le %s"

#: inc/Engine/CriticalPath/Admin/Post.php:223
msgid "Enable Load CSS asynchronously in WP Rocket settings"
msgstr "Activez Chargement Asynchrone du CSS dans les réglages de WP Rocket"

#: inc/Engine/CriticalPath/Admin/Post.php:224
msgid "Enable Load CSS asynchronously in the options above"
msgstr "Activez Chargement Asynchrone du CSS dans les options ci-dessus"

#: inc/Engine/CriticalPath/CriticalCSSSubscriber.php:176
msgid "Critical CSS generation is currently running."
msgstr "La génération du CSS critique est en cours."

#: inc/Engine/CriticalPath/CriticalCSSSubscriber.php:181
#, php-format
msgid "Go to the %1$sWP Rocket settings%2$s page to track progress."
msgstr "Allez à la page des %1$sparamètres WP Rocket %2$s afin d'en suivre l'évolution."

#: inc/Engine/CriticalPath/CriticalCSSSubscriber.php:396
#, php-format
msgid "Critical CSS generation is currently running: %1$d of %2$d page types completed. (Refresh this page to view progress)"
msgstr "La génération du CSS critique est en cours : %1$d de %2$d de types de contenus complétés. (Rafraîchir pour voir la progression)"

#: inc/Engine/CriticalPath/CriticalCSSSubscriber.php:472
#, php-format
msgid "Critical CSS generation finished for %1$d of %2$d page types."
msgstr "La génération du CSS critique est terminée pour %1$d des %2$d types de contenus."

#: inc/Engine/CriticalPath/CriticalCSSSubscriber.php:479
msgid "Critical CSS generation encountered one or more errors."
msgstr "La génération du CSS critique a rencontré une ou plusieurs erreur(s)."

#: inc/Engine/CriticalPath/CriticalCSSSubscriber.php:479
msgid "Learn more."
msgstr "En savoir plus."

#: inc/Engine/CriticalPath/DataManager.php:68
#, php-format
msgid "Critical CSS for %1$s on mobile not generated. Error: The destination folder could not be created."
msgstr "CSS critique pour %1$s sur mobile non-généré. Erreur : Le répertoire de destination n'a pas pu être créé."

#: inc/Engine/CriticalPath/DataManager.php:71
#, php-format
msgid "Critical CSS for %1$s not generated. Error: The destination folder could not be created."
msgstr "CSS critique pour %1$s non-généré. Erreur : Le répertoire de destination n'a pu être créé."

#: inc/Engine/CriticalPath/DataManager.php:106
msgid "Critical CSS file for mobile does not exist"
msgstr "Fichier critique CSS pour mobiles n'existe pas"

#: inc/Engine/CriticalPath/DataManager.php:108
msgid "Critical CSS file does not exist"
msgstr "Fichier critique CSS n'existe pas"

#: inc/Engine/CriticalPath/DataManager.php:120
msgid "Critical CSS file for mobile cannot be deleted"
msgstr "Fichier critique CSS pour mobiles ne peut être effacé"

#: inc/Engine/CriticalPath/DataManager.php:122
msgid "Critical CSS file cannot be deleted"
msgstr "Fichier critique CSS ne peut être effacé"

#: inc/Engine/CriticalPath/ProcessorService.php:187
#, php-format
msgid "Mobile Critical CSS for %1$s not generated."
msgstr "Le CSS critique aux mobiles pour %1$s n'a pas été généré."

#: inc/Engine/CriticalPath/ProcessorService.php:228
#, php-format
msgid "Critical CSS for %s in progress."
msgstr "Le CSS critique pour %s est en cours."

#: inc/Engine/CriticalPath/ProcessorService.php:262
#, php-format
msgid "Mobile Critical CSS for %s generated."
msgstr "Le CSS critique aux mobiles pour %s est généré."

#: inc/Engine/CriticalPath/ProcessorService.php:273
#, php-format
msgid "Critical CSS for %s generated."
msgstr "Le CSS critique pour %s a été généré."

#: inc/Engine/CriticalPath/ProcessorService.php:295
msgid "Critical CSS file deleted successfully."
msgstr "Fichier de CSS critique effacé avec succès"

#: inc/Engine/CriticalPath/ProcessorService.php:317
#, php-format
msgid "Mobile Critical CSS for %1$s timeout. Please retry a little later."
msgstr "Délai d'attente du CSS critique aux mobiles pour %1$s. Veuillez essayer de nouveau plus tard."

#: inc/Engine/CriticalPath/ProcessorService.php:330
#, php-format
msgid "Critical CSS for %1$s timeout. Please retry a little later."
msgstr "Délai d'attente pour le CSS critique pour %1$s. Veuillez essayer de nouveau plus tard."

#: inc/Engine/CriticalPath/RESTWP.php:141
msgid "Mobile CPCSS generation not enabled."
msgstr "Génération du CPCSS pour mobiles non-activé"

#: inc/Engine/CriticalPath/RESTWPPost.php:36
#: inc/Engine/CriticalPath/RESTWPPost.php:69
msgid "Requested post does not exist."
msgstr "L'article demandé n'existe pas"

#: inc/Engine/CriticalPath/RESTWPPost.php:46
msgid "Cannot generate CPCSS for unpublished post."
msgstr "Impossible de générer le CPCSS pour les articles non-publiés"

#: inc/Engine/HealthCheck/HealthCheck.php:82
#: inc/deprecated/3.5.php:858
msgid "The following scheduled event failed to run. This may indicate the CRON system is not running properly, which can prevent some WP Rocket features from working as intended:"
msgid_plural "The following scheduled events failed to run. This may indicate the CRON system is not running properly, which can prevent some WP Rocket features from working as intended:"
msgstr[0] "L'évènement planifié suivant n'a pas fonctionné. Ceci pourrait indiquer que le système CRON ne fonctionne pas correctement, ce qui peut prévenir certaines fonctionnalités WP Rocket de fonctionner tel que prévu :"
msgstr[1] "Les évènements planifiés suivants n'ont pas fonctionné. Ceci pourrait indiquer que le système CRON ne fonctionne pas correctement, ce qui peut prévenir certaines fonctionnalités WP Rocket de fonctionner tel que prévu :"
msgstr[2] "Les évènements planifiés suivants n'ont pas fonctionné. Ceci pourrait indiquer que le système CRON ne fonctionne pas correctement, ce qui peut prévenir certaines fonctionnalités WP Rocket de fonctionner tel que prévu :"

#: inc/Engine/HealthCheck/HealthCheck.php:88
#: inc/deprecated/3.5.php:867
msgid "Please contact your host to check if CRON is working."
msgstr "Veuillez contacter votre hébergeur afin de valider si le CRON est fonctionnel."

#: inc/Engine/HealthCheck/HealthCheck.php:142
msgid "Scheduled Cache Purge"
msgstr "Purge planifiée de la cache"

#: inc/Engine/HealthCheck/HealthCheck.php:143
msgid "Scheduled Database Optimization"
msgstr "Optimisation planifiée de la base de données"

#: inc/Engine/HealthCheck/HealthCheck.php:144
msgid "Database Optimization Process"
msgstr "Processus d'optimisation de la base de données"

#: inc/Engine/HealthCheck/HealthCheck.php:145
msgctxt "noun"
msgid "Preload"
msgstr "Préchargement"

#: inc/Engine/HealthCheck/HealthCheck.php:146
msgid "Critical Path CSS Generation Process"
msgstr "Processus de génération du CSS pour chemin critique"

#: inc/Engine/License/Renewal.php:139
#, php-format
msgid "Renew your license for 1 year now at %1$s%3$s%2$s."
msgstr "Renouvelez votre licence pour 1 an maintenant à %1$s%3$s%2$s."

#: inc/Engine/License/Renewal.php:152
#, php-format
msgid "Renew your license for 1 year now and get %1$s%3$s OFF%2$s immediately: you will only pay %1$s%4$s%2$s!"
msgstr "Renouvelez votre licence pour 1 an et bénéficiez d’une réduction immédiate %1$sde %3$s%2$s: vous ne paierez que %1$s%4$s%2$s!"

#: inc/Engine/License/Renewal.php:218
#, php-format
msgid "Renew before it is too late, you will pay %1$s%3$s%2$s."
msgstr "Renouvelez avant qu'il ne soit trop tard, vous payerez %1$s%3$s%2$s."

#: inc/Engine/License/Renewal.php:227
#, php-format
msgid "Renew with a %1$s%2$s discount%3$s before it is too late, you will only pay %1$s%4$s%3$s!"
msgstr "Renouvelez avec un %1$s%2$s escompte%3$s avant qu'il ne soit trop tard, vous ne payerez seulement que %1$s%4$s%3$s!"

#: inc/Engine/License/Renewal.php:546
#, php-format
msgid "You need a valid license to continue using this feature. %1$sRenew now%2$s before losing access."
msgstr "Vous devez détenir une licence valide pour continuer à utiliser cette option. %1$sRenouveler maintenant%2$s avant de perdre l'accès."

#: inc/Engine/License/Renewal.php:567
#, php-format
msgid "You need an active license to enable this option. %1$sRenew now%2$s."
msgstr "Vous devez détenir une licence active pour activer cette option. %1$sRenouveler maintenant%2$s."

#: inc/Engine/License/Renewal.php:595
#, php-format
msgid "You need an active license to enable this option. %1$sMore info%2$s."
msgstr "Vous devez détenir une licence active pour activer cette option. %1$sPlus d'infos%2$s."

#: inc/Engine/License/Upgrade.php:252
#, php-format
msgid "Take advantage of %1$s to speed up more websites:%2$s get a %3$s%4$s off%5$s for %3$supgrading your license to Plus or Infinite!%5$s"
msgid_plural "Take advantage of %1$s to speed up more websites:%2$s get a %3$s%4$s off%5$s for %3$supgrading your license to Infinite!%5$s"
msgstr[0] "Prenez avantage de %1$s afin d'accéléler plus de sites web :%2$s obtenez %3$s%4$s d'escompte%5$s pour %3$srehausser votre licence à \"Plus\" ou \"Infinite\" !%5$s"
msgstr[1] "Prenez avantage de %1$s afin d'accélérer plus de sites web :%2$s obtenez %3$s%4$s d'escompte%5$s pour %3$srehausser votre licence à \"Infinite\" !%5$s"
msgstr[2] "Prenez avantage de %1$s afin d'accélérer plus de sites web :%2$s obtenez %3$s%4$s d'escompte%5$s pour %3$srehausser votre licence à \"Infinite\" !%5$s"

#: inc/Engine/License/Upgrade.php:382
#: inc/Engine/License/Upgrade.php:405
msgid "Unlimited"
msgstr "Illimité"

#: inc/Engine/License/views/promo-banner.php:16
#, php-format
msgid "%s off"
msgstr "%s d'escompte"

#: inc/Engine/License/views/promo-banner.php:21
#, php-format
msgid "%s promotion is live!"
msgstr "La promotion %s est en cours!"

#: inc/Engine/License/views/promo-banner.php:27
msgid "Hurry Up! Deal ends in:"
msgstr "Dépêchez-vous! La promotion se termine dans :"

#: inc/Engine/License/views/promo-banner.php:31
#: inc/Engine/License/views/renewal-soon-banner.php:14
msgid "Minutes"
msgstr "Minutes"

#: inc/Engine/License/views/promo-banner.php:32
#: inc/Engine/License/views/renewal-soon-banner.php:15
msgid "Seconds"
msgstr "secondes"

#: inc/Engine/License/views/promo-banner.php:34
#: inc/Engine/License/views/upgrade-section.php:11
msgid "Upgrade now"
msgstr "Rehausser maintenant"

#: inc/Engine/License/views/promo-banner.php:36
#: inc/Engine/License/views/renewal-expired-banner-ocd-disabled.php:32
#: inc/Engine/License/views/renewal-expired-banner-ocd.php:42
#: inc/Engine/License/views/renewal-expired-banner.php:30
#: inc/admin/ui/notices.php:739
#: views/settings/page-sections/dashboard.php:46
msgid "Dismiss this notice"
msgstr "Ne pas tenir compte de cet avertissement."

#: inc/Engine/License/views/renewal-expired-banner-ocd-disabled.php:11
msgid "The Optimize CSS Delivery feature is disabled."
msgstr "L'option Optimiser le chargement du CSS est désactivée."

#: inc/Engine/License/views/renewal-expired-banner-ocd-disabled.php:15
msgid "You can no longer use the Remove Unused CSS or Load CSS asynchronously options."
msgstr "Vous ne pouvez plus utiliser les options Supprimer les ressources CSS inutilisées ou Chargement asynchrone du CSS."

#: inc/Engine/License/views/renewal-expired-banner-ocd-disabled.php:20
#, php-format
msgid "You need an %1$sactive license%2$s to keep optimizing your CSS delivery, which addresses a PageSpeed Insights recommendation and improves your page performance."
msgstr "Vous avez besoin d'une %1$slicence active%2$s pour continuer à optimiser le chargement de votre CSS, ce qui répond à une recommandation de PageSpeed Insights et améliore les performances de vos pages."

#: inc/Engine/License/views/renewal-expired-banner-ocd-disabled.php:29
#: inc/Engine/License/views/renewal-expired-banner-ocd.php:39
#: inc/Engine/License/views/renewal-expired-banner.php:27
#: inc/Engine/License/views/renewal-soon-banner.php:31
msgid "Renew now"
msgstr "Renouveler maintenant"

#: inc/Engine/License/views/renewal-expired-banner-ocd.php:11
msgid "You will soon lose access to some features"
msgstr "Vous perdrez bientôt l'accès à certaines options"

#: inc/Engine/License/views/renewal-expired-banner-ocd.php:18
#, php-format
msgid "You need an %1$sactive license to continue optimizing your CSS delivery%2$s."
msgstr "Vous avez besoin d'une %1$slicence active pour continuer à optimiser le chargement de votre CSS%2$s."

#: inc/Engine/License/views/renewal-expired-banner-ocd.php:24
msgid "The Remove Unused CSS and Load CSS asynchronously features are great options to address the PageSpeed Insights recommendations and improve your website performance."
msgstr "Les options Supprimer les ressources CSS inutilisées et Chargement Asynchrone du CSS sont des options intéressantes pour répondre aux recommandations de PageSpeed Insights et améliorer les performances de votre site Web."

#: inc/Engine/License/views/renewal-expired-banner-ocd.php:29
#, php-format
msgid "They will be %1$sautomatically disabled on %3$s%2$s."
msgstr "Elles seront %1$sautomatiquement désactivées le %3$s%2$s."

#: inc/Engine/License/views/renewal-expired-banner.php:11
msgid "Your WP Rocket license is expired!"
msgstr "Votre licence WP Rocket est expirée!"

#: inc/Engine/License/views/renewal-expired-banner.php:18
#, php-format
msgid "Your website could be much faster if it could take advantage of our %1$snew features and enhancements%2$s. 🚀"
msgstr "Votre site web pourrait être beaucoup plus rapide s'il pouvait profiter de nos %1$s nouvelles fonctionnalités et améliorations%2$s.🚀"

#: inc/Engine/License/views/renewal-soon-banner.php:22
#, php-format
msgid "Your %1$sWP Rocket license is about to expire%2$s: you will soon lose access to product updates and support."
msgstr "Votre %1$slicence WP Rocket est sur le point d'expirer%2$s : vous perdrez bientôt l'accès aux mises à jour et au support."

#: inc/Engine/License/views/upgrade-popin.php:12
msgid "Speed Up More Websites"
msgstr "Accélérez plus de sites web"

#: inc/Engine/License/views/upgrade-popin.php:19
#, php-format
msgid "You can use WP Rocket on more websites by upgrading your license. To upgrade, simply pay the %1$sprice difference%2$s between your current and new licenses, as shown below."
msgstr "Vous pouvez utiliser WP Rocket sur plus de sites web en rehaussant votre licence. Pour rehausser, simplement payer la %1$sdifférence de prix %2$s entre votre licence actuelle et la nouvelle, tel que montré ci-dessous."

#: inc/Engine/License/views/upgrade-popin.php:25
#, php-format
msgid "%1$sN.B.%2$s: Upgrading your license does not change your expiration date"
msgstr "%1$sP.S.%2$s: rehausser votre licence ne change pas votre date de renouvellement."

#: inc/Engine/License/views/upgrade-popin.php:35
#, php-format
msgid "Save $%s"
msgstr "Sauvegarder $%s"

#: inc/Engine/License/views/upgrade-popin.php:48
#, php-format
msgid "%s websites"
msgstr "%s sites web"

#: inc/Engine/License/views/upgrade-popin.php:54
#, php-format
msgid "Upgrade to %s"
msgstr "Rehausser à %s"

#: inc/Engine/License/views/upgrade-section.php:11
msgid "You can use WP Rocket on more websites by upgrading your license (you will only pay the price difference between your current and new licenses)."
msgstr "Vous pouvez utiliser WP Rocket sur plus de sites web en rehaussant votre licence (vous ne payerez que la différence de prix entre votre licence courante et la nouvelle)."

#: inc/Engine/Optimization/DynamicLists/DynamicLists.php:101
msgid "You need an active license to get the latest version of the lists from our server."
msgstr "Vous devez avoir une licence active pour obtenir la dernière version des listes de notre serveur."

#: inc/Engine/Optimization/DynamicLists/DynamicLists.php:115
msgid "Could not get updated lists from server."
msgstr "Impossible d'obtenir les listes mises à jour depuis le serveur."

#: inc/Engine/Optimization/DynamicLists/DynamicLists.php:124
msgid "Lists are up to date."
msgstr "Les listes sont à jour."

#: inc/Engine/Optimization/DynamicLists/DynamicLists.php:133
msgid "Could not update lists."
msgstr "Impossible de mettre à jour les listes."

#: inc/Engine/Optimization/DynamicLists/DynamicLists.php:142
msgid "Lists are successfully updated."
msgstr "Les listes ont été mises à jour avec succès."

#: inc/Engine/Optimization/RUCSS/Admin/Settings.php:131
#: views/settings/page-sections/dashboard.php:188
msgid "Clear Used CSS"
msgstr "Vider le CSS utilisé"

#: inc/Engine/Optimization/RUCSS/Admin/Settings.php:219
#, php-format
msgid "%1$s: Please wait %2$s seconds. The Remove Unused CSS service is processing your pages."
msgstr "%1$s: Veuillez patienter %2$s secondes. Le service Supprimer les ressources CSS inutilisées analyse vos pages."

#: inc/Engine/Optimization/RUCSS/Admin/Settings.php:264
#, php-format
msgid "%1$s: The Used CSS of your homepage has been processed. WP Rocket will continue to generate Used CSS for up to %2$s URLs per %3$s second(s)."
msgstr "%1$s: Le CSS utilisé de votre page d'accueil a été généré. WP Rocket va continuer de générer le CSS jusqu'à %2$s URLs par  %3$s seconde(s)."

#: inc/Engine/Optimization/RUCSS/Admin/Settings.php:273
#, php-format
msgid "We suggest enabling %1$sPreload%2$s for the fastest results."
msgstr "Nous vous suggérons d'activer le %1$sPréchargement%2$s pour obtenir des résultats plus rapides."

#: inc/Engine/Optimization/RUCSS/Admin/Settings.php:283
#, php-format
msgid "To learn more about the process check our %1$sdocumentation%2$s."
msgstr "Pour en apprendre plus sur le processus, consultez notre %1$sdocumentation%2$s."

#: inc/Engine/Optimization/RUCSS/Admin/Settings.php:481
msgid "We couldn't generate the used CSS because you're using a nulled version of WP Rocket. You need an active license to use the Remove Unused CSS feature and further improve your website's performance."
msgstr "Nous n'avons pas pu générer le CSS utilisé car vous utilisez une version piratée de WP Rocket. Vous avez besoin d'une licence active pour utiliser l'option Supprimer les CSS inutilisés et améliorer davantage les performances de votre site Web."

#: inc/Engine/Optimization/RUCSS/Admin/Settings.php:484
#, php-format
msgid "Click here to get a WP Rocket single license at %1$s off!"
msgstr "Cliquez ici pour obtenir une licence Single de WP Rocket à moins %1$s !"

#: inc/Engine/Optimization/RUCSS/Admin/Settings.php:521
#, php-format
msgid "Could not create the %2$s table in the database which is necessary for the Remove Unused CSS feature to work. Please reach out to <a href=\"%3$s\">our support</a>."
msgstr "Il n'a pas été possible de créer la table %2$s dans la base de données, qui est nécessaire pour que Supprimer les ressources CSS inutilisées fonctionne. Veuillez contacter <a href=\"%3$s\">notre support</a>."

#: inc/Engine/Optimization/RUCSS/Admin/Subscriber.php:322
#, php-format
msgid "%1$s: Used CSS option is not enabled!"
msgstr "%1$s: L'option du CSS utilisé n'est pas activée!"

#: inc/Engine/Optimization/RUCSS/Admin/Subscriber.php:343
#, php-format
msgid "%1$s: Used CSS cache cleared!"
msgstr "%1$s: Cache du CSS utilisé vidée!"

#: inc/Engine/Optimization/RUCSS/Controller/UsedCSS.php:776
msgid "Clear Used CSS of this URL"
msgstr "Nettoyer le CSS utilisé pour cet URL"

#: inc/Engine/Optimization/RUCSS/Cron/Subscriber.php:160
msgid "WP Rocket Remove Unused CSS pending jobs"
msgstr "Tâches en attente de l’option Supprimer les ressources CSS inutilisées de WP Rocket"

#: inc/Engine/Plugin/UpdaterApiTools.php:32
#: inc/deprecated/3.3.php:761
#: inc/deprecated/3.3.php:770
#, php-format
msgid "An unexpected error occurred. Something may be wrong with WP-Rocket.me or this server&#8217;s configuration. If you continue to have problems, <a href=\"%s\">contact support</a>."
msgstr "Une erreur inattendue s’est produite. Quelque chose ne va pas avec WP-Rocket.me ou avec la configuration de ce serveur. Si vous continuez à avoir des problèmes, <a href=\"%s\">contactez notre soutien technique</a>."

#: inc/Engine/Plugin/UpdaterSubscriber.php:472
#: inc/Engine/Plugin/UpdaterSubscriber.php:486
#: inc/classes/class-wp-rocket-requirements-check.php:203
#: inc/classes/class-wp-rocket-requirements-check.php:214
#: inc/deprecated/3.11.php:236
#: inc/deprecated/3.11.php:251
#, php-format
msgid "%s Update Rollback"
msgstr "Retour à la version %s"

#: inc/Engine/Plugin/UpdaterSubscriber.php:509
#: inc/deprecated/3.11.php:279
#, php-format
msgid "%1$sReturn to WP Rocket%2$s or %3$sgo to Plugins page%2$s"
msgstr "%1$sRetourner à WP Rocket%2$s ou %3$saller à la page des extensions%2$s"

#: inc/Engine/Preload/Admin/Settings.php:57
#, php-format
msgid "%1$s: The preload service is now active. After the initial preload it will continue to cache all your pages whenever they are purged. No further action is needed."
msgstr "%1$s: Le Préchargement est maintenant actif. Après le préchargement initial, il continuera à mettre en cache toutes vos pages chaque fois qu'elles seront purgées. Aucune autre action n'est nécessaire."

#: inc/Engine/Preload/Cron/Subscriber.php:138
msgid "WP Rocket Preload pending jobs"
msgstr "Tâches en attente de l'option Préchargement de WP Rocket"

#: inc/ThirdParty/Hostings/Cloudways.php:82
#, php-format
msgid "Varnish auto-purge will be automatically enabled once Varnish is enabled on your %s server."
msgstr "La purge automatiquement de Varnish sera automatiquement activée lorsque Varnish sera activé sur votre %s serveur."

#: inc/ThirdParty/Hostings/Kinsta.php:158
#, php-format
msgid "Your installation seems to be missing core Kinsta files managing Cache clearing, which will prevent your Kinsta installation and WP Rocket from working correctly. Please get in touch with Kinsta support through your %1$sMyKinsta%2$s account to resolve this issue."
msgstr "Des fichiers Kinsta de base, gérant l'effacement du cache et du CDN, semblent manquer dans votre installation, ce qui empêchera Kinsta et WP Rocket de fonctionner correctement. Veuillez contacter le support Kinsta via votre compte %1$sMyKinsta%2$s pour résoudre ce problème."

#: inc/ThirdParty/Plugins/CDN/Cloudflare.php:114
#, php-format
msgid "%1$s: Cloudflare's HTTP/2 Server Push is incompatible with the features of Remove Unused CSS and Combine CSS files. We strongly recommend disabling it."
msgstr "%1$s: Le Server Push HTTP/2 de Cloudflare est incompatible avec les options Supprimer les ressources CSS inutilisées et Combiner les fichier CSS. Nous vous recommandons vivement de la désactiver."

#: inc/ThirdParty/Plugins/ModPagespeed.php:102
#, php-format
msgid "<strong>%1$s</strong>: Mod PageSpeed is not compatible with this plugin and may cause unexpected results. %2$sMore Info%3$s"
msgstr "<strong>%1$s</strong>: Mod PageSpeed n'est pas compatible avec cette extension et peut causer des résultats inattendus. %2$sPlus d'Info%3$s"

#: inc/ThirdParty/Plugins/Optimization/Autoptimize.php:76
#, php-format
msgid "%1$sWP Rocket: %2$sWe have detected that Autoptimize's JavaScript Aggregation feature is enabled. WP Rocket's Delay JavaScript Execution will not be applied to the file it creates. We suggest disabling %1$sJavaScript Aggregation%2$s to take full advantage of Delay JavaScript Execution."
msgstr "%1$sWP Rocket : %2$sNous avons détecté que l'option Concaténer les fichiers JS d'Autoptimize est activée. L'option Reporter l'exécution JavaScript de WP Rocket ne sera pas appliquée au fichier qu'elle crée. Nous vous suggérons de désactiver %1$sConcaténer les fichiers JS%2$s  pour profiter pleinement de Reporter l'exécution JavaScript."

#: inc/ThirdParty/Plugins/Optimization/Autoptimize.php:131
#, php-format
msgid "%1$sWP Rocket: %2$sWe have detected that Autoptimize's Aggregate Inline CSS feature is enabled. WP Rocket's Load CSS Asynchronously will not work correctly. We suggest disabling %1$sAggregate Inline CSS%2$s to take full advantage of Load CSS Asynchronously Execution."
msgstr "%1$sWP Rocket : %2$sNous avons détecté que l'option Concaténer le CSS inline d'Autoptimize est activée. L'option Chargement Asychrone du CSS de WP Rocket ne sera pas appliquée au fichier qu'elle crée. Nous vous suggérons de désactiver %1$sConcaténer le CSS inline%2$s  pour profiter pleinement du Chargement Asychrone du CSS."

#: inc/ThirdParty/Plugins/Optimization/Ezoic.php:45
#, php-format
msgid "This plugin blocks WP Rocket's caching and optimizations. Deactivate it and use %1$sEzoic's nameserver integration%2$s instead."
msgstr "Cette extension empêche la mise en cache et les optimisations de WP Rocket. Désactivez-la et utilisez %1$sl'intégration du serveur de noms d'Ezoic%2$s à la place."

#: inc/ThirdParty/Plugins/Optimization/Hummingbird.php:78
#, php-format
msgctxt "Hummingbird notice"
msgid "Please deactivate the following %s option which conflicts with WP Rocket features:"
msgid_plural "Please deactivate the following %s options which conflict with WP Rocket features:"
msgstr[0] "Veuillez désactiver l'option %s qui entre en conflit avec les fonctionnalités de WP Rocket :"
msgstr[1] "Veuillez désactiver les %s options suivantes qui entrent en conflit avec les fonctionnalités de WP Rocket :"
msgstr[2] "Veuillez désactiver les %s options suivantes qui entrent en conflit avec les fonctionnalités de WP Rocket :"

#: inc/ThirdParty/Plugins/Optimization/Hummingbird.php:151
#, php-format
msgctxt "Hummingbird notice"
msgid "%1$s %2$sdisable emoji%3$s conflicts with WP Rockets %2$sdisable emoji%3$s"
msgstr "%1$s %2$sdésactivation des emojis%3$s entre en conflit avec la %2$sdésactivation des emojis%3$s de WP Rocket"

#: inc/ThirdParty/Plugins/Optimization/Hummingbird.php:187
#, php-format
msgctxt "Hummingbird notice"
msgid "%1$s %2$sGZIP compression%3$s conflicts with WP Rocket %2$sGZIP compression%3$s"
msgstr "%1$s %2$scompression GZIP%3$s entre en conflit avec la %2$scompression GZIP%3$s avec WP Rocket"

#: inc/ThirdParty/Plugins/Optimization/Hummingbird.php:223
#, php-format
msgctxt "Hummingbird notice"
msgid "%1$s %2$sbrowser caching%3$s conflicts with WP Rocket %2$sbrowser caching%3$s"
msgstr "%1$s %2$scaching du fureteur%3$s entre en conflit avec le %2$scaching du fureteur%3$s de WP Rocket"

#: inc/ThirdParty/Plugins/Optimization/Hummingbird.php:255
#, php-format
msgctxt "Hummingbird notice"
msgid "%1$s %2$spage caching%3$s conflicts with WP Rocket %2$spage caching%3$s"
msgstr "%1$s %2$scaching de page%3$s entre en conflit le %2$spage caching%3$s de WP Rocket"

#: inc/ThirdParty/Plugins/Optimization/Hummingbird.php:289
#, php-format
msgctxt "Hummingbird notice"
msgid "%1$s %2$sasset optimization%3$s conflicts with WP Rocket %2$sfile optimization%3$s"
msgstr "%1$s %2$sl'optimisation des fichiers%3$s entre en conflit avec %2$sl'optimisation des fichiers%3$s de WP Rocket"

#: inc/ThirdParty/Plugins/Optimization/WPMeteor.php:42
#, php-format
msgid "Delay JS is currently activated in %1$s. If you want to use WP Rocket’s delay JS, disable %1$s"
msgstr "Le report de l'exécution des JS est activée dans %1$s. Si vous voulez utiliser le Report de l’exécution JavaScript de WP Rocket, désactivez %1$s"

#: inc/ThirdParty/Plugins/Smush.php:108
#: inc/ThirdParty/Plugins/Smush.php:126
msgid "Smush"
msgstr "Smush"

#: inc/ThirdParty/Themes/Avada.php:116
msgid "Avada"
msgstr "Avada"

#: inc/admin/admin.php:18
#: inc/common/admin-bar.php:255
#: inc/deprecated/deprecated.php:1787
msgid "Support"
msgstr "Soutien"

#: inc/admin/admin.php:20
msgid "Docs"
msgstr "Documents"

#: inc/admin/admin.php:22
#: inc/common/admin-bar.php:243
msgid "FAQ"
msgstr "FAQ"

#: inc/admin/admin.php:24
#: inc/common/admin-bar.php:63
msgid "Settings"
msgstr "Réglages"

#: inc/admin/admin.php:458
msgid "Settings import failed: you do not have the permissions to do this."
msgstr "L'import des réglages a échoué : vous n'avez pas les permissions nécessaires pour faire ceci."

#: inc/admin/admin.php:462
msgid "Settings import failed: no file uploaded."
msgstr "L'import des réglages a échoué : aucun fichier téléversé."

#: inc/admin/admin.php:466
msgid "Settings import failed: incorrect filename."
msgstr "L'import des réglages a échoué : nom de fichier incorrect."

#: inc/admin/admin.php:477
msgid "Settings import failed: incorrect filetype."
msgstr "L'import des réglages a échoué : type de fichier incorrect."

#: inc/admin/admin.php:487
msgid "Settings import failed: "
msgstr "Échec de l'import des réglages :"

#: inc/admin/admin.php:503
msgid "Settings import failed: unexpected file content."
msgstr "Échec de l'import des réglages : contenu du fichier incorrect."

#: inc/admin/admin.php:533
msgid "Settings imported and saved."
msgstr "Réglages importés et sauvegardés."

#: inc/admin/options.php:127
msgid "Defer JavaScript Files"
msgstr "Charger le JavaScript en différé"

#: inc/admin/options.php:128
msgid "Excluded Delay JavaScript Files"
msgstr "Fichiers JavaScript exclus du report de l'éxecution"

#: inc/admin/options.php:150
#, php-format
msgid "%1$s: <em>%2$s</em>"
msgstr "%1$s: <em>%2$s</em>"

#: inc/admin/options.php:160
msgid "The following pattern is invalid and has been removed:"
msgid_plural "The following patterns are invalid and have been removed:"
msgstr[0] "Le modèle suivant est non-valide et a été retiré :"
msgstr[1] "Les modèles suivants sont non-valides et ont été retirés :"
msgstr[2] "Les modèles suivants sont non-valides et ont été retirés :"

#: inc/admin/options.php:176
msgid "More info"
msgstr "Plus d'infos"

#: inc/admin/ui/meta-boxes.php:37
#: inc/admin/ui/notices.php:748
#: inc/common/admin-bar.php:84
#: inc/common/admin-bar.php:136
#: views/settings/page-sections/dashboard.php:150
msgid "Clear cache"
msgstr "Vider le cache"

#: inc/admin/ui/meta-boxes.php:67
msgid "WP Rocket Options"
msgstr "Options WP Rocket"

#: inc/admin/ui/meta-boxes.php:96
msgid "Never cache this page"
msgstr "Ne jamais mettre en cache cette page"

#: inc/admin/ui/meta-boxes.php:100
msgid "Activate these options on this post:"
msgstr "Activer ces options sur cette page :"

#: inc/admin/ui/meta-boxes.php:103
msgid "LazyLoad for images"
msgstr "Chargement différé des images"

#: inc/admin/ui/meta-boxes.php:104
msgid "LazyLoad for iframes/videos"
msgstr "Chargement différé des iframes et vidéos"

#: inc/admin/ui/meta-boxes.php:105
msgid "Minify CSS"
msgstr "Minifier le CSS"

#: inc/admin/ui/meta-boxes.php:107
msgid "Minify/combine JS"
msgstr "Minifier / combiner le JS"

#: inc/admin/ui/meta-boxes.php:110
msgid "Defer JS"
msgstr "Différer le JS"

#: inc/admin/ui/meta-boxes.php:117
#, php-format
msgid "Activate first the %s option."
msgstr "Activer d’abord l’option de %s."

#: inc/admin/ui/meta-boxes.php:133
#, php-format
msgid "%1$sNote:%2$s None of these options will be applied if this post has been excluded from cache in the global cache settings."
msgstr "%1$sNote:%2$s Aucune de ces options ne sera appliquée à ce contenu s'il a été exclu des paramètres globaux de gestion du cache."

#: inc/admin/ui/notices.php:31
#: inc/admin/ui/notices.php:44
#, php-format
msgid ""
"<strong>%1$s</strong> has not been deactivated due to missing writing permissions.<br>\n"
"Make <strong>%2$s</strong> writeable and retry deactivation, or force deactivation now:"
msgstr ""
"<strong>%1$s</strong> n'a pas été désactivé en raisons de l'absence des permissions d'écriture.<br>\n"
"Rendez <strong>%2$s</strong> inscriptible et réessayez de désactiver, ou forcez la désactivation maintenant :"

#: inc/admin/ui/notices.php:97
#, php-format
msgid "<strong>%s</strong>: One or more plugins have been enabled or disabled, clear the cache if they affect the front end of your site."
msgstr "<strong>%s</strong>: Une ou plusieurs extensions ont été activées ou désactivées, videz le cache si elles influent sur l’apparence de votre site."

#: inc/admin/ui/notices.php:218
#, php-format
msgid "<strong>%s</strong>: The following plugins are not compatible with this plugin and may cause unexpected results:"
msgstr "<strong>%s</strong>: Les extensions suivantes ne sont pas compatibles avec cette extension et peuvent générer des résultats inattendus :"

#: inc/admin/ui/notices.php:224
msgid "Deactivate"
msgstr "Désactiver"

#: inc/admin/ui/notices.php:266
msgid "WP Rocket Footer JS is not an official add-on. It prevents some options in WP Rocket from working correctly. Please deactivate it if you have problems."
msgstr "WP Rocket Footer JS n'est pas un ajout officiel. Il prévient certaines options dans WP Rocket de fonctionner correctement. Veuillez le désactiver si vous avez des problèmes."

#: inc/admin/ui/notices.php:306
#, php-format
msgid "Endurance Cache is currently enabled, which will conflict with WP Rocket Cache. Please set the Endurance Cache cache level to Off (Level 0) on the %1$sSettings > General%2$s page to prevent any issues."
msgstr "Endurance Cache est présentement activé, ce qui peut causer conflit avec le cache de WP Rocket. Veuillez configurer le niveau de Endurance Cache à \"Off\" (Niveau 0) sur la page %1$sParamètres > Général%2$s afin de prévenir tout problème."

#: inc/admin/ui/notices.php:327
#, php-format
msgid "%1$s: A custom permalink structure is required for the plugin to work properly. %2$sGo to permalinks settings%3$s"
msgstr "%1$s: Une structure de permalien personnalisée est requise pour que cette extension fonctionne. %2$sRendez-vous aux réglages des permaliens%3$s"

#: inc/admin/ui/notices.php:374
#, php-format
msgid "%s could not modify the .htaccess file due to missing writing permissions."
msgstr "%s n'a pas pu modifier le fichier .htaccess dû à l'absence de permissions d'écriture.."

#: inc/admin/ui/notices.php:380
#: inc/admin/ui/notices.php:843
#, php-format
msgid "Troubleshoot: %1$sHow to make system files writeable%2$s"
msgstr "Pour résoudre le problème : %1$sComment rendre les fichiers systèmes accessible à l'écriture%2$s"

#: inc/admin/ui/notices.php:382
#: inc/admin/ui/notices.php:845
msgid "https://docs.wp-rocket.me/article/626-how-to-make-system-files-htaccess-wp-config-writeable/?utm_source=wp_plugin&utm_medium=wp_rocket"
msgstr "https://fr.docs.wp-rocket.me/article/945-fichiers-systemes-accessible-ecriture/?utm_source=wp_plugin&utm_medium=wp_rocket"

#: inc/admin/ui/notices.php:388
msgid "Don’t worry, WP Rocket’s page caching and settings will still function correctly."
msgstr "Pas d'inquiétude, la mise en cache de pages et les paramètres fonctionneront correctement."

#: inc/admin/ui/notices.php:388
msgid "For optimal performance, adding the following lines into your .htaccess is recommended (not required):"
msgstr "Pour une performance optimale, il est recommandé d'ajouter les lignes suivantes à votre fichier .htaccess (optionnel) :"

#: inc/admin/ui/notices.php:535
#, php-format
msgid "%1$s is good to go! %2$sTest your load time%4$s, or visit your %3$ssettings%4$s."
msgstr "%1$s est prêt à décoller ! %2$sTestez votre temps de chargement%4$s, ou jetez un oeil aux %3$sréglages%4$s."

#: inc/admin/ui/notices.php:576
msgid "Would you allow WP Rocket to collect non-sensitive diagnostic data from this website?"
msgstr "Autorisez-vous WP Rocket à recueillir des données de diagnostic non-sensibles de ce site web ?"

#: inc/admin/ui/notices.php:577
msgid "This would help us to improve WP Rocket for you in the future."
msgstr "Cela nous aiderait à améliorer WP Rocket pour vous dans le futur."

#: inc/admin/ui/notices.php:583
msgid "What info will we collect?"
msgstr "Quelles infos recueillerons-nous ?"

#: inc/admin/ui/notices.php:588
msgid "Below is a detailed view of all data WP Rocket will collect if granted permission. WP Rocket will never transmit any domain names or email addresses (except for license validation), IP addresses, or third-party API keys."
msgstr "Vous trouverez ci-dessous une vue détaillée de toutes les données que WP Rocket recueillera si vous nous le permettez. WP Rocket ne transmettra jamais de noms de domaine ou d'adresses courriel (sauf pour la validation de licence), d'adresses IP ou de clés d’API tierce-partie."

#: inc/admin/ui/notices.php:597
msgid "Yes, allow"
msgstr "Oui, autoriser"

#: inc/admin/ui/notices.php:600
msgid "No, thanks"
msgstr "Non, merci"

#: inc/admin/ui/notices.php:639
msgid "Thank you!"
msgstr "Merci!"

#: inc/admin/ui/notices.php:644
msgid "WP Rocket now collects these metrics from your website:"
msgstr "WP Rocket recueille désormais ces données de votre site web :"

#: inc/admin/ui/notices.php:682
#, php-format
msgid "%s: Cache cleared."
msgstr "%s : Cache vidé."

#: inc/admin/ui/notices.php:689
#, php-format
msgid "%s: Post cache cleared."
msgstr "%s : Cache des articles vidé."

#: inc/admin/ui/notices.php:696
#, php-format
msgid "%s: Term cache cleared."
msgstr "%s : Cache des termes vidé."

#: inc/admin/ui/notices.php:703
#, php-format
msgid "%s: User cache cleared."
msgstr "%s : Cache des utilisateurs vidé."

#: inc/admin/ui/notices.php:751
msgid "Stop Preload"
msgstr "Cesser le pré-chargement"

#: inc/admin/ui/notices.php:781
msgid "Force deactivation "
msgstr "Forcer la désactivation "

#: inc/admin/ui/notices.php:800
msgid "The following code should have been written to this file:"
msgstr "Le code suivant aurait dû être écrit dans ce fichier :"

#: inc/admin/ui/notices.php:831
#, php-format
msgid "%s cannot configure itself due to missing writing permissions."
msgstr "%s ne peut pas s’auto-configurer en raison de l'absence de permissions en écriture."

#: inc/admin/ui/notices.php:837
#, php-format
msgid "Affected file/folder: %s"
msgstr "Fichier/dossier affecté : %s"

#: inc/classes/admin/class-logs.php:124
msgid "The debug file could not be deleted."
msgstr "Le fichier de debug n'a pu être effacé."

#: inc/classes/class-wp-rocket-requirements-check.php:147
#, php-format
msgid "To function properly, %1$s %2$s requires at least:"
msgstr "Pour fonctionner correctement, %1$s %2$s nécessite au moins :"

#: inc/classes/class-wp-rocket-requirements-check.php:151
#, php-format
msgid "PHP %1$s. To use this WP Rocket version, please ask your web host how to upgrade your server to PHP %1$s or higher."
msgstr "PHP %1$s. Pour utiliser cette version de WP Rocket, demandez à votre hébergeur comment mettre à niveau votre serveur vers PHP %1$s ou supérieur."

#: inc/classes/class-wp-rocket-requirements-check.php:156
#, php-format
msgid "WordPress %1$s. To use this WP Rocket version, please upgrade WordPress to version %1$s or higher."
msgstr "WordPress %1$s. Pour utiliser cette version de WP Rocket, mettez à jour WordPress à la version %1$s ou plus récente."

#: inc/classes/class-wp-rocket-requirements-check.php:159
msgid "If you are not able to upgrade, you can rollback to the previous version by using the button below."
msgstr "Si vous n'êtes pas en mesure d’effectuer le rehaussement, vous pouvez restaurer WP Rocket à sa version précédente en utilisant le bouton ci-dessous."

#: inc/classes/class-wp-rocket-requirements-check.php:161
#: inc/deprecated/deprecated.php:1947
#, php-format
msgid "Re-install version %s"
msgstr "Réinstaller la version %s"

#: inc/classes/dependencies/wp-media/background-processing/wp-background-process.php:447
#, php-format
msgid "Every %d Minutes"
msgstr "Toutes les %d minutes"

#: inc/classes/logger/class-logger.php:260
#: inc/classes/logger/class-logger.php:292
msgid "The log file does not exist."
msgstr "Le fichier de log n'existe pas."

#: inc/classes/logger/class-logger.php:266
#: inc/classes/logger/class-logger.php:298
msgid "The log file could not be read."
msgstr "Le fichier de log n'a pu être lu."

#: inc/classes/logger/class-logger.php:285
msgid "The logs are not saved into a file."
msgstr "Les logs n'ont pu être sauvés dans un fichier."

#: inc/Addon/WebP/AdminSubscriber.php:93
#, php-format
msgid "You are using %1$s to serve WebP images so you do not need to enable this option. %2$sMore info%3$s %4$s If you prefer to have WP Rocket serve WebP for you instead, please disable WebP display in %1$s."
msgid_plural "You are using %1$s to serve WebP images so you do not need to enable this option. %2$sMore info%3$s %4$s If you prefer to have WP Rocket serve WebP for you instead, please disable WebP display in %1$s."
msgstr[0] "Vous utilisez %1$s pour servir les images WebP, vous n'avez donc pas besoin d'activer cette option. %2$sPlus d'infos%3$s %4$s Si vous préférez que WP Rocket serve les WebP pour vous, veuillez désactiver l’affichage des WebP de %1$s. "
msgstr[1] "Vous utilisez %1$s pour servir les images WebP, vous n'avez donc pas besoin d'activer cette option. %2$sPlus d'infos%3$s %4$s Si vous préférez que WP Rocket serve les WebP pour vous, veuillez désactiver l’affichage des WebP de %1$s. "
msgstr[2] "Vous utilisez %1$s pour servir les images WebP, vous n'avez donc pas besoin d'activer cette option. %2$sPlus d'infos%3$s %4$s Si vous préférez que WP Rocket serve les WebP pour vous, veuillez désactiver l’affichage des WebP de %1$s. "

#: inc/Addon/WebP/AdminSubscriber.php:105
msgid "WebP cache is disabled by filter."
msgstr "La mise en cache de WebP est désactivée par filtre."

#: inc/Addon/WebP/AdminSubscriber.php:115
#: inc/Addon/WebP/AdminSubscriber.php:141
#, php-format
msgid "You are using %1$s to convert images to WebP. If you want WP Rocket to serve them for you, activate this option. %2$sMore info%3$s"
msgid_plural "You are using %1$s to convert images to WebP. If you want WP Rocket to serve them for you, activate this option. %2$sMore info%3$s"
msgstr[0] "Vous utilisez %1$s pour convertir vos images à WebP. Si vous voulez que WP Rocket les serve pour vous, activez cette options. %2$sPlus d'info %3$s"
msgstr[1] "Vous utilisez %1$s pour convertir vos images à WebP. Si vous voulez que WP Rocket les serve pour vous, activez cette options. %2$sPlus d'info %3$s"
msgstr[2] "Vous utilisez %1$s pour convertir vos images à WebP. Si vous voulez que WP Rocket les serve pour vous, activez cette options. %2$sPlus d'info %3$s"

#: inc/Addon/WebP/AdminSubscriber.php:127
#: inc/Addon/WebP/AdminSubscriber.php:153
#, php-format
msgid "You are using %1$s to convert images to WebP. WP Rocket will create separate cache files to serve your WebP images. %2$sMore info%3$s"
msgid_plural "You are using %1$s to convert images to WebP. WP Rocket will create separate cache files to serve your WebP images. %2$sMore info%3$s"
msgstr[0] "Vous utilisez %1$s pour convertir des images à WebP. WP Rocket va créer des fichiers de cache séparés afin de servir vos images WebP. %2$sPlus d'info %3$s"
msgstr[1] "Vous utilisez %1$s pour convertir des images à WebP. WP Rocket va créer des fichiers de cache séparés afin de servir vos images WebP. %2$sPlus d'info %3$s"
msgstr[2] "Vous utilisez %1$s pour convertir des images à WebP. WP Rocket va créer des fichiers de cache séparés afin de servir vos images WebP. %2$sPlus d'info %3$s"

#: inc/Addon/WebP/AdminSubscriber.php:173
#, php-format
msgid "%5$sWe have not detected any compatible WebP plugin!%6$s%4$s If you don’t already have WebP images on your site consider using %3$sImagify%2$s or another supported plugin. %1$sMore info%2$s %4$s If you are not using WebP do not enable this option."
msgstr "%5$sNous n'avons pas détecté de plugin WebP compatible !%6$s%4$s Si vous n'avez pas encore d'images WebP sur votre site, envisagez d'utiliser %3$sImagify%2$s ou une autre extension supportée. %1$sPlus d'infos%2$s %4$s Si vous n'utilisez pas WebP, n'activez pas cette option."

#: inc/Addon/WebP/AdminSubscriber.php:185
msgid "WP Rocket will create separate cache files to serve your WebP images."
msgstr "WP Rocket va créer des fichier de cache séparés afin de servir vos images WebP"

#: inc/classes/subscriber/Tools/class-detect-missing-tags-subscriber.php:151
#, php-format
msgid "Failed to detect the following requirement in your theme: closing %1$s."
msgid_plural "Failed to detect the following requirements in your theme: closing %1$s."
msgstr[0] "Échec de la détection du requis suivant de votre thème : fermer %1$s."
msgstr[1] "Échec de la détection des requis suivants de votre thème : fermer %1$s."
msgstr[2] "Échec de la détection des requis suivants de votre thème : fermer %1$s."

#: inc/classes/subscriber/Tools/class-detect-missing-tags-subscriber.php:159
msgid "https://docs.wp-rocket.me/article/99-pages-not-cached-or-minify-cssjs-not-working/?utm_source=wp_plugin&utm_medium=wp_rocket#theme"
msgstr "https://docs.wp-rocket.me/article/99-pages-not-cached-or-minify-cssjs-not-working/?utm_source=wp_plugin&utm_medium=wp_rocket#theme"

#: inc/common/admin-bar.php:84
#: inc/common/admin-bar.php:136
#: views/settings/page-sections/dashboard.php:150
msgid "Clear and preload cache"
msgstr "Vider et précharger le cache"

#: inc/common/admin-bar.php:124
#: inc/functions/i18n.php:41
#: inc/functions/i18n.php:51
msgid "All languages"
msgstr "Toutes les langues"

#: inc/common/admin-bar.php:160
msgid "Clear this post"
msgstr "Vider cet article"

#: inc/common/admin-bar.php:174
msgid "Purge this URL"
msgstr "Purger cet URL"

#: inc/common/admin-bar.php:194
msgid "Purge Sucuri cache"
msgstr "Purger le cache Sucuri"

#: inc/common/admin-bar.php:218
#: views/settings/fields/rocket-cdn.php:55
msgid "Purge RocketCDN cache"
msgstr "Purger le cache RocketCDN"

#: inc/common/admin-bar.php:231
#: views/settings/partials/documentation.php:14
msgid "Documentation"
msgstr "Documentation"

#: inc/deprecated/3.10.php:163
msgid "OPcache purge failed."
msgstr "La purge de OpCache a échoué."

#: inc/deprecated/3.10.php:168
msgid "OPcache successfully purged"
msgstr "OpCache purgé avec succès"

#: inc/deprecated/3.11.php:81
msgid "Yoast SEO XML sitemap"
msgstr "Sitemap XML Yoast SEO"

#: inc/deprecated/3.11.php:83
#: inc/deprecated/3.12.php:58
#: inc/deprecated/3.12.php:87
#: inc/deprecated/3.12.php:186
#: inc/deprecated/3.12.php:253
#: inc/deprecated/3.12.php:342
#, php-format
msgid "We automatically detected the sitemap generated by the %s plugin. You can check the option to preload it."
msgstr "Nous avons automatiquement détecté le sitemap généré par l'extension %s. Vous pouvez cocher l'option pour l'inclure dans le préchargement."

#: inc/deprecated/3.12.php:56
msgid "All in One SEO XML sitemap"
msgstr "Sitemap XML All in One SEO"

#: inc/deprecated/3.12.php:85
msgid "Rank Math XML sitemap"
msgstr "Sitemap XML de Rank Math"

#: inc/deprecated/3.12.php:184
msgid "SEOPress XML sitemap"
msgstr "Sitemap XML SEOPress"

#: inc/deprecated/3.12.php:251
msgid "The SEO Framework XML sitemap"
msgstr "Le sitemap XML de The SEO Framework"

#: inc/deprecated/3.12.php:332
msgid "Jetpack XML Sitemaps"
msgstr "Sitemaps XML Jetpack"

#: inc/deprecated/3.12.php:334
msgid "Preload the sitemap from the Jetpack plugin"
msgstr "Précharger le sitemap de l'extension Jetpack"

#: inc/deprecated/3.2.php:52
#: views/settings/page-sections/imagify.php:36
msgid "Activate Imagify"
msgstr "Activer Imagify"

#: inc/deprecated/3.2.php:52
msgid "Install Imagify for Free"
msgstr "Installer Imagify gratuitement"

#: inc/deprecated/3.2.php:67
msgid "Speed up your website and boost your SEO by reducing image file sizes without losing quality with Imagify."
msgstr "Accélérez votre site web et améliorez votre référencement en réduisant le poids de vos image sans perte de qualité avec Imagify."

#: inc/deprecated/3.2.php:72
msgid "More details"
msgstr "Plus de détails"

#: inc/deprecated/3.2.php:228
#, php-format
msgid "Sitemap preload: %d pages have been cached."
msgstr "Pré-chargement du Sitemap : %d pages ont été mises en cache."

#: inc/deprecated/3.2.php:261
#, php-format
msgid "Sitemap preload: %d uncached pages have now been preloaded. (refresh to see progress)"
msgstr "Pré-chargement du Sitemap : %d pages ont été pré-chargées. (rafraîchir pour voir la progression)"

#: inc/deprecated/3.4.php:18
msgid "Choose a domain from the list"
msgstr "Choisissez un domaine dans la liste"

#: inc/deprecated/3.4.php:31
msgid "No domain available in your Cloudflare account"
msgstr "Aucun domaine disponible dans votre compte Cloudflare"

#: inc/deprecated/3.5.php:71
#: inc/deprecated/3.5.php:195
msgid "Curl is disabled on your server. Please ask your host to enable it. This is required for the Cloudflare Add-on to work correctly."
msgstr "CURL est désactivé sur votre serveur. Veuillez demander à votre hébergeur de l'activer. Ceci est requis pour que l'ajout Cloudflare fonctionne correctement."

#: inc/deprecated/3.5.php:79
#, php-format
msgid "Cloudflare email, API key and Zone ID are not set. Read the %1$sdocumentation%2$s for further guidance."
msgstr "Le courriel, la clé d'API et le Zone ID de Cloudflare de sont pas définis. Lisez la %1$sdocumentation%2$s pour plus d'assistance."

#: inc/deprecated/3.5.php:206
#, php-format
msgid "Cloudflare email and API key are not set. Read the %1$sdocumentation%2$s for further guidance."
msgstr "Le courriel et la clé d'API de Cloudflare ne sont pas configurés. Lisez la %1$sdocumentation%2$s pour plus d'assistance."

#: inc/deprecated/3.5.php:271
msgid "Connection to Cloudflare failed"
msgstr "La connexion à Cloudflare a échoué"

#: inc/deprecated/DeprecatedClassTrait.php:54
#, php-format
msgid "The called class %1$s is <strong>deprecated</strong> since version %2$s! Use %3$s instead."
msgstr "La classe appelée %1$s est <strong>obsolète</strong> depuis la version %2$s! Utilisez %3$s à la place."

#: inc/deprecated/DeprecatedClassTrait.php:65
#, php-format
msgid "The called class %1$s is <strong>deprecated</strong> since version %2$s!"
msgstr "La classe appelée %1$s est <strong>obsolète</strong> depuis la version %2$s!"

#: inc/deprecated/deprecated.php:240
msgid "<strong>JS</strong> files with Deferred Loading JavaScript"
msgstr "Fichiers <strong>JS</strong> en chargement différé du JavaScript"

#: inc/deprecated/deprecated.php:293
msgid "Add URL"
msgstr "Ajouter un URL"

#: inc/deprecated/deprecated.php:1277
#: views/settings/fields/import-form.php:22
msgid "Before you can upload your import file, you will need to fix the following error:"
msgstr "Avant de pouvoir envoyer votre fichier d’import, vous devez régler l’erreur suivante :"

#: inc/deprecated/deprecated.php:1288
#: views/settings/fields/import-form.php:35
#, php-format
msgid "Choose a file from your computer (maximum size: %s)"
msgstr "Choisissez un fichier de votre ordinateur (poids maximum : %s)"

#: inc/deprecated/deprecated.php:1385
msgid "Your Cloudflare credentials are valid."
msgstr "Vos accès Cloudflare sont valides."

#: inc/deprecated/deprecated.php:1389
msgid "Your Cloudflare credentials are invalid!"
msgstr "Vos accès Cloudflare sont invalides!"

#: inc/deprecated/deprecated.php:1453
msgid "Save and optimize"
msgstr "Sauvegarder et optimiser"

#: inc/deprecated/deprecated.php:1453
msgid "Optimize"
msgstr "Optimiser"

#: inc/deprecated/deprecated.php:1464
#: inc/deprecated/deprecated.php:1666
msgctxt "screen-reader-text"
msgid "Note:"
msgstr "Note :"

#: inc/deprecated/deprecated.php:1470
msgctxt "screen-reader-text"
msgid "Performance tip:"
msgstr "Truc de performance :"

#: inc/deprecated/deprecated.php:1476
msgctxt "screen-reader-text"
msgid "Third-party feature detected:"
msgstr "Autre fonctionnalité détectée :"

#: inc/deprecated/deprecated.php:1488
#: inc/deprecated/deprecated.php:1672
msgctxt "screen-reader-text"
msgid "Warning:"
msgstr "Attention : "

#: inc/deprecated/deprecated.php:1501
msgctxt "button text"
msgid "Download settings"
msgstr "Télécharger les réglages"

#: inc/deprecated/deprecated.php:1531
msgid "Replace site's hostname with:"
msgstr "Remplacer le nom d’hôte du site par :"

#: inc/deprecated/deprecated.php:1550
#: inc/deprecated/deprecated.php:1587
#: inc/deprecated/deprecated.php:1615
#: views/settings/fields/cnames.php:41
#: views/settings/fields/cnames.php:75
#: views/settings/fields/cnames.php:105
msgid "reserved for"
msgstr "réservé pour"

#: inc/deprecated/deprecated.php:1552
#: inc/deprecated/deprecated.php:1589
#: inc/deprecated/deprecated.php:1617
#: views/settings/fields/cnames.php:44
#: views/settings/fields/cnames.php:78
#: views/settings/fields/cnames.php:108
msgid "All files"
msgstr "Tous les fichiers"

#: inc/deprecated/deprecated.php:1564
#: inc/deprecated/deprecated.php:1594
#: inc/deprecated/deprecated.php:1622
#: views/settings/fields/cnames.php:56
#: views/settings/fields/cnames.php:90
#: views/settings/fields/cnames.php:120
msgid "Images"
msgstr "Images"

#: inc/deprecated/deprecated.php:1635
#: views/settings/fields/cnames.php:132
msgid "Add CNAME"
msgstr "Ajouter un CNAME"

#: inc/deprecated/deprecated.php:1706
msgid "Watch the video"
msgstr "Voir la vidéo"

#: inc/deprecated/deprecated.php:1771
msgid "Basic"
msgstr "Base"

#: inc/deprecated/deprecated.php:1772
msgid "Static Files"
msgstr "Fichiers statiques"

#: inc/deprecated/deprecated.php:1774
msgid "Advanced"
msgstr "Avancé"

#: inc/deprecated/deprecated.php:1944
#, php-format
msgid "%1$s %2$s requires at least PHP %3$s to function properly. To use this version, please ask your web host how to upgrade your server to PHP %3$s or higher. If you are not able to upgrade, you can rollback to the previous version by using the button below."
msgstr "%1$s %2$s requiert au moins PHP %3$s pour fonctionner correctement. Pour utiliser cette version, demandez à votre hébergeur de mettre à jour votre serveur vers PHP %3$s ou plus. Si cette mise-à-jour n'est pas possible, vous pouvez revenir à la version précédente en utilisant le bouton ci-dessous."

#: inc/functions/admin.php:21
msgid "There seems to be an issue validating your license. Please see the error message below."
msgid_plural "There seems to be an issue validating your license. You can see the error messages below."
msgstr[0] "Il semble y avoir un problème à confirmer votre licence. Vous pouvez voir le message d'erreur ci-dessous."
msgstr[1] "Il semble y avoir un problème à confirmer votre licence. Vous pouvez voir les messages d'erreur ci-dessous."
msgstr[2] "Il semble y avoir un problème à confirmer votre licence. Vous pouvez voir les messages d'erreur ci-dessous."

#: inc/functions/admin.php:361
msgid "Server type:"
msgstr "Type de serveur :"

#: inc/functions/admin.php:370
msgid "PHP version number:"
msgstr "Version PHP :"

#: inc/functions/admin.php:379
msgid "WordPress version number:"
msgstr "Version de WordPress :"

#: inc/functions/admin.php:388
msgid "WordPress multisite:"
msgstr "WordPress Multisite :"

#: inc/functions/admin.php:397
msgid "Current theme:"
msgstr "Thème actif :"

#: inc/functions/admin.php:406
msgid "Current site language:"
msgstr "Langage du site :"

#: inc/functions/admin.php:415
msgid "Active plugins:"
msgstr "Extensions actives :"

#: inc/functions/admin.php:418
msgid "Plugin names of all active plugins"
msgstr "Noms de toutes les extensions actives"

#: inc/functions/admin.php:424
msgid "Anonymized WP Rocket settings:"
msgstr "Réglages de WP Rocket anonymisés :"

#: inc/functions/admin.php:427
msgid "Which WP Rocket settings are active"
msgstr "Quels sont les réglages activés de WP Rocket"

#: inc/functions/options.php:429
msgid "The provided license data are not valid."
msgstr "Les données de la licence fournies ne sont pas valides."

#: inc/functions/options.php:432
#, php-format
msgid "To resolve, please %1$scontact support%2$s."
msgstr "Pour résoudre, veuillez %1$scontacter le soutien%2$s."

#: inc/functions/options.php:491
#: inc/functions/options.php:530
msgid "License validation failed. Our server could not resolve the request from your website."
msgstr "Échec de la validation de la licence. Notre serveur ne peut compléter la requête de votre site web."

#: inc/functions/options.php:491
#, php-format
msgid "Try clicking %1$sValidate License%2$s below. If the error persists, follow %3$sthis guide%4$s."
msgstr "Essayez de cliquer sur %1$sValider la license%2$s ci-dessous. Si l’erreur persiste, suivez les instructions de %3$sce guide%4$s."

#: inc/functions/options.php:507
msgid "License validation failed. You may be using a nulled version of the plugin. Please do the following:"
msgstr "La validation de votre licence a échoué. Vous utilisez peut-être une version piratée de cette extension. Veuillez suivre les points suivants :"

#: inc/functions/options.php:507
#: inc/functions/options.php:549
#, php-format
msgid "Login to your WP Rocket %1$saccount%2$s"
msgstr "Connectez-vous à votre %1$scompte%2$s WP Rocket"

#: inc/functions/options.php:507
#: inc/functions/options.php:549
msgid "Download the zip file"
msgstr "Télécharger le fichier ZIP"

#: inc/functions/options.php:507
#: inc/functions/options.php:549
msgid "Reinstall"
msgstr "Réinstaller"

#: inc/functions/options.php:507
#, php-format
msgid "If you do not have a WP Rocket account, please %1$spurchase a license%2$s."
msgstr "Si vous n'avez pas de compte WP Rocket, svp %1$sacheter une licence%2$s."

#: inc/functions/options.php:515
msgid "License validation failed. This user account does not exist in our database."
msgstr "Échec de validation de la licence. Le compte d'usager n'existe pas dans notre base de données."

#: inc/functions/options.php:515
msgid "To resolve, please contact support."
msgstr "Pour résoudre, svp contacter le soutien technique."

#: inc/functions/options.php:523
msgid "License validation failed. This user account is blocked."
msgstr "Échec de validation de la licence. Ce compte usager est sur notre liste noire."

#: inc/functions/options.php:523
#, php-format
msgid "Please see %1$sthis guide%2$s for more info."
msgstr "Veuillez consulter %1$sce guide%2$s pour plus d'info."

#: inc/functions/options.php:530
#, php-format
msgid "Try clicking %1$sSave Changes%2$s below. If the error persists, follow %3$sthis guide%4$s."
msgstr "Essayez de cliquer %1$sSauvegarder les changements%2$s ci-dessous. Si l'erreur persiste, suivez %3$sce guide%4$s."

#: inc/functions/options.php:543
msgid "Your license is not valid."
msgstr "Votre licence n’est pas valide."

#: inc/functions/options.php:543
#, php-format
msgid "Make sure you have an active %1$sWP Rocket license%2$s."
msgstr "Assurez-vous d'avoir une licence %1$sactive de WP Rocket %2$s."

#: inc/functions/options.php:545
msgid "You have added as many sites as your current license allows."
msgstr "Vous avez ajouté autant de sites que votre licence actuelle le permet."

#: inc/functions/options.php:545
#, php-format
msgid "Upgrade your %1$saccount%2$s or %3$stransfer your license%2$s to this domain."
msgstr "Rehausser votre %1$scompte%2$s ou %3$stransférer votre licence%2$s à ce domaine."

#: inc/functions/options.php:547
msgid "This website is not allowed."
msgstr "Ce site n'est pas autorisé."

#: inc/functions/options.php:547
#, php-format
msgid "Please %1$scontact support%2$s."
msgstr "Svp %1$scontacter le soutien technique%2$s."

#: inc/functions/options.php:549
msgid "This license key is not recognized."
msgstr "La clé de licence n'est pas reconnue."

#: inc/functions/options.php:549
#, php-format
msgid "If the issue persists, please %1$scontact support%2$s."
msgstr "Si le problème persiste, svp %1$scontacter le soutien technique%2$s."

#: inc/functions/options.php:555
#, php-format
msgid "License validation failed: %s"
msgstr "Échec de validation de la licence : %s"

#: inc/vendors/classes/class-imagify-partner.php:531
msgid "Plugin installed successfully."
msgstr "Extension installée avec succés."

#: inc/vendors/classes/class-imagify-partner.php:532
msgid "Sorry, you are not allowed to install plugins on this site."
msgstr "Désolé, vous n'avez pas les droits requis pour installer des extensions sur ce site."

#: inc/vendors/classes/class-imagify-partner.php:533
msgid "Sorry, you are not allowed to do that."
msgstr "Désolé, vous n'avez pas droit de faire cela."

#: inc/vendors/classes/class-imagify-partner.php:534
msgid "Plugin install failed."
msgstr "Installation de l'extension échouée."

#: inc/vendors/classes/class-imagify-partner.php:535
msgid "Go back"
msgstr "Retour en arrière"

#: views/cpcss/activate-cpcss-mobile.php:13
msgid "Load CSS asynchronously for mobile"
msgstr "Chargement asynchrone du CSS pour mobile"

#: views/cpcss/activate-cpcss-mobile.php:14
msgid "Your website currently uses the same Critical Path CSS for both desktop and mobile."
msgstr "Votre site web utiliser le même chemin critique de CSS pour les ordinateurs et mobiles."

#: views/cpcss/activate-cpcss-mobile.php:15
msgid "Click the button to enable mobile-specific CPCSS for your site."
msgstr "Peser le bouton pour activer le CPCSS spécifique aux mobiles pour votre site."

#: views/cpcss/activate-cpcss-mobile.php:20
#: views/settings/enable-google-fonts.php:19
#, php-format
msgid "This is a one-time action and this button will be removed afterwards. %1$sMore info%2$s"
msgstr "Ceci est une action unique et ce bouton sera retiré ensuite. %1$sPlus d'info%2$s"

#: views/cpcss/activate-cpcss-mobile.php:30
#, php-format
msgid "Your site is now using mobile-specific critical path CSS. %1$sMore info%2$s"
msgstr "Votre site présentement un chemin critique CSS spécifique aux mobiles. %1$sPlus d'info%2$s"

#: views/cpcss/activate-cpcss-mobile.php:39
msgid "Generate Mobile Specific CPCSS"
msgstr "Générer CPCSS spécifique aux mobiles"

#: views/cpcss/metabox/container.php:19
msgid "Critical Path CSS"
msgstr "CSS du chemin critique"

#: views/cpcss/metabox/generate.php:23
#, php-format
msgid "Generate specific Critical Path CSS for this post. %1$sMore info%2$s"
msgstr "Générer un chemin critique CSS pour cet article. %1$sPlus d'info%2$s"

#: views/cpcss/metabox/generate.php:33
#, php-format
msgid "This post uses specific Critical Path CSS. %1$sMore info%2$s"
msgstr "Cet article utilise un chemin critique de CSS. %1$sPlus d'info%2$s"

#: views/cpcss/metabox/generate.php:56
msgid "Revert back to the default CPCSS"
msgstr "Retourner au CPCSS par défaut"

#: views/deactivation-intent/form.php:21
msgid "Facing an issue?"
msgstr "Vous rencontrez un problème ?"

#: views/deactivation-intent/form.php:24
msgid "It is not always necessary to deactivate WP Rocket when facing any issues. Most of them can be fixed by deactivating only some options."
msgstr "Il n'est pas toujours nécessaire de désactiver WP Rocket lorsque vous rencontrez des problèmes. La plupart d'entre eux peuvent être résolus en désactivant seulement certaines options."

#: views/deactivation-intent/form.php:29
#, php-format
msgid "Our advice? Instead of deactivating WP Rocket, use our %1$sSafe Mode%2$s to quickly disable LazyLoad, File Optimization, and CDN options. Then check to see if your issue is resolved."
msgstr "Notre conseil ? Au lieu de désactiver WP Rocket, utilisez notre %1$sConfiguration sans conflit%2$s pour désactiver rapidement les options LazyLoad, Optimisation des Fichiers et CDN. Vérifiez ensuite si votre problème est résolu."

#: views/deactivation-intent/form.php:35
msgid "Do you want to use our Safe Mode to troubleshoot WP Rocket?"
msgstr "Voulez-vous utiliser notre Configuration sans conflit pour dépanner WP Rocket ?"

#: views/deactivation-intent/form.php:55
#, php-format
msgid "Yes, apply \"%1$sSafe Mode%2$s\""
msgstr "Oui, appliquer la %1$sConfiguration sans conflit%2$s"

#: views/deactivation-intent/form.php:76
msgid "No, deactivate and snooze this message for"
msgstr "Non, désactiver et mettre en veilleuse ce message pour"

#: views/deactivation-intent/form.php:78
msgid "1 day"
msgstr "1 jour"

#: views/deactivation-intent/form.php:79
msgid "7 days"
msgstr "7 jours"

#: views/deactivation-intent/form.php:80
msgid "30 days"
msgstr "30 jours"

#: views/deactivation-intent/form.php:81
msgid "Forever"
msgstr "Toujours"

#: views/deactivation-intent/form.php:87
msgid "Cancel"
msgstr "Annuler"

#: views/deactivation-intent/form.php:88
msgid "Confirm"
msgstr "Confirmer"

#: views/settings/dynamic-lists-update.php:14
msgid "Update Inclusion and Exclusion Lists"
msgstr "Mise à jour des listes d'inclusion et d'exclusion"

#: views/settings/dynamic-lists-update.php:19
#, php-format
msgid "Compatibility lists are downloaded automatically every week. Click the button if you want to update them manually. %1$sMore info%2$s"
msgstr "Les listes de compatibilité sont téléchargées automatiquement chaque semaine. Cliquez sur le bouton si vous souhaitez les mettre à jour manuellement. %1$sPlus d'infos%2$s"

#: views/settings/dynamic-lists-update.php:29
msgid "Update lists"
msgstr "Mettre à jour les listes"

#: views/settings/enable-google-fonts.php:13
msgid "Enable Google Font Optimization"
msgstr "Activer l'optimisation des Google Fonts"

#: views/settings/enable-google-fonts.php:14
msgid "Improves font performance and combines multiple font requests to reduce the number of HTTP requests."
msgstr "Améliore la performance des fontes et combine plusieurs requêtes de fontes pour réduire le nombre d'appels HTTP."

#: views/settings/enable-google-fonts.php:29
#, php-format
msgid "Google Fonts Optimization is now enabled for your site. %1$sMore info%2$s"
msgstr "L'optimisation des Google Fonts est maintenant activée pour votre site. %1$sPlus d'info%2$s"

#: views/settings/enable-google-fonts.php:38
msgid "Optimize Google Fonts"
msgstr "Optimiser Google Fonts"

#: views/settings/fields/cache-lifespan.php:26
msgid "Clear cache after"
msgstr "Vider le cache après"

#: views/settings/fields/cnames.php:58
#: views/settings/fields/cnames.php:92
#: views/settings/fields/cnames.php:122
msgid "CSS & JavaScript"
msgstr "CSS et JavaScript"

#: views/settings/fields/cnames.php:59
#: views/settings/fields/cnames.php:93
#: views/settings/fields/cnames.php:123
msgid "JavaScript"
msgstr "JavaScript"

#: views/settings/fields/cnames.php:60
#: views/settings/fields/cnames.php:94
#: views/settings/fields/cnames.php:124
msgid "CSS"
msgstr "CSS"

#: views/settings/fields/import-form.php:29
msgid "Import settings"
msgstr "Importer les réglages"

#: views/settings/fields/one-click-addon.php:43
#: views/settings/fields/rocket-addon.php:43
msgid "Add-on status"
msgstr "État de l’ajout"

#: views/settings/fields/rocket-addon.php:72
msgid "Modify options"
msgstr "Modifier les options"

#: views/settings/fields/rocket-cdn.php:29
#: views/settings/fields/rocket-cdn.php:41
msgid "CDN CNAME"
msgstr "Nom du CDN"

#: views/settings/fields/rocket-cdn.php:62
#, php-format
msgid "Purges RocketCDN cached resources for your website. %s"
msgstr "Purge les ressources en cache de votre site web dans RocketCDN. %s"

#: views/settings/fields/rocket-cdn.php:63
#: views/settings/page-sections/cloudflare.php:36
#: views/settings/page-sections/sucuri.php:37
msgid "Learn more"
msgstr "En savoir plus"

#: views/settings/fields/rocket-cdn.php:72
msgid "Clear all RocketCDN cache files"
msgstr "Effacer tous les fichiers de cache de RocketCDN"

#: views/settings/page-sections/cloudflare.php:27
msgid "Cloudflare Cache"
msgstr "Cache de Cloudflare"

#: views/settings/page-sections/cloudflare.php:35
#: views/settings/page-sections/sucuri.php:36
#, php-format
msgid "Purges cached resources for your website. %s"
msgstr "Purge les ressources mises en cache de votre site web. %s"

#: views/settings/page-sections/cloudflare.php:36
msgid "https://support.cloudflare.com/hc/en-us/articles/200169246"
msgstr "https://support.cloudflare.com/hc/en-us/articles/200169246"

#: views/settings/page-sections/cloudflare.php:45
msgid "Clear all Cloudflare cache files"
msgstr "Effacer tous les fichiers en cache de Cloudflare"

#: views/settings/page-sections/dashboard.php:38
msgid "Congratulations!"
msgstr "Félicitations!"

#: views/settings/page-sections/dashboard.php:40
msgid "WP Rocket is now activated and already working for you."
msgstr "WP Rocket est maintenant activé et prêt à travailler pour vous."

#: views/settings/page-sections/dashboard.php:42
msgid "Your website should be loading faster now!"
msgstr "Votre site web devrait se charger plus rapidement maintenant!"

#: views/settings/page-sections/dashboard.php:44
#, php-format
msgid "To guarantee fast websites, WP Rocket automatically applies 80% of web performance best practices."
msgstr "Afin d'assurer des sites rapides, WP Rocket applique automatiquement 80% des meilleures pratiques en terme de performance web."

#: views/settings/page-sections/dashboard.php:44
msgid "We also enable options that provide immediate benefits to your website."
msgstr "Nous activons également des options qui fournissent des bénéfices immédiats à votre site."

#: views/settings/page-sections/dashboard.php:45
msgid "Continue to the options to further optimize your site!"
msgstr "Continuez avec les options si vous souhaitez optimiser votre site davantage!"

#: views/settings/page-sections/dashboard.php:62
msgid "My Account"
msgstr "Mon compte"

#: views/settings/page-sections/dashboard.php:68
msgid "Refresh info"
msgstr "Actualiser l’information"

#: views/settings/page-sections/dashboard.php:86
msgid "with"
msgstr "avec"

#: views/settings/page-sections/dashboard.php:100
msgid "Expiration Date"
msgstr "Date d'expiration"

#: views/settings/page-sections/dashboard.php:110
msgid "View my account"
msgstr "Voir mon compte"

#: views/settings/page-sections/dashboard.php:137
msgid "Quick Actions"
msgstr "Actions rapides"

#: views/settings/page-sections/dashboard.php:144
msgid "Remove all cached files"
msgstr "Supprimer tous les fichiers du cache"

#: views/settings/page-sections/dashboard.php:164
#: views/settings/page-sections/dashboard.php:170
msgid "Regenerate Critical CSS"
msgstr "Régénérer le CSS critique"

#: views/settings/page-sections/dashboard.php:182
msgid "Remove Used CSS Cache"
msgstr "Retirer le cache du CSS utilis"

#: views/settings/page-sections/dashboard.php:205
msgid "Frequently Asked Questions"
msgstr "Foire aux questions"

#: views/settings/page-sections/dashboard.php:219
msgid "Still cannot find a solution?"
msgstr "Toujours pas trouvé de solution?"

#: views/settings/page-sections/dashboard.php:220
msgid "Submit a ticket and get help from our friendly and knowledgeable Rocketeers."
msgstr "Envoyez un ticket et obtenez l'aide de nos sympathiques Rocketeers."

#: views/settings/page-sections/dashboard.php:228
msgid "Ask support"
msgstr "Demandez au soutien"

#: views/settings/page-sections/database.php:25
msgid "Backup your database before you run a cleanup!"
msgstr "Faites une sauvegarde de votre base de données avant tout nettoyage!"

#: views/settings/page-sections/database.php:26
msgid "Once a database optimization has been performed, there is no way to undo it."
msgstr "Une fois qu’une optimisation de base de données est amorcée, les changements sont irréversibles."

#: views/settings/page-sections/database.php:28
msgid "Save Changes and Optimize"
msgstr "Sauvegarder changements et optimiser"

#: views/settings/page-sections/imagify.php:21
#, php-format
msgid "%1$sWP ROCKET%2$s created %3$sIMAGIFY%4$s %1$sfor best-in-class image optimization.%2$s"
msgstr "%1$sWP ROCKET%2$s a créé %3$sIMAGIFY%4$s %1$spour la meilleure optimisation des images.%2$s"

#: views/settings/page-sections/imagify.php:24
msgid "Compress image to make your website faster, all while maintaining image quality."
msgstr "Compresser les images afin d'accélérer votre site, tout en maintenant la qualité des images."

#: views/settings/page-sections/imagify.php:25
msgid "More on Imagify:"
msgstr "Plus d'info sur Imagify :"

#: views/settings/page-sections/imagify.php:27
msgid "Imagify Plugin Page"
msgstr "Page web de l'extension Imagify"

#: views/settings/page-sections/imagify.php:28
msgid "Imagify Website"
msgstr "Site web d'Imagify"

#: views/settings/page-sections/imagify.php:29
msgid "Review of Image Compression Plugins"
msgstr "Revue des extensions de compression d'images"

#: views/settings/page-sections/imagify.php:38
msgid "Install Imagify"
msgstr "Installer Imagify"

#: views/settings/page-sections/license.php:22
msgid "WP Rocket was not able to automatically validate your license."
msgstr "WP Rocket n'a pas été en mesure de valider automatiquement votre licence."

#: views/settings/page-sections/license.php:29
#, php-format
msgid "Follow this %1$s, or contact %2$s to get the engine started."
msgstr "Suivre ce %1$s, ou contactez %2$s pour démarrer le système."

#: views/settings/page-sections/license.php:32
#, php-format
msgid "%1$s%2$s%3$stutorial%4$s"
msgstr "%1$s%2$s%3$stutoriel%4$s"

#: views/settings/page-sections/license.php:34
msgid "https://docs.wp-rocket.me/article/100-resolving-problems-with-license-validation/?utm_source=wp_plugin&utm_medium=wp_rocket"
msgstr "https://fr.docs.wp-rocket.me/article/257-resoudre-les-problemes-de-validation-de-licence/?utm_source=wp_plugin&utm_medium=wp_rocket"

#: views/settings/page-sections/license.php:40
#, php-format
msgid "%1$s%2$s%3$ssupport%4$s"
msgstr "%1$s%2$s%3$ssupport%4$s"

#: views/settings/page-sections/sucuri.php:46
msgid "Clear all Sucuri cache files"
msgstr "Effacer tous les fichier de cache Sucuri"

#: views/settings/page-sections/tools.php:20
#, php-format
msgid "Files size: %1$s. Number of entries: %2$s."
msgstr "Taille du fichier : %1$s. Nombre d'entrées : %2$s."

#: views/settings/page-sections/tools.php:23
#, php-format
msgid "%1$sDownload the file%2$s."
msgstr "%1$sTélécharger le fichier%2$s."

#: views/settings/page-sections/tools.php:26
#, php-format
msgid "%1$sDelete the file%2$s."
msgstr "%1$sEffacer le fichier%2$s."

#: views/settings/page-sections/tools.php:37
msgid "Export settings"
msgstr "Exporter les réglages"

#: views/settings/page-sections/tools.php:38
msgid "Download a backup file of your settings"
msgstr "Télécharger un fichier de sauvegarde de vos réglages"

#: views/settings/page-sections/tools.php:46
msgid "Download settings"
msgstr "Télécharger les réglages"

#: views/settings/page-sections/tools.php:60
msgid "Rollback"
msgstr "Restauration"

#: views/settings/page-sections/tools.php:64
#, php-format
msgid "Has version %s caused an issue on your website?"
msgstr "La version %s vous pose des problèmes?"

#: views/settings/page-sections/tools.php:69
#, php-format
msgid "You can rollback to the previous major version here.%sThen send us a support request."
msgstr "Vous pouvez retourner à la précédente version majeure ici.%sPuis envoyez-nous une requête de soutien technique."

#: views/settings/page-sections/tools.php:80
#, php-format
msgid "Reinstall version %s"
msgstr "Réinstaller la version %s"

#: views/settings/page-sections/tools.php:106
msgid "Debug mode"
msgstr "Mode debug"

#: views/settings/page-sections/tools.php:111
msgid "Create a debug log file."
msgstr "Créer un fichier de debug."

#: views/settings/page-sections/tutorials.php:13
#: views/settings/partials/getting-started.php:18
msgid "Getting Started"
msgstr "Bien démarrer"

#: views/settings/page-sections/tutorials.php:15
msgid "Getting Started with WP Rocket"
msgstr "Pour démarrer avec WP Rocket"

#: views/settings/page-sections/tutorials.php:16
#: views/settings/partials/getting-started.php:12
msgid "Finding the Best Settings for Your Site"
msgstr "Trouver les meilleurs réglages pour votre site"

#: views/settings/page-sections/tutorials.php:17
#: views/settings/partials/getting-started.php:13
msgid "How to Check if WP Rocket is Caching Your Site"
msgstr "Comment valider que WP Rocket a mis en cache votre site"

#: views/settings/page-sections/tutorials.php:18
#: views/settings/partials/getting-started.php:14
msgid "How to Measure the Speed of Your Site"
msgstr "Comment mesurer la vitesse de votre site"

#: views/settings/page-sections/tutorials.php:19
msgid "How Preloading Works"
msgstr "Comment le pré-chargement fonctionne"

#: views/settings/page-sections/tutorials.php:23
msgid "Passing the Core Web vitals"
msgstr "Réussir les Core Web vitals"

#: views/settings/page-sections/tutorials.php:25
msgid "How to improve LCP with WP Rocket"
msgstr "Comment améliorer le LCP avec WP Rocket"

#: views/settings/page-sections/tutorials.php:26
msgid "How to improve FID with WP Rocket"
msgstr "Comment améliorer le FID avec WP Rocket"

#: views/settings/page-sections/tutorials.php:27
msgid "How to improve CLS with WP Rocket"
msgstr "Comment améliorer le CLS avec WP Rocket"

#: views/settings/page-sections/tutorials.php:31
msgid "Troubleshooting"
msgstr "Dépannage"

#: views/settings/page-sections/tutorials.php:33
msgid "Troubleshooting Display Issues with File Optimization"
msgstr "Diagnostiquer les problèmes d'affichage avec l'optimisation des fichiers"

#: views/settings/page-sections/tutorials.php:34
msgid "How to Find the Right JavaScript to Exclude"
msgstr "Comment trouver le bon Javascript à exclure"

#: views/settings/page-sections/tutorials.php:35
msgid "How External Content Slows Your Site"
msgstr "Comment les contenus externes ralentissent votre site."

#: views/settings/page-sections/tutorials.php:41
msgid "Set Up the Cloudflare Add-on"
msgstr "Configurer l'extension Cloudflare"

#: views/settings/page.php:16
msgid "WP Rocket Settings"
msgstr "Réglages de WP Rocket"

#: views/settings/page.php:30
#, php-format
msgid "version %s"
msgstr "version %s"

#: views/settings/page.php:60
msgid "Show Sidebar"
msgstr "Afficher la colonne latérale"

#: views/settings/page.php:82
#, php-format
msgid "Below is a detailed view of all data WP Rocket will collect %1$sif granted permission.%2$s"
msgstr "Vous trouverez ci-dessous une vue détaillée des données que WP Rocket recueillera %1$ssi l'autorisation lui est accordée.%2$s"

#: views/settings/page.php:87
msgid "WP Rocket will never transmit any domain names or email addresses (except for license validation), IP addresses, or third-party API keys."
msgstr "WP Rocket ne transmettra jamais de noms de domaine ou d'adresses courriels (sauf pour la validation de licence), d'adresses IP ou de clés d’API tierce-partie."

#: views/settings/page.php:89
msgid "Activate Rocket analytics"
msgstr "Activer analytiques de Rocket"

#: views/settings/partials/documentation.php:15
msgid "It is a great starting point to fix some of the most common issues."
msgstr "C'est un excellent point de départ pour résoudre certains des problèmes les plus fréquents."

#: views/settings/partials/documentation.php:22
msgid "Read the documentation"
msgstr "Lire la documentation"

#: views/settings/partials/getting-started.php:11
msgid "What WP Rocket Does For You By Default"
msgstr "Ce que fait WP Rocket pour vous par défaut"

#: views/settings/partials/sidebar.php:12
msgid "How to correctly measure your website’s loading time"
msgstr "Comment bien mesurer le temps de chargement de votre site web"

#: views/settings/partials/sidebar.php:14
msgid "Check our tutorial and learn how to measure the speed of your site."
msgstr "Consultez notre tutoriel et apprenez comment mesurer la vitesse de votre site."

#: views/settings/partials/sidebar.php:15
#: views/settings/partials/sidebar.php:24
msgid "Read our guide"
msgstr "Lire notre guide"

#: views/settings/partials/sidebar.php:18
msgid "Learn about optimal WP Rocket settings for mobile."
msgstr "Découvrez les réglages optimaux de WP Rocket pour les mobiles."

#: views/settings/partials/sidebar.php:27
msgid "Test and Improve Google Core Web Vitals for WordPress."
msgstr "Test et amélioration des Google Core Web Vitals pour WordPress."

#: views/settings/partials/sidebar.php:28
msgid "Read more"
msgstr "En savoir plus"

#: views/settings/partials/sidebar.php:33
msgid "You have not activated logged-in user cache."
msgstr "Vous n'avez pas activé le cache pour utilisateurs connectés."

#: views/settings/partials/sidebar.php:34
msgid "Use a private browser to check your website's speed and visual appearance."
msgstr "Utilisez un navigateur privé pour vérifier la vitesse et l’aspect visuel de votre site web."

#: views/settings/sections/addons-container.php:24
#: views/settings/sections/fields-container.php:28
msgid "Need Help?"
msgstr "Besoin d'aide ?"

#: inc/Addon/Cloudflare/Cloudflare.php:205
msgid "days"
msgstr ""

#: inc/Addon/Cloudflare/Cloudflare.php:208
msgid "seconds"
msgstr ""

#: inc/Addon/Cloudflare/Cloudflare.php:210
msgid "minutes"
msgstr ""

#: inc/Addon/Cloudflare/Cloudflare.php:212
msgid "hours"
msgstr ""

#: inc/Addon/Cloudflare/Subscriber.php:224
#: inc/Addon/Cloudflare/Subscriber.php:253
#, php-format
msgid "%1$sWP Rocket:%2$s %3$s"
msgstr ""

#: inc/Addon/Cloudflare/Subscriber.php:242
#, php-format
msgid "%1$sWP Rocket:%2$s Cloudflare cache successfully purged."
msgstr ""

#: inc/Addon/Cloudflare/Subscriber.php:328
msgctxt "Cloudflare caching level"
msgid "standard"
msgstr ""

#: inc/Addon/Cloudflare/Subscriber.php:403
#, php-format
msgid "Cloudflare browser cache set to %s"
msgstr ""

#: inc/Addon/Cloudflare/Subscriber.php:512
#, php-format
msgid "%1$sWP Rocket:%2$s Optimal settings activated for Cloudflare:"
msgstr ""

#: inc/Addon/Cloudflare/Subscriber.php:521
#, php-format
msgid "%1$sWP Rocket:%2$s Optimal settings deactivated for Cloudflare, reverted to previous settings:"
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:642
msgid "If you have problems after activating this option, copy and paste the default exclusions to quickly resolve issues:"
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:646
#, php-format
msgid "Also, please check our %1$sdocumentation%2$s for a list of compatibility exclusions."
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:651
msgid "Internal scripts are excluded by default to prevent issues. Remove them to take full advantage of this option."
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:654
#, php-format
msgid "If this causes trouble, restore the default exclusions, found %1$shere%2$s"
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:942
msgid "One-click exclusions"
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:943
msgid "When using the Delay JavaScript Execution, you might experience delay loading elements located in the viewport that need to appear immediately - e.g. slider, header, menu."
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:944
msgid "If you need instant visibility, click below on files that should NOT be delayed. This selection will help users interact with the elements straight away."
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:1827
#, php-format
msgid "%1$sPlanning on using Automatic Platform Optimization (APO)?%2$s Just activate the official Cloudflare plugin and configure it. WP Rocket will automatically enable compatibility."
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:2129
msgctxt "Sucuri"
msgid "Firewall API key (for plugin), must be in format {32 characters}/{32 characters}:"
msgstr ""

#: inc/Engine/CriticalPath/CriticalCSSSubscriber.php:840
#, php-format
msgid "We highly recommend the %1$supdated Remove Unused CSS%2$s for a better CSS optimization. Load CSS Asynchronously is always available as a back-up."
msgstr ""

#: inc/Engine/CriticalPath/CriticalCSSSubscriber.php:845
msgid "Stay with the old option"
msgstr ""

#: inc/Engine/License/Renewal.php:76
#, php-format
msgid "Renew before it is too late, you will only pay %1$s%2$s%3$s!"
msgstr ""

#: inc/Engine/License/Renewal.php:85
#, php-format
msgid "Renew with a %1$s%2$s discount%3$s before it is too late, you will only pay %4$s%5$s%6$s!"
msgstr ""

#: inc/Engine/Optimization/DelayJS/Admin/SiteList.php:195
msgid "Analytics & Ads"
msgstr ""

#: inc/Engine/Optimization/DelayJS/Admin/SiteList.php:200
msgid "Plugins"
msgstr ""

#: inc/Engine/Optimization/DelayJS/Admin/SiteList.php:205
msgid "Themes"
msgstr ""

#: inc/Engine/Optimization/DynamicLists/ServiceProvider.php:52
msgid "Default Lists"
msgstr ""

#: inc/Engine/Optimization/DynamicLists/ServiceProvider.php:58
msgid "Delay JavaScript Execution Exclusion Lists"
msgstr ""

#: inc/Engine/Optimization/RUCSS/Cron/Subscriber.php:174
msgid "WP Rocket clear Remove Unused CSS failed jobs"
msgstr ""

#: inc/Engine/Preload/Cron/Subscriber.php:166
msgid "WP Rocket Preload revert stuck failed jobs"
msgstr ""

#: inc/ThirdParty/Plugins/CDN/Cloudflare.php:157
msgid "Your site is using the official Cloudflare plugin. We have enabled Cloudflare auto-purge for compatibility. If you have APO activated, it is also compatible."
msgstr ""

#: inc/ThirdParty/Plugins/CDN/Cloudflare.php:158
msgid "Cloudflare cache will be purged each time WP Rocket clears its cache to ensure content is always up-to-date."
msgstr ""

#: inc/ThirdParty/Plugins/CDN/Cloudflare.php:202
#, php-format
msgid "%1$sWP Rocket:%2$s You are using \"Dynamic Cookies Cache\". Cloudflare APO is not yet compatible with that feature."
msgstr ""

#: inc/ThirdParty/Plugins/CDN/Cloudflare.php:208
#, php-format
msgid "You should either disable Cloudflare APO or check with the theme/plugin requiring the use of “Dynamic Cookies Cache” developers for an alternative way to be page-cache friendly. %1$sMore info%2$s"
msgstr ""

#: inc/ThirdParty/Plugins/CDN/Cloudflare.php:259
#, php-format
msgid "%1$sWP Rocket:%2$s You are using \"Separate cache files for mobile devices\". You need to activate \"Cache by Device Type\" %3$ssetting%5$s on Cloudflare APO to serve the right version of the cache. %4$sMore info%5$s"
msgstr ""

#: inc/ThirdParty/Plugins/CDN/Cloudflare.php:280
#, php-format
msgid "%1$sWP Rocket:%2$s You have \"Cache by Device Type\" enabled on Cloudflare APO. If you judge it necessary for the website to have a different cache on mobile and desktop, we suggest you enable our “Separate Cache Files for Mobiles Devices” to ensure the generated cache is accurate."
msgstr ""

#: inc/ThirdParty/Plugins/Optimization/Perfmatters.php:38
msgid "Remove Unused CSS is currently activated in Perfmatters. If you want to use WP Rocket's Remove Unused CSS feature, disable this option in Perfmatters."
msgstr ""

#: inc/ThirdParty/Plugins/Optimization/RapidLoad.php:39
msgid "Automated unused CSS removal is currently activated in RapidLoad Power-Up for Autoptimize. If you want to use WP Rocket's Remove Unused CSS feature, disable the  RapidLoad Power-Up for Autoptimize plugin."
msgstr ""

#: inc/admin/ui/notices.php:757
msgid "Turn on Remove Unused CSS"
msgstr ""

#: inc/admin/ui/notices.php:763
msgid "Enable “Separate Cache Files for Mobile Devices” now"
msgstr ""

#: views/deactivation-intent/form.php:68
#, php-format
msgid "and export WP Rocket settings %1$s(Recommended as current settings will be deleted)%2$s"
msgstr ""
