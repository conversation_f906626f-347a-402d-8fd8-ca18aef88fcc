msgid ""
msgstr ""
"Project-Id-Version: WP Rocket 3.11\n"
"Report-Msgid-Bugs-To: http://wp-rocket.me/\n"
"Last-Translator: <PERSON><PERSON><PERSON> <b<PERSON><PERSON><PERSON><PERSON>@gmail.com>, 2022\n"
"Language-Team: Croatian (https://www.transifex.com/wp-media/teams/18133/hr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"POT-Creation-Date: 2022-06-23 14:06-0400\n"
"PO-Revision-Date: 2019-08-26 15:14+0000\n"
"Language: hr\n"
"Plural-Forms: nplurals=3; plural=n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2;\n"
"X-Generator: Poedit 1.8.11\n"
"X-Poedit-Basepath: ..\n"
"X-Poedit-KeywordsList: __;_e;_x:1,2c;_ex:1,2c;_n:1,2;_nx:1,2,4c;_n_noop:1,2;_nx_noop:1,2,3c;esc_attr__;esc_html__;esc_attr_e;esc_html_e;esc_attr_x:1,2c;esc_html_x:1,2c\n"
"X-Poedit-SearchPath-0: .\n"
"X-Poedit-SearchPathExcluded-0: assets\n"
"X-Poedit-SearchPathExcluded-1: src\n"
"X-Poedit-SearchPathExcluded-2: vendor\n"
"X-Poedit-SearchPathExcluded-3: node_modules\n"
"X-Poedit-SearchPathExcluded-4: tests\n"
"X-Poedit-SearchPathExcluded-5: inc/Dependencies\n"
"X-Poedit-SourceCharset: UTF-8\n"
"X-Textdomain-Support: yes\n"

#: inc/3rd-party/hosting/flywheel.php:18
#: inc/3rd-party/hosting/pressidium.php:18
#: inc/3rd-party/hosting/wp-serveur.php:27
#: inc/ThirdParty/Hostings/Cloudways.php:90
#: inc/ThirdParty/Hostings/Dreampress.php:44
#: inc/ThirdParty/Hostings/Godaddy.php:63
#: inc/ThirdParty/Hostings/O2Switch.php:49
#: inc/ThirdParty/Hostings/OneCom.php:137
#: inc/ThirdParty/Hostings/ProIsp.php:51
#: inc/ThirdParty/Hostings/Savvii.php:50
#: inc/ThirdParty/Hostings/WPEngine.php:47
#: inc/ThirdParty/Hostings/WPXCloud.php:51
#: inc/deprecated/3.6.php:697
#: inc/deprecated/3.6.php:997
#: inc/deprecated/3.9.php:22
#, php-format
msgid "Your site is hosted on %s, we have enabled Varnish auto-purge for compatibility."
msgstr "Vaša web stranica je hostana kod %s. Radi kompatibilnosti smo uključili automatsko čišćenje Varnisha."

#: inc/deprecated/3.12.php:332
msgid "Jetpack XML Sitemaps"
msgstr "Jetpack XML mape web-lokacije"

#: inc/deprecated/3.12.php:334
msgid "Preload the sitemap from the Jetpack plugin"
msgstr "Unaprijed učitaj mapu web-lokacije od Jetpacka"

#: inc/deprecated/3.11.php:83
#: inc/deprecated/3.12.php:58
#: inc/deprecated/3.12.php:87
#: inc/deprecated/3.12.php:186
#: inc/deprecated/3.12.php:253
#: inc/deprecated/3.12.php:342
#, php-format
msgid "We automatically detected the sitemap generated by the %s plugin. You can check the option to preload it."
msgstr "Automatski detektiramo mapu web-lokacije generiranu pomoću%s. Možete označiti opciju da ga se predučita."

#: inc/deprecated/3.12.php:56
msgid "All in One SEO XML sitemap"
msgstr "All in One SEO XML mapa sajta"

#: inc/deprecated/3.12.php:85
msgid "Rank Math XML sitemap"
msgstr "Rank Math XML mapa web-lokacije"

#: inc/deprecated/3.12.php:184
msgid "SEOPress XML sitemap"
msgstr "SEOPress XML mapa weba"

#: inc/deprecated/3.12.php:251
msgid "The SEO Framework XML sitemap"
msgstr "SEO Framework XML mapa web-lokacije"

#: inc/Addon/Cloudflare/API/Client.php:129
msgid "Cloudflare did not provide any reply. Please try again later."
msgstr "Cloudflare nije dao nikakav odgovor. Molimo pokušajte ponovo kasnije."

#: inc/Addon/Cloudflare/API/Client.php:186
#: inc/deprecated/3.5.php:112
#: inc/deprecated/3.5.php:169
msgid "Incorrect Cloudflare email address or API key."
msgstr "Netočna Cloudflare email adresa ili API ključ."

#: inc/Addon/Cloudflare/API/Client.php:190
#: inc/Addon/Cloudflare/API/Client.php:204
#: inc/Addon/Cloudflare/Cloudflare.php:74
#: inc/Addon/Cloudflare/Cloudflare.php:109
#: inc/classes/subscriber/Tools/class-detect-missing-tags-subscriber.php:157
#: inc/deprecated/3.5.php:92
#: inc/deprecated/3.5.php:116
#: inc/deprecated/3.5.php:129
#: inc/deprecated/3.5.php:157
#: inc/deprecated/3.5.php:173
#, php-format
msgid "Read the %1$sdocumentation%2$s for further guidance."
msgstr "Pročitajte %1$sdokumentaciju%2$s za daljnje upute."

#: inc/Addon/Cloudflare/API/Client.php:192
#: inc/Addon/Cloudflare/API/Client.php:206
#: inc/Addon/Cloudflare/Auth/APIKey.php:63
#: inc/Addon/Cloudflare/Cloudflare.php:76
#: inc/Addon/Cloudflare/Cloudflare.php:111
#: inc/deprecated/3.5.php:81
#: inc/deprecated/3.5.php:94
#: inc/deprecated/3.5.php:118
#: inc/deprecated/3.5.php:131
#: inc/deprecated/3.5.php:159
#: inc/deprecated/3.5.php:175
#: inc/deprecated/3.5.php:208
msgid "https://docs.wp-rocket.me/article/18-using-wp-rocket-with-cloudflare/?utm_source=wp_plugin&utm_medium=wp_rocket#add-on"
msgstr "https://docs.wp-rocket.me/article/18-using-wp-rocket-with-cloudflare/?utm_source=wp_plugin&utm_medium=wp_rocket#add-on"

#: inc/Addon/Cloudflare/API/Client.php:200
#: inc/deprecated/3.5.php:125
msgid "Incorrect Cloudflare Zone ID."
msgstr "Netočan Cloudflare Zone ID."

#: inc/Addon/Cloudflare/Auth/APIKey.php:61
#, php-format
msgid "Cloudflare email and/or API key are not set. Read the %1$sdocumentation%2$s for further guidance."
msgstr "Cloudflare email i/ili API ključ nisu postavljeni. Pročitajte %1$sdokumentaciju%2$s za više uputa."

#: inc/Addon/Cloudflare/Cloudflare.php:70
#: inc/deprecated/3.5.php:88
msgid "Missing Cloudflare Zone ID."
msgstr "Nedostaje Cloudflare Zone ID."

#: inc/Addon/Cloudflare/Cloudflare.php:105
#: inc/deprecated/3.5.php:153
msgid "It looks like your domain is not set up on Cloudflare."
msgstr "Izgleda da vaša domena nije postavljena na Cloudflare-u."

#: inc/deprecated/3.5.php:587
#, php-format
msgid "<strong>WP Rocket:</strong> %s"
msgstr "<strong>WP Rocket:</strong> %s"

#: inc/deprecated/3.5.php:592
msgid "<strong>WP Rocket:</strong> Cloudflare cache successfully purged."
msgstr "<strong>WP Rocket:</strong> Cloudflare cache je uspješno očišćen."

#: inc/Addon/Cloudflare/Subscriber.php:632
#: inc/admin/options.php:184
#: inc/classes/subscriber/Tools/class-detect-missing-tags-subscriber.php:148
msgid "WP Rocket: "
msgstr "WP Rocket: "

#: inc/Addon/Cloudflare/Subscriber.php:297
#, php-format
msgid "Cloudflare development mode error: %s"
msgstr "Greška u Cloudflare razvojnom modu: %s"

#: inc/Addon/Cloudflare/Subscriber.php:304
#, php-format
msgid "Cloudflare development mode %s"
msgstr "Cloudflare razvojni mod%s"

#: inc/Addon/Cloudflare/Subscriber.php:321
#, php-format
msgid "Cloudflare cache level error: %s"
msgstr "Greška u Cloudflare cache nivou: %s"

#: inc/Addon/Cloudflare/Subscriber.php:334
#, php-format
msgid "Cloudflare cache level set to %s"
msgstr "Cloudflare cache nivo stavljen na %s"

#: inc/Addon/Cloudflare/Subscriber.php:350
#, php-format
msgid "Cloudflare minification error: %s"
msgstr "Greška u Cloudflare minifikaciji:%s"

#: inc/Addon/Cloudflare/Subscriber.php:357
#, php-format
msgid "Cloudflare minification %s"
msgstr "Cloudflare minifikacija%s"

#: inc/Addon/Cloudflare/Subscriber.php:373
#, php-format
msgid "Cloudflare rocket loader error: %s"
msgstr "Greška Cloudflare rocket učitavača:%s"

#: inc/Addon/Cloudflare/Subscriber.php:380
#, php-format
msgid "Cloudflare rocket loader %s"
msgstr "Cloudflare rocket učitavač:%s"

#: inc/Addon/Cloudflare/Subscriber.php:396
#, php-format
msgid "Cloudflare browser cache error: %s"
msgstr "Cache greška Cloudflare pretraživača :%s"

#: inc/Addon/Sucuri/Subscriber.php:95
#, php-format
msgid "Sucuri cache purge error: %s"
msgstr "Pogreška u čišćenju Sucuri cachea: %s"

#: inc/Addon/Sucuri/Subscriber.php:100
msgid "The Sucuri cache is being cleared. Note that it may take up to two minutes for it to be fully flushed."
msgstr "Sucuri cache se briše. Imajte na umu da može potrajati do dvije minute da se izbriše u potpunosti."

#: inc/Addon/Sucuri/Subscriber.php:217
msgid "Sucuri firewall API key was not found."
msgstr "API ključ Sucuri firewalla nije pronađen."

#: inc/Addon/Sucuri/Subscriber.php:230
msgid "Sucuri firewall API key is invalid."
msgstr "API ključ Sucuri firewalla nije važeći."

#: inc/Addon/Sucuri/Subscriber.php:285
#, php-format
msgid "Error when contacting Sucuri firewall API. Error message was: %s"
msgstr "Pogreška prilikom kontaktiranja API-ja Sucuri firewalla. Poruka o pogrešci je: %s"

#: inc/Addon/Sucuri/Subscriber.php:300
msgid "Could not get a response from the Sucuri firewall API."
msgstr "Nije moguće dobiti odgovor od API-ja Sucuri firewalla."

#: inc/Addon/Sucuri/Subscriber.php:315
msgid "Got an invalid response from the Sucuri firewall API."
msgstr "Zaprimljen nevažeći odgovor od API-ja Sucuri firewalla."

#: inc/Addon/Sucuri/Subscriber.php:329
msgid "The Sucuri firewall API returned an unknown error."
msgstr "Sucuri firewall API je vratio nepoznatu pogrešku."

#: inc/Addon/Sucuri/Subscriber.php:333
#, php-format
msgid "The Sucuri firewall API returned the following error: %s"
msgid_plural "The Sucuri firewall API returned the following errors: %s"
msgstr[0] "API Sucuri firewalla je vratio sljedeću pogrešku: %s"
msgstr[1] "API Sucuri firewalla je vratio sljedeće pogreške: %s"
msgstr[2] "API Sucuri firewalla je vratio sljedeće pogreške: %s"

#: inc/Engine/Admin/Database/Optimization.php:30
#: inc/Engine/Admin/Settings/Page.php:1475
msgid "Revisions"
msgstr "Izmjene"

#: inc/Engine/Admin/Database/Optimization.php:31
#: inc/Engine/Admin/Settings/Page.php:1485
msgid "Auto Drafts"
msgstr "Auto nacrti"

#: inc/Engine/Admin/Database/Optimization.php:32
#: inc/Engine/Admin/Settings/Page.php:1495
msgid "Trashed Posts"
msgstr "Objave u smeću"

#: inc/Engine/Admin/Database/Optimization.php:33
#: inc/Engine/Admin/Settings/Page.php:1505
msgid "Spam Comments"
msgstr "Spam kometari"

#: inc/Engine/Admin/Database/Optimization.php:34
#: inc/Engine/Admin/Settings/Page.php:1515
msgid "Trashed Comments"
msgstr "Obrisani komentari"

#: inc/Engine/Admin/Database/Optimization.php:35
msgid "Transients"
msgstr "Transienti"

#: inc/Engine/Admin/Database/Optimization.php:36
msgid "Tables"
msgstr "Tablice"

#: inc/Engine/Admin/Database/Subscriber.php:79
#: inc/deprecated/Engine/Addon/FacebookTracking/Subscriber.php:92
#: inc/deprecated/Engine/Addon/GoogleTracking/Subscriber.php:137
msgid "weekly"
msgstr "tjedno"

#: inc/Engine/Admin/Database/Subscriber.php:85
msgid "monthly"
msgstr "mjesečno"

#: inc/Engine/Admin/Database/Subscriber.php:194
msgid "Database optimization process is running"
msgstr "Postupak optimizacije baze podataka je u tijeku"

#: inc/Engine/Admin/Database/Subscriber.php:224
msgid "Database optimization process is complete. Everything was already optimized!"
msgstr "Database optimization process is complete. Everything was already optimized!"

#: inc/Engine/Admin/Database/Subscriber.php:227
msgid "Database optimization process is complete. List of optimized items below:"
msgstr "Postupak optimizacije baze podataka je završen. Popis optimiziranih stavki u nastavku:"

#: inc/Engine/Admin/Database/Subscriber.php:235
#, php-format
msgid "%1$d %2$s optimized."
msgstr "%1$d %2$s optimizirano."

#: inc/Engine/Admin/Settings/Page.php:201
msgid "Save Changes"
msgstr "Spremi Promjene"

#: inc/Engine/Admin/Settings/Page.php:201
msgid "Validate License"
msgstr "Potvrdite licencu"

#: inc/Engine/Admin/Settings/Page.php:257
#: inc/Engine/Admin/Settings/Page.php:258
msgid "Unavailable"
msgstr "Nedostupno"

#: inc/Engine/Admin/Settings/Page.php:352
#: inc/deprecated/deprecated.php:1789
#: views/settings/page-sections/dashboard.php:80
msgid "License"
msgstr "Licenca"

#: inc/Engine/Admin/Settings/Page.php:369
msgid "API key"
msgstr "API ključ"

#: inc/Engine/Admin/Settings/Page.php:384
msgid "Email address"
msgstr "Email adresa"

#: inc/Engine/Admin/Settings/Page.php:410
msgid "Dashboard"
msgstr "Nadzorna ploča"

#: inc/Engine/Admin/Settings/Page.php:411
msgid "Get help, account info"
msgstr "Zatražite pomoć, informacije o računu"

#: inc/Engine/Admin/Settings/Page.php:420
msgid "My Status"
msgstr "Moj status"

#: inc/Engine/Admin/Settings/Page.php:430
#: views/settings/page.php:75
msgid "Rocket Analytics"
msgstr "Rocket Analitika"

#: inc/Engine/Admin/Settings/Page.php:432
#, php-format
msgid "I agree to share anonymous data with the development team to help improve WP Rocket. %1$sWhat info will we collect?%2$s"
msgstr "Slažem se da dijelim anonimne podatke s razvojnim timom kako bih pomogao poboljšati WP Rocket. %1$sKoje ćemo podatke prikupiti?%2$s"

#: inc/Engine/Admin/Settings/Page.php:456
#: inc/Engine/Cache/WPCache.php:354
msgid "Cache"
msgstr "Cache"

#: inc/Engine/Admin/Settings/Page.php:457
msgid "Basic cache options"
msgstr "Osnovne mogućnosti cachea"

#: inc/Engine/Admin/Settings/Page.php:464
msgid "Mobile Cache"
msgstr "Mobilni Cache"

#: inc/Engine/Admin/Settings/Page.php:466
msgid "Speed up your site for mobile visitors."
msgstr "Ubrzajte svoju web-lokaciju za posjetitelje koji koriste mobilne uređaje."

#: inc/Engine/Admin/Settings/Page.php:471
msgid "We detected you use a plugin that requires a separate cache for mobile, and automatically enabled this option for compatibility."
msgstr "Otkrili smo da koristite dodatak koji zahtijeva zasebni cache za mobilne uređaje i automatski smo uključili tu mogućnost radi bolje kompatibilnosti."

#: inc/Engine/Admin/Settings/Page.php:475
msgid "User Cache"
msgstr "Cache korisnika"

#: inc/Engine/Admin/Settings/Page.php:478
#, php-format
msgid "%1$sUser cache%2$s is great when you have user-specific or restricted content on your website."
msgstr "%1$sCache korisnika%2$s je odličan kada prikazujete sadržaj isključivo određenim korisnicima ili imate ograničenu vidljivost sadržaja na vašoj web-lokaciji."

#: inc/Engine/Admin/Settings/Page.php:486
msgid "Cache Lifespan"
msgstr "Vijek trajanja cachea"

#: inc/Engine/Admin/Settings/Page.php:489
#, php-format
msgid "Cache files older than the specified lifespan will be deleted.<br>Enable %1$spreloading%2$s for the cache to be rebuilt automatically after lifespan expiration."
msgstr "Cache datoteke starije od navedenog životnog vijeka cache-a će biti uklonjene.<br>Aktivirajte %1$spreloading%2$s kako bi se cache obnovio nakon isteka."

#: inc/Engine/Admin/Settings/Page.php:503
msgid "Enable caching for logged-in WordPress users"
msgstr "Omogući caching za logirane WordPress korisnike."

#: inc/Engine/Admin/Settings/Page.php:511
msgid "Enable caching for mobile devices"
msgstr "Omogućite cachiranje na mobilnim uređajima"

#: inc/Engine/Admin/Settings/Page.php:526
msgid "Separate cache files for mobile devices"
msgstr "Odvojite cache datoteke za mobilne uređaje"

#: inc/Engine/Admin/Settings/Page.php:528
#, php-format
msgid "Most modern themes are responsive and should work without a separate cache. Enable this only if you have a dedicated mobile theme or plugin. %1$sMore info%2$s"
msgstr "Većina modernih tema je responzivna i trebala bi raditi bez zasebnog cachea. Omogućite ovo samo ako imate dediciranu mobilnu temu ili dodatak. %1$sViše informacija%2$s"

#: inc/Engine/Admin/Settings/Page.php:544
msgid "Specify time after which the global cache is cleared<br>(0 = unlimited )"
msgstr "Navedite vrijeme nakon kojeg će se izbrisati globalni cache<br>(0 = neograničeno )"

#: inc/Engine/Admin/Settings/Page.php:546
#, php-format
msgid "Reduce lifespan to 10 hours or less if you notice issues that seem to appear periodically. %1$sWhy?%2$s"
msgstr "Smanjite vijek trajanja na 10 sati ili manje ako primijetite probleme koji se povremeno pojavljuju. %1$sZašto?%2$s"

#: inc/Engine/Admin/Settings/Page.php:552
#: inc/Engine/License/views/promo-banner.php:30
#: inc/Engine/License/views/renewal-soon-banner.php:13
msgid "Hours"
msgstr "Sati"

#: inc/Engine/Admin/Settings/Page.php:553
#: inc/Engine/License/views/promo-banner.php:29
#: inc/Engine/License/views/renewal-soon-banner.php:12
msgid "Days"
msgstr "Dana"

#: inc/Engine/Admin/Settings/Page.php:602
msgid "File Optimization"
msgstr "Optimizacija datoteka"

#: inc/Engine/Admin/Settings/Page.php:603
msgid "Optimize CSS & JS"
msgstr "Optimiziraj CSS i JS"

#: inc/Engine/Admin/Settings/Page.php:621
msgid "CSS Files"
msgstr "CSS datoteke"

#: inc/Engine/Admin/Settings/Page.php:611
#: inc/Engine/Admin/Settings/Page.php:637
#, php-format
msgid "%1$s Minification is currently activated in <strong>Autoptimize</strong>. If you want to use %2$s’s minification, disable those options in Autoptimize."
msgstr "%1$s jMinfikacija je trenutno aktivirana pomoću dodatka <strong>Autoptimize</strong>. Ako želite koristiti %2$sminifikaciju, onemogućite te opcije u postavkama Autoptimize-a."

#: inc/Engine/Admin/Settings/Page.php:630
msgid "JavaScript Files"
msgstr "JavaScript datoteke"

#: inc/Engine/Admin/Settings/Page.php:663
msgid "Minify CSS files"
msgstr "Minificiraj CSS datoteke"

#: inc/Engine/Admin/Settings/Page.php:664
msgid "Minify CSS removes whitespace and comments to reduce the file size."
msgstr "Minificiranje CSS-a uklanja razmake  i komentare kako bi se smanjila veličina datoteke."

#: inc/Engine/Admin/Settings/Page.php:677
#: inc/Engine/Admin/Settings/Page.php:701
#: inc/Engine/Admin/Settings/Page.php:765
#: inc/Engine/Admin/Settings/Page.php:831
#: inc/Engine/Admin/Settings/Page.php:855
msgid "This could break things!"
msgstr "Ovo bi moglo poremetiti izgled/funkcionalnost vaše web-lokacije!"

#: inc/Engine/Admin/Settings/Page.php:678
#: inc/Engine/Admin/Settings/Page.php:702
#: inc/Engine/Admin/Settings/Page.php:766
#: inc/Engine/Admin/Settings/Page.php:832
#: inc/Engine/Admin/Settings/Page.php:856
msgid "If you notice any errors on your website after having activated this setting, just deactivate it again, and your site will be back to normal."
msgstr "Ako na web-lokaciji primijetite pogreške nakon što ste aktivirali ovu postavku, samo je ponovno deaktivirajte, a web-lokacija će se vratiti u normalu."

#: inc/Engine/Admin/Settings/Page.php:679
msgid "Activate minify CSS"
msgstr "Aktiviraj minifikaciju CSS-a"

#: inc/Engine/Admin/Settings/Page.php:684
msgid "Combine CSS files <em>(Enable Minify CSS files to select)</em>"
msgstr "Kombiniraj CSS datoteke <em>(Omogući minifikaciju CSS datoteka za odabir)</em>"

#: inc/Engine/Admin/Settings/Page.php:686
#, php-format
msgid "Combine CSS merges all your files into 1, reducing HTTP requests. Not recommended if your site uses HTTP/2. %1$sMore info%2$s"
msgstr "Kombiniranje CSS-a spaja sve vaše datoteke u jednu, smanjujući HTTP zahtjeve. Ne preporučuje se ako web-lokacija koristi HTTP/2. %1$sViše informacija%2$s"

#: inc/Engine/Admin/Settings/Page.php:687
msgid "For compatibility and best results, this option is disabled when Remove unused CSS is enabled."
msgstr "Radi kompatibilnosti i najboljih rezultata, ova je opcija onemogućena kada je omogućeno Uklanjanje nekorištenog CSS-a."

#: inc/Engine/Admin/Settings/Page.php:703
msgid "Activate combine CSS"
msgstr "Aktiviraj kombiniranje CSS-a"

#: inc/Engine/Admin/Settings/Page.php:708
#: inc/admin/options.php:124
msgid "Excluded CSS Files"
msgstr "Izuzete CSS datoteke"

#: inc/Engine/Admin/Settings/Page.php:709
msgid "Specify URLs of CSS files to be excluded from minification and concatenation (one per line)."
msgstr "Navedite URL-ove CSS datoteka koje treba isključiti iz minifikacije i kombiniranja (jedan URL po retku)."

#: inc/Engine/Admin/Settings/Page.php:710
msgid "<strong>Internal:</strong> The domain part of the URL will be stripped automatically. Use (.*).css wildcards to exclude all CSS files located at a specific path."
msgstr "<strong>Interno:</strong> Dio domene URL-a automatski će biti automatski uklonjen. Koristite (.*).css zamjenske znakove (wildcard) da biste isključili sve CSS datoteke koje se nalaze na određenoj putanji."

#: inc/Engine/Admin/Settings/Page.php:712
#, php-format
msgid "<strong>3rd Party:</strong> Use either the full URL path or only the domain name, to exclude external CSS. %1$sMore info%2$s"
msgstr "<strong>CSS treće strane:</strong> Za isključivanje vanjskog CSS-a upotrijebite ili punu putanju URL-a ili samo naziv domene. %1$sViše informacija%2$s"

#: inc/Engine/Admin/Settings/Page.php:725
#: inc/Engine/Admin/Settings/Page.php:747
msgid "Optimize CSS delivery"
msgstr "Optimiziraj isporuku CSS-a"

#: inc/Engine/Admin/Settings/Page.php:730
msgid "Optimize CSS delivery eliminates render-blocking CSS on your website. Only one method can be selected. Remove Unused CSS is recommended for optimal performance, but limited only to the users with active license."
msgstr "Optimizacija isporuke CSS-a eliminira CSS koji blokira generiranje prikaza na vašoj web stranici. Može se odabrati samo jedna metoda. Uklonjanje neiskorištenog CSS-a preporučuje se za optimalnu izvedbu, ali ograničeno je samo na korisnike s aktivnom licencom."

#: inc/Engine/Admin/Settings/Page.php:730
msgid "Optimize CSS delivery eliminates render-blocking CSS on your website. Only one method can be selected. Remove Unused CSS is recommended for optimal performance."
msgstr "Optimizacija isporuke CSS-a eliminira CSS koji blokira generiranje prikaza na vašoj web stranici. Može se odabrati samo jedna metoda. Za optimalne performanse preporučuje se uklanjanje neiskorištenog CSS -a."

#: inc/Engine/Admin/Settings/Page.php:740
#, php-format
msgid "Optimize CSS Delivery features are disabled on local environments. %1$sLearn more%2$s"
msgstr "Značajka Optimiziraj isporuku CSS-a je onemogućena u lokalnim okruženjima %1$sSaznaj više%2$s"

#: inc/Engine/Admin/Settings/Page.php:763
#, php-format
msgid "Removes unused CSS per page and helps to reduce page size and HTTP requests. Recommended for best performance. Test thoroughly! %1$sMore info%2$s"
msgstr "Uklanja neiskorišteni CSS po stranici i pomaže u smanjenju veličine stranice i HTTP zahtjeva. Preporučuje se za najbolje performanse. Temeljito testirajte! %1$sViše informacija%2$s"

#: inc/Engine/Admin/Settings/Page.php:767
msgid "Activate Remove Unused CSS"
msgstr "Aktivirajte Uklonite nekorišteni CSS"

#: inc/Engine/Admin/Settings/Page.php:773
msgid "CSS safelist"
msgstr "CSS sigurna lista"

#: inc/Engine/Admin/Settings/Page.php:774
msgid "Specify CSS filenames, IDs or classes that should not be removed (one per line)."
msgstr "Navedite imena CSS datoteka, ID-ove ili klase koje ne treba uklanjati (po jedan u retku)."

#: inc/Engine/Admin/Settings/Page.php:789
#: inc/admin/ui/meta-boxes.php:109
msgid "Load CSS asynchronously"
msgstr "Učitajte CSS asinkrono"

#: inc/Engine/Admin/Settings/Page.php:792
#, php-format
msgctxt "WP Critical CSS compatibility"
msgid "Load CSS asynchronously is currently handled by the %1$s plugin. If you want to use WP Rocket’s load CSS asynchronously option, disable the %1$s plugin."
msgstr "Učitavanjem CSS -a asinhrono trenutno upravlja%1$s plugin. Ako želite koristiti opciju asinhronog učitavanja CSS- a pomoću WP Rocketa, onemogućite plugin %1$s ."

#: inc/Engine/Admin/Settings/Page.php:794
#, php-format
msgid "Generates critical path CSS and loads CSS asynchronously. %1$sMore info%2$s"
msgstr "Generira critical path CSS  i asinhrono učitava CSS.%1$sViše informacija%2$s"

#: inc/Engine/Admin/Settings/Page.php:800
msgid "Fallback critical CSS"
msgstr "Zamjenski ključni CSS"

#: inc/Engine/Admin/Settings/Page.php:802
#, php-format
msgid "Provides a fallback if auto-generated critical path CSS is incomplete. %1$sMore info%2$s"
msgstr "Pruža zamjenu ako je automatski generiran CSS ključne putanje nepotpun. %1$sViše informacija%2$s"

#: inc/Engine/Admin/Settings/Page.php:817
msgid "Minify JavaScript files"
msgstr "Minificiraj JavaScript datoteke"

#: inc/Engine/Admin/Settings/Page.php:818
msgid "Minify JavaScript removes whitespace and comments to reduce the file size."
msgstr "Minficiranje JavaScripta uklanja razmake i komentare kako bi se smanjila veličina datoteke."

#: inc/Engine/Admin/Settings/Page.php:833
msgid "Activate minify JavaScript"
msgstr "Aktiviraj minifikaciju JavaScripta"

#: inc/Engine/Admin/Settings/Page.php:838
msgid "Combine JavaScript files <em>(Enable Minify JavaScript files to select)</em>"
msgstr "Kombiniraj JavaScript datoteke <em>(Omogući minifikaciju JavaScript datoteka za odabir)</em>"

#: inc/Engine/Admin/Settings/Page.php:840
#, php-format
msgid "Combine JavaScript files combines your site’s internal, 3rd party and inline JS reducing HTTP requests. Not recommended if your site uses HTTP/2. %1$sMore info%2$s"
msgstr "Kombiniranje JavaScript datoteka kombinira JS vaše web-lokacije, JS treće strane i inline JS smanjujući HTTP zahtjeve.. Ne preporučuje se ako web-lokacija koristi HTTP/2. %1$sViše informacija%2$s"

#: inc/Engine/Admin/Settings/Page.php:841
msgid "For compatibility and best results, this option is disabled when delay javascript execution is enabled."
msgstr "Radi kompatibilnosti i najboljih rezultata, ova je opcija onemogućena kada je omogućena odgoda izvršavanja javascripta."

#: inc/Engine/Admin/Settings/Page.php:857
msgid "Activate combine JavaScript"
msgstr "Aktiviraj kombiniranje JavaScripta"

#: inc/Engine/Admin/Settings/Page.php:862
#: inc/admin/options.php:125
msgid "Excluded Inline JavaScript"
msgstr "Izuzet Inline JavaScript"

#: inc/Engine/Admin/Settings/Page.php:864
#, php-format
msgid "Specify patterns of inline JavaScript to be excluded from concatenation (one per line). %1$sMore info%2$s"
msgstr "Navedite uzorke inline JavaScripta koji će biti isključeni iz kombiniranja (jedan po retku).%1$sViše informacija%2$s"

#: inc/Engine/Admin/Settings/Page.php:880
#: inc/Engine/Admin/Settings/Page.php:913
#: inc/Engine/Admin/Settings/Page.php:960
#: inc/admin/options.php:126
msgid "Excluded JavaScript Files"
msgstr "Izuzete JavaScript datoteke"

#: inc/Engine/Admin/Settings/Page.php:881
msgid "Specify URLs of JavaScript files to be excluded from minification and concatenation (one per line)."
msgstr "Odredite URL-ove JavaScript datoteka koje će biti isključene iz minifikacije i kombiniranja (jedan po retku)"

#: inc/Engine/Admin/Settings/Page.php:882
msgid "<strong>Internal:</strong> The domain part of the URL will be stripped automatically. Use (.*).js wildcards to exclude all JS files located at a specific path."
msgstr "<strong>Interno:</strong> Dio domene URL-a automatski će biti automatski uklonjen. Koristite (.*).js zamjenske znakove (wildcard) da biste isključili sve JS datoteke koje se nalaze na određenoj putanji."

#: inc/Engine/Admin/Settings/Page.php:884
#, php-format
msgid "<strong>3rd Party:</strong> Use either the full URL path or only the domain name, to exclude external JS. %1$sMore info%2$s"
msgstr "<strong>JS treće strane:</strong> Za isključivanje vanjskog JS-a upotrijebite ili punu putanju URL-a ili samo naziv domene. %1$sViše informacija%2$s"

#: inc/Engine/Admin/Settings/Page.php:900
msgid "Load JavaScript deferred"
msgstr "Učitaj JavaScript s odgodom"

#: inc/Engine/Admin/Settings/Page.php:902
#, php-format
msgid "Load JavaScript deferred eliminates render-blocking JS on your site and can improve load time. %1$sMore info%2$s"
msgstr "Učitavanje JavaScript s odgodom uklanja JS koji blokira prikaz vaše web-lokacije i može poboljšati vrijeme učitavanja.%1$sViše informacija%2$s"

#: inc/Engine/Admin/Settings/Page.php:915
#, php-format
msgid "Specify URLs or keywords of JavaScript files to be excluded from defer (one per line). %1$sMore info%2$s"
msgstr "Navedite URL-ove ili ključne riječi JavaScript datoteka koje će se izuzeti iz odgode (po jedan u retku). %1$sViše informacija%2$s"

#: inc/Engine/Admin/Settings/Page.php:931
#: inc/admin/ui/meta-boxes.php:111
msgid "Delay JavaScript execution"
msgstr "Odgodi izvršavanje JavaScripta"

#: inc/Engine/Admin/Settings/Page.php:933
#, php-format
msgid "Improves performance by delaying the loading of JavaScript files until user interaction (e.g. scroll, click). %1$sMore info%2$s"
msgstr "Poboljšava performanse odgađajući učitavanje JavaScript datoteka do interakcije korisnika (npr. scroll, klik).  %1$sViše informacija%2$s"

#: inc/Engine/Admin/Settings/Page.php:961
msgid "Specify URLs or keywords that can identify inline or JavaScript files to be excluded from delaying execution (one per line)."
msgstr "Navedite URL-ove ili ključne riječi koje mogu prepoznati inline ili JavaScript datoteke koje želite izuzeti iz odgode izvršavanja (jedna po retku)."

#: inc/Engine/Admin/Settings/Page.php:993
msgid "Media"
msgstr "Medij"

#: inc/Engine/Admin/Settings/Page.php:994
msgid "LazyLoad, image dimensions"
msgstr "LazyLoad, dimenzije slika"

#: inc/Engine/Admin/Settings/Page.php:1003
msgid "Autoptimize"
msgstr "Autoptimize"

#: inc/Engine/Admin/Settings/Page.php:1048
msgid "LazyLoad"
msgstr "LazyLoad"

#: inc/Engine/Admin/Settings/Page.php:1051
#, php-format
msgid "It can improve actual and perceived loading time as images, iframes, and videos will be loaded only as they enter (or about to enter) the viewport and reduces the number of HTTP requests. %1$sMore Info%2$s"
msgstr "Može poboljšati stvarno i opaženo vrijeme učitavanja tako što će slike, iframeove i videozapise učitati tek kad uđu (ili netom prije) u okvir za prikaz (viewport) te tako smanjiti broj HTTP zahtjeva. %1$sViše informacija%2$s"

#: inc/Engine/Admin/Settings/Page.php:1058
#, php-format
msgid "LazyLoad is currently activated in %2$s. If you want to use WP Rocket’s LazyLoad, disable this option in %2$s."
msgstr "Lazyload je trenutno aktivan pomoću %2$s. Ako želite koristiti WP Rocket LazyLoad, onemogućite ovu opciju pod%2$s."

#: inc/Engine/Admin/Settings/Page.php:1061
msgid "Image Dimensions"
msgstr "Dimenzije slika"

#: inc/Engine/Admin/Settings/Page.php:1064
#, php-format
msgid "Add missing width and height attributes to images. Helps prevent layout shifts and improve the reading experience for your visitors. %1$sMore info%2$s"
msgstr "Slikama dodajte atribute širine i visine koji nedostaju. Pomaže u sprječavanju pomicanja elemenata stranice i poboljšava iskustvo čitanja.. %1$sViše informacija%2$s"

#: inc/Engine/Admin/Settings/Page.php:1083
msgid "Enable for images"
msgstr "Uključi za slike"

#: inc/Engine/Admin/Settings/Page.php:1095
#, php-format
msgid "LazyLoad for images is currently activated in %2$s. If you want to use %1$s’s LazyLoad, disable this option in %2$s."
msgstr "Lazyload je trenutno aktivan pomoću %2$s. Ako želite koristiti %1$s lazyload, onemogućite ovu opciju pod%2$s."

#: inc/Engine/Admin/Settings/Page.php:1103
msgid "Enable for iframes and videos"
msgstr "Uključi za iframes i videa"

#: inc/Engine/Admin/Settings/Page.php:1118
msgid "Replace YouTube iframe with preview image"
msgstr "Zamijenite YouTube iframe slikom za pregled"

#: inc/Engine/Admin/Settings/Page.php:1120
#, php-format
msgid "Replace YouTube iframe with preview image is not compatible with %2$s."
msgstr "Zamijenite YouTube iframe slikom za pregled nije kompatibilan s %2$s."

#: inc/Engine/Admin/Settings/Page.php:1120
msgid "This can significantly improve your loading time if you have a lot of YouTube videos on a page."
msgstr "Može značajno poboljšati vrijeme učitavanja ako na stranici imate mnogo videozapisa sa YouTube-a."

#: inc/Engine/Admin/Settings/Page.php:1135
msgid "Excluded images or iframes"
msgstr "Izuzete slike ili iframe-ovi"

#: inc/Engine/Admin/Settings/Page.php:1137
#, php-format
msgid "Specify keywords (e.g. image filename, CSS class, domain) from the image or iframe code to be excluded (one per line). %1$sMore info%2$s"
msgstr "Navedite ključne riječi (npr. naziv datoteke slike, CSS klasa, domena) sa slike ili iframe koda koji će biti izuzeti (po jedna u retku).%1$sViše informacija%2$s"

#: inc/Engine/Admin/Settings/Page.php:1145
msgid "Add missing image dimensions"
msgstr "Dodaj dimenzije slike koje nedostaju"

#: inc/Engine/Admin/Settings/Page.php:1164
#: inc/deprecated/deprecated.php:1776
msgid "Preload"
msgstr "Predučitavanje"

#: inc/Engine/Admin/Settings/Page.php:1165
msgid "Generate cache files, preload fonts"
msgstr "Generiraj cache datoteke, predučitaj fontove"

#: inc/Engine/Admin/Settings/Page.php:1177
msgid "Preload Cache"
msgstr "Predučitaj cache"

#: inc/Engine/Admin/Settings/Page.php:1188
msgid "Preload Links"
msgstr "Predučitavanje linkova"

#: inc/Engine/Admin/Settings/Page.php:1191
#, php-format
msgid "Link preloading improves the perceived load time by downloading a page when a user hovers over the link. %1$sMore info%2$s"
msgstr "Predučitavanje linkova poboljšava percipirano vrijeme učitavanja preuzimanjem stranice kada korisnik zadrži pokazivač iznad linka.%1$sViše informacija%2$s"

#: inc/Engine/Admin/Settings/Page.php:1199
msgid "Prefetch DNS Requests"
msgstr "Prethodno dohvati DNS zahtjeve"

#: inc/Engine/Admin/Settings/Page.php:1201
msgid "DNS prefetching can make external files load faster, especially on mobile networks"
msgstr "Prethodno dohvaćanje (prefetch) DNS-a može ubrzati učitavanje vanjskih datoteka, posebno na mobilnim mrežama"

#: inc/Engine/Admin/Settings/Page.php:1206
msgid "Preload Fonts"
msgstr "Predučitaj fontove"

#: inc/Engine/Admin/Settings/Page.php:1209
#, php-format
msgid "Improves performance by helping browsers discover fonts in CSS files. %1$sMore info%2$s"
msgstr "Poboljšava performanse pomažući preglednicima da otkriju fontove u CSS datotekama. %1$sViše informacija%2$s"

#: inc/Engine/Admin/Settings/Page.php:1223
msgid "Activate Preloading"
msgstr "Aktiviraj predučitavanje"

#: inc/Engine/Admin/Settings/Page.php:1250
msgid "URLs to prefetch"
msgstr "URL-ovi koje treba prethodno dohvatiti (prefetch)"

#: inc/Engine/Admin/Settings/Page.php:1251
msgid "Specify external hosts to be prefetched (no <code>http:</code>, one per line)"
msgstr "Odredite vanjske hostove za pretragu (bez <code>http:</code>, jedan po retku)"

#: inc/Engine/Admin/Settings/Page.php:1260
msgid "Fonts to preload"
msgstr "Fontovi za predučitavanje"

#: inc/Engine/Admin/Settings/Page.php:1261
msgid "Specify urls of the font files to be preloaded (one per line). Fonts must be hosted on your own domain, or the domain you have specified on the CDN tab."
msgstr "Navedite URL-ove datoteka fontova koje treba unaprijed učitati (jedan u retku). Fontovi moraju biti hostani na vašoj domeni ili domeni koju ste naveli u kartici CDN."

#: inc/Engine/Admin/Settings/Page.php:1262
msgid "The domain part of the URL will be stripped automatically.<br/>Allowed font extensions: otf, ttf, svg, woff, woff2."
msgstr "Domena URL-a automatski će biti uklonjena.<br/>Dopuštena proširenja fonta: otf, ttf, svg, woff, woff2."

#: inc/Engine/Admin/Settings/Page.php:1271
msgid "Enable link preloading"
msgstr "Uključi predučitavanje linkova"

#: inc/Engine/Admin/Settings/Page.php:1290
msgid "Advanced Rules"
msgstr "Napredna pravila"

#: inc/Engine/Admin/Settings/Page.php:1291
msgid "Fine-tune cache rules"
msgstr "Fino podešavanje pravila cachiranja"

#: inc/Engine/Admin/Settings/Page.php:1302
msgid "Sensitive pages like custom login/logout URLs should be excluded from cache."
msgstr "Osjetljive stranice kao što su prilagođeni URL-ovi za prijavu / odjavu trebali bi biti izuzeti iz cachea."

#: inc/Engine/Admin/Settings/Page.php:1305
msgctxt "plugin name"
msgid "WooCommerce"
msgstr "WooCommerce"

#: inc/Engine/Admin/Settings/Page.php:1307
msgctxt "plugin name"
msgid "Easy Digital Downloads"
msgstr "Easy Digital Download-ovi"

#: inc/Engine/Admin/Settings/Page.php:1309
msgctxt "plugin name"
msgid "iThemes Exchange"
msgstr "iThemes Exchange"

#: inc/Engine/Admin/Settings/Page.php:1311
msgctxt "plugin name"
msgid "Jigoshop"
msgstr "Jigoshop"

#: inc/Engine/Admin/Settings/Page.php:1313
msgctxt "plugin name"
msgid "WP-Shop"
msgstr "WP-Shop"

#: inc/Engine/Admin/Settings/Page.php:1319
#, php-format
msgid "<br>Cart, checkout and \"my account\" pages set in <strong>%1$s%2$s%3$s</strong> will be detected and never cached by default."
msgstr "<br>Stranice košarice, naplate i \"moj račun\" postavljene u <strong>%1$s%2$s%3$s</strong> će biti detektirane i neće biti cachirane, prema zadanim postavkama."

#: inc/Engine/Admin/Settings/Page.php:1329
#: inc/admin/options.php:129
msgid "Never Cache URL(s)"
msgstr "Nikad ne cachiraj ove URL-ove"

#: inc/Engine/Admin/Settings/Page.php:1337
msgid "Never Cache Cookies"
msgstr "Nikad ne cachiraj ove Kolačiće (Cookies)"

#: inc/Engine/Admin/Settings/Page.php:1343
#: inc/admin/options.php:130
msgid "Never Cache User Agent(s)"
msgstr "Nikada ne cachiraj ove korisničke agente"

#: inc/Engine/Admin/Settings/Page.php:1349
#: inc/admin/options.php:131
msgid "Always Purge URL(s)"
msgstr "Uvijek očisti ove URL-ove"

#: inc/Engine/Admin/Settings/Page.php:1355
msgid "Cache Query String(s)"
msgstr "Nizovi upita za cachiranje (Cache Query String(s))"

#: inc/Engine/Admin/Settings/Page.php:1358
#, php-format
msgid "%1$sCache for query strings%2$s enables you to force caching for specific GET parameters."
msgstr "%1$sCache za nizove upita%2$s  omogućuje vam prisiljavanje cachiranja za određene GET parametre."

#: inc/Engine/Admin/Settings/Page.php:1369
msgid "Specify URLs of pages or posts that should never be cached (one per line)"
msgstr "Navedite URL-ove stranica ili objava koje ne bi smjeli nikad biti cachirani (jedan po retku)"

#: inc/Engine/Admin/Settings/Page.php:1370
#: inc/Engine/Admin/Settings/Page.php:1398
msgid "The domain part of the URL will be stripped automatically.<br>Use (.*) wildcards to address multiple URLs under a given path."
msgstr "Domena URL-a će automatski biti uklonjena.<br>Koristite (.*) zamjenske znakove (wildcard) da biste isključili sve URL-ove koje se nalaze na određenoj putanji."

#: inc/Engine/Admin/Settings/Page.php:1379
msgid "Specify full or partial IDs of cookies that, when set in the visitor's browser, should prevent a page from getting cached (one per line)"
msgstr "Navedite potpune ili djelomične ID-ove kolačića koji bi, kada se postave u pregledniku posjetitelja, trebali spriječiti da se stranica cachira (po jedan u retku)"

#: inc/Engine/Admin/Settings/Page.php:1387
msgid "Specify user agent strings that should never see cached pages (one per line)"
msgstr "Navedite nizove korisničkog agenta koji nikada ne bi trebali vidjeti cachirane (jedna po retku)"

#: inc/Engine/Admin/Settings/Page.php:1388
msgid "Use (.*) wildcards to detect parts of UA strings."
msgstr "Upotrijebite (.*) zamjenske znakove (wildcards) kako bi otkrili dijelove UA nizova."

#: inc/Engine/Admin/Settings/Page.php:1397
msgid "Specify URLs you always want purged from cache whenever you update any post or page (one per line)"
msgstr "Navedite URL-ove koje želite uvijek očistiti iz cachea svaki put kad ažurirate bilo koju objavu ili stranicu (jednu po retku)"

#: inc/Engine/Admin/Settings/Page.php:1406
msgid "Specify query strings for caching (one per line)"
msgstr "Navedite nizove upita za cachiranje (jedan po retku)"

#: inc/Engine/Admin/Settings/Page.php:1431
#: inc/deprecated/deprecated.php:1775
msgid "Database"
msgstr "Baza podataka"

#: inc/Engine/Admin/Settings/Page.php:1432
msgid "Optimize, reduce bloat"
msgstr "Optimizirajte, smanjite višak podataka"

#: inc/Engine/Admin/Settings/Page.php:1439
msgid "Post Cleanup"
msgstr "Čišćenje objava"

#: inc/Engine/Admin/Settings/Page.php:1441
msgid "Post revisions and drafts will be permanently deleted. Do not use this option if you need to retain revisions or drafts."
msgstr "Izmjene objava i skice bit će trajno izbrisani. Ne koristite ovu opciju ako želite zadržati revizije ili skice."

#: inc/Engine/Admin/Settings/Page.php:1446
msgid "Comments Cleanup"
msgstr "Čišćenje komentara"

#: inc/Engine/Admin/Settings/Page.php:1448
msgid "Spam and trashed comments will be permanently deleted."
msgstr "Spam i komentari u smeću će biti trajno izbrisani."

#: inc/Engine/Admin/Settings/Page.php:1452
msgid "Transients Cleanup"
msgstr "Čišćenje transienta"

#: inc/Engine/Admin/Settings/Page.php:1454
msgid "Transients are temporary options; they are safe to remove. They will be automatically regenerated as your plugins require them."
msgstr "Transienti su privremene opcije; sigurno ih je ukloniti. Biti će ponovno automatski generirani ukoliko ih vaši dodaci budu zahtijevali."

#: inc/Engine/Admin/Settings/Page.php:1458
msgid "Database Cleanup"
msgstr "Čišćenje baze podataka"

#: inc/Engine/Admin/Settings/Page.php:1460
msgid "Reduces overhead of database tables"
msgstr "Uklanja suvišne informacije u tablicama baze podataka"

#: inc/Engine/Admin/Settings/Page.php:1464
msgid "Automatic Cleanup"
msgstr "Automatsko čišćenje"

#: inc/Engine/Admin/Settings/Page.php:1477
#, php-format
msgid "%s revision in your database."
msgid_plural "%s revisions in your database."
msgstr[0] "%s revizija u vašoj bazi podataka."
msgstr[1] "%s revizija u vašoj bazi podataka."
msgstr[2] "%s revizija u vašoj bazi podataka."

#: inc/Engine/Admin/Settings/Page.php:1487
#, php-format
msgid "%s draft in your database."
msgid_plural "%s drafts in your database."
msgstr[0] "%s skica u vašoj bazi podataka."
msgstr[1] "%s skica u vašoj bazi podataka."
msgstr[2] "%s skica u vašoj bazi podataka."

#: inc/Engine/Admin/Settings/Page.php:1497
#, php-format
msgid "%s trashed post in your database."
msgid_plural "%s trashed posts in your database."
msgstr[0] "%s objava u smeću u vašoj bazi podataka."
msgstr[1] "%s objava u smeću u vašoj bazi podataka."
msgstr[2] "%s objava u smeću u vašoj bazi podataka."

#: inc/Engine/Admin/Settings/Page.php:1507
#, php-format
msgid "%s spam comment in your database."
msgid_plural "%s spam comments in your database."
msgstr[0] "%s spam komentar u vašoj bazi podataka."
msgstr[1] "%s spam komentara u vašoj bazi podataka."
msgstr[2] "%s spam komentara u vašoj bazi podataka."

#: inc/Engine/Admin/Settings/Page.php:1517
#, php-format
msgid "%s trashed comment in your database."
msgid_plural "%s trashed comments in your database."
msgstr[0] "%s obrisan komentar u vašoj bazi podataka."
msgstr[1] "%s obrisanih komentara u vašoj bazi podataka."
msgstr[2] "%s obrisanih komentara u vašoj bazi podataka."

#: inc/Engine/Admin/Settings/Page.php:1525
msgid "All transients"
msgstr "Svi transienti"

#: inc/Engine/Admin/Settings/Page.php:1527
#, php-format
msgid "%s transient in your database."
msgid_plural "%s transients in your database."
msgstr[0] "%s transient u vašoj bazi podataka."
msgstr[1] "%s transienta u vašoj bazi podataka."
msgstr[2] "%s transienta u vašoj bazi podataka."

#: inc/Engine/Admin/Settings/Page.php:1535
msgid "Optimize Tables"
msgstr "Optimize tablice podataka"

#: inc/Engine/Admin/Settings/Page.php:1537
#, php-format
msgid "%s table to optimize in your database."
msgid_plural "%s tables to optimize in your database."
msgstr[0] "%s tablica za optimizaciju u vašoj bazi podataka."
msgstr[1] "%s tablica za optimizaciju u vašoj bazi podataka."
msgstr[2] "%s tablica za optimizaciju u vašoj bazi podataka."

#: inc/Engine/Admin/Settings/Page.php:1548
msgid "Schedule Automatic Cleanup"
msgstr "Zakaži automatsko čišćenje"

#: inc/Engine/Admin/Settings/Page.php:1560
msgid "Frequency"
msgstr "Učestalost"

#: inc/Engine/Admin/Settings/Page.php:1568
msgid "Daily"
msgstr "Dnevno"

#: inc/Engine/Admin/Settings/Page.php:1569
msgid "Weekly"
msgstr "Tjedno"

#: inc/Engine/Admin/Settings/Page.php:1570
msgid "Monthly"
msgstr "Mjesečno"

#: inc/Engine/Admin/Settings/Page.php:1586
#: inc/Engine/Admin/Settings/Page.php:1597
#: inc/admin/ui/meta-boxes.php:108
#: inc/deprecated/deprecated.php:1773
msgid "CDN"
msgstr "CDN"

#: inc/Engine/Admin/Settings/Page.php:1587
msgid "Integrate your CDN"
msgstr "Integrirajte svoj CDN"

#: inc/Engine/Admin/Settings/Page.php:1599
msgid "All URLs of static files (CSS, JS, images) will be rewritten to the CNAME(s) you provide."
msgstr "Svi URL-ovi statičkih datoteka (CSS, JS, slike) bit će prepisani u CNAME(s) koje navedete."

#: inc/Engine/Admin/Settings/Page.php:1601
#, php-format
msgid "Not required for services like Cloudflare and Sucuri. Please see our available %1$sAdd-ons%2$s."
msgstr "Nije potrebno za servise kao što su Cloudflare i Sucuri. Molimo pogledajte naše dostupne %1$sDodatke%2$s."

#: inc/Engine/Admin/Settings/Page.php:1616
#: inc/admin/options.php:132
msgid "Exclude files from CDN"
msgstr "Izuzmi datoteke iz CDN-a"

#: inc/Engine/Admin/Settings/Page.php:1642
msgid "%1$s%2$l Add-on%3$s is currently enabled. Configuration of the CDN settings is not required for %2$l to work on your site."
msgid_plural "%1$s%2$l Add-ons%3$s are currently enabled. Configuration of the CDN settings is not required for %2$l to work on your site."
msgstr[0] "%1$s%2$l dodatak%3$s je trenutno omogućen. Konfiguracija CDN postavki nije potrebna da bi %2$l radio na vašoj web lokaciji."
msgstr[1] "%1$s%2$l dodatka%3$s su trenutno omogućena. Konfiguracija CDN postavki nije potrebna da bi %2$l radli na vašoj web lokaciji."
msgstr[2] "%1$s%2$l dodataka%3$s je trenutno omogućeno. Konfiguracija CDN postavki nije potrebna da bi %2$l radili na vašoj web lokaciji."

#: inc/Engine/Admin/Settings/Page.php:1667
msgid "Enable Content Delivery Network"
msgstr "Omogućite CDN."

#: inc/Engine/Admin/Settings/Page.php:1676
#: inc/Engine/CDN/RocketCDN/AdminPageSubscriber.php:159
msgid "CDN CNAME(s)"
msgstr "CDN CNAME(s)"

#: inc/Engine/Admin/Settings/Page.php:1677
#: inc/Engine/CDN/RocketCDN/AdminPageSubscriber.php:160
msgid "Specify the CNAME(s) below"
msgstr "U nastavku navedite CNAME(s)"

#: inc/Engine/Admin/Settings/Page.php:1684
msgid "Specify URL(s) of files that should not get served via CDN (one per line)."
msgstr "Navedite URL-ove datoteka koje se ne smiju posluživati ​​putem CDN-a (jedan po retku)."

#: inc/Engine/Admin/Settings/Page.php:1685
msgid "The domain part of the URL will be stripped automatically.<br>Use (.*) wildcards to exclude all files of a given file type located at a specific path."
msgstr "Dio s domenom URL-a će automatski biti uklonjen.<br>Koristite (.*) zamjenske znakove (wildcards) kako biste izuzeli sve datoteke određene vrste smještene na navedenoj putanji."

#: inc/Engine/Admin/Settings/Page.php:1708
#: inc/Engine/Admin/Settings/Page.php:1716
msgid "Heartbeat"
msgstr "Heartbeat"

#: inc/Engine/Admin/Settings/Page.php:1709
msgid "Control WordPress Heartbeat API"
msgstr "Upravljajte WordPress Heartbeat API-em"

#: inc/Engine/Admin/Settings/Page.php:1717
msgid "Reducing or disabling the Heartbeat API’s activity can help save some of your server’s resources."
msgstr "Smanjenje ili onemogućavanje aktivnosti Heartbeat API-ja može pomoći u uštedi resursa vašeg web poslužitelja."

#: inc/Engine/Admin/Settings/Page.php:1723
msgid "Reduce or disable Heartbeat activity"
msgstr "Smanji ili onemogući aktivnost Heartbeata"

#: inc/Engine/Admin/Settings/Page.php:1724
msgid "Reducing activity will change Heartbeat frequency from one hit each minute to one hit every 2 minutes."
msgstr "Smanjenje aktivnosti će promijeniti frekvenciju s jednog \"otkucaja\" svake minute do jednog \"otkucaja\" svake 2 minute."

#: inc/Engine/Admin/Settings/Page.php:1724
msgid "Disabling Heartbeat entirely may break plugins and themes using this API."
msgstr "Onemogućavanje funkcije Heartbeat može u potpunosti onemogućiti pravino funkcioniranje dodataka i /ili teme koja koristi ovaj API."

#: inc/Engine/Admin/Settings/Page.php:1738
msgid "Do not limit"
msgstr "Nemoj ograničiti"

#: inc/Engine/Admin/Settings/Page.php:1739
msgid "Reduce activity"
msgstr "Ograniči aktivnost"

#: inc/Engine/Admin/Settings/Page.php:1740
msgid "Disable"
msgstr "Onemogući"

#: inc/Engine/Admin/Settings/Page.php:1748
msgid "Control Heartbeat"
msgstr "Kontroliraj Heartbeat"

#: inc/Engine/Admin/Settings/Page.php:1757
msgid "Behavior in backend"
msgstr "Ponašanje u backendu"

#: inc/Engine/Admin/Settings/Page.php:1764
msgid "Behavior in post editor"
msgstr "Ponašanje u uređivaču objava"

#: inc/Engine/Admin/Settings/Page.php:1770
msgid "Behavior in frontend"
msgstr "Ponašanje u frontendu"

#: inc/Engine/Admin/Settings/Page.php:1787
#: views/settings/page-sections/tutorials.php:39
msgid "Add-ons"
msgstr "Dodaci"

#: inc/Engine/Admin/Settings/Page.php:1788
msgid "Add more features"
msgstr "Dodajte još značajki"

#: inc/Engine/Admin/Settings/Page.php:1795
msgid "One-click Rocket Add-ons"
msgstr "One-click Rocket dodaci"

#: inc/Engine/Admin/Settings/Page.php:1796
msgid "One-Click Add-ons are features extending available options without configuration needed. Switch the option \"on\" to enable from this screen."
msgstr "One-Click dodaci su značajke koje proširuju dostupne opcije bez potrebe za dodatnom konfiguracijom. Prebacite opciju \"uključeno\" da biste omogućili funkcionalnost s ovog zaslona."

#: inc/Engine/Admin/Settings/Page.php:1806
msgid "Rocket Add-ons"
msgstr "Rocket Dodaci"

#: inc/Engine/Admin/Settings/Page.php:1807
msgid "Rocket Add-ons are complementary features extending available options."
msgstr "Rocket Dodaci su komplementarne značajke koje proširuju dostupne opcije."

#: inc/Engine/Admin/Settings/Page.php:1817
#: inc/Engine/Admin/Settings/Page.php:1986
msgid "Cloudflare"
msgstr "Cloudflare"

#: inc/Engine/Admin/Settings/Page.php:1823
msgid "Integrate your Cloudflare account with this add-on."
msgstr "Integrirajte svoj Cloudflare račun pomoću ovog dodatka."

#: inc/Engine/Admin/Settings/Page.php:1824
msgid "Provide your account email, global API key, and domain to use options such as clearing the Cloudflare cache and enabling optimal settings with WP Rocket."
msgstr "Navedite adresu e-pošte svog računa, globalni API ključ i domenu da biste koristili opcije kao što je brisanje Cloudflare cachea i omogućavanje optimalnih postavki pomoću programa WP Rocket."

#: inc/Engine/Admin/Settings/Page.php:1875
msgid "Varnish"
msgstr "Varnish"

#: inc/Engine/Admin/Settings/Page.php:1881
msgid "If Varnish runs on your server, you must activate this add-on."
msgstr "Ako je Varnish pokrenut na vašem poslužitelju, morate aktivirati ovaj dodatak."

#: inc/Engine/Admin/Settings/Page.php:1883
#, php-format
msgid "Varnish cache will be purged each time WP Rocket clears its cache to ensure content is always up-to-date.<br>%1$sLearn more%2$s"
msgstr "Varnisj cache će biti izbrisan svaki put kada WP Rocket obriše svoj cache kako bi se osiguralo da je sadržaj uvijek ažuriran. <br>%1$sSaznaj više%2$s"

#: inc/Engine/Admin/Settings/Page.php:1918
msgid "WebP Compatibility"
msgstr "WebP kompatibilnost"

#: inc/Engine/Admin/Settings/Page.php:1924
msgid "Improve browser compatibility for WebP images."
msgstr "Poboljšajte kompatibilnost preglednika za slike u WebP formatu."

#: inc/Engine/Admin/Settings/Page.php:1928
#, php-format
msgid "Enable this option if you would like WP Rocket to serve WebP images to compatible browsers. Please note that WP Rocket cannot create WebP images for you. To create WebP images we recommend %1$sImagify%2$s. %3$sMore info%2$s"
msgstr "Omogućite ovu opciju ako želite da WP Rocket poslužuje WebP slike na kompatibilnim preglednicima. Imajte na umu da WP Rocket ne može stvoriti WebP slike za vas. Za stvaranje WebP slika preporučujemo %1$sImagify%2$s. %3$sViše informacija%2$s"

#: inc/Engine/Admin/Settings/Page.php:1948
msgid "Clear the Sucuri cache when WP Rocket’s cache is cleared."
msgstr "Očistite Sucuri cache kada je obrisan cache WP Rocketa."

#: inc/Engine/Admin/Settings/Page.php:1951
msgid "Provide your API key to clear the Sucuri cache when WP Rocket’s cache is cleared."
msgstr "Upišite svoj API ključ za brisanje Sucuri cachea kada je izbrisan cache WP Rocketa."

#: inc/Engine/Admin/Settings/Page.php:1959
#: inc/Engine/Admin/Settings/Page.php:2103
msgid "Sucuri"
msgstr "Sucuri"

#: inc/Engine/Admin/Settings/Page.php:1965
msgid "Synchronize Sucuri cache with this add-on."
msgstr "Sinkronizirajte Sucuri cache pomoću ovog dodatka."

#: inc/Engine/Admin/Settings/Page.php:2003
msgid "Cloudflare credentials"
msgstr "Cloudflare podaci"

#: inc/Engine/Admin/Settings/Page.php:2012
msgid "Cloudflare settings"
msgstr "Cloudflare postavke"

#: inc/Engine/Admin/Settings/Page.php:2026
msgctxt "Cloudflare"
msgid "Global API key:"
msgstr "Globalni API ključ:"

#: inc/Engine/Admin/Settings/Page.php:2027
msgctxt "Cloudflare"
msgid "Find your API key"
msgstr "Pronađi svoj API ključ"

#: inc/Engine/Admin/Settings/Page.php:2039
msgctxt "Cloudflare"
msgid "Account email"
msgstr "Email računa"

#: inc/Engine/Admin/Settings/Page.php:2048
msgctxt "Cloudflare"
msgid "Zone ID"
msgstr "Zone ID"

#: inc/Engine/Admin/Settings/Page.php:2058
msgid "Development mode"
msgstr "Development mode"

#: inc/Engine/Admin/Settings/Page.php:2060
#, php-format
msgid "Temporarily activate development mode on your website. This setting will automatically turn off after 3 hours. %1$sLearn more%2$s"
msgstr "Privremeno aktivirajte Development mode na web-lokaciji. Ova postavka će se automatski isključiti nakon 3 sata. %1$sSaznaj više%2$s"

#: inc/Engine/Admin/Settings/Page.php:2068
msgid "Optimal settings"
msgstr "Optimalne postavke"

#: inc/Engine/Admin/Settings/Page.php:2069
msgid "Automatically enhances your Cloudflare configuration for speed, performance grade and compatibility."
msgstr "Automatski poboljšava Cloudflare konfiguraciju za brzinu, ocjenu izvedbe i kompatibilnost."

#: inc/Engine/Admin/Settings/Page.php:2077
msgid "Relative protocol"
msgstr "Relativni protokol"

#: inc/Engine/Admin/Settings/Page.php:2078
msgid "Should only be used with Cloudflare's flexible SSL feature. URLs of static files (CSS, JS, images) will be rewritten to use // instead of http:// or https://."
msgstr "Treba se koristiti samo s Cloudflare-ovom fleksibilnom SSL značajkom. URL-ovi statičkih datoteka (CSS, JS, slike) bit će prepisani tako da koriste // umjesto http:// ili https://."

#: inc/Engine/Admin/Settings/Page.php:2116
msgid "Sucuri credentials"
msgstr "Sucuri podaci"

#: inc/Engine/Admin/Settings/Page.php:2130
msgctxt "Sucuri"
msgid "Find your API key"
msgstr "Pronađi svoj API ključ"

#: inc/Engine/Admin/Settings/Render.php:422
#: inc/deprecated/deprecated.php:1294
msgid "Upload file and import settings"
msgstr "Upload datoteke i uvoz settings"

#: inc/Engine/Admin/Settings/Settings.php:362
msgid "Sucuri Add-on: The API key for the Sucuri firewall must be in format <code>{32 characters}/{32 characters}</code>."
msgstr "Dodatak za Sucuri: API ključ za Sucuri firewall mora biti u formatu<code>{32 znaka}/{32 znaka}.</code>."

#: inc/Engine/Admin/Settings/Settings.php:452
#: inc/deprecated/deprecated.php:1245
msgid "Settings saved."
msgstr "Settings spremljene."

#: inc/Engine/Admin/Settings/Settings.php:668
msgid "Sorry! Adding /(.*) in Advanced Rules > Never Cache URL(s) was not saved because it disables caching and optimizations for every page on your site."
msgstr "Oprostite! Dodavanje /(.*) u Napredna pravila > Nikada ne cacheiraj URL(ove) nije spremljeno jer onemogućuje predmemoriranje i optimizacije za svaku stranicu na vašoj web-lokaciji."

#: inc/Engine/Admin/Settings/Subscriber.php:168
#: inc/deprecated/deprecated.php:1786
#: views/settings/page-sections/tools.php:33
msgid "Tools"
msgstr "Alati"

#: inc/Engine/Admin/Settings/Subscriber.php:169
msgid "Import, Export, Rollback"
msgstr "Uvoz, izvoz, povrat postavki"

#: inc/Engine/Admin/Settings/Subscriber.php:194
#: views/settings/page-sections/imagify.php:14
msgid "Image Optimization"
msgstr "Optimizacija slika"

#: inc/Engine/Admin/Settings/Subscriber.php:195
msgid "Compress your images"
msgstr "Sažmite slike"

#: inc/Engine/Admin/Settings/Subscriber.php:212
#: views/settings/page-sections/tutorials.php:48
msgid "Tutorials"
msgstr "Upute"

#: inc/Engine/Admin/Settings/Subscriber.php:213
msgid "Getting started and how to videos"
msgstr "Početak rada i edukativni videozapisi"

#: inc/Engine/CDN/RocketCDN/APIClient.php:134
msgid "We could not fetch the current price because RocketCDN API returned an unexpected error code."
msgstr "Nismo mogli dohvatiti trenutnu cijenu jer je RocketCDN API vratio neočekivani kod pogreške."

#: inc/Engine/CDN/RocketCDN/APIClient.php:140
msgid "RocketCDN is not available at the moment. Please retry later."
msgstr "RocketCDN trenutno nije dostupan. Pokušajte opet malo kasnije."

#: inc/Engine/CDN/RocketCDN/APIClient.php:177
msgid "RocketCDN cache purge failed: Missing identifier parameter."
msgstr "Čišćenje RocketCDN cachea nije uspjelo: nedostaje parametar identifikatora."

#: inc/Engine/CDN/RocketCDN/APIClient.php:186
msgid "RocketCDN cache purge failed: Missing user token."
msgstr "Čišćenje RocketCDN cachea nije uspjelo: nedostaje korisnički token."

#: inc/Engine/CDN/RocketCDN/APIClient.php:212
msgid "RocketCDN cache purge failed: The API returned an unexpected response code."
msgstr "Čišćenje RocketCDN cachea nije uspjelo: API je vratio neočekivani kôd odgovora."

#: inc/Engine/CDN/RocketCDN/APIClient.php:221
msgid "RocketCDN cache purge failed: The API returned an empty response."
msgstr "Čišćenje RocketCDN cachea nije uspjelo: API je vratio prazan odgovor."

#: inc/Engine/CDN/RocketCDN/APIClient.php:230
msgid "RocketCDN cache purge failed: The API returned an unexpected response."
msgstr "Čišćenje RocketCDN cachea nije uspjelo: API je vratio neočekivani odgovor."

#: inc/Engine/CDN/RocketCDN/APIClient.php:239
#, php-format
msgid "RocketCDN cache purge failed: %s."
msgstr "Čišćenje RocketCDN cachea nije uspjelo: %s."

#: inc/Engine/CDN/RocketCDN/APIClient.php:247
msgid "RocketCDN cache purge successful."
msgstr "Čišćenje RocketCDN cachea je uspješno."

#: inc/Engine/CDN/RocketCDN/AdminPageSubscriber.php:90
msgid "Next Billing Date"
msgstr "Sljedeći datum naplate"

#: inc/Engine/CDN/RocketCDN/AdminPageSubscriber.php:99
msgid "No Subscription"
msgstr "Nema pretplate"

#: inc/Engine/CDN/RocketCDN/AdminPageSubscriber.php:135
msgid "Your RocketCDN subscription is currently active."
msgstr "Vaša RocketCDN pretplata je trenutno aktivna."

#: inc/Engine/CDN/RocketCDN/AdminPageSubscriber.php:141
#, php-format
msgid "To use RocketCDN, replace your CNAME with %1$s%2$s%3$s."
msgstr "Da biste koristili RocketCDN, zamijenite svoj CNAME s %1$s%2$s%3$s."

#: inc/Engine/CDN/RocketCDN/AdminPageSubscriber.php:152
#: inc/Engine/CDN/RocketCDN/NoticesSubscriber.php:230
#: inc/Engine/CDN/RocketCDN/NoticesSubscriber.php:334
#, php-format
msgid "%1$sMore Info%2$s"
msgstr "%1$sViše informacija%2$s"

#: inc/Engine/CDN/RocketCDN/RESTSubscriber.php:137
msgid "RocketCDN enabled"
msgstr "Omogućen RocketCDN"

#: inc/Engine/CDN/RocketCDN/RESTSubscriber.php:160
msgid "RocketCDN disabled"
msgstr "RocketCDN onemogućen"

#: inc/Engine/CDN/RocketCDN/views/cta-big.php:27
#, php-format
msgid "Valid until %s only!"
msgstr "Vrijedi samo do %s !"

#: inc/Engine/CDN/RocketCDN/views/cta-big.php:36
msgid "Speed up your website thanks to:"
msgstr "Ubrzajte svoju web stranicu zahvaljujući:"

#: inc/Engine/CDN/RocketCDN/views/cta-big.php:48
#, php-format
msgid "Easy configuration: the %1$sbest CDN settings%2$s are automatically applied"
msgstr "Jednostavna konfiguracija:  %1$snajbolje CDN postavke%2$s su automatski primijenjene"

#: inc/Engine/CDN/RocketCDN/views/cta-big.php:54
#, php-format
msgid "WP Rocket integration: the CDN option is %1$sautomatically configured%2$s in our plugin"
msgstr "WP Rocket integracija: CDN je %1$sautomatski konfiguriran%2$s u našem pluginu"

#: inc/Engine/CDN/RocketCDN/views/cta-big.php:58
msgid "Learn more about RocketCDN"
msgstr "Saznaj više o RocketCDN-u"

#: inc/Engine/CDN/RocketCDN/views/cta-big.php:65
#, php-format
msgid "*$%1$s/month for 12 months then $%2$s/month. You can cancel your subscription at any time."
msgstr "* $%1$s/mjesec za 12 mjeseci a zatim $%2$s/mjesec. Svoju pretplatu možete otkazati u svakom trenutku."

#: inc/Engine/CDN/RocketCDN/views/cta-big.php:86
msgid "Billed monthly"
msgstr "Naplaćuje se mjesečno"

#: inc/Engine/CDN/RocketCDN/views/cta-big.php:87
msgid "Get Started"
msgstr "Započni"

#: inc/Engine/CDN/RocketCDN/views/cta-big.php:92
msgid "Reduce this banner"
msgstr "Smanji ovaj banner"

#: inc/Engine/CDN/RocketCDN/views/cta-small.php:17
msgid "Speed up your website with RocketCDN, WP Rocket’s Content Delivery Network."
msgstr "Ubrzajte svoju web stranicu pomoću RocketCDN-a od WP Rocketa."

#: inc/Engine/CDN/RocketCDN/views/cta-small.php:20
#: inc/Engine/CDN/RocketCDN/views/promote-notice.php:13
msgid "Learn More"
msgstr "Saznaj više"

#: inc/Engine/CDN/RocketCDN/views/dashboard-status.php:23
msgid "RocketCDN is unavailable on local domains and staging sites."
msgstr "RocketCDN nije dostupan na lokalnim domenama i staging webovima."

#: inc/Engine/CDN/RocketCDN/views/dashboard-status.php:32
msgid "Get RocketCDN"
msgstr "Nabavi RocketCDN"

#: inc/Engine/CDN/RocketCDN/views/promote-notice.php:11
msgid "New!"
msgstr "Novo!"

#: inc/Engine/CDN/RocketCDN/views/promote-notice.php:12
msgid "Speed up your website with RocketCDN, WP Rocket’s Content Delivery Network!"
msgstr "Ubrzajte svoju web stranicu pomoću RocketCDN-a od WP Rocketa."

#: inc/Engine/Cache/AdminSubscriber.php:118
#: inc/admin/admin.php:96
#: inc/admin/admin.php:118
#: inc/deprecated/3.5.php:898
msgid "Clear this cache"
msgstr "Očisti ovo iz cachea"

#: inc/Engine/Cache/PurgeExpired/Subscriber.php:75
msgid "WP Rocket Expired Cache Interval"
msgstr "WP Rocket interval isteklog cachea"

#: inc/Engine/Cache/WPCache.php:337
msgid "WP_CACHE value"
msgstr "WP_CACHE vrijednost"

#: inc/Engine/Cache/WPCache.php:358
msgid "The WP_CACHE constant needs to be set to true for WP Rocket cache to work properly"
msgstr "WP_CACHE konstanta mora biti postavljena na true da bi WP Rocket cache radio ispravno"

#: inc/Engine/Cache/WPCache.php:367
msgid "WP_CACHE is set to true"
msgstr "WP_CACHE je postavljen na true"

#: inc/Engine/Cache/WPCache.php:375
msgid "WP_CACHE is not set"
msgstr "WP_CACHE nijje postavljen"

#: inc/Engine/Cache/WPCache.php:383
msgid "WP_CACHE is set to false"
msgstr "WP_CACHE je postavljen na false"

#: inc/Engine/Common/Queue/RUCSSQueueRunner.php:253
msgid "Every minute"
msgstr "Svake minute"

#: inc/Engine/CriticalPath/APIClient.php:64
#, php-format
msgid "Critical CSS for %1$s not generated. Error: %2$s"
msgstr "Ključni CSS za %1$s nije generiran. Greška: %2$s"

#: inc/Engine/CriticalPath/APIClient.php:170
#, php-format
msgid "Critical CSS for %1$s on mobile not generated. Error: The API returned an empty response."
msgstr "Ključni CSS za %1$s na mobilnim uređajima nije generiran. Greška: API je vratio prazan odgovor."

#: inc/Engine/CriticalPath/APIClient.php:173
#, php-format
msgid "Critical CSS for %1$s not generated. Error: The API returned an empty response."
msgstr "Ključni CSS za %1$s nije generiran. Greška: API je vratio prazan odgovor."

#: inc/Engine/CriticalPath/APIClient.php:185
#, php-format
msgid "Critical CSS for %1$s on mobile not generated."
msgstr "Ključni CSS za %1$s na mobilnim uređajima nije generiran."

#: inc/Engine/CriticalPath/APIClient.php:187
#: inc/Engine/CriticalPath/ProcessorService.php:194
#, php-format
msgid "Critical CSS for %1$s not generated."
msgstr "Ključni CSS za %1$s nije generiran."

#: inc/Engine/CriticalPath/APIClient.php:195
#, php-format
msgid "Critical CSS for %1$s on mobile not generated. Error: The API returned an invalid response code."
msgstr "Ključni CSS za %1$s na mobilnim uređajima nije generiran. Greška: API je vratio nevažeći kôd odgovora."

#: inc/Engine/CriticalPath/APIClient.php:197
#, php-format
msgid "Critical CSS for %1$s not generated. Error: The API returned an invalid response code."
msgstr "Ključni CSS za %1$s nije generiran. Greška: API je vratio nevažeći kôd odgovora."

#: inc/Engine/CriticalPath/APIClient.php:205
#: inc/Engine/CriticalPath/ProcessorService.php:201
#, php-format
msgid "Error: %1$s"
msgstr "Greška: %1$s"

#: inc/Engine/CriticalPath/Admin/Admin.php:264
msgid "Regenerate Critical Path CSS"
msgstr "Obnovi putanju ključnog CSS-a"

#: inc/Engine/CriticalPath/Admin/Post.php:144
#: views/cpcss/metabox/generate.php:47
msgid "Generate Specific CPCSS"
msgstr "Stvaranje specifičnog CPCSS-a"

#: inc/Engine/CriticalPath/Admin/Post.php:145
#: views/cpcss/metabox/generate.php:45
msgid "Regenerate specific CPCSS"
msgstr "Ponovno stvaranje specifičnog CPCSS-a"

#: inc/Engine/CriticalPath/Admin/Post.php:216
msgid "This feature is not available for non-public post types."
msgstr "Ova značajka nije dostupna za vrste objava koje nisu javne."

#: inc/Engine/CriticalPath/Admin/Post.php:219
msgid "%l to use this feature."
msgstr "%l da koristite ovu značajku."

#: inc/Engine/CriticalPath/Admin/Post.php:222
#, php-format
msgid "Publish the %s"
msgstr "Objavi %s"

#: inc/Engine/CriticalPath/Admin/Post.php:223
msgid "Enable Load CSS asynchronously in WP Rocket settings"
msgstr "Omogućite asinhrono učitavanje CSS-a u postavkama WP Rocketa"

#: inc/Engine/CriticalPath/Admin/Post.php:224
msgid "Enable Load CSS asynchronously in the options above"
msgstr "Omogućite asinhrono učitavanje CSS-a u gornjim opcijama"

#: inc/Engine/CriticalPath/CriticalCSSSubscriber.php:176
msgid "Critical CSS generation is currently running."
msgstr "Trenutno je aktivno generiranje ključnog CSS-a."

#: inc/Engine/CriticalPath/CriticalCSSSubscriber.php:181
#, php-format
msgid "Go to the %1$sWP Rocket settings%2$s page to track progress."
msgstr "Idite na stranicu%1$s postavki WP Rocketa%2$s za praćenje napretka."

#: inc/Engine/CriticalPath/CriticalCSSSubscriber.php:396
#, php-format
msgid "Critical CSS generation is currently running: %1$d of %2$d page types completed. (Refresh this page to view progress)"
msgstr "Trenutno se izvodi generiranje ključnog CSS-a: %1$d od %2$d vrsta stranica dovršeno (Osvježite ovu stranicu kako biste vidjeli napredak)"

#: inc/Engine/CriticalPath/CriticalCSSSubscriber.php:472
#, php-format
msgid "Critical CSS generation finished for %1$d of %2$d page types."
msgstr "Generiranje ključnog CSS-a za %1$d od %2$d vrsta stranica."

#: inc/Engine/CriticalPath/CriticalCSSSubscriber.php:479
msgid "Critical CSS generation encountered one or more errors."
msgstr "Generiranje ključnog CSS-a naišlo je na jednu ili više pogrešaka."

#: inc/Engine/CriticalPath/CriticalCSSSubscriber.php:479
msgid "Learn more."
msgstr "Saznaj više"

#: inc/Engine/CriticalPath/DataManager.php:68
#, php-format
msgid "Critical CSS for %1$s on mobile not generated. Error: The destination folder could not be created."
msgstr "Ključni CSS za %1$s na mobilnim uređajima nije generiran. Greška: nije moguće stvoriti odredišnu mapu."

#: inc/Engine/CriticalPath/DataManager.php:71
#, php-format
msgid "Critical CSS for %1$s not generated. Error: The destination folder could not be created."
msgstr "Ključni CSS za %1$s nije generiran. Greška: nije moguće stvoriti odredišnu mapu."

#: inc/Engine/CriticalPath/DataManager.php:106
msgid "Critical CSS file for mobile does not exist"
msgstr "Datoteka ključnog CSS-a za mobilne uređaje ne postoji."

#: inc/Engine/CriticalPath/DataManager.php:108
msgid "Critical CSS file does not exist"
msgstr "Datoteka ključnog CSS-a ne postoji."

#: inc/Engine/CriticalPath/DataManager.php:120
msgid "Critical CSS file for mobile cannot be deleted"
msgstr "Datoteka kritičnog CSS-a za mobilne uređaje ne može biti izbrisana"

#: inc/Engine/CriticalPath/DataManager.php:122
msgid "Critical CSS file cannot be deleted"
msgstr "Datoteka kritičnog CSS-a ne može biti izbrisana"

#: inc/Engine/CriticalPath/ProcessorService.php:187
#, php-format
msgid "Mobile Critical CSS for %1$s not generated."
msgstr "Ključni CSS na mobilnim uređajima za%1$s nije generiran."

#: inc/Engine/CriticalPath/ProcessorService.php:228
#, php-format
msgid "Critical CSS for %s in progress."
msgstr "Ključni CSS za %s u tijeku."

#: inc/Engine/CriticalPath/ProcessorService.php:262
#, php-format
msgid "Mobile Critical CSS for %s generated."
msgstr "Ključni CSS na mobilnim uređajima za %s je generiran."

#: inc/Engine/CriticalPath/ProcessorService.php:273
#, php-format
msgid "Critical CSS for %s generated."
msgstr "Ključni CSS za %s je generiran."

#: inc/Engine/CriticalPath/ProcessorService.php:295
msgid "Critical CSS file deleted successfully."
msgstr "Datoteka kritičnog CSS-a uspješno izbrisana."

#: inc/Engine/CriticalPath/ProcessorService.php:317
#, php-format
msgid "Mobile Critical CSS for %1$s timeout. Please retry a little later."
msgstr "Ključni CSS na mobilnim uređajima za %1$s timeout. Probajte kasnije."

#: inc/Engine/CriticalPath/ProcessorService.php:330
#, php-format
msgid "Critical CSS for %1$s timeout. Please retry a little later."
msgstr "Ključni CSS za %1$s timeout. Probajte kasnije."

#: inc/Engine/CriticalPath/RESTWP.php:141
msgid "Mobile CPCSS generation not enabled."
msgstr "Generiranje mobilnog CPCSS-a nije aktivirano."

#: inc/Engine/CriticalPath/RESTWPPost.php:36
#: inc/Engine/CriticalPath/RESTWPPost.php:69
msgid "Requested post does not exist."
msgstr "Zatraženi post ne postoji."

#: inc/Engine/CriticalPath/RESTWPPost.php:46
msgid "Cannot generate CPCSS for unpublished post."
msgstr "Ne može se generirati CPCSS za neobjavljeni post."

#: inc/Engine/HealthCheck/HealthCheck.php:82
#: inc/deprecated/3.5.php:858
msgid "The following scheduled event failed to run. This may indicate the CRON system is not running properly, which can prevent some WP Rocket features from working as intended:"
msgid_plural "The following scheduled events failed to run. This may indicate the CRON system is not running properly, which can prevent some WP Rocket features from working as intended:"
msgstr[0] "Sljedeći planirani događaj nije uspio. To može značiti da CRON sustav ne radi ispravno, što može spriječiti da neke značajke WP Rocketa rade kako je predviđeno:"
msgstr[1] "Sljedeći planirani događaji nisu uspjeli. To može značiti da CRON sustav ne radi ispravno, što može spriječiti da neke značajke WP Rocketa rade kako je predviđeno:"
msgstr[2] "Sljedeći planirani događaji nisu uspjeli. To može značiti da CRON sustav ne radi ispravno, što može spriječiti da neke značajke WP Rocketa rade kako je predviđeno:"

#: inc/Engine/HealthCheck/HealthCheck.php:88
#: inc/deprecated/3.5.php:867
msgid "Please contact your host to check if CRON is working."
msgstr "Molimo kontaktirajte svoj hosting kako biste provjerili radi li CRON."

#: inc/Engine/HealthCheck/HealthCheck.php:142
msgid "Scheduled Cache Purge"
msgstr "Zakazano čišćene cachea"

#: inc/Engine/HealthCheck/HealthCheck.php:143
msgid "Scheduled Database Optimization"
msgstr "Zakazana optimizacija baze podataka"

#: inc/Engine/HealthCheck/HealthCheck.php:144
msgid "Database Optimization Process"
msgstr "Postupak optimizacije baze podataka"

#: inc/Engine/HealthCheck/HealthCheck.php:145
msgctxt "noun"
msgid "Preload"
msgstr "Predučitavanje"

#: inc/Engine/HealthCheck/HealthCheck.php:146
msgid "Critical Path CSS Generation Process"
msgstr "Postupak stvaranja ključnog puta CSS-a"

#: inc/Engine/License/Upgrade.php:252
#, php-format
msgid "Take advantage of %1$s to speed up more websites:%2$s get a %3$s%4$s off%5$s for %3$supgrading your license to Plus or Infinite!%5$s"
msgid_plural "Take advantage of %1$s to speed up more websites:%2$s get a %3$s%4$s off%5$s for %3$supgrading your license to Infinite!%5$s"
msgstr[0] "Iskoristite %1$s za ubrzavanje više web stranica:%2$s dobijete %3$s%4$s popusta%5$s za %3$snadogradnju vaše licence na Infinite!%5$s"
msgstr[1] "Iskoristite %1$s za ubrzavanje više web stranica:%2$s dobijete %3$s%4$s popusta%5$s za %3$snadogradnju vaše licence na Infinite!%5$s"
msgstr[2] "Iskoristite %1$s za ubrzavanje više web stranica:%2$s dobijete %3$s%4$s popusta%5$s za %3$snadogradnju vaše licence na Infinite!%5$s"

#: inc/Engine/License/Upgrade.php:382
#: inc/Engine/License/Upgrade.php:405
msgid "Unlimited"
msgstr "Neograničeno"

#: inc/Engine/License/views/promo-banner.php:16
#, php-format
msgid "%s off"
msgstr "%s popust"

#: inc/Engine/License/views/promo-banner.php:21
#, php-format
msgid "%s promotion is live!"
msgstr "%s promocija je aktivna!"

#: inc/Engine/License/views/promo-banner.php:27
msgid "Hurry Up! Deal ends in:"
msgstr "Požuri! Ponuda završava za:"

#: inc/Engine/License/views/promo-banner.php:31
#: inc/Engine/License/views/renewal-soon-banner.php:14
msgid "Minutes"
msgstr "Minuta"

#: inc/Engine/License/views/promo-banner.php:32
#: inc/Engine/License/views/renewal-soon-banner.php:15
msgid "Seconds"
msgstr "Sekundi"

#: inc/Engine/License/views/promo-banner.php:34
#: inc/Engine/License/views/upgrade-section.php:11
msgid "Upgrade now"
msgstr "Nadogradi sada"

#: inc/Engine/License/views/promo-banner.php:36
#: inc/Engine/License/views/renewal-expired-banner-ocd-disabled.php:32
#: inc/Engine/License/views/renewal-expired-banner-ocd.php:42
#: inc/Engine/License/views/renewal-expired-banner.php:30
#: inc/admin/ui/notices.php:739
#: views/settings/page-sections/dashboard.php:46
msgid "Dismiss this notice"
msgstr "Uklonite ovu obavijest"

#: inc/Engine/License/views/renewal-expired-banner.php:11
msgid "Your WP Rocket license is expired!"
msgstr "Vaša WP Rocket licenca je istekla!"

#: inc/Engine/License/views/renewal-expired-banner-ocd-disabled.php:29
#: inc/Engine/License/views/renewal-expired-banner-ocd.php:39
#: inc/Engine/License/views/renewal-expired-banner.php:27
#: inc/Engine/License/views/renewal-soon-banner.php:31
msgid "Renew now"
msgstr "Obnovi odmah"

#: inc/Engine/License/views/renewal-soon-banner.php:22
#, php-format
msgid "Your %1$sWP Rocket license is about to expire%2$s: you will soon lose access to product updates and support."
msgstr "Vaša %1$sWP Rocket licenca uskoro ističe%2$s: uskoro ćete izgubiti pristup ažuriranjima i podršci."

#: inc/Engine/License/Renewal.php:227
#, php-format
msgid "Renew with a %1$s%2$s discount%3$s before it is too late, you will only pay %1$s%4$s%3$s!"
msgstr "Obnovite uz %1$s%2$s popust%3$s prije nego što bude prekasno. Platit ćete samo %1$s%4$s%3$s!"

#: inc/Engine/License/views/upgrade-popin.php:12
msgid "Speed Up More Websites"
msgstr "Ubrzajte više web stranica"

#: inc/Engine/License/views/upgrade-popin.php:19
#, php-format
msgid "You can use WP Rocket on more websites by upgrading your license. To upgrade, simply pay the %1$sprice difference%2$s between your current and new licenses, as shown below."
msgstr "WP Rocket možete koristiti na više web stranica nadogradnjom licence. Za nadogradnju jednostavno platite %1$srazliku u cijeni%2$s između vaše trenutne i nove licence, kao što je prikazano u nastavku."

#: inc/Engine/License/views/upgrade-popin.php:25
#, php-format
msgid "%1$sN.B.%2$s: Upgrading your license does not change your expiration date"
msgstr "%1$sNapomena%2$s: Nadogradnja licence ne mijenja datum isteka"

#: inc/Engine/License/views/upgrade-popin.php:35
#, php-format
msgid "Save $%s"
msgstr "Uštedite $%s"

#: inc/Engine/License/views/upgrade-popin.php:48
#, php-format
msgid "%s websites"
msgstr "%s web stranica"

#: inc/Engine/License/views/upgrade-popin.php:54
#, php-format
msgid "Upgrade to %s"
msgstr "Nadogradite na %s"

#: inc/Engine/License/views/upgrade-section.php:11
msgid "You can use WP Rocket on more websites by upgrading your license (you will only pay the price difference between your current and new licenses)."
msgstr "WP Rocket možete koristiti na više web lokacija nadogradnjom licence (plaćat ćete samo razliku u cijeni između trenutne i nove licence)."

#: inc/Engine/Optimization/RUCSS/Admin/Settings.php:131
#: views/settings/page-sections/dashboard.php:188
msgid "Clear Used CSS"
msgstr "Očisti korišteni CSS"

#: inc/Engine/Optimization/RUCSS/Admin/Settings.php:219
#, php-format
msgid "%1$s: Please wait %2$s seconds. The Remove Unused CSS service is processing your pages."
msgstr "%1$s: Molimo pričekajte %2$s sekundi. Značajka Uklanjanje nekorištenog CSS-a obrađuje vaše stranice."

#: inc/Engine/Optimization/RUCSS/Admin/Settings.php:264
#, php-format
msgid "%1$s: The Used CSS of your homepage has been processed. WP Rocket will continue to generate Used CSS for up to %2$s URLs per %3$s second(s)."
msgstr "%1$s: Korišteni CSS vaše početne stranice je obrađen. WP Rocket će nastaviti generirati korišteni CSS do %2$s URL-a po %3$s sekundi."

#: inc/Engine/Optimization/RUCSS/Admin/Settings.php:283
#, php-format
msgid "To learn more about the process check our %1$sdocumentation%2$s."
msgstr "Da biste saznali više o procesu, provjerite našu %1$sdokumentaciju%2$s."

#: inc/Engine/Optimization/RUCSS/Admin/Settings.php:481
msgid "We couldn't generate the used CSS because you're using a nulled version of WP Rocket. You need an active license to use the Remove Unused CSS feature and further improve your website's performance."
msgstr "Nismo mogli generirati korišteni CSS jer koristite \"nulled\" verziju WP Rocketa. Potrebna vam je aktivna licenca da biste koristili značajku Ukloni neiskorišteni CSS i dodatno poboljšali izvedbu svoje web stranice."

#: inc/Engine/Optimization/RUCSS/Admin/Settings.php:484
#, php-format
msgid "Click here to get a WP Rocket single license at %1$s off!"
msgstr "Kliknite ovdje da biste dobili jednu licencu za WP Rocket na %1$s popusta!"

#: inc/Engine/Optimization/RUCSS/Admin/Subscriber.php:322
#, php-format
msgid "%1$s: Used CSS option is not enabled!"
msgstr "%1$s: Opcija korištenog CSS-a nije omogućena!"

#: inc/Engine/Optimization/RUCSS/Admin/Subscriber.php:343
#, php-format
msgid "%1$s: Used CSS cache cleared!"
msgstr "%1$s: Cache korištenog CSS-a je očišćen!"

#: inc/Engine/Optimization/RUCSS/Controller/UsedCSS.php:776
msgid "Clear Used CSS of this URL"
msgstr "Obrišite korišteni CSS ovog URL-a"

#: inc/Engine/Plugin/UpdaterApiTools.php:32
#: inc/deprecated/3.3.php:761
#: inc/deprecated/3.3.php:770
#, php-format
msgid "An unexpected error occurred. Something may be wrong with WP-Rocket.me or this server&#8217;s configuration. If you continue to have problems, <a href=\"%s\">contact support</a>."
msgstr "Došlo je do neočekivane pogreške. Nešto možda nije u redu s WP-Rocket.me ili konfiguracijom ovog poslužitelja. Ako i dalje imate problema, <a href=\"%s\">kontaktirajte podršku</a>."

#: inc/ThirdParty/Hostings/Cloudways.php:82
#, php-format
msgid "Varnish auto-purge will be automatically enabled once Varnish is enabled on your %s server."
msgstr "Varnish automatsko pročišćavanje automatski će se omogućiti nakon što omogućite Varnish na vašem %s serveru."

#: inc/ThirdParty/Hostings/Kinsta.php:158
#, php-format
msgid "Your installation seems to be missing core Kinsta files managing Cache clearing, which will prevent your Kinsta installation and WP Rocket from working correctly. Please get in touch with Kinsta support through your %1$sMyKinsta%2$s account to resolve this issue."
msgstr "Čini se da vašoj instalaciji nedostaju Kinsta datoteke koje upravljaju čišćenjem Cachea, što će spriječiti ispravno postavljanje vaše Kinsta instalacije i WP Rocketa. Molimo kontaktirajte Kinsta podršku putem vašeg %1$sMyKinsta%2$s računa kako biste riješili ovaj problem."

#: inc/ThirdParty/Plugins/ModPagespeed.php:102
#, php-format
msgid "<strong>%1$s</strong>: Mod PageSpeed is not compatible with this plugin and may cause unexpected results. %2$sMore Info%3$s"
msgstr "<strong>%1$s</strong>: Mod PageSpeed ​​nije kompatibilan s ovim dodatkom i može dovesti do neočekivanih rezultata. %2$sViše infromacija%3$s"

#: inc/ThirdParty/Plugins/Optimization/Autoptimize.php:76
#, php-format
msgid "%1$sWP Rocket: %2$sWe have detected that Autoptimize's JavaScript Aggregation feature is enabled. WP Rocket's Delay JavaScript Execution will not be applied to the file it creates. We suggest disabling %1$sJavaScript Aggregation%2$s to take full advantage of Delay JavaScript Execution."
msgstr "%1$sWP Rocket: %2$sOtkrili smo da je omogućena značajka JavaScript Aggregation Autoptimize plugina. WP Rocketova odgoda izvršavanja JavaScripta neće se primijeniti na datoteku koju kreira. Predlažemo onemogućavanje %1$sJavaScript Aggregation%2$s kako biste u potpunosti iskoristili prednosti odgode izvršavanja JavaScripta."

#: inc/ThirdParty/Plugins/Optimization/Autoptimize.php:131
#, php-format
msgid "%1$sWP Rocket: %2$sWe have detected that Autoptimize's Aggregate Inline CSS feature is enabled. WP Rocket's Load CSS Asynchronously will not work correctly. We suggest disabling %1$sAggregate Inline CSS%2$s to take full advantage of Load CSS Asynchronously Execution."
msgstr "%1$sWP Rocket: %2$sOtkrili smo da je omogućena značajka Aggregate Inline CSS Autoptimize plugina. WP Rocketovo Asinkrono učitavanje CSS-a neće raditi ispravno. Predlažemo onemogućavanje %1$sAggregate Inline CSS%2$s kako biste u potpunosti iskoristili prednosti Asinkronog učitavanja CSS-a."

#: inc/ThirdParty/Plugins/Optimization/Ezoic.php:45
#, php-format
msgid "This plugin blocks WP Rocket's caching and optimizations. Deactivate it and use %1$sEzoic's nameserver integration%2$s instead."
msgstr "Ovaj dodatak blokira caching i optimizacije WP Rocket-a. Deaktivirajte ga i koristite %1$sEzoic's nameserver integration%2$s umjesto toga."

#: inc/ThirdParty/Plugins/Optimization/Hummingbird.php:78
#, php-format
msgctxt "Hummingbird notice"
msgid "Please deactivate the following %s option which conflicts with WP Rocket features:"
msgid_plural "Please deactivate the following %s options which conflict with WP Rocket features:"
msgstr[0] "Isključite sljedeću %s opciju koja je u sukobu s WP Rocket značajkama:"
msgstr[1] "Isključite sljedeće %s opcije koje su u sukobu s WP Rocket značajkama:"
msgstr[2] "Isključite sljedeće %s opcije koje su u sukobu s WP Rocket značajkama:"

#: inc/ThirdParty/Plugins/Optimization/Hummingbird.php:151
#, php-format
msgctxt "Hummingbird notice"
msgid "%1$s %2$sdisable emoji%3$s conflicts with WP Rockets %2$sdisable emoji%3$s"
msgstr "%1$s %2$sdisable emoji%3$s conflicts with WP Rockets %2$sdisable emoji%3$s"

#: inc/ThirdParty/Plugins/Optimization/Hummingbird.php:187
#, php-format
msgctxt "Hummingbird notice"
msgid "%1$s %2$sGZIP compression%3$s conflicts with WP Rocket %2$sGZIP compression%3$s"
msgstr "%1$s %2$sGZIP kompresija%3$s je u sukobu s WP Rocket %2$sGZIP kompresijom%3$s"

#: inc/ThirdParty/Plugins/Optimization/Hummingbird.php:223
#, php-format
msgctxt "Hummingbird notice"
msgid "%1$s %2$sbrowser caching%3$s conflicts with WP Rocket %2$sbrowser caching%3$s"
msgstr "%1$s %2$scacheiranje preglednika %3$s je u sukobu s WP Rocket %2$scacheiranjem preglednika%3$s"

#: inc/ThirdParty/Plugins/Optimization/Hummingbird.php:255
#, php-format
msgctxt "Hummingbird notice"
msgid "%1$s %2$spage caching%3$s conflicts with WP Rocket %2$spage caching%3$s"
msgstr "%1$s %2$scaching za stranice%3$s je u sukobu s WP Rocket %2$scachingom stranica%3$s"

#: inc/ThirdParty/Plugins/Optimization/Hummingbird.php:289
#, php-format
msgctxt "Hummingbird notice"
msgid "%1$s %2$sasset optimization%3$s conflicts with WP Rocket %2$sfile optimization%3$s"
msgstr "%1$s %2$soptimizacija resursa%3$s je u sukobu s WP Rocket %2$soptimizacijom datoteka%3$s"

#: inc/ThirdParty/Plugins/Optimization/WPMeteor.php:42
#, php-format
msgid "Delay JS is currently activated in %1$s. If you want to use WP Rocket’s delay JS, disable %1$s"
msgstr "Odgoda JS-a je trenutno aktivirana u %1$s. Ako želite koristiti WP Rocket za odgodu JS-a , onemogućite %1$s"

#: inc/deprecated/3.11.php:81
msgid "Yoast SEO XML sitemap"
msgstr "Yoast SEO XML mapa sajta"

#: inc/ThirdParty/Plugins/Smush.php:108
#: inc/ThirdParty/Plugins/Smush.php:126
msgid "Smush"
msgstr "Smush"

#: inc/ThirdParty/Themes/Avada.php:116
msgid "Avada"
msgstr "Avada"

#: inc/admin/admin.php:18
#: inc/common/admin-bar.php:255
#: inc/deprecated/deprecated.php:1787
msgid "Support"
msgstr "Podrška"

#: inc/admin/admin.php:20
msgid "Docs"
msgstr "Dokumenti"

#: inc/admin/admin.php:22
#: inc/common/admin-bar.php:243
msgid "FAQ"
msgstr "Česta pitanja"

#: inc/admin/admin.php:24
#: inc/common/admin-bar.php:63
msgid "Settings"
msgstr "Postavke"

#: inc/Engine/Plugin/UpdaterSubscriber.php:472
#: inc/Engine/Plugin/UpdaterSubscriber.php:486
#: inc/classes/class-wp-rocket-requirements-check.php:203
#: inc/classes/class-wp-rocket-requirements-check.php:214
#: inc/deprecated/3.11.php:236
#: inc/deprecated/3.11.php:251
#, php-format
msgid "%s Update Rollback"
msgstr "%s Vraćanje ažuriranja"

#: inc/Engine/Plugin/UpdaterSubscriber.php:509
#: inc/deprecated/3.11.php:279
#, php-format
msgid "%1$sReturn to WP Rocket%2$s or %3$sgo to Plugins page%2$s"
msgstr "%1$sVratite se na WP Rocket%2$s ili %3$s idite na stranicu Dodaci%2$s"

#: inc/admin/admin.php:458
msgid "Settings import failed: you do not have the permissions to do this."
msgstr "Uvoz postavki nije uspio: nemate dopuštenje za to."

#: inc/admin/admin.php:462
msgid "Settings import failed: no file uploaded."
msgstr "Uvoz postavki nije uspio: datoteka nije prenesena."

#: inc/admin/admin.php:466
msgid "Settings import failed: incorrect filename."
msgstr "Settings import nije uspio: pogrešan naziv fajla."

#: inc/admin/admin.php:477
msgid "Settings import failed: incorrect filetype."
msgstr "Settings import nije uspio: pogrešna vrsta fajla."

#: inc/admin/admin.php:487
msgid "Settings import failed: "
msgstr "Uvoz postavki nije uspio:"

#: inc/admin/admin.php:503
msgid "Settings import failed: unexpected file content."
msgstr "Uvoz postavki nije uspio: neočekivani sadržaj datoteke."

#: inc/admin/admin.php:533
msgid "Settings imported and saved."
msgstr "Postavke uvezene i spremljene."

#: inc/admin/options.php:127
msgid "Defer JavaScript Files"
msgstr "Odgodi JavaScript datoteke"

#: inc/admin/options.php:128
msgid "Excluded Delay JavaScript Files"
msgstr "Isključene JavaScript datoteke iz odgode"

#: inc/admin/options.php:150
#, php-format
msgid "%1$s: <em>%2$s</em>"
msgstr "%1$s: <em>%2$s</em>"

#: inc/admin/options.php:160
msgid "The following pattern is invalid and has been removed:"
msgid_plural "The following patterns are invalid and have been removed:"
msgstr[0] "Sljedeći je obrazac nevažeći i uklonjen:"
msgstr[1] "Sljedeći su obrasci nevažeći i uklonjeni:"
msgstr[2] "Sljedeći su obrasci nevažeći i uklonjeni:"

#: inc/admin/options.php:176
msgid "More info"
msgstr "Više informacija"

#: inc/admin/ui/meta-boxes.php:37
#: inc/admin/ui/notices.php:748
#: inc/common/admin-bar.php:84
#: inc/common/admin-bar.php:136
#: views/settings/page-sections/dashboard.php:150
msgid "Clear cache"
msgstr "Očisti cache"

#: inc/admin/ui/meta-boxes.php:67
msgid "WP Rocket Options"
msgstr "Opcije WP Rocketa"

#: inc/admin/ui/meta-boxes.php:96
msgid "Never cache this page"
msgstr "Nikad ne spremaj ovu stranicu."

#: inc/admin/ui/meta-boxes.php:100
msgid "Activate these options on this post:"
msgstr "Aktivirajte ove opcije na ovom članku:"

#: inc/admin/ui/meta-boxes.php:103
msgid "LazyLoad for images"
msgstr "LazyLoad za slike"

#: inc/admin/ui/meta-boxes.php:104
msgid "LazyLoad for iframes/videos"
msgstr "LazyLoad za iframes/videa"

#: inc/admin/ui/meta-boxes.php:105
msgid "Minify/combine CSS"
msgstr "Minificirati/kombinirati CSS"

#: inc/Engine/Admin/Settings/Page.php:760
#: inc/admin/ui/meta-boxes.php:106
msgid "Remove Unused CSS"
msgstr "Uklonite nekorišteni CSS"

#: inc/admin/ui/meta-boxes.php:107
msgid "Minify/combine JS"
msgstr "Minificirati/kombinirati JS"

#: inc/admin/ui/meta-boxes.php:110
msgid "Defer JS"
msgstr "Defer JS"

#: inc/admin/ui/meta-boxes.php:117
#, php-format
msgid "Activate first the %s option."
msgstr "Prvo aktivirajte %s opciju."

#: inc/admin/ui/meta-boxes.php:133
#, php-format
msgid "%1$sNote:%2$s None of these options will be applied if this post has been excluded from cache in the global cache settings."
msgstr "%1$sNapomena:%2$s Nijedna od ovih opcija neće se primijeniti ako je ovaj post isključen iz cachea u globalnim postavkama cachea."

#: inc/admin/ui/notices.php:31
#: inc/admin/ui/notices.php:44
#, php-format
msgid ""
"<strong>%1$s</strong> has not been deactivated due to missing writing permissions.<br>\n"
"Make <strong>%2$s</strong> writeable and retry deactivation, or force deactivation now:"
msgstr ""
"<strong>%1$s</strong> nije deaktiviran zbog nedostatka dozvola za zapisivanje.<br>\n"
"Omogućite zapisivanje<strong>%2$s</strong>  i ponovno pokušajte deaktivaciju ili odmah prisilno deaktivirajte:"

#: inc/admin/ui/notices.php:97
#, php-format
msgid "<strong>%s</strong>: One or more plugins have been enabled or disabled, clear the cache if they affect the front end of your site."
msgstr "<strong>%s</strong>: Jedan ili više dodataka su omogućeni ili onemogućeni. Ako želite utjecati na pravilan prikaz web-lokacije, očistite cache."

#: inc/admin/ui/notices.php:218
#, php-format
msgid "<strong>%s</strong>: The following plugins are not compatible with this plugin and may cause unexpected results:"
msgstr "<strong>%s</strong> : Sljedeći pluginovi nisu kompatibilni s ovim pluginom i to može izazvati neočekivane rezultate:"

#: inc/admin/ui/notices.php:224
msgid "Deactivate"
msgstr "Deaktiviraj"

#: inc/admin/ui/notices.php:266
msgid "WP Rocket Footer JS is not an official add-on. It prevents some options in WP Rocket from working correctly. Please deactivate it if you have problems."
msgstr "WP Rocket Footer JS nije službeni dodatak. On spriječava da neke opcije WP Rocketa rade ispravno. Isključite ga ako primjetite problem."

#: inc/admin/ui/notices.php:306
#, php-format
msgid "Endurance Cache is currently enabled, which will conflict with WP Rocket Cache. Please set the Endurance Cache cache level to Off (Level 0) on the %1$sSettings > General%2$s page to prevent any issues."
msgstr "Endurance Cache je trenutno uključen, što će uzrokovati sukob s WP Rocket Cacheom. Molimo, postavite Endurance Cache razinu cachea na Off (Level 0) na stranici %1$sPostavke > Općenito%2$s kako biste spriječili probleme."

#: inc/admin/ui/notices.php:327
#, php-format
msgid "%1$s: A custom permalink structure is required for the plugin to work properly. %2$sGo to permalinks settings%3$s"
msgstr "%1$s: Za ispravan rad dodatka potrebna je prilagođena struktura stalnih veza. %2$sIdite na postavke stalnih veza%3$s"

#: inc/admin/ui/notices.php:374
#, php-format
msgid "%s could not modify the .htaccess file due to missing writing permissions."
msgstr "%s nije mogao izmijeniti .htaccess datoteku zbog nedostajućih dozvola pisanja."

#: inc/admin/ui/notices.php:380
#: inc/admin/ui/notices.php:843
#, php-format
msgid "Troubleshoot: %1$sHow to make system files writeable%2$s"
msgstr "Rješavanje problema:: %1$sKako omogućiti zapisivanje u sistemske datoteke%2$s"

#: inc/admin/ui/notices.php:382
#: inc/admin/ui/notices.php:845
msgid "https://docs.wp-rocket.me/article/626-how-to-make-system-files-htaccess-wp-config-writeable/?utm_source=wp_plugin&utm_medium=wp_rocket"
msgstr "https://docs.wp-rocket.me/article/626-how-to-make-system-files-htaccess-wp-config-writeable/?utm_source=wp_plugin&utm_medium=wp_rocket"

#: inc/admin/ui/notices.php:388
msgid "Don’t worry, WP Rocket’s page caching and settings will still function correctly."
msgstr "Ne brinite, caching i postavke WP Rocketa će i dalje raditi ispravno."

#: inc/admin/ui/notices.php:388
msgid "For optimal performance, adding the following lines into your .htaccess is recommended (not required):"
msgstr "Za optimalne performanse, preporučuje se dodavanje sljedećih redaka u vašu .htaccess datoteku (nije obvezno):"

#: inc/admin/ui/notices.php:535
#, php-format
msgid "%1$s is good to go! %2$sTest your load time%4$s, or visit your %3$ssettings%4$s."
msgstr "%1$s je spreman! %2$sTestirajte vrijeme učitavanja%4$s, ili posjetite vaše %3$spostavke%4$s."

#: inc/admin/ui/notices.php:576
msgid "Would you allow WP Rocket to collect non-sensitive diagnostic data from this website?"
msgstr "Dopuštate li WP Rocketu da prikupi osnovne dijagnostičke podatke s ove web stranice?"

#: inc/admin/ui/notices.php:577
msgid "This would help us to improve WP Rocket for you in the future."
msgstr "To bi nam pomoglo da u budućnosti poboljšamo WP Rocket."

#: inc/admin/ui/notices.php:583
msgid "What info will we collect?"
msgstr "Koje ćemo podatke prikupiti?"

#: inc/admin/ui/notices.php:588
msgid "Below is a detailed view of all data WP Rocket will collect if granted permission. WP Rocket will never transmit any domain names or email addresses (except for license validation), IP addresses, or third-party API keys."
msgstr "U nastavku se nalazi detaljan pregled svih podataka koje će WP Rocket prikupiti ako dobije dozvolu. WP Rocket nikada neće prenijeti imena domena ili adrese e-pošte (osim za provjeru licence), IP adrese ili API ključeve treće strane."

#: inc/admin/ui/notices.php:597
msgid "Yes, allow"
msgstr "Da, dozvoli"

#: inc/admin/ui/notices.php:600
msgid "No, thanks"
msgstr "Ne, hvala"

#: inc/admin/ui/notices.php:639
msgid "Thank you!"
msgstr "Hvala vam!"

#: inc/admin/ui/notices.php:644
msgid "WP Rocket now collects these metrics from your website:"
msgstr "WP Rocket sada prikuplja ove podatke s vaše web-lokacije:"

#: inc/admin/ui/notices.php:682
#, php-format
msgid "%s: Cache cleared."
msgstr "%s: Cache očišćen."

#: inc/admin/ui/notices.php:689
#, php-format
msgid "%s: Post cache cleared."
msgstr "%s: Cache objava očišćen."

#: inc/admin/ui/notices.php:696
#, php-format
msgid "%s: Term cache cleared."
msgstr "%s: Cache pojmova očišćen."

#: inc/admin/ui/notices.php:703
#, php-format
msgid "%s: User cache cleared."
msgstr "%s: Cache korisnika očišćen."

#: inc/admin/ui/notices.php:751
msgid "Stop Preload"
msgstr "Zaustavi predučitavanje"

#: inc/admin/ui/notices.php:781
msgid "Force deactivation "
msgstr "Prisili deaktiviranje"

#: inc/admin/ui/notices.php:800
msgid "The following code should have been written to this file:"
msgstr "Sljedeći kôd bi trebao biti zapisan u ovu datoteku:"

#: inc/admin/ui/notices.php:831
#, php-format
msgid "%s cannot configure itself due to missing writing permissions."
msgstr "%s se ne može konfigurirati zbog nedostatka dozvola za pisanje."

#: inc/admin/ui/notices.php:837
#, php-format
msgid "Affected file/folder: %s"
msgstr "Utjecana datoteka/mapa: %s"

#: inc/classes/admin/class-logs.php:124
msgid "The debug file could not be deleted."
msgstr "Datoteku za uklanjanje pogrešaka (debug) nije moguće izbrisati."

#: inc/classes/class-wp-rocket-requirements-check.php:147
#, php-format
msgid "To function properly, %1$s %2$s requires at least:"
msgstr "Za ispravno funkcioniranje %1$s %2$s potrebno je koristiti barem:"

#: inc/classes/class-wp-rocket-requirements-check.php:151
#, php-format
msgid "PHP %1$s. To use this WP Rocket version, please ask your web host how to upgrade your server to PHP %1$s or higher."
msgstr "PHP %1$s. Da biste koristili ovu WP Rocket verziju, molimo pitajte vašeg web host kako nadograditi vaš poslužitelj na PHP %1$s ili više. "

#: inc/classes/class-wp-rocket-requirements-check.php:156
#, php-format
msgid "WordPress %1$s. To use this WP Rocket version, please upgrade WordPress to version %1$s or higher."
msgstr "WordPress %1$s. Da biste koristili ovu verziju WP Rocketa, nadogradite WordPress na verziju  %1$s ili noviju."

#: inc/classes/class-wp-rocket-requirements-check.php:159
msgid "If you are not able to upgrade, you can rollback to the previous version by using the button below."
msgstr "Ako ne možete nadograditi, možete se vratiti na prethodnu verziju pomoću gumba u nastavku."

#: inc/classes/class-wp-rocket-requirements-check.php:161
#: inc/deprecated/deprecated.php:1947
#, php-format
msgid "Re-install version %s"
msgstr "Ponovno instalirajte verziju %s"

#: inc/classes/dependencies/wp-media/background-processing/wp-background-process.php:447
#, php-format
msgid "Every %d Minutes"
msgstr "Svake %d minute"

#: inc/classes/logger/class-logger.php:260
#: inc/classes/logger/class-logger.php:292
msgid "The log file does not exist."
msgstr "Log datoteka ne postoji."

#: inc/classes/logger/class-logger.php:266
#: inc/classes/logger/class-logger.php:298
msgid "The log file could not be read."
msgstr "Log datoteku nije moguće pročitati."

#: inc/classes/logger/class-logger.php:285
msgid "The logs are not saved into a file."
msgstr "Log zapisi nisu spremljeni u datoteku."

#: inc/Addon/WebP/AdminSubscriber.php:93
#, php-format
msgid "You are using %1$s to serve WebP images so you do not need to enable this option. %2$sMore info%3$s %4$s If you prefer to have WP Rocket serve WebP for you instead, please disable WebP display in %1$s."
msgid_plural "You are using %1$s to serve WebP images so you do not need to enable this option. %2$sMore info%3$s %4$s If you prefer to have WP Rocket serve WebP for you instead, please disable WebP display in %1$s."
msgstr[0] "Koristite %1$s tza posluživanje WebP slika pa ne morate omogućiti ovu opciju. %2$sViše informacija%3$s %4$s Ako umjesto toga želite da vam WP Rocket poslužuje WebP slike, onemogućite prikaz WebP-a u  %1$s."
msgstr[1] "Koristite%1$s za posluživanje WebP slika pa ne morate omogućiti ovu opciju. %2$sViše informacija%3$s %4$s Ako umjesto toga želite da vam WP Rocket poslužuje WebP slike, onemogućite prikaz WebP-a u  %1$s."
msgstr[2] "Koristite %1$s za posluživanje WebP slika pa ne morate omogućiti ovu opciju. %2$sViše informacija%3$s %4$s Ako umjesto toga želite da vam WP Rocket poslužuje WebP slike, onemogućite prikaz WebP-a u %1$s."

#: inc/Addon/WebP/AdminSubscriber.php:105
msgid "WebP cache is disabled by filter."
msgstr "WebP cache je onemogućen pomoću filtera."

#: inc/Addon/WebP/AdminSubscriber.php:115
#: inc/Addon/WebP/AdminSubscriber.php:141
#, php-format
msgid "You are using %1$s to convert images to WebP. If you want WP Rocket to serve them for you, activate this option. %2$sMore info%3$s"
msgid_plural "You are using %1$s to convert images to WebP. If you want WP Rocket to serve them for you, activate this option. %2$sMore info%3$s"
msgstr[0] "Koristite %1$s za konverziju slika u WebP format. Ukoliko želite da ih poslužuje WP Rocket, aktivirajte ovu opciju.  %2$sViše informacija%3$s"
msgstr[1] "Koristite %1$s za konverziju slika u WebP format. Ukoliko želite da ih poslužuje WP Rocket, aktivirajte ovu opciju.  %2$sViše informacija%3$s"
msgstr[2] "Koristite %1$s za konverziju slika u WebP format. Ukoliko želite da ih poslužuje WP Rocket, aktivirajte ovu opciju. %2$sViše informacija%3$s"

#: inc/Addon/WebP/AdminSubscriber.php:127
#: inc/Addon/WebP/AdminSubscriber.php:153
#, php-format
msgid "You are using %1$s to convert images to WebP. WP Rocket will create separate cache files to serve your WebP images. %2$sMore info%3$s"
msgid_plural "You are using %1$s to convert images to WebP. WP Rocket will create separate cache files to serve your WebP images. %2$sMore info%3$s"
msgstr[0] "Koristite %1$s za konverziju slika u WebP format. WP Rocket će kreirati zasebne cache datoteke za posluživanje vaših WebP slika. %2$sViše informacija%3$s"
msgstr[1] "Koristite %1$s za konverziju slika u WebP format. WP Rocket će kreirati zasebne cache datoteke za posluživanje vaših WebP slika. %2$sViše informacija%3$s"
msgstr[2] "Koristite %1$s za konverziju slika u WebP format. WP Rocket će kreirati zasebne cache datoteke za posluživanje vaših WebP slika.%2$sViše informacija%3$s"

#: inc/Addon/WebP/AdminSubscriber.php:173
#, php-format
msgid "%5$sWe have not detected any compatible WebP plugin!%6$s%4$s If you don’t already have WebP images on your site consider using %3$sImagify%2$s or another supported plugin. %1$sMore info%2$s %4$s If you are not using WebP do not enable this option."
msgstr "%5$sNismo otkrili nijedan kompatibilan dodatak za WebP!%6$s%4$s Ako još nemate WebP slike na svojoj web lokaciji, razmislite o upotrebi %3$sImagify%2$s ili neki drugi podržani dodatak. %1$sViše informacija%2$s %4$sAko ne koristite WebP, nemojte omogućiti ovu opciju."

#: inc/Addon/WebP/AdminSubscriber.php:185
msgid "WP Rocket will create separate cache files to serve your WebP images."
msgstr "WP Rocket će stvoriti zasebne cache datoteke za posluživanje WebP slika."

#: inc/classes/subscriber/Tools/class-detect-missing-tags-subscriber.php:151
#, php-format
msgid "Failed to detect the following requirement in your theme: closing %1$s."
msgid_plural "Failed to detect the following requirements in your theme: closing %1$s."
msgstr[0] "Otkrivanje sljedećeg zahtjeva u vašoj temi nije uspjelo: zatvaranje%1$s."
msgstr[1] "Otkrivanje sljedećih zahtjeva u vašoj temi nije uspjelo: zatvaranje %1$s."
msgstr[2] "Otkrivanje sljedećih zahtjeva u vašoj temi nije uspjelo: zatvaranje %1$s."

#: inc/classes/subscriber/Tools/class-detect-missing-tags-subscriber.php:159
msgid "https://docs.wp-rocket.me/article/99-pages-not-cached-or-minify-cssjs-not-working/?utm_source=wp_plugin&utm_medium=wp_rocket#theme"
msgstr "https://docs.wp-rocket.me/article/99-pages-not-cached-or-minify-cssjs-not-working/?utm_source=wp_plugin&utm_medium=wp_rocket#theme"

#: inc/common/admin-bar.php:124
#: inc/functions/i18n.php:41
#: inc/functions/i18n.php:51
msgid "All languages"
msgstr "Svi jezici"

#: inc/common/admin-bar.php:160
msgid "Clear this post"
msgstr "Poništite ovaj članak"

#: inc/common/admin-bar.php:174
msgid "Purge this URL"
msgstr "Očistite ovaj URL"

#: inc/common/admin-bar.php:194
msgid "Purge Sucuri cache"
msgstr "Očisti Sucuri cache"

#: inc/common/admin-bar.php:218
#: views/settings/fields/rocket-cdn.php:55
msgid "Purge RocketCDN cache"
msgstr "Očisti RocketCDN cache"

#: inc/common/admin-bar.php:231
#: views/settings/partials/documentation.php:14
msgid "Documentation"
msgstr "Dokumentacija"

#: inc/deprecated/3.10.php:163
msgid "OPcache purge failed."
msgstr "Čišćenje OPcachea nije uspjelo."

#: inc/deprecated/3.10.php:168
msgid "OPcache successfully purged"
msgstr "Čišćenje OPcachea uspješno"

#: inc/deprecated/3.2.php:52
#: views/settings/page-sections/imagify.php:36
msgid "Activate Imagify"
msgstr "Aktivirajte Imagify"

#: inc/deprecated/3.2.php:52
msgid "Install Imagify for Free"
msgstr "Instalacija besplatne verzije Imagify-a"

#: inc/deprecated/3.2.php:67
msgid "Speed up your website and boost your SEO by reducing image file sizes without losing quality with Imagify."
msgstr "Ubrzajte svoje web stranice i podignite svoj SEO smanjivanjem veličine slika bez gubitka kvalitete koristeći Imagify."

#: inc/deprecated/3.2.php:72
msgid "More details"
msgstr "Više detalja"

#: inc/deprecated/3.2.php:228
#, php-format
msgid "Sitemap preload: %d pages have been cached."
msgstr "Predučitavanje mape web-lokacije: %d stranica je cachirano."

#: inc/deprecated/3.2.php:261
#, php-format
msgid "Sitemap preload: %d uncached pages have now been preloaded. (refresh to see progress)"
msgstr "Predučitavanje mape we-lokacije: %d necachiranih stranica je uspješno predučitano (osvježite kako biste vidjeli napredak)"

#: inc/deprecated/3.4.php:18
msgid "Choose a domain from the list"
msgstr "Odaberite domenu s liste"

#: inc/deprecated/3.4.php:31
msgid "No domain available in your Cloudflare account"
msgstr "Nema dostupnih domena u vašem CloudFlare računu"

#: inc/deprecated/3.5.php:71
#: inc/deprecated/3.5.php:195
msgid "Curl is disabled on your server. Please ask your host to enable it. This is required for the Cloudflare Add-on to work correctly."
msgstr "Curl je onemogućen na vašem serveru a potreban je za ispravni rad Cloudflare dodatka. Molimo, kontaktirajte svoju hosting kompaniju."

#: inc/deprecated/3.5.php:79
#, php-format
msgid "Cloudflare email, API key and Zone ID are not set. Read the %1$sdocumentation%2$s for further guidance."
msgstr "Cloudflare email, API ključ i Zone ID nisu postavljeni. Pročitajte %1$sdokumentaciju%2$s za više informacija."

#: inc/deprecated/3.5.php:206
#, php-format
msgid "Cloudflare email and API key are not set. Read the %1$sdocumentation%2$s for further guidance."
msgstr "Cloudflare email i API ključ nisu postavljeni. Pročitajte %1$sdokumentaciju%2$s za više informacija."

#: inc/deprecated/3.5.php:271
msgid "Connection to Cloudflare failed"
msgstr "Povezivanje s Cloudflare-om neuspjelo"

#: inc/deprecated/DeprecatedClassTrait.php:54
#, php-format
msgid "The called class %1$s is <strong>deprecated</strong> since version %2$s! Use %3$s instead."
msgstr "Pozvana klasa%1$s je <strong>ukinuta</strong> od verzije %2$s! Koristite %3$s umjesto toga."

#: inc/deprecated/DeprecatedClassTrait.php:65
#, php-format
msgid "The called class %1$s is <strong>deprecated</strong> since version %2$s!"
msgstr "Pozvana klasa %1$s je<strong>ukinuta</strong> od verzije %2$s!"

#: inc/deprecated/deprecated.php:240
msgid "<strong>JS</strong> files with Deferred Loading JavaScript"
msgstr "<strong>JS</strong> datoteke s Odgođenim Učitavanjem JavaScript-e"

#: inc/deprecated/deprecated.php:293
msgid "Add URL"
msgstr "Dodaj URL"

#: inc/deprecated/deprecated.php:1277
#: views/settings/fields/import-form.php:22
msgid "Before you can upload your import file, you will need to fix the following error:"
msgstr "Prije nego što možete uploadati svoju datoteku za uvoz, trebat ćete popraviti sljedeću pogrešku:"

#: inc/deprecated/deprecated.php:1288
#: views/settings/fields/import-form.php:35
#, php-format
msgid "Choose a file from your computer (maximum size: %s)"
msgstr "Odaberite datoteku s vašeg računala (maksimalna veličina:%s)"

#: inc/deprecated/deprecated.php:1385
msgid "Your Cloudflare credentials are valid."
msgstr "Vaši Cloudflare isprave su važeće."

#: inc/deprecated/deprecated.php:1389
msgid "Your Cloudflare credentials are invalid!"
msgstr "Vaši Cloudflare isprave su nevažeće!"

#: inc/deprecated/deprecated.php:1453
msgid "Save and optimize"
msgstr "Spremi i optimiziraj"

#: inc/deprecated/deprecated.php:1453
msgid "Optimize"
msgstr "Optimiziraj"

#: inc/deprecated/deprecated.php:1464
#: inc/deprecated/deprecated.php:1666
msgctxt "screen-reader-text"
msgid "Note:"
msgstr "Napomena:"

#: inc/deprecated/deprecated.php:1470
msgctxt "screen-reader-text"
msgid "Performance tip:"
msgstr "Savjet o performansama:"

#: inc/deprecated/deprecated.php:1476
msgctxt "screen-reader-text"
msgid "Third-party feature detected:"
msgstr "Detektirana opcija treće strane:"

#: inc/deprecated/deprecated.php:1488
#: inc/deprecated/deprecated.php:1672
msgctxt "screen-reader-text"
msgid "Warning:"
msgstr "Upozorenje:"

#: inc/deprecated/deprecated.php:1501
msgctxt "button text"
msgid "Download settings"
msgstr "Postavke preuzimanja"

#: inc/deprecated/deprecated.php:1531
msgid "Replace site's hostname with:"
msgstr "Zamijenite hostname naziv stranice s:"

#: inc/deprecated/deprecated.php:1550
#: inc/deprecated/deprecated.php:1587
#: inc/deprecated/deprecated.php:1615
#: views/settings/fields/cnames.php:41
#: views/settings/fields/cnames.php:75
#: views/settings/fields/cnames.php:105
msgid "reserved for"
msgstr "rezervirano za"

#: inc/deprecated/deprecated.php:1552
#: inc/deprecated/deprecated.php:1589
#: inc/deprecated/deprecated.php:1617
#: views/settings/fields/cnames.php:44
#: views/settings/fields/cnames.php:78
#: views/settings/fields/cnames.php:108
msgid "All files"
msgstr "Sve datoteke"

#: inc/deprecated/deprecated.php:1564
#: inc/deprecated/deprecated.php:1594
#: inc/deprecated/deprecated.php:1622
#: views/settings/fields/cnames.php:56
#: views/settings/fields/cnames.php:90
#: views/settings/fields/cnames.php:120
msgid "Images"
msgstr "Slike"

#: inc/deprecated/deprecated.php:1635
#: views/settings/fields/cnames.php:132
msgid "Add CNAME"
msgstr "Dodaj CNAME"

#: inc/deprecated/deprecated.php:1706
msgid "Watch the video"
msgstr "Pogledajte video"

#: inc/deprecated/deprecated.php:1771
msgid "Basic"
msgstr "Osnovno"

#: inc/deprecated/deprecated.php:1772
msgid "Static Files"
msgstr "Statične Datoteke"

#: inc/deprecated/deprecated.php:1774
msgid "Advanced"
msgstr "Napredno"

#: inc/deprecated/deprecated.php:1944
#, php-format
msgid "%1$s %2$s requires at least PHP %3$s to function properly. To use this version, please ask your web host how to upgrade your server to PHP %3$s or higher. If you are not able to upgrade, you can rollback to the previous version by using the button below."
msgstr "%1$s %2$s zahtijeva minimalnu PHP verziju %3$s za ispravno funkcioniranje. Da biste koristili ovu verziju, pitajte svog web hosta kako nadograditi poslužitelj na verziju PHP-a %3$s ili višu. Ako ne možete nadograditi, možete se vratiti na prethodnu verziju pomoću gumba u nastavku."

#: inc/functions/admin.php:21
msgid "There seems to be an issue validating your license. Please see the error message below."
msgid_plural "There seems to be an issue validating your license. You can see the error messages below."
msgstr[0] "Čini se da postoji problem s provjerom vaše licence. Niže možete vidjeti poruku o pogrešci."
msgstr[1] "Čini se da postoji problem s provjerom vaše licence. Niže možete vidjeti poruke o pogreškama."
msgstr[2] "Čini se da postoji problem s provjerom vaše licence. Niže možete vidjeti poruke o pogreškama."

#: inc/functions/admin.php:361
msgid "Server type:"
msgstr "Vrsta servera:"

#: inc/functions/admin.php:370
msgid "PHP version number:"
msgstr "Verzija PHP-a:"

#: inc/functions/admin.php:379
msgid "WordPress version number:"
msgstr "Verzija WordPressa:"

#: inc/functions/admin.php:388
msgid "WordPress multisite:"
msgstr "WordPress multisite:"

#: inc/functions/admin.php:397
msgid "Current theme:"
msgstr "Aktivna tema:"

#: inc/functions/admin.php:406
msgid "Current site language:"
msgstr "Trenutni jezik web-lokacije:"

#: inc/functions/admin.php:415
msgid "Active plugins:"
msgstr "Aktivni dodaci:"

#: inc/functions/admin.php:418
msgid "Plugin names of all active plugins"
msgstr "Imena svih aktivnih dodataka"

#: inc/functions/admin.php:424
msgid "Anonymized WP Rocket settings:"
msgstr "Anonimizirane postavke WP Rocketa:"

#: inc/functions/admin.php:427
msgid "Which WP Rocket settings are active"
msgstr "Koje postavke WP Rocketa su aktivne"

#: inc/functions/options.php:429
msgid "The provided license data are not valid."
msgstr "Navedeni podaci o licenci nisu valjani."

#: inc/functions/options.php:432
#, php-format
msgid "To resolve, please %1$scontact support%2$s."
msgstr "Za rješavanje, molimo %1$skontaktirajte podršku%2$s."

#: inc/functions/options.php:491
#: inc/functions/options.php:530
msgid "License validation failed. Our server could not resolve the request from your website."
msgstr "Provjera valjanosti licence nije uspjela. Naš poslužitelj nije mogao riješiti zahtjev s vaše web-lokacije."

#: inc/functions/options.php:491
#, php-format
msgid "Try clicking %1$sValidate License%2$s below. If the error persists, follow %3$sthis guide%4$s."
msgstr "Pokušajte kliknuti %1$sPotvrdi licencu%2$s niže. Ako se pogreška i dalje pojavljuje, slijedite %3$sovaj vodič%4$s."

#: inc/functions/options.php:507
msgid "License validation failed. You may be using a nulled version of the plugin. Please do the following:"
msgstr "Provjera valjanosti licence nije uspjela. Možda koristite ilegalnu verziju dodatka. Učinite sljedeće:"

#: inc/functions/options.php:507
#: inc/functions/options.php:549
#, php-format
msgid "Login to your WP Rocket %1$saccount%2$s"
msgstr "Prijavite se u svoj WP Rocket %1$sračun%2$s"

#: inc/functions/options.php:507
#: inc/functions/options.php:549
msgid "Download the zip file"
msgstr "Preuzmite zip datoteku"

#: inc/functions/options.php:507
#: inc/functions/options.php:549
msgid "Reinstall"
msgstr "Reinstaliraj"

#: inc/functions/options.php:507
#, php-format
msgid "If you do not have a WP Rocket account, please %1$spurchase a license%2$s."
msgstr "Ako nemate WP Rocket račun, molimo %1$skupite licencu%2$s."

#: inc/functions/options.php:515
msgid "License validation failed. This user account does not exist in our database."
msgstr "Provjera valjanosti licence nije uspjela. Ovaj korisnički račun ne postoji u našoj bazi podataka."

#: inc/functions/options.php:515
msgid "To resolve, please contact support."
msgstr "Za rješavanje, kontaktirajte podršku."

#: inc/functions/options.php:523
msgid "License validation failed. This user account is blocked."
msgstr "Provjera valjanosti licence nije uspjela. Ovaj korisnički račun je blokiran."

#: inc/functions/options.php:523
#, php-format
msgid "Please see %1$sthis guide%2$s for more info."
msgstr "Molimo pogledajte %1$sovaj vodič%2$s za više informacija."

#: inc/functions/options.php:530
#, php-format
msgid "Try clicking %1$sSave Changes%2$s below. If the error persists, follow %3$sthis guide%4$s."
msgstr "Pokušajte kliknuti %1$sSpremi promjene%2$s niže. Ako se pogreška i dalje pojavljuje, slijedite %3$sovaj vodič%4$s."

#: inc/functions/options.php:543
msgid "Your license is not valid."
msgstr "Vaša licenca nije valjana."

#: inc/functions/options.php:543
#, php-format
msgid "Make sure you have an active %1$sWP Rocket license%2$s."
msgstr "Provjerite imate li aktivnu %1$sWP Rocket licencu%2$s."

#: inc/functions/options.php:545
msgid "You have added as many sites as your current license allows."
msgstr "Dodali ste onoliko web-lokacija koliko to dopušta vaša trenutna licenca."

#: inc/functions/options.php:545
#, php-format
msgid "Upgrade your %1$saccount%2$s or %3$stransfer your license%2$s to this domain."
msgstr "Nadogradite svoj %1$sračun%2$s ili %3$sprenesite licencu%2$s na ovu web lokaciju."

#: inc/functions/options.php:547
msgid "This website is not allowed."
msgstr "Ova web stranica nije dopuštena."

#: inc/functions/options.php:547
#, php-format
msgid "Please %1$scontact support%2$s."
msgstr "Molimo %1$skontaktirajte korisničku podršku%2$s."

#: inc/functions/options.php:549
msgid "This license key is not recognized."
msgstr "Ovaj licencni ključ nije prepoznat."

#: inc/functions/options.php:549
#, php-format
msgid "If the issue persists, please %1$scontact support%2$s."
msgstr "Ako se problem nastavi, molimo %1$skontaktirajte korisničku podršku%2$s."

#: inc/functions/options.php:555
#, php-format
msgid "License validation failed: %s"
msgstr "Provjera valjanosti licence nije uspjela: %s"

#: inc/vendors/classes/class-imagify-partner.php:531
msgid "Plugin installed successfully."
msgstr "Dodatak uspješno instaliran."

#: inc/vendors/classes/class-imagify-partner.php:532
msgid "Sorry, you are not allowed to install plugins on this site."
msgstr "Nažalost, ne smijete instalirati dodatke na ovu web-lokaciju."

#: inc/vendors/classes/class-imagify-partner.php:533
msgid "Sorry, you are not allowed to do that."
msgstr "Nažalost, to vam nije dopušteno."

#: inc/vendors/classes/class-imagify-partner.php:534
msgid "Plugin install failed."
msgstr "Neuspjela instalacija dodatka."

#: inc/vendors/classes/class-imagify-partner.php:535
msgid "Go back"
msgstr "Vrati se"

#: views/cpcss/activate-cpcss-mobile.php:13
msgid "Load CSS asynchronously for mobile"
msgstr "Učitajte CSS asinkrono za mobilne uređaje"

#: views/cpcss/activate-cpcss-mobile.php:14
msgid "Your website currently uses the same Critical Path CSS for both desktop and mobile."
msgstr "Vaša web lokacija trenutno koristi istu putanju ključnog CSS-a za desktop i za mobilne uređaje."

#: views/cpcss/activate-cpcss-mobile.php:15
msgid "Click the button to enable mobile-specific CPCSS for your site."
msgstr "Kliknite gumb kako biste omogućili CPCSS za svoju web lokaciju specifičan za mobilne uređaje."

#: views/cpcss/activate-cpcss-mobile.php:20
#: views/settings/enable-google-fonts.php:19
#, php-format
msgid "This is a one-time action and this button will be removed afterwards. %1$sMore info%2$s"
msgstr "Ovo je jednokratna radnja i ovaj će gumb biti uklonjen nakon toga.%1$sViše informacija%2$s"

#: views/cpcss/activate-cpcss-mobile.php:30
#, php-format
msgid "Your site is now using mobile-specific critical path CSS. %1$sMore info%2$s"
msgstr "Vaša web lokacija koristi CSS-ov kritični put za mobilne uređaje.%1$sViše informacija%2$s"

#: views/cpcss/activate-cpcss-mobile.php:39
msgid "Generate Mobile Specific CPCSS"
msgstr "Generiraj CPCSS specifičan za mobilne uređaje"

#: views/cpcss/metabox/container.php:19
msgid "Critical Path CSS"
msgstr "Kritični CSS put"

#: views/cpcss/metabox/generate.php:23
#, php-format
msgid "Generate specific Critical Path CSS for this post. %1$sMore info%2$s"
msgstr "Kreiraj kritični CSS put za ovaj post %1$sViše informacija%2$s"

#: views/cpcss/metabox/generate.php:33
#, php-format
msgid "This post uses specific Critical Path CSS. %1$sMore info%2$s"
msgstr "Ovaj post koristi kritični CSS put. %1$sViše informacija%2$s"

#: views/cpcss/metabox/generate.php:56
msgid "Revert back to the default CPCSS"
msgstr "Vrati se na zadani CPCSS"

#: views/deactivation-intent/form.php:21
msgid "Facing an issue?"
msgstr "Suočavate se s problemom?"

#: views/deactivation-intent/form.php:24
msgid "It is not always necessary to deactivate WP Rocket when facing any issues. Most of them can be fixed by deactivating only some options."
msgstr "Nije uvijek potrebno deaktivirati WP Rocket kada se suočite s bilo kakvim problemima. Većina problema se može popraviti deaktiviranjem samo nekih opcija."

#: views/deactivation-intent/form.php:29
#, php-format
msgid "Our advice? Instead of deactivating WP Rocket, use our %1$sSafe Mode%2$s to quickly disable LazyLoad, File Optimization, and CDN options. Then check to see if your issue is resolved."
msgstr "Naš savjet? Umjesto da deaktivirate WP Rocket, koristite naš %1$sSiguran način%2$s da brzo onemogućite opcije LazyLoad, Optimizacija datoteka i CDN. Zatim provjerite je li vaš problem riješen."

#: views/deactivation-intent/form.php:35
msgid "Do you want to use our Safe Mode to troubleshoot WP Rocket?"
msgstr "Želite li koristiti naš Siguran način za rješavanje problema s WP Rocketom?"

#: views/deactivation-intent/form.php:55
#, php-format
msgid "Yes, apply \"%1$sSafe Mode%2$s\""
msgstr "Da, primjeni \"%1$sSiguran način%2$s\""

#: views/deactivation-intent/form.php:76
msgid "No, deactivate and snooze this message for"
msgstr "Ne, deaktivirajte i odgodite ovu poruku na"

#: views/deactivation-intent/form.php:78
msgid "1 day"
msgstr "1 dan"

#: views/deactivation-intent/form.php:79
msgid "7 days"
msgstr "7 dana"

#: views/deactivation-intent/form.php:80
msgid "30 days"
msgstr "30 dana"

#: views/deactivation-intent/form.php:81
msgid "Forever"
msgstr "Zauvijek"

#: views/deactivation-intent/form.php:87
msgid "Cancel"
msgstr "Poništiti"

#: views/deactivation-intent/form.php:88
msgid "Confirm"
msgstr "Potvrdi"

#: views/settings/enable-google-fonts.php:13
msgid "Enable Google Font Optimization"
msgstr "Uključi optimizaciju Google fontova"

#: views/settings/enable-google-fonts.php:14
msgid "Improves font performance and combines multiple font requests to reduce the number of HTTP requests."
msgstr "Poboljšava performanse fonta i kombinira više zahtjeva za font kako bi se smanjio broj HTTP zahtjeva. "

#: views/settings/enable-google-fonts.php:29
#, php-format
msgid "Google Fonts Optimization is now enabled for your site. %1$sMore info%2$s"
msgstr "Optimizacija Google Fontova je uključena za vašu web stranicu. %1$sViše informacija%2$s"

#: views/settings/enable-google-fonts.php:38
msgid "Optimize Google Fonts"
msgstr "Optimiziraj Google Fontove"

#: views/settings/fields/cache-lifespan.php:26
msgid "Clear cache after"
msgstr "Očisti cache nakon"

#: views/settings/fields/cnames.php:58
#: views/settings/fields/cnames.php:92
#: views/settings/fields/cnames.php:122
msgid "CSS & JavaScript"
msgstr "CSS i JavaScript"

#: views/settings/fields/cnames.php:59
#: views/settings/fields/cnames.php:93
#: views/settings/fields/cnames.php:123
msgid "JavaScript"
msgstr "JavaScript"

#: views/settings/fields/cnames.php:60
#: views/settings/fields/cnames.php:94
#: views/settings/fields/cnames.php:124
msgid "CSS"
msgstr "CSS"

#: views/settings/fields/import-form.php:29
msgid "Import settings"
msgstr "Uvezi postavke"

#: views/settings/fields/one-click-addon.php:43
#: views/settings/fields/rocket-addon.php:43
msgid "Add-on status"
msgstr "Status dodatka"

#: views/settings/fields/rocket-addon.php:72
msgid "Modify options"
msgstr "Izmijenite opcije"

#: views/settings/fields/rocket-cdn.php:29
#: views/settings/fields/rocket-cdn.php:41
msgid "CDN CNAME"
msgstr "CDN CNAME"

#: views/settings/fields/rocket-cdn.php:62
#, php-format
msgid "Purges RocketCDN cached resources for your website. %s"
msgstr "Čisti  RocketCDN cachirane resurse za vašu web stranicu. %s"

#: views/settings/fields/rocket-cdn.php:63
#: views/settings/page-sections/cloudflare.php:36
#: views/settings/page-sections/sucuri.php:37
msgid "Learn more"
msgstr "Saznaj više"

#: views/settings/fields/rocket-cdn.php:72
msgid "Clear all RocketCDN cache files"
msgstr "Očisti sve RocketCDN cache datoteke"

#: views/settings/page-sections/cloudflare.php:27
msgid "Cloudflare Cache"
msgstr "Cloudflare Cache"

#: views/settings/page-sections/cloudflare.php:35
#: views/settings/page-sections/sucuri.php:36
#, php-format
msgid "Purges cached resources for your website. %s"
msgstr "Čisti cachirane resurse za vašu web-lokaciju. %s"

#: views/settings/page-sections/cloudflare.php:36
msgid "https://support.cloudflare.com/hc/en-us/articles/200169246"
msgstr "https://support.cloudflare.com/hc/en-us/articles/200169246"

#: views/settings/page-sections/cloudflare.php:45
msgid "Clear all Cloudflare cache files"
msgstr "Očisti sve Cloudflare cache datoteke"

#: views/settings/page-sections/dashboard.php:38
msgid "Congratulations!"
msgstr "Čestitamo!"

#: views/settings/page-sections/dashboard.php:40
msgid "WP Rocket is now activated and already working for you."
msgstr "WP Rocket je sada aktiviran i spreman za rad."

#: views/settings/page-sections/dashboard.php:42
msgid "Your website should be loading faster now!"
msgstr "Vaša bi se web-lokacija trebala već brže učitavati!"

#: views/settings/page-sections/dashboard.php:44
#, php-format
msgid "To guarantee fast websites, WP Rocket automatically applies 80% of web performance best practices."
msgstr "Kako bi se zajamčile brze web stranice, WP Rocket primjenjuje 80% najboljih praksi za web performanse."

#: views/settings/page-sections/dashboard.php:44
msgid "We also enable options that provide immediate benefits to your website."
msgstr "Omogućujemo i opcije koje pružaju trenutačnu korist vašoj web-lokaciji."

#: views/settings/page-sections/dashboard.php:45
msgid "Continue to the options to further optimize your site!"
msgstr "Nastavite do opcija za daljnju optimizaciju vaše web-lokacije!"

#: views/settings/page-sections/dashboard.php:62
msgid "My Account"
msgstr "Moj račun"

#: views/settings/page-sections/dashboard.php:68
msgid "Refresh info"
msgstr "Osvježi informacije"

#: views/settings/page-sections/dashboard.php:86
msgid "with"
msgstr "s"

#: views/settings/page-sections/dashboard.php:100
msgid "Expiration Date"
msgstr "Datum isteka"

#: views/settings/page-sections/dashboard.php:110
msgid "View my account"
msgstr "Pogledaj moj račun"

#: views/settings/page-sections/dashboard.php:137
msgid "Quick Actions"
msgstr "Brze radnje"

#: views/settings/page-sections/dashboard.php:144
msgid "Remove all cached files"
msgstr "Ukloni sve cachirane datoteke"

#: views/settings/page-sections/dashboard.php:164
#: views/settings/page-sections/dashboard.php:170
msgid "Regenerate Critical CSS"
msgstr "Obnovi ključni CSS"

#: views/settings/page-sections/dashboard.php:182
msgid "Remove Used CSS Cache"
msgstr "Ukloni nekorišteni CSS cache"

#: views/settings/page-sections/dashboard.php:205
msgid "Frequently Asked Questions"
msgstr "Učestalo postavljana pitanja"

#: views/settings/page-sections/dashboard.php:219
msgid "Still cannot find a solution?"
msgstr "Još uvijek ne možete pronaći rješenje?"

#: views/settings/page-sections/dashboard.php:220
msgid "Submit a ticket and get help from our friendly and knowledgeable Rocketeers."
msgstr "Otvorite ticket i zatražite pomoć od naše stručne ekipe."

#: views/settings/page-sections/dashboard.php:228
msgid "Ask support"
msgstr "Pitajte korisničku podršku"

#: views/settings/page-sections/database.php:25
msgid "Backup your database before you run a cleanup!"
msgstr "Backupirajte bazu podataka prije nego što pokrenete čišćenje!"

#: views/settings/page-sections/database.php:26
msgid "Once a database optimization has been performed, there is no way to undo it."
msgstr "Nakon što je izvršena optimizacija baze podataka, nema načina da je poništite."

#: views/settings/page-sections/database.php:28
msgid "Save Changes and Optimize"
msgstr "Spremi promjene i optimiziraj"

#: views/settings/page-sections/imagify.php:21
#, php-format
msgid "%1$sWP ROCKET%2$s created %3$sIMAGIFY%4$s %1$sfor best-in-class image optimization.%2$s"
msgstr "%1$sWP ROCKET%2$s je kreirao %3$sIMAGIFY%4$s %1$sza najbolju optimizaciju slika.%2$s"

#: views/settings/page-sections/imagify.php:24
msgid "Compress image to make your website faster, all while maintaining image quality."
msgstr "Komprimirajte sliku da biste učinili web-lokaciju bržom a pritom zadržali kvalitetu slike."

#: views/settings/page-sections/imagify.php:25
msgid "More on Imagify:"
msgstr "Više na Imagify:"

#: views/settings/page-sections/imagify.php:27
msgid "Imagify Plugin Page"
msgstr "Stranica Imagify dodatka"

#: views/settings/page-sections/imagify.php:28
msgid "Imagify Website"
msgstr "Imagify web-lokacija"

#: views/settings/page-sections/imagify.php:29
msgid "Review of Image Compression Plugins"
msgstr "Pregled dodataka za kompresiju slika"

#: views/settings/page-sections/imagify.php:38
msgid "Install Imagify"
msgstr "Instaliraj Imagify"

#: views/settings/page-sections/license.php:22
msgid "WP Rocket was not able to automatically validate your license."
msgstr "WP Rocket nije mogao automatski potvrditi valjanost vaše licence."

#: views/settings/page-sections/license.php:29
#, php-format
msgid "Follow this %1$s, or contact %2$s to get the engine started."
msgstr "Slijedite %1$s, ili kontaktirajte %2$sza pokretanje motora."

#: views/settings/page-sections/license.php:32
#, php-format
msgid "%1$s%2$s%3$stutorial%4$s"
msgstr "%1$s%2$s%3$stutorial%4$s"

#: views/settings/page-sections/license.php:34
msgid "https://docs.wp-rocket.me/article/100-resolving-problems-with-license-validation/?utm_source=wp_plugin&utm_medium=wp_rocket"
msgstr "https://docs.wp-rocket.me/article/100-resolving-problems-with-license-validation/?utm_source=wp_plugin&utm_medium=wp_rocket"

#: views/settings/page-sections/license.php:40
#, php-format
msgid "%1$s%2$s%3$ssupport%4$s"
msgstr "%1$s%2$s%3$spodrška%4$s"

#: views/settings/page-sections/sucuri.php:46
msgid "Clear all Sucuri cache files"
msgstr "Obrišite sve Sucuri cache datoteke"

#: views/settings/page-sections/tools.php:20
#, php-format
msgid "Files size: %1$s. Number of entries: %2$s."
msgstr "Veličina datoteke: %1$s. Broj unosa: %2$s."

#: views/settings/page-sections/tools.php:23
#, php-format
msgid "%1$sDownload the file%2$s."
msgstr "%1$sPreuzmi datoteku%2$s."

#: views/settings/page-sections/tools.php:26
#, php-format
msgid "%1$sDelete the file%2$s."
msgstr "%1$sIzbriši datoteku%2$s."

#: views/settings/page-sections/tools.php:37
msgid "Export settings"
msgstr "Izvezi postavke"

#: views/settings/page-sections/tools.php:38
msgid "Download a backup file of your settings"
msgstr "Preuzmite sigurnosnu kopiju svojih postavki"

#: views/settings/page-sections/tools.php:46
msgid "Download settings"
msgstr "Postavke preuzimanja"

#: views/settings/page-sections/tools.php:60
msgid "Rollback"
msgstr "Povratak postavki"

#: views/settings/page-sections/tools.php:64
#, php-format
msgid "Has version %s caused an issue on your website?"
msgstr "Je li verzija %s uzrokovala problem na vašoj web-lokaciji?"

#: views/settings/page-sections/tools.php:69
#, php-format
msgid "You can rollback to the previous major version here.%sThen send us a support request."
msgstr "Možete se vratiti na prethodnu verziju ovdje.%sZatim pošaljite zahtjev za podršku."

#: views/settings/page-sections/tools.php:80
#, php-format
msgid "Reinstall version %s"
msgstr "Reinstaliraj verziju %s"

#: views/settings/page-sections/tools.php:106
msgid "Debug mode"
msgstr "Način otklanjanja pogrešaka (debug)"

#: views/settings/page-sections/tools.php:111
msgid "Create a debug log file."
msgstr "Kreiraj debug log datoteku."

#: views/settings/page-sections/tutorials.php:13
#: views/settings/partials/getting-started.php:18
msgid "Getting Started"
msgstr "Kako započeti"

#: views/settings/page-sections/tutorials.php:15
msgid "Getting Started with WP Rocket"
msgstr "Početak rada s WP Rocketom"

#: views/settings/page-sections/tutorials.php:16
#: views/settings/partials/getting-started.php:12
msgid "Finding the Best Settings for Your Site"
msgstr "Pronalazak najboljih postavki za vašu web stranicu"

#: views/settings/page-sections/tutorials.php:17
#: views/settings/partials/getting-started.php:13
msgid "How to Check if WP Rocket is Caching Your Site"
msgstr "Kako provjeriti da li WP Rocket cachira vašu web stranicu"

#: views/settings/page-sections/tutorials.php:18
#: views/settings/partials/getting-started.php:14
msgid "How to Measure the Speed of Your Site"
msgstr "Kako izmjeriti brzinu učitavanja vaše web stranice"

#: views/settings/page-sections/tutorials.php:19
msgid "How Preloading Works"
msgstr "Kako funkcionira predučitavanje"

#: views/settings/page-sections/tutorials.php:23
msgid "Passing the Core Web vitals"
msgstr "Prolazak Core Web vitals"

#: views/settings/page-sections/tutorials.php:25
msgid "How to improve LCP with WP Rocket"
msgstr "Kako poboljšati LCP pomoću WP Rocketa"

#: views/settings/page-sections/tutorials.php:26
msgid "How to improve FID with WP Rocket"
msgstr "Kako poboljšati FID pomoću WP Rocketa"

#: views/settings/page-sections/tutorials.php:27
msgid "How to improve CLS with WP Rocket"
msgstr "Kako poboljšati CLS pomoću WP Rocketa"

#: views/settings/page-sections/tutorials.php:31
msgid "Troubleshooting"
msgstr "Rješavanje problema"

#: views/settings/page-sections/tutorials.php:33
msgid "Troubleshooting Display Issues with File Optimization"
msgstr "Rješavanje problema  prikaza prilikom optimizacije datoteka"

#: views/settings/page-sections/tutorials.php:34
msgid "How to Find the Right JavaScript to Exclude"
msgstr "Kako pronaći pravi JavaScript za isključivanje"

#: views/settings/page-sections/tutorials.php:35
msgid "How External Content Slows Your Site"
msgstr "Kako vanjski sadržaj/resursi usporavaju vašu web stranicu"

#: views/settings/page-sections/tutorials.php:41
msgid "Set Up the Cloudflare Add-on"
msgstr "Postavljanje Cloudflare dodatka"

#: views/settings/page.php:16
msgid "WP Rocket Settings"
msgstr "WP Rocket postavke"

#: views/settings/page.php:30
#, php-format
msgid "version %s"
msgstr "verzija %s"

#: views/settings/page.php:60
msgid "Show Sidebar"
msgstr "Prikaži bočnu traku"

#: views/settings/page.php:82
#, php-format
msgid "Below is a detailed view of all data WP Rocket will collect %1$sif granted permission.%2$s"
msgstr "U nastavku se nalazi detaljan pregled svih podataka koje će WP Rocket prikupiti%1$sako dobije dozvolu.%2$s"

#: views/settings/page.php:87
msgid "WP Rocket will never transmit any domain names or email addresses (except for license validation), IP addresses, or third-party API keys."
msgstr "WP Rocket nikada neće prenijeti imena domena ili adrese e-pošte (osim za provjeru licence), IP adrese ili API ključeve treće strane."

#: views/settings/page.php:89
msgid "Activate Rocket analytics"
msgstr "Aktiviraj Rocket analitiku"

#: views/settings/partials/documentation.php:15
msgid "It is a great starting point to fix some of the most common issues."
msgstr "To je izvrsna polazna točka za rješavanje nekih od najčešćih problema."

#: views/settings/partials/documentation.php:22
msgid "Read the documentation"
msgstr "Pročitaj dokumentaciju"

#: views/settings/partials/getting-started.php:11
msgid "What WP Rocket Does For You By Default"
msgstr "Što WP Rocket radi prema zadanim postavkama"

#: views/settings/partials/sidebar.php:12
msgid "How to correctly measure your website’s loading time"
msgstr "Kako točno izmjeriti vrijeme učitavanja web-lokacije"

#: views/settings/partials/sidebar.php:14
msgid "Check our tutorial and learn how to measure the speed of your site."
msgstr "Provjerite naš vodič i saznajte kako mjeriti brzinu učitavanja vaše web stranice."

#: views/settings/partials/sidebar.php:15
#: views/settings/partials/sidebar.php:24
msgid "Read our guide"
msgstr "Pročitajte naš vodič"

#: views/settings/partials/sidebar.php:18
msgid "Learn about optimal WP Rocket settings for mobile."
msgstr "Saznajte više o optimalnim postavkama WP Rocketa za mobilne uređaje."

#: views/settings/partials/sidebar.php:27
msgid "Test and Improve Google Core Web Vitals for WordPress."
msgstr "Testirajte i poboljšajte Google Core Web Vitals za WordPress."

#: views/settings/partials/sidebar.php:28
msgid "Read more"
msgstr "Pročitaj više"

#: views/settings/partials/sidebar.php:33
msgid "You have not activated logged-in user cache."
msgstr "Niste aktivirali cachiranje za logirane korisnike."

#: views/settings/partials/sidebar.php:34
msgid "Use a private browser to check your website's speed and visual appearance."
msgstr "Pomoću privatne/incognito kartice preglednika provjerite brzinu i izgled vaše web-lokacije."

#: views/settings/sections/addons-container.php:24
#: views/settings/sections/fields-container.php:28
msgid "Need Help?"
msgstr "Trebate pomoć?"

#: inc/Addon/Cloudflare/Cloudflare.php:205
msgid "days"
msgstr ""

#: inc/Addon/Cloudflare/Cloudflare.php:208
msgid "seconds"
msgstr ""

#: inc/Addon/Cloudflare/Cloudflare.php:210
msgid "minutes"
msgstr ""

#: inc/Addon/Cloudflare/Cloudflare.php:212
msgid "hours"
msgstr ""

#: inc/Addon/Cloudflare/Subscriber.php:224
#: inc/Addon/Cloudflare/Subscriber.php:253
#, php-format
msgid "%1$sWP Rocket:%2$s %3$s"
msgstr ""

#: inc/Addon/Cloudflare/Subscriber.php:242
#, php-format
msgid "%1$sWP Rocket:%2$s Cloudflare cache successfully purged."
msgstr ""

#: inc/Addon/Cloudflare/Subscriber.php:328
msgctxt "Cloudflare caching level"
msgid "standard"
msgstr ""

#: inc/Addon/Cloudflare/Subscriber.php:403
#, php-format
msgid "Cloudflare browser cache set to %s"
msgstr ""

#: inc/Addon/Cloudflare/Subscriber.php:512
#, php-format
msgid "%1$sWP Rocket:%2$s Optimal settings activated for Cloudflare:"
msgstr ""

#: inc/Addon/Cloudflare/Subscriber.php:521
#, php-format
msgid "%1$sWP Rocket:%2$s Optimal settings deactivated for Cloudflare, reverted to previous settings:"
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:642
msgid "If you have problems after activating this option, copy and paste the default exclusions to quickly resolve issues:"
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:646
#, php-format
msgid "Also, please check our %1$sdocumentation%2$s for a list of compatibility exclusions."
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:651
msgid "Internal scripts are excluded by default to prevent issues. Remove them to take full advantage of this option."
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:654
#, php-format
msgid "If this causes trouble, restore the default exclusions, found %1$shere%2$s"
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:942
msgid "One-click exclusions"
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:943
msgid "When using the Delay JavaScript Execution, you might experience delay loading elements located in the viewport that need to appear immediately - e.g. slider, header, menu."
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:944
msgid "If you need instant visibility, click below on files that should NOT be delayed. This selection will help users interact with the elements straight away."
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:1180
msgid "When you enable preloading WP Rocket will automatically detect your sitemaps and save all URLs to the database. The plugin will make sure that your cache is always preloaded."
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:1234
msgid "Exclude URLs"
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:1239
#, php-format
msgid "Specify URLs to be excluded from the preload feature (one per line). %1$sMore info%2$s"
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:1827
#, php-format
msgid "%1$sPlanning on using Automatic Platform Optimization (APO)?%2$s Just activate the official Cloudflare plugin and configure it. WP Rocket will automatically enable compatibility."
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:2129
msgctxt "Sucuri"
msgid "Firewall API key (for plugin), must be in format {32 characters}/{32 characters}:"
msgstr ""

#: inc/Engine/CDN/RocketCDN/views/cta-big.php:42
#, php-format
msgid "High performance Content Delivery Network (CDN) with %1$sunlimited bandwidth%2$s"
msgstr ""

#: inc/Engine/CriticalPath/CriticalCSSSubscriber.php:840
#, php-format
msgid "We highly recommend the %1$supdated Remove Unused CSS%2$s for a better CSS optimization. Load CSS Asynchronously is always available as a back-up."
msgstr ""

#: inc/Engine/CriticalPath/CriticalCSSSubscriber.php:845
msgid "Stay with the old option"
msgstr ""

#: inc/Engine/License/Renewal.php:76
#, php-format
msgid "Renew before it is too late, you will only pay %1$s%2$s%3$s!"
msgstr ""

#: inc/Engine/License/Renewal.php:85
#, php-format
msgid "Renew with a %1$s%2$s discount%3$s before it is too late, you will only pay %4$s%5$s%6$s!"
msgstr ""

#: inc/Engine/License/Renewal.php:139
#, php-format
msgid "Renew your license for 1 year now at %1$s%3$s%2$s."
msgstr ""

#: inc/Engine/License/Renewal.php:152
#, php-format
msgid "Renew your license for 1 year now and get %1$s%3$s OFF%2$s immediately: you will only pay %1$s%4$s%2$s!"
msgstr ""

#: inc/Engine/License/Renewal.php:218
#, php-format
msgid "Renew before it is too late, you will pay %1$s%3$s%2$s."
msgstr ""

#: inc/Engine/License/Renewal.php:546
#, php-format
msgid "You need a valid license to continue using this feature. %1$sRenew now%2$s before losing access."
msgstr ""

#: inc/Engine/License/Renewal.php:567
#, php-format
msgid "You need an active license to enable this option. %1$sRenew now%2$s."
msgstr ""

#: inc/Engine/License/Renewal.php:595
#, php-format
msgid "You need an active license to enable this option. %1$sMore info%2$s."
msgstr ""

#: inc/Engine/License/views/renewal-expired-banner-ocd-disabled.php:11
msgid "The Optimize CSS Delivery feature is disabled."
msgstr ""

#: inc/Engine/License/views/renewal-expired-banner-ocd-disabled.php:15
msgid "You can no longer use the Remove Unused CSS or Load CSS asynchronously options."
msgstr ""

#: inc/Engine/License/views/renewal-expired-banner-ocd-disabled.php:20
#, php-format
msgid "You need an %1$sactive license%2$s to keep optimizing your CSS delivery, which addresses a PageSpeed Insights recommendation and improves your page performance."
msgstr ""

#: inc/Engine/License/views/renewal-expired-banner-ocd.php:11
msgid "You will soon lose access to some features"
msgstr ""

#: inc/Engine/License/views/renewal-expired-banner-ocd.php:18
#, php-format
msgid "You need an %1$sactive license to continue optimizing your CSS delivery%2$s."
msgstr ""

#: inc/Engine/License/views/renewal-expired-banner-ocd.php:24
msgid "The Remove Unused CSS and Load CSS asynchronously features are great options to address the PageSpeed Insights recommendations and improve your website performance."
msgstr ""

#: inc/Engine/License/views/renewal-expired-banner-ocd.php:29
#, php-format
msgid "They will be %1$sautomatically disabled on %3$s%2$s."
msgstr ""

#: inc/Engine/License/views/renewal-expired-banner.php:18
#, php-format
msgid "Your website could be much faster if it could take advantage of our %1$snew features and enhancements%2$s. 🚀"
msgstr ""

#: inc/Engine/Optimization/DelayJS/Admin/SiteList.php:195
msgid "Analytics & Ads"
msgstr ""

#: inc/Engine/Optimization/DelayJS/Admin/SiteList.php:200
msgid "Plugins"
msgstr ""

#: inc/Engine/Optimization/DelayJS/Admin/SiteList.php:205
msgid "Themes"
msgstr ""

#: inc/Engine/Optimization/DynamicLists/DynamicLists.php:101
msgid "You need an active license to get the latest version of the lists from our server."
msgstr ""

#: inc/Engine/Optimization/DynamicLists/DynamicLists.php:115
msgid "Could not get updated lists from server."
msgstr ""

#: inc/Engine/Optimization/DynamicLists/DynamicLists.php:124
msgid "Lists are up to date."
msgstr ""

#: inc/Engine/Optimization/DynamicLists/DynamicLists.php:133
msgid "Could not update lists."
msgstr ""

#: inc/Engine/Optimization/DynamicLists/DynamicLists.php:142
msgid "Lists are successfully updated."
msgstr ""

#: inc/Engine/Optimization/DynamicLists/ServiceProvider.php:52
msgid "Default Lists"
msgstr ""

#: inc/Engine/Optimization/DynamicLists/ServiceProvider.php:58
msgid "Delay JavaScript Execution Exclusion Lists"
msgstr ""

#: inc/Engine/Optimization/RUCSS/Admin/Settings.php:273
#, php-format
msgid "We suggest enabling %1$sPreload%2$s for the fastest results."
msgstr ""

#: inc/Engine/Optimization/RUCSS/Admin/Settings.php:521
#, php-format
msgid "Could not create the %2$s table in the database which is necessary for the Remove Unused CSS feature to work. Please reach out to <a href=\"%3$s\">our support</a>."
msgstr ""

#: inc/Engine/Optimization/RUCSS/Cron/Subscriber.php:160
msgid "WP Rocket Remove Unused CSS pending jobs"
msgstr ""

#: inc/Engine/Optimization/RUCSS/Cron/Subscriber.php:174
msgid "WP Rocket clear Remove Unused CSS failed jobs"
msgstr ""

#: inc/Engine/Preload/Admin/Settings.php:57
#, php-format
msgid "%1$s: The preload service is now active. After the initial preload it will continue to cache all your pages whenever they are purged. No further action is needed."
msgstr ""

#: inc/Engine/Preload/Cron/Subscriber.php:138
msgid "WP Rocket Preload pending jobs"
msgstr ""

#: inc/Engine/Preload/Cron/Subscriber.php:166
msgid "WP Rocket Preload revert stuck failed jobs"
msgstr ""

#: inc/ThirdParty/Plugins/CDN/Cloudflare.php:114
#, php-format
msgid "%1$s: Cloudflare's HTTP/2 Server Push is incompatible with the features of Remove Unused CSS and Combine CSS files. We strongly recommend disabling it."
msgstr ""

#: inc/ThirdParty/Plugins/CDN/Cloudflare.php:157
msgid "Your site is using the official Cloudflare plugin. We have enabled Cloudflare auto-purge for compatibility. If you have APO activated, it is also compatible."
msgstr ""

#: inc/ThirdParty/Plugins/CDN/Cloudflare.php:158
msgid "Cloudflare cache will be purged each time WP Rocket clears its cache to ensure content is always up-to-date."
msgstr ""

#: inc/ThirdParty/Plugins/CDN/Cloudflare.php:202
#, php-format
msgid "%1$sWP Rocket:%2$s You are using \"Dynamic Cookies Cache\". Cloudflare APO is not yet compatible with that feature."
msgstr ""

#: inc/ThirdParty/Plugins/CDN/Cloudflare.php:208
#, php-format
msgid "You should either disable Cloudflare APO or check with the theme/plugin requiring the use of “Dynamic Cookies Cache” developers for an alternative way to be page-cache friendly. %1$sMore info%2$s"
msgstr ""

#: inc/ThirdParty/Plugins/CDN/Cloudflare.php:259
#, php-format
msgid "%1$sWP Rocket:%2$s You are using \"Separate cache files for mobile devices\". You need to activate \"Cache by Device Type\" %3$ssetting%5$s on Cloudflare APO to serve the right version of the cache. %4$sMore info%5$s"
msgstr ""

#: inc/ThirdParty/Plugins/CDN/Cloudflare.php:280
#, php-format
msgid "%1$sWP Rocket:%2$s You have \"Cache by Device Type\" enabled on Cloudflare APO. If you judge it necessary for the website to have a different cache on mobile and desktop, we suggest you enable our “Separate Cache Files for Mobiles Devices” to ensure the generated cache is accurate."
msgstr ""

#: inc/ThirdParty/Plugins/Optimization/Perfmatters.php:38
msgid "Remove Unused CSS is currently activated in Perfmatters. If you want to use WP Rocket's Remove Unused CSS feature, disable this option in Perfmatters."
msgstr ""

#: inc/ThirdParty/Plugins/Optimization/RapidLoad.php:39
msgid "Automated unused CSS removal is currently activated in RapidLoad Power-Up for Autoptimize. If you want to use WP Rocket's Remove Unused CSS feature, disable the  RapidLoad Power-Up for Autoptimize plugin."
msgstr ""

#: inc/admin/ui/notices.php:757
msgid "Turn on Remove Unused CSS"
msgstr ""

#: inc/admin/ui/notices.php:763
msgid "Enable “Separate Cache Files for Mobile Devices” now"
msgstr ""

#: inc/common/admin-bar.php:84
#: inc/common/admin-bar.php:136
#: views/settings/page-sections/dashboard.php:150
msgid "Clear and preload cache"
msgstr ""

#: views/deactivation-intent/form.php:68
#, php-format
msgid "and export WP Rocket settings %1$s(Recommended as current settings will be deleted)%2$s"
msgstr ""

#: views/settings/dynamic-lists-update.php:14
msgid "Update Inclusion and Exclusion Lists"
msgstr ""

#: views/settings/dynamic-lists-update.php:19
#, php-format
msgid "Compatibility lists are downloaded automatically every week. Click the button if you want to update them manually. %1$sMore info%2$s"
msgstr ""

#: views/settings/dynamic-lists-update.php:29
msgid "Update lists"
msgstr ""
