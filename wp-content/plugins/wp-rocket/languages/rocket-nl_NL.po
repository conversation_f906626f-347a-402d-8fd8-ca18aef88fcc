# Copyright (C) 2024 WP Media
# This file is distributed under the same license as the WP Rocket plugin.
# Translators:
# <AUTHOR> <EMAIL>, 2019
# <PERSON><PERSON> <<EMAIL>>, 2019
# <PERSON><PERSON><PERSON> van Peppix Benelux <<EMAIL>>, 2019
# 7ac0574f11c98cef2c2bb199611beca1_d5b800d <8274a5173ac4adfaf1e288187b4ccdcd_970173>, 2021
# Thom, 2023
# <AUTHOR> <EMAIL>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: WP Rocket 3.16-alpha1\n"
"Report-Msgid-Bugs-To: https://wordpress.org/support/plugin/wp-rocket\n"
"POT-Creation-Date: 2024-04-29T20:40:45+00:00\n"
"PO-Revision-Date: 2019-08-26 15:14+0000\n"
"Last-Translator: Toine <<EMAIL>>, 2024\n"
"Language-Team: Dutch (Netherlands) (https://app.transifex.com/wp-media/teams/18133/nl_NL/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: nl_NL\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Domain: rocket\n"
"X-Generator: WP-CLI 2.4.0\n"

#. Plugin Name of the plugin
msgid "WP Rocket"
msgstr "WP Rocket"

#. Plugin URI of the plugin
msgid "https://wp-rocket.me"
msgstr "https://wp-rocket.me"

#. Description of the plugin
msgid "The best WordPress performance plugin."
msgstr "De beste WordPress prestatieplugin."

#. Author of the plugin
msgid "WP Media"
msgstr "WP Media"

#. Author URI of the plugin
msgid "https://wp-media.me"
msgstr "https://wp-media.me"

#. Translators: %s = Hosting name.
#: inc/3rd-party/hosting/flywheel.php:18
#: inc/3rd-party/hosting/wp-serveur.php:27 inc/deprecated/3.6.php:697
#: inc/deprecated/3.6.php:997 inc/deprecated/3.9.php:22
#: inc/ThirdParty/Hostings/Cloudways.php:90
#: inc/ThirdParty/Hostings/Dreampress.php:44
#: inc/ThirdParty/Hostings/Godaddy.php:63
#: inc/ThirdParty/Hostings/O2Switch.php:49
#: inc/ThirdParty/Hostings/OneCom.php:137
#: inc/ThirdParty/Hostings/Pressidium.php:49
#: inc/ThirdParty/Hostings/ProIsp.php:51 inc/ThirdParty/Hostings/Savvii.php:50
#: inc/ThirdParty/Hostings/WPEngine.php:47
#: inc/ThirdParty/Hostings/WPXCloud.php:51
msgid ""
"Your site is hosted on %s, we have enabled Varnish auto-purge for "
"compatibility."
msgstr ""
"Je wordt gehost met %s, we hebben het automatisch legen van Varnish voor "
"compatibiliteit ingeschakeld."

#: inc/Addon/Cloudflare/API/Client.php:129
msgid "Cloudflare did not provide any reply. Please try again later."
msgstr ""
"Cloudflare gaf geen reactie. Probeer het op een later moment nogmaals."

#: inc/Addon/Cloudflare/API/Client.php:135
msgid "Cloudflare unexpected response"
msgstr "Cloudflare onverwachte reactie"

#: inc/Addon/Cloudflare/API/Client.php:143
msgid "Missing Cloudflare result."
msgstr "Ontbrekend Cloudflare resultaat."

#: inc/Addon/Cloudflare/API/Client.php:194 inc/deprecated/3.5.php:112
#: inc/deprecated/3.5.php:169
msgid "Incorrect Cloudflare email address or API key."
msgstr "Ongeldig Cloudflare e-mailadres of API-sleutel."

#. translators: %1$s = opening link; %2$s = closing link
#: inc/Addon/Cloudflare/API/Client.php:198
#: inc/Addon/Cloudflare/API/Client.php:212
#: inc/Addon/Cloudflare/Cloudflare.php:75
#: inc/Addon/Cloudflare/Cloudflare.php:108
#: inc/classes/subscriber/Tools/class-detect-missing-tags-subscriber.php:157
#: inc/deprecated/3.5.php:92 inc/deprecated/3.5.php:116
#: inc/deprecated/3.5.php:129 inc/deprecated/3.5.php:157
#: inc/deprecated/3.5.php:173
msgid "Read the %1$sdocumentation%2$s for further guidance."
msgstr "Lees de %1$sdocumentatie%2$s voor verdere begeleiding."

#. translators: Documentation exists in EN, FR; use localized URL if
#. applicable.
#: inc/Addon/Cloudflare/API/Client.php:200
#: inc/Addon/Cloudflare/API/Client.php:214
#: inc/Addon/Cloudflare/Auth/APIKey.php:63
#: inc/Addon/Cloudflare/Cloudflare.php:77
#: inc/Addon/Cloudflare/Cloudflare.php:110 inc/deprecated/3.5.php:81
#: inc/deprecated/3.5.php:94 inc/deprecated/3.5.php:118
#: inc/deprecated/3.5.php:131 inc/deprecated/3.5.php:159
#: inc/deprecated/3.5.php:175 inc/deprecated/3.5.php:208
msgid ""
"https://docs.wp-rocket.me/article/18-using-wp-rocket-with-"
"cloudflare/?utm_source=wp_plugin&utm_medium=wp_rocket#add-on"
msgstr ""
"https://docs.wp-rocket.me/article/18-using-wp-rocket-with-"
"cloudflare/?utm_source=wp_plugin&utm_medium=wp_rocket#add-on"

#: inc/Addon/Cloudflare/API/Client.php:208 inc/deprecated/3.5.php:125
msgid "Incorrect Cloudflare Zone ID."
msgstr "Ongeldige Cloudflare Zone ID."

#. translators: %1$s = opening link; %2$s = closing link
#: inc/Addon/Cloudflare/Auth/APIKey.php:61
msgid ""
"Cloudflare email and/or API key are not set. Read the %1$sdocumentation%2$s "
"for further guidance."
msgstr ""
"Cloudflare e-mail en/of API-sleutel zijn niet ingesteld. Lees de "
"%1$sdocumentatie%2$s voor verdere uitleg."

#: inc/Addon/Cloudflare/Cloudflare.php:71 inc/deprecated/3.5.php:88
msgid "Missing Cloudflare Zone ID."
msgstr "Cloudflare Zone ID mist."

#: inc/Addon/Cloudflare/Cloudflare.php:104 inc/deprecated/3.5.php:153
msgid "It looks like your domain is not set up on Cloudflare."
msgstr "Het lijkt er op dat jouw domein niet is ingesteld bij Cloudflare."

#: inc/Addon/Cloudflare/Cloudflare.php:200
msgid "days"
msgstr "dagen"

#: inc/Addon/Cloudflare/Cloudflare.php:203
msgid "seconds"
msgstr "seconden"

#: inc/Addon/Cloudflare/Cloudflare.php:205
msgid "minutes"
msgstr "minuten"

#: inc/Addon/Cloudflare/Cloudflare.php:207
msgid "hours"
msgstr "uren"

#. translators: %1$s = <strong>, %2$s = </strong>, %3$s = CloudFare API return
#. message.
#: inc/Addon/Cloudflare/Subscriber.php:232
#: inc/Addon/Cloudflare/Subscriber.php:261
msgid "%1$sWP Rocket:%2$s %3$s"
msgstr "%1$sWP Rocket:%2$s %3$s"

#. translators: %1$s = <strong>, %2$s = </strong>.
#: inc/Addon/Cloudflare/Subscriber.php:250
msgid "%1$sWP Rocket:%2$s Cloudflare cache successfully purged."
msgstr "%1$sWP Rocket:%2$s Cloudflare cache succesvol geleegd."

#. translators: %s is the message returned by the CloudFlare API.
#: inc/Addon/Cloudflare/Subscriber.php:311
msgid "Cloudflare development mode error: %s"
msgstr "Fout bij Cloudflare ontwikkelmodus: %s"

#. translators: %s is the message returned by the CloudFlare API.
#: inc/Addon/Cloudflare/Subscriber.php:318
msgid "Cloudflare development mode %s"
msgstr "Cloudflare ontwikkelmodus %s"

#. translators: %s is the message returned by the CloudFlare API.
#: inc/Addon/Cloudflare/Subscriber.php:337
msgid "Cloudflare cache level error: %s"
msgstr "Fout bij Cloudflare cache-niveau: %s"

#: inc/Addon/Cloudflare/Subscriber.php:344
msgctxt "Cloudflare caching level"
msgid "standard"
msgstr "standaard"

#. translators: %s is the caching level returned by the CloudFlare API.
#: inc/Addon/Cloudflare/Subscriber.php:350
msgid "Cloudflare cache level set to %s"
msgstr "Cache-niveau van Cloudflare ingesteld op %s"

#. translators: %s is the message returned by the CloudFlare API.
#: inc/Addon/Cloudflare/Subscriber.php:368
msgid "Cloudflare minification error: %s"
msgstr "Fout bij Cloudflare verkleining: %s"

#. translators: %s is the message returned by the CloudFlare API.
#: inc/Addon/Cloudflare/Subscriber.php:375
msgid "Cloudflare minification %s"
msgstr "Cloudflare verkleining %s"

#. translators: %s is the message returned by the CloudFlare API.
#: inc/Addon/Cloudflare/Subscriber.php:393
msgid "Cloudflare rocket loader error: %s"
msgstr "Fout bij Cloudflare Rocket Loader: %s"

#. translators: %s is the message returned by the CloudFlare API.
#: inc/Addon/Cloudflare/Subscriber.php:400
msgid "Cloudflare rocket loader %s"
msgstr "Cloudflare Rocket Loader %s"

#. translators: %s is the message returned by the CloudFlare API.
#: inc/Addon/Cloudflare/Subscriber.php:418
msgid "Cloudflare browser cache error: %s"
msgstr "Fout bij Cloudflare browsercache: %s"

#. translators: %s is the message returned by the CloudFlare API.
#: inc/Addon/Cloudflare/Subscriber.php:425
msgid "Cloudflare browser cache set to %s"
msgstr "Cloudflare browsercache ingesteld op %s"

#. translators: %1$s = strong opening tag, %2$s = strong closing tag.
#: inc/Addon/Cloudflare/Subscriber.php:536
msgid "%1$sWP Rocket:%2$s Optimal settings activated for Cloudflare:"
msgstr "%1$sWP Rocket:%2$s optimale instellingen geactiveerd voor Cloudflare:"

#. translators: %1$s = strong opening tag, %2$s = strong closing tag.
#: inc/Addon/Cloudflare/Subscriber.php:545
msgid ""
"%1$sWP Rocket:%2$s Optimal settings deactivated for Cloudflare, reverted to "
"previous settings:"
msgstr ""
"%1$sWP Rocket:%2$s optimale instellingen uitgeschakeld voor Cloudflare, "
"teruggezet naar vorige instellingen:"

#: inc/Addon/Cloudflare/Subscriber.php:661 inc/admin/options.php:165
#: inc/classes/subscriber/Tools/class-detect-missing-tags-subscriber.php:148
msgid "WP Rocket: "
msgstr "WP Rocket: "

#. translators: %s is the error message returned by the API.
#: inc/Addon/Sucuri/Subscriber.php:96
msgid "Sucuri cache purge error: %s"
msgstr "Sucuri cache legen foutmelding: %s"

#: inc/Addon/Sucuri/Subscriber.php:101
msgid ""
"The Sucuri cache is being cleared. Note that it may take up to two minutes "
"for it to be fully flushed."
msgstr ""
"De Sucuri cache wordt geleegd. Let er op dat het tot twee minuten kan duren "
"voordat deze volledig is doorgespoeld."

#: inc/Addon/Sucuri/Subscriber.php:218
msgid "Sucuri firewall API key was not found."
msgstr "Sucuri firewall API-sleutel is niet gevonden."

#: inc/Addon/Sucuri/Subscriber.php:231
msgid "Sucuri firewall API key is invalid."
msgstr "Sucuri firewall API-sleutel is ongeldig."

#. translators: %s is an error message.
#: inc/Addon/Sucuri/Subscriber.php:286
msgid "Error when contacting Sucuri firewall API. Error message was: %s"
msgstr "Fout bij contact met Sucuri firewall API. Foutmelding was: %s"

#: inc/Addon/Sucuri/Subscriber.php:301
msgid "Could not get a response from the Sucuri firewall API."
msgstr "Kon geen antwoord krijgen van de Sucuri firewall API."

#: inc/Addon/Sucuri/Subscriber.php:316
msgid "Got an invalid response from the Sucuri firewall API."
msgstr "Kreeg een ongeldig antwoord van de Sucuri firewall API."

#: inc/Addon/Sucuri/Subscriber.php:330
msgid "The Sucuri firewall API returned an unknown error."
msgstr "De Sucuri firewall API heeft een onbekende fout geretourneerd."

#. translators: %s is an error message.
#: inc/Addon/Sucuri/Subscriber.php:334
msgid "The Sucuri firewall API returned the following error: %s"
msgid_plural "The Sucuri firewall API returned the following errors: %s"
msgstr[0] "De Sucuri firewall API heeft de volgende fout geretourneerd: %s"
msgstr[1] "De Sucuri firewall API heeft de volgende fouten geretourneerd: %s"

#. Translators: %1$s = plugin name(s), %2$s = opening <a> tag, %3$s = closing
#. </a> tag.
#: inc/Addon/WebP/AdminSubscriber.php:93
msgid ""
"You are using %1$s to serve WebP images so you do not need to enable this "
"option. %2$sMore info%3$s %4$s If you prefer to have WP Rocket serve WebP "
"for you instead, please disable WebP display in %1$s."
msgid_plural ""
"You are using %1$s to serve WebP images so you do not need to enable this "
"option. %2$sMore info%3$s %4$s If you prefer to have WP Rocket serve WebP "
"for you instead, please disable WebP display in %1$s."
msgstr[0] ""
"Je gebruikt %1$s om WebP-afbeeldingen weer te geven, dus je hoeft deze optie"
" niet in te schakelen. %2$sMeer info%3$s %4$s Als je liever hebt dat WP "
"Rocket WebP voor je serveert, schakel dan WebP-weergave uit in %1$s."
msgstr[1] ""
"Je gebruikt %1$s om WebP-afbeeldingen weer te geven, dus je hoeft deze optie"
" niet in te schakelen. %2$sMeer info%3$s %4$s Als je liever hebt dat WP "
"Rocket WebP voor je serveert, schakel dan WebP-weergave uit in %1$s."

#: inc/Addon/WebP/AdminSubscriber.php:105
msgid "WebP cache is disabled by filter."
msgstr "WebP-cache is uitschakelt door een filter."

#. Translators: %1$s = plugin name(s), %2$s = opening <a> tag, %3$s = closing
#. </a> tag.
#: inc/Addon/WebP/AdminSubscriber.php:115
#: inc/Addon/WebP/AdminSubscriber.php:141
msgid ""
"You are using %1$s to convert images to WebP. If you want WP Rocket to serve"
" them for you, activate this option. %2$sMore info%3$s"
msgid_plural ""
"You are using %1$s to convert images to WebP. If you want WP Rocket to serve"
" them for you, activate this option. %2$sMore info%3$s"
msgstr[0] ""
"Je gebruikt %1$s om afbeeldingen te converteren naar WebP. Als je wilt dat "
"WP Rocket ze voor je serveert, activeer dan deze optie. %2$sMeer info%3$s"
msgstr[1] ""
"Je gebruikt %1$s om afbeeldingen te converteren naar WebP. Als je wilt dat "
"WP Rocket ze voor je serveert, activeer dan deze optie. %2$sMeer info%3$s"

#. Translators: %1$s = plugin name(s), %2$s = opening <a> tag, %3$s = closing
#. </a> tag.
#: inc/Addon/WebP/AdminSubscriber.php:127
#: inc/Addon/WebP/AdminSubscriber.php:153
msgid ""
"You are using %1$s to convert images to WebP. WP Rocket will create separate"
" cache files to serve your WebP images. %2$sMore info%3$s"
msgid_plural ""
"You are using %1$s to convert images to WebP. WP Rocket will create separate"
" cache files to serve your WebP images. %2$sMore info%3$s"
msgstr[0] ""
"Je gebruikt %1$s om afbeeldingen te converteren naar WebP. WP Rocket maakt "
"aparte cache-bestanden aan om je WebP-afbeeldingen te serveren. %2$sMeer "
"info%3$s"
msgstr[1] ""
"Je gebruikt %1$s om afbeeldingen te converteren naar WebP. WP Rocket maakt "
"aparte cache-bestanden aan om je WebP-afbeeldingen te serveren. %2$sMeer "
"info%3$s"

#. Translators: %1$s = opening <a> tag, %2$s = closing </a> tag.
#: inc/Addon/WebP/AdminSubscriber.php:173
msgid ""
"%5$sWe have not detected any compatible WebP plugin!%6$s%4$s If you don’t "
"already have WebP images on your site consider using %3$sImagify%2$s or "
"another supported plugin. %1$sMore info%2$s %4$s If you are not using WebP "
"do not enable this option."
msgstr ""
"%5$sWe hebben geen compatibele WebP-plugin gevonden!%6$s%4$s Als je nog geen"
" WebP-afbeeldingen op je site hebt, kun je overwegen om %3$sImagify%2$s of "
"een andere ondersteunde plugin te gebruiken. %1$sMeer info%2$s %4$s Als je "
"geen WebP gebruikt, schakel deze optie dan niet in."

#: inc/Addon/WebP/AdminSubscriber.php:185
msgid "WP Rocket will create separate cache files to serve your WebP images."
msgstr ""
"WP Rocket maakt aparte cache-bestanden voor het serveren van je WebP "
"afbeeldingen."

#: inc/admin/admin.php:18 inc/common/admin-bar.php:273
#: inc/deprecated/deprecated.php:1787
msgid "Support"
msgstr "Ondersteuning"

#: inc/admin/admin.php:20
msgid "Docs"
msgstr "Docs"

#: inc/admin/admin.php:22 inc/common/admin-bar.php:261
msgid "FAQ"
msgstr "FAQ"

#: inc/admin/admin.php:24 inc/common/admin-bar.php:70
msgid "Settings"
msgstr "Instellingen"

#: inc/admin/admin.php:96 inc/admin/admin.php:117 inc/deprecated/3.5.php:898
#: inc/Engine/Cache/AdminSubscriber.php:134
msgid "Clear this cache"
msgstr "Deze cache legen"

#: inc/admin/admin.php:402
msgid "Settings import failed: you do not have the permissions to do this."
msgstr ""
"Instellingen importeren mislukt: je hebt geen toestemming om dit te doen."

#: inc/admin/admin.php:406
msgid "Settings import failed: no file uploaded."
msgstr "Instellingen importeren mislukt: geen bestand geüpload."

#: inc/admin/admin.php:410
msgid "Settings import failed: incorrect filename."
msgstr "Instellingen importeren mislukt: onjuiste bestandsnaam."

#: inc/admin/admin.php:421
msgid "Settings import failed: incorrect filetype."
msgstr "Instellingen importeren mislukt: onjuist bestandstype."

#: inc/admin/admin.php:431
msgid "Settings import failed: "
msgstr "Importeren van instellingen mislukt:"

#: inc/admin/admin.php:447
msgid "Settings import failed: unexpected file content."
msgstr ""
"Importeren van instellingen mislukt: inhoud van het bestand is onjuist."

#: inc/admin/admin.php:492
msgid "Settings imported and saved."
msgstr "Instellingen geïmporteerd en opgeslagen."

#: inc/admin/options.php:102 inc/Engine/Admin/Settings/Page.php:571
msgid "Excluded CSS Files"
msgstr "Uitgesloten CSS-bestanden"

#: inc/admin/options.php:103 inc/Engine/Admin/Settings/Page.php:725
msgid "Excluded Inline JavaScript"
msgstr "Uitgesloten inline JavaScript"

#: inc/admin/options.php:104 inc/Engine/Admin/Settings/Page.php:743
#: inc/Engine/Admin/Settings/Page.php:776
#: inc/Engine/Admin/Settings/Page.php:823
msgid "Excluded JavaScript Files"
msgstr "Uitgesloten JavaScript-bestanden"

#: inc/admin/options.php:105
msgid "Defer JavaScript Files"
msgstr "JavaScript-bestanden uitstellen"

#: inc/admin/options.php:106
msgid "Excluded Delay JavaScript Files"
msgstr "Uitgesloten JavaScript-bestanden uitstellen"

#: inc/admin/options.php:107 inc/Engine/Admin/Settings/Page.php:1229
msgid "Never Cache URL(s)"
msgstr "Nooit URL(s) cachen"

#: inc/admin/options.php:108 inc/Engine/Admin/Settings/Page.php:1243
msgid "Never Cache User Agent(s)"
msgstr "Nooit user agent(s) cachen"

#: inc/admin/options.php:109 inc/Engine/Admin/Settings/Page.php:1249
msgid "Always Purge URL(s)"
msgstr "Altijd URL(s) legen"

#: inc/admin/options.php:110 inc/Engine/Admin/Settings/Page.php:1530
msgid "Exclude files from CDN"
msgstr "Sluit bestanden uit van CDN"

#. translators: 1 and 2 can be anything.
#: inc/admin/options.php:131
msgid "%1$s: <em>%2$s</em>"
msgstr "%1$s: <em>%2$s</em>"

#: inc/admin/options.php:141
msgid "The following pattern is invalid and has been removed:"
msgid_plural "The following patterns are invalid and have been removed:"
msgstr[0] "Het volgende patroon is ongeldig en is verwijderd:"
msgstr[1] "De volgende patronen zijn ongeldig en zijn verwijderd:"

#: inc/admin/options.php:157
msgid "More info"
msgstr "Meer informatie"

#: inc/admin/ui/meta-boxes.php:37 inc/admin/ui/notices.php:671
#: inc/common/admin-bar.php:91 inc/common/admin-bar.php:154
#: views/settings/page-sections/dashboard.php:150
msgid "Clear cache"
msgstr "Cache legen"

#. translators: %1$s WP Rocket plugin name; %2$s = file name.
#: inc/admin/ui/notices.php:30 inc/admin/ui/notices.php:43
msgid ""
"<strong>%1$s</strong> has not been deactivated due to missing writing permissions.<br>\n"
"Make <strong>%2$s</strong> writeable and retry deactivation, or force deactivation now:"
msgstr ""
"<strong>%1$s</strong> is door ontbrekende schrijfrechten niet gedeactiveerd.<br>\n"
"Maak <strong>%2$s</strong> schrijfbaar en probeer deactivatie nogmaals of forceer deactivatie nu:"

#. translators: %s is WP Rocket plugin name.
#: inc/admin/ui/notices.php:97
msgid ""
"<strong>%s</strong>: One or more plugins have been enabled or disabled, "
"clear the cache if they affect the front end of your site."
msgstr ""
"<strong>%s</strong>: één of meerdere plugins zijn in- of uitgeschakeld, leeg"
" de cache als dit van invloed is op de frontend van je site."

#. translators: %s is WP Rocket plugin name.
#: inc/admin/ui/notices.php:141
msgid ""
"<strong>%s</strong>: The following plugins are not compatible with this "
"plugin and may cause unexpected results:"
msgstr ""
"<strong>%s</strong>: de volgende plugins zijn niet compatibel met deze "
"plugin en kunnen onverwachte resultaten veroorzaken:"

#: inc/admin/ui/notices.php:147
msgid "Deactivate"
msgstr "Deactiveren"

#: inc/admin/ui/notices.php:189
msgid ""
"WP Rocket Footer JS is not an official add-on. It prevents some options in "
"WP Rocket from working correctly. Please deactivate it if you have problems."
msgstr ""
"WP Rocket Footer JS is geen officiële add-on. Het voorkomt dat sommige "
"opties in WP Rocket correct werken. Deactiveer het als je problemen "
"ondervindt."

#. translators: %1$s = opening link tag, %2$s = closing link tag.
#: inc/admin/ui/notices.php:229
msgid ""
"Endurance Cache is currently enabled, which will conflict with WP Rocket "
"Cache. Please set the Endurance Cache cache level to Off (Level 0) on the "
"%1$sSettings > General%2$s page to prevent any issues."
msgstr ""
"Endurance Cache is momenteel ingeschakeld, wat zal conflicteren met WP "
"Rocket Cache. Zet het Endurance Cache cache niveau op Off (Niveau 0) op de "
"%1$sInstellingen > Algemeen%2$s pagina om problemen te voorkomen."

#. translators: %1$s WP Rocket plugin name; %2$s = opening link; %3$s =
#. closing link
#: inc/admin/ui/notices.php:250
msgid ""
"%1$s: A custom permalink structure is required for the plugin to work "
"properly. %2$sGo to permalinks settings%3$s"
msgstr ""
"%1$s: een aangepaste permalinkstructuur is vereist door de plugin om juist "
"te werken. %2$sGa naar permalink-instellingen%3$s"

#. translators: %s = plugin name.
#: inc/admin/ui/notices.php:297
msgid ""
"%s could not modify the .htaccess file due to missing writing permissions."
msgstr ""
"%s kon het .htaccess-bestand niet wijzigen vanwege ontbrekende "
"schrijfrechten."

#. translators: This is a doc title! %1$s = opening link; %2$s = closing link
#: inc/admin/ui/notices.php:303 inc/admin/ui/notices.php:788
msgid "Troubleshoot: %1$sHow to make system files writeable%2$s"
msgstr "Probleemoplossing: %1$shoe maak je systeembestanden schrijfbaar%2$s"

#. translators: Documentation exists in EN, DE, FR, ES, IT; use loaclised URL
#. if applicable
#: inc/admin/ui/notices.php:305 inc/admin/ui/notices.php:790
msgid ""
"https://docs.wp-rocket.me/article/626-how-to-make-system-files-htaccess-wp-"
"config-writeable/?utm_source=wp_plugin&utm_medium=wp_rocket"
msgstr ""
"https://docs.wp-rocket.me/article/626-how-to-make-system-files-htaccess-wp-"
"config-writeable/?utm_source=wp_plugin&utm_medium=wp_rocket"

#: inc/admin/ui/notices.php:311
msgid ""
"Don’t worry, WP Rocket’s page caching and settings will still function "
"correctly."
msgstr ""
"Maak je geen zorgen, de pagina caching en instellingen van WP Rocket zullen "
"nog steeds correct functioneren."

#: inc/admin/ui/notices.php:311
msgid ""
"For optimal performance, adding the following lines into your .htaccess is "
"recommended (not required):"
msgstr ""
"Voor optimale prestaties is het aanbevolen (niet verplicht) om de volgende "
"regels toe te voegen aan je .htaccess:"

#. translators: %1$s = plugin name, %2$s + %3$s = opening links, %4$s =
#. closing link
#: inc/admin/ui/notices.php:458
msgid ""
"%1$s is good to go! %2$sTest your load time%4$s, or visit your "
"%3$ssettings%4$s."
msgstr ""
"%1$s is klaar voor gebruik! %2$sTest je laadtijd%4$s of bekijk je "
"%3$sinstellingen%4$s."

#: inc/admin/ui/notices.php:499
msgid ""
"Would you allow WP Rocket to collect non-sensitive diagnostic data from this"
" website?"
msgstr ""
"Wil je toestaan dat WP Rocket niet-gevoelige diagnostische gegevens van deze"
" website verzameld?"

#: inc/admin/ui/notices.php:500
msgid "This would help us to improve WP Rocket for you in the future."
msgstr "Dit helpt ons bij het verbeteren van WP Rocket in de toekomst."

#. translators: button text, click will expand data collection preview
#: inc/admin/ui/notices.php:506
msgid "What info will we collect?"
msgstr "Welke informatie zullen we verzamelen?"

#: inc/admin/ui/notices.php:511
msgid ""
"Below is a detailed view of all data WP Rocket will collect if granted "
"permission. WP Rocket will never transmit any domain names or email "
"addresses (except for license validation), IP addresses, or third-party API "
"keys."
msgstr ""
"Hieronder is een gedetailleerd overzicht van alle gegevens die WP Rocket zal"
" verzamelen na toestemming. WP Rocket zal nooit domeinnamen of "
"e-mailadressen versturen (behalve voor validatie van licentie), IP-adressen "
"of API-sleutels van derde partijen."

#. translators: button text for data collection opt-in
#: inc/admin/ui/notices.php:520
msgid "Yes, allow"
msgstr "Ja, toestaan"

#. translators: button text for data collection opt-in
#: inc/admin/ui/notices.php:523
msgid "No, thanks"
msgstr "Nee, bedankt"

#: inc/admin/ui/notices.php:562
msgid "Thank you!"
msgstr "Bedankt!"

#: inc/admin/ui/notices.php:567
msgid "WP Rocket now collects these metrics from your website:"
msgstr "WP Rocket verzamelt nu deze gegevens van je website:"

#. translators: %s = plugin name.
#: inc/admin/ui/notices.php:605
msgid "%s: Cache cleared."
msgstr "%s: cache geleegd."

#. translators: %s = plugin name.
#: inc/admin/ui/notices.php:612
msgid "%s: Post cache cleared."
msgstr "%s: berichtencache geleegd."

#. translators: %s = plugin name.
#: inc/admin/ui/notices.php:619
msgid "%s: Term cache cleared."
msgstr "%s: term-cache geleegd."

#. translators: %s = plugin name).
#: inc/admin/ui/notices.php:626
msgid "%s: User cache cleared."
msgstr "%s: gebruikerscache geleegd."

#: inc/admin/ui/notices.php:662 inc/Engine/License/views/promo-banner.php:36
#: inc/Engine/License/views/renewal-expired-banner-ocd-disabled.php:32
#: inc/Engine/License/views/renewal-expired-banner-ocd.php:42
#: inc/Engine/License/views/renewal-expired-banner.php:30
#: views/settings/page-sections/dashboard.php:46
msgid "Dismiss this notice"
msgstr "Negeer deze melding"

#: inc/admin/ui/notices.php:682 inc/Engine/Saas/Admin/AdminBar.php:80
#: inc/Engine/Saas/Admin/AdminBar.php:198
msgid "Clear Used CSS"
msgstr "Gebruikte CSS legen"

#: inc/admin/ui/notices.php:685
msgid "Stop Preload"
msgstr "Stop preloaden"

#: inc/admin/ui/notices.php:691
msgid "Turn on Remove Unused CSS"
msgstr "Verwijder ongebruikte CSS inschakelen"

#: inc/admin/ui/notices.php:697
msgid "Enable “Separate Cache Files for Mobile Devices” now"
msgstr "Schakel nu \"Cache-bestanden voor mobiele apparaten scheiden\" in"

#: inc/admin/ui/notices.php:715
msgid "Force deactivation "
msgstr "Deactivatie forceren"

#: inc/admin/ui/notices.php:745
msgid "The following code should have been written to this file:"
msgstr "De volgende code moest naar dit bestand worden geschreven:"

#. translators: %s = plugin name.
#: inc/admin/ui/notices.php:776
msgid "%s cannot configure itself due to missing writing permissions."
msgstr "%s kan zichzelf door ontbrekende schrijfrechten niet configureren."

#. translators: %s = file/folder name
#: inc/admin/ui/notices.php:782
msgid "Affected file/folder: %s"
msgstr "Betreffend(e) bestand/map: %s"

#: inc/classes/admin/class-logs.php:124
msgid "The debug file could not be deleted."
msgstr "Het debug-bestand kan niet verwijderd worden."

#. Translators: %1$s = Plugin name, %2$s = Plugin version.
#: inc/classes/class-wp-rocket-requirements-check.php:147
msgid "To function properly, %1$s %2$s requires at least:"
msgstr "Om goed te kunnen functioneren. %1$s %2$s vereist tenminste:"

#. Translators: %1$s = PHP version required.
#: inc/classes/class-wp-rocket-requirements-check.php:151
msgid ""
"PHP %1$s. To use this WP Rocket version, please ask your web host how to "
"upgrade your server to PHP %1$s or higher."
msgstr ""
"PHP %1$s. Als je deze WP Rocket versie wilt gebruiken, vraag dan je webhost "
"hoe je je server kunt upgraden naar PHP %1$s of hoger."

#. Translators: %1$s = WordPress version required.
#: inc/classes/class-wp-rocket-requirements-check.php:156
msgid ""
"WordPress %1$s. To use this WP Rocket version, please upgrade WordPress to "
"version %1$s or higher."
msgstr ""
"WordPress %1$s. Om deze versie van WP Rocket te gebruiken, raden wij je aan "
"om WordPress te updaten naar versie %1$s of hoger."

#: inc/classes/class-wp-rocket-requirements-check.php:159
msgid ""
"If you are not able to upgrade, you can rollback to the previous version by "
"using the button below."
msgstr ""
"Als je niet kan upgraden, kan je teruggaan naar de vorige versie door op de "
"knop hieronder te klikken."

#. Translators: %s = Previous plugin version.
#: inc/classes/class-wp-rocket-requirements-check.php:161
#: inc/deprecated/deprecated.php:1947
msgid "Re-install version %s"
msgstr "Versie %s opnieuw installeren"

#. translators: %s is the plugin name.
#: inc/classes/class-wp-rocket-requirements-check.php:203
#: inc/classes/class-wp-rocket-requirements-check.php:214
#: inc/deprecated/3.11.php:236 inc/deprecated/3.11.php:251
#: inc/Engine/Plugin/UpdaterSubscriber.php:483
#: inc/Engine/Plugin/UpdaterSubscriber.php:497
msgid "%s Update Rollback"
msgstr "%s-update terugdraaien"

#. translators: %1$s = missing tags;
#: inc/classes/subscriber/Tools/class-detect-missing-tags-subscriber.php:151
msgid ""
"Failed to detect the following requirement in your theme: closing %1$s."
msgid_plural ""
"Failed to detect the following requirements in your theme: closing %1$s."
msgstr[0] ""
"De volgende vereiste in je thema is niet gedetecteerd: sluiten %1$s."
msgstr[1] ""
"De volgende vereisten in je thema zijn niet gedetecteerd: sluiting %1$s."

#. translators: Documentation exists in EN, FR; use localized URL if
#. applicable.
#: inc/classes/subscriber/Tools/class-detect-missing-tags-subscriber.php:159
msgid ""
"https://docs.wp-rocket.me/article/99-pages-not-cached-or-minify-cssjs-not-"
"working/?utm_source=wp_plugin&utm_medium=wp_rocket#theme"
msgstr ""
"https://docs.wp-rocket.me/article/99-pages-not-cached-or-minify-cssjs-not-"
"working/?utm_source=wp_plugin&utm_medium=wp_rocket#theme"

#: inc/common/admin-bar.php:91 inc/common/admin-bar.php:154
#: views/settings/page-sections/dashboard.php:150
msgid "Clear and preload cache"
msgstr "Cache legen en preloaden"

#: inc/common/admin-bar.php:131 inc/functions/i18n.php:20
msgid "All languages"
msgstr "Alle talen"

#: inc/common/admin-bar.php:178
msgid "Clear this post"
msgstr "Dit bericht legen"

#: inc/common/admin-bar.php:192
msgid "Purge this URL"
msgstr "Deze URL legen"

#: inc/common/admin-bar.php:212
msgid "Purge Sucuri cache"
msgstr "Sucuri cache legen"

#: inc/common/admin-bar.php:236 views/settings/fields/rocket-cdn.php:55
msgid "Purge RocketCDN cache"
msgstr "RocketCDN cache legen"

#: inc/common/admin-bar.php:249 views/settings/partials/documentation.php:14
msgid "Documentation"
msgstr "Documentatie"

#: inc/deprecated/3.2.php:52 views/settings/page-sections/imagify.php:36
msgid "Activate Imagify"
msgstr "Imagify activeren"

#: inc/deprecated/3.2.php:52
msgid "Install Imagify for Free"
msgstr "Imagify gratis installeren"

#: inc/deprecated/3.2.php:67
msgid ""
"Speed up your website and boost your SEO by reducing image file sizes "
"without losing quality with Imagify."
msgstr ""
"Versnel je website en boost je SEO door het verkleinen van groottes van "
"afbeeldingsbestanden zonder de kwaliteit te verliezen met Imagify."

#: inc/deprecated/3.2.php:72
msgid "More details"
msgstr "Meer details"

#. translators: %d is the number of pages preloaded.
#: inc/deprecated/3.2.php:228
msgid "Sitemap preload: %d pages have been cached."
msgstr "Preloaden sitemap: %d pagina's zijn gecachet."

#. translators: %d = Number of pages preloaded.
#: inc/deprecated/3.2.php:261
msgid ""
"Sitemap preload: %d uncached pages have now been preloaded. (refresh to see "
"progress)"
msgstr ""
"Preloaden sitemap: %d niet-gecachete pagina's zijn nu gepreload. (Vernieuw "
"om voortgang te bekijken)"

#. translators: %s is an URL.
#: inc/deprecated/3.3.php:761 inc/deprecated/3.3.php:770
#: inc/Engine/Plugin/UpdaterApiTools.php:32
msgid ""
"An unexpected error occurred. Something may be wrong with WP-Rocket.me or "
"this server&#8217;s configuration. If you continue to have problems, <a "
"href=\"%s\">contact support</a>."
msgstr ""
"Er deed zich een onverwachte fout voor. Er kan iets mis zijn met WP-"
"Rocket.me of de configuratie van deze server. Als je deze problemen blijft "
"houden, <a href=\"%s\">neem dan contact op met onze ondersteuning</a>."

#: inc/deprecated/3.4.php:18
msgid "Choose a domain from the list"
msgstr "Kies een domein uit de lijst"

#: inc/deprecated/3.4.php:31
msgid "No domain available in your Cloudflare account"
msgstr "Geen domein beschikbaar in je Cloudflare account"

#: inc/deprecated/3.5.php:71 inc/deprecated/3.5.php:195
msgid ""
"Curl is disabled on your server. Please ask your host to enable it. This is "
"required for the Cloudflare Add-on to work correctly."
msgstr ""
"Curl is uitgeschakeld op je server. Vraag je host om het in te schakelen. "
"Dit is nodig om de Cloudflare add-on correct te laten werken."

#. translators: %1$s = opening link; %2$s = closing link
#: inc/deprecated/3.5.php:79
msgid ""
"Cloudflare email, API key and Zone ID are not set. Read the "
"%1$sdocumentation%2$s for further guidance."
msgstr ""
"Cloudflare e-mail, API-sleutel en Zone-ID zijn niet ingesteld. Lees de "
"%1$sdocumentatie%2$s voor meer informatie."

#. translators: %1$s = opening link; %2$s = closing link
#: inc/deprecated/3.5.php:206
msgid ""
"Cloudflare email and API key are not set. Read the %1$sdocumentation%2$s for"
" further guidance."
msgstr ""
"Cloudflare e-mail en API-sleutel zijn niet ingesteld. Lees de "
"%1$sdocumentatie%2$s voor meer informatie."

#: inc/deprecated/3.5.php:271
msgid "Connection to Cloudflare failed"
msgstr "Verbinding naar Cloudflare mislukt"

#. translators: %s = CloudFare API return message.
#: inc/deprecated/3.5.php:587
msgid "<strong>WP Rocket:</strong> %s"
msgstr "<strong>WP Rocket:</strong> %s"

#: inc/deprecated/3.5.php:592
msgid "<strong>WP Rocket:</strong> Cloudflare cache successfully purged."
msgstr "<strong>WP Rocket:</strong> Cloudflare cache succesvol geleegd."

#: inc/deprecated/3.5.php:858 inc/Engine/HealthCheck/HealthCheck.php:81
msgid ""
"The following scheduled event failed to run. This may indicate the CRON "
"system is not running properly, which can prevent some WP Rocket features "
"from working as intended:"
msgid_plural ""
"The following scheduled events failed to run. This may indicate the CRON "
"system is not running properly, which can prevent some WP Rocket features "
"from working as intended:"
msgstr[0] ""
"De volgende geplande gebeurtenis mislukt. Dit kan erop wijzen dat het CRON-"
"systeem niet goed werkt, waardoor sommige functies van WP Rocket niet werken"
" zoals bedoeld:"
msgstr[1] ""
"De volgende geplande gebeurtenissen zijn niet uitgevoerd. Dit kan erop "
"wijzen dat het CRON-systeem niet goed werkt, waardoor sommige functies van "
"WP Rocket niet werken zoals bedoeld:"

#: inc/deprecated/3.5.php:867 inc/Engine/HealthCheck/HealthCheck.php:88
msgid "Please contact your host to check if CRON is working."
msgstr "Neem contact op met je host om te controleren of CRON werkt."

#: inc/deprecated/3.10.php:163
msgid "OPcache purge failed."
msgstr "OPcache legen mislukt."

#: inc/deprecated/3.10.php:168
msgid "OPcache successfully purged"
msgstr "OPcache succesvol geleegd"

#: inc/deprecated/3.11.php:81
msgid "Yoast SEO XML sitemap"
msgstr "Yoast SEO XML-sitemap"

#. translators: %s = Name of the plugin.
#. translators: %s = plugin name, e.g. Yoast SEO.
#: inc/deprecated/3.11.php:83 inc/deprecated/3.12.php:58
#: inc/deprecated/3.12.php:87 inc/deprecated/3.12.php:186
#: inc/deprecated/3.12.php:253 inc/deprecated/3.12.php:342
msgid ""
"We automatically detected the sitemap generated by the %s plugin. You can "
"check the option to preload it."
msgstr ""
"We hebben de gegeneerde sitemap door de %s-plugin automatisch gedetecteerd. "
"Je kunt de optie om het te preloaden aanvinken."

#. translators: 1 and 3 are link openings, 2 is a link closing.
#: inc/deprecated/3.11.php:279 inc/Engine/Plugin/UpdaterSubscriber.php:520
msgid "%1$sReturn to WP Rocket%2$s or %3$sgo to Plugins page%2$s"
msgstr ""
"%1$sKeer terug naar WP Rocket%2$s of %3$sga naar de Plugins pagina%2$s"

#: inc/deprecated/3.12.php:56
msgid "All in One SEO XML sitemap"
msgstr "All in One SEO XML-sitemap"

#: inc/deprecated/3.12.php:85
msgid "Rank Math XML sitemap"
msgstr "Rank Math XML sitemap"

#: inc/deprecated/3.12.php:184
msgid "SEOPress XML sitemap"
msgstr "SEOPress XML sitemap"

#: inc/deprecated/3.12.php:251
msgid "The SEO Framework XML sitemap"
msgstr "De SEO Framework XML sitemap"

#: inc/deprecated/3.12.php:332
msgid "Jetpack XML Sitemaps"
msgstr "Jetpack XML Sitemaps"

#: inc/deprecated/3.12.php:334
msgid "Preload the sitemap from the Jetpack plugin"
msgstr "De sitemap van de Jetpack plugin preloaden"

#: inc/deprecated/3.15.php:28
#: inc/Engine/Admin/Metaboxes/PostEditOptionsSubscriber.php:74
msgid "WP Rocket Options"
msgstr "WP Rocket opties"

#: inc/deprecated/3.15.php:57 views/metaboxes/post_edit_options.php:19
msgid "Never cache this page"
msgstr "Deze pagina nooit cachen"

#: inc/deprecated/3.15.php:61 views/metaboxes/post_edit_options.php:23
msgid "Activate these options on this post:"
msgstr "Deze opties voor dit bericht activeren:"

#. translators: %s is the name of the option.
#: inc/deprecated/3.15.php:81
#: inc/Engine/Admin/Metaboxes/PostEditOptionsSubscriber.php:121
msgid "Activate first the %s option."
msgstr "Activeer eerst de %s-optie."

#. translators: %1$s = opening strong tag, %2$s = closing strong tag.
#: inc/deprecated/3.15.php:97 views/metaboxes/post_edit_options.php:38
msgid ""
"%1$sNote:%2$s None of these options will be applied if this post has been "
"excluded from cache in the global cache settings."
msgstr ""
"%1$sOpmerking:%2$s geen van deze opties worden toegepast op het moment dat "
"dit bericht is uitgesloten van caching in de globale instellingen."

#: inc/deprecated/deprecated.php:240
msgid "<strong>JS</strong> files with Deferred Loading JavaScript"
msgstr "<strong>JS</strong>-bestanden met uitgesteld laden-JavaScript"

#: inc/deprecated/deprecated.php:293
msgid "Add URL"
msgstr "URL toevoegen"

#: inc/deprecated/deprecated.php:1245
#: inc/Engine/Admin/Settings/Settings.php:451
msgid "Settings saved."
msgstr "Instellingen opgeslagen."

#: inc/deprecated/deprecated.php:1277 views/settings/fields/import-form.php:22
msgid ""
"Before you can upload your import file, you will need to fix the following "
"error:"
msgstr ""
"Voordat je je importbestand kunt uploaden, moet je de volgende fout "
"oplossen:"

#. translators: %s is the maximum upload size set on the current server.
#: inc/deprecated/deprecated.php:1288 views/settings/fields/import-form.php:35
msgid "Choose a file from your computer (maximum size: %s)"
msgstr "Kies een bestand van je computer (maximale grootte: %s)"

#: inc/deprecated/deprecated.php:1294 inc/Engine/Admin/Settings/Render.php:422
msgid "Upload file and import settings"
msgstr "Bestand uploaden en instellingen importeren"

#: inc/deprecated/deprecated.php:1385
msgid "Your Cloudflare credentials are valid."
msgstr "Je Cloudflare referenties zijn geldig."

#: inc/deprecated/deprecated.php:1389
msgid "Your Cloudflare credentials are invalid!"
msgstr "Je Cloudflare referenties zijn ongeldig!"

#: inc/deprecated/deprecated.php:1453
msgid "Save and optimize"
msgstr "Opslaan en optimaliseren"

#: inc/deprecated/deprecated.php:1453
msgid "Optimize"
msgstr "Optimaliseren"

#: inc/deprecated/deprecated.php:1464 inc/deprecated/deprecated.php:1666
msgctxt "screen-reader-text"
msgid "Note:"
msgstr "Opmerking:"

#: inc/deprecated/deprecated.php:1470
msgctxt "screen-reader-text"
msgid "Performance tip:"
msgstr "Prestatietip:"

#: inc/deprecated/deprecated.php:1476
msgctxt "screen-reader-text"
msgid "Third-party feature detected:"
msgstr "Functie van derde partij gedetecteerd:"

#: inc/deprecated/deprecated.php:1488 inc/deprecated/deprecated.php:1672
msgctxt "screen-reader-text"
msgid "Warning:"
msgstr "Waarschuwing:"

#: inc/deprecated/deprecated.php:1501
msgctxt "button text"
msgid "Download settings"
msgstr "Instellingen downloaden"

#: inc/deprecated/deprecated.php:1531
msgid "Replace site's hostname with:"
msgstr "Hostnaam site vervangen door:"

#: inc/deprecated/deprecated.php:1550 inc/deprecated/deprecated.php:1587
#: inc/deprecated/deprecated.php:1615 views/settings/fields/cnames.php:41
#: views/settings/fields/cnames.php:75 views/settings/fields/cnames.php:105
msgid "reserved for"
msgstr "voorbehouden aan"

#: inc/deprecated/deprecated.php:1552 inc/deprecated/deprecated.php:1589
#: inc/deprecated/deprecated.php:1617 views/settings/fields/cnames.php:44
#: views/settings/fields/cnames.php:78 views/settings/fields/cnames.php:108
msgid "All files"
msgstr "Alle bestanden"

#: inc/deprecated/deprecated.php:1564 inc/deprecated/deprecated.php:1594
#: inc/deprecated/deprecated.php:1622 views/settings/fields/cnames.php:56
#: views/settings/fields/cnames.php:90 views/settings/fields/cnames.php:120
msgid "Images"
msgstr "Afbeeldingen"

#: inc/deprecated/deprecated.php:1635 views/settings/fields/cnames.php:132
msgid "Add CNAME"
msgstr "CNAME toevoegen"

#: inc/deprecated/deprecated.php:1706
msgid "Watch the video"
msgstr "Bekijk de video"

#: inc/deprecated/deprecated.php:1771
msgid "Basic"
msgstr "Basis"

#: inc/deprecated/deprecated.php:1772
msgid "Static Files"
msgstr "Statische bestanden"

#: inc/deprecated/deprecated.php:1773 inc/Engine/Admin/Settings/Page.php:1500
#: inc/Engine/Admin/Settings/Page.php:1511
#: inc/Engine/CDN/Admin/Subscriber.php:28
msgid "CDN"
msgstr "CDN"

#: inc/deprecated/deprecated.php:1774
msgid "Advanced"
msgstr "Geavanceerd"

#: inc/deprecated/deprecated.php:1775 inc/Engine/Admin/Settings/Page.php:1345
msgid "Database"
msgstr "Database"

#: inc/deprecated/deprecated.php:1776 inc/Engine/Admin/Settings/Page.php:1051
msgid "Preload"
msgstr "Preloaden"

#: inc/deprecated/deprecated.php:1786
#: inc/Engine/Admin/Settings/Subscriber.php:170
#: views/settings/page-sections/tools.php:33
msgid "Tools"
msgstr "Tools"

#: inc/deprecated/deprecated.php:1789 inc/Engine/Admin/Settings/Page.php:357
#: views/settings/page-sections/dashboard.php:80
msgid "License"
msgstr "Licentie"

#. Translators: %1$s = Plugin name, %2$s = Plugin version, %3$s = PHP version
#. required.
#: inc/deprecated/deprecated.php:1944
msgid ""
"%1$s %2$s requires at least PHP %3$s to function properly. To use this "
"version, please ask your web host how to upgrade your server to PHP %3$s or "
"higher. If you are not able to upgrade, you can rollback to the previous "
"version by using the button below."
msgstr ""
"%1$s %2$s vereist minstens PHP %3$s om juist te functioneren. Om deze versie"
" te gebruiken, moet je je webhost vragen hoe je je server naar PHP %3$s of "
"hoger kunt upgraden. Als je niet kunt upgraden, kun je naar de vorige versie"
" terugdraaien door de knop hieronder te gebruiken."

#. translators: 1: PHP class name, 2: version number, 3: replacement class
#. name.
#: inc/deprecated/DeprecatedClassTrait.php:54
msgid ""
"The called class %1$s is <strong>deprecated</strong> since version %2$s! Use"
" %3$s instead."
msgstr ""
"De aangeroepen klasse %1$s is <strong>verouderd</strong> sinds versie %2$s! "
"Gebruik in plaats daarvan %3$s."

#. translators: 1: PHP class name, 2: version number.
#: inc/deprecated/DeprecatedClassTrait.php:65
msgid ""
"The called class %1$s is <strong>deprecated</strong> since version %2$s!"
msgstr ""
"De aangeroepen klasse %1$s is <strong>vervallen</strong> sinds versie %2$s!"

#: inc/deprecated/Engine/Addon/FacebookTracking/Subscriber.php:92
#: inc/deprecated/Engine/Addon/GoogleTracking/Subscriber.php:137
#: inc/Engine/Admin/Database/Subscriber.php:79
msgid "weekly"
msgstr "wekelijks"

#: inc/Engine/Admin/Database/Optimization.php:30
#: inc/Engine/Admin/Settings/Page.php:1389
msgid "Revisions"
msgstr "Revisies"

#: inc/Engine/Admin/Database/Optimization.php:31
#: inc/Engine/Admin/Settings/Page.php:1399
msgid "Auto Drafts"
msgstr "Automatische concepten"

#: inc/Engine/Admin/Database/Optimization.php:32
#: inc/Engine/Admin/Settings/Page.php:1409
msgid "Trashed Posts"
msgstr "Verwijderde berichten"

#: inc/Engine/Admin/Database/Optimization.php:33
#: inc/Engine/Admin/Settings/Page.php:1419
msgid "Spam Comments"
msgstr "Spam reacties"

#: inc/Engine/Admin/Database/Optimization.php:34
#: inc/Engine/Admin/Settings/Page.php:1429
msgid "Trashed Comments"
msgstr "Verwijderde reacties"

#: inc/Engine/Admin/Database/Optimization.php:35
msgid "Transients"
msgstr "Transients"

#: inc/Engine/Admin/Database/Optimization.php:36
msgid "Tables"
msgstr "Tabellen"

#: inc/Engine/Admin/Database/Subscriber.php:85
msgid "monthly"
msgstr "maandelijks"

#: inc/Engine/Admin/Database/Subscriber.php:194
msgid "Database optimization process is running"
msgstr "Proces voor optimalisatie database draait"

#: inc/Engine/Admin/Database/Subscriber.php:224
msgid ""
"Database optimization process is complete. Everything was already optimized!"
msgstr ""
"Proces voor optimalisatie database is voltooid. Alles was al "
"geoptimaliseerd!"

#: inc/Engine/Admin/Database/Subscriber.php:227
msgid ""
"Database optimization process is complete. List of optimized items below:"
msgstr ""
"Proces voor optimalisatie database is voltooid. Lijst van geoptimaliseerde "
"items hieronder:"

#. translators: %1$d = number of items optimized, %2$s = type of optimization
#: inc/Engine/Admin/Database/Subscriber.php:235
msgid "%1$d %2$s optimized."
msgstr "%1$d %2$s geoptimaliseerd."

#. translators: %1$s = <strong>, %2$s = </strong>, %3$s = <a>, %4$s = </a>.
#: inc/Engine/Admin/DomainChange/Subscriber.php:143
msgid ""
"%1$sWP Rocket:%2$s We detected that the website domain has changed. The "
"configuration files must be regenerated for the page cache and all other "
"optimizations to work as intended. %3$sLearn More%4$s"
msgstr ""
"%1$sWP Rocket:%2$s we hebben gedetecteerd dat het domein van de site is "
"gewijzigd. De configuratiebestanden moeten opnieuw worden aangemaakt om de "
"paginacache en alle andere optimalisaties te laten werken zoals bedoeld. "
"%3$sLeer meer%4$s"

#: inc/Engine/Admin/DomainChange/Subscriber.php:171
msgid "Regenerate WP Rocket configuration files now"
msgstr "WP Rocket configuratiebestanden nu opnieuw genereren"

#: inc/Engine/Admin/Settings/Page.php:223
msgid "Save Changes"
msgstr "Wijzigingen opslaan"

#: inc/Engine/Admin/Settings/Page.php:223
msgid "Validate License"
msgstr "Valideer licentie"

#: inc/Engine/Admin/Settings/Page.php:279
#: inc/Engine/Admin/Settings/Page.php:280 inc/functions/admin.php:550
msgid "Unavailable"
msgstr "Niet beschikbaar"

#: inc/Engine/Admin/Settings/Page.php:374
msgid "API key"
msgstr "API-sleutel"

#: inc/Engine/Admin/Settings/Page.php:389
msgid "Email address"
msgstr "E-mailadres"

#: inc/Engine/Admin/Settings/Page.php:415
msgid "Dashboard"
msgstr "Dashboard"

#: inc/Engine/Admin/Settings/Page.php:416
msgid "Get help, account info"
msgstr "Krijg hulp, accountinformatie"

#: inc/Engine/Admin/Settings/Page.php:425
msgid "My Status"
msgstr "Mijn status"

#: inc/Engine/Admin/Settings/Page.php:435 views/settings/page.php:75
msgid "Rocket Analytics"
msgstr "Rocket Analytics"

#. translators: %1$s = opening <a> tag, %2$s = closing </a> tag.
#: inc/Engine/Admin/Settings/Page.php:437
msgid ""
"I agree to share anonymous data with the development team to help improve WP"
" Rocket. %1$sWhat info will we collect?%2$s"
msgstr ""
"Ik ga ermee akkoord dat WP Rocket anoniem data met het development team "
"deelt ter verbetering van de plug-in. %1$sWelke informatie verzamelt de "
"plug-in? %2$s"

#: inc/Engine/Admin/Settings/Page.php:489
msgid "File Optimization"
msgstr "Bestandsoptimalisatie"

#: inc/Engine/Admin/Settings/Page.php:490
msgid "Optimize CSS & JS"
msgstr "CSS & JS optimaliseren"

#. translators: %1$s = type of minification (HTML, CSS or JS), %2$s = “WP
#. Rocket”.
#: inc/Engine/Admin/Settings/Page.php:498
msgid ""
"%1$s Minification is currently activated in <strong>Autoptimize</strong>. If"
" you want to use %2$s’s minification, disable this option in Autoptimize."
msgstr ""
"%1$s Verkleinen is momenteel geactiveerd in <strong>Autoptimize</strong>. "
"Als je %2$s's verkleinen wilt gebruiken, schakel deze optie dan uit in "
"Autoptimaliseren."

#: inc/Engine/Admin/Settings/Page.php:508
msgid "CSS Files"
msgstr "CSS-bestanden"

#: inc/Engine/Admin/Settings/Page.php:517
msgid "JavaScript Files"
msgstr "JavaScript-bestanden"

#. translators: %1$s = type of minification (HTML, CSS or JS), %2$s = “WP
#. Rocket”.
#: inc/Engine/Admin/Settings/Page.php:524
msgid ""
"%1$s Minification is currently activated in <strong>Autoptimize</strong>. If"
" you want to use %2$s’s minification, disable those options in Autoptimize."
msgstr ""
"%1$s Verkleinen is momenteel geactiveerd in <strong>Autoptimize</strong>. "
"Als je %2$s's verkleinen wilt gebruiken, schakel deze opties dan uit in "
"Autoptimaliseren."

#: inc/Engine/Admin/Settings/Page.php:529
msgid ""
"If you have problems after activating this option, copy and paste the "
"default exclusions to quickly resolve issues:"
msgstr ""
"Als je problemen ondervindt na het activeren van deze optie, kopieer en plak"
" dan de standaard uitsluitingen om problemen snel op te lossen:"

#. translators: %1$s = opening </a> tag, %2$s = closing </a> tag.
#: inc/Engine/Admin/Settings/Page.php:533
msgid ""
"Also, please check our %1$sdocumentation%2$s for a list of compatibility "
"exclusions."
msgstr ""
"Raadpleeg ook onze %1$sdocumentatie%2$s voor een lijst met "
"compatibiliteitsuitsluitingen."

#: inc/Engine/Admin/Settings/Page.php:538
msgid ""
"Internal scripts are excluded by default to prevent issues. Remove them to "
"take full advantage of this option."
msgstr ""
"Interne scripts worden standaard uitgesloten om problemen te voorkomen. "
"Verwijder ze om deze optie volledig te benutten."

#. translators: %1$s = opening </a> tag, %2$s = closing </a> tag.
#: inc/Engine/Admin/Settings/Page.php:541
msgid ""
"If this causes trouble, restore the default exclusions, found %1$shere%2$s"
msgstr ""
"Als dit problemen veroorzaakt, herstel dan de standaard uitsluitingen, te "
"vinden %1$shier%2$s"

#: inc/Engine/Admin/Settings/Page.php:550
msgid "Minify CSS files"
msgstr "CSS-bestanden verkleinen"

#: inc/Engine/Admin/Settings/Page.php:551
msgid "Minify CSS removes whitespace and comments to reduce the file size."
msgstr ""
"CSS verkleinen verwijdert witruimte en reacties om de bestandsgrootte te "
"verminderen."

#: inc/Engine/Admin/Settings/Page.php:564
#: inc/Engine/Admin/Settings/Page.php:628
#: inc/Engine/Admin/Settings/Page.php:694
#: inc/Engine/Admin/Settings/Page.php:718
msgid "This could break things!"
msgstr "Dit kan dingen breken!"

#: inc/Engine/Admin/Settings/Page.php:565
#: inc/Engine/Admin/Settings/Page.php:629
#: inc/Engine/Admin/Settings/Page.php:695
#: inc/Engine/Admin/Settings/Page.php:719
msgid ""
"If you notice any errors on your website after having activated this "
"setting, just deactivate it again, and your site will be back to normal."
msgstr ""
"Als je fouten op je website opmerkt na het activeren van deze instelling, "
"deactiveer het dan gewoon en je site zal weer als normaal zijn."

#: inc/Engine/Admin/Settings/Page.php:566
msgid "Activate minify CSS"
msgstr "CSS verkleinen activeren"

#: inc/Engine/Admin/Settings/Page.php:572
msgid ""
"Specify URLs of CSS files to be excluded from minification (one per line)."
msgstr ""
"Geef URL's op van CSS-bestanden die moeten worden uitgesloten van verkleinen"
" (één per regel)."

#: inc/Engine/Admin/Settings/Page.php:573
msgid ""
"<strong>Internal:</strong> The domain part of the URL will be stripped "
"automatically. Use (.*).css wildcards to exclude all CSS files located at a "
"specific path."
msgstr ""
"<strong>Intern:</strong> het domeindeel van de URL wordt automatisch "
"gestript. Gebruik (.*).css wildcards om alle CSS-bestanden op een specifiek "
"pad uit te sluiten."

#. translators: %1$s = opening <a> tag, %2$s = closing </a> tag.
#: inc/Engine/Admin/Settings/Page.php:575
msgid ""
"<strong>3rd Party:</strong> Use either the full URL path or only the domain "
"name, to exclude external CSS. %1$sMore info%2$s"
msgstr ""
"<strong>3e partij:</strong> gebruik het volledige URL-pad of alleen de "
"domeinnaam om externe CSS uit te sluiten. %1$sMeer informatie%2$s"

#: inc/Engine/Admin/Settings/Page.php:588
#: inc/Engine/Admin/Settings/Page.php:610
msgid "Optimize CSS delivery"
msgstr "CSS-levering optimaliseren"

#: inc/Engine/Admin/Settings/Page.php:593
msgid ""
"Optimize CSS delivery eliminates render-blocking CSS on your website. Only "
"one method can be selected. Remove Unused CSS is recommended for optimal "
"performance, but limited only to the users with active license."
msgstr ""
"CSS-levering optimaliseren elimineert render-blocking CSS op je site. Er kan"
" maar één methode worden geselecteerd. Ongebruikte CSS verwijderen wordt "
"aanbevolen voor optimale prestaties, maar is beperkt tot gebruikers met een "
"actieve licentie."

#: inc/Engine/Admin/Settings/Page.php:593
msgid ""
"Optimize CSS delivery eliminates render-blocking CSS on your website. Only "
"one method can be selected. Remove Unused CSS is recommended for optimal "
"performance."
msgstr ""
"CSS-levering optimaliseren elimineert render-blocking CSS op je site. Er kan"
" maar één methode worden geselecteerd. Ongebruikte CSS verwijderen wordt "
"aanbevolen voor optimale prestaties."

#. translators: %1$s = opening <a> tag, %2$s = closing </a> tag.
#: inc/Engine/Admin/Settings/Page.php:603
msgid ""
"Optimize CSS Delivery features are disabled on local environments. %1$sLearn"
" more%2$s"
msgstr ""
"CSS-leverfuncties optimaliseren zijn uitgeschakeld op lokale omgevingen. "
"%1$sLeer meer%2$s"

#: inc/Engine/Admin/Settings/Page.php:623
#: inc/Engine/Optimization/RUCSS/Admin/OptionSubscriber.php:74
msgid "Remove Unused CSS"
msgstr "Ongebruikte CSS verwijderen"

#. translators: %1$s = opening <a> tag, %2$s = closing </a> tag.
#: inc/Engine/Admin/Settings/Page.php:626
msgid ""
"Removes unused CSS per page and helps to reduce page size and HTTP requests."
" Recommended for best performance. Test thoroughly! %1$sMore info%2$s"
msgstr ""
"Verwijdert ongebruikte CSS per pagina en helpt de paginagrootte en HTTP-"
"verzoeken te verminderen. Aanbevolen voor de beste prestaties. Test grondig!"
" %1$sMeer informatie%2$s"

#: inc/Engine/Admin/Settings/Page.php:630
msgid "Activate Remove Unused CSS"
msgstr "Verwijder ongebruikte CSS activeren"

#: inc/Engine/Admin/Settings/Page.php:636
msgid "CSS safelist"
msgstr "CSS veilge lijst"

#: inc/Engine/Admin/Settings/Page.php:637
msgid ""
"Specify CSS filenames, IDs or classes that should not be removed (one per "
"line)."
msgstr ""
"Geef CSS-bestandsnamen, ID's of klassen op die niet verwijderd mogen worden "
"(één per regel)."

#: inc/Engine/Admin/Settings/Page.php:652
#: inc/Engine/CriticalPath/Admin/Subscriber.php:200
msgid "Load CSS asynchronously"
msgstr "CSS asynchroon laden"

#. translators: %1$s = plugin name.
#: inc/Engine/Admin/Settings/Page.php:655
msgctxt "WP Critical CSS compatibility"
msgid ""
"Load CSS asynchronously is currently handled by the %1$s plugin. If you want"
" to use WP Rocket’s load CSS asynchronously option, disable the %1$s plugin."
msgstr ""
"CSS asynchroon laden wordt momenteel afgehandeld door de %1$s plugin. Als je"
" de optie CSS asynchroon laden van WP Rocket wilt gebruiken, schakel dan de "
"%1$s plugin uit."

#. translators: %1$s = opening <a> tag, %2$s = closing </a> tag.
#: inc/Engine/Admin/Settings/Page.php:657
msgid ""
"Generates critical path CSS and loads CSS asynchronously. %1$sMore info%2$s"
msgstr "Genereert kritieke pad CSS en laadt CSS asynchroon. %1$sMeer info%2$s"

#: inc/Engine/Admin/Settings/Page.php:663
msgid "Fallback critical CSS"
msgstr "Terugvallen op kritieke CSS"

#. translators: %1$s = opening <a> tag, %2$s = closing </a> tag.
#: inc/Engine/Admin/Settings/Page.php:665
msgid ""
"Provides a fallback if auto-generated critical path CSS is incomplete. "
"%1$sMore info%2$s"
msgstr ""
"Biedt een vangnet als auto-gegenereerde kritieke pad CSS incompleet is. "
"%1$sMeer info%2$s"

#: inc/Engine/Admin/Settings/Page.php:680
msgid "Minify JavaScript files"
msgstr "JavaScript-bestanden verkleinen"

#: inc/Engine/Admin/Settings/Page.php:681
msgid ""
"Minify JavaScript removes whitespace and comments to reduce the file size."
msgstr ""
"JavaScript verkleinen verwijderd witruimte en reacties om zo de "
"bestandsgrootte te verlagen."

#: inc/Engine/Admin/Settings/Page.php:696
msgid "Activate minify JavaScript"
msgstr "Activeer het verkleinen van JavaScript"

#: inc/Engine/Admin/Settings/Page.php:701
msgid ""
"Combine JavaScript files <em>(Enable Minify JavaScript files to select)</em>"
msgstr ""
"Combineer JavaScript-bestanden <em>(Activeer JavaScript verkleinen om te "
"selecteren)</em>"

#. translators: %1$s = opening <a> tag, %2$s = closing </a> tag.
#: inc/Engine/Admin/Settings/Page.php:703
msgid ""
"Combine JavaScript files combines your site’s internal, 3rd party and inline"
" JS reducing HTTP requests. Not recommended if your site uses HTTP/2. "
"%1$sMore info%2$s"
msgstr ""
"JavaScript-bestanden combineren, combineert de interne, 3e partij en inline "
"JS van je site waardoor HTTP-verzoeken worden verminderd. Niet aanbevolen "
"als je site HTTP/2 gebruikt. %1$sMeer info%2$s"

#. translators: %1$s = opening <a> tag, %2$s = closing </a> tag.
#: inc/Engine/Admin/Settings/Page.php:704
msgid ""
"For compatibility and best results, this option is disabled when delay "
"javascript execution is enabled."
msgstr ""
"Voor compatibiliteit en de beste resultaten, wordt deze optie uitgeschakeld "
"wanneer JavaScript-uitvoering uitstellen is ingeschakeld."

#: inc/Engine/Admin/Settings/Page.php:720
msgid "Activate combine JavaScript"
msgstr "Activeer het combineren van JavaScript"

#. translators: %1$s = opening <a> tag, %2$s = closing </a> tag.
#: inc/Engine/Admin/Settings/Page.php:727
msgid ""
"Specify patterns of inline JavaScript to be excluded from concatenation (one"
" per line). %1$sMore info%2$s"
msgstr ""
"Specificeer patronen of inline JavaScript de uitgesloten moet worden van "
"aaneenschakeling (één per regel). %1$sMeer info%2$s"

#: inc/Engine/Admin/Settings/Page.php:744
msgid ""
"Specify URLs of JavaScript files to be excluded from minification and "
"concatenation (one per line)."
msgstr ""
"Specificeer URL's of JavaScript-bestanden die uitgesloten moeten worden van "
"verkleinen en aaneenschakeling (één per regel)."

#: inc/Engine/Admin/Settings/Page.php:745
msgid ""
"<strong>Internal:</strong> The domain part of the URL will be stripped "
"automatically. Use (.*).js wildcards to exclude all JS files located at a "
"specific path."
msgstr ""
"<strong>Intern:</strong> het domeindeel van de URL wordt automatisch "
"gestript. Gebruik (.*).js wildcards om alle JS-bestanden op een specifiek "
"pad uit te sluiten."

#. translators: %1$s = opening <a> tag, %2$s = closing </a> tag.
#: inc/Engine/Admin/Settings/Page.php:747
msgid ""
"<strong>3rd Party:</strong> Use either the full URL path or only the domain "
"name, to exclude external JS. %1$sMore info%2$s"
msgstr ""
"<strong>3e partij:</strong> gebruik het volledige URL-pad of alleen de "
"domeinnaam om externe JS uit te sluiten. %1$sMeer informatie%2$s"

#: inc/Engine/Admin/Settings/Page.php:763
#: inc/Engine/Optimization/DeferJS/AdminSubscriber.php:76
msgid "Load JavaScript deferred"
msgstr "JavaScript uitgesteld laden"

#. translators: %1$s = opening <a> tag, %2$s = closing </a> tag.
#: inc/Engine/Admin/Settings/Page.php:765
msgid ""
"Load JavaScript deferred eliminates render-blocking JS on your site and can "
"improve load time. %1$sMore info%2$s"
msgstr ""
"Uitgesteld JavaScript laden elimineert render-blocking JS op je site en kan "
"de laadtijd verbeteren. %1$sMeer info%2$s"

#. translators: %1$s = opening <a> tag, %2$s = closing </a> tag.
#: inc/Engine/Admin/Settings/Page.php:778
msgid ""
"Specify URLs or keywords of JavaScript files to be excluded from defer (one "
"per line). %1$sMore info%2$s"
msgstr ""
"Geef URL's of trefwoorden op van JavaScript-bestanden die moeten worden "
"uitgesloten van uitstellen (één per regel). %1$sMeer info%2$s"

#: inc/Engine/Admin/Settings/Page.php:794
#: inc/Engine/Optimization/DelayJS/Admin/Subscriber.php:210
msgid "Delay JavaScript execution"
msgstr "JavaScript-uitvoering uitstellen"

#. translators: %1$s = opening <a> tag, %2$s = closing </a> tag.
#: inc/Engine/Admin/Settings/Page.php:796
msgid ""
"Improves performance by delaying the loading of JavaScript files until user "
"interaction (e.g. scroll, click). %1$sMore info%2$s"
msgstr ""
"Verbetert de prestaties door het laden van JavaScript-bestanden uit te "
"stellen tot interactie van de gebruiker (bijv. scrollen, klikken). %1$sMeer "
"info%2$s"

#: inc/Engine/Admin/Settings/Page.php:805
msgid "One-click exclusions"
msgstr "Uitsluitingen met één klik"

#: inc/Engine/Admin/Settings/Page.php:806
msgid ""
"When using the Delay JavaScript Execution, you might experience delay "
"loading elements located in the viewport that need to appear immediately - "
"e.g. slider, header, menu."
msgstr ""
"Als je JavaScript-uitvoering uitstellen gebruikt, kun je vertraging oplopen "
"bij het laden van elementen in de viewport die onmiddellijk moeten "
"verschijnen, zoals een schuifbalk, header of menu."

#: inc/Engine/Admin/Settings/Page.php:807
msgid ""
"If you need instant visibility, click below on files that should NOT be "
"delayed. This selection will help users interact with the elements straight "
"away."
msgstr ""
"Als je onmiddellijke zichtbaarheid nodig hebt, klik dan hieronder op "
"bestanden die NIET mogen worden uitgesteld. Met deze selectie kunnen "
"gebruikers meteen met de elementen werken."

#: inc/Engine/Admin/Settings/Page.php:824
msgid ""
"Specify URLs or keywords that can identify inline or JavaScript files to be "
"excluded from delaying execution (one per line)."
msgstr ""
"Geef URL's of trefwoorden op die inline- of JavaScript-bestanden kunnen "
"identificeren die moeten worden uitgesloten van uitvoering uitstellen (één "
"per regel)."

#: inc/Engine/Admin/Settings/Page.php:856
msgid "Media"
msgstr "Media"

#: inc/Engine/Admin/Settings/Page.php:857
msgid "LazyLoad, image dimensions"
msgstr "LazyLoad, afbeeldingsafmetingen"

#: inc/Engine/Admin/Settings/Page.php:866
msgid "Autoptimize"
msgstr "Autoptimize"

#: inc/Engine/Admin/Settings/Page.php:920
msgid "LazyLoad"
msgstr "LazyLoad"

#. translators: %1$s = opening <a> tag, %2$s = closing </a> tag.
#: inc/Engine/Admin/Settings/Page.php:923
msgid ""
"It can improve actual and perceived loading time as images, iframes, and "
"videos will be loaded only as they enter (or about to enter) the viewport "
"and reduces the number of HTTP requests. %1$sMore Info%2$s"
msgstr ""
"Het kan de werkelijke en waargenomen laadtijd verbeteren omdat afbeeldingen,"
" iframes en video's alleen worden geladen als ze de viewport binnenkomen (of"
" bijna binnenkomen) en het aantal HTTP-verzoeken vermindert. %1$sMeer "
"info%2$s"

#. translators: %1$s = “WP Rocket”, %2$s = a list of plugin names.
#: inc/Engine/Admin/Settings/Page.php:930
msgid ""
"LazyLoad is currently activated in %2$s. If you want to use WP Rocket’s "
"LazyLoad, disable this option in %2$s."
msgstr ""
"LazyLoad is op dit moment geactiveerd in %2$s. Als je WP Rocket’s LazyLoad "
"wilt gebruiken, schakel deze optie uit in %2$s."

#: inc/Engine/Admin/Settings/Page.php:933
msgid "Image Dimensions"
msgstr "Afbeelding afmetingen"

#. translators: %1$s = opening <a> tag, %2$s = closing </a> tag.
#: inc/Engine/Admin/Settings/Page.php:936
msgid ""
"Add missing width and height attributes to images. Helps prevent layout "
"shifts and improve the reading experience for your visitors. %1$sMore "
"info%2$s"
msgstr ""
"Voeg ontbrekende breedte- en hoogteattributen toe aan afbeeldingen. Helpt "
"verschuivingen in de lay-out te voorkomen en de leeservaring voor je "
"bezoekers te verbeteren. %1$sMeer info%2$s"

#: inc/Engine/Admin/Settings/Page.php:955
msgid "Enable for images"
msgstr "Voor afbeeldingen inschakelen"

#. translators: %1$s = “WP Rocket”, %2$s = a list of plugin names.
#: inc/Engine/Admin/Settings/Page.php:967
msgid ""
"LazyLoad for images is currently activated in %2$s. If you want to use "
"%1$s’s LazyLoad, disable this option in %2$s."
msgstr ""
"LazyLoad voor afbeeldingen is op dit moment geactiveerd in %2$s. Als je "
"%1$s’s LazyLoad wilt gebruiken, schakel deze optie uit in %2$s."

#: inc/Engine/Admin/Settings/Page.php:975
msgid "Enable for CSS background images"
msgstr "Inschakelen voor CSS achtergrondafbeeldingen"

#: inc/Engine/Admin/Settings/Page.php:990
msgid "Enable for iframes and videos"
msgstr "Voor iframes en video's inschakelen"

#: inc/Engine/Admin/Settings/Page.php:1005
msgid "Replace YouTube iframe with preview image"
msgstr "YouTube iframe met voorbeeldafbeelding vervangen"

#. translators: %1$s = “WP Rocket”, %2$s = a list of plugin or themes names.
#: inc/Engine/Admin/Settings/Page.php:1007
msgid "Replace YouTube iframe with preview image is not compatible with %2$s."
msgstr ""
"Vervang YouTube iframe met een voorbeeldafbeelding is niet compatibel met "
"%2$s."

#. translators: %1$s = “WP Rocket”, %2$s = a list of plugin or themes names.
#: inc/Engine/Admin/Settings/Page.php:1007
msgid ""
"This can significantly improve your loading time if you have a lot of "
"YouTube videos on a page."
msgstr ""
"Dit kan je laadtijd aanzienlijk verbeteren als je veel YouTube video's op "
"een pagina hebt."

#: inc/Engine/Admin/Settings/Page.php:1022
msgid "Excluded images or iframes"
msgstr "Uitgesloten afbeeldingen of iframes"

#. translators: %1$s = opening <a> tag, %2$s = closing </a> tag.
#: inc/Engine/Admin/Settings/Page.php:1024
msgid ""
"Specify keywords (e.g. image filename, CSS filename, CSS class, domain) from"
" the image or iframe code to be excluded (one per line). %1$sMore info%2$s"
msgstr ""
"Geef trefwoorden op (bijv. bestandsnaam van afbeelding, CSS-bestandsnaam, "
"CSS-klasse, domein) van de uit te sluiten code van de afbeelding of het "
"iframe (één per regel). %1$sMeer info%2$s"

#: inc/Engine/Admin/Settings/Page.php:1032
msgid "Add missing image dimensions"
msgstr "Voeg missende afbeelding afmetingen toe"

#: inc/Engine/Admin/Settings/Page.php:1052
msgid "Generate cache files, preload fonts"
msgstr "Genereer cache-bestanden, lettertypes preloaden"

#: inc/Engine/Admin/Settings/Page.php:1064
msgid "Preload Cache"
msgstr "Cache preloaden"

#. translators: %1$s = opening <a> tag, %2$s = closing </a> tag.
#: inc/Engine/Admin/Settings/Page.php:1067
msgid ""
"When you enable preloading WP Rocket will automatically detect your sitemaps"
" and save all URLs to the database. The plugin will make sure that your "
"cache is always preloaded."
msgstr ""
"Als je preloaden inschakelt, zal WP Rocket automatisch je sitemaps "
"detecteren en alle URL's opslaan in de database. De plugin zorgt ervoor dat "
"je cache altijd wordt gepreload."

#: inc/Engine/Admin/Settings/Page.php:1075
msgid "Preload Links"
msgstr "Links preloaden"

#. translators: %1$s = opening <a> tag, %2$s = closing </a> tag.
#: inc/Engine/Admin/Settings/Page.php:1078
msgid ""
"Link preloading improves the perceived load time by downloading a page when "
"a user hovers over the link. %1$sMore info%2$s"
msgstr ""
"Link preloaden verbeterd de beleving van laadtijden door een pagina te "
"downloaden wanneer een gebruiker over een de link hovert. %1$sMeer info%2$s"

#: inc/Engine/Admin/Settings/Page.php:1086
msgid "Prefetch DNS Requests"
msgstr "DNS verzoeken prefetchen"

#: inc/Engine/Admin/Settings/Page.php:1088
msgid ""
"DNS prefetching can make external files load faster, especially on mobile "
"networks"
msgstr ""
"DNS prefetchen kan zorgen voor het sneller inladen van externe bestanden, "
"vooral op mobiele netwerken"

#: inc/Engine/Admin/Settings/Page.php:1093
msgid "Preload Fonts"
msgstr "Lettertypes preloaden"

#. translators: %1$s = opening <a> tag, %2$s = closing </a> tag.
#: inc/Engine/Admin/Settings/Page.php:1096
msgid ""
"Improves performance by helping browsers discover fonts in CSS files. "
"%1$sMore info%2$s"
msgstr ""
"Verbeterd prestaties door browser te helpen bij het ontdekken van "
"lettertypes in CSS bestanden. %1$sMeer informatie%2$s"

#: inc/Engine/Admin/Settings/Page.php:1110
msgid "Activate Preloading"
msgstr "Activeer preloaden"

#: inc/Engine/Admin/Settings/Page.php:1121
msgid "Exclude URLs"
msgstr "URL’s uitsluiten"

#. translators: %1$s = opening <a> tag, %2$s = closing </a> tag.
#: inc/Engine/Admin/Settings/Page.php:1126
msgid ""
"Specify URLs to be excluded from the preload feature (one per line). "
"%1$sMore info%2$s"
msgstr ""
"Geef URL's op die moeten worden uitgesloten van de preload functie (één per "
"regel). %1$sMeer informatie%2$s"

#: inc/Engine/Admin/Settings/Page.php:1137
msgid "URLs to prefetch"
msgstr "URL’s om te prefetchen"

#: inc/Engine/Admin/Settings/Page.php:1138
msgid ""
"Specify external hosts to be prefetched (no <code>http:</code>, one per "
"line)"
msgstr ""
"Specificeer externe hosts die geprefetched moeten worden (geen "
"<code>http:</code>, één per regel)"

#: inc/Engine/Admin/Settings/Page.php:1147
msgid "Fonts to preload"
msgstr "Lettertypes om te preloaden"

#: inc/Engine/Admin/Settings/Page.php:1148
msgid ""
"Specify urls of the font files to be preloaded (one per line). Fonts must be"
" hosted on your own domain, or the domain you have specified on the CDN tab."
msgstr ""
"Specificeer URL's van de lettertype bestanden die moeten worden gepreload "
"(één per regel). Lettertypes moeten gehost worden op je eigen domein of het "
"domein dat je hebt ingesteld op het CDN tabblad."

#: inc/Engine/Admin/Settings/Page.php:1149
msgid ""
"The domain part of the URL will be stripped automatically.<br/>Allowed font "
"extensions: otf, ttf, svg, woff, woff2."
msgstr ""
"Het domein deel van de URL wordt automatisch verwijderd. <br/>Toegestane "
"lettertype extensies: otf, ttf, svg, woff, woff2."

#: inc/Engine/Admin/Settings/Page.php:1158
msgid "Enable link preloading"
msgstr "Link preloaden activeren"

#: inc/Engine/Admin/Settings/Page.php:1177
msgid "Advanced Rules"
msgstr "Geavanceerde regels"

#: inc/Engine/Admin/Settings/Page.php:1178
msgid "Fine-tune cache rules"
msgstr "Cache-regels verfijnen"

#: inc/Engine/Admin/Settings/Page.php:1191
msgid ""
"Sensitive pages like custom login/logout URLs should be excluded from cache."
msgstr ""
"Gevoelige pagina's zoals gepersonaliseerde login/logout URL's moet "
"uitgesloten worden van de cache."

#: inc/Engine/Admin/Settings/Page.php:1194
msgctxt "plugin name"
msgid "WooCommerce"
msgstr "WooCommerce"

#: inc/Engine/Admin/Settings/Page.php:1196
msgctxt "plugin name"
msgid "Easy Digital Downloads"
msgstr "Easy Digital Downloads"

#: inc/Engine/Admin/Settings/Page.php:1198
msgctxt "plugin name"
msgid "iThemes Exchange"
msgstr "iThemes Exchange"

#: inc/Engine/Admin/Settings/Page.php:1200
msgctxt "plugin name"
msgid "Jigoshop"
msgstr "Jigoshop"

#: inc/Engine/Admin/Settings/Page.php:1202
msgctxt "plugin name"
msgid "WP-Shop"
msgstr "WP-Shop"

#. translators: %1$s = opening <a> tag, %2$s = plugin name, %3$s closing </a>
#. tag.
#: inc/Engine/Admin/Settings/Page.php:1208
msgid ""
"<br>Cart, checkout and \"my account\" pages set in "
"<strong>%1$s%2$s%3$s</strong> will be detected and never cached by default."
msgstr ""
"<br>De pagina's Winkelwagen, afrekenen en \"mijn account\" die zijn "
"ingesteld in <strong>%1$s%2$s%3$s</strong> worden standaard gedetecteerd en "
"nooit in de cache opgeslagen."

#: inc/Engine/Admin/Settings/Page.php:1218
msgid "Cache Lifespan"
msgstr "Cache levensduur"

#. translators: %1$s = opening <a> tag, %2$s = closing </a> tag.
#: inc/Engine/Admin/Settings/Page.php:1221
msgid ""
"Cache files older than the specified lifespan will be deleted.<br>Enable "
"%1$spreloading%2$s for the cache to be rebuilt automatically after lifespan "
"expiration."
msgstr ""
"Cache-bestanden die ouder zijn dan de opgegeven levensduur worden "
"verwijderd.<br>Schakel %1$spreloaden in%2$s zodat de cache automatisch wordt"
" herbouwd na het verlopen van de levensduur."

#: inc/Engine/Admin/Settings/Page.php:1237
msgid "Never Cache Cookies"
msgstr "Nooit cookies cachen"

#: inc/Engine/Admin/Settings/Page.php:1255
msgid "Cache Query String(s)"
msgstr "Query string(s) cachen"

#. translators: %1$s = opening <a> tag, %2$s = closing </a> tag.
#: inc/Engine/Admin/Settings/Page.php:1258
msgid ""
"%1$sCache for query strings%2$s enables you to force caching for specific "
"GET parameters."
msgstr ""
"%1$sCache voor query strings%2$s stelt je in staat om caching te forceren "
"voor specifieke GET parameters."

#: inc/Engine/Admin/Settings/Page.php:1269
msgid ""
"Specify time after which the global cache is cleared<br>(0 = unlimited )"
msgstr ""
"Specificeer de tijd waarna de volledige cache automatisch wordt geleegd <br>"
" (0 = onbeperkt, niet legen)"

#. translators: %1$s = opening <a> tag, %2$s = closing </a> tag.
#: inc/Engine/Admin/Settings/Page.php:1271
msgid ""
"Reduce lifespan to 10 hours or less if you notice issues that seem to appear"
" periodically. %1$sWhy?%2$s"
msgstr ""
"Verlaag de levensduur naar 10 uur of minder als je problemen ondervindt die "
"soms voorkomen. %1$sWaarom?%2$s"

#: inc/Engine/Admin/Settings/Page.php:1277
#: inc/Engine/License/views/promo-banner.php:30
#: inc/Engine/License/views/renewal-soon-banner.php:13
msgid "Hours"
msgstr "Uren"

#: inc/Engine/Admin/Settings/Page.php:1278
#: inc/Engine/License/views/promo-banner.php:29
#: inc/Engine/License/views/renewal-soon-banner.php:12
msgid "Days"
msgstr "Dagen"

#: inc/Engine/Admin/Settings/Page.php:1283
msgid ""
"Specify URLs of pages or posts that should never be cached (one per line)"
msgstr ""
"Specificeer URL's of pagina's or berichten die nooit gecachet mogen worden "
"(één per regel)"

#: inc/Engine/Admin/Settings/Page.php:1284
#: inc/Engine/Admin/Settings/Page.php:1312
msgid ""
"The domain part of the URL will be stripped automatically.<br>Use (.*) "
"wildcards to address multiple URLs under a given path."
msgstr ""
"Het domein deel van de URL wordt automatisch verwijderd.<br>Gebruik (.*) "
"wildcards om meerdere URL's onder een bepaald pad te adresseren."

#: inc/Engine/Admin/Settings/Page.php:1293
msgid ""
"Specify full or partial IDs of cookies that, when set in the visitor's "
"browser, should prevent a page from getting cached (one per line)"
msgstr ""
"Geef volledige of gedeeltelijke ID's op van cookies die, als ze in de "
"browser van de bezoeker zijn ingesteld, moeten voorkomen dat een pagina in "
"de cache wordt opgeslagen (één per regel)"

#: inc/Engine/Admin/Settings/Page.php:1301
msgid ""
"Specify user agent strings that should never see cached pages (one per line)"
msgstr ""
"Specificeer user agent-strings die nooit gecachete pagina's moeten zien (één"
" per regel)"

#: inc/Engine/Admin/Settings/Page.php:1302
msgid "Use (.*) wildcards to detect parts of UA strings."
msgstr "Gebruikt (.*)-wildcards om delen van UA-strings te detecteren."

#: inc/Engine/Admin/Settings/Page.php:1311
msgid ""
"Specify URLs you always want purged from cache whenever you update any post "
"or page (one per line)"
msgstr ""
"Specificeer URL's die je altijd uit de cache wilt legen als je een bericht "
"of pagina updatet (één per regel)"

#: inc/Engine/Admin/Settings/Page.php:1320
msgid "Specify query strings for caching (one per line)"
msgstr "Specificeer query strings voor caching (één per regel)"

#: inc/Engine/Admin/Settings/Page.php:1346
msgid "Optimize, reduce bloat"
msgstr "Optimaliseer, verminder bloat"

#: inc/Engine/Admin/Settings/Page.php:1353
msgid "Post Cleanup"
msgstr "Berichten opschonen"

#: inc/Engine/Admin/Settings/Page.php:1355
msgid ""
"Post revisions and drafts will be permanently deleted. Do not use this "
"option if you need to retain revisions or drafts."
msgstr ""
"Bericht revisies en concepten worden definitief verwijderd. Gebruik deze "
"optie niet als je revisies of concepten wilt behouden."

#: inc/Engine/Admin/Settings/Page.php:1360
msgid "Comments Cleanup"
msgstr "Reacties opschonen"

#: inc/Engine/Admin/Settings/Page.php:1362
msgid "Spam and trashed comments will be permanently deleted."
msgstr "Spam en verwijderde reacties worden definitief verwijderd."

#: inc/Engine/Admin/Settings/Page.php:1366
msgid "Transients Cleanup"
msgstr "Transients opschonen"

#: inc/Engine/Admin/Settings/Page.php:1368
msgid ""
"Transients are temporary options; they are safe to remove. They will be "
"automatically regenerated as your plugins require them."
msgstr ""
"Transients zijn tijdelijke opties; ze zijn veilig om te verwijderen. Ze "
"worden opnieuw gegenereerd op het moment dat je plugins deze nodig hebben."

#: inc/Engine/Admin/Settings/Page.php:1372
msgid "Database Cleanup"
msgstr "Database opschonen"

#: inc/Engine/Admin/Settings/Page.php:1374
msgid "Reduces overhead of database tables"
msgstr "Verminder de overhead binnen je database tabellen"

#: inc/Engine/Admin/Settings/Page.php:1378
msgid "Automatic Cleanup"
msgstr "Automatisch opruimen"

#. translators: %s is the number of revisions found in the database. It's a
#. formatted number, don't use %d.
#: inc/Engine/Admin/Settings/Page.php:1391
msgid "%s revision in your database."
msgid_plural "%s revisions in your database."
msgstr[0] "%s revisie in je database."
msgstr[1] "%s revisies in je database."

#. translators: %s is the number of revisions found in the database. It's a
#. formatted number, don't use %d.
#: inc/Engine/Admin/Settings/Page.php:1401
msgid "%s draft in your database."
msgid_plural "%s drafts in your database."
msgstr[0] "%s concept in je database."
msgstr[1] "%s concepten in je database."

#. translators: %s is the number of revisions found in the database. It's a
#. formatted number, don't use %d.
#: inc/Engine/Admin/Settings/Page.php:1411
msgid "%s trashed post in your database."
msgid_plural "%s trashed posts in your database."
msgstr[0] "%s verwijderd bericht in je database."
msgstr[1] "%s verwijderde berichten in je database."

#. translators: %s is the number of revisions found in the database. It's a
#. formatted number, don't use %d.
#: inc/Engine/Admin/Settings/Page.php:1421
msgid "%s spam comment in your database."
msgid_plural "%s spam comments in your database."
msgstr[0] "%s spam reactie in je database."
msgstr[1] "%s spam reacties in je database."

#. translators: %s is the number of revisions found in the database. It's a
#. formatted number, don't use %d.
#: inc/Engine/Admin/Settings/Page.php:1431
msgid "%s trashed comment in your database."
msgid_plural "%s trashed comments in your database."
msgstr[0] "%s verwijderde reactie in je database."
msgstr[1] "%s verwijderde reacties in je database."

#: inc/Engine/Admin/Settings/Page.php:1439
msgid "All transients"
msgstr "Alle transients"

#. translators: %s is the number of revisions found in the database. It's a
#. formatted number, don't use %d.
#: inc/Engine/Admin/Settings/Page.php:1441
msgid "%s transient in your database."
msgid_plural "%s transients in your database."
msgstr[0] "%s transient in je database."
msgstr[1] "%s transients in je database."

#: inc/Engine/Admin/Settings/Page.php:1449
msgid "Optimize Tables"
msgstr "Tabellen optimaliseren"

#. translators: %s is the number of revisions found in the database. It's a
#. formatted number, don't use %d.
#: inc/Engine/Admin/Settings/Page.php:1451
msgid "%s table to optimize in your database."
msgid_plural "%s tables to optimize in your database."
msgstr[0] "%s te optimaliseren tabel in je database."
msgstr[1] "%s te optimaliseren tabellen in je database."

#: inc/Engine/Admin/Settings/Page.php:1462
msgid "Schedule Automatic Cleanup"
msgstr "Automatisch opruimen plannen"

#: inc/Engine/Admin/Settings/Page.php:1474
msgid "Frequency"
msgstr "Frequentie"

#: inc/Engine/Admin/Settings/Page.php:1482
msgid "Daily"
msgstr "Dagelijks"

#: inc/Engine/Admin/Settings/Page.php:1483
msgid "Weekly"
msgstr "Wekelijks"

#: inc/Engine/Admin/Settings/Page.php:1484
msgid "Monthly"
msgstr "Maandelijks"

#: inc/Engine/Admin/Settings/Page.php:1501
msgid "Integrate your CDN"
msgstr "Integreer je CDN"

#: inc/Engine/Admin/Settings/Page.php:1513
msgid ""
"All URLs of static files (CSS, JS, images) will be rewritten to the CNAME(s)"
" you provide."
msgstr ""
"Alle URL's van statische bestanden (CSS, JS, afbeeldingen) worden "
"overschreven met de CNAME(s) die je invoert."

#. translators: %1$s = opening link tag, %2$s = closing link tag.
#: inc/Engine/Admin/Settings/Page.php:1515
msgid ""
"Not required for services like Cloudflare and Sucuri. Please see our "
"available %1$sAdd-ons%2$s."
msgstr ""
"Dit is niet verplicht voor services zoals Cloudflare en Sucuri. Bekijk "
"daarvoor onze beschikbare %1$sadd-ons%2$s."

#. translators: %1$s = opening em tag, %2$l = list of add-on name(s), %3$s =
#. closing em tag.
#: inc/Engine/Admin/Settings/Page.php:1561
msgid ""
"%1$s%2$l Add-on%3$s is currently enabled. Configuration of the CDN settings "
"is not required for %2$l to work on your site."
msgid_plural ""
"%1$s%2$l Add-ons%3$s are currently enabled. Configuration of the CDN "
"settings is not required for %2$l to work on your site."
msgstr[0] ""
"%1$s%2$l Add-on%3$s is momenteel ingeschakeld. Configuratie van de CDN-"
"instellingen is niet nodig om %2$l op je site te laten werken."
msgstr[1] ""
"%1$s%2$l Add-ons%3$s zijn momenteel ingeschakeld. Configuratie van de CDN-"
"instellingen is niet vereist om %2$l op je site te laten werken."

#: inc/Engine/Admin/Settings/Page.php:1587
msgid "Enable Content Delivery Network"
msgstr "Content Delivery Network inschakelen"

#: inc/Engine/Admin/Settings/Page.php:1596
#: inc/Engine/CDN/RocketCDN/AdminPageSubscriber.php:159
msgid "CDN CNAME(s)"
msgstr "CDN CNAME(s)"

#: inc/Engine/Admin/Settings/Page.php:1597
#: inc/Engine/CDN/RocketCDN/AdminPageSubscriber.php:160
msgid "Specify the CNAME(s) below"
msgstr "Specificeer de CNAME(s) hieronder"

#: inc/Engine/Admin/Settings/Page.php:1604
msgid ""
"Specify URL(s) of files that should not get served via CDN (one per line)."
msgstr ""
"Specificeer URL(s) van de bestanden die niet via de CDN mogen worden "
"geleverd (één URL per regel)"

#: inc/Engine/Admin/Settings/Page.php:1605
msgid ""
"The domain part of the URL will be stripped automatically.<br>Use (.*) "
"wildcards to exclude all files of a given file type located at a specific "
"path."
msgstr ""
"Het domein gedeelte van de URL wordt automatisch gestript.  <br>Gebruik (.*)"
" wildcards om alle bestanden van het gedeelte van de url uit te sluiten."

#: inc/Engine/Admin/Settings/Page.php:1628
#: inc/Engine/Admin/Settings/Page.php:1636
msgid "Heartbeat"
msgstr "Heartbeat"

#: inc/Engine/Admin/Settings/Page.php:1629
msgid "Control WordPress Heartbeat API"
msgstr "Beheer WordPress Heartbeat API"

#: inc/Engine/Admin/Settings/Page.php:1637
msgid ""
"Reducing or disabling the Heartbeat API’s activity can help save some of "
"your server’s resources."
msgstr ""
"Verminderen of uitschakelen van de Heartbeat API kan helpen bij het besparen"
" van je server's resources."

#: inc/Engine/Admin/Settings/Page.php:1643
msgid "Reduce or disable Heartbeat activity"
msgstr "Verminder of schakel Heartbeat activiteit uit"

#: inc/Engine/Admin/Settings/Page.php:1644
msgid ""
"Reducing activity will change Heartbeat frequency from one hit each minute "
"to one hit every 2 minutes."
msgstr ""
"Verminderen van de Heartbeat frequentie zal ervoor zorgen dat de API van één"
" keer per minuut, naar één keer per 2 minuten uitvoeren wordt ingesteld."

#: inc/Engine/Admin/Settings/Page.php:1644
msgid ""
"Disabling Heartbeat entirely may break plugins and themes using this API."
msgstr ""
"Volledig uitschakelen van Heartbeat kan problemen geven met plugins en "
"thema's die gebruik maken van deze API."

#: inc/Engine/Admin/Settings/Page.php:1658
msgid "Do not limit"
msgstr "Stel geen limiet in"

#: inc/Engine/Admin/Settings/Page.php:1659
msgid "Reduce activity"
msgstr "Verminder activiteit"

#: inc/Engine/Admin/Settings/Page.php:1660
msgid "Disable"
msgstr "Uitschakelen"

#: inc/Engine/Admin/Settings/Page.php:1668
msgid "Control Heartbeat"
msgstr "Beheer Heartbeat"

#: inc/Engine/Admin/Settings/Page.php:1677
msgid "Behavior in backend"
msgstr "Gedrag in de backend"

#: inc/Engine/Admin/Settings/Page.php:1684
msgid "Behavior in post editor"
msgstr "Gedrag in de berichten editor"

#: inc/Engine/Admin/Settings/Page.php:1690
msgid "Behavior in frontend"
msgstr "Gedrag in de frontend"

#: inc/Engine/Admin/Settings/Page.php:1709
#: views/settings/page-sections/tutorials.php:39
msgid "Add-ons"
msgstr "Add-ons"

#: inc/Engine/Admin/Settings/Page.php:1710
msgid "Add more features"
msgstr "Meer functies toevoegen"

#: inc/Engine/Admin/Settings/Page.php:1717
msgid "One-click Rocket Add-ons"
msgstr "One-click Rocket add-ons"

#: inc/Engine/Admin/Settings/Page.php:1718
msgid ""
"One-Click Add-ons are features extending available options without "
"configuration needed. Switch the option \"on\" to enable from this screen."
msgstr ""
"One-click Add-ons zijn functies die beschikbare opties uitbreiden zonder dat"
" configuratie nodig is. Schakel de optie naar \"aan\" om deze in te "
"schakelen vanuit dit scherm."

#: inc/Engine/Admin/Settings/Page.php:1728
msgid "Rocket Add-ons"
msgstr "Rocket add-ons"

#: inc/Engine/Admin/Settings/Page.php:1729
msgid "Rocket Add-ons are complementary features extending available options."
msgstr ""
"Rocket add-ons zijn aanvullende functies die beschikbare opties uitbreiden."

#: inc/Engine/Admin/Settings/Page.php:1740
msgid "User Cache"
msgstr "Gebruikerscache"

#: inc/Engine/Admin/Settings/Page.php:1746
msgid ""
"If you need to create a dedicated set of cache files for each logged-in "
"WordPress user, you must activate this add-on."
msgstr ""
"Als je voor elke ingelogde WordPress gebruiker een eigen set cache-bestanden"
" moet maken, dan moet je deze add-on activeren."

#. translators: %1$s = opening <a> tag, %2$s = closing </a> tag.
#: inc/Engine/Admin/Settings/Page.php:1748
msgid ""
"User cache is great when you have user-specific or restricted content on "
"your website.<br>%1$sLearn more%2$s"
msgstr ""
"Gebruikerscache is ideaal als je gebruikersspecifieke of afgeschermde inhoud"
" op je website hebt.<br>%1$sLeer meer%2$s"

#: inc/Engine/Admin/Settings/Page.php:1761
#: inc/Engine/Admin/Settings/Page.php:1930
msgid "Cloudflare"
msgstr "Cloudflare"

#: inc/Engine/Admin/Settings/Page.php:1767
msgid "Integrate your Cloudflare account with this add-on."
msgstr "Integreer je Cloudflare account met deze add-on."

#: inc/Engine/Admin/Settings/Page.php:1768
msgid ""
"Provide your account email, global API key, and domain to use options such "
"as clearing the Cloudflare cache and enabling optimal settings with WP "
"Rocket."
msgstr ""
"Geef je account e-mail, globale API-sleutel en domein op om opties te "
"gebruiken zoals het legen van de Cloudflare cache en het inschakelen van "
"optimale instellingen met WP Rocket."

#. translators: %1$s = opening span tag, %2$s = closing span tag.
#: inc/Engine/Admin/Settings/Page.php:1771
msgid ""
"%1$sPlanning on using Automatic Platform Optimization (APO)?%2$s Just "
"activate the official Cloudflare plugin and configure it. WP Rocket will "
"automatically enable compatibility."
msgstr ""
"%1$sBen je van plan Automatic Platform Optimization (APO) te gebruiken?%2$s "
"Activeer gewoon de officiële Cloudflare plugin en configureer deze. WP "
"Rocket zal de compatibiliteit automatisch inschakelen."

#: inc/Engine/Admin/Settings/Page.php:1819
msgid "Varnish"
msgstr "Varnish"

#: inc/Engine/Admin/Settings/Page.php:1825
msgid "If Varnish runs on your server, you must activate this add-on."
msgstr "Als Varnish op je server draait, moet je deze add-on activeren."

#. translators: %1$s = opening <a> tag, %2$s = closing </a> tag.
#: inc/Engine/Admin/Settings/Page.php:1827
msgid ""
"Varnish cache will be purged each time WP Rocket clears its cache to ensure "
"content is always up-to-date.<br>%1$sLearn more%2$s"
msgstr ""
"Varnish cache wordt elke keer geleegd als WP Rocket zijn cache leegt, zodat "
"de inhoud altijd up-to-date is.<br>%1$sLeer meer%2$s"

#: inc/Engine/Admin/Settings/Page.php:1862
msgid "WebP Compatibility"
msgstr "WebP compatibiliteit"

#: inc/Engine/Admin/Settings/Page.php:1868
msgid "Improve browser compatibility for WebP images."
msgstr "Verbeterde browsercompatibiliteit voor WebP-afbeeldingen."

#. translators: %1$s and %3$s = opening <a> tag, %2$s = closing </a> tag.
#: inc/Engine/Admin/Settings/Page.php:1872
msgid ""
"Enable this option if you would like WP Rocket to serve WebP images to "
"compatible browsers. Please note that WP Rocket cannot create WebP images "
"for you. To create WebP images we recommend %1$sImagify%2$s. %3$sMore "
"info%2$s"
msgstr ""
"Schakel deze optie in als je wilt dat WP Rocket WebP-afbeeldingen serveert "
"aan compatibele browsers. Let er op dat WP Rocket geen WebP-afbeeldingen "
"voor je kan maken. Voor het maken van WebP-afbeeldingen raden we "
"%1$sImagify%2$s aan. %3$sMeer info%2$s"

#: inc/Engine/Admin/Settings/Page.php:1892
msgid "Clear the Sucuri cache when WP Rocket’s cache is cleared."
msgstr "Sucuri cache legen wanneer WP Rocket's cache is geleegd."

#: inc/Engine/Admin/Settings/Page.php:1895
msgid ""
"Provide your API key to clear the Sucuri cache when WP Rocket’s cache is "
"cleared."
msgstr ""
"Voer je API-sleutel in om de Sucuri cache te legen op het moment dat WP "
"Rocket’s cache wordt geleegd."

#: inc/Engine/Admin/Settings/Page.php:1903
#: inc/Engine/Admin/Settings/Page.php:2047
msgid "Sucuri"
msgstr "Sucuri"

#: inc/Engine/Admin/Settings/Page.php:1909
msgid "Synchronize Sucuri cache with this add-on."
msgstr "Synchroniseer Sucuri cache met deze add-on."

#: inc/Engine/Admin/Settings/Page.php:1947
msgid "Cloudflare credentials"
msgstr "Cloudflare inloggegevens"

#: inc/Engine/Admin/Settings/Page.php:1956
msgid "Cloudflare settings"
msgstr "Cloudflare-instellingen"

#: inc/Engine/Admin/Settings/Page.php:1970
msgctxt "Cloudflare"
msgid "Global API key:"
msgstr "Globale API-sleutel:"

#: inc/Engine/Admin/Settings/Page.php:1971
msgctxt "Cloudflare"
msgid "Find your API key"
msgstr "Vind je API-code"

#: inc/Engine/Admin/Settings/Page.php:1983
msgctxt "Cloudflare"
msgid "Account email"
msgstr "Account e-mail"

#: inc/Engine/Admin/Settings/Page.php:1992
msgctxt "Cloudflare"
msgid "Zone ID"
msgstr "Zone ID"

#: inc/Engine/Admin/Settings/Page.php:2002
msgid "Development mode"
msgstr "Development modus"

#. translators: %1$s = link opening tag, %2$s = link closing tag.
#: inc/Engine/Admin/Settings/Page.php:2004
msgid ""
"Temporarily activate development mode on your website. This setting will "
"automatically turn off after 3 hours. %1$sLearn more%2$s"
msgstr ""
"Activeer tijdelijk de ontwikkelmodus op je site. Deze instelling wordt na 3 "
"uur automatisch uitgeschakeld. %1$sLeer meer%2$s"

#: inc/Engine/Admin/Settings/Page.php:2012
msgid "Optimal settings"
msgstr "Optimale instellingen"

#: inc/Engine/Admin/Settings/Page.php:2013
msgid ""
"Automatically enhances your Cloudflare configuration for speed, performance "
"grade and compatibility."
msgstr ""
"Verbetert automatisch je Cloudflare configuratie voor snelheid, "
"prestatiegraad en compatibiliteit."

#: inc/Engine/Admin/Settings/Page.php:2021
msgid "Relative protocol"
msgstr "Relatief protocol"

#: inc/Engine/Admin/Settings/Page.php:2022
msgid ""
"Should only be used with Cloudflare's flexible SSL feature. URLs of static "
"files (CSS, JS, images) will be rewritten to use // instead of http:// or "
"https://."
msgstr ""
"Mag alleen worden gebruikt met de flexibele SSL-functie van Cloudflare. "
"URL's van statische bestanden (CSS, JS, afbeeldingen) worden herschreven en "
"gebruiken nu // in plaats van http:// of https://."

#: inc/Engine/Admin/Settings/Page.php:2060
msgid "Sucuri credentials"
msgstr "Sucuri gegevens"

#: inc/Engine/Admin/Settings/Page.php:2073
msgctxt "Sucuri"
msgid ""
"Firewall API key (for plugin), must be in format {32 characters}/{32 "
"characters}:"
msgstr ""
"Firewall API-sleutel (voor plugin), moet het formaat {32 tekens}/{32 tekens}"
" hebben:"

#: inc/Engine/Admin/Settings/Page.php:2074
msgctxt "Sucuri"
msgid "Find your API key"
msgstr "Vind je API-code"

#: inc/Engine/Admin/Settings/Settings.php:361
msgid ""
"Sucuri Add-on: The API key for the Sucuri firewall must be in format "
"<code>{32 characters}/{32 characters}</code>."
msgstr ""
"Sucuri add-on: de API-sleutel voor de Sucuri firewall moet het formaat "
"<code>{32 tekens}/{32 tekens}</code> hebben."

#: inc/Engine/Admin/Settings/Settings.php:667
msgid ""
"Sorry! Adding /(.*) in Advanced Rules > Never Cache URL(s) was not saved "
"because it disables caching and optimizations for every page on your site."
msgstr ""
"Sorry! Het toevoegen van /(.*) in Geavanceerde regels > Nooit URL(s) cachen "
"is niet opgeslagen omdat het caching en optimalisaties uitschakelt voor elke"
" pagina op je site."

#: inc/Engine/Admin/Settings/Subscriber.php:171
msgid "Import, Export, Rollback"
msgstr "Importeren, exporteren, terugzetten"

#: inc/Engine/Admin/Settings/Subscriber.php:196
#: views/settings/page-sections/imagify.php:14
msgid "Image Optimization"
msgstr "Afbeeldingsoptimalisatie"

#: inc/Engine/Admin/Settings/Subscriber.php:197
msgid "Compress your images"
msgstr "Optimaliseer je afbeeldingen"

#: inc/Engine/Admin/Settings/Subscriber.php:214
#: views/settings/page-sections/tutorials.php:48
msgid "Tutorials"
msgstr "Tutorials"

#: inc/Engine/Admin/Settings/Subscriber.php:215
msgid "Getting started and how to videos"
msgstr "Aan de slag en how-to videos"

#: inc/Engine/Cache/PurgeExpired/Subscriber.php:75
msgid "WP Rocket Expired Cache Interval"
msgstr "WP Rocket verlopen cacheinterval"

#: inc/Engine/Cache/WPCache.php:337
msgid "WP_CACHE value"
msgstr "WP_CACHE waarde"

#: inc/Engine/Cache/WPCache.php:354
msgid "Cache"
msgstr "Cache"

#: inc/Engine/Cache/WPCache.php:358
msgid ""
"The WP_CACHE constant needs to be set to true for WP Rocket cache to work "
"properly"
msgstr ""
"De WP_CACHE constante moet worden ingesteld op true om WP Rocket cache goed "
"te laten werken"

#: inc/Engine/Cache/WPCache.php:367
msgid "WP_CACHE is set to true"
msgstr "WP_CACHE is ingesteld op true"

#: inc/Engine/Cache/WPCache.php:375
msgid "WP_CACHE is not set"
msgstr "WP_CACHE is niet ingesteld"

#: inc/Engine/Cache/WPCache.php:383
msgid "WP_CACHE is set to false"
msgstr "WP_CACHE is ingesteld op false"

#: inc/Engine/CDN/RocketCDN/AdminPageSubscriber.php:90
msgid "Next Billing Date"
msgstr "Volgende facturatiedatum"

#: inc/Engine/CDN/RocketCDN/AdminPageSubscriber.php:99
msgid "No Subscription"
msgstr "Geen abonnement"

#: inc/Engine/CDN/RocketCDN/AdminPageSubscriber.php:135
msgid "Your RocketCDN subscription is currently active."
msgstr "Je RocketCDN abonnement is op dit moment actief."

#. translators: %1$s = opening <code> tag, %2$s = CDN URL, %3$s = closing
#. </code> tag.
#: inc/Engine/CDN/RocketCDN/AdminPageSubscriber.php:141
msgid "To use RocketCDN, replace your CNAME with %1$s%2$s%3$s."
msgstr "Om RocketCDN te gebruiken, vervang je je CNAME door %1$s%2$s%3$s."

#. translators: %1$is = opening link tag, %2$s = closing link tag.
#: inc/Engine/CDN/RocketCDN/AdminPageSubscriber.php:152
#: inc/Engine/CDN/RocketCDN/NoticesSubscriber.php:230
#: inc/Engine/CDN/RocketCDN/NoticesSubscriber.php:334
msgid "%1$sMore Info%2$s"
msgstr "%1$sMeer info%2$s"

#: inc/Engine/CDN/RocketCDN/APIClient.php:134
msgid ""
"We could not fetch the current price because RocketCDN API returned an "
"unexpected error code."
msgstr ""
"We konden de huidige prijs niet ophalen omdat RocketCDN API een onverwachte "
"foutcode teruggaf."

#: inc/Engine/CDN/RocketCDN/APIClient.php:140
msgid "RocketCDN is not available at the moment. Please retry later."
msgstr "RocketCDN is momenteel niet beschikbaar. Probeer het later nog eens."

#: inc/Engine/CDN/RocketCDN/APIClient.php:177
msgid "RocketCDN cache purge failed: Missing identifier parameter."
msgstr "RocketCDN cache legen mislukt: identificatie parameter mist."

#: inc/Engine/CDN/RocketCDN/APIClient.php:186
msgid "RocketCDN cache purge failed: Missing user token."
msgstr "RocketCDN cache legen mislukt: gebruikerstoken mist."

#: inc/Engine/CDN/RocketCDN/APIClient.php:212
msgid ""
"RocketCDN cache purge failed: The API returned an unexpected response code."
msgstr ""
"RocketCDN cache legen mislukt: de API heeft een onverwachte responscode "
"geretourneerd."

#: inc/Engine/CDN/RocketCDN/APIClient.php:221
msgid "RocketCDN cache purge failed: The API returned an empty response."
msgstr ""
"RocketCDN cache legen mislukt: de API heeft een leeg antwoord teruggestuurd."

#: inc/Engine/CDN/RocketCDN/APIClient.php:230
msgid "RocketCDN cache purge failed: The API returned an unexpected response."
msgstr ""
"RocketCDN cache legen mislukt: de API heeft een onverwachte respons "
"teruggestuurd."

#. translators: %s = message returned by the API.
#: inc/Engine/CDN/RocketCDN/APIClient.php:239
msgid "RocketCDN cache purge failed: %s."
msgstr "RocketCDN cache legen mislukt: %s."

#: inc/Engine/CDN/RocketCDN/APIClient.php:247
msgid "RocketCDN cache purge successful."
msgstr "RocketCDN cache succesvol geleegd."

#: inc/Engine/CDN/RocketCDN/RESTSubscriber.php:137
#: tests/Integration/inc/Engine/CDN/RocketCDN/RESTSubscriber/enable.php:72
#: tests/Integration/inc/Engine/CDN/RocketCDN/RESTSubscriber/registerEnableRoute.php:302
msgid "RocketCDN enabled"
msgstr "RocketCDN ingeschakeld"

#: inc/Engine/CDN/RocketCDN/RESTSubscriber.php:160
#: tests/Integration/inc/Engine/CDN/RocketCDN/RESTSubscriber/disable.php:31
#: tests/Integration/inc/Engine/CDN/RocketCDN/RESTSubscriber/registerDisableRoute.php:147
msgid "RocketCDN disabled"
msgstr "RocketCDN uitgeschakelt"

#. Translators: %s = date formatted using date_i18n() and get_option(
#. 'date_format' ).
#: inc/Engine/CDN/RocketCDN/views/cta-big.php:27
msgid "Valid until %s only!"
msgstr "Alleen geldig tot %s!"

#: inc/Engine/CDN/RocketCDN/views/cta-big.php:36
msgid "Speed up your website thanks to:"
msgstr "Versnel je site dankzij:"

#. translators: %1$s = opening strong tag, %2$s = closing strong tag.
#: inc/Engine/CDN/RocketCDN/views/cta-big.php:42
msgid ""
"High performance Content Delivery Network (CDN) with %1$sunlimited "
"bandwidth%2$s"
msgstr ""
"Krachtig Content Delivery Network (CDN) met %1$sonbeperkte bandbreedte%2$s"

#. translators: %1$s = opening strong tag, %2$s = closing strong tag.
#: inc/Engine/CDN/RocketCDN/views/cta-big.php:48
msgid ""
"Easy configuration: the %1$sbest CDN settings%2$s are automatically applied"
msgstr ""
"Eenvoudige configuratie: de %1$sbeste CDN-instellingen%2$s worden "
"automatisch toegepast"

#. translators: %1$s = opening strong tag, %2$s = closing strong tag.
#: inc/Engine/CDN/RocketCDN/views/cta-big.php:54
msgid ""
"WP Rocket integration: the CDN option is %1$sautomatically configured%2$s in"
" our plugin"
msgstr ""
"WP Rocket integratie: de CDN optie is %1$sautomatisch geconfigureerd%2$s in "
"onze plugin"

#: inc/Engine/CDN/RocketCDN/views/cta-big.php:58
msgid "Learn more about RocketCDN"
msgstr "Leer meer over RocketCDN"

#. translators: %1$s = discounted price, %2$s = regular price.
#: inc/Engine/CDN/RocketCDN/views/cta-big.php:65
msgid ""
"*$%1$s/month for 12 months then $%2$s/month. You can cancel your "
"subscription at any time."
msgstr ""
"*$%1$s/maand voor 12 maanden daarna $%2$s/maand. Je kunt je abonnement op "
"elk moment opzeggen."

#: inc/Engine/CDN/RocketCDN/views/cta-big.php:86
msgid "Billed monthly"
msgstr "Maandelijks gefactureerd"

#: inc/Engine/CDN/RocketCDN/views/cta-big.php:87
msgid "Get Started"
msgstr "Aan de slag"

#: inc/Engine/CDN/RocketCDN/views/cta-big.php:92
msgid "Reduce this banner"
msgstr "Verklein deze banner"

#: inc/Engine/CDN/RocketCDN/views/cta-small.php:17
msgid ""
"Speed up your website with RocketCDN, WP Rocket’s Content Delivery Network."
msgstr ""
"Versnel je site met RocketCDN, het Content Delivery Network van WP Rocket."

#: inc/Engine/CDN/RocketCDN/views/cta-small.php:20
#: inc/Engine/CDN/RocketCDN/views/promote-notice.php:13
msgid "Learn More"
msgstr "Leer meer"

#: inc/Engine/CDN/RocketCDN/views/dashboard-status.php:23
msgid "RocketCDN is unavailable on local domains and staging sites."
msgstr "RocketCDN is niet beschikbaar op lokale domeinen en staging sites."

#: inc/Engine/CDN/RocketCDN/views/dashboard-status.php:32
msgid "Get RocketCDN"
msgstr "Verkrijg RocketCDN"

#: inc/Engine/CDN/RocketCDN/views/promote-notice.php:11
msgid "New!"
msgstr "Nieuw!"

#: inc/Engine/CDN/RocketCDN/views/promote-notice.php:12
msgid ""
"Speed up your website with RocketCDN, WP Rocket’s Content Delivery Network!"
msgstr ""
"Versnel je site met RocketCDN, het Content Delivery Network van WP Rocket!"

#: inc/Engine/Common/JobManager/Cron/Subscriber.php:180
msgid "WP Rocket process pending jobs"
msgstr "WP Rocket afwachtende taken verwerken"

#: inc/Engine/Common/JobManager/Cron/Subscriber.php:194
msgid "WP Rocket clear failed jobs"
msgstr "WP Rocket mislukte taken legen"

#: inc/Engine/Common/JobManager/Cron/Subscriber.php:206
msgid "WP Rocket process on submit jobs"
msgstr "WP Rocket verwerken bij indienen van taken"

#: inc/Engine/Common/Queue/RUCSSQueueRunner.php:265
msgid "Every minute"
msgstr "Elke minuut"

#: inc/Engine/CriticalPath/Admin/Admin.php:264
msgid "Regenerate Critical Path CSS"
msgstr "Pad naar kritieke CSS opnieuw genereren"

#: inc/Engine/CriticalPath/Admin/Post.php:144
#: views/cpcss/metabox/generate.php:47
msgid "Generate Specific CPCSS"
msgstr "Specifieke CPCSS genereren"

#: inc/Engine/CriticalPath/Admin/Post.php:145
#: views/cpcss/metabox/generate.php:45
msgid "Regenerate specific CPCSS"
msgstr "Specifieke CPCSS opnieuw genereren"

#: inc/Engine/CriticalPath/Admin/Post.php:216
msgid "This feature is not available for non-public post types."
msgstr "Deze functie is niet beschikbaar voor niet-openbare berichttypen."

#: inc/Engine/CriticalPath/Admin/Post.php:219
msgid "%l to use this feature."
msgstr "%l om deze feature te gebruiken."

#. translators: %s = post type.
#: inc/Engine/CriticalPath/Admin/Post.php:222
msgid "Publish the %s"
msgstr "Publiceer de %s"

#. translators: %s = post type.
#: inc/Engine/CriticalPath/Admin/Post.php:223
msgid "Enable Load CSS asynchronously in WP Rocket settings"
msgstr "CSS asynchroon laden inschakelen in WP Rocket instellingen"

#: inc/Engine/CriticalPath/Admin/Post.php:224
msgid "Enable Load CSS asynchronously in the options above"
msgstr "Schakel CSS asynchroon laden in bij de opties hierboven"

#. translators: %1$s = type of content, %2$s = error message.
#: inc/Engine/CriticalPath/APIClient.php:64
msgid "Critical CSS for %1$s not generated. Error: %2$s"
msgstr "Kritieke CSS voor %1$s niet gegenereerd. Fout: %2$s"

#. translators: %s = item URL.
#: inc/Engine/CriticalPath/APIClient.php:170
msgid ""
"Critical CSS for %1$s on mobile not generated. Error: The API returned an "
"empty response."
msgstr ""
"Kritieke CSS voor %1$s op mobiel niet gegenereerd. Fout: de API heeft een "
"leeg antwoord teruggestuurd."

#. translators: %s = item URL.
#: inc/Engine/CriticalPath/APIClient.php:173
msgid ""
"Critical CSS for %1$s not generated. Error: The API returned an empty "
"response."
msgstr ""
"Kritieke CSS voor %1$s niet gegenereerd. Fout: de API heeft een leeg "
"antwoord teruggestuurd."

#. translators: %s = item URL.
#: inc/Engine/CriticalPath/APIClient.php:185
msgid "Critical CSS for %1$s on mobile not generated."
msgstr "Kritieke CSS voor %1$s op mobiel niet gegenereerd."

#. translators: %s = item URL.
#. translators: %1$s = item URL or item type.
#: inc/Engine/CriticalPath/APIClient.php:187
#: inc/Engine/CriticalPath/ProcessorService.php:194
msgid "Critical CSS for %1$s not generated."
msgstr "Kritieke CSS voor %1$s niet gegenereerd."

#. translators: %s = URL.
#: inc/Engine/CriticalPath/APIClient.php:195
msgid ""
"Critical CSS for %1$s on mobile not generated. Error: The API returned an "
"invalid response code."
msgstr ""
"Kritieke CSS voor %1$s op mobiel niet gegenereerd. Fout: de API heeft een "
"ongeldige responscode geretourneerd."

#. translators: %s = URL.
#: inc/Engine/CriticalPath/APIClient.php:197
msgid ""
"Critical CSS for %1$s not generated. Error: The API returned an invalid "
"response code."
msgstr ""
"Kritieke CSS voor %1$s niet gegenereerd. Fout: de API heeft een ongeldige "
"responscode geretourneerd."

#. translators: %1$s = error message.
#: inc/Engine/CriticalPath/APIClient.php:205
#: inc/Engine/CriticalPath/ProcessorService.php:201
msgid "Error: %1$s"
msgstr "Fout: %1$s"

#: inc/Engine/CriticalPath/CriticalCSSSubscriber.php:177
msgid "Critical CSS generation is currently running."
msgstr "Kritieke CSS-generatie wordt momenteel uitgevoerd."

#. Translators: %1$s = opening link tag, %2$s = closing link tag.
#: inc/Engine/CriticalPath/CriticalCSSSubscriber.php:182
msgid "Go to the %1$sWP Rocket settings%2$s page to track progress."
msgstr ""
"Ga naar de %1$sWP Rocket instellingen%2$s pagina om de voortgang bij te "
"houden."

#. Translators: %1$d = number of critical CSS generated, %2$d = total number
#. of critical CSS to generate.
#: inc/Engine/CriticalPath/CriticalCSSSubscriber.php:397
msgid ""
"Critical CSS generation is currently running: %1$d of %2$d page types "
"completed. (Refresh this page to view progress)"
msgstr ""
"Genereren kritieke CSS draait momenteel: %1$d van %2$d paginatypes voltooid."
" (Vernieuw deze pagina om voortgang te bekijken)"

#. Translators: %1$d = number of critical CSS generated, %2$d = total number
#. of critical CSS to generate.
#: inc/Engine/CriticalPath/CriticalCSSSubscriber.php:473
msgid "Critical CSS generation finished for %1$d of %2$d page types."
msgstr "Genereren kritieke CSS voor %1$d van %2$d paginatypes afgerond."

#: inc/Engine/CriticalPath/CriticalCSSSubscriber.php:480
msgid "Critical CSS generation encountered one or more errors."
msgstr ""
"Bij het genereren kritieke CSS paden zijn één of meerdere fouten opgetreden."

#: inc/Engine/CriticalPath/CriticalCSSSubscriber.php:480
msgid "Learn more."
msgstr "Leer meer."

#. translators: %1$ = opening bold tag, %2$ = closing bold tag.
#: inc/Engine/CriticalPath/CriticalCSSSubscriber.php:841
msgid ""
"We highly recommend the %1$supdated Remove Unused CSS%2$s for a better CSS "
"optimization. Load CSS Asynchronously is always available as a back-up."
msgstr ""
"We raden de %1$sbijgewerkte Verwijder ongebruikte CSS%2$s ten zeerste aan "
"voor een betere CSS-optimalisatie. CSS asynchroon laden is altijd "
"beschikbaar als back-up."

#: inc/Engine/CriticalPath/CriticalCSSSubscriber.php:846
msgid "Stay with the old option"
msgstr "Bij de oude optie blijven"

#. translators: %s = item URL.
#: inc/Engine/CriticalPath/DataManager.php:68
msgid ""
"Critical CSS for %1$s on mobile not generated. Error: The destination folder"
" could not be created."
msgstr ""
"Kritieke CSS voor %1$s op mobiel niet gegenereerd. Fout: de doelmap kon niet"
" worden gemaakt."

#. translators: %s = item URL.
#: inc/Engine/CriticalPath/DataManager.php:71
msgid ""
"Critical CSS for %1$s not generated. Error: The destination folder could not"
" be created."
msgstr ""
"Kritieke CSS voor %1$s niet gegenereerd. Fout: de doelmap kon niet worden "
"gemaakt."

#: inc/Engine/CriticalPath/DataManager.php:106
msgid "Critical CSS file for mobile does not exist"
msgstr "Kritieke CSS-bestand voor mobiel bestaat niet"

#: inc/Engine/CriticalPath/DataManager.php:108
msgid "Critical CSS file does not exist"
msgstr "Kritieke CSS-bestand bestaat niet"

#: inc/Engine/CriticalPath/DataManager.php:120
msgid "Critical CSS file for mobile cannot be deleted"
msgstr "Kritieke CSS-bestand voor mobiel kan niet worden verwijderd"

#: inc/Engine/CriticalPath/DataManager.php:122
msgid "Critical CSS file cannot be deleted"
msgstr "Kritieke CSS-bestand kan niet worden verwijderd"

#. translators: %1$s = item URL or item type.
#: inc/Engine/CriticalPath/ProcessorService.php:187
msgid "Mobile Critical CSS for %1$s not generated."
msgstr "Kritieke CSS voor %1$s op mobiel niet gegenereerd."

#. translators: %1$s = Item URL or item type.
#: inc/Engine/CriticalPath/ProcessorService.php:228
msgid "Critical CSS for %s in progress."
msgstr "Kritieke CSS voor %s in uitvoering."

#. translators: %1$s = Item URL or item type.
#: inc/Engine/CriticalPath/ProcessorService.php:262
msgid "Mobile Critical CSS for %s generated."
msgstr "Kritieke CSS voor %s op mobiel gegenereerd."

#. translators: %1$s = Item URL or item type.
#: inc/Engine/CriticalPath/ProcessorService.php:273
msgid "Critical CSS for %s generated."
msgstr "Kritieke CSS voor %s gegenereerd."

#: inc/Engine/CriticalPath/ProcessorService.php:295
msgid "Critical CSS file deleted successfully."
msgstr "Kritieke CSS-bestand succesvol verwijderd."

#. translators: %1$s = Item URL or item type.
#: inc/Engine/CriticalPath/ProcessorService.php:317
msgid "Mobile Critical CSS for %1$s timeout. Please retry a little later."
msgstr ""
"Kritieke CSS voor %1$s  timeout op mobiel. Probeer het iets later opnieuw."

#. translators: %1$s = Item URL or item type.
#: inc/Engine/CriticalPath/ProcessorService.php:330
msgid "Critical CSS for %1$s timeout. Please retry a little later."
msgstr "Kritieke CSS voor %1$s timeout. Probeer het iets later opnieuw."

#: inc/Engine/CriticalPath/RESTWP.php:141
msgid "Mobile CPCSS generation not enabled."
msgstr "Mobiele CPCSS-generatie niet ingeschakeld."

#: inc/Engine/CriticalPath/RESTWPPost.php:36
#: inc/Engine/CriticalPath/RESTWPPost.php:69
msgid "Requested post does not exist."
msgstr "Opgevraagd bericht bestaat niet."

#: inc/Engine/CriticalPath/RESTWPPost.php:46
msgid "Cannot generate CPCSS for unpublished post."
msgstr "Kan geen CPCSS genereren voor ongepubliceerd bericht."

#: inc/Engine/HealthCheck/HealthCheck.php:142
msgid "Scheduled Cache Purge"
msgstr "Gepland cache legen"

#: inc/Engine/HealthCheck/HealthCheck.php:143
msgid "Scheduled Database Optimization"
msgstr "Geplande database optimalisatie"

#: inc/Engine/HealthCheck/HealthCheck.php:144
msgid "Database Optimization Process"
msgstr "Optimalisatieproces database"

#: inc/Engine/HealthCheck/HealthCheck.php:145
msgctxt "noun"
msgid "Preload"
msgstr "Preloaden"

#: inc/Engine/HealthCheck/HealthCheck.php:146
msgid "Critical Path CSS Generation Process"
msgstr "Kritieke pad CSS-generatieproces"

#. translators: %1$s = <strong>, %2$s = price, %3$s = </strong>.
#: inc/Engine/License/Renewal.php:76
msgid "Renew before it is too late, you will only pay %1$s%2$s%3$s!"
msgstr ""
"Verleng voordat het te laat is, want dan betaal je slechts %1$s%2$s%3$s!"

#. translators: %1$s = <strong>, %2$s = discount, %3$s = </strong>,%4$s =
#. <strong>, %5$s = price, %6$s=</strong>.
#: inc/Engine/License/Renewal.php:85
msgid ""
"Renew with a %1$s%2$s discount%3$s before it is too late, you will only pay "
"%4$s%5$s%6$s!"
msgstr ""
"Verleng met %1$s%2$s korting%3$s voordat het te laat is, dan betaal je "
"slechts %4$s%5$s%6$s!"

#. translators: %1$s = <strong>, %2$s = </strong>, %3$s = price.
#: inc/Engine/License/Renewal.php:139
msgid "Renew your license for 1 year now at %1$s%3$s%2$s."
msgstr "Verleng je licentie nu voor 1 jaar voor %1$s%3$s%2$s."

#. translators: %1$s = <strong>, %2$s = </strong>, %3$s = discount percentage,
#. %4$s = price.
#: inc/Engine/License/Renewal.php:152
msgid ""
"Renew your license for 1 year now and get %1$s%3$s OFF%2$s immediately: you "
"will only pay %1$s%4$s%2$s!"
msgstr ""
"Verleng je licentie nu voor 1 jaar en krijg direct %1$s%3$s UIT%2$s: je "
"betaalt slechts %1$s%4$s%2$s!"

#. translators: %1$s = <strong>, %2$s = </strong>, %3$s = discount price.
#: inc/Engine/License/Renewal.php:218
msgid "Renew before it is too late, you will pay %1$s%3$s%2$s."
msgstr "Verleng voordat het te laat is, want dan betaal je %1$s%3$s%2$s."

#. translators: %1$s = <strong>, %2$s = discount percentage, %3$s = </strong>,
#. %4$s = discount price.
#: inc/Engine/License/Renewal.php:227
msgid ""
"Renew with a %1$s%2$s discount%3$s before it is too late, you will only pay "
"%1$s%4$s%3$s!"
msgstr ""
"Verleng met %1$s%2$s korting%3$s voordat het te laat is, dan betaal je "
"slechts %1$s%4$s%3$s!"

#. translators: %1$s = <a>, %2$s = </a>.
#: inc/Engine/License/Renewal.php:542
msgid ""
"You need a valid license to continue using this feature. %1$sRenew now%2$s "
"before losing access."
msgstr ""
"Je hebt een geldige licentie nodig om deze functie te kunnen blijven "
"gebruiken. %1$sVerleng nu%2$s voordat je de toegang verliest."

#. translators: %1$s = <a>, %2$s = </a>.
#: inc/Engine/License/Renewal.php:563
msgid "You need an active license to enable this option. %1$sRenew now%2$s."
msgstr ""
"Je hebt een actieve licentie nodig om deze optie in te schakelen. "
"%1$sVerlengen nu%2$s."

#. translators: %1$s = <a>, %2$s = </a>.
#: inc/Engine/License/Renewal.php:591
msgid "You need an active license to enable this option. %1$sMore info%2$s."
msgstr ""
"Je hebt een actieve licentie nodig om deze optie in te schakelen. %1$sMeer "
"info%2$s."

#. translators: %1$s = promotion name, %2$s = <br>, %3$s = <strong>, %4$s =
#. promotion discount percentage, %5$s = </strong>.
#: inc/Engine/License/Upgrade.php:251
msgid ""
"Take advantage of %1$s to speed up more websites:%2$s get a %3$s%4$s off%5$s"
" for %3$supgrading your license to Plus or Infinite!%5$s"
msgid_plural ""
"Take advantage of %1$s to speed up more websites:%2$s get a %3$s%4$s off%5$s"
" for %3$supgrading your license to Infinite!%5$s"
msgstr[0] ""
"Profiteer van %1$s om meer sites sneller te maken:%2$s krijg een %3$s%4$s "
"korting%5$s voor %3$s het upgraden van je licentie naar Plus of "
"Oneindig!%5$s"
msgstr[1] ""
"Profiteer van %1$s om meer sites sneller te maken:%2$s krijg een %3$s%4$s "
"korting%5$s voor %3$s het upgraden van je licentie naar Infinite!%5$s"

#: inc/Engine/License/Upgrade.php:382 inc/Engine/License/Upgrade.php:405
msgid "Unlimited"
msgstr "Onbeperkt"

#. translators: %s = promotion discount percentage.
#: inc/Engine/License/views/promo-banner.php:16
msgid "%s off"
msgstr "%s uit"

#. translators: %s = promotion name.
#: inc/Engine/License/views/promo-banner.php:21
msgid "%s promotion is live!"
msgstr "%s aanbieding is live!"

#: inc/Engine/License/views/promo-banner.php:27
msgid "Hurry Up! Deal ends in:"
msgstr "Wees er snel bij! Aanbieding verloopt in:"

#: inc/Engine/License/views/promo-banner.php:31
#: inc/Engine/License/views/renewal-soon-banner.php:14
msgid "Minutes"
msgstr "Minuten"

#: inc/Engine/License/views/promo-banner.php:32
#: inc/Engine/License/views/renewal-soon-banner.php:15
msgid "Seconds"
msgstr "Seconden"

#: inc/Engine/License/views/promo-banner.php:34
#: inc/Engine/License/views/upgrade-section.php:11
msgid "Upgrade now"
msgstr "Upgrade nu"

#: inc/Engine/License/views/renewal-expired-banner-ocd-disabled.php:11
msgid "The Optimize CSS Delivery feature is disabled."
msgstr "De functie CSS-levering optimaliseren is uitgeschakeld."

#: inc/Engine/License/views/renewal-expired-banner-ocd-disabled.php:15
msgid ""
"You can no longer use the Remove Unused CSS or Load CSS asynchronously "
"options."
msgstr ""
"Je kunt de opties Ongebruikte CSS verwijderen of CSS asynchroon laden niet "
"meer gebruiken."

#. translators: %1$s = <strong>, %2$s = </strong>.
#: inc/Engine/License/views/renewal-expired-banner-ocd-disabled.php:20
msgid ""
"You need an %1$sactive license%2$s to keep optimizing your CSS delivery, "
"which addresses a PageSpeed Insights recommendation and improves your page "
"performance."
msgstr ""
"Je hebt een %1$sactieve licentie%2$s nodig om je CSS-levering te blijven "
"optimaliseren, wat een aanbeveling van PageSpeed Insights adresseert en de "
"prestaties van je pagina's verbetert."

#: inc/Engine/License/views/renewal-expired-banner-ocd-disabled.php:29
#: inc/Engine/License/views/renewal-expired-banner-ocd.php:39
#: inc/Engine/License/views/renewal-expired-banner.php:27
#: inc/Engine/License/views/renewal-soon-banner.php:31
msgid "Renew now"
msgstr "Verleng nu"

#: inc/Engine/License/views/renewal-expired-banner-ocd.php:11
msgid "You will soon lose access to some features."
msgstr "Je zult al snel de toegang tot sommige functies verliezen."

#. translators: %1$s = <strong>, %2$s = </strong>.
#: inc/Engine/License/views/renewal-expired-banner-ocd.php:18
msgid ""
"You need an %1$sactive license to continue optimizing your CSS delivery%2$s."
msgstr ""
"Je hebt een %1$sactieve licentie nodig om door te gaan met het optimaliseren"
" van je CSS levering%2$s."

#: inc/Engine/License/views/renewal-expired-banner-ocd.php:24
msgid ""
"The Remove Unused CSS and Load CSS asynchronously features are great options"
" to address the PageSpeed Insights recommendations and improve your website "
"performance."
msgstr ""
"De functies Ongebruikte CSS verwijderen en CSS asynchroon laden zijn "
"geweldige opties om de aanbevelingen van PageSpeed Insights op te volgen en "
"de prestaties van je site te verbeteren."

#. translators: %1$s = <strong>, %2$s = </strong>, %3$s = date.
#: inc/Engine/License/views/renewal-expired-banner-ocd.php:29
msgid "These features will be %1$sautomatically disabled on %3$s%2$s."
msgstr "Deze functies worden %1$sautomatisch uitgeschakeld op %3$s%2$s."

#: inc/Engine/License/views/renewal-expired-banner.php:11
msgid "Your WP Rocket license is expired!"
msgstr "Jouw WP Rocket licentie is verlopen!"

#. translators: %1$s = <strong>, %2$s = </strong>.
#: inc/Engine/License/views/renewal-expired-banner.php:18
msgid ""
"Your website could be much faster if it could take advantage of our %1$snew "
"features and enhancements%2$s. 🚀"
msgstr ""
"Je site zou veel sneller kunnen zijn als hij kon profiteren van onze "
"%1$snieuwe functies en verbeteringen%2$s. 🚀"

#. translators: %1$s = <strong>, %2$s = </strong>.
#: inc/Engine/License/views/renewal-soon-banner.php:22
msgid ""
"Your %1$sWP Rocket license is about to expire%2$s: you will soon lose access"
" to product updates and support."
msgstr ""
"Je %1$sWP Rocket licentie staat op het punt te verlopen%2$s: je verliest "
"binnenkort de toegang tot productupdates en ondersteuning."

#: inc/Engine/License/views/upgrade-popin.php:12
msgid "Speed Up More Websites"
msgstr "Maak meer sites sneller"

#. translators: %1$s = opening strong tag, %2$s = closing strong tag.
#: inc/Engine/License/views/upgrade-popin.php:19
msgid ""
"You can use WP Rocket on more websites by upgrading your license. To "
"upgrade, simply pay the %1$sprice difference%2$s between your current and "
"new licenses, as shown below."
msgstr ""
"Je kunt WP Rocket op meer sites gebruiken door je licentie te upgraden. Om "
"te upgraden betaal je simpelweg het %1$sprijsverschil%2$s tussen je huidige "
"en nieuwe licenties, zoals hieronder weergegeven."

#. translators: %1$s = opening strong tag, %2$s = closing strong tag.
#: inc/Engine/License/views/upgrade-popin.php:25
msgid ""
"%1$sN.B.%2$s: Upgrading your license does not change your expiration date"
msgstr ""
"%1$sN.B.%2$s: het upgraden van je licentie verandert je vervaldatum niet"

#. translators: %s = price.
#: inc/Engine/License/views/upgrade-popin.php:35
msgid "Save $%s"
msgstr "Bespaar $%s"

#. translators: %s = number of websites.
#: inc/Engine/License/views/upgrade-popin.php:48
msgid "%s websites"
msgstr "%s sites"

#. translators: %s = license name.
#: inc/Engine/License/views/upgrade-popin.php:54
msgid "Upgrade to %s"
msgstr "Upgrade naar %s"

#: inc/Engine/License/views/upgrade-section.php:11
msgid ""
"You can use WP Rocket on more websites by upgrading your license (you will "
"only pay the price difference between your current and new licenses)."
msgstr ""
"Je kunt WP Rocket op meer sites gebruiken door je licentie te upgraden (je "
"betaalt dan alleen het prijsverschil tussen je huidige en nieuwe licentie)."

#. translators: %1$s = plugin name.
#: inc/Engine/Media/AboveTheFold/Admin/Controller.php:143
msgid "%1$s: Critical images cleared!"
msgstr "%1$s: kritieke afbeeldingen geleegd!"

#: inc/Engine/Media/Lazyload/AdminSubscriber.php:62
msgid "LazyLoad for images"
msgstr "LazyLoad voor afbeeldingen"

#: inc/Engine/Media/Lazyload/AdminSubscriber.php:63
msgid "LazyLoad for iframes/videos"
msgstr "LazyLoad voor iframes/video's"

#: inc/Engine/Media/Lazyload/CSS/Admin/Subscriber.php:48
msgid "LazyLoad CSS backgrounds"
msgstr "LazyLoad CSS achtergronden"

#: inc/Engine/Optimization/DelayJS/Admin/SiteList.php:195
msgid "Analytics & Ads"
msgstr "Analytics en advertenties"

#: inc/Engine/Optimization/DelayJS/Admin/SiteList.php:200
msgid "Plugins"
msgstr "Plugins"

#: inc/Engine/Optimization/DelayJS/Admin/SiteList.php:205
msgid "Themes"
msgstr "Thema's"

#: inc/Engine/Optimization/DynamicLists/DynamicLists.php:101
msgid ""
"You need an active license to get the latest version of the lists from our "
"server."
msgstr ""
"Je hebt een actieve licentie nodig om de nieuwste versie van de lijsten van "
"onze server te krijgen."

#: inc/Engine/Optimization/DynamicLists/DynamicLists.php:116
msgid "Could not get updated lists from server."
msgstr "Kon geen bijgewerkte lijsten van de server krijgen."

#: inc/Engine/Optimization/DynamicLists/DynamicLists.php:125
msgid "Lists are up to date."
msgstr "Lijsten zijn up-to-date."

#: inc/Engine/Optimization/DynamicLists/DynamicLists.php:134
msgid "Could not update lists."
msgstr "Kan lijsten niet updaten."

#: inc/Engine/Optimization/DynamicLists/DynamicLists.php:143
msgid "Lists are successfully updated."
msgstr "Lijsten zijn succesvol bijgewerkt."

#: inc/Engine/Optimization/DynamicLists/ServiceProvider.php:72
msgid "Default Lists"
msgstr "Standaard lijsten"

#: inc/Engine/Optimization/DynamicLists/ServiceProvider.php:78
msgid "Delay JavaScript Execution Exclusion Lists"
msgstr "JavaScript-uitvoering uitstellen Uitsluitingslijsten"

#: inc/Engine/Optimization/DynamicLists/ServiceProvider.php:84
msgid "Incompatible plugins Lists"
msgstr "Incompatibele plugins Lijsten"

#: inc/Engine/Optimization/Minify/AdminSubscriber.php:65
msgid "Minify/combine JavaScript"
msgstr "JavaScript verkleinen/combineren"

#: inc/Engine/Optimization/Minify/CSS/AdminSubscriber.php:150
msgid "Minify CSS"
msgstr "CSS verkleinen"

#. translators: %1$s = plugin name, %2$s = table name, %3$s = <a> open tag,
#. %4$s = </a> closing tag.
#: inc/Engine/Optimization/RUCSS/Admin/Settings.php:253
msgid ""
"%1$s: Could not create the %2$s table in the database which is necessary for"
" the Remove Unused CSS feature to work. Please reach out to %3$sour "
"support%4$s."
msgstr ""
"%1$s: kon de %2$s tabel niet maken in de database die nodig is om de functie"
" Verwijder ongebruikte CSS te laten werken. Neem contact op met %3$s onze "
"support%4$s."

#. translators: %1$s = plugin name.
#: inc/Engine/Optimization/RUCSS/Admin/Subscriber.php:274
msgid "%1$s: Used CSS cache cleared!"
msgstr "%1$s: gebruikte CSS-cache geleegd!"

#. translators: %1$s = plugin name.
#: inc/Engine/Preload/Admin/Settings.php:57
msgid ""
"%1$s: The preload service is now active. After the initial preload it will "
"continue to cache all your pages whenever they are purged. No further action"
" is needed."
msgstr ""
"%1$s: de preload-service is nu actief. Na de initiële preload zal deze "
"doorgaan met het cachen van al je pagina's wanneer ze worden geleegd. Er is "
"geen verdere actie nodig."

#: inc/Engine/Preload/Cron/Subscriber.php:138
msgid "WP Rocket Preload pending jobs"
msgstr "WP Rocket preload wachtende taken"

#: inc/Engine/Preload/Cron/Subscriber.php:166
msgid "WP Rocket Preload revert stuck failed jobs"
msgstr "WP Rocket preload terugdraaien vastgelopen mislukte taken"

#: inc/Engine/Saas/Admin/AdminBar.php:73
#: inc/Engine/Saas/Admin/AdminBar.php:194
msgid "Clear Critical Images"
msgstr "Kritieke afbeeldingen legen"

#: inc/Engine/Saas/Admin/AdminBar.php:160
msgid "Clear Critical Images of this URL"
msgstr "Kritieke afbeeldingen legen van deze URL"

#: inc/Engine/Saas/Admin/AdminBar.php:163
msgid "Clear Used CSS of this URL"
msgstr "Gebruikte CSS van deze URL legen"

#: inc/Engine/Saas/Admin/AdminBar.php:193
msgid "Critical Images Cache"
msgstr "Kritieke afbeeldingen-cache"

#: inc/Engine/Saas/Admin/AdminBar.php:197
msgid "Remove Used CSS Cache"
msgstr "Gebruikte CSS-cache verwijderen"

#. translators: %1$s = plugin name, %2$s = number of seconds.
#: inc/Engine/Saas/Admin/Notices.php:104
msgid ""
"%1$s: Please wait %2$s seconds. The Remove Unused CSS service is processing "
"your pages, the plugin is optimizing LCP and the images above the fold."
msgstr ""
"%1$s: wacht %2$s seconden. De Verwijder ongebruikte CSS service verwerkt je "
"pagina's, de plugin optimaliseert LCP en de afbeeldingen boven de fold."

#. translators: %1$s = plugin name, %2$s = number of URLs, %3$s = number of
#. seconds.
#: inc/Engine/Saas/Admin/Notices.php:147
msgid ""
"%1$s: The LCP element has been optimized, and the images above the fold were excluded from lazyload. The Used CSS of your homepage has been processed.\n"
"\t\t\t WP Rocket will continue to generate Used CSS for up to %2$s URLs per %3$s second(s)."
msgstr ""
"%1$s: het LCP-element is geoptimaliseerd en de afbeeldingen boven de fold zijn uitgesloten van lazy-loaden. De Gebruikte CSS van je homepage is verwerkt.\n"
"\t\t\tWP Rocket zal Gebruikte CSS blijven genereren voor maximaal %2$s URL's per %3$s seconde(n)."

#. translators: %1$s = opening link tag, %2$s = closing link tag.
#: inc/Engine/Saas/Admin/Notices.php:160
msgid "We suggest enabling %1$sPreload%2$s for the fastest results."
msgstr ""
"We raden aan om %1$sPreloaden%2$s in te schakelen voor de snelste "
"resultaten."

#. translators: %1$s = opening link tag, %2$s = closing link tag.
#: inc/Engine/Saas/Admin/Notices.php:170
msgid "To learn more about the process check our %1$sdocumentation%2$s."
msgstr "Om meer te leren over het process, bekijk onze %1$sdocumentatie%2$s."

#: inc/Engine/Saas/Admin/Notices.php:236
msgid ""
"We couldn't generate the used CSS because you're using a nulled version of "
"WP Rocket. You need an active license to use the Remove Unused CSS feature "
"and further improve your website's performance."
msgstr ""
"We konden de gebruikte CSS niet genereren omdat je een nulled versie van WP "
"Rocket gebruikt. Je hebt een actieve licentie nodig om de functie Verwijder "
"ongebruikte CSS te gebruiken en de prestaties van je site verder te "
"verbeteren."

#. translators: %1$s = promo percentage.
#: inc/Engine/Saas/Admin/Notices.php:239
msgid "Click here to get a WP Rocket single license at %1$s off!"
msgstr "Klik hier voor een WP Rocket enkele licentie met %1$s korting!"

#. translators: %1$s = <a> open tag, %2$s = </a> closing tag.
#: inc/Engine/Saas/Admin/Notices.php:292
msgid ""
"It seems a security plugin or the server's firewall prevents WP Rocket from "
"accessing the SaaS features. IPs listed %1$shere in our documentation%2$s "
"should be added to your allowlists:"
msgstr ""
"Het lijkt erop dat een beveiligingsplugin of de firewall van de server "
"voorkomt dat WP Rocket toegang krijgt tot de SaaS-functies. IP's vermeld "
"%1$shier in onze documentatie%2$s moet worden toegevoegd aan je "
"toestemmingslijsten:"

#: inc/Engine/Saas/Admin/Notices.php:297
msgid "- In the security plugin, if you are using one"
msgstr "- In de beveiligingsplugin, als je er een gebruikt"

#: inc/Engine/Saas/Admin/Notices.php:298
msgid "- In the server's firewall. Your host can help you with this"
msgstr "- In de firewall van de server. Je host kan je hiermee helpen"

#: inc/functions/admin.php:21
msgid ""
"There seems to be an issue validating your license. Please see the error "
"message below."
msgid_plural ""
"There seems to be an issue validating your license. You can see the error "
"messages below."
msgstr[0] ""
"Er lijkt een probleem te zijn met het valideren van je licentie. Zie de "
"foutmelding hieronder."
msgstr[1] ""
"Er lijkt een probleem te zijn met het valideren van je licentie. Je kunt de "
"foutmeldingen hieronder zien."

#: inc/functions/admin.php:361
msgid "Server type:"
msgstr "Type server:"

#: inc/functions/admin.php:370
msgid "PHP version number:"
msgstr "PHP-versienummer:"

#: inc/functions/admin.php:379
msgid "WordPress version number:"
msgstr "WordPress versienummer:"

#: inc/functions/admin.php:388
msgid "WordPress multisite:"
msgstr "WordPress multisite:"

#: inc/functions/admin.php:397
msgid "Current theme:"
msgstr "Huidig thema:"

#: inc/functions/admin.php:406
msgid "Current site language:"
msgstr "Huidige taal site:"

#: inc/functions/admin.php:415
msgid "Active plugins:"
msgstr "Actieve plugins:"

#: inc/functions/admin.php:418
msgid "Plugin names of all active plugins"
msgstr "Pluginnamen van alle actieve plugins"

#: inc/functions/admin.php:424
msgid "Anonymized WP Rocket settings:"
msgstr "Geanonimiseerde WP Rocket instellingen:"

#: inc/functions/admin.php:427
msgid "Which WP Rocket settings are active"
msgstr "Welke WP Rocket instellingen actief zijn"

#: inc/functions/admin.php:433
msgid "WP Rocket license type"
msgstr "WP Rocket licentietype"

#: inc/functions/options.php:429
msgid "The provided license data are not valid."
msgstr "De verstrekte licentiegegevens zijn niet geldig."

#. Translators: %1$s = opening link tag, %2$s = closing link tag.
#: inc/functions/options.php:432
msgid "To resolve, please %1$scontact support%2$s."
msgstr ""
"Om op te lossen, neem alsjeblieft %1$scontact op met ondersteuning%2$s."

#. Translators: %1$s = opening em tag, %2$s = closing em tag, %3$s = opening
#. link tag, %4$s closing link tag.
#: inc/functions/options.php:491 inc/functions/options.php:530
msgid ""
"License validation failed. Our server could not resolve the request from "
"your website."
msgstr ""
"Licentie validatie mislukt. Onze server kon het verzoek van uw site niet "
"verwerken."

#. Translators: %1$s = opening em tag, %2$s = closing em tag, %3$s = opening
#. link tag, %4$s closing link tag.
#: inc/functions/options.php:491
msgid ""
"Try clicking %1$sValidate License%2$s below. If the error persists, follow "
"%3$sthis guide%4$s."
msgstr ""
"Probeer hieronder op %1$sLicentie valideren%2$s te klikken. Als de fout zich"
" blijft voordoen, volg dan %3$sdeze handleiding%4$s."

#. Translators: %1$s = opening link tag, %2$s = closing link tag.
#: inc/functions/options.php:507
msgid ""
"License validation failed. You may be using a nulled version of the plugin. "
"Please do the following:"
msgstr ""
"Licentie validatie mislukt. Het kan zijn dat je een nietige versie van de "
"plugin gebruikt. Doe het volgende:"

#. Translators: %1$s = opening link tag, %2$s = closing link tag.
#: inc/functions/options.php:507 inc/functions/options.php:549
msgid "Login to your WP Rocket %1$saccount%2$s"
msgstr "Meld je aan bij je WP Rocket %1$saccount%2$s"

#. Translators: %1$s = opening link tag, %2$s = closing link tag.
#: inc/functions/options.php:507 inc/functions/options.php:549
msgid "Download the zip file"
msgstr "Download het ZIP-bestand"

#. Translators: %1$s = opening link tag, %2$s = closing link tag.
#: inc/functions/options.php:507 inc/functions/options.php:549
msgid "Reinstall"
msgstr "Herinstalleren"

#. Translators: %1$s = opening link tag, %2$s = closing link tag.
#: inc/functions/options.php:507
msgid ""
"If you do not have a WP Rocket account, please %1$spurchase a license%2$s."
msgstr ""
"Als je nog geen WP Rocket account hebt, raden wij je aan om %1$seen licentie"
" aan te schaffen%2$s."

#. Translators: %1$s = opening link tag, %2$s = closing link tag.
#: inc/functions/options.php:515
msgid ""
"License validation failed. This user account does not exist in our database."
msgstr ""
"Licentie validatie mislukt. Dit gebruikersaccount bestaat niet in onze "
"database."

#. Translators: %1$s = opening link tag, %2$s = closing link tag.
#: inc/functions/options.php:515
msgid "To resolve, please contact support."
msgstr "Neem contact op met ondersteuning om dit op te lossen."

#. Translators: %1$s = opening link tag, %2$s = closing link tag.
#: inc/functions/options.php:523
msgid "License validation failed. This user account is blocked."
msgstr "Licentie validatie mislukt. Dit gebruikersaccount is geblokkeerd."

#. Translators: %1$s = opening link tag, %2$s = closing link tag.
#: inc/functions/options.php:523
msgid "Please see %1$sthis guide%2$s for more info."
msgstr "Zie %1$sdeze handleiding%2$s voor meer informatie."

#. Translators: %1$s = opening em tag, %2$s = closing em tag, %3$s = opening
#. link tag, %4$s closing link tag.
#: inc/functions/options.php:530
msgid ""
"Try clicking %1$sSave Changes%2$s below. If the error persists, follow "
"%3$sthis guide%4$s."
msgstr ""
"Probeer hieronder op %1$sWijzigingen opslaan%2$s te klikken. Als de fout "
"zich blijft voordoen, volg dan %3$sdeze handleiding%4$s."

#. Translators: %1$s = opening link tag, %2$s = closing link tag.
#: inc/functions/options.php:543
msgid "Your license is not valid."
msgstr "Je licentie is niet geldig."

#. Translators: %1$s = opening link tag, %2$s = closing link tag.
#: inc/functions/options.php:543
msgid "Make sure you have an active %1$sWP Rocket license%2$s."
msgstr "Zorg ervoor dat je een actieve %1$sWP Rocket licentie%2$s hebt."

#. Translators: %1$s = opening link tag, %2$s = closing link tag, %3$s =
#. opening link tag.
#: inc/functions/options.php:545
msgid "You have added as many sites as your current license allows."
msgstr ""
"Je hebt meer sites toegevoegd aan deze licentie dan dat je licentie "
"momenteel toestaat."

#. Translators: %1$s = opening link tag, %2$s = closing link tag, %3$s =
#. opening link tag.
#: inc/functions/options.php:545
msgid ""
"Upgrade your %1$saccount%2$s or %3$stransfer your license%2$s to this "
"domain."
msgstr ""
"Upgrade je %1$saccount%2$s of %3$sverhuis je licentie%2$s naar dit domein."

#. Translators: %1$s = opening link tag, %2$s = closing link tag.
#: inc/functions/options.php:547
msgid "This website is not allowed."
msgstr "Deze website is niet toegestaan."

#. Translators: %1$s = opening link tag, %2$s = closing link tag.
#: inc/functions/options.php:547
msgid "Please %1$scontact support%2$s."
msgstr "Neem contact op met %1$sde support%2$s."

#. Translators: %1$s = opening link tag, %2$s = closing link tag.
#: inc/functions/options.php:549
msgid "This license key is not recognized."
msgstr "Deze licentiecode word niet herkend."

#. Translators: %1$s = opening link tag, %2$s = closing link tag.
#: inc/functions/options.php:549
msgid "If the issue persists, please %1$scontact support%2$s."
msgstr "Als het probleem aanhoudt, neem dan %1$scontact op met support%2$s."

#. Translators: %s = error message returned.
#: inc/functions/options.php:555
msgid "License validation failed: %s"
msgstr "Validatie van de licentie is mislukt: %s"

#: inc/Logger/Logger.php:227 inc/Logger/Logger.php:257
msgid "The log file does not exist."
msgstr "Het logbestand bestaat niet."

#: inc/Logger/Logger.php:233 inc/Logger/Logger.php:263
msgid "The log file could not be read."
msgstr "Het logbestand kon niet gelezen worden."

#: inc/Logger/Logger.php:250
msgid "The logs are not saved into a file."
msgstr "De logs zijn niet opgeslagen in een bestand."

#. Translators: %s = Hosting name.
#: inc/ThirdParty/Hostings/Cloudways.php:82
msgid ""
"Varnish auto-purge will be automatically enabled once Varnish is enabled on "
"your %s server."
msgstr ""
"Varnish automatisch legen, wordt automatisch ingeschakeld zodra Varnish is "
"ingeschakeld op je %s server."

#. translators: %1$s = opening link tag, %2$s = closing link tag.
#: inc/ThirdParty/Hostings/Kinsta.php:159
msgid ""
"Your installation seems to be missing core Kinsta files managing Cache "
"clearing, which will prevent your Kinsta installation and WP Rocket from "
"working correctly. Please get in touch with Kinsta support through your "
"%1$sMyKinsta%2$s account to resolve this issue."
msgstr ""
"Je installatie lijkt kernbestanden van Kinsta te missen die het cache legen "
"beheert, waardoor je Kinsta installatie en WP Rocket niet correct werken. "
"Neem contact op met Kinsta support via je %1$sMyKinsta%2$s account om dit "
"probleem op te lossen."

#. translators: %1$s = plugin name.
#: inc/ThirdParty/Plugins/CDN/Cloudflare.php:127
msgid ""
"%1$s: Cloudflare's HTTP/2 Server Push is incompatible with the features of "
"Remove Unused CSS and Combine CSS files. We strongly recommend disabling it."
msgstr ""
"%1$s: Cloudflare's HTTP/2 Server Push is niet compatibel met de functies van"
" Verwijder ongebruikte CSS en CSS bestanden combineren. We raden ten zeerste"
" aan deze functie uit te schakelen."

#: inc/ThirdParty/Plugins/CDN/Cloudflare.php:170
msgid ""
"Your site is using the official Cloudflare plugin. We have enabled "
"Cloudflare auto-purge for compatibility. If you have APO activated, it is "
"also compatible."
msgstr ""
"Je site gebruikt de officiële Cloudflare-plugin. We hebben Cloudflare "
"automatisch legen ingeschakeld voor compatibiliteit. Als je APO hebt "
"geactiveerd, is dat ook compatibel."

#: inc/ThirdParty/Plugins/CDN/Cloudflare.php:171
msgid ""
"Cloudflare cache will be purged each time WP Rocket clears its cache to "
"ensure content is always up-to-date."
msgstr ""
"Cloudflare cache wordt elke keer geleegd als WP Rocket zijn cache leegt, "
"zodat de inhoud altijd up-to-date is."

#. Translators: %1$s = strong opening tag, %2$s = strong closing tag.
#: inc/ThirdParty/Plugins/CDN/Cloudflare.php:215
msgid ""
"%1$sWP Rocket:%2$s You are using \"Dynamic Cookies Cache\". Cloudflare APO "
"is not yet compatible with that feature."
msgstr ""
"%1$sWP Rocket:%2$s je gebruikt \"Dynamische cookies cache\". Cloudflare APO "
"is nog niet compatibel met die functie."

#. Translators:%1$s = opening <a> tag, %2$s = closing </a> tag.
#: inc/ThirdParty/Plugins/CDN/Cloudflare.php:221
msgid ""
"You should either disable Cloudflare APO or check with the theme/plugin "
"requiring the use of “Dynamic Cookies Cache” developers for an alternative "
"way to be page-cache friendly. %1$sMore info%2$s"
msgstr ""
"Je moet Cloudflare APO uitschakelen of navragen bij de ontwikkelaars van "
"thema's/plugin die het gebruik van “Dynamische cookies cache\" vereisen voor"
" een alternatieve manier om paginacache-vriendelijk te zijn. %1$sMeer "
"info%2$s"

#. Translators: %1$s = strong opening tag, %2$s = strong closing tag, %3$s =
#. opening <a> tag, %4$s = closing </a> tag, %5$s = opening <a> tag.
#: inc/ThirdParty/Plugins/CDN/Cloudflare.php:272
msgid ""
"%1$sWP Rocket:%2$s You are using \"Separate cache files for mobile "
"devices\". You need to activate \"Cache by Device Type\" %3$ssetting%5$s on "
"Cloudflare APO to serve the right version of the cache. %4$sMore info%5$s"
msgstr ""
"%1$sWP Rocket:%2$s je gebruikt \"Gescheiden cache-bestanden voor mobiele "
"apparaten\". Je moet \"Cache by Device Type\" %3$sinstelling%5$s op "
"Cloudflare APO activeren om de juiste versie van de cache te serveren. "
"%4$sMeer info%5$s"

#. Translators: %1$s = strong opening tag, %2$s = strong closing tag.
#: inc/ThirdParty/Plugins/CDN/Cloudflare.php:293
msgid ""
"%1$sWP Rocket:%2$s You have \"Cache by Device Type\" enabled on Cloudflare "
"APO. If you judge it necessary for the website to have a different cache on "
"mobile and desktop, we suggest you enable our “Separate Cache Files for "
"Mobiles Devices” to ensure the generated cache is accurate."
msgstr ""
"%1$sWP Rocket:%2$s je hebt \"Cache by Device Type\" ingeschakeld op "
"Cloudflare APO. Als je het nodig vindt dat de site een verschillende cache "
"heeft op mobiel en desktop, raden we je aan om onze \"Cache-bestanden voor "
"mobiele apparaten scheiden\" in te schakelen om ervoor te zorgen dat de "
"gegenereerde cache accuraat is."

#. translators: %1$s is WP Rocket plugin name, %2$s is opening <a> tag, %3$s
#. is closing </a> tag.
#: inc/ThirdParty/Plugins/ModPagespeed.php:102
msgid ""
"<strong>%1$s</strong>: Mod PageSpeed is not compatible with this plugin and "
"may cause unexpected results. %2$sMore Info%3$s"
msgstr ""
"<strong>%1$s</strong>: Mod PageSpeed is niet compatibel met deze plugin en "
"kan onverwachte resultaten veroorzaken. %2$sMeer info%3$s"

#. Translators: %1$s is an opening <strong> tag; %2$s is a closing </strong>
#. tag
#: inc/ThirdParty/Plugins/Optimization/Autoptimize.php:75
msgid ""
"%1$sWP Rocket: %2$sWe have detected that Autoptimize's JavaScript "
"Aggregation feature is enabled. WP Rocket's Delay JavaScript Execution will "
"not be applied to the file it creates. We suggest disabling %1$sJavaScript "
"Aggregation%2$s to take full advantage of Delay JavaScript Execution."
msgstr ""
"%1$sWP Rocket:%2$s we hebben gedetecteerd dat de JavaScript-"
"aggregatiefunctie van Autoptimize is ingeschakeld. WP Rocket's JavaScript-"
"uitvoering uitstellen zal niet worden toegepast op het bestand dat het "
"maakt. We raden aan om %1$sJavaScript-aggregatie%2$s uit te schakelen om "
"volledig te kunnen profiteren van Uitgestelde JavaScript-uitvoering."

#. Translators: %1$s is an opening <strong> tag; %2$s is a closing </strong>
#. tag
#: inc/ThirdParty/Plugins/Optimization/Autoptimize.php:130
msgid ""
"%1$sWP Rocket: %2$sWe have detected that Autoptimize's Aggregate Inline CSS "
"feature is enabled. WP Rocket's Load CSS Asynchronously will not work "
"correctly. We suggest disabling %1$sAggregate Inline CSS%2$s to take full "
"advantage of Load CSS Asynchronously Execution."
msgstr ""
"%1$sWP Rocket:%2$s we hebben gedetecteerd dat de functie Aggregate Inline "
"CSS van Autoptimize is ingeschakeld. WP Rocket's CSS asynchroon laden zal "
"niet correct werken. We raden aan om %1$sAggregate Inline CSS%2$s uit te "
"schakelen om volledig te kunnen profiteren van Load CSS Asynchronously "
"Execution."

#. translators: %1$s = opening <a> tag, %2$s = closing </a> tag.
#: inc/ThirdParty/Plugins/Optimization/Ezoic.php:45
msgid ""
"This plugin blocks WP Rocket's caching and optimizations. Deactivate it and "
"use %1$sEzoic's nameserver integration%2$s instead."
msgstr ""
"Deze plugin blokkeert de caching en optimalisaties van WP Rocket. Deactiveer"
" hem en gebruik in plaats daarvan %1$sEzoic's nameserverintegratie%2$s."

#. Translators: %s = Plugin name.
#: inc/ThirdParty/Plugins/Optimization/Hummingbird.php:78
msgctxt "Hummingbird notice"
msgid ""
"Please deactivate the following %s option which conflicts with WP Rocket "
"features:"
msgid_plural ""
"Please deactivate the following %s options which conflict with WP Rocket "
"features:"
msgstr[0] ""
"Deactiveer de volgende %s optie die conflicteert met de functies van WP "
"Rocket:"
msgstr[1] ""
"Deactiveer de volgende %s opties die conflicteren met de functies van WP "
"Rocket:"

#. Translators: %1$s = Plugin name, %2$s = <em>, %3$s = </em>.
#: inc/ThirdParty/Plugins/Optimization/Hummingbird.php:151
msgctxt "Hummingbird notice"
msgid ""
"%1$s %2$sdisable emoji%3$s conflicts with WP Rockets %2$sdisable emoji%3$s"
msgstr ""
"%1$s %2$semoji uitschakelen%3$s conflicteert met WP Rocket's %2$semoji "
"uitschakelen%3$s"

#. Translators: %1$s = Plugin name, %2$s = <em>, %3$s = </em>.
#: inc/ThirdParty/Plugins/Optimization/Hummingbird.php:187
msgctxt "Hummingbird notice"
msgid ""
"%1$s %2$sGZIP compression%3$s conflicts with WP Rocket %2$sGZIP "
"compression%3$s"
msgstr ""
"%1$s %2$sGZIP-compressie%3$s conflicteert met WP Rocket %2$sGZIP-"
"compressie%3$s"

#. Translators: %1$s = Plugin name, %2$s = <em>, %3$s = </em>.
#: inc/ThirdParty/Plugins/Optimization/Hummingbird.php:223
msgctxt "Hummingbird notice"
msgid ""
"%1$s %2$sbrowser caching%3$s conflicts with WP Rocket %2$sbrowser "
"caching%3$s"
msgstr ""
"%1$s %2$sbrowser caching%3$s conflicteert met WP Rocket %2$sbrowser "
"caching%3$s"

#. Translators: %1$s = Plugin name, %2$s = <em>, %3$s = </em>.
#: inc/ThirdParty/Plugins/Optimization/Hummingbird.php:255
msgctxt "Hummingbird notice"
msgid ""
"%1$s %2$spage caching%3$s conflicts with WP Rocket %2$spage caching%3$s"
msgstr ""
"%1$s %2$spage caching%3$s conflicteert met WP Rocket %2$spage caching%3$s"

#. Translators: %1$s = Plugin name, %2$s = <em>, %3$s = </em>.
#: inc/ThirdParty/Plugins/Optimization/Hummingbird.php:289
msgctxt "Hummingbird notice"
msgid ""
"%1$s %2$sasset optimization%3$s conflicts with WP Rocket %2$sfile "
"optimization%3$s"
msgstr ""
"%1$s %2$sasset optimalisatie%3$s conflicten met WP Rocket "
"%2$sbestandsoptimalisatie%3$s"

#: inc/ThirdParty/Plugins/Optimization/Perfmatters.php:38
msgid ""
"Remove Unused CSS is currently activated in Perfmatters. If you want to use "
"WP Rocket's Remove Unused CSS feature, disable this option in Perfmatters."
msgstr ""
"Verwijder ongebruikte CSS is momenteel geactiveerd in Perfmatters. Als je de"
" functie Verwijder ongebruikte CSS van WP Rocket wilt gebruiken, schakel "
"deze optie dan uit in Perfmatters."

#: inc/ThirdParty/Plugins/Optimization/RapidLoad.php:39
msgid ""
"Automated unused CSS removal is currently activated in RapidLoad Power-Up "
"for Autoptimize. If you want to use WP Rocket's Remove Unused CSS feature, "
"disable the  RapidLoad Power-Up for Autoptimize plugin."
msgstr ""
"Geautomatiseerde verwijdering van ongebruikte CSS is momenteel geactiveerd "
"in RapidLoad Power-Up voor Autoptimize. Als je de functie Verwijder "
"ongebruikte CSS van WP Rocket wilt gebruiken, schakel dan de plugin "
"RapidLoad Power-Up for Autoptimize uit."

#. translators: %1$s = plugin name.
#: inc/ThirdParty/Plugins/Optimization/WPMeteor.php:42
msgid ""
"Delay JS is currently activated in %1$s. If you want to use WP Rocket’s "
"delay JS, disable %1$s"
msgstr ""
"JS uitstellen is momenteel geactiveerd in %1$s. Als je WP Rocket's delay JS "
"wilt gebruiken, schakel dan %1$s uit"

#: inc/ThirdParty/Plugins/Smush.php:108 inc/ThirdParty/Plugins/Smush.php:126
msgid "Smush"
msgstr "Smush"

#: inc/ThirdParty/Themes/Avada.php:107
msgid "Avada"
msgstr "Avada"

#: inc/ThirdParty/Themes/Divi.php:293
msgid ""
"Your Divi template was updated. Clear the Used CSS if the layout, design or "
"CSS styles were changed."
msgstr ""
"Je Divi template is bijgewerkt. Leeg de Gebruikte CSS als de lay-out, het "
"ontwerp of de CSS-stijlen werden gewijzigd."

#: views/cpcss/activate-cpcss-mobile.php:13
msgid "Load CSS asynchronously for mobile"
msgstr "CSS asynchroon laden voor mobiel"

#: views/cpcss/activate-cpcss-mobile.php:14
msgid ""
"Your website currently uses the same Critical Path CSS for both desktop and "
"mobile."
msgstr ""
"Je site gebruikt momenteel hetzelfde Kritieke pad CSS voor zowel desktop als"
" mobiel."

#: views/cpcss/activate-cpcss-mobile.php:15
msgid "Click the button to enable mobile-specific CPCSS for your site."
msgstr ""
"Klik op de knop om mobiel-specifieke CPCSS in te schakelen voor je site."

#. translators: %1$s = opening link tag, %2$s = closing link tag.
#: views/cpcss/activate-cpcss-mobile.php:20
#: views/settings/enable-google-fonts.php:19
#: views/settings/mobile-cache.php:20
msgid ""
"This is a one-time action and this button will be removed afterwards. "
"%1$sMore info%2$s"
msgstr ""
"Dit is een eenmalige actie en deze knop wordt daarna verwijderd. %1$sMeer "
"info%2$s"

#. translators: %1$s = opening link tag, %2$s = closing link tag.
#: views/cpcss/activate-cpcss-mobile.php:30
msgid ""
"Your site is now using mobile-specific critical path CSS. %1$sMore info%2$s"
msgstr ""
"Je site gebruikt nu mobiel-specifieke kritieke pad CSS. %1$sMeer info%2$s"

#: views/cpcss/activate-cpcss-mobile.php:39
msgid "Generate Mobile Specific CPCSS"
msgstr "Mobiel-specifieke CPCSS genereren"

#: views/cpcss/metabox/container.php:19
msgid "Critical Path CSS"
msgstr "Kritieke pad CSS"

#. translators: %1$s = opening link tag, %2$s = closing link tag.
#: views/cpcss/metabox/generate.php:23
msgid "Generate specific Critical Path CSS for this post. %1$sMore info%2$s"
msgstr ""
"Specifieke kritieke pad CSS voor dit bericht genereren. %1$sMeer info%2$s"

#. translators: %1$s = opening link tag, %2$s = closing link tag.
#: views/cpcss/metabox/generate.php:33
msgid "This post uses specific Critical Path CSS. %1$sMore info%2$s"
msgstr ""
"Dit bericht maakt gebruik van specifieke Kritieke pad CSS. %1$sMeer info%2$s"

#: views/cpcss/metabox/generate.php:56
msgid "Revert back to the default CPCSS"
msgstr "Terugkeren naar de standaard CPCSS"

#: views/deactivation-intent/form.php:21
msgid "Facing an issue?"
msgstr "Ervaar je problemen?"

#: views/deactivation-intent/form.php:24
msgid ""
"It is not always necessary to deactivate WP Rocket when facing any issues. "
"Most of them can be fixed by deactivating only some options."
msgstr ""
"Het is niet altijd nodig om WP Rocket te deactiveren als je problemen "
"ondervindt. De meeste kunnen worden opgelost door slechts enkele opties uit "
"te schakelen."

#. translators: %1$s = opening strong tag, %2$s = closing strong tag.
#: views/deactivation-intent/form.php:29
msgid ""
"Our advice? Instead of deactivating WP Rocket, use our %1$sSafe Mode%2$s to "
"quickly disable LazyLoad, File Optimization, and CDN options. Then check to "
"see if your issue is resolved."
msgstr ""
"Ons advies? In plaats van WP Rocket te deactiveren, gebruik je onze "
"%1$sVeilige modus%2$s om snel LazyLoad, Bestandsoptimalisatie en CDN-opties "
"uit te schakelen. Controleer vervolgens of je probleem is opgelost."

#: views/deactivation-intent/form.php:35
msgid "Do you want to use our Safe Mode to troubleshoot WP Rocket?"
msgstr ""
"Wil je onze Veilige modus gebruiken om problemen met WP Rocket op te lossen?"

#. translators: %1$s = opening strong tag, %2$s = closing strong tag.
#: views/deactivation-intent/form.php:55
msgid "Yes, apply \"%1$sSafe Mode%2$s\""
msgstr "Ja, \"%1$sVeilige modus%2$s\" toepassen"

#. translators: %1$s = opening strong tag, %2$s = closing strong tag.
#: views/deactivation-intent/form.php:68
msgid ""
"and export WP Rocket settings %1$s(Recommended as current settings will be "
"deleted)%2$s"
msgstr ""
"en exporteer WP Rocket instellingen %1$s(Aanbevolen omdat de huidige "
"instellingen worden verwijderd)%2$s"

#: views/deactivation-intent/form.php:76
msgid "No, deactivate and snooze this message for"
msgstr "Nee, deactiveer en sluimer dit bericht voor"

#: views/deactivation-intent/form.php:78
msgid "1 day"
msgstr "1 dag"

#: views/deactivation-intent/form.php:79
msgid "7 days"
msgstr "7 dagen"

#: views/deactivation-intent/form.php:80
msgid "30 days"
msgstr "30 dagen"

#: views/deactivation-intent/form.php:81
msgid "Forever"
msgstr "Voor altijd"

#: views/deactivation-intent/form.php:87
msgid "Cancel"
msgstr "Annuleren"

#: views/deactivation-intent/form.php:88
msgid "Confirm"
msgstr "Bevestigen"

#. translators: %1$s = <strong>, %2$s = plugin version, %3$s = </strong>, %4$s
#. = <a>, %5$s = </a>, %6$s = <a>.
#: views/plugins/update-renewal-expired-notice.php:27
msgid ""
" %1$sWP Rocket %2$s%3$s is available. %4$sLearn more%5$s about the updates "
"and enhancements of this major version. You need an active license to use "
"them on your website, don’t miss out! %6$sRenew Now%5$s"
msgstr ""
" %1$sWP Rocket %2$s%3$s is beschikbaar. %4$sLeer meer%5$s over de updates en"
" verbeteringen van deze grote versie. Je hebt een actieve licentie nodig om "
"ze op je site te gebruiken, mis het niet! %6$sVerleng nu%5$s"

#: views/settings/dynamic-lists-update.php:14
msgid "Update Inclusion and Exclusion Lists"
msgstr "Lijsten met insluitingen en uitsluitingen updaten"

#. translators: %1$s = opening link tag, %2$s = closing link tag.
#: views/settings/dynamic-lists-update.php:19
msgid ""
"Compatibility lists are downloaded automatically every week. Click the "
"button if you want to update them manually. %1$sMore info%2$s"
msgstr ""
"Compatibiliteitslijsten worden elke week automatisch gedownload. Klik op de "
"knop als je ze handmatig wilt updaten. %1$sMeer info%2$s"

#: views/settings/dynamic-lists-update.php:29
msgid "Update lists"
msgstr "Lijsten updaten"

#: views/settings/enable-google-fonts.php:13
msgid "Enable Google Font Optimization"
msgstr "Google Font optimalisatie activeren"

#: views/settings/enable-google-fonts.php:14
msgid ""
"Improves font performance and combines multiple font requests to reduce the "
"number of HTTP requests."
msgstr ""
"Verbetert de lettertypeprestaties en combineert meerdere lettertypeverzoeken"
" om het aantal HTTP-verzoeken te verminderen."

#. translators: %1$s = opening link tag, %2$s = closing link tag.
#: views/settings/enable-google-fonts.php:29
msgid ""
"Google Fonts Optimization is now enabled for your site. %1$sMore info%2$s"
msgstr ""
"Google Fonts Optimalisatie is nu ingeschakeld voor je site. %1$sMeer "
"info%2$s"

#: views/settings/enable-google-fonts.php:38
msgid "Optimize Google Fonts"
msgstr "Optimaliseer Google Fonts"

#: views/settings/fields/cache-lifespan.php:26
msgid "Clear cache after"
msgstr "Cache legen na"

#: views/settings/fields/cnames.php:58 views/settings/fields/cnames.php:92
#: views/settings/fields/cnames.php:122
msgid "CSS & JavaScript"
msgstr "CSS & JavaScript"

#: views/settings/fields/cnames.php:59 views/settings/fields/cnames.php:93
#: views/settings/fields/cnames.php:123
msgid "JavaScript"
msgstr "JavaScript"

#: views/settings/fields/cnames.php:60 views/settings/fields/cnames.php:94
#: views/settings/fields/cnames.php:124
msgid "CSS"
msgstr "CSS"

#: views/settings/fields/import-form.php:29
msgid "Import settings"
msgstr "Instellingen importeren"

#: views/settings/fields/one-click-addon.php:43
#: views/settings/fields/rocket-addon.php:43
msgid "Add-on status"
msgstr "Status add-on"

#: views/settings/fields/rocket-addon.php:72
msgid "Modify options"
msgstr "Opties aanpassen"

#: views/settings/fields/rocket-cdn.php:29
#: views/settings/fields/rocket-cdn.php:41
msgid "CDN CNAME"
msgstr "CDN CNAME"

#. translators: %s is a "Learn more" link.
#: views/settings/fields/rocket-cdn.php:62
msgid "Purges RocketCDN cached resources for your website. %s"
msgstr "RocketCDN gecachete bronnen legen van je site. %s"

#. translators: %s is a "Learn more" link.
#: views/settings/fields/rocket-cdn.php:63
#: views/settings/page-sections/cloudflare.php:36
#: views/settings/page-sections/sucuri.php:37
msgid "Learn more"
msgstr "Meer weten"

#: views/settings/fields/rocket-cdn.php:72
msgid "Clear all RocketCDN cache files"
msgstr "Alle RocketCDN cache-bestanden legen"

#: views/settings/mobile-cache.php:11
msgid "Mobile Cache"
msgstr "Mobiele cache"

#: views/settings/mobile-cache.php:14
msgid "Speed your site for mobile visitors."
msgstr "Versnel je site voor mobiele bezoekers."

#: views/settings/mobile-cache.php:28
msgid "Mobile Cache is now enabled for your site."
msgstr "Mobiele cache is nu ingeschakeld voor je site."

#: views/settings/mobile-cache.php:34
msgid "Enable Mobile Cache"
msgstr "Mobiele cache inschakelen"

#: views/settings/page-sections/cloudflare.php:27
msgid "Cloudflare Cache"
msgstr "Cloudflare cache"

#. translators: %s is a "Learn more" link.
#: views/settings/page-sections/cloudflare.php:35
#: views/settings/page-sections/sucuri.php:36
msgid "Purges cached resources for your website. %s"
msgstr "Leegt gecachete bronnen op je website. %s"

#. translators: %s is a "Learn more" link.
#: views/settings/page-sections/cloudflare.php:36
msgid "https://support.cloudflare.com/hc/en-us/articles/200169246"
msgstr "https://support.cloudflare.com/hc/en-us/articles/200169246"

#: views/settings/page-sections/cloudflare.php:45
msgid "Clear all Cloudflare cache files"
msgstr "Alle Cloudflare cache-bestanden legen"

#: views/settings/page-sections/dashboard.php:38
msgid "Congratulations!"
msgstr "Gefeliciteerd!"

#: views/settings/page-sections/dashboard.php:40
msgid "WP Rocket is now activated and already working for you."
msgstr "WP Rocket is nu geactiveerd en en is al voor je aan het werk."

#: views/settings/page-sections/dashboard.php:42
msgid "Your website should be loading faster now!"
msgstr "Je site zou nu sneller moeten laden!"

#: views/settings/page-sections/dashboard.php:44
msgid ""
"To guarantee fast websites, WP Rocket automatically applies 80% of web "
"performance best practices."
msgstr ""
"Om snelle sites te garanderen, past WP Rocket automatisch 80% of best "
"practices voor webprestaties toe."

#: views/settings/page-sections/dashboard.php:44
msgid ""
"We also enable options that provide immediate benefits to your website."
msgstr ""
"We maken ook opties mogelijk die direct voordeel opleveren voor je site."

#: views/settings/page-sections/dashboard.php:45
msgid "Continue to the options to further optimize your site!"
msgstr "Ga verder naar de opties om je site verder te optimaliseren!"

#: views/settings/page-sections/dashboard.php:62
msgid "My Account"
msgstr "Mijn account"

#: views/settings/page-sections/dashboard.php:68
msgid "Refresh info"
msgstr "Informatie vernieuwen"

#: views/settings/page-sections/dashboard.php:86
msgid "with"
msgstr "met"

#: views/settings/page-sections/dashboard.php:100
msgid "Expiration Date"
msgstr "Einddatum"

#: views/settings/page-sections/dashboard.php:110
msgid "View my account"
msgstr "Mijn account bekijken"

#: views/settings/page-sections/dashboard.php:137
msgid "Quick Actions"
msgstr "Snelle acties"

#: views/settings/page-sections/dashboard.php:144
msgid "Remove all cached files"
msgstr "Alle gecachete bestanden verwijderen"

#: views/settings/page-sections/dashboard.php:164
#: views/settings/page-sections/dashboard.php:170
msgid "Regenerate Critical CSS"
msgstr "Kritieke CSS opnieuw genereren"

#: views/settings/page-sections/dashboard.php:196
msgid "Frequently Asked Questions"
msgstr "Veelgestelde vragen"

#: views/settings/page-sections/dashboard.php:210
msgid "Still cannot find a solution?"
msgstr "Geen oplossing gevonden?"

#: views/settings/page-sections/dashboard.php:211
msgid ""
"Submit a ticket and get help from our friendly and knowledgeable Rocketeers."
msgstr ""
"Dien een ticket in en krijg hulp van onze vriendelijke en deskundige "
"Rocketeers."

#: views/settings/page-sections/dashboard.php:219
msgid "Ask support"
msgstr "Vraag het de support"

#: views/settings/page-sections/database.php:25
msgid "Backup your database before you run a cleanup!"
msgstr "Backup je database voordat je het opschonen begint!"

#: views/settings/page-sections/database.php:26
msgid ""
"Once a database optimization has been performed, there is no way to undo it."
msgstr ""
"Nadat een database optimalisatie is uitgevoerd is er geen mogelijkheid meer "
"om dit ongedaan te maken."

#: views/settings/page-sections/database.php:28
msgid "Save Changes and Optimize"
msgstr "Wijzigingen opslaan en optimaliseer"

#. Translators: %1$s = <strong>, %2$s = </strong>, %3$s = <span
#. class="imagify-name">, %4$s = </span>.
#: views/settings/page-sections/imagify.php:21
msgid ""
"%1$sWP ROCKET%2$s created %3$sIMAGIFY%4$s %1$sfor best-in-class image "
"optimization.%2$s"
msgstr ""
"%1$sWP ROCKET%2$s creëerde %3$sIMAGIFY%4$s %1$svoor de beste "
"beeldoptimalisatie in zijn klasse.%2$s"

#: views/settings/page-sections/imagify.php:24
msgid ""
"Compress image to make your website faster, all while maintaining image "
"quality."
msgstr ""
"Comprimeer afbeeldingen om je site sneller te maken, terwijl de "
"afbeeldingskwaliteit behouden blijft."

#: views/settings/page-sections/imagify.php:25
msgid "More on Imagify:"
msgstr "Meer over Imagify:"

#: views/settings/page-sections/imagify.php:27
msgid "Imagify Plugin Page"
msgstr "Imagify Plugin Pagina"

#: views/settings/page-sections/imagify.php:28
msgid "Imagify Website"
msgstr "Imagify Website"

#: views/settings/page-sections/imagify.php:29
msgid "Review of Image Compression Plugins"
msgstr "Overzicht van plugins voor beeldcompressie"

#: views/settings/page-sections/imagify.php:38
msgid "Install Imagify"
msgstr "Installeer Imagify"

#: views/settings/page-sections/license.php:22
msgid "WP Rocket was not able to automatically validate your license."
msgstr "WP Rocket kon je licentie niet automatisch valideren."

#. translators: %1$s = tutorial URL, %2$s = support URL.
#: views/settings/page-sections/license.php:29
msgid "Follow this %1$s, or contact %2$s to get the engine started."
msgstr ""
"Volg deze %1$s, of neem contact op met %2$s om de motor te laten starten."

#. translators: %1$s = <a href=", %2$s =  tutorial href,  %3$s =  "
#. target="_blank">,  %4$s =  </a>.
#: views/settings/page-sections/license.php:32
msgid "%1$s%2$s%3$stutorial%4$s"
msgstr "%1$s%2$s%3$stutorial%4$s"

#: views/settings/page-sections/license.php:34
msgid ""
"https://docs.wp-rocket.me/article/100-resolving-problems-with-license-"
"validation/?utm_source=wp_plugin&utm_medium=wp_rocket"
msgstr ""
"https://docs.wp-rocket.me/article/100-resolving-problems-with-license-"
"validation/?utm_source=wp_plugin&utm_medium=wp_rocket"

#. translators: %1$s = <a href=", %2$s =  support href,  %3$s =  "
#. target="_blank">,  %4$s =  </a>.
#: views/settings/page-sections/license.php:40
msgid "%1$s%2$s%3$ssupport%4$s"
msgstr "%1$s%2$s%3$ssupport%4$s"

#: views/settings/page-sections/sucuri.php:46
msgid "Clear all Sucuri cache files"
msgstr "Alle Sucuri cache-bestanden legen"

#. translators: %1$s = formatted file size, %2$s = formatted number of entries
#. (don't use %2$d).
#: views/settings/page-sections/tools.php:20
msgid "Files size: %1$s. Number of entries: %2$s."
msgstr "Bestandsformaat: %1$s. Aantal items: %2$s."

#. translators: %1$s = opening <a> tag, %2$s = closing </a> tag.
#: views/settings/page-sections/tools.php:23
msgid "%1$sDownload the file%2$s."
msgstr "%1$sDownload het bestand%2$s."

#. translators: %1$s = opening <a> tag, %2$s = closing </a> tag.
#: views/settings/page-sections/tools.php:26
msgid "%1$sDelete the file%2$s."
msgstr "%1$sVerwijder het bestand%2$s."

#: views/settings/page-sections/tools.php:37
msgid "Export settings"
msgstr "Instellingen exporteren"

#: views/settings/page-sections/tools.php:38
msgid "Download a backup file of your settings"
msgstr "Download een back-upbestand van je instellingen"

#: views/settings/page-sections/tools.php:46
msgid "Download settings"
msgstr "Instellingen downloaden"

#: views/settings/page-sections/tools.php:60
msgid "Rollback"
msgstr "Terugdraaien"

#. translators: %s = WP Rocket version number.
#: views/settings/page-sections/tools.php:64
msgid "Has version %s caused an issue on your website?"
msgstr "Heeft versie %s voor problemen op je website gezorgd?"

#. translators: %s = <br>.
#: views/settings/page-sections/tools.php:69
msgid ""
"You can rollback to the previous major version here.%sThen send us a support"
" request."
msgstr ""
"Je kunt hier teruggaan naar de vorige hoofdversie.%sStuur ons dan een "
"ondersteuningsverzoek."

#. translators: %s = WP Rocket previous version.
#: views/settings/page-sections/tools.php:80
msgid "Reinstall version %s"
msgstr "Versie %s opnieuw installeren"

#: views/settings/page-sections/tools.php:106
msgid "Debug mode"
msgstr "Debug modus"

#: views/settings/page-sections/tools.php:111
msgid "Create a debug log file."
msgstr "Creëer een debug log-bestand."

#: views/settings/page-sections/tutorials.php:13
#: views/settings/partials/getting-started.php:18
msgid "Getting Started"
msgstr "Aan de slag"

#: views/settings/page-sections/tutorials.php:15
msgid "Getting Started with WP Rocket"
msgstr "Aan de slag met WP Rocket"

#: views/settings/page-sections/tutorials.php:16
#: views/settings/partials/getting-started.php:12
msgid "Finding the Best Settings for Your Site"
msgstr "De beste instellingen voor je site vinden"

#: views/settings/page-sections/tutorials.php:17
#: views/settings/partials/getting-started.php:13
msgid "How to Check if WP Rocket is Caching Your Site"
msgstr "Hoe je kunt controleren of WP Rocket je site cachet"

#: views/settings/page-sections/tutorials.php:18
#: views/settings/partials/getting-started.php:14
msgid "How to Measure the Speed of Your Site"
msgstr "Hoe je de snelheid van je site kunt meten"

#: views/settings/page-sections/tutorials.php:19
msgid "How Preloading Works"
msgstr "Hoe preloaden werkt"

#: views/settings/page-sections/tutorials.php:23
msgid "Passing the Core Web vitals"
msgstr "Core Web vitals gepasseerd"

#: views/settings/page-sections/tutorials.php:25
msgid "How to improve LCP with WP Rocket"
msgstr "Hoe LCP verbeteren met WP Rocket"

#: views/settings/page-sections/tutorials.php:26
msgid "How to improve FID with WP Rocket"
msgstr "Hoe je FID kunt verbeteren met WP Rocket"

#: views/settings/page-sections/tutorials.php:27
msgid "How to improve CLS with WP Rocket"
msgstr "Hoe je CLS kunt verbeteren met WP Rocket"

#: views/settings/page-sections/tutorials.php:31
msgid "Troubleshooting"
msgstr "Problemen oplossen"

#: views/settings/page-sections/tutorials.php:33
msgid "Troubleshooting Display Issues with File Optimization"
msgstr "Problemen met weergave oplossen met bestandsoptimalisatie"

#: views/settings/page-sections/tutorials.php:34
msgid "How to Find the Right JavaScript to Exclude"
msgstr "Hoe je het juiste JavaScript vindt om uit te sluiten"

#: views/settings/page-sections/tutorials.php:35
msgid "How External Content Slows Your Site"
msgstr "Hoe externe inhoud je site vertraagt"

#: views/settings/page-sections/tutorials.php:41
msgid "Set Up the Cloudflare Add-on"
msgstr "Stel de Cloudflare add-on in"

#: views/settings/page.php:16
msgid "WP Rocket Settings"
msgstr "WP Rocket instellingen"

#. translators: %s = Plugin version number.
#: views/settings/page.php:30
msgid "version %s"
msgstr "versie %s"

#: views/settings/page.php:60
msgid "Show Sidebar"
msgstr "Zijbalk weergeven"

#. translators: %1$s = <strong>, %2$s = </strong>.
#: views/settings/page.php:82
msgid ""
"Below is a detailed view of all data WP Rocket will collect %1$sif granted "
"permission.%2$s"
msgstr ""
"Hieronder vind je een gedetailleerd overzicht van alle gegevens die WP "
"Rocket zal verzamelen %1$sals je daar toestemming voor geeft.%2$s"

#: views/settings/page.php:88
msgid ""
"WP Rocket will never transmit any domain names or email addresses (except "
"for license validation), IP addresses, or third-party API keys."
msgstr ""
"WP Rocket zal nooit domeinnamen of e-mailadressen (behalve voor "
"licentievalidatie), IP-adressen of API-sleutels van derden doorgeven."

#: views/settings/page.php:90
msgid "Activate Rocket analytics"
msgstr "Activeer Rocket analytics"

#: views/settings/partials/documentation.php:15
msgid "It is a great starting point to fix some of the most common issues."
msgstr ""
"Het is een geweldig startpunt om een aantal van de meest voorkomende "
"problemen op te lossen."

#: views/settings/partials/documentation.php:22
msgid "Read the documentation"
msgstr "Lees de documentatie"

#: views/settings/partials/getting-started.php:11
msgid "What WP Rocket Does For You By Default"
msgstr "Wat WP Rocket standaard voor je doet"

#: views/settings/partials/sidebar.php:12
msgid "How to correctly measure your website’s loading time"
msgstr "Zo kan je de laadtijden correct meten"

#: views/settings/partials/sidebar.php:14
msgid "Check our tutorial and learn how to measure the speed of your site."
msgstr ""
"Bekijk onze tutorial en leer hoe je de snelheid van je site kunt meten."

#: views/settings/partials/sidebar.php:15
#: views/settings/partials/sidebar.php:24
msgid "Read our guide"
msgstr "Onze gids lezen"

#: views/settings/partials/sidebar.php:18
msgid "Learn about optimal WP Rocket settings for mobile."
msgstr "Leer meer over de optimale instellingen van WP Rocket voor mobiel."

#: views/settings/partials/sidebar.php:27
msgid "Test and Improve Google Core Web Vitals for WordPress."
msgstr "Test en verbeter Google Core Web Vitals voor WordPress."

#: views/settings/partials/sidebar.php:28
msgid "Read more"
msgstr "Lees meer"

#: views/settings/partials/sidebar.php:33
msgid "You have not activated logged-in user cache."
msgstr "Je hebt ingelogde gebruikerscache niet ingeschakeld."

#: views/settings/partials/sidebar.php:34
msgid ""
"Use a private browser to check your website's speed and visual appearance."
msgstr ""
"Gebruik een privé browser, zoals incognito modus in Chrome, om de snelheid "
"en visuele weergave van je site te testen."

#: views/settings/sections/addons-container.php:24
#: views/settings/sections/fields-container.php:28
msgid "Need Help?"
msgstr "Hulp nodig?"
