msgid ""
msgstr ""
"Project-Id-Version: WP Rocket 3.4\n"
"Report-Msgid-Bugs-To: http://wp-rocket.me/\n"
"Last-Translator: Alice O., 2020\n"
"Language-Team: Italian (Italy) (https://www.transifex.com/wp-media/teams/18133/it_IT/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"POT-Creation-Date: 2020-08-25 17:35-0400\n"
"PO-Revision-Date: 2019-08-26 15:14+0000\n"
"Language: it_IT\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Generator: Poedit 1.8.11\n"
"X-Poedit-Basepath: ..\n"
"X-Poedit-KeywordsList: __;_e;_x:1,2c;_ex:1,2c;_n:1,2;_nx:1,2,4c;_n_noop:1,2;_nx_noop:1,2,3c;esc_attr__;esc_html__;esc_attr_e;esc_html_e;esc_attr_x:1,2c;esc_html_x:1,2c\n"
"X-Poedit-SearchPath-0: .\n"
"X-Poedit-SearchPathExcluded-0: assets\n"
"X-Poedit-SearchPathExcluded-1: src\n"
"X-Poedit-SearchPathExcluded-2: vendor\n"
"X-Poedit-SearchPathExcluded-3: node_modules\n"
"X-Poedit-SearchPathExcluded-4: tests\n"
"X-Poedit-SourceCharset: UTF-8\n"
"X-Textdomain-Support: yes\n"

#: inc/3rd-party/hosting/flywheel.php:18
#: inc/3rd-party/hosting/pressidium.php:18
#: inc/3rd-party/hosting/wp-serveur.php:27
#: inc/ThirdParty/Hostings/Cloudways.php:90
#: inc/ThirdParty/Hostings/Dreampress.php:44
#: inc/ThirdParty/Hostings/Godaddy.php:63
#: inc/ThirdParty/Hostings/O2Switch.php:49
#: inc/ThirdParty/Hostings/OneCom.php:137
#: inc/ThirdParty/Hostings/ProIsp.php:51
#: inc/ThirdParty/Hostings/Savvii.php:50
#: inc/ThirdParty/Hostings/WPEngine.php:47
#: inc/ThirdParty/Hostings/WPXCloud.php:51
#: inc/deprecated/3.6.php:697
#: inc/deprecated/3.6.php:997
#: inc/deprecated/3.9.php:22
#, php-format
msgid "Your site is hosted on %s, we have enabled Varnish auto-purge for compatibility."
msgstr "Il tuo sito è ospitato su %s, e per garantire la compatibilità abbiamo abilitato l'opzione per l'eliminazione automatica della cache Varnish."

#: inc/deprecated/3.12.php:332
msgid "Jetpack XML Sitemaps"
msgstr "Sitemap XML Jetpack"

#: inc/deprecated/3.12.php:334
msgid "Preload the sitemap from the Jetpack plugin"
msgstr "Precarica la sitemap del plugin Jetpack"

#: inc/deprecated/3.11.php:83
#: inc/deprecated/3.12.php:58
#: inc/deprecated/3.12.php:87
#: inc/deprecated/3.12.php:186
#: inc/deprecated/3.12.php:253
#: inc/deprecated/3.12.php:342
#, php-format
msgid "We automatically detected the sitemap generated by the %s plugin. You can check the option to preload it."
msgstr "Abbiamo identificato automaticamente la sitemap generata dal plugin %s. Puoi fare clic sull'opzione per precaricarla."

#: inc/deprecated/3.12.php:56
msgid "All in One SEO XML sitemap"
msgstr "Sitemap XML di All in One SEO"

#: inc/deprecated/3.12.php:85
msgid "Rank Math XML sitemap"
msgstr "Sitemap XML di Rank Math"

#: inc/deprecated/3.12.php:184
msgid "SEOPress XML sitemap"
msgstr "Sitemap XML di SEOPress"

#: inc/deprecated/3.12.php:251
msgid "The SEO Framework XML sitemap"
msgstr "Sitemap XML di The SEO Framework"

#: inc/deprecated/3.11.php:81
msgid "Yoast SEO XML sitemap"
msgstr "Sitemap XML di Yoast SEO"

#: inc/Addon/Cloudflare/API/Client.php:186
#: inc/deprecated/3.5.php:112
#: inc/deprecated/3.5.php:169
msgid "Incorrect Cloudflare email address or API key."
msgstr "L'indirizzo email o la chiave API di Cloudflare non sono corretti."

#: inc/Addon/Cloudflare/API/Client.php:190
#: inc/Addon/Cloudflare/API/Client.php:204
#: inc/Addon/Cloudflare/Cloudflare.php:74
#: inc/Addon/Cloudflare/Cloudflare.php:109
#: inc/classes/subscriber/Tools/class-detect-missing-tags-subscriber.php:157
#: inc/deprecated/3.5.php:92
#: inc/deprecated/3.5.php:116
#: inc/deprecated/3.5.php:129
#: inc/deprecated/3.5.php:157
#: inc/deprecated/3.5.php:173
#, php-format
msgid "Read the %1$sdocumentation%2$s for further guidance."
msgstr "Leggi la %1$sdocumentazione%2$s per avere più informazioni."

#: inc/Addon/Cloudflare/API/Client.php:192
#: inc/Addon/Cloudflare/API/Client.php:206
#: inc/Addon/Cloudflare/Auth/APIKey.php:63
#: inc/Addon/Cloudflare/Cloudflare.php:76
#: inc/Addon/Cloudflare/Cloudflare.php:111
#: inc/deprecated/3.5.php:81
#: inc/deprecated/3.5.php:94
#: inc/deprecated/3.5.php:118
#: inc/deprecated/3.5.php:131
#: inc/deprecated/3.5.php:159
#: inc/deprecated/3.5.php:175
#: inc/deprecated/3.5.php:208
msgid "https://docs.wp-rocket.me/article/18-using-wp-rocket-with-cloudflare/?utm_source=wp_plugin&utm_medium=wp_rocket#add-on"
msgstr "https://docs.wp-rocket.me/article/18-using-wp-rocket-with-cloudflare/?utm_source=wp_plugin&utm_medium=wp_rocket#add-on"

#: inc/Addon/Cloudflare/API/Client.php:200
#: inc/deprecated/3.5.php:125
msgid "Incorrect Cloudflare Zone ID."
msgstr "Il Cloudflare Zone ID non è corretto."

#: inc/Addon/Cloudflare/Auth/APIKey.php:61
#, php-format
msgid "Cloudflare email and/or API key are not set. Read the %1$sdocumentation%2$s for further guidance."
msgstr "L'email e/o la chiave API di Cloudflare non sono impostate. Leggi la %1$sdocumentazione%2$s per avere ulteriori informazioni."

#: inc/Addon/Cloudflare/Cloudflare.php:70
#: inc/deprecated/3.5.php:88
msgid "Missing Cloudflare Zone ID."
msgstr "Manca il Cloudflare Zone ID."

#: inc/Addon/Cloudflare/Cloudflare.php:105
#: inc/deprecated/3.5.php:153
msgid "It looks like your domain is not set up on Cloudflare."
msgstr "Sembra che non abbia configurato il tuo dominio su Cloudflare."

#: inc/deprecated/3.5.php:587
#, php-format
msgid "<strong>WP Rocket:</strong> %s"
msgstr "<strong>WP Rocket:</strong>%s"

#: inc/deprecated/3.5.php:592
msgid "<strong>WP Rocket:</strong> Cloudflare cache successfully purged."
msgstr "<strong>WP Rocket:</strong> la cache di Cloudflare è stata eliminata correttamente."

#: inc/Addon/Cloudflare/Subscriber.php:632
#: inc/admin/options.php:184
#: inc/classes/subscriber/Tools/class-detect-missing-tags-subscriber.php:148
msgid "WP Rocket: "
msgstr "WP Rocket: "

#: inc/Addon/Cloudflare/Subscriber.php:297
#, php-format
msgid "Cloudflare development mode error: %s"
msgstr "Errore modalità sviluppo Cloudflare: %s"

#: inc/Addon/Cloudflare/Subscriber.php:304
#, php-format
msgid "Cloudflare development mode %s"
msgstr "Modalità sviluppo Cloudflare %s"

#: inc/Addon/Cloudflare/Subscriber.php:321
#, php-format
msgid "Cloudflare cache level error: %s"
msgstr "Errore nel livello cache Cloudflare: %s"

#: inc/Addon/Cloudflare/Subscriber.php:334
#, php-format
msgid "Cloudflare cache level set to %s"
msgstr "Livello cache Cloudflare impostato su %s"

#: inc/Addon/Cloudflare/Subscriber.php:350
#, php-format
msgid "Cloudflare minification error: %s"
msgstr "Errore minificazione Cloudflare: %s"

#: inc/Addon/Cloudflare/Subscriber.php:357
#, php-format
msgid "Cloudflare minification %s"
msgstr "Minificazione Cloudflare %s"

#: inc/Addon/Cloudflare/Subscriber.php:373
#, php-format
msgid "Cloudflare rocket loader error: %s"
msgstr "Errore del Rocket Loader di Cloudflare: %s"

#: inc/Addon/Cloudflare/Subscriber.php:380
#, php-format
msgid "Cloudflare rocket loader %s"
msgstr "Rocket Loader Cloudflare %s"

#: inc/Addon/Cloudflare/Subscriber.php:396
#, php-format
msgid "Cloudflare browser cache error: %s"
msgstr "Errore della cache del browser di Cloudflare: %s"

#: inc/Engine/Admin/Database/Subscriber.php:79
#: inc/deprecated/Engine/Addon/FacebookTracking/Subscriber.php:92
#: inc/deprecated/Engine/Addon/GoogleTracking/Subscriber.php:137
msgid "weekly"
msgstr "settimanale"

#: inc/Engine/Admin/Settings/Page.php:201
msgid "Save Changes"
msgstr "Salva le modifiche"

#: inc/Engine/Admin/Settings/Page.php:201
msgid "Validate License"
msgstr "Valida la licenza"

#: inc/Engine/Admin/Settings/Page.php:257
#: inc/Engine/Admin/Settings/Page.php:258
msgid "Unavailable"
msgstr "Non disponibile"

#: inc/Engine/Admin/Settings/Page.php:352
#: inc/deprecated/deprecated.php:1789
#: views/settings/page-sections/dashboard.php:80
msgid "License"
msgstr "Licenza"

#: inc/Engine/Admin/Settings/Page.php:369
msgid "API key"
msgstr "Chiave API"

#: inc/Engine/Admin/Settings/Page.php:384
msgid "Email address"
msgstr "Indirizzo email"

#: inc/Engine/Admin/Settings/Page.php:410
msgid "Dashboard"
msgstr "Bacheca"

#: inc/Engine/Admin/Settings/Page.php:411
msgid "Get help, account info"
msgstr "Info account e aiuto"

#: inc/Engine/Admin/Settings/Page.php:420
msgid "My Status"
msgstr "Stato"

#: inc/Engine/Admin/Settings/Page.php:430
#: views/settings/page.php:75
msgid "Rocket Analytics"
msgstr "Rocket Analytics"

#: inc/Engine/Admin/Settings/Page.php:432
#, php-format
msgid "I agree to share anonymous data with the development team to help improve WP Rocket. %1$sWhat info will we collect?%2$s"
msgstr "Sono d’accordo a condividere dati anonimi con la squadra dello sviluppo di WP Rocket per aiutare a migliorare il plugin. %1$sQuali informazioni raccogliamo?%2$s"

#: inc/Engine/Admin/Settings/Page.php:456
#: inc/Engine/Cache/WPCache.php:354
msgid "Cache"
msgstr "Cache"

#: inc/Engine/Admin/Settings/Page.php:457
msgid "Basic cache options"
msgstr "Opzioni cache di base"

#: inc/Engine/Admin/Settings/Page.php:464
msgid "Mobile Cache"
msgstr "Cache dispositivi mobili"

#: inc/Engine/Admin/Settings/Page.php:466
msgid "Speed up your site for mobile visitors."
msgstr "Rende il tuo sito più veloce sui dispositivi mobili."

#: inc/Engine/Admin/Settings/Page.php:471
msgid "We detected you use a plugin that requires a separate cache for mobile, and automatically enabled this option for compatibility."
msgstr "Abbiamo notato che stai usando un plugin che richiede una cache separata per i dispositivi mobili, per cui abbiamo abilitato automaticamente quest'opzione per garantirne la compatibilità."

#: inc/Engine/Admin/Settings/Page.php:475
msgid "User Cache"
msgstr "Cache utente"

#: inc/Engine/Admin/Settings/Page.php:478
#, php-format
msgid "%1$sUser cache%2$s is great when you have user-specific or restricted content on your website."
msgstr "%1$sCache utente%2$s è l’opzione perfetta per i siti web che includono contenuto dedicato ai soli utenti registrati."

#: inc/Engine/Admin/Settings/Page.php:486
msgid "Cache Lifespan"
msgstr "Vita utile della cache"

#: inc/Engine/Admin/Settings/Page.php:489
#, php-format
msgid "Cache files older than the specified lifespan will be deleted.<br>Enable %1$spreloading%2$s for the cache to be rebuilt automatically after lifespan expiration."
msgstr "I file di cache più vecchi della vita utile che hai specificato verranno eliminati.<br>Abilita il %1$sprecaricamento%2$s in modo che la cache venga ricostruita automaticamente dopo lo scadere della vita utile."

#: inc/Engine/Admin/Settings/Page.php:503
msgid "Enable caching for logged-in WordPress users"
msgstr "Abilita la cache per gli utenti connessi a WordPress"

#: inc/Engine/Admin/Settings/Page.php:511
msgid "Enable caching for mobile devices"
msgstr "Abilita la cache per i dispositivi mobili"

#: inc/Engine/Admin/Settings/Page.php:526
msgid "Separate cache files for mobile devices"
msgstr "Separa i file di cache per i dispositivi mobili"

#: inc/Engine/Admin/Settings/Page.php:528
#, php-format
msgid "Most modern themes are responsive and should work without a separate cache. Enable this only if you have a dedicated mobile theme or plugin. %1$sMore info%2$s"
msgstr "La maggior parte dei temi oggi sono responsive e dovrebbero funzionare senza una cache separata. Attiva questa opzione se usi un tema mobile o un plugin dedicato. %1$sPiù info%2$s"

#: inc/Engine/Admin/Settings/Page.php:544
msgid "Specify time after which the global cache is cleared<br>(0 = unlimited )"
msgstr "Specifica un lasso di tempo dopo il quale la cache globale verrà cancellata<br>(0 = illimitato)"

#: inc/Engine/Admin/Settings/Page.php:546
#, php-format
msgid "Reduce lifespan to 10 hours or less if you notice issues that seem to appear periodically. %1$sWhy?%2$s"
msgstr "Riduci la vita utile a 10 ore o meno se noti la comparsa frequente di problemi. %1$sPerché?%2$s"

#: inc/Engine/License/views/promo-banner.php:31
#: inc/Engine/License/views/renewal-soon-banner.php:14
msgid "Minutes"
msgstr "Minuti"

#: inc/Engine/Admin/Settings/Page.php:552
#: inc/Engine/License/views/promo-banner.php:30
#: inc/Engine/License/views/renewal-soon-banner.php:13
msgid "Hours"
msgstr "Ore"

#: inc/Engine/Admin/Settings/Page.php:553
#: inc/Engine/License/views/promo-banner.php:29
#: inc/Engine/License/views/renewal-soon-banner.php:12
msgid "Days"
msgstr "Giorni"

#: inc/Engine/Admin/Settings/Page.php:602
msgid "File Optimization"
msgstr "Ottimizzazione file"

#: inc/Engine/Admin/Settings/Page.php:603
msgid "Optimize CSS & JS"
msgstr "Ottimizza i file CSS & JS"

#: inc/Engine/Admin/Settings/Page.php:621
msgid "CSS Files"
msgstr "File CSS"

#: inc/Engine/Admin/Settings/Page.php:611
#: inc/Engine/Admin/Settings/Page.php:637
#, php-format
msgid "%1$s Minification is currently activated in <strong>Autoptimize</strong>. If you want to use %2$s’s minification, disable those options in Autoptimize."
msgstr "%1$s La minificazione è al momento attivata da <strong>Autoptimize</strong>. Se vuoi usare la minificazione di %2$s, disattiva quella di Autoptimize."

#: inc/Engine/Admin/Settings/Page.php:630
msgid "JavaScript Files"
msgstr "File JavaScript"

#: inc/Engine/Admin/Settings/Page.php:663
msgid "Minify CSS files"
msgstr "Minifica i file CSS"

#: inc/Engine/Admin/Settings/Page.php:664
msgid "Minify CSS removes whitespace and comments to reduce the file size."
msgstr "La minificazione del CSS rimuove gli spazi bianchi e i commenti, riducendo la dimensione del file."

#: inc/Engine/Admin/Settings/Page.php:677
#: inc/Engine/Admin/Settings/Page.php:701
#: inc/Engine/Admin/Settings/Page.php:765
#: inc/Engine/Admin/Settings/Page.php:831
#: inc/Engine/Admin/Settings/Page.php:855
msgid "This could break things!"
msgstr "Questo potrebbe creare dei malfunzionamenti!"

#: inc/Engine/Admin/Settings/Page.php:678
#: inc/Engine/Admin/Settings/Page.php:702
#: inc/Engine/Admin/Settings/Page.php:766
#: inc/Engine/Admin/Settings/Page.php:832
#: inc/Engine/Admin/Settings/Page.php:856
msgid "If you notice any errors on your website after having activated this setting, just deactivate it again, and your site will be back to normal."
msgstr "Se noti qualsiasi errore nel tuo sito web dopo aver attivato questa opzione, disattivala: tutto tornerà alla normalità."

#: inc/Engine/Admin/Settings/Page.php:679
msgid "Activate minify CSS"
msgstr "Attiva minificazione CSS"

#: inc/Engine/Admin/Settings/Page.php:684
msgid "Combine CSS files <em>(Enable Minify CSS files to select)</em>"
msgstr "Combina i file CSS <em>(devi selezionare prima l’opzione Minifica file CSS)</em>"

#: inc/Engine/Admin/Settings/Page.php:686
#, php-format
msgid "Combine CSS merges all your files into 1, reducing HTTP requests. Not recommended if your site uses HTTP/2. %1$sMore info%2$s"
msgstr "La combinazione del CSS accorpa tutti i tuoi file in 1, riducendo il numero di richieste HTTP. Non raccomandiamo quest’opzione se il tuo sito usa HTTP/2. %1$sPiù info%2$s"

#: inc/Engine/Admin/Settings/Page.php:703
msgid "Activate combine CSS"
msgstr "Attiva combinazione CSS"

#: inc/Engine/Admin/Settings/Page.php:708
#: inc/admin/options.php:124
msgid "Excluded CSS Files"
msgstr "File CSS esclusi"

#: inc/Engine/Admin/Settings/Page.php:709
msgid "Specify URLs of CSS files to be excluded from minification and concatenation (one per line)."
msgstr "Specifica gli URL dei file CSS da escludere dalla minificazione e concatenazione (uno per linea)."

#: inc/Engine/Admin/Settings/Page.php:710
msgid "<strong>Internal:</strong> The domain part of the URL will be stripped automatically. Use (.*).css wildcards to exclude all CSS files located at a specific path."
msgstr "<strong>Nota interna:</strong>la parte di URL che contiene il dominio verrà eliminata automaticamente. Usa i caratteri jolly (.*).css per escludere tutti i file CSS collocati in uno specifico percorso."

#: inc/Engine/Admin/Settings/Page.php:712
#, php-format
msgid "<strong>3rd Party:</strong> Use either the full URL path or only the domain name, to exclude external CSS. %1$sMore info%2$s"
msgstr "<strong>Terze parti:</strong>Per escludere i CSS esterni, usa il percorso URL completo oppure solo il nome dominio. %1$sPiù info%2$s"

#: inc/Engine/Admin/Settings/Page.php:725
#: inc/Engine/Admin/Settings/Page.php:747
msgid "Optimize CSS delivery"
msgstr "Ottimizza la pubblicazione del CSS"

#: inc/Engine/Admin/Settings/Page.php:800
msgid "Fallback critical CSS"
msgstr "Percorso critico del CSS alternativo"

#: inc/Engine/Admin/Settings/Page.php:802
#, php-format
msgid "Provides a fallback if auto-generated critical path CSS is incomplete. %1$sMore info%2$s"
msgstr "Genera un percorso alternativo quando il percorso critico del CSS auto-generatosi non è completo. %1$sPiù info%2$s"

#: inc/Engine/Admin/Settings/Page.php:817
msgid "Minify JavaScript files"
msgstr "Minifica i file JavaScript"

#: inc/Engine/Admin/Settings/Page.php:818
msgid "Minify JavaScript removes whitespace and comments to reduce the file size."
msgstr "Minificare il JavaScript rimuove gli spazi bianchi e i commenti, riducendo la dimensione del file."

#: inc/Engine/Admin/Settings/Page.php:833
msgid "Activate minify JavaScript"
msgstr "Attiva minificazione JavaScript"

#: inc/Engine/Admin/Settings/Page.php:838
msgid "Combine JavaScript files <em>(Enable Minify JavaScript files to select)</em>"
msgstr "Combina i file JavaScript <em>(devi selezionare prima l’opzione Minifica file JavaScript)</em>"

#: inc/Engine/Admin/Settings/Page.php:840
#, php-format
msgid "Combine JavaScript files combines your site’s internal, 3rd party and inline JS reducing HTTP requests. Not recommended if your site uses HTTP/2. %1$sMore info%2$s"
msgstr "Questa opzione combina i file JavaScript interni al tuo sito, quelli di terze parti e i JS inline, riducendo le richieste HTTP. Non raccomandiamo attivarla se il tuo sito usa HTTP/2. %1$sPiù info%2$s"

#: inc/Engine/Admin/Settings/Page.php:857
msgid "Activate combine JavaScript"
msgstr "Attiva combinazione JavaScript"

#: inc/Engine/Admin/Settings/Page.php:862
#: inc/admin/options.php:125
msgid "Excluded Inline JavaScript"
msgstr "JavaScript Inline esclusi"

#: inc/Engine/Admin/Settings/Page.php:864
#, php-format
msgid "Specify patterns of inline JavaScript to be excluded from concatenation (one per line). %1$sMore info%2$s"
msgstr "Specifica la forma dei JavScript inline da escludere dalla concatenazione (uno per linea). %1$sPiù info%2$s"

#: inc/Engine/Admin/Settings/Page.php:880
#: inc/Engine/Admin/Settings/Page.php:913
#: inc/Engine/Admin/Settings/Page.php:960
#: inc/admin/options.php:126
msgid "Excluded JavaScript Files"
msgstr "Escludere i file JavaScript"

#: inc/Engine/Admin/Settings/Page.php:881
msgid "Specify URLs of JavaScript files to be excluded from minification and concatenation (one per line)."
msgstr "Specifica gli URL dei file JavaScript da escludere da minificazione e concatenazione (uno per linea)."

#: inc/Engine/Admin/Settings/Page.php:882
msgid "<strong>Internal:</strong> The domain part of the URL will be stripped automatically. Use (.*).js wildcards to exclude all JS files located at a specific path."
msgstr "<strong>Per gli interni:</strong>la parte di URL che contiene il dominio verrà eliminata automaticamente. Usa i caratteri jolly (.*).js per escludere tutti i file JS collocati in uno specifico percorso."

#: inc/Engine/Admin/Settings/Page.php:884
#, php-format
msgid "<strong>3rd Party:</strong> Use either the full URL path or only the domain name, to exclude external JS. %1$sMore info%2$s"
msgstr "<strong>Esterni:</strong>Per escludere i JS esterni, usa il percorso URL completo oppure solo il nome dominio. %1$sPiù info%2$s"

#: inc/Engine/Admin/Settings/Page.php:900
msgid "Load JavaScript deferred"
msgstr "Carica il JavaScript in modalità differita"

#: inc/Engine/Admin/Settings/Page.php:902
#, php-format
msgid "Load JavaScript deferred eliminates render-blocking JS on your site and can improve load time. %1$sMore info%2$s"
msgstr "Caricare il JavaScript in modalità differita elimina il JS che blocca il rendering sul tuo sito, cosa che può migliorare il tempo di caricamento. %1$sPiù info%2$s"

#: inc/Engine/Admin/Settings/Page.php:931
#: inc/admin/ui/meta-boxes.php:111
msgid "Delay JavaScript execution"
msgstr "Rimanda l'esecuzione del JavaScript"

#: inc/Engine/Admin/Settings/Page.php:933
#, php-format
msgid "Improves performance by delaying the loading of JavaScript files until user interaction (e.g. scroll, click). %1$sMore info%2$s"
msgstr "Migliora le prestazioni rimandando il caricamento dei file JavaScript fino all'interazione con l'utente (ad es. scroll, clic). %1$sPiù info%2$s"

#: inc/Engine/Admin/Settings/Page.php:993
msgid "Media"
msgstr "Media"

#: inc/ThirdParty/Themes/Avada.php:116
msgid "Avada"
msgstr "Avada"

#: inc/Engine/Admin/Settings/Page.php:1003
msgid "Autoptimize"
msgstr "Autoptimize"

#: inc/Engine/Admin/Settings/Page.php:1048
msgid "LazyLoad"
msgstr "Lazyload"

#: inc/Engine/Admin/Settings/Page.php:1051
#, php-format
msgid "It can improve actual and perceived loading time as images, iframes, and videos will be loaded only as they enter (or about to enter) the viewport and reduces the number of HTTP requests. %1$sMore Info%2$s"
msgstr "Può migliorare sia il tempo di caricamento reale che quello percepito, perché immagini, iframe e video verranno caricati solo quando entrano (o stanno per entrare) nell'area visibile dello schermo. Riduce inoltre il numero di richieste HTTP. %1$sPiù info%2$s"

#: inc/Engine/Admin/Settings/Page.php:1058
#, php-format
msgid "LazyLoad is currently activated in %2$s. If you want to use WP Rocket’s LazyLoad, disable this option in %2$s."
msgstr "Il LazyLoad al momento è attivo su %2$s. Se desideri usare il LazyLoad di WP Rocket, disattiva questa opzione su %2$s."

#: inc/Engine/Admin/Settings/Page.php:1928
#, php-format
msgid "Enable this option if you would like WP Rocket to serve WebP images to compatible browsers. Please note that WP Rocket cannot create WebP images for you. To create WebP images we recommend %1$sImagify%2$s. %3$sMore info%2$s"
msgstr "Abilita questa questa opzione se desideri che WP Rocket serva immagini WebP ai browser compatibili. Considera che WP Rocket non creerà le immagini WebP al tuo posto. Per creare immagini WebP ti raccomandiamo %1$sImagify%2$s. %3$sPiù info%2$s"

#: inc/Engine/Admin/Settings/Page.php:1083
msgid "Enable for images"
msgstr "Abilita per le immagini"

#: inc/Engine/Admin/Settings/Page.php:1095
#, php-format
msgid "LazyLoad for images is currently activated in %2$s. If you want to use %1$s’s LazyLoad, disable this option in %2$s."
msgstr "Il LazyLoad per le immagini è al momento attivo su %2$s. Se vuoi usare il LazyLoad di %1$s, disattiva quest'opzione su %2$s."

#: inc/Engine/Admin/Settings/Page.php:1103
msgid "Enable for iframes and videos"
msgstr "Abilita per iframe e video"

#: inc/Engine/Admin/Settings/Page.php:1118
msgid "Replace YouTube iframe with preview image"
msgstr "Sostituisci l'iframe YouTube con un'immagine di anteprima"

#: inc/Engine/Admin/Settings/Page.php:1120
msgid "This can significantly improve your loading time if you have a lot of YouTube videos on a page."
msgstr "Questo migliora in maniera significativa il tempo di caricamento, se hai molti video YouTube in una pagina."

#: inc/Engine/Admin/Settings/Page.php:1120
#, php-format
msgid "Replace YouTube iframe with preview image is not compatible with %2$s."
msgstr "L'opzione \"Sostituisci l'iframe YouTube con un'immagine di anteprima\" non è compatibile con %2$s."

#: inc/Engine/Admin/Settings/Page.php:1164
#: inc/deprecated/deprecated.php:1776
msgid "Preload"
msgstr "Precaricamento"

#: inc/Engine/Admin/Settings/Page.php:1165
msgid "Generate cache files, preload fonts"
msgstr "Genera i file di cache, precarica i font"

#: inc/Engine/Admin/Settings/Page.php:1177
msgid "Preload Cache"
msgstr "Precarica la cache"

#: inc/Engine/Admin/Settings/Page.php:1199
msgid "Prefetch DNS Requests"
msgstr "Precarica le richieste DNS"

#: inc/Engine/Admin/Settings/Page.php:1201
msgid "DNS prefetching can make external files load faster, especially on mobile networks"
msgstr "Il precaricamento dei DNS può far sì che i file esterni carichino più rapidamente, specialmente sulle reti mobili"

#: inc/Engine/Admin/Settings/Page.php:1206
msgid "Preload Fonts"
msgstr "Precarica i font"

#: inc/Engine/Admin/Settings/Page.php:1209
#, php-format
msgid "Improves performance by helping browsers discover fonts in CSS files. %1$sMore info%2$s"
msgstr "Migliora la performance perché aiuta i browser a identificare i font dentro i file CSS. %1$sPiù info%2$s"

#: inc/Engine/Admin/Settings/Page.php:1188
msgid "Preload Links"
msgstr "Precaricamento dei link"

#: inc/Engine/Admin/Settings/Page.php:1191
#, php-format
msgid "Link preloading improves the perceived load time by downloading a page when a user hovers over the link. %1$sMore info%2$s"
msgstr "Il precaricamento dei link migliora il tempo di caricamento percepito perché scarica una pagina quando un utente passa il mouse sopra il link. %1$sPiù info%2$s"

#: inc/Engine/Admin/Settings/Page.php:1223
msgid "Activate Preloading"
msgstr "Attiva il precaricamento"

#: inc/Engine/Admin/Settings/Page.php:1250
msgid "URLs to prefetch"
msgstr "URLs da precaricare"

#: inc/Engine/Admin/Settings/Page.php:1251
msgid "Specify external hosts to be prefetched (no <code>http:</code>, one per line)"
msgstr "Specificare gli host esterni da precaricare (senza <code>http:</code>, uno per riga)"

#: inc/Engine/Admin/Settings/Page.php:1260
msgid "Fonts to preload"
msgstr "Font da precaricare"

#: inc/Engine/Admin/Settings/Page.php:1261
msgid "Specify urls of the font files to be preloaded (one per line). Fonts must be hosted on your own domain, or the domain you have specified on the CDN tab."
msgstr "Specifica gli URL dei file di font da precaricare (uno per riga). I font devono essere ospitati sul tuo dominio o sul dominio specificato nella sezione \"CDN\"."

#: inc/Engine/Admin/Settings/Page.php:1262
msgid "The domain part of the URL will be stripped automatically.<br/>Allowed font extensions: otf, ttf, svg, woff, woff2."
msgstr "La parte di URL contenente il dominio sarà rimossa automaticamente. <br/>Estensioni di font consentite: otf, ttf, svg, woff, woff2."

#: inc/Engine/Admin/Settings/Page.php:1271
msgid "Enable link preloading"
msgstr "Abilita il precaricamento dei link"

#: inc/Engine/Admin/Settings/Page.php:1290
msgid "Advanced Rules"
msgstr "Comandi avanzati"

#: inc/Engine/Admin/Settings/Page.php:1291
msgid "Fine-tune cache rules"
msgstr "Aggiusta regole di caching"

#: inc/Engine/Admin/Settings/Page.php:1302
msgid "Sensitive pages like custom login/logout URLs should be excluded from cache."
msgstr "Le pagine sensibili come gli URL di login/logout dovrebbero essere escluse dalla cache."

#: inc/Engine/Admin/Settings/Page.php:1305
msgctxt "plugin name"
msgid "WooCommerce"
msgstr "WooCommerce"

#: inc/Engine/Admin/Settings/Page.php:1307
msgctxt "plugin name"
msgid "Easy Digital Downloads"
msgstr "Easy Digital Downloads"

#: inc/Engine/Admin/Settings/Page.php:1309
msgctxt "plugin name"
msgid "iThemes Exchange"
msgstr "iThemes Exchange"

#: inc/Engine/Admin/Settings/Page.php:1311
msgctxt "plugin name"
msgid "Jigoshop"
msgstr "Jigoshop"

#: inc/Engine/Admin/Settings/Page.php:1313
msgctxt "plugin name"
msgid "WP-Shop"
msgstr "WP-Shop"

#: inc/Engine/Admin/Settings/Page.php:1319
#, php-format
msgid "<br>Cart, checkout and \"my account\" pages set in <strong>%1$s%2$s%3$s</strong> will be detected and never cached by default."
msgstr "<br>Le pagine del carrello, pagamento e ”mio account” predefinite da <strong>%1$s%2$s%3$s</strong> verranno identificate ed escluse automaticamente dalla cache."

#: inc/Engine/Admin/Settings/Page.php:1329
#: inc/admin/options.php:129
msgid "Never Cache URL(s)"
msgstr "Non salvare in cache questi URL"

#: inc/Engine/Admin/Settings/Page.php:1337
msgid "Never Cache Cookies"
msgstr "Non salvare in cache i cookie"

#: inc/Engine/Admin/Settings/Page.php:1343
#: inc/admin/options.php:130
msgid "Never Cache User Agent(s)"
msgstr "Non salvare in cache questi User Agent:"

#: inc/Engine/Admin/Settings/Page.php:1349
#: inc/admin/options.php:131
msgid "Always Purge URL(s)"
msgstr "Elimina sempre la cache di questi URL:"

#: inc/Engine/Admin/Settings/Page.php:1355
msgid "Cache Query String(s)"
msgstr "Cache delle query strings:"

#: inc/Engine/Admin/Settings/Page.php:1358
#, php-format
msgid "%1$sCache for query strings%2$s enables you to force caching for specific GET parameters."
msgstr "%1$sLa cache per le query string%2$s permette di forzare la memorizzazione in cache di specifici parametri GET."

#: inc/Engine/Admin/Settings/Page.php:1369
msgid "Specify URLs of pages or posts that should never be cached (one per line)"
msgstr "Specificare gli URL delle pagine o degli articoli che non dovrebbero mai essere memorizzati in cache (uno per linea)"

#: inc/Engine/Admin/Settings/Page.php:1370
#: inc/Engine/Admin/Settings/Page.php:1398
msgid "The domain part of the URL will be stripped automatically.<br>Use (.*) wildcards to address multiple URLs under a given path."
msgstr "La porzione di URL relativa al dominio non verrà inserita.<br>Puoi usare i caratteri jolly (.*) per gestire tutti gli URL che condividono un percorso comune."

#: inc/Engine/Admin/Settings/Page.php:1387
msgid "Specify user agent strings that should never see cached pages (one per line)"
msgstr "Specificare le stringhe degli user agent che non dovrebbero mai vedere la versione in cache delle pagine (uno per riga)"

#: inc/Engine/Admin/Settings/Page.php:1388
msgid "Use (.*) wildcards to detect parts of UA strings."
msgstr "Usa i caratteri jolly (.*) per identificare una parte del nome degli user agent."

#: inc/Engine/Admin/Settings/Page.php:1397
msgid "Specify URLs you always want purged from cache whenever you update any post or page (one per line)"
msgstr "Specificare gli URL per i quali cancellare sempre la cache ogni volta che si aggiorna un articolo o una pagina (uno per riga)"

#: inc/Engine/Admin/Settings/Page.php:1406
msgid "Specify query strings for caching (one per line)"
msgstr "Specificare le query string da memorizzare in cache (uno per riga)"

#: inc/Engine/Admin/Settings/Page.php:1431
#: inc/deprecated/deprecated.php:1775
msgid "Database"
msgstr "Database"

#: inc/Engine/Admin/Settings/Page.php:1432
msgid "Optimize, reduce bloat"
msgstr "Ottimizza e riduce la saturazione"

#: inc/Engine/Admin/Settings/Page.php:1439
msgid "Post Cleanup"
msgstr "Pulizia articoli"

#: inc/Engine/Admin/Settings/Page.php:1441
msgid "Post revisions and drafts will be permanently deleted. Do not use this option if you need to retain revisions or drafts."
msgstr "Le revisioni degli articoli e le bozze verranno cancellate permanentemente. Non usare questa opzione se hai bisogno di mantenere revisioni e bozze."

#: inc/Engine/Admin/Settings/Page.php:1446
msgid "Comments Cleanup"
msgstr "Pulizia dei commenti"

#: inc/Engine/Admin/Settings/Page.php:1448
msgid "Spam and trashed comments will be permanently deleted."
msgstr "I commenti spam e quelli cestinati verranno cancellati permanentemente."

#: inc/Engine/Admin/Settings/Page.php:1452
msgid "Transients Cleanup"
msgstr "Pulizia dei transient"

#: inc/Engine/Admin/Settings/Page.php:1454
msgid "Transients are temporary options; they are safe to remove. They will be automatically regenerated as your plugins require them."
msgstr "I transient sono opzioni temporanee, puoi rimuoverli senza problema. Verranno rigenerati automaticamente non appena i tuoi plugin ne avranno bisogno."

#: inc/Engine/Admin/Settings/Page.php:1458
msgid "Database Cleanup"
msgstr "Pulizia del database"

#: inc/Engine/Admin/Settings/Page.php:1460
msgid "Reduces overhead of database tables"
msgstr "Riduce l’overhead delle tabelle del database"

#: inc/Engine/Admin/Database/Optimization.php:30
#: inc/Engine/Admin/Settings/Page.php:1475
msgid "Revisions"
msgstr "Revisioni"

#: inc/Engine/Admin/Settings/Page.php:1477
#, php-format
msgid "%s revision in your database."
msgid_plural "%s revisions in your database."
msgstr[0] " %s revisione nel tuo database."
msgstr[1] "%s revisioni nel tuo database."

#: inc/Engine/Admin/Database/Optimization.php:31
#: inc/Engine/Admin/Settings/Page.php:1485
msgid "Auto Drafts"
msgstr "Bozze automatiche"

#: inc/Engine/Admin/Settings/Page.php:1487
#, php-format
msgid "%s draft in your database."
msgid_plural "%s drafts in your database."
msgstr[0] "%s bozza nel tuo database."
msgstr[1] "%s bozze nel tuo database."

#: inc/Engine/Admin/Database/Optimization.php:32
#: inc/Engine/Admin/Settings/Page.php:1495
msgid "Trashed Posts"
msgstr "Articoli cestinati"

#: inc/Engine/Admin/Settings/Page.php:1497
#, php-format
msgid "%s trashed post in your database."
msgid_plural "%s trashed posts in your database."
msgstr[0] "%s articolo cestinato nel tuo database."
msgstr[1] "%s articoli cestinati nel tuo database."

#: inc/Engine/Admin/Database/Optimization.php:33
#: inc/Engine/Admin/Settings/Page.php:1505
msgid "Spam Comments"
msgstr "Commenti spam"

#: inc/Engine/Admin/Settings/Page.php:1507
#, php-format
msgid "%s spam comment in your database."
msgid_plural "%s spam comments in your database."
msgstr[0] "%s  commento spam nel tuo database."
msgstr[1] "%s commenti spam nel tuo database."

#: inc/Engine/Admin/Database/Optimization.php:34
#: inc/Engine/Admin/Settings/Page.php:1515
msgid "Trashed Comments"
msgstr "Commenti cestinati"

#: inc/Engine/Admin/Settings/Page.php:1517
#, php-format
msgid "%s trashed comment in your database."
msgid_plural "%s trashed comments in your database."
msgstr[0] "%s commento cestinato nel tuo database."
msgstr[1] "%s commenti cestinati nel tuo database."

#: inc/Engine/Admin/Settings/Page.php:1525
msgid "All transients"
msgstr "Tutti i transient"

#: inc/Engine/Admin/Settings/Page.php:1527
#, php-format
msgid "%s transient in your database."
msgid_plural "%s transients in your database."
msgstr[0] "%s transient nel tuo database."
msgstr[1] "%s transient nel tuo database."

#: inc/Engine/Admin/Settings/Page.php:1535
msgid "Optimize Tables"
msgstr "Ottimizza le tabelle"

#: inc/Engine/Admin/Settings/Page.php:1537
#, php-format
msgid "%s table to optimize in your database."
msgid_plural "%s tables to optimize in your database."
msgstr[0] " %s tavola da ottimizzare nel tuo database."
msgstr[1] "%s tavole da ottimizzare nel tuo database."

#: inc/Engine/Admin/Settings/Page.php:1548
msgid "Schedule Automatic Cleanup"
msgstr "Programma la pulizia automatica"

#: inc/Engine/Admin/Settings/Page.php:1560
msgid "Frequency"
msgstr "Frequenza"

#: inc/Engine/Admin/Settings/Page.php:1568
msgid "Daily"
msgstr "Giornaliera"

#: inc/Engine/Admin/Settings/Page.php:1569
msgid "Weekly"
msgstr "Settimanale"

#: inc/Engine/Admin/Settings/Page.php:1570
msgid "Monthly"
msgstr "Mensile"

#: inc/Engine/Admin/Settings/Page.php:1586
#: inc/Engine/Admin/Settings/Page.php:1597
#: inc/admin/ui/meta-boxes.php:108
#: inc/deprecated/deprecated.php:1773
msgid "CDN"
msgstr "CDN"

#: inc/Engine/Admin/Settings/Page.php:1587
msgid "Integrate your CDN"
msgstr "Integra la tua CDN"

#: inc/Engine/Admin/Settings/Page.php:1599
msgid "All URLs of static files (CSS, JS, images) will be rewritten to the CNAME(s) you provide."
msgstr "Tutti gli URL dei file statici (CSS, JS, immagini) verranno riscritti con i CNAME(s) indicati qui sotto."

#: inc/Engine/Admin/Settings/Page.php:1601
#, php-format
msgid "Not required for services like Cloudflare and Sucuri. Please see our available %1$sAdd-ons%2$s."
msgstr "Non è obbligatorio per servizi come Cloudflare e Sucuri. Verifica gli %1$sAdd-on%2$s disponibili."

#: inc/Engine/Admin/Settings/Page.php:1616
#: inc/admin/options.php:132
msgid "Exclude files from CDN"
msgstr "Escludi file dalla CDN"

#: inc/Engine/Admin/Settings/Page.php:1667
msgid "Enable Content Delivery Network"
msgstr "Abilita il Content Delivery Network"

#: inc/Engine/Admin/Settings/Page.php:1676
#: inc/Engine/CDN/RocketCDN/AdminPageSubscriber.php:159
msgid "CDN CNAME(s)"
msgstr "CNAME(s) della CDN"

#: inc/Engine/Admin/Settings/Page.php:1677
#: inc/Engine/CDN/RocketCDN/AdminPageSubscriber.php:160
msgid "Specify the CNAME(s) below"
msgstr "Specificare i CNAME(s) qui sotto"

#: inc/Engine/Admin/Settings/Page.php:1684
msgid "Specify URL(s) of files that should not get served via CDN (one per line)."
msgstr "Specifica gli URL dei file che non devono essere serviti attraverso il CDN (uno per linea)."

#: inc/Engine/Admin/Settings/Page.php:1685
msgid "The domain part of the URL will be stripped automatically.<br>Use (.*) wildcards to exclude all files of a given file type located at a specific path."
msgstr "La porzione di URL relativa al dominio non verrà inserita.<br>Puoi usare i caratteri jolly (.*) per escludere tutti i file della stessa tipologia che condividono un percorso comune."

#: inc/Engine/Admin/Settings/Page.php:1708
#: inc/Engine/Admin/Settings/Page.php:1716
msgid "Heartbeat"
msgstr "Heartbeat"

#: inc/Engine/Admin/Settings/Page.php:1709
msgid "Control WordPress Heartbeat API"
msgstr "Controlla l'API Heartbeat di WordPress"

#: inc/Engine/Admin/Settings/Page.php:1717
msgid "Reducing or disabling the Heartbeat API’s activity can help save some of your server’s resources."
msgstr "Ridurre o disattivare l'attività dell'API Heartbeat può aiutarti a risparmiare le risorse del tuo server."

#: inc/Engine/Admin/Settings/Page.php:1723
msgid "Reduce or disable Heartbeat activity"
msgstr "Riduci o disattiva l'attività di Heartbeat"

#: inc/Engine/Admin/Settings/Page.php:1724
msgid "Reducing activity will change Heartbeat frequency from one hit each minute to one hit every 2 minutes."
msgstr "Ridurre l'attività cambierà la frequenza di Heartbeat da un hit al minuto a un hit ogni 2 minuti."

#: inc/Engine/Admin/Settings/Page.php:1724
msgid "Disabling Heartbeat entirely may break plugins and themes using this API."
msgstr "Disattivare completamente Heartbeat potrebbe compromettere la funzionalità di plugin e temi che usano questa API."

#: inc/Engine/Admin/Settings/Page.php:1738
msgid "Do not limit"
msgstr "Non limitare"

#: inc/Engine/Admin/Settings/Page.php:1739
msgid "Reduce activity"
msgstr "Riduci l'attività"

#: inc/Engine/Admin/Settings/Page.php:1740
msgid "Disable"
msgstr "Disattiva"

#: inc/Engine/Admin/Settings/Page.php:1748
msgid "Control Heartbeat"
msgstr "Controlla Heartbeat"

#: inc/Engine/Admin/Settings/Page.php:1757
msgid "Behavior in backend"
msgstr "Comportamento nel backend"

#: inc/Engine/Admin/Settings/Page.php:1764
msgid "Behavior in post editor"
msgstr "Comportamento nell'editor dei post"

#: inc/Engine/Admin/Settings/Page.php:1770
msgid "Behavior in frontend"
msgstr "Comportamento nel frontend"

#: inc/Engine/Admin/Settings/Page.php:1787
#: views/settings/page-sections/tutorials.php:39
msgid "Add-ons"
msgstr "Add-on"

#: inc/Engine/Admin/Settings/Page.php:1788
msgid "Add more features"
msgstr "Aggiungi più funzioni"

#: inc/Engine/Admin/Settings/Page.php:1795
msgid "One-click Rocket Add-ons"
msgstr "Rocket Add-on in 1-clic"

#: inc/Engine/Admin/Settings/Page.php:1796
msgid "One-Click Add-ons are features extending available options without configuration needed. Switch the option \"on\" to enable from this screen."
msgstr "Gli Add-on in 1-clic sono componenti aggiuntivi che estendono opzioni già disponibili senza che sia necessaria alcuna configurazione. Passa l’opzione a ”on” per abilitarla da questa schermata."

#: inc/Engine/Admin/Settings/Page.php:1806
msgid "Rocket Add-ons"
msgstr "Rocket Add-on"

#: inc/Engine/Admin/Settings/Page.php:1807
msgid "Rocket Add-ons are complementary features extending available options."
msgstr "I Rocket Add-on sono componenti aggiuntivi che estendono opzioni già disponibili."

#: inc/Engine/Admin/Settings/Page.php:1817
#: inc/Engine/Admin/Settings/Page.php:1986
msgid "Cloudflare"
msgstr "Cloudflare"

#: inc/Engine/Admin/Settings/Page.php:1823
msgid "Integrate your Cloudflare account with this add-on."
msgstr "Integra il tuo account Cloudflare con questo componente aggiuntivo."

#: inc/Engine/Admin/Settings/Page.php:1824
msgid "Provide your account email, global API key, and domain to use options such as clearing the Cloudflare cache and enabling optimal settings with WP Rocket."
msgstr "Fornisci il tuo account email, la chiave API globale e il dominio per usare opzioni come la cancellazione della cache Cloudflare e abilitare le impostazioni ottimali con WP Rocket."

#: inc/Engine/Admin/Settings/Page.php:1875
msgid "Varnish"
msgstr "Varnish"

#: inc/Engine/Admin/Settings/Page.php:1881
msgid "If Varnish runs on your server, you must activate this add-on."
msgstr "Se il tuo server usa Varnish, devi attivare questo componente aggiuntivo."

#: inc/Engine/Admin/Settings/Page.php:1883
#, php-format
msgid "Varnish cache will be purged each time WP Rocket clears its cache to ensure content is always up-to-date.<br>%1$sLearn more%2$s"
msgstr "La cache Varnish verrà cancellata allo stesso tempo della cache WP Rocket così il contenuto del tuo sito sarà sempre aggiornato.<br>%1$sMaggiori informazioni%2$s"

#: inc/Engine/Admin/Settings/Page.php:1948
msgid "Clear the Sucuri cache when WP Rocket’s cache is cleared."
msgstr "Cancella la cache Sucuri e la cache WP Rocket allo stesso tempo."

#: inc/Engine/Admin/Settings/Page.php:1951
msgid "Provide your API key to clear the Sucuri cache when WP Rocket’s cache is cleared."
msgstr "Inserisci la tua chiave API per cancellare la cache di Sucuri quando viene cancellata quella di WP Rocket."

#: inc/Engine/Admin/Settings/Page.php:1959
#: inc/Engine/Admin/Settings/Page.php:2103
msgid "Sucuri"
msgstr "Sucuri"

#: inc/Engine/Admin/Settings/Page.php:1965
msgid "Synchronize Sucuri cache with this add-on."
msgstr "Sincronizza la cache di Sucuri con questo add-on."

#: inc/Engine/Admin/Settings/Page.php:2003
msgid "Cloudflare credentials"
msgstr "Credenziali Clouflare"

#: inc/Engine/Admin/Settings/Page.php:2012
msgid "Cloudflare settings"
msgstr "Impostazioni Cloudflare"

#: inc/Engine/Admin/Settings/Page.php:2026
msgctxt "Cloudflare"
msgid "Global API key:"
msgstr "Chiave API Globale:"

#: inc/Engine/Admin/Settings/Page.php:2027
msgctxt "Cloudflare"
msgid "Find your API key"
msgstr "Trova la tua Chiave API"

#: inc/Engine/Admin/Settings/Page.php:2039
msgctxt "Cloudflare"
msgid "Account email"
msgstr "Account email"

#: inc/Engine/Admin/Settings/Page.php:2048
msgctxt "Cloudflare"
msgid "Zone ID"
msgstr "Zone ID"

#: inc/Engine/Admin/Settings/Page.php:2058
msgid "Development mode"
msgstr "Modalità sviluppo"

#: inc/Engine/Admin/Settings/Page.php:2060
#, php-format
msgid "Temporarily activate development mode on your website. This setting will automatically turn off after 3 hours. %1$sLearn more%2$s"
msgstr "Attiva temporaneamente la modalità sviluppo sul tuo sito. Questa impostazione verrà disattivata automaticamente dopo 3 ore. %1$sMaggiori informazioni%2$s"

#: inc/Engine/Admin/Settings/Page.php:2068
msgid "Optimal settings"
msgstr "Impostazioni ottimali"

#: inc/Engine/Admin/Settings/Page.php:2069
msgid "Automatically enhances your Cloudflare configuration for speed, performance grade and compatibility."
msgstr "Migliora automaticamente la configurazione del tuo Cloudflare in termini di velocità, valutazione della performance e compatibilità."

#: inc/Engine/Admin/Settings/Page.php:2077
msgid "Relative protocol"
msgstr "Protocollo relativo"

#: inc/Engine/Admin/Settings/Page.php:2078
msgid "Should only be used with Cloudflare's flexible SSL feature. URLs of static files (CSS, JS, images) will be rewritten to use // instead of http:// or https://."
msgstr "Dovrebbe essere usato solo insieme all'opzione Flexible SSL di Cloudflare. Gli URL dei file statici (CSS, JS, immagini) verranno riscritti per usare // invece che http:// o https://."

#: inc/Engine/Admin/Settings/Page.php:2116
msgid "Sucuri credentials"
msgstr "Credenziali di accesso per Sucuri"

#: inc/Engine/Admin/Settings/Page.php:2130
msgctxt "Sucuri"
msgid "Find your API key"
msgstr "Trova la tua Chiave API"

#: inc/Engine/Admin/Settings/Render.php:422
#: inc/deprecated/deprecated.php:1294
msgid "Upload file and import settings"
msgstr "Carica il file e importa le impostazioni"

#: inc/Engine/Admin/Settings/Settings.php:362
msgid "Sucuri Add-on: The API key for the Sucuri firewall must be in format <code>{32 characters}/{32 characters}</code>."
msgstr "Add-on Sucuri: la chiave API per il firewall Sucuri deve essere in formato <code>{32 caratteri}/{32 caratteri}</code>."

#: inc/Engine/Admin/Settings/Settings.php:452
#: inc/deprecated/deprecated.php:1245
msgid "Settings saved."
msgstr "Impostazioni salvate."

#: inc/Engine/Admin/Settings/Subscriber.php:168
#: inc/deprecated/deprecated.php:1786
#: views/settings/page-sections/tools.php:33
msgid "Tools"
msgstr "Strumenti"

#: inc/Engine/Admin/Settings/Subscriber.php:169
msgid "Import, Export, Rollback"
msgstr "Importa, Esporta, Rollback"

#: inc/Engine/Admin/Settings/Subscriber.php:194
#: views/settings/page-sections/imagify.php:14
msgid "Image Optimization"
msgstr "Ottimizzazione immagini"

#: inc/Engine/Admin/Settings/Subscriber.php:195
msgid "Compress your images"
msgstr "Comprimi le tue immagini"

#: inc/Engine/Admin/Settings/Subscriber.php:212
#: views/settings/page-sections/tutorials.php:48
msgid "Tutorials"
msgstr "Tutorial"

#: inc/Engine/Admin/Settings/Subscriber.php:213
msgid "Getting started and how to videos"
msgstr "Video tutorial per iniziare con WP Rocket (in inglese)"

#: inc/Engine/CDN/RocketCDN/APIClient.php:177
msgid "RocketCDN cache purge failed: Missing identifier parameter."
msgstr "L'eliminazione della cache RocketCDN non è riuscita: manca il parametro di identificazione."

#: inc/Engine/CDN/RocketCDN/APIClient.php:186
msgid "RocketCDN cache purge failed: Missing user token."
msgstr "L'eliminazione della cache RocketCDN non è riuscita: manca il token utente."

#: inc/Engine/CDN/RocketCDN/APIClient.php:212
msgid "RocketCDN cache purge failed: The API returned an unexpected response code."
msgstr "L'eliminazione della cache RocketCDN non è riuscita: l'API ha restituito un codice di risposta inaspettato."

#: inc/Engine/CDN/RocketCDN/APIClient.php:221
msgid "RocketCDN cache purge failed: The API returned an empty response."
msgstr "L'eliminazione della cache RocketCDN non è riuscita: l'API ha restituito una risposta vuota."

#: inc/Engine/CDN/RocketCDN/APIClient.php:230
msgid "RocketCDN cache purge failed: The API returned an unexpected response."
msgstr "L'eliminazione della cache RocketCDN non è riuscita: l'API ha restituito una risposta inaspettata."

#: inc/Engine/CDN/RocketCDN/APIClient.php:239
#, php-format
msgid "RocketCDN cache purge failed: %s."
msgstr "L'eliminazione della cache RocketCDN non è riuscita: %s."

#: inc/Engine/CDN/RocketCDN/APIClient.php:247
msgid "RocketCDN cache purge successful."
msgstr "La cache di RocketCDN è stata eliminata correttamente."

#: inc/Engine/CDN/RocketCDN/AdminPageSubscriber.php:90
msgid "Next Billing Date"
msgstr "Prossima data di fatturazione"

#: inc/Engine/CDN/RocketCDN/AdminPageSubscriber.php:99
msgid "No Subscription"
msgstr "Nessun abbonamento"

#: inc/Engine/CDN/RocketCDN/AdminPageSubscriber.php:135
msgid "Your RocketCDN subscription is currently active."
msgstr "Il tuo abbonamento a RocketCDN è ora attivo."

#: inc/Engine/CDN/RocketCDN/AdminPageSubscriber.php:141
#, php-format
msgid "To use RocketCDN, replace your CNAME with %1$s%2$s%3$s."
msgstr "Per usare RocketCDN sostituisci il CNAME con %1$s%2$s%3$s."

#: inc/Engine/CDN/RocketCDN/AdminPageSubscriber.php:152
#: inc/Engine/CDN/RocketCDN/NoticesSubscriber.php:230
#: inc/Engine/CDN/RocketCDN/NoticesSubscriber.php:334
#, php-format
msgid "%1$sMore Info%2$s"
msgstr "%1$sPiù info%2$s"

#: inc/Engine/CDN/RocketCDN/RESTSubscriber.php:137
msgid "RocketCDN enabled"
msgstr "RocketCDN è attivo"

#: inc/Engine/CDN/RocketCDN/RESTSubscriber.php:160
msgid "RocketCDN disabled"
msgstr "RocketCDN non è attivo"

#: inc/Engine/Cache/AdminSubscriber.php:118
#: inc/admin/admin.php:96
#: inc/admin/admin.php:118
#: inc/deprecated/3.5.php:898
msgid "Clear this cache"
msgstr "Cancella questa cache"

#: inc/Engine/Cache/WPCache.php:337
msgid "WP_CACHE value"
msgstr "Valore di WP_CACHE"

#: inc/Engine/Cache/WPCache.php:358
msgid "The WP_CACHE constant needs to be set to true for WP Rocket cache to work properly"
msgstr "La costante WP_CACHE deve essere impostata su true affinché la cache WP Rocket funzioni correttamente"

#: inc/Engine/Cache/WPCache.php:367
msgid "WP_CACHE is set to true"
msgstr "WP_CACHE è impostata su true"

#: inc/Engine/Cache/WPCache.php:375
msgid "WP_CACHE is not set"
msgstr "WP_CACHE non è impostata"

#: inc/Engine/Cache/WPCache.php:383
msgid "WP_CACHE is set to false"
msgstr "WP_CACHE è impostata su false"

#: inc/Engine/CriticalPath/APIClient.php:64
#, php-format
msgid "Critical CSS for %1$s not generated. Error: %2$s"
msgstr "Non è stato possibile generare il CSS critico per %1$s. Errore: %2$s"

#: inc/Engine/CriticalPath/APIClient.php:170
#, php-format
msgid "Critical CSS for %1$s on mobile not generated. Error: The API returned an empty response."
msgstr "Non è stato possibile generare il CSS critico mobile per %1$s. Errore: l'API ha restituito una risposta vuota."

#: inc/Engine/CriticalPath/APIClient.php:173
#, php-format
msgid "Critical CSS for %1$s not generated. Error: The API returned an empty response."
msgstr "Non è stato possibile generare il CSS critico per %1$s. Errore: l'API ha restituito una risposta vuota."

#: inc/Engine/CriticalPath/APIClient.php:185
#, php-format
msgid "Critical CSS for %1$s on mobile not generated."
msgstr "Non è stato possibile generare il CSS critico per mobile per%1$s."

#: inc/Engine/CriticalPath/APIClient.php:187
#: inc/Engine/CriticalPath/ProcessorService.php:194
#, php-format
msgid "Critical CSS for %1$s not generated."
msgstr "Non è stato possibile generare il CSS critico per %1$s."

#: inc/Engine/CriticalPath/APIClient.php:195
#, php-format
msgid "Critical CSS for %1$s on mobile not generated. Error: The API returned an invalid response code."
msgstr "Non è stato possibile generare il CSS critico mobile per %1$s. Errore: l'API ha restituito un codice di risposta non valido."

#: inc/Engine/CriticalPath/APIClient.php:197
#, php-format
msgid "Critical CSS for %1$s not generated. Error: The API returned an invalid response code."
msgstr "Non è stato possibile generare il CSS critico per %1$s. Errore: l'API ha restituito un codice di risposta non valido."

#: inc/Engine/CriticalPath/APIClient.php:205
#: inc/Engine/CriticalPath/ProcessorService.php:201
#, php-format
msgid "Error: %1$s"
msgstr "Errore: %1$s"

#: inc/Engine/CriticalPath/Admin/Admin.php:264
msgid "Regenerate Critical Path CSS"
msgstr "Rigenera il percorso critico del CSS"

#: inc/Engine/CriticalPath/Admin/Post.php:144
#: views/cpcss/metabox/generate.php:47
msgid "Generate Specific CPCSS"
msgstr "Genera il percorso critico CSS specifico."

#: inc/Engine/CriticalPath/Admin/Post.php:145
#: views/cpcss/metabox/generate.php:45
msgid "Regenerate specific CPCSS"
msgstr "Rigenera il CPCSS specifico"

#: inc/Engine/CriticalPath/Admin/Post.php:219
msgid "%l to use this feature."
msgstr "%l per usare questa funzionalità."

#: inc/Engine/CriticalPath/Admin/Post.php:222
#, php-format
msgid "Publish the %s"
msgstr "Pubblica il %s"

#: inc/Engine/CriticalPath/CriticalCSSSubscriber.php:176
msgid "Critical CSS generation is currently running."
msgstr "La generazione del CSS critico è in esecuzione."

#: inc/Engine/CriticalPath/CriticalCSSSubscriber.php:181
#, php-format
msgid "Go to the %1$sWP Rocket settings%2$s page to track progress."
msgstr "Vai alla pagina %1$sImpostazioni WP Rocket%2$s per monitorare il progresso."

#: inc/Engine/CriticalPath/CriticalCSSSubscriber.php:396
#, php-format
msgid "Critical CSS generation is currently running: %1$d of %2$d page types completed. (Refresh this page to view progress)"
msgstr "La generazione del CSS critico è in corso: %1$d di %2$d tipi di pagina sono stati completati. (Aggiorna la pagina per vedere il progresso)"

#: inc/Engine/CriticalPath/CriticalCSSSubscriber.php:472
#, php-format
msgid "Critical CSS generation finished for %1$d of %2$d page types."
msgstr "La generazione del CSS critico è terminata per %1$d di %2$d tipi di pagina."

#: inc/Engine/CriticalPath/CriticalCSSSubscriber.php:479
msgid "Critical CSS generation encountered one or more errors."
msgstr "Si sono verificati uno o più errori nella generazione del CSS critico."

#: inc/Engine/CriticalPath/CriticalCSSSubscriber.php:479
msgid "Learn more."
msgstr "Maggiori informazioni."

#: inc/Engine/CriticalPath/DataManager.php:106
msgid "Critical CSS file for mobile does not exist"
msgstr "Il file del CSS critico mobile non esiste"

#: inc/Engine/CriticalPath/DataManager.php:108
msgid "Critical CSS file does not exist"
msgstr "Il file del CSS critico non esiste"

#: inc/Engine/CriticalPath/DataManager.php:120
msgid "Critical CSS file for mobile cannot be deleted"
msgstr "Non è possibile eliminare il file del CSS critico mobile"

#: inc/Engine/CriticalPath/DataManager.php:122
msgid "Critical CSS file cannot be deleted"
msgstr "Non è possibile eliminare il file del CSS critico"

#: inc/Engine/CriticalPath/ProcessorService.php:187
#, php-format
msgid "Mobile Critical CSS for %1$s not generated."
msgstr "Non è stato possibile generare il CSS critico mobile per %1$s. "

#: inc/Engine/CriticalPath/ProcessorService.php:228
#, php-format
msgid "Critical CSS for %s in progress."
msgstr "Stiamo generando il CSS critico per %s."

#: inc/Engine/CriticalPath/ProcessorService.php:262
#, php-format
msgid "Mobile Critical CSS for %s generated."
msgstr "Il CSS critico mobile per %s è stato generato."

#: inc/Engine/CriticalPath/ProcessorService.php:273
#, php-format
msgid "Critical CSS for %s generated."
msgstr "Il CSS critico per %s è stato generato."

#: inc/Engine/CriticalPath/ProcessorService.php:295
msgid "Critical CSS file deleted successfully."
msgstr "Il file del CSS critico è stato eliminato correttamente."

#: inc/Engine/CriticalPath/ProcessorService.php:317
#, php-format
msgid "Mobile Critical CSS for %1$s timeout. Please retry a little later."
msgstr "Il CSS critico mobile per %1$s è entrato in timeout. Riprova più tardi."

#: inc/Engine/CriticalPath/ProcessorService.php:330
#, php-format
msgid "Critical CSS for %1$s timeout. Please retry a little later."
msgstr "Il CSS critico per %1$s è entrato in timeout. Riprova più tardi."

#: inc/Engine/CriticalPath/RESTWP.php:141
msgid "Mobile CPCSS generation not enabled."
msgstr "La generazione del CPCSS mobile non è abilitata."

#: inc/Engine/CriticalPath/RESTWPPost.php:36
#: inc/Engine/CriticalPath/RESTWPPost.php:69
msgid "Requested post does not exist."
msgstr "L'articolo richiesto non esiste."

#: inc/Engine/CriticalPath/RESTWPPost.php:46
msgid "Cannot generate CPCSS for unpublished post."
msgstr "Non è possibile generare il percorso critico del CSS per gli articoli non pubblicati."

#: inc/Engine/HealthCheck/HealthCheck.php:82
#: inc/deprecated/3.5.php:858
msgid "The following scheduled event failed to run. This may indicate the CRON system is not running properly, which can prevent some WP Rocket features from working as intended:"
msgid_plural "The following scheduled events failed to run. This may indicate the CRON system is not running properly, which can prevent some WP Rocket features from working as intended:"
msgstr[0] "Il seguenti evento programmato non è stato eseguito. Questo potrebbe indicare che il sistema CRON non stia funzionando correttamente, il che può impedire ad alcune funzionalità di WP Rocket di operare come previsto:"
msgstr[1] "I seguenti eventi programmati non sono stati eseguiti. Questo potrebbe indicare che il sistema CRON non stia funzionando correttamente, il che può impedire ad alcune funzionalità di WP Rocket di operare come previsto:"

#: inc/Engine/HealthCheck/HealthCheck.php:88
#: inc/deprecated/3.5.php:867
msgid "Please contact your host to check if CRON is working."
msgstr "Contatta il tuo host per verificare se il CRON sta funzionando."

#: inc/Engine/HealthCheck/HealthCheck.php:142
msgid "Scheduled Cache Purge"
msgstr "Eliminazione della cache programmata"

#: inc/Engine/HealthCheck/HealthCheck.php:143
msgid "Scheduled Database Optimization"
msgstr "Ottimizzazione del database programmata"

#: inc/Engine/HealthCheck/HealthCheck.php:144
msgid "Database Optimization Process"
msgstr "Processo di ottimizzazione del database"

#: inc/Engine/HealthCheck/HealthCheck.php:145
msgctxt "noun"
msgid "Preload"
msgstr "Precaricamento"

#: inc/Engine/HealthCheck/HealthCheck.php:146
msgid "Critical Path CSS Generation Process"
msgstr "Processo di generazione del CSS critico"

#: inc/ThirdParty/Hostings/Cloudways.php:82
#, php-format
msgid "Varnish auto-purge will be automatically enabled once Varnish is enabled on your %s server."
msgstr "L'opzione di eliminazione automatica della cache Varnish verrà automaticamente abilitata non appena Varnish sarà attivo sul tuo server %s."

#: inc/ThirdParty/Plugins/Optimization/Hummingbird.php:78
#, php-format
msgctxt "Hummingbird notice"
msgid "Please deactivate the following %s option which conflicts with WP Rocket features:"
msgid_plural "Please deactivate the following %s options which conflict with WP Rocket features:"
msgstr[0] "Disattiva la seguente %s opzione che entra in conflitto con WP Rocket:"
msgstr[1] "Disattiva le seguenti %s opzioni che entrano in conflitto con WP Rocket:"

#: inc/ThirdParty/Plugins/Optimization/Hummingbird.php:151
#, php-format
msgctxt "Hummingbird notice"
msgid "%1$s %2$sdisable emoji%3$s conflicts with WP Rockets %2$sdisable emoji%3$s"
msgstr "L'opzione %2$sDisattiva emoji%3$s di %1$s entra in conflitto con l'opzione %2$sDisattiva emoji%3$s di WP Rocket."

#: inc/ThirdParty/Plugins/Optimization/Hummingbird.php:187
#, php-format
msgctxt "Hummingbird notice"
msgid "%1$s %2$sGZIP compression%3$s conflicts with WP Rocket %2$sGZIP compression%3$s"
msgstr "La %2$scompressione GZIP%3$s di %1$s entra in conflitto con la %2$sCompression GZIP%3$s di WP Rocket."

#: inc/ThirdParty/Plugins/Optimization/Hummingbird.php:223
#, php-format
msgctxt "Hummingbird notice"
msgid "%1$s %2$sbrowser caching%3$s conflicts with WP Rocket %2$sbrowser caching%3$s"
msgstr "La %2$scache del browser%3$s di %1$s entra in conflitto con la %2$scache del browser%3$s di WP Rocket."

#: inc/ThirdParty/Plugins/Optimization/Hummingbird.php:255
#, php-format
msgctxt "Hummingbird notice"
msgid "%1$s %2$spage caching%3$s conflicts with WP Rocket %2$spage caching%3$s"
msgstr "La %2$scache di pagina%3$s di %1$s entra in conflitto con la %2$scache di pagina%3$s di WP Rocket."

#: inc/ThirdParty/Plugins/Optimization/Hummingbird.php:289
#, php-format
msgctxt "Hummingbird notice"
msgid "%1$s %2$sasset optimization%3$s conflicts with WP Rocket %2$sfile optimization%3$s"
msgstr "L'%2$sottimizzazione degli asset%3$s di %1$s entra in conflitto con la sezione %2$sOttimizzazione File%3$s di WP Rocket."

#: inc/ThirdParty/Plugins/Smush.php:108
#: inc/ThirdParty/Plugins/Smush.php:126
msgid "Smush"
msgstr "Smush"

#: inc/admin/admin.php:18
#: inc/common/admin-bar.php:255
#: inc/deprecated/deprecated.php:1787
msgid "Support"
msgstr "Supporto"

#: inc/admin/admin.php:20
msgid "Docs"
msgstr "Docs"

#: inc/admin/admin.php:22
#: inc/common/admin-bar.php:243
msgid "FAQ"
msgstr "FAQ"

#: inc/admin/admin.php:24
#: inc/common/admin-bar.php:63
msgid "Settings"
msgstr "Impostazioni"

#: inc/Engine/Plugin/UpdaterSubscriber.php:472
#: inc/Engine/Plugin/UpdaterSubscriber.php:486
#: inc/classes/class-wp-rocket-requirements-check.php:203
#: inc/classes/class-wp-rocket-requirements-check.php:214
#: inc/deprecated/3.11.php:236
#: inc/deprecated/3.11.php:251
#, php-format
msgid "%s Update Rollback"
msgstr "Tornare all'aggiornamento %s"

#: inc/Engine/Plugin/UpdaterSubscriber.php:509
#: inc/deprecated/3.11.php:279
#, php-format
msgid "%1$sReturn to WP Rocket%2$s or %3$sgo to Plugins page%2$s"
msgstr "%1$sTorna a WP Rocket%2$s o %3$svai alla pagina dei Plugin%2$s"

#: inc/admin/admin.php:458
msgid "Settings import failed: you do not have the permissions to do this."
msgstr "Importazione delle impostazioni non riuscito: non hai i permessi per compiere questa azione."

#: inc/admin/admin.php:462
msgid "Settings import failed: no file uploaded."
msgstr "Importazione delle impostazioni non riuscito: nessun file caricato."

#: inc/admin/admin.php:466
msgid "Settings import failed: incorrect filename."
msgstr "Importazione delle impostazioni: nome del file errato."

#: inc/admin/admin.php:477
msgid "Settings import failed: incorrect filetype."
msgstr "Importazione delle impostazioni: tipologia di file non corretta."

#: inc/admin/admin.php:487
msgid "Settings import failed: "
msgstr "L'importazione delle impostazioni non è andata a buon fine:"

#: inc/admin/admin.php:503
msgid "Settings import failed: unexpected file content."
msgstr "L'importazione delle impostazioni non è andata a buon fine: contenuto del file inatteso."

#: inc/admin/admin.php:533
msgid "Settings imported and saved."
msgstr "Impostazioni importate e salvate."

#: inc/admin/options.php:160
msgid "The following pattern is invalid and has been removed:"
msgid_plural "The following patterns are invalid and have been removed:"
msgstr[0] "Il seguente pattern non è valido ed è stato rimosso:"
msgstr[1] "I seguenti pattern non sono validi e sono stati rimossi:"

#: inc/admin/ui/meta-boxes.php:37
#: inc/admin/ui/notices.php:748
#: inc/common/admin-bar.php:84
#: inc/common/admin-bar.php:136
#: views/settings/page-sections/dashboard.php:150
msgid "Clear cache"
msgstr "Cancella la cache"

#: inc/admin/ui/meta-boxes.php:67
msgid "WP Rocket Options"
msgstr "Opzioni WP Rocket"

#: inc/admin/ui/meta-boxes.php:96
msgid "Never cache this page"
msgstr "Non mettere in cache questa pagina"

#: inc/admin/ui/meta-boxes.php:100
msgid "Activate these options on this post:"
msgstr "Attiva le seguenti opzioni su questo articolo:"

#: inc/admin/ui/meta-boxes.php:103
msgid "LazyLoad for images"
msgstr "LazyLoad per le immagini"

#: inc/admin/ui/meta-boxes.php:104
msgid "LazyLoad for iframes/videos"
msgstr "LazyLoad per iframe e video"

#: inc/admin/ui/meta-boxes.php:105
msgid "Minify/combine CSS"
msgstr "Minifica/combina CSS"

#: inc/admin/ui/meta-boxes.php:107
msgid "Minify/combine JS"
msgstr "Minifica/combina JS"

#: inc/admin/ui/meta-boxes.php:110
msgid "Defer JS"
msgstr "JS differito"

#: inc/admin/ui/meta-boxes.php:117
#, php-format
msgid "Activate first the %s option."
msgstr "Attiva prima l'opzione %s."

#: inc/admin/ui/meta-boxes.php:133
#, php-format
msgid "%1$sNote:%2$s None of these options will be applied if this post has been excluded from cache in the global cache settings."
msgstr "%1$sNota:%2$s Nessuna di queste opzioni verrà applicata se questo post è stato precedentemente escluso dalla cache nelle opzioni generali."

#: inc/admin/ui/notices.php:31
#: inc/admin/ui/notices.php:44
#, php-format
msgid ""
"<strong>%1$s</strong> has not been deactivated due to missing writing permissions.<br>\n"
"Make <strong>%2$s</strong> writeable and retry deactivation, or force deactivation now:"
msgstr ""
"<strong>%1$s</strong> non è stato disattivato perché mancano i permessi di scrittura necessari.<br>\n"
"Rendi <strong>%2$s</strong> riscrivibile e riprova a disattivare, o forza la disattivazione ora:"

#: inc/admin/ui/notices.php:97
#, php-format
msgid "<strong>%s</strong>: One or more plugins have been enabled or disabled, clear the cache if they affect the front end of your site."
msgstr "<strong>%s</strong>: Uno o più plugin sono stati abilitati o disabilitati: cancella la cache se vedi qualche problema di visualizzazione sul tuo sito."

#: inc/admin/ui/notices.php:218
#, php-format
msgid "<strong>%s</strong>: The following plugins are not compatible with this plugin and may cause unexpected results:"
msgstr "<strong>%s</strong>: I seguenti plugin non sono compatibili con questo plugin e possono causare risultati indesiderati:"

#: inc/admin/ui/notices.php:224
msgid "Deactivate"
msgstr "Disattiva"

#: inc/admin/ui/notices.php:266
msgid "WP Rocket Footer JS is not an official add-on. It prevents some options in WP Rocket from working correctly. Please deactivate it if you have problems."
msgstr "Il plugin WP Rocket Footer JS non è un add-on ufficiale di WP Rocket, e può impedire ad alcune opzioni di WP Rocket di funzionare correttamente: disattivalo in caso di problemi."

#: inc/admin/ui/notices.php:306
#, php-format
msgid "Endurance Cache is currently enabled, which will conflict with WP Rocket Cache. Please set the Endurance Cache cache level to Off (Level 0) on the %1$sSettings > General%2$s page to prevent any issues."
msgstr "Endurance Cache è attivo, e questo crea un conflitto con la cache di WP Rocket. Per evitare problemi, imposta il livello di cache di Endurance Cache su Off (livello 0) nella pagina %1$sImpostazioni > Generali%2$s."

#: inc/admin/ui/notices.php:327
#, php-format
msgid "%1$s: A custom permalink structure is required for the plugin to work properly. %2$sGo to permalinks settings%3$s"
msgstr "%1$s: È necessario impostare una struttura dei permalink personalizzata per far funzionare correttamente il plugin. %2$sVai alle impostazioni dei permalink%3$s"

#: inc/admin/ui/notices.php:374
#, php-format
msgid "%s could not modify the .htaccess file due to missing writing permissions."
msgstr "%s non ha potuto modificare il file .htaccess perché mancano i permessi di scrittura."

#: inc/admin/ui/notices.php:380
#: inc/admin/ui/notices.php:843
#, php-format
msgid "Troubleshoot: %1$sHow to make system files writeable%2$s"
msgstr "Risoluzione del problema: %1$sCome rendere riscrivibili i file di sistema?%2$s"

#: inc/admin/ui/notices.php:382
#: inc/admin/ui/notices.php:845
msgid "https://docs.wp-rocket.me/article/626-how-to-make-system-files-htaccess-wp-config-writeable/?utm_source=wp_plugin&utm_medium=wp_rocket"
msgstr "https://docs.wp-rocket.me/article/626-how-to-make-system-files-htaccess-wp-config-writeable/?utm_source=wp_plugin&utm_medium=wp_rocket"

#: inc/admin/ui/notices.php:388
msgid "Don’t worry, WP Rocket’s page caching and settings will still function correctly."
msgstr "Non ti preoccupare, il caching di pagina di WP Rocket e le altre impostazioni funzioneranno correttamente."

#: inc/admin/ui/notices.php:388
msgid "For optimal performance, adding the following lines into your .htaccess is recommended (not required):"
msgstr "Per una prestazione ottimale, ti raccomandiamo di aggiungere le seguenti linee nel file .htaccess (ma non è obbligatorio):"

#: inc/admin/ui/notices.php:535
#, php-format
msgid "%1$s is good to go! %2$sTest your load time%4$s, or visit your %3$ssettings%4$s."
msgstr "%1$s è pronto a partire! %2$sFai un test della velocità di caricamento%4$s, o verifica le tue %3$simpostazioni%4$s."

#: inc/admin/ui/notices.php:576
msgid "Would you allow WP Rocket to collect non-sensitive diagnostic data from this website?"
msgstr "Vuoi permettere a WP Rocket di raccogliere da questo sito web dei dati di diagnostica non sensibili?"

#: inc/admin/ui/notices.php:577
msgid "This would help us to improve WP Rocket for you in the future."
msgstr "Questo in futuro ci permetterà di migliorare WP Rocket per te."

#: inc/admin/ui/notices.php:583
msgid "What info will we collect?"
msgstr "Quali dati raccogliamo?"

#: inc/admin/ui/notices.php:588
msgid "Below is a detailed view of all data WP Rocket will collect if granted permission. WP Rocket will never transmit any domain names or email addresses (except for license validation), IP addresses, or third-party API keys."
msgstr "Qui sotto trovi una lista dettagliata dei dati che WP Rocket raccoglierà, se vorrai dargli il permesso di farlo. WP Rocket non trasmetterà nessun nome di dominio o indirizzo email (eccetto che per la validazione della licenza), indirizzi IP o le chiavi API di terze parti."

#: inc/admin/ui/notices.php:597
msgid "Yes, allow"
msgstr "Si, ve lo permetto"

#: inc/admin/ui/notices.php:600
msgid "No, thanks"
msgstr "No, grazie"

#: inc/admin/ui/notices.php:639
msgid "Thank you!"
msgstr "Grazie!"

#: inc/admin/ui/notices.php:644
msgid "WP Rocket now collects these metrics from your website:"
msgstr "WP Rocket ora collezionerà queste metriche dal tuo sito web:"

#: inc/admin/ui/notices.php:682
#, php-format
msgid "%s: Cache cleared."
msgstr "%s: Cache cancellata."

#: inc/admin/ui/notices.php:689
#, php-format
msgid "%s: Post cache cleared."
msgstr "%s: Cache dell'articolo cancellata."

#: inc/admin/ui/notices.php:696
#, php-format
msgid "%s: Term cache cleared."
msgstr "%s: Cache del termine cancellata."

#: inc/admin/ui/notices.php:703
#, php-format
msgid "%s: User cache cleared."
msgstr "%s: Cache utente cancellata."

#: inc/admin/ui/notices.php:751
msgid "Stop Preload"
msgstr "Interrompi il precaricamento"

#: inc/admin/ui/notices.php:781
msgid "Force deactivation "
msgstr "Forza la disattivazione "

#: inc/admin/ui/notices.php:800
msgid "The following code should have been written to this file:"
msgstr "Sarebbe stato necessario scrivere il codice seguente su questo file:"

#: inc/admin/ui/notices.php:831
#, php-format
msgid "%s cannot configure itself due to missing writing permissions."
msgstr "%s non può configurarsi perché mancano dei permessi di scrittura."

#: inc/admin/ui/notices.php:837
#, php-format
msgid "Affected file/folder: %s"
msgstr "File/cartella interessata: %s"

#: inc/Engine/Admin/Database/Optimization.php:35
msgid "Transients"
msgstr "Transient"

#: inc/Engine/Admin/Database/Optimization.php:36
msgid "Tables"
msgstr "Tabelle"

#: inc/classes/admin/class-logs.php:124
msgid "The debug file could not be deleted."
msgstr "Non è stato possibile eliminare il file di debug."

#: inc/classes/class-wp-rocket-requirements-check.php:147
#, php-format
msgid "To function properly, %1$s %2$s requires at least:"
msgstr "Per funzionare correttamente, %1$s %2$s richiede almeno:"

#: inc/classes/class-wp-rocket-requirements-check.php:151
#, php-format
msgid "PHP %1$s. To use this WP Rocket version, please ask your web host how to upgrade your server to PHP %1$s or higher."
msgstr "PHP %1$s. Per usare questa versione di WP Rocket, chiedi al tuo host come aggiornare il tuo server a PHP %1$s o superiore."

#: inc/classes/class-wp-rocket-requirements-check.php:156
#, php-format
msgid "WordPress %1$s. To use this WP Rocket version, please upgrade WordPress to version %1$s or higher."
msgstr "WordPress %1$s. Per usare questa versione di WP Rocket, aggiorna WordPress alla versione %1$s o superiore."

#: inc/classes/class-wp-rocket-requirements-check.php:159
msgid "If you are not able to upgrade, you can rollback to the previous version by using the button below."
msgstr "Se non ti è possibile fare l’aggiornamento, puoi ritornare alla versione precedente usando il pulsante qui sotto."

#: inc/classes/class-wp-rocket-requirements-check.php:161
#: inc/deprecated/deprecated.php:1947
#, php-format
msgid "Re-install version %s"
msgstr "Reinstalla la versione %s"

#: inc/classes/dependencies/wp-media/background-processing/wp-background-process.php:447
#, php-format
msgid "Every %d Minutes"
msgstr "Ogni %d minuti"

#: inc/classes/logger/class-logger.php:260
#: inc/classes/logger/class-logger.php:292
msgid "The log file does not exist."
msgstr "Il file di registro (log) non esiste."

#: inc/classes/logger/class-logger.php:266
#: inc/classes/logger/class-logger.php:298
msgid "The log file could not be read."
msgstr "Non è stato possibile leggere il file di registro (log)."

#: inc/classes/logger/class-logger.php:285
msgid "The logs are not saved into a file."
msgstr "I registri (log) non sono stati salvati in un file."

#: inc/Engine/Cache/PurgeExpired/Subscriber.php:75
msgid "WP Rocket Expired Cache Interval"
msgstr "Intervallo della cache WP Rocket scaduto"

#: inc/Addon/WebP/AdminSubscriber.php:105
msgid "WebP cache is disabled by filter."
msgstr "La cache WebP è stata disattivata tramite filtro."

#: inc/Addon/WebP/AdminSubscriber.php:115
#: inc/Addon/WebP/AdminSubscriber.php:141
#, php-format
msgid "You are using %1$s to convert images to WebP. If you want WP Rocket to serve them for you, activate this option. %2$sMore info%3$s"
msgid_plural "You are using %1$s to convert images to WebP. If you want WP Rocket to serve them for you, activate this option. %2$sMore info%3$s"
msgstr[0] "Stai usando %1$sper convertire le immagini in formato WebP. Se vuoi che WP Rocket si occupi di servire questi file, attiva questa opzione. %2$sPiù info%3$s"
msgstr[1] "Stai usando %1$s per convertire le immagini in formato WebP. Se vuoi che WP Rocket si occupi di servire questi file, attiva questa opzione. %2$sPiù info%3$s"

#: inc/Addon/WebP/AdminSubscriber.php:127
#: inc/Addon/WebP/AdminSubscriber.php:153
#, php-format
msgid "You are using %1$s to convert images to WebP. WP Rocket will create separate cache files to serve your WebP images. %2$sMore info%3$s"
msgid_plural "You are using %1$s to convert images to WebP. WP Rocket will create separate cache files to serve your WebP images. %2$sMore info%3$s"
msgstr[0] "Stai usando %1$s per convertire le immagini in formato WebP. WP Rocket creerà un file di cache separato per servire le tue immagini WebP. %2$sPiù info%3$s"
msgstr[1] "Stai usando %1$s per convertire le immagini in formato WebP. WP Rocket creerà un file di cache separato per servire le tue immagini WebP. %2$sPiù info%3$s"

#: inc/Addon/WebP/AdminSubscriber.php:185
msgid "WP Rocket will create separate cache files to serve your WebP images."
msgstr "WP Rocket creerà dei file di cache separati per servire le tue immagini WebP."

#: inc/classes/subscriber/Tools/class-detect-missing-tags-subscriber.php:151
#, php-format
msgid "Failed to detect the following requirement in your theme: closing %1$s."
msgid_plural "Failed to detect the following requirements in your theme: closing %1$s."
msgstr[0] "Non siamo riusciuti a identificare il seguente requisito nel tuo tema: %1$s di chiusura."
msgstr[1] "Non siamo riusciuti a identificare i seguenti requisiti nel tuo tema: %1$s di chiusura."

#: inc/classes/subscriber/Tools/class-detect-missing-tags-subscriber.php:159
msgid "https://docs.wp-rocket.me/article/99-pages-not-cached-or-minify-cssjs-not-working/?utm_source=wp_plugin&utm_medium=wp_rocket#theme"
msgstr "https://docs.wp-rocket.me/article/99-pages-not-cached-or-minify-cssjs-not-working/?utm_source=wp_plugin&utm_medium=wp_rocket#theme"

#: inc/Engine/Admin/Database/Subscriber.php:85
msgid "monthly"
msgstr "mensile"

#: inc/Engine/Admin/Database/Subscriber.php:194
msgid "Database optimization process is running"
msgstr "Il processo di ottimizzazione del database è in corso"

#: inc/Engine/Admin/Database/Subscriber.php:224
msgid "Database optimization process is complete. Everything was already optimized!"
msgstr "Il processo di ottimizzazione del database è completo. Era già tutto ottimizzato!"

#: inc/Engine/Admin/Database/Subscriber.php:227
msgid "Database optimization process is complete. List of optimized items below:"
msgstr "Il processo di ottimizzazione del database è completo. Ecco la lista degli elementi ottimizzati:"

#: inc/Engine/Admin/Database/Subscriber.php:235
#, php-format
msgid "%1$d %2$s optimized."
msgstr "%1$d %2$s ottimizzati."

#: inc/Addon/Sucuri/Subscriber.php:95
#, php-format
msgid "Sucuri cache purge error: %s"
msgstr "Errore nell'eliminazione della cache Sucuri: %s"

#: inc/Addon/Sucuri/Subscriber.php:100
msgid "The Sucuri cache is being cleared. Note that it may take up to two minutes for it to be fully flushed."
msgstr "Stiamo cancellando la cache Sucuri. Tieni in considerazione che questo processo di pulizia potrebbe durare fino a 2 minuti."

#: inc/Addon/Sucuri/Subscriber.php:217
msgid "Sucuri firewall API key was not found."
msgstr "La chiave API del firewall Sucuri non è stata trovata."

#: inc/Addon/Sucuri/Subscriber.php:230
msgid "Sucuri firewall API key is invalid."
msgstr "La chiave API del firewall Sucuri non è valida."

#: inc/Addon/Sucuri/Subscriber.php:285
#, php-format
msgid "Error when contacting Sucuri firewall API. Error message was: %s"
msgstr "C'è stato un errore quando abbiamo provato a contattare l'API del firewall di Sucuri. Il messaggio di errore era: %s"

#: inc/Addon/Sucuri/Subscriber.php:300
msgid "Could not get a response from the Sucuri firewall API."
msgstr "Non è stato possibile avere una risposta dall'API del firewall di Sucuri."

#: inc/Addon/Sucuri/Subscriber.php:315
msgid "Got an invalid response from the Sucuri firewall API."
msgstr "Abbiamo ricevuto una risposta non valida dall'API del firewall di Sucuri."

#: inc/Addon/Sucuri/Subscriber.php:329
msgid "The Sucuri firewall API returned an unknown error."
msgstr "L'API del firewall di Sucuri ha restituito un errore sconosciuto."

#: inc/Addon/Sucuri/Subscriber.php:333
#, php-format
msgid "The Sucuri firewall API returned the following error: %s"
msgid_plural "The Sucuri firewall API returned the following errors: %s"
msgstr[0] "L'API del firewall di Sucuri ha restituito il seguente errore: %s"
msgstr[1] "L'API del firewall di Sucuri ha restituito i seguenti errori: %s"

#: inc/Engine/Plugin/UpdaterApiTools.php:32
#: inc/deprecated/3.3.php:761
#: inc/deprecated/3.3.php:770
#, php-format
msgid "An unexpected error occurred. Something may be wrong with WP-Rocket.me or this server&#8217;s configuration. If you continue to have problems, <a href=\"%s\">contact support</a>."
msgstr "Si è verificato un errore imprevisto. Potrebbe esserci qualcosa che non va su  wp-rocket.me o nella configurazione di questo server. Se il problema persiste, <a href=\"%s\">contatta il servizio di supporto</a>."

#: inc/common/admin-bar.php:124
#: inc/functions/i18n.php:41
#: inc/functions/i18n.php:51
msgid "All languages"
msgstr "Tutte le lingue"

#: inc/common/admin-bar.php:160
msgid "Clear this post"
msgstr "Svuota la cache di questo articolo"

#: inc/common/admin-bar.php:174
msgid "Purge this URL"
msgstr "Elimina la cache per questo URL"

#: inc/common/admin-bar.php:194
msgid "Purge Sucuri cache"
msgstr "Elimina la cache di Sucuri"

#: inc/common/admin-bar.php:218
#: views/settings/fields/rocket-cdn.php:55
msgid "Purge RocketCDN cache"
msgstr "Elimina la cache di RocketCDN"

#: inc/common/admin-bar.php:231
#: views/settings/partials/documentation.php:14
msgid "Documentation"
msgstr "Documentazione"

#: inc/deprecated/3.10.php:163
msgid "OPcache purge failed."
msgstr "La pulizia di OPcache è fallita."

#: inc/deprecated/3.10.php:168
msgid "OPcache successfully purged"
msgstr "OPcache è stata ripulita con successo"

#: inc/deprecated/3.2.php:52
#: views/settings/page-sections/imagify.php:36
msgid "Activate Imagify"
msgstr "Attiva Imagify"

#: inc/deprecated/3.2.php:52
msgid "Install Imagify for Free"
msgstr "Installa Imagify gratuitamente"

#: inc/deprecated/3.2.php:67
msgid "Speed up your website and boost your SEO by reducing image file sizes without losing quality with Imagify."
msgstr "Velocizza il tuo sito web e dai un'accelerata al tuo SEO riducendo la dimensione dei file immagine senza perdere qualità grazie a Imagify."

#: inc/deprecated/3.2.php:72
msgid "More details"
msgstr "Maggiori informazioni"

#: inc/deprecated/3.2.php:228
#, php-format
msgid "Sitemap preload: %d pages have been cached."
msgstr "Precaricamento sitemap: è stata creata la cache per %d pagine."

#: inc/deprecated/3.2.php:261
#, php-format
msgid "Sitemap preload: %d uncached pages have now been preloaded. (refresh to see progress)"
msgstr "Precaricamento sitemap: sono state precaricate %d pagine che non erano in cache. (Aggiorna per verificare il progresso)"

#: inc/deprecated/3.4.php:18
msgid "Choose a domain from the list"
msgstr "Scegliere un dominio dalla lista"

#: inc/deprecated/3.4.php:31
msgid "No domain available in your Cloudflare account"
msgstr "Non ci sono domini disponibili nel tuo account CloudFlare"

#: inc/deprecated/3.5.php:71
#: inc/deprecated/3.5.php:195
msgid "Curl is disabled on your server. Please ask your host to enable it. This is required for the Cloudflare Add-on to work correctly."
msgstr "Curl è disabilitato sul tuo server e dovresti chiedere al tuo host di abilitarlo: è necessario affinché l'Add-on di Cloudflare funzioni correttamente."

#: inc/deprecated/3.5.php:79
#, php-format
msgid "Cloudflare email, API key and Zone ID are not set. Read the %1$sdocumentation%2$s for further guidance."
msgstr "L'email Cloudflare, la chiave API o il Zone ID non sono configurati. Leggi la %1$sdocumentazione%2$s per maggiori dettagli."

#: inc/deprecated/3.5.php:206
#, php-format
msgid "Cloudflare email and API key are not set. Read the %1$sdocumentation%2$s for further guidance."
msgstr "L'email Cloudflare e la chiave API non sono configurati. Leggi la %1$sdocumentazione%2$s per maggiori dettagli."

#: inc/deprecated/3.5.php:271
msgid "Connection to Cloudflare failed"
msgstr "Connessione a Cloudflare fallita"

#: inc/deprecated/DeprecatedClassTrait.php:54
#, php-format
msgid "The called class %1$s is <strong>deprecated</strong> since version %2$s! Use %3$s instead."
msgstr "La classe chiamata %1$s è <strong>obsoleta</strong> a partire dalla versione %2$s! Usa invece la %3$s."

#: inc/deprecated/DeprecatedClassTrait.php:65
#, php-format
msgid "The called class %1$s is <strong>deprecated</strong> since version %2$s!"
msgstr "La classe chiamata %1$s è <strong>obsoleta</strong> a partire dalla versione %2$s!"

#: inc/deprecated/deprecated.php:240
msgid "<strong>JS</strong> files with Deferred Loading JavaScript"
msgstr "File <strong>JS</strong> con caricamento JavaScript differito"

#: inc/deprecated/deprecated.php:293
msgid "Add URL"
msgstr "Aggiungi URL"

#: inc/deprecated/deprecated.php:1277
#: views/settings/fields/import-form.php:22
msgid "Before you can upload your import file, you will need to fix the following error:"
msgstr "Prima di poter caricare il tuo file di importazione, è necessario correggere il seguente errore:"

#: inc/deprecated/deprecated.php:1288
#: views/settings/fields/import-form.php:35
#, php-format
msgid "Choose a file from your computer (maximum size: %s)"
msgstr "Scegliere un file dal computer (dimensione massima: %s)"

#: inc/deprecated/deprecated.php:1385
msgid "Your Cloudflare credentials are valid."
msgstr "Le tue credenziali Clouflare sono valide."

#: inc/deprecated/deprecated.php:1389
msgid "Your Cloudflare credentials are invalid!"
msgstr "Le tue credenziali CloudFlare non sono valide!"

#: inc/deprecated/deprecated.php:1453
msgid "Save and optimize"
msgstr "Salva e ottimizza"

#: inc/deprecated/deprecated.php:1453
msgid "Optimize"
msgstr "Ottimizza"

#: inc/deprecated/deprecated.php:1464
#: inc/deprecated/deprecated.php:1666
msgctxt "screen-reader-text"
msgid "Note:"
msgstr "Nota:"

#: inc/deprecated/deprecated.php:1470
msgctxt "screen-reader-text"
msgid "Performance tip:"
msgstr "Suggerimento sulla performance:"

#: inc/deprecated/deprecated.php:1476
msgctxt "screen-reader-text"
msgid "Third-party feature detected:"
msgstr "Individuata funzione di un plugin di terzi:"

#: inc/deprecated/deprecated.php:1488
#: inc/deprecated/deprecated.php:1672
msgctxt "screen-reader-text"
msgid "Warning:"
msgstr "Attenzione:"

#: inc/deprecated/deprecated.php:1501
msgctxt "button text"
msgid "Download settings"
msgstr "Scarica impostazioni"

#: inc/deprecated/deprecated.php:1531
msgid "Replace site's hostname with:"
msgstr "Sostituisci il nome host del sito con:"

#: inc/deprecated/deprecated.php:1550
#: inc/deprecated/deprecated.php:1587
#: inc/deprecated/deprecated.php:1615
#: views/settings/fields/cnames.php:41
#: views/settings/fields/cnames.php:75
#: views/settings/fields/cnames.php:105
msgid "reserved for"
msgstr "riservato per"

#: inc/deprecated/deprecated.php:1552
#: inc/deprecated/deprecated.php:1589
#: inc/deprecated/deprecated.php:1617
#: views/settings/fields/cnames.php:44
#: views/settings/fields/cnames.php:78
#: views/settings/fields/cnames.php:108
msgid "All files"
msgstr "Tutti i file"

#: inc/deprecated/deprecated.php:1564
#: inc/deprecated/deprecated.php:1594
#: inc/deprecated/deprecated.php:1622
#: views/settings/fields/cnames.php:56
#: views/settings/fields/cnames.php:90
#: views/settings/fields/cnames.php:120
msgid "Images"
msgstr "Immagini"

#: inc/deprecated/deprecated.php:1635
#: views/settings/fields/cnames.php:132
msgid "Add CNAME"
msgstr "Aggiungi CNAME"

#: inc/deprecated/deprecated.php:1706
msgid "Watch the video"
msgstr "Guarda il video"

#: inc/deprecated/deprecated.php:1771
msgid "Basic"
msgstr "Base"

#: inc/deprecated/deprecated.php:1772
msgid "Static Files"
msgstr "File statici"

#: inc/deprecated/deprecated.php:1774
msgid "Advanced"
msgstr "Avanzate"

#: inc/deprecated/deprecated.php:1944
#, php-format
msgid "%1$s %2$s requires at least PHP %3$s to function properly. To use this version, please ask your web host how to upgrade your server to PHP %3$s or higher. If you are not able to upgrade, you can rollback to the previous version by using the button below."
msgstr "%1$s%2$s richiede almeno PHP %3$s per funzionare correttamente. Per usare questa versione, chiedi al tuo hosting di aggiornare il tuo server a PHP %3$s o superiore. Se non ti è possibile fare questo aggiornamento, puoi tornare alla versione precedente usando il bottone qui sotto."

#: inc/functions/admin.php:21
msgid "There seems to be an issue validating your license. Please see the error message below."
msgid_plural "There seems to be an issue validating your license. You can see the error messages below."
msgstr[0] "Sembra ci sia un problema con la validazione della tua licenza. Puoi vedere il messaggio di errore qui di seguito:"
msgstr[1] "Sembra ci sia un problema con la validazione della tua licenza. Puoi vedere i messaggi di errore qui di seguito:"

#: inc/functions/admin.php:361
msgid "Server type:"
msgstr "Tipo di server:"

#: inc/functions/admin.php:370
msgid "PHP version number:"
msgstr "Numero di versione PHP:"

#: inc/functions/admin.php:379
msgid "WordPress version number:"
msgstr "Numero di versione WordPress:"

#: inc/functions/admin.php:388
msgid "WordPress multisite:"
msgstr "Multisito WordPress:"

#: inc/functions/admin.php:397
msgid "Current theme:"
msgstr "Tema corrente:"

#: inc/functions/admin.php:406
msgid "Current site language:"
msgstr "Lingua corrente del sito:"

#: inc/functions/admin.php:415
msgid "Active plugins:"
msgstr "Plugin attivi:"

#: inc/functions/admin.php:418
msgid "Plugin names of all active plugins"
msgstr "Nome plugin di tutti i plugin attivi"

#: inc/functions/admin.php:424
msgid "Anonymized WP Rocket settings:"
msgstr "Impostazioni WP Rocket anonime:"

#: inc/functions/admin.php:427
msgid "Which WP Rocket settings are active"
msgstr "Quali impostazioni WP Rocket sono attive"

#: inc/functions/options.php:491
#: inc/functions/options.php:530
msgid "License validation failed. Our server could not resolve the request from your website."
msgstr "La validazione della licenza non è riuscita. Il nostro server non ha potuto risolvere la richiesta proveniente dal tuo sito web."

#: inc/functions/options.php:530
#, php-format
msgid "Try clicking %1$sSave Changes%2$s below. If the error persists, follow %3$sthis guide%4$s."
msgstr "Prova a fare clic su %1$sSalva Modifiche%2$s. Se l'errore persiste, segui %3$squesta guida%4$s."

#: inc/functions/options.php:507
msgid "License validation failed. You may be using a nulled version of the plugin. Please do the following:"
msgstr "La validazione della licenza non è riuscita. È possibile che tu stia usando una versione pirata del plugin. Fai quanto segue:"

#: inc/functions/options.php:507
#: inc/functions/options.php:549
#, php-format
msgid "Login to your WP Rocket %1$saccount%2$s"
msgstr "Connettiti al tuo %1$saccount%2$s WP Rocket"

#: inc/functions/options.php:507
#: inc/functions/options.php:549
msgid "Download the zip file"
msgstr "Scarica il file zip"

#: inc/functions/options.php:507
#: inc/functions/options.php:549
msgid "Reinstall"
msgstr "Reinstallare"

#: inc/functions/options.php:507
#, php-format
msgid "If you do not have a WP Rocket account, please %1$spurchase a license%2$s."
msgstr "Se non hai un account WP Rocket, %1$scompra una licenza%2$s."

#: inc/functions/options.php:515
msgid "License validation failed. This user account does not exist in our database."
msgstr "La convalida della licenza non è riuscita. L'account dell'utente non esiste nel nostro database."

#: inc/functions/options.php:515
msgid "To resolve, please contact support."
msgstr "Per risolvere, contatta il supporto."

#: inc/functions/options.php:523
msgid "License validation failed. This user account is blocked."
msgstr "Convalida della licenza non riuscita. Questo account utente è bloccato."

#: inc/functions/options.php:523
#, php-format
msgid "Please see %1$sthis guide%2$s for more info."
msgstr "Leggi %1$squesta guida%2$s per avere più informazioni."

#: inc/functions/options.php:543
msgid "Your license is not valid."
msgstr "La tua licenza non è valida."

#: inc/functions/options.php:543
#, php-format
msgid "Make sure you have an active %1$sWP Rocket license%2$s."
msgstr "Assicurati di avere un %1$slicenza WP Rocket%2$s attiva."

#: inc/functions/options.php:545
msgid "You have added as many sites as your current license allows."
msgstr "Ha aggiunto il numero massimo di siti permessi dalla tua licenza."

#: inc/functions/options.php:545
#, php-format
msgid "Upgrade your %1$saccount%2$s or %3$stransfer your license%2$s to this domain."
msgstr "Fai un upgrade del tuo %1$saccount%2$s o %3$strasferisci la tua licenza%2$s su questo dominio."

#: inc/functions/options.php:547
msgid "This website is not allowed."
msgstr "Questo sito web non è consentito."

#: inc/functions/options.php:547
#, php-format
msgid "Please %1$scontact support%2$s."
msgstr "Per favore %1$scontatta il supporto%2$s."

#: inc/functions/options.php:549
msgid "This license key is not recognized."
msgstr "La chiave della licenza non è stata riconosciuta."

#: inc/functions/options.php:549
#, php-format
msgid "If the issue persists, please %1$scontact support%2$s."
msgstr "Se il problema persiste, %1$scontatta il supporto%2$s."

#: inc/functions/options.php:555
#, php-format
msgid "License validation failed: %s"
msgstr "La convalida della licenza non è riuscita. %s"

#: inc/vendors/classes/class-imagify-partner.php:531
msgid "Plugin installed successfully."
msgstr "Il plugin è stato installato con successo."

#: inc/vendors/classes/class-imagify-partner.php:532
msgid "Sorry, you are not allowed to install plugins on this site."
msgstr "Non hai i permessi per installare plugin su questo sito."

#: inc/vendors/classes/class-imagify-partner.php:533
msgid "Sorry, you are not allowed to do that."
msgstr "Non hai permessi per farlo."

#: inc/vendors/classes/class-imagify-partner.php:534
msgid "Plugin install failed."
msgstr "L'installazione del plugin non è riuscita."

#: inc/vendors/classes/class-imagify-partner.php:535
msgid "Go back"
msgstr "Torna indietro"

#: views/cpcss/activate-cpcss-mobile.php:14
msgid "Your website currently uses the same Critical Path CSS for both desktop and mobile."
msgstr "Il tuo sito web utilizza al momento lo stesso percorso di CSS critico sia per desktop che per mobile."

#: views/cpcss/activate-cpcss-mobile.php:15
msgid "Click the button to enable mobile-specific CPCSS for your site."
msgstr "Fai clic sul pulsante per attivare sul tuo sito il percorso critico CSS specifico per mobile."

#: views/cpcss/activate-cpcss-mobile.php:20
#: views/settings/enable-google-fonts.php:19
#, php-format
msgid "This is a one-time action and this button will be removed afterwards. %1$sMore info%2$s"
msgstr "Questa è un'azione una tantum per cui questo pulsante verrà poi rimosso. %1$sPiù info%2$s"

#: views/cpcss/activate-cpcss-mobile.php:30
#, php-format
msgid "Your site is now using mobile-specific critical path CSS. %1$sMore info%2$s"
msgstr "Il tuo sito ora sta usando il percorso critico CSS specifico per mobile. %1$sPiù info%2$s"

#: views/cpcss/activate-cpcss-mobile.php:39
msgid "Generate Mobile Specific CPCSS"
msgstr "Genera il percorso critico CSS specifico per mobile."

#: views/cpcss/metabox/container.php:19
msgid "Critical Path CSS"
msgstr "Percorso critico del CSS "

#: views/cpcss/metabox/generate.php:23
#, php-format
msgid "Generate specific Critical Path CSS for this post. %1$sMore info%2$s"
msgstr "Genera il percorso critico CSS specifico per questo articolo. %1$sPiù info%2$s"

#: views/cpcss/metabox/generate.php:33
#, php-format
msgid "This post uses specific Critical Path CSS. %1$sMore info%2$s"
msgstr "Questo articolo usa un percorso critico CSS specifico. %1$sPiù info%2$s"

#: views/cpcss/metabox/generate.php:56
msgid "Revert back to the default CPCSS"
msgstr "Torna al CPCSS predefinito"

#: views/deactivation-intent/form.php:87
msgid "Cancel"
msgstr "Annulla"

#: views/settings/enable-google-fonts.php:13
msgid "Enable Google Font Optimization"
msgstr "Abilita l'ottimizzazione dei font Google"

#: views/settings/enable-google-fonts.php:14
msgid "Improves font performance and combines multiple font requests to reduce the number of HTTP requests."
msgstr "Migliora le prestazioni dei font e combina multiple richieste di font per ridurre il numero di richieste HTTP."

#: views/settings/enable-google-fonts.php:29
#, php-format
msgid "Google Fonts Optimization is now enabled for your site. %1$sMore info%2$s"
msgstr "L'ottimizzazione dei font Google è ora attiva sul tuo sito. %1$sPiù info%2$s"

#: views/settings/enable-google-fonts.php:38
msgid "Optimize Google Fonts"
msgstr "Ottimizza Google Fonts"

#: views/settings/fields/cache-lifespan.php:26
msgid "Clear cache after"
msgstr "Cancella la cache dopo"

#: views/settings/fields/cnames.php:58
#: views/settings/fields/cnames.php:92
#: views/settings/fields/cnames.php:122
msgid "CSS & JavaScript"
msgstr "CSS & JavaScript"

#: views/settings/fields/cnames.php:59
#: views/settings/fields/cnames.php:93
#: views/settings/fields/cnames.php:123
msgid "JavaScript"
msgstr "JavaScript"

#: views/settings/fields/cnames.php:60
#: views/settings/fields/cnames.php:94
#: views/settings/fields/cnames.php:124
msgid "CSS"
msgstr "CSS"

#: views/settings/fields/import-form.php:29
msgid "Import settings"
msgstr "Importa le impostazioni"

#: views/settings/fields/one-click-addon.php:43
#: views/settings/fields/rocket-addon.php:43
msgid "Add-on status"
msgstr "Stato dei componenti aggiuntivi"

#: views/settings/fields/rocket-addon.php:72
msgid "Modify options"
msgstr "Modifica le opzioni"

#: views/settings/fields/rocket-cdn.php:29
#: views/settings/fields/rocket-cdn.php:41
msgid "CDN CNAME"
msgstr "CNAME della CDN"

#: views/settings/fields/rocket-cdn.php:62
#, php-format
msgid "Purges RocketCDN cached resources for your website. %s"
msgstr "Elimina la cache delle risorse su RocketCDN per il tuo sito web. %s"

#: views/settings/fields/rocket-cdn.php:63
#: views/settings/page-sections/cloudflare.php:36
#: views/settings/page-sections/sucuri.php:37
msgid "Learn more"
msgstr "Maggiori informazioni"

#: views/settings/fields/rocket-cdn.php:72
msgid "Clear all RocketCDN cache files"
msgstr "Cancella tutti i file di cache di RocketCDN"

#: views/settings/page-sections/cloudflare.php:27
msgid "Cloudflare Cache"
msgstr "Cache Cloudflare"

#: views/settings/page-sections/cloudflare.php:35
#: views/settings/page-sections/sucuri.php:36
#, php-format
msgid "Purges cached resources for your website. %s"
msgstr "Elimina le risorse del tuo sito memorizzate in cache. %s"

#: views/settings/page-sections/cloudflare.php:36
msgid "https://support.cloudflare.com/hc/en-us/articles/200169246"
msgstr "https://support.cloudflare.com/hc/en-us/articles/200169246"

#: views/settings/page-sections/cloudflare.php:45
msgid "Clear all Cloudflare cache files"
msgstr "Cancella tutti i file della cache Cloudflare"

#: views/settings/page-sections/dashboard.php:38
msgid "Congratulations!"
msgstr "Congratulazioni!"

#: views/settings/page-sections/dashboard.php:40
msgid "WP Rocket is now activated and already working for you."
msgstr "WP Rocket è attivo e pronto a lavorare per te!"

#: views/settings/page-sections/dashboard.php:42
msgid "Your website should be loading faster now!"
msgstr "Il tuo sito web dovrebbe già essere più veloce!"

#: views/settings/page-sections/dashboard.php:44
msgid "We also enable options that provide immediate benefits to your website."
msgstr "Attiviamo anche delle opzioni che garantiscono benefici immediati al tuo sito web."

#: views/settings/page-sections/dashboard.php:45
msgid "Continue to the options to further optimize your site!"
msgstr "Vai alle opzioni per continuare a ottimizzare il tuo sito!"

#: views/settings/page-sections/dashboard.php:62
msgid "My Account"
msgstr "Il mio account"

#: views/settings/page-sections/dashboard.php:68
msgid "Refresh info"
msgstr "Aggiorna info"

#: views/settings/page-sections/dashboard.php:100
msgid "Expiration Date"
msgstr "Data di scadenza"

#: views/settings/page-sections/dashboard.php:110
msgid "View my account"
msgstr "Visualizza il mio account"

#: views/settings/page-sections/dashboard.php:137
msgid "Quick Actions"
msgstr "Azioni rapide"

#: views/settings/page-sections/dashboard.php:144
msgid "Remove all cached files"
msgstr "Rimuovi tutti i file di cache"

#: views/settings/page-sections/dashboard.php:164
#: views/settings/page-sections/dashboard.php:170
msgid "Regenerate Critical CSS"
msgstr "Rigenera il percorso critico del CSS"

#: views/settings/page-sections/dashboard.php:205
msgid "Frequently Asked Questions"
msgstr "Domande più frequenti - FAQ"

#: views/settings/page-sections/dashboard.php:219
msgid "Still cannot find a solution?"
msgstr "Ancora non hai trovato la soluzione?"

#: views/settings/page-sections/dashboard.php:220
msgid "Submit a ticket and get help from our friendly and knowledgeable Rocketeers."
msgstr "Invia un ticket e ottieni aiuto dai nostri amichevoli esperti Rocketeers."

#: views/settings/page-sections/dashboard.php:228
msgid "Ask support"
msgstr "Chiedi al supporto"

#: views/settings/page-sections/database.php:25
msgid "Backup your database before you run a cleanup!"
msgstr "Fai un backup del tuo database prima di realizzare una pulizia!"

#: views/settings/page-sections/database.php:26
msgid "Once a database optimization has been performed, there is no way to undo it."
msgstr "Una volta che l'ottimizzazione del database è stata realizzata, non c'è modo di tornare indietro."

#: views/settings/page-sections/database.php:28
msgid "Save Changes and Optimize"
msgstr "Salve le modifica e ottimizza"

#: views/settings/page-sections/imagify.php:21
#, php-format
msgid "%1$sWP ROCKET%2$s created %3$sIMAGIFY%4$s %1$sfor best-in-class image optimization.%2$s"
msgstr "%1$sWP ROCKET%2$sha creato %3$sIMAGIFY%4$s%1$sper un'ottimizzazione immagini che è la migliore della categoria.%2$s"

#: views/settings/page-sections/imagify.php:24
msgid "Compress image to make your website faster, all while maintaining image quality."
msgstr "Comprimi le tue immagini per rendere il tuo sito web più veloce, mantenendo intatta la loro qualità."

#: views/settings/page-sections/imagify.php:25
msgid "More on Imagify:"
msgstr "Più info su Imagify:"

#: views/settings/page-sections/imagify.php:27
msgid "Imagify Plugin Page"
msgstr "Pagina del plugin Imagify"

#: views/settings/page-sections/imagify.php:28
msgid "Imagify Website"
msgstr "Sito web Imagify"

#: views/settings/page-sections/imagify.php:29
msgid "Review of Image Compression Plugins"
msgstr "Valutazione dei plugin di compressione immagini"

#: views/settings/page-sections/imagify.php:38
msgid "Install Imagify"
msgstr "Installa Imagify"

#: views/settings/page-sections/license.php:22
msgid "WP Rocket was not able to automatically validate your license."
msgstr "WP Rocket non è riuscito a validare automaticamente la tua licenza."

#: views/settings/page-sections/license.php:29
#, php-format
msgid "Follow this %1$s, or contact %2$s to get the engine started."
msgstr "Segui questo %1$s o contatta il %2$s per accendere i motori."

#: views/settings/page-sections/license.php:32
#, php-format
msgid "%1$s%2$s%3$stutorial%4$s"
msgstr "%1$s%2$s%3$stutorial%4$s"

#: views/settings/page-sections/license.php:34
msgid "https://docs.wp-rocket.me/article/100-resolving-problems-with-license-validation/?utm_source=wp_plugin&utm_medium=wp_rocket"
msgstr "https://docs.wp-rocket.me/article/100-resolving-problems-with-license-validation/?utm_source=wp_plugin&utm_medium=wp_rocket"

#: views/settings/page-sections/license.php:40
#, php-format
msgid "%1$s%2$s%3$ssupport%4$s"
msgstr "%1$s%2$s%3$ssupporto%4$s"

#: views/settings/page-sections/sucuri.php:46
msgid "Clear all Sucuri cache files"
msgstr "Cancella tutti i file di cache di Sucuri"

#: views/settings/page-sections/tools.php:20
#, php-format
msgid "Files size: %1$s. Number of entries: %2$s."
msgstr "Dimensioni dei file: %1$s. Numero di voci: %2$s."

#: views/settings/page-sections/tools.php:23
#, php-format
msgid "%1$sDownload the file%2$s."
msgstr "%1$sScarica il file%2$s."

#: views/settings/page-sections/tools.php:26
#, php-format
msgid "%1$sDelete the file%2$s."
msgstr "%1$sCancella il file%2$s."

#: views/settings/page-sections/tools.php:37
msgid "Export settings"
msgstr "Esporta le impostazioni"

#: views/settings/page-sections/tools.php:38
msgid "Download a backup file of your settings"
msgstr "Scarica un file di backup delle tue impostazioni"

#: views/settings/page-sections/tools.php:46
msgid "Download settings"
msgstr "Scarica impostazioni"

#: views/settings/page-sections/tools.php:60
msgid "Rollback"
msgstr "Rollback"

#: views/settings/page-sections/tools.php:64
#, php-format
msgid "Has version %s caused an issue on your website?"
msgstr "La versione %1$s ha creato qualche problema sul tuo sito?"

#: views/settings/page-sections/tools.php:69
#, php-format
msgid "You can rollback to the previous major version here.%sThen send us a support request."
msgstr "Da qui puoi tornare a una precedente versione principale del plugin.%sPoi inviaci una richiesta di supporto."

#: views/settings/page-sections/tools.php:80
#, php-format
msgid "Reinstall version %s"
msgstr "Reinstalla la versione %s"

#: views/settings/page-sections/tools.php:106
msgid "Debug mode"
msgstr "Modalità debug"

#: views/settings/page-sections/tools.php:111
msgid "Create a debug log file."
msgstr "Crea un file registro di debug."

#: views/settings/page-sections/tutorials.php:13
#: views/settings/partials/getting-started.php:18
msgid "Getting Started"
msgstr "Per iniziare"

#: views/settings/page-sections/tutorials.php:15
msgid "Getting Started with WP Rocket"
msgstr "Getting Started with WP Rocket"

#: views/settings/page-sections/tutorials.php:16
#: views/settings/partials/getting-started.php:12
msgid "Finding the Best Settings for Your Site"
msgstr "Trova le migliori impostazioni per il tuo sito"

#: views/settings/page-sections/tutorials.php:17
#: views/settings/partials/getting-started.php:13
msgid "How to Check if WP Rocket is Caching Your Site"
msgstr "Come verificare se WP Rocket sta creando la cache per il tuo sito"

#: views/settings/page-sections/tutorials.php:18
#: views/settings/partials/getting-started.php:14
msgid "How to Measure the Speed of Your Site"
msgstr "Come misurare la velocità del tuo sito"

#: views/settings/page-sections/tutorials.php:33
msgid "Troubleshooting Display Issues with File Optimization"
msgstr "Troubleshooting Display Issues with File Optimization"

#: views/settings/page-sections/tutorials.php:34
msgid "How to Find the Right JavaScript to Exclude"
msgstr "Come trovare il corretto file JavaScript da escludere"

#: views/settings/page-sections/tutorials.php:35
msgid "How External Content Slows Your Site"
msgstr "How External Content Slows Your Site"

#: views/settings/page-sections/tutorials.php:19
msgid "How Preloading Works"
msgstr "Come funziona il precaricamento"

#: views/settings/page-sections/tutorials.php:41
msgid "Set Up the Cloudflare Add-on"
msgstr "Configura l'Add-On Cloudflare"

#: views/settings/page.php:16
msgid "WP Rocket Settings"
msgstr "Impostazioni WP Rocket"

#: views/settings/page.php:30
#, php-format
msgid "version %s"
msgstr "versione %s"

#: views/settings/page.php:60
msgid "Show Sidebar"
msgstr "Mostra la barra laterale"

#: views/settings/page.php:87
msgid "WP Rocket will never transmit any domain names or email addresses (except for license validation), IP addresses, or third-party API keys."
msgstr "WP Rocket non trasmetterà mai alcun nome di dominio o indirizzo email (eccetto che per la validazione della licenza), indirizzi IP o le chiavi API di terze parti."

#: views/settings/page.php:89
msgid "Activate Rocket analytics"
msgstr "Attiva Rocket Analytics"

#: views/settings/partials/documentation.php:15
msgid "It is a great starting point to fix some of the most common issues."
msgstr "È un ottimo punto di partenza per risolvere alcuni degli errori più comuni."

#: views/settings/partials/documentation.php:22
msgid "Read the documentation"
msgstr "Leggi la documentazione"

#: views/settings/partials/getting-started.php:11
msgid "What WP Rocket Does For You By Default"
msgstr "Cosa fa WP Rocket per impostazione predefinita"

#: views/settings/partials/sidebar.php:12
msgid "How to correctly measure your website’s loading time"
msgstr "Come misurare correttamente il tempo di caricamento del tuo sito web"

#: views/settings/partials/sidebar.php:14
msgid "Check our tutorial and learn how to measure the speed of your site."
msgstr "Guarda il nostro tutorial e impara a misurare la velocità del tuo sito."

#: views/settings/partials/sidebar.php:15
#: views/settings/partials/sidebar.php:24
msgid "Read our guide"
msgstr "Leggi la nostra guida"

#: views/settings/partials/sidebar.php:28
msgid "Read more"
msgstr "Leggi di più"

#: views/settings/partials/sidebar.php:33
msgid "You have not activated logged-in user cache."
msgstr "Non hai attivato la cache per gli utenti registrati."

#: views/settings/partials/sidebar.php:34
msgid "Use a private browser to check your website's speed and visual appearance."
msgstr "Usa una finestra privata del browser per controllare la velocità del tuo sito e l’aspetto visuale."

#: inc/Engine/CDN/RocketCDN/views/cta-big.php:27
#, php-format
msgid "Valid until %s only!"
msgstr "Valido solo fino al %s!"

#: inc/Engine/CDN/RocketCDN/views/cta-big.php:36
msgid "Speed up your website thanks to:"
msgstr "Velocizza il tuo sito web grazie a:"

#: inc/Engine/CDN/RocketCDN/views/cta-big.php:48
#, php-format
msgid "Easy configuration: the %1$sbest CDN settings%2$s are automatically applied"
msgstr "Facile configurazione: le %1$smigliori impostazioni per la CDN%2$s sono applicate automaticamente"

#: inc/Engine/CDN/RocketCDN/views/cta-big.php:54
#, php-format
msgid "WP Rocket integration: the CDN option is %1$sautomatically configured%2$s in our plugin"
msgstr "Integrazione con WP Rocket: l'opzione CDN è %1$sconfigurata automaticamente%2$s nel nostro plugin"

#: inc/Engine/CDN/RocketCDN/views/cta-big.php:87
msgid "Get Started"
msgstr "Inizia"

#: inc/Engine/CDN/RocketCDN/views/cta-big.php:58
msgid "Learn more about RocketCDN"
msgstr "Maggiori informazioni su RocketCDN"

#: inc/Engine/CDN/RocketCDN/views/cta-big.php:92
msgid "Reduce this banner"
msgstr "Rimpicciolisci questo banner"

#: inc/Engine/CDN/RocketCDN/views/cta-small.php:17
msgid "Speed up your website with RocketCDN, WP Rocket’s Content Delivery Network."
msgstr "Velocizza il tuo sito web con RocketCDN, la Content Delivery Network di WP Rocket."

#: inc/Engine/CDN/RocketCDN/views/cta-small.php:20
#: inc/Engine/CDN/RocketCDN/views/promote-notice.php:13
msgid "Learn More"
msgstr "Maggiori informazioni"

#: inc/Engine/CDN/RocketCDN/views/dashboard-status.php:23
msgid "RocketCDN is unavailable on local domains and staging sites."
msgstr "RocketCDN non è disponibile su domini locali e siti staging."

#: inc/Engine/CDN/RocketCDN/views/dashboard-status.php:32
msgid "Get RocketCDN"
msgstr "Ottieni RocketCDN"

#: inc/Engine/CDN/RocketCDN/views/promote-notice.php:11
msgid "New!"
msgstr "Novità!"

#: inc/Engine/CDN/RocketCDN/views/promote-notice.php:12
msgid "Speed up your website with RocketCDN, WP Rocket’s Content Delivery Network!"
msgstr "Velocizza il tuo sito web con RocketCDN, la Content Delivery Network di WP Rocket!"

#: views/settings/sections/addons-container.php:24
#: views/settings/sections/fields-container.php:28
msgid "Need Help?"
msgstr "Hai bisogno d’aiuto?"

#: inc/Addon/Cloudflare/API/Client.php:129
msgid "Cloudflare did not provide any reply. Please try again later."
msgstr ""

#: inc/Addon/Cloudflare/Cloudflare.php:205
msgid "days"
msgstr ""

#: inc/Addon/Cloudflare/Cloudflare.php:208
msgid "seconds"
msgstr ""

#: inc/Addon/Cloudflare/Cloudflare.php:210
msgid "minutes"
msgstr ""

#: inc/Addon/Cloudflare/Cloudflare.php:212
msgid "hours"
msgstr ""

#: inc/Addon/Cloudflare/Subscriber.php:224
#: inc/Addon/Cloudflare/Subscriber.php:253
#, php-format
msgid "%1$sWP Rocket:%2$s %3$s"
msgstr ""

#: inc/Addon/Cloudflare/Subscriber.php:242
#, php-format
msgid "%1$sWP Rocket:%2$s Cloudflare cache successfully purged."
msgstr ""

#: inc/Addon/Cloudflare/Subscriber.php:328
msgctxt "Cloudflare caching level"
msgid "standard"
msgstr ""

#: inc/Addon/Cloudflare/Subscriber.php:403
#, php-format
msgid "Cloudflare browser cache set to %s"
msgstr ""

#: inc/Addon/Cloudflare/Subscriber.php:512
#, php-format
msgid "%1$sWP Rocket:%2$s Optimal settings activated for Cloudflare:"
msgstr ""

#: inc/Addon/Cloudflare/Subscriber.php:521
#, php-format
msgid "%1$sWP Rocket:%2$s Optimal settings deactivated for Cloudflare, reverted to previous settings:"
msgstr ""

#: inc/Addon/WebP/AdminSubscriber.php:93
#, php-format
msgid "You are using %1$s to serve WebP images so you do not need to enable this option. %2$sMore info%3$s %4$s If you prefer to have WP Rocket serve WebP for you instead, please disable WebP display in %1$s."
msgid_plural "You are using %1$s to serve WebP images so you do not need to enable this option. %2$sMore info%3$s %4$s If you prefer to have WP Rocket serve WebP for you instead, please disable WebP display in %1$s."
msgstr[0] ""
msgstr[1] ""

#: inc/Addon/WebP/AdminSubscriber.php:173
#, php-format
msgid "%5$sWe have not detected any compatible WebP plugin!%6$s%4$s If you don’t already have WebP images on your site consider using %3$sImagify%2$s or another supported plugin. %1$sMore info%2$s %4$s If you are not using WebP do not enable this option."
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:642
msgid "If you have problems after activating this option, copy and paste the default exclusions to quickly resolve issues:"
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:646
#, php-format
msgid "Also, please check our %1$sdocumentation%2$s for a list of compatibility exclusions."
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:651
msgid "Internal scripts are excluded by default to prevent issues. Remove them to take full advantage of this option."
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:654
#, php-format
msgid "If this causes trouble, restore the default exclusions, found %1$shere%2$s"
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:687
msgid "For compatibility and best results, this option is disabled when Remove unused CSS is enabled."
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:730
msgid "Optimize CSS delivery eliminates render-blocking CSS on your website. Only one method can be selected. Remove Unused CSS is recommended for optimal performance, but limited only to the users with active license."
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:730
msgid "Optimize CSS delivery eliminates render-blocking CSS on your website. Only one method can be selected. Remove Unused CSS is recommended for optimal performance."
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:740
#, php-format
msgid "Optimize CSS Delivery features are disabled on local environments. %1$sLearn more%2$s"
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:760
#: inc/admin/ui/meta-boxes.php:106
msgid "Remove Unused CSS"
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:763
#, php-format
msgid "Removes unused CSS per page and helps to reduce page size and HTTP requests. Recommended for best performance. Test thoroughly! %1$sMore info%2$s"
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:767
msgid "Activate Remove Unused CSS"
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:773
msgid "CSS safelist"
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:774
msgid "Specify CSS filenames, IDs or classes that should not be removed (one per line)."
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:789
#: inc/admin/ui/meta-boxes.php:109
msgid "Load CSS asynchronously"
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:792
#, php-format
msgctxt "WP Critical CSS compatibility"
msgid "Load CSS asynchronously is currently handled by the %1$s plugin. If you want to use WP Rocket’s load CSS asynchronously option, disable the %1$s plugin."
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:794
#, php-format
msgid "Generates critical path CSS and loads CSS asynchronously. %1$sMore info%2$s"
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:841
msgid "For compatibility and best results, this option is disabled when delay javascript execution is enabled."
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:915
#, php-format
msgid "Specify URLs or keywords of JavaScript files to be excluded from defer (one per line). %1$sMore info%2$s"
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:942
msgid "One-click exclusions"
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:943
msgid "When using the Delay JavaScript Execution, you might experience delay loading elements located in the viewport that need to appear immediately - e.g. slider, header, menu."
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:944
msgid "If you need instant visibility, click below on files that should NOT be delayed. This selection will help users interact with the elements straight away."
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:961
msgid "Specify URLs or keywords that can identify inline or JavaScript files to be excluded from delaying execution (one per line)."
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:994
msgid "LazyLoad, image dimensions"
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:1061
msgid "Image Dimensions"
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:1064
#, php-format
msgid "Add missing width and height attributes to images. Helps prevent layout shifts and improve the reading experience for your visitors. %1$sMore info%2$s"
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:1135
msgid "Excluded images or iframes"
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:1137
#, php-format
msgid "Specify keywords (e.g. image filename, CSS class, domain) from the image or iframe code to be excluded (one per line). %1$sMore info%2$s"
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:1145
msgid "Add missing image dimensions"
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:1180
msgid "When you enable preloading WP Rocket will automatically detect your sitemaps and save all URLs to the database. The plugin will make sure that your cache is always preloaded."
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:1234
msgid "Exclude URLs"
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:1239
#, php-format
msgid "Specify URLs to be excluded from the preload feature (one per line). %1$sMore info%2$s"
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:1379
msgid "Specify full or partial IDs of cookies that, when set in the visitor's browser, should prevent a page from getting cached (one per line)"
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:1464
msgid "Automatic Cleanup"
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:1642
msgid "%1$s%2$l Add-on%3$s is currently enabled. Configuration of the CDN settings is not required for %2$l to work on your site."
msgid_plural "%1$s%2$l Add-ons%3$s are currently enabled. Configuration of the CDN settings is not required for %2$l to work on your site."
msgstr[0] ""
msgstr[1] ""

#: inc/Engine/Admin/Settings/Page.php:1827
#, php-format
msgid "%1$sPlanning on using Automatic Platform Optimization (APO)?%2$s Just activate the official Cloudflare plugin and configure it. WP Rocket will automatically enable compatibility."
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:1918
msgid "WebP Compatibility"
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:1924
msgid "Improve browser compatibility for WebP images."
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:2129
msgctxt "Sucuri"
msgid "Firewall API key (for plugin), must be in format {32 characters}/{32 characters}:"
msgstr ""

#: inc/Engine/Admin/Settings/Settings.php:668
msgid "Sorry! Adding /(.*) in Advanced Rules > Never Cache URL(s) was not saved because it disables caching and optimizations for every page on your site."
msgstr ""

#: inc/Engine/CDN/RocketCDN/APIClient.php:134
msgid "We could not fetch the current price because RocketCDN API returned an unexpected error code."
msgstr ""

#: inc/Engine/CDN/RocketCDN/APIClient.php:140
msgid "RocketCDN is not available at the moment. Please retry later."
msgstr ""

#: inc/Engine/CDN/RocketCDN/views/cta-big.php:42
#, php-format
msgid "High performance Content Delivery Network (CDN) with %1$sunlimited bandwidth%2$s"
msgstr ""

#: inc/Engine/CDN/RocketCDN/views/cta-big.php:65
#, php-format
msgid "*$%1$s/month for 12 months then $%2$s/month. You can cancel your subscription at any time."
msgstr ""

#: inc/Engine/CDN/RocketCDN/views/cta-big.php:86
msgid "Billed monthly"
msgstr ""

#: inc/Engine/Common/Queue/RUCSSQueueRunner.php:253
msgid "Every minute"
msgstr ""

#: inc/Engine/CriticalPath/Admin/Post.php:216
msgid "This feature is not available for non-public post types."
msgstr ""

#: inc/Engine/CriticalPath/Admin/Post.php:223
msgid "Enable Load CSS asynchronously in WP Rocket settings"
msgstr ""

#: inc/Engine/CriticalPath/Admin/Post.php:224
msgid "Enable Load CSS asynchronously in the options above"
msgstr ""

#: inc/Engine/CriticalPath/CriticalCSSSubscriber.php:840
#, php-format
msgid "We highly recommend the %1$supdated Remove Unused CSS%2$s for a better CSS optimization. Load CSS Asynchronously is always available as a back-up."
msgstr ""

#: inc/Engine/CriticalPath/CriticalCSSSubscriber.php:845
msgid "Stay with the old option"
msgstr ""

#: inc/Engine/CriticalPath/DataManager.php:68
#, php-format
msgid "Critical CSS for %1$s on mobile not generated. Error: The destination folder could not be created."
msgstr ""

#: inc/Engine/CriticalPath/DataManager.php:71
#, php-format
msgid "Critical CSS for %1$s not generated. Error: The destination folder could not be created."
msgstr ""

#: inc/Engine/License/Renewal.php:76
#, php-format
msgid "Renew before it is too late, you will only pay %1$s%2$s%3$s!"
msgstr ""

#: inc/Engine/License/Renewal.php:85
#, php-format
msgid "Renew with a %1$s%2$s discount%3$s before it is too late, you will only pay %4$s%5$s%6$s!"
msgstr ""

#: inc/Engine/License/Renewal.php:139
#, php-format
msgid "Renew your license for 1 year now at %1$s%3$s%2$s."
msgstr ""

#: inc/Engine/License/Renewal.php:152
#, php-format
msgid "Renew your license for 1 year now and get %1$s%3$s OFF%2$s immediately: you will only pay %1$s%4$s%2$s!"
msgstr ""

#: inc/Engine/License/Renewal.php:218
#, php-format
msgid "Renew before it is too late, you will pay %1$s%3$s%2$s."
msgstr ""

#: inc/Engine/License/Renewal.php:227
#, php-format
msgid "Renew with a %1$s%2$s discount%3$s before it is too late, you will only pay %1$s%4$s%3$s!"
msgstr ""

#: inc/Engine/License/Renewal.php:546
#, php-format
msgid "You need a valid license to continue using this feature. %1$sRenew now%2$s before losing access."
msgstr ""

#: inc/Engine/License/Renewal.php:567
#, php-format
msgid "You need an active license to enable this option. %1$sRenew now%2$s."
msgstr ""

#: inc/Engine/License/Renewal.php:595
#, php-format
msgid "You need an active license to enable this option. %1$sMore info%2$s."
msgstr ""

#: inc/Engine/License/Upgrade.php:252
#, php-format
msgid "Take advantage of %1$s to speed up more websites:%2$s get a %3$s%4$s off%5$s for %3$supgrading your license to Plus or Infinite!%5$s"
msgid_plural "Take advantage of %1$s to speed up more websites:%2$s get a %3$s%4$s off%5$s for %3$supgrading your license to Infinite!%5$s"
msgstr[0] ""
msgstr[1] ""

#: inc/Engine/License/Upgrade.php:382
#: inc/Engine/License/Upgrade.php:405
msgid "Unlimited"
msgstr ""

#: inc/Engine/License/views/promo-banner.php:16
#, php-format
msgid "%s off"
msgstr ""

#: inc/Engine/License/views/promo-banner.php:21
#, php-format
msgid "%s promotion is live!"
msgstr ""

#: inc/Engine/License/views/promo-banner.php:27
msgid "Hurry Up! Deal ends in:"
msgstr ""

#: inc/Engine/License/views/promo-banner.php:32
#: inc/Engine/License/views/renewal-soon-banner.php:15
msgid "Seconds"
msgstr ""

#: inc/Engine/License/views/promo-banner.php:34
#: inc/Engine/License/views/upgrade-section.php:11
msgid "Upgrade now"
msgstr ""

#: inc/Engine/License/views/promo-banner.php:36
#: inc/Engine/License/views/renewal-expired-banner-ocd-disabled.php:32
#: inc/Engine/License/views/renewal-expired-banner-ocd.php:42
#: inc/Engine/License/views/renewal-expired-banner.php:30
#: inc/admin/ui/notices.php:739
#: views/settings/page-sections/dashboard.php:46
msgid "Dismiss this notice"
msgstr ""

#: inc/Engine/License/views/renewal-expired-banner-ocd-disabled.php:11
msgid "The Optimize CSS Delivery feature is disabled."
msgstr ""

#: inc/Engine/License/views/renewal-expired-banner-ocd-disabled.php:15
msgid "You can no longer use the Remove Unused CSS or Load CSS asynchronously options."
msgstr ""

#: inc/Engine/License/views/renewal-expired-banner-ocd-disabled.php:20
#, php-format
msgid "You need an %1$sactive license%2$s to keep optimizing your CSS delivery, which addresses a PageSpeed Insights recommendation and improves your page performance."
msgstr ""

#: inc/Engine/License/views/renewal-expired-banner-ocd-disabled.php:29
#: inc/Engine/License/views/renewal-expired-banner-ocd.php:39
#: inc/Engine/License/views/renewal-expired-banner.php:27
#: inc/Engine/License/views/renewal-soon-banner.php:31
msgid "Renew now"
msgstr ""

#: inc/Engine/License/views/renewal-expired-banner-ocd.php:11
msgid "You will soon lose access to some features"
msgstr ""

#: inc/Engine/License/views/renewal-expired-banner-ocd.php:18
#, php-format
msgid "You need an %1$sactive license to continue optimizing your CSS delivery%2$s."
msgstr ""

#: inc/Engine/License/views/renewal-expired-banner-ocd.php:24
msgid "The Remove Unused CSS and Load CSS asynchronously features are great options to address the PageSpeed Insights recommendations and improve your website performance."
msgstr ""

#: inc/Engine/License/views/renewal-expired-banner-ocd.php:29
#, php-format
msgid "They will be %1$sautomatically disabled on %3$s%2$s."
msgstr ""

#: inc/Engine/License/views/renewal-expired-banner.php:11
msgid "Your WP Rocket license is expired!"
msgstr ""

#: inc/Engine/License/views/renewal-expired-banner.php:18
#, php-format
msgid "Your website could be much faster if it could take advantage of our %1$snew features and enhancements%2$s. 🚀"
msgstr ""

#: inc/Engine/License/views/renewal-soon-banner.php:22
#, php-format
msgid "Your %1$sWP Rocket license is about to expire%2$s: you will soon lose access to product updates and support."
msgstr ""

#: inc/Engine/License/views/upgrade-popin.php:12
msgid "Speed Up More Websites"
msgstr ""

#: inc/Engine/License/views/upgrade-popin.php:19
#, php-format
msgid "You can use WP Rocket on more websites by upgrading your license. To upgrade, simply pay the %1$sprice difference%2$s between your current and new licenses, as shown below."
msgstr ""

#: inc/Engine/License/views/upgrade-popin.php:25
#, php-format
msgid "%1$sN.B.%2$s: Upgrading your license does not change your expiration date"
msgstr ""

#: inc/Engine/License/views/upgrade-popin.php:35
#, php-format
msgid "Save $%s"
msgstr ""

#: inc/Engine/License/views/upgrade-popin.php:48
#, php-format
msgid "%s websites"
msgstr ""

#: inc/Engine/License/views/upgrade-popin.php:54
#, php-format
msgid "Upgrade to %s"
msgstr ""

#: inc/Engine/License/views/upgrade-section.php:11
msgid "You can use WP Rocket on more websites by upgrading your license (you will only pay the price difference between your current and new licenses)."
msgstr ""

#: inc/Engine/Optimization/DelayJS/Admin/SiteList.php:195
msgid "Analytics & Ads"
msgstr ""

#: inc/Engine/Optimization/DelayJS/Admin/SiteList.php:200
msgid "Plugins"
msgstr ""

#: inc/Engine/Optimization/DelayJS/Admin/SiteList.php:205
msgid "Themes"
msgstr ""

#: inc/Engine/Optimization/DynamicLists/DynamicLists.php:101
msgid "You need an active license to get the latest version of the lists from our server."
msgstr ""

#: inc/Engine/Optimization/DynamicLists/DynamicLists.php:115
msgid "Could not get updated lists from server."
msgstr ""

#: inc/Engine/Optimization/DynamicLists/DynamicLists.php:124
msgid "Lists are up to date."
msgstr ""

#: inc/Engine/Optimization/DynamicLists/DynamicLists.php:133
msgid "Could not update lists."
msgstr ""

#: inc/Engine/Optimization/DynamicLists/DynamicLists.php:142
msgid "Lists are successfully updated."
msgstr ""

#: inc/Engine/Optimization/DynamicLists/ServiceProvider.php:52
msgid "Default Lists"
msgstr ""

#: inc/Engine/Optimization/DynamicLists/ServiceProvider.php:58
msgid "Delay JavaScript Execution Exclusion Lists"
msgstr ""

#: inc/Engine/Optimization/RUCSS/Admin/Settings.php:131
#: views/settings/page-sections/dashboard.php:188
msgid "Clear Used CSS"
msgstr ""

#: inc/Engine/Optimization/RUCSS/Admin/Settings.php:219
#, php-format
msgid "%1$s: Please wait %2$s seconds. The Remove Unused CSS service is processing your pages."
msgstr ""

#: inc/Engine/Optimization/RUCSS/Admin/Settings.php:264
#, php-format
msgid "%1$s: The Used CSS of your homepage has been processed. WP Rocket will continue to generate Used CSS for up to %2$s URLs per %3$s second(s)."
msgstr ""

#: inc/Engine/Optimization/RUCSS/Admin/Settings.php:273
#, php-format
msgid "We suggest enabling %1$sPreload%2$s for the fastest results."
msgstr ""

#: inc/Engine/Optimization/RUCSS/Admin/Settings.php:283
#, php-format
msgid "To learn more about the process check our %1$sdocumentation%2$s."
msgstr ""

#: inc/Engine/Optimization/RUCSS/Admin/Settings.php:481
msgid "We couldn't generate the used CSS because you're using a nulled version of WP Rocket. You need an active license to use the Remove Unused CSS feature and further improve your website's performance."
msgstr ""

#: inc/Engine/Optimization/RUCSS/Admin/Settings.php:484
#, php-format
msgid "Click here to get a WP Rocket single license at %1$s off!"
msgstr ""

#: inc/Engine/Optimization/RUCSS/Admin/Settings.php:521
#, php-format
msgid "Could not create the %2$s table in the database which is necessary for the Remove Unused CSS feature to work. Please reach out to <a href=\"%3$s\">our support</a>."
msgstr ""

#: inc/Engine/Optimization/RUCSS/Admin/Subscriber.php:322
#, php-format
msgid "%1$s: Used CSS option is not enabled!"
msgstr ""

#: inc/Engine/Optimization/RUCSS/Admin/Subscriber.php:343
#, php-format
msgid "%1$s: Used CSS cache cleared!"
msgstr ""

#: inc/Engine/Optimization/RUCSS/Controller/UsedCSS.php:776
msgid "Clear Used CSS of this URL"
msgstr ""

#: inc/Engine/Optimization/RUCSS/Cron/Subscriber.php:160
msgid "WP Rocket Remove Unused CSS pending jobs"
msgstr ""

#: inc/Engine/Optimization/RUCSS/Cron/Subscriber.php:174
msgid "WP Rocket clear Remove Unused CSS failed jobs"
msgstr ""

#: inc/Engine/Preload/Admin/Settings.php:57
#, php-format
msgid "%1$s: The preload service is now active. After the initial preload it will continue to cache all your pages whenever they are purged. No further action is needed."
msgstr ""

#: inc/Engine/Preload/Cron/Subscriber.php:138
msgid "WP Rocket Preload pending jobs"
msgstr ""

#: inc/Engine/Preload/Cron/Subscriber.php:166
msgid "WP Rocket Preload revert stuck failed jobs"
msgstr ""

#: inc/ThirdParty/Hostings/Kinsta.php:158
#, php-format
msgid "Your installation seems to be missing core Kinsta files managing Cache clearing, which will prevent your Kinsta installation and WP Rocket from working correctly. Please get in touch with Kinsta support through your %1$sMyKinsta%2$s account to resolve this issue."
msgstr ""

#: inc/ThirdParty/Plugins/CDN/Cloudflare.php:114
#, php-format
msgid "%1$s: Cloudflare's HTTP/2 Server Push is incompatible with the features of Remove Unused CSS and Combine CSS files. We strongly recommend disabling it."
msgstr ""

#: inc/ThirdParty/Plugins/CDN/Cloudflare.php:157
msgid "Your site is using the official Cloudflare plugin. We have enabled Cloudflare auto-purge for compatibility. If you have APO activated, it is also compatible."
msgstr ""

#: inc/ThirdParty/Plugins/CDN/Cloudflare.php:158
msgid "Cloudflare cache will be purged each time WP Rocket clears its cache to ensure content is always up-to-date."
msgstr ""

#: inc/ThirdParty/Plugins/CDN/Cloudflare.php:202
#, php-format
msgid "%1$sWP Rocket:%2$s You are using \"Dynamic Cookies Cache\". Cloudflare APO is not yet compatible with that feature."
msgstr ""

#: inc/ThirdParty/Plugins/CDN/Cloudflare.php:208
#, php-format
msgid "You should either disable Cloudflare APO or check with the theme/plugin requiring the use of “Dynamic Cookies Cache” developers for an alternative way to be page-cache friendly. %1$sMore info%2$s"
msgstr ""

#: inc/ThirdParty/Plugins/CDN/Cloudflare.php:259
#, php-format
msgid "%1$sWP Rocket:%2$s You are using \"Separate cache files for mobile devices\". You need to activate \"Cache by Device Type\" %3$ssetting%5$s on Cloudflare APO to serve the right version of the cache. %4$sMore info%5$s"
msgstr ""

#: inc/ThirdParty/Plugins/CDN/Cloudflare.php:280
#, php-format
msgid "%1$sWP Rocket:%2$s You have \"Cache by Device Type\" enabled on Cloudflare APO. If you judge it necessary for the website to have a different cache on mobile and desktop, we suggest you enable our “Separate Cache Files for Mobiles Devices” to ensure the generated cache is accurate."
msgstr ""

#: inc/ThirdParty/Plugins/ModPagespeed.php:102
#, php-format
msgid "<strong>%1$s</strong>: Mod PageSpeed is not compatible with this plugin and may cause unexpected results. %2$sMore Info%3$s"
msgstr ""

#: inc/ThirdParty/Plugins/Optimization/Autoptimize.php:76
#, php-format
msgid "%1$sWP Rocket: %2$sWe have detected that Autoptimize's JavaScript Aggregation feature is enabled. WP Rocket's Delay JavaScript Execution will not be applied to the file it creates. We suggest disabling %1$sJavaScript Aggregation%2$s to take full advantage of Delay JavaScript Execution."
msgstr ""

#: inc/ThirdParty/Plugins/Optimization/Autoptimize.php:131
#, php-format
msgid "%1$sWP Rocket: %2$sWe have detected that Autoptimize's Aggregate Inline CSS feature is enabled. WP Rocket's Load CSS Asynchronously will not work correctly. We suggest disabling %1$sAggregate Inline CSS%2$s to take full advantage of Load CSS Asynchronously Execution."
msgstr ""

#: inc/ThirdParty/Plugins/Optimization/Ezoic.php:45
#, php-format
msgid "This plugin blocks WP Rocket's caching and optimizations. Deactivate it and use %1$sEzoic's nameserver integration%2$s instead."
msgstr ""

#: inc/ThirdParty/Plugins/Optimization/Perfmatters.php:38
msgid "Remove Unused CSS is currently activated in Perfmatters. If you want to use WP Rocket's Remove Unused CSS feature, disable this option in Perfmatters."
msgstr ""

#: inc/ThirdParty/Plugins/Optimization/RapidLoad.php:39
msgid "Automated unused CSS removal is currently activated in RapidLoad Power-Up for Autoptimize. If you want to use WP Rocket's Remove Unused CSS feature, disable the  RapidLoad Power-Up for Autoptimize plugin."
msgstr ""

#: inc/ThirdParty/Plugins/Optimization/WPMeteor.php:42
#, php-format
msgid "Delay JS is currently activated in %1$s. If you want to use WP Rocket’s delay JS, disable %1$s"
msgstr ""

#: inc/admin/options.php:127
msgid "Defer JavaScript Files"
msgstr ""

#: inc/admin/options.php:128
msgid "Excluded Delay JavaScript Files"
msgstr ""

#: inc/admin/options.php:150
#, php-format
msgid "%1$s: <em>%2$s</em>"
msgstr ""

#: inc/admin/options.php:176
msgid "More info"
msgstr ""

#: inc/admin/ui/notices.php:757
msgid "Turn on Remove Unused CSS"
msgstr ""

#: inc/admin/ui/notices.php:763
msgid "Enable “Separate Cache Files for Mobile Devices” now"
msgstr ""

#: inc/common/admin-bar.php:84
#: inc/common/admin-bar.php:136
#: views/settings/page-sections/dashboard.php:150
msgid "Clear and preload cache"
msgstr ""

#: inc/functions/options.php:429
msgid "The provided license data are not valid."
msgstr ""

#: inc/functions/options.php:432
#, php-format
msgid "To resolve, please %1$scontact support%2$s."
msgstr ""

#: inc/functions/options.php:491
#, php-format
msgid "Try clicking %1$sValidate License%2$s below. If the error persists, follow %3$sthis guide%4$s."
msgstr ""

#: views/cpcss/activate-cpcss-mobile.php:13
msgid "Load CSS asynchronously for mobile"
msgstr ""

#: views/deactivation-intent/form.php:21
msgid "Facing an issue?"
msgstr ""

#: views/deactivation-intent/form.php:24
msgid "It is not always necessary to deactivate WP Rocket when facing any issues. Most of them can be fixed by deactivating only some options."
msgstr ""

#: views/deactivation-intent/form.php:29
#, php-format
msgid "Our advice? Instead of deactivating WP Rocket, use our %1$sSafe Mode%2$s to quickly disable LazyLoad, File Optimization, and CDN options. Then check to see if your issue is resolved."
msgstr ""

#: views/deactivation-intent/form.php:35
msgid "Do you want to use our Safe Mode to troubleshoot WP Rocket?"
msgstr ""

#: views/deactivation-intent/form.php:55
#, php-format
msgid "Yes, apply \"%1$sSafe Mode%2$s\""
msgstr ""

#: views/deactivation-intent/form.php:68
#, php-format
msgid "and export WP Rocket settings %1$s(Recommended as current settings will be deleted)%2$s"
msgstr ""

#: views/deactivation-intent/form.php:76
msgid "No, deactivate and snooze this message for"
msgstr ""

#: views/deactivation-intent/form.php:78
msgid "1 day"
msgstr ""

#: views/deactivation-intent/form.php:79
msgid "7 days"
msgstr ""

#: views/deactivation-intent/form.php:80
msgid "30 days"
msgstr ""

#: views/deactivation-intent/form.php:81
msgid "Forever"
msgstr ""

#: views/deactivation-intent/form.php:88
msgid "Confirm"
msgstr ""

#: views/settings/dynamic-lists-update.php:14
msgid "Update Inclusion and Exclusion Lists"
msgstr ""

#: views/settings/dynamic-lists-update.php:19
#, php-format
msgid "Compatibility lists are downloaded automatically every week. Click the button if you want to update them manually. %1$sMore info%2$s"
msgstr ""

#: views/settings/dynamic-lists-update.php:29
msgid "Update lists"
msgstr ""

#: views/settings/page-sections/dashboard.php:44
#, php-format
msgid "To guarantee fast websites, WP Rocket automatically applies 80% of web performance best practices."
msgstr ""

#: views/settings/page-sections/dashboard.php:86
msgid "with"
msgstr ""

#: views/settings/page-sections/dashboard.php:182
msgid "Remove Used CSS Cache"
msgstr ""

#: views/settings/page-sections/tutorials.php:23
msgid "Passing the Core Web vitals"
msgstr ""

#: views/settings/page-sections/tutorials.php:25
msgid "How to improve LCP with WP Rocket"
msgstr ""

#: views/settings/page-sections/tutorials.php:26
msgid "How to improve FID with WP Rocket"
msgstr ""

#: views/settings/page-sections/tutorials.php:27
msgid "How to improve CLS with WP Rocket"
msgstr ""

#: views/settings/page-sections/tutorials.php:31
msgid "Troubleshooting"
msgstr ""

#: views/settings/page.php:82
#, php-format
msgid "Below is a detailed view of all data WP Rocket will collect %1$sif granted permission.%2$s"
msgstr ""

#: views/settings/partials/sidebar.php:18
msgid "Learn about optimal WP Rocket settings for mobile."
msgstr ""

#: views/settings/partials/sidebar.php:27
msgid "Test and Improve Google Core Web Vitals for WordPress."
msgstr ""
