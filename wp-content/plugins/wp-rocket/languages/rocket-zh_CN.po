# Copyright (C) 2023 WP Media
# This file is distributed under the same license as the WP Rocket plugin.
# Translators:
# <AUTHOR> <EMAIL>, 2019
# <PERSON><PERSON><PERSON>, 2021
# <AUTHOR> <EMAIL>, 2021
# <PERSON> <<EMAIL>>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: WP Rocket 3.14.4\n"
"Report-Msgid-Bugs-To: http://wp-rocket.me/\n"
"POT-Creation-Date: 2023-08-07 15:45-0400\n"
"PO-Revision-Date: 2019-08-26 15:14+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2023\n"
"Language-Team: Chinese (China) (https://app.transifex.com/wp-media/teams/18133/zh_CN/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: zh_CN\n"
"Plural-Forms: nplurals=1; plural=0;\n"
"X-Domain: rocket\n"
"X-Generator: WP-CLI 2.7.1\n"

#. Translators: %s = Hosting name.
#: inc/3rd-party/hosting/flywheel.php:18
#: inc/3rd-party/hosting/pressidium.php:18
#: inc/3rd-party/hosting/wp-serveur.php:27 inc/deprecated/3.6.php:697
#: inc/deprecated/3.6.php:997 inc/deprecated/3.9.php:22
#: inc/ThirdParty/Hostings/Cloudways.php:90
#: inc/ThirdParty/Hostings/Dreampress.php:44
#: inc/ThirdParty/Hostings/Godaddy.php:63
#: inc/ThirdParty/Hostings/O2Switch.php:49
#: inc/ThirdParty/Hostings/OneCom.php:137
#: inc/ThirdParty/Hostings/ProIsp.php:51 inc/ThirdParty/Hostings/Savvii.php:50
#: inc/ThirdParty/Hostings/WPEngine.php:47
#: inc/ThirdParty/Hostings/WPXCloud.php:51
msgid ""
"Your site is hosted on %s, we have enabled Varnish auto-purge for "
"compatibility."
msgstr "您的网站托管在 %s。为确保兼容性, 我们已为您启用了Varnish自动缓存刷新功能。"

#: inc/Addon/Cloudflare/API/Client.php:129
msgid "Cloudflare did not provide any reply. Please try again later."
msgstr "Cloudflare 未响应，请稍后重试。"

#: inc/Addon/Cloudflare/API/Client.php:135
msgid "Cloudflare unexpected response"
msgstr "Cloudflare 出现未知错误"

#: inc/Addon/Cloudflare/API/Client.php:143
msgid "Missing Cloudflare result."
msgstr "Cloudflare 未返回结果"

#: inc/Addon/Cloudflare/API/Client.php:194 inc/deprecated/3.5.php:112
#: inc/deprecated/3.5.php:169
msgid "Incorrect Cloudflare email address or API key."
msgstr "Cloudflare 邮箱 或 API key 不正确哦。"

#. translators: %1$s = opening link; %2$s = closing link
#: inc/Addon/Cloudflare/API/Client.php:198
#: inc/Addon/Cloudflare/API/Client.php:212
#: inc/Addon/Cloudflare/Cloudflare.php:75
#: inc/Addon/Cloudflare/Cloudflare.php:108
#: inc/classes/subscriber/Tools/class-detect-missing-tags-subscriber.php:157
#: inc/deprecated/3.5.php:92 inc/deprecated/3.5.php:116
#: inc/deprecated/3.5.php:129 inc/deprecated/3.5.php:157
#: inc/deprecated/3.5.php:173
msgid "Read the %1$sdocumentation%2$s for further guidance."
msgstr "查阅 %1$s 文档 %2$s 获取帮助。"

#. translators: Documentation exists in EN, FR; use localized URL if
#. applicable.
#: inc/Addon/Cloudflare/API/Client.php:200
#: inc/Addon/Cloudflare/API/Client.php:214
#: inc/Addon/Cloudflare/Auth/APIKey.php:63
#: inc/Addon/Cloudflare/Cloudflare.php:77
#: inc/Addon/Cloudflare/Cloudflare.php:110 inc/deprecated/3.5.php:81
#: inc/deprecated/3.5.php:94 inc/deprecated/3.5.php:118
#: inc/deprecated/3.5.php:131 inc/deprecated/3.5.php:159
#: inc/deprecated/3.5.php:175 inc/deprecated/3.5.php:208
msgid ""
"https://docs.wp-rocket.me/article/18-using-wp-rocket-with-"
"cloudflare/?utm_source=wp_plugin&utm_medium=wp_rocket#add-on"
msgstr ""
"https://docs.wp-rocket.me/article/18-using-wp-rocket-with-"
"cloudflare/?utm_source=wp_plugin&utm_medium=wp_rocket#add-on"

#: inc/Addon/Cloudflare/API/Client.php:208 inc/deprecated/3.5.php:125
msgid "Incorrect Cloudflare Zone ID."
msgstr "Cloudflare Zone ID 不正确。"

#. translators: %1$s = opening link; %2$s = closing link
#: inc/Addon/Cloudflare/Auth/APIKey.php:61
msgid ""
"Cloudflare email and/or API key are not set. Read the %1$sdocumentation%2$s "
"for further guidance."
msgstr "Cloudflare 邮箱 和/或 API Key 未设置。更多信息请查阅%1$s文档%2$s。"

#: inc/Addon/Cloudflare/Cloudflare.php:71 inc/deprecated/3.5.php:88
msgid "Missing Cloudflare Zone ID."
msgstr "Cloudflare Zone ID 缺失。"

#: inc/Addon/Cloudflare/Cloudflare.php:104 inc/deprecated/3.5.php:153
msgid "It looks like your domain is not set up on Cloudflare."
msgstr "貌似Cloudflare里您的域名未正确设置。"

#: inc/Addon/Cloudflare/Cloudflare.php:200
msgid "days"
msgstr "天"

#: inc/Addon/Cloudflare/Cloudflare.php:203
msgid "seconds"
msgstr "秒"

#: inc/Addon/Cloudflare/Cloudflare.php:205
msgid "minutes"
msgstr "分钟"

#: inc/Addon/Cloudflare/Cloudflare.php:207
msgid "hours"
msgstr "小时"

#. translators: %1$s = <strong>, %2$s = </strong>, %3$s = CloudFare API return
#. message.
#: inc/Addon/Cloudflare/Subscriber.php:232
#: inc/Addon/Cloudflare/Subscriber.php:261
msgid "%1$sWP Rocket:%2$s %3$s"
msgstr "%1$sWP Rocket:%2$s %3$s"

#. translators: %1$s = <strong>, %2$s = </strong>.
#: inc/Addon/Cloudflare/Subscriber.php:250
msgid "%1$sWP Rocket:%2$s Cloudflare cache successfully purged."
msgstr "%1$sWP Rocket:%2$s Cloudflare 缓存已成功清除。"

#. translators: %s is the message returned by the CloudFlare API.
#: inc/Addon/Cloudflare/Subscriber.php:311
msgid "Cloudflare development mode error: %s"
msgstr "Cloudfare 开发者模式错误：%s"

#. translators: %s is the message returned by the CloudFlare API.
#: inc/Addon/Cloudflare/Subscriber.php:318
msgid "Cloudflare development mode %s"
msgstr "Cloudfare 开发者模式 %s"

#. translators: %s is the message returned by the CloudFlare API.
#: inc/Addon/Cloudflare/Subscriber.php:337
msgid "Cloudflare cache level error: %s"
msgstr "Cloudfare 缓存级别错误：%s"

#: inc/Addon/Cloudflare/Subscriber.php:344
msgctxt "Cloudflare caching level"
msgid "standard"
msgstr "标准"

#. translators: %s is the caching level returned by the CloudFlare API.
#: inc/Addon/Cloudflare/Subscriber.php:350
msgid "Cloudflare cache level set to %s"
msgstr "Cloudfare 缓存级别已设置为：%s"

#. translators: %s is the message returned by the CloudFlare API.
#: inc/Addon/Cloudflare/Subscriber.php:368
msgid "Cloudflare minification error: %s"
msgstr "Cloudflare 压缩错误：%s"

#. translators: %s is the message returned by the CloudFlare API.
#: inc/Addon/Cloudflare/Subscriber.php:375
msgid "Cloudflare minification %s"
msgstr "Cloudflare 压缩：%s"

#. translators: %s is the message returned by the CloudFlare API.
#: inc/Addon/Cloudflare/Subscriber.php:393
msgid "Cloudflare rocket loader error: %s"
msgstr "Cloudflare 火箭加载错误：%s"

#. translators: %s is the message returned by the CloudFlare API.
#: inc/Addon/Cloudflare/Subscriber.php:400
msgid "Cloudflare rocket loader %s"
msgstr "Cloudflare 火箭加载 %s"

#. translators: %s is the message returned by the CloudFlare API.
#: inc/Addon/Cloudflare/Subscriber.php:418
msgid "Cloudflare browser cache error: %s"
msgstr "Cloudflare 浏览器缓存错误：%s"

#. translators: %s is the message returned by the CloudFlare API.
#: inc/Addon/Cloudflare/Subscriber.php:425
msgid "Cloudflare browser cache set to %s"
msgstr "Cloudflare 浏览器缓存已被设为 %s"

#. translators: %1$s = strong opening tag, %2$s = strong closing tag.
#: inc/Addon/Cloudflare/Subscriber.php:536
msgid "%1$sWP Rocket:%2$s Optimal settings activated for Cloudflare:"
msgstr "%1$sWP Rocket:%2$s 已为 Cloudflare 启用优化设置："

#. translators: %1$s = strong opening tag, %2$s = strong closing tag.
#: inc/Addon/Cloudflare/Subscriber.php:545
msgid ""
"%1$sWP Rocket:%2$s Optimal settings deactivated for Cloudflare, reverted to "
"previous settings:"
msgstr "%1$sWP Rocket:%2$s Cloudflare 优化设置已禁用, 已回滚至上个版本的设置："

#: inc/Addon/Cloudflare/Subscriber.php:661 inc/admin/options.php:144
#: inc/classes/subscriber/Tools/class-detect-missing-tags-subscriber.php:148
msgid "WP Rocket: "
msgstr "WP Rocket："

#. translators: %s is the error message returned by the API.
#: inc/Addon/Sucuri/Subscriber.php:96
msgid "Sucuri cache purge error: %s"
msgstr "Sucuri 缓存清理错误：%s"

#: inc/Addon/Sucuri/Subscriber.php:101
msgid ""
"The Sucuri cache is being cleared. Note that it may take up to two minutes "
"for it to be fully flushed."
msgstr "正在清除 Sucuri 缓存。 请注意，缓存完全刷新可能需要两分钟。"

#: inc/Addon/Sucuri/Subscriber.php:218
msgid "Sucuri firewall API key was not found."
msgstr "未找到 Sucuri 防火墙 API key。"

#: inc/Addon/Sucuri/Subscriber.php:231
msgid "Sucuri firewall API key is invalid."
msgstr "Sucuri 防火墙 API key无效。"

#. translators: %s is an error message.
#: inc/Addon/Sucuri/Subscriber.php:286
msgid "Error when contacting Sucuri firewall API. Error message was: %s"
msgstr "连接 Sucuri 防火墙API 时出错。错误信息：%s"

#: inc/Addon/Sucuri/Subscriber.php:301
msgid "Could not get a response from the Sucuri firewall API."
msgstr "Sucuri 防火墙 API未响应。"

#: inc/Addon/Sucuri/Subscriber.php:316
msgid "Got an invalid response from the Sucuri firewall API."
msgstr "从Sucuri 防火墙 API 中获得了一个无效响应。"

#: inc/Addon/Sucuri/Subscriber.php:330
msgid "The Sucuri firewall API returned an unknown error."
msgstr "Sucuri 防火墙 API 返回未知错误。"

#. translators: %s is an error message.
#: inc/Addon/Sucuri/Subscriber.php:334
msgid "The Sucuri firewall API returned the following error: %s"
msgid_plural "The Sucuri firewall API returned the following errors: %s"
msgstr[0] "Sucuri 防火墙 API 返回以下错误：%s"

#. Translators: %1$s = plugin name(s), %2$s = opening <a> tag, %3$s = closing
#. </a> tag.
#: inc/Addon/WebP/AdminSubscriber.php:93
msgid ""
"You are using %1$s to serve WebP images so you do not need to enable this "
"option. %2$sMore info%3$s %4$s If you prefer to have WP Rocket serve WebP "
"for you instead, please disable WebP display in %1$s."
msgid_plural ""
"You are using %1$s to serve WebP images so you do not need to enable this "
"option. %2$sMore info%3$s %4$s If you prefer to have WP Rocket serve WebP "
"for you instead, please disable WebP display in %1$s."
msgstr[0] ""
"您正在使用 %1$s的WebP 图片相关功能，因此无需开启该选项。%2$s更多信息%3$s %4$s如您想换用 WP Rocket， "
"请转到%1$s禁用相关选项。"

#: inc/Addon/WebP/AdminSubscriber.php:105
msgid "WebP cache is disabled by filter."
msgstr "WebP 缓存被过滤器禁用。"

#. Translators: %1$s = plugin name(s), %2$s = opening <a> tag, %3$s = closing
#. </a> tag.
#: inc/Addon/WebP/AdminSubscriber.php:115
#: inc/Addon/WebP/AdminSubscriber.php:141
msgid ""
"You are using %1$s to convert images to WebP. If you want WP Rocket to serve"
" them for you, activate this option. %2$sMore info%3$s"
msgid_plural ""
"You are using %1$s to convert images to WebP. If you want WP Rocket to serve"
" them for you, activate this option. %2$sMore info%3$s"
msgstr[0] "您正在使用 %1$s 将图片转换成WebP格式。如您想换用 WP Rocket，请启用该选项。%2$s更多信息%3$s"

#. Translators: %1$s = plugin name(s), %2$s = opening <a> tag, %3$s = closing
#. </a> tag.
#: inc/Addon/WebP/AdminSubscriber.php:127
#: inc/Addon/WebP/AdminSubscriber.php:153
msgid ""
"You are using %1$s to convert images to WebP. WP Rocket will create separate"
" cache files to serve your WebP images. %2$sMore info%3$s"
msgid_plural ""
"You are using %1$s to convert images to WebP. WP Rocket will create separate"
" cache files to serve your WebP images. %2$sMore info%3$s"
msgstr[0] "您正在使用%1$s将图片转换成WebP格式。WP Rocket将为您的WebP 图片生成单独的缓存文件。%2$s更多信息%3$s"

#. Translators: %1$s = opening <a> tag, %2$s = closing </a> tag.
#: inc/Addon/WebP/AdminSubscriber.php:173
msgid ""
"%5$sWe have not detected any compatible WebP plugin!%6$s%4$s If you don’t "
"already have WebP images on your site consider using %3$sImagify%2$s or "
"another supported plugin. %1$sMore info%2$s %4$s If you are not using WebP "
"do not enable this option."
msgstr ""
"%5$s未检测到兼容 WebP 插件 !%6$s%4$s如果您的网站内没有WebP图片可考虑使用 %3$sImagify%2$s 或其他支持的插件. "
"%1$s更多信息%2$s %4$s如果您没有使用 WebP, 请勿开启该选项."

#: inc/Addon/WebP/AdminSubscriber.php:185
msgid "WP Rocket will create separate cache files to serve your WebP images."
msgstr "WP Rocket 将为您的 WebP 图片创建单独的缓存文件。"

#: inc/admin/admin.php:18 inc/common/admin-bar.php:266
#: inc/deprecated/deprecated.php:1787
msgid "Support"
msgstr "支持"

#: inc/admin/admin.php:20
msgid "Docs"
msgstr "文档"

#: inc/admin/admin.php:22 inc/common/admin-bar.php:254
msgid "FAQ"
msgstr "常见问题"

#: inc/admin/admin.php:24 inc/common/admin-bar.php:63
msgid "Settings"
msgstr "设置"

#: inc/admin/admin.php:96 inc/admin/admin.php:118 inc/deprecated/3.5.php:898
#: inc/Engine/Cache/AdminSubscriber.php:133
msgid "Clear this cache"
msgstr "清除该缓存"

#: inc/admin/admin.php:397
msgid "Settings import failed: you do not have the permissions to do this."
msgstr "设置导入失败：无权限。"

#: inc/admin/admin.php:401
msgid "Settings import failed: no file uploaded."
msgstr "导入配置失败：未上传文件。"

#: inc/admin/admin.php:405
msgid "Settings import failed: incorrect filename."
msgstr "导入配置失败：文件名无效。"

#: inc/admin/admin.php:416
msgid "Settings import failed: incorrect filetype."
msgstr "导入配置失败：文件类型无效。"

#: inc/admin/admin.php:426
msgid "Settings import failed: "
msgstr "导入配置失败："

#: inc/admin/admin.php:442
msgid "Settings import failed: unexpected file content."
msgstr "导入配置失败: 文件内容有误。"

#: inc/admin/admin.php:472
msgid "Settings imported and saved."
msgstr "已导入并保存配置。"

#: inc/admin/options.php:84 inc/Engine/Admin/Settings/Page.php:708
msgid "Excluded CSS Files"
msgstr "排除CSS"

#: inc/admin/options.php:85 inc/Engine/Admin/Settings/Page.php:862
msgid "Excluded Inline JavaScript"
msgstr "排除内联的JavaScript"

#: inc/admin/options.php:86 inc/Engine/Admin/Settings/Page.php:880
#: inc/Engine/Admin/Settings/Page.php:913
#: inc/Engine/Admin/Settings/Page.php:960
msgid "Excluded JavaScript Files"
msgstr "排除JavaScript文件"

#: inc/admin/options.php:87
msgid "Defer JavaScript Files"
msgstr "延迟加载 JavaScript 文件"

#: inc/admin/options.php:88
msgid "Excluded Delay JavaScript Files"
msgstr "已排除的 延迟 JavaScript 文件"

#: inc/admin/options.php:89 inc/Engine/Admin/Settings/Page.php:1329
msgid "Never Cache URL(s)"
msgstr "永不缓存（URL）："

#: inc/admin/options.php:90 inc/Engine/Admin/Settings/Page.php:1343
msgid "Never Cache User Agent(s)"
msgstr "永不缓存（User Agents）："

#: inc/admin/options.php:91 inc/Engine/Admin/Settings/Page.php:1349
msgid "Always Purge URL(s)"
msgstr "总是清除以下URL缓存："

#: inc/admin/options.php:92 inc/Engine/Admin/Settings/Page.php:1616
msgid "Exclude files from CDN"
msgstr "从CDN中排除文件"

#. translators: 1 and 2 can be anything.
#: inc/admin/options.php:110
msgid "%1$s: <em>%2$s</em>"
msgstr "%1$s: <em>%2$s</em>"

#: inc/admin/options.php:120
msgid "The following pattern is invalid and has been removed:"
msgid_plural "The following patterns are invalid and have been removed:"
msgstr[0] "以下模式无效且已被删除："

#: inc/admin/options.php:136
msgid "More info"
msgstr "更多信息"

#: inc/admin/ui/meta-boxes.php:37 inc/admin/ui/notices.php:671
#: inc/common/admin-bar.php:84 inc/common/admin-bar.php:147
#: views/settings/page-sections/dashboard.php:150
msgid "Clear cache"
msgstr "清除缓存"

#: inc/admin/ui/meta-boxes.php:67
msgid "WP Rocket Options"
msgstr "WP Rocket 设置"

#: inc/admin/ui/meta-boxes.php:96
msgid "Never cache this page"
msgstr "永不缓存此页面"

#: inc/admin/ui/meta-boxes.php:100
msgid "Activate these options on this post:"
msgstr "在当前文章中启用这些选项："

#: inc/admin/ui/meta-boxes.php:103
msgid "LazyLoad for images"
msgstr "图片延迟加载"

#: inc/admin/ui/meta-boxes.php:104
msgid "LazyLoad for iframes/videos"
msgstr "框架 / 视频 延迟加载"

#: inc/admin/ui/meta-boxes.php:105
msgid "Minify/combine CSS"
msgstr "压缩 / 合并 CSS"

#: inc/admin/ui/meta-boxes.php:106 inc/Engine/Admin/Settings/Page.php:760
msgid "Remove Unused CSS"
msgstr "移除未使用的 CSS"

#: inc/admin/ui/meta-boxes.php:107
msgid "Minify/combine JS"
msgstr "压缩 / 合并 JS"

#: inc/admin/ui/meta-boxes.php:108 inc/deprecated/deprecated.php:1773
#: inc/Engine/Admin/Settings/Page.php:1586
#: inc/Engine/Admin/Settings/Page.php:1597
msgid "CDN"
msgstr "CDN"

#: inc/admin/ui/meta-boxes.php:109 inc/Engine/Admin/Settings/Page.php:789
msgid "Load CSS asynchronously"
msgstr "异步加载CSS文件"

#: inc/admin/ui/meta-boxes.php:110
msgid "Defer JS"
msgstr "JS 延迟加载"

#: inc/admin/ui/meta-boxes.php:111 inc/Engine/Admin/Settings/Page.php:931
msgid "Delay JavaScript execution"
msgstr "JavaScript 延迟执行"

#. translators: %s is the name of the option.
#: inc/admin/ui/meta-boxes.php:117
msgid "Activate first the %s option."
msgstr "请先启用 %s 选项。"

#. translators: %1$s = opening strong tag, %2$s = closing strong tag.
#: inc/admin/ui/meta-boxes.php:133
msgid ""
"%1$sNote:%2$s None of these options will be applied if this post has been "
"excluded from cache in the global cache settings."
msgstr "%1$s注意：%2$s如该文章已在全局缓存设置中被设为排除，这些选项将不会生效。"

#. translators: %1$s WP Rocket plugin name; %2$s = file name.
#: inc/admin/ui/notices.php:30 inc/admin/ui/notices.php:43
msgid ""
"<strong>%1$s</strong> has not been deactivated due to missing writing permissions.<br>\n"
"Make <strong>%2$s</strong> writeable and retry deactivation, or force deactivation now:"
msgstr ""
"<strong>%1$s</strong> 未被禁用：无写入权限。<br>\n"
"将 <strong>%2$s</strong> 设为可写并重试，或立即强制禁用："

#. translators: %s is WP Rocket plugin name.
#: inc/admin/ui/notices.php:97
msgid ""
"<strong>%s</strong>: One or more plugins have been enabled or disabled, "
"clear the cache if they affect the front end of your site."
msgstr "<strong>%s</strong>：监测到插件被启用/禁用，如插件影响网站前台页面，请清除缓存。"

#. translators: %s is WP Rocket plugin name.
#: inc/admin/ui/notices.php:141
msgid ""
"<strong>%s</strong>: The following plugins are not compatible with this "
"plugin and may cause unexpected results:"
msgstr "<strong>%s</strong>：下列插件与WP Rocket不兼容，可能导致出错："

#: inc/admin/ui/notices.php:147
msgid "Deactivate"
msgstr "停用"

#: inc/admin/ui/notices.php:189
msgid ""
"WP Rocket Footer JS is not an official add-on. It prevents some options in "
"WP Rocket from working correctly. Please deactivate it if you have problems."
msgstr "WP Rocket Footer JS 为非官方插件，会阻碍 WP Rocket 中某些选项的正常工作。如有问题请禁用哦。"

#. translators: %1$s = opening link tag, %2$s = closing link tag.
#: inc/admin/ui/notices.php:229
msgid ""
"Endurance Cache is currently enabled, which will conflict with WP Rocket "
"Cache. Please set the Endurance Cache cache level to Off (Level 0) on the "
"%1$sSettings > General%2$s page to prevent any issues."
msgstr ""
"Endurance Cache目前已启用，这将与WP Rocket Cache冲突。请在%1$s设置>一般%2$s页面设置Endurance "
"Cache的缓存级别为关闭(Level 0)以防止出现任何问题。"

#. translators: %1$s WP Rocket plugin name; %2$s = opening link; %3$s =
#. closing link
#: inc/admin/ui/notices.php:250
msgid ""
"%1$s: A custom permalink structure is required for the plugin to work "
"properly. %2$sGo to permalinks settings%3$s"
msgstr "%1$s：必须先启用固定链接，插件才能正常工作。%2$s进入固定链接设置%3$s"

#. translators: %s = plugin name.
#: inc/admin/ui/notices.php:297
msgid ""
"%s could not modify the .htaccess file due to missing writing permissions."
msgstr "%s 无法修改 .htaccess 文件：无写入权限。"

#. translators: This is a doc title! %1$s = opening link; %2$s = closing link
#: inc/admin/ui/notices.php:303 inc/admin/ui/notices.php:788
msgid "Troubleshoot: %1$sHow to make system files writeable%2$s"
msgstr "故障解决：%1$s如何将系统文件设为可写%2$s"

#. translators: Documentation exists in EN, DE, FR, ES, IT; use loaclised URL
#. if applicable
#: inc/admin/ui/notices.php:305 inc/admin/ui/notices.php:790
msgid ""
"https://docs.wp-rocket.me/article/626-how-to-make-system-files-htaccess-wp-"
"config-writeable/?utm_source=wp_plugin&utm_medium=wp_rocket"
msgstr ""
"https://docs.wp-rocket.me/article/626-how-to-make-system-files-htaccess-wp-"
"config-writeable/?utm_source=wp_plugin&utm_medium=wp_rocket"

#: inc/admin/ui/notices.php:311
msgid ""
"Don’t worry, WP Rocket’s page caching and settings will still function "
"correctly."
msgstr "不用担心，WP Rocket 的页面缓存和设置仍将正常工作。"

#: inc/admin/ui/notices.php:311
msgid ""
"For optimal performance, adding the following lines into your .htaccess is "
"recommended (not required):"
msgstr "为了优化性能，建议添加下列代码到您的 .htaccess 文件（非必须）："

#. translators: %1$s = plugin name, %2$s + %3$s = opening links, %4$s =
#. closing link
#: inc/admin/ui/notices.php:458
msgid ""
"%1$s is good to go! %2$sTest your load time%4$s, or visit your "
"%3$ssettings%4$s."
msgstr "%1$s准备好啦！%2$s测试您的加载时间%4$s，或访问您的%3$s设置%4$s。"

#: inc/admin/ui/notices.php:499
msgid ""
"Would you allow WP Rocket to collect non-sensitive diagnostic data from this"
" website?"
msgstr "您是否愿意 WP Rocket 收集此网站的脱敏诊断数据？"

#: inc/admin/ui/notices.php:500
msgid "This would help us to improve WP Rocket for you in the future."
msgstr "这将有助于将来我们为您持续改进 WP Rocket 。"

#. translators: button text, click will expand data collection preview
#: inc/admin/ui/notices.php:506
msgid "What info will we collect?"
msgstr "我们收集何种信息？"

#: inc/admin/ui/notices.php:511
msgid ""
"Below is a detailed view of all data WP Rocket will collect if granted "
"permission. WP Rocket will never transmit any domain names or email "
"addresses (except for license validation), IP addresses, or third-party API "
"keys."
msgstr ""
"以下详细列出了 WP Rocket 在获得许可后将收集的所有数据。WP Rocket 绝不会传输任何域名、邮箱（授权验证除外），IP地址或第三方API "
"key。"

#. translators: button text for data collection opt-in
#: inc/admin/ui/notices.php:520
msgid "Yes, allow"
msgstr "是，允许"

#. translators: button text for data collection opt-in
#: inc/admin/ui/notices.php:523
msgid "No, thanks"
msgstr "不，谢谢"

#: inc/admin/ui/notices.php:562
msgid "Thank you!"
msgstr "谢谢！"

#: inc/admin/ui/notices.php:567
msgid "WP Rocket now collects these metrics from your website:"
msgstr "WP Rocket 现在从您的网站收集这些指标："

#. translators: %s = plugin name.
#: inc/admin/ui/notices.php:605
msgid "%s: Cache cleared."
msgstr "%s：缓存已清除。"

#. translators: %s = plugin name.
#: inc/admin/ui/notices.php:612
msgid "%s: Post cache cleared."
msgstr "%s：文章缓存已清除。"

#. translators: %s = plugin name.
#: inc/admin/ui/notices.php:619
msgid "%s: Term cache cleared."
msgstr "%s：分类缓存已清除。"

#. translators: %s = plugin name).
#: inc/admin/ui/notices.php:626
msgid "%s: User cache cleared."
msgstr "%s：用户缓存已清除。"

#: inc/admin/ui/notices.php:662 inc/Engine/License/views/promo-banner.php:36
#: inc/Engine/License/views/renewal-expired-banner-ocd-disabled.php:32
#: inc/Engine/License/views/renewal-expired-banner-ocd.php:42
#: inc/Engine/License/views/renewal-expired-banner.php:30
#: views/settings/page-sections/dashboard.php:46
msgid "Dismiss this notice"
msgstr "忽略通知"

#: inc/admin/ui/notices.php:682
#: inc/Engine/Optimization/RUCSS/Admin/Settings.php:131
#: views/settings/page-sections/dashboard.php:188
msgid "Clear Used CSS"
msgstr "清理已使用的 CSS"

#: inc/admin/ui/notices.php:685
msgid "Stop Preload"
msgstr "停止预缓存"

#: inc/admin/ui/notices.php:691
msgid "Turn on Remove Unused CSS"
msgstr "开启 移除未使用CSS 功能"

#: inc/admin/ui/notices.php:697
msgid "Enable “Separate Cache Files for Mobile Devices” now"
msgstr "开启 “移动端单独缓存” 功能"

#: inc/admin/ui/notices.php:715
msgid "Force deactivation "
msgstr "强制停用 "

#: inc/admin/ui/notices.php:745
msgid "The following code should have been written to this file:"
msgstr "下列代码应写入到此文件："

#. translators: %s = plugin name.
#: inc/admin/ui/notices.php:776
msgid "%s cannot configure itself due to missing writing permissions."
msgstr "%s 无法完成配置：无写入权限。"

#. translators: %s = file/folder name
#: inc/admin/ui/notices.php:782
msgid "Affected file/folder: %s"
msgstr "受影响的文件/文件夹：%s"

#: inc/classes/admin/class-logs.php:124
msgid "The debug file could not be deleted."
msgstr "这个调试文件无法被删除。"

#. Translators: %1$s = Plugin name, %2$s = Plugin version.
#: inc/classes/class-wp-rocket-requirements-check.php:147
msgid "To function properly, %1$s %2$s requires at least:"
msgstr "为确保正常工作，%1$s %2$s 需要至少："

#. Translators: %1$s = PHP version required.
#: inc/classes/class-wp-rocket-requirements-check.php:151
msgid ""
"PHP %1$s. To use this WP Rocket version, please ask your web host how to "
"upgrade your server to PHP %1$s or higher."
msgstr "PHP %1$s。要使用当前 WP Rocket 版本，请联系您的主机服务商将 PHP 升级至 %1$s 或更高版本。"

#. Translators: %1$s = WordPress version required.
#: inc/classes/class-wp-rocket-requirements-check.php:156
msgid ""
"WordPress %1$s. To use this WP Rocket version, please upgrade WordPress to "
"version %1$s or higher."
msgstr "WordPress %1$s. 要使用当前 WP Rocket 版本，请升级 WordPress 至 %1$s 或更高版本。"

#: inc/classes/class-wp-rocket-requirements-check.php:159
msgid ""
"If you are not able to upgrade, you can rollback to the previous version by "
"using the button below."
msgstr "如无法升级，您可使用下方按钮还原至旧版本。"

#. Translators: %s = Previous plugin version.
#: inc/classes/class-wp-rocket-requirements-check.php:161
#: inc/deprecated/deprecated.php:1947
msgid "Re-install version %s"
msgstr "重新安装 %s 版本"

#. translators: %s is the plugin name.
#: inc/classes/class-wp-rocket-requirements-check.php:203
#: inc/classes/class-wp-rocket-requirements-check.php:214
#: inc/deprecated/3.11.php:236 inc/deprecated/3.11.php:251
#: inc/Engine/Plugin/UpdaterSubscriber.php:483
#: inc/Engine/Plugin/UpdaterSubscriber.php:497
msgid "%s Update Rollback"
msgstr "%s回滚更新"

#: inc/classes/logger/class-logger.php:260
#: inc/classes/logger/class-logger.php:292
msgid "The log file does not exist."
msgstr "日志文件不存在。"

#: inc/classes/logger/class-logger.php:266
#: inc/classes/logger/class-logger.php:298
msgid "The log file could not be read."
msgstr "日志文件不可读。"

#: inc/classes/logger/class-logger.php:285
msgid "The logs are not saved into a file."
msgstr "日志未保存到文件中。"

#. translators: %1$s = missing tags;
#: inc/classes/subscriber/Tools/class-detect-missing-tags-subscriber.php:151
msgid ""
"Failed to detect the following requirement in your theme: closing %1$s."
msgid_plural ""
"Failed to detect the following requirements in your theme: closing %1$s."
msgstr[0] "检测失败：关闭%1$s。"

#. translators: Documentation exists in EN, FR; use localized URL if
#. applicable.
#: inc/classes/subscriber/Tools/class-detect-missing-tags-subscriber.php:159
msgid ""
"https://docs.wp-rocket.me/article/99-pages-not-cached-or-minify-cssjs-not-"
"working/?utm_source=wp_plugin&utm_medium=wp_rocket#theme"
msgstr ""
"https://docs.wp-rocket.me/article/99-pages-not-cached-or-minify-cssjs-not-"
"working/?utm_source=wp_plugin&utm_medium=wp_rocket#theme"

#: inc/common/admin-bar.php:84 inc/common/admin-bar.php:147
#: views/settings/page-sections/dashboard.php:150
msgid "Clear and preload cache"
msgstr "清空并预缓存"

#: inc/common/admin-bar.php:135 inc/functions/i18n.php:40
#: inc/functions/i18n.php:50
msgid "All languages"
msgstr "所有语言"

#: inc/common/admin-bar.php:171
msgid "Clear this post"
msgstr "清除此文章"

#: inc/common/admin-bar.php:185
msgid "Purge this URL"
msgstr "清除此URL"

#: inc/common/admin-bar.php:205
msgid "Purge Sucuri cache"
msgstr "清除 Sucuri 缓存"

#: inc/common/admin-bar.php:229 views/settings/fields/rocket-cdn.php:55
msgid "Purge RocketCDN cache"
msgstr "清除 RocketCDN 缓存"

#: inc/common/admin-bar.php:242 views/settings/partials/documentation.php:14
msgid "Documentation"
msgstr "文档"

#: inc/deprecated/3.2.php:52 views/settings/page-sections/imagify.php:36
msgid "Activate Imagify"
msgstr "启用 Imagify"

#: inc/deprecated/3.2.php:52
msgid "Install Imagify for Free"
msgstr "免费安装 Imagify"

#: inc/deprecated/3.2.php:67
msgid ""
"Speed up your website and boost your SEO by reducing image file sizes "
"without losing quality with Imagify."
msgstr "在不损失质量的前提下，尽可能减小图片文件体积以加速网站和改善SEO。"

#: inc/deprecated/3.2.php:72
msgid "More details"
msgstr "更多详情"

#. translators: %d is the number of pages preloaded.
#: inc/deprecated/3.2.php:228
msgid "Sitemap preload: %d pages have been cached."
msgstr "Sitemap预缓存：已预缓存 %d 个页面。"

#. translators: %d = Number of pages preloaded.
#: inc/deprecated/3.2.php:261
msgid ""
"Sitemap preload: %d uncached pages have now been preloaded. (refresh to see "
"progress)"
msgstr "Sitemap预缓存：已预缓存 %d 个未缓存页面。（刷新以查看进度）"

#. translators: %s is an URL.
#: inc/deprecated/3.3.php:761 inc/deprecated/3.3.php:770
#: inc/Engine/Plugin/UpdaterApiTools.php:32
msgid ""
"An unexpected error occurred. Something may be wrong with WP-Rocket.me or "
"this server&#8217;s configuration. If you continue to have problems, <a "
"href=\"%s\">contact support</a>."
msgstr "出现未知错误。WP-Rocket.me 或当前系统配置有问题。如果持续出现这些问题，请<a href=\"%s\">联系客服</a>。"

#: inc/deprecated/3.4.php:18
msgid "Choose a domain from the list"
msgstr "从列表选择域名"

#: inc/deprecated/3.4.php:31
msgid "No domain available in your Cloudflare account"
msgstr "您的Cloudflare账户中未找到可用域名"

#: inc/deprecated/3.5.php:71 inc/deprecated/3.5.php:195
msgid ""
"Curl is disabled on your server. Please ask your host to enable it. This is "
"required for the Cloudflare Add-on to work correctly."
msgstr "Curl已被禁用。请联系主机服务商开启该功能，否则Cloudflare扩展无法正常工作哦。"

#. translators: %1$s = opening link; %2$s = closing link
#: inc/deprecated/3.5.php:79
msgid ""
"Cloudflare email, API key and Zone ID are not set. Read the "
"%1$sdocumentation%2$s for further guidance."
msgstr "Cloudflare 邮箱、API key 和 Zone ID 未设置。阅读%1$s文档%2$s 可获取更多信息。"

#. translators: %1$s = opening link; %2$s = closing link
#: inc/deprecated/3.5.php:206
msgid ""
"Cloudflare email and API key are not set. Read the %1$sdocumentation%2$s for"
" further guidance."
msgstr "Cloudflare 邮箱、API key 未设置。阅读%1$s文档%2$s可获取更多信息。"

#: inc/deprecated/3.5.php:271
msgid "Connection to Cloudflare failed"
msgstr "Cloudflare连接失败"

#. translators: %s = CloudFare API return message.
#: inc/deprecated/3.5.php:587
msgid "<strong>WP Rocket:</strong> %s"
msgstr "<strong>WP Rocket：</strong> %s"

#: inc/deprecated/3.5.php:592
msgid "<strong>WP Rocket:</strong> Cloudflare cache successfully purged."
msgstr "<strong>WP Rocket:</strong> 成功刷新 Cloudflare 缓存。"

#: inc/deprecated/3.5.php:858 inc/Engine/HealthCheck/HealthCheck.php:81
msgid ""
"The following scheduled event failed to run. This may indicate the CRON "
"system is not running properly, which can prevent some WP Rocket features "
"from working as intended:"
msgid_plural ""
"The following scheduled events failed to run. This may indicate the CRON "
"system is not running properly, which can prevent some WP Rocket features "
"from working as intended:"
msgstr[0] "以下调度的事件运行失败。这可能是CRON系统运行不正常，导致它可能会阻止某些WP Rocket功能按预期工作："

#: inc/deprecated/3.5.php:867 inc/Engine/HealthCheck/HealthCheck.php:88
msgid "Please contact your host to check if CRON is working."
msgstr "请检查您的主机以确认 CRON (定时计划) 是否正常工作。"

#: inc/deprecated/3.10.php:163
msgid "OPcache purge failed."
msgstr "OPcache 清除失败。"

#: inc/deprecated/3.10.php:168
msgid "OPcache successfully purged"
msgstr "OPcache 成功清除。"

#: inc/deprecated/3.11.php:81
msgid "Yoast SEO XML sitemap"
msgstr "Yoast SEO XML 网站地图"

#. translators: %s = Name of the plugin.
#. translators: %s = plugin name, e.g. Yoast SEO.
#: inc/deprecated/3.11.php:83 inc/deprecated/3.12.php:58
#: inc/deprecated/3.12.php:87 inc/deprecated/3.12.php:186
#: inc/deprecated/3.12.php:253 inc/deprecated/3.12.php:342
msgid ""
"We automatically detected the sitemap generated by the %s plugin. You can "
"check the option to preload it."
msgstr "已自动监测到 %s 插件生成的sitemap。 您可查看相关选项来预缓存。"

#. translators: 1 and 3 are link openings, 2 is a link closing.
#: inc/deprecated/3.11.php:279 inc/Engine/Plugin/UpdaterSubscriber.php:520
msgid "%1$sReturn to WP Rocket%2$s or %3$sgo to Plugins page%2$s"
msgstr "%1$s返回 WP Rocket %2$s或 %3$s转到插件页%2$s"

#: inc/deprecated/3.12.php:56
msgid "All in One SEO XML sitemap"
msgstr "All in One SEO XML 网站地图"

#: inc/deprecated/3.12.php:85
msgid "Rank Math XML sitemap"
msgstr "Rank Math XML 网站地图"

#: inc/deprecated/3.12.php:184
msgid "SEOPress XML sitemap"
msgstr "SEOPress XML sitemap"

#: inc/deprecated/3.12.php:251
msgid "The SEO Framework XML sitemap"
msgstr "The SEO Framework XML 网站地图"

#: inc/deprecated/3.12.php:332
msgid "Jetpack XML Sitemaps"
msgstr "Jetpack XML 网站地图"

#: inc/deprecated/3.12.php:334
msgid "Preload the sitemap from the Jetpack plugin"
msgstr "从Jetpack插件预缓存网站地图"

#: inc/deprecated/deprecated.php:240
msgid "<strong>JS</strong> files with Deferred Loading JavaScript"
msgstr "异步加载<strong>JS</strong>文件"

#: inc/deprecated/deprecated.php:293
msgid "Add URL"
msgstr "添加URL"

#: inc/deprecated/deprecated.php:1245
#: inc/Engine/Admin/Settings/Settings.php:452
msgid "Settings saved."
msgstr "设置已保存。"

#: inc/deprecated/deprecated.php:1277 views/settings/fields/import-form.php:22
msgid ""
"Before you can upload your import file, you will need to fix the following "
"error:"
msgstr "在上传配置文件前，你必须先修复以下错误："

#. translators: %s is the maximum upload size set on the current server.
#: inc/deprecated/deprecated.php:1288 views/settings/fields/import-form.php:35
msgid "Choose a file from your computer (maximum size: %s)"
msgstr "从您的电脑选择文件（≤ %s）"

#: inc/deprecated/deprecated.php:1294 inc/Engine/Admin/Settings/Render.php:422
msgid "Upload file and import settings"
msgstr "上传文件并导入配置"

#: inc/deprecated/deprecated.php:1385
msgid "Your Cloudflare credentials are valid."
msgstr "您的Cloudflare授权已验证。"

#: inc/deprecated/deprecated.php:1389
msgid "Your Cloudflare credentials are invalid!"
msgstr "您的Cloudflare授权无效！"

#: inc/deprecated/deprecated.php:1453
msgid "Save and optimize"
msgstr "保存并优化"

#: inc/deprecated/deprecated.php:1453
msgid "Optimize"
msgstr "优化"

#: inc/deprecated/deprecated.php:1464 inc/deprecated/deprecated.php:1666
msgctxt "screen-reader-text"
msgid "Note:"
msgstr "注意："

#: inc/deprecated/deprecated.php:1470
msgctxt "screen-reader-text"
msgid "Performance tip:"
msgstr "性能优化小窍门："

#: inc/deprecated/deprecated.php:1476
msgctxt "screen-reader-text"
msgid "Third-party feature detected:"
msgstr "检测到第三方功能："

#: inc/deprecated/deprecated.php:1488 inc/deprecated/deprecated.php:1672
msgctxt "screen-reader-text"
msgid "Warning:"
msgstr "警告："

#: inc/deprecated/deprecated.php:1501
msgctxt "button text"
msgid "Download settings"
msgstr "下载配置"

#: inc/deprecated/deprecated.php:1531
msgid "Replace site's hostname with:"
msgstr "将站点主机名重设为："

#: inc/deprecated/deprecated.php:1550 inc/deprecated/deprecated.php:1587
#: inc/deprecated/deprecated.php:1615 views/settings/fields/cnames.php:41
#: views/settings/fields/cnames.php:75 views/settings/fields/cnames.php:105
msgid "reserved for"
msgstr "套用到"

#: inc/deprecated/deprecated.php:1552 inc/deprecated/deprecated.php:1589
#: inc/deprecated/deprecated.php:1617 views/settings/fields/cnames.php:44
#: views/settings/fields/cnames.php:78 views/settings/fields/cnames.php:108
msgid "All files"
msgstr "所有文件"

#: inc/deprecated/deprecated.php:1564 inc/deprecated/deprecated.php:1594
#: inc/deprecated/deprecated.php:1622 views/settings/fields/cnames.php:56
#: views/settings/fields/cnames.php:90 views/settings/fields/cnames.php:120
msgid "Images"
msgstr "图片"

#: inc/deprecated/deprecated.php:1635 views/settings/fields/cnames.php:132
msgid "Add CNAME"
msgstr "添加"

#: inc/deprecated/deprecated.php:1706
msgid "Watch the video"
msgstr "观看视频"

#: inc/deprecated/deprecated.php:1771
msgid "Basic"
msgstr "基础设置"

#: inc/deprecated/deprecated.php:1772
msgid "Static Files"
msgstr "静态文件"

#: inc/deprecated/deprecated.php:1774
msgid "Advanced"
msgstr "高级"

#: inc/deprecated/deprecated.php:1775 inc/Engine/Admin/Settings/Page.php:1431
msgid "Database"
msgstr "数据库"

#: inc/deprecated/deprecated.php:1776 inc/Engine/Admin/Settings/Page.php:1164
msgid "Preload"
msgstr "预缓存"

#: inc/deprecated/deprecated.php:1786
#: inc/Engine/Admin/Settings/Subscriber.php:168
#: views/settings/page-sections/tools.php:33
msgid "Tools"
msgstr "工具"

#: inc/deprecated/deprecated.php:1789 inc/Engine/Admin/Settings/Page.php:352
#: views/settings/page-sections/dashboard.php:80
msgid "License"
msgstr "授权"

#. Translators: %1$s = Plugin name, %2$s = Plugin version, %3$s = PHP version
#. required.
#: inc/deprecated/deprecated.php:1944
msgid ""
"%1$s %2$s requires at least PHP %3$s to function properly. To use this "
"version, please ask your web host how to upgrade your server to PHP %3$s or "
"higher. If you are not able to upgrade, you can rollback to the previous "
"version by using the button below."
msgstr ""
"%1$s %2$s 需要PHP %3$s 以上版本才能正常运行。要使用该版本，请联系您的主机服务商升级PHP至 %3$s "
"或更高版本。如无法升级，您可使用下方按钮还原至旧版本。"

#. translators: 1: PHP class name, 2: version number, 3: replacement class
#. name.
#: inc/deprecated/DeprecatedClassTrait.php:54
msgid ""
"The called class %1$s is <strong>deprecated</strong> since version %2$s! Use"
" %3$s instead."
msgstr "从版本%2$s开始，被调用的类%1$s<strong>已弃用</strong>！请使用%3$s替代。"

#. translators: 1: PHP class name, 2: version number.
#: inc/deprecated/DeprecatedClassTrait.php:65
msgid ""
"The called class %1$s is <strong>deprecated</strong> since version %2$s!"
msgstr "从版本%2$s开始，调用类%1$s<strong>已弃用</strong>。"

#: inc/deprecated/Engine/Addon/FacebookTracking/Subscriber.php:92
#: inc/deprecated/Engine/Addon/GoogleTracking/Subscriber.php:137
#: inc/Engine/Admin/Database/Subscriber.php:79
msgid "weekly"
msgstr "每周"

#: inc/Engine/Admin/Database/Optimization.php:30
#: inc/Engine/Admin/Settings/Page.php:1475
msgid "Revisions"
msgstr "修订版本"

#: inc/Engine/Admin/Database/Optimization.php:31
#: inc/Engine/Admin/Settings/Page.php:1485
msgid "Auto Drafts"
msgstr "自动草稿"

#: inc/Engine/Admin/Database/Optimization.php:32
#: inc/Engine/Admin/Settings/Page.php:1495
msgid "Trashed Posts"
msgstr "回收站文章"

#: inc/Engine/Admin/Database/Optimization.php:33
#: inc/Engine/Admin/Settings/Page.php:1505
msgid "Spam Comments"
msgstr "垃圾评论"

#: inc/Engine/Admin/Database/Optimization.php:34
#: inc/Engine/Admin/Settings/Page.php:1515
msgid "Trashed Comments"
msgstr "回收站评论"

#: inc/Engine/Admin/Database/Optimization.php:35
msgid "Transients"
msgstr "临时数据"

#: inc/Engine/Admin/Database/Optimization.php:36
msgid "Tables"
msgstr "表"

#: inc/Engine/Admin/Database/Subscriber.php:85
msgid "monthly"
msgstr "每月"

#: inc/Engine/Admin/Database/Subscriber.php:194
msgid "Database optimization process is running"
msgstr "数据库优化正在运行"

#: inc/Engine/Admin/Database/Subscriber.php:224
msgid ""
"Database optimization process is complete. Everything was already optimized!"
msgstr "数据库优化完成。已为您优化所有数据！"

#: inc/Engine/Admin/Database/Subscriber.php:227
msgid ""
"Database optimization process is complete. List of optimized items below:"
msgstr "数据库优化完成。优化项列表如下："

#. translators: %1$d = number of items optimized, %2$s = type of optimization
#: inc/Engine/Admin/Database/Subscriber.php:235
msgid "%1$d %2$s optimized."
msgstr "%1$d %2$s 已优化。"

#. translators: %1$s = <strong>, %2$s = </strong>, %3$s = <a>, %4$s = </a>.
#: inc/Engine/Admin/DomainChange/Subscriber.php:158
msgid ""
"%1$sWP Rocket:%2$s We detected that the website domain has changed. The "
"configuration files must be regenerated for the page cache and all other "
"optimizations to work as intended. %3$sLearn More%4$s"
msgstr "%1$sWP Rocket:%2$s 检测到域名变更。请重新生成设置文件，以确保页面缓存和所有其他优化正常工作。 %3$s了解更多%4$s"

#: inc/Engine/Admin/DomainChange/Subscriber.php:186
msgid "Regenerate WP Rocket configuration files now"
msgstr "立即重新生成 WP Rocket 设置文件"

#: inc/Engine/Admin/Settings/Page.php:201
msgid "Save Changes"
msgstr "保存更改"

#: inc/Engine/Admin/Settings/Page.php:201
msgid "Validate License"
msgstr "验证授权"

#: inc/Engine/Admin/Settings/Page.php:257
#: inc/Engine/Admin/Settings/Page.php:258
msgid "Unavailable"
msgstr "不可用"

#: inc/Engine/Admin/Settings/Page.php:369
msgid "API key"
msgstr "API key"

#: inc/Engine/Admin/Settings/Page.php:384
msgid "Email address"
msgstr "邮箱"

#: inc/Engine/Admin/Settings/Page.php:410
msgid "Dashboard"
msgstr "仪表盘"

#: inc/Engine/Admin/Settings/Page.php:411
msgid "Get help, account info"
msgstr "获取帮助、账户信息"

#: inc/Engine/Admin/Settings/Page.php:420
msgid "My Status"
msgstr "我的状态"

#: inc/Engine/Admin/Settings/Page.php:430 views/settings/page.php:75
msgid "Rocket Analytics"
msgstr "Rocket 分析"

#. translators: %1$s = opening <a> tag, %2$s = closing </a> tag.
#: inc/Engine/Admin/Settings/Page.php:432
msgid ""
"I agree to share anonymous data with the development team to help improve WP"
" Rocket. %1$sWhat info will we collect?%2$s"
msgstr "我同意向开发团队分享匿名数据以帮助改进 WP Rocket。%1$s我们搜集何种信息？%2$s"

#: inc/Engine/Admin/Settings/Page.php:456 inc/Engine/Cache/WPCache.php:354
msgid "Cache"
msgstr "缓存"

#: inc/Engine/Admin/Settings/Page.php:457
msgid "Basic cache options"
msgstr "基础缓存选项"

#: inc/Engine/Admin/Settings/Page.php:464
msgid "Mobile Cache"
msgstr "移动端缓存"

#: inc/Engine/Admin/Settings/Page.php:466
msgid "Speed up your site for mobile visitors."
msgstr "为您网站的移动端访客加速。"

#: inc/Engine/Admin/Settings/Page.php:471
msgid ""
"We detected you use a plugin that requires a separate cache for mobile, and "
"automatically enabled this option for compatibility."
msgstr "我们监测到您正在使用其他的移动端缓存插件，并已自动启用该选项以确保兼容性。"

#: inc/Engine/Admin/Settings/Page.php:475
msgid "User Cache"
msgstr "用户端缓存"

#. translators: %1$s = opening <a> tag, %2$s = closing </a> tag.
#: inc/Engine/Admin/Settings/Page.php:478
msgid ""
"%1$sUser cache%2$s is great when you have user-specific or restricted "
"content on your website."
msgstr "%1$s用户端缓存%2$s适用于您的网站有些内容仅指定用户才能查看或有限制内容时。"

#: inc/Engine/Admin/Settings/Page.php:486
msgid "Cache Lifespan"
msgstr "缓存有效期"

#. translators: %1$s = opening <a> tag, %2$s = closing </a> tag.
#: inc/Engine/Admin/Settings/Page.php:489
msgid ""
"Cache files older than the specified lifespan will be deleted.<br>Enable "
"%1$spreloading%2$s for the cache to be rebuilt automatically after lifespan "
"expiration."
msgstr "指定生命周期外的缓存文件将被删除。<br>启用 %1$s预缓存%2$s 功能，缓存将在过期时自动重建。"

#: inc/Engine/Admin/Settings/Page.php:503
msgid "Enable caching for logged-in WordPress users"
msgstr "为已登录的 WordPress 用户启用缓存"

#: inc/Engine/Admin/Settings/Page.php:511
msgid "Enable caching for mobile devices"
msgstr "启用"

#: inc/Engine/Admin/Settings/Page.php:526
msgid "Separate cache files for mobile devices"
msgstr "移动端单独缓存"

#. translators: %1$s = opening <a> tag, %2$s = closing </a> tag.
#: inc/Engine/Admin/Settings/Page.php:528
msgid ""
"Most modern themes are responsive and should work without a separate cache. "
"Enable this only if you have a dedicated mobile theme or plugin. %1$sMore "
"info%2$s"
msgstr "现在大部分主题都是响应式的，并不需要单独缓存。只有当你正在使用移动端专用的主题或插件时才建议开启该功能。%1$s更多信息%2$s"

#: inc/Engine/Admin/Settings/Page.php:544
msgid ""
"Specify time after which the global cache is cleared<br>(0 = unlimited )"
msgstr "指定全局缓存刷新间隔<br>（0 = 无限制）"

#. translators: %1$s = opening <a> tag, %2$s = closing </a> tag.
#: inc/Engine/Admin/Settings/Page.php:546
msgid ""
"Reduce lifespan to 10 hours or less if you notice issues that seem to appear"
" periodically. %1$sWhy?%2$s"
msgstr "如您注意到某些问题周期性出现，请将时间间隔改为10小时或更短。%1$s为什么？%2$s"

#: inc/Engine/Admin/Settings/Page.php:552
#: inc/Engine/License/views/promo-banner.php:30
#: inc/Engine/License/views/renewal-soon-banner.php:13
msgid "Hours"
msgstr "小时"

#: inc/Engine/Admin/Settings/Page.php:553
#: inc/Engine/License/views/promo-banner.php:29
#: inc/Engine/License/views/renewal-soon-banner.php:12
msgid "Days"
msgstr "天"

#: inc/Engine/Admin/Settings/Page.php:602
msgid "File Optimization"
msgstr "文件优化"

#: inc/Engine/Admin/Settings/Page.php:603
msgid "Optimize CSS & JS"
msgstr "优化 CSS 和 JS"

#. translators: %1$s = type of minification (HTML, CSS or JS), %2$s = “WP
#. Rocket”.
#: inc/Engine/Admin/Settings/Page.php:611
#: inc/Engine/Admin/Settings/Page.php:637
msgid ""
"%1$s Minification is currently activated in <strong>Autoptimize</strong>. If"
" you want to use %2$s’s minification, disable those options in Autoptimize."
msgstr "<strong>自动优化</strong>中已启用%1$s压缩。 如您想使用 %2$s 的压缩功能，请禁用自动优化中的相关选项。"

#: inc/Engine/Admin/Settings/Page.php:621
msgid "CSS Files"
msgstr "CSS 文件"

#: inc/Engine/Admin/Settings/Page.php:630
msgid "JavaScript Files"
msgstr "JavaScript 文件"

#: inc/Engine/Admin/Settings/Page.php:642
msgid ""
"If you have problems after activating this option, copy and paste the "
"default exclusions to quickly resolve issues:"
msgstr "如果开启该选项后遇到问题， 可尝试复制粘贴默认的排除设置来快速解决。"

#. translators: %1$s = opening </a> tag, %2$s = closing </a> tag.
#: inc/Engine/Admin/Settings/Page.php:646
msgid ""
"Also, please check our %1$sdocumentation%2$s for a list of compatibility "
"exclusions."
msgstr "同时，请参考 %1$s参考文档%2$s中的兼容排除项列表。"

#: inc/Engine/Admin/Settings/Page.php:651
msgid ""
"Internal scripts are excluded by default to prevent issues. Remove them to "
"take full advantage of this option."
msgstr "默认已经排除了内部脚本已避免出错，您可视情况删除。"

#. translators: %1$s = opening </a> tag, %2$s = closing </a> tag.
#: inc/Engine/Admin/Settings/Page.php:654
msgid ""
"If this causes trouble, restore the default exclusions, found %1$shere%2$s"
msgstr "如果遇到问题， 请尝试还原至%1$s默认排除项%2$s"

#: inc/Engine/Admin/Settings/Page.php:663
msgid "Minify CSS files"
msgstr "压缩 CSS 文件"

#: inc/Engine/Admin/Settings/Page.php:664
msgid "Minify CSS removes whitespace and comments to reduce the file size."
msgstr "删除空格和注释以减小文件大小。"

#: inc/Engine/Admin/Settings/Page.php:677
#: inc/Engine/Admin/Settings/Page.php:701
#: inc/Engine/Admin/Settings/Page.php:765
#: inc/Engine/Admin/Settings/Page.php:831
#: inc/Engine/Admin/Settings/Page.php:855
msgid "This could break things!"
msgstr "可能导致页面出错！"

#: inc/Engine/Admin/Settings/Page.php:678
#: inc/Engine/Admin/Settings/Page.php:702
#: inc/Engine/Admin/Settings/Page.php:766
#: inc/Engine/Admin/Settings/Page.php:832
#: inc/Engine/Admin/Settings/Page.php:856
msgid ""
"If you notice any errors on your website after having activated this "
"setting, just deactivate it again, and your site will be back to normal."
msgstr "如启用该设置后网站出错，直接禁用即可恢复正常。"

#: inc/Engine/Admin/Settings/Page.php:679
msgid "Activate minify CSS"
msgstr "启用 CSS 压缩"

#: inc/Engine/Admin/Settings/Page.php:684
msgid "Combine CSS files <em>(Enable Minify CSS files to select)</em>"
msgstr "合并 CSS 文件<em>（需先勾选上方的\"压缩CSS文件\"）</em>"

#. translators: %1$s = opening <a> tag, %2$s = closing </a> tag.
#: inc/Engine/Admin/Settings/Page.php:686
msgid ""
"Combine CSS merges all your files into 1, reducing HTTP requests. Not "
"recommended if your site uses HTTP/2. %1$sMore info%2$s"
msgstr "将所有文件合并成一个文件以减少 HTTP 请求数。不推荐用于已使用 HTTP/2 的站点。%1$s更多信息%2$s"

#. translators: %1$s = opening <a> tag, %2$s = closing </a> tag.
#: inc/Engine/Admin/Settings/Page.php:687
msgid ""
"For compatibility and best results, this option is disabled when Remove "
"unused CSS is enabled."
msgstr "为确保兼容性和最佳效果, 当您启用了 \"移除未使用CSS\" 时, 当前选项将被禁用."

#: inc/Engine/Admin/Settings/Page.php:703
msgid "Activate combine CSS"
msgstr "启用 CSS 合并"

#: inc/Engine/Admin/Settings/Page.php:709
msgid ""
"Specify URLs of CSS files to be excluded from minification and concatenation"
" (one per line)."
msgstr "指定不启用压缩 & 合并的 CSS 文件 URL（每行一个）"

#: inc/Engine/Admin/Settings/Page.php:710
msgid ""
"<strong>Internal:</strong> The domain part of the URL will be stripped "
"automatically. Use (.*).css wildcards to exclude all CSS files located at a "
"specific path."
msgstr "<strong>内部: </strong> URL 的域名部分将被自动删除。可用 (.*).css 通配符来匹配所有相似文件。"

#. translators: %1$s = opening <a> tag, %2$s = closing </a> tag.
#: inc/Engine/Admin/Settings/Page.php:712
msgid ""
"<strong>3rd Party:</strong> Use either the full URL path or only the domain "
"name, to exclude external CSS. %1$sMore info%2$s"
msgstr "<strong>第三方：</strong>使用完整的 URL 路径或仅使用域名来排除外部 CSS。%1$s更多信息%2$s"

#: inc/Engine/Admin/Settings/Page.php:725
#: inc/Engine/Admin/Settings/Page.php:747
msgid "Optimize CSS delivery"
msgstr "CSS 递送优化"

#: inc/Engine/Admin/Settings/Page.php:730
msgid ""
"Optimize CSS delivery eliminates render-blocking CSS on your website. Only "
"one method can be selected. Remove Unused CSS is recommended for optimal "
"performance, but limited only to the users with active license."
msgstr "CSS 交付优化可以消除网站上的 CSS 渲染阻塞。只能选择一种方式哦。如您是授权用户，推荐选择 移除未使用 CSS。"

#: inc/Engine/Admin/Settings/Page.php:730
msgid ""
"Optimize CSS delivery eliminates render-blocking CSS on your website. Only "
"one method can be selected. Remove Unused CSS is recommended for optimal "
"performance."
msgstr "CSS递送优化可以去除网站中阻塞渲染的CSS. 只能选择一种方式. 建议移除未使用的CSS."

#. translators: %1$s = opening <a> tag, %2$s = closing </a> tag.
#: inc/Engine/Admin/Settings/Page.php:740
msgid ""
"Optimize CSS Delivery features are disabled on local environments. %1$sLearn"
" more%2$s"
msgstr "本地环境默认禁用 \"优化CSS递送\" 功能. %1$s更多信息%2$s"

#. translators: %1$s = opening <a> tag, %2$s = closing </a> tag.
#: inc/Engine/Admin/Settings/Page.php:763
msgid ""
"Removes unused CSS per page and helps to reduce page size and HTTP requests."
" Recommended for best performance. Test thoroughly! %1$sMore info%2$s"
msgstr "移除每个页面未使用的CSS, 减少页面大小和HTTP请求数. 建议使用以获得最佳性能. 请谨慎测试 ! %1$s更多信息%2$s"

#: inc/Engine/Admin/Settings/Page.php:767
msgid "Activate Remove Unused CSS"
msgstr "启用移除未使用的 CSS"

#: inc/Engine/Admin/Settings/Page.php:773
msgid "CSS safelist"
msgstr "CSS 安全列表"

#: inc/Engine/Admin/Settings/Page.php:774
msgid ""
"Specify CSS filenames, IDs or classes that should not be removed (one per "
"line)."
msgstr "指定无需移除的CSS文件名、ID 或 class 值（每行一个）。"

#. translators: %1$s = plugin name.
#: inc/Engine/Admin/Settings/Page.php:792
msgctxt "WP Critical CSS compatibility"
msgid ""
"Load CSS asynchronously is currently handled by the %1$s plugin. If you want"
" to use WP Rocket’s load CSS asynchronously option, disable the %1$s plugin."
msgstr " CSS 异步加载当前由%1$s扩展接管。如您想使用 WP Rocket 的 CSS 异步加载选项，请禁用%1$s扩展。"

#. translators: %1$s = opening <a> tag, %2$s = closing </a> tag.
#: inc/Engine/Admin/Settings/Page.php:794
msgid ""
"Generates critical path CSS and loads CSS asynchronously. %1$sMore info%2$s"
msgstr "生成关键路径 CSS 并异步加载.%1$s更多信息%2$s"

#: inc/Engine/Admin/Settings/Page.php:800
msgid "Fallback critical CSS"
msgstr "关键路径 CSS 备用代码"

#. translators: %1$s = opening <a> tag, %2$s = closing </a> tag.
#: inc/Engine/Admin/Settings/Page.php:802
msgid ""
"Provides a fallback if auto-generated critical path CSS is incomplete. "
"%1$sMore info%2$s"
msgstr "输入备用代码，以供自动生成关键路径 CSS 操作无法完成时使用。%1$s更多信息%2$s"

#: inc/Engine/Admin/Settings/Page.php:817
msgid "Minify JavaScript files"
msgstr "压缩 JS 文件"

#: inc/Engine/Admin/Settings/Page.php:818
msgid ""
"Minify JavaScript removes whitespace and comments to reduce the file size."
msgstr "移除空白字符和注释以减小文件体积。"

#: inc/Engine/Admin/Settings/Page.php:833
msgid "Activate minify JavaScript"
msgstr "启用 JavaScript 压缩"

#: inc/Engine/Admin/Settings/Page.php:838
msgid ""
"Combine JavaScript files <em>(Enable Minify JavaScript files to select)</em>"
msgstr "合并 JavaScript 文件<em>（需先勾选上方的\"压缩JS文件\"）</em>"

#. translators: %1$s = opening <a> tag, %2$s = closing </a> tag.
#: inc/Engine/Admin/Settings/Page.php:840
msgid ""
"Combine JavaScript files combines your site’s internal, 3rd party and inline"
" JS reducing HTTP requests. Not recommended if your site uses HTTP/2. "
"%1$sMore info%2$s"
msgstr "合并您网站内的内部、第三方以及内联 JS 文件，以减少 HTTP 请求数。不推荐用于已使用 HTTP/2 的站点。%1$s更多信息%2$s"

#. translators: %1$s = opening <a> tag, %2$s = closing </a> tag.
#: inc/Engine/Admin/Settings/Page.php:841
msgid ""
"For compatibility and best results, this option is disabled when delay "
"javascript execution is enabled."
msgstr "考虑到兼容性和最佳效果，如您已开启延迟执行 Javascript，该选项会被禁用。"

#: inc/Engine/Admin/Settings/Page.php:857
msgid "Activate combine JavaScript"
msgstr "启用 JavaScript 合并"

#. translators: %1$s = opening <a> tag, %2$s = closing </a> tag.
#: inc/Engine/Admin/Settings/Page.php:864
msgid ""
"Specify patterns of inline JavaScript to be excluded from concatenation (one"
" per line). %1$sMore info%2$s"
msgstr "指定不启用压缩 & 合并的 JS 文件 URL（每行一个）%1$s更多信息%2$s"

#: inc/Engine/Admin/Settings/Page.php:881
msgid ""
"Specify URLs of JavaScript files to be excluded from minification and "
"concatenation (one per line)."
msgstr "指定不启用压缩 & 合并的 JS 文件 URL（每行一个）"

#: inc/Engine/Admin/Settings/Page.php:882
msgid ""
"<strong>Internal:</strong> The domain part of the URL will be stripped "
"automatically. Use (.*).js wildcards to exclude all JS files located at a "
"specific path."
msgstr "<strong>内部: </strong> URL 的域名部分将被自动删除。可用 (.*).js 通配符来匹配所有相似文件。"

#. translators: %1$s = opening <a> tag, %2$s = closing </a> tag.
#: inc/Engine/Admin/Settings/Page.php:884
msgid ""
"<strong>3rd Party:</strong> Use either the full URL path or only the domain "
"name, to exclude external JS. %1$sMore info%2$s"
msgstr "<strong>第三方：</strong>使用完整的 URL 路径或仅使用域名来排除外部 JS。%1$s更多信息%2$s"

#: inc/Engine/Admin/Settings/Page.php:900
msgid "Load JavaScript deferred"
msgstr "JavaScript 异步加载"

#. translators: %1$s = opening <a> tag, %2$s = closing </a> tag.
#: inc/Engine/Admin/Settings/Page.php:902
msgid ""
"Load JavaScript deferred eliminates render-blocking JS on your site and can "
"improve load time. %1$sMore info%2$s"
msgstr "可以消除您网站阻塞渲染的 JS 以改善加载时间。%1$s更多信息%2$s"

#. translators: %1$s = opening <a> tag, %2$s = closing </a> tag.
#: inc/Engine/Admin/Settings/Page.php:915
msgid ""
"Specify URLs or keywords of JavaScript files to be excluded from defer (one "
"per line). %1$sMore info%2$s"
msgstr "指定无需延迟的 URL 或 JavaScript 文件的关键词（每行一个）。%1$s更多信息%2$s"

#. translators: %1$s = opening <a> tag, %2$s = closing </a> tag.
#: inc/Engine/Admin/Settings/Page.php:933
msgid ""
"Improves performance by delaying the loading of JavaScript files until user "
"interaction (e.g. scroll, click). %1$sMore info%2$s"
msgstr ""
"在页面加载时延迟加载JaveScript文件, 只在用户需要时（如滚动、点击）才加载这些非关键资源，可以优化网页性能。%1$s更多信息%2$s"

#: inc/Engine/Admin/Settings/Page.php:942
msgid "One-click exclusions"
msgstr "一键排除"

#: inc/Engine/Admin/Settings/Page.php:943
msgid ""
"When using the Delay JavaScript Execution, you might experience delay "
"loading elements located in the viewport that need to appear immediately - "
"e.g. slider, header, menu."
msgstr "当使用 JavaScript 延迟执行功能时, 视觉窗口内有些需要即时显示的元素（如幻灯片、页眉、菜单）可能会被延迟加载。"

#: inc/Engine/Admin/Settings/Page.php:944
msgid ""
"If you need instant visibility, click below on files that should NOT be "
"delayed. This selection will help users interact with the elements straight "
"away."
msgstr "如您需要保持即时可见，请点选下方要排除的文件。"

#: inc/Engine/Admin/Settings/Page.php:961
msgid ""
"Specify URLs or keywords that can identify inline or JavaScript files to be "
"excluded from delaying execution (one per line)."
msgstr "指定用于识别内联或 JavaScript 文件的 URL 或关键词（每行一个）。"

#: inc/Engine/Admin/Settings/Page.php:993
msgid "Media"
msgstr "媒体"

#: inc/Engine/Admin/Settings/Page.php:994
msgid "LazyLoad, image dimensions"
msgstr "懒加载, 图片尺寸"

#: inc/Engine/Admin/Settings/Page.php:1003
msgid "Autoptimize"
msgstr "自动优化"

#: inc/Engine/Admin/Settings/Page.php:1048
msgid "LazyLoad"
msgstr "懒加载"

#. translators: %1$s = opening <a> tag, %2$s = closing </a> tag.
#: inc/Engine/Admin/Settings/Page.php:1051
msgid ""
"It can improve actual and perceived loading time as images, iframes, and "
"videos will be loaded only as they enter (or about to enter) the viewport "
"and reduces the number of HTTP requests. %1$sMore Info%2$s"
msgstr ""
"它可帮您改善加载时间（例如: 图片, 内嵌框架和视频将仅在进入或即将进入可视区域时才加载）和减少 HTTP 请求数.%1$s更多信息%2$s"

#. translators: %1$s = “WP Rocket”, %2$s = a list of plugin names.
#: inc/Engine/Admin/Settings/Page.php:1058
msgid ""
"LazyLoad is currently activated in %2$s. If you want to use WP Rocket’s "
"LazyLoad, disable this option in %2$s."
msgstr "延迟加载已在 %2$s 中启用。若您想要使用 WP Rocket 的延迟加载功能，请先在 %2$s 中禁用此选项。"

#: inc/Engine/Admin/Settings/Page.php:1061
msgid "Image Dimensions"
msgstr "图片尺寸"

#. translators: %1$s = opening <a> tag, %2$s = closing </a> tag.
#: inc/Engine/Admin/Settings/Page.php:1064
msgid ""
"Add missing width and height attributes to images. Helps prevent layout "
"shifts and improve the reading experience for your visitors. %1$sMore "
"info%2$s"
msgstr "为图片添加缺失的宽高属性。这有助于防止布局偏移，并能提升访客阅读体验。 %1$s更多信息%2$s"

#: inc/Engine/Admin/Settings/Page.php:1083
msgid "Enable for images"
msgstr "为图片开启"

#. translators: %1$s = “WP Rocket”, %2$s = a list of plugin names.
#: inc/Engine/Admin/Settings/Page.php:1095
msgid ""
"LazyLoad for images is currently activated in %2$s. If you want to use "
"%1$s’s LazyLoad, disable this option in %2$s."
msgstr "延迟加载图像已在 %2$s 中启用。若您想要使用 %1$s 的延迟加载功能，请先在 %2$s 中禁用此选项。"

#: inc/Engine/Admin/Settings/Page.php:1103
msgid "Enable for iframes and videos"
msgstr "框架 & 视频"

#: inc/Engine/Admin/Settings/Page.php:1118
msgid "Replace YouTube iframe with preview image"
msgstr "用预览图替换 Youtube 框架"

#. translators: %1$s = “WP Rocket”, %2$s = a list of plugin or themes names.
#: inc/Engine/Admin/Settings/Page.php:1120
msgid "Replace YouTube iframe with preview image is not compatible with %2$s."
msgstr "用预览图替换Youtube 框架 不兼容于%2$s"

#. translators: %1$s = “WP Rocket”, %2$s = a list of plugin or themes names.
#: inc/Engine/Admin/Settings/Page.php:1120
msgid ""
"This can significantly improve your loading time if you have a lot of "
"YouTube videos on a page."
msgstr "如页面含有多个 Youtube 视频，可以极大改善页面加载时间。"

#: inc/Engine/Admin/Settings/Page.php:1135
msgid "Excluded images or iframes"
msgstr "排除图片或 iframe 框架"

#. translators: %1$s = opening <a> tag, %2$s = closing </a> tag.
#: inc/Engine/Admin/Settings/Page.php:1137
msgid ""
"Specify keywords (e.g. image filename, CSS class, domain) from the image or "
"iframe code to be excluded (one per line). %1$sMore info%2$s"
msgstr "指定要排除的图片或 iframe 框架代码的关键词（如图片文件名、CSS类、域名），每行一个。 %1$s更多信息%2$s"

#: inc/Engine/Admin/Settings/Page.php:1145
msgid "Add missing image dimensions"
msgstr "添加缺失的图像宽高属性"

#: inc/Engine/Admin/Settings/Page.php:1165
msgid "Generate cache files, preload fonts"
msgstr "生成缓存文件、预加载字体"

#: inc/Engine/Admin/Settings/Page.php:1177
msgid "Preload Cache"
msgstr "预加载缓存"

#. translators: %1$s = opening <a> tag, %2$s = closing </a> tag.
#: inc/Engine/Admin/Settings/Page.php:1180
msgid ""
"When you enable preloading WP Rocket will automatically detect your sitemaps"
" and save all URLs to the database. The plugin will make sure that your "
"cache is always preloaded."
msgstr "当您启用预加载功能时，WP Rocket 将自动检测您的站点地图并将所有 URL 保存到数据库中。该插件将确保您的缓存始终被预先加载。"

#: inc/Engine/Admin/Settings/Page.php:1188
msgid "Preload Links"
msgstr "链接预取"

#. translators: %1$s = opening <a> tag, %2$s = closing </a> tag.
#: inc/Engine/Admin/Settings/Page.php:1191
msgid ""
"Link preloading improves the perceived load time by downloading a page when "
"a user hovers over the link. %1$sMore info%2$s"
msgstr "链接预取是指当用户的鼠标经过某个链接时，预先下载对应网页，所以当用户点击链接时会觉得加载速度非常快。%1$s更多信息%2$s"

#: inc/Engine/Admin/Settings/Page.php:1199
msgid "Prefetch DNS Requests"
msgstr "DNS 预读取"

#: inc/Engine/Admin/Settings/Page.php:1201
msgid ""
"DNS prefetching can make external files load faster, especially on mobile "
"networks"
msgstr "DNS 预读取能更快地加载外部文件，特别是移动网络"

#: inc/Engine/Admin/Settings/Page.php:1206
msgid "Preload Fonts"
msgstr "预加载字体"

#. translators: %1$s = opening <a> tag, %2$s = closing </a> tag.
#: inc/Engine/Admin/Settings/Page.php:1209
msgid ""
"Improves performance by helping browsers discover fonts in CSS files. "
"%1$sMore info%2$s"
msgstr "帮助浏览器在 CSS 文件中发现字体来提高性能。%1$s了解详情%2$s"

#: inc/Engine/Admin/Settings/Page.php:1223
msgid "Activate Preloading"
msgstr "开启预缓存"

#: inc/Engine/Admin/Settings/Page.php:1234
msgid "Exclude URLs"
msgstr "排除的URL"

#. translators: %1$s = opening <a> tag, %2$s = closing </a> tag.
#: inc/Engine/Admin/Settings/Page.php:1239
msgid ""
"Specify URLs to be excluded from the preload feature (one per line). "
"%1$sMore info%2$s"
msgstr "指定要排除的URL（每行一个）。%1$s更多信息%2$s"

#: inc/Engine/Admin/Settings/Page.php:1250
msgid "URLs to prefetch"
msgstr "预读取的URL"

#: inc/Engine/Admin/Settings/Page.php:1251
msgid ""
"Specify external hosts to be prefetched (no <code>http:</code>, one per "
"line)"
msgstr "指定要预读取的外部主机名（不带 <code>http:</code>，每行一个）"

#: inc/Engine/Admin/Settings/Page.php:1260
msgid "Fonts to preload"
msgstr "要预加载的字体"

#: inc/Engine/Admin/Settings/Page.php:1261
msgid ""
"Specify urls of the font files to be preloaded (one per line). Fonts must be"
" hosted on your own domain, or the domain you have specified on the CDN tab."
msgstr "指定要预加载的字体文件的URL（每行一个）。字体必须托管在您自己的域名中，或者在CDN标签页中指定这个域名。"

#: inc/Engine/Admin/Settings/Page.php:1262
msgid ""
"The domain part of the URL will be stripped automatically.<br/>Allowed font "
"extensions: otf, ttf, svg, woff, woff2."
msgstr "URL的域名部分将被自动删除。<br/>允许的字体扩展为：otf，ttf，svg，woff，woff2。"

#: inc/Engine/Admin/Settings/Page.php:1271
msgid "Enable link preloading"
msgstr "启用链接预取"

#: inc/Engine/Admin/Settings/Page.php:1290
msgid "Advanced Rules"
msgstr "高级规则"

#: inc/Engine/Admin/Settings/Page.php:1291
msgid "Fine-tune cache rules"
msgstr "微调缓存规则"

#: inc/Engine/Admin/Settings/Page.php:1302
msgid ""
"Sensitive pages like custom login/logout URLs should be excluded from cache."
msgstr "某些特殊页面不建议缓存，如自定义的登录/注销URL。"

#: inc/Engine/Admin/Settings/Page.php:1305
msgctxt "plugin name"
msgid "WooCommerce"
msgstr "WooCommerce"

#: inc/Engine/Admin/Settings/Page.php:1307
msgctxt "plugin name"
msgid "Easy Digital Downloads"
msgstr "简易数字下载"

#: inc/Engine/Admin/Settings/Page.php:1309
msgctxt "plugin name"
msgid "iThemes Exchange"
msgstr "iThemes 交易"

#: inc/Engine/Admin/Settings/Page.php:1311
msgctxt "plugin name"
msgid "Jigoshop"
msgstr "Jigoshop"

#: inc/Engine/Admin/Settings/Page.php:1313
msgctxt "plugin name"
msgid "WP-Shop"
msgstr "WP-Shop"

#. translators: %1$s = opening <a> tag, %2$s = plugin name, %3$s closing </a>
#. tag.
#: inc/Engine/Admin/Settings/Page.php:1319
msgid ""
"<br>Cart, checkout and \"my account\" pages set in "
"<strong>%1$s%2$s%3$s</strong> will be detected and never cached by default."
msgstr "<br> 默认将自动监测并永不缓存<strong>%1$s%2$s%3$s</strong>中设置的购物车、收银台和”我的账户“页面。"

#: inc/Engine/Admin/Settings/Page.php:1337
msgid "Never Cache Cookies"
msgstr "永不缓存（Cookies）"

#: inc/Engine/Admin/Settings/Page.php:1355
msgid "Cache Query String(s)"
msgstr "缓存查询字符串："

#. translators: %1$s = opening <a> tag, %2$s = closing </a> tag.
#: inc/Engine/Admin/Settings/Page.php:1358
msgid ""
"%1$sCache for query strings%2$s enables you to force caching for specific "
"GET parameters."
msgstr "%1$s查询字符串缓存%2$s 可强制缓存指定GET参数。"

#: inc/Engine/Admin/Settings/Page.php:1369
msgid ""
"Specify URLs of pages or posts that should never be cached (one per line)"
msgstr "指定永不缓存的页面或文章URL (每行一个)"

#: inc/Engine/Admin/Settings/Page.php:1370
#: inc/Engine/Admin/Settings/Page.php:1398
msgid ""
"The domain part of the URL will be stripped automatically.<br>Use (.*) "
"wildcards to address multiple URLs under a given path."
msgstr "URL的域名部分将被自动删除。<br>可使用 (.*) 通配符来匹配多个相似URL。"

#: inc/Engine/Admin/Settings/Page.php:1379
msgid ""
"Specify full or partial IDs of cookies that, when set in the visitor's "
"browser, should prevent a page from getting cached (one per line)"
msgstr "指定部分或完整的Cookies ID（每行一个），用于阻止访客浏览器缓存页面"

#: inc/Engine/Admin/Settings/Page.php:1387
msgid ""
"Specify user agent strings that should never see cached pages (one per line)"
msgstr "指定永不缓存的 user agent 字符串（每行一个）"

#: inc/Engine/Admin/Settings/Page.php:1388
msgid "Use (.*) wildcards to detect parts of UA strings."
msgstr "可用 (.*) 通配符来匹配部分 UA 字符串。"

#: inc/Engine/Admin/Settings/Page.php:1397
msgid ""
"Specify URLs you always want purged from cache whenever you update any post "
"or page (one per line)"
msgstr "指定当更新任何文章或页面时，要清除缓存的 URL（每行一个）"

#: inc/Engine/Admin/Settings/Page.php:1406
msgid "Specify query strings for caching (one per line)"
msgstr "指定要缓存的查询字符串（每行一个）"

#: inc/Engine/Admin/Settings/Page.php:1432
msgid "Optimize, reduce bloat"
msgstr "优化，减少冗余"

#: inc/Engine/Admin/Settings/Page.php:1439
msgid "Post Cleanup"
msgstr "文章清理"

#: inc/Engine/Admin/Settings/Page.php:1441
msgid ""
"Post revisions and drafts will be permanently deleted. Do not use this "
"option if you need to retain revisions or drafts."
msgstr "永久删除文章的修订版本和草稿。如您需要保留修订版本或草稿， 请勿使用该选项。"

#: inc/Engine/Admin/Settings/Page.php:1446
msgid "Comments Cleanup"
msgstr "评论清理"

#: inc/Engine/Admin/Settings/Page.php:1448
msgid "Spam and trashed comments will be permanently deleted."
msgstr "永久删除垃圾评论和回收站评论。"

#: inc/Engine/Admin/Settings/Page.php:1452
msgid "Transients Cleanup"
msgstr "临时数据清理"

#: inc/Engine/Admin/Settings/Page.php:1454
msgid ""
"Transients are temporary options; they are safe to remove. They will be "
"automatically regenerated as your plugins require them."
msgstr "可安全清除。插件需要时将会自动重新生成。"

#: inc/Engine/Admin/Settings/Page.php:1458
msgid "Database Cleanup"
msgstr "数据库清理"

#: inc/Engine/Admin/Settings/Page.php:1460
msgid "Reduces overhead of database tables"
msgstr "优化数据库表"

#: inc/Engine/Admin/Settings/Page.php:1464
msgid "Automatic Cleanup"
msgstr "自动清理"

#. translators: %s is the number of revisions found in the database. It's a
#. formatted number, don't use %d.
#: inc/Engine/Admin/Settings/Page.php:1477
msgid "%s revision in your database."
msgid_plural "%s revisions in your database."
msgstr[0] "检测到 %s 个修订版本。"

#. translators: %s is the number of revisions found in the database. It's a
#. formatted number, don't use %d.
#: inc/Engine/Admin/Settings/Page.php:1487
msgid "%s draft in your database."
msgid_plural "%s drafts in your database."
msgstr[0] "检测到 %s 篇草稿。"

#. translators: %s is the number of revisions found in the database. It's a
#. formatted number, don't use %d.
#: inc/Engine/Admin/Settings/Page.php:1497
msgid "%s trashed post in your database."
msgid_plural "%s trashed posts in your database."
msgstr[0] "检测到 %s 篇回收站文章。"

#. translators: %s is the number of revisions found in the database. It's a
#. formatted number, don't use %d.
#: inc/Engine/Admin/Settings/Page.php:1507
msgid "%s spam comment in your database."
msgid_plural "%s spam comments in your database."
msgstr[0] "检测到 %s 条垃圾评论。"

#. translators: %s is the number of revisions found in the database. It's a
#. formatted number, don't use %d.
#: inc/Engine/Admin/Settings/Page.php:1517
msgid "%s trashed comment in your database."
msgid_plural "%s trashed comments in your database."
msgstr[0] "检测到 %s 条回收站评论。"

#: inc/Engine/Admin/Settings/Page.php:1525
msgid "All transients"
msgstr "所有临时数据"

#. translators: %s is the number of revisions found in the database. It's a
#. formatted number, don't use %d.
#: inc/Engine/Admin/Settings/Page.php:1527
msgid "%s transient in your database."
msgid_plural "%s transients in your database."
msgstr[0] "检测到 %s 条过期的临时数据。"

#: inc/Engine/Admin/Settings/Page.php:1535
msgid "Optimize Tables"
msgstr "优化数据表"

#. translators: %s is the number of revisions found in the database. It's a
#. formatted number, don't use %d.
#: inc/Engine/Admin/Settings/Page.php:1537
msgid "%s table to optimize in your database."
msgid_plural "%s tables to optimize in your database."
msgstr[0] "检测到 %s 张表可被优化。"

#: inc/Engine/Admin/Settings/Page.php:1548
msgid "Schedule Automatic Cleanup"
msgstr "自动清理计划任务"

#: inc/Engine/Admin/Settings/Page.php:1560
msgid "Frequency"
msgstr "频率"

#: inc/Engine/Admin/Settings/Page.php:1568
msgid "Daily"
msgstr "每日"

#: inc/Engine/Admin/Settings/Page.php:1569
msgid "Weekly"
msgstr "每周"

#: inc/Engine/Admin/Settings/Page.php:1570
msgid "Monthly"
msgstr "每月"

#: inc/Engine/Admin/Settings/Page.php:1587
msgid "Integrate your CDN"
msgstr "CDN整合"

#: inc/Engine/Admin/Settings/Page.php:1599
msgid ""
"All URLs of static files (CSS, JS, images) will be rewritten to the CNAME(s)"
" you provide."
msgstr "所有静态文件 URL（CSS、JS和图片）将被重写至下方您提供的 CNAME。"

#. translators: %1$s = opening link tag, %2$s = closing link tag.
#: inc/Engine/Admin/Settings/Page.php:1601
msgid ""
"Not required for services like Cloudflare and Sucuri. Please see our "
"available %1$sAdd-ons%2$s."
msgstr "Cloudflare 和 Sucuri 等服务不需要。请参考我们的可用%1$s扩展%2$s。"

#. translators: %1$s = opening em tag, %2$l = list of add-on name(s), %3$s =
#. closing em tag.
#: inc/Engine/Admin/Settings/Page.php:1647
msgid ""
"%1$s%2$l Add-on%3$s is currently enabled. Configuration of the CDN settings "
"is not required for %2$l to work on your site."
msgid_plural ""
"%1$s%2$l Add-ons%3$s are currently enabled. Configuration of the CDN "
"settings is not required for %2$l to work on your site."
msgstr[0] "%1$s%2$s 扩展%3$s已启用。CDN设置对于%2$s正常工作来说不是必须的哦。"

#: inc/Engine/Admin/Settings/Page.php:1673
msgid "Enable Content Delivery Network"
msgstr "启用CDN"

#: inc/Engine/Admin/Settings/Page.php:1682
#: inc/Engine/CDN/RocketCDN/AdminPageSubscriber.php:159
msgid "CDN CNAME(s)"
msgstr "CDN CNAME："

#: inc/Engine/Admin/Settings/Page.php:1683
#: inc/Engine/CDN/RocketCDN/AdminPageSubscriber.php:160
msgid "Specify the CNAME(s) below"
msgstr "指定 CNAME"

#: inc/Engine/Admin/Settings/Page.php:1690
msgid ""
"Specify URL(s) of files that should not get served via CDN (one per line)."
msgstr "指定不启用 CDN 的文件 URL（每行一个）"

#: inc/Engine/Admin/Settings/Page.php:1691
msgid ""
"The domain part of the URL will be stripped automatically.<br>Use (.*) "
"wildcards to exclude all files of a given file type located at a specific "
"path."
msgstr "URL 的域名部分将被自动删除。<br>可用 (.*) 通配符来匹配所有相似文件。"

#: inc/Engine/Admin/Settings/Page.php:1714
#: inc/Engine/Admin/Settings/Page.php:1722
msgid "Heartbeat"
msgstr "心跳监测"

#: inc/Engine/Admin/Settings/Page.php:1715
msgid "Control WordPress Heartbeat API"
msgstr "控制 WordPress 心跳监测的 API"

#: inc/Engine/Admin/Settings/Page.php:1723
msgid ""
"Reducing or disabling the Heartbeat API’s activity can help save some of "
"your server’s resources."
msgstr "降低其频率或禁用 API 可帮您节省部分服务器资源。"

#: inc/Engine/Admin/Settings/Page.php:1729
msgid "Reduce or disable Heartbeat activity"
msgstr "减少心跳监测频率或禁用心跳监测"

#: inc/Engine/Admin/Settings/Page.php:1730
msgid ""
"Reducing activity will change Heartbeat frequency from one hit each minute "
"to one hit every 2 minutes."
msgstr "减少活动：将心跳监测频率从每分钟一次改为每2分钟一次。"

#: inc/Engine/Admin/Settings/Page.php:1730
msgid ""
"Disabling Heartbeat entirely may break plugins and themes using this API."
msgstr "完全禁用：可能会破坏使用此 API 的插件和主题。"

#: inc/Engine/Admin/Settings/Page.php:1744
msgid "Do not limit"
msgstr "不限制"

#: inc/Engine/Admin/Settings/Page.php:1745
msgid "Reduce activity"
msgstr "减少活动"

#: inc/Engine/Admin/Settings/Page.php:1746
msgid "Disable"
msgstr "禁用"

#: inc/Engine/Admin/Settings/Page.php:1754
msgid "Control Heartbeat"
msgstr "心跳控制"

#: inc/Engine/Admin/Settings/Page.php:1763
msgid "Behavior in backend"
msgstr "后端行为监测"

#: inc/Engine/Admin/Settings/Page.php:1770
msgid "Behavior in post editor"
msgstr "文章编辑器行为监测"

#: inc/Engine/Admin/Settings/Page.php:1776
msgid "Behavior in frontend"
msgstr "前端行为监测"

#: inc/Engine/Admin/Settings/Page.php:1793
#: views/settings/page-sections/tutorials.php:39
msgid "Add-ons"
msgstr "扩展功能"

#: inc/Engine/Admin/Settings/Page.php:1794
msgid "Add more features"
msgstr "提供更多设置"

#: inc/Engine/Admin/Settings/Page.php:1801
msgid "One-click Rocket Add-ons"
msgstr "Rocket 一键扩展功能"

#: inc/Engine/Admin/Settings/Page.php:1802
msgid ""
"One-Click Add-ons are features extending available options without "
"configuration needed. Switch the option \"on\" to enable from this screen."
msgstr "额外的功能选项。设为 “on” 即可启用，无需另外配置。"

#: inc/Engine/Admin/Settings/Page.php:1812
msgid "Rocket Add-ons"
msgstr "Rocket 扩展功能"

#: inc/Engine/Admin/Settings/Page.php:1813
msgid "Rocket Add-ons are complementary features extending available options."
msgstr "现有功能的补充。"

#: inc/Engine/Admin/Settings/Page.php:1823
#: inc/Engine/Admin/Settings/Page.php:1992
msgid "Cloudflare"
msgstr "Cloudflare"

#: inc/Engine/Admin/Settings/Page.php:1829
msgid "Integrate your Cloudflare account with this add-on."
msgstr "使用该扩展整合您的Cloudflare账户。"

#: inc/Engine/Admin/Settings/Page.php:1830
msgid ""
"Provide your account email, global API key, and domain to use options such "
"as clearing the Cloudflare cache and enabling optimal settings with WP "
"Rocket."
msgstr "提供您的账户邮箱、全局 API key 和域名以开启相关优化选项并使用清除 Cloudflare 缓存等功能。"

#. translators: %1$s = opening span tag, %2$s = closing span tag.
#: inc/Engine/Admin/Settings/Page.php:1833
msgid ""
"%1$sPlanning on using Automatic Platform Optimization (APO)?%2$s Just "
"activate the official Cloudflare plugin and configure it. WP Rocket will "
"automatically enable compatibility."
msgstr ""
"%1$s准备好使用 自动平台优化（APO）功能了吗？%2$s 您只需启用 Cloudflare 官方插件并配置即可。WP Rocket 将自动为您优化。"

#: inc/Engine/Admin/Settings/Page.php:1881
msgid "Varnish"
msgstr "Varnish"

#: inc/Engine/Admin/Settings/Page.php:1887
msgid "If Varnish runs on your server, you must activate this add-on."
msgstr "如服务器已启用 Varnish，该功能必须开启。"

#. translators: %1$s = opening <a> tag, %2$s = closing </a> tag.
#: inc/Engine/Admin/Settings/Page.php:1889
msgid ""
"Varnish cache will be purged each time WP Rocket clears its cache to ensure "
"content is always up-to-date.<br>%1$sLearn more%2$s"
msgstr "当 WP Rocket 每次清除缓存时，自动清除 Varnish 缓存以使内容保持最新。<br>%1$s更多信息%2$s"

#: inc/Engine/Admin/Settings/Page.php:1924
msgid "WebP Compatibility"
msgstr "WebP 兼容"

#: inc/Engine/Admin/Settings/Page.php:1930
msgid "Improve browser compatibility for WebP images."
msgstr "让浏览器兼容WebP图片格式."

#. translators: %1$s and %3$s = opening <a> tag, %2$s = closing </a> tag.
#: inc/Engine/Admin/Settings/Page.php:1934
msgid ""
"Enable this option if you would like WP Rocket to serve WebP images to "
"compatible browsers. Please note that WP Rocket cannot create WebP images "
"for you. To create WebP images we recommend %1$sImagify%2$s. %3$sMore "
"info%2$s"
msgstr ""
"如您想使用 WP Rocket 来增加 WebP 图片支持，可开启此选项。请注意 WP Rocket 无法为您 创建 WebP 图片。要创建 WebP "
"图片 我们推荐使用 %1$sImagify%2$s 。%3$s更多信息%2$s"

#: inc/Engine/Admin/Settings/Page.php:1954
msgid "Clear the Sucuri cache when WP Rocket’s cache is cleared."
msgstr "在 WP Rocket 的缓存被清理时，清理 Sucuri 缓存。"

#: inc/Engine/Admin/Settings/Page.php:1957
msgid ""
"Provide your API key to clear the Sucuri cache when WP Rocket’s cache is "
"cleared."
msgstr "提供 Sucuri 的 API Key 以便用于当 WP Rocket 的缓存被清理时, 清理 Sucuri 缓存。"

#: inc/Engine/Admin/Settings/Page.php:1965
#: inc/Engine/Admin/Settings/Page.php:2109
msgid "Sucuri"
msgstr "Sucuri ( 一家CDN服务商 )"

#: inc/Engine/Admin/Settings/Page.php:1971
msgid "Synchronize Sucuri cache with this add-on."
msgstr "使用此扩展同步 Sucuri 缓存。"

#: inc/Engine/Admin/Settings/Page.php:2009
msgid "Cloudflare credentials"
msgstr "Cloudflare授权"

#: inc/Engine/Admin/Settings/Page.php:2018
msgid "Cloudflare settings"
msgstr "Cloudflare设置"

#: inc/Engine/Admin/Settings/Page.php:2032
msgctxt "Cloudflare"
msgid "Global API key:"
msgstr "全局 API key："

#: inc/Engine/Admin/Settings/Page.php:2033
msgctxt "Cloudflare"
msgid "Find your API key"
msgstr "查找我的 API Key"

#: inc/Engine/Admin/Settings/Page.php:2045
msgctxt "Cloudflare"
msgid "Account email"
msgstr "账户邮箱"

#: inc/Engine/Admin/Settings/Page.php:2054
msgctxt "Cloudflare"
msgid "Zone ID"
msgstr "Zone ID"

#: inc/Engine/Admin/Settings/Page.php:2064
msgid "Development mode"
msgstr "开发者模式"

#. translators: %1$s = link opening tag, %2$s = link closing tag.
#: inc/Engine/Admin/Settings/Page.php:2066
msgid ""
"Temporarily activate development mode on your website. This setting will "
"automatically turn off after 3 hours. %1$sLearn more%2$s"
msgstr "暂时在您的网站上激活开发模式。 该设置将在3小时后自动关闭。%1$s更多%2$s"

#: inc/Engine/Admin/Settings/Page.php:2074
msgid "Optimal settings"
msgstr "优化设置"

#: inc/Engine/Admin/Settings/Page.php:2075
msgid ""
"Automatically enhances your Cloudflare configuration for speed, performance "
"grade and compatibility."
msgstr "自动优化 Cloudflare 设置（基于速度，性能等级和兼容性）。"

#: inc/Engine/Admin/Settings/Page.php:2083
msgid "Relative protocol"
msgstr "相对协议"

#: inc/Engine/Admin/Settings/Page.php:2084
msgid ""
"Should only be used with Cloudflare's flexible SSL feature. URLs of static "
"files (CSS, JS, images) will be rewritten to use // instead of http:// or "
"https://."
msgstr ""
"仅适用于 Cloudflare的 flexible SSL 功能。静态文件（CSS, JS和图片）URL的 http:// 或 https:// "
"将被重写为 // 。"

#: inc/Engine/Admin/Settings/Page.php:2122
msgid "Sucuri credentials"
msgstr "Sucuri 凭证"

#: inc/Engine/Admin/Settings/Page.php:2135
msgctxt "Sucuri"
msgid ""
"Firewall API key (for plugin), must be in format {32 characters}/{32 "
"characters}:"
msgstr "防火墙 API Key (插件用) 的格式必须是 {32个字符}/{32个字符}:"

#: inc/Engine/Admin/Settings/Page.php:2136
msgctxt "Sucuri"
msgid "Find your API key"
msgstr "查找我的 API Key"

#: inc/Engine/Admin/Settings/Settings.php:362
msgid ""
"Sucuri Add-on: The API key for the Sucuri firewall must be in format "
"<code>{32 characters}/{32 characters}</code>."
msgstr "Sucuri 扩展: Sucuri 防火墙的 API key 长度应为<code>{32个字符}/{32个字符}</code>."

#: inc/Engine/Admin/Settings/Settings.php:668
msgid ""
"Sorry! Adding /(.*) in Advanced Rules > Never Cache URL(s) was not saved "
"because it disables caching and optimizations for every page on your site."
msgstr "您在 \"高级规则 > 从不缓存 URL\" 中添加的 /(.*) 未被保存, 因为它会禁用所有页面的缓存和优化."

#: inc/Engine/Admin/Settings/Subscriber.php:169
msgid "Import, Export, Rollback"
msgstr "导入、导出和还原"

#: inc/Engine/Admin/Settings/Subscriber.php:194
#: views/settings/page-sections/imagify.php:14
msgid "Image Optimization"
msgstr "图片优化"

#: inc/Engine/Admin/Settings/Subscriber.php:195
msgid "Compress your images"
msgstr "压缩您的图片"

#: inc/Engine/Admin/Settings/Subscriber.php:212
#: views/settings/page-sections/tutorials.php:48
msgid "Tutorials"
msgstr "指南"

#: inc/Engine/Admin/Settings/Subscriber.php:213
msgid "Getting started and how to videos"
msgstr "新手指南 和 How To 视频"

#: inc/Engine/Cache/PurgeExpired/Subscriber.php:75
msgid "WP Rocket Expired Cache Interval"
msgstr "WP Rocket 过期缓存间隔"

#: inc/Engine/Cache/WPCache.php:337
msgid "WP_CACHE value"
msgstr "WP_CACHE 值"

#: inc/Engine/Cache/WPCache.php:358
msgid ""
"The WP_CACHE constant needs to be set to true for WP Rocket cache to work "
"properly"
msgstr "为确保 WP Rocket 正常工作，WP_CACHE 常量需设为 true"

#: inc/Engine/Cache/WPCache.php:367
msgid "WP_CACHE is set to true"
msgstr "WP_CACHE 已设为 true"

#: inc/Engine/Cache/WPCache.php:375
msgid "WP_CACHE is not set"
msgstr "WP_CACHE 未设置"

#: inc/Engine/Cache/WPCache.php:383
msgid "WP_CACHE is set to false"
msgstr "WP_CACHE 已设为 false"

#: inc/Engine/CDN/RocketCDN/AdminPageSubscriber.php:90
msgid "Next Billing Date"
msgstr "下个付款日"

#: inc/Engine/CDN/RocketCDN/AdminPageSubscriber.php:99
msgid "No Subscription"
msgstr "未订阅"

#: inc/Engine/CDN/RocketCDN/AdminPageSubscriber.php:135
msgid "Your RocketCDN subscription is currently active."
msgstr "您的 RocketCDN 订阅已激活。"

#. translators: %1$s = opening <code> tag, %2$s = CDN URL, %3$s = closing
#. </code> tag.
#: inc/Engine/CDN/RocketCDN/AdminPageSubscriber.php:141
msgid "To use RocketCDN, replace your CNAME with %1$s%2$s%3$s."
msgstr "要使用 RocketCDN，请替换您的 CNAME 为 %1$s%2$s%3$s。"

#. translators: %1$is = opening link tag, %2$s = closing link tag.
#: inc/Engine/CDN/RocketCDN/AdminPageSubscriber.php:152
#: inc/Engine/CDN/RocketCDN/NoticesSubscriber.php:230
#: inc/Engine/CDN/RocketCDN/NoticesSubscriber.php:334
msgid "%1$sMore Info%2$s"
msgstr "%1$s更多信息%2$s"

#: inc/Engine/CDN/RocketCDN/APIClient.php:134
msgid ""
"We could not fetch the current price because RocketCDN API returned an "
"unexpected error code."
msgstr "RocketCDN API 返回未知错误，无法获取实时价格。"

#: inc/Engine/CDN/RocketCDN/APIClient.php:140
msgid "RocketCDN is not available at the moment. Please retry later."
msgstr "RocketCDN 暂时不可用，请稍后重试。"

#: inc/Engine/CDN/RocketCDN/APIClient.php:177
msgid "RocketCDN cache purge failed: Missing identifier parameter."
msgstr "RocketCDN 缓存清除失败：识别参数缺失。"

#: inc/Engine/CDN/RocketCDN/APIClient.php:186
msgid "RocketCDN cache purge failed: Missing user token."
msgstr "RocketCDN 缓存清除失败：用户 token 缺失。"

#: inc/Engine/CDN/RocketCDN/APIClient.php:212
msgid ""
"RocketCDN cache purge failed: The API returned an unexpected response code."
msgstr "RocketCDN 缓存清除失败：API 返回未知状态码。"

#: inc/Engine/CDN/RocketCDN/APIClient.php:221
msgid "RocketCDN cache purge failed: The API returned an empty response."
msgstr "RocketCDN 缓存清除失败：API 返回为空。"

#: inc/Engine/CDN/RocketCDN/APIClient.php:230
msgid "RocketCDN cache purge failed: The API returned an unexpected response."
msgstr "RocketCDN 缓存清除失败：API 返回未知状态码。"

#. translators: %s = message returned by the API.
#: inc/Engine/CDN/RocketCDN/APIClient.php:239
msgid "RocketCDN cache purge failed: %s."
msgstr "RocketCDN 缓存清除失败：%s。"

#: inc/Engine/CDN/RocketCDN/APIClient.php:247
msgid "RocketCDN cache purge successful."
msgstr "RocketCDN 缓存已成功清除。"

#: inc/Engine/CDN/RocketCDN/RESTSubscriber.php:137
msgid "RocketCDN enabled"
msgstr "RocketCDN 已启用"

#: inc/Engine/CDN/RocketCDN/RESTSubscriber.php:160
msgid "RocketCDN disabled"
msgstr "RocketCDN 已禁用"

#. Translators: %s = date formatted using date_i18n() and get_option(
#. 'date_format' ).
#: inc/Engine/CDN/RocketCDN/views/cta-big.php:27
msgid "Valid until %s only!"
msgstr " %s 即将到期！"

#: inc/Engine/CDN/RocketCDN/views/cta-big.php:36
msgid "Speed up your website thanks to:"
msgstr "致谢为您加速网站的贡献者："

#. translators: %1$s = opening strong tag, %2$s = closing strong tag.
#: inc/Engine/CDN/RocketCDN/views/cta-big.php:42
msgid ""
"High performance Content Delivery Network (CDN) with %1$sunlimited "
"bandwidth%2$s"
msgstr "高性能CDN，%1$s无限带宽%2$s"

#. translators: %1$s = opening strong tag, %2$s = closing strong tag.
#: inc/Engine/CDN/RocketCDN/views/cta-big.php:48
msgid ""
"Easy configuration: the %1$sbest CDN settings%2$s are automatically applied"
msgstr "轻松设置：已自动为您启用了 %1$sCDN 最佳设置%2$s "

#. translators: %1$s = opening strong tag, %2$s = closing strong tag.
#: inc/Engine/CDN/RocketCDN/views/cta-big.php:54
msgid ""
"WP Rocket integration: the CDN option is %1$sautomatically configured%2$s in"
" our plugin"
msgstr "WP Rocket 整合: 我们的插件已为您%1$s自动配置%2$sCDN 选项"

#: inc/Engine/CDN/RocketCDN/views/cta-big.php:58
msgid "Learn more about RocketCDN"
msgstr "了解 RocketCDN 的更多信息"

#. translators: %1$s = discounted price, %2$s = regular price.
#: inc/Engine/CDN/RocketCDN/views/cta-big.php:65
msgid ""
"*$%1$s/month for 12 months then $%2$s/month. You can cancel your "
"subscription at any time."
msgstr "*$%1$s/月, 一年后 $%2$s/月. 您可以随时取消订阅哦."

#: inc/Engine/CDN/RocketCDN/views/cta-big.php:86
msgid "Billed monthly"
msgstr "月付"

#: inc/Engine/CDN/RocketCDN/views/cta-big.php:87
msgid "Get Started"
msgstr "新手指南"

#: inc/Engine/CDN/RocketCDN/views/cta-big.php:92
msgid "Reduce this banner"
msgstr "不想看"

#: inc/Engine/CDN/RocketCDN/views/cta-small.php:17
msgid ""
"Speed up your website with RocketCDN, WP Rocket’s Content Delivery Network."
msgstr "用 RocketCDN 加速您的网站，是 WP Rocket 开发的哦 ~"

#: inc/Engine/CDN/RocketCDN/views/cta-small.php:20
#: inc/Engine/CDN/RocketCDN/views/promote-notice.php:13
msgid "Learn More"
msgstr "了解详情"

#: inc/Engine/CDN/RocketCDN/views/dashboard-status.php:23
msgid "RocketCDN is unavailable on local domains and staging sites."
msgstr "RocketCDN 无法用于本地域名或模拟环境。"

#: inc/Engine/CDN/RocketCDN/views/dashboard-status.php:32
msgid "Get RocketCDN"
msgstr "获取 RocketCDN"

#: inc/Engine/CDN/RocketCDN/views/promote-notice.php:11
msgid "New!"
msgstr "New!"

#: inc/Engine/CDN/RocketCDN/views/promote-notice.php:12
msgid ""
"Speed up your website with RocketCDN, WP Rocket’s Content Delivery Network!"
msgstr "用 RocketCDN 加速您的网站，是 WP Rocket 开发的哦 ~"

#: inc/Engine/Common/Queue/RUCSSQueueRunner.php:253
msgid "Every minute"
msgstr "每分钟"

#: inc/Engine/CriticalPath/Admin/Admin.php:264
msgid "Regenerate Critical Path CSS"
msgstr "重新生成关键路径CSS"

#: inc/Engine/CriticalPath/Admin/Post.php:144
#: views/cpcss/metabox/generate.php:47
msgid "Generate Specific CPCSS"
msgstr "生成特定的CPCSS"

#: inc/Engine/CriticalPath/Admin/Post.php:145
#: views/cpcss/metabox/generate.php:45
msgid "Regenerate specific CPCSS"
msgstr "重新生成特定的CPCSS"

#: inc/Engine/CriticalPath/Admin/Post.php:216
msgid "This feature is not available for non-public post types."
msgstr "此功能不可用于私密文章类型."

#: inc/Engine/CriticalPath/Admin/Post.php:219
msgid "%l to use this feature."
msgstr "%l 使用这个特性"

#. translators: %s = post type.
#: inc/Engine/CriticalPath/Admin/Post.php:222
msgid "Publish the %s"
msgstr "发布%s"

#. translators: %s = post type.
#: inc/Engine/CriticalPath/Admin/Post.php:223
msgid "Enable Load CSS asynchronously in WP Rocket settings"
msgstr "在WP Rocket 设置中开启 CSS 异步加载"

#: inc/Engine/CriticalPath/Admin/Post.php:224
msgid "Enable Load CSS asynchronously in the options above"
msgstr "在以上选项中启用 CSS 异步加载"

#. translators: %1$s = type of content, %2$s = error message.
#: inc/Engine/CriticalPath/APIClient.php:64
msgid "Critical CSS for %1$s not generated. Error: %2$s"
msgstr "%1$s的关键路径CSS未生成。错误：%2$s"

#. translators: %s = item URL.
#: inc/Engine/CriticalPath/APIClient.php:170
msgid ""
"Critical CSS for %1$s on mobile not generated. Error: The API returned an "
"empty response."
msgstr "移动端的%1$s关键CSS没有被生成。原因：API返回一个了空的响应报文"

#. translators: %s = item URL.
#: inc/Engine/CriticalPath/APIClient.php:173
msgid ""
"Critical CSS for %1$s not generated. Error: The API returned an empty "
"response."
msgstr "%1$s的关键CSS 没有生成。原因：API返回了一个空的响应报文。"

#. translators: %s = item URL.
#: inc/Engine/CriticalPath/APIClient.php:185
msgid "Critical CSS for %1$s on mobile not generated."
msgstr "移动端的关键CSS%1$s没有被生成"

#. translators: %s = item URL.
#. translators: %1$s = item URL or item type.
#: inc/Engine/CriticalPath/APIClient.php:187
#: inc/Engine/CriticalPath/ProcessorService.php:194
msgid "Critical CSS for %1$s not generated."
msgstr "%1$s的Critical CSS没有被生成"

#. translators: %s = URL.
#: inc/Engine/CriticalPath/APIClient.php:195
msgid ""
"Critical CSS for %1$s on mobile not generated. Error: The API returned an "
"invalid response code."
msgstr "移动端的%1$sCritical CSS没有被生成。原因：API返回一个了无效的响应编码"

#. translators: %s = URL.
#: inc/Engine/CriticalPath/APIClient.php:197
msgid ""
"Critical CSS for %1$s not generated. Error: The API returned an invalid "
"response code."
msgstr "%1$s的Critical CSS没有被生成。原因：API返回一个了无效的响应编码"

#. translators: %1$s = error message.
#: inc/Engine/CriticalPath/APIClient.php:205
#: inc/Engine/CriticalPath/ProcessorService.php:201
msgid "Error: %1$s"
msgstr "错误:%1$s"

#: inc/Engine/CriticalPath/CriticalCSSSubscriber.php:176
msgid "Critical CSS generation is currently running."
msgstr "关键CSS生成 正在运行中。"

#. Translators: %1$s = opening link tag, %2$s = closing link tag.
#: inc/Engine/CriticalPath/CriticalCSSSubscriber.php:181
msgid "Go to the %1$sWP Rocket settings%2$s page to track progress."
msgstr "查看进展请访问 %1$sWP Rocket 设置%2$s页面。"

#. Translators: %1$d = number of critical CSS generated, %2$d = total number
#. of critical CSS to generate.
#: inc/Engine/CriticalPath/CriticalCSSSubscriber.php:396
msgid ""
"Critical CSS generation is currently running: %1$d of %2$d page types "
"completed. (Refresh this page to view progress)"
msgstr "正在生成关键路径CSS：已完成 %1$d / %2$d 页。（刷新以查看进度）"

#. Translators: %1$d = number of critical CSS generated, %2$d = total number
#. of critical CSS to generate.
#: inc/Engine/CriticalPath/CriticalCSSSubscriber.php:472
msgid "Critical CSS generation finished for %1$d of %2$d page types."
msgstr "生成关键路径CSS进度：%1$d / %2$d种页面类型。"

#: inc/Engine/CriticalPath/CriticalCSSSubscriber.php:479
msgid "Critical CSS generation encountered one or more errors."
msgstr "关键CSS生成出错。"

#: inc/Engine/CriticalPath/CriticalCSSSubscriber.php:479
msgid "Learn more."
msgstr "了解更多。"

#. translators: %1$ = opening bold tag, %2$ = closing bold tag.
#: inc/Engine/CriticalPath/CriticalCSSSubscriber.php:840
msgid ""
"We highly recommend the %1$supdated Remove Unused CSS%2$s for a better CSS "
"optimization. Load CSS Asynchronously is always available as a back-up."
msgstr "我们强烈推荐您采用最新的 %1$s移除未使用 CSS%2$s功能以获得最佳性能。异步加载 CSS 仅作为备选项。"

#: inc/Engine/CriticalPath/CriticalCSSSubscriber.php:845
msgid "Stay with the old option"
msgstr "仍然选择之前的方式"

#. translators: %s = item URL.
#: inc/Engine/CriticalPath/DataManager.php:68
msgid ""
"Critical CSS for %1$s on mobile not generated. Error: The destination folder"
" could not be created."
msgstr "移动端的 %1$s 关键CSS 未生成。原因：目标文件夹无法创建。"

#. translators: %s = item URL.
#: inc/Engine/CriticalPath/DataManager.php:71
msgid ""
"Critical CSS for %1$s not generated. Error: The destination folder could not"
" be created."
msgstr "%1$s的关键 CSS 未生成。原因：目标文件夹无法创建。"

#: inc/Engine/CriticalPath/DataManager.php:106
msgid "Critical CSS file for mobile does not exist"
msgstr "移动设备的Critical CSS文件不存在。Critical CSS指的是渲染首屏的最小 CSS 集合。"

#: inc/Engine/CriticalPath/DataManager.php:108
msgid "Critical CSS file does not exist"
msgstr "Critical CSS文件不存在"

#: inc/Engine/CriticalPath/DataManager.php:120
msgid "Critical CSS file for mobile cannot be deleted"
msgstr "移动端的Critical CSS文件无法删除"

#: inc/Engine/CriticalPath/DataManager.php:122
msgid "Critical CSS file cannot be deleted"
msgstr "Critical CSS 文件无法删除"

#. translators: %1$s = item URL or item type.
#: inc/Engine/CriticalPath/ProcessorService.php:187
msgid "Mobile Critical CSS for %1$s not generated."
msgstr "移动端的Critical CSS%1$s没有被生成"

#. translators: %1$s = Item URL or item type.
#: inc/Engine/CriticalPath/ProcessorService.php:228
msgid "Critical CSS for %s in progress."
msgstr "%s的Critical CSS进行中"

#. translators: %1$s = Item URL or item type.
#: inc/Engine/CriticalPath/ProcessorService.php:262
msgid "Mobile Critical CSS for %s generated."
msgstr "移动端Critical CSS文件%s已生成"

#. translators: %1$s = Item URL or item type.
#: inc/Engine/CriticalPath/ProcessorService.php:273
msgid "Critical CSS for %s generated."
msgstr "已为 %s 生成关键路径CSS。"

#: inc/Engine/CriticalPath/ProcessorService.php:295
msgid "Critical CSS file deleted successfully."
msgstr "Critical CSS 文件已删除"

#. translators: %1$s = Item URL or item type.
#: inc/Engine/CriticalPath/ProcessorService.php:317
msgid "Mobile Critical CSS for %1$s timeout. Please retry a little later."
msgstr "移动端%1$s的Critical CSS 超时。请稍后再试。"

#. translators: %1$s = Item URL or item type.
#: inc/Engine/CriticalPath/ProcessorService.php:330
msgid "Critical CSS for %1$s timeout. Please retry a little later."
msgstr "%1$s的Critical CSS超时，请稍后再试。"

#: inc/Engine/CriticalPath/RESTWP.php:141
msgid "Mobile CPCSS generation not enabled."
msgstr "移动端CPCSS生成未开启"

#: inc/Engine/CriticalPath/RESTWPPost.php:36
#: inc/Engine/CriticalPath/RESTWPPost.php:69
msgid "Requested post does not exist."
msgstr "您访问的文章不存在"

#: inc/Engine/CriticalPath/RESTWPPost.php:46
msgid "Cannot generate CPCSS for unpublished post."
msgstr "无法为未发布的文章生成CPCSS"

#: inc/Engine/HealthCheck/HealthCheck.php:142
msgid "Scheduled Cache Purge"
msgstr "定期清除缓存"

#: inc/Engine/HealthCheck/HealthCheck.php:143
msgid "Scheduled Database Optimization"
msgstr "定期数据库优化"

#: inc/Engine/HealthCheck/HealthCheck.php:144
msgid "Database Optimization Process"
msgstr "数据库优化过程"

#: inc/Engine/HealthCheck/HealthCheck.php:145
msgctxt "noun"
msgid "Preload"
msgstr "预缓存"

#: inc/Engine/HealthCheck/HealthCheck.php:146
msgid "Critical Path CSS Generation Process"
msgstr "Critical CSS 路径生成过程"

#. translators: %1$s = <strong>, %2$s = price, %3$s = </strong>.
#: inc/Engine/License/Renewal.php:76
msgid "Renew before it is too late, you will only pay %1$s%2$s%3$s!"
msgstr "尽快续费哦，您只需支付 %1$s%2$s%3$s！"

#. translators: %1$s = <strong>, %2$s = discount, %3$s = </strong>,%4$s =
#. <strong>, %5$s = price, %6$s=</strong>.
#: inc/Engine/License/Renewal.php:85
msgid ""
"Renew with a %1$s%2$s discount%3$s before it is too late, you will only pay "
"%4$s%5$s%6$s!"
msgstr "限时 %1$s%2$s折%3$s续费，您只需要支付 %4$s%5$s%6$s 哦！"

#. translators: %1$s = <strong>, %2$s = </strong>, %3$s = price.
#: inc/Engine/License/Renewal.php:139
msgid "Renew your license for 1 year now at %1$s%3$s%2$s."
msgstr "续费1年仅需 %1$s%3$s%2$s。"

#. translators: %1$s = <strong>, %2$s = </strong>, %3$s = discount percentage,
#. %4$s = price.
#: inc/Engine/License/Renewal.php:152
msgid ""
"Renew your license for 1 year now and get %1$s%3$s OFF%2$s immediately: you "
"will only pay %1$s%4$s%2$s!"
msgstr "续费可享 %1$s%3$s折立减%2$s：您仅需支付 %1$s%4$s%2$s哦！"

#. translators: %1$s = <strong>, %2$s = </strong>, %3$s = discount price.
#: inc/Engine/License/Renewal.php:218
msgid "Renew before it is too late, you will pay %1$s%3$s%2$s."
msgstr "赶快续费吧，您将支付 %1$s%3$s%2$s。"

#. translators: %1$s = <strong>, %2$s = discount percentage, %3$s = </strong>,
#. %4$s = discount price.
#: inc/Engine/License/Renewal.php:227
msgid ""
"Renew with a %1$s%2$s discount%3$s before it is too late, you will only pay "
"%1$s%4$s%3$s!"
msgstr "立享  %1$s%2$s  折扣，您只需支付 %3$s %1$s%4$s%3$s！"

#. translators: %1$s = <a>, %2$s = </a>.
#: inc/Engine/License/Renewal.php:546
msgid ""
"You need a valid license to continue using this feature. %1$sRenew now%2$s "
"before losing access."
msgstr "该功能仅有效授权可用。%1$s立刻续费%2$s吧。"

#. translators: %1$s = <a>, %2$s = </a>.
#: inc/Engine/License/Renewal.php:567
msgid "You need an active license to enable this option. %1$sRenew now%2$s."
msgstr "开启该选项需要有效授权哦。%1$s立刻续费%2$s吧。"

#. translators: %1$s = <a>, %2$s = </a>.
#: inc/Engine/License/Renewal.php:595
msgid "You need an active license to enable this option. %1$sMore info%2$s."
msgstr "开启该选项需要有效授权哦。%1$s更多信息%2$s。"

#. translators: %1$s = promotion name, %2$s = <br>, %3$s = <strong>, %4$s =
#. promotion discount percentage, %5$s = </strong>.
#: inc/Engine/License/Upgrade.php:251
msgid ""
"Take advantage of %1$s to speed up more websites:%2$s get a %3$s%4$s off%5$s"
" for %3$supgrading your license to Plus or Infinite!%5$s"
msgid_plural ""
"Take advantage of %1$s to speed up more websites:%2$s get a %3$s%4$s off%5$s"
" for %3$supgrading your license to Infinite!%5$s"
msgstr[0] "利用 %1$s 来加速更多网站：%2$s 获取 %3$s%4$s 于%5$s 以%3$s升级为无限授权！%5$s"

#: inc/Engine/License/Upgrade.php:382 inc/Engine/License/Upgrade.php:405
msgid "Unlimited"
msgstr "无限"

#. translators: %s = promotion discount percentage.
#: inc/Engine/License/views/promo-banner.php:16
msgid "%s off"
msgstr "立减  %s"

#. translators: %s = promotion name.
#: inc/Engine/License/views/promo-banner.php:21
msgid "%s promotion is live!"
msgstr "%s 促销活动正在进行！"

#: inc/Engine/License/views/promo-banner.php:27
msgid "Hurry Up! Deal ends in:"
msgstr "速度上车！促销倒计时："

#: inc/Engine/License/views/promo-banner.php:31
#: inc/Engine/License/views/renewal-soon-banner.php:14
msgid "Minutes"
msgstr "分"

#: inc/Engine/License/views/promo-banner.php:32
#: inc/Engine/License/views/renewal-soon-banner.php:15
msgid "Seconds"
msgstr "秒"

#: inc/Engine/License/views/promo-banner.php:34
#: inc/Engine/License/views/upgrade-section.php:11
msgid "Upgrade now"
msgstr "立即升级"

#: inc/Engine/License/views/renewal-expired-banner-ocd-disabled.php:11
msgid "The Optimize CSS Delivery feature is disabled."
msgstr "CSS 交付优化已禁用。"

#: inc/Engine/License/views/renewal-expired-banner-ocd-disabled.php:15
msgid ""
"You can no longer use the Remove Unused CSS or Load CSS asynchronously "
"options."
msgstr "您不能再使用 移除未使用CSS 或 CSS异步加载 功能"

#. translators: %1$s = <strong>, %2$s = </strong>.
#: inc/Engine/License/views/renewal-expired-banner-ocd-disabled.php:20
msgid ""
"You need an %1$sactive license%2$s to keep optimizing your CSS delivery, "
"which addresses a PageSpeed Insights recommendation and improves your page "
"performance."
msgstr ""
"使用 CSS 交付优化功能需要%1$s有效授权%2$s哦~ 开启这个 PageSpeed Insight 推荐的功能， 可以改善您的页面性能。"

#: inc/Engine/License/views/renewal-expired-banner-ocd-disabled.php:29
#: inc/Engine/License/views/renewal-expired-banner-ocd.php:39
#: inc/Engine/License/views/renewal-expired-banner.php:27
#: inc/Engine/License/views/renewal-soon-banner.php:31
msgid "Renew now"
msgstr "立即续期"

#: inc/Engine/License/views/renewal-expired-banner-ocd.php:11
msgid "You will soon lose access to some features"
msgstr "您将很快无法再使用某些功能"

#. translators: %1$s = <strong>, %2$s = </strong>.
#: inc/Engine/License/views/renewal-expired-banner-ocd.php:18
msgid ""
"You need an %1$sactive license to continue optimizing your CSS delivery%2$s."
msgstr "您需要一个%1$s有效授权来继续优化您的CSS交付%2$s。"

#: inc/Engine/License/views/renewal-expired-banner-ocd.php:24
msgid ""
"The Remove Unused CSS and Load CSS asynchronously features are great options"
" to address the PageSpeed Insights recommendations and improve your website "
"performance."
msgstr "PageSpeed Insights 极力推荐 移除未使用 CSS 和 CSS异步加载 这两个功能， 它们可以改善您的网站性能。"

#. translators: %1$s = <strong>, %2$s = </strong>, %3$s = date.
#: inc/Engine/License/views/renewal-expired-banner-ocd.php:29
msgid "They will be %1$sautomatically disabled on %3$s%2$s."
msgstr "%3$s%2$s 它们将被%1$s自动禁用。"

#: inc/Engine/License/views/renewal-expired-banner.php:11
msgid "Your WP Rocket license is expired!"
msgstr "您的 WP Rocket 许可证已过期！"

#. translators: %1$s = <strong>, %2$s = </strong>.
#: inc/Engine/License/views/renewal-expired-banner.php:18
msgid ""
"Your website could be much faster if it could take advantage of our %1$snew "
"features and enhancements%2$s. 🚀"
msgstr "如果能用好我们的%1$s新功能%2$s，您的网站会变得更快哦。🚀"

#. translators: %1$s = <strong>, %2$s = </strong>.
#: inc/Engine/License/views/renewal-soon-banner.php:22
msgid ""
"Your %1$sWP Rocket license is about to expire%2$s: you will soon lose access"
" to product updates and support."
msgstr "您的%1$sWP Rocket授权即将到期%2$s: 届时您将无法获得产品更新和售后支持."

#: inc/Engine/License/views/upgrade-popin.php:12
msgid "Speed Up More Websites"
msgstr "加速更多网站"

#. translators: %1$s = opening strong tag, %2$s = closing strong tag.
#: inc/Engine/License/views/upgrade-popin.php:19
msgid ""
"You can use WP Rocket on more websites by upgrading your license. To "
"upgrade, simply pay the %1$sprice difference%2$s between your current and "
"new licenses, as shown below."
msgstr "如您想升级授权，仅需补齐%1$s差价%2$s 。升级后，即可在多个网站使用 WP Rocket。"

#. translators: %1$s = opening strong tag, %2$s = closing strong tag.
#: inc/Engine/License/views/upgrade-popin.php:25
msgid ""
"%1$sN.B.%2$s: Upgrading your license does not change your expiration date"
msgstr "%1$s注意%2$s：升级授权并不会延长有效期"

#. translators: %s = price.
#: inc/Engine/License/views/upgrade-popin.php:35
msgid "Save $%s"
msgstr "立省 $%s"

#. translators: %s = number of websites.
#: inc/Engine/License/views/upgrade-popin.php:48
msgid "%s websites"
msgstr "%s 个网站"

#. translators: %s = license name.
#: inc/Engine/License/views/upgrade-popin.php:54
msgid "Upgrade to %s"
msgstr "升级到 %s"

#: inc/Engine/License/views/upgrade-section.php:11
msgid ""
"You can use WP Rocket on more websites by upgrading your license (you will "
"only pay the price difference between your current and new licenses)."
msgstr "升级授权即可将 WP Rocket 用于多个网站。（仅需补齐%1$s差价%2$s ）"

#: inc/Engine/Optimization/DelayJS/Admin/SiteList.php:195
msgid "Analytics & Ads"
msgstr "分析 & 广告"

#: inc/Engine/Optimization/DelayJS/Admin/SiteList.php:200
msgid "Plugins"
msgstr "插件"

#: inc/Engine/Optimization/DelayJS/Admin/SiteList.php:205
msgid "Themes"
msgstr "主题"

#: inc/Engine/Optimization/DynamicLists/DynamicLists.php:101
msgid ""
"You need an active license to get the latest version of the lists from our "
"server."
msgstr "您需要一个有效授权来获取服务器上的最新列表。"

#: inc/Engine/Optimization/DynamicLists/DynamicLists.php:115
msgid "Could not get updated lists from server."
msgstr "无法从服务器获取最新列表。"

#: inc/Engine/Optimization/DynamicLists/DynamicLists.php:124
msgid "Lists are up to date."
msgstr "列表已是最新版本。"

#: inc/Engine/Optimization/DynamicLists/DynamicLists.php:133
msgid "Could not update lists."
msgstr "无法更新列表"

#: inc/Engine/Optimization/DynamicLists/DynamicLists.php:142
msgid "Lists are successfully updated."
msgstr "列表已成功更新。"

#: inc/Engine/Optimization/DynamicLists/ServiceProvider.php:60
msgid "Default Lists"
msgstr "默认列表"

#: inc/Engine/Optimization/DynamicLists/ServiceProvider.php:66
msgid "Delay JavaScript Execution Exclusion Lists"
msgstr "JavaScript 延迟执行的排除列表"

#: inc/Engine/Optimization/DynamicLists/ServiceProvider.php:72
msgid "Incompatible plugins Lists"
msgstr "不兼容的插件列表"

#. translators: %1$s = plugin name, %2$s = number of seconds.
#: inc/Engine/Optimization/RUCSS/Admin/Settings.php:224
msgid ""
"%1$s: Please wait %2$s seconds. The Remove Unused CSS service is processing "
"your pages."
msgstr "%1$s: 请稍等 %2$s 秒. \"移除未使用CSS服务\" 正在处理您的页面."

#. translators: %1$s = plugin name, %2$s = number of URLs, %3$s = number of
#. seconds.
#: inc/Engine/Optimization/RUCSS/Admin/Settings.php:274
msgid ""
"%1$s: The Used CSS of your homepage has been processed. WP Rocket will "
"continue to generate Used CSS for up to %2$s URLs per %3$s second(s)."
msgstr "%1$s: 您首页的已使用CSS已处理. WP Rocket 会持续处理已使用CSS, 当前速度: %2$sURL / %3$s 秒."

#. translators: %1$s = opening link tag, %2$s = closing link tag.
#: inc/Engine/Optimization/RUCSS/Admin/Settings.php:283
msgid "We suggest enabling %1$sPreload%2$s for the fastest results."
msgstr "我们建议开启 %1$s预缓存%2$s以获得最快的结果。"

#. translators: %1$s = opening link tag, %2$s = closing link tag.
#: inc/Engine/Optimization/RUCSS/Admin/Settings.php:293
msgid "To learn more about the process check our %1$sdocumentation%2$s."
msgstr "更多处理详情, 请查阅我们的 %1$s文档%2$s."

#: inc/Engine/Optimization/RUCSS/Admin/Settings.php:491
msgid ""
"We couldn't generate the used CSS because you're using a nulled version of "
"WP Rocket. You need an active license to use the Remove Unused CSS feature "
"and further improve your website's performance."
msgstr "Ummm... 由于您使用的是盗版 WP Rocket，我们无法生成已使用CSS。您需要一个有效授权来使用 移除未使用CSS 功能。"

#. translators: %1$s = promo percentage.
#: inc/Engine/Optimization/RUCSS/Admin/Settings.php:494
msgid "Click here to get a WP Rocket single license at %1$s off!"
msgstr "点我购买单用户授权，可享%1$s折立减哦！"

#. translators: %1$s = <a> open tag, %2$s = </a> closing tag.
#: inc/Engine/Optimization/RUCSS/Admin/Settings.php:547
msgid ""
"It seems a security plugin or the server's firewall prevents WP Rocket from "
"accessing the Remove Unused CSS generator. IPs listed %1$shere in our "
"documentation%2$s should be added to your allowlists:"
msgstr "访问 移除未使用CSS生成器 的请求被安全插件或服务器防火墙阻止。请将%1$s参考文档%2$s中的 IP 添加到允许列表："

#: inc/Engine/Optimization/RUCSS/Admin/Settings.php:552
msgid "- In the security plugin, if you are using one"
msgstr "- 在安全插件内，如果您在使用一个"

#: inc/Engine/Optimization/RUCSS/Admin/Settings.php:553
msgid "- In the server's firewall. Your host can help you with this"
msgstr "- 在服务器防火墙内。请联系您的主机商"

#. translators: %1$s = plugin name, %2$s = table name, %3$s = <a> open tag,
#. %4$s = </a> closing tag.
#: inc/Engine/Optimization/RUCSS/Admin/Settings.php:593
msgid ""
"%1$s: Could not create the %2$s table in the database which is necessary for"
" the Remove Unused CSS feature to work. Please reach out to %3$sour "
"support%4$s."
msgstr "%1$s：无法创建%2$s移除未使用 CSS功能所必须的数据库表。请联系%3$s我们的客服%4$s。"

#. translators: %1$s = plugin name.
#: inc/Engine/Optimization/RUCSS/Admin/Subscriber.php:315
msgid "%1$s: Used CSS option is not enabled!"
msgstr "%1$s: 已使用CSS 选项未启用 !"

#. translators: %1$s = plugin name.
#: inc/Engine/Optimization/RUCSS/Admin/Subscriber.php:337
msgid "%1$s: Used CSS cache cleared!"
msgstr "%1$s: 已使用CSS 缓存已清除 !"

#: inc/Engine/Optimization/RUCSS/Controller/UsedCSS.php:787
msgid "Clear Used CSS of this URL"
msgstr "清除该URL的已使用CSS"

#: inc/Engine/Optimization/RUCSS/Cron/Subscriber.php:160
msgid "WP Rocket Remove Unused CSS pending jobs"
msgstr "WP Rocket 移除未使用 CSS 的挂起作业"

#: inc/Engine/Optimization/RUCSS/Cron/Subscriber.php:174
msgid "WP Rocket clear Remove Unused CSS failed jobs"
msgstr "WP Rocket 清除 移除未使用CSS功能 的失败作业"

#. translators: %1$s = plugin name.
#: inc/Engine/Preload/Admin/Settings.php:57
msgid ""
"%1$s: The preload service is now active. After the initial preload it will "
"continue to cache all your pages whenever they are purged. No further action"
" is needed."
msgstr "%1$s：预缓存服务已启用。初次预缓存后，它会自动缓存被清除的任何页面，无需其他操作"

#: inc/Engine/Preload/Cron/Subscriber.php:138
msgid "WP Rocket Preload pending jobs"
msgstr "WP Rocket 预缓存的挂起作业"

#: inc/Engine/Preload/Cron/Subscriber.php:166
msgid "WP Rocket Preload revert stuck failed jobs"
msgstr "WP Rocket Preload 的失败作业"

#: inc/functions/admin.php:21
msgid ""
"There seems to be an issue validating your license. Please see the error "
"message below."
msgid_plural ""
"There seems to be an issue validating your license. You can see the error "
"messages below."
msgstr[0] "验证您的许可证时似乎有问题。"

#: inc/functions/admin.php:361
msgid "Server type:"
msgstr "服务器类型："

#: inc/functions/admin.php:370
msgid "PHP version number:"
msgstr "PHP版本号："

#: inc/functions/admin.php:379
msgid "WordPress version number:"
msgstr "WordPress版本号："

#: inc/functions/admin.php:388
msgid "WordPress multisite:"
msgstr "WordPress多站点："

#: inc/functions/admin.php:397
msgid "Current theme:"
msgstr "当前主题："

#: inc/functions/admin.php:406
msgid "Current site language:"
msgstr "当前站点语言："

#: inc/functions/admin.php:415
msgid "Active plugins:"
msgstr "已启用插件："

#: inc/functions/admin.php:418
msgid "Plugin names of all active plugins"
msgstr "所有已启用的插件名"

#: inc/functions/admin.php:424
msgid "Anonymized WP Rocket settings:"
msgstr "WP Rocket设置（匿名）："

#: inc/functions/admin.php:427
msgid "Which WP Rocket settings are active"
msgstr "WP Rocket中已启用的选项"

#: inc/functions/options.php:429
msgid "The provided license data are not valid."
msgstr "提供的授权信息无效。"

#. Translators: %1$s = opening link tag, %2$s = closing link tag.
#: inc/functions/options.php:432
msgid "To resolve, please %1$scontact support%2$s."
msgstr "如需解决问题，请%1$s联系客服%2$s。"

#. Translators: %1$s = opening em tag, %2$s = closing em tag, %3$s = opening
#. link tag, %4$s closing link tag.
#: inc/functions/options.php:491 inc/functions/options.php:530
msgid ""
"License validation failed. Our server could not resolve the request from "
"your website."
msgstr "验证授权失败. 我们的服务器无法处理来自您网站的请求"

#. Translators: %1$s = opening em tag, %2$s = closing em tag, %3$s = opening
#. link tag, %4$s closing link tag.
#: inc/functions/options.php:491
msgid ""
"Try clicking %1$sValidate License%2$s below. If the error persists, follow "
"%3$sthis guide%4$s."
msgstr "点击下方的%1$s验证授权%2$s试试. 如果还是出错, 请参考%3$s这个指南%4$s."

#. Translators: %1$s = opening link tag, %2$s = closing link tag.
#: inc/functions/options.php:507
msgid ""
"License validation failed. You may be using a nulled version of the plugin. "
"Please do the following:"
msgstr "授权验证失败. 您可能在使用破解版本. 请按以下操作:"

#. Translators: %1$s = opening link tag, %2$s = closing link tag.
#: inc/functions/options.php:507 inc/functions/options.php:549
msgid "Login to your WP Rocket %1$saccount%2$s"
msgstr "登录您的 WP Rocket %1$s账户%2$s"

#. Translators: %1$s = opening link tag, %2$s = closing link tag.
#: inc/functions/options.php:507 inc/functions/options.php:549
msgid "Download the zip file"
msgstr "下载 zip 文件"

#. Translators: %1$s = opening link tag, %2$s = closing link tag.
#: inc/functions/options.php:507 inc/functions/options.php:549
msgid "Reinstall"
msgstr "重新安装"

#. Translators: %1$s = opening link tag, %2$s = closing link tag.
#: inc/functions/options.php:507
msgid ""
"If you do not have a WP Rocket account, please %1$spurchase a license%2$s."
msgstr "如您没有 WP Rocket 账号, 请%1$s购买授权%2$s."

#. Translators: %1$s = opening link tag, %2$s = closing link tag.
#: inc/functions/options.php:515
msgid ""
"License validation failed. This user account does not exist in our database."
msgstr "验证授权失败。用户不存在。"

#. Translators: %1$s = opening link tag, %2$s = closing link tag.
#: inc/functions/options.php:515
msgid "To resolve, please contact support."
msgstr "如需解决问题请联系客服。"

#. Translators: %1$s = opening link tag, %2$s = closing link tag.
#: inc/functions/options.php:523
msgid "License validation failed. This user account is blocked."
msgstr "验证授权失败。该用户已被锁定。"

#. Translators: %1$s = opening link tag, %2$s = closing link tag.
#: inc/functions/options.php:523
msgid "Please see %1$sthis guide%2$s for more info."
msgstr "更多信息请参考%1$s这个文档%2$s。"

#. Translators: %1$s = opening em tag, %2$s = closing em tag, %3$s = opening
#. link tag, %4$s closing link tag.
#: inc/functions/options.php:530
msgid ""
"Try clicking %1$sSave Changes%2$s below. If the error persists, follow "
"%3$sthis guide%4$s."
msgstr "尝试点击下方的%1$s保存更改%2$s. 如问题仍然存在, 参考%3$s这个文档%4$s."

#. Translators: %1$s = opening link tag, %2$s = closing link tag.
#: inc/functions/options.php:543
msgid "Your license is not valid."
msgstr "您的授权无效。"

#. Translators: %1$s = opening link tag, %2$s = closing link tag.
#: inc/functions/options.php:543
msgid "Make sure you have an active %1$sWP Rocket license%2$s."
msgstr "请确保您有已激活的%1$sWP Rocket 授权%2$s。"

#. Translators: %1$s = opening link tag, %2$s = closing link tag, %3$s =
#. opening link tag.
#: inc/functions/options.php:545
msgid "You have added as many sites as your current license allows."
msgstr "您的网站数已超出授权限制。"

#. Translators: %1$s = opening link tag, %2$s = closing link tag, %3$s =
#. opening link tag.
#: inc/functions/options.php:545
msgid ""
"Upgrade your %1$saccount%2$s or %3$stransfer your license%2$s to this "
"domain."
msgstr "升级您的%1$s账号%2$s或%3$s转移授权%2$s至当前域名。"

#. Translators: %1$s = opening link tag, %2$s = closing link tag.
#: inc/functions/options.php:547
msgid "This website is not allowed."
msgstr "此网站未被允许。"

#. Translators: %1$s = opening link tag, %2$s = closing link tag.
#: inc/functions/options.php:547
msgid "Please %1$scontact support%2$s."
msgstr "请%1$s联系客服%2$s。"

#. Translators: %1$s = opening link tag, %2$s = closing link tag.
#: inc/functions/options.php:549
msgid "This license key is not recognized."
msgstr "该授权码无法识别。"

#. Translators: %1$s = opening link tag, %2$s = closing link tag.
#: inc/functions/options.php:549
msgid "If the issue persists, please %1$scontact support%2$s."
msgstr "如问题仍然存在, 请%1$s联系客服%2$s。"

#. Translators: %s = error message returned.
#: inc/functions/options.php:555
msgid "License validation failed: %s"
msgstr "授权验证失败：%s"

#. Translators: %s = Hosting name.
#: inc/ThirdParty/Hostings/Cloudways.php:82
msgid ""
"Varnish auto-purge will be automatically enabled once Varnish is enabled on "
"your %s server."
msgstr "在您的%s服务器上启用Varnish后，将自动启用Varnish自动清除功能。"

#. translators: %1$s = opening link tag, %2$s = closing link tag.
#: inc/ThirdParty/Hostings/Kinsta.php:158
msgid ""
"Your installation seems to be missing core Kinsta files managing Cache "
"clearing, which will prevent your Kinsta installation and WP Rocket from "
"working correctly. Please get in touch with Kinsta support through your "
"%1$sMyKinsta%2$s account to resolve this issue."
msgstr ""
"您安装的Kinsta可能缺失用于清除缓存的核心文件, 这会导致 Kinsta 和 WP Rocket 工作异常. \n"
"请通过您的%1$sMyKinsta%2$s账户联系 Kinsta 客服来解决问题."

#. translators: %1$s = plugin name.
#: inc/ThirdParty/Plugins/CDN/Cloudflare.php:125
msgid ""
"%1$s: Cloudflare's HTTP/2 Server Push is incompatible with the features of "
"Remove Unused CSS and Combine CSS files. We strongly recommend disabling it."
msgstr ""
"%1$s：Cloudflare 的 HTTP/2 Server Push 功能 与 移除未使用CSS、合并CSS功能不兼容。强烈建议您禁用它。"

#: inc/ThirdParty/Plugins/CDN/Cloudflare.php:168
msgid ""
"Your site is using the official Cloudflare plugin. We have enabled "
"Cloudflare auto-purge for compatibility. If you have APO activated, it is "
"also compatible."
msgstr ""
"您的网站正在使用 Cloudflare 官方插件。为确保兼容性，我们已经开启了 Cloudflare 的自动清除功能。如果开启了APO， 也没有问题。"

#: inc/ThirdParty/Plugins/CDN/Cloudflare.php:169
msgid ""
"Cloudflare cache will be purged each time WP Rocket clears its cache to "
"ensure content is always up-to-date."
msgstr "为确保内容是最新的，当 WP Rocket 清除缓存时，Cloudflare 缓存也将被清空。"

#. Translators: %1$s = strong opening tag, %2$s = strong closing tag.
#: inc/ThirdParty/Plugins/CDN/Cloudflare.php:213
msgid ""
"%1$sWP Rocket:%2$s You are using \"Dynamic Cookies Cache\". Cloudflare APO "
"is not yet compatible with that feature."
msgstr "%1$sWP Rocket:%2$s 您正在使用 “动态 Cookies 缓存” 功能。Cloudflare APO 与该功能不再兼容。"

#. Translators:%1$s = opening <a> tag, %2$s = closing </a> tag.
#: inc/ThirdParty/Plugins/CDN/Cloudflare.php:219
msgid ""
"You should either disable Cloudflare APO or check with the theme/plugin "
"requiring the use of “Dynamic Cookies Cache” developers for an alternative "
"way to be page-cache friendly. %1$sMore info%2$s"
msgstr ""
"您可禁用 Cloudflare APO 或询问您的主题/插件开发者是否有使用 “动态 Cookies 缓存” 的解决方案。%1$s更多信息%2$s"

#. Translators: %1$s = strong opening tag, %2$s = strong closing tag, %3$s =
#. opening <a> tag, %4$s = closing </a> tag, %5$s = opening <a> tag.
#: inc/ThirdParty/Plugins/CDN/Cloudflare.php:270
msgid ""
"%1$sWP Rocket:%2$s You are using \"Separate cache files for mobile "
"devices\". You need to activate \"Cache by Device Type\" %3$ssetting%5$s on "
"Cloudflare APO to serve the right version of the cache. %4$sMore info%5$s"
msgstr ""
"%1$sWP Rocket:%2$s 您正在使用 “移动端单独缓存” 功能。 为确保缓存正常工作，您需要启用 Cloudflare APO 设置中的 "
"“按设备类型缓存” %3$s选项%5$s 。%4$s更多信息%5$s"

#. Translators: %1$s = strong opening tag, %2$s = strong closing tag.
#: inc/ThirdParty/Plugins/CDN/Cloudflare.php:291
msgid ""
"%1$sWP Rocket:%2$s You have \"Cache by Device Type\" enabled on Cloudflare "
"APO. If you judge it necessary for the website to have a different cache on "
"mobile and desktop, we suggest you enable our “Separate Cache Files for "
"Mobiles Devices” to ensure the generated cache is accurate."
msgstr ""
"%1$sWP Rocket:%2$s 您启用了 Cloudflare APO 中的 “按设备类型缓存” 功能。如果您觉得有必要把电脑端和移动端分别缓存，"
" 我们建议您开启 “移动端单独缓存”功能。"

#. translators: %1$s is WP Rocket plugin name, %2$s is opening <a> tag, %3$s
#. is closing </a> tag.
#: inc/ThirdParty/Plugins/ModPagespeed.php:102
msgid ""
"<strong>%1$s</strong>: Mod PageSpeed is not compatible with this plugin and "
"may cause unexpected results. %2$sMore Info%3$s"
msgstr "<strong>%1$s</strong>：Mod PageSpeed与本插件不兼容，可能导致出错。%2$s更多信息%3$s"

#. Translators: %1$s is an opening <strong> tag; %2$s is a closing </strong>
#. tag
#: inc/ThirdParty/Plugins/Optimization/Autoptimize.php:75
msgid ""
"%1$sWP Rocket: %2$sWe have detected that Autoptimize's JavaScript "
"Aggregation feature is enabled. WP Rocket's Delay JavaScript Execution will "
"not be applied to the file it creates. We suggest disabling %1$sJavaScript "
"Aggregation%2$s to take full advantage of Delay JavaScript Execution."
msgstr ""
"%1$sWP Rocket: %2$s我们检测到您已启用 Autoptimize 的 \"JavaScript 合并\" 功能. WP Rocket 的"
" \"JavaScript 延迟执行\" 功能将不被应用于它所创建的文件. 如您想充分利用  \"JavaScript 延迟执行\" 功能, "
"我们建议您禁用%1$sJavaScript 合并%2$s."

#. Translators: %1$s is an opening <strong> tag; %2$s is a closing </strong>
#. tag
#: inc/ThirdParty/Plugins/Optimization/Autoptimize.php:130
msgid ""
"%1$sWP Rocket: %2$sWe have detected that Autoptimize's Aggregate Inline CSS "
"feature is enabled. WP Rocket's Load CSS Asynchronously will not work "
"correctly. We suggest disabling %1$sAggregate Inline CSS%2$s to take full "
"advantage of Load CSS Asynchronously Execution."
msgstr ""
"%1$sWP Rocket: %2$s检测到您已启用 Autoptimize 的 \"合并内联CSS\" 功能. WP Rocket 的 \" "
"CSS异步加载\" 可能工作异常. 如您想充分利用 \" CSS异步加载\" 功能, 我们建议您禁用 %1$s合并内联CSS%2$s."

#. translators: %1$s = opening <a> tag, %2$s = closing </a> tag.
#: inc/ThirdParty/Plugins/Optimization/Ezoic.php:45
msgid ""
"This plugin blocks WP Rocket's caching and optimizations. Deactivate it and "
"use %1$sEzoic's nameserver integration%2$s instead."
msgstr ""
"该插件阻止了 WP Rocket 的缓存和优化. 请禁用并使用%1$sEzoic 的Nameserver Intergration%2$s作为替代."

#. Translators: %s = Plugin name.
#: inc/ThirdParty/Plugins/Optimization/Hummingbird.php:78
msgctxt "Hummingbird notice"
msgid ""
"Please deactivate the following %s option which conflicts with WP Rocket "
"features:"
msgid_plural ""
"Please deactivate the following %s options which conflict with WP Rocket "
"features:"
msgstr[0] "请禁用与 WP Rocket 功能相冲突的下列%s选项："

#. Translators: %1$s = Plugin name, %2$s = <em>, %3$s = </em>.
#: inc/ThirdParty/Plugins/Optimization/Hummingbird.php:151
msgctxt "Hummingbird notice"
msgid ""
"%1$s %2$sdisable emoji%3$s conflicts with WP Rockets %2$sdisable emoji%3$s"
msgstr "%1$s%2$s禁用Emoji%3$s与 WP Rocket 的 %2$s禁用Emoji%3$s相冲突"

#. Translators: %1$s = Plugin name, %2$s = <em>, %3$s = </em>.
#: inc/ThirdParty/Plugins/Optimization/Hummingbird.php:187
msgctxt "Hummingbird notice"
msgid ""
"%1$s %2$sGZIP compression%3$s conflicts with WP Rocket %2$sGZIP "
"compression%3$s"
msgstr "%1$s%2$sGZIP压缩%3$s与WP Rocket的%2$sGZIP压缩%3$s冲突"

#. Translators: %1$s = Plugin name, %2$s = <em>, %3$s = </em>.
#: inc/ThirdParty/Plugins/Optimization/Hummingbird.php:223
msgctxt "Hummingbird notice"
msgid ""
"%1$s %2$sbrowser caching%3$s conflicts with WP Rocket %2$sbrowser "
"caching%3$s"
msgstr "%1$s%2$s浏览器缓存%3$s与WP Rocket的%2$s浏览器缓存%3$s冲突"

#. Translators: %1$s = Plugin name, %2$s = <em>, %3$s = </em>.
#: inc/ThirdParty/Plugins/Optimization/Hummingbird.php:255
msgctxt "Hummingbird notice"
msgid ""
"%1$s %2$spage caching%3$s conflicts with WP Rocket %2$spage caching%3$s"
msgstr "%1$s%2$s页面缓存%3$s与WP Rocket的%2$s页面缓存%3$s冲突"

#. Translators: %1$s = Plugin name, %2$s = <em>, %3$s = </em>.
#: inc/ThirdParty/Plugins/Optimization/Hummingbird.php:289
msgctxt "Hummingbird notice"
msgid ""
"%1$s %2$sasset optimization%3$s conflicts with WP Rocket %2$sfile "
"optimization%3$s"
msgstr "%1$s%2$s资源优化%3$s与WP Rocket的%2$s文件优化%3$s相冲突"

#: inc/ThirdParty/Plugins/Optimization/Perfmatters.php:38
msgid ""
"Remove Unused CSS is currently activated in Perfmatters. If you want to use "
"WP Rocket's Remove Unused CSS feature, disable this option in Perfmatters."
msgstr ""
"您已开启了 Perfmatters 中的 移除未使用CSS 功能， 如果你想换成 WP Rocket， 请先去 Perfmatters 内禁用掉哦。"

#: inc/ThirdParty/Plugins/Optimization/RapidLoad.php:39
msgid ""
"Automated unused CSS removal is currently activated in RapidLoad Power-Up "
"for Autoptimize. If you want to use WP Rocket's Remove Unused CSS feature, "
"disable the  RapidLoad Power-Up for Autoptimize plugin."
msgstr ""
"您已开启了 RapidLoad Power-Up for Autoptimize 中的 自动移除未使用CSS 功能， 如果你想换成 WP Rocket，"
" 请先去禁用掉哦。"

#. translators: %1$s = plugin name.
#: inc/ThirdParty/Plugins/Optimization/WPMeteor.php:42
msgid ""
"Delay JS is currently activated in %1$s. If you want to use WP Rocket’s "
"delay JS, disable %1$s"
msgstr "JS 延迟加载已在 %1$s 中启用。若您想要使用 WP Rocket 的延迟加载 JS 功能，请先禁用 %1$s 。"

#: inc/ThirdParty/Plugins/Smush.php:108 inc/ThirdParty/Plugins/Smush.php:126
msgid "Smush"
msgstr "Smush"

#: inc/ThirdParty/Themes/Avada.php:116
msgid "Avada"
msgstr "Avada"

#: inc/ThirdParty/Themes/Divi.php:341
msgid ""
"Your Divi template was updated. Clear the Used CSS if the layout, design or "
"CSS styles were changed."
msgstr "您的 Divi 模版已更新。如果网站布局、设计或CSS样式有更改，请清空已使用CSS。"

#: views/cpcss/activate-cpcss-mobile.php:13
msgid "Load CSS asynchronously for mobile"
msgstr "移动端异步加载 CSS 文件"

#: views/cpcss/activate-cpcss-mobile.php:14
msgid ""
"Your website currently uses the same Critical Path CSS for both desktop and "
"mobile."
msgstr "您的网站当前针对台式机和移动设备使用相同的Critical CSS路径。"

#: views/cpcss/activate-cpcss-mobile.php:15
msgid "Click the button to enable mobile-specific CPCSS for your site."
msgstr "单击该按钮为您的站点启用特定于移动设备的CPCSS。"

#. translators: %1$s = opening link tag, %2$s = closing link tag.
#: views/cpcss/activate-cpcss-mobile.php:20
#: views/settings/enable-google-fonts.php:19
msgid ""
"This is a one-time action and this button will be removed afterwards. "
"%1$sMore info%2$s"
msgstr "这是一次性操作，点击按钮此后将被删除。%1$s更多信息%2$s"

#. translators: %1$s = opening link tag, %2$s = closing link tag.
#: views/cpcss/activate-cpcss-mobile.php:30
msgid ""
"Your site is now using mobile-specific critical path CSS. %1$sMore info%2$s"
msgstr "您的站点现在已经使用了移动端特定的Critical Path CSS。%1$s更多信息%2$s"

#: views/cpcss/activate-cpcss-mobile.php:39
msgid "Generate Mobile Specific CPCSS"
msgstr "生成移动专用CPCSS"

#: views/cpcss/metabox/container.php:19
msgid "Critical Path CSS"
msgstr "关键的CSS路径："

#. translators: %1$s = opening link tag, %2$s = closing link tag.
#: views/cpcss/metabox/generate.php:23
msgid "Generate specific Critical Path CSS for this post. %1$sMore info%2$s"
msgstr "为文章生成特定的Critical Path CSS。%1$s更多信息%2$s"

#. translators: %1$s = opening link tag, %2$s = closing link tag.
#: views/cpcss/metabox/generate.php:33
msgid "This post uses specific Critical Path CSS. %1$sMore info%2$s"
msgstr "文章使用了特定的Critical Path CSS。%1$s更多信息%2$s"

#: views/cpcss/metabox/generate.php:56
msgid "Revert back to the default CPCSS"
msgstr "恢复为默认的CPCSS"

#: views/deactivation-intent/form.php:21
msgid "Facing an issue?"
msgstr "遇到问题了吗?"

#: views/deactivation-intent/form.php:24
msgid ""
"It is not always necessary to deactivate WP Rocket when facing any issues. "
"Most of them can be fixed by deactivating only some options."
msgstr "当遇到问题的时候, 没必要禁用 WP Rocket. 大部分情况下, 只需要禁用某些选项就行."

#. translators: %1$s = opening strong tag, %2$s = closing strong tag.
#: views/deactivation-intent/form.php:29
msgid ""
"Our advice? Instead of deactivating WP Rocket, use our %1$sSafe Mode%2$s to "
"quickly disable LazyLoad, File Optimization, and CDN options. Then check to "
"see if your issue is resolved."
msgstr ""
"我们的建议? 其实不用禁用 WP Rocket 啦~ 您可以先通过 %1$s安全模式%2$s 来快速禁用 延迟加载、文件优化和 CDN 选项, "
"然后再看看问题是否解决."

#: views/deactivation-intent/form.php:35
msgid "Do you want to use our Safe Mode to troubleshoot WP Rocket?"
msgstr "是否使用 \"安全模式\" 来排查 WP Rocket 故障?"

#. translators: %1$s = opening strong tag, %2$s = closing strong tag.
#: views/deactivation-intent/form.php:55
msgid "Yes, apply \"%1$sSafe Mode%2$s\""
msgstr "是, 应用 \"%1$s安全模式%2$s\""

#. translators: %1$s = opening strong tag, %2$s = closing strong tag.
#: views/deactivation-intent/form.php:68
msgid ""
"and export WP Rocket settings %1$s(Recommended as current settings will be "
"deleted)%2$s"
msgstr "并导出 WP Rocket 设置 %1$s （推荐操作，当前设置将被删除）%2$s"

#: views/deactivation-intent/form.php:76
msgid "No, deactivate and snooze this message for"
msgstr "否, 禁用并在指定天数内跳过通知: "

#: views/deactivation-intent/form.php:78
msgid "1 day"
msgstr "1 天"

#: views/deactivation-intent/form.php:79
msgid "7 days"
msgstr "7 天"

#: views/deactivation-intent/form.php:80
msgid "30 days"
msgstr "30 天"

#: views/deactivation-intent/form.php:81
msgid "Forever"
msgstr "永久"

#: views/deactivation-intent/form.php:87
msgid "Cancel"
msgstr "取消"

#: views/deactivation-intent/form.php:88
msgid "Confirm"
msgstr "确认"

#. translators: %1$s = <strong>, %2$s = plugin version, %3$s = </strong>, %4$s
#. = <a>, %5$s = </a>, %6$s = <a>.
#: views/plugins/update-renewal-expired-notice.php:27
msgid ""
" %1$sWP Rocket %2$s%3$s is available. %4$sLearn more%5$s about the updates "
"and enhancements of this major version. You need an active license to use "
"them on your website, don’t miss out! %6$sRenew Now%5$s"
msgstr ""
"发现 %1$sWP Rocket %2$s%3$s 新版本. %4$s点我了解更多详情%5$s 新版本需要有效授权才能使用哦! %6$s立即续费%5$s"

#: views/settings/dynamic-lists-update.php:14
msgid "Update Inclusion and Exclusion Lists"
msgstr "更新包含和排除列表"

#. translators: %1$s = opening link tag, %2$s = closing link tag.
#: views/settings/dynamic-lists-update.php:19
msgid ""
"Compatibility lists are downloaded automatically every week. Click the "
"button if you want to update them manually. %1$sMore info%2$s"
msgstr "兼容列表每周会自动下载。点击按钮可手动更新。%1$s更多信息%2$s"

#: views/settings/dynamic-lists-update.php:29
msgid "Update lists"
msgstr "更新列表"

#: views/settings/enable-google-fonts.php:13
msgid "Enable Google Font Optimization"
msgstr "启用 Google 字体优化"

#: views/settings/enable-google-fonts.php:14
msgid ""
"Improves font performance and combines multiple font requests to reduce the "
"number of HTTP requests."
msgstr "优化字体性能并合并多个字体请求以减少 HTTP请求数。"

#. translators: %1$s = opening link tag, %2$s = closing link tag.
#: views/settings/enable-google-fonts.php:29
msgid ""
"Google Fonts Optimization is now enabled for your site. %1$sMore info%2$s"
msgstr "Google 字体优化已启用。%1$s更多信息%2$s"

#: views/settings/enable-google-fonts.php:38
msgid "Optimize Google Fonts"
msgstr "优化 Google 字体"

#: views/settings/fields/cache-lifespan.php:26
msgid "Clear cache after"
msgstr "清空缓存间隔"

#: views/settings/fields/cnames.php:58 views/settings/fields/cnames.php:92
#: views/settings/fields/cnames.php:122
msgid "CSS & JavaScript"
msgstr "CSS & JavaScript"

#: views/settings/fields/cnames.php:59 views/settings/fields/cnames.php:93
#: views/settings/fields/cnames.php:123
msgid "JavaScript"
msgstr "JavaScript"

#: views/settings/fields/cnames.php:60 views/settings/fields/cnames.php:94
#: views/settings/fields/cnames.php:124
msgid "CSS"
msgstr "CSS"

#: views/settings/fields/import-form.php:29
msgid "Import settings"
msgstr "导入配置"

#: views/settings/fields/one-click-addon.php:43
#: views/settings/fields/rocket-addon.php:43
msgid "Add-on status"
msgstr "扩展功能状态"

#: views/settings/fields/rocket-addon.php:72
msgid "Modify options"
msgstr "修改设置"

#: views/settings/fields/rocket-cdn.php:29
#: views/settings/fields/rocket-cdn.php:41
msgid "CDN CNAME"
msgstr "CDN CNAME"

#. translators: %s is a "Learn more" link.
#: views/settings/fields/rocket-cdn.php:62
msgid "Purges RocketCDN cached resources for your website. %s"
msgstr "清除 RocketCDN 缓存资源。%s"

#. translators: %s is a "Learn more" link.
#: views/settings/fields/rocket-cdn.php:63
#: views/settings/page-sections/cloudflare.php:36
#: views/settings/page-sections/sucuri.php:37
msgid "Learn more"
msgstr "更多信息"

#: views/settings/fields/rocket-cdn.php:72
msgid "Clear all RocketCDN cache files"
msgstr "清除所有的 RocketCDN 缓存文件"

#: views/settings/page-sections/cloudflare.php:27
msgid "Cloudflare Cache"
msgstr "Cloudfare缓存"

#. translators: %s is a "Learn more" link.
#: views/settings/page-sections/cloudflare.php:35
#: views/settings/page-sections/sucuri.php:36
msgid "Purges cached resources for your website. %s"
msgstr "从您的网站清除缓存资源。%s"

#. translators: %s is a "Learn more" link.
#: views/settings/page-sections/cloudflare.php:36
msgid "https://support.cloudflare.com/hc/en-us/articles/200169246"
msgstr "https://support.cloudflare.com/hc/en-us/articles/200169246"

#: views/settings/page-sections/cloudflare.php:45
msgid "Clear all Cloudflare cache files"
msgstr "清除所有Cloudflare缓存文件"

#: views/settings/page-sections/dashboard.php:38
msgid "Congratulations!"
msgstr "恭喜！"

#: views/settings/page-sections/dashboard.php:40
msgid "WP Rocket is now activated and already working for you."
msgstr "WP Rocket 已激活并开始工作。"

#: views/settings/page-sections/dashboard.php:42
msgid "Your website should be loading faster now!"
msgstr "您的网站应该变快啦 !"

#: views/settings/page-sections/dashboard.php:44
msgid ""
"To guarantee fast websites, WP Rocket automatically applies 80% of web "
"performance best practices."
msgstr "为达到加速效果，WP Rocket 已自动帮您开启了80%的加速最佳操作。"

#: views/settings/page-sections/dashboard.php:44
msgid ""
"We also enable options that provide immediate benefits to your website."
msgstr "我们同时也帮您的网站启用了能带来实时效果的选项。"

#: views/settings/page-sections/dashboard.php:45
msgid "Continue to the options to further optimize your site!"
msgstr "继续调整设置来进一步优化您的网站吧！"

#: views/settings/page-sections/dashboard.php:62
msgid "My Account"
msgstr "我的账户"

#: views/settings/page-sections/dashboard.php:68
msgid "Refresh info"
msgstr "刷新信息"

#: views/settings/page-sections/dashboard.php:86
msgid "with"
msgstr "与"

#: views/settings/page-sections/dashboard.php:100
msgid "Expiration Date"
msgstr "过期时间"

#: views/settings/page-sections/dashboard.php:110
msgid "View my account"
msgstr "查看我的账户"

#: views/settings/page-sections/dashboard.php:137
msgid "Quick Actions"
msgstr "快捷操作"

#: views/settings/page-sections/dashboard.php:144
msgid "Remove all cached files"
msgstr "清除所有缓存"

#: views/settings/page-sections/dashboard.php:164
#: views/settings/page-sections/dashboard.php:170
msgid "Regenerate Critical CSS"
msgstr "重新生成关键路径 CSS"

#: views/settings/page-sections/dashboard.php:182
msgid "Remove Used CSS Cache"
msgstr "清除已使用CSS缓存"

#: views/settings/page-sections/dashboard.php:205
msgid "Frequently Asked Questions"
msgstr "FAQ"

#: views/settings/page-sections/dashboard.php:219
msgid "Still cannot find a solution?"
msgstr "仍未找到解决方案?"

#: views/settings/page-sections/dashboard.php:220
msgid ""
"Submit a ticket and get help from our friendly and knowledgeable Rocketeers."
msgstr "请发送工单以寻求帮助。我们的火箭工程师非常友好并且知识渊博哦 ~"

#: views/settings/page-sections/dashboard.php:228
msgid "Ask support"
msgstr "请求帮助"

#: views/settings/page-sections/database.php:25
msgid "Backup your database before you run a cleanup!"
msgstr "清理前请务必备份数据库！"

#: views/settings/page-sections/database.php:26
msgid ""
"Once a database optimization has been performed, there is no way to undo it."
msgstr "优化操作一旦运行无法撤销。"

#: views/settings/page-sections/database.php:28
msgid "Save Changes and Optimize"
msgstr "保存并优化"

#. Translators: %1$s = <strong>, %2$s = </strong>, %3$s = <span
#. class="imagify-name">, %4$s = </span>.
#: views/settings/page-sections/imagify.php:21
msgid ""
"%1$sWP ROCKET%2$s created %3$sIMAGIFY%4$s %1$sfor best-in-class image "
"optimization.%2$s"
msgstr "%1$sWP ROCKET%2$s创建了%1$s图片优化神器%2$s %3$sIMAGIFY %4$s"

#: views/settings/page-sections/imagify.php:24
msgid ""
"Compress image to make your website faster, all while maintaining image "
"quality."
msgstr "在确保图片质量的同时压缩图片，让您的网站加载更快。"

#: views/settings/page-sections/imagify.php:25
msgid "More on Imagify:"
msgstr "更多尽在 Imagify:"

#: views/settings/page-sections/imagify.php:27
msgid "Imagify Plugin Page"
msgstr "Imagify 插件介绍页"

#: views/settings/page-sections/imagify.php:28
msgid "Imagify Website"
msgstr "Imagify 网站"

#: views/settings/page-sections/imagify.php:29
msgid "Review of Image Compression Plugins"
msgstr "Imagify 评价"

#: views/settings/page-sections/imagify.php:38
msgid "Install Imagify"
msgstr "安装 Imagify 插件"

#: views/settings/page-sections/license.php:22
msgid "WP Rocket was not able to automatically validate your license."
msgstr "WP Rocket无法自动验证您的授权。"

#. translators: %1$s = tutorial URL, %2$s = support URL.
#: views/settings/page-sections/license.php:29
msgid "Follow this %1$s, or contact %2$s to get the engine started."
msgstr "请参考%1$s，或联系%2$s来开启WP Rocket引擎吧 ~"

#. translators: %1$s = <a href=", %2$s =  tutorial href,  %3$s =  "
#. target="_blank">,  %4$s =  </a>.
#: views/settings/page-sections/license.php:32
msgid "%1$s%2$s%3$stutorial%4$s"
msgstr "%1$s%2$s%3$s教程%4$s"

#: views/settings/page-sections/license.php:34
msgid ""
"https://docs.wp-rocket.me/article/100-resolving-problems-with-license-"
"validation/?utm_source=wp_plugin&utm_medium=wp_rocket"
msgstr ""
"https://docs.wp-rocket.me/article/100-resolving-problems-with-license-"
"validation/?utm_source=wp_plugin&utm_medium=wp_rocket"

#. translators: %1$s = <a href=", %2$s =  support href,  %3$s =  "
#. target="_blank">,  %4$s =  </a>.
#: views/settings/page-sections/license.php:40
msgid "%1$s%2$s%3$ssupport%4$s"
msgstr "%1$s%2$s%3$s支持%4$s"

#: views/settings/page-sections/sucuri.php:46
msgid "Clear all Sucuri cache files"
msgstr "清除所有的Sucuri缓存文件"

#. translators: %1$s = formatted file size, %2$s = formatted number of entries
#. (don't use %2$d).
#: views/settings/page-sections/tools.php:20
msgid "Files size: %1$s. Number of entries: %2$s."
msgstr "文件大小：%1$s。数据行数：%2$s。"

#. translators: %1$s = opening <a> tag, %2$s = closing </a> tag.
#: views/settings/page-sections/tools.php:23
msgid "%1$sDownload the file%2$s."
msgstr "%1$s下载文件%2$s。"

#. translators: %1$s = opening <a> tag, %2$s = closing </a> tag.
#: views/settings/page-sections/tools.php:26
msgid "%1$sDelete the file%2$s."
msgstr "%1$s删除文件%2$s。"

#: views/settings/page-sections/tools.php:37
msgid "Export settings"
msgstr "导出配置"

#: views/settings/page-sections/tools.php:38
msgid "Download a backup file of your settings"
msgstr "下载您的设置备份文件"

#: views/settings/page-sections/tools.php:46
msgid "Download settings"
msgstr "下载配置"

#: views/settings/page-sections/tools.php:60
msgid "Rollback"
msgstr "还原"

#. translators: %s = WP Rocket version number.
#: views/settings/page-sections/tools.php:64
msgid "Has version %s caused an issue on your website?"
msgstr "%s 版本有问题？"

#. translators: %s = <br>.
#: views/settings/page-sections/tools.php:69
msgid ""
"You can rollback to the previous major version here.%sThen send us a support"
" request."
msgstr "您可在此回滚到上个主流版本。%s然后寻求我们的帮助。"

#. translators: %s = WP Rocket previous version.
#: views/settings/page-sections/tools.php:80
msgid "Reinstall version %s"
msgstr "重新安装 %s 版本"

#: views/settings/page-sections/tools.php:106
msgid "Debug mode"
msgstr "调试模式"

#: views/settings/page-sections/tools.php:111
msgid "Create a debug log file."
msgstr "创建一个调试日志文件。"

#: views/settings/page-sections/tutorials.php:13
#: views/settings/partials/getting-started.php:18
msgid "Getting Started"
msgstr "新手指南"

#: views/settings/page-sections/tutorials.php:15
msgid "Getting Started with WP Rocket"
msgstr "玩转 WP Rocket"

#: views/settings/page-sections/tutorials.php:16
#: views/settings/partials/getting-started.php:12
msgid "Finding the Best Settings for Your Site"
msgstr "找到适用您网站的最佳设置"

#: views/settings/page-sections/tutorials.php:17
#: views/settings/partials/getting-started.php:13
msgid "How to Check if WP Rocket is Caching Your Site"
msgstr "如何确认 WP Rocket 缓存已正常工作"

#: views/settings/page-sections/tutorials.php:18
#: views/settings/partials/getting-started.php:14
msgid "How to Measure the Speed of Your Site"
msgstr "如何测试网站加载速度"

#: views/settings/page-sections/tutorials.php:19
msgid "How Preloading Works"
msgstr "预缓存工作原理"

#: views/settings/page-sections/tutorials.php:23
msgid "Passing the Core Web vitals"
msgstr "搞定谷歌核心算法"

#: views/settings/page-sections/tutorials.php:25
msgid "How to improve LCP with WP Rocket"
msgstr "如何用 WP Rocket 优化 LCP"

#: views/settings/page-sections/tutorials.php:26
msgid "How to improve FID with WP Rocket"
msgstr "如何用 WP Rocket 优化 FID"

#: views/settings/page-sections/tutorials.php:27
msgid "How to improve CLS with WP Rocket"
msgstr "如何用 WP Rocket 优化 CLS"

#: views/settings/page-sections/tutorials.php:31
msgid "Troubleshooting"
msgstr "故障排除"

#: views/settings/page-sections/tutorials.php:33
msgid "Troubleshooting Display Issues with File Optimization"
msgstr "排查文件优化引起的显示问题"

#: views/settings/page-sections/tutorials.php:34
msgid "How to Find the Right JavaScript to Exclude"
msgstr "如何找到要排除的 JavaScript"

#: views/settings/page-sections/tutorials.php:35
msgid "How External Content Slows Your Site"
msgstr "外部内容是怎么拖慢您的网站的"

#: views/settings/page-sections/tutorials.php:41
msgid "Set Up the Cloudflare Add-on"
msgstr "设置 Cloudflare 扩展"

#: views/settings/page.php:16
msgid "WP Rocket Settings"
msgstr "WP Rocket设置"

#. translators: %s = Plugin version number.
#: views/settings/page.php:30
msgid "version %s"
msgstr "%s 版本"

#: views/settings/page.php:60
msgid "Show Sidebar"
msgstr "显示侧边栏"

#. translators: %1$s = <strong>, %2$s = </strong>.
#: views/settings/page.php:82
msgid ""
"Below is a detailed view of all data WP Rocket will collect %1$sif granted "
"permission.%2$s"
msgstr "以下详细列出了 WP Rocket 在%1$s获得许可后%2$s将收集的所有数据。"

#: views/settings/page.php:87
msgid ""
"WP Rocket will never transmit any domain names or email addresses (except "
"for license validation), IP addresses, or third-party API keys."
msgstr "WP Rocket 绝不会传输任何域名、邮箱（授权验证除外），IP地址或第三方 API key。"

#: views/settings/page.php:89
msgid "Activate Rocket analytics"
msgstr "启用 Rocket 分析"

#: views/settings/partials/documentation.php:15
msgid "It is a great starting point to fix some of the most common issues."
msgstr "解决常见问题的好去处。"

#: views/settings/partials/documentation.php:22
msgid "Read the documentation"
msgstr "查看文档"

#: views/settings/partials/getting-started.php:11
msgid "What WP Rocket Does For You By Default"
msgstr "WP Rocket 默认情况下都为您做了些什么？"

#: views/settings/partials/sidebar.php:12
msgid "How to correctly measure your website’s loading time"
msgstr "如何正确测试网站加载时间"

#: views/settings/partials/sidebar.php:14
msgid "Check our tutorial and learn how to measure the speed of your site."
msgstr "查看我们的教程并学习如何测试网站加载时间。"

#: views/settings/partials/sidebar.php:15
#: views/settings/partials/sidebar.php:24
msgid "Read our guide"
msgstr "阅读指南"

#: views/settings/partials/sidebar.php:18
msgid "Learn about optimal WP Rocket settings for mobile."
msgstr "学习移动端的 WP Rocket 最佳设置."

#: views/settings/partials/sidebar.php:27
msgid "Test and Improve Google Core Web Vitals for WordPress."
msgstr "为 WordPress 测试和改善谷歌网站核心指标 ( Google Core Web Vitals )."

#: views/settings/partials/sidebar.php:28
msgid "Read more"
msgstr "阅读更多"

#: views/settings/partials/sidebar.php:33
msgid "You have not activated logged-in user cache."
msgstr "您尚未启用已登录用户缓存。"

#: views/settings/partials/sidebar.php:34
msgid ""
"Use a private browser to check your website's speed and visual appearance."
msgstr "请使用浏览器的隐私模式来检查网站速度和外观变化。"

#: views/settings/sections/addons-container.php:24
#: views/settings/sections/fields-container.php:28
msgid "Need Help?"
msgstr "需要帮助吗？"
