# Copyright (C) 2024 WP Media
# This file is distributed under the same license as the WP Rocket plugin.
# Translators:
# <AUTHOR> <EMAIL>, 2019
# <PERSON><PERSON><PERSON> <<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@gmail.com>, 2021
# <PERSON> <<EMAIL>>, 2023
# BouRock, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: WP Rocket 3.16-alpha2\n"
"Report-Msgid-Bugs-To: http://wp-rocket.me/\n"
"POT-Creation-Date: 2024-05-06T13:28:35+03:00\n"
"PO-Revision-Date: 2019-08-26 15:14+0000\n"
"Last-Translator: BouRock, 2024\n"
"Language-Team: Turkish (Turkey) (https://app.transifex.com/wp-media/teams/18133/tr_TR/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: tr_TR\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"
"X-Domain: rocket\n"
"X-Generator: WP-CLI 2.7.1\n"

#. Plugin Name of the plugin
msgid "WP Rocket"
msgstr "WP Rocket"

#. Plugin URI of the plugin
msgid "https://wp-rocket.me"
msgstr "https://wp-rocket.me"

#. Description of the plugin
msgid "The best WordPress performance plugin."
msgstr "En iyi WordPress performans eklentisi."

#. Author of the plugin
msgid "WP Media"
msgstr "WP Media"

#. Author URI of the plugin
msgid "https://wp-media.me"
msgstr "https://wp-media.me"

#. Translators: %s = Hosting name.
#: inc/3rd-party/hosting/flywheel.php:18
#: inc/3rd-party/hosting/wp-serveur.php:27 inc/deprecated/3.6.php:697
#: inc/deprecated/3.6.php:997 inc/deprecated/3.9.php:22
#: inc/ThirdParty/Hostings/Cloudways.php:90
#: inc/ThirdParty/Hostings/Dreampress.php:44
#: inc/ThirdParty/Hostings/Godaddy.php:63
#: inc/ThirdParty/Hostings/O2Switch.php:49
#: inc/ThirdParty/Hostings/OneCom.php:137
#: inc/ThirdParty/Hostings/Pressidium.php:49
#: inc/ThirdParty/Hostings/ProIsp.php:51 inc/ThirdParty/Hostings/Savvii.php:50
#: inc/ThirdParty/Hostings/WPEngine.php:47
#: inc/ThirdParty/Hostings/WPXCloud.php:51
msgid ""
"Your site is hosted on %s, we have enabled Varnish auto-purge for "
"compatibility."
msgstr ""
"Siteniz %s üzerinde barındırılıyor, uyumluluk için Varnish oto-temizle’yi "
"etkinleştirdik."

#: inc/Addon/Cloudflare/API/Client.php:129
msgid "Cloudflare did not provide any reply. Please try again later."
msgstr "Cloudflare hiçbir yanıt vermedi. Lütfen daha sonra tekrar deneyin."

#: inc/Addon/Cloudflare/API/Client.php:135
msgid "Cloudflare unexpected response"
msgstr "Cloudflare beklenmedik yanıtı"

#: inc/Addon/Cloudflare/API/Client.php:143
msgid "Missing Cloudflare result."
msgstr "Eksik Cloudflare sonucu."

#: inc/Addon/Cloudflare/API/Client.php:194 inc/deprecated/3.5.php:112
#: inc/deprecated/3.5.php:169
msgid "Incorrect Cloudflare email address or API key."
msgstr "Yanlış Cloudflare e-posta adresi veya API anahtarı."

#. translators: %1$s = opening link; %2$s = closing link
#: inc/Addon/Cloudflare/API/Client.php:198
#: inc/Addon/Cloudflare/API/Client.php:212
#: inc/Addon/Cloudflare/Cloudflare.php:75
#: inc/Addon/Cloudflare/Cloudflare.php:108
#: inc/classes/subscriber/Tools/class-detect-missing-tags-subscriber.php:157
#: inc/deprecated/3.5.php:92 inc/deprecated/3.5.php:116
#: inc/deprecated/3.5.php:129 inc/deprecated/3.5.php:157
#: inc/deprecated/3.5.php:173
msgid "Read the %1$sdocumentation%2$s for further guidance."
msgstr "İleriki rehberlik için %1$sbelgeleri%2$s okuyun."

#. translators: Documentation exists in EN, FR; use localized URL if
#. applicable.
#: inc/Addon/Cloudflare/API/Client.php:200
#: inc/Addon/Cloudflare/API/Client.php:214
#: inc/Addon/Cloudflare/Auth/APIKey.php:63
#: inc/Addon/Cloudflare/Cloudflare.php:77
#: inc/Addon/Cloudflare/Cloudflare.php:110 inc/deprecated/3.5.php:81
#: inc/deprecated/3.5.php:94 inc/deprecated/3.5.php:118
#: inc/deprecated/3.5.php:131 inc/deprecated/3.5.php:159
#: inc/deprecated/3.5.php:175 inc/deprecated/3.5.php:208
msgid ""
"https://docs.wp-rocket.me/article/18-using-wp-rocket-with-"
"cloudflare/?utm_source=wp_plugin&utm_medium=wp_rocket#add-on"
msgstr ""
"https://docs.wp-rocket.me/article/18-using-wp-rocket-with-"
"cloudflare/?utm_source=wp_plugin&utm_medium=wp_rocket#add-on"

#: inc/Addon/Cloudflare/API/Client.php:208 inc/deprecated/3.5.php:125
msgid "Incorrect Cloudflare Zone ID."
msgstr "Yanlış Cloudflare Bölge Kimliği."

#. translators: %1$s = opening link; %2$s = closing link
#: inc/Addon/Cloudflare/Auth/APIKey.php:61
msgid ""
"Cloudflare email and/or API key are not set. Read the %1$sdocumentation%2$s "
"for further guidance."
msgstr ""
"Cloudflare e-posta adresi ve/veya API anahtarı ayarlı değil. Daha fazla "
"rehberlik için %1$sbelgeleri%2$s okuyun."

#: inc/Addon/Cloudflare/Cloudflare.php:71 inc/deprecated/3.5.php:88
msgid "Missing Cloudflare Zone ID."
msgstr "Eksik Cloudflare Bölge Kimliği."

#: inc/Addon/Cloudflare/Cloudflare.php:104 inc/deprecated/3.5.php:153
msgid "It looks like your domain is not set up on Cloudflare."
msgstr "Etki alanınız Cloudflare’de ayarlanmamış gibi görünüyor."

#: inc/Addon/Cloudflare/Cloudflare.php:200
msgid "days"
msgstr "gün"

#: inc/Addon/Cloudflare/Cloudflare.php:203
msgid "seconds"
msgstr "saniye"

#: inc/Addon/Cloudflare/Cloudflare.php:205
msgid "minutes"
msgstr "dakika"

#: inc/Addon/Cloudflare/Cloudflare.php:207
msgid "hours"
msgstr "saat"

#. translators: %1$s = <strong>, %2$s = </strong>, %3$s = CloudFare API return
#. message.
#: inc/Addon/Cloudflare/Subscriber.php:232
#: inc/Addon/Cloudflare/Subscriber.php:261
msgid "%1$sWP Rocket:%2$s %3$s"
msgstr "%1$sWP Rocket:%2$s %3$s"

#. translators: %1$s = <strong>, %2$s = </strong>.
#: inc/Addon/Cloudflare/Subscriber.php:250
msgid "%1$sWP Rocket:%2$s Cloudflare cache successfully purged."
msgstr "%1$sWP Rocket:%2$s Cloudflare önbelleği başarılı olarak temizlendi."

#. translators: %s is the message returned by the CloudFlare API.
#: inc/Addon/Cloudflare/Subscriber.php:311
msgid "Cloudflare development mode error: %s"
msgstr "Cloudflare geliştirme kipi hatası: %s"

#. translators: %s is the message returned by the CloudFlare API.
#: inc/Addon/Cloudflare/Subscriber.php:318
msgid "Cloudflare development mode %s"
msgstr "Cloudflare geliştirme kipi %s"

#. translators: %s is the message returned by the CloudFlare API.
#: inc/Addon/Cloudflare/Subscriber.php:337
msgid "Cloudflare cache level error: %s"
msgstr "Cloudflare önbellek seviyesi hatası: %s"

#: inc/Addon/Cloudflare/Subscriber.php:344
msgctxt "Cloudflare caching level"
msgid "standard"
msgstr "standart"

#. translators: %s is the caching level returned by the CloudFlare API.
#: inc/Addon/Cloudflare/Subscriber.php:350
msgid "Cloudflare cache level set to %s"
msgstr "Cloudflare önbellek seviyesi %s olarak ayarlandı"

#. translators: %s is the message returned by the CloudFlare API.
#: inc/Addon/Cloudflare/Subscriber.php:368
msgid "Cloudflare minification error: %s"
msgstr "Cloudflare küçültme hatası: %s"

#. translators: %s is the message returned by the CloudFlare API.
#: inc/Addon/Cloudflare/Subscriber.php:375
msgid "Cloudflare minification %s"
msgstr "Cloudflare küçültme %s"

#. translators: %s is the message returned by the CloudFlare API.
#: inc/Addon/Cloudflare/Subscriber.php:393
msgid "Cloudflare rocket loader error: %s"
msgstr "Cloudflare rocket yükleyici hatası: %s"

#. translators: %s is the message returned by the CloudFlare API.
#: inc/Addon/Cloudflare/Subscriber.php:400
msgid "Cloudflare rocket loader %s"
msgstr "Cloudflare rocket yükleyici %s"

#. translators: %s is the message returned by the CloudFlare API.
#: inc/Addon/Cloudflare/Subscriber.php:418
msgid "Cloudflare browser cache error: %s"
msgstr "Cloudflare tarayıcı önbelleği hatası: %s"

#. translators: %s is the message returned by the CloudFlare API.
#: inc/Addon/Cloudflare/Subscriber.php:425
msgid "Cloudflare browser cache set to %s"
msgstr "Cloudflare tarayıcı önbelleği %s olarak ayarlandı"

#. translators: %1$s = strong opening tag, %2$s = strong closing tag.
#: inc/Addon/Cloudflare/Subscriber.php:536
msgid "%1$sWP Rocket:%2$s Optimal settings activated for Cloudflare:"
msgstr "%1$sWP Rocket:%2$s Cloudflare için en uygun ayarlar aktif edildi:"

#. translators: %1$s = strong opening tag, %2$s = strong closing tag.
#: inc/Addon/Cloudflare/Subscriber.php:545
msgid ""
"%1$sWP Rocket:%2$s Optimal settings deactivated for Cloudflare, reverted to "
"previous settings:"
msgstr ""
"%1$sWP Rocket:%2$s Cloudflare için en uygun ayarlar devre dışı bırakıldı, "
"önceki ayarlara geri döndürüldü:"

#: inc/Addon/Cloudflare/Subscriber.php:661 inc/admin/options.php:165
#: inc/classes/subscriber/Tools/class-detect-missing-tags-subscriber.php:148
msgid "WP Rocket: "
msgstr "WP Rocket: "

#. translators: %s is the error message returned by the API.
#: inc/Addon/Sucuri/Subscriber.php:96
msgid "Sucuri cache purge error: %s"
msgstr "Sucuri önbellek temizleme hatası: %s"

#: inc/Addon/Sucuri/Subscriber.php:101
msgid ""
"The Sucuri cache is being cleared. Note that it may take up to two minutes "
"for it to be fully flushed."
msgstr ""
"Sucuri önbelleği temizlenmekte. Tamamen temizlenmesinin iki dakika kadar "
"sürebileceğini unutmayın."

#: inc/Addon/Sucuri/Subscriber.php:218
msgid "Sucuri firewall API key was not found."
msgstr "Sucuri güvenlik duvarı API anahtarı bulunamadı."

#: inc/Addon/Sucuri/Subscriber.php:231
msgid "Sucuri firewall API key is invalid."
msgstr "Sucuri güvenlik duvarı API anahtarı geçersiz."

#. translators: %s is an error message.
#: inc/Addon/Sucuri/Subscriber.php:286
msgid "Error when contacting Sucuri firewall API. Error message was: %s"
msgstr ""
"Sucuri güvenlik duvarı API’si ile iletişime geçerken hata oldu. Hata "
"iletisi: %s"

#: inc/Addon/Sucuri/Subscriber.php:301
msgid "Could not get a response from the Sucuri firewall API."
msgstr "Sucuri güvenlik duvarı API’sinden bir yanıt alınamadı."

#: inc/Addon/Sucuri/Subscriber.php:316
msgid "Got an invalid response from the Sucuri firewall API."
msgstr "Sucuri güvenlik duvarı API’sinden geçersiz bir yanıt alındı."

#: inc/Addon/Sucuri/Subscriber.php:330
msgid "The Sucuri firewall API returned an unknown error."
msgstr "Sucuri güvenlik duvarı API’si bilinmeyen bir hata döndürdü."

#. translators: %s is an error message.
#: inc/Addon/Sucuri/Subscriber.php:334
msgid "The Sucuri firewall API returned the following error: %s"
msgid_plural "The Sucuri firewall API returned the following errors: %s"
msgstr[0] ""
"Sucuri güvenlik duvarı API’si şurada belirtilen hatayı döndürdü: %s"
msgstr[1] ""
"Sucuri güvenlik duvarı API’si şurada belirtilen hataları döndürdü: %s"

#. Translators: %1$s = plugin name(s), %2$s = opening <a> tag, %3$s = closing
#. </a> tag.
#: inc/Addon/WebP/AdminSubscriber.php:93
msgid ""
"You are using %1$s to serve WebP images so you do not need to enable this "
"option. %2$sMore info%3$s %4$s If you prefer to have WP Rocket serve WebP "
"for you instead, please disable WebP display in %1$s."
msgid_plural ""
"You are using %1$s to serve WebP images so you do not need to enable this "
"option. %2$sMore info%3$s %4$s If you prefer to have WP Rocket serve WebP "
"for you instead, please disable WebP display in %1$s."
msgstr[0] ""
"WebP resimlerini sunmak için %1$s kullanıyorsunuz bu yüzden bu seçeneği "
"etkinleştirmeniz gerekmiyor. %2$sDaha fazla bilgi%3$s %4$s Bu hizmeti "
"WebP’nin yerine sizin için WP Rocket’in sunmasını tercih ederseniz, lütfen "
"%1$s içinde WebP görüntülemeyi etkisizleştirin."
msgstr[1] ""
"WebP resimlerini sunmak için %1$s kullanıyorsunuz bu yüzden bu seçeneği "
"etkinleştirmeniz gerekmiyor. %2$sDaha fazla bilgi%3$s %4$s Bu hizmeti "
"WebP’nin yerine sizin için WP Rocket’in sunmasını tercih ederseniz, lütfen "
"%1$s içinde WebP görüntülemeyi etkisizleştirin."

#: inc/Addon/WebP/AdminSubscriber.php:105
msgid "WebP cache is disabled by filter."
msgstr "WebP önbelleği, süzgeç tarafından etkisizleştirildi."

#. Translators: %1$s = plugin name(s), %2$s = opening <a> tag, %3$s = closing
#. </a> tag.
#: inc/Addon/WebP/AdminSubscriber.php:115
#: inc/Addon/WebP/AdminSubscriber.php:141
msgid ""
"You are using %1$s to convert images to WebP. If you want WP Rocket to serve"
" them for you, activate this option. %2$sMore info%3$s"
msgid_plural ""
"You are using %1$s to convert images to WebP. If you want WP Rocket to serve"
" them for you, activate this option. %2$sMore info%3$s"
msgstr[0] ""
"Resimleri WebP’ye dönüştürmek için %1$s kullanıyorsunuz. WP Rocket’in "
"bunları sizin için sunmasını istiyorsanız, bu seçeneği aktif edin. %2$sDaha "
"fazla bilgi%3$s"
msgstr[1] ""
"Resimleri WebP’ye dönüştürmek için %1$s kullanıyorsunuz. WP Rocket’in "
"bunları sizin için sunmasını istiyorsanız, bu seçeneği aktif edin. %2$sDaha "
"fazla bilgi%3$s"

#. Translators: %1$s = plugin name(s), %2$s = opening <a> tag, %3$s = closing
#. </a> tag.
#: inc/Addon/WebP/AdminSubscriber.php:127
#: inc/Addon/WebP/AdminSubscriber.php:153
msgid ""
"You are using %1$s to convert images to WebP. WP Rocket will create separate"
" cache files to serve your WebP images. %2$sMore info%3$s"
msgid_plural ""
"You are using %1$s to convert images to WebP. WP Rocket will create separate"
" cache files to serve your WebP images. %2$sMore info%3$s"
msgstr[0] ""
"Resimleri WebP’ye dönüştürmek için %1$s kullanıyorsunuz. WP Rocket, WebP "
"resimlerinizi sunmak için ayrı önbellek dosyaları oluşturacak. %2$sDaha "
"fazla bilgi%3$s"
msgstr[1] ""
"Resimleri WebP’ye dönüştürmek için %1$s kullanıyorsunuz. WP Rocket, WebP "
"resimlerinizi sunmak için ayrı önbellek dosyaları oluşturacak. %2$sDaha "
"fazla bilgi%3$s"

#. Translators: %1$s = opening <a> tag, %2$s = closing </a> tag.
#: inc/Addon/WebP/AdminSubscriber.php:173
msgid ""
"%5$sWe have not detected any compatible WebP plugin!%6$s%4$s If you don’t "
"already have WebP images on your site consider using %3$sImagify%2$s or "
"another supported plugin. %1$sMore info%2$s %4$s If you are not using WebP "
"do not enable this option."
msgstr ""
"%5$sHerhangi bir uyumlu WebP eklentisi tespit etmedik!%6$s%4$s Eğer "
"sitenizde zaten WebP resimleriniz yoksa, %3$sImagify%2$s veya desteklenen "
"başka bir eklenti kullanmayı düşünün. %1$sDaha fazla bilgi%2$s %4$s Eğer "
"WebP kullanmıyorsanız, bu seçeneği etkinleştirmeyin."

#: inc/Addon/WebP/AdminSubscriber.php:185
msgid "WP Rocket will create separate cache files to serve your WebP images."
msgstr ""
"WP Rocket, WebP resimlerinizi sunmak için ayrı önbellek dosyaları "
"oluşturacak."

#: inc/admin/admin.php:18 inc/common/admin-bar.php:273
#: inc/deprecated/deprecated.php:1787
msgid "Support"
msgstr "Destek"

#: inc/admin/admin.php:20
msgid "Docs"
msgstr "Belgeler"

#: inc/admin/admin.php:22 inc/common/admin-bar.php:261
msgid "FAQ"
msgstr "SSS"

#: inc/admin/admin.php:24 inc/common/admin-bar.php:70
msgid "Settings"
msgstr "Ayarlar"

#: inc/admin/admin.php:96 inc/admin/admin.php:117 inc/deprecated/3.5.php:898
#: inc/Engine/Cache/AdminSubscriber.php:134
msgid "Clear this cache"
msgstr "Bu önbelleği temizle"

#: inc/admin/admin.php:402
msgid "Settings import failed: you do not have the permissions to do this."
msgstr "Ayarları içe aktarma başarısız oldu: bunu yapmak için izniniz yok."

#: inc/admin/admin.php:406
msgid "Settings import failed: no file uploaded."
msgstr "Ayarları içe aktarma başarısız oldu: gönderilen dosya yok."

#: inc/admin/admin.php:410
msgid "Settings import failed: incorrect filename."
msgstr "Ayarları içe aktarma başarısız oldu: yanlış dosya adı."

#: inc/admin/admin.php:421
msgid "Settings import failed: incorrect filetype."
msgstr "Ayarları içe aktarma başarısız oldu: yanlış dosya türü."

#: inc/admin/admin.php:431
msgid "Settings import failed: "
msgstr "Ayarları içe aktarma başarısız oldu:"

#: inc/admin/admin.php:447
msgid "Settings import failed: unexpected file content."
msgstr "Ayarları içe aktarma başarısız oldu: beklenmeyen dosya içeriği."

#: inc/admin/admin.php:492
msgid "Settings imported and saved."
msgstr "Ayarlar içe aktarıldı ve kaydedildi."

#: inc/admin/options.php:102 inc/Engine/Admin/Settings/Page.php:571
msgid "Excluded CSS Files"
msgstr "Hariç Tutulan CSS Dosyaları"

#: inc/admin/options.php:103 inc/Engine/Admin/Settings/Page.php:725
msgid "Excluded Inline JavaScript"
msgstr "Hariç Tutulan Satır İçi JavaScript"

#: inc/admin/options.php:104 inc/Engine/Admin/Settings/Page.php:743
#: inc/Engine/Admin/Settings/Page.php:776
#: inc/Engine/Admin/Settings/Page.php:823
msgid "Excluded JavaScript Files"
msgstr "Hariç Tutulan JavaScript Dosyaları"

#: inc/admin/options.php:105
msgid "Defer JavaScript Files"
msgstr "JavaScript Dosyalarını Ertele"

#: inc/admin/options.php:106
msgid "Excluded Delay JavaScript Files"
msgstr "Hariç Tutulan JavaScript Dosyalarını Geciktirme"

#: inc/admin/options.php:107 inc/Engine/Admin/Settings/Page.php:1229
msgid "Never Cache URL(s)"
msgstr "Asla Önbelleklenmeyen URL(ler)"

#: inc/admin/options.php:108 inc/Engine/Admin/Settings/Page.php:1243
msgid "Never Cache User Agent(s)"
msgstr "Asla Önbelleklenmeyen Kullanıcı Tanıtıcı(ları)sı"

#: inc/admin/options.php:109 inc/Engine/Admin/Settings/Page.php:1249
msgid "Always Purge URL(s)"
msgstr "Her Zaman Temizlenen URL(ler)"

#: inc/admin/options.php:110 inc/Engine/Admin/Settings/Page.php:1530
msgid "Exclude files from CDN"
msgstr "CDN’den hariç tutulan dosyalar"

#. translators: 1 and 2 can be anything.
#: inc/admin/options.php:131
msgid "%1$s: <em>%2$s</em>"
msgstr "%1$s: <em>%2$s</em>"

#: inc/admin/options.php:141
msgid "The following pattern is invalid and has been removed:"
msgid_plural "The following patterns are invalid and have been removed:"
msgstr[0] "Aşağıdaki desen geçersizdir ve kaldırıldı:"
msgstr[1] "Aşağıdaki desenler geçersizdir ve kaldırıldı:"

#: inc/admin/options.php:157
msgid "More info"
msgstr "Daha fazla bilgi"

#: inc/admin/ui/meta-boxes.php:37 inc/admin/ui/notices.php:671
#: inc/common/admin-bar.php:91 inc/common/admin-bar.php:154
#: views/settings/page-sections/dashboard.php:150
msgid "Clear cache"
msgstr "Önbelleği temizle"

#. translators: %1$s WP Rocket plugin name; %2$s = file name.
#: inc/admin/ui/notices.php:30 inc/admin/ui/notices.php:43
msgid ""
"<strong>%1$s</strong> has not been deactivated due to missing writing permissions.<br>\n"
"Make <strong>%2$s</strong> writeable and retry deactivation, or force deactivation now:"
msgstr ""
"<strong>%1$s</strong>, eksik yazma izinlerinden dolayı devre dışı bırakılmadı.<br>\n"
"<strong>%2$s</strong> yazılabilir yapın ve devre dışı bırakmayı yeniden deneyin, ya da şimdi devre dışı bırakmaya zorlayın:"

#. translators: %s is WP Rocket plugin name.
#: inc/admin/ui/notices.php:97
msgid ""
"<strong>%s</strong>: One or more plugins have been enabled or disabled, "
"clear the cache if they affect the front end of your site."
msgstr ""
"<strong>%s</strong>: Bir ya da daha fazla eklenti etkinleştirildi veya "
"etkisizleştirildi, sitenizin ön ucunu etkilerse önbelleği temizleyin."

#. translators: %s is WP Rocket plugin name.
#: inc/admin/ui/notices.php:141
msgid ""
"<strong>%s</strong>: The following plugins are not compatible with this "
"plugin and may cause unexpected results:"
msgstr ""
"<strong>%s</strong>: Aşağıdaki eklentiler bu eklenti ile uyumlu değil ve "
"beklenmeyen sonuçlara neden olabilir:"

#: inc/admin/ui/notices.php:147
msgid "Deactivate"
msgstr "Devre Dışı Bırak"

#: inc/admin/ui/notices.php:189
msgid ""
"WP Rocket Footer JS is not an official add-on. It prevents some options in "
"WP Rocket from working correctly. Please deactivate it if you have problems."
msgstr ""
"WP Rocket Footer JS resmi bir eklenti değildir. WP Rocket içindeki bazı "
"seçeneklerin doğru olarak çalışmasını önler. Eğer sorunlar yaşarsanız lütfen"
" devre dşı bırakın."

#. translators: %1$s = opening link tag, %2$s = closing link tag.
#: inc/admin/ui/notices.php:229
msgid ""
"Endurance Cache is currently enabled, which will conflict with WP Rocket "
"Cache. Please set the Endurance Cache cache level to Off (Level 0) on the "
"%1$sSettings > General%2$s page to prevent any issues."
msgstr ""
"WP Rocket Önbelleği ile çakışacak olan Endurance Önbelleği şu anda "
"etkinleştirildi. Herhangi bir sorunu önlemek için lütfen %1$sAyarlar > "
"Genel%2$s sayfasında Endurance Önbelleğinin önbellek seviyesini Kapalı "
"(Seviye 0) olarak ayarlayın."

#. translators: %1$s WP Rocket plugin name; %2$s = opening link; %3$s =
#. closing link
#: inc/admin/ui/notices.php:250
msgid ""
"%1$s: A custom permalink structure is required for the plugin to work "
"properly. %2$sGo to permalinks settings%3$s"
msgstr ""
"%1$s: Özel bir kalıcı bağlantı yapısı eklentinin düzgün bir şekilde "
"çalışması için gereklidir. %2$sKalıcı bağlantılar ayarlarına gidin%3$s"

#. translators: %s = plugin name.
#: inc/admin/ui/notices.php:297
msgid ""
"%s could not modify the .htaccess file due to missing writing permissions."
msgstr ""
"Eksik yazma izinlerinden dolayı %s .htaccess dosyasını yapılandıramadı."

#. translators: This is a doc title! %1$s = opening link; %2$s = closing link
#: inc/admin/ui/notices.php:303 inc/admin/ui/notices.php:788
msgid "Troubleshoot: %1$sHow to make system files writeable%2$s"
msgstr ""
"Sorun giderme: %1$sSistem dosyalarını nasıl yazılabilir yaparsınız%2$s"

#. translators: Documentation exists in EN, DE, FR, ES, IT; use loaclised URL
#. if applicable
#: inc/admin/ui/notices.php:305 inc/admin/ui/notices.php:790
msgid ""
"https://docs.wp-rocket.me/article/626-how-to-make-system-files-htaccess-wp-"
"config-writeable/?utm_source=wp_plugin&utm_medium=wp_rocket"
msgstr ""
"https://docs.wp-rocket.me/article/626-how-to-make-system-files-htaccess-wp-"
"config-writeable/?utm_source=wp_plugin&utm_medium=wp_rocket"

#: inc/admin/ui/notices.php:311
msgid ""
"Don’t worry, WP Rocket’s page caching and settings will still function "
"correctly."
msgstr ""
"Endişelenmeyin, WP Rocket’in sayfa önbelleklemesi ve ayarları hala düzgün "
"çalışacaktır."

#: inc/admin/ui/notices.php:311
msgid ""
"For optimal performance, adding the following lines into your .htaccess is "
"recommended (not required):"
msgstr ""
"En iyi performans için .htaccess dosyanıza aşağıdaki satırları eklemeniz "
"önerilir (gerekli değil):"

#. translators: %1$s = plugin name, %2$s + %3$s = opening links, %4$s =
#. closing link
#: inc/admin/ui/notices.php:458
msgid ""
"%1$s is good to go! %2$sTest your load time%4$s, or visit your "
"%3$ssettings%4$s."
msgstr ""
"%1$s gitmeye hazır! %2$sYükleme sürenizi deneyin%4$s, ya da "
"%3$sayarlarınızı%4$s ziyaret edin."

#: inc/admin/ui/notices.php:499
msgid ""
"Would you allow WP Rocket to collect non-sensitive diagnostic data from this"
" website?"
msgstr ""
"WP Rocket’in hassas olmayan tanılama verilerini bu web sitesinden "
"toplamasına izin verir misiniz?"

#: inc/admin/ui/notices.php:500
msgid "This would help us to improve WP Rocket for you in the future."
msgstr ""
"Bu bize gelecekte WP Rocket’i sizin için iyileştirmemize yardımcı olacak."

#. translators: button text, click will expand data collection preview
#: inc/admin/ui/notices.php:506
msgid "What info will we collect?"
msgstr "Ne tür bilgi toplayacağız?"

#: inc/admin/ui/notices.php:511
msgid ""
"Below is a detailed view of all data WP Rocket will collect if granted "
"permission. WP Rocket will never transmit any domain names or email "
"addresses (except for license validation), IP addresses, or third-party API "
"keys."
msgstr ""
"Aşağıdakiler, izin verildiği takdirde WP Rocket’in toplayacağı tüm verilerin"
" ayrıntılı bir görünümüdür. WP Rocket asla herhangi bir etki alanı adı veya "
"e-posta adresini (lisans doğrulaması hariç), IP adresini ya da üçüncü taraf "
"API anahtarını iletmez."

#. translators: button text for data collection opt-in
#: inc/admin/ui/notices.php:520
msgid "Yes, allow"
msgstr "Evet, izin ver"

#. translators: button text for data collection opt-in
#: inc/admin/ui/notices.php:523
msgid "No, thanks"
msgstr "Hayır, teşekkürler"

#: inc/admin/ui/notices.php:562
msgid "Thank you!"
msgstr "Teşekkür ederiz!"

#: inc/admin/ui/notices.php:567
msgid "WP Rocket now collects these metrics from your website:"
msgstr "WP Rocket web sitenizden şimdi şu ölçümleri toplar:"

#. translators: %s = plugin name.
#: inc/admin/ui/notices.php:605
msgid "%s: Cache cleared."
msgstr "%s: Önbellek temizlendi."

#. translators: %s = plugin name.
#: inc/admin/ui/notices.php:612
msgid "%s: Post cache cleared."
msgstr "%s: Yazı önbelleği temizlendi."

#. translators: %s = plugin name.
#: inc/admin/ui/notices.php:619
msgid "%s: Term cache cleared."
msgstr "%s: Terim önbelleği temizlendi."

#. translators: %s = plugin name).
#: inc/admin/ui/notices.php:626
msgid "%s: User cache cleared."
msgstr "%s: Kullanıcı önbelleği temizlendi."

#: inc/admin/ui/notices.php:662 inc/Engine/License/views/promo-banner.php:36
#: inc/Engine/License/views/renewal-expired-banner-ocd-disabled.php:32
#: inc/Engine/License/views/renewal-expired-banner-ocd.php:42
#: inc/Engine/License/views/renewal-expired-banner.php:30
#: views/settings/page-sections/dashboard.php:46
msgid "Dismiss this notice"
msgstr "Bu bildirimi anımsatma"

#: inc/admin/ui/notices.php:682 inc/Engine/Saas/Admin/AdminBar.php:84
#: inc/Engine/Saas/Admin/AdminBar.php:202
msgid "Clear Used CSS"
msgstr "Kullanılan CSS’yi Temizle"

#: inc/admin/ui/notices.php:685
msgid "Stop Preload"
msgstr "Önyüklemeyi durdur"

#: inc/admin/ui/notices.php:691
msgid "Turn on Remove Unused CSS"
msgstr "Kullanılmayan CSS’yi Kaldır’ı Aç"

#: inc/admin/ui/notices.php:697
msgid "Enable “Separate Cache Files for Mobile Devices” now"
msgstr "“Mobil Cihazlar İçin Önbellek Dosyalarını Ayır”ı şimdi etkinleştir"

#: inc/admin/ui/notices.php:715
msgid "Force deactivation "
msgstr "Devre dışı bırakmaya zorla"

#: inc/admin/ui/notices.php:745
msgid "The following code should have been written to this file:"
msgstr "Aşağıdaki kod bu dosyaya yazılmalıdır:"

#. translators: %s = plugin name.
#: inc/admin/ui/notices.php:776
msgid "%s cannot configure itself due to missing writing permissions."
msgstr "Eksik yazma izinlerinden dolayı %s kendini yapılandıramıyor."

#. translators: %s = file/folder name
#: inc/admin/ui/notices.php:782
msgid "Affected file/folder: %s"
msgstr "Etkilenen dosya/klasör: %s"

#: inc/classes/admin/class-logs.php:124
msgid "The debug file could not be deleted."
msgstr "Hata ayıklama dosyası silinemedi."

#. Translators: %1$s = Plugin name, %2$s = Plugin version.
#: inc/classes/class-wp-rocket-requirements-check.php:147
msgid "To function properly, %1$s %2$s requires at least:"
msgstr "Düzgün bir şekilde işlemesi için %1$s %2$s en az gerektirdiği:"

#. Translators: %1$s = PHP version required.
#: inc/classes/class-wp-rocket-requirements-check.php:151
msgid ""
"PHP %1$s. To use this WP Rocket version, please ask your web host how to "
"upgrade your server to PHP %1$s or higher."
msgstr ""
"PHP %1$s. Bu WP Rocket sürümünü kullanmak için lütfen web barındırıcınıza "
"sunucunuzu PHP %1$s veya daha yükseğine nasıl yükselteceğinizi sorun."

#. Translators: %1$s = WordPress version required.
#: inc/classes/class-wp-rocket-requirements-check.php:156
msgid ""
"WordPress %1$s. To use this WP Rocket version, please upgrade WordPress to "
"version %1$s or higher."
msgstr ""
"WordPress %1$s. Bu WP Rocket sürümünü kullanmak için lütfen WordPress’i %1$s"
" sürümü veya daha yükseğine yükseltin."

#: inc/classes/class-wp-rocket-requirements-check.php:159
msgid ""
"If you are not able to upgrade, you can rollback to the previous version by "
"using the button below."
msgstr ""
"Eğer yükseltemiyorsanız, aşağıdaki düğmeyi kullanarak önceki sürüme geri "
"alabilirsiniz."

#. Translators: %s = Previous plugin version.
#: inc/classes/class-wp-rocket-requirements-check.php:161
#: inc/deprecated/deprecated.php:1947
msgid "Re-install version %s"
msgstr "%s sürümünü yeniden yükle"

#. translators: %s is the plugin name.
#: inc/classes/class-wp-rocket-requirements-check.php:203
#: inc/classes/class-wp-rocket-requirements-check.php:214
#: inc/deprecated/3.11.php:236 inc/deprecated/3.11.php:251
#: inc/Engine/Plugin/UpdaterSubscriber.php:483
#: inc/Engine/Plugin/UpdaterSubscriber.php:497
msgid "%s Update Rollback"
msgstr "%s Güncellemesi Geri Alma"

#. translators: %1$s = missing tags;
#: inc/classes/subscriber/Tools/class-detect-missing-tags-subscriber.php:151
msgid ""
"Failed to detect the following requirement in your theme: closing %1$s."
msgid_plural ""
"Failed to detect the following requirements in your theme: closing %1$s."
msgstr[0] ""
"Temanızdaki şurada belirtilen gereksinimi algılama başarısız: %1$s "
"kapatılıyor."
msgstr[1] ""
"Temanızdaki şurada belirtilen gereksinimleri algılama başarısız: %1$s "
"kapatılıyor."

#. translators: Documentation exists in EN, FR; use localized URL if
#. applicable.
#: inc/classes/subscriber/Tools/class-detect-missing-tags-subscriber.php:159
msgid ""
"https://docs.wp-rocket.me/article/99-pages-not-cached-or-minify-cssjs-not-"
"working/?utm_source=wp_plugin&utm_medium=wp_rocket#theme"
msgstr ""
"https://docs.wp-rocket.me/article/99-pages-not-cached-or-minify-cssjs-not-"
"working/?utm_source=wp_plugin&utm_medium=wp_rocket#theme"

#: inc/common/admin-bar.php:91 inc/common/admin-bar.php:154
#: views/settings/page-sections/dashboard.php:150
msgid "Clear and preload cache"
msgstr "Önbelleği temizle ve önyükle"

#: inc/common/admin-bar.php:131 inc/functions/i18n.php:20
msgid "All languages"
msgstr "Tüm diller"

#: inc/common/admin-bar.php:178
msgid "Clear this post"
msgstr "Bu yazıyı temizle"

#: inc/common/admin-bar.php:192
msgid "Purge this URL"
msgstr "Bu URL’yi temizle"

#: inc/common/admin-bar.php:212
msgid "Purge Sucuri cache"
msgstr "Sucuri önbelleğini temizle"

#: inc/common/admin-bar.php:236 views/settings/fields/rocket-cdn.php:55
msgid "Purge RocketCDN cache"
msgstr "RocketCDN önbelleğini temizle"

#: inc/common/admin-bar.php:249 views/settings/partials/documentation.php:14
msgid "Documentation"
msgstr "Belgeler"

#: inc/deprecated/3.2.php:52 views/settings/page-sections/imagify.php:36
msgid "Activate Imagify"
msgstr "Imagify’ı Etkinleştir"

#: inc/deprecated/3.2.php:52
msgid "Install Imagify for Free"
msgstr "Imagify’ı Ücretsiz Yükleyin"

#: inc/deprecated/3.2.php:67
msgid ""
"Speed up your website and boost your SEO by reducing image file sizes "
"without losing quality with Imagify."
msgstr ""
"Imagify ile resim dosyası boyutlarını kalite kaybetmeden küçülterek web "
"sitenizi hızlandırın ve SEO’nuzu yükseltin."

#: inc/deprecated/3.2.php:72
msgid "More details"
msgstr "Daha fazla ayrıntı"

#. translators: %d is the number of pages preloaded.
#: inc/deprecated/3.2.php:228
msgid "Sitemap preload: %d pages have been cached."
msgstr "Site haritası önyükleme: %d sayfa önbelleklendi."

#. translators: %d = Number of pages preloaded.
#: inc/deprecated/3.2.php:261
msgid ""
"Sitemap preload: %d uncached pages have now been preloaded. (refresh to see "
"progress)"
msgstr ""
"Site haritası önyükleme: %d önbelleklenmeyen sayfanın şimdi önyüklemesi "
"yapıldı. (ilerlemeyi görmek için yenileyin)"

#. translators: %s is an URL.
#: inc/deprecated/3.3.php:761 inc/deprecated/3.3.php:770
#: inc/Engine/Plugin/UpdaterApiTools.php:32
msgid ""
"An unexpected error occurred. Something may be wrong with WP-Rocket.me or "
"this server&#8217;s configuration. If you continue to have problems, <a "
"href=\"%s\">contact support</a>."
msgstr ""
"Beklenmeyen bir hata meydana geldi. WP-Rocket.me veya bu sunucunun "
"yapılandırması ile ilgili birşey yanlış gitmiş olabilir. Eğer sorunlar "
"yaşamaya devam ederseniz, <a href=\"%s\">destekle iletişime geçin</a>."

#: inc/deprecated/3.4.php:18
msgid "Choose a domain from the list"
msgstr "Listeden bir etki alanı seçin"

#: inc/deprecated/3.4.php:31
msgid "No domain available in your Cloudflare account"
msgstr "Cloudflare hesabınızda hiç etki alanı mevcut değil"

#: inc/deprecated/3.5.php:71 inc/deprecated/3.5.php:195
msgid ""
"Curl is disabled on your server. Please ask your host to enable it. This is "
"required for the Cloudflare Add-on to work correctly."
msgstr ""
"Curl, sunucunuzda etkisizleştirilmiş. Lütfen barındırıcınızdan bunu "
"etkinleştirmesini isteyin. Bu, Cloudflare Eklentisinin doğru çalışması için "
"gereklidir."

#. translators: %1$s = opening link; %2$s = closing link
#: inc/deprecated/3.5.php:79
msgid ""
"Cloudflare email, API key and Zone ID are not set. Read the "
"%1$sdocumentation%2$s for further guidance."
msgstr ""
"Cloudflare e-posta adresi, API anahtarı ve Bölge Kimliği ayarlı değil. Daha "
"fazla rehberlik için %1$sbelgeleri%2$s okuyun."

#. translators: %1$s = opening link; %2$s = closing link
#: inc/deprecated/3.5.php:206
msgid ""
"Cloudflare email and API key are not set. Read the %1$sdocumentation%2$s for"
" further guidance."
msgstr ""
"Cloudflare e-posta adresi ve API anahtarı ayarlı değil. Daha fazla rehberlik"
" için %1$sbelgeleri%2$s okuyun."

#: inc/deprecated/3.5.php:271
msgid "Connection to Cloudflare failed"
msgstr "Cloudflare’a bağlantı başarısız oldu"

#. translators: %s = CloudFare API return message.
#: inc/deprecated/3.5.php:587
msgid "<strong>WP Rocket:</strong> %s"
msgstr "<strong>WP Rocket:</strong> %s"

#: inc/deprecated/3.5.php:592
msgid "<strong>WP Rocket:</strong> Cloudflare cache successfully purged."
msgstr ""
"<strong>WP Rocket:</strong> Cloudflare önbelleği başarılı olarak temizlendi."

#: inc/deprecated/3.5.php:858 inc/Engine/HealthCheck/HealthCheck.php:81
msgid ""
"The following scheduled event failed to run. This may indicate the CRON "
"system is not running properly, which can prevent some WP Rocket features "
"from working as intended:"
msgid_plural ""
"The following scheduled events failed to run. This may indicate the CRON "
"system is not running properly, which can prevent some WP Rocket features "
"from working as intended:"
msgstr[0] ""
"Aşağıdaki zamanlanan olayın çalıştırılması başarısız oldu. Bu, CRON "
"sisteminin düzgün bir şekilde çalışmadığını gösterebilir ve bu da bazı WP "
"Rocket özelliklerinin beklendiği gibi çalışmasını engelleyebilir:"
msgstr[1] ""
"Aşağıdaki zamanlanan olayların çalıştırılması başarısız oldu. Bu, CRON "
"sisteminin düzgün bir şekilde çalışmadığını gösterebilir ve bu da bazı WP "
"Rocket özelliklerinin beklendiği gibi çalışmasını engelleyebilir:"

#: inc/deprecated/3.5.php:867 inc/Engine/HealthCheck/HealthCheck.php:88
msgid "Please contact your host to check if CRON is working."
msgstr ""
"CRON’un çalışıp çalışmadığını denetlemek için lütfen barındırıcınız ile "
"iletişime geçin."

#: inc/deprecated/3.10.php:163
msgid "OPcache purge failed."
msgstr "OPcache temizleme başarısız oldu."

#: inc/deprecated/3.10.php:168
msgid "OPcache successfully purged"
msgstr "OPcache başarılı olarak temizlendi"

#: inc/deprecated/3.11.php:81
msgid "Yoast SEO XML sitemap"
msgstr "Yoast SEO XML site haritası"

#. translators: %s = Name of the plugin.
#. translators: %s = plugin name, e.g. Yoast SEO.
#: inc/deprecated/3.11.php:83 inc/deprecated/3.12.php:58
#: inc/deprecated/3.12.php:87 inc/deprecated/3.12.php:186
#: inc/deprecated/3.12.php:253 inc/deprecated/3.12.php:342
msgid ""
"We automatically detected the sitemap generated by the %s plugin. You can "
"check the option to preload it."
msgstr ""
"%s eklentisi tarafından üretilen site haritasını otomatik olarak algıladık. "
"Önyüklemek için seçeneği işaretleyebilirsiniz."

#. translators: 1 and 3 are link openings, 2 is a link closing.
#: inc/deprecated/3.11.php:279 inc/Engine/Plugin/UpdaterSubscriber.php:520
msgid "%1$sReturn to WP Rocket%2$s or %3$sgo to Plugins page%2$s"
msgstr ""
"%1$sWP Rocket’a geri dönün%2$s ya da %3$sEklentiler sayfasına gidin%2$s"

#: inc/deprecated/3.12.php:56
msgid "All in One SEO XML sitemap"
msgstr "All in One SEO XML site haritası"

#: inc/deprecated/3.12.php:85
msgid "Rank Math XML sitemap"
msgstr "Rank Math XML site haritası"

#: inc/deprecated/3.12.php:184
msgid "SEOPress XML sitemap"
msgstr "SEOPress XML site haritası"

#: inc/deprecated/3.12.php:251
msgid "The SEO Framework XML sitemap"
msgstr "SEO Framework XML site haritası"

#: inc/deprecated/3.12.php:332
msgid "Jetpack XML Sitemaps"
msgstr "Jetpack XML Site Haritaları"

#: inc/deprecated/3.12.php:334
msgid "Preload the sitemap from the Jetpack plugin"
msgstr "Jetpack eklentisinden site haritasını önyükleme yap"

#: inc/deprecated/3.15.php:28
#: inc/Engine/Admin/Metaboxes/PostEditOptionsSubscriber.php:74
msgid "WP Rocket Options"
msgstr "WP Rocket Seçenekleri"

#: inc/deprecated/3.15.php:57 views/metaboxes/post_edit_options.php:19
msgid "Never cache this page"
msgstr "Bu sayfayı asla önbellekleme"

#: inc/deprecated/3.15.php:61 views/metaboxes/post_edit_options.php:23
msgid "Activate these options on this post:"
msgstr "Şu seçenekleri bu yazıda aktif et:"

#. translators: %s is the name of the option.
#: inc/deprecated/3.15.php:81
#: inc/Engine/Admin/Metaboxes/PostEditOptionsSubscriber.php:121
msgid "Activate first the %s option."
msgstr "Önce %s seçeneğini aktif edin."

#. translators: %1$s = opening strong tag, %2$s = closing strong tag.
#: inc/deprecated/3.15.php:97 views/metaboxes/post_edit_options.php:38
msgid ""
"%1$sNote:%2$s None of these options will be applied if this post has been "
"excluded from cache in the global cache settings."
msgstr ""
"%1$sNot:%2$s Bu yazı genel önbellek ayarlarındaki önbellekten hariç "
"tutulduysa, bu seçeneklerden hiçbiri uygulanmayacaktır."

#: inc/deprecated/deprecated.php:240
msgid "<strong>JS</strong> files with Deferred Loading JavaScript"
msgstr "Yüklemesi Ertelenmiş JavaScript’i olan <strong>JS</strong> dosyaları"

#: inc/deprecated/deprecated.php:293
msgid "Add URL"
msgstr "URL ekle"

#: inc/deprecated/deprecated.php:1245
#: inc/Engine/Admin/Settings/Settings.php:451
msgid "Settings saved."
msgstr "Ayarlar kaydedildi."

#: inc/deprecated/deprecated.php:1277 views/settings/fields/import-form.php:22
msgid ""
"Before you can upload your import file, you will need to fix the following "
"error:"
msgstr ""
"İçe aktarma dosyanızı göndermeden önce, şurada belirtilen hatayı düzeltmeniz"
" gerekecek:"

#. translators: %s is the maximum upload size set on the current server.
#: inc/deprecated/deprecated.php:1288 views/settings/fields/import-form.php:35
msgid "Choose a file from your computer (maximum size: %s)"
msgstr "Bilgisayarınızdan bir dosya seçin (en fazla boyut: %s)"

#: inc/deprecated/deprecated.php:1294 inc/Engine/Admin/Settings/Render.php:422
msgid "Upload file and import settings"
msgstr "Dosyayı gönder ve ayarları içe aktar"

#: inc/deprecated/deprecated.php:1385
msgid "Your Cloudflare credentials are valid."
msgstr "Cloudflare kimlik bilgileriniz geçerli."

#: inc/deprecated/deprecated.php:1389
msgid "Your Cloudflare credentials are invalid!"
msgstr "Cloudflare kimlik bilgileriniz geçersiz!"

#: inc/deprecated/deprecated.php:1453
msgid "Save and optimize"
msgstr "Kaydet ve iyileştir"

#: inc/deprecated/deprecated.php:1453
msgid "Optimize"
msgstr "İyileştir"

#: inc/deprecated/deprecated.php:1464 inc/deprecated/deprecated.php:1666
msgctxt "screen-reader-text"
msgid "Note:"
msgstr "Not:"

#: inc/deprecated/deprecated.php:1470
msgctxt "screen-reader-text"
msgid "Performance tip:"
msgstr "Performans ipucu:"

#: inc/deprecated/deprecated.php:1476
msgctxt "screen-reader-text"
msgid "Third-party feature detected:"
msgstr "Üçüncü taraf özellik algılandı:"

#: inc/deprecated/deprecated.php:1488 inc/deprecated/deprecated.php:1672
msgctxt "screen-reader-text"
msgid "Warning:"
msgstr "Uyarı:"

#: inc/deprecated/deprecated.php:1501
msgctxt "button text"
msgid "Download settings"
msgstr "Ayarları indir"

#: inc/deprecated/deprecated.php:1531
msgid "Replace site's hostname with:"
msgstr "Sitenin anamakine adını şununla değiştir:"

#: inc/deprecated/deprecated.php:1550 inc/deprecated/deprecated.php:1587
#: inc/deprecated/deprecated.php:1615 views/settings/fields/cnames.php:41
#: views/settings/fields/cnames.php:75 views/settings/fields/cnames.php:105
msgid "reserved for"
msgstr "şunun için ayrıldı:"

#: inc/deprecated/deprecated.php:1552 inc/deprecated/deprecated.php:1589
#: inc/deprecated/deprecated.php:1617 views/settings/fields/cnames.php:44
#: views/settings/fields/cnames.php:78 views/settings/fields/cnames.php:108
msgid "All files"
msgstr "Tüm dosyalar"

#: inc/deprecated/deprecated.php:1564 inc/deprecated/deprecated.php:1594
#: inc/deprecated/deprecated.php:1622 views/settings/fields/cnames.php:56
#: views/settings/fields/cnames.php:90 views/settings/fields/cnames.php:120
msgid "Images"
msgstr "Resimler"

#: inc/deprecated/deprecated.php:1635 views/settings/fields/cnames.php:132
msgid "Add CNAME"
msgstr "CNAME ekle"

#: inc/deprecated/deprecated.php:1706
msgid "Watch the video"
msgstr "Görüntüyü izleyin"

#: inc/deprecated/deprecated.php:1771
msgid "Basic"
msgstr "Temel"

#: inc/deprecated/deprecated.php:1772
msgid "Static Files"
msgstr "Sabit Dosyalar"

#: inc/deprecated/deprecated.php:1773 inc/Engine/Admin/Settings/Page.php:1500
#: inc/Engine/Admin/Settings/Page.php:1511
#: inc/Engine/CDN/Admin/Subscriber.php:28
msgid "CDN"
msgstr "CDN"

#: inc/deprecated/deprecated.php:1774
msgid "Advanced"
msgstr "Gelişmiş"

#: inc/deprecated/deprecated.php:1775 inc/Engine/Admin/Settings/Page.php:1345
msgid "Database"
msgstr "Veritabanı"

#: inc/deprecated/deprecated.php:1776 inc/Engine/Admin/Settings/Page.php:1051
msgid "Preload"
msgstr "Önyükleme"

#: inc/deprecated/deprecated.php:1786
#: inc/Engine/Admin/Settings/Subscriber.php:171
#: views/settings/page-sections/tools.php:33
msgid "Tools"
msgstr "Araçlar"

#: inc/deprecated/deprecated.php:1789 inc/Engine/Admin/Settings/Page.php:357
#: views/settings/page-sections/dashboard.php:80
msgid "License"
msgstr "Lisans"

#. Translators: %1$s = Plugin name, %2$s = Plugin version, %3$s = PHP version
#. required.
#: inc/deprecated/deprecated.php:1944
msgid ""
"%1$s %2$s requires at least PHP %3$s to function properly. To use this "
"version, please ask your web host how to upgrade your server to PHP %3$s or "
"higher. If you are not able to upgrade, you can rollback to the previous "
"version by using the button below."
msgstr ""
"%1$s %2$s, düzgün bir şekilde işlemesi için en az PHP %3$s gerektirir. Bu "
"sürümü kullanmak için lütfen web barındırıcınıza sunucunuzu PHP %3$s veya "
"daha yüksek sürümüne nasıl yükselteceğinizi sorun. Eğer yükseltemiyorsanız, "
"aşağıdaki düğmeyi kullanarak önceki sürüme geri döndürebilirsiniz."

#. translators: 1: PHP class name, 2: version number, 3: replacement class
#. name.
#: inc/deprecated/DeprecatedClassTrait.php:54
msgid ""
"The called class %1$s is <strong>deprecated</strong> since version %2$s! Use"
" %3$s instead."
msgstr ""
"Çağrılan %1$s sınıfı, %2$s sürümünden bu yana <strong>kullanım "
"dışıdır</strong>! Bunun yerine %3$s kullanın."

#. translators: 1: PHP class name, 2: version number.
#: inc/deprecated/DeprecatedClassTrait.php:65
msgid ""
"The called class %1$s is <strong>deprecated</strong> since version %2$s!"
msgstr ""
"Çağrılan %1$s sınıfı, %2$s sürümünden bu yana <strong>kullanım "
"dışıdır</strong>!"

#: inc/deprecated/Engine/Addon/FacebookTracking/Subscriber.php:92
#: inc/deprecated/Engine/Addon/GoogleTracking/Subscriber.php:137
#: inc/Engine/Admin/Database/Subscriber.php:79
msgid "weekly"
msgstr "haftalık"

#: inc/Engine/Admin/Database/Optimization.php:30
#: inc/Engine/Admin/Settings/Page.php:1389
msgid "Revisions"
msgstr "Düzeltmeler"

#: inc/Engine/Admin/Database/Optimization.php:31
#: inc/Engine/Admin/Settings/Page.php:1399
msgid "Auto Drafts"
msgstr "Otomatik Taslaklar"

#: inc/Engine/Admin/Database/Optimization.php:32
#: inc/Engine/Admin/Settings/Page.php:1409
msgid "Trashed Posts"
msgstr "Çöpe Atılmış Yazılar"

#: inc/Engine/Admin/Database/Optimization.php:33
#: inc/Engine/Admin/Settings/Page.php:1419
msgid "Spam Comments"
msgstr "İstenmeyen Yorumlar"

#: inc/Engine/Admin/Database/Optimization.php:34
#: inc/Engine/Admin/Settings/Page.php:1429
msgid "Trashed Comments"
msgstr "Çöpe Atılmış Yorumlar"

#: inc/Engine/Admin/Database/Optimization.php:35
msgid "Transients"
msgstr "Geçiciler"

#: inc/Engine/Admin/Database/Optimization.php:36
msgid "Tables"
msgstr "Tablolar"

#: inc/Engine/Admin/Database/Subscriber.php:85
msgid "monthly"
msgstr "aylık"

#: inc/Engine/Admin/Database/Subscriber.php:194
msgid "Database optimization process is running"
msgstr "Veritabanı iyileştirme işlemi çalışıyor"

#: inc/Engine/Admin/Database/Subscriber.php:224
msgid ""
"Database optimization process is complete. Everything was already optimized!"
msgstr "Veritabanı iyileştirme işlemi tamamlandı. Herşey zaten iyileştirildi!"

#: inc/Engine/Admin/Database/Subscriber.php:227
msgid ""
"Database optimization process is complete. List of optimized items below:"
msgstr ""
"Veritabanı iyileştirme işlemi tamamlandı. İyileştirilen öğelerin listesi "
"aşağıdadır:"

#. translators: %1$d = number of items optimized, %2$s = type of optimization
#: inc/Engine/Admin/Database/Subscriber.php:235
msgid "%1$d %2$s optimized."
msgstr "%1$d %2$s iyi hale getirildi."

#. translators: %1$s = <strong>, %2$s = </strong>, %3$s = <a>, %4$s = </a>.
#: inc/Engine/Admin/DomainChange/Subscriber.php:143
msgid ""
"%1$sWP Rocket:%2$s We detected that the website domain has changed. The "
"configuration files must be regenerated for the page cache and all other "
"optimizations to work as intended. %3$sLearn More%4$s"
msgstr ""
"%1$sWP Rocket:%2$s Web sitesi etki alanının değiştiğini tespit ettik. Sayfa "
"önbelleği ve diğer tüm iyileştirmelerin amaçlandığı gibi çalışması için "
"yapılandırma dosyaları yeniden oluşturulmak zorundadır. %3$sDaha fazla bilgi"
" edinin%4$s"

#: inc/Engine/Admin/DomainChange/Subscriber.php:171
msgid "Regenerate WP Rocket configuration files now"
msgstr "WP Rocket yapılandırma dosyalarını şimdi yeniden oluştur"

#: inc/Engine/Admin/Settings/Page.php:223
msgid "Save Changes"
msgstr "Değişiklikleri Kaydet"

#: inc/Engine/Admin/Settings/Page.php:223
msgid "Validate License"
msgstr "Lisansı Doğrula"

#: inc/Engine/Admin/Settings/Page.php:279
#: inc/Engine/Admin/Settings/Page.php:280 inc/functions/admin.php:550
msgid "Unavailable"
msgstr "Mevcut değil"

#: inc/Engine/Admin/Settings/Page.php:374
msgid "API key"
msgstr "API anahtarı"

#: inc/Engine/Admin/Settings/Page.php:389
msgid "Email address"
msgstr "E-posta adresi"

#: inc/Engine/Admin/Settings/Page.php:415
msgid "Dashboard"
msgstr "Panel"

#: inc/Engine/Admin/Settings/Page.php:416
msgid "Get help, account info"
msgstr "Yardım alın, hesap bilgisi"

#: inc/Engine/Admin/Settings/Page.php:425
msgid "My Status"
msgstr "Durumum"

#: inc/Engine/Admin/Settings/Page.php:435 views/settings/page.php:75
msgid "Rocket Analytics"
msgstr "Rocket Çözümsel"

#. translators: %1$s = opening <a> tag, %2$s = closing </a> tag.
#: inc/Engine/Admin/Settings/Page.php:437
msgid ""
"I agree to share anonymous data with the development team to help improve WP"
" Rocket. %1$sWhat info will we collect?%2$s"
msgstr ""
"WP Rocket’i iyileştirmeye yardımcı olmak için geliştirme ekibiyle isimsiz "
"verileri paylaşmayı kabul ediyorum. %1$sNe tür bilgi toplayacağız?%2$s"

#: inc/Engine/Admin/Settings/Page.php:489
msgid "File Optimization"
msgstr "Dosyaları İyileştirme"

#: inc/Engine/Admin/Settings/Page.php:490
msgid "Optimize CSS & JS"
msgstr "CSS ve JS kodlarını iyileştirin"

#. translators: %1$s = type of minification (HTML, CSS or JS), %2$s = “WP
#. Rocket”.
#: inc/Engine/Admin/Settings/Page.php:498
msgid ""
"%1$s Minification is currently activated in <strong>Autoptimize</strong>. If"
" you want to use %2$s’s minification, disable this option in Autoptimize."
msgstr ""
"%1$s Küçültme şu anda <strong>Autoptimize</strong> içinde aktif edildi. Eğer"
" %2$s’in küçültmesini kullanmak istiyorsanız, Autoptimize içindeki bu "
"seçeneği etkisizleştirin."

#: inc/Engine/Admin/Settings/Page.php:508
msgid "CSS Files"
msgstr "CSS Dosyaları"

#: inc/Engine/Admin/Settings/Page.php:517
msgid "JavaScript Files"
msgstr "JavaScript Dosyaları"

#. translators: %1$s = type of minification (HTML, CSS or JS), %2$s = “WP
#. Rocket”.
#: inc/Engine/Admin/Settings/Page.php:524
msgid ""
"%1$s Minification is currently activated in <strong>Autoptimize</strong>. If"
" you want to use %2$s’s minification, disable those options in Autoptimize."
msgstr ""
"%1$s Küçültme şu anda <strong>Autoptimize</strong> içinde aktif edildi. Eğer"
" %2$s’in küçültmesini kullanmak istiyorsanız, Autoptimize içindeki bu "
"seçenekleri etkisizleştirin."

#: inc/Engine/Admin/Settings/Page.php:529
msgid ""
"If you have problems after activating this option, copy and paste the "
"default exclusions to quickly resolve issues:"
msgstr ""
"Eğer bu seçeneği etkinleştirdikten sonra sorun yaşarsanız, sorunları hızlı "
"bir şekilde çözmek için varsayılan hariç tutulanları kopyalayın ve "
"yapıştırın:"

#. translators: %1$s = opening </a> tag, %2$s = closing </a> tag.
#: inc/Engine/Admin/Settings/Page.php:533
msgid ""
"Also, please check our %1$sdocumentation%2$s for a list of compatibility "
"exclusions."
msgstr ""
"Ayrıca, uyumluluk istisnaları listesi için lütfen %1$sbelgelerimizi%2$s "
"gözden geçirin."

#: inc/Engine/Admin/Settings/Page.php:538
msgid ""
"Internal scripts are excluded by default to prevent issues. Remove them to "
"take full advantage of this option."
msgstr ""
"Sorunları önlemek için dahili betikler varsayılan olarak hariç tutulur. Bu "
"seçenekten tam olarak yararlanmak için bunları kaldırın."

#. translators: %1$s = opening </a> tag, %2$s = closing </a> tag.
#: inc/Engine/Admin/Settings/Page.php:541
msgid ""
"If this causes trouble, restore the default exclusions, found %1$shere%2$s"
msgstr ""
"Eğer bu, soruna neden olursa, %1$sburada%2$s bulunan varsayılan hariç "
"tutulanları geri yükleyin"

#: inc/Engine/Admin/Settings/Page.php:550
msgid "Minify CSS files"
msgstr "CSS dosyalarını küçült"

#: inc/Engine/Admin/Settings/Page.php:551
msgid "Minify CSS removes whitespace and comments to reduce the file size."
msgstr ""
"CSS’yi küçültme, dosya boyutunu düşürmek için boşlukları ve açıklamaları "
"kaldırır."

#: inc/Engine/Admin/Settings/Page.php:564
#: inc/Engine/Admin/Settings/Page.php:628
#: inc/Engine/Admin/Settings/Page.php:694
#: inc/Engine/Admin/Settings/Page.php:718
msgid "This could break things!"
msgstr "Bu birşeyleri bozabilir!"

#: inc/Engine/Admin/Settings/Page.php:565
#: inc/Engine/Admin/Settings/Page.php:629
#: inc/Engine/Admin/Settings/Page.php:695
#: inc/Engine/Admin/Settings/Page.php:719
msgid ""
"If you notice any errors on your website after having activated this "
"setting, just deactivate it again, and your site will be back to normal."
msgstr ""
"Bu ayarı aktif ettikten sonra web sitenizde herhangi bir hata fark "
"ederseniz, sadece tekrar devre dışı bırakın, ve siteniz normale dönecektir."

#: inc/Engine/Admin/Settings/Page.php:566
msgid "Activate minify CSS"
msgstr "CSS küçültmeyi aktif et"

#: inc/Engine/Admin/Settings/Page.php:572
msgid ""
"Specify URLs of CSS files to be excluded from minification (one per line)."
msgstr ""
"Küçültmeden hariç tutulacak CSS dosyalarının URL’lerini belirtin (her satıra"
" bir tane)."

#: inc/Engine/Admin/Settings/Page.php:573
msgid ""
"<strong>Internal:</strong> The domain part of the URL will be stripped "
"automatically. Use (.*).css wildcards to exclude all CSS files located at a "
"specific path."
msgstr ""
"<strong>Dahili:</strong> URL’nin etki alanı kısmı otomatik olarak "
"sadeleştirilecektir. Belirli bir yolda bulunan tüm CSS dosyalarını hariç "
"tutmak için (.*).css joker karakterlerini kullanın."

#. translators: %1$s = opening <a> tag, %2$s = closing </a> tag.
#: inc/Engine/Admin/Settings/Page.php:575
msgid ""
"<strong>3rd Party:</strong> Use either the full URL path or only the domain "
"name, to exclude external CSS. %1$sMore info%2$s"
msgstr ""
"<strong>3. Taraf:</strong> Harici CSS dosyalarını dahil etmemek için ya URL "
"tam yolunu ya da sadece etki alanı adını kullanın. %1$sDaha fazla bilgi%2$s"

#: inc/Engine/Admin/Settings/Page.php:588
#: inc/Engine/Admin/Settings/Page.php:610
msgid "Optimize CSS delivery"
msgstr "CSS teslimini iyileştir"

#: inc/Engine/Admin/Settings/Page.php:593
msgid ""
"Optimize CSS delivery eliminates render-blocking CSS on your website. Only "
"one method can be selected. Remove Unused CSS is recommended for optimal "
"performance, but limited only to the users with active license."
msgstr ""
"CSS teslimini en iyi hale getirmek, web sitenizde işleme engelleyici CSS’yi "
"ortadan kaldırır. Sadece bir yöntem seçilebilir. En iyi performans için "
"Kullanılmayan CSS’yi Kaldır önerilir, ancak sadece aktif lisansa sahip "
"kullanıcılarla sınırlıdır."

#: inc/Engine/Admin/Settings/Page.php:593
msgid ""
"Optimize CSS delivery eliminates render-blocking CSS on your website. Only "
"one method can be selected. Remove Unused CSS is recommended for optimal "
"performance."
msgstr ""
"CSS teslimini en iyi hale getirmek, web sitenizde işleme engelleyici CSS’yi "
"ortadan kaldırır. Sadece bir yöntem seçilebilir. En iyi performans için "
"Kullanılmayan CSS’yi Kaldır önerilir."

#. translators: %1$s = opening <a> tag, %2$s = closing </a> tag.
#: inc/Engine/Admin/Settings/Page.php:603
msgid ""
"Optimize CSS Delivery features are disabled on local environments. %1$sLearn"
" more%2$s"
msgstr ""
"CSS teslimini iyileştir özellikleri yerel ortamlarda etkisizleştirilir. "
"%1$sDaha fazla bilgi edinin%2$s"

#: inc/Engine/Admin/Settings/Page.php:623
#: inc/Engine/Optimization/RUCSS/Admin/OptionSubscriber.php:74
msgid "Remove Unused CSS"
msgstr "Kullanılmayan CSS’yi Kaldır"

#. translators: %1$s = opening <a> tag, %2$s = closing </a> tag.
#: inc/Engine/Admin/Settings/Page.php:626
msgid ""
"Removes unused CSS per page and helps to reduce page size and HTTP requests."
" Recommended for best performance. Test thoroughly! %1$sMore info%2$s"
msgstr ""
"Kullanılmayan CSS’yi sayfa başına kaldırır ve sayfa boyutunu ve HTTP "
"isteklerini azaltmaya yardımcı olur. Daha iyi performans için önerilir. "
"İyice deneyin! %1$sDaha fazla bilgi%2$s"

#: inc/Engine/Admin/Settings/Page.php:630
msgid "Activate Remove Unused CSS"
msgstr "Kullanılmayan CSS’yi Kaldır’ı aktif et"

#: inc/Engine/Admin/Settings/Page.php:636
msgid "CSS safelist"
msgstr "CSS güvenli listesi"

#: inc/Engine/Admin/Settings/Page.php:637
msgid ""
"Specify CSS filenames, IDs or classes that should not be removed (one per "
"line)."
msgstr ""
"Kaldırılmaması gereken CSS dosya adlarını, kimliklerini veya sınıfları "
"belirtin (her satıra bir tane)."

#: inc/Engine/Admin/Settings/Page.php:652
#: inc/Engine/CriticalPath/Admin/Subscriber.php:200
msgid "Load CSS asynchronously"
msgstr "CSS’yi eşzamanlı olmadan yükle"

#. translators: %1$s = plugin name.
#: inc/Engine/Admin/Settings/Page.php:655
msgctxt "WP Critical CSS compatibility"
msgid ""
"Load CSS asynchronously is currently handled by the %1$s plugin. If you want"
" to use WP Rocket’s load CSS asynchronously option, disable the %1$s plugin."
msgstr ""
"CSS’yi eşzamanlı olmadan yükleme, şu anda %1$s eklentisi tarafından "
"işlenmektedir. WP Rocket’in CSS’yi eşzamanlı olmadan yükleme seçeneğini "
"kullanmak istiyorsanız, %1$s eklentisini etkisizleştirin."

#. translators: %1$s = opening <a> tag, %2$s = closing </a> tag.
#: inc/Engine/Admin/Settings/Page.php:657
msgid ""
"Generates critical path CSS and loads CSS asynchronously. %1$sMore info%2$s"
msgstr ""
"Önemli Yol CSS’yi oluşturur ve CSS’yi eşzamanlı olmadan yükler. %1$sDaha "
"fazla bilgi%2$s"

#: inc/Engine/Admin/Settings/Page.php:663
msgid "Fallback critical CSS"
msgstr "Son çare önemli CSS"

#. translators: %1$s = opening <a> tag, %2$s = closing </a> tag.
#: inc/Engine/Admin/Settings/Page.php:665
msgid ""
"Provides a fallback if auto-generated critical path CSS is incomplete. "
"%1$sMore info%2$s"
msgstr ""
"Otomatik oluşturulmuş önemli yol CSS tamamlanmazsa son bir çare sağlar. "
"%1$sDaha fazla bilgi%2$s"

#: inc/Engine/Admin/Settings/Page.php:680
msgid "Minify JavaScript files"
msgstr "JavaScript dosyalarını küçült"

#: inc/Engine/Admin/Settings/Page.php:681
msgid ""
"Minify JavaScript removes whitespace and comments to reduce the file size."
msgstr ""
"JavaScript’i küçültme, dosya boyutunu düşürmek için boşlukları ve "
"açıklamaları kaldırır."

#: inc/Engine/Admin/Settings/Page.php:696
msgid "Activate minify JavaScript"
msgstr "JavaScript küçültmeyi aktif et"

#: inc/Engine/Admin/Settings/Page.php:701
msgid ""
"Combine JavaScript files <em>(Enable Minify JavaScript files to select)</em>"
msgstr ""
"JavaScript dosyalarını birleştir <em>(Seçmek için JavaScript dosyalarını "
"küçült’ü etkinleştirin)</em>"

#. translators: %1$s = opening <a> tag, %2$s = closing </a> tag.
#: inc/Engine/Admin/Settings/Page.php:703
msgid ""
"Combine JavaScript files combines your site’s internal, 3rd party and inline"
" JS reducing HTTP requests. Not recommended if your site uses HTTP/2. "
"%1$sMore info%2$s"
msgstr ""
"JavaScript dosyalarını birleştirme, sitenizin dahili, 3. taraf ve satır içi "
"JS dosyalarını birleştirir, HTTP isteklerini azaltır. Siteniz HTTP/2 "
"kullanıyorsa önerilmez. %1$sDaha fazla bilgi%2$s"

#. translators: %1$s = opening <a> tag, %2$s = closing </a> tag.
#: inc/Engine/Admin/Settings/Page.php:704
msgid ""
"For compatibility and best results, this option is disabled when delay "
"javascript execution is enabled."
msgstr ""
"Uyumluluk ve en iyi sonuçlar için javascript’i geciktir yürütmesi "
"etkinleştirildiğinde bu seçenek etkisizleştirilir."

#: inc/Engine/Admin/Settings/Page.php:720
msgid "Activate combine JavaScript"
msgstr "JavaScript birleştirmeyi aktif et"

#. translators: %1$s = opening <a> tag, %2$s = closing </a> tag.
#: inc/Engine/Admin/Settings/Page.php:727
msgid ""
"Specify patterns of inline JavaScript to be excluded from concatenation (one"
" per line). %1$sMore info%2$s"
msgstr ""
"Birleştirmeden hariç tutulacak satır içi JavaScript dosyalarının örneklerini"
" belirtin (her satıra bir tane). %1$sDaha fazla bilgi%2$s"

#: inc/Engine/Admin/Settings/Page.php:744
msgid ""
"Specify URLs of JavaScript files to be excluded from minification and "
"concatenation (one per line)."
msgstr ""
"Küçültmeden ve birleştirmeden hariç tutulacak JavaScript dosyalarının "
"URL’lerini belirtin (her satıra bir tane)."

#: inc/Engine/Admin/Settings/Page.php:745
msgid ""
"<strong>Internal:</strong> The domain part of the URL will be stripped "
"automatically. Use (.*).js wildcards to exclude all JS files located at a "
"specific path."
msgstr ""
"<strong>Dahili:</strong> URL’nin etki alanı kısmı otomatik olarak "
"sadeleştirilecektir. Belirli bir yolda bulunan tüm JS dosyalarını hariç "
"tutmak için (.*).js joker karakterlerini kullanın."

#. translators: %1$s = opening <a> tag, %2$s = closing </a> tag.
#: inc/Engine/Admin/Settings/Page.php:747
msgid ""
"<strong>3rd Party:</strong> Use either the full URL path or only the domain "
"name, to exclude external JS. %1$sMore info%2$s"
msgstr ""
"<strong>3. Taraf:</strong> Harici JS dosyalarını dahil etmemek için ya URL "
"tam yolunu ya da sadece etki alanı adını kullanın. %1$sDaha fazla bilgi%2$s"

#: inc/Engine/Admin/Settings/Page.php:763
#: inc/Engine/Optimization/DeferJS/AdminSubscriber.php:76
msgid "Load JavaScript deferred"
msgstr "Ertelenmiş JavaScript yükle"

#. translators: %1$s = opening <a> tag, %2$s = closing </a> tag.
#: inc/Engine/Admin/Settings/Page.php:765
msgid ""
"Load JavaScript deferred eliminates render-blocking JS on your site and can "
"improve load time. %1$sMore info%2$s"
msgstr ""
"Ertelenmiş JavaScript yüklemek, yükleme süresini iyileştirebilir ve "
"sitenizdeki işleyişi engelleyen JS’yi aradan kaldırır. %1$sDaha fazla "
"bilgi%2$s"

#. translators: %1$s = opening <a> tag, %2$s = closing </a> tag.
#: inc/Engine/Admin/Settings/Page.php:778
msgid ""
"Specify URLs or keywords of JavaScript files to be excluded from defer (one "
"per line). %1$sMore info%2$s"
msgstr ""
"Ertelenmeden hariç tutulacak JavaScript dosyalarının URL’lerini ya da "
"anahtar kelimelerini belirtin (her satıra bir tane). %1$sDaha fazla "
"bilgi%2$s"

#: inc/Engine/Admin/Settings/Page.php:794
#: inc/Engine/Optimization/DelayJS/Admin/Subscriber.php:210
msgid "Delay JavaScript execution"
msgstr "JavaScript’i Geciktir yürütmesi"

#. translators: %1$s = opening <a> tag, %2$s = closing </a> tag.
#: inc/Engine/Admin/Settings/Page.php:796
msgid ""
"Improves performance by delaying the loading of JavaScript files until user "
"interaction (e.g. scroll, click). %1$sMore info%2$s"
msgstr ""
"JavaScript dosyalarının yüklenmesini, kullanıcı etkileşimine (örn. kaydırma,"
" tıklama) kadar geciktirerek performansı artırır. %1$sDaha fazla bilgi%2$s"

#: inc/Engine/Admin/Settings/Page.php:805
msgid "One-click exclusions"
msgstr "Tek tıkla hariç tutmalar"

#: inc/Engine/Admin/Settings/Page.php:806
msgid ""
"When using the Delay JavaScript Execution, you might experience delay "
"loading elements located in the viewport that need to appear immediately - "
"e.g. slider, header, menu."
msgstr ""
"JavaScript’i Geciktir Yürütmesini kullanırken, görünüm alanında bulunan ve "
"hemen görünmesi gereken öğelerin yüklenmesinde gecikme yaşayabilirsiniz - "
"örn. kaydırıcı, başlık, menü."

#: inc/Engine/Admin/Settings/Page.php:807
msgid ""
"If you need instant visibility, click below on files that should NOT be "
"delayed. This selection will help users interact with the elements straight "
"away."
msgstr ""
"Eğer anında görünürlüğe ihtiyacınız varsa, geciktirilmemesi gereken "
"dosyalara aşağıdan tıklayın. Bu seçim, kullanıcıların öğelerle hemen "
"etkileşim kurmasına yardımcı olacak."

#: inc/Engine/Admin/Settings/Page.php:824
msgid ""
"Specify URLs or keywords that can identify inline or JavaScript files to be "
"excluded from delaying execution (one per line)."
msgstr ""
"Yürütmeyi geciktirmenin dışında bırakılacağı satır içi veya JavaScript "
"dosyalarını tanımlayabilecek URL’leri veya anahtar kelimeleri belirtin (her "
"satıra bir tane)."

#: inc/Engine/Admin/Settings/Page.php:856
msgid "Media"
msgstr "Ortam"

#: inc/Engine/Admin/Settings/Page.php:857
msgid "LazyLoad, image dimensions"
msgstr "LazyLoad, resim boyutları"

#: inc/Engine/Admin/Settings/Page.php:866
msgid "Autoptimize"
msgstr "Otoiyileştir"

#: inc/Engine/Admin/Settings/Page.php:920
msgid "LazyLoad"
msgstr "LazyLoad"

#. translators: %1$s = opening <a> tag, %2$s = closing </a> tag.
#: inc/Engine/Admin/Settings/Page.php:923
msgid ""
"It can improve actual and perceived loading time as images, iframes, and "
"videos will be loaded only as they enter (or about to enter) the viewport "
"and reduces the number of HTTP requests. %1$sMore Info%2$s"
msgstr ""
"Resimlerin, iframe’lerin, ve görüntülerin sadece görüntü alanına girmesiyle "
"(ya da girmek üzereyken) yükleneceği gibi asıl ve farkedilir yükleme "
"süresini iyileştirebilir ve HTTP isteklerinin sayısını azaltır. %1$sDaha "
"Fazla Bilgi%2$s"

#. translators: %1$s = “WP Rocket”, %2$s = a list of plugin names.
#: inc/Engine/Admin/Settings/Page.php:930
msgid ""
"LazyLoad is currently activated in %2$s. If you want to use WP Rocket’s "
"LazyLoad, disable this option in %2$s."
msgstr ""
"LazyLoad şu anda %2$s içinde aktif edildi. Eğer WP Rocket’in LazyLoad’ını "
"kullanmak istiyorsanız, %2$s içindeki bu seçeneği etkisizleştirin."

#: inc/Engine/Admin/Settings/Page.php:933
msgid "Image Dimensions"
msgstr "Resim Boyutları"

#. translators: %1$s = opening <a> tag, %2$s = closing </a> tag.
#: inc/Engine/Admin/Settings/Page.php:936
msgid ""
"Add missing width and height attributes to images. Helps prevent layout "
"shifts and improve the reading experience for your visitors. %1$sMore "
"info%2$s"
msgstr ""
"Resimlere eksik genişlik ve yükseklik özniteliklerini ekleyin. Düzen "
"değişimlerini önlemeye ve ziyaretçileriniz için okuma deneyimini "
"iyileştirmeye yardımcı olur. %1$sDaha fazla bilgi%2$s"

#: inc/Engine/Admin/Settings/Page.php:955
msgid "Enable for images"
msgstr "Resimler için etkinleştir"

#. translators: %1$s = “WP Rocket”, %2$s = a list of plugin names.
#: inc/Engine/Admin/Settings/Page.php:967
msgid ""
"LazyLoad for images is currently activated in %2$s. If you want to use "
"%1$s’s LazyLoad, disable this option in %2$s."
msgstr ""
"Resimler için LazyLoad şu anda %2$s içinde aktif edildi. Eğer %1$s’in "
"LazyLoad’ını kullanmak istiyorsanız, %2$s içindeki bu seçeneği "
"etkisizleştirin."

#: inc/Engine/Admin/Settings/Page.php:975
msgid "Enable for CSS background images"
msgstr "CSS arka plan resimleri için etkinleştir"

#: inc/Engine/Admin/Settings/Page.php:990
msgid "Enable for iframes and videos"
msgstr "Iframe’ler ve görüntüler için etkinleştir"

#: inc/Engine/Admin/Settings/Page.php:1005
msgid "Replace YouTube iframe with preview image"
msgstr "Önizleme resmi ile YouTube iframe’ini değiştir"

#. translators: %1$s = “WP Rocket”, %2$s = a list of plugin or themes names.
#: inc/Engine/Admin/Settings/Page.php:1007
msgid "Replace YouTube iframe with preview image is not compatible with %2$s."
msgstr ""
"Önizleme resmi ile değiştirilen YouTube iframe’i %2$s ile uyumlu değil."

#. translators: %1$s = “WP Rocket”, %2$s = a list of plugin or themes names.
#: inc/Engine/Admin/Settings/Page.php:1007
msgid ""
"This can significantly improve your loading time if you have a lot of "
"YouTube videos on a page."
msgstr ""
"Bir sayfada çok sayıda YouTube görüntüsüne sahipseniz bu önemli ölçüde "
"yükleme sürenizi iyileştirebilir."

#: inc/Engine/Admin/Settings/Page.php:1022
msgid "Excluded images or iframes"
msgstr "Hariç tutulan resimler veya iframe’ler"

#. translators: %1$s = opening <a> tag, %2$s = closing </a> tag.
#: inc/Engine/Admin/Settings/Page.php:1024
msgid ""
"Specify keywords (e.g. image filename, CSS filename, CSS class, domain) from"
" the image or iframe code to be excluded (one per line). %1$sMore info%2$s"
msgstr ""
"Resim veya iframe kodundan hariç tutulacak anahtar kelimeleri (örn. resim "
"dosya adı, CSS dosya adı, CSS sınıfı, etki alanı) belirtin (her satıra bir "
"tane). %1$sDaha fazla bilgi%2$s"

#: inc/Engine/Admin/Settings/Page.php:1032
msgid "Add missing image dimensions"
msgstr "Eksik resim boyutlarını ekle"

#: inc/Engine/Admin/Settings/Page.php:1052
msgid "Generate cache files, preload fonts"
msgstr "Önbellek dosyalarını oluşturun, yazı tiplerini önyükleyin"

#: inc/Engine/Admin/Settings/Page.php:1064
msgid "Preload Cache"
msgstr "Önbelleği Önyükle"

#. translators: %1$s = opening <a> tag, %2$s = closing </a> tag.
#: inc/Engine/Admin/Settings/Page.php:1067
msgid ""
"When you enable preloading WP Rocket will automatically detect your sitemaps"
" and save all URLs to the database. The plugin will make sure that your "
"cache is always preloaded."
msgstr ""
"Önceden yüklemeyi etkinleştirdiğinizde WP Rocket, site haritalarınızı "
"otomatik olarak algılayacak ve tüm URL’leri veritabanına kaydedecek. "
"Eklenti, önbelleğinizin her zaman önceden yüklendiğinden emin olacak."

#: inc/Engine/Admin/Settings/Page.php:1075
msgid "Preload Links"
msgstr "Bağlantıları Önyükle"

#. translators: %1$s = opening <a> tag, %2$s = closing </a> tag.
#: inc/Engine/Admin/Settings/Page.php:1078
msgid ""
"Link preloading improves the perceived load time by downloading a page when "
"a user hovers over the link. %1$sMore info%2$s"
msgstr ""
"Bağlantı önyükleme, kullanıcı bağlantının üzerine geldiğinde bir sayfa "
"indirerek algılanan yükleme süresini iyileştirir. %1$sDaha fazla bilgi%2$s"

#: inc/Engine/Admin/Settings/Page.php:1086
msgid "Prefetch DNS Requests"
msgstr "DNS İsteklerini Önceden Getirme"

#: inc/Engine/Admin/Settings/Page.php:1088
msgid ""
"DNS prefetching can make external files load faster, especially on mobile "
"networks"
msgstr ""
"Önceden DNS getirme, harici dosyaları daha hızlı yüklemeyi sağlayabilir, "
"özellikle de mobil ağlarda"

#: inc/Engine/Admin/Settings/Page.php:1093
msgid "Preload Fonts"
msgstr "Yazı Tiplerini Önyükle"

#. translators: %1$s = opening <a> tag, %2$s = closing </a> tag.
#: inc/Engine/Admin/Settings/Page.php:1096
msgid ""
"Improves performance by helping browsers discover fonts in CSS files. "
"%1$sMore info%2$s"
msgstr ""
"Tarayıcıların CSS dosyalarındaki yazı tiplerini keşfetmelerine yardımcı "
"olarak performansı artırır. %1$sDaha fazla bilgi%2$s"

#: inc/Engine/Admin/Settings/Page.php:1110
msgid "Activate Preloading"
msgstr "Önyüklemeyi aktif et"

#: inc/Engine/Admin/Settings/Page.php:1121
msgid "Exclude URLs"
msgstr "Hariç tutulan URL’ler"

#. translators: %1$s = opening <a> tag, %2$s = closing </a> tag.
#: inc/Engine/Admin/Settings/Page.php:1126
msgid ""
"Specify URLs to be excluded from the preload feature (one per line). "
"%1$sMore info%2$s"
msgstr ""
"Önyükleme özelliğinden hariç tutulacak URL’leri belirtin (her satıra bir "
"tane). %1$sDaha fazla bilgi%2$s"

#: inc/Engine/Admin/Settings/Page.php:1137
msgid "URLs to prefetch"
msgstr "Önceden gertirmek için URL’ler"

#: inc/Engine/Admin/Settings/Page.php:1138
msgid ""
"Specify external hosts to be prefetched (no <code>http:</code>, one per "
"line)"
msgstr ""
"Önceden getirilecek olan harici anamakineleri belirtin (<code>http:</code> "
"olmadan, her satıra bir tane)"

#: inc/Engine/Admin/Settings/Page.php:1147
msgid "Fonts to preload"
msgstr "Önyüklenecek yazı tipleri"

#: inc/Engine/Admin/Settings/Page.php:1148
msgid ""
"Specify urls of the font files to be preloaded (one per line). Fonts must be"
" hosted on your own domain, or the domain you have specified on the CDN tab."
msgstr ""
"Önyüklenecek yazı tipi dosyalarının URL’lerini belirtin (her satıra bir "
"tane). Yazı tipleri kendi etki alanınızda veya CDN sekmesinde belirttiğiniz "
"etki alanında barındırılmak zorundadır."

#: inc/Engine/Admin/Settings/Page.php:1149
msgid ""
"The domain part of the URL will be stripped automatically.<br/>Allowed font "
"extensions: otf, ttf, svg, woff, woff2."
msgstr ""
"URL’nin etki alanı kısmı otomatik olarak sadeleştirilecektir.<br/>İzin "
"verilen yazı tipi uzantıları: otf, ttf, svg, woff, woff2."

#: inc/Engine/Admin/Settings/Page.php:1158
msgid "Enable link preloading"
msgstr "Bağlantı önyüklemeyi etkinleştir"

#: inc/Engine/Admin/Settings/Page.php:1177
msgid "Advanced Rules"
msgstr "Gelişmiş Kurallar"

#: inc/Engine/Admin/Settings/Page.php:1178
msgid "Fine-tune cache rules"
msgstr "Önbellek kurallarına ince ayar yapın"

#: inc/Engine/Admin/Settings/Page.php:1191
msgid ""
"Sensitive pages like custom login/logout URLs should be excluded from cache."
msgstr ""
"Özel oturum açma/oturum kapatma URL’leri gibi hassas sayfalar önbellekten "
"hariç tutulmalıdır."

#: inc/Engine/Admin/Settings/Page.php:1194
msgctxt "plugin name"
msgid "WooCommerce"
msgstr "WooCommerce"

#: inc/Engine/Admin/Settings/Page.php:1196
msgctxt "plugin name"
msgid "Easy Digital Downloads"
msgstr "Easy Digital Downloads"

#: inc/Engine/Admin/Settings/Page.php:1198
msgctxt "plugin name"
msgid "iThemes Exchange"
msgstr "iThemes Exchange"

#: inc/Engine/Admin/Settings/Page.php:1200
msgctxt "plugin name"
msgid "Jigoshop"
msgstr "Jigoshop"

#: inc/Engine/Admin/Settings/Page.php:1202
msgctxt "plugin name"
msgid "WP-Shop"
msgstr "WP-Shop"

#. translators: %1$s = opening <a> tag, %2$s = plugin name, %3$s closing </a>
#. tag.
#: inc/Engine/Admin/Settings/Page.php:1208
msgid ""
"<br>Cart, checkout and \"my account\" pages set in "
"<strong>%1$s%2$s%3$s</strong> will be detected and never cached by default."
msgstr ""
"<br><strong>%1$s%2$s%3$s</strong> içinde ayarlanan sepet, ödeme ve "
"\"hesabım\" sayfaları algılanacak ve varsayılan olarak asla "
"önbelleklenmeyecektir."

#: inc/Engine/Admin/Settings/Page.php:1218
msgid "Cache Lifespan"
msgstr "Önbellek Geçerlilik Süresi"

#. translators: %1$s = opening <a> tag, %2$s = closing </a> tag.
#: inc/Engine/Admin/Settings/Page.php:1221
msgid ""
"Cache files older than the specified lifespan will be deleted.<br>Enable "
"%1$spreloading%2$s for the cache to be rebuilt automatically after lifespan "
"expiration."
msgstr ""
"Belirtilen geçerlilik süresinden eski önbellek dosyaları "
"silinecektir.<br>Geçerlilik süresi sona erdikten sonra otomatik olarak "
"yeniden oluşturulması için önbellek %1$sönyüklemeyi%2$s etkinleştirin."

#: inc/Engine/Admin/Settings/Page.php:1237
msgid "Never Cache Cookies"
msgstr "Asla Önbelleklenmeyen Tanımlama Bilgileri"

#: inc/Engine/Admin/Settings/Page.php:1255
msgid "Cache Query String(s)"
msgstr "Önbelleklenen Sorgu Dizgi(leri)si"

#. translators: %1$s = opening <a> tag, %2$s = closing </a> tag.
#: inc/Engine/Admin/Settings/Page.php:1258
msgid ""
"%1$sCache for query strings%2$s enables you to force caching for specific "
"GET parameters."
msgstr ""
"%1$sSorgu dizgilerini önbelleklemek%2$s belirli GET parametreleri için "
"önbelleklemeye zorlamanızı etkinleştirir."

#: inc/Engine/Admin/Settings/Page.php:1269
msgid ""
"Specify time after which the global cache is cleared<br>(0 = unlimited )"
msgstr ""
"Genel önbelleğin ne kadar süre sonra temizleneceği zamanı belirtin<br>(0 = "
"sınırsız)"

#. translators: %1$s = opening <a> tag, %2$s = closing </a> tag.
#: inc/Engine/Admin/Settings/Page.php:1271
msgid ""
"Reduce lifespan to 10 hours or less if you notice issues that seem to appear"
" periodically. %1$sWhy?%2$s"
msgstr ""
"Eğer belirli aralıklarla görünen sorunları fark ederseniz geçerlilik "
"süresini 10 saat veya daha azına düşürün. %1$sNeden?%2$s"

#: inc/Engine/Admin/Settings/Page.php:1277
#: inc/Engine/License/views/promo-banner.php:30
#: inc/Engine/License/views/renewal-soon-banner.php:13
msgid "Hours"
msgstr "Saat"

#: inc/Engine/Admin/Settings/Page.php:1278
#: inc/Engine/License/views/promo-banner.php:29
#: inc/Engine/License/views/renewal-soon-banner.php:12
msgid "Days"
msgstr "Gün"

#: inc/Engine/Admin/Settings/Page.php:1283
msgid ""
"Specify URLs of pages or posts that should never be cached (one per line)"
msgstr ""
"Asla önbelleklenmemesi gereken sayfa ve yazıların URL’lerini belirtin (her "
"satıra bir tane)"

#: inc/Engine/Admin/Settings/Page.php:1284
#: inc/Engine/Admin/Settings/Page.php:1312
msgid ""
"The domain part of the URL will be stripped automatically.<br>Use (.*) "
"wildcards to address multiple URLs under a given path."
msgstr ""
"URL’nin etki alanı kısmı otomatik olarak sadeleştirilecektir.<br>Verilen yol"
" altında çoklu URL’leri adreslemek için (.*) joker karakterlerini kullanın."

#: inc/Engine/Admin/Settings/Page.php:1293
msgid ""
"Specify full or partial IDs of cookies that, when set in the visitor's "
"browser, should prevent a page from getting cached (one per line)"
msgstr ""
"Ziyaretçinin tarayıcısında ayarlandığında, bir sayfanın önbelleklenmesine "
"engel olması gereken, tanımlama bilgilerinin tam ya da kısmi kimliklerini "
"belirtin (satır başına bir tane)"

#: inc/Engine/Admin/Settings/Page.php:1301
msgid ""
"Specify user agent strings that should never see cached pages (one per line)"
msgstr ""
"Önbelleklenmiş sayfaları asla görmemesi gereken kullanıcı tanıtıcı dizgisini"
" belirtin (her satıra bir tane)"

#: inc/Engine/Admin/Settings/Page.php:1302
msgid "Use (.*) wildcards to detect parts of UA strings."
msgstr ""
"UA dizgilerinin parçalarını algılamak için (.*) joker karakterlerini "
"kullanın."

#: inc/Engine/Admin/Settings/Page.php:1311
msgid ""
"Specify URLs you always want purged from cache whenever you update any post "
"or page (one per line)"
msgstr ""
"Herhangi bir yazıyı veya sayfayı her güncellediğinizde daima önbellekten "
"temizlenmesini istediğiniz URL’leri belirtin (her satıra bir tane)"

#: inc/Engine/Admin/Settings/Page.php:1320
msgid "Specify query strings for caching (one per line)"
msgstr "Önbellekleme için sorgu dizgilerini belirtin (her satıra bir tane)"

#: inc/Engine/Admin/Settings/Page.php:1346
msgid "Optimize, reduce bloat"
msgstr "İyileştirin, şişmeyi azaltın"

#: inc/Engine/Admin/Settings/Page.php:1353
msgid "Post Cleanup"
msgstr "Yazı Temizleme"

#: inc/Engine/Admin/Settings/Page.php:1355
msgid ""
"Post revisions and drafts will be permanently deleted. Do not use this "
"option if you need to retain revisions or drafts."
msgstr ""
"Yazı incelemeleri ve taslaklar kalıcı olarak silinecektir. İncelemeleri ya "
"da taslakları tutmanız gerekiyorsa bu seçeneği kullanmayın."

#: inc/Engine/Admin/Settings/Page.php:1360
msgid "Comments Cleanup"
msgstr "Yorumları Temizleme"

#: inc/Engine/Admin/Settings/Page.php:1362
msgid "Spam and trashed comments will be permanently deleted."
msgstr "İstenmeyen mesaj ve çöpe atılmış yorumlar kalıcı olarak silinecektir."

#: inc/Engine/Admin/Settings/Page.php:1366
msgid "Transients Cleanup"
msgstr "Geçicileri Temizleme"

#: inc/Engine/Admin/Settings/Page.php:1368
msgid ""
"Transients are temporary options; they are safe to remove. They will be "
"automatically regenerated as your plugins require them."
msgstr ""
"Geçiciler geçici seçeneklerdir; kaldırması güvenlidir. Eklentilerinizin "
"gerektirdiklerini otomatik olarak oluşturacaklardır."

#: inc/Engine/Admin/Settings/Page.php:1372
msgid "Database Cleanup"
msgstr "Veritabanı Temizleme"

#: inc/Engine/Admin/Settings/Page.php:1374
msgid "Reduces overhead of database tables"
msgstr "Veritabanı tablolarının ek yükünü azaltır"

#: inc/Engine/Admin/Settings/Page.php:1378
msgid "Automatic Cleanup"
msgstr "Otomatik Temizleme"

#. translators: %s is the number of revisions found in the database. It's a
#. formatted number, don't use %d.
#: inc/Engine/Admin/Settings/Page.php:1391
msgid "%s revision in your database."
msgid_plural "%s revisions in your database."
msgstr[0] "Veritabanınızda %s düzeltme."
msgstr[1] "Veritabanınızda %s düzeltme."

#. translators: %s is the number of revisions found in the database. It's a
#. formatted number, don't use %d.
#: inc/Engine/Admin/Settings/Page.php:1401
msgid "%s draft in your database."
msgid_plural "%s drafts in your database."
msgstr[0] "Veritabanınızda %s taslak."
msgstr[1] "Veritabanınızda %s taslak."

#. translators: %s is the number of revisions found in the database. It's a
#. formatted number, don't use %d.
#: inc/Engine/Admin/Settings/Page.php:1411
msgid "%s trashed post in your database."
msgid_plural "%s trashed posts in your database."
msgstr[0] "Veritabanınızda %s çöpe atılmış yazı."
msgstr[1] "Veritabanınızda %s çöpe atılmış yazı."

#. translators: %s is the number of revisions found in the database. It's a
#. formatted number, don't use %d.
#: inc/Engine/Admin/Settings/Page.php:1421
msgid "%s spam comment in your database."
msgid_plural "%s spam comments in your database."
msgstr[0] "Veritabanınızda %s istenmeyen yorum."
msgstr[1] "Veritabanınızda %s istenmeyen yorum."

#. translators: %s is the number of revisions found in the database. It's a
#. formatted number, don't use %d.
#: inc/Engine/Admin/Settings/Page.php:1431
msgid "%s trashed comment in your database."
msgid_plural "%s trashed comments in your database."
msgstr[0] "Veritabanınızda %s çöpe atılmış yorum."
msgstr[1] "Veritabanınızda %s çöpe atılmış yorum."

#: inc/Engine/Admin/Settings/Page.php:1439
msgid "All transients"
msgstr "Tüm geçiciler"

#. translators: %s is the number of revisions found in the database. It's a
#. formatted number, don't use %d.
#: inc/Engine/Admin/Settings/Page.php:1441
msgid "%s transient in your database."
msgid_plural "%s transients in your database."
msgstr[0] "Veritabanınızda %s geçici."
msgstr[1] "Veritabanınızda %s geçici."

#: inc/Engine/Admin/Settings/Page.php:1449
msgid "Optimize Tables"
msgstr "Tabloları iyileştir"

#. translators: %s is the number of revisions found in the database. It's a
#. formatted number, don't use %d.
#: inc/Engine/Admin/Settings/Page.php:1451
msgid "%s table to optimize in your database."
msgid_plural "%s tables to optimize in your database."
msgstr[0] "Veritabanınızda iyileştirmek için %s tablo."
msgstr[1] "Veritabanınızda iyileştirmek için %s tablo."

#: inc/Engine/Admin/Settings/Page.php:1462
msgid "Schedule Automatic Cleanup"
msgstr "Otomatik Temizlemeyi Zamanla"

#: inc/Engine/Admin/Settings/Page.php:1474
msgid "Frequency"
msgstr "Sıklıkla"

#: inc/Engine/Admin/Settings/Page.php:1482
msgid "Daily"
msgstr "Günlük"

#: inc/Engine/Admin/Settings/Page.php:1483
msgid "Weekly"
msgstr "Haftalık"

#: inc/Engine/Admin/Settings/Page.php:1484
msgid "Monthly"
msgstr "Aylık"

#: inc/Engine/Admin/Settings/Page.php:1501
msgid "Integrate your CDN"
msgstr "CDN’nizi bütünleştirin"

#: inc/Engine/Admin/Settings/Page.php:1513
msgid ""
"All URLs of static files (CSS, JS, images) will be rewritten to the CNAME(s)"
" you provide."
msgstr ""
"Sabit dosyaların (CSS, JS, resimler) tüm URL’leri, verdiğiniz CNAME(lere)e "
"yeniden yazılacaktır."

#. translators: %1$s = opening link tag, %2$s = closing link tag.
#: inc/Engine/Admin/Settings/Page.php:1515
msgid ""
"Not required for services like Cloudflare and Sucuri. Please see our "
"available %1$sAdd-ons%2$s."
msgstr ""
"Cloudflare ve Sucuri gibi hizmetler için gerekmez. Lütfen mevcut "
"%1$sEklentilerimize%2$s bakın."

#. translators: %1$s = opening em tag, %2$l = list of add-on name(s), %3$s =
#. closing em tag.
#: inc/Engine/Admin/Settings/Page.php:1561
msgid ""
"%1$s%2$l Add-on%3$s is currently enabled. Configuration of the CDN settings "
"is not required for %2$l to work on your site."
msgid_plural ""
"%1$s%2$l Add-ons%3$s are currently enabled. Configuration of the CDN "
"settings is not required for %2$l to work on your site."
msgstr[0] ""
"%1$s%2$l Eklentisi%3$s şu anda etkinleştirildi. CDN ayarlarının "
"yapılandırması, sitenizde çalışması amacıyla %2$l için gerekmez."
msgstr[1] ""
"%1$s%2$l Eklentileri%3$s şu anda etkinleştirildi. CDN ayarlarının "
"yapılandırması, sitenizde çalışması amacıyla %2$l için gerekmez."

#: inc/Engine/Admin/Settings/Page.php:1587
msgid "Enable Content Delivery Network"
msgstr "İçerik Dağıtım Ağını (CDN) etkinleştir"

#: inc/Engine/Admin/Settings/Page.php:1596
#: inc/Engine/CDN/RocketCDN/AdminPageSubscriber.php:159
msgid "CDN CNAME(s)"
msgstr "CDN CNAME(ler)"

#: inc/Engine/Admin/Settings/Page.php:1597
#: inc/Engine/CDN/RocketCDN/AdminPageSubscriber.php:160
msgid "Specify the CNAME(s) below"
msgstr "Aşağıda CNAME(leri) belirtin"

#: inc/Engine/Admin/Settings/Page.php:1604
msgid ""
"Specify URL(s) of files that should not get served via CDN (one per line)."
msgstr ""
"CDN aracılığıyla sunulmaması gereken dosyaların URL(lerini)sini belirtin "
"(her satıra bir tane)."

#: inc/Engine/Admin/Settings/Page.php:1605
msgid ""
"The domain part of the URL will be stripped automatically.<br>Use (.*) "
"wildcards to exclude all files of a given file type located at a specific "
"path."
msgstr ""
"URL’nin etki alanı kısmı otomatik olarak sadeleştirilecektir.<br>Belirli bir"
" yolda bulunan verilen bir dosya türünün tüm dosyalarını hariç tutmak için "
"(.*) joker karakterlerini kullanın."

#: inc/Engine/Admin/Settings/Page.php:1628
#: inc/Engine/Admin/Settings/Page.php:1636
msgid "Heartbeat"
msgstr "Kalp Atışı"

#: inc/Engine/Admin/Settings/Page.php:1629
msgid "Control WordPress Heartbeat API"
msgstr "WordPress Kalp Atışı API’sini denetleyin"

#: inc/Engine/Admin/Settings/Page.php:1637
msgid ""
"Reducing or disabling the Heartbeat API’s activity can help save some of "
"your server’s resources."
msgstr ""
"Kalp Atışı API’si etkinliğini azaltmak veya etkisizleştirmek, sunucunuzun "
"kaynaklarının bazılarını kurtarmasına yardımcı olabilir."

#: inc/Engine/Admin/Settings/Page.php:1643
msgid "Reduce or disable Heartbeat activity"
msgstr "Kalp Atışı etkinliğini azaltma veya etkisizleştirme"

#: inc/Engine/Admin/Settings/Page.php:1644
msgid ""
"Reducing activity will change Heartbeat frequency from one hit each minute "
"to one hit every 2 minutes."
msgstr ""
"Etkinliği azaltmak Kalp Atışı sıklığını, her dakikada bir vuruştan her 2 "
"dakikada bir vuruşa değiştirecektir."

#: inc/Engine/Admin/Settings/Page.php:1644
msgid ""
"Disabling Heartbeat entirely may break plugins and themes using this API."
msgstr ""
"Kalp Atışı’nın tamamen etkisizleştirilmesi bu API’yi kullanan eklentileri ve"
" temaları bozabilir."

#: inc/Engine/Admin/Settings/Page.php:1658
msgid "Do not limit"
msgstr "Sınırlandırma"

#: inc/Engine/Admin/Settings/Page.php:1659
msgid "Reduce activity"
msgstr "Etkinliği azalt"

#: inc/Engine/Admin/Settings/Page.php:1660
msgid "Disable"
msgstr "Etkisizleştir"

#: inc/Engine/Admin/Settings/Page.php:1668
msgid "Control Heartbeat"
msgstr "Kalp Atışı’nı denetle"

#: inc/Engine/Admin/Settings/Page.php:1677
msgid "Behavior in backend"
msgstr "Arka uçtaki davranış"

#: inc/Engine/Admin/Settings/Page.php:1684
msgid "Behavior in post editor"
msgstr "Yazı düzenleyicideki davranış"

#: inc/Engine/Admin/Settings/Page.php:1690
msgid "Behavior in frontend"
msgstr "Ön uçtaki davranış"

#: inc/Engine/Admin/Settings/Page.php:1709
#: views/settings/page-sections/tutorials.php:39
msgid "Add-ons"
msgstr "Eklentiler"

#: inc/Engine/Admin/Settings/Page.php:1710
msgid "Add more features"
msgstr "Daha fazla özellik ekleyin"

#: inc/Engine/Admin/Settings/Page.php:1717
msgid "One-click Rocket Add-ons"
msgstr "Tek Tık Rocket Eklentileri"

#: inc/Engine/Admin/Settings/Page.php:1718
msgid ""
"One-Click Add-ons are features extending available options without "
"configuration needed. Switch the option \"on\" to enable from this screen."
msgstr ""
"Tek Tık Eklentiler, yapılandırma gerekmeden mevcut seçenekleri genişleten "
"özelliklerdir. Etkinleştirmek için seçeneği bu ekrandan \"açık\" olarak "
"değiştirin."

#: inc/Engine/Admin/Settings/Page.php:1728
msgid "Rocket Add-ons"
msgstr "Rocket Eklentileri"

#: inc/Engine/Admin/Settings/Page.php:1729
msgid "Rocket Add-ons are complementary features extending available options."
msgstr ""
"Rocket Eklentileri mevcut seçenekleri genişleten tamamlayıcı özelliklerdir."

#: inc/Engine/Admin/Settings/Page.php:1740
msgid "User Cache"
msgstr "Kullanıcı Önbelleği"

#: inc/Engine/Admin/Settings/Page.php:1746
msgid ""
"If you need to create a dedicated set of cache files for each logged-in "
"WordPress user, you must activate this add-on."
msgstr ""
"Eğer oturum açan her WordPress kullanıcısı için adanmış bir önbellek dosyası"
" kümesi oluşturmanız gerekiyorsa bu eklentiyi etkinleştirmek zorundasınız."

#. translators: %1$s = opening <a> tag, %2$s = closing </a> tag.
#: inc/Engine/Admin/Settings/Page.php:1748
msgid ""
"User cache is great when you have user-specific or restricted content on "
"your website.<br>%1$sLearn more%2$s"
msgstr ""
"Web sitenizde kullanıcıya özgü ya da kısıtlı içeriğe sahip olduğunuzda "
"kullanıcı önbelleği mükemmeldir.<br>%1$sDaha fazla bilgi edinin%2$s"

#: inc/Engine/Admin/Settings/Page.php:1761
#: inc/Engine/Admin/Settings/Page.php:1930
msgid "Cloudflare"
msgstr "Cloudflare"

#: inc/Engine/Admin/Settings/Page.php:1767
msgid "Integrate your Cloudflare account with this add-on."
msgstr "Bu eklenti ile Cloudflare hesabınızı bütünleştirin."

#: inc/Engine/Admin/Settings/Page.php:1768
msgid ""
"Provide your account email, global API key, and domain to use options such "
"as clearing the Cloudflare cache and enabling optimal settings with WP "
"Rocket."
msgstr ""
"Cloudflare önbelleğini temizlemek ve WP Rocket ile en uygun ayarları "
"etkinleştirmek gibi seçenekleri kullanmak için hesap e-postanızı, genel API "
"anahtarını ve etki alanını sağlar."

#. translators: %1$s = opening span tag, %2$s = closing span tag.
#: inc/Engine/Admin/Settings/Page.php:1771
msgid ""
"%1$sPlanning on using Automatic Platform Optimization (APO)?%2$s Just "
"activate the official Cloudflare plugin and configure it. WP Rocket will "
"automatically enable compatibility."
msgstr ""
"%1$sOtomatik Platform İyileştirmesini (APO) kullanmayı mı "
"planlıyorsunuz?%2$s Sadece resmi Cloudflare eklentisini aktif edin ve "
"yapılandırın. WP Rocket uyumluluğu otomatik olarak etkinleştirecektir."

#: inc/Engine/Admin/Settings/Page.php:1819
msgid "Varnish"
msgstr "Varnish"

#: inc/Engine/Admin/Settings/Page.php:1825
msgid "If Varnish runs on your server, you must activate this add-on."
msgstr ""
"Varnish sunucunuzda çalışıyorsa, bu eklentiyi aktif etmek zorundasınız."

#. translators: %1$s = opening <a> tag, %2$s = closing </a> tag.
#: inc/Engine/Admin/Settings/Page.php:1827
msgid ""
"Varnish cache will be purged each time WP Rocket clears its cache to ensure "
"content is always up-to-date.<br>%1$sLearn more%2$s"
msgstr ""
"Varnish önbelleği, WP Rocket önbelleğini her temizlediğinde içeriğin her "
"zaman güncel olmasını sağlamak için temizlenecektir.<br>%1$sDaha fazla bilgi"
" edinin%2$s"

#: inc/Engine/Admin/Settings/Page.php:1862
msgid "WebP Compatibility"
msgstr "WebP Uyumluluğu"

#: inc/Engine/Admin/Settings/Page.php:1868
msgid "Improve browser compatibility for WebP images."
msgstr "WebP resimleri için tarayıcı uyumluluğunu iyileştirin."

#. translators: %1$s and %3$s = opening <a> tag, %2$s = closing </a> tag.
#: inc/Engine/Admin/Settings/Page.php:1872
msgid ""
"Enable this option if you would like WP Rocket to serve WebP images to "
"compatible browsers. Please note that WP Rocket cannot create WebP images "
"for you. To create WebP images we recommend %1$sImagify%2$s. %3$sMore "
"info%2$s"
msgstr ""
"WP Rocket’in WebP resimlerini uyumlu tarayıcılara sunmasını istiyorsanız bu "
"seçeneği etkinleştirin. WP Rocket’in sizin için WebP resimlerini "
"oluşturamayacağını lütfen unutmayın. WebP resimleri oluşturmak için "
"%1$sImagify%2$s öneriyoruz. %3$sDaha fazla bilgi%2$s"

#: inc/Engine/Admin/Settings/Page.php:1892
msgid "Clear the Sucuri cache when WP Rocket’s cache is cleared."
msgstr "WP Rocket’in önbelleği temizlendiğinde Sucuri önbelleğini temizler."

#: inc/Engine/Admin/Settings/Page.php:1895
msgid ""
"Provide your API key to clear the Sucuri cache when WP Rocket’s cache is "
"cleared."
msgstr ""
"WP Rocket’in önbelleği temizlendiğinde Sucuri önbelleğini temizlemek için "
"API anahtarınızı verin."

#: inc/Engine/Admin/Settings/Page.php:1903
#: inc/Engine/Admin/Settings/Page.php:2047
msgid "Sucuri"
msgstr "Sucuri"

#: inc/Engine/Admin/Settings/Page.php:1909
msgid "Synchronize Sucuri cache with this add-on."
msgstr "Bu eklenti ile Sucuri önbelleğini eşitleyin."

#: inc/Engine/Admin/Settings/Page.php:1947
msgid "Cloudflare credentials"
msgstr "Cloudflare kimlik bilgileri"

#: inc/Engine/Admin/Settings/Page.php:1956
msgid "Cloudflare settings"
msgstr "Cloudflare ayarları"

#: inc/Engine/Admin/Settings/Page.php:1970
msgctxt "Cloudflare"
msgid "Global API key:"
msgstr "Genel API anahtarı"

#: inc/Engine/Admin/Settings/Page.php:1971
msgctxt "Cloudflare"
msgid "Find your API key"
msgstr "API anahtarınızı bulun"

#: inc/Engine/Admin/Settings/Page.php:1983
msgctxt "Cloudflare"
msgid "Account email"
msgstr "Hesap e-postası"

#: inc/Engine/Admin/Settings/Page.php:1992
msgctxt "Cloudflare"
msgid "Zone ID"
msgstr "Bölge Kimliği - Zone ID"

#: inc/Engine/Admin/Settings/Page.php:2002
msgid "Development mode"
msgstr "Geliştirme kipi"

#. translators: %1$s = link opening tag, %2$s = link closing tag.
#: inc/Engine/Admin/Settings/Page.php:2004
msgid ""
"Temporarily activate development mode on your website. This setting will "
"automatically turn off after 3 hours. %1$sLearn more%2$s"
msgstr ""
"Web sitenizde geçici olarak geliştirme kipini aktif edin. Bu ayar otomatik "
"olarak 3 saat sonra kapanacak. %1$sDaha fazla bilgi edinin%2$s"

#: inc/Engine/Admin/Settings/Page.php:2012
msgid "Optimal settings"
msgstr "En uygun ayarlar"

#: inc/Engine/Admin/Settings/Page.php:2013
msgid ""
"Automatically enhances your Cloudflare configuration for speed, performance "
"grade and compatibility."
msgstr ""
"Hız, performans puanı ve uyumluluk için Cloudflare yapılandırmanızı otomatik"
" olarak iyileştirir."

#: inc/Engine/Admin/Settings/Page.php:2021
msgid "Relative protocol"
msgstr "Göreli protokol"

#: inc/Engine/Admin/Settings/Page.php:2022
msgid ""
"Should only be used with Cloudflare's flexible SSL feature. URLs of static "
"files (CSS, JS, images) will be rewritten to use // instead of http:// or "
"https://."
msgstr ""
"Sadece Cloudflare’in esnek SSL özelliği ile kullanılmalıdır. Sabit "
"dosyaların (CSS, JS, resimler) URL’leri, http:// veya https:// yerine // "
"kullanmak için yeniden yazılacaktır."

#: inc/Engine/Admin/Settings/Page.php:2060
msgid "Sucuri credentials"
msgstr "Sucuri kimlik bilgileri"

#: inc/Engine/Admin/Settings/Page.php:2073
msgctxt "Sucuri"
msgid ""
"Firewall API key (for plugin), must be in format {32 characters}/{32 "
"characters}:"
msgstr ""
"Güvenlik Duvarı API anahtarı (eklenti için), {32 karakter}/{32 karakter} "
"biçiminde olmak zorundadır:"

#: inc/Engine/Admin/Settings/Page.php:2074
msgctxt "Sucuri"
msgid "Find your API key"
msgstr "API anahtarınızı bulun"

#. translators: %1$s: opening strong tag, %2$s: closing strong tag, %3$s:
#. opening a tag, %4$s: option a tag, %5$s: opening a tag.
#: inc/Engine/Admin/Settings/Page.php:2295
msgid ""
"%1$sWP Rocket:%2$s the plugin has been updated to the 3.16 version. Our "
"brand new feature %3$sOptimize critical images%5$s is automatically "
"activated now! Also, the Cache tab was removed but the existing features "
"will remain working, %4$ssee more here%5$s."
msgstr ""
"%1$sWP Rocket:%2$s eklenti 3.16 sürümüne güncellendi. Yepyeni özelliğimiz "
"%3$sÖnemli resimleri iyileştir%5$s artık otomatik olarak etkinleştirildi! "
"Ayrıca, Önbellek sekmesi kaldırıldı ancak varolan özellikler çalışmaya devam"
" edecek, %4$sdaha fazlasına buradan bakın%5$s."

#: inc/Engine/Admin/Settings/Settings.php:361
msgid ""
"Sucuri Add-on: The API key for the Sucuri firewall must be in format "
"<code>{32 characters}/{32 characters}</code>."
msgstr ""
"Sucuri Eklentisi: Sucuri güvenlik duvarı için API anahtarı, <code>{32 "
"karakter}/{32 karakter}</code> biçiminde olmak zorundadır."

#: inc/Engine/Admin/Settings/Settings.php:667
msgid ""
"Sorry! Adding /(.*) in Advanced Rules > Never Cache URL(s) was not saved "
"because it disables caching and optimizations for every page on your site."
msgstr ""
"Üzgünüz! Gelişmiş Kurallar > URL’(leri)yi Asla Önbellekleme içine /(.*) "
"eklemek, sitenizdeki her sayfa için önbelleğe almayı ve iyileştirmeleri "
"etkisizleştirdiğinden kaydedilmedi."

#: inc/Engine/Admin/Settings/Subscriber.php:172
msgid "Import, Export, Rollback"
msgstr "İçe aktarın, Dışa aktarın, Geri alın"

#: inc/Engine/Admin/Settings/Subscriber.php:197
#: views/settings/page-sections/imagify.php:14
msgid "Image Optimization"
msgstr "Resim İyileştirme"

#: inc/Engine/Admin/Settings/Subscriber.php:198
msgid "Compress your images"
msgstr "Resimlerinizi sıkıştırın"

#: inc/Engine/Admin/Settings/Subscriber.php:215
#: views/settings/page-sections/tutorials.php:48
msgid "Tutorials"
msgstr "Eğitimler"

#: inc/Engine/Admin/Settings/Subscriber.php:216
msgid "Getting started and how to videos"
msgstr "Başlarken ve nasıl yapılır videoları"

#: inc/Engine/Cache/PurgeExpired/Subscriber.php:75
msgid "WP Rocket Expired Cache Interval"
msgstr "WP Roket Süresi Dolmuş Önbellek Aralığı"

#: inc/Engine/Cache/WPCache.php:337
msgid "WP_CACHE value"
msgstr "WP_CACHE değeri"

#: inc/Engine/Cache/WPCache.php:354
msgid "Cache"
msgstr "Önbellek"

#: inc/Engine/Cache/WPCache.php:358
msgid ""
"The WP_CACHE constant needs to be set to true for WP Rocket cache to work "
"properly"
msgstr ""
"WP Rocket önbelleğinin düzgün çalışması için WP_CACHE sabitinin true olarak "
"ayarlanması gerekir"

#: inc/Engine/Cache/WPCache.php:367
msgid "WP_CACHE is set to true"
msgstr "WP_CACHE, true olarak ayarlı"

#: inc/Engine/Cache/WPCache.php:375
msgid "WP_CACHE is not set"
msgstr "WP_CACHE ayarlı değil"

#: inc/Engine/Cache/WPCache.php:383
msgid "WP_CACHE is set to false"
msgstr "WP_CACHE, false olarak ayarlı"

#: inc/Engine/CDN/RocketCDN/AdminPageSubscriber.php:90
msgid "Next Billing Date"
msgstr "Sonraki Fatura Tarihi"

#: inc/Engine/CDN/RocketCDN/AdminPageSubscriber.php:99
msgid "No Subscription"
msgstr "Abonelik Yok"

#: inc/Engine/CDN/RocketCDN/AdminPageSubscriber.php:135
msgid "Your RocketCDN subscription is currently active."
msgstr "RocketCDN aboneliğiniz şu anda etkin."

#. translators: %1$s = opening <code> tag, %2$s = CDN URL, %3$s = closing
#. </code> tag.
#: inc/Engine/CDN/RocketCDN/AdminPageSubscriber.php:141
msgid "To use RocketCDN, replace your CNAME with %1$s%2$s%3$s."
msgstr "RocketCDN’yi kullanmak için CNAME’nizi %1$s%2$s%3$s ile değiştirin."

#. translators: %1$is = opening link tag, %2$s = closing link tag.
#: inc/Engine/CDN/RocketCDN/AdminPageSubscriber.php:152
#: inc/Engine/CDN/RocketCDN/NoticesSubscriber.php:230
#: inc/Engine/CDN/RocketCDN/NoticesSubscriber.php:334
msgid "%1$sMore Info%2$s"
msgstr "%1$sDaha Fazla Bilgi%2$s"

#: inc/Engine/CDN/RocketCDN/APIClient.php:134
msgid ""
"We could not fetch the current price because RocketCDN API returned an "
"unexpected error code."
msgstr ""
"RocketCDN API beklenmeyen bir hata kodu döndürdüğünden şu anki fiyatı "
"getiremedik."

#: inc/Engine/CDN/RocketCDN/APIClient.php:140
msgid "RocketCDN is not available at the moment. Please retry later."
msgstr ""
"RocketCDN şu anda kullanılabilir değil. Lütfen daha sonra yeniden deneyin."

#: inc/Engine/CDN/RocketCDN/APIClient.php:177
msgid "RocketCDN cache purge failed: Missing identifier parameter."
msgstr ""
"RocketCDN önbellek temizleme başarısız oldu: Eksik tanımlayıcı parametresi."

#: inc/Engine/CDN/RocketCDN/APIClient.php:186
msgid "RocketCDN cache purge failed: Missing user token."
msgstr ""
"RocketCDN önbellek temizleme başarısız oldu: Eksik kullanıcı belirteci."

#: inc/Engine/CDN/RocketCDN/APIClient.php:212
msgid ""
"RocketCDN cache purge failed: The API returned an unexpected response code."
msgstr ""
"RocketCDN önbellek temizleme başarısız oldu: API beklenmeyen bir yanıt kodu "
"döndürdü."

#: inc/Engine/CDN/RocketCDN/APIClient.php:221
msgid "RocketCDN cache purge failed: The API returned an empty response."
msgstr ""
"RocketCDN önbellek temizleme başarısız oldu: API boş bir yanıt döndürdü."

#: inc/Engine/CDN/RocketCDN/APIClient.php:230
msgid "RocketCDN cache purge failed: The API returned an unexpected response."
msgstr ""
"RocketCDN önbellek temizleme başarısız oldu: API beklenmeyen bir yanıt "
"döndürdü."

#. translators: %s = message returned by the API.
#: inc/Engine/CDN/RocketCDN/APIClient.php:239
msgid "RocketCDN cache purge failed: %s."
msgstr "RocketCDN önbellek temizleme başarısız oldu: %s"

#: inc/Engine/CDN/RocketCDN/APIClient.php:247
msgid "RocketCDN cache purge successful."
msgstr "RocketCDN önbellek temizleme başarılı."

#: inc/Engine/CDN/RocketCDN/RESTSubscriber.php:137
#: tests/Integration/inc/Engine/CDN/RocketCDN/RESTSubscriber/enable.php:72
#: tests/Integration/inc/Engine/CDN/RocketCDN/RESTSubscriber/registerEnableRoute.php:302
msgid "RocketCDN enabled"
msgstr "RocketCDN etkinleştirildi"

#: inc/Engine/CDN/RocketCDN/RESTSubscriber.php:160
#: tests/Integration/inc/Engine/CDN/RocketCDN/RESTSubscriber/disable.php:31
#: tests/Integration/inc/Engine/CDN/RocketCDN/RESTSubscriber/registerDisableRoute.php:147
msgid "RocketCDN disabled"
msgstr "RocketCDN etkisizleştirildi"

#. Translators: %s = date formatted using date_i18n() and get_option(
#. 'date_format' ).
#: inc/Engine/CDN/RocketCDN/views/cta-big.php:27
msgid "Valid until %s only!"
msgstr "Sadece %s tarihine kadar geçerli!"

#: inc/Engine/CDN/RocketCDN/views/cta-big.php:36
msgid "Speed up your website thanks to:"
msgstr "Şunlar sayesinde web sitenizi hızlandırın:"

#. translators: %1$s = opening strong tag, %2$s = closing strong tag.
#: inc/Engine/CDN/RocketCDN/views/cta-big.php:42
msgid ""
"High performance Content Delivery Network (CDN) with %1$sunlimited "
"bandwidth%2$s"
msgstr ""
"%1$sSınırsız bant genişliği%2$s ile yüksek performanslı İçerik Dağıtım Ağı "
"(CDN)"

#. translators: %1$s = opening strong tag, %2$s = closing strong tag.
#: inc/Engine/CDN/RocketCDN/views/cta-big.php:48
msgid ""
"Easy configuration: the %1$sbest CDN settings%2$s are automatically applied"
msgstr ""
"Kolay yapılandırma: %1$sen iyi CDN ayarları%2$s otomatik olarak uygulanır"

#. translators: %1$s = opening strong tag, %2$s = closing strong tag.
#: inc/Engine/CDN/RocketCDN/views/cta-big.php:54
msgid ""
"WP Rocket integration: the CDN option is %1$sautomatically configured%2$s in"
" our plugin"
msgstr ""
"WP Rocket bütünleştirmesi: CDN seçeneği eklentimizde %1$sotomatik olarak "
"yapılandırılır%2$s"

#: inc/Engine/CDN/RocketCDN/views/cta-big.php:58
msgid "Learn more about RocketCDN"
msgstr "RocketCDN hakkında daha fazla bilgi edinin"

#. translators: %1$s = discounted price, %2$s = regular price.
#: inc/Engine/CDN/RocketCDN/views/cta-big.php:65
msgid ""
"*$%1$s/month for 12 months then $%2$s/month. You can cancel your "
"subscription at any time."
msgstr ""
"*12 ay için $%1$s/ay ardından $%2$s/ay. Aboneliğinizi istediğiniz zaman "
"iptal edebilirsiniz."

#: inc/Engine/CDN/RocketCDN/views/cta-big.php:86
msgid "Billed monthly"
msgstr "Aylık faturalandırılır"

#: inc/Engine/CDN/RocketCDN/views/cta-big.php:87
msgid "Get Started"
msgstr "Başlayın"

#: inc/Engine/CDN/RocketCDN/views/cta-big.php:92
msgid "Reduce this banner"
msgstr "Bu afişi küçült"

#: inc/Engine/CDN/RocketCDN/views/cta-small.php:17
msgid ""
"Speed up your website with RocketCDN, WP Rocket’s Content Delivery Network."
msgstr ""
"RocketCDN, WP Rocket’in İçerik Dağıtım Ağı ile web sitenizi hızlandırın."

#: inc/Engine/CDN/RocketCDN/views/cta-small.php:20
#: inc/Engine/CDN/RocketCDN/views/promote-notice.php:13
msgid "Learn More"
msgstr "Daha Fazla Bilgi Edinin"

#: inc/Engine/CDN/RocketCDN/views/dashboard-status.php:23
msgid "RocketCDN is unavailable on local domains and staging sites."
msgstr ""
"RocketCDN yerel etki alanlarında ve hazırlık sitelerinde kullanılamaz."

#: inc/Engine/CDN/RocketCDN/views/dashboard-status.php:32
msgid "Get RocketCDN"
msgstr "RocketCDN’yi Al"

#: inc/Engine/CDN/RocketCDN/views/promote-notice.php:11
msgid "New!"
msgstr "Yeni!"

#: inc/Engine/CDN/RocketCDN/views/promote-notice.php:12
msgid ""
"Speed up your website with RocketCDN, WP Rocket’s Content Delivery Network!"
msgstr ""
"RocketCDN, WP Rocket’in İçerik Dağıtım Ağı ile web sitenizi hızlandırın!"

#: inc/Engine/Common/JobManager/Cron/Subscriber.php:185
msgid "WP Rocket process pending jobs"
msgstr "WP Rocket işlemi bekleyen işler"

#: inc/Engine/Common/JobManager/Cron/Subscriber.php:204
msgid "WP Rocket clear failed jobs"
msgstr "WP Rocket başarısız işleri temizledi"

#: inc/Engine/Common/JobManager/Cron/Subscriber.php:221
msgid "WP Rocket process on submit jobs"
msgstr "Gönderme işlerinde WP Roket süreci"

#: inc/Engine/Common/Queue/RUCSSQueueRunner.php:265
msgid "Every minute"
msgstr "Her dakika"

#: inc/Engine/CriticalPath/Admin/Admin.php:264
msgid "Regenerate Critical Path CSS"
msgstr "Önemli Yol CSS’yi yeniden oluştur"

#: inc/Engine/CriticalPath/Admin/Post.php:144
#: views/cpcss/metabox/generate.php:47
msgid "Generate Specific CPCSS"
msgstr "Belirli ÖYCSS’yi Oluştur"

#: inc/Engine/CriticalPath/Admin/Post.php:145
#: views/cpcss/metabox/generate.php:45
msgid "Regenerate specific CPCSS"
msgstr "Belirli ÖYCSS’yi Yeniden Oluştur"

#: inc/Engine/CriticalPath/Admin/Post.php:216
msgid "This feature is not available for non-public post types."
msgstr ""
"Bu özellik ortak olmayan gönderi türleri için kullanılabilir değildir."

#: inc/Engine/CriticalPath/Admin/Post.php:219
msgid "%l to use this feature."
msgstr "Bu özelliği kullanmak için %l."

#. translators: %s = post type.
#: inc/Engine/CriticalPath/Admin/Post.php:222
msgid "Publish the %s"
msgstr "%s Yayınla"

#. translators: %s = post type.
#: inc/Engine/CriticalPath/Admin/Post.php:223
msgid "Enable Load CSS asynchronously in WP Rocket settings"
msgstr "WP Rocket ayarlarında CSS’yi eşzamanlı olmadan yükle’yi etkinleştir"

#: inc/Engine/CriticalPath/Admin/Post.php:224
msgid "Enable Load CSS asynchronously in the options above"
msgstr "Yukarıdaki seçeneklerde CSS’yi eşzamanlı olmadan yükle’yi etkinleştir"

#. translators: %1$s = type of content, %2$s = error message.
#: inc/Engine/CriticalPath/APIClient.php:64
msgid "Critical CSS for %1$s not generated. Error: %2$s"
msgstr "%1$s için Önemli CSS oluşturulmadı. Hata: %2$s"

#. translators: %s = item URL.
#: inc/Engine/CriticalPath/APIClient.php:170
msgid ""
"Critical CSS for %1$s on mobile not generated. Error: The API returned an "
"empty response."
msgstr ""
"Mobil üzerinde %1$s için Önemli CSS oluşturulmadı. Hata: API boş bir yanıt "
"döndürdü."

#. translators: %s = item URL.
#: inc/Engine/CriticalPath/APIClient.php:173
msgid ""
"Critical CSS for %1$s not generated. Error: The API returned an empty "
"response."
msgstr "%1$s için Önemli CSS oluşturulmadı. Hata: API boş bir yanıt döndürdü."

#. translators: %s = item URL.
#: inc/Engine/CriticalPath/APIClient.php:185
msgid "Critical CSS for %1$s on mobile not generated."
msgstr "Mobil üzerinde %1$s için Önemli CSS oluşturulmadı."

#. translators: %s = item URL.
#. translators: %1$s = item URL or item type.
#: inc/Engine/CriticalPath/APIClient.php:187
#: inc/Engine/CriticalPath/ProcessorService.php:194
msgid "Critical CSS for %1$s not generated."
msgstr "%1$s için Önemli CSS oluşturulmadı."

#. translators: %s = URL.
#: inc/Engine/CriticalPath/APIClient.php:195
msgid ""
"Critical CSS for %1$s on mobile not generated. Error: The API returned an "
"invalid response code."
msgstr ""
"Mobil üzerinde %1$s için Önemli CSS oluşturulmadı. Hata: API geçersiz bir "
"yanıt kodu döndürdü."

#. translators: %s = URL.
#: inc/Engine/CriticalPath/APIClient.php:197
msgid ""
"Critical CSS for %1$s not generated. Error: The API returned an invalid "
"response code."
msgstr ""
"%1$s için Önemli CSS oluşturulmadı. Hata: API geçersiz bir yanıt kodu "
"döndürdü."

#. translators: %1$s = error message.
#: inc/Engine/CriticalPath/APIClient.php:205
#: inc/Engine/CriticalPath/ProcessorService.php:201
msgid "Error: %1$s"
msgstr "Hata: %1$s"

#: inc/Engine/CriticalPath/CriticalCSSSubscriber.php:177
msgid "Critical CSS generation is currently running."
msgstr "Önemli CSS üretimi şu anda çalışıyor."

#. Translators: %1$s = opening link tag, %2$s = closing link tag.
#: inc/Engine/CriticalPath/CriticalCSSSubscriber.php:182
msgid "Go to the %1$sWP Rocket settings%2$s page to track progress."
msgstr "İlerlemeyi izlemek için %1$sWP Rocket ayarları%2$s sayfasına gidin."

#. Translators: %1$d = number of critical CSS generated, %2$d = total number
#. of critical CSS to generate.
#: inc/Engine/CriticalPath/CriticalCSSSubscriber.php:397
msgid ""
"Critical CSS generation is currently running: %1$d of %2$d page types "
"completed. (Refresh this page to view progress)"
msgstr ""
"Önemli CSS oluşturma şu anda çalışıyor: %1$d / %2$d sayfa türü tamamlandı. "
"(İlerlemeyi görmek için bu sayfayı yenileyin)"

#. Translators: %1$d = number of critical CSS generated, %2$d = total number
#. of critical CSS to generate.
#: inc/Engine/CriticalPath/CriticalCSSSubscriber.php:473
msgid "Critical CSS generation finished for %1$d of %2$d page types."
msgstr "Önemli CSS oluşturma, %1$d / %2$d sayfa türü için bitti."

#: inc/Engine/CriticalPath/CriticalCSSSubscriber.php:480
msgid "Critical CSS generation encountered one or more errors."
msgstr "Önemli CSS oluşturma, bir ya da daha fazla hata ile karşılaştı."

#: inc/Engine/CriticalPath/CriticalCSSSubscriber.php:480
msgid "Learn more."
msgstr "Daha fazla bilgi edinin."

#. translators: %1$ = opening bold tag, %2$ = closing bold tag.
#: inc/Engine/CriticalPath/CriticalCSSSubscriber.php:841
msgid ""
"We highly recommend the %1$supdated Remove Unused CSS%2$s for a better CSS "
"optimization. Load CSS Asynchronously is always available as a back-up."
msgstr ""
"Daha iyi bir CSS iyileştirmesi için %1$sgüncellenmiş Kullanılmayan CSS’yi "
"Kaldır’ı%2$s şiddetle tavsiye ederiz. CSS’yi Eşzamanlı Olmadan Yükle, her "
"zaman yedek olarak kullanılabilir."

#: inc/Engine/CriticalPath/CriticalCSSSubscriber.php:846
msgid "Stay with the old option"
msgstr "Eski seçenekle kal"

#. translators: %s = item URL.
#: inc/Engine/CriticalPath/DataManager.php:68
msgid ""
"Critical CSS for %1$s on mobile not generated. Error: The destination folder"
" could not be created."
msgstr ""
"Mobil üzerinde %1$s için Önemli CSS oluşturulmadı. Hata: Hedef klasör "
"oluşturulamadı."

#. translators: %s = item URL.
#: inc/Engine/CriticalPath/DataManager.php:71
msgid ""
"Critical CSS for %1$s not generated. Error: The destination folder could not"
" be created."
msgstr ""
"%1$s için Önemli CSS oluşturulmadı. Hata: Hedef klasör oluşturulamadı."

#: inc/Engine/CriticalPath/DataManager.php:106
msgid "Critical CSS file for mobile does not exist"
msgstr "Mobil için Önemli CSS dosyası yok"

#: inc/Engine/CriticalPath/DataManager.php:108
msgid "Critical CSS file does not exist"
msgstr "Önemli CSS dosyası yok"

#: inc/Engine/CriticalPath/DataManager.php:120
msgid "Critical CSS file for mobile cannot be deleted"
msgstr "Mobil için Önemli CSS dosyası silinemez"

#: inc/Engine/CriticalPath/DataManager.php:122
msgid "Critical CSS file cannot be deleted"
msgstr "Önemli CSS dosyası silinemez"

#. translators: %1$s = item URL or item type.
#: inc/Engine/CriticalPath/ProcessorService.php:187
msgid "Mobile Critical CSS for %1$s not generated."
msgstr "%1$s için Mobil Önemli CSS oluşturulmadı."

#. translators: %1$s = Item URL or item type.
#: inc/Engine/CriticalPath/ProcessorService.php:228
msgid "Critical CSS for %s in progress."
msgstr "%s için Önemli CSS devam ediyor."

#. translators: %1$s = Item URL or item type.
#: inc/Engine/CriticalPath/ProcessorService.php:262
msgid "Mobile Critical CSS for %s generated."
msgstr "%s için Mobil Önemli CSS oluşturuldu."

#. translators: %1$s = Item URL or item type.
#: inc/Engine/CriticalPath/ProcessorService.php:273
msgid "Critical CSS for %s generated."
msgstr "%s için Önemli CSS oluşturuldu."

#: inc/Engine/CriticalPath/ProcessorService.php:295
msgid "Critical CSS file deleted successfully."
msgstr "Önemli CSS dosyası başarılı olarak silindi."

#. translators: %1$s = Item URL or item type.
#: inc/Engine/CriticalPath/ProcessorService.php:317
msgid "Mobile Critical CSS for %1$s timeout. Please retry a little later."
msgstr ""
"%1$s için Mobil Önemli CSS zaman aşımına uğradı. Lütfen biraz sonra yeniden "
"deneyin."

#. translators: %1$s = Item URL or item type.
#: inc/Engine/CriticalPath/ProcessorService.php:330
msgid "Critical CSS for %1$s timeout. Please retry a little later."
msgstr ""
"%1$s için Önemli CSS zaman aşımına uğradı. Lütfen biraz sonra yeniden "
"deneyin."

#: inc/Engine/CriticalPath/RESTWP.php:141
msgid "Mobile CPCSS generation not enabled."
msgstr "Mobil ÖYCSS oluşturma etkinleştirilmedi."

#: inc/Engine/CriticalPath/RESTWPPost.php:36
#: inc/Engine/CriticalPath/RESTWPPost.php:69
msgid "Requested post does not exist."
msgstr "İstenen yazı yok."

#: inc/Engine/CriticalPath/RESTWPPost.php:46
msgid "Cannot generate CPCSS for unpublished post."
msgstr "Yayınlanmamış yazı için ÖYCSS oluşturulamıyor."

#: inc/Engine/HealthCheck/HealthCheck.php:142
msgid "Scheduled Cache Purge"
msgstr "Zamanlanmış Önbellek Temizleme"

#: inc/Engine/HealthCheck/HealthCheck.php:143
msgid "Scheduled Database Optimization"
msgstr "Zamanlanmış Veritabanı İyileştirme"

#: inc/Engine/HealthCheck/HealthCheck.php:144
msgid "Database Optimization Process"
msgstr "Veritabanı İyileştirme İşlemi"

#: inc/Engine/HealthCheck/HealthCheck.php:145
msgctxt "noun"
msgid "Preload"
msgstr "Önyükleme"

#: inc/Engine/HealthCheck/HealthCheck.php:146
msgid "Critical Path CSS Generation Process"
msgstr "Önemli Yol CSS’yi Oluşturma İşlemi"

#. translators: %1$s = <strong>, %2$s = price, %3$s = </strong>.
#: inc/Engine/License/Renewal.php:76
msgid "Renew before it is too late, you will only pay %1$s%2$s%3$s!"
msgstr "Çok geç olmadan yenileyin, sadece %1$s%2$s%3$s ödeyeceksiniz!"

#. translators: %1$s = <strong>, %2$s = discount, %3$s = </strong>,%4$s =
#. <strong>, %5$s = price, %6$s=</strong>.
#: inc/Engine/License/Renewal.php:85
msgid ""
"Renew with a %1$s%2$s discount%3$s before it is too late, you will only pay "
"%4$s%5$s%6$s!"
msgstr ""
"Çok geç olmadan %1$s%2$s indirim%3$s ile yenileyin, sadece %4$s%5$s%6$s "
"ödeyeceksiniz!"

#. translators: %1$s = <strong>, %2$s = </strong>, %3$s = price.
#: inc/Engine/License/Renewal.php:139
msgid "Renew your license for 1 year now at %1$s%3$s%2$s."
msgstr "Lisansınızı şimdi %1$s%3$s%2$s ücretle 1 yıllığına yenileyin."

#. translators: %1$s = <strong>, %2$s = </strong>, %3$s = discount percentage,
#. %4$s = price.
#: inc/Engine/License/Renewal.php:152
msgid ""
"Renew your license for 1 year now and get %1$s%3$s OFF%2$s immediately: you "
"will only pay %1$s%4$s%2$s!"
msgstr ""
"Lisansınızı 1 yıllığına hemen yenileyin ve %1$s%3$s İNDİRİM%2$s alın: sadece"
" %1$s%4$s%2$s ödeyeceksiniz!"

#. translators: %1$s = <strong>, %2$s = </strong>, %3$s = discount price.
#: inc/Engine/License/Renewal.php:218
msgid "Renew before it is too late, you will pay %1$s%3$s%2$s."
msgstr "Çok geç olmadan yenileyin, %1$s%3$s%2$s ödeyeceksiniz."

#. translators: %1$s = <strong>, %2$s = discount percentage, %3$s = </strong>,
#. %4$s = discount price.
#: inc/Engine/License/Renewal.php:227
msgid ""
"Renew with a %1$s%2$s discount%3$s before it is too late, you will only pay "
"%1$s%4$s%3$s!"
msgstr ""
"Çok geç olmadan %1$s%2$s indirim%3$s ile yenileyin, sadece %1$s%4$s%3$s "
"ödeyeceksiniz!"

#. translators: %1$s = <a>, %2$s = </a>.
#: inc/Engine/License/Renewal.php:542
msgid ""
"You need a valid license to continue using this feature. %1$sRenew now%2$s "
"before losing access."
msgstr ""
"Bu özelliği kullanmaya devam etmek için geçerli bir lisansa ihtiyacınız var."
" Erişimi kaybetmeden önce %1$sşimdi yenileyin%2$s."

#. translators: %1$s = <a>, %2$s = </a>.
#: inc/Engine/License/Renewal.php:563
msgid "You need an active license to enable this option. %1$sRenew now%2$s."
msgstr ""
"Bu seçeneği etkinleştirmek için aktif bir lisansa ihtiyacınız var. %1$sŞimdi"
" yenile%2$s."

#. translators: %1$s = <a>, %2$s = </a>.
#: inc/Engine/License/Renewal.php:591
msgid "You need an active license to enable this option. %1$sMore info%2$s."
msgstr ""
"Bu seçeneği etkinleştirmek için aktif bir lisansa ihtiyacınız var. %1$sDaha "
"fazla bilgi%2$s."

#. translators: %1$s = promotion name, %2$s = <br>, %3$s = <strong>, %4$s =
#. promotion discount percentage, %5$s = </strong>.
#: inc/Engine/License/Upgrade.php:251
msgid ""
"Take advantage of %1$s to speed up more websites:%2$s get a %3$s%4$s off%5$s"
" for %3$supgrading your license to Plus or Infinite!%5$s"
msgid_plural ""
"Take advantage of %1$s to speed up more websites:%2$s get a %3$s%4$s off%5$s"
" for %3$supgrading your license to Infinite!%5$s"
msgstr[0] ""
"Daha fazla web sitesini hızlandırmak için %1$s avantajından yararlanın:%2$s "
"%3$slisansınızı Infinite’e veya Plus’a yükseltmek%5$s için %3$s%4$s "
"indirim%5$s alın!"
msgstr[1] ""
"Daha fazla web sitesini hızlandırmak için %1$s avantajından yararlanın:%2$s "
"%3$slisansınızı Infinite’e yükseltmek%5$s için %3$s%4$s indirim%5$s alın!"

#: inc/Engine/License/Upgrade.php:382 inc/Engine/License/Upgrade.php:405
msgid "Unlimited"
msgstr "Sınırsız"

#. translators: %s = promotion discount percentage.
#: inc/Engine/License/views/promo-banner.php:16
msgid "%s off"
msgstr "%s indirim"

#. translators: %s = promotion name.
#: inc/Engine/License/views/promo-banner.php:21
msgid "%s promotion is live!"
msgstr "%s promosyonu geçerli!"

#: inc/Engine/License/views/promo-banner.php:27
msgid "Hurry Up! Deal ends in:"
msgstr "Acele Edin! Anlaşmanın bitiş zamanı:"

#: inc/Engine/License/views/promo-banner.php:31
#: inc/Engine/License/views/renewal-soon-banner.php:14
msgid "Minutes"
msgstr "Dakika"

#: inc/Engine/License/views/promo-banner.php:32
#: inc/Engine/License/views/renewal-soon-banner.php:15
msgid "Seconds"
msgstr "Saniye"

#: inc/Engine/License/views/promo-banner.php:34
#: inc/Engine/License/views/upgrade-section.php:11
msgid "Upgrade now"
msgstr "Şimdi yükselt"

#: inc/Engine/License/views/renewal-expired-banner-ocd-disabled.php:11
msgid "The Optimize CSS Delivery feature is disabled."
msgstr "CSS teslimini iyileştir özelliği etkisizleştirildi."

#: inc/Engine/License/views/renewal-expired-banner-ocd-disabled.php:15
msgid ""
"You can no longer use the Remove Unused CSS or Load CSS asynchronously "
"options."
msgstr ""
"Artık Kullanılmayan CSS’yi Kaldır veya CSS’yi eşzamansız olarak yükle "
"seçeneklerini kullanamazsınız."

#. translators: %1$s = <strong>, %2$s = </strong>.
#: inc/Engine/License/views/renewal-expired-banner-ocd-disabled.php:20
msgid ""
"You need an %1$sactive license%2$s to keep optimizing your CSS delivery, "
"which addresses a PageSpeed Insights recommendation and improves your page "
"performance."
msgstr ""
"CSS teslimini iyileştirmeye devam etmek için bir PageSpeed ​​Insights "
"önerisini ele alan ve sayfa performansınızı iyileştiren %1$saktif bir "
"lisansa%2$s ihtiyacınız var."

#: inc/Engine/License/views/renewal-expired-banner-ocd-disabled.php:29
#: inc/Engine/License/views/renewal-expired-banner-ocd.php:39
#: inc/Engine/License/views/renewal-expired-banner.php:27
#: inc/Engine/License/views/renewal-soon-banner.php:31
msgid "Renew now"
msgstr "Şimdi yenile"

#: inc/Engine/License/views/renewal-expired-banner-ocd.php:11
msgid "You will soon lose access to some features."
msgstr "Yakında bazı özelliklere erişiminizi kaybedeceksiniz."

#. translators: %1$s = <strong>, %2$s = </strong>.
#: inc/Engine/License/views/renewal-expired-banner-ocd.php:18
msgid ""
"You need an %1$sactive license to continue optimizing your CSS delivery%2$s."
msgstr ""
"%1$sCSS teslimini iyileştirmeye devam etmek için aktif bir lisansa%2$s "
"ihtiyacınız var."

#: inc/Engine/License/views/renewal-expired-banner-ocd.php:24
msgid ""
"The Remove Unused CSS and Load CSS asynchronously features are great options"
" to address the PageSpeed Insights recommendations and improve your website "
"performance."
msgstr ""
"Kullanılmayan CSS’yi Kaldır veya CSS’yi eşzamansız olarak yükle özellikleri,"
" PageSpeed ​​Insights önerilerini ele almak ve web sitenizin performansını "
"artırmak için harika seçeneklerdir."

#. translators: %1$s = <strong>, %2$s = </strong>, %3$s = date.
#: inc/Engine/License/views/renewal-expired-banner-ocd.php:29
msgid "These features will be %1$sautomatically disabled on %3$s%2$s."
msgstr ""
"Bu özellikler %1$s%3$s tarihinde otomatik olarak etkisizleştirilecektir%2$s."

#: inc/Engine/License/views/renewal-expired-banner.php:11
msgid "Your WP Rocket license is expired!"
msgstr "WP Rocket lisansınızın süresi doldu!"

#. translators: %1$s = <strong>, %2$s = </strong>.
#: inc/Engine/License/views/renewal-expired-banner.php:18
msgid ""
"Your website could be much faster if it could take advantage of our %1$snew "
"features and enhancements%2$s. 🚀"
msgstr ""
"%1$sYeni özelliklerimizden ve geliştirmelerimizden%2$s yararlanabilseydi web"
" siteniz çok daha hızlı olabilirdi.🚀"

#. translators: %1$s = <strong>, %2$s = </strong>.
#: inc/Engine/License/views/renewal-soon-banner.php:22
msgid ""
"Your %1$sWP Rocket license is about to expire%2$s: you will soon lose access"
" to product updates and support."
msgstr ""
"%1$sWP Rocket lisansınızın süresi dolmak üzere%2$s: yakında ürün "
"güncellemelerine ve desteğine erişiminizi kaybedeceksiniz."

#: inc/Engine/License/views/upgrade-popin.php:12
msgid "Speed Up More Websites"
msgstr "Daha Fazla Web Sitesini Hızlandırın"

#. translators: %1$s = opening strong tag, %2$s = closing strong tag.
#: inc/Engine/License/views/upgrade-popin.php:19
msgid ""
"You can use WP Rocket on more websites by upgrading your license. To "
"upgrade, simply pay the %1$sprice difference%2$s between your current and "
"new licenses, as shown below."
msgstr ""
"WP Rocket’ı lisansınızı yükselterek daha fazla web sitesinde "
"kullanabilirsiniz. Yükseltmek için sadece şu anki ve yeni lisanslarınız "
"arasındaki %1$sfiyat farkını%2$s, aşağıda gösterildiği gibi ödeyin."

#. translators: %1$s = opening strong tag, %2$s = closing strong tag.
#: inc/Engine/License/views/upgrade-popin.php:25
msgid ""
"%1$sN.B.%2$s: Upgrading your license does not change your expiration date"
msgstr ""
"%1$sLütfen Dikkat%2$s: Lisansınızı yükseltmek, geçerlilik sonu tarihinizi "
"değiştirmez"

#. translators: %s = price.
#: inc/Engine/License/views/upgrade-popin.php:35
msgid "Save $%s"
msgstr "$%s dolar tasarruf edin"

#. translators: %s = number of websites.
#: inc/Engine/License/views/upgrade-popin.php:48
msgid "%s websites"
msgstr "%s web sitesi"

#. translators: %s = license name.
#: inc/Engine/License/views/upgrade-popin.php:54
msgid "Upgrade to %s"
msgstr "%s olarak yükselt"

#: inc/Engine/License/views/upgrade-section.php:11
msgid ""
"You can use WP Rocket on more websites by upgrading your license (you will "
"only pay the price difference between your current and new licenses)."
msgstr ""
"WP Rocket’ı lisansınızı yükselterek daha fazla web sitesinde "
"kullanabilirsiniz (sadece şu anki ve yeni lisanslarınız arasındaki fiyat "
"farkını ödeyeceksiniz)."

#. translators: %1$s = plugin name.
#: inc/Engine/Media/AboveTheFold/Admin/Controller.php:143
msgid "%1$s: Critical images cleared!"
msgstr "%1$s: Önemli resimler temizlendi!"

#: inc/Engine/Media/AboveTheFold/AJAX/Controller.php:131
msgid "Script error"
msgstr "Komut kodu hatası"

#: inc/Engine/Media/AboveTheFold/AJAX/Controller.php:134
msgid "Script timeout"
msgstr "Komut kodu zaman aşımı"

#: inc/Engine/Media/Lazyload/AdminSubscriber.php:62
msgid "LazyLoad for images"
msgstr "Resimler için LazyLoad"

#: inc/Engine/Media/Lazyload/AdminSubscriber.php:63
msgid "LazyLoad for iframes/videos"
msgstr "Iframe’ler ve görüntüler için LazyLoad"

#: inc/Engine/Media/Lazyload/CSS/Admin/Subscriber.php:48
msgid "LazyLoad CSS backgrounds"
msgstr "LazyLoad CSS arka planları"

#: inc/Engine/Optimization/DelayJS/Admin/SiteList.php:195
msgid "Analytics & Ads"
msgstr "Çözümsel ve Reklamlar"

#: inc/Engine/Optimization/DelayJS/Admin/SiteList.php:200
msgid "Plugins"
msgstr "Eklentiler"

#: inc/Engine/Optimization/DelayJS/Admin/SiteList.php:205
msgid "Themes"
msgstr "Temalar"

#: inc/Engine/Optimization/DynamicLists/DynamicLists.php:101
msgid ""
"You need an active license to get the latest version of the lists from our "
"server."
msgstr ""
"Listelerin en son sürümünü sunucumuzdan almak için aktif bir lisansa "
"ihtiyacınız var."

#: inc/Engine/Optimization/DynamicLists/DynamicLists.php:116
msgid "Could not get updated lists from server."
msgstr "Sunucudan güncellenmiş listeler alınamadı."

#: inc/Engine/Optimization/DynamicLists/DynamicLists.php:125
msgid "Lists are up to date."
msgstr "Listeler güncel."

#: inc/Engine/Optimization/DynamicLists/DynamicLists.php:134
msgid "Could not update lists."
msgstr "Listeler güncellenemedi."

#: inc/Engine/Optimization/DynamicLists/DynamicLists.php:143
msgid "Lists are successfully updated."
msgstr "Listeler başarılı olarak güncellendi."

#: inc/Engine/Optimization/DynamicLists/ServiceProvider.php:72
msgid "Default Lists"
msgstr "Varsayılan Listeler"

#: inc/Engine/Optimization/DynamicLists/ServiceProvider.php:78
msgid "Delay JavaScript Execution Exclusion Lists"
msgstr "JavaScript’i Geciktir Yürütmesi Hariç Tutma Listeleri"

#: inc/Engine/Optimization/DynamicLists/ServiceProvider.php:84
msgid "Incompatible plugins Lists"
msgstr "Uyumsuz eklenti Listeleri"

#: inc/Engine/Optimization/Minify/AdminSubscriber.php:65
msgid "Minify/combine JavaScript"
msgstr "JavaScript’i küçült/birleştir"

#: inc/Engine/Optimization/Minify/CSS/AdminSubscriber.php:150
msgid "Minify CSS"
msgstr "CSS’yi küçült"

#. translators: %1$s = plugin name, %2$s = table name, %3$s = <a> open tag,
#. %4$s = </a> closing tag.
#: inc/Engine/Optimization/RUCSS/Admin/Settings.php:253
msgid ""
"%1$s: Could not create the %2$s table in the database which is necessary for"
" the Remove Unused CSS feature to work. Please reach out to %3$sour "
"support%4$s."
msgstr ""
"%1$s: Kullanılmayan CSS'yi Kaldır özelliğinin çalışması için gerekli olan "
"%2$s tablosu veritabanında oluşturulamadı. Lütfen %3$sdestek birimimize%4$s "
"ulaşın."

#. translators: %1$s = plugin name.
#: inc/Engine/Optimization/RUCSS/Admin/Subscriber.php:274
msgid "%1$s: Used CSS cache cleared!"
msgstr "%1$s: Kullanılan CSS önbelleği temizlendi!"

#. translators: %1$s = plugin name.
#: inc/Engine/Preload/Admin/Settings.php:57
msgid ""
"%1$s: The preload service is now active. After the initial preload it will "
"continue to cache all your pages whenever they are purged. No further action"
" is needed."
msgstr ""
"%1$s: Önyükleme hizmeti artık etkin. İlk önyüklemeden sonra, tüm "
"sayfalarınızı her temizlendiklerinde önbelleğe almaya devam edecek. Başka "
"bir işlem yapılmasına gerek yoktur."

#: inc/Engine/Preload/Cron/Subscriber.php:138
msgid "WP Rocket Preload pending jobs"
msgstr "WP Rocket Önyükleme bekleyen işler"

#: inc/Engine/Preload/Cron/Subscriber.php:166
msgid "WP Rocket Preload revert stuck failed jobs"
msgstr "WP Rocket Önyükleme, sıkışmış başarısız işleri geri alır"

#: inc/Engine/Saas/Admin/AdminBar.php:77
#: inc/Engine/Saas/Admin/AdminBar.php:198
msgid "Clear Critical Images"
msgstr "Önemli Resimleri Temizle"

#: inc/Engine/Saas/Admin/AdminBar.php:164
msgid "Clear Critical Images of this URL"
msgstr "Bu URL'nin Önemli Resimlerini Temizle"

#: inc/Engine/Saas/Admin/AdminBar.php:167
msgid "Clear Used CSS of this URL"
msgstr "Bu URL’nin Kullanılan CSS’sini Temizle"

#: inc/Engine/Saas/Admin/AdminBar.php:197
msgid "Critical Images Cache"
msgstr "Önemli Resimler Önbelleği"

#: inc/Engine/Saas/Admin/AdminBar.php:201
msgid "Remove Used CSS Cache"
msgstr "Kullanılan CSS Önbelleğini Kaldır"

#. translators: %1$s = plugin name, %2$s = number of seconds.
#: inc/Engine/Saas/Admin/Notices.php:104
msgid ""
"%1$s: Please wait %2$s seconds. The Remove Unused CSS service is processing "
"your pages, the plugin is optimizing LCP and the images above the fold."
msgstr ""
"%1$s: Lütfen %2$s saniye bekleyin. Kullanılmayan CSS’yi Kaldır hizmeti "
"sayfalarınızı işliyor, eklenti LCP’yi ve ekranın üst kısmındaki resimleri "
"iyileştiriyor."

#. translators: %1$s = plugin name, %2$s = number of URLs, %3$s = number of
#. seconds.
#: inc/Engine/Saas/Admin/Notices.php:147
msgid ""
"%1$s: The LCP element has been optimized, and the images above the fold were excluded from lazyload. The Used CSS of your homepage has been processed.\n"
"\t\t\t WP Rocket will continue to generate Used CSS for up to %2$s URLs per %3$s second(s)."
msgstr ""
"%1$s: LCP öğesi iyileştirildi ve ekranın üst kısmındaki resimler lazyload’ın dışında bırakıldı. Ana sayfanızın Kullanılan CSS’si işlendi.\n"
"\t\t\t WP Rocket, %3$s saniyede %2$s URL’ye kadar Kullanılan CSS oluşturmaya devam edecek."

#. translators: %1$s = opening link tag, %2$s = closing link tag.
#: inc/Engine/Saas/Admin/Notices.php:170
msgid "We suggest enabling %1$sPreload%2$s for the fastest results."
msgstr ""
"En hızlı sonuçlar için %1$sÖnyükleme%2$s’nin etkinleştirilmesini öneririz."

#. translators: %1$s = opening link tag, %2$s = closing link tag.
#: inc/Engine/Saas/Admin/Notices.php:180
msgid "To learn more about the process check our %1$sdocumentation%2$s."
msgstr ""
"İşlem hakkında daha fazla bilgi edinmek için %1$sbelgelerimizi%2$s gözden "
"geçirin."

#: inc/Engine/Saas/Admin/Notices.php:246
msgid ""
"We couldn't generate the used CSS because you're using a nulled version of "
"WP Rocket. You need an active license to use the Remove Unused CSS feature "
"and further improve your website's performance."
msgstr ""
"WP Rocket’in nulled bir sürümünü kullandığınız için kullanılan CSS’yi "
"oluşturamadık. Kullanılmayan CSS’yi Kaldır özelliğini kullanmak ve web "
"sitenizin performansını daha da iyileştirmek için aktif bir lisansa "
"ihtiyacınız var."

#. translators: %1$s = promo percentage.
#: inc/Engine/Saas/Admin/Notices.php:249
msgid "Click here to get a WP Rocket single license at %1$s off!"
msgstr "%1$s indirimle WP Rocket tekli lisansı almak için burayı tıklayın!"

#. translators: %1$s = <a> open tag, %2$s = </a> closing tag.
#: inc/Engine/Saas/Admin/Notices.php:302
msgid ""
"It seems a security plugin or the server's firewall prevents WP Rocket from "
"accessing the SaaS features. IPs listed %1$shere in our documentation%2$s "
"should be added to your allowlists:"
msgstr ""
"Görünüşe göre bir güvenlik eklentisi veya sunucunun güvenlik duvarı, SaaS "
"özelliklerinin WP Rocket’a erişmesini önlüyor. %1$sBurada belgelerimizde%2$s"
" listelenen IP’ler, izinli listelerinize eklenmelidir:"

#: inc/Engine/Saas/Admin/Notices.php:307
msgid "- In the security plugin, if you are using one"
msgstr "- Güvenlik eklentisinde, eğer bir eklenti kullanıyorsanız"

#: inc/Engine/Saas/Admin/Notices.php:308
msgid "- In the server's firewall. Your host can help you with this"
msgstr ""
"- Sunucunun güvenlik duvarında. Barındırıcınız bu konuda size yardımcı "
"olabilir"

#: inc/functions/admin.php:21
msgid ""
"There seems to be an issue validating your license. Please see the error "
"message below."
msgid_plural ""
"There seems to be an issue validating your license. You can see the error "
"messages below."
msgstr[0] ""
"Lisansınızı doğrularken bir sorun oldu gibi görünüyor. Lütfen aşağıdaki hata"
" iletisine bakın."
msgstr[1] ""
"Lisansınızı doğrularken bir sorun oldu gibi görünüyor. Aşağıda hata "
"iletilerini görebilirsiniz."

#: inc/functions/admin.php:361
msgid "Server type:"
msgstr "Sunucu türü:"

#: inc/functions/admin.php:370
msgid "PHP version number:"
msgstr "PHP sürüm numarası:"

#: inc/functions/admin.php:379
msgid "WordPress version number:"
msgstr "WordPress sürüm numarası:"

#: inc/functions/admin.php:388
msgid "WordPress multisite:"
msgstr "WordPress çoklu site:"

#: inc/functions/admin.php:397
msgid "Current theme:"
msgstr "Şu anki tema:"

#: inc/functions/admin.php:406
msgid "Current site language:"
msgstr "Şu anki site dili:"

#: inc/functions/admin.php:415
msgid "Active plugins:"
msgstr "Aktif eklentiler:"

#: inc/functions/admin.php:418
msgid "Plugin names of all active plugins"
msgstr "Tüm aktif eklentilerin eklenti adları"

#: inc/functions/admin.php:424
msgid "Anonymized WP Rocket settings:"
msgstr "İsimsizleştirilmiş WP Rocket ayarları:"

#: inc/functions/admin.php:427
msgid "Which WP Rocket settings are active"
msgstr "Hangi WP Rocket ayarları aktif"

#: inc/functions/admin.php:433
msgid "WP Rocket license type"
msgstr "WP Roket lisans türü"

#: inc/functions/options.php:429
msgid "The provided license data are not valid."
msgstr "Verilen lisans verileri geçerli değil."

#. Translators: %1$s = opening link tag, %2$s = closing link tag.
#: inc/functions/options.php:432
msgid "To resolve, please %1$scontact support%2$s."
msgstr "Çözmek için lütfen %1$sdestek ile iletişime geçin%2$s."

#. Translators: %1$s = opening em tag, %2$s = closing em tag, %3$s = opening
#. link tag, %4$s closing link tag.
#: inc/functions/options.php:491 inc/functions/options.php:530
msgid ""
"License validation failed. Our server could not resolve the request from "
"your website."
msgstr ""
"Lisans doğrulama başarısız oldu. Sunucumuz web sitenizden gelen isteği "
"çözemedi."

#. Translators: %1$s = opening em tag, %2$s = closing em tag, %3$s = opening
#. link tag, %4$s closing link tag.
#: inc/functions/options.php:491
msgid ""
"Try clicking %1$sValidate License%2$s below. If the error persists, follow "
"%3$sthis guide%4$s."
msgstr ""
"Aşağıda %1$sLisansı Doğrula%2$s düğmesine tıklamaya çalışın. Eğer hata devam"
" ederse, %3$sbu kılavuzu%4$s takip edin."

#. Translators: %1$s = opening link tag, %2$s = closing link tag.
#: inc/functions/options.php:507
msgid ""
"License validation failed. You may be using a nulled version of the plugin. "
"Please do the following:"
msgstr ""
"Lisans doğrulama başarısız oldu. Eklentinin nulled sürümünü kullanıyorsunuz."
" Lütfen aşağıdakini yapın:"

#. Translators: %1$s = opening link tag, %2$s = closing link tag.
#: inc/functions/options.php:507 inc/functions/options.php:549
msgid "Login to your WP Rocket %1$saccount%2$s"
msgstr "WP Rocket %1$shesabınıza%2$s oturum açın"

#. Translators: %1$s = opening link tag, %2$s = closing link tag.
#: inc/functions/options.php:507 inc/functions/options.php:549
msgid "Download the zip file"
msgstr "Zip dosyasını indir"

#. Translators: %1$s = opening link tag, %2$s = closing link tag.
#: inc/functions/options.php:507 inc/functions/options.php:549
msgid "Reinstall"
msgstr "Yeniden Yükle"

#. Translators: %1$s = opening link tag, %2$s = closing link tag.
#: inc/functions/options.php:507
msgid ""
"If you do not have a WP Rocket account, please %1$spurchase a license%2$s."
msgstr ""
"Eğer bir WP Rocket hesabınız yoksa, lütfen %1$sbir lisans satın alın%2$s."

#. Translators: %1$s = opening link tag, %2$s = closing link tag.
#: inc/functions/options.php:515
msgid ""
"License validation failed. This user account does not exist in our database."
msgstr ""
"Lisans doğrulama başarısız oldu. Bu kullanıcı veritabanımızda mevcut değil."

#. Translators: %1$s = opening link tag, %2$s = closing link tag.
#: inc/functions/options.php:515
msgid "To resolve, please contact support."
msgstr "Çözmek için lütfen destek ile iletişime geçin."

#. Translators: %1$s = opening link tag, %2$s = closing link tag.
#: inc/functions/options.php:523
msgid "License validation failed. This user account is blocked."
msgstr "Lisans doğrulama başarısız oldu. Bu kullanıcı hesabı engellendi."

#. Translators: %1$s = opening link tag, %2$s = closing link tag.
#: inc/functions/options.php:523
msgid "Please see %1$sthis guide%2$s for more info."
msgstr "Daha fazla bilgi için lütfen %1$sbu kılavuza%2$s bakın."

#. Translators: %1$s = opening em tag, %2$s = closing em tag, %3$s = opening
#. link tag, %4$s closing link tag.
#: inc/functions/options.php:530
msgid ""
"Try clicking %1$sSave Changes%2$s below. If the error persists, follow "
"%3$sthis guide%4$s."
msgstr ""
"Aşağıda %1$sDeğişiklikleri Kaydet%2$s düğmesine tıklamaya çalışın. Eğer hata"
" devam ederse, %3$sbu kılavuzu%4$s takip edin."

#. Translators: %1$s = opening link tag, %2$s = closing link tag.
#: inc/functions/options.php:543
msgid "Your license is not valid."
msgstr "Lisansınız geçerli değil."

#. Translators: %1$s = opening link tag, %2$s = closing link tag.
#: inc/functions/options.php:543
msgid "Make sure you have an active %1$sWP Rocket license%2$s."
msgstr "Aktif bir %1$sWP Rocket lisansına%2$s sahip olduğunuzdan emin olun."

#. Translators: %1$s = opening link tag, %2$s = closing link tag, %3$s =
#. opening link tag.
#: inc/functions/options.php:545
msgid "You have added as many sites as your current license allows."
msgstr "Şu anki lisansınızın izin verdiği kadar çok site eklediniz."

#. Translators: %1$s = opening link tag, %2$s = closing link tag, %3$s =
#. opening link tag.
#: inc/functions/options.php:545
msgid ""
"Upgrade your %1$saccount%2$s or %3$stransfer your license%2$s to this "
"domain."
msgstr ""
"%1$sHesabınızı%2$s yükseltin ya da %3$slisansınızı bu etki alanına "
"aktarın%2$s."

#. Translators: %1$s = opening link tag, %2$s = closing link tag.
#: inc/functions/options.php:547
msgid "This website is not allowed."
msgstr "Bu web sitesine izin verilmedi."

#. Translators: %1$s = opening link tag, %2$s = closing link tag.
#: inc/functions/options.php:547
msgid "Please %1$scontact support%2$s."
msgstr "Lütfen %1$sdestek ile iletişime geçin%2$s."

#. Translators: %1$s = opening link tag, %2$s = closing link tag.
#: inc/functions/options.php:549
msgid "This license key is not recognized."
msgstr "Bu lisans anahtarı tanınmadı."

#. Translators: %1$s = opening link tag, %2$s = closing link tag.
#: inc/functions/options.php:549
msgid "If the issue persists, please %1$scontact support%2$s."
msgstr "Eğer sorun devam ederse, lütfen %1$sdestek ile iletişime geçin%2$s."

#. Translators: %s = error message returned.
#: inc/functions/options.php:555
msgid "License validation failed: %s"
msgstr "Lisans doğrulama başarısız oldu: %s"

#: inc/Logger/Logger.php:227 inc/Logger/Logger.php:257
msgid "The log file does not exist."
msgstr "Günlük dosyası mevcut değil."

#: inc/Logger/Logger.php:233 inc/Logger/Logger.php:263
msgid "The log file could not be read."
msgstr "Günlük dosyası okunamadı."

#: inc/Logger/Logger.php:250
msgid "The logs are not saved into a file."
msgstr "Günlükler bir dosya içine kaydedilmedi."

#. Translators: %s = Hosting name.
#: inc/ThirdParty/Hostings/Cloudways.php:82
msgid ""
"Varnish auto-purge will be automatically enabled once Varnish is enabled on "
"your %s server."
msgstr ""
"%s sunucunuzda Varnish etkinleştirildikten sonra Varnish otomatik temizleme,"
" otomatik olarak etkinleştirilecektir."

#. translators: %1$s = opening link tag, %2$s = closing link tag.
#: inc/ThirdParty/Hostings/Kinsta.php:159
msgid ""
"Your installation seems to be missing core Kinsta files managing Cache "
"clearing, which will prevent your Kinsta installation and WP Rocket from "
"working correctly. Please get in touch with Kinsta support through your "
"%1$sMyKinsta%2$s account to resolve this issue."
msgstr ""
"Kurulumunuzda, Kinsta kurulumunuzu ve WP Rocket’in doğru bir şekilde "
"çalışmasını önleyecek, Önbellek temizlemeyi yöneten çekirdek Kinsta "
"dosyalarının eksik olduğu görülüyor. Lütfen bu sorunu çözmek için  "
"%1$sMyKinsta%2$s hesabınız aracılığıyla Kinsta desteği ile temasa geçin."

#. translators: %1$s = plugin name.
#: inc/ThirdParty/Plugins/CDN/Cloudflare.php:127
msgid ""
"%1$s: Cloudflare's HTTP/2 Server Push is incompatible with the features of "
"Remove Unused CSS and Combine CSS files. We strongly recommend disabling it."
msgstr ""
"%1$s: Cloudflare’in HTTP/2 Sunucu Yollaması, Kullanılmayan CSS’yi Kaldır ve "
"CSS Dosyalarını Birleştir’in özellikleriyle uyumlu değil. "
"Etkisizleştirmenizi şiddetle öneririz."

#: inc/ThirdParty/Plugins/CDN/Cloudflare.php:170
msgid ""
"Your site is using the official Cloudflare plugin. We have enabled "
"Cloudflare auto-purge for compatibility. If you have APO activated, it is "
"also compatible."
msgstr ""
"Siteniz resmi Cloudflare eklentisini kullanıyor. Uyumluluk için Cloudflare "
"otomatik temizlemeyi etkinleştirdik. Eğer APO’nuzu aktif ettiyseniz, o da "
"uyumludur."

#: inc/ThirdParty/Plugins/CDN/Cloudflare.php:171
msgid ""
"Cloudflare cache will be purged each time WP Rocket clears its cache to "
"ensure content is always up-to-date."
msgstr ""
"Cloudflare önbelleği, WP Rocket önbelleğini her temizlediğinde içeriğin her "
"zaman güncel olmasını sağlamak için temizlenecektir."

#. Translators: %1$s = strong opening tag, %2$s = strong closing tag.
#: inc/ThirdParty/Plugins/CDN/Cloudflare.php:215
msgid ""
"%1$sWP Rocket:%2$s You are using \"Dynamic Cookies Cache\". Cloudflare APO "
"is not yet compatible with that feature."
msgstr ""
"%1$sWP Rocket:%2$s \"Değişken Tanımlama Bilgileri Önbelleği\"ni "
"kullanıyorsunuz. Cloudflare APO henüz bu özellikle uyumlu değil."

#. Translators:%1$s = opening <a> tag, %2$s = closing </a> tag.
#: inc/ThirdParty/Plugins/CDN/Cloudflare.php:221
msgid ""
"You should either disable Cloudflare APO or check with the theme/plugin "
"requiring the use of “Dynamic Cookies Cache” developers for an alternative "
"way to be page-cache friendly. %1$sMore info%2$s"
msgstr ""
"Sayfa önbelleği dostu olmanın alternatif bir yolu için ya Cloudflare APO’yu "
"etkisizleştirmeli ya da “Değişken Tanımlama Bilgileri Önbelleği” "
"geliştiricilerinin kullanımını gerektiren temayı/eklentiyi "
"işaretlemelisiniz. %1$sDaha fazla bilgi%2$s"

#. Translators: %1$s = strong opening tag, %2$s = strong closing tag, %3$s =
#. opening <a> tag, %4$s = closing </a> tag, %5$s = opening <a> tag.
#: inc/ThirdParty/Plugins/CDN/Cloudflare.php:272
msgid ""
"%1$sWP Rocket:%2$s You are using \"Separate cache files for mobile "
"devices\". You need to activate \"Cache by Device Type\" %3$ssetting%5$s on "
"Cloudflare APO to serve the right version of the cache. %4$sMore info%5$s"
msgstr ""
"%1$sWP Rocket:%2$s \"Mobil cihazlar için önbellek dosyalarını ayır\"ı "
"kullanıyorsunuz. Önbelleğin doğru sürümünü sunmak için Cloudflare APO’da "
"\"Cihaz Türüne Göre Önbellekle\" %3$sayarını%5$s aktif etmeniz "
"gerek.%4$sDaha fazla bilgi%5$s"

#. Translators: %1$s = strong opening tag, %2$s = strong closing tag.
#: inc/ThirdParty/Plugins/CDN/Cloudflare.php:293
msgid ""
"%1$sWP Rocket:%2$s You have \"Cache by Device Type\" enabled on Cloudflare "
"APO. If you judge it necessary for the website to have a different cache on "
"mobile and desktop, we suggest you enable our “Separate Cache Files for "
"Mobiles Devices” to ensure the generated cache is accurate."
msgstr ""
"%1$sWP Rocket:%2$s Cloudflare APO’da \"Cihaz Türüne Göre Önbellekle\"yi "
"etkinleştirdiniz. Eğer web sitesi için mobil ve masaüstünde farklı bir "
"önbelleğe sahip olmasının gerekli olduğuna karar verirseniz, oluşturulan "
"önbelleğin doğru olduğundan emin olmak için \"Mobil Cihazlar için Önbellek "
"Dosyalarını Ayır\" seçeneğimizi etkinleştirmenizi öneririz."

#. translators: %1$s is WP Rocket plugin name, %2$s is opening <a> tag, %3$s
#. is closing </a> tag.
#: inc/ThirdParty/Plugins/ModPagespeed.php:102
msgid ""
"<strong>%1$s</strong>: Mod PageSpeed is not compatible with this plugin and "
"may cause unexpected results. %2$sMore Info%3$s"
msgstr ""
"<strong>%1$s</strong>: Mod PageSpeed bu eklenti ile uyumlu değil ve "
"beklenmeyen sonuçlara neden olabilir. %2$sDaha Fazla Bilgi%3$s"

#. Translators: %1$s is an opening <strong> tag; %2$s is a closing </strong>
#. tag
#: inc/ThirdParty/Plugins/Optimization/Autoptimize.php:75
msgid ""
"%1$sWP Rocket: %2$sWe have detected that Autoptimize's JavaScript "
"Aggregation feature is enabled. WP Rocket's Delay JavaScript Execution will "
"not be applied to the file it creates. We suggest disabling %1$sJavaScript "
"Aggregation%2$s to take full advantage of Delay JavaScript Execution."
msgstr ""
"%1$sWP Rocket: %2$sAutoptimize’ın JavaScript Toplama özelliğinin "
"etkinleştirildiğini tespit ettik. WP Rocket’in JavaScript’i Geciktir "
"Yürütmesi, oluşturduğu dosyaya uygulanmayacaktır. JavaScript’i Geciktir "
"Yürütmesi’nden tam olarak yararlanmak için %1$sJavaScript Toplama%2$s "
"özelliğinin etkisizleştirilmesini öneririz."

#. Translators: %1$s is an opening <strong> tag; %2$s is a closing </strong>
#. tag
#: inc/ThirdParty/Plugins/Optimization/Autoptimize.php:130
msgid ""
"%1$sWP Rocket: %2$sWe have detected that Autoptimize's Aggregate Inline CSS "
"feature is enabled. WP Rocket's Load CSS Asynchronously will not work "
"correctly. We suggest disabling %1$sAggregate Inline CSS%2$s to take full "
"advantage of Load CSS Asynchronously Execution."
msgstr ""
"%1$sWP Rocket: %2$sAutoptimize’ın Satır İçi CSS Topla özelliğinin "
"etkinleştirildiğini tespit ettik. WP Rocket’in CSS’yi Eşzamanlı Olmadan "
"Yükle özelliği düzgün olarak çalışmayacaktır. CSS’yi Eşzamanlı Olmadan Yükle"
" Yürütmesi’nden tam olarak yararlanmak için %1$sSatır İçi CSS Topla%2$s "
"özelliğinin etkisizleştirilmesini öneririz."

#. translators: %1$s = opening <a> tag, %2$s = closing </a> tag.
#: inc/ThirdParty/Plugins/Optimization/Ezoic.php:45
msgid ""
"This plugin blocks WP Rocket's caching and optimizations. Deactivate it and "
"use %1$sEzoic's nameserver integration%2$s instead."
msgstr ""
"Bu eklenti, WP Rocket’in önbelleğe alınmasını ve iyileştirmelerini engeller."
" Bunu devre dışı bırakın ve bunun yerine %1$sEzoic’in ad sunucusu "
"bütünleştirmesi%2$s'ni kullanın."

#. Translators: %s = Plugin name.
#: inc/ThirdParty/Plugins/Optimization/Hummingbird.php:78
msgctxt "Hummingbird notice"
msgid ""
"Please deactivate the following %s option which conflicts with WP Rocket "
"features:"
msgid_plural ""
"Please deactivate the following %s options which conflict with WP Rocket "
"features:"
msgstr[0] ""
"Lütfen WP Rocket özellikleri ile çakışan aşağıdaki %s seçeneği devre dışı "
"bırakın:"
msgstr[1] ""
"Lütfen WP Rocket özellikleri ile çakışan aşağıdaki %s seçeneği devre dışı "
"bırakın:"

#. Translators: %1$s = Plugin name, %2$s = <em>, %3$s = </em>.
#: inc/ThirdParty/Plugins/Optimization/Hummingbird.php:151
msgctxt "Hummingbird notice"
msgid ""
"%1$s %2$sdisable emoji%3$s conflicts with WP Rockets %2$sdisable emoji%3$s"
msgstr ""
"%1$s %2$semoji etkisizleştirmesi%3$s WP Rockets %2$semoji "
"etkisizleştirmesi%3$s ile çakışıyor"

#. Translators: %1$s = Plugin name, %2$s = <em>, %3$s = </em>.
#: inc/ThirdParty/Plugins/Optimization/Hummingbird.php:187
msgctxt "Hummingbird notice"
msgid ""
"%1$s %2$sGZIP compression%3$s conflicts with WP Rocket %2$sGZIP "
"compression%3$s"
msgstr ""
"%1$s %2$sGZIP sıkıştırması%3$s WP Rockets %2$sGZIP sıkıştırması%3$s ile "
"çakışıyor"

#. Translators: %1$s = Plugin name, %2$s = <em>, %3$s = </em>.
#: inc/ThirdParty/Plugins/Optimization/Hummingbird.php:223
msgctxt "Hummingbird notice"
msgid ""
"%1$s %2$sbrowser caching%3$s conflicts with WP Rocket %2$sbrowser "
"caching%3$s"
msgstr ""
"%1$s %2$starayıcı önbelleklemesi%3$s WP Rockets %2$starayıcı "
"önbelleklemesi%3$s ile çakışıyor"

#. Translators: %1$s = Plugin name, %2$s = <em>, %3$s = </em>.
#: inc/ThirdParty/Plugins/Optimization/Hummingbird.php:255
msgctxt "Hummingbird notice"
msgid ""
"%1$s %2$spage caching%3$s conflicts with WP Rocket %2$spage caching%3$s"
msgstr ""
"%1$s %2$ssayfa önbelleklemesi%3$s WP Rockets %2$ssayfa önbelleklemesi%3$s "
"ile çakışıyor"

#. Translators: %1$s = Plugin name, %2$s = <em>, %3$s = </em>.
#: inc/ThirdParty/Plugins/Optimization/Hummingbird.php:289
msgctxt "Hummingbird notice"
msgid ""
"%1$s %2$sasset optimization%3$s conflicts with WP Rocket %2$sfile "
"optimization%3$s"
msgstr ""
"%1$s %2$svarlık iyileştirmesi%3$s WP Rockets %2$sdosya iyileştirmesi%3$s ile"
" çakışıyor"

#: inc/ThirdParty/Plugins/Optimization/Perfmatters.php:38
msgid ""
"Remove Unused CSS is currently activated in Perfmatters. If you want to use "
"WP Rocket's Remove Unused CSS feature, disable this option in Perfmatters."
msgstr ""
"Kullanılmayan CSS’yi Kaldır şu anda Perfmatters’ta aktif edildi. Eğer WP "
"Rocket’in Kullanılmayan CSS’yi Kaldır özelliğini kullanmak istiyorsanız, "
"Perfmatters’ta bu seçeneği etkisizleştirin."

#: inc/ThirdParty/Plugins/Optimization/RapidLoad.php:39
msgid ""
"Automated unused CSS removal is currently activated in RapidLoad Power-Up "
"for Autoptimize. If you want to use WP Rocket's Remove Unused CSS feature, "
"disable the  RapidLoad Power-Up for Autoptimize plugin."
msgstr ""
"Otomatikleştirilmiş kullanılmayan CSS kaldırma şu anda RapidLoad Power-Up "
"for Autoptimize’da aktif edildi. Eğer WP Rocket’in Kullanılmayan CSS’yi "
"Kaldır özelliğini kullanmak istiyorsanız, RapidLoad Power-Up for Autoptimize"
" eklentisini etkisizleştirin."

#. translators: %1$s = plugin name.
#: inc/ThirdParty/Plugins/Optimization/WPMeteor.php:42
msgid ""
"Delay JS is currently activated in %1$s. If you want to use WP Rocket’s "
"delay JS, disable %1$s"
msgstr ""
"JS’yi geciktir, şu anda %1$s içinde aktif edildi. Eğer WP Rocket’in JS’yi "
"geciktir’ini kullanmak istiyorsanız, %1$s etkisizleştirin."

#: inc/ThirdParty/Plugins/Smush.php:108 inc/ThirdParty/Plugins/Smush.php:126
msgid "Smush"
msgstr "Smush"

#: inc/ThirdParty/Themes/Avada.php:107
msgid "Avada"
msgstr "Avada"

#: inc/ThirdParty/Themes/Divi.php:293
msgid ""
"Your Divi template was updated. Clear the Used CSS if the layout, design or "
"CSS styles were changed."
msgstr ""
"Divi şablonunuz güncellendi. Düzen, tasarım veya CSS stilleri "
"değiştirilmişse, Kullanılan CSS’yi temizleyin."

#: views/cpcss/activate-cpcss-mobile.php:13
msgid "Load CSS asynchronously for mobile"
msgstr "Mobil için CSS’yi eşzamanlı olmadan yükle"

#: views/cpcss/activate-cpcss-mobile.php:14
msgid ""
"Your website currently uses the same Critical Path CSS for both desktop and "
"mobile."
msgstr ""
"Web siteniz şu anda hem masaüstü hem de mobil cihazlar için aynı Önemli Yol "
"CSS’yi kullanıyor."

#: views/cpcss/activate-cpcss-mobile.php:15
msgid "Click the button to enable mobile-specific CPCSS for your site."
msgstr ""
"Siteniz için mobile özgü ÖYCSS’yi etkinleştirmek için düğmeye tıklayın."

#. translators: %1$s = opening link tag, %2$s = closing link tag.
#: views/cpcss/activate-cpcss-mobile.php:20
#: views/settings/enable-google-fonts.php:19
#: views/settings/mobile-cache.php:20
msgid ""
"This is a one-time action and this button will be removed afterwards. "
"%1$sMore info%2$s"
msgstr ""
"Bu tek seferlik bir eylemdir ve daha sonra bu düğme kaldırılacaktır. "
"%1$sDaha fazla bilgi%2$s"

#. translators: %1$s = opening link tag, %2$s = closing link tag.
#: views/cpcss/activate-cpcss-mobile.php:30
msgid ""
"Your site is now using mobile-specific critical path CSS. %1$sMore info%2$s"
msgstr ""
"Bu site artık mobile özgü önemli yol CSS’yi kullanıyor. %1$sDaha fazla "
"bilgi%2$s"

#: views/cpcss/activate-cpcss-mobile.php:39
msgid "Generate Mobile Specific CPCSS"
msgstr "Mobile Özgü ÖYCSS’yi Oluştur"

#: views/cpcss/metabox/container.php:19
msgid "Critical Path CSS"
msgstr "Önemli Yol CSS"

#. translators: %1$s = opening link tag, %2$s = closing link tag.
#: views/cpcss/metabox/generate.php:23
msgid "Generate specific Critical Path CSS for this post. %1$sMore info%2$s"
msgstr ""
"Bu yazı için belirli bir Önemli Yol CSS’yi oluşturun. %1$sDaha fazla "
"bilgi%2$s"

#. translators: %1$s = opening link tag, %2$s = closing link tag.
#: views/cpcss/metabox/generate.php:33
msgid "This post uses specific Critical Path CSS. %1$sMore info%2$s"
msgstr ""
"Bu yazı belirli bir Önemli Yol CSS’yi kullanır. %1$sDaha fazla bilgi%2$s"

#: views/cpcss/metabox/generate.php:56
msgid "Revert back to the default CPCSS"
msgstr "Varsayılan ÖYCSS’ye geri dön"

#: views/deactivation-intent/form.php:21
msgid "Facing an issue?"
msgstr "Bir sorunla mı karşılaşıyorsunuz?"

#: views/deactivation-intent/form.php:24
msgid ""
"It is not always necessary to deactivate WP Rocket when facing any issues. "
"Most of them can be fixed by deactivating only some options."
msgstr ""
"Herhangi bir sorunla karşılaştığınızda WP Rocket’i devre dışı bırakmak her "
"zaman gerekli değildir. Bunların çoğu, yalnızca bazı seçenekler devre dışı "
"bırakılarak düzeltilebilir."

#. translators: %1$s = opening strong tag, %2$s = closing strong tag.
#: views/deactivation-intent/form.php:29
msgid ""
"Our advice? Instead of deactivating WP Rocket, use our %1$sSafe Mode%2$s to "
"quickly disable LazyLoad, File Optimization, and CDN options. Then check to "
"see if your issue is resolved."
msgstr ""
"Bizim tavsiyemiz mi? WP Rocket’i devre dışı bırakmak yerine, LazyLoad, Dosya"
" İyileştirmesi ve CDN seçeneklerini hızla devre dışı bırakmak için "
"%1$sGüvenli Kip%2$s’imizi kullanın. Ardından, sorununuzun çözülüp "
"çözülmediğini denetleyin."

#: views/deactivation-intent/form.php:35
msgid "Do you want to use our Safe Mode to troubleshoot WP Rocket?"
msgstr ""
"WP Rocket sorunlarını gidermek için Güvenli Kip’imizi kullanmak istiyor "
"musunuz?"

#. translators: %1$s = opening strong tag, %2$s = closing strong tag.
#: views/deactivation-intent/form.php:55
msgid "Yes, apply \"%1$sSafe Mode%2$s\""
msgstr "Evet, \"%1$sGüvenli Kip%2$s\"i uygula"

#. translators: %1$s = opening strong tag, %2$s = closing strong tag.
#: views/deactivation-intent/form.php:68
msgid ""
"and export WP Rocket settings %1$s(Recommended as current settings will be "
"deleted)%2$s"
msgstr ""
"ve WP Rocket ayarlarını dışa aktarın %1$s(Şu anki ayarlar silineceği için "
"önerilir)%2$s"

#: views/deactivation-intent/form.php:76
msgid "No, deactivate and snooze this message for"
msgstr "Hayır, bu iletiyi devre dışı bırak ve şu süre kadar ertele"

#: views/deactivation-intent/form.php:78
msgid "1 day"
msgstr "1 gün"

#: views/deactivation-intent/form.php:79
msgid "7 days"
msgstr "7 gün"

#: views/deactivation-intent/form.php:80
msgid "30 days"
msgstr "30 gün"

#: views/deactivation-intent/form.php:81
msgid "Forever"
msgstr "Sürekli"

#: views/deactivation-intent/form.php:87
msgid "Cancel"
msgstr "İptal"

#: views/deactivation-intent/form.php:88
msgid "Confirm"
msgstr "Onayla"

#. translators: %1$s = <strong>, %2$s = plugin version, %3$s = </strong>, %4$s
#. = <a>, %5$s = </a>, %6$s = <a>.
#: views/plugins/update-renewal-expired-notice.php:27
msgid ""
" %1$sWP Rocket %2$s%3$s is available. %4$sLearn more%5$s about the updates "
"and enhancements of this major version. You need an active license to use "
"them on your website, don’t miss out! %6$sRenew Now%5$s"
msgstr ""
" %1$sWP Roket %2$s%3$s kullanılabilir. Bu ana sürümün güncellemeleri ve "
"geliştirmeleri hakkında %4$sdaha fazla bilgi edinin%5$s. Bunları web "
"sitenizde kullanmak için aktif bir lisansa ihtiyacınız var, kaçırmayın! "
"%6$sŞimdi Yenile%5$s"

#: views/settings/dynamic-lists-update.php:14
msgid "Update Inclusion and Exclusion Lists"
msgstr "Dahil Etme ve Hariç Tutma Listelerini güncelleyin"

#. translators: %1$s = opening link tag, %2$s = closing link tag.
#: views/settings/dynamic-lists-update.php:19
msgid ""
"Compatibility lists are downloaded automatically every week. Click the "
"button if you want to update them manually. %1$sMore info%2$s"
msgstr ""
"Uyumluluk listeleri her hafta otomatik olarak indirilir. Bunları el ile "
"güncellemek istiyorsanız düğmeye tıklayın. %1$sDaha fazla bilgi%2$s"

#: views/settings/dynamic-lists-update.php:29
msgid "Update lists"
msgstr "Listeleri güncelle"

#: views/settings/enable-google-fonts.php:13
msgid "Enable Google Font Optimization"
msgstr "Google Yazı Tipi İyileştirmesini etkinleştirin"

#: views/settings/enable-google-fonts.php:14
msgid ""
"Improves font performance and combines multiple font requests to reduce the "
"number of HTTP requests."
msgstr ""
"Yazı tipi performansını artırır ve HTTP isteklerinin sayısını azaltmak için "
"birden çok yazı tipi isteğini birleştirir."

#. translators: %1$s = opening link tag, %2$s = closing link tag.
#: views/settings/enable-google-fonts.php:29
msgid ""
"Google Fonts Optimization is now enabled for your site. %1$sMore info%2$s"
msgstr ""
"Google Yazı Tipleri İyileştirme artık siteniz için etkinleştirildi. %1$sDaha"
" fazla bilgi%2$s"

#: views/settings/enable-google-fonts.php:38
msgid "Optimize Google Fonts"
msgstr "Google Yazı Tiplerini iyileştir"

#: views/settings/fields/cache-lifespan.php:26
msgid "Clear cache after"
msgstr "Şu süreden sonra önbelleği temizle"

#: views/settings/fields/cnames.php:58 views/settings/fields/cnames.php:92
#: views/settings/fields/cnames.php:122
msgid "CSS & JavaScript"
msgstr "CSS ve JavaScript"

#: views/settings/fields/cnames.php:59 views/settings/fields/cnames.php:93
#: views/settings/fields/cnames.php:123
msgid "JavaScript"
msgstr "JavaScript"

#: views/settings/fields/cnames.php:60 views/settings/fields/cnames.php:94
#: views/settings/fields/cnames.php:124
msgid "CSS"
msgstr "CSS"

#: views/settings/fields/import-form.php:29
msgid "Import settings"
msgstr "Ayarları içe aktarın"

#: views/settings/fields/one-click-addon.php:43
#: views/settings/fields/rocket-addon.php:43
msgid "Add-on status"
msgstr "Eklenti durumu"

#: views/settings/fields/rocket-addon.php:72
msgid "Modify options"
msgstr "Seçenekleri değiştir"

#: views/settings/fields/rocket-cdn.php:29
#: views/settings/fields/rocket-cdn.php:41
msgid "CDN CNAME"
msgstr "CDN CNAME"

#. translators: %s is a "Learn more" link.
#: views/settings/fields/rocket-cdn.php:62
msgid "Purges RocketCDN cached resources for your website. %s"
msgstr "Web siteniz için RocketCDN önbelleklenmiş kaynaklarını temizler. %s"

#. translators: %s is a "Learn more" link.
#: views/settings/fields/rocket-cdn.php:63
#: views/settings/page-sections/cloudflare.php:36
#: views/settings/page-sections/sucuri.php:37
msgid "Learn more"
msgstr "Daha fazla bilgi edinin"

#: views/settings/fields/rocket-cdn.php:72
msgid "Clear all RocketCDN cache files"
msgstr "Tüm RocketCDN önbellek dosyalarını temizle"

#: views/settings/mobile-cache.php:11
msgid "Mobile Cache"
msgstr "Mobil Önbelleği"

#: views/settings/mobile-cache.php:14
msgid "Speed your site for mobile visitors."
msgstr "Mobil ziyaretçiler için sitenizi hızlandırın."

#: views/settings/mobile-cache.php:28
msgid "Mobile Cache is now enabled for your site."
msgstr "Mobil Önbellek artık siteniz için etkinleştirildi."

#: views/settings/mobile-cache.php:34
msgid "Enable Mobile Cache"
msgstr "Mobil Önbelleği etkinleştir"

#: views/settings/page-sections/cloudflare.php:27
msgid "Cloudflare Cache"
msgstr "Cloudflare Önbelleği"

#. translators: %s is a "Learn more" link.
#: views/settings/page-sections/cloudflare.php:35
#: views/settings/page-sections/sucuri.php:36
msgid "Purges cached resources for your website. %s"
msgstr "Web sitenizin önbelleklenmiş kaynaklarını temizler. %s"

#. translators: %s is a "Learn more" link.
#: views/settings/page-sections/cloudflare.php:36
msgid "https://support.cloudflare.com/hc/en-us/articles/200169246"
msgstr "https://support.cloudflare.com/hc/en-us/articles/200169246"

#: views/settings/page-sections/cloudflare.php:45
msgid "Clear all Cloudflare cache files"
msgstr "Tüm Cloudflare önbellek dosyalarını temizle"

#: views/settings/page-sections/dashboard.php:38
msgid "Congratulations!"
msgstr "Tebrikler!"

#: views/settings/page-sections/dashboard.php:40
msgid "WP Rocket is now activated and already working for you."
msgstr "WP Rocket şu an aktif edildi ve halen sizin için çalışıyor."

#: views/settings/page-sections/dashboard.php:42
msgid "Your website should be loading faster now!"
msgstr "Web siteniz artık daha hızlı yüklenmelidir!"

#: views/settings/page-sections/dashboard.php:44
msgid ""
"To guarantee fast websites, WP Rocket automatically applies 80% of web "
"performance best practices."
msgstr ""
"Hızlı web sitelerini garantilemek için en iyi WP Rocket web performans "
"uygulamalarının %80’ini otomatik olarak uygular."

#: views/settings/page-sections/dashboard.php:44
msgid ""
"We also enable options that provide immediate benefits to your website."
msgstr ""
"Ayrıca web sitenize anında fayda sağlayan seçenekleri etkinleştiriyoruz."

#: views/settings/page-sections/dashboard.php:45
msgid "Continue to the options to further optimize your site!"
msgstr "Sitenizi daha da iyileştirmek için seçeneklere devam edin!"

#: views/settings/page-sections/dashboard.php:62
msgid "My Account"
msgstr "Hesabım"

#: views/settings/page-sections/dashboard.php:68
msgid "Refresh info"
msgstr "Bilgiyi yenile"

#: views/settings/page-sections/dashboard.php:86
msgid "with"
msgstr "birlikte"

#: views/settings/page-sections/dashboard.php:100
msgid "Expiration Date"
msgstr "Bitiş Tarihi"

#: views/settings/page-sections/dashboard.php:110
msgid "View my account"
msgstr "Hesabımı göster"

#: views/settings/page-sections/dashboard.php:137
msgid "Quick Actions"
msgstr "Hızlı Eylemler"

#: views/settings/page-sections/dashboard.php:144
msgid "Remove all cached files"
msgstr "Tüm önbelleklenen dosyaları kaldırın"

#: views/settings/page-sections/dashboard.php:164
#: views/settings/page-sections/dashboard.php:170
msgid "Regenerate Critical CSS"
msgstr "Önemli CSS’yi yeniden oluştur"

#: views/settings/page-sections/dashboard.php:196
msgid "Frequently Asked Questions"
msgstr "Sıkça Sorulan Sorular"

#: views/settings/page-sections/dashboard.php:210
msgid "Still cannot find a solution?"
msgstr "Hala bir çözüm bulamadınız mı?"

#: views/settings/page-sections/dashboard.php:211
msgid ""
"Submit a ticket and get help from our friendly and knowledgeable Rocketeers."
msgstr ""
"Bir çağrı gönderin ve arkadaş yanlısı ve bilgili Roketçilerimizden yardım "
"alın."

#: views/settings/page-sections/dashboard.php:219
msgid "Ask support"
msgstr "Destek iste"

#: views/settings/page-sections/database.php:25
msgid "Backup your database before you run a cleanup!"
msgstr "Temizlemeyi çalıştırmadan önce veritabanınızı yedekleyin!"

#: views/settings/page-sections/database.php:26
msgid ""
"Once a database optimization has been performed, there is no way to undo it."
msgstr ""
"Bir kere bir veritabanı iyileştirme yapıldı mı, bunu geri almanın hiç yolu "
"yoktur."

#: views/settings/page-sections/database.php:28
msgid "Save Changes and Optimize"
msgstr "Değişiklikleri Kaydet ve İyileştir"

#. Translators: %1$s = <strong>, %2$s = </strong>, %3$s = <span
#. class="imagify-name">, %4$s = </span>.
#: views/settings/page-sections/imagify.php:21
msgid ""
"%1$sWP ROCKET%2$s created %3$sIMAGIFY%4$s %1$sfor best-in-class image "
"optimization.%2$s"
msgstr ""
"%1$sWP ROCKET%2$s, resim iyileştirme için sınıfının en iyisi %3$sIMAGIFY%4$s"
" %1$seklentisini yarattı.%2$s"

#: views/settings/page-sections/imagify.php:24
msgid ""
"Compress image to make your website faster, all while maintaining image "
"quality."
msgstr ""
"Tüm resim kalitesini korurken, web sitenizin daha hızlı hale getirmek için "
"resmi sıkıştırın."

#: views/settings/page-sections/imagify.php:25
msgid "More on Imagify:"
msgstr "Imagify’da daha fazlası:"

#: views/settings/page-sections/imagify.php:27
msgid "Imagify Plugin Page"
msgstr "Imagify Eklenti Sayfası"

#: views/settings/page-sections/imagify.php:28
msgid "Imagify Website"
msgstr "Imagify Web Sitesi"

#: views/settings/page-sections/imagify.php:29
msgid "Review of Image Compression Plugins"
msgstr "Resim Sıkıştırma Eklentileri İncelemesi"

#: views/settings/page-sections/imagify.php:38
msgid "Install Imagify"
msgstr "Imagify’ı Yükle"

#: views/settings/page-sections/license.php:22
msgid "WP Rocket was not able to automatically validate your license."
msgstr "WP Rocket, lisansınızı otomatik olarak doğrulayamadı."

#. translators: %1$s = tutorial URL, %2$s = support URL.
#: views/settings/page-sections/license.php:29
msgid "Follow this %1$s, or contact %2$s to get the engine started."
msgstr ""
"Motoru çalıştırmak için bu %1$s takip edin ya da %2$s ile iletişime geçin."

#. translators: %1$s = <a href=", %2$s =  tutorial href,  %3$s =  "
#. target="_blank">,  %4$s =  </a>.
#: views/settings/page-sections/license.php:32
msgid "%1$s%2$s%3$stutorial%4$s"
msgstr "%1$s%2$s%3$seğitimi%4$s"

#: views/settings/page-sections/license.php:34
msgid ""
"https://docs.wp-rocket.me/article/100-resolving-problems-with-license-"
"validation/?utm_source=wp_plugin&utm_medium=wp_rocket"
msgstr ""
"https://docs.wp-rocket.me/article/100-resolving-problems-with-license-"
"validation/?utm_source=wp_plugin&utm_medium=wp_rocket"

#. translators: %1$s = <a href=", %2$s =  support href,  %3$s =  "
#. target="_blank">,  %4$s =  </a>.
#: views/settings/page-sections/license.php:40
msgid "%1$s%2$s%3$ssupport%4$s"
msgstr "%1$s%2$s%3$sdestek%4$s"

#: views/settings/page-sections/sucuri.php:46
msgid "Clear all Sucuri cache files"
msgstr "Tüm Sucuri önbellek dosyalarını temizle"

#. translators: %1$s = formatted file size, %2$s = formatted number of entries
#. (don't use %2$d).
#: views/settings/page-sections/tools.php:20
msgid "Files size: %1$s. Number of entries: %2$s."
msgstr "Dosyaların boyutu: %1$s. Giriş sayısı: %2$s."

#. translators: %1$s = opening <a> tag, %2$s = closing </a> tag.
#: views/settings/page-sections/tools.php:23
msgid "%1$sDownload the file%2$s."
msgstr "%1$sDosyayı indir%2$s."

#. translators: %1$s = opening <a> tag, %2$s = closing </a> tag.
#: views/settings/page-sections/tools.php:26
msgid "%1$sDelete the file%2$s."
msgstr "%1$sDosyayı sil%2$s."

#: views/settings/page-sections/tools.php:37
msgid "Export settings"
msgstr "Ayarları dışa aktarın"

#: views/settings/page-sections/tools.php:38
msgid "Download a backup file of your settings"
msgstr "Ayarlarınızın bir yedekleme dosyasını indirin"

#: views/settings/page-sections/tools.php:46
msgid "Download settings"
msgstr "Ayarları indir"

#: views/settings/page-sections/tools.php:60
msgid "Rollback"
msgstr "Geri alın"

#. translators: %s = WP Rocket version number.
#: views/settings/page-sections/tools.php:64
msgid "Has version %s caused an issue on your website?"
msgstr "%s sürümü web sitenizde bir soruna mı neden oldu?"

#. translators: %s = <br>.
#: views/settings/page-sections/tools.php:69
msgid ""
"You can rollback to the previous major version here.%sThen send us a support"
" request."
msgstr ""
"Burada önceki büyük sürüme geri alabilirsiniz.%sArdından bize bir destek "
"isteği gönderin."

#. translators: %s = WP Rocket previous version.
#: views/settings/page-sections/tools.php:80
msgid "Reinstall version %s"
msgstr "%s sürümünü yeniden yükle"

#: views/settings/page-sections/tools.php:106
msgid "Debug mode"
msgstr "Hata ayıklama kipi"

#: views/settings/page-sections/tools.php:111
msgid "Create a debug log file."
msgstr "Hata ayıklama günlük dosyası oluşturun."

#: views/settings/page-sections/tutorials.php:13
#: views/settings/partials/getting-started.php:18
msgid "Getting Started"
msgstr "Başlarken"

#: views/settings/page-sections/tutorials.php:15
msgid "Getting Started with WP Rocket"
msgstr "WP Rocket ile Başlarken"

#: views/settings/page-sections/tutorials.php:16
#: views/settings/partials/getting-started.php:12
msgid "Finding the Best Settings for Your Site"
msgstr "Siteniz için En İyi Ayarları Bulma"

#: views/settings/page-sections/tutorials.php:17
#: views/settings/partials/getting-started.php:13
msgid "How to Check if WP Rocket is Caching Your Site"
msgstr "WP Rocket’in Sitenizi Önbelleklediği Nasıl Denetlenir"

#: views/settings/page-sections/tutorials.php:18
#: views/settings/partials/getting-started.php:14
msgid "How to Measure the Speed of Your Site"
msgstr "Sitenizin Hızı Nasıl Ölçülür"

#: views/settings/page-sections/tutorials.php:19
msgid "How Preloading Works"
msgstr "Önyükleme Nasıl Çalışır"

#: views/settings/page-sections/tutorials.php:23
msgid "Passing the Core Web vitals"
msgstr "Core Web vitals bilgilerini geçme"

#: views/settings/page-sections/tutorials.php:25
msgid "How to improve LCP with WP Rocket"
msgstr "WP Rocket ile LCP nasıl geliştirilir"

#: views/settings/page-sections/tutorials.php:26
msgid "How to improve FID with WP Rocket"
msgstr "WP Rocket ile FID nasıl geliştirilir"

#: views/settings/page-sections/tutorials.php:27
msgid "How to improve CLS with WP Rocket"
msgstr "WP Rocket ile CLS nasıl geliştirilir"

#: views/settings/page-sections/tutorials.php:31
msgid "Troubleshooting"
msgstr "Sorun Giderme"

#: views/settings/page-sections/tutorials.php:33
msgid "Troubleshooting Display Issues with File Optimization"
msgstr "Dosya İyileştirme ile Görüntü Sorunlarının Sorununu Giderme"

#: views/settings/page-sections/tutorials.php:34
msgid "How to Find the Right JavaScript to Exclude"
msgstr "Hariç Tutmak için Doğru JavaScript Nasıl Bulunur"

#: views/settings/page-sections/tutorials.php:35
msgid "How External Content Slows Your Site"
msgstr "Dış İçerik Sitenizi Nasıl Yavaşlatır"

#: views/settings/page-sections/tutorials.php:41
msgid "Set Up the Cloudflare Add-on"
msgstr "Cloudflare Eklentisini Ayarlama"

#: views/settings/page.php:16
msgid "WP Rocket Settings"
msgstr "WP Rocket Ayarları"

#. translators: %s = Plugin version number.
#: views/settings/page.php:30
msgid "version %s"
msgstr "sürüm %s"

#: views/settings/page.php:60
msgid "Show Sidebar"
msgstr "Kenar Çubuğunu Göster"

#. translators: %1$s = <strong>, %2$s = </strong>.
#: views/settings/page.php:82
msgid ""
"Below is a detailed view of all data WP Rocket will collect %1$sif granted "
"permission.%2$s"
msgstr ""
"Aşağıdakiler, %1$sizin verildiği takdirde%2$s WP Rocket’in toplayacağı tüm "
"verilerin ayrıntılı bir görünümüdür."

#: views/settings/page.php:88
msgid ""
"WP Rocket will never transmit any domain names or email addresses (except "
"for license validation), IP addresses, or third-party API keys."
msgstr ""
"WP Rocket asla herhangi bir etki alanı adı veya e-posta adresini (lisans "
"doğrulaması hariç), IP adreslerini, ya da üçüncü taraf API anahtarlarını "
"iletmeyecek."

#: views/settings/page.php:90
msgid "Activate Rocket analytics"
msgstr "Rocket çözümseli aktif et"

#: views/settings/partials/documentation.php:15
msgid "It is a great starting point to fix some of the most common issues."
msgstr ""
"En yaygın sorunların bazılarını düzeltmek için harika bir başlangıç "
"noktasıdır."

#: views/settings/partials/documentation.php:22
msgid "Read the documentation"
msgstr "Belgeleri okuyun"

#: views/settings/partials/getting-started.php:11
msgid "What WP Rocket Does For You By Default"
msgstr "WP Roket Varsayılan Olarak Sizin İçin Ne Yapar"

#: views/settings/partials/sidebar.php:12
msgid "How to correctly measure your website’s loading time"
msgstr "Web sitenizin yükleme süresini nasıl doğru olarak ölçersiniz"

#: views/settings/partials/sidebar.php:14
msgid "Check our tutorial and learn how to measure the speed of your site."
msgstr ""
"Eğitimimizi gözden geçirin ve sitenizin hızının nasıl ölçüldüğünü öğrenin."

#: views/settings/partials/sidebar.php:15
#: views/settings/partials/sidebar.php:24
msgid "Read our guide"
msgstr "Rehberimizi okuyun"

#: views/settings/partials/sidebar.php:18
msgid "Learn about optimal WP Rocket settings for mobile."
msgstr ""
"Mobil için en uygun WP Roket ayarları hakkında daha fazla bilgi edinin."

#: views/settings/partials/sidebar.php:27
msgid "Test and Improve Google Core Web Vitals for WordPress."
msgstr "WordPress için Google Core Web Vitals’ı Deneyin ve Geliştirin."

#: views/settings/partials/sidebar.php:28
msgid "Read more"
msgstr "Daha fazla bilgi edinin"

#: views/settings/partials/sidebar.php:33
msgid "You have not activated logged-in user cache."
msgstr "Oturum açmış kullanıcı önbelleğini aktif etmediniz."

#: views/settings/partials/sidebar.php:34
msgid ""
"Use a private browser to check your website's speed and visual appearance."
msgstr ""
"Web sitenizin hızını ve görsel görünümünü kontrol etmek için özel bir "
"tarayıcı kullanın."

#: views/settings/sections/addons-container.php:24
#: views/settings/sections/fields-container.php:28
msgid "Need Help?"
msgstr "Yardım Mı Gerekli?"
