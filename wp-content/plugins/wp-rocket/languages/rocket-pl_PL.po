msgid ""
msgstr ""
"Project-Id-Version: WP Rocket 3.12\n"
"Report-Msgid-Bugs-To: http://wp-rocket.me/\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2023\n"
"Language-Team: Polish (Poland) (https://www.transifex.com/wp-media/teams/18133/pl_PL/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"POT-Creation-Date: 2023-01-18 09:48-0500\n"
"PO-Revision-Date: 2019-08-26 15:14+0000\n"
"Language: pl_PL\n"
"Plural-Forms: nplurals=4; plural=(n==1 ? 0 : (n%10>=2 && n%10<=4) && (n%100<12 || n%100>14) ? 1 : n!=1 && (n%10>=0 && n%10<=1) || (n%10>=5 && n%10<=9) || (n%100>=12 && n%100<=14) ? 2 : 3);\n"
"X-Generator: Poedit 1.8.11\n"
"X-Poedit-Basepath: ..\n"
"X-Poedit-KeywordsList: __;_e;_x:1,2c;_ex:1,2c;_n:1,2;_nx:1,2,4c;_n_noop:1,2;_nx_noop:1,2,3c;esc_attr__;esc_html__;esc_attr_e;esc_html_e;esc_attr_x:1,2c;esc_html_x:1,2c\n"
"X-Poedit-SearchPath-0: .\n"
"X-Poedit-SearchPathExcluded-0: assets\n"
"X-Poedit-SearchPathExcluded-1: src\n"
"X-Poedit-SearchPathExcluded-2: vendor\n"
"X-Poedit-SearchPathExcluded-3: node_modules\n"
"X-Poedit-SearchPathExcluded-4: tests\n"
"X-Poedit-SearchPathExcluded-5: inc/Dependencies\n"
"X-Poedit-SourceCharset: UTF-8\n"
"X-Textdomain-Support: yes\n"

#: inc/3rd-party/hosting/flywheel.php:18
#: inc/3rd-party/hosting/pressidium.php:18
#: inc/3rd-party/hosting/wp-serveur.php:27
#: inc/ThirdParty/Hostings/Cloudways.php:90
#: inc/ThirdParty/Hostings/Dreampress.php:44
#: inc/ThirdParty/Hostings/Godaddy.php:63
#: inc/ThirdParty/Hostings/O2Switch.php:49
#: inc/ThirdParty/Hostings/OneCom.php:137
#: inc/ThirdParty/Hostings/ProIsp.php:51
#: inc/ThirdParty/Hostings/Savvii.php:50
#: inc/ThirdParty/Hostings/WPEngine.php:47
#: inc/ThirdParty/Hostings/WPXCloud.php:51
#: inc/deprecated/3.6.php:697
#: inc/deprecated/3.6.php:997
#: inc/deprecated/3.9.php:22
#, php-format
msgid "Your site is hosted on %s, we have enabled Varnish auto-purge for compatibility."
msgstr "Twoja strona jest hostowana w %s, włączyliśmy funkcję Varnish auto-purge w celu zapewnienia kompatybilności."

#: inc/Addon/Cloudflare/API/Client.php:129
msgid "Cloudflare did not provide any reply. Please try again later."
msgstr "Cloudflare nie udzielił żadnej odpowiedzi. Proszę spróbować ponownie później."

#: inc/Addon/Cloudflare/API/Client.php:186
#: inc/deprecated/3.5.php:112
#: inc/deprecated/3.5.php:169
msgid "Incorrect Cloudflare email address or API key."
msgstr "Nieprawidłowy adres e-mail lub klucz API Cloudflare."

#: inc/Addon/Cloudflare/API/Client.php:190
#: inc/Addon/Cloudflare/API/Client.php:204
#: inc/Addon/Cloudflare/Cloudflare.php:74
#: inc/Addon/Cloudflare/Cloudflare.php:109
#: inc/classes/subscriber/Tools/class-detect-missing-tags-subscriber.php:157
#: inc/deprecated/3.5.php:92
#: inc/deprecated/3.5.php:116
#: inc/deprecated/3.5.php:129
#: inc/deprecated/3.5.php:157
#: inc/deprecated/3.5.php:173
#, php-format
msgid "Read the %1$sdocumentation%2$s for further guidance."
msgstr "Przeczytaj %1$sdokumentację%2$s w celu uzyskania dalszych wskazówek."

#: inc/Addon/Cloudflare/API/Client.php:192
#: inc/Addon/Cloudflare/API/Client.php:206
#: inc/Addon/Cloudflare/Auth/APIKey.php:63
#: inc/Addon/Cloudflare/Cloudflare.php:76
#: inc/Addon/Cloudflare/Cloudflare.php:111
#: inc/deprecated/3.5.php:81
#: inc/deprecated/3.5.php:94
#: inc/deprecated/3.5.php:118
#: inc/deprecated/3.5.php:131
#: inc/deprecated/3.5.php:159
#: inc/deprecated/3.5.php:175
#: inc/deprecated/3.5.php:208
msgid "https://docs.wp-rocket.me/article/18-using-wp-rocket-with-cloudflare/?utm_source=wp_plugin&utm_medium=wp_rocket#add-on"
msgstr "https://docs.wp-rocket.me/article/18-using-wp-rocket-with-cloudflare/?utm_source=wp_plugin&utm_medium=wp_rocket#add-on"

#: inc/Addon/Cloudflare/API/Client.php:200
#: inc/deprecated/3.5.php:125
msgid "Incorrect Cloudflare Zone ID."
msgstr "Nieprawidłowy ID strefy Cloudflare."

#: inc/Addon/Cloudflare/Auth/APIKey.php:61
#, php-format
msgid "Cloudflare email and/or API key are not set. Read the %1$sdocumentation%2$s for further guidance."
msgstr "E-mail i klucz API Cloudflare nie są ustawione. Przeczytaj %1$sdokumentację%2$s w celu uzyskania dalszych wskazówek."

#: inc/Addon/Cloudflare/Cloudflare.php:70
#: inc/deprecated/3.5.php:88
msgid "Missing Cloudflare Zone ID."
msgstr "Brak ID strefy Cloudflare."

#: inc/Addon/Cloudflare/Cloudflare.php:105
#: inc/deprecated/3.5.php:153
msgid "It looks like your domain is not set up on Cloudflare."
msgstr "Wygląda na to, że twoja domena nie jest ustawiona na Cloudflare."

#: inc/deprecated/3.5.php:587
#, php-format
msgid "<strong>WP Rocket:</strong> %s"
msgstr "<strong>WP Rocket:</strong> %s"

#: inc/deprecated/3.5.php:592
msgid "<strong>WP Rocket:</strong> Cloudflare cache successfully purged."
msgstr "<strong>WP Rocket:</strong> Pamięć podręczna w Cloudflare została skutecznie oczyszczona."

#: inc/Addon/Cloudflare/Subscriber.php:632
#: inc/admin/options.php:184
#: inc/classes/subscriber/Tools/class-detect-missing-tags-subscriber.php:148
msgid "WP Rocket: "
msgstr "WP Rocket: "

#: inc/Addon/Cloudflare/Subscriber.php:297
#, php-format
msgid "Cloudflare development mode error: %s"
msgstr "Błąd trybu deweloperskiego CloudFlare: %s"

#: inc/Addon/Cloudflare/Subscriber.php:304
#, php-format
msgid "Cloudflare development mode %s"
msgstr "Tryb deweloperski CloudFlare %s"

#: inc/Addon/Cloudflare/Subscriber.php:321
#, php-format
msgid "Cloudflare cache level error: %s"
msgstr "Błąd poziomu pamięci podręcznej CloudFlare: %s"

#: inc/Addon/Cloudflare/Subscriber.php:334
#, php-format
msgid "Cloudflare cache level set to %s"
msgstr "Poziom pamięci podręcznej CloudFlare ustawiono na %s"

#: inc/Addon/Cloudflare/Subscriber.php:350
#, php-format
msgid "Cloudflare minification error: %s"
msgstr "Błąd minifikacji CloudFlare: %s"

#: inc/Addon/Cloudflare/Subscriber.php:357
#, php-format
msgid "Cloudflare minification %s"
msgstr "Minifikacja CloudFlare %s"

#: inc/Addon/Cloudflare/Subscriber.php:373
#, php-format
msgid "Cloudflare rocket loader error: %s"
msgstr "Błąd ładowania rocket loadera CloudFlare: %s"

#: inc/Addon/Cloudflare/Subscriber.php:380
#, php-format
msgid "Cloudflare rocket loader %s"
msgstr "Rocket loader CloudFlare %s"

#: inc/Addon/Cloudflare/Subscriber.php:396
#, php-format
msgid "Cloudflare browser cache error: %s"
msgstr "Błąd pamięci podręcznej przeglądarki CloudFlare: %s"

#: inc/Addon/Sucuri/Subscriber.php:95
#, php-format
msgid "Sucuri cache purge error: %s"
msgstr "Błąd czyszczenia pamięci podręcznej Sucuri: %s"

#: inc/Addon/Sucuri/Subscriber.php:100
msgid "The Sucuri cache is being cleared. Note that it may take up to two minutes for it to be fully flushed."
msgstr "Sucuri cache jest czyszczony. Należy pamiętać, że pełne czyszczenie może zająć do dwóch minut."

#: inc/Addon/Sucuri/Subscriber.php:217
msgid "Sucuri firewall API key was not found."
msgstr "Nie znaleziono klucza API zapory sieciowej Sucuri."

#: inc/Addon/Sucuri/Subscriber.php:230
msgid "Sucuri firewall API key is invalid."
msgstr "Klucz API zapory sieciowej Sucuri jest nieprawidłowy."

#: inc/Addon/Sucuri/Subscriber.php:285
#, php-format
msgid "Error when contacting Sucuri firewall API. Error message was: %s"
msgstr "Błąd podczas kontaktu z interfejsem API zapory sieciowej Sucuri. Komunikat o błędzie to: %s"

#: inc/Addon/Sucuri/Subscriber.php:300
msgid "Could not get a response from the Sucuri firewall API."
msgstr "Nie udało się uzyskać odpowiedzi z API zapory sieciowej Sucuri."

#: inc/Addon/Sucuri/Subscriber.php:315
msgid "Got an invalid response from the Sucuri firewall API."
msgstr "Otrzymał nieprawidłową odpowiedź od API zapory sieciowej Sucuri."

#: inc/Addon/Sucuri/Subscriber.php:329
msgid "The Sucuri firewall API returned an unknown error."
msgstr "API zapory sieciowej Sucuri zwróciło nieznany błąd."

#: inc/Addon/Sucuri/Subscriber.php:333
#, php-format
msgid "The Sucuri firewall API returned the following error: %s"
msgid_plural "The Sucuri firewall API returned the following errors: %s"
msgstr[0] "API zapory Sucuri zwróciło następujący błąd: %s"
msgstr[1] "API zapory Sucuri zwróciło następujące błędy: %s"
msgstr[2] "API zapory Sucuri zwróciło następujące błędy: %s"
msgstr[3] "API zapory Sucuri zwróciło następujące błędy: %s"

#: inc/Engine/Admin/Database/Optimization.php:30
#: inc/Engine/Admin/Settings/Page.php:1475
msgid "Revisions"
msgstr "Wersje"

#: inc/Engine/Admin/Database/Optimization.php:31
#: inc/Engine/Admin/Settings/Page.php:1485
msgid "Auto Drafts"
msgstr "Automatyczne szkice"

#: inc/Engine/Admin/Database/Optimization.php:32
#: inc/Engine/Admin/Settings/Page.php:1495
msgid "Trashed Posts"
msgstr "Usunięte wpisy"

#: inc/Engine/Admin/Database/Optimization.php:33
#: inc/Engine/Admin/Settings/Page.php:1505
msgid "Spam Comments"
msgstr "Spamowe komentarze"

#: inc/Engine/Admin/Database/Optimization.php:34
#: inc/Engine/Admin/Settings/Page.php:1515
msgid "Trashed Comments"
msgstr "Usunięte komentarze"

#: inc/Engine/Admin/Database/Optimization.php:35
msgid "Transients"
msgstr "Tymczasowe wpisy"

#: inc/Engine/Admin/Database/Optimization.php:36
msgid "Tables"
msgstr "Tabele"

#: inc/Engine/Admin/Database/Subscriber.php:79
#: inc/deprecated/Engine/Addon/FacebookTracking/Subscriber.php:92
#: inc/deprecated/Engine/Addon/GoogleTracking/Subscriber.php:137
msgid "weekly"
msgstr "tygodniowo"

#: inc/Engine/Admin/Database/Subscriber.php:85
msgid "monthly"
msgstr "miesięcznie"

#: inc/Engine/Admin/Database/Subscriber.php:194
msgid "Database optimization process is running"
msgstr "Trwa proces optymalizacji bazy danych"

#: inc/Engine/Admin/Database/Subscriber.php:224
msgid "Database optimization process is complete. Everything was already optimized!"
msgstr "Proces optymalizacji bazy danych został zakończony. Wszystko było już zoptymalizowane!"

#: inc/Engine/Admin/Database/Subscriber.php:227
msgid "Database optimization process is complete. List of optimized items below:"
msgstr "Proces optymalizacji bazy danych został zakończony. Lista zoptymalizowanych elementów poniżej:"

#: inc/Engine/Admin/Database/Subscriber.php:235
#, php-format
msgid "%1$d %2$s optimized."
msgstr "%1$d %2$s zoptymalizowany."

#: inc/Engine/Admin/Settings/Page.php:201
msgid "Save Changes"
msgstr "Zapisz zmiany"

#: inc/Engine/Admin/Settings/Page.php:201
msgid "Validate License"
msgstr "Weryfikuj licencję"

#: inc/Engine/Admin/Settings/Page.php:257
#: inc/Engine/Admin/Settings/Page.php:258
msgid "Unavailable"
msgstr "Niedostępne"

#: inc/Engine/Admin/Settings/Page.php:352
#: inc/deprecated/deprecated.php:1789
#: views/settings/page-sections/dashboard.php:80
msgid "License"
msgstr "Licencja"

#: inc/Engine/Admin/Settings/Page.php:369
msgid "API key"
msgstr "Klucz API"

#: inc/Engine/Admin/Settings/Page.php:384
msgid "Email address"
msgstr "Adres e-mail"

#: inc/Engine/Admin/Settings/Page.php:410
msgid "Dashboard"
msgstr "Kokpit"

#: inc/Engine/Admin/Settings/Page.php:411
msgid "Get help, account info"
msgstr "Uzyskaj pomoc, informacje o koncie"

#: inc/Engine/Admin/Settings/Page.php:420
msgid "My Status"
msgstr "Mój status"

#: inc/Engine/Admin/Settings/Page.php:430
#: views/settings/page.php:75
msgid "Rocket Analytics"
msgstr "Rocket Analytics"

#: inc/Engine/Admin/Settings/Page.php:432
#, php-format
msgid "I agree to share anonymous data with the development team to help improve WP Rocket. %1$sWhat info will we collect?%2$s"
msgstr "Wyrażam zgodę na udostępnienie anonimowych danych zespołowi rozwojowemu w celu ulepszenia WP Rocket. %1$sJakie informacje będziemy gromadzić%2$s"

#: inc/Engine/Admin/Settings/Page.php:456
#: inc/Engine/Cache/WPCache.php:354
msgid "Cache"
msgstr "Pamięć podręczna"

#: inc/Engine/Admin/Settings/Page.php:457
msgid "Basic cache options"
msgstr "Podstawowe opcje pamięci podręcznej"

#: inc/Engine/Admin/Settings/Page.php:464
msgid "Mobile Cache"
msgstr "Pamięć podręczna dla urządzeń przenośnych"

#: inc/Engine/Admin/Settings/Page.php:466
msgid "Speed up your site for mobile visitors."
msgstr "Przyspiesz swoją stronę dla odwiedzających z urządzeń mobilnych."

#: inc/Engine/Admin/Settings/Page.php:471
msgid "We detected you use a plugin that requires a separate cache for mobile, and automatically enabled this option for compatibility."
msgstr "Wykryliśmy, że używasz wtyczki, która wymaga oddzielnej pamięci podręcznej dla urządzeń przenośnych i automatycznie włączyliśmy tę opcję w celu zapewnienia zgodności."

#: inc/Engine/Admin/Settings/Page.php:475
msgid "User Cache"
msgstr "Pamięć podręczna użytkownika"

#: inc/Engine/Admin/Settings/Page.php:478
#, php-format
msgid "%1$sUser cache%2$s is great when you have user-specific or restricted content on your website."
msgstr "%1$sPamięć podręczna użytkowników%2$s jest świetna, gdy masz specyficzne dla użytkownika lub ograniczone treści na swojej stronie internetowej."

#: inc/Engine/Admin/Settings/Page.php:486
msgid "Cache Lifespan"
msgstr "Cykl trwania pamięci podręcznej"

#: inc/Engine/Admin/Settings/Page.php:489
#, php-format
msgid "Cache files older than the specified lifespan will be deleted.<br>Enable %1$spreloading%2$s for the cache to be rebuilt automatically after lifespan expiration."
msgstr "Pliki pamięci podręcznej starsze niż określony okres trwania zostaną usunięte.<br> Włącz %1$swstępne ładowanie%2$s, aby pamięć podręczna była automatycznie odbudowywana po upływie okresu trwania."

#: inc/Engine/Admin/Settings/Page.php:503
msgid "Enable caching for logged-in WordPress users"
msgstr "Włącz przechowywanie w pamięci podręcznej dla zalogowanych użytkowników WordPress"

#: inc/Engine/Admin/Settings/Page.php:511
msgid "Enable caching for mobile devices"
msgstr "Włącz przechowywanie w pamięci podręcznej dla urządzeń mobilnych"

#: inc/Engine/Admin/Settings/Page.php:526
msgid "Separate cache files for mobile devices"
msgstr "Przechowuj oddzielnie pliki cache dla urządzeń przenośnych"

#: inc/Engine/Admin/Settings/Page.php:528
#, php-format
msgid "Most modern themes are responsive and should work without a separate cache. Enable this only if you have a dedicated mobile theme or plugin. %1$sMore info%2$s"
msgstr "Większość nowoczesnych motywów jest responsywna i powinna działać bez osobnej pamięci podręcznej. Włącz to tylko, jeśli masz dedykowany motyw mobilny lub wtyczkę. %1$sWięcej informacji%2$s"

#: inc/Engine/Admin/Settings/Page.php:544
msgid "Specify time after which the global cache is cleared<br>(0 = unlimited )"
msgstr "Określ czas, po którym globalna pamięć podręczna zostanie oczyszczona<br>(0 = bez ograniczeń)"

#: inc/Engine/Admin/Settings/Page.php:546
#, php-format
msgid "Reduce lifespan to 10 hours or less if you notice issues that seem to appear periodically. %1$sWhy?%2$s"
msgstr "Skróć cykl trwania do 10 godzin lub mniej, jeśli zauważysz problemy, które wydają się pojawiać okresowo. %1$sDlaczego?%2$s"

#: inc/Engine/Admin/Settings/Page.php:552
#: inc/Engine/License/views/promo-banner.php:30
#: inc/Engine/License/views/renewal-soon-banner.php:13
msgid "Hours"
msgstr "Godziny"

#: inc/Engine/Admin/Settings/Page.php:553
#: inc/Engine/License/views/promo-banner.php:29
#: inc/Engine/License/views/renewal-soon-banner.php:12
msgid "Days"
msgstr "Dzień/Dni"

#: inc/Engine/Admin/Settings/Page.php:602
msgid "File Optimization"
msgstr "Optymalizacja plików"

#: inc/Engine/Admin/Settings/Page.php:603
msgid "Optimize CSS & JS"
msgstr "Optymalizacja CSS i JS"

#: inc/Engine/Admin/Settings/Page.php:621
msgid "CSS Files"
msgstr "Pliki CSS"

#: inc/Engine/Admin/Settings/Page.php:611
#: inc/Engine/Admin/Settings/Page.php:637
#, php-format
msgid "%1$s Minification is currently activated in <strong>Autoptimize</strong>. If you want to use %2$s’s minification, disable those options in Autoptimize."
msgstr "%1$s Minifikacja jest obecnie aktywowana w <strong>Autoptimize</strong>. Jeśli chcesz użyć minifikacji %2$s, wyłącz te opcje w Autoptimize."

#: inc/Engine/Admin/Settings/Page.php:630
msgid "JavaScript Files"
msgstr "Pliki JavaScript"

#: inc/Engine/Admin/Settings/Page.php:663
msgid "Minify CSS files"
msgstr "Minifikuj pliki CSS"

#: inc/Engine/Admin/Settings/Page.php:664
msgid "Minify CSS removes whitespace and comments to reduce the file size."
msgstr "Minifikacja CSS usuwa białą przestrzeń i komentarze, aby zmniejszyć rozmiar pliku."

#: inc/Engine/Admin/Settings/Page.php:677
#: inc/Engine/Admin/Settings/Page.php:701
#: inc/Engine/Admin/Settings/Page.php:765
#: inc/Engine/Admin/Settings/Page.php:831
#: inc/Engine/Admin/Settings/Page.php:855
msgid "This could break things!"
msgstr "To może coś zepsuć!"

#: inc/Engine/Admin/Settings/Page.php:678
#: inc/Engine/Admin/Settings/Page.php:702
#: inc/Engine/Admin/Settings/Page.php:766
#: inc/Engine/Admin/Settings/Page.php:832
#: inc/Engine/Admin/Settings/Page.php:856
msgid "If you notice any errors on your website after having activated this setting, just deactivate it again, and your site will be back to normal."
msgstr "Jeśli zauważysz jakiekolwiek błędy na swojej witrynie internetowej po aktywowaniu tego ustawienia, po prostu wyłącz je ponownie, a Twoja witryna powróci do normy."

#: inc/Engine/Admin/Settings/Page.php:679
msgid "Activate minify CSS"
msgstr "Aktywuj minifikację CSS"

#: inc/Engine/Admin/Settings/Page.php:684
msgid "Combine CSS files <em>(Enable Minify CSS files to select)</em>"
msgstr "Połącz pliki CSS <em>(Włącz Minifikację CSS, aby wybrać)</em>"

#: inc/Engine/Admin/Settings/Page.php:686
#, php-format
msgid "Combine CSS merges all your files into 1, reducing HTTP requests. Not recommended if your site uses HTTP/2. %1$sMore info%2$s"
msgstr "Łączenie CSS konkantenuje wszystkie pliki w jeden, redukując żądania HTTP. Nie zalecane, jeśli witryna używa HTTP/2. %1$sWięcej informacji%2$s"

#: inc/Engine/Admin/Settings/Page.php:687
msgid "For compatibility and best results, this option is disabled when Remove unused CSS is enabled."
msgstr "Dla kompatybilności i najlepszych rezultatów, ta opcja jest wyłączona, gdy włączona jest opcja Usuń nieużywane CSS."

#: inc/Engine/Admin/Settings/Page.php:703
msgid "Activate combine CSS"
msgstr "Aktywuj łączenie CSS"

#: inc/Engine/Admin/Settings/Page.php:708
#: inc/admin/options.php:124
msgid "Excluded CSS Files"
msgstr "Pomijane pliki CSS"

#: inc/Engine/Admin/Settings/Page.php:709
msgid "Specify URLs of CSS files to be excluded from minification and concatenation (one per line)."
msgstr "Określ adresy URL plików CSS, które mają być wyłączone z minifikacji i łączenia (po jednym na linię)"

#: inc/Engine/Admin/Settings/Page.php:710
msgid "<strong>Internal:</strong> The domain part of the URL will be stripped automatically. Use (.*).css wildcards to exclude all CSS files located at a specific path."
msgstr "<strong>Wewnętrzny:</strong> Domenowa część adresu URL zostanie usunięta automatycznie. Użyj (.*).css wildcardów, aby wykluczyć wszystkie pliki CSS znajdujące się na określonej ścieżce."

#: inc/Engine/Admin/Settings/Page.php:712
#, php-format
msgid "<strong>3rd Party:</strong> Use either the full URL path or only the domain name, to exclude external CSS. %1$sMore info%2$s"
msgstr "<strong>Zewnętrzne:</strong> Użyj albo pełnej ścieżki URL albo tylko nazwy domeny, aby wykluczyć zewnętrzny CSS. %1$sWięcej informacji%2$s"

#: inc/Engine/Admin/Settings/Page.php:725
#: inc/Engine/Admin/Settings/Page.php:747
msgid "Optimize CSS delivery"
msgstr "Optymalizacja dostarczania CSS"

#: inc/Engine/Admin/Settings/Page.php:730
msgid "Optimize CSS delivery eliminates render-blocking CSS on your website. Only one method can be selected. Remove Unused CSS is recommended for optimal performance, but limited only to the users with active license."
msgstr "Optymalizacja dostarczania CSS eliminuje blokowanie renderowania CSS na twojej stronie. Tylko jedna metoda może być wybrana. Usuń nieużywany CSS jest zalecany dla optymalnej wydajności, ale ograniczony tylko do użytkowników z aktywną licencją."

#: inc/Engine/Admin/Settings/Page.php:730
msgid "Optimize CSS delivery eliminates render-blocking CSS on your website. Only one method can be selected. Remove Unused CSS is recommended for optimal performance."
msgstr "Optymalizacja dostarczania CSS eliminuje blokowanie renderowania CSS na twojej stronie. Tylko jedna metoda może być wybrana. Usuń nieużywany CSS jest zalecane dla optymalnej wydajności."

#: inc/Engine/Admin/Settings/Page.php:740
#, php-format
msgid "Optimize CSS Delivery features are disabled on local environments. %1$sLearn more%2$s"
msgstr "Optymalizuj CSS Funkcje dostarczania są wyłączone w środowiskach lokalnych. %1$sDowiedz się więcej%2$s"

#: inc/Engine/Admin/Settings/Page.php:760
#: inc/admin/ui/meta-boxes.php:106
msgid "Remove Unused CSS"
msgstr "Usuń nieużywany CSS"

#: inc/Engine/Admin/Settings/Page.php:763
#, php-format
msgid "Removes unused CSS per page and helps to reduce page size and HTTP requests. Recommended for best performance. Test thoroughly! %1$sMore info%2$s"
msgstr "Usuwa nieużywany CSS na stronie i pomaga zredukować rozmiar strony i żądania HTTP. Zalecane dla najlepszej wydajności. Przetestuj dokładnie! %1$sWięcej informacji%2$s"

#: inc/Engine/Admin/Settings/Page.php:767
msgid "Activate Remove Unused CSS"
msgstr "Aktywuj usuwanie nieużywanego CSS"

#: inc/Engine/Admin/Settings/Page.php:773
msgid "CSS safelist"
msgstr "Bezpieczna lista CSS"

#: inc/Engine/Admin/Settings/Page.php:774
msgid "Specify CSS filenames, IDs or classes that should not be removed (one per line)."
msgstr "Określ nazwy plików CSS, identyfikatory lub klasy, które nie powinny być usuwane (jedna na linię)."

#: inc/Engine/Admin/Settings/Page.php:789
#: inc/admin/ui/meta-boxes.php:109
msgid "Load CSS asynchronously"
msgstr "Wczytaj CSS asynchronicznie"

#: inc/Engine/Admin/Settings/Page.php:792
#, php-format
msgctxt "WP Critical CSS compatibility"
msgid "Load CSS asynchronously is currently handled by the %1$s plugin. If you want to use WP Rocket’s load CSS asynchronously option, disable the %1$s plugin."
msgstr "Ładowanie CSS asynchronicznie jest obecnie obsługiwane przez wtyczkę %1$s. Jeśli chcesz korzystać z opcji asynchronicznego ładowania CSS przez WP Rocket, wyłącz wtyczkę %1$s."

#: inc/Engine/Admin/Settings/Page.php:794
#, php-format
msgid "Generates critical path CSS and loads CSS asynchronously. %1$sMore info%2$s"
msgstr "Generuje CSS ścieżki krytycznej i ładuje CSS asynchronicznie. %1$sWięcej informacji%2$s"

#: inc/Engine/Admin/Settings/Page.php:800
msgid "Fallback critical CSS"
msgstr "Krytyczne dla bezpieczeństwa CSS"

#: inc/Engine/Admin/Settings/Page.php:802
#, php-format
msgid "Provides a fallback if auto-generated critical path CSS is incomplete. %1$sMore info%2$s"
msgstr "Zapewnia bezpieczny fallback, jeśli automatycznie wygenerowana ścieżka krytyczna CSS jest niekompletna. %1$sWięcej informacji%2$s"

#: inc/Engine/Admin/Settings/Page.php:817
msgid "Minify JavaScript files"
msgstr "Minifikuj pliki JavaScript"

#: inc/Engine/Admin/Settings/Page.php:818
msgid "Minify JavaScript removes whitespace and comments to reduce the file size."
msgstr "Minifikacja JavaScript usuwa białą przestrzeń i komentarze, aby zmniejszyć rozmiar pliku."

#: inc/Engine/Admin/Settings/Page.php:833
msgid "Activate minify JavaScript"
msgstr "Aktywuj minifikację JavaScript"

#: inc/Engine/Admin/Settings/Page.php:838
msgid "Combine JavaScript files <em>(Enable Minify JavaScript files to select)</em>"
msgstr "Połącz pliki JavaScript <em>(Włącz Minify JavaScript aby wybrać)</em>"

#: inc/Engine/Admin/Settings/Page.php:840
#, php-format
msgid "Combine JavaScript files combines your site’s internal, 3rd party and inline JS reducing HTTP requests. Not recommended if your site uses HTTP/2. %1$sMore info%2$s"
msgstr "Łączenie plików JavaScript łączy w sobie wewnętrzne, zewnętrzne oraz inline JS, redukując żądania HTTP. Nie zalecane, jeśli witryna używa HTTP/2. %1$sWięcej informacji%2$s"

#: inc/Engine/Admin/Settings/Page.php:841
msgid "For compatibility and best results, this option is disabled when delay javascript execution is enabled."
msgstr "Dla kompatybilności i najlepszych wyników, opcja ta jest wyłączona, gdy włączone jest opóźnienie wykonywania javascript."

#: inc/Engine/Admin/Settings/Page.php:857
msgid "Activate combine JavaScript"
msgstr "Aktywuj łączenie JavaScript"

#: inc/Engine/Admin/Settings/Page.php:862
#: inc/admin/options.php:125
msgid "Excluded Inline JavaScript"
msgstr "Pomijany inline JavaScript"

#: inc/Engine/Admin/Settings/Page.php:864
#, php-format
msgid "Specify patterns of inline JavaScript to be excluded from concatenation (one per line). %1$sMore info%2$s"
msgstr "Określa wzory inline JavaScript, które mają być wyłączone z konkatenacji (jeden na linię). %1$sWięcej informacji%2$s"

#: inc/Engine/Admin/Settings/Page.php:880
#: inc/Engine/Admin/Settings/Page.php:913
#: inc/Engine/Admin/Settings/Page.php:960
#: inc/admin/options.php:126
msgid "Excluded JavaScript Files"
msgstr "Pomijane pliki JavaScript"

#: inc/Engine/Admin/Settings/Page.php:881
msgid "Specify URLs of JavaScript files to be excluded from minification and concatenation (one per line)."
msgstr "Określ adresy URL plików JavaScript, które mają być wyłączone z minifikacji i konkatenacji (jeden na linię)."

#: inc/Engine/Admin/Settings/Page.php:882
msgid "<strong>Internal:</strong> The domain part of the URL will be stripped automatically. Use (.*).js wildcards to exclude all JS files located at a specific path."
msgstr "Część domeny adresu URL zostanie automatycznie usunięta.<br>Użyj symboli wieloznacznych wildcard takich jak (.*).js, aby wykluczyć wszystkie pliki JS znajdujące się w określonej ścieżce."

#: inc/Engine/Admin/Settings/Page.php:884
#, php-format
msgid "<strong>3rd Party:</strong> Use either the full URL path or only the domain name, to exclude external JS. %1$sMore info%2$s"
msgstr "<strong>Zewnętrzne: </strong> Użyj pełnej ścieżki URL lub tylko nazwy domeny, aby wykluczyć zewnętrzne JS. %1$sWięcej informacji%2$s"

#: inc/Engine/Admin/Settings/Page.php:900
msgid "Load JavaScript deferred"
msgstr "Ładuj pliki JS metodą defer"

#: inc/Engine/Admin/Settings/Page.php:902
#, php-format
msgid "Load JavaScript deferred eliminates render-blocking JS on your site and can improve load time. %1$sMore info%2$s"
msgstr "Opóźnione ładowanie JavaScript eliminuje blokowanie renderowania JS na Twojej stronie i może poprawić czas ładowania. %1$sWięcej informacji%2$s"

#: inc/Engine/Admin/Settings/Page.php:915
#, php-format
msgid "Specify URLs or keywords of JavaScript files to be excluded from defer (one per line). %1$sMore info%2$s"
msgstr "Podaj adresy URL lub słowa kluczowe plików JavaScript, które mają być wyłączone z odroczenia (jeden na linię). %1$sWięcej informacji%2$s"

#: inc/Engine/Admin/Settings/Page.php:931
#: inc/admin/ui/meta-boxes.php:111
msgid "Delay JavaScript execution"
msgstr "Opóźnianie wykonywania skryptów JavaScript"

#: inc/Engine/Admin/Settings/Page.php:933
#, php-format
msgid "Improves performance by delaying the loading of JavaScript files until user interaction (e.g. scroll, click). %1$sMore info%2$s"
msgstr "Poprawia wydajność poprzez opóźnianie ładowania plików JavaScript do czasu interakcji użytkownika (np. przewijania, klikania). %1$s Więcej informacji%2$s"

#: inc/Engine/Admin/Settings/Page.php:961
msgid "Specify URLs or keywords that can identify inline or JavaScript files to be excluded from delaying execution (one per line)."
msgstr "Określ adresy URL lub słowa kluczowe, które mogą identyfikować pliki inline lub JavaScript, które mają być wyłączone z opóźniania wykonania (jeden na linię)."

#: inc/Engine/Admin/Settings/Page.php:993
msgid "Media"
msgstr "Media"

#: inc/Engine/Admin/Settings/Page.php:994
msgid "LazyLoad, image dimensions"
msgstr "LazyLoad, wymiary obrazu"

#: inc/Engine/Admin/Settings/Page.php:1003
msgid "Autoptimize"
msgstr "Automatyczna optymalizacja"

#: inc/Engine/Admin/Settings/Page.php:1048
msgid "LazyLoad"
msgstr "LazyLoad"

#: inc/Engine/Admin/Settings/Page.php:1051
#, php-format
msgid "It can improve actual and perceived loading time as images, iframes, and videos will be loaded only as they enter (or about to enter) the viewport and reduces the number of HTTP requests. %1$sMore Info%2$s"
msgstr "Może to poprawić rzeczywisty i postrzegany czas ładowania obrazów, ramek iframe, a filmy wideo będą ładowane tylko w momencie ich widoczności (lub w trakcie przewijania) i zmniejsza liczbę żądań HTTP. %1$sWięcej informacji%2$s"

#: inc/Engine/Admin/Settings/Page.php:1058
#, php-format
msgid "LazyLoad is currently activated in %2$s. If you want to use WP Rocket’s LazyLoad, disable this option in %2$s."
msgstr "LazyLoad jest obecnie aktywowany w %2$s. Jeśli chcesz korzystać z LazyLoad WP Rocket, wyłącz tę opcję w %2$s."

#: inc/Engine/Admin/Settings/Page.php:1061
msgid "Image Dimensions"
msgstr "Wymiary Obrazu"

#: inc/Engine/Admin/Settings/Page.php:1064
#, php-format
msgid "Add missing width and height attributes to images. Helps prevent layout shifts and improve the reading experience for your visitors. %1$sMore info%2$s"
msgstr "Dodaj brakujące atrybuty szerokości i wysokości do obrazów. Pomaga zapobiegać przesunięciom układu i poprawić komfort czytania dla odwiedzających. %1$sWięcej informacji %2$s"

#: inc/Engine/Admin/Settings/Page.php:1083
msgid "Enable for images"
msgstr "Włącz dla obrazów"

#: inc/Engine/Admin/Settings/Page.php:1095
#, php-format
msgid "LazyLoad for images is currently activated in %2$s. If you want to use %1$s’s LazyLoad, disable this option in %2$s."
msgstr "LazyLoad dla obrazów jest obecnie aktywowany w %2$s. Jeśli chcesz użyć LazyLoad dla %1$s, wyłącz tę opcję w %2$s."

#: inc/Engine/Admin/Settings/Page.php:1103
msgid "Enable for iframes and videos"
msgstr "Włącz dla iframe i wideo"

#: inc/Engine/Admin/Settings/Page.php:1118
msgid "Replace YouTube iframe with preview image"
msgstr "Wymień iframe YouTube, obrazem podglądu"

#: inc/Engine/Admin/Settings/Page.php:1120
#, php-format
msgid "Replace YouTube iframe with preview image is not compatible with %2$s."
msgstr "Zastępowanie YouTube iframe podglądem obrazu nie jest kompatybilne z %2$s."

#: inc/Engine/Admin/Settings/Page.php:1120
msgid "This can significantly improve your loading time if you have a lot of YouTube videos on a page."
msgstr "Może to znacznie poprawić czas ładowania, jeśli masz dużo filmów YouTube w witrynie."

#: inc/Engine/Admin/Settings/Page.php:1135
msgid "Excluded images or iframes"
msgstr "Wyłączone obrazy lub ramki iframe"

#: inc/Engine/Admin/Settings/Page.php:1137
#, php-format
msgid "Specify keywords (e.g. image filename, CSS class, domain) from the image or iframe code to be excluded (one per line). %1$sMore info%2$s"
msgstr "Podaj słowa kluczowe (np. nazwa pliku obrazu, klasa CSS, domena) z obrazu lub kod iframe, który ma być wykluczony (jeden na linię). %1$s Więcej informacji%2$s"

#: inc/Engine/Admin/Settings/Page.php:1145
msgid "Add missing image dimensions"
msgstr "Dodaj brakujące wymiary obrazu"

#: inc/Engine/Admin/Settings/Page.php:1164
#: inc/deprecated/deprecated.php:1776
msgid "Preload"
msgstr "Wstępne ładowanie"

#: inc/Engine/Admin/Settings/Page.php:1165
msgid "Generate cache files, preload fonts"
msgstr "Generowanie plików w pamięci podręcznej, wstępne ładowanie czcionek"

#: inc/Engine/Admin/Settings/Page.php:1177
msgid "Preload Cache"
msgstr "Wstępne ładowanie pamięci podręcznej"

#: inc/Engine/Admin/Settings/Page.php:1180
msgid "When you enable preloading WP Rocket will automatically detect your sitemaps and save all URLs to the database. The plugin will make sure that your cache is always preloaded."
msgstr "Po włączeniu wstępnego ładowania WP Rocket automatycznie wykryje twoje sitemapy i zapisze wszystkie adresy URL do bazy danych. Wtyczka upewni się, że twoja pamięć podręczna jest zawsze wstępnie załadowana."

#: inc/Engine/Admin/Settings/Page.php:1188
msgid "Preload Links"
msgstr "Linki do wstępnego ładowania"

#: inc/Engine/Admin/Settings/Page.php:1191
#, php-format
msgid "Link preloading improves the perceived load time by downloading a page when a user hovers over the link. %1$sMore info%2$s"
msgstr "Wstępne ładowanie łączy poprawia postrzegany czas ładowania poprzez pobranie strony po najechaniu na nią przez użytkownika. %1$s Więcej informacji%2$s"

#: inc/Engine/Admin/Settings/Page.php:1199
msgid "Prefetch DNS Requests"
msgstr "Prefetch żądania DNS"

#: inc/Engine/Admin/Settings/Page.php:1201
msgid "DNS prefetching can make external files load faster, especially on mobile networks"
msgstr "Prefetch DNS może przyspieszyć ładowanie plików zewnętrznych, szczególnie w sieciach komórkowych"

#: inc/Engine/Admin/Settings/Page.php:1206
msgid "Preload Fonts"
msgstr "Wstępne ładowanie czcionek"

#: inc/Engine/Admin/Settings/Page.php:1209
#, php-format
msgid "Improves performance by helping browsers discover fonts in CSS files. %1$sMore info%2$s"
msgstr "Poprawia wydajność, pomagając przeglądarkom w odkrywaniu czcionek w plikach CSS. %1$s Więcej informacji%2$s"

#: inc/Engine/Admin/Settings/Page.php:1223
msgid "Activate Preloading"
msgstr "Aktywuj Wstępne Ładowanie"

#: inc/Engine/Admin/Settings/Page.php:1234
msgid "Exclude URLs"
msgstr "Wykluczanie adresów URL"

#: inc/Engine/Admin/Settings/Page.php:1239
#, php-format
msgid "Specify URLs to be excluded from the preload feature (one per line). %1$sMore info%2$s"
msgstr "Określ adresy URL, które mają być wykluczone z funkcji preload (jeden na linię). %1$sWięcej informacji%2$s"

#: inc/Engine/Admin/Settings/Page.php:1250
msgid "URLs to prefetch"
msgstr "URL do prefetch"

#: inc/Engine/Admin/Settings/Page.php:1251
msgid "Specify external hosts to be prefetched (no <code>http:</code>, one per line)"
msgstr "Określ zewnętrzne hosty, które mają być wstępnie ustawione metodą prefetch (nie dodawaj <code>http:</code>, po jednym na linię)"

#: inc/Engine/Admin/Settings/Page.php:1260
msgid "Fonts to preload"
msgstr "Czcionki do wstępnego ładowania"

#: inc/Engine/Admin/Settings/Page.php:1261
msgid "Specify urls of the font files to be preloaded (one per line). Fonts must be hosted on your own domain, or the domain you have specified on the CDN tab."
msgstr "Określ urle plików z czcionkami, które mają być wgrane (jeden na linię). Czcionki muszą być hostowane we własnej domenie, lub domenie, którą podałeś w zakładce CDN."

#: inc/Engine/Admin/Settings/Page.php:1262
msgid "The domain part of the URL will be stripped automatically.<br/>Allowed font extensions: otf, ttf, svg, woff, woff2."
msgstr "Domenowa część adresu URL zostanie usunięta automatycznie.<br/>Dozwolone rozszerzenia czcionki: otf, ttf, svg, woff, woff2."

#: inc/Engine/Admin/Settings/Page.php:1271
msgid "Enable link preloading"
msgstr "Włącz wstępne ładowanie łączy"

#: inc/Engine/Admin/Settings/Page.php:1290
msgid "Advanced Rules"
msgstr "Zaawansowane reguły"

#: inc/Engine/Admin/Settings/Page.php:1291
msgid "Fine-tune cache rules"
msgstr "Dopasowanie zasad dotyczących pamięci podręcznej"

#: inc/Engine/Admin/Settings/Page.php:1302
msgid "Sensitive pages like custom login/logout URLs should be excluded from cache."
msgstr "Wrażliwe strony, takie jak niestandardowe adresy URL logowania/wylogowywania powinny być wyłączone z pamięci podręcznej."

#: inc/Engine/Admin/Settings/Page.php:1305
msgctxt "plugin name"
msgid "WooCommerce"
msgstr "WooCommerce"

#: inc/Engine/Admin/Settings/Page.php:1307
msgctxt "plugin name"
msgid "Easy Digital Downloads"
msgstr "Easy Digital Downloads"

#: inc/Engine/Admin/Settings/Page.php:1309
msgctxt "plugin name"
msgid "iThemes Exchange"
msgstr "iThemes Exchange"

#: inc/Engine/Admin/Settings/Page.php:1311
msgctxt "plugin name"
msgid "Jigoshop"
msgstr "Jigoshop"

#: inc/Engine/Admin/Settings/Page.php:1313
msgctxt "plugin name"
msgid "WP-Shop"
msgstr "WP-Shop"

#: inc/Engine/Admin/Settings/Page.php:1319
#, php-format
msgid "<br>Cart, checkout and \"my account\" pages set in <strong>%1$s%2$s%3$s</strong> will be detected and never cached by default."
msgstr "<br>Strony koszyka, kasy i \"moje konto\" ustawione w <strong>%1$s%2$s%3$s</strong> zostaną wykryte i nigdy nie będą domyślnie buforowane."

#: inc/Engine/Admin/Settings/Page.php:1329
#: inc/admin/options.php:129
msgid "Never Cache URL(s)"
msgstr "Nigdy nie używaj pamięci podręcznej dla URL(i)"

#: inc/Engine/Admin/Settings/Page.php:1337
msgid "Never Cache Cookies"
msgstr "Nigdy nie używaj pamięci podręcznej dla cookies"

#: inc/Engine/Admin/Settings/Page.php:1343
#: inc/admin/options.php:130
msgid "Never Cache User Agent(s)"
msgstr "Nigdy nie używaj pamięci podręcznej dla agenta(-ów) użytkowników"

#: inc/Engine/Admin/Settings/Page.php:1349
#: inc/admin/options.php:131
msgid "Always Purge URL(s)"
msgstr "Zawsze oczyścić adres(y) URL"

#: inc/Engine/Admin/Settings/Page.php:1355
msgid "Cache Query String(s)"
msgstr "Przechowuj ciąg(i) zapytań w pamięci podręcznej"

#: inc/Engine/Admin/Settings/Page.php:1358
#, php-format
msgid "%1$sCache for query strings%2$s enables you to force caching for specific GET parameters."
msgstr "%1$sPrzechowywanie ciąg(-ów) zapytań w pamięci podręcznej%2$s umożliwia wymuszenie buforowania dla określonych parametrów GET."

#: inc/Engine/Admin/Settings/Page.php:1369
msgid "Specify URLs of pages or posts that should never be cached (one per line)"
msgstr "Określ adresy URL stron lub wpisów, które nigdy nie powinny być przechowywane w pamięci podręcznej (po jednym na linię)"

#: inc/Engine/Admin/Settings/Page.php:1370
#: inc/Engine/Admin/Settings/Page.php:1398
msgid "The domain part of the URL will be stripped automatically.<br>Use (.*) wildcards to address multiple URLs under a given path."
msgstr "Część domeny adresu URL zostanie automatycznie usunięta. <br>Użyj symboli wieloznacznych wildcard (.*), w celu uwzględnienia wielu adresów URL w danej ścieżce."

#: inc/Engine/Admin/Settings/Page.php:1379
msgid "Specify full or partial IDs of cookies that, when set in the visitor's browser, should prevent a page from getting cached (one per line)"
msgstr "Podaj pełne lub częściowe identyfikatory plików cookie, które po ustawieniu w przeglądarce użytkownika powinny uniemożliwiać zbuforowanie strony (jeden na linię)"

#: inc/Engine/Admin/Settings/Page.php:1387
msgid "Specify user agent strings that should never see cached pages (one per line)"
msgstr "Określ agentów użytkowników, którzy nigdy nie powinni otrzymywać stron z pamięci podręcznej (po jednej na linię)"

#: inc/Engine/Admin/Settings/Page.php:1388
msgid "Use (.*) wildcards to detect parts of UA strings."
msgstr "Użyj symboli wieloznacznych wildcard (.*) do wykrywania części łańcuchów UA."

#: inc/Engine/Admin/Settings/Page.php:1397
msgid "Specify URLs you always want purged from cache whenever you update any post or page (one per line)"
msgstr "Określ adresy URL, które zawsze chcesz usunąć z pamięci podręcznej przy każdej aktualizacji wpisu lub strony (po jednym na linię)"

#: inc/Engine/Admin/Settings/Page.php:1406
msgid "Specify query strings for caching (one per line)"
msgstr "Określ ciągi zapytań przeznaczonych do przechowywania w pamięci podręcznej (po jednym na linię)"

#: inc/Engine/Admin/Settings/Page.php:1431
#: inc/deprecated/deprecated.php:1775
msgid "Database"
msgstr "Baza danych"

#: inc/Engine/Admin/Settings/Page.php:1432
msgid "Optimize, reduce bloat"
msgstr "Optymalizacja, redukcja nadmiaru"

#: inc/Engine/Admin/Settings/Page.php:1439
msgid "Post Cleanup"
msgstr "Czyszczenie wpisów"

#: inc/Engine/Admin/Settings/Page.php:1441
msgid "Post revisions and drafts will be permanently deleted. Do not use this option if you need to retain revisions or drafts."
msgstr "Wersje wpisów i szkice zostaną trwale usunięte. Nie używaj tej opcji, jeśli chcesz zachować poprzednie wersje w wpisach lub szkice."

#: inc/Engine/Admin/Settings/Page.php:1446
msgid "Comments Cleanup"
msgstr "Czyszczenie komentarzy"

#: inc/Engine/Admin/Settings/Page.php:1448
msgid "Spam and trashed comments will be permanently deleted."
msgstr "Spam i komentarze znajdujące się w koszu zostaną trwale usunięte."

#: inc/Engine/Admin/Settings/Page.php:1452
msgid "Transients Cleanup"
msgstr "Czyszczenie tymczasowych wpisów"

#: inc/Engine/Admin/Settings/Page.php:1454
msgid "Transients are temporary options; they are safe to remove. They will be automatically regenerated as your plugins require them."
msgstr "Tymczasowe wpisy (transients) są opcjami tymczasowymi; można je bezpiecznie usunąć. Będą one automatycznie regenerowane zgodnie z wymaganiami dotyczącymi wtyczek."

#: inc/Engine/Admin/Settings/Page.php:1458
msgid "Database Cleanup"
msgstr "Czyszczenie bazy danych"

#: inc/Engine/Admin/Settings/Page.php:1460
msgid "Reduces overhead of database tables"
msgstr "Zmniejsza nadmiar danych w tabelach bazy danych"

#: inc/Engine/Admin/Settings/Page.php:1464
msgid "Automatic Cleanup"
msgstr "Automatyczne czyszczenie"

#: inc/Engine/Admin/Settings/Page.php:1477
#, php-format
msgid "%s revision in your database."
msgid_plural "%s revisions in your database."
msgstr[0] "%s wersja w Twojej bazie danych."
msgstr[1] "%s wersje w Twojej bazie danych."
msgstr[2] "%s wersji w Twojej bazie danych."
msgstr[3] "%s wersji w Twojej bazie danych."

#: inc/Engine/Admin/Settings/Page.php:1487
#, php-format
msgid "%s draft in your database."
msgid_plural "%s drafts in your database."
msgstr[0] "%s szkic w Twojej bazie danych."
msgstr[1] "%s szkice w Twojej bazie danych."
msgstr[2] "%s szkicy w Twojej bazie danych."
msgstr[3] "%s szkicy w Twojej bazie danych."

#: inc/Engine/Admin/Settings/Page.php:1497
#, php-format
msgid "%s trashed post in your database."
msgid_plural "%s trashed posts in your database."
msgstr[0] "%s wpis w Twojej bazie danych znajdujący się w koszu."
msgstr[1] "%s wpisy w Twojej bazie danych znajdujące się w koszu."
msgstr[2] "%s wpisów w Twojej bazie danych znajdujących się w koszu."
msgstr[3] "%s wpisów w Twojej bazie danych znajdujących się w koszu."

#: inc/Engine/Admin/Settings/Page.php:1507
#, php-format
msgid "%s spam comment in your database."
msgid_plural "%s spam comments in your database."
msgstr[0] "%s spamowy komentarz w Twojej bazie danych."
msgstr[1] "%s spamowe komentarze w Twojej bazie danych."
msgstr[2] "%s spamowych komentarzy w Twojej bazie danych."
msgstr[3] "%s spamowych komentarzy w Twojej bazie danych."

#: inc/Engine/Admin/Settings/Page.php:1517
#, php-format
msgid "%s trashed comment in your database."
msgid_plural "%s trashed comments in your database."
msgstr[0] "%s komentarz w Twojej bazie danych znajdujący się w koszu."
msgstr[1] "%s komentarze w Twojej bazie danych znajdujące się w koszu."
msgstr[2] "%s komentarzy w Twojej bazie danych znajdujące się w koszu."
msgstr[3] "%s komentarzy w Twojej bazie danych znajdujące się w koszu."

#: inc/Engine/Admin/Settings/Page.php:1525
msgid "All transients"
msgstr "Wszystkie tymczasowe wpisy"

#: inc/Engine/Admin/Settings/Page.php:1527
#, php-format
msgid "%s transient in your database."
msgid_plural "%s transients in your database."
msgstr[0] "%s wpis tymczasowy w Twojej bazie danych."
msgstr[1] "%s wpisy tymczasowy w Twojej bazie danych."
msgstr[2] "%s wpisów tymczasowych w Twojej bazie danych."
msgstr[3] "%s wpisów tymczasowych w Twojej bazie danych."

#: inc/Engine/Admin/Settings/Page.php:1535
msgid "Optimize Tables"
msgstr "Optymalizacja tabel"

#: inc/Engine/Admin/Settings/Page.php:1537
#, php-format
msgid "%s table to optimize in your database."
msgid_plural "%s tables to optimize in your database."
msgstr[0] "%s tabela do optymalizacji w Twojej bazie danych."
msgstr[1] "%s tabele do optymalizacji w Twojej bazie danych."
msgstr[2] "%s tabeli do optymalizacji w Twojej bazie danych."
msgstr[3] "%s tabeli do optymalizacji w Twojej bazie danych."

#: inc/Engine/Admin/Settings/Page.php:1548
msgid "Schedule Automatic Cleanup"
msgstr "Zaplanuj automatyczne czyszczenie"

#: inc/Engine/Admin/Settings/Page.php:1560
msgid "Frequency"
msgstr "Częstotliwość"

#: inc/Engine/Admin/Settings/Page.php:1568
msgid "Daily"
msgstr "Codziennie"

#: inc/Engine/Admin/Settings/Page.php:1569
msgid "Weekly"
msgstr "Co tydzień"

#: inc/Engine/Admin/Settings/Page.php:1570
msgid "Monthly"
msgstr "Co miesiąc"

#: inc/Engine/Admin/Settings/Page.php:1586
#: inc/Engine/Admin/Settings/Page.php:1597
#: inc/admin/ui/meta-boxes.php:108
#: inc/deprecated/deprecated.php:1773
msgid "CDN"
msgstr "CDN"

#: inc/Engine/Admin/Settings/Page.php:1587
msgid "Integrate your CDN"
msgstr "Zintegruj swój CDN"

#: inc/Engine/Admin/Settings/Page.php:1599
msgid "All URLs of static files (CSS, JS, images) will be rewritten to the CNAME(s) you provide."
msgstr "Wszystkie adresy URL plików statycznych (CSS, JS, obrazy) zostaną przepisane do CNAME, które podasz."

#: inc/Engine/Admin/Settings/Page.php:1601
#, php-format
msgid "Not required for services like Cloudflare and Sucuri. Please see our available %1$sAdd-ons%2$s."
msgstr "Nie wymagane w przypadku usług takich jak Cloudflare i Sucuri. Proszę zobaczyć nasze dostępne %1$sDodatki%2$s."

#: inc/Engine/Admin/Settings/Page.php:1616
#: inc/admin/options.php:132
msgid "Exclude files from CDN"
msgstr "Pomiń pliki z CDN"

#: inc/Engine/Admin/Settings/Page.php:1642
msgid "%1$s%2$l Add-on%3$s is currently enabled. Configuration of the CDN settings is not required for %2$l to work on your site."
msgid_plural "%1$s%2$l Add-ons%3$s are currently enabled. Configuration of the CDN settings is not required for %2$l to work on your site."
msgstr[0] "%1$s%2$l Dodatek%3$s jest obecnie włączony. Konfiguracja ustawień CDN nie jest wymagana, aby %2$l działał na twojej stronie."
msgstr[1] "%1$s%2$l Dodatki%3$s jest obecnie włączony. Konfiguracja ustawień CDN nie jest wymagana, aby %2$l działał na twojej stronie."
msgstr[2] "%1$s%2$l Dodatków%3$s jest obecnie włączony. Konfiguracja ustawień CDN nie jest wymagana, aby %2$l działał na twojej stronie."
msgstr[3] "%1$s%2$l Dodatków%3$s jest obecnie włączony. Konfiguracja ustawień CDN nie jest wymagana, aby %2$l działał na twojej stronie."

#: inc/Engine/Admin/Settings/Page.php:1667
msgid "Enable Content Delivery Network"
msgstr "Włącz sieć dostarczania treści CDN"

#: inc/Engine/Admin/Settings/Page.php:1676
#: inc/Engine/CDN/RocketCDN/AdminPageSubscriber.php:159
msgid "CDN CNAME(s)"
msgstr "CDN CNAME:"

#: inc/Engine/Admin/Settings/Page.php:1677
#: inc/Engine/CDN/RocketCDN/AdminPageSubscriber.php:160
msgid "Specify the CNAME(s) below"
msgstr "Należy wymienić poniżej pozycje CNAME"

#: inc/Engine/Admin/Settings/Page.php:1684
msgid "Specify URL(s) of files that should not get served via CDN (one per line)."
msgstr "Podaj adres(y) URL plików, które nie powinny być obsługiwane przez CDN (jeden na linię)."

#: inc/Engine/Admin/Settings/Page.php:1685
msgid "The domain part of the URL will be stripped automatically.<br>Use (.*) wildcards to exclude all files of a given file type located at a specific path."
msgstr "Część domenowa adresu URL zostanie automatycznie usunięta.<br>Użyj (.*) symboli wieloznacznych, aby wykluczyć wszystkie pliki danego typu pliku znajdującego się w określonej ścieżce."

#: inc/Engine/Admin/Settings/Page.php:1708
#: inc/Engine/Admin/Settings/Page.php:1716
msgid "Heartbeat"
msgstr "Heartbeat"

#: inc/Engine/Admin/Settings/Page.php:1709
msgid "Control WordPress Heartbeat API"
msgstr "Kontrola WordPress Heartbeat API"

#: inc/Engine/Admin/Settings/Page.php:1717
msgid "Reducing or disabling the Heartbeat API’s activity can help save some of your server’s resources."
msgstr "Ograniczenie lub wyłączenie aktywności Heartbeat API może pomóc w oszczędzaniu zasobów Twojego serwera."

#: inc/Engine/Admin/Settings/Page.php:1723
msgid "Reduce or disable Heartbeat activity"
msgstr "Zmniejszenie lub wyłączenie aktywności Heartbeat"

#: inc/Engine/Admin/Settings/Page.php:1724
msgid "Reducing activity will change Heartbeat frequency from one hit each minute to one hit every 2 minutes."
msgstr "Zmniejszenie aktywności spowoduje zmianę częstotliwości Heartbeat z jednego uruchomienia co minutę na jedno uruchomienie co 2 minuty."

#: inc/Engine/Admin/Settings/Page.php:1724
msgid "Disabling Heartbeat entirely may break plugins and themes using this API."
msgstr "Wyłączenie Heartbeat całkowicie może spowodować usterki wtyczek i motywów korzystających z tego API."

#: inc/Engine/Admin/Settings/Page.php:1738
msgid "Do not limit"
msgstr "Brak limitu"

#: inc/Engine/Admin/Settings/Page.php:1739
msgid "Reduce activity"
msgstr "Zmniejszenie aktywności"

#: inc/Engine/Admin/Settings/Page.php:1740
msgid "Disable"
msgstr "Wyłącz"

#: inc/Engine/Admin/Settings/Page.php:1748
msgid "Control Heartbeat"
msgstr "Kontrola Heartbeat"

#: inc/Engine/Admin/Settings/Page.php:1757
msgid "Behavior in backend"
msgstr "Zachowanie w backendzie"

#: inc/Engine/Admin/Settings/Page.php:1764
msgid "Behavior in post editor"
msgstr "Zachowanie w edytorze wpisu"

#: inc/Engine/Admin/Settings/Page.php:1770
msgid "Behavior in frontend"
msgstr "Zachowanie w frontend"

#: inc/Engine/Admin/Settings/Page.php:1787
#: views/settings/page-sections/tutorials.php:39
msgid "Add-ons"
msgstr "Dodatki"

#: inc/Engine/Admin/Settings/Page.php:1788
msgid "Add more features"
msgstr "Dodaj więcej funkcji"

#: inc/Engine/Admin/Settings/Page.php:1795
msgid "One-click Rocket Add-ons"
msgstr "Dodatki One-click Rocket Add-ons"

#: inc/Engine/Admin/Settings/Page.php:1796
msgid "One-Click Add-ons are features extending available options without configuration needed. Switch the option \"on\" to enable from this screen."
msgstr "One-Click Add-ons to funkcje rozszerzające dostępne opcje bez konieczności konfigurowania. Włączyć opcję \"on\", aby włączyć z tego ekranu."

#: inc/Engine/Admin/Settings/Page.php:1806
msgid "Rocket Add-ons"
msgstr "Dodatki One-click Rocket Add-ons"

#: inc/Engine/Admin/Settings/Page.php:1807
msgid "Rocket Add-ons are complementary features extending available options."
msgstr "Rocket Add-ons to uzupełniające się funkcje rozszerzające dostępne opcje."

#: inc/Engine/Admin/Settings/Page.php:1817
#: inc/Engine/Admin/Settings/Page.php:1986
msgid "Cloudflare"
msgstr "CloudFlare"

#: inc/Engine/Admin/Settings/Page.php:1823
msgid "Integrate your Cloudflare account with this add-on."
msgstr "Zintegruj swoje konto CloudFlare dzięki temu dodatkowi."

#: inc/Engine/Admin/Settings/Page.php:1824
msgid "Provide your account email, global API key, and domain to use options such as clearing the Cloudflare cache and enabling optimal settings with WP Rocket."
msgstr "Podaj swój adres e-mail konta, globalny klucz API i domenę, aby korzystać z opcji takich jak czyszczenie pamięci podręcznej Cloudflare i umożliwienie optymalnych ustawień z WP Rocket."

#: inc/Engine/Admin/Settings/Page.php:1875
msgid "Varnish"
msgstr "Varnish"

#: inc/Engine/Admin/Settings/Page.php:1881
msgid "If Varnish runs on your server, you must activate this add-on."
msgstr "Jeśli Varnish działa na Twoim serwerze, musisz aktywować ten dodatek."

#: inc/Engine/Admin/Settings/Page.php:1883
#, php-format
msgid "Varnish cache will be purged each time WP Rocket clears its cache to ensure content is always up-to-date.<br>%1$sLearn more%2$s"
msgstr "Pamięć podręczna Varnish będzie oczyszczana za każdym razem, gdy WP Rocket wyczyści pamięć podręczną, aby zapewnić, że zawartość jest zawsze aktualna.<br>%1$sDowiedz się więcej%2$s"

#: inc/Engine/Admin/Settings/Page.php:1918
msgid "WebP Compatibility"
msgstr "Zgodność z WebP"

#: inc/Engine/Admin/Settings/Page.php:1924
msgid "Improve browser compatibility for WebP images."
msgstr "Poprawiono kompatybilność obrazów WebP z przeglądarkami."

#: inc/Engine/Admin/Settings/Page.php:1928
#, php-format
msgid "Enable this option if you would like WP Rocket to serve WebP images to compatible browsers. Please note that WP Rocket cannot create WebP images for you. To create WebP images we recommend %1$sImagify%2$s. %3$sMore info%2$s"
msgstr "Włącz tę opcję, jeśli chcesz, aby WP Rocket obsługiwał obrazy WebP dla kompatybilnych przeglądarek. Proszę zauważyć, że WP Rocket nie może tworzyć obrazów WebP dla Ciebie. Do tworzenia obrazów WebP polecamy %1$sImagify%2$s. %3$sWięcej informacji%2$s"

#: inc/Engine/Admin/Settings/Page.php:1948
msgid "Clear the Sucuri cache when WP Rocket’s cache is cleared."
msgstr "Wyczyść pamięć podręczną Sucuri, gdy pamięć podręczna WP Rocket jest czyszczona."

#: inc/Engine/Admin/Settings/Page.php:1951
msgid "Provide your API key to clear the Sucuri cache when WP Rocket’s cache is cleared."
msgstr "Podaj swój klucz API, aby wyczyścić pamięć podręczną Sucuri, gdy pamięć podręczna WP Rocket jest czyszczona."

#: inc/Engine/Admin/Settings/Page.php:1959
#: inc/Engine/Admin/Settings/Page.php:2103
msgid "Sucuri"
msgstr "Sucuri"

#: inc/Engine/Admin/Settings/Page.php:1965
msgid "Synchronize Sucuri cache with this add-on."
msgstr "Zsynchronizuj pamięć podręczną Sucuri, dzięki temu dodatkowi."

#: inc/Engine/Admin/Settings/Page.php:2003
msgid "Cloudflare credentials"
msgstr "Dane uwierzytelniające CloudFlare"

#: inc/Engine/Admin/Settings/Page.php:2012
msgid "Cloudflare settings"
msgstr "Ustawienia CloudFlare"

#: inc/Engine/Admin/Settings/Page.php:2026
msgctxt "Cloudflare"
msgid "Global API key:"
msgstr "Globalny klucz API:"

#: inc/Engine/Admin/Settings/Page.php:2027
msgctxt "Cloudflare"
msgid "Find your API key"
msgstr "Znajdź swój klucz API"

#: inc/Engine/Admin/Settings/Page.php:2039
msgctxt "Cloudflare"
msgid "Account email"
msgstr "E-mail konta"

#: inc/Engine/Admin/Settings/Page.php:2048
msgctxt "Cloudflare"
msgid "Zone ID"
msgstr "ID strefy"

#: inc/Engine/Admin/Settings/Page.php:2058
msgid "Development mode"
msgstr "Tryb deweloperski"

#: inc/Engine/Admin/Settings/Page.php:2060
#, php-format
msgid "Temporarily activate development mode on your website. This setting will automatically turn off after 3 hours. %1$sLearn more%2$s"
msgstr "Tymczasowo aktywuj tryb deweloperski w swojej witrynie. To ustawienie wyłączy się automatycznie po 3 godzinach. %1$sDowiedz się więcej%2$s"

#: inc/Engine/Admin/Settings/Page.php:2068
msgid "Optimal settings"
msgstr "Optymalne ustawienia"

#: inc/Engine/Admin/Settings/Page.php:2069
msgid "Automatically enhances your Cloudflare configuration for speed, performance grade and compatibility."
msgstr "Automatycznie poprawia konfigurację CloudFlare w celu zapewnienia szybkości, klasy wydajności i kompatybilności."

#: inc/Engine/Admin/Settings/Page.php:2077
msgid "Relative protocol"
msgstr "Protokół względny"

#: inc/Engine/Admin/Settings/Page.php:2078
msgid "Should only be used with Cloudflare's flexible SSL feature. URLs of static files (CSS, JS, images) will be rewritten to use // instead of http:// or https://."
msgstr "Powinien być używany tylko z elastyczną funkcją Cloudflare'a SSL. adresy URL plików statycznych (CSS, JS, obrazy) zostaną przepisane w celu użycia // zamiast http:// lub https://."

#: inc/Engine/Admin/Settings/Page.php:2116
msgid "Sucuri credentials"
msgstr "Dane uwierzytelniające Sucuri"

#: inc/Engine/Admin/Settings/Page.php:2130
msgctxt "Sucuri"
msgid "Find your API key"
msgstr "Znajdź swój klucz API"

#: inc/Engine/Admin/Settings/Render.php:422
#: inc/deprecated/deprecated.php:1294
msgid "Upload file and import settings"
msgstr "Wczytaj plik i zaimportuj ustawienia"

#: inc/Engine/Admin/Settings/Settings.php:362
msgid "Sucuri Add-on: The API key for the Sucuri firewall must be in format <code>{32 characters}/{32 characters}</code>."
msgstr "Dodatek Sucuri: Klucz API dla zapory sieciowej Sucuri musi być w formacie <code>{32 znaki}/{32 znaki}</code>."

#: inc/Engine/Admin/Settings/Settings.php:452
#: inc/deprecated/deprecated.php:1245
msgid "Settings saved."
msgstr "Ustawienia zostały zapisane."

#: inc/Engine/Admin/Settings/Settings.php:668
msgid "Sorry! Adding /(.*) in Advanced Rules > Never Cache URL(s) was not saved because it disables caching and optimizations for every page on your site."
msgstr "Przepraszamy! Dodanie /(.*) w Regułach zaawansowanych > Nigdy nie buforuj URL(i) nie zostało zapisane, ponieważ wyłącza buforowanie i optymalizacje dla każdej strony w Twojej witrynie."

#: inc/Engine/Admin/Settings/Subscriber.php:168
#: inc/deprecated/deprecated.php:1786
#: views/settings/page-sections/tools.php:33
msgid "Tools"
msgstr "Narzędzia"

#: inc/Engine/Admin/Settings/Subscriber.php:169
msgid "Import, Export, Rollback"
msgstr "Import, Eksport, Wycofanie"

#: inc/Engine/Admin/Settings/Subscriber.php:194
#: views/settings/page-sections/imagify.php:14
msgid "Image Optimization"
msgstr "Optymalizacja obrazków"

#: inc/Engine/Admin/Settings/Subscriber.php:195
msgid "Compress your images"
msgstr "Kompresuj obrazy"

#: inc/Engine/Admin/Settings/Subscriber.php:212
#: views/settings/page-sections/tutorials.php:48
msgid "Tutorials"
msgstr "Poradniki"

#: inc/Engine/Admin/Settings/Subscriber.php:213
msgid "Getting started and how to videos"
msgstr "Pierwsze kroki i szkolenia wideo"

#: inc/Engine/CDN/RocketCDN/APIClient.php:134
msgid "We could not fetch the current price because RocketCDN API returned an unexpected error code."
msgstr "Nie mogliśmy pobrać aktualnej ceny, ponieważ API RocketCDN zwróciło nieoczekiwany kod błędu."

#: inc/Engine/CDN/RocketCDN/APIClient.php:140
msgid "RocketCDN is not available at the moment. Please retry later."
msgstr "RocketCDN nie jest w tej chwili dostępny. Proszę spróbować ponownie później."

#: inc/Engine/CDN/RocketCDN/APIClient.php:177
msgid "RocketCDN cache purge failed: Missing identifier parameter."
msgstr "RocketCDN: nie udało się oczyścić pamięci podręcznej: Brakujący parametr identyfikatora."

#: inc/Engine/CDN/RocketCDN/APIClient.php:186
msgid "RocketCDN cache purge failed: Missing user token."
msgstr "RocketCDN: nie udało się oczyścić pamięci podręcznej: Brakujący token użytkownika."

#: inc/Engine/CDN/RocketCDN/APIClient.php:212
msgid "RocketCDN cache purge failed: The API returned an unexpected response code."
msgstr "RocketCDN: nie udało się oczyścić pamięci podręcznej: API zwróciło nieoczekiwany kod odpowiedzi."

#: inc/Engine/CDN/RocketCDN/APIClient.php:221
msgid "RocketCDN cache purge failed: The API returned an empty response."
msgstr "RocketCDN: nie udało się oczyścić pamięci podręcznej: API zwróciło pustą odpowiedź."

#: inc/Engine/CDN/RocketCDN/APIClient.php:230
msgid "RocketCDN cache purge failed: The API returned an unexpected response."
msgstr "RocketCDN: nie udało się oczyścić pamięci podręcznej: API zwróciło nieoczekiwaną odpowiedź."

#: inc/Engine/CDN/RocketCDN/APIClient.php:239
#, php-format
msgid "RocketCDN cache purge failed: %s."
msgstr "RocketCDN: nie udało się oczyścić pamięci podręcznej: cache purge nie powiodło się: %s."

#: inc/Engine/CDN/RocketCDN/APIClient.php:247
msgid "RocketCDN cache purge successful."
msgstr "RocketCDN: pomyślnie oczyszczono pamięć podręczną."

#: inc/Engine/CDN/RocketCDN/AdminPageSubscriber.php:90
msgid "Next Billing Date"
msgstr "Data wystawienia następnego rachunku"

#: inc/Engine/CDN/RocketCDN/AdminPageSubscriber.php:99
msgid "No Subscription"
msgstr "Brak subskrypcji"

#: inc/Engine/CDN/RocketCDN/AdminPageSubscriber.php:135
msgid "Your RocketCDN subscription is currently active."
msgstr "Twoja subskrypcja RocketCDN jest obecnie aktywna."

#: inc/Engine/CDN/RocketCDN/AdminPageSubscriber.php:141
#, php-format
msgid "To use RocketCDN, replace your CNAME with %1$s%2$s%3$s."
msgstr "Aby użyć RocketCDN, zastąp CNAME wartością %1$s%2$s%3$s."

#: inc/Engine/CDN/RocketCDN/AdminPageSubscriber.php:152
#: inc/Engine/CDN/RocketCDN/NoticesSubscriber.php:230
#: inc/Engine/CDN/RocketCDN/NoticesSubscriber.php:334
#, php-format
msgid "%1$sMore Info%2$s"
msgstr "%1$sWięcej informacji%2$s"

#: inc/Engine/CDN/RocketCDN/RESTSubscriber.php:137
msgid "RocketCDN enabled"
msgstr "RocketCDN włączony"

#: inc/Engine/CDN/RocketCDN/RESTSubscriber.php:160
msgid "RocketCDN disabled"
msgstr "RakietaCDN wyłączony"

#: inc/Engine/CDN/RocketCDN/views/cta-big.php:27
#, php-format
msgid "Valid until %s only!"
msgstr "Ważny tylko do %s!"

#: inc/Engine/CDN/RocketCDN/views/cta-big.php:36
msgid "Speed up your website thanks to:"
msgstr "Dzięki temu przyspieszysz swoją stronę internetową:"

#: inc/Engine/CDN/RocketCDN/views/cta-big.php:42
#, php-format
msgid "High performance Content Delivery Network (CDN) with %1$sunlimited bandwidth%2$s"
msgstr "Wysokowydajna sieć dostarczania treści (CDN) z %1$snieograniczoną przepustowością%2$s"

#: inc/Engine/CDN/RocketCDN/views/cta-big.php:48
#, php-format
msgid "Easy configuration: the %1$sbest CDN settings%2$s are automatically applied"
msgstr "Łatwa konfiguracja: najważniejsze %1$sustawienia CDN%2$s są stosowane automatycznie"

#: inc/Engine/CDN/RocketCDN/views/cta-big.php:54
#, php-format
msgid "WP Rocket integration: the CDN option is %1$sautomatically configured%2$s in our plugin"
msgstr "Integracja z WP Rocketem: opcja CDN jest %1$sautomatycznie skonfigurowana%2$s w naszej wtyczce"

#: inc/Engine/CDN/RocketCDN/views/cta-big.php:58
msgid "Learn more about RocketCDN"
msgstr "Dowiedz się więcej o RocketCDN"

#: inc/Engine/CDN/RocketCDN/views/cta-big.php:65
#, php-format
msgid "*$%1$s/month for 12 months then $%2$s/month. You can cancel your subscription at any time."
msgstr "*$%1$s/miesiąc przez 12 miesięcy, a następnie $%2$s/miesiąc. Możesz anulować subskrypcję w dowolnym momencie."

#: inc/Engine/CDN/RocketCDN/views/cta-big.php:86
msgid "Billed monthly"
msgstr "Rozliczane miesięcznie"

#: inc/Engine/CDN/RocketCDN/views/cta-big.php:87
msgid "Get Started"
msgstr "Rozpocznij"

#: inc/Engine/CDN/RocketCDN/views/cta-big.php:92
msgid "Reduce this banner"
msgstr "Zredukuj ten baner"

#: inc/Engine/CDN/RocketCDN/views/cta-small.php:17
msgid "Speed up your website with RocketCDN, WP Rocket’s Content Delivery Network."
msgstr "Przyspiesz swoją stronę internetową dzięki RocketCDN, dzięki sieci dostarczania treści WP Rocket."

#: inc/Engine/CDN/RocketCDN/views/cta-small.php:20
#: inc/Engine/CDN/RocketCDN/views/promote-notice.php:13
msgid "Learn More"
msgstr "Dowiedz się więcej"

#: inc/Engine/CDN/RocketCDN/views/dashboard-status.php:23
msgid "RocketCDN is unavailable on local domains and staging sites."
msgstr "RocketCDN jest niedostępny na lokalnych domenach i stronach deweloperskich."

#: inc/Engine/CDN/RocketCDN/views/dashboard-status.php:32
msgid "Get RocketCDN"
msgstr "Zdobądź RocketCDN"

#: inc/Engine/CDN/RocketCDN/views/promote-notice.php:11
msgid "New!"
msgstr "Nowość!"

#: inc/Engine/CDN/RocketCDN/views/promote-notice.php:12
msgid "Speed up your website with RocketCDN, WP Rocket’s Content Delivery Network!"
msgstr "Przyspiesz swoją stronę internetową z pomocą RocketCDN, dzięki sieci dostarczania treści WP Rocket!"

#: inc/Engine/Cache/AdminSubscriber.php:118
#: inc/admin/admin.php:96
#: inc/admin/admin.php:118
#: inc/deprecated/3.5.php:898
msgid "Clear this cache"
msgstr "Wyczyść tą pamięć podręczną"

#: inc/Engine/Cache/PurgeExpired/Subscriber.php:75
msgid "WP Rocket Expired Cache Interval"
msgstr "Okres wygaśnięcia pamięci podręcznej WP Rocket"

#: inc/Engine/Cache/WPCache.php:337
msgid "WP_CACHE value"
msgstr "Wartość WP_CACHE"

#: inc/Engine/Cache/WPCache.php:358
msgid "The WP_CACHE constant needs to be set to true for WP Rocket cache to work properly"
msgstr "Stała WP_CACHE musi być ustawiona na wartość rzeczywistą, aby pamięć podręczna WP Rocket działała prawidłowo"

#: inc/Engine/Cache/WPCache.php:367
msgid "WP_CACHE is set to true"
msgstr "WP_CACHE jest ustawiony na true"

#: inc/Engine/Cache/WPCache.php:375
msgid "WP_CACHE is not set"
msgstr "WP_CACHE nie jest ustawiony"

#: inc/Engine/Cache/WPCache.php:383
msgid "WP_CACHE is set to false"
msgstr "WP_CACHE jest ustawiony na false"

#: inc/Engine/Common/Queue/RUCSSQueueRunner.php:253
msgid "Every minute"
msgstr "Co minutę"

#: inc/Engine/CriticalPath/APIClient.php:64
#, php-format
msgid "Critical CSS for %1$s not generated. Error: %2$s"
msgstr "Krytyczny CSS dla %1$s nie został wygenerowany. Błąd: %2$s"

#: inc/Engine/CriticalPath/APIClient.php:170
#, php-format
msgid "Critical CSS for %1$s on mobile not generated. Error: The API returned an empty response."
msgstr "Krytyczny CSS za %1$s na urządzeniach nie został wygenerowany. Błąd: API zwróciło pustą odpowiedź."

#: inc/Engine/CriticalPath/APIClient.php:173
#, php-format
msgid "Critical CSS for %1$s not generated. Error: The API returned an empty response."
msgstr "Krytyczny CSS dla %1$s nie został wygenerowany. Błąd: API zwróciło pustą odpowiedź."

#: inc/Engine/CriticalPath/APIClient.php:185
#, php-format
msgid "Critical CSS for %1$s on mobile not generated."
msgstr "Krytyczny CSS dla %1$s na urządzenia mobilne nie został wygenerowany."

#: inc/Engine/CriticalPath/APIClient.php:187
#: inc/Engine/CriticalPath/ProcessorService.php:194
#, php-format
msgid "Critical CSS for %1$s not generated."
msgstr "Krytyczny CSS dla %1$s nie został wygenerowany."

#: inc/Engine/CriticalPath/APIClient.php:195
#, php-format
msgid "Critical CSS for %1$s on mobile not generated. Error: The API returned an invalid response code."
msgstr "Krytyczny CSS za %1$s na urządzenia mobilne nie został wygenerowany. Błąd: API zwróciło nieprawidłowy kod odpowiedzi."

#: inc/Engine/CriticalPath/APIClient.php:197
#, php-format
msgid "Critical CSS for %1$s not generated. Error: The API returned an invalid response code."
msgstr "Krytyczny CSS dla %1$s nie został wygenerowany. Błąd: API zwróciło nieprawidłowy kod odpowiedzi."

#: inc/Engine/CriticalPath/APIClient.php:205
#: inc/Engine/CriticalPath/ProcessorService.php:201
#, php-format
msgid "Error: %1$s"
msgstr "Błąd: %1$s"

#: inc/Engine/CriticalPath/Admin/Admin.php:264
msgid "Regenerate Critical Path CSS"
msgstr "Regeneruj CSS w krytycznej ścieżce"

#: inc/Engine/CriticalPath/Admin/Post.php:144
#: views/cpcss/metabox/generate.php:47
msgid "Generate Specific CPCSS"
msgstr "Generuj specyficzny CPCSS"

#: inc/Engine/CriticalPath/Admin/Post.php:145
#: views/cpcss/metabox/generate.php:45
msgid "Regenerate specific CPCSS"
msgstr "Regeneruj określony CPCSS"

#: inc/Engine/CriticalPath/Admin/Post.php:216
msgid "This feature is not available for non-public post types."
msgstr "Ta funkcja nie jest dostępna dla niepublicznych typów wpisów."

#: inc/Engine/CriticalPath/Admin/Post.php:219
msgid "%l to use this feature."
msgstr "%l do korzystania z tej funkcji."

#: inc/Engine/CriticalPath/Admin/Post.php:222
#, php-format
msgid "Publish the %s"
msgstr "Opublikuj %s"

#: inc/Engine/CriticalPath/Admin/Post.php:223
msgid "Enable Load CSS asynchronously in WP Rocket settings"
msgstr "Włącz asynchroniczne ładowanie CSS w ustawieniach WP Rocket"

#: inc/Engine/CriticalPath/Admin/Post.php:224
msgid "Enable Load CSS asynchronously in the options above"
msgstr "Włącz opcję Wczytaj CSS asynchronicznie w opcjach powyżej"

#: inc/Engine/CriticalPath/CriticalCSSSubscriber.php:176
msgid "Critical CSS generation is currently running."
msgstr "Generowanie krytycznych CSS jest obecnie uruchomione."

#: inc/Engine/CriticalPath/CriticalCSSSubscriber.php:181
#, php-format
msgid "Go to the %1$sWP Rocket settings%2$s page to track progress."
msgstr "Przejdź do strony %1$sustawień WP Rocket%2$s, aby śledzić postęp."

#: inc/Engine/CriticalPath/CriticalCSSSubscriber.php:396
#, php-format
msgid "Critical CSS generation is currently running: %1$d of %2$d page types completed. (Refresh this page to view progress)"
msgstr "Obecnie trwa generowanie krytycznego CSS: %1$d z %2$d typów stron zostało zakończonych. (Odśwież tę stronę, aby zobaczyć postęp)"

#: inc/Engine/CriticalPath/CriticalCSSSubscriber.php:472
#, php-format
msgid "Critical CSS generation finished for %1$d of %2$d page types."
msgstr "Generowanie krytycznego CSS zakończone dla %1$d z %2$d typów stron."

#: inc/Engine/CriticalPath/CriticalCSSSubscriber.php:479
msgid "Critical CSS generation encountered one or more errors."
msgstr "Generowanie krytycznych CSS napotkało jeden lub więcej błędów."

#: inc/Engine/CriticalPath/CriticalCSSSubscriber.php:479
msgid "Learn more."
msgstr "Dowiedz się więcej."

#: inc/Engine/CriticalPath/DataManager.php:68
#, php-format
msgid "Critical CSS for %1$s on mobile not generated. Error: The destination folder could not be created."
msgstr "Krytyczny CSS dla %1$s dla urządzeń mobilnych nie został wygenerowany. Błąd: Folder docelowy nie mógł zostać utworzony."

#: inc/Engine/CriticalPath/DataManager.php:71
#, php-format
msgid "Critical CSS for %1$s not generated. Error: The destination folder could not be created."
msgstr "Krytyczny CSS dla %1$s nie został wygenerowany. Błąd: Folder docelowy nie mógł zostać utworzony."

#: inc/Engine/CriticalPath/DataManager.php:106
msgid "Critical CSS file for mobile does not exist"
msgstr "Nie istnieje krytyczny plik CSS dla urządzeń mobilnych"

#: inc/Engine/CriticalPath/DataManager.php:108
msgid "Critical CSS file does not exist"
msgstr "Nie istnieje krytyczny plik CSS"

#: inc/Engine/CriticalPath/DataManager.php:120
msgid "Critical CSS file for mobile cannot be deleted"
msgstr "Krytyczny plik CSS dla urządzeń mobilnych nie może zostać usunięty"

#: inc/Engine/CriticalPath/DataManager.php:122
msgid "Critical CSS file cannot be deleted"
msgstr "Krytyczny plik CSS nie może zostać usunięty"

#: inc/Engine/CriticalPath/ProcessorService.php:187
#, php-format
msgid "Mobile Critical CSS for %1$s not generated."
msgstr "Krytyczny CSS dla urządzeń mobilnych dla %1$s nie został wygenerowany."

#: inc/Engine/CriticalPath/ProcessorService.php:228
#, php-format
msgid "Critical CSS for %s in progress."
msgstr "Krytyczny CSS dla %s w toku."

#: inc/Engine/CriticalPath/ProcessorService.php:262
#, php-format
msgid "Mobile Critical CSS for %s generated."
msgstr "Krytyczny CSS dla urządzeń mobilnych dla %s został wygenerowany."

#: inc/Engine/CriticalPath/ProcessorService.php:273
#, php-format
msgid "Critical CSS for %s generated."
msgstr "Krytyczne CSS dla %s zostało wygenerowane."

#: inc/Engine/CriticalPath/ProcessorService.php:295
msgid "Critical CSS file deleted successfully."
msgstr "Krytyczny plik CSS został pomyślnie usunięty."

#: inc/Engine/CriticalPath/ProcessorService.php:317
#, php-format
msgid "Mobile Critical CSS for %1$s timeout. Please retry a little later."
msgstr "Krytyczny CSS dla urządzeń mobilnych dla %1$s przekroczył limit czasu. Proszę spróbować ponownie trochę później."

#: inc/Engine/CriticalPath/ProcessorService.php:330
#, php-format
msgid "Critical CSS for %1$s timeout. Please retry a little later."
msgstr "Krytyczny CSS %1$s przekroczył limit czasu. Proszę spróbować ponownie trochę później."

#: inc/Engine/CriticalPath/RESTWP.php:141
msgid "Mobile CPCSS generation not enabled."
msgstr "Generowanie CPCSS dla urządzeń mobilnych nie jest włączone."

#: inc/Engine/CriticalPath/RESTWPPost.php:36
#: inc/Engine/CriticalPath/RESTWPPost.php:69
msgid "Requested post does not exist."
msgstr "Żądany wpis nie istnieje."

#: inc/Engine/CriticalPath/RESTWPPost.php:46
msgid "Cannot generate CPCSS for unpublished post."
msgstr "Nie można wygenerować CPCSS dla nieopublikowanych postów."

#: inc/Engine/HealthCheck/HealthCheck.php:82
#: inc/deprecated/3.5.php:858
msgid "The following scheduled event failed to run. This may indicate the CRON system is not running properly, which can prevent some WP Rocket features from working as intended:"
msgid_plural "The following scheduled events failed to run. This may indicate the CRON system is not running properly, which can prevent some WP Rocket features from working as intended:"
msgstr[0] "Następujące zaplanowane zdarzenie zakończyło się niepowodzeniem. Może to wskazywać na to, że system CRON nie działa prawidłowo, co może uniemożliwić działanie niektórych funkcji WP Rocket zgodnie z przeznaczeniem:"
msgstr[1] "Następujące zaplanowane zdarzenia zakończyły się niepowodzeniem. Może to wskazywać na to, że system CRON nie działa prawidłowo, co może uniemożliwić działanie niektórych funkcji WP Rocket zgodnie z przeznaczeniem:"
msgstr[2] "Następujące zaplanowane zdarzenia zakończyły się niepowodzeniem. Może to wskazywać na to, że system CRON nie działa prawidłowo, co może uniemożliwić działanie niektórych funkcji WP Rocket zgodnie z przeznaczeniem:"
msgstr[3] "Następujące zaplanowane zdarzenia zakończyły się niepowodzeniem. Może to wskazywać na to, że system CRON nie działa prawidłowo, co może uniemożliwić działanie niektórych funkcji WP Rocket zgodnie z przeznaczeniem:"

#: inc/Engine/HealthCheck/HealthCheck.php:88
#: inc/deprecated/3.5.php:867
msgid "Please contact your host to check if CRON is working."
msgstr "Prosimy o kontakt z dostawcą hostingu w celu sprawdzenia, czy usługa CRON działa."

#: inc/Engine/HealthCheck/HealthCheck.php:142
msgid "Scheduled Cache Purge"
msgstr "Planowe oczyszczenie pamięci podręcznej"

#: inc/Engine/HealthCheck/HealthCheck.php:143
msgid "Scheduled Database Optimization"
msgstr "Planowa optymalizacja bazy danych"

#: inc/Engine/HealthCheck/HealthCheck.php:144
msgid "Database Optimization Process"
msgstr "Proces optymalizacji bazy danych"

#: inc/Engine/HealthCheck/HealthCheck.php:145
msgctxt "noun"
msgid "Preload"
msgstr "Wstępne ładowanie"

#: inc/Engine/HealthCheck/HealthCheck.php:146
msgid "Critical Path CSS Generation Process"
msgstr "Proces generowania krytycznej ścieżki CSS"

#: inc/Engine/License/Renewal.php:76
#, php-format
msgid "Renew before it is too late, you will only pay %1$s%2$s%3$s!"
msgstr "Odnów, zanim będzie za późno, bo zapłacisz %1$s%3$s%2$s."

#: inc/Engine/License/Renewal.php:85
#, php-format
msgid "Renew with a %1$s%2$s discount%3$s before it is too late, you will only pay %4$s%5$s%6$s!"
msgstr "Odnów z %1$s%2$s rabatem %3$s zanim będzie za późno, zapłacisz tylko %4$s%5$s%6$s!"

#: inc/Engine/License/Renewal.php:139
#, php-format
msgid "Renew your license for 1 year now at %1$s%3$s%2$s."
msgstr "Odnów swoją licencję na 1 rok już teraz pod adresem %1$s%3$s%2$s."

#: inc/Engine/License/Renewal.php:152
#, php-format
msgid "Renew your license for 1 year now and get %1$s%3$s OFF%2$s immediately: you will only pay %1$s%4$s%2$s!"
msgstr "Przedłuż swoją licencję na 1 rok już teraz i otrzymaj od razu %1$s%3$s zniżki%2$s: zapłacisz tylko %1$s%4$s%2$s!"

#: inc/Engine/License/Renewal.php:218
#, php-format
msgid "Renew before it is too late, you will pay %1$s%3$s%2$s."
msgstr "Odnów, zanim będzie za późno, bo zapłacisz %1$s%3$s%2$s."

#: inc/Engine/License/Renewal.php:227
#, php-format
msgid "Renew with a %1$s%2$s discount%3$s before it is too late, you will only pay %1$s%4$s%3$s!"
msgstr "Odnów z %1$s%2$s rabatem %3$s zanim będzie za późno, zapłacisz tylko %1$s%4$s%3$s!"

#: inc/Engine/License/Renewal.php:546
#, php-format
msgid "You need a valid license to continue using this feature. %1$sRenew now%2$s before losing access."
msgstr "Aby móc dalej korzystać z tej funkcji, trzeba mieć ważną licencję. %1$sProszę odnowić teraz%2$s przed utratą dostępu."

#: inc/Engine/License/Renewal.php:567
#, php-format
msgid "You need an active license to enable this option. %1$sRenew now%2$s."
msgstr "Aby włączyć tę opcję, musisz mieć aktywną licencję. %1$sOdnów teraz%2$s."

#: inc/Engine/License/Renewal.php:595
#, php-format
msgid "You need an active license to enable this option. %1$sMore info%2$s."
msgstr "Aby włączyć tę opcję, trzeba mieć aktywną licencję. %1$sWięcej informacji%2$s."

#: inc/Engine/License/Upgrade.php:252
#, php-format
msgid "Take advantage of %1$s to speed up more websites:%2$s get a %3$s%4$s off%5$s for %3$supgrading your license to Plus or Infinite!%5$s"
msgid_plural "Take advantage of %1$s to speed up more websites:%2$s get a %3$s%4$s off%5$s for %3$supgrading your license to Infinite!%5$s"
msgstr[0] "Skorzystaj z zalety %1$s, by przyspieszyć działanie większej liczby stron internetowych:%2$s otrzymaj %3$s%4$s zniżkę%5$s przy %3$sulepszeniu do licencji Plus lub Infinite!%5$s"
msgstr[1] "Skorzystaj z zalety %1$s, by przyspieszyć działanie większej liczby stron internetowych:%2$s otrzymaj %3$s%4$s zniżkę%5$s przy %3$sulepszeniu do licencji Infinite!%5$s"
msgstr[2] "Skorzystaj z zalety %1$s, by przyspieszyć działanie większej liczby stron internetowych:%2$s otrzymaj %3$s%4$s zniżkę%5$s przy %3$sulepszeniu do licencji Infinite!%5$s"
msgstr[3] "Skorzystaj z zalety %1$s, by przyspieszyć działanie większej liczby stron internetowych:%2$s otrzymaj %3$s%4$s zniżkę%5$s przy %3$sulepszeniu do licencji Infinite!%5$s"

#: inc/Engine/License/Upgrade.php:382
#: inc/Engine/License/Upgrade.php:405
msgid "Unlimited"
msgstr "Bez ograniczeń"

#: inc/Engine/License/views/promo-banner.php:16
#, php-format
msgid "%s off"
msgstr "%s zniżki"

#: inc/Engine/License/views/promo-banner.php:21
#, php-format
msgid "%s promotion is live!"
msgstr "%s promocja jest na aktywna!"

#: inc/Engine/License/views/promo-banner.php:27
msgid "Hurry Up! Deal ends in:"
msgstr "Pospieszcie się! Okazja się kończy za:"

#: inc/Engine/License/views/promo-banner.php:31
#: inc/Engine/License/views/renewal-soon-banner.php:14
msgid "Minutes"
msgstr "Minut(y)"

#: inc/Engine/License/views/promo-banner.php:32
#: inc/Engine/License/views/renewal-soon-banner.php:15
msgid "Seconds"
msgstr "Sekundy"

#: inc/Engine/License/views/promo-banner.php:34
#: inc/Engine/License/views/upgrade-section.php:11
msgid "Upgrade now"
msgstr "Uaktualnij teraz"

#: inc/Engine/License/views/promo-banner.php:36
#: inc/Engine/License/views/renewal-expired-banner-ocd-disabled.php:32
#: inc/Engine/License/views/renewal-expired-banner-ocd.php:42
#: inc/Engine/License/views/renewal-expired-banner.php:30
#: inc/admin/ui/notices.php:739
#: views/settings/page-sections/dashboard.php:46
msgid "Dismiss this notice"
msgstr "Odrzuć to powiadomienie"

#: inc/Engine/License/views/renewal-expired-banner-ocd-disabled.php:11
msgid "The Optimize CSS Delivery feature is disabled."
msgstr "Funkcja Optymalizacji dostarczania CSS jest wyłączona."

#: inc/Engine/License/views/renewal-expired-banner-ocd-disabled.php:15
msgid "You can no longer use the Remove Unused CSS or Load CSS asynchronously options."
msgstr "Nie można już używać opcji Usuń nieużywany CSS lub Załaduj CSS asynchronicznie."

#: inc/Engine/License/views/renewal-expired-banner-ocd-disabled.php:20
#, php-format
msgid "You need an %1$sactive license%2$s to keep optimizing your CSS delivery, which addresses a PageSpeed Insights recommendation and improves your page performance."
msgstr "Potrzebujesz %1$saktywnej licencji%2$s, aby kontynuować optymalizację dostarczania CSS, co odpowiada zaleceniom PageSpeed Insights i poprawia wydajność strony."

#: inc/Engine/License/views/renewal-expired-banner-ocd-disabled.php:29
#: inc/Engine/License/views/renewal-expired-banner-ocd.php:39
#: inc/Engine/License/views/renewal-expired-banner.php:27
#: inc/Engine/License/views/renewal-soon-banner.php:31
msgid "Renew now"
msgstr "Odnów teraz"

#: inc/Engine/License/views/renewal-expired-banner-ocd.php:11
msgid "You will soon lose access to some features"
msgstr "Wkrótce stracisz dostęp do niektórych funkcji"

#: inc/Engine/License/views/renewal-expired-banner-ocd.php:18
#, php-format
msgid "You need an %1$sactive license to continue optimizing your CSS delivery%2$s."
msgstr "Potrzebujesz %1$saktywnej licencji, aby kontynuować optymalizację dostarczania CSS%2$s."

#: inc/Engine/License/views/renewal-expired-banner-ocd.php:24
msgid "The Remove Unused CSS and Load CSS asynchronously features are great options to address the PageSpeed Insights recommendations and improve your website performance."
msgstr "Funkcje Usuń nieużywany CSS i Załaduj CSS asynchronicznie są świetnymi opcjami, które pozwalają na uwzględnienie zaleceń PageSpeed Insights i poprawę wydajności Twojej strony."

#: inc/Engine/License/views/renewal-expired-banner-ocd.php:29
#, php-format
msgid "They will be %1$sautomatically disabled on %3$s%2$s."
msgstr "Zostaną one %1$sautomatycznie wyłączone w dniu %3$s%2$s."

#: inc/Engine/License/views/renewal-expired-banner.php:11
msgid "Your WP Rocket license is expired!"
msgstr "Twoja licencja WP Rocket wygasła!"

#: inc/Engine/License/views/renewal-expired-banner.php:18
#, php-format
msgid "Your website could be much faster if it could take advantage of our %1$snew features and enhancements%2$s. 🚀"
msgstr "Twoja strona może działać znacznie szybciej, jeśli skorzysta z naszych %1$snowych funkcji i ulepszeń%2$s. 🚀"

#: inc/Engine/License/views/renewal-soon-banner.php:22
#, php-format
msgid "Your %1$sWP Rocket license is about to expire%2$s: you will soon lose access to product updates and support."
msgstr "Twoja %1$sLicencja WP Rocket wkrótce wygaśnie%2$s: wkrótce stracisz dostęp do aktualizacji produktu i pomocy technicznej."

#: inc/Engine/License/views/upgrade-popin.php:12
msgid "Speed Up More Websites"
msgstr "Przyspiesz więcej stron internetowych"

#: inc/Engine/License/views/upgrade-popin.php:19
#, php-format
msgid "You can use WP Rocket on more websites by upgrading your license. To upgrade, simply pay the %1$sprice difference%2$s between your current and new licenses, as shown below."
msgstr "Możesz korzystać z WP Rocket na wielu stronach internetowych, uaktualniając swoją licencję. Aby uaktualnić, po prostu zapłać %1$sróżnicę w cenie%2$s pomiędzy aktualną a nową licencją, jak pokazano poniżej."

#: inc/Engine/License/views/upgrade-popin.php:25
#, php-format
msgid "%1$sN.B.%2$s: Upgrading your license does not change your expiration date"
msgstr "%1$sN.B.%2$s: Aktualizacja twojej licencji nie zmienia daty wygaśnięcia"

#: inc/Engine/License/views/upgrade-popin.php:35
#, php-format
msgid "Save $%s"
msgstr "Zapisz $%s"

#: inc/Engine/License/views/upgrade-popin.php:48
#, php-format
msgid "%s websites"
msgstr "%s stron internetowych"

#: inc/Engine/License/views/upgrade-popin.php:54
#, php-format
msgid "Upgrade to %s"
msgstr "Aktualizuj %s"

#: inc/Engine/License/views/upgrade-section.php:11
msgid "You can use WP Rocket on more websites by upgrading your license (you will only pay the price difference between your current and new licenses)."
msgstr "Możesz korzystać z WP Rocket na większej liczbie stron internetowych, uaktualniając swoją licencję (płacisz tylko różnicę w cenie między aktualną a nową licencją)."

#: inc/Engine/Optimization/DynamicLists/DynamicLists.php:101
msgid "You need an active license to get the latest version of the lists from our server."
msgstr "Aby pobrać najnowszą wersję list z naszego serwera, musisz mieć aktywną licencję."

#: inc/Engine/Optimization/DynamicLists/DynamicLists.php:115
msgid "Could not get updated lists from server."
msgstr "Nie można pobrać z serwera zaktualizowanych list."

#: inc/Engine/Optimization/DynamicLists/DynamicLists.php:124
msgid "Lists are up to date."
msgstr "Listy są aktualne."

#: inc/Engine/Optimization/DynamicLists/DynamicLists.php:133
msgid "Could not update lists."
msgstr "Nie można zaktualizować list."

#: inc/Engine/Optimization/DynamicLists/DynamicLists.php:142
msgid "Lists are successfully updated."
msgstr "Listy zostały pomyślnie zaktualizowane."

#: inc/Engine/Optimization/RUCSS/Admin/Settings.php:131
#: views/settings/page-sections/dashboard.php:188
msgid "Clear Used CSS"
msgstr "Wyczyść używane CSS"

#: inc/Engine/Optimization/RUCSS/Admin/Settings.php:219
#, php-format
msgid "%1$s: Please wait %2$s seconds. The Remove Unused CSS service is processing your pages."
msgstr "%1$s: Proszę czekać %2$s sekund. Usługa Usuń nieużywany CSS przetwarza Twoje strony."

#: inc/Engine/Optimization/RUCSS/Admin/Settings.php:264
#, php-format
msgid "%1$s: The Used CSS of your homepage has been processed. WP Rocket will continue to generate Used CSS for up to %2$s URLs per %3$s second(s)."
msgstr "%1$s: Używany CSS Państwa strony głównej został przetworzony. WP Rocket będzie kontynuował generowanie Używanego CSS dla maksymalnie %2$s adresów URL w ciągu %3$s sekund."

#: inc/Engine/Optimization/RUCSS/Admin/Settings.php:273
#, php-format
msgid "We suggest enabling %1$sPreload%2$s for the fastest results."
msgstr "Zalecamy włączenie opcji %1$sWstępnego ładowania%2$s w celu uzyskania najszybszych wyników."

#: inc/Engine/Optimization/RUCSS/Admin/Settings.php:283
#, php-format
msgid "To learn more about the process check our %1$sdocumentation%2$s."
msgstr "Aby dowiedzieć się więcej o procesie, proszę zapoznać się z naszą %1$sdokumentacją%2$s."

#: inc/Engine/Optimization/RUCSS/Admin/Settings.php:481
msgid "We couldn't generate the used CSS because you're using a nulled version of WP Rocket. You need an active license to use the Remove Unused CSS feature and further improve your website's performance."
msgstr "Nie mogliśmy wygenerować użytego CSS, ponieważ używasz wersji nulled WP Rocket. Musisz posiadać aktywną licencję, aby móc korzystać z funkcji Usuń nieużywany CSS i dalej poprawiać wydajność swojej strony."

#: inc/Engine/Optimization/RUCSS/Admin/Settings.php:484
#, php-format
msgid "Click here to get a WP Rocket single license at %1$s off!"
msgstr "Kliknij tutaj, aby otrzymać pojedynczą licencję WP Rocket z %1$s zniżki!"

#: inc/Engine/Optimization/RUCSS/Admin/Settings.php:521
#, php-format
msgid "Could not create the %2$s table in the database which is necessary for the Remove Unused CSS feature to work. Please reach out to <a href=\"%3$s\">our support</a>."
msgstr "Nie można utworzyć tabeli %2$s w bazie danych, która jest niezbędna do działania funkcji Usuń nieużywany CSS. Skontaktuj się z <a href=\"%3$s\">naszym działem wsparcia</a>."

#: inc/Engine/Optimization/RUCSS/Admin/Subscriber.php:322
#, php-format
msgid "%1$s: Used CSS option is not enabled!"
msgstr "%1$s: Opcja Używanego CSS nie jest włączona!"

#: inc/Engine/Optimization/RUCSS/Admin/Subscriber.php:343
#, php-format
msgid "%1$s: Used CSS cache cleared!"
msgstr "%1$s: Pamięć podręczna używanego CSS została wyczyszczona!"

#: inc/Engine/Optimization/RUCSS/Controller/UsedCSS.php:776
msgid "Clear Used CSS of this URL"
msgstr "Wyczyść używane CSS dla tego adresu URL"

#: inc/Engine/Optimization/RUCSS/Cron/Subscriber.php:160
msgid "WP Rocket Remove Unused CSS pending jobs"
msgstr "Zadania oczekujące usuwania nieużywanego CSS"

#: inc/Engine/Plugin/UpdaterApiTools.php:32
#: inc/deprecated/3.3.php:761
#: inc/deprecated/3.3.php:770
#, php-format
msgid "An unexpected error occurred. Something may be wrong with WP-Rocket.me or this server&#8217;s configuration. If you continue to have problems, <a href=\"%s\">contact support</a>."
msgstr "Wystąpił nieoczekiwany błąd. Coś może być nie tak z WP-Rocket.me lub konfiguracją Twojego serwera. Jeśli nadal masz problemy, <a href=\"%s\">skontaktuj się z wsparciem technicznym</a>."

#: inc/Engine/Plugin/UpdaterSubscriber.php:472
#: inc/Engine/Plugin/UpdaterSubscriber.php:486
#: inc/classes/class-wp-rocket-requirements-check.php:203
#: inc/classes/class-wp-rocket-requirements-check.php:214
#: inc/deprecated/3.11.php:236
#: inc/deprecated/3.11.php:251
#, php-format
msgid "%s Update Rollback"
msgstr "%s Wycofania aktualizacji"

#: inc/Engine/Plugin/UpdaterSubscriber.php:509
#: inc/deprecated/3.11.php:279
#, php-format
msgid "%1$sReturn to WP Rocket%2$s or %3$sgo to Plugins page%2$s"
msgstr "%1$sWróć do WP Rocket%2$s lub %3$s przejdź do strony Wtyczki%2$s"

#: inc/Engine/Preload/Admin/Settings.php:57
#, php-format
msgid "%1$s: The preload service is now active. After the initial preload it will continue to cache all your pages whenever they are purged. No further action is needed."
msgstr "%1$s: Usługa wstępnego ładowania jest teraz aktywna. Po pierwszym załadowaniu wstępnym będzie on nadal buforował wszystkie Państwa strony, gdy tylko zostaną one oczyszczone. Nie ma potrzeby podejmowania dalszych działań."

#: inc/Engine/Preload/Cron/Subscriber.php:138
msgid "WP Rocket Preload pending jobs"
msgstr "Nadchodzące zadania wstępnego ładowania"

#: inc/ThirdParty/Hostings/Cloudways.php:82
#, php-format
msgid "Varnish auto-purge will be automatically enabled once Varnish is enabled on your %s server."
msgstr "Varnish auto-purge zostanie automatycznie włączony po włączeniu Varnish na Twoim serwerze %s."

#: inc/ThirdParty/Hostings/Kinsta.php:158
#, php-format
msgid "Your installation seems to be missing core Kinsta files managing Cache clearing, which will prevent your Kinsta installation and WP Rocket from working correctly. Please get in touch with Kinsta support through your %1$sMyKinsta%2$s account to resolve this issue."
msgstr "Wydaje się, że w Pana instalacji brakuje podstawowych plików Kinsta zarządzających czyszczeniem Cache, co uniemożliwia prawidłowe działanie instalacji Kinsta i WP Rocket. Proszę skontaktować się z obsługą Kinsta poprzez swoje konto %1$sMyKinsta%2$s, aby rozwiązać ten problem."

#: inc/ThirdParty/Plugins/CDN/Cloudflare.php:114
#, php-format
msgid "%1$s: Cloudflare's HTTP/2 Server Push is incompatible with the features of Remove Unused CSS and Combine CSS files. We strongly recommend disabling it."
msgstr "%1$s: Funkcja HTTP/2 Server Push firmy Cloudflare jest niekompatybilna z funkcjami usuwania nieużywanego CSS i łączenia plików CSS. Zdecydowanie zalecamy wyłączenie tej funkcji."

#: inc/ThirdParty/Plugins/ModPagespeed.php:102
#, php-format
msgid "<strong>%1$s</strong>: Mod PageSpeed is not compatible with this plugin and may cause unexpected results. %2$sMore Info%3$s"
msgstr "<strong>%1$s</strong>: Mod PageSpeed nie jest kompatybilny z tą wtyczką i może powodować nieoczekiwane rezultaty. %2$sWięcej informacji%3$s"

#: inc/ThirdParty/Plugins/Optimization/Autoptimize.php:76
#, php-format
msgid "%1$sWP Rocket: %2$sWe have detected that Autoptimize's JavaScript Aggregation feature is enabled. WP Rocket's Delay JavaScript Execution will not be applied to the file it creates. We suggest disabling %1$sJavaScript Aggregation%2$s to take full advantage of Delay JavaScript Execution."
msgstr "%1$sWP Rocket: %2$sWykryliśmy, że funkcja Agregacji JavaScript w Autoptimize jest włączona. Opóźnienie wykonania skryptu JavaScript w WP Rocket nie zostanie zastosowane do pliku, który tworzy. Sugerujemy wyłączenie %1$sJavaScript Aggregation%2$s, aby w pełni wykorzystać Delay JavaScript Execution."

#: inc/ThirdParty/Plugins/Optimization/Autoptimize.php:131
#, php-format
msgid "%1$sWP Rocket: %2$sWe have detected that Autoptimize's Aggregate Inline CSS feature is enabled. WP Rocket's Load CSS Asynchronously will not work correctly. We suggest disabling %1$sAggregate Inline CSS%2$s to take full advantage of Load CSS Asynchronously Execution."
msgstr "%1$sWP Rocket: %2$sWykryliśmy, że funkcja agregowania wewnętrznego CSS jest włączona. Asynchroniczne ładowanie CSS w WP Rocket nie będzie działać poprawnie. Sugerujemy wyłączenie %1$sagregowania wewnętrznego CSS%2$s aby w pełni wykorzystać asynchroniczne wykonanie ładowania CSS."

#: inc/ThirdParty/Plugins/Optimization/Ezoic.php:45
#, php-format
msgid "This plugin blocks WP Rocket's caching and optimizations. Deactivate it and use %1$sEzoic's nameserver integration%2$s instead."
msgstr "Ta wtyczka blokuje buforowanie i optymalizacje WP Rocket. Dezaktywuj ją i zamiast tego użyj %1$sEzoic's nameserver integration%2$s."

#: inc/ThirdParty/Plugins/Optimization/Hummingbird.php:78
#, php-format
msgctxt "Hummingbird notice"
msgid "Please deactivate the following %s option which conflicts with WP Rocket features:"
msgid_plural "Please deactivate the following %s options which conflict with WP Rocket features:"
msgstr[0] "Proszę wyłączyć %s opcję, konfliktującą z funkcjami WP Rocket:"
msgstr[1] "Proszę wyłączyć %s opcje, konfliktujące z funkcjami WP Rocket:"
msgstr[2] "Proszę wyłączyć %s opcji, konfliktujących z funkcjami WP Rocket:"
msgstr[3] "Proszę wyłączyć %s opcji, konfliktujących z funkcjami WP Rocket:"

#: inc/ThirdParty/Plugins/Optimization/Hummingbird.php:151
#, php-format
msgctxt "Hummingbird notice"
msgid "%1$s %2$sdisable emoji%3$s conflicts with WP Rockets %2$sdisable emoji%3$s"
msgstr "%1$s %2$swyłączenie emocji%3$s konfliktuje z %2$swyłączeniem emoji%3$s WP Rockets"

#: inc/ThirdParty/Plugins/Optimization/Hummingbird.php:187
#, php-format
msgctxt "Hummingbird notice"
msgid "%1$s %2$sGZIP compression%3$s conflicts with WP Rocket %2$sGZIP compression%3$s"
msgstr "%1$s %2$skompresja GZIP%3$s konfliktuje z %2$skompresją GZIP%3$s WP Rocket"

#: inc/ThirdParty/Plugins/Optimization/Hummingbird.php:223
#, php-format
msgctxt "Hummingbird notice"
msgid "%1$s %2$sbrowser caching%3$s conflicts with WP Rocket %2$sbrowser caching%3$s"
msgstr "%1$s %2$spamięć podręczna przeglądarki%3$s konfliktuje z %2$spamięcią podręczną przeglądarki%3$s WP Rocket"

#: inc/ThirdParty/Plugins/Optimization/Hummingbird.php:255
#, php-format
msgctxt "Hummingbird notice"
msgid "%1$s %2$spage caching%3$s conflicts with WP Rocket %2$spage caching%3$s"
msgstr "%1$s %2$spamięć podręczna stron%3$s konfliktuje z %2$spamięcią podręczną%3$s WP Rocket"

#: inc/ThirdParty/Plugins/Optimization/Hummingbird.php:289
#, php-format
msgctxt "Hummingbird notice"
msgid "%1$s %2$sasset optimization%3$s conflicts with WP Rocket %2$sfile optimization%3$s"
msgstr "%1$s %2$soptymalizacja zasobów%3$s konfliktuje z %2$soptymalizacją plików%3$s WP Rocket"

#: inc/ThirdParty/Plugins/Optimization/WPMeteor.php:42
#, php-format
msgid "Delay JS is currently activated in %1$s. If you want to use WP Rocket’s delay JS, disable %1$s"
msgstr "Opóźnienie JS jest obecnie włączone w %1$s. Jeśli chcesz używać opóźnionego ładowania WP Rocket, wyłącz %1$s"

#: inc/ThirdParty/Plugins/Smush.php:108
#: inc/ThirdParty/Plugins/Smush.php:126
msgid "Smush"
msgstr "Smush"

#: inc/ThirdParty/Themes/Avada.php:116
msgid "Avada"
msgstr "Avada"

#: inc/admin/admin.php:18
#: inc/common/admin-bar.php:255
#: inc/deprecated/deprecated.php:1787
msgid "Support"
msgstr "Wsparcie techniczne"

#: inc/admin/admin.php:20
msgid "Docs"
msgstr "Dokumentacja"

#: inc/admin/admin.php:22
#: inc/common/admin-bar.php:243
msgid "FAQ"
msgstr "FAQ"

#: inc/admin/admin.php:24
#: inc/common/admin-bar.php:63
msgid "Settings"
msgstr "Ustawienia"

#: inc/admin/admin.php:458
msgid "Settings import failed: you do not have the permissions to do this."
msgstr "Importowanie ustawień nie powiodło się: nie masz uprawnień, aby to zrobić."

#: inc/admin/admin.php:462
msgid "Settings import failed: no file uploaded."
msgstr "Import ustawień nie powiódł się: nie został przesłany żaden plik."

#: inc/admin/admin.php:466
msgid "Settings import failed: incorrect filename."
msgstr "Import ustawień nie powiódł się: nieprawidłowa nazwa pliku."

#: inc/admin/admin.php:477
msgid "Settings import failed: incorrect filetype."
msgstr "Import ustawień nie powiódł się: nieprawidłowy typ pliku."

#: inc/admin/admin.php:487
msgid "Settings import failed: "
msgstr "Import ustawień nie powiódł się: "

#: inc/admin/admin.php:503
msgid "Settings import failed: unexpected file content."
msgstr "Import ustawień nie powiódł się: nieoczekiwana zawartość pliku."

#: inc/admin/admin.php:533
msgid "Settings imported and saved."
msgstr "Ustawienia zostały zaimportowane i zapisane."

#: inc/admin/options.php:127
msgid "Defer JavaScript Files"
msgstr "Odroczenie plików JavaScript"

#: inc/admin/options.php:128
msgid "Excluded Delay JavaScript Files"
msgstr "Wykluczenia opóźnionego ładowania JavaScript"

#: inc/admin/options.php:150
#, php-format
msgid "%1$s: <em>%2$s</em>"
msgstr "%1$s<em>%2$s</em>"

#: inc/admin/options.php:160
msgid "The following pattern is invalid and has been removed:"
msgid_plural "The following patterns are invalid and have been removed:"
msgstr[0] "Poniższy wzór jest nieprawidłowy i został usunięty:"
msgstr[1] "Poniższe wzory są nieprawidłowe i zostały usunięte:"
msgstr[2] "Poniższe wzory są nieprawidłowe i zostały usunięte:"
msgstr[3] "Poniższe wzory są nieprawidłowe i zostały usunięte:"

#: inc/admin/options.php:176
msgid "More info"
msgstr "Więcej informacji"

#: inc/admin/ui/meta-boxes.php:37
#: inc/admin/ui/notices.php:748
#: inc/common/admin-bar.php:84
#: inc/common/admin-bar.php:136
#: views/settings/page-sections/dashboard.php:150
msgid "Clear cache"
msgstr "Wyczyść pamięć podręczną"

#: inc/admin/ui/meta-boxes.php:67
msgid "WP Rocket Options"
msgstr "Opcje WP Rocket"

#: inc/admin/ui/meta-boxes.php:96
msgid "Never cache this page"
msgstr "Nigdy nie przechowuj w pamięci podręcznej tej strony"

#: inc/admin/ui/meta-boxes.php:100
msgid "Activate these options on this post:"
msgstr "Aktywuj te opcje dla tego wpisu:"

#: inc/admin/ui/meta-boxes.php:103
msgid "LazyLoad for images"
msgstr "LazyLoad dla obrazów"

#: inc/admin/ui/meta-boxes.php:104
msgid "LazyLoad for iframes/videos"
msgstr "LazyLoad dla iframe/wideo"

#: inc/admin/ui/meta-boxes.php:105
msgid "Minify CSS"
msgstr "Minifikuj CSS"

#: inc/admin/ui/meta-boxes.php:107
msgid "Minify/combine JS"
msgstr "Minifikuj/łącz JS"

#: inc/admin/ui/meta-boxes.php:110
msgid "Defer JS"
msgstr "Odroczony JS"

#: inc/admin/ui/meta-boxes.php:117
#, php-format
msgid "Activate first the %s option."
msgstr "Aktywuj najpierw opcję %s."

#: inc/admin/ui/meta-boxes.php:133
#, php-format
msgid "%1$sNote:%2$s None of these options will be applied if this post has been excluded from cache in the global cache settings."
msgstr "%1$sUwaga:%2$s Żadna z tych opcji nie zostanie zastosowana, jeśli post ten został wykluczony z pamięci podręcznej w ustawieniach globalnych pamięci podręcznej."

#: inc/admin/ui/notices.php:31
#: inc/admin/ui/notices.php:44
#, php-format
msgid ""
"<strong>%1$s</strong> has not been deactivated due to missing writing permissions.<br>\n"
"Make <strong>%2$s</strong> writeable and retry deactivation, or force deactivation now:"
msgstr ""
"<strong>%1$s</strong> nie został wyłączony z powodu braku uprawnień do zapisu.<br>\n"
"Dodaj do <strong>%2$s</strong> możliwość zapisu i ponów dezaktywację lub wymuś dezaktywację teraz:"

#: inc/admin/ui/notices.php:97
#, php-format
msgid "<strong>%s</strong>: One or more plugins have been enabled or disabled, clear the cache if they affect the front end of your site."
msgstr "<strong>%s</strong>: Jedna lub więcej wtyczek zostało włączonych lub wyłączonych, wyczyścić pamięć podręczną, jeśli sądzisz, że może mieć to wpływ na frontend witryny."

#: inc/admin/ui/notices.php:218
#, php-format
msgid "<strong>%s</strong>: The following plugins are not compatible with this plugin and may cause unexpected results:"
msgstr "<strong>%s</strong>: Następujące wtyczki nie są kompatybilne z tą wtyczką i mogą powodować nieoczekiwane rezultaty:"

#: inc/admin/ui/notices.php:224
msgid "Deactivate"
msgstr "Dezaktywuj"

#: inc/admin/ui/notices.php:266
msgid "WP Rocket Footer JS is not an official add-on. It prevents some options in WP Rocket from working correctly. Please deactivate it if you have problems."
msgstr "WP Rocket Footer JS nie jest oficjalnym dodatkiem. Wpływa on negatywnie na prawidłowe działanie niektórych opcji w WP Rocket. Jeśli masz problemy, wyłącz go."

#: inc/admin/ui/notices.php:306
#, php-format
msgid "Endurance Cache is currently enabled, which will conflict with WP Rocket Cache. Please set the Endurance Cache cache level to Off (Level 0) on the %1$sSettings > General%2$s page to prevent any issues."
msgstr "Endurance Cache jest obecnie włączony, co spowoduje konflikt z WP Rocket Cache. Proszę ustawić poziom pamięci podręcznej Endurance Cache na Off (Level 0) na stronie %1$sUstawienia > Ogólne%2$s, aby zapobiec potencjalnym problemom."

#: inc/admin/ui/notices.php:327
#, php-format
msgid "%1$s: A custom permalink structure is required for the plugin to work properly. %2$sGo to permalinks settings%3$s"
msgstr "%1$s: Aby wtyczka działała poprawnie, wymagana jest niestandardowa struktura bezpośrednich odnośników. %2$sPrzejdź do ustawień bezpośrednich odnośników%3$s"

#: inc/admin/ui/notices.php:374
#, php-format
msgid "%s could not modify the .htaccess file due to missing writing permissions."
msgstr "%s nie mógł zmienić pliku .htaccess z powodu braku uprawnień do zapisu."

#: inc/admin/ui/notices.php:380
#: inc/admin/ui/notices.php:843
#, php-format
msgid "Troubleshoot: %1$sHow to make system files writeable%2$s"
msgstr "Rozwiązywanie problemów: %1$sJak sprawić, aby pliki systemowe mogły być zapisywane%2$s"

#: inc/admin/ui/notices.php:382
#: inc/admin/ui/notices.php:845
msgid "https://docs.wp-rocket.me/article/626-how-to-make-system-files-htaccess-wp-config-writeable/?utm_source=wp_plugin&utm_medium=wp_rocket"
msgstr "https://docs.wp-rocket.me/article/626-how-to-make-system-files-htaccess-wp-config-writeable/?utm_source=wp_plugin&utm_medium=wp_rocket"

#: inc/admin/ui/notices.php:388
msgid "Don’t worry, WP Rocket’s page caching and settings will still function correctly."
msgstr "Nie martw się, pamięć podręczna stron WP Rocket i ustawienia nadal będą działać poprawnie."

#: inc/admin/ui/notices.php:388
msgid "For optimal performance, adding the following lines into your .htaccess is recommended (not required):"
msgstr "W celu zapewnienia optymalnej wydajności zaleca się dodanie następujących linii do pliku .htaccess (nie jest to wymagane):"

#: inc/admin/ui/notices.php:535
#, php-format
msgid "%1$s is good to go! %2$sTest your load time%4$s, or visit your %3$ssettings%4$s."
msgstr "%1$s w gotowości! %2$sPrzetestuj swój czas ładowania%4$s lub odwiedź stronę %3$sustawień%4$s."

#: inc/admin/ui/notices.php:576
msgid "Would you allow WP Rocket to collect non-sensitive diagnostic data from this website?"
msgstr "Czy zezwoliłbyś WP Rocket na gromadzenie niewrażliwych danych diagnostycznych z tej witryny internetowej?"

#: inc/admin/ui/notices.php:577
msgid "This would help us to improve WP Rocket for you in the future."
msgstr "Pomoże nam to w przyszłości ulepszyć WP Rocket dla Ciebie."

#: inc/admin/ui/notices.php:583
msgid "What info will we collect?"
msgstr "Jakie informacje będziemy gromadzić?"

#: inc/admin/ui/notices.php:588
msgid "Below is a detailed view of all data WP Rocket will collect if granted permission. WP Rocket will never transmit any domain names or email addresses (except for license validation), IP addresses, or third-party API keys."
msgstr "Poniżej znajduje się szczegółowy widok wszystkich danych gromadzonych przez WP Rocket po uzyskaniu zezwolenia. WP Rocket nigdy nie będzie transmitował nazw domen ani adresów e-mail (z wyjątkiem weryfikacji licencji), adresów IP lub kluczy API osób trzecich."

#: inc/admin/ui/notices.php:597
msgid "Yes, allow"
msgstr "Tak, zezwalam"

#: inc/admin/ui/notices.php:600
msgid "No, thanks"
msgstr "Nie, dziękuję"

#: inc/admin/ui/notices.php:639
msgid "Thank you!"
msgstr "Dziękuje!"

#: inc/admin/ui/notices.php:644
msgid "WP Rocket now collects these metrics from your website:"
msgstr "WP Rocket zbiera teraz te wskaźniki z Twojej witryny internetowej:"

#: inc/admin/ui/notices.php:682
#, php-format
msgid "%s: Cache cleared."
msgstr "%s: Oczyszczono pamięć podręczną."

#: inc/admin/ui/notices.php:689
#, php-format
msgid "%s: Post cache cleared."
msgstr "%s: Oczyszczono pamięć podręczną wpisów."

#: inc/admin/ui/notices.php:696
#, php-format
msgid "%s: Term cache cleared."
msgstr "%s: Oczyszczono pamięć podręczną wyrażeń taksonomii."

#: inc/admin/ui/notices.php:703
#, php-format
msgid "%s: User cache cleared."
msgstr "%s: Oczyszczono pamięć podręczną użytkowników."

#: inc/admin/ui/notices.php:751
msgid "Stop Preload"
msgstr "Zatrzymaj wstępne ładowanie"

#: inc/admin/ui/notices.php:781
msgid "Force deactivation "
msgstr "Wymuś dezaktywację "

#: inc/admin/ui/notices.php:800
msgid "The following code should have been written to this file:"
msgstr "Do tego pliku należy wpisać następujący kod:"

#: inc/admin/ui/notices.php:831
#, php-format
msgid "%s cannot configure itself due to missing writing permissions."
msgstr "%s nie może się skonfigurować z powodu braku uprawnień do zapisu."

#: inc/admin/ui/notices.php:837
#, php-format
msgid "Affected file/folder: %s"
msgstr "Plik/folder, którego dotyczy problem: %s"

#: inc/classes/admin/class-logs.php:124
msgid "The debug file could not be deleted."
msgstr "Plik debugowania nie mógł zostać usunięty."

#: inc/classes/class-wp-rocket-requirements-check.php:147
#, php-format
msgid "To function properly, %1$s %2$s requires at least:"
msgstr "Aby poprawnie działać, %1$s %2$s wymaga co najmniej:"

#: inc/classes/class-wp-rocket-requirements-check.php:151
#, php-format
msgid "PHP %1$s. To use this WP Rocket version, please ask your web host how to upgrade your server to PHP %1$s or higher."
msgstr "PHP %1$s. Aby użyć tej wersji WP Rocket, zapytaj swojego dostawcy hostingu jak uaktualnić serwer do PHP %1$s lub nowszej."

#: inc/classes/class-wp-rocket-requirements-check.php:156
#, php-format
msgid "WordPress %1$s. To use this WP Rocket version, please upgrade WordPress to version %1$s or higher."
msgstr "WordPress %1$s. Aby użyć tej wersji WP Rocket, należy uaktualnić WordPress do wersji %1$s lub nowszej."

#: inc/classes/class-wp-rocket-requirements-check.php:159
msgid "If you are not able to upgrade, you can rollback to the previous version by using the button below."
msgstr "Jeśli nie możesz dokonać aktualizacji, możesz wrócić do poprzedniej wersji, korzystając z przycisku poniżej."

#: inc/classes/class-wp-rocket-requirements-check.php:161
#: inc/deprecated/deprecated.php:1947
#, php-format
msgid "Re-install version %s"
msgstr "Ponowna instalacja wersji %s"

#: inc/classes/dependencies/wp-media/background-processing/wp-background-process.php:447
#, php-format
msgid "Every %d Minutes"
msgstr "Co %d minut"

#: inc/classes/logger/class-logger.php:260
#: inc/classes/logger/class-logger.php:292
msgid "The log file does not exist."
msgstr "Plik dziennika nie istnieje."

#: inc/classes/logger/class-logger.php:266
#: inc/classes/logger/class-logger.php:298
msgid "The log file could not be read."
msgstr "Plik dziennika nie mógł być odczytany."

#: inc/classes/logger/class-logger.php:285
msgid "The logs are not saved into a file."
msgstr "Dzienniki nie są zapisywane w pliku."

#: inc/Addon/WebP/AdminSubscriber.php:93
#, php-format
msgid "You are using %1$s to serve WebP images so you do not need to enable this option. %2$sMore info%3$s %4$s If you prefer to have WP Rocket serve WebP for you instead, please disable WebP display in %1$s."
msgid_plural "You are using %1$s to serve WebP images so you do not need to enable this option. %2$sMore info%3$s %4$s If you prefer to have WP Rocket serve WebP for you instead, please disable WebP display in %1$s."
msgstr[0] "Używasz %1$s do serwowania obrazów WebP, więc nie musisz włączać tej opcji. %2$sWięcej informacji%3$s %4$s Jeśli wolisz, aby WP Rocket obsługiwał WebP dla Ciebie zamiast tego, wyłącz wyświetlanie WebP w %1$s."
msgstr[1] "Używasz %1$s do serwowania obrazów WebP, więc nie musisz włączać tej opcji. %2$sWięcej informacji%3$s %4$s Jeśli wolisz, aby WP Rocket obsługiwał WebP dla Ciebie zamiast tego, wyłącz wyświetlanie WebP w %1$s."
msgstr[2] "Używasz %1$s do serwowania obrazów WebP, więc nie musisz włączać tej opcji. %2$sWięcej informacji%3$s %4$s Jeśli wolisz, aby WP Rocket obsługiwał WebP dla Ciebie zamiast tego, wyłącz wyświetlanie WebP w %1$s."
msgstr[3] "Używasz %1$s do serwowania obrazów WebP, więc nie musisz włączać tej opcji. %2$sWięcej informacji%3$s %4$s Jeśli wolisz, aby WP Rocket obsługiwał WebP dla Ciebie zamiast tego, wyłącz wyświetlanie WebP w %1$s."

#: inc/Addon/WebP/AdminSubscriber.php:105
msgid "WebP cache is disabled by filter."
msgstr "Pamięć podręczna WebP jest wyłączona przez filtr."

#: inc/Addon/WebP/AdminSubscriber.php:115
#: inc/Addon/WebP/AdminSubscriber.php:141
#, php-format
msgid "You are using %1$s to convert images to WebP. If you want WP Rocket to serve them for you, activate this option. %2$sMore info%3$s"
msgid_plural "You are using %1$s to convert images to WebP. If you want WP Rocket to serve them for you, activate this option. %2$sMore info%3$s"
msgstr[0] "Używasz %1$s do konwersji obrazów do WebP. Jeśli chcesz, aby WP Rocket obsługiwał WebP, aktywuj tę opcję. %2$sWięcej informacji%3$s"
msgstr[1] "Używasz %1$s do konwersji obrazów do WebP. Jeśli chcesz, aby WP Rocket obsługiwał WebP, aktywuj tę opcję. %2$sWięcej informacji%3$s"
msgstr[2] "Używasz %1$s do konwersji obrazów do WebP. Jeśli chcesz, aby WP Rocket obsługiwał WebP, aktywuj tę opcję. %2$sWięcej informacji%3$s"
msgstr[3] "Używasz %1$s do konwersji obrazów do WebP. Jeśli chcesz, aby WP Rocket obsługiwał WebP, aktywuj tę opcję. %2$sWięcej informacji%3$s"

#: inc/Addon/WebP/AdminSubscriber.php:127
#: inc/Addon/WebP/AdminSubscriber.php:153
#, php-format
msgid "You are using %1$s to convert images to WebP. WP Rocket will create separate cache files to serve your WebP images. %2$sMore info%3$s"
msgid_plural "You are using %1$s to convert images to WebP. WP Rocket will create separate cache files to serve your WebP images. %2$sMore info%3$s"
msgstr[0] "Używasz %1$s do konwersji obrazów do WebP. WP Rocket utworzy osobne pliki pamięci podręcznej do obsługi obrazów WebP. %2$sWięcej informacji%3$s"
msgstr[1] "Używasz %1$s do konwersji obrazów do WebP. WP Rocket utworzy osobne pliki pamięci podręcznej do obsługi obrazów WebP. %2$sWięcej informacji%3$s"
msgstr[2] "Używasz %1$s do konwersji obrazów do WebP. WP Rocket utworzy osobne pliki pamięci podręcznej do obsługi obrazów WebP. %2$sWięcej informacji%3$s"
msgstr[3] "Używasz %1$s do konwersji obrazów do WebP. WP Rocket utworzy osobne pliki pamięci podręcznej do obsługi obrazów WebP. %2$sWięcej informacji%3$s"

#: inc/Addon/WebP/AdminSubscriber.php:173
#, php-format
msgid "%5$sWe have not detected any compatible WebP plugin!%6$s%4$s If you don’t already have WebP images on your site consider using %3$sImagify%2$s or another supported plugin. %1$sMore info%2$s %4$s If you are not using WebP do not enable this option."
msgstr "%5$sNie wykryliśmy żadnego kompatybilnego pluginu do WebP!%6$s%4$s Jeśli nie masz jeszcze obrazów WebP na swojej stronie rozważ użycie %3$sImagify%2$s lub innego wspieranego pluginu. %1$sWięcej informacji%2$s %4$s Jeśli nie używasz WebP nie włączaj tej opcji."

#: inc/Addon/WebP/AdminSubscriber.php:185
msgid "WP Rocket will create separate cache files to serve your WebP images."
msgstr "WP Rocket utworzy osobne pliki pamięci podręcznej dla obsługi obrazów WebP."

#: inc/classes/subscriber/Tools/class-detect-missing-tags-subscriber.php:151
#, php-format
msgid "Failed to detect the following requirement in your theme: closing %1$s."
msgid_plural "Failed to detect the following requirements in your theme: closing %1$s."
msgstr[0] "Nie udało się wykryć wymaganej zależności w motywie: zamknięcie %1$s."
msgstr[1] "Nie udało się wykryć wymaganych zależności w motywie: zamknięcie %1$s."
msgstr[2] "Nie udało się wykryć wymaganych zależności w motywie: zamknięcie %1$s."
msgstr[3] "Nie udało się wykryć wymaganych zależności w motywie: zamknięcie %1$s."

#: inc/classes/subscriber/Tools/class-detect-missing-tags-subscriber.php:159
msgid "https://docs.wp-rocket.me/article/99-pages-not-cached-or-minify-cssjs-not-working/?utm_source=wp_plugin&utm_medium=wp_rocket#theme"
msgstr "https://docs.wp-rocket.me/article/99-pages-not-cached-or-minify-cssjs-not-working/?utm_source=wp_plugin&utm_medium=wp_rocket#theme"

#: inc/common/admin-bar.php:84
#: inc/common/admin-bar.php:136
#: views/settings/page-sections/dashboard.php:150
msgid "Clear and preload cache"
msgstr "Czyszczenie i wstępne ładowanie pamięci podręcznej"

#: inc/common/admin-bar.php:124
#: inc/functions/i18n.php:41
#: inc/functions/i18n.php:51
msgid "All languages"
msgstr "Wszystkie języki"

#: inc/common/admin-bar.php:160
msgid "Clear this post"
msgstr "Wyczyść ten wpis"

#: inc/common/admin-bar.php:174
msgid "Purge this URL"
msgstr "Oczyścić z tego adresu"

#: inc/common/admin-bar.php:194
msgid "Purge Sucuri cache"
msgstr "Oczyścić pamięć podręczną Sucuri"

#: inc/common/admin-bar.php:218
#: views/settings/fields/rocket-cdn.php:55
msgid "Purge RocketCDN cache"
msgstr "Oczyść pamięć podręczną RocketCDN"

#: inc/common/admin-bar.php:231
#: views/settings/partials/documentation.php:14
msgid "Documentation"
msgstr "Dokumentacja"

#: inc/deprecated/3.10.php:163
msgid "OPcache purge failed."
msgstr "Oczyszczanie OPcache nie powiodło się."

#: inc/deprecated/3.10.php:168
msgid "OPcache successfully purged"
msgstr "OPcache został skutecznie oczyszczony"

#: inc/deprecated/3.11.php:81
msgid "Yoast SEO XML sitemap"
msgstr "Mapa witryny XML wtyczki Yoast SEO"

#: inc/deprecated/3.11.php:83
#: inc/deprecated/3.12.php:58
#: inc/deprecated/3.12.php:87
#: inc/deprecated/3.12.php:186
#: inc/deprecated/3.12.php:253
#: inc/deprecated/3.12.php:342
#, php-format
msgid "We automatically detected the sitemap generated by the %s plugin. You can check the option to preload it."
msgstr "Wykryto automatycznie mapę witryny wygenerowanę przez wtyczkę %s. Możesz zaznaczyć opcję, aby ją wstępnie wczytać metodą wstępnego ładowania."

#: inc/deprecated/3.12.php:56
msgid "All in One SEO XML sitemap"
msgstr "Mapa witryny XML wtyczki All in One SEO"

#: inc/deprecated/3.12.php:85
msgid "Rank Math XML sitemap"
msgstr "Mapa witryny XML Rank Math"

#: inc/deprecated/3.12.php:184
msgid "SEOPress XML sitemap"
msgstr "Mapa witryny XML SEOPress"

#: inc/deprecated/3.12.php:251
msgid "The SEO Framework XML sitemap"
msgstr "Mapa witryny XML SEO Framework"

#: inc/deprecated/3.12.php:332
msgid "Jetpack XML Sitemaps"
msgstr "Mapa witryny XML wtyczki Jetpack"

#: inc/deprecated/3.12.php:334
msgid "Preload the sitemap from the Jetpack plugin"
msgstr "Załaduj wstępnie mapę witryny z wtyczki Jetpack"

#: inc/deprecated/3.2.php:52
#: views/settings/page-sections/imagify.php:36
msgid "Activate Imagify"
msgstr "Aktywuj Imagify"

#: inc/deprecated/3.2.php:52
msgid "Install Imagify for Free"
msgstr "Zainstaluj \"Imagify\" za darmo"

#: inc/deprecated/3.2.php:67
msgid "Speed up your website and boost your SEO by reducing image file sizes without losing quality with Imagify."
msgstr "Przyspiesz swoją witrynę i popraw SEO poprzez zmniejszenie rozmiaru plików graficznych bez utraty jakości z Imagify."

#: inc/deprecated/3.2.php:72
msgid "More details"
msgstr "Więcej szczegółów"

#: inc/deprecated/3.2.php:228
#, php-format
msgid "Sitemap preload: %d pages have been cached."
msgstr "Ładowanie wstępne oparty o mapę witryny: %d stron(-y) zostało zachowanych do pamięci podręcznej."

#: inc/deprecated/3.2.php:261
#, php-format
msgid "Sitemap preload: %d uncached pages have now been preloaded. (refresh to see progress)"
msgstr "Wstępne ładowanie oparte o mapę witryny: %d stron(-y), które nie zostały wczytane w pamięci podręcznej, zostały załadowane wstępnie. (Odśwież, aby zobaczyć postęp)"

#: inc/deprecated/3.4.php:18
msgid "Choose a domain from the list"
msgstr "Wybierz domenę z listy"

#: inc/deprecated/3.4.php:31
msgid "No domain available in your Cloudflare account"
msgstr "Brak domeny dostępnej na koncie Cloudflare"

#: inc/deprecated/3.5.php:71
#: inc/deprecated/3.5.php:195
msgid "Curl is disabled on your server. Please ask your host to enable it. This is required for the Cloudflare Add-on to work correctly."
msgstr "Curl się jest wyłączone na serwerze. Proszę poprosić obsługę hostingu, aby został aktywowany. Jest to wymagane, aby dodatek Cloudflare działał prawidłowo."

#: inc/deprecated/3.5.php:79
#, php-format
msgid "Cloudflare email, API key and Zone ID are not set. Read the %1$sdocumentation%2$s for further guidance."
msgstr "E-mail Cloudflare , klucz API i ID strefy nie są ustawione. Przeczytaj %1$sdokumentację%2$s w celu uzyskania dalszych wskazówek."

#: inc/deprecated/3.5.php:206
#, php-format
msgid "Cloudflare email and API key are not set. Read the %1$sdocumentation%2$s for further guidance."
msgstr "E-mail i klucz API Cloudflare nie są ustawione. Przeczytaj %1$sdokumentację%2$s w celu uzyskania dalszych wskazówek."

#: inc/deprecated/3.5.php:271
msgid "Connection to Cloudflare failed"
msgstr "Nie powiodło się połączenie z Cloudflare"

#: inc/deprecated/DeprecatedClassTrait.php:54
#, php-format
msgid "The called class %1$s is <strong>deprecated</strong> since version %2$s! Use %3$s instead."
msgstr "Tak zwana klasa %1$s jest <strong>przestarzała</strong> od wersji %2$s! Użyj zamiast tego %3$s."

#: inc/deprecated/DeprecatedClassTrait.php:65
#, php-format
msgid "The called class %1$s is <strong>deprecated</strong> since version %2$s!"
msgstr "Tak zwana klasa %1$s jest <strong>przestarzała</strong> od wersji %2$s!"

#: inc/deprecated/deprecated.php:240
msgid "<strong>JS</strong> files with Deferred Loading JavaScript"
msgstr "Pliki <strong>JS</strong> z odroczonym wczytywaniem JavaScript metodą defer"

#: inc/deprecated/deprecated.php:293
msgid "Add URL"
msgstr "Dodaj adres URL"

#: inc/deprecated/deprecated.php:1277
#: views/settings/fields/import-form.php:22
msgid "Before you can upload your import file, you will need to fix the following error:"
msgstr "Zanim będzie można przesłać plik importu, trzeba rozwiązać następujący błąd:"

#: inc/deprecated/deprecated.php:1288
#: views/settings/fields/import-form.php:35
#, php-format
msgid "Choose a file from your computer (maximum size: %s)"
msgstr "Wybierz plik z komputera (maksymalny rozmiar: %s)"

#: inc/deprecated/deprecated.php:1385
msgid "Your Cloudflare credentials are valid."
msgstr "Twoje uwierzytelnienie CloudFlare jest ważne."

#: inc/deprecated/deprecated.php:1389
msgid "Your Cloudflare credentials are invalid!"
msgstr "Twoje uwierzytelnienie CloudFlare jest nieważne!"

#: inc/deprecated/deprecated.php:1453
msgid "Save and optimize"
msgstr "Zapisz i optymalizuj"

#: inc/deprecated/deprecated.php:1453
msgid "Optimize"
msgstr "Optymalizuj"

#: inc/deprecated/deprecated.php:1464
#: inc/deprecated/deprecated.php:1666
msgctxt "screen-reader-text"
msgid "Note:"
msgstr "Uwaga:"

#: inc/deprecated/deprecated.php:1470
msgctxt "screen-reader-text"
msgid "Performance tip:"
msgstr "Wskazówka dotycząca wydajności:"

#: inc/deprecated/deprecated.php:1476
msgctxt "screen-reader-text"
msgid "Third-party feature detected:"
msgstr "Wykryto zewnętrzne funkcje:"

#: inc/deprecated/deprecated.php:1488
#: inc/deprecated/deprecated.php:1672
msgctxt "screen-reader-text"
msgid "Warning:"
msgstr "Ostrzeżenie:"

#: inc/deprecated/deprecated.php:1501
msgctxt "button text"
msgid "Download settings"
msgstr "Pobierz ustawienia"

#: inc/deprecated/deprecated.php:1531
msgid "Replace site's hostname with:"
msgstr "Zastąp nazwę hosta witryny nazwą:"

#: inc/deprecated/deprecated.php:1550
#: inc/deprecated/deprecated.php:1587
#: inc/deprecated/deprecated.php:1615
#: views/settings/fields/cnames.php:41
#: views/settings/fields/cnames.php:75
#: views/settings/fields/cnames.php:105
msgid "reserved for"
msgstr "zarezerwowane dla"

#: inc/deprecated/deprecated.php:1552
#: inc/deprecated/deprecated.php:1589
#: inc/deprecated/deprecated.php:1617
#: views/settings/fields/cnames.php:44
#: views/settings/fields/cnames.php:78
#: views/settings/fields/cnames.php:108
msgid "All files"
msgstr "Wszystkie pliki"

#: inc/deprecated/deprecated.php:1564
#: inc/deprecated/deprecated.php:1594
#: inc/deprecated/deprecated.php:1622
#: views/settings/fields/cnames.php:56
#: views/settings/fields/cnames.php:90
#: views/settings/fields/cnames.php:120
msgid "Images"
msgstr "Obrazy"

#: inc/deprecated/deprecated.php:1635
#: views/settings/fields/cnames.php:132
msgid "Add CNAME"
msgstr "Dodaj CNAME"

#: inc/deprecated/deprecated.php:1706
msgid "Watch the video"
msgstr "Obejrzyj wideo"

#: inc/deprecated/deprecated.php:1771
msgid "Basic"
msgstr "Podstawowe"

#: inc/deprecated/deprecated.php:1772
msgid "Static Files"
msgstr "Statyczne pliki"

#: inc/deprecated/deprecated.php:1774
msgid "Advanced"
msgstr "Zaawansowane"

#: inc/deprecated/deprecated.php:1944
#, php-format
msgid "%1$s %2$s requires at least PHP %3$s to function properly. To use this version, please ask your web host how to upgrade your server to PHP %3$s or higher. If you are not able to upgrade, you can rollback to the previous version by using the button below."
msgstr "%1$s %2$s wymaga co najmniej PHP %3$s do prawidłowego działania. Aby użyć tej wersji, zapytaj swojego dostawcę hostingu, jak uaktualnić serwer do PHP %3$s lub nowszej. Jeśli nie możesz dokonać aktualizacji, możesz wrócić do poprzedniej wersji, korzystając z przycisku poniżej."

#: inc/functions/admin.php:21
msgid "There seems to be an issue validating your license. Please see the error message below."
msgid_plural "There seems to be an issue validating your license. You can see the error messages below."
msgstr[0] "Wydaje się, że istnieje problem z walidacją licencji. Proszę zapoznać się z poniższym komunikatem o błędzie."
msgstr[1] "Wydaje się, że istnieje problem z walidacją licencji. Proszę zapoznać się z poniższymi komunikatami o błędzie."
msgstr[2] "Wydaje się, że istnieje problem z walidacją licencji. Proszę zapoznać się z poniższymi komunikatami o błędzie."
msgstr[3] "Wydaje się, że istnieją problemy z walidacją licencji. Proszę zapoznać się z poniższymi komunikatami o błędach."

#: inc/functions/admin.php:361
msgid "Server type:"
msgstr "Typ serwera:"

#: inc/functions/admin.php:370
msgid "PHP version number:"
msgstr "Numer wersji PHP:"

#: inc/functions/admin.php:379
msgid "WordPress version number:"
msgstr "Numer wersji WordPress:"

#: inc/functions/admin.php:388
msgid "WordPress multisite:"
msgstr "WordPress Multisite:"

#: inc/functions/admin.php:397
msgid "Current theme:"
msgstr "Bieżący motyw:"

#: inc/functions/admin.php:406
msgid "Current site language:"
msgstr "Aktualny język strony:"

#: inc/functions/admin.php:415
msgid "Active plugins:"
msgstr "Włączone wtyczki:"

#: inc/functions/admin.php:418
msgid "Plugin names of all active plugins"
msgstr "Nazwy wszystkich aktywnych wtyczek"

#: inc/functions/admin.php:424
msgid "Anonymized WP Rocket settings:"
msgstr "Anonimowe ustawienia WP Rocket:"

#: inc/functions/admin.php:427
msgid "Which WP Rocket settings are active"
msgstr "Które ustawienia WP Rocket są aktywne"

#: inc/functions/options.php:429
msgid "The provided license data are not valid."
msgstr "Podane dane licencyjne są nieprawidłowe."

#: inc/functions/options.php:432
#, php-format
msgid "To resolve, please %1$scontact support%2$s."
msgstr "Aby rozwiązać problem, należy %1$sskontaktować się z pomocą techniczną%2$s."

#: inc/functions/options.php:491
#: inc/functions/options.php:530
msgid "License validation failed. Our server could not resolve the request from your website."
msgstr "Walidacja licencji nie powiodła się. Nasz serwer nie był w stanie zrealizować żądania z Twojej witryny internetowej."

#: inc/functions/options.php:491
#, php-format
msgid "Try clicking %1$sValidate License%2$s below. If the error persists, follow %3$sthis guide%4$s."
msgstr "Proszę spróbować kliknąć %1$sWeryfikuj licencję%2$s poniżej. Jeżeli błąd nadal występuje, proszę postępować zgodnie z %3$stym przewodnikiem%4$s."

#: inc/functions/options.php:507
msgid "License validation failed. You may be using a nulled version of the plugin. Please do the following:"
msgstr "Walidacja licencji nie powiodła się. Możliwe jest, że używasz wersji nulled wtyczki. Proszę wykonać następujące czynności:"

#: inc/functions/options.php:507
#: inc/functions/options.php:549
#, php-format
msgid "Login to your WP Rocket %1$saccount%2$s"
msgstr "Zaloguj się do swojego %1$skonta WP Rocket%2$s"

#: inc/functions/options.php:507
#: inc/functions/options.php:549
msgid "Download the zip file"
msgstr "Pobierz plik zip"

#: inc/functions/options.php:507
#: inc/functions/options.php:549
msgid "Reinstall"
msgstr "Reinstalacja"

#: inc/functions/options.php:507
#, php-format
msgid "If you do not have a WP Rocket account, please %1$spurchase a license%2$s."
msgstr "Jeśli nie posiadasz konta WP Rocket, proszę %1$szakupić licencję%2$s."

#: inc/functions/options.php:515
msgid "License validation failed. This user account does not exist in our database."
msgstr "Weryfikacja licencji nie powiodła się. To konto użytkownika nie istnieje w naszej bazie danych."

#: inc/functions/options.php:515
msgid "To resolve, please contact support."
msgstr "Aby rozwiązać problem, prosimy o kontakt z działem pomocy technicznej."

#: inc/functions/options.php:523
msgid "License validation failed. This user account is blocked."
msgstr "Zatwierdzenie licencji nie powiodło się. To konto użytkownika jest zablokowane."

#: inc/functions/options.php:523
#, php-format
msgid "Please see %1$sthis guide%2$s for more info."
msgstr "Zobacz %1$sten poradnik%2$s, aby uzyskać więcej informacji."

#: inc/functions/options.php:530
#, php-format
msgid "Try clicking %1$sSave Changes%2$s below. If the error persists, follow %3$sthis guide%4$s."
msgstr "Spróbuj kliknąć %1$sZapisz zmiany%2$s poniżej. Jeśli błąd występuje nadal, postępuj zgodnie z %3$szaleceniami przewodnika%4$s."

#: inc/functions/options.php:543
msgid "Your license is not valid."
msgstr "Twoja licencja jest niepoprawna."

#: inc/functions/options.php:543
#, php-format
msgid "Make sure you have an active %1$sWP Rocket license%2$s."
msgstr "Upewnij się, że posiadasz aktywną licencję %1$sWP Rocket%2$s."

#: inc/functions/options.php:545
msgid "You have added as many sites as your current license allows."
msgstr "Dodałeś tyle witryn, na ile pozwala aktualna licencja."

#: inc/functions/options.php:545
#, php-format
msgid "Upgrade your %1$saccount%2$s or %3$stransfer your license%2$s to this domain."
msgstr "Ulepsz swoje %1$skonto%2$s lub %3$stransferuj licencję%2$s do tej domeny."

#: inc/functions/options.php:547
msgid "This website is not allowed."
msgstr "Ta witryna nie jest dozwolona."

#: inc/functions/options.php:547
#, php-format
msgid "Please %1$scontact support%2$s."
msgstr "Prosimy o %1$skontakt z działem pomocy technicznej%2$s."

#: inc/functions/options.php:549
msgid "This license key is not recognized."
msgstr "Ten klucz licencyjny nie został rozpoznany."

#: inc/functions/options.php:549
#, php-format
msgid "If the issue persists, please %1$scontact support%2$s."
msgstr "Jeśli problem występuje nadal się, prosimy o %1$skontakt z działem pomocy technicznej%2$s."

#: inc/functions/options.php:555
#, php-format
msgid "License validation failed: %s"
msgstr "Weryfikacja licencji nie powiodła się: %s"

#: inc/vendors/classes/class-imagify-partner.php:531
msgid "Plugin installed successfully."
msgstr "Wtyczka zainstalowana pomyślnie."

#: inc/vendors/classes/class-imagify-partner.php:532
msgid "Sorry, you are not allowed to install plugins on this site."
msgstr "Przepraszamy, nie posiadasz uprawnień do instalowania wtyczek w tej witrynie."

#: inc/vendors/classes/class-imagify-partner.php:533
msgid "Sorry, you are not allowed to do that."
msgstr "Przepraszamy, nie wolno Ci tego robić."

#: inc/vendors/classes/class-imagify-partner.php:534
msgid "Plugin install failed."
msgstr "Instalacja wtyczki nie powiodła się."

#: inc/vendors/classes/class-imagify-partner.php:535
msgid "Go back"
msgstr "Wróć"

#: views/cpcss/activate-cpcss-mobile.php:13
msgid "Load CSS asynchronously for mobile"
msgstr "Wczytaj CSS asynchronicznie w urządzeniach mobilnych"

#: views/cpcss/activate-cpcss-mobile.php:14
msgid "Your website currently uses the same Critical Path CSS for both desktop and mobile."
msgstr "Twoja strona korzysta obecnie z tej samej ścieżki krytycznej CSS zarówno dla urządzeń stacjonarnych jak i mobilnych."

#: views/cpcss/activate-cpcss-mobile.php:15
msgid "Click the button to enable mobile-specific CPCSS for your site."
msgstr "Kliknij przycisk, aby włączyć CPCSS specyficzny dla urządzeń przenośnych dla Twojej witryny."

#: views/cpcss/activate-cpcss-mobile.php:20
#: views/settings/enable-google-fonts.php:19
#, php-format
msgid "This is a one-time action and this button will be removed afterwards. %1$sMore info%2$s"
msgstr "Jest to jednorazowa czynność, a ten przycisk zostanie później usunięty. %1$s Więcej informacji%2$s"

#: views/cpcss/activate-cpcss-mobile.php:30
#, php-format
msgid "Your site is now using mobile-specific critical path CSS. %1$sMore info%2$s"
msgstr "Twoja strona używa teraz specyficznej dla urządzeń mobilnych ścieżki krytycznej CSS. %1$s Więcej informacji%2$s"

#: views/cpcss/activate-cpcss-mobile.php:39
msgid "Generate Mobile Specific CPCSS"
msgstr "Generowanie CPCSS specyficznego dla urządzeń przenośnych"

#: views/cpcss/metabox/container.php:19
msgid "Critical Path CSS"
msgstr "Ścieżka krytyczna CSS"

#: views/cpcss/metabox/generate.php:23
#, php-format
msgid "Generate specific Critical Path CSS for this post. %1$sMore info%2$s"
msgstr "Wygeneruj specyficzną ścieżkę krytyczną CSS dla tego wpisu. %1$s Więcej informacji%2$s"

#: views/cpcss/metabox/generate.php:33
#, php-format
msgid "This post uses specific Critical Path CSS. %1$sMore info%2$s"
msgstr "Ten wpis wykorzystuje specyficzną ścieżkę krytyczną CSS. %1$s Więcej informacji%2$s"

#: views/cpcss/metabox/generate.php:56
msgid "Revert back to the default CPCSS"
msgstr "Powrót do domyślnego CPCSS"

#: views/deactivation-intent/form.php:21
msgid "Facing an issue?"
msgstr "Masz problem?"

#: views/deactivation-intent/form.php:24
msgid "It is not always necessary to deactivate WP Rocket when facing any issues. Most of them can be fixed by deactivating only some options."
msgstr "Nie zawsze trzeba dezaktywować WP Rocket, gdy pojawiają się jakieś problemy. Większość z nich można naprawić, dezaktywując tylko niektóre opcje."

#: views/deactivation-intent/form.php:29
#, php-format
msgid "Our advice? Instead of deactivating WP Rocket, use our %1$sSafe Mode%2$s to quickly disable LazyLoad, File Optimization, and CDN options. Then check to see if your issue is resolved."
msgstr "Nasza rada? Zamiast dezaktywować WP Rocket, proszę użyć naszego %1$sTrybu bezpiecznego%2$s, aby szybko wyłączyć opcje LazyLoadingu, Optymalizacji plików i CDN. Następnie proszę sprawdzić, czy Twój problem został rozwiązany."

#: views/deactivation-intent/form.php:35
msgid "Do you want to use our Safe Mode to troubleshoot WP Rocket?"
msgstr "Czy chcesz skorzystać z naszego trybu bezpiecznego, aby rozwiązać problem z WP Rocket?"

#: views/deactivation-intent/form.php:55
#, php-format
msgid "Yes, apply \"%1$sSafe Mode%2$s\""
msgstr "Tak, zastosuj \"%1$s Tryb bezpieczny%2$s\""

#: views/deactivation-intent/form.php:68
#, php-format
msgid "and export WP Rocket settings %1$s(Recommended as current settings will be deleted)%2$s"
msgstr "i wyeksportuj ustawienia WP Rocket %1$s(zalecane, ponieważ bieżące ustawienia zostaną usunięte)%2$s"

#: views/deactivation-intent/form.php:76
msgid "No, deactivate and snooze this message for"
msgstr "Nie, dezaktywuj i wycisz tę wiadomość na"

#: views/deactivation-intent/form.php:78
msgid "1 day"
msgstr "1 dzień"

#: views/deactivation-intent/form.php:79
msgid "7 days"
msgstr "7 dni"

#: views/deactivation-intent/form.php:80
msgid "30 days"
msgstr "30 dni"

#: views/deactivation-intent/form.php:81
msgid "Forever"
msgstr "Na zawsze"

#: views/deactivation-intent/form.php:87
msgid "Cancel"
msgstr "Anuluj"

#: views/deactivation-intent/form.php:88
msgid "Confirm"
msgstr "Potwierdź"

#: views/settings/dynamic-lists-update.php:14
msgid "Update Inclusion and Exclusion Lists"
msgstr "Uaktualnij listy włączonych i wykluczonych"

#: views/settings/dynamic-lists-update.php:19
#, php-format
msgid "Compatibility lists are downloaded automatically every week. Click the button if you want to update them manually. %1$sMore info%2$s"
msgstr "Listy kompatybilności są pobierane automatycznie co tydzień. Kliknij przycisk, jeśli chcesz aktualizować je ręcznie. %1$sWięcej informacji%2$s"

#: views/settings/dynamic-lists-update.php:29
msgid "Update lists"
msgstr "Aktualizacja list"

#: views/settings/enable-google-fonts.php:13
msgid "Enable Google Font Optimization"
msgstr "Włącz optymalizację czcionek Google"

#: views/settings/enable-google-fonts.php:14
msgid "Improves font performance and combines multiple font requests to reduce the number of HTTP requests."
msgstr "Poprawia wydajność czcionek i łączy wiele żądań dotyczących czcionek, aby zmniejszyć liczbę żądań HTTP."

#: views/settings/enable-google-fonts.php:29
#, php-format
msgid "Google Fonts Optimization is now enabled for your site. %1$sMore info%2$s"
msgstr "Optymalizacja czcionek Google jest teraz włączona dla Twojej witryny. %1$s Więcej informacji%2$s"

#: views/settings/enable-google-fonts.php:38
msgid "Optimize Google Fonts"
msgstr "Optymalizacja czcionek Google"

#: views/settings/fields/cache-lifespan.php:26
msgid "Clear cache after"
msgstr "Wyczyść pamięć podręczną po"

#: views/settings/fields/cnames.php:58
#: views/settings/fields/cnames.php:92
#: views/settings/fields/cnames.php:122
msgid "CSS & JavaScript"
msgstr "CSS i JavaScript"

#: views/settings/fields/cnames.php:59
#: views/settings/fields/cnames.php:93
#: views/settings/fields/cnames.php:123
msgid "JavaScript"
msgstr "JavaScript"

#: views/settings/fields/cnames.php:60
#: views/settings/fields/cnames.php:94
#: views/settings/fields/cnames.php:124
msgid "CSS"
msgstr "CSS"

#: views/settings/fields/import-form.php:29
msgid "Import settings"
msgstr "Ustawienia importu"

#: views/settings/fields/one-click-addon.php:43
#: views/settings/fields/rocket-addon.php:43
msgid "Add-on status"
msgstr "Status dodatku"

#: views/settings/fields/rocket-addon.php:72
msgid "Modify options"
msgstr "Modyfikuj opcje"

#: views/settings/fields/rocket-cdn.php:29
#: views/settings/fields/rocket-cdn.php:41
msgid "CDN CNAME"
msgstr "CNAME CDN"

#: views/settings/fields/rocket-cdn.php:62
#, php-format
msgid "Purges RocketCDN cached resources for your website. %s"
msgstr "Czyści zasoby pamięci podręcznej RocketCDN dla Twojej witryny. %s"

#: views/settings/fields/rocket-cdn.php:63
#: views/settings/page-sections/cloudflare.php:36
#: views/settings/page-sections/sucuri.php:37
msgid "Learn more"
msgstr "Dowiedz się więcej"

#: views/settings/fields/rocket-cdn.php:72
msgid "Clear all RocketCDN cache files"
msgstr "Wyczyść wszystkie pliki pamięci podręcznej RocketCDN"

#: views/settings/page-sections/cloudflare.php:27
msgid "Cloudflare Cache"
msgstr "Pamięci podręczna CloudFlare"

#: views/settings/page-sections/cloudflare.php:35
#: views/settings/page-sections/sucuri.php:36
#, php-format
msgid "Purges cached resources for your website. %s"
msgstr "Oczyszcza przechowywane w pamięci podręcznej zasoby dla Twojej witryny internetowej. %s"

#: views/settings/page-sections/cloudflare.php:36
msgid "https://support.cloudflare.com/hc/en-us/articles/200169246"
msgstr "https://support.cloudflare.com/hc/en-us/articles/200169246"

#: views/settings/page-sections/cloudflare.php:45
msgid "Clear all Cloudflare cache files"
msgstr "Wyczyść wszystkie pliki pamięci podręcznej CloudFlare"

#: views/settings/page-sections/dashboard.php:38
msgid "Congratulations!"
msgstr "Gratulacje!"

#: views/settings/page-sections/dashboard.php:40
msgid "WP Rocket is now activated and already working for you."
msgstr "WP Rocket jest teraz aktywny i działa w tle."

#: views/settings/page-sections/dashboard.php:42
msgid "Your website should be loading faster now!"
msgstr "Twoja witryna powinna ładować się teraz szybciej!"

#: views/settings/page-sections/dashboard.php:44
#, php-format
msgid "To guarantee fast websites, WP Rocket automatically applies 80% of web performance best practices."
msgstr "Aby zagwarantować szybkie strony internetowe, WP Rocket automatycznie stosuje 80% najlepszych praktyk dotyczących wydajności."

#: views/settings/page-sections/dashboard.php:44
msgid "We also enable options that provide immediate benefits to your website."
msgstr "Oferujemy również opcje, które zapewniają natychmiastowe korzyści dla Twojej witryny internetowej."

#: views/settings/page-sections/dashboard.php:45
msgid "Continue to the options to further optimize your site!"
msgstr "Przejdź do opcji dalszej optymalizacji swojej witryny!"

#: views/settings/page-sections/dashboard.php:62
msgid "My Account"
msgstr "Moje konto"

#: views/settings/page-sections/dashboard.php:68
msgid "Refresh info"
msgstr "Odśwież informacje"

#: views/settings/page-sections/dashboard.php:86
msgid "with"
msgstr "z"

#: views/settings/page-sections/dashboard.php:100
msgid "Expiration Date"
msgstr "Data wygaśnięcia"

#: views/settings/page-sections/dashboard.php:110
msgid "View my account"
msgstr "Zobacz moje konto"

#: views/settings/page-sections/dashboard.php:137
msgid "Quick Actions"
msgstr "Szybkie działania"

#: views/settings/page-sections/dashboard.php:144
msgid "Remove all cached files"
msgstr "Usuń wszystkie pliki pamięci podręcznej"

#: views/settings/page-sections/dashboard.php:164
#: views/settings/page-sections/dashboard.php:170
msgid "Regenerate Critical CSS"
msgstr "Regeneruj krytyczny CSS"

#: views/settings/page-sections/dashboard.php:182
msgid "Remove Used CSS Cache"
msgstr "Usuń pamięć podręczną używanego CSS"

#: views/settings/page-sections/dashboard.php:205
msgid "Frequently Asked Questions"
msgstr "Najczęściej zadawane pytania"

#: views/settings/page-sections/dashboard.php:219
msgid "Still cannot find a solution?"
msgstr "Wciąż nie możesz znaleźć rozwiązania?"

#: views/settings/page-sections/dashboard.php:220
msgid "Submit a ticket and get help from our friendly and knowledgeable Rocketeers."
msgstr "Wyślij zgłoszenie i uzyskaj pomoc od naszych przyjaznych i kompetentnych Rocketeersów."

#: views/settings/page-sections/dashboard.php:228
msgid "Ask support"
msgstr "Poproś o wsparcie"

#: views/settings/page-sections/database.php:25
msgid "Backup your database before you run a cleanup!"
msgstr "Utwórz kopię zapasową swojej bazy danych przed uruchomieniem czyszczenia!"

#: views/settings/page-sections/database.php:26
msgid "Once a database optimization has been performed, there is no way to undo it."
msgstr "Po wykonaniu optymalizacji bazy danych nie ma możliwości jej cofnięcia."

#: views/settings/page-sections/database.php:28
msgid "Save Changes and Optimize"
msgstr "Zapisz zmiany i optymalizuj"

#: views/settings/page-sections/imagify.php:21
#, php-format
msgid "%1$sWP ROCKET%2$s created %3$sIMAGIFY%4$s %1$sfor best-in-class image optimization.%2$s"
msgstr "%1$sWP ROCKET%2$s stworzył %3$sIMAGIFY%4$s %1$s dla najlepszej w swojej klasie optymalizacji obrazu.%2$s"

#: views/settings/page-sections/imagify.php:24
msgid "Compress image to make your website faster, all while maintaining image quality."
msgstr "Kompresuj obraz, aby uczynić Twoją witrynę szybszą, przy jednoczesnym zachowaniu jakości obrazu."

#: views/settings/page-sections/imagify.php:25
msgid "More on Imagify:"
msgstr "Więcej na temat Imagify:"

#: views/settings/page-sections/imagify.php:27
msgid "Imagify Plugin Page"
msgstr "Strona wtyczki Imagify"

#: views/settings/page-sections/imagify.php:28
msgid "Imagify Website"
msgstr "Witryna Imagify"

#: views/settings/page-sections/imagify.php:29
msgid "Review of Image Compression Plugins"
msgstr "Przegląd wtyczek do kompresji obrazów"

#: views/settings/page-sections/imagify.php:38
msgid "Install Imagify"
msgstr "Zainstaluj Imagify za darmo"

#: views/settings/page-sections/license.php:22
msgid "WP Rocket was not able to automatically validate your license."
msgstr "WP Rocket nie był w stanie automatycznie zatwierdzić Twojej licencji."

#: views/settings/page-sections/license.php:29
#, php-format
msgid "Follow this %1$s, or contact %2$s to get the engine started."
msgstr "Postępuj zgodnie z tą instrukcją %1$s, lub skontaktuj się z %2$s, aby uruchomić to cacko."

#: views/settings/page-sections/license.php:32
#, php-format
msgid "%1$s%2$s%3$stutorial%4$s"
msgstr "%1$s%2$s%3$stutorial%4$s"

#: views/settings/page-sections/license.php:34
msgid "https://docs.wp-rocket.me/article/100-resolving-problems-with-license-validation/?utm_source=wp_plugin&utm_medium=wp_rocket"
msgstr "https://docs.wp-rocket.me/article/100-resolving-problems-with-license-validation/?utm_source=wp_plugin&utm_medium=wp_rocket"

#: views/settings/page-sections/license.php:40
#, php-format
msgid "%1$s%2$s%3$ssupport%4$s"
msgstr "%1$s%2$s%3$swsparcie%4$s"

#: views/settings/page-sections/sucuri.php:46
msgid "Clear all Sucuri cache files"
msgstr "Wyczyść wszystkie pliki pamięci podręcznej Sucuri"

#: views/settings/page-sections/tools.php:20
#, php-format
msgid "Files size: %1$s. Number of entries: %2$s."
msgstr "Wielkość plików: %1$s. Liczba wpisów: %2$s."

#: views/settings/page-sections/tools.php:23
#, php-format
msgid "%1$sDownload the file%2$s."
msgstr "%1$sPobierz plik%2$s."

#: views/settings/page-sections/tools.php:26
#, php-format
msgid "%1$sDelete the file%2$s."
msgstr "%1$s Usuń plik%2$s."

#: views/settings/page-sections/tools.php:37
msgid "Export settings"
msgstr "Ustawienia eksportu"

#: views/settings/page-sections/tools.php:38
msgid "Download a backup file of your settings"
msgstr "Pobierz plik kopii zapasowej ustawień"

#: views/settings/page-sections/tools.php:46
msgid "Download settings"
msgstr "Pobierz ustawienia"

#: views/settings/page-sections/tools.php:60
msgid "Rollback"
msgstr "Wycofanie"

#: views/settings/page-sections/tools.php:64
#, php-format
msgid "Has version %s caused an issue on your website?"
msgstr "Czy wersja %s spowodowała problem na Twojej witrynie?"

#: views/settings/page-sections/tools.php:69
#, php-format
msgid "You can rollback to the previous major version here.%sThen send us a support request."
msgstr "Możesz powrócić do poprzedniej głównej wersji tutaj.%sNastępnie wyślij do nas zapytanie o wsparcie."

#: views/settings/page-sections/tools.php:80
#, php-format
msgid "Reinstall version %s"
msgstr "Wersja do ponownej instalacji %s"

#: views/settings/page-sections/tools.php:106
msgid "Debug mode"
msgstr "Tryb debugowania"

#: views/settings/page-sections/tools.php:111
msgid "Create a debug log file."
msgstr "Utwórz plik dziennika debugowania."

#: views/settings/page-sections/tutorials.php:13
#: views/settings/partials/getting-started.php:18
msgid "Getting Started"
msgstr "Pierwsze kroki"

#: views/settings/page-sections/tutorials.php:15
msgid "Getting Started with WP Rocket"
msgstr "Rozpoczęcie pracy z pakietem WP Rocket"

#: views/settings/page-sections/tutorials.php:16
#: views/settings/partials/getting-started.php:12
msgid "Finding the Best Settings for Your Site"
msgstr "Określenie najlepszych ustawień dla Twojej strony internetowej"

#: views/settings/page-sections/tutorials.php:17
#: views/settings/partials/getting-started.php:13
msgid "How to Check if WP Rocket is Caching Your Site"
msgstr "Jak sprawdzić, czy WP Rocket buforuje Twoją witrynę"

#: views/settings/page-sections/tutorials.php:18
#: views/settings/partials/getting-started.php:14
msgid "How to Measure the Speed of Your Site"
msgstr "Jak zmierzyć prędkość Twojej strony internetowej"

#: views/settings/page-sections/tutorials.php:19
msgid "How Preloading Works"
msgstr "Jak działa wstępne ładowanie"

#: views/settings/page-sections/tutorials.php:23
msgid "Passing the Core Web vitals"
msgstr "Zaliczenie testów Core Web Vitals"

#: views/settings/page-sections/tutorials.php:25
msgid "How to improve LCP with WP Rocket"
msgstr "Jak ulepszyć LCP za pomocą WP Rocket"

#: views/settings/page-sections/tutorials.php:26
msgid "How to improve FID with WP Rocket"
msgstr "Jak poprawić FID za pomocą WP Rocket"

#: views/settings/page-sections/tutorials.php:27
msgid "How to improve CLS with WP Rocket"
msgstr "Jak poprawić CLS za pomocą WP Rocket"

#: views/settings/page-sections/tutorials.php:31
msgid "Troubleshooting"
msgstr "Wsparcie"

#: views/settings/page-sections/tutorials.php:33
msgid "Troubleshooting Display Issues with File Optimization"
msgstr "Rozwiązywanie problemów z wyświetlaniem przy optymalizacji plików"

#: views/settings/page-sections/tutorials.php:34
msgid "How to Find the Right JavaScript to Exclude"
msgstr "Jak znaleźć odpowiedni skrypt JavaScript, który należy wykluczyć"

#: views/settings/page-sections/tutorials.php:35
msgid "How External Content Slows Your Site"
msgstr "Jak zawartość zewnętrzna spowalnia Twoją stronę internetową"

#: views/settings/page-sections/tutorials.php:41
msgid "Set Up the Cloudflare Add-on"
msgstr "Skonfiguruj dodatek \"Cloudflare\""

#: views/settings/page.php:16
msgid "WP Rocket Settings"
msgstr "Ustawienia WP Rocket"

#: views/settings/page.php:30
#, php-format
msgid "version %s"
msgstr "wersja %s"

#: views/settings/page.php:60
msgid "Show Sidebar"
msgstr "Pokaż boczny panel"

#: views/settings/page.php:82
#, php-format
msgid "Below is a detailed view of all data WP Rocket will collect %1$sif granted permission.%2$s"
msgstr "Poniżej znajduje się szczegółowy widok wszystkich danych, które WP Rocket zgromadzi %1$sjeśli przyznano mu zezwolenie.%2$s"

#: views/settings/page.php:87
msgid "WP Rocket will never transmit any domain names or email addresses (except for license validation), IP addresses, or third-party API keys."
msgstr "WP Rocket nigdy nie będzie transmitował nazw domen ani adresów e-mail (z wyjątkiem weryfikacji licencji), adresów IP lub kluczy API osób trzecich."

#: views/settings/page.php:89
msgid "Activate Rocket analytics"
msgstr "Aktywuj analitykę Rocket Analytics"

#: views/settings/partials/documentation.php:15
msgid "It is a great starting point to fix some of the most common issues."
msgstr "Jest to świetny punkt wyjścia, aby naprawić niektóre z najbardziej powszechnych problemów."

#: views/settings/partials/documentation.php:22
msgid "Read the documentation"
msgstr "Przeczytaj dokumentację"

#: views/settings/partials/getting-started.php:11
msgid "What WP Rocket Does For You By Default"
msgstr "Co WP Rocket robi dla Ciebie bez dodatkowej konfiguracji"

#: views/settings/partials/sidebar.php:12
msgid "How to correctly measure your website’s loading time"
msgstr "Jak prawidłowo zmierzyć czas ładowania witryny internetowej"

#: views/settings/partials/sidebar.php:14
msgid "Check our tutorial and learn how to measure the speed of your site."
msgstr "Sprawdź nasz samouczek i dowiedz się, jak mierzyć prędkość Twojej witryny."

#: views/settings/partials/sidebar.php:15
#: views/settings/partials/sidebar.php:24
msgid "Read our guide"
msgstr "Przeczytaj nasz przewodnik"

#: views/settings/partials/sidebar.php:18
msgid "Learn about optimal WP Rocket settings for mobile."
msgstr "Dowiedz się o optymalnych ustawieniach WP Rocket dla urządzeń mobilnych."

#: views/settings/partials/sidebar.php:27
msgid "Test and Improve Google Core Web Vitals for WordPress."
msgstr "Testuj i ulepszaj Google Core Web Vitals dla WordPressa."

#: views/settings/partials/sidebar.php:28
msgid "Read more"
msgstr "Zobacz więcej"

#: views/settings/partials/sidebar.php:33
msgid "You have not activated logged-in user cache."
msgstr "Nie aktywowałeś pamięci podręcznej zalogowanego użytkownika."

#: views/settings/partials/sidebar.php:34
msgid "Use a private browser to check your website's speed and visual appearance."
msgstr "Skorzystaj z trybu incognito przeglądarki, aby sprawdzić szybkość i wygląd swojej strony internetowej."

#: views/settings/sections/addons-container.php:24
#: views/settings/sections/fields-container.php:28
msgid "Need Help?"
msgstr "Potrzebujesz pomocy?"

#: inc/Addon/Cloudflare/Cloudflare.php:205
msgid "days"
msgstr ""

#: inc/Addon/Cloudflare/Cloudflare.php:208
msgid "seconds"
msgstr ""

#: inc/Addon/Cloudflare/Cloudflare.php:210
msgid "minutes"
msgstr ""

#: inc/Addon/Cloudflare/Cloudflare.php:212
msgid "hours"
msgstr ""

#: inc/Addon/Cloudflare/Subscriber.php:224
#: inc/Addon/Cloudflare/Subscriber.php:253
#, php-format
msgid "%1$sWP Rocket:%2$s %3$s"
msgstr ""

#: inc/Addon/Cloudflare/Subscriber.php:242
#, php-format
msgid "%1$sWP Rocket:%2$s Cloudflare cache successfully purged."
msgstr ""

#: inc/Addon/Cloudflare/Subscriber.php:328
msgctxt "Cloudflare caching level"
msgid "standard"
msgstr ""

#: inc/Addon/Cloudflare/Subscriber.php:403
#, php-format
msgid "Cloudflare browser cache set to %s"
msgstr ""

#: inc/Addon/Cloudflare/Subscriber.php:512
#, php-format
msgid "%1$sWP Rocket:%2$s Optimal settings activated for Cloudflare:"
msgstr ""

#: inc/Addon/Cloudflare/Subscriber.php:521
#, php-format
msgid "%1$sWP Rocket:%2$s Optimal settings deactivated for Cloudflare, reverted to previous settings:"
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:642
msgid "If you have problems after activating this option, copy and paste the default exclusions to quickly resolve issues:"
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:646
#, php-format
msgid "Also, please check our %1$sdocumentation%2$s for a list of compatibility exclusions."
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:651
msgid "Internal scripts are excluded by default to prevent issues. Remove them to take full advantage of this option."
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:654
#, php-format
msgid "If this causes trouble, restore the default exclusions, found %1$shere%2$s"
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:942
msgid "One-click exclusions"
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:943
msgid "When using the Delay JavaScript Execution, you might experience delay loading elements located in the viewport that need to appear immediately - e.g. slider, header, menu."
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:944
msgid "If you need instant visibility, click below on files that should NOT be delayed. This selection will help users interact with the elements straight away."
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:1827
#, php-format
msgid "%1$sPlanning on using Automatic Platform Optimization (APO)?%2$s Just activate the official Cloudflare plugin and configure it. WP Rocket will automatically enable compatibility."
msgstr ""

#: inc/Engine/Admin/Settings/Page.php:2129
msgctxt "Sucuri"
msgid "Firewall API key (for plugin), must be in format {32 characters}/{32 characters}:"
msgstr ""

#: inc/Engine/CriticalPath/CriticalCSSSubscriber.php:840
#, php-format
msgid "We highly recommend the %1$supdated Remove Unused CSS%2$s for a better CSS optimization. Load CSS Asynchronously is always available as a back-up."
msgstr ""

#: inc/Engine/CriticalPath/CriticalCSSSubscriber.php:845
msgid "Stay with the old option"
msgstr ""

#: inc/Engine/Optimization/DelayJS/Admin/SiteList.php:195
msgid "Analytics & Ads"
msgstr ""

#: inc/Engine/Optimization/DelayJS/Admin/SiteList.php:200
msgid "Plugins"
msgstr ""

#: inc/Engine/Optimization/DelayJS/Admin/SiteList.php:205
msgid "Themes"
msgstr ""

#: inc/Engine/Optimization/DynamicLists/ServiceProvider.php:52
msgid "Default Lists"
msgstr ""

#: inc/Engine/Optimization/DynamicLists/ServiceProvider.php:58
msgid "Delay JavaScript Execution Exclusion Lists"
msgstr ""

#: inc/Engine/Optimization/RUCSS/Cron/Subscriber.php:174
msgid "WP Rocket clear Remove Unused CSS failed jobs"
msgstr ""

#: inc/Engine/Preload/Cron/Subscriber.php:166
msgid "WP Rocket Preload revert stuck failed jobs"
msgstr ""

#: inc/ThirdParty/Plugins/CDN/Cloudflare.php:157
msgid "Your site is using the official Cloudflare plugin. We have enabled Cloudflare auto-purge for compatibility. If you have APO activated, it is also compatible."
msgstr ""

#: inc/ThirdParty/Plugins/CDN/Cloudflare.php:158
msgid "Cloudflare cache will be purged each time WP Rocket clears its cache to ensure content is always up-to-date."
msgstr ""

#: inc/ThirdParty/Plugins/CDN/Cloudflare.php:202
#, php-format
msgid "%1$sWP Rocket:%2$s You are using \"Dynamic Cookies Cache\". Cloudflare APO is not yet compatible with that feature."
msgstr ""

#: inc/ThirdParty/Plugins/CDN/Cloudflare.php:208
#, php-format
msgid "You should either disable Cloudflare APO or check with the theme/plugin requiring the use of “Dynamic Cookies Cache” developers for an alternative way to be page-cache friendly. %1$sMore info%2$s"
msgstr ""

#: inc/ThirdParty/Plugins/CDN/Cloudflare.php:259
#, php-format
msgid "%1$sWP Rocket:%2$s You are using \"Separate cache files for mobile devices\". You need to activate \"Cache by Device Type\" %3$ssetting%5$s on Cloudflare APO to serve the right version of the cache. %4$sMore info%5$s"
msgstr ""

#: inc/ThirdParty/Plugins/CDN/Cloudflare.php:280
#, php-format
msgid "%1$sWP Rocket:%2$s You have \"Cache by Device Type\" enabled on Cloudflare APO. If you judge it necessary for the website to have a different cache on mobile and desktop, we suggest you enable our “Separate Cache Files for Mobiles Devices” to ensure the generated cache is accurate."
msgstr ""

#: inc/ThirdParty/Plugins/Optimization/Perfmatters.php:38
msgid "Remove Unused CSS is currently activated in Perfmatters. If you want to use WP Rocket's Remove Unused CSS feature, disable this option in Perfmatters."
msgstr ""

#: inc/ThirdParty/Plugins/Optimization/RapidLoad.php:39
msgid "Automated unused CSS removal is currently activated in RapidLoad Power-Up for Autoptimize. If you want to use WP Rocket's Remove Unused CSS feature, disable the  RapidLoad Power-Up for Autoptimize plugin."
msgstr ""

#: inc/admin/ui/notices.php:757
msgid "Turn on Remove Unused CSS"
msgstr ""

#: inc/admin/ui/notices.php:763
msgid "Enable “Separate Cache Files for Mobile Devices” now"
msgstr ""
