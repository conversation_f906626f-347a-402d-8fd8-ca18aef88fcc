{"version": 3, "names": [], "mappings": "", "sources": ["lazyload-css.js"], "sourcesContent": ["!function o(n,c,s){function i(r,e){if(!c[r]){if(!n[r]){var t=\"function\"==typeof require&&require;if(!e&&t)return t(r,!0);if(a)return a(r,!0);throw(t=new Error(\"Cannot find module '\"+r+\"'\")).code=\"MODULE_NOT_FOUND\",t}t=c[r]={exports:{}},n[r][0].call(t.exports,function(e){return i(n[r][1][e]||e)},t,t.exports,o,n,c,s)}return c[r].exports}for(var a=\"function\"==typeof require&&require,e=0;e<s.length;e++)i(s[e]);return i}({1:[function(e,r,t){\"use strict\";!function(){const e=rocket_pairs||[],t=document.querySelector(\"#wpr-lazyload-bg\");var r=rocket_lazyload_css_data.threshold||300;const o=new IntersectionObserver(e=>{e.forEach(r=>{if(r.isIntersecting){const e=rocket_pairs.filter(e=>r.target.matches(e.selector));e.map(e=>{e&&(t.innerHTML+=e.style,e.elements.forEach(e=>{e.setAttribute(\"data-rocket-lazy-bg\",\"loaded\"),o.unobserve(e)}))})}})},{rootMargin:r+\"px\"});e.forEach(r=>{try{const e=document.querySelectorAll(r.selector);e.forEach(e=>{o.observe(e),(r.elements||(r.elements=[])).push(e)})}catch(e){console.error(e)}})}()},{}]},{},[1]);"], "file": "lazyload-css.js"}