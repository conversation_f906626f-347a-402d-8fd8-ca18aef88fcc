{"version": 3, "sources": ["lazyload.js"], "names": ["global", "factory", "exports", "_typeof", "module", "define", "amd", "LazyLoad", "this", "running<PERSON>n<PERSON><PERSON><PERSON>", "window", "isBot", "test", "defaultSettings", "elements_selector", "threshold", "thresholds", "document", "createElement", "data_srcset", "data_sizes", "data_bg", "class_loading", "class_loaded", "class_error", "load_delay", "auto_unobserve", "callback_enter", "callback_exit", "callback_reveal", "callback_loaded", "callback_error", "callback_finish", "getInstanceSettings", "getData", "element", "attribute", "value", "getAttribute", "setData", "attrName", "setAttribute", "processedDataName", "removeAttribute", "getWasProcessedData", "setTimeoutData", "getTimeoutData", "timeoutDataName", "createInstance", "elementToPurge", "options", "event", "instance", "classObj", "createEvent", "initCustomEvent", "dispatchEvent", "callbackIfSet", "autoInitialize", "callback", "argument", "updateLoadingCount", "plusMinus", "_elements", "length", "_loadingCount", "_settings", "getSourceTags", "parentTag", "childTag", "sourceTags", "i", "children", "setAttributeIfValue", "tagName", "settings", "data_src", "backgroundImage", "IMG", "parent", "parentNode", "for<PERSON>ach", "sourceTag", "setImageAttributes", "IFRAME", "VIDEO", "load", "setSources", "elements", "setSourcesFunctions", "setSourcesFunction", "setSourcesVideo", "filter", "srcDataValue", "bgDataValue", "setSourcesBgImage", "addClass", "className", "addEventListener", "eventName", "handler", "removeEventListener", "genericLoadEventName", "<PERSON><PERSON><PERSON><PERSON>", "removeClass", "loadHandler", "addEventListeners", "errorEventName", "success", "removeEventListeners", "target", "supportsClassList", "remove", "replace", "RegExp", "<PERSON><PERSON><PERSON><PERSON>", "addOneShotEventListeners", "revealAndUnobserve", "observer", "unobserve", "cancelDelayLoad", "timeoutId", "clearTimeout", "loadDelay", "revealElement", "force", "managedTags", "indexOf", "setTimeout", "callback_set", "setObserver", "_observer", "IntersectionObserver", "entries", "entry", "setWasProcessedData", "delayLoad", "onEnter", "root", "container", "rootMargin", "isIntersecting", "customSettings", "getObserverSettings", "trueString", "prototype", "update", "_this", "supportsIntersectionObserver", "querySelectorAll", "purgeProcessedElements", "Array", "observe", "loadAll", "_this2", "_this3", "optionsItem"], "mappings": "+cAAC,SAAUA,EAAQC,GACC,YAAnB,oBAAOC,QAAP,YAAAC,QAAOD,WAA0C,oBAAXE,OAAyBA,OAAOF,QAAUD,IAC9D,mBAAXI,QAAyBA,OAAOC,IAAMD,OAAOJ,GACnDD,EAAOO,SAAWN,IAHnB,CAAAO,KAAA,WAAkBP,aAIlB,IAACQ,EAAmB,oBAAAC,OAIfC,EAFNF,KAAsB,aAAGC,SAGvBD,oBADIE,WAKN,gCAAkCC,KACjCH,UAAAA,WAKKI,EACLC,GAAiB,yBADMJ,OAGvBK,EACAC,GAJuB,cAAAC,SAAAC,cAAA,KAMvBC,EAAa,CACbC,kBAAY,MACZC,UAAOV,GARgBF,EAAAQ,SAAA,KASvBK,UAAAA,IACAC,WAAAA,KACAC,SAAAA,MACAC,YAAU,SACVC,WAAAA,QACAC,QAAAA,KACAC,cAAAA,UACAC,aAAAA,SACAC,YAAAA,QACAC,WAAAA,EACAC,gBAAAA,EAnBuBL,eAAxB,KAeCC,cAAe,KAOhBC,gBAAII,KACHH,gBAAO,KACPC,eAFD,KAHCC,gBAAiB,MAYZE,EAAU,SAACC,EAASC,GAMzB,OAAIC,EAAKC,aALMA,QAKKF,IAGnBG,EAAA,SAAAJ,EAAAC,EAAAC,GAJD,IAAIG,EAJWF,QAIaF,EAKpBK,OAARN,EAGkCA,EAAAM,aAC1BN,EAASO,GAHjBP,EAPDQ,gBAAAH,IAYMI,EAAsB,SAAAT,GAAO,MAZnB,SAehBD,EAAMW,EAnBN,kBAmBAA,EAAA,SAAAV,EAAAE,GAAA,OACCE,EAAQJ,EAvBe,aAuBWE,IAELS,EAAYX,SAAAA,GAASY,OAAAA,EAAAA,EAzB3B,eAgCOC,EAAW,SAAKC,EAAhBC,GAAA,IAAvBC,EAGRC,EAAA,IAAAC,EAAAH,GAKC,IAJDC,EAAMH,IAAAA,YALN,wBAKMA,CAAAA,OAA0BK,CAAAA,SAAAA,KAC/B,MAAIF,IAEJA,EAAIC,SAAWE,YAAA,gBAORC,gBAfR,yBAeqC,GAAO,EAAO,CAAEH,SAAAA,IALnD1C,OAAA8C,cAAAL,IAYF,IAAAM,EAASC,SAAAA,EAAeL,GACvBM,GACCA,EAAAC,IAGAC,EAAA,SAAAT,EAAAU,GACAd,EAAAA,eAAeK,EACT,IAHPD,EAGOW,UAAAC,QAAA,IAAAZ,EAAAa,eACNR,EAAAL,EAAAc,UAAAlC,kBAIAmC,EAAA,SAAAC,GAkBD,IAjBA,IAiBgBC,EAjBhBC,EAAA,GAiBSC,EAAI,EAAcF,EAAWD,EAAUI,SAASD,GAAKA,GAAK,EAf9C,WAAhBd,EAAAA,SACLa,EAAIX,KAAUU,GAGd,OAJDC,GAOClB,EAAA,SAAAjB,EAA0B2B,EAA1BzB,GAiBKA,GAdJF,EAAAM,aAAAD,EAAAH,IAGI8B,EAAgB,SAAAhC,EAAhBgC,GACLM,EAkBCtC,EAjBD,QACCD,EAAImC,EAASK,EAATtD,aAEHqD,EACDtC,EAmBA,SAlBDD,EAAAC,EAAOmC,EAAPnD,cAqBAsD,EAAoBtC,EAAS,MAAOD,EAAQC,EAASwC,EAASC,YAsC7DzC,EAAc0C,CACdC,IAvDA,SAAA3C,EAAAwC,GACA,IAAAI,EAAA5C,EAAA6C,WACD7C,GAAA,YAAQM,EAAAA,SAJT0B,EAAAY,GA0BaE,QAAQ,SAAAC,GAnBrBC,EAAMA,EAAqBR,KAY1BQ,EAZDhD,EAAAwC,IA2DCS,OA5CuBJ,SAAAA,EAAvBL,GAaAF,EAAoBtC,EAAS,MAAOD,EAAQC,EAASwC,EAASC,YAwB9DS,MAjCC,SAAmBlD,EAAAwC,GAClBQ,EAAmBD,GADpBD,QAAA,SAAAC,GAGAT,EAaCS,EAXFC,MAVDjD,EAAAgD,EAAAP,EAAAC,aAcCH,EAAAA,EAAoBtC,MAASD,EAAOA,EAAQC,EAASwC,WACrDxC,EAFDmD,SAiBAC,EAAA,SAAApD,EAAAiB,GAoBC,IAtIAoC,EAAAvC,EAsIM0B,EAAWvB,EAASc,UAPrBuB,EAAAA,EAAmBf,QACrBgB,EADwBD,EAAAf,GAE3BU,GAAAA,EAID,OAHCC,EAAOM,EAAAA,GAHoB9B,EAA5BT,EAAA,QAaEA,EAASW,WA5IVyB,EA4IsCpC,EAASW,UA5I/Cd,EA4I0Dd,EA7I3DqD,EAAAI,OAAA,SAAAzD,GAAA,OAAAA,IAAAc,OAuGkBkB,SAAAA,EAAchC,GAC/BmC,IAAAA,EAAApC,EAAmBC,EAAA+C,EAAaN,UAC/BH,EAAAA,EACCS,EACAP,EACAzC,SAGFuC,IACAtC,EAAAA,MAAA0C,gBAAA1C,QAAAA,OAAA0D,EAAA1D,OAGD2D,IACC3D,EAAM0D,MAAAA,gBAAuB1D,GAoB7B4D,CAAgB5D,EAAQuC,IAExBsB,EAAIN,SAAAA,EAAJO,GACCP,EACA7B,EAAAA,UAAAA,IAAkBoC,GAGlB9D,EAAA8D,YAAA9D,EAAA8D,UAAA,IAAA,IAAAA,GAgBAC,EAAA,SAAA/D,EAAAgE,EAAAC,GAYDjE,EAAQ+D,iBAAiBC,EAAWC,IAG/BC,EAAsB,SAAClE,EAASgE,EAAWC,GARjDjE,EAAMmE,oBAAuBH,EAA7BC,IASCjE,EAAQkE,SAAAA,EAAoBF,EAA5BI,GACAF,EAFDlE,EAnBMqE,OAmBNC,GAYCJ,EAAoBlE,EA9BG,aA8B0BsE,GARlDJ,EAAMK,EArBJ,QAqBIA,IAGLR,EAAiB/D,SAASwE,EAAAA,EAAgBJ,GAC1C,IAJD5B,EAAAvB,EAAAc,UAcO+B,EAAYW,EAAUjC,EAASpD,aAAeoD,EAASnD,YARxDqF,EAAAA,EACLR,EAAAA,gBACAA,EAAAA,eACAA,EAAAA,EAAmBS,QArDpB,SAAA3E,EAAA8D,GAsBKc,EATL5E,EAAM6D,UAAWgB,OAAXhB,GAGJ7D,EAAA8D,UAAA9D,EAAA8D,UACAgB,QAAA,IAAAC,OAAA,WAAAjB,EAAA,YAAA,KAWAgB,QAAQ,OAAQ,IAVjB9E,QAAAA,OAAQ8D,IA8CRO,CAAYrE,EAASwC,EAASrD,eAR/B0E,EAAMmB,EAAYlB,GACjBxC,EAAIkB,EAAWvB,GAEfS,EAAiB+C,GACdjC,IAKHqB,EAAA,SAAA7D,EAAAiB,GACAK,IAAAA,EAAcE,SAAdF,EAAcE,GAEdE,EAAAA,GAAkB,EAACT,GACnByD,EAbD1E,EAAAsE,EAAAF,IAeMa,EAAAA,SAAAA,EAAAA,GACLD,EAAMV,GAAc,EAAArD,GACnB+D,EAAahE,EAAaC,EAA1BmD,KA7BwB,SAACpE,EAASsE,EAAaF,GARjDL,EAAMA,EAfAM,OAeAN,GACL/D,EAAQ+D,EAfe,aAevBO,GACAP,EAFD/D,EAbE,QAaFoE,GAuCEG,CAHDvE,EAAAsE,EAAAF,IAKCY,EAAahE,CAAAA,MAAO,SAAOC,SAkBvBiE,EAAqB,SAAClF,EAASiB,GAPpC,IAAAkE,EAAK3C,EAASlD,UACb4F,EAAAA,EAAkBjE,GAClBkE,GAAAlE,EAAAc,UAAAxC,gBACA4F,EAAAC,UAAApF,IAqBIqF,EAAkB,SAAArF,GATxB,IAAAsF,EAAe3E,EAACX,GACfsF,IAEAC,aAAK/C,GACJ9B,EAAAV,EAAA,QAEDqF,EAAAA,SAAerF,EAAfiB,GACA,IAPDuE,EAAAvE,EAAAc,UAAAzC,WAoBKgG,EAAY3E,EAAeX,GAXhCsF,IAECA,EAAKA,WAAW,WACfJ,EAAQlF,EAAAiB,GACRoE,EAAArF,IAcEwF,GAbHD,EAAYvF,EAACsF,KAiBRG,EAAgB,SAACzF,EAASiB,EAAUyE,GAb1C,IAAAlD,EAAevB,EAAGc,WACjB2D,GAAIF,EAAqBzD,KAEzB4D,EAAAC,QAAe5F,EAAAuC,UAAA,IACd0C,EAAQjF,EAAAiB,GACR4C,EAAA7D,EAAAwC,EAAArD,gBACDmG,EAAAA,EAAYO,GA7Qe,SAAA7F,GAAOI,EAAAJ,EAhBnC,gBAIgB,QA0RdkF,CAAAA,GACAG,EAAAA,EAAgBrF,gBAAhBA,GACAsB,EAAEkE,EAHHM,aAAA9F,KAYA+F,EAAgBH,SAAAA,GACfX,QAAAA,IAqBDhE,EAAS+E,UAAY,IAAIC,qBAAqB,SAAAC,GAlB9C9C,EAAAA,QAAWpD,SAAAA,GAAD,OAMY,SAAAmG,GAAK,OAf5BA,EAAMV,gBAAgBU,EAAhBV,kBAAiBzF,EAUtBoG,CAAAA,GApEA,SAAApG,EAAAiB,GAUA,IAAMuB,EAAWvB,EAASc,UAN1BwC,EAAAA,EAAkBvE,eAASsE,GAT5B9B,EAAAlD,WAcA+G,EAAMC,EAAUrF,GAIdiE,EAAmBlF,EAASiB,GAwD7BK,CAAAA,EAAckB,OAAS9C,GA1CT,SAACM,EAASiB,GARzB,IAAMiE,EAAAA,EAAqBnD,UAC1BT,EAAI6D,EAAWlE,cAAfjB,GACAyF,EAAAA,YAECN,EAASC,GA+CV9D,CAAAA,EAAckB,OAASsD,MAMiB,CAhBxCS,MAgB2B/D,EAeJvB,EAASc,WA/B5ByE,YAAU/F,SAAoBT,KAAUwC,EAAAgE,UAC3CC,WAD2CjE,EACnC3D,YAAA2D,EAAA5D,UAAA,QAYJ8H,GAGsB,IAAAlE,GAmBtBpE,EAAW,SAASuI,EAAgBtD,GAnB1ChF,KAAMuI,UAzTkB,SAAAD,GACxB,OAAME,SAAa,GAAnBnI,EAAAiI,GAwTMC,CAAAA,GAA8BvI,KAAAyD,cAAK,EACxCyE,EAAM/D,MACNiE,KAAAA,OAAAA,IAkDE,OA1BHrI,EAAS0I,UAAY,CArBrBC,OAAMhB,SAAW1C,GAAG,IAAA2D,EAAA3I,KACfmE,EAACyE,KAAAA,UACJrF,EACAyB,GAuBCb,EAASgE,UAAUU,iBAAiB1E,EAAS7D,mBArB9CuH,KAAAA,UAxS6B,SAAA7C,GAA/B,OAAM8D,EAAAA,OAAAA,SAAAA,GAAyB,OAAA1G,EAAzB0G,KAwSIrE,CAAasE,MAAAN,UACpBJ,MAAAA,KAAeP,KAKjB3H,GAAAH,KAAA2H,UAKA3H,KAAAuD,UAAKE,QAAgB,SAAA9B,GACrB+F,EAAAA,UAAYsB,QAAZrH,KAjBD3B,KAAAiJ,WAqBAlJ,QAAQ,WAAR,IAAAmJ,EAAAlJ,KACC0I,KAAMf,YAAqB3H,KAAAuD,UAAAkB,QAAA,SAAA9C,GAuBxBuH,EAAKvB,UAAUZ,UAAUpF,KAE1B3B,KAAK2H,UAAY,MAElB3H,KAAKuD,UAAY,KArBjBvD,KAAA0D,UAAKH,MAILuB,KAAA,SAASnD,EAAUgG,GAClBP,EAAK6B,EAALjJ,KAAAqH,IAwBF4B,QAAS,WAAW,IAAAE,EAAAnJ,KApBduD,KAAUkB,UACdA,QAAKkD,SAAAA,GACLd,EAFDlF,EAAAwH,OAOClJ,GAhUoC,SAArC4C,EAAAH,GACA,GAACA,EAGDC,GAAAA,EAAMI,OAEP7C,IAAAA,IAAO8C,EAAAA,EAAAA,EAAPoG,EAAA1G,EAAAqB,GAAAA,GAAA,EAZDvB,EAAAK,EAAAuG,QAUmD5G,EAAjDK,EAAAH,GA6TEQ,CAAInD,EAAJG,OAAe6G,iBAEhBhH", "file": "lazyload.min.js", "sourcesContent": ["(function (global, factory) {\n\ttypeof exports === 'object' && typeof module !== 'undefined' ? module.exports = factory() :\n\ttypeof define === 'function' && define.amd ? define(factory) :\n\t(global.LazyLoad = factory());\n}(this, (function () { 'use strict';\n\nconst runningOnBrowser = typeof window !== \"undefined\";\n\nconst isBot =\n\t(runningOnBrowser && !(\"onscroll\" in window)) ||\n\t(typeof navigator !== \"undefined\" &&\n\t\t/(gle|ing|ro)bot|crawl|spider/i.test(navigator.userAgent));\n\nconst supportsIntersectionObserver =\n\trunningOnBrowser && \"IntersectionObserver\" in window;\n\nconst supportsClassList =\n\trunningOnBrowser && \"classList\" in document.createElement(\"p\");\n\nconst defaultSettings = {\n\telements_selector: \"img\",\n\tcontainer: isBot || runningOnBrowser ? document : null,\n\tthreshold: 300,\n\tthresholds: null,\n\tdata_src: \"src\",\n\tdata_srcset: \"srcset\",\n\tdata_sizes: \"sizes\",\n\tdata_bg: \"bg\",\n\tclass_loading: \"loading\",\n\tclass_loaded: \"loaded\",\n\tclass_error: \"error\",\n\tload_delay: 0,\n\tauto_unobserve: true,\n\tcallback_enter: null,\n\tcallback_exit: null,\n\tcallback_reveal: null,\n\tcallback_loaded: null,\n\tcallback_error: null,\n\tcallback_finish: null\n};\n\nvar getInstanceSettings = customSettings => {\n\treturn Object.assign({}, defaultSettings, customSettings);\n};\n\nconst dataPrefix = \"data-\";\nconst processedDataName = \"was-processed\";\nconst timeoutDataName = \"ll-timeout\";\nconst trueString = \"true\";\n\nconst getData = (element, attribute) => {\n\treturn element.getAttribute(dataPrefix + attribute);\n};\n\nconst setData = (element, attribute, value) => {\n\tvar attrName = dataPrefix + attribute;\n\tif (value === null) {\n\t\telement.removeAttribute(attrName);\n\t\treturn;\n\t}\n\telement.setAttribute(attrName, value);\n};\n\nconst setWasProcessedData = element =>\n\tsetData(element, processedDataName, trueString);\n\nconst getWasProcessedData = element =>\n\tgetData(element, processedDataName) === trueString;\n\nconst setTimeoutData = (element, value) =>\n\tsetData(element, timeoutDataName, value);\n\nconst getTimeoutData = element => getData(element, timeoutDataName);\n\nconst purgeProcessedElements = elements => {\n\treturn elements.filter(element => !getWasProcessedData(element));\n};\n\nconst purgeOneElement = (elements, elementToPurge) => {\n\treturn elements.filter(element => element !== elementToPurge);\n};\n\n/* Creates instance and notifies it through the window element */\nconst createInstance = function(classObj, options) {\n\tvar event;\n\tlet eventString = \"LazyLoad::Initialized\";\n\tlet instance = new classObj(options);\n\ttry {\n\t\t// Works in modern browsers\n\t\tevent = new CustomEvent(eventString, { detail: { instance } });\n\t} catch (err) {\n\t\t// Works in Internet Explorer (all versions)\n\t\tevent = document.createEvent(\"CustomEvent\");\n\t\tevent.initCustomEvent(eventString, false, false, { instance });\n\t}\n\twindow.dispatchEvent(event);\n};\n\n/* Auto initialization of one or more instances of lazyload, depending on the \n    options passed in (plain object or an array) */\nfunction autoInitialize(classObj, options) {\n\tif (!options) {\n\t\treturn;\n\t}\n\tif (!options.length) {\n\t\t// Plain object\n\t\tcreateInstance(classObj, options);\n\t} else {\n\t\t// Array of objects\n\t\tfor (let i = 0, optionsItem; (optionsItem = options[i]); i += 1) {\n\t\t\tcreateInstance(classObj, optionsItem);\n\t\t}\n\t}\n}\n\nconst callbackIfSet = (callback, argument) => {\n\tif (callback) {\n\t\tcallback(argument);\n\t}\n};\n\nconst updateLoadingCount = (instance, plusMinus) => {\n\tinstance._loadingCount += plusMinus;\n\tif (instance._elements.length === 0 && instance._loadingCount === 0) {\n\t\tcallbackIfSet(instance._settings.callback_finish);\n\t}\n};\n\nconst getSourceTags = parentTag => {\n\tlet sourceTags = [];\n\tfor (let i = 0, childTag; (childTag = parentTag.children[i]); i += 1) {\n\t\tif (childTag.tagName === \"SOURCE\") {\n\t\t\tsourceTags.push(childTag);\n\t\t}\n\t}\n\treturn sourceTags;\n};\n\nconst setAttributeIfValue = (element, attrName, value) => {\n\tif (!value) {\n\t\treturn;\n\t}\n\telement.setAttribute(attrName, value);\n};\n\nconst setImageAttributes = (element, settings) => {\n\tsetAttributeIfValue(\n\t\telement,\n\t\t\"sizes\",\n\t\tgetData(element, settings.data_sizes)\n\t);\n\tsetAttributeIfValue(\n\t\telement,\n\t\t\"srcset\",\n\t\tgetData(element, settings.data_srcset)\n\t);\n\tsetAttributeIfValue(element, \"src\", getData(element, settings.data_src));\n};\n\nconst setSourcesImg = (element, settings) => {\n\tconst parent = element.parentNode;\n\n\tif (parent && parent.tagName === \"PICTURE\") {\n\t\tlet sourceTags = getSourceTags(parent);\n\t\tsourceTags.forEach(sourceTag => {\n\t\t\tsetImageAttributes(sourceTag, settings);\n\t\t});\n\t}\n\n\tsetImageAttributes(element, settings);\n};\n\nconst setSourcesIframe = (element, settings) => {\n\tsetAttributeIfValue(element, \"src\", getData(element, settings.data_src));\n};\n\nconst setSourcesVideo = (element, settings) => {\n\tlet sourceTags = getSourceTags(element);\n\tsourceTags.forEach(sourceTag => {\n\t\tsetAttributeIfValue(\n\t\t\tsourceTag,\n\t\t\t\"src\",\n\t\t\tgetData(sourceTag, settings.data_src)\n\t\t);\n\t});\n\tsetAttributeIfValue(element, \"src\", getData(element, settings.data_src));\n\telement.load();\n};\n\nconst setSourcesBgImage = (element, settings) => {\n\tconst srcDataValue = getData(element, settings.data_src);\n\tconst bgDataValue = getData(element, settings.data_bg);\n\n\tif (srcDataValue) {\n\t\telement.style.backgroundImage = `url(\"${srcDataValue}\")`;\n\t}\n\n\tif (bgDataValue) {\n\t\telement.style.backgroundImage = bgDataValue;\n\t}\n};\n\nconst setSourcesFunctions = {\n\tIMG: setSourcesImg,\n\tIFRAME: setSourcesIframe,\n\tVIDEO: setSourcesVideo\n};\n\nconst setSources = (element, instance) => {\n\tconst settings = instance._settings;\n\tconst tagName = element.tagName;\n\tconst setSourcesFunction = setSourcesFunctions[tagName];\n\tif (setSourcesFunction) {\n\t\tsetSourcesFunction(element, settings);\n\t\tupdateLoadingCount(instance, 1);\n\t\tinstance._elements = purgeOneElement(instance._elements, element);\n\t\treturn;\n\t}\n\tsetSourcesBgImage(element, settings);\n};\n\nconst addClass = (element, className) => {\n\tif (supportsClassList) {\n\t\telement.classList.add(className);\n\t\treturn;\n\t}\n\telement.className += (element.className ? \" \" : \"\") + className;\n};\n\nconst removeClass = (element, className) => {\n\tif (supportsClassList) {\n\t\telement.classList.remove(className);\n\t\treturn;\n\t}\n\telement.className = element.className.\n\t\treplace(new RegExp(\"(^|\\\\s+)\" + className + \"(\\\\s+|$)\"), \" \").\n\t\treplace(/^\\s+/, \"\").\n\t\treplace(/\\s+$/, \"\");\n};\n\nconst genericLoadEventName = \"load\";\nconst mediaLoadEventName = \"loadeddata\";\nconst errorEventName = \"error\";\n\nconst addEventListener = (element, eventName, handler) => {\n\telement.addEventListener(eventName, handler);\n};\n\nconst removeEventListener = (element, eventName, handler) => {\n\telement.removeEventListener(eventName, handler);\n};\n\nconst addEventListeners = (element, loadHandler, errorHandler) => {\n\taddEventListener(element, genericLoadEventName, loadHandler);\n\taddEventListener(element, mediaLoadEventName, loadHandler);\n\taddEventListener(element, errorEventName, errorHandler);\n};\n\nconst removeEventListeners = (element, loadHandler, errorHandler) => {\n\tremoveEventListener(element, genericLoadEventName, loadHandler);\n\tremoveEventListener(element, mediaLoadEventName, loadHandler);\n\tremoveEventListener(element, errorEventName, errorHandler);\n};\n\nconst eventHandler = function(event, success, instance) {\n\tvar settings = instance._settings;\n\tconst className = success ? settings.class_loaded : settings.class_error;\n\tconst callback = success\n\t\t? settings.callback_loaded\n\t\t: settings.callback_error;\n\tconst element = event.target;\n\n\tremoveClass(element, settings.class_loading);\n\taddClass(element, className);\n\tcallbackIfSet(callback, element);\n\n\tupdateLoadingCount(instance, -1);\n};\n\nconst addOneShotEventListeners = (element, instance) => {\n\tconst loadHandler = event => {\n\t\teventHandler(event, true, instance);\n\t\tremoveEventListeners(element, loadHandler, errorHandler);\n\t};\n\tconst errorHandler = event => {\n\t\teventHandler(event, false, instance);\n\t\tremoveEventListeners(element, loadHandler, errorHandler);\n\t};\n\taddEventListeners(element, loadHandler, errorHandler);\n};\n\nconst managedTags = [\"IMG\", \"IFRAME\", \"VIDEO\"];\n\nconst onEnter = (element, instance) => {\n\tconst settings = instance._settings;\n\tcallbackIfSet(settings.callback_enter, element);\n\tif (!settings.load_delay) {\n\t\trevealAndUnobserve(element, instance);\n\t\treturn;\n\t}\n\tdelayLoad(element, instance);\n};\n\nconst revealAndUnobserve = (element, instance) => {\n\tvar observer = instance._observer;\n\trevealElement(element, instance);\n\tif (observer && instance._settings.auto_unobserve) {\n\t\tobserver.unobserve(element);\n\t}\n};\n\nconst onExit = (element, instance) => {\n\tconst settings = instance._settings;\n\tcallbackIfSet(settings.callback_exit, element);\n\tif (!settings.load_delay) {\n\t\treturn;\n\t}\n\tcancelDelayLoad(element);\n};\n\nconst cancelDelayLoad = element => {\n\tvar timeoutId = getTimeoutData(element);\n\tif (!timeoutId) {\n\t\treturn; // do nothing if timeout doesn't exist\n\t}\n\tclearTimeout(timeoutId);\n\tsetTimeoutData(element, null);\n};\n\nconst delayLoad = (element, instance) => {\n\tvar loadDelay = instance._settings.load_delay;\n\tvar timeoutId = getTimeoutData(element);\n\tif (timeoutId) {\n\t\treturn; // do nothing if timeout already set\n\t}\n\ttimeoutId = setTimeout(function() {\n\t\trevealAndUnobserve(element, instance);\n\t\tcancelDelayLoad(element);\n\t}, loadDelay);\n\tsetTimeoutData(element, timeoutId);\n};\n\nconst revealElement = (element, instance, force) => {\n\tvar settings = instance._settings;\n\tif (!force && getWasProcessedData(element)) {\n\t\treturn; // element has already been processed and force wasn't true\n\t}\n\tif (managedTags.indexOf(element.tagName) > -1) {\n\t\taddOneShotEventListeners(element, instance);\n\t\taddClass(element, settings.class_loading);\n\t}\n\tsetSources(element, instance);\n\tsetWasProcessedData(element);\n\tcallbackIfSet(settings.callback_reveal, element);\n\tcallbackIfSet(settings.callback_set, element);\n};\n\nconst isIntersecting = entry =>\n\tentry.isIntersecting || entry.intersectionRatio > 0;\n\nconst getObserverSettings = settings => ({\n\troot: settings.container === document ? null : settings.container,\n\trootMargin: settings.thresholds || settings.threshold + \"px\"\n});\n\nconst setObserver = instance => {\n\tif (!supportsIntersectionObserver) {\n\t\treturn false;\n\t}\n\tinstance._observer = new IntersectionObserver(entries => {\n\t\tentries.forEach(entry =>\n\t\t\tisIntersecting(entry)\n\t\t\t\t? onEnter(entry.target, instance)\n\t\t\t\t: onExit(entry.target, instance)\n\t\t);\n\t}, getObserverSettings(instance._settings));\n\treturn true;\n};\n\nconst LazyLoad = function(customSettings, elements) {\n\tthis._settings = getInstanceSettings(customSettings);\n\tthis._loadingCount = 0;\n\tsetObserver(this);\n\tthis.update(elements);\n};\n\nLazyLoad.prototype = {\n\tupdate: function(elements) {\n\t\tconst settings = this._settings;\n\t\tconst _elements =\n\t\t\telements ||\n\t\t\tsettings.container.querySelectorAll(settings.elements_selector);\n\n\t\tthis._elements = purgeProcessedElements(\n\t\t\tArray.prototype.slice.call(_elements) // NOTE: nodeset to array for IE compatibility\n\t\t);\n\n\t\tif (isBot || !this._observer) {\n\t\t\tthis.loadAll();\n\t\t\treturn;\n\t\t}\n\n\t\tthis._elements.forEach(element => {\n\t\t\tthis._observer.observe(element);\n\t\t});\n\t},\n\n\tdestroy: function() {\n\t\tif (this._observer) {\n\t\t\tthis._elements.forEach(element => {\n\t\t\t\tthis._observer.unobserve(element);\n\t\t\t});\n\t\t\tthis._observer = null;\n\t\t}\n\t\tthis._elements = null;\n\t\tthis._settings = null;\n\t},\n\n\tload: function(element, force) {\n\t\trevealElement(element, this, force);\n\t},\n\n\tloadAll: function() {\n\t\tvar elements = this._elements;\n\t\telements.forEach(element => {\n\t\t\trevealAndUnobserve(element, this);\n\t\t});\n\t}\n};\n\n/* Automatic instances creation if required (useful for async script loading) */\nif (runningOnBrowser) {\n\tautoInitialize(LazyLoad, window.lazyLoadOptions);\n}\n\nreturn LazyLoad;\n\n})));\n"]}