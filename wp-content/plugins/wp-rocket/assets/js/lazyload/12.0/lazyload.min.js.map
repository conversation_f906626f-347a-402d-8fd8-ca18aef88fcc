{"version": 3, "sources": ["lazyload.js"], "names": ["global", "factory", "exports", "_typeof", "module", "define", "amd", "LazyLoad", "this", "running<PERSON>n<PERSON><PERSON><PERSON>", "window", "isBot", "test", "defaultSettings", "elements_selector", "threshold", "thresholds", "document", "createElement", "data_srcset", "data_sizes", "data_bg", "class_loading", "class_loaded", "class_error", "load_delay", "auto_unobserve", "callback_enter", "callback_exit", "callback_reveal", "callback_loaded", "callback_error", "callback_finish", "use_native", "getInstanceSettings", "createInstance", "classObj", "options", "event", "detail", "instance", "CustomEvent", "err", "createEvent", "eventString", "dispatchEvent", "dataPrefix", "element", "attribute", "processedDataName", "setData", "value", "attrName", "getData", "removeAttribute", "getWasProcessedData", "setTimeoutData", "setWasProcessedData", "getTimeoutData", "callback", "argument", "updateLoadingCount", "_loadingCount", "plusMinus", "_elements", "length", "callbackIfSet", "_settings", "getSourceTags", "parentTag", "childTag", "sourceTags", "i", "children", "setAttribute", "setAttributeIfValue", "tagName", "settings", "data_src", "backgroundImage", "IMG", "parent", "parentNode", "for<PERSON>ach", "sourceTag", "setImageAttributes", "IFRAME", "VIDEO", "load", "setSources", "elements", "setSourcesFunctions", "setSourcesFunction", "setSourcesVideo", "timeoutDataName", "srcDataValue", "bgDataValue", "setSourcesBgImage", "addClass", "className", "addEventListener", "eventName", "handler", "removeEventListener", "genericLoadEventName", "<PERSON><PERSON><PERSON><PERSON>", "removeClass", "loadHandler", "addEventListeners", "errorEventName", "success", "removeEventListeners", "target", "supportsClassList", "remove", "replace", "RegExp", "<PERSON><PERSON><PERSON><PERSON>", "addOneShotEventListeners", "revealAndUnobserve", "observer", "unobserve", "cancelDelayLoad", "timeoutId", "clearTimeout", "loadDelay", "revealElement", "force", "managedTags", "indexOf", "setTimeout", "callback_set", "setObserver", "_observer", "IntersectionObserver", "entries", "entry", "delayLoad", "onEnter", "root", "container", "rootMargin", "isIntersecting", "nativeLazyTags", "getElements", "trueString", "purgeProcessedElements", "getObserverSettings", "querySelectorAll", "queryElements", "shouldUseNative", "customSettings", "loadAllNative", "prototype", "update", "_this", "nodeSetToArray", "loadAll", "destroy", "_this2", "_this3", "autoInitialize", "optionsItem"], "mappings": "+cAAC,SAAUA,EAAQC,GACC,YAAnB,oBAAOC,QAAP,YAAAC,QAAOD,WAA0C,oBAAXE,OAAyBA,OAAOF,QAAUD,IAC9D,mBAAXI,QAAyBA,OAAOC,IAAMD,OAAOJ,GACnDD,EAAOO,SAAWN,IAHnB,CAAAO,KAAA,WAAkBP,aAIlB,IAACQ,EAAmB,oBAAAC,OAIfC,EAFNF,KAAsB,aAAGC,SAGvBD,oBADIE,WAKN,gCAAkCC,KACjCH,UAAAA,WAKKI,EACLC,GAAiB,yBADMJ,OAGvBK,EACAC,GAJuB,cAAAC,SAAAC,cAAA,KAMvBC,EAAa,CACbC,kBAAY,MACZC,UAAOV,GARgBF,EAAAQ,SAAA,KASvBK,UAAAA,IACAC,WAAAA,KACAC,SAAAA,MACAC,YAAU,SACVC,WAAAA,QACAC,QAAAA,KACAC,cAAAA,UACAC,aAAAA,SACAC,YAAAA,QACAC,WAAAA,EACAC,gBAAAA,EACAC,eAAY,KApBWL,cAAxB,KAgBCC,gBAAiB,KAOlBC,gBAAII,KACHH,eAAO,KACPC,gBAFD,KAIAC,YAAA,GAKCE,EAAI,SAAAC,EAAAC,GACH,IAAAC,EACuCC,EAAQ,IAAAH,EAAAC,GAAEG,IAAZF,EAArC,IAAAG,YAAYA,wBAAZ,CAAAF,OAAA,CAAAC,SAAAA,KACA,MAACE,IAEDJ,EAAAA,SAAQrB,YAAS0B,gBACjBL,gBAJYG,yBAIUG,GAAa,EAAO,CAA1CJ,SAAAA,IAAiD9B,OAAjDmC,cAAAP,IAiBCH,IAKGW,EAAU,SAAAC,EAAhBC,GACA,OAAMC,EAAAA,aANHd,QAMHa,IAQME,EAAU,SAACH,EAASC,EAAWG,GAJrC,IAAAC,EAVGjB,QAUGkB,EACEN,OAAPI,EAIAJ,EAAIK,aAAWN,EAAaE,GAH5BD,EAFDO,gBAAAF,IAgBMG,EAAsB,SAAAR,GAAO,MAvBlC,SAiBAA,EAAAA,EAnBE,kBA4BGS,EAAiB,SAACT,EAASI,GAAV,OANvBD,EAAMO,EArBJ,aAqB0BN,IAA5BO,EAAA,SAAAX,GAAA,OAAAM,EAAAN,EArBE,eA8BIW,EAAc,SAAGC,EAAjBD,GAAwBC,GAAAA,EAA9BC,IAG+BC,EAAKN,SAAAA,EAAoBR,GAAzBP,EAA9BsB,eAAAC,EADD,IAAAvB,EAAAwB,UAAAC,QAAA,IAAAzB,EAAAsB,eAiBEI,EAAc1B,EAAS2B,UAAUnC,kBAZlCoC,EAAA,SAAAC,GAkBA,IAjBA,IAiBgBC,EAnBjBC,EAAA,GAmBUC,EAAI,EAAcF,EAAWD,EAAUI,SAASD,GAAKA,GAAK,EAf9C,WAAhBN,EAAAA,SACLK,EAAIZ,KAAUW,GAGd,OAJDC,GAOC/B,EAAA,SAAAO,EAA0BgB,EAA1BZ,GAiBKA,GAdJJ,EAAA2B,aAAAtB,EAAAD,IAGIiB,EAAgB,SAAArB,EAAhBqB,GACLO,EAkBC5B,EAjBD,QACCM,EAAIiB,EAASM,EAATxD,aAEHuD,EACD5B,EAmBA,SAlBDM,EAAAN,EAAOwB,EAAPpD,cAqBAwD,EAAoB5B,EAAS,MAAOM,EAAQN,EAAS8B,EAASC,YAsC7D/B,EAAcgC,CACdC,IAvDA,SAAAjC,EAAA8B,GACA,IAAAI,EAAAlC,EAAAmC,WACDnC,GAAA,YAAQ2B,EAAAA,SAJTN,EAAAa,GA0BaE,QAAQ,SAAAC,GAnBrBC,EAAMA,EAAqBR,KAY1BQ,EAZDtC,EAAA8B,IA2DCS,OA5CuBJ,SAAAA,EAAvBL,GAaAF,EAAoB5B,EAAS,MAAOM,EAAQN,EAAS8B,EAASC,YAwB9DS,MAjCC,SAAmBxC,EAAA8B,GAClBQ,EAAmBD,GADpBD,QAAA,SAAAC,GAGAT,EAaCS,EAXFC,MAVDhC,EAAA+B,EAAAP,EAAAC,aAcCH,EAAAA,EAAoB5B,MAASM,EAAOA,EAAQN,EAAS8B,WACrD9B,EAFDyC,SAiBAC,EAAA,SAAA1C,EAAAP,GAoBC,IA3GsBkD,EAAjBlC,EA2GCqB,EAAWrC,EAAS2B,UAPrBwB,EAAAA,EAAmBf,QACrBgB,EADwBD,EAAAf,GAE3BU,GAAAA,EAID,OAHCC,EAAOM,EAAAA,GAHoBhC,EAA5BrB,EAAA,QAaEA,EAASwB,WAjHY0B,EAiHgBlD,EAASwB,UAjH1CR,EAiHqDT,EAjHpC2C,EACtBxC,OAAQH,SAAAA,GAAS+C,OAAAA,IAAiB3C,OA0EjBiB,SAAAA,EAAcrB,GAC/BwB,IAAAA,EAAAlB,EAAmBN,EAAAqC,EAAaN,UAC/BH,EAAAA,EACCS,EACAP,EACAxB,SAGFsB,IACA5B,EAAAA,MAAAgC,gBAAAhC,QAAAA,OAAAgD,EAAAhD,OAGDiD,IACCjD,EAAMgD,MAAAA,gBAAuBhD,GAoB7BkD,CAAgBlD,EAAQ6B,IAExBsB,EAAIN,SAAAA,EAAJO,GACCP,EACA/B,EAAAA,UAAAA,IAAkBsC,GAGlBpD,EAAAoD,YAAApD,EAAAoD,UAAA,IAAA,IAAAA,GAgBAC,EAAA,SAAArD,EAAAsD,EAAAC,GAYDvD,EAAQqD,iBAAiBC,EAAWC,IAG/BC,EAAsB,SAACxD,EAASsD,EAAWC,GARjDvD,EAAMyD,oBAAuBH,EAA7BC,IASCvD,EAAQwD,SAAAA,EAAoBF,EAA5BI,GACAF,EAFDxD,EAnBM2D,OAmBNC,GAYCJ,EAAoBxD,EA9BG,aA8B0B4D,GARlDJ,EAAMK,EArBJ,QAqBIA,IAGLR,EAAiBrD,SAAS8D,EAAAA,EAAgBJ,GAC1C,IAJD5B,EAAArC,EAAA2B,UAcOgC,EAAYW,EAAUjC,EAAStD,aAAesD,EAASrD,YARxDuF,EAAAA,EACLR,EAAAA,gBACAA,EAAAA,eACAA,EAAAA,EAAmBS,QArDpB,SAAAjE,EAAAoD,GAsBKc,EATLlE,EAAMmD,UAAWgB,OAAXhB,GAGJnD,EAAAoD,UAAApD,EAAAoD,UACAgB,QAAA,IAAAC,OAAA,WAAAjB,EAAA,YAAA,KAWAgB,QAAQ,OAAQ,IAVjBpE,QAAAA,OAAQoD,IA8CRO,CAAY3D,EAAS8B,EAASvD,eAR/B4E,EAAMmB,EAAYlB,GACjBjC,EAAIW,EAAWrC,GAEfqB,EAAiBiD,GACdjC,IAKHqB,EAAA,SAAAnD,EAAAP,GACA0B,IAAAA,EAAcP,SAAdO,EAAcP,GAEdE,EAAAA,GAAkB,EAACrB,GACnBuE,EAbDhE,EAAA4D,EAAAF,IAeMa,EAAAA,SAAAA,EAAAA,GACLD,EAAMV,GAAc,EAAAnE,GACnB6E,EAAa/E,EAAaE,EAA1BiE,KA7BwB,SAAC1D,EAAS4D,EAAaF,GARjDL,EAAMA,EAfAM,OAeAN,GACLrD,EAAQqD,EAfe,aAevBO,GACAP,EAFDrD,EAbE,QAaF0D,GAuCEG,CAHD7D,EAAA4D,EAAAF,IAKCY,EAAa/E,CAAAA,MAAO,SAAOE,SAkBvB+E,EAAqB,SAACxE,EAASP,GAPpC,IAAAgF,EAAK3C,EAASpD,UACb8F,EAAAA,EAAkB/E,GAClBgF,GAAAhF,EAAA2B,UAAAzC,gBACA8F,EAAAC,UAAA1E,IAqBI2E,EAAkB,SAAA3E,GATxB,IAAA4E,EAAejE,EAACX,GACf4E,IAEAC,aAAK/C,GACJrB,EAAAT,EAAA,QAED2E,EAAAA,SAAe3E,EAAfP,GACA,IAPDqF,EAAArF,EAAA2B,UAAA1C,WAoBKkG,EAAYjE,EAAeX,GAXhC4E,IAECA,EAAKA,WAAW,WACfJ,EAAQxE,EAAAP,GACRkF,EAAA3E,IAcE8E,GAbHD,EAAY7E,EAAC4E,KAiBRG,EAAgB,SAAC/E,EAASP,EAAUuF,GAb1C,IAAAlD,EAAerC,EAAG2B,WACjB4D,GAAIF,EAAqB1D,KAEzB6D,EAAAC,QAAelF,EAAA6B,UAAA,IACd0C,EAAQvE,EAAAP,GACR0D,EAAAnD,EAAA8B,EAAAvD,gBACDqG,EAAAA,EAAYO,GArPH5E,SAAAA,GACRJ,EAAAH,EAjBC,gBAEF,QAoQCwE,CAAAA,GACAG,EAAAA,EAAgB3E,gBAAhBA,GACAmB,EAAE2D,EAHHM,aAAApF,KAYAqF,EAAgBH,SAAAA,GACfX,QAAAA,IAqBD9E,EAAS6F,UAAY,IAAIC,qBAAqB,SAAAC,GAlB9C9C,EAAAA,QAAW1C,SAAAA,GAAD,OAMY,SAAAyF,GAAK,OAf5BA,EAAMV,gBAAgBU,EAAhBV,kBAAiB/E,EAUtBU,CAAAA,GApEA,SAAAV,EAAAP,GAUA,IAAMqC,EAAWrC,EAAS2B,UAN1ByC,EAAAA,EAAkB7D,eAAS4D,GAT5B9B,EAAApD,WAcAgH,EAAMC,EAAUlG,GAId+E,EAAmBxE,EAASP,GAwD7B0B,CAAAA,EAAcW,OAAShD,GA1CT,SAACkB,EAASP,GARzB,IAAM+E,EAAAA,EAAqBpD,UAC1BD,EAAIsD,EAAWhF,cAAfO,GACA+E,EAAAA,YAECN,EAASC,GA+CVvD,CAAAA,EAAcW,OAASsD,MAMiB,CAhBxCQ,MAgB2B9D,EAeJrC,EAAS2B,WA/B5ByE,YAAUrF,SAAoBR,KAAU8B,EAAA+D,UAC3CC,WAD2ChE,EACnC7D,YAAA6D,EAAA9D,UAAA,QAYJ+H,GAGsB,IAAAjE,GAmBtBkE,EAAiB,CAAC,MAAO,UAoBzBC,EAAc,SAACtD,EAAUb,GAAX,OA5SQ,SAAAa,GAAO,OAAAA,EAClCrC,OAAQN,SAAAA,GAASE,OAAAA,EAAuBgG,KAuRzCC,EARGJ,EAQoBpD,GAJnByD,SAAAA,GAAoB3G,OACvBqC,EAAA+D,UAAAQ,iBAAAvE,EAAA/D,mBAGDuI,CAAAxE,GARkB2D,MACZE,UAAQF,MAAMxB,KAAQxE,KADzBsG,IAAAA,GAUGQ,EAAAA,SAAkBC,EAAlBD,GAA0B9I,KAAA2D,UA/UT,SAAAoF,GACtB,OAAIjH,SAAJ,GAAAzB,EAAA0I,GA+UA1E,CAAuB0E,GADQ/I,KAAhCsD,cAAA,EAwBCsE,EAAY5H,MArBbA,KAAMgJ,OAAAA,IAqCJ,OAlCCjJ,EAAAkJ,UAAA,CACAC,OAAA,SAAAhE,GAAA,IA5BiClD,EA4BjCmH,EAAAnJ,KAuBGqE,EAAWrE,KAAK2D,WAtBpBpB,KAAAA,UAAQ2B,EAAagB,EAAWb,IAChCiD,GAAAA,KAAc/E,YA9ByB,SAAA8B,GAAA,OACxC8D,EAAI1G,YAAW2G,YAAc3H,iBAAkB4D,UAiC1C+E,CAAc/E,MAlCgBrC,EAkCIiH,MAlCxCzF,UAAAmB,QAAA,SAAApC,IA0BmD,IAA7CgG,EAAed,QAAQlF,EAAQ6B,WAnBnC7B,EAAA2B,aAAA,UAAA,QACAoD,EAAA/E,EAAAP,MA0BFhC,KAAAwD,UAAAgF,EAAAtD,EAAAb,IAEArE,KAAM6I,UAAAA,QAAgB,SAAAtG,GAAQ4G,EAAAtB,UAC7BxD,QAAS+D,MANRpI,KANDqJ,WAcmBC,QAAA,WACnBZ,IAAAA,EAAAA,KADD1I,KAAA6H,YA6BG7H,KAAKwD,UAAUmB,QAAQ,SAAApC,GA1B1BgH,EAAMxJ,UAAWkH,UAAXlH,KAELC,KAAA6H,UAAKvE,MAELtD,KAAAwD,UAAY0B,KACZlF,KALD2D,UAAA,MAQCuF,KAAAA,SAAQ3G,EAAAgF,GAAmBD,EAAA/E,EAAAvC,KAAAuH,IAE1B8B,QAAA,WAAA,IAAAG,EAAAxJ,KA8BAA,KAAKwD,UAAUmB,QAAQ,SAAApC,GA7BvBwE,EAAcxE,EAAKsF,OAKlBmB,GArWH,SAAApH,EAAAC,GAGC,GAAKA,EADN,GAAAA,EAAS4H,OAIR,IAAA,IAAahG,EAAR5B,EAAAA,EAAgB6H,EAAA7H,EAAAmC,GAAAA,GAAA,EACpBrC,EAAAC,EAAA8H,QAHA/H,EAAAC,EAAAC,GAkWC4H,CAAKjG,EAAYgF,OAAAA,iBAElBzI", "file": "lazyload.min.js", "sourcesContent": ["(function (global, factory) {\n\ttypeof exports === 'object' && typeof module !== 'undefined' ? module.exports = factory() :\n\ttypeof define === 'function' && define.amd ? define(factory) :\n\t(global.LazyLoad = factory());\n}(this, (function () { 'use strict';\n\nconst runningOnBrowser = typeof window !== \"undefined\";\r\n\r\nconst isBot =\r\n\t(runningOnBrowser && !(\"onscroll\" in window)) ||\r\n\t(typeof navigator !== \"undefined\" &&\r\n\t\t/(gle|ing|ro)bot|crawl|spider/i.test(navigator.userAgent));\r\n\r\nconst supportsIntersectionObserver =\r\n\trunningOnBrowser && \"IntersectionObserver\" in window;\r\n\r\nconst supportsClassList =\r\n\trunningOnBrowser && \"classList\" in document.createElement(\"p\");\n\nconst defaultSettings = {\r\n\telements_selector: \"img\",\r\n\tcontainer: isBot || runningOnBrowser ? document : null,\r\n\tthreshold: 300,\r\n\tthresholds: null,\r\n\tdata_src: \"src\",\r\n\tdata_srcset: \"srcset\",\r\n\tdata_sizes: \"sizes\",\r\n\tdata_bg: \"bg\",\r\n\tclass_loading: \"loading\",\r\n\tclass_loaded: \"loaded\",\r\n\tclass_error: \"error\",\r\n\tload_delay: 0,\r\n\tauto_unobserve: true,\r\n\tcallback_enter: null,\r\n\tcallback_exit: null,\r\n\tcallback_reveal: null,\r\n\tcallback_loaded: null,\r\n\tcallback_error: null,\r\n\tcallback_finish: null,\r\n\tuse_native: false\r\n};\r\n\r\nvar getInstanceSettings = customSettings => {\r\n\treturn Object.assign({}, defaultSettings, customSettings);\r\n};\n\n/* Creates instance and notifies it through the window element */\r\nconst createInstance = function(classObj, options) {\r\n\tvar event;\r\n\tlet eventString = \"LazyLoad::Initialized\";\r\n\tlet instance = new classObj(options);\r\n\ttry {\r\n\t\t// Works in modern browsers\r\n\t\tevent = new CustomEvent(eventString, { detail: { instance } });\r\n\t} catch (err) {\r\n\t\t// Works in Internet Explorer (all versions)\r\n\t\tevent = document.createEvent(\"CustomEvent\");\r\n\t\tevent.initCustomEvent(eventString, false, false, { instance });\r\n\t}\r\n\twindow.dispatchEvent(event);\r\n};\r\n\r\n/* Auto initialization of one or more instances of lazyload, depending on the \r\n    options passed in (plain object or an array) */\r\nfunction autoInitialize(classObj, options) {\r\n\tif (!options) {\r\n\t\treturn;\r\n\t}\r\n\tif (!options.length) {\r\n\t\t// Plain object\r\n\t\tcreateInstance(classObj, options);\r\n\t} else {\r\n\t\t// Array of objects\r\n\t\tfor (let i = 0, optionsItem; (optionsItem = options[i]); i += 1) {\r\n\t\t\tcreateInstance(classObj, optionsItem);\r\n\t\t}\r\n\t}\r\n}\n\nconst dataPrefix = \"data-\";\r\nconst processedDataName = \"was-processed\";\r\nconst timeoutDataName = \"ll-timeout\";\r\nconst trueString = \"true\";\r\n\r\nconst getData = (element, attribute) => {\r\n\treturn element.getAttribute(dataPrefix + attribute);\r\n};\r\n\r\nconst setData = (element, attribute, value) => {\r\n\tvar attrName = dataPrefix + attribute;\r\n\tif (value === null) {\r\n\t\telement.removeAttribute(attrName);\r\n\t\treturn;\r\n\t}\r\n\telement.setAttribute(attrName, value);\r\n};\r\n\r\nconst setWasProcessedData = element =>\r\n\tsetData(element, processedDataName, trueString);\r\n\r\nconst getWasProcessedData = element =>\r\n\tgetData(element, processedDataName) === trueString;\r\n\r\nconst setTimeoutData = (element, value) =>\r\n\tsetData(element, timeoutDataName, value);\r\n\r\nconst getTimeoutData = element => getData(element, timeoutDataName);\n\nconst purgeProcessedElements = elements => {\r\n\treturn elements.filter(element => !getWasProcessedData(element));\r\n};\r\n\r\nconst purgeOneElement = (elements, elementToPurge) => {\r\n\treturn elements.filter(element => element !== elementToPurge);\r\n};\n\nconst callbackIfSet = (callback, argument) => {\r\n\tif (callback) {\r\n\t\tcallback(argument);\r\n\t}\r\n};\n\nconst updateLoadingCount = (instance, plusMinus) => {\r\n\tinstance._loadingCount += plusMinus;\r\n\tif (instance._elements.length === 0 && instance._loadingCount === 0) {\r\n\t\tcallbackIfSet(instance._settings.callback_finish);\r\n\t}\r\n};\n\nconst getSourceTags = parentTag => {\r\n\tlet sourceTags = [];\r\n\tfor (let i = 0, childTag; (childTag = parentTag.children[i]); i += 1) {\r\n\t\tif (childTag.tagName === \"SOURCE\") {\r\n\t\t\tsourceTags.push(childTag);\r\n\t\t}\r\n\t}\r\n\treturn sourceTags;\r\n};\r\n\r\nconst setAttributeIfValue = (element, attrName, value) => {\r\n\tif (!value) {\r\n\t\treturn;\r\n\t}\r\n\telement.setAttribute(attrName, value);\r\n};\r\n\r\nconst setImageAttributes = (element, settings) => {\r\n\tsetAttributeIfValue(\r\n\t\telement,\r\n\t\t\"sizes\",\r\n\t\tgetData(element, settings.data_sizes)\r\n\t);\r\n\tsetAttributeIfValue(\r\n\t\telement,\r\n\t\t\"srcset\",\r\n\t\tgetData(element, settings.data_srcset)\r\n\t);\r\n\tsetAttributeIfValue(element, \"src\", getData(element, settings.data_src));\r\n};\r\n\r\nconst setSourcesImg = (element, settings) => {\r\n\tconst parent = element.parentNode;\r\n\r\n\tif (parent && parent.tagName === \"PICTURE\") {\r\n\t\tlet sourceTags = getSourceTags(parent);\r\n\t\tsourceTags.forEach(sourceTag => {\r\n\t\t\tsetImageAttributes(sourceTag, settings);\r\n\t\t});\r\n\t}\r\n\r\n\tsetImageAttributes(element, settings);\r\n};\r\n\r\nconst setSourcesIframe = (element, settings) => {\r\n\tsetAttributeIfValue(element, \"src\", getData(element, settings.data_src));\r\n};\r\n\r\nconst setSourcesVideo = (element, settings) => {\r\n\tlet sourceTags = getSourceTags(element);\r\n\tsourceTags.forEach(sourceTag => {\r\n\t\tsetAttributeIfValue(\r\n\t\t\tsourceTag,\r\n\t\t\t\"src\",\r\n\t\t\tgetData(sourceTag, settings.data_src)\r\n\t\t);\r\n\t});\r\n\tsetAttributeIfValue(element, \"src\", getData(element, settings.data_src));\r\n\telement.load();\r\n};\r\n\r\nconst setSourcesBgImage = (element, settings) => {\r\n\tconst srcDataValue = getData(element, settings.data_src);\r\n\tconst bgDataValue = getData(element, settings.data_bg);\r\n\r\n\tif (srcDataValue) {\r\n\t\telement.style.backgroundImage = `url(\"${srcDataValue}\")`;\r\n\t}\r\n\r\n\tif (bgDataValue) {\r\n\t\telement.style.backgroundImage = bgDataValue;\r\n\t}\r\n};\r\n\r\nconst setSourcesFunctions = {\r\n\tIMG: setSourcesImg,\r\n\tIFRAME: setSourcesIframe,\r\n\tVIDEO: setSourcesVideo\r\n};\r\n\r\nconst setSources = (element, instance) => {\r\n\tconst settings = instance._settings;\r\n\tconst tagName = element.tagName;\r\n\tconst setSourcesFunction = setSourcesFunctions[tagName];\r\n\tif (setSourcesFunction) {\r\n\t\tsetSourcesFunction(element, settings);\r\n\t\tupdateLoadingCount(instance, 1);\r\n\t\tinstance._elements = purgeOneElement(instance._elements, element);\r\n\t\treturn;\r\n\t}\r\n\tsetSourcesBgImage(element, settings);\r\n};\n\nconst addClass = (element, className) => {\r\n\tif (supportsClassList) {\r\n\t\telement.classList.add(className);\r\n\t\treturn;\r\n\t}\r\n\telement.className += (element.className ? \" \" : \"\") + className;\r\n};\r\n\r\nconst removeClass = (element, className) => {\r\n\tif (supportsClassList) {\r\n\t\telement.classList.remove(className);\r\n\t\treturn;\r\n\t}\r\n\telement.className = element.className.\r\n\t\treplace(new RegExp(\"(^|\\\\s+)\" + className + \"(\\\\s+|$)\"), \" \").\r\n\t\treplace(/^\\s+/, \"\").\r\n\t\treplace(/\\s+$/, \"\");\r\n};\n\nconst genericLoadEventName = \"load\";\r\nconst mediaLoadEventName = \"loadeddata\";\r\nconst errorEventName = \"error\";\r\n\r\nconst addEventListener = (element, eventName, handler) => {\r\n\telement.addEventListener(eventName, handler);\r\n};\r\n\r\nconst removeEventListener = (element, eventName, handler) => {\r\n\telement.removeEventListener(eventName, handler);\r\n};\r\n\r\nconst addEventListeners = (element, loadHandler, errorHandler) => {\r\n\taddEventListener(element, genericLoadEventName, loadHandler);\r\n\taddEventListener(element, mediaLoadEventName, loadHandler);\r\n\taddEventListener(element, errorEventName, errorHandler);\r\n};\r\n\r\nconst removeEventListeners = (element, loadHandler, errorHandler) => {\r\n\tremoveEventListener(element, genericLoadEventName, loadHandler);\r\n\tremoveEventListener(element, mediaLoadEventName, loadHandler);\r\n\tremoveEventListener(element, errorEventName, errorHandler);\r\n};\r\n\r\nconst eventHandler = function(event, success, instance) {\r\n\tvar settings = instance._settings;\r\n\tconst className = success ? settings.class_loaded : settings.class_error;\r\n\tconst callback = success\r\n\t\t? settings.callback_loaded\r\n\t\t: settings.callback_error;\r\n\tconst element = event.target;\r\n\r\n\tremoveClass(element, settings.class_loading);\r\n\taddClass(element, className);\r\n\tcallbackIfSet(callback, element);\r\n\r\n\tupdateLoadingCount(instance, -1);\r\n};\r\n\r\nconst addOneShotEventListeners = (element, instance) => {\r\n\tconst loadHandler = event => {\r\n\t\teventHandler(event, true, instance);\r\n\t\tremoveEventListeners(element, loadHandler, errorHandler);\r\n\t};\r\n\tconst errorHandler = event => {\r\n\t\teventHandler(event, false, instance);\r\n\t\tremoveEventListeners(element, loadHandler, errorHandler);\r\n\t};\r\n\taddEventListeners(element, loadHandler, errorHandler);\r\n};\n\nconst managedTags = [\"IMG\", \"IFRAME\", \"VIDEO\"];\r\n\r\nconst onEnter = (element, instance) => {\r\n\tconst settings = instance._settings;\r\n\tcallbackIfSet(settings.callback_enter, element);\r\n\tif (!settings.load_delay) {\r\n\t\trevealAndUnobserve(element, instance);\r\n\t\treturn;\r\n\t}\r\n\tdelayLoad(element, instance);\r\n};\r\n\r\nconst revealAndUnobserve = (element, instance) => {\r\n\tvar observer = instance._observer;\r\n\trevealElement(element, instance);\r\n\tif (observer && instance._settings.auto_unobserve) {\r\n\t\tobserver.unobserve(element);\r\n\t}\r\n};\r\n\r\nconst onExit = (element, instance) => {\r\n\tconst settings = instance._settings;\r\n\tcallbackIfSet(settings.callback_exit, element);\r\n\tif (!settings.load_delay) {\r\n\t\treturn;\r\n\t}\r\n\tcancelDelayLoad(element);\r\n};\r\n\r\nconst cancelDelayLoad = element => {\r\n\tvar timeoutId = getTimeoutData(element);\r\n\tif (!timeoutId) {\r\n\t\treturn; // do nothing if timeout doesn't exist\r\n\t}\r\n\tclearTimeout(timeoutId);\r\n\tsetTimeoutData(element, null);\r\n};\r\n\r\nconst delayLoad = (element, instance) => {\r\n\tvar loadDelay = instance._settings.load_delay;\r\n\tvar timeoutId = getTimeoutData(element);\r\n\tif (timeoutId) {\r\n\t\treturn; // do nothing if timeout already set\r\n\t}\r\n\ttimeoutId = setTimeout(function() {\r\n\t\trevealAndUnobserve(element, instance);\r\n\t\tcancelDelayLoad(element);\r\n\t}, loadDelay);\r\n\tsetTimeoutData(element, timeoutId);\r\n};\r\n\r\nconst revealElement = (element, instance, force) => {\r\n\tvar settings = instance._settings;\r\n\tif (!force && getWasProcessedData(element)) {\r\n\t\treturn; // element has already been processed and force wasn't true\r\n\t}\r\n\tif (managedTags.indexOf(element.tagName) > -1) {\r\n\t\taddOneShotEventListeners(element, instance);\r\n\t\taddClass(element, settings.class_loading);\r\n\t}\r\n\tsetSources(element, instance);\r\n\tsetWasProcessedData(element);\r\n\tcallbackIfSet(settings.callback_reveal, element);\r\n\tcallbackIfSet(settings.callback_set, element);\r\n};\n\nconst isIntersecting = entry =>\r\n\tentry.isIntersecting || entry.intersectionRatio > 0;\r\n\r\nconst getObserverSettings = settings => ({\r\n\troot: settings.container === document ? null : settings.container,\r\n\trootMargin: settings.thresholds || settings.threshold + \"px\"\r\n});\r\n\r\nconst setObserver = instance => {\r\n\tif (!supportsIntersectionObserver) {\r\n\t\treturn false;\r\n\t}\r\n\tinstance._observer = new IntersectionObserver(entries => {\r\n\t\tentries.forEach(entry =>\r\n\t\t\tisIntersecting(entry)\r\n\t\t\t\t? onEnter(entry.target, instance)\r\n\t\t\t\t: onExit(entry.target, instance)\r\n\t\t);\r\n\t}, getObserverSettings(instance._settings));\r\n\treturn true;\r\n};\n\nconst nativeLazyTags = [\"IMG\", \"IFRAME\"];\r\n\r\nconst shouldUseNative = settings =>\r\n\tsettings.use_native && \"loading\" in HTMLImageElement.prototype;\r\n\r\nconst loadAllNative = instance => {\r\n\tinstance._elements.forEach(element => {\r\n\t\tif (nativeLazyTags.indexOf(element.tagName) === -1) {\r\n\t\t\treturn;\r\n\t\t}\r\n\t\telement.setAttribute(\"loading\", \"lazy\");\r\n\t\trevealElement(element, instance);\r\n\t});\r\n};\n\nconst nodeSetToArray = nodeSet => Array.prototype.slice.call(nodeSet);\n\nconst queryElements = settings =>\r\n\tsettings.container.querySelectorAll(settings.elements_selector);\r\n\r\nconst getElements = (elements, settings) =>\r\n\tpurgeProcessedElements(nodeSetToArray(elements || queryElements(settings)));\n\nconst LazyLoad = function(customSettings, elements) {\r\n\tthis._settings = getInstanceSettings(customSettings);\r\n\tthis._loadingCount = 0;\r\n\tsetObserver(this);\r\n\tthis.update(elements);\r\n};\r\n\r\nLazyLoad.prototype = {\r\n\tupdate: function(elements) {\r\n\t\tvar settings = this._settings;\r\n\t\tthis._elements = getElements(elements, settings);\r\n\t\tif (isBot || !this._observer) {\r\n\t\t\tthis.loadAll();\r\n\t\t\treturn;\r\n\t\t}\r\n\t\tif (shouldUseNative(settings)) {\r\n\t\t\tloadAllNative(this);\r\n\t\t\tthis._elements = getElements(elements, settings);\r\n\t\t}\r\n\t\tthis._elements.forEach(element => {\r\n\t\t\tthis._observer.observe(element);\r\n\t\t});\r\n\t},\r\n\r\n\tdestroy: function() {\r\n\t\tif (this._observer) {\r\n\t\t\tthis._elements.forEach(element => {\r\n\t\t\t\tthis._observer.unobserve(element);\r\n\t\t\t});\r\n\t\t\tthis._observer = null;\r\n\t\t}\r\n\t\tthis._elements = null;\r\n\t\tthis._settings = null;\r\n\t},\r\n\r\n\tload: function(element, force) {\r\n\t\trevealElement(element, this, force);\r\n\t},\r\n\r\n\tloadAll: function() {\r\n\t\tthis._elements.forEach(element => {\r\n\t\t\trevealAndUnobserve(element, this);\r\n\t\t});\r\n\t}\r\n};\r\n\r\n/* Automatic instances creation if required (useful for async script loading) */\r\nif (runningOnBrowser) {\r\n\tautoInitialize(LazyLoad, window.lazyLoadOptions);\r\n}\n\nreturn LazyLoad;\n\n})));\n"]}