{"version": 3, "names": [], "mappings": "", "sources": ["lcp-beacon.min.js"], "sourcesContent": ["!function n(r,s,o){function c(t,e){if(!s[t]){if(!r[t]){var i=\"function\"==typeof require&&require;if(!e&&i)return i(t,!0);if(a)return a(t,!0);throw(e=new Error(\"Cannot find module '\"+t+\"'\")).code=\"MODULE_NOT_FOUND\",e}i=s[t]={exports:{}},r[t][0].call(i.exports,function(e){return c(r[t][1][e]||e)},i,i.exports,n,r,s,o)}return s[t].exports}for(var a=\"function\"==typeof require&&require,e=0;e<o.length;e++)c(o[e]);return c}({1:[function(e,t,i){\"use strict\";(class n{constructor(e){this.config=e,this.performanceImages=[],this.errorCode=\"\",this.scriptTimer=new Date,this.infiniteLoopId=null}async init(){if(await this._isValidPreconditions()){this.infiniteLoopId=setTimeout(()=>{this._handleInfiniteLoop()},1e4);try{var e=this._generateLcpCandidates(1/0);e&&(this._initWithFirstElementWithInfo(e),this._fillATFWithoutDuplications(e))}catch(e){this.errorCode=\"script_error\",this._logMessage(\"Script Error: \"+e)}this._saveFinalResultIntoDB()}else this._finalize()}async _isValidPreconditions(){return this._isNotValidScreensize()?(this._logMessage(\"Bailing out because screen size is not acceptable\"),!1):!this._isPageCached()||!await this._isGeneratedBefore()||(this._logMessage(\"Bailing out because data is already available\"),!1)}_isPageCached(){var e=document.documentElement.nextSibling&&document.documentElement.nextSibling.data?document.documentElement.nextSibling.data:\"\";return e&&e.includes(\"Debug: cached\")}async _isGeneratedBefore(){var e=new FormData;return e.append(\"action\",\"rocket_check_lcp\"),e.append(\"rocket_lcp_nonce\",this.config.nonce),e.append(\"url\",this.config.url),e.append(\"is_mobile\",this.config.is_mobile),(e=await fetch(this.config.ajax_url,{method:\"POST\",credentials:\"same-origin\",body:e}).then(e=>e.json())).success}_isNotValidScreensize(){var e=window.innerWidth||document.documentElement.clientWidth,t=window.innerHeight||document.documentElement.clientHeight,i=this.config.is_mobile&&(e>this.config.width_threshold||t>this.config.height_threshold),e=!this.config.is_mobile&&(e<this.config.width_threshold||t<this.config.height_threshold);return i||e}_generateLcpCandidates(e){var t=document.querySelectorAll(this.config.elements);return t.length<=0?[]:Array.from(t).map(e=>{if(\"img\"===e.nodeName.toLowerCase()&&\"picture\"===e.parentElement.nodeName.toLowerCase())return null;let t;if(\"picture\"===e.nodeName.toLowerCase()){var i=e.querySelector(\"img\");if(!i)return null;t=i.getBoundingClientRect()}else t=e.getBoundingClientRect();return{element:e,rect:t}}).filter(e=>null!==e).filter(e=>0<e.rect.width&&0<e.rect.height&&this._isIntersecting(e.rect)).map(e=>({item:e,area:this._getElementArea(e.rect),elementInfo:this._getElementInfo(e.element)})).sort((e,t)=>t.area-e.area).slice(0,e).map(e=>({element:e.item.element,elementInfo:e.elementInfo}))}_isIntersecting(e){return 0<=e.bottom&&0<=e.right&&e.top<=(window.innerHeight||document.documentElement.clientHeight)&&e.left<=(window.innerWidth||document.documentElement.clientWidth)}_getElementArea(e){return Math.min(e.width,(window.innerWidth||document.documentElement.clientWidth)-e.left)*Math.min(e.height,(window.innerHeight||document.documentElement.clientHeight)-e.top)}_getElementInfo(e){var t=e.nodeName.toLowerCase(),i={type:\"\",src:\"\",srcset:\"\",sizes:\"\",sources:[],bg_set:[],current_src:\"\"};if(\"img\"===t&&e.srcset)i.type=\"img-srcset\",i.src=e.src,i.srcset=e.srcset,i.sizes=e.sizes,i.current_src=e.currentSrc;else if(\"img\"===t)i.type=\"img\",i.src=e.src,i.current_src=e.currentSrc;else if(\"video\"===t){i.type=\"img\";var n=e.querySelector(\"source\");i.src=e.poster||(n?n.src:\"\"),i.current_src=i.src}else if(\"svg\"===t)(n=e.querySelector(\"image\"))&&(i.type=\"img\",i.src=n.getAttribute(\"href\")||\"\",i.current_src=i.src);else if(\"picture\"===t)i.type=\"picture\",n=e.querySelector(\"img\"),i.src=n?n.src:\"\",i.sources=Array.from(e.querySelectorAll(\"source\")).map(e=>({srcset:e.srcset||\"\",media:e.media||\"\",type:e.type||\"\",sizes:e.sizes||\"\"}));else{if(0===(t=[window.getComputedStyle(e,null).getPropertyValue(\"background-image\"),getComputedStyle(e,\":after\").getPropertyValue(\"background-image\"),getComputedStyle(e,\":before\").getPropertyValue(\"background-image\")].filter(e=>\"none\"!==e)).length)return null;if(n=t[0],i.type=\"bg-img\",n.includes(\"image-set(\")&&(i.type=\"bg-img-set\"),!n||\"\"===n||n.includes(\"data:image\"))return null;e=[...n.matchAll(/url\\(\\s*?['\"]?\\s*?(.+?)\\s*?[\"']?\\s*?\\)/gi)],i.bg_set=e.map(e=>e[1]?{src:e[1].trim()+(e[2]?\" \"+e[2].trim():\"\")}:{}),i.bg_set.every(e=>\"\"===e.src)&&(i.bg_set=e.map(e=>e[1]?{src:e[1].trim()}:{})),0<i.bg_set.length&&(i.src=i.bg_set[0].src,\"bg-img-set\"===i.type)&&(i.src=i.bg_set)}return i}_initWithFirstElementWithInfo(e){(e=e.find(e=>null!==e.elementInfo))?this.performanceImages=[{...e.elementInfo,label:\"lcp\"}]:(this._logMessage(\"No LCP candidate found.\"),this.performanceImages=[])}_fillATFWithoutDuplications(e){e.forEach(e=>{var{element:e,elementInfo:t}=e;!this._isDuplicateImage(e)&&t&&this.performanceImages.push({...t,label:\"above-the-fold\"})})}_isDuplicateImage(e){const t=this._getElementInfo(e);var i;return null!==t&&(e=\"img\"===t.type||\"img-srcset\"===t.type||\"video\"===t.type,i=\"bg-img\"===t.type||\"bg-img-set\"===t.type||\"picture\"===t.type,e||i)&&this.performanceImages.some(e=>e.src===t.src)}_getFinalStatus(){return\"\"!==this.errorCode?this.errorCode:10<=(new Date-this.scriptTimer)/1e3?\"timeout\":\"success\"}_saveFinalResultIntoDB(){var e=new FormData;e.append(\"action\",\"rocket_lcp\"),e.append(\"rocket_lcp_nonce\",this.config.nonce),e.append(\"url\",this.config.url),e.append(\"is_mobile\",this.config.is_mobile),e.append(\"images\",JSON.stringify(this.performanceImages)),e.append(\"status\",this._getFinalStatus()),fetch(this.config.ajax_url,{method:\"POST\",credentials:\"same-origin\",body:e,headers:{\"wpr-saas-no-intercept\":!0}}).then(e=>e.json()).then(e=>{this._logMessage(e)}).catch(e=>{this._logMessage(e)}).finally(()=>{this._finalize()})}_handleInfiniteLoop(){this._saveFinalResultIntoDB()}_finalize(){document.querySelector('[data-name=\"wpr-lcp-beacon\"]').setAttribute(\"beacon-completed\",\"true\"),clearTimeout(this.infiniteLoopId)}_logMessage(e){this.config.debug&&console.log(e)}static run(){if(window.rocket_lcp_data){const e=new n(window.rocket_lcp_data);\"loading\"!==document.readyState?setTimeout(()=>{e.init()},window.rocket_lcp_data.delay):document.addEventListener(\"DOMContentLoaded\",()=>{setTimeout(()=>{e.init()},window.rocket_lcp_data.delay)})}}}).run()},{}]},{},[1]);"], "file": "lcp-beacon.min.js"}