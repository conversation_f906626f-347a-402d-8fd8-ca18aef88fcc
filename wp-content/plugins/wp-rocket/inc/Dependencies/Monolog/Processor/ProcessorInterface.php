<?php

/*
 * This file is part of the WP_Rocket\Dependencies\Monolog package.
 *
 * (c) <PERSON><PERSON> <j.boggia<PERSON>@seld.be>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace WP_Rocket\Dependencies\Monolog\Processor;

/**
 * An optional interface to allow labelling WP_Rocket\Dependencies\Monolog processors.
 *
 * <AUTHOR> <<EMAIL>>
 */
interface ProcessorInterface
{
    /**
     * @return array The processed records
     */
    public function __invoke(array $records);
}
