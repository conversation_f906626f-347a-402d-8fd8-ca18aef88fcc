<?php

namespace WP_Rocket\Dependencies\PathConverter;

/**
 * Convert file paths.
 *
 * Please report bugs on https://github.com/matthias<PERSON><PERSON>/path-converter/issues
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright Copyright (c) 2015, <PERSON>. All rights reserved
 * @license MIT License
 */
interface ConverterInterface
{
    /**
     * Convert file paths.
     *
     * @param string $path The path to be converted
     *
     * @return string The new path
     */
    public function convert($path);
}
