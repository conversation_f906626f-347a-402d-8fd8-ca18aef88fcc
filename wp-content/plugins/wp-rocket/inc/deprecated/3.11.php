<?php

/**
 * Add Yoast SEO sitemap option to WP Rocket default options
 *
 * @since 2.8
 * @since 3.11.1 deprecated
 *
 * <AUTHOR> <PERSON>ona
 *
 * @param array $options WP Rocket options array.
 * @return array Updated WP Rocket options array
 */
function rocket_add_yoast_seo_sitemap_option( $options ) {
	_deprecated_function( __FUNCTION__ . '()', '3.11.1' );

	$options['yoast_xml_sitemap'] = 0;

	return $options;
}

/**
 * Sanitize Yoast SEO sitemap option value
 *
 * @since 2.8
 * @since 3.11.1 deprecated
 *
 * <AUTHOR> Perona
 *
 * @param array $inputs WP Rocket inputs array.
 * @return array Sanitized WP Rocket inputs array
 */
function rocket_yoast_seo_sitemap_option_sanitize( $inputs ) {
	_deprecated_function( __FUNCTION__ . '()', '3.11.1' );

	$inputs['yoast_xml_sitemap'] = ! empty( $inputs['yoast_xml_sitemap'] ) ? 1 : 0;

	return $inputs;
}

/**
 * Add Yoast SEO sitemap URL to the sitemaps to preload
 *
 * @since 2.8
 * @since 3.11.1 deprecated
 *
 * <AUTHOR> <PERSON>ona
 *
 * @param array $sitemaps Sitemaps to preload.
 * @return array Updated Sitemaps to preload
 */
function rocket_add_yoast_seo_sitemap( $sitemaps ) {
	_deprecated_function( __FUNCTION__ . '()', '3.11.1' );

	if ( get_rocket_option( 'yoast_xml_sitemap', false ) ) {
		$sitemaps[] = WPSEO_Sitemaps_Router::get_base_url( 'sitemap_index.xml' );
	}

	return $sitemaps;
}

/**
 * Add Yoast SEO option to WP Rocket settings
 *
 * @since 2.8
 * @since 3.11.1 deprecated
 *
 * <AUTHOR> Perona
 *
 * @param array $options WP Rocket settings array.
 * @return array Updated WP Rocket settings array
 */
function rocket_sitemap_preload_yoast_seo_option( $options ) {
	_deprecated_function( __FUNCTION__ . '()', '3.11.1' );

	$options['yoast_xml_sitemap'] = [
		'type'              => 'checkbox',
		'container_class'   => [
			'wpr-field--children',
		],
		'label'             => __( 'Yoast SEO XML sitemap', 'rocket' ),
		// translators: %s = Name of the plugin.
		'description'       => sprintf( __( 'We automatically detected the sitemap generated by the %s plugin. You can check the option to preload it.', 'rocket' ), 'Yoast SEO' ),
		'parent'            => 'sitemap_preload',
		'section'           => 'preload_section',
		'page'              => 'preload',
		'default'           => 0,
		'sanitize_callback' => 'sanitize_checkbox',
	];

	return $options;
}

/**
 * Clear Kinsta cache when clearing WP Rocket cache
 *
 * @since 3.0
 * <AUTHOR> Perona
 *
 * @return void
 */
function rocket_clean_kinsta_cache() {
	global $kinsta_cache;
	_deprecated_function( __FUNCTION__ . '()', '3.11.1' );

	if ( ! empty( $kinsta_cache->kinsta_cache_purge ) ) {
		$kinsta_cache->kinsta_cache_purge->purge_complete_caches();
	}
}

/**
 * Partially clear Kinsta cache when partially clearing WP Rocket cache
 *
 * @since 3.0
 * <AUTHOR> Perona
 *
 * @param object $post Post object.
 * @return void
 */
function rocket_clean_kinsta_post_cache( $post ) {
	_deprecated_function( __FUNCTION__ . '()', '3.11.1' );
	global $kinsta_cache;
	$kinsta_cache->kinsta_cache_purge->initiate_purge( $post->ID, 'post' );
}


/**
 * Clears Kinsta cache for the homepage URL when using "Purge this URL" from the admin bar on the front end
 *
 * @since 3.0.4
 * <AUTHOR> Perona
 *
 * @param string $root WP Rocket root cache path.
 * @param string $lang Current language.
 * @return void
 */
function rocket_clean_kinsta_cache_home( $root = '', $lang = '' ) {
	_deprecated_function( __FUNCTION__ . '()', '3.11.1' );
	$url = get_rocket_i18n_home_url( $lang );
	$url = trailingslashit( $url ) . 'kinsta-clear-cache/';

	wp_remote_get(
		$url,
		[
			'blocking' => false,
			'timeout'  => 0.01,
		]
	);
}

/**
 * Clears Kinsta cache for a specific URL when using "Purge this URL" from the admin bar on the front end
 *
 * @since 3.0.4
 * <AUTHOR> Perona
 *
 * @param string $url URL to purge.
 * @return void
 */
function rocket_clean_kinsta_cache_url( $url ) {
	_deprecated_function( __FUNCTION__ . '()', '3.11.1' );
	$url = trailingslashit( $url ) . 'kinsta-clear-cache/';

	wp_remote_get(
		$url,
		[
			'blocking' => false,
			'timeout'  => 0.01,
		]
	);
}

/**
 * Remove WP Rocket functions on WP core action hooks to prevent triggering a double cache clear.
 *
 * @since 3.0
 * <AUTHOR> Perona
 *
 * @return void
 */
function rocket_remove_partial_purge_hooks() {
	_deprecated_function( __FUNCTION__ . '()', '3.11.1' );
	// WP core action hooks rocket_clean_post() gets hooked into.
	$clean_post_hooks = [
		// Disables the refreshing of partial cache when content is edited.
		'wp_trash_post',
		'delete_post',
		'clean_post_cache',
		'wp_update_comment_count',
	];

	// Remove rocket_clean_post() from core action hooks.
	array_map(
		function( $hook ) {
			remove_action( $hook, 'rocket_clean_post' );
		},
		$clean_post_hooks
	);

	remove_filter( 'rocket_clean_files', 'rocket_clean_files_users' );
}

/**
 * Do the rollback
 *
 * @since 3.11.5 deprecated
 * @since 2.4
 */
function rocket_rollback() {
	_deprecated_function( __FUNCTION__ . '()', '3.11.5' );
	if ( ! isset( $_GET['_wpnonce'] ) || ! wp_verify_nonce( sanitize_key( $_GET['_wpnonce'] ), 'rocket_rollback' ) ) {
		wp_nonce_ays( '' );
	}

	/**
	 * Fires before doing the rollback
	 */
	do_action( 'rocket_before_rollback' );

	$plugin_transient = get_site_transient( 'update_plugins' );
	$plugin_folder    = plugin_basename( dirname( WP_ROCKET_FILE ) );
	$plugin           = $plugin_folder . '/' . basename( WP_ROCKET_FILE );

	$plugin_transient->response[ $plugin ] = (object) [
		'slug'        => $plugin_folder,
		'new_version' => WP_ROCKET_LASTVERSION,
		'url'         => 'https://wp-rocket.me',
		'package'     => sprintf( 'https://wp-rocket.me/%s/wp-rocket_%s.zip', get_rocket_option( 'consumer_key' ), WP_ROCKET_LASTVERSION ),
	];

	set_site_transient( 'update_plugins', $plugin_transient );

	require_once ABSPATH . 'wp-admin/includes/class-wp-upgrader.php';

	// translators: %s is the plugin name.
	$title         = sprintf( __( '%s Update Rollback', 'rocket' ), WP_ROCKET_PLUGIN_NAME );
	$nonce         = 'upgrade-plugin_' . $plugin;
	$url           = 'update.php?action=upgrade-plugin&plugin=' . rawurlencode( $plugin );
	$upgrader_skin = new Plugin_Upgrader_Skin( compact( 'title', 'nonce', 'url', 'plugin' ) );
	$upgrader      = new Plugin_Upgrader( $upgrader_skin );

	remove_filter( 'site_transient_update_plugins', 'rocket_check_update', 1 );
	add_filter( 'update_plugin_complete_actions', 'rocket_rollback_add_return_link' );
	rocket_put_content( WP_CONTENT_DIR . '/advanced-cache.php', '' );

	$upgrader->upgrade( $plugin );

	wp_die(
		'',
		// translators: %s is the plugin name.
		esc_html( sprintf( __( '%s Update Rollback', 'rocket' ), WP_ROCKET_PLUGIN_NAME ) ),
		[
			'response' => 200,
		]
	);
}

/**
 * After a rollback has been done, replace the "return to" link by a link pointing to WP Rocket's tools page.
 * A link to the plugins page is kept in case the plugin is not reactivated correctly.
 *
 * @since 3.11.5 deprecated
 * @since  3.2.4
 * <AUTHOR> Viguier
 * <AUTHOR> Basil Lal
 *
 * @param  array $update_actions Array of plugin action links.
 * @return array                 The array of links where the "return to" link has been replaced.
 */
function rocket_rollback_add_return_link( $update_actions ) {
	_deprecated_function( __FUNCTION__ . '()', '3.11.5' );

	if ( ! isset( $update_actions['plugins_page'] ) ) {
		return $update_actions;
	}

	$update_actions['plugins_page'] = sprintf(
		/* translators: 1 and 3 are link openings, 2 is a link closing. */
		__( '%1$sReturn to WP Rocket%2$s or %3$sgo to Plugins page%2$s', 'rocket' ),
		'<a href="' . esc_url( admin_url( 'options-general.php?page=' . WP_ROCKET_PLUGIN_SLUG ) . '#tools' ) . '" target="_parent">',
		'</a>',
		'<a href="' . esc_url( admin_url( 'plugins.php' ) ) . '" target="_parent">'
	);

	return $update_actions;
}
