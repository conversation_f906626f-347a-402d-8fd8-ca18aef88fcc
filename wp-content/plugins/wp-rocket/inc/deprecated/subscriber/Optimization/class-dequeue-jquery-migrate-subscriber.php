<?php
namespace WP_Rocket\Subscriber\Optimization;

use WP_Rocket\deprecated\DeprecatedClassTrait;
use WP_Rocket\Event_Management\Subscriber_Interface;
use WP_Rocket\Admin\Options_Data as Options;
use WP_Scripts;

/**
 * Dequeue jQuery Migrate
 *
 * @since 3.5
 * <AUTHOR> <PERSON>
 */
class Dequeue_JQuery_Migrate_Subscriber implements Subscriber_Interface {
	use DeprecatedClassTrait;
	/**
	 * Plugin options
	 *
	 * @since 3.5
	 * <AUTHOR> <PERSON>
	 *
	 * @var Options
	 */
	private $options;

	/**
	 * Constructor
	 *
	 * @since 3.5
	 * <AUTHOR> <PERSON>
	 *
	 * @param Options $options Plugin options.
	 */
	public function __construct( Options $options ) {
		self::deprecated_class( '3.8' );
		$this->options = $options;
	}

	/**
	 * {@inheritdoc}
	 */
	public static function get_subscribed_events() {
		return [
			'wp_default_scripts' => [ 'dequeue_jquery_migrate' ],
		];
	}

	/**
	 * Dequeue jquery migrate
	 *
	 * @since 3.5
	 * <AUTHOR>
	 *
	 * @param WP_Scripts $scripts WP_Scripts instance.
	 * @return bool|void
	 */
	public function dequeue_jquery_migrate( $scripts ) {
		if ( ! $this->is_allowed() ) {
			return false;
		}

		if ( ! empty( $scripts->registered['jquery'] ) ) {
			$jquery_dependencies                 = $scripts->registered['jquery']->deps;
			$scripts->registered['jquery']->deps = array_diff( $jquery_dependencies, [ 'jquery-migrate' ] );
		}
	}

	/**
	 * Check if dequeue jquery migrate option is enabled
	 *
	 * @since 3.5
	 * <AUTHOR> Cristina
	 *
	 * @return boolean
	 */
	protected function is_allowed() {
		if ( rocket_get_constant( 'DONOTROCKETOPTIMIZE', false ) ) {
			return false;
		}

		if ( ! $this->options->get( 'dequeue_jquery_migrate' ) ) {
			return false;
		}

		return true;
	}
}
