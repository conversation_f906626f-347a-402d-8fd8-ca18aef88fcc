<?php

defined( 'ABSPATH' ) || exit;

if ( class_exists( 'FlywheelNginxCompat' ) ) {
	require WP_ROCKET_3RD_PARTY_PATH . 'hosting/flywheel.php';
}

if ( defined( 'DB_HOST' ) && strpos( DB_HOST, '.wpserveur.net' ) !== false ) {
	require WP_ROCKET_3RD_PARTY_PATH . 'hosting/wp-serveur.php';
}

if ( rocket_is_plugin_active( 'sg-cachepress/sg-cachepress.php' ) ) {
	require WP_ROCKET_3RD_PARTY_PATH . 'hosting/siteground.php';
}

if ( defined( 'PL_INSTANCE_REF' ) && class_exists( '\Presslabs\Cache\CacheHandler' ) && file_exists( WP_CONTENT_DIR . '/advanced-cache.php' ) ) {
	require WP_ROCKET_3RD_PARTY_PATH . 'hosting/presslabs.php';
}

require WP_ROCKET_3RD_PARTY_PATH . 'hosting/pagely.php';
require WP_ROCKET_3RD_PARTY_PATH . 'hosting/nginx.php';

require WP_ROCKET_3RD_PARTY_PATH . 'plugins/slider/meta-slider.php';
require WP_ROCKET_3RD_PARTY_PATH . 'plugins/slider/soliloquy.php';
require WP_ROCKET_3RD_PARTY_PATH . 'plugins/i18n/polylang.php';
require WP_ROCKET_3RD_PARTY_PATH . 'plugins/ecommerce/aelia-currencyswitcher.php';
require WP_ROCKET_3RD_PARTY_PATH . 'plugins/ecommerce/aelia-prices-by-country.php';
require WP_ROCKET_3RD_PARTY_PATH . 'plugins/ecommerce/aelia-tax-display-by-country.php';
require WP_ROCKET_3RD_PARTY_PATH . 'plugins/ecommerce/woocommerce-multilingual.php';
require WP_ROCKET_3RD_PARTY_PATH . 'plugins/ecommerce/woocommerce-currency-converter-widget.php';
require WP_ROCKET_3RD_PARTY_PATH . 'plugins/ecommerce/edd-software-licencing.php';
require WP_ROCKET_3RD_PARTY_PATH . 'plugins/ecommerce/easy-digital-downloads.php';
require WP_ROCKET_3RD_PARTY_PATH . 'plugins/ecommerce/ithemes-exchange.php';
require WP_ROCKET_3RD_PARTY_PATH . 'plugins/ecommerce/jigoshop.php';
require WP_ROCKET_3RD_PARTY_PATH . 'plugins/ecommerce/wpshop.php';
require WP_ROCKET_3RD_PARTY_PATH . 'plugins/ecommerce/give.php';
require WP_ROCKET_3RD_PARTY_PATH . 'plugins/autoptimize.php';
require WP_ROCKET_3RD_PARTY_PATH . 'plugins/envira-gallery.php';
require WP_ROCKET_3RD_PARTY_PATH . 'plugins/cookies/cookie-notice.php';
require WP_ROCKET_3RD_PARTY_PATH . 'plugins/cookies/uk-cookie-consent.php';
require WP_ROCKET_3RD_PARTY_PATH . 'plugins/cookies/eu-cookie-law.php';
require WP_ROCKET_3RD_PARTY_PATH . 'plugins/cookies/weepie-cookie-allow.php';
require WP_ROCKET_3RD_PARTY_PATH . 'plugins/cookies/gdpr.php';
require WP_ROCKET_3RD_PARTY_PATH . 'plugins/rating/kk-star-ratings.php';
require WP_ROCKET_3RD_PARTY_PATH . 'plugins/rating/wp-postratings.php';
require WP_ROCKET_3RD_PARTY_PATH . 'plugins/wp-print.php';
require WP_ROCKET_3RD_PARTY_PATH . 'plugins/buddypress.php';
require WP_ROCKET_3RD_PARTY_PATH . 'plugins/disqus.php';
require WP_ROCKET_3RD_PARTY_PATH . 'plugins/custom-login.php';
require WP_ROCKET_3RD_PARTY_PATH . 'plugins/mobile/wp-appkit.php';
require WP_ROCKET_3RD_PARTY_PATH . 'plugins/seo/premium-seo-pack.php';
require WP_ROCKET_3RD_PARTY_PATH . 'plugins/wp-rest-api.php';
require WP_ROCKET_3RD_PARTY_PATH . 'plugins/page-builder/thrive-visual-editor.php';
require WP_ROCKET_3RD_PARTY_PATH . 'plugins/page-builder/visual-composer.php';
require WP_ROCKET_3RD_PARTY_PATH . 'plugins/security/secupress.php';
require WP_ROCKET_3RD_PARTY_PATH . 'plugins/security/sf-move-login.php';
require WP_ROCKET_3RD_PARTY_PATH . 'plugins/security/wps-hide-login.php';
require WP_ROCKET_3RD_PARTY_PATH . 'plugins/varnish-http-purge.php';
require WP_ROCKET_3RD_PARTY_PATH . 'plugins/thrive-leads.php';
require WP_ROCKET_3RD_PARTY_PATH . 'plugins/mailchimp.php';
require WP_ROCKET_3RD_PARTY_PATH . 'plugins/advanced-custom-fields.php';
require WP_ROCKET_3RD_PARTY_PATH . 'plugins/wp-offload-s3.php';
require WP_ROCKET_3RD_PARTY_PATH . 'plugins/wp-offload-s3-assets.php';
require WP_ROCKET_3RD_PARTY_PATH . 'plugins/s2member.php';
require WP_ROCKET_3RD_PARTY_PATH . 'plugins/sumome.php';
require WP_ROCKET_3RD_PARTY_PATH . 'plugins/nginx-helper.php';

require WP_ROCKET_3RD_PARTY_PATH . 'themes/studiopress.php';
