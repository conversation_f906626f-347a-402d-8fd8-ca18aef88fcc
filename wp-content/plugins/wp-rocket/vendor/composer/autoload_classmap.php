<?php

// autoload_classmap.php @generated by Composer

$vendorDir = dirname(__DIR__);
$baseDir = dirname($vendorDir);

return array(
    'CloudFlare\\IpRewrite' => $vendorDir . '/cloudflare/cf-ip-rewrite/src/CloudFlare/IpRewrite.php',
    'CloudFlare\\IpUtils' => $vendorDir . '/cloudflare/cf-ip-rewrite/src/CloudFlare/IpUtils.php',
    'Composer\\InstalledVersions' => $vendorDir . '/composer/InstalledVersions.php',
    'Imagify_Partner' => $baseDir . '/inc/vendors/classes/class-imagify-partner.php',
    'Minify_CSS_UriRewriter' => $baseDir . '/inc/vendors/classes/class-minify-css-urirewriter.php',
    'Minify_HTML' => $baseDir . '/inc/deprecated/vendors/classes/class-minify-html.php',
    'WPMedia\\Cloudflare\\Auth\\APIKeyFactory' => $baseDir . '/inc/Addon/Cloudflare/Auth/APIKeyFactory.php',
    'WPMedia\\Cloudflare\\Auth\\AuthFactoryInterface' => $baseDir . '/inc/Addon/Cloudflare/Auth/AuthFactoryInterface.php',
    'WP_Rocket\\Abstract_Render' => $baseDir . '/inc/classes/class-abstract-render.php',
    'WP_Rocket\\Addon\\Busting\\BustingFactory' => $baseDir . '/inc/deprecated/Engine/Addon/Busting/BustingFactory.php',
    'WP_Rocket\\Addon\\Busting\\FileBustingTrait' => $baseDir . '/inc/deprecated/Engine/Addon/Busting/FileBustingTrait.php',
    'WP_Rocket\\Addon\\Cloudflare\\API\\Client' => $baseDir . '/inc/Addon/Cloudflare/API/Client.php',
    'WP_Rocket\\Addon\\Cloudflare\\API\\Endpoints' => $baseDir . '/inc/Addon/Cloudflare/API/Endpoints.php',
    'WP_Rocket\\Addon\\Cloudflare\\Admin\\Subscriber' => $baseDir . '/inc/Addon/Cloudflare/Admin/Subscriber.php',
    'WP_Rocket\\Addon\\Cloudflare\\Auth\\APIKey' => $baseDir . '/inc/Addon/Cloudflare/Auth/APIKey.php',
    'WP_Rocket\\Addon\\Cloudflare\\Auth\\AuthInterface' => $baseDir . '/inc/Addon/Cloudflare/Auth/AuthInterface.php',
    'WP_Rocket\\Addon\\Cloudflare\\Cloudflare' => $baseDir . '/inc/Addon/Cloudflare/Cloudflare.php',
    'WP_Rocket\\Addon\\Cloudflare\\ServiceProvider' => $baseDir . '/inc/Addon/Cloudflare/ServiceProvider.php',
    'WP_Rocket\\Addon\\Cloudflare\\Subscriber' => $baseDir . '/inc/Addon/Cloudflare/Subscriber.php',
    'WP_Rocket\\Addon\\FacebookTracking\\Subscriber' => $baseDir . '/inc/deprecated/Engine/Addon/FacebookTracking/Subscriber.php',
    'WP_Rocket\\Addon\\GoogleTracking\\GoogleAnalytics' => $baseDir . '/inc/deprecated/Engine/Addon/GoogleTracking/GoogleAnalytics.php',
    'WP_Rocket\\Addon\\GoogleTracking\\GoogleTagManager' => $baseDir . '/inc/deprecated/Engine/Addon/GoogleTracking/GoogleTagManager.php',
    'WP_Rocket\\Addon\\GoogleTracking\\Subscriber' => $baseDir . '/inc/deprecated/Engine/Addon/GoogleTracking/Subscriber.php',
    'WP_Rocket\\Addon\\ServiceProvider' => $baseDir . '/inc/Addon/ServiceProvider.php',
    'WP_Rocket\\Addon\\Sucuri\\Subscriber' => $baseDir . '/inc/Addon/Sucuri/Subscriber.php',
    'WP_Rocket\\Addon\\Varnish\\ServiceProvider' => $baseDir . '/inc/Addon/Varnish/ServiceProvider.php',
    'WP_Rocket\\Addon\\Varnish\\Subscriber' => $baseDir . '/inc/Addon/Varnish/Subscriber.php',
    'WP_Rocket\\Addon\\Varnish\\Varnish' => $baseDir . '/inc/Addon/Varnish/Varnish.php',
    'WP_Rocket\\Addon\\WebP\\AbstractWebp' => $baseDir . '/inc/Addon/WebP/AbstractWebp.php',
    'WP_Rocket\\Addon\\WebP\\AdminSubscriber' => $baseDir . '/inc/Addon/WebP/AdminSubscriber.php',
    'WP_Rocket\\Addon\\WebP\\Subscriber' => $baseDir . '/inc/Addon/WebP/Subscriber.php',
    'WP_Rocket\\Admin\\Abstract_Options' => $baseDir . '/inc/classes/admin/class-abstract-options.php',
    'WP_Rocket\\Admin\\Logs' => $baseDir . '/inc/classes/admin/class-logs.php',
    'WP_Rocket\\Admin\\Options' => $baseDir . '/inc/classes/admin/class-options.php',
    'WP_Rocket\\Admin\\Options_Data' => $baseDir . '/inc/classes/admin/class-options-data.php',
    'WP_Rocket\\Buffer\\Abstract_Buffer' => $baseDir . '/inc/classes/Buffer/class-abstract-buffer.php',
    'WP_Rocket\\Buffer\\Cache' => $baseDir . '/inc/classes/Buffer/class-cache.php',
    'WP_Rocket\\Buffer\\Config' => $baseDir . '/inc/classes/Buffer/class-config.php',
    'WP_Rocket\\Buffer\\Tests' => $baseDir . '/inc/classes/Buffer/class-tests.php',
    'WP_Rocket\\Busting\\Abstract_Busting' => $baseDir . '/inc/deprecated/classes/busting/class-abstract-busting.php',
    'WP_Rocket\\Busting\\Facebook_Pickles' => $baseDir . '/inc/deprecated/classes/busting/class-facebook-pickles.php',
    'WP_Rocket\\Busting\\Facebook_SDK' => $baseDir . '/inc/deprecated/classes/busting/class-facebook-sdk.php',
    'WP_Rocket\\Dependencies\\Database\\Base' => $baseDir . '/inc/Dependencies/Database/Base.php',
    'WP_Rocket\\Dependencies\\Database\\Column' => $baseDir . '/inc/Dependencies/Database/Column.php',
    'WP_Rocket\\Dependencies\\Database\\Queries\\Compare' => $baseDir . '/inc/Dependencies/Database/Queries/Compare.php',
    'WP_Rocket\\Dependencies\\Database\\Queries\\Date' => $baseDir . '/inc/Dependencies/Database/Queries/Date.php',
    'WP_Rocket\\Dependencies\\Database\\Queries\\Meta' => $baseDir . '/inc/Dependencies/Database/Queries/Meta.php',
    'WP_Rocket\\Dependencies\\Database\\Query' => $baseDir . '/inc/Dependencies/Database/Query.php',
    'WP_Rocket\\Dependencies\\Database\\Row' => $baseDir . '/inc/Dependencies/Database/Row.php',
    'WP_Rocket\\Dependencies\\Database\\Schema' => $baseDir . '/inc/Dependencies/Database/Schema.php',
    'WP_Rocket\\Dependencies\\Database\\Table' => $baseDir . '/inc/Dependencies/Database/Table.php',
    'WP_Rocket\\Dependencies\\League\\Container\\Argument\\ArgumentInterface' => $baseDir . '/inc/Dependencies/League/Container/Argument/ArgumentInterface.php',
    'WP_Rocket\\Dependencies\\League\\Container\\Argument\\ArgumentResolverInterface' => $baseDir . '/inc/Dependencies/League/Container/Argument/ArgumentResolverInterface.php',
    'WP_Rocket\\Dependencies\\League\\Container\\Argument\\ArgumentResolverTrait' => $baseDir . '/inc/Dependencies/League/Container/Argument/ArgumentResolverTrait.php',
    'WP_Rocket\\Dependencies\\League\\Container\\Argument\\DefaultValueArgument' => $baseDir . '/inc/Dependencies/League/Container/Argument/DefaultValueArgument.php',
    'WP_Rocket\\Dependencies\\League\\Container\\Argument\\DefaultValueInterface' => $baseDir . '/inc/Dependencies/League/Container/Argument/DefaultValueInterface.php',
    'WP_Rocket\\Dependencies\\League\\Container\\Argument\\LiteralArgument' => $baseDir . '/inc/Dependencies/League/Container/Argument/LiteralArgument.php',
    'WP_Rocket\\Dependencies\\League\\Container\\Argument\\LiteralArgumentInterface' => $baseDir . '/inc/Dependencies/League/Container/Argument/LiteralArgumentInterface.php',
    'WP_Rocket\\Dependencies\\League\\Container\\Argument\\Literal\\ArrayArgument' => $baseDir . '/inc/Dependencies/League/Container/Argument/Literal/ArrayArgument.php',
    'WP_Rocket\\Dependencies\\League\\Container\\Argument\\Literal\\BooleanArgument' => $baseDir . '/inc/Dependencies/League/Container/Argument/Literal/BooleanArgument.php',
    'WP_Rocket\\Dependencies\\League\\Container\\Argument\\Literal\\CallableArgument' => $baseDir . '/inc/Dependencies/League/Container/Argument/Literal/CallableArgument.php',
    'WP_Rocket\\Dependencies\\League\\Container\\Argument\\Literal\\FloatArgument' => $baseDir . '/inc/Dependencies/League/Container/Argument/Literal/FloatArgument.php',
    'WP_Rocket\\Dependencies\\League\\Container\\Argument\\Literal\\IntegerArgument' => $baseDir . '/inc/Dependencies/League/Container/Argument/Literal/IntegerArgument.php',
    'WP_Rocket\\Dependencies\\League\\Container\\Argument\\Literal\\ObjectArgument' => $baseDir . '/inc/Dependencies/League/Container/Argument/Literal/ObjectArgument.php',
    'WP_Rocket\\Dependencies\\League\\Container\\Argument\\Literal\\StringArgument' => $baseDir . '/inc/Dependencies/League/Container/Argument/Literal/StringArgument.php',
    'WP_Rocket\\Dependencies\\League\\Container\\Argument\\ResolvableArgument' => $baseDir . '/inc/Dependencies/League/Container/Argument/ResolvableArgument.php',
    'WP_Rocket\\Dependencies\\League\\Container\\Argument\\ResolvableArgumentInterface' => $baseDir . '/inc/Dependencies/League/Container/Argument/ResolvableArgumentInterface.php',
    'WP_Rocket\\Dependencies\\League\\Container\\Container' => $baseDir . '/inc/Dependencies/League/Container/Container.php',
    'WP_Rocket\\Dependencies\\League\\Container\\ContainerAwareInterface' => $baseDir . '/inc/Dependencies/League/Container/ContainerAwareInterface.php',
    'WP_Rocket\\Dependencies\\League\\Container\\ContainerAwareTrait' => $baseDir . '/inc/Dependencies/League/Container/ContainerAwareTrait.php',
    'WP_Rocket\\Dependencies\\League\\Container\\DefinitionContainerInterface' => $baseDir . '/inc/Dependencies/League/Container/DefinitionContainerInterface.php',
    'WP_Rocket\\Dependencies\\League\\Container\\Definition\\Definition' => $baseDir . '/inc/Dependencies/League/Container/Definition/Definition.php',
    'WP_Rocket\\Dependencies\\League\\Container\\Definition\\DefinitionAggregate' => $baseDir . '/inc/Dependencies/League/Container/Definition/DefinitionAggregate.php',
    'WP_Rocket\\Dependencies\\League\\Container\\Definition\\DefinitionAggregateInterface' => $baseDir . '/inc/Dependencies/League/Container/Definition/DefinitionAggregateInterface.php',
    'WP_Rocket\\Dependencies\\League\\Container\\Definition\\DefinitionInterface' => $baseDir . '/inc/Dependencies/League/Container/Definition/DefinitionInterface.php',
    'WP_Rocket\\Dependencies\\League\\Container\\Exception\\ContainerException' => $baseDir . '/inc/Dependencies/League/Container/Exception/ContainerException.php',
    'WP_Rocket\\Dependencies\\League\\Container\\Exception\\NotFoundException' => $baseDir . '/inc/Dependencies/League/Container/Exception/NotFoundException.php',
    'WP_Rocket\\Dependencies\\League\\Container\\Inflector\\Inflector' => $baseDir . '/inc/Dependencies/League/Container/Inflector/Inflector.php',
    'WP_Rocket\\Dependencies\\League\\Container\\Inflector\\InflectorAggregate' => $baseDir . '/inc/Dependencies/League/Container/Inflector/InflectorAggregate.php',
    'WP_Rocket\\Dependencies\\League\\Container\\Inflector\\InflectorAggregateInterface' => $baseDir . '/inc/Dependencies/League/Container/Inflector/InflectorAggregateInterface.php',
    'WP_Rocket\\Dependencies\\League\\Container\\Inflector\\InflectorInterface' => $baseDir . '/inc/Dependencies/League/Container/Inflector/InflectorInterface.php',
    'WP_Rocket\\Dependencies\\League\\Container\\ReflectionContainer' => $baseDir . '/inc/Dependencies/League/Container/ReflectionContainer.php',
    'WP_Rocket\\Dependencies\\League\\Container\\ServiceProvider\\AbstractServiceProvider' => $baseDir . '/inc/Dependencies/League/Container/ServiceProvider/AbstractServiceProvider.php',
    'WP_Rocket\\Dependencies\\League\\Container\\ServiceProvider\\BootableServiceProviderInterface' => $baseDir . '/inc/Dependencies/League/Container/ServiceProvider/BootableServiceProviderInterface.php',
    'WP_Rocket\\Dependencies\\League\\Container\\ServiceProvider\\ServiceProviderAggregate' => $baseDir . '/inc/Dependencies/League/Container/ServiceProvider/ServiceProviderAggregate.php',
    'WP_Rocket\\Dependencies\\League\\Container\\ServiceProvider\\ServiceProviderAggregateInterface' => $baseDir . '/inc/Dependencies/League/Container/ServiceProvider/ServiceProviderAggregateInterface.php',
    'WP_Rocket\\Dependencies\\League\\Container\\ServiceProvider\\ServiceProviderInterface' => $baseDir . '/inc/Dependencies/League/Container/ServiceProvider/ServiceProviderInterface.php',
    'WP_Rocket\\Dependencies\\Minify\\CSS' => $baseDir . '/inc/Dependencies/Minify/CSS.php',
    'WP_Rocket\\Dependencies\\Minify\\Exception' => $baseDir . '/inc/Dependencies/Minify/Exception.php',
    'WP_Rocket\\Dependencies\\Minify\\Exceptions\\BasicException' => $baseDir . '/inc/Dependencies/Minify/Exceptions/BasicException.php',
    'WP_Rocket\\Dependencies\\Minify\\Exceptions\\FileImportException' => $baseDir . '/inc/Dependencies/Minify/Exceptions/FileImportException.php',
    'WP_Rocket\\Dependencies\\Minify\\Exceptions\\IOException' => $baseDir . '/inc/Dependencies/Minify/Exceptions/IOException.php',
    'WP_Rocket\\Dependencies\\Minify\\JS' => $baseDir . '/inc/Dependencies/Minify/JS.php',
    'WP_Rocket\\Dependencies\\Minify\\Minify' => $baseDir . '/inc/Dependencies/Minify/Minify.php',
    'WP_Rocket\\Dependencies\\Monolog\\ErrorHandler' => $baseDir . '/inc/Dependencies/Monolog/ErrorHandler.php',
    'WP_Rocket\\Dependencies\\Monolog\\Formatter\\FormatterInterface' => $baseDir . '/inc/Dependencies/Monolog/Formatter/FormatterInterface.php',
    'WP_Rocket\\Dependencies\\Monolog\\Formatter\\HtmlFormatter' => $baseDir . '/inc/Dependencies/Monolog/Formatter/HtmlFormatter.php',
    'WP_Rocket\\Dependencies\\Monolog\\Formatter\\LineFormatter' => $baseDir . '/inc/Dependencies/Monolog/Formatter/LineFormatter.php',
    'WP_Rocket\\Dependencies\\Monolog\\Formatter\\NormalizerFormatter' => $baseDir . '/inc/Dependencies/Monolog/Formatter/NormalizerFormatter.php',
    'WP_Rocket\\Dependencies\\Monolog\\Handler\\AbstractHandler' => $baseDir . '/inc/Dependencies/Monolog/Handler/AbstractHandler.php',
    'WP_Rocket\\Dependencies\\Monolog\\Handler\\AbstractProcessingHandler' => $baseDir . '/inc/Dependencies/Monolog/Handler/AbstractProcessingHandler.php',
    'WP_Rocket\\Dependencies\\Monolog\\Handler\\FormattableHandlerInterface' => $baseDir . '/inc/Dependencies/Monolog/Handler/FormattableHandlerInterface.php',
    'WP_Rocket\\Dependencies\\Monolog\\Handler\\FormattableHandlerTrait' => $baseDir . '/inc/Dependencies/Monolog/Handler/FormattableHandlerTrait.php',
    'WP_Rocket\\Dependencies\\Monolog\\Handler\\HandlerInterface' => $baseDir . '/inc/Dependencies/Monolog/Handler/HandlerInterface.php',
    'WP_Rocket\\Dependencies\\Monolog\\Handler\\ProcessableHandlerInterface' => $baseDir . '/inc/Dependencies/Monolog/Handler/ProcessableHandlerInterface.php',
    'WP_Rocket\\Dependencies\\Monolog\\Handler\\ProcessableHandlerTrait' => $baseDir . '/inc/Dependencies/Monolog/Handler/ProcessableHandlerTrait.php',
    'WP_Rocket\\Dependencies\\Monolog\\Handler\\StreamHandler' => $baseDir . '/inc/Dependencies/Monolog/Handler/StreamHandler.php',
    'WP_Rocket\\Dependencies\\Monolog\\Logger' => $baseDir . '/inc/Dependencies/Monolog/Logger.php',
    'WP_Rocket\\Dependencies\\Monolog\\Processor\\IntrospectionProcessor' => $baseDir . '/inc/Dependencies/Monolog/Processor/IntrospectionProcessor.php',
    'WP_Rocket\\Dependencies\\Monolog\\Processor\\ProcessorInterface' => $baseDir . '/inc/Dependencies/Monolog/Processor/ProcessorInterface.php',
    'WP_Rocket\\Dependencies\\Monolog\\Registry' => $baseDir . '/inc/Dependencies/Monolog/Registry.php',
    'WP_Rocket\\Dependencies\\Monolog\\ResettableInterface' => $baseDir . '/inc/Dependencies/Monolog/ResettableInterface.php',
    'WP_Rocket\\Dependencies\\Monolog\\SignalHandler' => $baseDir . '/inc/Dependencies/Monolog/SignalHandler.php',
    'WP_Rocket\\Dependencies\\Monolog\\Utils' => $baseDir . '/inc/Dependencies/Monolog/Utils.php',
    'WP_Rocket\\Dependencies\\PathConverter\\Converter' => $baseDir . '/inc/Dependencies/PathConverter/Converter.php',
    'WP_Rocket\\Dependencies\\PathConverter\\ConverterInterface' => $baseDir . '/inc/Dependencies/PathConverter/ConverterInterface.php',
    'WP_Rocket\\Dependencies\\PathConverter\\NoConverter' => $baseDir . '/inc/Dependencies/PathConverter/NoConverter.php',
    'WP_Rocket\\Dependencies\\Psr\\Container\\ContainerExceptionInterface' => $baseDir . '/inc/Dependencies/Psr/Container/ContainerExceptionInterface.php',
    'WP_Rocket\\Dependencies\\Psr\\Container\\ContainerInterface' => $baseDir . '/inc/Dependencies/Psr/Container/ContainerInterface.php',
    'WP_Rocket\\Dependencies\\Psr\\Container\\NotFoundExceptionInterface' => $baseDir . '/inc/Dependencies/Psr/Container/NotFoundExceptionInterface.php',
    'WP_Rocket\\Dependencies\\Psr\\Log\\AbstractLogger' => $baseDir . '/inc/Dependencies/Psr/Log/AbstractLogger.php',
    'WP_Rocket\\Dependencies\\Psr\\Log\\InvalidArgumentException' => $baseDir . '/inc/Dependencies/Psr/Log/InvalidArgumentException.php',
    'WP_Rocket\\Dependencies\\Psr\\Log\\LogLevel' => $baseDir . '/inc/Dependencies/Psr/Log/LogLevel.php',
    'WP_Rocket\\Dependencies\\Psr\\Log\\LoggerAwareInterface' => $baseDir . '/inc/Dependencies/Psr/Log/LoggerAwareInterface.php',
    'WP_Rocket\\Dependencies\\Psr\\Log\\LoggerAwareTrait' => $baseDir . '/inc/Dependencies/Psr/Log/LoggerAwareTrait.php',
    'WP_Rocket\\Dependencies\\Psr\\Log\\LoggerInterface' => $baseDir . '/inc/Dependencies/Psr/Log/LoggerInterface.php',
    'WP_Rocket\\Dependencies\\Psr\\Log\\LoggerTrait' => $baseDir . '/inc/Dependencies/Psr/Log/LoggerTrait.php',
    'WP_Rocket\\Dependencies\\Psr\\Log\\NullLogger' => $baseDir . '/inc/Dependencies/Psr/Log/NullLogger.php',
    'WP_Rocket\\Dependencies\\Psr\\SimpleCache\\CacheException' => $baseDir . '/inc/Dependencies/Psr/SimpleCache/CacheException.php',
    'WP_Rocket\\Dependencies\\Psr\\SimpleCache\\CacheInterface' => $baseDir . '/inc/Dependencies/Psr/SimpleCache/CacheInterface.php',
    'WP_Rocket\\Dependencies\\Psr\\SimpleCache\\InvalidArgumentException' => $baseDir . '/inc/Dependencies/Psr/SimpleCache/InvalidArgumentException.php',
    'WP_Rocket\\Dependencies\\RocketLazyload\\Assets' => $baseDir . '/inc/Dependencies/RocketLazyload/Assets.php',
    'WP_Rocket\\Dependencies\\RocketLazyload\\Iframe' => $baseDir . '/inc/Dependencies/RocketLazyload/Iframe.php',
    'WP_Rocket\\Dependencies\\RocketLazyload\\Image' => $baseDir . '/inc/Dependencies/RocketLazyload/Image.php',
    'WP_Rocket\\Engine\\Activation\\Activation' => $baseDir . '/inc/Engine/Activation/Activation.php',
    'WP_Rocket\\Engine\\Activation\\ActivationInterface' => $baseDir . '/inc/Engine/Activation/ActivationInterface.php',
    'WP_Rocket\\Engine\\Activation\\ServiceProvider' => $baseDir . '/inc/Engine/Activation/ServiceProvider.php',
    'WP_Rocket\\Engine\\Admin\\API\\ServiceProvider' => $baseDir . '/inc/Engine/Admin/API/ServiceProvider.php',
    'WP_Rocket\\Engine\\Admin\\API\\Subscriber' => $baseDir . '/inc/Engine/Admin/API/Subscriber.php',
    'WP_Rocket\\Engine\\Admin\\ActionSchedulerSubscriber' => $baseDir . '/inc/Engine/Admin/ActionSchedulerSubscriber.php',
    'WP_Rocket\\Engine\\Admin\\Beacon\\Beacon' => $baseDir . '/inc/Engine/Admin/Beacon/Beacon.php',
    'WP_Rocket\\Engine\\Admin\\Beacon\\ServiceProvider' => $baseDir . '/inc/Engine/Admin/Beacon/ServiceProvider.php',
    'WP_Rocket\\Engine\\Admin\\Database\\Optimization' => $baseDir . '/inc/Engine/Admin/Database/Optimization.php',
    'WP_Rocket\\Engine\\Admin\\Database\\OptimizationProcess' => $baseDir . '/inc/Engine/Admin/Database/OptimizationProcess.php',
    'WP_Rocket\\Engine\\Admin\\Database\\ServiceProvider' => $baseDir . '/inc/Engine/Admin/Database/ServiceProvider.php',
    'WP_Rocket\\Engine\\Admin\\Database\\Subscriber' => $baseDir . '/inc/Engine/Admin/Database/Subscriber.php',
    'WP_Rocket\\Engine\\Admin\\Deactivation\\DeactivationIntent' => $baseDir . '/inc/Engine/Admin/Deactivation/DeactivationIntent.php',
    'WP_Rocket\\Engine\\Admin\\Deactivation\\Subscriber' => $baseDir . '/inc/Engine/Admin/Deactivation/Subscriber.php',
    'WP_Rocket\\Engine\\Admin\\DomainChange\\ServiceProvider' => $baseDir . '/inc/Engine/Admin/DomainChange/ServiceProvider.php',
    'WP_Rocket\\Engine\\Admin\\DomainChange\\Subscriber' => $baseDir . '/inc/Engine/Admin/DomainChange/Subscriber.php',
    'WP_Rocket\\Engine\\Admin\\Metaboxes\\PostEditOptionsSubscriber' => $baseDir . '/inc/Engine/Admin/Metaboxes/PostEditOptionsSubscriber.php',
    'WP_Rocket\\Engine\\Admin\\ServiceProvider' => $baseDir . '/inc/Engine/Admin/ServiceProvider.php',
    'WP_Rocket\\Engine\\Admin\\Settings\\Page' => $baseDir . '/inc/Engine/Admin/Settings/Page.php',
    'WP_Rocket\\Engine\\Admin\\Settings\\Render' => $baseDir . '/inc/Engine/Admin/Settings/Render.php',
    'WP_Rocket\\Engine\\Admin\\Settings\\ServiceProvider' => $baseDir . '/inc/Engine/Admin/Settings/ServiceProvider.php',
    'WP_Rocket\\Engine\\Admin\\Settings\\Settings' => $baseDir . '/inc/Engine/Admin/Settings/Settings.php',
    'WP_Rocket\\Engine\\Admin\\Settings\\Subscriber' => $baseDir . '/inc/Engine/Admin/Settings/Subscriber.php',
    'WP_Rocket\\Engine\\CDN\\Admin\\Subscriber' => $baseDir . '/inc/Engine/CDN/Admin/Subscriber.php',
    'WP_Rocket\\Engine\\CDN\\CDN' => $baseDir . '/inc/Engine/CDN/CDN.php',
    'WP_Rocket\\Engine\\CDN\\RocketCDN\\APIClient' => $baseDir . '/inc/Engine/CDN/RocketCDN/APIClient.php',
    'WP_Rocket\\Engine\\CDN\\RocketCDN\\AdminPageSubscriber' => $baseDir . '/inc/Engine/CDN/RocketCDN/AdminPageSubscriber.php',
    'WP_Rocket\\Engine\\CDN\\RocketCDN\\CDNOptionsManager' => $baseDir . '/inc/Engine/CDN/RocketCDN/CDNOptionsManager.php',
    'WP_Rocket\\Engine\\CDN\\RocketCDN\\DataManagerSubscriber' => $baseDir . '/inc/Engine/CDN/RocketCDN/DataManagerSubscriber.php',
    'WP_Rocket\\Engine\\CDN\\RocketCDN\\NoticesSubscriber' => $baseDir . '/inc/Engine/CDN/RocketCDN/NoticesSubscriber.php',
    'WP_Rocket\\Engine\\CDN\\RocketCDN\\RESTSubscriber' => $baseDir . '/inc/Engine/CDN/RocketCDN/RESTSubscriber.php',
    'WP_Rocket\\Engine\\CDN\\RocketCDN\\ServiceProvider' => $baseDir . '/inc/Engine/CDN/RocketCDN/ServiceProvider.php',
    'WP_Rocket\\Engine\\CDN\\ServiceProvider' => $baseDir . '/inc/Engine/CDN/ServiceProvider.php',
    'WP_Rocket\\Engine\\CDN\\Subscriber' => $baseDir . '/inc/Engine/CDN/Subscriber.php',
    'WP_Rocket\\Engine\\Cache\\AdminSubscriber' => $baseDir . '/inc/Engine/Cache/AdminSubscriber.php',
    'WP_Rocket\\Engine\\Cache\\AdvancedCache' => $baseDir . '/inc/Engine/Cache/AdvancedCache.php',
    'WP_Rocket\\Engine\\Cache\\Config\\ConfigSubscriber' => $baseDir . '/inc/Engine/Cache/Config/ConfigSubscriber.php',
    'WP_Rocket\\Engine\\Cache\\Config\\Subscriber' => $baseDir . '/inc/Engine/Cache/Config/Subscriber.php',
    'WP_Rocket\\Engine\\Cache\\Purge' => $baseDir . '/inc/Engine/Cache/Purge.php',
    'WP_Rocket\\Engine\\Cache\\PurgeActionsSubscriber' => $baseDir . '/inc/Engine/Cache/PurgeActionsSubscriber.php',
    'WP_Rocket\\Engine\\Cache\\PurgeExpired\\PurgeExpiredCache' => $baseDir . '/inc/Engine/Cache/PurgeExpired/PurgeExpiredCache.php',
    'WP_Rocket\\Engine\\Cache\\PurgeExpired\\Subscriber' => $baseDir . '/inc/Engine/Cache/PurgeExpired/Subscriber.php',
    'WP_Rocket\\Engine\\Cache\\ServiceProvider' => $baseDir . '/inc/Engine/Cache/ServiceProvider.php',
    'WP_Rocket\\Engine\\Cache\\WPCache' => $baseDir . '/inc/Engine/Cache/WPCache.php',
    'WP_Rocket\\Engine\\Capabilities\\Manager' => $baseDir . '/inc/Engine/Capabilities/Manager.php',
    'WP_Rocket\\Engine\\Capabilities\\ServiceProvider' => $baseDir . '/inc/Engine/Capabilities/ServiceProvider.php',
    'WP_Rocket\\Engine\\Capabilities\\Subscriber' => $baseDir . '/inc/Engine/Capabilities/Subscriber.php',
    'WP_Rocket\\Engine\\Common\\Ajax\\AjaxHandler' => $baseDir . '/inc/Engine/Common/Ajax/AjaxHandler.php',
    'WP_Rocket\\Engine\\Common\\Cache\\CacheInterface' => $baseDir . '/inc/Engine/Common/Cache/CacheInterface.php',
    'WP_Rocket\\Engine\\Common\\Cache\\FilesystemCache' => $baseDir . '/inc/Engine/Common/Cache/FilesystemCache.php',
    'WP_Rocket\\Engine\\Common\\Clock\\ClockInterface' => $baseDir . '/inc/Engine/Common/Clock/ClockInterface.php',
    'WP_Rocket\\Engine\\Common\\Clock\\WPRClock' => $baseDir . '/inc/Engine/Common/Clock/WPRClock.php',
    'WP_Rocket\\Engine\\Common\\Context\\AbstractContext' => $baseDir . '/inc/Engine/Common/Context/AbstractContext.php',
    'WP_Rocket\\Engine\\Common\\Context\\ContextInterface' => $baseDir . '/inc/Engine/Common/Context/ContextInterface.php',
    'WP_Rocket\\Engine\\Common\\Database\\Queries\\AbstractQuery' => $baseDir . '/inc/Engine/Common/Database/Queries/AbstractQuery.php',
    'WP_Rocket\\Engine\\Common\\Database\\TableInterface' => $baseDir . '/inc/Engine/Common/Database/TableInterface.php',
    'WP_Rocket\\Engine\\Common\\Database\\Tables\\AbstractTable' => $baseDir . '/inc/Engine/Common/Database/Tables/AbstractTable.php',
    'WP_Rocket\\Engine\\Common\\ExtractCSS\\ServiceProvider' => $baseDir . '/inc/Engine/Common/ExtractCSS/ServiceProvider.php',
    'WP_Rocket\\Engine\\Common\\ExtractCSS\\Subscriber' => $baseDir . '/inc/Engine/Common/ExtractCSS/Subscriber.php',
    'WP_Rocket\\Engine\\Common\\JobManager\\APIHandler\\APIClient' => $baseDir . '/inc/Engine/Common/JobManager/APIHandler/APIClient.php',
    'WP_Rocket\\Engine\\Common\\JobManager\\APIHandler\\AbstractAPIClient' => $baseDir . '/inc/Engine/Common/JobManager/APIHandler/AbstractAPIClient.php',
    'WP_Rocket\\Engine\\Common\\JobManager\\APIHandler\\AbstractSafeAPIClient' => $baseDir . '/inc/Engine/Common/JobManager/APIHandler/AbstractSafeAPIClient.php',
    'WP_Rocket\\Engine\\Common\\JobManager\\AbstractFactory\\SaasFactory' => $baseDir . '/inc/Engine/Common/JobManager/AbstractFactory/SaasFactory.php',
    'WP_Rocket\\Engine\\Common\\JobManager\\Cron\\Subscriber' => $baseDir . '/inc/Engine/Common/JobManager/Cron/Subscriber.php',
    'WP_Rocket\\Engine\\Common\\JobManager\\JobProcessor' => $baseDir . '/inc/Engine/Common/JobManager/JobProcessor.php',
    'WP_Rocket\\Engine\\Common\\JobManager\\Managers\\AbstractManager' => $baseDir . '/inc/Engine/Common/JobManager/Managers/AbstractManager.php',
    'WP_Rocket\\Engine\\Common\\JobManager\\Managers\\ManagerInterface' => $baseDir . '/inc/Engine/Common/JobManager/Managers/ManagerInterface.php',
    'WP_Rocket\\Engine\\Common\\JobManager\\Queue\\Queue' => $baseDir . '/inc/Engine/Common/JobManager/Queue/Queue.php',
    'WP_Rocket\\Engine\\Common\\JobManager\\ServiceProvider' => $baseDir . '/inc/Engine/Common/JobManager/ServiceProvider.php',
    'WP_Rocket\\Engine\\Common\\JobManager\\Strategy\\Context\\RetryContext' => $baseDir . '/inc/Engine/Common/JobManager/Strategy/Context/RetryContext.php',
    'WP_Rocket\\Engine\\Common\\JobManager\\Strategy\\Factory\\StrategyFactory' => $baseDir . '/inc/Engine/Common/JobManager/Strategy/Factory/StrategyFactory.php',
    'WP_Rocket\\Engine\\Common\\JobManager\\Strategy\\Strategies\\DefaultProcess' => $baseDir . '/inc/Engine/Common/JobManager/Strategy/Strategies/DefaultProcess.php',
    'WP_Rocket\\Engine\\Common\\JobManager\\Strategy\\Strategies\\JobSetFail' => $baseDir . '/inc/Engine/Common/JobManager/Strategy/Strategies/JobSetFail.php',
    'WP_Rocket\\Engine\\Common\\JobManager\\Strategy\\Strategies\\ResetRetryProcess' => $baseDir . '/inc/Engine/Common/JobManager/Strategy/Strategies/ResetRetryProcess.php',
    'WP_Rocket\\Engine\\Common\\JobManager\\Strategy\\Strategies\\StrategyInterface' => $baseDir . '/inc/Engine/Common/JobManager/Strategy/Strategies/StrategyInterface.php',
    'WP_Rocket\\Engine\\Common\\Queue\\AbstractASQueue' => $baseDir . '/inc/Engine/Common/Queue/AbstractASQueue.php',
    'WP_Rocket\\Engine\\Common\\Queue\\Cleaner' => $baseDir . '/inc/Engine/Common/Queue/Cleaner.php',
    'WP_Rocket\\Engine\\Common\\Queue\\QueueInterface' => $baseDir . '/inc/Engine/Common/Queue/QueueInterface.php',
    'WP_Rocket\\Engine\\Common\\Queue\\RUCSSQueueRunner' => $baseDir . '/inc/Engine/Common/Queue/RUCSSQueueRunner.php',
    'WP_Rocket\\Engine\\Common\\Utils' => $baseDir . '/inc/Engine/Common/Utils.php',
    'WP_Rocket\\Engine\\CriticalPath\\APIClient' => $baseDir . '/inc/Engine/CriticalPath/APIClient.php',
    'WP_Rocket\\Engine\\CriticalPath\\Admin\\Admin' => $baseDir . '/inc/Engine/CriticalPath/Admin/Admin.php',
    'WP_Rocket\\Engine\\CriticalPath\\Admin\\Post' => $baseDir . '/inc/Engine/CriticalPath/Admin/Post.php',
    'WP_Rocket\\Engine\\CriticalPath\\Admin\\Settings' => $baseDir . '/inc/Engine/CriticalPath/Admin/Settings.php',
    'WP_Rocket\\Engine\\CriticalPath\\Admin\\Subscriber' => $baseDir . '/inc/Engine/CriticalPath/Admin/Subscriber.php',
    'WP_Rocket\\Engine\\CriticalPath\\CriticalCSS' => $baseDir . '/inc/Engine/CriticalPath/CriticalCSS.php',
    'WP_Rocket\\Engine\\CriticalPath\\CriticalCSSGeneration' => $baseDir . '/inc/Engine/CriticalPath/CriticalCSSGeneration.php',
    'WP_Rocket\\Engine\\CriticalPath\\CriticalCSSSubscriber' => $baseDir . '/inc/Engine/CriticalPath/CriticalCSSSubscriber.php',
    'WP_Rocket\\Engine\\CriticalPath\\DataManager' => $baseDir . '/inc/Engine/CriticalPath/DataManager.php',
    'WP_Rocket\\Engine\\CriticalPath\\ProcessorService' => $baseDir . '/inc/Engine/CriticalPath/ProcessorService.php',
    'WP_Rocket\\Engine\\CriticalPath\\RESTCSSSubscriber' => $baseDir . '/inc/Engine/CriticalPath/RESTCSSSubscriber.php',
    'WP_Rocket\\Engine\\CriticalPath\\RESTWP' => $baseDir . '/inc/Engine/CriticalPath/RESTWP.php',
    'WP_Rocket\\Engine\\CriticalPath\\RESTWPInterface' => $baseDir . '/inc/Engine/CriticalPath/RESTWPInterface.php',
    'WP_Rocket\\Engine\\CriticalPath\\RESTWPPost' => $baseDir . '/inc/Engine/CriticalPath/RESTWPPost.php',
    'WP_Rocket\\Engine\\CriticalPath\\ServiceProvider' => $baseDir . '/inc/Engine/CriticalPath/ServiceProvider.php',
    'WP_Rocket\\Engine\\CriticalPath\\TransientTrait' => $baseDir . '/inc/Engine/CriticalPath/TransientTrait.php',
    'WP_Rocket\\Engine\\Deactivation\\Deactivation' => $baseDir . '/inc/Engine/Deactivation/Deactivation.php',
    'WP_Rocket\\Engine\\Deactivation\\DeactivationInterface' => $baseDir . '/inc/Engine/Deactivation/DeactivationInterface.php',
    'WP_Rocket\\Engine\\Deactivation\\ServiceProvider' => $baseDir . '/inc/Engine/Deactivation/ServiceProvider.php',
    'WP_Rocket\\Engine\\Debug\\DebugSubscriber' => $baseDir . '/inc/Engine/Debug/DebugSubscriber.php',
    'WP_Rocket\\Engine\\Debug\\RUCSS\\Subscriber' => $baseDir . '/inc/Engine/Debug/RUCSS/Subscriber.php',
    'WP_Rocket\\Engine\\Debug\\Resolver' => $baseDir . '/inc/Engine/Debug/Resolver.php',
    'WP_Rocket\\Engine\\Debug\\ServiceProvider' => $baseDir . '/inc/Engine/Debug/ServiceProvider.php',
    'WP_Rocket\\Engine\\HealthCheck\\ActionSchedulerCheck' => $baseDir . '/inc/Engine/HealthCheck/ActionSchedulerCheck.php',
    'WP_Rocket\\Engine\\HealthCheck\\HealthCheck' => $baseDir . '/inc/Engine/HealthCheck/HealthCheck.php',
    'WP_Rocket\\Engine\\HealthCheck\\ServiceProvider' => $baseDir . '/inc/Engine/HealthCheck/ServiceProvider.php',
    'WP_Rocket\\Engine\\Heartbeat\\HeartbeatSubscriber' => $baseDir . '/inc/Engine/Heartbeat/HeartbeatSubscriber.php',
    'WP_Rocket\\Engine\\Heartbeat\\ServiceProvider' => $baseDir . '/inc/Engine/Heartbeat/ServiceProvider.php',
    'WP_Rocket\\Engine\\License\\API\\Pricing' => $baseDir . '/inc/Engine/License/API/Pricing.php',
    'WP_Rocket\\Engine\\License\\API\\PricingClient' => $baseDir . '/inc/Engine/License/API/PricingClient.php',
    'WP_Rocket\\Engine\\License\\API\\User' => $baseDir . '/inc/Engine/License/API/User.php',
    'WP_Rocket\\Engine\\License\\API\\UserClient' => $baseDir . '/inc/Engine/License/API/UserClient.php',
    'WP_Rocket\\Engine\\License\\Renewal' => $baseDir . '/inc/Engine/License/Renewal.php',
    'WP_Rocket\\Engine\\License\\ServiceProvider' => $baseDir . '/inc/Engine/License/ServiceProvider.php',
    'WP_Rocket\\Engine\\License\\Subscriber' => $baseDir . '/inc/Engine/License/Subscriber.php',
    'WP_Rocket\\Engine\\License\\Upgrade' => $baseDir . '/inc/Engine/License/Upgrade.php',
    'WP_Rocket\\Engine\\Media\\AboveTheFold\\AJAX\\Controller' => $baseDir . '/inc/Engine/Media/AboveTheFold/AJAX/Controller.php',
    'WP_Rocket\\Engine\\Media\\AboveTheFold\\AJAX\\Subscriber' => $baseDir . '/inc/Engine/Media/AboveTheFold/AJAX/Subscriber.php',
    'WP_Rocket\\Engine\\Media\\AboveTheFold\\Activation\\Activation' => $baseDir . '/inc/Engine/Media/AboveTheFold/Activation/Activation.php',
    'WP_Rocket\\Engine\\Media\\AboveTheFold\\Activation\\ServiceProvider' => $baseDir . '/inc/Engine/Media/AboveTheFold/Activation/ServiceProvider.php',
    'WP_Rocket\\Engine\\Media\\AboveTheFold\\Admin\\Controller' => $baseDir . '/inc/Engine/Media/AboveTheFold/Admin/Controller.php',
    'WP_Rocket\\Engine\\Media\\AboveTheFold\\Admin\\Subscriber' => $baseDir . '/inc/Engine/Media/AboveTheFold/Admin/Subscriber.php',
    'WP_Rocket\\Engine\\Media\\AboveTheFold\\Context\\Context' => $baseDir . '/inc/Engine/Media/AboveTheFold/Context/Context.php',
    'WP_Rocket\\Engine\\Media\\AboveTheFold\\Cron\\Controller' => $baseDir . '/inc/Engine/Media/AboveTheFold/Cron/Controller.php',
    'WP_Rocket\\Engine\\Media\\AboveTheFold\\Cron\\Subscriber' => $baseDir . '/inc/Engine/Media/AboveTheFold/Cron/Subscriber.php',
    'WP_Rocket\\Engine\\Media\\AboveTheFold\\Database\\Queries\\AboveTheFold' => $baseDir . '/inc/Engine/Media/AboveTheFold/Database/Queries/AboveTheFold.php',
    'WP_Rocket\\Engine\\Media\\AboveTheFold\\Database\\Rows\\AboveTheFold' => $baseDir . '/inc/Engine/Media/AboveTheFold/Database/Rows/AboveTheFold.php',
    'WP_Rocket\\Engine\\Media\\AboveTheFold\\Database\\Schemas\\AboveTheFold' => $baseDir . '/inc/Engine/Media/AboveTheFold/Database/Schemas/AboveTheFold.php',
    'WP_Rocket\\Engine\\Media\\AboveTheFold\\Database\\Tables\\AboveTheFold' => $baseDir . '/inc/Engine/Media/AboveTheFold/Database/Tables/AboveTheFold.php',
    'WP_Rocket\\Engine\\Media\\AboveTheFold\\Frontend\\Controller' => $baseDir . '/inc/Engine/Media/AboveTheFold/Frontend/Controller.php',
    'WP_Rocket\\Engine\\Media\\AboveTheFold\\Frontend\\Subscriber' => $baseDir . '/inc/Engine/Media/AboveTheFold/Frontend/Subscriber.php',
    'WP_Rocket\\Engine\\Media\\AboveTheFold\\ServiceProvider' => $baseDir . '/inc/Engine/Media/AboveTheFold/ServiceProvider.php',
    'WP_Rocket\\Engine\\Media\\AboveTheFold\\WarmUp\\APIClient' => $baseDir . '/inc/Engine/Media/AboveTheFold/WarmUp/APIClient.php',
    'WP_Rocket\\Engine\\Media\\AboveTheFold\\WarmUp\\Controller' => $baseDir . '/inc/Engine/Media/AboveTheFold/WarmUp/Controller.php',
    'WP_Rocket\\Engine\\Media\\AboveTheFold\\WarmUp\\Queue' => $baseDir . '/inc/Engine/Media/AboveTheFold/WarmUp/Queue.php',
    'WP_Rocket\\Engine\\Media\\AboveTheFold\\WarmUp\\Subscriber' => $baseDir . '/inc/Engine/Media/AboveTheFold/WarmUp/Subscriber.php',
    'WP_Rocket\\Engine\\Media\\Emojis\\EmojisSubscriber' => $baseDir . '/inc/Engine/Media/Emojis/EmojisSubscriber.php',
    'WP_Rocket\\Engine\\Media\\ImageDimensions\\AdminSubscriber' => $baseDir . '/inc/Engine/Media/ImageDimensions/AdminSubscriber.php',
    'WP_Rocket\\Engine\\Media\\ImageDimensions\\ImageDimensions' => $baseDir . '/inc/Engine/Media/ImageDimensions/ImageDimensions.php',
    'WP_Rocket\\Engine\\Media\\ImageDimensions\\Subscriber' => $baseDir . '/inc/Engine/Media/ImageDimensions/Subscriber.php',
    'WP_Rocket\\Engine\\Media\\Lazyload\\AdminSubscriber' => $baseDir . '/inc/Engine/Media/Lazyload/AdminSubscriber.php',
    'WP_Rocket\\Engine\\Media\\Lazyload\\CSS\\Admin\\ServiceProvider' => $baseDir . '/inc/Engine/Media/Lazyload/CSS/Admin/ServiceProvider.php',
    'WP_Rocket\\Engine\\Media\\Lazyload\\CSS\\Admin\\Subscriber' => $baseDir . '/inc/Engine/Media/Lazyload/CSS/Admin/Subscriber.php',
    'WP_Rocket\\Engine\\Media\\Lazyload\\CSS\\Context\\LazyloadCSSContext' => $baseDir . '/inc/Engine/Media/Lazyload/CSS/Context/LazyloadCSSContext.php',
    'WP_Rocket\\Engine\\Media\\Lazyload\\CSS\\Data\\LazyloadCSSContentFactory' => $baseDir . '/inc/Engine/Media/Lazyload/CSS/Data/LazyloadCSSContentFactory.php',
    'WP_Rocket\\Engine\\Media\\Lazyload\\CSS\\Data\\LazyloadedContent' => $baseDir . '/inc/Engine/Media/Lazyload/CSS/Data/LazyloadedContent.php',
    'WP_Rocket\\Engine\\Media\\Lazyload\\CSS\\Data\\ProtectedContent' => $baseDir . '/inc/Engine/Media/Lazyload/CSS/Data/ProtectedContent.php',
    'WP_Rocket\\Engine\\Media\\Lazyload\\CSS\\Front\\ContentFetcher' => $baseDir . '/inc/Engine/Media/Lazyload/CSS/Front/ContentFetcher.php',
    'WP_Rocket\\Engine\\Media\\Lazyload\\CSS\\Front\\Extractor' => $baseDir . '/inc/Engine/Media/Lazyload/CSS/Front/Extractor.php',
    'WP_Rocket\\Engine\\Media\\Lazyload\\CSS\\Front\\FileResolver' => $baseDir . '/inc/Engine/Media/Lazyload/CSS/Front/FileResolver.php',
    'WP_Rocket\\Engine\\Media\\Lazyload\\CSS\\Front\\MappingFormatter' => $baseDir . '/inc/Engine/Media/Lazyload/CSS/Front/MappingFormatter.php',
    'WP_Rocket\\Engine\\Media\\Lazyload\\CSS\\Front\\RuleFormatter' => $baseDir . '/inc/Engine/Media/Lazyload/CSS/Front/RuleFormatter.php',
    'WP_Rocket\\Engine\\Media\\Lazyload\\CSS\\Front\\TagGenerator' => $baseDir . '/inc/Engine/Media/Lazyload/CSS/Front/TagGenerator.php',
    'WP_Rocket\\Engine\\Media\\Lazyload\\CSS\\ServiceProvider' => $baseDir . '/inc/Engine/Media/Lazyload/CSS/ServiceProvider.php',
    'WP_Rocket\\Engine\\Media\\Lazyload\\CSS\\Subscriber' => $baseDir . '/inc/Engine/Media/Lazyload/CSS/Subscriber.php',
    'WP_Rocket\\Engine\\Media\\Lazyload\\CanLazyloadTrait' => $baseDir . '/inc/Engine/Media/Lazyload/CanLazyloadTrait.php',
    'WP_Rocket\\Engine\\Media\\Lazyload\\Subscriber' => $baseDir . '/inc/Engine/Media/Lazyload/Subscriber.php',
    'WP_Rocket\\Engine\\Media\\ServiceProvider' => $baseDir . '/inc/Engine/Media/ServiceProvider.php',
    'WP_Rocket\\Engine\\Optimization\\AbstractOptimization' => $baseDir . '/inc/Engine/Optimization/AbstractOptimization.php',
    'WP_Rocket\\Engine\\Optimization\\AdminServiceProvider' => $baseDir . '/inc/Engine/Optimization/AdminServiceProvider.php',
    'WP_Rocket\\Engine\\Optimization\\AssetsLocalCache' => $baseDir . '/inc/Engine/Optimization/AssetsLocalCache.php',
    'WP_Rocket\\Engine\\Optimization\\Buffer\\Optimization' => $baseDir . '/inc/Engine/Optimization/Buffer/Optimization.php',
    'WP_Rocket\\Engine\\Optimization\\Buffer\\Subscriber' => $baseDir . '/inc/Engine/Optimization/Buffer/Subscriber.php',
    'WP_Rocket\\Engine\\Optimization\\CSSTrait' => $baseDir . '/inc/Engine/Optimization/CSSTrait.php',
    'WP_Rocket\\Engine\\Optimization\\CacheDynamicResource' => $baseDir . '/inc/Engine/Optimization/CacheDynamicResource.php',
    'WP_Rocket\\Engine\\Optimization\\ContentTrait' => $baseDir . '/inc/Engine/Optimization/ContentTrait.php',
    'WP_Rocket\\Engine\\Optimization\\DeferJS\\AdminSubscriber' => $baseDir . '/inc/Engine/Optimization/DeferJS/AdminSubscriber.php',
    'WP_Rocket\\Engine\\Optimization\\DeferJS\\DeferJS' => $baseDir . '/inc/Engine/Optimization/DeferJS/DeferJS.php',
    'WP_Rocket\\Engine\\Optimization\\DeferJS\\ServiceProvider' => $baseDir . '/inc/Engine/Optimization/DeferJS/ServiceProvider.php',
    'WP_Rocket\\Engine\\Optimization\\DeferJS\\Subscriber' => $baseDir . '/inc/Engine/Optimization/DeferJS/Subscriber.php',
    'WP_Rocket\\Engine\\Optimization\\DelayJS\\Admin\\Settings' => $baseDir . '/inc/Engine/Optimization/DelayJS/Admin/Settings.php',
    'WP_Rocket\\Engine\\Optimization\\DelayJS\\Admin\\SiteList' => $baseDir . '/inc/Engine/Optimization/DelayJS/Admin/SiteList.php',
    'WP_Rocket\\Engine\\Optimization\\DelayJS\\Admin\\Subscriber' => $baseDir . '/inc/Engine/Optimization/DelayJS/Admin/Subscriber.php',
    'WP_Rocket\\Engine\\Optimization\\DelayJS\\HTML' => $baseDir . '/inc/Engine/Optimization/DelayJS/HTML.php',
    'WP_Rocket\\Engine\\Optimization\\DelayJS\\ServiceProvider' => $baseDir . '/inc/Engine/Optimization/DelayJS/ServiceProvider.php',
    'WP_Rocket\\Engine\\Optimization\\DelayJS\\Subscriber' => $baseDir . '/inc/Engine/Optimization/DelayJS/Subscriber.php',
    'WP_Rocket\\Engine\\Optimization\\DynamicLists\\AbstractAPIClient' => $baseDir . '/inc/Engine/Optimization/DynamicLists/AbstractAPIClient.php',
    'WP_Rocket\\Engine\\Optimization\\DynamicLists\\AbstractDataManager' => $baseDir . '/inc/Engine/Optimization/DynamicLists/AbstractDataManager.php',
    'WP_Rocket\\Engine\\Optimization\\DynamicLists\\DefaultLists\\APIClient' => $baseDir . '/inc/Engine/Optimization/DynamicLists/DefaultLists/APIClient.php',
    'WP_Rocket\\Engine\\Optimization\\DynamicLists\\DefaultLists\\DataManager' => $baseDir . '/inc/Engine/Optimization/DynamicLists/DefaultLists/DataManager.php',
    'WP_Rocket\\Engine\\Optimization\\DynamicLists\\DelayJSLists\\APIClient' => $baseDir . '/inc/Engine/Optimization/DynamicLists/DelayJSLists/APIClient.php',
    'WP_Rocket\\Engine\\Optimization\\DynamicLists\\DelayJSLists\\DataManager' => $baseDir . '/inc/Engine/Optimization/DynamicLists/DelayJSLists/DataManager.php',
    'WP_Rocket\\Engine\\Optimization\\DynamicLists\\DynamicLists' => $baseDir . '/inc/Engine/Optimization/DynamicLists/DynamicLists.php',
    'WP_Rocket\\Engine\\Optimization\\DynamicLists\\IncompatiblePluginsLists\\APIClient' => $baseDir . '/inc/Engine/Optimization/DynamicLists/IncompatiblePluginsLists/APIClient.php',
    'WP_Rocket\\Engine\\Optimization\\DynamicLists\\IncompatiblePluginsLists\\DataManager' => $baseDir . '/inc/Engine/Optimization/DynamicLists/IncompatiblePluginsLists/DataManager.php',
    'WP_Rocket\\Engine\\Optimization\\DynamicLists\\ServiceProvider' => $baseDir . '/inc/Engine/Optimization/DynamicLists/ServiceProvider.php',
    'WP_Rocket\\Engine\\Optimization\\DynamicLists\\Subscriber' => $baseDir . '/inc/Engine/Optimization/DynamicLists/Subscriber.php',
    'WP_Rocket\\Engine\\Optimization\\GoogleFonts\\AbstractGFOptimization' => $baseDir . '/inc/Engine/Optimization/GoogleFonts/AbstractGFOptimization.php',
    'WP_Rocket\\Engine\\Optimization\\GoogleFonts\\Admin\\Settings' => $baseDir . '/inc/Engine/Optimization/GoogleFonts/Admin/Settings.php',
    'WP_Rocket\\Engine\\Optimization\\GoogleFonts\\Admin\\Subscriber' => $baseDir . '/inc/Engine/Optimization/GoogleFonts/Admin/Subscriber.php',
    'WP_Rocket\\Engine\\Optimization\\GoogleFonts\\Combine' => $baseDir . '/inc/Engine/Optimization/GoogleFonts/Combine.php',
    'WP_Rocket\\Engine\\Optimization\\GoogleFonts\\CombineV2' => $baseDir . '/inc/Engine/Optimization/GoogleFonts/CombineV2.php',
    'WP_Rocket\\Engine\\Optimization\\GoogleFonts\\Subscriber' => $baseDir . '/inc/Engine/Optimization/GoogleFonts/Subscriber.php',
    'WP_Rocket\\Engine\\Optimization\\IEConditionalSubscriber' => $baseDir . '/inc/Engine/Optimization/IEConditionalSubscriber.php',
    'WP_Rocket\\Engine\\Optimization\\Minify\\AbstractMinifySubscriber' => $baseDir . '/inc/Engine/Optimization/Minify/AbstractMinifySubscriber.php',
    'WP_Rocket\\Engine\\Optimization\\Minify\\AdminSubscriber' => $baseDir . '/inc/Engine/Optimization/Minify/AdminSubscriber.php',
    'WP_Rocket\\Engine\\Optimization\\Minify\\CSS\\AbstractCSSOptimization' => $baseDir . '/inc/Engine/Optimization/Minify/CSS/AbstractCSSOptimization.php',
    'WP_Rocket\\Engine\\Optimization\\Minify\\CSS\\AdminSubscriber' => $baseDir . '/inc/Engine/Optimization/Minify/CSS/AdminSubscriber.php',
    'WP_Rocket\\Engine\\Optimization\\Minify\\CSS\\Minify' => $baseDir . '/inc/Engine/Optimization/Minify/CSS/Minify.php',
    'WP_Rocket\\Engine\\Optimization\\Minify\\CSS\\Subscriber' => $baseDir . '/inc/Engine/Optimization/Minify/CSS/Subscriber.php',
    'WP_Rocket\\Engine\\Optimization\\Minify\\JS\\AbstractJSOptimization' => $baseDir . '/inc/Engine/Optimization/Minify/JS/AbstractJSOptimization.php',
    'WP_Rocket\\Engine\\Optimization\\Minify\\JS\\Combine' => $baseDir . '/inc/Engine/Optimization/Minify/JS/Combine.php',
    'WP_Rocket\\Engine\\Optimization\\Minify\\JS\\Minify' => $baseDir . '/inc/Engine/Optimization/Minify/JS/Minify.php',
    'WP_Rocket\\Engine\\Optimization\\Minify\\JS\\Subscriber' => $baseDir . '/inc/Engine/Optimization/Minify/JS/Subscriber.php',
    'WP_Rocket\\Engine\\Optimization\\Minify\\ProcessorInterface' => $baseDir . '/inc/Engine/Optimization/Minify/ProcessorInterface.php',
    'WP_Rocket\\Engine\\Optimization\\QueryString\\Remove' => $baseDir . '/inc/deprecated/Engine/Optimization/QueryString/Remove.php',
    'WP_Rocket\\Engine\\Optimization\\QueryString\\RemoveSubscriber' => $baseDir . '/inc/deprecated/Engine/Optimization/QueryString/RemoveSubscriber.php',
    'WP_Rocket\\Engine\\Optimization\\RUCSS\\Admin\\Database' => $baseDir . '/inc/Engine/Optimization/RUCSS/Admin/Database.php',
    'WP_Rocket\\Engine\\Optimization\\RUCSS\\Admin\\OptionSubscriber' => $baseDir . '/inc/Engine/Optimization/RUCSS/Admin/OptionSubscriber.php',
    'WP_Rocket\\Engine\\Optimization\\RUCSS\\Admin\\Settings' => $baseDir . '/inc/Engine/Optimization/RUCSS/Admin/Settings.php',
    'WP_Rocket\\Engine\\Optimization\\RUCSS\\Admin\\Subscriber' => $baseDir . '/inc/Engine/Optimization/RUCSS/Admin/Subscriber.php',
    'WP_Rocket\\Engine\\Optimization\\RUCSS\\Context\\RUCSSContext' => $baseDir . '/inc/Engine/Optimization/RUCSS/Context/RUCSSContext.php',
    'WP_Rocket\\Engine\\Optimization\\RUCSS\\Context\\RUCSSContextSaas' => $baseDir . '/inc/Engine/Optimization/RUCSS/Context/RUCSSContextSaas.php',
    'WP_Rocket\\Engine\\Optimization\\RUCSS\\Context\\RUCSSOptimizeContext' => $baseDir . '/inc/Engine/Optimization/RUCSS/Context/RUCSSOptimizeContext.php',
    'WP_Rocket\\Engine\\Optimization\\RUCSS\\Controller\\Filesystem' => $baseDir . '/inc/Engine/Optimization/RUCSS/Controller/Filesystem.php',
    'WP_Rocket\\Engine\\Optimization\\RUCSS\\Controller\\UsedCSS' => $baseDir . '/inc/Engine/Optimization/RUCSS/Controller/UsedCSS.php',
    'WP_Rocket\\Engine\\Optimization\\RUCSS\\Cron\\Subscriber' => $baseDir . '/inc/Engine/Optimization/RUCSS/Cron/Subscriber.php',
    'WP_Rocket\\Engine\\Optimization\\RUCSS\\Database\\Queries\\UsedCSS' => $baseDir . '/inc/Engine/Optimization/RUCSS/Database/Queries/UsedCSS.php',
    'WP_Rocket\\Engine\\Optimization\\RUCSS\\Database\\Row\\UsedCSS' => $baseDir . '/inc/Engine/Optimization/RUCSS/Database/Row/UsedCSS.php',
    'WP_Rocket\\Engine\\Optimization\\RUCSS\\Database\\Schemas\\UsedCSS' => $baseDir . '/inc/Engine/Optimization/RUCSS/Database/Schemas/UsedCSS.php',
    'WP_Rocket\\Engine\\Optimization\\RUCSS\\Database\\Tables\\UsedCSS' => $baseDir . '/inc/Engine/Optimization/RUCSS/Database/Tables/UsedCSS.php',
    'WP_Rocket\\Engine\\Optimization\\RUCSS\\Frontend\\Subscriber' => $baseDir . '/inc/Engine/Optimization/RUCSS/Frontend/Subscriber.php',
    'WP_Rocket\\Engine\\Optimization\\RUCSS\\Jobs\\Factory' => $baseDir . '/inc/Engine/Optimization/RUCSS/Jobs/Factory.php',
    'WP_Rocket\\Engine\\Optimization\\RUCSS\\Jobs\\Manager' => $baseDir . '/inc/Engine/Optimization/RUCSS/Jobs/Manager.php',
    'WP_Rocket\\Engine\\Optimization\\RUCSS\\ServiceProvider' => $baseDir . '/inc/Engine/Optimization/RUCSS/ServiceProvider.php',
    'WP_Rocket\\Engine\\Optimization\\RegexTrait' => $baseDir . '/inc/Engine/Optimization/RegexTrait.php',
    'WP_Rocket\\Engine\\Optimization\\ServiceProvider' => $baseDir . '/inc/Engine/Optimization/ServiceProvider.php',
    'WP_Rocket\\Engine\\Optimization\\UrlTrait' => $baseDir . '/inc/Engine/Optimization/UrlTrait.php',
    'WP_Rocket\\Engine\\Plugin\\InformationSubscriber' => $baseDir . '/inc/Engine/Plugin/InformationSubscriber.php',
    'WP_Rocket\\Engine\\Plugin\\RenewalNotice' => $baseDir . '/inc/Engine/Plugin/RenewalNotice.php',
    'WP_Rocket\\Engine\\Plugin\\ServiceProvider' => $baseDir . '/inc/Engine/Plugin/ServiceProvider.php',
    'WP_Rocket\\Engine\\Plugin\\UpdaterApiCommonSubscriber' => $baseDir . '/inc/Engine/Plugin/UpdaterApiCommonSubscriber.php',
    'WP_Rocket\\Engine\\Plugin\\UpdaterApiTools' => $baseDir . '/inc/Engine/Plugin/UpdaterApiTools.php',
    'WP_Rocket\\Engine\\Plugin\\UpdaterSubscriber' => $baseDir . '/inc/Engine/Plugin/UpdaterSubscriber.php',
    'WP_Rocket\\Engine\\Preload\\Activation\\Activation' => $baseDir . '/inc/Engine/Preload/Activation/Activation.php',
    'WP_Rocket\\Engine\\Preload\\Activation\\ServiceProvider' => $baseDir . '/inc/Engine/Preload/Activation/ServiceProvider.php',
    'WP_Rocket\\Engine\\Preload\\Admin\\Settings' => $baseDir . '/inc/Engine/Preload/Admin/Settings.php',
    'WP_Rocket\\Engine\\Preload\\Admin\\Subscriber' => $baseDir . '/inc/Engine/Preload/Admin/Subscriber.php',
    'WP_Rocket\\Engine\\Preload\\Controller\\CheckExcludedTrait' => $baseDir . '/inc/Engine/Preload/Controller/CheckExcludedTrait.php',
    'WP_Rocket\\Engine\\Preload\\Controller\\CheckFinished' => $baseDir . '/inc/Engine/Preload/Controller/CheckFinished.php',
    'WP_Rocket\\Engine\\Preload\\Controller\\ClearCache' => $baseDir . '/inc/Engine/Preload/Controller/ClearCache.php',
    'WP_Rocket\\Engine\\Preload\\Controller\\CrawlHomepage' => $baseDir . '/inc/Engine/Preload/Controller/CrawlHomepage.php',
    'WP_Rocket\\Engine\\Preload\\Controller\\LoadInitialSitemap' => $baseDir . '/inc/Engine/Preload/Controller/LoadInitialSitemap.php',
    'WP_Rocket\\Engine\\Preload\\Controller\\PreloadUrl' => $baseDir . '/inc/Engine/Preload/Controller/PreloadUrl.php',
    'WP_Rocket\\Engine\\Preload\\Controller\\Queue' => $baseDir . '/inc/Engine/Preload/Controller/Queue.php',
    'WP_Rocket\\Engine\\Preload\\Cron\\Subscriber' => $baseDir . '/inc/Engine/Preload/Cron/Subscriber.php',
    'WP_Rocket\\Engine\\Preload\\Database\\Queries\\Cache' => $baseDir . '/inc/Engine/Preload/Database/Queries/Cache.php',
    'WP_Rocket\\Engine\\Preload\\Database\\Rows\\CacheRow' => $baseDir . '/inc/Engine/Preload/Database/Rows/CacheRow.php',
    'WP_Rocket\\Engine\\Preload\\Database\\Schemas\\Cache' => $baseDir . '/inc/Engine/Preload/Database/Schemas/Cache.php',
    'WP_Rocket\\Engine\\Preload\\Database\\Tables\\Cache' => $baseDir . '/inc/Engine/Preload/Database/Tables/Cache.php',
    'WP_Rocket\\Engine\\Preload\\Fonts' => $baseDir . '/inc/Engine/Preload/Fonts.php',
    'WP_Rocket\\Engine\\Preload\\Frontend\\FetchSitemap' => $baseDir . '/inc/Engine/Preload/Frontend/FetchSitemap.php',
    'WP_Rocket\\Engine\\Preload\\Frontend\\SitemapParser' => $baseDir . '/inc/Engine/Preload/Frontend/SitemapParser.php',
    'WP_Rocket\\Engine\\Preload\\Frontend\\Subscriber' => $baseDir . '/inc/Engine/Preload/Frontend/Subscriber.php',
    'WP_Rocket\\Engine\\Preload\\Links\\AdminSubscriber' => $baseDir . '/inc/Engine/Preload/Links/AdminSubscriber.php',
    'WP_Rocket\\Engine\\Preload\\Links\\ServiceProvider' => $baseDir . '/inc/Engine/Preload/Links/ServiceProvider.php',
    'WP_Rocket\\Engine\\Preload\\Links\\Subscriber' => $baseDir . '/inc/Engine/Preload/Links/Subscriber.php',
    'WP_Rocket\\Engine\\Preload\\ServiceProvider' => $baseDir . '/inc/Engine/Preload/ServiceProvider.php',
    'WP_Rocket\\Engine\\Preload\\Subscriber' => $baseDir . '/inc/Engine/Preload/Subscriber.php',
    'WP_Rocket\\Engine\\Saas\\Admin\\AdminBar' => $baseDir . '/inc/Engine/Saas/Admin/AdminBar.php',
    'WP_Rocket\\Engine\\Saas\\Admin\\Clean' => $baseDir . '/inc/Engine/Saas/Admin/Clean.php',
    'WP_Rocket\\Engine\\Saas\\Admin\\Notices' => $baseDir . '/inc/Engine/Saas/Admin/Notices.php',
    'WP_Rocket\\Engine\\Saas\\Admin\\Subscriber' => $baseDir . '/inc/Engine/Saas/Admin/Subscriber.php',
    'WP_Rocket\\Engine\\Saas\\ServiceProvider' => $baseDir . '/inc/Engine/Saas/ServiceProvider.php',
    'WP_Rocket\\Engine\\Support\\Data' => $baseDir . '/inc/Engine/Support/Data.php',
    'WP_Rocket\\Engine\\Support\\Rest' => $baseDir . '/inc/Engine/Support/Rest.php',
    'WP_Rocket\\Engine\\Support\\ServiceProvider' => $baseDir . '/inc/Engine/Support/ServiceProvider.php',
    'WP_Rocket\\Engine\\Support\\Subscriber' => $baseDir . '/inc/Engine/Support/Subscriber.php',
    'WP_Rocket\\Event_Management\\Event_Manager' => $baseDir . '/inc/classes/event-management/class-event-manager.php',
    'WP_Rocket\\Event_Management\\Event_Manager_Aware_Subscriber_Interface' => $baseDir . '/inc/classes/event-management/event-manager-aware-subscriber-interface.php',
    'WP_Rocket\\Event_Management\\Subscriber_Interface' => $baseDir . '/inc/classes/event-management/subscriber-interface.php',
    'WP_Rocket\\Interfaces\\Render_Interface' => $baseDir . '/inc/classes/interfaces/class-render-interface.php',
    'WP_Rocket\\Logger\\HTMLFormatter' => $baseDir . '/inc/Logger/HTMLFormatter.php',
    'WP_Rocket\\Logger\\Logger' => $baseDir . '/inc/Logger/Logger.php',
    'WP_Rocket\\Logger\\LoggerAware' => $baseDir . '/inc/Logger/LoggerAware.php',
    'WP_Rocket\\Logger\\LoggerAwareInterface' => $baseDir . '/inc/Logger/LoggerAwareInterface.php',
    'WP_Rocket\\Logger\\ServiceProvider' => $baseDir . '/inc/Logger/ServiceProvider.php',
    'WP_Rocket\\Logger\\StreamHandler' => $baseDir . '/inc/Logger/StreamHandler.php',
    'WP_Rocket\\Plugin' => $baseDir . '/inc/Plugin.php',
    'WP_Rocket\\ServiceProvider\\Common_Subscribers' => $baseDir . '/inc/classes/ServiceProvider/class-common-subscribers.php',
    'WP_Rocket\\ServiceProvider\\Options' => $baseDir . '/inc/classes/ServiceProvider/class-options.php',
    'WP_Rocket\\Subscriber\\Admin\\Settings\\Beacon_Subscriber' => $baseDir . '/inc/deprecated/subscriber/admin/Settings/class-beacon-subscriber.php',
    'WP_Rocket\\Subscriber\\Optimization\\Dequeue_JQuery_Migrate_Subscriber' => $baseDir . '/inc/deprecated/subscriber/Optimization/class-dequeue-jquery-migrate-subscriber.php',
    'WP_Rocket\\Subscriber\\Optimization\\Minify_HTML_Subscriber' => $baseDir . '/inc/deprecated/subscriber/admin/Optimization/class-minify-html-subscriber.php',
    'WP_Rocket\\Subscriber\\Third_Party\\Plugins\\Images\\Webp\\EWWW_Subscriber' => $baseDir . '/inc/classes/subscriber/third-party/plugins/Images/Webp/class-ewww-subscriber.php',
    'WP_Rocket\\Subscriber\\Third_Party\\Plugins\\Images\\Webp\\Imagify_Subscriber' => $baseDir . '/inc/classes/subscriber/third-party/plugins/Images/Webp/class-imagify-subscriber.php',
    'WP_Rocket\\Subscriber\\Third_Party\\Plugins\\Images\\Webp\\Optimus_Subscriber' => $baseDir . '/inc/classes/subscriber/third-party/plugins/Images/Webp/class-optimus-subscriber.php',
    'WP_Rocket\\Subscriber\\Third_Party\\Plugins\\Images\\Webp\\ShortPixel_Subscriber' => $baseDir . '/inc/classes/subscriber/third-party/plugins/Images/Webp/class-shortpixel-subscriber.php',
    'WP_Rocket\\Subscriber\\Third_Party\\Plugins\\Images\\Webp\\Webp_Common' => $baseDir . '/inc/classes/subscriber/third-party/plugins/Images/Webp/trait-webp-common.php',
    'WP_Rocket\\Subscriber\\Third_Party\\Plugins\\Images\\Webp\\Webp_Interface' => $baseDir . '/inc/classes/subscriber/third-party/plugins/Images/Webp/webp-interface.php',
    'WP_Rocket\\Subscriber\\Third_Party\\Plugins\\Mobile_Subscriber' => $baseDir . '/inc/classes/subscriber/third-party/plugins/class-mobile-subscriber.php',
    'WP_Rocket\\Subscriber\\Third_Party\\Plugins\\NGG_Subscriber' => $baseDir . '/inc/classes/subscriber/third-party/plugins/class-ngg-subscriber.php',
    'WP_Rocket\\Subscriber\\Third_Party\\Plugins\\SyntaxHighlighter_Subscriber' => $baseDir . '/inc/classes/subscriber/third-party/plugins/class-syntaxhighlighter-subscriber.php',
    'WP_Rocket\\Subscriber\\Tools\\Detect_Missing_Tags_Subscriber' => $baseDir . '/inc/classes/subscriber/Tools/class-detect-missing-tags-subscriber.php',
    'WP_Rocket\\ThirdParty\\Hostings\\AbstractNoCacheHost' => $baseDir . '/inc/ThirdParty/Hostings/AbstractNoCacheHost.php',
    'WP_Rocket\\ThirdParty\\Hostings\\Cloudways' => $baseDir . '/inc/ThirdParty/Hostings/Cloudways.php',
    'WP_Rocket\\ThirdParty\\Hostings\\Dreampress' => $baseDir . '/inc/ThirdParty/Hostings/Dreampress.php',
    'WP_Rocket\\ThirdParty\\Hostings\\Godaddy' => $baseDir . '/inc/ThirdParty/Hostings/Godaddy.php',
    'WP_Rocket\\ThirdParty\\Hostings\\HostResolver' => $baseDir . '/inc/ThirdParty/Hostings/HostResolver.php',
    'WP_Rocket\\ThirdParty\\Hostings\\HostSubscriberFactory' => $baseDir . '/inc/ThirdParty/Hostings/HostSubscriberFactory.php',
    'WP_Rocket\\ThirdParty\\Hostings\\Kinsta' => $baseDir . '/inc/ThirdParty/Hostings/Kinsta.php',
    'WP_Rocket\\ThirdParty\\Hostings\\LiteSpeed' => $baseDir . '/inc/ThirdParty/Hostings/LiteSpeed.php',
    'WP_Rocket\\ThirdParty\\Hostings\\O2Switch' => $baseDir . '/inc/ThirdParty/Hostings/O2Switch.php',
    'WP_Rocket\\ThirdParty\\Hostings\\OneCom' => $baseDir . '/inc/ThirdParty/Hostings/OneCom.php',
    'WP_Rocket\\ThirdParty\\Hostings\\Pressable' => $baseDir . '/inc/ThirdParty/Hostings/Pressable.php',
    'WP_Rocket\\ThirdParty\\Hostings\\Pressidium' => $baseDir . '/inc/ThirdParty/Hostings/Pressidium.php',
    'WP_Rocket\\ThirdParty\\Hostings\\ProIsp' => $baseDir . '/inc/ThirdParty/Hostings/ProIsp.php',
    'WP_Rocket\\ThirdParty\\Hostings\\Savvii' => $baseDir . '/inc/ThirdParty/Hostings/Savvii.php',
    'WP_Rocket\\ThirdParty\\Hostings\\ServiceProvider' => $baseDir . '/inc/ThirdParty/Hostings/ServiceProvider.php',
    'WP_Rocket\\ThirdParty\\Hostings\\SpinUpWP' => $baseDir . '/inc/ThirdParty/Hostings/SpinUpWP.php',
    'WP_Rocket\\ThirdParty\\Hostings\\WPEngine' => $baseDir . '/inc/ThirdParty/Hostings/WPEngine.php',
    'WP_Rocket\\ThirdParty\\Hostings\\WPXCloud' => $baseDir . '/inc/ThirdParty/Hostings/WPXCloud.php',
    'WP_Rocket\\ThirdParty\\Hostings\\WordPressCom' => $baseDir . '/inc/ThirdParty/Hostings/WordPressCom.php',
    'WP_Rocket\\ThirdParty\\NullSubscriber' => $baseDir . '/inc/ThirdParty/NullSubscriber.php',
    'WP_Rocket\\ThirdParty\\Plugins\\Ads\\Adthrive' => $baseDir . '/inc/ThirdParty/Plugins/Ads/Adthrive.php',
    'WP_Rocket\\ThirdParty\\Plugins\\CDN\\Cloudflare' => $baseDir . '/inc/ThirdParty/Plugins/CDN/Cloudflare.php',
    'WP_Rocket\\ThirdParty\\Plugins\\CDN\\CloudflareFacade' => $baseDir . '/inc/ThirdParty/Plugins/CDN/CloudflareFacade.php',
    'WP_Rocket\\ThirdParty\\Plugins\\ContactForm7' => $baseDir . '/inc/ThirdParty/Plugins/ContactForm7.php',
    'WP_Rocket\\ThirdParty\\Plugins\\ConvertPlug' => $baseDir . '/inc/ThirdParty/Plugins/ConvertPlug.php',
    'WP_Rocket\\ThirdParty\\Plugins\\Ecommerce\\BigCommerce' => $baseDir . '/inc/ThirdParty/Plugins/Ecommerce/BigCommerce.php',
    'WP_Rocket\\ThirdParty\\Plugins\\Ecommerce\\WooCommerceSubscriber' => $baseDir . '/inc/ThirdParty/Plugins/Ecommerce/WooCommerceSubscriber.php',
    'WP_Rocket\\ThirdParty\\Plugins\\I18n\\TranslatePress' => $baseDir . '/inc/ThirdParty/Plugins/I18n/TranslatePress.php',
    'WP_Rocket\\ThirdParty\\Plugins\\I18n\\WPML' => $baseDir . '/inc/ThirdParty/Plugins/I18n/WPML.php',
    'WP_Rocket\\ThirdParty\\Plugins\\I18n\\Weglot' => $baseDir . '/inc/ThirdParty/Plugins/I18n/Weglot.php',
    'WP_Rocket\\ThirdParty\\Plugins\\InlineRelatedPosts' => $baseDir . '/inc/ThirdParty/Plugins/InlineRelatedPosts.php',
    'WP_Rocket\\ThirdParty\\Plugins\\Jetpack' => $baseDir . '/inc/ThirdParty/Plugins/Jetpack.php',
    'WP_Rocket\\ThirdParty\\Plugins\\ModPagespeed' => $baseDir . '/inc/ThirdParty/Plugins/ModPagespeed.php',
    'WP_Rocket\\ThirdParty\\Plugins\\Optimization\\AMP' => $baseDir . '/inc/ThirdParty/Plugins/Optimization/AMP.php',
    'WP_Rocket\\ThirdParty\\Plugins\\Optimization\\Autoptimize' => $baseDir . '/inc/ThirdParty/Plugins/Optimization/Autoptimize.php',
    'WP_Rocket\\ThirdParty\\Plugins\\Optimization\\Ezoic' => $baseDir . '/inc/ThirdParty/Plugins/Optimization/Ezoic.php',
    'WP_Rocket\\ThirdParty\\Plugins\\Optimization\\Hummingbird' => $baseDir . '/inc/ThirdParty/Plugins/Optimization/Hummingbird.php',
    'WP_Rocket\\ThirdParty\\Plugins\\Optimization\\Perfmatters' => $baseDir . '/inc/ThirdParty/Plugins/Optimization/Perfmatters.php',
    'WP_Rocket\\ThirdParty\\Plugins\\Optimization\\RapidLoad' => $baseDir . '/inc/ThirdParty/Plugins/Optimization/RapidLoad.php',
    'WP_Rocket\\ThirdParty\\Plugins\\Optimization\\RocketLazyLoad' => $baseDir . '/inc/ThirdParty/Plugins/Optimization/RocketLazyLoad.php',
    'WP_Rocket\\ThirdParty\\Plugins\\Optimization\\WPMeteor' => $baseDir . '/inc/ThirdParty/Plugins/Optimization/WPMeteor.php',
    'WP_Rocket\\ThirdParty\\Plugins\\PDFEmbedder' => $baseDir . '/inc/ThirdParty/Plugins/PDFEmbedder.php',
    'WP_Rocket\\ThirdParty\\Plugins\\PWA' => $baseDir . '/inc/ThirdParty/Plugins/PWA.php',
    'WP_Rocket\\ThirdParty\\Plugins\\PageBuilder\\BeaverBuilder' => $baseDir . '/inc/ThirdParty/Plugins/PageBuilder/BeaverBuilder.php',
    'WP_Rocket\\ThirdParty\\Plugins\\PageBuilder\\Elementor' => $baseDir . '/inc/ThirdParty/Plugins/PageBuilder/Elementor.php',
    'WP_Rocket\\ThirdParty\\Plugins\\RevolutionSlider' => $baseDir . '/inc/ThirdParty/Plugins/RevolutionSlider.php',
    'WP_Rocket\\ThirdParty\\Plugins\\SEO\\AllInOneSEOPack' => $baseDir . '/inc/ThirdParty/Plugins/SEO/AllInOneSEOPack.php',
    'WP_Rocket\\ThirdParty\\Plugins\\SEO\\RankMathSEO' => $baseDir . '/inc/ThirdParty/Plugins/SEO/RankMathSEO.php',
    'WP_Rocket\\ThirdParty\\Plugins\\SEO\\SEOPress' => $baseDir . '/inc/ThirdParty/Plugins/SEO/SEOPress.php',
    'WP_Rocket\\ThirdParty\\Plugins\\SEO\\TheSEOFramework' => $baseDir . '/inc/ThirdParty/Plugins/SEO/TheSEOFramework.php',
    'WP_Rocket\\ThirdParty\\Plugins\\SEO\\Yoast' => $baseDir . '/inc/ThirdParty/Plugins/SEO/Yoast.php',
    'WP_Rocket\\ThirdParty\\Plugins\\Security\\WordFenceCompatibility' => $baseDir . '/inc/ThirdParty/Plugins/Security/WordFenceCompatibility.php',
    'WP_Rocket\\ThirdParty\\Plugins\\SimpleCustomCss' => $baseDir . '/inc/ThirdParty/Plugins/SimpleCustomCss.php',
    'WP_Rocket\\ThirdParty\\Plugins\\Smush' => $baseDir . '/inc/ThirdParty/Plugins/Smush.php',
    'WP_Rocket\\ThirdParty\\Plugins\\TheEventsCalendar' => $baseDir . '/inc/ThirdParty/Plugins/TheEventsCalendar.php',
    'WP_Rocket\\ThirdParty\\Plugins\\ThirstyAffiliates' => $baseDir . '/inc/ThirdParty/Plugins/ThirstyAffiliates.php',
    'WP_Rocket\\ThirdParty\\Plugins\\UnlimitedElements' => $baseDir . '/inc/ThirdParty/Plugins/UnlimitedElements.php',
    'WP_Rocket\\ThirdParty\\Plugins\\WPGeotargeting' => $baseDir . '/inc/ThirdParty/Plugins/WPGeotargeting.php',
    'WP_Rocket\\ThirdParty\\ReturnTypesTrait' => $baseDir . '/inc/ThirdParty/ReturnTypesTrait.php',
    'WP_Rocket\\ThirdParty\\ServiceProvider' => $baseDir . '/inc/ThirdParty/ServiceProvider.php',
    'WP_Rocket\\ThirdParty\\SubscriberFactoryInterface' => $baseDir . '/inc/ThirdParty/SubscriberFactoryInterface.php',
    'WP_Rocket\\ThirdParty\\Themes\\Avada' => $baseDir . '/inc/ThirdParty/Themes/Avada.php',
    'WP_Rocket\\ThirdParty\\Themes\\Bridge' => $baseDir . '/inc/ThirdParty/Themes/Bridge.php',
    'WP_Rocket\\ThirdParty\\Themes\\Divi' => $baseDir . '/inc/ThirdParty/Themes/Divi.php',
    'WP_Rocket\\ThirdParty\\Themes\\Flatsome' => $baseDir . '/inc/ThirdParty/Themes/Flatsome.php',
    'WP_Rocket\\ThirdParty\\Themes\\Jevelin' => $baseDir . '/inc/ThirdParty/Themes/Jevelin.php',
    'WP_Rocket\\ThirdParty\\Themes\\MinimalistBlogger' => $baseDir . '/inc/ThirdParty/Themes/MinimalistBlogger.php',
    'WP_Rocket\\ThirdParty\\Themes\\Polygon' => $baseDir . '/inc/ThirdParty/Themes/Polygon.php',
    'WP_Rocket\\ThirdParty\\Themes\\ServiceProvider' => $baseDir . '/inc/ThirdParty/Themes/ServiceProvider.php',
    'WP_Rocket\\ThirdParty\\Themes\\Shoptimizer' => $baseDir . '/inc/ThirdParty/Themes/Shoptimizer.php',
    'WP_Rocket\\ThirdParty\\Themes\\SubscriberFactory' => $baseDir . '/inc/ThirdParty/Themes/SubscriberFactory.php',
    'WP_Rocket\\ThirdParty\\Themes\\ThemeResolver' => $baseDir . '/inc/ThirdParty/Themes/ThemeResolver.php',
    'WP_Rocket\\ThirdParty\\Themes\\Themify' => $baseDir . '/inc/ThirdParty/Themes/Themify.php',
    'WP_Rocket\\ThirdParty\\Themes\\Uncode' => $baseDir . '/inc/ThirdParty/Themes/Uncode.php',
    'WP_Rocket\\ThirdParty\\Themes\\Xstore' => $baseDir . '/inc/ThirdParty/Themes/Xstore.php',
    'WP_Rocket\\Traits\\Config_Updater' => $baseDir . '/inc/classes/traits/trait-config-updater.php',
    'WP_Rocket\\Traits\\Memoize' => $baseDir . '/inc/classes/traits/trait-memoize.php',
    'WP_Rocket\\deprecated\\DeprecatedClassTrait' => $baseDir . '/inc/deprecated/DeprecatedClassTrait.php',
    'WP_Rocket\\deprecated\\Engine\\Media\\Embeds\\EmbedsSubscriber' => $baseDir . '/inc/deprecated/Engine/Media/Embeds/EmbedsSubscriber.php',
    'WP_Rocket_Mobile_Detect' => $baseDir . '/inc/classes/dependencies/mobiledetect/mobiledetectlib/Mobile_Detect.php',
    'WP_Rocket_WP_Async_Request' => $baseDir . '/inc/classes/dependencies/wp-media/background-processing/wp-async-request.php',
    'WP_Rocket_WP_Background_Process' => $baseDir . '/inc/classes/dependencies/wp-media/background-processing/wp-background-process.php',
);
