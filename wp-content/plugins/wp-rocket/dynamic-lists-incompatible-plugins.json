{"": [{"slug": "wp-super-cache", "file": "wp-super-cache/wp-cache.php"}, {"slug": "enable-gzip-compression", "file": "enable-gzip-compression/enable-gzip-compression.php"}, {"slug": "quick-cache", "file": "quick-cache/quick-cache.php"}, {"slug": "leverage-browser-caching-ninjas", "file": "leverage-browser-caching-ninjas/leverage-browser-caching-ninja.php"}, {"slug": "wp-performance-score-booster", "file": "wp-performance-score-booster/wp-performance-score-booster.php"}, {"slug": "litespeed-cache", "file": "litespeed-cache/litespeed-cache.php"}, {"slug": "remove-query-strings-from-static-resources", "file": "remove-query-strings-from-static-resources/remove-query-strings.php"}, {"slug": "wp-http-compression", "file": "wp-http-compression/wp-http-compression.php"}, {"slug": "query-strings-remover", "file": "query-strings-remover/query-strings-remover.php"}, {"slug": "page-optimize", "file": "page-optimize/page-optimize.php"}, {"slug": "speed-booster-pack", "file": "speed-booster-pack/speed-booster-pack.php"}, {"slug": "swift-performance", "file": "swift-performance/performance.php"}, {"slug": "gzip-ninja-speed-compression", "file": "gzip-ninja-speed-compression/gzip-ninja-speed.php"}, {"slug": "super-static-cache", "file": "super-static-cache/super-static-cache.php"}, {"slug": "lite-cache", "file": "lite-cache/plugin.php"}, {"slug": "hyper-cache", "file": "hyper-cache/plugin.php"}, {"slug": "wp-ffpc", "file": "wp-ffpc/wp-ffpc.php"}, {"slug": "wp-fast-cache", "file": "wp-fast-cache/wp-fast-cache.php"}, {"slug": "psn-pagespeed-ninja", "file": "psn-pagespeed-ninja/pagespeedninja.php"}, {"slug": "swift-performance-lite", "file": "swift-performance-lite/performance.php"}, {"slug": "force-gzip", "file": "force-gzip/force-gzip.php"}, {"slug": "add-expires-headers", "file": "add-expires-headers/add-expires-headers.php"}, {"slug": "hyper-cache-extended", "file": "hyper-cache-extended/plugin.php"}, {"slug": "gator-cache", "file": "gator-cache/gator-cache.php"}, {"slug": "flexicache", "file": "flexicache/wp-plugin.php"}, {"slug": "wp-fastest-cache", "file": "wp-fastest-cache/wpFastestCache.php"}, {"slug": "wordpress-gzip-compression", "file": "wordpress-gzip-compression/ezgz.php"}, {"slug": "wp-optimize", "file": "wp-optimize/wp-optimize.php"}, {"slug": "check-and-enable-gzip-compression", "file": "check-and-enable-gzip-compression/richards-toolbox.php"}, {"slug": "far-future-expiry-header", "file": "far-future-expiry-header/far-future-expiration.php"}, {"slug": "leverage-browser-caching", "file": "leverage-browser-caching/leverage-browser-caching.php"}, {"slug": "wpcompressor", "file": "wpcompressor/wpcompressor.php"}, {"slug": "combine-css", "file": "combine-css/combine-css.php"}, {"slug": "w3-total-cache", "file": "w3-total-cache/w3-total-cache.php"}, {"slug": "cache-enabler", "file": "cache-enabler/cache-enabler.php"}], "minify_css||minify_js": [{"slug": "merge-minify-refresh", "file": "merge-minify-refresh/merge-minify-refresh.php"}, {"slug": "async-js-and-css", "file": "async-js-and-css/asyncJSandCSS.php"}, {"slug": "wp-super-minify", "file": "wp-super-minify/wp-super-minify.php"}, {"slug": "fast-velocity-minify", "file": "fast-velocity-minify/fvm.php"}, {"slug": "dependency-minification", "file": "dependency-minification/dependency-minification.php"}, {"slug": "bwp-minify", "file": "bwp-minify/bwp-minify.php"}, {"slug": "minqueue", "file": "minqueue/plugin.php"}, {"slug": "scripts-gzip", "file": "scripts-gzip/scripts_gzip.php"}, {"slug": "wp-minify", "file": "wp-minify/wp-minify.php"}], "lazyload": [{"slug": "lazy-load", "file": "lazy-load/lazy-load.php"}, {"slug": "bj-lazy-load", "file": "bj-lazy-load/bj-lazy-load.php"}, {"slug": "jquery-image-lazy-loading", "file": "jquery-image-lazy-loading/jq_img_lazy_load.php"}, {"slug": "crazy-lazy", "file": "crazy-lazy/crazy-lazy.php"}, {"slug": "specify-image-dimensions", "file": "specify-image-dimensions/specify-image-dimensions.php"}, {"slug": "advanced-lazy-load", "file": "advanced-lazy-load/advanced_lazyload.php"}], "minify_js": [{"slug": "wp-js", "file": "wp-js/wp-js.php"}, {"slug": "scripts-to-footerphp", "file": "scripts-to-footerphp/scripts-to-footer.php"}, {"slug": "combine-js", "file": "combine-js/combine-js.php"}, {"slug": "footer-javascript", "file": "footer-javascript/footer-javascript.php"}], "control_heartbeat": [{"slug": "heartbeat-control", "file": "heartbeat-control/heartbeat-control.php"}], "lazyload_iframes": [{"slug": "lazy-load-for-videos", "file": "lazy-load-for-videos/codeispoetry.php"}]}