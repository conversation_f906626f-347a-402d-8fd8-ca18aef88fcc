# Advanced Custom Fields Translations are a combination of translate.wordpress.org contributions,
# combined with user contributed strings for the PRO version.
# Translations from translate.wordpress.org take priority over translations in this file.
# translate.wordpress.org contributions are synced at the time of each release.
#
# If you would like to contribute translations, please visit
# https://translate.wordpress.org/projects/wp-plugins/advanced-custom-fields/stable/
#
# For additional ACF PRO strings, please submit a pull request over on the ACF GitHub repo at
# http://github.com/advancedcustomfields/acf using the .pot (and any existing .po) files in /lang/pro/
#
# This file is distributed under the same license as Advanced Custom Fields.
msgid ""
msgstr ""
"PO-Revision-Date: 2024-06-27T14:24:00+00:00\n"
"Report-Msgid-Bugs-To: http://support.advancedcustomfields.com\n"
"Language: de_DE_formal\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: gettext\n"
"Project-Id-Version: Advanced Custom Fields\n"

#: includes/ajax/class-acf-ajax-upgrade.php:24
msgid "Sorry, you don't have permission to do that."
msgstr "Du bist leider nicht berechtigt, diese Aktion durchzuführen."

#: includes/ajax/class-acf-ajax-query-users.php:24
msgid "Invalid request args."
msgstr ""

#: includes/ajax/class-acf-ajax-local-json-diff.php:37
msgid "Sorry, you are not allowed to do that."
msgstr ""

#: includes/ajax/class-acf-ajax-check-screen.php:27
#: includes/ajax/class-acf-ajax-user-setting.php:38
msgid "Sorry, you do not have permission to do that."
msgstr ""

#: includes/class-acf-site-health.php:643
msgid "Blocks Using Post Meta"
msgstr ""

#: includes/admin/views/acf-field-group/pro-features.php:25
#: includes/admin/views/acf-field-group/pro-features.php:27
#: includes/admin/views/global/header.php:27
msgid "ACF PRO logo"
msgstr ""

#: includes/admin/views/acf-field-group/field.php:37
msgid "ACF PRO Logo"
msgstr ""

#. translators: %s - field/param name
#: includes/fields/class-acf-field-icon_picker.php:683
msgid "%s requires a valid attachment ID when type is set to media_library."
msgstr ""

#. translators: %s - field name
#: includes/fields/class-acf-field-icon_picker.php:667
msgid "%s is a required property of acf."
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:643
msgid "The value of icon to save."
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:637
msgid "The type of icon to save."
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:617
msgid "Yes icon"
msgstr "Ja-Icon"

#: includes/fields/class-acf-field-icon_picker.php:616
msgid "Wordpress-alt icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:615
msgid "Wordpress icon"
msgstr "WordPress-Icon"

#: includes/fields/class-acf-field-icon_picker.php:614
msgid "Welcome write-blog icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:613
msgid "Welcome widgets-menus icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:612
msgid "Welcome view-site icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:611
msgid "Welcome learn-more icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:610
msgid "Welcome comments icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:609
msgid "Welcome add-page icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:608
msgid "Warning icon"
msgstr "Warnung-Icon"

#: includes/fields/class-acf-field-icon_picker.php:607
msgid "Visibility icon"
msgstr "Sichtbarkeit-Icon"

#: includes/fields/class-acf-field-icon_picker.php:606
msgid "Video-alt3 icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:605
msgid "Video-alt2 icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:604
msgid "Video-alt icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:603
msgid "Vault icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:602
msgid "Upload icon"
msgstr "Upload-Icon"

#: includes/fields/class-acf-field-icon_picker.php:601
msgid "Update icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:600
msgid "Unlock icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:599
msgid "Universal access alternative icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:598
msgid "Universal access icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:597
msgid "Undo icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:596
msgid "Twitter icon"
msgstr "Twitter-Icon"

#: includes/fields/class-acf-field-icon_picker.php:595
msgid "Trash icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:594
msgid "Translation icon"
msgstr "Übersetzung-Icon"

#: includes/fields/class-acf-field-icon_picker.php:593
msgid "Tickets alternative icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:592
msgid "Tickets icon"
msgstr "Tickets-Icon"

#: includes/fields/class-acf-field-icon_picker.php:591
msgid "Thumbs-up icon"
msgstr "Daumen-hoch-Icon"

#: includes/fields/class-acf-field-icon_picker.php:590
msgid "Thumbs-down icon"
msgstr "Daumen-runter-Icon"

#: includes/fields/class-acf-field-icon_picker.php:589
msgid "Text icon"
msgstr "Text-Icon"

#: includes/fields/class-acf-field-icon_picker.php:588
msgid "Testimonial icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:587
msgid "Tagcloud icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:586
msgid "Tag icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:585
msgid "Tablet icon"
msgstr "Tablet-Icon"

#: includes/fields/class-acf-field-icon_picker.php:584
msgid "Store icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:583
msgid "Sticky icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:582
msgid "Star-half icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:581
msgid "Star-filled icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:580
msgid "Star-empty icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:579
msgid "Sos icon"
msgstr "SOS-Icon"

#: includes/fields/class-acf-field-icon_picker.php:578
msgid "Sort icon"
msgstr "Sortieren-Icon"

#: includes/fields/class-acf-field-icon_picker.php:577
msgid "Smiley icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:576
msgid "Smartphone icon"
msgstr "Smartphone-Icon"

#: includes/fields/class-acf-field-icon_picker.php:575
msgid "Slides icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:574
msgid "Shield-alt icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:573
msgid "Shield icon"
msgstr "Schild-Icon"

#: includes/fields/class-acf-field-icon_picker.php:572
msgid "Share-alt2 icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:571
msgid "Share-alt icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:570
msgid "Share icon"
msgstr "Teilen-Icon"

#: includes/fields/class-acf-field-icon_picker.php:569
msgid "Search icon"
msgstr "Suchen-Icon"

#: includes/fields/class-acf-field-icon_picker.php:568
msgid "Screenoptions icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:567
msgid "Schedule icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:566
msgid "Rss icon"
msgstr "RSS-Icon"

#: includes/fields/class-acf-field-icon_picker.php:565
msgid "Redo icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:564
msgid "Randomize icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:563
msgid "Products icon"
msgstr "Produkte-Icon"

#: includes/fields/class-acf-field-icon_picker.php:562
msgid "Pressthis icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:561
msgid "Post-status icon"
msgstr "Beitragsstatus-Icon"

#: includes/fields/class-acf-field-icon_picker.php:560
msgid "Portfolio icon"
msgstr "Portfolio-Icon"

#: includes/fields/class-acf-field-icon_picker.php:559
msgid "Plus-alt icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:558
msgid "Plus icon"
msgstr "Plus-Icon"

#: includes/fields/class-acf-field-icon_picker.php:557
msgid "Playlist-video icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:556
msgid "Playlist-audio icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:555
msgid "Phone icon"
msgstr "Telefon-Icon"

#: includes/fields/class-acf-field-icon_picker.php:554
msgid "Performance icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:553
msgid "Paperclip icon"
msgstr "Büroklammer-Icon"

#: includes/fields/class-acf-field-icon_picker.php:552
msgid "Palmtree icon"
msgstr "Palme-Icon"

#: includes/fields/class-acf-field-icon_picker.php:551
msgid "No alternative icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:550
msgid "No icon"
msgstr "Nein-Icon"

#: includes/fields/class-acf-field-icon_picker.php:549
msgid "Networking icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:548
msgid "Nametag icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:547
msgid "Move icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:546
msgid "Money icon"
msgstr "Geld-Icon"

#: includes/fields/class-acf-field-icon_picker.php:545
msgid "Minus icon"
msgstr "Minus-Icon"

#: includes/fields/class-acf-field-icon_picker.php:544
msgid "Migrate icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:543
msgid "Microphone icon"
msgstr "Mikrofon-Icon"

#: includes/fields/class-acf-field-icon_picker.php:542
msgid "Menu icon"
msgstr "Menü-Icon"

#: includes/fields/class-acf-field-icon_picker.php:541
msgid "Megaphone icon"
msgstr "Megafon-Icon"

#: includes/fields/class-acf-field-icon_picker.php:540
msgid "Media video icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:539
msgid "Media text icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:538
msgid "Media spreadsheet icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:537
msgid "Media interactive icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:536
msgid "Media document icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:535
msgid "Media default icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:534
msgid "Media code icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:533
msgid "Media audio icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:532
msgid "Media archive icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:531
msgid "Marker icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:530
msgid "Lock icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:529
msgid "Location-alt icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:528
msgid "Location icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:527
msgid "List-view icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:526
msgid "Lightbulb icon"
msgstr "Glühbirnen-Icon"

#: includes/fields/class-acf-field-icon_picker.php:525
msgid "Leftright icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:524
msgid "Layout icon"
msgstr "Layout-Icon"

#: includes/fields/class-acf-field-icon_picker.php:523
msgid "Laptop icon"
msgstr "Laptop-Icon"

#: includes/fields/class-acf-field-icon_picker.php:522
msgid "Info icon"
msgstr "Info-Icon"

#: includes/fields/class-acf-field-icon_picker.php:521
msgid "Index-card icon"
msgstr "Karteikarte-Icon"

#: includes/fields/class-acf-field-icon_picker.php:520
msgid "Images-alt2 icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:519
msgid "Images-alt icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:518
msgid "Image rotate-right icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:517
msgid "Image rotate-left icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:516
msgid "Image rotate icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:515
msgid "Image flip-vertical icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:514
msgid "Image flip-horizontal icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:513
msgid "Image filter icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:512
msgid "Image crop icon"
msgstr "Bildauschnitt-Icon"

#: includes/fields/class-acf-field-icon_picker.php:511
msgid "Id-alt icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:510
msgid "Id icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:509
msgid "Hidden icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:508
msgid "Heart icon"
msgstr "Herz-Icon"

#: includes/fields/class-acf-field-icon_picker.php:507
msgid "Hammer icon"
msgstr "Hammer-Icon"

#: includes/fields/class-acf-field-icon_picker.php:506
msgid "Groups icon"
msgstr "Gruppen-Icon"

#: includes/fields/class-acf-field-icon_picker.php:505
msgid "Grid-view icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:504
msgid "Googleplus icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:503
msgid "Forms icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:502
msgid "Format video icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:501
msgid "Format status icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:500
msgid "Format quote icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:499
msgid "Format image icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:498
msgid "Format gallery icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:497
msgid "Format chat icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:496
msgid "Format audio icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:495
msgid "Format aside icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:494
msgid "Flag icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:493
msgid "Filter icon"
msgstr "Filter-Icon"

#: includes/fields/class-acf-field-icon_picker.php:492
msgid "Feedback icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:491
msgid "Facebook alt icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:490
msgid "Facebook icon"
msgstr "Facebook-Icon"

#: includes/fields/class-acf-field-icon_picker.php:489
msgid "External icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:488
msgid "Exerpt-view icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:487
msgid "Email alt icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:486
msgid "Email icon"
msgstr "E-Mail-Icon"

#: includes/fields/class-acf-field-icon_picker.php:485
msgid "Video icon"
msgstr "Video-Icon"

#: includes/fields/class-acf-field-icon_picker.php:484
msgid "Unlink icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:483
msgid "Underline icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:482
msgid "Ul icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:481
msgid "Textcolor icon"
msgstr "Textfarbe-Icon"

#: includes/fields/class-acf-field-icon_picker.php:480
msgid "Table icon"
msgstr "Tabelle-Icon"

#: includes/fields/class-acf-field-icon_picker.php:479
msgid "Strikethrough icon"
msgstr "Durchgestrichen-Icon"

#: includes/fields/class-acf-field-icon_picker.php:478
msgid "Spellcheck icon"
msgstr "Rechtschreibprüfung-Icon"

#: includes/fields/class-acf-field-icon_picker.php:477
msgid "Rtl icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:476
msgid "Removeformatting icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:475
msgid "Quote icon"
msgstr "Zitat-Icon"

#: includes/fields/class-acf-field-icon_picker.php:474
msgid "Paste word icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:473
msgid "Paste text icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:472
msgid "Paragraph icon"
msgstr "Absatz-Icon"

#: includes/fields/class-acf-field-icon_picker.php:471
msgid "Outdent icon"
msgstr "Ausrücken-Icon"

#: includes/fields/class-acf-field-icon_picker.php:470
msgid "Ol icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:469
msgid "Kitchensink icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:468
msgid "Justify icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:467
msgid "Italic icon"
msgstr "Kursiv-Icon"

#: includes/fields/class-acf-field-icon_picker.php:466
msgid "Insertmore icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:465
msgid "Indent icon"
msgstr "Einrücken-Icon"

#: includes/fields/class-acf-field-icon_picker.php:464
msgid "Help icon"
msgstr "Hilfe-Icon"

#: includes/fields/class-acf-field-icon_picker.php:463
msgid "Expand icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:462
msgid "Customchar icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:461
msgid "Contract icon"
msgstr "Vertrag-Icon"

#: includes/fields/class-acf-field-icon_picker.php:460
msgid "Code icon"
msgstr "Code-Icon"

#: includes/fields/class-acf-field-icon_picker.php:459
msgid "Break icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:458
msgid "Bold icon"
msgstr "Fett-Icon"

#: includes/fields/class-acf-field-icon_picker.php:457
msgid "alignright icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:456
msgid "alignleft icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:455
msgid "aligncenter icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:454
msgid "Edit icon"
msgstr "Bearbeiten-Icon"

#: includes/fields/class-acf-field-icon_picker.php:453
msgid "Download icon"
msgstr "Download-Icon"

#: includes/fields/class-acf-field-icon_picker.php:452
msgid "Dismiss icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:451
msgid "Desktop icon"
msgstr "Desktop-Icon"

#: includes/fields/class-acf-field-icon_picker.php:450
msgid "Dashboard icon"
msgstr "Dashboard-Icon"

#: includes/fields/class-acf-field-icon_picker.php:449
msgid "Controls volumeon icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:448
msgid "Controls volumeoff icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:447
msgid "Controls skipforward icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:446
msgid "Controls skipback icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:445
msgid "Controls repeat icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:444
msgid "Controls play icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:443
msgid "Controls pause icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:442
msgid "Controls forward icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:441
msgid "Controls back icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:440
msgid "Cloud icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:439
msgid "Clock icon"
msgstr "Uhr-Icon"

#: includes/fields/class-acf-field-icon_picker.php:438
msgid "Clipboard icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:437
msgid "Chart pie icon"
msgstr "Tortendiagramm-Icon"

#: includes/fields/class-acf-field-icon_picker.php:436
msgid "Chart line icon"
msgstr "Liniendiagramm-Icon"

#: includes/fields/class-acf-field-icon_picker.php:435
msgid "Chart bar icon"
msgstr "Balkendiagramm-Icon"

#: includes/fields/class-acf-field-icon_picker.php:434
msgid "Chart area icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:433
msgid "Category icon"
msgstr "Kategorie-Icon"

#: includes/fields/class-acf-field-icon_picker.php:432
msgid "Cart icon"
msgstr "Warenkorb-Icon"

#: includes/fields/class-acf-field-icon_picker.php:431
msgid "Carrot icon"
msgstr "Karotte-Icon"

#: includes/fields/class-acf-field-icon_picker.php:430
msgid "Camera icon"
msgstr "Kamera-Icon"

#: includes/fields/class-acf-field-icon_picker.php:429
msgid "Calendar alt icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:428
msgid "Calendar icon"
msgstr "Kalender-Icon"

#: includes/fields/class-acf-field-icon_picker.php:427
msgid "Businessman icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:426
msgid "Building icon"
msgstr "Gebäude-Icon"

#: includes/fields/class-acf-field-icon_picker.php:425
msgid "Book alt icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:424
msgid "Book icon"
msgstr "Buch-Icon"

#: includes/fields/class-acf-field-icon_picker.php:423
msgid "Backup icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:422
msgid "Awards icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:421
msgid "Art icon"
msgstr "Kunst-Icon"

#: includes/fields/class-acf-field-icon_picker.php:420
msgid "Arrow up-alt2 icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:419
msgid "Arrow up-alt icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:418
msgid "Arrow up icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:417
msgid "Arrow right-alt2 icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:416
msgid "Arrow right-alt icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:415
msgid "Arrow right icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:414
msgid "Arrow left-alt2 icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:413
msgid "Arrow left-alt icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:412
msgid "Arrow left icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:411
msgid "Arrow down-alt2 icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:410
msgid "Arrow down-alt icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:409
msgid "Arrow down icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:408
msgid "Archive icon"
msgstr "Archiv-Icon"

#: includes/fields/class-acf-field-icon_picker.php:407
msgid "Analytics icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:406
msgid "Align-right icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:405
msgid "Align-none icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:404
msgid "Align-left icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:403
msgid "Align-center icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:402
msgid "Album icon"
msgstr "Album-Icon"

#: includes/fields/class-acf-field-icon_picker.php:401
msgid "Users icon"
msgstr "Benutzer-Icon"

#: includes/fields/class-acf-field-icon_picker.php:400
msgid "Tools icon"
msgstr "Werkzeuge-Icon"

#: includes/fields/class-acf-field-icon_picker.php:399
msgid "Site icon"
msgstr "Website-Icon"

#: includes/fields/class-acf-field-icon_picker.php:398
msgid "Settings icon"
msgstr "Einstellungen-Icon"

#: includes/fields/class-acf-field-icon_picker.php:397
msgid "Post icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:396
msgid "Plugins icon"
msgstr "Plugins-Icon"

#: includes/fields/class-acf-field-icon_picker.php:395
msgid "Page icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:394
msgid "Network icon"
msgstr "Netzwerk-Icon"

#: includes/fields/class-acf-field-icon_picker.php:393
msgid "Multisite icon"
msgstr "Multisite-Icon"

#: includes/fields/class-acf-field-icon_picker.php:392
msgid "Media icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:391
msgid "Links icon"
msgstr "Links-Icon"

#: includes/fields/class-acf-field-icon_picker.php:390
msgid "Home icon"
msgstr "Home-Icon"

#: includes/fields/class-acf-field-icon_picker.php:389
msgid "Customizer icon"
msgstr "Customizer-Icon"

#: includes/fields/class-acf-field-icon_picker.php:388
msgid "Comments icon"
msgstr "Kommentare-Icon"

#: includes/fields/class-acf-field-icon_picker.php:387
msgid "Collapse icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:386
msgid "Appearance icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:385
msgid "Generic icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:321
msgid "Icon picker requires a value."
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:316
msgid "Icon picker requires an icon type."
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:285
msgid ""
"The available icons matching your search query have been updated in the icon "
"picker below."
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:284
msgid "No results found for that search term"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:266
msgid "Array"
msgstr "Array"

#: includes/fields/class-acf-field-icon_picker.php:265
msgid "String"
msgstr ""

#. translators: %s - link to documentation
#: includes/fields/class-acf-field-icon_picker.php:253
msgid "Specify the return format for the icon. %s"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:238
msgid "Select where content editors can choose the icon from."
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:211
msgid "The URL to the icon you'd like to use, or svg as Data URI"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:194
msgid "Browse Media Library"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:185
msgid "The currently selected image preview"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:176
msgid "Click to change the icon in the Media Library"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:142
msgid "Search icons..."
msgstr "Icons suchen …"

#: includes/fields/class-acf-field-icon_picker.php:53
msgid "Media Library"
msgstr "Mediathek"

#: includes/fields/class-acf-field-icon_picker.php:49
msgid "Dashicons"
msgstr "Dashicons"

#: includes/fields/class-acf-field-icon_picker.php:26
msgid ""
"An interactive UI for selecting an icon. Select from Dashicons, the media "
"library, or a standalone URL input."
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:23
msgid "Icon Picker"
msgstr ""

#: includes/class-acf-site-health.php:704
msgid "JSON Load Paths"
msgstr ""

#: includes/class-acf-site-health.php:698
msgid "JSON Save Paths"
msgstr ""

#: includes/class-acf-site-health.php:689
msgid "Registered ACF Forms"
msgstr ""

#: includes/class-acf-site-health.php:683
msgid "Shortcode Enabled"
msgstr "Shortcode aktiviert"

#: includes/class-acf-site-health.php:675
msgid "Field Settings Tabs Enabled"
msgstr ""

#: includes/class-acf-site-health.php:667
msgid "Field Type Modal Enabled"
msgstr ""

#: includes/class-acf-site-health.php:659
msgid "Admin UI Enabled"
msgstr ""

#: includes/class-acf-site-health.php:650
msgid "Block Preloading Enabled"
msgstr ""

#: includes/class-acf-site-health.php:638
msgid "Blocks Per ACF Block Version"
msgstr ""

#: includes/class-acf-site-health.php:633
msgid "Blocks Per API Version"
msgstr ""

#: includes/class-acf-site-health.php:606
msgid "Registered ACF Blocks"
msgstr ""

#: includes/class-acf-site-health.php:600
msgid "Light"
msgstr ""

#: includes/class-acf-site-health.php:600
msgid "Standard"
msgstr "Standard"

#: includes/class-acf-site-health.php:599
msgid "REST API Format"
msgstr "REST-API-Format"

#: includes/class-acf-site-health.php:591
msgid "Registered Options Pages (PHP)"
msgstr ""

#: includes/class-acf-site-health.php:577
msgid "Registered Options Pages (JSON)"
msgstr ""

#: includes/class-acf-site-health.php:572
msgid "Registered Options Pages (UI)"
msgstr ""

#: includes/class-acf-site-health.php:542
msgid "Options Pages UI Enabled"
msgstr ""

#: includes/class-acf-site-health.php:534
msgid "Registered Taxonomies (JSON)"
msgstr ""

#: includes/class-acf-site-health.php:522
msgid "Registered Taxonomies (UI)"
msgstr ""

#: includes/class-acf-site-health.php:510
msgid "Registered Post Types (JSON)"
msgstr ""

#: includes/class-acf-site-health.php:498
msgid "Registered Post Types (UI)"
msgstr ""

#: includes/class-acf-site-health.php:485
msgid "Post Types and Taxonomies Enabled"
msgstr ""

#: includes/class-acf-site-health.php:478
msgid "Number of Third Party Fields by Field Type"
msgstr ""

#: includes/class-acf-site-health.php:473
msgid "Number of Fields by Field Type"
msgstr ""

#: includes/class-acf-site-health.php:440
msgid "Field Groups Enabled for GraphQL"
msgstr ""

#: includes/class-acf-site-health.php:427
msgid "Field Groups Enabled for REST API"
msgstr ""

#: includes/class-acf-site-health.php:415
msgid "Registered Field Groups (JSON)"
msgstr ""

#: includes/class-acf-site-health.php:403
msgid "Registered Field Groups (PHP)"
msgstr ""

#: includes/class-acf-site-health.php:391
msgid "Registered Field Groups (UI)"
msgstr ""

#: includes/class-acf-site-health.php:379
msgid "Active Plugins"
msgstr "Aktive Plugins"

#: includes/class-acf-site-health.php:353
msgid "Parent Theme"
msgstr "Übergeordnetes Theme"

#: includes/class-acf-site-health.php:342
msgid "Active Theme"
msgstr "Aktives Theme"

#: includes/class-acf-site-health.php:333
msgid "Is Multisite"
msgstr ""

#: includes/class-acf-site-health.php:328
msgid "MySQL Version"
msgstr "MySQL-Version"

#: includes/class-acf-site-health.php:323
msgid "WordPress Version"
msgstr "WordPress-Version"

#: includes/class-acf-site-health.php:316
msgid "Subscription Expiry Date"
msgstr ""

#: includes/class-acf-site-health.php:308
msgid "License Status"
msgstr "Lizenzstatus"

#: includes/class-acf-site-health.php:303
msgid "License Type"
msgstr "Lizenz-Typ"

#: includes/class-acf-site-health.php:298
msgid "Licensed URL"
msgstr "Lizensierte URL"

#: includes/class-acf-site-health.php:292
msgid "License Activated"
msgstr "Lizenz aktiviert"

#: includes/class-acf-site-health.php:286
msgid "Free"
msgstr "Kostenlos"

#: includes/class-acf-site-health.php:285
msgid "Plugin Type"
msgstr "Plugin-Typ"

#: includes/class-acf-site-health.php:280
msgid "Plugin Version"
msgstr "Plugin-Version"

#: includes/class-acf-site-health.php:251
msgid ""
"This section contains debug information about your ACF configuration which "
"can be useful to provide to support."
msgstr ""

#: includes/assets.php:373 assets/build/js/acf-input.js:11311
#: assets/build/js/acf-input.js:12393
msgid "An ACF Block on this page requires attention before you can save."
msgstr ""

#. translators: %s - The clear log button opening HTML tag. %s - The closing
#. HTML tag.
#: includes/admin/views/escaped-html-notice.php:63
msgid ""
"This data is logged as we detect values that have been changed during "
"output. %1$sClear log and dismiss%2$s after escaping the values in your "
"code. The notice will reappear if we detect changed values again."
msgstr ""

#: includes/admin/views/escaped-html-notice.php:25
msgid "Dismiss permanently"
msgstr ""

#: includes/admin/views/acf-field-group/field.php:220
msgid "Instructions for content editors. Shown when submitting data."
msgstr ""

#: includes/admin/post-types/admin-field-group.php:143
#: assets/build/js/acf-input.js:1460 assets/build/js/acf-input.js:1558
msgid "Has no term selected"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:142
#: assets/build/js/acf-input.js:1437 assets/build/js/acf-input.js:1534
msgid "Has any term selected"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:141
#: assets/build/js/acf-input.js:1412 assets/build/js/acf-input.js:1507
msgid "Terms do not contain"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:140
#: assets/build/js/acf-input.js:1387 assets/build/js/acf-input.js:1481
msgid "Terms contain"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:139
#: assets/build/js/acf-input.js:1368 assets/build/js/acf-input.js:1461
msgid "Term is not equal to"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:138
#: assets/build/js/acf-input.js:1349 assets/build/js/acf-input.js:1441
msgid "Term is equal to"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:137
#: assets/build/js/acf-input.js:1052 assets/build/js/acf-input.js:1116
msgid "Has no user selected"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:136
#: assets/build/js/acf-input.js:1029 assets/build/js/acf-input.js:1092
msgid "Has any user selected"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:135
#: assets/build/js/acf-input.js:1003 assets/build/js/acf-input.js:1064
msgid "Users do not contain"
msgstr "Benutzer enthalten nicht"

#: includes/admin/post-types/admin-field-group.php:134
#: assets/build/js/acf-input.js:976 assets/build/js/acf-input.js:1035
msgid "Users contain"
msgstr "Benutzer enthalten"

#: includes/admin/post-types/admin-field-group.php:133
#: assets/build/js/acf-input.js:957 assets/build/js/acf-input.js:1015
msgid "User is not equal to"
msgstr "Benutzer ist ungleich"

#: includes/admin/post-types/admin-field-group.php:132
#: assets/build/js/acf-input.js:938 assets/build/js/acf-input.js:995
msgid "User is equal to"
msgstr "Benutzer ist gleich"

#: includes/admin/post-types/admin-field-group.php:131
#: assets/build/js/acf-input.js:915 assets/build/js/acf-input.js:971
msgid "Has no page selected"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:130
#: assets/build/js/acf-input.js:892 assets/build/js/acf-input.js:947
msgid "Has any page selected"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:129
#: assets/build/js/acf-input.js:865 assets/build/js/acf-input.js:918
msgid "Pages do not contain"
msgstr "Seiten enthalten nicht"

#: includes/admin/post-types/admin-field-group.php:128
#: assets/build/js/acf-input.js:838 assets/build/js/acf-input.js:889
msgid "Pages contain"
msgstr "Seiten enthalten"

#: includes/admin/post-types/admin-field-group.php:127
#: assets/build/js/acf-input.js:819 assets/build/js/acf-input.js:869
msgid "Page is not equal to"
msgstr "Seite ist ungleich"

#: includes/admin/post-types/admin-field-group.php:126
#: assets/build/js/acf-input.js:800 assets/build/js/acf-input.js:849
msgid "Page is equal to"
msgstr "Seite ist gleich"

#: includes/admin/post-types/admin-field-group.php:125
#: assets/build/js/acf-input.js:1188 assets/build/js/acf-input.js:1259
msgid "Has no relationship selected"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:124
#: assets/build/js/acf-input.js:1165 assets/build/js/acf-input.js:1235
msgid "Has any relationship selected"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:123
#: assets/build/js/acf-input.js:1326 assets/build/js/acf-input.js:1415
msgid "Has no post selected"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:122
#: assets/build/js/acf-input.js:1303 assets/build/js/acf-input.js:1389
msgid "Has any post selected"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:121
#: assets/build/js/acf-input.js:1276 assets/build/js/acf-input.js:1358
msgid "Posts do not contain"
msgstr "Beiträge enthalten nicht"

#: includes/admin/post-types/admin-field-group.php:120
#: assets/build/js/acf-input.js:1249 assets/build/js/acf-input.js:1327
msgid "Posts contain"
msgstr "Beiträge enthalten"

#: includes/admin/post-types/admin-field-group.php:119
#: assets/build/js/acf-input.js:1230 assets/build/js/acf-input.js:1305
msgid "Post is not equal to"
msgstr "Beitrag ist ungleich"

#: includes/admin/post-types/admin-field-group.php:118
#: assets/build/js/acf-input.js:1211 assets/build/js/acf-input.js:1283
msgid "Post is equal to"
msgstr "Beitrag ist gleich"

#: includes/admin/post-types/admin-field-group.php:117
#: assets/build/js/acf-input.js:1139 assets/build/js/acf-input.js:1207
msgid "Relationships do not contain"
msgstr "Beziehungen enthalten nicht"

#: includes/admin/post-types/admin-field-group.php:116
#: assets/build/js/acf-input.js:1113 assets/build/js/acf-input.js:1180
msgid "Relationships contain"
msgstr "Beziehungen enthalten"

#: includes/admin/post-types/admin-field-group.php:115
#: assets/build/js/acf-input.js:1094 assets/build/js/acf-input.js:1160
msgid "Relationship is not equal to"
msgstr "Beziehung ist ungleich"

#: includes/admin/post-types/admin-field-group.php:114
#: assets/build/js/acf-input.js:1075 assets/build/js/acf-input.js:1140
msgid "Relationship is equal to"
msgstr "Beziehung ist gleich"

#: includes/Blocks/Bindings.php:35
msgctxt "The core ACF block binding source name for fields on the current page"
msgid "ACF Fields"
msgstr "ACF-Felder"

#: includes/admin/views/browse-fields-modal.php:14
msgid "ACF PRO Feature"
msgstr "ACF-PRO-Funktion"

#: includes/admin/views/browse-fields-modal.php:10
msgid "Renew PRO to Unlock"
msgstr "PRO-Lizenz erneuern zum Freischalten"

#: includes/admin/views/browse-fields-modal.php:8
msgid "Renew PRO License"
msgstr "PRO-Lizenz erneuern"

#: includes/admin/views/acf-field-group/field.php:41
msgid "PRO fields cannot be edited without an active license."
msgstr "PRO-Felder können ohne aktive Lizenz nicht bearbeitet werden."

#: includes/admin/admin-internal-post-type-list.php:232
msgid ""
"Please activate your ACF PRO license to edit field groups assigned to an ACF "
"Block."
msgstr ""
"Bitte aktiviere deine ACF-PRO-Lizenz, um Feldgruppen bearbeiten zu können, "
"die einem ACF-Block zugewiesen wurden."

#: includes/admin/admin-internal-post-type-list.php:231
msgid "Please activate your ACF PRO license to edit this options page."
msgstr ""
"Bitte aktiviere deine ACF-PRO-Lizenz, um diese Optionsseite zu bearbeiten."

#: includes/api/api-template.php:381 includes/api/api-template.php:435
msgid ""
"Returning escaped HTML values is only possible when format_value is also "
"true. The field values have not been returned for security."
msgstr ""

#: includes/api/api-template.php:46 includes/api/api-template.php:247
#: includes/api/api-template.php:939
msgid ""
"Returning an escaped HTML value is only possible when format_value is also "
"true. The field value has not been returned for security."
msgstr ""

#. translators: %1$s - name of the ACF plugin. %2$s - Link to documentation.
#. %3$s - Link to show more details about the error
#: includes/admin/views/escaped-html-notice.php:32
msgid ""
"%1$s ACF now automatically escapes unsafe HTML when rendered by "
"<code>the_field</code> or the ACF shortcode. We've detected the output of "
"some of your fields has been modified by this change, but this may not be a "
"breaking change. %2$s."
msgstr ""

#: includes/admin/views/escaped-html-notice.php:27
msgid "Please contact your site administrator or developer for more details."
msgstr ""
"Bitte kontaktiere den Administrator oder Entwickler deiner Website für mehr "
"Details."

#: includes/admin/views/escaped-html-notice.php:5
msgid "Learn&nbsp;more"
msgstr ""

#: includes/admin/admin.php:63
msgid "Hide&nbsp;details"
msgstr "Details&nbsp;verbergen"

#: includes/admin/admin.php:62 includes/admin/views/escaped-html-notice.php:24
msgid "Show&nbsp;details"
msgstr "Details&nbsp;anzeigen"

#. translators: %1$s - The selector used  %2$s The field name  3%$s The parent
#. function name
#: includes/admin/views/escaped-html-notice.php:49
msgid "%1$s (%2$s) - rendered via %3$s"
msgstr "%1$s (%2$s) - gerendert via %3$s"

#: includes/admin/views/global/navigation.php:226
msgid "Renew ACF PRO License"
msgstr "ACF-PRO-Lizenz erneuern"

#: includes/admin/views/acf-field-group/pro-features.php:17
msgid "Renew License"
msgstr "Lizenz erneuern"

#: includes/admin/views/acf-field-group/pro-features.php:14
msgid "Manage License"
msgstr "Lizenz verwalten"

#: includes/admin/views/acf-field-group/options.php:102
msgid "'High' position not supported in the Block Editor"
msgstr "Die „Hoch“-Position wird im Block-Editor nicht unterstützt"

#: includes/admin/views/options-page-preview.php:30
msgid "Upgrade to ACF PRO"
msgstr "Upgrade auf ACF PRO"

#. translators: %s URL to ACF options pages documentation
#: includes/admin/views/options-page-preview.php:7
msgid ""
"ACF <a href=\"%s\" target=\"_blank\">options pages</a> are custom admin "
"pages for managing global settings via fields. You can create multiple pages "
"and sub-pages."
msgstr ""

#: includes/admin/views/global/header.php:35
msgid "Add Options Page"
msgstr "Optionen-Seite hinzufügen"

#: includes/admin/views/acf-post-type/advanced-settings.php:708
msgid "In the editor used as the placeholder of the title."
msgstr "Wird im Editor als Platzhalter für den Titel verwendet."

#: includes/admin/views/acf-post-type/advanced-settings.php:707
msgid "Title Placeholder"
msgstr "Titel-Platzhalter"

#: includes/admin/views/global/navigation.php:97
msgid "4 Months Free"
msgstr "4 Monate kostenlos"

#. translators: %s - A singular label for a post type or taxonomy.
#: includes/admin/views/global/form-top.php:59
msgid "(Duplicated from %s)"
msgstr "(Duplikat von %s)"

#: includes/admin/tools/class-acf-admin-tool-export.php:289
msgid "Select Options Pages"
msgstr "Options-Seite auswählen"

#: includes/admin/post-types/admin-taxonomy.php:107
msgid "Duplicate taxonomy"
msgstr "Taxonomie duplizieren"

#: includes/admin/post-types/admin-post-type.php:106
#: includes/admin/post-types/admin-taxonomy.php:106
msgid "Create taxonomy"
msgstr "Taxonomie erstellen"

#: includes/admin/post-types/admin-post-type.php:105
msgid "Duplicate post type"
msgstr "Inhalttyp duplizieren"

#: includes/admin/post-types/admin-post-type.php:104
#: includes/admin/post-types/admin-taxonomy.php:108
msgid "Create post type"
msgstr "Inhaltstyp erstellen"

#: includes/admin/post-types/admin-post-type.php:103
#: includes/admin/post-types/admin-taxonomy.php:105
msgid "Link field groups"
msgstr "Feldgruppen verlinken"

#: includes/admin/post-types/admin-post-type.php:102
#: includes/admin/post-types/admin-taxonomy.php:104
msgid "Add fields"
msgstr "Felder hinzufügen"

#: includes/admin/post-types/admin-field-group.php:147
#: assets/build/js/acf-field-group.js:2803
#: assets/build/js/acf-field-group.js:3298
msgid "This Field"
msgstr "Dieses Feld"

#: includes/admin/admin.php:352
msgid "ACF PRO"
msgstr "ACF PRO"

#: includes/admin/admin.php:350
msgid "Feedback"
msgstr "Feedback"

#: includes/admin/admin.php:348
msgid "Support"
msgstr "Hilfe"

#. translators: This text is prepended by a link to ACF's website, and appended
#. by a link to WP Engine's website.
#: includes/admin/admin.php:323
msgid "is developed and maintained by"
msgstr "wird entwickelt und gewartet von"

#. translators: %s - either "post type" or "taxonomy"
#: includes/admin/admin-internal-post-type.php:313
msgid "Add this %s to the location rules of the selected field groups."
msgstr "Füge %s zu den Positions-Optionen der ausgewählten Feldgruppen hinzu."

#. translators: %s the URL to ACF's bidirectional relationship documentation
#: includes/acf-bidirectional-functions.php:272
msgid ""
"Enabling the bidirectional setting allows you to update a value in the "
"target fields for each value selected for this field, adding or removing the "
"Post ID, Taxonomy ID or User ID of the item being updated. For more "
"information, please read the <a href=\"%s\" target=\"_blank\">documentation</"
"a>."
msgstr ""

#: includes/acf-bidirectional-functions.php:248
msgid ""
"Select field(s) to store the reference back to the item being updated. You "
"may select this field. Target fields must be compatible with where this "
"field is being displayed. For example, if this field is displayed on a "
"Taxonomy, your target field should be of type Taxonomy"
msgstr ""

#: includes/acf-bidirectional-functions.php:247
msgid "Target Field"
msgstr "Ziel-Feld"

#: includes/acf-bidirectional-functions.php:221
msgid "Update a field on the selected values, referencing back to this ID"
msgstr ""
"Ein Feld mit den ausgewählten Werten aktualisieren, auf diese ID "
"zurückverweisend"

#: includes/acf-bidirectional-functions.php:220
msgid "Bidirectional"
msgstr "Bidirektional"

#. translators: %s A field type name, such as "Relationship"
#: includes/acf-bidirectional-functions.php:193
msgid "%s Field"
msgstr "%s Feld"

#: includes/fields/class-acf-field-page_link.php:487
#: includes/fields/class-acf-field-post_object.php:400
#: includes/fields/class-acf-field-select.php:380
#: includes/fields/class-acf-field-user.php:111
msgid "Select Multiple"
msgstr "Mehrere auswählen"

#: includes/admin/views/global/navigation.php:238
msgid "WP Engine logo"
msgstr "Logo von WP Engine"

#: includes/admin/views/acf-taxonomy/basic-settings.php:58
msgid "Lower case letters, underscores and dashes only, Max 32 characters."
msgstr ""
"Erlaubt sind Kleinbuchstaben, Unterstriche (_) und Striche (-), maximal 32 "
"Zeichen."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1136
msgid "The capability name for assigning terms of this taxonomy."
msgstr "Der Name der Berechtigung, um Begriffe dieser Taxonomie zuzuordnen."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1135
msgid "Assign Terms Capability"
msgstr "Begriffs-Berechtigung zuordnen"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1119
msgid "The capability name for deleting terms of this taxonomy."
msgstr "Der Name der Berechtigung, um Begriffe dieser Taxonomie zu löschen."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1118
msgid "Delete Terms Capability"
msgstr "Begriffs-Berechtigung löschen"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1102
msgid "The capability name for editing terms of this taxonomy."
msgstr "Der Name der Berechtigung, um Begriffe dieser Taxonomie zu bearbeiten."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1101
msgid "Edit Terms Capability"
msgstr "Begriffs-Berechtigung bearbeiten"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1085
msgid "The capability name for managing terms of this taxonomy."
msgstr "Der Name der Berechtigung, um Begriffe dieser Taxonomie zu verwalten."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1084
msgid "Manage Terms Capability"
msgstr "Begriffs-Berechtigung verwalten"

#: includes/admin/views/acf-post-type/advanced-settings.php:914
msgid ""
"Sets whether posts should be excluded from search results and taxonomy "
"archive pages."
msgstr ""
"Legt fest, ob Beiträge von den Suchergebnissen und Taxonomie-Archivseiten "
"ausgeschlossen werden sollen."

#: includes/admin/views/acf-field-group/pro-features.php:78
msgid "More Tools from WP Engine"
msgstr "Mehr Werkzeuge von WP Engine"

#. translators: %s - WP Engine logo
#: includes/admin/views/acf-field-group/pro-features.php:73
msgid "Built for those that build with WordPress, by the team at %s"
msgstr "Gebaut für alle, die mit WordPress bauen, vom Team bei %s"

#: includes/admin/views/acf-field-group/pro-features.php:6
msgid "View Pricing & Upgrade"
msgstr "Preise anzeigen und Upgrade installieren"

#: includes/admin/views/acf-field-group/pro-features.php:3
#: includes/admin/views/options-page-preview.php:29
#: includes/fields/class-acf-field-icon_picker.php:248
msgid "Learn More"
msgstr "Mehr erfahren"

#: includes/admin/views/acf-field-group/pro-features.php:28
msgid ""
"Speed up your workflow and develop better websites with features like ACF "
"Blocks and Options Pages, and sophisticated field types like Repeater, "
"Flexible Content, Clone, and Gallery."
msgstr ""

#: includes/admin/views/acf-field-group/pro-features.php:2
msgid "Unlock Advanced Features and Build Even More with ACF PRO"
msgstr "Schalte erweiterte Funktionen frei und erschaffe noch mehr mit ACF PRO"

#. translators: %s - singular label of post type/taxonomy, i.e. "Movie"/"Genre"
#: includes/admin/views/global/form-top.php:19
msgid "%s fields"
msgstr "%s Felder"

#: includes/admin/post-types/admin-taxonomies.php:267
msgid "No terms"
msgstr "Keine Begriffe"

#: includes/admin/post-types/admin-taxonomies.php:240
msgid "No post types"
msgstr "Keine Inhaltstypen"

#: includes/admin/post-types/admin-post-types.php:264
msgid "No posts"
msgstr "Keine Beiträge"

#: includes/admin/post-types/admin-post-types.php:238
msgid "No taxonomies"
msgstr "Keine Taxonomien"

#: includes/admin/post-types/admin-post-types.php:183
#: includes/admin/post-types/admin-taxonomies.php:182
msgid "No field groups"
msgstr "Keine Feldgruppen"

#: includes/admin/post-types/admin-field-groups.php:255
msgid "No fields"
msgstr "Keine Felder"

#: includes/admin/post-types/admin-field-groups.php:128
#: includes/admin/post-types/admin-post-types.php:147
#: includes/admin/post-types/admin-taxonomies.php:146
msgid "No description"
msgstr "Keine Beschreibung"

#: includes/fields/class-acf-field-page_link.php:454
#: includes/fields/class-acf-field-post_object.php:363
#: includes/fields/class-acf-field-relationship.php:562
msgid "Any post status"
msgstr "Jeder Beitragsstatus"

#: includes/post-types/class-acf-taxonomy.php:288
msgid ""
"This taxonomy key is already in use by another taxonomy registered outside "
"of ACF and cannot be used."
msgstr ""
"Dieser Taxonomie-Schlüssel stammt von einer anderen Taxonomie außerhalb von "
"ACF und kann nicht verwendet werden."

#: includes/post-types/class-acf-taxonomy.php:284
msgid ""
"This taxonomy key is already in use by another taxonomy in ACF and cannot be "
"used."
msgstr ""
"Dieser Taxonomie-Schlüssel stammt von einer anderen Taxonomie in ACF und "
"kann nicht verwendet werden."

#: includes/post-types/class-acf-taxonomy.php:256
msgid ""
"The taxonomy key must only contain lower case alphanumeric characters, "
"underscores or dashes."
msgstr ""
"Der Taxonomie-Schlüssel darf nur Kleinbuchstaben, Unterstriche und "
"Trennstriche enthalten."

#: includes/post-types/class-acf-taxonomy.php:251
msgid "The taxonomy key must be under 32 characters."
msgstr "Der Taxonomie-Schlüssel muss kürzer als 32 Zeichen sein."

#: includes/post-types/class-acf-taxonomy.php:99
msgid "No Taxonomies found in Trash"
msgstr "Es wurden keine Taxonomien im Papierkorb gefunden"

#: includes/post-types/class-acf-taxonomy.php:98
msgid "No Taxonomies found"
msgstr "Es wurden keine Taxonomien gefunden"

#: includes/post-types/class-acf-taxonomy.php:97
msgid "Search Taxonomies"
msgstr "Suche Taxonomien"

#: includes/post-types/class-acf-taxonomy.php:96
msgid "View Taxonomy"
msgstr "Taxonomie anzeigen"

#: includes/post-types/class-acf-taxonomy.php:95
msgid "New Taxonomy"
msgstr "Neue Taxonomie"

#: includes/post-types/class-acf-taxonomy.php:94
msgid "Edit Taxonomy"
msgstr "Taxonomie bearbeiten"

#: includes/post-types/class-acf-taxonomy.php:93
msgid "Add New Taxonomy"
msgstr "Neue Taxonomie hinzufügen"

#: includes/post-types/class-acf-post-type.php:100
msgid "No Post Types found in Trash"
msgstr "Es wurden keine Inhaltstypen im Papierkorb gefunden"

#: includes/post-types/class-acf-post-type.php:99
msgid "No Post Types found"
msgstr "Es wurden keine Inhaltstypen gefunden"

#: includes/post-types/class-acf-post-type.php:98
msgid "Search Post Types"
msgstr "Inhaltstypen suchen"

#: includes/post-types/class-acf-post-type.php:97
msgid "View Post Type"
msgstr "Inhaltstyp anzeigen"

#: includes/post-types/class-acf-post-type.php:96
msgid "New Post Type"
msgstr "Neuer Inhaltstyp"

#: includes/post-types/class-acf-post-type.php:95
msgid "Edit Post Type"
msgstr "Inhaltstyp bearbeiten"

#: includes/post-types/class-acf-post-type.php:94
msgid "Add New Post Type"
msgstr "Neuen Inhaltstyp hinzufügen"

#: includes/post-types/class-acf-post-type.php:366
msgid ""
"This post type key is already in use by another post type registered outside "
"of ACF and cannot be used."
msgstr ""
"Dieser Inhaltstyp-Schlüssel stammt von einem anderen Inhaltstyp außerhalb "
"von ACF und kann nicht verwendet werden."

#: includes/post-types/class-acf-post-type.php:361
msgid ""
"This post type key is already in use by another post type in ACF and cannot "
"be used."
msgstr ""
"Dieser Inhaltstyp-Schlüssel stammt von einem anderen Inhaltstyp in ACF und "
"kann nicht verwendet werden."

#. translators: %s a link to WordPress.org's Reserved Terms page
#: includes/post-types/class-acf-post-type.php:339
#: includes/post-types/class-acf-taxonomy.php:262
msgid ""
"This field must not be a WordPress <a href=\"%s\" target=\"_blank\">reserved "
"term</a>."
msgstr ""
"Dieses Feld darf kein von WordPress <a href=\"%s\" "
"target=\"_blank\">reservierter Begriff</a> sein."

#: includes/post-types/class-acf-post-type.php:333
msgid ""
"The post type key must only contain lower case alphanumeric characters, "
"underscores or dashes."
msgstr ""
"Der Inhaltstyp-Schlüssel darf nur Kleinbuchstaben, Unterstriche und "
"Trennstriche enthalten."

#: includes/post-types/class-acf-post-type.php:328
msgid "The post type key must be under 20 characters."
msgstr "Der Inhaltstyp-Schlüssel muss kürzer als 20 Zeichen sein."

#: includes/fields/class-acf-field-wysiwyg.php:24
msgid "We do not recommend using this field in ACF Blocks."
msgstr "Es wird nicht empfohlen, dieses Feld in ACF-Blöcken zu verwenden."

#: includes/fields/class-acf-field-wysiwyg.php:24
msgid ""
"Displays the WordPress WYSIWYG editor as seen in Posts and Pages allowing "
"for a rich text-editing experience that also allows for multimedia content."
msgstr ""
"Zeigt den WordPress-WYSIWYG-Editor an, wie er in Beiträgen und Seiten zu "
"sehen ist, und ermöglicht so eine umfangreiche Textbearbeitung, die auch "
"Multimedia-Inhalte zulässt."

#: includes/fields/class-acf-field-wysiwyg.php:22
msgid "WYSIWYG Editor"
msgstr "WYSIWYG-Editor"

#: includes/fields/class-acf-field-user.php:17
msgid ""
"Allows the selection of one or more users which can be used to create "
"relationships between data objects."
msgstr ""
"Ermöglicht die Auswahl von einem oder mehreren Benutzern, die zur Erstellung "
"von Beziehungen zwischen Datenobjekten verwendet werden können."

#: includes/fields/class-acf-field-url.php:20
msgid "A text input specifically designed for storing web addresses."
msgstr ""
"Eine Texteingabe, die speziell für die Speicherung von Webadressen "
"entwickelt wurde."

#: includes/fields/class-acf-field-icon_picker.php:56
#: includes/fields/class-acf-field-url.php:19
msgid "URL"
msgstr "URL"

#: includes/fields/class-acf-field-true_false.php:24
msgid ""
"A toggle that allows you to pick a value of 1 or 0 (on or off, true or "
"false, etc). Can be presented as a stylized switch or checkbox."
msgstr ""
"Ein Schalter, mit dem ein Wert von 1 oder 0 (ein oder aus, wahr oder falsch "
"usw.) auswählt werden kann. Kann als stilisierter Schalter oder "
"Kontrollkästchen dargestellt werden."

#: includes/fields/class-acf-field-time_picker.php:24
msgid ""
"An interactive UI for picking a time. The time format can be customized "
"using the field settings."
msgstr ""
"Eine interaktive Benutzeroberfläche zum Auswählen einer Zeit. Das Zeitformat "
"kann in den Feldeinstellungen angepasst werden."

#: includes/fields/class-acf-field-textarea.php:23
msgid "A basic textarea input for storing paragraphs of text."
msgstr ""
"Eine einfache Eingabe in Form eines Textbereiches zum Speichern von "
"Textabsätzen."

#: includes/fields/class-acf-field-text.php:23
msgid "A basic text input, useful for storing single string values."
msgstr ""
"Eine einfache Texteingabe, nützlich für die Speicherung einzelner "
"Zeichenfolgen."

#: includes/fields/class-acf-field-taxonomy.php:22
msgid ""
"Allows the selection of one or more taxonomy terms based on the criteria and "
"options specified in the fields settings."
msgstr ""
"Ermöglicht die Auswahl von einem oder mehreren Taxonomiebegriffen auf der "
"Grundlage der in den Feldeinstellungen angegebenen Kriterien und Optionen."

#: includes/fields/class-acf-field-tab.php:25
msgid ""
"Allows you to group fields into tabbed sections in the edit screen. Useful "
"for keeping fields organized and structured."
msgstr ""

#: includes/fields/class-acf-field-select.php:24
msgid "A dropdown list with a selection of choices that you specify."
msgstr ""
"Eine Dropdown-Liste mit einer von dir angegebenen Auswahl an "
"Wahlmöglichkeiten."

#: includes/fields/class-acf-field-relationship.php:19
msgid ""
"A dual-column interface to select one or more posts, pages, or custom post "
"type items to create a relationship with the item that you're currently "
"editing. Includes options to search and filter."
msgstr ""

#: includes/fields/class-acf-field-range.php:23
msgid ""
"An input for selecting a numerical value within a specified range using a "
"range slider element."
msgstr ""
"Ein Schieberegler-Eingabefeld für numerische Zahlenwerte in einem "
"festgelegten Bereich."

#: includes/fields/class-acf-field-radio.php:24
msgid ""
"A group of radio button inputs that allows the user to make a single "
"selection from values that you specify."
msgstr ""
"Eine Gruppe von Radiobuttons, die es dem Benutzer ermöglichen, eine einzelne "
"Auswahl aus von dir angegebenen Werten zu treffen."

#: includes/fields/class-acf-field-post_object.php:17
msgid ""
"An interactive and customizable UI for picking one or many posts, pages or "
"post type items with the option to search. "
msgstr ""
"Eine interaktive und anpassbare Benutzeroberfläche zur Auswahl einer "
"beliebigen Anzahl von Beiträgen, Seiten oder Inhaltstypen-Elemente mit der "
"Option zum Suchen. "

#: includes/fields/class-acf-field-password.php:23
msgid "An input for providing a password using a masked field."
msgstr "Ein Passwort-Feld, das die Eingabe maskiert."

#: includes/fields/class-acf-field-page_link.php:446
#: includes/fields/class-acf-field-post_object.php:355
#: includes/fields/class-acf-field-relationship.php:554
msgid "Filter by Post Status"
msgstr "Nach Beitragsstatus filtern"

#: includes/fields/class-acf-field-page_link.php:24
msgid ""
"An interactive dropdown to select one or more posts, pages, custom post type "
"items or archive URLs, with the option to search."
msgstr ""
"Ein interaktives Drop-down-Menü zur Auswahl von einem oder mehreren "
"Beiträgen, Seiten, individuellen Inhaltstypen oder Archiv-URLs mit der "
"Option zur Suche."

#: includes/fields/class-acf-field-oembed.php:24
msgid ""
"An interactive component for embedding videos, images, tweets, audio and "
"other content by making use of the native WordPress oEmbed functionality."
msgstr ""
"Ein interaktives Feld zum Einbetten von Videos, Bildern, Tweets, Audio und "
"anderen Inhalten unter Verwendung der nativen WordPress-oEmbed-"
"Funktionalität."

#: includes/fields/class-acf-field-number.php:23
msgid "An input limited to numerical values."
msgstr "Eine auf numerische Werte beschränkte Eingabe."

#: includes/fields/class-acf-field-message.php:25
msgid ""
"Used to display a message to editors alongside other fields. Useful for "
"providing additional context or instructions around your fields."
msgstr ""

#: includes/fields/class-acf-field-link.php:24
msgid ""
"Allows you to specify a link and its properties such as title and target "
"using the WordPress native link picker."
msgstr ""

#: includes/fields/class-acf-field-image.php:24
msgid "Uses the native WordPress media picker to upload, or choose images."
msgstr ""
"Nutzt den nativen WordPress-Mediendialog zum Hochladen oder Auswählen von "
"Bildern."

#: includes/fields/class-acf-field-group.php:24
msgid ""
"Provides a way to structure fields into groups to better organize the data "
"and the edit screen."
msgstr ""
"Bietet die Möglichkeit zur Gruppierung von Feldern, um Daten und den "
"Bearbeiten-Bildschirm besser zu strukturieren."

#: includes/fields/class-acf-field-google-map.php:24
msgid ""
"An interactive UI for selecting a location using Google Maps. Requires a "
"Google Maps API key and additional configuration to display correctly."
msgstr ""
"Eine interaktive Benutzeroberfläche zur Auswahl eines Standortes unter "
"Verwendung von Google Maps. Benötigt einen Google-Maps-API-Schlüssel und "
"eine zusätzliche Konfiguration für eine korrekte Anzeige."

#: includes/fields/class-acf-field-file.php:24
msgid "Uses the native WordPress media picker to upload, or choose files."
msgstr ""
"Nutzt den nativen WordPress-Mediendialog zum Hochladen oder Auswählen von "
"Dateien."

#: includes/fields/class-acf-field-email.php:23
msgid "A text input specifically designed for storing email addresses."
msgstr ""
"Ein Texteingabefeld, das speziell für die Speicherung von E-Mail-Adressen "
"entwickelt wurde."

#: includes/fields/class-acf-field-date_time_picker.php:24
msgid ""
"An interactive UI for picking a date and time. The date return format can be "
"customized using the field settings."
msgstr ""
"Eine interaktive Benutzeroberfläche zur Auswahl von Datum und Uhrzeit. Das "
"zurückgegebene Datumsformat kann in den Feldeinstellungen angepasst werden."

#: includes/fields/class-acf-field-date_picker.php:24
msgid ""
"An interactive UI for picking a date. The date return format can be "
"customized using the field settings."
msgstr ""
"Eine interaktive Benutzeroberfläche zur Auswahl eines Datums. Das "
"zurückgegebene Datumsformat kann in den Feldeinstellungen angepasst werden."

#: includes/fields/class-acf-field-color_picker.php:24
msgid "An interactive UI for selecting a color, or specifying a Hex value."
msgstr ""
"Eine interaktive Benutzeroberfläche zur Auswahl einer Farbe, oder zur "
"Eingabe eines Hex-Wertes."

#: includes/fields/class-acf-field-checkbox.php:24
msgid ""
"A group of checkbox inputs that allow the user to select one, or multiple "
"values that you specify."
msgstr ""
"Eine Gruppe von Auswahlkästchen, die du festlegst, aus denen der Benutzer "
"einen oder mehrere Werte auswählen kann."

#: includes/fields/class-acf-field-button-group.php:25
msgid ""
"A group of buttons with values that you specify, users can choose one option "
"from the values provided."
msgstr ""
"Eine Gruppe von Buttons mit von dir festgelegten Werten. Die Benutzer können "
"eine Option aus den angegebenen Werten auswählen."

#: includes/fields/class-acf-field-accordion.php:26
msgid ""
"Allows you to group and organize custom fields into collapsable panels that "
"are shown while editing content. Useful for keeping large datasets tidy."
msgstr ""

#: includes/fields.php:449
msgid ""
"This provides a solution for repeating content such as slides, team members, "
"and call-to-action tiles, by acting as a parent to a set of subfields which "
"can be repeated again and again."
msgstr ""

#: includes/fields.php:439
msgid ""
"This provides an interactive interface for managing a collection of "
"attachments. Most settings are similar to the Image field type. Additional "
"settings allow you to specify where new attachments are added in the gallery "
"and the minimum/maximum number of attachments allowed."
msgstr ""

#: includes/fields.php:429
msgid ""
"This provides a simple, structured, layout-based editor. The Flexible "
"Content field allows you to define, create and manage content with total "
"control by using layouts and subfields to design the available blocks."
msgstr ""

#: includes/fields.php:419
msgid ""
"This allows you to select and display existing fields. It does not duplicate "
"any fields in the database, but loads and displays the selected fields at "
"run-time. The Clone field can either replace itself with the selected fields "
"or display the selected fields as a group of subfields."
msgstr ""

#: includes/fields.php:416
msgctxt "noun"
msgid "Clone"
msgstr "Klon"

#: includes/admin/views/global/navigation.php:86
#: includes/class-acf-site-health.php:286 includes/fields.php:331
msgid "PRO"
msgstr "PRO"

#: includes/fields.php:329 includes/fields.php:386
msgid "Advanced"
msgstr "Erweitert"

#: includes/ajax/class-acf-ajax-local-json-diff.php:90
msgid "JSON (newer)"
msgstr "JSON (neuer)"

#: includes/ajax/class-acf-ajax-local-json-diff.php:86
msgid "Original"
msgstr "Original"

#: includes/ajax/class-acf-ajax-local-json-diff.php:60
msgid "Invalid post ID."
msgstr "Ungültige Beitrags-ID."

#: includes/ajax/class-acf-ajax-local-json-diff.php:52
msgid "Invalid post type selected for review."
msgstr "Der für die Betrachtung ausgewählte Inhaltstyp ist ungültig."

#: includes/admin/views/global/navigation.php:189
msgid "More"
msgstr "Mehr"

#: includes/admin/views/browse-fields-modal.php:96
msgid "Tutorial"
msgstr "Anleitung"

#: includes/admin/views/browse-fields-modal.php:73
msgid "Select Field"
msgstr "Feld auswählen"

#. translators: %s: A link to the popular fields used in ACF
#: includes/admin/views/browse-fields-modal.php:60
msgid "Try a different search term or browse %s"
msgstr "Probiere es mit einem anderen Suchbegriff oder durchsuche %s"

#: includes/admin/views/browse-fields-modal.php:57
msgid "Popular fields"
msgstr "Beliebte Felder"

#. translators: %s: The invalid search term
#: includes/admin/views/browse-fields-modal.php:50
#: includes/fields/class-acf-field-icon_picker.php:155
msgid "No search results for '%s'"
msgstr "Es wurden keine Suchergebnisse für ‚%s‘ gefunden"

#: includes/admin/views/browse-fields-modal.php:23
msgid "Search fields..."
msgstr "Felder suchen ..."

#: includes/admin/views/browse-fields-modal.php:21
msgid "Select Field Type"
msgstr "Feldtyp auswählen"

#: includes/admin/views/browse-fields-modal.php:4
msgid "Popular"
msgstr "Beliebt"

#: includes/admin/views/acf-taxonomy/list-empty.php:15
msgid "Add Taxonomy"
msgstr "Taxonomie hinzufügen"

#: includes/admin/views/acf-taxonomy/list-empty.php:14
msgid "Create custom taxonomies to classify post type content"
msgstr ""
"Erstelle individuelle Taxonomien, um die Inhalte von Inhaltstypen zu "
"kategorisieren"

#: includes/admin/views/acf-taxonomy/list-empty.php:13
msgid "Add Your First Taxonomy"
msgstr "Deine erste Taxonomie hinzufügen"

#: includes/admin/views/acf-taxonomy/basic-settings.php:122
msgid "Hierarchical taxonomies can have descendants (like categories)."
msgstr "Hierarchische Taxonomien können untergeordnete haben (wie Kategorien)."

#: includes/admin/views/acf-taxonomy/basic-settings.php:107
msgid "Makes a taxonomy visible on the frontend and in the admin dashboard."
msgstr "Macht eine Taxonomie sichtbar im Frontend und im Admin-Dashboard."

#: includes/admin/views/acf-taxonomy/basic-settings.php:91
msgid "One or many post types that can be classified with this taxonomy."
msgstr ""
"Einer oder mehrere Inhaltstypen, die mit dieser Taxonomie kategorisiert "
"werden können."

#. translators: example taxonomy
#: includes/admin/views/acf-taxonomy/basic-settings.php:60
msgid "genre"
msgstr "genre"

#. translators: example taxonomy
#: includes/admin/views/acf-taxonomy/basic-settings.php:42
msgid "Genre"
msgstr "Genre"

#. translators: example taxonomy
#: includes/admin/views/acf-taxonomy/basic-settings.php:25
msgid "Genres"
msgstr "Genres"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1211
msgid ""
"Optional custom controller to use instead of `WP_REST_Terms_Controller `."
msgstr ""
"Optionalen individuellen Controller verwenden anstelle von "
"„WP_REST_Terms_Controller“."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1155
msgid "Expose this post type in the REST API."
msgstr "Diesen Inhaltstyp in der REST-API anzeigen."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1055
msgid "Customize the query variable name"
msgstr "Den Namen der Abfrage-Variable anpassen"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1028
msgid ""
"Terms can be accessed using the non-pretty permalink, e.g., {query_var}"
"={term_slug}."
msgstr ""
"Begriffe können über den unschicken Permalink abgerufen werden, z. B. "
"{query_var}={term_slug}."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:981
msgid "Parent-child terms in URLs for hierarchical taxonomies."
msgstr "Über-/untergeordnete Begriffe in URLs von hierarchischen Taxonomien."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:941
msgid "Customize the slug used in the URL"
msgstr "Passe die Titelform an, die in der URL genutzt wird"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:924
msgid "Permalinks for this taxonomy are disabled."
msgstr "Permalinks sind für diese Taxonomie deaktiviert."

#. translators: this string will be appended with the new permalink structure.
#: includes/admin/views/acf-taxonomy/advanced-settings.php:921
msgid ""
"Rewrite the URL using the taxonomy key as the slug. Your permalink structure "
"will be"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:913
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1030
#: includes/admin/views/acf-taxonomy/basic-settings.php:57
msgid "Taxonomy Key"
msgstr "Taxonomie-Schlüssel"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:911
msgid "Select the type of permalink to use for this taxonomy."
msgstr "Wähle den Permalink-Typ, der für diese Taxonomie genutzt werden soll."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:896
msgid "Display a column for the taxonomy on post type listing screens."
msgstr ""
"Anzeigen einer Spalte für die Taxonomie in der Listenansicht der "
"Inhaltstypen."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:895
msgid "Show Admin Column"
msgstr "Admin-Spalte anzeigen"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:882
msgid "Show the taxonomy in the quick/bulk edit panel."
msgstr "Die Taxonomie im Schnell- und Mehrfach-Bearbeitungsbereich anzeigen."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:881
msgid "Quick Edit"
msgstr "QuickEdit"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:868
msgid "List the taxonomy in the Tag Cloud Widget controls."
msgstr ""
"Listet die Taxonomie in den Steuerelementen des Schlagwortwolke-Widgets auf."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:867
msgid "Tag Cloud"
msgstr "Schlagwort-Wolke"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:824
msgid ""
"A PHP function name to be called for sanitizing taxonomy data saved from a "
"meta box."
msgstr ""
"Ein PHP-Funktionsname, der zum Bereinigen von Taxonomiedaten aufgerufen "
"werden soll, die von einer Metabox gespeichert wurden."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:823
msgid "Meta Box Sanitization Callback"
msgstr "Metabox-Bereinigungs-Callback"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:805
msgid ""
"A PHP function name to be called to handle the content of a meta box on your "
"taxonomy."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:804
msgid "Register Meta Box Callback"
msgstr "Metabox-Callback registrieren"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:763
msgid "No Meta Box"
msgstr "Keine Metabox"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:762
msgid "Custom Meta Box"
msgstr "Individuelle Metabox"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:758
msgid ""
"Controls the meta box on the content editor screen. By default, the "
"Categories meta box is shown for hierarchical taxonomies, and the Tags meta "
"box is shown for non-hierarchical taxonomies."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:757
msgid "Meta Box"
msgstr "Metabox"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:746
#: includes/admin/views/acf-taxonomy/advanced-settings.php:767
msgid "Categories Meta Box"
msgstr "Kategorien-Metabox"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:745
#: includes/admin/views/acf-taxonomy/advanced-settings.php:766
msgid "Tags Meta Box"
msgstr "Schlagwörter-Metabox"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:704
msgid "A link to a tag"
msgstr "Ein Link zu einem Schlagwort"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:703
msgid "Describes a navigation link block variation used in the block editor."
msgstr ""

#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:698
msgid "A link to a %s"
msgstr "Ein Link zu einer Taxonomie %s"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:683
msgid "Tag Link"
msgstr "Schlagwort-Link"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:682
msgid ""
"Assigns a title for navigation link block variation used in the block editor."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:663
msgid "← Go to tags"
msgstr "← Zu Schlagwörtern gehen"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:662
msgid ""
"Assigns the text used to link back to the main index after updating a term."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:661
msgid "Back To Items"
msgstr "Zurück zu den Elementen"

#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:657
msgid "← Go to %s"
msgstr "← Zu %s gehen"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:642
msgid "Tags list"
msgstr "Schlagwörter-Liste"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:641
msgid "Assigns text to the table hidden heading."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:622
msgid "Tags list navigation"
msgstr "Navigation der Schlagwörterliste"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:621
msgid "Assigns text to the table pagination hidden heading."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:597
msgid "Filter by category"
msgstr "Nach Kategorie filtern"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:596
msgid "Assigns text to the filter button in the posts lists table."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:595
msgid "Filter By Item"
msgstr "Filtern nach Element"

#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:591
msgid "Filter by %s"
msgstr "Nach %s filtern"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:575
#: includes/admin/views/acf-taxonomy/advanced-settings.php:576
msgid ""
"The description is not prominent by default; however, some themes may show "
"it."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:574
msgid "Describes the Description field on the Edit Tags screen."
msgstr ""
"Beschreibt das Beschreibungsfeld in der Schlagwörter-bearbeiten-Ansicht."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:573
msgid "Description Field Description"
msgstr "Beschreibung des Beschreibungfeldes"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:554
#: includes/admin/views/acf-taxonomy/advanced-settings.php:555
msgid ""
"Assign a parent term to create a hierarchy. The term Jazz, for example, "
"would be the parent of Bebop and Big Band"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:553
msgid "Describes the Parent field on the Edit Tags screen."
msgstr ""
"Beschreibt das übergeordnete Feld in der Schlagwörter-bearbeiten-Ansicht."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:552
msgid "Parent Field Description"
msgstr "Beschreibung des übergeordneten Feldes"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:538
#: includes/admin/views/acf-taxonomy/advanced-settings.php:539
msgid ""
"The \"slug\" is the URL-friendly version of the name. It is usually all "
"lower case and contains only letters, numbers, and hyphens."
msgstr ""
"Die Titelform ist die URL-freundliche Version des Namens. Sie besteht "
"üblicherweise aus Kleinbuchstaben und zudem nur aus Buchstaben, Zahlen und "
"Bindestrichen."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:537
msgid "Describes the Slug field on the Edit Tags screen."
msgstr "Beschreibt das Titelform-Feld in der Schlagwörter-bearbeiten-Ansicht."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:536
msgid "Slug Field Description"
msgstr "Beschreibung des Titelformfeldes"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:522
#: includes/admin/views/acf-taxonomy/advanced-settings.php:523
msgid "The name is how it appears on your site"
msgstr "Der Name ist das, was auf deiner Website angezeigt wird"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:521
msgid "Describes the Name field on the Edit Tags screen."
msgstr "Beschreibt das Namensfeld in der Schlagwörter-bearbeiten-Ansicht."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:520
msgid "Name Field Description"
msgstr "Beschreibung des Namenfeldes"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:507
msgid "No tags"
msgstr "Keine Schlagwörter"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:506
msgid ""
"Assigns the text displayed in the posts and media list tables when no tags "
"or categories are available."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:505
msgid "No Terms"
msgstr "Keine Begriffe"

#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:501
msgid "No %s"
msgstr "Keine %s-Taxonomien"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:486
msgid "No tags found"
msgstr "Es wurden keine Schlagwörter gefunden"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:485
msgid ""
"Assigns the text displayed when clicking the 'choose from most used' text in "
"the taxonomy meta box when no tags are available, and assigns the text used "
"in the terms list table when there are no items for a taxonomy."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:484
msgid "Not Found"
msgstr "Es wurde nichts gefunden"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:463
msgid "Assigns text to the Title field of the Most Used tab."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:462
#: includes/admin/views/acf-taxonomy/advanced-settings.php:464
#: includes/admin/views/acf-taxonomy/advanced-settings.php:465
msgid "Most Used"
msgstr "Am häufigsten verwendet"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:444
msgid "Choose from the most used tags"
msgstr "Wähle aus den meistgenutzten Schlagwörtern"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:443
msgid ""
"Assigns the 'choose from most used' text used in the meta box when "
"JavaScript is disabled. Only used on non-hierarchical taxonomies."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:442
msgid "Choose From Most Used"
msgstr "Wähle aus den Meistgenutzten"

#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:438
msgid "Choose from the most used %s"
msgstr "Wähle aus den meistgenutzten %s"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:418
msgid "Add or remove tags"
msgstr "Schlagwörter hinzufügen oder entfernen"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:417
msgid ""
"Assigns the add or remove items text used in the meta box when JavaScript is "
"disabled. Only used on non-hierarchical taxonomies"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:416
msgid "Add Or Remove Items"
msgstr "Elemente hinzufügen oder entfernen"

#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:412
msgid "Add or remove %s"
msgstr "%s hinzufügen oder entfernen"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:392
msgid "Separate tags with commas"
msgstr "Schlagwörter durch Kommas trennen"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:391
msgid ""
"Assigns the separate item with commas text used in the taxonomy meta box. "
"Only used on non-hierarchical taxonomies."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:390
msgid "Separate Items With Commas"
msgstr "Trenne Elemente mit Kommas"

#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:386
msgid "Separate %s with commas"
msgstr "Trenne %s durch Kommas"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:366
msgid "Popular Tags"
msgstr "Beliebte Schlagwörter"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:365
msgid "Assigns popular items text. Only used for non-hierarchical taxonomies."
msgstr ""
"Zugeordneter Text für beliebte Elemente. Wird nur für nicht-hierarchische "
"Taxonomien verwendet."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:364
msgid "Popular Items"
msgstr "Beliebte Elemente"

#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:361
msgid "Popular %s"
msgstr "Beliebte %s"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:347
msgid "Search Tags"
msgstr "Schlagwörter suchen"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:346
msgid "Assigns search items text."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:323
msgid "Parent Category:"
msgstr "Übergeordnete Kategorie:"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:322
msgid "Assigns parent item text, but with a colon (:) added to the end."
msgstr ""
"Den Text des übergeordneten Elements zuordnen, aber mit einem Doppelpunkt "
"(:) am Ende."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:321
msgid "Parent Item With Colon"
msgstr "Übergeordnetes Element mit Doppelpunkt"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:298
msgid "Parent Category"
msgstr "Übergeordnete Kategorie"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:297
msgid "Assigns parent item text. Only used on hierarchical taxonomies."
msgstr ""
"Zugeordneter Text für übergeordnete Elemente. Wird nur für hierarchische "
"Taxonomien verwendet."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:296
msgid "Parent Item"
msgstr "Übergeordnetes Element"

#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:293
msgid "Parent %s"
msgstr "Übergeordnete Taxonomie %s"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:278
msgid "New Tag Name"
msgstr "Neuer Schlagwortname"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:277
msgid "Assigns the new item name text."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:276
msgid "New Item Name"
msgstr "Name des neuen Elements"

#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:273
msgid "New %s Name"
msgstr "Neuer %s-Name"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:258
msgid "Add New Tag"
msgstr "Ein neues Schlagwort hinzufügen"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:257
msgid "Assigns the add new item text."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:238
msgid "Update Tag"
msgstr "Schlagwort aktualisieren"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:237
msgid "Assigns the update item text."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:236
msgid "Update Item"
msgstr "Element aktualisieren"

#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:233
msgid "Update %s"
msgstr "%s aktualisieren"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:218
msgid "View Tag"
msgstr "Schlagwort anzeigen"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:217
msgid "In the admin bar to view term during editing."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:198
msgid "Edit Tag"
msgstr "Schlagwort bearbeiten"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:197
msgid "At the top of the editor screen when editing a term."
msgstr "Oben in der Editoransicht, wenn ein Begriff bearbeitet wird."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:178
msgid "All Tags"
msgstr "Alle Schlagwörter"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:177
msgid "Assigns the all items text."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:158
msgid "Assigns the menu name text."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:157
msgid "Menu Label"
msgstr "Menü-Beschriftung"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:131
msgid "Active taxonomies are enabled and registered with WordPress."
msgstr "Aktive Taxonomien sind aktiviert und in WordPress registriert."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:115
msgid "A descriptive summary of the taxonomy."
msgstr "Eine beschreibende Zusammenfassung der Taxonomie."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:95
msgid "A descriptive summary of the term."
msgstr "Eine beschreibende Zusammenfassung des Begriffs."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:94
msgid "Term Description"
msgstr "Beschreibung des Begriffs"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:76
msgid "Single word, no spaces. Underscores and dashes allowed."
msgstr ""
"Einzelnes Wort, keine Leerzeichen. Unterstriche und Bindestriche erlaubt."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:75
msgid "Term Slug"
msgstr "Begriffs-Titelform"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:56
msgid "The name of the default term."
msgstr "Der Name des Standardbegriffs."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:55
msgid "Term Name"
msgstr "Name des Begriffs"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:41
msgid ""
"Create a term for the taxonomy that cannot be deleted. It will not be "
"selected for posts by default."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:40
msgid "Default Term"
msgstr "Standardbegriff"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:28
msgid ""
"Whether terms in this taxonomy should be sorted in the order they are "
"provided to `wp_set_object_terms()`."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:27
msgid "Sort Terms"
msgstr "Begriffe sortieren"

#: includes/admin/views/acf-post-type/list-empty.php:14
msgid "Add Post Type"
msgstr "Inhaltstyp hinzufügen"

#: includes/admin/views/acf-post-type/list-empty.php:13
msgid ""
"Expand the functionality of WordPress beyond standard posts and pages with "
"custom post types."
msgstr ""
"Erweitere die Funktionalität von WordPress über Standard-Beiträge und -"
"Seiten hinaus mit individuellen Inhaltstypen."

#: includes/admin/views/acf-post-type/list-empty.php:12
msgid "Add Your First Post Type"
msgstr "Deinen ersten Inhaltstyp hinzufügen"

#: includes/admin/views/acf-post-type/basic-settings.php:136
#: includes/admin/views/acf-taxonomy/basic-settings.php:135
msgid "I know what I'm doing, show me all the options."
msgstr "Ich weiß, was ich tue, zeig mir alle Optionen."

#: includes/admin/views/acf-post-type/basic-settings.php:135
#: includes/admin/views/acf-taxonomy/basic-settings.php:134
msgid "Advanced Configuration"
msgstr "Erweiterte Konfiguration"

#: includes/admin/views/acf-post-type/basic-settings.php:123
msgid "Hierarchical post types can have descendants (like pages)."
msgstr "Hierarchische Inhaltstypen können untergeordnete haben (wie Seiten)."

#: includes/admin/views/acf-post-type/basic-settings.php:122
#: includes/admin/views/acf-taxonomy/advanced-settings.php:980
#: includes/admin/views/acf-taxonomy/basic-settings.php:121
msgid "Hierarchical"
msgstr "Hierarchisch"

#: includes/admin/views/acf-post-type/basic-settings.php:107
msgid "Visible on the frontend and in the admin dashboard."
msgstr "Sichtbar im Frontend und im Admin-Dashboard."

#: includes/admin/views/acf-post-type/basic-settings.php:106
#: includes/admin/views/acf-taxonomy/basic-settings.php:106
msgid "Public"
msgstr "Öffentlich"

#. translators: example post type
#: includes/admin/views/acf-post-type/basic-settings.php:59
msgid "movie"
msgstr "Film"

#: includes/admin/views/acf-post-type/basic-settings.php:57
msgid "Lower case letters, underscores and dashes only, Max 20 characters."
msgstr ""
"Nur Kleinbuchstaben, Unterstriche und Bindestriche, maximal 20 Zeichen."

#. translators: example post type
#: includes/admin/views/acf-post-type/basic-settings.php:41
msgid "Movie"
msgstr "Film"

#: includes/admin/views/acf-post-type/basic-settings.php:39
#: includes/admin/views/acf-taxonomy/basic-settings.php:40
msgid "Singular Label"
msgstr "Beschriftung (Einzahl)"

#. translators: example post type
#: includes/admin/views/acf-post-type/basic-settings.php:24
msgid "Movies"
msgstr "Filme"

#: includes/admin/views/acf-post-type/basic-settings.php:22
#: includes/admin/views/acf-taxonomy/basic-settings.php:23
msgid "Plural Label"
msgstr "Beschriftung (Mehrzahl)"

#: includes/admin/views/acf-post-type/advanced-settings.php:1298
msgid ""
"Optional custom controller to use instead of `WP_REST_Posts_Controller`."
msgstr ""
"Optionalen individuellen Controller verwenden anstelle von "
"„WP_REST_Posts_Controller“."

#: includes/admin/views/acf-post-type/advanced-settings.php:1297
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1210
msgid "Controller Class"
msgstr "Controller-Klasse"

#: includes/admin/views/acf-post-type/advanced-settings.php:1279
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1191
msgid "The namespace part of the REST API URL."
msgstr "Der Namensraum-Teil der REST-API-URL."

#: includes/admin/views/acf-post-type/advanced-settings.php:1278
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1190
msgid "Namespace Route"
msgstr "Namensraum-Route"

#: includes/admin/views/acf-post-type/advanced-settings.php:1260
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1172
msgid "The base URL for the post type REST API URLs."
msgstr "Die Basis-URL für REST-API-URLs des Inhalttyps."

#: includes/admin/views/acf-post-type/advanced-settings.php:1259
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1171
msgid "Base URL"
msgstr "Basis-URL"

#: includes/admin/views/acf-post-type/advanced-settings.php:1245
msgid ""
"Exposes this post type in the REST API. Required to use the block editor."
msgstr ""
"Zeigt diesen Inhalttyp in der REST-API. Wird zur Verwendung im Block-Editor "
"benötigt."

#: includes/admin/views/acf-post-type/advanced-settings.php:1244
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1154
msgid "Show In REST API"
msgstr "Im REST-API anzeigen"

#: includes/admin/views/acf-post-type/advanced-settings.php:1223
msgid "Customize the query variable name."
msgstr "Den Namen der Abfrage-Variable anpassen."

#: includes/admin/views/acf-post-type/advanced-settings.php:1222
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1054
msgid "Query Variable"
msgstr "Abfrage-Variable"

#: includes/admin/views/acf-post-type/advanced-settings.php:1200
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1032
msgid "No Query Variable Support"
msgstr "Keine Unterstützung von Abfrage-Variablen"

#: includes/admin/views/acf-post-type/advanced-settings.php:1199
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1031
msgid "Custom Query Variable"
msgstr "Individuelle Abfrage-Variable"

#: includes/admin/views/acf-post-type/advanced-settings.php:1196
msgid ""
"Items can be accessed using the non-pretty permalink, eg. {post_type}"
"={post_slug}."
msgstr ""
"Elemente können über den unschicken Permalink abgerufen werden, z. B. "
"{post_type}={post_slug}."

#: includes/admin/views/acf-post-type/advanced-settings.php:1195
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1027
msgid "Query Variable Support"
msgstr "Unterstützung von Abfrage-Variablen"

#: includes/admin/views/acf-post-type/advanced-settings.php:1170
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1003
msgid "URLs for an item and items can be accessed with a query string."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1169
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1002
msgid "Publicly Queryable"
msgstr "Öffentlich abfragbar"

#: includes/admin/views/acf-post-type/advanced-settings.php:1148
msgid "Custom slug for the Archive URL."
msgstr "Individuelle Titelform für die Archiv-URL."

#: includes/admin/views/acf-post-type/advanced-settings.php:1147
msgid "Archive Slug"
msgstr "Archiv-Titelform"

#: includes/admin/views/acf-post-type/advanced-settings.php:1134
msgid ""
"Has an item archive that can be customized with an archive template file in "
"your theme."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1133
msgid "Archive"
msgstr "Archiv"

#: includes/admin/views/acf-post-type/advanced-settings.php:1113
msgid "Pagination support for the items URLs such as the archives."
msgstr ""
"Unterstützung für Seitennummerierung der Element-URLs, wie bei Archiven."

#: includes/admin/views/acf-post-type/advanced-settings.php:1112
msgid "Pagination"
msgstr "Seitennummerierung"

#: includes/admin/views/acf-post-type/advanced-settings.php:1095
msgid "RSS feed URL for the post type items."
msgstr "RSS-Feed-URL für Inhaltstyp-Elemente."

#: includes/admin/views/acf-post-type/advanced-settings.php:1094
msgid "Feed URL"
msgstr "Feed-URL"

#: includes/admin/views/acf-post-type/advanced-settings.php:1076
#: includes/admin/views/acf-taxonomy/advanced-settings.php:961
msgid ""
"Alters the permalink structure to add the `WP_Rewrite::$front` prefix to "
"URLs."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1075
#: includes/admin/views/acf-taxonomy/advanced-settings.php:960
msgid "Front URL Prefix"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1056
msgid "Customize the slug used in the URL."
msgstr "Passe die Titelform an, die in der URL genutzt wird."

#: includes/admin/views/acf-post-type/advanced-settings.php:1055
#: includes/admin/views/acf-taxonomy/advanced-settings.php:940
msgid "URL Slug"
msgstr "URL-Titelform"

#: includes/admin/views/acf-post-type/advanced-settings.php:1039
msgid "Permalinks for this post type are disabled."
msgstr "Permalinks sind für diesen Inhaltstyp deaktiviert."

#. translators: this string will be appended with the new permalink structure.
#: includes/admin/views/acf-post-type/advanced-settings.php:1038
#: includes/admin/views/acf-taxonomy/advanced-settings.php:923
msgid ""
"Rewrite the URL using a custom slug defined in the input below. Your "
"permalink structure will be"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1030
#: includes/admin/views/acf-taxonomy/advanced-settings.php:915
msgid "No Permalink (prevent URL rewriting)"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1029
#: includes/admin/views/acf-taxonomy/advanced-settings.php:914
msgid "Custom Permalink"
msgstr "Individueller Permalink"

#: includes/admin/views/acf-post-type/advanced-settings.php:1028
#: includes/admin/views/acf-post-type/advanced-settings.php:1198
#: includes/admin/views/acf-post-type/basic-settings.php:56
msgid "Post Type Key"
msgstr "Inhaltstyp-Schlüssel"

#. translators: this string will be appended with the new permalink structure.
#: includes/admin/views/acf-post-type/advanced-settings.php:1026
#: includes/admin/views/acf-post-type/advanced-settings.php:1036
msgid ""
"Rewrite the URL using the post type key as the slug. Your permalink "
"structure will be"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1024
#: includes/admin/views/acf-taxonomy/advanced-settings.php:910
msgid "Permalink Rewrite"
msgstr "Permalink neu schreiben"

#: includes/admin/views/acf-post-type/advanced-settings.php:1010
msgid "Delete items by a user when that user is deleted."
msgstr "Elemente eines Benutzers löschen, wenn der Benutzer gelöscht wird."

#: includes/admin/views/acf-post-type/advanced-settings.php:1009
msgid "Delete With User"
msgstr "Zusammen mit dem Benutzer löschen"

#: includes/admin/views/acf-post-type/advanced-settings.php:995
msgid "Allow the post type to be exported from 'Tools' > 'Export'."
msgstr "Erlaubt den Inhaltstyp zu exportieren unter „Werkzeuge“ > „Export“."

#: includes/admin/views/acf-post-type/advanced-settings.php:994
msgid "Can Export"
msgstr "Kann exportieren"

#: includes/admin/views/acf-post-type/advanced-settings.php:963
msgid "Optionally provide a plural to be used in capabilities."
msgstr "Du kannst optional eine Mehrzahl für Berechtigungen angeben."

#: includes/admin/views/acf-post-type/advanced-settings.php:962
msgid "Plural Capability Name"
msgstr "Name der Berechtigung (Mehrzahl)"

#: includes/admin/views/acf-post-type/advanced-settings.php:944
msgid "Choose another post type to base the capabilities for this post type."
msgstr ""
"Wähle einen anderen Inhaltstyp aus als Basis der Berechtigungen für diesen "
"Inhaltstyp."

#: includes/admin/views/acf-post-type/advanced-settings.php:943
msgid "Singular Capability Name"
msgstr "Name der Berechtigung (Einzahl)"

#: includes/admin/views/acf-post-type/advanced-settings.php:929
msgid ""
"By default the capabilities of the post type will inherit the 'Post' "
"capability names, eg. edit_post, delete_posts. Enable to use post type "
"specific capabilities, eg. edit_{singular}, delete_{plural}."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:928
msgid "Rename Capabilities"
msgstr "Berechtigungen umbenennen"

#: includes/admin/views/acf-post-type/advanced-settings.php:913
msgid "Exclude From Search"
msgstr "Von der Suche ausschließen"

#: includes/admin/views/acf-post-type/advanced-settings.php:900
#: includes/admin/views/acf-taxonomy/advanced-settings.php:854
msgid ""
"Allow items to be added to menus in the 'Appearance' > 'Menus' screen. Must "
"be turned on in 'Screen options'."
msgstr ""
"Erlaubt das Hinzufügen von Elementen zu Menüs unter „Design“ > „Menüs“. Muss "
"aktiviert werden unter „Ansicht anpassen“."

#: includes/admin/views/acf-post-type/advanced-settings.php:899
#: includes/admin/views/acf-taxonomy/advanced-settings.php:853
msgid "Appearance Menus Support"
msgstr "Unterstützung für Design-Menüs"

#: includes/admin/views/acf-post-type/advanced-settings.php:881
msgid "Appears as an item in the 'New' menu in the admin bar."
msgstr "Erscheint als Eintrag im „Neu“-Menü der Adminleiste."

#: includes/admin/views/acf-post-type/advanced-settings.php:880
msgid "Show In Admin Bar"
msgstr "In der Adminleiste anzeigen"

#: includes/admin/views/acf-post-type/advanced-settings.php:849
msgid ""
"A PHP function name to be called when setting up the meta boxes for the edit "
"screen."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:848
msgid "Custom Meta Box Callback"
msgstr "Individueller Metabox-Callback"

#: includes/admin/views/acf-post-type/advanced-settings.php:822
msgid "Menu Icon"
msgstr "Menü-Icon"

#: includes/admin/views/acf-post-type/advanced-settings.php:778
msgid "The position in the sidebar menu in the admin dashboard."
msgstr "Die Position im Seitenleisten-Menü des Admin-Dashboards."

#: includes/admin/views/acf-post-type/advanced-settings.php:777
msgid "Menu Position"
msgstr "Menü-Position"

#: includes/admin/views/acf-post-type/advanced-settings.php:759
msgid ""
"By default the post type will get a new top level item in the admin menu. If "
"an existing top level item is supplied here, the post type will be added as "
"a submenu item under it."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:758
msgid "Admin Menu Parent"
msgstr "Übergeordnetes Admin-Menü"

#: includes/admin/views/acf-post-type/advanced-settings.php:739
#: includes/admin/views/acf-taxonomy/advanced-settings.php:734
msgid "Admin editor navigation in the sidebar menu."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:738
#: includes/admin/views/acf-taxonomy/advanced-settings.php:733
msgid "Show In Admin Menu"
msgstr "Im Admin-Menü anzeigen"

#: includes/admin/views/acf-post-type/advanced-settings.php:725
#: includes/admin/views/acf-taxonomy/advanced-settings.php:719
msgid "Items can be edited and managed in the admin dashboard."
msgstr "Elemente können im Admin-Dashboard bearbeitet und verwaltet werden."

#: includes/admin/views/acf-post-type/advanced-settings.php:724
#: includes/admin/views/acf-taxonomy/advanced-settings.php:718
msgid "Show In UI"
msgstr "In der Benutzeroberfläche anzeigen"

#: includes/admin/views/acf-post-type/advanced-settings.php:694
msgid "A link to a post."
msgstr "Ein Link zu einem Beitrag."

#: includes/admin/views/acf-post-type/advanced-settings.php:693
msgid "Description for a navigation link block variation."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:692
#: includes/admin/views/acf-taxonomy/advanced-settings.php:702
msgid "Item Link Description"
msgstr "Beschreibung des Element-Links"

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:688
msgid "A link to a %s."
msgstr "Ein Link zu einem Inhaltstyp %s"

#: includes/admin/views/acf-post-type/advanced-settings.php:673
msgid "Post Link"
msgstr "Beitragslink"

#: includes/admin/views/acf-post-type/advanced-settings.php:672
msgid "Title for a navigation link block variation."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:671
#: includes/admin/views/acf-taxonomy/advanced-settings.php:681
msgid "Item Link"
msgstr "Element-Link"

#. translators: %s Singular form of post type name
#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:668
#: includes/admin/views/acf-taxonomy/advanced-settings.php:678
msgid "%s Link"
msgstr "%s-Link"

#: includes/admin/views/acf-post-type/advanced-settings.php:653
msgid "Post updated."
msgstr "Der Beitrag wurde aktualisiert."

#: includes/admin/views/acf-post-type/advanced-settings.php:652
msgid "In the editor notice after an item is updated."
msgstr "Im Editor-Hinweis, nachdem ein Element aktualisiert wurde."

#: includes/admin/views/acf-post-type/advanced-settings.php:651
msgid "Item Updated"
msgstr "Das Element wurde aktualisiert"

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:648
msgid "%s updated."
msgstr "%s wurde aktualisiert."

#: includes/admin/views/acf-post-type/advanced-settings.php:633
msgid "Post scheduled."
msgstr "Die Beiträge wurden geplant."

#: includes/admin/views/acf-post-type/advanced-settings.php:632
msgid "In the editor notice after scheduling an item."
msgstr "Im Editor-Hinweis, nachdem ein Element geplant wurde."

#: includes/admin/views/acf-post-type/advanced-settings.php:631
msgid "Item Scheduled"
msgstr "Das Element wurde geplant"

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:628
msgid "%s scheduled."
msgstr "%s wurde geplant."

#: includes/admin/views/acf-post-type/advanced-settings.php:613
msgid "Post reverted to draft."
msgstr "Der Beitrag wurde auf Entwurf zurückgesetzt."

#: includes/admin/views/acf-post-type/advanced-settings.php:612
msgid "In the editor notice after reverting an item to draft."
msgstr ""
"Im Editor-Hinweis, nachdem ein Element auf Entwurf zurückgesetzt wurde."

#: includes/admin/views/acf-post-type/advanced-settings.php:611
msgid "Item Reverted To Draft"
msgstr "Das Element wurde auf Entwurf zurückgesetzt"

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:608
msgid "%s reverted to draft."
msgstr "%s wurde auf Entwurf zurückgesetzt."

#: includes/admin/views/acf-post-type/advanced-settings.php:593
msgid "Post published privately."
msgstr "Der Beitrag wurde privat veröffentlicht."

#: includes/admin/views/acf-post-type/advanced-settings.php:592
msgid "In the editor notice after publishing a private item."
msgstr "Im Editor-Hinweis, nachdem ein Element privat veröffentlicht wurde."

#: includes/admin/views/acf-post-type/advanced-settings.php:591
msgid "Item Published Privately"
msgstr "Das Element wurde privat veröffentlicht"

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:588
msgid "%s published privately."
msgstr "%s wurde privat veröffentlicht."

#: includes/admin/views/acf-post-type/advanced-settings.php:573
msgid "Post published."
msgstr "Der Beitrag wurde veröffentlicht."

#: includes/admin/views/acf-post-type/advanced-settings.php:572
msgid "In the editor notice after publishing an item."
msgstr "Im Editor-Hinweis, nachdem ein Element veröffentlicht wurde."

#: includes/admin/views/acf-post-type/advanced-settings.php:571
msgid "Item Published"
msgstr "Das Element wurde veröffentlicht"

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:568
msgid "%s published."
msgstr "%s wurde veröffentlicht."

#: includes/admin/views/acf-post-type/advanced-settings.php:553
msgid "Posts list"
msgstr "Liste der Beiträge"

#: includes/admin/views/acf-post-type/advanced-settings.php:552
msgid "Used by screen readers for the items list on the post type list screen."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:551
#: includes/admin/views/acf-taxonomy/advanced-settings.php:640
msgid "Items List"
msgstr "Elementliste"

#. translators: %s Plural form of post type name
#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:548
#: includes/admin/views/acf-taxonomy/advanced-settings.php:637
msgid "%s list"
msgstr "%s-Liste"

#: includes/admin/views/acf-post-type/advanced-settings.php:533
msgid "Posts list navigation"
msgstr "Navigation der Beiträge-Liste"

#: includes/admin/views/acf-post-type/advanced-settings.php:532
msgid ""
"Used by screen readers for the filter list pagination on the post type list "
"screen."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:531
#: includes/admin/views/acf-taxonomy/advanced-settings.php:620
msgid "Items List Navigation"
msgstr "Navigation der Elementliste"

#. translators: %s Plural form of post type name
#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:528
#: includes/admin/views/acf-taxonomy/advanced-settings.php:617
msgid "%s list navigation"
msgstr "%s-Listen-Navigation"

#: includes/admin/views/acf-post-type/advanced-settings.php:512
msgid "Filter posts by date"
msgstr "Beiträge nach Datum filtern"

#: includes/admin/views/acf-post-type/advanced-settings.php:511
msgid ""
"Used by screen readers for the filter by date heading on the post type list "
"screen."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:510
msgid "Filter Items By Date"
msgstr "Elemente nach Datum filtern"

#. translators: %s Plural form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:506
msgid "Filter %s by date"
msgstr "%s nach Datum filtern"

#: includes/admin/views/acf-post-type/advanced-settings.php:491
msgid "Filter posts list"
msgstr "Liste mit Beiträgen filtern"

#: includes/admin/views/acf-post-type/advanced-settings.php:490
msgid ""
"Used by screen readers for the filter links heading on the post type list "
"screen."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:489
msgid "Filter Items List"
msgstr "Elemente-Liste filtern"

#. translators: %s Plural form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:485
msgid "Filter %s list"
msgstr "%s-Liste filtern"

#: includes/admin/views/acf-post-type/advanced-settings.php:469
msgid "In the media modal showing all media uploaded to this item."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:468
msgid "Uploaded To This Item"
msgstr "Zu diesem Element hochgeladen"

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:464
msgid "Uploaded to this %s"
msgstr "Zu diesem %s hochgeladen"

#: includes/admin/views/acf-post-type/advanced-settings.php:449
msgid "Insert into post"
msgstr "In den Beitrag einfügen"

#: includes/admin/views/acf-post-type/advanced-settings.php:448
msgid "As the button label when adding media to content."
msgstr "Als Button-Beschriftung, wenn Medien zum Inhalt hinzugefügt werden."

#: includes/admin/views/acf-post-type/advanced-settings.php:447
msgid "Insert Into Media Button"
msgstr ""

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:443
msgid "Insert into %s"
msgstr "In %s einfügen"

#: includes/admin/views/acf-post-type/advanced-settings.php:428
msgid "Use as featured image"
msgstr "Als Beitragsbild verwenden"

#: includes/admin/views/acf-post-type/advanced-settings.php:427
msgid ""
"As the button label for selecting to use an image as the featured image."
msgstr ""
"Als Button-Beschriftung, wenn ein Bild als Beitragsbild ausgewählt wird."

#: includes/admin/views/acf-post-type/advanced-settings.php:426
msgid "Use Featured Image"
msgstr "Beitragsbild verwenden"

#: includes/admin/views/acf-post-type/advanced-settings.php:413
msgid "Remove featured image"
msgstr "Beitragsbild entfernen"

#: includes/admin/views/acf-post-type/advanced-settings.php:412
msgid "As the button label when removing the featured image."
msgstr "Als Button-Beschriftung, wenn das Beitragsbild entfernt wird."

#: includes/admin/views/acf-post-type/advanced-settings.php:411
msgid "Remove Featured Image"
msgstr "Beitragsbild entfernen"

#: includes/admin/views/acf-post-type/advanced-settings.php:398
msgid "Set featured image"
msgstr "Beitragsbild festlegen"

#: includes/admin/views/acf-post-type/advanced-settings.php:397
msgid "As the button label when setting the featured image."
msgstr "Als Button-Beschriftung, wenn das Beitragsbild festgelegt wird."

#: includes/admin/views/acf-post-type/advanced-settings.php:396
msgid "Set Featured Image"
msgstr "Beitragsbild festlegen"

#: includes/admin/views/acf-post-type/advanced-settings.php:383
msgid "Featured image"
msgstr "Beitragsbild"

#: includes/admin/views/acf-post-type/advanced-settings.php:382
msgid "In the editor used for the title of the featured image meta box."
msgstr ""
"In dem Editor, der für den Titel der Beitragsattribute-Metabox verwendet "
"wird."

#: includes/admin/views/acf-post-type/advanced-settings.php:381
msgid "Featured Image Meta Box"
msgstr "Beitragsbild-Metabox"

#: includes/admin/views/acf-post-type/advanced-settings.php:368
msgid "Post Attributes"
msgstr "Beitrags-Attribute"

#: includes/admin/views/acf-post-type/advanced-settings.php:367
msgid "In the editor used for the title of the post attributes meta box."
msgstr ""
"In dem Editor, der für den Titel der Beitragsattribute-Metabox verwendet "
"wird."

#: includes/admin/views/acf-post-type/advanced-settings.php:366
msgid "Attributes Meta Box"
msgstr "Metabox-Attribute"

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:363
msgid "%s Attributes"
msgstr "%s-Attribute"

#: includes/admin/views/acf-post-type/advanced-settings.php:348
msgid "Post Archives"
msgstr "Beitrags-Archive"

#: includes/admin/views/acf-post-type/advanced-settings.php:347
msgid ""
"Adds 'Post Type Archive' items with this label to the list of posts shown "
"when adding items to an existing menu in a CPT with archives enabled. Only "
"appears when editing menus in 'Live Preview' mode and a custom archive slug "
"has been provided."
msgstr ""
"Fügt ‚Inhaltstyp-Archiv‘-Elemente mit dieser Beschriftung zur Liste der "
"Beiträge hinzu, die beim Hinzufügen von Elementen zu einem bestehenden Menü "
"in einem individuellen Inhaltstyp mit aktivierten Archiven angezeigt werden. "
"Erscheint nur, wenn Menüs im Modus „Live-Vorschau“ bearbeitet werden und "
"eine individuelle Archiv-Titelform angegeben wurde."

#: includes/admin/views/acf-post-type/advanced-settings.php:346
msgid "Archives Nav Menu"
msgstr "Navigations-Menü der Archive"

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:343
msgid "%s Archives"
msgstr "%s-Archive"

#: includes/admin/views/acf-post-type/advanced-settings.php:328
msgid "No posts found in Trash"
msgstr "Es wurden keine Beiträge im Papierkorb gefunden"

#: includes/admin/views/acf-post-type/advanced-settings.php:327
msgid ""
"At the top of the post type list screen when there are no posts in the trash."
msgstr ""
"Oben in der Listen-Ansicht des Inhaltstyps, wenn keine Beiträge im "
"Papierkorb vorhanden sind."

#: includes/admin/views/acf-post-type/advanced-settings.php:326
msgid "No Items Found in Trash"
msgstr "Es wurden keine Elemente im Papierkorb gefunden"

#. translators: %s Plural form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:322
msgid "No %s found in Trash"
msgstr "%s konnten nicht im Papierkorb gefunden werden"

#: includes/admin/views/acf-post-type/advanced-settings.php:307
msgid "No posts found"
msgstr "Es wurden keine Beiträge gefunden"

#: includes/admin/views/acf-post-type/advanced-settings.php:306
msgid ""
"At the top of the post type list screen when there are no posts to display."
msgstr ""
"Oben in der Listenansicht für Inhaltstypen, wenn es keine Beiträge zum "
"Anzeigen gibt."

#: includes/admin/views/acf-post-type/advanced-settings.php:305
msgid "No Items Found"
msgstr "Es wurden keine Elemente gefunden"

#. translators: %s Plural form of post type name
#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:301
#: includes/admin/views/acf-taxonomy/advanced-settings.php:480
msgid "No %s found"
msgstr "%s konnten nicht gefunden werden"

#: includes/admin/views/acf-post-type/advanced-settings.php:286
msgid "Search Posts"
msgstr "Beiträge suchen"

#: includes/admin/views/acf-post-type/advanced-settings.php:285
msgid "At the top of the items screen when searching for an item."
msgstr "Oben in der Elementansicht, während der Suche nach einem Element."

#: includes/admin/views/acf-post-type/advanced-settings.php:284
#: includes/admin/views/acf-taxonomy/advanced-settings.php:345
msgid "Search Items"
msgstr "Elemente suchen"

#. translators: %s Singular form of post type name
#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:281
#: includes/admin/views/acf-taxonomy/advanced-settings.php:342
msgid "Search %s"
msgstr "%s suchen"

#: includes/admin/views/acf-post-type/advanced-settings.php:266
msgid "Parent Page:"
msgstr "Übergeordnete Seite:"

#: includes/admin/views/acf-post-type/advanced-settings.php:265
msgid "For hierarchical types in the post type list screen."
msgstr "Für hierarchische Typen in der Listenansicht der Inhaltstypen."

#: includes/admin/views/acf-post-type/advanced-settings.php:264
msgid "Parent Item Prefix"
msgstr "Präfix des übergeordneten Elementes"

#. translators: %s Singular form of post type name
#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:261
#: includes/admin/views/acf-taxonomy/advanced-settings.php:318
msgid "Parent %s:"
msgstr "%s, übergeordnet:"

#: includes/admin/views/acf-post-type/advanced-settings.php:246
msgid "New Post"
msgstr "Neuer Beitrag"

#: includes/admin/views/acf-post-type/advanced-settings.php:244
msgid "New Item"
msgstr "Neues Element"

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:241
msgid "New %s"
msgstr "Neuer Inhaltstyp %s"

#: includes/admin/views/acf-post-type/advanced-settings.php:206
#: includes/admin/views/acf-post-type/advanced-settings.php:226
msgid "Add New Post"
msgstr "Neuen Beitrag hinzufügen"

#: includes/admin/views/acf-post-type/advanced-settings.php:205
msgid "At the top of the editor screen when adding a new item."
msgstr "Oben in der Editoransicht, wenn ein neues Element hinzugefügt wird."

#: includes/admin/views/acf-post-type/advanced-settings.php:204
#: includes/admin/views/acf-taxonomy/advanced-settings.php:256
msgid "Add New Item"
msgstr "Neues Element hinzufügen"

#. translators: %s Singular form of post type name
#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:201
#: includes/admin/views/acf-post-type/advanced-settings.php:221
#: includes/admin/views/acf-taxonomy/advanced-settings.php:253
msgid "Add New %s"
msgstr "Neu hinzufügen: %s"

#: includes/admin/views/acf-post-type/advanced-settings.php:186
msgid "View Posts"
msgstr "Beiträge anzeigen"

#: includes/admin/views/acf-post-type/advanced-settings.php:185
msgid ""
"Appears in the admin bar in the 'All Posts' view, provided the post type "
"supports archives and the home page is not an archive of that post type."
msgstr ""
"Wird in der Adminleiste in der Ansicht „Alle Beiträge“ angezeigt, sofern der "
"Inhaltstyp Archive unterstützt und die Homepage kein Archiv dieses "
"Inhaltstyps ist."

#: includes/admin/views/acf-post-type/advanced-settings.php:184
msgid "View Items"
msgstr "Elemente anzeigen"

#: includes/admin/views/acf-post-type/advanced-settings.php:166
msgid "View Post"
msgstr "Beitrag anzeigen"

#: includes/admin/views/acf-post-type/advanced-settings.php:165
msgid "In the admin bar to view item when editing it."
msgstr "In der Adminleiste, um das Element beim Bearbeiten anzuzeigen."

#: includes/admin/views/acf-post-type/advanced-settings.php:164
#: includes/admin/views/acf-taxonomy/advanced-settings.php:216
msgid "View Item"
msgstr "Element anzeigen"

#. translators: %s Singular form of post type name
#. translators: %s Plural form of post type name
#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:161
#: includes/admin/views/acf-post-type/advanced-settings.php:181
#: includes/admin/views/acf-taxonomy/advanced-settings.php:213
msgid "View %s"
msgstr "%s anzeigen"

#: includes/admin/views/acf-post-type/advanced-settings.php:146
msgid "Edit Post"
msgstr "Beitrag bearbeiten"

#: includes/admin/views/acf-post-type/advanced-settings.php:145
msgid "At the top of the editor screen when editing an item."
msgstr "Oben in der Editoransicht, wenn ein Element bearbeitet wird."

#: includes/admin/views/acf-post-type/advanced-settings.php:144
#: includes/admin/views/acf-taxonomy/advanced-settings.php:196
msgid "Edit Item"
msgstr "Element bearbeiten"

#. translators: %s Singular form of post type name
#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:141
#: includes/admin/views/acf-taxonomy/advanced-settings.php:193
msgid "Edit %s"
msgstr "%s bearbeiten"

#: includes/admin/views/acf-post-type/advanced-settings.php:126
msgid "All Posts"
msgstr "Alle Beiträge"

#: includes/admin/views/acf-post-type/advanced-settings.php:125
#: includes/admin/views/acf-post-type/advanced-settings.php:225
#: includes/admin/views/acf-post-type/advanced-settings.php:245
msgid "In the post type submenu in the admin dashboard."
msgstr "Im Untermenü des Inhaltstyps im Admin-Dashboard."

#: includes/admin/views/acf-post-type/advanced-settings.php:124
#: includes/admin/views/acf-taxonomy/advanced-settings.php:176
msgid "All Items"
msgstr "Alle Elemente"

#. translators: %s Plural form of post type name
#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:121
#: includes/admin/views/acf-taxonomy/advanced-settings.php:173
msgid "All %s"
msgstr "Alle %s"

#: includes/admin/views/acf-post-type/advanced-settings.php:105
msgid "Admin menu name for the post type."
msgstr "Name des Admin-Menüs für den Inhaltstyp."

#: includes/admin/views/acf-post-type/advanced-settings.php:104
msgid "Menu Name"
msgstr "Menüname"

#: includes/admin/views/acf-post-type/advanced-settings.php:90
#: includes/admin/views/acf-taxonomy/advanced-settings.php:142
msgid "Regenerate all labels using the Singular and Plural labels"
msgstr ""
"Alle Beschriftungen unter Verwendung der Einzahl- und Mehrzahl-"
"Beschriftungen neu generieren"

#: includes/admin/views/acf-post-type/advanced-settings.php:88
#: includes/admin/views/acf-taxonomy/advanced-settings.php:140
msgid "Regenerate"
msgstr "Neu generieren"

#: includes/admin/views/acf-post-type/advanced-settings.php:79
msgid "Active post types are enabled and registered with WordPress."
msgstr "Aktive Inhaltstypen sind aktiviert und in WordPress registriert."

#: includes/admin/views/acf-post-type/advanced-settings.php:63
msgid "A descriptive summary of the post type."
msgstr "Eine beschreibende Zusammenfassung des Inhaltstyps."

#: includes/admin/views/acf-post-type/advanced-settings.php:48
msgid "Add Custom"
msgstr "Individuell hinzufügen"

#: includes/admin/views/acf-post-type/advanced-settings.php:42
msgid "Enable various features in the content editor."
msgstr "Verschiedene Funktionen im Inhalts-Editor aktivieren."

#: includes/admin/views/acf-post-type/advanced-settings.php:31
msgid "Post Formats"
msgstr "Beitragsformate"

#: includes/admin/views/acf-post-type/advanced-settings.php:25
msgid "Editor"
msgstr "Editor"

#: includes/admin/views/acf-post-type/advanced-settings.php:24
msgid "Trackbacks"
msgstr "Trackbacks"

#: includes/admin/views/acf-post-type/basic-settings.php:87
msgid "Select existing taxonomies to classify items of the post type."
msgstr ""
"Vorhandene Taxonomien auswählen, um Elemente des Inhaltstyps zu "
"kategorisieren."

#: includes/admin/views/acf-field-group/field.php:158
msgid "Browse Fields"
msgstr "Felder durchsuchen"

#: includes/admin/tools/class-acf-admin-tool-import.php:287
msgid "Nothing to import"
msgstr "Es gibt nichts zu importieren"

#: includes/admin/tools/class-acf-admin-tool-import.php:282
msgid ". The Custom Post Type UI plugin can be deactivated."
msgstr ". Das Plugin Custom Post Type UI kann deaktiviert werden."

#. translators: %d - number of items imported from CPTUI
#: includes/admin/tools/class-acf-admin-tool-import.php:273
msgid "Imported %d item from Custom Post Type UI -"
msgid_plural "Imported %d items from Custom Post Type UI -"
msgstr[0] "Es wurde %d Element von Custom Post Type UI importiert -"
msgstr[1] "Es wurden %d Elemente von Custom Post Type UI importiert -"

#: includes/admin/tools/class-acf-admin-tool-import.php:257
msgid "Failed to import taxonomies."
msgstr "Der Import der Taxonomien ist fehlgeschlagen."

#: includes/admin/tools/class-acf-admin-tool-import.php:239
msgid "Failed to import post types."
msgstr "Der Import der Inhaltstypen ist fehlgeschlagen."

#: includes/admin/tools/class-acf-admin-tool-import.php:228
msgid "Nothing from Custom Post Type UI plugin selected for import."
msgstr ""
"Es wurde nichts aus dem Plugin Custom Post Type UI für den Import ausgewählt."

#: includes/admin/tools/class-acf-admin-tool-import.php:204
msgid "Imported 1 item"
msgid_plural "Imported %s items"
msgstr[0] "1 Element wurde importiert"
msgstr[1] "%s Elemente wurden importiert"

#: includes/admin/tools/class-acf-admin-tool-import.php:119
msgid ""
"Importing a Post Type or Taxonomy with the same key as one that already "
"exists will overwrite the settings for the existing Post Type or Taxonomy "
"with those of the import."
msgstr ""
"Wenn ein Inhaltstyp oder eine Taxonomie mit einem Schlüssel importiert wird, "
"der bereits vorhanden ist, werden die Einstellungen des vorhandenen "
"Inhaltstyps oder der vorhandenen Taxonomie mit denen des Imports "
"überschrieben."

#: includes/admin/tools/class-acf-admin-tool-import.php:108
#: includes/admin/tools/class-acf-admin-tool-import.php:124
msgid "Import from Custom Post Type UI"
msgstr "Aus Custom Post Type UI importieren"

#: includes/admin/tools/class-acf-admin-tool-export.php:354
msgid ""
"The following code can be used to register a local version of the selected "
"items. Storing field groups, post types, or taxonomies locally can provide "
"many benefits such as faster load times, version control & dynamic fields/"
"settings. Simply copy and paste the following code to your theme's functions."
"php file or include it within an external file, then deactivate or delete "
"the items from the ACF admin."
msgstr ""
"Der folgende Code kann verwendet werden, um eine lokale Version der "
"ausgewählten Elemente zu registrieren. Die lokale Speicherung von "
"Feldgruppen, Inhaltstypen oder Taxonomien kann viele Vorteile bieten, wie "
"z. B. schnellere Ladezeiten, Versionskontrolle und dynamische Felder/"
"Einstellungen. Kopiere den folgenden Code in die Datei functions.php deines "
"Themes oder füge ihn in eine externe Datei ein und deaktiviere oder lösche "
"anschließend die Elemente in der ACF-Administration."

#: includes/admin/tools/class-acf-admin-tool-export.php:353
msgid "Export - Generate PHP"
msgstr "Export – PHP generieren"

#: includes/admin/tools/class-acf-admin-tool-export.php:330
msgid "Export"
msgstr "Export"

#: includes/admin/tools/class-acf-admin-tool-export.php:264
msgid "Select Taxonomies"
msgstr "Taxonomien auswählen"

#: includes/admin/tools/class-acf-admin-tool-export.php:239
msgid "Select Post Types"
msgstr "Inhaltstypen auswählen"

#: includes/admin/tools/class-acf-admin-tool-export.php:155
msgid "Exported 1 item."
msgid_plural "Exported %s items."
msgstr[0] "1 ELement wurde exportiert."
msgstr[1] "%s Elemente wurden exportiert."

#: includes/admin/post-types/admin-taxonomy.php:127
#: assets/build/js/acf-internal-post-type.js:182
#: assets/build/js/acf-internal-post-type.js:256
msgid "Category"
msgstr "Kategorie"

#: includes/admin/post-types/admin-taxonomy.php:125
#: assets/build/js/acf-internal-post-type.js:179
#: assets/build/js/acf-internal-post-type.js:253
msgid "Tag"
msgstr "Schlagwort"

#. translators: %s taxonomy name
#: includes/admin/post-types/admin-taxonomy.php:82
msgid "%s taxonomy created"
msgstr "Die Taxonomie %s wurde erstellt"

#. translators: %s taxonomy name
#: includes/admin/post-types/admin-taxonomy.php:76
msgid "%s taxonomy updated"
msgstr "Die Taxonomie %s wurde aktualisiert"

#: includes/admin/post-types/admin-taxonomy.php:56
msgid "Taxonomy draft updated."
msgstr "Der Taxonomie-Entwurf wurde aktualisiert."

#: includes/admin/post-types/admin-taxonomy.php:55
msgid "Taxonomy scheduled for."
msgstr "Die Taxonomie wurde geplant für."

#: includes/admin/post-types/admin-taxonomy.php:54
msgid "Taxonomy submitted."
msgstr "Die Taxonomie wurde übermittelt."

#: includes/admin/post-types/admin-taxonomy.php:53
msgid "Taxonomy saved."
msgstr "Die Taxonomie wurde gespeichert."

#: includes/admin/post-types/admin-taxonomy.php:49
msgid "Taxonomy deleted."
msgstr "Die Taxonomie wurde gelöscht."

#: includes/admin/post-types/admin-taxonomy.php:48
msgid "Taxonomy updated."
msgstr "Die Taxonomie wurde aktualisiert."

#: includes/admin/post-types/admin-taxonomies.php:351
#: includes/admin/post-types/admin-taxonomy.php:153
msgid ""
"This taxonomy could not be registered because its key is in use by another "
"taxonomy registered by another plugin or theme."
msgstr ""
"Diese Taxonomie konnte nicht registriert werden, da der Schlüssel von einer "
"anderen Taxonomie, die von einem anderen Plugin oder Theme registriert "
"wurde, genutzt wird."

#. translators: %s number of taxonomies synchronized
#: includes/admin/post-types/admin-taxonomies.php:333
msgid "Taxonomy synchronized."
msgid_plural "%s taxonomies synchronized."
msgstr[0] "Die Taxonomie wurde synchronisiert."
msgstr[1] "%s Taxonomien wurden synchronisiert."

#. translators: %s number of taxonomies duplicated
#: includes/admin/post-types/admin-taxonomies.php:326
msgid "Taxonomy duplicated."
msgid_plural "%s taxonomies duplicated."
msgstr[0] "Die Taxonomie wurde dupliziert."
msgstr[1] "%s Taxonomien wurden dupliziert."

#. translators: %s number of taxonomies deactivated
#: includes/admin/post-types/admin-taxonomies.php:319
msgid "Taxonomy deactivated."
msgid_plural "%s taxonomies deactivated."
msgstr[0] "Die Taxonomie wurde deaktiviert."
msgstr[1] "%s Taxonomien wurden deaktiviert."

#. translators: %s number of taxonomies activated
#: includes/admin/post-types/admin-taxonomies.php:312
msgid "Taxonomy activated."
msgid_plural "%s taxonomies activated."
msgstr[0] "Die Taxonomie wurde aktiviert."
msgstr[1] "%s Taxonomien wurden aktiviert."

#: includes/admin/post-types/admin-taxonomies.php:113
msgid "Terms"
msgstr "Begriffe"

#. translators: %s number of post types synchronized
#: includes/admin/post-types/admin-post-types.php:327
msgid "Post type synchronized."
msgid_plural "%s post types synchronized."
msgstr[0] "Der Inhaltstyp wurde synchronisiert."
msgstr[1] "%s Inhaltstypen wurden synchronisiert."

#. translators: %s number of post types duplicated
#: includes/admin/post-types/admin-post-types.php:320
msgid "Post type duplicated."
msgid_plural "%s post types duplicated."
msgstr[0] "Der Inhaltstyp wurde dupliziert."
msgstr[1] "%s Inhaltstypen wurden dupliziert."

#. translators: %s number of post types deactivated
#: includes/admin/post-types/admin-post-types.php:313
msgid "Post type deactivated."
msgid_plural "%s post types deactivated."
msgstr[0] "Der Inhaltstyp wurde deaktiviert."
msgstr[1] "%s Inhaltstypen wurden deaktiviert."

#. translators: %s number of post types activated
#: includes/admin/post-types/admin-post-types.php:306
msgid "Post type activated."
msgid_plural "%s post types activated."
msgstr[0] "Der Inhaltstyp wurde aktiviert."
msgstr[1] "%s Inhaltstypen wurden aktiviert."

#: includes/admin/post-types/admin-post-types.php:87
#: includes/admin/post-types/admin-taxonomies.php:111
#: includes/admin/tools/class-acf-admin-tool-import.php:79
#: includes/admin/views/acf-taxonomy/basic-settings.php:82
#: includes/post-types/class-acf-post-type.php:91
msgid "Post Types"
msgstr "Inhaltstypen"

#: includes/admin/post-types/admin-post-type.php:158
#: includes/admin/post-types/admin-taxonomy.php:160
msgid "Advanced Settings"
msgstr "Erweiterte Einstellungen"

#: includes/admin/post-types/admin-post-type.php:157
#: includes/admin/post-types/admin-taxonomy.php:159
msgid "Basic Settings"
msgstr "Grundlegende Einstellungen"

#: includes/admin/post-types/admin-post-type.php:151
#: includes/admin/post-types/admin-post-types.php:345
msgid ""
"This post type could not be registered because its key is in use by another "
"post type registered by another plugin or theme."
msgstr ""
"Dieser Inhaltstyp konnte nicht registriert werden, da dessen Schlüssel von "
"einem anderen Inhaltstyp, der von einem anderen Plugin oder Theme "
"registriert wurde, genutzt wird."

#: includes/admin/post-types/admin-post-type.php:126
#: assets/build/js/acf-internal-post-type.js:176
#: assets/build/js/acf-internal-post-type.js:250
msgid "Pages"
msgstr "Seiten"

#: includes/admin/admin-internal-post-type.php:347
msgid "Link Existing Field Groups"
msgstr "Vorhandene Feldgruppen verknüpfen"

#. translators: %s post type name
#: includes/admin/post-types/admin-post-type.php:80
msgid "%s post type created"
msgstr "Der Inhaltstyp %s wurde erstellt"

#. translators: %s taxonomy name
#: includes/admin/post-types/admin-taxonomy.php:78
msgid "Add fields to %s"
msgstr "Felder zu %s hinzufügen"

#. translators: %s post type name
#: includes/admin/post-types/admin-post-type.php:76
msgid "%s post type updated"
msgstr "Der Inhaltstyp %s wurde aktualisiert"

#: includes/admin/post-types/admin-post-type.php:56
msgid "Post type draft updated."
msgstr "Der Inhaltstyp-Entwurf wurde aktualisiert."

#: includes/admin/post-types/admin-post-type.php:55
msgid "Post type scheduled for."
msgstr "Der Inhaltstyp wurde geplant für."

#: includes/admin/post-types/admin-post-type.php:54
msgid "Post type submitted."
msgstr "Der Inhaltstyp wurde übermittelt."

#: includes/admin/post-types/admin-post-type.php:53
msgid "Post type saved."
msgstr "Der Inhaltstyp wurde gespeichert."

#: includes/admin/post-types/admin-post-type.php:50
msgid "Post type updated."
msgstr "Der Inhaltstyp wurde aktualisiert."

#: includes/admin/post-types/admin-post-type.php:49
msgid "Post type deleted."
msgstr "Der Inhaltstyp wurde gelöscht."

#: includes/admin/post-types/admin-field-group.php:146
#: assets/build/js/acf-field-group.js:1159
#: assets/build/js/acf-field-group.js:1383
msgid "Type to search..."
msgstr "Tippen, um zu suchen …"

#: includes/admin/post-types/admin-field-group.php:101
#: assets/build/js/acf-field-group.js:1186
#: assets/build/js/acf-field-group.js:2349
#: assets/build/js/acf-field-group.js:1429
#: assets/build/js/acf-field-group.js:2761
msgid "PRO Only"
msgstr "Nur Pro"

#: includes/admin/post-types/admin-field-group.php:93
#: assets/build/js/acf-internal-post-type.js:308
#: assets/build/js/acf-internal-post-type.js:417
msgid "Field groups linked successfully."
msgstr "Die Feldgruppen wurden erfolgreich verlinkt."

#. translators: %s - URL to ACF tools page.
#: includes/admin/admin.php:199
msgid ""
"Import Post Types and Taxonomies registered with Custom Post Type UI and "
"manage them with ACF. <a href=\"%s\">Get Started</a>."
msgstr ""
"Importiere Inhaltstypen und Taxonomien, die mit Custom Post Type UI "
"registriert wurden, und verwalte sie mit ACF. <a href=\"%s\">Jetzt starten</"
"a>."

#: includes/admin/admin.php:46 includes/admin/admin.php:352
#: includes/class-acf-site-health.php:250
msgid "ACF"
msgstr "ACF"

#: includes/admin/admin-internal-post-type.php:314
msgid "taxonomy"
msgstr "Taxonomie"

#: includes/admin/admin-internal-post-type.php:314
msgid "post type"
msgstr "Inhaltstyp"

#: includes/admin/admin-internal-post-type.php:338
msgid "Done"
msgstr "Erledigt"

#: includes/admin/admin-internal-post-type.php:324
msgid "Field Group(s)"
msgstr "Feldgruppe(n)"

#: includes/admin/admin-internal-post-type.php:323
msgid "Select one or many field groups..."
msgstr "Wähle eine Feldgruppe oder mehrere ..."

#: includes/admin/admin-internal-post-type.php:322
msgid "Please select the field groups to link."
msgstr "Bitte wähle die Feldgruppe zum Verlinken aus."

#: includes/admin/admin-internal-post-type.php:280
msgid "Field group linked successfully."
msgid_plural "Field groups linked successfully."
msgstr[0] "Die Feldgruppe wurde erfolgreich verlinkt."
msgstr[1] "Die Feldgruppen wurden erfolgreich verlinkt."

#: includes/admin/admin-internal-post-type-list.php:277
#: includes/admin/post-types/admin-post-types.php:346
#: includes/admin/post-types/admin-taxonomies.php:352
msgctxt "post status"
msgid "Registration Failed"
msgstr "Die Registrierung ist fehlgeschlagen"

#: includes/admin/admin-internal-post-type-list.php:276
msgid ""
"This item could not be registered because its key is in use by another item "
"registered by another plugin or theme."
msgstr ""
"Dieses Element konnte nicht registriert werden, da dessen Schlüssel von "
"einem anderen Element, das von einem anderen Plugin oder Theme registriert "
"wurde, genutzt wird."

#: includes/acf-internal-post-type-functions.php:509
#: includes/acf-internal-post-type-functions.php:538
msgid "REST API"
msgstr "REST-API"

#: includes/acf-internal-post-type-functions.php:508
#: includes/acf-internal-post-type-functions.php:537
#: includes/acf-internal-post-type-functions.php:564
msgid "Permissions"
msgstr "Berechtigungen"

#: includes/acf-internal-post-type-functions.php:507
#: includes/acf-internal-post-type-functions.php:536
msgid "URLs"
msgstr "URLs"

#: includes/acf-internal-post-type-functions.php:506
#: includes/acf-internal-post-type-functions.php:535
#: includes/acf-internal-post-type-functions.php:562
msgid "Visibility"
msgstr "Sichtbarkeit"

#: includes/acf-internal-post-type-functions.php:505
#: includes/acf-internal-post-type-functions.php:534
#: includes/acf-internal-post-type-functions.php:563
msgid "Labels"
msgstr "Beschriftungen"

#: includes/admin/post-types/admin-field-group.php:279
msgid "Field Settings Tabs"
msgstr "Tabs für Feldeinstellungen"

#. Author URI of the plugin
#: acf.php
msgid ""
"https://wpengine.com/?utm_source=wordpress."
"org&utm_medium=referral&utm_campaign=plugin_directory&utm_content=advanced_custom_fields"
msgstr ""
"https://wpengine.com/?utm_source=wordpress."
"org&utm_medium=referral&utm_campaign=plugin_directory&utm_content=advanced_custom_fields"

#: includes/api/api-template.php:1015
msgid "[ACF shortcode value disabled for preview]"
msgstr "[Die Vorschau des ACF-Shortcodes wurde deaktiviert]"

#: includes/admin/admin-internal-post-type.php:290
#: includes/admin/post-types/admin-field-group.php:572
msgid "Close Modal"
msgstr "Modal schließen"

#: includes/admin/post-types/admin-field-group.php:92
#: assets/build/js/acf-field-group.js:1701
#: assets/build/js/acf-field-group.js:2032
msgid "Field moved to other group"
msgstr "Das Feld wurde zu einer anderen Gruppe verschoben"

#: includes/admin/post-types/admin-field-group.php:91
#: assets/build/js/acf.js:1443 assets/build/js/acf.js:1521
msgid "Close modal"
msgstr "Modal schließen"

#: includes/fields/class-acf-field-tab.php:119
msgid "Start a new group of tabs at this tab."
msgstr "Eine neue Gruppe von Tabs in diesem Tab beginnen."

#: includes/fields/class-acf-field-tab.php:118
msgid "New Tab Group"
msgstr "Neue Tab-Gruppe"

#: includes/fields/class-acf-field-select.php:423
#: includes/fields/class-acf-field-true_false.php:188
msgid "Use a stylized checkbox using select2"
msgstr "Ein stylisches Auswahlkästchen mit select2 verwenden"

#: includes/fields/class-acf-field-radio.php:250
msgid "Save Other Choice"
msgstr "Eine andere Auswahlmöglichkeit speichern"

#: includes/fields/class-acf-field-radio.php:239
msgid "Allow Other Choice"
msgstr "Eine andere Auswahlmöglichkeit erlauben"

#: includes/fields/class-acf-field-checkbox.php:420
msgid "Add Toggle All"
msgstr "„Alles umschalten“ hinzufügen"

#: includes/fields/class-acf-field-checkbox.php:379
msgid "Save Custom Values"
msgstr "Individuelle Werte speichern"

#: includes/fields/class-acf-field-checkbox.php:368
msgid "Allow Custom Values"
msgstr "Individuelle Werte zulassen"

#: includes/fields/class-acf-field-checkbox.php:134
msgid "Checkbox custom values cannot be empty. Uncheck any empty values."
msgstr ""
"Individuelle Werte von Auswahlkästchen dürfen nicht leer sein. Deaktiviere "
"alle leeren Werte."

#: includes/admin/views/global/navigation.php:253
msgid "Updates"
msgstr "Aktualisierungen"

#: includes/admin/views/global/navigation.php:177
#: includes/admin/views/global/navigation.php:181
msgid "Advanced Custom Fields logo"
msgstr "Advanced-Custom-Fields-Logo"

#: includes/admin/views/global/form-top.php:92
msgid "Save Changes"
msgstr "Änderungen speichern"

#: includes/admin/views/global/form-top.php:79
msgid "Field Group Title"
msgstr "Feldgruppen-Titel"

#: includes/admin/views/acf-post-type/advanced-settings.php:709
#: includes/admin/views/global/form-top.php:3
msgid "Add title"
msgstr "Titel hinzufügen"

#. translators: %s url to getting started guide
#: includes/admin/views/acf-field-group/list-empty.php:30
#: includes/admin/views/acf-post-type/list-empty.php:20
#: includes/admin/views/acf-taxonomy/list-empty.php:21
#: includes/admin/views/options-page-preview.php:13
msgid ""
"New to ACF? Take a look at our <a href=\"%s\" target=\"_blank\">getting "
"started guide</a>."
msgstr ""
"Neu bei ACF? Wirf einen Blick auf <a href=\"%s\" target=\"_blank\">die "
"Anleitung zum Starten (engl.)</a>."

#: includes/admin/views/acf-field-group/list-empty.php:24
msgid "Add Field Group"
msgstr "Feldgruppe hinzufügen"

#. translators: %s url to creating a field group page
#: includes/admin/views/acf-field-group/list-empty.php:18
msgid ""
"ACF uses <a href=\"%s\" target=\"_blank\">field groups</a> to group custom "
"fields together, and then attach those fields to edit screens."
msgstr ""
"ACF verwendet <a href=\"%s\" target=\"_blank\">Feldgruppen</a>, um "
"individuelle Felder zu gruppieren und diese dann in Bearbeitungsansichten "
"anzuhängen."

#: includes/admin/views/acf-field-group/list-empty.php:12
msgid "Add Your First Field Group"
msgstr "Deine erste Feldgruppe hinzufügen"

#: includes/admin/admin-options-pages-preview.php:28
#: includes/admin/views/acf-field-group/pro-features.php:58
#: includes/admin/views/global/navigation.php:86
#: includes/admin/views/global/navigation.php:255
msgid "Options Pages"
msgstr "Optionen-Seiten"

#: includes/admin/views/acf-field-group/pro-features.php:54
msgid "ACF Blocks"
msgstr "ACF-Blöcke"

#: includes/admin/views/acf-field-group/pro-features.php:62
msgid "Gallery Field"
msgstr "Galerie-Feld"

#: includes/admin/views/acf-field-group/pro-features.php:42
msgid "Flexible Content Field"
msgstr "Feld „Flexibler Inhalt“"

#: includes/admin/views/acf-field-group/pro-features.php:46
msgid "Repeater Field"
msgstr "Wiederholungs-Feld"

#: includes/admin/views/global/navigation.php:215
msgid "Unlock Extra Features with ACF PRO"
msgstr "Zusatzfunktionen mit ACF PRO freischalten"

#: includes/admin/views/acf-field-group/options.php:267
msgid "Delete Field Group"
msgstr "Feldgruppe löschen"

#. translators: 1: Post creation date 2: Post creation time
#: includes/admin/views/acf-field-group/options.php:261
msgid "Created on %1$s at %2$s"
msgstr "Erstellt am %1$s um %2$s"

#: includes/acf-field-group-functions.php:497
msgid "Group Settings"
msgstr "Gruppeneinstellungen"

#: includes/acf-field-group-functions.php:495
msgid "Location Rules"
msgstr "Regeln für die Position"

#. translators: %s url to field types list
#: includes/admin/views/acf-field-group/fields.php:73
msgid ""
"Choose from over 30 field types. <a href=\"%s\" target=\"_blank\">Learn "
"more</a>."
msgstr ""
"Wähle aus mehr als 30 Feldtypen. <a href=\"%s\" target=\"_blank\">Mehr "
"erfahren (engl.)</a>."

#: includes/admin/views/acf-field-group/fields.php:65
msgid ""
"Get started creating new custom fields for your posts, pages, custom post "
"types and other WordPress content."
msgstr ""
"Beginne mit der Erstellung neuer individueller Felder für deine Beiträge, "
"Seiten, individuellen Inhaltstypen und sonstigen WordPress-Inhalten."

#: includes/admin/views/acf-field-group/fields.php:64
msgid "Add Your First Field"
msgstr "Füge dein erstes Feld hinzu"

#. translators: A symbol (or text, if not available in your locale) meaning
#. "Order Number", in terms of positional placement.
#: includes/admin/views/acf-field-group/fields.php:43
msgid "#"
msgstr "#"

#: includes/admin/views/acf-field-group/fields.php:33
#: includes/admin/views/acf-field-group/fields.php:67
#: includes/admin/views/acf-field-group/fields.php:101
#: includes/admin/views/global/form-top.php:88
msgid "Add Field"
msgstr "Feld hinzufügen"

#: includes/acf-field-group-functions.php:496 includes/fields.php:384
msgid "Presentation"
msgstr "Präsentation"

#: includes/fields.php:383
msgid "Validation"
msgstr "Validierung"

#: includes/acf-internal-post-type-functions.php:504
#: includes/acf-internal-post-type-functions.php:533 includes/fields.php:382
msgid "General"
msgstr "Allgemein"

#: includes/admin/tools/class-acf-admin-tool-import.php:67
msgid "Import JSON"
msgstr "JSON importieren"

#: includes/admin/tools/class-acf-admin-tool-export.php:338
msgid "Export As JSON"
msgstr "Als JSON exportieren"

#. translators: %s number of field groups deactivated
#: includes/admin/post-types/admin-field-groups.php:360
msgid "Field group deactivated."
msgid_plural "%s field groups deactivated."
msgstr[0] "Die Feldgruppe wurde deaktiviert."
msgstr[1] "%s Feldgruppen wurden deaktiviert."

#. translators: %s number of field groups activated
#: includes/admin/post-types/admin-field-groups.php:353
msgid "Field group activated."
msgid_plural "%s field groups activated."
msgstr[0] "Die Feldgruppe wurde aktiviert."
msgstr[1] "%s Feldgruppen wurden aktiviert."

#: includes/admin/admin-internal-post-type-list.php:470
#: includes/admin/admin-internal-post-type-list.php:496
msgid "Deactivate"
msgstr "Deaktivieren"

#: includes/admin/admin-internal-post-type-list.php:470
msgid "Deactivate this item"
msgstr "Dieses Element deaktivieren"

#: includes/admin/admin-internal-post-type-list.php:466
#: includes/admin/admin-internal-post-type-list.php:495
msgid "Activate"
msgstr "Aktivieren"

#: includes/admin/admin-internal-post-type-list.php:466
msgid "Activate this item"
msgstr "Dieses Element aktivieren"

#: includes/admin/post-types/admin-field-group.php:88
#: assets/build/js/acf-field-group.js:2862
#: assets/build/js/acf-field-group.js:3375
msgid "Move field group to trash?"
msgstr "Soll die Feldgruppe in den Papierkorb verschoben werden?"

#: acf.php:500 includes/admin/admin-internal-post-type-list.php:264
#: includes/admin/post-types/admin-field-group.php:304
#: includes/admin/post-types/admin-post-type.php:282
#: includes/admin/post-types/admin-taxonomy.php:284
msgctxt "post status"
msgid "Inactive"
msgstr "Inaktiv"

#. Author of the plugin
#: acf.php
msgid "WP Engine"
msgstr "WP Engine"

#: acf.php:558
msgid ""
"Advanced Custom Fields and Advanced Custom Fields PRO should not be active "
"at the same time. We've automatically deactivated Advanced Custom Fields PRO."
msgstr ""
"Advanced Custom Fields und Advanced Custom Fields PRO sollten nicht "
"gleichzeitig aktiviert sein. Advanced Custom Fields PRO wurde automatisch "
"deaktiviert."

#: acf.php:556
msgid ""
"Advanced Custom Fields and Advanced Custom Fields PRO should not be active "
"at the same time. We've automatically deactivated Advanced Custom Fields."
msgstr ""
"Advanced Custom Fields und Advanced Custom Fields PRO sollten nicht "
"gleichzeitig aktiviert sein. Advanced Custom Fields wurde automatisch "
"deaktiviert."

#. translators: %1 plugin name, %2 the URL to the documentation on this error
#: includes/acf-value-functions.php:376
msgid ""
"<strong>%1$s</strong> - We've detected one or more calls to retrieve ACF "
"field values before ACF has been initialized. This is not supported and can "
"result in malformed or missing data. <a href=\"%2$s\" "
"target=\"_blank\">Learn how to fix this</a>."
msgstr ""
"<strong>%1$s</strong> – Es wurde mindestens ein Versuch festgestellt, ACF-"
"Feldwerte abzurufen, bevor ACF initialisiert wurde. Dies wird nicht "
"unterstützt und kann zu fehlerhaften oder fehlenden Daten führen. <a "
"href=\"%2$s\" target=\"_blank\">Lerne, wie du das beheben kannst (engl.)</a>."

#: includes/fields/class-acf-field-user.php:578
msgid "%1$s must have a user with the %2$s role."
msgid_plural "%1$s must have a user with one of the following roles: %2$s"
msgstr[0] "%1$s muss einen Benutzer mit der %2$s-Rolle haben."
msgstr[1] "%1$s muss einen Benutzer mit einer der folgenden rollen haben: %2$s"

#: includes/fields/class-acf-field-user.php:569
msgid "%1$s must have a valid user ID."
msgstr "%1$s muss eine gültige Benutzer-ID haben."

#: includes/fields/class-acf-field-user.php:408
msgid "Invalid request."
msgstr "Ungültige Anfrage."

#: includes/fields/class-acf-field-select.php:637
msgid "%1$s is not one of %2$s"
msgstr "%1$s ist nicht eins von %2$s"

#: includes/fields/class-acf-field-post_object.php:649
msgid "%1$s must have term %2$s."
msgid_plural "%1$s must have one of the following terms: %2$s"
msgstr[0] "%1$s muss den Begriff %2$s haben."
msgstr[1] "%1$s muss einen der folgenden Begriffe haben: %2$s"

#: includes/fields/class-acf-field-post_object.php:633
msgid "%1$s must be of post type %2$s."
msgid_plural "%1$s must be of one of the following post types: %2$s"
msgstr[0] "%1$s muss vom Inhaltstyp %2$s sein."
msgstr[1] "%1$s muss einer der folgenden Inhaltstypen sein: %2$s"

#: includes/fields/class-acf-field-post_object.php:624
msgid "%1$s must have a valid post ID."
msgstr "%1$s muss eine gültige Beitrags-ID haben."

#: includes/fields/class-acf-field-file.php:447
msgid "%s requires a valid attachment ID."
msgstr "%s erfordert eine gültige Anhangs-ID."

#: includes/admin/views/acf-field-group/options.php:233
msgid "Show in REST API"
msgstr "Im REST-API anzeigen"

#: includes/fields/class-acf-field-color_picker.php:156
msgid "Enable Transparency"
msgstr "Transparenz aktivieren"

#: includes/fields/class-acf-field-color_picker.php:175
msgid "RGBA Array"
msgstr "RGBA-Array"

#: includes/fields/class-acf-field-color_picker.php:92
msgid "RGBA String"
msgstr "RGBA-Zeichenfolge"

#: includes/fields/class-acf-field-color_picker.php:91
#: includes/fields/class-acf-field-color_picker.php:174
msgid "Hex String"
msgstr "Hex-Zeichenfolge"

#: includes/admin/views/browse-fields-modal.php:12
msgid "Upgrade to PRO"
msgstr "Upgrade auf PRO"

#: includes/admin/post-types/admin-field-group.php:304
#: includes/admin/post-types/admin-post-type.php:282
#: includes/admin/post-types/admin-taxonomy.php:284
msgctxt "post status"
msgid "Active"
msgstr "Aktiv"

#: includes/fields/class-acf-field-email.php:166
msgid "'%s' is not a valid email address"
msgstr "‚%s‘ ist keine gültige E-Mail-Adresse"

#: includes/fields/class-acf-field-color_picker.php:70
msgid "Color value"
msgstr "Farbwert"

#: includes/fields/class-acf-field-color_picker.php:68
msgid "Select default color"
msgstr "Standardfarbe auswählen"

#: includes/fields/class-acf-field-color_picker.php:66
msgid "Clear color"
msgstr "Farbe entfernen"

#: includes/acf-wp-functions.php:90
msgid "Blocks"
msgstr "Blöcke"

#: includes/acf-wp-functions.php:86
msgid "Options"
msgstr "Optionen"

#: includes/acf-wp-functions.php:82
msgid "Users"
msgstr "Benutzer"

#: includes/acf-wp-functions.php:78
msgid "Menu items"
msgstr "Menüelemente"

#: includes/acf-wp-functions.php:70
msgid "Widgets"
msgstr "Widgets"

#: includes/acf-wp-functions.php:62
msgid "Attachments"
msgstr "Anhänge"

#: includes/acf-wp-functions.php:57
#: includes/admin/post-types/admin-post-types.php:112
#: includes/admin/post-types/admin-taxonomies.php:86
#: includes/admin/tools/class-acf-admin-tool-import.php:90
#: includes/admin/views/acf-post-type/basic-settings.php:86
#: includes/post-types/class-acf-taxonomy.php:90
#: includes/post-types/class-acf-taxonomy.php:91
msgid "Taxonomies"
msgstr "Taxonomien"

#: includes/acf-wp-functions.php:44
#: includes/admin/post-types/admin-post-type.php:124
#: includes/admin/post-types/admin-post-types.php:114
#: includes/admin/views/acf-post-type/advanced-settings.php:106
#: assets/build/js/acf-internal-post-type.js:173
#: assets/build/js/acf-internal-post-type.js:247
msgid "Posts"
msgstr "Beiträge"

#: includes/ajax/class-acf-ajax-local-json-diff.php:81
msgid "Last updated: %s"
msgstr "Zuletzt aktualisiert: %s"

#: includes/ajax/class-acf-ajax-local-json-diff.php:75
msgid "Sorry, this post is unavailable for diff comparison."
msgstr ""
"Leider steht diese Feldgruppe nicht für einen Diff-Vergleich zur Verfügung."

#: includes/ajax/class-acf-ajax-local-json-diff.php:47
msgid "Invalid field group parameter(s)."
msgstr "Ungültige(r) Feldgruppen-Parameter."

#: includes/admin/admin-internal-post-type-list.php:429
msgid "Awaiting save"
msgstr "Ein Speichern wird erwartet"

#: includes/admin/admin-internal-post-type-list.php:426
msgid "Saved"
msgstr "Gespeichert"

#: includes/admin/admin-internal-post-type-list.php:422
#: includes/admin/tools/class-acf-admin-tool-import.php:46
msgid "Import"
msgstr "Importieren"

#: includes/admin/admin-internal-post-type-list.php:418
msgid "Review changes"
msgstr "Änderungen überprüfen"

#: includes/admin/admin-internal-post-type-list.php:394
msgid "Located in: %s"
msgstr "Ist zu finden in: %s"

#: includes/admin/admin-internal-post-type-list.php:391
msgid "Located in plugin: %s"
msgstr "Liegt im Plugin: %s"

#: includes/admin/admin-internal-post-type-list.php:388
msgid "Located in theme: %s"
msgstr "Liegt im Theme: %s"

#: includes/admin/post-types/admin-field-groups.php:235
msgid "Various"
msgstr "Verschiedene"

#: includes/admin/admin-internal-post-type-list.php:230
#: includes/admin/admin-internal-post-type-list.php:503
msgid "Sync changes"
msgstr "Änderungen synchronisieren"

#: includes/admin/admin-internal-post-type-list.php:229
msgid "Loading diff"
msgstr "Diff laden"

#: includes/admin/admin-internal-post-type-list.php:228
msgid "Review local JSON changes"
msgstr "Lokale JSON-Änderungen überprüfen"

#: includes/admin/admin.php:174
msgid "Visit website"
msgstr "Website besuchen"

#: includes/admin/admin.php:173
msgid "View details"
msgstr "Details anzeigen"

#: includes/admin/admin.php:172
msgid "Version %s"
msgstr "Version %s"

#: includes/admin/admin.php:171
msgid "Information"
msgstr "Information"

#: includes/admin/admin.php:162
msgid ""
"<a href=\"%s\" target=\"_blank\">Help Desk</a>. The support professionals on "
"our Help Desk will assist with your more in depth, technical challenges."
msgstr ""
"<a href=\"%s\" target=\"_blank\">Help-Desk</a>. Die Support-Experten unseres "
"Help-Desks werden dir bei komplexeren technischen Herausforderungen "
"unterstützend zur Seite stehen."

#: includes/admin/admin.php:158
msgid ""
"<a href=\"%s\" target=\"_blank\">Discussions</a>. We have an active and "
"friendly community on our Community Forums who may be able to help you "
"figure out the 'how-tos' of the ACF world."
msgstr ""
"<a href=\"%s\" target=\"_blank\">Diskussionen</a>. Wir haben eine aktive und "
"freundliche Community in unseren Community-Foren, die dir vielleicht dabei "
"helfen kann, dich mit den „How-tos“ der ACF-Welt vertraut zu machen."

#: includes/admin/admin.php:154
msgid ""
"<a href=\"%s\" target=\"_blank\">Documentation</a>. Our extensive "
"documentation contains references and guides for most situations you may "
"encounter."
msgstr ""
"<a href=\"%s\" target=\"_blank\">Dokumentation (engl.)</a>. Diese umfassende "
"Dokumentation beinhaltet Referenzen und Leitfäden zu den meisten "
"Situationen, die du vorfinden wirst."

#: includes/admin/admin.php:151
msgid ""
"We are fanatical about support, and want you to get the best out of your "
"website with ACF. If you run into any difficulties, there are several places "
"you can find help:"
msgstr ""
"Wir legen großen Wert auf Support und möchten, dass du mit ACF das Beste aus "
"deiner Website herausholst. Wenn du auf Schwierigkeiten stößt, gibt es "
"mehrere Stellen, an denen du Hilfe finden kannst:"

#: includes/admin/admin.php:148 includes/admin/admin.php:150
msgid "Help & Support"
msgstr "Hilfe und Support"

#: includes/admin/admin.php:139
msgid ""
"Please use the Help & Support tab to get in touch should you find yourself "
"requiring assistance."
msgstr ""
"Falls du Hilfe benötigst, nutze bitte den Tab „Hilfe und Support“, um dich "
"mit uns in Verbindung zu setzen."

#: includes/admin/admin.php:136
msgid ""
"Before creating your first Field Group, we recommend first reading our <a "
"href=\"%s\" target=\"_blank\">Getting started</a> guide to familiarize "
"yourself with the plugin's philosophy and best practises."
msgstr ""
"Bevor du deine erste Feldgruppe erstellst, wird empfohlen, vorab die "
"Anleitung <a href=\"%s\" target=\"_blank\">Erste Schritte (engl.)</a> "
"durchzulesen, um dich mit der Philosophie hinter dem Plugin und den besten "
"Praktiken vertraut zu machen."

#: includes/admin/admin.php:134
msgid ""
"The Advanced Custom Fields plugin provides a visual form builder to "
"customize WordPress edit screens with extra fields, and an intuitive API to "
"display custom field values in any theme template file."
msgstr ""
"Das Advanced-Custom-Fields-Plugin bietet einen visuellen Formular-Builder, "
"um WordPress-Bearbeitungsansichten mit weiteren Feldern zu individualisieren "
"und eine intuitive API, um individuelle Feldwerte in beliebigen Theme-"
"Template-Dateien anzuzeigen."

#: includes/admin/admin.php:131 includes/admin/admin.php:133
msgid "Overview"
msgstr "Übersicht"

#. translators: %s the name of the location type
#: includes/locations.php:38
msgid "Location type \"%s\" is already registered."
msgstr "Positions-Typ „%s“ ist bereits registriert."

#. translators: %s class name for a location that could not be found
#: includes/locations.php:26
msgid "Class \"%s\" does not exist."
msgstr "Die Klasse „%s“ existiert nicht."

#: includes/ajax/class-acf-ajax-query-users.php:28
#: includes/ajax/class-acf-ajax.php:157
msgid "Invalid nonce."
msgstr "Der Nonce ist ungültig."

#: includes/fields/class-acf-field-user.php:400
msgid "Error loading field."
msgstr "Fehler beim Laden des Felds."

#: assets/build/js/acf-input.js:3438 assets/build/js/acf-input.js:3507
#: assets/build/js/acf-input.js:3686 assets/build/js/acf-input.js:3760
msgid "Location not found: %s"
msgstr "Die Position wurde nicht gefunden: %s"

#: includes/forms/form-user.php:328
msgid "<strong>Error</strong>: %s"
msgstr "<strong>Fehler</strong>: %s"

#: includes/locations/class-acf-location-widget.php:22
msgid "Widget"
msgstr "Widget"

#: includes/locations/class-acf-location-user-role.php:24
msgid "User Role"
msgstr "Benutzerrolle"

#: includes/locations/class-acf-location-comment.php:22
msgid "Comment"
msgstr "Kommentar"

#: includes/locations/class-acf-location-post-format.php:22
msgid "Post Format"
msgstr "Beitragsformat"

#: includes/locations/class-acf-location-nav-menu-item.php:22
msgid "Menu Item"
msgstr "Menüelement"

#: includes/locations/class-acf-location-post-status.php:22
msgid "Post Status"
msgstr "Beitragsstatus"

#: includes/acf-wp-functions.php:74
#: includes/locations/class-acf-location-nav-menu.php:89
msgid "Menus"
msgstr "Menüs"

#: includes/locations/class-acf-location-nav-menu.php:80
msgid "Menu Locations"
msgstr "Menüpositionen"

#: includes/locations/class-acf-location-nav-menu.php:22
msgid "Menu"
msgstr "Menü"

#: includes/locations/class-acf-location-post-taxonomy.php:22
msgid "Post Taxonomy"
msgstr "Beitrags-Taxonomie"

#: includes/locations/class-acf-location-page-type.php:114
msgid "Child Page (has parent)"
msgstr "Unterseite (hat übergeordnete Seite)"

#: includes/locations/class-acf-location-page-type.php:113
msgid "Parent Page (has children)"
msgstr "Übergeordnete Seite (hat Unterseiten)"

#: includes/locations/class-acf-location-page-type.php:112
msgid "Top Level Page (no parent)"
msgstr "Seite der ersten Ebene (keine übergeordnete)"

#: includes/locations/class-acf-location-page-type.php:111
msgid "Posts Page"
msgstr "Beitrags-Seite"

#: includes/locations/class-acf-location-page-type.php:110
msgid "Front Page"
msgstr "Startseite"

#: includes/locations/class-acf-location-page-type.php:22
msgid "Page Type"
msgstr "Seitentyp"

#: includes/locations/class-acf-location-current-user.php:73
msgid "Viewing back end"
msgstr "Backend anzeigen"

#: includes/locations/class-acf-location-current-user.php:72
msgid "Viewing front end"
msgstr "Frontend anzeigen"

#: includes/locations/class-acf-location-current-user.php:71
msgid "Logged in"
msgstr "Angemeldet"

#: includes/locations/class-acf-location-current-user.php:22
msgid "Current User"
msgstr "Aktueller Benutzer"

#: includes/locations/class-acf-location-page-template.php:22
msgid "Page Template"
msgstr "Seiten-Template"

#: includes/locations/class-acf-location-user-form.php:74
msgid "Register"
msgstr "Registrieren"

#: includes/locations/class-acf-location-user-form.php:73
msgid "Add / Edit"
msgstr "Hinzufügen/bearbeiten"

#: includes/locations/class-acf-location-user-form.php:22
msgid "User Form"
msgstr "Benutzerformular"

#: includes/locations/class-acf-location-page-parent.php:22
msgid "Page Parent"
msgstr "Übergeordnete Seite"

#: includes/locations/class-acf-location-current-user-role.php:77
msgid "Super Admin"
msgstr "Super-Administrator"

#: includes/locations/class-acf-location-current-user-role.php:22
msgid "Current User Role"
msgstr "Aktuelle Benutzerrolle"

#: includes/locations/class-acf-location-page-template.php:73
#: includes/locations/class-acf-location-post-template.php:85
msgid "Default Template"
msgstr "Standard-Template"

#: includes/locations/class-acf-location-post-template.php:22
msgid "Post Template"
msgstr "Beitrags-Template"

#: includes/locations/class-acf-location-post-category.php:22
msgid "Post Category"
msgstr "Beitragskategorie"

#: includes/locations/class-acf-location-attachment.php:84
msgid "All %s formats"
msgstr "Alle %s Formate"

#: includes/locations/class-acf-location-attachment.php:22
msgid "Attachment"
msgstr "Anhang"

#: includes/validation.php:313
msgid "%s value is required"
msgstr "%s Wert ist erforderlich"

#: includes/admin/views/acf-field-group/conditional-logic.php:64
msgid "Show this field if"
msgstr "Dieses Feld anzeigen, falls"

#: includes/admin/views/acf-field-group/conditional-logic.php:25
#: includes/admin/views/acf-field-group/field.php:122 includes/fields.php:385
msgid "Conditional Logic"
msgstr "Bedingte Logik"

#: includes/admin/views/acf-field-group/conditional-logic.php:169
#: includes/admin/views/acf-field-group/location-rule.php:84
msgid "and"
msgstr "und"

#: includes/admin/post-types/admin-field-groups.php:97
#: includes/admin/post-types/admin-post-types.php:118
#: includes/admin/post-types/admin-taxonomies.php:117
msgid "Local JSON"
msgstr "Lokales JSON"

#: includes/admin/views/acf-field-group/pro-features.php:50
msgid "Clone Field"
msgstr "Feld duplizieren"

#. translators: %s a list of plugin
#: includes/admin/views/upgrade/notice.php:32
msgid ""
"Please also check all premium add-ons (%s) are updated to the latest version."
msgstr ""
"Stelle bitte ebenfalls sicher, dass alle Premium-Add-ons (%s) auf die "
"neueste Version aktualisiert wurden."

#: includes/admin/views/upgrade/notice.php:29
msgid ""
"This version contains improvements to your database and requires an upgrade."
msgstr ""
"Diese Version enthält Verbesserungen für deine Datenbank und erfordert ein "
"Upgrade."

#. translators: %1 plugin name, %2 version number
#: includes/admin/views/upgrade/notice.php:28
msgid "Thank you for updating to %1$s v%2$s!"
msgstr "Danke für die Aktualisierung auf %1$s v%2$s!"

#: includes/admin/views/upgrade/notice.php:26
msgid "Database Upgrade Required"
msgstr "Ein Upgrade der Datenbank ist erforderlich"

#: includes/admin/post-types/admin-field-group.php:159
#: includes/admin/views/upgrade/notice.php:17
msgid "Options Page"
msgstr "Optionen-Seite"

#: includes/admin/views/upgrade/notice.php:14 includes/fields.php:436
msgid "Gallery"
msgstr "Galerie"

#: includes/admin/views/upgrade/notice.php:11 includes/fields.php:426
msgid "Flexible Content"
msgstr "Flexibler Inhalt"

#: includes/admin/views/upgrade/notice.php:8 includes/fields.php:446
msgid "Repeater"
msgstr "Wiederholung"

#: includes/admin/views/tools/tools.php:16
msgid "Back to all tools"
msgstr "Zurück zur Werkzeugübersicht"

#: includes/admin/views/acf-field-group/options.php:195
msgid ""
"If multiple field groups appear on an edit screen, the first field group's "
"options will be used (the one with the lowest order number)"
msgstr ""
"Werden in der Bearbeitungsansicht mehrere Feldgruppen angezeigt, werden die "
"Optionen der ersten Feldgruppe verwendet (die mit der niedrigsten "
"Sortierungs-Nummer)"

#: includes/admin/views/acf-field-group/options.php:195
msgid "<b>Select</b> items to <b>hide</b> them from the edit screen."
msgstr ""
"Die Elemente <b>auswählen</b>, die in der Bearbeitungsansicht <b>verborgen</"
"b> werden sollen."

#: includes/admin/views/acf-field-group/options.php:194
msgid "Hide on screen"
msgstr "In der Ansicht ausblenden"

#: includes/admin/views/acf-field-group/options.php:186
msgid "Send Trackbacks"
msgstr "Trackbacks senden"

#: includes/admin/post-types/admin-taxonomy.php:126
#: includes/admin/views/acf-field-group/options.php:185
#: includes/admin/views/acf-taxonomy/advanced-settings.php:159
#: assets/build/js/acf-internal-post-type.js:180
#: assets/build/js/acf-internal-post-type.js:254
msgid "Tags"
msgstr "Schlagwörter"

#: includes/admin/post-types/admin-taxonomy.php:128
#: includes/admin/views/acf-field-group/options.php:184
#: assets/build/js/acf-internal-post-type.js:183
#: assets/build/js/acf-internal-post-type.js:257
msgid "Categories"
msgstr "Kategorien"

#: includes/admin/views/acf-field-group/options.php:182
#: includes/admin/views/acf-post-type/advanced-settings.php:28
msgid "Page Attributes"
msgstr "Seiten-Attribute"

#: includes/admin/views/acf-field-group/options.php:181
msgid "Format"
msgstr "Format"

#: includes/admin/views/acf-field-group/options.php:180
#: includes/admin/views/acf-post-type/advanced-settings.php:22
msgid "Author"
msgstr "Autor"

#: includes/admin/views/acf-field-group/options.php:179
msgid "Slug"
msgstr "Titelform"

#: includes/admin/views/acf-field-group/options.php:178
#: includes/admin/views/acf-post-type/advanced-settings.php:27
msgid "Revisions"
msgstr "Revisionen"

#: includes/acf-wp-functions.php:66
#: includes/admin/views/acf-field-group/options.php:177
#: includes/admin/views/acf-post-type/advanced-settings.php:23
msgid "Comments"
msgstr "Kommentare"

#: includes/admin/views/acf-field-group/options.php:176
msgid "Discussion"
msgstr "Diskussion"

#: includes/admin/views/acf-field-group/options.php:174
#: includes/admin/views/acf-post-type/advanced-settings.php:26
msgid "Excerpt"
msgstr "Textauszug"

#: includes/admin/views/acf-field-group/options.php:173
msgid "Content Editor"
msgstr "Inhalts-Editor"

#: includes/admin/views/acf-field-group/options.php:172
msgid "Permalink"
msgstr "Permalink"

#: includes/admin/views/acf-field-group/options.php:250
msgid "Shown in field group list"
msgstr "Wird in der Feldgruppen-Liste angezeigt"

#: includes/admin/views/acf-field-group/options.php:157
msgid "Field groups with a lower order will appear first"
msgstr "Die Feldgruppen mit niedrigerer Ordnung werden zuerst angezeigt"

#: includes/admin/views/acf-field-group/options.php:156
msgid "Order No."
msgstr "Sortierungs-Nr."

#: includes/admin/views/acf-field-group/options.php:147
msgid "Below fields"
msgstr "Unterhalb der Felder"

#: includes/admin/views/acf-field-group/options.php:146
msgid "Below labels"
msgstr "Unterhalb der Beschriftungen"

#: includes/admin/views/acf-field-group/options.php:139
msgid "Instruction Placement"
msgstr "Platzierung der Anweisungen"

#: includes/admin/views/acf-field-group/options.php:122
msgid "Label Placement"
msgstr "Platzierung der Beschriftung"

#: includes/admin/views/acf-field-group/options.php:110
msgid "Side"
msgstr "Seite"

#: includes/admin/views/acf-field-group/options.php:109
msgid "Normal (after content)"
msgstr "Normal (nach Inhalt)"

#: includes/admin/views/acf-field-group/options.php:108
msgid "High (after title)"
msgstr "Hoch (nach dem Titel)"

#: includes/admin/views/acf-field-group/options.php:101
msgid "Position"
msgstr "Position"

#: includes/admin/views/acf-field-group/options.php:92
msgid "Seamless (no metabox)"
msgstr "Übergangslos (keine Metabox)"

#: includes/admin/views/acf-field-group/options.php:91
msgid "Standard (WP metabox)"
msgstr "Standard (WP-Metabox)"

#: includes/admin/views/acf-field-group/options.php:84
msgid "Style"
msgstr "Stil"

#: includes/admin/views/acf-field-group/fields.php:55
msgid "Type"
msgstr "Typ"

#: includes/admin/post-types/admin-field-groups.php:91
#: includes/admin/post-types/admin-post-types.php:111
#: includes/admin/post-types/admin-taxonomies.php:110
#: includes/admin/views/acf-field-group/fields.php:54
msgid "Key"
msgstr "Schlüssel"

#. translators: Hidden accessibility text for the positional order number of
#. the field.
#: includes/admin/views/acf-field-group/fields.php:48
msgid "Order"
msgstr "Reihenfolge"

#: includes/admin/views/acf-field-group/field.php:321
msgid "Close Field"
msgstr "Feld schließen"

#: includes/admin/views/acf-field-group/field.php:252
msgid "id"
msgstr "ID"

#: includes/admin/views/acf-field-group/field.php:236
msgid "class"
msgstr "Klasse"

#: includes/admin/views/acf-field-group/field.php:278
msgid "width"
msgstr "Breite"

#: includes/admin/views/acf-field-group/field.php:272
msgid "Wrapper Attributes"
msgstr "Wrapper-Attribute"

#: includes/fields/class-acf-field.php:311
msgid "Required"
msgstr "Erforderlich"

#: includes/admin/views/acf-field-group/field.php:219
msgid "Instructions"
msgstr "Anweisungen"

#: includes/admin/views/acf-field-group/field.php:142
msgid "Field Type"
msgstr "Feldtyp"

#: includes/admin/views/acf-field-group/field.php:183
msgid "Single word, no spaces. Underscores and dashes allowed"
msgstr ""
"Einzelnes Wort ohne Leerzeichen. Unterstriche und Bindestriche sind erlaubt"

#: includes/admin/views/acf-field-group/field.php:182
msgid "Field Name"
msgstr "Feldname"

#: includes/admin/views/acf-field-group/field.php:170
msgid "This is the name which will appear on the EDIT page"
msgstr "Dies ist der Name, der auf der BEARBEITUNGS-Seite erscheinen wird"

#: includes/admin/views/acf-field-group/field.php:169
#: includes/admin/views/browse-fields-modal.php:69
msgid "Field Label"
msgstr "Feldbeschriftung"

#: includes/admin/views/acf-field-group/field.php:94
msgid "Delete"
msgstr "Löschen"

#: includes/admin/views/acf-field-group/field.php:94
msgid "Delete field"
msgstr "Feld löschen"

#: includes/admin/views/acf-field-group/field.php:92
msgid "Move"
msgstr "Verschieben"

#: includes/admin/views/acf-field-group/field.php:92
msgid "Move field to another group"
msgstr "Feld in eine andere Gruppe verschieben"

#: includes/admin/views/acf-field-group/field.php:90
msgid "Duplicate field"
msgstr "Feld duplizieren"

#: includes/admin/views/acf-field-group/field.php:86
#: includes/admin/views/acf-field-group/field.php:89
msgid "Edit field"
msgstr "Feld bearbeiten"

#: includes/admin/views/acf-field-group/field.php:82
msgid "Drag to reorder"
msgstr "Ziehen zum Sortieren"

#: includes/admin/post-types/admin-field-group.php:99
#: includes/admin/views/acf-field-group/location-group.php:3
#: assets/build/js/acf-field-group.js:2387
#: assets/build/js/acf-field-group.js:2812
msgid "Show this field group if"
msgstr "Diese Feldgruppe anzeigen, falls"

#: includes/admin/views/upgrade/upgrade.php:93
#: includes/ajax/class-acf-ajax-upgrade.php:34
msgid "No updates available."
msgstr "Es sind keine Aktualisierungen verfügbar."

#. translators: %s the url to the field group page.
#: includes/admin/views/upgrade/upgrade.php:32
msgid "Database upgrade complete. <a href=\"%s\">See what's new</a>"
msgstr ""
"Das Datenbank-Upgrade wurde abgeschlossen. <a href=\"%s\">Schau nach was es "
"Neues gibt</a>"

#: includes/admin/views/upgrade/upgrade.php:27
msgid "Reading upgrade tasks..."
msgstr "Aufgaben für das Upgrade einlesen ..."

#: includes/admin/views/upgrade/network.php:165
#: includes/admin/views/upgrade/upgrade.php:64
msgid "Upgrade failed."
msgstr "Das Upgrade ist fehlgeschlagen."

#: includes/admin/views/upgrade/network.php:162
msgid "Upgrade complete."
msgstr "Das Upgrade wurde abgeschlossen."

#. translators: %s the version being upgraded to.
#. translators: %s the new ACF version
#: includes/admin/views/upgrade/network.php:148
#: includes/admin/views/upgrade/upgrade.php:29
msgid "Upgrading data to version %s"
msgstr "Das Upgrade der Daten auf Version %s wird ausgeführt"

#: includes/admin/views/upgrade/network.php:120
#: includes/admin/views/upgrade/notice.php:46
msgid ""
"It is strongly recommended that you backup your database before proceeding. "
"Are you sure you wish to run the updater now?"
msgstr ""
"Es wird dringend empfohlen, dass du deine Datenbank sicherst, bevor du "
"fortfährst. Bist du sicher, dass du die Aktualisierung jetzt durchführen "
"möchtest?"

#: includes/admin/views/upgrade/network.php:116
msgid "Please select at least one site to upgrade."
msgstr "Bitte mindestens eine Website für das Upgrade auswählen."

#. translators: %s admin dashboard url page
#: includes/admin/views/upgrade/network.php:96
msgid ""
"Database Upgrade complete. <a href=\"%s\">Return to network dashboard</a>"
msgstr ""
"Das Upgrade der Datenbank wurde fertiggestellt. <a href=\"%s\">Zurück zum "
"Netzwerk-Dashboard</a>"

#: includes/admin/views/upgrade/network.php:79
msgid "Site is up to date"
msgstr "Die Website ist aktuell"

#. translators: %1 current db version, %2 available db version
#: includes/admin/views/upgrade/network.php:77
msgid "Site requires database upgrade from %1$s to %2$s"
msgstr "Die Website erfordert ein Upgrade der Datenbank von %1$s auf %2$s"

#: includes/admin/views/upgrade/network.php:34
#: includes/admin/views/upgrade/network.php:45
msgid "Site"
msgstr "Website"

#. translators: %s The button label name, translated seperately
#: includes/admin/views/upgrade/network.php:24
#: includes/admin/views/upgrade/network.php:25
#: includes/admin/views/upgrade/network.php:94
msgid "Upgrade Sites"
msgstr "Upgrade der Websites"

#. translators: %s The button label name, translated seperately
#: includes/admin/views/upgrade/network.php:24
msgid ""
"The following sites require a DB upgrade. Check the ones you want to update "
"and then click %s."
msgstr ""
"Folgende Websites erfordern ein Upgrade der Datenbank. Markiere die, die du "
"aktualisieren möchtest und klick dann auf %s."

#: includes/admin/views/acf-field-group/conditional-logic.php:184
#: includes/admin/views/acf-field-group/locations.php:37
msgid "Add rule group"
msgstr "Eine Regelgruppe hinzufügen"

#: includes/admin/views/acf-field-group/locations.php:10
msgid ""
"Create a set of rules to determine which edit screens will use these "
"advanced custom fields"
msgstr ""
"Erstelle einen Satz von Regeln, um festzulegen, in welchen "
"Bearbeitungsansichten diese „advanced custom fields“ genutzt werden"

#: includes/admin/views/acf-field-group/locations.php:9
msgid "Rules"
msgstr "Regeln"

#: includes/admin/tools/class-acf-admin-tool-export.php:449
msgid "Copied"
msgstr "Kopiert"

#: includes/admin/tools/class-acf-admin-tool-export.php:425
msgid "Copy to clipboard"
msgstr "In die Zwischenablage kopieren"

#: includes/admin/tools/class-acf-admin-tool-export.php:331
msgid ""
"Select the items you would like to export and then select your export "
"method. Export As JSON to export to a .json file which you can then import "
"to another ACF installation. Generate PHP to export to PHP code which you "
"can place in your theme."
msgstr ""
"Wähle, welche Feldgruppen du exportieren möchtest und wähle dann die "
"Exportmethode. Exportiere als JSON, um eine JSON-Datei zu exportieren, die "
"du im Anschluss in eine andere ACF-Installation importieren kannst. Verwende "
"den „PHP erstellen“-Button, um den resultierenden PHP-Code zu exportieren, "
"den du in dein Theme einfügen kannst."

#: includes/admin/tools/class-acf-admin-tool-export.php:215
msgid "Select Field Groups"
msgstr "Feldgruppen auswählen"

#: includes/admin/tools/class-acf-admin-tool-export.php:88
#: includes/admin/tools/class-acf-admin-tool-export.php:121
msgid "No field groups selected"
msgstr "Es wurden keine Feldgruppen ausgewählt"

#: includes/admin/tools/class-acf-admin-tool-export.php:38
#: includes/admin/tools/class-acf-admin-tool-export.php:339
#: includes/admin/tools/class-acf-admin-tool-export.php:363
msgid "Generate PHP"
msgstr "PHP erstellen"

#: includes/admin/tools/class-acf-admin-tool-export.php:34
msgid "Export Field Groups"
msgstr "Feldgruppen exportieren"

#: includes/admin/tools/class-acf-admin-tool-import.php:172
msgid "Import file empty"
msgstr "Die importierte Datei ist leer"

#: includes/admin/tools/class-acf-admin-tool-import.php:163
msgid "Incorrect file type"
msgstr "Inkorrekter Dateityp"

#: includes/admin/tools/class-acf-admin-tool-import.php:158
msgid "Error uploading file. Please try again"
msgstr "Fehler beim Upload der Datei. Bitte erneut versuchen"

#: includes/admin/tools/class-acf-admin-tool-import.php:47
msgid ""
"Select the Advanced Custom Fields JSON file you would like to import. When "
"you click the import button below, ACF will import the items in that file."
msgstr ""
"Wähle die Advanced-Custom-Fields-JSON-Datei, die du importieren möchtest. "
"Wenn du den Import-Button unten anklickst, wird ACF die Feldgruppen "
"importieren."

#: includes/admin/tools/class-acf-admin-tool-import.php:26
msgid "Import Field Groups"
msgstr "Feldgruppen importieren"

#: includes/admin/admin-internal-post-type-list.php:417
msgid "Sync"
msgstr "Synchronisieren"

#. translators: %s: field group title
#: includes/admin/admin-internal-post-type-list.php:960
msgid "Select %s"
msgstr "%s auswählen"

#: includes/admin/admin-internal-post-type-list.php:460
#: includes/admin/admin-internal-post-type-list.php:492
#: includes/admin/views/acf-field-group/field.php:90
msgid "Duplicate"
msgstr "Duplizieren"

#: includes/admin/admin-internal-post-type-list.php:460
msgid "Duplicate this item"
msgstr "Dieses Element duplizieren"

#: includes/admin/views/acf-post-type/advanced-settings.php:41
msgid "Supports"
msgstr "Hilfe"

#: includes/admin/admin.php:346
#: includes/admin/views/browse-fields-modal.php:102
msgid "Documentation"
msgstr "Dokumentation"

#: includes/admin/post-types/admin-field-groups.php:90
#: includes/admin/post-types/admin-post-types.php:110
#: includes/admin/post-types/admin-taxonomies.php:109
#: includes/admin/views/acf-field-group/options.php:249
#: includes/admin/views/acf-post-type/advanced-settings.php:62
#: includes/admin/views/acf-taxonomy/advanced-settings.php:114
#: includes/admin/views/upgrade/network.php:36
#: includes/admin/views/upgrade/network.php:47
msgid "Description"
msgstr "Beschreibung"

#: includes/admin/admin-internal-post-type-list.php:414
#: includes/admin/admin-internal-post-type-list.php:832
msgid "Sync available"
msgstr "Synchronisierung verfügbar"

#. translators: %s number of field groups synchronized
#: includes/admin/post-types/admin-field-groups.php:374
msgid "Field group synchronized."
msgid_plural "%s field groups synchronized."
msgstr[0] "Die Feldgruppe wurde synchronisiert."
msgstr[1] "%s Feldgruppen wurden synchronisiert."

#. translators: %s number of field groups duplicated
#: includes/admin/post-types/admin-field-groups.php:367
msgid "Field group duplicated."
msgid_plural "%s field groups duplicated."
msgstr[0] "Die Feldgruppe wurde dupliziert."
msgstr[1] "%s Feldgruppen wurden dupliziert."

#: includes/admin/admin-internal-post-type-list.php:155
msgid "Active <span class=\"count\">(%s)</span>"
msgid_plural "Active <span class=\"count\">(%s)</span>"
msgstr[0] "Aktiv <span class=\"count\">(%s)</span>"
msgstr[1] "Aktiv <span class=\"count\">(%s)</span>"

#: includes/admin/admin-upgrade.php:251
msgid "Review sites & upgrade"
msgstr "Websites prüfen und ein Upgrade durchführen"

#: includes/admin/admin-upgrade.php:59 includes/admin/admin-upgrade.php:90
#: includes/admin/admin-upgrade.php:91 includes/admin/admin-upgrade.php:227
#: includes/admin/views/upgrade/network.php:21
#: includes/admin/views/upgrade/upgrade.php:23
msgid "Upgrade Database"
msgstr "Upgrade der Datenbank"

#: includes/admin/views/acf-field-group/options.php:175
#: includes/admin/views/acf-post-type/advanced-settings.php:30
msgid "Custom Fields"
msgstr "Individuelle Felder"

#: includes/admin/post-types/admin-field-group.php:609
msgid "Move Field"
msgstr "Feld verschieben"

#: includes/admin/post-types/admin-field-group.php:602
#: includes/admin/post-types/admin-field-group.php:606
msgid "Please select the destination for this field"
msgstr "Bitte das Ziel für dieses Feld auswählen"

#. translators: Confirmation message once a field has been moved to a different
#. field group.
#: includes/admin/post-types/admin-field-group.php:568
msgid "The %1$s field can now be found in the %2$s field group"
msgstr "Das %1$s-Feld kann jetzt in der %2$s-Feldgruppe gefunden werden"

#: includes/admin/post-types/admin-field-group.php:565
msgid "Move Complete."
msgstr "Das Verschieben ist abgeschlossen."

#: includes/admin/views/acf-field-group/field.php:52
#: includes/admin/views/acf-field-group/options.php:217
#: includes/admin/views/acf-post-type/advanced-settings.php:78
#: includes/admin/views/acf-taxonomy/advanced-settings.php:130
msgid "Active"
msgstr "Aktiv"

#: includes/admin/post-types/admin-field-group.php:276
msgid "Field Keys"
msgstr "Feldschlüssel"

#: includes/admin/post-types/admin-field-group.php:180
msgid "Settings"
msgstr "Einstellungen"

#: includes/admin/post-types/admin-field-groups.php:92
msgid "Location"
msgstr "Position"

#: includes/admin/post-types/admin-field-group.php:100
#: assets/build/js/acf-input.js:1688 assets/build/js/acf-input.js:1850
msgid "Null"
msgstr "Null"

#: includes/admin/post-types/admin-field-group.php:97
#: includes/class-acf-internal-post-type.php:728
#: includes/post-types/class-acf-field-group.php:345
#: assets/build/js/acf-field-group.js:1541
#: assets/build/js/acf-field-group.js:1860
msgid "copy"
msgstr "kopieren"

#: includes/admin/post-types/admin-field-group.php:96
#: assets/build/js/acf-field-group.js:627
#: assets/build/js/acf-field-group.js:782
msgid "(this field)"
msgstr "(dieses Feld)"

#: includes/admin/post-types/admin-field-group.php:94
#: assets/build/js/acf-input.js:1629 assets/build/js/acf-input.js:1651
#: assets/build/js/acf-input.js:1783 assets/build/js/acf-input.js:1808
msgid "Checked"
msgstr "Ausgewählt"

#: includes/admin/post-types/admin-field-group.php:90
#: assets/build/js/acf-field-group.js:1646
#: assets/build/js/acf-field-group.js:1972
msgid "Move Custom Field"
msgstr "Individuelles Feld verschieben"

#: includes/admin/post-types/admin-field-group.php:89
#: assets/build/js/acf-field-group.js:653
#: assets/build/js/acf-field-group.js:808
msgid "No toggle fields available"
msgstr "Es sind keine Felder zum Umschalten verfügbar"

#: includes/admin/post-types/admin-field-group.php:87
msgid "Field group title is required"
msgstr "Ein Titel für die Feldgruppe ist erforderlich"

#: includes/admin/post-types/admin-field-group.php:86
#: assets/build/js/acf-field-group.js:1635
#: assets/build/js/acf-field-group.js:1958
msgid "This field cannot be moved until its changes have been saved"
msgstr ""
"Dieses Feld kann erst verschoben werden, wenn dessen Änderungen gespeichert "
"wurden"

#: includes/admin/post-types/admin-field-group.php:85
#: assets/build/js/acf-field-group.js:1445
#: assets/build/js/acf-field-group.js:1755
msgid "The string \"field_\" may not be used at the start of a field name"
msgstr "Die Zeichenfolge „field_“ darf nicht am Beginn eines Feldnamens stehen"

#: includes/admin/post-types/admin-field-group.php:69
msgid "Field group draft updated."
msgstr "Der Entwurf der Feldgruppe wurde aktualisiert."

#: includes/admin/post-types/admin-field-group.php:68
msgid "Field group scheduled for."
msgstr "Feldgruppe geplant für."

#: includes/admin/post-types/admin-field-group.php:67
msgid "Field group submitted."
msgstr "Die Feldgruppe wurde übertragen."

#: includes/admin/post-types/admin-field-group.php:66
msgid "Field group saved."
msgstr "Die Feldgruppe wurde gespeichert."

#: includes/admin/post-types/admin-field-group.php:65
msgid "Field group published."
msgstr "Die Feldgruppe wurde veröffentlicht."

#: includes/admin/post-types/admin-field-group.php:62
msgid "Field group deleted."
msgstr "Die Feldgruppe wurde gelöscht."

#: includes/admin/post-types/admin-field-group.php:60
#: includes/admin/post-types/admin-field-group.php:61
#: includes/admin/post-types/admin-field-group.php:63
msgid "Field group updated."
msgstr "Die Feldgruppe wurde aktualisiert."

#: includes/admin/admin-tools.php:107
#: includes/admin/views/global/navigation.php:251
#: includes/admin/views/tools/tools.php:13
msgid "Tools"
msgstr "Werkzeuge"

#: includes/locations/abstract-acf-location.php:105
msgid "is not equal to"
msgstr "ist ungleich"

#: includes/locations/abstract-acf-location.php:104
msgid "is equal to"
msgstr "ist gleich"

#: includes/locations.php:104
msgid "Forms"
msgstr "Formulare"

#: includes/admin/post-types/admin-post-type.php:125 includes/locations.php:102
#: includes/locations/class-acf-location-page.php:22
#: assets/build/js/acf-internal-post-type.js:175
#: assets/build/js/acf-internal-post-type.js:249
msgid "Page"
msgstr "Seite"

#: includes/admin/post-types/admin-post-type.php:123 includes/locations.php:101
#: includes/locations/class-acf-location-post.php:22
#: assets/build/js/acf-internal-post-type.js:172
#: assets/build/js/acf-internal-post-type.js:246
msgid "Post"
msgstr "Beitrag"

#: includes/fields.php:328
msgid "Relational"
msgstr "Relational"

#: includes/fields.php:327
msgid "Choice"
msgstr "Auswahl"

#: includes/fields.php:325
msgid "Basic"
msgstr "Grundlegend"

#: includes/fields.php:276
msgid "Unknown"
msgstr "Unbekannt"

#: includes/fields.php:276
msgid "Field type does not exist"
msgstr "Der Feldtyp existiert nicht"

#: includes/forms/form-front.php:217
msgid "Spam Detected"
msgstr "Es wurde Spam entdeckt"

#: includes/forms/form-front.php:100
msgid "Post updated"
msgstr "Der Beitrag wurde aktualisiert"

#: includes/forms/form-front.php:99
msgid "Update"
msgstr "Aktualisieren"

#: includes/forms/form-front.php:54
msgid "Validate Email"
msgstr "E-Mail-Adresse bestätigen"

#: includes/fields.php:326 includes/forms/form-front.php:46
msgid "Content"
msgstr "Inhalt"

#: includes/admin/views/acf-post-type/advanced-settings.php:21
#: includes/forms/form-front.php:37
msgid "Title"
msgstr "Titel"

#: includes/assets.php:376 includes/forms/form-comment.php:140
#: assets/build/js/acf-input.js:8413 assets/build/js/acf-input.js:9185
msgid "Edit field group"
msgstr "Feldgruppe bearbeiten"

#: includes/admin/post-types/admin-field-group.php:113
#: assets/build/js/acf-input.js:1815 assets/build/js/acf-input.js:1990
msgid "Selection is less than"
msgstr "Die Auswahl ist kleiner als"

#: includes/admin/post-types/admin-field-group.php:112
#: assets/build/js/acf-input.js:1799 assets/build/js/acf-input.js:1965
msgid "Selection is greater than"
msgstr "Die Auswahl ist größer als"

#: includes/admin/post-types/admin-field-group.php:111
#: assets/build/js/acf-input.js:1771 assets/build/js/acf-input.js:1936
msgid "Value is less than"
msgstr "Der Wert ist kleiner als"

#: includes/admin/post-types/admin-field-group.php:110
#: assets/build/js/acf-input.js:1744 assets/build/js/acf-input.js:1908
msgid "Value is greater than"
msgstr "Der Wert ist größer als"

#: includes/admin/post-types/admin-field-group.php:109
#: assets/build/js/acf-input.js:1602 assets/build/js/acf-input.js:1744
msgid "Value contains"
msgstr "Der Wert enthält"

#: includes/admin/post-types/admin-field-group.php:108
#: assets/build/js/acf-input.js:1579 assets/build/js/acf-input.js:1713
msgid "Value matches pattern"
msgstr "Der Wert entspricht dem Muster"

#: includes/admin/post-types/admin-field-group.php:107
#: assets/build/js/acf-input.js:1560 assets/build/js/acf-input.js:1725
#: assets/build/js/acf-input.js:1693 assets/build/js/acf-input.js:1888
msgid "Value is not equal to"
msgstr "Wert ist ungleich"

#: includes/admin/post-types/admin-field-group.php:106
#: assets/build/js/acf-input.js:1533 assets/build/js/acf-input.js:1669
#: assets/build/js/acf-input.js:1657 assets/build/js/acf-input.js:1828
msgid "Value is equal to"
msgstr "Der Wert ist gleich"

#: includes/admin/post-types/admin-field-group.php:105
#: assets/build/js/acf-input.js:1514 assets/build/js/acf-input.js:1637
msgid "Has no value"
msgstr "Hat keinen Wert"

#: includes/admin/post-types/admin-field-group.php:104
#: assets/build/js/acf-input.js:1487 assets/build/js/acf-input.js:1586
msgid "Has any value"
msgstr "Hat einen Wert"

#: includes/admin/admin-internal-post-type.php:337
#: includes/admin/views/browse-fields-modal.php:72 includes/assets.php:354
#: assets/build/js/acf.js:1570 assets/build/js/acf.js:1662
msgid "Cancel"
msgstr "Abbrechen"

#: includes/assets.php:350 assets/build/js/acf.js:1744
#: assets/build/js/acf.js:1859
msgid "Are you sure?"
msgstr "Bist du sicher?"

#: includes/assets.php:370 assets/build/js/acf-input.js:10481
#: assets/build/js/acf-input.js:11531
msgid "%d fields require attention"
msgstr "%d Felder erfordern Aufmerksamkeit"

#: includes/assets.php:369 assets/build/js/acf-input.js:10479
#: assets/build/js/acf-input.js:11529
msgid "1 field requires attention"
msgstr "1 Feld erfordert Aufmerksamkeit"

#: includes/assets.php:368 includes/validation.php:247
#: includes/validation.php:255 assets/build/js/acf-input.js:10474
#: assets/build/js/acf-input.js:11524
msgid "Validation failed"
msgstr "Die Überprüfung ist fehlgeschlagen"

#: includes/assets.php:367 assets/build/js/acf-input.js:10642
#: assets/build/js/acf-input.js:11702
msgid "Validation successful"
msgstr "Die Überprüfung war erfolgreich"

#: includes/media.php:54 assets/build/js/acf-input.js:8241
#: assets/build/js/acf-input.js:8989
msgid "Restricted"
msgstr "Eingeschränkt"

#: includes/media.php:53 assets/build/js/acf-input.js:8056
#: assets/build/js/acf-input.js:8753
msgid "Collapse Details"
msgstr "Details ausblenden"

#: includes/media.php:52 assets/build/js/acf-input.js:8056
#: assets/build/js/acf-input.js:8750
msgid "Expand Details"
msgstr "Details einblenden"

#: includes/admin/views/acf-post-type/advanced-settings.php:470
#: includes/media.php:51 assets/build/js/acf-input.js:7923
#: assets/build/js/acf-input.js:8598
msgid "Uploaded to this post"
msgstr "Zu diesem Beitrag hochgeladen"

#: includes/media.php:50 assets/build/js/acf-input.js:7962
#: assets/build/js/acf-input.js:8637
msgctxt "verb"
msgid "Update"
msgstr "Aktualisieren"

#: includes/media.php:49
msgctxt "verb"
msgid "Edit"
msgstr "Bearbeiten"

#: includes/assets.php:364 assets/build/js/acf-input.js:10252
#: assets/build/js/acf-input.js:11296
msgid "The changes you made will be lost if you navigate away from this page"
msgstr "Deine Änderungen werden verlorengehen, wenn du diese Seite verlässt"

#: includes/api/api-helpers.php:2959
msgid "File type must be %s."
msgstr "Der Dateityp muss %s sein."

#: includes/admin/post-types/admin-field-group.php:98
#: includes/admin/views/acf-field-group/conditional-logic.php:64
#: includes/admin/views/acf-field-group/conditional-logic.php:182
#: includes/admin/views/acf-field-group/location-group.php:3
#: includes/admin/views/acf-field-group/locations.php:35
#: includes/api/api-helpers.php:2956 assets/build/js/acf-field-group.js:781
#: assets/build/js/acf-field-group.js:2427
#: assets/build/js/acf-field-group.js:946
#: assets/build/js/acf-field-group.js:2859
msgid "or"
msgstr "oder"

#: includes/api/api-helpers.php:2932
msgid "File size must not exceed %s."
msgstr "Die Dateigröße darf nicht größer als %s sein."

#: includes/api/api-helpers.php:2928
msgid "File size must be at least %s."
msgstr "Die Dateigröße muss mindestens %s sein."

#: includes/api/api-helpers.php:2915
msgid "Image height must not exceed %dpx."
msgstr "Die Höhe des Bild darf %dpx nicht überschreiten."

#: includes/api/api-helpers.php:2911
msgid "Image height must be at least %dpx."
msgstr "Die Höhe des Bildes muss mindestens %dpx sein."

#: includes/api/api-helpers.php:2899
msgid "Image width must not exceed %dpx."
msgstr "Die Breite des Bildes darf %dpx nicht überschreiten."

#: includes/api/api-helpers.php:2895
msgid "Image width must be at least %dpx."
msgstr "Die Breite des Bildes muss mindestens %dpx sein."

#: includes/api/api-helpers.php:1409 includes/api/api-term.php:140
msgid "(no title)"
msgstr "(ohne Titel)"

#: includes/api/api-helpers.php:765
msgid "Full Size"
msgstr "Volle Größe"

#: includes/api/api-helpers.php:730
msgid "Large"
msgstr "Groß"

#: includes/api/api-helpers.php:729
msgid "Medium"
msgstr "Mittel"

#: includes/api/api-helpers.php:728
msgid "Thumbnail"
msgstr "Vorschaubild"

#: includes/acf-field-functions.php:854
#: includes/admin/post-types/admin-field-group.php:95
#: assets/build/js/acf-field-group.js:1090
#: assets/build/js/acf-field-group.js:1277
msgid "(no label)"
msgstr "(keine Beschriftung)"

#: includes/fields/class-acf-field-textarea.php:135
msgid "Sets the textarea height"
msgstr "Legt die Höhe des Textbereichs fest"

#: includes/fields/class-acf-field-textarea.php:134
msgid "Rows"
msgstr "Zeilen"

#: includes/fields/class-acf-field-textarea.php:22
msgid "Text Area"
msgstr "Textbereich"

#: includes/fields/class-acf-field-checkbox.php:421
msgid "Prepend an extra checkbox to toggle all choices"
msgstr ""
"Ein zusätzliches Auswahlfeld voranstellen, um alle Optionen auszuwählen"

#: includes/fields/class-acf-field-checkbox.php:383
msgid "Save 'custom' values to the field's choices"
msgstr "Individuelle Werte in den Auswahlmöglichkeiten des Feldes speichern"

#: includes/fields/class-acf-field-checkbox.php:372
msgid "Allow 'custom' values to be added"
msgstr "Das Hinzufügen individueller Werte erlauben"

#: includes/fields/class-acf-field-checkbox.php:35
msgid "Add new choice"
msgstr "Eine neue Auswahlmöglichkeit hinzufügen"

#: includes/fields/class-acf-field-checkbox.php:157
msgid "Toggle All"
msgstr "Alle umschalten"

#: includes/fields/class-acf-field-page_link.php:476
msgid "Allow Archives URLs"
msgstr "Archiv-URLs erlauben"

#: includes/fields/class-acf-field-page_link.php:185
msgid "Archives"
msgstr "Archive"

#: includes/fields/class-acf-field-page_link.php:22
msgid "Page Link"
msgstr "Seiten-Link"

#: includes/fields/class-acf-field-taxonomy.php:870
#: includes/locations/class-acf-location-user-form.php:72
msgid "Add"
msgstr "Hinzufügen"

#: includes/admin/views/acf-field-group/fields.php:53
#: includes/fields/class-acf-field-taxonomy.php:840
msgid "Name"
msgstr "Name"

#: includes/fields/class-acf-field-taxonomy.php:825
msgid "%s added"
msgstr "%s hinzugefügt"

#: includes/fields/class-acf-field-taxonomy.php:789
msgid "%s already exists"
msgstr "%s ist bereits vorhanden"

#: includes/fields/class-acf-field-taxonomy.php:777
msgid "User unable to add new %s"
msgstr "Der Benutzer kann keine neue %s hinzufügen"

#: includes/fields/class-acf-field-taxonomy.php:664
msgid "Term ID"
msgstr "Begriffs-ID"

#: includes/fields/class-acf-field-taxonomy.php:663
msgid "Term Object"
msgstr "Begriffs-Objekt"

#: includes/fields/class-acf-field-taxonomy.php:648
msgid "Load value from posts terms"
msgstr "Den Wert aus den Begriffen des Beitrags laden"

#: includes/fields/class-acf-field-taxonomy.php:647
msgid "Load Terms"
msgstr "Begriffe laden"

#: includes/fields/class-acf-field-taxonomy.php:637
msgid "Connect selected terms to the post"
msgstr "Verbinde die ausgewählten Begriffe mit dem Beitrag"

#: includes/fields/class-acf-field-taxonomy.php:636
msgid "Save Terms"
msgstr "Begriffe speichern"

#: includes/fields/class-acf-field-taxonomy.php:626
msgid "Allow new terms to be created whilst editing"
msgstr "Erlaubt das Erstellen neuer Begriffe während des Bearbeitens"

#: includes/fields/class-acf-field-taxonomy.php:625
msgid "Create Terms"
msgstr "Begriffe erstellen"

#: includes/fields/class-acf-field-taxonomy.php:684
msgid "Radio Buttons"
msgstr "Radiobuttons"

#: includes/fields/class-acf-field-taxonomy.php:683
msgid "Single Value"
msgstr "Einzelner Wert"

#: includes/fields/class-acf-field-taxonomy.php:681
msgid "Multi Select"
msgstr "Mehrfachauswahl"

#: includes/fields/class-acf-field-checkbox.php:22
#: includes/fields/class-acf-field-taxonomy.php:680
msgid "Checkbox"
msgstr "Auswahlkästchen"

#: includes/fields/class-acf-field-taxonomy.php:679
msgid "Multiple Values"
msgstr "Mehrere Werte"

#: includes/fields/class-acf-field-taxonomy.php:674
msgid "Select the appearance of this field"
msgstr "Das Design für dieses Feld auswählen"

#: includes/fields/class-acf-field-taxonomy.php:673
msgid "Appearance"
msgstr "Design"

#: includes/fields/class-acf-field-taxonomy.php:615
msgid "Select the taxonomy to be displayed"
msgstr "Wähle die Taxonomie, welche angezeigt werden soll"

#: includes/fields/class-acf-field-taxonomy.php:579
msgctxt "No Terms"
msgid "No %s"
msgstr "Keine %s"

#: includes/fields/class-acf-field-number.php:240
msgid "Value must be equal to or lower than %d"
msgstr "Der Wert muss kleiner oder gleich %d sein"

#: includes/fields/class-acf-field-number.php:235
msgid "Value must be equal to or higher than %d"
msgstr "Der Wert muss größer oder gleich %d sein"

#: includes/fields/class-acf-field-number.php:223
msgid "Value must be a number"
msgstr "Der Wert muss eine Zahl sein"

#: includes/fields/class-acf-field-number.php:22
msgid "Number"
msgstr "Numerisch"

#: includes/fields/class-acf-field-radio.php:254
msgid "Save 'other' values to the field's choices"
msgstr "Weitere Werte unter den Auswahlmöglichkeiten des Feldes speichern"

#: includes/fields/class-acf-field-radio.php:243
msgid "Add 'other' choice to allow for custom values"
msgstr ""
"Das Hinzufügen der Auswahlmöglichkeit ‚weitere‘ erlaubt individuelle Werte"

#: includes/admin/views/global/navigation.php:199
msgid "Other"
msgstr "Weitere"

#: includes/fields/class-acf-field-radio.php:22
msgid "Radio Button"
msgstr "Radiobutton"

#: includes/fields/class-acf-field-accordion.php:103
msgid ""
"Define an endpoint for the previous accordion to stop. This accordion will "
"not be visible."
msgstr ""
"Definiert einen Endpunkt, an dem das vorangegangene Akkordeon endet. Dieses "
"Akkordeon wird nicht sichtbar sein."

#: includes/fields/class-acf-field-accordion.php:92
msgid "Allow this accordion to open without closing others."
msgstr "Dieses Akkordeon öffnen, ohne die anderen zu schließen."

#: includes/fields/class-acf-field-accordion.php:91
msgid "Multi-Expand"
msgstr "Mehrfach-Erweiterung"

#: includes/fields/class-acf-field-accordion.php:81
msgid "Display this accordion as open on page load."
msgstr "Dieses Akkordeon beim Laden der Seite in geöffnetem Zustand anzeigen."

#: includes/fields/class-acf-field-accordion.php:80
msgid "Open"
msgstr "Geöffnet"

#: includes/fields/class-acf-field-accordion.php:24
msgid "Accordion"
msgstr "Akkordeon"

#: includes/fields/class-acf-field-file.php:253
#: includes/fields/class-acf-field-file.php:265
msgid "Restrict which files can be uploaded"
msgstr "Beschränkt, welche Dateien hochgeladen werden können"

#: includes/fields/class-acf-field-file.php:207
msgid "File ID"
msgstr "Datei-ID"

#: includes/fields/class-acf-field-file.php:206
msgid "File URL"
msgstr "Datei-URL"

#: includes/fields/class-acf-field-file.php:205
msgid "File Array"
msgstr "Datei-Array"

#: includes/fields/class-acf-field-file.php:176
msgid "Add File"
msgstr "Datei hinzufügen"

#: includes/admin/tools/class-acf-admin-tool-import.php:151
#: includes/fields/class-acf-field-file.php:176
msgid "No file selected"
msgstr "Es wurde keine Datei ausgewählt"

#: includes/fields/class-acf-field-file.php:140
msgid "File name"
msgstr "Dateiname"

#: includes/fields/class-acf-field-file.php:57
#: assets/build/js/acf-input.js:3162 assets/build/js/acf-input.js:3385
msgid "Update File"
msgstr "Datei aktualisieren"

#: includes/fields/class-acf-field-file.php:56
#: assets/build/js/acf-input.js:3161 assets/build/js/acf-input.js:3384
msgid "Edit File"
msgstr "Datei bearbeiten"

#: includes/admin/tools/class-acf-admin-tool-import.php:55
#: includes/fields/class-acf-field-file.php:55
#: assets/build/js/acf-input.js:3135 assets/build/js/acf-input.js:3357
msgid "Select File"
msgstr "Datei auswählen"

#: includes/fields/class-acf-field-file.php:22
msgid "File"
msgstr "Datei"

#: includes/fields/class-acf-field-password.php:22
msgid "Password"
msgstr "Passwort"

#: includes/fields/class-acf-field-select.php:365
msgid "Specify the value returned"
msgstr "Lege den Rückgabewert fest"

#: includes/fields/class-acf-field-select.php:433
msgid "Use AJAX to lazy load choices?"
msgstr "Soll AJAX genutzt werden, um Auswahlen verzögert zu laden?"

#: includes/fields/class-acf-field-checkbox.php:333
#: includes/fields/class-acf-field-select.php:354
msgid "Enter each default value on a new line"
msgstr "Jeden Standardwert in einer neuen Zeile eingeben"

#: includes/fields/class-acf-field-select.php:229 includes/media.php:48
#: assets/build/js/acf-input.js:7821 assets/build/js/acf-input.js:8483
msgctxt "verb"
msgid "Select"
msgstr "Auswählen"

#: includes/fields/class-acf-field-select.php:109
msgctxt "Select2 JS load_fail"
msgid "Loading failed"
msgstr "Das Laden ist fehlgeschlagen"

#: includes/fields/class-acf-field-select.php:108
msgctxt "Select2 JS searching"
msgid "Searching&hellip;"
msgstr "Suchen&hellip;"

#: includes/fields/class-acf-field-select.php:107
msgctxt "Select2 JS load_more"
msgid "Loading more results&hellip;"
msgstr "Mehr Ergebnisse laden&hellip;"

#: includes/fields/class-acf-field-select.php:106
msgctxt "Select2 JS selection_too_long_n"
msgid "You can only select %d items"
msgstr "Du kannst nur %d Elemente auswählen"

#: includes/fields/class-acf-field-select.php:105
msgctxt "Select2 JS selection_too_long_1"
msgid "You can only select 1 item"
msgstr "Du kannst nur ein Element auswählen"

#: includes/fields/class-acf-field-select.php:104
msgctxt "Select2 JS input_too_long_n"
msgid "Please delete %d characters"
msgstr "Lösche bitte %d Zeichen"

#: includes/fields/class-acf-field-select.php:103
msgctxt "Select2 JS input_too_long_1"
msgid "Please delete 1 character"
msgstr "Lösche bitte ein Zeichen"

#: includes/fields/class-acf-field-select.php:102
msgctxt "Select2 JS input_too_short_n"
msgid "Please enter %d or more characters"
msgstr "Gib bitte %d oder mehr Zeichen ein"

#: includes/fields/class-acf-field-select.php:101
msgctxt "Select2 JS input_too_short_1"
msgid "Please enter 1 or more characters"
msgstr "Gib bitte ein oder mehr Zeichen ein"

#: includes/fields/class-acf-field-select.php:100
msgctxt "Select2 JS matches_0"
msgid "No matches found"
msgstr "Es wurden keine Übereinstimmungen gefunden"

#: includes/fields/class-acf-field-select.php:99
msgctxt "Select2 JS matches_n"
msgid "%d results are available, use up and down arrow keys to navigate."
msgstr ""
"Es sind %d Ergebnisse verfügbar, benutze die Pfeiltasten um nach oben und "
"unten zu navigieren."

#: includes/fields/class-acf-field-select.php:98
msgctxt "Select2 JS matches_1"
msgid "One result is available, press enter to select it."
msgstr "Ein Ergebnis ist verfügbar, Eingabetaste drücken, um es auszuwählen."

#: includes/fields/class-acf-field-select.php:22
#: includes/fields/class-acf-field-taxonomy.php:685
msgctxt "noun"
msgid "Select"
msgstr "Auswahl"

#: includes/fields/class-acf-field-user.php:102
msgid "User ID"
msgstr "Benutzer-ID"

#: includes/fields/class-acf-field-user.php:101
msgid "User Object"
msgstr "Benutzer-Objekt"

#: includes/fields/class-acf-field-user.php:100
msgid "User Array"
msgstr "Benutzer-Array"

#: includes/fields/class-acf-field-user.php:88
msgid "All user roles"
msgstr "Alle Benutzerrollen"

#: includes/fields/class-acf-field-user.php:80
msgid "Filter by Role"
msgstr "Nach Rolle filtern"

#: includes/fields/class-acf-field-user.php:15 includes/locations.php:103
msgid "User"
msgstr "Benutzer"

#: includes/fields/class-acf-field-separator.php:22
msgid "Separator"
msgstr "Trennzeichen"

#: includes/fields/class-acf-field-color_picker.php:69
msgid "Select Color"
msgstr "Farbe auswählen"

#: includes/admin/post-types/admin-post-type.php:127
#: includes/admin/post-types/admin-taxonomy.php:129
#: includes/fields/class-acf-field-color_picker.php:67
#: assets/build/js/acf-internal-post-type.js:72
#: assets/build/js/acf-internal-post-type.js:86
msgid "Default"
msgstr "Standard"

#: includes/admin/views/acf-post-type/advanced-settings.php:89
#: includes/admin/views/acf-taxonomy/advanced-settings.php:141
#: includes/fields/class-acf-field-color_picker.php:65
msgid "Clear"
msgstr "Leeren"

#: includes/fields/class-acf-field-color_picker.php:22
msgid "Color Picker"
msgstr "Farbpicker"

#: includes/fields/class-acf-field-date_time_picker.php:82
msgctxt "Date Time Picker JS pmTextShort"
msgid "P"
msgstr "P"

#: includes/fields/class-acf-field-date_time_picker.php:81
msgctxt "Date Time Picker JS pmText"
msgid "PM"
msgstr "PM"

#: includes/fields/class-acf-field-date_time_picker.php:78
msgctxt "Date Time Picker JS amTextShort"
msgid "A"
msgstr "A"

#: includes/fields/class-acf-field-date_time_picker.php:77
msgctxt "Date Time Picker JS amText"
msgid "AM"
msgstr "AM"

#: includes/fields/class-acf-field-date_time_picker.php:75
msgctxt "Date Time Picker JS selectText"
msgid "Select"
msgstr "Auswählen"

#: includes/fields/class-acf-field-date_time_picker.php:74
msgctxt "Date Time Picker JS closeText"
msgid "Done"
msgstr "Fertig"

#: includes/fields/class-acf-field-date_time_picker.php:73
msgctxt "Date Time Picker JS currentText"
msgid "Now"
msgstr "Jetzt"

#: includes/fields/class-acf-field-date_time_picker.php:72
msgctxt "Date Time Picker JS timezoneText"
msgid "Time Zone"
msgstr "Zeitzone"

#: includes/fields/class-acf-field-date_time_picker.php:71
msgctxt "Date Time Picker JS microsecText"
msgid "Microsecond"
msgstr "Mikrosekunde"

#: includes/fields/class-acf-field-date_time_picker.php:70
msgctxt "Date Time Picker JS millisecText"
msgid "Millisecond"
msgstr "Millisekunde"

#: includes/fields/class-acf-field-date_time_picker.php:69
msgctxt "Date Time Picker JS secondText"
msgid "Second"
msgstr "Sekunde"

#: includes/fields/class-acf-field-date_time_picker.php:68
msgctxt "Date Time Picker JS minuteText"
msgid "Minute"
msgstr "Minute"

#: includes/fields/class-acf-field-date_time_picker.php:67
msgctxt "Date Time Picker JS hourText"
msgid "Hour"
msgstr "Stunde"

#: includes/fields/class-acf-field-date_time_picker.php:66
msgctxt "Date Time Picker JS timeText"
msgid "Time"
msgstr "Zeit"

#: includes/fields/class-acf-field-date_time_picker.php:65
msgctxt "Date Time Picker JS timeOnlyTitle"
msgid "Choose Time"
msgstr "Zeit wählen"

#: includes/fields/class-acf-field-date_time_picker.php:22
msgid "Date Time Picker"
msgstr "Datums- und Zeitauswahl"

#: includes/fields/class-acf-field-accordion.php:102
msgid "Endpoint"
msgstr "Endpunkt"

#: includes/admin/views/acf-field-group/options.php:130
#: includes/fields/class-acf-field-tab.php:109
msgid "Left aligned"
msgstr "Linksbündig"

#: includes/admin/views/acf-field-group/options.php:129
#: includes/fields/class-acf-field-tab.php:108
msgid "Top aligned"
msgstr "Oben ausgerichtet"

#: includes/fields/class-acf-field-tab.php:104
msgid "Placement"
msgstr "Platzierung"

#: includes/fields/class-acf-field-tab.php:23
msgid "Tab"
msgstr "Tab"

#: includes/fields/class-acf-field-url.php:138
msgid "Value must be a valid URL"
msgstr "Der Wert muss eine gültige URL sein"

#: includes/fields/class-acf-field-link.php:153
msgid "Link URL"
msgstr "Link-URL"

#: includes/fields/class-acf-field-link.php:152
msgid "Link Array"
msgstr "Link-Array"

#: includes/fields/class-acf-field-link.php:124
msgid "Opens in a new window/tab"
msgstr "In einem neuen Fenster/Tab öffnen"

#: includes/fields/class-acf-field-link.php:119
msgid "Select Link"
msgstr "Link auswählen"

#: includes/fields/class-acf-field-link.php:22
msgid "Link"
msgstr "Link"

#: includes/fields/class-acf-field-email.php:22
msgid "Email"
msgstr "E-Mail-Adresse"

#: includes/fields/class-acf-field-number.php:173
#: includes/fields/class-acf-field-range.php:206
msgid "Step Size"
msgstr "Schrittweite"

#: includes/fields/class-acf-field-number.php:143
#: includes/fields/class-acf-field-range.php:184
msgid "Maximum Value"
msgstr "Maximalwert"

#: includes/fields/class-acf-field-number.php:133
#: includes/fields/class-acf-field-range.php:173
msgid "Minimum Value"
msgstr "Mindestwert"

#: includes/fields/class-acf-field-range.php:22
msgid "Range"
msgstr "Bereich"

#: includes/fields/class-acf-field-button-group.php:165
#: includes/fields/class-acf-field-checkbox.php:350
#: includes/fields/class-acf-field-radio.php:210
#: includes/fields/class-acf-field-select.php:372
msgid "Both (Array)"
msgstr "Beide (Array)"

#: includes/admin/views/acf-field-group/fields.php:52
#: includes/fields/class-acf-field-button-group.php:164
#: includes/fields/class-acf-field-checkbox.php:349
#: includes/fields/class-acf-field-radio.php:209
#: includes/fields/class-acf-field-select.php:371
msgid "Label"
msgstr "Beschriftung"

#: includes/fields/class-acf-field-button-group.php:163
#: includes/fields/class-acf-field-checkbox.php:348
#: includes/fields/class-acf-field-radio.php:208
#: includes/fields/class-acf-field-select.php:370
msgid "Value"
msgstr "Wert"

#: includes/fields/class-acf-field-button-group.php:211
#: includes/fields/class-acf-field-checkbox.php:411
#: includes/fields/class-acf-field-radio.php:282
msgid "Vertical"
msgstr "Vertikal"

#: includes/fields/class-acf-field-button-group.php:210
#: includes/fields/class-acf-field-checkbox.php:412
#: includes/fields/class-acf-field-radio.php:283
msgid "Horizontal"
msgstr "Horizontal"

#: includes/fields/class-acf-field-button-group.php:138
#: includes/fields/class-acf-field-checkbox.php:323
#: includes/fields/class-acf-field-radio.php:183
#: includes/fields/class-acf-field-select.php:343
msgid "red : Red"
msgstr "rot : Rot"

#: includes/fields/class-acf-field-button-group.php:138
#: includes/fields/class-acf-field-checkbox.php:323
#: includes/fields/class-acf-field-radio.php:183
#: includes/fields/class-acf-field-select.php:343
msgid "For more control, you may specify both a value and label like this:"
msgstr ""
"Für mehr Kontrolle kannst du sowohl einen Wert als auch eine Beschriftung "
"wie folgt angeben:"

#: includes/fields/class-acf-field-button-group.php:138
#: includes/fields/class-acf-field-checkbox.php:323
#: includes/fields/class-acf-field-radio.php:183
#: includes/fields/class-acf-field-select.php:343
msgid "Enter each choice on a new line."
msgstr "Jede Option in eine neue Zeile eintragen."

#: includes/fields/class-acf-field-button-group.php:137
#: includes/fields/class-acf-field-checkbox.php:322
#: includes/fields/class-acf-field-radio.php:182
#: includes/fields/class-acf-field-select.php:342
msgid "Choices"
msgstr "Auswahlmöglichkeiten"

#: includes/fields/class-acf-field-button-group.php:23
msgid "Button Group"
msgstr "Button-Gruppe"

#: includes/fields/class-acf-field-button-group.php:183
#: includes/fields/class-acf-field-page_link.php:508
#: includes/fields/class-acf-field-post_object.php:421
#: includes/fields/class-acf-field-radio.php:228
#: includes/fields/class-acf-field-select.php:401
#: includes/fields/class-acf-field-taxonomy.php:694
#: includes/fields/class-acf-field-user.php:132
msgid "Allow Null"
msgstr "NULL-Werte zulassen?"

#: includes/fields/class-acf-field-page_link.php:262
#: includes/fields/class-acf-field-post_object.php:243
#: includes/fields/class-acf-field-taxonomy.php:858
msgid "Parent"
msgstr "Übergeordnet"

#: includes/fields/class-acf-field-wysiwyg.php:367
msgid "TinyMCE will not be initialized until field is clicked"
msgstr "TinyMCE wird erst initialisiert, wenn das Feld geklickt wird"

#: includes/fields/class-acf-field-wysiwyg.php:366
msgid "Delay Initialization"
msgstr "Soll die Initialisierung verzögert werden?"

#: includes/fields/class-acf-field-wysiwyg.php:355
msgid "Show Media Upload Buttons"
msgstr "Sollen Buttons zum Hochladen von Medien anzeigt werden?"

#: includes/fields/class-acf-field-wysiwyg.php:339
msgid "Toolbar"
msgstr "Werkzeugleiste"

#: includes/fields/class-acf-field-wysiwyg.php:331
msgid "Text Only"
msgstr "Nur Text"

#: includes/fields/class-acf-field-wysiwyg.php:330
msgid "Visual Only"
msgstr "Nur visuell"

#: includes/fields/class-acf-field-wysiwyg.php:329
msgid "Visual & Text"
msgstr "Visuell und Text"

#: includes/fields/class-acf-field-icon_picker.php:237
#: includes/fields/class-acf-field-wysiwyg.php:324
msgid "Tabs"
msgstr "Tabs"

#: includes/fields/class-acf-field-wysiwyg.php:268
msgid "Click to initialize TinyMCE"
msgstr "Klicken, um TinyMCE zu initialisieren"

#: includes/fields/class-acf-field-wysiwyg.php:262
msgctxt "Name for the Text editor tab (formerly HTML)"
msgid "Text"
msgstr "Text"

#: includes/fields/class-acf-field-wysiwyg.php:261
msgid "Visual"
msgstr "Visuell"

#: includes/fields/class-acf-field-text.php:181
#: includes/fields/class-acf-field-textarea.php:217
msgid "Value must not exceed %d characters"
msgstr "Der Wert darf %d Zeichen nicht überschreiten"

#: includes/fields/class-acf-field-text.php:116
#: includes/fields/class-acf-field-textarea.php:114
msgid "Leave blank for no limit"
msgstr "Leer lassen, wenn es keine Begrenzung gibt"

#: includes/fields/class-acf-field-text.php:115
#: includes/fields/class-acf-field-textarea.php:113
msgid "Character Limit"
msgstr "Zeichenbegrenzung"

#: includes/fields/class-acf-field-email.php:144
#: includes/fields/class-acf-field-number.php:194
#: includes/fields/class-acf-field-password.php:95
#: includes/fields/class-acf-field-range.php:228
#: includes/fields/class-acf-field-text.php:156
msgid "Appears after the input"
msgstr "Wird nach dem Eingabefeld angezeigt"

#: includes/fields/class-acf-field-email.php:143
#: includes/fields/class-acf-field-number.php:193
#: includes/fields/class-acf-field-password.php:94
#: includes/fields/class-acf-field-range.php:227
#: includes/fields/class-acf-field-text.php:155
msgid "Append"
msgstr "Anhängen"

#: includes/fields/class-acf-field-email.php:134
#: includes/fields/class-acf-field-number.php:184
#: includes/fields/class-acf-field-password.php:85
#: includes/fields/class-acf-field-range.php:218
#: includes/fields/class-acf-field-text.php:146
msgid "Appears before the input"
msgstr "Wird dem Eingabefeld vorangestellt"

#: includes/fields/class-acf-field-email.php:133
#: includes/fields/class-acf-field-number.php:183
#: includes/fields/class-acf-field-password.php:84
#: includes/fields/class-acf-field-range.php:217
#: includes/fields/class-acf-field-text.php:145
msgid "Prepend"
msgstr "Voranstellen"

#: includes/fields/class-acf-field-email.php:124
#: includes/fields/class-acf-field-number.php:164
#: includes/fields/class-acf-field-password.php:75
#: includes/fields/class-acf-field-text.php:136
#: includes/fields/class-acf-field-textarea.php:146
#: includes/fields/class-acf-field-url.php:105
msgid "Appears within the input"
msgstr "Wird innerhalb des Eingabefeldes angezeigt"

#: includes/fields/class-acf-field-email.php:123
#: includes/fields/class-acf-field-number.php:163
#: includes/fields/class-acf-field-password.php:74
#: includes/fields/class-acf-field-text.php:135
#: includes/fields/class-acf-field-textarea.php:145
#: includes/fields/class-acf-field-url.php:104
msgid "Placeholder Text"
msgstr "Platzhaltertext"

#: includes/fields/class-acf-field-button-group.php:148
#: includes/fields/class-acf-field-email.php:104
#: includes/fields/class-acf-field-number.php:114
#: includes/fields/class-acf-field-radio.php:193
#: includes/fields/class-acf-field-range.php:154
#: includes/fields/class-acf-field-text.php:96
#: includes/fields/class-acf-field-textarea.php:94
#: includes/fields/class-acf-field-url.php:85
#: includes/fields/class-acf-field-wysiwyg.php:292
msgid "Appears when creating a new post"
msgstr "Wird bei der Erstellung eines neuen Beitrags angezeigt"

#: includes/fields/class-acf-field-text.php:22
msgid "Text"
msgstr "Text"

#: includes/fields/class-acf-field-relationship.php:742
msgid "%1$s requires at least %2$s selection"
msgid_plural "%1$s requires at least %2$s selections"
msgstr[0] "%1$s erfordert mindestens %2$s Auswahl"
msgstr[1] "%1$s erfordert mindestens %2$s Auswahlen"

#: includes/fields/class-acf-field-post_object.php:391
#: includes/fields/class-acf-field-relationship.php:605
msgid "Post ID"
msgstr "Beitrags-ID"

#: includes/fields/class-acf-field-post_object.php:15
#: includes/fields/class-acf-field-post_object.php:390
#: includes/fields/class-acf-field-relationship.php:604
msgid "Post Object"
msgstr "Beitrags-Objekt"

#: includes/fields/class-acf-field-relationship.php:637
msgid "Maximum Posts"
msgstr "Höchstzahl an Beiträgen"

#: includes/fields/class-acf-field-relationship.php:627
msgid "Minimum Posts"
msgstr "Mindestzahl an Beiträgen"

#: includes/admin/views/acf-field-group/options.php:183
#: includes/admin/views/acf-post-type/advanced-settings.php:29
#: includes/fields/class-acf-field-relationship.php:662
msgid "Featured Image"
msgstr "Beitragsbild"

#: includes/fields/class-acf-field-relationship.php:658
msgid "Selected elements will be displayed in each result"
msgstr "Die ausgewählten Elemente werden in jedem Ergebnis angezeigt"

#: includes/fields/class-acf-field-relationship.php:657
msgid "Elements"
msgstr "Elemente"

#: includes/fields/class-acf-field-relationship.php:591
#: includes/fields/class-acf-field-taxonomy.php:20
#: includes/fields/class-acf-field-taxonomy.php:614
#: includes/locations/class-acf-location-taxonomy.php:22
msgid "Taxonomy"
msgstr "Taxonomie"

#: includes/fields/class-acf-field-relationship.php:590
#: includes/locations/class-acf-location-post-type.php:22
#: includes/post-types/class-acf-post-type.php:92
msgid "Post Type"
msgstr "Inhaltstyp"

#: includes/fields/class-acf-field-relationship.php:584
msgid "Filters"
msgstr "Filter"

#: includes/fields/class-acf-field-page_link.php:469
#: includes/fields/class-acf-field-post_object.php:378
#: includes/fields/class-acf-field-relationship.php:577
msgid "All taxonomies"
msgstr "Alle Taxonomien"

#: includes/fields/class-acf-field-page_link.php:461
#: includes/fields/class-acf-field-post_object.php:370
#: includes/fields/class-acf-field-relationship.php:569
msgid "Filter by Taxonomy"
msgstr "Nach Taxonomie filtern"

#: includes/fields/class-acf-field-page_link.php:439
#: includes/fields/class-acf-field-post_object.php:348
#: includes/fields/class-acf-field-relationship.php:547
msgid "All post types"
msgstr "Alle Inhaltstypen"

#: includes/fields/class-acf-field-page_link.php:431
#: includes/fields/class-acf-field-post_object.php:340
#: includes/fields/class-acf-field-relationship.php:539
msgid "Filter by Post Type"
msgstr "Nach Inhaltstyp filtern"

#: includes/fields/class-acf-field-relationship.php:439
msgid "Search..."
msgstr "Suche ..."

#: includes/fields/class-acf-field-relationship.php:369
msgid "Select taxonomy"
msgstr "Taxonomie auswählen"

#: includes/fields/class-acf-field-relationship.php:361
msgid "Select post type"
msgstr "Inhaltstyp auswählen"

#: includes/fields/class-acf-field-relationship.php:78
#: assets/build/js/acf-input.js:4937 assets/build/js/acf-input.js:5402
msgid "No matches found"
msgstr "Es wurde keine Übereinstimmung gefunden"

#: includes/fields/class-acf-field-relationship.php:77
#: assets/build/js/acf-input.js:4920 assets/build/js/acf-input.js:5381
msgid "Loading"
msgstr "Wird geladen"

#: includes/fields/class-acf-field-relationship.php:76
#: assets/build/js/acf-input.js:4824 assets/build/js/acf-input.js:5271
msgid "Maximum values reached ( {max} values )"
msgstr "Die maximal möglichen Werte wurden erreicht ({max} Werte)"

#: includes/fields/class-acf-field-relationship.php:17
msgid "Relationship"
msgstr "Beziehung"

#: includes/fields/class-acf-field-file.php:277
#: includes/fields/class-acf-field-image.php:307
msgid "Comma separated list. Leave blank for all types"
msgstr ""
"Eine durch Kommata getrennte Liste. Leer lassen, um alle Typen zu erlauben"

#: includes/fields/class-acf-field-file.php:276
#: includes/fields/class-acf-field-image.php:306
msgid "Allowed File Types"
msgstr "Erlaubte Dateiformate"

#: includes/fields/class-acf-field-file.php:264
#: includes/fields/class-acf-field-image.php:270
msgid "Maximum"
msgstr "Maximum"

#: includes/fields/class-acf-field-file.php:144
#: includes/fields/class-acf-field-file.php:256
#: includes/fields/class-acf-field-file.php:268
#: includes/fields/class-acf-field-image.php:261
#: includes/fields/class-acf-field-image.php:297
msgid "File size"
msgstr "Dateigröße"

#: includes/fields/class-acf-field-image.php:235
#: includes/fields/class-acf-field-image.php:271
msgid "Restrict which images can be uploaded"
msgstr "Beschränkt, welche Bilder hochgeladen werden können"

#: includes/fields/class-acf-field-file.php:252
#: includes/fields/class-acf-field-image.php:234
msgid "Minimum"
msgstr "Minimum"

#: includes/fields/class-acf-field-file.php:222
#: includes/fields/class-acf-field-image.php:200
msgid "Uploaded to post"
msgstr "Wurde zum Beitrag hochgeladen"

#: includes/fields/class-acf-field-file.php:221
#: includes/fields/class-acf-field-image.php:199
#: includes/locations/class-acf-location-attachment.php:73
#: includes/locations/class-acf-location-comment.php:61
#: includes/locations/class-acf-location-nav-menu.php:74
#: includes/locations/class-acf-location-taxonomy.php:63
#: includes/locations/class-acf-location-user-form.php:71
#: includes/locations/class-acf-location-user-role.php:78
#: includes/locations/class-acf-location-widget.php:65
msgid "All"
msgstr "Alle"

#: includes/fields/class-acf-field-file.php:216
#: includes/fields/class-acf-field-image.php:194
msgid "Limit the media library choice"
msgstr "Beschränkt die Auswahl in der Mediathek"

#: includes/fields/class-acf-field-file.php:215
#: includes/fields/class-acf-field-image.php:193
msgid "Library"
msgstr "Mediathek"

#: includes/fields/class-acf-field-image.php:326
msgid "Preview Size"
msgstr "Vorschau-Größe"

#: includes/fields/class-acf-field-image.php:185
msgid "Image ID"
msgstr "Bild-ID"

#: includes/fields/class-acf-field-image.php:184
msgid "Image URL"
msgstr "Bild-URL"

#: includes/fields/class-acf-field-image.php:183
msgid "Image Array"
msgstr "Bild-Array"

#: includes/fields/class-acf-field-button-group.php:158
#: includes/fields/class-acf-field-checkbox.php:343
#: includes/fields/class-acf-field-file.php:200
#: includes/fields/class-acf-field-link.php:147
#: includes/fields/class-acf-field-radio.php:203
msgid "Specify the returned value on front end"
msgstr "Legt den Rückgabewert für das Frontend fest"

#: includes/fields/class-acf-field-button-group.php:157
#: includes/fields/class-acf-field-checkbox.php:342
#: includes/fields/class-acf-field-file.php:199
#: includes/fields/class-acf-field-link.php:146
#: includes/fields/class-acf-field-radio.php:202
#: includes/fields/class-acf-field-taxonomy.php:658
msgid "Return Value"
msgstr "Rückgabewert"

#: includes/fields/class-acf-field-image.php:155
msgid "Add Image"
msgstr "Bild hinzufügen"

#: includes/fields/class-acf-field-image.php:155
msgid "No image selected"
msgstr "Es wurde kein Bild ausgewählt"

#: includes/assets.php:353 includes/fields/class-acf-field-file.php:152
#: includes/fields/class-acf-field-image.php:135
#: includes/fields/class-acf-field-link.php:124 assets/build/js/acf.js:1569
#: assets/build/js/acf.js:1661
msgid "Remove"
msgstr "Entfernen"

#: includes/admin/views/acf-field-group/field.php:89
#: includes/fields/class-acf-field-file.php:150
#: includes/fields/class-acf-field-image.php:133
#: includes/fields/class-acf-field-link.php:124
msgid "Edit"
msgstr "Bearbeiten"

#: includes/fields/class-acf-field-image.php:63 includes/media.php:55
#: assets/build/js/acf-input.js:7868 assets/build/js/acf-input.js:8537
msgid "All images"
msgstr "Alle Bilder"

#: includes/fields/class-acf-field-image.php:62
#: assets/build/js/acf-input.js:4181 assets/build/js/acf-input.js:4579
msgid "Update Image"
msgstr "Bild aktualisieren"

#: includes/fields/class-acf-field-image.php:61
#: assets/build/js/acf-input.js:4180 assets/build/js/acf-input.js:4578
msgid "Edit Image"
msgstr "Bild bearbeiten"

#: includes/fields/class-acf-field-image.php:60
#: assets/build/js/acf-input.js:4016 assets/build/js/acf-input.js:4156
#: assets/build/js/acf-input.js:4404 assets/build/js/acf-input.js:4553
msgid "Select Image"
msgstr "Bild auswählen"

#: includes/fields/class-acf-field-image.php:22
msgid "Image"
msgstr "Bild"

#: includes/fields/class-acf-field-message.php:110
msgid "Allow HTML markup to display as visible text instead of rendering"
msgstr "HTML-Markup als sichtbaren Text anzeigen, anstatt es zu rendern"

#: includes/fields/class-acf-field-message.php:109
msgid "Escape HTML"
msgstr "HTML maskieren"

#: includes/fields/class-acf-field-message.php:101
#: includes/fields/class-acf-field-textarea.php:162
msgid "No Formatting"
msgstr "Keine Formatierung"

#: includes/fields/class-acf-field-message.php:100
#: includes/fields/class-acf-field-textarea.php:161
msgid "Automatically add &lt;br&gt;"
msgstr "Automatisches Hinzufügen von &lt;br&gt;"

#: includes/fields/class-acf-field-message.php:99
#: includes/fields/class-acf-field-textarea.php:160
msgid "Automatically add paragraphs"
msgstr "Absätze automatisch hinzufügen"

#: includes/fields/class-acf-field-message.php:95
#: includes/fields/class-acf-field-textarea.php:156
msgid "Controls how new lines are rendered"
msgstr "Legt fest, wie Zeilenumbrüche gerendert werden"

#: includes/fields/class-acf-field-message.php:94
#: includes/fields/class-acf-field-textarea.php:155
msgid "New Lines"
msgstr "Zeilenumbrüche"

#: includes/fields/class-acf-field-date_picker.php:221
#: includes/fields/class-acf-field-date_time_picker.php:208
msgid "Week Starts On"
msgstr "Die Woche beginnt am"

#: includes/fields/class-acf-field-date_picker.php:190
msgid "The format used when saving a value"
msgstr "Das Format für das Speichern eines Wertes"

#: includes/fields/class-acf-field-date_picker.php:189
msgid "Save Format"
msgstr "Format speichern"

#: includes/fields/class-acf-field-date_picker.php:61
msgctxt "Date Picker JS weekHeader"
msgid "Wk"
msgstr "W"

#: includes/fields/class-acf-field-date_picker.php:60
msgctxt "Date Picker JS prevText"
msgid "Prev"
msgstr "Vorheriges"

#: includes/fields/class-acf-field-date_picker.php:59
msgctxt "Date Picker JS nextText"
msgid "Next"
msgstr "Nächstes"

#: includes/fields/class-acf-field-date_picker.php:58
msgctxt "Date Picker JS currentText"
msgid "Today"
msgstr "Heute"

#: includes/fields/class-acf-field-date_picker.php:57
msgctxt "Date Picker JS closeText"
msgid "Done"
msgstr "Fertig"

#: includes/fields/class-acf-field-date_picker.php:22
msgid "Date Picker"
msgstr "Datumspicker"

#: includes/fields/class-acf-field-image.php:238
#: includes/fields/class-acf-field-image.php:274
#: includes/fields/class-acf-field-oembed.php:241
msgid "Width"
msgstr "Breite"

#: includes/fields/class-acf-field-oembed.php:238
#: includes/fields/class-acf-field-oembed.php:250
msgid "Embed Size"
msgstr "Einbettungs-Größe"

#: includes/fields/class-acf-field-oembed.php:198
msgid "Enter URL"
msgstr "URL eingeben"

#: includes/fields/class-acf-field-oembed.php:22
msgid "oEmbed"
msgstr "oEmbed"

#: includes/fields/class-acf-field-true_false.php:172
msgid "Text shown when inactive"
msgstr "Der Text, der im aktiven Zustand angezeigt wird"

#: includes/fields/class-acf-field-true_false.php:171
msgid "Off Text"
msgstr "Wenn inaktiv"

#: includes/fields/class-acf-field-true_false.php:156
msgid "Text shown when active"
msgstr "Der Text, der im inaktiven Zustand angezeigt wird"

#: includes/fields/class-acf-field-true_false.php:155
msgid "On Text"
msgstr "Wenn aktiv"

#: includes/fields/class-acf-field-select.php:422
#: includes/fields/class-acf-field-true_false.php:187
msgid "Stylized UI"
msgstr "Gestylte UI"

#: includes/fields/class-acf-field-button-group.php:147
#: includes/fields/class-acf-field-checkbox.php:332
#: includes/fields/class-acf-field-color_picker.php:144
#: includes/fields/class-acf-field-email.php:103
#: includes/fields/class-acf-field-number.php:113
#: includes/fields/class-acf-field-radio.php:192
#: includes/fields/class-acf-field-range.php:153
#: includes/fields/class-acf-field-select.php:353
#: includes/fields/class-acf-field-text.php:95
#: includes/fields/class-acf-field-textarea.php:93
#: includes/fields/class-acf-field-true_false.php:135
#: includes/fields/class-acf-field-url.php:84
#: includes/fields/class-acf-field-wysiwyg.php:291
msgid "Default Value"
msgstr "Standardwert"

#: includes/fields/class-acf-field-true_false.php:126
msgid "Displays text alongside the checkbox"
msgstr "Zeigt den Text neben dem Auswahlkästchen an"

#: includes/fields/class-acf-field-message.php:23
#: includes/fields/class-acf-field-message.php:84
#: includes/fields/class-acf-field-true_false.php:125
msgid "Message"
msgstr "Mitteilung"

#: includes/assets.php:352 includes/class-acf-site-health.php:277
#: includes/class-acf-site-health.php:334
#: includes/fields/class-acf-field-true_false.php:79
#: includes/fields/class-acf-field-true_false.php:175
#: assets/build/js/acf.js:1746 assets/build/js/acf.js:1861
msgid "No"
msgstr "Nein"

#: includes/assets.php:351 includes/class-acf-site-health.php:276
#: includes/class-acf-site-health.php:334
#: includes/fields/class-acf-field-true_false.php:76
#: includes/fields/class-acf-field-true_false.php:159
#: assets/build/js/acf.js:1745 assets/build/js/acf.js:1860
msgid "Yes"
msgstr "Ja"

#: includes/fields/class-acf-field-true_false.php:22
msgid "True / False"
msgstr "Wahr/falsch"

#: includes/fields/class-acf-field-group.php:412
msgid "Row"
msgstr "Reihe"

#: includes/fields/class-acf-field-group.php:411
msgid "Table"
msgstr "Tabelle"

#: includes/admin/post-types/admin-field-group.php:158
#: includes/fields/class-acf-field-group.php:410
msgid "Block"
msgstr "Block"

#: includes/fields/class-acf-field-group.php:405
msgid "Specify the style used to render the selected fields"
msgstr "Lege den Stil für die Darstellung der ausgewählten Felder fest"

#: includes/fields.php:330 includes/fields/class-acf-field-button-group.php:204
#: includes/fields/class-acf-field-checkbox.php:405
#: includes/fields/class-acf-field-group.php:404
#: includes/fields/class-acf-field-radio.php:276
msgid "Layout"
msgstr "Layout"

#: includes/fields/class-acf-field-group.php:388
msgid "Sub Fields"
msgstr "Untergeordnete Felder"

#: includes/fields/class-acf-field-group.php:22
msgid "Group"
msgstr "Gruppe"

#: includes/fields/class-acf-field-google-map.php:222
msgid "Customize the map height"
msgstr "Kartenhöhe anpassen"

#: includes/fields/class-acf-field-google-map.php:221
#: includes/fields/class-acf-field-image.php:249
#: includes/fields/class-acf-field-image.php:285
#: includes/fields/class-acf-field-oembed.php:253
msgid "Height"
msgstr "Höhe"

#: includes/fields/class-acf-field-google-map.php:210
msgid "Set the initial zoom level"
msgstr "Den Anfangswert für Zoom einstellen"

#: includes/fields/class-acf-field-google-map.php:209
msgid "Zoom"
msgstr "Zoom"

#: includes/fields/class-acf-field-google-map.php:183
#: includes/fields/class-acf-field-google-map.php:196
msgid "Center the initial map"
msgstr "Ausgangskarte zentrieren"

#: includes/fields/class-acf-field-google-map.php:182
#: includes/fields/class-acf-field-google-map.php:195
msgid "Center"
msgstr "Zentriert"

#: includes/fields/class-acf-field-google-map.php:154
msgid "Search for address..."
msgstr "Nach der Adresse suchen ..."

#: includes/fields/class-acf-field-google-map.php:151
msgid "Find current location"
msgstr "Aktuelle Position finden"

#: includes/fields/class-acf-field-google-map.php:150
msgid "Clear location"
msgstr "Position löschen"

#: includes/fields/class-acf-field-google-map.php:149
#: includes/fields/class-acf-field-relationship.php:589
msgid "Search"
msgstr "Suchen"

#: includes/fields/class-acf-field-google-map.php:57
#: assets/build/js/acf-input.js:3528 assets/build/js/acf-input.js:3786
msgid "Sorry, this browser does not support geolocation"
msgstr "Dieser Browser unterstützt leider keine Standortbestimmung"

#: includes/fields/class-acf-field-google-map.php:22
msgid "Google Map"
msgstr "Google Maps"

#: includes/fields/class-acf-field-date_picker.php:201
#: includes/fields/class-acf-field-date_time_picker.php:189
#: includes/fields/class-acf-field-time_picker.php:122
msgid "The format returned via template functions"
msgstr "Das über Template-Funktionen zurückgegebene Format"

#: includes/fields/class-acf-field-color_picker.php:168
#: includes/fields/class-acf-field-date_picker.php:200
#: includes/fields/class-acf-field-date_time_picker.php:188
#: includes/fields/class-acf-field-icon_picker.php:260
#: includes/fields/class-acf-field-image.php:177
#: includes/fields/class-acf-field-post_object.php:385
#: includes/fields/class-acf-field-relationship.php:599
#: includes/fields/class-acf-field-select.php:364
#: includes/fields/class-acf-field-time_picker.php:121
#: includes/fields/class-acf-field-user.php:95
msgid "Return Format"
msgstr "Rückgabeformat"

#: includes/fields/class-acf-field-date_picker.php:179
#: includes/fields/class-acf-field-date_picker.php:210
#: includes/fields/class-acf-field-date_time_picker.php:180
#: includes/fields/class-acf-field-date_time_picker.php:198
#: includes/fields/class-acf-field-time_picker.php:113
#: includes/fields/class-acf-field-time_picker.php:129
msgid "Custom:"
msgstr "Individuell:"

#: includes/fields/class-acf-field-date_picker.php:171
#: includes/fields/class-acf-field-date_time_picker.php:171
#: includes/fields/class-acf-field-time_picker.php:106
msgid "The format displayed when editing a post"
msgstr "Das angezeigte Format beim Bearbeiten eines Beitrags"

#: includes/fields/class-acf-field-date_picker.php:170
#: includes/fields/class-acf-field-date_time_picker.php:170
#: includes/fields/class-acf-field-time_picker.php:105
msgid "Display Format"
msgstr "Darstellungsformat"

#: includes/fields/class-acf-field-time_picker.php:22
msgid "Time Picker"
msgstr "Zeitpicker"

#. translators: counts for inactive field groups
#: acf.php:506
msgid "Inactive <span class=\"count\">(%s)</span>"
msgid_plural "Inactive <span class=\"count\">(%s)</span>"
msgstr[0] "Deaktiviert <span class=\"count\">(%s)</span>"
msgstr[1] "Deaktiviert <span class=\"count\">(%s)</span>"

#: acf.php:467
msgid "No Fields found in Trash"
msgstr "Es wurden keine Felder im Papierkorb gefunden"

#: acf.php:466
msgid "No Fields found"
msgstr "Es wurden keine Felder gefunden"

#: acf.php:465
msgid "Search Fields"
msgstr "Felder suchen"

#: acf.php:464
msgid "View Field"
msgstr "Feld anzeigen"

#: acf.php:463 includes/admin/views/acf-field-group/fields.php:113
msgid "New Field"
msgstr "Neues Feld"

#: acf.php:462
msgid "Edit Field"
msgstr "Feld bearbeiten"

#: acf.php:461
msgid "Add New Field"
msgstr "Neues Feld hinzufügen"

#: acf.php:459
msgid "Field"
msgstr "Feld"

#: acf.php:458 includes/admin/post-types/admin-field-group.php:179
#: includes/admin/post-types/admin-field-groups.php:93
#: includes/admin/views/acf-field-group/fields.php:32
msgid "Fields"
msgstr "Felder"

#: acf.php:433
msgid "No Field Groups found in Trash"
msgstr "Es wurden keine Feldgruppen im Papierkorb gefunden"

#: acf.php:432
msgid "No Field Groups found"
msgstr "Es wurden keine Feldgruppen gefunden"

#: acf.php:431
msgid "Search Field Groups"
msgstr "Feldgruppen durchsuchen"

#: acf.php:430
msgid "View Field Group"
msgstr "Feldgruppe anzeigen"

#: acf.php:429
msgid "New Field Group"
msgstr "Neue Feldgruppe"

#: acf.php:428
msgid "Edit Field Group"
msgstr "Feldgruppe bearbeiten"

#: acf.php:427
msgid "Add New Field Group"
msgstr "Neue Feldgruppe hinzufügen"

#: acf.php:426 acf.php:460
#: includes/admin/views/acf-post-type/advanced-settings.php:224
#: includes/post-types/class-acf-post-type.php:93
#: includes/post-types/class-acf-taxonomy.php:92
msgid "Add New"
msgstr "Neu hinzufügen"

#: acf.php:425
msgid "Field Group"
msgstr "Feldgruppe"

#: acf.php:424 includes/admin/post-types/admin-field-groups.php:55
#: includes/admin/post-types/admin-post-types.php:113
#: includes/admin/post-types/admin-taxonomies.php:112
msgid "Field Groups"
msgstr "Feldgruppen"

#. Description of the plugin
#: acf.php
msgid "Customize WordPress with powerful, professional and intuitive fields."
msgstr ""
"WordPress durch leistungsfähige, professionelle und zugleich intuitive "
"Felder erweitern."

#. Plugin URI of the plugin
#: acf.php
msgid "https://www.advancedcustomfields.com"
msgstr "https://www.advancedcustomfields.com"

#. Plugin Name of the plugin
#: acf.php acf.php:93
msgid "Advanced Custom Fields"
msgstr "Advanced Custom Fields"

#: pro/acf-pro.php:27
msgid "Advanced Custom Fields PRO"
msgstr "Advanced Custom Fields PRO"

#: pro/blocks.php:170
msgid "Block type name is required."
msgstr "Name des Block-Typs wird benötigt."

#. translators: The name of the block type
#: pro/blocks.php:178
msgid "Block type \"%s\" is already registered."
msgstr "Block-Typ „%s“ ist bereits registriert."

#: pro/blocks.php:726
msgid "Switch to Edit"
msgstr "Zum Bearbeiten wechseln"

#: pro/blocks.php:727
msgid "Switch to Preview"
msgstr "Zur Vorschau wechseln"

#: pro/blocks.php:728
msgid "Change content alignment"
msgstr "Ausrichtung des Inhalts ändern"

#. translators: %s: Block type title
#: pro/blocks.php:731
msgid "%s settings"
msgstr "%s Einstellungen"

#: pro/blocks.php:936
msgid "This block contains no editable fields."
msgstr ""

#. translators: %s: an admin URL to the field group edit screen
#: pro/blocks.php:942
msgid ""
"Assign a <a href=\"%s\" target=\"_blank\">field group</a> to add fields to "
"this block."
msgstr ""

#: pro/options-page.php:78
msgid "Options Updated"
msgstr "Optionen aktualisiert"

#: pro/updates.php:99
msgid ""
"To enable updates, please enter your license key on the <a "
"href=\"%1$s\">Updates</a> page. If you don't have a licence key, please see "
"<a href=\"%2$s\" target=\"_blank\">details & pricing</a>."
msgstr ""

#: pro/updates.php:159
msgid ""
"<b>ACF Activation Error</b>. Your defined license key has changed, but an "
"error occurred when deactivating your old licence"
msgstr ""

#: pro/updates.php:154
msgid ""
"<b>ACF Activation Error</b>. Your defined license key has changed, but an "
"error occurred when connecting to activation server"
msgstr ""

#: pro/updates.php:192
msgid "<b>ACF Activation Error</b>"
msgstr ""

#: pro/updates.php:187
msgid ""
"<b>ACF Activation Error</b>. An error occurred when connecting to activation "
"server"
msgstr ""

#: pro/updates.php:279
msgid "Check Again"
msgstr "Erneut suchen"

#: pro/updates.php:593
msgid "<b>ACF Activation Error</b>. Could not connect to activation server"
msgstr ""

#: pro/admin/admin-options-page.php:195
msgid "Publish"
msgstr "Veröffentlichen"

#: pro/admin/admin-options-page.php:199
msgid ""
"No Custom Field Groups found for this options page. <a href=\"%s\">Create a "
"Custom Field Group</a>"
msgstr ""
"Keine Feldgruppen für diese Options-Seite gefunden. <a href=\"%s\">Eine "
"Feldgruppe erstellen</a>"

#: pro/admin/admin-updates.php:52
msgid "<b>Error</b>. Could not connect to update server"
msgstr ""
"<b>Fehler</b>. Es konnte keine Verbindung zum Aktualisierungsserver "
"hergestellt werden"

#: pro/admin/admin-updates.php:212
msgid ""
"<b>Error</b>. Could not authenticate update package. Please check again or "
"deactivate and reactivate your ACF PRO license."
msgstr ""
"<b>Fehler</b>. Das Aktualisierungspaket konnte nicht authentifiziert werden. "
"Bitte probieren Sie es nochmal oder deaktivieren und reaktivieren Sie ihre "
"ACF PRO-Lizenz."

#: pro/admin/admin-updates.php:199
msgid ""
"<b>Error</b>. Your license for this site has expired or been deactivated. "
"Please reactivate your ACF PRO license."
msgstr ""

#: pro/fields/class-acf-field-clone.php:27,
#: pro/fields/class-acf-field-repeater.php:31
msgid ""
"Allows you to select and display existing fields. It does not duplicate any "
"fields in the database, but loads and displays the selected fields at run-"
"time. The Clone field can either replace itself with the selected fields or "
"display the selected fields as a group of subfields."
msgstr ""

#: pro/fields/class-acf-field-clone.php:819
msgid "Select one or more fields you wish to clone"
msgstr "Wählen Sie ein oder mehrere Felder aus die Sie klonen möchten"

#: pro/fields/class-acf-field-clone.php:838
msgid "Display"
msgstr "Anzeige"

#: pro/fields/class-acf-field-clone.php:839
msgid "Specify the style used to render the clone field"
msgstr "Geben Sie den Stil an mit dem das Klon-Feld angezeigt werden soll"

#: pro/fields/class-acf-field-clone.php:844
msgid "Group (displays selected fields in a group within this field)"
msgstr ""
"Gruppe (zeigt die ausgewählten Felder in einer Gruppe innerhalb dieses "
"Feldes an)"

#: pro/fields/class-acf-field-clone.php:845
msgid "Seamless (replaces this field with selected fields)"
msgstr "Nahtlos (ersetzt dieses Feld mit den ausgewählten Feldern)"

#: pro/fields/class-acf-field-clone.php:868
msgid "Labels will be displayed as %s"
msgstr "Beschriftungen werden als %s angezeigt"

#: pro/fields/class-acf-field-clone.php:873
msgid "Prefix Field Labels"
msgstr "Präfix für Feldbeschriftungen"

#: pro/fields/class-acf-field-clone.php:883
msgid "Values will be saved as %s"
msgstr "Werte werden als %s gespeichert"

#: pro/fields/class-acf-field-clone.php:888
msgid "Prefix Field Names"
msgstr "Präfix für Feldnamen"

#: pro/fields/class-acf-field-clone.php:1005
msgid "Unknown field"
msgstr "Unbekanntes Feld"

#: pro/fields/class-acf-field-clone.php:1042
msgid "Unknown field group"
msgstr "Unbekannte Feldgruppe"

#: pro/fields/class-acf-field-clone.php:1046
msgid "All fields from %s field group"
msgstr "Alle Felder der Feldgruppe %s"

#: pro/fields/class-acf-field-flexible-content.php:27
msgid ""
"Allows you to define, create and manage content with total control by "
"creating layouts that contain subfields that content editors can choose from."
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:36,
#: pro/fields/class-acf-field-repeater.php:103,
#: pro/fields/class-acf-field-repeater.php:297
msgid "Add Row"
msgstr "Eintrag hinzufügen"

#: pro/fields/class-acf-field-flexible-content.php:76,
#: pro/fields/class-acf-field-flexible-content.php:943,
#: pro/fields/class-acf-field-flexible-content.php:1022
msgid "layout"
msgid_plural "layouts"
msgstr[0] "Layout"
msgstr[1] "Layouts"

#: pro/fields/class-acf-field-flexible-content.php:77
msgid "layouts"
msgstr "Einträge"

#: pro/fields/class-acf-field-flexible-content.php:81,
#: pro/fields/class-acf-field-flexible-content.php:942,
#: pro/fields/class-acf-field-flexible-content.php:1021
msgid "This field requires at least {min} {label} {identifier}"
msgstr "Dieses Feld erfordert mindestens {min} {label} {identifier}"

#: pro/fields/class-acf-field-flexible-content.php:82
msgid "This field has a limit of {max} {label} {identifier}"
msgstr "Dieses Feld erlaubt höchstens {max} {label} {identifier}"

#: pro/fields/class-acf-field-flexible-content.php:85
msgid "{available} {label} {identifier} available (max {max})"
msgstr "{available} {label} {identifier} möglich (max {max})"

#: pro/fields/class-acf-field-flexible-content.php:86
msgid "{required} {label} {identifier} required (min {min})"
msgstr "{required} {label} {identifier} erforderlich (min {min})"

#: pro/fields/class-acf-field-flexible-content.php:89
msgid "Flexible Content requires at least 1 layout"
msgstr "Flexibler Inhalt benötigt mindestens ein Layout"

#: pro/fields/class-acf-field-flexible-content.php:282
msgid "Click the \"%s\" button below to start creating your layout"
msgstr "Klicke \"%s\" zum Erstellen des Layouts"

#: pro/fields/class-acf-field-flexible-content.php:423
msgid "Add layout"
msgstr "Layout hinzufügen"

#: pro/fields/class-acf-field-flexible-content.php:424
msgid "Duplicate layout"
msgstr "Layout duplizieren"

#: pro/fields/class-acf-field-flexible-content.php:425
msgid "Remove layout"
msgstr "Layout entfernen"

#: pro/fields/class-acf-field-flexible-content.php:426,
#: pro/fields/class-acf-repeater-table.php:382
msgid "Click to toggle"
msgstr "Zum Auswählen anklicken"

#: pro/fields/class-acf-field-flexible-content.php:562
msgid "Delete Layout"
msgstr "Layout löschen"

#: pro/fields/class-acf-field-flexible-content.php:563
msgid "Duplicate Layout"
msgstr "Layout duplizieren"

#: pro/fields/class-acf-field-flexible-content.php:564
msgid "Add New Layout"
msgstr "Neues Layout hinzufügen"

#: pro/fields/class-acf-field-flexible-content.php:564
#, fuzzy
#| msgid "Add layout"
msgid "Add Layout"
msgstr "Layout hinzufügen"

#: pro/fields/class-acf-field-flexible-content.php:647
msgid "Min"
msgstr "Min"

#: pro/fields/class-acf-field-flexible-content.php:662
msgid "Max"
msgstr "Max"

#: pro/fields/class-acf-field-flexible-content.php:705
msgid "Minimum Layouts"
msgstr "Mindestzahl an Layouts"

#: pro/fields/class-acf-field-flexible-content.php:716
msgid "Maximum Layouts"
msgstr "Höchstzahl an Layouts"

#: pro/fields/class-acf-field-flexible-content.php:727,
#: pro/fields/class-acf-field-repeater.php:293
msgid "Button Label"
msgstr "Button-Beschriftung"

#: pro/fields/class-acf-field-flexible-content.php:1710,
#: pro/fields/class-acf-field-repeater.php:918
msgid "%s must be of type array or null."
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:1721
msgid "%1$s must contain at least %2$s %3$s layout."
msgid_plural "%1$s must contain at least %2$s %3$s layouts."
msgstr[0] ""
msgstr[1] ""

#: pro/fields/class-acf-field-flexible-content.php:1737
msgid "%1$s must contain at most %2$s %3$s layout."
msgid_plural "%1$s must contain at most %2$s %3$s layouts."
msgstr[0] ""
msgstr[1] ""

#: pro/fields/class-acf-field-gallery.php:27
msgid ""
"An interactive interface for managing a collection of attachments, such as "
"images."
msgstr ""

#: pro/fields/class-acf-field-gallery.php:77
msgid "Add Image to Gallery"
msgstr "Bild zur Galerie hinzufügen"

#: pro/fields/class-acf-field-gallery.php:78
msgid "Maximum selection reached"
msgstr "Maximale Auswahl erreicht"

#: pro/fields/class-acf-field-gallery.php:324
msgid "Length"
msgstr "Länge"

#: pro/fields/class-acf-field-gallery.php:368
msgid "Caption"
msgstr "Bildunterschrift"

#: pro/fields/class-acf-field-gallery.php:380
msgid "Alt Text"
msgstr "Alt Text"

#: pro/fields/class-acf-field-gallery.php:504
msgid "Add to gallery"
msgstr "Zur Galerie hinzufügen"

#: pro/fields/class-acf-field-gallery.php:508
msgid "Bulk actions"
msgstr "Massenverarbeitung"

#: pro/fields/class-acf-field-gallery.php:509
msgid "Sort by date uploaded"
msgstr "Sortiere nach Upload-Datum"

#: pro/fields/class-acf-field-gallery.php:510
msgid "Sort by date modified"
msgstr "Sortiere nach Änderungs-Datum"

#: pro/fields/class-acf-field-gallery.php:511
msgid "Sort by title"
msgstr "Sortiere nach Titel"

#: pro/fields/class-acf-field-gallery.php:512
msgid "Reverse current order"
msgstr "Aktuelle Sortierung umkehren"

#: pro/fields/class-acf-field-gallery.php:524
msgid "Close"
msgstr "Schließen"

#: pro/fields/class-acf-field-gallery.php:615
msgid "Minimum Selection"
msgstr "Minimale Auswahl"

#: pro/fields/class-acf-field-gallery.php:625
msgid "Maximum Selection"
msgstr "Maximale Auswahl"

#: pro/fields/class-acf-field-gallery.php:707
msgid "Allowed file types"
msgstr "Erlaubte Dateiformate"

#: pro/fields/class-acf-field-gallery.php:727
msgid "Insert"
msgstr "Einfügen"

#: pro/fields/class-acf-field-gallery.php:728
msgid "Specify where new attachments are added"
msgstr "Geben Sie an wo neue Anhänge hinzugefügt werden sollen"

#: pro/fields/class-acf-field-gallery.php:732
msgid "Append to the end"
msgstr "Anhängen"

#: pro/fields/class-acf-field-gallery.php:733
msgid "Prepend to the beginning"
msgstr "Voranstellen"

#: pro/fields/class-acf-field-repeater.php:66,
#: pro/fields/class-acf-field-repeater.php:463
#, fuzzy
#| msgid "Minimum rows reached ({min} rows)"
msgid "Minimum rows not reached ({min} rows)"
msgstr "Mindestzahl der Einträge hat ({min} Reihen) erreicht"

#: pro/fields/class-acf-field-repeater.php:67
msgid "Maximum rows reached ({max} rows)"
msgstr "Höchstzahl der Einträge hat ({max} Reihen) erreicht"

#: pro/fields/class-acf-field-repeater.php:68
msgid "Error loading page"
msgstr ""

#: pro/fields/class-acf-field-repeater.php:69
msgid "Order will be assigned upon save"
msgstr ""

#: pro/fields/class-acf-field-repeater.php:196
msgid "Useful for fields with a large number of rows."
msgstr ""

#: pro/fields/class-acf-field-repeater.php:207
msgid "Rows Per Page"
msgstr ""

#: pro/fields/class-acf-field-repeater.php:208
msgid "Set the number of rows to be displayed on a page."
msgstr ""

#: pro/fields/class-acf-field-repeater.php:240
msgid "Minimum Rows"
msgstr "Mindestzahl der Einträge"

#: pro/fields/class-acf-field-repeater.php:251
msgid "Maximum Rows"
msgstr "Höchstzahl der Einträge"

#: pro/fields/class-acf-field-repeater.php:281
msgid "Collapsed"
msgstr "Zugeklappt"

#: pro/fields/class-acf-field-repeater.php:282
msgid "Select a sub field to show when row is collapsed"
msgstr ""
"Wähle ein Unterfelder welches im zugeklappten Zustand angezeigt werden soll"

#: pro/fields/class-acf-field-repeater.php:1060
#, fuzzy
#| msgid "Invalid field group ID."
msgid "Invalid field key or name."
msgstr "Ungültige Feldgruppen-ID."

#: pro/fields/class-acf-field-repeater.php:1069
msgid "There was an error retrieving the field."
msgstr ""

#: pro/fields/class-acf-repeater-table.php:369
#, fuzzy
#| msgid "Drag to reorder"
msgid "Click to reorder"
msgstr "Ziehen zum Sortieren"

#: pro/fields/class-acf-repeater-table.php:402
msgid "Add row"
msgstr "Eintrag hinzufügen"

#: pro/fields/class-acf-repeater-table.php:403
msgid "Duplicate row"
msgstr "Zeile duplizieren"

#: pro/fields/class-acf-repeater-table.php:404
msgid "Remove row"
msgstr "Eintrag löschen"

#: pro/fields/class-acf-repeater-table.php:448,
#: pro/fields/class-acf-repeater-table.php:465,
#: pro/fields/class-acf-repeater-table.php:466
msgid "Current Page"
msgstr ""

#: pro/fields/class-acf-repeater-table.php:456,
#: pro/fields/class-acf-repeater-table.php:457
#, fuzzy
#| msgid "Front Page"
msgid "First Page"
msgstr "Startseite"

#: pro/fields/class-acf-repeater-table.php:460,
#: pro/fields/class-acf-repeater-table.php:461
#, fuzzy
#| msgid "Posts Page"
msgid "Previous Page"
msgstr "Beitrags-Seite"

#. translators: 1: Current page, 2: Total pages.
#: pro/fields/class-acf-repeater-table.php:470
msgctxt "paging"
msgid "%1$s of %2$s"
msgstr ""

#: pro/fields/class-acf-repeater-table.php:477,
#: pro/fields/class-acf-repeater-table.php:478
#, fuzzy
#| msgid "Front Page"
msgid "Next Page"
msgstr "Startseite"

#: pro/fields/class-acf-repeater-table.php:481,
#: pro/fields/class-acf-repeater-table.php:482
#, fuzzy
#| msgid "Posts Page"
msgid "Last Page"
msgstr "Beitrags-Seite"

#: pro/locations/class-acf-location-block.php:71
msgid "No block types exist"
msgstr "Keine Blocktypen vorhanden"

#: pro/locations/class-acf-location-options-page.php:70
msgid "No options pages exist"
msgstr "Keine Options-Seiten vorhanden"

#: pro/admin/views/html-settings-updates.php:6
msgid "Deactivate License"
msgstr "Lizenz deaktivieren"

#: pro/admin/views/html-settings-updates.php:6
msgid "Activate License"
msgstr "Lizenz aktivieren"

#: pro/admin/views/html-settings-updates.php:16
msgid "License Information"
msgstr "Lizenzinformation"

#: pro/admin/views/html-settings-updates.php:34
msgid ""
"To unlock updates, please enter your license key below. If you don't have a "
"licence key, please see <a href=\"%s\" target=\"_blank\">details & pricing</"
"a>."
msgstr ""
"Um die Aktualisierungsfähigkeit freizuschalten geben Sie bitte unten Ihren "
"Lizenzschlüssel ein. Falls Sie keinen besitzen sollten informieren Sie sich "
"bitte hier hinsichtlich <a href=\"%s\" target=\"_blank\">Preisen und aller "
"weiteren Details</a>."

#: pro/admin/views/html-settings-updates.php:37
msgid "License Key"
msgstr "Lizenzschlüssel"

#: pro/admin/views/html-settings-updates.php:22
msgid "Your license key is defined in wp-config.php."
msgstr ""

#: pro/admin/views/html-settings-updates.php:29
msgid "Retry Activation"
msgstr ""

#: pro/admin/views/html-settings-updates.php:61
msgid "Update Information"
msgstr "Aktualisierungsinformationen"

#: pro/admin/views/html-settings-updates.php:68
msgid "Current Version"
msgstr "Installierte Version"

#: pro/admin/views/html-settings-updates.php:76
msgid "Latest Version"
msgstr "Aktuellste Version"

#: pro/admin/views/html-settings-updates.php:84
msgid "Update Available"
msgstr "Aktualisierung verfügbar"

#: pro/admin/views/html-settings-updates.php:98
msgid "Upgrade Notice"
msgstr "Hinweis zum Upgrade"

#: pro/admin/views/html-settings-updates.php:126
msgid "Check For Updates"
msgstr ""

#: pro/admin/views/html-settings-updates.php:121
#, fuzzy
#| msgid "Please enter your license key above to unlock updates"
msgid "Enter your license key to unlock updates"
msgstr ""
"Bitte geben Sie oben Ihren Lizenzschlüssel ein um die "
"Aktualisierungsfähigkeit freizuschalten"

#: pro/admin/views/html-settings-updates.php:119
msgid "Update Plugin"
msgstr "Plugin aktualisieren"

#: pro/admin/views/html-settings-updates.php:117
msgid "Please reactivate your license to unlock updates"
msgstr ""
