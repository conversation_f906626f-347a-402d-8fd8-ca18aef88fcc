<?php

/* @wordpress-plugin
 * Plugin Name:       Speed Tweaks
 * Description:       Local version of jQuery for performance and security
 * Version:           0.1
 * Author:            <PERSON>
 * Author URI:        https://theuniversal.co/
 * Text Domain:       speed-tweaks
 * Domain Path:       /languages
 */


/* Local jQuery */

function custom_dequeue_jquery()
{

    if (!is_admin_or_login()) {
        // Dequeue the existing jQuery
        wp_dequeue_script('jquery');
        wp_deregister_script('jquery');

        // Register and enqueue local jQuery
        wp_register_script('jquery', plugin_dir_url( __FILE__ ) . 'public/jquery.min.js', false, '3.7.1', false);
        wp_enqueue_script('jquery');
    }
}

add_action('wp_enqueue_scripts', 'custom_dequeue_jquery', 10);

function is_admin_or_login()
{
    // Are we logged in, or requesting the login page?
    return (is_admin() || $GLOBALS['pagenow'] === 'wp-login.php');
}
