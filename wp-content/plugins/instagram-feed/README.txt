=== Smash Balloon Social Photo Feed – Easy Social Feeds Plugin ===
Contributors: smashballoon, craig-at-smash-balloon, am, smub
Tags: Instagram, Instagram feed, Instagram photos, Instagram widget, Instagram gallery
Requires at least: 4.1
Tested up to: 6.6
Stable tag: 6.4.3
Requires PHP: 5.6
License: GPLv2 or later
License URI: http://www.gnu.org/licenses/gpl-2.0.html

Formerly "Instagram Feed". Display clean, customizable, and responsive Instagram feeds from multiple accounts. Supports Instagram oEmbeds.

== Description ==

Display Instagram posts from your Instagram accounts, either in the same single feed or in multiple different ones. The perfect Instagram gallery plugin for your WordPress site.

https://www.youtube.com/watch?v=517rApIqzbw

= Features =
* **New:** Now automatically powers your **Instagram oEmbeds**
* Super **simple to set up**
* Display photos from **multiple Instagram accounts** in the same feed or in separate feeds
* Completely **responsive** and mobile ready - layout looks great on any screen size and in any container width
* **Completely customizable** - Customize the width, height, number of photos, number of columns, image size, background color, image spacing and more!
* Display **multiple Instagram feeds** on the same page or on different pages throughout your site
* **GDPR Compliance** - automatically integrates with many of the popular GDPR cookie consent plugins and includes a 1-click easy GDPR setting.
* Use the drag-and-drop customizer to completely customize each of your Instagram feeds
* Display thumbnail, medium or **full-size photos** from your Instagram feed
* **Infinitely load more** of your Instagram photos with the 'Load More' button
* Includes a **Follow on Instagram button** at the bottom of your feed
* Display a **beautiful header** at the top of your feed
* Display your Instagram photos chronologically or in random order
* Add your own Custom CSS and JavaScript for even deeper customizations
* Handy block for easily adding your feed to posts and pages using the block editor
* Integrations with Elementor and Divi page builders. Instagram for Elementor is super simple

= Benefits =
* **Increase Social Engagement** - Increase engagement between you and your Instagram followers. Increase your number of followers by displaying your Instagram content directly on your site.
* **Save Time** - Don't have time to update your photos on your site? Save time and increase efficiency by only posting your photos to Instagram and automatically displaying them on your website
* **Display Your Content Your Way** - Customize your Instagram feeds to look exactly the way you want, so that they blend seamlessly into your site or pop out at your visitors!
* **Keep Your Site Looking Fresh** - Automatically push your new Instagram content straight to your site to keep it looking fresh and keeping your audience engaged.
* **Super simple to set up** - Once installed, you can be displaying your Instagram photos within 30 seconds! No confusing steps or Instagram Developer account needed.
* **Powers all Instagram oEmbeds on your site** - With WordPress removing support for Instagram oEmbeds, the plugin will now power all Instagram embeds on your site, old and new, to allow them to continue working.

= Pro Version =
In order to maintain the free version of the plugin on an ongoing basis, and to provide quick and effective support for free, we offer a Pro version of the plugin. The Pro version allows you to:
* Display Hashtag feeds
* View photos and videos in a popup lightbox directly on your site
* View post comments for user feeds
* Display the number of like and comments for each post
* Create carousels from your posts
* Use "Masonry" or "Highlight" layouts for your feeds
* Display captions for photos and videos
* Filter posts based on hashtag/word
* Advanced moderation system for hiding/showing certain posts
* Block posts by specific users
* Create "shoppable" Instagram feeds, and more.
* Add custom links to each post like link in bio
* Quick create templates for easy feed creation

[Find out more about the Pro version](https://smashballoon.com/instagram-feed/?utm_campaign=instagram-free-readme&utm_source=proversion&utm_medium=profindout "Instagram Feed Pro") or [try out the Pro demo](https://smashballoon.com/instagram-feed/demo/?utm_campaign=instagram-free-readme&utm_source=proversion&utm_medium=readmedemo "Instagram Feed Pro Demo").

= Featured Reviews =
"**Simple and concise** - Excellent plugin. Simple and non-bloated. I had a couple small issues with the plugin when I first started using it, but a quick comment on the support forums got a new version pushed out the next day with the fix. Awesome support!" - [Josh Jones](https://wordpress.org/support/topic/simple-and-concise-3 'Simple and concise Instagram plugin')

"**Great plugin, greater support!** - I've definitely noticed an increase in followers on Instagram since I added this plugin to my sidebar. Thanks for the help in making some adjustments...looks and works great!" - [BNOTP](https://wordpress.org/support/topic/thanks-for-a-great-plugin-6 'Great plugin, greater Support!')

= Feedback or Support =
We're dedicated to providing the most customizable, robust and well supported Instagram feed plugin in the world, so if you have an issue or have any feedback on how to improve the plugin then please open a ticket in the [Support forum](http://wordpress.org/support/plugin/instagram-feed 'Instagram Feed Support Forum').

For a pop-up photo **lightbox**, to display posts by **hashtag**, show photo **captions**, **video** support + more, check out the [Pro version](http://smashballoon.com/instagram-feed/?utm_campaign=instagram-free-readme&utm_source=feedbacj&utm_medium=support 'Instagram Feed Pro').

== Installation ==

1. Install the Instagram Feed plugin either via the WordPress plugin directory, or by uploading the files to your web server (in the `/wp-content/plugins/` directory).
2. Activate the Instagram Feed plugin through the 'Plugins' menu in WordPress.
3. Navigate to the 'Instagram Feed' settings page to connect your Instagram account.
4. Use the shortcode `[instagram-feed feed=1]` in your page, post or widget to display your Instagram photos.
5. You can display multiple Instagram feeds by using shortcode options, for example: `[instagram-feed num=6 cols=3]`

For simple step-by-step directions on how to set up the Instagram Feed plugin please refer to our [setup guide](http://smashballoon.com/instagram-feed/free/?utm_campaign=instagram-free-readme&utm_source=installation&utm_medium=setup 'Instagram Feed setup guide').

= Display your Feed =

**Single Feed**

Copy and paste the following shortcode directly into the page, post or widget where you'd like the Instagram feed to show up: `[instagram-feed]`

**Multiple Feeds**

If you'd like to display multiple Instagram feeds then you can create multiple feeds and specify which feed you'd like to display using the feed ID like so: `[instagram-feed feed=2]`

If you'd like to display feed from more than one account, connect multiple accounts and then create an additional feed. Embed it with the feed ID in the shortcode: `[instagram-feed feed=3]`

You can display as many different Instagram feeds as you like, on either the same page or on different pages, by just using the shortcode options and the assigned feed ID below. For example:
`[instagram-feed feed=1]`
`[instagram-feed feed=2]`
`[instagram-feed feed=3]`

= Setting up the Free Instagram Feed WordPress Plugin =

1) Once you've installed the Instagram Feed plugin click on the Instagram Feed item in your WordPress menu

2) Follow the onboarding steps to connect an account and create your first feed.

3) Customize your feed with tons of settings and options to display it just how you would like.

4) Once you've customized your feed, copy the [instagram-feed feed=1] shortcode.

5) Paste it into any page, post or widget where you want the feed to appear. Alternatively, use our handy block to add it to a page.

6) You can use our handy Instagram Feed widget to display a feed in a sidebar or other widget area.

== Frequently Asked Questions ==

= Can I display multiple Instagram feeds on my site or on the same page? =

Yep. You can display multiple Instagram feeds by using our built-in feed creation tool, for example: `[instagram-feed feed=2]`.

= Can I display photos from more than one Instagram account in one single feed? =

Yep. You can connect multiple accounts and include them as sources when creating a feed.

= Does the plugin work with Instagram oEmbeds? =

Yes. In version 2.5, support was added to allow the plugin to power your Instagram oEmbeds as official support for these is no longer available in WordPress core. Just connect your account on the oEmbeds settings page inside the plugin and we'll do the rest. No developer app or account required.

= Can I display a feed with in a widget or with a block? =

Yes. Use the Instagram widget or Instagram block to display your feed in a sidebar or other widget area.

= How do I connect my Instagram account? =

We've made it super easy. Just follow the steps outlined when onboarding and you will have a connected account with simple clicks.

= My feed isn't displaying. Why not!? =

There are a few common reasons for this:

* **Your Access Token may not be valid.** Try clicking on the blue Instagram login button on the plugin's Settings page again and copy and paste the Instagram token it gives you into the plugin's Access Token field.
* **The plugin's JavaScript file isn't included in your page.** This is most likely because your WordPress theme is missing the WordPress [wp_footer](http://codex.wordpress.org/Function_Reference/wp_footer) function which is required for plugins to be able to add their JavaScript files to your page. You can fix this by opening your theme's **footer.php** file and adding the following directly before the closing </body> tag between two php tags: `wp_footer();`
* **Your website may contain a JavaScript error which is preventing JavaScript from running.** The plugin uses JavaScript to load the Instagram photos into your page and so needs JavaScript to be running in order to work. You would need to remove any existing JavaScript errors on your website for the plugin to be able to load in your feed.

If you're still having an issue displaying your feed then please open a ticket in the [Support forum](http://wordpress.org/support/plugin/instagram-feed 'Instagram Feed Support Forum') with a link to the page where you're trying to display the Instagram feed and, if possible, a link to your Instagram account.

= Are there any security issues with using an Access Token on my site? =

Nope. The Access Token used in the plugin is a "read only" token, which means that it could never be used maliciously to manipulate your account.

= Can I view the full-size photos or play Instagram videos directly on my website?  =

This is a feature of the [Pro version](http://smashballoon.com/instagram-feed/?utm_campaign=instagram-free-readme&utm_source=faqs&utm_medium=fullsize 'Instagram Feed Pro') of the plugin, which allows you to view the photos in a pop-up lightbox, support videos, display captions, display photos by hashtag + more!

= How do I embed my feed directly into a WordPress page template? =

You can embed your feed directly into a template file by using the WordPress [do_shortcode](http://codex.wordpress.org/Function_Reference/do_shortcode) function: `<?php echo do_shortcode('[instagram-feed]'); ?>`.

= My Feed Stopped Working – All I see is a Loading Symbol =

If your Instagram photos aren't loading and all your see is a loading symbol then there are a few common reasons:

1) There's an issue with the Instagram Access Token that you are using

You can obtain a new access token on the Instagram Feed Settings page by using the "connect" button to reconnect the account.

Occasionally the connect button inside the plugin does not successfully update the access token. You can try [this link](https://smashballoon.com/instagram-feed/token/?utm_campaign=instagram-free-readme&utm_source=faqs&utm_medium=faqconnectionissue) as well.

2) The plugin's JavaScript file isn't being included in your page

This is most likely because your WordPress theme is missing the WordPress wp_footer function which is required for plugins to be able to add their JavaScript files to your page. You can fix this by opening your theme's footer.php file and adding the following directly before the closing </body> tag:

<?php wp_footer(); ?>

3) There's a JavaScript error on your site which is preventing the plugin's JavaScript file from running

You can find out whether this is the case by right clicking on your page, selecting 'Inspect Element', and then clicking on the 'Console' tab, or by selecting the 'JavaScript Console' option from your browser's Developer Tools.

If a JavaScript error is occurring on your site then you'll see it listed in red along with the JavaScript file which is causing it.

4) The feed you are trying to display has no Instagram posts

If you are trying to display an Instagram feed that has no posts made to it, a loading symbol may be all that shows for the Instagram feed or nothing at all. Once you add an Instagram post the Instagram feed should display normally

5) The shortcode you are using is incorrect

You may have an error in the Instagram Feed shortcode you are using or are missing a necessary argument.

= What are the available options that I can use to customize my feed? =

There are plenty of options to customize your feed! See the list below:
* **General Options**
* **width of feed** - The width of your feed.
* **height of feed** - The height of your feed.
* **background color** - The background color of the feed. Any hex color code.
*
* **Photo Options**
* **sorting** - Sort the Instagram posts by Newest to Oldest (none) or Random (random)
* **number of posts** - The number of posts to display initially.
*
* **desktop columns** - The number of columns in your feed when displayed on desktop devices
* **tablet columns** - The number of columns in your feed when displayed on tablet devices
* **mobile columns** - The number of columns in your feed when displayed on mobile or phone devices
*
* **image spacing** - The spacing around your photos
*
* **Header Options**
* **show header** - Whether to show the feed Header.
* **show bio** - Whether to show the account's bio in the feed Header.
* **custom avatar** - URL of a custom Avatar image for the header (use whatever image you want for your account).
*
* **header color** - The color of the feed Header text.
*
* **'Load More' Button Options**
* **show load more button** - Whether to show the 'Load More' button.
* **button color** - The background color of the button. Any hex color code
* **button text color** - The text color of the button.
* **button text** - The text used for the button - Example: "Load More Photos"
*
* **'Follow on Instagram' Button Options**
* **show the follow button ** - Whether to show the 'Follow on Instagram' button.
* **follow color** - The background color of the 'Follow on Instagram' button.
* **follow text color** - The text color of the 'Follow on Instagram' button.
* **follow text** - The text used for the 'Follow on Instagram' button - Example: "Follow me"

For more options, check out the [Pro version](http://smashballoon.com/instagram-feed/?utm_campaign=instagram-free-readme&utm_source=whatare&utm_medium=proshortcode 'Instagram Feed Pro').

For more FAQs related to the Instagram Feed plugin please visit the [FAQ section](https://smashballoon.com/instagram-feed/support/faq/?utm_campaign=instagram-free-readme&utm_source=whatare&utm_medium=faqs 'Instagram Feed plugin FAQs') on our website.

== Screenshots ==

1. Easily display feeds from any of your Instagram accounts
2. Your Instagram Feed is completely customizable and responsive
3. Combine multiple accounts into a single feed
5. Super quick and easy to get started. Just click the button to connect an Instagram account.
5. Customize layouts, styles, colors, and more
6. Just copy and paste the shortcode into any page, post or widget on your site. You can also use the block editor with our handy Instagram Feed block.

== Other Notes ==

Add beautifully clean, customizable, and responsive Instagram feeds to your website. Super simple to set up and has tons of customization options to seamlessly match the look and feel of your site.

= Why do I need this? =

**Increase Social Engagement**
Increase engagement between you and your Instagram followers. Increase your number of Instagram followers by displaying your Instagram content directly on your site.

**Save Time**
Don't have time to update your photos on your site? Save time and increase efficiency by only posting your photos to Instagram and automatically displaying them on your website.

**Display Your Content Your Way**
Customize your Instagram feeds to look exactly the way you want, so that they blend seamlessly into your site or pop out at your visitors!

**Keep Your Site Looking Fresh**
Automatically push your new Instagram content straight to your site to keep it looking fresh and keeping your audience engaged.

**No Coding Required**
Choose from tons of built-in Instagram Feed customization options to create a truly unique feed of your Instagram content.

**Super simple to set up**
Once installed, you can be displaying your Instagram photos within 30 seconds! No confusing steps or Instagram Developer account needed.

**Mind-blowing Customer Support**
We understand that sometimes you need help, have issues or just have questions. We love our users and strive to provide the best support experience in the business. We're experts in the Instagram API and can provide unparalleled service and expertise. If you need support then just let us know and we'll get back to you right away.

= What can it do? =

* Display Instagram photos from any Instagram account you own.
* Completely responsive and mobile ready –your Instagram feed layout looks great on any screen size and in any container width
* Display multiple Instagram feeds on the same page or on different pages throughout your site by using our powerful Instagram Feed shortcode options
* Display posts from multiple Instagram User IDs
* Use the built-in customization options to completely customize each of your Instagram feeds
* Infinitely load more of your Instagram photos with the 'Load More' button
* Plus more features added all the time!

= Completely Customizable =

* By default the Instagram feed will adopt the style of your website, but can be completely customized to look however you like!
* Set the number of Instagram photos you want to display
* Choose how many columns to display your Instagram photos in and the size of the Instagram photos
* Choose to show or hide certain parts of the Instagram feed, such as the header, 'Load More', and 'Follow' buttons
* Control the width, height and background color of your Instagram feed
* Set the spacing/padding between the Instagram photos
* Display Instagram photos in chronological or random order
* Use your own custom text and colors for the 'Load More' and 'Follow' buttons
* Enter your own custom CSS or JavaScript for even deeper customization
* Use the shortcode options to style multiple Instagram feeds in completely different ways
* Plus more customization options added all the time!

= What's Next =

If you like our WordPress Instagram plugin, then consider checking out our other projects:

[OptinMonster](https://optinmonster.com/?utm_campaign=instagram-free&utm_source=readme&utm_medium=whats-next-links) – Get more email subscribers with the most popular conversion optimization plugin for WordPress.
[WPForms](https://wpforms.com/?utm_campaign=instagram-free&utm_source=readme&utm_medium=whats-next-links) – #1 drag & drop online form builder for WordPress (trusted by 5 million sites).
[AIOSEO](https://aioseo.com/?utm_campaign=instagram-free&utm_source=readme&utm_medium=whats-next-links) – The original WordPress SEO plugin to help you rank higher in search results (trusted by over 3 million sites).
[MonsterInsights](https://monsterinsights.com/?utm_campaign=instagram-free&utm_source=readme&utm_medium=whats-next-links) – See the stats that matter and grow your business with confidence. Best Google Analytics plugin for WordPress.
[SeedProd](https://seedprod.com/?utm_campaign=instagram-free&utm_source=readme&utm_medium=whats-next-links) – Create beautiful landing pages with our powerful drag & drop landing page builder.
[WP Mail SMTP](https://wpmailsmtp.com/?utm_campaign=instagram-free&utm_source=readme&utm_medium=whats-next-links) – Improve email deliverability for your contact form with the most popular SMTP plugin for WordPress.
[WPCode](https://wpcode.com/?utm_campaign=instagram-free&utm_source=readme&utm_medium=whats-next-links) - must have WordPress code snippet management plugin to help you future-proof website customization (trusted by 1.5 million sites).
[Duplicator](https://duplicator.com/?utm_campaign=instagram-free&utm_source=readme&utm_medium=whats-next-links) - popular WordPress backup and migration plugin used by over 1 million websites.
[WP Simple Pay](https://wpsimplepay.com/?utm_campaign=instagram-free&utm_source=readme&utm_medium=whats-next-links) – #1 Stripe payments plugin for WordPress. Start accepting one-time or recurring payments without a shopping cart.
[PushEngage](https://pushengage.com/?utm_campaign=instagram-free&utm_source=readme&utm_medium=whats-next-links) – Connect with visitors after they leave your website with the leading web push notification plugin.
[RafflePress](https://rafflepress.com/?utm_campaign=instagram-free&utm_source=readme&utm_medium=whats-next-links) – Best WordPress giveaway and contest plugin to grow traffic and social followers.
[TrustPulse](https://trustpulse.com/?utm_campaign=instagram-free&utm_source=readme&utm_medium=whats-next-links) – Add real-time social proof notifications to boost your store conversions by up to 15%.
[SearchWP](https://searchwp.com/?utm_campaign=instagram-free&utm_source=readme&utm_medium=whats-next-links) – The most advanced custom WordPress search plugin to improve WordPress search quality.
[AffiliateWP](https://affiliatewp.com/?utm_campaign=instagram-free&utm_source=readme&utm_medium=whats-next-links) – #1 affiliate management plugin for WordPress. Add a referral program to your online store.
[Easy Digital Downloads](https://easydigitaldownloads.com/?utm_campaign=instagram-free&utm_source=readme&utm_medium=whats-next-links) – The best WordPress eCommerce plugin to sell digital products (eBooks, software, music, and more).
[WPCharitable](https://wpcharitable.com/?utm_campaign=instagram-free&utm_source=readme&utm_medium=whats-next-links) - Top-rated WordPress donation and fundraising plugin for WordPress.

Visit [WPBeginner](https://wpbeginner.com/?utm_campaign=instagram-free&utm_source=readme&utm_medium=whats-next-links) to learn from our [WordPress Tutorials](https://www.wpbeginner.com/category/wp-tutorials/?utm_campaign=instagram-free&utm_source=readme&utm_medium=whats-next-links) and about the [best WordPress plugins](https://www.wpbeginner.com/category/plugins/?utm_campaign=instagram-free&utm_source=readme&utm_medium=whats-next-links).

== Changelog ==
= 6.4.3 =
* New: Added support for new version of CookieYes GDPR plugin.
* New: Updated the URL for oEmbed auth flow.
* Fix: Resolved conflict with TikTok Feeds and removed assets when callout is not shown.
* Fix: Fixed issues with admin notices not dismissing properly.

= 6.4.2 =
* Fix: PHP fatal error caused by dependencies not being prefixed with the correct namespace.

= 6.4.1 =
* Fix: PHP fatal error that could occur in some circumstances when using the plugin.
* Fix: JS error that occurred on the frontend.

= 6.4 =
* New: Added a checklist and callout feature to help users get started with all Smash Balloon products installed
* Tweak: Added some improvements to our onboarding wizard to make it clear what certain steps do.
* Tweak: Made improvements to our oEmbed connection process.
* Fix: Fixed AJAX themes not loading jQuery dependency causing the feed to break in some circumstances.
* Translations: Updated our translation .pot file.

= 6.3.1 =
* Fix: Fixed some admin notices not dismissing as expected.
* Fix: Fixed an issue that would cause a fatal error when trying to delete the plugin.

= 6.3 =
* New: Updated CSS floats to flexbox and grid for better layout support and compatibility with themes. If you have made customizations in form of CSS, JS, or custom feed templates, make sure your feed looks as expected after updating. If you need time to update your customizations, use the "Legacy CSS" setting on the "Advanced" tab to revert the CSS file to version 6.2.
* New: Improved how images are displayed to support lazy loading.
* Fix: Added support for Borlabs Cookie v3.0.

= 6.2.10 =
* New: Added a menu item to easily install our new [TikTok Feeds](https://wordpress.org/plugins/feeds-for-tiktok/) plugin!

= 6.2.9 =
* New: Added Elementor widget and Divi module for Instagram Feed.
* Tweak: Enhanced the secure custom login tool for the support team to troubleshoot certain API issues on your site.
* Tweak: Improved and hardened our code base to improve reliability.
* Fix: Updated settings info to display in the debug report.

= 6.2.8 =
* Fix: Menu items for our other plugins were not working when clicked.

= 6.2.7 =
* Tweak: Improved and hardened our code base to improve reliability.

= 6.2.6 =
* Fix: Resetting optimized images would also reset Instagram Feed settings.

= 6.2.5 =
* Fix: Fixed a problem with our code that would remove admin footer text from other sources.
* Fix: Fixed an issue causing the minified CSS file to not actually be minified.

= 6.2.4 =
* Fix: Disabled translation for a string that would cause an error in some languages.

= 6.2.3 =
* Fix: Fixed translation bugs that would cause PHP errors on certain settings pages.
* Fix: Fixed a PHP error that would occur when the database record for translations was corrupted.

= 6.2.2 =
* Translations: Fixed many translation issues and added translation files for German, French, Japanese, and many more.
* Fix: Fixed an issue that would cause extra slashes to be added to text settings that contained single and double quotes.
* Fix: Fixed an issue that would cause the customizer to not work the first time it was accessed.

= 6.2.1 =
* Fix: Fixed a deprecation warning that would occur in PHP 8.2+.
* Fix: Fixed a PHP error that would occur when using PHP 8.0+, legacy feeds, and a shortcode with no arguments.

= 6.2 =
* New: Added an onboarding wizard for new users to easily get started with Instagram Feeds.

= 6.1.6 =
* Fix: Changed code related to account connection for increased reliability.
* Fix: Only 20 feeds were available for export when using the tool on the settings page.
* Fix: Fixed an issue causing a PHP error "creation of a dynamic property" when using PHP 8.2.

= 6.1.5 =
* Fix: Fixed personal accounts unable to retrieve new tweets and showing an error with the code 100.
* Fix: Updated API calls for business accounts to work with upcoming changes from Instagram.

= 6.1.4 =
* Fix: Removed the option to add a feed to a widget if the current theme does not support widgets.
* New: Added a menu item to easily install our new [Reviews Feed](https://wordpress.org/plugins/reviews-feed/) plugin!

= 6.1.3 =
* Fix: Fixed a PHP Warning: strtotime(): Epoch doesn’t fit in a PHP integer.
* Fix: Header bio was not updating when the bio for the connected Instagram account was updated.
* Fix: Fixed a few text items not being translatable.

= 6.1.2 =
* Fix: When enabling Facebook oEmbeds from the oEmbed page, an Instagram icon would appear when the Custom Facebook Feed plugin was being activated.
* Fix: Fixed a CSS parsing error.

= 6.1.1 =
* Fix: When using the customizer to enable the setting for the header "show outside scrollable area" and adding a background color, the preview would not show the same result as the actual feed.
* Fix: Disabling the JavaScript image loading on the "Advanced" settings tab would cause the customizer preview to look distorted.
* Fix: When customizing a feed, the load more button would become active when switching the device preview.
* Fix: Fixed a PHP warning that would occur when bulk deleting feeds.

= 6.1 =
* New: Added the ability to filter "Reels" posts in feeds. When customizing a feed and using the moderation settings you can now choose to show or hide Instagram "Reels" posts.
* New: Add a header image and bio text for personal sources. Go to the settings page and click on the gear icon to add this to an existing source.
* New: Added support for Instagram "Reels" oEmbeds. Use WordPress' embed block to create rich oEmbed links in blog posts and pages.
* Tweak: Vue.js code is now loaded from a local file shipped with the plugin rather than an external CDN for use with the customizer in the admin area.

= 6.0.8 =
* Tweak: Added a workaround to retrieve missing images if none were returned by Instagram for a post.
* Fix: Custom colors assigned to the Follow button would not apply when using a custom color palette.
* Fix: Added additional plugin hardening.
* Fix: A fatal error would occur with older versions of PHP and WordPress in some circumstances.

= 6.0.7 =
* Fix: Removed legacy "disable mobile" setting support as it was causing confusion for users updating from 2.x where changes to feed columns would not have an effect.
* Fix: Removed the reference in the feed CSS file to an image file that didn't exist.in the feed CSS file.
* Fix: All sources would be removed when the grace period to address app permission issues ended. Now only the single source will be removed.
* Fix: The number of posts would be inaccurate in the feed preview when using the customizer for mobile devices.

= 6.0.6 =
* Tweak: Added a warning notice to allow a grace period before Instagram data is permanently deleted from your site after deauthorizing the Smash Balloon Instagram app. Due to Instagram requirements, any Instagram data on your site must be deleted within a reasonable time after the app has been deauthorized. The new warning notice provides a 7 day grace period to allow you time to reauthorize the app if you don't want the data to be deleted.
* Tweak: Reconnecting an account now results in deleting the original connection in the database and adding a new one. This will prevent issues with some caching systems like Redis.
* Fix: Only the first 20 sources were available when creating feeds and changing sources for a feed.
* Fix: The link in some error messages were incorrect resulting in "access denied" error messages when clicking on them.

= 6.0.5 =
* Tweak: If WordPress cron is broken or behind schedule and causing background caching to not work, the plugin will update the feed when the page loads.
* Fix: Jetpack's "Master Bar" feature was causing the sidebar in the customizer to be partially hidden.
* Fix: Added back support for the "class" shortcode setting for all feeds.
* Fix: Removed all Font Awesome icons and no longer include the CSS file from the Font Awesome CDN.

= 6.0.4 =
* Fix: Added back the ability to use up to 10 columns in feeds.
* Fix: The reconnect link that would display when an account had an error would not redirect to connect.smashballoon.com.

= 6.0.3 =
* Tweak: Updated our logo throughout the plugin to match our new [website](https://smashballoon.com/).
* Tweak: Changed how the hover color for follow and load more buttons is applied to prevent theme conflicts.
* Fix: Fixed JavaScript file not being added to the page when using the plugin GDPR Cookie Consent by WebToffee.
* Fix: Dismissing dashboard notifications would cause the "Add new feed" button to stop working until the page was refreshed.

= 6.0.2 =
* Fix: Fixed Instagram Feed JavaScript file missing from the page when using the "AJAX theme loading fix" setting causing blank images to display.
* Fix: Added the ability to create the custom database tables if there was an error when first trying to create them.
* Fix: Fixed the error message not displaying if there was an error when trying to connect a personal or basic account.

= 6.0.1 =
* Fix: Custom HTML templates were not applying to new feeds.
* Fix: Some custom tables were not being created for specific versions of MySQL.
* Fix: The shortcode setting "showfollow=false" was not working for legacy feeds.
* Fix: The shortcode settings "showheader" and "showbio" were applying for non-legacy feeds causing confusion when trying to change these settings in the customizer.
* Fix: The customizer would not resize images causing blank images to show when GDPR features were enabled.
* Fix: Fixed PHP warning "Undefined array key tagged".

= 6.0 =
* Important: Minimum supported WordPress version has been raised from 3.5 to 4.1.
* New: Our biggest update ever! We've completely redesigned the plugin settings from head to toe to make it easier to create, manage, and customize your Instagram feeds.
* New: All your feeds are now displayed in one place on the "All Feeds" page. This shows a list of any existing (legacy) feeds and any new ones that you create. Note: If you updated from a version prior to v2.8 then you may need to view your feeds on your webpage so that the plugin can locate them and list them here.
* New: Easily edit individual feed settings for new feeds instead of cumbersome shortcode options.
* New: It's now much easier to create feeds. Just click "Add New", select your feed type, connect your account, and you're done!
* New: Brand new feed customizer. We've completely redesigned feed customization from the ground up, reorganizing the settings to make them easier to find.
* New: Live Feed Preview. You can now see changes you make to your feeds in real time, right in the settings page. Easily preview them on desktop, tablet, and mobile sizes.
* New: Color Scheme option. It's now easier than ever to change colors across your feed without needing to adjust individual color settings. Just set a color scheme to effortlessly change colors across your entire feed.
* New: You can now change the number of columns in your feed across desktop, tablet, and mobile.
* New: Easily import and export feed settings to make it simple to move feeds across sites.

== Privacy Policy ==

=**Data Collection**=

Instagram Feed does not collect any personal data from your Meta accounts (Instagram, Facebook). All data retrieved from the Meta API is used solely for the purpose of displaying your feed on your WordPress site.

=**External Connections**=

This plugin connects to three external sites:

* **Smashballoon.com**: Used for connecting and authenticating your sources. Occasionally, it sends dynamic notices to users. These notices can include information about major API changes, critical updates, and marketing messages.

* **Instagram.com and Facebook.com**: Used to fetch posts so your visitors can view them directly on your site without needing to leave.

=**Data Usage**=

All data retrieved from the Meta API is used solely for displaying your Instagram feed on your WordPress site. No personal data is collected, stored, or shared beyond this purpose.