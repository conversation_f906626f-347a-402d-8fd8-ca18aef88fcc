var sbi_js_exists = void 0 !== sbi_js_exists; sbi_js_exists || (!function (e) { function i() { this.feeds = {}, this.options = sb_instagram_js_options } function t(e, i, t) { this.el = e, this.index = i, this.settings = t, this.minImageWidth = 0, this.imageResolution = 150, this.resizedImages = {}, this.needsResizing = [], this.outOfPages = !1, this.page = 1, this.isInitialized = !1 } function s(i, t) { e.ajax({ url: sbiajaxurl, type: "post", data: i, success: t }) } i.prototype = { createPage: function (i, t) { void 0 !== sb_instagram_js_options.ajax_url && void 0 === window.sbiajaxurl && (window.sbiajaxurl = sb_instagram_js_options.ajax_url), (void 0 === window.sbiajaxurl || -1 === window.sbiajaxurl.indexOf(window.location.hostname)) && (window.sbiajaxurl = location.protocol + "//" + window.location.hostname + "/wp-admin/admin-ajax.php"), e("#sbi-builder-app").length && void 0 === window.sbiresizedImages && e(".sbi_resized_image_data").length && void 0 !== e(".sbi_resized_image_data").attr("data-resized") && 0 === e(".sbi_resized_image_data").attr("data-resized").indexOf('{"') && (window.sbiresizedImages = JSON.parse(e(".sbi_resized_image_data").attr("data-resized")), e(".sbi_resized_image_data").remove()), e(".sbi_no_js_error_message").remove(), e(".sbi_no_js").removeClass("sbi_no_js"), i(t) }, createFeeds: function (i) { i.whenFeedsCreated(e(".sbi").each(function (i) { e(this).attr("data-sbi-index", i + 1); var a = e(this), n = void 0 !== a.attr("data-sbi-flags") ? a.attr("data-sbi-flags").split(",") : [], o = void 0 !== a.attr("data-options") ? JSON.parse(a.attr("data-options")) : {}; n.indexOf("testAjax") > -1 && (window.sbi.triggeredTest = !0, s({ action: "sbi_on_ajax_test_trigger" }, function (e) { console.log("did test") })); var d, r, l, c = { cols: a.attr("data-cols"), colsmobile: void 0 !== a.attr("data-colsmobile") && "same" !== a.attr("data-colsmobile") ? a.attr("data-colsmobile") : a.attr("data-cols"), colstablet: void 0 !== a.attr("data-colstablet") && "same" !== a.attr("data-colstablet") ? a.attr("data-colstablet") : a.attr("data-cols"), num: a.attr("data-num"), imgRes: a.attr("data-res"), feedID: a.attr("data-feedid"), postID: void 0 !== a.attr("data-postid") ? a.attr("data-postid") : "unknown", shortCodeAtts: a.attr("data-shortcode-atts"), resizingEnabled: -1 === n.indexOf("resizeDisable"), imageLoadEnabled: -1 === n.indexOf("imageLoadDisable"), debugEnabled: n.indexOf("debug") > -1, favorLocal: n.indexOf("favorLocal") > -1, ajaxPostLoad: n.indexOf("ajaxPostLoad") > -1, gdpr: n.indexOf("gdpr") > -1, overrideBlockCDN: n.indexOf("overrideBlockCDN") > -1, consentGiven: !1, locator: n.indexOf("locator") > -1, autoMinRes: 1, general: o }; window.sbi.feeds[i] = (d = this, r = i, l = c, new t(d, r, l)), window.sbi.feeds[i].setResizedImages(), window.sbi.feeds[i].init(); var g = jQuery.Event("sbiafterfeedcreate"); g.feed = window.sbi.feeds[i], jQuery(window).trigger(g) })) }, afterFeedsCreated: function () { e(".sb_instagram_header").each(function () { var i = e(this); i.find(".sbi_header_link").on("mouseenter mouseleave", function (e) { switch (e.type) { case "mouseenter": i.find(".sbi_header_img_hover").addClass("sbi_fade_in"); break; case "mouseleave": i.find(".sbi_header_img_hover").removeClass("sbi_fade_in") } }) }) }, encodeHTML: function (e) { if (void 0 === e) return ""; var i = e.replace(/(>)/g, "&gt;"), i = i.replace(/(<)/g, "&lt;"); return (i = i.replace(/(&lt;br\/&gt;)/g, "<br>")).replace(/(&lt;br&gt;)/g, "<br>") }, urlDetect: function (e) { return e.match(/https?:\/\/(www\.)?[-a-zA-Z0-9@:%._\+~#=]{2,256}\.[a-z]{2,6}\b([-a-zA-Z0-9@:%_\+.~#?&//=]*)/g) } }, t.prototype = { init: function () { var i, t = this; t.settings.consentGiven = t.checkConsent(), e(this.el).find(".sbi_photo").parent("p").length && e(this.el).addClass("sbi_no_autop"), e(this.el).find("#sbi_mod_error").length && e(this.el).prepend(e(this.el).find("#sbi_mod_error")), this.settings.ajaxPostLoad ? this.getNewPostSet() : this.afterInitialImagesLoaded(); var s = (i = 0, function (e, t) { clearTimeout(i), i = setTimeout(e, t) }); jQuery(window).on("resize", function () { s(function () { t.afterResize() }, 500) }), e(this.el).find(".sbi_item").each(function () { t.lazyLoadCheck(e(this)) }) }, initLayout: function () { }, afterInitialImagesLoaded: function () { this.initLayout(), this.loadMoreButtonInit(), this.hideExtraImagesForWidth(), this.beforeNewImagesRevealed(), this.revealNewImages(), this.afterNewImagesRevealed() }, afterResize: function () { this.setImageHeight(), this.setImageResolution(), this.maybeRaiseImageResolution(), this.setImageSizeClass() }, afterLoadMoreClicked: function (e) { e.find(".sbi_loader").removeClass("sbi_hidden"), e.find(".sbi_btn_text").addClass("sbi_hidden"), e.closest(".sbi").find(".sbi_num_diff_hide").addClass("sbi_transition").removeClass("sbi_num_diff_hide") }, afterNewImagesLoaded: function () { var i = e(this.el), t = this; this.beforeNewImagesRevealed(), this.revealNewImages(), this.afterNewImagesRevealed(), setTimeout(function () { i.find(".sbi_loader").addClass("sbi_hidden"), i.find(".sbi_btn_text").removeClass("sbi_hidden"), t.maybeRaiseImageResolution() }, 500) }, beforeNewImagesRevealed: function () { this.setImageHeight(), this.maybeRaiseImageResolution(!0), this.setImageSizeClass() }, revealNewImages: function () { var i = e(this.el); i.find(".sbi-screenreader").each(function () { e(this).find("img").remove() }), "function" == typeof sbi_custom_js && setTimeout(function () { sbi_custom_js() }, 100), this.applyImageLiquid(), i.find(".sbi_item").each(function (e) { jQuery(this).find(".sbi_photo").on("mouseenter mouseleave", function (e) { switch (e.type) { case "mouseenter": jQuery(this).fadeTo(200, .85); break; case "mouseleave": jQuery(this).stop().fadeTo(500, 1) } }) }), setTimeout(function () { jQuery("#sbi_images .sbi_item.sbi_new").removeClass("sbi_new"); var e = 10; i.find(".sbi_transition").each(function () { var i = jQuery(this); setTimeout(function () { i.removeClass("sbi_transition") }, e), e += 10 }) }, 500) }, lazyLoadCheck: function (i) { if (i.find(".sbi_photo").length && !i.closest(".sbi").hasClass("sbi-no-ll-check")) { var t = this.getImageUrls(i), s = void 0 !== t[640] ? t[640] : i.find(".sbi_photo").attr("data-full-res"); !(!this.settings.consentGiven && s.indexOf("scontent") > -1) && i.find(".sbi_photo img").each(function () { s && void 0 !== e(this).attr("data-src") && e(this).attr("data-src", s), s && void 0 !== e(this).attr("data-orig-src") && e(this).attr("data-orig-src", s), e(this).on("load", function () { !e(this).hasClass("sbi-replaced") && e(this).attr("src").indexOf("placeholder") > -1 && (e(this).addClass("sbi-replaced"), s && (e(this).attr("src", s), e(this).closest(".sbi_imgLiquid_bgSize").length && e(this).closest(".sbi_imgLiquid_bgSize").css("background-image", "url(" + s + ")"))) }) }) } }, afterNewImagesRevealed: function () { this.listenForVisibilityChange(), this.sendNeedsResizingToServer(), this.settings.imageLoadEnabled || e(".sbi_no_resraise").removeClass("sbi_no_resraise"); var i = e.Event("sbiafterimagesloaded"); i.el = e(this.el), e(window).trigger(i) }, setResizedImages: function () { e(this.el).find(".sbi_resized_image_data").length && void 0 !== e(this.el).find(".sbi_resized_image_data").attr("data-resized") && 0 === e(this.el).find(".sbi_resized_image_data").attr("data-resized").indexOf('{"') ? (this.resizedImages = JSON.parse(e(this.el).find(".sbi_resized_image_data").attr("data-resized")), e(this.el).find(".sbi_resized_image_data").remove()) : void 0 !== window.sbiresizedImages && (this.resizedImages = window.sbiresizedImages) }, sendNeedsResizingToServer: function () { var i = this, t = e(this.el); if (i.needsResizing.length > 0 && i.settings.resizingEnabled) { var a = e(this.el).find(".sbi_item").length, n = void 0 !== i.settings.general.cache_all && i.settings.general.cache_all, o = ""; if (void 0 !== t.attr("data-locatornonce") && (o = t.attr("data-locatornonce")), e("#sbi-builder-app").length) { if (void 0 !== window.sbiresizeTriggered && window.sbiresizeTriggered) return; window.sbiresizeTriggered = !0 } var d = { action: "sbi_resized_images_submit", needs_resizing: i.needsResizing, offset: a, feed_id: i.settings.feedID, atts: i.settings.shortCodeAtts, location: i.locationGuess(), post_id: i.settings.postID, cache_all: n, locator_nonce: o }, r = function (t) { var s = t; for (var a in "object" != typeof t && 0 === t.trim().indexOf("{") && (s = JSON.parse(t.trim())), i.settings.debugEnabled && console.log(s), s) s.hasOwnProperty(a) && (i.resizedImages[a] = s[a]); i.maybeRaiseImageResolution(), setTimeout(function () { i.afterResize() }, 500), e("#sbi-builder-app").length && (window.sbiresizeTriggered = !1) }; s(d, r) } else if (i.settings.locator) { var o = ""; void 0 !== t.attr("data-locatornonce") && (o = t.attr("data-locatornonce")); var d = { action: "sbi_do_locator", feed_id: i.settings.feedID, atts: i.settings.shortCodeAtts, location: i.locationGuess(), post_id: i.settings.postID, locator_nonce: o }, r = function (e) { }; s(d, r) } }, loadMoreButtonInit: function () { var i = e(this.el), t = this; i.find("#sbi_load .sbi_load_btn").off().on("click", function () { t.afterLoadMoreClicked(jQuery(this)), t.getNewPostSet() }) }, getNewPostSet: function () { var i = e(this.el), t = this; t.page++; var a, n = ""; void 0 !== i.attr("data-locatornonce") && (n = i.attr("data-locatornonce")), s({ action: "sbi_load_more_clicked", offset: i.find(".sbi_item").length, page: t.page, feed_id: t.settings.feedID, atts: t.settings.shortCodeAtts, location: t.locationGuess(), post_id: t.settings.postID, current_resolution: t.imageResolution, locator_nonce: n }, function (s) { var a = s; "object" != typeof s && 0 === s.trim().indexOf("{") && (a = JSON.parse(s.trim())), t.settings.debugEnabled && console.log(a), t.appendNewPosts(a.html), t.addResizedImages(a.resizedImages), t.settings.ajaxPostLoad ? (t.settings.ajaxPostLoad = !1, t.afterInitialImagesLoaded()) : t.afterNewImagesLoaded(), a.feedStatus.shouldPaginate ? t.outOfPages = !1 : (t.outOfPages = !0, i.find(".sbi_load_btn").hide()), e(".sbi_no_js").removeClass("sbi_no_js") }) }, appendNewPosts: function (i) { var t = e(this.el); t.find("#sbi_images .sbi_item").length ? t.find("#sbi_images .sbi_item").last().after(i) : t.find("#sbi_images").append(i) }, addResizedImages: function (e) { for (var i in e) this.resizedImages[i] = e[i] }, setImageHeight: function () { var i = e(this.el), t = i.find(".sbi_photo").eq(0).innerWidth(), s = this.getColumnCount(), a = i.find("#sbi_images").innerWidth() - i.find("#sbi_images").width(), n = a / 2; sbi_photo_width_manual = i.find("#sbi_images").width() / s - a, i.find(".sbi_photo").css("height", t), i.find(".sbi-owl-nav").length && setTimeout(function () { var e = 2; i.find(".sbi_owl2row-item").length && (e = 1); var t = i.find(".sbi_photo").eq(0).innerWidth() / e; t += parseInt(n) * (2 + (2 - e)), i.find(".sbi-owl-nav div").css("top", t) }, 100) }, maybeRaiseSingleImageResolution: function (i, t, s) { var a = this, n = a.getImageUrls(i), o = i.find(".sbi_photo img").attr("src"), d = 150, r = i.find("img").get(0), l = o === window.sbi.options.placeholder ? 1 : r.naturalWidth / r.naturalHeight, s = void 0 !== s && s; if (!(i.hasClass("sbi_no_resraise") || i.hasClass("sbi_had_error") || i.find(".sbi_link_area").length && i.find(".sbi_link_area").hasClass("sbi_had_error"))) { if (n.length < 1) { i.find(".sbi_link_area").length && i.find(".sbi_link_area").attr("href", window.sbi.options.placeholder.replace("placeholder.png", "thumb-placeholder.png")); return } (i.find(".sbi_link_area").length && i.find(".sbi_link_area").attr("href") === window.sbi.options.placeholder.replace("placeholder.png", "thumb-placeholder.png") || !a.settings.consentGiven) && i.find(".sbi_link_area").attr("href", n[n.length - 1]), void 0 !== n[640] && i.find(".sbi_photo").attr("data-full-res", n[640]), e.each(n, function (e, i) { i === o && (d = parseInt(e), s = !1) }); var c = 640; switch (a.settings.imgRes) { case "thumb": c = 150; break; case "medium": c = 320; break; case "full": c = 640; break; default: var g = Math.max(a.settings.autoMinRes, i.find(".sbi_photo").innerWidth()), h = a.getBestResolutionForAuto(g, l, i); switch (h) { case 320: c = 320; break; case 150: c = 150 } }if (c > d || o === window.sbi.options.placeholder || s) { if (a.settings.debugEnabled) { var f = o === window.sbi.options.placeholder ? "was placeholder" : "too small"; console.log("rais res for " + o, f) } var b = n[c].split("?ig_cache_key")[0]; if (o !== b && (i.find(".sbi_photo img").attr("src", b), i.find(".sbi_photo").css("background-image", 'url("' + b + '")')), d = c, "auto" === a.settings.imgRes) { var u = !1; i.find(".sbi_photo img").on("load", function () { var t = e(this), s = t.get(0).naturalWidth / t.get(0).naturalHeight; if (1e3 !== t.get(0).naturalWidth && s > l && !u) { switch (a.settings.debugEnabled && console.log("rais res again for aspect ratio change " + o), u = !0, g = i.find(".sbi_photo").innerWidth(), h = a.getBestResolutionForAuto(g, s, i), c = 640, h) { case 320: c = 320; break; case 150: c = 150 }c > d && (b = n[c].split("?ig_cache_key")[0], t.attr("src", b), t.closest(".sbi_photo").css("background-image", 'url("' + b + '")')), ("masonry" === a.layout || "highlight" === a.layout) && (e(a.el).find("#sbi_images").smashotope(a.isotopeArgs), setTimeout(function () { e(a.el).find("#sbi_images").smashotope(a.isotopeArgs) }, 500)) } else if (a.settings.debugEnabled) { var r = u ? "already checked" : "no aspect ratio change"; console.log("not raising res for replacement  " + o, r) } }) } } i.find("img").on("error", function () { if (e(this).hasClass("sbi_img_error")) console.log("unfixed error " + e(this).attr("src")); else { if (e(this).addClass("sbi_img_error"), !(e(this).attr("src").indexOf("media/?size=") > -1 || e(this).attr("src").indexOf("cdninstagram") > -1 || e(this).attr("src").indexOf("fbcdn") > -1) && a.settings.consentGiven) { if ("undefined" !== e(this).closest(".sbi_photo").attr("data-img-src-set")) { var i = JSON.parse(e(this).closest(".sbi_photo").attr("data-img-src-set").replace(/\\\//g, "/")); void 0 !== i.d && (e(this).attr("src", i.d), e(this).closest(".sbi_photo").css("background-image", "url(" + i.d + ")"), e(this).closest(".sbi_item").addClass("sbi_had_error").find(".sbi_link_area").attr("href", i[640]).addClass("sbi_had_error")) } } else { a.settings.favorLocal = !0; var i = a.getImageUrls(e(this).closest(".sbi_item")); void 0 !== i[640] && (e(this).attr("src", i[640]), e(this).closest(".sbi_photo").css("background-image", "url(" + i[640] + ")"), e(this).closest(".sbi_item").addClass("sbi_had_error").find(".sbi_link_area").attr("href", i[640]).addClass("sbi_had_error")) } setTimeout(function () { a.afterResize() }, 1500) } }) } }, maybeRaiseImageResolution: function (i) { var t = this, s = !t.isInitialized; e(t.el).find(void 0 !== i && !0 === i ? ".sbi_item.sbi_new" : ".sbi_item").each(function (i) { !e(this).hasClass("sbi_num_diff_hide") && e(this).find(".sbi_photo").length && void 0 !== e(this).find(".sbi_photo").attr("data-img-src-set") && t.maybeRaiseSingleImageResolution(e(this), i, s) }), t.isInitialized = !0 }, getBestResolutionForAuto: function (i, t, s) { (isNaN(t) || t < 1) && (t = 1); var a = 10 * Math.ceil(i * t / 10), n = [150, 320, 640]; if (s.hasClass("sbi_highlighted") && (a *= 2), -1 === n.indexOf(parseInt(a))) { var o = !1; e.each(n, function (e, i) { i > parseInt(a) && !o && (a = i, o = !0) }) } return a }, hideExtraImagesForWidth: function () { if ("carousel" !== this.layout) { var i = e(this.el), t = void 0 !== i.attr("data-num") && "" !== i.attr("data-num") ? parseInt(i.attr("data-num")) : 1, s = void 0 !== i.attr("data-nummobile") && "" !== i.attr("data-nummobile") ? parseInt(i.attr("data-nummobile")) : t; 480 > e(window).width() || "mobile" === window.sbi_preview_device ? s < i.find(".sbi_item").length && i.find(".sbi_item").slice(s - i.find(".sbi_item").length).addClass("sbi_num_diff_hide") : t < i.find(".sbi_item").length && i.find(".sbi_item").slice(t - i.find(".sbi_item").length).addClass("sbi_num_diff_hide") } }, setImageSizeClass: function () { var i = e(this.el); i.removeClass("sbi_small sbi_medium"); var t = i.innerWidth(), s = parseInt(i.find("#sbi_images").outerWidth() - i.find("#sbi_images").width()) / 2, a = this.getColumnCount(), n = (t - s * (a + 2)) / a; n > 120 && n < 240 ? i.addClass("sbi_medium") : n <= 120 && i.addClass("sbi_small") }, setMinImageWidth: function () { e(this.el).find(".sbi_item .sbi_photo").first().length ? this.minImageWidth = e(this.el).find(".sbi_item .sbi_photo").first().innerWidth() : this.minImageWidth = 150 }, setImageResolution: function () { if ("auto" === this.settings.imgRes) this.imageResolution = "auto"; else switch (this.settings.imgRes) { case "thumb": this.imageResolution = 150; break; case "medium": this.imageResolution = 320; break; default: this.imageResolution = 640 } }, getImageUrls: function (e) { var i = JSON.parse(e.find(".sbi_photo").attr("data-img-src-set").replace(/\\\//g, "/")), t = e.attr("id").replace("sbi_", ""); if (this.settings.consentGiven || this.settings.overrideBlockCDN || (i = []), void 0 !== this.resizedImages[t] && "video" !== this.resizedImages[t] && "pending" !== this.resizedImages[t] && "error" !== this.resizedImages[t].id && "video" !== this.resizedImages[t].id && "pending" !== this.resizedImages[t].id) { if (void 0 !== this.resizedImages[t].sizes) { var s = []; void 0 !== this.resizedImages[t].sizes.full && (i[640] = sb_instagram_js_options.resized_url + this.resizedImages[t].id + "full.jpg", s.push(640)), void 0 !== this.resizedImages[t].sizes.low && (i[320] = sb_instagram_js_options.resized_url + this.resizedImages[t].id + "low.jpg", s.push(320)), void 0 !== this.resizedImages[t].sizes.thumb && (s.push(150), i[150] = sb_instagram_js_options.resized_url + this.resizedImages[t].id + "thumb.jpg"), this.settings.favorLocal && (-1 === s.indexOf(640) && s.indexOf(320) > -1 && (i[640] = sb_instagram_js_options.resized_url + this.resizedImages[t].id + "low.jpg"), -1 === s.indexOf(320) && (s.indexOf(640) > -1 ? i[320] = sb_instagram_js_options.resized_url + this.resizedImages[t].id + "full.jpg" : s.indexOf(150) > -1 && (i[320] = sb_instagram_js_options.resized_url + this.resizedImages[t].id + "thumb.jpg")), -1 === s.indexOf(150) && (s.indexOf(320) > -1 ? i[150] = sb_instagram_js_options.resized_url + this.resizedImages[t].id + "low.jpg" : s.indexOf(640) > -1 && (i[150] = sb_instagram_js_options.resized_url + this.resizedImages[t].id + "full.jpg"))) } } else (void 0 === this.resizedImages[t] || void 0 !== this.resizedImages[t].id && "pending" !== this.resizedImages[t].id && "error" !== this.resizedImages[t].id) && this.addToNeedsResizing(t); return i }, getAvatarUrl: function (e, i) { if ("" === e) return ""; var t = this.settings.general.avatars, i = void 0 !== i ? i : "local"; return "local" === i ? void 0 !== t["LCL" + e] && 1 === parseInt(t["LCL" + e]) ? sb_instagram_js_options.resized_url + e + ".jpg" : void 0 !== t[e] ? t[e] : "" : void 0 !== t[e] ? t[e] : void 0 !== t["LCL" + e] && 1 === parseInt(t["LCL" + e]) ? sb_instagram_js_options.resized_url + e + ".jpg" : "" }, addToNeedsResizing: function (e) { -1 === this.needsResizing.indexOf(e) && this.needsResizing.push(e) }, applyImageLiquid: function () { var i, t, s, a, n = e(this.el); (i = i || { VER: "0.9.944" }).bgs_Available = !1, i.bgs_CheckRunned = !1, function (e) { e.fn.extend({ sbi_imgLiquid: function (t) { this.defaults = { fill: !0, verticalAlign: "center", horizontalAlign: "center", useBackgroundSize: !0, useDataHtmlAttr: !0, responsive: !0, delay: 0, fadeInTime: 0, removeBoxBackground: !0, hardPixels: !0, responsiveCheckTime: 500, timecheckvisibility: 500, onStart: null, onFinish: null, onItemStart: null, onItemFinish: null, onItemError: null }, function t() { if (!i.bgs_CheckRunned) { i.bgs_CheckRunned = !0; var s = e('<span style="background-size:cover" />'); e("body").append(s), function () { var e = s[0]; if (e && window.getComputedStyle) { var t = window.getComputedStyle(e, null); t && t.backgroundSize && (i.bgs_Available = "cover" === t.backgroundSize) } }(), s.remove() } }(); var s = this; return this.options = t, this.settings = e.extend({}, this.defaults, this.options), this.settings.onStart && this.settings.onStart(), this.each(function (t) { function a() { l.data("sbi_imgLiquid_error", !0), r.addClass("sbi_imgLiquid_error"), d.onItemError && d.onItemError(t, r, l), o() } function n() { var e, i, s, a, n, c, g, h, f = 0, b = 0, u = r.width(), m = r.height(); void 0 === l.data("owidth") && l.data("owidth", l[0].width), void 0 === l.data("oheight") && l.data("oheight", l[0].height), d.fill === u / m >= l.data("owidth") / l.data("oheight") ? (e = "100%", i = "auto", s = Math.floor(u), a = Math.floor(u * (l.data("oheight") / l.data("owidth")))) : (e = "auto", i = "100%", s = Math.floor(m * (l.data("owidth") / l.data("oheight"))), a = Math.floor(m)), n = d.horizontalAlign.toLowerCase(), g = u - s, "left" === n && (b = 0), "center" === n && (b = .5 * g), "right" === n && (b = g), -1 !== n.indexOf("%") && (n = parseInt(n.replace("%", ""), 10)) > 0 && (b = g * n * .01), c = d.verticalAlign.toLowerCase(), h = m - a, "left" === c && (f = 0), "center" === c && (f = .5 * h), "bottom" === c && (f = h), -1 !== c.indexOf("%") && (c = parseInt(c.replace("%", ""), 10)) > 0 && (f = h * c * .01), d.hardPixels && (e = s, i = a), l.css({ width: e, height: i, "margin-left": Math.floor(b), "margin-top": Math.floor(f) }), l.data("sbi_imgLiquid_oldProcessed") || (l.fadeTo(d.fadeInTime, 1), l.data("sbi_imgLiquid_oldProcessed", !0), d.removeBoxBackground && r.css("background-image", "none"), r.addClass("sbi_imgLiquid_nobgSize"), r.addClass("sbi_imgLiquid_ready")), d.onItemFinish && d.onItemFinish(t, r, l), o() } function o() { t === s.length - 1 && s.settings.onFinish && s.settings.onFinish() } var d = s.settings, r = e(this), l = e("img:first", r); return l.length ? (l.data("sbi_imgLiquid_settings") ? (r.removeClass("sbi_imgLiquid_error").removeClass("sbi_imgLiquid_ready"), d = e.extend({}, l.data("sbi_imgLiquid_settings"), s.options)) : d = e.extend({}, s.settings, function e() { var t = {}; if (s.settings.useDataHtmlAttr) { var a = r.attr("data-sbi_imgLiquid-fill"), n = r.attr("data-sbi_imgLiquid-horizontalAlign"), o = r.attr("data-sbi_imgLiquid-verticalAlign"); ("true" === a || "false" === a) && (t.fill = Boolean("true" === a)), void 0 === n || "left" !== n && "center" !== n && "right" !== n && -1 === n.indexOf("%") || (t.horizontalAlign = n), void 0 === o || "top" !== o && "bottom" !== o && "center" !== o && -1 === o.indexOf("%") || (t.verticalAlign = o) } return i.isIE && s.settings.ieFadeInDisabled && (t.fadeInTime = 0), t }()), l.data("sbi_imgLiquid_settings", d), d.onItemStart && d.onItemStart(t, r, l), void (i.bgs_Available && d.useBackgroundSize ? void (-1 === r.css("background-image").indexOf(encodeURI(l.attr("src"))) && r.css({ "background-image": 'url("' + encodeURI(l.attr("src")) + '")' }), r.css({ "background-size": d.fill ? "cover" : "contain", "background-position": (d.horizontalAlign + " " + d.verticalAlign).toLowerCase(), "background-repeat": "no-repeat" }), e("a:first", r).css({ display: "block", width: "100%", height: "100%" }), e("img", r).css({ display: "none" }), d.onItemFinish && d.onItemFinish(t, r, l), r.addClass("sbi_imgLiquid_bgSize"), r.addClass("sbi_imgLiquid_ready"), o()) : function i() { if (l.data("oldSrc") && l.data("oldSrc") !== l.attr("src")) { var s = l.clone().removeAttr("style"); return s.data("sbi_imgLiquid_settings", l.data("sbi_imgLiquid_settings")), l.parent().prepend(s), l.remove(), (l = s)[0].width = 0, void setTimeout(i, 10) } return l.data("sbi_imgLiquid_oldProcessed") ? void n() : (l.data("sbi_imgLiquid_oldProcessed", !1), l.data("oldSrc", l.attr("src")), e("img:not(:first)", r).css("display", "none"), r.css({ overflow: "hidden" }), l.fadeTo(0, 0).removeAttr("width").removeAttr("height").css({ visibility: "visible", "max-width": "none", "max-height": "none", width: "auto", height: "auto", display: "block" }), l.on("error", a), l[0].onerror = a, function e() { l.data("sbi_imgLiquid_error") || l.data("sbi_imgLiquid_loaded") || l.data("sbi_imgLiquid_oldProcessed") || (r.is(":visible") && l[0].complete && l[0].width > 0 && l[0].height > 0 ? (l.data("sbi_imgLiquid_loaded", !0), setTimeout(n, t * d.delay)) : setTimeout(e, d.timecheckvisibility)) }(), void function e() { (d.responsive || l.data("sbi_imgLiquid_oldProcessed")) && l.data("sbi_imgLiquid_settings") && (d = l.data("sbi_imgLiquid_settings"), r.actualSize = r.get(0).offsetWidth + r.get(0).offsetHeight / 1e4, r.sizeOld && r.actualSize !== r.sizeOld && n(), r.sizeOld = r.actualSize, setTimeout(e, d.responsiveCheckTime)) }()) }())) : void a() }) } }) }(jQuery), t = i.injectCss, s = document.getElementsByTagName("head")[0], (a = document.createElement("style")).type = "text/css", a.styleSheet ? a.styleSheet.cssText = t : a.appendChild(document.createTextNode(t)), s.appendChild(a), "function" == typeof n.find(".sbi_photo").sbi_imgLiquid && n.find(".sbi_photo").sbi_imgLiquid({ fill: !0 }) }, listenForVisibilityChange: function () { var i, t, s, a = this; i = jQuery, t = { callback: function () { }, runOnLoad: !0, frequency: 100, sbiPreviousVisibility: null }, (s = {}).sbiCheckVisibility = function (e, i) { if (jQuery.contains(document, e[0])) { var t = i.sbiPreviousVisibility, a = e.is(":visible"); i.sbiPreviousVisibility = a, null == t ? i.runOnLoad && i.callback(e, a) : t !== a && i.callback(e, a), setTimeout(function () { s.sbiCheckVisibility(e, i) }, i.frequency) } }, i.fn.sbiVisibilityChanged = function (e) { var a = i.extend({}, t, e); return this.each(function () { s.sbiCheckVisibility(i(this), a) }) }, "function" == typeof e(this.el).filter(":hidden").sbiVisibilityChanged && e(this.el).filter(":hidden").sbiVisibilityChanged({ callback: function (e, i) { a.afterResize() }, runOnLoad: !1 }) }, getColumnCount: function () { var i = e(this.el), t = this.settings.cols, s = this.settings.colsmobile, a = this.settings.colstablet, n = t; return sbiWindowWidth = window.innerWidth, i.hasClass("sbi_mob_col_auto") ? (sbiWindowWidth < 640 && parseInt(t) > 2 && 7 > parseInt(t) && (n = 2), sbiWindowWidth < 640 && parseInt(t) > 6 && 11 > parseInt(t) && (n = 4), sbiWindowWidth <= 480 && parseInt(t) > 2 && (n = 1)) : sbiWindowWidth > 480 && sbiWindowWidth <= 800 ? n = a : sbiWindowWidth <= 480 && (n = s), parseInt(n) }, checkConsent: function () { if (this.settings.consentGiven || !this.settings.gdpr) return !0; if (void 0 !== window.cookieyes) void 0 !== window.cookieyes._ckyConsentStore.get && (this.settings.consentGiven = "yes" === window.cookieyes._ckyConsentStore.get("functional")); else if ("undefined" != typeof CLI_Cookie) null !== CLI_Cookie.read(CLI_ACCEPT_COOKIE_NAME) && (null !== CLI_Cookie.read("cookielawinfo-checkbox-non-necessary") && (this.settings.consentGiven = "yes" === CLI_Cookie.read("cookielawinfo-checkbox-non-necessary")), null !== CLI_Cookie.read("cookielawinfo-checkbox-necessary") && (this.settings.consentGiven = "yes" === CLI_Cookie.read("cookielawinfo-checkbox-necessary"))); else if (void 0 !== window.cnArgs) { var e = ("; " + document.cookie).split("; cookie_notice_accepted="); if (2 === e.length) { var i = e.pop().split(";").shift(); this.settings.consentGiven = "true" === i } } else void 0 !== window.cookieconsent ? this.settings.consentGiven = "allow" === function e(i) { for (var t = i + "=", s = window.document.cookie.split(";"), a = 0; a < s.length; a++) { var n = s[a].trim(); if (0 == n.indexOf(t)) return n.substring(t.length, n.length) } return "" }("complianz_consent_status") : void 0 !== window.Cookiebot ? this.settings.consentGiven = Cookiebot.consented : void 0 !== window.BorlabsCookie && (this.settings.consentGiven = void 0 !== window.BorlabsCookie.Consents ? window.BorlabsCookie.Consents.hasConsent("instagram") : window.BorlabsCookie.checkCookieConsent("instagram")); var t = jQuery.Event("sbicheckconsent"); return t.feed = this, jQuery(window).trigger(t), this.settings.consentGiven }, afterConsentToggled: function () { if (this.checkConsent()) { var e = this; e.maybeRaiseImageResolution(), setTimeout(function () { e.afterResize() }, 500) } }, locationGuess: function () { var i = e(this.el), t = "content"; return i.closest("footer").length ? t = "footer" : i.closest(".header").length || i.closest("header").length ? t = "header" : (i.closest(".sidebar").length || i.closest("aside").length) && (t = "sidebar"), t } }, window.sbi_init = function () { window.sbi = new i, window.sbi.createPage(window.sbi.createFeeds, { whenFeedsCreated: window.sbi.afterFeedsCreated }) } }(jQuery), jQuery(document).ready(function (e) { void 0 === window.sb_instagram_js_options && (window.sb_instagram_js_options = { font_method: "svg", resized_url: location.protocol + "//" + window.location.hostname + "/wp-content/uploads/sb-instagram-feed-images/", placeholder: location.protocol + "//" + window.location.hostname + "/wp-content/plugins/instagram-feed/img/placeholder.png" }), void 0 !== window.sb_instagram_js_options.resized_url && -1 === window.sb_instagram_js_options.resized_url.indexOf(location.protocol) && ("http:" === location.protocol ? window.sb_instagram_js_options.resized_url = window.sb_instagram_js_options.resized_url.replace("https:", "http:") : window.sb_instagram_js_options.resized_url = window.sb_instagram_js_options.resized_url.replace("http:", "https:")), sbi_init(), e("#cookie-notice a").on("click", function () { setTimeout(function () { e.each(window.sbi.feeds, function (e) { window.sbi.feeds[e].afterConsentToggled() }) }, 1e3) }), e("#cookie-law-info-bar a").on("click", function () { setTimeout(function () { e.each(window.sbi.feeds, function (e) { window.sbi.feeds[e].afterConsentToggled() }) }, 1e3) }), e(".cli-user-preference-checkbox, .cky-notice button").on("click", function () { setTimeout(function () { e.each(window.sbi.feeds, function (e) { window.sbi.feeds[e].checkConsent(), window.sbi.feeds[e].afterConsentToggled() }) }, 1e3) }), e(window).on("CookiebotOnAccept", function (i) { e.each(window.sbi.feeds, function (e) { window.sbi.feeds[e].settings.consentGiven = !0, window.sbi.feeds[e].afterConsentToggled() }) }), e(document).on("cmplzAcceptAll", function (i) { e.each(window.sbi.feeds, function (e) { window.sbi.feeds[e].settings.consentGiven = !0, window.sbi.feeds[e].afterConsentToggled() }) }), e(document).on("cmplzRevoke", function (i) { e.each(window.sbi.feeds, function (e) { window.sbi.feeds[e].settings.consentGiven = !1, window.sbi.feeds[e].afterConsentToggled() }) }), e(document).on("borlabs-cookie-consent-saved", function (i) { e.each(window.sbi.feeds, function (e) { window.sbi.feeds[e].settings.consentGiven = !1, window.sbi.feeds[e].afterConsentToggled() }) }) }));