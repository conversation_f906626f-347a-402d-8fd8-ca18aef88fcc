"use strict";

(function () {
    var _wp = wp,
        _wp$serverSideRender = _wp.serverSideRender,
        createElement = wp.element.createElement,
        ServerSideRender = _wp$serverSideRender === void 0 ? wp.components.ServerSideRender : _wp$serverSideRender,
        _ref = wp.blockEditor || wp.editor,
        InspectorControls = _ref.InspectorControls,
        _wp$components = wp.components,
        TextareaControl = _wp$components.TextareaControl,
        Button = _wp$components.Button,
        PanelBody = _wp$components.PanelBody,
        Placeholder = _wp$components.Placeholder,
        registerBlockType = wp.blocks.registerBlockType;

    var sbiIcon = createElement('svg', {
        width: 20,
        height: 20,
        viewBox: '0 0 448 512',
        className: 'dashicon'
    }, createElement('path', {
        fill: 'currentColor',
        d: 'M224.1 141c-63.6 0-114.9 51.3-114.9 114.9s51.3 114.9 114.9 114.9S339 319.5 339 255.9 287.7 141 224.1 141zm0 189.6c-41.1 0-74.7-33.5-74.7-74.7s33.5-74.7 74.7-74.7 74.7 33.5 74.7 74.7-33.6 74.7-74.7 74.7zm146.4-194.3c0 14.9-12 26.8-26.8 26.8-14.9 0-26.8-12-26.8-26.8s12-26.8 26.8-26.8 26.8 12 26.8 26.8zm76.1 27.2c-1.7-35.9-9.9-67.7-36.2-93.9-26.2-26.2-58-34.4-93.9-36.2-37-2.1-147.9-2.1-184.9 0-35.8 1.7-67.6 9.9-93.9 36.1s-34.4 58-36.2 93.9c-2.1 37-2.1 147.9 0 184.9 1.7 35.9 9.9 67.7 36.2 93.9s58 34.4 93.9 36.2c37 2.1 147.9 2.1 184.9 0 35.9-1.7 67.7-9.9 93.9-36.2 26.2-26.2 34.4-58 36.2-93.9 2.1-37 2.1-147.8 0-184.8zM398.8 388c-7.8 19.6-22.9 34.7-42.6 42.6-29.5 11.7-99.5 9-132.1 9s-102.7 2.6-132.1-9c-19.6-7.8-34.7-22.9-42.6-42.6-11.7-29.5-9-99.5-9-132.1s-2.6-102.7 9-132.1c7.8-19.6 22.9-34.7 42.6-42.6 29.5-11.7 99.5-9 132.1-9s102.7-2.6 132.1 9c19.6 7.8 34.7 22.9 42.6 42.6 11.7 29.5 9 99.5 9 132.1s2.7 102.7-9 132.1z'
    }));

    registerBlockType('sbi/sbi-feed-block', {
        title: 'Instagram Feed',
        icon: sbiIcon,
        category: 'widgets',
        attributes: {
            noNewChanges: {
                type: 'boolean',
            },
            shortcodeSettings: {
                type: 'string',
            }
        },
        edit: function edit(props) {
            var _props = props,
                setAttributes = _props.setAttributes,
                _props$attributes = _props.attributes,
                _props$attributes$sho = _props$attributes.shortcodeSettings,
                shortcodeSettings = _props$attributes$sho === void 0 ? sbi_block_editor.shortcodeSettings : _props$attributes$sho,
                _props$attributes$cli = _props$attributes.noNewChanges,
                noNewChanges = _props$attributes$cli === void 0 ? true : _props$attributes$cli;

            function setState(shortcodeSettingsContent) {
                setAttributes({
                    noNewChanges: false,
                    shortcodeSettings: shortcodeSettingsContent
                })
            }

            function previewClick(content) {
                setAttributes({
                    noNewChanges: true,
                })
            }
            function afterRender() {
                var executed = false;
                // no way to run a script after AJAX call to get feed so we just try to execute it on a few intervals
                setTimeout(function() { if (typeof sbi_init !== 'undefined') {sbi_init();}},1000);
                setTimeout(function() { if (typeof sbi_init !== 'undefined') {sbi_init();}},2000);
                setTimeout(function() { if (typeof sbi_init !== 'undefined') {sbi_init();}},3000);
                setTimeout(function() { if (typeof sbi_init !== 'undefined') {sbi_init();}},5000);
                setTimeout(function() { if (typeof sbi_init !== 'undefined') {sbi_init();}},10000);
            }

            var jsx = [React.createElement(InspectorControls, {
                key: "sbi-gutenberg-setting-selector-inspector-controls"
            }, React.createElement(PanelBody, {
                title: sbi_block_editor.i18n.addSettings
            }, React.createElement(TextareaControl, {
                key: "sbi-gutenberg-settings",
                className: "sbi-gutenberg-settings",
                label: sbi_block_editor.i18n.shortcodeSettings,
                help: sbi_block_editor.i18n.example + ": 'user=\"smashballoon\" showbutton=\"true\"'",
                value: shortcodeSettings,
                onChange: setState
            }), React.createElement(Button, {
                key: "sbi-gutenberg-preview",
                className: "sbi-gutenberg-preview",
                onClick: previewClick,
                isDefault: true
            }, sbi_block_editor.i18n.preview)))];

            if (noNewChanges) {
                afterRender();
                jsx.push(React.createElement(ServerSideRender, {
                    key: "instagram-feeds/instagram-feeds",
                    block: "sbi/sbi-feed-block",
                    attributes: props.attributes,
                }));
            } else {
                props.attributes.noNewChanges = false;
                jsx.push(React.createElement(Placeholder, {
                    key: "sbi-gutenberg-setting-selector-select-wrap",
                    className: "sbi-gutenberg-setting-selector-select-wrap"
                }, React.createElement(Button, {
                    key: "sbi-gutenberg-preview",
                    className: "sbi-gutenberg-preview",
                    onClick: previewClick,
                    isDefault: true
                }, sbi_block_editor.i18n.preview)));
            }

            return jsx;
        },
        save: function save() {
            return null;
        }
    });
})();