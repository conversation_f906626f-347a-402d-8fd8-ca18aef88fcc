{"name": "smashballoon/framework", "description": "WordPress plugin framework. Developed by SmashBalloon Team.", "type": "library", "authors": [{"name": "Smashballoon", "email": "<EMAIL>"}], "scripts": {"post-package-install": "phpcs --config-set installed_paths vendor/phpcompatibility/php-compatibility"}, "autoload": {"psr-4": {"InstagramFeed\\Vendor\\Smashballoon\\Framework\\": ""}, "files": ["Utilities/functions.php"], "exclude-from-classmap": ["Packages/Blocks/blocks/*"]}, "require": {"php": ">=5.6"}, "require-dev": {"squizlabs/php_codesniffer": "^3.7", "phpcompatibility/php-compatibility": "^9.3"}}