# WordPress Plugin Framework

<p>
Supports <code>PHP >= 5.6</code>.
<br>For contribution environment setup, it uses <code>Composer 2.X</code>
</p>

## Installation

Add below code snippet in `composer.json`, then run `composer install`.

```json
{
  "repositories": [
    {
      "type": "vcs",
      "url": "**************:awesomemotive/sb-common.git"
    }
  ],
  "require": {
    "smashballoon/framework": "dev-master"
  }
}
```
