<?php

namespace InstagramFeed\Vendor;

return array('root' => array('name' => 'smashballoon/instagram-feed', 'pretty_version' => 'dev-master', 'version' => 'dev-master', 'reference' => '60da339c25b6961e9f633a7494cbe2653f4f4222', 'type' => 'library', 'install_path' => __DIR__ . '/../../', 'aliases' => array(), 'dev' => \false), 'versions' => array('smashballoon/framework' => array('pretty_version' => 'dev-master', 'version' => 'dev-master', 'reference' => 'fedbc18a520db84eb074d01500b94d5bf076dd65', 'type' => 'library', 'install_path' => __DIR__ . '/../smashballoon/framework', 'aliases' => array(0 => '9999999-dev'), 'dev_requirement' => \false), 'smashballoon/instagram-feed' => array('pretty_version' => 'dev-master', 'version' => 'dev-master', 'reference' => '60da339c25b6961e9f633a7494cbe2653f4f4222', 'type' => 'library', 'install_path' => __DIR__ . '/../../', 'aliases' => array(), 'dev_requirement' => \false), 'smashballoon/stubs' => array('pretty_version' => 'dev-master', 'version' => 'dev-master', 'reference' => '063c3e588e897985d83cb65958c45e12281263ba', 'type' => 'library', 'install_path' => __DIR__ . '/../smashballoon/stubs', 'aliases' => array(0 => '9999999-dev'), 'dev_requirement' => \false)));
