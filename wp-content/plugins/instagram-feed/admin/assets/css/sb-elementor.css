.sb-elem-icon{
	content: '#';
}
.sb-elem-icon.sb-elem-facebook{
	content: url("data:image/svg+xml,%3Csvg width='29' height='29' viewBox='0 0 29 29' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M12.9056 18.109H13.4056V17.609H12.9056V18.109ZM9.79722 18.109H9.29722V18.609H9.79722V18.109ZM9.79722 14.5601V14.0601H9.29722V14.5601H9.79722ZM12.9056 14.5601V15.0601H13.4056V14.5601H12.9056ZM20.2604 7.32764H20.7604V6.90407L20.3426 6.83444L20.2604 7.32764ZM20.2604 10.3503V10.8503H20.7604V10.3503H20.2604ZM16.7237 14.5601H16.2237V15.0601H16.7237V14.5601ZM20.1258 14.5601L20.6199 14.6367L20.7094 14.0601H20.1258V14.5601ZM19.5751 18.109V18.609H20.0035L20.0692 18.1857L19.5751 18.109ZM16.7237 18.109V17.609H16.2237V18.109H16.7237ZM27.0523 14.5601H26.5523L26.5523 14.5612L27.0523 14.5601ZM17.0803 26.6136L16.9877 26.1222L17.0803 26.6136ZM12.5489 26.6139L12.6411 26.1225L12.5489 26.6139ZM14.8147 1.79797C7.80749 1.79797 2.07703 7.51685 2.07703 14.5601H3.07703C3.07703 8.06849 8.36042 2.79797 14.8147 2.79797V1.79797ZM2.07703 14.5601C2.07703 20.7981 6.54984 25.9974 12.4567 27.1053L12.6411 26.1225C7.19999 25.1019 3.07703 20.3097 3.07703 14.5601H2.07703ZM13.4056 26.3139V18.109H12.4056V26.3139H13.4056ZM12.9056 17.609H9.79722V18.609H12.9056V17.609ZM10.2972 18.109V14.5601H9.29722V18.109H10.2972ZM9.79722 15.0601H12.9056V14.0601H9.79722V15.0601ZM13.4056 14.5601V11.8556H12.4056V14.5601H13.4056ZM13.4056 11.8556C13.4056 10.4191 13.8299 9.36825 14.5211 8.67703C15.2129 7.98525 16.2305 7.59512 17.5314 7.59512V6.59512C16.0299 6.59512 14.7346 7.04939 13.814 7.96992C12.893 8.891 12.4056 10.2204 12.4056 11.8556H13.4056ZM17.5314 7.59512C18.1724 7.59512 18.8342 7.65119 19.3401 7.70823C19.592 7.73664 19.8029 7.76502 19.9504 7.78622C20.0241 7.79681 20.0818 7.8056 20.1208 7.81167C20.1402 7.81471 20.155 7.81706 20.1647 7.81863C20.1695 7.81941 20.1731 7.82 20.1754 7.82037C20.1765 7.82056 20.1773 7.82069 20.1778 7.82077C20.178 7.82081 20.1782 7.82083 20.1782 7.82084C20.1783 7.82085 20.1783 7.82085 20.1783 7.82085C20.1783 7.82085 20.1783 7.82085 20.1783 7.82085C20.1782 7.82084 20.1782 7.82084 20.2604 7.32764C20.3426 6.83444 20.3426 6.83443 20.3425 6.83443C20.3425 6.83442 20.3424 6.83441 20.3424 6.83441C20.3423 6.83439 20.3422 6.83437 20.342 6.83435C20.3418 6.8343 20.3414 6.83425 20.341 6.83417C20.3401 6.83403 20.3389 6.83383 20.3373 6.83357C20.3342 6.83307 20.3298 6.83234 20.3241 6.83142C20.3127 6.82958 20.2962 6.82695 20.2749 6.82362C20.2323 6.81698 20.1707 6.8076 20.0927 6.7964C19.9369 6.774 19.7157 6.74425 19.4521 6.71453C18.927 6.65532 18.2243 6.59512 17.5314 6.59512V7.59512ZM19.7604 7.32764V10.3503H20.7604V7.32764H19.7604ZM20.2604 9.85033H18.7185V10.8503H20.2604V9.85033ZM18.7185 9.85033C17.851 9.85033 17.1997 10.1241 16.7742 10.6148C16.3604 11.0922 16.2237 11.7025 16.2237 12.2594H17.2237C17.2237 11.8495 17.3257 11.5053 17.5298 11.2699C17.7224 11.0477 18.0684 10.8503 18.7185 10.8503V9.85033ZM16.2237 12.2594V14.5601H17.2237V12.2594H16.2237ZM16.7237 15.0601H20.1258V14.0601H16.7237V15.0601ZM19.6317 14.4834L19.081 18.0323L20.0692 18.1857L20.6199 14.6367L19.6317 14.4834ZM19.5751 17.609H16.7237V18.609H19.5751V17.609ZM16.2237 18.109V26.3135H17.2237V18.109H16.2237ZM17.1728 27.105C20.0281 26.5671 22.618 25.068 24.5079 22.8512L23.7469 22.2025C22.0054 24.2452 19.6188 25.6266 16.9877 26.1222L17.1728 27.105ZM24.5079 22.8512C26.4793 20.5388 27.5591 17.5977 27.5523 14.559L26.5523 14.5612C26.5586 17.3614 25.5636 20.0715 23.7469 22.2025L24.5079 22.8512ZM27.5523 14.5601C27.5523 7.51685 21.8218 1.79797 14.8147 1.79797V2.79797C21.2689 2.79797 26.5523 8.06849 26.5523 14.5601H27.5523ZM16.2237 26.3135C16.2237 26.8064 16.6682 27.2 17.1728 27.105L16.9877 26.1222C17.119 26.0975 17.2237 26.2006 17.2237 26.3135H16.2237ZM12.4567 27.1053C12.9603 27.1998 13.4056 26.8073 13.4056 26.3139H12.4056C12.4056 26.2001 12.5107 26.098 12.6411 26.1225L12.4567 27.1053Z' fill='%23576067'/%3E%3Cpath d='M12.9056 18.1088H13.4056V17.6088H12.9056V18.1088ZM9.79724 18.1088H9.29724V18.6088H9.79724V18.1088ZM9.79724 14.5599V14.0599H9.29724V14.5599H9.79724ZM12.9056 14.5599V15.0599H13.4056V14.5599H12.9056ZM20.2604 7.32749H20.7604V6.90392L20.3426 6.83429L20.2604 7.32749ZM20.2604 10.3502V10.8502H20.7604V10.3502H20.2604ZM16.7237 14.5599H16.2237V15.0599H16.7237V14.5599ZM20.1258 14.5599L20.6199 14.6366L20.7094 14.0599H20.1258V14.5599ZM19.5751 18.1088V18.6088H20.0035L20.0692 18.1855L19.5751 18.1088ZM16.7237 18.1088V17.6088H16.2237V18.1088H16.7237ZM13.4056 26.366V18.1088H12.4056V26.366H13.4056ZM12.9056 17.6088H9.79724V18.6088H12.9056V17.6088ZM10.2972 18.1088V14.5599H9.29724V18.1088H10.2972ZM9.79724 15.0599H12.9056V14.0599H9.79724V15.0599ZM13.4056 14.5599V11.8554H12.4056V14.5599H13.4056ZM13.4056 11.8554C13.4056 10.4189 13.8299 9.3681 14.5212 8.67688C15.2129 7.9851 16.2305 7.59497 17.5314 7.59497V6.59497C16.0299 6.59497 14.7346 7.04924 13.814 7.96977C12.893 8.89085 12.4056 10.2202 12.4056 11.8554H13.4056ZM17.5314 7.59497C18.1724 7.59497 18.8342 7.65103 19.3401 7.70808C19.592 7.73648 19.803 7.76487 19.9504 7.78607C20.0241 7.79666 20.0819 7.80545 20.1208 7.81152C20.1402 7.81455 20.155 7.81691 20.1647 7.81848C20.1695 7.81926 20.1731 7.81984 20.1754 7.82022C20.1765 7.8204 20.1773 7.82054 20.1778 7.82061C20.178 7.82065 20.1782 7.82068 20.1783 7.82069C20.1783 7.8207 20.1783 7.8207 20.1783 7.8207C20.1783 7.8207 20.1783 7.82069 20.1783 7.82069C20.1782 7.82069 20.1782 7.82068 20.2604 7.32749C20.3426 6.83429 20.3426 6.83428 20.3425 6.83427C20.3425 6.83427 20.3424 6.83426 20.3424 6.83425C20.3423 6.83424 20.3422 6.83422 20.3421 6.8342C20.3418 6.83415 20.3414 6.83409 20.341 6.83402C20.3401 6.83387 20.3389 6.83367 20.3374 6.83342C20.3343 6.83291 20.3298 6.83219 20.3241 6.83127C20.3127 6.82943 20.2962 6.82679 20.2749 6.82347C20.2324 6.81683 20.1707 6.80745 20.0927 6.79624C19.9369 6.77385 19.7158 6.7441 19.4522 6.71438C18.927 6.65516 18.2244 6.59497 17.5314 6.59497V7.59497ZM19.7604 7.32749V10.3502H20.7604V7.32749H19.7604ZM20.2604 9.85018H18.7185V10.8502H20.2604V9.85018ZM18.7185 9.85018C17.8511 9.85018 17.1997 10.1239 16.7743 10.6146C16.3604 11.092 16.2237 11.7024 16.2237 12.2592H17.2237C17.2237 11.8493 17.3257 11.5052 17.5298 11.2697C17.7224 11.0476 18.0684 10.8502 18.7185 10.8502V9.85018ZM16.2237 12.2592V14.5599H17.2237V12.2592H16.2237ZM16.7237 15.0599H20.1258V14.0599H16.7237V15.0599ZM19.6317 14.4833L19.081 18.0322L20.0692 18.1855L20.6199 14.6366L19.6317 14.4833ZM19.5751 17.6088H16.7237V18.6088H19.5751V17.6088ZM16.2237 18.1088V26.366H17.2237V18.1088H16.2237ZM16.4146 26.1752H13.2148V27.1752H16.4146V26.1752ZM16.2237 26.366C16.2237 26.2606 16.3092 26.1752 16.4146 26.1752V27.1752C16.8615 27.1752 17.2237 26.8129 17.2237 26.366H16.2237ZM12.4056 26.366C12.4056 26.8129 12.7679 27.1752 13.2148 27.1752V26.1752C13.3202 26.1752 13.4056 26.2606 13.4056 26.366H12.4056Z' fill='%23576067'/%3E%3C/svg%3E%0A");
}
.sb-elem-icon.sb-elem-instagram{
	content: url("data:image/svg+xml,%3Csvg width='24' height='24' viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Crect x='1' y='1' width='22' height='22' rx='6' stroke='%23576067' stroke-width='1.5'/%3E%3Ccircle cx='12' cy='12' r='5' stroke='%23576067' stroke-width='1.5'/%3E%3Ccircle cx='18.5' cy='5.75' r='1.25' fill='%23576067'/%3E%3C/svg%3E%0A");
}
.sb-elem-icon.sb-elem-twitter{
	content: url("data:image/svg+xml,%3Csvg width='29' height='29' viewBox='0 0 29 29' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M27.8762 6.78846C27.9345 6.7065 27.8465 6.60094 27.7536 6.63936C27.0263 6.93989 26.26 7.15757 25.4698 7.29241C25.3632 7.31061 25.3082 7.16347 25.3956 7.09969C26.2337 6.48801 26.8918 5.63649 27.2636 4.63802C27.2958 4.55162 27.2014 4.47574 27.1212 4.52123C26.1605 5.06618 25.1092 5.45524 24.0054 5.68842C23.971 5.69568 23.9354 5.68407 23.9113 5.65844C22.9264 4.60891 21.5545 4 19.9999 4C17.0624 4 14.6624 6.4 14.6624 9.3625C14.6624 9.74071 14.702 10.109 14.7724 10.4586C14.7855 10.5235 14.7342 10.5842 14.6681 10.5803C10.3227 10.3231 6.45216 8.2421 3.84135 5.09864C3.79681 5.04501 3.71241 5.0515 3.67879 5.11257C3.26061 5.8722 3.02493 6.75115 3.02493 7.675C3.02493 9.41548 3.84362 10.9704 5.13417 11.9317C5.2141 11.9913 5.17327 12.12 5.07385 12.1129C4.32811 12.0597 3.63173 11.835 3.00827 11.5171C2.99303 11.5094 2.97493 11.5204 2.97493 11.5375V11.5375C2.97493 13.9896 4.6205 16.0638 6.86301 16.7007C6.96452 16.7295 6.96588 16.8757 6.86218 16.8953C6.25772 17.0096 5.63724 17.0173 5.0289 16.9176C4.95384 16.9052 4.89095 16.9762 4.91633 17.0479C5.26967 18.0466 5.91213 18.9192 6.7637 19.5537C7.58576 20.1661 8.56481 20.5283 9.58351 20.6001C9.67715 20.6067 9.71634 20.7266 9.64124 20.7829C7.78574 22.1744 5.52424 22.9237 3.19993 22.9125C2.91864 22.9125 2.63736 22.9015 2.35608 22.8796C2.25034 22.8714 2.20189 23.0116 2.29272 23.0664C4.58933 24.4509 7.27959 25.25 10.1499 25.25C19.9999 25.25 25.4124 17.075 25.4124 9.9875C25.4124 9.76833 25.4124 9.5598 25.4026 9.34228C25.4011 9.30815 25.4168 9.27551 25.4445 9.2555C26.3819 8.57814 27.1984 7.74079 27.8762 6.78846Z' stroke='%23576067' stroke-width='1.25' stroke-linejoin='round'/%3E%3C/svg%3E%0A");
}
.sb-elem-icon.sb-elem-youtube{
	content: url("data:image/svg+xml,%3Csvg width='27' height='20' viewBox='0 0 27 20' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M11 13.3267C11 13.4037 11.0834 13.4518 11.15 13.4133L17.3377 9.83658C17.4043 9.79808 17.4043 9.70192 17.3377 9.66342L11.15 6.08673C11.0834 6.0482 11 6.0963 11 6.17331V13.3267ZM25.45 3.7125C25.6125 4.3 25.725 5.0875 25.8 6.0875C25.8875 7.0875 25.925 7.95 25.925 8.7L26 9.75C26 12.4875 25.8 14.5 25.45 15.7875C25.1375 16.9125 24.4125 17.6375 23.2875 17.95C22.7 18.1125 21.625 18.225 19.975 18.3C18.35 18.3875 16.8625 18.425 15.4875 18.425L13.5 18.5C8.2625 18.5 5 18.3 3.7125 17.95C2.5875 17.6375 1.8625 16.9125 1.55 15.7875C1.3875 15.2 1.275 14.4125 1.2 13.4125C1.1125 12.4125 1.075 11.55 1.075 10.8L1 9.75C1 7.0125 1.2 5 1.55 3.7125C1.8625 2.5875 2.5875 1.8625 3.7125 1.55C4.3 1.3875 5.375 1.275 7.025 1.2C8.65 1.1125 10.1375 1.075 11.5125 1.075L13.5 1C18.7375 1 22 1.2 23.2875 1.55C24.4125 1.8625 25.1375 2.5875 25.45 3.7125Z' stroke='%23576067' stroke-width='1.25' stroke-linejoin='round'/%3E%3C/svg%3E%0A");
}
#elementor-panel-category-smash-balloon .icon{
	height: 44px;
}

#elementor-panel-category-smash-balloon .elementor-element:before{
	content:  url("data:image/svg+xml,%3Csvg width='13' height='17' viewBox='0 0 13 17' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath fill-rule='evenodd' clip-rule='evenodd' d='M12.1181 7.76035C12.1181 3.75058 9.48981 0.5 6.24644 0.5C3.00307 0.5 0.373535 3.75058 0.373535 7.76035C0.373535 11.6043 2.78204 14.7408 5.83201 15.0039L5.50767 16.0309L7.54138 15.858L6.82784 14.9859C9.79733 14.6255 12.1181 11.5287 12.1181 7.76035Z' fill='%23636D75'/%3E%3Cpath fill-rule='evenodd' clip-rule='evenodd' d='M7.64698 3.05249L7.92956 5.96825L10.8576 6.05236L8.7395 8.02172L10.4124 10.4413L7.59414 9.91157L6.73975 12.7299L5.44249 10.2061L2.82313 11.4063L3.83076 8.70076L1.27612 7.41224L4.01079 6.53275L3.25618 3.83878L5.84337 5.31376L7.64698 3.05249Z' fill='white'/%3E%3C/svg%3E%0A");
	position: absolute;
	right: 7px;
	top: 7px;
}
#elementor-panel-category-smash-balloon .elementor-element:hover:before{
	content:  url("data:image/svg+xml,%3Csvg width='13' height='17' viewBox='0 0 13 17' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath fill-rule='evenodd' clip-rule='evenodd' d='M12.1181 7.76035C12.1181 3.75058 9.48981 0.5 6.24644 0.5C3.00307 0.5 0.373535 3.75058 0.373535 7.76035C0.373535 11.6043 2.78204 14.7408 5.83201 15.0039L5.50767 16.0309L7.54138 15.858L6.82784 14.9859C9.79733 14.6255 12.1181 11.5287 12.1181 7.76035Z' fill='%23FE544F'/%3E%3Cpath fill-rule='evenodd' clip-rule='evenodd' d='M7.64698 3.05249L7.92956 5.96825L10.8576 6.05236L8.7395 8.02172L10.4124 10.4413L7.59414 9.91157L6.73975 12.7299L5.44249 10.2061L2.82313 11.4063L3.83076 8.70076L1.27612 7.41224L4.01079 6.53275L3.25618 3.83878L5.84337 5.31376L7.64698 3.05249Z' fill='white'/%3E%3C/svg%3E%0A");
}


.sb-popup-cls{
	height: 14px;
	width: 14px;
	position: absolute;
	cursor: pointer;
	right: 17px;
	top: 17px;
	z-index: 3;
}
.sb-center-boss{
	display: flex;
	justify-content: center;
	align-items: center;
}
.sb-popup-inside{
	left: 80px;
	width: 880px;
	max-width: calc(100% - 200px);
	background: #fff;
	color: #141B38;
	position: relative;
	box-shadow: 0px 26.7377px 77.2886px rgba(0, 0, 0, 0.107828), 0px 14.2952px 41.3222px rgba(0, 0, 0, 0.0894161), 0px 8.01379px 23.1649px rgba(0, 0, 0, 0.075), 0px 4.25607px 12.3027px rgba(0, 0, 0, 0.0605839), 0px 1.77104px 5.11942px rgba(0, 0, 0, 0.0421718);
	border-radius: 2px;
	overflow-y: auto;
	max-height: 80vh;
}
.sb-popup-inside.sb-narrower-modal {
	max-width: 576px;
}
.sb-popup-inside.sb-onboarding-tooltip {
	overflow-y: visible !important;
}
@media all and (max-width: 960px) {
	.sb-popup-inside {
		left: 20px;
		width: 100%;
		max-width: calc(100% - 100px);
	}
}

/* Install Plugin Modal on Select Source Flow */
.sb-btn-orange{
	background: #FE544F!important;
	color: #fff!important;
}
.sb-btn-orange:hover{
	background: #EC352F!important;
	border-color: #EC352F!important;
	color: #fff!important;
}
.sb-btn-orange:focus,
.sb-btn-orange:active{
	background: #BC120E!important;
	border-color: #BC120E!important;
	color: #fff!important;
}

/*red*/
.sb-btn-blue{
	background: #0068A0!important;
	color: #fff!important;
}
.sb-btn-blue:hover{
	background: #0096CC!important;
	border-color: #0096CC!important;
	color: #fff!important;
}
.sb-btn-blue:focus,
.sb-btn-blue:active{
	background: #004D77!important;
	border-color: #004D77!important;
	color: #fff!important;
}
.sb-fs-boss{
	position: fixed;
	height: 100vh;
	width: 100%;
	left: 0;
	top: 0;
	bottom: 0;
	right: 0;
	background: rgba(0,0,0,.4);
	z-index: 9989;
}
.sb-install-plugin-modal {
	max-width: 580px;
}
.sb-install-plugin-body .sb-install-plugin-header {
	height: 106px;
	background: #F3F4F5;
	padding: 20px;
	display: flex;
	box-sizing: border-box;
    flex-wrap: wrap;
    align-items: center;
}
.sb-install-plugin-body .sb-install-plugin-header .sb-plugin-image {
	background-color: #fff;
    box-shadow: 0px 1px 2px rgb(0 0 0 / 5%), 0px 4px 5px rgb(0 0 0 / 5%);
    border-radius: 2px;
    padding: 15px;
    max-height: 66px;
    box-sizing: border-box;
	margin-right: 24px;
}
.sb-install-plugin-body .sb-install-plugin-header h3 {
	font-size: 18px !important;
	line-height: 25px !important;
	display: flex;
    align-items: center;
    text-transform: capitalize;
}
.sb-install-plugin-body .sb-install-plugin-header h3 span {
	color: #fff;
	background: #59AB46;
	border-radius: 2px;
	font-size: 10px;
	line-height: 16px;
	letter-spacing: 0.08em;
	text-transform: uppercase;
	padding: 0px 6px;
	margin-left: 10px;
}
.sb-install-plugin-body .sb-install-plugin-header p {
	display: flex;
	font-size: 12px;
	line-height: 18px;
	color: #434960;
	margin: 5px 0 0 0;
}
.sb-install-plugin-body .sb-install-plugin-header p .sb-author-logo {
	margin-right: 8px;
}

.sb-install-plugin-body .sb-install-plugin-content {
	padding: 20px 20px 32px 107px;
}
.sb-install-plugin-body .sb-install-plugin-content p {
	margin: 0px;
	font-size: 14px;
	line-height: 22px;
	color: #434960;
    padding-right: 20px;
}
.sb-install-plugin-body .sb-install-plugin-content .sb-plugin-btn {
	display: flex;
	align-items: center;
	justify-content: center;
	height: 38px;
	padding: 8px 20px;
	box-sizing: border-box;
	transition: all .15s ease-in-out;
	border-radius: 2px;
	width: 100%;
	margin-top: 28px;
	border: none;
	font-size: 14px;
	font-weight: 600;
	line-height: 160%;
	cursor: pointer;
}
.sb-install-plugin-body .sb-install-plugin-content .sb-btn-orange:disabled {
	color: #8C8F9A !important;
	background: #E8E8EB !important;
	cursor: not-allowed;
}
.sb-install-plugin-body .sb-install-plugin-content .sb-plugin-btn span {
	height: 20px;
	width: 20px;
	margin-right: 5px;
}
.sb-mr-fd-list button {
	cursor: pointer;
}
.sb-plugin-name strong{
	color: #434960;
	text-transform: uppercase;
}
.sb-plugin-image{
	border-radius: 13px !important;
    -webkit-transform: rotate(-3deg);
    transform: rotate(-3deg);
}
.sb-plugin-image svg{
	-webkit-transform: rotate(3deg);
    transform: rotate(3deg);
}

.sb-plugin-cta-logo {
	position: absolute;
	right: -11px;
	bottom: -10px;
	z-index: 1;
}