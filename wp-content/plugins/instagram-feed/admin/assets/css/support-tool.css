.sbi-support-tool-tab {
    overflow: hidden;
    border-bottom: 1px solid #ccc;
    background-color: #f1f1f1;
}

.sbi-support-tool-tab button {
    background-color: inherit;
    float: left;
    border: none;
    outline: none;
    cursor: pointer;
    padding: 14px 16px;
    transition: 0.3s;
    font-size: 16px;
    font-weight: 600;
    color: #3c434a;
}

.sbi-support-tool-tab button:hover {
    background-color: #ddd;
}

.sbi-support-tool-tab button.active {
    background-color: #2271b1;
    color: #fff;
}

.sbi-support-tool-tabcontent {
    display: none;
    padding: 6px 12px;
    border-top: none;
}

.sbi-support-tool-tabcontent.active {
    display: block;
}

.sbi_support_tools_wrap {
    padding: 20px;
}

.sbi_support_tools_field_group {
    margin-bottom: 20px;
}

.sb-srcs-item-avatar img {
    border-radius: 50%;
}

.sbi-fb-srcs-item-ins {
    background: #fff;
    padding: 10px;
    margin-bottom: 10px;
    border-radius: 5px;
}

.sbi-response-success {
    color: #46b450;
}

.sbi-response-error {
    color: #dc3232;
}

.sbi-checkboxes {
    margin-top: 10px;
    padding: 10px;
    border: 1px solid #ddd;
    display: grid;
    grid-template-columns: repeat(4, 1fr);
}

.sbi-checkboxes .sbi-checkbox-action-btns {
    grid-column: 1 / -1;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    margin: 10px;
    gap: 20px;
}

.sbi-confirm {
    background: #46b450 !important;
    border-color: #46b450 !important;
    color: #fff !important;
}

.sbi-cancel {
    background: #dc3232 !important;
    border-color: #dc3232 !important;
    color: #fff !important;
}

pre {
    white-space: pre-wrap;
    /* Since CSS 2.1 */
    white-space: -moz-pre-wrap;
    /* Mozilla, since 1999 */
    white-space: -pre-wrap;
    /* Opera 4-6 */
    white-space: -o-pre-wrap;
    /* Opera 7 */
    word-wrap: break-word;
    /* Internet Explorer 5.5+ */
    overflow-wrap: break-word;
    /* Modern browsers */
}

.sbi-hashtags-inner {
    margin-top: 10px;
    padding: 10px;
    border: 1px solid #ddd;
    display: grid;
    gap: 10px;
    grid-template-columns: repeat(4, 1fr);
}

.sbi-system-info {
    box-sizing: border-box;
    background: #F9F9FA;
    border: 1px solid #E8E8EB;
    width: 100%;
    resize: none;
    border-radius: 0;
    padding: 20px 28px;
    font-size: 12px;
    line-height: 18px;
    color: #141B38;
    font-family: 'Fira Code', monospace;
    word-break: break-all;
}

.sbi-response-success-preview {
    display: flex;
    gap: 10px;
    margin-top: 10px;
}

.sbi-api-notes {
    background: #cec;
    margin-top: 10px;
    padding: 10px
}

.sbi-feeds-connected-accounts {
    display: flex;
    gap: 20px;
    margin-top: 5px;
}