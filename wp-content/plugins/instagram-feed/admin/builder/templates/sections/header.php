<?php
    /**
     * SBI Header Notices
     *
     * @since 6.0
     */
    do_action('sbi_header_notices');
?>
<div class="sbi-fb-header sbi-fb-fs" v-if="!iscustomizerScreen">
    <div class="sbi-fb-header-left">
        <div class="sbi-fb-hd-logo">
			<svg width="151" height="30" viewBox="0 0 183 36" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M45.6081 11.5428C43.766 10.173 42.0183 9.98403 40.9319 9.98403C39.4676 9.98403 38.1451 10.2911 37.035 11.4247C36.0904 12.393 35.6652 13.5739 35.6652 14.9673C35.6652 15.723 35.7833 16.833 36.6099 17.7069C37.224 18.3682 38.0742 18.6988 38.8063 18.9586L40.1053 19.4073C40.554 19.5726 41.5223 19.9269 41.9474 20.2812C42.2781 20.5646 42.4906 20.8952 42.4906 21.4148C42.4906 22.0052 42.2308 22.4303 41.9238 22.6901C41.4042 23.1389 40.743 23.2333 40.2706 23.2333C39.5385 23.2333 38.9008 23.0444 38.2868 22.6665C37.8616 22.4067 37.224 21.8635 36.8225 21.462L34.9331 24.0599C35.5235 24.6504 36.4446 25.3825 37.2476 25.784C38.2395 26.28 39.2314 26.4453 40.3651 26.4453C41.4042 26.4453 43.4353 26.3036 44.8287 24.8393C45.6553 23.9891 46.2222 22.572 46.2222 20.9424C46.2222 20.0214 45.986 18.8877 45.0649 18.0139C44.4509 17.4235 43.6006 17.0692 42.9157 16.8094L41.7349 16.3607C40.6721 15.9592 40.1289 15.8175 39.751 15.4632C39.5149 15.2507 39.3968 14.9673 39.3968 14.5894C39.3968 14.1879 39.5621 13.8336 39.7983 13.5975C40.2234 13.1251 40.8374 13.0543 41.3334 13.0543C41.7821 13.0543 42.7977 13.1251 43.9785 14.1879L45.6081 11.5428Z" fill="#141B38"></path> <path d="M47.729 26.091H51.1299V20.7063C51.1299 20.352 51.1536 19.1712 51.7912 18.5571C52.1219 18.2501 52.5234 18.132 52.9721 18.132C53.3263 18.132 53.7515 18.2028 54.1057 18.5571C54.6253 19.0767 54.6489 19.9033 54.6489 20.6118V26.091H58.0498V20.848C58.0498 20.1631 58.1206 19.3365 58.5694 18.7697C58.8528 18.3918 59.3251 18.132 59.8919 18.132C60.3879 18.132 60.8366 18.3445 61.12 18.6988C61.5688 19.2656 61.5688 20.2103 61.5688 20.7535V26.091H64.9697V19.4309C64.9697 18.6752 64.8988 17.2582 63.8833 16.2898C63.2456 15.6758 62.2064 15.2979 60.9311 15.2979C60.1045 15.2979 59.4196 15.4396 58.6875 15.8884C58.0026 16.3135 57.6011 16.8094 57.3413 17.2345C57.0579 16.5969 56.6091 16.0773 56.0659 15.7703C55.3574 15.3452 54.5308 15.2979 54.0821 15.2979C53.2791 15.2979 52.0038 15.4632 51.1299 16.7858V15.5577H47.729V26.091Z" fill="#141B38"></path> <path d="M75.0016 16.7386C74.0333 15.416 72.5927 15.2035 71.6952 15.2035C70.231 15.2035 68.9556 15.723 68.0345 16.6441C67.0662 17.6124 66.405 19.1239 66.405 20.8952C66.405 22.2886 66.8301 23.6584 67.9401 24.8393C69.0973 26.0674 70.3727 26.4453 71.8842 26.4453C72.758 26.4453 74.0806 26.2327 75.0016 24.8157V26.091H78.4025V15.5577H75.0016V16.7386ZM72.5454 18.132C73.1595 18.132 73.9152 18.3682 74.4348 18.8641C74.9308 19.3365 75.2142 20.045 75.2142 20.8007C75.2142 21.6982 74.8363 22.3595 74.3876 22.7846C73.9389 23.2333 73.3012 23.5167 72.6163 23.5167C71.8133 23.5167 71.0812 23.1861 70.6088 22.6901C70.3018 22.3595 69.9003 21.7454 69.9003 20.8007C69.9003 19.8561 70.3254 19.242 70.6797 18.8877C71.1284 18.439 71.8133 18.132 72.5454 18.132Z" fill="#141B38"></path> <path d="M88.1247 16.1009C87.2745 15.6049 86.1881 15.2035 84.6294 15.2035C83.6611 15.2035 82.3857 15.3924 81.441 16.2662C80.827 16.833 80.4255 17.6833 80.4255 18.6988C80.4255 19.5018 80.6853 20.0922 81.1812 20.6118C81.63 21.0605 82.2676 21.3912 82.8817 21.5801L83.7319 21.8399C84.2279 21.9816 84.5349 22.0761 84.7711 22.2178C85.0781 22.4067 85.1489 22.6429 85.1489 22.8318C85.1489 23.0916 85.0072 23.3514 84.7947 23.5167C84.4877 23.7529 83.9445 23.7529 83.7319 23.7529C83.2832 23.7529 82.7872 23.6584 82.3149 23.4223C81.9606 23.2569 81.4883 22.9263 81.1576 22.6429L79.717 24.9338C81.0868 26.1382 82.6219 26.4453 84.0389 26.4453C85.1489 26.4453 86.4243 26.28 87.4634 25.2408C87.9358 24.7684 88.5498 23.871 88.5498 22.4776C88.5498 21.6746 88.3373 21.0369 87.7232 20.4701C87.18 19.9741 86.566 19.738 85.9755 19.549L85.0781 19.2656C84.653 19.1239 84.2987 19.0531 84.0625 18.9114C83.8972 18.8169 83.7319 18.6752 83.7319 18.439C83.7319 18.2737 83.8264 18.0848 83.9445 17.9667C84.157 17.7541 84.5585 17.6596 84.9128 17.6596C85.5741 17.6596 86.259 17.9431 86.7785 18.2501L88.1247 16.1009Z" fill="#141B38"></path> <path d="M89.9573 26.091H93.3582V20.8244C93.3582 20.3048 93.3818 19.2892 94.0667 18.628C94.2556 18.439 94.6807 18.1084 95.4129 18.1084C95.9088 18.1084 96.4284 18.2737 96.7354 18.5807C97.2786 19.1003 97.3022 19.9505 97.3022 20.6827V26.091H100.703V19.4073C100.703 18.6043 100.632 17.3054 99.664 16.3371C98.7429 15.3924 97.5148 15.2743 96.6882 15.2743C95.9324 15.2743 95.342 15.3452 94.6335 15.723C94.232 15.9356 93.7833 16.2898 93.3582 16.8094V8.87402H89.9573V26.091Z" fill="#141B38"></path> <path d="M107.852 10.3383V26.091H113.78C114.748 26.091 116.637 25.9965 117.936 24.7448C118.55 24.1308 119.164 23.1389 119.164 21.5565C119.164 20.1631 118.668 19.2656 118.149 18.746C117.582 18.1792 116.732 17.8014 115.976 17.6833C116.354 17.5179 116.897 17.1873 117.299 16.526C117.724 15.8411 117.818 15.1326 117.818 14.5185C117.818 13.8336 117.7 12.5111 116.685 11.5428C115.457 10.3855 113.52 10.3383 112.67 10.3383H107.852ZM111.442 13.1724H112.008C112.67 13.1724 113.378 13.1724 113.898 13.5739C114.181 13.7864 114.512 14.2115 114.512 14.8964C114.512 15.5813 114.205 16.0537 113.874 16.2898C113.355 16.6677 112.528 16.7386 112.032 16.7386H111.442V13.1724ZM111.442 19.4309H112.434C113.166 19.4309 114.394 19.4309 115.031 20.0214C115.268 20.2339 115.527 20.659 115.527 21.2967C115.527 21.8635 115.338 22.3122 115.008 22.6193C114.347 23.2333 113.237 23.2569 112.315 23.2569H111.442V19.4309Z" fill="#141B38"></path> <path d="M128.842 16.7386C127.873 15.416 126.433 15.2035 125.535 15.2035C124.071 15.2035 122.796 15.723 121.875 16.6441C120.906 17.6124 120.245 19.1239 120.245 20.8952C120.245 22.2886 120.67 23.6584 121.78 24.8393C122.937 26.0674 124.213 26.4453 125.724 26.4453C126.598 26.4453 127.921 26.2327 128.842 24.8157V26.091H132.243V15.5577H128.842V16.7386ZM126.385 18.132C126.999 18.132 127.755 18.3682 128.275 18.8641C128.771 19.3365 129.054 20.045 129.054 20.8007C129.054 21.6982 128.676 22.3595 128.228 22.7846C127.779 23.2333 127.141 23.5167 126.456 23.5167C125.653 23.5167 124.921 23.1861 124.449 22.6901C124.142 22.3595 123.74 21.7454 123.74 20.8007C123.74 19.8561 124.165 19.242 124.52 18.8877C124.968 18.439 125.653 18.132 126.385 18.132Z" fill="#141B38"></path> <path d="M134.265 8.87402V26.091H137.666V8.87402H134.265Z" fill="#141B38"></path> <path d="M139.784 8.87402V26.091H143.185V8.87402H139.784Z" fill="#141B38"></path> <path d="M157.064 20.8244C157.064 19.4782 156.545 17.9667 155.482 16.9039C154.537 15.9592 152.931 15.2035 150.877 15.2035C148.822 15.2035 147.216 15.9592 146.271 16.9039C145.208 17.9667 144.689 19.4782 144.689 20.8244C144.689 22.1705 145.208 23.6821 146.271 24.7448C147.216 25.6895 148.822 26.4453 150.877 26.4453C152.931 26.4453 154.537 25.6895 155.482 24.7448C156.545 23.6821 157.064 22.1705 157.064 20.8244ZM150.877 18.0848C151.656 18.0848 152.294 18.3445 152.79 18.8405C153.285 19.3365 153.569 19.9741 153.569 20.8244C153.569 21.6746 153.285 22.3122 152.79 22.8082C152.294 23.3042 151.656 23.564 150.9 23.564C150.003 23.564 149.389 23.2333 148.964 22.8082C148.562 22.4067 148.184 21.7927 148.184 20.8244C148.184 19.9741 148.468 19.3365 148.964 18.8405C149.46 18.3445 150.097 18.0848 150.877 18.0848Z" fill="#141B38"></path> <path d="M170.332 20.8244C170.332 19.4782 169.813 17.9667 168.75 16.9039C167.805 15.9592 166.199 15.2035 164.145 15.2035C162.09 15.2035 160.484 15.9592 159.539 16.9039C158.476 17.9667 157.957 19.4782 157.957 20.8244C157.957 22.1705 158.476 23.6821 159.539 24.7448C160.484 25.6895 162.09 26.4453 164.145 26.4453C166.199 26.4453 167.805 25.6895 168.75 24.7448C169.813 23.6821 170.332 22.1705 170.332 20.8244ZM164.145 18.0848C164.924 18.0848 165.562 18.3445 166.058 18.8405C166.554 19.3365 166.837 19.9741 166.837 20.8244C166.837 21.6746 166.554 22.3122 166.058 22.8082C165.562 23.3042 164.924 23.564 164.168 23.564C163.271 23.564 162.657 23.2333 162.232 22.8082C161.83 22.4067 161.452 21.7927 161.452 20.8244C161.452 19.9741 161.736 19.3365 162.232 18.8405C162.728 18.3445 163.365 18.0848 164.145 18.0848Z" fill="#141B38"></path> <path d="M171.745 26.091H175.145V20.6827C175.145 19.9978 175.24 19.242 175.807 18.6752C176.067 18.3918 176.515 18.1084 177.224 18.1084C177.838 18.1084 178.263 18.3209 178.523 18.5807C179.066 19.1239 179.09 19.9978 179.09 20.6827V26.091H182.49V19.4309C182.49 18.5807 182.42 17.329 181.428 16.3371C180.53 15.4396 179.326 15.2743 178.405 15.2743C177.413 15.2743 176.185 15.4869 175.145 16.8094V15.5577H171.745V26.091Z" fill="#141B38"></path> <path fill-rule="evenodd" clip-rule="evenodd" d="M27.2235 16.8291C27.2235 7.53469 21.1311 0 13.6131 0C6.09513 0 0 7.53469 0 16.8291C0 25.7393 5.5828 33.0095 12.6525 33.6193L11.9007 36L16.6147 35.599L14.9608 33.5775C21.8439 32.7422 27.2235 25.5639 27.2235 16.8291Z" fill="#FE544F"></path> <path fill-rule="evenodd" clip-rule="evenodd" d="M16.8586 5.91699L17.5137 12.6756L24.3006 12.8705L19.3911 17.4354L23.2687 23.044L16.7362 21.816L14.7557 28.3487L11.7488 22.4987L5.67719 25.2808L8.01283 19.0094L2.09131 16.0227L8.43013 13.9841L6.68099 7.73959L12.678 11.1585L16.8586 5.91699Z" fill="white"></path></svg>
            <span class="breadcrumb-title">/ {{isSetupPage === 'true' ? genericText.setup : genericText.dashboard}}</span>
        </div>
        <div v-if="sbi_builder.pluginsInfo.social_wall.activated" class="sb-social-wall-link-wrap">
            <div v-for="(link, key) in allFeedsScreen.socialWallLinks" class="sb-social-wall-link" v-html="link"></div>
        </div>
    </div>
    <div class="sbi-fb-header-right">
        <a class="sbi-fb-hd-btn sb-btn-grey sb-button-standard" data-icon="left" :href="supportPageUrl">
            <svg class="sb" width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M8.16797 14.0001H9.83464V12.3334H8.16797V14.0001ZM9.0013 0.666748C4.4013 0.666748 0.667969 4.40008 0.667969 9.00008C0.667969 13.6001 4.4013 17.3334 9.0013 17.3334C13.6013 17.3334 17.3346 13.6001 17.3346 9.00008C17.3346 4.40008 13.6013 0.666748 9.0013 0.666748ZM9.0013 15.6667C5.3263 15.6667 2.33464 12.6751 2.33464 9.00008C2.33464 5.32508 5.3263 2.33341 9.0013 2.33341C12.6763 2.33341 15.668 5.32508 15.668 9.00008C15.668 12.6751 12.6763 15.6667 9.0013 15.6667ZM9.0013 4.00008C7.15964 4.00008 5.66797 5.49175 5.66797 7.33342H7.33464C7.33464 6.41675 8.08464 5.66675 9.0013 5.66675C9.91797 5.66675 10.668 6.41675 10.668 7.33342C10.668 9.00008 8.16797 8.79175 8.16797 11.5001H9.83464C9.83464 9.62508 12.3346 9.41675 12.3346 7.33342C12.3346 5.49175 10.843 4.00008 9.0013 4.00008Z" fill="#141B38"/>
            </svg>
            <span>{{genericText.help}}</span>
        </a>
    </div>
	<div class="sb-loadingbar-ctn" v-if="loadingBar"></div>
</div>
<!--CFF customizer Heading-->
<div class="sbi-fb-header sbi-csz-header sbi-fb-fs" v-if="iscustomizerScreen" v-bind:class="{ 'sb-onboarding-highlight' : viewsActive.onboardingStep === 1 && viewsActive.onboardingCustomizerPopup }">
	<div class="sbi-csz-header-insider">
		<button class="sbi-fb-hd-btn sbi-btn-grey sb-button-standard sbi-small-chevron" data-icon="left" @click.prevent.default="JSON.stringify(customizerFeedDataInitial) === JSON.stringify(customizerFeedData) ? window.location = builderUrl : openDialogBox('backAllToFeed')">
            <svg width="6" height="8" viewBox="0 0 6 8" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M5.27203 0.94L4.33203 0L0.332031 4L4.33203 8L5.27203 7.06L2.2187 4L5.27203 0.94Z" fill="#141B38"/>
            </svg>

            <span>{{genericText.backAllFeeds}}</span>
		</button>
		<div class="sbi-csz-hd-name" :data-edit="viewsActive.editName">
			<input id="sbi-csz-hd-input" v-model="customizerFeedData.feed_info.feed_name" type="text" :style="'width:' + customizerScreens.inputNameWidth + ';'" :onfocus="updateInputWidth()" :onkeypress="updateInputWidth()" @keyup.enter="activateView('editName')">
			<span class="sb-bold sb-standard-p" v-if="!viewsActive['editName']">{{customizerFeedData.feed_info.feed_name}}</span>
			<button v-if="customizerFeedData.feed_info.id !== 'legacy'" class="sbi-csz-name-ed-btn" v-html="viewsActive.editName ? svgIcons['checkmarklarge'] : svgIcons['edit']" @click.prevent.default="activateView('editName')"></button>
		</div>

		<div class="sbi-csz-hd-actions">
			<a :href="supportPageUrl" class="sbi-fb-hd-btn sbi-btn-grey sb-button-standard" target="_blank">
                    <svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M8.16797 14.0001H9.83464V12.3334H8.16797V14.0001ZM9.0013 0.666748C4.4013 0.666748 0.667969 4.40008 0.667969 9.00008C0.667969 13.6001 4.4013 17.3334 9.0013 17.3334C13.6013 17.3334 17.3346 13.6001 17.3346 9.00008C17.3346 4.40008 13.6013 0.666748 9.0013 0.666748ZM9.0013 15.6667C5.3263 15.6667 2.33464 12.6751 2.33464 9.00008C2.33464 5.32508 5.3263 2.33341 9.0013 2.33341C12.6763 2.33341 15.668 5.32508 15.668 9.00008C15.668 12.6751 12.6763 15.6667 9.0013 15.6667ZM9.0013 4.00008C7.15964 4.00008 5.66797 5.49175 5.66797 7.33342H7.33464C7.33464 6.41675 8.08464 5.66675 9.0013 5.66675C9.91797 5.66675 10.668 6.41675 10.668 7.33342C10.668 9.00008 8.16797 8.79175 8.16797 11.5001H9.83464C9.83464 9.62508 12.3346 9.41675 12.3346 7.33342C12.3346 5.49175 10.843 4.00008 9.0013 4.00008Z" fill="#141B38"/>
                    </svg>
				<span>{{genericText.help}}</span>
			</a>
			<button v-if="customizerFeedData.feed_info.id !== 'legacy'" class="sbi-fb-hd-btn sbi-csz-btn-embd sbi-btn-dark sb-button-standard" @click.prevent.default="activateView('embedPopup', null, true)">
                <svg width="16" height="12" viewBox="0 0 16 12" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M16 6L12.2287 9.77133L11.286 8.82867L14.1147 6L11.286 3.17133L12.2287 2.22867L16 6ZM1.88533 6L4.714 8.82867L3.77133 9.77133L0 6L3.77133 2.22867L4.71333 3.17133L1.88533 6ZM6.52533 12H5.10667L9.47467 0H10.8933L6.52533 12Z" fill="white"/>
                </svg>
				<span>{{customizeScreensText.common.embed}}</span>
			</button>
			<button class="sbi-fb-hd-btn sbi-csz-btn-save sbi-btn-orange sb-button-standard" @click.prevent.default="saveFeedSettings()">
                <svg width="16" height="12" viewBox="0 0 16 12" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path fill-rule="evenodd" clip-rule="evenodd" d="M6.08058 8.36133L14.0355 0.406383L15.8033 2.17415L6.08058 11.8969L0.777281 6.59357L2.54505 4.8258L6.08058 8.36133Z" fill="white"/>
                </svg>
				<span>{{customizeScreensText.common.save}}</span>
			</button>
		</div>
	</div>
	<div class="sb-loadingbar-ctn" v-if="loadingBar"></div>
</div>
