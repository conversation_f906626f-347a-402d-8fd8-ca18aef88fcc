<svg width="248" height="118" viewBox="0 0 248 118" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_dd)">
<rect x="16.3555" y="21.3516" width="82" height="82" rx="2" transform="rotate(-7 16.3555 21.3516)" fill="white"/>
<path d="M59.9242 39.7491C50.371 40.9221 43.5123 49.6808 44.6917 59.2861C45.758 67.9709 53.0004 74.3987 61.463 74.682L59.9701 62.5233L55.5582 63.065L54.9397 58.0278L59.3516 57.4861L58.8802 53.6475C58.3449 49.2877 60.6387 46.5729 64.6163 46.0845C66.5096 45.8521 68.5302 45.939 68.5302 45.939L69.057 50.2292L66.8685 50.498C64.7146 50.7624 64.2014 52.183 64.3699 53.5552L64.7709 56.8207L69.5996 56.2278L69.4365 61.361L65.3894 61.8579L66.8823 74.0166C70.8959 72.8676 74.3666 70.3216 76.6677 66.8381C78.9689 63.3547 79.9489 59.1633 79.4308 55.0207C78.2514 45.4153 69.4775 38.5761 59.9242 39.7491Z" fill="#006BFA"/>
</g>
<g filter="url(#filter1_dd)">
<rect x="93" y="7" width="82" height="82" rx="2" fill="white"/>
<path d="M134.002 38.5664C128.752 38.5664 124.568 42.832 124.568 48C124.568 53.25 128.752 57.4336 134.002 57.4336C139.17 57.4336 143.436 53.25 143.436 48C143.436 42.832 139.17 38.5664 134.002 38.5664ZM134.002 54.1523C130.639 54.1523 127.85 51.4453 127.85 48C127.85 44.6367 130.557 41.9297 134.002 41.9297C137.365 41.9297 140.072 44.6367 140.072 48C140.072 51.4453 137.365 54.1523 134.002 54.1523ZM145.979 38.2383C145.979 37.0078 144.994 36.0234 143.764 36.0234C142.533 36.0234 141.549 37.0078 141.549 38.2383C141.549 39.4688 142.533 40.4531 143.764 40.4531C144.994 40.4531 145.979 39.4688 145.979 38.2383ZM152.213 40.4531C152.049 37.5 151.393 34.875 149.26 32.7422C147.127 30.6094 144.502 29.9531 141.549 29.7891C138.514 29.625 129.408 29.625 126.373 29.7891C123.42 29.9531 120.877 30.6094 118.662 32.7422C116.529 34.875 115.873 37.5 115.709 40.4531C115.545 43.4883 115.545 52.5938 115.709 55.6289C115.873 58.582 116.529 61.125 118.662 63.3398C120.877 65.4727 123.42 66.1289 126.373 66.293C129.408 66.457 138.514 66.457 141.549 66.293C144.502 66.1289 147.127 65.4727 149.26 63.3398C151.393 61.125 152.049 58.582 152.213 55.6289C152.377 52.5938 152.377 43.4883 152.213 40.4531ZM148.275 58.8281C147.701 60.4688 146.389 61.6992 144.83 62.3555C142.369 63.3398 136.627 63.0938 134.002 63.0938C131.295 63.0938 125.553 63.3398 123.174 62.3555C121.533 61.6992 120.303 60.4688 119.646 58.8281C118.662 56.4492 118.908 50.707 118.908 48C118.908 45.375 118.662 39.6328 119.646 37.1719C120.303 35.6133 121.533 34.3828 123.174 33.7266C125.553 32.7422 131.295 32.9883 134.002 32.9883C136.627 32.9883 142.369 32.7422 144.83 33.7266C146.389 34.3008 147.619 35.6133 148.275 37.1719C149.26 39.6328 149.014 45.375 149.014 48C149.014 50.707 149.26 56.4492 148.275 58.8281Z" fill="url(#paint0_linear)"/>
</g>
<g filter="url(#filter2_dd)">
<rect x="160.559" y="11.9888" width="82" height="82" rx="2" transform="rotate(6 160.559 11.9888)" fill="white"/>
<path d="M193.02 61.9051L202.601 57.6333L194.117 51.4626L193.02 61.9051ZM214.571 50.3923C214.712 51.2341 214.753 52.347 214.711 53.7503C214.687 55.1554 214.613 56.3618 214.503 57.4061L214.454 58.879C214.053 62.6905 213.48 65.4633 212.804 67.2047C212.205 68.7253 211.089 69.6287 209.477 69.8992C208.635 70.0394 207.122 70.0388 204.814 69.9017C202.538 69.7857 200.462 69.6203 198.547 69.4191L195.769 69.2326C188.477 68.4662 183.963 67.7103 182.222 67.0345C180.701 66.4348 179.798 65.3193 179.528 63.7072C179.387 62.8654 179.346 61.7525 179.388 60.3492C179.412 58.944 179.486 57.7377 179.596 56.6934L179.645 55.2205C180.046 51.409 180.619 48.6362 181.295 46.8948C181.894 45.3741 183.01 44.4708 184.622 44.2003C185.464 44.06 186.977 44.0607 189.285 44.1978C191.561 44.3137 193.637 44.4792 195.552 44.6804L198.33 44.8668C205.622 45.6333 210.135 46.3892 211.877 47.0649C213.398 47.6647 214.301 48.7802 214.571 50.3923Z" fill="#EB2121"/>
</g>
<defs>
<filter id="filter0_dd" x="0.355469" y="4.3584" width="113.382" height="113.382" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/>
<feOffset dy="1"/>
<feGaussianBlur stdDeviation="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/>
<feOffset dx="-5" dy="4"/>
<feGaussianBlur stdDeviation="5.5"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.1 0"/>
<feBlend mode="normal" in2="effect1_dropShadow" result="effect2_dropShadow"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect2_dropShadow" result="shape"/>
</filter>
<filter id="filter1_dd" x="77" y="0" width="104" height="104" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/>
<feOffset dy="1"/>
<feGaussianBlur stdDeviation="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/>
<feOffset dx="-5" dy="4"/>
<feGaussianBlur stdDeviation="5.5"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.1 0"/>
<feBlend mode="normal" in2="effect1_dropShadow" result="effect2_dropShadow"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect2_dropShadow" result="shape"/>
</filter>
<filter id="filter2_dd" x="135.988" y="4.98877" width="112.122" height="112.122" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/>
<feOffset dy="1"/>
<feGaussianBlur stdDeviation="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/>
<feOffset dx="-5" dy="4"/>
<feGaussianBlur stdDeviation="5.5"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.1 0"/>
<feBlend mode="normal" in2="effect1_dropShadow" result="effect2_dropShadow"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect2_dropShadow" result="shape"/>
</filter>
<linearGradient id="paint0_linear" x1="128.678" y1="99.9504" x2="206.083" y2="20.9379" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="0.147864" stop-color="#F6640E"/>
<stop offset="0.443974" stop-color="#BA03A7"/>
<stop offset="0.733337" stop-color="#6A01B9"/>
<stop offset="1" stop-color="#6B01B9"/>
</linearGradient>
</defs>
</svg>
