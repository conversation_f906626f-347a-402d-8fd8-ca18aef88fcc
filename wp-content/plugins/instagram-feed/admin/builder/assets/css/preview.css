body{
	overflow-x:hidden;
}
.sbi-preview-ctn .no-overflow{
	overflow: hidden!important;
}
.sbi-preview-ctn .sbi-fb-fs{
	width: 100%;
	position: relative;
	float: left;
	box-sizing: border-box;
}
.sbi-fullsize-link{
	position: absolute;
	width: 100%;
	height: 100%;
	left: 0;
	top: 0;
	z-index: 9;
	cursor: pointer;
}
.sbi-feed-height{
	overflow-x: hidden!important;
}
.sbi-preview-ctn h1,
.sbi-preview-ctn h2,
.sbi-preview-ctn h3,
.sbi-preview-ctn h4,
.sbi-preview-ctn h5,
.sbi-preview-ctn h6{
	padding: 0px;
	margin: 0px;
	line-height: 1em;
}
.sbi-preview-ctn  h3, .sbi-preview-ctn  h4, .sbi-preview-ctn  h5, .sbi-preview-ctn  h6, .sbi-preview-ctn  p {
    float: left;
    width: 100%;
    clear: both;
    padding: 0;
    margin: 5px 0;
    line-height: 1.4;
    word-wrap: break-word;
}

/*Preview Sections Highlights*/
.sbi-preview-section{
	opacity: 1;
}
.sbi-preview-section[data-dimmed="true"]{
	opacity: 0.4;
}


.sbi-preview-ctn .sbi-preview-header-visual{
	margin-bottom: 25px;
}
.sbi-preview-ctn .sbi-preview-header-cover{
	overflow: hidden;
	height: 300px;
}
.sbi-preview-ctn .sbi-preview-header-cover img{
	position: absolute;
	width: 100%;
	height: auto;
	left: 0px;
	top: 50%;
	-webkit-transform: translateY(-50%);
	transform: translateY(-50%);
}
.sbi-preview-ctn .sbi-preview-header-likebox{
	position: absolute;
	right: 10px;
	bottom: 10px;
	display: flex;
	align-items: center;
	justify-content: center;
	background: #eee;
    border-radius: 2px;
    padding: 3px 5px;
    color: #445790;
}
.sbi-preview-ctn .sbi-preview-header-likebox svg{
	fill: currentColor;
	width: 18px;
	margin-right: 5px;
	float: left;
}
.sbi-preview-ctn .sbi-preview-header-likebox span{
    font-size: 15px;
    line-height: 20px;
}
.sbi-preview-ctn .sbi-preview-header-info-ctn{
	padding: 0 20px;
}
.sbi-preview-ctn .sbi-preview-header-avatar{
	width: 100px;
	border-color: 3px;
	border: 2px solid #fff;
	margin-top: -25px;
	border-radius: 3px;
	position: absolute;
	left: 20px;
}
.sbi-preview-ctn .sbi-preview-header-avatar img{
	width: 100%;
	border-radius: 3px;
	float: left;
}
.sbi-preview-ctn .sbi-preview-header-info{
	padding-top: 10px;
	margin-left: 125px;
}

.sbi-preview-ctn .sbi-preview-header-text-h{
	display: flex;
	align-items: center;
	line-height: 1em
}
.sbi-preview-ctn .sbi-header-text{
	line-height: 1em!important;
}
.sbi-preview-ctn .sbi-preview-header-text-icon{
	margin-right: 10px;
}
.sbi-preview-ctn .sbi-preview-header-text-icon svg{
	float: left;
}
.sbi-preview-ctn .sbi-preview-header-name {
    display: inline-block;
    margin: 0 8px 0 0;
    padding: 0;
    font-size: 1.2em;
    line-height: 1.2em;
}
.sbi-preview-ctn .sbi-preview-header-bio{
    margin: 3px 0 0 0;
    padding: 0;
    line-height: 1.2em;
}

/*Load More Button*/
.sbi-preview-ctn .sbi-preview-loadmore-ctn{
	margin-bottom: 20px;
}
.sbi-preview-ctn .sbi-preview-loadmore-btn{
	display: flex;
	justify-content: center;
	align-items: center;
	cursor: pointer;
	min-height: 45px;
	padding: 5px 0;
    margin: 10px 0 0 0;
    line-height: 1em;
    font-size: 15px;

    border: none;
    background: #eee;
    background: rgba(0,0,0,0.05);
    width: 100%;
    position: relative;
    text-align: center;
    box-sizing: border-box;
    outline: none;
    text-decoration: none;
    -moz-border-radius: 5px;
    -webkit-border-radius: 5px;
    border-radius: 5px;
    -moz-transition: background 0.1s ease-in-out;
    -webkit-transition: background 0.1s ease-in-out;
    -o-transition: background 0.1s ease-in-out;
    transition: background 0.1s ease-in-out;
}
.sbi-preview-ctn .sbi-preview-loadmore-btn:hover {
    background: #ddd;
    background: rgba(0,0,0,0.1);
    text-decoration: none;
    -moz-transition: background 0.1s ease-in-out;
    -webkit-transition: background 0.1s ease-in-out;
    -o-transition: background 0.1s ease-in-out;
    transition: background 0.1s ease-in-out;
}

/*Post List Layout*/
/* Masonry Layout  */
/*
[data-feed-layout="masonry"] .sbi-post-item-ctn{
	margin: 0px!important;
}
[data-feed-layout="masonry"]{
	display: grid;
    grid-gap: 20px;
  	grid-auto-rows: 0;
}

[data-feed-layout="masonry"][data-feed-columns="1"]{
	grid-template-columns: repeat(1, minmax(100px,1fr));
}
[data-feed-layout="masonry"][data-feed-columns="2"]{
	grid-template-columns: repeat(2, minmax(100px,1fr));
}
[data-feed-layout="masonry"][data-feed-columns="3"]{
	grid-template-columns: repeat(3, minmax(100px,1fr));
}
[data-feed-layout="masonry"][data-feed-columns="4"]{
	grid-template-columns: repeat(4, minmax(100px,1fr));
}
[data-feed-layout="masonry"][data-feed-columns="5"]{
	grid-template-columns: repeat(5, minmax(100px,1fr));
}
[data-feed-layout="masonry"][data-feed-columns="6"]{
	grid-template-columns: repeat(6, minmax(100px,1fr));
}
*/
[data-feed-layout="masonry"] .sbi-post-item-ctn{
	height: auto!important;
}
[data-feed-layout="masonry"][data-feed-columns="1"] .sbi-post-item-ctn{
	width: 100%;
}
[data-feed-layout="masonry"][data-feed-columns="2"] .sbi-post-item-ctn{
	width: 47%;
  	margin: 8px 1.5%;
}
[data-feed-layout="masonry"][data-feed-columns="3"] .sbi-post-item-ctn{
	width: 30.33%;
  	margin: 8px 1.5%;
}
[data-feed-layout="masonry"][data-feed-columns="4"] .sbi-post-item-ctn{
  	width: 22%;
  	margin: 8px 1.5%;
}
[data-feed-layout="masonry"][data-feed-columns="5"] .sbi-post-item-ctn{
	width: 17%;
  	margin: 8px 1.5%;
}
[data-feed-layout="masonry"][data-feed-columns="6"] .sbi-post-item-ctn{
	width: 13.516%;
  	margin: 8px 1.5%;
}

[data-feed-layout="masonry"] .sbi-media-item-ctn .sbi-media-item-image-real{
	height: 100%;
	position: absolute;
	width: 100%;
	left: 0;
	top: 0;
	bottom: 0;
	right: 0;
}
[data-feed-layout="masonry"] .sbi-media-item-ctn .sbi-media-item-image-poster{
	opacity: 0;
}

/* Grid Layout  */
[data-feed-layout="grid"]{
	display: grid;
    grid-gap: 20px;
}
[data-feed-layout="grid"][data-feed-columns="1"]{
	grid-template-columns: repeat(1, minmax(100px,1fr));
}
[data-feed-layout="grid"][data-feed-columns="2"]{
	grid-template-columns: 1fr 1fr;
}

[data-feed-layout="grid"][data-feed-columns="3"]{
	grid-template-columns: repeat(3, minmax(100px,1fr));
}

[data-feed-layout="grid"][data-feed-columns="4"]{
	grid-template-columns: repeat(4, minmax(100px,1fr));
}
[data-feed-layout="grid"][data-feed-columns="5"]{
	grid-template-columns: repeat(5, minmax(100px,1fr));
}
[data-feed-layout="grid"][data-feed-columns="6"]{
	grid-template-columns: repeat(6, minmax(100px,1fr));
}

[data-feed-layout="grid"] .sbi-post-item-ctn{
	overflow: hidden;
	margin: 0px!important
}
[data-feed-layout="grid"] .sbi-post-item-ctn .sbi-post-grid-image{
	position: absolute;
	width: 100%;
	height: 100%;
	left: 0px;
	top: 0px;
	bottom: 0px;
	right: 0px;
	background-size: cover !important;
	background-position: center center !important;
}
[data-feed-layout="grid"] .sbi-photos-item-ctn img{
	opacity: 0
}
[data-feed-layout="grid"] .sbi-singlemedia-item.sbi-albums-item-ctn .sbi-albums-item-cover{
	height: calc(100% - 70px);
	left: 0;
	top: 0;
	right: 0;
}
[data-feed-layout="grid"] .sbi-albums-item-ctn .sbi-post-item-content{
	height: inherit;
}


.sbi-preview-ctn .sbi-media-item-ctn{
	background: none!important;
}
.sbi-preview-ctn .sbi-singlemedia-item{
	background: none!important;
	padding: 0px!important
}


[data-feed-layout="list"] .sbi-post-item-ctn{
	height: auto!important;
}

/*Post Single Item*/
.sbi-post-item-info-ctn{
	display: flex;
	align-items: center;
	margin-bottom: 15px;
    padding: 0;
    line-height: 1.2;
    width: 100%;
}
.sbi-post-item-info{
	float: left;
    padding: 3px 0 0 0;
    font-size: 14px;
    font-weight: bold;
    line-height: 1.2;
}
.sbi-post-item-date{
	color: #9197a3;
    font-weight: normal;
    font-size: 11px;
    margin-top: 1px !important;
    margin-bottom: 0px !important;
}

.sbi-post-item-avatar,
.sbi-post-item-avatar img{
	width: 45px;
	height: 45px;
	border-radius: 50px;
}
.sbi-post-item-avatar{
	margin-right: 10px;
}
.sbi-post-item-author-name{
	font-weight: bold;
}
.sbi-post-item-story{
	font-weight: normal;
	font-size: 14px;
	padding: 3px 0 0 0;
    line-height: 1.2;
}
.sbi-preview-ctn .sbi-post-item-ctn{
	width: 100%;
	float: left;
	position: relative;
	box-sizing: border-box;
    padding: 15px;
    margin: 8px 0;
}

.sbi-preview-ctn .sbi-post-item-ctn[data-post-layout="half"] .sbi-post-item-sides,
.sbi-preview-ctn .sbi-post-item-ctn[data-post-layout="half"][data-post-type="links"]  .sbi-post-item-link-ctn{
	display: grid!important;
	grid-template-columns: calc(50% - 15px) calc(50% - 15px);
	grid-column-gap: 30px;
}

.sbi-preview-ctn .sbi-post-item-ctn[data-post-layout="half"][data-post-type="links"] .sbi-post-item-sides{
	display: flex;
	flex-direction: column-reverse;
}

.sbi-preview-ctn .sbi-post-item-ctn[data-post-layout="thumb"] .sbi-post-item-sides,
.sbi-preview-ctn .sbi-post-item-ctn[data-post-layout="thumb"][data-post-type="links"]  .sbi-post-item-link-ctn{
	display: grid;
	grid-template-columns: calc(20% - 15px) calc(80% - 15px);
	grid-column-gap: 30px;
}

.sbi-preview-ctn .sbi-post-item-ctn[data-post-layout="thumb"][data-post-type="links"] .sbi-post-item-sides{
	display: flex;
	flex-direction: column-reverse;
}

/* Added for events */
.sbi-preview-ctn .sbi-preview-posts-list-ctn[data-feed-type="events"] .sbi-post-item-ctn[data-post-layout="half"] .sbi-post-item-sides,
.sbi-preview-ctn .sbi-preview-posts-list-ctn[data-feed-type="events"] .sbi-post-item-ctn[data-post-layout="thumb"] .sbi-post-item-sides{
	display: grid!important;
}

[data-feed-type="reviews"] [data-post-layout="half"] .sbi-post-item-sides,
[data-feed-type="reviews"] [data-post-layout="thumb"] .sbi-post-item-sides{
	grid-template-columns: 100%!important;
}

.sbi-preview-ctn .sbi-post-item-ctn[data-post-layout="half"][data-post-type="statuses"] .sbi-post-item-sides,
.sbi-preview-ctn .sbi-post-item-ctn[data-post-layout="thumb"][data-post-type="statuses"] .sbi-post-item-sides{
	display: block!important;
	width: 100%!important;
}

.sbi-preview-ctn .sbi-preview-posts-list-ctn[data-boxshadow="true"] .sbi-post-item-ctn{
	box-shadow: 0 0 10px 0 rgba(0,0,0, 0.15);
}
.sbi-preview-ctn .sbi-preview-posts-list-ctn[data-poststyle="regular"] .sbi-post-item-ctn{
    padding: 15px 0px;
}
.sbi-preview-ctn [data-feed-layout="carousel"][data-feed-type="singlealbum"] .sbi-post-item-ctn{
	height: 100%;
	background-position: center!important;
	background-size: cover!important;
}
/*Narrow Rules for Preview*/
[data-preview-device="mobile"] [data-narrow="active"] [data-post-layout="half"] .sbi-post-item-sides,
[data-preview-device="mobile"] [data-narrow="active"] [data-post-layout="thumb"] .sbi-post-item-sides,
[data-narrow="active"][data-feed-columns="3"] [data-post-layout="half"] .sbi-post-item-sides,
[data-narrow="active"][data-feed-columns="3"] [data-post-layout="thumb"] .sbi-post-item-sides,
[data-narrow="active"][data-feed-columns="4"] [data-post-layout="half"] .sbi-post-item-sides,
[data-narrow="active"][data-feed-columns="4"] [data-post-layout="thumb"] .sbi-post-item-sides,
[data-narrow="active"][data-feed-columns="5"] [data-post-layout="half"] .sbi-post-item-sides,
[data-narrow="active"][data-feed-columns="5"] [data-post-layout="thumb"] .sbi-post-item-sides,
[data-narrow="active"][data-feed-columns="6"] [data-post-layout="half"] .sbi-post-item-sides,
[data-narrow="active"][data-feed-columns="6"] [data-post-layout="thumb"] .sbi-post-item-sides,
[data-preview-device="mobile"] [data-narrow="active"] [data-post-layout="half"][data-post-type="links"] .sbi-post-item-link-ctn,
[data-preview-device="mobile"] [data-narrow="active"] [data-post-layout="thumb"][data-post-type="links"] .sbi-post-item-link-ctn{
    display: flex !important;
    flex-direction: column-reverse;
}
[data-narrow="active"][data-feed-columns="3"] [data-post-layout="half"][data-post-type="links"] .sbi-post-item-link-ctn,
[data-narrow="active"][data-feed-columns="3"] [data-post-layout="thumb"][data-post-type="links"] .sbi-post-item-link-ctn,
[data-narrow="active"][data-feed-columns="4"] [data-post-layout="half"][data-post-type="links"] .sbi-post-item-link-ctn,
[data-narrow="active"][data-feed-columns="4"] [data-post-layout="thumb"][data-post-type="links"] .sbi-post-item-link-ctn,
[data-narrow="active"][data-feed-columns="5"] [data-post-layout="half"][data-post-type="links"] .sbi-post-item-link-ctn,
[data-narrow="active"][data-feed-columns="5"] [data-post-layout="thumb"][data-post-type="links"] .sbi-post-item-link-ctn,
[data-narrow="active"][data-feed-columns="6"] [data-post-layout="half"][data-post-type="links"] .sbi-post-item-link-ctn,
[data-narrow="active"][data-feed-columns="6"] [data-post-layout="thumb"][data-post-type="links"] .sbi-post-item-link-ctn{
    display: flex !important;
    flex-direction: column;

}


.sbi-preview-ctn .sbi-post-item-text{
	padding: 0;
    margin: 5px 0;
    line-height: 1.4;
    word-wrap: break-word;
}
.sbi-preview-ctn .sbi-post-item-text a:hover{
	text-decoration: underline;
}

.sbi-post-item-text-expand a{
    font-size: 11px;
    font-weight: normal;
    cursor: pointer;
}
.sbi-preview-ctn .sbi-post-item-text-expand a:hover{
	text-decoration: underline;
}

.sbi-preview-ctn .sbi-post-item-meta-top{
	display: flex;
	align-items: center;
}
.sbi-preview-ctn .sbi-post-item-action-link{
	margin-left: auto;
}
.sbi-preview-ctn .sbi-post-item-action-txt{
	font-size: 11px;
	cursor: pointer;
	font-weight: normal;
}
.sbi-preview-ctn .sbi-post-item-action-link a:hover{
	text-decoration: underline;
}
.sbi-preview-ctn .sbi-post-item-dot{
	padding: 0 5px;
}

.sbi-preview-ctn .sbi-post-item-share-link{
	position: relative;
}
.sbi-preview-ctn .sbi-post-item-share-tooltip{
	position: absolute;
	bottom: 22px;
	right: -5px;
	width: 100px;
	padding: 3px 5px;
	margin: 0;
	background: #333;
	color: #fff;
	font-size: 12px;
	line-height: 1.4;
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 9999;
	-moz-border-radius: 4px;
    -webkit-border-radius: 4px;
    border-radius: 4px;
}
.sbi-preview-ctn .sbi-post-item-share-tooltip:before{
	content: '';
	position: absolute;
	right: 11px;
	bottom: -4px;
	border-top: 7px solid #333;
	border-left: 7px solid transparent;
	border-right: 7px solid transparent;
}

.sbi-preview-ctn .sbi-post-item-share-tooltip a{
	display: flex;
	width: 26px;
	height: 26px;
	justify-content: center;
	align-items: center;
    color: #fff !important;
    -moz-border-radius: 4px;
    -webkit-border-radius: 4px;
    border-radius: 4px;
}
.sbi-preview-ctn .sbi-post-item-share-tooltip svg{
	width: 16px;
}

.sbi-preview-ctn .sbi-post-item-meta{
	height: 37px;
    margin: 5px 12px 5px 0;
    display: flex;
    padding: 0 5px 0 10px;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    border-radius: 4px;
    background: rgba(0,0,0,0.05);
    box-shadow: 0 0 0 1px rgba(0,0,0,0.07);
    -webkit-transition: all .2s ease-in-out;
    transition: all .2s ease-in-out;
}
.sbi-preview-ctn .sbi-post-item-meta:hover{
    box-shadow: 0 0 0 1px rgb(0 0 0 / 9%);
    background: rgba(0,0,0,0.07);
}

.sbi-preview-ctn .sbi-post-item-view-comment,.sbi-post-meta-item{
	display: flex;
	justify-content: center;
	align-items: center;
}
.sbi-preview-ctn .sbi-post-meta-item{
	float: left;
    width: auto;
    display: block;
    list-style-type: none !important;
    margin: 0 10px 0 0 !important;
    padding: 0 !important;
    font-size: 11px;
    line-height: 16px !important;
    background: none !important;
}
.sbi-preview-ctn .sbi-post-meta-item-icon, .sbi-post-meta-item-icon svg{
	width: 16px;
	position: relative;
	float: left;
	z-index: 2;
}
.sbi-preview-ctn .sbi-post-meta-item-icon{
	margin-right: 5px;
}
.sbi-preview-ctn .sbi-post-meta-item-icon svg:nth-of-type(2){
	position: absolute;
	left: 0px;
	top: 0px;
	z-index: -1;
}
.sbi-preview-ctn .sbi-post-item-full-img{
	position: relative;
	float: left;
	width: 100%;
	height: auto;
}

/*Responsive Meta & Actions Links*/
.sbi-preview-ctn [data-feed-columns="3"] .sbi-post-item-meta-top,
.sbi-preview-ctn [data-feed-columns="4"] .sbi-post-item-meta-top,
.sbi-preview-ctn [data-feed-columns="5"] .sbi-post-item-meta-top,
.sbi-preview-ctn [data-feed-columns="6"] .sbi-post-item-meta-top{
	flex-direction: column-reverse;
    align-items: flex-start;
}

.sbi-preview-ctn [data-feed-columns="3"] .sbi-post-item-action-link,
.sbi-preview-ctn [data-feed-columns="4"] .sbi-post-item-action-link,
.sbi-preview-ctn [data-feed-columns="5"] .sbi-post-item-action-link,
.sbi-preview-ctn [data-feed-columns="6"] .sbi-post-item-action-link{
    margin-left: unset;
}

/*Light & Auto Icon Theme*/
.sbi-post-meta-item-icon-comment svg path,
.sbi-post-meta-item-icon-share svg path,
.sbi-post-meta-item-icon-like svg path{
	fill: rgba(0,0,0,0.4);
	-webkit-transition: all 0.2s ease;
	transition: all 0.2s ease;
}
.sbi-post-meta-item-icon-comment svg.sbi-svg-bg path,
.sbi-post-meta-item-icon-share svg.sbi-svg-bg path{
	    fill: rgba(255,255,255,0.7);
}
.sbi-post-meta-item-icon-like svg.sbi-svg-bg path{
	fill: url(#likeGrad);
}

.sbi-preview-ctn .sbi-post-item-meta:hover svg path{
	fill: rgba(0,0,0,0.5);
}

.sbi-preview-ctn .sbi-post-item-meta:hover .sbi-post-meta-item-icon-share svg.sbi-svg-bg path{
	fill: #fdf3d0;
}
.sbi-preview-ctn .sbi-post-item-meta:hover .sbi-post-meta-item-icon-comment svg.sbi-svg-bg path{
	fill: #fff;
}

.sbi-preview-ctn .sbi-post-item-meta:hover .sbi-post-meta-item-icon-like svg.sbi-svg-bg path{
	fill: url(#likeGradHover);
}

/*Dark Icon Theme*/
.sbi-post-item-meta[data-icon-theme="dark"] .sbi-post-meta-item-icon-comment svg path,
.sbi-post-item-meta[data-icon-theme="dark"] .sbi-post-meta-item-icon-share svg path,
.sbi-post-item-meta[data-icon-theme="dark"] .sbi-post-meta-item-icon-like svg path{
	fill: rgba(255,255,255,0.05);
	-webkit-transition: all 0.2s ease;
	transition: all 0.2s ease;
}
.sbi-post-item-meta[data-icon-theme="dark"] .sbi-post-meta-item-icon-comment svg.sbi-svg-bg path,
.sbi-post-item-meta[data-icon-theme="dark"] .sbi-post-meta-item-icon-share svg.sbi-svg-bg path{
	    fill: rgba(255,255,255,0.9);
}
.sbi-post-item-meta[data-icon-theme="dark"] .sbi-post-meta-item-icon-like svg.sbi-svg-bg path{
	fill: url(#likeGradDark);
}

.sbi-preview-ctn .sbi-post-item-meta[data-icon-theme="dark"]:hover svg path{
	fill: #fff;
}

.sbi-preview-ctn .sbi-post-item-meta[data-icon-theme="dark"]:hover .sbi-post-meta-item-icon-share svg.sbi-svg-bg path{
	fill: #fdf3d0;
}
.sbi-preview-ctn .sbi-post-item-meta[data-icon-theme="dark"]:hover .sbi-post-meta-item-icon-comment svg.sbi-svg-bg path{
	fill: #fff;
}

.sbi-preview-ctn .sbi-post-item-meta[data-icon-theme="dark"]:hover .sbi-post-meta-item-icon-like svg.sbi-svg-bg path{
	fill: url(#likeGradHover);
}
.sbi-preview-ctn .sbi-post-item-meta[data-icon-theme="dark"]:hover  svg.sbi-svg-bg{
	z-index: 3!important;
}

/*Post Meta comments*/
.sbi-post-item-meta-comments p,
.sbi-post-item-meta-comments .sbi-post-comment-item,
.sbi-post-item-meta-comments .sbi-comment-reply,
.sbi-post-item-meta-comments .sbi-post-comment-item p {
    width: 100%;
    min-width: 100%;
    font-size: 12px;
    margin: 0 0 1px 0;
    line-height: 17px;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
}


.sbi-post-item-comments-top{
	font-size: 12px;
	margin-bottom: 1px;
    padding: 10px 10px;
    background: rgba(0,0,0,0.05);
    border-radius: 4px;
    border-bottom-left-radius: 0px;
    border-bottom-right-radius: 0px;
}
.sbi-post-item-comments-top a:hover{
	text-decoration: underline;
}
.sbi-post-item-comments-icon{
	width: 16px;
	height: 16px;
	float: left;
	margin-right: 8px;
	display: flex;
	justify-content: center;
	align-items: center;
}
.sbi-post-item-comments-icon svg{
	width: 14px;
    margin-top: 4px;
}
.sbi-post-item-comments-list{
	background: rgba(0,0,0,0.05);
    border-radius: 4px;
    border-top-left-radius: 0px;
    border-top-right-radius: 0px;
}
.sbi-post-comment-item{
    padding: 7px 10px;
    font-size: 12px;
    display: flex;
 	align-items: flex-start;
}
.sbi-post-comment-item-avatar{
	width: 32px;
	height: 32px;
	float: left;
	margin-right: 10px;
	margin-top: 3px;
}
.sbi-post-comment-item-avatar img{
	width: 32px;
	height: 32px;
	float: left;
	border-radius: 50px;
}
.sbi-post-comment-item-author{
	font-weight: bold;
	margin-right: 6px;
	float: left;
}
.sbi-post-comment-item-author:hover{
	text-decoration: underline;
}
.sbi-post-comment-item-txt{
	font-size: 12px;
	line-height: 1.4;
    margin: 0px;
}
.sbi-post-comment-item-date{
    font-size: 11px;
    margin: 3px 0 0 0;
    display: block;
    font-style: italic;
    line-height: 1em;
    clear: both;
    float: left;
}


.sbi-post-item-media-album, .sbi-post-item-media-album a,.sbi-post-item-album-poster{
	float: left;
	width: 100%;
	position: relative;
	display: block;
}
.sbi-post-item-album-thumb{
	position: relative;
	display: inline-block;
	background-size: cover;
    background-position: center center;
    background-repeat: no-repeat;
}
.sbi-post-item-album-thumb-overlay{
	position: absolute;
	width: 100%;
	height: 100%;
	left: 0;
	top: 0;
	z-index: 3;
	background: rgba(0,0,0,0.4);
	display: flex;
	justify-content: center;
	align-items: center;
	color: #fff;
    font-size: 30px;
}

.sbi-post-item-album-thumbs{
	width: 100%;
	display: grid;
	margin-top: 5px;
	float: left;
}
.sbi-post-item-album-thumbs[data-length='1']{
	grid-template-columns: 100%;
}
.sbi-post-item-album-thumbs[data-length='2']{
	grid-template-columns: calc(50% - 3px) calc(50% - 3px);
	grid-column-gap: 5px;
}

.sbi-post-item-album-thumbs[data-length='3']{
	grid-template-columns: calc(33.33% - 3px) calc(33.33% - 3px) calc(33.33% - 3px);
	grid-column-gap: 5px;
}

.sbi-post-item-album-thumbs .sbi-post-item-album-thumb:first-of-type{
	grid-row: 1 / 1;
    grid-column: 1 / 1;
    padding-bottom: 100%;
}
.sbi-post-item-media-album{
	margin-bottom: 10px;
}

.sbi-preview-ctn .sbi-post-item-iframe-ctn[data-source="spotify"] iframe{
	width: 100%;
	height: 80px;
}

.sbi-preview-ctn .sbi-post-item-iframe-ctn[data-source="soundcloud"] iframe{
	width: 100%;
	height: 100px;
}

.sbi-preview-ctn .sbi-post-item-media-ctn{
	margin: 5px 0px;
}
.sbi-preview-ctn .sbi-post-item-video-ctn{
	position: relative;
}
.sbi-preview-ctn .sbi-post-item-iframe-ctn[data-source="video"] .sbi-post-item-iframe,
/*.sbi-preview-ctn .sbi-post-item-video-ctn,*/
.sbi-preview-ctn .sbi-videos-item-ctn .sbi-videos-item-source{
	width: 100%;
	float: left;
	clear: both;
	/*margin: 5px 0;*/
	position: relative;
	/*padding-bottom: 62%;*/
	padding-bottom: 56.25%;
}

.sbi-play-video-icon{
	position: absolute;
	width: 56px;
	height: 56px;
	background: rgba(0,0,0,0.7);
	color: #fff;
	left: 50%;
	top: 50%;
	border-radius: 8px;
	cursor: pointer;
	z-index: 1;
	-webkit-transform: translate(-50%, -50%);
    transform: translate(-50%, -50%);
}
.sbi-play-video-icon:before{
	content: '';
    position: absolute;
    border-left: 22px solid currentColor;
    border-top: 17px solid transparent;
    border-bottom: 17px solid transparent;
    left: 19px;
    top: 11px;
    border-radius: 4px;
}

.sbi-preview-ctn .sbi-post-item-iframe-ctn[data-source="video"] .sbi-post-item-iframe iframe,
.sbi-preview-ctn .sbi-post-item-video-ctn[data-lightbox="false"] iframe,
.sbi-preview-ctn .sbi-videos-item-ctn[data-lightbox="false"] .sbi-videos-item-source iframe,
.sbi-lightbox-video iframe,
.sbi-preview-ctn .sbi-videos-item-source iframe{
    position: absolute;
    width: 100%;
    height: 100%;
    z-index: 1;
    top: 0;
    left: 0;
    margin: 0;
    padding: 0;
    border: 0;
}
.sbi-lightbox-video {
	width: 950px;
	background: #212121;
	float: left;
	clear: both;
	padding-bottom: 56.25%;
	position: relative;
}
.sbi-preview-ctn .sbi-post-item-video-ctn iframe{
	width: 100%;
	float: left;
}

.sbi-preview-ctn .sbi-post-item-link-ctn{
    width: 100%;
	float: left;
    clear: both;
    padding: 8px;
    margin: 10px 0 5px 0;
    background: rgba(0,0,0,0.02);
    border: 1px solid rgba(0,0,0,0.07);
    box-sizing: border-box;
}
.sbi-preview-ctn .sbi-post-item-link-ctn[data-linkbox="on"]{
	border: 0px !important;
	background: none !important;
	padding: 0px !important;
}

.sbi-preview-ctn .sbi-post-item-link-a:hover{
	text-decoration: underline;
}
.sbi-preview-ctn .sbi-post-item-link-ctn img{
	width: 100%;
    max-width: 100%;
	height: auto;
	float: none;
}
.sbi-preview-ctn .sbi-post-item-link-ctn > a{
    width: 100%;
    max-width: 100%;
    margin-right: 0%;
    margin-bottom: 5px;
    text-align: center;
    float: left;
}
.sbi-preview-ctn .sbi-post-item-link-info{
	line-height: 1.4;
}

.sbi-preview-ctn .sbi-photos-item-ctn{
	margin: 0 0 20px 0;
    padding: 0;
    text-align: center;
    border: 1px solid rgba(0, 0, 0, .1);
}
[data-feed-layout="grid"] .sbi-photos-item-ctn,
[data-feed-layout="grid"] .sbi-albums-item-ctn{
	border-radius: unset!important;
}
.sbi-preview-ctn .sbi-singlemedia-item-info{
	display: flex;
	flex-direction: column;
	align-items: center;
}
.sbi-preview-ctn .sbi-singlemedia-item-info a:hover{
	text-decoration: underline;
}
.sbi-preview-ctn .sbi-singlemedia-item-info{
	width: 100%;
    height: 70px;
    float: left;
    clear: both;
    overflow: hidden;
}
.sbi-preview-ctn .sbi-singlemedia-item-info > h4{
    font-size: 14px!important;
    line-height: 1.2!important;
    margin: 10px 0 0 0!important;
    padding: 0!important;
}
.sbi-preview-ctn .sbi-singlemedia-item-info > p{
	font-size: 12px;
    color: gray;
    margin: 5px 0 0 0;
}
.sbi-preview-ctn .sbi-singlemedia-item{
    margin: 0 0 20px 0;
    text-align: center;
}

.sbi-preview-ctn .sbi-preview-likebox-ctn iframe{
    width: 100% !important;
    margin: 0 !important;
    border: 0 !important;
    padding: 0;
    position: relative;
    top: 0;
    left: 0;
}

/*Event Details*/
.sbi-preview-ctn .sbi-post-event-street{
	display: block
}


/*
	Featured & Single Album Holder
*/
.sbi-preview-ctn .sbi-single-holder-ctn{
	height: 600px;
	background: #fff;
	box-shadow: 0 0px 6px rgba(0,0,0,0.05);
	display: flex;
	justify-content: center;
	align-items: center;
    flex-direction: column;
}

.sbi-preview-ctn .sbi-single-holder-img{
	width: 130px;
	height: 130px;
	background: #F3F4F5;
	border: 1px dashed #D0D1D7;
	display: flex;
	justify-content: center;
	align-items: center;
	margin-bottom: 50px;
}
.sbi-preview-ctn .sbi-single-holder-content{
	text-align: center;
}
.sbi-preview-ctn .sbi-single-holder-content strong{
	font-size: 24px;
	margin-bottom: 10px;
}
.sbi-preview-ctn .sbi-single-holder-content p{
	font-size: 16px;
    line-height: 1.7em;
    color: #8C8F9A;
    margin: 0px;
    margin-top: 8px;
}


/*Post Element Overlay */
.sbi-preview-ctn .sbi-post-overlay{
	position: absolute;
	width: 100%;
	height: 100%;
	cursor: pointer;
	left: 0;
	top: 0;
	background: rgba(0,0,0,0.4);
	z-index: 9;
	opacity: 0;
	visibility: hidden;
	-webkit-transition: all .2s ease-in-out;
	transition: all .2s ease-in-out;
}
.sbi-preview-ctn .sbi-post-item-media-wrap:hover .sbi-post-overlay,
.sbi-preview-ctn .sbi-post-item-iframe-ctn:hover .sbi-post-overlay,
.sbi-preview-ctn .sbi-post-item-video-ctn:hover .sbi-post-overlay,
.sbi-preview-ctn .sbi-post-overlay-parent:hover .sbi-post-overlay,
.sbi-preview-ctn .sbi-videos-item-source:hover .sbi-post-overlay{
	opacity: 1;
	visibility: visible;
}

/*Lightbox */
.sbi-lightbox-ctn{
	width: 100%;
	height: 100%;
	position: absolute;
	left: 0;
	top: 2px;
	background: rgba(0,0,0,0.9);
	z-index: 9;
	display: none;
	justify-content: center;
    align-items: flex-start;
    padding-top: 100px;
}
.sbi-lightbox-ctn[data-visibility="shown"]{
	display: flex;
}
.sbi-lightbox-closer{
	position: absolute;
	left: 0;
	top: 0;
	z-index: 1;
	height: 100%;
	width: 100%;
}
.sbi-lightbox-image img{
	float: left;
	max-width:780px;
}
.sbi-lightbox-content{
	max-width: 100%;
	position: relative;
	z-index: 2;
}
.sbi-lightbox-content-insider{
	position: relative;
	padding-right: 300px;
	display: flex;
}
[data-comments="false"] .sbi-lightbox-content-insider{
	padding-right: 0px;
}
[data-comments="false"] .sbi-lightbox-content{
	max-width: 100%;
	position: relative;
	z-index: 2;
}

.sbi-lightbox-sidebar{
	width: 300px;
	box-sizing: border-box;
	position: absolute;
	right: 0px;
	top: 0px;
	background: #fff;
	height: 100%;
	padding: 15px;
	overflow: auto;
}
.sbi-lightbox-caption{
    float: left;
    box-sizing: border-box;
    padding: 10px;
    background: #fff;
    position: relative;
    position: absolute;
}
.sbi-lightbox-caption .sbi-lightbox-cls{
	top:3px;
}

.sbi-lightbox-sidebar .sbi-post-item-meta-top{
	display: none!important;
}
.sbi-lightbox-sidebar .sbi-post-item-meta-comments{
	display: block!important;
}
.sbi-lightbox-sidebar .sbi-post-item-meta-wrap{
	margin-top: 12px;
}

.sbi-lightbox-cls{
	position: absolute;
    width: 15px;
    height: 15px;
    top: 8px;
    right: 6px;
    cursor: pointer;
    z-index: 8;
}
.sbi-lightbox-cls:before,
.sbi-lightbox-cls:after{
   	content: '';
    position: absolute;
    height: 3px;
    width: 13px;
    left: 1px;
    top: 6px;
    background: currentColor;
    -webkit-transform: rotate(45deg);
    transform: rotate(45deg);
}
.sbi-lightbox-cls:after{
	-webkit-transform: rotate(-45deg);
	transform: rotate(-45deg);
}

.sbi-lightbox-nav{
	position: absolute;
	width: 40px;
	height: 100%;
	display: flex;
	justify-content: center;
	align-items: center;
	left: -60px;
	cursor: pointer;
}
.sbi-lightbox-next{
	left: unset;
	right:-60px;
}
.sbi-lightbox-nav-icon{
	width: 24px;
	height: 24px;
	border-bottom: 4px solid #fff;
	border-right: 4px solid #fff;
	-webkit-transform: rotate(135deg);
	transform: rotate(135deg);
	opacity: .7;
	-webkit-transition: all .2s ease-in-out;
	transition: all .2s ease-in-out;
}
.sbi-lightbox-next .sbi-lightbox-nav-icon{
	-webkit-transform: rotate(-45deg);
	transform: rotate(-45deg);
}
.sbi-lightbox-nav:hover .sbi-lightbox-nav-icon{
	opacity: 1;
}
.sbi-lightbox-thumbs{
    background: #222;
}
.sbi-lightbox-thumb-item{
	float: left;
	width: 60px;
	height: 60px;
	cursor: pointer;
	box-sizing: border-box;
	-webkit-transition: all .2s ease-in-out;
	transition: all .2s ease-in-out;
	background-size: cover;
	background-position: center center;
	margin: 4px;
}
.sbi-lightbox-thumb-item:hover, .sbi-lightbox-thumb-item[data-active="true"]{
	box-shadow: inset 0 0 0 4px rgb(255 255 255 / 70%);
    border: 1px solid #bbb;
}

.sbi-lightbox-sidebar .sbi-post-item-text{
	line-height: 1.3em
}
/*
	COLOR SCHEMES
*/
/*LIGHT*/
[data-preview-colorscheme="light"] .sbi-post-item-content{
	color: rgba(0,0,0,.8);
}

[data-preview-colorscheme="light"] .sbi-singlemedia-item-info p{
	color: rgba(0,0,0,.7);
}

[data-preview-colorscheme="light"] .sbi-post-item-text a{
	color: #141B38;
	text-decoration: underline;
}
[data-preview-colorscheme="light"] .sbi-post-item-text-expand,
[data-preview-colorscheme="light"] .sbi-post-item-action-link{
	color: #141B38;
}

[data-preview-colorscheme="light"] .sbi-post-item-date{
	color: rgba(0,0,0,0.5)
}

/*DARK*/
[data-preview-colorscheme="dark"] .sbi-post-item-content{
	color: rgba(255,255,255,.75);
	background-color: rgba(0,0,0,.85)
}
[data-preview-colorscheme="dark"] .sbi-singlemedia-item-info p{
	color: rgba(255,255,255,.75);
}
[data-preview-colorscheme="dark"] .sbi-post-item-text a{
	color: #fff;
	text-decoration: underline;
}
[data-preview-colorscheme="dark"] .sbi-post-item-text-expand,
[data-preview-colorscheme="dark"] .sbi-post-item-action-link{
	color: #fff;
}

[data-preview-colorscheme="dark"] .sbi-post-item-date{
	color: rgba(255,255,255,.5);
}

.sbi-videos-item-ctn,.sbi-albums-item-ctn,.sbi-photos-item-ctn{
	border-bottom: 0px!important;
}

.sbi-preview-posts-grid .sbi-videos-item-ctn .sbi-post-item-content,
.sbi-preview-posts-grid .sbi-videos-item-ctn .sbi-post-overlay-parent,
.sbi-preview-posts-grid .sbi-videos-item-ctn .sbi-post-overlay-parent > .sbi-fb-fs{
	position: absolute;
	width: 100%;
	height: 100%;
}
.sbi-preview-posts-grid .sbi-videos-item-ctn .sbi-post-overlay-parent{
	background-size: cover!important;
	background-position: center center!important;
}
.sbi-preview-posts-grid .sbi-videos-item-ctn .sbi-post-overlay-parent{
	height: calc(100% - 70px)
}
.sbi-preview-posts-grid .sbi-singlemedia-item-info{
	position: absolute;
	bottom: 0px;
}
/*
	Carousel
*/
.owl-stage{
	position: relative;
}
.owl-stage-outer{
	overflow: hidden;
}
.owl-item{
	float: left;
}
.owl-dots{
	width: 100%;
    height: 35px;
    text-align: center;
    margin-top: 10px;
    margin-bottom: 30px;
    display: flex;
    justify-content: center;
    align-items: center;
}
.owl-dot{
    display: inline-block;
    width: 12px;
    height: 12px;
    margin: 5px 7px;
    filter: Alpha(Opacity=25);
    opacity: 0.25;
    padding:0px !important;
    border-radius: 20px;
    background: #000;
    box-shadow: 0 0 1px 0 rgb(255 255 255 / 80%);
    outline: none;
    border: 0px;
    cursor: pointer;
}
.owl-dot.active,.owl-dot:hover{
	opacity: .5;
}

.owl-prev,
.owl-next{
	width: 40px;
	height: 40px;
	border-radius: 50px;
	padding: 0px;
	outline: none;
    border: 0px;
    cursor: pointer;
    font-size: 22px;
    display: flex;
    justify-content: center;
    align-items: center;
    background: #000;
    color: #fff;
}
.owl-dots .owl-prev,
.owl-dots .owl-next{
	display: inline-block;
}

.owl-prev span,
.owl-next span{
	margin-top: -3px;
}
.sbi-carousel[data-navigation="none"] .owl-nav{
	display: none;
}

.sbi-carousel[data-navigation="onhover"] .owl-prev,
.sbi-carousel[data-navigation="onhover"] .owl-next{
	opacity: 0;
	position: absolute;
	top: 50%;
	margin-top: -20px;
	left: 0px;
	-webkit-transition: all .2s ease-in-out;
	transition: all .2s ease-in-out;
}
.sbi-carousel[data-navigation="onhover"] .owl-next{
	left: unset;
	right: 0px;
}
.sbi-carousel[data-navigation="below"] .owl-prev,
.sbi-carousel[data-navigation="below"] .owl-next,
.sbi-carousel:hover .owl-prev,
.sbi-carousel:hover .owl-next{
	opacity: .5;
}

.sbi-carousel .owl-prev:hover,
.sbi-carousel .owl-next:hover{
	opacity: 1;
}

.sbi-carousel .cff_carousel-more{
    display: block;
    position: relative;
    clear: both;
    width: 100%;
    text-align: center;
    border-radius: 0;
    border-top: 1px solid rgba(0,0,0,0.5);

    background: #000;
    filter: Alpha(Opacity=25);
    opacity: 0.25;
    color: #fff;
    padding: 0;
    border: none;
    margin-top: 0;
    height: auto;
}
.sbi-carousel .cff_carousel-more:hover,
.sbi-carousel .cff_carousel-more:focus{
    filter: Alpha(Opacity=50);
    opacity: 0.5;
}
.sbi-carousel .cff_carousel-more .fa-caret-down {
    font-size: 50px;
    line-height: 1;
    padding: 0;
    position: relative;
    top: -19px;
}

/*Reviews Ratings*/
.sbi-rating {
    float: none;
    display: inline-block;
    margin: 0 0 5px 0 !important;
    padding: 3px 6px;
    height: auto;
    background: #5890FF;
    color: #fff;
    white-space: nowrap;
    border-radius: 100px;
    line-height: 1em;
}
span.sbi-rating-num,  span.sbi-star {
    margin: 0 2px 0 0 !important;
    padding: 0;
    height: auto;
    display: inline-block;
    vertical-align: top;
    line-height: 1;
    color: #fff;
    font-weight: normal;
    font-size: 12px!important;
}
span.sbi-rating-num{
   margin: 0 2px 0 4px !important;
}