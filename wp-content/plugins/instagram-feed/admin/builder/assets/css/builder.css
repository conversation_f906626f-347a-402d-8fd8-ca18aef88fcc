body{
    background: #red;
    --cl-orange: #FE544F;
    --cl-dark:#2C324C;
    --error-red: #D72C2C;
    --customizer-blue: #0096CC;
    --dark-blue: #2A65DB;
}
.sb-tr-1{-webkit-transition: all .1s ease-in-out; transition: all .1s ease-in-out;}
.sb-tr-2{-webkit-transition: all .2s ease-in-out; transition: all .2s ease-in-out;}
.sb-tr-3{-webkit-transition: all .3s ease-in-out; transition: all .3s ease-in-out;}
.sb-tr-4{-webkit-transition: all .4s ease-in-out; transition: all .4s ease-in-out;}
.sb-tr-5{-webkit-transition: all .5s ease-in-out; transition: all .5s ease-in-out;}
.sb-tr-6{-webkit-transition: all .6s ease-in-out; transition: all .6s ease-in-out;}
.sb-tr-7{-webkit-transition: all .7s ease-in-out; transition: all .7s ease-in-out;}
.sb-tr-8{-webkit-transition: all .8s ease-in-out; transition: all .8s ease-in-out;}
.sb-tr-9{-webkit-transition: all .9s ease-in-out; transition: all .9s ease-in-out;}
.sb-tr-10{-webkit-transition: all 1s ease-in-out; transition: all 1s ease-in-out;}
.sbi-bg-1{
    background: #2A65DB;
}

/****
Buttons
 */
.sb-button {
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    border-radius: 2px;
    cursor: pointer;
    outline: none;
    box-shadow: none;
    border: none;
    -webkit-transition: all .15s ease-in-out;
    transition: all .15s ease-in-out;
}
.sb-button-standard{
    position: relative;
    font-style: normal;
    font-weight: 600;
    font-size: 14px;
    padding: 10px 20px 10px 39px;
    line-height: 16px;
    border: unset;
    cursor: pointer;
}
.sb-button-standard svg {
    width: 16px;
    height: 16px;
    position: absolute;
    left: 13px;
    right: auto;
    top: 10px;
    bottom: auto;
}
.sb-button-small.sb-button-left-icon {
    padding-left: 32px;
}
.sb-button-small.sb-button-right-icon {
    padding-right: 32px;
}
.sb-button-small.sb-button-left-icon svg {
    position: absolute;
    left: 13px;
}
.sb-button-small.sb-button-right-icon svg {
    position: absolute;
    right: 13px;
}
.sb-button-standard.sb-button-right-icon {
    padding: 10px 39px 10px 20px;
}
.sb-button-standard.sb-button-right-icon svg {
    right: 13px;
    left: auto;
}

.sb-button-small {
    position: relative;
    font-style: normal;
    font-weight: 600;
    font-size: 12px;
    padding: 6px 12px;
    line-height: 160%;
}

.sb-button-no-border {
    border-radius: 0 !important;
    border: none !important;
}

/*orange*/
.sbi-btn-orange{
    background: #FE544F!important;
    color: #fff!important;
}
.sbi-btn-orange:hover{
    background: #EC352F!important;
    color: #fff!important;
}
.sbi-btn-orange:focus{
    background: #BC120E!important;
    color: #fff!important;
}

/*red*/
.sbi-btn-red{
    background: #D72C2C!important;
    color: #fff!important;
}
.sbi-btn-red:hover{
    background: #DF5757!important;
    color: #fff!important;
}
.sbi-btn-red:focus{
    background: #841919!important;
    color: #fff!important;
}

/*red*/
.sbi-btn-blue{
    background: #0068A0!important;
    color: #fff!important;
}
.sbi-btn-blue:hover{
    background: #0096CC!important;
    color: #fff!important;
}
.sbi-btn-blue:focus{
    background: #004D77!important;
    color: #fff!important;
}

/*grey*/
.sbi-btn-grey{
    background: #F3F4F5!important;
    color: #141B38!important;
    border: 1px solid #D0D1D7!important;
}
.sbi-btn-grey:hover{
    background: #fff!important;
    color: #141B38!important;
    border: 1px solid #DCDDE1!important;
}
.sbi-btn-grey:focus{
    background: #E8E8EB!important;
    color: #141B38!important;
    border: 1px solid #D0D1D7!important;
}

/*dark*/
.sbi-btn-dark{
    background: #2C324C!important;
    color: #fff!important;
}
.sbi-btn-dark:hover{
    background: #434960!important;
    color: #fff!important;
}
.sbi-btn-dark:focus{
    background: #141B38!important;
    color: #fff!important;
}

.sb-dark-hover:hover svg,
.sb-dark-hover:hover path{
    fill: #141B38;
}

/*disabled*/
.sbi-btn-orange[data-active="false"],
.sbi-btn-blue[data-active="false"],
.sbi-btn-red[data-active="false"],
.sbi-btn-grey[data-active="false"],
.sbi-btn-dark[data-active="false"]{
    background: #E8E8EB !important;
    color: #8C8F9A !important;
}

/* Text */
#sb_instagram{
    overflow-x: hidden;
}
#sbi-builder-app h1:not(#sb_instagram h1) {
    font-style: normal;
    font-weight: 600;
    font-size: 36px;
    line-height: 125%;
    color: #141B38;
    margin: 0;
    letter-spacing: 0;
}
#sbi-builder-app h2:not(#sb_instagram h2) {
    font-style: normal;
    font-weight: 600;
    font-size: 32px;
    line-height: 125%;
    margin: 0;
    letter-spacing: 0;
}
#sbi-builder-app h3:not(#sb_instagram h3):not(.sb_instagram_header h3) {
    font-style: normal;
    font-weight: 600;
    font-size: 24px;
    line-height: 120%;
    margin: 0 0 4px 0;
    letter-spacing: 0;
}

#sbi-builder-app h4:not(#sb_instagram h5) {
    font-style: normal;
    font-weight: 600;
    font-size: 18px;
    line-height: 140%;
    margin: 0 0 4px 0;
    letter-spacing: 0;
}
.sbi-fb-feedtypes-pp-ctn h4 {
    font-size: 24px !important;
    margin-bottom: 28px !important;
}

#sbi-builder-app .sb-small-p,
#sbi-builder-app .sb-standard-p{
    font-style: normal;
    font-weight: normal;
    font-size: 14px;
    line-height: 160%;
    color: #8C8F9A;
    margin: 0;
}
#sbi-builder-app .sb-standard-p{
    font-size: 16px;
    color: #141B38;
}
#sbi-builder-app .sbi-fb-source-inp::placeholder {
    color: #8C8F9A;
    font-size: 14px;
    font-weight: normal;
}
#sbi-builder-app .sb-bold {
    font-weight: 600;
}
#sbi-builder-app .sb-dark-text {
    color: #141B38;
}
#sbi-builder-app .sbi-btn-orange .sb-small-p,
#sbi-builder-app .sbi-btn-blue .sb-small-p,
#sbi-builder-app .sbi-btn-red .sb-small-p,
#sbi-builder-app .sbi-btn-dark .sb-small-p{
    color: #fff;
}

#sbi-builder-app .sb-caption {
    font-style: normal;
    font-weight: normal;
    font-size: 13px;
    line-height: 150%;
    color: #141B38;
}
#sbi-builder-app .sb-caption.sb-caption-lighter {
    color: #5F6368;
}

#sbi-builder-app .sb-small {
    font-style: normal;
    font-weight: bold;
    font-size: 10px;
    line-height: 160%;
    letter-spacing: 0.02em;
    text-transform: uppercase;
    color: #141B38;
}

#sbi-builder-app .sb-lighter {
    color: #434960;
}
#sbi-builder-app .sb-lightest {
    color: #74777D;
}
#sbi-builder-app .sb-small-text {
    font-size: 12px;
}
/* Positioning */
.sb-icon-label {
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    align-items: center;
}
/* Misc Stylings */
.sb-flex-center {
    display: -ms-flexbox;
    display: -webkit-flex;
    display: flex;

    -ms-flex-align: center;
    -webkit-align-items: center;
    -webkit-box-align: center;

    align-items: center;
}
.sb-box-shadow {
    box-shadow: 0px 1px 2px rgba(0, 0, 0, 0.05), 0px 4px 5px rgba(0, 0, 0, 0.05);
}
#sbi-builder-app .sb-icon-small svg {
    height: 10px;
}

/*
Social Links
*/
.sbi-cl-facebook, .sbi-clhv-facebook:hover{
    color: #006BFA!important;
}
.sbi-bg-facebook, .sbi-bghv-facebook:hover{
    background: #006BFA!important;
}

.sbi-cl-instagram, .sbi-clhv-instagram:hover{
    color: #BA03A7!important;
}
.sbi-bg-instagram, .sbi-bghv-instagram:hover{
    background: #BA03A7!important;
}

.sbi-cl-twitter, .sbi-clhv-twitter:hover{
    color: #1B90EF!important;
}
.sbi-bg-twitter, .sbi-bghv-twitter:hover{
    background: #1B90EF!important;
}

.sbi-cl-youtube, .sbi-clhv-youtube:hover{
    color: #EB2121!important;
}
.sbi-bg-youtube, .sbi-bghv-youtube:hover{
    background: #EB2121!important;
}

.sbi-cl-linkedin, .sbi-clhv-linkedin:hover{
    color: #007bb6!important;
}
.sbi-bg-linkedin, .sbi-bghv-linkedin:hover{
    background: #007bb6!important;
}

.sbi-cl-mail, .sbi-clhv-mail:hover{
    color: #666!important;
}
.sbi-bg-mail, .sbi-bghv-mail:hover{
    background: #666!important;
}

#sbi-builder-app{
    font-family: -apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,Oxygen-Sans,Ubuntu,Cantarell,"Helvetica Neue",sans-serif;;
    visibility: hidden;
    opacity: 0;
}
#sbi-builder-app[data-app-loaded="true"]{
    visibility: visible;
    opacity: 1;
}

.sbi-builder-app a, .sbi-builder-app a:focus{
    text-decoration: none;
    /*color: inherit;*/
    outline: none;
}
.sbi-fb-btn{
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    padding: 9px 38px;
    font-style: normal;
    font-weight: 500;
    font-size: 14px;
    line-height: 17px;
    border-radius: 2px;
    cursor: pointer;
    color: #fff;
    outline: none;
    box-shadow: none;
    border: none;;
}
.sbi-fb-btn:focus{
    box-shadow: none;
}
.sbi-fb-fs, .sb-fs{
    width: 100%;
    position: relative;
    float: left;
    box-sizing: border-box;
}
.sbi-fs-a{
    width: 100%;
    height: 100%;
    display: block;
    position: absolute;
    left: 0;
    top: 0;
    z-index: 1;
}
.sb-btn:focus{
    outline: none;
}
#wpcontent,
.instagram-feed_page_sbi-feed-builder #wpcontent{
    padding-left: 0px!important;
}
#wpfooter{
    display: none;
}
#wpbody-content{
    padding-bottom: 0px;
}
#wpbody{
    padding-left: 0px!important;
}
.sbi-fb-wrapper{
    max-width: 92%;
    position: relative;
    margin: auto;
    color: #141B38;
}
.sbi-fb-fs-boss{
    position: fixed;
    height: 100vh;
    width: 100%;
    left: 0;
    top: 0;
    bottom: 0;
    right: 0;
    background: rgba(0,0,0,.4);
    z-index: 9989;
}


/*
Header
*/
.sbi-fb-create-ctn{
    margin-top: 104px;
    padding: 0 54px;
    box-sizing: border-box;
    width: 100%;
}
.sbi-fb-header{
    height: 64px;
    position: absolute;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    background: #fff;
    padding: 0px 52px;
    z-index: 99;
}
.sbi-fb-header-left {
    display: flex;
}
.sbi-fb-header-left .sb-social-wall-link-wrap {
    margin-left: 30px;
}
.sbi-fb-hd-logo {
    display: flex;
    vertical-align: middle;
    align-items: center;
    gap: 5px;
}
.sbi-fb-hd-logo .sb-logo-letters-wrap {
    transform: translate(0px, -2px);
}
.sbi-fb-hd-logo .breadcrumb-title {
    font-size: 14px;
    font-weight: 400;
    line-height: 22px;
    letter-spacing: 0em;
    margin-left: 4px;
}
.sbi-csz-header.sbi-fb-header{
    position: fixed!important;
    padding: 0 20px;
}
.sbi-csz-header-insider{
    width: calc(100% - 160px);
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    height: 52px;
}
body.jetpack-masterbar .sbi-csz-header-insider {
  width: calc(100% - 272px);
}
@media all and (max-width: 960px) {
    .sbi-csz-header-insider{
        width: calc(100% - 36px);
    }
    .sb-customizer-ctn .sb-customizer-sidebar{
        left: 36px;
    }
  body.jetpack-masterbar .sb-customizer-sidebar {
    left: 36px;
  }
  body.jetpack-masterbar .sbi-csz-header-insider {
    width: calc(100% - 36px);
  }
}
.sbi-fb-extpp-lite-btn-texts {
    display: flex;
    flex-direction: column;
}
.sbi-fb-extpp-lite-btn .sbi-fb-extpp-lite-btn-discount-applied {
    font-size: 12px;
    font-weight: normal;
}
.sbi-fb-hd-btn{
    cursor: pointer;
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    position: relative;
    border: 1px solid;
    -webkit-transition: all .15s ease-in-out;
    transition: all .15s ease-in-out;
}
.sbi-fb-hd-btn i{
    margin: 0px 5px;
}
.sbi-fb-ft-action .sbi-fb-hd-btn {
    -webkit-transition: background .15s ease-in-out;
    transition: background .15s ease-in-out;
}
.sbi-fb-full-wrapper {
    padding: 0 53px;
    padding-top: 82px;
}
.sbi-csz-hd-actions{
    display: flex;
    justify-content: center;
    align-items: center;
}
.sbi-csz-hd-actions > button{
    margin-left: 10px;
}

.sbi-csz-btn-embd{
    color: #fff;
    background: var(--cl-dark);
    border-color: var(--cl-dark);
}
.sbi-csz-btn-save{
    color: #fff;
    background: var(--cl-orange);
    border-color: var(--cl-orange);
}
.sbi-csz-hd-name{
    display: flex;
    justify-content: center;
    align-items: center;
    height: 45px;
}
.sbi-csz-hd-name strong{
    font-size: 18px;
}
.sbi-csz-hd-name[data-edit="true"] strong{
    display: none;
}
.sbi-csz-hd-name input[type="text"]{
    border-radius: 0px;
    border: 0px;
    background: #CCE7FF;
    outline: none;
    font-size: 18px;
    font-weight: 700;
    display: none;
}
.sbi-csz-hd-name[data-edit="true"] input[type="text"]{
    display: block;
}
.sbi-csz-hd-name input[type="text"]:focus{
    outline: none!important;
    border: 0px!important;
    box-shadow: none!important;
}


.sbi-csz-name-ed-btn{
    width: 22px;
    height: 22px;
    cursor: pointer;
    margin: 0 10px;
    background: #E8E8EB;
    border: 1px solid #E8E8EB;
    outline: none;
}
.sbi-csz-name-ed-btn:focus,
.sbi-csz-name-ed-btn:hover{
    outline: none;
    background-color: #fff;
}
.sbi-csz-name-ed-btn svg{
    width: 11px;
    fill: #141B38;
    float: left;
    margin-left: -1px;
}


/*
Welcome Screen Empty State
*/
.sbi-fb-wlcm-header{
    display: flex;
    align-items: center;
    margin-bottom: 28px;
    margin-top: 23px;
}
#sbi-fb-full-wrapper .sbi-fb-wlcm-header {
    margin-bottom: 34px;
}

.sbi-fb-wlcm-header h3,
.sbi-fb-create-ctn h3{
    font-weight: 600;
    font-size: 32px;
    line-height: 40px;
    padding: 0;
    margin: 0;
    float: left;
}
.sbi-fb-btn-new{
    background: var(--cl-orange);
    position: relative;
    float: left;
    margin-left: 20px;
    font-size: 12px;
    padding: 8px 12px 8px 32px;
    font-weight: 700;
}
.sbi-fb-btn-new svg{
    width: 10px;
    height: 10px;
    position: absolute;
    left: 12px;
}

.sbi-fb-inf-cnt{
    position: relative;
    background: #fff;
    padding: 27px;
    box-shadow: 0px 1px 2px rgba(0, 0, 0, 0.05), 0px 4px 5px rgba(0, 0, 0, 0.05);
    border-radius: 2px;
    max-width: 100%;
}
.sbi-fb-inf-num{
    width: 30px;
    height: 30px;
    position: relative;
    float: left;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 12px;
    font-weight: 600;
    color: #141B38;
    margin-right: 20px;

}
.sbi-fb-inf-num span{
    z-index: 1;
}
.sbi-fb-inf-num:before{
    content: '';
    position: absolute;
    width: 100%;
    height: 100%;
    background: #D8DADD;
    border-radius: 50%;
}
.sbi-fb-inf-txt{
    float: left;
}
/*.sbi-fb-inf-txt strong{
	display: block;
	font-size: 20px;
	line-height: 1em;
	font-weight: bold;
	margin-bottom: 8px;
}*/
.sbi-fb-inf-txt span{
    display: block;
    font-size: 16px;
    color: #5F6368;
    line-height: 1.5em;
}
.sbi-fb-wlcm-inf-1 {
    padding-left: 297px;
}
.sbi-fb-wlcm-inf-1 .sbi-fb-inf-svg{
    position: absolute;
    left: 191px;
    top: 32px;
    /*float: left;
    margin-top: 30px;
    margin-left: 230px;
    position: relative;
    margin-right: 25px;*/
}
.sbi-fb-wlcm-inf-1 .sbi-fb-inf-svg svg{
    margin-top: -45px;
}
.sb-head {
    position: absolute;
    top: -2px;
    left:-4px;
}
.sbi-fb-wlcm-inf-1 .sbi-fb-inf-cnt{
    float: left;
    width: 523px;
    margin-bottom: 24px;
}
.sbi-fb-wlcm-inf-2{
    display: flex;
    justify-content: space-between;
    padding-left: 152px;
}
.sbi-fb-wlcm-inf-2 .sbi-fb-inf-cnt{
    float: left;
    width: 590px;
    margin-bottom: 29px;
    height: 67px;
    display: flex;
    align-items: center;
}
.sbi-fb-wlcm-inf-2 .sbi-fb-inf-img{
    position: absolute;
    right: 15px;
    top: -15px;
}

.sbi-fb-wlcm-inf-3{
    display: flex;
    justify-content: flex-start;
    align-items: center;
    padding-left: 297px;
}
.sbi-fb-wlcm-inf-3 .sbi-fb-inf-cnt{
    float: left;
    width: 620px;
    height: 80px;
    display: flex;
    justify-content: center;
    align-items: center;
}
.sbi-fb-wlcm-inf-3 .sbi-fb-inf-img{
    float: left;
    left: 10px;
    top: -10px;
    position: absolute;
}
.sbi-fb-wlcm-inf-2 .sbi-fb-inf-num, .sbi-fb-wlcm-inf-3 .sbi-fb-inf-num{
    margin-top: -20px;
}
.sbi-fb-types-ctn, .sbi-fb-slctsrc-ctn, .sbi-fb-section-wh{
    background: #fff;
    border: 1px solid #E7E7E9;
}
.sbi-fb-wrapper h3, .sbi-fb-section-wh h3{
    font-size: 32px;
    line-height: 39px;
    font-weight: 600;
}
.sbi-fb-create-ctn h3{
    margin-bottom: 30px;
}
.sbi-fb-types h4, .sbi-fb-section-wh h4 {
    font-size:20px;
    line-height: 24px;
    font-weight: 600;
}
#sbi-builder-app .sbi-fb-create-ctn h4,
#sbi-builder-app .sbi-fb-feedtypes-pp-ctn  h4{
    margin-bottom: 1px;
}
#sbi-builder-app .sbi-fb-feedtypes-pp-ctn .sbi-fb-types {
    margin-top: 20px;
}
#sbi-builder-app .sbi-fb-adv-types .sbi-adv-types-heading{
    font-style: normal;
    font-weight: bold;
    font-size: 12px;
    line-height: 160%;
    letter-spacing: 0.05em;
    text-transform: uppercase;
    color: #8C8F9A;
}
.sbi-fb-types-desc{
    font-size: 14px;
    color: #434960;
    margin-bottom: 33px;
    display: block;
}
.sbi-fb-types, .sbi-fb-adv-types{
    padding: 22px 35px 0;
}
.sbi-fb-adv-types {
    padding-top: 0;
}
#sbi-builder-app .sbi-fb-type-el-info a,
.sbi-business-required{
    color: #0068A0;
    font-size: 12px;
    display: flex;
    align-self: center;
}
#sbi-builder-app .sbi-fb-type-el-info a span{
    margin-right: 7px;
}
#sbi-builder-app .sbi-fb-type-el-info a svg,
.sbi-business-required svg{
    height: 16px;
    line-height: 12px;
    vertical-align: top;
    margin-top: 1px;
}
.sbi-fb-types-list{
    display: grid;
    grid-template-columns: 24.25% 24.25% 24.25% 25.25%;
    grid-column-gap: 1%;
    margin-bottom: 31px;
}

/*
.sbi-fb-adv-types .sbi-fb-types-list{
	grid-template-columns: 24.25% 24.25% 24.25% 24.25%;
}
*/
.sbi-fb-heading {
    margin-bottom: 24px;
    width: 100%;
    float: left;
}
.sbi-fb-heading h1{
    float: left;
}
.sbi-fb-heading .sbi-fb-btn{
    float: right!important;
}

.sbi-fb-adv-types .sbi-fb-types-list{
    margin-bottom: 71px;
}
.sbi-fb-type-el{
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    padding: 0;
    position: relative;
    background: #fff;
    border: 1px solid #F1F1F1;
    border-radius: 3px;
    cursor: pointer;
    box-sizing: border-box;
}
.sbi-fb-type-el:not([data-type="socialwall"]):hover,
.sbi-fb-adv-types .sbi-fb-type-el:not([data-type="socialwall"]):hover{
    border: 1px solid #F6966B;
}


.sbi-fb-adv-types .sbi-fb-type-el{
    border: 1px solid #FFDBBA;
}
.sb-control-feedtype-ctn .sbi-fb-type-el{
    border: 2px solid #E8E8EB;
    margin-bottom: 7px;
}
.sb-control-feedtype-ctn .sbi-fb-type-el-info{
	margin-top: 14px
}
.sbi-fb-type-el[data-active="true"],
.sbi-fb-adv-types .sbi-fb-type-el[data-active="true"]{
    border: 2px solid var(--cl-orange)!important;
}
.sbi-fb-type-el[data-active="true"]:not([data-type="socialwall"]):after{
    content: '';
    position: absolute;
    width: 10px;
    height: 4px;
    border-left: 2px solid #fff;
    border-bottom: 2px solid #fff;
    z-index: 3;
    right: 5px;
    top: 7px;
    -webkit-transform: rotate(-45deg);
    transform: rotate(-45deg);
}
.sbi-fb-type-el[data-active="true"]:before{
    content: '';
    position: absolute;
    width: 24px;
    height: 24px;
    right: 0px;
    top: 0px;
    z-index: 2;
    background: #DCDDE1;
}
.sbi-fb-type-el[data-active="true"]:before{
    background: var(--cl-orange)
}
.sbi-fb-type-el[data-type="socialwall"]:before{
    display: none;
}


.sbi-fb-type-el-img{
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    background: #E2F5FF;
    border-bottom: 1px solid #fff;
}
.sbi-fb-type-el[data-active="true"] .sbi-fb-type-el-img{
    background-color: #F1F1F1;
    border-bottom: 2px solid #fff;
}
.sbi-fb-type-el-img svg {
    max-width: 100%;
}
.sbi-fb-adv-types .sbi-fb-type-el-img{
    background: #FCF4EF;
}
.sbi-fb-type-el-info{
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: center;
    text-align: center;
    flex: 1 1 auto;
    padding: 0 18px 13px;
}
.sbi-fb-type-el-link{
    font-size: 13px;
    font-weight: 400;
    margin-bottom: 5px;
}

.sb-control-feedtype-ctn .sbi-fb-type-el-info{
	padding: 15px 70px 25px;
}
.sb-control-feedtype-ctn .sbi-fb-type-el-info strong{
	font-size: 14px;
}
#sbi-builder-app .sbi-fb-type-el p {
    margin: 11px 0 4px;
}
#sbi-builder-app .sbi-fb-type-el[data-active="true"] .sbi-fb-type-el-info {
    padding-bottom: 12px;
}
#sbi-builder-app .sbi-fb-type-el p svg {
    margin-left: 1px;
    vertical-align: middle;
}
/*.sbi-fb-type-el-info strong{
	font-weight: 600;
	font-size: 16px;
	line-height: 19px;
	margin-bottom: 10px;
	text-transform: capitalize;
	display: flex;
	justify-content: center;
	align-items: center;
}
.sbi-fb-type-el-info strong i{
	color: #ed8000;
	margin-left: 5px;
	font-size: 13px;
	margin-top: 2px;
}
.sbi-fb-type-el-info span{
	font-size: 13px;
	line-height: 1.4em;
	color: #74777D;
}*/
.sbi-fb-adv-types .sbi-fb-type-el-img {
    border-color: #FCF4EF;
}
.sbi-fb-ft-action {
    border-top: 1px solid #D8DADD;
    padding: 30px 40px;
    margin-top: 63px;
}
.sbi-fb-slctfd-action .sbi-fb-wrapper{
    display: flex;
    align-items: center;
    justify-content: flex-end;
}
.sbi-fb-slctfd-action{
    padding: 16px 0;
}
.sbi-fb-slctf-nxt{
    height: auto;
    padding: 7px 36px 7px 37px;
}
.sbi-fb-slctf-back{
    margin-right: auto;
    height: auto;
    padding: 7px 37px 7px 36px;
}
.sbi-fb-slctf-back svg,
.sbi-fb-slctf-nxt svg {
    width: 6px;
    height: 10px;
}
.sbi-fb-slctf-back span {
    display: inline-block;
    margin-left: 15px;
    line-height: 160%;
}
.sbi-fb-slctf-nxt span {
    display: inline-block;
    margin-right: 15px;
    line-height: 160%;
}
.sbi-fb-btn-ac{
    opacity: 0.6;
}
.sbi-fb-btn-ac[data-active="true"]{
    opacity: 1;
}

.sb-control-single-id-ctn{
    margin-top: 35px;
    padding: 25px 0px;
}
.sb-control-single-id-ctn:before,
.sb-control-before-brd:before
{
    content: '';
    position: absolute;
    border-top: 1px solid #DCDDE1;
    height: 2px;
    top: 0px;
    left: -20px;
    width: calc(100% + 40px);
}
.sb-control-single-id-input{
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 7px;
}
.sb-control-single-id-input input{
    height: 45px;
    float: left;
    background: #fff;
    margin-top: 6px;
    margin-right: 7px;
}

/*
	Video Type Chooser
*/
.sbi-fb-section-wh.sbi-fb-section-videos{
	padding: 50px 21px 54px 30px;
}
.sbi-fb-videotype-chooser{
	display: grid;
	grid-template-columns: 49% 49%;
    grid-column-gap: 1%;

}
.sbi-fb-section-videos{
	flex-direction: column;
}
.sbi-fb-section-videos .sbi-fb-section-video-playlist{
	margin-top: 30px;
}

.sbi-fb-section-video-playlist .sbi-fb-wh-inp{
	margin-top: 10px;
	margin-bottom: 10px!important;
}
.sbi-fb-section-videos .sbi-fb-sglelm-left{
	padding-right: 0px;
}
/*
Source Screen
*/
.sbi-fb-sec-heading > span{
    font-size:14px;
    color: #434960;
    line-height: 1.7em;
    display: block;
}
#sbi-builder-app .sbi-fb-sec-heading h4{
    margin-bottom: 2px;
}
#sbi-builder-app .sbi-fb-slctsrc-ctn .sbi-fb-sec-heading h4{
    margin-bottom: 2px !important;
}
.sbi-fb-sec-heading {
    margin-bottom: 14px;
}
.sbi-builder-app .sbi-fb-slctsrc-content,.sbi-fb-section-wh-insd{
    padding: 23px 30px;
}

.sbi-fb-slctsrc-ctn h4,.sbi-fb-section-wh-insd h4{
    font-size: 20px;
    padding: 0px;
    margin: 0px;
}
.sbi-fb-srcslist-ctn{
    display: grid;
    grid-template-columns: 32.66% 32.66% 32.66%;
    grid-column-gap: 1%;
    margin-bottom: 28px;
}
.sbi-fb-srcs-item{
    width: 100%;
    cursor: pointer;
    height: 62px;
    margin: 1% 0;
    border-radius: 3px;
    border: 1px solid #E7E7E9;
    display: flex;
    position: relative;
}
.sbi-fb-srcs-item[data-disabled="true"]{
    background: #F3F4F5;
}
.sbi-fb-srcs-item[data-disabled="true"] .sbi-fb-srcs-item-inf{
    opacity: .55;

}
#sbi-builder-app .sbi-fb-srcs-item .sbi-fb-srcs-item-inf .sbi-fb-srcs-item-name {
    color: #141B38;
    padding-right: 44px;
}
/*Disabled Controls*/
.sb-control-elem-ctn[data-disabled="true"] input[type="text"],
.sb-control-elem-ctn[data-disabled="true"] input[type="number"],
.sb-control-elem-ctn[data-disabled="true"] input[type="date"],
.sb-control-elem-ctn[data-disabled="true"] textarea{
    background: #f0f0f0!important;
    border-color: #D0D1D7!important;
}

.sb-control-elem-ctn[data-disabled="true"] .sb-control-colorpicker-swatch{
    background: #D0D1D7!important;
}
.sb-control-elem-ctn[data-disabled="true"] .sb-control-checkbox{
    background: #D0D1D7!important;
    border-color: #c1c1c1!important;
}
.sb-control-elem-ctn[data-disabled="true"] .sb-control-toggle-elm{
    background: #e5e6e7!important;
}
.sb-control-elem-ctn[data-disabled="true"] .sb-control-toggle-elm{
    border-color: #c1c1c1!important;
}
.sb-control-elem-ctn[data-disabled="true"] .sb-control-toggle-elm[data-active="true"]{
    border-top: 0px;
}
.sb-control-elem-ctn[data-disabled="true"] .sb-control-toggle-elm .sb-control-toggle-deco{
    border-color: #bbb!important;
}
.sb-control-elem-ctn[data-disabled="true"] .sb-control-toggle-elm[data-active="true"] .sb-control-toggle-deco{
    border-color: #8C8F9A!important;

}
#sbi-builder-app .sbi-fb-srcs-item .sbi-fb-srcs-item-inf .sbi-fb-srcs-item-name span {
    max-height: 30px;
    display: block;
    overflow: hidden;
    line-height: 1.1;
    padding-bottom:1px;
    color: #141B38;
}

#sbi-builder-app .sbi-fb-srcs-item:hover{
    border-color: #86D0F9;
}
#sbi-builder-app .sbi-fb-srcs-item[data-active="true"]{
    border-color: #0096cc;
}

.sbi-fb-source-top .sbi-fb-srcs-item{
    margin-bottom: 0px;
}
#sbi-builder-app .sbi-fb-srcs-new{
    display: flex;
    justify-content: center;
    align-items: center;
    background: #EBF5FF;
    border: 1px solid #EBF5FF;
}
#sbi-builder-app .sbi-fb-srcs-new span{
    margin-left: 13px;
    color: #0096CC;
}
.sbi-fb-srcs-new i{
    font-size: 14px;
    padding: 0 10px;
    margin-left: -10px;
}
.sbi-fb-srcs-item-chkbx{
    width: 40px;
    height: inherit;
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 0 16px 0 7px;
}

.sbi-fb-srcs-item-chkbx-ic,.sbi-fb-chbx-round{
    width: 16px;
    height: 16px;
    box-sizing: border-box;
    position: relative;
    border-radius: 50px;
    border: 2px solid #8c8f9a;
}
[data-source="active"] .sbi-fb-srcs-item-chkbx-ic{
    border-radius: 2px;
}

[data-active="true"] .sbi-fb-srcs-item-chkbx-ic, [data-active="true"] > .sbi-fb-chbx-round, .sbi-fb-source-popup [data-active="true"] > .sbi-fb-chbx-round{
    border-color: #0096cc;
    background: #0096cc;
}
[data-source="active"] [data-active="true"] .sbi-fb-srcs-item-chkbx-ic:before{
    content: ''!important;
    position: absolute!important;
    width: 8px!important;
    height: 3px!important;
    border-left: 2px solid #fff;
    border-bottom: 2px solid #fff;
    top: 2px!important;
    right: 1px!important;
    left: unset!important;
    background: unset!important;
    border-radius: unset!important;
    -webkit-transform: rotate(-45deg);
    transform: rotate(-45deg);
}

[data-multifeed="inactive"] [data-active="true"] .sbi-fb-srcs-item-chkbx-ic:before, [data-active="true"] > .sbi-fb-chbx-round:before,
.sbi-fb-source-popup .sbi-fb-source-list [data-active="true"] .sbi-fb-srcs-item-chkbx-ic:before,
.sbi-fb-section-videos [data-active="true"] .sbi-fb-srcs-item-chkbx-ic:before{
    content: '';
    position: absolute;
    height: 6px;
    width: 6px;
    background: #fff;
    border-radius: 25px;
    left: 3px;
    top: 3px;
}

.sbi-fb-sources-empty-ctn{
    padding: 24px 32px 28px 24px;
    background: #F9F9FA;
    border: 1px dashed #DCDDE1;
    margin-top: 10px;
    display: flex;
    align-items: center;
}

.sbi-fb-sources-empty-txt{
    font-style: normal;
    font-weight: normal;
    font-size: 16px;
    line-height: 160%;
    color: #141B38;
}
.sbi-fb-sources-empty-btn-ctn{
    margin-left: auto;
}
.sb-addsources-btn{
    color: #fff;
    height: 27px;
    padding: 9px 23px;
}
.sb-addsources-btn svg{
    margin-right: 10px;
    fill: currentColor;
        width: 14px;
    height: 14px;
}
.sbi-fb-mr-feeds,.sbi-fb-section-wh{
    margin-top: 8px;
    background: #fff;
    box-shadow: 0px 1px 2px rgba(0, 0, 0, 0.05), 0px 4px 5px rgba(0, 0, 0, 0.05);
    display: flex;
    align-items: center;
}
.sbi-fb-mr-fd-img {
    float: left;
    margin-right: 6%;
    width: 28%;
    line-height: 0;
}
.sbi-fb-mr-fd-img img{
    width: 100%;
}
.sbi-fb-mr-fd-content {
    display: flex;
    flex: 1;
    flex-direction: column;
    padding-right: 16px;
}
.sbi-fb-mr-fd-heading{
    font-weight:600;
    font-size: 18px;
    line-height: 1.7em;
    margin-bottom: 24px;
    float: left;
}
.sbi-fb-mr-fd-list{
    margin-bottom: 10px;
    display: grid;
    grid-template-columns: 1fr 1fr 1fr 1fr 1fr;
    justify-items: flex-start;
    gap: 8px;
}
.sbi-fb-mr-fd-item{
    box-sizing: border-box;
    position: relative;
    border: 1px solid #DCDDE1;
    border-radius: 2px;
    height: 40px;
    width: 100%;
    min-width: 120px;
    color: #141B38;
    font-size: 14px;
    font-weight:600;
    text-transform: capitalize;
    display: flex;
    justify-content: center;
    align-items: center;
	transition: all .15s ease-in-out;
}
.sbi-icon-platform-wrap {
    width: 100%;
    display: flex;
    justify-content: left;
    align-items: center;
    padding: 0 5px;
}

.sbi-fb-mr-fd-ic {
    margin-right: 9px;
}
.sbi-fb-mr-fd-ic svg{
    height: 16px;
    fill: currentColor;
    float: left;
}
#sbi-builder-app .sbi-fb-mr-fd-heading h3 {
    margin-bottom: 21px;
    max-width: 385px;
}



/*Embed Popup*/
.sbi-fb-embed-step-1{
    margin-top: 36px;
}

.sbi-fb-embed-step-1 > div{
    padding: 0 20px;
}
.sbi-fb-embed-step-1-top{
    padding-bottom: 53px!important;
    border-bottom: 1px solid #E8E8EB;
    margin-bottom: 42px;
}
#sbi-builder-app .sbi-fb-embed-step-1 h4{
    margin-bottom: 12px;
}
.sbi-fb-embed-input-ctn{
    display: flex;
}
.sbi-fb-embed-input-ctn input,
.sbi-fb-embed-input-ctn input[type="text"]{
    position: relative;
    font-style: normal;
    font-weight: normal;
    font-size: 16px;
    line-height: 160%;
    color: #141B38;
    height: 44px;
    width: 77%;
    box-sizing: border-box;
    border-radius: 0px;
    border: 1px solid #D0D1D7!important;
    border-right: 0px!important;

}
.sbi-fb-embed-input-ctn input:focus, .sbi-fb-embed-input-ctn .sbi-fb-hd-btn:focus{
    box-shadow: none!important;
    outline: none!important
}
.sbi-fb-embed-input-ctn .sbi-fb-hd-btn{
    width: 23%;
    border-bottom-left-radius: 0px;
    border-top-left-radius: 0px;
}
.sbi-fb-embed-input-ctn svg{
    width: 17px;
    fill: currentColor;
    float: left;
    margin-right: 10px;
}
.sbi-fb-embed-step-1-bottom h4{
    text-align: center;
}
.sbi-fb-embed-btns-ctn{
    display: flex;
    justify-content: center;
    gap: 2%;
    margin-top: 14px;
    margin-bottom: 60px;
}
.sbi-fb-embed-btn{
    cursor: pointer;
    width: 100%;
    max-width: 400px;
    height: 50px;
    background: #F3F4F5;
    border-radius: 2px;
    border: 1px solid #DCDDE1;
    display: flex;
    align-items: center;
    justify-content: space-between;
    box-sizing: border-box;
    padding: 0 20px;
    position: relative;
    color: #141B38;
    font-size: 16px;
    font-weight: 500;
}
.sbi-fb-embed-btn .sb-icon-label svg{
    fill: currentColor;
    width: 18px;
    margin-right: 10px;
    float: left;
}
.sbi-fb-embed-popup .sb-embed-breadcrumb {
    display: flex;
    align-items: center;
    padding: 25px 30px 0;
    margin-bottom: 2px;
}
.sb-embed-breadcrumb a {
    font-style: normal;
    font-weight: bold;
    font-size: 10px;
    line-height: 160%;
    letter-spacing: 0.08em;
    text-transform: uppercase;
    color: #434960;
    cursor: pointer;
}
.sb-embed-breadcrumb a:hover {
    color: #141B38;
}
.sb-embed-breadcrumb svg {
    margin-right: 8px;
}
.sbi-fb-embed-step-2 > div{
    padding: 0 29px;
}

.sb-customizer-sidebar-cache-wrapper{
    margin-top: 20px;
    padding: 0 20px;
}
.sbi-fb-embed-step-2-list{
    margin-top: 25px;
    border-top: 1px solid #DCDDE1;
    padding-top: 14px !important;
}
.sbi-fb-embed-step-2-list > strong{
    text-transform: uppercase;
    font-size: 12px;
    color: #434960;
    margin-bottom: 10px;
}
.sbi-fb-embed-step-2-pages{
    margin-top: 8px;
    height: 250px;
    overflow: auto;
}
.sbi-fb-embed-step-2-pages .sb-control-toggle-icon svg {
    width: 11px;
}
.sbi-fb-embed-step-2-action{
    padding: 10px 34px 10px 30px !important;
    background: #F3F4F5;
    box-shadow: 0px -4px 5px rgba(0, 0, 0, 0.1);
}
.sbi-fb-embed-step-2-action a{
    color: #fff;
}
.sbi-fb-embed-step-2-action a[data-active="false"]{
    opacity: .75;
}

/*Dialog Popup*/
.sbi-fb-dialog-remove-source{
    background: #F3F4F5;
    padding: 40px 20px;
    display: flex;
    justify-content: center;
    align-items: center;
}
.sbi-fb-dialog-remove-source .sbi-fb-srcs-item{
    background: #fff;
    width: 280px;
    padding-left: 20px;
    box-sizing: border-box;
    margin-top: 0px;
}
.sbi-fb-srcs-item-remove{
    position: absolute;
    width: 35px;
    height: 35px;
    border-radius: 50px;
    background: #fff;
    border:1px solid #E7E7E9;
    z-index: 3;
    right: -13px;
    bottom: -13px;
    display: flex;
    justify-content: center;
    align-items: center;
    box-shadow: 0px 26.7377px 77.2886px rgba(0, 0, 0, 0.107828), 0px 14.2952px 41.3222px rgba(0, 0, 0, 0.0894161), 0px 8.01379px 23.1649px rgba(0, 0, 0, 0.075), 0px 4.25607px 12.3027px rgba(0, 0, 0, 0.0605839), 0px 1.77104px 5.11942px rgba(0, 0, 0, 0.0421718);
}
.sbi-fb-srcs-item-remove svg{
    width: 12px;
    float: left;
    fill: var(--error-red);
}
.sbi-fb-dialog-popup-content{
    display: flex;
    justify-content: center;
    align-items: center;
    text-align: center;
    flex-direction: column;
    padding: 38px 12%;
}
.sbi-fb-dialog-popup-content strong{
    font-size: 22px;
    color: #141B38;
    display: block;
    margin-bottom: 15px;
}
.sbi-fb-dialog-popup-content span{
    font-size: 16px;
    line-height: 1.5em;
    color: #434960;
}
.sbi-fb-dialog-popup-actions{
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 25px;
}
.sbi-fb-dialog-popup-actions button{
    width: 100%;
    margin: 4px 4px;
    cursor: pointer;
    height: 42px;
}

/*
	Footer Sticky Widget
*/
.sbi-stck-wdg{
    position: fixed;
    right: 21px;
    z-index: 9;
    bottom: 20px;
}
.sbi-stck-wdg-btn{
    width: 52px;
    height: 52px;
    background: #fff;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    justify-content: center;
    align-items: center;
    filter: drop-shadow(0px 9px 13px rgba(0, 0, 0, 0.2));
}
.sbi-stck-wdg-btn svg{
    width: 25px;
    fill: #FE544F;
    height: 33px;
    float: left;
}
.sbi-stck-wdg[data-active="true"] .sbi-stck-wdg-btn svg,.sbi-stck-wdg-btn-cls{
    display: none;
}

.sbi-stck-wdg[data-active="true"] .sbi-stck-wdg-btn-cls,
.sbi-stck-wdg[data-active="true"] .sbi-stck-wdg-btn-cls svg{
    display: block;
}
.sbi-stck-wdg-btn-cls{
    width: inherit;
    height: inherit;
    position: relative;
    color: #364152;
    box-shadow: 0px 1px 6px rgba(0, 0, 0,  .05), 0px 9px 12px rgba(0, 0, 0,  .05);
    border-radius: 70px;
}
.sbi-stck-wdg-btn-cls svg {
    width: 14px;
    height: 14px;
    position: absolute;
    top: 50%;
    right: 0;
    bottom: 0;
    left: 50%;
    margin-top: -7px;
    margin-left: -7px;
}

.sbi-stck-pop{
    position: absolute;
    width: 292px;
    height: auto;
    background: #fff;
    border: 1px solid #E2E8F0;
    box-sizing: border-box;
    box-shadow: 0px 1px 3px rgba(0, 0, 0, 0.15), 0px 3px 14px rgba(0, 0, 0, 0.12);
    border-radius: 2px;
    padding: 20px;
    right: 0px;
    bottom: 100px;
    color: #141B38;
    opacity: 0;
    padding-bottom: 82px;
    visibility: hidden;
}
.sbi-stck-wdg[data-active="true"] .sbi-stck-pop{
    bottom: 66px;
    opacity: 1;
    visibility: visible;
}

.sbi-stck-pop svg{
    fill: currentColor;
}
.sbi-stck-el-list{
    border: 1px solid #DCDDE1;
    border-radius: 2px;
}
.sbi-stck-el{
    display: flex;
    align-items: center;
    padding: 11px 13px;
    border-bottom: 1px solid #DCDDE1;
}
.sbi-stck-el:hover{
    background: #F3F4F5;
}
.sbi-stck-el:last-of-type{
    border-bottom: 0px;
}
.sbi-stck-el-list .sbi-chevron svg{
    width: 5px;
    height: 8px;
}
.sbi-stck-el-list .sbi-stck-el-icon svg {

}
.sbi-stck-el .sbi-stck-el-txt{
    color: #27303F;
}
.sbi-stck-el.sbi-stck-el-upgrd{
    padding: 8px 14px;
    font-size: 14px;
    background: var(--cl-orange);
    color: #fff;
}
.sbi-chevron {
    position: absolute;
    right: 14px
}
.sbi-stck-el.sbi-stck-el-upgrd .sbi-stck-el-txt{
    color: #fff;
}
.sbi-stck-el.sbi-stck-el-upgrd:after{
    top: 20px;
    opacity: 1;
}
.sbi-stck-el-icon{
    margin-right: 10px;
}
.sbi-stck-el-icon svg{
    width: 17px;
    float: left;
}
.sbi-stck-el.sbi-stck-el-upgrd svg path{
    fill: #fff!important;
}
#sbi-builder-app .sbi-stck-title{
    margin-top: 20px;
    margin-bottom: 10px;
}

.sbi-stck-follow{
    background: #F3F4F5;
    margin-top: 20px;
    left: 0px;
    bottom: 0px;
    position: absolute;
    padding: 12px 20px;
    display: flex;
    align-items:  center;
}
.sbi-stck-follow span{
    font-weight: 600;
    font-size: 14px;
}
.sbi-stck-flw-links{
    display: flex;
    justify-content: center;
    align-items: center;
    margin-left: auto;
}
.sbi-stck-flw-links a{
    width: 36px;
    height: 28px;
    color: inherit;
    display: inline-flex;
    justify-content: center;
    align-items: center;
    margin-left: 4px;
    border-radius: 2px;
}
.sbi-stck-flw-links svg{
    width: 17px;
    color: #141B38;
}
.sbi-stck-flw-links a:hover{
    background: #fff;
}
.sbi-stck-flw-links a:hover svg{
    color: inherit;
}



/*
	Builder Footer
*/
.sbi-bld-footer > div{
    background: #fff;
    color: #141B38;
    margin: 30px 0;
}
.sbi-bld-footer{
    padding-top: 0px!important
}
.sbi-bld-ft-content{
    display: flex;
    align-items: center;
    max-width: 1200px;
}
.sbi-bld-ft-img{
    float: left;
    width: 15%;
    margin-right: 5%;
    /*height: 158px;*/
}
.sbi-bld-ft-img svg{
    margin-bottom: -4px;
}
.sbi-bld-ft-txt{
    display: flex;
    justify-content: center;
    align-items: center;
    width: 59%;
    margin-right: 5%;
}
.sbi-bld-ft-txt strong,
.sbi-fb-mr-fd-heading span{
    color: var(--cl-orange);
}
#sbi-builder-app .sbi-bld-ft-info{
    width: 44%;
    color: #434960;
}
#sb-footer-banner .sbi-bld-ft-title{
    width: 63%;
    margin-right: 6%;
}
.sbi-bld-ft-action{
    width: 17%;
    display: flex;
    justify-content: left;
    align-items: center;
}
.sbi-bld-ft-action svg{
    top: 14px;
    height: 10px;
}
@media all and (max-width: 1130px) {
    #sb-footer-banner .sbi-bld-ft-img{
        width: 17%;
        margin-right: 3%;
    }
    #sb-footer-banner .sbi-bld-ft-txt{
        display: flex;
        justify-content: center;
        align-items: center;
        width: 61%;
        margin-right: 3%;
    }
    #sbi-builder-app #sb-footer-banner h3 {
        font-size: 20px;
    }
    #sbi-builder-app #sb-footer-banner .sb-small-p {
        font-size: 13px;
    }
    #sb-footer-banner .sb-button-standard {
        font-size: 13px;
        padding-left: 16px;
        line-height: 15px;
    }
}

.sbi-bld-ft-btm{
    font-size: 14px;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 15px 0px;
    border-top: 1px solid #DCDDE1;
}
.sbi-bld-ft-btm strong{
    padding: 0 5px;
}
.sbi-bld-ft-btm a{
    display: inline-block;
    padding: 0 10px;
    font-weight: 500;
    color: #0068A0;
}
.sbi-bld-ft-btm a i{
    font-size: 12px;
    margin-left: 5px;
}
.sbi-bld-ft-bns{
    display: inline-block;
    margin: 0 10px;
    padding: 5px 10px;
    border-radius: 5px;
    font-size: 14px;
    font-weight: 700;
    text-transform: uppercase;
    color: #663D00;
    background: #FFDF99;
}
/*
	Extensions Poup
*/
.sbi-fb-fs-link{
    position: absolute;
    width: 100%;
    height: 100%;
    left: 0;
    top: 0;
    right: 0;
    bottom: 0;
    z-index: 1;
}
.sbi-fb-extensions-popup{

}
.sbi-fb-extpp-top{
    display: flex;
    justify-content: center;
    align-items: center;
    height: 264px;
    background: #F3F4F5;
}
.sbi-fb-extpp-img{
    width: 50%;
    float: left;
    display: flex;
    justify-content: center;
}
.sbi-fb-extensions-popup[data-getext-view="featuredpost"] .sbi-fb-extpp-img{
    padding: 50px 0px;
}
.sbi-fb-extensions-popup[data-getext-view="singlealbum"] .sbi-fb-extpp-img{
    padding: 65px 0px;
}
.sbi-fb-extpp-social-wall .sbi-fb-extpp-top {
    display: flex;
    flex-direction: column;
    align-items: center;
    height: auto;
    padding-top: 29px;
}
.sbi-fb-extensions-popup[data-getext-view="socialwall"] .sbi-fb-extpp-head  {
    margin: 0 0 12px;
    padding-top: 14px;
}
.sbi-fb-extensions-popup[data-getext-view="socialwall"] .sbi-fb-extpp-head h2 {
    font-size: 24px !important;
    line-height: 120% !important;
}
.sbi-fb-extpp-info{
    width: 50%;
    float: left;
    display: flex;
    justify-content: center;
    flex-direction: column;
    padding-left: 40px;
}
.sbi-fb-extensions-popup[data-getext-view="socialwall"] .sbi-fb-extpp-info {
    width: 40%;
    padding-right: 50px;
}

.sbi-fb-extensions-popup[data-getext-view="socialwall"] .sbi-fb-extpp-lite-btn {
    margin-top: 20px;
    max-width: 270px;
    box-sizing: border-box;
    padding: 7px 11px;
}
.sbi-fb-extensions-popup[data-getext-view="socialwall"] .sbi-extension-bullet-list {
    grid-template-columns: 33% 33% 33%;
}
#sbi-builder-app .sbi-fb-extensions-popup[data-getext-view="socialwall"] .sbi-fb-extpp-head{
    margin: 0 0 9px;
}
#sbi-builder-app .sbi-fb-extpp-desc {
    color: #475569;
    display: flex;
    justify-content: center;
    align-items: center;
}
.sbi-fb-extpp-social-wall .sbi-fb-extpp-top {
    display: flex;
    flex-direction: column;
    align-items: center;
    height: auto;
    padding-top: 29px;
}

.sbi-fb-extpp-info{
    width: 50%;
    float: left;
    display: flex;
    justify-content: center;
    flex-direction: column;
    padding-left: 40px;
}
.sbi-fb-extpp-info .sbi-fb-extpp-head,
.sbi-fb-extpp-info .sbi-fb-extpp-desc{
    width: 100%;
}
#sbi-builder-app .sbi-fb-extpp-head{
    margin: 0 0 12px;
}
#sbi-builder-app .sbi-fb-extpp-desc {
    color: #475569;
    display: flex;
    justify-content: left;
    align-items: center;
    width: 105%;
}
.sbi-fb-extpp-head span{
    color: var(--cl-orange);
}
.sbi-fb-extpp-head span.sb-social-wall{
    color: inherit;
    font-size: 26px;
    line-height: 120% !important;
    display: inline-block;
}
.sbi-fb-extpp-desc .sb-social-wall {
    width: 90%;
}
.sbi-fb-extpp-head span{
    color: var(--cl-orange);
}
.sbi-fb-extpp-lite-btn{
   float: left;
    font-size: 15px;
    font-weight: 500;
    padding: 10px 20px;
    color: #0068A0;
    background: #fff;
    border: 1px solid #DCDDE1;
    margin-bottom: 14px;
    margin-top: 12px;
    align-items: center;
}
.sbi-fb-extpp-lite-btn svg{
    fill: currentColor;
    width: 20px;
    float: left;
    margin-right: 10px;
}
.sbi-fb-extpp-inc-list{
    border: 1px solid #dcdde1;
    margin-top: 10px;
    margin-bottom: 30px;
}
.sbi-fb-extpp-bottom-strg{
    font-size:18px;
    font-weight: 600;
}
.sbi-fb-extpp-inc-items{
    display: grid;
    grid-template-columns: 25% 25% 25% 25%;
}
.sbi-fb-extpp-inc-item, .sbi-fb-extpp-inc-item-bottom{
    position: relative;
    height: 50px;
    display: flex;
    justify-content: center;
    align-items: center;
    border-right:  1px solid #dcdde1;
    border-bottom:  1px solid #dcdde1;
    font-size: 14px;
}
.sbi-fb-extpp-inc-item-bottom{
    border: 0px !important;
}
.sbi-fb-extpp-inc-item:last-of-type{
    border-right: 0px;
}
.sbi-fb-extpp-inc-item svg, .sbi-fb-extpp-inc-item-bottom svg{
    width: 17px; margin-right: 10px; fill: currentColor; float: left;
}
.sbi-fb-extpp-bottom{
    padding: 20px 40px;
    background: #fff;
}
.sbi-extension-bullet-list {
    display: grid;
    grid-template-columns: 33% 33% 33%;
    grid-column-gap: 2%;
    margin-top: 12px;
    margin-bottom: 40px;
}
.sbi-extension-single-bullet {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    margin: 4px 0;
}
#sbi-builder-app .sbi-extension-single-bullet span {
    color: #434960;
}

.sbi-extension-single-bullet svg {
    margin-right: 12px;
}
.sbi-fb-extpp-btns{
    display: grid;
    grid-template-columns: 100%;
    grid-column-gap: 1%;
}
.sbi-fb-extpp-btns div{
    height: 38px;
    cursor: pointer;
    position: relative;
    border-radius: 3px;
    font-style: normal;
    font-weight: 600;
    font-size: 14px;
    line-height: 160%;
    display: flex;
    justify-content: center;
    align-items: center;
    box-sizing: border-box;
}
.sbi-fb-extpp-get-btn{
    color: #fff;
    background: var(--cl-orange);
}
.sbi-fb-extpp-demo-btn{
    border: 2px solid #DCDDE1;
    background: #F3F4F5;
}

/*
	Feed Type Creation Process : Single Album;
*/
.sbi-fb-section-wh-insd{
    display: flex;
}
.sbi-fb-sglelm-inp-ctn{
    margin-top: 100px;
}

.sbi-fb-sglelm-inp-ctn input[type="text"]{
    height: 44px;
}
.sbi-fb-section-wh.sbi-fb-sglelm-ctn {
    padding: 20px 21px 24px 30px;
}
.sbi-fb-section-wh.sbi-fb-sglelm-ctn .sbi-fb-section-wh-insd {
    padding: 0;
}
.sbi-fb-sglelm-inp-ctn input[type="text"]::placeholder{
    font-style: normal;
    font-weight: normal;
    font-size: 16px;
    line-height: 160%;
    color: #8C8F9A;
}
.sbi-fb-sglelm-error-icon{
    width: 23px;
    height: 23px;
    background: var(--error-red);
    font-family: monospace;
    font-weight: 900;
    color: #fff;
    font-size: 15px;
    position: absolute;
    right: 10px;
    top: 10px;
    z-index: 999999999999999999;
    border-radius: 50px;
    display: flex;
    justify-content: center;
    align-items: center;
}
.sbi-fb-sglelm-errormsg{
    color: var(--error-red);
    font-weight: 800;
}
.sbi-fb-sglelm-inp-ctn .sbi-fb-wh-inp{
    margin-bottom: 10px;
}
.sbi-fb-sglelm-left{
    padding-right: 100px;
}
.sbi-fb-sglelm-img-ctn{
    background:#E8E8EB;
    height: 350px;
    display: flex;
    justify-content: center;
    align-items: center;
    text-align: center;
    flex-direction: column;
    padding: 100px 50px;
    border: 1px solid #D0D1D7;
    box-sizing: border-box;
}
.sbi-fb-sglelm-img-ctn.sbi-fb-sglelm-img-pf{
    padding: 100px 35px;
}
.sbi-fb-sglelm-img-ctn strong{
    font-size: 16px;
    color: #434960;
    margin-bottom: 8px;
    margin-top: 30px;
}
.sbi-fb-sglelm-img-ctn span{
    font-size: 14px;
    line-height:1.6em;
    color: #8C8F9A;
}
.sbi-fb-sglelm-right{
    width: 445px
}
.sbi-fb-sglelm-img-errorctn span{
    padding: 0 20px;
    color: #434960;
    font-size: 20px;
    margin-top: 30px;
    line-height: 1.2em;
}
.sbi-fb-sglelm-preview{
    height: 375px;
    background-color: #141B38;
    background-position: center center;
    background-size: cover;
    display: flex;
    justify-content: center;
    align-items: flex-end;
    color: #fff;
}
.sbi-fb-sglelm-preview:after{
    content: '';
    position: absolute;
    width: 100%; height: 100%;
    background-image: linear-gradient(to bottom, rgba(0,0,0,0) , rgba(0,0,0,.5));
}
.sbi-fb-sglelm-prev-info{
    width: 100%;
    padding: 20px 30px;
    padding-bottom: 40px;
    display: flex;
    justify-content: center;
    align-items: center;
    text-align: center;
    flex-direction: column;
    z-index: 2;
}
.sbi-fb-sglelm-prev-info strong{
    font-size: 18px;
    margin-bottom: 5px;
}
.sbi-fb-sglelm-prev-info span{
    font-size: 16px;
    line-height: 1.7em;
}


/*
	Feeds List
*/
#sbi-builder-app .sbi-fb-select,
#sbi-builder-app .sbi-fb-select:hover,
#sbi-builder-app .sbi-fb-select:focus{
    border-radius: 0px!important;
    border: 1px solid #D0D1D7!important;
    outline: unset!important;
    float: left;
    min-height: auto;
    padding: 6px 31px 6px 8px;
    background: #fff url("data:image/svg+xml,%3Csvg width='10' height='5' viewBox='0 0 10 5' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M0.833252 0.333252L4.99992 4.49992L9.16658 0.333252H0.833252Z' fill='%238C8F9A'/%3E%3C/svg%3E%0A") no-repeat right 13px top 55%;
    background-size: 10px 6px;
}
.sbi-fd-lst-bulk-ctn > *{
    float: left;
    margin-right: 10px;
}
.sbi-fd-lst-bulk-ctn{
    margin-bottom: 8px;
}
.sbi-fd-lst-pagination-ctn{
    float: right;
    display: flex;
    align-items: center;
    margin-top: 10px;
}
.sbi-fd-lst-pgnt-btn{
    height: 30px;
    padding: 0 8px;
    cursor: pointer;
    border-radius: 3px;
}
.sbi-fd-lst-pgnt-info{
    display: inline-block;
    padding: 0 3px;
}
.sbi-fd-lst-count{
    margin-right: 10px;
}
.sbi-feeds-list table{
    width: 100%;
    text-align: left;
    border-spacing: 0px;
    box-sizing: border-box;
    box-shadow: 0px 1px 2px rgba(0, 0, 0, 0.05), 0px 4px 5px rgba(0, 0, 0, 0.05);
    background: #fff;
}

.sbi-feeds-list table a{
    cursor: pointer;
}
.sbi-fd-lst-tbody tr:nth-child(odd){
    background: #F3F4F5;
}
.sbi-fd-lst-tbody tr td{
    position: relative;
    vertical-align: top;
    padding: 12px 5px;
    font-size: 16px;
}
.sbi-feeds-list table tr td:first-child {
    width: 30px;
}
.sbi-fd-lst-thtf{
    background: #fff;
}
.sbi-fd-lst-thtf th, .sbi-fd-lst-thtf td{
    padding: 6px 5px;
}
.sbi-fd-lst-thtf th{
    border-bottom: 1px solid #DCDDE1;
}
.sbi-fd-lst-thtf td{
    border-top: 1px solid #DCDDE1;
}
.sbi-fd-lst-chkbx{
    width: 11px;
    height: 11px;
    position: relative;
    border-radius: 2px;
    border: 1px solid #97A6BA;
    background: #fff;
    cursor: pointer;
    margin-left: 10px;
}
tbody .sbi-fd-lst-chkbx {
    position: absolute;
    top: 15px;
    left: 5px;
    bottom: 0;
    right: 0;
}
.sbi-fd-lst-thtf .sbi-fd-lst-chkbx{
    width: 11px;
    height: 11px;
    border: 1px solid #D8DADD;
}
.sbi-fd-lst-chkbx[data-active="true"]{
    background: var(--customizer-blue);
    border-color: var(--customizer-blue)!important;
}
.sbi-fd-lst-chkbx[data-active="true"]:before{
    content: '';
    position: absolute;
    width: 6px;
    height: 3px;
    left: 2px;
    top: 2px;
    border-bottom: 2px solid #fff;
    border-left: 2px solid #fff;
    -webkit-transform: rotate(-45deg);
    transform: rotate(-45deg);
}


.sbi-fd-lst-name{
    font-size: 17px;
    font-weight:500;
    color: #0068A0!important;
}
.sbi-fd-lst-type{
    display: block;
    text-transform: capitalize;
}
.sb-instances-cell {
    margin-top: 4px;
}
.sbi-fd-lst-btn{
    width: 21px;
    height: 21px;
    display: inline-flex;
    justify-content: center;
    align-items: center;
    margin: 0 4px;
    cursor: pointer;
    color: #141B38;
    border-radius: 2px;
    border: 1px solid #D8DADD;
    -webkit-transition: all .2s ease-in-out;
    transition: all .2s ease-in-out;
    background: #fff;
}
.sbi-fd-lst-btn svg {
    height: 13px;
}
.sbi-fd-lst-actions .sbi-fd-lst-btn,
.sbi-fb-inst-tbl-ctn .sbi-fd-lst-btn{
    box-sizing: border-box;
    width: 36px;
    height: 32px;
    background: transparent;
}
.sbi-fd-lst-actions .sbi-fd-lst-btn {
    margin: 0 4px 0 0;
}
.sbi-fd-lst-actions .sbi-fd-lst-btn:last-child {
    margin: 0;
}
.sbi-fd-lst-actions .sbi-fd-lst-btn svg {
    width: 12px;
    height: 16px;
}
.sbi-fd-lst-btn-delete{
    color: #D72C2C;
    border-color: #FBD5D5;
}
.sbi-fd-lst-btn svg{
    fill: currentColor;
    width: 14px;
    float: left;
}
.sbi-fd-lst-actions .sbi-fd-lst-btn:hover svg,
.sbi-feedtype-section .sbi-fd-lst-btn-delete:hover svg{
    color: #fff;
}
.sbi-fd-lst-actions .sbi-fd-lst-btn:hover{
    background: #0096CC;
    border-color: #0096CC;
}
.sbi-fd-lst-actions .sbi-fd-lst-btn-delete:hover, .sbi-feedtype-section .sbi-fd-lst-btn-delete:hover{
    background: #D72C2C;
    border-color: #D72C2C;
}
.sbi-fd-lst-dimmed .sbi-fd-lst-btn, .sbi-fd-lst-dimmed .sbi-fd-lst-btn:hover{
    background: #F3F4F5!important;
    border-color:#D8DADD!important;
    color: #8C8F9A!important;
}
.sbi-fd-lst-dimmed .sbi-fd-lst-btn:hover svg{
    color: #8C8F9A!important;
}

.sbi-fb-tltp-parent{
    position: relative;
}
.sbi-fb-view-instances[data-active="true"]{
    font-weight: 600;
    text-decoration: underline;
    cursor: pointer;
    line-height: 1em;
}
.sbi-fb-tltp-elem{
    position: absolute;
    color: #fff;
    background: #434960;
    font-size: 14px;
    padding: 7px 10px;
    border-radius: 3px;
    font-weight:500;
    z-index: 9;
    text-align: center;
    opacity: 0;
    visibility: hidden;
    top: calc(-100% - 30px);
    left: 50%;
    -webkit-transform: translateX(-50%);
    transform: translateX(-50%);
    -webkit-transition: all .2s ease-in-out;
    transition: all .2s ease-in-out;
}
.sbi-fb-tltp-elem span{
    position: relative; z-index: 3;
}
.sbi-fb-tltp-elem:after{
    content: '';
    position: absolute;
    height: 10px;
    width: 10px;
    bottom: -5px;
    left: calc(50% - 5px);
    background: #434960;
    transform: rotate(-45deg);
}
.sbi-fb-tltp-parent:hover .sbi-fb-tltp-elem {
    top: calc(-100% - 20px);
    opacity: 1;
    visibility: visible;
}
.sbi-fd-lst-shortcode-cp{
    margin-left: 10px;
}
.sbi-fd-lst-act-th{
    width: 190px;
    max-width: 190px;
}

/*
Feed Instance Popup
*/
.sbi-fb-popup-feedinst .sbi-fb-source-top{
    display: flex;
    align-items: center;
}
.sbi-fb-popup-feedinst h5{
    margin-bottom: 0px;
    float: left;
    font-size: 27px;
}
.sbi-fb-fdinst-type{
    padding: 5px 5px;
    background: #E8E8EB;
    margin-left: 12px;
    float: left;
}
.sbi-fb-inst-tbl-ctn{
    padding: 0 23px 63px;
}
.sbi-fb-inst-tbl-ctn table{
    width: 100%;
    border-spacing: unset;
    box-sizing: border-box;
    border: 1px solid #DCDDE1;
    text-align: left;
}
.sbi-fb-inst-tbl-ctn tfoot,.sbi-fb-inst-tbl-ctn  thead{
    background: #F3F4F5
}
.sbi-fb-inst-tbl-ctn .sbi-fd-lst-thtf th, .sbi-fb-inst-tbl-ctn .sbi-fd-lst-thtf td{
    font-size: 13px;
    color: #364152;
    padding: 13px 10px;
}
.sbi-fb-inst-tbl-ctn  .sbi-fd-lst-tbody tr:nth-child(odd){
    background: #fff;
}
.sbi-fb-inst-tbl-ctn  .sbi-fd-lst-tbody tr:nth-child(even){
    background: #F3F4F5;
}
.sbi-fb-inst-tbl-ctn .sbi-fd-lst-thtf tr th,
.sbi-fb-inst-tbl-ctn .sbi-fd-lst-thtf tr td{
    padding: 4px 20px;
}
.sbi-fb-inst-tbl-ctn .sbi-fd-lst-tbody tr td{
    padding: 11px 20px;
}
.sbi-fb-inst-tbl-ctn .sbi-fd-lst-name{
    font-size: 14px;
}
.sbi-fb-inst-tbl-shrtc{
    display: flex;
    align-items: center;
}

.sbi-fd-inst-btn{
    width: 10px;
    height: 10px;
    box-sizing: border-box;
    border-right: 3px solid #8C8F9A;
    border-top: 3px solid #8C8F9A;
    cursor: pointer;
    -webkit-transform: rotate(45deg);
    transform: rotate(45deg);
}

/*Legacy Feeds */
.sbi-fb-lgc-top-new{
    color: #141B38;
    background: #fff;
    margin: 10px 0px;
    padding: 15px 20px;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 14px;
    font-weight: 600;
}
.sbi-fb-lgc-gr{
    font-size: 12px;
    text-transform: uppercase;
    font-weight: 500;
    background: #59AB46;
    color: #fff;
    margin-right: 10px;
    padding: 6px 10px;
    border-radius: 2px;
    line-height: 1em;
}
.sbi-fb-lgc-inf-ctn{
    background: #fff;
    padding: 0 20px;
    display: flex;
    align-items: center;
    height: 58px;
}
.sbi-fb-lgc-inf-ctn > h4{
    margin-right: 10px!important;
}

.sbi-fb-lgc-inf-ctn > *{
    float: left;
    display: inline-block;
    position: relative;
}
#sbi-builder-app .sbi-fb-lgc-inf-ctn > h4{
    margin-right: 14px;
    margin-bottom: 0;
}
#sbi-builder-app .sbi-fb-lgc-btn-stg{
    display: flex;
    margin-left: auto;
}
.sbi-fd-legacy-feed-toggle{
    height: 34px;
    display: flex;
    justify-content: center;
    align-items: center;
    background: #E2F5FF;
    color:#0068A0;
    cursor: pointer;
    font-weight: 600;
    font-size: 14px;
    line-height: 160%;
}
.sbi-fd-legacy-feed-toggle:hover {
    background: #BFE8FF;
}
.sbi-fd-legacy-feed-toggle span{
    display: inline-block;
    position: relative;
    padding-right: 20px;
    margin-left: -20px;
}
.sbi-fd-legacy-feed-toggle[data-active="true"] span:after{
    -webkit-transform: rotate(-225deg);
    transform: rotate(-225deg);
    top: 7px;
}
.sbi-feeds-list .sbi-legacy-table-wrap table{
    box-shadow: none;
    border-top: 1px solid #DCDDE1;
}
.sbi-fb-lgc-ctn{
    box-shadow: 0px 1px 2px rgba(0, 0, 0, 0.05), 0px 4px 5px rgba(0, 0, 0, 0.05);
    margin-bottom: 20px;
}

.sbi-fb-onbrd-infotxt{
    display: flex;
    align-items: center;
}
.sbi-fb-onbrd-infotxt svg{
    fill: currentColor;
    width: 16px;
    display: inline-block;
    margin-left: 10px;
    float: left;
}

.sbi-fb-onbrd-tltp-parent{
    position: relative;
}
.sbi-fb-onbrd-tltp-elem{
    position: absolute;
    z-index: 9;
    background: #fff;
    border-radius: 2px;
    color: #434960;
    padding: 16px 52px 4px 24px;
    font-size: 15px;
    left: -30px;
    top: calc(100% + 20px);
    line-height: 1.7em;
    box-shadow: 0px 1px 18px rgba(0,0,0,.2);
    display: none;
}



.sbi-fb-onbrd-tltp-elem[data-active="false"]{
    display: none;
}
.sbi-fb-onbrd-tltp-elem[data-active="true"],
.sbi-fb-onbrd-tltp-hover:hover .sbi-fb-onbrd-tltp-elem{
    display: block;
    min-width: 440px;
    padding-bottom: 15px;
}
.sbi-fb-onbrd-tltp-elem .sb-pointer {
    position: absolute;
    left: 85px;
    top: -17px;
}
.sbi-fb-onbrd-tltp-elem-2 .sb-pointer {
    left: 485px;

}
.sbi-fb-onbrd-tltp-elem:after{
    background: #fff;
    z-index: 999;
    top: 0px;
    margin-left: -10px;
    width: 40px;
    box-shadow: unset;
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
}
.sbi-fb-onbrd-tltp-parent.sbi-fb-onbrd-tltp-center-top .sbi-fb-onbrd-tltp-elem{
    left: 50%;
    -webkit-transform:translateX(-50%);
    transform:translateX(-50%);
    bottom: calc(100% + 15px);
    top: unset;
    padding: 8px 11px;
    width: 100%;
    text-align: center;
    box-shadow: 0 5px 9px rgba(0,0,0,.2), 0 -4px 9px rgba(0,0,0,.1);
}
.sbi-fb-onbrd-tltp-parent.sbi-fb-onbrd-tltp-center-top .sbi-fb-onbrd-tltp-elem:before {
    content: '';
    position: absolute;
    left: 50%;
    bottom: -10px;
    border-top: 12px solid #fff;
    border-right: 12px solid transparent;
    border-left: 12px solid transparent;
    -webkit-transform: translateX(-50%);
    transform: translateX(-50%);
    z-index: 2;
}
.sbi-fb-onbrd-tltp-parent.sbi-fb-onbrd-tltp-center-top .sbi-fb-onbrd-tltp-elem:before,
.sbi-fb-onbrd-tltp-parent.sbi-fb-onbrd-tltp-center-top .sbi-fb-onbrd-tltp-elem:after{
    left: calc(50% - 10px);
    top: unset;
}
.sbi-fb-onbrd-tltp-parent.sbi-fb-onbrd-tltp-center-top .sbi-fb-onbrd-tltp-elem:before{
    bottom: -10px;
}
.sbi-fb-onbrd-tltp-parent.sbi-fb-onbrd-tltp-center-top .sbi-fb-onbrd-tltp-elem:after{
    bottom: 0px;
}


#sbi-builder-app .sbi-fb-onbrd-tltp-txt{
    margin: 0px;
}
.sbi-fb-onbrd-tltp-txt:last-of-type{
    margin-bottom: 0px;
}
.sbi-fb-onbrd-tltp-elem .sbi-fb-popup-cls{
    top: 12px;
    right: 16px;
}

[data-tltp-pos*="right"] .sbi-fb-onbrd-tltp-elem{
    right: 50px;
    left: unset;
}
[data-tltp-pos*="right"] .sbi-fb-onbrd-tltp-elem:before,
[data-tltp-pos*="right"] .sbi-fb-onbrd-tltp-elem:after{
    left: unset;
    right: 13%
}
[data-tltp-pos*="right"] .sbi-fb-onbrd-tltp-elem:after{
    margin-left: unset;
    margin-right: -10px;
}
.sbi-fd-lst-dimmed .sbi-fb-onbrd-tltp-elem{
    top: 100%;
}
.sbi-fb-cp-clpboard{
    width: 0px;
    height: 0px;
    position: absolute;
    left: -100000px;
}
.sbi-fb-copied{
    position: fixed;
    z-index: 9999999;
    background: #010101;
    color: #fff;
    line-height: 1em;
    font-size: 15px;
    font-weight: 500;
    padding: 10px 20px;
    opacity: 0;
    visibility: hidden;
    -webkit-transition: all .2s ease-in-out, opacity .4s ease-in-out;
    transition: all .2s ease-in-out, opacity .4s ease-in-out;
    left: 50%;
    bottom: -20px;
    -webkit-transform: translateX(-50%);
    transform: translateX(-50%);
}
.sbi-fb-copied[data-active="true"]{
    opacity: 1;
    visibility: visible;
    bottom: 20px;
}

/*
****
	customizer Screens
****
*/
.sb-customizer-ctn{
    /*margin-top: 52px;*/
    min-height: 100vh;
}
.sb-customizer-sidebar{
    position: fixed;
    z-index: 100;
    width: 375px;
    box-shadow: 4px 0px 14px rgba(0, 0, 0,  .05), 1px 0px 4px rgba(0, 0, 0,  .1);
    background: #fff;
    left: 160px;
    top: 96px;
    overflow: auto;
    bottom: 0px;
    padding-bottom: 50px;
}
.jetpack-masterbar .sb-customizer-sidebar {
  left: 272px;
}
body.folded .sb-customizer-sidebar{
    left: 36px;
}

.sb-customizer-sidebar-tab-ctn{
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 24px;
}
.sb-customizer-sidebar-tab{
    width: 50%;
    height: 56px;
    background: #F3F4F5;
    border-bottom: 2px solid #F3F4F5;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
}
.sb-customizer-sidebar-tab[data-active="true"]{
    border-color: #0096CC;
}
.sb-customizer-sidebar-tab:hover {
    background: #fff!important;
    color: #141B38!important;
}
.sb-customizer-sidebar-sec-el{
    height: 52px;
    cursor: pointer;
    display: flex;
    align-items: center;
    color: #141B38;
    border-bottom: 1px solid #DCDDE1;
    -webkit-transition: background .2s ease-in-out;
    transition: background .2s ease-in-out;
    padding: 0 20px;
}
.sb-customizer-sidebar-sec-el:hover{
    background: #F3F4F5;
    border-bottom: 1px solid #F3F4F5;
}
.sb-customizer-sidebar-sec-el .sb-customizer-chevron svg{
    position: absolute;
    right: 22px;
    top: 22px;
}
.sb-customizer-sidebar-sec-el-icon{
    margin-right: 15px;
    width: 30px;
    height: 30px;
    display: flex;
    justify-content: center;
    align-items: center;
}
.sb-customizer-sidebar-sec-el-icon svg{
    width: 20px;
    float: left;
    fill: currentColor;
}
.sb-customizer-sidebar-sec-elhead{
    height: 50px;
    padding: 0 20px;
    display: flex;
    align-items: center;
    margin-top: 30px;
    font-style: normal;
    font-weight: bold;
    font-size: 12px;
    line-height: 160%;
    letter-spacing: 0.05em;
    text-transform: uppercase;
    color: #8C8F9A;
}
.sb-customizer-sidebar-header{
    padding: 20px 20px;
    border-bottom: 1px solid #DCDDE1;
}
.sb-customizer-sidebar-header[data-separator="none"]{
    border-bottom: 0px!important;
    padding-bottom: 0px;
}

.sb-customizer-sidebar-breadcrumb{
    margin-bottom: 15px;
}
.sb-customizer-sidebar-sec1 a:hover{
    text-decoration: underline;
    cursor: pointer;
}

.sb-breadcrumb-pro-label{
    color: #fff !important;
    display: inline-block;
    background: var(--cl-orange);
    padding: 5px 8px;
    font-size: 11px;
    line-height: 1em !important;
    border-radius: 4px;
    -webkit-transform: translateX(5px) translateY(-4px);
    transform: translateX(8px) translateY(-2px);
}
.sb-control-elem-label .sb-breadcrumb-pro-label{
     -webkit-transform: translateX(5px) translateY(0px);
    transform: translateX(5px) translateY(0px);
}
.sb-customizer-sidebar-breadcrumb a, .sb-customizer-sidebar-breadcrumb span{
    display: inline-block;
    position: relative;
    cursor: pointer;
    color: #434960;
    text-transform: uppercase;
    font-size: 10px;
    line-height: 160%;
    letter-spacing: 0.08em;
    padding: 0 17px;
    height: 20px;
    font-weight: 600;
}
.sb-customizer-sidebar-breadcrumb span{
    cursor: text;
}
.sb-customizer-sidebar-breadcrumb svg {
    position: absolute;
    left: 4px;
    top: 6px;

}


.sb-customizer-sidebar-header strong{
    font-size: 26px;
    color: #141B38;
    display: block;
    margin-bottom: 10px;
    line-height: 1.1em;
}
.sb-customizer-sidebar-header span{
    line-height: 1em;
    color: #434960;
}
.sb-customizer-sidebar-intro {
    display: block;
    padding: 5px 0 10px 0;
    line-height: 1.7 !important;
}
.sb-customizer-ctrl-link{
    text-decoration: underline!important;
}
/*
Controls Style
*/
.sb-control-label[data-title="true"]{
    font-weight: 600;
}

.sb-control-elem-ctn{
    display: flex;
    color: #141B38;
    padding: 20px 20px;
}
.sb-control-elem-ctn[data-stacked="true"]{
    padding: 5px 20px;
}
.sb-control-elem-ctn[data-type="heading"]{
    padding-bottom: 0;
}
.sb-control-elem-overlay{
	width: 100%!important;
	height: 100%!important;
	position: absolute!important;
	left: 0!important;
	top: 0!important;
	z-index: 5!important;
    background: rgba(255,255,255,0.4)!important;
}
#sbi-builder-app .sb-control-elem-ctn[data-type="heading"] .sb-small-p{
    font-weight: 600;
}
#sbi-builder-app .sb-control-elem-ctn[data-type="heading"] .sb-control-elem-description{
    font-style: normal;
    font-weight: normal;
    font-size: 12px;
    line-height: 150%;
    color: #434960;
}
#sbi-builder-app  .sb-control-elem-ctn[data-type="heading"] .sb-control-elem-label {
    margin-bottom: 0;
}
.sb-control-elem-heading svg {
    margin-left: 8px;
}
.sb-control-elem-ctn[data-separator="top"],.sb-control-elem-ctn[data-separator="both"]{
    border-top: 1px solid #DCDDE1;
}
.sb-control-elem-ctn[data-separator="bottom"],.sb-control-elem-ctn[data-separator="both"]{
    border-bottom: 1px solid #DCDDE1;
}
.sb-control-elem-ctn[data-type="separator"]{
    padding: 0;
}
.sb-control-elem-separator{
    height: 2px;
    border-top: 1px solid #DCDDE1;
}

.sb-control-elem-ctn[data-reverse="true"]{
    flex-direction: row-reverse;
}
.sb-control-elem-ctn svg{
    fill: currentColor;
}
.sb-control-elem-output{
    padding-left: 20px;
}
.sb-control-elem-ctn[data-reverse="true"] .sb-control-elem-output{
    padding-right: 20px;
    padding-left: 0px;
}
.sb-control-elem-ctn[data-layout="block"]{
    display: block;
}
.sb-control-elem-ctn[data-layout="block"] .sb-control-elem-label{
    width: 100%;
    float: left;
    margin-bottom: 8px;
}
.sb-control-elem-ctn[data-layout="block"] .sb-control-elem-output{
    padding: 0px;
}
.sb-control-elem-ctn[data-layout="block"] input[type="number"] {
    height: 36px;
    background: #FFFFFF;
    border-radius: 1px 0 0 1px !important;
    /*border-right-color: #F3F4F5 !important;*/
}
.sb-control-elem-ctn[data-layout="block"] [data-contains-suffix="true"] input[type="number"]{
    border-right-color: #F3F4F5 !important;
}
.sb-control-elem-ctn[data-child="true"]{
    padding-left: 70px;
}

.sb-control-elem-ctn[data-layout="half"]{
    align-items: center;
    justify-content: flex-start;
}
.sb-control-elem-ctn[data-layout="half"][data-switcher-top="true"]{
    align-items: flex-start!important;
}
.sb-control-elem-ctn[data-layout="half"][data-switcher-top="true"] .sb-control-switcher-ctn{
    margin-top: 5px;
}
.sb-control-elem-ctn[data-layout="half"] > div{
    width: 100%;
}
.sb-control-elem-ctn[data-type="switcher"][data-reverse="true"][data-layout="half"] > div{
    width: unset;
}
.sb-control-elem-ctn[data-layout="half"][data-reverse="true"]{
    justify-content: flex-end;
}
[data-type="switcher"][data-reverse="true"][data-layout="half"] .sb-control-elem-output{
    padding-right: 5px;

}
.sb-control-elem-label-title{
    display: flex;
    align-items: center;
}
.sb-control-elem-description{
    color: #434960;
    font-size: 13px;
    float: left;
    margin-top: 4px;
}
.sb-control-elem-description a{
    cursor: pointer;
}
.sb-control-elem-ctn[data-heading="strong"] .sb-control-elem-heading{
    font-weight: 500!important;
}
.sb-control-elem-heading[data-underline="true"]{
    text-decoration: underline;
}
.sb-control-elem-icon{
    display: flex;
    justify-content: center;
    align-items: center;
}
.sb-control-elem-icon svg{
    width: 16px;
    float: left;
    margin-right: 10px;
}
.sb-control-elem-tltp{
    margin-left: 10px;
    position: relative;
}
.sb-control-elem-tltp-icon{
    float: left;
    cursor: pointer;
}
.sb-control-elem-tltp-icon svg{
    width: 14px;
    float: left;
}

/*Switcher Control*/
.sb-control-switcher-ctn{
    cursor: pointer;
    display: flex;
    align-items: center;
    float: left;
}
.sb-control-switcher{
    width: 36px;
    height: 18px;
    border-radius: 25px;
    background: #D0D1D7;
    position: relative;
    margin-right: 10px;
}

.sb-control-switcher:before{
    content: '';
    position: absolute;
    height: 14px;
    width: 14px;
    left: 3px;
    top: 2px;
    border-radius: 25px;
    background: #fff;
    box-shadow: 0px 1px 2px rgba(0, 0, 0, 0.25);
    -webkit-transition: all .2s ease-in-out;
    transition: all .2s ease-in-out;
}
.sb-control-switcher-ctn[data-active="true"] .sb-control-switcher{
    background: var(--customizer-blue);
}
.sb-control-switcher-ctn[data-active="true"] .sb-control-switcher:before{
    left: 19px;
}

/*Toggle SINGLE Control*/
.sb-control-toggle-elm{
    display: flex;
    align-items: center;
    cursor: pointer;
    height: 46px;
    padding: 0 15px;
    border-right: 1px solid #E8E8EB;
    border-left: 1px solid #E8E8EB;
    border-bottom: 1px solid #E8E8EB;
}
.sb-control-toggle-elm:first-child {
    border-top: 1px solid #E8E8EB;
}
.sb-control-toggle-extension-cover{
    width: 100%;
    height: 100%;
    position: absolute;
    left: 0px;
    top: 0;
    cursor: pointer;
    z-index: 4;

}

.sb-control-toggle-deco{
    width: 16px;
    height: 16px;
    margin-right: 13px;
    box-sizing: border-box;
    border-radius: 50%;
    border: 2px solid #d0d1d7;
}
.sb-control-toggle-icon{
    display: flex;
    justify-content: center;
    align-items: center;
    margin-right: 13px;
}
.sb-control-toggle-icon svg{
    width: 16px;
    float: left;
    fill: #141B38;
}
.sb-control-toggle-elm .sb-control-label span {
    display: flex;
    align-items: center;
    gap: 10px;
}
.sb-control-toggle-elm[data-active="true"]{
    background: #F7FDFF;
    border-radius: 2px;
    border: 1px solid var(--customizer-blue);
}
.sb-control-toggle-elm[data-active="true"]{
    background: #F7FDFF;
    border-radius: 2px;
    border: 1px solid var(--customizer-blue);
}
.sb-control-toggle-elm:hover:not(.sb-control-toggle-elm[data-disabled="true"]) .sb-control-toggle-deco,
.sb-control-toggle-elm[data-active="true"]:not(.sb-control-toggle-elm[data-disabled="true"]):hover .sb-control-toggle-deco{
    border: 4px solid var(--customizer-blue);
    background: #fff;
}
.sb-control-toggle-elm[data-active="true"] .sb-control-toggle-deco{
    border: 6px solid var(--customizer-blue);
    background: #fff;
}

/*Toggle SET Control*/
.sb-control-toggle-set-ctn .sb-control-toggle-elm[data-active="true"]{
    border-radius: 0px;
}

/*Toggle Button Control*/
.sb-control-togglebutton-ctn{
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 3px;
    background: #f3f4f5;
    border-radius: 4px;
    border: 1px solid #eee;
    height: 40px;
}
.sb-control-togglebutton-elm{
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    height: 33px;
    cursor: pointer;
    font-weight: 600;
    border-radius: 4px;
    text-transform: capitalize;
    line-height: 1em;
}
.sb-control-togglebutton-elm[data-active="true"]{
    color: var(--dark-blue);
    background: #fff;
}
.sb-control-togglebutton-elm:hover{
    color: var(--dark-blue);
}

.sb-control-toggle-set-desc-ctn .sb-control-label{
    font-weight: 600;
}
.sb-control-toggle-set-desc-ctn .sb-control-toggle-elm{
    height: 82px;
}

.sb-control-toggle-set-desc-ctn .sb-control-toggle-deco{
    flex-grow: 0;
    flex-shrink: 0;
}
.sb-control-moderatiomode-selement .sb-control-elem-label-title{
    margin-bottom: 15px;
}
.sb-control-moderatiomode-selement{
    margin-bottom: 20px;
    padding: 20px 0px;
}

/*Input Control*/
.sb-control-input-ctn{
    display: flex;
}
.sb-control-input,.sb-control-input-textrea{
    height: 40px;
    background: #fff;
    border: 1px solid #D0D1D7!important;
    border-radius: unset!important;
    outline: unset!important;
    padding: 0 10px!important;
    line-height: 1em;
    margin: 0px;
}
.sb-control-input-textrea{
    padding: 10px!important;
    height: 120px;
    line-height: 1.5em;
}
.sb-control-input:hover{
    color: inherit!important;
}
.sb-control-input:focus,.sb-control-input-textrea:focus{
    border: 1px solid #8C8F9A!important;
    border-radius: unset!important;
    box-shadow: unset!important;
    outline: unset!important;
}
.sb-control-input[disabled],.sb-control-input-textrea[disabled]{
    background-color: #F3F4F5;
    border: 1px solid #E8E8EB;
}
.sb-control-input-info{
    display: flex;
    justify-content: center;
    align-items: center;
    background: #F3F4F5;
    padding: 0 8px;
    font-weight: normal;
    font-size: 14px;
    line-height: 160%;
    color: #434960;
    border: 1px solid #D0D1D7;
    border-left: none;
    border-radius: 0 1px 1px 0 !important;
}

/*ImageChooser Control*/
.sb-control-imagechooser-ctn{
    display: flex;
    background: #F3F4F5;
    border: 1px solid #ccc!important;
    height: 40px;
}

.sb-control-imagechooser-input,
.sb-control-imagechooser-input:focus{
    outline: none!important;
    border: none!important;
    box-shadow: none!important;
    background: none!important;
    height: 40px;
}
.sb-control-imagechooser-btn svg{
    width: 18px;
    margin-right: 5px;
    float: left;
}
.sb-control-imagechooser-btn{
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 0 10px;
    cursor: pointer;
    border-left: 1px solid #ccc!important;
    font-weight: 500;
    color: #141B38;
}
.sb-control-imagechooser-clear{
    position: absolute;
    right: 4px;
    top: 10px;
}
.sb-control-imagechooser-clear-icon{
    position: relative;
    width: 20px;
    height: 20px;
    color: #141B38;
    cursor: pointer;
    opacity: 0.5;
}
.sb-control-imagechooser-clear-icon:hover{
    opacity: 1;
}
.sb-control-imagechooser-clear-icon:before,
.sb-control-imagechooser-clear-icon:after{
    content: '';
    position: absolute;
    width: 16px;
    height: 2px;
    background: currentColor;
    left: 2px;
    top: 9px;
    -webkit-transform:rotate(45deg);
    transform:rotate(45deg);
}
.sb-control-imagechooser-clear-icon:after{
       -webkit-transform:rotate(-45deg);
    transform:rotate(-45deg);
}
.sb-control-imagechooser-padding{
    padding-right: 31px!important;
}

/*CheckBox Control*/
.sb-control-checkbox-ctn{
    cursor: pointer;
    display: flex;
}
[data-disabled="true"] .sb-control-checkbox{
    z-index: -1!important;
}
.sb-control-checkbox{
    width: 18px;
    height: 18px;
    border-radius: 2px;
    float: left;
    border: 2px solid #D0D1D7;
    box-sizing: border-box;
    position: relative;
    margin-right: 10px;
    flex: none;
}
.sb-control-checkbox-ctn > div{
	z-index: 3;
}
.sb-control-checkbox-ctn:hover .sb-control-checkbox-hover{
	opacity: 1;
}
.sb-control-checkbox-hover{
	position: absolute;
	left: -20px;
	top: 0px;
	width: calc(100% + 40px);
	height: 100%;
	background:#F3F4F5;
	z-index: 1;
	opacity: 0;
}

.sb-control-checkbox{
	z-index: 5!important;
}


.sb-control-checkbox[data-active="true"]{
    background: var(--customizer-blue);
    border-color: var(--customizer-blue);
    color: #fff;
}
.sb-control-checkbox[data-active="true"]:before{
    content: '';
    position: absolute;
    width: 8px;
    height: 4px;
    top: 2px;
    left: 2px;
    border-left: 2px solid currentColor;
    border-bottom: 2px solid currentColor;
    -webkit-transform: rotate(-45deg);
    transform: rotate(-45deg);
}


/*Action Button Control*/
.sb-control-action-button{
    height: 38px;
    cursor: pointer;
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    background: #F3F4F5;
    border-radius: 2px;
    border: 1px solid #DCDDE1;
    position: relative;
    line-height: 1em;
    margin-top: 7px;
}

.sb-control-action-button div{
    float: left;
    width: 20px;
    height: 20px;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-right: 8px;
}
.sb-control-action-button svg{
    width: 17px;
}

/* WPColorPicker Control Redesign */
.sb-control-colorpicker-ctn .wp-picker-default{
    display: none!important;
}
.sb-control-colorpicker-ctn .minicolors-theme-default.minicolors{
    width: 100%;
}

.sb-control-colorpicker-ctn  .minicolors-theme-default.minicolors-position-right .minicolors-swatch {
    /*
    left: auto;
    right: 6px;
    top: 6px;
    width: 26px;
    height: 26px;
    background: #F9F9FA;
    border: 0px;
    */

    left: auto;
    right: 1px;
    top: 1px;
    width: 35px;
    height: 35px;
    background: #F9F9FA;
    border: 0px;
}
.minicolors-swatch-color{
	box-shadow: none;
}
.sb-control-colorpicker-ctn .minicolors-input,.sb-control-colorpicker-ctn .minicolors-input:focus{
    height: 37px;
    width: 100%!important;
    background: #fff;
    border: 1px solid #D0D1D7!important;
    border-radius: unset!important;
    outline: unset!important;
    box-shadow: unset!important;
    padding: 0 10px!important;
    line-height: 1em;
    margin: 0px;
}
.sb-control-colorpicker-ctn .minicolors-input:focus{
    border: 1px solid #0096CC!important;
}

[data-type="colorpicker"] .minicolors-input{
	font-size: 13px;
}
[data-type="colorpicker"] .minicolors-input{
    font-style: normal;
    font-weight: normal;
    font-size: 12px;
    line-height: 150%;
    color: #434960;
}

.sb-control-colorpicker-btn{
    height: 38px;
    display: flex;
    justify-content: center;
    align-items: center;
    border: 1px solid #DCDDE1;
    border-left: 0px;
    padding: 0 10px;
    background: #F3F4F5;
    font-style: normal;
    font-weight: 600;
    font-size: 12px;
    line-height: 160%;
    color: #141B38;
    cursor: pointer;
    box-sizing: border-box;
}
.sb-control-colorpicker-ctn[data-picker-style="reset"] .minicolors-input,
.sb-control-coloroverride-ctn{
    background: #F3F4F5!important;
    border: 1px solid #DCDDE1!important;
}
.sb-control-colorpicker-ctn[data-picker-style="reset"] .minicolors-theme-default.minicolors-position-right .minicolors-swatch,
.sb-control-coloroverride-ctn .sb-control-coloroverride-swatch{
    top: 8px;
    width: 22px;
    height: 22px;
}
.sb-control-coloroverride-ctn .sb-control-coloroverride-swatch{
	position: absolute;
    right: 10px;
}
.sb-control-coloroverride-ctn .sb-control-colorpicker-btn{
	margin-left: auto;
	border: 0px!important;
	border-left: 1px solid #DCDDE1!important;
}
.sb-control-coloroverride-ctn .sb-control-coloroverride-content{
    width: 100%;
    display: flex;
    align-items: center;
    position: relative;
}
.sb-control-coloroverride-ctn .sb-control-coloroverride-txt{
    padding-left: 9px;
    box-sizing: border-box;
}

/*
	Customizer Preview
*/
.sb-customizer-preview{
    min-height: 100px;
    width: calc(100% - 375px);
    float: right;
    box-sizing: border-box;
    padding: 0 20px;
    display: flex;
    justify-content: center;
    margin-top: 64px;
    position: relative;
    flex-wrap: wrap;
}
.sb-customizer-preview .sbi-admin-notices{
    margin-top: 20px;
    width: 100%;
}
.sb-customizer-preview .license-details-modal .sbi-modal-content {
    max-height: none;
    height: auto;
}
.sb-customizer-preview .sbi-sb-modal-body .sb-why-renew-list:nth-child(3) {
    margin-bottom: 0;
}
.sb-customizer-preview .sbi-sb-modal-body .sb-why-renew-list:last-child {
    margin-bottom: 0;
}
.sb-customizer-preview[data-preview-device="desktop"] .sb-preview-ctn{
    width: 100%;
    max-width: 1200px;
}
.sb-customizer-preview[data-preview-device="tablet"] .sb-preview-ctn{
    max-width: 100%;
    width: 800px;
}
.sb-customizer-preview[data-preview-device="mobile"] .sb-preview-ctn{
    max-width: 100%;
    width: 400px;
}
.sbi-preview-ctn {
    padding: 10px;
}

.sb-preview-top-chooser{
    padding: 18px 0 16px;
    display: flex;
    color: #434960;
    align-items: center;
}
.sb-preview-top-chooser strong{
    font-style: normal;
    font-weight: bold;
    font-size: 12px;
    line-height: 160%;
    letter-spacing: 0.05em;
    text-transform: uppercase;
    color: #434960;
    display: inline-flex;
}
.sbi-moderate-heading{
    margin-top: 21px;
}
.sb-preview-top-chooser strong > svg{
    margin-right: 10px;
    margin-left: 16px;
    fill: currentColor;
    width: 20px;
}

.sb-preview-chooser{
    height: 36px;
    background: #E8E8EB;
    margin-left: auto;
    padding: 0 2px;
    border-radius: 1px;
    display: flex;
    justify-content: center;
    align-items: center;
}
.sb-preview-chooser-btn,.sb-preview-chooser-btn:focus{
    width: 40px;
    height: 32px;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    float: left;
    border: 0;
    background: unset;
    outline: none;
}

.sb-preview-chooser-btn svg{
    width: 15px;
    fill: currentColor;
    float: left;
}
.sb-preview-chooser-btn.sb-mobilee svg{
    width: 9px;
    fill: currentColor;
    float: left;
}
.sb-preview-chooser-btn[data-active="true"],
.sb-preview-chooser-btn:hover{
    background: #fff!important;
    box-shadow: 0px 1px 1px rgba(0, 0, 0, 0.25);
    border-radius: 1px;
}

.sb-control-checkboxsection-header{
    width: 100%;
    margin-top: 25px;
    display: flex;
    align-items: center;
    text-transform: uppercase;
    font-size: 13px;
    padding-bottom: 15px;
    position: relative;
}
.sb-control-checkboxsection-header:before{
    content: '';
    position: absolute;
    left: -20px;
    width: calc(100% + 40px);
    height: 1px;
    background: #DCDDE1;
    bottom: 0px;
}
.sb-control-checkboxsection-name{
    display: flex;
    align-items: center;
}
.sb-control-checkboxsection-header svg{
    width: 20px;
    margin-right: 7px;
    fill: #434960;
    float: left;
}
.sb-control-checkboxsection-header > strong{
    margin-left: auto;
}
[data-type="checkboxsection"] .sb-control-checkbox-ctn{
    align-items: center;
    height: 50px;
}
[data-type="checkboxsection"] {
    padding: 0 20px !important;
}
[data-type="checkboxsection"] .sb-control-elem-label{
    display: none;
}
[data-type="checkboxsection"] strong{
    color: #434960
}
[data-type="checkboxsection"] [data-active="true"] strong{
    color: #141B38;
}
.sb-control-checkboxsection-btn{
    width: 21px;
    height: 21px;
    position: relative;
    cursor: pointer;
}
.sb-control-checkboxsection-btn:before{
    content: '';
    position: absolute;
    width: 7px;
    height: 7px;
    left: 5px;
    top: 7px;
    border-right: 2px solid #8C8F9A;
    border-bottom: 2px solid #8C8F9A;
    -webkit-transform: rotate(-45deg);
    transform: rotate(-45deg);
}

/*CheckBox List*/
[data-type="checkboxlist"] .sb-control-checkbox-ctn{
    margin-bottom: 10px!important;
}
[data-type="checkboxlist"] .sb-control-checkbox-ctn .sb-control-label{
font-style: normal;
    font-weight: normal;
    font-size: 14px;
    line-height: 160%;
}

/*Source Controls*/
.sb-control-sources-ctn .sbi-fb-srcs-item{
    box-sizing: border-box;
    position: relative;
    cursor: auto;
    display: block;
    height: auto;
    border: 1px solid #E7E7E9;
    min-height: 60px;
    overflow: auto;
    margin-top: 0px;
}
.sbi-fb-srcs-item-ins{
    cursor: pointer;
    display: flex;
    height: 62px;
    padding: 0 10px;
    position: relative;
}
.sb-control-src-icon{
    width: 20px;
    height: 20px;
    position: absolute;
    right: 10px;
    top: 10px;
    z-index: 2;
    cursor: pointer;
    display: flex;
    justify-content: center;
    align-items: center;
}
.sb-control-src-icon svg{
    width: 13px;
    float: left;
}
[data-expanded="true"] .sb-control-src-expand  svg,.sbi-fb-srcs-info {
    display: none
}
.sb-control-src-expand-chevron{
    width: 7px;
    height: 7px;
    border-left: 2px solid currentColor;
    border-top: 2px solid currentColor;
    -webkit-transform: rotate(45deg);
    transform: rotate(45deg);
    display: none;
}
[data-expanded="true"] .sb-control-src-expand-chevron,[data-expanded="true"] .sbi-fb-srcs-info {
    display: block;
}
.sb-control-src-remove svg{
    width: 11px;
    fill: var(--error-red);
}
.sb-control-sources-ctn[data-multifeed="true"] .sb-control-src-expand {
    right: 30px;
}
.sb-control-elem-output .sb-control-src-expand:hover {
    background: #F3F4F5;
    border-radius: 3px;
}
.sb-control-elem-output .sb-control-src-expand:hover path{
    fill: #111;
}

.sb-control-sources-ctn .sbi-fb-srcs-item .sbi-fb-srcs-item-name{
    font-size: 17px;
    line-height: 1em;
    margin-bottom: 3px;
}

.sbi-fb-srcs-info-item{
    display: flex;
    border-top: 1px solid #E7E7E9;
    box-sizing: border-box;
    width: 100%;
    float: left;
    padding: 8px 10px;
}
.sbi-fb-srcs-info-item:first-of-type{
    align-items: center;
}
.sbi-fb-srcs-info-item strong{
    font-size: 14px;
    width: 50px;
}
.sbi-fb-srcs-info-item span{
    font-size: 13px;
    line-height: 1.1em;
    color: #434960;
    font-weight: 400;
    display: inline-block;
    word-break: break-all;
    width: calc(100% - 80px);
    padding: 0 15px;
    box-sizing: border-box;
}
.sbi-fb-srcs-info-icon{
    width: 26px;
    height: 26px;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    margin-left: auto;
    border: 1px solid #D0D1D7;
    border-radius: 2px;
}
.sbi-fb-srcs-info-icon svg{
    width: 15px;
    float: left;
}
.sb-control-sources-ctn .sb-control-action-button{
    margin-top: 8px;
    margin-bottom: 16px;
}
.sb-control-sources-promo-ctn{
    padding: 16px 0;
}
.sb-control-sources-promo-ctn:before{
    content: '';
    position: absolute;
    height: 1px;
    width: calc(100% + 40px);
    left: -20px;
    top: 0px;
    background: #E7E7E9;
}
.sb-control-sources-promo-top{
    width: 100%;
    box-sizing: border-box;
    padding: 30px 20px;
    border: 1px solid #E8E8EB;
    float: left;
    background: #F9F9FA;
}
.sb-control-sources-promo-top > div{
    width: 100%;
    float: left;
    text-align: center;
    display: flex;
    justify-content: center;
    align-items: center;
    box-sizing: border-box;
    line-height: 1.6em;
}
.sb-btn-chevron{
    display: inline-block;
    width: 7px;
    height: 7px;
    border-right: 2px solid currentColor;
    border-top: 2px solid currentColor;
    -webkit-transform: rotate(45deg);
    transform: rotate(45deg);
}
.sb-control-sources-promo-text{
    font-size: 15px;
    font-weight: 600;
}
.sb-control-sources-promo-btn{
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 16px 20px;
    color: #fff !important;
    background: #0068A0;
    font-size: 15px;
}
.sb-control-sources-promo-btn div{
    margin-left: 10px;
}
.sb-control-sources-promo-icon{
    margin-bottom: 20px;
}

/*Loading Bar*/
.sb-loadingbar-ctn{
    position: absolute;
    height: 5px;
    width: 100%;
    left: 0px;
    bottom: 0px;
    background: rgba(227, 79, 14, 0.25);
    z-index: 999999999999999;
}
.sb-loadingbar-ctn:before, .sb-loadingbar-ctn:after{
    content: '';
    position: absolute;
    height: 5px;
    background: #FE544F;
    z-index: 9;
    top: 0;
}

.sb-loadingbar-ctn:before{
    -webkit-animation: sbi-loading-animation 4s infinite;
    animation: sbi-loading-animation 4s infinite;
}
.sb-loadingbar-ctn:after{
    -webkit-animation: sbi-loading-animation 4s 2s infinite;
    animation: sbi-loading-animation 4s 2s infinite;
}

@-webkit-keyframes sbi-loading-animation {
    from { left: -5%; width: 0%; }
    to { left: 130%; width: 50%;}
}
@keyframes sbi-loading-animation {
    from { left: -5%; width: 0%; }
    to { left: 130%; width: 50%;}
}

/*Notification Element*/
.sb-notification-ctn{
    position: fixed;
    bottom: -100px;
    left: 200px;
    z-index: 99999;
    background: #fff;
    display: flex;
    justify-content: center;
    align-items: center;
    border-left: 3px solid #fff;
    line-height: 1em;
    padding: 10px 20px;
    padding-left: 0px;
    border-radius: 4px;
    box-shadow: 0px 26.7377px 77.2886px rgba(0, 0, 0, 0.107828), 0px 14.2952px 41.3222px rgba(0, 0, 0, 0.0894161), 0px 8.01379px 23.1649px rgba(0, 0, 0, 0.075), 0px 4.25607px 12.3027px rgba(0, 0, 0, 0.0605839), 0px 1.77104px 5.11942px rgba(0, 0, 0, 0.0421718);

}
.sb-notification-ctn[data-active="hidden"]{
    -webkit-animation: sbi-notification-hide .5s forwards linear;
    animation: sbi-notification-hide .5s forwards linear;
}
.sb-notification-ctn[data-active="shown"]{
    -webkit-animation: sbi-notification-show .5s forwards linear;
    animation: sbi-notification-show .5s forwards linear;
}
@-webkit-keyframes sbi-notification-show { 0%{bottom: -100px;} 50%{bottom: 70px;} 70%{bottom: 60px;} 85%{bottom: 65px;} 100%{bottom: 50px;}}
@keyframes sbi-notification-show { 0%{bottom: -100px;} 50%{bottom: 70px;} 70%{bottom: 60px;} 85%{bottom: 65px;} 100%{bottom: 50px;}}

@-webkit-keyframes sbi-notification-hide {0%{bottom: 50px;}55%{bottom: 65px;}70%{bottom: 60px;}85%{bottom: 70px;}100%{bottom: -100px;}}
@keyframes sbi-notification-hide {0%{bottom: 50px;}55%{bottom: 65px;}70%{bottom: 60px;}85%{bottom: 70px;}100%{bottom: -100px;}}

.sb-notification-ctn[data-type="success"]{
    border-color: #59AB46;
}
.sb-notification-ctn[data-type="error"]{
    border-color: #D72C2C;
}
.sb-notification-ctn[data-type="message"]{
    border-color: #141B38;
}
.sb-notification-icon{
    width: 25px;
    height: 25px;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-left: 10px;
    margin-right: 15px;
}
.sb-notification-icon svg{
    width: 22px;
    height: 22px;
    float: left;
    fill: currentColor;
}

.sb-notification-ctn[data-type="success"] .sb-notification-icon{
    color: #59AB46;
}
.sb-notification-ctn[data-type="error"] .sb-notification-icon{
    color: #D72C2C;
}
.sb-notification-ctn[data-type="message"] .sb-notification-icon{
    color: #141B38;
}

.sb-notification-ctn span{
    font-size: 14px;
    color: #141B38;
    font-weight:500;
}

/* Onboarding */
.sb-onboarding-tooltip {
    display: none;
    position: absolute;
    min-height: auto;
    width: 432px;
    max-width: 100%;
    padding: 0;
    border-radius: 2px;
}
#sb-onboarding-tooltip-multiple-2,
#sb-onboarding-tooltip-multiple-3{
    width: 528px;
}
#sb-onboarding-tooltip-single-2 {
    width: 402px;
}
.sb-onboarding-active .sb-onboarding-highlight .sbi-fb-btn.sbi-fb-btn-new,
.sb-onboarding-active .sb-positioning-wrap.sb-onboarding-highlight,
.sb-onboarding-active .sbi-fd-lst-bigctn .sbi-table-wrap.sb-onboarding-highlight,
.sb-onboarding-active .sbi-fb-lgc-ctn.sb-onboarding-highlight,
.sb-onboarding-active .sbi-fb-lgc-ctn .sbi-legacy-table-wrap.sb-onboarding-highlight{
    position: relative;
    z-index: 100000;
}
.sb-onboarding-active .sbi-fd-legacy-feed-toggle {
    display: none;
}
.sbi-legacy-table-wrap.sb-onboarding-highlight {
    clear: both;
}
.sb-onboarding-tooltip-1 {
    top: 50px;
    left: 8px;
}
#sb-onboarding-tooltip-single-2 {
    bottom: -179px;
    top: auto;
    left: 68%;
    margin-left: -201px;
}
#sb-onboarding-tooltip-multiple-2,
#sb-onboarding-tooltip-multiple-3{
    top: -200px;
    left: 20%;
}
#sb-onboarding-tooltip-multiple-3 {
    top: -210px;
}
.sb-onboarding-tooltip  .sbi-fb-wrapper {
    display: flex;
    justify-content: flex-end;
}
.sb-positioning-wrap {
    width: 432px;
}
.sb-onboarding-tooltip .sbi-fb-popup-cls {
    position:absolute;
    width: 12px;
    height: 12px;
    top: 12px;
    right: 12px;
}
.sb-onboarding-tooltip .sbi-fb-popup-cls svg {
    width: 12px;
    height: 12px;
}
.sb-onboarding-tooltip h3 {
    font-size: 16px;
    color: #141B38;
    line-height: 160%;
    font-weight: 600;
    margin: 0;
}
.sb-onboarding-step {
    font-style: normal;
    font-weight: normal;
    font-size: 12px;
    line-height: 160%;
    color: #434960;
    margin: 2px 0 20px;
    display: block;
}
.sbi-onboarding-next,
.sbi-onboarding-previous{
    color: #353A41;
    background: #F3F4F5;
    border: 1px solid #DCDDE1;
    margin-left: 10px;
}
.sb-onboarding-tooltip .sbi-fb-hd-btn {
    margin-right: 0;
}
.sb-onboarding-tooltip .sbi-fb-hd-btn i {
    margin: 0;
}
.sbi-onboarding-finish{
    margin-left: 10px;
    padding: 0 32px;
}
.sb-onboarding-tooltip .sbi-fb-hd-btn[data-active="false"] {
    background-color: #e8e8eb;
    color: #8c8f99;
}
.sb-onboarding-tooltip .sbi-fb-hd-btn[data-active="false"]:hover {
    cursor: default;
}
.sb-step-counter-wrap span {
    font-style: normal;
    font-weight: bold;
    font-size: 12px;
    line-height: 160%;
    letter-spacing: 0.05em;
    text-transform: uppercase;
    color: #141B38;
}
.sb-onboarding-tooltip .sb-pointer {
    position: absolute;
    left: 50px;
    top: -14px;
}
.sb-onboarding-tooltip .sb-pointer.sb-bottom-pointer {
    top: auto;;
    bottom: -14px;
}
#sb-onboarding-tooltip-single-2 .sb-pointer {
    left: 193px;
}
#sb-onboarding-tooltip-multiple-2:before,
#sb-onboarding-tooltip-multiple-3:before{
    bottom: -8px;
}
.sb-onboarding-top-row {
    padding: 20px 44px 0 24px;
}
.sb-onboarding-bottom-row {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    padding: 8px 16px 12px 24px;
}


/*Vue Color Picker*/
.sb-control-colorpicker-ctn .vc-sketch{
	box-shadow: none!important;
}
.sb-control-colorpicker-popup{
    box-shadow: 0px 0px 10px rgba(0,0,0,0.2);
    position: absolute;
    z-index: 99;
    top: 100%;
	background: #fff;
	right: 0px;
}
.sb-control-colorpicker-popup .sb-colorpicker-reset-btn{
width: calc(100% - 20px);
	margin-left: 10px;
	margin-bottom: 15px;
}
.sb-control-colorpicker-swatch{
	width: 38px;
    height: 38px;
    position: absolute;
    right: 1px;
    top: 1px;
    background: #f7f7f7;
}
.sb-control-colorpicker-ctn .sb-control-input{
	width: 100%;
}

@media (min-width: 768px) and (max-width: 1023px) {

    .sbi-csz-header-insider .sb-button-standard {
        padding: 10px 14px 10px 30px;
    }
}
@media (min-width: 1024px) and (max-width: 1200px) {
    .sbi-fb-wlcm-inf-3 {
        padding-left: 120px;
    }
}
@media (max-width: 767px) {
    .sbi-fd-lst-thtf th:nth-child(3),
    .sbi-fd-lst-thtf th:nth-child(4),
    .sbi-fd-lst-tbody tr td:nth-child(3),
    .sbi-fd-lst-tbody tr td:nth-child(4),
    .sbi-fd-lst-thtf tr td:nth-child(3),
    .sbi-fd-lst-thtf tr td:nth-child(4) {
        display: none;
    }
    .sbi-fd-lst-thtf th:last-child,
    .sbi-fd-lst-thtf tr td:last-child {
        padding-right: 15px;
        text-align: right;
    }
    .sbi-fd-lst-tbody tr td.sbi-fd-lst-actions .sb-flex-center{
        justify-content: flex-end;
        padding-right: 8px;
    }
    .sbi-fb-full-wrapper {
        padding: 70px 20px 0 20px;
    }
    .sbi-fb-header {
        padding: 0px 20px;
    }
    .sbi-bld-ft-content {
        flex-wrap: wrap;
    }
    #sb-footer-banner .sbi-bld-ft-img {
        width: 100%;
        height: 140px;
    }
    #sb-footer-banner .sbi-bld-ft-img img {
        height: 100%;
        width: auto;
    }
    #sb-footer-banner .sbi-bld-ft-txt {
        justify-content: center;
        align-items: center;
        width: 100%;
        margin-right: 3%;
        padding: 20px;
    }
    .sbi-bld-ft-action {
        width: 100%;
        padding: 0 20px 20px;
    }
    .sbi-bld-footer > div {
        margin-bottom: 60px;
    }

    /* single feed page */
    .sbi-csz-header-insider {
        flex-wrap: wrap;
        height: auto;
        width: 100%;
    }
    .sbi-fb-header.sbi-csz-header {
        height: 110px;
    }
    .sbi-csz-header.sbi-fb-header {
        padding-bottom: 6px;
    }
    .sbi-csz-header .sbi-csz-hd-actions {
        margin-top: 10px;
    }
    .sb-customizer-preview {
        display: none;
    }
    .sb-customizer-ctn .sb-customizer-sidebar {
        position: initial;
        margin-top: 110px;
        width: 100%;
    }
    .sb-notification-ctn {
        left: 20px;
    }
    .sbi-fb-header .sb-button-standard{
        padding: 10px 10px 10px 31px;
    }
    .sbi-fb-embed-ctn.sb-fs-boss.sbi-fb-center-boss .sbi-fb-popup-inside {
        top: 35px;
    }
    .sbi-fb-embed-btns-ctn {
        grid-template-columns: 100%;
    }
    .sbi-fb-embed-btns-ctn .sbi-fb-embed-btn {
        margin-bottom: 10px;
    }
    .sbi-fb-embed-ctn.sb-fs-boss.sbi-fb-center-boss .sbi-fb-popup-inside .sbi-fb-embed-step-1-top {
        margin-bottom: 10px;
    }
    .sbi-fb-embed-ctn.sb-fs-boss.sbi-fb-center-boss .sbi-fb-popup-inside .sbi-fb-embed-input-ctn {
        flex-wrap: wrap;
    }
    .sbi-fb-embed-input-ctn input, .sbi-fb-embed-input-ctn input[type="text"] {
        width: 100%;
        border-right: 1px solid #D0D1D7!important;
    }
    .sbi-fb-embed-input-ctn .sbi-fb-hd-btn {
        width: 32%;
        max-width: 120px;
        margin-top: 10px !important;
        padding: 8px;
    }
    .sb-fs-boss.sbi-fb-center-boss {
        z-index: 100001 !important;
    }
    #sb-footer-banner .sbi-bld-ft-txt {
        flex-wrap: wrap;
    }
    #sbi-builder-app #sb-footer-banner h3,
    #sbi-builder-app #sb-footer-banner .sb-small-p {
        width: 100%;
    }
    #sbi-builder-app #sb-footer-banner h3 {
        margin-bottom: 10px;
    }
    .sbi-fb-srcslist-ctn {
        grid-template-columns: 100%
    }
    .sbi-fb-mr-fd-img {
        width: 100%;
        margin-right: 0;
    }
    .sbi-fb-mr-fd-img svg {
        max-width: 100%;
    }
    .sbi-fd-lst-tbody tr td:nth-child(2) {
        width: 50%;
    }

    .sbi-fb-wlcm-inf-1,
    .sbi-fb-wlcm-inf-2,
    .sbi-fb-wlcm-inf-3 {
        padding-left: 0;
    }

    .sbi-fb-wlcm-inf-3 .sbi-fb-inf-img,
    .sbi-fb-wlcm-inf-2 .sbi-fb-inf-img,
    .sbi-fb-wlcm-inf-1 .sbi-fb-inf-svg {
        display: none;
    }
    .sbi-fb-wlcm-inf-3 .sbi-fb-inf-cnt {
        justify-content: flex-start;
    }
    .sbi-fb-wlcm-inf-1 .sbi-fb-inf-cnt {
        width: calc(100% - 53px);
    }
    .sbi-fb-wlcm-inf-2 .sbi-fb-inf-cnt,
    .sbi-fb-wlcm-inf-3 .sbi-fb-inf-cnt {
        width: 100%;
    }

    .sbi-fb-wlcm-inf-1 .sbi-fb-inf-cnt .sbi-fb-inf-txt{
        width: 80%;
    }

    .sbi-fb-wlcm-inf-2 .sbi-fb-inf-cnt {
        margin-bottom: 24px;
    }

    .sbi-fb-mr-fd-list {
        grid-template-columns: 1fr 1fr;
    }
}

@media (min-width: 768px) and (max-width: 1023px) {
    .sbi-fb-wlcm-inf-3 {
        padding-left: 7px;
    }
    .sbi-fb-wlcm-inf-2 {
        padding-left: 52px;
    }
    .sbi-fb-wlcm-inf-1 {
        padding-left: 85px;
    }
    .sbi-fb-wlcm-inf-1 .sbi-fb-inf-svg {
        display: none;
    }
}

@media (min-width: 768px) and (max-width: 1366px) {
    .sbi-fb-mr-fd-list {
        grid-template-columns: 1fr 1fr 1fr;
    }
}


/*
    Multiple Sources Sections
*/

#sbi-multiple-sources-ctn .sbi-fb-slctsrc-content{
    padding: 25px 30px;
    border-bottom: 1px solid #E8E9EA;
}
.sbi-feedtype-section{
    padding: 30px;
    border-bottom: 1px solid #E8E9EA;
}
.sbi-feedtype-sec-icon-heading{
    display: flex;
    align-self: center;
}
.sbi-feedtype-icon-wrap svg{
    width: 16px!important;
    height: 16px!important;
    float: left;
    fill: #0096CC;
    margin-right: 6px;
}

.sbi-feedtype-sec-icon-heading span{
    color: #141B38;
    font-weight:600;
    font-size: 18px;
}
.sbi-feedtype-sec-icon-heading a{
    font-size: 12px;
    display: inline-block;
    margin-top: 2px;
    margin-left: 6px;
}
.sbi-feedtype-sec-desc{
    font-size: 12px;
}

#sbi-multiple-sources-ctn .sbi-fb-hd-btn svg{
    fill: #141B38;
}
.sbi-addsource-type-btn{
    display: flex;
    justify-content: center;
    align-self: center;
    padding: 12px 20px;
    color: #8C8F9A;
    cursor: pointer;
    font-size: 14px;
    font-weight: 600;
}
.sbi-addsource-type-btn svg{
    margin-right: 12px;
    margin-top: 2px;
    fill: #8C8F9A;
}
.sbi-addsource-type-btn:hover,
.sbi-addsource-type-btn:hover svg{
    color: #434960;
    fill: #434960;
}

.sbi-feedtype-section .sbi-fd-lst-btn-delete{
    height: 32px;
    width: 36px;
    position: absolute;
    right: 20px;
    top: 20px;
    z-index: 2;
}
.sbi-feedtype-icon-wrap {
    position: absolute;
    top: 0;
    left: 0;
}
.sbi-feedtype-sec-wrap {
    margin-left: 28px;
}
.sbi-feedtype-section .sbi-fd-lst-btn-delete{
    border-color: #D8DADD;
}
.sbi-fb-type-el[data-checked="true"]{
    border: 1px solid #D8D8D8
}

.sbi-fb-type-el[data-checked="true"]:before{
    content: ''!important;
    position: absolute!important;
    height: 100%!important;
    width: 100%!important;
    left: 0!important;
    top: 0!important;
    background: #f1f1f1!important;
    opacity: .35!important;
    cursor: default!important;
}
.sbi-fb-type-el[data-checked="true"]:after{
    display: none!important;
}
.sbi-fb-type-el[data-checked="true"] .sbi-fb-type-el-info *{
    color: #8C8F9A!important;
}
.sbi-fb-type-el-info .sb-control-elem-tltp,
.sbi-fb-type-el-info .sb-control-elem-tltp-icon{
    display: inline-block;
    color: inherit;
    fill: currentColor;
    margin: 0px;
}
.sbi-fb-feedtypes-pp-ctn .sbi-fb-type-el-info{
    padding: 0 45px 20px!important;
}

.sbi-fb-feedtypes-pp-ctn .sbi-fb-types-list{
    grid-template-columns: 24% 24% 24% 24%;
}
#sbi-builder-app .sbi-fb-feedtypes-pp-ctn .sbi-fb-types{
    padding: 23px 30px 10px!important;;
    padding-bottom: 0px!important;
}
.sbi-fb-feedtypes-popup .sb-button-no-border {
    position: absolute;
    z-index: 99;
    top: 20px;
    left: 33px;
    font-weight: bold;
    font-size: 12px;
    line-height: 160%;

    letter-spacing: 0.05em;
    text-transform: uppercase;
    cursor: pointer;
}
.sbi-fb-feedtypes-popup .sb-button-no-border svg {
    margin-right: 9px;
}
.sbi-fb-addsourtype-ctn{
    margin-bottom: 30px;
    padding: 0px 30px!important;
}
.sbi-fb-addsourtype-ctn .sbi-fb-source-btn{
    margin-top: 0px;
}
.sbi-fb-feedtypes-pp-ctn h4{
    margin-bottom: 20px;
}
.sbi-fb-sourcelist-pp-ctn .sbi-fb-source-top{
    padding: 22px 19px 0px;
}

.sbi-fb-sourcelist-pp-ctn .sbi-fb-sourcelist-pp{
        float: left;
    width: 100%;
    box-sizing: border-box;
    padding: 0 20px;
}
.sbi-fb-sourcelist-pp-ctn .sbi-fb-addsourtype-ctn{
    padding: 0px 20px!important;
}

.sbi-fb-sourcelist-pp-ctn .sbi-fb-srcs-desc{
    margin-bottom: 20px;
}
.sbi-fb-sourcelist-pp-ctn .sbi-fb-source-pp-customizer .sbi-fb-srcslist-ctn{
    grid-template-columns: 49% 49%;
    min-height: 80px;
    overflow: auto;
}

.sbi-selected-sources-ctn .sbi-fb-hd-btn{
    display: inline-flex;
    margin-bottom: 7px;
    float: left;
}
.sbi-selected-source-item{
    width: auto;
    height: 38px;
    border: 1px solid #D0D1D7;
    border-radius: 2px;
    display: inline-flex;
    align-items: center;
    margin-right: 10px;
    margin-bottom: 7px;
    box-sizing: border-box;
    float: left;
}
.sbi-selected-source-item-avatar,
.sbi-selected-source-item-avatar img{
    width: 36px;
    height: 36px;
}
.sbi-selected-source-item-avatar {
    border-right: 1px solid #F3F4F5;
}

.sbi-selected-source-item span{
    font-weight:600;
    margin: 0px 10px;
    font-size: 13px;
}
.sbi-selected-source-item-icon{
    margin-left: auto;
    width: 36px;
    height: 36px;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
}
.sbi-selected-source-item-icon svg{
    fill: #D72C2C;
    width: 14px;
    height: 14px;
    float: left;
}

#sbi-multiple-sources-ctn .sbi-feedtype-sec-desc{
    margin: 4px 0 16px;
}

.sbi-hashtag-item{
    display: inline-flex;
    font-weight: 400;
    height: 26px;
    font-size: 12px;
    align-items: center;
    padding: 0px 6px 1px 10px;
    border-radius: 50px;
    background: #f3f1f1;
    margin-right: 10px;
    margin-bottom: 10px;
}
.sbi-hashtag-item-delete{
    width: 16px;
    height: 16px;
    background: #8C8F9A;
    color: #fff;
    margin-left: 5px;
    border-radius:50px;
    cursor: pointer;
    position: relative;
}

.sbi-hashtag-item-delete:before,
.sbi-hashtag-item-delete:after{
    content: '';
    position: absolute;
    height: 2px;
    width: 8px;
    background: currentColor;
    left: 4px;
    top: 7px;
    -webkit-transform: rotate(45deg);
    transform: rotate(45deg);
}
.sbi-hashtag-item-delete:after{
    -webkit-transform: rotate(-45deg);
    transform: rotate(-45deg);
}
.sbi-hashtag-fetchby-chbx{
    display: flex;
    margin-bottom: 15px;
    margin-top: 10px;
}
.sbi-hashtag-fetchby-chbx .sbi-fb-stp-src-type{
    margin-left: 0px!important;
    margin-right: 20px!important;
}
/*
    Custom View Control With Image & info
*/
.sb-control-imginfo-elem{
    background: #F9F9FA;
    border: 1px solid #E8E8EB;
    padding: 20px 30px;
}
.sb-control-imginfo-icon svg{
    fill: none!important;
}
.sb-control-imginfo-icon{
    display: flex;
    justify-content: center;
    align-self: center;
}
.sb-control-imginfo-text{
    display: flex;
    flex-direction: column;
    padding-top: 20px;
}
.sb-control-imginfo-text [data-textalign="center"]{
    justify-content: center;
}

.sb-control-imginfo-text strong{
    font-size: 18px;
    margin-bottom: 20px;
    line-height: 1.3;
}
.sb-control-shoppbale-enbaled-ctn .sb-control-imginfo-text strong{
    font-size: 14px;
    text-align: center;
    line-height: 1.4em;
}
.sb-control-imginfo-text span{
    color: #434960;
    font-size: 14px;
}

.sb-shoppable-edit-btn{
    position: absolute;
    right: 10px;
    top: 10px;
    cursor: pointer;
    z-index: 9;
    padding: 7px 13px 8px 35px;
    border: 2px solid rgba(255,255,255,.1);
}
.sb-shoppable-edit-btn-link svg{
    width: 16px;
    height: 10px;
    top: 10px;
    fill: currentColor;
}

.sb-control-selectedpost-info{
    display: flex;
    align-items: center;
    box-sizing: border-box;
    padding: 10px;
    border: 1px solid #D0D1D7;
    margin-bottom: 30px;
    margin-top: 10px;
}
.sb-control-selectedpost-info img{
    width: 77px;
    height: 77px;
}

.sb-control-selectedpost-info span{
    padding: 0 15px;
    color: #141B38;
    font-size: 13px;
    line-height: 1.6em;
}
.sb-control-selectedpost-input span{
    color: #434960;
    font-size: 13px;
    margin-bottom: 5px;
}

.sb-control-selectedpost-btns{
    display: grid;
    grid-template-columns: 48% 48%;
    grid-column-gap: 4%;
    margin-top: 10px;
}
.sb-control-selectedpost-btns button{
    cursor: pointer;
    font-weight: 600;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 9px;
}
.sb-control-selectedpost-btns button svg{
    margin-right: 10px;
}

/*
    Feed Type Customizer
*/
.sb-control-feedtype-item{
     margin-bottom: 30px;
    padding-bottom: 20px;
}

.sb-control-feedtype-item:after{
    content: '';
    position: absolute;
    height: 1px;
    width: calc(100% + 40px);
    left: -20px;
    background: #DCDDE1;
    bottom: 0px;
}
.sb-control-feedtype-item:last-of-type:after{
    display: none;
}

.sb-control-feedtype-list{
    margin-top: 10px;
}
.sb-control-feedtype-list-item{
    float: left;
    height: 30px;
    display: flex;
    justify-content: center;
    align-items: center;
    background: #F3F4F5;
    border-radius: 50px;
    padding: 0 14px;
    margin-right: 10px;
    margin-bottom: 10px;
}

.sb-control-feedtype-list-item-icon{
    display: flex;
    justify-content: center;
    align-items: center;
}
.sb-control-feedtype-list-item svg{
     width: 12px;
    margin-right: 4px;
    fill: #0096CC;
}
.sbi-fb-extppcustomizer-btns{
    display: grid;
    grid-template-columns: 49% 49%;
    grid-column-gap: 1%;
}
.sbi-fb-feedtypescustomizer-pp-ctn .sbi-fb-feedtypes-popup{
    padding: 22px 20px;
}
.sbi-fb-feedtypescustomizer-pp-ctn  .sbi-fb-source-top{
    padding: 0px;
    padding-bottom: 22px;
}
.sbi-fb-feedtypescustomizer-pp-ctn .sbi-fb-feedtypescustomizer-content{
    background: #F9F9FA;
    border: 1px solid #E8E8EB;
    margin-bottom: 20px;
    border-radius: 4px;
}
.sbi-fb-feedtypescustomizer-pp-ctn .sbi-feedtype-section{
    box-shadow: unset;
    border-bottom: 1px solid #E8E8EB;
}
.sbi-fb-feedtypescustomizer-pp-ctn .sbi-feedtype-section:last-of-type{
    border-bottom: 0px;
}
.sbi-fb-feedtypescustomizer-pp-ctn .sbi-feedtype-sec-desc{
    margin-bottom: 20px;
}
.sbi-fb-feedtypescustomizer-pp-ctn .sbi-hashtag-fetchby .sbi-feedtype-sec-desc{
    margin-bottom: 0px;
}

.sbi-fb-feedtypescustomizer-pp-ctn .sbi-selected-source-item{
    border: 1px solid #e1e1e1;
}
.sbi-fb-feedtypescustomizer-content .sbi-fb-hd-btn svg{
    fill: #141B38;
}
.sbi-fb-feedtypescustomizer-content .sbi-feedtype-icon-wrap svg{
    width: 20px!important;
    height: 20px!important;
}
.sbi-fb-feedtypescustomizer-content .sbi-feedtype-section{
    padding: 24px;
}

/*
    Moderation Mode
*/
.sb-control-moderationmode-action-btns{
    padding-top: 30px;
}
.sb-control-moderationmode-action-btns button{
    margin-bottom: 20px;
}
.sbi-moderation-overlay-ctn{
    position: absolute;
    width: 100%;
    height: 100%;
    left: 0;
    top: 0;
    z-index: 8;
    cursor: pointer;
}
.sbi-moderation-toggle{
    position: absolute;
    width: 66px;
    height: 22px;
    top: 10px;
    right: 10px;
    background: #FFFFFF;
    box-shadow: 0px 1px 2px rgba(0, 0, 0, 0.05), 0px 4px 5px rgba(0, 0, 0, 0.05);
    border-radius: 2px;
    padding: 2px;

}
.sbi-moderation-toggle-icon{
    width: 33px;
    height: 22px;
    position: relative;
    float: left;
    border-radius: 2px;
    color: #8C8F9A;
}

.sbi-moderation-toggle[data-type="active"] .sbi-moderation-checkmark{
    background: #59AB46;
    color: #fff!important;
}

.sbi-moderation-toggle[data-type="inactive"] .sbi-moderation-x{
    background: #D72C2C;
    color: #fff!important;
}
.sbi_expand{
    cursor: pointer;
}
.sbi-moderation-checkmark:before{
    content: '';
    position: absolute;
    width: 13px;
    height: 5px;
    left: 8px;
    top: 5px;
    border-bottom: 2px solid currentColor;
    border-left: 2px solid currentColor;
    -webkit-transform: rotate(-45deg);
    transform: rotate(-45deg);
}

.sbi-moderation-x:before,
.sbi-moderation-x:after{
        content: '';
    position: absolute;
    width: 15px;
    height: 2px;
    left: 9px;
    top: 10px;
    background: currentColor;
    -webkit-transform: rotate(-45deg);
    transform: rotate(-45deg);
}
.sbi-moderation-x:after{
   -webkit-transform: rotate(45deg);
    transform: rotate(45deg);
}
.sb-control-moderationmode-elements > .sb-control-switcher-ctn{
    margin-bottom: 25px;
}

.sbi_header_text  h3{
    color: inherit;
}


.sbi-customizer-ms-modes .sbi_link,
.sbi-customizer-ms-modes .sb_instagram_header{
    display: none!important;
}

.sbi-moderation-pagination{
    display: flex;
    margin-top: 20px;
    justify-content: flex-end;
}
.sbi-moderation-pagination-btn{
    margin-left: 20px;
}

#sb_instagram .sbi_item:not(.sbi_transition) .sbi_photo,
#sb_instagram.sbi_highlight #sbi_images, #sb_instagram.sbi_masonry #sbi_images,
#sb_instagram #sbi_images .sbi_item{
    -webkit-transition: unset!important;
    transition: unset!important;
}

#sb_instagram .sbi_item:not(.sbi_transition):hover .sbi_photo{
    -webkit-transition: all .5s!important;
    transition: all .5s!important;
}

#sb_instagram .sbi_item .sbi_photo_shady{
    position: absolute;
    left: 0;
    top: 0;
    height: 100%;
    width: 100%;
    z-index: 2;
        background-position: inherit;
    background-size: inherit;
}
/*
    FREE
*/
.sbi-fb-types-list-pro{
    display: grid;
    grid-template-columns: 24.5% 24.5% 24.5% 24.5%;
    grid-column-gap: 0.5%;
    margin-top: 10px;
}
.sbi-fb-type-el-pro{
    display: flex;
    flex-direction: row;
    align-items: center;
    padding: 12px 20px 12px 12px;
    background: #F9F9FA;
    border: 1px solid #D0D1D7;
    box-sizing: border-box;
    border-radius: 2px;
    color: #141B38;
    font-weight: 600;
    font-size: 14px;
    margin-bottom: 10px;
    cursor: pointer;
}
.sbi-fb-type-el-pro:hover{
    background: #E2F5FF;
    border: 1px solid #E2F5FF;
}
.sbi-fb-type-el-pro-img svg{
    float: left;
}
.sbi-fb-type-el-pro-img{
    margin-right: 20px;
}

/*Lite Top Banner Dismiss*/
.sbi-builder-app-lite-dismiss .sbi-header-notice{
    position: fixed!important;
    top: 32px;
    width: 100%;
    z-index: 2;
    left: 0;
}
.sbi-builder-app-lite-dismiss .sbi-fb-header{
    top: 64px;
}
.sbi-builder-app-lite-dismiss .sb-customizer-sidebar{
    top: 128px;
}
.sbi-builder-app-lite-dismiss .sb-customizer-preview{
    margin-top: 100px;
}

.sbi-fb-feedtypescustomizer-pp-ctn .sbi-addsource-type-btn{
    color: #0068A0;
}
.sbi-fb-feedtypescustomizer-pp-ctn .sbi-addsource-type-btn svg{
    fill: #0068A0;
}
.sb-control-label-pro-toggle{
    display: inline-block;
    background: #b9b9b9;
    color: #fff;
    border-radius: 3px;
    padding: 1px 8px;
    text-transform: uppercase;
    font-size: 11px;
    margin-left: 2px;
}


/*Lightbox */
.sbi-lightbox-ctn{
    width: 100%;
    height: 100%;
    position: absolute!important;
    left: 0;
    top: 2px;
    z-index: 9;
    justify-content: center;
    align-items: flex-start;
    padding-top: 100px;
    opacity: 0;
    visibility: hidden;
}
.sbi-lightbox-dummy-overlay{
    background: rgba(0,0,0,0.9);
    position: absolute;
    width: 100%;
    height: 100vh;
    left: 0;
    top: 0;
}

.sbi-lightbox-ctn[data-visibility="true"]{
    display: flex;
}
.sbi-lightbox-ctn[data-visibility="true"].sbi_lightbox-active{
    display: flex!important;
}
.sbi-lightbox-ctn.sbi_lightbox-disabled{
    display: none!important;
}

#sbi_lightbox.sbi-lightbox-dummy-ctn .sbi_lb-outerContainer{
   width: calc(100% - 140px)!important;
    height: auto!important;
    left: calc(50% - 70px)!important;
    transform: translateX(-50%);
    max-height: 585px!important;
    padding-right: 0px!important;
}
#sbi_lightbox.sbi-lightbox-dummy-ctn .sbi_lb-container-wrapper{
    width: calc(100% - 300px);
}
#sbi_lightbox.sbi-lightbox-dummy-ctn.sbi_lb-comments-disabled .sbi_lb-container-wrapper{
    width: 100%;
}

#sbi_lightbox.sbi-lightbox-dummy-ctn .sbi_lb-image1{
    width: 100%!important;
    height: auto!important;
}

#sbi_lightbox.sbi-lightbox-dummy-ctn .sbi_lb-nav a{
    pointer-events: none;
}
#sbi_lightbox.sbi-lightbox-dummy-ctn.sbi_lb-comments-disabled .sbi_lb-outerContainer{
    width: calc(60% - 140px)!important;
    left: 50%!important;
    position: absolute!important;
    background: none;
}


#sbi_lightbox.sbi-lightbox-ctn{
    top: 0px!important
}
body #sbi_lightboxOverlay.sbi_lightboxOverlay,
body #sbi_lightbox.sbi_lightbox:not(.sbi-lightbox-dummy-ctn){
    display: none!important;
    opacity: 0!important;
    visibility: hidden!important;
}

#sbi-builder-app[data-app-loaded="true"] .sbi-lightbox-ctn{
    opacity: 1;
    visibility: visible;
    height: auto;
}

.sbi_lb-comments-disabled .sbi_lb-commentBox{
    display: none!important;
}

/*
    Onboarding Wizard
*/
.sb-onboarding-wizard-ctn {
    width: calc(100% + 160px);
    position: absolute;
    background: #E6E6EB;
    padding: 0 20px;
    box-sizing: border-box;
    border-top: 2px solid #434960;
    min-height: 100vh;
    left: -160px;
    top: -34px;
    z-index: 9999999;
    padding-top: 100px;
    padding-bottom: 100px;
    margin-top: 0px;
}
.sb-onboarding-wizard-ctn[data-step="0"]{
    z-index: 1;
}
.instagram-feed_page_sbi-setup .sbi-fb-source-ctn,
.instagram-feed_page_sbi-setup .sb-dialog-ctn {
    z-index: 999999999999999999999;
}
.folded .sb-onboarding-wizard-ctn {
    width: calc(100% + 36px);
    left: -36px;
}

.sb-onboarding-wizard-ctn[data-step="0"]{
    position: relative;
    width: 100%!important;
    left: 0!important;
    top: 0!important;
    padding-top:0px!important;
    border-color: transparent!important;
    margin-top: 63px;
}

.sb-onboarding-wizard-wrapper {
    width: 850px;
    max-width: 100%;
    margin: auto;
    margin-top: 50px;
}
.sb-onboarding-wizard-wrapper[data-step="0"]{
    width: 1200px;
    margin-top: 100px;
}


.sb-onboarding-wizard-wrapper>div {
    width: 100%;
    box-sizing: border-box;
    float: left;
    position: relative;
}

.sb-onboarding-wizard-top {
    display: flex;
    justify-content: center;
    align-items: center;
}

.sb-onboarding-wizard-logo-ctn {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 68px;
    height: 68px;
    position: relative;
    margin-right: 35px;
    margin-left: -35px;
}
.sb-onboarding-wizard-logo1-ctn {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 46px;
    height: 68px;
    position: relative;
    margin-right: 15px;
    margin-left: -15px;
}
.sb-onboarding-wizard-logo-balloon1{
    width: 100%;
    float: left;
}

.sb-onboarding-wizard-logo-ctn:after {
    content: '';
    position: absolute;
    width: 100%;
    height: 100%;
    background: #fff;
    border-radius: 10px;
    box-shadow: 0px 1.4945039749145508px 2.0379600524902344px 0px #00000012,
        0px 4.1321120262146px 5.634698390960693px 0px #0000000C,
        0px 9.94853401184082px 13.566183090209961px 0px #00000009,
        0px 33px 45px 0px #00000006;
    -webkit-transform: rotate(-10deg);
    transform: rotate(-10deg);
}

.sb-onboarding-wizard-logo-ctn img {
    z-index: 2;
}

.sb-onboarding-wizard-logo-balloon {
    position: absolute;
    right: -15px;
    bottom: -13px;
}

.sb-onboarding-wizard-plugin-name h3 {
    font-weight: 600 !important;
    color: #141B38;
    font-size: 32px !important;
    line-height: 1.1em !important;
}

.sb-onboarding-wizard-plugin-name span {
    font-weight: 600;
    color: #141B38;
    font-size: 20px;
    opacity: .6;
    margin-top: 6px;
    float: left;
}

.sb-onboarding-wizard-steps {
    margin-top: 50px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.sb-onboarding-wizard-steps:before {
    content: '';
    position: absolute;
    width: 100%;
    height: 2px;
    background: #CED0D9;
    margin-top: -1px;
}

.sb-onboarding-wizard-step-icon {
    width: 20px;
    height: 20px;
    position: relative;
    background: #fff;
    border: 4px solid #CED0D9;
    border-radius: 25px;
    box-sizing: border-box;
}

.sb-onboarding-wizard-step-icon:before {
    content: '';
    position: absolute;
    width: calc(100% + 6px);
    height: calc(100% + 6px);
    border: 3px solid #E6E6EB;
    left: -6px;
    top: -6px;
    border-radius: inherit;
    z-index: 1;
}

.sb-onboarding-wizard-step-icon[data-active="true"] {
    border-color: #0096CC;
}

.sb-onboarding-wizard-step-icon[data-active="done"] {
    background: #2C324C;
    border-color: #2C324C;
}

.sb-onboarding-wizard-step-icon[data-active="done"]:after {
    content: '';
    position: absolute;
    height: 3px;
    width: 8px;
    color: #fff;
    border-left: 2px solid currentColor;
    border-bottom: 2px solid currentColor;
    -webkit-transform: rotate(-45deg);
    transform: rotate(-45deg);
    top: 2px;
    left: 1px;
}

.sb-onboarding-wizard-step-ctn {
    width: 100%;
    float: left;
    margin-top: 30px;
}

.sb-onboarding-wizard-step-wrapper {
    background: #fff;
    border-radius: 5px;
    padding-bottom: 20px;
    box-shadow: 0px 4px 5px 0px #0000000D, 0px 1px 2px 0px #0000000D;
}

/*
.sb-onboarding-wizard-step-welcome {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    text-align: center;
    padding: 100px 20%;
}
*/
.sb-onboarding-wizard-step-welcome {
    display: grid;
    grid-template-columns: 55% 45%;
    padding-bottom: 0px;
}
.sb-onboarding-wizard-welcome-text{
    padding: 0px 10%;
    display: flex;
    flex-direction: column;
    justify-content: center;
}
.sb-onboarding-wizard-welcome-banner {
    background-color:#0068A0;
    border-top-right-radius: 5px;
    border-bottom-right-radius: 5px;
}
.sb-onboarding-wizard-welcome-banner img{
    width: 100%;
    height: auto;
    float: left;
    border-radius: inherit;
}

.sb-onboarding-wizard-welcome-btns {
    display: flex;
    column-gap: 15px;
}

.sb-onboarding-wizard-step-welcome h3 {
    font-size: 24px !important;
    font-weight: 600 !important;
    line-height: 29px !important;
    margin-top: 0px !important;
    margin-bottom: 5px !important;
}

.sb-onboarding-wizard-step-welcome p {
    font-size: 14px;
    font-weight: 400;
    line-height: 22px;
    margin-bottom: 45px;
    color: #696D80;
}

.sb-onboarding-wizard-step-welcome button {
    padding: 16px 25px;
    font-weight: 600;
    border-radius: 4px;
    flex-grow: 1;
    position: relative;
}
.sb-onboarding-wizard-step-welcome button.sb-btn-wizard-next:before,
.sb-onboarding-wizard-upgrade-btns a.sb-btn-wizard-next:before{
    left: unset;
    top: 21px;
    right: 40px;
}


.sb-onboarding-wizard-step-top {
    display: flex;
    flex-direction: column;
    padding: 20px 20px;
    border-bottom: 1px solid #E6E6EB;
}

.sb-onboarding-wizard-step-top[data-large="true"] {
    padding: 30px 20px;
}

.sb-onboarding-wizard-step-top strong {
    font-size: 12px;
    font-weight: 700;
    line-height: 19px;
    letter-spacing: 0.05em;
    color: #696D80;
}

.sb-onboarding-wizard-step-top h4 {
    font-size: 18px !important;
    font-weight: 600 !important;
    line-height: 25px !important;
    color: #000;
}

.sb-onboarding-wizard-step-top[data-large="true"] h4 {
    font-size: 20px !important;
}

.sb-onboarding-wizard-step-top span {
    font-size: 14px;
    font-weight: 400;
    line-height: 22px;
    color: #696D80;
}

.sb-onboarding-wizard-step-pag-btns {
    display: flex;
    justify-content: flex-end;
    margin-top: 30px;
}

.sb-onboarding-wizard-step-pag-btns button {
    padding: 10px 20px 10px 33px !important;
    margin-left: 20px !important;
    border-radius: 5px;
    position: relative;
}

.sb-btn-wizard-next:before,
.sb-btn-wizard-back:before {
    content: '';
    position: absolute;
    width: 5px;
    height: 5px;
    border-left: 2px solid currentColor;
    border-bottom: 2px solid currentColor;
    left: 15px;
    top: 15px;
    -webkit-transform: rotate(45deg);
    transform: rotate(45deg);
}

.sb-btn-wizard-next:before {
    left: 15px;
    top: 15px;
    -webkit-transform: rotate(230deg);
    transform: rotate(230deg);
}

.sb-onboarding-wizard-ctn button {
    -webkit-transition: unset !important;
    transition: unset !important;
}

.sb-onboarding-wizard-elements-list>div {
    padding: 25px;
}

.sb-onboarding-wizard-elements-list>div:nth-child(odd),
.sb-onboarding-wizard-success-list>div:nth-child(odd) {
    background: #F9F9FA;
}

.sb-onboarding-wizard-elem-info {
    display: inline-flex;
    width: calc(100% - 50px);
    float: left;
    align-content: flex-start;
}

.sb-onboarding-wizard-elem-icon {
    width: 28px;
    height: 28px;
    margin-right: 20px;
    display: flex;
    justify-content: center;
    align-items: center;
}
.sb-onboarding-wizard-elem-icon img{
    width: 28px;
    height: 28px;
    float: left;
}

.sb-onboarding-wizard-elem-text {
    display: flex;
    flex-direction: column;
}

.sb-onboarding-wizard-elem-text strong {
    font-size: 16px;
    font-weight: 600;
    line-height: 26px;
    display: flex;
    align-items: center;
}

.sb-onboarding-wizard-elem-text > span {
    font-size: 14px;
    font-weight: 400;
    line-height: 22px;
    color: #434960;
}
.sb-onboarding-wizard-elem-text-installs{
    font-size: 12px;
    font-weight: 400;
    font-style: italic;
    color: #005B8C;
    display: inline-flex;
    height: 20px;
    align-items: center;
    justify-content: center;
    column-gap: 5px;
    margin-left: 14px;
}

.sb-onboarding-wizard-elem-toggle {
    width: 50px;
    float: right;
}

.sb-onboarding-wizard-elem-toggle>div {
    width: 36px;
    height: 16px;
    padding: 2px;
    border-radius: 31px;
    background-color: #9295A6;
    position: relative;
    cursor: pointer;
}


.sb-onboarding-wizard-elem-toggle>div[data-color="green"][data-active="true"] {
    background-color: #59AB46;
}

.sb-onboarding-wizard-elem-toggle>div[data-uncheck="true"] {
    opacity: .5;
    cursor: auto;
}

.sb-onboarding-wizard-elem-toggle>div:before {
    content: '';
    position: absolute;
    width: 16px;
    height: 16px;
    border-radius: 50px;
    top: 2px;
    left: 3px;
    background-color: #fff;
    box-shadow: 0px 1px 2px 0px #00000040;
    -webkit-transition: all .2s ease-in-out;
    transition: all .2s ease-in-out;
}

.sb-onboarding-wizard-elem-toggle>div[data-active="true"]:before {
    left: 22px;
}

.sb-onboarding-wizard-elem-toggle>div[data-color="blue"][data-active="true"]{
    background-color: #0096CC;
}

.sb-onboarding-wizard-elements-list-hd {
    padding: 20px !important;
    padding-top: 25px !important;
    background-color: #fff !important;
    border-bottom: 1px solid #E6E6EB !important;
    font-size: 18px !important;
    font-weight: 600 !important;
    line-height: 25px !important;
}

.sb-onboarding-wizard-step-configuref {
    padding-bottom: 30px;
}
.sb-onboarding-wizard-success-list{
    overflow: hidden;
}
.sb-onboarding-wizard-step-success{
    padding-bottom: 0px;
}
.sb-onboarding-wizard-succes-elem {
    padding: 0px 22px;
    display: flex;
    align-items: center;
    column-gap: 10px;
    visibility: hidden;
    opacity: 0;
    height: 0px;
    transform: translateX(-20px);
    transition: all .3s ease-in-out;
}

.sb-onboarding-wizard-success-list[data-done="true"] .sb-onboarding-wizard-succes-elem {
    height: 54px;
    visibility: visible;
    opacity: 1;
    transform: translateX(0px);
}
.sb-onboarding-wizard-success-list[data-done="true"] .sb-onboarding-wizard-succes-elem:last-of-type {
    margin-bottom: 20px;
}

.sb-onboarding-wizard-succes-icon {
    width: 20px;
    height: 20px;
    color: #468737;
}

.sb-onboarding-wizard-succes-icon svg path {
    stroke: currentColor;
}

.sb-onboarding-wizard-succes-text {
    font-size: 16px;
    font-weight: 400;
    line-height: 26px;
    color: #2C324C;
}

.sb-onboarding-wizard-upgrader {
    margin-top: 30px;
    /* background: #F9F9FA; */
    padding: 30px 10%;
    box-shadow: 0px 4px 50px 0px #0000000D, 0px 1px 2px 0px #0000000D;
    border-radius: 5px;
}

.sb-onboarding-wizard-features-ctn,
.sb-onboarding-wizard-license-ctn {
    background: #fff;
    border: 1px solid #E6E6EB;
    box-shadow: 0px 4px 5px 0px #0000000D, 0px 1px 2px 0px #0000000D;
    margin-bottom: 20px;
    border-radius: 5px;
}

.sb-onboarding-wizard-upgrader-features {
    background: #F9F9FA;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 15px 40px;
    column-gap: 8px;
    border-top-right-radius: 8px;
    border-top-left-radius: 8px;
}

.sb-onboarding-wizard-upgrader-feature-el {
    display: inline-flex;
    font-size: 13px;
    font-weight: 600;
    line-height: 21px;
    column-gap: 10px;
    justify-content: center;
    align-items: center;
    padding: 4px 8px;
    background: #FFFFFF;
    box-shadow: 0px 4px 10px 0px #0000000D, 0px 1px 2px 0px #0000000D;
    border-radius: 6px;
    margin: 3px 0px;
}

.sb-onboarding-wizard-upgrader-feature-el svg {
    float: left;
}

.sb-onboarding-wizard-upgrader-action-ctn {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 40px 20px;
}

.sb-onboarding-wizard-upgrader-action-ctn h4 {
    font-size: 18px !important;
    font-weight: 600 !important;
    line-height: 25px !important;
}

.sb-onboarding-wizard-upgrader-action-ctn p {
    font-size: 15px;
    font-weight: 400;
    line-height: 21px;
    color: #696D80;
    padding: 0 20%;
    margin-bottom: 22px;
    text-align: center;
}

.sb-onboarding-wizard-upgrader-action-ctn a {
    font-size: 16px;
    font-weight: 600;
    line-height: 26px;
    text-align: left;
    padding: 10px 35px;
    border: 0px;
    border-radius: 4px;
}

.sb-onboarding-wizard-upgrader-bonus {
    display: flex;
    justify-content: center;
    align-items: center;
    background: #FFEFCC;
    border-bottom-right-radius: 8px;
    border-bottom-left-radius: 8px;
    font-size: 13px;
    font-weight: 400;
    line-height: 21px;
    padding: 8px 10px;
    column-gap: 5px;
}

.sb-onboarding-wizard-license-ctn {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 40px 60px;
}

.sb-onboarding-wizard-license-ctn strong {
    font-size: 20px;
    font-weight: 600;
    line-height: 25px;
    text-align: center;
    color: #000;
}

.sb-onboarding-wizard-license-ctn p {
    font-size: 14px;
    font-weight: 400;
    line-height: 21px;
    text-align: center;
    color: #696D80;
    margin-top: 5px;
    margin-bottom: 22px;
}

.sb-onboarding-wizard-license-inp-ctn {
    display: flex;
    width: 100%;
    padding: 0 5%;
    align-items: center;
    justify-content: center;
    box-sizing: border-box;
    column-gap: 12px;
}

.sb-onboarding-wizard-license-inp-ctn input {
    flex: 1;
    padding: 5px 10px!important;
    border: 1px solid #E6E6EB
}

.sb-onboarding-wizard-license-inp-ctn button {
    height: 40px;
    font-size: 14px;
    font-weight: 600;
    line-height: 22px;
}

.sb-onboarding-wizard-finish-ctn {
    display: flex;
    justify-content: center;
    align-items: center;
}

.sb-onboarding-wizard-finish-ctn button {
    font-weight: 600;
    font-size: 12px;
    color: #141B38 !important;
    padding: 13px 23px;
    line-height: 1em;
}

.sb-onboarding-wizard-finish-ctn button svg{
    margin-left: 10px;
    float: left;
    margin-top: 3px;
}
.sb-onboarding-wizard-sources-list{
    display: grid;
    grid-template-columns: 32.66% 32.66% 32.66%;
    grid-column-gap: 1%;
    margin-bottom: 10px;
    padding: 10px 20px;
}

.sb-onboarding-wizard-sources-list > div {
    display: flex;
    height: 52px;
    position: relative;
    border-radius: 3px;
    margin-bottom: 10px;
}

.sb-onboarding-wizard-source-newitem{
    justify-content: center;
    align-items: center;
    background: #EBF5FF;
    border: 1px solid #EBF5FF;
    color: #0096CC;
    cursor: pointer;
    }

.sb-onboarding-wizard-source-newitem span{
    color: inherit!important;
    margin-left: 10px !important;
}

.sb-onboarding-wizard-source-item {
    align-items: center;
    box-sizing: border-box;
    padding: 0 10px;
    border: 1px solid #CED0D9;
    padding-bottom: 2px;
}
.sb-onboarding-wizard-source-item strong{
    color: #000;
}

.sb-onboarding-wizard-source-item-avatar{
    position: relative;
    width: 32px;
    height: 32px;
    border-radius: 50px;
    margin-right: 12px;
}
.sb-onboarding-wizard-source-item-avatar img{
    width: inherit;
    height: inherit;
    border-radius: 50px;
    float: left;
}
.sb-onboarding-wizard-source-item-avatar svg{
    position: absolute;
    right: -5px;
    bottom: -3px;
}

.sb-onboarding-wizard-source-item-delete{
    display: flex;
    width: 20px;
    height: 20px;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    margin-left: auto;
}
.sb-onboarding-wizard-source-item-delete svg{
    float: left;
}
.sb-onboarding-wizard-succes-name{
    text-transform: capitalize;
}

.sb-onboarding-wizard-upgrade-ctn{
    display: grid;
    grid-template-columns: 54% 46%;
    background-color: #0068A0;
    box-shadow: 0px 4px 5px 0px #0000000D, 0px 1px 2px 0px #0000000D;
    margin-top: 20px;
    margin-bottom: 20px;
    border-radius: 5px;
}
.sb-onboarding-wizard-upgrade-banner img{
    width: 100%;
    float: left;
    border-top-right-radius: 5px;
    border-bottom-right-radius: 5px;
}
.sb-onboarding-wizard-upgrade-text{
    padding: 0px 10%;
    display: flex;
    flex-direction: column;
    justify-content: center;
}
.sb-onboarding-wizard-upgrade-text h3{
    color: #fff;
}
.sb-onboarding-wizard-upgrade-text p {
    color: #fff;
    margin: 25px 0px;
}
.sb-onboarding-wizard-upgrade-btns a,
.sb-onboarding-wizard-upgrade-btns a:hover{
    color: #0068A0;
    background: #fff;
    padding: 16px 25px;
    position: relative;
}

.sb-onboarding-wizard-upgrade-off{
    padding: 12px 20px;
    display: grid;
    grid-template-columns: 50px calc(100% - 50px);
    background: #FFDF99;
    margin-top: 18px;
    border-radius: 4px;
}
.sb-onboarding-wizard-upgrade-off div{
    display: flex;
    width: 35px;
    height: 35px;
    justify-content: center;
    align-items: center;
}
.sb-onboarding-wizard-upgrade-off div svg{
    float: left;
}
.sb-onboarding-wizard-upgrade-off span{
    color:#331F00;
}

.sb-onboarding-wizard-close{
    top: 0px;
    position: fixed;
    display: flex;
    justify-content: center;
    align-items: center;
    width: 120px;
    height: 40px;
    right: 100px;
    background-color: #434960;
    color: #fff;
    font-size: 14px;
    font-weight: 600;
    column-gap: 10px;
    border-bottom-left-radius: 4px;
    border-bottom-right-radius: 4px;
    cursor: pointer;
    z-index: 99;
}

.instagram-feed_page_sbi-setup .sbi-header-notice{
    display: none;
}

.instagram-feed_page_sbi-setup .sbi-stck-wdg {
    z-index: 99999999999;
}
.sb-btn-wizard-next:before,
.sb-btn-wizard-install:before{
    left: unset !important;
    right: 16px !important;
}
button.sb-btn-wizard-install,
div:not(.sb-onboarding-wizard-welcome-btns) > button.sb-btn-wizard-next{
    padding: 10px 33px 10px 20px !important;
}
.sb-onboarding-wizard-step-wrapper.sb-onboarding-wizard-step-installp{
    padding-bottom: 0px;
}
.sb-onboarding-wizard-elem:last-of-type{
    border-bottom-right-radius: 5px;
    border-bottom-left-radius: 5px;
}
.sb-onboarding-wizard-license-error{
    color: #D72C2C;
    display: flex;
    margin-top: 20px;
    font-weight: 500;
}
.sb-onboarding-wizard-license-error a{
    text-decoration: underline;
}
.sb-onboarding-wizard-license-inp-ctn button svg{
    fill: currentColor!important;
    margin-right: 10px;
}
.sb-onboarding-wizard-license-inp-ctn button svg path{
    fill: currentColor !important;
}

@media (min-width: 760px) and (max-width: 960px) {
    .sb-onboarding-wizard-step-welcome {
        grid-template-columns: 70% 30%;
    }
    .sb-onboarding-wizard-welcome-text{
        padding: 100px 10%;
    }
    .sb-onboarding-wizard-welcome-banner{
        justify-content: center;
        display: flex;
        align-items: center;
    }

}
@media all and (max-width: 760px) {
    .sb-onboarding-wizard-step-welcome {
        display: flex;
        flex-direction: column-reverse;
        margin-bottom: 20px;
    }
    .sb-onboarding-wizard-welcome-text{
        padding: 100px 10%;
    }
}
@media all and (max-width: 960px) {
    .sb-onboarding-wizard-ctn{
        width: 100%;
        left: 0px;
        top: 0;
    }
    .sb-onboarding-wizard-sources-list{
            grid-template-columns: 49% 49%;
    }
}


.sb-onboarding-wizard-smash-list{
    display: flex;
    align-items: center;
    margin-top: 10px;
}
.sb-onboarding-wizard-smash-inside{
    display: flex;
    padding: 6px 10px;
    border: 1px solid #E6E6EB;
    border-radius: 4px;
    column-gap: 10px;
    align-items: center;
}
.sb-onboarding-wizard-smash-list svg{
    float: left;
    fill: #888;
}
.sb-onboarding-wizard-smash-elem{
    display: flex;
    align-items: center;
    column-gap: 5px;
    text-transform: capitalize;
}
.sb-onboarding-wizard-smash-elem span {
    color: #333;
    font-size: 14px;
    font-weight: 500;
}
.sb-onboarding-wizard-smash-elem img{
    width: 20px;
    height: auto;
    float: left;
}
.sb-onboarding-wizard-smash-inside .sb-control-elem-tltp{
    margin-left: unset;
    width: 14px;
    height: 14px;
    cursor: pointer;
}
.sb-onboarding-wizard-smash-inside .sb-control-elem-tltp-content{
    position: absolute;
    opacity: 0;
    visibility: hidden;
    top: -30px;
    left: 7px;
    -webkit-transition: all .2s ease-in-out;
        transition: all .2s ease-in-out;
}
.sb-onboarding-wizard-smash-inside .sb-control-elem-tltp:hover .sb-control-elem-tltp-content{
    opacity: 1;
    visibility: visible;
    top: -16px;
    max-width: 300px;
    width: auto;
    left: 7px;
}

.sb-onboarding-wizard-clicking{
    width: 300px;
    font-size: 14px;
    color: #3b4262;
    line-height: 1.7em;
    position: absolute;
    bottom: -65px;
    display: flex;
    column-gap: 10px;
}
.sb-onboarding-wizard-clicking svg{
    fill: currentColor;
    margin-top: 5px;
}
.sb-onboarding-wizard-clicking span > span{
    text-transform: capitalize;
}