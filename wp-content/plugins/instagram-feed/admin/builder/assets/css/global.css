/*
	Global CSS styles
	Theses styles can be used for different screens
	& In different SmashBalloon Plugins

	* Transitions
	* Default, Hover & Focus Colors & Backgrounds
	* Social Colors
	* Buttons
	* Full Screen Boss
	* Add Source Popup
	* Confirm Dialog
	* Full Screen Loader
	* Tooltip

*/
#sbi-settings{
    visibility: hidden;
    opacity: 0;
}
#sbi-settings[data-app-loaded="true"]{
    visibility: visible;
    opacity: 1;
}


#sbi-builder-app {
	-webkit-font-smoothing: antialiased;
}

/*
	Transitions
*/
.sb-tr-1{
	-webkit-transition: all .1s ease-in-out;
	transition: all .1s ease-in-out;
}
.sb-tr-2{
	-webkit-transition: all .2s ease-in-out;
	transition: all .2s ease-in-out;
}
.sb-tr-3{
	-webkit-transition: all .3s ease-in-out;
	transition: all .3s ease-in-out;
}
.sb-tr-4{
	-webkit-transition: all .4s ease-in-out;
	transition: all .4s ease-in-out;
}
.sb-tr-5{
	-webkit-transition: all .5s ease-in-out;
	transition: all .5s ease-in-out;
}
.sb-tr-6{
	-webkit-transition: all .6s ease-in-out;
	transition: all .6s ease-in-out;
}
.sb-tr-7{
	-webkit-transition: all .7s ease-in-out;
	transition: all .7s ease-in-out;
}
.sb-tr-8{
	-webkit-transition: all .8s ease-in-out;
	transition: all .8s ease-in-out;
}
.sb-tr-9{
	-webkit-transition: all .9s ease-in-out;
	transition: all .9s ease-in-out;
}
.sb-tr-10{
	-webkit-transition: all 1s ease-in-out;
	transition: all 1s ease-in-out;
}

/*
	* Default, Hover & Focus Colors & Backgrounds
*/
.sb-small-p,
.sb-standard-p{
	font-style: normal;
	font-weight: normal;
	font-size: 14px;
	line-height: 160%;
	color: #8C8F9A;
	margin: 0;
}
.sb-standard-p{
	font-size: 16px;
	color: #141B38;
}
#sbi-settings h3 {
	font-style: normal;
	font-weight: 600;
	font-size: 24px;
	line-height: 120%;
	margin: 0 0 4px 0;
	letter-spacing: 0;
}
.sb-caption {
	font-style: normal;
	font-weight: normal;
	font-size: 13px;
	line-height: 150%;
	color: #141B38;
}
.sb-caption.sb-caption-lighter {
	color: #5F6368;
}
.sb-small-text {
	font-size: 12px;
}
.sb-bold {
	font-weight: 600;
}
.sb-dark-text {
	color: #141B38;
}
.sb-small {
	font-style: normal;
	font-weight: bold;
	font-size: 10px;
	line-height: 160%;
	letter-spacing: 0.08em;
	text-transform: uppercase;
	color: #141B38;
}
.sb-button-no-border {
	border-radius: 0 !important;
	border: none !important;
}
.sb-icon-small svg {
	height: 10px;
}
.sb-dark-hover:hover svg, .sb-dark-hover:hover path {
	fill: #141B38;
}
/*orange*/
.sb-btn-orange{
	background: #FE544F!important;
	color: #fff!important;
}
.sb-btn-orange:hover{
	background: #EC352F!important;
	border-color: #EC352F!important;
	color: #fff!important;
}
.sb-btn-orange:focus,
.sb-btn-orange:active{
	background: #BC120E!important;
	border-color: #BC120E!important;
	color: #fff!important;
}

/*red*/
.sb-btn-red{
	background: #D72C2C!important;
	color: #fff!important;
}
.sb-btn-red:hover{
	background: #DF5757!important;
	color: #fff!important;
}
.sb-btn-red:focus,
.sb-btn-red:active{
	background: #841919!important;
	color: #fff!important;
}

/*red*/
.sb-btn-blue{
	background: #0068A0!important;
	color: #fff!important;
}
.sb-btn-blue:hover{
	background: #0096CC!important;
	border-color: #0096CC!important;
	color: #fff!important;
}
.sb-btn-blue:focus,
.sb-btn-blue:active{
	background: #004D77!important;
	border-color: #004D77!important;
	color: #fff!important;
}

/*grey*/
.sb-btn-grey{
	background: #F3F4F5!important;
	color: #141B38!important;
	border: 1px solid #D0D1D7!important;
}
.sb-btn-grey:hover{
	background: #fff!important;
	color: #141B38!important;
	border: 1px solid #DCDDE1!important;
}
.sb-btn-grey:focus,
.sb-btn-grey:active{
	background: #E8E8EB!important;
	color: #141B38!important;
	border: 1px solid #D0D1D7!important;
}

/*dark*/
.sb-btn-dark{
	background: #2C324C!important;
	color: #fff!important;
}
.sb-btn-dark:hover{
	background: #434960!important;
	color: #fff!important;
}
.sb-btn-dark:focus,
.sb-btn-dark:active{
	background: #141B38!important;
	color: #fff!important;
}

/*orange*/
.sbi-btn-orange{
	background: #FE544F!important;
	color: #fff!important;
}
.sbi-btn-orange:hover{
	background: #F37036!important;
	color: #fff!important;
}
.sbi-btn-orange:focus,
.sbi-btn-orange:active{
	background: #BC120E!important;
	color: #fff!important;
}

/*red*/
.sbi-btn-red{
	background: #D72C2C!important;
	color: #fff!important;
}
.sbi-btn-red:hover{
	background: #DF5757!important;
	color: #fff!important;
}
.sbi-btn-red:focus,
.sbi-btn-red:active{
	background: #841919!important;
	color: #fff!important;
}

/*red*/
.sbi-btn-blue{
	background: #0068A0!important;
	color: #fff!important;
}
.sbi-btn-blue:hover{
	background: #0096CC!important;
	color: #fff!important;
}
.sbi-btn-blue:focus,
.sbi-btn-blue:active{
	background: #004D77!important;
	color: #fff!important;
}

/*grey*/
.sbi-btn-grey{
	background: #F3F4F5!important;
	color: #141B38!important;
	border: 1px solid #D0D1D7!important;
}
.sbi-btn-grey:hover{
	background: #fff!important;
	color: #141B38!important;
	border: 1px solid #DCDDE1!important;
}
.sbi-btn-grey:focus,
.sbi-btn-grey:active{
	background: #E8E8EB!important;
	color: #141B38!important;
	border: 1px solid #D0D1D7!important;
}

/*dark*/
.sbi-btn-dark{
	background: #2C324C!important;
	color: #fff!important;
}
.sbi-btn-dark:hover{
	background: #434960!important;
	color: #fff!important;
}
.sbi-btn-dark:focus,
.sbi-btn-dark:active{
	background: #141B38!important;
	color: #fff!important;
}

/*disabled*/
.sb-btn-orange[data-active="false"],
.sb-btn-blue[data-active="false"],
.sb-btn-red[data-active="false"],
.sb-btn-grey[data-active="false"],
.sb-btn-dark[data-active="false"]{
	background: #E8E8EB !important;
	color: #8C8F9A !important;
}


/*
	* Social Colors
*/

/*Facebook*/
.sb-cl-facebook,
.sb-clhv-facebook:hover{
	color: #006BFA!important;
}
.sbi-bg-facebook,
.sbi-bghv-facebook:hover{
	background: #006BFA!important;
}

/*Instagram*/
.sb-cl-instagram,
.sb-clhv-instagram:hover{
	color: #BA03A7!important;
}
.sbi-bg-instagram,
.sbi-bghv-instagram:hover{
	background: #BA03A7!important;
}

/*Twitter*/
.sb-cl-twitter,
.sb-clhv-twitter:hover{
	color: #1B90EF!important;
}
.sbi-bg-twitter,
.sbi-bghv-twitter:hover{
	background: #1B90EF!important;
}

/*YouTube*/
.sb-cl-youtube,
.sb-clhv-youtube:hover{
	color: #EB2121!important;
}
.sbi-bg-youtube,
 .sbi-bghv-youtube:hover{
	background: #EB2121!important;
}

/*Linkedin*/
.sb-cl-linkedin,
.sb-clhv-linkedin:hover{
	color: #007bb6!important;
}
.sbi-bg-linkedin,
.sbi-bghv-linkedin:hover{
	background: #007bb6!important;
}

/*Mail*/
.sb-cl-mail,
.sb-clhv-mail:hover{
	color: #666!important;
}
.sbi-bg-mail,
.sbi-bghv-mail:hover{
	background: #666!important;
}

.sb-cursor-pointer{
	cursor: pointer;
}
.sbi-stck-el.sbi-stck-el-upgrd svg path{
  fill: #fff!important;
}
/*Buttons*/
.sb-btn{
	display: flex;
	flex-direction: row;
	justify-content: center;
	align-items: center;
	padding: 9px 38px;
	font-style: normal;
	font-weight: 500;
	font-size: 14px;
	line-height: 17px;
	border-radius: 2px;
	cursor: pointer;
	color: #fff;
	outline: none;
	box-shadow: none;
	border: none;
	gap: 8px;
}
.sb-btn:focus{
	box-shadow: none;
}

.sbi-small-chevron svg{
	width: 6px;
	height: 8px;
	top: 14px;
	left: 18px;
}

.sb-btn-right-icon{
	display: flex;
	justify-content: flex-start;
	padding: 15px 12px;
}
.sb-btn-right-txt{
	display: flex;
	justify-content: flex-start;
	align-items: center;
	line-height: 1em;
}
.sb-btn-right-txt svg{
	float: left;
	margin-right: 10px;
}

.sb-btn-right-chevron{
	width: 7px;
	height: 7px;
	border-right: 2px solid currentColor;
	border-bottom: 2px solid currentColor;
	-webkit-transform: rotate(-45deg);
	transform: rotate(-45deg);
	margin-left: auto;
	margin-right: 10px;
}

/*Add Source*/
/*
	Add new Source Popup
*/
.sbi-fb-popup-cls{
	height: 14px;
	width: 14px;
	position: absolute;
	cursor: pointer;
	right: 17px;
	top: 17px;
	z-index: 3;
}
.sbi-fb-center-boss{
	display: flex;
	justify-content: center;
	align-items: center;
}
.sbi-fb-popup-inside{
	left: 80px;
  max-width: 1172px;
  width: calc(85% - 200px);
	background: #fff;
	color: #141B38;
	position: relative;
	box-shadow: 0px 26.7377px 77.2886px rgba(0, 0, 0, 0.107828), 0px 14.2952px 41.3222px rgba(0, 0, 0, 0.0894161), 0px 8.01379px 23.1649px rgba(0, 0, 0, 0.075), 0px 4.25607px 12.3027px rgba(0, 0, 0, 0.0605839), 0px 1.77104px 5.11942px rgba(0, 0, 0, 0.0421718);
	border-radius: 2px;
	overflow-y: auto;
	max-height: 80vh;
}
.sbi-fb-feedtypes-popup.sbi-fb-popup-inside{
	width: 1172px;
	max-width: calc(85% - 200px);
}
.sbi-fb-extensions-pp-ctn .sbi-fb-popup-inside{
  max-width: 880px!important;

}


.sbi-fb-popup-inside.sbi-narrower-modal {
	max-width: 576px;
}
.sbi-fb-popup-inside.sb-onboarding-tooltip {
	overflow-y: visible !important;
}
@media all and (max-width: 960px) {
  .sbi-fb-popup-inside {
    left: 20px;
    max-width: 920px;
    width: calc(85% - 20px);
  }
}
.sbi-fb-embed-popup{
	width: 575px;
}

.sbi-fb-source-pp-customizer h5,.sbi-fb-embed-popup h5{
	font-size: 24px;
	padding: 0 20px;
	margin-top: 25px;
	margin-bottom: 15px;
}
.sbi-fb-feedtypes-popup h5{
	font-size: 26px;
	padding: 0 20px;
	margin-top: 25px;
	margin-bottom: 30px;
}

.sbi-fb-feedtypes-pp-ctn .sbi-fb-adv-types .sbi-fb-types-list{
	margin-bottom: 30px !important
}
.sbi-fb-srcs-update{
	background: var(--cl-orange);
	font-weight: 600;
	font-size: 14px;
	line-height: 160%;
	height: 38px;
}
.sbi-fb-srcs-update-footer{
	display: flex;
	align-items: center;
	border-top: 1px solid #DCDDE1;
	background: #F9F9FA;
}
.sbi-fb-srcs-update-footer-txt{
	font-size: 18px;
	line-height: 1.7em;
}

.sbi-fb-srcs-update-footer-image svg{
	width: 100%;
	height: auto;
	float: left;
}
.sbi-fb-srcs-update-footer-txt{
	box-sizing: border-box;
	display: flex;
	justify-content: center;
}
.sbi-fb-stp1-elm{
	margin-bottom: 30px;
	float: left;
	display: flex;
}
.sbi-fb-source-top .sbi-fb-stp1-elm:first-of-type{
	justify-content: center;
	align-items: center;
	padding-bottom: 30px;
	border-bottom: 1px solid #D0D1D7;
}
.sbi-fb-stp1-event{
	padding-bottom: 0px!important;
	border-bottom: none!important;
	padding-right: 20%;
    align-items: flex-start!important;
}
.sbi-fb-stp1-elm-desc > a{
	font-weight: 600;
	text-decoration: underline;
	color: #2A65DB;
}

.sbi-fb-stp-src-ctn,
.sbi-flex-center-center{
	display: flex;
	justify-content: center;
	align-content: center;
}
.sbi-fb-stp-src-type{
	display: flex;
	justify-content: center;
	align-content: center;
	font-size: 13px;
	font-weight:400;
	cursor: pointer;
	margin-left: 20px;
	color: #434960;
	white-space: nowrap;
}
.sbi-fb-stp-src-type[data-active="true"]{
	font-weight:600;
	color: #141B38;
}
.sbi-not-sure-wrap span svg {
	margin-right: 5px;
}
.sbi-not-sure-wrap:hover .sbi-not-sure-tooltip{
	display: block;
}
.sbi-fb-stp1-elm-ic{
	width: 28px;
	height: 28px;
	border-radius: 35px;
	background: #F3F4F5;
	display: flex;
	justify-content: center;
	align-items: center;
	font-weight: 500;
	flex: none;
}
.sbi-fb-stp1-elm-head{
	font-size: 16px;
	font-weight: 600;
}
.sbi-fb-stp1-elm-desc{
	font-size: 15px;
	margin-top: 4px;
	line-height: 1.3em;
	color: #5F6368;
}

.sbi-fb-source-btm-hd{
	font-size: 15px;
	font-weight: 500;
}
.sbi-fb-src-back-top i{
	font-size: 12px;
	margin-right: 10px;
}
.sbi-fb-source-account-info{
	background: #F3F4F5;
	color: #8C8F9A;
	display: flex;
	align-items: center;
	padding: 6px 6px 6px 12px;
	font-size: 15px;
}
.sbi-fb-source-list {
	display: grid;
	grid-template-columns: 49.5% 49.5%;
	grid-column-gap: 1%;
	margin-top: 10px;
	max-height: calc(80vh - 360px);
	overflow-y: auto;
	min-height: 80px;
}

.sbi-fb-source-account-info strong{
	color: #141B38;
}
.sbi-fb-source-btn{
	margin-top: 60px;
	color: #fff;
	display: flex;
	justify-content: center;
	align-items: center;
	background: #0068A0;
	cursor: pointer;
	padding: 10px 20px;
	border-radius:  3px;
	font-size: 15px;
	font-weight: 500;
	border: none;
}
.sbi-fb-icon-success{
	position: relative;
	width: 30px;
	height: 20px;
	margin-right: 10px;
}
.sbi-fb-icon-success:before{
	content: '';
	position: absolute;
	width: 13px;
	height: 5px;
	top: 4px;
	border-left: 3px solid currentColor;
	border-bottom: 3px solid currentColor;
	-webkit-transform: rotate(-45deg);
	transform: rotate(-45deg);
}

.sbi-fb-source-inp-label,.sbi-fb-wh-label{
	font-size: 14px;
	color: #434960;
}
.sbi-fb-source-mnl-type .sbi-fb-stp-src-ctn{
	justify-content: flex-start;
	margin-bottom: 20px;
	margin-top: 10px;
}
.sbi-fb-source-mnl-type .sbi-fb-stp-src-ctn .sbi-fb-stp-src-type{
	margin-left: 0px;
	margin-right: 20px
}
.sbi-fb-source-inp,.sbi-fb-wh-inp{
	border-radius: 0px !important;
	outline: none ;
	margin-top: 5px;
	border: 1px solid #D0D1D7 !important;
	margin-bottom: 20px;
	height: 35px;
	line-height: 35px;
}
.sbi-fb-sec-heading span{
	font-size:14px;
	color: #434960;
	line-height: 1.7em;
	margin-top: 10px;
	display: block;
}
#sbi-builder-app .sbi-fb-sec-heading span {
	margin-top: 0;
}
.sbi-fb-sec-heading {
	margin-bottom: 10px;
}
.sbi-fb-slctsrc-content,.sbi-fb-section-wh-insd{
	padding:  40px;
}

.sbi-fb-slctsrc-ctn h4,.sbi-fb-section-wh-insd h4{
	font-size: 20px;
	padding: 0px;
	margin: 0px;
}

.sbi-fb-srcs-item{
	width: 100%;
	cursor: pointer;
	height: 62px;
	margin-top: 10px;
	border-radius: 3px;
	border: 1px solid #E7E7E9;
	display: flex;
	position: relative;
}
.sbi-connecting-account-item {
	display: flex;
	align-content: center;
	align-items: center;

	position: relative;
	box-sizing: border-box;

	height: 62px;
	width: 208px;
	margin: auto;
	padding: 12px;
	background: #fff;
	box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05), 0 4px 5px rgba(0, 0, 0, 0.05);
	border-radius: 4px;
}
.sbi-fb-source-step4 {
	max-width: 576px;
}
.sbi-source-account-box {
	padding: 32px;

	background-color: #F3F4F5;
}
.sb-details-wrap {
	align-items: center;
}
.sbi-connecting-account-item .sbi-fb-srcs-item-avatar {
	margin-right: 8px;
}
.sbi-fb-srcs-item .sbi-fb-srcs-item-avatar {
	margin-right: 10px;
}
.sbi-fb-srcs-item[data-disabled="true"]{
	background: #F3F4F5;
}
.sbi-fb-stp-src-type[data-disabled="true"],
.sbi-fb-stp-src-type[data-disabled="true"] .sbi-fb-chbx-round{
	color: #8C8F9A !important;
	cursor: default;
}
.sbi-fb-stp-src-type[data-disabled="true"] {
	position: relative;
}

.sbi-fb-srcs-item[data-disabled="true"] .sbi-fb-srcs-item-inf{
	opacity: .55;
}

.sbi-fb-srcs-item:hover{
	border-color: #86D0F9;
}
.sbi-fb-srcs-item[data-active="true"]{
	border-color: #0096cc;
}

.sbi-fb-source-top .sbi-fb-srcs-item{
	margin-bottom: 0px;
}
.sbi-fb-srcs-new{
	display: flex;
	justify-content: center;
	align-items: center;
	font-size: 16px;
	color: #0096CC;
	background: #EBF5FF;
	font-weight: 600;
	border: 1px solid #EBF5FF;
}
.sbi-fb-srcs-new i{
	font-size: 14px;
	padding: 0 10px;
	margin-left: -10px;
}
.sbi-fb-srcs-item-chkbx{
	width: 40px;
	height: inherit;
	display: flex;
	justify-content: center;
	align-items: center;
	margin: 0px 7px;
}

.sbi-fb-srcs-item-chkbx-ic,.sbi-fb-chbx-round{
	width: 16px;
	height: 16px;
	box-sizing: border-box;
	position: relative;
	border-radius: 50px;
	border: 2px solid #8c8f9a;
}
[data-multifeed="active"] .sbi-fb-srcs-item-chkbx-ic{
	border-radius: 2px;
}

[data-active="true"] .sbi-fb-srcs-item-chkbx-ic, [data-active="true"] > .sbi-fb-chbx-round, .sbi-fb-source-popup [data-active="true"] > .sbi-fb-chbx-round{
	border-color: #0096cc;
	background: #0096cc;
}
[data-multifeed="active"] [data-active="true"] .sbi-fb-srcs-item-chkbx-ic:before{
	content: '';
	position: absolute;
	width: 8px;
	height: 3px;
	border-left: 2px solid #fff;
	border-bottom: 2px solid #fff;
	top: 2px;
	right: 1px;
	-webkit-transform: rotate(-45deg);
	transform: rotate(-45deg);
}

[data-multifeed="inactive"] [data-active="true"] .sbi-fb-srcs-item-chkbx-ic:before, [data-active="true"] > .sbi-fb-chbx-round:before,
.sbi-fb-source-popup .sbi-fb-source-list [data-active="true"] .sbi-fb-srcs-item-chkbx-ic:before{
	content: '';
	position: absolute;
	height: 6px;
	width: 6px;
	background: #fff;
	border-radius: 25px;
	left: 3px;
	top: 3px;
}

.sbi-fb-srcs-item-avatar{
	display: flex;
	width: 42px;
	height: inherit;
	justify-content: center;
	align-items: center;
}
.sbi-fb-srcs-item-avatar img{
	width: 42px;
	height: 42px;
	border-radius: 50%;
	background: #eee;
}
.sbi-fb-srcs-item-inf{
	width: 100%;
	height: inherit;
	display: flex;
	justify-content: center;
	flex-direction: column;
}
.sbi-fb-srcs-item-name{
    color: #141B38;
	font-weight: 600;
}
.sbi-fb-srcs-item-name-event{
	font-size: 10px;
    font-weight: 500;
    color: #777;
}

.sbi-fb-srcs-item-type{
	color: #434960;
	font-weight: 600;
	text-transform: uppercase;
	display: flex;
	align-items: center;
}
.sbi-fb-srcs-item-type svg{
	fill: currentColor;
}
[data-type="page"] .sbi-fb-srcs-item-type svg{
	width: 11px;
}
.sbi-fb-srcs-back{
    margin-right: auto;
}


/*Full Screen Window*/
.sb-fs-boss{
	position: fixed;
	height: 100vh;
	width: 100%;
	left: 0;
	top: 0;
	bottom: 0;
	right: 0;
	background: rgba(0,0,0,.4);
	z-index: 9989;
}

.sb-dialog-popup{
	width: 575px!important;
	min-height: 250px;
}
.sb-dialog-remove-source{
	background: #F3F4F5;
	padding: 40px 20px;
	display: flex;
	justify-content: center;
	align-items: center;
}
.sb-dialog-remove-source .sbi-fb-srcs-item{
	background: #fff;
	width: 280px;
    padding-left: 20px;
    box-sizing: border-box;
	margin-top: 0px;
}
.sb-dialog-popup-content{
	display: flex;
	justify-content: center;
	align-items: center;
	text-align: center;
	flex-direction: column;
	padding: 38px 12%;
}
.sb-dialog-popup-content strong{
	font-size: 22px;
	color: #141B38;
	display: block;
	margin-bottom: 15px;
	line-height: 160%;
}
.sb-dialog-popup-content span{
	font-size: 16px;
	line-height: 1.5em;
	color: #434960;
}
.sb-dialog-popup-actions{
	display: flex;
	justify-content: center;
	align-items: center;
	margin-top: 25px;
}
.sb-dialog-popup-actions button{
	width: 100%;
	margin: 4px 4px;
	cursor: pointer;
	height: 42px;
}


.sb-full-screen-loader{
	position: absolute;
    width: 100%;
    height: calc(100vh - 32px);
    background: #f0f0f1;
    z-index: 999999;
    overflow: hidden;
    display: none;
    opacity: 0;
    visibility: hidden;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    top: 0px;
}
.sb-full-screen-loader[data-show="shown"]{
	display: flex;
	opacity: 1;
	visibility: visible;
}
.sb-full-screen-loader-logo{
	width: 190px;
	height: 190px;
	position: relative;
	margin-bottom: 40px;
	margin-top: -55px;
}
.sb-full-screen-loader-logo svg {
  height: auto;
}
.sb-full-screen-loader-border{
	width: inherit;
	height: inherit;
	box-sizing: border-box;
	border: 6px solid green;
	left: 0;
	top: 0;
	position: absolute;
	border-radius: 50%;
}
.sb-full-screen-loader-img{
	width: inherit;
	height: inherit;
	display: flex;
	justify-content: center;
	align-items: center;
	position: absolute;
    top: 0px;
    left: 0px;
	z-index: 2;
}
.sb-full-screen-loader-img svg{
	float: left;
	width: 60px;
	fill: #FE544F;
}
.sb-full-screen-loader-txt{
	color: #434960;
	font-size: 24px;
	font-weight: 600;
}

.sb-full-screen-loader-spinner,
.sb-full-screen-loader-spinner:before,
.sb-full-screen-loader-spinner:after {
  border-radius: 50%;
}
.sb-full-screen-loader-spinner {
	color: #FE544F;
	position: relative;
	width: 190px;
	height: 190px;
	box-shadow: inset 0 0 0 6px;
	-webkit-transform: translateZ(0);
	transform: translateZ(0);
}
.sb-full-screen-loader-spinner:before,
.sb-full-screen-loader-spinner:after {
  position: absolute;
  content: '';
}
.sb-full-screen-loader-spinner:before {
  width: 100px;
    height: 200px;
    background: #f0f0f1;
    border-radius: 190px 0 0 190px;
    top: -1px;
    left: -8px;
    -webkit-transform-origin: 5.1em 5.1em;
    transform-origin: 100px 100px;
    -webkit-animation: sbi-loader-spinner 2s infinite ease 1.5s;
    animation: sbi-loader-spinner 2s infinite ease 1.5s;
}
.sb-full-screen-loader-spinner:after {
     width: 97px;
    height: 192px;
    background: #f0f0f1;
    border-radius: 0 190px 190px 0;
    top: -1px;
    left: 94px;
    -webkit-transform-origin: 1px 95px;
    transform-origin: 1px 95px;
    -webkit-animation: sbi-loader-spinner 2s infinite ease;
    animation: sbi-loader-spinner 2s infinite ease;
}
@-webkit-keyframes sbi-loader-spinner {
  0% {
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }
  100% {
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}
@keyframes sbi-loader-spinner {
  0% {
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }
  100% {
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}


/* Social Wall Popup */

.sbi-fb-extpp-social-wall-graphic {
	display: flex;
	flex-direction: column;
	align-items: center;
	padding-bottom: 78px
}
.sbi-graphic-row,
.sbi-fb-social-wall-between {
	position: relative;
}
.sbi-graphic-row-main {
	display: flex;
	justify-content: center;
	flex-direction: row;
	align-items: center;
}
.sbi-fb-social-wall-group {
	display: flex;
	flex-direction: column;
	align-items: center;
	width: 13%;
	margin: 1.5%;
}
.sbi-fb-social-wall-group p {
	margin: 0;
	text-align:center;
}
.sbi-fb-social-wall-between.sbi-fb-social-wall-between-instagram svg {
	margin-top:120%;
}
.sbi-fb-social-wall-between.sbi-fb-social-wall-between-facebook svg {
	margin-top:40%;
}
.sbi-fb-social-wall-between.sbi-fb-social-wall-between-twitter svg {
	margin-top:60%;
}
.sbi-fb-social-wall-end {
	position: absolute;
	right: -1%;
	top: 40%;
}
.sbi-graphic-bottom {
	width: 100%;
	display: flex;
	justify-content: center;
	flex-direction: row;
	align-items: center;
	margin-top:3%;
}
.sbi-all-in-same {
	display: flex;
	justify-content: center;
	flex-direction: row;
	align-items: center;
	height: 38px;
	width: 72.5%;
	/*background: #FFFFFF;*/
	/*box-shadow: 0px 6px 7px rgba(0, 26, 119, 0.07), 0px 3px 4px rgba(0, 26, 119, 0.06), 0px 1.80196px 2.25245px rgba(0, 26, 119, 0.05), 0px 0.749837px 0.937296px rgba(0, 26, 119, 0.1137);*/
	/*border-radius: 3px;*/
	border-top: 1px solid #ddd;
    border-bottom: 1px solid #ddd;
}
.sbi-fb-extpp-social-wall .sbi-fb-extpp-bottom {
	background: #F3F4F5;
}
.sbi-fb-social-wall-end-arrow {
	position: relative;
}
.sbi-arrow-head {
	position: absolute;
	bottom: 1%;
	left: 39%;
}

/* To Builder */
.sbi-fb-type-el[data-active="true"]:before {
	content: '';
	position: absolute;
	width: 24px;
	height: 24px;
	right: 0px;
	top: 0px;
	z-index: 2;
	border-radius: 0 0 0 2px;
	background: var(--cl-orange);
}

.sbi-fb-type-el {
	display: flex;
	flex-direction: column;
	align-items: flex-start;
	padding: 0;
	position: relative;
	background: #fff;
	border: 1px solid #D8D8D8;
	border-radius: 3px;
	cursor: pointer;
	box-sizing: border-box;
}

.sbi-fb-slctf-back span {
	font-weight: 600;
}

.sbi-fb-slctfd-action {
	padding: 16px 54px;
	position: fixed;
	bottom: 0;
	background: #f0f0f1;
	width: calc(100% - 160px);
}

.sbi-fb-wrapper {
	max-width: 100%;
	position: relative;
	margin: auto;
	color: #141B38;
}

.sbi-fb-create-ctn {
	float: left;
	margin-top: 104px;
	padding: 0 54px 65px;
}

.sbi-fb-extpp-btns a {
	height: 38px;
	cursor: pointer;
	position: relative;
	border-radius: 3px;
	font-style: normal;
	font-weight: 600;
	font-size: 14px;
	line-height: 160%;
	display: flex;
	justify-content: center;
	align-items: center;
	box-sizing: border-box;
}

.sbi-fb-extpp-lite-btn {
	display: flex;
	align-items: self-start;
	font-size: 14px;
	font-weight: 600;
	padding: 6px 11px;
	color: #0068A0;
	background: #fff;
	border: 1px solid #DCDDE1;
	margin-bottom: 14px;
}

.sbi-fb-extpp-lite-btn svg {
    fill: currentColor;
    width: 17px;
    height: 16px;
    float: left;
    margin-right: 8px;
    margin-top: 4px;
}

.sbi-fb-stp-src-type .sbi-fb-chbx-round {
	margin-right: 8px;
	margin-top: 2px;
}

#sbi-builder-app .sbi-fb-stp-src-type {
	display: flex;
	justify-content: center;
	align-content: center;
	font-size: 14px;
	font-weight: 400;
	cursor: pointer;
	margin-left: 20px;
	color: #434960;
}

.sbi-csz-name-ed-btn {
	width: 28px;
	height: 28px;
	cursor: pointer;
	margin: 0 10px;
	background: #E8E8EB;
	border: 1px solid #E8E8EB;
	outline: none;
	border-radius: 2px;
}
.sbi-csz-name-ed-btn svg {
	width: 16px;
	height: 14px;

	fill: #141B38;
	float: left;
	margin-left: -1px;
}

.sbi-csz-name-ed-btn:focus, .sbi-csz-name-ed-btn:hover {
	outline: none;
	background-color: #d9d9dc;
	border-color: #d9d9dc;
}

.sb-preview-chooser-btn, .sb-preview-chooser-btn:focus {
	width: 38px;
	height: 32px;
	display: flex;
	justify-content: center;
	align-items: center;
	cursor: pointer;
	float: left;
	border: 0;
	background: unset;
	margin: 0 1px;
	outline: none;
}

.sb-customizer-sidebar {
	position: fixed;
	z-index: 100;
	width: 375px;
	box-shadow: 4px 0px 14px rgb(0 0 0 / 5%), 1px 0px 4px rgb(0 0 0 / 10%);
	background: #fff;
	left: 160px;
	top: 98px;
	overflow: auto;
	bottom: 0px;
	padding-bottom: 50px;
}

.sb-customizer-sidebar-breadcrumb a, .sb-customizer-sidebar-breadcrumb span {
	display: inline-block;
	position: relative;
	cursor: pointer;
	color: #434960;
	text-transform: uppercase;
	font-size: 10px;
	line-height: 160%;
	letter-spacing: 0.02em;
	padding: 0 5px 0 4px;
	height: 20px;
	font-weight: 700;
}
.sb-control-elem-ctn {
	display: flex;
	color: #434960;
	padding: 20px 20px;
}
.sb-control-toggle-icon svg {
	width: 13px;
	float: left;
	fill: #434960;
}
.sb-control-elem-ctn .sb-control-toggle-elm[data-active="true"] {
	display: flex;
	color: #141B38;
	padding: 0 15px;
}
.sb-control-elem-ctn .sb-control-toggle-elm[data-active="true"] svg {
	fill: #141B38;
}

.sb-control-elem-ctn[data-layout="block"] input[type="number"] {
	height: 36px;
	background: #FFFFFF;
	border-radius: 2px 0 0 2px !important;
}

.sb-control-input-info {
	display: flex;
	justify-content: center;
	align-items: center;
	background: #F3F4F5;
	padding: 0 8px;
	font-weight: normal;
	font-size: 14px;
	line-height: 160%;
	color: #434960;
	border: 1px solid #D0D1D7;
	border-left: none;
	border-radius: 0 2px 2px 0 !important;
}
.sb-control-elem-output input[type="text"],
.sb-control-elem-output input[type="number"]{
	border-radius: 1px !important;
}

.sb-control-colorpicker-ctn .minicolors-theme-default.minicolors-position-right .minicolors-swatch {
	left: auto;
	right: 1px;
	top: 1px;
	width: 35px;
	height: 35px;
	background: #F9F9FA;
	border: 0px;
	border-radius: 2px;
}
.sb-control-colorpicker-ctn[data-picker-style="reset"] .minicolors-input {
	background: #F3F4F5!important;
	border: 1px solid #DCDDE1!important;
	height: 38px;
}

.sb-customizer-sidebar-breadcrumb svg {
	position: relative;
	left: auto;
	top: auto;
	margin-right: 9px;
}

.sb-customizer-sidebar-breadcrumb {
	display: flex;
	justify-content: flex-start;
	align-items: center;
	margin-bottom: 15px;
}

.sbi-fb-srcs-info-item span {
	font-size: 13px;
	line-height: 1.5em;
	color: #434960;
	font-weight: 400;
	display: inline-block;
	word-break: break-all;
	width: calc(100% - 80px);
	padding: 0 15px;
	box-sizing: border-box;
}

/* Onboarding */
#sbi-builder-app .sb-onboarding-tooltip {
	display: none;
	position: absolute;
	min-height: auto;
	width: 460px;
	max-width: 100%;
	padding: 0;
	border-radius: 2px;
	z-index: 9999;
}
#sb-onboarding-tooltip-multiple-2,
#sb-onboarding-tooltip-multiple-3{
	width: 528px;
}
#sb-onboarding-tooltip-single-2 {
	width: 402px;
}
.sb-onboarding-active .sb-onboarding-highlight .sbi-fb-btn.sbi-fb-btn-new,
.sb-onboarding-active .sb-positioning-wrap.sb-onboarding-highlight,
.sb-onboarding-active .sbi-fd-lst-bigctn .sbi-table-wrap.sb-onboarding-highlight,
.sb-onboarding-active .sbi-fb-lgc-ctn.sb-onboarding-highlight,
.sb-onboarding-active .sbi-fb-lgc-ctn .sbi-legacy-table-wrap.sb-onboarding-highlight,
.sb-onboarding-active .sb-customizer-sidebar-tab.sb-onboarding-highlight,
.sb-onboarding-active .sbi-fb-header.sb-onboarding-highlight{
	position: relative;
	z-index: 100000;
}
.sb-onboarding-active .sbi-fd-legacy-feed-toggle {
	display: none;
}
.sbi-legacy-table-wrap.sb-onboarding-highlight {
	clear: both;
}
#sbi-builder-app .sb-onboarding-tooltip-1 {
	top: 50px;
	left: 8px;
}
#sb-onboarding-tooltip-single-2 {
	bottom: -179px;
	top: auto;
	left: 68%;
	margin-left: -201px;
}
#sb-onboarding-tooltip-multiple-2,
#sb-onboarding-tooltip-multiple-3{
	top: -200px;
	left: 20%;
}
#sb-onboarding-tooltip-multiple-3 {
	top: -210px;
}
#sbi-builder-app .sb-onboarding-tooltip  .sbi-fb-wrapper {
	display: flex;
	justify-content: flex-end;
}
#sbi-builder-app .sb-positioning-wrap {
	width: 432px;
}
#sbi-builder-app .sb-onboarding-tooltip .sbi-fb-popup-cls {
	position:absolute;
	width: 12px;
	height: 12px;
	top: 12px;
	right: 12px;
}
#sbi-builder-app .sb-onboarding-tooltip .sbi-fb-popup-cls svg {
	width: 12px;
	height: 12px;
}
#sbi-builder-app .sb-onboarding-tooltip h3 {
	font-size: 16px;
	color: #141B38;
	line-height: 160%;
	font-weight: 600;
	margin: 0;
}
#sbi-builder-app .sb-onboarding-step {
	font-style: normal;
	font-weight: normal;
	font-size: 13px;
	line-height: 160%;
	color: #434960;
	margin: 2px 0 20px;
	display: block;
}
.sbi-onboarding-next,
.sbi-onboarding-previous{
	color: #353A41;
	background: #F3F4F5;
	border: 1px solid #DCDDE1;
	margin-left: 10px;
}
.sbi-onboarding-previous[data-active=false]{
	pointer-events: none;
}
#sbi-builder-app .sb-onboarding-tooltip .sbi-fb-hd-btn {
	margin-right: 0;
}
#sbi-builder-app .sb-onboarding-tooltip .sbi-fb-hd-btn i {
	margin: 0;
}
.sbi-onboarding-finish{
	margin-left: 10px;
	padding: 0 32px;
}
#sbi-builder-app .sb-onboarding-tooltip .sbi-fb-hd-btn[data-active="false"] {
	background-color: #e8e8eb;
	color: #8c8f99;
}
#sbi-builder-app .sb-onboarding-tooltip .sbi-fb-hd-btn[data-active="false"]:hover {
	cursor: default;
}
.sb-step-counter-wrap span {
	font-style: normal;
	font-weight: bold;
	font-size: 12px;
	line-height: 160%;
	letter-spacing: 0.05em;
	text-transform: uppercase;
	color: #141B38;
}
#sbi-builder-app .sb-onboarding-tooltip .sb-pointer {
	position: absolute;
	left: 50px;
	top: -10px;
}
#sbi-builder-app .sb-pointer.sb-bottom-pointer {
	top: auto;
	bottom: -14px;
}
#sb-onboarding-tooltip-single-2 .sb-pointer {
	left: 193px;
}
#sb-onboarding-tooltip-multiple-2:before,
#sb-onboarding-tooltip-multiple-3:before{
	bottom: -8px;
}
#sbi-builder-app .sb-onboarding-top-row {
	padding: 20px 44px 0 24px;
}
#sbi-builder-app .sb-onboarding-bottom-row {
	display: flex;
	flex-direction: row;
	justify-content: space-between;
	align-items: center;
	padding: 8px 16px 12px 24px;
}


#sbi-builder-app #sb-onboarding-tooltip-customizer-1 {
	position:absolute;
	right: 210px;
	left: auto;
	top:  84px;
}

#sbi-builder-app #sb-onboarding-tooltip-customizer-1 .sb-pointer {
	right: 110px;
	left: auto;
}

.sb-customizer-sidebar.sb-onboarding-highlight {
	z-index: 9999;
	overflow: visible;

}
#sb-onboarding-tooltip-customizer-2,
#sb-onboarding-tooltip-customizer-3 {
	z-index: 100000;
	top: 80px;
}
#sb-onboarding-tooltip-customizer-3 {
	left: 171px;
}
#sbi-builder-app #sb-onboarding-tooltip-customizer-3 .sb-pointer{
	left: 100px;
}

.sb-onboarding-active .sb-customizer-sidebar-tab-ctn,
.sb-onboarding-active .sb-customizer-sidebar-sec-ctn,
.sb-onboarding-active .sbi-csz-header .sbi-csz-header-insider{
	/*pointer-events: none !important;*/
}

/* Misc Tooltip */
.sbi-not-sure-tooltip {
	left: -360px;
	top: -110px;
}
.sbi-not-sure-tooltip .sb-pointer {
	bottom: -17px !important;
	left: 390px;
}
.sbi-not-sure-tooltip {
	display: none;
}
.sbi-not-sure-tooltip-icon:hover .sbi-not-sure-tooltip {
	display: block;
	padding: 12px 16px 16px;
	width: 400px;
}

/*
	Add new Source Popup
*/
.sbi-fb-popup-cls{
    height: 14px;
    width: 14px;
    position: absolute;
    cursor: pointer;
    right: 17px;
    top: 17px;
    z-index: 3;
}
.sbi-fb-center-boss{
    display: flex;
    justify-content: center;
    align-items: center;
}
.sbi-fb-left-boss{
	display: flex;
	justify-content: left;
	align-items: center;
}


.sbi-fb-embed-popup,.sbi-fb-dialog-popup{
    width: 575px;
}
.sbi-fb-dialog-popup{
    min-height: 250px;
}

.sbi-fb-source-pp-customizer .sbi-fb-srcslist-ctn{
    grid-template-columns: 32.7% 32.7% 32.7%;
    grid-column-gap: 1%;
}
.sbi-fb-srcs-desc{
    width: 55%;
    font-style: normal;
    font-weight: normal;
    font-size: 14px;
    line-height: 160%;
    color: #434960;
    box-sizing: border-box;
    margin-bottom: 29px;
}
#sbi-builder-app .sbi-fb-source-pp-customizer h3{
    margin-bottom: 6px;
}
#sbi-builder-app .sbi-fb-embed-popup h3 {
    padding: 23px 25px 0;
    margin-bottom: 0;
}
#sbi-builder-app .sbi-fb-embed-popup .sbi-fb-embed-step-2 h3 {
    padding: 0 0 0 29px;
}
.sbi-fb-feedtypes-popup h5{
    font-size: 26px;
    padding: 0 20px;
    margin-top: 25px;
    margin-bottom: 30px;
}

.sbi-fb-feedtypes-pp-ctn .sbi-fb-adv-types .sbi-fb-types-list{
    margin-bottom: 30px !important
}
.sbi-fb-feedtypes-popup .sbi-fb-types, .sbi-fb-feedtypes-popup .sbi-fb-adv-types {
    padding: 0;
}
.sbi-fb-source-top{
    padding: 22px 19px 28px;
}
#sbi-builder-app .sbi-fb-source-step1 .sbi-fb-source-top h3{
    margin-bottom: 40px;
}
#sbi-builder-app .sbi-fb-source-step3 .sbi-fb-source-top h3 {
    margin-bottom: 30px;
}
#sbi-settings .sbi-fb-source-step4 .sbi-fb-stp1-elm-desc {
	max-width: 470px;
	margin-bottom: 26px;
}
#sbi-settings .sb-step-text {
	color: #434960;
}
.sbi-fb-srcs-update{
    background: var(--cl-orange);
    font-weight: 600;
    font-size: 14px;
    line-height: 160%;
    height: 38px;
}
.sbi-fb-srcs-update svg{
    margin-right: 10px;
}
.sbi-fb-srcs-update-footer{
    display: flex;
    align-items: center;
    border-top: 1px solid #DCDDE1;
    background: #F9F9FA;
}
.sbi-fb-srcs-update-footer-txt{
    font-size: 18px;
    line-height: 1.7em;
}
.sbi-fb-srcs-update-footer-btn{

}
#sbi-builder-app .sbi-fb-srcs-update-footer-btn a{
    height: 48px;
    padding: 0 20px 0 24px;
}
.sbi-fb-srcs-update-footer-image{
    width: 26%;
    margin-right: 7%;
}
#sbi-builder-app .sbi-fb-srcs-update-footer-btn a svg {
    position: relative;
    top: auto;
    right: auto;
    width: auto;
    height: auto;
    margin-left: 15px;
}
.sbi-fb-srcs-update-footer-image svg{
    width: 100%;
    height: auto;
    float: left;
}
.sbi-fb-srcs-update-footer-txt{
    box-sizing: border-box;
    display: flex;
    justify-content: center;
    width: 38%;
    margin-right: 7%;
}
.sbi-fb-stp1-elm{
    margin-bottom: 20px;
    float: left;
    display: flex;
}
.sbi-fb-source-top .sbi-fb-stp1-elm:first-of-type{
    justify-content: center;
    align-items: center;
    padding-bottom: 24px;
    border-bottom: 1px solid #D0D1D7;
}
.sbi-fb-stp1-event{
    padding-bottom: 0px!important;
    border-bottom: none!important;
    padding-right: 11%;
    align-items: flex-start!important;
}
.sbi-fb-stp1-elm-desc > a{
    font-weight: 600;
    text-decoration: underline;
    color: #2A65DB;
}

.sbi-fb-stp-src-ctn{
    display: flex;
    justify-content: center;
    align-content: center;
}
#sbi-builder-app .sbi-fb-stp-src-type{
    display: flex;
    justify-content: center;
    align-content: center;
    font-size: 13px;
    font-weight:400;
    cursor: pointer;
    margin-left: 20px;
    color: #434960;
}
.sbi-fb-stp-src-type .sbi-fb-chbx-round{
    margin-right: 8px;
    margin-top: 2px;
}
.sbi-fb-stp-src-type[data-active="true"]{
    font-weight:600;
    color: #141B38;
}
.sbi-fb-stp1-elm-ic{
    width: 28px;
    height: 28px;
    border-radius: 35px;
    background: #F3F4F5;
    display: flex;
    justify-content: center;
    align-items: center;
    font-weight: 500;
    flex: none;
    transform: translate(0, -2px);
}
.sbi-fb-stp1-elm-txt{
    margin-left: 16px;
    margin-right: 21%;
    width: 100%;
}
.sbi-fb-stp1-elm-head{
    font-size: 14px;
    font-weight: 600;
    color: #141B38;
}
.sbi-fb-stp1-elm-desc{
    font-size: 13px;
    margin-top: 4px;
    line-height: 150%;
    color: #888a8e;
}
.sbi-fb-stp1-event .sbi-fb-stp1-elm-desc {
    margin-top: 4px;
}
.sbi-fb-stp1-elm-act .sb-btn.sbi-fb-stp1-connect{
    padding: 8px 20px 8px 40px;
    background: #0068A0;
    position: relative;
    font-style: normal;
    font-weight: 600;
    font-size: 14px;
    line-height: 160%;
}
.sbi-fb-stp1-connect svg{
    position: absolute;
    top: 10px;
    right: 0;
    bottom: 0;
    left: 13px;
    width: 17px;
    fill: currentColor;
}
.sbi-fb-stp1-connect path{
	fill: currentColor;
}
.sbi-fb-source-bottom{
    padding: 18px 20px;
    background: #F3F4F5;
    display: flex;
    align-items: center;
    border-radius: 0 0 2px 2px;
}
.sbi-manual-question {
    display: flex;
    align-items: center;
}
.sbi-manual-question svg{
    margin-right: 23px;
    float: left;
}
.sbi-fb-source-btm-hd{
    font-size: 14px;
    font-weight: 500;
}
.sbi-fb-src-add-manual{
    margin-left: auto;
    padding: 8px 20px 8px 40px;
}
.sbi-fb-src-add-manual svg{
    position: absolute;
    top: 11px;
    right: 0;
    bottom: 0;
    left: 13px;
    fill: currentColor;
}
#sbi-builder-app .sbi-fb-source-step2 .sbi-fb-source-top h3 {
    margin-bottom: 35px;
}
#sbi-builder-app .sbi-fb-source-step4 .sbi-fb-source-top h3,
#sbi-builder-app .sbi-fb-source-step4 .sbi-fb-source-top h4{
	margin-bottom: 8px;
}
#sbi-builder-app .sbi-fb-source-step4 .sbi-fb-stp1-elm-desc {
	max-width: 470px;
	margin-bottom: 26px;
}
.sbi-fb-src-add-manual i{
    font-size: 12px;
    margin-right: 10px;
}
.sbi-fb-src-back-top{
    float: left;
    font-style: normal;
    font-weight: 600;
    font-size: 12px;
    line-height: 160%;
    letter-spacing: 0.05em;
    text-transform: uppercase;
    cursor: pointer;
    color: #141B38;
    margin-bottom: 10px;
    display: flex;
    justify-content: center;
    align-items: center;
}
.sbi-fb-src-back-top svg{
    margin-left: 6px;
    margin-right: 10px;
}

.sbi-fb-source-account-info{
    background: #F3F4F5;
    padding: 6px 6px 6px 12px;

    display: flex;
    align-items: center;
}
.sbi-fb-source-list{
    display: grid;
    grid-template-columns: 49.4% 49.4%;
    grid-column-gap: 1.2%;
    margin-top: 8px;
}
.sbi-fb-source-popup.sbi-fb-popup-inside .sbi-fb-srcs-item {
    box-sizing: border-box;
    margin: 1.2% 0;
}

.sbi-fb-source-account-info img{
    width: 23px;
    height: 23px;
    border-radius: 23px;
    background: #fafafa;
    margin-left: 10px;
    margin-right: 10px;
}
.sbi-fb-source-account-info strong{
    color: #141B38;
}
.sbi-fb-src-change{
    padding: 7px 20px 7px 40px;
    margin-left: auto;
}
.sbi-fb-src-change svg{
    position: absolute;
    top: 11px;
    right: 0;
    bottom: 0;
    left: 13px;
    fill: currentColor;
}
.sbi-fb-source-btn{
    margin-top: 10px;
    color: #fff;
    display: flex;
    justify-content: center;
    align-items: center;
    background: #0068A0;
    cursor: pointer;
    padding: 8px 20px;
    line-height: 160%;
    border-radius:  2px;
    font-size: 14px;
    font-weight: 500;
    border: none;
	text-decoration: none;
}
#sbi-builder-app .sbi-fb-source-step3 .sbi-fb-source-btn {
    margin-top: 45px;
}
#sbi-builder-app .sbi-fb-source-step3  .sbi-fb-src-back-top {
    margin-bottom: 4px;
}
.sbi-fb-icon-success{
    position: relative;
    width: 30px;
    height: 20px;
    margin-right: 10px;
}
.sbi-fb-icon-success:before{
    content: '';
    position: absolute;
    width: 13px;
    height: 5px;
    top: 4px;
    border-left: 3px solid currentColor;
    border-bottom: 3px solid currentColor;
    -webkit-transform: rotate(-45deg);
    transform: rotate(-45deg);
}

.sbi-fb-icon-cancel{
    position: relative;
    width: 25px;
    height: 25px;
    margin-right: 10px;
}
.sbi-fb-icon-cancel:before,
.sbi-fb-icon-cancel:after{
    content: '';
    position: absolute;
    width: 17px;
    height: 2px;
    top: 12px;
    left: 5px;
    background: currentColor;
    -webkit-transform: rotate(-45deg);
    transform: rotate(-45deg);
}
.sbi-fb-icon-cancel:after{
    -webkit-transform: rotate(45deg);
    transform: rotate(45deg);
}

.sbi-fb-source-inp-label,.sbi-fb-wh-label{
    font-size: 14px;
    color: #434960;
}
.sbi-fb-source-mnl-type .sbi-fb-stp-src-ctn{
    justify-content: flex-start;
    margin-bottom: 22px;
    margin-top: 8px;
}
#sbi-builder-app .sbi-fb-source-mnl-type .sbi-fb-stp-src-ctn .sbi-fb-stp-src-type{
    margin-left: 0;
    margin-right: 32px
}
.sbi-fb-source-inp,.sbi-fb-wh-inp{
    border-radius: 0px !important;
    outline: none ;
    margin-top: 2px;
    border: 1px solid #D0D1D7 !important;
    margin-bottom: 20px;
    height: 38px;
    line-height: 38px;
}


.sbi-fb-source-popup h1 {
    font-style: normal;
    font-weight: 600;
    font-size: 36px;
    line-height: 125%;
    color: #141B38;
    margin: 0;
}
.sbi-fb-source-popup h2 {
    font-style: normal;
    font-weight: 600;
    font-size: 32px;
    line-height: 125%;
    margin: 0;
}
.sbi-fb-source-popup h3 {
    font-style: normal;
    font-weight: 600;
    font-size: 24px;
    line-height: 120%;
    margin: 0 0 40px 0;
    letter-spacing: 0;
}

.sbi-fb-source-popup h4 {
    font-style: normal;
    font-weight: 600;
    font-size: 18px;
    line-height: 140%;
    margin: 0 0 4px 0;
}

.sbi-fb-slctf-back svg,
.sbi-fb-slctf-nxt svg,
.sbi-fb-source-btn-next svg{
	width: 6px;
	height: 10px;
}

.sbi-fb-source-btn-next span {
	display: inline-block;
	margin-right: 15px;
	line-height: 160%;
}

.sb-single-step {
	display: flex;
	align-items: center;
	margin-bottom: 16px;
}
.sb-step-number {
	display: flex;
	align-items: center;
	justify-content: center;
	background: #F3F4F5;
	border-radius: 30px;
	width: 24px;
	height: 24px;
	margin-right: 16px;
}

#sbi-builder-app .sb-step-text {
	color: #434960;
}

.sb-two-buttons-wrap {
	box-sizing:border-box;
	float: left;
	display: flex;
	justify-content: center;
	align-items: center;
	width: 100%;
	padding: 0 19px 28px;
}
.sb-two-buttons-wrap .sbi-fb-source-btn {
	margin-top: 0;
}
.sbi-fb-source-step4 .sbi-fb-source-top {
	padding: 24px 54px 32px;
	text-align: center;
}
.sb-two-buttons-wrap > button:first-of-type {
	margin-right: 12px;
}

.sb-two-buttons-wrap .sb-question-circle {
	margin-right: 9px;
}

#sbi-group-app-tooltip{
	cursor: pointer;
}
.sbi-group-app-screenshot{
	display: none;
	position: absolute;
	bottom: 92px;
	left: 50%;
	margin-left: -325px;
	z-index: 10;
	border: 1px solid rgba(0,0,0,0.3);
	box-shadow: 0 0 0 5000px rgb(0 0 0 / 20%);
	border-radius: 5px;
}
#sbi-group-app-tooltip:hover .sbi-group-app-screenshot{
	display: block !important;
}
.sbi-fb-source-btn[data-active = false] {
	pointer-events: none !important;
}
.sbi-fb-source-btn[data-active = false] svg path{
	fill: #8C8F9A !important;
}

.sb-directions-p {
	margin-bottom: 40px;
}

.sbi-fb-srcs-item-avatar{
    display: flex;
    width: 42px;
    height: inherit;
    justify-content: center;
    align-items: center;
}
.sbi-fb-srcs-item-avatar img{
    width: 36px;
    height: 36px;
    border-radius: 50%;
    background: #eee;
}
.sbi-fb-srcs-item-inf{
    width: 100%;
    height: inherit;
    display: flex;
    justify-content: center;
    flex-direction: column;
}
.sbi-fb-srcs-item-name{
    font-weight: 600;
}
.sbi-fb-srcs-item-name-event{
    font-size: 10px;
    font-weight: 500;
    color: #777;
}

.sbi-fb-srcs-item-type{
    color: #434960;
    font-weight: 600;
    text-transform: uppercase;
    display: flex;
    align-items: center;
}
.sb-source-error-wrap {
	display: flex;
	justify-content: left;
	align-items: center;
	margin-left: 9px;
}

.sb-source-error-wrap span {
	font-weight: 600;
	font-size: 12px;
	line-height: 160%;

	color: #D72C2C;
	margin-left: 5px;
}
.sbi-fb-source-popup .sb-source-error-wrap {
	margin-left: 0;
}
.sb-source-error-wrap a {
	margin-left: 8px;
	font-weight: 600;
	font-size: 12px;
	line-height: 160%;
	text-decoration-line: underline;

	color: #0068A0;
}
.sb-is-group .sb-details-wrap {
    position: relative;
    display: flex;
    align-items: center;
    padding: 2px 6px 2px 22px;
    background: #F3F4F5;
    border-radius: 2px;
}
 .sb-highlight-admin .sb-details-wrap {
    padding: 2px 6px 2px 17px;

    background: #E2F5FF;
    color: #0068A0;
}
.sb-highlight-admin span {
    color: #0068A0;
}
.sbi-fb-srcs-item-type i{
    margin-right: 5px;
}
.sbi-fb-srcs-item-type svg{
    float: left;
    margin-right: 5px;
    fill: currentColor;
    height: 8px;
}
.sb-has-details .sbi-fb-srcs-item-type svg{
    position: absolute;
    top: 5px;
    right: 0;
    bottom: 0;
    left: 5px;
    height: 9px;
}
.sb-has-details .sbi-fb-srcs-item-type.sb-highlight-admin svg {
    top: 6px;
}
[data-type="page"] .sbi-fb-srcs-item-type svg{
    height: 9px;
}
.sbi-fb-srcs-back{
    margin-right: auto;
}
.sbi-groups-connect-actions {
	float: left;
}
.sb-alert,
.sbi-error-ctn .sb-alert{
	position: relative;
	margin: 0 0 38px;
	padding: 12px 12px 12px 44px;
	word-break: break-word;
	background: #FFEFCC
}
.sbi-connection-error .sb-alert {
  margin: 20px;
}
.sbi-connect-actions .sbi-fb-source-btn {
	margin-top: 0;
}
.sbi-connect-actions {
	float: left;
}
.sbi-connect-actions.sb-alerts-wrap .sb-alert {
	clear: left;
}
.sb-alerts-wrap .sb-alert svg,
.sb-alert svg,
.sbi-error-ctn .sb-alert svg{
	position: absolute;
	top: 13px;
	left: 13px;
}
.sb-alert span,
.sbi-error-ctn .sb-alert span{
	color: #995C00;
}
#sbi-builder-app .sb-alert span a,
.sbi-error-ctn .sb-alert span a{
	color: #663D00;
	font-weight: 600;
	text-decoration: underline;
}
#sbi-settings .sbi-fb-source-step1.sbi-has-alert .sbi-fb-source-top h3 {
	margin-bottom: 12px;
}
.sbi-groups-connect-actions .sbi-fb-source-btn {
	margin-top: 0;
}
/* Install Plugin Modal on Select Source Flow */
.sbi-install-plugin-modal {
	max-width: 580px;
}
.sbi-install-plugin-body .sbi-install-plugin-header {
	height: 106px;
	background: #F3F4F5;
	padding: 20px;
	display: flex;
	box-sizing: border-box;
    flex-wrap: wrap;
    align-items: center;
}
.sbi-install-plugin-body .sbi-install-plugin-header .sb-plugin-image {
	background-color: #fff;
    box-shadow: 0px 1px 2px rgb(0 0 0 / 5%), 0px 4px 5px rgb(0 0 0 / 5%);
    border-radius: 2px;
    padding: 15px;
    max-height: 66px;
    box-sizing: border-box;
	margin-right: 24px;
}
.sbi-install-plugin-body .sbi-install-plugin-header h3 {
	font-size: 18px !important;
	line-height: 25px !important;
	display: flex;
    align-items: center;
}
.sbi-install-plugin-body .sbi-install-plugin-header h3 span {
	color: #fff;
	background: #59AB46;
	border-radius: 2px;
	font-size: 10px;
	line-height: 16px;
	letter-spacing: 0.08em;
	text-transform: uppercase;
	padding: 0px 6px;
	margin-left: 10px;
}
.sbi-install-plugin-body .sbi-install-plugin-header p {
	display: flex;
	font-size: 12px;
	line-height: 18px;
	color: #434960;
	margin: 5px 0 0 0;
}
.sbi-install-plugin-body .sbi-install-plugin-header p .sb-author-logo {
	margin-right: 8px;
}

.sbi-install-plugin-body .sbi-install-plugin-content {
	padding: 20px 20px 32px 107px;
}
.sbi-install-plugin-body .sbi-install-plugin-content p {
	margin: 0px;
	font-size: 14px;
	line-height: 22px;
	color: #434960;
    padding-right: 20px;
}
.sbi-install-plugin-body .sbi-install-plugin-content .sbi-install-plugin-btn {
	display: flex;
	align-items: center;
	justify-content: center;
	height: 38px;
	padding: 8px 20px;
	box-sizing: border-box;
	transition: all .15s ease-in-out;
	border-radius: 2px;
	width: 100%;
	margin-top: 28px;
	border: none;
	font-size: 14px;
	font-weight: 600;
	line-height: 160%;
	cursor: pointer;
}
.sbi-install-plugin-body .sbi-install-plugin-content .sbi-btn-orange:disabled {
	color: #8C8F9A !important;
	background: #E8E8EB !important;
	cursor: not-allowed;
}
.sbi-install-plugin-body .sbi-install-plugin-content .sbi-install-plugin-btn span {
	height: 20px;
	width: 20px;
	margin-right: 5px;
}
.sbi-fb-mr-fd-list button {
	cursor: pointer;
	display: flex;
	justify-content: space-between;
}

/* Tooltips */
.sb-control-elem-tltp{
	margin-left: 10px;
	position: relative;
}
.sb-control-elem-tltp-icon{
	float: left;
	cursor: pointer;
}
.sb-control-elem-tltp-icon svg{
	width: 14px;
	float: left;
}
@media (min-width: 768px) and (max-width: 1079px) {
	.sbi-fb-mr-fd-img {
		width: 37%;
	}
	.sbi-fb-mr-fd-list button:not(:last-child) {
		margin-bottom: 8px;
	}
}
@media (min-width: 768px) and (max-width: 1023px) {
	.sbi-fb-srcslist-ctn,
	.sbi-fb-source-pp-customizer .sbi-fb-srcslist-ctn {
		grid-template-columns: 49.2% 49.2%;
		gap: 2%;
		margin-top: 8px;
	}
	.sbi-fb-slctfd-action {
		width: 100%;
	}
	.sbi-fb-srcs-item {
		margin-top: 3px;
	}
	.sbi-manual-question {
		max-width: 390px;
	}
	.sbi-fb-feedtypes-popup {
		left: 0;
		max-width: calc(100% - 60px);
	}
	.sbi-fb-feedtypes-pp-ctn.sbi-fb-center-boss {
		top: 40px;
	}
}

@media (max-width: 767px) {
	.sbi-fb-feedtypes-pp-ctn.sbi-fb-center-boss {
		align-items: baseline;
	}
	.sbi-fb-feedtypes-popup {
		max-width: calc(100% - 40px);
		top: 50px;
	}
	#sbi-builder-app .sb-positioning-wrap {
		width: 130px;
	}
	.sbi-fb-embed-popup, .sbi-fb-dialog-popup {
        width: 100%;
		max-width: calc(100% - 40px);
    }
    .sbi-fb-popup-inside {
        left: 0;
    }
	.sbi-fb-extensions-pp-ctn .sbi-fb-popup-inside.sbi-fb-extensions-popup,
	.sbi-fb-source-ctn .sbi-fb-popup-inside  {
		max-width: calc(100% - 40px);
	}
	.sbi-fb-source-ctn.sbi-fb-center-boss,
	.sbi-fb-extensions-pp-ctn.sbi-fb-center-boss{
		align-items: baseline;
	}
	.sbi-fb-extensions-pp-ctn.sbi-fb-center-boss .sbi-fb-popup-inside{
		top: 50px;
	}
	.sbi-extension-bullet-list {
		grid-template-columns: 100%;
	}
	.sbi-fb-extpp-bottom {
		padding: 20px;
	}
	.sbi-fb-extpp-info {
		width: 100%;
		padding-right: 20px;
		padding-left: 20px;
	}
	.sbi-fb-extpp-img {
		display: none;
	}
	.sbi-fb-extpp-top {
		height: 195px;
	}
	.sbi-fb-source-pp-customizer .sbi-fb-srcslist-ctn {
		grid-template-columns: 99.2%;
		gap: 2%;
		margin-bottom: 50px;
	}
	.sbi-fb-source-account-info {
		flex-wrap: wrap;
	}
	.sbi-fb-source-account-info > span {
		width: 100%;
	}
	.sbi-fb-source-account-info img {
		margin-left: 0;
	}
	.sbi-fb-source-btn {
		margin-top: 25px;
	}
	.sbi-fb-source-bottom {
		flex-wrap: wrap;
		margin-top: 0;
	}
	.sbi-fb-src-add-manual {
		margin-left: 0;
		margin-top: 15px;
	}
	.sbi-fb-types-list {
		grid-template-columns: 100%;
	}
	.sbi-fb-types-list .sbi-fb-type-el {
		margin-bottom: 10px;
	}
	.sbi-fb-create-ctn {
		margin-top: 90px;
		padding: 0 20px 65px;
	}
	#sbi-builder-app h1 {
		font-size: 32px;
	}
	.sbi-fb-social-wall-between,
	.sbi-fb-social-wall-end {
		display: none;
	}
	.sbi-graphic-row-main {
		justify-content: space-between;
	}
	.sbi-fb-slctfd-action {
		width: 100%;
	}
	.sbi-fb-mr-feeds {
		flex-wrap: wrap;
		margin-bottom: 60px;
	}
	.sbi-fb-mr-feeds .sbi-fb-mr-fd-content,
	.sbi-fb-slctsrc-content, .sbi-fb-section-wh-insd,
	.sbi-fb-types, .sbi-fb-adv-types {
		padding: 20px;
	}
	.sbi-fb-source-ctn .sbi-fb-popup-inside.sbi-install-plugin-modal {
		top: 0;
	}
	.sbi-fb-mr-fd-list button:not(:last-child) {
		margin-bottom: 8px;
	}
	.sbi-fb-srcs-item {
		margin-top: 3px;
	}

    #sbi-builder-app h2 {
        font-size: 26px;
    }
	.sbi-fb-srcs-desc {
		width: 100%;
	}
	.sbi-fb-source-list {
		grid-template-columns: 99%;
	}
	.sb-dialog-ctn.sbi-fb-center-boss .sbi-fb-popup-inside {
		max-width: calc(100% - 40px);
	}
}

@media (max-width: 480px) {
	.sbi-fb-source-ctn .sbi-fb-popup-inside {
		top: 45px;
		margin-bottom: 46px;
	}
	.sbi-csz-header-insider .sb-button-standard {
		font-size: 13px;
		padding: 8px 15px 8px 34px;
	}
	.sbi-csz-header-insider .sb-button-standard svg {
		width: 13px;
		height: 13px;
		left: 14px;
		top: 10px;
	}
	.sbi-fb-header.sbi-csz-header {
		height: 126px;
	}
	.sbi-csz-hd-name {
		height: 30px;
		margin-top: 5px;
	}
	.sb-customizer-ctn .sb-customizer-sidebar {
		margin-top: 120px;
	}
	.sb-fs-boss.sbi-fb-center-boss {
		overflow: scroll;
	}
	.sbi-fb-stp1-elm {
		flex-wrap: wrap;
	}
	.sbi-fb-stp1-elm-txt {
		margin-right: 0%;
		width: calc(100% - 50px);
	}
	.sbi-fb-stp1-elm .sbi-fb-stp-src-ctn.sbi-fb-stp1-elm-act {
		margin-left: 24px;
	}
	.sbi-fb-stp1-elm .sbi-fb-stp1-elm-act {
		margin-top: 10px;
		margin-left: 42px;
	}

}


.sb-control-elem-tltp-content{
	position: fixed;
	background: #fff;
	padding: 8px 11px;
	text-align: center;
	line-height: 1.5em;
	top: 0;
    left: 0;
	z-index: 999999999;
    -webkit-transform: translateX(-50%) translateY(-100%);
    transform: translateX(-50%) translateY(-100%);
	box-shadow: 0 2px 8px rgba(0, 0, 0, 0.19);
}


.sb-control-elem-tltp-content:before{
	content: '';
	position: absolute;
	left: 50%;
	bottom: -10px;
	border-top: 12px solid #fff;
	border-right: 12px solid transparent;
	border-left: 12px solid transparent;
	-webkit-transform: translateX(-50%);
	transform: translateX(-50%);
	z-index: 2;
}
.sb-control-elem-tltp-content:after{
	content: '';
    position: absolute;
    left: 50%;
    bottom: -12px;
    margin-left: 0px;
    border-top: 14px solid #eee;
    border-right: 15px solid transparent;
    border-left: 15px solid transparent;
    -webkit-transform: translateX(-50%);
    transform: translateX(-50%);
}

.sb-control-elem-tltp:hover .sb-control-elem-tltp-content{
	opacity: 1;
	visibility: visible;
	top: 24px;
	max-width: 300px;
    width: auto;
}
.sb-control-elem-tltp-txt{
	position: relative;
	max-width: 300px;
	width: max-content;
}
.sb-control-elem-tltp-txt[data-align="left"]{
	text-align: left!important;
}
.sb-tltp-black-link{
	text-decoration: underline!important;
	color: #000;
}
/* Social Wall Links */
.sb-social-wall-link-wrap {
	display: flex;
	font-size: 14px;
	margin: 10px 0;
}
.sb-social-wall-link {
	padding: 0 12px;
	border-right: 1px solid #ccc;
	color: #0068A0!important;
	line-height: 1;
}
.sb-social-wall-link a {
	text-decoration: none;
}
.sb-social-wall-link:first-child {
	padding-left: 0;
	border-right: 1px solid #ccc;
	color: #0068A0!important;
	line-height: 1;
}
.sb-social-wall-link:last-child {
	border-right: none;
}
.sb-social-wall-link a:focus {
    outline: none;
    box-shadow: none;
}




/*
	Add Source Loading
*/
.sb-fb-source-redirect-ld{
	text-align: center;
}
.sb-fb-source-redirect-ld div{
	display: inline-block;
	width: 32px;
	height: 32px;
	border-radius: 50px;
	margin: 0 10px;
	position: relative;
	background-color: #0096CC;
  	color: #0096CC;
	-webkit-animation: sb-source-redirect 1s infinite linear alternate;
  	 	 animation: sb-source-redirect 1s infinite linear alternate;
    -webkit-animation-delay: .5s;
    	animation-delay: .5s;
}

.sb-fb-source-redirect-ld div:before,
.sb-fb-source-redirect-ld div:after{
	content: '';
  	display: inline-block;
  	position: absolute;
  	top: 0;
}

.sb-fb-source-redirect-ld div:before{
	left: -45px;
  	width: 32px;
  	height: 32px;
  	border-radius: 50px;
  	background-color: #0096CC;
  	color: #0096CC;
  	-webkit-animation: sb-source-redirect 1s infinite alternate;
  		animation: sb-source-redirect 1s infinite alternate;
  	-webkit-animation-delay: 0s;
  		animation-delay: 0s;
}

.sb-fb-source-redirect-ld div:after{
	left: 45px;
 	width: 32px;
 	height: 32px;
 	border-radius: 50px;
 	background-color: #0096CC;
 	color: #0096CC;
 	-webkit-animation: sb-source-redirect 1s infinite alternate;
 		animation: sb-source-redirect 1s infinite alternate;
 	-webkit-animation-delay: 1s;
 		animation-delay: 1s;
}



@-webkit-keyframes sb-source-redirect {
   0% {background-color: #0096CC;}
  50%,100% {background-color: #B5E5FF;}
}
@keyframes sb-source-redirect {
  0% {background-color: #0096CC;}
  50%,100% {background-color: #B5E5FF;}
}


.sb-fb-source-redirect-info{
	text-align: center;
	margin-top: 50px;
}

.sb-fb-source-redirect-info strong{
	font-size: 18px;
}
.sb-fb-source-redirect-info p{
	color: #8C8F9A;
    padding: 0 24%;
    font-size: 16px;
    margin-bottom: 0px;
}

.sbi-fb-popup-inside[data-step="redirect_1"]{
    width: 575px;
    height: 320px;
    display: flex;
    justify-content: center;
    align-items: center;
}

/*Personal Account Popup*/
.sbi-personal-account-ctn .sbi-source-account-box{
	display: flex;
	justify-content: center;
	align-items: center;
}
.sbi-pers-account-icon{
	width: 64px;
	height: 64px;
	display: flex;
	justify-content: center;
	align-items: center;
	border: 7px solid #bfe8ff;
	border-radius: 50%;
	box-shadow: 0px 1px 2px rgba(0, 0, 0, 0.05), 0px 4px 5px rgba(0, 0, 0, 0.05);
}
.sbi-pers-account-icon svg{
	float: left;
}

.sbi-personal-account-ctn .sbi-fb-personal-step1 .sbi-fb-source-top {
    padding: 27px 54px 27px;
    text-align: center;
}
.sbi-fb-personal-step2 .sbi-fb-source-btn{
	margin-right: 0px !important;
}
.sbi-personal-account-ctn button{
	font-weight: 600;
}
.sbi-fb-personal-form{
	text-align: left;
	padding-top: 25px;
	padding-bottom: 20px;
}
.sbi-fb-personal-step2 > *{
	padding-left: 19px;
	padding-right: 19px;
}
.sbi-fb-personal-upload-btn{
	margin-top: 12px;
}
.sbi-fb-personal-upload-btn button span{
	padding: 0 5px;
}
.sbi-fb-personal-upload-btn button svg{
	float: left;
}
.sbi-fb-personal-upload-btn input[type="file"]{
	display: none;
}
.sbi-fb-personal-upload-btn > *{
	display: inline-flex;
	margin-right: 7px!important;
}


.sbi-fb-personal-textarea{
	margin-top: 16px;
}
.sbi-fb-personal-textarea > *{
	width: 100%;
	float: left;
}
.sbi-fb-personal-textarea label{
	font-size: 14px;
	color: #434960;
	margin-bottom: 10px;
	margin-top: 10px;
}
.sbi-fb-personal-textarea textarea{
	height: 77px;
	border: 1px solid #D0D1D7;
	border-radius: 1px;
	padding: 8px 2px 2px 12px;
	outline: none;
}

/*** 7.0 Settings CTA ***/
.sbi-settings-cta {
    background-color: #fff;
    padding: 24px 20px 20px;
    box-shadow: 0px 1px 2px rgb(0 0 0 / 5%), 0px 4px 5px rgb(0 0 0 / 5%);
    margin-top: 12px;
}
.sbi-settings-cta .sbi-cta-title,
.sbi-settings-cta .sbi-cta-head-inner {
    display: flex;
}
.sbi-settings-cta .sbi-cta-head-inner {
	justify-content: space-between;
	max-width: 820px;
}
.sbi-settings-cta .sbi-cta-head-inner .sbi-cta-btn a {
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    padding: 5px 12px 6px 20px;
    font-weight: 600;
    font-size: 14px;
    line-height: 160%;
    margin-top: 5px;
}
.sbi-settings-cta .sbi-cta-head-inner .sbi-cta-btn a svg {
	margin-left: 12px;
	width: 10px;
}
.sbi-settings-cta .sbi-plugin-title-bt {
	display: flex;
}
.sbi-settings-cta .sbi-plugin-title-bt .sbi-cta-btn {
	width: 177px;
}
.sbi-settings-cta .sbi-cta-title .sbi-plugin-logo {
    background: #FFFFFF;
    box-shadow: 0px 6.05242px 7.56552px rgba(0, 26, 119, 0.0415), 0px 3.39293px 4.24117px rgba(0, 26, 119, 0.04), 0px 1.80196px 2.25245px rgba(0, 26, 119, 0.0285), 0px 0.749837px 0.937296px rgba(0, 26, 119, 0.0337);
    border-radius: 2px;
    min-width: 60px;
    width: 65px;
    height: 65px;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-right: 30px;
    border: 1px solid #ecebeb;
}
#sbi-builder-app .sbi-settings-cta .sbi-cta-title .sbi-plugin-logo{
	width: 75px;
    height: 75px;
}
.sbi-settings-cta .sbi-cta-title .sbi-cta-discount-label {
    background: #FFEFCC;
    border-radius: 2px;
    padding: 0px 11px 0px 11px;
    font-weight: 600;
    font-size: 14px;
    line-height: 160%;
    color: #663D00;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    max-width: 445px;
    margin-top: 4px;
    box-sizing: border-box;
    margin-right: 10px;
    height: 35px;
}

#sbi-builder-app .sbi-settings-cta .sbi-cta-title .sbi-cta-discount-label svg {
	margin-right: 6px;
}

.sbi-settings-cta .sbi-cta-title .sbi-cta-discount-label svg path{
    fill: #663D00;
}


#sbi-builder-app .sbi-settings-cta .sbi-cta-title .sbi-cta-discount-label {
	color: #663D00;
	background: #FFEFCC;
}
.sbi-settings-cta .sbi-cta-boxes {
	margin: 36px 0px 0px 7px;
    display: flex;
    background-color: #fff;
    box-shadow: 0px 1px 2px rgb(0 0 0 / 5%), 0px 4px 5px rgb(0 0 0 / 5%);
    border: 1px solid #E8E8EB;
    border-bottom: 0px solid transparent;
	max-width: 1170px;
}
.sbi-settings-cta .sbi-cta-box:not(:last-child) {
    border-right: 1px solid #E8E8EB;
}
.sbi-settings-cta .sbi-cta-box {
    padding: 16px 30px 16px 18px;
    display: flex;
}
.sbi-settings-cta .sbi-cta-box .sbi-cta-box-icon {
    margin-right: 24px;
    max-height: 50px;
}
.sbi-settings-cta .sbi-cta-box .sbi-cta-box-title {
    font-weight: 600;
    font-size: 14px;
    line-height: 140%;
    padding-top: 5px;
}
.sbi-settings-cta .sbi-cta-much-more{
    display: flex;
    padding: 0 14px;
    flex-wrap: wrap;
    margin-top: 3px;
	justify-content: space-between;
	max-width: 1170px;
}
.sbi-settings-cta .sbi-cta-much-more .sbi-cta-mm-left {
    width: 330px;
}
.sbi-settings-cta .sbi-cta-much-more .sbi-cta-mm-right ul {
    display: grid;
    grid-template-columns: 33% 33% 33%;
    grid-column-gap: 2%;
    padding-top: 10px;
}
.sbi-settings-cta .sbi-cta-much-more .sbi-cta-mm-left h4 {
    font-weight: 600;
    font-size: 18px;
    line-height: 140%;
    color: #141B38;
    margin: 1.33em 0 !important;
}
.sbi-settings-cta .sbi-cta-much-more .sbi-cta-mm-right ul li {
    font-size: 14px;
    line-height: 160%;
    color: #434960;
    width: 260px;
    position: relative;
}
.sbi-settings-cta .sbi-cta-much-more .sbi-cta-mm-right ul li::before {
    background: #0096CC;
    width: 4px;
    height: 4px;
    content: '';
    position: absolute;
    left: -19px;
    top: 10px;
}
.sbi-settings-cta .sbi-cta-try-demo {
    margin-top: 13px;
	max-width: 1180px;
}
.sbi-settings-cta .sbi-cta-try-demo a {
    width: 100%;
    justify-content: center;
	display: flex;
	align-items: center;
	vertical-align: middle;
	background: #F3F4F5;
	border: 1px solid #DCDDE1;
	box-sizing: border-box;
	border-radius: 2px;
	padding: 7px 20px;
	font-weight: 600;
	font-size: 14px;
	line-height: 22px;
	color: #141B38;
	letter-spacing: 0.2px;
	cursor: pointer;
	transition: all 0.15s ease-in-out;
	text-decoration: none;
}
.sbi-settings-cta .sbi-cta-try-demo a span svg {
    transform: translate(5px, 4px);
}
.sbi-settings-cta .sbi-cta-try-demo .sbi-btn span {
    margin-left: 9px;
    transform: translateY(1px);
}

.sbi-cta-toggle-features {
	background: #F3F4F5;
    width: 100%;
    display: flex;
    justify-content: center;
	padding: 5px 0 4px 15px;
	box-shadow: 0px 1px 2px rgb(0 0 0 / 5%), 0px 4px 5px rgb(0 0 0 / 5%);
	box-sizing: border-box;
}
.sbi-cta-toggle-features .sbi-cta-toggle-btn {
    font-weight: 600;
    font-size: 14px;
    line-height: 160%;
	border: none;
	background: none;
	display: flex;
	cursor: pointer;
}
.sbi-cta-toggle-features .sbi-cta-toggle-btn svg {
    transform: translateY(0px);
    margin-left: 4px;
}

@media (min-width: 1170px) and (max-width: 1540px) {
	.sbi-settings-cta .sbi-cta-much-more .sbi-cta-mm-right ul li {
		width: 240px;
	}
	.sbi-settings-cta .sbi-cta-much-more .sbi-cta-mm-left {
		width: 220px;
	}
}
@media (min-width: 1170px) and (max-width: 1340px) {
	.sbi-settings-cta .sbi-cta-box .sbi-cta-box-icon {
		margin-right: 15px;
	}
	.sbi-settings-cta .sbi-cta-box {
		padding: 16px 15px 16px 15px;
	}
}
@media (max-width: 1300px) {
	.sbi-settings-cta .sbi-cta-much-more .sbi-cta-mm-right ul {
		grid-template-columns: 48% 48%;
	}
}
@media (max-width: 1170px) {
	.sbi-settings-cta .sbi-cta-boxes {
		flex-wrap: wrap;
	}
	.sbi-settings-cta .sbi-cta-boxes .sbi-cta-box {
		width: 50%;
		box-sizing: border-box;
	}
	.sbi-settings-cta .sbi-cta-box:not(:last-child) {
		border-right: none;
	}
	.sbi-settings-cta .sbi-cta-box:first-child,
	.sbi-settings-cta .sbi-cta-box:nth-child(2) {
		border-bottom: 1px solid #E8E8EB;
	}
	.sbi-settings-cta .sbi-cta-box:first-child,
	.sbi-settings-cta .sbi-cta-box:nth-child(3) {
		border-right: 1px solid #E8E8EB;
	}
	.sbi-settings-cta .sbi-cta-much-more .sbi-cta-mm-right {
		padding-left: 20px;
	}
	.sbi-settings-cta .sbi-cta-much-more .sbi-cta-mm-right ul {
		margin: 0;
		padding: 0;
	}
}
@media (max-width: 770px) {
	.sbi-settings-cta .sbi-cta-boxes .sbi-cta-box {
		width: 100%;
    	align-items: center;
	}
	.sbi-settings-cta .sbi-cta-box {
		border: none;
	}
	.sbi-settings-cta .sbi-cta-box:not(:last-child) {
		border-bottom: 1px solid #E8E8EB;
	}
	.sbi-settings-cta .sbi-cta-much-more .sbi-cta-mm-right ul {
		grid-template-columns: 100%;
		margin-top: 0px;
	}
	.sbi-settings-cta .sbi-cta-much-more .sbi-cta-mm-left h4 {
		margin-bottom: 10px !important;
	}
	.sb-tab-content .sb-tab-box .sbi-caching-pro-cta {
		padding: 8px 25px;
	}

	.sbi-settings-cta .sbi-cta-head-inner {
		flex-wrap: wrap;
	}

	.sbi-settings-cta .sbi-cta-title {
		margin-bottom: 20px;
	}
	.sbi-settings-cta .sbi-cta-btn {
		width: 100%;
	}
	.sbi-settings-cta .sbi-plugin-title-bt {
		flex-direction: column;
	}

	.sbi-plugin-title {
		width: 67%;
	}
}

@media (max-width: 580px) {
	.sbi-settings-cta .sbi-cta-title .sbi-plugin-logo {
		margin-right: 20px;
	}
}



  /* Uncanny Automator Integration Popup */

  .sbi-integration-popup-modal .sbi-integration-popup {
	width: 100%;
	max-width: 580px;
	box-shadow: 0px 26.7377px 77.2886px rgba(0, 0, 0, 0.107828), 0px 14.2952px 41.3222px rgba(0, 0, 0, 0.0894161), 0px 8.01379px 23.1649px rgba(0, 0, 0, 0.075), 0px 4.25607px 12.3027px rgba(0, 0, 0, 0.0605839), 0px 1.77104px 5.11942px rgba(0, 0, 0, 0.0421718);
	border-radius: 8px;
	padding: 25px 0 0 0;
}
.sbi-integration-popup-modal .sbi-integration-popup .sbi-popup-content-header {
	text-align: center;
	padding: 0 100px;
}
.sbi-integration-popup-modal .sbi-integration-popup .sbi-popup-content-header img {
	width: 226px;
	margin-bottom: -55px;
}
#sbi-settings .sbi-integration-popup-modal .sbi-integration-popup .sbi-popup-content-header h3 {
	font-weight: 600;
	font-size: 18px;
	line-height: 140%;
	text-align: center;
	color: #141B38;
}
.sbi-integration-popup-modal .sbi-integration-popup .sbi-popup-content-header p {
	font-weight: 400;
	font-size: 12px;
	line-height: 160%;
	text-align: center;
	color: #696D80;
}
.sbi-integration-popup-modal .sbi-integration-popup .sbi-popup-ua-integration-steps {
	margin-top: 16px;
	padding: 33px 43px 48px;
}
.sbi-integration-popup-modal .sbi-integration-popup .sbi-popup-ua-integration-step {
	background: #FFFFFF;
	border: 1px solid #E6E6EB;
	border-radius: 8px;
	padding: 21px 100px 27px 20px;
	position: relative;
	margin-bottom: 12px;
}
.sbi-integration-popup-modal .sbi-integration-popup .sbi-popup-ua-integration-step .sbi-step-image {
    position: absolute;
    right: -7px;
    width: 170px;
    top: 17px;
}

.sbi-integration-popup-modal .sbi-integration-popup .sbi-popup-ua-setup-step .sbi-step-image {
	width: 186px;
    right: -1px;
	top: 8px;
}
.sbi-integration-popup-modal .sbi-integration-popup .sbi-popup-ua-integration-step h4 {
	font-weight: 600;
	font-size: 14px;
	line-height: 160%;
	color: #141B38;
	margin: 0;
}
.sbi-integration-popup-modal .sbi-integration-popup .sbi-popup-ua-integration-step p {
	font-weight: 400;
	font-size: 12px;
	line-height: 160%;
	color: #696D80;
	margin-top: 0px;
}
.sbi-integration-popup-modal .sbi-integration-popup .sbi-popup-ua-integration-step .sbi-btn {
	margin-top: 16px;
	border-radius: 4px;
	padding: 6px 8px;
	font-weight: 600;
	font-size: 12px;
	line-height: 160%;
}
.sbi-integration-popup-modal .sbi-integration-popup .sbi-popup-ua-integration-step .sbi-btn-install {
	background-color: #0068A0;
	color: #fff;
	box-shadow: 0px 2px 5px rgba(60, 66, 87, 0.12), 0px 1px 1px rgba(0, 0, 0, 0.08), inset 0px -1px 1px rgba(0, 0, 0, 0.12);
	transition: all .2s ease-in;
}
.sbi-integration-popup-modal .sbi-integration-popup .sbi-popup-ua-integration-step .sbi-btn-install:hover {
	background-color: #117db7;
}
.sbi-integration-popup-modal .sbi-integration-popup .sbi-popup-ua-integration-step .sbi-btn-install:disabled {
	background-color: #117db7 !important;
    color: white !important;
}
.sbi-integration-popup-modal .sbi-integration-popup .sbi-popup-ua-integration-step .sbi-btn svg {
	margin-right: 1px;
	width: 16px;
	height: 17px;
}
.sbi-integration-popup-modal .sbi-integration-popup .sbi-popup-ua-integration-step .sbi-btn svg path {
	fill: white;
}
.sbi-integration-popup-modal .sbi-integration-popup .sbi-popup-ua-integration-step .sbi-btn:disabled {
	background: #F9F9FA;
	color: #696D80;
}

@media (max-width: 768px) {
	.sbi-integration-popup-modal .sbi-integration-popup {
		width: calc(85% - 20px);
		left: 0px;
	}
}

@media (max-width: 600px) {
	.sbi-integration-popup-modal .sbi-integration-popup .sbi-popup-content-header {
		padding: 0 50px;
	}
	.sbi-integration-popup-modal .sbi-integration-popup .sbi-popup-ua-integration-steps {
		padding: 33px 25px 48px;
	}
	.sbi-integration-popup-modal .sbi-integration-popup .sbi-popup-ua-integration-step {
		padding: 21px 94px 27px 20px;
	}
}