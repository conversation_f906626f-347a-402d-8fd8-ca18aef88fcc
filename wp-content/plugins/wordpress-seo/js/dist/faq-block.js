(()=>{"use strict";var e={n:t=>{var s=t&&t.__esModule?()=>t.default:()=>t;return e.d(s,{a:s}),s},d:(t,s)=>{for(var n in s)e.o(s,n)&&!e.o(t,n)&&Object.defineProperty(t,n,{enumerable:!0,get:s[n]})},o:(e,t)=>Object.prototype.hasOwnProperty.call(e,t)};const t=window.React,s=window.wp.blockEditor,n=window.wp.blocks,o=JSON.parse('{"$schema":"https://schemas.wp.org/trunk/block.json","apiVersion":3,"version":"22.7","name":"yoast/faq-block","title":"Yoast FAQ","description":"List your Frequently Asked Questions in an SEO-friendly way.","category":"yoast-structured-data-blocks","icon":"editor-ul","keywords":["FAQ","Frequently Asked Questions","Schema","SEO","Structured Data"],"textdomain":"wordpress-seo","attributes":{"questions":{"type":"array"},"additionalListCssClasses":{"type":"string"}},"example":{"attributes":{"steps":[{"id":"faq-question-1","question":[],"answer":[]},{"id":"faq-question-2","question":[],"answer":[]}]}},"editorScript":"yoast-seo-faq-block","editorStyle":"yoast-seo-structured-data-blocks"}'),i=window.yoast.propTypes;var r=e.n(i);const a=window.wp.i18n,u=window.wp.a11y,c=window.wp.isShallowEqual,l=window.wp.element,h=window.wp.components,d=function(e){return class extends l.Component{render(){return(0,t.createElement)(l.Fragment,null,(0,t.createElement)(e,{...this.props})," ")}}},p=d(s.RichText.Content);class m extends l.Component{constructor(e){super(e),this.onSelectImage=this.onSelectImage.bind(this),this.onFocusAnswer=this.onFocusAnswer.bind(this),this.onFocusQuestion=this.onFocusQuestion.bind(this),this.onChangeAnswer=this.onChangeAnswer.bind(this),this.onChangeQuestion=this.onChangeQuestion.bind(this),this.onInsertQuestion=this.onInsertQuestion.bind(this),this.onRemoveQuestion=this.onRemoveQuestion.bind(this),this.onMoveDown=this.onMoveDown.bind(this),this.onMoveUp=this.onMoveUp.bind(this)}getMediaUploadButton(e){return(0,t.createElement)(h.Button,{className:"schema-faq-section-button faq-section-add-media",icon:"insert",onClick:e.open},(0,a.__)("Add image","wordpress-seo"))}onFocusQuestion(){this.props.onFocus("question",this.props.index)}onFocusAnswer(){this.props.onFocus("answer",this.props.index)}onChangeQuestion(e){const{index:t,onChange:s,attributes:{answer:n,question:o}}=this.props;s(e,n,o,n,t)}onChangeAnswer(e){const{index:t,onChange:s,attributes:{answer:n,question:o}}=this.props;s(o,e,o,n,t)}onInsertQuestion(){this.props.insertQuestion(this.props.index)}onRemoveQuestion(){this.props.removeQuestion(this.props.index)}onMoveUp(){this.props.isFirst||this.props.onMoveUp(this.props.index)}onMoveDown(){this.props.isLast||this.props.onMoveDown(this.props.index)}getButtons(){const{attributes:e}=this.props;return(0,t.createElement)("div",{className:"schema-faq-section-button-container"},(0,t.createElement)(s.MediaUpload,{onSelect:this.onSelectImage,allowedTypes:["image"],value:e.id,render:this.getMediaUploadButton}),(0,t.createElement)(h.Button,{className:"schema-faq-section-button",icon:"trash",label:(0,a.__)("Delete question","wordpress-seo"),onClick:this.onRemoveQuestion}),(0,t.createElement)(h.Button,{className:"schema-faq-section-button",icon:"insert",label:(0,a.__)("Insert question","wordpress-seo"),onClick:this.onInsertQuestion}))}getMover(){return(0,t.createElement)("div",{className:"schema-faq-section-mover"},(0,t.createElement)(h.Button,{className:"editor-block-mover__control",onClick:this.onMoveUp,icon:"arrow-up-alt2",label:(0,a.__)("Move question up","wordpress-seo"),"aria-disabled":this.props.isFirst}),(0,t.createElement)(h.Button,{className:"editor-block-mover__control",onClick:this.onMoveDown,icon:"arrow-down-alt2",label:(0,a.__)("Move question down","wordpress-seo"),"aria-disabled":this.props.isLast}))}onSelectImage(e){const{attributes:{answer:s,question:n},index:o}=this.props;let i=s.slice();const r=(0,t.createElement)("img",{className:`wp-image-${e.id}`,alt:e.alt,src:e.url,style:"max-width:100%;"});i.push?i.push(r):i=[i,r],this.props.onChange(n,i,n,s,o)}static getImageSrc(e){if(!e||!e.filter)return!1;const t=e.filter((e=>e&&e.type&&"img"===e.type))[0];return!!t&&t.props.src}static Content(e){return(0,t.createElement)("div",{className:"schema-faq-section",id:e.id,key:e.id},(0,t.createElement)(p,{tagName:"strong",className:"schema-faq-question",key:e.id+"-question",value:e.question}),(0,t.createElement)(p,{tagName:"p",className:"schema-faq-answer",key:e.id+"-answer",value:e.answer}))}shouldComponentUpdate(e){return!(0,c.isShallowEqualObjects)(e,this.props)}render(){const{attributes:e,isSelected:n}=this.props,{id:o,question:i,answer:r}=e;return(0,t.createElement)("div",{className:"schema-faq-section",key:o},(0,t.createElement)(s.RichText,{identifier:o+"-question",className:"schema-faq-question",tagName:"p",key:o+"-question",value:i,onChange:this.onChangeQuestion,onFocus:this.onFocusQuestion,unstableOnFocus:this.onFocusQuestion,placeholder:(0,a.__)("Enter a question","wordpress-seo"),allowedFormats:["core/italic","core/strikethrough","core/link","core/annotation"]}),(0,t.createElement)(s.RichText,{identifier:o+"-answer",className:"schema-faq-answer",tagName:"p",key:o+"-answer",value:r,onChange:this.onChangeAnswer,onFocus:this.onFocusAnswer,unstableOnFocus:this.onFocusAnswer,placeholder:(0,a.__)("Enter the answer to the question","wordpress-seo")}),n&&(0,t.createElement)("div",{className:"schema-faq-section-controls-container"},this.getMover(),this.getButtons()))}}m.propTypes={index:r().number.isRequired,attributes:r().object.isRequired,onChange:r().func.isRequired,insertQuestion:r().func.isRequired,removeQuestion:r().func.isRequired,onFocus:r().func.isRequired,onMoveUp:r().func.isRequired,onMoveDown:r().func.isRequired,isSelected:r().bool.isRequired,isFirst:r().bool.isRequired,isLast:r().bool.isRequired};const q=d(m.Content);class w extends l.Component{constructor(e){super(e),this.state={focus:""},this.changeQuestion=this.changeQuestion.bind(this),this.insertQuestion=this.insertQuestion.bind(this),this.removeQuestion=this.removeQuestion.bind(this),this.swapQuestions=this.swapQuestions.bind(this),this.moveQuestionDown=this.moveQuestionDown.bind(this),this.moveQuestionUp=this.moveQuestionUp.bind(this),this.setFocus=this.setFocus.bind(this),this.onAddQuestionButtonClick=this.onAddQuestionButtonClick.bind(this)}static generateId(e){return`${e}-${(new Date).getTime()}`}onAddQuestionButtonClick(){this.insertQuestion(null,[],[],!1)}changeQuestion(e,t,s,n,o){const i=this.props.attributes.questions?this.props.attributes.questions.slice():[];if(o>=i.length)return;if(i[o].question!==s||i[o].answer!==n)return;i[o]={id:i[o].id,question:e,answer:t,jsonQuestion:(0,l.renderToString)(e),jsonAnswer:(0,l.renderToString)(t)};const r=m.getImageSrc(t);r&&(i[o].jsonImageSrc=r),this.props.setAttributes({questions:i})}insertQuestion(e=null,t=[],s=[],n=!0){const o=this.props.attributes.questions?this.props.attributes.questions.slice():[];null===e&&(e=o.length-1),o.splice(e+1,0,{id:w.generateId("faq-question"),question:t,answer:s,jsonQuestion:"",jsonAnswer:""}),this.props.setAttributes({questions:o}),n?setTimeout(this.setFocus.bind(this,"question",e)):(0,u.speak)((0,a.__)("New question added","wordpress-seo"))}swapQuestions(e,t){const s=this.props.attributes.questions?this.props.attributes.questions.slice():[],n=s[e];s[e]=s[t],s[t]=n,this.props.setAttributes({questions:s});const[o,i]=this.state.focus.split(":");o===`${e}`?this.setFocus(i,t):o===`${t}`&&this.setFocus(i,e)}moveQuestionUp(e){this.swapQuestions(e,e-1)}moveQuestionDown(e){this.swapQuestions(e,e+1)}removeQuestion(e){const t=this.props.attributes.questions?this.props.attributes.questions.slice():[];t.splice(e,1),this.props.setAttributes({questions:t});let s=0;t[e]?s=e:t[e-1]&&(s=e-1),this.setFocus("question",s)}setFocus(e,t){const s=`${t}:${e}`;s!==this.state.focus&&this.setState({focus:s})}getAddQuestionButton(){return(0,t.createElement)(h.Button,{icon:"insert",onClick:this.onAddQuestionButtonClick,className:"schema-faq-add-question"},(0,a.__)("Add question","wordpress-seo"))}getQuestions(){const{attributes:e}=this.props;if(!e.questions)return null;const[s]=this.state.focus.split(":");return e.questions.map(((n,o)=>(0,t.createElement)(m,{index:o,key:n.id,attributes:n,insertQuestion:this.insertQuestion,removeQuestion:this.removeQuestion,onChange:this.changeQuestion,onFocus:this.setFocus,isSelected:s===`${o}`,onMoveUp:this.moveQuestionUp,onMoveDown:this.moveQuestionDown,isFirst:0===o,isLast:o===e.questions.length-1})))}static Content(e){const{questions:s,className:n}=e,o=s?s.map(((e,s)=>(0,t.createElement)(q,{key:s,...e}))):null,i=["schema-faq",n].filter((e=>e)).join(" ");return(0,t.createElement)("div",{className:i},o)}render(){const{className:e}=this.props,s=["schema-faq",e].filter((e=>e)).join(" ");return(0,t.createElement)("div",{className:s},(0,t.createElement)("div",null,this.getQuestions()),(0,t.createElement)("div",{className:"schema-faq-buttons"},this.getAddQuestionButton()))}}function b(e){const n=d(s.RichText.Content);return(0,t.createElement)("div",{className:"schema-faq-section",key:e.id},(0,t.createElement)(n,{tagName:"strong",className:"schema-faq-question",key:e.id+"-question",value:e.question}),(0,t.createElement)(n,{tagName:"p",className:"schema-faq-answer",key:e.id+"-answer",value:e.answer}))}function g(e){const{questions:s,className:n}=e.attributes,o=d(b),i=s?s.map(((e,s)=>(0,t.createElement)(o,{key:s,...e}))):null,r=["schema-faq",n].filter((e=>e)).join(" ");return(0,t.createElement)("div",{className:r},i)}w.propTypes={attributes:r().object.isRequired,setAttributes:r().func.isRequired,className:r().string},w.defaultProps={className:""},g.propTypes={attributes:r().object.isRequired};const v={v13_1:g};(0,n.registerBlockType)(o,{edit:({attributes:e,setAttributes:n,className:o})=>{const i=(0,s.useBlockProps)();return e.questions&&0!==e.questions.length||(e.questions=[{id:w.generateId("faq-question"),question:[],answer:[]}]),(0,t.createElement)("div",{...i},(0,t.createElement)(w,{attributes:e,setAttributes:n,className:o}))},save:({attributes:e})=>{const n=s.useBlockProps.save(e);return(0,t.createElement)(w.Content,{...n})},deprecated:[{attributes:o.attributes,save:v.v13_1}]})})();