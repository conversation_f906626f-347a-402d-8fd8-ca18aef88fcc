(()=>{"use strict";var e={6746:(e,t,s)=>{var n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},r=i(s(9196)),a=i(s(9156)),o=i(s(6743));function i(e){return e&&e.__esModule?e:{default:e}}var l=void 0;function c(e,t){var s,o,i,u,d,p,m,h,g=[],y={};for(p=0;p<e.length;p++)if("string"!==(d=e[p]).type){if(!t.hasOwnProperty(d.value)||void 0===t[d.value])throw new Error("Invalid interpolation, missing component node: `"+d.value+"`");if("object"!==n(t[d.value]))throw new Error("Invalid interpolation, component node must be a ReactElement or null: `"+d.value+"`","\n> "+l);if("componentClose"===d.type)throw new Error("Missing opening component token: `"+d.value+"`");if("componentOpen"===d.type){s=t[d.value],i=p;break}g.push(t[d.value])}else g.push(d.value);return s&&(u=function(e,t){var s,n,r=t[e],a=0;for(n=e+1;n<t.length;n++)if((s=t[n]).value===r.value){if("componentOpen"===s.type){a++;continue}if("componentClose"===s.type){if(0===a)return n;a--}}throw new Error("Missing closing component token `"+r.value+"`")}(i,e),m=c(e.slice(i+1,u),t),o=r.default.cloneElement(s,{},m),g.push(o),u<e.length-1&&(h=c(e.slice(u+1),t),g=g.concat(h))),1===g.length?g[0]:(g.forEach((function(e,t){e&&(y["interpolation-child-"+t]=e)})),(0,a.default)(y))}t.Z=function(e){var t=e.mixedString,s=e.components,r=e.throwErrors;if(l=t,!s)return t;if("object"!==(void 0===s?"undefined":n(s))){if(r)throw new Error("Interpolation Error: unable to process `"+t+"` because components is not an object");return t}var a=(0,o.default)(t);try{return c(a,s)}catch(e){if(r)throw new Error("Interpolation Error: unable to process `"+t+"` because of error `"+e.message+"`");return t}}},6743:e=>{function t(e){return e.match(/^\{\{\//)?{type:"componentClose",value:e.replace(/\W/g,"")}:e.match(/\/\}\}$/)?{type:"componentSelfClosing",value:e.replace(/\W/g,"")}:e.match(/^\{\{/)?{type:"componentOpen",value:e.replace(/\W/g,"")}:{type:"string",value:e}}e.exports=function(e){return e.split(/(\{\{\/?\s*\w+\s*\/?\}\})/g).map(t)}},9156:(e,t,s)=>{var n=s(9196),r="function"==typeof Symbol&&Symbol.for&&Symbol.for("react.element")||60103,a=s(7942),o=s(9179),i=s(397),l=".",c=":",u="function"==typeof Symbol&&Symbol.iterator,d="@@iterator";function p(e,t){return e&&"object"==typeof e&&null!=e.key?(s=e.key,n={"=":"=0",":":"=2"},"$"+(""+s).replace(/[=:]/g,(function(e){return n[e]}))):t.toString(36);var s,n}function m(e,t,s,n){var a,i=typeof e;if("undefined"!==i&&"boolean"!==i||(e=null),null===e||"string"===i||"number"===i||"object"===i&&e.$$typeof===r)return s(n,e,""===t?l+p(e,0):t),1;var h=0,g=""===t?l:t+c;if(Array.isArray(e))for(var y=0;y<e.length;y++)h+=m(a=e[y],g+p(a,y),s,n);else{var f=function(e){var t=e&&(u&&e[u]||e[d]);if("function"==typeof t)return t}(e);if(f)for(var b,w=f.call(e),v=0;!(b=w.next()).done;)h+=m(a=b.value,g+p(a,v++),s,n);else if("object"===i){var k=""+e;o(!1,"Objects are not valid as a React child (found: %s).%s","[object Object]"===k?"object with keys {"+Object.keys(e).join(", ")+"}":k,"")}}return h}var h=/\/+/g;function g(e){return(""+e).replace(h,"$&/")}var y,f,b=w,w=function(e){var t=this;if(t.instancePool.length){var s=t.instancePool.pop();return t.call(s,e),s}return new t(e)};function v(e,t,s,n){this.result=e,this.keyPrefix=t,this.func=s,this.context=n,this.count=0}function k(e,t,s){var r,o,i=e.result,l=e.keyPrefix,c=e.func,u=e.context,d=c.call(u,t,e.count++);Array.isArray(d)?E(d,i,s,a.thatReturnsArgument):null!=d&&(n.isValidElement(d)&&(r=d,o=l+(!d.key||t&&t.key===d.key?"":g(d.key)+"/")+s,d=n.cloneElement(r,{key:o},void 0!==r.props?r.props.children:void 0)),i.push(d))}function E(e,t,s,n,r){var a="";null!=s&&(a=g(s)+"/");var o=v.getPooled(t,a,n,r);!function(e,t,s){null==e||m(e,"",t,s)}(e,k,o),v.release(o)}v.prototype.destructor=function(){this.result=null,this.keyPrefix=null,this.func=null,this.context=null,this.count=0},y=function(e,t,s,n){var r=this;if(r.instancePool.length){var a=r.instancePool.pop();return r.call(a,e,t,s,n),a}return new r(e,t,s,n)},(f=v).instancePool=[],f.getPooled=y||b,f.poolSize||(f.poolSize=10),f.release=function(e){var t=this;o(e instanceof t,"Trying to release an instance into a pool of a different type."),e.destructor(),t.instancePool.length<t.poolSize&&t.instancePool.push(e)},e.exports=function(e){if("object"!=typeof e||!e||Array.isArray(e))return i(!1,"React.addons.createFragment only accepts a single object. Got: %s",e),e;if(n.isValidElement(e))return i(!1,"React.addons.createFragment does not accept a ReactElement without a wrapper object."),e;o(1!==e.nodeType,"React.addons.createFragment(...): Encountered an invalid child; DOM elements are not valid children of React components.");var t=[];for(var s in e)E(e[s],t,s,a.thatReturnsArgument);return t}},7942:e=>{function t(e){return function(){return e}}var s=function(){};s.thatReturns=t,s.thatReturnsFalse=t(!1),s.thatReturnsTrue=t(!0),s.thatReturnsNull=t(null),s.thatReturnsThis=function(){return this},s.thatReturnsArgument=function(e){return e},e.exports=s},9179:e=>{e.exports=function(e,t,s,n,r,a,o,i){if(!e){var l;if(void 0===t)l=new Error("Minified exception occurred; use the non-minified dev environment for the full error message and additional helpful warnings.");else{var c=[s,n,r,a,o,i],u=0;(l=new Error(t.replace(/%s/g,(function(){return c[u++]})))).name="Invariant Violation"}throw l.framesToPop=1,l}}},397:(e,t,s)=>{var n=s(7942);e.exports=n},9196:e=>{e.exports=window.React}},t={};function s(n){var r=t[n];if(void 0!==r)return r.exports;var a=t[n]={exports:{}};return e[n](a,a.exports,s),a.exports}s.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return s.d(t,{a:t}),t},s.d=(e,t)=>{for(var n in t)s.o(t,n)&&!s.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},s.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),s.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},(()=>{var e={};s.r(e),s.d(e,{refreshDelay:()=>l});var t={};s.r(t),s.d(t,{default:()=>b,initializationDone:()=>w,sortResultsByIdentifier:()=>f});var n={};s.r(n),s.d(n,{default:()=>U,getIconForScore:()=>Y});var r={};s.r(r),s.d(r,{doAjaxRequest:()=>Ws});var a={};s.r(a),s.d(a,{setTextdomainL10n:()=>Xs});var o={};s.r(o),s.d(o,{applyReplaceUsingPlugin:()=>bn,createLabelFromName:()=>un,excerptFromContent:()=>fn,fillReplacementVariables:()=>ln,handlePrefixes:()=>cn,mapCustomFields:()=>yn,mapCustomTaxonomies:()=>gn,nonReplaceVars:()=>on,prepareCustomFieldForDispatch:()=>mn,prepareCustomTaxonomyForDispatch:()=>hn,pushNewReplaceVar:()=>dn,replaceSpaces:()=>pn});const i=window.yoast.externals.contexts,l=500,c=window.lodash;function u(){return(0,c.get)(window,"wpseoScriptData.metabox",{intl:{},isRtl:!1})}const d=window.wp.i18n,p=window.yoast.analysis,m=window.wp.hooks,h=window.yoast.externals.redux;function g(){}let y=!1;function f(e){return e.sort(((e,t)=>e._identifier.localeCompare(t._identifier)))}function b(e,t,s,n,r){if(!y)return;const a=p.Paper.parse(t());e.analyze(a).then((o=>{const{result:{seo:i,readability:l,inclusiveLanguage:c}}=o;if(i){const e=i[""];e.results.forEach((e=>{e.getMarker=()=>()=>s(a,e.marks)})),e.results=f(e.results),n.dispatch(h.actions.setSeoResultsForKeyword(a.getKeyword(),e.results)),n.dispatch(h.actions.setOverallSeoScore(e.score,a.getKeyword())),n.dispatch(h.actions.refreshSnippetEditor()),r.saveScores(e.score,a.getKeyword())}l&&(l.results.forEach((e=>{e.getMarker=()=>()=>s(a,e.marks)})),l.results=f(l.results),n.dispatch(h.actions.setReadabilityResults(l.results)),n.dispatch(h.actions.setOverallReadabilityScore(l.score)),n.dispatch(h.actions.refreshSnippetEditor()),r.saveContentScore(l.score)),c&&(c.results.forEach((e=>{e.getMarker=()=>()=>s(a,e.marks)})),c.results=f(c.results),n.dispatch(h.actions.setInclusiveLanguageResults(c.results)),n.dispatch(h.actions.setOverallInclusiveLanguageScore(c.score)),n.dispatch(h.actions.refreshSnippetEditor()),r.saveInclusiveLanguageScore(c.score)),(0,m.doAction)("yoast.analysis.refresh",o,{paper:a,worker:e,collectData:t,applyMarks:s,store:n,dataCollector:r})})).catch(g)}function w(){y=!0}var v=s(9196);const k=window.wp.element,E=window.yoast.styledComponents;var R=s.n(E);const _=window.yoast.propTypes;var x=s.n(_);const T=window.yoast.componentsNew,S=window.yoast.helpers,C=window.yoast.styleGuide,I=C.colors.$color_bad,L=C.colors.$palette_error_background,q=C.colors.$color_grey_text_light,P=C.colors.$palette_error_text,A=R().div`
	display: flex;
	flex-direction: column;
`,N=R().label`
	font-size: var(--yoast-font-size-default);
	font-weight: var(--yoast-font-weight-bold);
	${(0,S.getDirectionalStyle)("margin-right: 4px","margin-left: 4px")};
`,M=R().span`
	margin-bottom: 0.5em;
`,O=R()(T.InputField)`
	flex: 1 !important;
	box-sizing: border-box;
	max-width: 100%;
	margin: 0; // Reset margins inherited from WordPress.

	// Hide native X in Edge and IE11.
	&::-ms-clear {
		display: none;
	}

	&.has-error {
		border-color: ${I} !important;
		background-color: ${L} !important;

		&:focus {
			box-shadow: 0 0 2px ${I} !important;
		}
	}
`,B=R().ul`
	color: ${P};
	list-style-type: disc;
	list-style-position: outside;
	margin: 0;
	margin-left: 1.2em;
`,K=R().li`
	color: ${P};
	margin: 0 0 0.5em 0;
`,F=(0,T.addFocusStyle)(R().button`
		border: 1px solid transparent;
		box-shadow: none;
		background: none;
		flex: 0 0 32px;
		height: 32px;
		max-width: 32px;
		padding: 0;
		cursor: pointer;
	`);F.propTypes={type:x().string,focusColor:x().string,focusBackgroundColor:x().string,focusBorderColor:x().string},F.defaultProps={type:"button",focusColor:C.colors.$color_button_text_hover,focusBackgroundColor:"transparent",focusBorderColor:C.colors.$color_blue};const D=R()(T.SvgIcon)`
	margin-top: 4px;
`,$=R().div`
	display: flex;
	flex-direction: row;
	align-items: center;

	&.has-remove-keyword-button {
		${O} {
			${(0,S.getDirectionalStyle)("padding-right: 40px","padding-left: 40px")};
		}

		${F} {
			${(0,S.getDirectionalStyle)("margin-left: -32px","margin-right: -32px")};
		}
	}
`;class H extends k.Component{constructor(e){super(e),this.handleChange=this.handleChange.bind(this)}handleChange(e){this.props.onChange(e.target.value)}renderLabel(){const{id:e,label:t,helpLink:s}=this.props;return(0,v.createElement)(M,null,(0,v.createElement)(N,{htmlFor:e},t),s)}renderErrorMessages(){const e=[...this.props.errorMessages];return!(0,c.isEmpty)(e)&&(0,v.createElement)(B,null,e.map(((e,t)=>(0,v.createElement)(K,{key:t},(0,v.createElement)("span",{role:"alert"},e)))))}render(){const{id:e,showLabel:t,keyword:s,onRemoveKeyword:n,onBlurKeyword:r,onFocusKeyword:a,hasError:o}=this.props,i=!t,l=n!==c.noop;return(0,v.createElement)(A,null,t&&this.renderLabel(),o&&this.renderErrorMessages(),(0,v.createElement)($,{className:l?"has-remove-keyword-button":null},(0,v.createElement)(O,{"aria-label":i?this.props.label:null,type:"text",id:e,className:o?"has-error":null,onChange:this.handleChange,onFocus:a,onBlur:r,value:s,autoComplete:"off"}),l&&(0,v.createElement)(F,{onClick:n,focusBoxShadowColor:"#084A67"},(0,v.createElement)(D,{size:"18px",icon:"times-circle",color:q}))))}}H.propTypes={id:x().string.isRequired,showLabel:x().bool,keyword:x().string,onChange:x().func.isRequired,onRemoveKeyword:x().func,onBlurKeyword:x().func,onFocusKeyword:x().func,label:x().string.isRequired,helpLink:x().node,hasError:x().bool,errorMessages:x().arrayOf(x().string)},H.defaultProps={showLabel:!0,keyword:"",onRemoveKeyword:c.noop,onBlurKeyword:c.noop,onFocusKeyword:c.noop,helpLink:null,hasError:!1,errorMessages:[]};const W=H;function j(e,t=""){const s=e.getIdentifier(),n={score:e.score,rating:p.interpreters.scoreToRating(e.score),hasMarks:e.hasMarks(),marker:e.getMarker(),id:s,text:e.text,markerId:t.length>0?`${t}:${s}`:s,hasBetaBadge:e.hasBetaBadge(),hasJumps:e.hasJumps(),editFieldName:e.editFieldName};return"ok"===n.rating&&(n.rating="OK"),n}function z(e,t){switch(e.rating){case"error":t.errorsResults.push(e);break;case"feedback":t.considerationsResults.push(e);break;case"bad":t.problemsResults.push(e);break;case"OK":t.improvementsResults.push(e);break;case"good":t.goodResults.push(e)}return t}function Y(e){switch(e){case"loading":return{icon:"loading-spinner",color:C.colors.$color_green_medium_light};case"not-set":return{icon:"seo-score-none",color:C.colors.$color_score_icon};case"noindex":return{icon:"seo-score-none",color:C.colors.$color_noindex};case"good":return{icon:"seo-score-good",color:C.colors.$color_green_medium};case"ok":return{icon:"seo-score-ok",color:C.colors.$color_ok};default:return{icon:"seo-score-bad",color:C.colors.$color_red}}}function U(e,t=""){let s={errorsResults:[],problemsResults:[],improvementsResults:[],goodResults:[],considerationsResults:[]};if(!e)return s;for(let n=0;n<e.length;n++){const r=e[n];r.text&&(s=z(j(r,t),s))}return s}const V=(0,S.makeOutboundLink)(R().a`
	display: inline-block;
	position: relative;
	outline: none;
	text-decoration: none;
	border-radius: 100%;
	width: 24px;
	height: 24px;
	margin: -4px 0;
	vertical-align: middle;

	color: ${C.colors.$color_help_text};
	
	&:hover,
	&:focus {
		color: ${C.colors.$color_snippet_focus};	
	}
	
	// Overwrite the default blue active color for links.
	&:active {
		color: ${C.colors.$color_help_text};	
	}

	&::before {
		position: absolute;
		top: 0;
		left: 0;
		padding: 2px;
		content: "\f223";
	}
`),G=R()(T.Collapsible)`
	h2 > button {
		padding-left: 24px;
		padding-top: 16px;

		&:hover {
			background-color: #f0f0f0;
		}
	}

	div[class^="collapsible_content"] {
		padding: 24px 0;
		margin: 0 24px;
		border-top: 1px solid rgba(0,0,0,0.2);
	}

`,Z=window.wp.components,J="yoast yoast-gutenberg-modal",Q=e=>{const{title:t,className:s,showYoastIcon:n,additionalClassName:r,...a}=e,o=n?(0,v.createElement)("span",{className:"yoast-icon"}):null;return(0,v.createElement)(Z.Modal,{title:t,className:`${s} ${r}`,icon:o,...a},e.children)};Q.propTypes={title:x().string,className:x().string,showYoastIcon:x().bool,children:x().oneOfType([x().node,x().arrayOf(x().node)]),additionalClassName:x().string},Q.defaultProps={title:"Yoast SEO",className:J,showYoastIcon:!0,children:null,additionalClassName:""};const X=Q,ee=(window.yoast.socialMetadataForms,e=>({type:e.subtype,width:e.width,height:e.height,url:e.url,id:e.id,sizes:e.sizes,alt:e.alt||e.title||e.name}));const te=({hiddenField:e,hiddenFieldImageId:t,hiddenFieldFallbackImageId:s,hasImageValidation:n,...r})=>{const[a,o]=(0,k.useState)(null!==document.getElementById(s)),i=(0,k.useMemo)((()=>document.getElementById(e))),l=(0,k.useMemo)((()=>document.getElementById(t)));let c=null;c=s&&document.getElementById(s)?(0,k.useMemo)((()=>document.getElementById(s))):l;const[u,d]=(0,k.useState)({url:i?i.value:"",id:c?parseInt(c.value,10):"",alt:""}),[p,m]=(0,k.useState)([]),h=(0,k.useCallback)((e=>{i&&(i.value=e.url),c&&(c.value=e.id)})),g=(0,k.useCallback)((()=>{(function(e){const t=window.wp.media();return t.on("select",(()=>{const s=t.state().get("selection").first();e(ee(s.attributes))})),t})((e=>{c=l,d(e),h(e),n&&m((0,S.validateFacebookImage)(e)),o(!1)})).open()}),[n,h]),y=(0,k.useCallback)((()=>{c=l;const e={url:"",id:"",alt:""};d(e),h(e),m([]),o(!0)}),[h]);return(0,k.useEffect)((()=>{u.id&&!u.alt&&function(e){return new Promise(((t,s)=>{window.wp.media.attachment||s(),window.wp.media.attachment(e).fetch().then((e=>{t(ee(e))})).catch((()=>s()))}))}(u.id).then((e=>d(e)))}),[u]),(0,v.createElement)(T.ImageSelect,{...r,usingFallback:a,imageUrl:u.url,imageId:u.id,imageAltText:u.alt,onClick:g,onRemoveImageClick:y,warnings:p})};te.propTypes={hiddenField:x().string.isRequired,hiddenFieldImageId:x().string,hiddenFieldFallbackImageId:x().string,hasImageValidation:x().bool},te.defaultProps={hiddenFieldImageId:"",hiddenFieldFallbackImageId:"",hasImageValidation:!1};const se=te;function ne({target:e,children:t}){let s=e;return"string"==typeof e&&(s=document.getElementById(e)),s?(0,k.createPortal)(t,s):null}function re({target:e,label:t,hasPreview:s,hiddenField:n,hiddenFieldImageId:r,hiddenFieldFallbackImageId:a,selectImageButtonId:o,replaceImageButtonId:i,removeImageButtonId:l,hasNewBadge:c,isDisabled:u,hasPremiumBadge:d,hasImageValidation:p}){return(0,v.createElement)(ne,{target:e},(0,v.createElement)(se,{label:t,hasPreview:s,hiddenField:n,hiddenFieldImageId:r,hiddenFieldFallbackImageId:a,selectImageButtonId:o,replaceImageButtonId:i,removeImageButtonId:l,hasNewBadge:c,isDisabled:u,hasPremiumBadge:d,hasImageValidation:p}))}ne.propTypes={target:x().oneOfType([x().string,x().object]).isRequired,children:x().node.isRequired},re.propTypes={target:x().string.isRequired,label:x().string.isRequired,hasPreview:x().bool.isRequired,hiddenField:x().string.isRequired,hiddenFieldImageId:x().string,hiddenFieldFallbackImageId:x().string,selectImageButtonId:x().string,replaceImageButtonId:x().string,removeImageButtonId:x().string,hasNewBadge:x().bool,isDisabled:x().bool,hasPremiumBadge:x().bool,hasImageValidation:x().bool},re.defaultProps={hiddenFieldImageId:"",hiddenFieldFallbackImageId:"",selectImageButtonId:"",replaceImageButtonId:"",removeImageButtonId:"",hasNewBadge:!1,isDisabled:!1,hasPremiumBadge:!1,hasImageValidation:!1};const ae=({target:e,scoreIndicator:t})=>(0,v.createElement)(ne,{target:e},(0,v.createElement)(T.SvgIcon,{...Y(t)}));ae.propTypes={target:x().string.isRequired,scoreIndicator:x().string.isRequired};const oe=ae,ie=e=>{const[t,s]=(0,k.useState)(!1),{prefixIcon:n}=e;return(0,v.createElement)("div",{className:"yoast components-panel__body "+(t?"is-opened":"")},(0,v.createElement)("h2",{className:"components-panel__body-title"},(0,v.createElement)("button",{onClick:function(){s(!t)},className:"components-button components-panel__body-toggle",type:"button",id:e.buttonId},(0,v.createElement)("span",{className:"yoast-icon-span",style:{fill:`${n&&n.color||""}`}},n&&(0,v.createElement)(T.SvgIcon,{icon:n.icon,color:n.color,size:n.size})),(0,v.createElement)("span",{className:"yoast-title-container"},(0,v.createElement)("div",{className:"yoast-title"},e.title),(0,v.createElement)("div",{className:"yoast-subtitle"},e.subTitle)),e.hasBetaBadgeLabel&&(0,v.createElement)(T.BetaBadge,null),(0,v.createElement)("span",{className:"yoast-chevron","aria-hidden":"true"}))),t&&e.children)},le=ie;ie.propTypes={title:x().string.isRequired,children:x().oneOfType([x().node,x().arrayOf(x().node)]).isRequired,prefixIcon:x().object,subTitle:x().string,hasBetaBadgeLabel:x().bool,buttonId:x().string},ie.defaultProps={prefixIcon:null,subTitle:"",hasBetaBadgeLabel:!1,buttonId:null};const ce=({children:e})=>(0,v.createElement)("div",null,e);ce.propTypes={renderPriority:x().number.isRequired,children:x().node.isRequired};const ue=ce,de=({theme:e,location:t,children:s})=>(0,v.createElement)(i.LocationProvider,{value:t},(0,v.createElement)(E.ThemeProvider,{theme:e},s));de.propTypes={theme:x().object.isRequired,location:x().oneOf(["sidebar","metabox","modal"]).isRequired,children:x().element.isRequired};const pe=de,me=window.wp.compose,he=window.wp.data,ge=e=>(0,v.createElement)("div",{className:"yoast components-panel__body"},(0,v.createElement)("h2",{className:"components-panel__body-title"},(0,v.createElement)("button",{id:e.id,onClick:e.onClick,className:"components-button components-panel__body-toggle",type:"button"},e.prefixIcon&&(0,v.createElement)("span",{className:"yoast-icon-span",style:{fill:`${e.prefixIcon&&e.prefixIcon.color||""}`}},(0,v.createElement)(T.SvgIcon,{size:e.prefixIcon.size,icon:e.prefixIcon.icon})),(0,v.createElement)("span",{className:"yoast-title-container"},(0,v.createElement)("div",{className:"yoast-title"},e.title),(0,v.createElement)("div",{className:"yoast-subtitle"},e.subTitle)),e.children,e.suffixIcon&&(0,v.createElement)(T.SvgIcon,{size:e.suffixIcon.size,icon:e.suffixIcon.icon}),e.SuffixHeroIcon))),ye=ge;ge.propTypes={onClick:x().func.isRequired,title:x().string.isRequired,id:x().string,subTitle:x().string,suffixIcon:x().object,SuffixHeroIcon:x().object,prefixIcon:x().object,children:x().node},ge.defaultProps={id:"",suffixIcon:null,SuffixHeroIcon:null,prefixIcon:null,subTitle:"",children:null};const fe=({id:e,postTypeName:t,children:s,title:n,isOpen:r,close:a,open:o,shouldCloseOnClickOutside:l,showChangesWarning:c,SuffixHeroIcon:u})=>(0,v.createElement)(k.Fragment,null,r&&(0,v.createElement)(i.LocationProvider,{value:"modal"},(0,v.createElement)(X,{title:n,onRequestClose:a,additionalClassName:"yoast-collapsible-modal yoast-post-settings-modal",id:"id",shouldCloseOnClickOutside:l},(0,v.createElement)("div",{className:"yoast-content-container"},(0,v.createElement)("div",{className:"yoast-modal-content"},s)),(0,v.createElement)("div",{className:"yoast-notice-container"},(0,v.createElement)("hr",null),(0,v.createElement)("div",{className:"yoast-button-container"},c&&(0,v.createElement)("p",null,/* Translators: %s translates to the Post Label in singular form */
(0,d.sprintf)((0,d.__)("Make sure to save your %s for changes to take effect","wordpress-seo"),t)),(0,v.createElement)("button",{className:"yoast-button yoast-button--primary yoast-button--post-settings-modal",type:"button",onClick:a},/* Translators: %s translates to the Post Label in singular form */
(0,d.sprintf)((0,d.__)("Return to your %s","wordpress-seo"),t)))))),(0,v.createElement)(ye,{id:e+"-open-button",title:n,SuffixHeroIcon:u,suffixIcon:u?null:{size:"20px",icon:"pencil-square"},onClick:o}));fe.propTypes={id:x().string.isRequired,postTypeName:x().string.isRequired,children:x().oneOfType([x().node,x().arrayOf(x().node)]).isRequired,title:x().string.isRequired,isOpen:x().bool.isRequired,open:x().func.isRequired,close:x().func.isRequired,shouldCloseOnClickOutside:x().bool,showChangesWarning:x().bool,SuffixHeroIcon:x().object},fe.defaultProps={shouldCloseOnClickOutside:!0,showChangesWarning:!0};const be=fe,we=(0,me.compose)([(0,he.withSelect)(((e,t)=>{const{getPostOrPageString:s,getIsModalOpen:n}=e("yoast-seo/editor");return{postTypeName:s(),isOpen:n(t.id)}})),(0,he.withDispatch)(((e,t)=>{const{openEditorModal:s,closeEditorModal:n}=e("yoast-seo/editor");return{open:()=>s(t.id),close:n}}))])(be),ve=(0,me.compose)([(0,he.withSelect)(((e,t)=>{const{isAlertDismissed:s}=e(t.store||"yoast-seo/editor");return{isAlertDismissed:s(t.alertKey)}})),(0,he.withDispatch)(((e,t)=>{const{dismissAlert:s}=e(t.store||"yoast-seo/editor");return{onDismissed:()=>s(t.alertKey)}}))])(T.Alert),ke=window.yoast.analysisReport,Ee=window.yoast.uiLibrary,Re=v.forwardRef((function(e,t){return v.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true",ref:t},e),v.createElement("path",{fillRule:"evenodd",d:"M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z",clipRule:"evenodd"}))})),_e=(R().div`
	min-width: 600px;

	@media screen and ( max-width: 680px ) {
		min-width: 0;
		width: 86vw;
	}
`,R().div`
	@media screen and ( min-width: 600px ) {
		max-width: 420px;
	}
`),xe=(R()(T.Icon)`
	float: ${(0,S.getDirectionalStyle)("right","left")};
	margin: ${(0,S.getDirectionalStyle)("0 0 16px 16px","0 16px 16px 0")};

	&& {
		width: 150px;
		height: 150px;

		@media screen and ( max-width: 680px ) {
			width: 80px;
			height: 80px;
		}
	}
`,window.wp.url),Te=R().div`
  padding: 25px 32px 32px;
  color: #303030;
`,Se=R().ul`
  margin: 0;
  padding: 0;

  li {
    list-style-image: var(--yoast-svg-icon-check);
    margin: 0.5rem 0 0 1.5rem;
    line-height: 1.4em;

    &::marker {
      font-size: 1.5rem;
    }
  }
`,Ce=R().span`
  display: block;
  margin-top: 4px;
`,Ie=R().h2`
  margin-top: 0;
  margin-bottom: 0.25rem;
  color: #303030;
  font-size: 0.8125rem;
  font-weight: 600;
`,Le=R().p`
  display: block;
  margin: 0.25rem 0 1rem 0 !important;
  max-width: 420px;
`,qe=R().hr`
  margin-top: 1.5rem;
  margin-bottom: 1rem;
  border-top: 0;
  border-bottom: 1px solid #E2E8F0;
`,Pe=R().div`
  text-align: center;
`,Ae=R().a`
  width: 100%;
`,Ne=(0,S.makeOutboundLink)(Ae);class Me extends k.Component{constructor(e){super(e),this.state={defaultPrice:"99"}}createBenefitsList(e){return e.length>0&&(0,v.createElement)(Se,{role:"list"},e.map(((e,t)=>(0,v.createElement)("li",{key:`upsell-benefit-${t}`},(0,k.createInterpolateElement)(e,{strong:(0,v.createElement)("strong",null)})))))}render(){const e=(0,he.select)("yoast-seo/editor").isPromotionActive("black-friday-2023-promotion"),{defaultPrice:t}=this.state,s=e?"69.30":null,n=s||t;return(0,v.createElement)(k.Fragment,null,e&&(0,v.createElement)("div",{className:"yst-flex yst-justify-between yst-items-center yst-text-lg yst-content-between yst-bg-black yst-text-amber-300 yst-h-9 yst-border-amber-300 yst-border-y yst-border-x-0 yst-border-solid yst-px-6"},(0,v.createElement)("div",null,(0,d.__)("BLACK FRIDAY","wordpress-seo")),(0,v.createElement)("div",null,(0,d.__)("30% OFF","wordpress-seo"))),(0,v.createElement)(Te,null,(0,v.createElement)(Ie,null,this.props.title),(0,v.createElement)(Le,null,this.props.description),(0,v.createElement)(Pe,null,(0,v.createElement)(Ne,{...this.props.upsellButton},this.props.upsellButtonText,this.props.upsellButtonHasCaret&&(0,v.createElement)("span",{"aria-hidden":"true",className:"yoast-button-upsell__caret"})),(0,v.createElement)("div",{className:"yst-text-slate-600 yst-my-4"},s&&(0,v.createElement)(k.Fragment,null,(0,v.createElement)("span",{className:"yst-text-slate-500 yst-line-through"},t)," "),(0,v.createElement)("span",{className:"yst-text-slate-900 yst-text-2xl yst-font-bold"},n)," ",(0,d.__)("$ USD / € EUR / £ GBP per year (ex. VAT)","wordpress-seo")),(0,v.createElement)(Ce,{id:this.props.upsellButton["aria-describedby"]},this.props.upsellButtonLabel)),(0,v.createElement)(qe,null),(0,v.createElement)(Ie,null,this.props.benefitsTitle),this.createBenefitsList(this.props.benefits)))}}Me.propTypes={title:x().node,benefits:x().array,benefitsTitle:x().node,description:x().node,upsellButton:x().object,upsellButtonText:x().string.isRequired,upsellButtonLabel:x().string,upsellButtonHasCaret:x().bool},Me.defaultProps={title:null,description:null,benefits:[],benefitsTitle:null,upsellButton:{href:"",className:"button button-primary"},upsellButtonLabel:"",upsellButtonHasCaret:!0};const Oe=Me,Be=(0,d.__)("Check your text on even more SEO criteria and get an enhanced keyphrase analysis, making it easier to optimize your content.","wordpress-seo"),Ke=e=>{const{locationContext:t}=(0,i.useRootContext)(),s=(0,xe.addQueryArgs)(wpseoAdminL10n[e.buyLink],{context:t});return(0,v.createElement)(Oe,{title:(0,d.__)("Get more help with writing content that ranks","wordpress-seo"),description:e.description,benefitsTitle:/* translators: %s expands to 'Yoast SEO Premium'. */
(0,d.sprintf)("%s also gives you:","Yoast SEO Premium"),benefits:[(0,d.sprintf)(/* translators: %1$s expands to a strong opening tag, %2$s expands to a strong closing tag. */
(0,d.__)("%1$sAI%2$s: Better SEO titles and meta descriptions, faster.","wordpress-seo"),"<strong>","</strong>"),(0,d.sprintf)(/* translators: %1$s expands to a strong opening tag, %2$s expands to a strong closing tag. */
(0,d.__)("%1$sMultiple keywords%2$s: Rank higher for more searches.","wordpress-seo"),"<strong>","</strong>"),(0,d.sprintf)(/* translators: %1$s expands to a strong opening tag, %2$s expands to a strong closing tag. */
(0,d.__)("%1$sSuper fast%2$s internal linking suggestions.","wordpress-seo"),"<strong>","</strong>"),(0,d.sprintf)(/* translators: %1$s expands to a strong opening tag, %2$s expands to a strong closing tag. */
(0,d.__)("%1$sNo more broken links%2$s: Automatic redirect manager.","wordpress-seo"),"<strong>","</strong>"),(0,d.sprintf)(/* translators: %1$s expands to a strong opening tag, %2$s expands to a strong closing tag. */
(0,d.__)("%1$sAppealing social previews%2$s people actually want to click on.","wordpress-seo"),"<strong>","</strong>"),(0,d.sprintf)(/* translators: %1$s expands to a strong opening tag, %2$s expands to a strong closing tag. */
(0,d.__)("%1$s24/7 support%2$s: Also on evenings and weekends.","wordpress-seo"),"<strong>","</strong>")],upsellButtonText:(0,d.sprintf)(/* translators: %s expands to 'Yoast SEO Premium'. */
(0,d.__)("Unlock with %s","wordpress-seo"),"Yoast SEO Premium"),upsellButton:{href:s,className:"yoast-button-upsell",rel:null,"data-ctb-id":"f6a84663-465f-4cb5-8ba5-f7a6d72224b2","data-action":"load-nfd-ctb"},upsellButtonLabel:(0,d.__)("1 year of premium support and updates included!","wordpress-seo")})};Ke.propTypes={buyLink:x().string.isRequired,description:x().string},Ke.defaultProps={description:Be};const Fe=Ke;class De extends k.Component{constructor(e){super(e);const t=this.props.results;this.state={mappedResults:{}},null!==t&&(this.state={mappedResults:U(t,this.props.keywordKey)}),this.handleMarkButtonClick=this.handleMarkButtonClick.bind(this),this.handleEditButtonClick=this.handleEditButtonClick.bind(this),this.handleResultsChange=this.handleResultsChange.bind(this),this.renderHighlightingUpsell=this.renderHighlightingUpsell.bind(this),this.createMarkButton=this.createMarkButton.bind(this)}componentDidUpdate(e){null!==this.props.results&&this.props.results!==e.results&&this.setState({mappedResults:U(this.props.results,this.props.keywordKey)})}createMarkButton({ariaLabel:e,id:t,className:s,status:n,onClick:r,isPressed:a}){return(0,v.createElement)(k.Fragment,null,(0,v.createElement)(T.IconButtonToggle,{marksButtonStatus:n,className:s,onClick:r,id:t,icon:"eye",pressed:a,ariaLabel:e}),this.props.shouldUpsellHighlighting&&(0,v.createElement)("div",{className:"yst-root"},(0,v.createElement)(Ee.Badge,{className:"yst-absolute yst-px-[3px] yst-py-[3px] yst--right-[6.5px] yst--top-[6.5px]",size:"small",variant:"upsell"},(0,v.createElement)(Re,{className:"yst-w-2.5 yst-h-2.5 yst-shrink-0",role:"img","aria-hidden":!0,focusable:!1}))))}deactivateMarker(){this.props.setActiveMarker(null),this.props.setMarkerPauseStatus(!1),this.removeMarkers()}activateMarker(e,t){this.props.setActiveMarker(e),t()}handleMarkButtonClick(e,t){const s=this.props.keywordKey.length>0?`${this.props.keywordKey}:${e}`:e;s===this.props.activeMarker?this.deactivateMarker():this.activateMarker(s,t)}handleResultsChange(e,t,s){const n=this.props.keywordKey.length>0?`${this.props.keywordKey}:${e}`:e;n===this.props.activeMarker&&(s?(0,c.isUndefined)(t)||this.activateMarker(n,t):this.deactivateMarker())}focusOnKeyphraseField(e){const t=this.props.keywordKey,s=""===t?"focus-keyword-input-"+e:"yoast-keyword-input-"+t+"-"+e,n=document.getElementById(s);n.focus(),n.scrollIntoView({behavior:"auto",block:"center",inline:"center"})}focusOnGooglePreviewField(e,t){let s;s="metaDescriptionKeyword"===e||"metaDescriptionLength"===e?"description":"titleWidth"===e||"keyphraseInSEOTitle"===e?"title":"slug";const n=document.getElementById("yoast-google-preview-"+s+"-"+t);n.focus(),n.scrollIntoView({behavior:"auto",block:"center",inline:"center"})}handleEditButtonClick(e){const t=this.props.location;"functionWordsInKeyphrase"!==e&&"keyphraseLength"!==e?(["metaDescriptionKeyword","metaDescriptionLength","titleWidth","keyphraseInSEOTitle","slugKeyword"].includes(e)&&this.handleGooglePreviewFocus(t,e),(0,m.doAction)("yoast.focus.input",e)):this.focusOnKeyphraseField(t)}handleGooglePreviewFocus(e,t){if("sidebar"===e)document.getElementById("yoast-search-appearance-modal-open-button").click(),setTimeout((()=>this.focusOnGooglePreviewField(t,"modal")),500);else{const s=document.getElementById("yoast-snippet-editor-metabox");s&&"false"===s.getAttribute("aria-expanded")?(s.click(),setTimeout((()=>this.focusOnGooglePreviewField(t,e)),100)):this.focusOnGooglePreviewField(t,e)}}removeMarkers(){window.YoastSEO.analysis.applyMarks(new p.Paper("",{}),[])}renderHighlightingUpsell(e,t){const s=(0,d.__)("Highlight areas of improvement in your text, no more searching for a needle in a haystack, straight to optimizing! Now also in Elementor!","wordpress-seo");return e&&(0,v.createElement)(X,{title:(0,d.__)("Unlock Premium SEO analysis","wordpress-seo"),onRequestClose:t,additionalClassName:"",className:`${J} yoast-gutenberg-modal__box yoast-gutenberg-modal__no-padding`,id:"yoast-premium-seo-analysis-highlighting-modal",shouldCloseOnClickOutside:!0},(0,v.createElement)(_e,null,(0,v.createElement)(Fe,{buyLink:this.props.highlightingUpsellLink,description:s})))}render(){const{mappedResults:e}=this.state,{errorsResults:t,improvementsResults:s,goodResults:n,considerationsResults:r,problemsResults:a}=e,{upsellResults:o,resultCategoryLabels:i}=this.props,l={errors:(0,d.__)("Errors","wordpress-seo"),problems:(0,d.__)("Problems","wordpress-seo"),improvements:(0,d.__)("Improvements","wordpress-seo"),considerations:(0,d.__)("Considerations","wordpress-seo"),goodResults:(0,d.__)("Good results","wordpress-seo")},c=Object.assign(l,i);let u=this.props.marksButtonStatus;return"enabled"===u&&this.props.shortcodesForParsing.length>0&&(u="disabled"),(0,v.createElement)(k.Fragment,null,(0,v.createElement)(ke.ContentAnalysis,{errorsResults:t,problemsResults:a,upsellResults:o,improvementsResults:s,considerationsResults:r,goodResults:n,activeMarker:this.props.activeMarker,onMarkButtonClick:this.handleMarkButtonClick,onEditButtonClick:this.handleEditButtonClick,marksButtonClassName:this.props.marksButtonClassName,editButtonClassName:this.props.editButtonClassName,marksButtonStatus:u,headingLevel:3,keywordKey:this.props.keywordKey,isPremium:this.props.isPremium,resultCategoryLabels:c,onResultChange:this.handleResultsChange,shouldUpsellHighlighting:this.props.shouldUpsellHighlighting,renderHighlightingUpsell:this.renderHighlightingUpsell,markButtonFactory:this.createMarkButton}))}}De.propTypes={results:x().array,upsellResults:x().array,marksButtonClassName:x().string,editButtonClassName:x().string,marksButtonStatus:x().oneOf(["enabled","disabled","hidden"]),setActiveMarker:x().func.isRequired,setMarkerPauseStatus:x().func.isRequired,activeMarker:x().string,keywordKey:x().string,location:x().string,isPremium:x().bool,resultCategoryLabels:x().shape({errors:x().string,problems:x().string,improvements:x().string,considerations:x().string,goodResults:x().string}),shortcodesForParsing:x().array,shouldUpsellHighlighting:x().bool,highlightingUpsellLink:x().string},De.defaultProps={results:null,upsellResults:[],marksButtonStatus:"enabled",marksButtonClassName:"",editButtonClassName:"",activeMarker:null,keywordKey:"",location:"",isPremium:!1,resultCategoryLabels:{},shortcodesForParsing:[],shouldUpsellHighlighting:!1,highlightingUpsellLink:""};const $e=De,He=(0,me.compose)([(0,he.withSelect)((e=>{const{getActiveMarker:t,getIsPremium:s,getShortcodesForParsing:n}=e("yoast-seo/editor");return{activeMarker:t(),isPremium:s(),shortcodesForParsing:n()}})),(0,he.withDispatch)((e=>{const{setActiveMarker:t,setMarkerPauseStatus:s}=e("yoast-seo/editor");return{setActiveMarker:t,setMarkerPauseStatus:s}}))])($e),We=()=>(0,v.createElement)("p",{className:"yoast-related-keyphrases-modal__loading-message"},(0,d.sprintf)(/* translators: %1$s expands to "Yoast SEO", %2$s expands to "Semrush". */
(0,d.__)("Please wait while %1$s connects to %2$s to get related keyphrases...","wordpress-seo"),"Yoast SEO","Semrush")," ",(0,v.createElement)(T.SvgIcon,{icon:"loading-spinner"})),je=(0,S.makeOutboundLink)(),ze=()=>(0,v.createElement)(k.Fragment,null,(0,v.createElement)("p",null,(0,d.sprintf)(/* translators: %s : Expands to "Semrush". */
(0,d.__)("You've reached your request limit for today. Check back tomorrow or upgrade your plan over at %s.","wordpress-seo"),"Semrush")),(0,v.createElement)(je,{href:window.wpseoAdminL10n["shortlinks.semrush.prices"],className:"yoast-button-upsell"},(0,d.sprintf)(/* translators: %s : Expands to "Semrush". */
(0,d.__)("Upgrade your %s plan","wordpress-seo"),"Semrush"),(0,v.createElement)("span",{"aria-hidden":"true",className:"yoast-button-upsell__caret"}))),Ye=window.wp.apiFetch;var Ue=s.n(Ye);const Ve="yoast-semrush-country-selector",Ge=[{value:"us",name:"United States - US"},{value:"uk",name:"United Kingdom - UK"},{value:"ca",name:"Canada - CA"},{value:"ru",name:"Russia - RU"},{value:"de",name:"Germany - DE"},{value:"fr",name:"France - FR"},{value:"es",name:"Spain - ES"},{value:"it",name:"Italy - IT"},{value:"br",name:"Brazil - BR"},{value:"au",name:"Australia - AU"},{value:"ar",name:"Argentina - AR"},{value:"be",name:"Belgium - BE"},{value:"ch",name:"Switzerland - CH"},{value:"dk",name:"Denmark - DK"},{value:"fi",name:"Finland - FI"},{value:"hk",name:"Hong Kong - HK"},{value:"ie",name:"Ireland - IE"},{value:"il",name:"Israel - IL"},{value:"mx",name:"Mexico - MX"},{value:"nl",name:"Netherlands - NL"},{value:"no",name:"Norway - NO"},{value:"pl",name:"Poland - PL"},{value:"se",name:"Sweden - SE"},{value:"sg",name:"Singapore - SG"},{value:"tr",name:"Turkey - TR"},{value:"jp",name:"Japan - JP"},{value:"in",name:"India - IN"},{value:"hu",name:"Hungary - HU"},{value:"af",name:"Afghanistan - AF"},{value:"al",name:"Albania - AL"},{value:"dz",name:"Algeria - DZ"},{value:"ao",name:"Angola - AO"},{value:"am",name:"Armenia - AM"},{value:"at",name:"Austria - AT"},{value:"az",name:"Azerbaijan - AZ"},{value:"bh",name:"Bahrain - BH"},{value:"bd",name:"Bangladesh - BD"},{value:"by",name:"Belarus - BY"},{value:"bz",name:"Belize - BZ"},{value:"bo",name:"Bolivia - BO"},{value:"ba",name:"Bosnia and Herzegovina - BA"},{value:"bw",name:"Botswana - BW"},{value:"bn",name:"Brunei - BN"},{value:"bg",name:"Bulgaria - BG"},{value:"cv",name:"Cabo Verde - CV"},{value:"kh",name:"Cambodia - KH"},{value:"cm",name:"Cameroon - CM"},{value:"cl",name:"Chile - CL"},{value:"co",name:"Colombia - CO"},{value:"cr",name:"Costa Rica - CR"},{value:"hr",name:"Croatia - HR"},{value:"cy",name:"Cyprus - CY"},{value:"cz",name:"Czech Republic - CZ"},{value:"cd",name:"Congo - CD"},{value:"do",name:"Dominican Republic - DO"},{value:"ec",name:"Ecuador - EC"},{value:"eg",name:"Egypt - EG"},{value:"sv",name:"El Salvador - SV"},{value:"ee",name:"Estonia - EE"},{value:"et",name:"Ethiopia - ET"},{value:"ge",name:"Georgia - GE"},{value:"gh",name:"Ghana - GH"},{value:"gr",name:"Greece - GR"},{value:"gt",name:"Guatemala - GT"},{value:"gy",name:"Guyana - GY"},{value:"ht",name:"Haiti - HT"},{value:"hn",name:"Honduras - HN"},{value:"is",name:"Iceland - IS"},{value:"id",name:"Indonesia - ID"},{value:"jm",name:"Jamaica - JM"},{value:"jo",name:"Jordan - JO"},{value:"kz",name:"Kazakhstan - KZ"},{value:"kw",name:"Kuwait - KW"},{value:"lv",name:"Latvia - LV"},{value:"lb",name:"Lebanon - LB"},{value:"lt",name:"Lithuania - LT"},{value:"lu",name:"Luxembourg - LU"},{value:"mg",name:"Madagascar - MG"},{value:"my",name:"Malaysia - MY"},{value:"mt",name:"Malta - MT"},{value:"mu",name:"Mauritius - MU"},{value:"md",name:"Moldova - MD"},{value:"mn",name:"Mongolia - MN"},{value:"me",name:"Montenegro - ME"},{value:"ma",name:"Morocco - MA"},{value:"mz",name:"Mozambique - MZ"},{value:"na",name:"Namibia - NA"},{value:"np",name:"Nepal - NP"},{value:"nz",name:"New Zealand - NZ"},{value:"ni",name:"Nicaragua - NI"},{value:"ng",name:"Nigeria - NG"},{value:"om",name:"Oman - OM"},{value:"py",name:"Paraguay - PY"},{value:"pe",name:"Peru - PE"},{value:"ph",name:"Philippines - PH"},{value:"pt",name:"Portugal - PT"},{value:"ro",name:"Romania - RO"},{value:"sa",name:"Saudi Arabia - SA"},{value:"sn",name:"Senegal - SN"},{value:"rs",name:"Serbia - RS"},{value:"sk",name:"Slovakia - SK"},{value:"si",name:"Slovenia - SI"},{value:"za",name:"South Africa - ZA"},{value:"kr",name:"South Korea - KR"},{value:"lk",name:"Sri Lanka - LK"},{value:"th",name:"Thailand - TH"},{value:"bs",name:"Bahamas - BS"},{value:"tt",name:"Trinidad and Tobago - TT"},{value:"tn",name:"Tunisia - TN"},{value:"ua",name:"Ukraine - UA"},{value:"ae",name:"United Arab Emirates - AE"},{value:"uy",name:"Uruguay - UY"},{value:"ve",name:"Venezuela - VE"},{value:"vn",name:"Vietnam - VN"},{value:"zm",name:"Zambia - ZM"},{value:"zw",name:"Zimbabwe - ZW"},{value:"ly",name:"Libya - LY"}];class Ze extends k.Component{constructor(e){super(e),this.relatedKeyphrasesRequest=this.relatedKeyphrasesRequest.bind(this),this.onChangeHandler=this.onChangeHandler.bind(this)}componentDidMount(){this.props.response&&this.props.keyphrase===this.props.lastRequestKeyphrase||this.relatedKeyphrasesRequest()}storeCountryCode(e){Ue()({path:"yoast/v1/semrush/country_code",method:"POST",data:{country_code:e}})}async relatedKeyphrasesRequest(){const{keyphrase:e,countryCode:t,newRequest:s}=this.props;s(t,e),this.storeCountryCode(t);const n=await this.doRequest(e,t);200!==n.status?this.handleFailedResponse(n):this.handleSuccessResponse(n)}handleSuccessResponse(e){const{setNoResultsFound:t,setRequestSucceeded:s}=this.props;0!==e.results.rows.length?s(e):t()}handleFailedResponse(e){const{setRequestLimitReached:t,setRequestFailed:s}=this.props;"error"in e&&(e.error.includes("TOTAL LIMIT EXCEEDED")?t():s(e))}async doRequest(e,t){return await Ue()({path:(0,xe.addQueryArgs)("/yoast/v1/semrush/related_keyphrases",{keyphrase:e,country_code:t})})}onChangeHandler(e){this.props.setCountry(e)}render(){return(0,v.createElement)("div",{id:Ve},(0,v.createElement)(T.SingleSelect,{id:Ve+"-select",label:(0,d.__)("Show results for:","wordpress-seo"),name:"semrush-country-code",options:Ge,selected:this.props.countryCode,onChange:this.onChangeHandler,wrapperClassName:"yoast-field-group yoast-field-group--inline"}),(0,v.createElement)(T.NewButton,{id:Ve+"-button",variant:"secondary",onClick:this.relatedKeyphrasesRequest},(0,d.__)("Select country","wordpress-seo")))}}Ze.propTypes={keyphrase:x().string,countryCode:x().string,response:x().object,lastRequestKeyphrase:x().string,setCountry:x().func.isRequired,newRequest:x().func.isRequired,setNoResultsFound:x().func.isRequired,setRequestSucceeded:x().func.isRequired,setRequestLimitReached:x().func.isRequired,setRequestFailed:x().func.isRequired},Ze.defaultProps={keyphrase:"",countryCode:"us",response:{},lastRequestKeyphrase:""};const Je=Ze,Qe=({data:e,mapChartDataToTableData:t,dataTableCaption:s,dataTableHeaderLabels:n,isDataTableVisuallyHidden:r})=>e.length!==n.length?(0,v.createElement)("p",null,(0,d.__)("The number of headers and header labels don't match.","wordpress-seo")):(0,v.createElement)("div",{className:r?"screen-reader-text":null},(0,v.createElement)("table",null,(0,v.createElement)("caption",null,s),(0,v.createElement)("thead",null,(0,v.createElement)("tr",null,n.map(((e,t)=>(0,v.createElement)("th",{key:t},e))))),(0,v.createElement)("tbody",null,(0,v.createElement)("tr",null,e.map(((e,s)=>(0,v.createElement)("td",{key:s},t(e.y))))))));Qe.propTypes={data:x().arrayOf(x().shape({x:x().number,y:x().number})).isRequired,mapChartDataToTableData:x().func,dataTableCaption:x().string.isRequired,dataTableHeaderLabels:x().array.isRequired,isDataTableVisuallyHidden:x().bool},Qe.defaultProps={mapChartDataToTableData:null,isDataTableVisuallyHidden:!0};const Xe=Qe,et=({data:e,width:t,height:s,fillColor:n,strokeColor:r,strokeWidth:a,className:o,mapChartDataToTableData:i,dataTableCaption:l,dataTableHeaderLabels:c,isDataTableVisuallyHidden:u})=>{const d=Math.max(1,Math.max(...e.map((e=>e.x)))),p=Math.max(1,Math.max(...e.map((e=>e.y)))),m=s-a,h=e.map((e=>`${e.x/d*t},${m-e.y/p*m+a}`)).join(" "),g=`0,${m+a} `+h+` ${t},${m+a}`;return(0,v.createElement)(k.Fragment,null,(0,v.createElement)("svg",{width:t,height:s,viewBox:`0 0 ${t} ${s}`,className:o,role:"img","aria-hidden":"true",focusable:"false"},(0,v.createElement)("polygon",{fill:n,points:g}),(0,v.createElement)("polyline",{fill:"none",stroke:r,strokeWidth:a,strokeLinejoin:"round",strokeLinecap:"round",points:h})),i&&(0,v.createElement)(Xe,{data:e,mapChartDataToTableData:i,dataTableCaption:l,dataTableHeaderLabels:c,isDataTableVisuallyHidden:u}))};et.propTypes={data:x().arrayOf(x().shape({x:x().number,y:x().number})).isRequired,width:x().number.isRequired,height:x().number.isRequired,fillColor:x().string,strokeColor:x().string,strokeWidth:x().number,className:x().string,mapChartDataToTableData:x().func,dataTableCaption:x().string.isRequired,dataTableHeaderLabels:x().array.isRequired,isDataTableVisuallyHidden:x().bool},et.defaultProps={fillColor:null,strokeColor:"#000000",strokeWidth:1,className:"",mapChartDataToTableData:null,isDataTableVisuallyHidden:!0};const tt=et,st=(0,S.makeOutboundLink)();class nt extends k.Component{constructor(e){super(e),this.transformTrendDataToChartPoints=this.transformTrendDataToChartPoints.bind(this),this.getAreaChartDataTableHeaderLabels=this.getAreaChartDataTableHeaderLabels.bind(this),this.mapAreaChartDataToTableData=this.mapAreaChartDataToTableData.bind(this)}transformTrendDataToChartPoints(e){return e.split(",").map(((e,t)=>({x:t,y:parseFloat(e)})))}getAreaChartDataTableHeaderLabels(){return[(0,d.__)("Twelve months ago","wordpress-seo"),(0,d.__)("Eleven months ago","wordpress-seo"),(0,d.__)("Ten months ago","wordpress-seo"),(0,d.__)("Nine months ago","wordpress-seo"),(0,d.__)("Eight months ago","wordpress-seo"),(0,d.__)("Seven months ago","wordpress-seo"),(0,d.__)("Six months ago","wordpress-seo"),(0,d.__)("Five months ago","wordpress-seo"),(0,d.__)("Four months ago","wordpress-seo"),(0,d.__)("Three months ago","wordpress-seo"),(0,d.__)("Two months ago","wordpress-seo"),(0,d.__)("Last month","wordpress-seo")]}mapAreaChartDataToTableData(e){return Math.round(100*e)}render(){const{keyphrase:e,relatedKeyphrases:t,countryCode:s,data:n,renderAction:r}=this.props,a="https://www.semrush.com/analytics/keywordoverview/?q="+encodeURIComponent(e)+"&db="+encodeURIComponent(s);return n&&!(0,c.isEmpty)(n.results)&&(0,v.createElement)(k.Fragment,null,(0,v.createElement)("table",{className:"yoast yoast-table"},(0,v.createElement)("thead",null,(0,v.createElement)("tr",null,(0,v.createElement)("th",{scope:"col",className:"yoast-table--primary"},(0,d.__)("Related keyphrase","wordpress-seo")),(0,v.createElement)("th",{scope:"col",abbr:(0,d.__)("Volume","wordpress-seo")},(0,d.__)("Volume","wordpress-seo"),(0,v.createElement)(V,{href:window.wpseoAdminL10n["shortlinks.semrush.volume_help"],className:"dashicons"},(0,v.createElement)("span",{className:"screen-reader-text"},/* translators: Hidden accessibility text. */
(0,d.__)("Learn more about the related keyphrases volume","wordpress-seo")))),(0,v.createElement)("th",{scope:"col",abbr:(0,d.__)("Trend","wordpress-seo")},(0,d.__)("Trend","wordpress-seo"),(0,v.createElement)(V,{href:window.wpseoAdminL10n["shortlinks.semrush.trend_help"],className:"dashicons"},(0,v.createElement)("span",{className:"screen-reader-text"},/* translators: Hidden accessibility text. */
(0,d.__)("Learn more about the related keyphrases trend","wordpress-seo")))),r&&(0,v.createElement)("td",{className:"yoast-table--nobreak"}))),(0,v.createElement)("tbody",null,n.results.rows.map(((e,s)=>{const n=e[0],a=this.transformTrendDataToChartPoints(e[2]),o=this.getAreaChartDataTableHeaderLabels();return(0,v.createElement)("tr",{key:s},(0,v.createElement)("td",null,n),(0,v.createElement)("td",null,e[1]),(0,v.createElement)("td",{className:"yoast-table--nopadding"},(0,v.createElement)(tt,{width:66,height:24,data:a,strokeWidth:1.8,strokeColor:"#498afc",fillColor:"#ade3fc",className:"yoast-related-keyphrases-modal__chart",mapChartDataToTableData:this.mapAreaChartDataToTableData,dataTableCaption:(0,d.__)("Keyphrase volume in the last 12 months on a scale from 0 to 100.","wordpress-seo"),dataTableHeaderLabels:o})),r&&(0,v.createElement)("td",{className:"yoast-table--nobreak"},r(n,t)))})))),(0,v.createElement)("p",{style:{marginBottom:0}},(0,v.createElement)(st,{href:a},(0,d.sprintf)(/* translators: %s expands to Semrush */
(0,d.__)("Get more insights at %s","wordpress-seo"),"Semrush"))))}}nt.propTypes={data:x().object,keyphrase:x().string,relatedKeyphrases:x().array,countryCode:x().string,renderAction:x().func},nt.defaultProps={data:{},keyphrase:"",relatedKeyphrases:[],countryCode:"us",renderAction:null};const rt=nt,at=(0,S.makeOutboundLink)(),ot=()=>(0,v.createElement)(T.Alert,{type:"info"},(0,d.sprintf)(/* translators: %s: Expands to "Yoast SEO". */
(0,d.__)("You’ll reach more people with multiple keyphrases! Want to quickly add these related keyphrases to the %s analyses for even better content optimization?","wordpress-seo"),"Yoast SEO")+" ",(0,v.createElement)(at,{href:window.wpseoAdminL10n["shortlinks.semrush.premium_landing_page"]},(0,d.sprintf)(/* translators: %s: Expands to "Yoast SEO Premium". */
(0,d.__)("Explore %s!","wordpress-seo"),"Yoast SEO Premium"))),it=()=>(0,v.createElement)(T.Alert,{type:"error"},(0,d.__)("We've encountered a problem trying to get related keyphrases. Please try again later.","wordpress-seo")),lt=()=>(0,v.createElement)(T.Alert,{type:"warning"},(0,d.sprintf)(/* translators: %s: Expands to "Yoast SEO". */
(0,d.__)("You've reached the maximum amount of 4 related keyphrases. You can change or remove related keyphrases in the %s metabox or sidebar.","wordpress-seo"),"Yoast SEO"));function ct(e){const{response:t,lastRequestKeyphrase:s,keyphrase:n,newRequest:r,setCountry:a,renderAction:o,countryCode:i,requestLimitReached:l,setRequestFailed:p,setNoResultsFound:m,relatedKeyphrases:h,setRequestSucceeded:g,setRequestLimitReached:y}=e,f=u().isPremium;return(0,v.createElement)(k.Fragment,null,!l&&(0,v.createElement)(k.Fragment,null,!f&&(0,v.createElement)(ot,null),f&&function(e){return e&&e.length>=4}(h)&&(0,v.createElement)(lt,null),(0,v.createElement)(Je,{countryCode:i,setCountry:a,newRequest:r,keyphrase:n,setRequestFailed:p,setNoResultsFound:m,setRequestSucceeded:g,setRequestLimitReached:y,response:t,lastRequestKeyphrase:s})),function(e){const{isPending:t,requestLimitReached:s,isSuccess:n,response:r,requestHasData:a}=e;return t?(0,v.createElement)(We,null):s?(0,v.createElement)(ze,null):!n&&function(e){return!(0,c.isEmpty)(e)&&"error"in e}(r)?(0,v.createElement)(it,null):a?void 0:(0,v.createElement)("p",null,(0,d.__)("Sorry, there's no data available for that keyphrase/country combination.","wordpress-seo"))}(e),(0,v.createElement)(rt,{keyphrase:n,relatedKeyphrases:h,countryCode:i,renderAction:o,data:t}))}ct.propTypes={keyphrase:x().string,relatedKeyphrases:x().array,renderAction:x().func,requestLimitReached:x().bool,countryCode:x().string.isRequired,setCountry:x().func.isRequired,newRequest:x().func.isRequired,setRequestSucceeded:x().func.isRequired,setRequestLimitReached:x().func.isRequired,setRequestFailed:x().func.isRequired,setNoResultsFound:x().func.isRequired,response:x().object,lastRequestKeyphrase:x().string},ct.defaultProps={keyphrase:"",relatedKeyphrases:[],renderAction:null,requestLimitReached:!1,response:{},lastRequestKeyphrase:""};const ut=(0,me.compose)([(0,he.withSelect)((e=>{const{getFocusKeyphrase:t,getSEMrushSelectedCountry:s,getSEMrushRequestLimitReached:n,getSEMrushRequestResponse:r,getSEMrushRequestIsSuccess:a,getSEMrushIsRequestPending:o,getSEMrushRequestHasData:i,getSEMrushRequestKeyphrase:l}=e("yoast-seo/editor");return{keyphrase:t(),countryCode:s(),requestLimitReached:n(),response:r(),isSuccess:a(),isPending:o(),requestHasData:i(),lastRequestKeyphrase:l()}})),(0,he.withDispatch)((e=>{const{setSEMrushChangeCountry:t,setSEMrushNewRequest:s,setSEMrushRequestSucceeded:n,setSEMrushRequestFailed:r,setSEMrushSetRequestLimitReached:a,setSEMrushNoResultsFound:o}=e("yoast-seo/editor");return{setCountry:e=>{t(e)},newRequest:(e,t)=>{s(e,t)},setRequestSucceeded:e=>{n(e)},setRequestFailed:e=>{r(e)},setRequestLimitReached:()=>{a()},setNoResultsFound:()=>{o()}}}))])(ct),dt=window.moment;var pt=s.n(dt),mt=s(6746);const ht=(0,S.makeOutboundLink)(),gt=e=>{const t=(0,d.sprintf)(/* translators: %d expands to the amount of allowed keyphrases on a free account, %s expands to a link to Wincher plans. */
(0,d.__)("You've reached the maximum amount of %d keyphrases you can add to your Wincher account. If you wish to add more keyphrases, please %s.","wordpress-seo"),e.limit,"{{updateWincherPlanLink/}}");return(0,v.createElement)(T.Alert,{type:"error"},(0,mt.Z)({mixedString:t,components:{updateWincherPlanLink:(0,v.createElement)(ht,{href:wpseoAdminGlobalL10n["links.wincher.pricing"]},(0,d.sprintf)(/* translators: %s : Expands to "Wincher". */
(0,d.__)("upgrade your %s plan","wordpress-seo"),"Wincher"))}}))};gt.propTypes={limit:x().number},gt.defaultProps={limit:10};const yt=gt,ft=()=>(0,v.createElement)(T.Alert,{type:"error"},(0,d.__)("Something went wrong while tracking the ranking position(s) of your page. Please try again later.","wordpress-seo"));async function bt(e,t,s,n=200){try{const r=await e();return!!r&&(r.status===n?t(r):s(r))}catch(e){console.error(e.message)}}async function wt(e){try{return await Ue()(e)}catch(e){return e.error&&e.status?e:e instanceof Response&&await e.json()}}async function vt(e){return(0,c.isArray)(e)||(e=[e]),await wt({path:"yoast/v1/wincher/keyphrases/track",method:"POST",data:{keyphrases:e}})}const kt=R().p`
	color: ${C.colors.$color_pink_dark};
	font-size: 14px;
	font-weight: 700;
	margin: 13px 0 10px;
`,Et=R()(T.SvgIcon)`
	margin-right: 5px;
	vertical-align: middle;
`,Rt=R().button`
	position: absolute;
	top: 9px;
	right: 9px;
	border: none;
    background: none;
    cursor: pointer;
`,_t=R().p`
	font-size: 13px;
	font-weight: 500;
	margin: 10px 0 13px;
`,xt=R().div`
	position: relative;
	background: ${e=>e.isTitleShortened?"#F5F7F7":"transparent"};
	border: 1px solid #C7C7C7;
	border-left: 4px solid${C.colors.$color_pink_dark};
	padding: 0 16px;
	margin-bottom: 1.5em;
`,Tt=e=>{const[t,s]=(0,k.useState)(null);return(0,k.useEffect)((()=>{e&&!t&&async function(){return await wt({path:"yoast/v1/wincher/account/limit",method:"GET"})}().then((e=>s(e)))}),[t]),t};Tt.propTypes={limit:x().bool.isRequired};const St=({limit:e,usage:t,isTitleShortened:s,isFreeAccount:n})=>{const r=(0,d.sprintf)(
/* Translators: %1$s expands to the number of used keywords.
   * %2$s expands to the account keywords limit.
   */
(0,d.__)("Your are tracking %1$s out of %2$s keyphrases included in your free account.","wordpress-seo"),t,e),a=(0,d.sprintf)(
/* Translators: %1$s expands to the number of used keywords.
   * %2$s expands to the account keywords limit.
   */
(0,d.__)("Your are tracking %1$s out of %2$s keyphrases included in your account.","wordpress-seo"),t,e),o=n?r:a,i=(0,d.sprintf)(
/* Translators: %1$s expands to the number of used keywords.
   * %2$s expands to the account keywords limit.
   */
(0,d.__)("Keyphrases tracked: %1$s/%2$s","wordpress-seo"),t,e),l=s?i:o;return(0,v.createElement)(kt,null,s&&(0,v.createElement)(Et,{icon:"exclamation-triangle",color:C.colors.$color_pink_dark,size:"14px"}),l)};St.propTypes={limit:x().number.isRequired,usage:x().number.isRequired,isTitleShortened:x().bool,isFreeAccount:x().bool};const Ct=(0,S.makeOutboundLink)(),It=({discount:e,months:t})=>{const s=(0,v.createElement)(Ct,{href:wpseoAdminGlobalL10n["links.wincher.upgrade"],style:{fontWeight:600}},(0,d.sprintf)(/* Translators: %s : Expands to "Wincher". */
(0,d.__)("Click here to upgrade your %s plan","wordpress-seo"),"Wincher"));if(!e||!t)return(0,v.createElement)(_t,null,s);const n=100*e,r=(0,d.sprintf)(
/* Translators: %1$s expands to upgrade account link.
   * %2$s expands to the upgrade discount value.
   * %3$s expands to the upgrade discount duration e.g. 2 months.
   */
(0,d.__)("%1$s and get an exclusive %2$s discount for %3$s month(s).","wordpress-seo"),"{{wincherAccountUpgradeLink/}}",n+"%",t);return(0,v.createElement)(_t,null,(0,mt.Z)({mixedString:r,components:{wincherAccountUpgradeLink:s}}))};It.propTypes={discount:x().number,months:x().number};const Lt=({onClose:e,isTitleShortened:t,trackingInfo:s})=>{const n=(()=>{const[e,t]=(0,k.useState)(null);return(0,k.useEffect)((()=>{e||async function(){return await wt({path:"yoast/v1/wincher/account/upgrade-campaign",method:"GET"})}().then((e=>t(e)))}),[e]),e})();if(null===s)return null;const{limit:r,usage:a}=s;if(!(r&&a/r>=.8))return null;const o=Boolean(null==n?void 0:n.discount);return(0,v.createElement)(xt,{isTitleShortened:t},e&&(0,v.createElement)(Rt,{type:"button","aria-label":(0,d.__)("Close the upgrade callout","wordpress-seo"),onClick:e},(0,v.createElement)(T.SvgIcon,{icon:"times-circle",color:C.colors.$color_pink_dark,size:"14px"})),(0,v.createElement)(St,{...s,isTitleShortened:t,isFreeAccount:o}),(0,v.createElement)(It,{discount:null==n?void 0:n.discount,months:null==n?void 0:n.months}))};Lt.propTypes={onClose:x().func,isTitleShortened:x().bool,trackingInfo:x().object};const qt=Lt,Pt=()=>(0,v.createElement)(T.Alert,{type:"success"},(0,d.sprintf)(/* translators: %s: Expands to "Wincher". */
(0,d.__)("You have successfully connected to %s! You can now track the SEO performance for the keyphrase(s) of this page.","wordpress-seo"),"Wincher")),At=()=>(0,v.createElement)(T.Alert,{type:"info"},(0,d.sprintf)(/* translators: %s: Expands to "Wincher". */
(0,d.__)("%s is currently tracking the ranking position(s) of your page. This may take a few minutes. Please wait or check back later.","wordpress-seo"),"Wincher")),Nt=()=>(0,v.createElement)("p",{className:"yoast-wincher-seo-performance-modal__loading-message"},(0,d.__)("Tracking the ranking position...","wordpress-seo")," ",(0,v.createElement)(T.SvgIcon,{icon:"loading-spinner"})),Mt=R()(T.SvgIcon)`
	margin-left: 2px;
	flex-shrink: 0;
	rotate: ${e=>e.isImproving?"-90deg":"90deg"};
`,Ot=R().span`
	color: ${e=>e.isImproving?"#69AB56":"#DC3332"};
	font-size: 13px;
	font-weight: 600;
	line-height: 20px;
	margin-right: 2px;
	margin-left: 12px;
`,Bt=R().td`
	padding-right: 0 !important;

	& > div {
		margin: 0px;
	}
`,Kt=R().td`
	padding-left: 2px !important;
`,Ft=R().td.attrs({className:"yoast-table--nopadding"})`
	& > div {
		justify-content: center;
	}
`,Dt=R().div`
	display: flex;
	align-items: center;
	& > a {
		box-sizing: border-box;
	}
`,$t=R().button`
	background: none;
	color: inherit;
	border: none;
	padding: 0;
	font: inherit;
	cursor: pointer;
	outline: inherit;
    display: flex;
    align-items: center;
`,Ht=R().tr`
	background-color: ${e=>e.isEnabled?"#FFFFFF":"#F9F9F9"} !important;
`;function Wt(e){return Math.round(100*e)}function jt({chartData:e}){if((0,c.isEmpty)(e)||(0,c.isEmpty)(e.position))return"?";const t=function(e){return Array.from({length:e.position.history.length},((e,t)=>t+1)).map((e=>(0,d.sprintf)((0,d._n)("%d day","%d days",e,"wordpress-seo"),e)))}(e),s=e.position.history.map(((e,t)=>({x:t,y:101-e.value})));return(0,v.createElement)(tt,{width:66,height:24,data:s,strokeWidth:1.8,strokeColor:"#498afc",fillColor:"#ade3fc",mapChartDataToTableData:Wt,dataTableCaption:(0,d.__)("Keyphrase position in the last 90 days on a scale from 0 to 100.","wordpress-seo"),dataTableHeaderLabels:t})}jt.propTypes={chartData:x().object},jt.defaultProps={chartData:{}};const zt=({rowData:e})=>{var t;if(null==e||null===(t=e.position)||void 0===t||!t.change)return(0,v.createElement)(jt,{chartData:e});const s=e.position.change<0;return(0,v.createElement)(k.Fragment,null,(0,v.createElement)(jt,{chartData:e}),(0,v.createElement)(Ot,{isImproving:s},Math.abs(e.position.change)),(0,v.createElement)(Mt,{icon:"caret-right",color:s?"#69AB56":"#DC3332",size:"14px",isImproving:s}))};function Yt(e){var t;const{keyphrase:s,rowData:n,onTrackKeyphrase:r,onUntrackKeyphrase:a,isFocusKeyphrase:o,isDisabled:i,isLoading:l,isSelected:u,onSelectKeyphrases:p}=e,m=!(0,c.isEmpty)(n),h=!(0,c.isEmpty)(null==n||null===(t=n.position)||void 0===t?void 0:t.history),g=(0,k.useCallback)((()=>{i||(m?a(s,n.id):r(s))}),[s,r,a,m,n,i]),y=(0,k.useCallback)((()=>{p((e=>u?e.filter((e=>e!==s)):e.concat(s)))}),[p,u,s]);return(0,v.createElement)(Ht,{isEnabled:m},(0,v.createElement)(Bt,null,h&&(0,v.createElement)(T.Checkbox,{id:"select-"+s,onChange:y,checked:u,label:""})),(0,v.createElement)(Kt,null,s,o&&(0,v.createElement)("span",null,"*")),function(e){const{rowData:t,websiteId:s,keyphrase:n,onSelectKeyphrases:r}=e,a=(0,k.useCallback)((()=>{r([n])}),[r,n]),o=!(0,c.isEmpty)(t),i=t&&t.updated_at&&pt()(t.updated_at)>=pt()().subtract(7,"days"),l=t?(0,d.sprintf)("https://app.wincher.com/websites/%s/keywords?serp=%s&utm_medium=plugin&utm_source=yoast&referer=yoast&partner=yoast",s,t.id):null;return o?i?(0,v.createElement)(k.Fragment,null,(0,v.createElement)("td",null,(0,v.createElement)(Dt,null,function(e){return!e||!e.position||e.position.value>100?"> 100":e.position.value}(t),(0,v.createElement)(T.ButtonStyledLink,{variant:"secondary",href:l,style:{height:28,marginLeft:12},rel:"noopener",target:"_blank"},(0,d.__)("View","wordpress-seo")))),(0,v.createElement)("td",{className:"yoast-table--nopadding"},(0,v.createElement)($t,{type:"button",onClick:a},(0,v.createElement)(zt,{rowData:t}))),(0,v.createElement)("td",null,(u=t.updated_at,pt()(u).fromNow()))):(0,v.createElement)("td",{className:"yoast-table--nopadding",colSpan:"3"},(0,v.createElement)(Nt,null)):(0,v.createElement)("td",{className:"yoast-table--nopadding",colSpan:"3"},(0,v.createElement)("i",null,(0,d.__)("Activate tracking to show the ranking position","wordpress-seo")));var u}(e),(0,v.createElement)(Ft,null,function({keyphrase:e,isEnabled:t,toggleAction:s,isLoading:n}){return n?(0,v.createElement)(T.SvgIcon,{icon:"loading-spinner"}):(0,v.createElement)(T.Toggle,{id:`toggle-keyphrase-tracking-${e}`,className:"wincher-toggle",isEnabled:t,onSetToggleState:s,showToggleStateLabel:!1})}({keyphrase:s,isEnabled:m,toggleAction:g,isLoading:l})))}zt.propTypes={rowData:x().object},Yt.propTypes={rowData:x().object,keyphrase:x().string.isRequired,onTrackKeyphrase:x().func,onUntrackKeyphrase:x().func,isFocusKeyphrase:x().bool,isDisabled:x().bool,isLoading:x().bool,websiteId:x().string,isSelected:x().bool.isRequired,onSelectKeyphrases:x().func.isRequired},Yt.defaultProps={rowData:{},onTrackKeyphrase:()=>{},onUntrackKeyphrase:()=>{},isFocusKeyphrase:!1,isDisabled:!1,isLoading:!1,websiteId:""};const Ut=(0,S.makeOutboundLink)(),Vt=R().span`
	display: block;
	font-style: italic;

	@media (min-width: 782px) {
		display: inline;
		position: absolute;
		${(0,S.getDirectionalStyle)("right","left")}: 8px;
	}
`,Gt=R().div`
	width: 100%;
	overflow-y: auto;
`,Zt=R().th`
	pointer-events: ${e=>e.isDisabled?"none":"initial"};
	padding-right: 0 !important;

	& > div {
		margin: 0px;
	}
`,Jt=R().th`
	padding-left: 2px !important;
`,Qt=e=>{const t=(0,k.useRef)();return(0,k.useEffect)((()=>{t.current=e})),t.current},Xt=(0,c.debounce)((async function(e=null,t=null,s=null,n){return await wt({path:"yoast/v1/wincher/keyphrases",method:"POST",data:{keyphrases:e,permalink:s,startAt:t},signal:n})}),500,{leading:!0}),es=e=>{const{addTrackedKeyphrase:t,isLoggedIn:s,keyphrases:n,permalink:r,removeTrackedKeyphrase:a,setKeyphraseLimitReached:o,setRequestFailed:i,setRequestSucceeded:l,setTrackedKeyphrases:u,setHasTrackedAll:p,trackAll:m,trackedKeyphrases:h,isNewlyAuthenticated:g,websiteId:y,focusKeyphrase:f,newRequest:b,startAt:w,selectedKeyphrases:E,onSelectKeyphrases:R}=e,_=(0,k.useRef)(),x=(0,k.useRef)(),S=(0,k.useRef)(!1),[C,I]=(0,k.useState)([]),L=(0,k.useCallback)((e=>{const t=e.toLowerCase();return h&&!(0,c.isEmpty)(h)&&h.hasOwnProperty(t)?h[t]:null}),[h]),q=(0,k.useMemo)((()=>async()=>{await bt((()=>(x.current&&x.current.abort(),x.current="undefined"==typeof AbortController?null:new AbortController,Xt(n,w,r,x.current.signal))),(e=>{l(e),u(e.results)}),(e=>{i(e)}))}),[l,i,u,n,r,w]),P=(0,k.useCallback)((async e=>{const s=(Array.isArray(e)?e:[e]).map((e=>e.toLowerCase()));I((e=>[...e,...s])),await bt((()=>vt(s)),(e=>{l(e),t(e.results),q()}),(e=>{400===e.status&&e.limit&&o(e.limit),i(e)}),201),I((e=>(0,c.without)(e,...s)))}),[l,i,o,t,q]),A=(0,k.useCallback)((async(e,t)=>{e=e.toLowerCase(),I((t=>[...t,e])),await bt((()=>async function(e){return await wt({path:"yoast/v1/wincher/keyphrases/untrack",method:"DELETE",data:{keyphraseID:e}})}(t)),(t=>{l(t),a(e)}),(e=>{i(e)})),I((t=>(0,c.without)(t,e)))}),[l,a,i]),N=(0,k.useCallback)((async e=>{b(),await P(e)}),[b,P]),M=Qt(r),O=Qt(n),B=Qt(w),K=r&&w;(0,k.useEffect)((()=>{s&&K&&(r!==M||(0,c.difference)(n,O).length||w!==B)&&q()}),[s,r,M,n,O,q,K,w,B]),(0,k.useEffect)((()=>{if(s&&m&&null!==h){const e=n.filter((e=>!L(e)));e.length&&P(e),p()}}),[s,m,h,P,p,L,n]),(0,k.useEffect)((()=>{g&&!S.current&&(q(),S.current=!0)}),[g,q]),(0,k.useEffect)((()=>{if(s&&!(0,c.isEmpty)(h))return(0,c.filter)(h,(e=>(0,c.isEmpty)(e.updated_at))).length>0&&(_.current=setInterval((()=>{q()}),1e4)),()=>{clearInterval(_.current)}}),[s,h,q]);const F=s&&null===h,D=(0,k.useMemo)((()=>(0,c.isEmpty)(h)?[]:Object.values(h).filter((e=>{var t;return!(0,c.isEmpty)(null==e||null===(t=e.position)||void 0===t?void 0:t.history)})).map((e=>e.keyword))),[h]),$=(0,k.useMemo)((()=>E.length>0&&D.length>0&&D.every((e=>E.includes(e)))),[E,D]),H=(0,k.useCallback)((()=>{R($?[]:D)}),[R,$,D]),W=(0,k.useMemo)((()=>(0,c.orderBy)(n,[e=>Object.values(h||{}).map((e=>e.keyword)).includes(e)],["desc"])),[n,h]);return n&&!(0,c.isEmpty)(n)&&(0,v.createElement)(k.Fragment,null,(0,v.createElement)(Gt,null,(0,v.createElement)("table",{className:"yoast yoast-table"},(0,v.createElement)("thead",null,(0,v.createElement)("tr",null,(0,v.createElement)(Zt,{isDisabled:0===D.length},(0,v.createElement)(T.Checkbox,{id:"select-all",onChange:H,checked:$,label:""})),(0,v.createElement)(Jt,{scope:"col",abbr:(0,d.__)("Keyphrase","wordpress-seo")},(0,d.__)("Keyphrase","wordpress-seo")),(0,v.createElement)("th",{scope:"col",abbr:(0,d.__)("Position","wordpress-seo")},(0,d.__)("Position","wordpress-seo")),(0,v.createElement)("th",{scope:"col",abbr:(0,d.__)("Position over time","wordpress-seo")},(0,d.__)("Position over time","wordpress-seo")),(0,v.createElement)("th",{scope:"col",abbr:(0,d.__)("Last updated","wordpress-seo")},(0,d.__)("Last updated","wordpress-seo")),(0,v.createElement)("th",{scope:"col",abbr:(0,d.__)("Tracking","wordpress-seo")},(0,d.__)("Tracking","wordpress-seo")))),(0,v.createElement)("tbody",null,W.map(((e,t)=>(0,v.createElement)(Yt,{key:`trackable-keyphrase-${t}`,keyphrase:e,onTrackKeyphrase:N,onUntrackKeyphrase:A,rowData:L(e),isFocusKeyphrase:e===f.trim().toLowerCase(),websiteId:y,isDisabled:!s,isLoading:F||C.indexOf(e.toLowerCase())>=0,isSelected:E.includes(e),onSelectKeyphrases:R})))))),(0,v.createElement)("p",{style:{marginBottom:0,position:"relative"}},(0,v.createElement)(Ut,{href:wpseoAdminGlobalL10n["links.wincher.login"]},(0,d.sprintf)(/* translators: %s expands to Wincher */
(0,d.__)("Get more insights over at %s","wordpress-seo"),"Wincher")),(0,v.createElement)(Vt,null,(0,d.__)("* focus keyphrase","wordpress-seo"))))};es.propTypes={addTrackedKeyphrase:x().func.isRequired,isLoggedIn:x().bool,isNewlyAuthenticated:x().bool,keyphrases:x().array,newRequest:x().func.isRequired,removeTrackedKeyphrase:x().func.isRequired,setRequestFailed:x().func.isRequired,setKeyphraseLimitReached:x().func.isRequired,setRequestSucceeded:x().func.isRequired,setTrackedKeyphrases:x().func.isRequired,setHasTrackedAll:x().func.isRequired,trackAll:x().bool,trackedKeyphrases:x().object,websiteId:x().string,permalink:x().string.isRequired,focusKeyphrase:x().string,startAt:x().string,selectedKeyphrases:x().arrayOf(x().string).isRequired,onSelectKeyphrases:x().func.isRequired},es.defaultProps={isLoggedIn:!1,isNewlyAuthenticated:!1,keyphrases:[],trackAll:!1,websiteId:"",focusKeyphrase:""};const ts=es,ss=(0,me.compose)([(0,he.withSelect)((e=>{const{getWincherWebsiteId:t,getWincherTrackableKeyphrases:s,getWincherLoginStatus:n,getWincherPermalink:r,getFocusKeyphrase:a,isWincherNewlyAuthenticated:o,shouldWincherTrackAll:i}=e("yoast-seo/editor");return{focusKeyphrase:a(),keyphrases:s(),isLoggedIn:n(),trackAll:i(),websiteId:t(),isNewlyAuthenticated:o(),permalink:r()}})),(0,he.withDispatch)((e=>{const{setWincherNewRequest:t,setWincherRequestSucceeded:s,setWincherRequestFailed:n,setWincherSetKeyphraseLimitReached:r,setWincherTrackedKeyphrases:a,setWincherTrackingForKeyphrase:o,setWincherTrackAllKeyphrases:i,unsetWincherTrackingForKeyphrase:l}=e("yoast-seo/editor");return{newRequest:()=>{t()},setRequestSucceeded:e=>{s(e)},setRequestFailed:e=>{n(e)},setKeyphraseLimitReached:e=>{r(e)},addTrackedKeyphrase:e=>{o(e)},removeTrackedKeyphrase:e=>{l(e)},setTrackedKeyphrases:e=>{a(e)},setHasTrackedAll:()=>{i(!1)}}}))])(ts),ns=(0,S.makeOutboundLink)(),rs=(0,S.makeOutboundLink)(),as=()=>{const e=(0,d.sprintf)((0,d.__)(/* translators: %1$s expands to a link to Wincher, %2$s expands to a link to the keyphrase tracking article on Yoast.com */
"With %1$s you can track the ranking position of your page in the search results based on your keyphrase(s). %2$s","wordpress-seo"),"{{wincherLink/}}","{{wincherReadMoreLink/}}");return(0,v.createElement)("p",null,(0,mt.Z)({mixedString:e,components:{wincherLink:(0,v.createElement)(ns,{href:wpseoAdminGlobalL10n["links.wincher.website"]},"Wincher"),wincherReadMoreLink:(0,v.createElement)(rs,{href:wpseoAdminL10n["shortlinks.wincher.seo_performance"]},(0,d.__)("Read more about keyphrase tracking with Wincher","wordpress-seo"))}}))},os=()=>(0,v.createElement)(T.Alert,{type:"error"},(0,d.__)("No keyphrase has been set. Please set a keyphrase first.","wordpress-seo")),is=()=>(0,v.createElement)(T.Alert,{type:"info"},(0,d.sprintf)(/* translators: %s: Expands to "Wincher". */
(0,d.__)("Automatic tracking of keyphrases is enabled. Your keyphrase(s) will automatically be tracked by %s when you publish your post.","wordpress-seo"),"Wincher"));class ls{constructor(e,t={},s={}){this.url=e,this.origin=new URL(e).origin,this.eventHandlers=Object.assign({success:{type:"",callback:()=>{}},error:{type:"",callback:()=>{}}},t),this.options=Object.assign({height:570,width:340,title:""},s),this.popup=null,this.createPopup=this.createPopup.bind(this),this.messageHandler=this.messageHandler.bind(this),this.getPopup=this.getPopup.bind(this)}createPopup(){const{height:e,width:t,title:s}=this.options,n=["top="+(window.top.outerHeight/2+window.top.screenY-e/2),"left="+(window.top.outerWidth/2+window.top.screenX-t/2),"width="+t,"height="+e,"resizable=1","scrollbars=1","status=0"];this.popup&&!this.popup.closed||(this.popup=window.open(this.url,s,n.join(","))),this.popup&&this.popup.focus(),window.addEventListener("message",this.messageHandler,!1)}async messageHandler(e){const{data:t,source:s,origin:n}=e;n===this.origin&&this.popup===s&&(t.type===this.eventHandlers.success.type&&(this.popup.close(),window.removeEventListener("message",this.messageHandler,!1),await this.eventHandlers.success.callback(t)),t.type===this.eventHandlers.error.type&&(this.popup.close(),window.removeEventListener("message",this.messageHandler,!1),await this.eventHandlers.error.callback(t)))}getPopup(){return this.popup}isClosed(){return!this.popup||this.popup.closed}focus(){this.isClosed()||this.popup.focus()}}const cs=e=>{const t=(0,d.sprintf)(/* translators: %s expands to a link to open the Wincher login popup. */
(0,d.__)("It seems like something went wrong when retrieving your website's data. Please %s and try again.","wordpress-seo"),"{{reconnectToWincher/}}","Wincher");return(0,v.createElement)(T.Alert,{type:"error",className:e.className},(0,mt.Z)({mixedString:t,components:{reconnectToWincher:(0,v.createElement)("a",{href:"#",onClick:t=>{t.preventDefault(),e.onReconnect()}},(0,d.sprintf)(/* translators: %s : Expands to "Wincher". */
(0,d.__)("reconnect to %s","wordpress-seo"),"Wincher"))}}))};cs.propTypes={onReconnect:x().func.isRequired,className:x().string},cs.defaultProps={className:""};const us=cs,ds=()=>(0,v.createElement)(T.Alert,{type:"error"},(0,d.__)("Before you can track your SEO performance make sure to set either the post’s title and save it as a draft or manually set the post’s slug.","wordpress-seo")),ps=window.yoast["chart.js"],ms="label";function hs(e,t){"function"==typeof e?e(t):e&&(e.current=t)}function gs(e,t){e.labels=t}function ys(e,t){let s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:ms;const n=[];e.datasets=t.map((t=>{const r=e.datasets.find((e=>e[s]===t[s]));return r&&t.data&&!n.includes(r)?(n.push(r),Object.assign(r,t),r):{...t}}))}function fs(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:ms;const s={labels:[],datasets:[]};return gs(s,e.labels),ys(s,e.datasets,t),s}function bs(e,t){const{height:s=150,width:n=300,redraw:r=!1,datasetIdKey:a,type:o,data:i,options:l,plugins:c=[],fallbackContent:u,updateMode:d,...p}=e,m=(0,v.useRef)(null),h=(0,v.useRef)(),g=()=>{m.current&&(h.current=new ps.Chart(m.current,{type:o,data:fs(i,a),options:l&&{...l},plugins:c}),hs(t,h.current))},y=()=>{hs(t,null),h.current&&(h.current.destroy(),h.current=null)};return(0,v.useEffect)((()=>{!r&&h.current&&l&&function(e,t){const s=e.options;s&&t&&Object.assign(s,t)}(h.current,l)}),[r,l]),(0,v.useEffect)((()=>{!r&&h.current&&gs(h.current.config.data,i.labels)}),[r,i.labels]),(0,v.useEffect)((()=>{!r&&h.current&&i.datasets&&ys(h.current.config.data,i.datasets,a)}),[r,i.datasets]),(0,v.useEffect)((()=>{h.current&&(r?(y(),setTimeout(g)):h.current.update(d))}),[r,l,i.labels,i.datasets,d]),(0,v.useEffect)((()=>{h.current&&(y(),setTimeout(g))}),[o]),(0,v.useEffect)((()=>(g(),()=>y())),[]),v.createElement("canvas",Object.assign({ref:m,role:"img",height:s,width:n},p),u)}const ws=(0,v.forwardRef)(bs);function vs(e,t){return ps.Chart.register(t),(0,v.forwardRef)(((t,s)=>v.createElement(ws,Object.assign({},t,{ref:s,type:e}))))}const ks=vs("line",ps.LineController),Es={datetime:"MMM D, YYYY, h:mm:ss a",millisecond:"h:mm:ss.SSS a",second:"h:mm:ss a",minute:"h:mm a",hour:"hA",day:"MMM D",week:"ll",month:"MMM YYYY",quarter:"[Q]Q - YYYY",year:"YYYY"};ps._adapters._date.override("function"==typeof pt()?{_id:"moment",formats:function(){return Es},parse:function(e,t){return"string"==typeof e&&"string"==typeof t?e=pt()(e,t):e instanceof pt()||(e=pt()(e)),e.isValid()?e.valueOf():null},format:function(e,t){return pt()(e).format(t)},add:function(e,t,s){return pt()(e).add(t,s).valueOf()},diff:function(e,t,s){return pt()(e).diff(pt()(t),s)},startOf:function(e,t,s){return e=pt()(e),"isoWeek"===t?(s=Math.trunc(Math.min(Math.max(0,s),6)),e.isoWeekday(s).startOf("day").valueOf()):e.startOf(t).valueOf()},endOf:function(e,t){return pt()(e).endOf(t).valueOf()}}:{}),Math.PI,Number.POSITIVE_INFINITY,Math.log10,Math.sign,"undefined"==typeof window||window.requestAnimationFrame,new Map,Object.create(null),Object.create(null),Number.EPSILON;const Rs=["top","right","bottom","left"];function _s(e,t,s){const n={};s=s?"-"+s:"";for(let r=0;r<4;r++){const a=Rs[r];n[a]=parseFloat(e[t+"-"+a+s])||0}return n.width=n.left+n.right,n.height=n.top+n.bottom,n}!function(){let e=!1;try{const t={get passive(){return e=!0,!1}};window.addEventListener("test",null,t),window.removeEventListener("test",null,t)}catch(e){}}(),ps.Chart.register(ps.CategoryScale,ps.LineController,ps.LineElement,ps.PointElement,ps.LinearScale,ps.TimeScale,ps.Legend,ps.Tooltip);const xs=["#ff983b","#ffa3f7","#3798ff","#ff3b3b","#acce81","#b51751","#3949ab","#26c6da","#ccb800","#de66ff","#4db6ac","#ffab91","#45f5f1","#77f210","#90a4ae","#ffd54f","#006b5e","#8ec7d2","#b1887c","#cc9300"];function Ts({datasets:e,isChartShown:t,keyphrases:s}){if(!t)return null;const n=(0,k.useMemo)((()=>Object.fromEntries([...s].sort().map(((e,t)=>[e,xs[t%xs.length]])))),[s]),r=e.map((e=>{const t=n[e.label];return{...e,data:e.data.map((({datetime:e,value:t})=>({x:e,y:t}))),lineTension:0,pointRadius:1,pointHoverRadius:4,borderWidth:2,pointHitRadius:6,backgroundColor:t,borderColor:t}})).filter((e=>!1!==e.selected));return(0,v.createElement)(ks,{height:100,data:{datasets:r},options:{plugins:{legend:{display:!0,position:"bottom",labels:{color:"black",usePointStyle:!0,boxHeight:7,boxWidth:7},onClick:c.noop},tooltip:{enabled:!0,callbacks:{title:e=>pt()(e[0].raw.x).utc().format("YYYY-MM-DD")},titleAlign:"center",mode:"xPoint",position:"nearest",usePointStyle:!0,boxHeight:7,boxWidth:7,boxPadding:2}},scales:{x:{bounds:"ticks",type:"time",time:{unit:"day",minUnit:"day"},grid:{display:!1},ticks:{autoSkipPadding:50,maxRotation:0,color:"black"}},y:{bounds:"ticks",offset:!0,reverse:!0,ticks:{precision:0,color:"black"},max:101}}}})}ps.Interaction.modes.xPoint=(e,t,s,n)=>{const r=function(e,t){if("native"in e)return e;const{canvas:s,currentDevicePixelRatio:n}=t,r=(m=s).ownerDocument.defaultView.getComputedStyle(m,null),a="border-box"===r.boxSizing,o=_s(r,"padding"),i=_s(r,"border","width"),{x:l,y:c,box:u}=function(e,t){const s=e.touches,n=s&&s.length?s[0]:e,{offsetX:r,offsetY:a}=n;let o,i,l=!1;if(((e,t,s)=>(e>0||t>0)&&(!s||!s.shadowRoot))(r,a,e.target))o=r,i=a;else{const e=t.getBoundingClientRect();o=n.clientX-e.left,i=n.clientY-e.top,l=!0}return{x:o,y:i,box:l}}(e,s),d=o.left+(u&&i.left),p=o.top+(u&&i.top);var m;let{width:h,height:g}=t;return a&&(h-=o.width+i.width,g-=o.height+i.height),{x:Math.round((l-d)/h*s.width/n),y:Math.round((c-p)/g*s.height/n)}}(t,e);let a=[];if(ps.Interaction.evaluateInteractionItems(e,"x",r,((e,t,s)=>{e.inXRange(r.x,n)&&a.push({element:e,datasetIndex:t,index:s})})),0===a.length)return a;const o=a.reduce(((e,t)=>Math.abs(r.x-e.element.x)<Math.abs(r.x-t.element.x)?e:t)).element.x;return a=a.filter((e=>e.element.x===o)),a.some((e=>Math.abs(e.element.y-r.y)<10))?a:[]},Ts.propTypes={datasets:x().arrayOf(x().shape({label:x().string.isRequired,data:x().arrayOf(x().shape({datetime:x().string.isRequired,value:x().number.isRequired})).isRequired,selected:x().bool})).isRequired,isChartShown:x().bool.isRequired,keyphrases:x().array.isRequired};const Ss=({response:e,onLogin:t})=>[401,403,404].includes(e.status)?(0,v.createElement)(us,{onReconnect:t}):(0,v.createElement)(ft,null);Ss.propTypes={response:x().object.isRequired,onLogin:x().func.isRequired};const Cs=({isSuccess:e,response:t,allKeyphrasesMissRanking:s,onLogin:n,keyphraseLimitReached:r,limit:a})=>r?(0,v.createElement)(yt,{limit:a}):(0,c.isEmpty)(t)||e?s?(0,v.createElement)(At,null):null:(0,v.createElement)(Ss,{response:t,onLogin:n});Cs.propTypes={isSuccess:x().bool.isRequired,allKeyphrasesMissRanking:x().bool.isRequired,response:x().object,onLogin:x().func.isRequired,keyphraseLimitReached:x().bool.isRequired,limit:x().number.isRequired},Cs.defaultProps={response:{}};let Is=null;const Ls=async e=>{if(Is&&!Is.isClosed())return void Is.focus();const{url:t}=await async function(){return await wt({path:"yoast/v1/wincher/authorization-url",method:"GET"})}();Is=new ls(t,{success:{type:"wincher:oauth:success",callback:t=>(async(e,t)=>{const{onAuthentication:s,setRequestSucceeded:n,setRequestFailed:r,keyphrases:a,addTrackedKeyphrase:o,setKeyphraseLimitReached:i}=e;await bt((()=>async function(e){const{code:t,websiteId:s}=e;return await wt({path:"yoast/v1/wincher/authenticate",method:"POST",data:{code:t,websiteId:s}})}(t)),(async e=>{s(!0,!0,t.websiteId.toString()),n(e);const l=(Array.isArray(a)?a:[a]).map((e=>e.toLowerCase()));await bt((()=>vt(l)),(e=>{n(e),o(e.results)}),(e=>{400===e.status&&e.limit&&i(e.limit),r(e)}),201);const c=Is.getPopup();c&&c.close()}),(async e=>r(e)))})(e,t)},error:{type:"wincher:oauth:error",callback:()=>e.onAuthentication(!1,!1)}},{title:"Wincher_login",width:500,height:700}),Is.createPopup()},qs=e=>e.isLoggedIn?null:(0,v.createElement)("p",null,(0,v.createElement)(T.NewButton,{onClick:e.onLogin,variant:"primary"},(0,d.sprintf)(/* translators: %s expands to Wincher */
(0,d.__)("Connect with %s","wordpress-seo"),"Wincher")));qs.propTypes={isLoggedIn:x().bool.isRequired,onLogin:x().func.isRequired};const Ps=R().div`
	p {
		margin: 1em 0;
	}
`,As=R().div`
	${e=>e.isDisabled&&"\n\t\topacity: .5;\n\t\tpointer-events: none;\n\t"};
`,Ns=R().div`
	font-weight: var(--yoast-font-weight-bold);
	color: var(--yoast-color-label);
	font-size: var(--yoast-font-size-default);
`,Ms=R().div.attrs({className:"yoast-field-group"})`
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 14px;
`,Os=R().div`
	margin: 8px 0;
`,Bs=pt().utc().startOf("day"),Ks=[{name:(0,d.__)("Last day","wordpress-seo"),value:pt()(Bs).subtract(1,"days").format(),defaultIndex:1},{name:(0,d.__)("Last week","wordpress-seo"),value:pt()(Bs).subtract(1,"week").format(),defaultIndex:2},{name:(0,d.__)("Last month","wordpress-seo"),value:pt()(Bs).subtract(1,"month").format(),defaultIndex:3},{name:(0,d.__)("Last year","wordpress-seo"),value:pt()(Bs).subtract(1,"year").format(),defaultIndex:0}],Fs=e=>{const{onSelect:t,selected:s,options:n,isLoggedIn:r}=e;return r?n.length<1?null:(0,v.createElement)("select",{className:"components-select-control__input",id:"wincher-period-picker",value:(null==s?void 0:s.value)||n[0].value,onChange:t},n.map((e=>(0,v.createElement)("option",{key:e.name,value:e.value},e.name)))):null};Fs.propTypes={onSelect:x().func.isRequired,selected:x().object,options:x().array.isRequired,isLoggedIn:x().bool.isRequired};const Ds=e=>{const{trackedKeyphrases:t,isLoggedIn:s,keyphrases:n,shouldTrackAll:r,permalink:a,historyDaysLimit:o}=e;if(!a&&s)return(0,v.createElement)(ds,null);if(0===n.length)return(0,v.createElement)(os,null);const i=pt()(Bs).subtract(o,"days"),l=Ks.filter((e=>pt()(e.value).isSameOrAfter(i))),u=(0,c.orderBy)(l,(e=>e.defaultIndex),"desc")[0],[p,m]=(0,k.useState)(u),[h,g]=(0,k.useState)([]),y=h.length>0,f=(0,me.usePrevious)(t);(0,k.useEffect)((()=>{if(!(0,c.isEmpty)(t)&&(0,c.difference)(Object.keys(t),Object.keys(f||[])).length){const e=Object.values(t).map((e=>e.keyword));g(e)}}),[t,f]),(0,k.useEffect)((()=>{m(u)}),[null==u?void 0:u.name]);const b=(0,k.useCallback)((e=>{const t=Ks.find((t=>t.value===e.target.value));t&&m(t)}),[m]),w=(0,k.useMemo)((()=>(0,c.isEmpty)(h)||(0,c.isEmpty)(t)?[]:Object.values(t).filter((e=>{var t;return!(null==e||null===(t=e.position)||void 0===t||!t.history)})).map((e=>{var t;return{label:e.keyword,data:e.position.history,selected:h.includes(e.keyword)&&!(0,c.isEmpty)(null===(t=e.position)||void 0===t?void 0:t.history)}}))),[h,t]);return(0,v.createElement)(As,{isDisabled:!s},(0,v.createElement)("p",null,(0,d.__)("You can enable / disable tracking the SEO performance for each keyphrase below.","wordpress-seo")),s&&r&&(0,v.createElement)(is,null),(0,v.createElement)(Ms,null,(0,v.createElement)(Fs,{selected:p,onSelect:b,options:l,isLoggedIn:s})),(0,v.createElement)(Os,null,(0,v.createElement)(Ts,{isChartShown:y,datasets:w,keyphrases:n})),(0,v.createElement)(ss,{startAt:null==p?void 0:p.value,selectedKeyphrases:h,onSelectKeyphrases:g,trackedKeyphrases:t}))};function $s(e){const{isNewlyAuthenticated:t,isLoggedIn:s}=e,n=(0,k.useCallback)((()=>{Ls(e)}),[Ls,e]),r=Tt(s);return(0,v.createElement)(Ps,null,t&&(0,v.createElement)(Pt,null),s&&(0,v.createElement)(qt,{trackingInfo:r}),(0,v.createElement)(Ns,null,(0,d.__)("SEO performance","wordpress-seo"),(0,v.createElement)(T.HelpIcon,{linkTo:wpseoAdminL10n["shortlinks.wincher.seo_performance"]
/* translators: Hidden accessibility text. */,linkText:(0,d.__)("Learn more about the SEO performance feature.","wordpress-seo")})),(0,v.createElement)(as,null),(0,v.createElement)(qs,{isLoggedIn:s,onLogin:n}),(0,v.createElement)(Cs,{...e,onLogin:n}),(0,v.createElement)(Ds,{...e,historyDaysLimit:(null==r?void 0:r.historyDays)||31}))}Ds.propTypes={trackedKeyphrases:x().object,keyphrases:x().array.isRequired,isLoggedIn:x().bool.isRequired,shouldTrackAll:x().bool.isRequired,permalink:x().string.isRequired,historyDaysLimit:x().number},$s.propTypes={trackedKeyphrases:x().object,addTrackedKeyphrase:x().func.isRequired,isLoggedIn:x().bool,isNewlyAuthenticated:x().bool,keyphrases:x().array,response:x().object,shouldTrackAll:x().bool,permalink:x().string,historyDaysLimit:x().number},$s.defaultProps={trackedKeyphrases:null,isLoggedIn:!1,isNewlyAuthenticated:!1,keyphrases:[],response:{},shouldTrackAll:!1,permalink:"",historyDaysLimit:0};const Hs=(0,me.compose)([(0,he.withSelect)((e=>{const{isWincherNewlyAuthenticated:t,getWincherKeyphraseLimitReached:s,getWincherLimit:n,getWincherHistoryDaysLimit:r,getWincherLoginStatus:a,getWincherRequestIsSuccess:o,getWincherRequestResponse:i,getWincherTrackableKeyphrases:l,getWincherTrackedKeyphrases:c,getWincherAllKeyphrasesMissRanking:u,getWincherPermalink:d,shouldWincherAutomaticallyTrackAll:p}=e("yoast-seo/editor");return{keyphrases:l(),trackedKeyphrases:c(),allKeyphrasesMissRanking:u(),isLoggedIn:a(),isNewlyAuthenticated:t(),isSuccess:o(),keyphraseLimitReached:s(),limit:n(),response:i(),shouldTrackAll:p(),permalink:d(),historyDaysLimit:r()}})),(0,he.withDispatch)((e=>{const{setWincherWebsiteId:t,setWincherRequestSucceeded:s,setWincherRequestFailed:n,setWincherTrackingForKeyphrase:r,setWincherSetKeyphraseLimitReached:a,setWincherLoginStatus:o}=e("yoast-seo/editor");return{setRequestSucceeded:e=>{s(e)},setRequestFailed:e=>{n(e)},addTrackedKeyphrase:e=>{r(e)},setKeyphraseLimitReached:e=>{a(e)},onAuthentication:(e,s,n)=>{t(n),o(e,s)}}}))])($s);function Ws(e,t,s,n){return new Promise(((r,a)=>{jQuery.ajax({type:e,url:t,beforeSend:s?e=>{e.setRequestHeader("X-WP-Nonce",s)}:null,data:n,dataType:"json",success:r,error:a})}))}let js,zs,Ys,Us;const Vs=/<(\/)?(\w+)\s*(\/)?>/g;function Gs(e,t,s,n,r){return{element:e,tokenStart:t,tokenLength:s,prevOffset:n,leadingTextStart:r,children:[]}}function Zs(){const e=js.length-zs;0!==e&&Ys.push(js.substring(zs,zs+e))}function Js(e){const{element:t,tokenStart:s,tokenLength:n,prevOffset:r,children:a}=e,o=Us[Us.length-1],i=js.substring(o.prevOffset,s);i&&o.children.push(i),o.children.push((0,k.cloneElement)(t,null,...a)),o.prevOffset=r||s+n}function Qs(e){const t=function(){const e=Vs.exec(js);if(null===e)return["no-more-tokens"];const t=e.index,[s,n,r,a]=e,o=s.length;return a?["self-closed",r,t,o]:n?["closer",r,t,o]:["opener",r,t,o]}(),[s,n,r,a]=t,o=Us.length,i=r>zs?zs:null;if(!e[n])return Zs(),!1;switch(s){case"no-more-tokens":if(0!==o){const{leadingTextStart:e,tokenStart:t}=Us.pop();Ys.push(js.substring(e,e+t))}return Zs(),!1;case"self-closed":return 0===o?(null!==i&&Ys.push(js.substring(i,r)),Ys.push(e[n]),zs=r+a,!0):(Js(Gs(e[n],r,a)),zs=r+a,!0);case"opener":return Us.push(Gs(e[n],r,a,r+a,i)),zs=r+a,!0;case"closer":if(1===o)return function(e){const{element:t,leadingTextStart:s,prevOffset:n,tokenStart:r,children:a}=Us.pop(),o=e?js.substring(n,e):js.substring(n);o&&a.push(o),null!==s&&Ys.push(js.substring(s,r)),Ys.push((0,k.cloneElement)(t,null,...a))}(r),zs=r+a,!0;const t=Us.pop(),s=js.substring(t.prevOffset,r);t.children.push(s),t.prevOffset=r+a;const l=Gs(t.element,t.tokenStart,t.tokenLength,r+a);return l.children=t.children,Js(l),zs=r+a,!0;default:return Zs(),!1}}function Xs(e,t="wpseoYoastJSL10n"){const s=(0,c.get)(window,[t,e,"locale_data",e],!1);"yoast-components"===e&&(e="wordpress-seo"),!1===s?(0,d.setLocaleData)({"":{}},e):(0,d.setLocaleData)(s,e)}const en=window.wp.sanitize,tn="SNIPPET_EDITOR_UPDATE_REPLACEMENT_VARIABLE",sn="SNIPPET_EDITOR_UPDATE_REPLACEMENT_VARIABLES_BATCH";function nn(e,t,s="",n=!1){const r="string"==typeof t?(0,S.decodeHTML)(t):t;return{type:tn,name:e,value:r,label:s,hidden:n}}function rn(e){return e.charAt(0).toUpperCase()+e.slice(1)}const{stripHTMLTags:an}=S.strings,on=["slug","content","contentImage","snippetPreviewImageURL"];function ln(e,t){(0,c.forEach)(e,((e,s)=>{on.includes(s)||t.dispatch(nn(s,e))}))}function cn(e){if(!["ct_","cf_","pt_"].includes(e.substring(0,3)))return e.replace(/_/g," ");const t=e.slice(0,3);switch(-1!==(e=e.slice(3)).indexOf("desc_")&&(e=e.slice(5)+" description"),t){case"ct_":e+=" (custom taxonomy)";break;case"cf_":e+=" (custom field)";break;case"pt_":e="Post type ("+(e=e.replace("single","singular"))+")"}return e}function un(e){return rn(e=cn(e))}function dn(e,t){return e.push({name:t.name,label:t.label||un(t.name),value:t.value}),e}function pn(e,t="_"){return e.replace(/\s/g,t)}function mn(e){return{name:"cf_"+pn(e),label:rn(e+" (custom field)")}}function hn(e){const t=pn(e);return{name:"ct_"+t,label:rn(e+" (custom taxonomy)"),descriptionName:"ct_desc_"+t,descriptionLabel:rn(e+" description (custom taxonomy)")}}function gn(e,t){if(!e.custom_taxonomies)return e;const s={};return(0,c.forEach)(e.custom_taxonomies,((e,t)=>{const{name:n,label:r,descriptionName:a,descriptionLabel:o}=hn(t),i="string"==typeof e.name?(0,S.decodeHTML)(e.name):e.name,l="string"==typeof e.description?(0,S.decodeHTML)(e.description):e.description;s[n]={value:i,label:r},s[a]={value:l,label:o}})),t.dispatch(function(e){return{type:sn,updatedVariables:e}}(s)),(0,c.omit)({...e},"custom_taxonomies")}function yn(e,t){return e.custom_fields?((0,c.forEach)(e.custom_fields,((e,s)=>{const{name:n,label:r}=mn(s);t.dispatch(nn(n,e,r))})),(0,c.omit)({...e},"custom_fields")):e}function fn(e,t=156){return(e=(e=(0,en.stripTags)(e)).trim()).length<=t||(e=e.substring(0,t),/\s/.test(e)&&(e=e.substring(0,e.lastIndexOf(" ")))),e}const bn=function(e){const t=(0,c.get)(window,["YoastSEO","app","pluggable"],!1);if(!t||!(0,c.get)(window,["YoastSEO","app","pluggable","loaded"],!1))return function(e){const t=(0,c.get)(window,["YoastSEO","wp","replaceVarsPlugin","replaceVariables"],c.identity);return{url:e.url,title:an(t(e.title)),description:an(t(e.description)),filteredSEOTitle:e.filteredSEOTitle?an(t(e.filteredSEOTitle)):""}}(e);const s=t._applyModifications.bind(t);return{url:e.url,title:an(s("data_page_title",e.title)),description:an(s("data_meta_desc",e.description)),filteredSEOTitle:e.filteredSEOTitle?an(s("data_page_title",e.filteredSEOTitle)):""}};var wn="score-text",vn="image yoast-logo svg",kn=jQuery;function En(e,t,s=null){return null!==s?(0,c.get)(s,t,""):(0,c.get)(wpseoScriptData,`metabox.publish_box.labels.${e}.${t}`,"")}window.yoast=window.yoast||{},window.yoast.editorModules={analysis:{getL10nObject:u,getContentLocale:function(){const e=u();return(0,c.get)(e,"contentLocale","en_US")},getIndicatorForScore:function(e){return(0,c.isNil)(e)||(e/=10),function(e){switch(e){case"feedback":return{className:"na",screenReaderText:(0,d.__)("Feedback","wordpress-seo"),screenReaderReadabilityText:"",screenReaderInclusiveLanguageText:""};case"bad":return{className:"bad",screenReaderText:(0,d.__)("Needs improvement","wordpress-seo"),screenReaderReadabilityText:(0,d.__)("Needs improvement","wordpress-seo"),screenReaderInclusiveLanguageText:(0,d.__)("Needs improvement","wordpress-seo")};case"ok":return{className:"ok",screenReaderText:(0,d.__)("OK SEO score","wordpress-seo"),screenReaderReadabilityText:(0,d.__)("OK","wordpress-seo"),screenReaderInclusiveLanguageText:(0,d.__)("Potentially non-inclusive","wordpress-seo")};case"good":return{className:"good",screenReaderText:(0,d.__)("Good SEO score","wordpress-seo"),screenReaderReadabilityText:(0,d.__)("Good","wordpress-seo"),screenReaderInclusiveLanguageText:(0,d.__)("Good","wordpress-seo")};default:return{className:"loading",screenReaderText:"",screenReaderReadabilityText:"",screenReaderInclusiveLanguageText:""}}}(p.interpreters.scoreToRating(e))},isZapierConnected:function(){const e=u();return 1===(0,c.get)(e,"zapierConnectedStatus",0)},isZapierIntegrationActive:function(){const e=u();return 1===(0,c.get)(e,"zapierIntegrationActive",0)},constants:e,refreshAnalysis:t},components:{HelpLink:V,TopLevelProviders:pe,higherorder:{withYoastSidebarPriority:e=>{const t=t=>{const{renderPriority:s,...n}=t;return(0,v.createElement)(e,{...n})};return t.propTypes={renderPriority:x().number},t}},contentAnalysis:{KeywordInput:W,mapResults:n},contexts:{location:{LocationContext:i.LocationContext,LocationProvider:i.LocationProvider,LocationConsumer:i.LocationConsumer}},SidebarItem:ue,SidebarCollapsible:le,MetaboxCollapsible:e=>(0,v.createElement)(G,{hasPadding:!0,hasSeparator:!0,...e}),Modal:X,portals:{Portal:ne,ImageSelectPortal:re,ScoreIconPortal:oe}},containers:{EditorModal:we,PersistentDismissableAlert:ve,Results:He,SEMrushRelatedKeyphrases:ut,WincherSEOPerformance:Hs},helpers:{ajaxHelper:r,createInterpolateElement:(e,t)=>{if(js=e,zs=0,Ys=[],Us=[],Vs.lastIndex=0,!(e=>{const t="object"==typeof e,s=t&&Object.values(e);return t&&s.length&&s.every((e=>(0,k.isValidElement)(e)))})(t))throw new TypeError("The conversionMap provided is not valid. It must be an object with values that are WPElements");do{}while(Qs(t));return(0,k.createElement)(k.Fragment,null,...Ys)},createWatcher:(e,t)=>{let s=e();return()=>{const n=e();(0,c.isEqual)(n,s)||(s=n,t((0,c.clone)(n)))}},isBlockEditor:function(){return window.wpseoScriptData&&"1"===window.wpseoScriptData.isBlockEditor},i18n:a,replacementVariableHelpers:o,publishBox:{updateScore:function(e,t,s=null){var n=kn("#"+e+"-score"),r=vn+" "+t;n.children(".image").attr("class",r);var a=En(e,t,s);n.children("."+wn).html(a)},createScoresInPublishBox:function(e,t,s=null){const n=kn("<div />",{class:"misc-pub-section yoast yoast-seo-score "+e+"-score",id:e+"-score"}),r=kn("<span />",{class:wn,html:En(e,t,s)}),a=kn("<span>").attr("class",vn+" na");n.append(a).append(r),kn("#yoast-seo-publishbox-section").append(n)},scrollToCollapsible:function(e){const t=kn("#wpadminbar"),s=kn(e);if(!t||!s)return;const n="fixed"===t.css("position")?t.height():0;kn([document.documentElement,document.body]).animate({scrollTop:s.offset().top-n},1e3),s.trigger("focus"),0===s.parent().siblings().length&&s.trigger("click")}},updateAdminBar:function(e){jQuery("#wp-admin-bar-wpseo-menu .wpseo-score-icon").attr("title",e.screenReaderText).attr("class","wpseo-score-icon "+e.className).find(".wpseo-score-text").text(e.screenReaderText)},updateTrafficLight:function(e){var t=jQuery(".yst-traffic-light"),s=t.closest(".wpseo-meta-section-link"),n=jQuery("#wpseo-traffic-light-desc"),r=e.className||"na";t.attr("class","yst-traffic-light "+r),s.attr("aria-describedby","wpseo-traffic-light-desc"),n.length>0?n.text(e.screenReaderText):s.closest("li").append("<span id='wpseo-traffic-light-desc' class='screen-reader-text'>"+e.screenReaderText+"</span>")}}}})()})();