(()=>{"use strict";var e={n:t=>{var n=t&&t.__esModule?()=>t.default:()=>t;return e.d(n,{a:n}),n},d:(t,n)=>{for(var s in n)e.o(n,s)&&!e.o(t,s)&&Object.defineProperty(t,s,{enumerable:!0,get:n[s]})},o:(e,t)=>Object.prototype.hasOwnProperty.call(e,t)};const t=window.React,n=window.wp.element,s=window.jQuery;var r=e.n(s);const i=window.wp.i18n,o=window.yoast.componentsNew,a=window.yoast.styleGuide,d=window.yoast.propTypes;var l=e.n(d);const c=window.yoast.helpers,p=window.yoast.styledComponents;var u=e.n(p);class g extends Error{constructor(e,t,n,s,r){super(e),this.name="RequestError",this.url=t,this.method=n,this.statusCode=s,this.stackTrace=r}}const{stripTagsFromHtmlString:h}=c.strings,m=["a","p"],w=u().div`
	margin-top: 8px;
`,x=u().pre`
	overflow-x: scroll;
	max-width: 500px;
	border: 1px solid;
	padding: 16px;
`;function y({title:e,value:n}){return n?(0,t.createElement)("p",null,(0,t.createElement)("strong",null,e),(0,t.createElement)("br",null),n):null}function E({title:e,value:n}){return n?(0,t.createElement)("details",null,(0,t.createElement)("summary",null,e),(0,t.createElement)(x,null,n)):null}function S({message:e,error:n}){return(0,t.createElement)(o.Alert,{type:"error"},(0,t.createElement)("div",{dangerouslySetInnerHTML:{__html:h(e,m)}}),(0,t.createElement)("details",null,(0,t.createElement)("summary",null,(0,i.__)("Error details","wordpress-seo")),(0,t.createElement)(w,null,(0,t.createElement)(y,{title:(0,i.__)("Request URL","wordpress-seo"),value:n.url}),(0,t.createElement)(y,{title:(0,i.__)("Request method","wordpress-seo"),value:n.method}),(0,t.createElement)(y,{title:(0,i.__)("Status code","wordpress-seo"),value:n.statusCode}),(0,t.createElement)(y,{title:(0,i.__)("Error message","wordpress-seo"),value:n.message}),(0,t.createElement)(E,{title:(0,i.__)("Response","wordpress-seo"),value:n.parseString}),(0,t.createElement)(E,{title:(0,i.__)("Error stack trace","wordpress-seo"),value:n.stackTrace}))))}y.propTypes={title:l().string.isRequired,value:l().any},y.defaultProps={value:""},E.propTypes={title:l().string.isRequired,value:l().string},E.defaultProps={value:""},S.propTypes={message:l().string.isRequired,error:l().oneOfType([l().instanceOf(Error),l().instanceOf(g)]).isRequired};class f extends Error{constructor(e,t){super(e),this.name="ParseError",this.parseString=t}}const _="idle",I="in_progress",A="errored",v="completed";class T extends n.Component{constructor(e){super(e),this.settings=yoastIndexingData,this.state={state:_,processed:0,error:null,amount:parseInt(this.settings.amount,10),firstTime:"1"===this.settings.firstTime},this.startIndexing=this.startIndexing.bind(this),this.stopIndexing=this.stopIndexing.bind(this)}async doIndexingRequest(e,t){const n=await fetch(e,{method:"POST",headers:{"X-WP-Nonce":t}}),s=await n.text();let r;try{r=JSON.parse(s)}catch(e){throw new f("Error parsing the response to JSON.",s)}if(!n.ok){const t=r.data?r.data.stackTrace:"";throw new g(r.message,e,"POST",n.status,t)}return r}async doPreIndexingAction(e){"function"==typeof this.props.preIndexingActions[e]&&await this.props.preIndexingActions[e](this.settings)}async doPostIndexingAction(e,t){"function"==typeof this.props.indexingActions[e]&&await this.props.indexingActions[e](t.objects,this.settings)}async doIndexing(e){let t=this.settings.restApi.root+this.settings.restApi.indexing_endpoints[e];for(;this.isState(I)&&!1!==t;)try{await this.doPreIndexingAction(e);const s=await this.doIndexingRequest(t,this.settings.restApi.nonce);await this.doPostIndexingAction(e,s),(0,n.flushSync)((()=>{this.setState((e=>({processed:e.processed+s.objects.length,firstTime:!1})))})),t=s.next_url}catch(e){(0,n.flushSync)((()=>{this.setState({state:A,error:e,firstTime:!1})}))}}async index(){for(const e of Object.keys(this.settings.restApi.indexing_endpoints))await this.doIndexing(e);this.isState(A)||this.isState(_)||this.completeIndexing()}async startIndexing(){this.setState({processed:0,state:I},this.index)}completeIndexing(){this.setState({state:v})}stopIndexing(){this.setState((e=>({state:_,processed:0,amount:e.amount-e.processed})))}componentDidMount(){var e,t;if(!this.settings.disabled&&(this.props.indexingStateCallback(0===this.state.amount?"completed":this.state.state),"true"===new URLSearchParams(window.location.search).get("start-indexation"))){const n=function(e,t){const n=new URL(e);return n.searchParams.delete("start-indexation"),n.href}(window.location.href);null,e=document.title,t=n,window.history.pushState(null,e,t),this.startIndexing()}}componentDidUpdate(e,t){this.state.state!==t.state&&this.props.indexingStateCallback(this.state.state)}isState(e){return this.state.state===e}renderFirstIndexationNotice(){return(0,t.createElement)(o.Alert,{type:"info"},(0,i.__)("This feature includes and replaces the Text Link Counter and Internal Linking Analysis","wordpress-seo"))}renderStartButton(){return(0,t.createElement)(o.NewButton,{variant:"primary",onClick:this.startIndexing},(0,i.__)("Start SEO data optimization","wordpress-seo"))}renderStopButton(){return(0,t.createElement)(o.NewButton,{variant:"secondary",onClick:this.stopIndexing},(0,i.__)("Stop SEO data optimization","wordpress-seo"))}renderDisabledTool(){return(0,t.createElement)(n.Fragment,null,(0,t.createElement)("p",null,(0,t.createElement)(o.NewButton,{variant:"secondary",disabled:!0},(0,i.__)("Start SEO data optimization","wordpress-seo"))),(0,t.createElement)(o.Alert,{type:"info"},(0,i.__)("SEO data optimization is disabled for non-production environments.","wordpress-seo")))}renderProgressBar(){return(0,t.createElement)(n.Fragment,null,(0,t.createElement)(o.ProgressBar,{style:{height:"16px",margin:"8px 0"},progressColor:a.colors.$color_pink_dark,max:parseInt(this.state.amount,10),value:this.state.processed}),(0,t.createElement)("p",{style:{color:a.colors.$palette_grey_text}},(0,i.__)("Optimizing SEO data... This may take a while.","wordpress-seo")))}renderErrorAlert(){return(0,t.createElement)(S,{message:yoastIndexingData.errorMessage,error:this.state.error})}renderTool(){return(0,t.createElement)(n.Fragment,null,this.isState(I)&&this.renderProgressBar(),this.isState(A)&&this.renderErrorAlert(),this.isState(_)&&this.state.firstTime&&this.renderFirstIndexationNotice(),this.isState(I)?this.renderStopButton():this.renderStartButton())}render(){return this.settings.disabled?this.renderDisabledTool():this.isState(v)||0===this.state.amount?(0,t.createElement)(o.Alert,{type:"success"},(0,i.__)("SEO data optimization complete","wordpress-seo")):this.renderTool()}}T.propTypes={indexingActions:l().object,preIndexingActions:l().object,indexingStateCallback:l().func},T.defaultProps={indexingActions:{},preIndexingActions:{},indexingStateCallback:()=>{}};const b=T;let P;function O(){P||(P=document.getElementById("yoast-seo-indexing-action")),P&&(0,n.render)((0,t.createElement)(b,{preIndexingActions:window.yoast.indexing.preIndexingActions,indexingActions:window.yoast.indexing.indexingActions}),P)}window.yoast=window.yoast||{},window.yoast.indexing=window.yoast.indexing||{},window.yoast.indexing.preIndexingActions={},window.yoast.indexing.indexingActions={},window.yoast.indexing.registerPreIndexingAction=(e,t)=>{window.yoast.indexing.preIndexingActions[e]=t,O()},window.yoast.indexing.registerIndexingAction=(e,t)=>{window.yoast.indexing.indexingActions[e]=t,O()},r()((function(){O()}))})();