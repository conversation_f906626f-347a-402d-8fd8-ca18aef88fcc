(()=>{"use strict";var e={6746:(e,t,n)=>{var r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},o=i(n(9196)),s=i(n(9156)),a=i(n(6743));function i(e){return e&&e.__esModule?e:{default:e}}var l=void 0;function c(e,t){var n,a,i,u,p,d,h,m,y=[],f={};for(d=0;d<e.length;d++)if("string"!==(p=e[d]).type){if(!t.hasOwnProperty(p.value)||void 0===t[p.value])throw new Error("Invalid interpolation, missing component node: `"+p.value+"`");if("object"!==r(t[p.value]))throw new Error("Invalid interpolation, component node must be a ReactElement or null: `"+p.value+"`","\n> "+l);if("componentClose"===p.type)throw new Error("Missing opening component token: `"+p.value+"`");if("componentOpen"===p.type){n=t[p.value],i=d;break}y.push(t[p.value])}else y.push(p.value);return n&&(u=function(e,t){var n,r,o=t[e],s=0;for(r=e+1;r<t.length;r++)if((n=t[r]).value===o.value){if("componentOpen"===n.type){s++;continue}if("componentClose"===n.type){if(0===s)return r;s--}}throw new Error("Missing closing component token `"+o.value+"`")}(i,e),h=c(e.slice(i+1,u),t),a=o.default.cloneElement(n,{},h),y.push(a),u<e.length-1&&(m=c(e.slice(u+1),t),y=y.concat(m))),1===y.length?y[0]:(y.forEach((function(e,t){e&&(f["interpolation-child-"+t]=e)})),(0,s.default)(f))}t.Z=function(e){var t=e.mixedString,n=e.components,o=e.throwErrors;if(l=t,!n)return t;if("object"!==(void 0===n?"undefined":r(n))){if(o)throw new Error("Interpolation Error: unable to process `"+t+"` because components is not an object");return t}var s=(0,a.default)(t);try{return c(s,n)}catch(e){if(o)throw new Error("Interpolation Error: unable to process `"+t+"` because of error `"+e.message+"`");return t}}},6743:e=>{function t(e){return e.match(/^\{\{\//)?{type:"componentClose",value:e.replace(/\W/g,"")}:e.match(/\/\}\}$/)?{type:"componentSelfClosing",value:e.replace(/\W/g,"")}:e.match(/^\{\{/)?{type:"componentOpen",value:e.replace(/\W/g,"")}:{type:"string",value:e}}e.exports=function(e){return e.split(/(\{\{\/?\s*\w+\s*\/?\}\})/g).map(t)}},9156:(e,t,n)=>{var r=n(9196),o="function"==typeof Symbol&&Symbol.for&&Symbol.for("react.element")||60103,s=n(7942),a=n(9179),i=n(397),l=".",c=":",u="function"==typeof Symbol&&Symbol.iterator,p="@@iterator";function d(e,t){return e&&"object"==typeof e&&null!=e.key?(n=e.key,r={"=":"=0",":":"=2"},"$"+(""+n).replace(/[=:]/g,(function(e){return r[e]}))):t.toString(36);var n,r}function h(e,t,n,r){var s,i=typeof e;if("undefined"!==i&&"boolean"!==i||(e=null),null===e||"string"===i||"number"===i||"object"===i&&e.$$typeof===o)return n(r,e,""===t?l+d(e,0):t),1;var m=0,y=""===t?l:t+c;if(Array.isArray(e))for(var f=0;f<e.length;f++)m+=h(s=e[f],y+d(s,f),n,r);else{var g=function(e){var t=e&&(u&&e[u]||e[p]);if("function"==typeof t)return t}(e);if(g)for(var w,b=g.call(e),v=0;!(w=b.next()).done;)m+=h(s=w.value,y+d(s,v++),n,r);else if("object"===i){var E=""+e;a(!1,"Objects are not valid as a React child (found: %s).%s","[object Object]"===E?"object with keys {"+Object.keys(e).join(", ")+"}":E,"")}}return m}var m=/\/+/g;function y(e){return(""+e).replace(m,"$&/")}var f,g,w=b,b=function(e){var t=this;if(t.instancePool.length){var n=t.instancePool.pop();return t.call(n,e),n}return new t(e)};function v(e,t,n,r){this.result=e,this.keyPrefix=t,this.func=n,this.context=r,this.count=0}function E(e,t,n){var o,a,i=e.result,l=e.keyPrefix,c=e.func,u=e.context,p=c.call(u,t,e.count++);Array.isArray(p)?k(p,i,n,s.thatReturnsArgument):null!=p&&(r.isValidElement(p)&&(o=p,a=l+(!p.key||t&&t.key===p.key?"":y(p.key)+"/")+n,p=r.cloneElement(o,{key:a},void 0!==o.props?o.props.children:void 0)),i.push(p))}function k(e,t,n,r,o){var s="";null!=n&&(s=y(n)+"/");var a=v.getPooled(t,s,r,o);!function(e,t,n){null==e||h(e,"",t,n)}(e,E,a),v.release(a)}v.prototype.destructor=function(){this.result=null,this.keyPrefix=null,this.func=null,this.context=null,this.count=0},f=function(e,t,n,r){var o=this;if(o.instancePool.length){var s=o.instancePool.pop();return o.call(s,e,t,n,r),s}return new o(e,t,n,r)},(g=v).instancePool=[],g.getPooled=f||w,g.poolSize||(g.poolSize=10),g.release=function(e){var t=this;a(e instanceof t,"Trying to release an instance into a pool of a different type."),e.destructor(),t.instancePool.length<t.poolSize&&t.instancePool.push(e)},e.exports=function(e){if("object"!=typeof e||!e||Array.isArray(e))return i(!1,"React.addons.createFragment only accepts a single object. Got: %s",e),e;if(r.isValidElement(e))return i(!1,"React.addons.createFragment does not accept a ReactElement without a wrapper object."),e;a(1!==e.nodeType,"React.addons.createFragment(...): Encountered an invalid child; DOM elements are not valid children of React components.");var t=[];for(var n in e)k(e[n],t,n,s.thatReturnsArgument);return t}},7942:e=>{function t(e){return function(){return e}}var n=function(){};n.thatReturns=t,n.thatReturnsFalse=t(!1),n.thatReturnsTrue=t(!0),n.thatReturnsNull=t(null),n.thatReturnsThis=function(){return this},n.thatReturnsArgument=function(e){return e},e.exports=n},9179:e=>{e.exports=function(e,t,n,r,o,s,a,i){if(!e){var l;if(void 0===t)l=new Error("Minified exception occurred; use the non-minified dev environment for the full error message and additional helpful warnings.");else{var c=[n,r,o,s,a,i],u=0;(l=new Error(t.replace(/%s/g,(function(){return c[u++]})))).name="Invariant Violation"}throw l.framesToPop=1,l}}},397:(e,t,n)=>{var r=n(7942);e.exports=r},9196:e=>{e.exports=window.React}},t={};function n(r){var o=t[r];if(void 0!==o)return o.exports;var s=t[r]={exports:{}};return e[r](s,s.exports,n),s.exports}n.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return n.d(t,{a:t}),t},n.d=(e,t)=>{for(var r in t)n.o(t,r)&&!n.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},n.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),(()=>{var e=n(9196);const t=window.wp.element,r=window.yoast.propTypes;var o=n.n(r);const s=window.yoast.styledComponents;var a=n.n(s);const i=window.wp.i18n,l=window.lodash;var c=n(6746);const u=window.yoast.helpers,p=window.yoast.componentsNew,d=t=>(0,e.createElement)(p.Alert,{type:"warning",className:t.className},(0,i.sprintf)(/* translators: %s: Expands to "Wincher". */
(0,i.__)('Your %s account does not contain any keyphrases for this website yet. You can track keyphrases by using the "Track SEO Performance" button in the post editor.',"wordpress-seo"),"Wincher"));d.propTypes={className:o().string},d.defaultProps={className:""};const h=d,m=window.moment;var y=n.n(m);const f=({data:t,mapChartDataToTableData:n,dataTableCaption:r,dataTableHeaderLabels:o,isDataTableVisuallyHidden:s})=>t.length!==o.length?(0,e.createElement)("p",null,(0,i.__)("The number of headers and header labels don't match.","wordpress-seo")):(0,e.createElement)("div",{className:s?"screen-reader-text":null},(0,e.createElement)("table",null,(0,e.createElement)("caption",null,r),(0,e.createElement)("thead",null,(0,e.createElement)("tr",null,o.map(((t,n)=>(0,e.createElement)("th",{key:n},t))))),(0,e.createElement)("tbody",null,(0,e.createElement)("tr",null,t.map(((t,r)=>(0,e.createElement)("td",{key:r},n(t.y))))))));f.propTypes={data:o().arrayOf(o().shape({x:o().number,y:o().number})).isRequired,mapChartDataToTableData:o().func,dataTableCaption:o().string.isRequired,dataTableHeaderLabels:o().array.isRequired,isDataTableVisuallyHidden:o().bool},f.defaultProps={mapChartDataToTableData:null,isDataTableVisuallyHidden:!0};const g=f,w=({data:n,width:r,height:o,fillColor:s,strokeColor:a,strokeWidth:i,className:l,mapChartDataToTableData:c,dataTableCaption:u,dataTableHeaderLabels:p,isDataTableVisuallyHidden:d})=>{const h=Math.max(1,Math.max(...n.map((e=>e.x)))),m=Math.max(1,Math.max(...n.map((e=>e.y)))),y=o-i,f=n.map((e=>`${e.x/h*r},${y-e.y/m*y+i}`)).join(" "),w=`0,${y+i} `+f+` ${r},${y+i}`;return(0,e.createElement)(t.Fragment,null,(0,e.createElement)("svg",{width:r,height:o,viewBox:`0 0 ${r} ${o}`,className:l,role:"img","aria-hidden":"true",focusable:"false"},(0,e.createElement)("polygon",{fill:s,points:w}),(0,e.createElement)("polyline",{fill:"none",stroke:a,strokeWidth:i,strokeLinejoin:"round",strokeLinecap:"round",points:f})),c&&(0,e.createElement)(g,{data:n,mapChartDataToTableData:c,dataTableCaption:u,dataTableHeaderLabels:p,isDataTableVisuallyHidden:d}))};w.propTypes={data:o().arrayOf(o().shape({x:o().number,y:o().number})).isRequired,width:o().number.isRequired,height:o().number.isRequired,fillColor:o().string,strokeColor:o().string,strokeWidth:o().number,className:o().string,mapChartDataToTableData:o().func,dataTableCaption:o().string.isRequired,dataTableHeaderLabels:o().array.isRequired,isDataTableVisuallyHidden:o().bool},w.defaultProps={fillColor:null,strokeColor:"#000000",strokeWidth:1,className:"",mapChartDataToTableData:null,isDataTableVisuallyHidden:!0};const b=w,v=()=>(0,e.createElement)("p",{className:"yoast-wincher-seo-performance-modal__loading-message"},(0,i.__)("Tracking the ranking position...","wordpress-seo")," ",(0,e.createElement)(p.SvgIcon,{icon:"loading-spinner"})),E=a()(p.SvgIcon)`
	margin-left: 2px;
	flex-shrink: 0;
	rotate: ${e=>e.isImproving?"-90deg":"90deg"};
`,k=a().span`
	color: ${e=>e.isImproving?"#69AB56":"#DC3332"};
	font-size: 13px;
	font-weight: 600;
	line-height: 20px;
	margin-right: 2px;
	margin-left: 12px;
`,T=a().td`
	padding-right: 0 !important;

	& > div {
		margin: 0px;
	}
`,_=a().td`
	padding-left: 2px !important;
`,C=a().td.attrs({className:"yoast-table--nopadding"})`
	& > div {
		justify-content: center;
	}
`,S=a().div`
	display: flex;
	align-items: center;
	& > a {
		box-sizing: border-box;
	}
`,x=a().button`
	background: none;
	color: inherit;
	border: none;
	padding: 0;
	font: inherit;
	cursor: pointer;
	outline: inherit;
    display: flex;
    align-items: center;
`,D=a().tr`
	background-color: ${e=>e.isEnabled?"#FFFFFF":"#F9F9F9"} !important;
`;function R(e){return Math.round(100*e)}function I({chartData:t}){if((0,l.isEmpty)(t)||(0,l.isEmpty)(t.position))return"?";const n=function(e){return Array.from({length:e.position.history.length},((e,t)=>t+1)).map((e=>(0,i.sprintf)((0,i._n)("%d day","%d days",e,"wordpress-seo"),e)))}(t),r=t.position.history.map(((e,t)=>({x:t,y:101-e.value})));return(0,e.createElement)(b,{width:66,height:24,data:r,strokeWidth:1.8,strokeColor:"#498afc",fillColor:"#ade3fc",mapChartDataToTableData:R,dataTableCaption:(0,i.__)("Keyphrase position in the last 90 days on a scale from 0 to 100.","wordpress-seo"),dataTableHeaderLabels:n})}function N(e){return!e||!e.position||e.position.value>100?"> 100":e.position.value}I.propTypes={chartData:o().object},I.defaultProps={chartData:{}};const L=({rowData:n})=>{var r;if(null==n||null===(r=n.position)||void 0===r||!r.change)return(0,e.createElement)(I,{chartData:n});const o=n.position.change<0;return(0,e.createElement)(t.Fragment,null,(0,e.createElement)(I,{chartData:n}),(0,e.createElement)(k,{isImproving:o},Math.abs(n.position.change)),(0,e.createElement)(E,{icon:"caret-right",color:o?"#69AB56":"#DC3332",size:"14px",isImproving:o}))};function P(n){var r;const{keyphrase:o,rowData:s,onTrackKeyphrase:a,onUntrackKeyphrase:c,isFocusKeyphrase:u,isDisabled:d,isLoading:h,isSelected:m,onSelectKeyphrases:f}=n,g=!(0,l.isEmpty)(s),w=!(0,l.isEmpty)(null==s||null===(r=s.position)||void 0===r?void 0:r.history),b=(0,t.useCallback)((()=>{d||(g?c(o,s.id):a(o))}),[o,a,c,g,s,d]),E=(0,t.useCallback)((()=>{f((e=>m?e.filter((e=>e!==o)):e.concat(o)))}),[f,m,o]);return(0,e.createElement)(D,{isEnabled:g},(0,e.createElement)(T,null,w&&(0,e.createElement)(p.Checkbox,{id:"select-"+o,onChange:E,checked:m,label:""})),(0,e.createElement)(_,null,o,u&&(0,e.createElement)("span",null,"*")),function(n){const{rowData:r,websiteId:o,keyphrase:s,onSelectKeyphrases:a}=n,c=(0,t.useCallback)((()=>{a([s])}),[a,s]),u=!(0,l.isEmpty)(r),d=r&&r.updated_at&&y()(r.updated_at)>=y()().subtract(7,"days"),h=r?(0,i.sprintf)("https://app.wincher.com/websites/%s/keywords?serp=%s&utm_medium=plugin&utm_source=yoast&referer=yoast&partner=yoast",o,r.id):null;return u?d?(0,e.createElement)(t.Fragment,null,(0,e.createElement)("td",null,(0,e.createElement)(S,null,N(r),(0,e.createElement)(p.ButtonStyledLink,{variant:"secondary",href:h,style:{height:28,marginLeft:12},rel:"noopener",target:"_blank"},(0,i.__)("View","wordpress-seo")))),(0,e.createElement)("td",{className:"yoast-table--nopadding"},(0,e.createElement)(x,{type:"button",onClick:c},(0,e.createElement)(L,{rowData:r}))),(0,e.createElement)("td",null,(m=r.updated_at,y()(m).fromNow()))):(0,e.createElement)("td",{className:"yoast-table--nopadding",colSpan:"3"},(0,e.createElement)(v,null)):(0,e.createElement)("td",{className:"yoast-table--nopadding",colSpan:"3"},(0,e.createElement)("i",null,(0,i.__)("Activate tracking to show the ranking position","wordpress-seo")));var m}(n),(0,e.createElement)(C,null,function({keyphrase:t,isEnabled:n,toggleAction:r,isLoading:o}){return o?(0,e.createElement)(p.SvgIcon,{icon:"loading-spinner"}):(0,e.createElement)(p.Toggle,{id:`toggle-keyphrase-tracking-${t}`,className:"wincher-toggle",isEnabled:n,onSetToggleState:r,showToggleStateLabel:!1})}({keyphrase:o,isEnabled:g,toggleAction:b,isLoading:h})))}L.propTypes={rowData:o().object},P.propTypes={rowData:o().object,keyphrase:o().string.isRequired,onTrackKeyphrase:o().func,onUntrackKeyphrase:o().func,isFocusKeyphrase:o().bool,isDisabled:o().bool,isLoading:o().bool,websiteId:o().string,isSelected:o().bool.isRequired,onSelectKeyphrases:o().func.isRequired},P.defaultProps={rowData:{},onTrackKeyphrase:()=>{},onUntrackKeyphrase:()=>{},isFocusKeyphrase:!1,isDisabled:!1,isLoading:!1,websiteId:""};const A=t=>{const n=(0,i.sprintf)(/* translators: %s expands to a link to open the Wincher login popup. */
(0,i.__)("It seems like something went wrong when retrieving your website's data. Please %s and try again.","wordpress-seo"),"{{reconnectToWincher/}}","Wincher");return(0,e.createElement)(p.Alert,{type:"error",className:t.className},(0,c.Z)({mixedString:n,components:{reconnectToWincher:(0,e.createElement)("a",{href:"#",onClick:e=>{e.preventDefault(),t.onReconnect()}},(0,i.sprintf)(/* translators: %s : Expands to "Wincher". */
(0,i.__)("reconnect to %s","wordpress-seo"),"Wincher"))}}))};A.propTypes={onReconnect:o().func.isRequired,className:o().string},A.defaultProps={className:""};const q=A,W=window.yoast.styleGuide,j=window.wp.apiFetch;var $=n.n(j);async function F(e){try{return await $()(e)}catch(e){return e.error&&e.status?e:e instanceof Response&&await e.json()}}const O=a().p`
	color: ${W.colors.$color_pink_dark};
	font-size: 14px;
	font-weight: 700;
	margin: 13px 0 10px;
`,H=a()(p.SvgIcon)`
	margin-right: 5px;
	vertical-align: middle;
`,B=a().button`
	position: absolute;
	top: 9px;
	right: 9px;
	border: none;
    background: none;
    cursor: pointer;
`,K=a().p`
	font-size: 13px;
	font-weight: 500;
	margin: 10px 0 13px;
`,z=a().div`
	position: relative;
	background: ${e=>e.isTitleShortened?"#F5F7F7":"transparent"};
	border: 1px solid #C7C7C7;
	border-left: 4px solid${W.colors.$color_pink_dark};
	padding: 0 16px;
	margin-bottom: 1.5em;
`,M=e=>{const[n,r]=(0,t.useState)(null);return(0,t.useEffect)((()=>{e&&!n&&async function(){return await F({path:"yoast/v1/wincher/account/limit",method:"GET"})}().then((e=>r(e)))}),[n]),n};M.propTypes={limit:o().bool.isRequired};const V=({limit:t,usage:n,isTitleShortened:r,isFreeAccount:o})=>{const s=(0,i.sprintf)(
/* Translators: %1$s expands to the number of used keywords.
   * %2$s expands to the account keywords limit.
   */
(0,i.__)("Your are tracking %1$s out of %2$s keyphrases included in your free account.","wordpress-seo"),n,t),a=(0,i.sprintf)(
/* Translators: %1$s expands to the number of used keywords.
   * %2$s expands to the account keywords limit.
   */
(0,i.__)("Your are tracking %1$s out of %2$s keyphrases included in your account.","wordpress-seo"),n,t),l=o?s:a,c=(0,i.sprintf)(
/* Translators: %1$s expands to the number of used keywords.
   * %2$s expands to the account keywords limit.
   */
(0,i.__)("Keyphrases tracked: %1$s/%2$s","wordpress-seo"),n,t),u=r?c:l;return(0,e.createElement)(O,null,r&&(0,e.createElement)(H,{icon:"exclamation-triangle",color:W.colors.$color_pink_dark,size:"14px"}),u)};V.propTypes={limit:o().number.isRequired,usage:o().number.isRequired,isTitleShortened:o().bool,isFreeAccount:o().bool};const G=(0,u.makeOutboundLink)(),Y=({discount:t,months:n})=>{const r=(0,e.createElement)(G,{href:wpseoAdminGlobalL10n["links.wincher.upgrade"],style:{fontWeight:600}},(0,i.sprintf)(/* Translators: %s : Expands to "Wincher". */
(0,i.__)("Click here to upgrade your %s plan","wordpress-seo"),"Wincher"));if(!t||!n)return(0,e.createElement)(K,null,r);const o=100*t,s=(0,i.sprintf)(
/* Translators: %1$s expands to upgrade account link.
   * %2$s expands to the upgrade discount value.
   * %3$s expands to the upgrade discount duration e.g. 2 months.
   */
(0,i.__)("%1$s and get an exclusive %2$s discount for %3$s month(s).","wordpress-seo"),"{{wincherAccountUpgradeLink/}}",o+"%",n);return(0,e.createElement)(K,null,(0,c.Z)({mixedString:s,components:{wincherAccountUpgradeLink:r}}))};Y.propTypes={discount:o().number,months:o().number};const U=({onClose:n,isTitleShortened:r,trackingInfo:o})=>{const s=(()=>{const[e,n]=(0,t.useState)(null);return(0,t.useEffect)((()=>{e||async function(){return await F({path:"yoast/v1/wincher/account/upgrade-campaign",method:"GET"})}().then((e=>n(e)))}),[e]),e})();if(null===o)return null;const{limit:a,usage:l}=o;if(!(a&&l/a>=.8))return null;const c=Boolean(null==s?void 0:s.discount);return(0,e.createElement)(z,{isTitleShortened:r},n&&(0,e.createElement)(B,{type:"button","aria-label":(0,i.__)("Close the upgrade callout","wordpress-seo"),onClick:n},(0,e.createElement)(p.SvgIcon,{icon:"times-circle",color:W.colors.$color_pink_dark,size:"14px"})),(0,e.createElement)(V,{...o,isTitleShortened:r,isFreeAccount:c}),(0,e.createElement)(Y,{discount:null==s?void 0:s.discount,months:null==s?void 0:s.months}))};U.propTypes={onClose:o().func,isTitleShortened:o().bool,trackingInfo:o().object};const Z=U,Q=(0,u.makeOutboundLink)(),X=(0,u.makeOutboundLink)(),J=(0,u.makeOutboundLink)(),ee=(0,u.makeOutboundLink)(),te=a().div`
	& .wincher-performance-report-alert {
		margin-bottom: 1em;
	}
`,ne=a().table`
	pointer-events: none;
	user-select: none;
`,re=a().div`
	position: relative;
	width: 100%;
	overflow-y: auto;
`,oe=a().div`
	margin: 0;
    -webkit-filter: blur(4px);
    -moz-filter: blur(4px);
    -o-filter: blur(4px);
    -ms-filter: blur(4px);
    filter: blur(4px);
`,se=a().p`
	top: 47%;
	left: 50%;
	position: absolute;
`,ae=e=>{const{websiteId:t,id:n}=e;return(0,i.sprintf)("https://app.wincher.com/websites/%s/keywords?serp=%s&utm_medium=plugin&utm_source=yoast&referer=yoast&partner=yoast",t,n)},ie=t=>{const{isLoggedIn:n,onConnectAction:r}=t;return n?null:(0,e.createElement)(se,null,(0,e.createElement)(p.NewButton,{onClick:r,variant:"primary",style:{left:"-50%",backgroundColor:"#2371B0"}},(0,i.sprintf)(/* translators: %s expands to Wincher */
(0,i.__)("Connect with %s","wordpress-seo"),"Wincher")))};ie.propTypes={isLoggedIn:o().bool.isRequired,onConnectAction:o().func.isRequired};const le=({isBlurred:t,children:n})=>t?(0,e.createElement)("td",null,(0,e.createElement)(oe,null,n)):(0,e.createElement)("td",null,n);le.propTypes={isBlurred:o().bool,children:o().oneOfType([o().string,o().number,o().object])};const ce=({keyphrase:t,websiteId:n,isBlurred:r})=>{const{id:o,keyword:s}=t;return(0,e.createElement)("tr",null,(0,e.createElement)(le,{isBlurred:r},s),(0,e.createElement)(le,{isBlurred:r},N(t)),(0,e.createElement)(le,{isBlurred:r,className:"yoast-table--nopadding"},(0,e.createElement)(I,{chartData:t})),(0,e.createElement)(le,{isBlurred:r,className:"yoast-table--nobreak"},(0,e.createElement)(Q,{href:ae({websiteId:n,id:o})},(0,i.__)("View","wordpress-seo"))))};ce.propTypes={keyphrase:o().object.isRequired,websiteId:o().string.isRequired,isBlurred:o().bool};const ue=()=>(0,e.createElement)(p.Alert,{type:"error",className:"wincher-performance-report-alert"},(0,i.sprintf)((0,i.__)("Network Error: Unable to connect to the server. Please check your internet connection and try again later.","wordpress-seo"))),pe=t=>{const{data:n}=t;return!(0,l.isEmpty)(n)&&(0,l.isEmpty)(n.results)?(0,e.createElement)(p.Alert,{type:"success",className:"wincher-performance-report-alert"},(0,i.sprintf)(/* translators: %1$s and %2$s: Expands to "Wincher". */
(0,i.__)('You have successfully connected with %1$s. Your %2$s account does not contain any keyphrases for this website yet. You can track keyphrases by using the "Track SEO Performance" button in the post editor.',"wordpress-seo"),"Wincher","Wincher")):(0,e.createElement)(p.Alert,{type:"success",className:"wincher-performance-report-alert"},(0,i.sprintf)(/* translators: %s: Expands to "Wincher". */
(0,i.__)("You have successfully connected with %s.","wordpress-seo"),"Wincher"))};pe.propTypes={data:o().object.isRequired};const de=t=>{const{data:n,onConnectAction:r,isConnectSuccess:o,isNetworkError:s,isFailedRequest:a}=t;return s?(0,e.createElement)(ue,{data:n}):o?(0,e.createElement)(pe,{data:n}):a?(0,e.createElement)(q,{onReconnect:r,className:"wincher-performance-report-alert"}):null};de.propTypes={data:o().object.isRequired,onConnectAction:o().func.isRequired,isConnectSuccess:o().bool.isRequired,isNetworkError:o().bool.isRequired,isFailedRequest:o().bool.isRequired};const he=t=>{const{data:n,isNetworkError:r,isConnectSuccess:o}=t,s=(e=>e&&[401,403,404].includes(e.status))(n);return r||o||s?(0,e.createElement)(de,{...t,isFailedRequest:s}):!n||(0,l.isEmpty)(n.results)?(0,e.createElement)(h,{className:"wincher-performance-report-alert"}):null};he.propTypes={data:o().object.isRequired,onConnectAction:o().func.isRequired,isConnectSuccess:o().bool.isRequired,isNetworkError:o().bool.isRequired};const me=({isLoggedIn:t})=>{const n=(0,i.sprintf)(/* translators: %s expands to a link to Wincher login */
(0,i.__)("This overview only shows you keyphrases added to Yoast SEO. There may be other keyphrases added to your %s.","wordpress-seo"),"{{wincherAccountLink/}}"),r=(0,i.sprintf)(/* translators: %s expands to a link to Wincher login */
(0,i.__)("This overview will show you your top performing keyphrases in Google. Connect with %s to get started.","wordpress-seo"),"{{wincherLink/}}"),o=t?n:r;return(0,e.createElement)("p",null,(0,c.Z)({mixedString:o,components:{wincherAccountLink:(0,e.createElement)(J,{href:wpseoAdminGlobalL10n["links.wincher.login"]},(0,i.sprintf)(/* translators: %s : Expands to "Wincher". */
(0,i.__)("%s account","wordpress-seo"),"Wincher")),wincherLink:(0,e.createElement)(ee,{href:wpseoAdminGlobalL10n["links.wincher.about"]},(0,i.sprintf)(/* translators: %s : Expands to "Wincher". */
(0,i.__)("%s","wordpress-seo"),"Wincher"))}}))};me.propTypes={isLoggedIn:o().bool.isRequired};const ye={results:[{id:0,keyword:"wincher",position:{value:84,history:[{value:90},{value:89},{value:94},{value:98},{value:84}]}},{id:1,keyword:"rank tracker",position:{value:20,history:[{value:50},{value:30},{value:66},{value:15},{value:20}]}},{id:2,keyword:"performance",position:{value:2,history:[{value:44},{value:66},{value:18},{value:31},{value:2}]}}]},fe=({isBlurred:t,children:n})=>t?(0,e.createElement)(ne,{className:"yoast yoast-table"},n):(0,e.createElement)("table",{className:"yoast yoast-table"},n);fe.propTypes={isBlurred:o().bool,children:o().any};const ge=n=>{const{className:r,websiteId:o,isLoggedIn:s,onConnectAction:a,isConnectSuccess:c}=n,u=s?n.data:ye,p=!s,d=(e=>e&&!(0,l.isEmpty)(e)&&!(0,l.isEmpty)(e.results))(u),h=M(s);return(0,e.createElement)(te,{className:r},s&&(0,e.createElement)(Z,{isTitleShortened:!0,trackingInfo:h}),(0,e.createElement)(he,{...n,data:u,isConnectSuccess:c&&s}),d&&(0,e.createElement)(t.Fragment,null,(0,e.createElement)(me,{isLoggedIn:s}),(0,e.createElement)(re,null,(0,e.createElement)(fe,{isBlurred:p},(0,e.createElement)("thead",null,(0,e.createElement)("tr",null,(0,e.createElement)("th",{scope:"col",abbr:(0,i.__)("Keyphrase","wordpress-seo")},(0,i.__)("Keyphrase","wordpress-seo")),(0,e.createElement)("th",{scope:"col",abbr:(0,i.__)("Position","wordpress-seo")},(0,i.__)("Position","wordpress-seo")),(0,e.createElement)("th",{scope:"col",abbr:(0,i.__)("Position over time","wordpress-seo")},(0,i.__)("Position over time","wordpress-seo")),(0,e.createElement)("td",{className:"yoast-table--nobreak"}))),(0,e.createElement)("tbody",null,(0,l.map)(u.results,((t,n)=>(0,e.createElement)(ce,{key:`keyphrase-${n}`,keyphrase:t,websiteId:o,isBlurred:p}))))),(0,e.createElement)(ie,{isLoggedIn:s,onConnectAction:a})),(0,e.createElement)("p",{style:{marginBottom:0,position:"relative"}},(0,e.createElement)(X,{href:wpseoAdminGlobalL10n["links.wincher.login"]},(0,i.sprintf)(/* translators: %s expands to Wincher */
(0,i.__)("Get more insights over at %s","wordpress-seo"),"Wincher")))))};ge.propTypes={className:o().string,data:o().object.isRequired,websiteId:o().string.isRequired,isLoggedIn:o().bool.isRequired,isConnectSuccess:o().bool.isRequired,isNetworkError:o().bool.isRequired,onConnectAction:o().func.isRequired},ge.defaultProps={className:"wincher-seo-performance"};const we=ge;class be{constructor(e,t={},n={}){this.url=e,this.origin=new URL(e).origin,this.eventHandlers=Object.assign({success:{type:"",callback:()=>{}},error:{type:"",callback:()=>{}}},t),this.options=Object.assign({height:570,width:340,title:""},n),this.popup=null,this.createPopup=this.createPopup.bind(this),this.messageHandler=this.messageHandler.bind(this),this.getPopup=this.getPopup.bind(this)}createPopup(){const{height:e,width:t,title:n}=this.options,r=["top="+(window.top.outerHeight/2+window.top.screenY-e/2),"left="+(window.top.outerWidth/2+window.top.screenX-t/2),"width="+t,"height="+e,"resizable=1","scrollbars=1","status=0"];this.popup&&!this.popup.closed||(this.popup=window.open(this.url,n,r.join(","))),this.popup&&this.popup.focus(),window.addEventListener("message",this.messageHandler,!1)}async messageHandler(e){const{data:t,source:n,origin:r}=e;r===this.origin&&this.popup===n&&(t.type===this.eventHandlers.success.type&&(this.popup.close(),window.removeEventListener("message",this.messageHandler,!1),await this.eventHandlers.success.callback(t)),t.type===this.eventHandlers.error.type&&(this.popup.close(),window.removeEventListener("message",this.messageHandler,!1),await this.eventHandlers.error.callback(t)))}getPopup(){return this.popup}isClosed(){return!this.popup||this.popup.closed}focus(){this.isClosed()||this.popup.focus()}}class ve extends t.Component{constructor(){super(),this.state={wincherData:{},wincherWebsiteId:wpseoWincherDashboardWidgetL10n.wincher_website_id,wincherIsLoggedIn:"1"===wpseoWincherDashboardWidgetL10n.wincher_is_logged_in,isDataFetched:!1,isConnectSuccess:!1,isNetworkError:!1},this.onConnect=this.onConnect.bind(this),this.getWincherData=this.getWincherData.bind(this),this.performAuthenticationRequest=this.performAuthenticationRequest.bind(this),this.onConnectSuccess=this.onConnectSuccess.bind(this),this.onNetworkDisconnectionError=this.onNetworkDisconnectionError.bind(this)}componentDidMount(){const e=jQuery("#wpseo-wincher-dashboard-overview-hide");e.is(":checked")&&this.fetchData(),e.on("click",(()=>{this.fetchData()}))}fetchData(){this.state.isDataFetched||(this.state.wincherIsLoggedIn&&this.getWincherData(),this.setState({isDataFetched:!0}))}async getWincherData(){const e=await async function(e=null,t=null,n=null,r){return await F({path:"yoast/v1/wincher/keyphrases",method:"POST",data:{keyphrases:e,permalink:n,startAt:t},signal:r})}();if(200===e.status){const t=(0,l.filter)(e.results,(e=>!(0,l.isEmpty)(e.position))),n=(0,l.sortBy)(t,(e=>e.position.value)).splice(0,5);this.setState({wincherData:{results:n,status:e.status}})}else this.setState({wincherData:{results:[],status:e.status}})}async performAuthenticationRequest(e){if(200!==(await async function(e){const{code:t,websiteId:n}=e;return await F({path:"yoast/v1/wincher/authenticate",method:"POST",data:{code:t,websiteId:n}})}(e)).status)return;this.setState({wincherIsLoggedIn:!0,wincherWebsiteId:e.websiteId.toString()}),await this.getWincherData();const t=this.loginPopup.getPopup();t&&t.close()}async onConnectSuccess(e){this.setState({isConnectSuccess:!0,isNetworkError:!1}),await this.performAuthenticationRequest(e)}async onNetworkDisconnectionError(){this.setState({isConnectSuccess:!1,isNetworkError:!0})}async onConnect(){if(this.loginPopup&&!this.loginPopup.isClosed())return void this.loginPopup.focus();const{url:e}=await async function(){return await F({path:"yoast/v1/wincher/authorization-url",method:"GET"})}();e&&void 0!==e?(this.loginPopup=new be(e,{success:{type:"wincher:oauth:success",callback:e=>this.onConnectSuccess(e)},error:{type:"wincher:oauth:error",callback:()=>{}}},{title:"Wincher_login",width:500,height:700}),this.loginPopup.createPopup()):this.onNetworkDisconnectionError()}render(){return(0,e.createElement)(we,{key:"wincher-performance-report",data:this.state.wincherData,websiteId:this.state.wincherWebsiteId,isLoggedIn:this.state.wincherIsLoggedIn,isConnectSuccess:this.state.isConnectSuccess,isNetworkError:this.state.isNetworkError,onConnectAction:this.onConnect})}}const Ee=document.getElementById("yoast-seo-wincher-dashboard-widget");Ee&&(0,t.render)((0,e.createElement)(ve,null),Ee)})()})();