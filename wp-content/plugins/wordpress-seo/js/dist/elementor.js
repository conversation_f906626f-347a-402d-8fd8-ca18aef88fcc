(()=>{var e={6746:(e,t,s)=>{"use strict";var a="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},n=i(s(9196)),r=i(s(9156)),o=i(s(6743));function i(e){return e&&e.__esModule?e:{default:e}}var l=void 0;function c(e,t){var s,o,i,d,p,u,m,h,g=[],y={};for(u=0;u<e.length;u++)if("string"!==(p=e[u]).type){if(!t.hasOwnProperty(p.value)||void 0===t[p.value])throw new Error("Invalid interpolation, missing component node: `"+p.value+"`");if("object"!==a(t[p.value]))throw new Error("Invalid interpolation, component node must be a ReactElement or null: `"+p.value+"`","\n> "+l);if("componentClose"===p.type)throw new Error("Missing opening component token: `"+p.value+"`");if("componentOpen"===p.type){s=t[p.value],i=u;break}g.push(t[p.value])}else g.push(p.value);return s&&(d=function(e,t){var s,a,n=t[e],r=0;for(a=e+1;a<t.length;a++)if((s=t[a]).value===n.value){if("componentOpen"===s.type){r++;continue}if("componentClose"===s.type){if(0===r)return a;r--}}throw new Error("Missing closing component token `"+n.value+"`")}(i,e),m=c(e.slice(i+1,d),t),o=n.default.cloneElement(s,{},m),g.push(o),d<e.length-1&&(h=c(e.slice(d+1),t),g=g.concat(h))),1===g.length?g[0]:(g.forEach((function(e,t){e&&(y["interpolation-child-"+t]=e)})),(0,r.default)(y))}t.Z=function(e){var t=e.mixedString,s=e.components,n=e.throwErrors;if(l=t,!s)return t;if("object"!==(void 0===s?"undefined":a(s))){if(n)throw new Error("Interpolation Error: unable to process `"+t+"` because components is not an object");return t}var r=(0,o.default)(t);try{return c(r,s)}catch(e){if(n)throw new Error("Interpolation Error: unable to process `"+t+"` because of error `"+e.message+"`");return t}}},6743:e=>{"use strict";function t(e){return e.match(/^\{\{\//)?{type:"componentClose",value:e.replace(/\W/g,"")}:e.match(/\/\}\}$/)?{type:"componentSelfClosing",value:e.replace(/\W/g,"")}:e.match(/^\{\{/)?{type:"componentOpen",value:e.replace(/\W/g,"")}:{type:"string",value:e}}e.exports=function(e){return e.split(/(\{\{\/?\s*\w+\s*\/?\}\})/g).map(t)}},9156:(e,t,s)=>{"use strict";var a=s(9196),n="function"==typeof Symbol&&Symbol.for&&Symbol.for("react.element")||60103,r=s(7942),o=s(9179),i=s(397),l=".",c=":",d="function"==typeof Symbol&&Symbol.iterator,p="@@iterator";function u(e,t){return e&&"object"==typeof e&&null!=e.key?(s=e.key,a={"=":"=0",":":"=2"},"$"+(""+s).replace(/[=:]/g,(function(e){return a[e]}))):t.toString(36);var s,a}function m(e,t,s,a){var r,i=typeof e;if("undefined"!==i&&"boolean"!==i||(e=null),null===e||"string"===i||"number"===i||"object"===i&&e.$$typeof===n)return s(a,e,""===t?l+u(e,0):t),1;var h=0,g=""===t?l:t+c;if(Array.isArray(e))for(var y=0;y<e.length;y++)h+=m(r=e[y],g+u(r,y),s,a);else{var f=function(e){var t=e&&(d&&e[d]||e[p]);if("function"==typeof t)return t}(e);if(f)for(var w,b=f.call(e),E=0;!(w=b.next()).done;)h+=m(r=w.value,g+u(r,E++),s,a);else if("object"===i){var v=""+e;o(!1,"Objects are not valid as a React child (found: %s).%s","[object Object]"===v?"object with keys {"+Object.keys(e).join(", ")+"}":v,"")}}return h}var h=/\/+/g;function g(e){return(""+e).replace(h,"$&/")}var y,f,w=b,b=function(e){var t=this;if(t.instancePool.length){var s=t.instancePool.pop();return t.call(s,e),s}return new t(e)};function E(e,t,s,a){this.result=e,this.keyPrefix=t,this.func=s,this.context=a,this.count=0}function v(e,t,s){var n,o,i=e.result,l=e.keyPrefix,c=e.func,d=e.context,p=c.call(d,t,e.count++);Array.isArray(p)?_(p,i,s,r.thatReturnsArgument):null!=p&&(a.isValidElement(p)&&(n=p,o=l+(!p.key||t&&t.key===p.key?"":g(p.key)+"/")+s,p=a.cloneElement(n,{key:o},void 0!==n.props?n.props.children:void 0)),i.push(p))}function _(e,t,s,a,n){var r="";null!=s&&(r=g(s)+"/");var o=E.getPooled(t,r,a,n);!function(e,t,s){null==e||m(e,"",t,s)}(e,v,o),E.release(o)}E.prototype.destructor=function(){this.result=null,this.keyPrefix=null,this.func=null,this.context=null,this.count=0},y=function(e,t,s,a){var n=this;if(n.instancePool.length){var r=n.instancePool.pop();return n.call(r,e,t,s,a),r}return new n(e,t,s,a)},(f=E).instancePool=[],f.getPooled=y||w,f.poolSize||(f.poolSize=10),f.release=function(e){var t=this;o(e instanceof t,"Trying to release an instance into a pool of a different type."),e.destructor(),t.instancePool.length<t.poolSize&&t.instancePool.push(e)},e.exports=function(e){if("object"!=typeof e||!e||Array.isArray(e))return i(!1,"React.addons.createFragment only accepts a single object. Got: %s",e),e;if(a.isValidElement(e))return i(!1,"React.addons.createFragment does not accept a ReactElement without a wrapper object."),e;o(1!==e.nodeType,"React.addons.createFragment(...): Encountered an invalid child; DOM elements are not valid children of React components.");var t=[];for(var s in e)_(e[s],t,s,r.thatReturnsArgument);return t}},7942:e=>{"use strict";function t(e){return function(){return e}}var s=function(){};s.thatReturns=t,s.thatReturnsFalse=t(!1),s.thatReturnsTrue=t(!0),s.thatReturnsNull=t(null),s.thatReturnsThis=function(){return this},s.thatReturnsArgument=function(e){return e},e.exports=s},9179:e=>{"use strict";e.exports=function(e,t,s,a,n,r,o,i){if(!e){var l;if(void 0===t)l=new Error("Minified exception occurred; use the non-minified dev environment for the full error message and additional helpful warnings.");else{var c=[s,a,n,r,o,i],d=0;(l=new Error(t.replace(/%s/g,(function(){return c[d++]})))).name="Invariant Violation"}throw l.framesToPop=1,l}}},397:(e,t,s)=>{"use strict";var a=s(7942);e.exports=a},4530:(e,t)=>{var s;!function(){"use strict";var a={}.hasOwnProperty;function n(){for(var e=[],t=0;t<arguments.length;t++){var s=arguments[t];if(s){var r=typeof s;if("string"===r||"number"===r)e.push(s);else if(Array.isArray(s)){if(s.length){var o=n.apply(null,s);o&&e.push(o)}}else if("object"===r){if(s.toString!==Object.prototype.toString&&!s.toString.toString().includes("[native code]")){e.push(s.toString());continue}for(var i in s)a.call(s,i)&&s[i]&&e.push(i)}}}return e.join(" ")}e.exports?(n.default=n,e.exports=n):void 0===(s=function(){return n}.apply(t,[]))||(e.exports=s)}()},9196:e=>{"use strict";e.exports=window.React}},t={};function s(a){var n=t[a];if(void 0!==n)return n.exports;var r=t[a]={exports:{}};return e[a](r,r.exports,s),r.exports}s.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return s.d(t,{a:t}),t},s.d=(e,t)=>{for(var a in t)s.o(t,a)&&!s.o(e,a)&&Object.defineProperty(e,a,{enumerable:!0,get:t[a]})},s.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),s.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},(()=>{"use strict";var e={};s.r(e),s.d(e,{DISMISS_ALERT:()=>he,SNIPPET_EDITOR_FIND_CUSTOM_FIELDS:()=>ge,wistiaEmbedPermission:()=>ye});var t={};s.r(t),s.d(t,{loadSnippetEditorData:()=>ve,updateData:()=>Ee});var a={};s.r(a),s.d(a,{getAnalysisData:()=>We});var n={};s.r(n),s.d(n,{authorFirstName:()=>Ge,authorLastName:()=>Ze,category:()=>et,categoryTitle:()=>tt,currentDate:()=>Qe,currentDay:()=>Xe,currentMonth:()=>Je,currentYear:()=>st,date:()=>at,excerpt:()=>nt,focusKeyphrase:()=>rt,id:()=>ot,modified:()=>it,name:()=>lt,page:()=>ct,pageNumber:()=>dt,pageTotal:()=>pt,permalink:()=>ut,postContent:()=>mt,postDay:()=>ht,postMonth:()=>gt,postTypeNamePlural:()=>ft,postTypeNameSingular:()=>wt,postYear:()=>yt,primaryCategory:()=>bt,searchPhrase:()=>Et,separator:()=>vt,siteDescription:()=>_t,siteName:()=>kt,tag:()=>xt,term404:()=>St,termDescription:()=>Tt,termHierarchy:()=>Rt,termTitle:()=>Ct,title:()=>It,userDescription:()=>Lt});const r=window.wp.data,o=window.wp.hooks;var i=s(9196),l=s.n(i);const c=window.wp.components,d=window.wp.element,p=window.wp.i18n,u=window.yoast.uiLibrary,m=window.yoast.propTypes;var h=s.n(m);h().string.isRequired;const g=i.forwardRef((function(e,t){return i.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:2,stroke:"currentColor","aria-hidden":"true",ref:t},e),i.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M8 11V7a4 4 0 118 0m-4 8v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2z"}))})),y=i.forwardRef((function(e,t){return i.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true",ref:t},e),i.createElement("path",{fillRule:"evenodd",d:"M12.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-2.293-2.293a1 1 0 010-1.414z",clipRule:"evenodd"}))})),f=({learnMoreLink:e,thumbnail:t,wistiaEmbedPermission:s,upsellLink:a,isProductCopy:n,title:r,upsellLabel:o,newToText:l,bundleNote:c})=>{const{onClose:m,initialFocus:h}=(0,u.useModalContext)(),f={a:(0,i.createElement)(v,{href:e,className:"yst-inline-flex yst-items-center yst-gap-1 yst-no-underline yst-font-medium",variant:"primary"}),ArrowNarrowRightIcon:(0,i.createElement)(y,{className:"yst-w-4 yst-h-4 rtl:yst-rotate-180"})};return(0,i.createElement)("div",{className:"yst-flex yst-flex-col yst-items-center yst-p-10"},(0,i.createElement)("div",{className:"yst-relative yst-w-full"},(0,i.createElement)(j,{videoId:"vmrahpfjxp",thumbnail:t,wistiaEmbedPermission:s}),(0,i.createElement)(u.Badge,{className:"yst-absolute yst-top-0 yst-right-2 yst-mt-2 yst-ml-2",variant:"info"},"Beta")),(0,i.createElement)("div",{className:"yst-mt-6 yst-text-xs yst-font-medium"},(0,i.createElement)("span",{className:"yst-introduction-modal-uppercase"},l)),(0,i.createElement)("div",{className:"yst-mt-4 yst-mx-1.5 yst-text-center"},(0,i.createElement)("h3",{className:"yst-text-slate-900 yst-text-lg yst-font-medium"},r),(0,i.createElement)("div",{className:"yst-mt-2 yst-text-slate-600 yst-text-sm"},n?(0,d.createInterpolateElement)((0,p.sprintf)(/* translators: %1$s and %2$s are anchor tags; %3$s is the arrow icon. */
(0,p.__)("Let AI do some of the thinking for you and help you save time. Get high-quality suggestions for product titles and meta descriptions to make your content rank high and look good on social media. %1$sLearn more%2$s%3$s","wordpress-seo"),"<a>","<ArrowNarrowRightIcon />","</a>"),f):(0,d.createInterpolateElement)((0,p.sprintf)(/* translators: %1$s and %2$s are anchor tags; %3$s is the arrow icon. */
(0,p.__)("Let AI do some of the thinking for you and help you save time. Get high-quality suggestions for titles and meta descriptions to make your content rank high and look good on social media. %1$sLearn more%2$s%3$s","wordpress-seo"),"<a>","<ArrowNarrowRightIcon />","</a>"),f))),(0,i.createElement)("div",{className:"yst-w-full yst-flex yst-mt-10"},(0,i.createElement)(u.Button,{as:"a",className:"yst-grow",size:"extra-large",variant:"upsell",href:a,target:"_blank",ref:h},(0,i.createElement)(g,{className:"yst--ml-1 yst-mr-2 yst-h-5 yst-w-5"}),o,(0,i.createElement)("span",{className:"yst-sr-only"},/* translators: Hidden accessibility text. */
(0,p.__)("(Opens in a new browser tab)","wordpress-seo")))),c,(0,i.createElement)(u.Button,{as:"a",className:"yst-mt-4",variant:"tertiary",onClick:m},(0,p.__)("Close","wordpress-seo")))};f.propTypes={learnMoreLink:h().string.isRequired,upsellLink:h().string.isRequired,thumbnail:h().shape({src:h().string.isRequired,width:h().string,height:h().string}).isRequired,wistiaEmbedPermission:h().shape({value:h().bool.isRequired,status:h().string.isRequired,set:h().func.isRequired}).isRequired,title:h().string,upsellLabel:h().string,newToText:h().string,isProductCopy:h().bool,bundleNote:h().oneOfType([h().string,h().element])},f.defaultProps={title:(0,p.__)("Use AI to write your titles & meta descriptions!","wordpress-seo"),upsellLabel:(0,p.sprintf)(/* translators: %1$s expands to Yoast SEO Premium. */
(0,p.__)("Unlock with %1$s","wordpress-seo"),"Yoast SEO Premium"),newToText:(0,p.sprintf)(/* translators: %1$s expands to Yoast SEO Premium. */
(0,p.__)("New in %1$s","wordpress-seo"),"Yoast SEO Premium"),isProductCopy:!1,bundleNote:""};var w;function b(){return b=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var s=arguments[t];for(var a in s)Object.prototype.hasOwnProperty.call(s,a)&&(e[a]=s[a])}return e},b.apply(this,arguments)}h().string,h().node.isRequired,h().node.isRequired,h().node,h().oneOf(Object.keys({lg:{grid:"yst-grid lg:yst-grid-cols-3 lg:yst-gap-12",col1:"yst-col-span-1",col2:"lg:yst-mt-0 lg:yst-col-span-2"},xl:{grid:"yst-grid xl:yst-grid-cols-3 xl:yst-gap-12",col1:"yst-col-span-1",col2:"xl:yst-mt-0 xl:yst-col-span-2"},"2xl":{grid:"yst-grid 2xl:yst-grid-cols-3 2xl:yst-gap-12",col1:"yst-col-span-1",col2:"2xl:yst-mt-0 2xl:yst-col-span-2"}}));const E=e=>i.createElement("svg",b({xmlns:"http://www.w3.org/2000/svg","aria-hidden":"true",viewBox:"0 0 1000 1000"},e),w||(w=i.createElement("path",{fill:"#fff",d:"M500 0C223.9 0 0 223.9 0 500s223.9 500 500 500 500-223.9 500-500S776.1 0 500 0Zm87.2 412.4c0-21.9 4.3-40.2 13.1-54.4s24-27.1 45.9-38.2l10.1-4.9c17.8-9 22.4-16.7 22.4-26 0-11.1-9.5-19.1-25-19.1-18.3 0-32.2 9.5-41.8 28.9l-24.7-24.8c5.4-11.6 14.1-20.9 25.8-28.1a70.8 70.8 0 0 1 38.9-11.1c17.8 0 33.3 4.6 45.9 14.2s19.4 22.7 19.4 39.4c0 26.6-15 42.9-43.1 57.3l-15.7 8c-16.8 8.5-25.1 16-27.4 29.4h85.4v35.4H587.2Zm-82.1 373.3c-157.8 0-285.7-127.9-285.7-285.7s127.9-285.7 285.7-285.7a286.4 286.4 0 0 1 55.9 5.5l-55.9 116.9c-90 0-163.3 73.3-163.3 163.3s73.3 163.3 163.3 163.3a162.8 162.8 0 0 0 106.4-39.6l61.8 107.2a283.9 283.9 0 0 1-168.2 54.8ZM705 704.1l-70.7-122.5H492.9l70.7-122.4H705l70.7 122.4Z"}))),v=({href:e,children:t,...s})=>(0,i.createElement)(u.Link,{target:"_blank",rel:"noopener noreferrer",...s,href:e},t,(0,i.createElement)("span",{className:"yst-sr-only"},/* translators: Hidden accessibility text. */
(0,p.__)("(Opens in a new browser tab)","wordpress-seo")));v.propTypes={href:h().string.isRequired,children:h().node},v.defaultProps={children:null};const _=i.forwardRef((function(e,t){return i.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:2,stroke:"currentColor","aria-hidden":"true",ref:t},e),i.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M17 8l4 4m0 0l-4 4m4-4H3"}))}));var k,x,S;function T(){return T=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var s=arguments[t];for(var a in s)Object.prototype.hasOwnProperty.call(s,a)&&(e[a]=s[a])}return e},T.apply(this,arguments)}const R=e=>i.createElement("svg",T({xmlns:"http://www.w3.org/2000/svg",id:"star-rating-half_svg__Layer_1","data-name":"Layer 1",viewBox:"0 0 500 475.53"},e),k||(k=i.createElement("defs",null,i.createElement("style",null,".star-rating-half_svg__cls-1{fill:#fbbf24}"))),x||(x=i.createElement("path",{d:"M250 392.04 98.15 471.87l29-169.09L4.3 183.03l169.77-24.67L250 4.52l75.93 153.84 169.77 24.67-122.85 119.75 29 169.09L250 392.04z",className:"star-rating-half_svg__cls-1"})),S||(S=i.createElement("path",{d:"m250 9.04 73.67 149.27.93 1.88 2.08.3 164.72 23.94-119.19 116.19-1.51 1.47.36 2.07 28.14 164.06-147.34-77.46-1.86-1-1.86 1-147.34 77.46 28.14-164.06.36-2.07-1.51-1.47L8.6 184.43l164.72-23.9 2.08-.3.93-1.88L250 9.04m0-9-77.25 156.49L0 181.64l125 121.89-29.51 172L250 394.3l154.51 81.23-29.51-172 125-121.89-172.75-25.11L250 0Z",className:"star-rating-half_svg__cls-1"})),i.createElement("path",{d:"m500 181.64-172.75-25.11L250 0v394.3l154.51 81.23L375 303.48l125-121.84z",style:{fill:"#f3f4f6"}}));function C(){return C=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var s=arguments[t];for(var a in s)Object.prototype.hasOwnProperty.call(s,a)&&(e[a]=s[a])}return e},C.apply(this,arguments)}const I=e=>i.createElement("svg",C({xmlns:"http://www.w3.org/2000/svg","data-name":"Layer 1",viewBox:"0 0 500 475.53"},e),i.createElement("path",{d:"m250 0 77.25 156.53L500 181.64 375 303.48l29.51 172.05L250 394.3 95.49 475.53 125 303.48 0 181.64l172.75-25.11L250 0z",style:{fill:"#fbbf24"}}));var L,P,A,O,M,D,F,N,q;function U(){return U=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var s=arguments[t];for(var a in s)Object.prototype.hasOwnProperty.call(s,a)&&(e[a]=s[a])}return e},U.apply(this,arguments)}const B=e=>i.createElement("svg",U({xmlns:"http://www.w3.org/2000/svg","aria-hidden":"true",viewBox:"0 0 500 500"},e),L||(L=i.createElement("path",{fill:"#a4286a",d:"M80 0h340a80 80 0 0 1 80 80v420H80a80 80 0 0 1-80-80V80A80 80 0 0 1 80 0z"})),P||(P=i.createElement("path",{fill:"#6c2548",d:"M437.61 2 155.89 500H500V80a80 80 0 0 0-62.39-78z"})),A||(A=i.createElement("path",{fill:"#fff",d:"M74.4 337.3v34.9c21.6-.9 38.5-8 52.8-22.5s27.4-38 39.9-72.9l92.6-248h-44.8L140.3 236l-37-116.2h-41l54.4 139.8a57.54 57.54 0 0 1 0 41.8c-5.5 14.2-15.4 30.9-42.3 35.9z"})),O||(O=i.createElement("circle",{cx:368.33,cy:124.68,r:97.34,fill:"#9fda4f",transform:"rotate(-45 368.335 124.68)"})),M||(M=i.createElement("path",{fill:"#77b227",d:"m416.2 39.93-95.74 169.51A97.34 97.34 0 1 0 416.2 39.93z"})),D||(D=i.createElement("path",{fill:"#fec228",d:"m294.78 254.75-.15-.08-.13-.07a63.6 63.6 0 0 0-62.56 110.76h.13a63.6 63.6 0 0 0 62.71-110.67z"})),F||(F=i.createElement("path",{fill:"#f49a00",d:"m294.5 254.59-62.56 110.76a63.6 63.6 0 1 0 62.56-110.76z"})),N||(N=i.createElement("path",{fill:"#ff4e47",d:"M222.31 450.07A38.16 38.16 0 0 0 203 416.83a38.18 38.18 0 1 0 19.41 33.27z"})),q||(q=i.createElement("path",{fill:"#ed261f",d:"m202.9 416.8-37.54 66.48a38.17 38.17 0 0 0 37.54-66.48z"}))),$=({link:e,linkProps:t,promotions:s})=>{const a=(0,d.useMemo)((()=>(0,p.__)("Use AI to generate titles and meta descriptions, automatically redirect deleted pages, get 24/7 support, and much, much more!","wordpress-seo")),[]),n=(0,d.createInterpolateElement)((0,p.sprintf)(/* translators: %1$s and %2$s expand to a span wrap to avoid linebreaks. %3$s expands to "Yoast SEO Premium". */
(0,p.__)("%1$sGet%2$s %3$s","wordpress-seo"),"<nowrap>","</nowrap>","Yoast SEO Premium"),{nowrap:(0,i.createElement)("span",{className:"yst-whitespace-nowrap"})}),r=s.includes("black-friday-2023-promotion"),o=(0,d.createInterpolateElement)((0,p.sprintf)(/* translators: %1$s and %2$s expand to strong tags. */
(0,p.__)("%1$sSAVE 30%%%2$s on your 12 month subscription","wordpress-seo"),"<strong>","</strong>"),{strong:(0,i.createElement)("strong",null)});return(0,i.createElement)("div",{className:"yst-p-6 yst-rounded-lg yst-text-white yst-bg-primary-500 yst-shadow"},(0,i.createElement)("figure",{className:"yst-logo-square yst-w-16 yst-h-16 yst-mx-auto yst-overflow-hidden yst-border yst-border-white yst-rounded-xl yst-rounded-br-none yst-relative yst-z-10 yst-mt-[-2.6rem]"},(0,i.createElement)(B,null)),r&&(0,i.createElement)("div",{className:"sidebar__sale_banner_container"},(0,i.createElement)("div",{className:"sidebar__sale_banner"},(0,i.createElement)("span",{className:"banner_text"},(0,p.__)("BLACK FRIDAY - 30% OFF","wordpress-seo")))),(0,i.createElement)(u.Title,{as:"h2",className:"yst-mt-6 yst-text-base yst-font-extrabold yst-text-white"},n),(0,i.createElement)("p",{className:"yst-mt-2"},a),r&&(0,i.createElement)("div",{className:"yst-text-center yst-border-t-[1px] yst-border-white yst-italic yst-mt-3"},(0,i.createElement)("p",{className:"yst-text-[10px] yst-my-3 yst-mx-0"},o)),(0,i.createElement)(u.Button,{as:"a",variant:"upsell",href:e,target:"_blank",rel:"noopener",className:"yst-flex yst-justify-center yst-gap-2 yst-mt-4 focus:yst-ring-offset-primary-500",...t},(0,i.createElement)("span",null,r?(0,p.__)("Claim your 30% off now!","wordpress-seo"):n),(0,i.createElement)(_,{className:"yst-w-4 yst-h-4 yst-icon-rtl"})),(0,i.createElement)("p",{className:"yst-text-center yst-text-xs yst-mx-2 yst-font-light yst-leading-5 yst-mt-2"},(0,p.__)("Only $/€/£99 per year (ex VAT)","wordpress-seo"),(0,i.createElement)("br",null),(0,p.__)("30-day money back guarantee.","wordpress-seo")),(0,i.createElement)("hr",{className:"yst-border-t yst-border-primary-300 yst-my-4"}),(0,i.createElement)("a",{className:"yst-block yst-mt-4 yst-no-underline",href:"https://www.g2.com/products/yoast-yoast/reviews",target:"_blank",rel:"noopener noreferrer"},(0,i.createElement)("span",{className:"yst-font-medium yst-text-white hover:yst-underline"},(0,p.__)("Read reviews from real users","wordpress-seo")),(0,i.createElement)("span",{className:"yst-flex yst-gap-2 yst-mt-2 yst-items-center"},(0,i.createElement)(E,{className:"yst-w-5 yst-h-5"}),(0,i.createElement)("span",{className:"yst-flex yst-gap-1"},(0,i.createElement)(I,{className:"yst-w-5 yst-h-5"}),(0,i.createElement)(I,{className:"yst-w-5 yst-h-5"}),(0,i.createElement)(I,{className:"yst-w-5 yst-h-5"}),(0,i.createElement)(I,{className:"yst-w-5 yst-h-5"}),(0,i.createElement)(R,{className:"yst-w-5 yst-h-5"})),(0,i.createElement)("span",{className:"yst-text-sm yst-font-semibold yst-text-white"},"4.6 / 5"))))};$.propTypes={link:h().string.isRequired,linkProps:h().object,promotions:h().array},$.defaultProps={linkProps:{},promotions:[]},h().node.isRequired;const W=window.yoast.reactHelmet,K="loading",H="showPlay",Y="askPermission",z="isPlaying",j=({videoId:e,thumbnail:t,wistiaEmbedPermission:s})=>{const[a,n]=(0,d.useState)(s.value?z:H),r=(0,d.useCallback)((()=>n(z)),[n]),o=(0,d.useCallback)((()=>{s.value?r():n(Y)}),[s.value,r,n]),l=(0,d.useCallback)((()=>n(H)),[n]),c=(0,d.useCallback)((()=>{s.set(!0),r()}),[s.set,r]);return(0,i.createElement)(i.Fragment,null,s.value&&(0,i.createElement)(W.Helmet,null,(0,i.createElement)("script",{src:"https://fast.wistia.com/assets/external/E-v1.js",async:!0})),(0,i.createElement)("div",{className:"yst-relative yst-w-full yst-h-0 yst-pt-[56.25%] yst-overflow-hidden yst-rounded-md yst-drop-shadow-md yst-bg-white"},a===H&&(0,i.createElement)("button",{type:"button",className:"yst-absolute yst-inset-0 yst-button yst-p-0 yst-border-none yst-bg-white yst-transition-opacity yst-duration-1000 yst-opacity-100",onClick:o},(0,i.createElement)("img",{className:"yst-w-full yst-h-auto",alt:"",loading:"lazy",decoding:"async",...t})),a===Y&&(0,i.createElement)("div",{className:"yst-absolute yst-inset-0 yst-flex yst-flex-col yst-items-center yst-justify-center yst-bg-white"},(0,i.createElement)("p",{className:"yst-max-w-xs yst-mx-auto yst-text-center"},s.status===K&&(0,i.createElement)(u.Spinner,null),s.status!==K&&(0,p.sprintf)(/* translators: %1$s expands to Yoast SEO. %2$s expands to Wistia. */
(0,p.__)("To see this video, you need to allow %1$s to load embedded videos from %2$s.","wordpress-seo"),"Yoast SEO","Wistia")),(0,i.createElement)("div",{className:"yst-flex yst-mt-6 yst-gap-x-4"},(0,i.createElement)(u.Button,{type:"button",variant:"secondary",onClick:l,disabled:s.status===K},(0,p.__)("Deny","wordpress-seo")),(0,i.createElement)(u.Button,{type:"button",variant:"primary",onClick:c,disabled:s.status===K},(0,p.__)("Allow","wordpress-seo")))),s.value&&a===z&&(0,i.createElement)("div",{className:"yst-absolute yst-w-full yst-h-full yst-top-0 yst-left-0"},null===e&&(0,i.createElement)(u.Spinner,{className:"yst-h-full yst-mx-auto"}),null!==e&&(0,i.createElement)("div",{className:`wistia_embed wistia_async_${e} videoFoam=true`}))))};j.propTypes={videoId:h().string.isRequired,thumbnail:h().shape({src:h().string.isRequired,width:h().string,height:h().string}).isRequired,wistiaEmbedPermission:h().shape({value:h().bool.isRequired,status:h().string.isRequired,set:h().func.isRequired}).isRequired};const V="yoast-seo/editor",G=()=>{const e=(0,r.useSelect)((e=>e(V).selectLink("https://yoa.st/ai-generator-learn-more")),[]),t=(0,r.useSelect)((e=>e(V).selectLink("https://yoa.st/ai-generator-upsell")),[]),s=(0,r.useSelect)((e=>e(V).selectLink("https://yoa.st/ai-generator-upsell-woo-seo-premium-bundle")),[]),a=(0,r.useSelect)((e=>e(V).selectLink("https://yoa.st/ai-generator-upsell-woo-seo")),[]),n=(0,r.useSelect)((e=>e(V).getIsPremium()),[]),o=(0,r.useSelect)((e=>e(V).getIsWooSeoUpsell()),[]),l=(0,r.useSelect)((e=>e(V).getIsProduct()),[]),c=!(!o&&(!l||o||n)),u={isProductCopy:c,upsellLink:t};if(c){const e=(0,p.sprintf)(/* translators: %1$s expands to Yoast SEO Premium, %2$s expands to Yoast WooCommerce SEO. */
(0,p.__)("%1$s + %2$s","wordpress-seo"),"Yoast SEO Premium","Yoast WooCommerce SEO");u.newToText=(0,p.sprintf)(/* translators: %1$s expands to Yoast SEO Premium and Yoast WooCommerce SEO. */
(0,p.__)("New in %1$s","wordpress-seo"),e),u.title=(0,p.__)("Generate product titles & descriptions with AI!","wordpress-seo"),!n&&o&&(u.upsellLabel=`${(0,p.sprintf)(/* translators: %1$s expands to Woo Premium bundle. */
(0,p.__)("Unlock with the %1$s","wordpress-seo"),"Woo Premium bundle")}*`,u.bundleNote=(0,i.createElement)("div",{className:"yst-text-xs yst-text-slate-500 yst-mt-2"},`*${e}`),u.upsellLink=s),n&&(u.upsellLabel=(0,p.sprintf)(/* translators: %1$s expands to Yoast WooCommerce SEO. */
(0,p.__)("Unlock with %1$s","wordpress-seo"),"Yoast WooCommerce SEO"),u.upsellLink=a)}const m=(0,r.useSelect)((e=>e(V).selectImageLink("ai-generator-preview.png")),[]),h=(0,d.useMemo)((()=>({src:m,width:"432",height:"244"})),[m]),g=(0,r.useSelect)((e=>e(V).selectWistiaEmbedPermissionValue()),[]),y=(0,r.useSelect)((e=>e(V).selectWistiaEmbedPermissionStatus()),[]),{setWistiaEmbedPermission:w}=(0,r.useDispatch)(V),b=(0,d.useMemo)((()=>({value:g,status:y,set:w})),[g,y,w]);return(0,i.createElement)(f,{learnMoreLink:e,thumbnail:h,wistiaEmbedPermission:b,...u})},Z=({fieldId:e})=>{const[t,,,s,a]=(0,u.useToggleState)(!1),n=(0,d.useCallback)((()=>{s()}),[s]),r=(0,d.useRef)(null);return(0,i.createElement)(i.Fragment,null,(0,i.createElement)("button",{type:"button",id:`yst-replacevar__use-ai-button__${e}`,className:"yst-replacevar__use-ai-button-upsell",onClick:n},(0,p.__)("Use AI","wordpress-seo")),(0,i.createElement)(u.Modal,{className:"yst-introduction-modal",isOpen:t,onClose:a,initialFocus:r},(0,i.createElement)(u.Modal.Panel,{className:"yst-max-w-lg yst-p-0 yst-rounded-3xl yst-introduction-modal-panel"},(0,i.createElement)(G,{onClose:a,focusElementRef:r}))))};Z.propTypes={fieldId:h().string.isRequired};const Q="yoast-seo/editor",X=window.yoast.externals.redux,J=window.lodash,ee=window.yoast.reduxJsToolkit,te=window.wp.url,se="linkParams",ae=(0,ee.createSlice)({name:se,initialState:{},reducers:{setLinkParams:(e,{payload:t})=>t}}),ne=(ae.getInitialState,{selectLinkParam:(e,t,s={})=>(0,J.get)(e,`${se}.${t}`,s),selectLinkParams:e=>(0,J.get)(e,se,{})});ne.selectLink=(0,ee.createSelector)([ne.selectLinkParams,(e,t)=>t,(e,t,s={})=>s],((e,t,s)=>(0,te.addQueryArgs)(t,{...e,...s}))),ae.actions,ae.reducer;const re=(0,ee.createSlice)({name:"notifications",initialState:{},reducers:{addNotification:{reducer:(e,{payload:t})=>{e[t.id]={id:t.id,variant:t.variant,size:t.size,title:t.title,description:t.description}},prepare:({id:e,variant:t="info",size:s="default",title:a,description:n})=>({payload:{id:e||(0,ee.nanoid)(),variant:t,size:s,title:a||"",description:n}})},removeNotification:(e,{payload:t})=>(0,J.omit)(e,t)}}),oe=(re.getInitialState,re.actions,re.reducer,"pluginUrl"),ie=(0,ee.createSlice)({name:oe,initialState:"",reducers:{setPluginUrl:(e,{payload:t})=>t}}),le=(ie.getInitialState,{selectPluginUrl:e=>(0,J.get)(e,oe,"")});le.selectImageLink=(0,ee.createSelector)([le.selectPluginUrl,(e,t,s="images")=>s,(e,t)=>t],((e,t,s)=>[(0,J.trimEnd)(e,"/"),(0,J.trim)(t,"/"),(0,J.trimStart)(s,"/")].join("/"))),ie.actions,ie.reducer;const ce=window.wp.apiFetch;var de=s.n(ce);const pe="wistiaEmbedPermission",ue=(0,ee.createSlice)({name:pe,initialState:{value:!1,status:"idle",error:{}},reducers:{setWistiaEmbedPermissionValue:(e,{payload:t})=>{e.value=Boolean(t)}},extraReducers:e=>{e.addCase(`${pe}/request`,(e=>{e.status=K})),e.addCase(`${pe}/success`,((e,{payload:t})=>{e.status="success",e.value=Boolean(t&&t.value)})),e.addCase(`${pe}/error`,((e,{payload:t})=>{e.status="error",e.value=Boolean(t&&t.value),e.error={code:(0,J.get)(t,"error.code",500),message:(0,J.get)(t,"error.message","Unknown")}}))}}),me=(ue.getInitialState,ue.actions,{[pe]:async({payload:e})=>de()({path:"/yoast/v1/wistia_embed_permission",method:"POST",data:{value:Boolean(e)}})});function he({alertKey:e}){return new Promise((t=>wpseoApi.post("alerts/dismiss",{key:e},(()=>t()))))}function ge({query:e,postId:t}){return new Promise((s=>{wpseoApi.get("meta/search",{query:e,post_id:t},(e=>{s(e.meta)}))}))}ue.reducer;const ye=me[pe];class fe{static get titleElement(){return document.getElementById(window.wpseoScriptData.isPost?"yoast_wpseo_title":"hidden_wpseo_title")}static get descriptionElement(){return document.getElementById(window.wpseoScriptData.isPost?"yoast_wpseo_metadesc":"hidden_wpseo_desc")}static get slugElement(){return document.getElementById("yoast_wpseo_slug")}static get title(){return fe.titleElement.value}static set title(e){fe.titleElement.value=e}static get description(){return fe.descriptionElement.value}static set description(e){fe.descriptionElement.value=e}static get slug(){return fe.slugElement.value}static set slug(e){fe.slugElement.value=e}}const{UPDATE_DATA:we,LOAD_SNIPPET_EDITOR_DATA:be}=X.actions;function Ee(e){if(e.hasOwnProperty("title")){let t=e.title;e.title===(0,J.get)(window,"wpseoScriptData.metabox.title_template","")&&(t=""),fe.title=t}if(e.hasOwnProperty("description")){let t=e.description;e.description===(0,J.get)(window,"wpseoScriptData.metabox.metadesc_template","")&&(t=""),fe.description=t}return e.hasOwnProperty("slug")&&(fe.slug=e.slug),{type:we,data:e}}const ve=()=>{const e=(0,J.get)(window,"wpseoScriptData.metabox.title_template",""),t=(0,J.get)(window,"wpseoScriptData.metabox.metadesc_template","");return{type:be,data:{title:fe.title||e,description:fe.description||t,slug:fe.slug},templates:{title:e,description:t}}},_e=window.yoast.helpers,ke="yoast-measurement-element";function xe(e){let t=document.getElementById(ke);return t||(t=function(){const e=document.createElement("div");return e.id=ke,e.style.position="absolute",e.style.left="-9999em",e.style.top=0,e.style.height=0,e.style.overflow="hidden",e.style.fontFamily="arial, sans-serif",e.style.fontSize="20px",e.style.fontWeight="400",document.body.appendChild(e),e}()),t.innerText=e,t.offsetWidth}class Se{constructor(e){this.refresh=e,this.loaded=!1,this.preloadThreshold=3e3,this.plugins={},this.modifications={},this._registerPlugin=this._registerPlugin.bind(this),this._ready=this._ready.bind(this),this._reloaded=this._reloaded.bind(this),this._registerModification=this._registerModification.bind(this),this._registerAssessment=this._registerAssessment.bind(this),this._applyModifications=this._applyModifications.bind(this),setTimeout(this._pollLoadingPlugins.bind(this),1500)}_registerPlugin(e,t){return(0,J.isString)(e)?(0,J.isUndefined)(t)||(0,J.isObject)(t)?!1===this._validateUniqueness(e)?(console.error("Failed to register plugin. Plugin with name "+e+" already exists"),!1):(this.plugins[e]=t,!0):(console.error("Failed to register plugin "+e+". Expected parameters `options` to be a object."),!1):(console.error("Failed to register plugin. Expected parameter `pluginName` to be a string."),!1)}_ready(e){return(0,J.isString)(e)?(0,J.isUndefined)(this.plugins[e])?(console.error("Failed to modify status for plugin "+e+". The plugin was not properly registered."),!1):(this.plugins[e].status="ready",!0):(console.error("Failed to modify status for plugin "+e+". Expected parameter `pluginName` to be a string."),!1)}_reloaded(e){return(0,J.isString)(e)?(0,J.isUndefined)(this.plugins[e])?(console.error("Failed to reload Content Analysis for plugin "+e+". The plugin was not properly registered."),!1):(this.refresh(),!0):(console.error("Failed to reload Content Analysis for "+e+". Expected parameter `pluginName` to be a string."),!1)}_registerModification(e,t,s,a){if(!(0,J.isString)(e))return console.error("Failed to register modification for plugin "+s+". Expected parameter `modification` to be a string."),!1;if(!(0,J.isFunction)(t))return console.error("Failed to register modification for plugin "+s+". Expected parameter `callable` to be a function."),!1;if(!(0,J.isString)(s))return console.error("Failed to register modification for plugin "+s+". Expected parameter `pluginName` to be a string."),!1;if(!1===this._validateOrigin(s))return console.error("Failed to register modification for plugin "+s+". The integration has not finished loading yet."),!1;const n={callable:t,origin:s,priority:(0,J.isNumber)(a)?a:10};return(0,J.isUndefined)(this.modifications[e])&&(this.modifications[e]=[]),this.modifications[e].push(n),!0}_registerAssessment(e,t,s,a){return(0,J.isString)(t)?(0,J.isObject)(s)?(0,J.isString)(a)?(t=a+"-"+t,e.addAssessment(t,s),!0):(console.error("Failed to register assessment for plugin "+a+". Expected parameter `pluginName` to be a string."),!1):(console.error("Failed to register assessment for plugin "+a+". Expected parameter `assessment` to be a function."),!1):(console.error("Failed to register test for plugin "+a+". Expected parameter `name` to be a string."),!1)}_applyModifications(e,t,s){let a=this.modifications[e];return!(0,J.isArray)(a)||a.length<1||(a=this._stripIllegalModifications(a),a.sort(((e,t)=>e.priority-t.priority)),(0,J.forEach)(a,(function(a){const n=a.callable(t,s);typeof n==typeof t?t=n:console.error("Modification with name "+e+" performed by plugin with name "+a.origin+" was ignored because the data that was returned by it was of a different type than the data we had passed it.")}))),t}_pollLoadingPlugins(e){e=(0,J.isUndefined)(e)?0:e,!0===this._allReady()?(this.loaded=!0,this.refresh()):e>=this.preloadThreshold?(this._pollTimeExceeded(),this.loaded=!0,this.refresh()):(e+=50,setTimeout(this._pollLoadingPlugins.bind(this,e),50))}_allReady(){return(0,J.reduce)(this.plugins,(function(e,t){return e&&"ready"===t.status}),!0)}_pollTimeExceeded(){(0,J.forEach)(this.plugins,(function(e,t){(0,J.isUndefined)(e.options)||"ready"===e.options.status||(console.error("Error: Plugin "+t+". did not finish loading in time."),delete this.plugins[t])}))}_stripIllegalModifications(e){return(0,J.forEach)(e,((t,s)=>{!1===this._validateOrigin(t.origin)&&delete e[s]})),e}_validateOrigin(e){return"ready"===this.plugins[e].status}_validateUniqueness(e){return(0,J.isUndefined)(this.plugins[e])}}let Te=null;const Re=()=>{if(null===Te){const e=(0,r.dispatch)("yoast-seo/editor").runAnalysis;Te=window.YoastSEO.app&&window.YoastSEO.app.pluggable?window.YoastSEO.app.pluggable:new Se(e)}return Te},Ce=e=>Re()._ready(e),Ie=e=>Re()._reloaded(e),Le=(e,t,s,a)=>Re()._registerModification(e,t,s,a),Pe=(e,t)=>Re()._registerPlugin(e,t),Ae=(e,t,s)=>Re().loaded?Re()._applyModifications(e,t,s):t,{getBaseUrlFromSettings:Oe,getContentLocale:Me,getEditorDataContent:De,getFocusKeyphrase:Fe,getSnippetEditorDescriptionWithTemplate:Ne,getSnippetEditorSlug:qe,getSnippetEditorTitleWithTemplate:Ue,getDateFromSettings:Be}=X.selectors,We=e=>{let t=Ue(e),s=Ne(e),a=qe(e);const n=Oe(e);return t=_e.strings.stripHTMLTags(Ae("data_page_title",t)),s=_e.strings.stripHTMLTags(Ae("data_meta_desc",s)),a=a.trim().replace(/\s+/g,"-"),{text:De(e),title:t,keyword:Fe(e),description:s,locale:Me(e),titleWidth:xe(t),slug:a,permalink:n+a,date:Be(e)}},Ke=window.yoast.analysis;function He(e,t){const{updateWordsToHighlight:s}=(0,r.dispatch)("yoast-seo/editor");e("morphology",new Ke.Paper("",{keyword:t})).then((({result:{keyphraseForms:e}})=>{s((0,J.uniq)((0,J.flatten)(e)))})).catch((()=>{s([])}))}const Ye=(0,J.debounce)(He,500);function ze(){return(0,J.get)(window,"wpseoScriptData.metabox",{intl:{},isRtl:!1})}var je=jQuery;function Ve(e,t,s,a,n){this._scriptUrl=a,this._options={usedKeywords:t.keyword_usage,usedKeywordsPostTypes:t.keyword_usage_post_types,searchUrl:t.search_url,postUrl:t.post_edit_url},this._keywordUsage=t.keyword_usage,this._usedKeywordsPostTypes=t.keyword_usage_post_types,this._postID=je("#post_ID, [name=tag_ID]").val(),this._taxonomy=je("[name=taxonomy]").val()||"",this._nonce=n,this._ajaxAction=e,this._refreshAnalysis=s,this._initialized=!1}Ve.prototype.init=function(){const{worker:e}=window.YoastSEO.analysis;this.requestKeywordUsage=(0,J.debounce)(this.requestKeywordUsage.bind(this),500),e.loadScript(this._scriptUrl).then((()=>{e.sendMessage("initialize",this._options,"used-keywords-assessment")})).then((()=>{this._initialized=!0,(0,J.isEqual)(this._options.usedKeywords,this._keywordUsage)?this._refreshAnalysis():e.sendMessage("updateKeywordUsage",this._keywordUsage,"used-keywords-assessment").then((()=>this._refreshAnalysis()))})).catch((e=>console.error(e)))},Ve.prototype.setKeyword=function(e){(0,J.has)(this._keywordUsage,e)||this.requestKeywordUsage(e)},Ve.prototype.requestKeywordUsage=function(e){je.post(ajaxurl,{action:this._ajaxAction,post_id:this._postID,keyword:e,taxonomy:this._taxonomy,nonce:this._nonce},this.updateKeywordUsage.bind(this,e),"json")},Ve.prototype.updateKeywordUsage=function(e,t){const{worker:s}=window.YoastSEO.analysis,a=t.keyword_usage,n=t.post_types;a&&(0,J.isArray)(a)&&(this._keywordUsage[e]=a,this._usedKeywordsPostTypes[e]=n,this._initialized&&s.sendMessage("updateKeywordUsage",{usedKeywords:this._keywordUsage,usedKeywordsPostTypes:this._usedKeywordsPostTypes},"used-keywords-assessment").then((()=>this._refreshAnalysis())))};const Ge={name:"author_first_name",label:"Author first name",placeholder:"%%author_first_name%%",aliases:[],getReplacement:function(){return(0,J.get)(window,"wpseoScriptData.analysis.plugins.replaceVars.replace_vars.author_first_name","")},regexp:new RegExp("%%author_first_name%%","g")},Ze={name:"author_last_name",label:"Author last name",placeholder:"%%author_last_name%%",aliases:[],getReplacement:function(){return(0,J.get)(window,"wpseoScriptData.analysis.plugins.replaceVars.replace_vars.author_last_name","")},regexp:new RegExp("%%author_last_name%%","g")},Qe={name:"currentdate",label:"Current date",placeholder:"%%currentdate%%",aliases:[],getReplacement:function(){return(0,J.get)(window,"wpseoScriptData.analysis.plugins.replaceVars.replace_vars.currentdate","")},regexp:new RegExp("%%currentdate%%","g")},Xe={name:"currentday",label:"Current day",placeholder:"%%currentday%%",aliases:[],getReplacement:function(){return(0,J.get)(window,"wpseoScriptData.analysis.plugins.replaceVars.replace_vars.currentday","")},regexp:new RegExp("%%currentday%%","g")},Je={name:"currentmonth",label:"Current month",placeholder:"%%currentmonth%%",aliases:[],getReplacement:function(){return(0,J.get)(window,"wpseoScriptData.analysis.plugins.replaceVars.replace_vars.currentmonth","")},regexp:new RegExp("%%currentmonth%%","g")},et={name:"category",label:"Category",placeholder:"%%category%%",aliases:[],getReplacement:function(){return(0,J.get)(window,"wpseoScriptData.analysis.plugins.replaceVars.replace_vars.category","")},regexp:new RegExp("%%category%%","g")},tt={name:"category_title",label:"Category Title",placeholder:"%%category_title%%",aliases:[],getReplacement:function(){return(0,J.get)(window,"wpseoScriptData.analysis.plugins.replaceVars.replace_vars.category_title","")},regexp:new RegExp("%%category_title%%","g")},st={name:"currentyear",label:"Current year",placeholder:"%%currentyear%%",aliases:[],getReplacement:function(){return(0,J.get)(window,"wpseoScriptData.analysis.plugins.replaceVars.replace_vars.currentyear","")},regexp:new RegExp("%%currentyear%%","g")},at={name:"date",label:"Date",placeholder:"%%date%%",aliases:[],getReplacement:function(){return(0,J.get)(window,"wpseoScriptData.analysis.plugins.replaceVars.replace_vars.date","")},regexp:new RegExp("%%date%%","g")},nt={name:"excerpt",label:"Excerpt",placeholder:"%%excerpt%%",aliases:[{name:"excerpt_only",label:"Excerpt only",placeholder:"%%excerpt_only%%"}],getReplacement:function(){return(0,r.select)("yoast-seo/editor").getEditorDataExcerptWithFallback()},regexp:new RegExp("%%excerpt%%|%%excerpt_only%%","g")},rt={name:"focuskw",label:"Focus keyphrase",placeholder:"%%focuskw%%",aliases:[],getReplacement:function(){return(0,r.select)("yoast-seo/editor").getFocusKeyphrase()},regexp:new RegExp("%%focuskw%%|%%keyword%%","g")},ot={name:"id",label:"ID",placeholder:"%%id%%",aliases:[],getReplacement:function(){return(0,J.get)(window,"wpseoScriptData.analysis.plugins.replaceVars.replace_vars.id","")},regexp:new RegExp("%%id%%","g")},it={name:"modified",label:"Modified",placeholder:"%%modified%%",aliases:[],getReplacement:function(){return(0,J.get)(window,"wpseoScriptData.analysis.plugins.replaceVars.replace_vars.modified","")},regexp:new RegExp("%%modified%%","g")},lt={name:"name",label:"Name",placeholder:"%%name%%",aliases:[],getReplacement:function(){return(0,J.get)(window,"wpseoScriptData.analysis.plugins.replaceVars.replace_vars.name","")},regexp:new RegExp("%%name%%","g")},ct={name:"page",label:"Page",placeholder:"%%page%%",aliases:[],getReplacement:function(){return(0,J.get)(window,"wpseoScriptData.analysis.plugins.replaceVars.replace_vars.page","")},regexp:new RegExp("%%page%%","g")},dt={name:"pagenumber",label:"Pagenumber",placeholder:"%%pagenumber%%",aliases:[],getReplacement:function(){return(0,J.get)(window,"wpseoScriptData.analysis.plugins.replaceVars.replace_vars.pagenumber","")},regexp:new RegExp("%%pagenumber%%","g")},pt={name:"pagetotal",label:"Pagetotal",placeholder:"%%pagetotal%%",aliases:[],getReplacement:function(){return(0,J.get)(window,"wpseoScriptData.analysis.plugins.replaceVars.replace_vars.pagetotal","")},regexp:new RegExp("%%pagetotal%%","g")},ut={name:"permalink",label:"Permalink",placeholder:"%%permalink%%",aliases:[],getReplacement:function(){return(0,J.get)(window,"wpseoScriptData.analysis.plugins.replaceVars.replace_vars.permalink","")},regexp:new RegExp("%%permalink%%","g")},mt={name:"post_content",label:"Post Content",placeholder:"%%post_content%%",aliases:[],getReplacement:function(){return(0,J.get)(window,"wpseoScriptData.analysis.plugins.replaceVars.replace_vars.post_content","")},regexp:new RegExp("%%post_content%%","g")},ht={name:"post_day",label:"Post Day",placeholder:"%%post_day%%",aliases:[],getReplacement:function(){return(0,J.get)(window,"wpseoScriptData.analysis.plugins.replaceVars.replace_vars.post_day","")},regexp:new RegExp("%%post_day%%","g")},gt={name:"post_month",label:"Post Month",placeholder:"%%post_month%%",aliases:[],getReplacement:function(){return(0,J.get)(window,"wpseoScriptData.analysis.plugins.replaceVars.replace_vars.post_month","")},regexp:new RegExp("%%post_month%%","g")},yt={name:"post_year",label:"Post Year",placeholder:"%%post_year%%",aliases:[],getReplacement:function(){return(0,J.get)(window,"wpseoScriptData.analysis.plugins.replaceVars.replace_vars.post_year","")},regexp:new RegExp("%%post_year%%","g")},ft={name:"pt_plural",label:"Post type (plural)",placeholder:"%%pt_plural%%",aliases:[],getReplacement:function(){return(0,J.get)(window,"wpseoScriptData.analysis.plugins.replaceVars.replace_vars.pt_plural","")},regexp:new RegExp("%%pt_plural%%","g")},wt={name:"pt_single",label:"Post type (singular)",placeholder:"%%pt_single%%",aliases:[],getReplacement:function(){return(0,J.get)(window,"wpseoScriptData.analysis.plugins.replaceVars.replace_vars.pt_single","")},regexp:new RegExp("%%pt_single%%","g")},bt={name:"primary_category",label:"Primary category",placeholder:"%%primary_category%%",aliases:[],getReplacement:function(){return(0,J.get)(window,"wpseoScriptData.analysis.plugins.replaceVars.replace_vars.primary_category","")},regexp:new RegExp("%%primary_category%%","g")},Et={name:"searchphrase",label:"Search phrase",placeholder:"%%searchphrase%%",aliases:[],getReplacement:function(){return(0,J.get)(window,"wpseoScriptData.analysis.plugins.replaceVars.replace_vars.searchphrase","")},regexp:new RegExp("%%searchphrase%%","g")},vt={name:"sep",label:"Separator",placeholder:"%%sep%%",aliases:[],getReplacement:function(){return(0,J.get)(window,"wpseoScriptData.analysis.plugins.replaceVars.replace_vars.sep","")},regexp:new RegExp("%%sep%%(\\s*%%sep%%)*","g")},_t={name:"sitedesc",label:"Tagline",placeholder:"%%sitedesc%%",aliases:[],getReplacement:function(){return(0,J.get)(window,"wpseoScriptData.analysis.plugins.replaceVars.replace_vars.sitedesc","")},regexp:new RegExp("%%sitedesc%%","g")},kt={name:"sitename",label:"Site title",placeholder:"%%sitename%%",aliases:[],getReplacement:function(){return(0,J.get)(window,"wpseoScriptData.analysis.plugins.replaceVars.replace_vars.sitename","")},regexp:new RegExp("%%sitename%%","g")},xt={name:"tag",label:"Tag",placeholder:"%%tag%%",aliases:[],getReplacement:function(){return(0,J.get)(window,"wpseoScriptData.analysis.plugins.replaceVars.replace_vars.tag","")},regexp:new RegExp("%%tag%%","g")},St={name:"term404",label:"Term404",placeholder:"%%term404%%",aliases:[],getReplacement:function(){return(0,J.get)(window,"wpseoScriptData.analysis.plugins.replaceVars.replace_vars.term404","")},regexp:new RegExp("%%term404%%","g")},Tt={name:"term_description",label:"Term description",placeholder:"%%term_description%%",aliases:[{name:"tag_description",label:"Tag description",placeholder:"%%tag_description%%"},{name:"category_description",label:"Category description",placeholder:"%%category_description%%"}],getReplacement:function(){return(0,J.get)(window,"YoastSEO.app.rawData.text","")},regexp:new RegExp("%%term_description%%|%%tag_description%%|%%category_description%%","g")},Rt={name:"term_hierarchy",label:"Term hierarchy",placeholder:"%%term_hierarchy%%",aliases:[],getReplacement:function(){return(0,J.get)(window,"wpseoScriptData.analysis.plugins.replaceVars.replace_vars.term_hierarchy","")},regexp:new RegExp("%%term_hierarchy%%","g")},Ct={name:"term_title",label:"Term title",placeholder:"%%term_title%%",aliases:[],getReplacement:function(){return(0,J.get)(window,"wpseoScriptData.analysis.plugins.replaceVars.replace_vars.term_title","")},regexp:new RegExp("%%term_title%%","g")},It={name:"title",label:"Title",placeholder:"%%title%%",aliases:[],getReplacement:function(){return(0,r.select)("yoast-seo/editor").getEditorDataTitle()},regexp:new RegExp("%%title%%","g")},Lt={name:"user_description",label:"User description",placeholder:"%%user_description%%",aliases:[],getReplacement:function(){return(0,J.get)(window,"wpseoScriptData.analysis.plugins.replaceVars.replace_vars.user_description","")},regexp:new RegExp("%%user_description%%","g")};var Pt={source:"wpseoScriptData.analysis.plugins.replaceVars",scope:[],aliases:[]},At=function(e,t,s){this.placeholder=e,this.replacement=t,this.options=(0,J.defaults)(s,Pt)};At.prototype.getPlaceholder=function(e){return(e=e||!1)&&this.hasAlias()?this.placeholder+"|"+this.getAliases().join("|"):this.placeholder},At.prototype.setSource=function(e){this.options.source=e},At.prototype.hasScope=function(){return!(0,J.isEmpty)(this.options.scope)},At.prototype.addScope=function(e){this.hasScope()||(this.options.scope=[]),this.options.scope.push(e)},At.prototype.inScope=function(e){return!this.hasScope()||(0,J.indexOf)(this.options.scope,e)>-1},At.prototype.hasAlias=function(){return!(0,J.isEmpty)(this.options.aliases)},At.prototype.addAlias=function(e){this.hasAlias()||(this.options.aliases=[]),this.options.aliases.push(e)},At.prototype.getAliases=function(){return this.options.aliases};const Ot=At,Mt="replaceVariablePlugin";let Dt=null,Ft=null;const Nt=e=>{["content","title","snippet_title","snippet_meta","primary_category","data_page_title","data_meta_desc","excerpt"].forEach((t=>{Le(t,e,Mt,10)}))},qt=(e="")=>{switch(""===e&&(e=(0,J.get)(window,"wpseoScriptData.analysis.plugins.replaceVars.scope","")),e){case"post":case"page":return["authorFirstName","authorLastName","category","categoryTitle","currentDate","currentDay","currentMonth","currentYear","date","excerpt","id","focusKeyphrase","modified","name","page","primaryCategory","pageNumber","pageTotal","permalink","postContent","postDay","postMonth","postYear","postTypeNamePlural","postTypeNameSingular","searchPhrase","separator","siteDescription","siteName","tag","title","userDescription"]}return[]},Ut=e=>Nt((t=>t.replace(new RegExp(e.placeholder,"g"),e.replacement))),Bt=()=>{if(null===Ft){Ft=[];const e=(0,J.get)(window,"wpseoScriptData.analysis.plugins.replaceVars.hidden_replace_vars",[]);(null===Dt&&(Dt=qt().map((e=>null==n?void 0:n[e])).filter(Boolean)),Dt).forEach((t=>{const s=e.includes(t.name);Ft.push({name:t.name,label:t.label,value:t.placeholder,hidden:s}),t.aliases.forEach((e=>{Ft.push({name:e.name,label:e.label,value:e.placeholder,hidden:s})}))}))}return Ft},$t=500;function Wt(){}function Kt(e){return e.sort(((e,t)=>e._identifier.localeCompare(t._identifier)))}function Ht(){const e=ze();return(0,J.get)(e,"contentLocale","en_US")}function Yt(){const e=ze();return!0===(0,J.get)(e,"contentAnalysisActive",!1)}function zt(){const e=ze();return!0===(0,J.get)(e,"keywordAnalysisActive",!1)}function jt(){const e=ze();return!0===(0,J.get)(e,"inclusiveLanguageAnalysisActive",!1)}const Vt=window.yoast.featureFlag;const Gt="yoastmark";function Zt(e,t){return e._properties.position.startOffset>t.length||e._properties.position.endOffset>t.length}function Qt(e,t,s){const a=e.dom;let n=e.getContent();if(n=Ke.markers.removeMarks(n),(0,J.isEmpty)(s))return void e.setContent(n);n=s[0].hasPosition()?function(e,t){if(!t)return"";for(let s=(e=(0,J.orderBy)(e,(e=>e._properties.position.startOffset),["asc"])).length-1;s>=0;s--){const a=e[s];Zt(a,t)||(t=a.applyWithPosition(t))}return t}(s,n):function(e,t,s,a){const{fieldsToMark:n,selectedHTML:r}=Ke.languageProcessing.getFieldsToMark(s,a);return(0,J.forEach)(s,(function(t){"acf_content"!==e.id&&(t._properties.marked=Ke.languageProcessing.normalizeHTML(t._properties.marked),t._properties.original=Ke.languageProcessing.normalizeHTML(t._properties.original)),n.length>0?r.forEach((e=>{const s=t.applyWithReplace(e);a=a.replace(e,s)})):a=t.applyWithReplace(a)})),a}(e,0,s,n),e.setContent(n),function(e){let t=e.getContent();t=t.replace(new RegExp("&lt;yoastmark.+?&gt;","g"),"").replace(new RegExp("&lt;/yoastmark&gt;","g"),""),e.setContent(t)}(e);const r=a.select(Gt);(0,J.forEach)(r,(function(e){e.setAttribute("data-mce-bogus","1")}))}function Xt(e){return window.test=e,Qt.bind(null,e)}J.noop,J.noop,J.noop;const Jt="content";function es(e){if("undefined"==typeof tinyMCE||void 0===tinyMCE.editors||0===tinyMCE.editors.length)return!1;const t=tinyMCE.get(e);return null!==t&&!t.isHidden()}window.wp.annotations;const ts=function(e){return(0,J.uniq)((0,J.flatten)(e.map((e=>{if(!(0,J.isUndefined)(e.getFieldsToMark()))return e.getFieldsToMark()}))))},ss=window.wp.richText,as=/(<([a-z]|\/)[^<>]+>)/gi,{htmlEntitiesRegex:ns}=Ke.helpers.htmlEntities,rs=e=>{let t=0;return(0,J.forEachRight)(e,(e=>{const[s]=e;let a=s.length;/^<\/?br/.test(s)&&(a-=1),t+=a})),t},os="<yoastmark class='yoast-text-mark'>",is="</yoastmark>",ls='<yoastmark class="yoast-text-mark">';function cs(e,t,s,a,n){const r=a.clientId,o=(0,ss.create)({html:e,multilineTag:s.multilineTag,multilineWrapperTag:s.multilineWrapperTag}).text;return(0,J.flatMap)(n,(s=>{let n;return n=s.hasBlockPosition&&s.hasBlockPosition()?function(e,t,s,a,n){if(t===e.getBlockClientId()){let t=e.getBlockPositionStart(),r=e.getBlockPositionEnd();if(e.isMarkForFirstBlockSection()){const e=((e,t,s)=>{const a="yoast/faq-block"===s?'<strong class="schema-faq-question">':'<strong class="schema-how-to-step-name">';return{blockStartOffset:e-=a.length,blockEndOffset:t-=a.length}})(t,r,s);t=e.blockStartOffset,r=e.blockEndOffset}if(a.slice(t,r)===n.slice(t,r))return[{startOffset:t,endOffset:r}];const o=((e,t,s)=>{const a=s.slice(0,e),n=s.slice(0,t),r=((e,t,s,a)=>{const n=[...e.matchAll(as)];s-=rs(n);const r=[...t.matchAll(as)];return{blockStartOffset:s,blockEndOffset:a-=rs(r)}})(a,n,e,t),o=((e,t,s,a)=>{let n=[...e.matchAll(ns)];return(0,J.forEachRight)(n,(e=>{const[,t]=e;s-=t.length})),n=[...t.matchAll(ns)],(0,J.forEachRight)(n,(e=>{const[,t]=e;a-=t.length})),{blockStartOffset:s,blockEndOffset:a}})(a,n,e=r.blockStartOffset,t=r.blockEndOffset);return{blockStartOffset:e=o.blockStartOffset,blockEndOffset:t=o.blockEndOffset}})(t,r,a);return[{startOffset:o.blockStartOffset,endOffset:o.blockEndOffset}]}return[]}(s,r,a.name,e,o):function(e,t){const s=t.getOriginal().replace(/(<([^>]+)>)/gi,""),a=t.getMarked().replace(/(<(?!\/?yoastmark)[^>]+>)/gi,""),n=function(e,t,s=!0){const a=[];if(0===e.length)return a;let n,r=0;for(s||(t=t.toLowerCase(),e=e.toLowerCase());(n=e.indexOf(t,r))>-1;)a.push(n),r=n+t.length;return a}(e,s);if(0===n.length)return[];const r=function(e){let t=e.indexOf(os);const s=t>=0;s||(t=e.indexOf(ls));let a=null;const n=[];for(;t>=0;){if(a=(e=s?e.replace(os,""):e.replace(ls,"")).indexOf(is),a<t)return[];e=e.replace(is,""),n.push({startOffset:t,endOffset:a}),t=s?e.indexOf(os):e.indexOf(ls),a=null}return n}(a),o=[];return r.forEach((e=>{n.forEach((a=>{const n=a+e.startOffset;let r=a+e.endOffset;0===e.startOffset&&e.endOffset===t.getOriginal().length&&(r=a+s.length),o.push({startOffset:n,endOffset:r})}))})),o}(o,s),n?n.map((e=>({...e,block:r,richTextIdentifier:t}))):[]}))}const ds=e=>e[0].toUpperCase()+e.slice(1),ps=(e,t,s,a,n)=>(e=e.map((e=>{const r=`${e.id}-${n[0]}`,o=`${e.id}-${n[1]}`,i=ds(n[0]),l=ds(n[1]),c=e[`json${i}`],d=e[`json${l}`],{marksForFirstSection:p,marksForSecondSection:u}=((e,t)=>({marksForFirstSection:e.filter((e=>e.hasBlockPosition&&e.hasBlockPosition()?e.getBlockAttributeId()===t.id&&e.isMarkForFirstBlockSection():e)),marksForSecondSection:e.filter((e=>e.hasBlockPosition&&e.hasBlockPosition()?e.getBlockAttributeId()===t.id&&!e.isMarkForFirstBlockSection():e))}))(t,e),m=cs(c,r,s,a,p),h=cs(d,o,s,a,u);return m.concat(h)})),(0,J.flattenDeep)(e)),us="yoast";let ms=[];const hs={"core/paragraph":[{key:"content"}],"core/list":[{key:"values",multilineTag:"li",multilineWrapperTag:["ul","ol"]}],"core/list-item":[{key:"content"}],"core/heading":[{key:"content"}],"core/audio":[{key:"caption"}],"core/embed":[{key:"caption"}],"core/gallery":[{key:"caption"}],"core/image":[{key:"caption"}],"core/table":[{key:"caption"}],"core/video":[{key:"caption"}],"yoast/faq-block":[{key:"questions"}],"yoast/how-to-block":[{key:"steps"},{key:"jsonDescription"}]};function gs(){const e=ms.shift();e&&((0,r.dispatch)("core/annotations").__experimentalAddAnnotation(e),ys())}function ys(){(0,J.isFunction)(window.requestIdleCallback)?window.requestIdleCallback(gs,{timeout:1e3}):setTimeout(gs,150)}const fs=(e,t)=>{return(0,J.flatMap)((s=e.name,hs.hasOwnProperty(s)?hs[s]:[]),(s=>"yoast/faq-block"===e.name?((e,t,s)=>{const a=t.attributes[e.key];return 0===a.length?[]:ps(a,s,e,t,["question","answer"])})(s,e,t):"yoast/how-to-block"===e.name?((e,t,s)=>{const a=t.attributes[e.key];if(a&&0===a.length)return[];const n=[];return"steps"===e.key&&n.push(ps(a,s,e,t,["name","text"])),"jsonDescription"===e.key&&(s=s.filter((e=>e.hasBlockPosition&&e.hasBlockPosition()?!e.getBlockAttributeId():e)),n.push(cs(a,"description",e,t,s))),(0,J.flattenDeep)(n)})(s,e,t):function(e,t,s){const a=e.key,n=((e,t)=>{const s=e.attributes[t];return"string"==typeof s?s:s.toString()})(t,a);return cs(n,a,e,t,s)}(s,e,t)));var s};function ws(e,t){return(0,J.flatMap)(e,(e=>{const s=function(e){return e.innerBlocks.length>0}(e)?ws(e.innerBlocks,t):[];return fs(e,t).concat(s)}))}function bs(e){ms=[],(0,r.dispatch)("core/annotations").__experimentalRemoveAnnotationsBySource(us);const t=ts(e);if(0===e.length)return;let s=(0,r.select)("core/block-editor").getBlocks();var a;t.length>0&&(s=s.filter((e=>t.some((t=>"core/"+t===e.name))))),a=ws(s,e),ms=a.map((e=>({blockClientId:e.block,source:us,richTextIdentifier:e.richTextIdentifier,range:{start:e.startOffset,end:e.endOffset}}))),ys()}function Es(e,t){let s;es(Jt)&&((0,J.isUndefined)(s)&&(s=Xt(tinyMCE.get(Jt))),s(e,t)),(0,r.select)("core/block-editor")&&(0,J.isFunction)((0,r.select)("core/block-editor").getBlocks)&&(0,r.select)("core/annotations")&&(0,J.isFunction)((0,r.dispatch)("core/annotations").__experimentalAddAnnotation)&&(function(e,t){tinyMCE.editors.map((e=>Xt(e))).forEach((s=>s(e,t)))}(e,t),bs(t)),(0,o.doAction)("yoast.analysis.applyMarks",t)}function vs(){const e=(0,r.select)("yoast-seo/editor").isMarkingAvailable(),t=(0,r.select)("yoast-seo/editor").getMarkerPauseStatus();return!e||t?J.noop:Es}const _s=(0,J.debounce)((async function(e,t){const{text:s,...a}=t,n=new Ke.Paper(s,a);try{const t=await e.analyze(n),{seo:s,readability:a,inclusiveLanguage:i}=t.result;if(s){const e=s[""];e.results.forEach((e=>{e.getMarker=()=>()=>window.YoastSEO.analysis.applyMarks(n,e.marks)})),e.results=Kt(e.results),(0,r.dispatch)("yoast-seo/editor").setSeoResultsForKeyword(n.getKeyword(),e.results),(0,r.dispatch)("yoast-seo/editor").setOverallSeoScore(e.score,n.getKeyword())}a&&(a.results.forEach((e=>{e.getMarker=()=>()=>window.YoastSEO.analysis.applyMarks(n,e.marks)})),a.results=Kt(a.results),(0,r.dispatch)("yoast-seo/editor").setReadabilityResults(a.results),(0,r.dispatch)("yoast-seo/editor").setOverallReadabilityScore(a.score)),i&&(i.results.forEach((e=>{e.getMarker=()=>()=>window.YoastSEO.analysis.applyMarks(n,e.marks)})),i.results=Kt(i.results),(0,r.dispatch)("yoast-seo/editor").setInclusiveLanguageResults(i.results),(0,r.dispatch)("yoast-seo/editor").setOverallInclusiveLanguageScore(i.score)),(0,o.doAction)("yoast.analysis.run",t,{paper:n})}catch(e){}}),$t);function ks(){const{getAnalysisData:e,getEditorDataTitle:t}=(0,r.select)("yoast-seo/editor");let s=e();s={...s,textTitle:t()};const a=function(e){return e.title=Ae("data_page_title",e.title),e.title=Ae("title",e.title),e.description=Ae("data_meta_desc",e.description),e.text=Ae("content",e.text),e}(s);return(0,o.applyFilters)("yoast.analysis.data",a)}function xs(){const{getAnalysisTimestamp:e,isCornerstoneContent:t}=(0,r.select)("yoast-seo/editor"),s=function(){const e=(0,J.get)(window,["wpseoScriptData","analysis","worker","url"],"analysis-worker.js"),t=(0,Ke.createWorker)(e),s=(0,J.get)(window,["wpseoScriptData","analysis","worker","dependencies"],[]),a=[];for(const e in s){if(!Object.prototype.hasOwnProperty.call(s,e))continue;const t=window.document.getElementById(`${e}-js-translations`);if(!t)continue;const n=t.innerHTML.slice(214),r=n.indexOf(","),o=n.slice(0,r-1);try{const e=JSON.parse(n.slice(r+1,-4));a.push([o,e])}catch(t){console.warn(`Failed to parse translation data for ${e} to send to the Yoast SEO worker`);continue}}return t.postMessage({dependencies:s,translations:a}),new Ke.AnalysisWorkerWrapper(t)}();s.initialize(function(e={}){let t={locale:Ht(),contentAnalysisActive:Yt(),keywordAnalysisActive:zt(),inclusiveLanguageAnalysisActive:jt(),defaultQueryParams:(0,J.get)(window,["wpseoAdminL10n","default_query_params"],{}),logLevel:(0,J.get)(window,["wpseoScriptData","analysis","worker","log_level"],"ERROR"),enabledFeatures:(0,Vt.enabledFeatures)()};t=(0,J.merge)(t,e);const s=function(){const e=ze();return(0,J.get)(e,"translations",{domain:"wordpress-seo",locale_data:{"wordpress-seo":{"":{}}}})}();return(0,J.isUndefined)(s)||(0,J.isUndefined)(s.domain)||(t.translations=s),t}({useCornerstone:t(),marker:vs()})).catch(Wt),window.YoastSEO.analysis.applyMarks=(e,t)=>vs()(e,t);let a=ks(),n=t(),o=e();return(0,r.subscribe)((()=>{const r=t(),i=ks(),l=e();if(r!==n)return n=r,a=i,void s.initialize({useCornerstone:r}).then((()=>_s(s,i))).catch(Wt);l===o&&!1!==(0,J.isEqual)(i,a)||(a=i,o=l,_s(s,i))})),s}const Ss=window.wp.domReady;var Ts=s.n(Ss);const Rs=window.yoast.externals.contexts,Cs=({handleRefreshClick:e,supportLink:t})=>(0,i.createElement)("div",{className:"yst-flex yst-gap-2"},(0,i.createElement)(u.Button,{onClick:e},(0,p.__)("Refresh this page","wordpress-seo")),(0,i.createElement)(u.Button,{variant:"secondary",as:"a",href:t,target:"_blank",rel:"noopener"},(0,p.__)("Contact support","wordpress-seo")));Cs.propTypes={handleRefreshClick:h().func.isRequired,supportLink:h().string.isRequired};const Is=({handleRefreshClick:e,supportLink:t})=>(0,i.createElement)("div",{className:"yst-grid yst-grid-cols-1 yst-gap-y-2"},(0,i.createElement)(u.Button,{className:"yst-order-last",onClick:e},(0,p.__)("Refresh this page","wordpress-seo")),(0,i.createElement)(u.Button,{variant:"secondary",as:"a",href:t,target:"_blank",rel:"noopener"},(0,p.__)("Contact support","wordpress-seo")));Is.propTypes={handleRefreshClick:h().func.isRequired,supportLink:h().string.isRequired};const Ls=({error:e,children:t})=>(0,i.createElement)("div",{role:"alert",className:"yst-max-w-screen-sm yst-p-8 yst-space-y-4"},(0,i.createElement)(u.Title,null,(0,p.__)("Something went wrong. An unexpected error occurred.","wordpress-seo")),(0,i.createElement)("p",null,(0,p.__)("We're very sorry, but it seems like the following error has interrupted our application:","wordpress-seo")),(0,i.createElement)(u.Alert,{variant:"error"},(null==e?void 0:e.message)||(0,p.__)("Undefined error message.","wordpress-seo")),(0,i.createElement)("p",null,(0,p.__)("Unfortunately, this means that any unsaved changes in this section will be lost. You can try and refresh this page to resolve the problem. If this error still occurs, please get in touch with our support team, and we'll get you all the help you need!","wordpress-seo")),t);Ls.propTypes={error:h().object.isRequired,children:h().node},Ls.VerticalButtons=Is,Ls.HorizontalButtons=Cs;const Ps=({error:e})=>{const t=(0,d.useCallback)((()=>{var e,t;return null===(e=window)||void 0===e||null===(t=e.location)||void 0===t?void 0:t.reload()}),[]),s=(0,r.useSelect)((e=>e("yoast-seo/editor").selectLink("https://yoa.st/elementor-error-support")),[]),a=(0,r.useSelect)((e=>e("yoast-seo/editor").getPreference("isRtl",!1)),[]);return(0,i.createElement)(u.Root,{context:{isRtl:a}},(0,i.createElement)(Ls,{error:e},(0,i.createElement)(Ls.VerticalButtons,{supportLink:s,handleRefreshClick:t})))};function As(){return(0,i.createElement)(u.ErrorBoundary,{FallbackComponent:Ps},(0,i.createElement)(c.Slot,{name:"YoastElementor"},(e=>{return void 0===(t=e).length?t:(0,J.flatten)(t).sort(((e,t)=>void 0===e.props.renderPriority?1:e.props.renderPriority-t.props.renderPriority));var t})))}Ps.propTypes={error:h().object.isRequired};const Os=window.wp.compose,Ms=i.forwardRef((function(e,t){return i.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true",ref:t},e),i.createElement("path",{fillRule:"evenodd",d:"M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z",clipRule:"evenodd"}))})),Ds=()=>[(0,p.sprintf)(/* translators: %1$s expands to a strong opening tag, %2$s expands to a strong closing tag. */
(0,p.__)("%1$sAI%2$s: Better SEO titles and meta descriptions, faster.","wordpress-seo"),"<strong>","</strong>"),(0,p.sprintf)(/* translators: %1$s expands to a strong opening tag, %2$s expands to a strong closing tag. */
(0,p.__)("%1$sMultiple keywords%2$s: Rank higher for more searches.","wordpress-seo"),"<strong>","</strong>"),(0,p.sprintf)(/* translators: %1$s expands to a strong opening tag, %2$s expands to a strong closing tag. */
(0,p.__)("%1$sSuper fast%2$s internal linking suggestions.","wordpress-seo"),"<strong>","</strong>"),(0,p.sprintf)(/* translators: %1$s expands to a strong opening tag, %2$s expands to a strong closing tag. */
(0,p.__)("%1$sNo more broken links%2$s: Automatic redirect manager.","wordpress-seo"),"<strong>","</strong>"),(0,p.sprintf)(/* translators: %1$s expands to a strong opening tag, %2$s expands to a strong closing tag. */
(0,p.__)("%1$sAppealing social previews%2$s people actually want to click on.","wordpress-seo"),"<strong>","</strong>"),(0,p.sprintf)(/* translators: %1$s expands to a strong opening tag, %2$s expands to a strong closing tag. */
(0,p.__)("%1$s24/7 support%2$s: Also on evenings and weekends.","wordpress-seo"),"<strong>","</strong>")];var Fs=s(4530),Ns=s.n(Fs);const qs=({className:e,...t})=>(0,i.createElement)("span",{className:Ns()("yst-grow yst-overflow-hidden yst-overflow-ellipsis yst-whitespace-nowrap yst-font-wp yst-text-[#555] yst-text-base yst-leading-[normal] yst-subpixel-antialiased yst-text-left",e),...t});qs.displayName="MetaboxButton.Text",qs.propTypes={className:h().string},qs.defaultProps={className:""};const Us=({className:e,...t})=>(0,i.createElement)("button",{type:"button",className:Ns()("yst-flex yst-items-center yst-w-full yst-pt-4 yst-pb-4 yst-pr-4 yst-pl-6 yst-space-x-2 yst-border-t yst-border-t-[rgb(0,0,0,0.2)] yst-rounded-none yst-transition-all hover:yst-bg-[#f0f0f0] focus:yst-outline focus:yst-outline-[1px] focus:yst-outline-[color:#0066cd] focus:-yst-outline-offset-1 focus:yst-shadow-[0_0_3px_rgba(8,74,103,0.8)]",e),...t});Us.propTypes={className:h().string},Us.defaultProps={className:""},Us.Text=qs;const Bs=window.yoast.componentsNew,$s=e=>(0,i.createElement)("div",{className:"yoast components-panel__body"},(0,i.createElement)("h2",{className:"components-panel__body-title"},(0,i.createElement)("button",{id:e.id,onClick:e.onClick,className:"components-button components-panel__body-toggle",type:"button"},e.prefixIcon&&(0,i.createElement)("span",{className:"yoast-icon-span",style:{fill:`${e.prefixIcon&&e.prefixIcon.color||""}`}},(0,i.createElement)(Bs.SvgIcon,{size:e.prefixIcon.size,icon:e.prefixIcon.icon})),(0,i.createElement)("span",{className:"yoast-title-container"},(0,i.createElement)("div",{className:"yoast-title"},e.title),(0,i.createElement)("div",{className:"yoast-subtitle"},e.subTitle)),e.children,e.suffixIcon&&(0,i.createElement)(Bs.SvgIcon,{size:e.suffixIcon.size,icon:e.suffixIcon.icon}),e.SuffixHeroIcon))),Ws=$s;$s.propTypes={onClick:h().func.isRequired,title:h().string.isRequired,id:h().string,subTitle:h().string,suffixIcon:h().object,SuffixHeroIcon:h().object,prefixIcon:h().object,children:h().node},$s.defaultProps={id:"",suffixIcon:null,SuffixHeroIcon:null,prefixIcon:null,subTitle:"",children:null};const Ks=window.yoast.styledComponents;var Hs=s.n(Ks);const Ys=Hs().div`
  padding: 25px 32px 32px;
  color: #303030;
`,zs=Hs().ul`
  margin: 0;
  padding: 0;

  li {
    list-style-image: var(--yoast-svg-icon-check);
    margin: 0.5rem 0 0 1.5rem;
    line-height: 1.4em;

    &::marker {
      font-size: 1.5rem;
    }
  }
`,js=Hs().span`
  display: block;
  margin-top: 4px;
`,Vs=Hs().h2`
  margin-top: 0;
  margin-bottom: 0.25rem;
  color: #303030;
  font-size: 0.8125rem;
  font-weight: 600;
`,Gs=Hs().p`
  display: block;
  margin: 0.25rem 0 1rem 0 !important;
  max-width: 420px;
`,Zs=Hs().hr`
  margin-top: 1.5rem;
  margin-bottom: 1rem;
  border-top: 0;
  border-bottom: 1px solid #E2E8F0;
`,Qs=Hs().div`
  text-align: center;
`,Xs=Hs().a`
  width: 100%;
`,Js=(0,_e.makeOutboundLink)(Xs);class ea extends d.Component{constructor(e){super(e),this.state={defaultPrice:"99"}}createBenefitsList(e){return e.length>0&&(0,i.createElement)(zs,{role:"list"},e.map(((e,t)=>(0,i.createElement)("li",{key:`upsell-benefit-${t}`},(0,d.createInterpolateElement)(e,{strong:(0,i.createElement)("strong",null)})))))}render(){const e=(0,r.select)("yoast-seo/editor").isPromotionActive("black-friday-2023-promotion"),{defaultPrice:t}=this.state,s=e?"69.30":null,a=s||t;return(0,i.createElement)(d.Fragment,null,e&&(0,i.createElement)("div",{className:"yst-flex yst-justify-between yst-items-center yst-text-lg yst-content-between yst-bg-black yst-text-amber-300 yst-h-9 yst-border-amber-300 yst-border-y yst-border-x-0 yst-border-solid yst-px-6"},(0,i.createElement)("div",null,(0,p.__)("BLACK FRIDAY","wordpress-seo")),(0,i.createElement)("div",null,(0,p.__)("30% OFF","wordpress-seo"))),(0,i.createElement)(Ys,null,(0,i.createElement)(Vs,null,this.props.title),(0,i.createElement)(Gs,null,this.props.description),(0,i.createElement)(Qs,null,(0,i.createElement)(Js,{...this.props.upsellButton},this.props.upsellButtonText,this.props.upsellButtonHasCaret&&(0,i.createElement)("span",{"aria-hidden":"true",className:"yoast-button-upsell__caret"})),(0,i.createElement)("div",{className:"yst-text-slate-600 yst-my-4"},s&&(0,i.createElement)(d.Fragment,null,(0,i.createElement)("span",{className:"yst-text-slate-500 yst-line-through"},t)," "),(0,i.createElement)("span",{className:"yst-text-slate-900 yst-text-2xl yst-font-bold"},a)," ",(0,p.__)("$ USD / € EUR / £ GBP per year (ex. VAT)","wordpress-seo")),(0,i.createElement)(js,{id:this.props.upsellButton["aria-describedby"]},this.props.upsellButtonLabel)),(0,i.createElement)(Zs,null),(0,i.createElement)(Vs,null,this.props.benefitsTitle),this.createBenefitsList(this.props.benefits)))}}ea.propTypes={title:h().node,benefits:h().array,benefitsTitle:h().node,description:h().node,upsellButton:h().object,upsellButtonText:h().string.isRequired,upsellButtonLabel:h().string,upsellButtonHasCaret:h().bool},ea.defaultProps={title:null,description:null,benefits:[],benefitsTitle:null,upsellButton:{href:"",className:"button button-primary"},upsellButtonLabel:"",upsellButtonHasCaret:!0};const ta=ea,sa=Hs().div`
	min-width: 600px;

	@media screen and ( max-width: 680px ) {
		min-width: 0;
		width: 86vw;
	}
`,aa=Hs().div`
	@media screen and ( min-width: 600px ) {
		max-width: 420px;
	}
`,na=(Hs()(Bs.Icon)`
	float: ${(0,_e.getDirectionalStyle)("right","left")};
	margin: ${(0,_e.getDirectionalStyle)("0 0 16px 16px","0 16px 16px 0")};

	&& {
		width: 150px;
		height: 150px;

		@media screen and ( max-width: 680px ) {
			width: 80px;
			height: 80px;
		}
	}
`,"yoast yoast-gutenberg-modal"),ra=e=>{const{title:t,className:s,showYoastIcon:a,additionalClassName:n,...r}=e,o=a?(0,i.createElement)("span",{className:"yoast-icon"}):null;return(0,i.createElement)(c.Modal,{title:t,className:`${s} ${n}`,icon:o,...r},e.children)};ra.propTypes={title:h().string,className:h().string,showYoastIcon:h().bool,children:h().oneOfType([h().node,h().arrayOf(h().node)]),additionalClassName:h().string},ra.defaultProps={title:"Yoast SEO",className:na,showYoastIcon:!0,children:null,additionalClassName:""};const oa=ra,ia=()=>{const[e,,,t,s]=(0,u.useToggleState)(!1),{locationContext:a}=(0,Rs.useRootContext)(),n=(0,u.useSvgAria)(),r=a.includes("sidebar"),o=a.includes("metabox"),l=wpseoAdminL10n[r?"shortlinks.upsell.sidebar.internal_linking_suggestions":"shortlinks.upsell.metabox.internal_linking_suggestions"];return(0,i.createElement)(i.Fragment,null,e&&(0,i.createElement)(oa,{title:(0,p.__)("Get internal linking suggestions","wordpress-seo"),onRequestClose:s,additionalClassName:"",id:"yoast-internal-linking-suggestions-upsell",className:`${na} yoast-gutenberg-modal__box yoast-gutenberg-modal__no-padding`,shouldCloseOnClickOutside:!0},(0,i.createElement)(aa,null,(0,i.createElement)(ta,{title:(0,p.__)("Rank higher by connecting your content","wordpress-seo"),description:(0,p.sprintf)(/* translators: %s expands to Yoast SEO Premium. */
(0,p.__)("%s automatically suggests to what content you can link with easy drag-and-drop functionality, which is good for your SEO!","wordpress-seo"),"Yoast SEO Premium"),benefitsTitle:/* translators: %s expands to 'Yoast SEO Premium'. */
(0,p.sprintf)("%s also gives you:","Yoast SEO Premium"),benefits:Ds(),upsellButtonText:(0,p.sprintf)(/* translators: %s expands to 'Yoast SEO Premium'. */
(0,p.__)("Unlock with %s","wordpress-seo"),"Yoast SEO Premium"),upsellButton:{href:(0,te.addQueryArgs)(l,{context:a}),className:"yoast-button-upsell",rel:null,"data-ctb-id":"f6a84663-465f-4cb5-8ba5-f7a6d72224b2","data-action":"load-nfd-ctb"},upsellButtonLabel:(0,p.__)("1 year free support and updates included!","wordpress-seo")}))),r&&(0,i.createElement)(Ws,{id:"yoast-internal-linking-suggestions-sidebar-modal-open-button",title:(0,p.__)("Internal linking suggestions","wordpress-seo"),onClick:t},(0,i.createElement)("div",{className:"yst-root"},(0,i.createElement)(u.Badge,{size:"small",variant:"upsell"},(0,i.createElement)(Ms,{className:"yst-w-2.5 yst-h-2.5 yst-shrink-0",...n})))),o&&(0,i.createElement)("div",{className:"yst-root"},(0,i.createElement)(Us,{id:"yoast-internal-linking-suggestions-metabox-modal-open-button",onClick:t},(0,i.createElement)(Us.Text,null,(0,p.__)("Internal linking suggestions","wordpress-seo")),(0,i.createElement)(u.Badge,{size:"small",variant:"upsell"},(0,i.createElement)(Ms,{className:"yst-w-2.5 yst-h-2.5 yst-mr-1 yst-shrink-0",...n}),(0,i.createElement)("span",null,"Premium")))))},la=window.yoast.externals.components;function ca(){return(0,Os.createHigherOrderComponent)((function(e){return(0,Os.pure)((function(t){const s=(0,d.useContext)(Rs.LocationContext);return(0,d.createElement)(e,{...t,location:s})}))}),"withLocation")}const da=(0,Os.compose)([(0,r.withSelect)((e=>{const{isCornerstoneContent:t}=e("yoast-seo/editor");return{isCornerstone:t(),learnMoreUrl:wpseoAdminL10n["shortlinks.cornerstone_content_info"]}})),(0,r.withDispatch)((e=>{const{toggleCornerstoneContent:t}=e("yoast-seo/editor");return{onChange:t}})),ca()])(la.CollapsibleCornerstone),pa=(0,Os.compose)([(0,r.withSelect)(((e,t)=>{const{isAlertDismissed:s}=e(t.store||"yoast-seo/editor");return{isAlertDismissed:s(t.alertKey)}})),(0,r.withDispatch)(((e,t)=>{const{dismissAlert:s}=e(t.store||"yoast-seo/editor");return{onDismissed:()=>s(t.alertKey)}}))]),ua=({children:e,id:t,hasIcon:s=!0,title:a,image:n=null,isAlertDismissed:r,onDismissed:o})=>r?null:(0,i.createElement)("div",{id:t,className:"notice-yoast yoast is-dismissible"},(0,i.createElement)("div",{className:"notice-yoast__container"},(0,i.createElement)("div",null,(0,i.createElement)("div",{className:"notice-yoast__header"},s&&(0,i.createElement)("span",{className:"yoast-icon"}),(0,i.createElement)("h2",{className:"notice-yoast__header-heading"},a)),(0,i.createElement)("p",null,e)),n&&(0,i.createElement)(n,{height:"60"})),(0,i.createElement)("button",{type:"button",className:"notice-dismiss",onClick:o},(0,i.createElement)("span",{className:"screen-reader-text"},/* translators: Hidden accessibility text. */
(0,p.__)("Dismiss this notice.","wordpress-seo"))));ua.propTypes={children:h().node.isRequired,id:h().string.isRequired,hasIcon:h().bool,title:h().any.isRequired,image:h().elementType,isAlertDismissed:h().bool.isRequired,onDismissed:h().func.isRequired};const ma=pa(ua),ha=({store:e="yoast-seo/editor",image:t=null,title:s,promoId:a,alertKey:n,children:o,...l})=>(0,r.select)(e).isPromotionActive(a)&&(0,i.createElement)(ma,{alertKey:n,store:e,id:n,title:s,image:t,...l},o);ha.propTypes={store:h().string,image:h().elementType,title:h().any.isRequired,promoId:h().string.isRequired,alertKey:h().string.isRequired,children:h().node};const ga=({store:e="yoast-seo/editor",location:t="sidebar",...s})=>{const a=(0,r.useSelect)((t=>t(e).getIsPremium()),[e]),n=(0,r.useSelect)((t=>t(e).selectLinkParams()),[e]),o="sidebar"===t?(0,p.sprintf)(/* translators: %1$s expands to YOAST SEO PREMIUM */
(0,p.__)("BLACK FRIDAY SALE: %1$s","wordpress-seo"),"YOAST SEO PREMIUM"):(0,d.createInterpolateElement)((0,p.sprintf)(/* translators: %1$s expands to YOAST SEO PREMIUM, %2$s expands to a link on yoast.com, %3$s expands to the anchor end tag. */
(0,p.__)("BLACK FRIDAY SALE: %1$s %2$sBuy now!%3$s","wordpress-seo"),"YOAST SEO PREMIUM","<a>","</a>"),{a:(0,i.createElement)("a",{href:(0,te.addQueryArgs)("https://yoa.st/black-friday-sale",n),target:"_blank",rel:"noreferrer"})});return a?null:(0,i.createElement)(ha,{id:`black-friday-2023-promotion-${t}`,promoId:"black-friday-2023-promotion",alertKey:"black-friday-2023-promotion",store:e,title:o,image:Image,...s},(0,i.createElement)("span",{className:"yoast-bf-sale-badge"},(0,p.__)("30% OFF!","wordpress-seo")," "),"sidebar"===t&&(0,i.createElement)("a",{className:"yst-block yst--mb-[1em]",href:(0,te.addQueryArgs)("https://yoa.st/black-friday-sale",n),target:"_blank",rel:"noreferrer"},(0,p.__)("Buy now!","wordpress-seo")))};ga.propTypes={store:h().string,location:h().oneOf(["sidebar","metabox"])};const ya=({store:e="yoast-seo/editor",...t})=>{const s=(0,r.useSelect)((t=>t(e).selectLinkParams()),[e]),a=(0,d.createInterpolateElement)((0,p.sprintf)(/* translators:  %1$s expands to Yoast, %2$s expands to a 'strong' start tag, %2$s to a 'strong' end tag. */
(0,p.__)("The %1$s %2$sultimate Black Friday checklist%3$s helps you prepare in time, so you can boost your results during this sale.","wordpress-seo"),"Yoast","<strong>","</strong>"),{strong:(0,i.createElement)("strong",null)});return(0,i.createElement)(ha,{id:"black-friday-2023-sidebar-checklist",promoId:"black-friday-2023-checklist",alertKey:"black-friday-2023-sidebar-checklist",store:e,title:(0,p.__)("Is your WooCommerce store ready for Black Friday?","wordpress-seo"),...t},a," ",(0,i.createElement)("a",{href:(0,te.addQueryArgs)("https://yoa.st/black-friday-checklist",s),target:"_blank",rel:"noreferrer"},(0,p.__)("Get the checklist and start optimizing now!","wordpress-seo")))};ya.propTypes={store:h().string};const fa="trustpilot-review-notification",wa="yoast-seo/editor";const ba=()=>{const e=(0,r.useSelect)((e=>e(wa).getIsPremium()),[]),t=(0,r.useSelect)((e=>e(wa).isAlertDismissed(fa)),[]),{overallScore:s}=(0,r.useSelect)((e=>e(wa).getResultsForFocusKeyword()),[]),{dismissAlert:a}=(0,r.useDispatch)(wa),n=(0,d.useCallback)((()=>a(fa)),[a]),[o,i]=(0,d.useState)(!1);return(0,d.useEffect)((()=>{var e,t;"good"===(null===(t=s,(0,J.isNil)(t)||(t/=10),e=function(e){switch(e){case"feedback":return{className:"na",screenReaderText:(0,p.__)("Feedback","wordpress-seo"),screenReaderReadabilityText:"",screenReaderInclusiveLanguageText:""};case"bad":return{className:"bad",screenReaderText:(0,p.__)("Needs improvement","wordpress-seo"),screenReaderReadabilityText:(0,p.__)("Needs improvement","wordpress-seo"),screenReaderInclusiveLanguageText:(0,p.__)("Needs improvement","wordpress-seo")};case"ok":return{className:"ok",screenReaderText:(0,p.__)("OK SEO score","wordpress-seo"),screenReaderReadabilityText:(0,p.__)("OK","wordpress-seo"),screenReaderInclusiveLanguageText:(0,p.__)("Potentially non-inclusive","wordpress-seo")};case"good":return{className:"good",screenReaderText:(0,p.__)("Good SEO score","wordpress-seo"),screenReaderReadabilityText:(0,p.__)("Good","wordpress-seo"),screenReaderInclusiveLanguageText:(0,p.__)("Good","wordpress-seo")};default:return{className:"loading",screenReaderText:"",screenReaderReadabilityText:"",screenReaderInclusiveLanguageText:""}}}(Ke.interpreters.scoreToRating(t)))||void 0===e?void 0:e.className)&&i(!0)}),[s]),{shouldShow:!e&&!t&&o,dismiss:n}},Ea=(0,_e.makeOutboundLink)(),va=()=>{const{shouldShow:e,dismiss:t}=ba(),{locationContext:s}=(0,Rs.useRootContext)(),a=(0,r.useSelect)((e=>e(wa).selectLink("https://yoa.st/trustpilot-review",{context:s})),[s]);return(0,i.createElement)(ua,{alertKey:fa,store:wa,id:fa,title:(0,p.__)("Show Yoast SEO some love!","wordpress-seo"),hasIcon:!1,isAlertDismissed:!e,onDismissed:t},(0,p.__)("Happy with the plugin?","wordpress-seo")," ",(0,i.createElement)(Ea,{href:a,rel:"noopener noreferrer"},(0,p.__)("Leave a quick review","wordpress-seo")),".")};var _a,ka,xa,Sa,Ta,Ra,Ca,Ia,La,Pa,Aa,Oa,Ma,Da,Fa,Na,qa,Ua,Ba,$a,Wa,Ka,Ha,Ya,za,ja,Va,Ga,Za,Qa,Xa,Ja,en,tn,sn,an,nn,rn,on,ln,cn,dn,pn,un,mn,hn,gn;function yn(){return yn=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var s=arguments[t];for(var a in s)Object.prototype.hasOwnProperty.call(s,a)&&(e[a]=s[a])}return e},yn.apply(this,arguments)}const fn=e=>i.createElement("svg",yn({xmlns:"http://www.w3.org/2000/svg","aria-hidden":"true",viewBox:"0 0 448 360"},e),_a||(_a=i.createElement("circle",{cx:226,cy:211,r:149,fill:"#f0ecf0"})),ka||(ka=i.createElement("path",{fill:"#fbd2a6",d:"M173.53 189.38s-35.47-5.3-41.78-11c-9.39-24.93-29.61-48-35.47-66.21-.71-2.24 3.72-11.39 3.53-15.41s-5.34-11.64-5.23-14-.09-15.27-.09-15.27l-4.75-.72s-5.13 6.07-3.56 9.87c-1.73-4.19 4.3 7.93.5 9.35 0 0-6-5.94-11.76-8.27s-19.57-3.65-19.57-3.65L43.19 73l-4.42.6L31 69.7l-2.85 5.12 7.53 5.29L40.86 92l17.19 10.2 10.2 10.56 9.86 3.56s26.49 79.67 45 92c17 11.33 37.23 15.92 37.23 15.92z"})),xa||(xa=i.createElement("path",{fill:"#a4286a",d:"M270.52 345.13c2.76-14.59 15.94-35.73 30.24-54.58 16.22-21.39 14-79.66-33.19-91.46-17.3-4.32-52.25-1-59.85-3.41C186.54 189 170 187 168 190.17c-5 10.51-7.73 27.81-5.51 36.26 1.18 4.73 3.54 5.91 20.49 13.4-5.12 15-16.35 26.3-22.86 37s7.88 27.2 7.1 33.51c-.48 3.8-4.26 21.13-7.18 34.25a149.47 149.47 0 0 0 110.3 8.66 25.66 25.66 0 0 1 .18-8.12z"})),Sa||(Sa=i.createElement("path",{fill:"#9a5815",d:"M206.76 66.43c-5 14.4-1.42 25.67-3.93 40.74-10 60.34-24.08 43.92-31.44 93.6 7.24-14.19 14.32-15.82 20.63-23.11-.83 3.09-10.25 13.75-8.05 34.81 9.85-8.51 6.35-8.75 11.86-8.54.36 3.25 3.53 3.22-3.59 10.53 2.52.69 17.42-14.32 20.16-12.66s0 5.72-6 7.76c2.15 2.2 30.47-3.87 43.81-14.71 4.93-4 10-13.16 13.38-18.2 7.17-10.62 12.38-24.77 17.71-36.6 8.94-19.87 15.09-39.34 16.11-61.31.53-10.44-3.41-18.44-4.41-28.86-2.57-27.8-67.63-37.26-86.24 16.55z"})),Ta||(Ta=i.createElement("path",{fill:"#efb17c",d:"M277.74 179.06c.62-.79 1.24-1.59 1.84-2.39-.85 2.59-1.52 3.73-1.84 2.39z"})),Ra||(Ra=i.createElement("path",{fill:"#fbd2a6",d:"M216.1 206.72c3.69-5.42 8.28-3.35 15.57-8.28 3.76-3.06 1.57-9.46 1.77-11.82 18.25 4.56 37.38-1.18 49.07-16 .62 5.16-2.77 22.27-.2 27 4.73 8.67 13.4 18.92 13.4 18.92-35.47-2.76-63.45 39-89.86 44.54 5.52-28.74-2.36-35.84 10.25-54.36z"})),Ca||(Ca=i.createElement("path",{fill:"#f6b488",d:"m235.21 167.9 53.21-25.23s-3.65 24-6.5 32.72c-64.05 62.66-46.47-7.33-46.71-7.49z"})),Ia||(Ia=i.createElement("path",{fill:"#fbd2a6",d:"M226.86 50.64C215 59.31 206.37 93.21 204 95.57c-19.46 19.47-3.59 41.39-3.94 51.24-.2 5.52-4.14 25.42 5.72 29.36 22.22 8.89 60-3.48 67.19-12.61 13.28-16.75 40.89-94.78 17.74-108.19-7.92-4.58-42.78-20.18-63.85-4.73z"})),La||(La=i.createElement("path",{fill:"#e5766c",d:"M243.69 143.66c-10.7-6.16-8.56-6.73-19.76-12.71-3.86-2.07-3.94.64-6.32 0-2.91-.79-1.39-2.74-5.37-3.48-6.52-1.21-3.67 3.63-3.15 6 1.32 6.15-8.17 17.3 3.26 21.42 12.65 4.55 21.38-9.41 31.34-11.23z"})),Pa||(Pa=i.createElement("path",{fill:"#fff",d:"M240.68 143.9c-11.49-5.53-11.65-8.17-24.64-11.69-8.6-2.32-5.53 1-5.69 4.42-.2 4.16-1.26 9.87 4.9 12.66 9 4.09 18.16-6.02 25.43-5.39zm.7-40.9c-.16 1.26-.06 4.9 5.46 8.25 11.43-4.73 16.36-2.56 17-3.33 1.48-1.76-2-8.87-7.88-9.85-5.58-.94-14.14 1.24-14.58 4.93z"})),Aa||(Aa=i.createElement("path",{fill:"#000001",d:"M263.53 108.19c-4.32-4.33-6.85-6.24-12.26-8.21-2.77-1-6.18.18-8.65 1.67a3.65 3.65 0 0 0-1.24 1.23h-.12a3.73 3.73 0 0 1 1-1.52 12.53 12.53 0 0 1 11.93-3c4.73 1 9.43 4.63 9.42 9.82z"})),Oa||(Oa=i.createElement("circle",{cx:254.13,cy:104.05,r:4.19,fill:"#000001"})),Ma||(Ma=i.createElement("path",{fill:"#fff",d:"M225.26 99.22c-.29 1-6.6 3.45-10.92 1.48-1.15-3.24-5-6.43-5.25-6.71-.5-2.86 5.55-8 10.06-6.3a10.21 10.21 0 0 1 6.11 11.53z"})),Da||(Da=i.createElement("path",{fill:"#000001",d:"M209.29 94.21c-.19-2.34 1.84-4.1 3.65-5.2 7-3.87 13.18 3 12.43 10h-.12c-.14-4-2.38-8.44-6.47-9.11a3.19 3.19 0 0 0-2.42.31c-1.37.85-2.38 2-3.89 2.56-1 .45-1.92.42-3 1.4h-.22z"})),Fa||(Fa=i.createElement("circle",{cx:219.55,cy:95.28,r:4,fill:"#000001"})),Na||(Na=i.createElement("path",{fill:"#efb17c",d:"M218.66 120.27a27.32 27.32 0 0 0 4.54 3.45c-2.29-.72-4.28-.69-6.32-2.27-2.53-2-3.39-5.16-.73-7.72 10.24-9.82 12.56-13.82 14.77-24.42-1 12.37-6 17.77-10.63 23.18-2.53 2.97-4.68 5.06-1.63 7.78z"})),qa||(qa=i.createElement("path",{fill:"#a57c52",d:"M231.22 69.91c-.67-3.41-8.78-2.83-11.06-1.93-3.48 1.39-6.08 5.22-7.13 8.53 2.9-4.3 6.74-8.12 12.46-6 1.16.42 3.18 2.35 4.48 1.85s1.03-2.2 1.25-2.45zm32.16 8.56c-2.75-1.66-12.24-5.08-12.18.82 2.56.24 5-.19 7.64.95 11.22 4.76 12.77 17.61 12.85 17.86.2-.53.1 1.26.23.7-.02.2.95-12.12-8.54-20.33z"})),Ua||(Ua=i.createElement("path",{fill:"#fbd2a6",d:"M53.43 250.73c6.29 0-.6-.17 7.34 0 1.89.05-2.38-.7 0-.69 4.54-4.2 12.48-.74 20.6-2.45 4.55.35 3.93 1.35 5.59 4.19 4.89 8.38 4.78 14.21 14 19.56 16.42 8.38 66 12.92 88.49 18.86 5.52.83 42.64-20.15 61-23.75 6.51 10.74 11.46 28.68 8.39 34.93-6.54 13.3-57.07 25.4-75.91 25.15C156.47 326.18 94 294 92.2 293c-.94-.57.7-.7-7.68 0s-10.15.72-17.47-1.4c-3-.87-4.61-1.33-6.33-3.54-2 .22-3.39.2-4.78-1-3.15-2.74-4.84-6.61-2.73-10.06h-.12c-3.35-2.48-6.54-7.69-3.08-11.72 1-1.18 6.06-1.94 7.77-2.28-1.58-.29-6.37.19-7.49-.72-3.06-2.5-4.96-11.55 3.14-11.55z"})),Ba||(Ba=i.createElement("path",{fill:"#a4286a",d:"M303.22 237.52c-9.87-11.88-41.59 8.19-47.8 12.34s-14.89 17.95-14.89 17.95c6 9.43 8.36 31 5.65 46.34l30.51-3s18-15.62 22.59-28.7 6.3-42.54 6.3-42.54"})),$a||($a=i.createElement("path",{fill:"#cb9833",d:"M278.63 31.67c-6.08 0-22.91 4.07-22.93 12.91 0 11 47.9 38.38 16.14 85.85 10.21-.79 10.79-8.12 14.92-14.93-3.66 77-49.38 93.58-40.51 142.25 7.68-25.81 20.3-11.62 38.13-33.84 3.45 4.88 9 18.28-9.46 33.78 50-31.26 57.31-56.6 51.92-95C319.93 113.53 348.7 42 278.63 31.67z"})),Wa||(Wa=i.createElement("path",{fill:"#fbd2a6",d:"M283.64 126.83c-2.42 9.67-8 15.76-1.48 16.46A21.26 21.26 0 0 0 302 132.6c5.17-8.52 3.93-16.44-2.46-18s-13.48 2.56-15.9 12.23z"})),Ka||(Ka=i.createElement("path",{fill:"#efb17c",d:"M38 73.45c1.92 2 4.25 9.21 6.32 10.91 2.25 1.85 5.71 2.12 8.1 4.45 3.66-2 6-8.72 10-9.31-2.59 1.31-4.42 3.5-6.93 4.88-1.42.8-3 1.31-4.38 2.25-2.16-1.46-4.27-1.77-6.26-3.38-2.52-2.02-5.31-8-6.85-9.8z"})),Ha||(Ha=i.createElement("path",{fill:"#efb17c",d:"M39 74.4c4.83 1.1 12.52 6.44 15.89 10-3.22-1.34-14.73-6.15-15.89-10zm.62-1.5c6.71-.79 18 1.54 23.29 5.9-3.85-.2-5.42-1.48-9-2.94-4.08-1.69-8.83-2.03-14.29-2.96zm46.43 14.58c-3.72-1.32-10.52-1.13-13.22 3.52 2-1.16 1.84-2.11 4.18-1.72-3.81-4.15 8.16-.74 11.6-.24m-2.78 13.15c.56-3.29-8-7.81-10.58-9.17-6.25-3.29-12.16 1.36-19.33-4.53 5.94 6.1 14.23 2.5 19.55 5.76 3.06 1.88 8.65 6.09 9.35 9.38-.23-.4 1.29-1.44 1.01-1.44z"})),Ya||(Ya=i.createElement("circle",{cx:38.13,cy:30.03,r:3.14,fill:"#b89ac8"})),za||(za=i.createElement("circle",{cx:60.26,cy:39.96,r:3.14,fill:"#e31e0c"})),ja||(ja=i.createElement("circle",{cx:50.29,cy:25.63,r:3.14,fill:"#3baa45"})),Va||(Va=i.createElement("circle",{cx:22.19,cy:19.21,r:3.14,fill:"#2ca9e1"})),Ga||(Ga=i.createElement("circle",{cx:22.19,cy:30.03,r:3.14,fill:"#e31e0c"})),Za||(Za=i.createElement("circle",{cx:26.86,cy:8.28,r:3.14,fill:"#3baa45"})),Qa||(Qa=i.createElement("circle",{cx:49.32,cy:39.99,r:3.14,fill:"#e31e0c"})),Xa||(Xa=i.createElement("circle",{cx:63.86,cy:59.52,r:3.14,fill:"#f8ad39"})),Ja||(Ja=i.createElement("circle",{cx:50.88,cy:50.72,r:3.14,fill:"#3baa45"})),en||(en=i.createElement("circle",{cx:63.47,cy:76.17,r:3.14,fill:"#e31e0c"})),tn||(tn=i.createElement("circle",{cx:38.34,cy:14.83,r:3.14,fill:"#2ca9e1"})),sn||(sn=i.createElement("circle",{cx:44.44,cy:5.92,r:3.14,fill:"#f8ad39"})),an||(an=i.createElement("circle",{cx:57.42,cy:10.24,r:3.14,fill:"#e31e0c"})),nn||(nn=i.createElement("circle",{cx:66.81,cy:12.4,r:3.14,fill:"#2ca9e1"})),rn||(rn=i.createElement("circle",{cx:77.95,cy:5.14,r:3.14,fill:"#b89ac8"})),on||(on=i.createElement("circle",{cx:77.95,cy:30.34,r:3.14,fill:"#e31e0c"})),ln||(ln=i.createElement("circle",{cx:80.97,cy:16.55,r:3.14,fill:"#f8ad39"})),cn||(cn=i.createElement("circle",{cx:62.96,cy:27.27,r:3.14,fill:"#3baa45"})),dn||(dn=i.createElement("circle",{cx:75.36,cy:48.67,r:3.14,fill:"#2ca9e1"})),pn||(pn=i.createElement("circle",{cx:76.11,cy:65.31,r:3.14,fill:"#3baa45"})),un||(un=i.createElement("path",{fill:"#71b026",d:"M78.58 178.43C54.36 167.26 32 198.93 5 198.93c19.56 20.49 63.53 1.52 69 15.5 1.48-14.01 4.11-30.9 4.58-36z"})),mn||(mn=i.createElement("path",{fill:"#074a67",d:"M67.75 251.08c0-4.65 10.13-72.65 10.13-72.65h2.8l-9.09 72.3z"})),hn||(hn=i.createElement("ellipse",{cx:255.38,cy:103.18,fill:"#fff",rx:1.84,ry:1.77})),gn||(gn=i.createElement("ellipse",{cx:221.24,cy:94.75,fill:"#fff",rx:1.84,ry:1.77}))),wn=({store:e="yoast-seo/editor",image:t=fn,url:s,...a})=>(0,r.useSelect)((t=>t(e).getIsPremium()))?null:(0,i.createElement)(ma,{alertKey:"webinar-promo-notification",store:e,id:"webinar-promo-notification",title:(0,p.__)("Join our FREE webinar for SEO success","wordpress-seo"),image:t,url:s,...a},(0,p.__)("Feeling lost when it comes to optimizing your site for the search engines? Join our FREE webinar to gain the confidence that you need in order to start optimizing like a pro! You'll obtain the knowledge and tools to start effectively implementing SEO.","wordpress-seo")," ",(0,i.createElement)("a",{href:s,target:"_blank",rel:"noreferrer"},(0,p.__)("Sign up today!","wordpress-seo")));wn.propTypes={store:h().string,image:h().elementType,url:h().string.isRequired};const bn=wn,En=()=>window.wpseoScriptData&&"1"===window.wpseoScriptData.isWooCommerceActive,vn=(e="yoast-seo/editor")=>{const t=(0,r.select)(e).isPromotionActive("black-friday-2023-promotion"),s=(0,r.select)(e).isAlertDismissed("black-friday-2023-promotion");return t?s:((e="yoast-seo/editor")=>{const t=(0,r.select)(e).isPromotionActive("black-friday-2023-checklist"),s=(0,r.select)(e).isAlertDismissed("black-friday-2023-sidebar-checklist");return!t||s})(e)},kn=i.forwardRef((function(e,t){return i.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true",ref:t},e),i.createElement("path",{d:"M11 3a1 1 0 10-2 0v1a1 1 0 102 0V3zM15.657 5.757a1 1 0 00-1.414-1.414l-.707.707a1 1 0 001.414 1.414l.707-.707zM18 10a1 1 0 01-1 1h-1a1 1 0 110-2h1a1 1 0 011 1zM5.05 6.464A1 1 0 106.464 5.05l-.707-.707a1 1 0 00-1.414 1.414l.707.707zM5 10a1 1 0 01-1 1H3a1 1 0 110-2h1a1 1 0 011 1zM8 16v-1h4v1a2 2 0 11-4 0zM12 14c.015-.34.208-.646.477-.859a4 4 0 10-4.954 0c.27.213.462.519.476.859h4.002z"}))})),xn=(e=null)=>(0,i.useMemo)((()=>{const t={role:"img","aria-hidden":"true"};return null!==e&&(t.focusable=e?"true":"false"),t}),[e]),Sn=({id:e,postTypeName:t,children:s,title:a,isOpen:n,close:r,open:o,shouldCloseOnClickOutside:l,showChangesWarning:c,SuffixHeroIcon:u})=>(0,i.createElement)(d.Fragment,null,n&&(0,i.createElement)(Rs.LocationProvider,{value:"modal"},(0,i.createElement)(oa,{title:a,onRequestClose:r,additionalClassName:"yoast-collapsible-modal yoast-post-settings-modal",id:"id",shouldCloseOnClickOutside:l},(0,i.createElement)("div",{className:"yoast-content-container"},(0,i.createElement)("div",{className:"yoast-modal-content"},s)),(0,i.createElement)("div",{className:"yoast-notice-container"},(0,i.createElement)("hr",null),(0,i.createElement)("div",{className:"yoast-button-container"},c&&(0,i.createElement)("p",null,/* Translators: %s translates to the Post Label in singular form */
(0,p.sprintf)((0,p.__)("Make sure to save your %s for changes to take effect","wordpress-seo"),t)),(0,i.createElement)("button",{className:"yoast-button yoast-button--primary yoast-button--post-settings-modal",type:"button",onClick:r},/* Translators: %s translates to the Post Label in singular form */
(0,p.sprintf)((0,p.__)("Return to your %s","wordpress-seo"),t)))))),(0,i.createElement)(Ws,{id:e+"-open-button",title:a,SuffixHeroIcon:u,suffixIcon:u?null:{size:"20px",icon:"pencil-square"},onClick:o}));Sn.propTypes={id:h().string.isRequired,postTypeName:h().string.isRequired,children:h().oneOfType([h().node,h().arrayOf(h().node)]).isRequired,title:h().string.isRequired,isOpen:h().bool.isRequired,open:h().func.isRequired,close:h().func.isRequired,shouldCloseOnClickOutside:h().bool,showChangesWarning:h().bool,SuffixHeroIcon:h().object},Sn.defaultProps={shouldCloseOnClickOutside:!0,showChangesWarning:!0};const Tn=Sn,Rn=(0,Os.compose)([(0,r.withSelect)(((e,t)=>{const{getPostOrPageString:s,getIsModalOpen:a}=e("yoast-seo/editor");return{postTypeName:s(),isOpen:a(t.id)}})),(0,r.withDispatch)(((e,t)=>{const{openEditorModal:s,closeEditorModal:a}=e("yoast-seo/editor");return{open:()=>s(t.id),close:a}}))])(Tn),Cn=()=>{const e=(0,r.useSelect)((e=>e("yoast-seo/editor").getEstimatedReadingTime()),[]),t=(0,d.useMemo)((()=>(0,J.get)(window,"wpseoAdminL10n.shortlinks-insights-estimated_reading_time","")),[]);return(0,i.createElement)(Bs.InsightsCard,{amount:e,unit:(0,p._n)("minute","minutes",e,"wordpress-seo"),title:(0,p.__)("Reading time","wordpress-seo"),linkTo:t
/* translators: Hidden accessibility text. */,linkText:(0,p.__)("Learn more about reading time","wordpress-seo")})},In=(0,_e.makeOutboundLink)();function Ln(e,t,s){const a=function(e){switch(e){case Ke.DIFFICULTY.FAIRLY_DIFFICULT:case Ke.DIFFICULTY.DIFFICULT:case Ke.DIFFICULTY.VERY_DIFFICULT:return(0,p.__)("Try to make shorter sentences, using less difficult words to improve readability","wordpress-seo");case Ke.DIFFICULTY.NO_DATA:return(0,p.__)("Continue writing to get insight into the readability of your text!","wordpress-seo");default:return(0,p.__)("Good job!","wordpress-seo")}}(t);return(0,i.createElement)("span",null,function(e,t){return-1===e?(0,p.sprintf)((0,p.__)("Your text should be slightly longer to calculate your Flesch reading ease score.","wordpress-seo")):(0,p.sprintf)(
/* Translators: %1$s expands to the numeric Flesch reading ease score,
  		%2$s expands to the easiness of reading (e.g. 'easy' or 'very difficult').
  	 */
(0,p.__)("The copy scores %1$s in the test, which is considered %2$s to read.","wordpress-seo"),e,function(e){switch(e){case Ke.DIFFICULTY.NO_DATA:return(0,p.__)("no data","wordpress-seo");case Ke.DIFFICULTY.VERY_EASY:return(0,p.__)("very easy","wordpress-seo");case Ke.DIFFICULTY.EASY:return(0,p.__)("easy","wordpress-seo");case Ke.DIFFICULTY.FAIRLY_EASY:return(0,p.__)("fairly easy","wordpress-seo");case Ke.DIFFICULTY.OKAY:return(0,p.__)("okay","wordpress-seo");case Ke.DIFFICULTY.FAIRLY_DIFFICULT:return(0,p.__)("fairly difficult","wordpress-seo");case Ke.DIFFICULTY.DIFFICULT:return(0,p.__)("difficult","wordpress-seo");case Ke.DIFFICULTY.VERY_DIFFICULT:return(0,p.__)("very difficult","wordpress-seo")}}(t))}(e,t)," ",t>=Ke.DIFFICULTY.FAIRLY_DIFFICULT?(0,i.createElement)(In,{href:s},a+"."):a)}const Pn=()=>{let e=(0,r.useSelect)((e=>e("yoast-seo/editor").getFleschReadingEaseScore()),[]);const t=(0,d.useMemo)((()=>(0,J.get)(window,"wpseoAdminL10n.shortlinks-insights-flesch_reading_ease","")),[]),s=(0,r.useSelect)((e=>e("yoast-seo/editor").getFleschReadingEaseDifficulty()),[e]),a=(0,d.useMemo)((()=>{const t=(0,J.get)(window,"wpseoAdminL10n.shortlinks-insights-flesch_reading_ease_article","");return Ln(e,s,t)}),[e,s]);return-1===e&&(e="?"),(0,i.createElement)(Bs.InsightsCard,{amount:e,unit:(0,p.__)("out of 100","wordpress-seo"),title:(0,p.__)("Flesch reading ease","wordpress-seo"),linkTo:t
/* translators: Hidden accessibility text. */,linkText:(0,p.__)("Learn more about Flesch reading ease","wordpress-seo"),description:a})};let An,On,Mn,Dn;const Fn=/<(\/)?(\w+)\s*(\/)?>/g;function Nn(e,t,s,a,n){return{element:e,tokenStart:t,tokenLength:s,prevOffset:a,leadingTextStart:n,children:[]}}function qn(){const e=An.length-On;0!==e&&Mn.push(An.substring(On,On+e))}function Un(e){const{element:t,tokenStart:s,tokenLength:a,prevOffset:n,children:r}=e,o=Dn[Dn.length-1],i=An.substring(o.prevOffset,s);i&&o.children.push(i),o.children.push((0,d.cloneElement)(t,null,...r)),o.prevOffset=n||s+a}function Bn(e){const t=function(){const e=Fn.exec(An);if(null===e)return["no-more-tokens"];const t=e.index,[s,a,n,r]=e,o=s.length;return r?["self-closed",n,t,o]:a?["closer",n,t,o]:["opener",n,t,o]}(),[s,a,n,r]=t,o=Dn.length,i=n>On?On:null;if(!e[a])return qn(),!1;switch(s){case"no-more-tokens":if(0!==o){const{leadingTextStart:e,tokenStart:t}=Dn.pop();Mn.push(An.substring(e,e+t))}return qn(),!1;case"self-closed":return 0===o?(null!==i&&Mn.push(An.substring(i,n)),Mn.push(e[a]),On=n+r,!0):(Un(Nn(e[a],n,r)),On=n+r,!0);case"opener":return Dn.push(Nn(e[a],n,r,n+r,i)),On=n+r,!0;case"closer":if(1===o)return function(e){const{element:t,leadingTextStart:s,prevOffset:a,tokenStart:n,children:r}=Dn.pop(),o=e?An.substring(a,e):An.substring(a);o&&r.push(o),null!==s&&Mn.push(An.substring(s,n)),Mn.push((0,d.cloneElement)(t,null,...r))}(n),On=n+r,!0;const t=Dn.pop(),s=An.substring(t.prevOffset,n);t.children.push(s),t.prevOffset=n+r;const l=Nn(t.element,t.tokenStart,t.tokenLength,n+r);return l.children=t.children,Un(l),On=n+r,!0;default:return qn(),!1}}const $n=(e,t)=>{if(An=e,On=0,Mn=[],Dn=[],Fn.lastIndex=0,!(e=>{const t="object"==typeof e,s=t&&Object.values(e);return t&&s.length&&s.every((e=>(0,d.isValidElement)(e)))})(t))throw new TypeError("The conversionMap provided is not valid. It must be an object with values that are WPElements");do{}while(Bn(t));return(0,d.createElement)(d.Fragment,null,...Mn)},Wn=({data:e,itemScreenReaderText:t,className:s,...a})=>{const n=(0,d.useMemo)((()=>{var t,s;return null!==(t=null===(s=(0,J.maxBy)(e,"number"))||void 0===s?void 0:s.number)&&void 0!==t?t:0}),[e]);return(0,i.createElement)("ul",{className:Ns()("yoast-data-model",s),...a},e.map((({name:e,number:s})=>(0,i.createElement)("li",{key:`${e}_dataItem`,style:{"--yoast-width":s/n*100+"%"}},e,(0,i.createElement)("span",null,s),t&&(0,i.createElement)("span",{className:"screen-reader-text"},(0,p.sprintf)(t,s))))))};Wn.propTypes={data:h().arrayOf(h().shape({name:h().string.isRequired,number:h().number.isRequired})),itemScreenReaderText:h().string,className:h().string},Wn.defaultProps={data:[],itemScreenReaderText:"",className:""};const Kn=Wn,Hn=(0,_e.makeOutboundLink)(),Yn=({location:e})=>{const t=(0,r.useSelect)((e=>{var t,s;return null===(t=null===(s=e("yoast-seo-premium/editor"))||void 0===s?void 0:s.getPreference("isProminentWordsAvailable",!1))||void 0===t||t}),[]),s=(0,r.useSelect)((e=>e("yoast-seo/editor").getPreference("shouldUpsell",!1)),[]),a=(0,d.useMemo)((()=>(0,J.get)(window,`wpseoAdminL10n.shortlinks-insights-upsell-${e}-prominent_words`,"")),[e]),n=(0,d.useMemo)((()=>{const e=(0,J.get)(window,"wpseoAdminL10n.shortlinks-insights-keyword_research_link","");return $n((0,p.sprintf)(
// translators: %1$s and %2$s are replaced by opening and closing <a> tags.
(0,p.__)("Read our %1$sultimate guide to keyword research%2$s to learn more about keyword research and keyword strategy.","wordpress-seo"),"<a>","</a>"),{a:(0,i.createElement)(Hn,{href:e})})}),[]),o=(0,d.useMemo)((()=>$n((0,p.sprintf)(
// translators: %1$s expands to a starting `b` tag, %1$s expands to a closing `b` tag and %3$s expands to `Yoast SEO Premium`.
(0,p.__)("With %1$s%3$s%2$s, this section will show you which words occur most often in your text. By checking these prominent words against your intended keyword(s), you'll know how to edit your text to be more focused.","wordpress-seo"),"<b>","</b>","Yoast SEO Premium"),{b:(0,i.createElement)("b",null)})),[]),l=(0,r.useSelect)((e=>{var t,s;return null!==(t=null===(s=e("yoast-seo-premium/editor"))||void 0===s?void 0:s.getProminentWords())&&void 0!==t?t:[]}),[]),c=(0,d.useMemo)((()=>{const e=(0,p.sprintf)(
// translators: %1$s expands to Yoast SEO Premium.
(0,p.__)("Get %s to enjoy the benefits of prominent words","wordpress-seo"),"Yoast SEO Premium").split(/\s+/);return e.map(((t,s)=>({name:t,number:e.length-s})))}),[]),u=(0,d.useMemo)((()=>s?c:l.map((({word:e,occurrence:t})=>({name:e,number:t})))),[l,c]);if(!t)return null;const{locationContext:m}=(0,Rs.useRootContext)();return(0,i.createElement)("div",{className:"yoast-prominent-words"},(0,i.createElement)("div",{className:"yoast-field-group__title"},(0,i.createElement)("b",null,(0,p.__)("Prominent words","wordpress-seo"))),!s&&(0,i.createElement)("p",null,0===u.length?(0,p.__)("Once you add a bit more copy, we'll give you a list of words that occur the most in the content. These give an indication of what your content focuses on.","wordpress-seo"):(0,p.__)("The following words occur the most in the content. These give an indication of what your content focuses on. If the words differ a lot from your topic, you might want to rewrite your content accordingly.","wordpress-seo")),s&&(0,i.createElement)("p",null,o),s&&(0,i.createElement)(Hn,{href:(0,te.addQueryArgs)(a,{context:m}),"data-action":"load-nfd-ctb","data-ctb-id":"f6a84663-465f-4cb5-8ba5-f7a6d72224b2",className:"yoast-button yoast-button-upsell"},(0,p.sprintf)(
// translators: %s expands to `Premium` (part of add-on name).
(0,p.__)("Unlock with %s","wordpress-seo"),"Premium"),(0,i.createElement)("span",{"aria-hidden":"true",className:"yoast-button-upsell__caret"})),(0,i.createElement)("p",null,n),(0,i.createElement)(Kn,{data:u,itemScreenReaderText:/* translators: Hidden accessibility text; %d expands to the number of occurrences. */
(0,p.__)("%d occurrences","wordpress-seo"),"aria-label":(0,p.__)("Prominent words","wordpress-seo"),className:s?"yoast-data-model--upsell":null}))};Yn.propTypes={location:h().string.isRequired};const zn=Yn,jn=(0,_e.makeOutboundLink)(),Vn=({location:e})=>{const t=(0,d.useMemo)((()=>(0,J.get)(window,`wpseoAdminL10n.shortlinks-insights-upsell-${e}-text_formality`,"")),[e]),s=(0,d.useMemo)((()=>$n((0,p.sprintf)(
// Translators: %1$s expands to a starting `b` tag, %2$s expands to a closing `b` tag and %3$s expands to `Yoast SEO Premium`.
(0,p.__)("%1$s%3$s%2$s will help you assess the formality level of your text.","wordpress-seo"),"<b>","</b>","Yoast SEO Premium"),{b:(0,i.createElement)("b",null)})),[]);return(0,i.createElement)(d.Fragment,null,(0,i.createElement)("div",null,(0,i.createElement)("p",null,s),(0,i.createElement)(jn,{href:t,className:"yoast-button yoast-button-upsell"},(0,p.sprintf)(
// Translators: %s expands to `Premium` (part of add-on name).
(0,p.__)("Unlock with %s","wordpress-seo"),"Premium"),(0,i.createElement)("span",{"aria-hidden":"true",className:"yoast-button-upsell__caret"}))))};Vn.propTypes={location:h().string.isRequired};const Gn=Vn,Zn=({location:e,name:t})=>{const s=(0,r.useSelect)((e=>e("yoast-seo/editor").isFormalitySupported()),[]),a=ze().isPremium,n=a?(0,J.get)(window,"wpseoAdminL10n.shortlinks-insights-text_formality_info_premium",""):(0,J.get)(window,"wpseoAdminL10n.shortlinks-insights-text_formality_info_free",""),o=(0,p.__)("Read more about text formality.","wordpress-seo");return s?(0,i.createElement)("div",{className:"yoast-text-formality"},(0,i.createElement)("div",{className:"yoast-field-group__title"},(0,i.createElement)("b",null,(0,p.__)("Text formality","wordpress-seo")),(0,i.createElement)(Bs.HelpIcon,{linkTo:n,linkText:o})),a?(0,i.createElement)(c.Slot,{name:t}):(0,i.createElement)(Gn,{location:e})):null};Zn.propTypes={location:h().string.isRequired,name:h().string.isRequired};const Qn=Zn,Xn=()=>{const e=(0,r.useSelect)((e=>e("yoast-seo/editor").getTextLength()),[]),t=(0,d.useMemo)((()=>(0,J.get)(window,"wpseoAdminL10n.shortlinks-insights-word_count","")),[]);let s=(0,p._n)("word","words",e.count,"wordpress-seo"),a=(0,p.__)("Word count","wordpress-seo"),n=(0,p.__)("Learn more about word count","wordpress-seo");return"character"===e.unit&&(s=(0,p._n)("character","characters",e.count,"wordpress-seo"),a=(0,p.__)("Character count","wordpress-seo"),
/* translators: Hidden accessibility text. */
n=(0,p.__)("Learn more about character count","wordpress-seo")),(0,i.createElement)(Bs.InsightsCard,{amount:e.count,unit:s,title:a,linkTo:t,linkText:n})},Jn=Hs()(kn)`
	width: 18px;
	height: 18px;
	margin: 3px;
`,er=({location:e})=>{const t=(0,r.useSelect)((e=>e("yoast-seo/editor").getIsElementorEditor()),[]),s=(0,r.useSelect)((e=>e("yoast-seo/editor").isFleschReadingEaseAvailable()),[]),a=xn();return(0,i.createElement)(Rn,{title:(0,p.__)("Insights","wordpress-seo"),id:`yoast-insights-modal-${e}`,shouldCloseOnClickOutside:!t,showChangesWarning:!1,SuffixHeroIcon:(0,i.createElement)(Jn,{className:"yst-text-slate-500",...a})},(0,i.createElement)("div",{className:"yoast-insights yoast-modal-content--columns"},(0,i.createElement)(zn,{location:e}),(0,i.createElement)("div",null,s&&(0,i.createElement)("div",{className:"yoast-insights-row"},(0,i.createElement)(Pn,null)),(0,i.createElement)("div",{className:"yoast-insights-row yoast-insights-row--columns"},(0,i.createElement)(Cn,null),(0,i.createElement)(Xn,null)),(0,Vt.isFeatureEnabled)("TEXT_FORMALITY")&&(0,i.createElement)(Qn,{location:e,name:"YoastTextFormalityMetabox"}))))};er.propTypes={location:h().string},er.defaultProps={location:"sidebar"};const tr=er;function sr(e){return 0===e.message.length?null:(0,i.createElement)(Bs.Alert,{type:e.type},e.message)}sr.propTypes={message:h().oneOfType([h().array,h().string]).isRequired,type:h().string.isRequired};const ar=(0,r.withSelect)((e=>{const{getWarningMessage:t}=e("yoast-seo/editor");return{message:t(),type:"info"}}))(sr),nr=({children:e})=>(0,i.createElement)("div",null,e);nr.propTypes={renderPriority:h().number.isRequired,children:h().node.isRequired};const rr=nr,or=i.forwardRef((function(e,t){return i.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true",ref:t},e),i.createElement("path",{fillRule:"evenodd",d:"M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z",clipRule:"evenodd"}))})),ir=window.yoast.searchMetadataPreviews,lr=Hs()(Bs.StyledSection)`
	&${Bs.StyledSectionBase} {
		padding: 0;

		& ${Bs.StyledHeading} {
			${(0,_e.getDirectionalStyle)("padding-left","padding-right")}: 20px;
			margin-left: ${(0,_e.getDirectionalStyle)("0","20px")};
		}
	}
`,cr=({children:e,title:t,icon:s,hasPaperStyle:a,shoppingData:n})=>(0,i.createElement)(lr,{headingLevel:3,headingText:t,headingIcon:s,headingIconColor:"#555",hasPaperStyle:a,shoppingData:n},e);cr.propTypes={children:h().element,title:h().string,icon:h().string,hasPaperStyle:h().bool,shoppingData:h().object},cr.defaultProps={hasPaperStyle:!0,shoppingData:null};const dr=cr;window.wp.sanitize;const{stripHTMLTags:pr}=_e.strings,ur=(0,J.memoize)(((e,t)=>0===e?J.noop:(0,J.debounce)((s=>t(s,e)),500))),mr=({link:e,text:t})=>(0,i.createElement)(u.Root,null,(0,i.createElement)("p",null,t),(0,i.createElement)(u.Button,{href:e,as:"a",className:"yst-gap-2 yst-mb-5 yst-mt-2",variant:"upsell",target:"_blank",rel:"noopener"},(0,i.createElement)(g,{className:"yst-w-4 yst-h-4 yst--ml-1 yst-shrink-0"}),(0,p.sprintf)(/* translators: %1$s expands to Yoast WooCommerce SEO. */
(0,p.__)("Unlock with %1$s","wordpress-seo"),"Yoast WooCommerce SEO")));mr.propTypes={link:h().string.isRequired,text:h().string.isRequired};const hr=mr,gr=function(e,t){let s=0;return t.shortenedBaseUrl&&"string"==typeof t.shortenedBaseUrl&&(s=t.shortenedBaseUrl.length),e.url=e.url.replace(/\s+/g,"-"),"-"===e.url[e.url.length-1]&&(e.url=e.url.slice(0,-1)),"-"===e.url[s]&&(e.url=e.url.slice(0,s)+e.url.slice(s+1)),function(e){const t=(0,J.get)(window,["YoastSEO","app","pluggable"],!1);if(!t||!(0,J.get)(window,["YoastSEO","app","pluggable","loaded"],!1))return function(e){const t=(0,J.get)(window,["YoastSEO","wp","replaceVarsPlugin","replaceVariables"],J.identity);return{url:e.url,title:pr(t(e.title)),description:pr(t(e.description)),filteredSEOTitle:e.filteredSEOTitle?pr(t(e.filteredSEOTitle)):""}}(e);const s=t._applyModifications.bind(t);return{url:e.url,title:pr(s("data_page_title",e.title)),description:pr(s("data_meta_desc",e.description)),filteredSEOTitle:e.filteredSEOTitle?pr(s("data_page_title",e.filteredSEOTitle)):""}}(e)},yr=(0,Os.compose)([(0,r.withSelect)((function(e){const{getBaseUrlFromSettings:t,getDateFromSettings:s,getFocusKeyphrase:a,getRecommendedReplaceVars:n,getReplaceVars:r,getShoppingData:o,getSiteIconUrlFromSettings:i,getSnippetEditorData:l,getSnippetEditorMode:c,getSnippetEditorPreviewImageUrl:d,getSnippetEditorWordsToHighlight:p,isCornerstoneContent:u,getIsTerm:m,getContentLocale:h,getSiteName:g}=e("yoast-seo/editor"),y=r();return y.forEach((e=>{""!==e.value||["title","excerpt","excerpt_only"].includes(e.name)||(e.value="%%"+e.name+"%%")})),{baseUrl:t(),data:l(),date:s(),faviconSrc:i(),keyword:a(),mobileImageSrc:d(),mode:c(),recommendedReplacementVariables:n(),replacementVariables:y,shoppingData:o(),wordsToHighlight:p(),isCornerstone:u(),isTaxonomy:m(),locale:h(),siteName:g()}})),(0,r.withDispatch)((function(e,t,{select:s}){const{updateData:a,switchMode:n,updateAnalysisData:r,findCustomFields:o}=e("yoast-seo/editor"),i=e("core/editor"),l=s("yoast-seo/editor").getPostId();return{onChange:(e,t)=>{switch(e){case"mode":n(t);break;case"slug":a({slug:t}),i&&i.editPost({slug:t});break;default:a({[e]:t})}},onChangeAnalysisData:r,onReplacementVariableSearchChange:ur(l,o)}}))])((e=>{const t=(0,J.get)(window,"wpseoScriptData.metabox.woocommerceUpsellGooglePreviewLink",""),s=(0,J.get)(window,"wpseoScriptData.woocommerceUpsell",""),a=(0,p.__)("Want an enhanced Google preview of how your WooCommerce products look in the search results?","wordpress-seo");return(0,i.createElement)(Rs.LocationConsumer,null,(n=>(0,i.createElement)(dr,{icon:"eye",hasPaperStyle:e.hasPaperStyle},(0,i.createElement)(i.Fragment,null,s&&(0,i.createElement)(hr,{link:t,text:a}),(0,i.createElement)(ir.SnippetEditor,{...e,descriptionPlaceholder:(0,p.__)("Please provide a meta description by editing the snippet below.","wordpress-seo"),mapEditorDataToPreview:gr,showCloseButton:!1,idSuffix:n})))))})),{stripHTMLTags:fr}=_e.strings,wr=(e,t)=>{const s=(0,r.select)("yoast-seo/editor").getSnippetEditorTemplates();""===e.title&&(e.title=s.title),""===e.description&&(e.description=s.description);let a=0;return t.shortenedBaseUrl&&"string"==typeof t.shortenedBaseUrl&&(a=t.shortenedBaseUrl.length),e.url=e.url.replace(/\s+/g,"-"),"-"===e.url[e.url.length-1]&&(e.url=e.url.slice(0,-1)),"-"===e.url[a]&&(e.url=e.url.slice(0,a)+e.url.slice(a+1)),{url:e.url,title:fr(Ae("data_page_title",e.title)),description:fr(Ae("data_meta_desc",e.description)),filteredSEOTitle:fr(Ae("data_page_title",e.filteredSEOTitle))}},br=({isLoading:e,onLoad:t,location:s,...a})=>((0,d.useEffect)((()=>{setTimeout((()=>{e&&t()}))})),e?null:(0,i.createElement)(dr,{icon:"eye",hasPaperStyle:a.hasPaperStyle},(0,i.createElement)(ir.SnippetEditor,{...a,descriptionPlaceholder:(0,p.__)("Please provide a meta description by editing the snippet below.","wordpress-seo"),mapEditorDataToPreview:wr,showCloseButton:!1,idSuffix:s})));br.propTypes={isLoading:h().bool.isRequired,onLoad:h().func.isRequired,hasPaperStyle:h().bool.isRequired,location:h().string.isRequired};const Er=(0,Os.compose)([(0,r.withSelect)((e=>{const{getBaseUrlFromSettings:t,getDateFromSettings:s,getEditorDataImageUrl:a,getFocusKeyphrase:n,getRecommendedReplaceVars:r,getSiteIconUrlFromSettings:o,getSnippetEditorData:i,getSnippetEditorIsLoading:l,getSnippetEditorMode:c,getSnippetEditorWordsToHighlight:d,isCornerstoneContent:p,getContentLocale:u,getSiteName:m}=e("yoast-seo/editor");return{baseUrl:t(),data:i(),date:s(),faviconSrc:o(),isLoading:l(),keyword:n(),mobileImageSrc:a(),mode:c(),recommendedReplacementVariables:r(),replacementVariables:Bt(),wordsToHighlight:d(),isCornerstone:p(),locale:u(),siteName:m()}})),(0,r.withDispatch)((e=>{const{updateData:t,switchMode:s,updateAnalysisData:a,loadSnippetEditorData:n}=e("yoast-seo/editor");return{onChange:(e,a)=>{switch(e){case"mode":s(a);break;case"slug":t({slug:a});break;default:t({[e]:a})}},onChangeAnalysisData:a,onLoad:n}})),ca()])(br),vr=Hs()(or)`
	width: 18px;
	height: 18px;
	margin: 3px;
`,_r=()=>{const e=xn(),t=(0,r.useSelect)((e=>e("yoast-seo/editor").getIsElementorEditor()),[]);return(0,i.createElement)(Rn,{title:(0,p.__)("Search appearance","wordpress-seo"),id:"yoast-search-appearance-modal",shouldCloseOnClickOutside:!1,SuffixHeroIcon:(0,i.createElement)(vr,{className:"yst-text-slate-500",...e})},!0===t&&(0,i.createElement)(Er,{showCloseButton:!1,hasPaperStyle:!1}),!1===t&&(0,i.createElement)(yr,{showCloseButton:!1,hasPaperStyle:!1}))},kr=i.forwardRef((function(e,t){return i.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true",ref:t},e),i.createElement("path",{d:"M15 8a3 3 0 10-2.977-2.63l-4.94 2.47a3 3 0 100 4.319l4.94 2.47a3 3 0 10.895-1.789l-4.94-2.47a3.027 3.027 0 000-.74l4.94-2.47C13.456 7.68 14.19 8 15 8z"}))})),xr=Hs().p`
	color: #606770;
	flex-shrink: 0;
	font-size: 12px;
	line-height: 16px;
	overflow: hidden;
	padding: 0;
	text-overflow: ellipsis;
	text-transform: uppercase;
	white-space: nowrap;
	margin: 0;
	position: ${e=>"landscape"===e.mode?"relative":"static"};
`,Sr=e=>{const{siteUrl:t}=e;return(0,i.createElement)(i.Fragment,null,(0,i.createElement)("span",{className:"screen-reader-text"},t),(0,i.createElement)(xr,{"aria-hidden":"true"},(0,i.createElement)("span",null,t)))};Sr.propTypes={siteUrl:h().string.isRequired};const Tr=Sr,Rr=window.yoast.socialMetadataForms,Cr=window.yoast.styleGuide,Ir=Hs().img`
	&& {
		max-width: ${e=>e.width}px;
		height: ${e=>e.height}px;
		position: absolute;
		top: 50%;
		left: 50%;
		transform: translate(-50%, -50%);
		max-width: none;
	}
`,Lr=Hs().img`
	&&{
		height: 100%;
		position: absolute;
		width: 100%;
		object-fit: cover;
	}
`,Pr=Hs().div`
	padding-bottom: ${e=>e.aspectRatio}%;
`,Ar=e=>{const{imageProps:t,width:s,height:a,imageMode:n}=e;return"landscape"===n?(0,i.createElement)(Pr,{aspectRatio:t.aspectRatio},(0,i.createElement)(Lr,{src:t.src,alt:t.alt})):(0,i.createElement)(Ir,{src:t.src,alt:t.alt,width:s,height:a,imageProperties:t})};function Or(e,t,s){return"landscape"===s?{widthRatio:t.width/e.landscapeWidth,heightRatio:t.height/e.landscapeHeight}:"portrait"===s?{widthRatio:t.width/e.portraitWidth,heightRatio:t.height/e.portraitHeight}:{widthRatio:t.width/e.squareWidth,heightRatio:t.height/e.squareHeight}}function Mr(e,t){return t.widthRatio<=t.heightRatio?{width:Math.round(e.width/t.widthRatio),height:Math.round(e.height/t.widthRatio)}:{width:Math.round(e.width/t.heightRatio),height:Math.round(e.height/t.heightRatio)}}async function Dr(e,t,s=!1){const a=await function(e){return new Promise(((t,s)=>{const a=new Image;a.onload=()=>{t({width:a.width,height:a.height})},a.onerror=s,a.src=e}))}(e);let n=s?"landscape":"square";"Facebook"===t&&(n=(0,Rr.determineFacebookImageMode)(a));const r=function(e){return"Twitter"===e?Rr.TWITTER_IMAGE_SIZES:Rr.FACEBOOK_IMAGE_SIZES}(t),o=function(e,t,s){return"square"===s&&t.width===t.height?{width:e.squareWidth,height:e.squareHeight}:Mr(t,Or(e,t,s))}(r,a,n);return{mode:n,height:o.height,width:o.width}}async function Fr(e,t,s=!1){try{return{imageProperties:await Dr(e,t,s),status:"loaded"}}catch(e){return{imageProperties:null,status:"errored"}}}Ar.propTypes={imageProps:h().shape({src:h().string.isRequired,alt:h().string.isRequired,aspectRatio:h().number.isRequired}).isRequired,width:h().number.isRequired,height:h().number.isRequired,imageMode:h().string},Ar.defaultProps={imageMode:"landscape"};const Nr=Hs().div`
	position: relative;
	${e=>"landscape"===e.mode?`max-width: ${e.dimensions.width}`:`min-width: ${e.dimensions.width}; height: ${e.dimensions.height}`};
	overflow: hidden;
	background-color: ${Cr.colors.$color_white};
`,qr=Hs().div`
	box-sizing: border-box;
	max-width: ${Rr.FACEBOOK_IMAGE_SIZES.landscapeWidth}px;
	height: ${Rr.FACEBOOK_IMAGE_SIZES.landscapeHeight}px;
	background-color: ${Cr.colors.$color_grey};
	border-style: dashed;
	border-width: 1px;
	// We're not using standard colors to increase contrast for accessibility.
	color: #006DAC;
	// We're not using standard colors to increase contrast for accessibility.
	background-color: #f1f1f1;
	display: flex;
	justify-content: center;
	align-items: center;
	text-decoration: underline;
	font-size: 14px;
	cursor: pointer;
`;class Ur extends i.Component{constructor(e){super(e),this.state={imageProperties:null,status:"loading"},this.socialMedium="Facebook",this.handleFacebookImage=this.handleFacebookImage.bind(this),this.setState=this.setState.bind(this)}async handleFacebookImage(){try{const e=await Fr(this.props.src,this.socialMedium);this.setState(e),this.props.onImageLoaded(e.imageProperties.mode||"landscape")}catch(e){this.setState(e),this.props.onImageLoaded("landscape")}}componentDidUpdate(e){e.src!==this.props.src&&this.handleFacebookImage()}componentDidMount(){this.handleFacebookImage()}retrieveContainerDimensions(e){switch(e){case"square":return{height:Rr.FACEBOOK_IMAGE_SIZES.squareHeight+"px",width:Rr.FACEBOOK_IMAGE_SIZES.squareWidth+"px"};case"portrait":return{height:Rr.FACEBOOK_IMAGE_SIZES.portraitHeight+"px",width:Rr.FACEBOOK_IMAGE_SIZES.portraitWidth+"px"};case"landscape":return{height:Rr.FACEBOOK_IMAGE_SIZES.landscapeHeight+"px",width:Rr.FACEBOOK_IMAGE_SIZES.landscapeWidth+"px"}}}render(){const{imageProperties:e,status:t}=this.state;if("loading"===t||""===this.props.src||"errored"===t)return(0,i.createElement)(qr,{onClick:this.props.onImageClick,onMouseEnter:this.props.onMouseEnter,onMouseLeave:this.props.onMouseLeave},(0,p.__)("Select image","wordpress-seo"));const s=this.retrieveContainerDimensions(e.mode);return(0,i.createElement)(Nr,{mode:e.mode,dimensions:s,onMouseEnter:this.props.onMouseEnter,onMouseLeave:this.props.onMouseLeave,onClick:this.props.onImageClick},(0,i.createElement)(Ar,{imageProps:{src:this.props.src,alt:this.props.alt,aspectRatio:Rr.FACEBOOK_IMAGE_SIZES.aspectRatio},width:e.width,height:e.height,imageMode:e.mode}))}}Ur.propTypes={src:h().string,alt:h().string,onImageLoaded:h().func,onImageClick:h().func,onMouseEnter:h().func,onMouseLeave:h().func},Ur.defaultProps={src:"",alt:"",onImageLoaded:J.noop,onImageClick:J.noop,onMouseEnter:J.noop,onMouseLeave:J.noop};const Br=Ur,$r=Hs().span`
	line-height: ${20}px;
	min-height : ${20}px;
	color: #1d2129;
	font-weight: 600;
	overflow: hidden;
	font-size: 16px;
	margin: 3px 0 0;
	letter-spacing: normal;
	white-space: normal;
	flex-shrink: 0;
	cursor: pointer;
	display: -webkit-box;
	-webkit-line-clamp: ${e=>e.lineCount};
	-webkit-box-orient: vertical;
	overflow: hidden;
`,Wr=Hs().p`
	line-height: ${16}px;
	min-height : ${16}px;
	color: #606770;
	font-size: 14px;
	padding: 0;
	text-overflow: ellipsis;
	margin: 3px 0 0 0;
	display: -webkit-box;
	cursor: pointer;
	-webkit-line-clamp: ${e=>e.lineCount};
	-webkit-box-orient: vertical;
	overflow: hidden;

	@media all and ( max-width: ${e=>e.maxWidth} ) {
		display: none;
	}
`,Kr=e=>{switch(e){case"landscape":return"527px";case"square":case"portrait":return"369px";default:return"476px"}},Hr=Hs().div`
	box-sizing: border-box;
	display: flex;
	flex-direction: ${e=>"landscape"===e.mode?"column":"row"};
	background-color: #f2f3f5;
	max-width: 527px;
`,Yr=Hs().div`
	box-sizing: border-box;
	background-color: #f2f3f5;
	margin: 0;
	padding: 10px 12px;
	position: relative;
	border-bottom: ${e=>"landscape"===e.mode?"":"1px solid #dddfe2"};
	border-top: ${e=>"landscape"===e.mode?"":"1px solid #dddfe2"};
	border-right: ${e=>"landscape"===e.mode?"":"1px solid #dddfe2"};
	border: ${e=>"landscape"===e.mode?"1px solid #dddfe2":""};
	display: flex;
	flex-direction: column;
	flex-grow: 1;
	justify-content: ${e=>"landscape"===e.mode?"flex-start":"center"};
	font-size: 12px;
	overflow: hidden;
`;class zr extends i.Component{constructor(e){super(e),this.state={imageMode:null,maxLineCount:0,descriptionLineCount:0},this.facebookTitleRef=l().createRef(),this.onImageLoaded=this.onImageLoaded.bind(this),this.onImageEnter=this.props.onMouseHover.bind(this,"image"),this.onTitleEnter=this.props.onMouseHover.bind(this,"title"),this.onDescriptionEnter=this.props.onMouseHover.bind(this,"description"),this.onLeave=this.props.onMouseHover.bind(this,""),this.onSelectTitle=this.props.onSelect.bind(this,"title"),this.onSelectDescription=this.props.onSelect.bind(this,"description")}onImageLoaded(e){this.setState({imageMode:e})}getTitleLineCount(){return this.facebookTitleRef.current.offsetHeight/20}maybeSetMaxLineCount(){const{imageMode:e,maxLineCount:t}=this.state,s="landscape"===e?2:5;s!==t&&this.setState({maxLineCount:s})}maybeSetDescriptionLineCount(){const{descriptionLineCount:e,maxLineCount:t,imageMode:s}=this.state,a=this.getTitleLineCount();let n=t-a;"portrait"===s&&(n=5===a?0:4),n!==e&&this.setState({descriptionLineCount:n})}componentDidUpdate(){this.maybeSetMaxLineCount(),this.maybeSetDescriptionLineCount()}render(){const{imageMode:e,maxLineCount:t,descriptionLineCount:s}=this.state;return(0,i.createElement)(Hr,{id:"facebookPreview",mode:e},(0,i.createElement)(Br,{src:this.props.imageUrl||this.props.imageFallbackUrl,alt:this.props.alt,onImageLoaded:this.onImageLoaded,onImageClick:this.props.onImageClick,onMouseEnter:this.onImageEnter,onMouseLeave:this.onLeave}),(0,i.createElement)(Yr,{mode:e},(0,i.createElement)(Tr,{siteUrl:this.props.siteUrl,mode:e}),(0,i.createElement)($r,{ref:this.facebookTitleRef,onMouseEnter:this.onTitleEnter,onMouseLeave:this.onLeave,onClick:this.onSelectTitle,lineCount:t},this.props.title),s>0&&(0,i.createElement)(Wr,{maxWidth:Kr(e),onMouseEnter:this.onDescriptionEnter,onMouseLeave:this.onLeave,onClick:this.onSelectDescription,lineCount:s},this.props.description)))}}zr.propTypes={siteUrl:h().string.isRequired,title:h().string.isRequired,description:h().string,imageUrl:h().string,imageFallbackUrl:h().string,alt:h().string,onSelect:h().func,onImageClick:h().func,onMouseHover:h().func},zr.defaultProps={description:"",alt:"",imageUrl:"",imageFallbackUrl:"",onSelect:()=>{},onImageClick:()=>{},onMouseHover:()=>{}};const jr=zr,Vr=Hs().div`
	text-transform: lowercase;
	color: rgb(83, 100, 113);
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
	margin: 0;
	fill: currentcolor;
	display: flex;
	flex-direction: row;
	align-items: flex-end;
`,Gr=e=>(0,i.createElement)(Vr,null,(0,i.createElement)("span",null,e.siteUrl));Gr.propTypes={siteUrl:h().string.isRequired};const Zr=Gr,Qr=(e,t=!0)=>e?`\n\t\t\tmax-width: ${Rr.TWITTER_IMAGE_SIZES.landscapeWidth}px;\n\t\t\t${t?"border-bottom: 1px solid #E1E8ED;":""}\n\t\t\tborder-radius: 14px 14px 0 0;\n\t\t\t`:`\n\t\twidth: ${Rr.TWITTER_IMAGE_SIZES.squareWidth}px;\n\t\t${t?"border-right: 1px solid #E1E8ED;":""}\n\t\tborder-radius: 14px 0 0 14px;\n\t\t`,Xr=Hs().div`
	position: relative;
	box-sizing: content-box;
	overflow: hidden;
	background-color: #e1e8ed;
	flex-shrink: 0;
	${e=>Qr(e.isLarge)}
`,Jr=Hs().div`
	display: flex;
	justify-content: center;
	align-items: center;
	box-sizing: border-box;
	max-width: 100%;
	margin: 0;
	padding: 1em;
	text-align: center;
	font-size: 1rem;
	${e=>Qr(e.isLarge,!1)}
`,eo=Hs()(Jr)`
	${e=>e.isLarge&&`height: ${Rr.TWITTER_IMAGE_SIZES.landscapeHeight}px;`}
	border-top-left-radius: 14px;
	${e=>e.isLarge?"border-top-right-radius":"border-bottom-left-radius"}: 14px;
	border-style: dashed;
	border-width: 1px;
	// We're not using standard colors to increase contrast for accessibility.
	color: #006DAC;
	// We're not using standard colors to increase contrast for accessibility.
	background-color: #f1f1f1;
	text-decoration: underline;
	font-size: 14px;
	cursor: pointer;
`;class to extends l().Component{constructor(e){super(e),this.state={status:"loading"},this.socialMedium="Twitter",this.handleTwitterImage=this.handleTwitterImage.bind(this),this.setState=this.setState.bind(this)}async handleTwitterImage(){if(null===this.props.src)return;const e=await Fr(this.props.src,this.socialMedium,this.props.isLarge);this.setState(e)}componentDidUpdate(e){e.src!==this.props.src&&this.handleTwitterImage()}componentDidMount(){this.handleTwitterImage()}render(){const{status:e,imageProperties:t}=this.state;return"loading"===e||""===this.props.src||"errored"===e?(0,i.createElement)(eo,{isLarge:this.props.isLarge,onClick:this.props.onImageClick,onMouseEnter:this.props.onMouseEnter,onMouseLeave:this.props.onMouseLeave},(0,p.__)("Select image","wordpress-seo")):(0,i.createElement)(Xr,{isLarge:this.props.isLarge,onClick:this.props.onImageClick,onMouseEnter:this.props.onMouseEnter,onMouseLeave:this.props.onMouseLeave},(0,i.createElement)(Ar,{imageProps:{src:this.props.src,alt:this.props.alt,aspectRatio:Rr.TWITTER_IMAGE_SIZES.aspectRatio},width:t.width,height:t.height,imageMode:t.mode}))}}to.propTypes={isLarge:h().bool.isRequired,src:h().string,alt:h().string,onImageClick:h().func,onMouseEnter:h().func,onMouseLeave:h().func},to.defaultProps={src:"",alt:"",onMouseEnter:J.noop,onImageClick:J.noop,onMouseLeave:J.noop};const so=Hs().div`
	display: flex;
	flex-direction: column;
	padding: 12px;
	justify-content: center;
	margin: 0;
	box-sizing: border-box;
	flex: auto;
	min-width: 0px;
	gap:2px;
	> * {
		line-height:20px;
		min-height:20px;
		font-size:15px;
    }
`,ao=e=>(0,i.createElement)(so,null,e.children);ao.propTypes={children:h().array.isRequired};const no=ao,ro=Hs().p`
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
	margin: 0;
	color: rgb(15, 20, 25);
	cursor: pointer;
`,oo=Hs().p`
	max-height: 55px;
	overflow: hidden;
	text-overflow: ellipsis;
	margin: 0;
	color: rgb(83, 100, 113);
	display: -webkit-box;
	cursor: pointer;
	-webkit-line-clamp: 2;
	-webkit-box-orient: vertical;

	@media all and ( max-width: ${Rr.TWITTER_IMAGE_SIZES.landscapeWidth}px ) {
		display: none;
	}
`,io=Hs().div`
	font-family: system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Ubuntu, "Helvetica Neue", sans-serif;
	font-size: 15px;
	font-weight: 400;
	line-height: 20px;
	max-width: 507px;
	border: 1px solid #E1E8ED;
	box-sizing: border-box;
	border-radius: 14px;
	color: #292F33;
	background: #FFFFFF;
	text-overflow: ellipsis;
	display: flex;

	&:hover {
		background: #f5f8fa;
		border: 1px solid rgba(136,153,166,.5);
	}
`,lo=Hs()(io)`
	flex-direction: column;
	max-height: 370px;
`,co=Hs()(io)`
	flex-direction: row;
	height: 125px;
`;class po extends i.Component{constructor(e){super(e),this.onImageEnter=this.props.onMouseHover.bind(this,"image"),this.onTitleEnter=this.props.onMouseHover.bind(this,"title"),this.onDescriptionEnter=this.props.onMouseHover.bind(this,"description"),this.onLeave=this.props.onMouseHover.bind(this,""),this.onSelectTitle=this.props.onSelect.bind(this,"title"),this.onSelectDescription=this.props.onSelect.bind(this,"description")}render(){const{isLarge:e,imageUrl:t,imageFallbackUrl:s,alt:a,title:n,description:r,siteUrl:o}=this.props,l=e?lo:co;return(0,i.createElement)(l,{id:"twitterPreview"},(0,i.createElement)(to,{src:t||s,alt:a,isLarge:e,onImageClick:this.props.onImageClick,onMouseEnter:this.onImageEnter,onMouseLeave:this.onLeave}),(0,i.createElement)(no,null,(0,i.createElement)(Zr,{siteUrl:o}),(0,i.createElement)(ro,{onMouseEnter:this.onTitleEnter,onMouseLeave:this.onLeave,onClick:this.onSelectTitle},n),(0,i.createElement)(oo,{onMouseEnter:this.onDescriptionEnter,onMouseLeave:this.onLeave,onClick:this.onSelectDescription},r)))}}po.propTypes={siteUrl:h().string.isRequired,title:h().string.isRequired,description:h().string,isLarge:h().bool,imageUrl:h().string,imageFallbackUrl:h().string,alt:h().string,onSelect:h().func,onImageClick:h().func,onMouseHover:h().func},po.defaultProps={description:"",alt:"",imageUrl:"",imageFallbackUrl:"",onSelect:()=>{},onImageClick:()=>{},onMouseHover:()=>{},isLarge:!0};const uo=po,mo=window.yoast.replacementVariableEditor;class ho extends i.Component{constructor(e){super(e),this.state={activeField:"",hoveredField:""},this.SocialPreview="Social"===e.socialMediumName?jr:uo,this.setHoveredField=this.setHoveredField.bind(this),this.setActiveField=this.setActiveField.bind(this),this.setEditorRef=this.setEditorRef.bind(this),this.setEditorFocus=this.setEditorFocus.bind(this)}setHoveredField(e){e!==this.state.hoveredField&&this.setState({hoveredField:e})}setActiveField(e){e!==this.state.activeField&&this.setState({activeField:e},(()=>this.setEditorFocus(e)))}setEditorFocus(e){switch(e){case"title":this.titleEditorRef.focus();break;case"description":this.descriptionEditorRef.focus()}}setEditorRef(e,t){switch(e){case"title":this.titleEditorRef=t;break;case"description":this.descriptionEditorRef=t}}render(){const{onDescriptionChange:e,onTitleChange:t,onSelectImageClick:s,onRemoveImageClick:a,socialMediumName:n,imageWarnings:r,siteUrl:o,description:c,descriptionInputPlaceholder:d,descriptionPreviewFallback:p,imageUrl:u,imageFallbackUrl:m,alt:h,title:g,titleInputPlaceholder:y,titlePreviewFallback:f,replacementVariables:w,recommendedReplacementVariables:b,applyReplacementVariables:E,onReplacementVariableSearchChange:v,isPremium:_,isLarge:k,socialPreviewLabel:x,idSuffix:S,activeMetaTabId:T}=this.props,R=E({title:g||f,description:c||p});return(0,i.createElement)(l().Fragment,null,x&&(0,i.createElement)(Bs.SimulatedLabel,null,x),(0,i.createElement)(this.SocialPreview,{onMouseHover:this.setHoveredField,onSelect:this.setActiveField,onImageClick:s,siteUrl:o,title:R.title,description:R.description,imageUrl:u,imageFallbackUrl:m,alt:h,isLarge:k,activeMetaTabId:T}),(0,i.createElement)(Rr.SocialMetadataPreviewForm,{onDescriptionChange:e,socialMediumName:n,title:g,titleInputPlaceholder:y,onRemoveImageClick:a,imageSelected:!!u,imageUrl:u,onTitleChange:t,onSelectImageClick:s,description:c,descriptionInputPlaceholder:d,imageWarnings:r,replacementVariables:w,recommendedReplacementVariables:b,onReplacementVariableSearchChange:v,onMouseHover:this.setHoveredField,hoveredField:this.state.hoveredField,onSelect:this.setActiveField,activeField:this.state.activeField,isPremium:_,setEditorRef:this.setEditorRef,idSuffix:S}))}}ho.propTypes={title:h().string.isRequired,onTitleChange:h().func.isRequired,description:h().string.isRequired,onDescriptionChange:h().func.isRequired,imageUrl:h().string.isRequired,imageFallbackUrl:h().string.isRequired,onSelectImageClick:h().func.isRequired,onRemoveImageClick:h().func.isRequired,socialMediumName:h().string.isRequired,alt:h().string,isPremium:h().bool,imageWarnings:h().array,isLarge:h().bool,siteUrl:h().string,descriptionInputPlaceholder:h().string,titleInputPlaceholder:h().string,descriptionPreviewFallback:h().string,titlePreviewFallback:h().string,replacementVariables:mo.replacementVariablesShape,recommendedReplacementVariables:mo.recommendedReplacementVariablesShape,applyReplacementVariables:h().func,onReplacementVariableSearchChange:h().func,socialPreviewLabel:h().string,idSuffix:h().string,activeMetaTabId:h().string},ho.defaultProps={imageWarnings:[],recommendedReplacementVariables:[],replacementVariables:[],isPremium:!1,isLarge:!0,siteUrl:"",descriptionInputPlaceholder:"",titleInputPlaceholder:"",descriptionPreviewFallback:"",titlePreviewFallback:"",alt:"",applyReplacementVariables:e=>e,onReplacementVariableSearchChange:null,socialPreviewLabel:"",idSuffix:"",activeMetaTabId:""};const go={},yo=(e,t,{log:s=console.warn}={})=>{go[e]||(go[e]=!0,s(t))},fo=(e,t=J.noop)=>{const s={};for(const a in e)Object.hasOwn(e,a)&&Object.defineProperty(s,a,{set:s=>{e[a]=s,t("set",a,s)},get:()=>(t("get",a),e[a])});return s};fo({squareWidth:125,squareHeight:125,landscapeWidth:506,landscapeHeight:265,aspectRatio:50.2},((e,t)=>yo(`@yoast/social-metadata-previews/TWITTER_IMAGE_SIZES/${e}/${t}`,`[@yoast/social-metadata-previews] "TWITTER_IMAGE_SIZES.${t}" is deprecated and will be removed in the future, please use this from @yoast/social-metadata-forms instead.`))),fo({squareWidth:158,squareHeight:158,landscapeWidth:527,landscapeHeight:273,portraitWidth:158,portraitHeight:237,aspectRatio:52.2,largeThreshold:{width:446,height:233}},((e,t)=>yo(`@yoast/social-metadata-previews/FACEBOOK_IMAGE_SIZES/${e}/${t}`,`[@yoast/social-metadata-previews] "FACEBOOK_IMAGE_SIZES.${t}" is deprecated and will be removed in the future, please use this from @yoast/social-metadata-forms instead.`)));const wo=Hs().div`
	max-width: calc(527px + 1.5rem);
`,bo=e=>{const t="X"===e.socialMediumName?(0,p.__)("X share preview","wordpress-seo"):(0,p.__)("Social share preview","wordpress-seo"),{locationContext:s}=(0,u.useRootContext)();return(0,i.createElement)(u.Root,null,(0,i.createElement)(wo,null,(0,i.createElement)(u.FeatureUpsell,{shouldUpsell:!0,variant:"card",cardLink:(0,te.addQueryArgs)(wpseoAdminL10n["shortlinks.upsell.social_preview."+e.socialMediumName.toLowerCase()],{context:s}),cardText:(0,p.sprintf)(/* translators: %1$s expands to Yoast SEO Premium. */
(0,p.__)("Unlock with %1$s","wordpress-seo"),"Yoast SEO Premium"),"data-action":"load-nfd-ctb","data-ctb-id":"f6a84663-465f-4cb5-8ba5-f7a6d72224b2"},(0,i.createElement)("div",{className:"yst-grayscale yst-opacity-50"},(0,i.createElement)(u.Label,null,t),(0,i.createElement)(jr,{title:"",description:"",siteUrl:"",imageUrl:"",imageFallbackUrl:"",alt:"",onSelect:J.noop,onImageClick:J.noop,onMouseHover:J.noop})))))};bo.propTypes={socialMediumName:h().oneOf(["Social","Twitter","X"]).isRequired};const Eo=bo;class vo extends d.Component{constructor(e){super(e),this.state={activeField:"",hoveredField:""},this.setHoveredField=this.setHoveredField.bind(this),this.setActiveField=this.setActiveField.bind(this),this.setEditorRef=this.setEditorRef.bind(this),this.setEditorFocus=this.setEditorFocus.bind(this)}setHoveredField(e){e!==this.state.hoveredField&&this.setState({hoveredField:e})}setActiveField(e){e!==this.state.activeField&&this.setState({activeField:e},(()=>this.setEditorFocus(e)))}setEditorFocus(e){switch(e){case"title":this.titleEditorRef.focus();break;case"description":this.descriptionEditorRef.focus()}}setEditorRef(e,t){switch(e){case"title":this.titleEditorRef=t;break;case"description":this.descriptionEditorRef=t}}render(){const{onDescriptionChange:e,onTitleChange:t,onSelectImageClick:s,onRemoveImageClick:a,socialMediumName:n,imageWarnings:r,description:o,descriptionInputPlaceholder:l,imageUrl:c,alt:p,title:u,titleInputPlaceholder:m,replacementVariables:h,recommendedReplacementVariables:g,onReplacementVariableSearchChange:y,isPremium:f,location:w}=this.props;return(0,i.createElement)(d.Fragment,null,(0,i.createElement)(Eo,{socialMediumName:n}),(0,i.createElement)(Rr.SocialMetadataPreviewForm,{onDescriptionChange:e,socialMediumName:n,title:u,titleInputPlaceholder:m,onRemoveImageClick:a,imageSelected:!!c,imageUrl:c,imageAltText:p,onTitleChange:t,onSelectImageClick:s,description:o,descriptionInputPlaceholder:l,imageWarnings:r,replacementVariables:h,recommendedReplacementVariables:g,onReplacementVariableSearchChange:y,onMouseHover:this.setHoveredField,hoveredField:this.state.hoveredField,onSelect:this.setActiveField,activeField:this.state.activeField,isPremium:f,setEditorRef:this.setEditorRef,idSuffix:w}))}}vo.propTypes={title:h().string.isRequired,onTitleChange:h().func.isRequired,description:h().string.isRequired,onDescriptionChange:h().func.isRequired,imageUrl:h().string.isRequired,onSelectImageClick:h().func.isRequired,onRemoveImageClick:h().func.isRequired,socialMediumName:h().string.isRequired,isPremium:h().bool,imageWarnings:h().array,descriptionInputPlaceholder:h().string,titleInputPlaceholder:h().string,replacementVariables:mo.replacementVariablesShape,recommendedReplacementVariables:mo.recommendedReplacementVariablesShape,onReplacementVariableSearchChange:h().func,location:h().string,alt:h().string},vo.defaultProps={imageWarnings:[],recommendedReplacementVariables:[],replacementVariables:[],isPremium:!1,descriptionInputPlaceholder:"",titleInputPlaceholder:"",onReplacementVariableSearchChange:null,location:"",alt:""};const _o=vo,ko=e=>{const[t,s]=(0,d.useState)(""),a=(0,d.useCallback)((e=>{s(e.detail.metaTabId)}),[s]);(0,d.useEffect)((()=>(setTimeout(e.onLoad),window.addEventListener("YoastSEO:metaTabChange",a),()=>{window.removeEventListener("YoastSEO:metaTabChange",a)})),[]);const n=(0,d.useMemo)((()=>({...e,activeMetaTabId:t})),[e,t]);return e.isPremium?(0,i.createElement)(c.Slot,{name:`YoastFacebookPremium${e.location.charAt(0).toUpperCase()+e.location.slice(1)}`,fillProps:n}):(0,i.createElement)(_o,{...n})};ko.propTypes={isPremium:h().bool.isRequired,onLoad:h().func.isRequired,location:h().string.isRequired};const xo=ko;function So(e){(function(e){const t=window.wp.media();return t.on("select",(()=>{const s=t.state().get("selection").first();var a;e({type:(a=s.attributes).subtype,width:a.width,height:a.height,url:a.url,id:a.id,sizes:a.sizes,alt:a.alt||a.title||a.name})})),t})(e).open()}const To=()=>{So((e=>(0,r.dispatch)("yoast-seo/editor").setFacebookPreviewImage((e=>{const{width:t,height:s}=e,a=(0,Rr.determineFacebookImageMode)({width:t,height:s}),n=Rr.FACEBOOK_IMAGE_SIZES[a+"Width"],r=Rr.FACEBOOK_IMAGE_SIZES[a+"Height"],o=Object.values(e.sizes).find((e=>e.width>=n&&e.height>=r));return{url:o?o.url:e.url,id:e.id,warnings:(0,_e.validateFacebookImage)(e),alt:e.alt||""}})(e))))},Ro=(0,Os.compose)([(0,r.withSelect)((e=>{const{getFacebookDescription:t,getDescription:s,getFacebookTitle:a,getSeoTitle:n,getFacebookImageUrl:r,getImageFallback:o,getFacebookWarnings:i,getRecommendedReplaceVars:l,getReplaceVars:c,getSiteUrl:d,getSeoTitleTemplate:p,getSeoTitleTemplateNoFallback:u,getSocialTitleTemplate:m,getSeoDescriptionTemplate:h,getSocialDescriptionTemplate:g,getReplacedExcerpt:y,getFacebookAltText:f}=e("yoast-seo/editor");return{imageUrl:r(),imageFallbackUrl:o(),recommendedReplacementVariables:l(),replacementVariables:c(),description:t(),descriptionPreviewFallback:g()||s()||h()||y()||"",title:a(),titlePreviewFallback:m()||n()||u()||p()||"",imageWarnings:i(),siteUrl:d(),isPremium:!!ze().isPremium,titleInputPlaceholder:"",descriptionInputPlaceholder:"",socialMediumName:"Social",alt:f()}})),(0,r.withDispatch)(((e,t,{select:s})=>{const{setFacebookPreviewTitle:a,setFacebookPreviewDescription:n,clearFacebookPreviewImage:r,loadFacebookPreviewData:o,findCustomFields:i}=e("yoast-seo/editor"),l=s("yoast-seo/editor").getPostId();return{onSelectImageClick:To,onRemoveImageClick:r,onDescriptionChange:n,onTitleChange:a,onLoad:o,onReplacementVariableSearchChange:ur(l,i)}})),ca()])(xo),Co=e=>((0,d.useEffect)((()=>{setTimeout(e.onLoad)}),[]),e.isPremium?(0,i.createElement)(c.Slot,{name:`YoastTwitterPremium${e.location.charAt(0).toUpperCase()+e.location.slice(1)}`,fillProps:e}):(0,i.createElement)(_o,{...e}));Co.propTypes={isPremium:h().bool.isRequired,onLoad:h().func.isRequired,location:h().string.isRequired};const Io=Co,Lo=()=>{So((e=>(0,r.dispatch)("yoast-seo/editor").setTwitterPreviewImage((e=>{const t="summary"!==(0,J.get)(window,"wpseoScriptData.metabox.twitterCardType")?"landscape":"square",s=Rr.TWITTER_IMAGE_SIZES[t+"Width"],a=Rr.TWITTER_IMAGE_SIZES[t+"Height"],n=Object.values(e.sizes).find((e=>e.width>=s&&e.height>=a));return{url:n?n.url:e.url,id:e.id,warnings:(0,_e.validateTwitterImage)(e),alt:e.alt||""}})(e))))},Po=(0,Os.compose)([(0,r.withSelect)((e=>{const{getTwitterDescription:t,getTwitterTitle:s,getTwitterImageUrl:a,getFacebookImageUrl:n,getFacebookTitle:r,getFacebookDescription:o,getDescription:i,getSeoTitle:l,getTwitterWarnings:c,getTwitterImageType:d,getImageFallback:p,getRecommendedReplaceVars:u,getReplaceVars:m,getSiteUrl:h,getSeoTitleTemplate:g,getSeoTitleTemplateNoFallback:y,getSocialTitleTemplate:f,getSeoDescriptionTemplate:w,getSocialDescriptionTemplate:b,getReplacedExcerpt:E,getTwitterAltText:v}=e("yoast-seo/editor");return{imageUrl:a(),imageFallbackUrl:n()||p(),recommendedReplacementVariables:u(),replacementVariables:m(),description:t(),descriptionPreviewFallback:b()||o()||i()||w()||E()||"",title:s(),titlePreviewFallback:f()||r()||l()||y()||g()||"",imageWarnings:c(),siteUrl:h(),isPremium:!!ze().isPremium,isLarge:"summary"!==d(),titleInputPlaceholder:"",descriptionInputPlaceholder:"",socialMediumName:"X",alt:v()}})),(0,r.withDispatch)(((e,t,{select:s})=>{const{setTwitterPreviewTitle:a,setTwitterPreviewDescription:n,clearTwitterPreviewImage:r,loadTwitterPreviewData:o,findCustomFields:i}=e("yoast-seo/editor"),l=s("yoast-seo/editor").getPostId();return{onSelectImageClick:Lo,onRemoveImageClick:r,onDescriptionChange:n,onTitleChange:a,onLoad:o,onReplacementVariableSearchChange:ur(l,i)}})),ca()])(Io),Ao=Hs()(Bs.Collapsible)`
	h2 > button {
		padding-left: 0;
		padding-top: 16px;

		&:hover {
			background-color: #f0f0f0;
		}
	}

	div[class^="collapsible_content"] {
		padding: 24px 0;
		margin: 0 24px;
		border-top: 1px solid rgba(0,0,0,0.2);
	}

`,Oo=e=>(0,i.createElement)(Ao,{hasPadding:!1,hasSeparator:!0,...e}),Mo=Hs().legend`
	margin: 16px 0;
	padding: 0;
	color: ${Cr.colors.$color_headings};
	font-size: 12px;
	font-weight: 300;
`,Do=Hs().legend`
	margin: 0 0 16px;
	padding: 0;
	color: ${Cr.colors.$color_headings};
	font-size: 12px;
	font-weight: 300;
`,Fo=Hs()(kr)`
	width: 18px;
	height: 18px;
	margin: 3px;
`,No=e=>{const{useOpenGraphData:t,useTwitterData:s}=e;if(!t&&!s)return;const a=xn();return(0,i.createElement)(Rn
/* translators: Social media appearance refers to a preview of how a page will be represented on social media. */,{title:(0,p.__)("Social media appearance","wordpress-seo"),id:"yoast-social-appearance-modal",shouldCloseOnClickOutside:!1,SuffixHeroIcon:(0,i.createElement)(Fo,{className:"yst-text-slate-500",...a})},t&&(0,i.createElement)(d.Fragment,null,(0,i.createElement)(Do,null,(0,p.__)("Determine how your post should look on social media like Facebook, X, Instagram, WhatsApp, Threads, LinkedIn, Slack, and more.","wordpress-seo")),(0,i.createElement)(Ro,null),s&&(0,i.createElement)(Mo,null,(0,p.__)("To customize the appearance of your post specifically for X, please fill out the 'X appearance' settings below. If you leave these settings untouched, the 'Social media appearance' settings mentioned above will also be applied for sharing on X.","wordpress-seo"))),t&&s&&(0,i.createElement)(Oo,{title:(0,p.__)("X appearance","wordpress-seo"),hasSeparator:!0,initialIsOpen:!1},(0,i.createElement)(Po,null)),!t&&s&&(0,i.createElement)(d.Fragment,null,(0,i.createElement)(Do,null,(0,p.__)("To customize the appearance of your post specifically for X, please fill out the 'X appearance' settings below.","wordpress-seo")),(0,i.createElement)(Po,null)))};No.propTypes={useOpenGraphData:h().bool.isRequired,useTwitterData:h().bool.isRequired};const qo=No,Uo=(0,p.__)("Check your text on even more SEO criteria and get an enhanced keyphrase analysis, making it easier to optimize your content.","wordpress-seo"),Bo=e=>{const{locationContext:t}=(0,Rs.useRootContext)(),s=(0,te.addQueryArgs)(wpseoAdminL10n[e.buyLink],{context:t});return(0,i.createElement)(ta,{title:(0,p.__)("Get more help with writing content that ranks","wordpress-seo"),description:e.description,benefitsTitle:/* translators: %s expands to 'Yoast SEO Premium'. */
(0,p.sprintf)("%s also gives you:","Yoast SEO Premium"),benefits:Ds(),upsellButtonText:(0,p.sprintf)(/* translators: %s expands to 'Yoast SEO Premium'. */
(0,p.__)("Unlock with %s","wordpress-seo"),"Yoast SEO Premium"),upsellButton:{href:s,className:"yoast-button-upsell",rel:null,"data-ctb-id":"f6a84663-465f-4cb5-8ba5-f7a6d72224b2","data-action":"load-nfd-ctb"},upsellButtonLabel:(0,p.__)("1 year of premium support and updates included!","wordpress-seo")})};Bo.propTypes={buyLink:h().string.isRequired,description:h().string},Bo.defaultProps={description:Uo};const $o=Bo,Wo=({location:e})=>{const[t,s]=(0,d.useState)(!1),a=(0,d.useCallback)((()=>s(!1)),[]),n=(0,d.useCallback)((()=>s(!0)),[]),r=(0,u.useSvgAria)();return(0,i.createElement)(d.Fragment,null,t&&(0,i.createElement)(oa,{title:(0,p.__)("Unlock Premium SEO analysis","wordpress-seo"),onRequestClose:a,additionalClassName:"",className:`${na} yoast-gutenberg-modal__box yoast-gutenberg-modal__no-padding`,id:"yoast-premium-seo-analysis-modal",shouldCloseOnClickOutside:!0},(0,i.createElement)(aa,null,(0,i.createElement)($o,{buyLink:`shortlinks.upsell.${e}.premium_seo_analysis_button`}))),"sidebar"===e&&(0,i.createElement)(Ws,{id:"yoast-premium-seo-analysis-modal-open-button",title:(0,p.__)("Premium SEO analysis","wordpress-seo"),prefixIcon:{icon:"seo-score-none",color:Cr.colors.$color_grey},onClick:n},(0,i.createElement)("div",{className:"yst-root"},(0,i.createElement)(u.Badge,{size:"small",variant:"upsell"},(0,i.createElement)(Ms,{className:"yst-w-2.5 yst-h-2.5 yst-shrink-0",...r})))),"metabox"===e&&(0,i.createElement)("div",{className:"yst-root"},(0,i.createElement)(Us,{id:"yoast-premium-seo-analysis-metabox-modal-open-button",onClick:n},(0,i.createElement)(Bs.SvgIcon,{icon:"seo-score-none",color:Cr.colors.$color_grey}),(0,i.createElement)(Us.Text,null,(0,p.__)("Premium SEO analysis","wordpress-seo")),(0,i.createElement)(u.Badge,{size:"small",variant:"upsell"},(0,i.createElement)(Ms,{className:"yst-w-2.5 yst-h-2.5 yst-mr-1 yst-shrink-0",...r}),(0,i.createElement)("span",null,"Premium")))))};Wo.propTypes={location:h().string},Wo.defaultProps={location:"sidebar"};const Ko=Wo,Ho=e=>{const[t,s]=(0,d.useState)(!1),{prefixIcon:a}=e;return(0,i.createElement)("div",{className:"yoast components-panel__body "+(t?"is-opened":"")},(0,i.createElement)("h2",{className:"components-panel__body-title"},(0,i.createElement)("button",{onClick:function(){s(!t)},className:"components-button components-panel__body-toggle",type:"button",id:e.buttonId},(0,i.createElement)("span",{className:"yoast-icon-span",style:{fill:`${a&&a.color||""}`}},a&&(0,i.createElement)(Bs.SvgIcon,{icon:a.icon,color:a.color,size:a.size})),(0,i.createElement)("span",{className:"yoast-title-container"},(0,i.createElement)("div",{className:"yoast-title"},e.title),(0,i.createElement)("div",{className:"yoast-subtitle"},e.subTitle)),e.hasBetaBadgeLabel&&(0,i.createElement)(Bs.BetaBadge,null),(0,i.createElement)("span",{className:"yoast-chevron","aria-hidden":"true"}))),t&&e.children)},Yo=Ho;Ho.propTypes={title:h().string.isRequired,children:h().oneOfType([h().node,h().arrayOf(h().node)]).isRequired,prefixIcon:h().object,subTitle:h().string,hasBetaBadgeLabel:h().bool,buttonId:h().string},Ho.defaultProps={prefixIcon:null,subTitle:"",hasBetaBadgeLabel:!1,buttonId:null};var zo=s(6746);const jo=(0,_e.makeOutboundLink)(),Vo=Hs().div`
	padding: 16px;
`,Go="yoast-seo/editor";function Zo({location:e,show:t}){return t?(0,i.createElement)(Bs.Alert,{type:"info"},(0,p.sprintf)(/* translators: %s Expands to "Yoast News SEO" */
(0,p.__)("Are you working on a news article? %s helps you optimize your site for Google News.","wordpress-seo"),"Yoast News SEO")+" ",(0,i.createElement)(jo,{href:window.wpseoAdminL10n[`shortlinks.upsell.${e}.news`]},(0,p.sprintf)(/* translators: %s: Expands to "Yoast News SEO". */
(0,p.__)("Buy %s now!","wordpress-seo"),"Yoast News SEO"))):null}Zo.propTypes={show:h().bool.isRequired,location:h().string.isRequired};const Qo=(e,t,s)=>{const a=(0,r.useSelect)((e=>e(Go).getIsProduct()),[]),n=(0,r.useSelect)((e=>e(Go).getIsWooSeoActive()),[]),o=a&&n?{name:(0,p.__)("Item Page","wordpress-seo"),value:"ItemPage"}:e.find((e=>e.value===t));return[{name:(0,p.sprintf)(/* translators: %1$s expands to the plural name of the current post type, %2$s expands to the current site wide default. */
(0,p.__)("Default for %1$s (%2$s)","wordpress-seo"),s,o?o.name:""),value:""},...e]},Xo=e=>(0,p.sprintf)(/* translators: %1$s expands to the plural name of the current post type, %2$s and %3$s expand to a link to the Settings page */
(0,p.__)("You can change the default type for %1$s under Content types in the %2$sSettings%3$s.","wordpress-seo"),e,"{{link}}","{{/link}}");h().string.isRequired,h().string.isRequired,h().string.isRequired;const Jo=e=>{const t=Qo(e.pageTypeOptions,e.defaultPageType,e.postTypeName),s=Qo(e.articleTypeOptions,e.defaultArticleType,e.postTypeName),a=(0,J.get)(window,"wpseoScriptData.metabox.woocommerceUpsellSchemaLink",""),n=(0,J.get)(window,"wpseoScriptData.woocommerceUpsell",""),[o,l]=(0,d.useState)(e.schemaArticleTypeSelected),c=(0,p.__)("Want your products stand out in search results with rich results like price, reviews and more?","wordpress-seo"),u=(0,r.useSelect)((e=>e(Go).getIsProduct()),[]),m=(0,r.useSelect)((e=>e(Go).getIsWooSeoActive()),[]),h=u&&m,g=(0,d.useCallback)(((e,t)=>{l(t)}),[o]);return(0,d.useEffect)((()=>{g(null,e.schemaArticleTypeSelected)}),[e.schemaArticleTypeSelected]),(0,i.createElement)(d.Fragment,null,(0,i.createElement)(Bs.FieldGroup,{label:(0,p.__)("What type of page or content is this?","wordpress-seo"),linkTo:e.additionalHelpTextLink
/* translators: Hidden accessibility text. */,linkText:(0,p.__)("Learn more about page or content types","wordpress-seo")}),n&&(0,i.createElement)(hr,{link:a,text:c}),(0,i.createElement)(Bs.Select,{id:(0,_e.join)(["yoast-schema-page-type",e.location]),options:t,label:(0,p.__)("Page type","wordpress-seo"),onChange:e.schemaPageTypeChange,selected:h?"ItemPage":e.schemaPageTypeSelected,disabled:h}),e.showArticleTypeInput&&(0,i.createElement)(Bs.Select,{id:(0,_e.join)(["yoast-schema-article-type",e.location]),options:s,label:(0,p.__)("Article type","wordpress-seo"),onChange:e.schemaArticleTypeChange,selected:e.schemaArticleTypeSelected,onOptionFocus:g}),(0,i.createElement)(Zo,{location:e.location,show:!e.isNewsEnabled&&(f=o,w=e.defaultArticleType,"NewsArticle"===f||""===f&&"NewsArticle"===w)}),e.displayFooter&&!h&&(0,i.createElement)("p",null,(y=e.postTypeName,(0,zo.Z)({mixedString:Xo(y),components:{link:(0,i.createElement)("a",{href:"/wp-admin/admin.php?page=wpseo_page_settings",target:"_blank"})}}))),h&&(0,i.createElement)("p",null,(0,p.sprintf)(/* translators: %1$s expands to Yoast WooCommerce SEO. */
(0,p.__)("You have %1$s activated on your site, automatically setting the Page type for your products to 'Item Page'. As a result, the Page type selection is disabled.","wordpress-seo"),"Yoast WooCommerce SEO")));var y,f,w},ei=h().arrayOf(h().shape({name:h().string,value:h().string}));Jo.propTypes={schemaPageTypeChange:h().func,schemaPageTypeSelected:h().string,pageTypeOptions:ei.isRequired,schemaArticleTypeChange:h().func,schemaArticleTypeSelected:h().string,articleTypeOptions:ei.isRequired,showArticleTypeInput:h().bool.isRequired,additionalHelpTextLink:h().string.isRequired,helpTextLink:h().string.isRequired,helpTextTitle:h().string.isRequired,helpTextDescription:h().string.isRequired,postTypeName:h().string.isRequired,displayFooter:h().bool,defaultPageType:h().string.isRequired,defaultArticleType:h().string.isRequired,location:h().string.isRequired,isNewsEnabled:h().bool},Jo.defaultProps={schemaPageTypeChange:()=>{},schemaPageTypeSelected:null,schemaArticleTypeChange:()=>{},schemaArticleTypeSelected:null,displayFooter:!1,isNewsEnabled:!1};const ti=e=>e.isMetabox?(0,d.createPortal)((0,i.createElement)(Vo,null,(0,i.createElement)(Jo,{...e})),document.getElementById("wpseo-meta-section-schema")):(0,i.createElement)(Jo,{...e});ti.propTypes={showArticleTypeInput:h().bool,articleTypeLabel:h().string,additionalHelpTextLink:h().string,pageTypeLabel:h().string.isRequired,helpTextLink:h().string.isRequired,helpTextTitle:h().string.isRequired,helpTextDescription:h().string.isRequired,isMetabox:h().bool.isRequired,postTypeName:h().string.isRequired,displayFooter:h().bool,loadSchemaArticleData:h().func.isRequired,loadSchemaPageData:h().func.isRequired,location:h().string.isRequired},ti.defaultProps={showArticleTypeInput:!1,articleTypeLabel:"",additionalHelpTextLink:"",displayFooter:!1};const si=ti;class ai{static get articleTypeInput(){return document.getElementById("yoast_wpseo_schema_article_type")}static get defaultArticleType(){return ai.articleTypeInput.getAttribute("data-default")}static get articleType(){return ai.articleTypeInput.value}static set articleType(e){ai.articleTypeInput.value=e}static get pageTypeInput(){return document.getElementById("yoast_wpseo_schema_page_type")}static get defaultPageType(){return ai.pageTypeInput.getAttribute("data-default")}static get pageType(){return ai.pageTypeInput.value}static set pageType(e){ai.pageTypeInput.value=e}}const ni=e=>{const t=null!==ai.articleTypeInput;(0,d.useEffect)((()=>{e.loadSchemaPageData(),t&&e.loadSchemaArticleData()}),[]);const{pageTypeOptions:s,articleTypeOptions:a}=window.wpseoScriptData.metabox.schema,n={articleTypeLabel:(0,p.__)("Article type","wordpress-seo"),pageTypeLabel:(0,p.__)("Page type","wordpress-seo"),postTypeName:window.wpseoAdminL10n.postTypeNamePlural,helpTextTitle:(0,p.__)("Yoast SEO automatically describes your pages using schema.org","wordpress-seo"),helpTextDescription:(0,p.__)("This helps search engines understand your website and your content. You can change some of your settings for this page below.","wordpress-seo"),showArticleTypeInput:t,pageTypeOptions:s,articleTypeOptions:a},r={...e,...n,...(o=e.location,"metabox"===o?{helpTextLink:wpseoAdminL10n["shortlinks.metabox.schema.explanation"],additionalHelpTextLink:wpseoAdminL10n["shortlinks.metabox.schema.page_type"],isMetabox:!0}:{helpTextLink:wpseoAdminL10n["shortlinks.sidebar.schema.explanation"],additionalHelpTextLink:wpseoAdminL10n["shortlinks.sidebar.schema.page_type"],isMetabox:!1})};var o;return(0,i.createElement)(si,{...r})};ni.propTypes={displayFooter:h().bool.isRequired,schemaPageTypeSelected:h().string.isRequired,schemaArticleTypeSelected:h().string.isRequired,defaultArticleType:h().string.isRequired,defaultPageType:h().string.isRequired,loadSchemaPageData:h().func.isRequired,loadSchemaArticleData:h().func.isRequired,schemaPageTypeChange:h().func.isRequired,schemaArticleTypeChange:h().func.isRequired,location:h().string.isRequired};const ri=(0,Os.compose)([(0,r.withSelect)((e=>{const{getPreferences:t,getPageType:s,getDefaultPageType:a,getArticleType:n,getDefaultArticleType:r}=e("yoast-seo/editor"),{displaySchemaSettingsFooter:o,isNewsEnabled:i}=t();return{displayFooter:o,isNewsEnabled:i,schemaPageTypeSelected:s(),schemaArticleTypeSelected:n(),defaultArticleType:r(),defaultPageType:a()}})),(0,r.withDispatch)((e=>{const{setPageType:t,setArticleType:s,getSchemaPageData:a,getSchemaArticleData:n}=e("yoast-seo/editor");return{loadSchemaPageData:a,loadSchemaArticleData:n,schemaPageTypeChange:t,schemaArticleTypeChange:s}})),ca()])(ni),oi=({noIndex:e,onNoIndexChange:t,editorContext:s,isPrivateBlog:a})=>{const n=(e=>{const t=(0,p.__)("No","wordpress-seo"),s=(0,p.__)("Yes","wordpress-seo"),a=e.noIndex?t:s;return window.wpseoScriptData.isPost?[{name:(0,p.sprintf)(/* translators: the first %s translates to "yes" or "no", the second %s translates to the content type label in plural form */
(0,p.__)("%s (current default for %s)","wordpress-seo"),a,e.postTypeNamePlural),value:"0"},{name:t,value:"1"},{name:s,value:"2"}]:[{name:(0,p.sprintf)(/* translators: the first %s translates to "yes" or "no", the second %s translates to the content type label in plural form */
(0,p.__)("%s (current default for %s)","wordpress-seo"),a,e.postTypeNamePlural),value:"default"},{name:s,value:"index"},{name:t,value:"noindex"}]})(s);return(0,i.createElement)(Rs.LocationConsumer,null,(s=>(0,i.createElement)(d.Fragment,null,a&&(0,i.createElement)(Bs.Alert,{type:"warning"},(0,p.__)("Even though you can set the meta robots setting here, the entire site is set to noindex in the sitewide privacy settings, so these settings won't have an effect.","wordpress-seo")),(0,i.createElement)(Bs.Select,{label:(0,p.__)("Allow search engines to show this content in search results?","wordpress-seo"),onChange:t,id:(0,_e.join)(["yoast-meta-robots-noindex",s]),options:n,selected:e,linkTo:wpseoAdminL10n["shortlinks.advanced.allow_search_engines"]
/* translators: Hidden accessibility text. */,linkText:(0,p.__)("Learn more about the no-index setting on our help page.","wordpress-seo")}))))};oi.propTypes={noIndex:h().string.isRequired,onNoIndexChange:h().func.isRequired,editorContext:h().object.isRequired,isPrivateBlog:h().bool},oi.defaultProps={isPrivateBlog:!1};const ii=({noFollow:e,onNoFollowChange:t})=>(0,i.createElement)(Rs.LocationConsumer,null,(s=>{const a=(0,_e.join)(["yoast-meta-robots-nofollow",s]);return(0,i.createElement)(Bs.RadioButtonGroup,{id:a,options:[{value:"0",label:"Yes"},{value:"1",label:"No"}],label:(0,p.__)("Should search engines follow links on this content?","wordpress-seo"),groupName:a,onChange:t,selected:e,linkTo:wpseoAdminL10n["shortlinks.advanced.follow_links"]
/* translators: Hidden accessibility text. */,linkText:(0,p.__)("Learn more about the no-follow setting on our help page.","wordpress-seo")})}));ii.propTypes={noFollow:h().string.isRequired,onNoFollowChange:h().func.isRequired};const li=({advanced:e,onAdvancedChange:t})=>(0,i.createElement)(Rs.LocationConsumer,null,(s=>{const a=(0,_e.join)(["yoast-meta-robots-advanced",s]),n=`${a}-input`;return(0,i.createElement)(Bs.MultiSelect,{label:(0,p.__)("Meta robots advanced","wordpress-seo"),onChange:t,id:a,inputId:n,options:[{name:(0,p.__)("No Image Index","wordpress-seo"),value:"noimageindex"},{name:(0,p.__)("No Archive","wordpress-seo"),value:"noarchive"},{name:(0,p.__)("No Snippet","wordpress-seo"),value:"nosnippet"}],selected:e,linkTo:wpseoAdminL10n["shortlinks.advanced.meta_robots"]
/* translators: Hidden accessibility text. */,linkText:(0,p.__)("Learn more about advanced meta robots settings on our help page.","wordpress-seo")})}));li.propTypes={advanced:h().array.isRequired,onAdvancedChange:h().func.isRequired};const ci=({breadcrumbsTitle:e,onBreadcrumbsTitleChange:t})=>(0,i.createElement)(Rs.LocationConsumer,null,(s=>(0,i.createElement)(Bs.TextInput,{label:(0,p.__)("Breadcrumbs Title","wordpress-seo"),id:(0,_e.join)(["yoast-breadcrumbs-title",s]),onChange:t,value:e,linkTo:wpseoAdminL10n["shortlinks.advanced.breadcrumbs_title"]
/* translators: Hidden accessibility text. */,linkText:(0,p.__)("Learn more about the breadcrumbs title setting on our help page.","wordpress-seo")})));ci.propTypes={breadcrumbsTitle:h().string.isRequired,onBreadcrumbsTitleChange:h().func.isRequired};const di=({canonical:e,onCanonicalChange:t})=>(0,i.createElement)(Rs.LocationConsumer,null,(s=>(0,i.createElement)(Bs.TextInput,{label:(0,p.__)("Canonical URL","wordpress-seo"),id:(0,_e.join)(["yoast-canonical",s]),onChange:t,value:e,linkTo:"https://yoa.st/canonical-url"
/* translators: Hidden accessibility text. */,linkText:(0,p.__)("Learn more about canonical URLs on our help page.","wordpress-seo")})));di.propTypes={canonical:h().string.isRequired,onCanonicalChange:h().func.isRequired};const pi=e=>{const{noIndex:t,noFollow:s,advanced:a,breadcrumbsTitle:n,canonical:r,onNoIndexChange:o,onNoFollowChange:l,onAdvancedChange:c,onBreadcrumbsTitleChange:p,onCanonicalChange:u,onLoad:m,isLoading:h,editorContext:g,isBreadcrumbsDisabled:y,isPrivateBlog:f}=e;(0,d.useEffect)((()=>{setTimeout((()=>{h&&m()}))}));const w={noIndex:t,onNoIndexChange:o,editorContext:g,isPrivateBlog:f},b={noFollow:s,onNoFollowChange:l},E={advanced:a,onAdvancedChange:c},v={breadcrumbsTitle:n,onBreadcrumbsTitleChange:p},_={canonical:r,onCanonicalChange:u};return h?null:(0,i.createElement)(d.Fragment,null,(0,i.createElement)(oi,{...w}),g.isPost&&(0,i.createElement)(ii,{...b}),g.isPost&&(0,i.createElement)(li,{...E}),!y&&(0,i.createElement)(ci,{...v}),(0,i.createElement)(di,{..._}))};pi.propTypes={noIndex:h().string.isRequired,canonical:h().string.isRequired,onNoIndexChange:h().func.isRequired,onCanonicalChange:h().func.isRequired,onLoad:h().func.isRequired,isLoading:h().bool.isRequired,editorContext:h().object.isRequired,isBreadcrumbsDisabled:h().bool.isRequired,isPrivateBlog:h().bool,advanced:h().array,onAdvancedChange:h().func,noFollow:h().string,onNoFollowChange:h().func,breadcrumbsTitle:h().string,onBreadcrumbsTitleChange:h().func},pi.defaultProps={advanced:[],onAdvancedChange:()=>{},noFollow:"",onNoFollowChange:()=>{},breadcrumbsTitle:"",onBreadcrumbsTitleChange:()=>{},isPrivateBlog:!1};const ui=pi,mi=(0,Os.compose)([(0,r.withSelect)((e=>{const{getNoIndex:t,getNoFollow:s,getAdvanced:a,getBreadcrumbsTitle:n,getCanonical:r,getIsLoading:o,getEditorContext:i,getPreferences:l}=e("yoast-seo/editor"),{isBreadcrumbsDisabled:c,isPrivateBlog:d}=l();return{noIndex:t(),noFollow:s(),advanced:a(),breadcrumbsTitle:n(),canonical:r(),isLoading:o(),editorContext:i(),isBreadcrumbsDisabled:c,isPrivateBlog:d}})),(0,r.withDispatch)((e=>{const{setNoIndex:t,setNoFollow:s,setAdvanced:a,setBreadcrumbsTitle:n,setCanonical:r,loadAdvancedSettingsData:o}=e("yoast-seo/editor");return{onNoIndexChange:t,onNoFollowChange:s,onAdvancedChange:a,onBreadcrumbsTitleChange:n,onCanonicalChange:r,onLoad:o}}))])(ui),hi=()=>(0,i.createElement)("p",{className:"yoast-related-keyphrases-modal__loading-message"},(0,p.sprintf)(/* translators: %1$s expands to "Yoast SEO", %2$s expands to "Semrush". */
(0,p.__)("Please wait while %1$s connects to %2$s to get related keyphrases...","wordpress-seo"),"Yoast SEO","Semrush")," ",(0,i.createElement)(Bs.SvgIcon,{icon:"loading-spinner"})),gi=(0,_e.makeOutboundLink)(),yi=()=>(0,i.createElement)(d.Fragment,null,(0,i.createElement)("p",null,(0,p.sprintf)(/* translators: %s : Expands to "Semrush". */
(0,p.__)("You've reached your request limit for today. Check back tomorrow or upgrade your plan over at %s.","wordpress-seo"),"Semrush")),(0,i.createElement)(gi,{href:window.wpseoAdminL10n["shortlinks.semrush.prices"],className:"yoast-button-upsell"},(0,p.sprintf)(/* translators: %s : Expands to "Semrush". */
(0,p.__)("Upgrade your %s plan","wordpress-seo"),"Semrush"),(0,i.createElement)("span",{"aria-hidden":"true",className:"yoast-button-upsell__caret"}))),fi="yoast-semrush-country-selector",wi=[{value:"us",name:"United States - US"},{value:"uk",name:"United Kingdom - UK"},{value:"ca",name:"Canada - CA"},{value:"ru",name:"Russia - RU"},{value:"de",name:"Germany - DE"},{value:"fr",name:"France - FR"},{value:"es",name:"Spain - ES"},{value:"it",name:"Italy - IT"},{value:"br",name:"Brazil - BR"},{value:"au",name:"Australia - AU"},{value:"ar",name:"Argentina - AR"},{value:"be",name:"Belgium - BE"},{value:"ch",name:"Switzerland - CH"},{value:"dk",name:"Denmark - DK"},{value:"fi",name:"Finland - FI"},{value:"hk",name:"Hong Kong - HK"},{value:"ie",name:"Ireland - IE"},{value:"il",name:"Israel - IL"},{value:"mx",name:"Mexico - MX"},{value:"nl",name:"Netherlands - NL"},{value:"no",name:"Norway - NO"},{value:"pl",name:"Poland - PL"},{value:"se",name:"Sweden - SE"},{value:"sg",name:"Singapore - SG"},{value:"tr",name:"Turkey - TR"},{value:"jp",name:"Japan - JP"},{value:"in",name:"India - IN"},{value:"hu",name:"Hungary - HU"},{value:"af",name:"Afghanistan - AF"},{value:"al",name:"Albania - AL"},{value:"dz",name:"Algeria - DZ"},{value:"ao",name:"Angola - AO"},{value:"am",name:"Armenia - AM"},{value:"at",name:"Austria - AT"},{value:"az",name:"Azerbaijan - AZ"},{value:"bh",name:"Bahrain - BH"},{value:"bd",name:"Bangladesh - BD"},{value:"by",name:"Belarus - BY"},{value:"bz",name:"Belize - BZ"},{value:"bo",name:"Bolivia - BO"},{value:"ba",name:"Bosnia and Herzegovina - BA"},{value:"bw",name:"Botswana - BW"},{value:"bn",name:"Brunei - BN"},{value:"bg",name:"Bulgaria - BG"},{value:"cv",name:"Cabo Verde - CV"},{value:"kh",name:"Cambodia - KH"},{value:"cm",name:"Cameroon - CM"},{value:"cl",name:"Chile - CL"},{value:"co",name:"Colombia - CO"},{value:"cr",name:"Costa Rica - CR"},{value:"hr",name:"Croatia - HR"},{value:"cy",name:"Cyprus - CY"},{value:"cz",name:"Czech Republic - CZ"},{value:"cd",name:"Congo - CD"},{value:"do",name:"Dominican Republic - DO"},{value:"ec",name:"Ecuador - EC"},{value:"eg",name:"Egypt - EG"},{value:"sv",name:"El Salvador - SV"},{value:"ee",name:"Estonia - EE"},{value:"et",name:"Ethiopia - ET"},{value:"ge",name:"Georgia - GE"},{value:"gh",name:"Ghana - GH"},{value:"gr",name:"Greece - GR"},{value:"gt",name:"Guatemala - GT"},{value:"gy",name:"Guyana - GY"},{value:"ht",name:"Haiti - HT"},{value:"hn",name:"Honduras - HN"},{value:"is",name:"Iceland - IS"},{value:"id",name:"Indonesia - ID"},{value:"jm",name:"Jamaica - JM"},{value:"jo",name:"Jordan - JO"},{value:"kz",name:"Kazakhstan - KZ"},{value:"kw",name:"Kuwait - KW"},{value:"lv",name:"Latvia - LV"},{value:"lb",name:"Lebanon - LB"},{value:"lt",name:"Lithuania - LT"},{value:"lu",name:"Luxembourg - LU"},{value:"mg",name:"Madagascar - MG"},{value:"my",name:"Malaysia - MY"},{value:"mt",name:"Malta - MT"},{value:"mu",name:"Mauritius - MU"},{value:"md",name:"Moldova - MD"},{value:"mn",name:"Mongolia - MN"},{value:"me",name:"Montenegro - ME"},{value:"ma",name:"Morocco - MA"},{value:"mz",name:"Mozambique - MZ"},{value:"na",name:"Namibia - NA"},{value:"np",name:"Nepal - NP"},{value:"nz",name:"New Zealand - NZ"},{value:"ni",name:"Nicaragua - NI"},{value:"ng",name:"Nigeria - NG"},{value:"om",name:"Oman - OM"},{value:"py",name:"Paraguay - PY"},{value:"pe",name:"Peru - PE"},{value:"ph",name:"Philippines - PH"},{value:"pt",name:"Portugal - PT"},{value:"ro",name:"Romania - RO"},{value:"sa",name:"Saudi Arabia - SA"},{value:"sn",name:"Senegal - SN"},{value:"rs",name:"Serbia - RS"},{value:"sk",name:"Slovakia - SK"},{value:"si",name:"Slovenia - SI"},{value:"za",name:"South Africa - ZA"},{value:"kr",name:"South Korea - KR"},{value:"lk",name:"Sri Lanka - LK"},{value:"th",name:"Thailand - TH"},{value:"bs",name:"Bahamas - BS"},{value:"tt",name:"Trinidad and Tobago - TT"},{value:"tn",name:"Tunisia - TN"},{value:"ua",name:"Ukraine - UA"},{value:"ae",name:"United Arab Emirates - AE"},{value:"uy",name:"Uruguay - UY"},{value:"ve",name:"Venezuela - VE"},{value:"vn",name:"Vietnam - VN"},{value:"zm",name:"Zambia - ZM"},{value:"zw",name:"Zimbabwe - ZW"},{value:"ly",name:"Libya - LY"}];class bi extends d.Component{constructor(e){super(e),this.relatedKeyphrasesRequest=this.relatedKeyphrasesRequest.bind(this),this.onChangeHandler=this.onChangeHandler.bind(this)}componentDidMount(){this.props.response&&this.props.keyphrase===this.props.lastRequestKeyphrase||this.relatedKeyphrasesRequest()}storeCountryCode(e){de()({path:"yoast/v1/semrush/country_code",method:"POST",data:{country_code:e}})}async relatedKeyphrasesRequest(){const{keyphrase:e,countryCode:t,newRequest:s}=this.props;s(t,e),this.storeCountryCode(t);const a=await this.doRequest(e,t);200!==a.status?this.handleFailedResponse(a):this.handleSuccessResponse(a)}handleSuccessResponse(e){const{setNoResultsFound:t,setRequestSucceeded:s}=this.props;0!==e.results.rows.length?s(e):t()}handleFailedResponse(e){const{setRequestLimitReached:t,setRequestFailed:s}=this.props;"error"in e&&(e.error.includes("TOTAL LIMIT EXCEEDED")?t():s(e))}async doRequest(e,t){return await de()({path:(0,te.addQueryArgs)("/yoast/v1/semrush/related_keyphrases",{keyphrase:e,country_code:t})})}onChangeHandler(e){this.props.setCountry(e)}render(){return(0,i.createElement)("div",{id:fi},(0,i.createElement)(Bs.SingleSelect,{id:fi+"-select",label:(0,p.__)("Show results for:","wordpress-seo"),name:"semrush-country-code",options:wi,selected:this.props.countryCode,onChange:this.onChangeHandler,wrapperClassName:"yoast-field-group yoast-field-group--inline"}),(0,i.createElement)(Bs.NewButton,{id:fi+"-button",variant:"secondary",onClick:this.relatedKeyphrasesRequest},(0,p.__)("Select country","wordpress-seo")))}}bi.propTypes={keyphrase:h().string,countryCode:h().string,response:h().object,lastRequestKeyphrase:h().string,setCountry:h().func.isRequired,newRequest:h().func.isRequired,setNoResultsFound:h().func.isRequired,setRequestSucceeded:h().func.isRequired,setRequestLimitReached:h().func.isRequired,setRequestFailed:h().func.isRequired},bi.defaultProps={keyphrase:"",countryCode:"us",response:{},lastRequestKeyphrase:""};const Ei=bi,vi=({data:e,mapChartDataToTableData:t,dataTableCaption:s,dataTableHeaderLabels:a,isDataTableVisuallyHidden:n})=>e.length!==a.length?(0,i.createElement)("p",null,(0,p.__)("The number of headers and header labels don't match.","wordpress-seo")):(0,i.createElement)("div",{className:n?"screen-reader-text":null},(0,i.createElement)("table",null,(0,i.createElement)("caption",null,s),(0,i.createElement)("thead",null,(0,i.createElement)("tr",null,a.map(((e,t)=>(0,i.createElement)("th",{key:t},e))))),(0,i.createElement)("tbody",null,(0,i.createElement)("tr",null,e.map(((e,s)=>(0,i.createElement)("td",{key:s},t(e.y))))))));vi.propTypes={data:h().arrayOf(h().shape({x:h().number,y:h().number})).isRequired,mapChartDataToTableData:h().func,dataTableCaption:h().string.isRequired,dataTableHeaderLabels:h().array.isRequired,isDataTableVisuallyHidden:h().bool},vi.defaultProps={mapChartDataToTableData:null,isDataTableVisuallyHidden:!0};const _i=vi,ki=({data:e,width:t,height:s,fillColor:a,strokeColor:n,strokeWidth:r,className:o,mapChartDataToTableData:l,dataTableCaption:c,dataTableHeaderLabels:p,isDataTableVisuallyHidden:u})=>{const m=Math.max(1,Math.max(...e.map((e=>e.x)))),h=Math.max(1,Math.max(...e.map((e=>e.y)))),g=s-r,y=e.map((e=>`${e.x/m*t},${g-e.y/h*g+r}`)).join(" "),f=`0,${g+r} `+y+` ${t},${g+r}`;return(0,i.createElement)(d.Fragment,null,(0,i.createElement)("svg",{width:t,height:s,viewBox:`0 0 ${t} ${s}`,className:o,role:"img","aria-hidden":"true",focusable:"false"},(0,i.createElement)("polygon",{fill:a,points:f}),(0,i.createElement)("polyline",{fill:"none",stroke:n,strokeWidth:r,strokeLinejoin:"round",strokeLinecap:"round",points:y})),l&&(0,i.createElement)(_i,{data:e,mapChartDataToTableData:l,dataTableCaption:c,dataTableHeaderLabels:p,isDataTableVisuallyHidden:u}))};ki.propTypes={data:h().arrayOf(h().shape({x:h().number,y:h().number})).isRequired,width:h().number.isRequired,height:h().number.isRequired,fillColor:h().string,strokeColor:h().string,strokeWidth:h().number,className:h().string,mapChartDataToTableData:h().func,dataTableCaption:h().string.isRequired,dataTableHeaderLabels:h().array.isRequired,isDataTableVisuallyHidden:h().bool},ki.defaultProps={fillColor:null,strokeColor:"#000000",strokeWidth:1,className:"",mapChartDataToTableData:null,isDataTableVisuallyHidden:!0};const xi=ki,Si=(0,_e.makeOutboundLink)(Hs().a`
	display: inline-block;
	position: relative;
	outline: none;
	text-decoration: none;
	border-radius: 100%;
	width: 24px;
	height: 24px;
	margin: -4px 0;
	vertical-align: middle;

	color: ${Cr.colors.$color_help_text};
	
	&:hover,
	&:focus {
		color: ${Cr.colors.$color_snippet_focus};	
	}
	
	// Overwrite the default blue active color for links.
	&:active {
		color: ${Cr.colors.$color_help_text};	
	}

	&::before {
		position: absolute;
		top: 0;
		left: 0;
		padding: 2px;
		content: "\f223";
	}
`),Ti=(0,_e.makeOutboundLink)();class Ri extends d.Component{constructor(e){super(e),this.transformTrendDataToChartPoints=this.transformTrendDataToChartPoints.bind(this),this.getAreaChartDataTableHeaderLabels=this.getAreaChartDataTableHeaderLabels.bind(this),this.mapAreaChartDataToTableData=this.mapAreaChartDataToTableData.bind(this)}transformTrendDataToChartPoints(e){return e.split(",").map(((e,t)=>({x:t,y:parseFloat(e)})))}getAreaChartDataTableHeaderLabels(){return[(0,p.__)("Twelve months ago","wordpress-seo"),(0,p.__)("Eleven months ago","wordpress-seo"),(0,p.__)("Ten months ago","wordpress-seo"),(0,p.__)("Nine months ago","wordpress-seo"),(0,p.__)("Eight months ago","wordpress-seo"),(0,p.__)("Seven months ago","wordpress-seo"),(0,p.__)("Six months ago","wordpress-seo"),(0,p.__)("Five months ago","wordpress-seo"),(0,p.__)("Four months ago","wordpress-seo"),(0,p.__)("Three months ago","wordpress-seo"),(0,p.__)("Two months ago","wordpress-seo"),(0,p.__)("Last month","wordpress-seo")]}mapAreaChartDataToTableData(e){return Math.round(100*e)}render(){const{keyphrase:e,relatedKeyphrases:t,countryCode:s,data:a,renderAction:n}=this.props,r="https://www.semrush.com/analytics/keywordoverview/?q="+encodeURIComponent(e)+"&db="+encodeURIComponent(s);return a&&!(0,J.isEmpty)(a.results)&&(0,i.createElement)(d.Fragment,null,(0,i.createElement)("table",{className:"yoast yoast-table"},(0,i.createElement)("thead",null,(0,i.createElement)("tr",null,(0,i.createElement)("th",{scope:"col",className:"yoast-table--primary"},(0,p.__)("Related keyphrase","wordpress-seo")),(0,i.createElement)("th",{scope:"col",abbr:(0,p.__)("Volume","wordpress-seo")},(0,p.__)("Volume","wordpress-seo"),(0,i.createElement)(Si,{href:window.wpseoAdminL10n["shortlinks.semrush.volume_help"],className:"dashicons"},(0,i.createElement)("span",{className:"screen-reader-text"},/* translators: Hidden accessibility text. */
(0,p.__)("Learn more about the related keyphrases volume","wordpress-seo")))),(0,i.createElement)("th",{scope:"col",abbr:(0,p.__)("Trend","wordpress-seo")},(0,p.__)("Trend","wordpress-seo"),(0,i.createElement)(Si,{href:window.wpseoAdminL10n["shortlinks.semrush.trend_help"],className:"dashicons"},(0,i.createElement)("span",{className:"screen-reader-text"},/* translators: Hidden accessibility text. */
(0,p.__)("Learn more about the related keyphrases trend","wordpress-seo")))),n&&(0,i.createElement)("td",{className:"yoast-table--nobreak"}))),(0,i.createElement)("tbody",null,a.results.rows.map(((e,s)=>{const a=e[0],r=this.transformTrendDataToChartPoints(e[2]),o=this.getAreaChartDataTableHeaderLabels();return(0,i.createElement)("tr",{key:s},(0,i.createElement)("td",null,a),(0,i.createElement)("td",null,e[1]),(0,i.createElement)("td",{className:"yoast-table--nopadding"},(0,i.createElement)(xi,{width:66,height:24,data:r,strokeWidth:1.8,strokeColor:"#498afc",fillColor:"#ade3fc",className:"yoast-related-keyphrases-modal__chart",mapChartDataToTableData:this.mapAreaChartDataToTableData,dataTableCaption:(0,p.__)("Keyphrase volume in the last 12 months on a scale from 0 to 100.","wordpress-seo"),dataTableHeaderLabels:o})),n&&(0,i.createElement)("td",{className:"yoast-table--nobreak"},n(a,t)))})))),(0,i.createElement)("p",{style:{marginBottom:0}},(0,i.createElement)(Ti,{href:r},(0,p.sprintf)(/* translators: %s expands to Semrush */
(0,p.__)("Get more insights at %s","wordpress-seo"),"Semrush"))))}}Ri.propTypes={data:h().object,keyphrase:h().string,relatedKeyphrases:h().array,countryCode:h().string,renderAction:h().func},Ri.defaultProps={data:{},keyphrase:"",relatedKeyphrases:[],countryCode:"us",renderAction:null};const Ci=Ri,Ii=(0,_e.makeOutboundLink)(),Li=()=>(0,i.createElement)(Bs.Alert,{type:"info"},(0,p.sprintf)(/* translators: %s: Expands to "Yoast SEO". */
(0,p.__)("You’ll reach more people with multiple keyphrases! Want to quickly add these related keyphrases to the %s analyses for even better content optimization?","wordpress-seo"),"Yoast SEO")+" ",(0,i.createElement)(Ii,{href:window.wpseoAdminL10n["shortlinks.semrush.premium_landing_page"]},(0,p.sprintf)(/* translators: %s: Expands to "Yoast SEO Premium". */
(0,p.__)("Explore %s!","wordpress-seo"),"Yoast SEO Premium"))),Pi=()=>(0,i.createElement)(Bs.Alert,{type:"error"},(0,p.__)("We've encountered a problem trying to get related keyphrases. Please try again later.","wordpress-seo")),Ai=()=>(0,i.createElement)(Bs.Alert,{type:"warning"},(0,p.sprintf)(/* translators: %s: Expands to "Yoast SEO". */
(0,p.__)("You've reached the maximum amount of 4 related keyphrases. You can change or remove related keyphrases in the %s metabox or sidebar.","wordpress-seo"),"Yoast SEO"));function Oi(e){const{response:t,lastRequestKeyphrase:s,keyphrase:a,newRequest:n,setCountry:r,renderAction:o,countryCode:l,requestLimitReached:c,setRequestFailed:u,setNoResultsFound:m,relatedKeyphrases:h,setRequestSucceeded:g,setRequestLimitReached:y}=e,f=ze().isPremium;return(0,i.createElement)(d.Fragment,null,!c&&(0,i.createElement)(d.Fragment,null,!f&&(0,i.createElement)(Li,null),f&&function(e){return e&&e.length>=4}(h)&&(0,i.createElement)(Ai,null),(0,i.createElement)(Ei,{countryCode:l,setCountry:r,newRequest:n,keyphrase:a,setRequestFailed:u,setNoResultsFound:m,setRequestSucceeded:g,setRequestLimitReached:y,response:t,lastRequestKeyphrase:s})),function(e){const{isPending:t,requestLimitReached:s,isSuccess:a,response:n,requestHasData:r}=e;return t?(0,i.createElement)(hi,null):s?(0,i.createElement)(yi,null):!a&&function(e){return!(0,J.isEmpty)(e)&&"error"in e}(n)?(0,i.createElement)(Pi,null):r?void 0:(0,i.createElement)("p",null,(0,p.__)("Sorry, there's no data available for that keyphrase/country combination.","wordpress-seo"))}(e),(0,i.createElement)(Ci,{keyphrase:a,relatedKeyphrases:h,countryCode:l,renderAction:o,data:t}))}Oi.propTypes={keyphrase:h().string,relatedKeyphrases:h().array,renderAction:h().func,requestLimitReached:h().bool,countryCode:h().string.isRequired,setCountry:h().func.isRequired,newRequest:h().func.isRequired,setRequestSucceeded:h().func.isRequired,setRequestLimitReached:h().func.isRequired,setRequestFailed:h().func.isRequired,setNoResultsFound:h().func.isRequired,response:h().object,lastRequestKeyphrase:h().string},Oi.defaultProps={keyphrase:"",relatedKeyphrases:[],renderAction:null,requestLimitReached:!1,response:{},lastRequestKeyphrase:""};const Mi=(0,Os.compose)([(0,r.withSelect)((e=>{const{getFocusKeyphrase:t,getSEMrushSelectedCountry:s,getSEMrushRequestLimitReached:a,getSEMrushRequestResponse:n,getSEMrushRequestIsSuccess:r,getSEMrushIsRequestPending:o,getSEMrushRequestHasData:i,getSEMrushRequestKeyphrase:l}=e("yoast-seo/editor");return{keyphrase:t(),countryCode:s(),requestLimitReached:a(),response:n(),isSuccess:r(),isPending:o(),requestHasData:i(),lastRequestKeyphrase:l()}})),(0,r.withDispatch)((e=>{const{setSEMrushChangeCountry:t,setSEMrushNewRequest:s,setSEMrushRequestSucceeded:a,setSEMrushRequestFailed:n,setSEMrushSetRequestLimitReached:r,setSEMrushNoResultsFound:o}=e("yoast-seo/editor");return{setCountry:e=>{t(e)},newRequest:(e,t)=>{s(e,t)},setRequestSucceeded:e=>{a(e)},setRequestFailed:e=>{n(e)},setRequestLimitReached:()=>{r()},setNoResultsFound:()=>{o()}}}))])(Oi),Di=i.forwardRef((function(e,t){return i.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true",ref:t},e),i.createElement("path",{d:"M2 11a1 1 0 011-1h2a1 1 0 011 1v5a1 1 0 01-1 1H3a1 1 0 01-1-1v-5zM8 7a1 1 0 011-1h2a1 1 0 011 1v9a1 1 0 01-1 1H9a1 1 0 01-1-1V7zM14 4a1 1 0 011-1h2a1 1 0 011 1v12a1 1 0 01-1 1h-2a1 1 0 01-1-1V4z"}))}));var Fi,Ni;function qi(){return qi=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var s=arguments[t];for(var a in s)Object.prototype.hasOwnProperty.call(s,a)&&(e[a]=s[a])}return e},qi.apply(this,arguments)}const Ui=e=>i.createElement("svg",qi({xmlns:"http://www.w3.org/2000/svg","aria-hidden":"true",viewBox:"0 0 425 456.27"},e),Fi||(Fi=i.createElement("path",{d:"M73 405.26a66.79 66.79 0 0 1-6.54-1.7 64.75 64.75 0 0 1-6.28-2.31c-1-.42-2-.89-3-1.37-1.49-.72-3-1.56-4.77-2.56-1.5-.88-2.71-1.64-3.83-2.39-.9-.61-1.8-1.26-2.68-1.92a70.154 70.154 0 0 1-5.08-4.19 69.21 69.21 0 0 1-8.4-9.17c-.92-1.2-1.68-2.25-2.35-3.24a70.747 70.747 0 0 1-3.44-5.64 68.29 68.29 0 0 1-8.29-32.55V142.13a68.26 68.26 0 0 1 8.29-32.55c1-1.92 2.21-3.82 3.44-5.64s2.55-3.58 4-5.27a69.26 69.26 0 0 1 14.49-13.25C50.37 84.19 52.27 83 54.2 82A67.59 67.59 0 0 1 73 75.09a68.75 68.75 0 0 1 13.75-1.39h169.66L263 55.39H86.75A86.84 86.84 0 0 0 0 142.13v196.09A86.84 86.84 0 0 0 86.75 425h11.32v-18.35H86.75A68.75 68.75 0 0 1 73 405.26zM368.55 60.85l-1.41-.53-6.41 17.18 1.41.53a68.06 68.06 0 0 1 8.66 4c1.93 1 3.82 2.2 5.65 3.43A69.19 69.19 0 0 1 391 98.67c1.4 1.68 2.72 3.46 3.95 5.27s2.39 3.72 3.44 5.64a68.29 68.29 0 0 1 8.29 32.55v264.52H233.55l-.44.76c-3.07 5.37-6.26 10.48-9.49 15.19L222 425h203V142.13a87.2 87.2 0 0 0-56.45-81.28z"})),Ni||(Ni=i.createElement("path",{stroke:"#000",strokeMiterlimit:10,strokeWidth:3.81,d:"M119.8 408.28v46c28.49-1.12 50.73-10.6 69.61-29.58 19.45-19.55 36.17-50 52.61-96L363.94 1.9H305l-98.25 272.89-48.86-153h-54l71.7 184.18a75.67 75.67 0 0 1 0 55.12c-7.3 18.68-20.25 40.66-55.79 47.19z"}))),Bi=window.moment;var $i=s.n(Bi);const Wi=(0,_e.makeOutboundLink)(),Ki=e=>{const t=(0,p.sprintf)(/* translators: %d expands to the amount of allowed keyphrases on a free account, %s expands to a link to Wincher plans. */
(0,p.__)("You've reached the maximum amount of %d keyphrases you can add to your Wincher account. If you wish to add more keyphrases, please %s.","wordpress-seo"),e.limit,"{{updateWincherPlanLink/}}");return(0,i.createElement)(Bs.Alert,{type:"error"},(0,zo.Z)({mixedString:t,components:{updateWincherPlanLink:(0,i.createElement)(Wi,{href:wpseoAdminGlobalL10n["links.wincher.pricing"]},(0,p.sprintf)(/* translators: %s : Expands to "Wincher". */
(0,p.__)("upgrade your %s plan","wordpress-seo"),"Wincher"))}}))};Ki.propTypes={limit:h().number},Ki.defaultProps={limit:10};const Hi=Ki,Yi=()=>(0,i.createElement)(Bs.Alert,{type:"error"},(0,p.__)("Something went wrong while tracking the ranking position(s) of your page. Please try again later.","wordpress-seo"));async function zi(e,t,s,a=200){try{const n=await e();return!!n&&(n.status===a?t(n):s(n))}catch(e){console.error(e.message)}}async function ji(e){try{return await de()(e)}catch(e){return e.error&&e.status?e:e instanceof Response&&await e.json()}}async function Vi(e){return(0,J.isArray)(e)||(e=[e]),await ji({path:"yoast/v1/wincher/keyphrases/track",method:"POST",data:{keyphrases:e}})}const Gi=Hs().p`
	color: ${Cr.colors.$color_pink_dark};
	font-size: 14px;
	font-weight: 700;
	margin: 13px 0 10px;
`,Zi=Hs()(Bs.SvgIcon)`
	margin-right: 5px;
	vertical-align: middle;
`,Qi=Hs().button`
	position: absolute;
	top: 9px;
	right: 9px;
	border: none;
    background: none;
    cursor: pointer;
`,Xi=Hs().p`
	font-size: 13px;
	font-weight: 500;
	margin: 10px 0 13px;
`,Ji=Hs().div`
	position: relative;
	background: ${e=>e.isTitleShortened?"#F5F7F7":"transparent"};
	border: 1px solid #C7C7C7;
	border-left: 4px solid${Cr.colors.$color_pink_dark};
	padding: 0 16px;
	margin-bottom: 1.5em;
`,el=e=>{const[t,s]=(0,d.useState)(null);return(0,d.useEffect)((()=>{e&&!t&&async function(){return await ji({path:"yoast/v1/wincher/account/limit",method:"GET"})}().then((e=>s(e)))}),[t]),t};el.propTypes={limit:h().bool.isRequired};const tl=({limit:e,usage:t,isTitleShortened:s,isFreeAccount:a})=>{const n=(0,p.sprintf)(
/* Translators: %1$s expands to the number of used keywords.
   * %2$s expands to the account keywords limit.
   */
(0,p.__)("Your are tracking %1$s out of %2$s keyphrases included in your free account.","wordpress-seo"),t,e),r=(0,p.sprintf)(
/* Translators: %1$s expands to the number of used keywords.
   * %2$s expands to the account keywords limit.
   */
(0,p.__)("Your are tracking %1$s out of %2$s keyphrases included in your account.","wordpress-seo"),t,e),o=a?n:r,l=(0,p.sprintf)(
/* Translators: %1$s expands to the number of used keywords.
   * %2$s expands to the account keywords limit.
   */
(0,p.__)("Keyphrases tracked: %1$s/%2$s","wordpress-seo"),t,e),c=s?l:o;return(0,i.createElement)(Gi,null,s&&(0,i.createElement)(Zi,{icon:"exclamation-triangle",color:Cr.colors.$color_pink_dark,size:"14px"}),c)};tl.propTypes={limit:h().number.isRequired,usage:h().number.isRequired,isTitleShortened:h().bool,isFreeAccount:h().bool};const sl=(0,_e.makeOutboundLink)(),al=({discount:e,months:t})=>{const s=(0,i.createElement)(sl,{href:wpseoAdminGlobalL10n["links.wincher.upgrade"],style:{fontWeight:600}},(0,p.sprintf)(/* Translators: %s : Expands to "Wincher". */
(0,p.__)("Click here to upgrade your %s plan","wordpress-seo"),"Wincher"));if(!e||!t)return(0,i.createElement)(Xi,null,s);const a=100*e,n=(0,p.sprintf)(
/* Translators: %1$s expands to upgrade account link.
   * %2$s expands to the upgrade discount value.
   * %3$s expands to the upgrade discount duration e.g. 2 months.
   */
(0,p.__)("%1$s and get an exclusive %2$s discount for %3$s month(s).","wordpress-seo"),"{{wincherAccountUpgradeLink/}}",a+"%",t);return(0,i.createElement)(Xi,null,(0,zo.Z)({mixedString:n,components:{wincherAccountUpgradeLink:s}}))};al.propTypes={discount:h().number,months:h().number};const nl=({onClose:e,isTitleShortened:t,trackingInfo:s})=>{const a=(()=>{const[e,t]=(0,d.useState)(null);return(0,d.useEffect)((()=>{e||async function(){return await ji({path:"yoast/v1/wincher/account/upgrade-campaign",method:"GET"})}().then((e=>t(e)))}),[e]),e})();if(null===s)return null;const{limit:n,usage:r}=s;if(!(n&&r/n>=.8))return null;const o=Boolean(null==a?void 0:a.discount);return(0,i.createElement)(Ji,{isTitleShortened:t},e&&(0,i.createElement)(Qi,{type:"button","aria-label":(0,p.__)("Close the upgrade callout","wordpress-seo"),onClick:e},(0,i.createElement)(Bs.SvgIcon,{icon:"times-circle",color:Cr.colors.$color_pink_dark,size:"14px"})),(0,i.createElement)(tl,{...s,isTitleShortened:t,isFreeAccount:o}),(0,i.createElement)(al,{discount:null==a?void 0:a.discount,months:null==a?void 0:a.months}))};nl.propTypes={onClose:h().func,isTitleShortened:h().bool,trackingInfo:h().object};const rl=nl,ol=()=>(0,i.createElement)(Bs.Alert,{type:"success"},(0,p.sprintf)(/* translators: %s: Expands to "Wincher". */
(0,p.__)("You have successfully connected to %s! You can now track the SEO performance for the keyphrase(s) of this page.","wordpress-seo"),"Wincher")),il=()=>(0,i.createElement)(Bs.Alert,{type:"info"},(0,p.sprintf)(/* translators: %s: Expands to "Wincher". */
(0,p.__)("%s is currently tracking the ranking position(s) of your page. This may take a few minutes. Please wait or check back later.","wordpress-seo"),"Wincher")),ll=()=>(0,i.createElement)("p",{className:"yoast-wincher-seo-performance-modal__loading-message"},(0,p.__)("Tracking the ranking position...","wordpress-seo")," ",(0,i.createElement)(Bs.SvgIcon,{icon:"loading-spinner"})),cl=Hs()(Bs.SvgIcon)`
	margin-left: 2px;
	flex-shrink: 0;
	rotate: ${e=>e.isImproving?"-90deg":"90deg"};
`,dl=Hs().span`
	color: ${e=>e.isImproving?"#69AB56":"#DC3332"};
	font-size: 13px;
	font-weight: 600;
	line-height: 20px;
	margin-right: 2px;
	margin-left: 12px;
`,pl=Hs().td`
	padding-right: 0 !important;

	& > div {
		margin: 0px;
	}
`,ul=Hs().td`
	padding-left: 2px !important;
`,ml=Hs().td.attrs({className:"yoast-table--nopadding"})`
	& > div {
		justify-content: center;
	}
`,hl=Hs().div`
	display: flex;
	align-items: center;
	& > a {
		box-sizing: border-box;
	}
`,gl=Hs().button`
	background: none;
	color: inherit;
	border: none;
	padding: 0;
	font: inherit;
	cursor: pointer;
	outline: inherit;
    display: flex;
    align-items: center;
`,yl=Hs().tr`
	background-color: ${e=>e.isEnabled?"#FFFFFF":"#F9F9F9"} !important;
`;function fl(e){return Math.round(100*e)}function wl({chartData:e}){if((0,J.isEmpty)(e)||(0,J.isEmpty)(e.position))return"?";const t=function(e){return Array.from({length:e.position.history.length},((e,t)=>t+1)).map((e=>(0,p.sprintf)((0,p._n)("%d day","%d days",e,"wordpress-seo"),e)))}(e),s=e.position.history.map(((e,t)=>({x:t,y:101-e.value})));return(0,i.createElement)(xi,{width:66,height:24,data:s,strokeWidth:1.8,strokeColor:"#498afc",fillColor:"#ade3fc",mapChartDataToTableData:fl,dataTableCaption:(0,p.__)("Keyphrase position in the last 90 days on a scale from 0 to 100.","wordpress-seo"),dataTableHeaderLabels:t})}wl.propTypes={chartData:h().object},wl.defaultProps={chartData:{}};const bl=({rowData:e})=>{var t;if(null==e||null===(t=e.position)||void 0===t||!t.change)return(0,i.createElement)(wl,{chartData:e});const s=e.position.change<0;return(0,i.createElement)(d.Fragment,null,(0,i.createElement)(wl,{chartData:e}),(0,i.createElement)(dl,{isImproving:s},Math.abs(e.position.change)),(0,i.createElement)(cl,{icon:"caret-right",color:s?"#69AB56":"#DC3332",size:"14px",isImproving:s}))};function El(e){var t;const{keyphrase:s,rowData:a,onTrackKeyphrase:n,onUntrackKeyphrase:r,isFocusKeyphrase:o,isDisabled:l,isLoading:c,isSelected:u,onSelectKeyphrases:m}=e,h=!(0,J.isEmpty)(a),g=!(0,J.isEmpty)(null==a||null===(t=a.position)||void 0===t?void 0:t.history),y=(0,d.useCallback)((()=>{l||(h?r(s,a.id):n(s))}),[s,n,r,h,a,l]),f=(0,d.useCallback)((()=>{m((e=>u?e.filter((e=>e!==s)):e.concat(s)))}),[m,u,s]);return(0,i.createElement)(yl,{isEnabled:h},(0,i.createElement)(pl,null,g&&(0,i.createElement)(Bs.Checkbox,{id:"select-"+s,onChange:f,checked:u,label:""})),(0,i.createElement)(ul,null,s,o&&(0,i.createElement)("span",null,"*")),function(e){const{rowData:t,websiteId:s,keyphrase:a,onSelectKeyphrases:n}=e,r=(0,d.useCallback)((()=>{n([a])}),[n,a]),o=!(0,J.isEmpty)(t),l=t&&t.updated_at&&$i()(t.updated_at)>=$i()().subtract(7,"days"),c=t?(0,p.sprintf)("https://app.wincher.com/websites/%s/keywords?serp=%s&utm_medium=plugin&utm_source=yoast&referer=yoast&partner=yoast",s,t.id):null;return o?l?(0,i.createElement)(d.Fragment,null,(0,i.createElement)("td",null,(0,i.createElement)(hl,null,function(e){return!e||!e.position||e.position.value>100?"> 100":e.position.value}(t),(0,i.createElement)(Bs.ButtonStyledLink,{variant:"secondary",href:c,style:{height:28,marginLeft:12},rel:"noopener",target:"_blank"},(0,p.__)("View","wordpress-seo")))),(0,i.createElement)("td",{className:"yoast-table--nopadding"},(0,i.createElement)(gl,{type:"button",onClick:r},(0,i.createElement)(bl,{rowData:t}))),(0,i.createElement)("td",null,(u=t.updated_at,$i()(u).fromNow()))):(0,i.createElement)("td",{className:"yoast-table--nopadding",colSpan:"3"},(0,i.createElement)(ll,null)):(0,i.createElement)("td",{className:"yoast-table--nopadding",colSpan:"3"},(0,i.createElement)("i",null,(0,p.__)("Activate tracking to show the ranking position","wordpress-seo")));var u}(e),(0,i.createElement)(ml,null,function({keyphrase:e,isEnabled:t,toggleAction:s,isLoading:a}){return a?(0,i.createElement)(Bs.SvgIcon,{icon:"loading-spinner"}):(0,i.createElement)(Bs.Toggle,{id:`toggle-keyphrase-tracking-${e}`,className:"wincher-toggle",isEnabled:t,onSetToggleState:s,showToggleStateLabel:!1})}({keyphrase:s,isEnabled:h,toggleAction:y,isLoading:c})))}bl.propTypes={rowData:h().object},El.propTypes={rowData:h().object,keyphrase:h().string.isRequired,onTrackKeyphrase:h().func,onUntrackKeyphrase:h().func,isFocusKeyphrase:h().bool,isDisabled:h().bool,isLoading:h().bool,websiteId:h().string,isSelected:h().bool.isRequired,onSelectKeyphrases:h().func.isRequired},El.defaultProps={rowData:{},onTrackKeyphrase:()=>{},onUntrackKeyphrase:()=>{},isFocusKeyphrase:!1,isDisabled:!1,isLoading:!1,websiteId:""};const vl=(0,_e.makeOutboundLink)(),_l=Hs().span`
	display: block;
	font-style: italic;

	@media (min-width: 782px) {
		display: inline;
		position: absolute;
		${(0,_e.getDirectionalStyle)("right","left")}: 8px;
	}
`,kl=Hs().div`
	width: 100%;
	overflow-y: auto;
`,xl=Hs().th`
	pointer-events: ${e=>e.isDisabled?"none":"initial"};
	padding-right: 0 !important;

	& > div {
		margin: 0px;
	}
`,Sl=Hs().th`
	padding-left: 2px !important;
`,Tl=e=>{const t=(0,d.useRef)();return(0,d.useEffect)((()=>{t.current=e})),t.current},Rl=(0,J.debounce)((async function(e=null,t=null,s=null,a){return await ji({path:"yoast/v1/wincher/keyphrases",method:"POST",data:{keyphrases:e,permalink:s,startAt:t},signal:a})}),500,{leading:!0}),Cl=e=>{const{addTrackedKeyphrase:t,isLoggedIn:s,keyphrases:a,permalink:n,removeTrackedKeyphrase:r,setKeyphraseLimitReached:o,setRequestFailed:l,setRequestSucceeded:c,setTrackedKeyphrases:u,setHasTrackedAll:m,trackAll:h,trackedKeyphrases:g,isNewlyAuthenticated:y,websiteId:f,focusKeyphrase:w,newRequest:b,startAt:E,selectedKeyphrases:v,onSelectKeyphrases:_}=e,k=(0,d.useRef)(),x=(0,d.useRef)(),S=(0,d.useRef)(!1),[T,R]=(0,d.useState)([]),C=(0,d.useCallback)((e=>{const t=e.toLowerCase();return g&&!(0,J.isEmpty)(g)&&g.hasOwnProperty(t)?g[t]:null}),[g]),I=(0,d.useMemo)((()=>async()=>{await zi((()=>(x.current&&x.current.abort(),x.current="undefined"==typeof AbortController?null:new AbortController,Rl(a,E,n,x.current.signal))),(e=>{c(e),u(e.results)}),(e=>{l(e)}))}),[c,l,u,a,n,E]),L=(0,d.useCallback)((async e=>{const s=(Array.isArray(e)?e:[e]).map((e=>e.toLowerCase()));R((e=>[...e,...s])),await zi((()=>Vi(s)),(e=>{c(e),t(e.results),I()}),(e=>{400===e.status&&e.limit&&o(e.limit),l(e)}),201),R((e=>(0,J.without)(e,...s)))}),[c,l,o,t,I]),P=(0,d.useCallback)((async(e,t)=>{e=e.toLowerCase(),R((t=>[...t,e])),await zi((()=>async function(e){return await ji({path:"yoast/v1/wincher/keyphrases/untrack",method:"DELETE",data:{keyphraseID:e}})}(t)),(t=>{c(t),r(e)}),(e=>{l(e)})),R((t=>(0,J.without)(t,e)))}),[c,r,l]),A=(0,d.useCallback)((async e=>{b(),await L(e)}),[b,L]),O=Tl(n),M=Tl(a),D=Tl(E),F=n&&E;(0,d.useEffect)((()=>{s&&F&&(n!==O||(0,J.difference)(a,M).length||E!==D)&&I()}),[s,n,O,a,M,I,F,E,D]),(0,d.useEffect)((()=>{if(s&&h&&null!==g){const e=a.filter((e=>!C(e)));e.length&&L(e),m()}}),[s,h,g,L,m,C,a]),(0,d.useEffect)((()=>{y&&!S.current&&(I(),S.current=!0)}),[y,I]),(0,d.useEffect)((()=>{if(s&&!(0,J.isEmpty)(g))return(0,J.filter)(g,(e=>(0,J.isEmpty)(e.updated_at))).length>0&&(k.current=setInterval((()=>{I()}),1e4)),()=>{clearInterval(k.current)}}),[s,g,I]);const N=s&&null===g,q=(0,d.useMemo)((()=>(0,J.isEmpty)(g)?[]:Object.values(g).filter((e=>{var t;return!(0,J.isEmpty)(null==e||null===(t=e.position)||void 0===t?void 0:t.history)})).map((e=>e.keyword))),[g]),U=(0,d.useMemo)((()=>v.length>0&&q.length>0&&q.every((e=>v.includes(e)))),[v,q]),B=(0,d.useCallback)((()=>{_(U?[]:q)}),[_,U,q]),$=(0,d.useMemo)((()=>(0,J.orderBy)(a,[e=>Object.values(g||{}).map((e=>e.keyword)).includes(e)],["desc"])),[a,g]);return a&&!(0,J.isEmpty)(a)&&(0,i.createElement)(d.Fragment,null,(0,i.createElement)(kl,null,(0,i.createElement)("table",{className:"yoast yoast-table"},(0,i.createElement)("thead",null,(0,i.createElement)("tr",null,(0,i.createElement)(xl,{isDisabled:0===q.length},(0,i.createElement)(Bs.Checkbox,{id:"select-all",onChange:B,checked:U,label:""})),(0,i.createElement)(Sl,{scope:"col",abbr:(0,p.__)("Keyphrase","wordpress-seo")},(0,p.__)("Keyphrase","wordpress-seo")),(0,i.createElement)("th",{scope:"col",abbr:(0,p.__)("Position","wordpress-seo")},(0,p.__)("Position","wordpress-seo")),(0,i.createElement)("th",{scope:"col",abbr:(0,p.__)("Position over time","wordpress-seo")},(0,p.__)("Position over time","wordpress-seo")),(0,i.createElement)("th",{scope:"col",abbr:(0,p.__)("Last updated","wordpress-seo")},(0,p.__)("Last updated","wordpress-seo")),(0,i.createElement)("th",{scope:"col",abbr:(0,p.__)("Tracking","wordpress-seo")},(0,p.__)("Tracking","wordpress-seo")))),(0,i.createElement)("tbody",null,$.map(((e,t)=>(0,i.createElement)(El,{key:`trackable-keyphrase-${t}`,keyphrase:e,onTrackKeyphrase:A,onUntrackKeyphrase:P,rowData:C(e),isFocusKeyphrase:e===w.trim().toLowerCase(),websiteId:f,isDisabled:!s,isLoading:N||T.indexOf(e.toLowerCase())>=0,isSelected:v.includes(e),onSelectKeyphrases:_})))))),(0,i.createElement)("p",{style:{marginBottom:0,position:"relative"}},(0,i.createElement)(vl,{href:wpseoAdminGlobalL10n["links.wincher.login"]},(0,p.sprintf)(/* translators: %s expands to Wincher */
(0,p.__)("Get more insights over at %s","wordpress-seo"),"Wincher")),(0,i.createElement)(_l,null,(0,p.__)("* focus keyphrase","wordpress-seo"))))};Cl.propTypes={addTrackedKeyphrase:h().func.isRequired,isLoggedIn:h().bool,isNewlyAuthenticated:h().bool,keyphrases:h().array,newRequest:h().func.isRequired,removeTrackedKeyphrase:h().func.isRequired,setRequestFailed:h().func.isRequired,setKeyphraseLimitReached:h().func.isRequired,setRequestSucceeded:h().func.isRequired,setTrackedKeyphrases:h().func.isRequired,setHasTrackedAll:h().func.isRequired,trackAll:h().bool,trackedKeyphrases:h().object,websiteId:h().string,permalink:h().string.isRequired,focusKeyphrase:h().string,startAt:h().string,selectedKeyphrases:h().arrayOf(h().string).isRequired,onSelectKeyphrases:h().func.isRequired},Cl.defaultProps={isLoggedIn:!1,isNewlyAuthenticated:!1,keyphrases:[],trackAll:!1,websiteId:"",focusKeyphrase:""};const Il=Cl,Ll=(0,Os.compose)([(0,r.withSelect)((e=>{const{getWincherWebsiteId:t,getWincherTrackableKeyphrases:s,getWincherLoginStatus:a,getWincherPermalink:n,getFocusKeyphrase:r,isWincherNewlyAuthenticated:o,shouldWincherTrackAll:i}=e("yoast-seo/editor");return{focusKeyphrase:r(),keyphrases:s(),isLoggedIn:a(),trackAll:i(),websiteId:t(),isNewlyAuthenticated:o(),permalink:n()}})),(0,r.withDispatch)((e=>{const{setWincherNewRequest:t,setWincherRequestSucceeded:s,setWincherRequestFailed:a,setWincherSetKeyphraseLimitReached:n,setWincherTrackedKeyphrases:r,setWincherTrackingForKeyphrase:o,setWincherTrackAllKeyphrases:i,unsetWincherTrackingForKeyphrase:l}=e("yoast-seo/editor");return{newRequest:()=>{t()},setRequestSucceeded:e=>{s(e)},setRequestFailed:e=>{a(e)},setKeyphraseLimitReached:e=>{n(e)},addTrackedKeyphrase:e=>{o(e)},removeTrackedKeyphrase:e=>{l(e)},setTrackedKeyphrases:e=>{r(e)},setHasTrackedAll:()=>{i(!1)}}}))])(Il),Pl=(0,_e.makeOutboundLink)(),Al=(0,_e.makeOutboundLink)(),Ol=()=>{const e=(0,p.sprintf)((0,p.__)(/* translators: %1$s expands to a link to Wincher, %2$s expands to a link to the keyphrase tracking article on Yoast.com */
"With %1$s you can track the ranking position of your page in the search results based on your keyphrase(s). %2$s","wordpress-seo"),"{{wincherLink/}}","{{wincherReadMoreLink/}}");return(0,i.createElement)("p",null,(0,zo.Z)({mixedString:e,components:{wincherLink:(0,i.createElement)(Pl,{href:wpseoAdminGlobalL10n["links.wincher.website"]},"Wincher"),wincherReadMoreLink:(0,i.createElement)(Al,{href:wpseoAdminL10n["shortlinks.wincher.seo_performance"]},(0,p.__)("Read more about keyphrase tracking with Wincher","wordpress-seo"))}}))},Ml=()=>(0,i.createElement)(Bs.Alert,{type:"error"},(0,p.__)("No keyphrase has been set. Please set a keyphrase first.","wordpress-seo")),Dl=()=>(0,i.createElement)(Bs.Alert,{type:"info"},(0,p.sprintf)(/* translators: %s: Expands to "Wincher". */
(0,p.__)("Automatic tracking of keyphrases is enabled. Your keyphrase(s) will automatically be tracked by %s when you publish your post.","wordpress-seo"),"Wincher"));class Fl{constructor(e,t={},s={}){this.url=e,this.origin=new URL(e).origin,this.eventHandlers=Object.assign({success:{type:"",callback:()=>{}},error:{type:"",callback:()=>{}}},t),this.options=Object.assign({height:570,width:340,title:""},s),this.popup=null,this.createPopup=this.createPopup.bind(this),this.messageHandler=this.messageHandler.bind(this),this.getPopup=this.getPopup.bind(this)}createPopup(){const{height:e,width:t,title:s}=this.options,a=["top="+(window.top.outerHeight/2+window.top.screenY-e/2),"left="+(window.top.outerWidth/2+window.top.screenX-t/2),"width="+t,"height="+e,"resizable=1","scrollbars=1","status=0"];this.popup&&!this.popup.closed||(this.popup=window.open(this.url,s,a.join(","))),this.popup&&this.popup.focus(),window.addEventListener("message",this.messageHandler,!1)}async messageHandler(e){const{data:t,source:s,origin:a}=e;a===this.origin&&this.popup===s&&(t.type===this.eventHandlers.success.type&&(this.popup.close(),window.removeEventListener("message",this.messageHandler,!1),await this.eventHandlers.success.callback(t)),t.type===this.eventHandlers.error.type&&(this.popup.close(),window.removeEventListener("message",this.messageHandler,!1),await this.eventHandlers.error.callback(t)))}getPopup(){return this.popup}isClosed(){return!this.popup||this.popup.closed}focus(){this.isClosed()||this.popup.focus()}}const Nl=e=>{const t=(0,p.sprintf)(/* translators: %s expands to a link to open the Wincher login popup. */
(0,p.__)("It seems like something went wrong when retrieving your website's data. Please %s and try again.","wordpress-seo"),"{{reconnectToWincher/}}","Wincher");return(0,i.createElement)(Bs.Alert,{type:"error",className:e.className},(0,zo.Z)({mixedString:t,components:{reconnectToWincher:(0,i.createElement)("a",{href:"#",onClick:t=>{t.preventDefault(),e.onReconnect()}},(0,p.sprintf)(/* translators: %s : Expands to "Wincher". */
(0,p.__)("reconnect to %s","wordpress-seo"),"Wincher"))}}))};Nl.propTypes={onReconnect:h().func.isRequired,className:h().string},Nl.defaultProps={className:""};const ql=Nl,Ul=()=>(0,i.createElement)(Bs.Alert,{type:"error"},(0,p.__)("Before you can track your SEO performance make sure to set either the post’s title and save it as a draft or manually set the post’s slug.","wordpress-seo")),Bl=window.yoast["chart.js"],$l="label";function Wl(e,t){"function"==typeof e?e(t):e&&(e.current=t)}function Kl(e,t){e.labels=t}function Hl(e,t){let s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:$l;const a=[];e.datasets=t.map((t=>{const n=e.datasets.find((e=>e[s]===t[s]));return n&&t.data&&!a.includes(n)?(a.push(n),Object.assign(n,t),n):{...t}}))}function Yl(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:$l;const s={labels:[],datasets:[]};return Kl(s,e.labels),Hl(s,e.datasets,t),s}function zl(e,t){const{height:s=150,width:a=300,redraw:n=!1,datasetIdKey:r,type:o,data:l,options:c,plugins:d=[],fallbackContent:p,updateMode:u,...m}=e,h=(0,i.useRef)(null),g=(0,i.useRef)(),y=()=>{h.current&&(g.current=new Bl.Chart(h.current,{type:o,data:Yl(l,r),options:c&&{...c},plugins:d}),Wl(t,g.current))},f=()=>{Wl(t,null),g.current&&(g.current.destroy(),g.current=null)};return(0,i.useEffect)((()=>{!n&&g.current&&c&&function(e,t){const s=e.options;s&&t&&Object.assign(s,t)}(g.current,c)}),[n,c]),(0,i.useEffect)((()=>{!n&&g.current&&Kl(g.current.config.data,l.labels)}),[n,l.labels]),(0,i.useEffect)((()=>{!n&&g.current&&l.datasets&&Hl(g.current.config.data,l.datasets,r)}),[n,l.datasets]),(0,i.useEffect)((()=>{g.current&&(n?(f(),setTimeout(y)):g.current.update(u))}),[n,c,l.labels,l.datasets,u]),(0,i.useEffect)((()=>{g.current&&(f(),setTimeout(y))}),[o]),(0,i.useEffect)((()=>(y(),()=>f())),[]),i.createElement("canvas",Object.assign({ref:h,role:"img",height:s,width:a},m),p)}const jl=(0,i.forwardRef)(zl);function Vl(e,t){return Bl.Chart.register(t),(0,i.forwardRef)(((t,s)=>i.createElement(jl,Object.assign({},t,{ref:s,type:e}))))}const Gl=Vl("line",Bl.LineController),Zl={datetime:"MMM D, YYYY, h:mm:ss a",millisecond:"h:mm:ss.SSS a",second:"h:mm:ss a",minute:"h:mm a",hour:"hA",day:"MMM D",week:"ll",month:"MMM YYYY",quarter:"[Q]Q - YYYY",year:"YYYY"};Bl._adapters._date.override("function"==typeof $i()?{_id:"moment",formats:function(){return Zl},parse:function(e,t){return"string"==typeof e&&"string"==typeof t?e=$i()(e,t):e instanceof $i()||(e=$i()(e)),e.isValid()?e.valueOf():null},format:function(e,t){return $i()(e).format(t)},add:function(e,t,s){return $i()(e).add(t,s).valueOf()},diff:function(e,t,s){return $i()(e).diff($i()(t),s)},startOf:function(e,t,s){return e=$i()(e),"isoWeek"===t?(s=Math.trunc(Math.min(Math.max(0,s),6)),e.isoWeekday(s).startOf("day").valueOf()):e.startOf(t).valueOf()},endOf:function(e,t){return $i()(e).endOf(t).valueOf()}}:{}),Math.PI,Number.POSITIVE_INFINITY,Math.log10,Math.sign,"undefined"==typeof window||window.requestAnimationFrame,new Map,Object.create(null),Object.create(null),Number.EPSILON;const Ql=["top","right","bottom","left"];function Xl(e,t,s){const a={};s=s?"-"+s:"";for(let n=0;n<4;n++){const r=Ql[n];a[r]=parseFloat(e[t+"-"+r+s])||0}return a.width=a.left+a.right,a.height=a.top+a.bottom,a}!function(){let e=!1;try{const t={get passive(){return e=!0,!1}};window.addEventListener("test",null,t),window.removeEventListener("test",null,t)}catch(e){}}(),Bl.Chart.register(Bl.CategoryScale,Bl.LineController,Bl.LineElement,Bl.PointElement,Bl.LinearScale,Bl.TimeScale,Bl.Legend,Bl.Tooltip);const Jl=["#ff983b","#ffa3f7","#3798ff","#ff3b3b","#acce81","#b51751","#3949ab","#26c6da","#ccb800","#de66ff","#4db6ac","#ffab91","#45f5f1","#77f210","#90a4ae","#ffd54f","#006b5e","#8ec7d2","#b1887c","#cc9300"];function ec({datasets:e,isChartShown:t,keyphrases:s}){if(!t)return null;const a=(0,d.useMemo)((()=>Object.fromEntries([...s].sort().map(((e,t)=>[e,Jl[t%Jl.length]])))),[s]),n=e.map((e=>{const t=a[e.label];return{...e,data:e.data.map((({datetime:e,value:t})=>({x:e,y:t}))),lineTension:0,pointRadius:1,pointHoverRadius:4,borderWidth:2,pointHitRadius:6,backgroundColor:t,borderColor:t}})).filter((e=>!1!==e.selected));return(0,i.createElement)(Gl,{height:100,data:{datasets:n},options:{plugins:{legend:{display:!0,position:"bottom",labels:{color:"black",usePointStyle:!0,boxHeight:7,boxWidth:7},onClick:J.noop},tooltip:{enabled:!0,callbacks:{title:e=>$i()(e[0].raw.x).utc().format("YYYY-MM-DD")},titleAlign:"center",mode:"xPoint",position:"nearest",usePointStyle:!0,boxHeight:7,boxWidth:7,boxPadding:2}},scales:{x:{bounds:"ticks",type:"time",time:{unit:"day",minUnit:"day"},grid:{display:!1},ticks:{autoSkipPadding:50,maxRotation:0,color:"black"}},y:{bounds:"ticks",offset:!0,reverse:!0,ticks:{precision:0,color:"black"},max:101}}}})}Bl.Interaction.modes.xPoint=(e,t,s,a)=>{const n=function(e,t){if("native"in e)return e;const{canvas:s,currentDevicePixelRatio:a}=t,n=(m=s).ownerDocument.defaultView.getComputedStyle(m,null),r="border-box"===n.boxSizing,o=Xl(n,"padding"),i=Xl(n,"border","width"),{x:l,y:c,box:d}=function(e,t){const s=e.touches,a=s&&s.length?s[0]:e,{offsetX:n,offsetY:r}=a;let o,i,l=!1;if(((e,t,s)=>(e>0||t>0)&&(!s||!s.shadowRoot))(n,r,e.target))o=n,i=r;else{const e=t.getBoundingClientRect();o=a.clientX-e.left,i=a.clientY-e.top,l=!0}return{x:o,y:i,box:l}}(e,s),p=o.left+(d&&i.left),u=o.top+(d&&i.top);var m;let{width:h,height:g}=t;return r&&(h-=o.width+i.width,g-=o.height+i.height),{x:Math.round((l-p)/h*s.width/a),y:Math.round((c-u)/g*s.height/a)}}(t,e);let r=[];if(Bl.Interaction.evaluateInteractionItems(e,"x",n,((e,t,s)=>{e.inXRange(n.x,a)&&r.push({element:e,datasetIndex:t,index:s})})),0===r.length)return r;const o=r.reduce(((e,t)=>Math.abs(n.x-e.element.x)<Math.abs(n.x-t.element.x)?e:t)).element.x;return r=r.filter((e=>e.element.x===o)),r.some((e=>Math.abs(e.element.y-n.y)<10))?r:[]},ec.propTypes={datasets:h().arrayOf(h().shape({label:h().string.isRequired,data:h().arrayOf(h().shape({datetime:h().string.isRequired,value:h().number.isRequired})).isRequired,selected:h().bool})).isRequired,isChartShown:h().bool.isRequired,keyphrases:h().array.isRequired};const tc=({response:e,onLogin:t})=>[401,403,404].includes(e.status)?(0,i.createElement)(ql,{onReconnect:t}):(0,i.createElement)(Yi,null);tc.propTypes={response:h().object.isRequired,onLogin:h().func.isRequired};const sc=({isSuccess:e,response:t,allKeyphrasesMissRanking:s,onLogin:a,keyphraseLimitReached:n,limit:r})=>n?(0,i.createElement)(Hi,{limit:r}):(0,J.isEmpty)(t)||e?s?(0,i.createElement)(il,null):null:(0,i.createElement)(tc,{response:t,onLogin:a});sc.propTypes={isSuccess:h().bool.isRequired,allKeyphrasesMissRanking:h().bool.isRequired,response:h().object,onLogin:h().func.isRequired,keyphraseLimitReached:h().bool.isRequired,limit:h().number.isRequired},sc.defaultProps={response:{}};let ac=null;const nc=async e=>{if(ac&&!ac.isClosed())return void ac.focus();const{url:t}=await async function(){return await ji({path:"yoast/v1/wincher/authorization-url",method:"GET"})}();ac=new Fl(t,{success:{type:"wincher:oauth:success",callback:t=>(async(e,t)=>{const{onAuthentication:s,setRequestSucceeded:a,setRequestFailed:n,keyphrases:r,addTrackedKeyphrase:o,setKeyphraseLimitReached:i}=e;await zi((()=>async function(e){const{code:t,websiteId:s}=e;return await ji({path:"yoast/v1/wincher/authenticate",method:"POST",data:{code:t,websiteId:s}})}(t)),(async e=>{s(!0,!0,t.websiteId.toString()),a(e);const l=(Array.isArray(r)?r:[r]).map((e=>e.toLowerCase()));await zi((()=>Vi(l)),(e=>{a(e),o(e.results)}),(e=>{400===e.status&&e.limit&&i(e.limit),n(e)}),201);const c=ac.getPopup();c&&c.close()}),(async e=>n(e)))})(e,t)},error:{type:"wincher:oauth:error",callback:()=>e.onAuthentication(!1,!1)}},{title:"Wincher_login",width:500,height:700}),ac.createPopup()},rc=e=>e.isLoggedIn?null:(0,i.createElement)("p",null,(0,i.createElement)(Bs.NewButton,{onClick:e.onLogin,variant:"primary"},(0,p.sprintf)(/* translators: %s expands to Wincher */
(0,p.__)("Connect with %s","wordpress-seo"),"Wincher")));rc.propTypes={isLoggedIn:h().bool.isRequired,onLogin:h().func.isRequired};const oc=Hs().div`
	p {
		margin: 1em 0;
	}
`,ic=Hs().div`
	${e=>e.isDisabled&&"\n\t\topacity: .5;\n\t\tpointer-events: none;\n\t"};
`,lc=Hs().div`
	font-weight: var(--yoast-font-weight-bold);
	color: var(--yoast-color-label);
	font-size: var(--yoast-font-size-default);
`,cc=Hs().div.attrs({className:"yoast-field-group"})`
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 14px;
`,dc=Hs().div`
	margin: 8px 0;
`,pc=$i().utc().startOf("day"),uc=[{name:(0,p.__)("Last day","wordpress-seo"),value:$i()(pc).subtract(1,"days").format(),defaultIndex:1},{name:(0,p.__)("Last week","wordpress-seo"),value:$i()(pc).subtract(1,"week").format(),defaultIndex:2},{name:(0,p.__)("Last month","wordpress-seo"),value:$i()(pc).subtract(1,"month").format(),defaultIndex:3},{name:(0,p.__)("Last year","wordpress-seo"),value:$i()(pc).subtract(1,"year").format(),defaultIndex:0}],mc=e=>{const{onSelect:t,selected:s,options:a,isLoggedIn:n}=e;return n?a.length<1?null:(0,i.createElement)("select",{className:"components-select-control__input",id:"wincher-period-picker",value:(null==s?void 0:s.value)||a[0].value,onChange:t},a.map((e=>(0,i.createElement)("option",{key:e.name,value:e.value},e.name)))):null};mc.propTypes={onSelect:h().func.isRequired,selected:h().object,options:h().array.isRequired,isLoggedIn:h().bool.isRequired};const hc=e=>{const{trackedKeyphrases:t,isLoggedIn:s,keyphrases:a,shouldTrackAll:n,permalink:r,historyDaysLimit:o}=e;if(!r&&s)return(0,i.createElement)(Ul,null);if(0===a.length)return(0,i.createElement)(Ml,null);const l=$i()(pc).subtract(o,"days"),c=uc.filter((e=>$i()(e.value).isSameOrAfter(l))),u=(0,J.orderBy)(c,(e=>e.defaultIndex),"desc")[0],[m,h]=(0,d.useState)(u),[g,y]=(0,d.useState)([]),f=g.length>0,w=(0,Os.usePrevious)(t);(0,d.useEffect)((()=>{if(!(0,J.isEmpty)(t)&&(0,J.difference)(Object.keys(t),Object.keys(w||[])).length){const e=Object.values(t).map((e=>e.keyword));y(e)}}),[t,w]),(0,d.useEffect)((()=>{h(u)}),[null==u?void 0:u.name]);const b=(0,d.useCallback)((e=>{const t=uc.find((t=>t.value===e.target.value));t&&h(t)}),[h]),E=(0,d.useMemo)((()=>(0,J.isEmpty)(g)||(0,J.isEmpty)(t)?[]:Object.values(t).filter((e=>{var t;return!(null==e||null===(t=e.position)||void 0===t||!t.history)})).map((e=>{var t;return{label:e.keyword,data:e.position.history,selected:g.includes(e.keyword)&&!(0,J.isEmpty)(null===(t=e.position)||void 0===t?void 0:t.history)}}))),[g,t]);return(0,i.createElement)(ic,{isDisabled:!s},(0,i.createElement)("p",null,(0,p.__)("You can enable / disable tracking the SEO performance for each keyphrase below.","wordpress-seo")),s&&n&&(0,i.createElement)(Dl,null),(0,i.createElement)(cc,null,(0,i.createElement)(mc,{selected:m,onSelect:b,options:c,isLoggedIn:s})),(0,i.createElement)(dc,null,(0,i.createElement)(ec,{isChartShown:f,datasets:E,keyphrases:a})),(0,i.createElement)(Ll,{startAt:null==m?void 0:m.value,selectedKeyphrases:g,onSelectKeyphrases:y,trackedKeyphrases:t}))};function gc(e){const{isNewlyAuthenticated:t,isLoggedIn:s}=e,a=(0,d.useCallback)((()=>{nc(e)}),[nc,e]),n=el(s);return(0,i.createElement)(oc,null,t&&(0,i.createElement)(ol,null),s&&(0,i.createElement)(rl,{trackingInfo:n}),(0,i.createElement)(lc,null,(0,p.__)("SEO performance","wordpress-seo"),(0,i.createElement)(Bs.HelpIcon,{linkTo:wpseoAdminL10n["shortlinks.wincher.seo_performance"]
/* translators: Hidden accessibility text. */,linkText:(0,p.__)("Learn more about the SEO performance feature.","wordpress-seo")})),(0,i.createElement)(Ol,null),(0,i.createElement)(rc,{isLoggedIn:s,onLogin:a}),(0,i.createElement)(sc,{...e,onLogin:a}),(0,i.createElement)(hc,{...e,historyDaysLimit:(null==n?void 0:n.historyDays)||31}))}hc.propTypes={trackedKeyphrases:h().object,keyphrases:h().array.isRequired,isLoggedIn:h().bool.isRequired,shouldTrackAll:h().bool.isRequired,permalink:h().string.isRequired,historyDaysLimit:h().number},gc.propTypes={trackedKeyphrases:h().object,addTrackedKeyphrase:h().func.isRequired,isLoggedIn:h().bool,isNewlyAuthenticated:h().bool,keyphrases:h().array,response:h().object,shouldTrackAll:h().bool,permalink:h().string,historyDaysLimit:h().number},gc.defaultProps={trackedKeyphrases:null,isLoggedIn:!1,isNewlyAuthenticated:!1,keyphrases:[],response:{},shouldTrackAll:!1,permalink:"",historyDaysLimit:0};const yc=(0,Os.compose)([(0,r.withSelect)((e=>{const{isWincherNewlyAuthenticated:t,getWincherKeyphraseLimitReached:s,getWincherLimit:a,getWincherHistoryDaysLimit:n,getWincherLoginStatus:r,getWincherRequestIsSuccess:o,getWincherRequestResponse:i,getWincherTrackableKeyphrases:l,getWincherTrackedKeyphrases:c,getWincherAllKeyphrasesMissRanking:d,getWincherPermalink:p,shouldWincherAutomaticallyTrackAll:u}=e("yoast-seo/editor");return{keyphrases:l(),trackedKeyphrases:c(),allKeyphrasesMissRanking:d(),isLoggedIn:r(),isNewlyAuthenticated:t(),isSuccess:o(),keyphraseLimitReached:s(),limit:a(),response:i(),shouldTrackAll:u(),permalink:p(),historyDaysLimit:n()}})),(0,r.withDispatch)((e=>{const{setWincherWebsiteId:t,setWincherRequestSucceeded:s,setWincherRequestFailed:a,setWincherTrackingForKeyphrase:n,setWincherSetKeyphraseLimitReached:r,setWincherLoginStatus:o}=e("yoast-seo/editor");return{setRequestSucceeded:e=>{s(e)},setRequestFailed:e=>{a(e)},addTrackedKeyphrase:e=>{n(e)},setKeyphraseLimitReached:e=>{r(e)},onAuthentication:(e,s,a)=>{t(a),o(e,s)}}}))])(gc),fc=Hs()(Di)`
	width: 18px;
	height: 18px;
	margin: 3px;
`;function wc(e){const{keyphrases:t,onNoKeyphraseSet:s,onOpen:a,location:n}=e;if(!t.length){let e=document.querySelector("#focus-keyword-input-metabox");return e||(e=document.querySelector("#focus-keyword-input-sidebar")),e.focus(),void s()}a(n)}function bc(e){const{location:t,whichModalOpen:s,shouldCloseOnClickOutside:a}=e,n=(0,d.useCallback)((()=>{wc(e)}),[wc,e]),r=(0,p.__)("Track SEO performance","wordpress-seo"),o=xn();return(0,i.createElement)(d.Fragment,null,s===t&&(0,i.createElement)(oa,{title:r,onRequestClose:e.onClose,icon:(0,i.createElement)(Ui,null),additionalClassName:"yoast-wincher-seo-performance-modal yoast-gutenberg-modal__no-padding",shouldCloseOnClickOutside:a},(0,i.createElement)(sa,{className:"yoast-gutenberg-modal__content yoast-wincher-seo-performance-modal__content"},(0,i.createElement)(yc,null))),"sidebar"===t&&(0,i.createElement)(Ws,{id:`wincher-open-button-${t}`,title:r,SuffixHeroIcon:(0,i.createElement)(fc,{className:"yst-text-slate-500",...o}),onClick:n}),"metabox"===t&&(0,i.createElement)("div",{className:"yst-root"},(0,i.createElement)(Us,{id:`wincher-open-button-${t}`,onClick:n},(0,i.createElement)(Us.Text,null,r),(0,i.createElement)(Di,{className:"yst-h-5 yst-w-5 yst-text-slate-500",...o}))))}bc.propTypes={location:h().string,whichModalOpen:h().oneOf(["none","metabox","sidebar","postpublish"]),shouldCloseOnClickOutside:h().bool,keyphrases:h().array.isRequired,onNoKeyphraseSet:h().func.isRequired,onOpen:h().func.isRequired,onClose:h().func.isRequired},bc.defaultProps={location:"",whichModalOpen:"none",shouldCloseOnClickOutside:!0};const Ec=(0,Os.compose)([(0,r.withSelect)((e=>{const{getWincherModalOpen:t,getWincherTrackableKeyphrases:s}=e("yoast-seo/editor");return{keyphrases:s(),whichModalOpen:t()}})),(0,r.withDispatch)((e=>{const{setWincherOpenModal:t,setWincherDismissModal:s,setWincherNoKeyphrase:a}=e("yoast-seo/editor");return{onOpen:e=>{t(e)},onClose:()=>{s()},onNoKeyphraseSet:()=>{a()}}}))])(bc),vc=e=>(0,i.createElement)(ta,{title:(0,p.__)("Reach a wider audience","wordpress-seo"),description:(0,p.__)("Get help optimizing for up to 5 related keyphrases. This helps you reach a wider audience and get more traffic.","wordpress-seo"),benefitsTitle:/* translators: %s expands to 'Yoast SEO Premium'. */
(0,p.sprintf)("%s also gives you:","Yoast SEO Premium"),benefits:Ds(),upsellButtonText:(0,p.sprintf)(/* translators: %s expands to 'Yoast SEO Premium'. */
(0,p.__)("Unlock with %s","wordpress-seo"),"Yoast SEO Premium"),upsellButton:{href:e.buyLink,className:"yoast-button-upsell",rel:null,"data-ctb-id":"f6a84663-465f-4cb5-8ba5-f7a6d72224b2","data-action":"load-nfd-ctb"},upsellButtonLabel:(0,p.__)("1 year free support and updates included!","wordpress-seo")});vc.propTypes={buyLink:h().string.isRequired};const _c=vc,kc=()=>{const[e,,,t,s]=(0,u.useToggleState)(!1),a=(0,d.useContext)(Rs.LocationContext),{locationContext:n}=(0,Rs.useRootContext)(),r=(0,u.useSvgAria)(),o=wpseoAdminL10n["sidebar"===a.toLowerCase()?"shortlinks.upsell.sidebar.additional_button":"shortlinks.upsell.metabox.additional_button"];return(0,i.createElement)(i.Fragment,null,e&&(0,i.createElement)(oa,{title:(0,p.__)("Add related keyphrases","wordpress-seo"),onRequestClose:s,additionalClassName:"",id:"yoast-additional-keyphrases-modal",className:`${na} yoast-gutenberg-modal__box yoast-gutenberg-modal__no-padding`,shouldCloseOnClickOutside:!0},(0,i.createElement)(aa,null,(0,i.createElement)(_c,{buyLink:(0,te.addQueryArgs)(o,{context:n})}))),"sidebar"===a&&(0,i.createElement)(Ws,{id:"yoast-additional-keyphrase-modal-open-button",title:(0,p.__)("Add related keyphrase","wordpress-seo"),prefixIcon:{icon:"plus",color:Cr.colors.$color_grey_medium_dark},onClick:t},(0,i.createElement)("div",{className:"yst-root"},(0,i.createElement)(u.Badge,{size:"small",variant:"upsell"},(0,i.createElement)(Ms,{className:"yst-w-2.5 yst-h-2.5 yst-shrink-0",...r})))),"metabox"===a&&(0,i.createElement)("div",{className:"yst-root"},(0,i.createElement)(Us,{id:"yoast-additional-keyphrase-metabox-modal-open-button",onClick:t},(0,i.createElement)(Bs.SvgIcon,{icon:"plus",color:Cr.colors.$color_grey_medium_dark}),(0,i.createElement)(Us.Text,null,(0,p.__)("Add related keyphrase","wordpress-seo")),(0,i.createElement)(u.Badge,{size:"small",variant:"upsell"},(0,i.createElement)(Ms,{className:"yst-w-2.5 yst-h-2.5 yst-mr-1 yst-shrink-0",...r}),(0,i.createElement)("span",null,"Premium")))))};function xc({isLoading:e,onLoad:t,settings:s}){const a=(({webinarIntroUrl:e})=>{const{shouldShow:t}=ba(),s=(e=>{for(const t of e)if(null!=t&&t.getIsEligible())return t;return null})([{getIsEligible:()=>t,component:va},{getIsEligible:vn,component:()=>(0,i.createElement)(bn,{hasIcon:!1,image:null,url:e})},{getIsEligible:En,component:()=>(0,i.createElement)(ya,{hasIcon:!1})},{getIsEligible:()=>!0,component:()=>(0,i.createElement)(ga,{hasIcon:!1})}]);return(null==s?void 0:s.component)||null})({webinarIntroUrl:(0,J.get)(window,"wpseoScriptData.webinarIntroElementorUrl","https://yoa.st/webinar-intro-elementor")});return(0,d.useEffect)((()=>{setTimeout((()=>{e&&t()}))})),e?null:(0,i.createElement)(d.Fragment,null,(0,i.createElement)(c.Fill,{name:"YoastElementor"},(0,i.createElement)(rr,{renderPriority:1},(0,i.createElement)(ar,null),a&&(0,i.createElement)(a,null)),s.isKeywordAnalysisActive&&(0,i.createElement)(rr,{renderPriority:8},(0,i.createElement)(la.KeywordInput,{isSEMrushIntegrationActive:s.isSEMrushIntegrationActive}),!window.wpseoScriptData.metabox.isPremium&&(0,i.createElement)(c.Fill,{name:"YoastRelatedKeyphrases"},(0,i.createElement)(Mi,null))),s.isKeywordAnalysisActive&&(0,i.createElement)(rr,{renderPriority:10},(0,i.createElement)(d.Fragment,null,(0,i.createElement)(la.SeoAnalysis,{shouldUpsell:s.shouldUpsell,shouldUpsellWordFormRecognition:s.isWordFormRecognitionActive,shouldUpsellHighlighting:s.shouldUpsell}),s.shouldUpsell&&(0,i.createElement)(Ko,null))),s.isContentAnalysisActive&&(0,i.createElement)(rr,{renderPriority:15},(0,i.createElement)(la.ReadabilityAnalysis,{shouldUpsell:s.shouldUpsell,shouldUpsellHighlighting:s.shouldUpsell})),s.isInclusiveLanguageAnalysisActive&&(0,i.createElement)(rr,{renderPriority:19},(0,i.createElement)(la.InclusiveLanguageAnalysis,{shouldUpsellHighlighting:s.shouldUpsell})),s.isKeywordAnalysisActive&&(0,i.createElement)(rr,{key:"additional-keywords-upsell",renderPriority:22},s.shouldUpsell&&(0,i.createElement)(kc,null)),s.isKeywordAnalysisActive&&s.isWincherIntegrationActive&&(0,i.createElement)(rr,{key:"wincher-seo-performance",renderPriority:23},(0,i.createElement)(Ec,{location:"sidebar",shouldCloseOnClickOutside:!1})),s.shouldUpsell&&(0,i.createElement)(rr,{key:"internal-linking-suggestions-upsell",renderPriority:24},(0,i.createElement)(ia,null)),(0,i.createElement)(rr,{renderPriority:25},(0,i.createElement)(_r,null)),(s.useOpenGraphData||s.useTwitterData)&&(0,i.createElement)(rr,{key:"social-appearance",renderPriority:26},(0,i.createElement)(qo,{useOpenGraphData:s.useOpenGraphData,useTwitterData:s.useTwitterData})),s.displaySchemaSettings&&(0,i.createElement)(rr,{renderPriority:28},(0,i.createElement)(Yo,{title:(0,p.__)("Schema","wordpress-seo")},(0,i.createElement)(ri,null))),s.displayAdvancedTab&&(0,i.createElement)(rr,{renderPriority:29},(0,i.createElement)(Yo,{title:(0,p.__)("Advanced","wordpress-seo"),buttonId:"yoast-seo-elementor-advanced-button"},(0,i.createElement)(mi,{location:"sidebar"}))),s.isCornerstoneActive&&(0,i.createElement)(rr,{renderPriority:30},(0,i.createElement)(da,null)),s.isInsightsEnabled&&(0,i.createElement)(rr,{renderPriority:32},(0,i.createElement)(tr,{location:"elementor"}))))}xc.propTypes={isLoading:h().bool.isRequired,onLoad:h().func.isRequired,settings:h().object.isRequired};const Sc=(0,Os.compose)([(0,r.withSelect)((e=>{const{getPreferences:t,getSnippetEditorIsLoading:s}=e("yoast-seo/editor");return{settings:t(),isLoading:s()}})),(0,r.withDispatch)((e=>{const{loadSnippetEditorData:t}=e("yoast-seo/editor");return{onLoad:t}}))])(xc);class Tc extends $e.modules.hookUI.Base{constructor(e,t,s){super(),this.hook=e,this.id=t,this.callback=s}getCommand(){return this.hook}getId(){return this.id}apply(){return this.callback()}}class Rc extends $e.modules.hookData.Base{constructor(e,t,s){super(),this.hook=e,this.id=t,this.callback=s}getCommand(){return this.hook}getId(){return this.id}apply(){return this.callback()}}function Cc(e,t,s){$e.hooks.registerUIAfter(new Tc(e,t,s))}function Ic(e,t,s){$e.hooks.registerUIBefore(new Tc(e,t,s))}const Lc=({theme:e,location:t,children:s})=>(0,i.createElement)(Rs.LocationProvider,{value:t},(0,i.createElement)(Ks.ThemeProvider,{theme:e},s));Lc.propTypes={theme:h().object.isRequired,location:h().oneOf(["sidebar","metabox","modal"]).isRequired,children:h().element.isRequired};const Pc=Lc,Ac=[];let Oc=null;class Mc extends d.Component{constructor(e){super(e),this.state={registeredComponents:[]}}registerComponent(e,t){this.setState({registeredComponents:[...this.state.registeredComponents,{key:e,Component:t}]})}render(){return this.state.registeredComponents.map((({Component:e,key:t})=>(0,i.createElement)(e,{key:t})))}}function Dc(e,t){null===Oc||null===Oc.current?Ac.push({key:e,Component:t}):Oc.current.registerComponent(e,t)}const Fc="yoast-elementor-react-tab";let Nc,qc=!1;function Uc(e){e.oldValue=e.value}function Bc(){Nc.forEach((e=>Uc(e)))}const $c=(0,J.debounce)((function(){let e;qc&&(
/* Translators: %1$s translates to the Post Label in singular form */
e=(0,p.sprintf)((0,p.__)("Unfortunately we cannot save changes to your SEO settings while you are working on a draft of an already-published %1$s. If you want to save your SEO changes, make sure to click 'Update', or wait to make your SEO changes until you are ready to update the %1$s.","wordpress-seo"),window.wpseoAdminL10n.postTypeNameSingular.toLowerCase())),"draft"===window.elementor.settings.page.model.get("post_status")&&(e=""),(0,r.dispatch)("yoast-seo/editor").setWarningMessage(e)}),500,{trailing:!0});function Wc(e){var t;(function(e){return["yoast_wpseo_linkdex","yoast_wpseo_content_score","yoast_wpseo_inclusive_language_score","yoast_wpseo_words_for_linking","yoast_wpseo_estimated-reading-time-minutes"].includes(e.name)})(e)||(t=e.name,["yoast_wpseo_focuskeywords","hidden_wpseo_focuskeywords"].includes(t)&&function(e,t){if(t===e)return!0;if(""===t||""===e)return!1;const s=JSON.parse(t),a=JSON.parse(e);return s.length===a.length&&s.every(((e,t)=>e.keyword===a[t].keyword))}(e.oldValue,e.value)||e.value!==e.oldValue&&(qc=!0,$c(),window.$e.internal("document/save/set-is-modified",{status:!0}),Uc(e)))}function Kc(e){qc=!1;const t=jQuery(e).serializeArray().reduce(((e,{name:t,value:s})=>(e[t]=s,e)),{});jQuery.post(e.getAttribute("action"),t,(({success:e,data:s},a,n)=>{e?((0,o.doAction)("yoast.elementor.save.success",n),s.slug&&s.slug!==t.slug&&(0,r.dispatch)("yoast-seo/editor").updateData({slug:s.slug}),Bc(),$c()):qc=!0}))}const Hc=({id:e,children:t})=>{const[s,a]=(0,d.useState)(null);return((e,t,s={childList:!0,subtree:!0})=>{(0,d.useEffect)((()=>{const a=new MutationObserver(t);return a.observe(e,s),()=>a.disconnect()}),[e,t])})(document.body,(()=>{const n=document.getElementById(e);n?null===s&&a((0,d.createPortal)(t,n)):null!==s&&a(null)})),s},Yc=()=>{var e;const t=document.getElementById("elementor-panel-page-settings-controls");if(!t)return;const s=null===(e=t.getElementsByClassName("elementor-control"))||void 0===e?void 0:e[0];s&&(s.style.display="none");const a=document.createElement("div");a.id=Fc,a.className="yoast yoast-elementor-panel__fills",t.appendChild(a)},zc=()=>{const e=document.createElement("div");e.id="yoast-elementor-react-root",document.body.appendChild(e),function(e,t){const s=ze();Oc=(0,d.createRef)();const a={isRtl:s.isRtl};(0,d.render)((0,i.createElement)(Pc,{theme:a,location:"sidebar"},(0,i.createElement)(c.SlotFillProvider,null,(0,i.createElement)(d.Fragment,null,t,(0,i.createElement)(Mc,{ref:Oc})))),document.getElementById(e)),Ac.forEach((e=>{Oc.current.registerComponent(e.key,e.Component)}))}(e.id,(0,i.createElement)(Rs.Root,{context:{locationContext:"elementor-sidebar"}},(0,i.createElement)(Hc,{id:Fc},(0,i.createElement)(As,null),(0,i.createElement)(Sc,null))))};function jc(){window.YoastSEO=window.YoastSEO||{},window.YoastSEO._registerReactComponent=Dc,Ts()(zc),window.elementor.settings.page.model.on("change",(e=>{e.changed&&e.changed.post_status&&$c()}));const e=Kc.bind(null,document.getElementById("yoast-form"));$e.hooks.registerDataAfter(new Rc("document/save/save","yoast-seo-save",(()=>{window.elementor.config.document.id===window.elementor.config.document.revisions.current_id&&e()}))),window.elementor.modules.layouts.panel.pages.menu.Menu.addItem({name:"yoast",icon:"yoast yoast-element-menu-icon",title:"Yoast SEO",type:"page",callback:()=>{try{window.$e.route("panel/page-settings/yoast-tab")}catch(e){window.$e.route("panel/page-settings/settings"),window.$e.route("panel/page-settings/yoast-tab")}Yc()}},"more"),jQuery(document).on("click",'[data-tab="yoast-tab"]:not(.elementor-active)',Yc).on("keyup",'[data-tab="yoast-tab"]:not(.elementor-active)',(e=>{13!==e.keyCode&&32!==e.keyCode||e.currentTarget.click()})),Nc=document.querySelectorAll("input[name^='yoast']"),Bc(),setInterval((()=>Nc.forEach(Wc)),500)}const Vc=()=>{const{getContentLocale:e}=(0,r.select)("yoast-seo/editor"),t=((...e)=>()=>e.map((e=>e())))(e,ks),s=(()=>{const{setEstimatedReadingTime:e,setFleschReadingEase:t,setTextLength:s}=(0,r.dispatch)("yoast-seo/editor"),a=(0,J.get)(window,"YoastSEO.analysis.worker.runResearch",J.noop);return()=>{const n=Ke.Paper.parse(ks());a("readingTime",n).then((t=>e(t.result))),a("getFleschReadingScore",n).then((e=>{e.result&&t(e.result)})),a("wordCountInText",n).then((e=>s(e.result)))}})();return setTimeout(s,1500),((e,t)=>{let s=e();return()=>{const a=e();(0,J.isEqual)(a,s)||(s=a,t((0,J.clone)(a)))}})(t,s)},Gc={content:"",title:"",excerpt:"",slug:"",imageUrl:""},Zc="yoastmark";function Qc(e){const t=window.elementor.settings.page.model.get("post_featured_image"),s=(0,J.get)(t,"url","");return""===s?function(e){const t=Ke.languageProcessing.imageInText(e);if(0===t.length)return"";const s=jQuery.parseHTML(t.join(""));for(const e of s)if(e.src)return e.src;return""}(e):s}function Xc(){window.elementor.documents.getCurrent().$element.find(".elementor-widget-container").each(((e,t)=>{-1!==t.innerHTML.indexOf("<"+Zc)&&(t.innerHTML=Ke.markers.removeMarks(t.innerHTML))})),(0,r.dispatch)("yoast-seo/editor").setActiveMarker(null),(0,r.dispatch)("yoast-seo/editor").setMarkerPauseStatus(!1),window.YoastSEO.analysis.applyMarks(new Ke.Paper("",{}),[])}const Jc=(0,J.debounce)((function(){const e=window.elementor.documents.getCurrent();if(!["wp-post","wp-page"].includes(e.config.type))return;if((0,r.select)("yoast-seo/editor").getActiveMarker())return;const t=function(e){const t=function(e){const t=[];return e.$element.find(".elementor-widget-container").each(((e,s)=>{const a=s.innerHTML.replace(/[\n\t]/g,"").trim();t.push(a)})),t.join("")}(e);return{content:t,title:window.elementor.settings.page.model.get("post_title"),excerpt:window.elementor.settings.page.model.get("post_excerpt")||"",imageUrl:Qc(t),status:window.elementor.settings.page.model.get("post_status")}}(e);t.content!==Gc.content&&(Gc.content=t.content,(0,r.dispatch)("yoast-seo/editor").setEditorDataContent(Gc.content)),t.title!==Gc.title&&(Gc.title=t.title,(0,r.dispatch)("yoast-seo/editor").setEditorDataTitle(Gc.title),"draft"!==t.status&&"auto-draft"!==t.status||(0,r.dispatch)("yoast-seo/editor").updateData({slug:(0,te.cleanForSlug)(Gc.title)})),t.excerpt!==Gc.excerpt&&(Gc.excerpt=t.excerpt,(0,r.dispatch)("yoast-seo/editor").setEditorDataExcerpt(Gc.excerpt)),t.imageUrl!==Gc.imageUrl&&(Gc.imageUrl=t.imageUrl,(0,r.dispatch)("yoast-seo/editor").setEditorDataImageUrl(Gc.imageUrl))}),$t);function ed(){new MutationObserver(Jc).observe(window.document,{attributes:!0,childList:!0,subtree:!0,characterData:!0})}function td(){window.YoastSEO=window.YoastSEO||{},window.YoastSEO.store=function(){const s=(0,r.registerStore)("yoast-seo/editor",{reducer:(0,r.combineReducers)(X.reducers),selectors:{...X.selectors,...a},actions:(0,J.pickBy)({...X.actions,...t},(e=>"function"==typeof e)),controls:e});return(e=>{e.dispatch(X.actions.loadCornerstoneContent()),e.dispatch(X.actions.loadFocusKeyword()),e.dispatch(X.actions.setMarkerStatus(window.wpseoScriptData.metabox.elementorMarkerStatus)),e.dispatch(X.actions.setSettings({socialPreviews:{sitewideImage:window.wpseoScriptData.metabox.sitewide_social_image,siteName:window.wpseoScriptData.metabox.site_name,contentImage:window.wpseoScriptData.metabox.first_content_image,twitterCardType:window.wpseoScriptData.metabox.twitterCardType},snippetEditor:{baseUrl:window.wpseoScriptData.metabox.base_url,date:window.wpseoScriptData.metabox.metaDescriptionDate,recommendedReplacementVariables:window.wpseoScriptData.analysis.plugins.replaceVars.recommended_replace_vars,siteIconUrl:window.wpseoScriptData.metabox.siteIconUrl}}));const{facebook:t,twitter:s}=window.wpseoScriptData.metabox.showSocial;t&&e.dispatch(X.actions.loadFacebookPreviewData()),s&&e.dispatch(X.actions.loadTwitterPreviewData()),e.dispatch(X.actions.setSEMrushChangeCountry(window.wpseoScriptData.metabox.countryCode)),e.dispatch(X.actions.setSEMrushLoginStatus(window.wpseoScriptData.metabox.SEMrushLoginStatus)),e.dispatch(X.actions.setWincherLoginStatus(window.wpseoScriptData.metabox.wincherLoginStatus,!1)),e.dispatch(X.actions.setWincherWebsiteId(window.wpseoScriptData.metabox.wincherWebsiteId)),e.dispatch(X.actions.setWincherAutomaticKeyphaseTracking(window.wpseoScriptData.metabox.wincherAutoAddKeyphrases)),e.dispatch(X.actions.setDismissedAlerts((0,J.get)(window,"wpseoScriptData.dismissedAlerts",{}))),e.dispatch(X.actions.setCurrentPromotions((0,J.get)(window,"wpseoScriptData.currentPromotions",{}))),e.dispatch(X.actions.setIsPremium(Boolean((0,J.get)(window,"wpseoScriptData.metabox.isPremium",!1)))),e.dispatch(X.actions.setLinkParams((0,J.get)(window,"wpseoScriptData.linkParams",{}))),e.dispatch(X.actions.setPluginUrl((0,J.get)(window,"wpseoScriptData.pluginUrl",""))),e.dispatch(X.actions.setWistiaEmbedPermissionValue("1"===(0,J.get)(window,"wpseoScriptData.wistiaEmbedPermission",!1)))})(s),s}(),Ic("panel/editor/open","yoast-seo-reset-marks-edit",(0,J.debounce)(Xc,$t)),Ic("document/save/save","yoast-seo-reset-marks-save",Xc),Cc("editor/documents/attach-preview","yoast-seo-content-scraper-initial",Jc),Cc("editor/documents/attach-preview","yoast-seo-content-scraper",(0,J.debounce)(ed,$t)),Cc("document/save/set-is-modified","yoast-seo-content-scraper-on-modified",Jc),window.YoastSEO.pluginReady=Ce,window.YoastSEO.pluginReloaded=Ie,window.YoastSEO.registerModification=Le,window.YoastSEO.registerPlugin=Pe,window.YoastSEO.applyModifications=Ae,window.YoastSEO.analysis=window.YoastSEO.analysis||{},window.YoastSEO.analysis.run=(0,r.dispatch)("yoast-seo/editor").runAnalysis,window.YoastSEO.analysis.worker=xs(),window.YoastSEO.analysis.collectData=ks,Pe(Mt,{status:"ready"}),qt().forEach((e=>{const t=null==n?void 0:n[e];if(t){const e=(({getReplacement:e,regexp:t})=>s=>s.replace(t,e()))(t);Nt(e)}})),window.YoastSEO.wp=window.YoastSEO.wp||{},window.YoastSEO.wp.replaceVarsPlugin={addReplacement:Ut,ReplaceVar:Ot},function(){const e=ze(),t=(0,J.get)(window,["wpseoScriptData","analysis","worker","keywords_assessment_url"],"used-keywords-assessment.js"),s=(0,J.get)(window,["wpseoScriptData","usedKeywordsNonce"],""),a=new Ve("get_focus_keyword_usage_and_post_types",e,(0,r.dispatch)("yoast-seo/editor").runAnalysis,t,s);a.init();let n="";(0,r.subscribe)((()=>{const e=(0,r.select)("yoast-seo/editor").getFocusKeyphrase();e!==n&&(n=e,a.setKeyword(e))}))}(),(()=>{if((0,r.select)("yoast-seo/editor").getPreference("isInsightsEnabled",!1))(0,r.dispatch)("yoast-seo/editor").loadEstimatedReadingTime(),(0,r.subscribe)((0,J.debounce)(Vc(),1500,{maxWait:3e3}))})(),function(e){const{getFocusKeyphrase:t}=(0,r.select)("yoast-seo/editor");let s=t();He(e,s),(0,r.subscribe)((()=>{const a=t();s!==a&&(s=a,Ye(e,a))}))}(window.YoastSEO.analysis.worker.runResearch),window.elementorFrontend.config.experimentalFeatures.editor_v2?function(){if(!0===window.elementor.config.user.introduction["yoast-introduction-editor-v2"])return;const e=new window.elementorModules.editor.utils.Introduction({introductionKey:"yoast-introduction-editor-v2",dialogOptions:{id:"yoast-introduction-editor-v2",className:"elementor-right-click-introduction yoast-elementor-introduction",headerMessage:(0,p.__)("Yoast SEO for Elementor","wordpress-seo"),message:(0,p.__)("Get started with Yoast SEO's content analysis for Elementor!","wordpress-seo"),position:{my:"center top",at:"center bottom+20",of:document.querySelector("button[value='document-settings']"),using:function(e,t){this.style.setProperty("--yoast-elementor-introduction-arrow",t.target.left-t.element.left+8+"px");const s=t.target.element.closest("#elementor-editor-wrapper-v2 header");s&&s.offsetHeight>e.top-12?this.style.top=s.offsetHeight+20+"px":(0,J.isObject)(s)&&s[0].offsetHeight>e.top-12?this.style.top=s[0].offsetHeight+12+"px":this.style.top=e.top+"px",this.style.left=e.left+"px"},autoRefresh:!0},hide:{onOutsideClick:!1}},onDialogInitCallback:t=>{window.$e.routes.on("run:after",(function(e,s){"panel/page-settings/settings"===s&&t.getElements("ok").trigger("click")})),t.addButton({name:"ok",text:(0,p.__)("Got it","wordpress-seo"),callback:()=>e.setViewed()}),t.getElements("ok").addClass("elementor-button elementor-button-success")}});setTimeout((function t(){try{e.show()}catch(e){setTimeout(t,100)}}),100)}():function(){if(!0===window.elementor.config.user.introduction["yoast-introduction"])return;const e=new window.elementorModules.editor.utils.Introduction({introductionKey:"yoast-introduction",dialogOptions:{id:"yoast-introduction",className:"elementor-right-click-introduction yoast-elementor-introduction",headerMessage:(0,p.__)("New: Yoast SEO for Elementor","wordpress-seo"),message:(0,p.__)("Get started with Yoast SEO's content analysis for Elementor!","wordpress-seo"),position:{my:"left top",at:"right top",autoRefresh:!0},hide:{onOutsideClick:!1}},onDialogInitCallback:t=>{window.$e.routes.on("run:after",(function(e,s){"panel/menu"===s&&t.getElements("ok").trigger("click")})),t.addButton({name:"ok",text:(0,p.__)("Got it","wordpress-seo"),callback:()=>e.setViewed()}),t.getElements("ok").addClass("elementor-button elementor-button-success")}});setTimeout((function t(){try{e.show(window.elementor.getPanelView().header.currentView.ui.menuButton[0])}catch(e){setTimeout(t,100)}}),100)}(),jc(),window.wpseoScriptData.postType&&!["attachment","product"].includes(window.wpseoScriptData.postType)&&(()=>{const e=(0,r.select)(Q).getIsPremium(),t=(0,r.select)(Q).getIsWooSeoUpsell(),s=(0,r.select)(Q).getIsProduct()?!e||t:!e;(0,o.addFilter)("yoast.replacementVariableEditor.additionalButtons","yoast/yoast-seo-premium/AiGenerator",((e,{fieldId:t})=>(s&&e.push((0,i.createElement)(c.Fill,{name:`yoast.replacementVariableEditor.additionalButtons.${t}`},(0,i.createElement)(Z,{fieldId:t}))),e)))})(),(0,o.doAction)("yoast.elementor.loaded")}jQuery(window).on("elementor:init",(()=>{window.elementor.on("panel:init",(()=>{setTimeout(td)}))}))})()})();