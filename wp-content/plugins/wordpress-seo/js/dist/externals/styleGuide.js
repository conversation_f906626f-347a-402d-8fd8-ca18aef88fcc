(()=>{"use strict";var e={34130:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.angleRight=t.angleLeft=void 0,t.rgba=function(e,t){return"rgba( "+function(e){if("string"!=typeof e)throw new Error("Please pass a string representation of a color in hex notation.");if(e.match(/^#[a-fA-F0-9]{6}$/))return parseInt(`${e[1]}${e[2]}`,16)+", "+parseInt(`${e[3]}${e[4]}`,16)+", "+parseInt(`${e[5]}${e[6]}`,16);if(e.match(/^#[a-fA-F0-9]{3}$/))return parseInt(`${e[1]}${e[1]}`,16)+", "+parseInt(`${e[2]}${e[2]}`,16)+", "+parseInt(` ${e[3]}${e[3]}`,16);throw new Error("Couldn't parse the color string. Please provide the color as a string in hex notation.")}(e)+", "+t+" )"},t.withCaretStyles=void 0;var o=a(r(98487)),l=r(23695),_=a(r(29769));function a(e){return e&&e.__esModule?e:{default:e}}const c=e=>"data:image/svg+xml;charset=utf8,"+encodeURIComponent('<svg width="1792" height="1792" viewBox="0 0 1792 1792" xmlns="http://www.w3.org/2000/svg"><path fill="'+e+'" d="M1152 896q0 26-19 45l-448 448q-19 19-45 19t-45-19-19-45v-896q0-26 19-45t45-19 45 19l448 448q19 19 19 45z" /></svg>');t.angleRight=c;const n=e=>"data:image/svg+xml;charset=utf8,"+encodeURIComponent('<svg width="1792" height="1792" viewBox="0 0 192 512" xmlns="http://www.w3.org/2000/svg"><path fill="'+e+'" d="M192 127.338v257.324c0 17.818-21.543 26.741-34.142 14.142L29.196 270.142c-7.81-7.81-7.81-20.474 0-28.284l128.662-128.662c12.599-12.6 34.142-3.676 34.142 14.142z"/></svg>');function i(e){return(0,l.getDirectionalStyle)(c($(e)),n($(e)))(e)}function $(e){return e.isActive?_.default.$color_snippet_focus:e.isHovered?_.default.$color_snippet_hover:"transparent"}t.angleLeft=n,t.withCaretStyles=e=>(0,o.default)(e)`
		&::before {
			display: block;
			position: absolute;
			top: 4px;
			${(0,l.getDirectionalStyle)("left","right")}: -25px;
			width: 24px;
			height: 24px;
			background-image: url( ${i} );
			background-size: 25px;
			content: "";
		}
	`},23695:e=>{e.exports=window.yoast.helpers},98487:e=>{e.exports=window.yoast.styledComponents},29769:e=>{e.exports=JSON.parse('{"$palette_white":"#fff","$palette_grey_ultra_light":"#f7f7f7","$palette_grey_light":"#f1f1f1","$palette_grey_medium_light":"#e2e4e7","$palette_grey":"#ddd","$palette_grey_medium":"#ccc","$palette_grey_disabled":"#a0a5aa","$palette_grey_medium_dark":"#888","$palette_grey_text_light":"#767676","$palette_grey_text":"#616161","$palette_grey_dark":"#555","$palette_black":"#000","$palette_purple":"#5d237a","$palette_purple_dark":"#6c2548","$palette_pink":"#d73763","$palette_pink_light":"#e1bee7","$palette_pink_dark":"#a4286a","$palette_blue":"#0066cd","$palette_blue_light":"#a9a9ce","$palette_blue_medium":"#1e8cbe","$palette_blue_link":"#0073aa","$palette_blue_focus":"#5b9dd9","$palette_yoast_focus":"#007fff","$palette_blue_dark":"#084a67","$palette_green":"#77b227","$palette_green_light":"#7ad03a","$palette_green_medium_light":"#64a60a","$palette_green_medium":"#008a00","$palette_green_blue":"#009288","$palette_orange":"#dc5c04","$palette_orange_light":"#ee7c1b","$palette_red":"#dc3232","$palette_red_light":"#f9bdbd","$palette_yellow":"#ffeb3b","$palette_yellow_score":"#f5c819","$palette_button_upsell":"#fec228","$palette_button_upsell_hover":"#f2ae01","$palette_link_text":"#004973","$palette_error_background":"#f9dcdc","$palette_error_text":"#8f1919","$palette_error_emphasis":"#dc3232","$palette_info_background":"#cce5ff","$palette_info_text":"#00468f","$palette_info_emphasis":"#007dff","$palette_success_background":"#e2f2cc","$palette_success_text":"#395315","$palette_success_emphasis":"#6ea029","$palette_warning_background":"#fff3cd","$palette_warning_text":"#674e00","$palette_warning_emphasis":"#ffc201","$color_bad":"#dc3232","$color_ok":"#ee7c1b","$color_good":"#7ad03a","$color_noindex":"#1e8cbe","$color_score_icon":"#888","$color_white":"#fff","$color_black":"#000","$color_green":"#77b227","$color_green_medium":"#008a00","$color_green_blue":"#009288","$color_grey":"#ddd","$color_grey_dark":"#555","$color_purple":"#5d237a","$color_purple_dark":"#6c2548","$color_pink":"#d73763","$color_pink_light":"#e1bee7","$color_pink_dark":"#a4286a","$color_blue":"#0066cd","$color_blue_light":"#a9a9ce","$color_blue_dark":"#084a67","$color_red":"#dc3232","$color_border_light":"#f7f7f7","$color_border_gutenberg":"#e2e4e7","$color_border":"#ccc","$color_input_border":"#ddd","$color_help_text":"#767676","$color_upsell_text":"#767676","$color_background_light":"#f7f7f7","$color_button":"#f7f7f7","$color_button_text":"#555","$color_button_border":"#ccc","$color_button_hover":"#fff","$color_button_border_hover":"#888","$color_button_text_hover":"#000","$color_button_border_active":"#000","$color_button_upsell":"#fec228","$color_button_upsell_hover":"#f2ae01","$color_headings":"#555","$color_marker_inactive":"#555","$color_marker_active":"#fff","$color_marker_disabled":"#a0a5aa","$color_error":"#dc3232","$color_orange":"#dc5c04","$color_orange_hover":"#c35204","$color_grey_hover":"#cecece","$color_pink_hover":"#cc2956","$color_grey_cta":"#ddd","$color_grey_line":"#ddd","$color_grey_quote":"#616161","$color_grey_text":"#616161","$color_grey_text_light":"#767676","$color_snippet_focus":"#1e8cbe","$color_snippet_hover":"#ccc","$color_snippet_active":"#555","$color_blue_link":"#0073aa","$color_blue_focus":"#5b9dd9","$color_blue_focus_shadow":"#1e8cbe","$color_yoast_focus":"#007fff","$color_yoast_focus_outer":"rgba(0,127,255,0.25)","$color_grey_medium_dark":"#888","$color_green_medium_light":"#64a60a","$color_grey_disabled":"#a0a5aa","$color_grey_medium":"#ccc","$color_grey_light":"#f1f1f1","$color_yellow":"#ffeb3b","$color_yellow_score":"#f5c819","$color_error_message":"#f9bdbd","$color_alert_link_text":"#004973","$color_alert_error_text":"#8f1919","$color_alert_error_background":"#f9dcdc","$color_alert_info_text":"#00468f","$color_alert_info_background":"#cce5ff","$color_alert_success_text":"#395315","$color_alert_success_background":"#e2f2cc","$color_alert_warning_text":"#674e00","$color_alert_warning_background":"#fff3cd"}')},28587:e=>{e.exports=JSON.parse('{"mobile":"768px","tablet":"1224px"}')}},t={};function r(o){var l=t[o];if(void 0!==l)return l.exports;var _=t[o]={exports:{}};return e[o](_,_.exports,r),_.exports}var o={};(()=>{var e=o;Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"angleLeft",{enumerable:!0,get:function(){return _.angleLeft}}),Object.defineProperty(e,"angleRight",{enumerable:!0,get:function(){return _.angleRight}}),Object.defineProperty(e,"breakpoints",{enumerable:!0,get:function(){return l.default}}),Object.defineProperty(e,"colors",{enumerable:!0,get:function(){return t.default}}),Object.defineProperty(e,"rgba",{enumerable:!0,get:function(){return _.rgba}}),Object.defineProperty(e,"withCaretStyles",{enumerable:!0,get:function(){return _.withCaretStyles}});var t=a(r(29769)),l=a(r(28587)),_=r(34130);function a(e){return e&&e.__esModule?e:{default:e}}})(),(window.yoast=window.yoast||{}).styleGuide=o})();