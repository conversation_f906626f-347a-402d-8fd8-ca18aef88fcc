(()=>{var t={37166:(t,e,n)=>{"use strict";n.r(e),n.d(e,{composeDecorators:()=>w,createEditorStateWithText:()=>b,default:()=>S});var r=n(7206),i=n(99196),o=n.n(i),s=n(85890),a=n.n(s),u=n(43393),c=n.n(u);function f(){return f=Object.assign||function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(t[r]=n[r])}return t},f.apply(this,arguments)}function l(t,e){if(null==t)return{};var n,r,i={},o=Object.keys(t);for(r=0;r<o.length;r++)n=o[r],e.indexOf(n)>=0||(i[n]=t[n]);return i}function h(t){var e=t.getCurrentContent().getBlockMap(),n=e.last().getKey(),i=e.last().getLength(),o=new r.SelectionState({anchorKey:n,anchorOffset:i,focusKey:n,focusOffset:i});return r.EditorState.acceptSelection(t,o)}var p="-",d=function(){function t(t){this.decorators=void 0,this.decorators=c().List(t)}var e=t.prototype;return e.getDecorations=function(t,e){var n=new Array(t.getText().length).fill(null);return this.decorators.forEach((function(r,i){r.getDecorations(t,e).forEach((function(t,e){t&&(n[e]=i+p+t)}))})),c().List(n)},e.getComponentForKey=function(e){return this.getDecoratorForKey(e).getComponentForKey(t.getInnerKey(e))},e.getPropsForKey=function(e){return this.getDecoratorForKey(e).getPropsForKey(t.getInnerKey(e))},e.getDecoratorForKey=function(t){var e=t.split(p),n=Number(e[0]);return this.decorators.get(n)},t.getInnerKey=function(t){return t.split(p).slice(1).join(p)},t}(),v=function(t){return"function"==typeof t.getDecorations&&"function"==typeof t.getComponentForKey&&"function"==typeof t.getPropsForKey};function _(t){return(0,r.getDefaultKeyBinding)(t)}function y(t,e,n,i){var o,s=i.setEditorState;switch(t){case"backspace":case"backspace-word":case"backspace-to-start-of-line":o=r.RichUtils.onBackspace(e);break;case"delete":case"delete-word":case"delete-to-end-of-block":o=r.RichUtils.onDelete(e);break;default:return"not-handled"}return null!=o?(s(o),"handled"):"not-handled"}var g=function(t){var e,n;return null!=(null==t?void 0:t.decorators)?null==(e=t.decorators)?void 0:e.size:null!=(null==t?void 0:t._decorators)?null==(n=t._decorators)?void 0:n.length:void 0},m=function(t){var e,n;function i(e){var n;return(n=t.call(this,e)||this).editor=null,n.state={readOnly:!1},n.onChange=function(t){var e=t;n.resolvePlugins().forEach((function(t){t.onChange&&(e=t.onChange(e,n.getPluginMethods()))})),n.props.onChange&&n.props.onChange(e)},n.getPlugins=function(){return[].concat(n.props.plugins)},n.getProps=function(){return f({},n.props)},n.getReadOnly=function(){return n.props.readOnly||n.state.readOnly},n.setReadOnly=function(t){t!==n.state.readOnly&&n.setState({readOnly:t})},n.getEditorRef=function(){return n.editor},n.getEditorState=function(){return n.props.editorState},n.getPluginMethods=function(){return{getPlugins:n.getPlugins,getProps:n.getProps,setEditorState:n.onChange,getEditorState:n.getEditorState,getReadOnly:n.getReadOnly,setReadOnly:n.setReadOnly,getEditorRef:n.getEditorRef}},n.createPluginHooks=function(){return t=[n.props].concat(n.resolvePlugins()),e=n.getPluginMethods(),r={},i=new Set(["onChange"]),t.forEach((function(n){Object.keys(n).forEach((function(n){i.has(n)||(i.add(n),n.startsWith("on")?r[n]=function(t,e,n){return function(){for(var r=arguments.length,i=new Array(r),o=0;o<r;o++)i[o]=arguments[o];return e.some((function(e){var r=e[t];return"function"==typeof r&&!0===r.apply(void 0,i.concat([n]))}))}}(n,t,e):n.startsWith("handle")?r[n]=function(t,e,n){return function(){for(var r=arguments.length,i=new Array(r),o=0;o<r;o++)i[o]=arguments[o];return e.some((function(e){var r=e[t];return"function"==typeof r&&"handled"===r.apply(void 0,i.concat([n]))}))?"handled":"not-handled"}}(n,t,e):n.endsWith("Fn")&&("blockRendererFn"===n?r.blockRendererFn=function(t,e){return function(n){var r={props:{}};return t.forEach((function(t){if("function"==typeof t.blockRendererFn){var i=t.blockRendererFn(n,e);if(null!=i){var o=i.props,s=l(i,["props"]),a=r,u=a.props,c=l(a,["props"]);r=f({},c,s,{props:f({},u,o)})}}})),!!r.component&&r}}(t,e):"blockStyleFn"===n?r.blockStyleFn=function(t,e){return function(n){var r=[];return t.forEach((function(t){if("function"==typeof t.blockStyleFn){var i=t.blockStyleFn(n,e);null!=i&&r.push(i)}})),r.join(" ")}}(t,e):"customStyleFn"===n?r.customStyleFn=function(t,e){return function(n,r){var i;return t.some((function(t){return"function"==typeof t.customStyleFn&&void 0!==(i=t.customStyleFn(n,r,e))}))&&i?i:{}}}(t,e):"keyBindingFn"===n&&(r.keyBindingFn=function(t,e){return function(n){var r=null;return t.some((function(t){return"function"==typeof t.keyBindingFn&&void 0!==(r=t.keyBindingFn(n,e))}))?r:null}}(t,e))))}))})),r;var t,e,r,i},n.resolvePlugins=function(){var t=n.getPlugins();return!0===n.props.defaultKeyBindings&&t.push({keyBindingFn:_}),!0===n.props.defaultKeyCommands&&t.push({handleKeyCommand:y}),t},n.resolveCustomStyleMap=function(){return n.props.plugins.filter((function(t){return void 0!==t.customStyleMap})).map((function(t){return t.customStyleMap})).concat([n.props.customStyleMap]).reduce((function(t,e){return f({},t,e)}),{})},n.resolveblockRenderMap=function(){var t=n.props.plugins.filter((function(t){return void 0!==t.blockRenderMap})).reduce((function(t,e){return t.merge(e.blockRenderMap)}),(0,u.Map)({}));return n.props.defaultBlockRenderMap&&(t=r.DefaultDraftBlockRenderMap.merge(t)),n.props.blockRenderMap&&(t=t.merge(n.props.blockRenderMap)),t},n.resolveAccessibilityProps=function(){var t={};return n.resolvePlugins().forEach((function(e){if("function"==typeof e.getAccessibilityProps){var n=e.getAccessibilityProps(),r={};void 0===t.ariaHasPopup?r.ariaHasPopup=n.ariaHasPopup:"true"===n.ariaHasPopup&&(r.ariaHasPopup="true"),void 0===t.ariaExpanded?r.ariaExpanded=n.ariaExpanded:!0===n.ariaExpanded&&(r.ariaExpanded=!0),t=f({},t,n,r)}})),t},[n.props].concat(n.resolvePlugins()).forEach((function(t){t&&"function"==typeof t.initialize&&t.initialize(n.getPluginMethods())})),n}n=t,(e=i).prototype=Object.create(n.prototype),e.prototype.constructor=e,e.__proto__=n;var s=i.prototype;return s.focus=function(){this.editor&&this.editor.focus()},s.blur=function(){this.editor&&this.editor.blur()},s.componentDidMount=function(){var t,e,n,i,s,a,c=(t=this.props,e=this.getEditorState,n=this.onChange,i=function(t){var e=t.decorators,n=t.plugins,r=void 0===n?[]:n;return(0,u.List)([{decorators:e}].concat(r)).filter((function(t){return void 0!==(null==t?void 0:t.decorators)})).flatMap((function(t){return null==t?void 0:t.decorators}))}(t),s=function(t,e,n){var i=(0,u.List)(t).map((function(t){var r=t.component;return f({},t,{component:function(t){return o().createElement(r,f({},t,{getEditorState:e,setEditorState:n}))}})})).toJS();return new r.CompositeDecorator(i)}(i.filter((function(t){return!v(t)})),e,n),a=i.filter((function(t){return v(t)})),new d(a.push(s))),l=r.EditorState.set(this.props.editorState,{decorator:c});this.onChange(h(l))},s.componentDidUpdate=function(t){var e=this.props,n=t.editorState.getDecorator(),i=e.editorState.getDecorator();if(n&&!(n===i||n&&i&&g(n)===g(i))){var o=r.EditorState.set(e.editorState,{decorator:n});this.onChange(h(o))}},s.componentWillUnmount=function(){var t=this;this.resolvePlugins().forEach((function(e){e.willUnmount&&e.willUnmount({getEditorState:t.getEditorState,setEditorState:t.onChange})}))},s.render=function(){var t=this,e=this.createPluginHooks(),n=this.resolveCustomStyleMap(),i=this.resolveAccessibilityProps(),s=this.resolveblockRenderMap(),a=this.props;a.keyBindingFn;var u=l(a,["keyBindingFn"]);return o().createElement(r.Editor,f({},u,i,e,{readOnly:this.props.readOnly||this.state.readOnly,customStyleMap:n,blockRenderMap:s,onChange:this.onChange,editorState:this.props.editorState,ref:function(e){t.editor=e}}))},i}(i.Component);m.propTypes={editorState:a().object.isRequired,onChange:a().func.isRequired,plugins:a().array,defaultKeyBindings:a().bool,defaultKeyCommands:a().bool,defaultBlockRenderMap:a().bool,customStyleMap:a().object,decorators:a().array},m.defaultProps={defaultBlockRenderMap:!0,defaultKeyBindings:!0,defaultKeyCommands:!0,customStyleMap:{},plugins:[],decorators:[]};var b=function(t){return r.EditorState.createWithText?r.EditorState.createWithText(t):r.EditorState.createWithContent(r.ContentState.createFromText(t))},w=function(){for(var t=arguments.length,e=new Array(t),n=0;n<t;n++)e[n]=arguments[n];if(0===e.length)return function(t){return t};if(1===e.length)return e[0];var r=e[e.length-1];return function(){for(var t=r.apply(void 0,arguments),n=e.length-2;n>=0;n-=1)t=(0,e[n])(t);return t}};const S=m},95423:(t,e,n)=>{"use strict";n.r(e),n.d(e,{MentionSuggestions:()=>Jt,addMention:()=>Pt,default:()=>te,defaultSuggestionsFilter:()=>ee,defaultTheme:()=>Xt});var r=n(43393),i=n(99196),o=n.n(i);function s(t){var e,n,r="";if("string"==typeof t||"number"==typeof t)r+=t;else if("object"==typeof t)if(Array.isArray(t))for(e=0;e<t.length;e++)t[e]&&(n=s(t[e]))&&(r&&(r+=" "),r+=n);else for(e in t)t[e]&&(r&&(r+=" "),r+=e);return r}function a(){for(var t,e,n=0,r="";n<arguments.length;)(t=arguments[n++])&&(e=s(t))&&(r&&(r+=" "),r+=e);return r}var u=n(85890),c=n.n(u),f=n(7206);const l=window.lodash.escapeRegExp;var h=n.n(l);function p(t){if(null==t)return window;if("[object Window]"!==t.toString()){var e=t.ownerDocument;return e&&e.defaultView||window}return t}function d(t){return t instanceof p(t).Element||t instanceof Element}function v(t){return t instanceof p(t).HTMLElement||t instanceof HTMLElement}function _(t){return"undefined"!=typeof ShadowRoot&&(t instanceof p(t).ShadowRoot||t instanceof ShadowRoot)}var y=Math.max,g=Math.min,m=Math.round;function b(t,e){void 0===e&&(e=!1);var n=t.getBoundingClientRect(),r=1,i=1;if(v(t)&&e){var o=t.offsetHeight,s=t.offsetWidth;s>0&&(r=m(n.width)/s||1),o>0&&(i=m(n.height)/o||1)}return{width:n.width/r,height:n.height/i,top:n.top/i,right:n.right/r,bottom:n.bottom/i,left:n.left/r,x:n.left/r,y:n.top/i}}function w(t){var e=p(t);return{scrollLeft:e.pageXOffset,scrollTop:e.pageYOffset}}function S(t){return t?(t.nodeName||"").toLowerCase():null}function E(t){return((d(t)?t.ownerDocument:t.document)||window.document).documentElement}function O(t){return b(E(t)).left+w(t).scrollLeft}function x(t){return p(t).getComputedStyle(t)}function I(t){var e=x(t),n=e.overflow,r=e.overflowX,i=e.overflowY;return/auto|scroll|overlay|hidden/.test(n+i+r)}function z(t,e,n){void 0===n&&(n=!1);var r,i,o=v(e),s=v(e)&&function(t){var e=t.getBoundingClientRect(),n=m(e.width)/t.offsetWidth||1,r=m(e.height)/t.offsetHeight||1;return 1!==n||1!==r}(e),a=E(e),u=b(t,s),c={scrollLeft:0,scrollTop:0},f={x:0,y:0};return(o||!o&&!n)&&(("body"!==S(e)||I(a))&&(c=(r=e)!==p(r)&&v(r)?{scrollLeft:(i=r).scrollLeft,scrollTop:i.scrollTop}:w(r)),v(e)?((f=b(e,!0)).x+=e.clientLeft,f.y+=e.clientTop):a&&(f.x=O(a))),{x:u.left+c.scrollLeft-f.x,y:u.top+c.scrollTop-f.y,width:u.width,height:u.height}}function M(t){var e=b(t),n=t.offsetWidth,r=t.offsetHeight;return Math.abs(e.width-n)<=1&&(n=e.width),Math.abs(e.height-r)<=1&&(r=e.height),{x:t.offsetLeft,y:t.offsetTop,width:n,height:r}}function C(t){return"html"===S(t)?t:t.assignedSlot||t.parentNode||(_(t)?t.host:null)||E(t)}function D(t){return["html","body","#document"].indexOf(S(t))>=0?t.ownerDocument.body:v(t)&&I(t)?t:D(C(t))}function k(t,e){var n;void 0===e&&(e=[]);var r=D(t),i=r===(null==(n=t.ownerDocument)?void 0:n.body),o=p(r),s=i?[o].concat(o.visualViewport||[],I(r)?r:[]):r,a=e.concat(s);return i?a:a.concat(k(C(s)))}function R(t){return["table","td","th"].indexOf(S(t))>=0}function A(t){return v(t)&&"fixed"!==x(t).position?t.offsetParent:null}function q(t){for(var e=p(t),n=A(t);n&&R(n)&&"static"===x(n).position;)n=A(n);return n&&("html"===S(n)||"body"===S(n)&&"static"===x(n).position)?e:n||function(t){var e=-1!==navigator.userAgent.toLowerCase().indexOf("firefox");if(-1!==navigator.userAgent.indexOf("Trident")&&v(t)&&"fixed"===x(t).position)return null;for(var n=C(t);v(n)&&["html","body"].indexOf(S(n))<0;){var r=x(n);if("none"!==r.transform||"none"!==r.perspective||"paint"===r.contain||-1!==["transform","perspective"].indexOf(r.willChange)||e&&"filter"===r.willChange||e&&r.filter&&"none"!==r.filter)return n;n=n.parentNode}return null}(t)||e}var j="top",P="bottom",B="right",T="left",F="auto",K=[j,P,B,T],V="start",L="end",U="viewport",W="popper",N=K.reduce((function(t,e){return t.concat([e+"-"+V,e+"-"+L])}),[]),H=[].concat(K,[F]).reduce((function(t,e){return t.concat([e,e+"-"+V,e+"-"+L])}),[]),J=["beforeRead","read","afterRead","beforeMain","main","afterMain","beforeWrite","write","afterWrite"];function $(t){var e=new Map,n=new Set,r=[];function i(t){n.add(t.name),[].concat(t.requires||[],t.requiresIfExists||[]).forEach((function(t){if(!n.has(t)){var r=e.get(t);r&&i(r)}})),r.push(t)}return t.forEach((function(t){e.set(t.name,t)})),t.forEach((function(t){n.has(t.name)||i(t)})),r}var Y={placement:"bottom",modifiers:[],strategy:"absolute"};function X(){for(var t=arguments.length,e=new Array(t),n=0;n<t;n++)e[n]=arguments[n];return!e.some((function(t){return!(t&&"function"==typeof t.getBoundingClientRect)}))}function G(t){void 0===t&&(t={});var e=t,n=e.defaultModifiers,r=void 0===n?[]:n,i=e.defaultOptions,o=void 0===i?Y:i;return function(t,e,n){void 0===n&&(n=o);var i,s,a={placement:"bottom",orderedModifiers:[],options:Object.assign({},Y,o),modifiersData:{},elements:{reference:t,popper:e},attributes:{},styles:{}},u=[],c=!1,f={state:a,setOptions:function(n){var i="function"==typeof n?n(a.options):n;l(),a.options=Object.assign({},o,a.options,i),a.scrollParents={reference:d(t)?k(t):t.contextElement?k(t.contextElement):[],popper:k(e)};var s,c,h=function(t){var e=$(t);return J.reduce((function(t,n){return t.concat(e.filter((function(t){return t.phase===n})))}),[])}((s=[].concat(r,a.options.modifiers),c=s.reduce((function(t,e){var n=t[e.name];return t[e.name]=n?Object.assign({},n,e,{options:Object.assign({},n.options,e.options),data:Object.assign({},n.data,e.data)}):e,t}),{}),Object.keys(c).map((function(t){return c[t]}))));return a.orderedModifiers=h.filter((function(t){return t.enabled})),a.orderedModifiers.forEach((function(t){var e=t.name,n=t.options,r=void 0===n?{}:n,i=t.effect;if("function"==typeof i){var o=i({state:a,name:e,instance:f,options:r});u.push(o||function(){})}})),f.update()},forceUpdate:function(){if(!c){var t=a.elements,e=t.reference,n=t.popper;if(X(e,n)){a.rects={reference:z(e,q(n),"fixed"===a.options.strategy),popper:M(n)},a.reset=!1,a.placement=a.options.placement,a.orderedModifiers.forEach((function(t){return a.modifiersData[t.name]=Object.assign({},t.data)}));for(var r=0;r<a.orderedModifiers.length;r++)if(!0!==a.reset){var i=a.orderedModifiers[r],o=i.fn,s=i.options,u=void 0===s?{}:s,l=i.name;"function"==typeof o&&(a=o({state:a,options:u,name:l,instance:f})||a)}else a.reset=!1,r=-1}}},update:(i=function(){return new Promise((function(t){f.forceUpdate(),t(a)}))},function(){return s||(s=new Promise((function(t){Promise.resolve().then((function(){s=void 0,t(i())}))}))),s}),destroy:function(){l(),c=!0}};if(!X(t,e))return f;function l(){u.forEach((function(t){return t()})),u=[]}return f.setOptions(n).then((function(t){!c&&n.onFirstUpdate&&n.onFirstUpdate(t)})),f}}var Q={passive:!0};function Z(t){return t.split("-")[0]}function tt(t){return t.split("-")[1]}function et(t){return["top","bottom"].indexOf(t)>=0?"x":"y"}function nt(t){var e,n=t.reference,r=t.element,i=t.placement,o=i?Z(i):null,s=i?tt(i):null,a=n.x+n.width/2-r.width/2,u=n.y+n.height/2-r.height/2;switch(o){case j:e={x:a,y:n.y-r.height};break;case P:e={x:a,y:n.y+n.height};break;case B:e={x:n.x+n.width,y:u};break;case T:e={x:n.x-r.width,y:u};break;default:e={x:n.x,y:n.y}}var c=o?et(o):null;if(null!=c){var f="y"===c?"height":"width";switch(s){case V:e[c]=e[c]-(n[f]/2-r[f]/2);break;case L:e[c]=e[c]+(n[f]/2-r[f]/2)}}return e}var rt={top:"auto",right:"auto",bottom:"auto",left:"auto"};function it(t){var e,n=t.popper,r=t.popperRect,i=t.placement,o=t.variation,s=t.offsets,a=t.position,u=t.gpuAcceleration,c=t.adaptive,f=t.roundOffsets,l=t.isFixed,h=!0===f?function(t){var e=t.x,n=t.y,r=window.devicePixelRatio||1;return{x:m(e*r)/r||0,y:m(n*r)/r||0}}(s):"function"==typeof f?f(s):s,d=h.x,v=void 0===d?0:d,_=h.y,y=void 0===_?0:_,g=s.hasOwnProperty("x"),b=s.hasOwnProperty("y"),w=T,S=j,O=window;if(c){var I=q(n),z="clientHeight",M="clientWidth";I===p(n)&&"static"!==x(I=E(n)).position&&"absolute"===a&&(z="scrollHeight",M="scrollWidth"),(i===j||(i===T||i===B)&&o===L)&&(S=P,y-=(l&&O.visualViewport?O.visualViewport.height:I[z])-r.height,y*=u?1:-1),i!==T&&(i!==j&&i!==P||o!==L)||(w=B,v-=(l&&O.visualViewport?O.visualViewport.width:I[M])-r.width,v*=u?1:-1)}var C,D=Object.assign({position:a},c&&rt);return u?Object.assign({},D,((C={})[S]=b?"0":"",C[w]=g?"0":"",C.transform=(O.devicePixelRatio||1)<=1?"translate("+v+"px, "+y+"px)":"translate3d("+v+"px, "+y+"px, 0)",C)):Object.assign({},D,((e={})[S]=b?y+"px":"",e[w]=g?v+"px":"",e.transform="",e))}var ot={left:"right",right:"left",bottom:"top",top:"bottom"};function st(t){return t.replace(/left|right|bottom|top/g,(function(t){return ot[t]}))}var at={start:"end",end:"start"};function ut(t){return t.replace(/start|end/g,(function(t){return at[t]}))}function ct(t,e){var n=e.getRootNode&&e.getRootNode();if(t.contains(e))return!0;if(n&&_(n)){var r=e;do{if(r&&t.isSameNode(r))return!0;r=r.parentNode||r.host}while(r)}return!1}function ft(t){return Object.assign({},t,{left:t.x,top:t.y,right:t.x+t.width,bottom:t.y+t.height})}function lt(t,e){return e===U?ft(function(t){var e=p(t),n=E(t),r=e.visualViewport,i=n.clientWidth,o=n.clientHeight,s=0,a=0;return r&&(i=r.width,o=r.height,/^((?!chrome|android).)*safari/i.test(navigator.userAgent)||(s=r.offsetLeft,a=r.offsetTop)),{width:i,height:o,x:s+O(t),y:a}}(t)):d(e)?function(t){var e=b(t);return e.top=e.top+t.clientTop,e.left=e.left+t.clientLeft,e.bottom=e.top+t.clientHeight,e.right=e.left+t.clientWidth,e.width=t.clientWidth,e.height=t.clientHeight,e.x=e.left,e.y=e.top,e}(e):ft(function(t){var e,n=E(t),r=w(t),i=null==(e=t.ownerDocument)?void 0:e.body,o=y(n.scrollWidth,n.clientWidth,i?i.scrollWidth:0,i?i.clientWidth:0),s=y(n.scrollHeight,n.clientHeight,i?i.scrollHeight:0,i?i.clientHeight:0),a=-r.scrollLeft+O(t),u=-r.scrollTop;return"rtl"===x(i||n).direction&&(a+=y(n.clientWidth,i?i.clientWidth:0)-o),{width:o,height:s,x:a,y:u}}(E(t)))}function ht(t){return Object.assign({},{top:0,right:0,bottom:0,left:0},t)}function pt(t,e){return e.reduce((function(e,n){return e[n]=t,e}),{})}function dt(t,e){void 0===e&&(e={});var n=e,r=n.placement,i=void 0===r?t.placement:r,o=n.boundary,s=void 0===o?"clippingParents":o,a=n.rootBoundary,u=void 0===a?U:a,c=n.elementContext,f=void 0===c?W:c,l=n.altBoundary,h=void 0!==l&&l,p=n.padding,_=void 0===p?0:p,m=ht("number"!=typeof _?_:pt(_,K)),w=f===W?"reference":W,O=t.rects.popper,I=t.elements[h?w:f],z=function(t,e,n){var r="clippingParents"===e?function(t){var e=k(C(t)),n=["absolute","fixed"].indexOf(x(t).position)>=0,r=n&&v(t)?q(t):t;return d(r)?e.filter((function(t){return d(t)&&ct(t,r)&&"body"!==S(t)&&(!n||"static"!==x(t).position)})):[]}(t):[].concat(e),i=[].concat(r,[n]),o=i[0],s=i.reduce((function(e,n){var r=lt(t,n);return e.top=y(r.top,e.top),e.right=g(r.right,e.right),e.bottom=g(r.bottom,e.bottom),e.left=y(r.left,e.left),e}),lt(t,o));return s.width=s.right-s.left,s.height=s.bottom-s.top,s.x=s.left,s.y=s.top,s}(d(I)?I:I.contextElement||E(t.elements.popper),s,u),M=b(t.elements.reference),D=nt({reference:M,element:O,strategy:"absolute",placement:i}),R=ft(Object.assign({},O,D)),A=f===W?R:M,T={top:z.top-A.top+m.top,bottom:A.bottom-z.bottom+m.bottom,left:z.left-A.left+m.left,right:A.right-z.right+m.right},F=t.modifiersData.offset;if(f===W&&F){var V=F[i];Object.keys(T).forEach((function(t){var e=[B,P].indexOf(t)>=0?1:-1,n=[j,P].indexOf(t)>=0?"y":"x";T[t]+=V[n]*e}))}return T}function vt(t,e,n){return y(t,g(e,n))}function _t(t,e,n){return void 0===n&&(n={x:0,y:0}),{top:t.top-e.height-n.y,right:t.right-e.width+n.x,bottom:t.bottom-e.height+n.y,left:t.left-e.width-n.x}}function yt(t){return[j,B,P,T].some((function(e){return t[e]>=0}))}var gt=G({defaultModifiers:[{name:"eventListeners",enabled:!0,phase:"write",fn:function(){},effect:function(t){var e=t.state,n=t.instance,r=t.options,i=r.scroll,o=void 0===i||i,s=r.resize,a=void 0===s||s,u=p(e.elements.popper),c=[].concat(e.scrollParents.reference,e.scrollParents.popper);return o&&c.forEach((function(t){t.addEventListener("scroll",n.update,Q)})),a&&u.addEventListener("resize",n.update,Q),function(){o&&c.forEach((function(t){t.removeEventListener("scroll",n.update,Q)})),a&&u.removeEventListener("resize",n.update,Q)}},data:{}},{name:"popperOffsets",enabled:!0,phase:"read",fn:function(t){var e=t.state,n=t.name;e.modifiersData[n]=nt({reference:e.rects.reference,element:e.rects.popper,strategy:"absolute",placement:e.placement})},data:{}},{name:"computeStyles",enabled:!0,phase:"beforeWrite",fn:function(t){var e=t.state,n=t.options,r=n.gpuAcceleration,i=void 0===r||r,o=n.adaptive,s=void 0===o||o,a=n.roundOffsets,u=void 0===a||a,c={placement:Z(e.placement),variation:tt(e.placement),popper:e.elements.popper,popperRect:e.rects.popper,gpuAcceleration:i,isFixed:"fixed"===e.options.strategy};null!=e.modifiersData.popperOffsets&&(e.styles.popper=Object.assign({},e.styles.popper,it(Object.assign({},c,{offsets:e.modifiersData.popperOffsets,position:e.options.strategy,adaptive:s,roundOffsets:u})))),null!=e.modifiersData.arrow&&(e.styles.arrow=Object.assign({},e.styles.arrow,it(Object.assign({},c,{offsets:e.modifiersData.arrow,position:"absolute",adaptive:!1,roundOffsets:u})))),e.attributes.popper=Object.assign({},e.attributes.popper,{"data-popper-placement":e.placement})},data:{}},{name:"applyStyles",enabled:!0,phase:"write",fn:function(t){var e=t.state;Object.keys(e.elements).forEach((function(t){var n=e.styles[t]||{},r=e.attributes[t]||{},i=e.elements[t];v(i)&&S(i)&&(Object.assign(i.style,n),Object.keys(r).forEach((function(t){var e=r[t];!1===e?i.removeAttribute(t):i.setAttribute(t,!0===e?"":e)})))}))},effect:function(t){var e=t.state,n={popper:{position:e.options.strategy,left:"0",top:"0",margin:"0"},arrow:{position:"absolute"},reference:{}};return Object.assign(e.elements.popper.style,n.popper),e.styles=n,e.elements.arrow&&Object.assign(e.elements.arrow.style,n.arrow),function(){Object.keys(e.elements).forEach((function(t){var r=e.elements[t],i=e.attributes[t]||{},o=Object.keys(e.styles.hasOwnProperty(t)?e.styles[t]:n[t]).reduce((function(t,e){return t[e]="",t}),{});v(r)&&S(r)&&(Object.assign(r.style,o),Object.keys(i).forEach((function(t){r.removeAttribute(t)})))}))}},requires:["computeStyles"]},{name:"offset",enabled:!0,phase:"main",requires:["popperOffsets"],fn:function(t){var e=t.state,n=t.options,r=t.name,i=n.offset,o=void 0===i?[0,0]:i,s=H.reduce((function(t,n){return t[n]=function(t,e,n){var r=Z(t),i=[T,j].indexOf(r)>=0?-1:1,o="function"==typeof n?n(Object.assign({},e,{placement:t})):n,s=o[0],a=o[1];return s=s||0,a=(a||0)*i,[T,B].indexOf(r)>=0?{x:a,y:s}:{x:s,y:a}}(n,e.rects,o),t}),{}),a=s[e.placement],u=a.x,c=a.y;null!=e.modifiersData.popperOffsets&&(e.modifiersData.popperOffsets.x+=u,e.modifiersData.popperOffsets.y+=c),e.modifiersData[r]=s}},{name:"flip",enabled:!0,phase:"main",fn:function(t){var e=t.state,n=t.options,r=t.name;if(!e.modifiersData[r]._skip){for(var i=n.mainAxis,o=void 0===i||i,s=n.altAxis,a=void 0===s||s,u=n.fallbackPlacements,c=n.padding,f=n.boundary,l=n.rootBoundary,h=n.altBoundary,p=n.flipVariations,d=void 0===p||p,v=n.allowedAutoPlacements,_=e.options.placement,y=Z(_),g=u||(y!==_&&d?function(t){if(Z(t)===F)return[];var e=st(t);return[ut(t),e,ut(e)]}(_):[st(_)]),m=[_].concat(g).reduce((function(t,n){return t.concat(Z(n)===F?function(t,e){void 0===e&&(e={});var n=e,r=n.placement,i=n.boundary,o=n.rootBoundary,s=n.padding,a=n.flipVariations,u=n.allowedAutoPlacements,c=void 0===u?H:u,f=tt(r),l=f?a?N:N.filter((function(t){return tt(t)===f})):K,h=l.filter((function(t){return c.indexOf(t)>=0}));0===h.length&&(h=l);var p=h.reduce((function(e,n){return e[n]=dt(t,{placement:n,boundary:i,rootBoundary:o,padding:s})[Z(n)],e}),{});return Object.keys(p).sort((function(t,e){return p[t]-p[e]}))}(e,{placement:n,boundary:f,rootBoundary:l,padding:c,flipVariations:d,allowedAutoPlacements:v}):n)}),[]),b=e.rects.reference,w=e.rects.popper,S=new Map,E=!0,O=m[0],x=0;x<m.length;x++){var I=m[x],z=Z(I),M=tt(I)===V,C=[j,P].indexOf(z)>=0,D=C?"width":"height",k=dt(e,{placement:I,boundary:f,rootBoundary:l,altBoundary:h,padding:c}),R=C?M?B:T:M?P:j;b[D]>w[D]&&(R=st(R));var A=st(R),q=[];if(o&&q.push(k[z]<=0),a&&q.push(k[R]<=0,k[A]<=0),q.every((function(t){return t}))){O=I,E=!1;break}S.set(I,q)}if(E)for(var L=function(t){var e=m.find((function(e){var n=S.get(e);if(n)return n.slice(0,t).every((function(t){return t}))}));if(e)return O=e,"break"},U=d?3:1;U>0&&"break"!==L(U);U--);e.placement!==O&&(e.modifiersData[r]._skip=!0,e.placement=O,e.reset=!0)}},requiresIfExists:["offset"],data:{_skip:!1}},{name:"preventOverflow",enabled:!0,phase:"main",fn:function(t){var e=t.state,n=t.options,r=t.name,i=n.mainAxis,o=void 0===i||i,s=n.altAxis,a=void 0!==s&&s,u=n.boundary,c=n.rootBoundary,f=n.altBoundary,l=n.padding,h=n.tether,p=void 0===h||h,d=n.tetherOffset,v=void 0===d?0:d,_=dt(e,{boundary:u,rootBoundary:c,padding:l,altBoundary:f}),m=Z(e.placement),b=tt(e.placement),w=!b,S=et(m),E="x"===S?"y":"x",O=e.modifiersData.popperOffsets,x=e.rects.reference,I=e.rects.popper,z="function"==typeof v?v(Object.assign({},e.rects,{placement:e.placement})):v,C="number"==typeof z?{mainAxis:z,altAxis:z}:Object.assign({mainAxis:0,altAxis:0},z),D=e.modifiersData.offset?e.modifiersData.offset[e.placement]:null,k={x:0,y:0};if(O){if(o){var R,A="y"===S?j:T,F="y"===S?P:B,K="y"===S?"height":"width",L=O[S],U=L+_[A],W=L-_[F],N=p?-I[K]/2:0,H=b===V?x[K]:I[K],J=b===V?-I[K]:-x[K],$=e.elements.arrow,Y=p&&$?M($):{width:0,height:0},X=e.modifiersData["arrow#persistent"]?e.modifiersData["arrow#persistent"].padding:{top:0,right:0,bottom:0,left:0},G=X[A],Q=X[F],nt=vt(0,x[K],Y[K]),rt=w?x[K]/2-N-nt-G-C.mainAxis:H-nt-G-C.mainAxis,it=w?-x[K]/2+N+nt+Q+C.mainAxis:J+nt+Q+C.mainAxis,ot=e.elements.arrow&&q(e.elements.arrow),st=ot?"y"===S?ot.clientTop||0:ot.clientLeft||0:0,at=null!=(R=null==D?void 0:D[S])?R:0,ut=L+it-at,ct=vt(p?g(U,L+rt-at-st):U,L,p?y(W,ut):W);O[S]=ct,k[S]=ct-L}if(a){var ft,lt="x"===S?j:T,ht="x"===S?P:B,pt=O[E],_t="y"===E?"height":"width",yt=pt+_[lt],gt=pt-_[ht],mt=-1!==[j,T].indexOf(m),bt=null!=(ft=null==D?void 0:D[E])?ft:0,wt=mt?yt:pt-x[_t]-I[_t]-bt+C.altAxis,St=mt?pt+x[_t]+I[_t]-bt-C.altAxis:gt,Et=p&&mt?function(t,e,n){var r=vt(t,e,n);return r>n?n:r}(wt,pt,St):vt(p?wt:yt,pt,p?St:gt);O[E]=Et,k[E]=Et-pt}e.modifiersData[r]=k}},requiresIfExists:["offset"]},{name:"arrow",enabled:!0,phase:"main",fn:function(t){var e,n=t.state,r=t.name,i=t.options,o=n.elements.arrow,s=n.modifiersData.popperOffsets,a=Z(n.placement),u=et(a),c=[T,B].indexOf(a)>=0?"height":"width";if(o&&s){var f=function(t,e){return ht("number"!=typeof(t="function"==typeof t?t(Object.assign({},e.rects,{placement:e.placement})):t)?t:pt(t,K))}(i.padding,n),l=M(o),h="y"===u?j:T,p="y"===u?P:B,d=n.rects.reference[c]+n.rects.reference[u]-s[u]-n.rects.popper[c],v=s[u]-n.rects.reference[u],_=q(o),y=_?"y"===u?_.clientHeight||0:_.clientWidth||0:0,g=d/2-v/2,m=f[h],b=y-l[c]-f[p],w=y/2-l[c]/2+g,S=vt(m,w,b),E=u;n.modifiersData[r]=((e={})[E]=S,e.centerOffset=S-w,e)}},effect:function(t){var e=t.state,n=t.options.element,r=void 0===n?"[data-popper-arrow]":n;null!=r&&("string"!=typeof r||(r=e.elements.popper.querySelector(r)))&&ct(e.elements.popper,r)&&(e.elements.arrow=r)},requires:["popperOffsets"],requiresIfExists:["preventOverflow"]},{name:"hide",enabled:!0,phase:"main",requiresIfExists:["preventOverflow"],fn:function(t){var e=t.state,n=t.name,r=e.rects.reference,i=e.rects.popper,o=e.modifiersData.preventOverflow,s=dt(e,{elementContext:"reference"}),a=dt(e,{altBoundary:!0}),u=_t(s,r),c=_t(a,i,o),f=yt(u),l=yt(c);e.modifiersData[n]={referenceClippingOffsets:u,popperEscapeOffsets:c,isReferenceHidden:f,hasPopperEscaped:l},e.attributes.popper=Object.assign({},e.attributes.popper,{"data-popper-reference-hidden":f,"data-popper-escaped":l})}}]}),mt=n(69590),bt=n.n(mt),wt=function(t){return t.reduce((function(t,e){var n=e[0],r=e[1];return t[n]=r,t}),{})},St="undefined"!=typeof window&&window.document&&window.document.createElement?i.useLayoutEffect:i.useEffect,Et=[];const Ot=window.lodash;function xt(){return xt=Object.assign||function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(t[r]=n[r])}return t},xt.apply(this,arguments)}function It(t,e){return It=Object.setPrototypeOf||function(t,e){return t.__proto__=e,t},It(t,e)}function zt(t,e){if(null==t)return{};var n,r,i={},o=Object.keys(t);for(r=0;r<o.length;r++)n=o[r],e.indexOf(n)>=0||(i[n]=t[n]);return i}function Mt(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=new Array(e);n<e;n++)r[n]=t[n];return r}function Ct(t){var e=t.mention,n=t.children,r=t.className;return o().createElement("a",{href:e.link,className:r,spellCheck:!1,"data-testid":"mentionLink"},n)}function Dt(t){var e=t.children,n=t.className;return o().createElement("span",{className:n,spellCheck:!1,"data-testid":"mentionText"},e)}function kt(t){var e=t.entityKey,n=t.theme,r=void 0===n?{}:n,i=t.mentionComponent,s=t.children,u=t.decoratedText,c=t.className,f=t.contentState,l=a(r.mention,c),h=f.getEntity(e).getData().mention,p=i||(h.link?Ct:Dt);return o().createElement(p,{entityKey:e,mention:h,theme:r,className:l,decoratedText:u},s)}var Rt=function(t){var e=t.onMentionSelect,n=t.mention,r=t.theme,s=t.index,a=t.onMentionFocus,u=t.isFocused,c=t.id,f=t.searchValue,l=t.entryComponent,h=(0,i.useRef)(!1),p=(0,i.useRef)(null);(0,i.useEffect)((function(){u&&requestAnimationFrame((function(){var t;return null==(t=p.current)?void 0:t.scrollIntoView({behavior:"smooth",block:"nearest"})}))}),[u]),(0,i.useEffect)((function(){h.current=!1}));var d=u?r.mentionSuggestionsEntryFocused:r.mentionSuggestionsEntry;return o().createElement("div",{ref:p},o().createElement(l,{className:d,onMouseDown:function(t){t.preventDefault(),h.current=!0},onMouseUp:function(){h.current&&(e(n),h.current=!1)},onMouseEnter:function(){a(s)},role:"option",id:c,"aria-selected":u?"true":void 0,theme:r,mention:n,isFocused:u,searchValue:f,selectMention:e}))};Rt.propTypes={entryComponent:c().any.isRequired,searchValue:c().string,onMentionSelect:c().func};var At=Rt,qt=function(t,e,n){var r=e.getAnchorKey(),i=e.getAnchorOffset();return function(t,e,n){for(var r,i=t.substr(0,e),o=n.map((function(t){return h()(t)})).join("|"),s=new RegExp("(\\s|^)("+o+")","g"),a=0,u=0,c=function(t,e){var n="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(n)return(n=n.call(t)).next.bind(n);if(Array.isArray(t)||(n=function(t,e){if(t){if("string"==typeof t)return Mt(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);return"Object"===n&&t.constructor&&(n=t.constructor.name),"Map"===n||"Set"===n?Array.from(t):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?Mt(t,e):void 0}}(t))||e&&t&&"number"==typeof t.length){n&&(t=n);var r=0;return function(){return r>=t.length?{done:!0}:{done:!1,value:t[r++]}}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(i.matchAll(s));!(r=c()).done;){var f=r.value,l=f[1].length,p=f[2].length;u=(a=(f.index||0)+l)+p}var d=i.slice(u);return{begin:a,end:i.length,matchingString:d}}(t.getCurrentContent().getBlockForKey(r).getText(),i,n)};function jt(t){return"@"===t?"mention":t+"mention"}function Pt(t,e,n,r,i){var o=t.getCurrentContent().createEntity(jt(r),i,{mention:e}).getLastCreatedEntityKey(),s=t.getSelection(),a=qt(t,s,[r]),u=a.begin,c=a.end,l=s.merge({anchorOffset:u,focusOffset:c}),h=f.Modifier.replaceText(t.getCurrentContent(),l,""+n+e.name,void 0,o),p=l.getAnchorKey();t.getCurrentContent().getBlockForKey(p).getLength()===c&&(h=f.Modifier.insertText(h,h.getSelectionAfter()," "));var d=f.EditorState.push(t,h,"insert-fragment");return f.EditorState.forceSelection(d,h.getSelectionAfter())}function Bt(t){var e=t.mention,n=t.theme,r=void 0===n?{}:n;return e.avatar?o().createElement("img",{src:e.avatar,className:r.mentionSuggestionsEntryAvatar,role:"presentation"}):null}var Tt=["mention","theme","isFocused","searchValue","selectMention"];function Ft(t){var e=t.mention,n=t.theme;t.isFocused,t.searchValue,t.selectMention;var r=zt(t,Tt);return o().createElement("div",r,o().createElement(Bt,{mention:e,theme:n}),o().createElement("span",{className:null==n?void 0:n.mentionSuggestionsEntryText},e.name))}var Kt=function t(e){return e?"static"!==window.getComputedStyle(e).getPropertyValue("position")?e:t(e.parentElement):null};function Vt(t){var e,n=t.decoratorRect,r=t.popover,i=t.props,o=Kt(r.parentElement);if(o){var s=o.getBoundingClientRect();e={scrollLeft:o.scrollLeft,scrollTop:o.scrollTop,left:n.left-s.left,top:n.bottom-s.top}}else e={scrollTop:window.pageYOffset||document.documentElement.scrollTop,scrollLeft:window.pageXOffset||document.documentElement.scrollLeft,top:n.bottom,left:n.left};var a,u,c=e.left+e.scrollLeft,f=e.top+e.scrollTop;return i.open&&(i.suggestions.length>0?(a="scale(1)",u="all 0.25s cubic-bezier(.3,1.2,.2,1)"):(a="scale(0)",u="all 0.35s cubic-bezier(.3,1,.2,1)")),{left:c+"px",top:f+"px",transform:a,transformOrigin:"1em 0%",transition:u}}function Lt(t){return void 0!==t}function Ut(t){var e=t.store,n=t.children,r=t.theme,s=t.popperOptions,u=void 0===s?{placement:"bottom-start"}:s,c=(0,i.useState)((function(){return a(r.mentionSuggestions,r.mentionSuggestionsPopup)})),f=c[0],l=c[1],h=(0,i.useState)(null),p=h[0],d=h[1],v=function(t,e,n){void 0===n&&(n={});var r=i.useRef(null),o={onFirstUpdate:n.onFirstUpdate,placement:n.placement||"bottom",strategy:n.strategy||"absolute",modifiers:n.modifiers||Et},s=i.useState({styles:{popper:{position:o.strategy,left:"0",top:"0"},arrow:{position:"absolute"}},attributes:{}}),a=s[0],u=s[1],c=i.useMemo((function(){return{name:"updateState",enabled:!0,phase:"write",fn:function(t){var e=t.state,n=Object.keys(e.elements);u({styles:wt(n.map((function(t){return[t,e.styles[t]||{}]}))),attributes:wt(n.map((function(t){return[t,e.attributes[t]]})))})},requires:["computeStyles"]}}),[]),f=i.useMemo((function(){var t={onFirstUpdate:o.onFirstUpdate,placement:o.placement,strategy:o.strategy,modifiers:[].concat(o.modifiers,[c,{name:"applyStyles",enabled:!1}])};return bt()(r.current,t)?r.current||t:(r.current=t,t)}),[o.onFirstUpdate,o.placement,o.strategy,o.modifiers,c]),l=i.useRef();return St((function(){l.current&&l.current.setOptions(f)}),[f]),St((function(){if(null!=t&&null!=e){var r=(n.createPopper||gt)(t,e,f);return l.current=r,function(){r.destroy(),l.current=null}}}),[t,e,n.createPopper]),{state:l.current?l.current.state:null,styles:a.styles,attributes:a.attributes,update:l.current?l.current.update:null,forceUpdate:l.current?l.current.forceUpdate:null}}(e.getReferenceElement(),p,u),_=v.styles,y=v.attributes;return(0,i.useEffect)((function(){requestAnimationFrame((function(){return l(a(r.mentionSuggestions,r.mentionSuggestionsPopup,r.mentionSuggestionsPopupVisible))}))}),[r]),o().createElement("div",xt({ref:d,style:_.popper},y.popper,{className:f}),n)}var Wt=(0,Ot.once)((function(t){})),Nt=["entryComponent","popoverComponent","popperOptions","popoverContainer","onOpenChange","onAddMention","onSearchChange","suggestions","ariaProps","callbacks","theme","store","entityMutability","positionSuggestions","mentionTriggers","mentionPrefix"],Ht=function(t){var e,n;function r(e){var n;return(n=t.call(this,e)||this).state={focusedOptionIndex:0},n.key=(0,f.genKey)(),n.popover=void 0,n.activeOffsetKey=void 0,n.lastSearchValue=void 0,n.lastActiveTrigger="",n.lastSelectionIsInsideWord=void 0,n.onEditorStateChange=function(t){var e=n.props.store.getAllSearches();if(0===e.size)return t;var r=function(t,e,n){var r=t.getSelection(),i=r.getAnchorKey(),o=r.getAnchorOffset();if(!r.isCollapsed()||!r.getHasFocus())return null;var s=e.map((function(t){return function(t){var e=t.split("-"),n=e[0],r=e[1],i=e[2];return{blockKey:n,decoratorKey:parseInt(r,10),leafKey:parseInt(i,10)}}(t)})).filter((function(t){return t.blockKey===i})).map((function(e){return t.getBlockTree(e.blockKey).getIn([e.decoratorKey])}));if(s.every((function(t){return void 0===t})))return null;var a=t.getCurrentContent().getBlockForKey(i).getText(),u=s.filter(Lt).map((function(t){var e=t.start,r=t.end;return n.map((function(t){return 0===e&&o>=e+t.length&&a.substr(0,t.length)===t&&o<=r||n.length>1&&o>=e+t.length&&(a.substr(e+1,t.length)===t||a.substr(e,t.length)===t)&&o<=r||1===n.length&&o>=e+t.length&&o<=r?t:void 0})).filter(Lt)[0]})).filter(Lt);if(u.isEmpty())return null;var c=u.entrySeq().first();return{activeOffsetKey:c[0],activeTrigger:c[1]}}(t,e,n.props.mentionTriggers);if(!r)return n.props.store.resetEscapedSearch(),n.closeDropdown(),t;var i=n.activeOffsetKey;return n.activeOffsetKey=r.activeOffsetKey,n.onSearchChange(t,t.getSelection(),n.activeOffsetKey,i,r.activeTrigger),n.props.store.isEscaped(n.activeOffsetKey||"")||n.props.store.resetEscapedSearch(),n.props.open||n.props.store.isEscaped(n.activeOffsetKey||"")||n.openDropdown(),i!==n.activeOffsetKey&&n.setState({focusedOptionIndex:0}),t},n.onSearchChange=function(t,e,r,i,o){var s=qt(t,e,[o]).matchingString;n.lastActiveTrigger===o&&n.lastSearchValue===s&&r===i||(n.lastActiveTrigger=o,n.lastSearchValue=s,n.props.onSearchChange({trigger:o,value:s}),n.setState({focusedOptionIndex:0}))},n.onDownArrow=function(t){t.preventDefault();var e=n.state.focusedOptionIndex+1;n.onMentionFocus(e>=n.props.suggestions.length?0:e)},n.onTab=function(t){t.preventDefault(),n.commitSelection()},n.onUpArrow=function(t){if(t.preventDefault(),n.props.suggestions.length>0){var e=n.state.focusedOptionIndex-1;n.onMentionFocus(e<0?n.props.suggestions.length-1:e)}},n.onEscape=function(t){t.preventDefault(),n.props.store.escapeSearch(n.activeOffsetKey||""),n.closeDropdown(),n.props.store.setEditorState(n.props.store.getEditorState())},n.onMentionSelect=function(t){if(t){n.props.onAddMention&&n.props.onAddMention(t),n.closeDropdown();var e=Pt(n.props.store.getEditorState(),t,n.props.mentionPrefix,n.lastActiveTrigger||"",n.props.entityMutability);n.props.store.setEditorState(e)}},n.onMentionFocus=function(t){var e="mention-option-"+n.key+"-"+t;n.props.ariaProps.ariaActiveDescendantID=e,n.setState({focusedOptionIndex:t}),n.props.store.setEditorState(n.props.store.getEditorState())},n.commitSelection=function(){var t=n.props.suggestions[n.state.focusedOptionIndex];return n.props.store.getIsOpened()&&t?(n.onMentionSelect(t),"handled"):"not-handled"},n.openDropdown=function(){n.props.callbacks.handleReturn=n.commitSelection,n.props.callbacks.keyBindingFn=function(t){40===t.keyCode&&n.onDownArrow(t),38===t.keyCode&&n.onUpArrow(t),27===t.keyCode&&n.onEscape(t),9===t.keyCode&&n.onTab(t)};var t="mention-option-"+n.key+"-"+n.state.focusedOptionIndex;n.props.ariaProps.ariaActiveDescendantID=t,n.props.ariaProps.ariaOwneeID="mentions-list-"+n.key,n.props.ariaProps.ariaHasPopup="true",n.props.ariaProps.ariaExpanded=!0,n.props.onOpenChange(!0)},n.closeDropdown=function(){n.props.callbacks.handleReturn=void 0,n.props.callbacks.keyBindingFn=void 0,n.props.ariaProps.ariaHasPopup="false",n.props.ariaProps.ariaExpanded=!1,n.props.ariaProps.ariaActiveDescendantID=void 0,n.props.ariaProps.ariaOwneeID=void 0,n.props.onOpenChange(!1)},n.props.callbacks.onChange=n.onEditorStateChange,n}n=t,(e=r).prototype=Object.create(n.prototype),e.prototype.constructor=e,It(e,n);var i=r.prototype;return i.componentDidUpdate=function(){if(this.popover){var t=this.props.suggestions.length;if(t>0&&this.state.focusedOptionIndex>=t&&this.setState({focusedOptionIndex:t-1}),!this.props.store.getAllSearches().has(this.activeOffsetKey))return;for(var e=this.props.store.getPortalClientRect(this.activeOffsetKey),n=(this.props.positionSuggestions||Vt)({decoratorRect:e,props:this.props,popover:this.popover}),r=0,i=Object.entries(n);r<i.length;r++){var o=i[r],s=o[0],a=o[1];this.popover.style[s]=a}}},i.componentWillUnmount=function(){this.props.callbacks.onChange=void 0},i.render=function(){var t=this;if(!this.props.open)return null;var e=this.props,n=e.entryComponent,r=e.popoverComponent,i=e.popperOptions,s=e.popoverContainer,a=void 0===s?Ut:s;e.onOpenChange,e.onAddMention,e.onSearchChange,e.suggestions,e.ariaProps,e.callbacks;var u=e.theme,c=void 0===u?{}:u;e.store,e.entityMutability;var f=e.positionSuggestions;e.mentionTriggers,e.mentionPrefix;var l=zt(e,Nt);return r||f?(Wt("The properties `popoverComponent` and `positionSuggestions` are deprecated and will be removed in @draft-js-plugins/mentions 6.0 . Use `popperOptions` instead"),o().cloneElement(r||o().createElement("div",null),xt({},l,{className:c.mentionSuggestions,role:"listbox",id:"mentions-list-"+this.key,ref:function(e){t.popover=e}}),this.props.suggestions.map((function(e,r){return o().createElement(At,{key:null!=e.id?e.id:e.name,onMentionSelect:t.onMentionSelect,onMentionFocus:t.onMentionFocus,isFocused:t.state.focusedOptionIndex===r,mention:e,index:r,id:"mention-option-"+t.key+"-"+r,theme:c,searchValue:t.lastSearchValue,entryComponent:n||Ft})})))):this.props.renderEmptyPopup||0!==this.props.suggestions.length?o().createElement(a,{store:this.props.store,popperOptions:i,theme:c},this.props.suggestions.map((function(e,r){return o().createElement(At,{key:null!=e.id?e.id:e.name,onMentionSelect:t.onMentionSelect,onMentionFocus:t.onMentionFocus,isFocused:t.state.focusedOptionIndex===r,mention:e,index:r,id:"mention-option-"+t.key+"-"+r,theme:c,searchValue:t.lastSearchValue,entryComponent:n||Ft})}))):null},r}(i.Component);Ht.propTypes={open:c().bool.isRequired,onOpenChange:c().func.isRequired,entityMutability:c().oneOf(["SEGMENTED","IMMUTABLE","MUTABLE"]),entryComponent:c().func,onAddMention:c().func,suggestions:c().array.isRequired};var Jt=Ht,$t="undefined"!=typeof window?i.useLayoutEffect:i.useEffect;function Yt(t){var e=(0,i.useRef)(),n=function(t){t.store.updatePortalClientRect(t.offsetKey,(function(){return e.current.getBoundingClientRect()}))};return $t((function(){return t.store.register(t.offsetKey),t.store.setIsOpened(!0),n(t),t.store.setEditorState(t.store.getEditorState()),function(){t.store.unregister(t.offsetKey),t.store.setIsOpened(!1),t.store.setReferenceElement(null)}}),[]),(0,i.useEffect)((function(){n(t)})),o().createElement("span",{ref:function(n){e.current=n,t.store.setReferenceElement(n)}},t.children)}var Xt={mention:"m6zwb4v",mentionSuggestions:"mnw6qvm",mentionSuggestionsPopup:"m1ymsnxd",mentionSuggestionsPopupVisible:"m126ak5t",mentionSuggestionsEntry:"mtiwdxc",mentionSuggestionsEntryFocused:"myz2dw1",mentionSuggestionsEntryText:"mpqdcgq",mentionSuggestionsEntryAvatar:"m1mfvffo"},Gt=function(t){return function(e,n,r){e.findEntityRanges((function(e){var n=e.getEntity();return null!==n&&t.some((function(t){return r.getEntity(n).getType()===jt(t)}))}),n)}},Qt=/\s/;function Zt(t,e){return 0===e||Qt.test(t[e-1])}var te=function(t){void 0===t&&(t={});var e,n,i,s,a,u,c,f={keyBindingFn:void 0,handleKeyCommand:void 0,handleReturn:void 0,onChange:void 0},l={ariaHasPopup:"false",ariaExpanded:!1,ariaOwneeID:void 0,ariaActiveDescendantID:void 0},p=(0,r.Map)(),d=(0,r.Map)(),v=!1,_={getEditorState:void 0,setEditorState:void 0,getPortalClientRect:function(t){return d.get(t)()},getAllSearches:function(){return p},isEscaped:function(t){return e===t},escapeSearch:function(t){e=t},resetEscapedSearch:function(){e=void 0},register:function(t){p=p.set(t,t)},updatePortalClientRect:function(t,e){d=d.set(t,e)},unregister:function(t){p=p.delete(t),d=d.delete(t)},getIsOpened:function(){return v},setIsOpened:function(t){v=t},getReferenceElement:function(){return n},setReferenceElement:function(t){n=t}},y=t,g=y.mentionPrefix,m=void 0===g?"":g,b=y.theme,w=void 0===b?Xt:b,S=y.positionSuggestions,E=y.mentionComponent,O=y.mentionSuggestionsComponent,x=void 0===O?Jt:O,I=y.entityMutability,z=void 0===I?"SEGMENTED":I,M=y.mentionTrigger,C=void 0===M?"@":M,D=y.mentionRegExp,k=void 0===D?"[\\w-À-ÖØ-öø-ÿĀ-ňŊ-ſА-я぀-ゟ゠-ヿ㄰-㆏가-힣一-龥؀-ۿÀ-ỹ]":D,R=y.supportWhitespace,A=void 0!==R&&R,q=y.popperOptions,j="string"==typeof C?[C]:C,P={ariaProps:l,callbacks:f,theme:w,store:_,entityMutability:z,positionSuggestions:S,mentionTriggers:j,mentionPrefix:m,popperOptions:q};return{MentionSuggestions:function(t){return o().createElement(x,xt({},t,P))},decorators:[{strategy:Gt(j),component:function(t){return o().createElement(kt,xt({},t,{theme:w,mentionComponent:E}))}},{strategy:(i=j,s=A,a=k,u="("+i.map((function(t){return h()(t)})).join("|")+")",c=s?new RegExp(u+"("+a+"|\\s)*","g"):new RegExp("(\\s|^)"+u+a+"*","g"),function(t,e){!function(t,e,n,r){var i=e.getText();e.findEntityRanges((function(t){return!t.getEntity()}),(function(e,o){var s=i.slice(e,o);n?function(t,e,n,r){for(var i,o,s=t.lastIndex;null!==(i=t.exec(e))&&t.lastIndex!==s;){s=t.lastIndex;var a=(o=n+i.index)+i[0].length;Zt(e,i.index)&&r(o,a)}}(t,s,e,r):function(t,e,n,r){for(var i,o,s=t.lastIndex;null!==(i=t.exec(e))&&t.lastIndex!==s;){s=t.lastIndex;var a=(o=n+i.index)+i[0].length;Qt.test(e[o])&&(o+=1),r(o,a)}}(t,s,e,r)}))}(c,t,s,e)}),component:function(t){return o().createElement(Yt,xt({},t,{store:_}))}}],getAccessibilityProps:function(){return{role:"combobox",ariaAutoComplete:"list",ariaHasPopup:l.ariaHasPopup,ariaExpanded:l.ariaExpanded,ariaActiveDescendantID:l.ariaActiveDescendantID,ariaOwneeID:l.ariaOwneeID}},initialize:function(t){var e=t.getEditorState,n=t.setEditorState;_.getEditorState=e,_.setEditorState=n},keyBindingFn:function(t){return f.keyBindingFn&&f.keyBindingFn(t)},handleReturn:function(t){return f.handleReturn&&f.handleReturn(t)},onChange:function(t){return f.onChange?f.onChange(t):t}}},ee=function(t,e,n){var r=t.toLowerCase(),i=(n&&!Array.isArray(e)?e[n]:e).filter((function(t){return!r||t.name.toLowerCase().indexOf(r)>-1})),o=i.length<5?i.length:5;return i.slice(0,o)}},82080:(t,e,n)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.Mention=void 0;var r=a(n(99196)),i=a(n(98487)),o=a(n(85890)),s=a(n(70602));function a(t){return t&&t.__esModule?t:{default:t}}const u=i.default.span`
	color: rgb(15 23 42);
	background-color: rgb(226 232 240);
	padding: 0.125rem 0.5rem;
	margin: 0 0.125rem;
	border-radius: 17px;
  	font-size: .75rem;
  	font-weight: 500;
  	line-height: 1.25;
}
	&:hover {
      color: rgb(15 23 42);
	  background-color: rgb(226 232 240);
	  cursor: auto;
	}
`,c=({children:t,className:e})=>r.default.createElement(u,{className:(0,s.default)("yst-replacevar__mention",e),spellCheck:!1},t);e.Mention=c,c.propTypes={children:o.default.node.isRequired,className:o.default.string.isRequired}},51381:(t,e,n)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var r=n(92694),i=function(t,e){if(t&&t.__esModule)return t;if(null===t||"object"!=typeof t&&"function"!=typeof t)return{default:t};var n=v(e);if(n&&n.has(t))return n.get(t);var r={__proto__:null},i=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var o in t)if("default"!==o&&Object.prototype.hasOwnProperty.call(t,o)){var s=i?Object.getOwnPropertyDescriptor(t,o):null;s&&(s.get||s.set)?Object.defineProperty(r,o,s):r[o]=t[o]}return r.default=t,n&&n.set(t,r),r}(n(99196)),o=d(n(85890)),s=d(n(12049)),a=n(65736),u=n(55609),c=d(n(42578)),f=n(37188),l=n(32183),h=n(34353),p=n(81413);function d(t){return t&&t.__esModule?t:{default:t}}function v(t){if("function"!=typeof WeakMap)return null;var e=new WeakMap,n=new WeakMap;return(v=function(t){return t?n:e})(t)}class _ extends i.default.Component{constructor(t){super(t),this.uniqueId=(0,s.default)("replacement-variable-editor-field-"),"description"===t.type?this.InputContainer=l.DescriptionInputContainer:this.InputContainer=l.TitleInputContainer,t.withCaret&&(this.InputContainer=(0,f.withCaretStyles)(this.InputContainer)),this.triggerReplacementVariableSuggestions=this.triggerReplacementVariableSuggestions.bind(this)}triggerReplacementVariableSuggestions(){this.ref.triggerReplacementVariableSuggestions()}render(){const{label:t,onChange:e,content:n,onFocus:o,onBlur:s,isActive:f,isHovered:h,onSearchChange:d,replacementVariables:v,recommendedReplacementVariables:_,editorRef:y,placeholder:g,fieldId:m,onMouseEnter:b,onMouseLeave:w,hasNewBadge:S,isDisabled:E,hasPremiumBadge:O,type:x}=this.props,I=this.InputContainer,z=(0,r.applyFilters)("yoast.replacementVariableEditor.additionalButtons",[],{fieldId:m,type:x});return i.default.createElement(l.FormSection,{className:["yst-replacevar",E&&"yst-replacevar--disabled"].filter(Boolean).join(" "),onMouseEnter:b,onMouseLeave:w},i.default.createElement(p.SimulatedLabel,{className:"yst-replacevar__label",id:this.uniqueId,onClick:o},t),O&&i.default.createElement(p.PremiumBadge,{inLabel:!0}),S&&i.default.createElement(p.NewBadge,{inLabel:!0}),i.default.createElement(l.ButtonsContainer,{className:"yst-replacevar__buttons"},i.default.createElement(u.Slot,{name:`yoast.replacementVariableEditor.additionalButtons.${m}`}),z.map(((t,e)=>i.default.createElement(i.Fragment,{key:`additional-button-${e}-${m}`},t))),i.default.createElement(u.Slot,{key:`PluginComponent-${m}`,name:`PluginComponent-${m}`}),i.default.createElement(l.TriggerReplacementVariableSuggestionsButton,{className:"yst-replacevar__button-insert",onClick:this.triggerReplacementVariableSuggestions,disabled:E},(0,a.__)("Insert variable","wordpress-seo"))),i.default.createElement(I,{className:"yst-replacevar__editor",onClick:o,isActive:f&&!E,isHovered:h},i.default.createElement(c.default,{fieldId:m,placeholder:g,content:n,onChange:e,onFocus:o,onBlur:s,onSearchChange:d,replacementVariables:v,recommendedReplacementVariables:_,ref:t=>{this.ref=t,y(t)},ariaLabelledBy:this.uniqueId,isDisabled:E})))}}_.propTypes={editorRef:o.default.func,content:o.default.string.isRequired,onChange:o.default.func.isRequired,onBlur:o.default.func,onSearchChange:o.default.func,replacementVariables:h.replacementVariablesShape,recommendedReplacementVariables:h.recommendedReplacementVariablesShape,isActive:o.default.bool,isHovered:o.default.bool,withCaret:o.default.bool,onFocus:o.default.func,label:o.default.string,placeholder:o.default.string,type:o.default.oneOf(["title","description"]).isRequired,fieldId:o.default.string,onMouseEnter:o.default.func,onMouseLeave:o.default.func,hasNewBadge:o.default.bool,isDisabled:o.default.bool,hasPremiumBadge:o.default.bool},_.defaultProps={onFocus:()=>{},onBlur:()=>{},onSearchChange:null,replacementVariables:[],recommendedReplacementVariables:[],fieldId:"",placeholder:"",label:"",withCaret:!1,isHovered:!1,isActive:!1,editorRef:()=>{},onMouseEnter:()=>{},onMouseLeave:()=>{},hasNewBadge:!1,isDisabled:!1,hasPremiumBadge:!1},e.default=_},42578:(t,e,n)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=e.ReplacementVariableEditorStandaloneInnerComponent=void 0;var r=E(n(99196)),i=E(n(37166)),o=E(n(95423)),s=E(n(60940)),a=E(n(25853)),u=E(n(66366)),c=E(n(1843)),f=E(n(16965)),l=E(n(18491)),h=E(n(85890)),p=n(25158),d=n(92694),v=n(65736),_=function(t,e){if(t&&t.__esModule)return t;if(null===t||"object"!=typeof t&&"function"!=typeof t)return{default:t};var n=S(e);if(n&&n.has(t))return n.get(t);var r={__proto__:null},i=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var o in t)if("default"!==o&&Object.prototype.hasOwnProperty.call(t,o)){var s=i?Object.getOwnPropertyDescriptor(t,o):null;s&&(s.get||s.set)?Object.defineProperty(r,o,s):r[o]=t[o]}return r.default=t,n&&n.set(t,r),r}(n(98487)),y=n(34353),g=n(82080),m=n(31689),b=n(17481),w=n(45608);function S(t){if("function"!=typeof WeakMap)return null;var e=new WeakMap,n=new WeakMap;return(S=function(t){return t?n:e})(t)}function E(t){return t&&t.__esModule?t:{default:t}}const O=_.default.div`
	div {
		z-index: 10995;
	}
	> div {
		max-height: 450px;
		overflow-y: auto;
	}
`,x=new RegExp("(?:\\p{RI}\\p{RI}|\\p{Emoji}(?:\\p{Emoji_Modifier}|\\u{FE0F}\\u{20E3}?|[\\u{E0020}-\\u{E007E}]+\\u{E007F})?(?:\\u{200D}\\p{Emoji}(?:\\p{Emoji_Modifier}|\\u{FE0F}\\u{20E3}?|[\\u{E0020}-\\u{E007E}]+\\u{E007F})?)*)","gu");class I extends r.default.Component{constructor(t){super(t);const{content:e,replacementVariables:n,recommendedReplacementVariables:r}=this.props,i=(0,m.unserializeEditor)(e,n),o=this.determineCurrentReplacementVariables(n,r);this.state={editorState:i,searchValue:"",isSuggestionsOpen:!1,editorKey:this.props.fieldId,suggestions:this.mapReplacementVariablesToSuggestions(o)},this._serializedContent=e,this.initializeBinds(),this.initializeDraftJsPlugins()}initializeBinds(){this.onChange=this.onChange.bind(this),this.handleKeyCommand=this.handleKeyCommand.bind(this),this.onSearchChange=this.onSearchChange.bind(this),this.setEditorRef=this.setEditorRef.bind(this),this.handleCopyCutEvent=this.handleCopyCutEvent.bind(this),this.debouncedA11ySpeak=(0,a.default)(p.speak.bind(this),500),this.onSuggestionsOpenChange=this.onSuggestionsOpenChange.bind(this)}initializeDraftJsPlugins(){const t=(0,o.default)({mentionTrigger:"%",entityMutability:"IMMUTABLE",mentionComponent:g.Mention}),e=(0,s.default)({stripEntities:!1});this.pluginList={mentionsPlugin:t,singleLinePlugin:{...e,handleReturn:()=>{}}},this.pluginList=(0,d.applyFilters)("yoast.replacementVariableEditor.pluginList",this.pluginList)}serializeContent(t){const e=(0,m.serializeEditor)(t.getCurrentContent());this._serializedContent!==e&&(this._serializedContent=e,this.props.onChange(this._serializedContent))}onChange(t){return new Promise((e=>{t=(0,m.replaceReplacementVariables)(t,this.props.replacementVariables),t=(0,w.selectReplacementVariables)(t,this.state.editorState),this.setState({editorState:t},(()=>{this.serializeContent(t),e()}))}))}handleKeyCommand(t){if("backspace"!==t&&"delete"!==t)return"not-handled";let e=(0,b.removeSelectedText)(this.state.editorState);const n=e.getCurrentContent(),r=e.getSelection();if(!r.isCollapsed())return"not-handled";const i=r.getStartOffset();if(i<0)return"not-handled";const o=n.getBlockForKey(r.getStartKey()).getText(),s="backspace"===t?i-1:i+1;if((o.codePointAt(s)||0)<=127)return"not-handled";let a;return a="backspace"===t?this.getBackwardMatch(o,i):this.getForwardMatch(o,i),a?(e=(0,b.removeEmojiCompletely)(e,a,t),this.onChange(e).then((()=>this.focus())),"handled"):"not-handled"}getForwardMatch(t,e){let n=1;return[2,3,4,5,6,7,8,9,10,11,12,13,14].every((r=>{const i=t.slice(e,e+r);return!(null===i.match(x)||i.match(x).length>1||(n=r,0))})),t.slice(e,e+n).match(x)}getBackwardMatch(t,e){return t.slice(0,e).match(x)}mapReplacementVariablesToSuggestions(t){return t.map((t=>({...t,name:t.label,replaceName:t.name})))}suggestionsFilter(t,e){const n=t.toLowerCase();return e.filter((function(t){return!(t.hidden||n&&0!==t.name.toLowerCase().indexOf(n))}))}determineCurrentReplacementVariables(t,e,n=""){if(""===n&&!(0,u.default)(e)){const n=(0,c.default)(t,(t=>(0,f.default)(e,t.name)));if(0!==n.length)return n}return t}onSearchChange({value:t}){this.props.onSearchChange&&this.props.onSearchChange(t);const e=this.determineCurrentReplacementVariables(this.props.replacementVariables,this.props.recommendedReplacementVariables,t),n=this.mapReplacementVariablesToSuggestions(e);this.setState({searchValue:t,suggestions:this.suggestionsFilter(t,n)}),setTimeout((()=>{this.announceSearchResults()}))}onSuggestionsOpenChange(t){this.setState({isSuggestionsOpen:t})}announceSearchResults(){const{suggestions:t}=this.state;t.length?this.debouncedA11ySpeak((0,v.sprintf)((0,v._n)("%d result found, use up and down arrow keys to navigate","%d results found, use up and down arrow keys to navigate",t.length,"wordpress-seo"),t.length),"assertive"):this.debouncedA11ySpeak((0,v.__)("No results","wordpress-seo"),"assertive")}focus(){this.editor.focus()}setEditorRef(t){this.editor=t}setEditorFieldId(){(0,l.default)(this.editor,"editor.editor").id=this.props.fieldId}triggerReplacementVariableSuggestions(){let t=(0,b.removeSelectedText)(this.state.editorState);const e=t.getSelection(),n=t.getCurrentContent(),r=(0,b.getAnchorBlock)(n,e).getText(),i=(0,b.getCaretOffset)(e),o=!(0,b.hasWhitespaceAt)(r,i-1),s=!(0,b.hasWhitespaceAt)(r,i),a=(0,b.getTrigger)(o,s);if(t=(0,b.insertText)(t,a),s){const e=i+a.length-1;t=(0,b.moveCaret)(t,e)}this.onChange(t).then((()=>this.focus()))}componentDidUpdate(t,e){const{content:n,replacementVariables:r,recommendedReplacementVariables:i}=t,{searchValue:o}=this.state,s={},a=this.props,u=a.content!==this._serializedContent&&a.content!==n,c=a.replacementVariables!==r,f=a.replacementVariables.map((t=>t.name)).filter((t=>!r.map((t=>t.name)).includes(t))).some((t=>n.includes("%%"+t+"%%")));if(u&&(this._serializedContent=a.content,s.editorState=(0,m.unserializeEditor)(a.content,a.replacementVariables)),!u&&c&&f&&(this._serializedContent=a.content,s.editorState=(0,m.unserializeEditor)(a.content,a.replacementVariables)),c){const t=this.determineCurrentReplacementVariables(a.replacementVariables,i,o);s.suggestions=this.suggestionsFilter(o,this.mapReplacementVariablesToSuggestions(t))}(c||u)&&this.setState({...e,...s})}handleCopyCutEvent(t){const{editorState:e}=this.state,n=e.getSelection();if(n.getHasFocus())try{const r=t.clipboardData,i=e.getCurrentContent(),o=(0,m.serializeSelection)(i,n);r.setData("text/plain",o),t.preventDefault()}catch(t){console.error("Couldn't copy content of editor to clipboard, defaulting to browser copy behavior."),console.error("Original error: ",t)}}componentDidMount(){document.addEventListener("copy",this.handleCopyCutEvent),document.addEventListener("cut",this.handleCopyCutEvent),this.setEditorFieldId()}componentWillUnmount(){this.debouncedA11ySpeak.cancel(),document.removeEventListener("copy",this.handleCopyCutEvent),document.removeEventListener("cut",this.handleCopyCutEvent)}render(){const{MentionSuggestions:t}=this.pluginList.mentionsPlugin,{onFocus:e,onBlur:n,ariaLabelledBy:o,placeholder:s,theme:a,isDisabled:u,fieldId:c}=this.props,{editorState:f,suggestions:l,isSuggestionsOpen:h}=this.state;return r.default.createElement(r.default.Fragment,null,r.default.createElement(i.default,{key:this.state.editorKey,textDirectionality:a.isRtl?"RTL":"LTR",editorState:f,handleKeyCommand:this.handleKeyCommand,onChange:this.onChange,onFocus:e,onBlur:n,plugins:Object.values(this.pluginList),ref:this.setEditorRef,stripPastedStyles:!0,ariaLabelledBy:o,placeholder:s,spellCheck:!0,readOnly:u}),(0,d.applyFilters)("yoast.replacementVariableEditor.additionalPlugins",r.default.createElement(r.default.Fragment,null),this.pluginList,c),r.default.createElement(O,null,r.default.createElement(t,{onSearchChange:this.onSearchChange,suggestions:l,onOpenChange:this.onSuggestionsOpenChange,open:h})))}}e.ReplacementVariableEditorStandaloneInnerComponent=I,I.propTypes={content:h.default.string.isRequired,replacementVariables:y.replacementVariablesShape.isRequired,recommendedReplacementVariables:y.recommendedReplacementVariablesShape,ariaLabelledBy:h.default.string.isRequired,onSearchChange:h.default.func,onChange:h.default.func.isRequired,onFocus:h.default.func,onBlur:h.default.func,theme:h.default.object,placeholder:h.default.string,fieldId:h.default.string.isRequired,isDisabled:h.default.bool},I.defaultProps={onSearchChange:null,onFocus:()=>{},onBlur:()=>{},placeholder:"",theme:{isRtl:!1},recommendedReplacementVariables:[],isDisabled:!1},e.default=(0,_.withTheme)(I)},26895:(t,e,n)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var r=u(n(99196)),i=u(n(85890)),o=n(81413),s=u(n(9283)),a=n(34353);function u(t){return t&&t.__esModule?t:{default:t}}class c extends r.default.Component{constructor(t){super(t),this.state={activeField:null,hoveredField:null},this.setFieldFocus=this.setFieldFocus.bind(this),this.handleChange=this.handleChange.bind(this),this.onClick=this.onClick.bind(this),this.onBlur=this.onBlur.bind(this)}handleChange(t,e){this.props.onChange(t,e)}setFieldFocus(t){this.setState({activeField:t})}onBlur(){this.setState({activeField:null})}onClick(t){this.setFieldFocus(t)}render(){const{data:t,replacementVariables:e,recommendedReplacementVariables:n,descriptionEditorFieldPlaceholder:i,hasPaperStyle:a,fieldIds:u,labels:c,hasNewBadge:f,isDisabled:l,hasPremiumBadge:h}=this.props,{activeField:p,hoveredField:d}=this.state;return r.default.createElement(o.ErrorBoundary,null,r.default.createElement(s.default,{descriptionEditorFieldPlaceholder:i,data:t,activeField:p,hoveredField:d,onChange:this.handleChange,onFocus:this.setFieldFocus,onBlur:this.onBlur,replacementVariables:e,recommendedReplacementVariables:n,containerPadding:a?"0 20px":"0",fieldIds:u,labels:c,hasNewBadge:f,isDisabled:l,hasPremiumBadge:h}))}}c.propTypes={replacementVariables:a.replacementVariablesShape,recommendedReplacementVariables:a.recommendedReplacementVariablesShape,data:i.default.shape({title:i.default.string.isRequired,description:i.default.string.isRequired}).isRequired,onChange:i.default.func.isRequired,descriptionEditorFieldPlaceholder:i.default.string,hasPaperStyle:i.default.bool,fieldIds:i.default.shape({title:i.default.string.isRequired,description:i.default.string.isRequired}).isRequired,labels:i.default.shape({title:i.default.string,description:i.default.string}),hasNewBadge:i.default.bool,isDisabled:i.default.bool,hasPremiumBadge:i.default.bool},c.defaultProps={replacementVariables:[],recommendedReplacementVariables:[],hasPaperStyle:!0,descriptionEditorFieldPlaceholder:null,labels:{},hasNewBadge:!1,isDisabled:!1,hasPremiumBadge:!1},e.default=c},9283:(t,e,n)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=e.StyledEditor=void 0;var r=c(n(99196)),i=c(n(85890)),o=n(65736),s=c(n(98487)),a=c(n(51381)),u=n(34353);function c(t){return t&&t.__esModule?t:{default:t}}const f=e.StyledEditor=s.default.section`
	padding: ${t=>t.padding?t.padding:"0 20px"};
`;class l extends r.default.Component{constructor(t){super(t),this.elements={title:null,description:null},this.setRef=this.setRef.bind(this),this.setTitleRef=this.setTitleRef.bind(this),this.setDescriptionRef=this.setDescriptionRef.bind(this),this.triggerReplacementVariableSuggestions=this.triggerReplacementVariableSuggestions.bind(this),this.onFocusTitle=this.onFocusTitle.bind(this),this.onChangeTitle=this.onChangeTitle.bind(this),this.onFocusDescription=this.onFocusDescription.bind(this),this.onChangeDescription=this.onChangeDescription.bind(this)}setRef(t,e){this.elements[t]=e}setTitleRef(t){this.setRef("title",t)}setDescriptionRef(t){this.setRef("description",t)}componentDidUpdate(t){this.focusOnActiveFieldChange(t.activeField)}focusOnActiveFieldChange(t){const{activeField:e}=this.props;e&&e!==t&&this.elements[e].focus()}triggerReplacementVariableSuggestions(t){this.elements[t].triggerReplacementVariableSuggestions()}onFocusTitle(){this.props.onFocus("title")}onChangeTitle(t){this.props.onChange("title",t)}onFocusDescription(){this.props.onFocus("description")}onChangeDescription(t){this.props.onChange("description",t)}render(){const{descriptionEditorFieldPlaceholder:t,activeField:e,hoveredField:n,replacementVariables:i,recommendedReplacementVariables:s,onBlur:u,data:{title:c,description:l},containerPadding:h,fieldIds:p,labels:d,hasNewBadge:v,isDisabled:_,hasPremiumBadge:y}=this.props;return r.default.createElement(f,{padding:h},r.default.createElement(a.default,{type:"title",label:d.title||(0,o.__)("SEO title","wordpress-seo"),onFocus:this.onFocusTitle,onBlur:u,isActive:"title"===e,isHovered:"title"===n,editorRef:this.setTitleRef,replacementVariables:i,recommendedReplacementVariables:s,content:c,onChange:this.onChangeTitle,fieldId:p.title,hasNewBadge:v,isDisabled:_,hasPremiumBadge:y}),r.default.createElement(a.default,{type:"description",placeholder:t,label:d.description||(0,o.__)("Meta description","wordpress-seo"),onFocus:this.onFocusDescription,onBlur:u,isActive:"description"===e,isHovered:"description"===n,editorRef:this.setDescriptionRef,replacementVariables:i,recommendedReplacementVariables:s,content:l,onChange:this.onChangeDescription,fieldId:p.description,hasNewBadge:v,isDisabled:_,hasPremiumBadge:y}))}}l.propTypes={replacementVariables:u.replacementVariablesShape,recommendedReplacementVariables:u.recommendedReplacementVariablesShape,onChange:i.default.func.isRequired,onFocus:i.default.func,onBlur:i.default.func,data:i.default.shape({title:i.default.string,description:i.default.string}).isRequired,activeField:i.default.oneOf(["title","description"]),hoveredField:i.default.oneOf(["title","description"]),descriptionEditorFieldPlaceholder:i.default.string,containerPadding:i.default.string,fieldIds:i.default.shape({title:i.default.string.isRequired,description:i.default.string.isRequired}).isRequired,labels:i.default.shape({title:i.default.string,description:i.default.string}),hasNewBadge:i.default.bool,isDisabled:i.default.bool,hasPremiumBadge:i.default.bool},l.defaultProps={replacementVariables:[],recommendedReplacementVariables:[],onFocus:()=>{},onBlur:()=>{},containerPadding:"0 20px",descriptionEditorFieldPlaceholder:null,labels:{},hasNewBadge:!1,isDisabled:!1,hasPremiumBadge:!1,activeField:"",hoveredField:""},e.default=l},34353:(t,e,n)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.replacementVariablesShape=e.recommendedReplacementVariablesShape=void 0;var r,i=(r=n(85890))&&r.__esModule?r:{default:r};e.replacementVariablesShape=i.default.arrayOf(i.default.shape({name:i.default.string.isRequired,value:i.default.string.isRequired,label:i.default.string,description:i.default.string,hidden:i.default.bool})),e.recommendedReplacementVariablesShape=i.default.arrayOf(i.default.string)},17481:(t,e,n)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.removeSelectedText=e.removeEmojiCompletely=e.moveCaret=e.insertText=e.hasWhitespaceAt=e.getTrigger=e.getCaretOffset=e.getAnchorBlock=void 0;var r=n(7206);e.getTrigger=(t,e)=>{let n="%";return t&&(n=" "+n),e&&(n+=" "),n},e.hasWhitespaceAt=(t,e)=>{const n=t.charAt(e);return 0===n.length||/\s/.test(n)},e.getCaretOffset=t=>t.getIsBackward()?t.getEndOffset():t.getStartOffset();const i=(t,e)=>{const n=e.getAnchorKey();return t.getBlockForKey(n)};e.getAnchorBlock=i,e.insertText=(t,e)=>{const n=t.getCurrentContent(),i=t.getSelection();if(!i.isCollapsed())return t;const o=r.Modifier.insertText(n,i,e);return r.EditorState.push(t,o,"insert-characters")},e.removeSelectedText=t=>{const e=t.getCurrentContent(),n=t.getSelection(),i=r.Modifier.removeRange(e,n,"backward");return r.EditorState.push(t,i,"remove-range")},e.moveCaret=(t,e,n="")=>{const o=t.getCurrentContent(),s=t.getSelection();""===n&&(n=i(o,s).getKey());const a=r.SelectionState.createEmpty(n).merge({anchorOffset:e,focusOffset:e});return r.EditorState.acceptSelection(t,a)},e.removeEmojiCompletely=(t,e,n)=>{const i=t.getSelection(),o=t.getCurrentContent(),s=i.getStartOffset(),a=o.getBlockForKey(i.getStartKey()),u=e[e.length-1].length,c="backspace"===n?s-u:s+u,f=new r.SelectionState({anchorOffset:c,anchorKey:a.getKey(),focusOffset:s,focusKey:a.getKey(),isBackward:"delete"===n,hasFocus:i.getHasFocus()});return r.EditorState.push(t,r.Modifier.replaceText(o,f,""),"remove-range")}},45608:(t,e,n)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.getEntityAtPosition=o,e.getEntityRange=i,e.selectReplacementVariables=function(t,e){const n=t.getSelection(),u=e.getSelection(),c=t.getCurrentContent();if(n===u)return t;const f=function(t,e,n){const r=t.getStartOffset(),u=t.getStartKey(),c=t.getEndOffset(),f=t.getEndKey(),{startOffsetProperty:l,endOffsetProperty:h}=s(t.getIsBackward()),p=o(n,u,r);if(null!==p){const r=i(n,u,p),{start:o,end:s}=r;t=a(e,r)?t.merge({[l]:s}):t.merge({[l]:o})}const d=o(n,f,c);if(null!==d){const r=i(n,u,d),{start:o,end:s}=r;t=a(e,r)?t.merge({[h]:o}):t.merge({[h]:s})}return t}(n,u,c);return f!==n&&(t=r.EditorState.forceSelection(t,f)),t};var r=n(7206);function i(t,e,n){const r=t.getBlockForKey(e);let i=null;return r.findEntityRanges((t=>t.getEntity()===n),((t,e)=>{i={start:t,end:e}})),i}function o(t,e,n){const r=t.getBlockForKey(e).getEntityAt(n),o=i(t,e,r);return null===o||o.start===n?null:r}const s=function(t){let e="anchorOffset",n="focusOffset";return t&&(e="focusOffset",n="anchorOffset"),{startOffsetProperty:e,endOffsetProperty:n}};function a(t,e){const{start:n,end:r}=e;return t.getStartOffset()<=n&&t.getEndOffset()>=r}},31689:(t,e,n)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.addLabel=v,e.addPositionInformation=_,e.createEntityInContent=g,e.findReplacementVariables=d,e.getReplacementVariableLabel=p,e.getSelectedText=b,e.moveSelectionAfterReplacement=y,e.replaceByPosition=f,e.replaceReplacementVariables=w,e.replaceVariableWithEntity=m,e.serializeBlock=h,e.serializeEditor=function(t,e=" "){return t.getBlockMap().map((e=>h(e,(e=>t.getEntity(e))))).join(e)},e.serializeSelection=function(t,e,n=" "){const r=e.getStartKey(),i=e.getEndKey(),o=t.getBlockMap();let s=!1;return o.skipUntil((function(t){return t.getKey()===r})).takeUntil((function(t){const e=s;return t.getKey()===i&&(s=!0),e})).map((function(n){const o=n.getKey(),s={};return o===r&&(s.start=e.getStartOffset()),o===i&&(s.end=e.getEndOffset()),h(n,(e=>t.getEntity(e)),s)})).join(n)},e.serializeVariable=c,e.unserializeEditor=function(t,e){return w(r.EditorState.createWithContent(r.ContentState.createFromText(t)),e)};var r=n(7206),i=n(23695);const o="%%",s=/%%([A-Za-z0-9_]+)%%/g,a="%mention",u="IMMUTABLE";function c(t){return o+t+o}function f(t,e=[]){return[...e].reverse().forEach((e=>{const{start:n,end:r,replacementText:i}=e,o=t.slice(0,n),s=t.slice(r,t.length);t=o+i+s})),t}function l(t,e,n){return t>=e&&t<=n}function h(t,e,{start:n=0,end:r=t.getText().length}={}){const i=t.getText().slice(n,r),o=[];return t.findEntityRanges((t=>!!t.getEntity()),((i,s)=>{if(l(i,n,r)&&l(s,n,r)){const r=e(t.getEntityAt(i));r.data.mention&&o.push({start:i-n,end:s-n,replacementText:c(r.data.mention.replaceName)})}})),f(i,o)}function p(t,e){let n=e;return t.forEach((t=>{t.name===e&&t.label&&(n=t.label)})),n}function d(t){const e=[];let n;for(;n=s.exec(t);){const[t,r]=n;e.push({name:r,start:n.index,length:t.length})}return e}function v(t,e){return{...t,label:p(e,t.name)}}function _(t){return{...t,start:t.start,end:t.start+t.length,delta:t.label.length-t.length}}function y(t,e,n){const{start:r,end:i,delta:o}=n;if(t.hasEdgeWithin(e,r,i)){const e=i+o;t=t.merge({anchorOffset:e,focusOffset:e})}else t.focusOffset>i&&(t=t.merge({anchorOffset:t.anchorOffset+o,focusOffset:t.focusOffset+o}));return t}function g(t,e){const n={mention:{replaceName:e.name}};return t.createEntity(a,u,n)}function m(t,e,n){let i=t.getCurrentContent();const o=r.SelectionState.createEmpty(n).merge({anchorOffset:e.start,focusOffset:e.end});i=g(i,e);const s=r.Modifier.replaceText(i,o,e.label,null,i.getLastCreatedEntityKey());return r.EditorState.push(t,s,"apply-entity")}function b(t,e){const n=e.getAnchorKey(),r=t.getCurrentContent().getBlockForKey(n),i=e.getStartOffset(),o=e.getEndOffset();return r.getText().slice(i,o)}function w(t,e){const n=t.getCurrentContent().getBlockMap();let o=t;return n.forEach((t=>{const{text:n,key:s}=t;[...d(n)].reverse().forEach((t=>{t=_(t=v(t,e));let n=o.getSelection();n=y(n,s,t);const a=function(t,e,n,o){const s=t.getCurrentContent(),a=b(t,r.SelectionState.createEmpty(n).merge({anchorOffset:o.end,focusOffset:o.end+1}));if(!(0,i.getWordBoundaries)().includes(a)){const i=r.SelectionState.createEmpty(n).merge({anchorOffset:o.end,focusOffset:o.end}),a=r.Modifier.insertText(s,i," ");t=r.EditorState.push(t,a,"insert-characters"),e.getAnchorOffset()>=o.start&&(e=e.merge({anchorOffset:e.getAnchorOffset()+1,focusOffset:e.getFocusOffset()+1}))}return{editorState:t,selection:e}}(o,n,s,t);o=m(a.editorState,t,s),o=r.EditorState.acceptSelection(o,a.selection)}))})),o}},32183:(t,e,n)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.TriggerReplacementVariableSuggestionsButton=e.TitleInputContainer=e.StandardButton=e.FormSection=e.DescriptionInputContainer=e.ButtonsContainer=void 0;var r,i=(r=n(98487))&&r.__esModule?r:{default:r},o=n(23695),s=n(37188),a=n(81413);const u="#707070",c=(e.TitleInputContainer=(0,i.default)(a.VariableEditorInputContainer)`
	.public-DraftStyleDefault-block {
		line-height: 1.85714285; // 26px based on 14px font-size
	}

	.public-DraftEditorPlaceholder-root {
		color: ${u};
		line-height: 1.85714285; // 26px based on 14px font-size
	}

	.public-DraftEditorPlaceholder-hasFocus {
		color: ${u};
	}
`,e.DescriptionInputContainer=(0,i.default)(a.VariableEditorInputContainer)`
	min-height: 72px;
	padding: 4px 5px;
	line-height: 1.85714285; // 26px based on 14px font-size

	.public-DraftEditorPlaceholder-root {
		color: ${u};
		position: absolute;
		line-height: 1.85714285; // 26px based on 14px font-size
	}

	.public-DraftEditorPlaceholder-hasFocus {
		color: ${u};
		position: absolute;
	}
`,e.FormSection=i.default.div`
	display: flex;
	flex-wrap: wrap;
	align-items: center;
	margin: 16px 0 0 0;
`,e.StandardButton=(0,i.default)(a.Button)`
	color: #303030;
	box-sizing: border-box;
	border-radius: 4px;
	box-shadow: inset 0 -2px 0 0 rgba(0,0,0,0.1);
	font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif;
	padding: 4px;
	border: 1px solid #dbdbdb;
	font-size: 14px;
	font-weight: 400;
	line-height: 1.5;
	margin-bottom: 5px;
	max-width: 200px;
	padding: 0 0.5em;
`);e.TriggerReplacementVariableSuggestionsButton=(0,i.default)(c)`
	font-size: 13px;
	margin-bottom: 0; /* Override StandardButton margin instead of changing that. */
	& svg {
		${(0,o.getDirectionalStyle)("margin-right","margin-left")}: 7px;
		fill: ${s.colors.$color_grey_dark};
	}
`,e.ButtonsContainer=i.default.div`
	display: inline-flex;
	gap: 0.5em;
	margin-inline-start: auto;
	margin-bottom: 5px;
`},60940:(t,e,n)=>{"use strict";n.r(e),n.d(e,{default:()=>c});var r=n(66581),i=n(7206),o=/\n/g;function s(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:" ";return t.replace(o,e)}function a(t){return t.set("entity",null)}var u={stripEntities:!0};const c=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return t=Object.assign({},u,t),{blockRenderMap:(0,r.Map)({unstyled:{element:"div"}}),onChange:function(e){var n=e.getCurrentContent().getBlocksAsArray();if(n.length>1)e=function(t,e,n){e=e||t.getCurrentContent().getBlocksAsArray();var o=(0,r.List)(),u=(0,r.List)();e.forEach((function(t){"atomic"!==t.getType()&&(o=o.push(s(t.getText())),u=u.concat(t.getCharacterList()))})),n.stripEntities&&(u=u.map(a));var c=new i.ContentBlock({key:(0,i.genKey)(),text:o.join(""),type:"unstyled",characterList:u,depth:0}),f=i.ContentState.createFromBlockArray([c]);return t=i.EditorState.push(t,f,"remove-range"),i.EditorState.moveFocusToEnd(t)}(e,n,t);else{var u=n[0],c=u.getText(),f=u.getCharacterList(),l=t.stripEntities&&function(t){var e=!1;return t.forEach((function(t){null!==t.get("entity")&&(e=!0)})),e}(f);if(o.test(c)||l){c=s(c),t.stripEntities&&(f=f.map(a)),u=new i.ContentBlock({key:(0,i.genKey)(),text:c,type:"unstyled",characterList:f,depth:0});var h=i.ContentState.createFromBlockArray([u]);e=i.EditorState.push(e,h,"insert-characters")}}return e},handleReturn:function(t){return"handled"}}}},66581:function(t){t.exports=function(){"use strict";var t=Array.prototype.slice;function e(t,e){e&&(t.prototype=Object.create(e.prototype)),t.prototype.constructor=t}function n(t){return s(t)?t:J(t)}function r(t){return a(t)?t:$(t)}function i(t){return u(t)?t:Y(t)}function o(t){return s(t)&&!c(t)?t:X(t)}function s(t){return!(!t||!t[l])}function a(t){return!(!t||!t[h])}function u(t){return!(!t||!t[p])}function c(t){return a(t)||u(t)}function f(t){return!(!t||!t[d])}e(r,n),e(i,n),e(o,n),n.isIterable=s,n.isKeyed=a,n.isIndexed=u,n.isAssociative=c,n.isOrdered=f,n.Keyed=r,n.Indexed=i,n.Set=o;var l="@@__IMMUTABLE_ITERABLE__@@",h="@@__IMMUTABLE_KEYED__@@",p="@@__IMMUTABLE_INDEXED__@@",d="@@__IMMUTABLE_ORDERED__@@",v="delete",_=5,y=1<<_,g=y-1,m={},b={value:!1},w={value:!1};function S(t){return t.value=!1,t}function E(t){t&&(t.value=!0)}function O(){}function x(t,e){e=e||0;for(var n=Math.max(0,t.length-e),r=new Array(n),i=0;i<n;i++)r[i]=t[i+e];return r}function I(t){return void 0===t.size&&(t.size=t.__iterate(M)),t.size}function z(t,e){if("number"!=typeof e){var n=e>>>0;if(""+n!==e||4294967295===n)return NaN;e=n}return e<0?I(t)+e:e}function M(){return!0}function C(t,e,n){return(0===t||void 0!==n&&t<=-n)&&(void 0===e||void 0!==n&&e>=n)}function D(t,e){return R(t,e,0)}function k(t,e){return R(t,e,e)}function R(t,e,n){return void 0===t?n:t<0?Math.max(0,e+t):void 0===e?t:Math.min(e,t)}var A=0,q=1,j=2,P="function"==typeof Symbol&&Symbol.iterator,B="@@iterator",T=P||B;function F(t){this.next=t}function K(t,e,n,r){var i=0===t?e:1===t?n:[e,n];return r?r.value=i:r={value:i,done:!1},r}function V(){return{value:void 0,done:!0}}function L(t){return!!N(t)}function U(t){return t&&"function"==typeof t.next}function W(t){var e=N(t);return e&&e.call(t)}function N(t){var e=t&&(P&&t[P]||t[B]);if("function"==typeof e)return e}function H(t){return t&&"number"==typeof t.length}function J(t){return null==t?st():s(t)?t.toSeq():function(t){var e=ct(t)||"object"==typeof t&&new nt(t);if(!e)throw new TypeError("Expected Array or iterable object of values, or keyed object: "+t);return e}(t)}function $(t){return null==t?st().toKeyedSeq():s(t)?a(t)?t.toSeq():t.fromEntrySeq():at(t)}function Y(t){return null==t?st():s(t)?a(t)?t.entrySeq():t.toIndexedSeq():ut(t)}function X(t){return(null==t?st():s(t)?a(t)?t.entrySeq():t:ut(t)).toSetSeq()}F.prototype.toString=function(){return"[Iterator]"},F.KEYS=A,F.VALUES=q,F.ENTRIES=j,F.prototype.inspect=F.prototype.toSource=function(){return this.toString()},F.prototype[T]=function(){return this},e(J,n),J.of=function(){return J(arguments)},J.prototype.toSeq=function(){return this},J.prototype.toString=function(){return this.__toString("Seq {","}")},J.prototype.cacheResult=function(){return!this._cache&&this.__iterateUncached&&(this._cache=this.entrySeq().toArray(),this.size=this._cache.length),this},J.prototype.__iterate=function(t,e){return ft(this,t,e,!0)},J.prototype.__iterator=function(t,e){return lt(this,t,e,!0)},e($,J),$.prototype.toKeyedSeq=function(){return this},e(Y,J),Y.of=function(){return Y(arguments)},Y.prototype.toIndexedSeq=function(){return this},Y.prototype.toString=function(){return this.__toString("Seq [","]")},Y.prototype.__iterate=function(t,e){return ft(this,t,e,!1)},Y.prototype.__iterator=function(t,e){return lt(this,t,e,!1)},e(X,J),X.of=function(){return X(arguments)},X.prototype.toSetSeq=function(){return this},J.isSeq=ot,J.Keyed=$,J.Set=X,J.Indexed=Y;var G,Q,Z,tt="@@__IMMUTABLE_SEQ__@@";function et(t){this._array=t,this.size=t.length}function nt(t){var e=Object.keys(t);this._object=t,this._keys=e,this.size=e.length}function rt(t){this._iterable=t,this.size=t.length||t.size}function it(t){this._iterator=t,this._iteratorCache=[]}function ot(t){return!(!t||!t[tt])}function st(){return G||(G=new et([]))}function at(t){var e=Array.isArray(t)?new et(t).fromEntrySeq():U(t)?new it(t).fromEntrySeq():L(t)?new rt(t).fromEntrySeq():"object"==typeof t?new nt(t):void 0;if(!e)throw new TypeError("Expected Array or iterable object of [k, v] entries, or keyed object: "+t);return e}function ut(t){var e=ct(t);if(!e)throw new TypeError("Expected Array or iterable object of values: "+t);return e}function ct(t){return H(t)?new et(t):U(t)?new it(t):L(t)?new rt(t):void 0}function ft(t,e,n,r){var i=t._cache;if(i){for(var o=i.length-1,s=0;s<=o;s++){var a=i[n?o-s:s];if(!1===e(a[1],r?a[0]:s,t))return s+1}return s}return t.__iterateUncached(e,n)}function lt(t,e,n,r){var i=t._cache;if(i){var o=i.length-1,s=0;return new F((function(){var t=i[n?o-s:s];return s++>o?{value:void 0,done:!0}:K(e,r?t[0]:s-1,t[1])}))}return t.__iteratorUncached(e,n)}function ht(t,e){return e?pt(e,t,"",{"":t}):dt(t)}function pt(t,e,n,r){return Array.isArray(e)?t.call(r,n,Y(e).map((function(n,r){return pt(t,n,r,e)}))):vt(e)?t.call(r,n,$(e).map((function(n,r){return pt(t,n,r,e)}))):e}function dt(t){return Array.isArray(t)?Y(t).map(dt).toList():vt(t)?$(t).map(dt).toMap():t}function vt(t){return t&&(t.constructor===Object||void 0===t.constructor)}function _t(t,e){if(t===e||t!=t&&e!=e)return!0;if(!t||!e)return!1;if("function"==typeof t.valueOf&&"function"==typeof e.valueOf){if((t=t.valueOf())===(e=e.valueOf())||t!=t&&e!=e)return!0;if(!t||!e)return!1}return!("function"!=typeof t.equals||"function"!=typeof e.equals||!t.equals(e))}function yt(t,e){if(t===e)return!0;if(!s(e)||void 0!==t.size&&void 0!==e.size&&t.size!==e.size||void 0!==t.__hash&&void 0!==e.__hash&&t.__hash!==e.__hash||a(t)!==a(e)||u(t)!==u(e)||f(t)!==f(e))return!1;if(0===t.size&&0===e.size)return!0;var n=!c(t);if(f(t)){var r=t.entries();return e.every((function(t,e){var i=r.next().value;return i&&_t(i[1],t)&&(n||_t(i[0],e))}))&&r.next().done}var i=!1;if(void 0===t.size)if(void 0===e.size)"function"==typeof t.cacheResult&&t.cacheResult();else{i=!0;var o=t;t=e,e=o}var l=!0,h=e.__iterate((function(e,r){if(n?!t.has(e):i?!_t(e,t.get(r,m)):!_t(t.get(r,m),e))return l=!1,!1}));return l&&t.size===h}function gt(t,e){if(!(this instanceof gt))return new gt(t,e);if(this._value=t,this.size=void 0===e?1/0:Math.max(0,e),0===this.size){if(Q)return Q;Q=this}}function mt(t,e){if(!t)throw new Error(e)}function bt(t,e,n){if(!(this instanceof bt))return new bt(t,e,n);if(mt(0!==n,"Cannot step a Range by 0"),t=t||0,void 0===e&&(e=1/0),n=void 0===n?1:Math.abs(n),e<t&&(n=-n),this._start=t,this._end=e,this._step=n,this.size=Math.max(0,Math.ceil((e-t)/n-1)+1),0===this.size){if(Z)return Z;Z=this}}function wt(){throw TypeError("Abstract")}function St(){}function Et(){}function Ot(){}J.prototype[tt]=!0,e(et,Y),et.prototype.get=function(t,e){return this.has(t)?this._array[z(this,t)]:e},et.prototype.__iterate=function(t,e){for(var n=this._array,r=n.length-1,i=0;i<=r;i++)if(!1===t(n[e?r-i:i],i,this))return i+1;return i},et.prototype.__iterator=function(t,e){var n=this._array,r=n.length-1,i=0;return new F((function(){return i>r?{value:void 0,done:!0}:K(t,i,n[e?r-i++:i++])}))},e(nt,$),nt.prototype.get=function(t,e){return void 0===e||this.has(t)?this._object[t]:e},nt.prototype.has=function(t){return this._object.hasOwnProperty(t)},nt.prototype.__iterate=function(t,e){for(var n=this._object,r=this._keys,i=r.length-1,o=0;o<=i;o++){var s=r[e?i-o:o];if(!1===t(n[s],s,this))return o+1}return o},nt.prototype.__iterator=function(t,e){var n=this._object,r=this._keys,i=r.length-1,o=0;return new F((function(){var s=r[e?i-o:o];return o++>i?{value:void 0,done:!0}:K(t,s,n[s])}))},nt.prototype[d]=!0,e(rt,Y),rt.prototype.__iterateUncached=function(t,e){if(e)return this.cacheResult().__iterate(t,e);var n=W(this._iterable),r=0;if(U(n))for(var i;!(i=n.next()).done&&!1!==t(i.value,r++,this););return r},rt.prototype.__iteratorUncached=function(t,e){if(e)return this.cacheResult().__iterator(t,e);var n=W(this._iterable);if(!U(n))return new F(V);var r=0;return new F((function(){var e=n.next();return e.done?e:K(t,r++,e.value)}))},e(it,Y),it.prototype.__iterateUncached=function(t,e){if(e)return this.cacheResult().__iterate(t,e);for(var n,r=this._iterator,i=this._iteratorCache,o=0;o<i.length;)if(!1===t(i[o],o++,this))return o;for(;!(n=r.next()).done;){var s=n.value;if(i[o]=s,!1===t(s,o++,this))break}return o},it.prototype.__iteratorUncached=function(t,e){if(e)return this.cacheResult().__iterator(t,e);var n=this._iterator,r=this._iteratorCache,i=0;return new F((function(){if(i>=r.length){var e=n.next();if(e.done)return e;r[i]=e.value}return K(t,i,r[i++])}))},e(gt,Y),gt.prototype.toString=function(){return 0===this.size?"Repeat []":"Repeat [ "+this._value+" "+this.size+" times ]"},gt.prototype.get=function(t,e){return this.has(t)?this._value:e},gt.prototype.includes=function(t){return _t(this._value,t)},gt.prototype.slice=function(t,e){var n=this.size;return C(t,e,n)?this:new gt(this._value,k(e,n)-D(t,n))},gt.prototype.reverse=function(){return this},gt.prototype.indexOf=function(t){return _t(this._value,t)?0:-1},gt.prototype.lastIndexOf=function(t){return _t(this._value,t)?this.size:-1},gt.prototype.__iterate=function(t,e){for(var n=0;n<this.size;n++)if(!1===t(this._value,n,this))return n+1;return n},gt.prototype.__iterator=function(t,e){var n=this,r=0;return new F((function(){return r<n.size?K(t,r++,n._value):{value:void 0,done:!0}}))},gt.prototype.equals=function(t){return t instanceof gt?_t(this._value,t._value):yt(t)},e(bt,Y),bt.prototype.toString=function(){return 0===this.size?"Range []":"Range [ "+this._start+"..."+this._end+(1!==this._step?" by "+this._step:"")+" ]"},bt.prototype.get=function(t,e){return this.has(t)?this._start+z(this,t)*this._step:e},bt.prototype.includes=function(t){var e=(t-this._start)/this._step;return e>=0&&e<this.size&&e===Math.floor(e)},bt.prototype.slice=function(t,e){return C(t,e,this.size)?this:(t=D(t,this.size),(e=k(e,this.size))<=t?new bt(0,0):new bt(this.get(t,this._end),this.get(e,this._end),this._step))},bt.prototype.indexOf=function(t){var e=t-this._start;if(e%this._step==0){var n=e/this._step;if(n>=0&&n<this.size)return n}return-1},bt.prototype.lastIndexOf=function(t){return this.indexOf(t)},bt.prototype.__iterate=function(t,e){for(var n=this.size-1,r=this._step,i=e?this._start+n*r:this._start,o=0;o<=n;o++){if(!1===t(i,o,this))return o+1;i+=e?-r:r}return o},bt.prototype.__iterator=function(t,e){var n=this.size-1,r=this._step,i=e?this._start+n*r:this._start,o=0;return new F((function(){var s=i;return i+=e?-r:r,o>n?{value:void 0,done:!0}:K(t,o++,s)}))},bt.prototype.equals=function(t){return t instanceof bt?this._start===t._start&&this._end===t._end&&this._step===t._step:yt(this,t)},e(wt,n),e(St,wt),e(Et,wt),e(Ot,wt),wt.Keyed=St,wt.Indexed=Et,wt.Set=Ot;var xt="function"==typeof Math.imul&&-2===Math.imul(4294967295,2)?Math.imul:function(t,e){var n=65535&(t|=0),r=65535&(e|=0);return n*r+((t>>>16)*r+n*(e>>>16)<<16>>>0)|0};function It(t){return t>>>1&1073741824|3221225471&t}function zt(t){if(!1===t||null==t)return 0;if("function"==typeof t.valueOf&&(!1===(t=t.valueOf())||null==t))return 0;if(!0===t)return 1;var e=typeof t;if("number"===e){if(t!=t||t===1/0)return 0;var n=0|t;for(n!==t&&(n^=4294967295*t);t>4294967295;)n^=t/=4294967295;return It(n)}if("string"===e)return t.length>jt?function(t){var e=Tt[t];return void 0===e&&(e=Mt(t),Bt===Pt&&(Bt=0,Tt={}),Bt++,Tt[t]=e),e}(t):Mt(t);if("function"==typeof t.hashCode)return t.hashCode();if("object"===e)return function(t){var e;if(Rt&&void 0!==(e=kt.get(t)))return e;if(void 0!==(e=t[qt]))return e;if(!Dt){if(void 0!==(e=t.propertyIsEnumerable&&t.propertyIsEnumerable[qt]))return e;if(void 0!==(e=function(t){if(t&&t.nodeType>0)switch(t.nodeType){case 1:return t.uniqueID;case 9:return t.documentElement&&t.documentElement.uniqueID}}(t)))return e}if(e=++At,1073741824&At&&(At=0),Rt)kt.set(t,e);else{if(void 0!==Ct&&!1===Ct(t))throw new Error("Non-extensible objects are not allowed as keys.");if(Dt)Object.defineProperty(t,qt,{enumerable:!1,configurable:!1,writable:!1,value:e});else if(void 0!==t.propertyIsEnumerable&&t.propertyIsEnumerable===t.constructor.prototype.propertyIsEnumerable)t.propertyIsEnumerable=function(){return this.constructor.prototype.propertyIsEnumerable.apply(this,arguments)},t.propertyIsEnumerable[qt]=e;else{if(void 0===t.nodeType)throw new Error("Unable to set a non-enumerable property on object.");t[qt]=e}}return e}(t);if("function"==typeof t.toString)return Mt(t.toString());throw new Error("Value type "+e+" cannot be hashed.")}function Mt(t){for(var e=0,n=0;n<t.length;n++)e=31*e+t.charCodeAt(n)|0;return It(e)}var Ct=Object.isExtensible,Dt=function(){try{return Object.defineProperty({},"@",{}),!0}catch(t){return!1}}();var kt,Rt="function"==typeof WeakMap;Rt&&(kt=new WeakMap);var At=0,qt="__immutablehash__";"function"==typeof Symbol&&(qt=Symbol(qt));var jt=16,Pt=255,Bt=0,Tt={};function Ft(t){mt(t!==1/0,"Cannot perform this action with an infinite size.")}function Kt(t){return null==t?te():Vt(t)&&!f(t)?t:te().withMutations((function(e){var n=r(t);Ft(n.size),n.forEach((function(t,n){return e.set(n,t)}))}))}function Vt(t){return!(!t||!t[Ut])}e(Kt,St),Kt.of=function(){var e=t.call(arguments,0);return te().withMutations((function(t){for(var n=0;n<e.length;n+=2){if(n+1>=e.length)throw new Error("Missing value for key: "+e[n]);t.set(e[n],e[n+1])}}))},Kt.prototype.toString=function(){return this.__toString("Map {","}")},Kt.prototype.get=function(t,e){return this._root?this._root.get(0,void 0,t,e):e},Kt.prototype.set=function(t,e){return ee(this,t,e)},Kt.prototype.setIn=function(t,e){return this.updateIn(t,m,(function(){return e}))},Kt.prototype.remove=function(t){return ee(this,t,m)},Kt.prototype.deleteIn=function(t){return this.updateIn(t,(function(){return m}))},Kt.prototype.update=function(t,e,n){return 1===arguments.length?t(this):this.updateIn([t],e,n)},Kt.prototype.updateIn=function(t,e,n){n||(n=e,e=void 0);var r=ce(this,sn(t),e,n);return r===m?void 0:r},Kt.prototype.clear=function(){return 0===this.size?this:this.__ownerID?(this.size=0,this._root=null,this.__hash=void 0,this.__altered=!0,this):te()},Kt.prototype.merge=function(){return oe(this,void 0,arguments)},Kt.prototype.mergeWith=function(e){return oe(this,e,t.call(arguments,1))},Kt.prototype.mergeIn=function(e){var n=t.call(arguments,1);return this.updateIn(e,te(),(function(t){return"function"==typeof t.merge?t.merge.apply(t,n):n[n.length-1]}))},Kt.prototype.mergeDeep=function(){return oe(this,se,arguments)},Kt.prototype.mergeDeepWith=function(e){var n=t.call(arguments,1);return oe(this,ae(e),n)},Kt.prototype.mergeDeepIn=function(e){var n=t.call(arguments,1);return this.updateIn(e,te(),(function(t){return"function"==typeof t.mergeDeep?t.mergeDeep.apply(t,n):n[n.length-1]}))},Kt.prototype.sort=function(t){return Re($e(this,t))},Kt.prototype.sortBy=function(t,e){return Re($e(this,e,t))},Kt.prototype.withMutations=function(t){var e=this.asMutable();return t(e),e.wasAltered()?e.__ensureOwner(this.__ownerID):this},Kt.prototype.asMutable=function(){return this.__ownerID?this:this.__ensureOwner(new O)},Kt.prototype.asImmutable=function(){return this.__ensureOwner()},Kt.prototype.wasAltered=function(){return this.__altered},Kt.prototype.__iterator=function(t,e){return new Xt(this,t,e)},Kt.prototype.__iterate=function(t,e){var n=this,r=0;return this._root&&this._root.iterate((function(e){return r++,t(e[1],e[0],n)}),e),r},Kt.prototype.__ensureOwner=function(t){return t===this.__ownerID?this:t?Zt(this.size,this._root,t,this.__hash):(this.__ownerID=t,this.__altered=!1,this)},Kt.isMap=Vt;var Lt,Ut="@@__IMMUTABLE_MAP__@@",Wt=Kt.prototype;function Nt(t,e){this.ownerID=t,this.entries=e}function Ht(t,e,n){this.ownerID=t,this.bitmap=e,this.nodes=n}function Jt(t,e,n){this.ownerID=t,this.count=e,this.nodes=n}function $t(t,e,n){this.ownerID=t,this.keyHash=e,this.entries=n}function Yt(t,e,n){this.ownerID=t,this.keyHash=e,this.entry=n}function Xt(t,e,n){this._type=e,this._reverse=n,this._stack=t._root&&Qt(t._root)}function Gt(t,e){return K(t,e[0],e[1])}function Qt(t,e){return{node:t,index:0,__prev:e}}function Zt(t,e,n,r){var i=Object.create(Wt);return i.size=t,i._root=e,i.__ownerID=n,i.__hash=r,i.__altered=!1,i}function te(){return Lt||(Lt=Zt(0))}function ee(t,e,n){var r,i;if(t._root){var o=S(b),s=S(w);if(r=ne(t._root,t.__ownerID,0,void 0,e,n,o,s),!s.value)return t;i=t.size+(o.value?n===m?-1:1:0)}else{if(n===m)return t;i=1,r=new Nt(t.__ownerID,[[e,n]])}return t.__ownerID?(t.size=i,t._root=r,t.__hash=void 0,t.__altered=!0,t):r?Zt(i,r):te()}function ne(t,e,n,r,i,o,s,a){return t?t.update(e,n,r,i,o,s,a):o===m?t:(E(a),E(s),new Yt(e,r,[i,o]))}function re(t){return t.constructor===Yt||t.constructor===$t}function ie(t,e,n,r,i){if(t.keyHash===r)return new $t(e,r,[t.entry,i]);var o,s=(0===n?t.keyHash:t.keyHash>>>n)&g,a=(0===n?r:r>>>n)&g;return new Ht(e,1<<s|1<<a,s===a?[ie(t,e,n+_,r,i)]:(o=new Yt(e,r,i),s<a?[t,o]:[o,t]))}function oe(t,e,n){for(var i=[],o=0;o<n.length;o++){var a=n[o],u=r(a);s(a)||(u=u.map((function(t){return ht(t)}))),i.push(u)}return ue(t,e,i)}function se(t,e,n){return t&&t.mergeDeep&&s(e)?t.mergeDeep(e):_t(t,e)?t:e}function ae(t){return function(e,n,r){if(e&&e.mergeDeepWith&&s(n))return e.mergeDeepWith(t,n);var i=t(e,n,r);return _t(e,i)?e:i}}function ue(t,e,n){return 0===(n=n.filter((function(t){return 0!==t.size}))).length?t:0!==t.size||t.__ownerID||1!==n.length?t.withMutations((function(t){for(var r=e?function(n,r){t.update(r,m,(function(t){return t===m?n:e(t,n,r)}))}:function(e,n){t.set(n,e)},i=0;i<n.length;i++)n[i].forEach(r)})):t.constructor(n[0])}function ce(t,e,n,r){var i=t===m,o=e.next();if(o.done){var s=i?n:t,a=r(s);return a===s?t:a}mt(i||t&&t.set,"invalid keyPath");var u=o.value,c=i?m:t.get(u,m),f=ce(c,e,n,r);return f===c?t:f===m?t.remove(u):(i?te():t).set(u,f)}function fe(t){return t=(t=(858993459&(t-=t>>1&1431655765))+(t>>2&858993459))+(t>>4)&252645135,127&(t+=t>>8)+(t>>16)}function le(t,e,n,r){var i=r?t:x(t);return i[e]=n,i}Wt[Ut]=!0,Wt[v]=Wt.remove,Wt.removeIn=Wt.deleteIn,Nt.prototype.get=function(t,e,n,r){for(var i=this.entries,o=0,s=i.length;o<s;o++)if(_t(n,i[o][0]))return i[o][1];return r},Nt.prototype.update=function(t,e,n,r,i,o,s){for(var a=i===m,u=this.entries,c=0,f=u.length;c<f&&!_t(r,u[c][0]);c++);var l=c<f;if(l?u[c][1]===i:a)return this;if(E(s),(a||!l)&&E(o),!a||1!==u.length){if(!l&&!a&&u.length>=he)return function(t,e,n,r){t||(t=new O);for(var i=new Yt(t,zt(n),[n,r]),o=0;o<e.length;o++){var s=e[o];i=i.update(t,0,void 0,s[0],s[1])}return i}(t,u,r,i);var h=t&&t===this.ownerID,p=h?u:x(u);return l?a?c===f-1?p.pop():p[c]=p.pop():p[c]=[r,i]:p.push([r,i]),h?(this.entries=p,this):new Nt(t,p)}},Ht.prototype.get=function(t,e,n,r){void 0===e&&(e=zt(n));var i=1<<((0===t?e:e>>>t)&g),o=this.bitmap;return 0==(o&i)?r:this.nodes[fe(o&i-1)].get(t+_,e,n,r)},Ht.prototype.update=function(t,e,n,r,i,o,s){void 0===n&&(n=zt(r));var a=(0===e?n:n>>>e)&g,u=1<<a,c=this.bitmap,f=0!=(c&u);if(!f&&i===m)return this;var l=fe(c&u-1),h=this.nodes,p=f?h[l]:void 0,d=ne(p,t,e+_,n,r,i,o,s);if(d===p)return this;if(!f&&d&&h.length>=pe)return function(t,e,n,r,i){for(var o=0,s=new Array(y),a=0;0!==n;a++,n>>>=1)s[a]=1&n?e[o++]:void 0;return s[r]=i,new Jt(t,o+1,s)}(t,h,c,a,d);if(f&&!d&&2===h.length&&re(h[1^l]))return h[1^l];if(f&&d&&1===h.length&&re(d))return d;var v=t&&t===this.ownerID,b=f?d?c:c^u:c|u,w=f?d?le(h,l,d,v):function(t,e,n){var r=t.length-1;if(n&&e===r)return t.pop(),t;for(var i=new Array(r),o=0,s=0;s<r;s++)s===e&&(o=1),i[s]=t[s+o];return i}(h,l,v):function(t,e,n,r){var i=t.length+1;if(r&&e+1===i)return t[e]=n,t;for(var o=new Array(i),s=0,a=0;a<i;a++)a===e?(o[a]=n,s=-1):o[a]=t[a+s];return o}(h,l,d,v);return v?(this.bitmap=b,this.nodes=w,this):new Ht(t,b,w)},Jt.prototype.get=function(t,e,n,r){void 0===e&&(e=zt(n));var i=(0===t?e:e>>>t)&g,o=this.nodes[i];return o?o.get(t+_,e,n,r):r},Jt.prototype.update=function(t,e,n,r,i,o,s){void 0===n&&(n=zt(r));var a=(0===e?n:n>>>e)&g,u=i===m,c=this.nodes,f=c[a];if(u&&!f)return this;var l=ne(f,t,e+_,n,r,i,o,s);if(l===f)return this;var h=this.count;if(f){if(!l&&--h<de)return function(t,e,n,r){for(var i=0,o=0,s=new Array(n),a=0,u=1,c=e.length;a<c;a++,u<<=1){var f=e[a];void 0!==f&&a!==r&&(i|=u,s[o++]=f)}return new Ht(t,i,s)}(t,c,h,a)}else h++;var p=t&&t===this.ownerID,d=le(c,a,l,p);return p?(this.count=h,this.nodes=d,this):new Jt(t,h,d)},$t.prototype.get=function(t,e,n,r){for(var i=this.entries,o=0,s=i.length;o<s;o++)if(_t(n,i[o][0]))return i[o][1];return r},$t.prototype.update=function(t,e,n,r,i,o,s){void 0===n&&(n=zt(r));var a=i===m;if(n!==this.keyHash)return a?this:(E(s),E(o),ie(this,t,e,n,[r,i]));for(var u=this.entries,c=0,f=u.length;c<f&&!_t(r,u[c][0]);c++);var l=c<f;if(l?u[c][1]===i:a)return this;if(E(s),(a||!l)&&E(o),a&&2===f)return new Yt(t,this.keyHash,u[1^c]);var h=t&&t===this.ownerID,p=h?u:x(u);return l?a?c===f-1?p.pop():p[c]=p.pop():p[c]=[r,i]:p.push([r,i]),h?(this.entries=p,this):new $t(t,this.keyHash,p)},Yt.prototype.get=function(t,e,n,r){return _t(n,this.entry[0])?this.entry[1]:r},Yt.prototype.update=function(t,e,n,r,i,o,s){var a=i===m,u=_t(r,this.entry[0]);return(u?i===this.entry[1]:a)?this:(E(s),a?void E(o):u?t&&t===this.ownerID?(this.entry[1]=i,this):new Yt(t,this.keyHash,[r,i]):(E(o),ie(this,t,e,zt(r),[r,i])))},Nt.prototype.iterate=$t.prototype.iterate=function(t,e){for(var n=this.entries,r=0,i=n.length-1;r<=i;r++)if(!1===t(n[e?i-r:r]))return!1},Ht.prototype.iterate=Jt.prototype.iterate=function(t,e){for(var n=this.nodes,r=0,i=n.length-1;r<=i;r++){var o=n[e?i-r:r];if(o&&!1===o.iterate(t,e))return!1}},Yt.prototype.iterate=function(t,e){return t(this.entry)},e(Xt,F),Xt.prototype.next=function(){for(var t=this._type,e=this._stack;e;){var n,r=e.node,i=e.index++;if(r.entry){if(0===i)return Gt(t,r.entry)}else if(r.entries){if(i<=(n=r.entries.length-1))return Gt(t,r.entries[this._reverse?n-i:i])}else if(i<=(n=r.nodes.length-1)){var o=r.nodes[this._reverse?n-i:i];if(o){if(o.entry)return Gt(t,o.entry);e=this._stack=Qt(o,e)}continue}e=this._stack=this._stack.__prev}return{value:void 0,done:!0}};var he=y/4,pe=y/2,de=y/4;function ve(t){var e=xe();if(null==t)return e;if(_e(t))return t;var n=i(t),r=n.size;return 0===r?e:(Ft(r),r>0&&r<y?Oe(0,r,_,null,new me(n.toArray())):e.withMutations((function(t){t.setSize(r),n.forEach((function(e,n){return t.set(n,e)}))})))}function _e(t){return!(!t||!t[ye])}e(ve,Et),ve.of=function(){return this(arguments)},ve.prototype.toString=function(){return this.__toString("List [","]")},ve.prototype.get=function(t,e){if((t=z(this,t))>=0&&t<this.size){var n=Me(this,t+=this._origin);return n&&n.array[t&g]}return e},ve.prototype.set=function(t,e){return function(t,e,n){if((e=z(t,e))!=e)return t;if(e>=t.size||e<0)return t.withMutations((function(t){e<0?Ce(t,e).set(0,n):Ce(t,0,e+1).set(e,n)}));e+=t._origin;var r=t._tail,i=t._root,o=S(w);return e>=ke(t._capacity)?r=Ie(r,t.__ownerID,0,e,n,o):i=Ie(i,t.__ownerID,t._level,e,n,o),o.value?t.__ownerID?(t._root=i,t._tail=r,t.__hash=void 0,t.__altered=!0,t):Oe(t._origin,t._capacity,t._level,i,r):t}(this,t,e)},ve.prototype.remove=function(t){return this.has(t)?0===t?this.shift():t===this.size-1?this.pop():this.splice(t,1):this},ve.prototype.insert=function(t,e){return this.splice(t,0,e)},ve.prototype.clear=function(){return 0===this.size?this:this.__ownerID?(this.size=this._origin=this._capacity=0,this._level=_,this._root=this._tail=null,this.__hash=void 0,this.__altered=!0,this):xe()},ve.prototype.push=function(){var t=arguments,e=this.size;return this.withMutations((function(n){Ce(n,0,e+t.length);for(var r=0;r<t.length;r++)n.set(e+r,t[r])}))},ve.prototype.pop=function(){return Ce(this,0,-1)},ve.prototype.unshift=function(){var t=arguments;return this.withMutations((function(e){Ce(e,-t.length);for(var n=0;n<t.length;n++)e.set(n,t[n])}))},ve.prototype.shift=function(){return Ce(this,1)},ve.prototype.merge=function(){return De(this,void 0,arguments)},ve.prototype.mergeWith=function(e){return De(this,e,t.call(arguments,1))},ve.prototype.mergeDeep=function(){return De(this,se,arguments)},ve.prototype.mergeDeepWith=function(e){var n=t.call(arguments,1);return De(this,ae(e),n)},ve.prototype.setSize=function(t){return Ce(this,0,t)},ve.prototype.slice=function(t,e){var n=this.size;return C(t,e,n)?this:Ce(this,D(t,n),k(e,n))},ve.prototype.__iterator=function(t,e){var n=0,r=Ee(this,e);return new F((function(){var e=r();return e===Se?{value:void 0,done:!0}:K(t,n++,e)}))},ve.prototype.__iterate=function(t,e){for(var n,r=0,i=Ee(this,e);(n=i())!==Se&&!1!==t(n,r++,this););return r},ve.prototype.__ensureOwner=function(t){return t===this.__ownerID?this:t?Oe(this._origin,this._capacity,this._level,this._root,this._tail,t,this.__hash):(this.__ownerID=t,this)},ve.isList=_e;var ye="@@__IMMUTABLE_LIST__@@",ge=ve.prototype;function me(t,e){this.array=t,this.ownerID=e}ge[ye]=!0,ge[v]=ge.remove,ge.setIn=Wt.setIn,ge.deleteIn=ge.removeIn=Wt.removeIn,ge.update=Wt.update,ge.updateIn=Wt.updateIn,ge.mergeIn=Wt.mergeIn,ge.mergeDeepIn=Wt.mergeDeepIn,ge.withMutations=Wt.withMutations,ge.asMutable=Wt.asMutable,ge.asImmutable=Wt.asImmutable,ge.wasAltered=Wt.wasAltered,me.prototype.removeBefore=function(t,e,n){if(n===e?1<<e:0===this.array.length)return this;var r=n>>>e&g;if(r>=this.array.length)return new me([],t);var i,o=0===r;if(e>0){var s=this.array[r];if((i=s&&s.removeBefore(t,e-_,n))===s&&o)return this}if(o&&!i)return this;var a=ze(this,t);if(!o)for(var u=0;u<r;u++)a.array[u]=void 0;return i&&(a.array[r]=i),a},me.prototype.removeAfter=function(t,e,n){if(n===(e?1<<e:0)||0===this.array.length)return this;var r,i=n-1>>>e&g;if(i>=this.array.length)return this;if(e>0){var o=this.array[i];if((r=o&&o.removeAfter(t,e-_,n))===o&&i===this.array.length-1)return this}var s=ze(this,t);return s.array.splice(i+1),r&&(s.array[i]=r),s};var be,we,Se={};function Ee(t,e){var n=t._origin,r=t._capacity,i=ke(r),o=t._tail;return s(t._root,t._level,0);function s(t,a,u){return 0===a?function(t,s){var a=s===i?o&&o.array:t&&t.array,u=s>n?0:n-s,c=r-s;return c>y&&(c=y),function(){if(u===c)return Se;var t=e?--c:u++;return a&&a[t]}}(t,u):function(t,i,o){var a,u=t&&t.array,c=o>n?0:n-o>>i,f=1+(r-o>>i);return f>y&&(f=y),function(){for(;;){if(a){var t=a();if(t!==Se)return t;a=null}if(c===f)return Se;var n=e?--f:c++;a=s(u&&u[n],i-_,o+(n<<i))}}}(t,a,u)}}function Oe(t,e,n,r,i,o,s){var a=Object.create(ge);return a.size=e-t,a._origin=t,a._capacity=e,a._level=n,a._root=r,a._tail=i,a.__ownerID=o,a.__hash=s,a.__altered=!1,a}function xe(){return be||(be=Oe(0,0,_))}function Ie(t,e,n,r,i,o){var s,a=r>>>n&g,u=t&&a<t.array.length;if(!u&&void 0===i)return t;if(n>0){var c=t&&t.array[a],f=Ie(c,e,n-_,r,i,o);return f===c?t:((s=ze(t,e)).array[a]=f,s)}return u&&t.array[a]===i?t:(E(o),s=ze(t,e),void 0===i&&a===s.array.length-1?s.array.pop():s.array[a]=i,s)}function ze(t,e){return e&&t&&e===t.ownerID?t:new me(t?t.array.slice():[],e)}function Me(t,e){if(e>=ke(t._capacity))return t._tail;if(e<1<<t._level+_){for(var n=t._root,r=t._level;n&&r>0;)n=n.array[e>>>r&g],r-=_;return n}}function Ce(t,e,n){void 0!==e&&(e|=0),void 0!==n&&(n|=0);var r=t.__ownerID||new O,i=t._origin,o=t._capacity,s=i+e,a=void 0===n?o:n<0?o+n:i+n;if(s===i&&a===o)return t;if(s>=a)return t.clear();for(var u=t._level,c=t._root,f=0;s+f<0;)c=new me(c&&c.array.length?[void 0,c]:[],r),f+=1<<(u+=_);f&&(s+=f,i+=f,a+=f,o+=f);for(var l=ke(o),h=ke(a);h>=1<<u+_;)c=new me(c&&c.array.length?[c]:[],r),u+=_;var p=t._tail,d=h<l?Me(t,a-1):h>l?new me([],r):p;if(p&&h>l&&s<o&&p.array.length){for(var v=c=ze(c,r),y=u;y>_;y-=_){var m=l>>>y&g;v=v.array[m]=ze(v.array[m],r)}v.array[l>>>_&g]=p}if(a<o&&(d=d&&d.removeAfter(r,0,a)),s>=h)s-=h,a-=h,u=_,c=null,d=d&&d.removeBefore(r,0,s);else if(s>i||h<l){for(f=0;c;){var b=s>>>u&g;if(b!==h>>>u&g)break;b&&(f+=(1<<u)*b),u-=_,c=c.array[b]}c&&s>i&&(c=c.removeBefore(r,u,s-f)),c&&h<l&&(c=c.removeAfter(r,u,h-f)),f&&(s-=f,a-=f)}return t.__ownerID?(t.size=a-s,t._origin=s,t._capacity=a,t._level=u,t._root=c,t._tail=d,t.__hash=void 0,t.__altered=!0,t):Oe(s,a,u,c,d)}function De(t,e,n){for(var r=[],o=0,a=0;a<n.length;a++){var u=n[a],c=i(u);c.size>o&&(o=c.size),s(u)||(c=c.map((function(t){return ht(t)}))),r.push(c)}return o>t.size&&(t=t.setSize(o)),ue(t,e,r)}function ke(t){return t<y?0:t-1>>>_<<_}function Re(t){return null==t?je():Ae(t)?t:je().withMutations((function(e){var n=r(t);Ft(n.size),n.forEach((function(t,n){return e.set(n,t)}))}))}function Ae(t){return Vt(t)&&f(t)}function qe(t,e,n,r){var i=Object.create(Re.prototype);return i.size=t?t.size:0,i._map=t,i._list=e,i.__ownerID=n,i.__hash=r,i}function je(){return we||(we=qe(te(),xe()))}function Pe(t,e,n){var r,i,o=t._map,s=t._list,a=o.get(e),u=void 0!==a;if(n===m){if(!u)return t;s.size>=y&&s.size>=2*o.size?(r=(i=s.filter((function(t,e){return void 0!==t&&a!==e}))).toKeyedSeq().map((function(t){return t[0]})).flip().toMap(),t.__ownerID&&(r.__ownerID=i.__ownerID=t.__ownerID)):(r=o.remove(e),i=a===s.size-1?s.pop():s.set(a,void 0))}else if(u){if(n===s.get(a)[1])return t;r=o,i=s.set(a,[e,n])}else r=o.set(e,s.size),i=s.set(s.size,[e,n]);return t.__ownerID?(t.size=r.size,t._map=r,t._list=i,t.__hash=void 0,t):qe(r,i)}function Be(t,e){this._iter=t,this._useKeys=e,this.size=t.size}function Te(t){this._iter=t,this.size=t.size}function Fe(t){this._iter=t,this.size=t.size}function Ke(t){this._iter=t,this.size=t.size}function Ve(t){var e=nn(t);return e._iter=t,e.size=t.size,e.flip=function(){return t},e.reverse=function(){var e=t.reverse.apply(this);return e.flip=function(){return t.reverse()},e},e.has=function(e){return t.includes(e)},e.includes=function(e){return t.has(e)},e.cacheResult=rn,e.__iterateUncached=function(e,n){var r=this;return t.__iterate((function(t,n){return!1!==e(n,t,r)}),n)},e.__iteratorUncached=function(e,n){if(e===j){var r=t.__iterator(e,n);return new F((function(){var t=r.next();if(!t.done){var e=t.value[0];t.value[0]=t.value[1],t.value[1]=e}return t}))}return t.__iterator(e===q?A:q,n)},e}function Le(t,e,n){var r=nn(t);return r.size=t.size,r.has=function(e){return t.has(e)},r.get=function(r,i){var o=t.get(r,m);return o===m?i:e.call(n,o,r,t)},r.__iterateUncached=function(r,i){var o=this;return t.__iterate((function(t,i,s){return!1!==r(e.call(n,t,i,s),i,o)}),i)},r.__iteratorUncached=function(r,i){var o=t.__iterator(j,i);return new F((function(){var i=o.next();if(i.done)return i;var s=i.value,a=s[0];return K(r,a,e.call(n,s[1],a,t),i)}))},r}function Ue(t,e){var n=nn(t);return n._iter=t,n.size=t.size,n.reverse=function(){return t},t.flip&&(n.flip=function(){var e=Ve(t);return e.reverse=function(){return t.flip()},e}),n.get=function(n,r){return t.get(e?n:-1-n,r)},n.has=function(n){return t.has(e?n:-1-n)},n.includes=function(e){return t.includes(e)},n.cacheResult=rn,n.__iterate=function(e,n){var r=this;return t.__iterate((function(t,n){return e(t,n,r)}),!n)},n.__iterator=function(e,n){return t.__iterator(e,!n)},n}function We(t,e,n,r){var i=nn(t);return r&&(i.has=function(r){var i=t.get(r,m);return i!==m&&!!e.call(n,i,r,t)},i.get=function(r,i){var o=t.get(r,m);return o!==m&&e.call(n,o,r,t)?o:i}),i.__iterateUncached=function(i,o){var s=this,a=0;return t.__iterate((function(t,o,u){if(e.call(n,t,o,u))return a++,i(t,r?o:a-1,s)}),o),a},i.__iteratorUncached=function(i,o){var s=t.__iterator(j,o),a=0;return new F((function(){for(;;){var o=s.next();if(o.done)return o;var u=o.value,c=u[0],f=u[1];if(e.call(n,f,c,t))return K(i,r?c:a++,f,o)}}))},i}function Ne(t,e,n,r){var i=t.size;if(void 0!==e&&(e|=0),void 0!==n&&(n===1/0?n=i:n|=0),C(e,n,i))return t;var o=D(e,i),s=k(n,i);if(o!=o||s!=s)return Ne(t.toSeq().cacheResult(),e,n,r);var a,u=s-o;u==u&&(a=u<0?0:u);var c=nn(t);return c.size=0===a?a:t.size&&a||void 0,!r&&ot(t)&&a>=0&&(c.get=function(e,n){return(e=z(this,e))>=0&&e<a?t.get(e+o,n):n}),c.__iterateUncached=function(e,n){var i=this;if(0===a)return 0;if(n)return this.cacheResult().__iterate(e,n);var s=0,u=!0,c=0;return t.__iterate((function(t,n){if(!u||!(u=s++<o))return c++,!1!==e(t,r?n:c-1,i)&&c!==a})),c},c.__iteratorUncached=function(e,n){if(0!==a&&n)return this.cacheResult().__iterator(e,n);var i=0!==a&&t.__iterator(e,n),s=0,u=0;return new F((function(){for(;s++<o;)i.next();if(++u>a)return{value:void 0,done:!0};var t=i.next();return r||e===q?t:K(e,u-1,e===A?void 0:t.value[1],t)}))},c}function He(t,e,n,r){var i=nn(t);return i.__iterateUncached=function(i,o){var s=this;if(o)return this.cacheResult().__iterate(i,o);var a=!0,u=0;return t.__iterate((function(t,o,c){if(!a||!(a=e.call(n,t,o,c)))return u++,i(t,r?o:u-1,s)})),u},i.__iteratorUncached=function(i,o){var s=this;if(o)return this.cacheResult().__iterator(i,o);var a=t.__iterator(j,o),u=!0,c=0;return new F((function(){var t,o,f;do{if((t=a.next()).done)return r||i===q?t:K(i,c++,i===A?void 0:t.value[1],t);var l=t.value;o=l[0],f=l[1],u&&(u=e.call(n,f,o,s))}while(u);return i===j?t:K(i,o,f,t)}))},i}function Je(t,e,n){var r=nn(t);return r.__iterateUncached=function(r,i){var o=0,a=!1;return function t(u,c){var f=this;u.__iterate((function(i,u){return(!e||c<e)&&s(i)?t(i,c+1):!1===r(i,n?u:o++,f)&&(a=!0),!a}),i)}(t,0),o},r.__iteratorUncached=function(r,i){var o=t.__iterator(r,i),a=[],u=0;return new F((function(){for(;o;){var t=o.next();if(!1===t.done){var c=t.value;if(r===j&&(c=c[1]),e&&!(a.length<e)||!s(c))return n?t:K(r,u++,c,t);a.push(o),o=c.__iterator(r,i)}else o=a.pop()}return{value:void 0,done:!0}}))},r}function $e(t,e,n){e||(e=on);var r=a(t),i=0,o=t.toSeq().map((function(e,r){return[r,e,i++,n?n(e,r,t):e]})).toArray();return o.sort((function(t,n){return e(t[3],n[3])||t[2]-n[2]})).forEach(r?function(t,e){o[e].length=2}:function(t,e){o[e]=t[1]}),r?$(o):u(t)?Y(o):X(o)}function Ye(t,e,n){if(e||(e=on),n){var r=t.toSeq().map((function(e,r){return[e,n(e,r,t)]})).reduce((function(t,n){return Xe(e,t[1],n[1])?n:t}));return r&&r[0]}return t.reduce((function(t,n){return Xe(e,t,n)?n:t}))}function Xe(t,e,n){var r=t(n,e);return 0===r&&n!==e&&(null==n||n!=n)||r>0}function Ge(t,e,r){var i=nn(t);return i.size=new et(r).map((function(t){return t.size})).min(),i.__iterate=function(t,e){for(var n,r=this.__iterator(q,e),i=0;!(n=r.next()).done&&!1!==t(n.value,i++,this););return i},i.__iteratorUncached=function(t,i){var o=r.map((function(t){return t=n(t),W(i?t.reverse():t)})),s=0,a=!1;return new F((function(){var n;return a||(n=o.map((function(t){return t.next()})),a=n.some((function(t){return t.done}))),a?{value:void 0,done:!0}:K(t,s++,e.apply(null,n.map((function(t){return t.value}))))}))},i}function Qe(t,e){return ot(t)?e:t.constructor(e)}function Ze(t){if(t!==Object(t))throw new TypeError("Expected [K, V] tuple: "+t)}function tn(t){return Ft(t.size),I(t)}function en(t){return a(t)?r:u(t)?i:o}function nn(t){return Object.create((a(t)?$:u(t)?Y:X).prototype)}function rn(){return this._iter.cacheResult?(this._iter.cacheResult(),this.size=this._iter.size,this):J.prototype.cacheResult.call(this)}function on(t,e){return t>e?1:t<e?-1:0}function sn(t){var e=W(t);if(!e){if(!H(t))throw new TypeError("Expected iterable or array-like: "+t);e=W(n(t))}return e}function an(t,e){var n,r=function(o){if(o instanceof r)return o;if(!(this instanceof r))return new r(o);if(!n){n=!0;var s=Object.keys(t);(function(t,e){try{e.forEach(ln.bind(void 0,t))}catch(t){}})(i,s),i.size=s.length,i._name=e,i._keys=s,i._defaultValues=t}this._map=Kt(o)},i=r.prototype=Object.create(un);return i.constructor=r,r}e(Re,Kt),Re.of=function(){return this(arguments)},Re.prototype.toString=function(){return this.__toString("OrderedMap {","}")},Re.prototype.get=function(t,e){var n=this._map.get(t);return void 0!==n?this._list.get(n)[1]:e},Re.prototype.clear=function(){return 0===this.size?this:this.__ownerID?(this.size=0,this._map.clear(),this._list.clear(),this):je()},Re.prototype.set=function(t,e){return Pe(this,t,e)},Re.prototype.remove=function(t){return Pe(this,t,m)},Re.prototype.wasAltered=function(){return this._map.wasAltered()||this._list.wasAltered()},Re.prototype.__iterate=function(t,e){var n=this;return this._list.__iterate((function(e){return e&&t(e[1],e[0],n)}),e)},Re.prototype.__iterator=function(t,e){return this._list.fromEntrySeq().__iterator(t,e)},Re.prototype.__ensureOwner=function(t){if(t===this.__ownerID)return this;var e=this._map.__ensureOwner(t),n=this._list.__ensureOwner(t);return t?qe(e,n,t,this.__hash):(this.__ownerID=t,this._map=e,this._list=n,this)},Re.isOrderedMap=Ae,Re.prototype[d]=!0,Re.prototype[v]=Re.prototype.remove,e(Be,$),Be.prototype.get=function(t,e){return this._iter.get(t,e)},Be.prototype.has=function(t){return this._iter.has(t)},Be.prototype.valueSeq=function(){return this._iter.valueSeq()},Be.prototype.reverse=function(){var t=this,e=Ue(this,!0);return this._useKeys||(e.valueSeq=function(){return t._iter.toSeq().reverse()}),e},Be.prototype.map=function(t,e){var n=this,r=Le(this,t,e);return this._useKeys||(r.valueSeq=function(){return n._iter.toSeq().map(t,e)}),r},Be.prototype.__iterate=function(t,e){var n,r=this;return this._iter.__iterate(this._useKeys?function(e,n){return t(e,n,r)}:(n=e?tn(this):0,function(i){return t(i,e?--n:n++,r)}),e)},Be.prototype.__iterator=function(t,e){if(this._useKeys)return this._iter.__iterator(t,e);var n=this._iter.__iterator(q,e),r=e?tn(this):0;return new F((function(){var i=n.next();return i.done?i:K(t,e?--r:r++,i.value,i)}))},Be.prototype[d]=!0,e(Te,Y),Te.prototype.includes=function(t){return this._iter.includes(t)},Te.prototype.__iterate=function(t,e){var n=this,r=0;return this._iter.__iterate((function(e){return t(e,r++,n)}),e)},Te.prototype.__iterator=function(t,e){var n=this._iter.__iterator(q,e),r=0;return new F((function(){var e=n.next();return e.done?e:K(t,r++,e.value,e)}))},e(Fe,X),Fe.prototype.has=function(t){return this._iter.includes(t)},Fe.prototype.__iterate=function(t,e){var n=this;return this._iter.__iterate((function(e){return t(e,e,n)}),e)},Fe.prototype.__iterator=function(t,e){var n=this._iter.__iterator(q,e);return new F((function(){var e=n.next();return e.done?e:K(t,e.value,e.value,e)}))},e(Ke,$),Ke.prototype.entrySeq=function(){return this._iter.toSeq()},Ke.prototype.__iterate=function(t,e){var n=this;return this._iter.__iterate((function(e){if(e){Ze(e);var r=s(e);return t(r?e.get(1):e[1],r?e.get(0):e[0],n)}}),e)},Ke.prototype.__iterator=function(t,e){var n=this._iter.__iterator(q,e);return new F((function(){for(;;){var e=n.next();if(e.done)return e;var r=e.value;if(r){Ze(r);var i=s(r);return K(t,i?r.get(0):r[0],i?r.get(1):r[1],e)}}}))},Te.prototype.cacheResult=Be.prototype.cacheResult=Fe.prototype.cacheResult=Ke.prototype.cacheResult=rn,e(an,St),an.prototype.toString=function(){return this.__toString(fn(this)+" {","}")},an.prototype.has=function(t){return this._defaultValues.hasOwnProperty(t)},an.prototype.get=function(t,e){if(!this.has(t))return e;var n=this._defaultValues[t];return this._map?this._map.get(t,n):n},an.prototype.clear=function(){if(this.__ownerID)return this._map&&this._map.clear(),this;var t=this.constructor;return t._empty||(t._empty=cn(this,te()))},an.prototype.set=function(t,e){if(!this.has(t))throw new Error('Cannot set unknown key "'+t+'" on '+fn(this));if(this._map&&!this._map.has(t)&&e===this._defaultValues[t])return this;var n=this._map&&this._map.set(t,e);return this.__ownerID||n===this._map?this:cn(this,n)},an.prototype.remove=function(t){if(!this.has(t))return this;var e=this._map&&this._map.remove(t);return this.__ownerID||e===this._map?this:cn(this,e)},an.prototype.wasAltered=function(){return this._map.wasAltered()},an.prototype.__iterator=function(t,e){var n=this;return r(this._defaultValues).map((function(t,e){return n.get(e)})).__iterator(t,e)},an.prototype.__iterate=function(t,e){var n=this;return r(this._defaultValues).map((function(t,e){return n.get(e)})).__iterate(t,e)},an.prototype.__ensureOwner=function(t){if(t===this.__ownerID)return this;var e=this._map&&this._map.__ensureOwner(t);return t?cn(this,e,t):(this.__ownerID=t,this._map=e,this)};var un=an.prototype;function cn(t,e,n){var r=Object.create(Object.getPrototypeOf(t));return r._map=e,r.__ownerID=n,r}function fn(t){return t._name||t.constructor.name||"Record"}function ln(t,e){Object.defineProperty(t,e,{get:function(){return this.get(e)},set:function(t){mt(this.__ownerID,"Cannot set on an immutable record."),this.set(e,t)}})}function hn(t){return null==t?bn():pn(t)&&!f(t)?t:bn().withMutations((function(e){var n=o(t);Ft(n.size),n.forEach((function(t){return e.add(t)}))}))}function pn(t){return!(!t||!t[vn])}un[v]=un.remove,un.deleteIn=un.removeIn=Wt.removeIn,un.merge=Wt.merge,un.mergeWith=Wt.mergeWith,un.mergeIn=Wt.mergeIn,un.mergeDeep=Wt.mergeDeep,un.mergeDeepWith=Wt.mergeDeepWith,un.mergeDeepIn=Wt.mergeDeepIn,un.setIn=Wt.setIn,un.update=Wt.update,un.updateIn=Wt.updateIn,un.withMutations=Wt.withMutations,un.asMutable=Wt.asMutable,un.asImmutable=Wt.asImmutable,e(hn,Ot),hn.of=function(){return this(arguments)},hn.fromKeys=function(t){return this(r(t).keySeq())},hn.prototype.toString=function(){return this.__toString("Set {","}")},hn.prototype.has=function(t){return this._map.has(t)},hn.prototype.add=function(t){return gn(this,this._map.set(t,!0))},hn.prototype.remove=function(t){return gn(this,this._map.remove(t))},hn.prototype.clear=function(){return gn(this,this._map.clear())},hn.prototype.union=function(){var e=t.call(arguments,0);return 0===(e=e.filter((function(t){return 0!==t.size}))).length?this:0!==this.size||this.__ownerID||1!==e.length?this.withMutations((function(t){for(var n=0;n<e.length;n++)o(e[n]).forEach((function(e){return t.add(e)}))})):this.constructor(e[0])},hn.prototype.intersect=function(){var e=t.call(arguments,0);if(0===e.length)return this;e=e.map((function(t){return o(t)}));var n=this;return this.withMutations((function(t){n.forEach((function(n){e.every((function(t){return t.includes(n)}))||t.remove(n)}))}))},hn.prototype.subtract=function(){var e=t.call(arguments,0);if(0===e.length)return this;e=e.map((function(t){return o(t)}));var n=this;return this.withMutations((function(t){n.forEach((function(n){e.some((function(t){return t.includes(n)}))&&t.remove(n)}))}))},hn.prototype.merge=function(){return this.union.apply(this,arguments)},hn.prototype.mergeWith=function(e){var n=t.call(arguments,1);return this.union.apply(this,n)},hn.prototype.sort=function(t){return wn($e(this,t))},hn.prototype.sortBy=function(t,e){return wn($e(this,e,t))},hn.prototype.wasAltered=function(){return this._map.wasAltered()},hn.prototype.__iterate=function(t,e){var n=this;return this._map.__iterate((function(e,r){return t(r,r,n)}),e)},hn.prototype.__iterator=function(t,e){return this._map.map((function(t,e){return e})).__iterator(t,e)},hn.prototype.__ensureOwner=function(t){if(t===this.__ownerID)return this;var e=this._map.__ensureOwner(t);return t?this.__make(e,t):(this.__ownerID=t,this._map=e,this)},hn.isSet=pn;var dn,vn="@@__IMMUTABLE_SET__@@",yn=hn.prototype;function gn(t,e){return t.__ownerID?(t.size=e.size,t._map=e,t):e===t._map?t:0===e.size?t.__empty():t.__make(e)}function mn(t,e){var n=Object.create(yn);return n.size=t?t.size:0,n._map=t,n.__ownerID=e,n}function bn(){return dn||(dn=mn(te()))}function wn(t){return null==t?In():Sn(t)?t:In().withMutations((function(e){var n=o(t);Ft(n.size),n.forEach((function(t){return e.add(t)}))}))}function Sn(t){return pn(t)&&f(t)}yn[vn]=!0,yn[v]=yn.remove,yn.mergeDeep=yn.merge,yn.mergeDeepWith=yn.mergeWith,yn.withMutations=Wt.withMutations,yn.asMutable=Wt.asMutable,yn.asImmutable=Wt.asImmutable,yn.__empty=bn,yn.__make=mn,e(wn,hn),wn.of=function(){return this(arguments)},wn.fromKeys=function(t){return this(r(t).keySeq())},wn.prototype.toString=function(){return this.__toString("OrderedSet {","}")},wn.isOrderedSet=Sn;var En,On=wn.prototype;function xn(t,e){var n=Object.create(On);return n.size=t?t.size:0,n._map=t,n.__ownerID=e,n}function In(){return En||(En=xn(je()))}function zn(t){return null==t?An():Mn(t)?t:An().unshiftAll(t)}function Mn(t){return!(!t||!t[Dn])}On[d]=!0,On.__empty=In,On.__make=xn,e(zn,Et),zn.of=function(){return this(arguments)},zn.prototype.toString=function(){return this.__toString("Stack [","]")},zn.prototype.get=function(t,e){var n=this._head;for(t=z(this,t);n&&t--;)n=n.next;return n?n.value:e},zn.prototype.peek=function(){return this._head&&this._head.value},zn.prototype.push=function(){if(0===arguments.length)return this;for(var t=this.size+arguments.length,e=this._head,n=arguments.length-1;n>=0;n--)e={value:arguments[n],next:e};return this.__ownerID?(this.size=t,this._head=e,this.__hash=void 0,this.__altered=!0,this):Rn(t,e)},zn.prototype.pushAll=function(t){if(0===(t=i(t)).size)return this;Ft(t.size);var e=this.size,n=this._head;return t.reverse().forEach((function(t){e++,n={value:t,next:n}})),this.__ownerID?(this.size=e,this._head=n,this.__hash=void 0,this.__altered=!0,this):Rn(e,n)},zn.prototype.pop=function(){return this.slice(1)},zn.prototype.unshift=function(){return this.push.apply(this,arguments)},zn.prototype.unshiftAll=function(t){return this.pushAll(t)},zn.prototype.shift=function(){return this.pop.apply(this,arguments)},zn.prototype.clear=function(){return 0===this.size?this:this.__ownerID?(this.size=0,this._head=void 0,this.__hash=void 0,this.__altered=!0,this):An()},zn.prototype.slice=function(t,e){if(C(t,e,this.size))return this;var n=D(t,this.size);if(k(e,this.size)!==this.size)return Et.prototype.slice.call(this,t,e);for(var r=this.size-n,i=this._head;n--;)i=i.next;return this.__ownerID?(this.size=r,this._head=i,this.__hash=void 0,this.__altered=!0,this):Rn(r,i)},zn.prototype.__ensureOwner=function(t){return t===this.__ownerID?this:t?Rn(this.size,this._head,t,this.__hash):(this.__ownerID=t,this.__altered=!1,this)},zn.prototype.__iterate=function(t,e){if(e)return this.reverse().__iterate(t);for(var n=0,r=this._head;r&&!1!==t(r.value,n++,this);)r=r.next;return n},zn.prototype.__iterator=function(t,e){if(e)return this.reverse().__iterator(t);var n=0,r=this._head;return new F((function(){if(r){var e=r.value;return r=r.next,K(t,n++,e)}return{value:void 0,done:!0}}))},zn.isStack=Mn;var Cn,Dn="@@__IMMUTABLE_STACK__@@",kn=zn.prototype;function Rn(t,e,n,r){var i=Object.create(kn);return i.size=t,i._head=e,i.__ownerID=n,i.__hash=r,i.__altered=!1,i}function An(){return Cn||(Cn=Rn(0))}function qn(t,e){var n=function(n){t.prototype[n]=e[n]};return Object.keys(e).forEach(n),Object.getOwnPropertySymbols&&Object.getOwnPropertySymbols(e).forEach(n),t}kn[Dn]=!0,kn.withMutations=Wt.withMutations,kn.asMutable=Wt.asMutable,kn.asImmutable=Wt.asImmutable,kn.wasAltered=Wt.wasAltered,n.Iterator=F,qn(n,{toArray:function(){Ft(this.size);var t=new Array(this.size||0);return this.valueSeq().__iterate((function(e,n){t[n]=e})),t},toIndexedSeq:function(){return new Te(this)},toJS:function(){return this.toSeq().map((function(t){return t&&"function"==typeof t.toJS?t.toJS():t})).__toJS()},toJSON:function(){return this.toSeq().map((function(t){return t&&"function"==typeof t.toJSON?t.toJSON():t})).__toJS()},toKeyedSeq:function(){return new Be(this,!0)},toMap:function(){return Kt(this.toKeyedSeq())},toObject:function(){Ft(this.size);var t={};return this.__iterate((function(e,n){t[n]=e})),t},toOrderedMap:function(){return Re(this.toKeyedSeq())},toOrderedSet:function(){return wn(a(this)?this.valueSeq():this)},toSet:function(){return hn(a(this)?this.valueSeq():this)},toSetSeq:function(){return new Fe(this)},toSeq:function(){return u(this)?this.toIndexedSeq():a(this)?this.toKeyedSeq():this.toSetSeq()},toStack:function(){return zn(a(this)?this.valueSeq():this)},toList:function(){return ve(a(this)?this.valueSeq():this)},toString:function(){return"[Iterable]"},__toString:function(t,e){return 0===this.size?t+e:t+" "+this.toSeq().map(this.__toStringMapper).join(", ")+" "+e},concat:function(){return Qe(this,function(t,e){var n=a(t),i=[t].concat(e).map((function(t){return s(t)?n&&(t=r(t)):t=n?at(t):ut(Array.isArray(t)?t:[t]),t})).filter((function(t){return 0!==t.size}));if(0===i.length)return t;if(1===i.length){var o=i[0];if(o===t||n&&a(o)||u(t)&&u(o))return o}var c=new et(i);return n?c=c.toKeyedSeq():u(t)||(c=c.toSetSeq()),(c=c.flatten(!0)).size=i.reduce((function(t,e){if(void 0!==t){var n=e.size;if(void 0!==n)return t+n}}),0),c}(this,t.call(arguments,0)))},includes:function(t){return this.some((function(e){return _t(e,t)}))},entries:function(){return this.__iterator(j)},every:function(t,e){Ft(this.size);var n=!0;return this.__iterate((function(r,i,o){if(!t.call(e,r,i,o))return n=!1,!1})),n},filter:function(t,e){return Qe(this,We(this,t,e,!0))},find:function(t,e,n){var r=this.findEntry(t,e);return r?r[1]:n},forEach:function(t,e){return Ft(this.size),this.__iterate(e?t.bind(e):t)},join:function(t){Ft(this.size),t=void 0!==t?""+t:",";var e="",n=!0;return this.__iterate((function(r){n?n=!1:e+=t,e+=null!=r?r.toString():""})),e},keys:function(){return this.__iterator(A)},map:function(t,e){return Qe(this,Le(this,t,e))},reduce:function(t,e,n){var r,i;return Ft(this.size),arguments.length<2?i=!0:r=e,this.__iterate((function(e,o,s){i?(i=!1,r=e):r=t.call(n,r,e,o,s)})),r},reduceRight:function(t,e,n){var r=this.toKeyedSeq().reverse();return r.reduce.apply(r,arguments)},reverse:function(){return Qe(this,Ue(this,!0))},slice:function(t,e){return Qe(this,Ne(this,t,e,!0))},some:function(t,e){return!this.every(Fn(t),e)},sort:function(t){return Qe(this,$e(this,t))},values:function(){return this.__iterator(q)},butLast:function(){return this.slice(0,-1)},isEmpty:function(){return void 0!==this.size?0===this.size:!this.some((function(){return!0}))},count:function(t,e){return I(t?this.toSeq().filter(t,e):this)},countBy:function(t,e){return function(t,e,n){var r=Kt().asMutable();return t.__iterate((function(i,o){r.update(e.call(n,i,o,t),0,(function(t){return t+1}))})),r.asImmutable()}(this,t,e)},equals:function(t){return yt(this,t)},entrySeq:function(){var t=this;if(t._cache)return new et(t._cache);var e=t.toSeq().map(Tn).toIndexedSeq();return e.fromEntrySeq=function(){return t.toSeq()},e},filterNot:function(t,e){return this.filter(Fn(t),e)},findEntry:function(t,e,n){var r=n;return this.__iterate((function(n,i,o){if(t.call(e,n,i,o))return r=[i,n],!1})),r},findKey:function(t,e){var n=this.findEntry(t,e);return n&&n[0]},findLast:function(t,e,n){return this.toKeyedSeq().reverse().find(t,e,n)},findLastEntry:function(t,e,n){return this.toKeyedSeq().reverse().findEntry(t,e,n)},findLastKey:function(t,e){return this.toKeyedSeq().reverse().findKey(t,e)},first:function(){return this.find(M)},flatMap:function(t,e){return Qe(this,function(t,e,n){var r=en(t);return t.toSeq().map((function(i,o){return r(e.call(n,i,o,t))})).flatten(!0)}(this,t,e))},flatten:function(t){return Qe(this,Je(this,t,!0))},fromEntrySeq:function(){return new Ke(this)},get:function(t,e){return this.find((function(e,n){return _t(n,t)}),void 0,e)},getIn:function(t,e){for(var n,r=this,i=sn(t);!(n=i.next()).done;){var o=n.value;if((r=r&&r.get?r.get(o,m):m)===m)return e}return r},groupBy:function(t,e){return function(t,e,n){var r=a(t),i=(f(t)?Re():Kt()).asMutable();t.__iterate((function(o,s){i.update(e.call(n,o,s,t),(function(t){return(t=t||[]).push(r?[s,o]:o),t}))}));var o=en(t);return i.map((function(e){return Qe(t,o(e))}))}(this,t,e)},has:function(t){return this.get(t,m)!==m},hasIn:function(t){return this.getIn(t,m)!==m},isSubset:function(t){return t="function"==typeof t.includes?t:n(t),this.every((function(e){return t.includes(e)}))},isSuperset:function(t){return(t="function"==typeof t.isSubset?t:n(t)).isSubset(this)},keyOf:function(t){return this.findKey((function(e){return _t(e,t)}))},keySeq:function(){return this.toSeq().map(Bn).toIndexedSeq()},last:function(){return this.toSeq().reverse().first()},lastKeyOf:function(t){return this.toKeyedSeq().reverse().keyOf(t)},max:function(t){return Ye(this,t)},maxBy:function(t,e){return Ye(this,e,t)},min:function(t){return Ye(this,t?Kn(t):Un)},minBy:function(t,e){return Ye(this,e?Kn(e):Un,t)},rest:function(){return this.slice(1)},skip:function(t){return this.slice(Math.max(0,t))},skipLast:function(t){return Qe(this,this.toSeq().reverse().skip(t).reverse())},skipWhile:function(t,e){return Qe(this,He(this,t,e,!0))},skipUntil:function(t,e){return this.skipWhile(Fn(t),e)},sortBy:function(t,e){return Qe(this,$e(this,e,t))},take:function(t){return this.slice(0,Math.max(0,t))},takeLast:function(t){return Qe(this,this.toSeq().reverse().take(t).reverse())},takeWhile:function(t,e){return Qe(this,function(t,e,n){var r=nn(t);return r.__iterateUncached=function(r,i){var o=this;if(i)return this.cacheResult().__iterate(r,i);var s=0;return t.__iterate((function(t,i,a){return e.call(n,t,i,a)&&++s&&r(t,i,o)})),s},r.__iteratorUncached=function(r,i){var o=this;if(i)return this.cacheResult().__iterator(r,i);var s=t.__iterator(j,i),a=!0;return new F((function(){if(!a)return{value:void 0,done:!0};var t=s.next();if(t.done)return t;var i=t.value,u=i[0],c=i[1];return e.call(n,c,u,o)?r===j?t:K(r,u,c,t):(a=!1,{value:void 0,done:!0})}))},r}(this,t,e))},takeUntil:function(t,e){return this.takeWhile(Fn(t),e)},valueSeq:function(){return this.toIndexedSeq()},hashCode:function(){return this.__hash||(this.__hash=function(t){if(t.size===1/0)return 0;var e=f(t),n=a(t),r=e?1:0;return function(t,e){return e=xt(e,3432918353),e=xt(e<<15|e>>>-15,461845907),e=xt(e<<13|e>>>-13,5),e=xt((e=(e+3864292196|0)^t)^e>>>16,2246822507),It((e=xt(e^e>>>13,3266489909))^e>>>16)}(t.__iterate(n?e?function(t,e){r=31*r+Wn(zt(t),zt(e))|0}:function(t,e){r=r+Wn(zt(t),zt(e))|0}:e?function(t){r=31*r+zt(t)|0}:function(t){r=r+zt(t)|0}),r)}(this))}});var jn=n.prototype;jn[l]=!0,jn[T]=jn.values,jn.__toJS=jn.toArray,jn.__toStringMapper=Vn,jn.inspect=jn.toSource=function(){return this.toString()},jn.chain=jn.flatMap,jn.contains=jn.includes,qn(r,{flip:function(){return Qe(this,Ve(this))},mapEntries:function(t,e){var n=this,r=0;return Qe(this,this.toSeq().map((function(i,o){return t.call(e,[o,i],r++,n)})).fromEntrySeq())},mapKeys:function(t,e){var n=this;return Qe(this,this.toSeq().flip().map((function(r,i){return t.call(e,r,i,n)})).flip())}});var Pn=r.prototype;function Bn(t,e){return e}function Tn(t,e){return[e,t]}function Fn(t){return function(){return!t.apply(this,arguments)}}function Kn(t){return function(){return-t.apply(this,arguments)}}function Vn(t){return"string"==typeof t?JSON.stringify(t):String(t)}function Ln(){return x(arguments)}function Un(t,e){return t<e?1:t>e?-1:0}function Wn(t,e){return t^e+2654435769+(t<<6)+(t>>2)|0}return Pn[h]=!0,Pn[T]=jn.entries,Pn.__toJS=jn.toObject,Pn.__toStringMapper=function(t,e){return JSON.stringify(e)+": "+Vn(t)},qn(i,{toKeyedSeq:function(){return new Be(this,!1)},filter:function(t,e){return Qe(this,We(this,t,e,!1))},findIndex:function(t,e){var n=this.findEntry(t,e);return n?n[0]:-1},indexOf:function(t){var e=this.keyOf(t);return void 0===e?-1:e},lastIndexOf:function(t){var e=this.lastKeyOf(t);return void 0===e?-1:e},reverse:function(){return Qe(this,Ue(this,!1))},slice:function(t,e){return Qe(this,Ne(this,t,e,!1))},splice:function(t,e){var n=arguments.length;if(e=Math.max(0|e,0),0===n||2===n&&!e)return this;t=D(t,t<0?this.count():this.size);var r=this.slice(0,t);return Qe(this,1===n?r:r.concat(x(arguments,2),this.slice(t+e)))},findLastIndex:function(t,e){var n=this.findLastEntry(t,e);return n?n[0]:-1},first:function(){return this.get(0)},flatten:function(t){return Qe(this,Je(this,t,!1))},get:function(t,e){return(t=z(this,t))<0||this.size===1/0||void 0!==this.size&&t>this.size?e:this.find((function(e,n){return n===t}),void 0,e)},has:function(t){return(t=z(this,t))>=0&&(void 0!==this.size?this.size===1/0||t<this.size:-1!==this.indexOf(t))},interpose:function(t){return Qe(this,function(t,e){var n=nn(t);return n.size=t.size&&2*t.size-1,n.__iterateUncached=function(n,r){var i=this,o=0;return t.__iterate((function(t,r){return(!o||!1!==n(e,o++,i))&&!1!==n(t,o++,i)}),r),o},n.__iteratorUncached=function(n,r){var i,o=t.__iterator(q,r),s=0;return new F((function(){return(!i||s%2)&&(i=o.next()).done?i:s%2?K(n,s++,e):K(n,s++,i.value,i)}))},n}(this,t))},interleave:function(){var t=[this].concat(x(arguments)),e=Ge(this.toSeq(),Y.of,t),n=e.flatten(!0);return e.size&&(n.size=e.size*t.length),Qe(this,n)},keySeq:function(){return bt(0,this.size)},last:function(){return this.get(-1)},skipWhile:function(t,e){return Qe(this,He(this,t,e,!1))},zip:function(){return Qe(this,Ge(this,Ln,[this].concat(x(arguments))))},zipWith:function(t){var e=x(arguments);return e[0]=this,Qe(this,Ge(this,t,e))}}),i.prototype[p]=!0,i.prototype[d]=!0,qn(o,{get:function(t,e){return this.has(t)?t:e},includes:function(t){return this.has(t)},keySeq:function(){return this.valueSeq()}}),o.prototype.has=jn.includes,o.prototype.contains=o.prototype.includes,qn($,r.prototype),qn(Y,i.prototype),qn(X,o.prototype),qn(St,r.prototype),qn(Et,i.prototype),qn(Ot,o.prototype),{Iterable:n,Seq:J,Collection:wt,Map:Kt,OrderedMap:Re,List:ve,Stack:zn,Set:hn,OrderedSet:wn,Record:an,Range:bt,Repeat:gt,is:_t,fromJS:ht}}()},43393:function(t){t.exports=function(){"use strict";var t=Array.prototype.slice;function e(t,e){e&&(t.prototype=Object.create(e.prototype)),t.prototype.constructor=t}function n(t){return s(t)?t:J(t)}function r(t){return a(t)?t:$(t)}function i(t){return u(t)?t:Y(t)}function o(t){return s(t)&&!c(t)?t:X(t)}function s(t){return!(!t||!t[l])}function a(t){return!(!t||!t[h])}function u(t){return!(!t||!t[p])}function c(t){return a(t)||u(t)}function f(t){return!(!t||!t[d])}e(r,n),e(i,n),e(o,n),n.isIterable=s,n.isKeyed=a,n.isIndexed=u,n.isAssociative=c,n.isOrdered=f,n.Keyed=r,n.Indexed=i,n.Set=o;var l="@@__IMMUTABLE_ITERABLE__@@",h="@@__IMMUTABLE_KEYED__@@",p="@@__IMMUTABLE_INDEXED__@@",d="@@__IMMUTABLE_ORDERED__@@",v="delete",_=5,y=1<<_,g=y-1,m={},b={value:!1},w={value:!1};function S(t){return t.value=!1,t}function E(t){t&&(t.value=!0)}function O(){}function x(t,e){e=e||0;for(var n=Math.max(0,t.length-e),r=new Array(n),i=0;i<n;i++)r[i]=t[i+e];return r}function I(t){return void 0===t.size&&(t.size=t.__iterate(M)),t.size}function z(t,e){if("number"!=typeof e){var n=e>>>0;if(""+n!==e||4294967295===n)return NaN;e=n}return e<0?I(t)+e:e}function M(){return!0}function C(t,e,n){return(0===t||void 0!==n&&t<=-n)&&(void 0===e||void 0!==n&&e>=n)}function D(t,e){return R(t,e,0)}function k(t,e){return R(t,e,e)}function R(t,e,n){return void 0===t?n:t<0?Math.max(0,e+t):void 0===e?t:Math.min(e,t)}var A=0,q=1,j=2,P="function"==typeof Symbol&&Symbol.iterator,B="@@iterator",T=P||B;function F(t){this.next=t}function K(t,e,n,r){var i=0===t?e:1===t?n:[e,n];return r?r.value=i:r={value:i,done:!1},r}function V(){return{value:void 0,done:!0}}function L(t){return!!N(t)}function U(t){return t&&"function"==typeof t.next}function W(t){var e=N(t);return e&&e.call(t)}function N(t){var e=t&&(P&&t[P]||t[B]);if("function"==typeof e)return e}function H(t){return t&&"number"==typeof t.length}function J(t){return null==t?st():s(t)?t.toSeq():function(t){var e=ct(t)||"object"==typeof t&&new nt(t);if(!e)throw new TypeError("Expected Array or iterable object of values, or keyed object: "+t);return e}(t)}function $(t){return null==t?st().toKeyedSeq():s(t)?a(t)?t.toSeq():t.fromEntrySeq():at(t)}function Y(t){return null==t?st():s(t)?a(t)?t.entrySeq():t.toIndexedSeq():ut(t)}function X(t){return(null==t?st():s(t)?a(t)?t.entrySeq():t:ut(t)).toSetSeq()}F.prototype.toString=function(){return"[Iterator]"},F.KEYS=A,F.VALUES=q,F.ENTRIES=j,F.prototype.inspect=F.prototype.toSource=function(){return this.toString()},F.prototype[T]=function(){return this},e(J,n),J.of=function(){return J(arguments)},J.prototype.toSeq=function(){return this},J.prototype.toString=function(){return this.__toString("Seq {","}")},J.prototype.cacheResult=function(){return!this._cache&&this.__iterateUncached&&(this._cache=this.entrySeq().toArray(),this.size=this._cache.length),this},J.prototype.__iterate=function(t,e){return ft(this,t,e,!0)},J.prototype.__iterator=function(t,e){return lt(this,t,e,!0)},e($,J),$.prototype.toKeyedSeq=function(){return this},e(Y,J),Y.of=function(){return Y(arguments)},Y.prototype.toIndexedSeq=function(){return this},Y.prototype.toString=function(){return this.__toString("Seq [","]")},Y.prototype.__iterate=function(t,e){return ft(this,t,e,!1)},Y.prototype.__iterator=function(t,e){return lt(this,t,e,!1)},e(X,J),X.of=function(){return X(arguments)},X.prototype.toSetSeq=function(){return this},J.isSeq=ot,J.Keyed=$,J.Set=X,J.Indexed=Y;var G,Q,Z,tt="@@__IMMUTABLE_SEQ__@@";function et(t){this._array=t,this.size=t.length}function nt(t){var e=Object.keys(t);this._object=t,this._keys=e,this.size=e.length}function rt(t){this._iterable=t,this.size=t.length||t.size}function it(t){this._iterator=t,this._iteratorCache=[]}function ot(t){return!(!t||!t[tt])}function st(){return G||(G=new et([]))}function at(t){var e=Array.isArray(t)?new et(t).fromEntrySeq():U(t)?new it(t).fromEntrySeq():L(t)?new rt(t).fromEntrySeq():"object"==typeof t?new nt(t):void 0;if(!e)throw new TypeError("Expected Array or iterable object of [k, v] entries, or keyed object: "+t);return e}function ut(t){var e=ct(t);if(!e)throw new TypeError("Expected Array or iterable object of values: "+t);return e}function ct(t){return H(t)?new et(t):U(t)?new it(t):L(t)?new rt(t):void 0}function ft(t,e,n,r){var i=t._cache;if(i){for(var o=i.length-1,s=0;s<=o;s++){var a=i[n?o-s:s];if(!1===e(a[1],r?a[0]:s,t))return s+1}return s}return t.__iterateUncached(e,n)}function lt(t,e,n,r){var i=t._cache;if(i){var o=i.length-1,s=0;return new F((function(){var t=i[n?o-s:s];return s++>o?{value:void 0,done:!0}:K(e,r?t[0]:s-1,t[1])}))}return t.__iteratorUncached(e,n)}function ht(t,e){return e?pt(e,t,"",{"":t}):dt(t)}function pt(t,e,n,r){return Array.isArray(e)?t.call(r,n,Y(e).map((function(n,r){return pt(t,n,r,e)}))):vt(e)?t.call(r,n,$(e).map((function(n,r){return pt(t,n,r,e)}))):e}function dt(t){return Array.isArray(t)?Y(t).map(dt).toList():vt(t)?$(t).map(dt).toMap():t}function vt(t){return t&&(t.constructor===Object||void 0===t.constructor)}function _t(t,e){if(t===e||t!=t&&e!=e)return!0;if(!t||!e)return!1;if("function"==typeof t.valueOf&&"function"==typeof e.valueOf){if((t=t.valueOf())===(e=e.valueOf())||t!=t&&e!=e)return!0;if(!t||!e)return!1}return!("function"!=typeof t.equals||"function"!=typeof e.equals||!t.equals(e))}function yt(t,e){if(t===e)return!0;if(!s(e)||void 0!==t.size&&void 0!==e.size&&t.size!==e.size||void 0!==t.__hash&&void 0!==e.__hash&&t.__hash!==e.__hash||a(t)!==a(e)||u(t)!==u(e)||f(t)!==f(e))return!1;if(0===t.size&&0===e.size)return!0;var n=!c(t);if(f(t)){var r=t.entries();return e.every((function(t,e){var i=r.next().value;return i&&_t(i[1],t)&&(n||_t(i[0],e))}))&&r.next().done}var i=!1;if(void 0===t.size)if(void 0===e.size)"function"==typeof t.cacheResult&&t.cacheResult();else{i=!0;var o=t;t=e,e=o}var l=!0,h=e.__iterate((function(e,r){if(n?!t.has(e):i?!_t(e,t.get(r,m)):!_t(t.get(r,m),e))return l=!1,!1}));return l&&t.size===h}function gt(t,e){if(!(this instanceof gt))return new gt(t,e);if(this._value=t,this.size=void 0===e?1/0:Math.max(0,e),0===this.size){if(Q)return Q;Q=this}}function mt(t,e){if(!t)throw new Error(e)}function bt(t,e,n){if(!(this instanceof bt))return new bt(t,e,n);if(mt(0!==n,"Cannot step a Range by 0"),t=t||0,void 0===e&&(e=1/0),n=void 0===n?1:Math.abs(n),e<t&&(n=-n),this._start=t,this._end=e,this._step=n,this.size=Math.max(0,Math.ceil((e-t)/n-1)+1),0===this.size){if(Z)return Z;Z=this}}function wt(){throw TypeError("Abstract")}function St(){}function Et(){}function Ot(){}J.prototype[tt]=!0,e(et,Y),et.prototype.get=function(t,e){return this.has(t)?this._array[z(this,t)]:e},et.prototype.__iterate=function(t,e){for(var n=this._array,r=n.length-1,i=0;i<=r;i++)if(!1===t(n[e?r-i:i],i,this))return i+1;return i},et.prototype.__iterator=function(t,e){var n=this._array,r=n.length-1,i=0;return new F((function(){return i>r?{value:void 0,done:!0}:K(t,i,n[e?r-i++:i++])}))},e(nt,$),nt.prototype.get=function(t,e){return void 0===e||this.has(t)?this._object[t]:e},nt.prototype.has=function(t){return this._object.hasOwnProperty(t)},nt.prototype.__iterate=function(t,e){for(var n=this._object,r=this._keys,i=r.length-1,o=0;o<=i;o++){var s=r[e?i-o:o];if(!1===t(n[s],s,this))return o+1}return o},nt.prototype.__iterator=function(t,e){var n=this._object,r=this._keys,i=r.length-1,o=0;return new F((function(){var s=r[e?i-o:o];return o++>i?{value:void 0,done:!0}:K(t,s,n[s])}))},nt.prototype[d]=!0,e(rt,Y),rt.prototype.__iterateUncached=function(t,e){if(e)return this.cacheResult().__iterate(t,e);var n=W(this._iterable),r=0;if(U(n))for(var i;!(i=n.next()).done&&!1!==t(i.value,r++,this););return r},rt.prototype.__iteratorUncached=function(t,e){if(e)return this.cacheResult().__iterator(t,e);var n=W(this._iterable);if(!U(n))return new F(V);var r=0;return new F((function(){var e=n.next();return e.done?e:K(t,r++,e.value)}))},e(it,Y),it.prototype.__iterateUncached=function(t,e){if(e)return this.cacheResult().__iterate(t,e);for(var n,r=this._iterator,i=this._iteratorCache,o=0;o<i.length;)if(!1===t(i[o],o++,this))return o;for(;!(n=r.next()).done;){var s=n.value;if(i[o]=s,!1===t(s,o++,this))break}return o},it.prototype.__iteratorUncached=function(t,e){if(e)return this.cacheResult().__iterator(t,e);var n=this._iterator,r=this._iteratorCache,i=0;return new F((function(){if(i>=r.length){var e=n.next();if(e.done)return e;r[i]=e.value}return K(t,i,r[i++])}))},e(gt,Y),gt.prototype.toString=function(){return 0===this.size?"Repeat []":"Repeat [ "+this._value+" "+this.size+" times ]"},gt.prototype.get=function(t,e){return this.has(t)?this._value:e},gt.prototype.includes=function(t){return _t(this._value,t)},gt.prototype.slice=function(t,e){var n=this.size;return C(t,e,n)?this:new gt(this._value,k(e,n)-D(t,n))},gt.prototype.reverse=function(){return this},gt.prototype.indexOf=function(t){return _t(this._value,t)?0:-1},gt.prototype.lastIndexOf=function(t){return _t(this._value,t)?this.size:-1},gt.prototype.__iterate=function(t,e){for(var n=0;n<this.size;n++)if(!1===t(this._value,n,this))return n+1;return n},gt.prototype.__iterator=function(t,e){var n=this,r=0;return new F((function(){return r<n.size?K(t,r++,n._value):{value:void 0,done:!0}}))},gt.prototype.equals=function(t){return t instanceof gt?_t(this._value,t._value):yt(t)},e(bt,Y),bt.prototype.toString=function(){return 0===this.size?"Range []":"Range [ "+this._start+"..."+this._end+(this._step>1?" by "+this._step:"")+" ]"},bt.prototype.get=function(t,e){return this.has(t)?this._start+z(this,t)*this._step:e},bt.prototype.includes=function(t){var e=(t-this._start)/this._step;return e>=0&&e<this.size&&e===Math.floor(e)},bt.prototype.slice=function(t,e){return C(t,e,this.size)?this:(t=D(t,this.size),(e=k(e,this.size))<=t?new bt(0,0):new bt(this.get(t,this._end),this.get(e,this._end),this._step))},bt.prototype.indexOf=function(t){var e=t-this._start;if(e%this._step==0){var n=e/this._step;if(n>=0&&n<this.size)return n}return-1},bt.prototype.lastIndexOf=function(t){return this.indexOf(t)},bt.prototype.__iterate=function(t,e){for(var n=this.size-1,r=this._step,i=e?this._start+n*r:this._start,o=0;o<=n;o++){if(!1===t(i,o,this))return o+1;i+=e?-r:r}return o},bt.prototype.__iterator=function(t,e){var n=this.size-1,r=this._step,i=e?this._start+n*r:this._start,o=0;return new F((function(){var s=i;return i+=e?-r:r,o>n?{value:void 0,done:!0}:K(t,o++,s)}))},bt.prototype.equals=function(t){return t instanceof bt?this._start===t._start&&this._end===t._end&&this._step===t._step:yt(this,t)},e(wt,n),e(St,wt),e(Et,wt),e(Ot,wt),wt.Keyed=St,wt.Indexed=Et,wt.Set=Ot;var xt="function"==typeof Math.imul&&-2===Math.imul(4294967295,2)?Math.imul:function(t,e){var n=65535&(t|=0),r=65535&(e|=0);return n*r+((t>>>16)*r+n*(e>>>16)<<16>>>0)|0};function It(t){return t>>>1&1073741824|3221225471&t}function zt(t){if(!1===t||null==t)return 0;if("function"==typeof t.valueOf&&(!1===(t=t.valueOf())||null==t))return 0;if(!0===t)return 1;var e=typeof t;if("number"===e){var n=0|t;for(n!==t&&(n^=4294967295*t);t>4294967295;)n^=t/=4294967295;return It(n)}if("string"===e)return t.length>jt?function(t){var e=Tt[t];return void 0===e&&(e=Mt(t),Bt===Pt&&(Bt=0,Tt={}),Bt++,Tt[t]=e),e}(t):Mt(t);if("function"==typeof t.hashCode)return t.hashCode();if("object"===e)return function(t){var e;if(Rt&&void 0!==(e=kt.get(t)))return e;if(void 0!==(e=t[qt]))return e;if(!Dt){if(void 0!==(e=t.propertyIsEnumerable&&t.propertyIsEnumerable[qt]))return e;if(void 0!==(e=function(t){if(t&&t.nodeType>0)switch(t.nodeType){case 1:return t.uniqueID;case 9:return t.documentElement&&t.documentElement.uniqueID}}(t)))return e}if(e=++At,1073741824&At&&(At=0),Rt)kt.set(t,e);else{if(void 0!==Ct&&!1===Ct(t))throw new Error("Non-extensible objects are not allowed as keys.");if(Dt)Object.defineProperty(t,qt,{enumerable:!1,configurable:!1,writable:!1,value:e});else if(void 0!==t.propertyIsEnumerable&&t.propertyIsEnumerable===t.constructor.prototype.propertyIsEnumerable)t.propertyIsEnumerable=function(){return this.constructor.prototype.propertyIsEnumerable.apply(this,arguments)},t.propertyIsEnumerable[qt]=e;else{if(void 0===t.nodeType)throw new Error("Unable to set a non-enumerable property on object.");t[qt]=e}}return e}(t);if("function"==typeof t.toString)return Mt(t.toString());throw new Error("Value type "+e+" cannot be hashed.")}function Mt(t){for(var e=0,n=0;n<t.length;n++)e=31*e+t.charCodeAt(n)|0;return It(e)}var Ct=Object.isExtensible,Dt=function(){try{return Object.defineProperty({},"@",{}),!0}catch(t){return!1}}();var kt,Rt="function"==typeof WeakMap;Rt&&(kt=new WeakMap);var At=0,qt="__immutablehash__";"function"==typeof Symbol&&(qt=Symbol(qt));var jt=16,Pt=255,Bt=0,Tt={};function Ft(t){mt(t!==1/0,"Cannot perform this action with an infinite size.")}function Kt(t){return null==t?te():Vt(t)&&!f(t)?t:te().withMutations((function(e){var n=r(t);Ft(n.size),n.forEach((function(t,n){return e.set(n,t)}))}))}function Vt(t){return!(!t||!t[Ut])}e(Kt,St),Kt.prototype.toString=function(){return this.__toString("Map {","}")},Kt.prototype.get=function(t,e){return this._root?this._root.get(0,void 0,t,e):e},Kt.prototype.set=function(t,e){return ee(this,t,e)},Kt.prototype.setIn=function(t,e){return this.updateIn(t,m,(function(){return e}))},Kt.prototype.remove=function(t){return ee(this,t,m)},Kt.prototype.deleteIn=function(t){return this.updateIn(t,(function(){return m}))},Kt.prototype.update=function(t,e,n){return 1===arguments.length?t(this):this.updateIn([t],e,n)},Kt.prototype.updateIn=function(t,e,n){n||(n=e,e=void 0);var r=ce(this,sn(t),e,n);return r===m?void 0:r},Kt.prototype.clear=function(){return 0===this.size?this:this.__ownerID?(this.size=0,this._root=null,this.__hash=void 0,this.__altered=!0,this):te()},Kt.prototype.merge=function(){return oe(this,void 0,arguments)},Kt.prototype.mergeWith=function(e){return oe(this,e,t.call(arguments,1))},Kt.prototype.mergeIn=function(e){var n=t.call(arguments,1);return this.updateIn(e,te(),(function(t){return"function"==typeof t.merge?t.merge.apply(t,n):n[n.length-1]}))},Kt.prototype.mergeDeep=function(){return oe(this,se,arguments)},Kt.prototype.mergeDeepWith=function(e){var n=t.call(arguments,1);return oe(this,ae(e),n)},Kt.prototype.mergeDeepIn=function(e){var n=t.call(arguments,1);return this.updateIn(e,te(),(function(t){return"function"==typeof t.mergeDeep?t.mergeDeep.apply(t,n):n[n.length-1]}))},Kt.prototype.sort=function(t){return Re($e(this,t))},Kt.prototype.sortBy=function(t,e){return Re($e(this,e,t))},Kt.prototype.withMutations=function(t){var e=this.asMutable();return t(e),e.wasAltered()?e.__ensureOwner(this.__ownerID):this},Kt.prototype.asMutable=function(){return this.__ownerID?this:this.__ensureOwner(new O)},Kt.prototype.asImmutable=function(){return this.__ensureOwner()},Kt.prototype.wasAltered=function(){return this.__altered},Kt.prototype.__iterator=function(t,e){return new Xt(this,t,e)},Kt.prototype.__iterate=function(t,e){var n=this,r=0;return this._root&&this._root.iterate((function(e){return r++,t(e[1],e[0],n)}),e),r},Kt.prototype.__ensureOwner=function(t){return t===this.__ownerID?this:t?Zt(this.size,this._root,t,this.__hash):(this.__ownerID=t,this.__altered=!1,this)},Kt.isMap=Vt;var Lt,Ut="@@__IMMUTABLE_MAP__@@",Wt=Kt.prototype;function Nt(t,e){this.ownerID=t,this.entries=e}function Ht(t,e,n){this.ownerID=t,this.bitmap=e,this.nodes=n}function Jt(t,e,n){this.ownerID=t,this.count=e,this.nodes=n}function $t(t,e,n){this.ownerID=t,this.keyHash=e,this.entries=n}function Yt(t,e,n){this.ownerID=t,this.keyHash=e,this.entry=n}function Xt(t,e,n){this._type=e,this._reverse=n,this._stack=t._root&&Qt(t._root)}function Gt(t,e){return K(t,e[0],e[1])}function Qt(t,e){return{node:t,index:0,__prev:e}}function Zt(t,e,n,r){var i=Object.create(Wt);return i.size=t,i._root=e,i.__ownerID=n,i.__hash=r,i.__altered=!1,i}function te(){return Lt||(Lt=Zt(0))}function ee(t,e,n){var r,i;if(t._root){var o=S(b),s=S(w);if(r=ne(t._root,t.__ownerID,0,void 0,e,n,o,s),!s.value)return t;i=t.size+(o.value?n===m?-1:1:0)}else{if(n===m)return t;i=1,r=new Nt(t.__ownerID,[[e,n]])}return t.__ownerID?(t.size=i,t._root=r,t.__hash=void 0,t.__altered=!0,t):r?Zt(i,r):te()}function ne(t,e,n,r,i,o,s,a){return t?t.update(e,n,r,i,o,s,a):o===m?t:(E(a),E(s),new Yt(e,r,[i,o]))}function re(t){return t.constructor===Yt||t.constructor===$t}function ie(t,e,n,r,i){if(t.keyHash===r)return new $t(e,r,[t.entry,i]);var o,s=(0===n?t.keyHash:t.keyHash>>>n)&g,a=(0===n?r:r>>>n)&g;return new Ht(e,1<<s|1<<a,s===a?[ie(t,e,n+_,r,i)]:(o=new Yt(e,r,i),s<a?[t,o]:[o,t]))}function oe(t,e,n){for(var i=[],o=0;o<n.length;o++){var a=n[o],u=r(a);s(a)||(u=u.map((function(t){return ht(t)}))),i.push(u)}return ue(t,e,i)}function se(t,e,n){return t&&t.mergeDeep&&s(e)?t.mergeDeep(e):_t(t,e)?t:e}function ae(t){return function(e,n,r){if(e&&e.mergeDeepWith&&s(n))return e.mergeDeepWith(t,n);var i=t(e,n,r);return _t(e,i)?e:i}}function ue(t,e,n){return 0===(n=n.filter((function(t){return 0!==t.size}))).length?t:0!==t.size||t.__ownerID||1!==n.length?t.withMutations((function(t){for(var r=e?function(n,r){t.update(r,m,(function(t){return t===m?n:e(t,n,r)}))}:function(e,n){t.set(n,e)},i=0;i<n.length;i++)n[i].forEach(r)})):t.constructor(n[0])}function ce(t,e,n,r){var i=t===m,o=e.next();if(o.done){var s=i?n:t,a=r(s);return a===s?t:a}mt(i||t&&t.set,"invalid keyPath");var u=o.value,c=i?m:t.get(u,m),f=ce(c,e,n,r);return f===c?t:f===m?t.remove(u):(i?te():t).set(u,f)}function fe(t){return t=(t=(858993459&(t-=t>>1&1431655765))+(t>>2&858993459))+(t>>4)&252645135,127&(t+=t>>8)+(t>>16)}function le(t,e,n,r){var i=r?t:x(t);return i[e]=n,i}Wt[Ut]=!0,Wt[v]=Wt.remove,Wt.removeIn=Wt.deleteIn,Nt.prototype.get=function(t,e,n,r){for(var i=this.entries,o=0,s=i.length;o<s;o++)if(_t(n,i[o][0]))return i[o][1];return r},Nt.prototype.update=function(t,e,n,r,i,o,s){for(var a=i===m,u=this.entries,c=0,f=u.length;c<f&&!_t(r,u[c][0]);c++);var l=c<f;if(l?u[c][1]===i:a)return this;if(E(s),(a||!l)&&E(o),!a||1!==u.length){if(!l&&!a&&u.length>=he)return function(t,e,n,r){t||(t=new O);for(var i=new Yt(t,zt(n),[n,r]),o=0;o<e.length;o++){var s=e[o];i=i.update(t,0,void 0,s[0],s[1])}return i}(t,u,r,i);var h=t&&t===this.ownerID,p=h?u:x(u);return l?a?c===f-1?p.pop():p[c]=p.pop():p[c]=[r,i]:p.push([r,i]),h?(this.entries=p,this):new Nt(t,p)}},Ht.prototype.get=function(t,e,n,r){void 0===e&&(e=zt(n));var i=1<<((0===t?e:e>>>t)&g),o=this.bitmap;return 0==(o&i)?r:this.nodes[fe(o&i-1)].get(t+_,e,n,r)},Ht.prototype.update=function(t,e,n,r,i,o,s){void 0===n&&(n=zt(r));var a=(0===e?n:n>>>e)&g,u=1<<a,c=this.bitmap,f=0!=(c&u);if(!f&&i===m)return this;var l=fe(c&u-1),h=this.nodes,p=f?h[l]:void 0,d=ne(p,t,e+_,n,r,i,o,s);if(d===p)return this;if(!f&&d&&h.length>=pe)return function(t,e,n,r,i){for(var o=0,s=new Array(y),a=0;0!==n;a++,n>>>=1)s[a]=1&n?e[o++]:void 0;return s[r]=i,new Jt(t,o+1,s)}(t,h,c,a,d);if(f&&!d&&2===h.length&&re(h[1^l]))return h[1^l];if(f&&d&&1===h.length&&re(d))return d;var v=t&&t===this.ownerID,b=f?d?c:c^u:c|u,w=f?d?le(h,l,d,v):function(t,e,n){var r=t.length-1;if(n&&e===r)return t.pop(),t;for(var i=new Array(r),o=0,s=0;s<r;s++)s===e&&(o=1),i[s]=t[s+o];return i}(h,l,v):function(t,e,n,r){var i=t.length+1;if(r&&e+1===i)return t[e]=n,t;for(var o=new Array(i),s=0,a=0;a<i;a++)a===e?(o[a]=n,s=-1):o[a]=t[a+s];return o}(h,l,d,v);return v?(this.bitmap=b,this.nodes=w,this):new Ht(t,b,w)},Jt.prototype.get=function(t,e,n,r){void 0===e&&(e=zt(n));var i=(0===t?e:e>>>t)&g,o=this.nodes[i];return o?o.get(t+_,e,n,r):r},Jt.prototype.update=function(t,e,n,r,i,o,s){void 0===n&&(n=zt(r));var a=(0===e?n:n>>>e)&g,u=i===m,c=this.nodes,f=c[a];if(u&&!f)return this;var l=ne(f,t,e+_,n,r,i,o,s);if(l===f)return this;var h=this.count;if(f){if(!l&&--h<de)return function(t,e,n,r){for(var i=0,o=0,s=new Array(n),a=0,u=1,c=e.length;a<c;a++,u<<=1){var f=e[a];void 0!==f&&a!==r&&(i|=u,s[o++]=f)}return new Ht(t,i,s)}(t,c,h,a)}else h++;var p=t&&t===this.ownerID,d=le(c,a,l,p);return p?(this.count=h,this.nodes=d,this):new Jt(t,h,d)},$t.prototype.get=function(t,e,n,r){for(var i=this.entries,o=0,s=i.length;o<s;o++)if(_t(n,i[o][0]))return i[o][1];return r},$t.prototype.update=function(t,e,n,r,i,o,s){void 0===n&&(n=zt(r));var a=i===m;if(n!==this.keyHash)return a?this:(E(s),E(o),ie(this,t,e,n,[r,i]));for(var u=this.entries,c=0,f=u.length;c<f&&!_t(r,u[c][0]);c++);var l=c<f;if(l?u[c][1]===i:a)return this;if(E(s),(a||!l)&&E(o),a&&2===f)return new Yt(t,this.keyHash,u[1^c]);var h=t&&t===this.ownerID,p=h?u:x(u);return l?a?c===f-1?p.pop():p[c]=p.pop():p[c]=[r,i]:p.push([r,i]),h?(this.entries=p,this):new $t(t,this.keyHash,p)},Yt.prototype.get=function(t,e,n,r){return _t(n,this.entry[0])?this.entry[1]:r},Yt.prototype.update=function(t,e,n,r,i,o,s){var a=i===m,u=_t(r,this.entry[0]);return(u?i===this.entry[1]:a)?this:(E(s),a?void E(o):u?t&&t===this.ownerID?(this.entry[1]=i,this):new Yt(t,this.keyHash,[r,i]):(E(o),ie(this,t,e,zt(r),[r,i])))},Nt.prototype.iterate=$t.prototype.iterate=function(t,e){for(var n=this.entries,r=0,i=n.length-1;r<=i;r++)if(!1===t(n[e?i-r:r]))return!1},Ht.prototype.iterate=Jt.prototype.iterate=function(t,e){for(var n=this.nodes,r=0,i=n.length-1;r<=i;r++){var o=n[e?i-r:r];if(o&&!1===o.iterate(t,e))return!1}},Yt.prototype.iterate=function(t,e){return t(this.entry)},e(Xt,F),Xt.prototype.next=function(){for(var t=this._type,e=this._stack;e;){var n,r=e.node,i=e.index++;if(r.entry){if(0===i)return Gt(t,r.entry)}else if(r.entries){if(i<=(n=r.entries.length-1))return Gt(t,r.entries[this._reverse?n-i:i])}else if(i<=(n=r.nodes.length-1)){var o=r.nodes[this._reverse?n-i:i];if(o){if(o.entry)return Gt(t,o.entry);e=this._stack=Qt(o,e)}continue}e=this._stack=this._stack.__prev}return{value:void 0,done:!0}};var he=y/4,pe=y/2,de=y/4;function ve(t){var e=xe();if(null==t)return e;if(_e(t))return t;var n=i(t),r=n.size;return 0===r?e:(Ft(r),r>0&&r<y?Oe(0,r,_,null,new me(n.toArray())):e.withMutations((function(t){t.setSize(r),n.forEach((function(e,n){return t.set(n,e)}))})))}function _e(t){return!(!t||!t[ye])}e(ve,Et),ve.of=function(){return this(arguments)},ve.prototype.toString=function(){return this.__toString("List [","]")},ve.prototype.get=function(t,e){if((t=z(this,t))>=0&&t<this.size){var n=Me(this,t+=this._origin);return n&&n.array[t&g]}return e},ve.prototype.set=function(t,e){return function(t,e,n){if((e=z(t,e))!=e)return t;if(e>=t.size||e<0)return t.withMutations((function(t){e<0?Ce(t,e).set(0,n):Ce(t,0,e+1).set(e,n)}));e+=t._origin;var r=t._tail,i=t._root,o=S(w);return e>=ke(t._capacity)?r=Ie(r,t.__ownerID,0,e,n,o):i=Ie(i,t.__ownerID,t._level,e,n,o),o.value?t.__ownerID?(t._root=i,t._tail=r,t.__hash=void 0,t.__altered=!0,t):Oe(t._origin,t._capacity,t._level,i,r):t}(this,t,e)},ve.prototype.remove=function(t){return this.has(t)?0===t?this.shift():t===this.size-1?this.pop():this.splice(t,1):this},ve.prototype.insert=function(t,e){return this.splice(t,0,e)},ve.prototype.clear=function(){return 0===this.size?this:this.__ownerID?(this.size=this._origin=this._capacity=0,this._level=_,this._root=this._tail=null,this.__hash=void 0,this.__altered=!0,this):xe()},ve.prototype.push=function(){var t=arguments,e=this.size;return this.withMutations((function(n){Ce(n,0,e+t.length);for(var r=0;r<t.length;r++)n.set(e+r,t[r])}))},ve.prototype.pop=function(){return Ce(this,0,-1)},ve.prototype.unshift=function(){var t=arguments;return this.withMutations((function(e){Ce(e,-t.length);for(var n=0;n<t.length;n++)e.set(n,t[n])}))},ve.prototype.shift=function(){return Ce(this,1)},ve.prototype.merge=function(){return De(this,void 0,arguments)},ve.prototype.mergeWith=function(e){return De(this,e,t.call(arguments,1))},ve.prototype.mergeDeep=function(){return De(this,se,arguments)},ve.prototype.mergeDeepWith=function(e){var n=t.call(arguments,1);return De(this,ae(e),n)},ve.prototype.setSize=function(t){return Ce(this,0,t)},ve.prototype.slice=function(t,e){var n=this.size;return C(t,e,n)?this:Ce(this,D(t,n),k(e,n))},ve.prototype.__iterator=function(t,e){var n=0,r=Ee(this,e);return new F((function(){var e=r();return e===Se?{value:void 0,done:!0}:K(t,n++,e)}))},ve.prototype.__iterate=function(t,e){for(var n,r=0,i=Ee(this,e);(n=i())!==Se&&!1!==t(n,r++,this););return r},ve.prototype.__ensureOwner=function(t){return t===this.__ownerID?this:t?Oe(this._origin,this._capacity,this._level,this._root,this._tail,t,this.__hash):(this.__ownerID=t,this)},ve.isList=_e;var ye="@@__IMMUTABLE_LIST__@@",ge=ve.prototype;function me(t,e){this.array=t,this.ownerID=e}ge[ye]=!0,ge[v]=ge.remove,ge.setIn=Wt.setIn,ge.deleteIn=ge.removeIn=Wt.removeIn,ge.update=Wt.update,ge.updateIn=Wt.updateIn,ge.mergeIn=Wt.mergeIn,ge.mergeDeepIn=Wt.mergeDeepIn,ge.withMutations=Wt.withMutations,ge.asMutable=Wt.asMutable,ge.asImmutable=Wt.asImmutable,ge.wasAltered=Wt.wasAltered,me.prototype.removeBefore=function(t,e,n){if(n===e?1<<e:0===this.array.length)return this;var r=n>>>e&g;if(r>=this.array.length)return new me([],t);var i,o=0===r;if(e>0){var s=this.array[r];if((i=s&&s.removeBefore(t,e-_,n))===s&&o)return this}if(o&&!i)return this;var a=ze(this,t);if(!o)for(var u=0;u<r;u++)a.array[u]=void 0;return i&&(a.array[r]=i),a},me.prototype.removeAfter=function(t,e,n){if(n===(e?1<<e:0)||0===this.array.length)return this;var r,i=n-1>>>e&g;if(i>=this.array.length)return this;if(e>0){var o=this.array[i];if((r=o&&o.removeAfter(t,e-_,n))===o&&i===this.array.length-1)return this}var s=ze(this,t);return s.array.splice(i+1),r&&(s.array[i]=r),s};var be,we,Se={};function Ee(t,e){var n=t._origin,r=t._capacity,i=ke(r),o=t._tail;return s(t._root,t._level,0);function s(t,a,u){return 0===a?function(t,s){var a=s===i?o&&o.array:t&&t.array,u=s>n?0:n-s,c=r-s;return c>y&&(c=y),function(){if(u===c)return Se;var t=e?--c:u++;return a&&a[t]}}(t,u):function(t,i,o){var a,u=t&&t.array,c=o>n?0:n-o>>i,f=1+(r-o>>i);return f>y&&(f=y),function(){for(;;){if(a){var t=a();if(t!==Se)return t;a=null}if(c===f)return Se;var n=e?--f:c++;a=s(u&&u[n],i-_,o+(n<<i))}}}(t,a,u)}}function Oe(t,e,n,r,i,o,s){var a=Object.create(ge);return a.size=e-t,a._origin=t,a._capacity=e,a._level=n,a._root=r,a._tail=i,a.__ownerID=o,a.__hash=s,a.__altered=!1,a}function xe(){return be||(be=Oe(0,0,_))}function Ie(t,e,n,r,i,o){var s,a=r>>>n&g,u=t&&a<t.array.length;if(!u&&void 0===i)return t;if(n>0){var c=t&&t.array[a],f=Ie(c,e,n-_,r,i,o);return f===c?t:((s=ze(t,e)).array[a]=f,s)}return u&&t.array[a]===i?t:(E(o),s=ze(t,e),void 0===i&&a===s.array.length-1?s.array.pop():s.array[a]=i,s)}function ze(t,e){return e&&t&&e===t.ownerID?t:new me(t?t.array.slice():[],e)}function Me(t,e){if(e>=ke(t._capacity))return t._tail;if(e<1<<t._level+_){for(var n=t._root,r=t._level;n&&r>0;)n=n.array[e>>>r&g],r-=_;return n}}function Ce(t,e,n){void 0!==e&&(e|=0),void 0!==n&&(n|=0);var r=t.__ownerID||new O,i=t._origin,o=t._capacity,s=i+e,a=void 0===n?o:n<0?o+n:i+n;if(s===i&&a===o)return t;if(s>=a)return t.clear();for(var u=t._level,c=t._root,f=0;s+f<0;)c=new me(c&&c.array.length?[void 0,c]:[],r),f+=1<<(u+=_);f&&(s+=f,i+=f,a+=f,o+=f);for(var l=ke(o),h=ke(a);h>=1<<u+_;)c=new me(c&&c.array.length?[c]:[],r),u+=_;var p=t._tail,d=h<l?Me(t,a-1):h>l?new me([],r):p;if(p&&h>l&&s<o&&p.array.length){for(var v=c=ze(c,r),y=u;y>_;y-=_){var m=l>>>y&g;v=v.array[m]=ze(v.array[m],r)}v.array[l>>>_&g]=p}if(a<o&&(d=d&&d.removeAfter(r,0,a)),s>=h)s-=h,a-=h,u=_,c=null,d=d&&d.removeBefore(r,0,s);else if(s>i||h<l){for(f=0;c;){var b=s>>>u&g;if(b!==h>>>u&g)break;b&&(f+=(1<<u)*b),u-=_,c=c.array[b]}c&&s>i&&(c=c.removeBefore(r,u,s-f)),c&&h<l&&(c=c.removeAfter(r,u,h-f)),f&&(s-=f,a-=f)}return t.__ownerID?(t.size=a-s,t._origin=s,t._capacity=a,t._level=u,t._root=c,t._tail=d,t.__hash=void 0,t.__altered=!0,t):Oe(s,a,u,c,d)}function De(t,e,n){for(var r=[],o=0,a=0;a<n.length;a++){var u=n[a],c=i(u);c.size>o&&(o=c.size),s(u)||(c=c.map((function(t){return ht(t)}))),r.push(c)}return o>t.size&&(t=t.setSize(o)),ue(t,e,r)}function ke(t){return t<y?0:t-1>>>_<<_}function Re(t){return null==t?je():Ae(t)?t:je().withMutations((function(e){var n=r(t);Ft(n.size),n.forEach((function(t,n){return e.set(n,t)}))}))}function Ae(t){return Vt(t)&&f(t)}function qe(t,e,n,r){var i=Object.create(Re.prototype);return i.size=t?t.size:0,i._map=t,i._list=e,i.__ownerID=n,i.__hash=r,i}function je(){return we||(we=qe(te(),xe()))}function Pe(t,e,n){var r,i,o=t._map,s=t._list,a=o.get(e),u=void 0!==a;if(n===m){if(!u)return t;s.size>=y&&s.size>=2*o.size?(r=(i=s.filter((function(t,e){return void 0!==t&&a!==e}))).toKeyedSeq().map((function(t){return t[0]})).flip().toMap(),t.__ownerID&&(r.__ownerID=i.__ownerID=t.__ownerID)):(r=o.remove(e),i=a===s.size-1?s.pop():s.set(a,void 0))}else if(u){if(n===s.get(a)[1])return t;r=o,i=s.set(a,[e,n])}else r=o.set(e,s.size),i=s.set(s.size,[e,n]);return t.__ownerID?(t.size=r.size,t._map=r,t._list=i,t.__hash=void 0,t):qe(r,i)}function Be(t,e){this._iter=t,this._useKeys=e,this.size=t.size}function Te(t){this._iter=t,this.size=t.size}function Fe(t){this._iter=t,this.size=t.size}function Ke(t){this._iter=t,this.size=t.size}function Ve(t){var e=nn(t);return e._iter=t,e.size=t.size,e.flip=function(){return t},e.reverse=function(){var e=t.reverse.apply(this);return e.flip=function(){return t.reverse()},e},e.has=function(e){return t.includes(e)},e.includes=function(e){return t.has(e)},e.cacheResult=rn,e.__iterateUncached=function(e,n){var r=this;return t.__iterate((function(t,n){return!1!==e(n,t,r)}),n)},e.__iteratorUncached=function(e,n){if(e===j){var r=t.__iterator(e,n);return new F((function(){var t=r.next();if(!t.done){var e=t.value[0];t.value[0]=t.value[1],t.value[1]=e}return t}))}return t.__iterator(e===q?A:q,n)},e}function Le(t,e,n){var r=nn(t);return r.size=t.size,r.has=function(e){return t.has(e)},r.get=function(r,i){var o=t.get(r,m);return o===m?i:e.call(n,o,r,t)},r.__iterateUncached=function(r,i){var o=this;return t.__iterate((function(t,i,s){return!1!==r(e.call(n,t,i,s),i,o)}),i)},r.__iteratorUncached=function(r,i){var o=t.__iterator(j,i);return new F((function(){var i=o.next();if(i.done)return i;var s=i.value,a=s[0];return K(r,a,e.call(n,s[1],a,t),i)}))},r}function Ue(t,e){var n=nn(t);return n._iter=t,n.size=t.size,n.reverse=function(){return t},t.flip&&(n.flip=function(){var e=Ve(t);return e.reverse=function(){return t.flip()},e}),n.get=function(n,r){return t.get(e?n:-1-n,r)},n.has=function(n){return t.has(e?n:-1-n)},n.includes=function(e){return t.includes(e)},n.cacheResult=rn,n.__iterate=function(e,n){var r=this;return t.__iterate((function(t,n){return e(t,n,r)}),!n)},n.__iterator=function(e,n){return t.__iterator(e,!n)},n}function We(t,e,n,r){var i=nn(t);return r&&(i.has=function(r){var i=t.get(r,m);return i!==m&&!!e.call(n,i,r,t)},i.get=function(r,i){var o=t.get(r,m);return o!==m&&e.call(n,o,r,t)?o:i}),i.__iterateUncached=function(i,o){var s=this,a=0;return t.__iterate((function(t,o,u){if(e.call(n,t,o,u))return a++,i(t,r?o:a-1,s)}),o),a},i.__iteratorUncached=function(i,o){var s=t.__iterator(j,o),a=0;return new F((function(){for(;;){var o=s.next();if(o.done)return o;var u=o.value,c=u[0],f=u[1];if(e.call(n,f,c,t))return K(i,r?c:a++,f,o)}}))},i}function Ne(t,e,n,r){var i=t.size;if(void 0!==e&&(e|=0),void 0!==n&&(n|=0),C(e,n,i))return t;var o=D(e,i),s=k(n,i);if(o!=o||s!=s)return Ne(t.toSeq().cacheResult(),e,n,r);var a,u=s-o;u==u&&(a=u<0?0:u);var c=nn(t);return c.size=0===a?a:t.size&&a||void 0,!r&&ot(t)&&a>=0&&(c.get=function(e,n){return(e=z(this,e))>=0&&e<a?t.get(e+o,n):n}),c.__iterateUncached=function(e,n){var i=this;if(0===a)return 0;if(n)return this.cacheResult().__iterate(e,n);var s=0,u=!0,c=0;return t.__iterate((function(t,n){if(!u||!(u=s++<o))return c++,!1!==e(t,r?n:c-1,i)&&c!==a})),c},c.__iteratorUncached=function(e,n){if(0!==a&&n)return this.cacheResult().__iterator(e,n);var i=0!==a&&t.__iterator(e,n),s=0,u=0;return new F((function(){for(;s++<o;)i.next();if(++u>a)return{value:void 0,done:!0};var t=i.next();return r||e===q?t:K(e,u-1,e===A?void 0:t.value[1],t)}))},c}function He(t,e,n,r){var i=nn(t);return i.__iterateUncached=function(i,o){var s=this;if(o)return this.cacheResult().__iterate(i,o);var a=!0,u=0;return t.__iterate((function(t,o,c){if(!a||!(a=e.call(n,t,o,c)))return u++,i(t,r?o:u-1,s)})),u},i.__iteratorUncached=function(i,o){var s=this;if(o)return this.cacheResult().__iterator(i,o);var a=t.__iterator(j,o),u=!0,c=0;return new F((function(){var t,o,f;do{if((t=a.next()).done)return r||i===q?t:K(i,c++,i===A?void 0:t.value[1],t);var l=t.value;o=l[0],f=l[1],u&&(u=e.call(n,f,o,s))}while(u);return i===j?t:K(i,o,f,t)}))},i}function Je(t,e,n){var r=nn(t);return r.__iterateUncached=function(r,i){var o=0,a=!1;return function t(u,c){var f=this;u.__iterate((function(i,u){return(!e||c<e)&&s(i)?t(i,c+1):!1===r(i,n?u:o++,f)&&(a=!0),!a}),i)}(t,0),o},r.__iteratorUncached=function(r,i){var o=t.__iterator(r,i),a=[],u=0;return new F((function(){for(;o;){var t=o.next();if(!1===t.done){var c=t.value;if(r===j&&(c=c[1]),e&&!(a.length<e)||!s(c))return n?t:K(r,u++,c,t);a.push(o),o=c.__iterator(r,i)}else o=a.pop()}return{value:void 0,done:!0}}))},r}function $e(t,e,n){e||(e=on);var r=a(t),i=0,o=t.toSeq().map((function(e,r){return[r,e,i++,n?n(e,r,t):e]})).toArray();return o.sort((function(t,n){return e(t[3],n[3])||t[2]-n[2]})).forEach(r?function(t,e){o[e].length=2}:function(t,e){o[e]=t[1]}),r?$(o):u(t)?Y(o):X(o)}function Ye(t,e,n){if(e||(e=on),n){var r=t.toSeq().map((function(e,r){return[e,n(e,r,t)]})).reduce((function(t,n){return Xe(e,t[1],n[1])?n:t}));return r&&r[0]}return t.reduce((function(t,n){return Xe(e,t,n)?n:t}))}function Xe(t,e,n){var r=t(n,e);return 0===r&&n!==e&&(null==n||n!=n)||r>0}function Ge(t,e,r){var i=nn(t);return i.size=new et(r).map((function(t){return t.size})).min(),i.__iterate=function(t,e){for(var n,r=this.__iterator(q,e),i=0;!(n=r.next()).done&&!1!==t(n.value,i++,this););return i},i.__iteratorUncached=function(t,i){var o=r.map((function(t){return t=n(t),W(i?t.reverse():t)})),s=0,a=!1;return new F((function(){var n;return a||(n=o.map((function(t){return t.next()})),a=n.some((function(t){return t.done}))),a?{value:void 0,done:!0}:K(t,s++,e.apply(null,n.map((function(t){return t.value}))))}))},i}function Qe(t,e){return ot(t)?e:t.constructor(e)}function Ze(t){if(t!==Object(t))throw new TypeError("Expected [K, V] tuple: "+t)}function tn(t){return Ft(t.size),I(t)}function en(t){return a(t)?r:u(t)?i:o}function nn(t){return Object.create((a(t)?$:u(t)?Y:X).prototype)}function rn(){return this._iter.cacheResult?(this._iter.cacheResult(),this.size=this._iter.size,this):J.prototype.cacheResult.call(this)}function on(t,e){return t>e?1:t<e?-1:0}function sn(t){var e=W(t);if(!e){if(!H(t))throw new TypeError("Expected iterable or array-like: "+t);e=W(n(t))}return e}function an(t,e){var n,r=function(o){if(o instanceof r)return o;if(!(this instanceof r))return new r(o);if(!n){n=!0;var s=Object.keys(t);(function(t,e){try{e.forEach(ln.bind(void 0,t))}catch(t){}})(i,s),i.size=s.length,i._name=e,i._keys=s,i._defaultValues=t}this._map=Kt(o)},i=r.prototype=Object.create(un);return i.constructor=r,r}e(Re,Kt),Re.of=function(){return this(arguments)},Re.prototype.toString=function(){return this.__toString("OrderedMap {","}")},Re.prototype.get=function(t,e){var n=this._map.get(t);return void 0!==n?this._list.get(n)[1]:e},Re.prototype.clear=function(){return 0===this.size?this:this.__ownerID?(this.size=0,this._map.clear(),this._list.clear(),this):je()},Re.prototype.set=function(t,e){return Pe(this,t,e)},Re.prototype.remove=function(t){return Pe(this,t,m)},Re.prototype.wasAltered=function(){return this._map.wasAltered()||this._list.wasAltered()},Re.prototype.__iterate=function(t,e){var n=this;return this._list.__iterate((function(e){return e&&t(e[1],e[0],n)}),e)},Re.prototype.__iterator=function(t,e){return this._list.fromEntrySeq().__iterator(t,e)},Re.prototype.__ensureOwner=function(t){if(t===this.__ownerID)return this;var e=this._map.__ensureOwner(t),n=this._list.__ensureOwner(t);return t?qe(e,n,t,this.__hash):(this.__ownerID=t,this._map=e,this._list=n,this)},Re.isOrderedMap=Ae,Re.prototype[d]=!0,Re.prototype[v]=Re.prototype.remove,e(Be,$),Be.prototype.get=function(t,e){return this._iter.get(t,e)},Be.prototype.has=function(t){return this._iter.has(t)},Be.prototype.valueSeq=function(){return this._iter.valueSeq()},Be.prototype.reverse=function(){var t=this,e=Ue(this,!0);return this._useKeys||(e.valueSeq=function(){return t._iter.toSeq().reverse()}),e},Be.prototype.map=function(t,e){var n=this,r=Le(this,t,e);return this._useKeys||(r.valueSeq=function(){return n._iter.toSeq().map(t,e)}),r},Be.prototype.__iterate=function(t,e){var n,r=this;return this._iter.__iterate(this._useKeys?function(e,n){return t(e,n,r)}:(n=e?tn(this):0,function(i){return t(i,e?--n:n++,r)}),e)},Be.prototype.__iterator=function(t,e){if(this._useKeys)return this._iter.__iterator(t,e);var n=this._iter.__iterator(q,e),r=e?tn(this):0;return new F((function(){var i=n.next();return i.done?i:K(t,e?--r:r++,i.value,i)}))},Be.prototype[d]=!0,e(Te,Y),Te.prototype.includes=function(t){return this._iter.includes(t)},Te.prototype.__iterate=function(t,e){var n=this,r=0;return this._iter.__iterate((function(e){return t(e,r++,n)}),e)},Te.prototype.__iterator=function(t,e){var n=this._iter.__iterator(q,e),r=0;return new F((function(){var e=n.next();return e.done?e:K(t,r++,e.value,e)}))},e(Fe,X),Fe.prototype.has=function(t){return this._iter.includes(t)},Fe.prototype.__iterate=function(t,e){var n=this;return this._iter.__iterate((function(e){return t(e,e,n)}),e)},Fe.prototype.__iterator=function(t,e){var n=this._iter.__iterator(q,e);return new F((function(){var e=n.next();return e.done?e:K(t,e.value,e.value,e)}))},e(Ke,$),Ke.prototype.entrySeq=function(){return this._iter.toSeq()},Ke.prototype.__iterate=function(t,e){var n=this;return this._iter.__iterate((function(e){if(e){Ze(e);var r=s(e);return t(r?e.get(1):e[1],r?e.get(0):e[0],n)}}),e)},Ke.prototype.__iterator=function(t,e){var n=this._iter.__iterator(q,e);return new F((function(){for(;;){var e=n.next();if(e.done)return e;var r=e.value;if(r){Ze(r);var i=s(r);return K(t,i?r.get(0):r[0],i?r.get(1):r[1],e)}}}))},Te.prototype.cacheResult=Be.prototype.cacheResult=Fe.prototype.cacheResult=Ke.prototype.cacheResult=rn,e(an,St),an.prototype.toString=function(){return this.__toString(fn(this)+" {","}")},an.prototype.has=function(t){return this._defaultValues.hasOwnProperty(t)},an.prototype.get=function(t,e){if(!this.has(t))return e;var n=this._defaultValues[t];return this._map?this._map.get(t,n):n},an.prototype.clear=function(){if(this.__ownerID)return this._map&&this._map.clear(),this;var t=this.constructor;return t._empty||(t._empty=cn(this,te()))},an.prototype.set=function(t,e){if(!this.has(t))throw new Error('Cannot set unknown key "'+t+'" on '+fn(this));var n=this._map&&this._map.set(t,e);return this.__ownerID||n===this._map?this:cn(this,n)},an.prototype.remove=function(t){if(!this.has(t))return this;var e=this._map&&this._map.remove(t);return this.__ownerID||e===this._map?this:cn(this,e)},an.prototype.wasAltered=function(){return this._map.wasAltered()},an.prototype.__iterator=function(t,e){var n=this;return r(this._defaultValues).map((function(t,e){return n.get(e)})).__iterator(t,e)},an.prototype.__iterate=function(t,e){var n=this;return r(this._defaultValues).map((function(t,e){return n.get(e)})).__iterate(t,e)},an.prototype.__ensureOwner=function(t){if(t===this.__ownerID)return this;var e=this._map&&this._map.__ensureOwner(t);return t?cn(this,e,t):(this.__ownerID=t,this._map=e,this)};var un=an.prototype;function cn(t,e,n){var r=Object.create(Object.getPrototypeOf(t));return r._map=e,r.__ownerID=n,r}function fn(t){return t._name||t.constructor.name||"Record"}function ln(t,e){Object.defineProperty(t,e,{get:function(){return this.get(e)},set:function(t){mt(this.__ownerID,"Cannot set on an immutable record."),this.set(e,t)}})}function hn(t){return null==t?bn():pn(t)&&!f(t)?t:bn().withMutations((function(e){var n=o(t);Ft(n.size),n.forEach((function(t){return e.add(t)}))}))}function pn(t){return!(!t||!t[vn])}un[v]=un.remove,un.deleteIn=un.removeIn=Wt.removeIn,un.merge=Wt.merge,un.mergeWith=Wt.mergeWith,un.mergeIn=Wt.mergeIn,un.mergeDeep=Wt.mergeDeep,un.mergeDeepWith=Wt.mergeDeepWith,un.mergeDeepIn=Wt.mergeDeepIn,un.setIn=Wt.setIn,un.update=Wt.update,un.updateIn=Wt.updateIn,un.withMutations=Wt.withMutations,un.asMutable=Wt.asMutable,un.asImmutable=Wt.asImmutable,e(hn,Ot),hn.of=function(){return this(arguments)},hn.fromKeys=function(t){return this(r(t).keySeq())},hn.prototype.toString=function(){return this.__toString("Set {","}")},hn.prototype.has=function(t){return this._map.has(t)},hn.prototype.add=function(t){return gn(this,this._map.set(t,!0))},hn.prototype.remove=function(t){return gn(this,this._map.remove(t))},hn.prototype.clear=function(){return gn(this,this._map.clear())},hn.prototype.union=function(){var e=t.call(arguments,0);return 0===(e=e.filter((function(t){return 0!==t.size}))).length?this:0!==this.size||this.__ownerID||1!==e.length?this.withMutations((function(t){for(var n=0;n<e.length;n++)o(e[n]).forEach((function(e){return t.add(e)}))})):this.constructor(e[0])},hn.prototype.intersect=function(){var e=t.call(arguments,0);if(0===e.length)return this;e=e.map((function(t){return o(t)}));var n=this;return this.withMutations((function(t){n.forEach((function(n){e.every((function(t){return t.includes(n)}))||t.remove(n)}))}))},hn.prototype.subtract=function(){var e=t.call(arguments,0);if(0===e.length)return this;e=e.map((function(t){return o(t)}));var n=this;return this.withMutations((function(t){n.forEach((function(n){e.some((function(t){return t.includes(n)}))&&t.remove(n)}))}))},hn.prototype.merge=function(){return this.union.apply(this,arguments)},hn.prototype.mergeWith=function(e){var n=t.call(arguments,1);return this.union.apply(this,n)},hn.prototype.sort=function(t){return wn($e(this,t))},hn.prototype.sortBy=function(t,e){return wn($e(this,e,t))},hn.prototype.wasAltered=function(){return this._map.wasAltered()},hn.prototype.__iterate=function(t,e){var n=this;return this._map.__iterate((function(e,r){return t(r,r,n)}),e)},hn.prototype.__iterator=function(t,e){return this._map.map((function(t,e){return e})).__iterator(t,e)},hn.prototype.__ensureOwner=function(t){if(t===this.__ownerID)return this;var e=this._map.__ensureOwner(t);return t?this.__make(e,t):(this.__ownerID=t,this._map=e,this)},hn.isSet=pn;var dn,vn="@@__IMMUTABLE_SET__@@",yn=hn.prototype;function gn(t,e){return t.__ownerID?(t.size=e.size,t._map=e,t):e===t._map?t:0===e.size?t.__empty():t.__make(e)}function mn(t,e){var n=Object.create(yn);return n.size=t?t.size:0,n._map=t,n.__ownerID=e,n}function bn(){return dn||(dn=mn(te()))}function wn(t){return null==t?In():Sn(t)?t:In().withMutations((function(e){var n=o(t);Ft(n.size),n.forEach((function(t){return e.add(t)}))}))}function Sn(t){return pn(t)&&f(t)}yn[vn]=!0,yn[v]=yn.remove,yn.mergeDeep=yn.merge,yn.mergeDeepWith=yn.mergeWith,yn.withMutations=Wt.withMutations,yn.asMutable=Wt.asMutable,yn.asImmutable=Wt.asImmutable,yn.__empty=bn,yn.__make=mn,e(wn,hn),wn.of=function(){return this(arguments)},wn.fromKeys=function(t){return this(r(t).keySeq())},wn.prototype.toString=function(){return this.__toString("OrderedSet {","}")},wn.isOrderedSet=Sn;var En,On=wn.prototype;function xn(t,e){var n=Object.create(On);return n.size=t?t.size:0,n._map=t,n.__ownerID=e,n}function In(){return En||(En=xn(je()))}function zn(t){return null==t?An():Mn(t)?t:An().unshiftAll(t)}function Mn(t){return!(!t||!t[Dn])}On[d]=!0,On.__empty=In,On.__make=xn,e(zn,Et),zn.of=function(){return this(arguments)},zn.prototype.toString=function(){return this.__toString("Stack [","]")},zn.prototype.get=function(t,e){var n=this._head;for(t=z(this,t);n&&t--;)n=n.next;return n?n.value:e},zn.prototype.peek=function(){return this._head&&this._head.value},zn.prototype.push=function(){if(0===arguments.length)return this;for(var t=this.size+arguments.length,e=this._head,n=arguments.length-1;n>=0;n--)e={value:arguments[n],next:e};return this.__ownerID?(this.size=t,this._head=e,this.__hash=void 0,this.__altered=!0,this):Rn(t,e)},zn.prototype.pushAll=function(t){if(0===(t=i(t)).size)return this;Ft(t.size);var e=this.size,n=this._head;return t.reverse().forEach((function(t){e++,n={value:t,next:n}})),this.__ownerID?(this.size=e,this._head=n,this.__hash=void 0,this.__altered=!0,this):Rn(e,n)},zn.prototype.pop=function(){return this.slice(1)},zn.prototype.unshift=function(){return this.push.apply(this,arguments)},zn.prototype.unshiftAll=function(t){return this.pushAll(t)},zn.prototype.shift=function(){return this.pop.apply(this,arguments)},zn.prototype.clear=function(){return 0===this.size?this:this.__ownerID?(this.size=0,this._head=void 0,this.__hash=void 0,this.__altered=!0,this):An()},zn.prototype.slice=function(t,e){if(C(t,e,this.size))return this;var n=D(t,this.size);if(k(e,this.size)!==this.size)return Et.prototype.slice.call(this,t,e);for(var r=this.size-n,i=this._head;n--;)i=i.next;return this.__ownerID?(this.size=r,this._head=i,this.__hash=void 0,this.__altered=!0,this):Rn(r,i)},zn.prototype.__ensureOwner=function(t){return t===this.__ownerID?this:t?Rn(this.size,this._head,t,this.__hash):(this.__ownerID=t,this.__altered=!1,this)},zn.prototype.__iterate=function(t,e){if(e)return this.reverse().__iterate(t);for(var n=0,r=this._head;r&&!1!==t(r.value,n++,this);)r=r.next;return n},zn.prototype.__iterator=function(t,e){if(e)return this.reverse().__iterator(t);var n=0,r=this._head;return new F((function(){if(r){var e=r.value;return r=r.next,K(t,n++,e)}return{value:void 0,done:!0}}))},zn.isStack=Mn;var Cn,Dn="@@__IMMUTABLE_STACK__@@",kn=zn.prototype;function Rn(t,e,n,r){var i=Object.create(kn);return i.size=t,i._head=e,i.__ownerID=n,i.__hash=r,i.__altered=!1,i}function An(){return Cn||(Cn=Rn(0))}function qn(t,e){var n=function(n){t.prototype[n]=e[n]};return Object.keys(e).forEach(n),Object.getOwnPropertySymbols&&Object.getOwnPropertySymbols(e).forEach(n),t}kn[Dn]=!0,kn.withMutations=Wt.withMutations,kn.asMutable=Wt.asMutable,kn.asImmutable=Wt.asImmutable,kn.wasAltered=Wt.wasAltered,n.Iterator=F,qn(n,{toArray:function(){Ft(this.size);var t=new Array(this.size||0);return this.valueSeq().__iterate((function(e,n){t[n]=e})),t},toIndexedSeq:function(){return new Te(this)},toJS:function(){return this.toSeq().map((function(t){return t&&"function"==typeof t.toJS?t.toJS():t})).__toJS()},toJSON:function(){return this.toSeq().map((function(t){return t&&"function"==typeof t.toJSON?t.toJSON():t})).__toJS()},toKeyedSeq:function(){return new Be(this,!0)},toMap:function(){return Kt(this.toKeyedSeq())},toObject:function(){Ft(this.size);var t={};return this.__iterate((function(e,n){t[n]=e})),t},toOrderedMap:function(){return Re(this.toKeyedSeq())},toOrderedSet:function(){return wn(a(this)?this.valueSeq():this)},toSet:function(){return hn(a(this)?this.valueSeq():this)},toSetSeq:function(){return new Fe(this)},toSeq:function(){return u(this)?this.toIndexedSeq():a(this)?this.toKeyedSeq():this.toSetSeq()},toStack:function(){return zn(a(this)?this.valueSeq():this)},toList:function(){return ve(a(this)?this.valueSeq():this)},toString:function(){return"[Iterable]"},__toString:function(t,e){return 0===this.size?t+e:t+" "+this.toSeq().map(this.__toStringMapper).join(", ")+" "+e},concat:function(){return Qe(this,function(t,e){var n=a(t),i=[t].concat(e).map((function(t){return s(t)?n&&(t=r(t)):t=n?at(t):ut(Array.isArray(t)?t:[t]),t})).filter((function(t){return 0!==t.size}));if(0===i.length)return t;if(1===i.length){var o=i[0];if(o===t||n&&a(o)||u(t)&&u(o))return o}var c=new et(i);return n?c=c.toKeyedSeq():u(t)||(c=c.toSetSeq()),(c=c.flatten(!0)).size=i.reduce((function(t,e){if(void 0!==t){var n=e.size;if(void 0!==n)return t+n}}),0),c}(this,t.call(arguments,0)))},includes:function(t){return this.some((function(e){return _t(e,t)}))},entries:function(){return this.__iterator(j)},every:function(t,e){Ft(this.size);var n=!0;return this.__iterate((function(r,i,o){if(!t.call(e,r,i,o))return n=!1,!1})),n},filter:function(t,e){return Qe(this,We(this,t,e,!0))},find:function(t,e,n){var r=this.findEntry(t,e);return r?r[1]:n},findEntry:function(t,e){var n;return this.__iterate((function(r,i,o){if(t.call(e,r,i,o))return n=[i,r],!1})),n},findLastEntry:function(t,e){return this.toSeq().reverse().findEntry(t,e)},forEach:function(t,e){return Ft(this.size),this.__iterate(e?t.bind(e):t)},join:function(t){Ft(this.size),t=void 0!==t?""+t:",";var e="",n=!0;return this.__iterate((function(r){n?n=!1:e+=t,e+=null!=r?r.toString():""})),e},keys:function(){return this.__iterator(A)},map:function(t,e){return Qe(this,Le(this,t,e))},reduce:function(t,e,n){var r,i;return Ft(this.size),arguments.length<2?i=!0:r=e,this.__iterate((function(e,o,s){i?(i=!1,r=e):r=t.call(n,r,e,o,s)})),r},reduceRight:function(t,e,n){var r=this.toKeyedSeq().reverse();return r.reduce.apply(r,arguments)},reverse:function(){return Qe(this,Ue(this,!0))},slice:function(t,e){return Qe(this,Ne(this,t,e,!0))},some:function(t,e){return!this.every(Fn(t),e)},sort:function(t){return Qe(this,$e(this,t))},values:function(){return this.__iterator(q)},butLast:function(){return this.slice(0,-1)},isEmpty:function(){return void 0!==this.size?0===this.size:!this.some((function(){return!0}))},count:function(t,e){return I(t?this.toSeq().filter(t,e):this)},countBy:function(t,e){return function(t,e,n){var r=Kt().asMutable();return t.__iterate((function(i,o){r.update(e.call(n,i,o,t),0,(function(t){return t+1}))})),r.asImmutable()}(this,t,e)},equals:function(t){return yt(this,t)},entrySeq:function(){var t=this;if(t._cache)return new et(t._cache);var e=t.toSeq().map(Tn).toIndexedSeq();return e.fromEntrySeq=function(){return t.toSeq()},e},filterNot:function(t,e){return this.filter(Fn(t),e)},findLast:function(t,e,n){return this.toKeyedSeq().reverse().find(t,e,n)},first:function(){return this.find(M)},flatMap:function(t,e){return Qe(this,function(t,e,n){var r=en(t);return t.toSeq().map((function(i,o){return r(e.call(n,i,o,t))})).flatten(!0)}(this,t,e))},flatten:function(t){return Qe(this,Je(this,t,!0))},fromEntrySeq:function(){return new Ke(this)},get:function(t,e){return this.find((function(e,n){return _t(n,t)}),void 0,e)},getIn:function(t,e){for(var n,r=this,i=sn(t);!(n=i.next()).done;){var o=n.value;if((r=r&&r.get?r.get(o,m):m)===m)return e}return r},groupBy:function(t,e){return function(t,e,n){var r=a(t),i=(f(t)?Re():Kt()).asMutable();t.__iterate((function(o,s){i.update(e.call(n,o,s,t),(function(t){return(t=t||[]).push(r?[s,o]:o),t}))}));var o=en(t);return i.map((function(e){return Qe(t,o(e))}))}(this,t,e)},has:function(t){return this.get(t,m)!==m},hasIn:function(t){return this.getIn(t,m)!==m},isSubset:function(t){return t="function"==typeof t.includes?t:n(t),this.every((function(e){return t.includes(e)}))},isSuperset:function(t){return(t="function"==typeof t.isSubset?t:n(t)).isSubset(this)},keySeq:function(){return this.toSeq().map(Bn).toIndexedSeq()},last:function(){return this.toSeq().reverse().first()},max:function(t){return Ye(this,t)},maxBy:function(t,e){return Ye(this,e,t)},min:function(t){return Ye(this,t?Kn(t):Un)},minBy:function(t,e){return Ye(this,e?Kn(e):Un,t)},rest:function(){return this.slice(1)},skip:function(t){return this.slice(Math.max(0,t))},skipLast:function(t){return Qe(this,this.toSeq().reverse().skip(t).reverse())},skipWhile:function(t,e){return Qe(this,He(this,t,e,!0))},skipUntil:function(t,e){return this.skipWhile(Fn(t),e)},sortBy:function(t,e){return Qe(this,$e(this,e,t))},take:function(t){return this.slice(0,Math.max(0,t))},takeLast:function(t){return Qe(this,this.toSeq().reverse().take(t).reverse())},takeWhile:function(t,e){return Qe(this,function(t,e,n){var r=nn(t);return r.__iterateUncached=function(r,i){var o=this;if(i)return this.cacheResult().__iterate(r,i);var s=0;return t.__iterate((function(t,i,a){return e.call(n,t,i,a)&&++s&&r(t,i,o)})),s},r.__iteratorUncached=function(r,i){var o=this;if(i)return this.cacheResult().__iterator(r,i);var s=t.__iterator(j,i),a=!0;return new F((function(){if(!a)return{value:void 0,done:!0};var t=s.next();if(t.done)return t;var i=t.value,u=i[0],c=i[1];return e.call(n,c,u,o)?r===j?t:K(r,u,c,t):(a=!1,{value:void 0,done:!0})}))},r}(this,t,e))},takeUntil:function(t,e){return this.takeWhile(Fn(t),e)},valueSeq:function(){return this.toIndexedSeq()},hashCode:function(){return this.__hash||(this.__hash=function(t){if(t.size===1/0)return 0;var e=f(t),n=a(t),r=e?1:0;return function(t,e){return e=xt(e,3432918353),e=xt(e<<15|e>>>-15,461845907),e=xt(e<<13|e>>>-13,5),e=xt((e=(e+3864292196|0)^t)^e>>>16,2246822507),It((e=xt(e^e>>>13,3266489909))^e>>>16)}(t.__iterate(n?e?function(t,e){r=31*r+Wn(zt(t),zt(e))|0}:function(t,e){r=r+Wn(zt(t),zt(e))|0}:e?function(t){r=31*r+zt(t)|0}:function(t){r=r+zt(t)|0}),r)}(this))}});var jn=n.prototype;jn[l]=!0,jn[T]=jn.values,jn.__toJS=jn.toArray,jn.__toStringMapper=Vn,jn.inspect=jn.toSource=function(){return this.toString()},jn.chain=jn.flatMap,jn.contains=jn.includes,function(){try{Object.defineProperty(jn,"length",{get:function(){if(!n.noLengthWarning){var t;try{throw new Error}catch(e){t=e.stack}if(-1===t.indexOf("_wrapObject"))return console&&console.warn&&console.warn("iterable.length has been deprecated, use iterable.size or iterable.count(). This warning will become a silent error in a future version. "+t),this.size}}})}catch(t){}}(),qn(r,{flip:function(){return Qe(this,Ve(this))},findKey:function(t,e){var n=this.findEntry(t,e);return n&&n[0]},findLastKey:function(t,e){return this.toSeq().reverse().findKey(t,e)},keyOf:function(t){return this.findKey((function(e){return _t(e,t)}))},lastKeyOf:function(t){return this.findLastKey((function(e){return _t(e,t)}))},mapEntries:function(t,e){var n=this,r=0;return Qe(this,this.toSeq().map((function(i,o){return t.call(e,[o,i],r++,n)})).fromEntrySeq())},mapKeys:function(t,e){var n=this;return Qe(this,this.toSeq().flip().map((function(r,i){return t.call(e,r,i,n)})).flip())}});var Pn=r.prototype;function Bn(t,e){return e}function Tn(t,e){return[e,t]}function Fn(t){return function(){return!t.apply(this,arguments)}}function Kn(t){return function(){return-t.apply(this,arguments)}}function Vn(t){return"string"==typeof t?JSON.stringify(t):t}function Ln(){return x(arguments)}function Un(t,e){return t<e?1:t>e?-1:0}function Wn(t,e){return t^e+2654435769+(t<<6)+(t>>2)|0}return Pn[h]=!0,Pn[T]=jn.entries,Pn.__toJS=jn.toObject,Pn.__toStringMapper=function(t,e){return JSON.stringify(e)+": "+Vn(t)},qn(i,{toKeyedSeq:function(){return new Be(this,!1)},filter:function(t,e){return Qe(this,We(this,t,e,!1))},findIndex:function(t,e){var n=this.findEntry(t,e);return n?n[0]:-1},indexOf:function(t){var e=this.toKeyedSeq().keyOf(t);return void 0===e?-1:e},lastIndexOf:function(t){var e=this.toKeyedSeq().reverse().keyOf(t);return void 0===e?-1:e},reverse:function(){return Qe(this,Ue(this,!1))},slice:function(t,e){return Qe(this,Ne(this,t,e,!1))},splice:function(t,e){var n=arguments.length;if(e=Math.max(0|e,0),0===n||2===n&&!e)return this;t=D(t,t<0?this.count():this.size);var r=this.slice(0,t);return Qe(this,1===n?r:r.concat(x(arguments,2),this.slice(t+e)))},findLastIndex:function(t,e){var n=this.toKeyedSeq().findLastKey(t,e);return void 0===n?-1:n},first:function(){return this.get(0)},flatten:function(t){return Qe(this,Je(this,t,!1))},get:function(t,e){return(t=z(this,t))<0||this.size===1/0||void 0!==this.size&&t>this.size?e:this.find((function(e,n){return n===t}),void 0,e)},has:function(t){return(t=z(this,t))>=0&&(void 0!==this.size?this.size===1/0||t<this.size:-1!==this.indexOf(t))},interpose:function(t){return Qe(this,function(t,e){var n=nn(t);return n.size=t.size&&2*t.size-1,n.__iterateUncached=function(n,r){var i=this,o=0;return t.__iterate((function(t,r){return(!o||!1!==n(e,o++,i))&&!1!==n(t,o++,i)}),r),o},n.__iteratorUncached=function(n,r){var i,o=t.__iterator(q,r),s=0;return new F((function(){return(!i||s%2)&&(i=o.next()).done?i:s%2?K(n,s++,e):K(n,s++,i.value,i)}))},n}(this,t))},interleave:function(){var t=[this].concat(x(arguments)),e=Ge(this.toSeq(),Y.of,t),n=e.flatten(!0);return e.size&&(n.size=e.size*t.length),Qe(this,n)},last:function(){return this.get(-1)},skipWhile:function(t,e){return Qe(this,He(this,t,e,!1))},zip:function(){return Qe(this,Ge(this,Ln,[this].concat(x(arguments))))},zipWith:function(t){var e=x(arguments);return e[0]=this,Qe(this,Ge(this,t,e))}}),i.prototype[p]=!0,i.prototype[d]=!0,qn(o,{get:function(t,e){return this.has(t)?t:e},includes:function(t){return this.has(t)},keySeq:function(){return this.valueSeq()}}),o.prototype.has=jn.includes,qn($,r.prototype),qn(Y,i.prototype),qn(X,o.prototype),qn(St,r.prototype),qn(Et,i.prototype),qn(Ot,o.prototype),{Iterable:n,Seq:J,Collection:wt,Map:Kt,OrderedMap:Re,List:ve,Stack:zn,Set:hn,OrderedSet:wn,Record:an,Range:bt,Repeat:gt,is:_t,fromJS:ht}}()},69590:t=>{var e="undefined"!=typeof Element,n="function"==typeof Map,r="function"==typeof Set,i="function"==typeof ArrayBuffer&&!!ArrayBuffer.isView;function o(t,s){if(t===s)return!0;if(t&&s&&"object"==typeof t&&"object"==typeof s){if(t.constructor!==s.constructor)return!1;var a,u,c,f;if(Array.isArray(t)){if((a=t.length)!=s.length)return!1;for(u=a;0!=u--;)if(!o(t[u],s[u]))return!1;return!0}if(n&&t instanceof Map&&s instanceof Map){if(t.size!==s.size)return!1;for(f=t.entries();!(u=f.next()).done;)if(!s.has(u.value[0]))return!1;for(f=t.entries();!(u=f.next()).done;)if(!o(u.value[1],s.get(u.value[0])))return!1;return!0}if(r&&t instanceof Set&&s instanceof Set){if(t.size!==s.size)return!1;for(f=t.entries();!(u=f.next()).done;)if(!s.has(u.value[0]))return!1;return!0}if(i&&ArrayBuffer.isView(t)&&ArrayBuffer.isView(s)){if((a=t.length)!=s.length)return!1;for(u=a;0!=u--;)if(t[u]!==s[u])return!1;return!0}if(t.constructor===RegExp)return t.source===s.source&&t.flags===s.flags;if(t.valueOf!==Object.prototype.valueOf)return t.valueOf()===s.valueOf();if(t.toString!==Object.prototype.toString)return t.toString()===s.toString();if((a=(c=Object.keys(t)).length)!==Object.keys(s).length)return!1;for(u=a;0!=u--;)if(!Object.prototype.hasOwnProperty.call(s,c[u]))return!1;if(e&&t instanceof Element)return!1;for(u=a;0!=u--;)if(("_owner"!==c[u]&&"__v"!==c[u]&&"__o"!==c[u]||!t.$$typeof)&&!o(t[c[u]],s[c[u]]))return!1;return!0}return t!=t&&s!=s}t.exports=function(t,e){try{return o(t,e)}catch(t){if((t.message||"").match(/stack|recursion/i))return console.warn("react-fast-compare cannot handle circular refs"),!1;throw t}}},70602:(t,e)=>{var n;!function(){"use strict";var r={}.hasOwnProperty;function i(){for(var t=[],e=0;e<arguments.length;e++){var n=arguments[e];if(n){var o=typeof n;if("string"===o||"number"===o)t.push(n);else if(Array.isArray(n)){if(n.length){var s=i.apply(null,n);s&&t.push(s)}}else if("object"===o){if(n.toString!==Object.prototype.toString&&!n.toString.toString().includes("[native code]")){t.push(n.toString());continue}for(var a in n)r.call(n,a)&&n[a]&&t.push(a)}}}return t.join(" ")}t.exports?(i.default=i,t.exports=i):void 0===(n=function(){return i}.apply(e,[]))||(t.exports=n)}()},99196:t=>{"use strict";t.exports=window.React},25853:t=>{"use strict";t.exports=window.lodash.debounce},1843:t=>{"use strict";t.exports=window.lodash.filter},18491:t=>{"use strict";t.exports=window.lodash.get},16965:t=>{"use strict";t.exports=window.lodash.includes},66366:t=>{"use strict";t.exports=window.lodash.isEmpty},12049:t=>{"use strict";t.exports=window.lodash.uniqueId},25158:t=>{"use strict";t.exports=window.wp.a11y},55609:t=>{"use strict";t.exports=window.wp.components},92694:t=>{"use strict";t.exports=window.wp.hooks},65736:t=>{"use strict";t.exports=window.wp.i18n},81413:t=>{"use strict";t.exports=window.yoast.componentsNew},7206:t=>{"use strict";t.exports=window.yoast.draftJs},23695:t=>{"use strict";t.exports=window.yoast.helpers},85890:t=>{"use strict";t.exports=window.yoast.propTypes},37188:t=>{"use strict";t.exports=window.yoast.styleGuide},98487:t=>{"use strict";t.exports=window.yoast.styledComponents}},e={};function n(r){var i=e[r];if(void 0!==i)return i.exports;var o=e[r]={exports:{}};return t[r].call(o.exports,o,o.exports,n),o.exports}n.n=t=>{var e=t&&t.__esModule?()=>t.default:()=>t;return n.d(e,{a:e}),e},n.d=(t,e)=>{for(var r in e)n.o(e,r)&&!n.o(t,r)&&Object.defineProperty(t,r,{enumerable:!0,get:e[r]})},n.o=(t,e)=>Object.prototype.hasOwnProperty.call(t,e),n.r=t=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})};var r={};(()=>{"use strict";var t=r;Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"ReplacementVariableEditor",{enumerable:!0,get:function(){return e.default}}),Object.defineProperty(t,"ReplacementVariableEditorStandalone",{enumerable:!0,get:function(){return i.default}}),Object.defineProperty(t,"SettingsSnippetEditor",{enumerable:!0,get:function(){return o.default}}),Object.defineProperty(t,"StandardButton",{enumerable:!0,get:function(){return a.StandardButton}}),Object.defineProperty(t,"StyledEditor",{enumerable:!0,get:function(){return s.StyledEditor}}),Object.defineProperty(t,"TriggerReplacementVariableSuggestionsButton",{enumerable:!0,get:function(){return a.TriggerReplacementVariableSuggestionsButton}}),Object.defineProperty(t,"recommendedReplacementVariablesShape",{enumerable:!0,get:function(){return u.recommendedReplacementVariablesShape}}),Object.defineProperty(t,"replacementVariablesShape",{enumerable:!0,get:function(){return u.replacementVariablesShape}});var e=c(n(51381)),i=c(n(42578)),o=c(n(26895)),s=n(9283),a=n(32183),u=n(34353);function c(t){return t&&t.__esModule?t:{default:t}}})(),(window.yoast=window.yoast||{}).replacementVariableEditor=r})();