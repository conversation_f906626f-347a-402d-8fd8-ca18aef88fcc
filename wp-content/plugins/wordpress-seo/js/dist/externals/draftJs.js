(()=>{var t={19785:(t,e,r)=>{"use strict";function n(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{},n=Object.keys(r);"function"==typeof Object.getOwnPropertySymbols&&(n=n.concat(Object.getOwnPropertySymbols(r).filter((function(t){return Object.getOwnPropertyDescriptor(r,t).enumerable})))),n.forEach((function(e){i(t,e,r[e])}))}return t}function i(t,e,r){return e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var o=r(10329),a=r(4516),s=r(38777),u=r(67953),c=r(42307),l=r(14289),f=r(25027),p=r(68642),h=r(43393),d=r(61173),g=p("draft_tree_data_support"),y=g?u:s,v=h.List,m=h.Repeat,_={insertAtomicBlock:function(t,e,r){var i=t.getCurrentContent(),s=t.getSelection(),u=c.removeRange(i,s,"backward"),p=u.getSelectionAfter(),h=c.splitBlock(u,p),d=h.getSelectionAfter(),_=c.setBlockType(h,d,"atomic"),b=a.create({entity:e}),S={key:f(),type:"atomic",text:r,characterList:v(m(b,r.length))},w={key:f(),type:"unstyled"};g&&(S=n({},S,{nextSibling:w.key}),w=n({},w,{prevSibling:S.key}));var x=[new y(S),new y(w)],k=o.createFromArray(x),C=c.replaceWithFragment(_,d,k),E=C.merge({selectionBefore:s,selectionAfter:C.getSelectionAfter().set("hasFocus",!0)});return l.push(t,E,"insert-fragment")},moveAtomicBlock:function(t,e,r,n){var i,o=t.getCurrentContent(),a=t.getSelection();if("before"===n||"after"===n){var s=o.getBlockForKey("before"===n?r.getStartKey():r.getEndKey());i=d(o,e,s,n)}else{var u=c.removeRange(o,r,"backward"),f=u.getSelectionAfter(),p=u.getBlockForKey(f.getFocusKey());if(0===f.getStartOffset())i=d(u,e,p,"before");else if(f.getEndOffset()===p.getLength())i=d(u,e,p,"after");else{var h=c.splitBlock(u,f),g=h.getSelectionAfter(),y=h.getBlockForKey(g.getFocusKey());i=d(h,e,y,"before")}}var v=i.merge({selectionBefore:a,selectionAfter:i.getSelectionAfter().set("hasFocus",!0)});return l.push(t,v,"move-block")}};t.exports=_},10329:(t,e,r)=>{"use strict";var n=r(43393).OrderedMap,i={createFromArray:function(t){return n(t.map((function(t){return[t.getKey(),t]})))}};t.exports=i},34365:(t,e,r)=>{"use strict";function n(t,e,r){return e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var i=r(29407),o=r(96495),a=r(43393),s=a.List,u=a.Repeat,c=a.Record,l=function(){return!0},f=c({start:null,end:null}),p=c({start:null,end:null,decoratorKey:null,leaves:null}),h={generate:function(t,e,r){var n=e.getLength();if(!n)return s.of(new p({start:0,end:0,decoratorKey:null,leaves:s.of(new f({start:0,end:0}))}));var o=[],a=r?r.getDecorations(e,t):s(u(null,n)),c=e.getCharacterList();return i(a,d,l,(function(t,e){var r,n,u,h;o.push(new p({start:t,end:e,decoratorKey:a.get(t),leaves:(r=c.slice(t,e).toList(),n=t,u=[],h=r.map((function(t){return t.getStyle()})).toList(),i(h,d,l,(function(t,e){u.push(new f({start:t+n,end:e+n}))})),s(u))}))})),s(o)},fromJS:function(t){var e=t.leaves,r=function(t,e){if(null==t)return{};var r,n,i={},o=Object.keys(t);for(n=0;n<o.length;n++)r=o[n],e.indexOf(r)>=0||(i[r]=t[r]);return i}(t,["leaves"]);return new p(function(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{},i=Object.keys(r);"function"==typeof Object.getOwnPropertySymbols&&(i=i.concat(Object.getOwnPropertySymbols(r).filter((function(t){return Object.getOwnPropertyDescriptor(r,t).enumerable})))),i.forEach((function(e){n(t,e,r[e])}))}return t}({},r,{leaves:null!=e?s(Array.isArray(e)?e:o(e)).map((function(t){return f(t)})):null}))}};function d(t,e){return t===e}t.exports=h},4516:(t,e,r)=>{"use strict";var n=r(43393),i=n.Map,o=n.OrderedSet,a=n.Record,s=o(),u={style:s,entity:null},c=function(t){var e,r;function n(){return t.apply(this,arguments)||this}r=t,(e=n).prototype=Object.create(r.prototype),e.prototype.constructor=e,e.__proto__=r;var a=n.prototype;return a.getStyle=function(){return this.get("style")},a.getEntity=function(){return this.get("entity")},a.hasStyle=function(t){return this.getStyle().includes(t)},n.applyStyle=function(t,e){var r=t.set("style",t.getStyle().add(e));return n.create(r)},n.removeStyle=function(t,e){var r=t.set("style",t.getStyle().remove(e));return n.create(r)},n.applyEntity=function(t,e){var r=t.getEntity()===e?t:t.set("entity",e);return n.create(r)},n.create=function(t){if(!t)return l;var e=i({style:s,entity:null}).merge(t),r=f.get(e);if(r)return r;var o=new n(e);return f=f.set(e,o),o},n.fromJS=function(t){var e=t.style,r=t.entity;return new n({style:Array.isArray(e)?o(e):e,entity:Array.isArray(r)?o(r):r})},n}(a(u)),l=new c,f=i([[i(u),l]]);c.EMPTY=l,t.exports=c},25369:(t,e,r)=>{"use strict";var n=r(43393).List,i=function(){function t(t){var e,r;r=void 0,(e="_decorators")in this?Object.defineProperty(this,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):this[e]=r,this._decorators=t.slice()}var e=t.prototype;return e.getDecorations=function(t,e){var r=Array(t.getText().length).fill(null);return this._decorators.forEach((function(n,i){var o=0;(0,n.strategy)(t,(function(t,e){(function(t,e,r){for(var n=e;n<r;n++)if(null!=t[n])return!1;return!0})(r,t,e)&&(function(t,e,r,n){for(var i=e;i<r;i++)t[i]=n}(r,t,e,i+"."+o),o++)}),e)})),n(r)},e.getComponentForKey=function(t){var e=parseInt(t.split(".")[0],10);return this._decorators[e].component},e.getPropsForKey=function(t){var e=parseInt(t.split(".")[0],10);return this._decorators[e].props},t}();t.exports=i},38777:(t,e,r)=>{"use strict";var n=r(4516),i=r(29407),o=r(43393),a=o.List,s=o.Map,u=o.OrderedSet,c=o.Record,l=o.Repeat,f=u(),p=function(t){var e,r;function o(e){return t.call(this,function(t){if(!t)return t;var e=t.characterList,r=t.text;return r&&!e&&(t.characterList=a(l(n.EMPTY,r.length))),t}(e))||this}r=t,(e=o).prototype=Object.create(r.prototype),e.prototype.constructor=e,e.__proto__=r;var s=o.prototype;return s.getKey=function(){return this.get("key")},s.getType=function(){return this.get("type")},s.getText=function(){return this.get("text")},s.getCharacterList=function(){return this.get("characterList")},s.getLength=function(){return this.getText().length},s.getDepth=function(){return this.get("depth")},s.getData=function(){return this.get("data")},s.getInlineStyleAt=function(t){var e=this.getCharacterList().get(t);return e?e.getStyle():f},s.getEntityAt=function(t){var e=this.getCharacterList().get(t);return e?e.getEntity():null},s.findStyleRanges=function(t,e){i(this.getCharacterList(),h,t,e)},s.findEntityRanges=function(t,e){i(this.getCharacterList(),d,t,e)},o}(c({key:"",type:"unstyled",text:"",characterList:a(),depth:0,data:s()}));function h(t,e){return t.getStyle()===e.getStyle()}function d(t,e){return t.getEntity()===e.getEntity()}t.exports=p},67953:(t,e,r)=>{"use strict";var n=r(4516),i=r(29407),o=r(43393),a=o.List,s=o.Map,u=o.OrderedSet,c=o.Record,l=o.Repeat,f=u(),p={parent:null,characterList:a(),data:s(),depth:0,key:"",text:"",type:"unstyled",children:a(),prevSibling:null,nextSibling:null},h=function(t,e){return t.getStyle()===e.getStyle()},d=function(t,e){return t.getEntity()===e.getEntity()},g=function(t){var e,r;function o(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:p;return t.call(this,function(t){if(!t)return t;var e=t.characterList,r=t.text;return r&&!e&&(t.characterList=a(l(n.EMPTY,r.length))),t}(e))||this}r=t,(e=o).prototype=Object.create(r.prototype),e.prototype.constructor=e,e.__proto__=r;var s=o.prototype;return s.getKey=function(){return this.get("key")},s.getType=function(){return this.get("type")},s.getText=function(){return this.get("text")},s.getCharacterList=function(){return this.get("characterList")},s.getLength=function(){return this.getText().length},s.getDepth=function(){return this.get("depth")},s.getData=function(){return this.get("data")},s.getInlineStyleAt=function(t){var e=this.getCharacterList().get(t);return e?e.getStyle():f},s.getEntityAt=function(t){var e=this.getCharacterList().get(t);return e?e.getEntity():null},s.getChildKeys=function(){return this.get("children")},s.getParentKey=function(){return this.get("parent")},s.getPrevSiblingKey=function(){return this.get("prevSibling")},s.getNextSiblingKey=function(){return this.get("nextSibling")},s.findStyleRanges=function(t,e){i(this.getCharacterList(),h,t,e)},s.findEntityRanges=function(t,e){i(this.getCharacterList(),d,t,e)},o}(c(p));t.exports=g},66912:(t,e,r)=>{"use strict";function n(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{},n=Object.keys(r);"function"==typeof Object.getOwnPropertySymbols&&(n=n.concat(Object.getOwnPropertySymbols(r).filter((function(t){return Object.getOwnPropertyDescriptor(r,t).enumerable})))),n.forEach((function(e){i(t,e,r[e])}))}return t}function i(t,e,r){return e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var o=r(10329),a=r(4516),s=r(38777),u=r(67953),c=r(82222),l=r(25110),f=r(25027),p=r(96495),h=r(68642),d=r(43393),g=r(55283),y=d.List,v=d.Record,m=d.Repeat,_=d.Map,b=d.OrderedMap,S=v({entityMap:null,blockMap:null,selectionBefore:null,selectionAfter:null}),w=h("draft_tree_data_support")?u:s,x=function(t){var e,r;function i(){return t.apply(this,arguments)||this}r=t,(e=i).prototype=Object.create(r.prototype),e.prototype.constructor=e,e.__proto__=r;var s=i.prototype;return s.getEntityMap=function(){return c},s.getBlockMap=function(){return this.get("blockMap")},s.getSelectionBefore=function(){return this.get("selectionBefore")},s.getSelectionAfter=function(){return this.get("selectionAfter")},s.getBlockForKey=function(t){return this.getBlockMap().get(t)},s.getKeyBefore=function(t){return this.getBlockMap().reverse().keySeq().skipUntil((function(e){return e===t})).skip(1).first()},s.getKeyAfter=function(t){return this.getBlockMap().keySeq().skipUntil((function(e){return e===t})).skip(1).first()},s.getBlockAfter=function(t){return this.getBlockMap().skipUntil((function(e,r){return r===t})).skip(1).first()},s.getBlockBefore=function(t){return this.getBlockMap().reverse().skipUntil((function(e,r){return r===t})).skip(1).first()},s.getBlocksAsArray=function(){return this.getBlockMap().toArray()},s.getFirstBlock=function(){return this.getBlockMap().first()},s.getLastBlock=function(){return this.getBlockMap().last()},s.getPlainText=function(t){return this.getBlockMap().map((function(t){return t?t.getText():""})).join(t||"\n")},s.getLastCreatedEntityKey=function(){return c.__getLastCreatedEntityKey()},s.hasText=function(){var t=this.getBlockMap();return t.size>1||escape(t.first().getText()).replace(/%u200B/g,"").length>0},s.createEntity=function(t,e,r){return c.__create(t,e,r),this},s.mergeEntityData=function(t,e){return c.__mergeData(t,e),this},s.replaceEntityData=function(t,e){return c.__replaceData(t,e),this},s.addEntity=function(t){return c.__add(t),this},s.getEntity=function(t){return c.__get(t)},s.getAllEntities=function(){return c.__getAll()},s.loadWithEntities=function(t){return c.__loadWithEntities(t)},i.createFromBlockArray=function(t,e){var r=Array.isArray(t)?t:t.contentBlocks,n=o.createFromArray(r),a=n.isEmpty()?new l:l.createEmpty(n.first().getKey());return new i({blockMap:n,entityMap:e||c,selectionBefore:a,selectionAfter:a})},i.createFromText=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:/\r\n?|\n/g,r=t.split(e).map((function(t){return t=g(t),new w({key:f(),text:t,type:"unstyled",characterList:y(m(a.EMPTY,t.length))})}));return i.createFromBlockArray(r)},i.fromJS=function(t){return new i(n({},t,{blockMap:b(t.blockMap).map(i.createContentBlockFromJS),selectionBefore:new l(t.selectionBefore),selectionAfter:new l(t.selectionAfter)}))},i.createContentBlockFromJS=function(t){var e=t.characterList;return new w(n({},t,{data:_(t.data),characterList:null!=e?y((Array.isArray(e)?e:p(e)).map((function(t){return a.fromJS(t)}))):void 0}))},i}(S);t.exports=x},13483:(t,e,r)=>{"use strict";var n=r(4516),i=r(43393).Map,o={add:function(t,e,r){return a(t,e,r,!0)},remove:function(t,e,r){return a(t,e,r,!1)}};function a(t,e,r,o){var a=t.getBlockMap(),s=e.getStartKey(),u=e.getStartOffset(),c=e.getEndKey(),l=e.getEndOffset(),f=a.skipUntil((function(t,e){return e===s})).takeUntil((function(t,e){return e===c})).concat(i([[c,a.get(c)]])).map((function(t,e){var i,a;s===c?(i=u,a=l):(i=e===s?u:0,a=e===c?l:t.getLength());for(var f,p=t.getCharacterList();i<a;)f=p.get(i),p=p.set(i,o?n.applyStyle(f,r):n.removeStyle(f,r)),i++;return t.set("characterList",p)}));return t.merge({blockMap:a.merge(f),selectionBefore:e,selectionAfter:e})}t.exports=o},77907:(t,e,r)=>{"use strict";function n(t,e,r){return e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var i=r(4856),o=r(69270),a=r(48083),s=r(43393),u=r(73759),c=r(22045),l=s.Map,f={subtree:!0,characterData:!0,childList:!0,characterDataOldValue:!1,attributes:!1},p=i.isBrowser("IE <= 11"),h=function(){function t(t){var e=this;n(this,"observer",void 0),n(this,"container",void 0),n(this,"mutations",void 0),n(this,"onCharData",void 0),this.container=t,this.mutations=l();var r=a(t);r.MutationObserver&&!p?this.observer=new r.MutationObserver((function(t){return e.registerMutations(t)})):this.onCharData=function(t){t.target instanceof Node||u(!1),e.registerMutation({type:"characterData",target:t.target})}}var e=t.prototype;return e.start=function(){this.observer?this.observer.observe(this.container,f):this.container.addEventListener("DOMCharacterDataModified",this.onCharData)},e.stopAndFlushMutations=function(){var t=this.observer;t?(this.registerMutations(t.takeRecords()),t.disconnect()):this.container.removeEventListener("DOMCharacterDataModified",this.onCharData);var e=this.mutations;return this.mutations=l(),e},e.registerMutations=function(t){for(var e=0;e<t.length;e++)this.registerMutation(t[e])},e.getMutationTextContent=function(t){var e=t.type,r=t.target,n=t.removedNodes;if("characterData"===e){if(""!==r.textContent)return p?r.textContent.replace("\n",""):r.textContent}else if("childList"===e){if(n&&n.length)return"";if(""!==r.textContent)return r.textContent}return null},e.registerMutation=function(t){var e=this.getMutationTextContent(t);if(null!=e){var r=c(o(t.target));this.mutations=this.mutations.set(r,e)}},t}();t.exports=h},526:(t,e,r)=>{"use strict";var n=r(99196),i=r(62620),o=(0,r(43393).Map)({"header-one":{element:"h1"},"header-two":{element:"h2"},"header-three":{element:"h3"},"header-four":{element:"h4"},"header-five":{element:"h5"},"header-six":{element:"h6"},section:{element:"section"},article:{element:"article"},"unordered-list-item":{element:"li",wrapper:n.createElement("ul",{className:i("public/DraftStyleDefault/ul")})},"ordered-list-item":{element:"li",wrapper:n.createElement("ol",{className:i("public/DraftStyleDefault/ol")})},blockquote:{element:"blockquote"},atomic:{element:"figure"},"code-block":{element:"pre",wrapper:n.createElement("pre",{className:i("public/DraftStyleDefault/pre")})},unstyled:{element:"div",aliasedElements:["p"]}});t.exports=o},37619:t=>{"use strict";t.exports={BOLD:{fontWeight:"bold"},CODE:{fontFamily:"monospace",wordWrap:"break-word"},ITALIC:{fontStyle:"italic"},STRIKETHROUGH:{textDecoration:"line-through"},UNDERLINE:{textDecoration:"underline"}}},9041:(t,e,r)=>{"use strict";var n=r(19785),i=r(10329),o=r(4516),a=r(25369),s=r(38777),u=r(66912),c=r(526),l=r(37619),f=r(87210),p=r(37898),h=r(82222),d=r(42307),g=r(39006),y=r(14289),v=r(47387),m=r(70054),_=r(41947),b=r(25110),S=r(79981),w=r(99607),x=r(25027),k=r(41714),C=r(96629),E={Editor:f,EditorBlock:p,EditorState:y,CompositeDecorator:a,Entity:h,EntityInstance:g,BlockMapBuilder:i,CharacterMetadata:o,ContentBlock:s,ContentState:u,RawDraftContentState:m,SelectionState:b,AtomicBlockUtils:n,KeyBindingUtil:v,Modifier:d,RichUtils:_,DefaultDraftBlockRenderMap:c,DefaultDraftInlineStyle:l,convertFromHTML:r(67841),convertFromRaw:w,convertToRaw:S,genKey:x,getDefaultKeyBinding:k,getVisibleSelectionRect:C};t.exports=E},87210:(t,e,r)=>{"use strict";var n=r(27418);function i(){return i=n||function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},i.apply(this,arguments)}function o(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{},n=Object.keys(r);"function"==typeof Object.getOwnPropertySymbols&&(n=n.concat(Object.getOwnPropertySymbols(r).filter((function(t){return Object.getOwnPropertyDescriptor(r,t).enumerable})))),n.forEach((function(e){s(t,e,r[e])}))}return t}function a(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function s(t,e,r){return e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function u(t,e){t.prototype=Object.create(e.prototype),t.prototype.constructor=t,t.__proto__=e}var c=r(526),l=r(37619),f=r(33418),p=r(87791),h=r(61494),d=r(19394),g=r(4083),y=r(28094),v=r(5880),m=r(14289),_=r(99196),b=r(65994),S=r(19051),w=r(4856),x=r(62620),k=r(25027),C=r(41714),E=r(79749),O=r(68642),D=r(73759),K=r(20717),T=r(22045),M=w.isBrowser("IE"),A=!M,I={edit:d,composite:f,drag:h,cut:null,render:null},B=!1,L=function(t){function e(){return t.apply(this,arguments)||this}u(e,t);var r=e.prototype;return r.render=function(){return null},r.componentDidMount=function(){this._update()},r.componentDidUpdate=function(){this._update()},r._update=function(){var t=this.props.editor;t._latestEditorState=this.props.editorState,t._blockSelectEvents=!0},e}(_.Component),R=function(t){function e(e){var r;return s(a(r=t.call(this,e)||this),"_blockSelectEvents",void 0),s(a(r),"_clipboard",void 0),s(a(r),"_handler",void 0),s(a(r),"_dragCount",void 0),s(a(r),"_internalDrag",void 0),s(a(r),"_editorKey",void 0),s(a(r),"_placeholderAccessibilityID",void 0),s(a(r),"_latestEditorState",void 0),s(a(r),"_latestCommittedEditorState",void 0),s(a(r),"_pendingStateFromBeforeInput",void 0),s(a(r),"_onBeforeInput",void 0),s(a(r),"_onBlur",void 0),s(a(r),"_onCharacterData",void 0),s(a(r),"_onCompositionEnd",void 0),s(a(r),"_onCompositionStart",void 0),s(a(r),"_onCopy",void 0),s(a(r),"_onCut",void 0),s(a(r),"_onDragEnd",void 0),s(a(r),"_onDragOver",void 0),s(a(r),"_onDragStart",void 0),s(a(r),"_onDrop",void 0),s(a(r),"_onInput",void 0),s(a(r),"_onFocus",void 0),s(a(r),"_onKeyDown",void 0),s(a(r),"_onKeyPress",void 0),s(a(r),"_onKeyUp",void 0),s(a(r),"_onMouseDown",void 0),s(a(r),"_onMouseUp",void 0),s(a(r),"_onPaste",void 0),s(a(r),"_onSelect",void 0),s(a(r),"editor",void 0),s(a(r),"editorContainer",void 0),s(a(r),"focus",void 0),s(a(r),"blur",void 0),s(a(r),"setMode",void 0),s(a(r),"exitCurrentMode",void 0),s(a(r),"restoreEditorDOM",void 0),s(a(r),"setClipboard",void 0),s(a(r),"getClipboard",void 0),s(a(r),"getEditorKey",void 0),s(a(r),"update",void 0),s(a(r),"onDragEnter",void 0),s(a(r),"onDragLeave",void 0),s(a(r),"_handleEditorContainerRef",(function(t){r.editorContainer=t,r.editor=null!==t?t.firstChild:null})),s(a(r),"focus",(function(t){var e=r.props.editorState,n=e.getSelection().getHasFocus(),i=r.editor;if(i){var o=S.getScrollParent(i),a=t||E(o),s=a.x,u=a.y;K(i)||D(!1),i.focus(),o===window?window.scrollTo(s,u):b.setTop(o,u),n||r.update(m.forceSelection(e,e.getSelection()))}})),s(a(r),"blur",(function(){var t=r.editor;t&&(K(t)||D(!1),t.blur())})),s(a(r),"setMode",(function(t){var e=r.props,n=e.onPaste,i=e.onCut,a=e.onCopy,s=o({},I.edit);n&&(s.onPaste=n),i&&(s.onCut=i),a&&(s.onCopy=a);var u=o({},I,{edit:s});r._handler=u[t]})),s(a(r),"exitCurrentMode",(function(){r.setMode("edit")})),s(a(r),"restoreEditorDOM",(function(t){r.setState({contentsKey:r.state.contentsKey+1},(function(){r.focus(t)}))})),s(a(r),"setClipboard",(function(t){r._clipboard=t})),s(a(r),"getClipboard",(function(){return r._clipboard})),s(a(r),"update",(function(t){r._latestEditorState=t,r.props.onChange(t)})),s(a(r),"onDragEnter",(function(){r._dragCount++})),s(a(r),"onDragLeave",(function(){r._dragCount--,0===r._dragCount&&r.exitCurrentMode()})),r._blockSelectEvents=!1,r._clipboard=null,r._handler=null,r._dragCount=0,r._editorKey=e.editorKey||k(),r._placeholderAccessibilityID="placeholder-"+r._editorKey,r._latestEditorState=e.editorState,r._latestCommittedEditorState=e.editorState,r._onBeforeInput=r._buildHandler("onBeforeInput"),r._onBlur=r._buildHandler("onBlur"),r._onCharacterData=r._buildHandler("onCharacterData"),r._onCompositionEnd=r._buildHandler("onCompositionEnd"),r._onCompositionStart=r._buildHandler("onCompositionStart"),r._onCopy=r._buildHandler("onCopy"),r._onCut=r._buildHandler("onCut"),r._onDragEnd=r._buildHandler("onDragEnd"),r._onDragOver=r._buildHandler("onDragOver"),r._onDragStart=r._buildHandler("onDragStart"),r._onDrop=r._buildHandler("onDrop"),r._onInput=r._buildHandler("onInput"),r._onFocus=r._buildHandler("onFocus"),r._onKeyDown=r._buildHandler("onKeyDown"),r._onKeyPress=r._buildHandler("onKeyPress"),r._onKeyUp=r._buildHandler("onKeyUp"),r._onMouseDown=r._buildHandler("onMouseDown"),r._onMouseUp=r._buildHandler("onMouseUp"),r._onPaste=r._buildHandler("onPaste"),r._onSelect=r._buildHandler("onSelect"),r.getEditorKey=function(){return r._editorKey},r.state={contentsKey:0},r}u(e,t);var n=e.prototype;return n._buildHandler=function(t){var e=this;return function(r){if(!e.props.readOnly){var n=e._handler&&e._handler[t];n&&(g?g((function(){return n(e,r)})):n(e,r))}}},n._showPlaceholder=function(){return!!this.props.placeholder&&!this.props.editorState.isInCompositionMode()&&!this.props.editorState.getCurrentContent().hasText()},n._renderPlaceholder=function(){if(this._showPlaceholder()){var t={text:T(this.props.placeholder),editorState:this.props.editorState,textAlignment:this.props.textAlignment,accessibilityID:this._placeholderAccessibilityID};return _.createElement(y,t)}return null},n._renderARIADescribedBy=function(){var t=this.props.ariaDescribedBy||"",e=this._showPlaceholder()?this._placeholderAccessibilityID:"";return t.replace("{{editor_id_placeholder}}",e)||void 0},n.render=function(){var t=this.props,e=t.blockRenderMap,r=t.blockRendererFn,n=t.blockStyleFn,a=t.customStyleFn,s=t.customStyleMap,u=t.editorState,c=t.preventScroll,f=t.readOnly,h=t.textAlignment,d=t.textDirectionality,g=x({"DraftEditor/root":!0,"DraftEditor/alignLeft":"left"===h,"DraftEditor/alignRight":"right"===h,"DraftEditor/alignCenter":"center"===h}),y=this.props.role||"textbox",v="combobox"===y?!!this.props.ariaExpanded:null,m={blockRenderMap:e,blockRendererFn:r,blockStyleFn:n,customStyleMap:o({},l,s),customStyleFn:a,editorKey:this._editorKey,editorState:u,preventScroll:c,textDirectionality:d};return _.createElement("div",{className:g},this._renderPlaceholder(),_.createElement("div",{className:x("DraftEditor/editorContainer"),ref:this._handleEditorContainerRef},_.createElement("div",{"aria-activedescendant":f?null:this.props.ariaActiveDescendantID,"aria-autocomplete":f?null:this.props.ariaAutoComplete,"aria-controls":f?null:this.props.ariaControls,"aria-describedby":this._renderARIADescribedBy(),"aria-expanded":f?null:v,"aria-label":this.props.ariaLabel,"aria-labelledby":this.props.ariaLabelledBy,"aria-multiline":this.props.ariaMultiline,"aria-owns":f?null:this.props.ariaOwneeID,autoCapitalize:this.props.autoCapitalize,autoComplete:this.props.autoComplete,autoCorrect:this.props.autoCorrect,className:x({notranslate:!f,"public/DraftEditor/content":!0}),contentEditable:!f,"data-testid":this.props.webDriverTestID,onBeforeInput:this._onBeforeInput,onBlur:this._onBlur,onCompositionEnd:this._onCompositionEnd,onCompositionStart:this._onCompositionStart,onCopy:this._onCopy,onCut:this._onCut,onDragEnd:this._onDragEnd,onDragEnter:this.onDragEnter,onDragLeave:this.onDragLeave,onDragOver:this._onDragOver,onDragStart:this._onDragStart,onDrop:this._onDrop,onFocus:this._onFocus,onInput:this._onInput,onKeyDown:this._onKeyDown,onKeyPress:this._onKeyPress,onKeyUp:this._onKeyUp,onMouseUp:this._onMouseUp,onPaste:this._onPaste,onSelect:this._onSelect,ref:this.props.editorRef,role:f?null:y,spellCheck:A&&this.props.spellCheck,style:{outline:"none",userSelect:"text",WebkitUserSelect:"text",whiteSpace:"pre-wrap",wordWrap:"break-word"},suppressContentEditableWarning:!0,tabIndex:this.props.tabIndex},_.createElement(L,{editor:this,editorState:u}),_.createElement(p,i({},m,{key:"contents"+this.state.contentsKey})))))},n.componentDidMount=function(){this._blockSelectEvents=!1,!B&&O("draft_ods_enabled")&&(B=!0,v.initODS()),this.setMode("edit"),M&&(this.editor?this.editor.ownerDocument.execCommand("AutoUrlDetect",!1,!1):r.g.execCommand("AutoUrlDetect",!1,!1))},n.componentDidUpdate=function(){this._blockSelectEvents=!1,this._latestEditorState=this.props.editorState,this._latestCommittedEditorState=this.props.editorState},e}(_.Component);s(R,"defaultProps",{ariaDescribedBy:"{{editor_id_placeholder}}",blockRenderMap:c,blockRendererFn:function(){return null},blockStyleFn:function(){return""},keyBindingFn:C,readOnly:!1,spellCheck:!1,stripPastedStyles:!1}),t.exports=R},37898:(t,e,r)=>{"use strict";var n=r(27418);function i(){return i=n||function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},i.apply(this,arguments)}var o=r(42282),a=r(22146),s=r(99196),u=r(65994),c=r(19051),l=r(54191),f=r(16633),p=r(62620),h=r(55258),d=r(79749),g=r(70746),y=r(73759),v=r(20717),m=r(22045),_=function(t,e){return t.getAnchorKey()===e||t.getFocusKey()===e},b=function(t){var e,r;function n(){for(var e,r,n,i,o=arguments.length,a=new Array(o),s=0;s<o;s++)a[s]=arguments[s];return i=void 0,(n="_node")in(r=function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(e=t.call.apply(t,[this].concat(a))||this))?Object.defineProperty(r,n,{value:i,enumerable:!0,configurable:!0,writable:!0}):r[n]=i,e}r=t,(e=n).prototype=Object.create(r.prototype),e.prototype.constructor=e,e.__proto__=r;var b=n.prototype;return b.shouldComponentUpdate=function(t){return this.props.block!==t.block||this.props.tree!==t.tree||this.props.direction!==t.direction||_(t.selection,t.block.getKey())&&t.forceSelection},b.componentDidMount=function(){if(!this.props.preventScroll){var t=this.props.selection,e=t.getEndKey();if(t.getHasFocus()&&e===this.props.block.getKey()){var r=this._node;if(null!=r){var n,i=c.getScrollParent(r),o=d(i);if(i===window){var a=h(r);(n=a.y+a.height-g().height)>0&&window.scrollTo(o.x,o.y+n+10)}else v(r)||y(!1),(n=r.offsetHeight+r.offsetTop-(i.offsetTop+i.offsetHeight+o.y))>0&&u.setTop(i,u.getTop(i)+n+10)}}}},b._renderChildren=function(){var t=this,e=this.props.block,r=e.getKey(),n=e.getText(),u=this.props.tree.size-1,c=_(this.props.selection,r);return this.props.tree.map((function(p,h){var d=p.get("leaves");if(0===d.size)return null;var g=d.size-1,y=d.map((function(i,l){var f=a.encode(r,h,l),p=i.get("start"),d=i.get("end");return s.createElement(o,{key:f,offsetKey:f,block:e,start:p,selection:c?t.props.selection:null,forceSelection:t.props.forceSelection,text:n.slice(p,d),styleSet:e.getInlineStyleAt(p),customStyleMap:t.props.customStyleMap,customStyleFn:t.props.customStyleFn,isLast:h===u&&l===g})})).toArray(),v=p.get("decoratorKey");if(null==v)return y;if(!t.props.decorator)return y;var _=m(t.props.decorator),b=_.getComponentForKey(v);if(!b)return y;var S=_.getPropsForKey(v),w=a.encode(r,h,0),x=d.first().get("start"),k=d.last().get("end"),C=n.slice(x,k),E=e.getEntityAt(p.get("start")),O=f.getHTMLDirIfDifferent(l.getDirection(C),t.props.direction),D={contentState:t.props.contentState,decoratedText:C,dir:O,start:x,end:k,blockKey:r,entityKey:E,offsetKey:w};return s.createElement(b,i({},S,D,{key:w}),y)})).toArray()},b.render=function(){var t=this,e=this.props,r=e.direction,n=e.offsetKey,i=p({"public/DraftStyleDefault/block":!0,"public/DraftStyleDefault/ltr":"LTR"===r,"public/DraftStyleDefault/rtl":"RTL"===r});return s.createElement("div",{"data-offset-key":n,className:i,ref:function(e){return t._node=e}},this._renderChildren())},n}(s.Component);t.exports=b},25821:(t,e,r)=>{"use strict";var n=r(27418);function i(){return i=n||function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},i.apply(this,arguments)}function o(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{},n=Object.keys(r);"function"==typeof Object.getOwnPropertySymbols&&(n=n.concat(Object.getOwnPropertySymbols(r).filter((function(t){return Object.getOwnPropertyDescriptor(r,t).enumerable})))),n.forEach((function(e){a(t,e,r[e])}))}return t}function a(t,e,r){return e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var s=r(59513),u=r(22146),c=r(99196),l=r(65994),f=r(19051),p=r(55258),h=r(79749),d=r(70746),g=r(43393),y=r(73759),v=r(20717),m=(g.List,function(t,e){return t.getAnchorKey()===e||t.getFocusKey()===e}),_=function(t,e){var r=e.get(t.getType())||e.get("unstyled"),n=r.wrapper;return{Element:r.element||e.get("unstyled").element,wrapperTemplate:n}},b=function(t,e){var r=e(t);return r?{CustomComponent:r.component,customProps:r.props,customEditable:r.editable}:{}},S=function(t,e,r,n,i,a){var s={"data-block":!0,"data-editor":e,"data-offset-key":r,key:t.getKey(),ref:a},u=n(t);return u&&(s.className=u),void 0!==i.customEditable&&(s=o({},s,{contentEditable:i.customEditable,suppressContentEditableWarning:!0})),s},w=function(t){var e,r;function n(){for(var e,r=arguments.length,n=new Array(r),i=0;i<r;i++)n[i]=arguments[i];return a(function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(e=t.call.apply(t,[this].concat(n))||this),"wrapperRef",c.createRef()),e}r=t,(e=n).prototype=Object.create(r.prototype),e.prototype.constructor=e,e.__proto__=r;var g=n.prototype;return g.shouldComponentUpdate=function(t){var e=this.props,r=e.block,n=e.direction,i=e.tree,o=!r.getChildKeys().isEmpty(),a=r!==t.block||i!==t.tree||n!==t.direction||m(t.selection,t.block.getKey())&&t.forceSelection;return o||a},g.componentDidMount=function(){var t=this.props.selection,e=t.getEndKey();if(t.getHasFocus()&&e===this.props.block.getKey()){var r=this.wrapperRef.current;if(r){var n,i=f.getScrollParent(r),o=h(i);if(i===window){var a=p(r);(n=a.y+a.height-d().height)>0&&window.scrollTo(o.x,o.y+n+10)}else{v(r)||y(!1);var s=r;(n=s.offsetHeight+s.offsetTop-(i.offsetHeight+o.y))>0&&l.setTop(i,l.getTop(i)+n+10)}}}},g.render=function(){var t=this,e=this.props,r=e.block,a=e.blockRenderMap,l=e.blockRendererFn,f=e.blockStyleFn,p=e.contentState,h=e.decorator,d=e.editorKey,g=e.editorState,y=e.customStyleFn,v=e.customStyleMap,w=e.direction,x=e.forceSelection,k=e.selection,C=e.tree,E=null;r.children.size&&(E=r.children.reduce((function(e,r){var i=u.encode(r,0,0),s=p.getBlockForKey(r),h=b(s,l),y=h.CustomComponent||n,v=_(s,a),m=v.Element,w=v.wrapperTemplate,x=S(s,d,i,f,h,null),k=o({},t.props,{tree:g.getBlockTree(r),blockProps:h.customProps,offsetKey:i,block:s});return e.push(c.createElement(m,x,c.createElement(y,k))),!w||function(t,e){var r=t.getNextSiblingKey();return!!r&&e.getBlockForKey(r).getType()===t.getType()}(s,p)||function(t,e,r){var n=[],i=!0,o=!1,a=void 0;try{for(var s,l=r.reverse()[Symbol.iterator]();!(i=(s=l.next()).done);i=!0){var f=s.value;if(f.type!==e)break;n.push(f)}}catch(t){o=!0,a=t}finally{try{i||null==l.return||l.return()}finally{if(o)throw a}}r.splice(r.indexOf(n[0]),n.length+1);var p=n.reverse(),h=p[0].key;r.push(c.cloneElement(t,{key:"".concat(h,"-wrap"),"data-offset-key":u.encode(h,0,0)},p))}(w,m,e),e}),[]));var O=r.getKey(),D=u.encode(O,0,0),K=b(r,l),T=K.CustomComponent,M=null!=T?c.createElement(T,i({},this.props,{tree:g.getBlockTree(O),blockProps:K.customProps,offsetKey:D,block:r})):c.createElement(s,{block:r,children:E,contentState:p,customStyleFn:y,customStyleMap:v,decorator:h,direction:w,forceSelection:x,hasSelection:m(k,O),selection:k,tree:C});if(r.getParentKey())return M;var A=_(r,a).Element,I=S(r,d,D,f,K,this.wrapperRef);return c.createElement(A,I,M)},n}(c.Component);t.exports=w},33418:(t,e,r)=>{"use strict";var n=r(77907),i=r(42307),o=r(22146),a=r(14289),s=r(25399),u=r(4856),c=r(14507),l=r(84907),f=r(1244),p=r(42128),h=r(22045),d=u.isBrowser("IE"),g=!1,y=!1,v=null,m={onCompositionStart:function(t){y=!0,function(t){v||(v=new n(l(t))).start()}(t)},onCompositionEnd:function(t){g=!1,y=!1,setTimeout((function(){g||m.resolveComposition(t)}),20)},onSelect:c,onKeyDown:function(t,e){if(!y)return m.resolveComposition(t),void t._onKeyDown(e);e.which!==s.RIGHT&&e.which!==s.LEFT||e.preventDefault()},onKeyPress:function(t,e){e.which===s.RETURN&&e.preventDefault()},resolveComposition:function(t){if(!y){var e=h(v).stopAndFlushMutations();v=null,g=!0;var r=a.set(t._latestEditorState,{inCompositionMode:!1});if(t.exitCurrentMode(),e.size){var n=r.getCurrentContent();e.forEach((function(t,e){var s=o.decode(e),u=s.blockKey,c=s.decoratorKey,l=s.leafKey,f=r.getBlockTree(u).getIn([c,"leaves",l]),h=f.start,d=f.end,g=r.getSelection().merge({anchorKey:u,focusKey:u,anchorOffset:h,focusOffset:d,isBackward:!1}),y=p(n,g),v=n.getBlockForKey(u).getInlineStyleAt(h);n=i.replaceText(n,g,t,v,y),r=a.set(r,{currentContent:n})}));var s=f(r,l(t)).selectionState;t.restoreEditorDOM();var u=d?a.forceSelection(r,s):a.acceptSelection(r,s);t.update(a.push(u,n,"insert-characters"))}else t.update(r)}}};t.exports=m},88795:(t,e,r)=>{"use strict";var n=r(27418);function i(){return i=n||function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},i.apply(this,arguments)}function o(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{},n=Object.keys(r);"function"==typeof Object.getOwnPropertySymbols&&(n=n.concat(Object.getOwnPropertySymbols(r).filter((function(t){return Object.getOwnPropertyDescriptor(r,t).enumerable})))),n.forEach((function(e){a(t,e,r[e])}))}return t}function a(t,e,r){return e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var s=r(37898),u=r(22146),c=r(99196),l=r(62620),f=r(71108),p=r(22045),h=function(t,e,r,n){return l({"public/DraftStyleDefault/unorderedListItem":"unordered-list-item"===t,"public/DraftStyleDefault/orderedListItem":"ordered-list-item"===t,"public/DraftStyleDefault/reset":r,"public/DraftStyleDefault/depth0":0===e,"public/DraftStyleDefault/depth1":1===e,"public/DraftStyleDefault/depth2":2===e,"public/DraftStyleDefault/depth3":3===e,"public/DraftStyleDefault/depth4":e>=4,"public/DraftStyleDefault/listLTR":"LTR"===n,"public/DraftStyleDefault/listRTL":"RTL"===n})},d=function(t){var e,r;function n(){return t.apply(this,arguments)||this}r=t,(e=n).prototype=Object.create(r.prototype),e.prototype.constructor=e,e.__proto__=r;var a=n.prototype;return a.shouldComponentUpdate=function(t){var e=this.props.editorState,r=t.editorState;if(e.getDirectionMap()!==r.getDirectionMap())return!0;if(e.getSelection().getHasFocus()!==r.getSelection().getHasFocus())return!0;var n=r.getNativelyRenderedContent(),i=e.isInCompositionMode(),o=r.isInCompositionMode();if(e===r||null!==n&&r.getCurrentContent()===n||i&&o)return!1;var a=e.getCurrentContent(),s=r.getCurrentContent(),u=e.getDecorator(),c=r.getDecorator();return i!==o||a!==s||u!==c||r.mustForceSelection()},a.render=function(){for(var t=this.props,e=t.blockRenderMap,r=t.blockRendererFn,n=t.blockStyleFn,a=t.customStyleMap,l=t.customStyleFn,d=t.editorState,g=t.editorKey,y=t.preventScroll,v=t.textDirectionality,m=d.getCurrentContent(),_=d.getSelection(),b=d.mustForceSelection(),S=d.getDecorator(),w=p(d.getDirectionMap()),x=m.getBlocksAsArray(),k=[],C=null,E=null,O=0;O<x.length;O++){var D=x[O],K=D.getKey(),T=D.getType(),M=r(D),A=void 0,I=void 0,B=void 0;M&&(A=M.component,I=M.props,B=M.editable);var L=v||w.get(K),R=u.encode(K,0,0),N={contentState:m,block:D,blockProps:I,blockStyleFn:n,customStyleMap:a,customStyleFn:l,decorator:S,direction:L,forceSelection:b,offsetKey:R,preventScroll:y,selection:_,tree:d.getBlockTree(K)},F=e.get(T)||e.get("unstyled"),P=F.wrapper,z=F.element||e.get("unstyled").element,j=D.getDepth(),U="";n&&(U=n(D)),"li"===z&&(U=f(U,h(T,j,E!==P||null===C||j>C,L)));var q=A||s,H={className:U,"data-block":!0,"data-editor":g,"data-offset-key":R,key:K};void 0!==B&&(H=o({},H,{contentEditable:B,suppressContentEditableWarning:!0}));var W=c.createElement(z,H,c.createElement(q,i({},N,{key:K})));k.push({block:W,wrapperTemplate:P,key:K,offsetKey:R}),C=P?D.getDepth():null,E=P}for(var V=[],G=0;G<k.length;){var J=k[G];if(J.wrapperTemplate){var X=[];do{X.push(k[G].block),G++}while(G<k.length&&k[G].wrapperTemplate===J.wrapperTemplate);var Y=c.cloneElement(J.wrapperTemplate,{key:J.key+"-wrap","data-offset-key":J.offsetKey},X);V.push(Y)}else V.push(J.block),G++}return c.createElement("div",{"data-contents":"true"},V)},n}(c.Component);t.exports=d},87791:(t,e,r)=>{"use strict";var n=r(68642)("draft_tree_data_support");t.exports=r(n?69459:88795)},69459:(t,e,r)=>{"use strict";var n=r(27418);function i(){return i=n||function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},i.apply(this,arguments)}var o=r(25821),a=r(22146),s=r(99196),u=r(22045),c=function(t){var e,r;function n(){return t.apply(this,arguments)||this}r=t,(e=n).prototype=Object.create(r.prototype),e.prototype.constructor=e,e.__proto__=r;var c=n.prototype;return c.shouldComponentUpdate=function(t){var e=this.props.editorState,r=t.editorState;if(e.getDirectionMap()!==r.getDirectionMap())return!0;if(e.getSelection().getHasFocus()!==r.getSelection().getHasFocus())return!0;var n=r.getNativelyRenderedContent(),i=e.isInCompositionMode(),o=r.isInCompositionMode();if(e===r||null!==n&&r.getCurrentContent()===n||i&&o)return!1;var a=e.getCurrentContent(),s=r.getCurrentContent(),u=e.getDecorator(),c=r.getDecorator();return i!==o||a!==s||u!==c||r.mustForceSelection()},c.render=function(){for(var t=this.props,e=t.blockRenderMap,r=t.blockRendererFn,n=t.blockStyleFn,c=t.customStyleMap,l=t.customStyleFn,f=t.editorState,p=t.editorKey,h=t.textDirectionality,d=f.getCurrentContent(),g=f.getSelection(),y=f.mustForceSelection(),v=f.getDecorator(),m=u(f.getDirectionMap()),_=[],b=d.getBlocksAsArray()[0];b;){var S=b.getKey(),w={blockRenderMap:e,blockRendererFn:r,blockStyleFn:n,contentState:d,customStyleFn:l,customStyleMap:c,decorator:v,editorKey:p,editorState:f,forceSelection:y,selection:g,block:b,direction:h||m.get(S),tree:f.getBlockTree(S)},x=(e.get(b.getType())||e.get("unstyled")).wrapper;_.push({block:s.createElement(o,i({key:S},w)),wrapperTemplate:x,key:S,offsetKey:a.encode(S,0,0)});var k=b.getNextSiblingKey();b=k?d.getBlockForKey(k):null}for(var C=[],E=0;E<_.length;){var O=_[E];if(O.wrapperTemplate){var D=[];do{D.push(_[E].block),E++}while(E<_.length&&_[E].wrapperTemplate===O.wrapperTemplate);var K=s.cloneElement(O.wrapperTemplate,{key:O.key+"-wrap","data-offset-key":O.offsetKey},D);C.push(K)}else C.push(O.block),E++}return s.createElement("div",{"data-contents":"true"},C)},n}(s.Component);t.exports=c},3259:(t,e,r)=>{"use strict";var n=r(27418);function i(){return i=n||function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},i.apply(this,arguments)}var o=r(22146),a=r(99196),s=r(54191),u=r(16633),c=function(t){var e,r;function n(){return t.apply(this,arguments)||this}return r=t,(e=n).prototype=Object.create(r.prototype),e.prototype.constructor=e,e.__proto__=r,n.prototype.render=function(){var t=this.props,e=t.block,r=t.children,n=t.contentState,c=t.decorator,l=t.decoratorKey,f=t.direction,p=t.leafSet,h=t.text,d=e.getKey(),g=p.get("leaves"),y=c.getComponentForKey(l),v=c.getPropsForKey(l),m=o.encode(d,parseInt(l,10),0),_=h.slice(g.first().get("start"),g.last().get("end")),b=u.getHTMLDirIfDifferent(s.getDirection(_),f);return a.createElement(y,i({},v,{contentState:n,decoratedText:_,dir:b,key:m,entityKey:e.getEntityAt(p.get("start")),offsetKey:m}),r)},n}(a.Component);t.exports=c},61494:(t,e,r)=>{"use strict";var n=r(44891),i=r(42307),o=r(14289),a=r(69270),s=r(75795),u=r(21738),c=r(94486),l=r(48083),f=r(42177),p=r(22045),h={onDragEnd:function(t){t.exitCurrentMode(),d(t)},onDrop:function(t,e){var r=new n(e.nativeEvent.dataTransfer),l=t._latestEditorState,h=function(t,e){var r=null,n=null,i=s(t.currentTarget);if("function"==typeof i.caretRangeFromPoint){var o=i.caretRangeFromPoint(t.x,t.y);r=o.startContainer,n=o.startOffset}else{if(!t.rangeParent)return null;r=t.rangeParent,n=t.rangeOffset}r=p(r),n=p(n);var u=p(a(r));return c(e,u,n,u,n)}(e.nativeEvent,l);if(e.preventDefault(),t._dragCount=0,t.exitCurrentMode(),null!=h){var y=r.getFiles();if(y.length>0){if(t.props.handleDroppedFiles&&f(t.props.handleDroppedFiles(h,y)))return;u(y,(function(e){e&&t.update(g(l,h,e))}))}else{var v=t._internalDrag?"internal":"external";t.props.handleDrop&&f(t.props.handleDrop(h,r,v))||(t._internalDrag?t.update(function(t,e){var r=i.moveText(t.getCurrentContent(),t.getSelection(),e);return o.push(t,r,"insert-fragment")}(l,h)):t.update(g(l,h,r.getText()))),d(t)}}}};function d(t){t._internalDrag=!1;var e=t.editorContainer;if(e){var r=new MouseEvent("mouseup",{view:l(e),bubbles:!0,cancelable:!0});e.dispatchEvent(r)}}function g(t,e,r){var n=i.insertText(t.getCurrentContent(),e,r,t.getCurrentInlineStyle());return o.push(t,n,"insert-fragment")}t.exports=h},19394:(t,e,r)=>{"use strict";var n=r(4856),i=r(26396),o=r(43421),a=r(6155),s=r(69328),u=r(73935),c=r(39499),l=r(80981),f=r(62186),p=r(29971),h=r(46397),d=r(6089),g=r(14507),y=n.isBrowser("Chrome"),v=n.isBrowser("Firefox"),m=y||v?g:function(t){},_={onBeforeInput:i,onBlur:o,onCompositionStart:a,onCopy:s,onCut:u,onDragOver:c,onDragStart:l,onFocus:f,onInput:p,onKeyDown:h,onPaste:d,onSelect:g,onMouseUp:m,onKeyUp:m};t.exports=_},4083:(t,e,r)=>{"use strict";var n=r(91850).unstable_flushControlled;t.exports=n},42282:(t,e,r)=>{"use strict";var n=r(27418),i=r(80052),o=r(99196),a=r(73759),s=r(16581),u=r(45412).setDraftEditorSelection,c=function(t){var e,r;function c(){for(var e,r,n,i,o=arguments.length,a=new Array(o),s=0;s<o;s++)a[s]=arguments[s];return i=void 0,(n="leaf")in(r=function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(e=t.call.apply(t,[this].concat(a))||this))?Object.defineProperty(r,n,{value:i,enumerable:!0,configurable:!0,writable:!0}):r[n]=i,e}r=t,(e=c).prototype=Object.create(r.prototype),e.prototype.constructor=e,e.__proto__=r;var l=c.prototype;return l._setSelection=function(){var t=this.props.selection;if(null!=t&&t.getHasFocus()){var e=this.props,r=e.block,n=e.start,i=e.text,o=r.getKey(),c=n+i.length;if(t.hasEdgeWithin(o,n,c)){var l=this.leaf;l||a(!1);var f,p=l.firstChild;p||a(!1),p.nodeType===Node.TEXT_NODE?f=p:s(p)?f=l:(f=p.firstChild)||a(!1),u(t,f,o,n,c)}}},l.shouldComponentUpdate=function(t){var e=this.leaf;return e||a(!1),e.textContent!==t.text||t.styleSet!==this.props.styleSet||t.forceSelection},l.componentDidUpdate=function(){this._setSelection()},l.componentDidMount=function(){this._setSelection()},l.render=function(){var t=this,e=this.props.block,r=this.props.text;r.endsWith("\n")&&this.props.isLast&&(r+="\n");var a=this.props,s=a.customStyleMap,u=a.customStyleFn,c=a.offsetKey,l=a.styleSet,f=l.reduce((function(t,e){var r={},i=s[e];return void 0!==i&&t.textDecoration!==i.textDecoration&&(r.textDecoration=[t.textDecoration,i.textDecoration].join(" ").trim()),n(t,i,r)}),{});if(u){var p=u(l,e);f=n(f,p)}return o.createElement("span",{"data-offset-key":c,ref:function(e){return t.leaf=e},style:f},o.createElement(i,null,r))},c}(o.Component);t.exports=c},59513:(t,e,r)=>{"use strict";var n=r(3259),i=r(42282),o=r(22146),a=r(43393),s=r(99196),u=r(62620),c=(a.List,function(t){var e,r;function a(){return t.apply(this,arguments)||this}return r=t,(e=a).prototype=Object.create(r.prototype),e.prototype.constructor=e,e.__proto__=r,a.prototype.render=function(){var t=this.props,e=t.block,r=t.contentState,a=t.customStyleFn,c=t.customStyleMap,l=t.decorator,f=t.direction,p=t.forceSelection,h=t.hasSelection,d=t.selection,g=t.tree,y=e.getKey(),v=e.getText(),m=g.size-1,_=this.props.children||g.map((function(t,u){var g=t.get("decoratorKey"),_=t.get("leaves"),b=_.size-1,S=_.map((function(t,r){var n=o.encode(y,u,r),l=t.get("start"),f=t.get("end");return s.createElement(i,{key:n,offsetKey:n,block:e,start:l,selection:h?d:null,forceSelection:p,text:v.slice(l,f),styleSet:e.getInlineStyleAt(l),customStyleMap:c,customStyleFn:a,isLast:g===m&&r===b})})).toArray();return g&&l?s.createElement(n,{block:e,children:S,contentState:r,decorator:l,decoratorKey:g,direction:f,leafSet:t,text:v,key:u}):S})).toArray();return s.createElement("div",{"data-offset-key":o.encode(y,0,0),className:u({"public/DraftStyleDefault/block":!0,"public/DraftStyleDefault/ltr":"LTR"===f,"public/DraftStyleDefault/rtl":"RTL"===f})},_)},a}(s.Component));t.exports=c},28094:(t,e,r)=>{"use strict";var n=r(99196),i=r(62620),o=function(t){var e,r;function o(){return t.apply(this,arguments)||this}r=t,(e=o).prototype=Object.create(r.prototype),e.prototype.constructor=e,e.__proto__=r;var a=o.prototype;return a.shouldComponentUpdate=function(t){return this.props.text!==t.text||this.props.editorState.getSelection().getHasFocus()!==t.editorState.getSelection().getHasFocus()},a.render=function(){var t=this.props.editorState.getSelection().getHasFocus(),e=i({"public/DraftEditorPlaceholder/root":!0,"public/DraftEditorPlaceholder/hasFocus":t});return n.createElement("div",{className:e},n.createElement("div",{className:i("public/DraftEditorPlaceholder/inner"),id:this.props.accessibilityID,style:{whiteSpace:"pre-wrap"}},this.props.text))},o}(n.Component);t.exports=o},80052:(t,e,r)=>{"use strict";function n(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function i(t,e,r){return e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var o=r(99196),a=r(4856),s=r(73759),u=r(84368),c=a.isBrowser("IE <= 11"),l=function(t){var e,r;function a(e){var r;return i(n(r=t.call(this,e)||this),"_forceFlag",void 0),i(n(r),"_node",void 0),r._forceFlag=!1,r}r=t,(e=a).prototype=Object.create(r.prototype),e.prototype.constructor=e,e.__proto__=r;var l=a.prototype;return l.shouldComponentUpdate=function(t){var e=this._node,r=""===t.children;u(e)||s(!1);var n=e;return r?!function(t){return c?"\n"===t.textContent:"BR"===t.tagName}(n):n.textContent!==t.children},l.componentDidMount=function(){this._forceFlag=!this._forceFlag},l.componentDidUpdate=function(){this._forceFlag=!this._forceFlag},l.render=function(){var t,e=this;return""===this.props.children?this._forceFlag?(t=function(t){return e._node=t},c?o.createElement("span",{key:"A","data-text":"true",ref:t},"\n"):o.createElement("br",{key:"A","data-text":"true",ref:t})):function(t){return c?o.createElement("span",{key:"B","data-text":"true",ref:t},"\n"):o.createElement("br",{key:"B","data-text":"true",ref:t})}((function(t){return e._node=t})):o.createElement("span",{key:this._forceFlag?"A":"B","data-text":"true",ref:function(t){return e._node=t}},this.props.children)},a}(o.Component);t.exports=l},5880:t=>{"use strict";t.exports={initODS:function(){},handleExtensionCausedError:function(){}}},82222:(t,e,r)=>{"use strict";function n(t,e,r){return e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var i=r(39006),o=r(43393),a=r(73759),s=r(76363),u=(0,o.Map)(),c=s();function l(t,e){console.warn("WARNING: "+t+' will be deprecated soon!\nPlease use "'+e+'" instead.')}var f={getLastCreatedEntityKey:function(){return l("DraftEntity.getLastCreatedEntityKey","contentState.getLastCreatedEntityKey"),f.__getLastCreatedEntityKey()},create:function(t,e,r){return l("DraftEntity.create","contentState.createEntity"),f.__create(t,e,r)},add:function(t){return l("DraftEntity.add","contentState.addEntity"),f.__add(t)},get:function(t){return l("DraftEntity.get","contentState.getEntity"),f.__get(t)},__getAll:function(){return u},__loadWithEntities:function(t){u=t,c=s()},mergeData:function(t,e){return l("DraftEntity.mergeData","contentState.mergeEntityData"),f.__mergeData(t,e)},replaceData:function(t,e){return l("DraftEntity.replaceData","contentState.replaceEntityData"),f.__replaceData(t,e)},__getLastCreatedEntityKey:function(){return c},__create:function(t,e,r){return f.__add(new i({type:t,mutability:e,data:r||{}}))},__add:function(t){return c=s(),u=u.set(c,t),c},__get:function(t){var e=u.get(t);return e||a(!1),e},__mergeData:function(t,e){var r=f.__get(t),i=function(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{},i=Object.keys(r);"function"==typeof Object.getOwnPropertySymbols&&(i=i.concat(Object.getOwnPropertySymbols(r).filter((function(t){return Object.getOwnPropertyDescriptor(r,t).enumerable})))),i.forEach((function(e){n(t,e,r[e])}))}return t}({},r.getData(),e),o=r.set("data",i);return u=u.set(t,o),o},__replaceData:function(t,e){var r=f.__get(t).set("data",e);return u=u.set(t,r),r}};t.exports=f},39006:(t,e,r)=>{"use strict";var n=function(t){var e,r;function n(){return t.apply(this,arguments)||this}r=t,(e=n).prototype=Object.create(r.prototype),e.prototype.constructor=e,e.__proto__=r;var i=n.prototype;return i.getType=function(){return this.get("type")},i.getMutability=function(){return this.get("mutability")},i.getData=function(){return this.get("data")},n}((0,r(43393).Record)({type:"TOKEN",mutability:"IMMUTABLE",data:Object}));t.exports=n},5195:t=>{"use strict";t.exports={getRemovalRange:function(t,e,r,n,i){var o=r.split(" ");o=o.map((function(t,e){if("forward"===i){if(e>0)return" "+t}else if(e<o.length-1)return t+" ";return t}));for(var a,s=n,u=null,c=null,l=0;l<o.length;l++){if(t<(a=s+o[l].length)&&s<e)null!==u||(u=s),c=a;else if(null!==u)break;s=a}var f=n+r.length,p=u===n,h=c===f;return(!p&&h||p&&!h)&&("forward"===i?c!==f&&c++:u!==n&&u--),{start:u,end:c}}}},97432:t=>{"use strict";t.exports={logBlockedSelectionEvent:function(){return null},logSelectionStateFailure:function(){return null}}},42307:(t,e,r)=>{"use strict";var n=r(4516),i=r(13483),o=r(68750),a=r(81446),s=r(88687),u=r(43393),c=r(54542),l=r(18467),f=r(73759),p=r(57429),h=r(14017),d=r(54879),g=r(36043),y=u.OrderedSet,v={replaceText:function(t,e,r,i,o){var a=h(t,e),s=d(a,e),u=n.create({style:i||y(),entity:o||null});return l(s,s.getSelectionAfter(),r,u)},insertText:function(t,e,r,n,i){return e.isCollapsed()||f(!1),v.replaceText(t,e,r,n,i)},moveText:function(t,e,r){var n=s(t,e),i=v.removeRange(t,e,"backward");return v.replaceWithFragment(i,r,n)},replaceWithFragment:function(t,e,r){var n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"REPLACE_WITH_NEW_DATA",i=h(t,e),o=d(i,e);return c(o,o.getSelectionAfter(),r,n)},removeRange:function(t,e,r){var n,i,o,s;e.getIsBackward()&&(e=e.merge({anchorKey:e.getFocusKey(),anchorOffset:e.getFocusOffset(),focusKey:e.getAnchorKey(),focusOffset:e.getAnchorOffset(),isBackward:!1})),n=e.getAnchorKey(),i=e.getFocusKey(),o=t.getBlockForKey(n),s=t.getBlockForKey(i);var u=e.getStartOffset(),c=e.getEndOffset(),l=o.getEntityAt(u),f=s.getEntityAt(c-1);if(n===i&&l&&l===f){var p=a(t.getEntityMap(),o,s,e,r);return d(t,p)}var g=h(t,e);return d(g,e)},splitBlock:function(t,e){var r=h(t,e),n=d(r,e);return g(n,n.getSelectionAfter())},applyInlineStyle:function(t,e,r){return i.add(t,e,r)},removeInlineStyle:function(t,e,r){return i.remove(t,e,r)},setBlockType:function(t,e,r){return p(t,e,(function(t){return t.merge({type:r,depth:0})}))},setBlockData:function(t,e,r){return p(t,e,(function(t){return t.merge({data:r})}))},mergeBlockData:function(t,e,r){return p(t,e,(function(t){return t.merge({data:t.getData().merge(r)})}))},applyEntity:function(t,e,r){var n=h(t,e);return o(n,e,r)}};t.exports=v},22146:t=>{"use strict";var e="-",r={encode:function(t,r,n){return t+e+r+e+n},decode:function(t){var r=t.split(e).reverse(),n=r[0],i=r[1];return{blockKey:r.slice(2).reverse().join(e),decoratorKey:parseInt(i,10),leafKey:parseInt(n,10)}}};t.exports=r},45712:(t,e,r)=>{"use strict";function n(t,e,r){return e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var i=r(38777),o=r(67953),a=r(67841),s=r(25027),u=r(69769),c=r(68642),l=r(43393),f=r(55283),p=l.List,h=l.Repeat,d=c("draft_tree_data_support"),g=d?o:i,y={processHTML:function(t,e){return a(t,u,e)},processText:function(t,e,r){return t.reduce((function(t,i,o){i=f(i);var a=s(),u={key:a,type:r,text:i,characterList:p(h(e,i.length))};if(d&&0!==o){var c=o-1;u=function(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{},i=Object.keys(r);"function"==typeof Object.getOwnPropertySymbols&&(i=i.concat(Object.getOwnPropertySymbols(r).filter((function(t){return Object.getOwnPropertyDescriptor(r,t).enumerable})))),i.forEach((function(e){n(t,e,r[e])}))}return t}({},u,{prevSibling:(t[c]=t[c].merge({nextSibling:a})).getKey()})}return t.push(new g(u)),t}),[])}};t.exports=y},73932:(t,e,r)=>{"use strict";var n="['‘’]",i="\\s|(?![_])"+r(65724).getPunctuation(),o=new RegExp("^(?:"+i+")*(?:"+n+"|(?!"+i+").)*(?:(?!"+i+").)"),a=new RegExp("(?:(?!"+i+").)(?:"+n+"|(?!"+i+").)*(?:"+i+")*$");function s(t,e){var r=e?a.exec(t):o.exec(t);return r?r[0]:t}var u={getBackward:function(t){return s(t,!0)},getForward:function(t){return s(t,!1)}};t.exports=u},86155:t=>{"use strict";var e={stringify:function(t){return"_"+String(t)},unstringify:function(t){return t.slice(1)}};t.exports=e},68957:(t,e,r)=>{"use strict";function n(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{},n=Object.keys(r);"function"==typeof Object.getOwnPropertySymbols&&(n=n.concat(Object.getOwnPropertySymbols(r).filter((function(t){return Object.getOwnPropertyDescriptor(r,t).enumerable})))),n.forEach((function(e){i(t,e,r[e])}))}return t}function i(t,e,r){return e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var o=r(25027),a=r(73759),s=function(t){if(!t||!t.type)return!1;var e=t.type;return"unordered-list-item"===e||"ordered-list-item"===e},u={fromRawTreeStateToRawState:function(t){var e=t.blocks,r=[];return Array.isArray(e)||a(!1),Array.isArray(e)&&e.length?(function(t,e){for(var i=[].concat(t).reverse();i.length;){var o=i.pop();l=void 0,l=n({},c=o),s(c)&&(l.depth=l.depth||0,function(t){Array.isArray(t.children)&&(t.children=t.children.map((function(e){return e.type===t.type?n({},e,{depth:(t.depth||0)+1}):e})))}(c),null!=c.children&&c.children.length>0)||(delete l.children,r.push(l));var u=o.children;Array.isArray(u)||a(!1),i=i.concat([].concat(u.reverse()))}var c,l}(e),t.blocks=r,n({},t,{blocks:r})):t},fromRawStateToRawTreeState:function(t){var e=[],r=[];return t.blocks.forEach((function(t){var i=s(t),a=t.depth||0,u=n({},t,{children:[]});if(i){var c=r[0];if(null==c&&0===a)e.push(u);else if(null==c||c.depth<a-1){var l={key:o(),text:"",depth:a-1,type:t.type,children:[],entityRanges:[],inlineStyleRanges:[]};r.unshift(l),1===a?e.push(l):null!=c&&c.children.push(l),l.children.push(u)}else if(c.depth===a-1)c.children.push(u);else{for(;null!=c&&c.depth>=a;)r.shift(),c=r[0];a>0?c.children.push(u):e.push(u)}}else e.push(u)})),n({},t,{blocks:e})}};t.exports=u},12119:(t,e,r)=>{"use strict";r(63620),t.exports={isValidBlock:function(t,e){var r=t.getKey(),n=t.getParentKey();if(null!=n&&!e.get(n).getChildKeys().includes(r))return!1;if(!t.getChildKeys().map((function(t){return e.get(t)})).every((function(t){return t.getParentKey()===r})))return!1;var i=t.getPrevSiblingKey();if(null!=i&&e.get(i).getNextSiblingKey()!==r)return!1;var o=t.getNextSiblingKey();return(null==o||e.get(o).getPrevSiblingKey()===r)&&!(null!==o&&null!==i&&i===o||""!=t.text&&t.getChildKeys().size>0)},isConnectedTree:function(t){var e=t.toArray().filter((function(t){return null==t.getParentKey()&&null==t.getPrevSiblingKey()}));if(1!==e.length)return!1;for(var r=0,n=e.shift().getKey(),i=[];null!=n;){var o=t.get(n),a=o.getChildKeys(),s=o.getNextSiblingKey();if(a.size>0){null!=s&&i.unshift(s);var u=a.map((function(e){return t.get(e)})).find((function(t){return null==t.getPrevSiblingKey()}));if(null==u)return!1;n=u.getKey()}else n=null!=o.getNextSiblingKey()?o.getNextSiblingKey():i.shift();r++}return r===t.size},isValidTree:function(t){var e=this;return!!t.toArray().every((function(r){return e.isValidBlock(r,t)}))&&this.isConnectedTree(t)}}},33337:(t,e,r)=>{"use strict";var n,i=r(7902),o=r(43393),a=r(22045),s=o.OrderedMap,u={getDirectionMap:function(t,e){n?n.reset():n=new i;var r=t.getBlockMap(),u=r.valueSeq().map((function(t){return a(n).getDirection(t.getText())})),c=s(r.keySeq().zip(u));return null!=e&&o.is(e,c)?e:c}};t.exports=u},14289:(t,e,r)=>{"use strict";function n(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{},n=Object.keys(r);"function"==typeof Object.getOwnPropertySymbols&&(n=n.concat(Object.getOwnPropertySymbols(r).filter((function(t){return Object.getOwnPropertyDescriptor(r,t).enumerable})))),n.forEach((function(e){i(t,e,r[e])}))}return t}function i(t,e,r){return e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var o=r(34365),a=r(66912),s=r(33337),u=r(25110),c=r(43393),l=c.OrderedSet,f=c.Record,p=c.Stack,h=c.OrderedMap,d=c.List,g=f({allowUndo:!0,currentContent:null,decorator:null,directionMap:null,forceSelection:!1,inCompositionMode:!1,inlineStyleOverride:null,lastChangeType:null,nativelyRenderedContent:null,redoStack:p(),selection:null,treeMap:null,undoStack:p()}),y=function(){e.createEmpty=function(t){return this.createWithText("",t)},e.createWithText=function(t,r){return e.createWithContent(a.createFromText(t),r)},e.createWithContent=function(t,r){if(0===t.getBlockMap().count())return e.createEmpty(r);var n=t.getBlockMap().first().getKey();return e.create({currentContent:t,undoStack:p(),redoStack:p(),decorator:r||null,selection:u.createEmpty(n)})},e.create=function(t){var r=t.currentContent,i=n({},t,{treeMap:m(r,t.decorator),directionMap:s.getDirectionMap(r)});return new e(new g(i))},e.fromJS=function(t){return new e(new g(n({},t,{directionMap:null!=t.directionMap?h(t.directionMap):t.directionMap,inlineStyleOverride:null!=t.inlineStyleOverride?l(t.inlineStyleOverride):t.inlineStyleOverride,nativelyRenderedContent:null!=t.nativelyRenderedContent?a.fromJS(t.nativelyRenderedContent):t.nativelyRenderedContent,redoStack:null!=t.redoStack?p(t.redoStack.map((function(t){return a.fromJS(t)}))):t.redoStack,selection:null!=t.selection?new u(t.selection):t.selection,treeMap:null!=t.treeMap?h(t.treeMap).map((function(t){return d(t).map((function(t){return o.fromJS(t)}))})):t.treeMap,undoStack:null!=t.undoStack?p(t.undoStack.map((function(t){return a.fromJS(t)}))):t.undoStack,currentContent:a.fromJS(t.currentContent)})))},e.set=function(t,r){return new e(t.getImmutable().withMutations((function(e){var n=e.get("decorator"),i=n;null===r.decorator?i=null:r.decorator&&(i=r.decorator);var a=r.currentContent||t.getCurrentContent();if(i!==n){var s,u=e.get("treeMap");return s=i&&n?function(t,e,r,n,i){return r.merge(e.toSeq().filter((function(e){return n.getDecorations(e,t)!==i.getDecorations(e,t)})).map((function(e){return o.generate(t,e,n)})))}(a,a.getBlockMap(),u,i,n):m(a,i),void e.merge({decorator:i,treeMap:s,nativelyRenderedContent:null})}a!==t.getCurrentContent()&&e.set("treeMap",function(t,e,r,n){var i=t.getCurrentContent().set("entityMap",r),a=i.getBlockMap();return t.getImmutable().get("treeMap").merge(e.toSeq().filter((function(t,e){return t!==a.get(e)})).map((function(t){return o.generate(i,t,n)})))}(t,a.getBlockMap(),a.getEntityMap(),i)),e.merge(r)})))};var t=e.prototype;function e(t){i(this,"_immutable",void 0),this._immutable=t}return t.toJS=function(){return this.getImmutable().toJS()},t.getAllowUndo=function(){return this.getImmutable().get("allowUndo")},t.getCurrentContent=function(){return this.getImmutable().get("currentContent")},t.getUndoStack=function(){return this.getImmutable().get("undoStack")},t.getRedoStack=function(){return this.getImmutable().get("redoStack")},t.getSelection=function(){return this.getImmutable().get("selection")},t.getDecorator=function(){return this.getImmutable().get("decorator")},t.isInCompositionMode=function(){return this.getImmutable().get("inCompositionMode")},t.mustForceSelection=function(){return this.getImmutable().get("forceSelection")},t.getNativelyRenderedContent=function(){return this.getImmutable().get("nativelyRenderedContent")},t.getLastChangeType=function(){return this.getImmutable().get("lastChangeType")},t.getInlineStyleOverride=function(){return this.getImmutable().get("inlineStyleOverride")},e.setInlineStyleOverride=function(t,r){return e.set(t,{inlineStyleOverride:r})},t.getCurrentInlineStyle=function(){var t=this.getInlineStyleOverride();if(null!=t)return t;var e=this.getCurrentContent(),r=this.getSelection();return r.isCollapsed()?function(t,e){var r=e.getStartKey(),n=e.getStartOffset(),i=t.getBlockForKey(r);return n>0?i.getInlineStyleAt(n-1):i.getLength()?i.getInlineStyleAt(0):_(t,r)}(e,r):function(t,e){var r=e.getStartKey(),n=e.getStartOffset(),i=t.getBlockForKey(r);return n<i.getLength()?i.getInlineStyleAt(n):n>0?i.getInlineStyleAt(n-1):_(t,r)}(e,r)},t.getBlockTree=function(t){return this.getImmutable().getIn(["treeMap",t])},t.isSelectionAtStartOfContent=function(){var t=this.getCurrentContent().getBlockMap().first().getKey();return this.getSelection().hasEdgeWithin(t,0,0)},t.isSelectionAtEndOfContent=function(){var t=this.getCurrentContent().getBlockMap().last(),e=t.getLength();return this.getSelection().hasEdgeWithin(t.getKey(),e,e)},t.getDirectionMap=function(){return this.getImmutable().get("directionMap")},e.acceptSelection=function(t,e){return v(t,e,!1)},e.forceSelection=function(t,e){return e.getHasFocus()||(e=e.set("hasFocus",!0)),v(t,e,!0)},e.moveSelectionToEnd=function(t){var r=t.getCurrentContent().getLastBlock(),n=r.getKey(),i=r.getLength();return e.acceptSelection(t,new u({anchorKey:n,anchorOffset:i,focusKey:n,focusOffset:i,isBackward:!1}))},e.moveFocusToEnd=function(t){var r=e.moveSelectionToEnd(t);return e.forceSelection(r,r.getSelection())},e.push=function(t,r,n){var i=!(arguments.length>3&&void 0!==arguments[3])||arguments[3];if(t.getCurrentContent()===r)return t;var o=s.getDirectionMap(r,t.getDirectionMap());if(!t.getAllowUndo())return e.set(t,{currentContent:r,directionMap:o,lastChangeType:n,selection:r.getSelectionAfter(),forceSelection:i,inlineStyleOverride:null});var a=t.getSelection(),u=t.getCurrentContent(),c=t.getUndoStack(),l=r;a!==u.getSelectionAfter()||function(t,e){return e!==t.getLastChangeType()||"insert-characters"!==e&&"backspace-character"!==e&&"delete-character"!==e}(t,n)?(c=c.push(u),l=l.set("selectionBefore",a)):"insert-characters"!==n&&"backspace-character"!==n&&"delete-character"!==n||(l=l.set("selectionBefore",u.getSelectionBefore()));var f=t.getInlineStyleOverride();-1===["adjust-depth","change-block-type","split-block"].indexOf(n)&&(f=null);var h={currentContent:l,directionMap:o,undoStack:c,redoStack:p(),lastChangeType:n,selection:r.getSelectionAfter(),forceSelection:i,inlineStyleOverride:f};return e.set(t,h)},e.undo=function(t){if(!t.getAllowUndo())return t;var r=t.getUndoStack(),n=r.peek();if(!n)return t;var i=t.getCurrentContent(),o=s.getDirectionMap(n,t.getDirectionMap());return e.set(t,{currentContent:n,directionMap:o,undoStack:r.shift(),redoStack:t.getRedoStack().push(i),forceSelection:!0,inlineStyleOverride:null,lastChangeType:"undo",nativelyRenderedContent:null,selection:i.getSelectionBefore()})},e.redo=function(t){if(!t.getAllowUndo())return t;var r=t.getRedoStack(),n=r.peek();if(!n)return t;var i=t.getCurrentContent(),o=s.getDirectionMap(n,t.getDirectionMap());return e.set(t,{currentContent:n,directionMap:o,undoStack:t.getUndoStack().push(i),redoStack:r.shift(),forceSelection:!0,inlineStyleOverride:null,lastChangeType:"redo",nativelyRenderedContent:null,selection:n.getSelectionAfter()})},t.getImmutable=function(){return this._immutable},e}();function v(t,e,r){return y.set(t,{selection:e,forceSelection:r,nativelyRenderedContent:null,inlineStyleOverride:null})}function m(t,e){return t.getBlockMap().map((function(r){return o.generate(t,r,e)})).toOrderedMap()}function _(t,e){var r=t.getBlockMap().reverse().skipUntil((function(t,r){return r===e})).skip(1).skipUntil((function(t,e){return t.getLength()})).first();return r?r.getInlineStyleAt(r.getLength()-1):l()}t.exports=y},47387:(t,e,r)=>{"use strict";var n=r(4856),i=r(17797),o=n.isPlatform("Mac OS X"),a={isCtrlKeyCommand:function(t){return!!t.ctrlKey&&!t.altKey},isOptionKeyCommand:function(t){return o&&t.altKey},usesMacOSHeuristics:function(){return o},hasCommandModifier:function(t){return o?!!t.metaKey&&!t.altKey:a.isCtrlKeyCommand(t)},isSoftNewlineEvent:i};t.exports=a},70054:()=>{},41947:(t,e,r)=>{"use strict";var n=r(42307),i=r(14289),o=r(1665),a=r(22045),s={currentBlockContainsLink:function(t){var e=t.getSelection(),r=t.getCurrentContent(),n=r.getEntityMap();return r.getBlockForKey(e.getAnchorKey()).getCharacterList().slice(e.getStartOffset(),e.getEndOffset()).some((function(t){var e=t.getEntity();return!!e&&"LINK"===n.__get(e).getType()}))},getCurrentBlockType:function(t){var e=t.getSelection();return t.getCurrentContent().getBlockForKey(e.getStartKey()).getType()},getDataObjectForLinkURL:function(t){return{url:t.toString()}},handleKeyCommand:function(t,e,r){switch(e){case"bold":return s.toggleInlineStyle(t,"BOLD");case"italic":return s.toggleInlineStyle(t,"ITALIC");case"underline":return s.toggleInlineStyle(t,"UNDERLINE");case"code":return s.toggleCode(t);case"backspace":case"backspace-word":case"backspace-to-start-of-line":return s.onBackspace(t);case"delete":case"delete-word":case"delete-to-end-of-block":return s.onDelete(t);default:return null}},insertSoftNewline:function(t){var e=n.insertText(t.getCurrentContent(),t.getSelection(),"\n",t.getCurrentInlineStyle(),null),r=i.push(t,e,"insert-characters");return i.forceSelection(r,e.getSelectionAfter())},onBackspace:function(t){var e=t.getSelection();if(!e.isCollapsed()||e.getAnchorOffset()||e.getFocusOffset())return null;var r=t.getCurrentContent(),n=e.getStartKey(),o=r.getBlockBefore(n);if(o&&"atomic"===o.getType()){var a=r.getBlockMap().delete(o.getKey()),u=r.merge({blockMap:a,selectionAfter:e});if(u!==r)return i.push(t,u,"remove-range")}var c=s.tryToRemoveBlockStyle(t);return c?i.push(t,c,"change-block-type"):null},onDelete:function(t){var e=t.getSelection();if(!e.isCollapsed())return null;var r=t.getCurrentContent(),o=e.getStartKey(),a=r.getBlockForKey(o).getLength();if(e.getStartOffset()<a)return null;var s=r.getBlockAfter(o);if(!s||"atomic"!==s.getType())return null;var u=e.merge({focusKey:s.getKey(),focusOffset:s.getLength()}),c=n.removeRange(r,u,"forward");return c!==r?i.push(t,c,"remove-range"):null},onTab:function(t,e,r){var n=e.getSelection(),a=n.getAnchorKey();if(a!==n.getFocusKey())return e;var s=e.getCurrentContent(),u=s.getBlockForKey(a),c=u.getType();if("unordered-list-item"!==c&&"ordered-list-item"!==c)return e;t.preventDefault();var l=u.getDepth();if(!t.shiftKey&&l===r)return e;var f=o(s,n,t.shiftKey?-1:1,r);return i.push(e,f,"adjust-depth")},toggleBlockType:function(t,e){var r=t.getSelection(),o=r.getStartKey(),s=r.getEndKey(),u=t.getCurrentContent(),c=r;if(o!==s&&0===r.getEndOffset()){var l=a(u.getBlockBefore(s));s=l.getKey(),c=c.merge({anchorKey:o,anchorOffset:r.getStartOffset(),focusKey:s,focusOffset:l.getLength(),isBackward:!1})}if(u.getBlockMap().skipWhile((function(t,e){return e!==o})).reverse().skipWhile((function(t,e){return e!==s})).some((function(t){return"atomic"===t.getType()})))return t;var f=u.getBlockForKey(o).getType()===e?"unstyled":e;return i.push(t,n.setBlockType(u,c,f),"change-block-type")},toggleCode:function(t){var e=t.getSelection(),r=e.getAnchorKey(),n=e.getFocusKey();return e.isCollapsed()||r!==n?s.toggleBlockType(t,"code-block"):s.toggleInlineStyle(t,"CODE")},toggleInlineStyle:function(t,e){var r=t.getSelection(),o=t.getCurrentInlineStyle();if(r.isCollapsed())return i.setInlineStyleOverride(t,o.has(e)?o.remove(e):o.add(e));var a,s=t.getCurrentContent();return a=o.has(e)?n.removeInlineStyle(s,r,e):n.applyInlineStyle(s,r,e),i.push(t,a,"change-inline-style")},toggleLink:function(t,e,r){var o=n.applyEntity(t.getCurrentContent(),e,r);return i.push(t,o,"apply-entity")},tryToRemoveBlockStyle:function(t){var e=t.getSelection(),r=e.getAnchorOffset();if(e.isCollapsed()&&0===r){var i=e.getAnchorKey(),o=t.getCurrentContent(),a=o.getBlockForKey(i).getType(),s=o.getBlockBefore(i);if("code-block"===a&&s&&"code-block"===s.getType()&&0!==s.getLength())return null;if("unstyled"!==a)return n.setBlockType(o,e,"unstyled")}return null}};t.exports=s},83751:(t,e,r)=>{"use strict";var n=r(42307),i=r(14289),o=r(88687),a=r(22045),s=null,u={cut:function(t){var e=t.getCurrentContent(),r=t.getSelection(),u=null;if(r.isCollapsed()){var c=r.getAnchorKey(),l=e.getBlockForKey(c).getLength();if(l===r.getAnchorOffset()){var f=e.getKeyAfter(c);if(null==f)return t;u=r.set("focusKey",f).set("focusOffset",0)}else u=r.set("focusOffset",l)}else u=r;u=a(u),s=o(e,u);var p=n.removeRange(e,u,"forward");return p===e?t:i.push(t,p,"remove-range")},paste:function(t){if(!s)return t;var e=n.replaceWithFragment(t.getCurrentContent(),t.getSelection(),s);return i.push(t,e,"insert-fragment")}};t.exports=u},25110:(t,e,r)=>{"use strict";var n=function(t){var e,r;function n(){return t.apply(this,arguments)||this}r=t,(e=n).prototype=Object.create(r.prototype),e.prototype.constructor=e,e.__proto__=r;var i=n.prototype;return i.serialize=function(){return"Anchor: "+this.getAnchorKey()+":"+this.getAnchorOffset()+", Focus: "+this.getFocusKey()+":"+this.getFocusOffset()+", Is Backward: "+String(this.getIsBackward())+", Has Focus: "+String(this.getHasFocus())},i.getAnchorKey=function(){return this.get("anchorKey")},i.getAnchorOffset=function(){return this.get("anchorOffset")},i.getFocusKey=function(){return this.get("focusKey")},i.getFocusOffset=function(){return this.get("focusOffset")},i.getIsBackward=function(){return this.get("isBackward")},i.getHasFocus=function(){return this.get("hasFocus")},i.hasEdgeWithin=function(t,e,r){var n=this.getAnchorKey(),i=this.getFocusKey();if(n===i&&n===t){var o=this.getStartOffset(),a=this.getEndOffset();return e<=o&&o<=r||e<=a&&a<=r}if(t!==n&&t!==i)return!1;var s=t===n?this.getAnchorOffset():this.getFocusOffset();return e<=s&&r>=s},i.isCollapsed=function(){return this.getAnchorKey()===this.getFocusKey()&&this.getAnchorOffset()===this.getFocusOffset()},i.getStartKey=function(){return this.getIsBackward()?this.getFocusKey():this.getAnchorKey()},i.getStartOffset=function(){return this.getIsBackward()?this.getFocusOffset():this.getAnchorOffset()},i.getEndKey=function(){return this.getIsBackward()?this.getAnchorKey():this.getFocusKey()},i.getEndOffset=function(){return this.getIsBackward()?this.getAnchorOffset():this.getFocusOffset()},n.createEmpty=function(t){return new n({anchorKey:t,anchorOffset:0,focusKey:t,focusOffset:0,isBackward:!1,hasFocus:!1})},n}((0,r(43393).Record)({anchorKey:"",anchorOffset:0,focusKey:"",focusOffset:0,isBackward:!1,hasFocus:!1}));t.exports=n},1665:t=>{"use strict";t.exports=function(t,e,r,n){var i=e.getStartKey(),o=e.getEndKey(),a=t.getBlockMap(),s=a.toSeq().skipUntil((function(t,e){return e===i})).takeUntil((function(t,e){return e===o})).concat([[o,a.get(o)]]).map((function(t){var e=t.getDepth()+r;return e=Math.max(0,Math.min(e,n)),t.set("depth",e)}));return a=a.merge(s),t.merge({blockMap:a,selectionBefore:e,selectionAfter:e})}},2835:(t,e,r)=>{"use strict";var n=r(4516);t.exports=function(t,e,r,i){for(var o=e,a=t.getCharacterList();o<r;)a=a.set(o,n.applyEntity(a.get(o),i)),o++;return t.set("characterList",a)}},68750:(t,e,r)=>{"use strict";var n=r(2835),i=r(43393);t.exports=function(t,e,r){var o=t.getBlockMap(),a=e.getStartKey(),s=e.getStartOffset(),u=e.getEndKey(),c=e.getEndOffset(),l=o.skipUntil((function(t,e){return e===a})).takeUntil((function(t,e){return e===u})).toOrderedMap().merge(i.OrderedMap([[u,o.get(u)]])).map((function(t,e){var i=e===a?s:0,o=e===u?c:t.getLength();return n(t,i,o,r)}));return t.merge({blockMap:o.merge(l),selectionBefore:e,selectionAfter:e})}},79981:(t,e,r)=>{"use strict";function n(t,e,r){return e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var i=r(38777),o=r(67953),a=r(86155),s=r(56265),u=r(31487),c=r(73759),l=function(t,e){return{key:t.getKey(),text:t.getText(),type:t.getType(),depth:t.getDepth(),inlineStyleRanges:u(t),entityRanges:s(t,e),data:t.getData().toObject()}};t.exports=function(t){var e={entityMap:{},blocks:[]};return e=function(t,e){var r=e.entityMap,s=[],u={},f={},p=0;return t.getBlockMap().forEach((function(t){t.findEntityRanges((function(t){return null!==t.getEntity()}),(function(e){var n=t.getEntityAt(e),i=a.stringify(n);f[i]||(f[i]=n,r[i]="".concat(p),p++)})),function(t,e,r,a){if(t instanceof i)r.push(l(t,e));else{t instanceof o||c(!1);var s=t.getParentKey(),u=a[t.getKey()]=function(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{},i=Object.keys(r);"function"==typeof Object.getOwnPropertySymbols&&(i=i.concat(Object.getOwnPropertySymbols(r).filter((function(t){return Object.getOwnPropertyDescriptor(r,t).enumerable})))),i.forEach((function(e){n(t,e,r[e])}))}return t}({},l(t,e),{children:[]});s?a[s].children.push(u):r.push(u)}}(t,r,s,u)})),{blocks:s,entityMap:r}}(t,e),e=function(t,e){var r=e.blocks,n=e.entityMap,i={};return Object.keys(n).forEach((function(e,r){var n=t.getEntity(a.unstringify(e));i[r]={type:n.getType(),mutability:n.getMutability(),data:n.getData()}})),{blocks:r,entityMap:i}}(t,e),e}},67841:(t,e,r)=>{"use strict";var n;function i(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{},n=Object.keys(r);"function"==typeof Object.getOwnPropertySymbols&&(n=n.concat(Object.getOwnPropertySymbols(r).filter((function(t){return Object.getOwnPropertyDescriptor(r,t).enumerable})))),n.forEach((function(e){o(t,e,r[e])}))}return t}function o(t,e,r){return e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var a=r(4516),s=r(38777),u=r(67953),c=r(526),l=r(82222),f=r(61425),p=r(62620),h=r(25027),d=r(69769),g=r(68642),y=r(43393),v=y.List,m=y.Map,_=y.OrderedSet,b=r(78241),S=r(16581),w=r(20717),x=r(35039),k=g("draft_tree_data_support"),C=new RegExp("\r","g"),E=new RegExp("\n","g"),O=new RegExp("^\n","g"),D=new RegExp("&nbsp;","g"),K=new RegExp("&#13;?","g"),T=new RegExp("&#8203;?","g"),M=["bold","bolder","500","600","700","800","900"],A=["light","lighter","normal","100","200","300","400"],I=["className","href","rel","target","title"],B=["alt","className","height","src","width"],L=(o(n={},p("public/DraftStyleDefault/depth0"),0),o(n,p("public/DraftStyleDefault/depth1"),1),o(n,p("public/DraftStyleDefault/depth2"),2),o(n,p("public/DraftStyleDefault/depth3"),3),o(n,p("public/DraftStyleDefault/depth4"),4),n),R=m({b:"BOLD",code:"CODE",del:"STRIKETHROUGH",em:"ITALIC",i:"ITALIC",s:"STRIKETHROUGH",strike:"STRIKETHROUGH",strong:"BOLD",u:"UNDERLINE",mark:"HIGHLIGHT"}),N=function(t){return w(t)&&t.style.fontFamily.includes("monospace")?"CODE":null},F=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;return Object.keys(L).some((function(r){t.classList.contains(r)&&(e=L[r])})),e},P=function(t){if(!b(t))return!1;var e=t;if(!e.href||"http:"!==e.protocol&&"https:"!==e.protocol&&"mailto:"!==e.protocol&&"tel:"!==e.protocol)return!1;try{return new f(e.href),!0}catch(t){return!1}},z=function(t){if(!x(t))return!1;var e=t;return!(!e.attributes.getNamedItem("src")||!e.attributes.getNamedItem("src").value)},j=function(t,e){if(!w(t))return e;var r=t,n=r.style.fontWeight,i=r.style.fontStyle,o=r.style.textDecoration;return e.withMutations((function(t){M.indexOf(n)>=0?t.add("BOLD"):A.indexOf(n)>=0&&t.remove("BOLD"),"italic"===i?t.add("ITALIC"):"normal"===i&&t.remove("ITALIC"),"underline"===o&&t.add("UNDERLINE"),"line-through"===o&&t.add("STRIKETHROUGH"),"none"===o&&(t.remove("UNDERLINE"),t.remove("STRIKETHROUGH"))}))},U=function(t){return"ul"===t||"ol"===t},q=function(){function t(t,e){o(this,"characterList",v()),o(this,"currentBlockType","unstyled"),o(this,"currentDepth",0),o(this,"currentEntity",null),o(this,"currentText",""),o(this,"wrapper",null),o(this,"blockConfigs",[]),o(this,"contentBlocks",[]),o(this,"entityMap",l),o(this,"blockTypeMap",void 0),o(this,"disambiguate",void 0),this.clear(),this.blockTypeMap=t,this.disambiguate=e}var e=t.prototype;return e.clear=function(){this.characterList=v(),this.blockConfigs=[],this.currentBlockType="unstyled",this.currentDepth=0,this.currentEntity=null,this.currentText="",this.entityMap=l,this.wrapper=null,this.contentBlocks=[]},e.addDOMNode=function(t){var e;return this.contentBlocks=[],this.currentDepth=0,(e=this.blockConfigs).push.apply(e,this._toBlockConfigs([t],_())),this._trimCurrentText(),""!==this.currentText&&this.blockConfigs.push(this._makeBlockConfig()),this},e.getContentBlocks=function(){return 0===this.contentBlocks.length&&(k?this._toContentBlocks(this.blockConfigs):this._toFlatContentBlocks(this.blockConfigs)),{contentBlocks:this.contentBlocks,entityMap:this.entityMap}},e._makeBlockConfig=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=i({key:t.key||h(),type:this.currentBlockType,text:this.currentText,characterList:this.characterList,depth:this.currentDepth,parent:null,children:v(),prevSibling:null,nextSibling:null,childConfigs:[]},t);return this.characterList=v(),this.currentBlockType="unstyled",this.currentText="",e},e._toBlockConfigs=function(t,e){for(var r=[],n=0;n<t.length;n++){var i=t[n],o=i.nodeName.toLowerCase();if("body"===o||U(o)){this._trimCurrentText(),""!==this.currentText&&r.push(this._makeBlockConfig());var a=this.currentDepth,s=this.wrapper;U(o)&&(this.wrapper=o,U(s)&&this.currentDepth++),r.push.apply(r,this._toBlockConfigs(Array.from(i.childNodes),e)),this.currentDepth=a,this.wrapper=s}else{var u=this.blockTypeMap.get(o);if(void 0===u)if("#text"!==o)if("br"!==o)if(z(i))this._addImgNode(i,e);else if(P(i))this._addAnchorNode(i,r,e);else{var c=e;R.has(o)&&(c=c.add(R.get(o))),c=j(i,c);var l=N(i);null!=l&&(c=c.add(l)),r.push.apply(r,this._toBlockConfigs(Array.from(i.childNodes),c))}else this._addBreakNode(i,e);else this._addTextNode(i,e);else{this._trimCurrentText(),""!==this.currentText&&r.push(this._makeBlockConfig());var f=this.currentDepth,p=this.wrapper;if(this.wrapper="pre"===o?"pre":this.wrapper,"string"!=typeof u&&(u=this.disambiguate(o,this.wrapper)||u[0]||"unstyled"),!k&&w(i)&&("unordered-list-item"===u||"ordered-list-item"===u)){var d=i;this.currentDepth=F(d,this.currentDepth)}var g=h(),y=this._toBlockConfigs(Array.from(i.childNodes),e);this._trimCurrentText(),r.push(this._makeBlockConfig({key:g,childConfigs:y,type:u})),this.currentDepth=f,this.wrapper=p}}}return r},e._appendText=function(t,e){var r;this.currentText+=t;var n=a.create({style:e,entity:this.currentEntity});this.characterList=(r=this.characterList).push.apply(r,Array(t.length).fill(n))},e._trimCurrentText=function(){var t=this.currentText.length,e=t-this.currentText.trimLeft().length,r=this.currentText.trimRight().length,n=this.characterList.findEntry((function(t){return null!==t.getEntity()}));(e=void 0!==n?Math.min(e,n[0]):e)>(r=void 0!==(n=this.characterList.reverse().findEntry((function(t){return null!==t.getEntity()})))?Math.max(r,t-n[0]):r)?(this.currentText="",this.characterList=v()):(this.currentText=this.currentText.slice(e,r),this.characterList=this.characterList.slice(e,r))},e._addTextNode=function(t,e){var r=t.textContent;""===r.trim()&&"pre"!==this.wrapper&&(r=" "),"pre"!==this.wrapper&&(r=(r=r.replace(O,"")).replace(E," ")),this._appendText(r,e)},e._addBreakNode=function(t,e){S(t)&&this._appendText("\n",e)},e._addImgNode=function(t,e){if(x(t)){var r=t,n={};B.forEach((function(t){var e=r.getAttribute(t);e&&(n[t]=e)})),this.currentEntity=this.entityMap.__create("IMAGE","IMMUTABLE",n),g("draftjs_fix_paste_for_img")?"presentation"!==r.getAttribute("role")&&this._appendText("📷",e):this._appendText("📷",e),this.currentEntity=null}},e._addAnchorNode=function(t,e,r){if(b(t)){var n=t,i={};I.forEach((function(t){var e=n.getAttribute(t);e&&(i[t]=e)})),i.url=new f(n.href).toString(),this.currentEntity=this.entityMap.__create("LINK","MUTABLE",i||{}),e.push.apply(e,this._toBlockConfigs(Array.from(t.childNodes),r)),this.currentEntity=null}},e._toContentBlocks=function(t){for(var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,r=t.length-1,n=0;n<=r;n++){var o=t[n];o.parent=e,o.prevSibling=n>0?t[n-1].key:null,o.nextSibling=n<r?t[n+1].key:null,o.children=v(o.childConfigs.map((function(t){return t.key}))),this.contentBlocks.push(new u(i({},o))),this._toContentBlocks(o.childConfigs,o.key)}},e._hoistContainersInBlockConfigs=function(t){var e=this;return v(t).flatMap((function(t){return"unstyled"!==t.type||""!==t.text?[t]:e._hoistContainersInBlockConfigs(t.childConfigs)}))},e._toFlatContentBlocks=function(t){var e=this;this._hoistContainersInBlockConfigs(t).forEach((function(t){var r=e._extractTextFromBlockConfigs(t.childConfigs),n=r.text,o=r.characterList;e.contentBlocks.push(new s(i({},t,{text:t.text+n,characterList:t.characterList.concat(o)})))}))},e._extractTextFromBlockConfigs=function(t){for(var e=t.length-1,r="",n=v(),i=0;i<=e;i++){var o=t[i];r+=o.text,n=n.concat(o.characterList),""!==r&&"unstyled"!==o.type&&(r+="\n",n=n.push(n.last()));var a=this._extractTextFromBlockConfigs(o.childConfigs);r+=a.text,n=n.concat(a.characterList)}return{text:r,characterList:n}},t}();t.exports=function(t){var e=arguments.length>2&&void 0!==arguments[2]?arguments[2]:c,r=(arguments.length>1&&void 0!==arguments[1]?arguments[1]:d)(t=t.trim().replace(C,"").replace(D," ").replace(K,"").replace(T,""));if(!r)return null;var n=function(t){var e={};return t.mapKeys((function(t,r){var n=[r.element];void 0!==r.aliasedElements&&n.push.apply(n,r.aliasedElements),n.forEach((function(r){void 0===e[r]?e[r]=t:"string"==typeof e[r]?e[r]=[e[r],t]:e[r].push(t)}))})),m(e)}(e);return new q(n,(function(t,e){return"li"===t?"ol"===e?"ordered-list-item":"unordered-list-item":null})).addDOMNode(r).getContentBlocks()}},99607:(t,e,r)=>{"use strict";function n(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{},n=Object.keys(r);"function"==typeof Object.getOwnPropertySymbols&&(n=n.concat(Object.getOwnPropertySymbols(r).filter((function(t){return Object.getOwnPropertyDescriptor(r,t).enumerable})))),n.forEach((function(e){i(t,e,r[e])}))}return t}function i(t,e,r){return e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var o=r(38777),a=r(67953),s=r(66912),u=r(82222),c=r(68957),l=(r(12119),r(25110)),f=r(86019),p=r(67134),h=r(59672),d=r(25027),g=r(68642),y=r(43393),v=r(73759),m=g("draft_tree_data_support"),_=y.List,b=y.Map,S=y.OrderedMap,w=function(t,e){var r=t.key,n=t.type,i=t.data;return{text:t.text,depth:t.depth||0,type:n||"unstyled",key:r||d(),data:b(i),characterList:x(t,e)}},x=function(t,e){var r=t.text,i=t.entityRanges,o=t.inlineStyleRanges,a=i||[];return f(h(r,o||[]),p(r,a.filter((function(t){return e.hasOwnProperty(t.key)})).map((function(t){return n({},t,{key:e[t.key]})}))))},k=function(t){return n({},t,{key:t.key||d()})},C=function(t,e,r){var i=e.map((function(t){return n({},t,{parentRef:r})}));return t.concat(i.reverse())};t.exports=function(t){Array.isArray(t.blocks)||v(!1);var e=function(t){var e=t.entityMap,r={};return Object.keys(e).forEach((function(t){var n=e[t],i=n.type,o=n.mutability,a=n.data;r[t]=u.__create(i,o,a||{})})),r}(t),r=function(t,e){var r=t.blocks.find((function(t){return Array.isArray(t.children)&&t.children.length>0})),i=m&&!r?c.fromRawStateToRawTreeState(t).blocks:t.blocks;if(!m)return function(t,e){return S(t.map((function(t){var r=new o(w(t,e));return[r.getKey(),r]})))}(r?c.fromRawTreeStateToRawState(t).blocks:i,e);var s=function(t,e){return t.map(k).reduce((function(r,i,o){Array.isArray(i.children)||v(!1);var s=i.children.map(k),u=new a(n({},w(i,e),{prevSibling:0===o?null:t[o-1].key,nextSibling:o===t.length-1?null:t[o+1].key,children:_(s.map((function(t){return t.key})))}));r=r.set(u.getKey(),u);for(var c=C([],s,u);c.length>0;){var l=c.pop(),f=l.parentRef,p=f.getChildKeys(),h=p.indexOf(l.key),d=Array.isArray(l.children);if(!d){d||v(!1);break}var g=l.children.map(k),y=new a(n({},w(l,e),{parent:f.getKey(),children:_(g.map((function(t){return t.key}))),prevSibling:0===h?null:p.get(h-1),nextSibling:h===p.size-1?null:p.get(h+1)}));r=r.set(y.getKey(),y),c=C(c,g,y)}return r}),S())}(i,e);return s}(t,e),i=r.isEmpty()?new l:l.createEmpty(r.first().getKey());return new s({blockMap:r,entityMap:e,selectionBefore:i,selectionAfter:i})}},86019:(t,e,r)=>{"use strict";var n=r(4516),i=r(43393).List;t.exports=function(t,e){var r=t.map((function(t,r){var i=e[r];return n.create({style:t,entity:i})}));return i(r)}},67134:(t,e,r)=>{"use strict";var n=r(38935).substr;t.exports=function(t,e){var r=Array(t.length).fill(null);return e&&e.forEach((function(e){for(var i=n(t,0,e.offset).length,o=i+n(t,e.offset,e.length).length,a=i;a<o;a++)r[a]=e.key})),r}},59672:(t,e,r)=>{"use strict";var n=r(38935),i=r(43393).OrderedSet,o=n.substr,a=i();t.exports=function(t,e){var r=Array(t.length).fill(a);return e&&e.forEach((function(e){for(var n=o(t,0,e.offset).length,i=n+o(t,e.offset,e.length).length;n<i;)r[n]=r[n].add(e.style),n++})),r}},99407:t=>{"use strict";t.exports={notEmptyKey:function(t){return null!=t&&""!=t}}},26396:(t,e,r)=>{"use strict";var n=r(42307),i=r(14289),o=r(4856),a=r(42128),s=r(42177),u=r(40258),c=r(22045),l=r(56926),f=o.isBrowser("Firefox");function p(t,e,r,o,a){var s=n.replaceText(t.getCurrentContent(),t.getSelection(),e,r,o);return i.push(t,s,"insert-characters",a)}t.exports=function(t,e){void 0!==t._pendingStateFromBeforeInput&&(t.update(t._pendingStateFromBeforeInput),t._pendingStateFromBeforeInput=void 0);var r=t._latestEditorState,n=e.data;if(n)if(t.props.handleBeforeInput&&s(t.props.handleBeforeInput(n,r,e.timeStamp)))e.preventDefault();else{var o=r.getSelection(),h=o.getStartOffset(),d=o.getAnchorKey();if(!o.isCollapsed())return e.preventDefault(),void t.update(p(r,n,r.getCurrentInlineStyle(),a(r.getCurrentContent(),r.getSelection()),!0));var g,y=p(r,n,r.getCurrentInlineStyle(),a(r.getCurrentContent(),r.getSelection()),!1),v=!1;if(v||(v=u(t._latestCommittedEditorState)),!v){var m=r.getBlockTree(d),_=y.getBlockTree(d);v=m.size!==_.size||m.zip(_).some((function(t){var e=t[0],r=t[1],i=e.get("start"),o=i+(i>=h?n.length:0),a=e.get("end"),s=a+(a>=h?n.length:0),u=r.get("start"),c=r.get("end"),l=r.get("decoratorKey");return e.get("decoratorKey")!==l||e.get("leaves").size!==r.get("leaves").size||o!==u||s!==c||null!=l&&c-u!=a-i}))}if(v||(g=n,v=f&&("'"==g||"/"==g)),v||(v=c(y.getDirectionMap()).get(d)!==c(r.getDirectionMap()).get(d)),v)return e.preventDefault(),y=i.set(y,{forceSelection:!0}),void t.update(y);y=i.set(y,{nativelyRenderedContent:y.getCurrentContent()}),t._pendingStateFromBeforeInput=y,l((function(){void 0!==t._pendingStateFromBeforeInput&&(t.update(t._pendingStateFromBeforeInput),t._pendingStateFromBeforeInput=void 0)}))}}},43421:(t,e,r)=>{"use strict";var n=r(14289),i=r(67476),o=r(31003);t.exports=function(t,e){var r=e.currentTarget.ownerDocument;if(!Boolean(t.props.preserveSelectionOnBlur)&&o(r)===r.body){var a=r.defaultView.getSelection(),s=t.editor;1===a.rangeCount&&i(s,a.anchorNode)&&i(s,a.focusNode)&&a.removeAllRanges()}var u=t._latestEditorState,c=u.getSelection();if(c.getHasFocus()){var l=c.set("hasFocus",!1);t.props.onBlur&&t.props.onBlur(e),t.update(n.acceptSelection(u,l))}}},6155:(t,e,r)=>{"use strict";var n=r(14289);t.exports=function(t,e){t.setMode("composite"),t.update(n.set(t._latestEditorState,{inCompositionMode:!0})),t._onCompositionStart(e)}},69328:(t,e,r)=>{"use strict";var n=r(94882);t.exports=function(t,e){t._latestEditorState.getSelection().isCollapsed()?e.preventDefault():t.setClipboard(n(t._latestEditorState))}},73935:(t,e,r)=>{"use strict";var n=r(42307),i=r(14289),o=r(19051),a=r(94882),s=r(79749),u=r(80809);t.exports=function(t,e){var r,c=t._latestEditorState,l=c.getSelection(),f=e.target;if(l.isCollapsed())e.preventDefault();else{if(u(f)){var p=f;r=s(o.getScrollParent(p))}var h=a(c);t.setClipboard(h),t.setMode("cut"),setTimeout((function(){t.restoreEditorDOM(r),t.exitCurrentMode(),t.update(function(t){var e=n.removeRange(t.getCurrentContent(),t.getSelection(),"forward");return i.push(t,e,"remove-range")}(c))}),0)}}},39499:t=>{"use strict";t.exports=function(t,e){t.setMode("drag"),e.preventDefault()}},80981:t=>{"use strict";t.exports=function(t){t._internalDrag=!0,t.setMode("drag")}},62186:(t,e,r)=>{"use strict";var n=r(14289),i=r(4856);t.exports=function(t,e){var r=t._latestEditorState,o=r.getSelection();if(!o.getHasFocus()){var a=o.set("hasFocus",!0);t.props.onFocus&&t.props.onFocus(e),i.isBrowser("Chrome < 60.0.3081.0")?t.update(n.forceSelection(r,a)):t.update(n.acceptSelection(r,a))}}},29971:(t,e,r)=>{"use strict";var n=r(42307),i=r(22146),o=r(14289),a=r(4856),s=r(99407).notEmptyKey,u=r(69270),c=r(62800),l=r(22045),f=a.isEngine("Gecko");t.exports=function(t,e){void 0!==t._pendingStateFromBeforeInput&&(t.update(t._pendingStateFromBeforeInput),t._pendingStateFromBeforeInput=void 0);var r=t.editor.ownerDocument.defaultView.getSelection(),a=r.anchorNode,p=r.isCollapsed,h=(null==a?void 0:a.nodeType)!==Node.TEXT_NODE&&(null==a?void 0:a.nodeType)!==Node.ELEMENT_NODE;if(null!=a&&!h){if(a.nodeType===Node.TEXT_NODE&&(null!==a.previousSibling||null!==a.nextSibling)){var d=a.parentNode;if(null==d)return;a.nodeValue=d.textContent;for(var g=d.firstChild;null!=g;g=g.nextSibling)g!==a&&d.removeChild(g)}var y=a.textContent,v=t._latestEditorState,m=l(u(a)),_=i.decode(m),b=_.blockKey,S=_.decoratorKey,w=_.leafKey,x=v.getBlockTree(b).getIn([S,"leaves",w]),k=x.start,C=x.end,E=v.getCurrentContent(),O=E.getBlockForKey(b),D=O.getText().slice(k,C);if(y.endsWith("\n\n")&&(y=y.slice(0,-1)),y!==D){var K,T,M,A,I=v.getSelection(),B=I.merge({anchorOffset:k,focusOffset:C,isBackward:!1}),L=O.getEntityAt(k),R=s(L)?E.getEntity(L):null,N="MUTABLE"===(null!=R?R.getMutability():null),F=N?"spellcheck-change":"apply-entity",P=n.replaceText(E,B,y,O.getInlineStyleAt(k),N?O.getEntityAt(k):null);if(f)K=r.anchorOffset,T=r.focusOffset,A=(M=k+Math.min(K,T))+Math.abs(K-T),K=M,T=A;else{var z=y.length-D.length;M=I.getStartOffset(),A=I.getEndOffset(),K=p?A+z:M,T=A+z}var j=P.merge({selectionBefore:E.getSelectionAfter(),selectionAfter:I.merge({anchorOffset:K,focusOffset:T})});t.update(o.push(v,j,F))}else{var U=e.nativeEvent.inputType;if(U){var q=function(t,e){return"deleteContentBackward"===t?c(e):e}(U,v);if(q!==v)return t.restoreEditorDOM(),void t.update(q)}}}}},46397:(t,e,r)=>{"use strict";var n=r(42307),i=r(14289),o=r(47387),a=r(25399),s=r(83751),u=r(4856),c=r(42177),l=r(49779),f=r(51050),p=r(13767),h=r(77978),d=r(67217),g=r(8425),y=r(62800),v=r(13998),m=r(53318),_=r(87051),b=o.isOptionKeyCommand,S=u.isBrowser("Chrome");t.exports=function(t,e){var r=e.which,o=t._latestEditorState;function u(r){var n=t.props[r];return!!n&&(n(e),!0)}switch(r){case a.RETURN:if(e.preventDefault(),t.props.handleReturn&&c(t.props.handleReturn(e,o)))return;break;case a.ESC:if(e.preventDefault(),u("onEscape"))return;break;case a.TAB:if(u("onTab"))return;break;case a.UP:if(u("onUpArrow"))return;break;case a.RIGHT:if(u("onRightArrow"))return;break;case a.DOWN:if(u("onDownArrow"))return;break;case a.LEFT:if(u("onLeftArrow"))return;break;case a.SPACE:S&&b(e)&&e.preventDefault()}var w=t.props.keyBindingFn(e);if(null!=w&&""!==w)if("undo"!==w){if(e.preventDefault(),!t.props.handleKeyCommand||!c(t.props.handleKeyCommand(w,o,e.timeStamp))){var x=function(t,e,r){switch(t){case"redo":return i.redo(e);case"delete":return v(e);case"delete-word":return p(e);case"backspace":return y(e);case"backspace-word":return f(e);case"backspace-to-start-of-line":return l(e,r);case"split-block":return h(e);case"transpose-characters":return m(e);case"move-selection-to-start-of-block":return g(e);case"move-selection-to-end-of-block":return d(e);case"secondary-cut":return s.cut(e);case"secondary-paste":return s.paste(e);default:return e}}(w,o,e);x!==o&&t.update(x)}}else _(e,o,t.update);else if(r===a.SPACE&&S&&b(e)){var k=n.replaceText(o.getCurrentContent(),o.getSelection()," ");t.update(i.push(o,k,"insert-characters"))}}},6089:(t,e,r)=>{"use strict";var n=r(10329),i=r(4516),o=r(44891),a=r(42307),s=r(45712),u=r(14289),c=r(41947),l=r(42128),f=r(21738),p=r(42177),h=r(44300);function d(t,e,r){var n=a.replaceWithFragment(t.getCurrentContent(),t.getSelection(),e);return u.push(t,n.set("entityMap",r),"insert-fragment")}t.exports=function(t,e){e.preventDefault();var r=new o(e.clipboardData);if(!r.isRichText()){var g=r.getFiles(),y=r.getText();if(g.length>0){if(t.props.handlePastedFiles&&p(t.props.handlePastedFiles(g)))return;return void f(g,(function(e){if(e=e||y){var r=t._latestEditorState,o=h(e),f=i.create({style:r.getCurrentInlineStyle(),entity:l(r.getCurrentContent(),r.getSelection())}),p=c.getCurrentBlockType(r),d=s.processText(o,f,p),g=n.createFromArray(d),v=a.replaceWithFragment(r.getCurrentContent(),r.getSelection(),g);t.update(u.push(r,v,"insert-fragment"))}}))}}var v=[],m=r.getText(),_=r.getHTML(),b=t._latestEditorState;if(t.props.formatPastedText){var S=t.props.formatPastedText(m,_);m=S.text,_=S.html}if(!t.props.handlePastedText||!p(t.props.handlePastedText(m,_,b))){if(m&&(v=h(m)),!t.props.stripPastedStyles){var w,x=t.getClipboard();if(!t.props.formatPastedText&&r.isRichText()&&x){if(-1!==(null===(w=_)||void 0===w?void 0:w.indexOf(t.getEditorKey()))||1===v.length&&1===x.size&&x.first().getText()===m)return void t.update(d(t._latestEditorState,x))}else if(x&&r.types.includes("com.apple.webarchive")&&!r.types.includes("text/html")&&function(t,e){return t.length===e.size&&e.valueSeq().every((function(e,r){return e.getText()===t[r]}))}(v,x))return void t.update(d(t._latestEditorState,x));if(_){var k=s.processHTML(_,t.props.blockRenderMap);if(k){var C=k.contentBlocks,E=k.entityMap;if(C){var O=n.createFromArray(C);return void t.update(d(t._latestEditorState,O,E))}}}t.setClipboard(null)}if(v.length){var D=i.create({style:b.getCurrentInlineStyle(),entity:l(b.getCurrentContent(),b.getSelection())}),K=c.getCurrentBlockType(b),T=s.processText(v,D,K),M=n.createFromArray(T);t.update(d(t._latestEditorState,M))}}}},14507:(t,e,r)=>{"use strict";var n=r(97432),i=r(14289),o=r(84907),a=r(1244);t.exports=function(t){if(t._blockSelectEvents||t._latestEditorState!==t.props.editorState){if(t._blockSelectEvents){var e=t.props.editorState.getSelection();n.logBlockedSelectionEvent({anonymizedDom:"N/A",extraParams:JSON.stringify({stacktrace:(new Error).stack}),selectionState:JSON.stringify(e.toJS())})}}else{var r=t.props.editorState,s=a(r,o(t)),u=s.selectionState;u!==r.getSelection()&&(r=s.needsRecovery?i.forceSelection(r,u):i.acceptSelection(r,u),t.update(r))}}},56265:(t,e,r)=>{"use strict";var n=r(86155),i=r(38935).strlen;t.exports=function(t,e){var r=[];return t.findEntityRanges((function(t){return!!t.getEntity()}),(function(o,a){var s=t.getText(),u=t.getEntityAt(o);r.push({offset:i(s.slice(0,o)),length:i(s.slice(o,a)),key:Number(e[n.stringify(u)])})})),r}},31487:(t,e,r)=>{"use strict";var n=r(38935),i=r(29407),o=function(t,e){return t===e},a=function(t){return!!t},s=[];t.exports=function(t){var e=t.getCharacterList().map((function(t){return t.getStyle()})).toList(),r=e.flatten().toSet().map((function(r){return function(t,e,r){var s=[],u=e.map((function(t){return t.has(r)})).toList();return i(u,o,a,(function(e,i){var o=t.getText();s.push({offset:n.strlen(o.slice(0,e)),length:n.strlen(o.slice(e,i)),style:r})})),s}(t,e,r)}));return Array.prototype.concat.apply(s,r.toJS())}},88182:(t,e,r)=>{"use strict";var n=r(38935),i=r(75795),o=r(6092),a=r(73759);function s(t,e){for(var r=1/0,n=1/0,i=-1/0,o=-1/0,a=0;a<t.length;a++){var s=t[a];0!==s.width&&1!==s.width&&(r=Math.min(r,s.top),n=Math.min(n,s.bottom),i=Math.max(i,s.top),o=Math.max(o,s.bottom))}return i<=n&&i-r<e&&o-n<e}function u(t){switch(t.nodeType){case Node.DOCUMENT_TYPE_NODE:return 0;case Node.TEXT_NODE:case Node.PROCESSING_INSTRUCTION_NODE:case Node.COMMENT_NODE:return t.length;default:return t.childNodes.length}}t.exports=function(t){t.collapsed||a(!1);var e=(t=t.cloneRange()).startContainer;1!==e.nodeType&&(e=e.parentNode);var r=function(t){var e=getComputedStyle(t),r=i(t),n=r.createElement("div");n.style.fontFamily=e.fontFamily,n.style.fontSize=e.fontSize,n.style.fontStyle=e.fontStyle,n.style.fontWeight=e.fontWeight,n.style.lineHeight=e.lineHeight,n.style.position="absolute",n.textContent="M";var o=r.body;o||a(!1),o.appendChild(n);var s=n.getBoundingClientRect();return o.removeChild(n),s.height}(e),c=t.endContainer,l=t.endOffset;for(t.setStart(t.startContainer,0);s(o(t),r)&&(c=t.startContainer,l=t.startOffset,c.parentNode||a(!1),t.setStartBefore(c),1!==c.nodeType||"inline"===getComputedStyle(c).display););for(var f=c,p=l-1;;){for(var h=f.nodeValue,d=p;d>=0;d--)if(!(null!=h&&d>0&&n.isSurrogatePair(h,d-1))){if(t.setStart(f,d),!s(o(t),r))break;c=f,l=d}if(-1===d||0===f.childNodes.length)break;p=u(f=f.childNodes[d])}return t.setStart(c,l),t}},69270:(t,e,r)=>{"use strict";var n=r(75795),i=r(93578);t.exports=function(t){for(var e=t;e&&e!==n(t).documentElement;){var r=i(e);if(null!=r)return r;e=e.parentNode}return null}},29407:t=>{"use strict";t.exports=function(t,e,r,n){if(t.size){var i=0;t.reduce((function(t,o,a){return e(t,o)||(r(t)&&n(i,a),i=a),o})),r(t.last())&&n(i,t.count())}}},25027:t=>{"use strict";var e={},r=Math.pow(2,24);t.exports=function(){for(var t;void 0===t||e.hasOwnProperty(t)||!isNaN(+t);)t=Math.floor(Math.random()*r).toString(32);return e[t]=!0,t}},81446:(t,e,r)=>{"use strict";var n=r(5195),i=r(64994),o=r(73759);function a(t,e,r,a,s,u,c){var l=r.getStartOffset(),f=r.getEndOffset(),p=t.__get(s).getMutability(),h=c?l:f;if("MUTABLE"===p)return r;var d=i(e,s).filter((function(t){return h<=t.end&&h>=t.start}));1!=d.length&&o(!1);var g=d[0];if("IMMUTABLE"===p)return r.merge({anchorOffset:g.start,focusOffset:g.end,isBackward:!1});u||(c?f=g.end:l=g.start);var y=n.getRemovalRange(l,f,e.getText().slice(g.start,g.end),g.start,a);return r.merge({anchorOffset:y.start,focusOffset:y.end,isBackward:!1})}t.exports=function(t,e,r,n,i){var o=n.getStartOffset(),s=n.getEndOffset(),u=e.getEntityAt(o),c=r.getEntityAt(s-1);if(!u&&!c)return n;var l=n;if(u&&u===c)l=a(t,e,l,i,u,!0,!0);else if(u&&c){var f=a(t,e,l,i,u,!1,!0),p=a(t,r,l,i,c,!1,!1);l=l.merge({anchorOffset:f.getAnchorOffset(),focusOffset:p.getFocusOffset(),isBackward:!1})}else if(u){var h=a(t,e,l,i,u,!1,!0);l=l.merge({anchorOffset:h.getStartOffset(),isBackward:!1})}else if(c){var d=a(t,r,l,i,c,!1,!1);l=l.merge({focusOffset:d.getEndOffset(),isBackward:!1})}return l}},84907:(t,e,r)=>{"use strict";var n=r(73759),i=r(20717);t.exports=function(t){var e=t.editorContainer;return e||n(!1),i(e.firstChild)||n(!1),e.firstChild}},88687:(t,e,r)=>{"use strict";var n=r(98555),i=r(14017);t.exports=function(t,e){var r=e.getStartKey(),o=e.getStartOffset(),a=e.getEndKey(),s=e.getEndOffset(),u=i(t,e).getBlockMap(),c=u.keySeq(),l=c.indexOf(r),f=c.indexOf(a)+1;return n(u.slice(l,f).map((function(t,e){var n=t.getText(),i=t.getCharacterList();return r===a?t.merge({text:n.slice(o,s),characterList:i.slice(o,s)}):e===r?t.merge({text:n.slice(o),characterList:i.slice(o)}):e===a?t.merge({text:n.slice(0,s),characterList:i.slice(0,s)}):t})))}},75795:t=>{"use strict";t.exports=function(t){return t&&t.ownerDocument?t.ownerDocument:document}},41714:(t,e,r)=>{"use strict";var n=r(47387),i=r(25399),o=r(4856),a=o.isPlatform("Mac OS X"),s=a&&o.isBrowser("Firefox < 29"),u=n.hasCommandModifier,c=n.isCtrlKeyCommand;function l(t){return a&&t.altKey||c(t)}t.exports=function(t){switch(t.keyCode){case 66:return u(t)?"bold":null;case 68:return c(t)?"delete":null;case 72:return c(t)?"backspace":null;case 73:return u(t)?"italic":null;case 74:return u(t)?"code":null;case 75:return a&&c(t)?"secondary-cut":null;case 77:case 79:return c(t)?"split-block":null;case 84:return a&&c(t)?"transpose-characters":null;case 85:return u(t)?"underline":null;case 87:return a&&c(t)?"backspace-word":null;case 89:return c(t)?a?"secondary-paste":"redo":null;case 90:return function(t){return u(t)?t.shiftKey?"redo":"undo":null}(t)||null;case i.RETURN:return"split-block";case i.DELETE:return function(t){return!a&&t.shiftKey?null:l(t)?"delete-word":"delete"}(t);case i.BACKSPACE:return function(t){return u(t)&&a?"backspace-to-start-of-line":l(t)?"backspace-word":"backspace"}(t);case i.LEFT:return s&&u(t)?"move-selection-to-start-of-block":null;case i.RIGHT:return s&&u(t)?"move-selection-to-end-of-block":null;default:return null}}},1244:(t,e,r)=>{"use strict";var n=r(8101);t.exports=function(t,e){var r=e.ownerDocument.defaultView.getSelection(),i=r.anchorNode,o=r.anchorOffset,a=r.focusNode,s=r.focusOffset;return 0===r.rangeCount||null==i||null==a?{selectionState:t.getSelection().set("hasFocus",!1),needsRecovery:!1}:n(t,e,i,o,a,s)}},8101:(t,e,r)=>{"use strict";var n=r(69270),i=r(93578),o=r(94486),a=r(73759),s=r(84368),u=r(22045);function c(t,e,r){var o=e,c=n(o);if(null!=c||t&&(t===o||t.firstChild===o)||a(!1),t===o&&(o=o.firstChild,s(o)||a(!1),"true"!==o.getAttribute("data-contents")&&a(!1),r>0&&(r=o.childNodes.length)),0===r){var f=null;if(null!=c)f=c;else{var p=function(t){for(;t.firstChild&&(s(t.firstChild)&&"true"===t.firstChild.getAttribute("data-blocks")||i(t.firstChild));)t=t.firstChild;return t}(o);f=u(i(p))}return{key:f,offset:0}}var h=o.childNodes[r-1],d=null,g=null;if(i(h)){var y=function(t){for(;t.lastChild&&(s(t.lastChild)&&"true"===t.lastChild.getAttribute("data-blocks")||i(t.lastChild));)t=t.lastChild;return t}(h);d=u(i(y)),g=l(y)}else d=u(c),g=l(h);return{key:d,offset:g}}function l(t){var e=t.textContent;return"\n"===e?0:e.length}t.exports=function(t,e,r,i,a,s){var l=r.nodeType===Node.TEXT_NODE,f=a.nodeType===Node.TEXT_NODE;if(l&&f)return{selectionState:o(t,u(n(r)),i,u(n(a)),s),needsRecovery:!1};var p=null,h=null,d=!0;return l?(p={key:u(n(r)),offset:i},h=c(e,a,s)):f?(h={key:u(n(a)),offset:s},p=c(e,r,i)):(p=c(e,r,i),h=c(e,a,s),r===a&&i===s&&(d=!!r.firstChild&&"BR"!==r.firstChild.nodeName)),{selectionState:o(t,p.key,p.offset,h.key,h.offset),needsRecovery:d}}},42128:(t,e,r)=>{"use strict";var n=r(99407).notEmptyKey;function i(t,e){return n(e)&&"MUTABLE"===t.__get(e).getMutability()?e:null}t.exports=function(t,e){var r;if(e.isCollapsed()){var n=e.getAnchorKey(),o=e.getAnchorOffset();return o>0?(r=t.getBlockForKey(n).getEntityAt(o-1))!==t.getBlockForKey(n).getEntityAt(o)?null:i(t.getEntityMap(),r):null}var a=e.getStartKey(),s=e.getStartOffset(),u=t.getBlockForKey(a);return r=s===u.getLength()?null:u.getEntityAt(s),i(t.getEntityMap(),r)}},94882:(t,e,r)=>{"use strict";var n=r(88687);t.exports=function(t){var e=t.getSelection();return e.isCollapsed()?null:n(t.getCurrentContent(),e)}},39506:(t,e,r)=>{"use strict";var n=r(67953);t.exports=function(t,e){if(!(t instanceof n))return null;var r=t.getNextSiblingKey();if(r)return r;var i=t.getParentKey();if(!i)return null;for(var o=e.get(i);o&&!o.getNextSiblingKey();){var a=o.getParentKey();o=a?e.get(a):null}return o?o.getNextSiblingKey():null}},96495:t=>{"use strict";t.exports=function(t){return Object.keys(t).map((function(e){return t[e]}))}},98056:(t,e,r)=>{"use strict";var n=r(6092);t.exports=function(t){var e=n(t),r=0,i=0,o=0,a=0;if(e.length){if(e.length>1&&0===e[0].width){var s=e[1];r=s.top,i=s.right,o=s.bottom,a=s.left}else{var u=e[0];r=u.top,i=u.right,o=u.bottom,a=u.left}for(var c=1;c<e.length;c++){var l=e[c];0!==l.height&&0!==l.width&&(r=Math.min(r,l.top),i=Math.max(i,l.right),o=Math.max(o,l.bottom),a=Math.min(a,l.left))}}return{top:r,right:i,bottom:o,left:a,width:i-a,height:o-r}}},6092:(t,e,r)=>{"use strict";var n=r(4856),i=r(73759),o=n.isBrowser("Chrome")?function(t){for(var e=t.cloneRange(),r=[],n=t.endContainer;null!=n;n=n.parentNode){var o=n===t.commonAncestorContainer;o?e.setStart(t.startContainer,t.startOffset):e.setStart(e.endContainer,0);var a,s=Array.from(e.getClientRects());if(r.push(s),o)return r.reverse(),(a=[]).concat.apply(a,r);e.setEndBefore(n)}i(!1)}:function(t){return Array.from(t.getClientRects())};t.exports=o},64994:(t,e,r)=>{"use strict";var n=r(73759);t.exports=function(t,e){var r=[];return t.findEntityRanges((function(t){return t.getEntity()===e}),(function(t,e){r.push({start:t,end:e})})),r.length||n(!1),r}},69769:(t,e,r)=>{"use strict";var n=r(4856),i=r(73759),o=n.isBrowser("IE <= 9");t.exports=function(t){var e,r=null;return!o&&document.implementation&&document.implementation.createHTMLDocument&&((e=document.implementation.createHTMLDocument("foo")).documentElement||i(!1),e.documentElement.innerHTML=t,r=e.getElementsByTagName("body")[0]),r}},93578:(t,e,r)=>{"use strict";var n=r(84368);t.exports=function t(e){if(n(e)){var r=e,i=r.getAttribute("data-offset-key");if(i)return i;for(var o=0;o<r.childNodes.length;o++){var a=t(r.childNodes[o]);if(a)return a}}return null}},21738:(t,e,r)=>{"use strict";var n=r(73759),i=/\.textClipping$/,o={"text/plain":!0,"text/html":!0,"text/rtf":!0};t.exports=function(t,e){var a=0,s=[];t.forEach((function(u){!function(t,e){if(!r.g.FileReader||t.type&&!(t.type in o))e("");else{if(""===t.type){var a="";return i.test(t.name)&&(a=t.name.replace(i,"")),void e(a)}var s=new FileReader;s.onload=function(){var t=s.result;"string"!=typeof t&&n(!1),e(t)},s.onerror=function(){e("")},s.readAsText(t)}}(u,(function(r){a++,r&&s.push(r.slice(0,5e3)),a==t.length&&e(s.join("\r"))}))}))}},94486:(t,e,r)=>{"use strict";var n=r(22146),i=r(22045);t.exports=function(t,e,r,o,a){var s=i(t.getSelection());if(!e||!o)return s;var u=n.decode(e),c=u.blockKey,l=t.getBlockTree(c),f=l&&l.getIn([u.decoratorKey,"leaves",u.leafKey]),p=n.decode(o),h=p.blockKey,d=t.getBlockTree(h),g=d&&d.getIn([p.decoratorKey,"leaves",p.leafKey]);if(!f||!g)return s;var y=f.get("start"),v=g.get("start"),m=f?y+r:null,_=g?v+a:null;if(s.getAnchorKey()===c&&s.getAnchorOffset()===m&&s.getFocusKey()===h&&s.getFocusOffset()===_)return s;var b=!1;if(c===h){var S=f.get("end"),w=g.get("end");b=v===y&&w===S?a<r:v<y}else b=t.getCurrentContent().getBlockMap().keySeq().skipUntil((function(t){return t===c||t===h})).first()===h;return s.merge({anchorKey:c,anchorOffset:m,focusKey:h,focusOffset:_,isBackward:b})}},96629:(t,e,r)=>{"use strict";var n=r(98056);t.exports=function(t){var e=t.getSelection();if(!e.rangeCount)return null;var r=e.getRangeAt(0),i=n(r),o=i.top,a=i.right,s=i.bottom,u=i.left;return 0===o&&0===a&&0===s&&0===u?null:i}},48083:t=>{"use strict";t.exports=function(t){return t&&t.ownerDocument&&t.ownerDocument.defaultView?t.ownerDocument.defaultView:window}},68642:t=>{"use strict";t.exports=function(t){return!("undefined"==typeof window||!window.__DRAFT_GKX||!window.__DRAFT_GKX[t])}},54542:(t,e,r)=>{"use strict";var n=r(10329),i=r(67953),o=r(43393),a=r(40779),s=r(73759),u=r(98555),c=o.List;t.exports=function(t,e,r){var o=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"REPLACE_WITH_NEW_DATA";e.isCollapsed()||s(!1);var l=t.getBlockMap(),f=u(r),p=e.getStartKey(),h=e.getStartOffset(),d=l.get(p);return d instanceof i&&(d.getChildKeys().isEmpty()||s(!1)),1===f.size?function(t,e,r,n,i,o){var s=arguments.length>6&&void 0!==arguments[6]?arguments[6]:"REPLACE_WITH_NEW_DATA",u=r.get(i),c=u.getText(),l=u.getCharacterList(),f=i,p=o+n.getText().length,h=null;switch(s){case"MERGE_OLD_DATA_TO_NEW_DATA":h=n.getData().merge(u.getData());break;case"REPLACE_WITH_NEW_DATA":h=n.getData()}var d=u.getType();c&&"unstyled"===d&&(d=n.getType());var g=u.merge({text:c.slice(0,o)+n.getText()+c.slice(o),characterList:a(l,n.getCharacterList(),o),type:d,data:h});return t.merge({blockMap:r.set(i,g),selectionBefore:e,selectionAfter:e.merge({anchorKey:f,anchorOffset:p,focusKey:f,focusOffset:p,isBackward:!1})})}(t,e,l,f.first(),p,h,o):function(t,e,r,o,a,s){var u=r.first()instanceof i,l=[],f=o.size,p=r.get(a),h=o.first(),d=o.last(),g=d.getLength(),y=d.getKey(),v=u&&(!p.getChildKeys().isEmpty()||!h.getChildKeys().isEmpty());r.forEach((function(t,e){e===a?(v?l.push(t):l.push(function(t,e,r){var n=t.getText(),i=t.getCharacterList(),o=n.slice(0,e),a=i.slice(0,e),s=r.first();return t.merge({text:o+s.getText(),characterList:a.concat(s.getCharacterList()),type:o?t.getType():s.getType(),data:s.getData()})}(t,s,o)),o.slice(v?0:1,f-1).forEach((function(t){return l.push(t)})),l.push(function(t,e,r){var n=t.getText(),i=t.getCharacterList(),o=n.length,a=n.slice(e,o),s=i.slice(e,o),u=r.last();return u.merge({text:u.getText()+a,characterList:u.getCharacterList().concat(s),data:u.getData()})}(t,s,o))):l.push(t)}));var m=n.createFromArray(l);return u&&(m=function(t,e,r,n){return t.withMutations((function(e){var i=r.getKey(),o=n.getKey(),a=r.getNextSiblingKey(),s=r.getParentKey(),u=function(t,e){var r=t.getKey(),n=t,i=[];for(e.get(r)&&i.push(r);n&&n.getNextSiblingKey();){var o=n.getNextSiblingKey();if(!o)break;i.push(o),n=e.get(o)}return i}(n,t),l=u[u.length-1];if(e.get(o)?(e.setIn([i,"nextSibling"],o),e.setIn([o,"prevSibling"],i)):(e.setIn([i,"nextSibling"],n.getNextSiblingKey()),e.setIn([n.getNextSiblingKey(),"prevSibling"],i)),e.setIn([l,"nextSibling"],a),a&&e.setIn([a,"prevSibling"],l),u.forEach((function(t){return e.setIn([t,"parent"],s)})),s){var f=t.get(s).getChildKeys(),p=f.indexOf(i)+1,h=f.toArray();h.splice.apply(h,[p,0].concat(u)),e.setIn([s,"children"],c(h))}}))}(m,0,p,h)),t.merge({blockMap:m,selectionBefore:e,selectionAfter:e.merge({anchorKey:y,anchorOffset:g,focusKey:y,focusOffset:g,isBackward:!1})})}(t,e,l,f,p,h)}},40779:t=>{"use strict";t.exports=function(t,e,r){var n=t;if(r===n.count())e.forEach((function(t){n=n.push(t)}));else if(0===r)e.reverse().forEach((function(t){n=n.unshift(t)}));else{var i=n.slice(0,r),o=n.slice(r);n=i.concat(e,o).toList()}return n}},18467:(t,e,r)=>{"use strict";var n=r(43393),i=r(40779),o=r(73759),a=n.Repeat;t.exports=function(t,e,r,n){e.isCollapsed()||o(!1);var s=null;if(null!=r&&(s=r.length),null==s||0===s)return t;var u=t.getBlockMap(),c=e.getStartKey(),l=e.getStartOffset(),f=u.get(c),p=f.getText(),h=f.merge({text:p.slice(0,l)+r+p.slice(l,f.getLength()),characterList:i(f.getCharacterList(),a(n,s).toList(),l)}),d=l+s;return t.merge({blockMap:u.set(c,h),selectionAfter:e.merge({anchorOffset:d,focusOffset:d})})}},84368:t=>{"use strict";t.exports=function(t){return!(!t||!t.ownerDocument)&&t.nodeType===Node.ELEMENT_NODE}},42177:t=>{"use strict";t.exports=function(t){return"handled"===t||!0===t}},78241:(t,e,r)=>{"use strict";var n=r(84368);t.exports=function(t){return!(!t||!t.ownerDocument)&&n(t)&&"A"===t.nodeName}},16581:(t,e,r)=>{"use strict";var n=r(84368);t.exports=function(t){return!(!t||!t.ownerDocument)&&n(t)&&"BR"===t.nodeName}},20717:t=>{"use strict";t.exports=function(t){return!(!t||!t.ownerDocument)&&(t.ownerDocument.defaultView?t instanceof t.ownerDocument.defaultView.HTMLElement:t instanceof HTMLElement)}},35039:(t,e,r)=>{"use strict";var n=r(84368);t.exports=function(t){return!(!t||!t.ownerDocument)&&n(t)&&"IMG"===t.nodeName}},80809:t=>{"use strict";t.exports=function(t){if(!t||!("ownerDocument"in t))return!1;if("ownerDocument"in t){var e=t;if(!e.ownerDocument.defaultView)return e instanceof Node;if(e instanceof e.ownerDocument.defaultView.Node)return!0}return!1}},40258:t=>{"use strict";t.exports=function(t){var e=t.getSelection(),r=e.getAnchorKey(),n=t.getBlockTree(r),i=e.getStartOffset(),o=!1;return n.some((function(t){return i===t.get("start")?(o=!0,!0):i<t.get("end")&&t.get("leaves").some((function(t){var e=t.get("start");return i===e&&(o=!0,!0)}))})),o}},17797:(t,e,r)=>{"use strict";var n=r(25399);t.exports=function(t){return t.which===n.RETURN&&(t.getModifierState("Shift")||t.getModifierState("Alt")||t.getModifierState("Control"))}},49779:(t,e,r)=>{"use strict";var n=r(14289),i=r(88182),o=r(8101),a=r(53268),s=r(14730);t.exports=function(t,e){var r=s(t,(function(t){var r=t.getSelection();if(r.isCollapsed()&&0===r.getAnchorOffset())return a(t,1);var n=e.currentTarget.ownerDocument.defaultView.getSelection().getRangeAt(0);return n=i(n),o(t,null,n.endContainer,n.endOffset,n.startContainer,n.startOffset).selectionState}),"backward");return r===t.getCurrentContent()?t:n.push(t,r,"remove-range")}},51050:(t,e,r)=>{"use strict";var n=r(73932),i=r(14289),o=r(53268),a=r(14730);t.exports=function(t){var e=a(t,(function(t){var e=t.getSelection(),r=e.getStartOffset();if(0===r)return o(t,1);var i=e.getStartKey(),a=t.getCurrentContent().getBlockForKey(i).getText().slice(0,r),s=n.getBackward(a);return o(t,s.length||1)}),"backward");return e===t.getCurrentContent()?t:i.push(t,e,"remove-range")}},13767:(t,e,r)=>{"use strict";var n=r(73932),i=r(14289),o=r(19417),a=r(14730);t.exports=function(t){var e=a(t,(function(t){var e=t.getSelection(),r=e.getStartOffset(),i=e.getStartKey(),a=t.getCurrentContent().getBlockForKey(i).getText().slice(r),s=n.getForward(a);return o(t,s.length||1)}),"forward");return e===t.getCurrentContent()?t:i.push(t,e,"remove-range")}},77978:(t,e,r)=>{"use strict";var n=r(42307),i=r(14289);t.exports=function(t){var e=n.splitBlock(t.getCurrentContent(),t.getSelection());return i.push(t,e,"split-block")}},67217:(t,e,r)=>{"use strict";var n=r(14289);t.exports=function(t){var e=t.getSelection(),r=e.getEndKey(),i=t.getCurrentContent().getBlockForKey(r).getLength();return n.set(t,{selection:e.merge({anchorKey:r,anchorOffset:i,focusKey:r,focusOffset:i,isBackward:!1}),forceSelection:!0})}},8425:(t,e,r)=>{"use strict";var n=r(14289);t.exports=function(t){var e=t.getSelection(),r=e.getStartKey();return n.set(t,{selection:e.merge({anchorKey:r,anchorOffset:0,focusKey:r,focusOffset:0,isBackward:!1}),forceSelection:!0})}},62800:(t,e,r)=>{"use strict";var n=r(14289),i=r(38935),o=r(53268),a=r(14730);t.exports=function(t){var e=a(t,(function(t){var e=t.getSelection(),r=t.getCurrentContent(),n=e.getAnchorKey(),a=e.getAnchorOffset(),s=r.getBlockForKey(n).getText()[a-1];return o(t,s?i.getUTF16Length(s,0):1)}),"backward");if(e===t.getCurrentContent())return t;var r=t.getSelection();return n.push(t,e.set("selectionBefore",r),r.isCollapsed()?"backspace-character":"remove-range")}},13998:(t,e,r)=>{"use strict";var n=r(14289),i=r(38935),o=r(19417),a=r(14730);t.exports=function(t){var e=a(t,(function(t){var e=t.getSelection(),r=t.getCurrentContent(),n=e.getAnchorKey(),a=e.getAnchorOffset(),s=r.getBlockForKey(n).getText()[a];return o(t,s?i.getUTF16Length(s,0):1)}),"forward");if(e===t.getCurrentContent())return t;var r=t.getSelection();return n.push(t,e.set("selectionBefore",r),r.isCollapsed()?"delete-character":"remove-range")}},53318:(t,e,r)=>{"use strict";var n=r(42307),i=r(14289),o=r(88687);t.exports=function(t){var e=t.getSelection();if(!e.isCollapsed())return t;var r=e.getAnchorOffset();if(0===r)return t;var a,s,u=e.getAnchorKey(),c=t.getCurrentContent(),l=c.getBlockForKey(u).getLength();if(l<=1)return t;r===l?(a=e.set("anchorOffset",r-1),s=e):s=(a=e.set("focusOffset",r+1)).set("anchorOffset",r+1);var f=o(c,a),p=n.removeRange(c,a,"backward"),h=p.getSelectionAfter(),d=h.getAnchorOffset()-1,g=h.merge({anchorOffset:d,focusOffset:d}),y=n.replaceWithFragment(p,g,f),v=i.push(t,y,"insert-fragment");return i.acceptSelection(v,s)}},87051:(t,e,r)=>{"use strict";var n=r(14289);t.exports=function(t,e,r){var i=n.undo(e);if("spellcheck-change"!==e.getLastChangeType())t.preventDefault(),e.getNativelyRenderedContent()?(r(n.set(e,{nativelyRenderedContent:null})),setTimeout((function(){r(i)}),0)):r(i);else{var o=i.getCurrentContent();r(n.set(i,{nativelyRenderedContent:o}))}}},57429:(t,e,r)=>{"use strict";var n=r(43393).Map;t.exports=function(t,e,r){var i=e.getStartKey(),o=e.getEndKey(),a=t.getBlockMap(),s=a.toSeq().skipUntil((function(t,e){return e===i})).takeUntil((function(t,e){return e===o})).concat(n([[o,a.get(o)]])).map(r);return t.merge({blockMap:a.merge(s),selectionBefore:e,selectionAfter:e})}},61173:(t,e,r)=>{"use strict";var n=r(67953),i=r(39506),o=r(43393),a=r(73759),s=o.OrderedMap,u=o.List,c=function(t,e,r){if(t){var n=e.get(t);n&&e.set(t,r(n))}},l=function(t,e,r,n,i){if(!i)return t;var o="after"===n,a=e.getKey(),s=r.getKey(),l=e.getParentKey(),f=e.getNextSiblingKey(),p=e.getPrevSiblingKey(),h=r.getParentKey(),d=o?r.getNextSiblingKey():s,g=o?s:r.getPrevSiblingKey();return t.withMutations((function(t){c(l,t,(function(t){var e=t.getChildKeys();return t.merge({children:e.delete(e.indexOf(a))})})),c(p,t,(function(t){return t.merge({nextSibling:f})})),c(f,t,(function(t){return t.merge({prevSibling:p})})),c(d,t,(function(t){return t.merge({prevSibling:a})})),c(g,t,(function(t){return t.merge({nextSibling:a})})),c(h,t,(function(t){var e=t.getChildKeys(),r=e.indexOf(s),n=o?r+1:0!==r?r-1:0,i=e.toArray();return i.splice(n,0,a),t.merge({children:u(i)})})),c(a,t,(function(t){return t.merge({nextSibling:d,prevSibling:g,parent:h})}))}))};t.exports=function(t,e,r,o){"replace"===o&&a(!1);var u=r.getKey(),c=e.getKey();c===u&&a(!1);var f=t.getBlockMap(),p=e instanceof n,h=[e],d=f.delete(c);p&&(h=[],d=f.withMutations((function(t){var r=e.getNextSiblingKey(),n=i(e,t);t.toSeq().skipUntil((function(t){return t.getKey()===c})).takeWhile((function(t){var e=t.getKey(),i=e===c,o=r&&e!==r,a=!r&&t.getParentKey()&&(!n||e!==n);return!!(i||o||a)})).forEach((function(e){h.push(e),t.delete(e.getKey())}))})));var g=d.toSeq().takeUntil((function(t){return t===r})),y=d.toSeq().skipUntil((function(t){return t===r})).skip(1),v=h.map((function(t){return[t.getKey(),t]})),m=s();if("before"===o){var _=t.getBlockBefore(u);_&&_.getKey()===e.getKey()&&a(!1),m=g.concat([].concat(v,[[u,r]]),y).toOrderedMap()}else if("after"===o){var b=t.getBlockAfter(u);b&&b.getKey()===c&&a(!1),m=g.concat([[u,r]].concat(v),y).toOrderedMap()}return t.merge({blockMap:l(m,e,r,o,p),selectionBefore:t.getSelectionAfter(),selectionAfter:t.getSelectionAfter().merge({anchorKey:c,focusKey:c})})}},53268:(t,e,r)=>{"use strict";r(63620),t.exports=function(t,e){var r=t.getSelection(),n=t.getCurrentContent(),i=r.getStartKey(),o=r.getStartOffset(),a=i,s=0;if(e>o){var u=n.getKeyBefore(i);null==u?a=i:(a=u,s=n.getBlockForKey(u).getText().length)}else s=o-e;return r.merge({focusKey:a,focusOffset:s,isBackward:!0})}},19417:(t,e,r)=>{"use strict";r(63620),t.exports=function(t,e){var r,n=t.getSelection(),i=n.getStartKey(),o=n.getStartOffset(),a=t.getCurrentContent(),s=i;return e>a.getBlockForKey(i).getText().length-o?(s=a.getKeyAfter(i),r=0):r=o+e,n.merge({focusKey:s,focusOffset:r})}},98555:(t,e,r)=>{"use strict";var n=r(67953),i=r(25027),o=r(43393).OrderedMap;t.exports=function(t){return t.first()instanceof n?function(t){var e,r={};return o(t.withMutations((function(t){t.forEach((function(n,o){var a=n.getKey(),s=n.getNextSiblingKey(),u=n.getPrevSiblingKey(),c=n.getChildKeys(),l=n.getParentKey(),f=i();if(r[a]=f,s&&(t.get(s)?t.setIn([s,"prevSibling"],f):t.setIn([a,"nextSibling"],null)),u&&(t.get(u)?t.setIn([u,"nextSibling"],f):t.setIn([a,"prevSibling"],null)),l&&t.get(l)){var p=t.get(l).getChildKeys();t.setIn([l,"children"],p.set(p.indexOf(n.getKey()),f))}else t.setIn([a,"parent"],null),e&&(t.setIn([e.getKey(),"nextSibling"],f),t.setIn([a,"prevSibling"],r[e.getKey()])),e=t.get(a);c.forEach((function(e){t.get(e)?t.setIn([e,"parent"],f):t.setIn([a,"children"],n.getChildKeys().filter((function(t){return t!==e})))}))}))})).toArray().map((function(t){return[r[t.getKey()],t.set("key",r[t.getKey()])]})))}(t):function(t){return o(t.toArray().map((function(t){var e=i();return[e,t.set("key",e)]})))}(t)}},14017:(t,e,r)=>{"use strict";var n=r(4516),i=r(29407),o=r(73759);function a(t,e,r){var a=e.getCharacterList(),s=r>0?a.get(r-1):void 0,u=r<a.count()?a.get(r):void 0,c=s?s.getEntity():void 0,l=u?u.getEntity():void 0;if(l&&l===c&&"MUTABLE"!==t.__get(l).getMutability()){for(var f,p=function(t,e,r){var n;return i(t,(function(t,e){return t.getEntity()===e.getEntity()}),(function(t){return t.getEntity()===e}),(function(t,e){t<=r&&e>=r&&(n={start:t,end:e})})),"object"!=typeof n&&o(!1),n}(a,l,r),h=p.start,d=p.end;h<d;)f=a.get(h),a=a.set(h,n.applyEntity(f,null)),h++;return e.set("characterList",a)}return e}t.exports=function(t,e){var r=t.getBlockMap(),n=t.getEntityMap(),i={},o=e.getStartKey(),s=e.getStartOffset(),u=r.get(o),c=a(n,u,s);c!==u&&(i[o]=c);var l=e.getEndKey(),f=e.getEndOffset(),p=r.get(l);o===l&&(p=c);var h=a(n,p,f);return h!==p&&(i[l]=h),Object.keys(i).length?t.merge({blockMap:r.merge(i),selectionAfter:e}):t.set("selectionAfter",e)}},54879:(t,e,r)=>{"use strict";var n=r(67953),i=r(39506),o=r(43393),a=(o.List,o.Map),s=function(t,e,r){if(t){var n=e.get(t);n&&e.set(t,r(n))}},u=function(t,e){var r=[];if(!t)return r;for(var n=e.get(t);n&&n.getParentKey();){var i=n.getParentKey();i&&r.push(i),n=i?e.get(i):null}return r},c=function(t,e,r){if(!t)return null;for(var n=r.get(t.getKey()).getNextSiblingKey();n&&!e.get(n);)n=r.get(n).getNextSiblingKey()||null;return n},l=function(t,e,r){if(!t)return null;for(var n=r.get(t.getKey()).getPrevSiblingKey();n&&!e.get(n);)n=r.get(n).getPrevSiblingKey()||null;return n};t.exports=function(t,e){if(e.isCollapsed())return t;var r,o=t.getBlockMap(),f=e.getStartKey(),p=e.getStartOffset(),h=e.getEndKey(),d=e.getEndOffset(),g=o.get(f),y=o.get(h),v=g instanceof n,m=[];if(v){var _=y.getChildKeys(),b=u(h,o);y.getNextSiblingKey()&&(m=m.concat(b)),_.isEmpty()||(m=m.concat(b.concat([h]))),m=m.concat(u(i(y,o),o))}r=g===y?function(t,e,r){if(0===e)for(;e<r;)t=t.shift(),e++;else if(r===t.count())for(;r>e;)t=t.pop(),r--;else{var n=t.slice(0,e),i=t.slice(r);t=n.concat(i).toList()}return t}(g.getCharacterList(),p,d):g.getCharacterList().slice(0,p).concat(y.getCharacterList().slice(d));var S=g.merge({text:g.getText().slice(0,p)+y.getText().slice(d),characterList:r}),w=v&&0===p&&0===d&&y.getParentKey()===f&&null==y.getPrevSiblingKey()?a([[f,null]]):o.toSeq().skipUntil((function(t,e){return e===f})).takeUntil((function(t,e){return e===h})).filter((function(t,e){return-1===m.indexOf(e)})).concat(a([[h,null]])).map((function(t,e){return e===f?S:null})),x=o.merge(w).filter((function(t){return!!t}));return v&&g!==y&&(x=function(t,e,r,n){return t.withMutations((function(o){if(s(e.getKey(),o,(function(t){return t.merge({nextSibling:c(t,o,n),prevSibling:l(t,o,n)})})),s(r.getKey(),o,(function(t){return t.merge({nextSibling:c(t,o,n),prevSibling:l(t,o,n)})})),u(e.getKey(),n).forEach((function(t){return s(t,o,(function(t){return t.merge({children:t.getChildKeys().filter((function(t){return o.get(t)})),nextSibling:c(t,o,n),prevSibling:l(t,o,n)})}))})),s(e.getNextSiblingKey(),o,(function(t){return t.merge({prevSibling:e.getPrevSiblingKey()})})),s(e.getPrevSiblingKey(),o,(function(t){return t.merge({nextSibling:c(t,o,n)})})),s(r.getNextSiblingKey(),o,(function(t){return t.merge({prevSibling:l(t,o,n)})})),s(r.getPrevSiblingKey(),o,(function(t){return t.merge({nextSibling:r.getNextSiblingKey()})})),u(r.getKey(),n).forEach((function(t){s(t,o,(function(t){return t.merge({children:t.getChildKeys().filter((function(t){return o.get(t)})),nextSibling:c(t,o,n),prevSibling:l(t,o,n)})}))})),function(t,e){var r=[];if(!t)return r;for(var n=i(t,e);n&&e.get(n);){var o=e.get(n);r.push(n),n=o.getParentKey()?i(o,e):null}return r}(r,n).forEach((function(t){return s(t,o,(function(t){return t.merge({nextSibling:c(t,o,n),prevSibling:l(t,o,n)})}))})),null==t.get(e.getKey())&&null!=t.get(r.getKey())&&r.getParentKey()===e.getKey()&&null==r.getPrevSiblingKey()){var a=e.getPrevSiblingKey();s(r.getKey(),o,(function(t){return t.merge({prevSibling:a})})),s(a,o,(function(t){return t.merge({nextSibling:r.getKey()})}));var f=a?t.get(a):null,p=f?f.getParentKey():null;if(e.getChildKeys().forEach((function(t){s(t,o,(function(t){return t.merge({parent:p})}))})),null!=p){var h=t.get(p);s(p,o,(function(t){return t.merge({children:h.getChildKeys().concat(e.getChildKeys())})}))}s(e.getChildKeys().find((function(e){return null===t.get(e).getNextSiblingKey()})),o,(function(t){return t.merge({nextSibling:e.getNextSiblingKey()})}))}}))}(x,g,y,o)),t.merge({blockMap:x,selectionBefore:e,selectionAfter:e.merge({anchorKey:f,anchorOffset:p,focusKey:f,focusOffset:p,isBackward:!1})})}},14730:(t,e,r)=>{"use strict";var n=r(42307),i=r(68642)("draft_tree_data_support");t.exports=function(t,e,r){var o=t.getSelection(),a=t.getCurrentContent(),s=o,u=o.getAnchorKey(),c=o.getFocusKey(),l=a.getBlockForKey(u);if(i&&"forward"===r&&u!==c)return a;if(o.isCollapsed()){if("forward"===r){if(t.isSelectionAtEndOfContent())return a;if(i&&o.getAnchorOffset()===a.getBlockForKey(u).getLength()){var f=a.getBlockForKey(l.nextSibling);if(!f||0===f.getLength())return a}}else if(t.isSelectionAtStartOfContent())return a;if((s=e(t))===o)return a}return n.removeRange(a,s,r)}},55283:t=>{"use strict";var e=new RegExp("\r","g");t.exports=function(t){return t.replace(e,"")}},45412:(t,e,r)=>{"use strict";var n=r(5880),i=r(97432),o=r(4856),a=r(67476),s=r(31003),u=r(75795),c=r(73759),l=r(84368),f=o.isBrowser("IE");function p(t,e){if(!t)return"[empty]";var r=h(t,e);return r.nodeType===Node.TEXT_NODE?r.textContent:(l(r)||c(!1),r.outerHTML)}function h(t,e){var r=void 0!==e?e(t):[];if(t.nodeType===Node.TEXT_NODE){var n=t.textContent.length;return u(t).createTextNode("[text "+n+(r.length?" | "+r.join(", "):"")+"]")}var i=t.cloneNode();1===i.nodeType&&r.length&&i.setAttribute("data-labels",r.join(", "));for(var o=t.childNodes,a=0;a<o.length;a++)i.appendChild(h(o[a],e));return i}function d(t,e){for(var r=t,n=r;r;){if(l(r)&&n.hasAttribute("contenteditable"))return p(r,e);n=r=r.parentNode}return"Could not find contentEditable parent of node"}function g(t){return null===t.nodeValue?t.childNodes.length:t.nodeValue.length}function y(t,e,r,n){var o=s();if(t.extend&&null!=e&&a(o,e)){r>g(e)&&i.logSelectionStateFailure({anonymizedDom:d(e),extraParams:JSON.stringify({offset:r}),selectionState:JSON.stringify(n.toJS())});var u=e===t.focusNode;try{t.rangeCount>0&&t.extend&&t.extend(e,r)}catch(a){throw i.logSelectionStateFailure({anonymizedDom:d(e,(function(e){var r=[];return e===o&&r.push("active element"),e===t.anchorNode&&r.push("selection anchor node"),e===t.focusNode&&r.push("selection focus node"),r})),extraParams:JSON.stringify({activeElementName:o?o.nodeName:null,nodeIsFocus:e===t.focusNode,nodeWasFocus:u,selectionRangeCount:t.rangeCount,selectionAnchorNodeName:t.anchorNode?t.anchorNode.nodeName:null,selectionAnchorOffset:t.anchorOffset,selectionFocusNodeName:t.focusNode?t.focusNode.nodeName:null,selectionFocusOffset:t.focusOffset,message:a?""+a:null,offset:r},null,2),selectionState:JSON.stringify(n.toJS(),null,2)}),a}}else if(e&&t.rangeCount>0){var c=t.getRangeAt(0);c.setEnd(e,r),t.addRange(c.cloneRange())}}function v(t,e,r,o){var a=u(e).createRange();if(r>g(e)&&(i.logSelectionStateFailure({anonymizedDom:d(e),extraParams:JSON.stringify({offset:r}),selectionState:JSON.stringify(o.toJS())}),n.handleExtensionCausedError()),a.setStart(e,r),f)try{t.addRange(a)}catch(t){}else t.addRange(a)}t.exports={setDraftEditorSelection:function(t,e,r,n,i){var o=u(e);if(a(o.documentElement,e)){var s=o.defaultView.getSelection(),c=t.getAnchorKey(),l=t.getAnchorOffset(),f=t.getFocusKey(),p=t.getFocusOffset(),h=t.getIsBackward();if(!s.extend&&h){var d=c,g=l;c=f,l=p,f=d,p=g,h=!1}var m=c===r&&n<=l&&i>=l,_=f===r&&n<=p&&i>=p;if(m&&_)return s.removeAllRanges(),v(s,e,l-n,t),void y(s,e,p-n,t);if(h){if(_&&(s.removeAllRanges(),v(s,e,p-n,t)),m){var b=s.focusNode,S=s.focusOffset;s.removeAllRanges(),v(s,e,l-n,t),y(s,b,S,t)}}else m&&(s.removeAllRanges(),v(s,e,l-n,t)),_&&y(s,e,p-n,t)}},addFocusToSelection:y}},36043:(t,e,r)=>{"use strict";var n=r(67953),i=r(25027),o=r(43393),a=r(73759),s=r(57429),u=o.List,c=o.Map,l=function(t,e,r){if(t){var n=e.get(t);n&&e.set(t,r(n))}};t.exports=function(t,e){e.isCollapsed()||a(!1);var r=e.getAnchorKey(),o=t.getBlockMap(),f=o.get(r),p=f.getText();if(!p){var h=f.getType();if("unordered-list-item"===h||"ordered-list-item"===h)return s(t,e,(function(t){return t.merge({type:"unstyled",depth:0})}))}var d=e.getAnchorOffset(),g=f.getCharacterList(),y=i(),v=f instanceof n,m=f.merge({text:p.slice(0,d),characterList:g.slice(0,d)}),_=m.merge({key:y,text:p.slice(d),characterList:g.slice(d),data:c()}),b=o.toSeq().takeUntil((function(t){return t===f})),S=o.toSeq().skipUntil((function(t){return t===f})).rest(),w=b.concat([[r,m],[y,_]],S).toOrderedMap();return v&&(f.getChildKeys().isEmpty()||a(!1),w=function(t,e,r){return t.withMutations((function(t){var n=e.getKey(),i=r.getKey();l(e.getParentKey(),t,(function(t){var e=t.getChildKeys(),r=e.indexOf(n)+1,o=e.toArray();return o.splice(r,0,i),t.merge({children:u(o)})})),l(e.getNextSiblingKey(),t,(function(t){return t.merge({prevSibling:i})})),l(n,t,(function(t){return t.merge({nextSibling:i})})),l(i,t,(function(t){return t.merge({prevSibling:n})}))}))}(w,m,_)),t.merge({blockMap:w,selectionBefore:e,selectionAfter:e.merge({anchorKey:y,anchorOffset:0,focusKey:y,focusOffset:0,isBackward:!1})})}},44300:t=>{"use strict";var e=/\r\n?|\n/g;t.exports=function(t){return t.split(e)}},76363:t=>{"use strict";t.exports=function(){return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,(function(t){var e=16*Math.random()|0;return("x"==t?e:3&e|8).toString(16)}))}},44891:(t,e,r)=>{"use strict";var n=r(51006),i=r(89825),o=r(60139),a=new RegExp("\r\n","g"),s={"text/rtf":1,"text/html":1};function u(t){if("file"==t.kind)return t.getAsFile()}var c=function(){function t(t){this.data=t,this.types=t.types?i(t.types):[]}var e=t.prototype;return e.isRichText=function(){return!(!this.getHTML()||!this.getText())||!this.isImage()&&this.types.some((function(t){return s[t]}))},e.getText=function(){var t;return this.data.getData&&(this.types.length?-1!=this.types.indexOf("text/plain")&&(t=this.data.getData("text/plain")):t=this.data.getData("Text")),t?t.replace(a,"\n"):null},e.getHTML=function(){if(this.data.getData){if(!this.types.length)return this.data.getData("Text");if(-1!=this.types.indexOf("text/html"))return this.data.getData("text/html")}},e.isLink=function(){return this.types.some((function(t){return-1!=t.indexOf("Url")||-1!=t.indexOf("text/uri-list")||t.indexOf("text/x-moz-url")}))},e.getLink=function(){return this.data.getData?-1!=this.types.indexOf("text/x-moz-url")?this.data.getData("text/x-moz-url").split("\n")[0]:-1!=this.types.indexOf("text/uri-list")?this.data.getData("text/uri-list"):this.data.getData("url"):null},e.isImage=function(){var t=this.types.some((function(t){return-1!=t.indexOf("application/x-moz-file")}));if(t)return!0;for(var e=this.getFiles(),r=0;r<e.length;r++){var i=e[r].type;if(!n.isImage(i))return!1}return!0},e.getCount=function(){return this.data.hasOwnProperty("items")?this.data.items.length:this.data.hasOwnProperty("mozItemCount")?this.data.mozItemCount:this.data.files?this.data.files.length:null},e.getFiles=function(){return this.data.items?Array.prototype.slice.call(this.data.items).map(u).filter(o.thatReturnsArgument):this.data.files?Array.prototype.slice.call(this.data.files):[]},e.hasFiles=function(){return this.getFiles().length>0},t}();t.exports=c},25399:t=>{"use strict";t.exports={BACKSPACE:8,TAB:9,RETURN:13,ALT:18,ESC:27,SPACE:32,PAGE_UP:33,PAGE_DOWN:34,END:35,HOME:36,LEFT:37,UP:38,RIGHT:39,DOWN:40,DELETE:46,COMMA:188,PERIOD:190,A:65,Z:90,ZERO:48,NUMPAD_0:96,NUMPAD_9:105}},51006:t=>{"use strict";var e={isImage:function(t){return"image"===r(t)[0]},isJpeg:function(t){var n=r(t);return e.isImage(t)&&("jpeg"===n[1]||"pjpeg"===n[1])}};function r(t){return t.split("/")}t.exports=e},65994:t=>{"use strict";function e(t,e){return!!e&&(t===e.documentElement||t===e.body)}var r={getTop:function(t){var r=t.ownerDocument;return e(t,r)?r.body.scrollTop||r.documentElement.scrollTop:t.scrollTop},setTop:function(t,r){var n=t.ownerDocument;e(t,n)?n.body.scrollTop=n.documentElement.scrollTop=r:t.scrollTop=r},getLeft:function(t){var r=t.ownerDocument;return e(t,r)?r.body.scrollLeft||r.documentElement.scrollLeft:t.scrollLeft},setLeft:function(t,r){var n=t.ownerDocument;e(t,n)?n.body.scrollLeft=n.documentElement.scrollLeft=r:t.scrollLeft=r}};t.exports=r},19051:(t,e,r)=>{"use strict";function n(t,e){var r=i.get(t,e);return"auto"===r||"scroll"===r}var i={get:r(85466),getScrollParent:function(t){if(!t)return null;for(var e=t.ownerDocument;t&&t!==e.body;){if(n(t,"overflow")||n(t,"overflowY")||n(t,"overflowX"))return t;t=t.parentNode}return e.defaultView||e.parentWindow}};t.exports=i},65724:t=>{"use strict";t.exports={getPunctuation:function(){return"[.,+*?$|#{}()'\\^\\-\\[\\]\\\\\\/!@%\"~=<>_:;・、。〈-】〔-〟：-？！-／［-｀｛-･⸮؟٪-٬؛،؍﴾﴿᠁।၊။‐-‧‰-⁞¡-±´-¸º»¿]"}}},61425:t=>{"use strict";var e=function(){function t(t){var e,r;r=void 0,(e="_uri")in this?Object.defineProperty(this,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):this[e]=r,this._uri=t}return t.prototype.toString=function(){return this._uri},t}();t.exports=e},54191:(t,e,r)=>{"use strict";var n=r(16633),i=r(73759),o="֐־׀׃׆׈-׏א-ת׫-ׯװ-ײ׳-״׵-׿߀-߉ߊ-ߪߴ-ߵߺ߻-߿ࠀ-ࠕࠚࠤࠨ࠮-࠯࠰-࠾࠿ࡀ-ࡘ࡜-࡝࡞࡟-࢟‏יִײַ-ﬨשׁ-זּ﬷טּ-לּ﬽מּ﬿נּ-סּ﭂ףּ-פּ﭅צּ-ﭏ",a="؈؋؍؛؜؝؞-؟ؠ-ؿـف-ي٭ٮ-ٯٱ-ۓ۔ەۥ-ۦۮ-ۯۺ-ۼ۽-۾ۿ܀-܍܎܏ܐܒ-ܯ݋-݌ݍ-ޥޱ޲-޿ࢠ-ࢲࢳ-ࣣﭐ-ﮱ﮲-﯁﯂-﯒ﯓ-ﴽ﵀-﵏ﵐ-ﶏ﶐-﶑ﶒ-ﷇ﷈-﷏ﷰ-ﷻ﷼﷾-﷿ﹰ-ﹴ﹵ﹶ-ﻼ﻽-﻾",s=new RegExp("[A-Za-zªµºÀ-ÖØ-öø-ƺƻƼ-ƿǀ-ǃǄ-ʓʔʕ-ʯʰ-ʸʻ-ˁː-ˑˠ-ˤˮͰ-ͳͶ-ͷͺͻ-ͽͿΆΈ-ΊΌΎ-ΡΣ-ϵϷ-ҁ҂Ҋ-ԯԱ-Ֆՙ՚-՟ա-և։ःऄ-हऻऽा-ीॉ-ौॎ-ॏॐक़-ॡ।-॥०-९॰ॱॲ-ঀং-ঃঅ-ঌএ-ঐও-নপ-রলশ-হঽা-ীে-ৈো-ৌৎৗড়-ঢ়য়-ৡ০-৯ৰ-ৱ৴-৹৺ਃਅ-ਊਏ-ਐਓ-ਨਪ-ਰਲ-ਲ਼ਵ-ਸ਼ਸ-ਹਾ-ੀਖ਼-ੜਫ਼੦-੯ੲ-ੴઃઅ-ઍએ-ઑઓ-નપ-રલ-ળવ-હઽા-ીૉો-ૌૐૠ-ૡ૦-૯૰ଂ-ଃଅ-ଌଏ-ଐଓ-ନପ-ରଲ-ଳଵ-ହଽାୀେ-ୈୋ-ୌୗଡ଼-ଢ଼ୟ-ୡ୦-୯୰ୱ୲-୷ஃஅ-ஊஎ-ஐஒ-கங-சஜஞ-டண-தந-பம-ஹா-ிு-ூெ-ைொ-ௌௐௗ௦-௯௰-௲ఁ-ఃఅ-ఌఎ-ఐఒ-నప-హఽు-ౄౘ-ౙౠ-ౡ౦-౯౿ಂ-ಃಅ-ಌಎ-ಐಒ-ನಪ-ಳವ-ಹಽಾಿೀ-ೄೆೇ-ೈೊ-ೋೕ-ೖೞೠ-ೡ೦-೯ೱ-ೲം-ഃഅ-ഌഎ-ഐഒ-ഺഽാ-ീെ-ൈൊ-ൌൎൗൠ-ൡ൦-൯൰-൵൹ൺ-ൿං-ඃඅ-ඖක-නඳ-රලව-ෆා-ෑෘ-ෟ෦-෯ෲ-ෳ෴ก-ะา-ำเ-ๅๆ๏๐-๙๚-๛ກ-ຂຄງ-ຈຊຍດ-ທນ-ຟມ-ຣລວສ-ຫອ-ະາ-ຳຽເ-ໄໆ໐-໙ໜ-ໟༀ༁-༃༄-༒༓༔༕-༗༚-༟༠-༩༪-༳༴༶༸༾-༿ཀ-ཇཉ-ཬཿ྅ྈ-ྌ྾-࿅࿇-࿌࿎-࿏࿐-࿔࿕-࿘࿙-࿚က-ဪါ-ာေးျ-ြဿ၀-၉၊-၏ၐ-ၕၖ-ၗၚ-ၝၡၢ-ၤၥ-ၦၧ-ၭၮ-ၰၵ-ႁႃ-ႄႇ-ႌႎႏ႐-႙ႚ-ႜ႞-႟Ⴀ-ჅჇჍა-ჺ჻ჼჽ-ቈቊ-ቍቐ-ቖቘቚ-ቝበ-ኈኊ-ኍነ-ኰኲ-ኵኸ-ኾዀዂ-ዅወ-ዖዘ-ጐጒ-ጕጘ-ፚ፠-፨፩-፼ᎀ-ᎏᎠ-Ᏼᐁ-ᙬ᙭-᙮ᙯ-ᙿᚁ-ᚚᚠ-ᛪ᛫-᛭ᛮ-ᛰᛱ-ᛸᜀ-ᜌᜎ-ᜑᜠ-ᜱ᜵-᜶ᝀ-ᝑᝠ-ᝬᝮ-ᝰក-ឳាើ-ៅះ-ៈ។-៖ៗ៘-៚ៜ០-៩᠐-᠙ᠠ-ᡂᡃᡄ-ᡷᢀ-ᢨᢪᢰ-ᣵᤀ-ᤞᤣ-ᤦᤩ-ᤫᤰ-ᤱᤳ-ᤸ᥆-᥏ᥐ-ᥭᥰ-ᥴᦀ-ᦫᦰ-ᧀᧁ-ᧇᧈ-ᧉ᧐-᧙᧚ᨀ-ᨖᨙ-ᨚ᨞-᨟ᨠ-ᩔᩕᩗᩡᩣ-ᩤᩭ-ᩲ᪀-᪉᪐-᪙᪠-᪦ᪧ᪨-᪭ᬄᬅ-ᬳᬵᬻᬽ-ᭁᭃ-᭄ᭅ-ᭋ᭐-᭙᭚-᭠᭡-᭪᭴-᭼ᮂᮃ-ᮠᮡᮦ-ᮧ᮪ᮮ-ᮯ᮰-᮹ᮺ-ᯥᯧᯪ-ᯬᯮ᯲-᯳᯼-᯿ᰀ-ᰣᰤ-ᰫᰴ-ᰵ᰻-᰿᱀-᱉ᱍ-ᱏ᱐-᱙ᱚ-ᱷᱸ-ᱽ᱾-᱿᳀-᳇᳓᳡ᳩ-ᳬᳮ-ᳱᳲ-ᳳᳵ-ᳶᴀ-ᴫᴬ-ᵪᵫ-ᵷᵸᵹ-ᶚᶛ-ᶿḀ-ἕἘ-Ἕἠ-ὅὈ-Ὅὐ-ὗὙὛὝὟ-ώᾀ-ᾴᾶ-ᾼιῂ-ῄῆ-ῌῐ-ΐῖ-Ίῠ-Ῥῲ-ῴῶ-ῼ‎ⁱⁿₐ-ₜℂℇℊ-ℓℕℙ-ℝℤΩℨK-ℭℯ-ℴℵ-ℸℹℼ-ℿⅅ-ⅉⅎ⅏Ⅰ-ↂↃ-ↄↅ-ↈ⌶-⍺⎕⒜-ⓩ⚬⠀-⣿Ⰰ-Ⱞⰰ-ⱞⱠ-ⱻⱼ-ⱽⱾ-ⳤⳫ-ⳮⳲ-ⳳⴀ-ⴥⴧⴭⴰ-ⵧⵯ⵰ⶀ-ⶖⶠ-ⶦⶨ-ⶮⶰ-ⶶⶸ-ⶾⷀ-ⷆⷈ-ⷎⷐ-ⷖⷘ-ⷞ々〆〇〡-〩〮-〯〱-〵〸-〺〻〼ぁ-ゖゝ-ゞゟァ-ヺー-ヾヿㄅ-ㄭㄱ-ㆎ㆐-㆑㆒-㆕㆖-㆟ㆠ-ㆺㇰ-ㇿ㈀-㈜㈠-㈩㈪-㉇㉈-㉏㉠-㉻㉿㊀-㊉㊊-㊰㋀-㋋㋐-㋾㌀-㍶㍻-㏝㏠-㏾㐀-䶵一-鿌ꀀ-ꀔꀕꀖ-ꒌꓐ-ꓷꓸ-ꓽ꓾-꓿ꔀ-ꘋꘌꘐ-ꘟ꘠-꘩ꘪ-ꘫꙀ-ꙭꙮꚀ-ꚛꚜ-ꚝꚠ-ꛥꛦ-ꛯ꛲-꛷Ꜣ-ꝯꝰꝱ-ꞇ꞉-꞊Ꞌ-ꞎꞐ-ꞭꞰ-Ʇꟷꟸ-ꟹꟺꟻ-ꠁꠃ-ꠅꠇ-ꠊꠌ-ꠢꠣ-ꠤꠧ꠰-꠵꠶-꠷ꡀ-ꡳꢀ-ꢁꢂ-ꢳꢴ-ꣃ꣎-꣏꣐-꣙ꣲ-ꣷ꣸-꣺ꣻ꤀-꤉ꤊ-ꤥ꤮-꤯ꤰ-ꥆꥒ-꥓꥟ꥠ-ꥼꦃꦄ-ꦲꦴ-ꦵꦺ-ꦻꦽ-꧀꧁-꧍ꧏ꧐-꧙꧞-꧟ꧠ-ꧤꧦꧧ-ꧯ꧰-꧹ꧺ-ꧾꨀ-ꨨꨯ-ꨰꨳ-ꨴꩀ-ꩂꩄ-ꩋꩍ꩐-꩙꩜-꩟ꩠ-ꩯꩰꩱ-ꩶ꩷-꩹ꩺꩻꩽꩾ-ꪯꪱꪵ-ꪶꪹ-ꪽꫀꫂꫛ-ꫜꫝ꫞-꫟ꫠ-ꫪꫫꫮ-ꫯ꫰-꫱ꫲꫳ-ꫴꫵꬁ-ꬆꬉ-ꬎꬑ-ꬖꬠ-ꬦꬨ-ꬮꬰ-ꭚ꭛ꭜ-ꭟꭤ-ꭥꯀ-ꯢꯣ-ꯤꯦ-ꯧꯩ-ꯪ꯫꯬꯰-꯹가-힣ힰ-ퟆퟋ-ퟻ-豈-舘並-龎ﬀ-ﬆﬓ-ﬗＡ-Ｚａ-ｚｦ-ｯｰｱ-ﾝﾞ-ﾟﾠ-ﾾￂ-ￇￊ-ￏￒ-ￗￚ-ￜ"+o+a+"]"),u=new RegExp("["+o+a+"]");function c(t){var e=s.exec(t);return null==e?null:e[0]}function l(t){var e=c(t);return null==e?n.NEUTRAL:u.exec(e)?n.RTL:n.LTR}function f(t,e){if(e=e||n.NEUTRAL,!t.length)return e;var r=l(t);return r===n.NEUTRAL?e:r}function p(t,e){return e||(e=n.getGlobalDir()),n.isStrong(e)||i(!1),f(t,e)}var h={firstStrongChar:c,firstStrongCharDir:l,resolveBlockDir:f,getDirection:p,isDirectionLTR:function(t,e){return p(t,e)===n.LTR},isDirectionRTL:function(t,e){return p(t,e)===n.RTL}};t.exports=h},16633:(t,e,r)=>{"use strict";var n=r(73759),i="LTR",o="RTL",a=null;function s(t){return t===i||t===o}function u(t){return s(t)||n(!1),t===i?"ltr":"rtl"}function c(t){a=t}var l={NEUTRAL:"NEUTRAL",LTR:i,RTL:o,isStrong:s,getHTMLDir:u,getHTMLDirIfDifferent:function(t,e){return s(t)||n(!1),s(e)||n(!1),t===e?null:u(t)},setGlobalDir:c,initGlobalDir:function(){c(i)},getGlobalDir:function(){return a||this.initGlobalDir(),a||n(!1),a}};t.exports=l},7902:(t,e,r)=>{"use strict";function n(t,e,r){return e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var i=r(54191),o=r(16633),a=r(73759),s=function(){function t(t){n(this,"_defaultDir",void 0),n(this,"_lastDir",void 0),t?o.isStrong(t)||a(!1):t=o.getGlobalDir(),this._defaultDir=t,this.reset()}var e=t.prototype;return e.reset=function(){this._lastDir=this._defaultDir},e.getDirection=function(t){return this._lastDir=i.getDirection(t,this._lastDir),this._lastDir},t}();t.exports=s},38935:(t,e,r)=>{"use strict";var n=r(73759),i=55296,o=57343,a=/[\uD800-\uDFFF]/;function s(t){return i<=t&&t<=o}function u(t){return a.test(t)}function c(t,e){return 1+s(t.charCodeAt(e))}function l(t,e,r){if(e=e||0,r=void 0===r?1/0:r||0,!u(t))return t.substr(e,r);var n=t.length;if(n<=0||e>n||r<=0)return"";var i=0;if(e>0){for(;e>0&&i<n;e--)i+=c(t,i);if(i>=n)return""}else if(e<0){for(i=n;e<0&&0<i;e++)i-=c(t,i-1);i<0&&(i=0)}var o=n;if(r<n)for(o=i;r>0&&o<n;r--)o+=c(t,o);return t.substring(i,o)}var f={getCodePoints:function(t){for(var e=[],r=0;r<t.length;r+=c(t,r))e.push(t.codePointAt(r));return e},getUTF16Length:c,hasSurrogateUnit:u,isCodeUnitInSurrogateRange:s,isSurrogatePair:function(t,e){if(0<=e&&e<t.length||n(!1),e+1===t.length)return!1;var r=t.charCodeAt(e),a=t.charCodeAt(e+1);return i<=r&&r<=56319&&56320<=a&&a<=o},strlen:function(t){if(!u(t))return t.length;for(var e=0,r=0;r<t.length;r+=c(t,r))e++;return e},substring:function(t,e,r){(e=e||0)<0&&(e=0),(r=void 0===r?1/0:r||0)<0&&(r=0);var n=Math.abs(r-e);return l(t,e=e<r?e:r,n)},substr:l};t.exports=f},4856:(t,e,r)=>{"use strict";var n=r(95845),i=r(59859),o=r(79467),a=r(51767);function s(t,e,r,n){if(t===r)return!0;if(!r.startsWith(t))return!1;var o=r.slice(t.length);return!!e&&(o=n?n(o):o,i.contains(o,e))}function u(t){return"Windows"===n.platformName?t.replace(/^\s*NT/,""):t}var c={isBrowser:function(t){return s(n.browserName,n.browserFullVersion,t)},isBrowserArchitecture:function(t){return s(n.browserArchitecture,null,t)},isDevice:function(t){return s(n.deviceName,null,t)},isEngine:function(t){return s(n.engineName,n.engineVersion,t)},isPlatform:function(t){return s(n.platformName,n.platformFullVersion,t,u)},isPlatformArchitecture:function(t){return s(n.platformArchitecture,null,t)}};t.exports=o(c,a)},95845:(t,e,r)=>{"use strict";var n,i="Unknown",o=(new(r(42238))).getResult(),a=function(t){if(!t)return{major:"",minor:""};var e=t.split(".");return{major:e[0],minor:e[1]}}(o.browser.version),s={browserArchitecture:o.cpu.architecture||i,browserFullVersion:o.browser.version||i,browserMinorVersion:a.minor||i,browserName:o.browser.name||i,browserVersion:o.browser.major||i,deviceName:o.device.model||i,engineName:o.engine.name||i,engineVersion:o.engine.version||i,platformArchitecture:o.cpu.architecture||i,platformName:(n=o.os.name,{"Mac OS":"Mac OS X"}[n]||n||i),platformVersion:o.os.version||i,platformFullVersion:o.os.version||i};t.exports=s},59859:(t,e,r)=>{"use strict";var n=r(73759),i=/\./,o=/\|\|/,a=/\s+\-\s+/,s=/^(<=|<|=|>=|~>|~|>|)?\s*(.+)/,u=/^(\d*)(.*)/;function c(t,e){if(""===(t=t.trim()))return!0;var r,n=e.split(i),o=p(t),a=o.modifier,s=o.rangeComponents;switch(a){case"<":return l(n,s);case"<=":return-1===(r=m(n,s))||0===r;case">=":return f(n,s);case">":return 1===m(n,s);case"~":case"~>":return function(t,e){var r=e.slice(),n=e.slice();n.length>1&&n.pop();var i=n.length-1,o=parseInt(n[i],10);return h(o)&&(n[i]=o+1+""),f(t,r)&&l(t,n)}(n,s);default:return function(t,e){return 0===m(t,e)}(n,s)}}function l(t,e){return-1===m(t,e)}function f(t,e){var r=m(t,e);return 1===r||0===r}function p(t){var e=t.split(i),r=e[0].match(s);return r||n(!1),{modifier:r[1],rangeComponents:[r[2]].concat(e.slice(1))}}function h(t){return!isNaN(t)&&isFinite(t)}function d(t){return!p(t).modifier}function g(t,e){for(var r=t.length;r<e;r++)t[r]="0"}function y(t,e){var r=t.match(u)[1],n=e.match(u)[1],i=parseInt(r,10),o=parseInt(n,10);return h(i)&&h(o)&&i!==o?v(i,o):v(t,e)}function v(t,e){return typeof t!=typeof e&&n(!1),t>e?1:t<e?-1:0}function m(t,e){for(var r=function(t,e){g(t=t.slice(),(e=e.slice()).length);for(var r=0;r<e.length;r++){var n=e[r].match(/^[x*]$/i);if(n&&(e[r]=t[r]="0","*"===n[0]&&r===e.length-1))for(var i=r;i<t.length;i++)t[i]="0"}return g(e,t.length),[t,e]}(t,e),n=r[0],i=r[1],o=0;o<i.length;o++){var a=y(n[o],i[o]);if(a)return a}return 0}var _={contains:function(t,e){return function(t,e){var r=t.split(o);return r.length>1?r.some((function(t){return _.contains(t,e)})):function(t,e){var r=t.split(a);if(r.length>0&&r.length<=2||n(!1),1===r.length)return c(r[0],e);var i=r[0],o=r[1];return d(i)&&d(o)||n(!1),c(">="+i,e)&&c("<="+o,e)}(t=r[0].trim(),e)}(t.trim(),e.trim())}};t.exports=_},52297:t=>{"use strict";var e=/-(.)/g;t.exports=function(t){return t.replace(e,(function(t,e){return e.toUpperCase()}))}},67476:(t,e,r)=>{"use strict";var n=r(52334);t.exports=function t(e,r){return!(!e||!r)&&(e===r||!n(e)&&(n(r)?t(e,r.parentNode):"contains"in e?e.contains(r):!!e.compareDocumentPosition&&!!(16&e.compareDocumentPosition(r))))}},89825:(t,e,r)=>{"use strict";var n=r(73759);t.exports=function(t){return function(t){return!!t&&("object"==typeof t||"function"==typeof t)&&"length"in t&&!("setInterval"in t)&&"number"!=typeof t.nodeType&&(Array.isArray(t)||"callee"in t||"item"in t)}(t)?Array.isArray(t)?t.slice():function(t){var e=t.length;if((Array.isArray(t)||"object"!=typeof t&&"function"!=typeof t)&&n(!1),"number"!=typeof e&&n(!1),0===e||e-1 in t||n(!1),"function"==typeof t.callee&&n(!1),t.hasOwnProperty)try{return Array.prototype.slice.call(t)}catch(t){}for(var r=Array(e),i=0;i<e;i++)r[i]=t[i];return r}(t):[t]}},62620:t=>{"use strict";function e(t){return t.replace(/\//g,"-")}t.exports=function(t){return"object"==typeof t?Object.keys(t).filter((function(e){return t[e]})).map(e).join(" "):Array.prototype.map.call(arguments,e).join(" ")}},60139:t=>{"use strict";function e(t){return function(){return t}}var r=function(){};r.thatReturns=e,r.thatReturnsFalse=e(!1),r.thatReturnsTrue=e(!0),r.thatReturnsNull=e(null),r.thatReturnsThis=function(){return this},r.thatReturnsArgument=function(t){return t},t.exports=r},31003:t=>{"use strict";t.exports=function(t){if(void 0===(t=t||("undefined"!=typeof document?document:void 0)))return null;try{return t.activeElement||t.body}catch(e){return t.body}}},35179:t=>{"use strict";var e="undefined"!=typeof navigator&&navigator.userAgent.indexOf("AppleWebKit")>-1;t.exports=function(t){return(t=t||document).scrollingElement?t.scrollingElement:e||"CSS1Compat"!==t.compatMode?t.body:t.documentElement}},55258:(t,e,r)=>{"use strict";var n=r(23123);t.exports=function(t){var e=n(t);return{x:e.left,y:e.top,width:e.right-e.left,height:e.bottom-e.top}}},23123:(t,e,r)=>{"use strict";var n=r(67476);t.exports=function(t){var e=t.ownerDocument.documentElement;if(!("getBoundingClientRect"in t)||!n(e,t))return{left:0,right:0,top:0,bottom:0};var r=t.getBoundingClientRect();return{left:Math.round(r.left)-e.clientLeft,right:Math.round(r.right)-e.clientLeft,top:Math.round(r.top)-e.clientTop,bottom:Math.round(r.bottom)-e.clientTop}}},79749:(t,e,r)=>{"use strict";var n=r(35179),i=r(30787);t.exports=function(t){var e=n(t.ownerDocument||t.document);t.Window&&t instanceof t.Window&&(t=e);var r=i(t),o=t===e?t.ownerDocument.documentElement:t,a=t.scrollWidth-o.clientWidth,s=t.scrollHeight-o.clientHeight;return r.x=Math.max(0,Math.min(r.x,a)),r.y=Math.max(0,Math.min(r.y,s)),r}},85466:(t,e,r)=>{"use strict";var n=r(52297),i=r(89349);function o(t){return null==t?t:String(t)}t.exports=function(t,e){var r;if(window.getComputedStyle&&(r=window.getComputedStyle(t,null)))return o(r.getPropertyValue(i(e)));if(document.defaultView&&document.defaultView.getComputedStyle){if(r=document.defaultView.getComputedStyle(t,null))return o(r.getPropertyValue(i(e)));if("display"===e)return"none"}return t.currentStyle?o("float"===e?t.currentStyle.cssFloat||t.currentStyle.styleFloat:t.currentStyle[n(e)]):o(t.style&&t.style[n(e)])}},30787:t=>{"use strict";t.exports=function(t){return t.Window&&t instanceof t.Window?{x:t.pageXOffset||t.document.documentElement.scrollLeft,y:t.pageYOffset||t.document.documentElement.scrollTop}:{x:t.scrollLeft,y:t.scrollTop}}},70746:t=>{"use strict";function e(){var t;return document.documentElement&&(t=document.documentElement.clientWidth),!t&&document.body&&(t=document.body.clientWidth),t||0}function r(){var t;return document.documentElement&&(t=document.documentElement.clientHeight),!t&&document.body&&(t=document.body.clientHeight),t||0}function n(){return{width:window.innerWidth||e(),height:window.innerHeight||r()}}n.withoutScrollbars=function(){return{width:e(),height:r()}},t.exports=n},89349:t=>{"use strict";var e=/([A-Z])/g;t.exports=function(t){return t.replace(e,"-$1").toLowerCase()}},73759:t=>{"use strict";t.exports=function(t,e){for(var r=arguments.length,n=new Array(r>2?r-2:0),i=2;i<r;i++)n[i-2]=arguments[i];if(!t){var o;if(void 0===e)o=new Error("Minified exception occurred; use the non-minified dev environment for the full error message and additional helpful warnings.");else{var a=0;(o=new Error(e.replace(/%s/g,(function(){return String(n[a++])})))).name="Invariant Violation"}throw o.framesToPop=1,o}}},20901:t=>{"use strict";t.exports=function(t){var e=(t?t.ownerDocument||t:document).defaultView||window;return!(!t||!("function"==typeof e.Node?t instanceof e.Node:"object"==typeof t&&"number"==typeof t.nodeType&&"string"==typeof t.nodeName))}},52334:(t,e,r)=>{"use strict";var n=r(20901);t.exports=function(t){return n(t)&&3==t.nodeType}},71108:t=>{"use strict";t.exports=function(t){var e=t||"",r=arguments.length;if(r>1)for(var n=1;n<r;n++){var i=arguments[n];i&&(e=(e?e+" ":"")+i)}return e}},79467:t=>{"use strict";var e=Object.prototype.hasOwnProperty;t.exports=function(t,r,n){if(!t)return null;var i={};for(var o in t)e.call(t,o)&&(i[o]=r.call(n,t[o],o,t));return i}},51767:t=>{"use strict";t.exports=function(t){var e={};return function(r){return e.hasOwnProperty(r)||(e[r]=t.call(this,r)),e[r]}}},22045:t=>{"use strict";t.exports=function(t){if(null!=t)return t;throw new Error("Got unexpected null or undefined")}},56926:(t,e,r)=>{"use strict";r(24889),t.exports=r.g.setImmediate},63620:(t,e,r)=>{"use strict";var n=r(60139);t.exports=n},43393:function(t){t.exports=function(){"use strict";var t=Array.prototype.slice;function e(t,e){e&&(t.prototype=Object.create(e.prototype)),t.prototype.constructor=t}function r(t){return a(t)?t:G(t)}function n(t){return s(t)?t:J(t)}function i(t){return u(t)?t:X(t)}function o(t){return a(t)&&!c(t)?t:Y(t)}function a(t){return!(!t||!t[f])}function s(t){return!(!t||!t[p])}function u(t){return!(!t||!t[h])}function c(t){return s(t)||u(t)}function l(t){return!(!t||!t[d])}e(n,r),e(i,r),e(o,r),r.isIterable=a,r.isKeyed=s,r.isIndexed=u,r.isAssociative=c,r.isOrdered=l,r.Keyed=n,r.Indexed=i,r.Set=o;var f="@@__IMMUTABLE_ITERABLE__@@",p="@@__IMMUTABLE_KEYED__@@",h="@@__IMMUTABLE_INDEXED__@@",d="@@__IMMUTABLE_ORDERED__@@",g="delete",y=5,v=1<<y,m=v-1,_={},b={value:!1},S={value:!1};function w(t){return t.value=!1,t}function x(t){t&&(t.value=!0)}function k(){}function C(t,e){e=e||0;for(var r=Math.max(0,t.length-e),n=new Array(r),i=0;i<r;i++)n[i]=t[i+e];return n}function E(t){return void 0===t.size&&(t.size=t.__iterate(D)),t.size}function O(t,e){if("number"!=typeof e){var r=e>>>0;if(""+r!==e||4294967295===r)return NaN;e=r}return e<0?E(t)+e:e}function D(){return!0}function K(t,e,r){return(0===t||void 0!==r&&t<=-r)&&(void 0===e||void 0!==r&&e>=r)}function T(t,e){return A(t,e,0)}function M(t,e){return A(t,e,e)}function A(t,e,r){return void 0===t?r:t<0?Math.max(0,e+t):void 0===e?t:Math.min(e,t)}var I=0,B=1,L=2,R="function"==typeof Symbol&&Symbol.iterator,N="@@iterator",F=R||N;function P(t){this.next=t}function z(t,e,r,n){var i=0===t?e:1===t?r:[e,r];return n?n.value=i:n={value:i,done:!1},n}function j(){return{value:void 0,done:!0}}function U(t){return!!W(t)}function q(t){return t&&"function"==typeof t.next}function H(t){var e=W(t);return e&&e.call(t)}function W(t){var e=t&&(R&&t[R]||t[N]);if("function"==typeof e)return e}function V(t){return t&&"number"==typeof t.length}function G(t){return null==t?at():a(t)?t.toSeq():function(t){var e=ct(t)||"object"==typeof t&&new rt(t);if(!e)throw new TypeError("Expected Array or iterable object of values, or keyed object: "+t);return e}(t)}function J(t){return null==t?at().toKeyedSeq():a(t)?s(t)?t.toSeq():t.fromEntrySeq():st(t)}function X(t){return null==t?at():a(t)?s(t)?t.entrySeq():t.toIndexedSeq():ut(t)}function Y(t){return(null==t?at():a(t)?s(t)?t.entrySeq():t:ut(t)).toSetSeq()}P.prototype.toString=function(){return"[Iterator]"},P.KEYS=I,P.VALUES=B,P.ENTRIES=L,P.prototype.inspect=P.prototype.toSource=function(){return this.toString()},P.prototype[F]=function(){return this},e(G,r),G.of=function(){return G(arguments)},G.prototype.toSeq=function(){return this},G.prototype.toString=function(){return this.__toString("Seq {","}")},G.prototype.cacheResult=function(){return!this._cache&&this.__iterateUncached&&(this._cache=this.entrySeq().toArray(),this.size=this._cache.length),this},G.prototype.__iterate=function(t,e){return lt(this,t,e,!0)},G.prototype.__iterator=function(t,e){return ft(this,t,e,!0)},e(J,G),J.prototype.toKeyedSeq=function(){return this},e(X,G),X.of=function(){return X(arguments)},X.prototype.toIndexedSeq=function(){return this},X.prototype.toString=function(){return this.__toString("Seq [","]")},X.prototype.__iterate=function(t,e){return lt(this,t,e,!1)},X.prototype.__iterator=function(t,e){return ft(this,t,e,!1)},e(Y,G),Y.of=function(){return Y(arguments)},Y.prototype.toSetSeq=function(){return this},G.isSeq=ot,G.Keyed=J,G.Set=Y,G.Indexed=X;var $,Z,Q,tt="@@__IMMUTABLE_SEQ__@@";function et(t){this._array=t,this.size=t.length}function rt(t){var e=Object.keys(t);this._object=t,this._keys=e,this.size=e.length}function nt(t){this._iterable=t,this.size=t.length||t.size}function it(t){this._iterator=t,this._iteratorCache=[]}function ot(t){return!(!t||!t[tt])}function at(){return $||($=new et([]))}function st(t){var e=Array.isArray(t)?new et(t).fromEntrySeq():q(t)?new it(t).fromEntrySeq():U(t)?new nt(t).fromEntrySeq():"object"==typeof t?new rt(t):void 0;if(!e)throw new TypeError("Expected Array or iterable object of [k, v] entries, or keyed object: "+t);return e}function ut(t){var e=ct(t);if(!e)throw new TypeError("Expected Array or iterable object of values: "+t);return e}function ct(t){return V(t)?new et(t):q(t)?new it(t):U(t)?new nt(t):void 0}function lt(t,e,r,n){var i=t._cache;if(i){for(var o=i.length-1,a=0;a<=o;a++){var s=i[r?o-a:a];if(!1===e(s[1],n?s[0]:a,t))return a+1}return a}return t.__iterateUncached(e,r)}function ft(t,e,r,n){var i=t._cache;if(i){var o=i.length-1,a=0;return new P((function(){var t=i[r?o-a:a];return a++>o?{value:void 0,done:!0}:z(e,n?t[0]:a-1,t[1])}))}return t.__iteratorUncached(e,r)}function pt(t,e){return e?ht(e,t,"",{"":t}):dt(t)}function ht(t,e,r,n){return Array.isArray(e)?t.call(n,r,X(e).map((function(r,n){return ht(t,r,n,e)}))):gt(e)?t.call(n,r,J(e).map((function(r,n){return ht(t,r,n,e)}))):e}function dt(t){return Array.isArray(t)?X(t).map(dt).toList():gt(t)?J(t).map(dt).toMap():t}function gt(t){return t&&(t.constructor===Object||void 0===t.constructor)}function yt(t,e){if(t===e||t!=t&&e!=e)return!0;if(!t||!e)return!1;if("function"==typeof t.valueOf&&"function"==typeof e.valueOf){if((t=t.valueOf())===(e=e.valueOf())||t!=t&&e!=e)return!0;if(!t||!e)return!1}return!("function"!=typeof t.equals||"function"!=typeof e.equals||!t.equals(e))}function vt(t,e){if(t===e)return!0;if(!a(e)||void 0!==t.size&&void 0!==e.size&&t.size!==e.size||void 0!==t.__hash&&void 0!==e.__hash&&t.__hash!==e.__hash||s(t)!==s(e)||u(t)!==u(e)||l(t)!==l(e))return!1;if(0===t.size&&0===e.size)return!0;var r=!c(t);if(l(t)){var n=t.entries();return e.every((function(t,e){var i=n.next().value;return i&&yt(i[1],t)&&(r||yt(i[0],e))}))&&n.next().done}var i=!1;if(void 0===t.size)if(void 0===e.size)"function"==typeof t.cacheResult&&t.cacheResult();else{i=!0;var o=t;t=e,e=o}var f=!0,p=e.__iterate((function(e,n){if(r?!t.has(e):i?!yt(e,t.get(n,_)):!yt(t.get(n,_),e))return f=!1,!1}));return f&&t.size===p}function mt(t,e){if(!(this instanceof mt))return new mt(t,e);if(this._value=t,this.size=void 0===e?1/0:Math.max(0,e),0===this.size){if(Z)return Z;Z=this}}function _t(t,e){if(!t)throw new Error(e)}function bt(t,e,r){if(!(this instanceof bt))return new bt(t,e,r);if(_t(0!==r,"Cannot step a Range by 0"),t=t||0,void 0===e&&(e=1/0),r=void 0===r?1:Math.abs(r),e<t&&(r=-r),this._start=t,this._end=e,this._step=r,this.size=Math.max(0,Math.ceil((e-t)/r-1)+1),0===this.size){if(Q)return Q;Q=this}}function St(){throw TypeError("Abstract")}function wt(){}function xt(){}function kt(){}G.prototype[tt]=!0,e(et,X),et.prototype.get=function(t,e){return this.has(t)?this._array[O(this,t)]:e},et.prototype.__iterate=function(t,e){for(var r=this._array,n=r.length-1,i=0;i<=n;i++)if(!1===t(r[e?n-i:i],i,this))return i+1;return i},et.prototype.__iterator=function(t,e){var r=this._array,n=r.length-1,i=0;return new P((function(){return i>n?{value:void 0,done:!0}:z(t,i,r[e?n-i++:i++])}))},e(rt,J),rt.prototype.get=function(t,e){return void 0===e||this.has(t)?this._object[t]:e},rt.prototype.has=function(t){return this._object.hasOwnProperty(t)},rt.prototype.__iterate=function(t,e){for(var r=this._object,n=this._keys,i=n.length-1,o=0;o<=i;o++){var a=n[e?i-o:o];if(!1===t(r[a],a,this))return o+1}return o},rt.prototype.__iterator=function(t,e){var r=this._object,n=this._keys,i=n.length-1,o=0;return new P((function(){var a=n[e?i-o:o];return o++>i?{value:void 0,done:!0}:z(t,a,r[a])}))},rt.prototype[d]=!0,e(nt,X),nt.prototype.__iterateUncached=function(t,e){if(e)return this.cacheResult().__iterate(t,e);var r=H(this._iterable),n=0;if(q(r))for(var i;!(i=r.next()).done&&!1!==t(i.value,n++,this););return n},nt.prototype.__iteratorUncached=function(t,e){if(e)return this.cacheResult().__iterator(t,e);var r=H(this._iterable);if(!q(r))return new P(j);var n=0;return new P((function(){var e=r.next();return e.done?e:z(t,n++,e.value)}))},e(it,X),it.prototype.__iterateUncached=function(t,e){if(e)return this.cacheResult().__iterate(t,e);for(var r,n=this._iterator,i=this._iteratorCache,o=0;o<i.length;)if(!1===t(i[o],o++,this))return o;for(;!(r=n.next()).done;){var a=r.value;if(i[o]=a,!1===t(a,o++,this))break}return o},it.prototype.__iteratorUncached=function(t,e){if(e)return this.cacheResult().__iterator(t,e);var r=this._iterator,n=this._iteratorCache,i=0;return new P((function(){if(i>=n.length){var e=r.next();if(e.done)return e;n[i]=e.value}return z(t,i,n[i++])}))},e(mt,X),mt.prototype.toString=function(){return 0===this.size?"Repeat []":"Repeat [ "+this._value+" "+this.size+" times ]"},mt.prototype.get=function(t,e){return this.has(t)?this._value:e},mt.prototype.includes=function(t){return yt(this._value,t)},mt.prototype.slice=function(t,e){var r=this.size;return K(t,e,r)?this:new mt(this._value,M(e,r)-T(t,r))},mt.prototype.reverse=function(){return this},mt.prototype.indexOf=function(t){return yt(this._value,t)?0:-1},mt.prototype.lastIndexOf=function(t){return yt(this._value,t)?this.size:-1},mt.prototype.__iterate=function(t,e){for(var r=0;r<this.size;r++)if(!1===t(this._value,r,this))return r+1;return r},mt.prototype.__iterator=function(t,e){var r=this,n=0;return new P((function(){return n<r.size?z(t,n++,r._value):{value:void 0,done:!0}}))},mt.prototype.equals=function(t){return t instanceof mt?yt(this._value,t._value):vt(t)},e(bt,X),bt.prototype.toString=function(){return 0===this.size?"Range []":"Range [ "+this._start+"..."+this._end+(this._step>1?" by "+this._step:"")+" ]"},bt.prototype.get=function(t,e){return this.has(t)?this._start+O(this,t)*this._step:e},bt.prototype.includes=function(t){var e=(t-this._start)/this._step;return e>=0&&e<this.size&&e===Math.floor(e)},bt.prototype.slice=function(t,e){return K(t,e,this.size)?this:(t=T(t,this.size),(e=M(e,this.size))<=t?new bt(0,0):new bt(this.get(t,this._end),this.get(e,this._end),this._step))},bt.prototype.indexOf=function(t){var e=t-this._start;if(e%this._step==0){var r=e/this._step;if(r>=0&&r<this.size)return r}return-1},bt.prototype.lastIndexOf=function(t){return this.indexOf(t)},bt.prototype.__iterate=function(t,e){for(var r=this.size-1,n=this._step,i=e?this._start+r*n:this._start,o=0;o<=r;o++){if(!1===t(i,o,this))return o+1;i+=e?-n:n}return o},bt.prototype.__iterator=function(t,e){var r=this.size-1,n=this._step,i=e?this._start+r*n:this._start,o=0;return new P((function(){var a=i;return i+=e?-n:n,o>r?{value:void 0,done:!0}:z(t,o++,a)}))},bt.prototype.equals=function(t){return t instanceof bt?this._start===t._start&&this._end===t._end&&this._step===t._step:vt(this,t)},e(St,r),e(wt,St),e(xt,St),e(kt,St),St.Keyed=wt,St.Indexed=xt,St.Set=kt;var Ct="function"==typeof Math.imul&&-2===Math.imul(4294967295,2)?Math.imul:function(t,e){var r=65535&(t|=0),n=65535&(e|=0);return r*n+((t>>>16)*n+r*(e>>>16)<<16>>>0)|0};function Et(t){return t>>>1&1073741824|3221225471&t}function Ot(t){if(!1===t||null==t)return 0;if("function"==typeof t.valueOf&&(!1===(t=t.valueOf())||null==t))return 0;if(!0===t)return 1;var e=typeof t;if("number"===e){var r=0|t;for(r!==t&&(r^=4294967295*t);t>4294967295;)r^=t/=4294967295;return Et(r)}if("string"===e)return t.length>Lt?function(t){var e=Ft[t];return void 0===e&&(e=Dt(t),Nt===Rt&&(Nt=0,Ft={}),Nt++,Ft[t]=e),e}(t):Dt(t);if("function"==typeof t.hashCode)return t.hashCode();if("object"===e)return function(t){var e;if(At&&void 0!==(e=Mt.get(t)))return e;if(void 0!==(e=t[Bt]))return e;if(!Tt){if(void 0!==(e=t.propertyIsEnumerable&&t.propertyIsEnumerable[Bt]))return e;if(void 0!==(e=function(t){if(t&&t.nodeType>0)switch(t.nodeType){case 1:return t.uniqueID;case 9:return t.documentElement&&t.documentElement.uniqueID}}(t)))return e}if(e=++It,1073741824&It&&(It=0),At)Mt.set(t,e);else{if(void 0!==Kt&&!1===Kt(t))throw new Error("Non-extensible objects are not allowed as keys.");if(Tt)Object.defineProperty(t,Bt,{enumerable:!1,configurable:!1,writable:!1,value:e});else if(void 0!==t.propertyIsEnumerable&&t.propertyIsEnumerable===t.constructor.prototype.propertyIsEnumerable)t.propertyIsEnumerable=function(){return this.constructor.prototype.propertyIsEnumerable.apply(this,arguments)},t.propertyIsEnumerable[Bt]=e;else{if(void 0===t.nodeType)throw new Error("Unable to set a non-enumerable property on object.");t[Bt]=e}}return e}(t);if("function"==typeof t.toString)return Dt(t.toString());throw new Error("Value type "+e+" cannot be hashed.")}function Dt(t){for(var e=0,r=0;r<t.length;r++)e=31*e+t.charCodeAt(r)|0;return Et(e)}var Kt=Object.isExtensible,Tt=function(){try{return Object.defineProperty({},"@",{}),!0}catch(t){return!1}}();var Mt,At="function"==typeof WeakMap;At&&(Mt=new WeakMap);var It=0,Bt="__immutablehash__";"function"==typeof Symbol&&(Bt=Symbol(Bt));var Lt=16,Rt=255,Nt=0,Ft={};function Pt(t){_t(t!==1/0,"Cannot perform this action with an infinite size.")}function zt(t){return null==t?te():jt(t)&&!l(t)?t:te().withMutations((function(e){var r=n(t);Pt(r.size),r.forEach((function(t,r){return e.set(r,t)}))}))}function jt(t){return!(!t||!t[qt])}e(zt,wt),zt.prototype.toString=function(){return this.__toString("Map {","}")},zt.prototype.get=function(t,e){return this._root?this._root.get(0,void 0,t,e):e},zt.prototype.set=function(t,e){return ee(this,t,e)},zt.prototype.setIn=function(t,e){return this.updateIn(t,_,(function(){return e}))},zt.prototype.remove=function(t){return ee(this,t,_)},zt.prototype.deleteIn=function(t){return this.updateIn(t,(function(){return _}))},zt.prototype.update=function(t,e,r){return 1===arguments.length?t(this):this.updateIn([t],e,r)},zt.prototype.updateIn=function(t,e,r){r||(r=e,e=void 0);var n=ce(this,or(t),e,r);return n===_?void 0:n},zt.prototype.clear=function(){return 0===this.size?this:this.__ownerID?(this.size=0,this._root=null,this.__hash=void 0,this.__altered=!0,this):te()},zt.prototype.merge=function(){return oe(this,void 0,arguments)},zt.prototype.mergeWith=function(e){return oe(this,e,t.call(arguments,1))},zt.prototype.mergeIn=function(e){var r=t.call(arguments,1);return this.updateIn(e,te(),(function(t){return"function"==typeof t.merge?t.merge.apply(t,r):r[r.length-1]}))},zt.prototype.mergeDeep=function(){return oe(this,ae,arguments)},zt.prototype.mergeDeepWith=function(e){var r=t.call(arguments,1);return oe(this,se(e),r)},zt.prototype.mergeDeepIn=function(e){var r=t.call(arguments,1);return this.updateIn(e,te(),(function(t){return"function"==typeof t.mergeDeep?t.mergeDeep.apply(t,r):r[r.length-1]}))},zt.prototype.sort=function(t){return Ae(Je(this,t))},zt.prototype.sortBy=function(t,e){return Ae(Je(this,e,t))},zt.prototype.withMutations=function(t){var e=this.asMutable();return t(e),e.wasAltered()?e.__ensureOwner(this.__ownerID):this},zt.prototype.asMutable=function(){return this.__ownerID?this:this.__ensureOwner(new k)},zt.prototype.asImmutable=function(){return this.__ensureOwner()},zt.prototype.wasAltered=function(){return this.__altered},zt.prototype.__iterator=function(t,e){return new Yt(this,t,e)},zt.prototype.__iterate=function(t,e){var r=this,n=0;return this._root&&this._root.iterate((function(e){return n++,t(e[1],e[0],r)}),e),n},zt.prototype.__ensureOwner=function(t){return t===this.__ownerID?this:t?Qt(this.size,this._root,t,this.__hash):(this.__ownerID=t,this.__altered=!1,this)},zt.isMap=jt;var Ut,qt="@@__IMMUTABLE_MAP__@@",Ht=zt.prototype;function Wt(t,e){this.ownerID=t,this.entries=e}function Vt(t,e,r){this.ownerID=t,this.bitmap=e,this.nodes=r}function Gt(t,e,r){this.ownerID=t,this.count=e,this.nodes=r}function Jt(t,e,r){this.ownerID=t,this.keyHash=e,this.entries=r}function Xt(t,e,r){this.ownerID=t,this.keyHash=e,this.entry=r}function Yt(t,e,r){this._type=e,this._reverse=r,this._stack=t._root&&Zt(t._root)}function $t(t,e){return z(t,e[0],e[1])}function Zt(t,e){return{node:t,index:0,__prev:e}}function Qt(t,e,r,n){var i=Object.create(Ht);return i.size=t,i._root=e,i.__ownerID=r,i.__hash=n,i.__altered=!1,i}function te(){return Ut||(Ut=Qt(0))}function ee(t,e,r){var n,i;if(t._root){var o=w(b),a=w(S);if(n=re(t._root,t.__ownerID,0,void 0,e,r,o,a),!a.value)return t;i=t.size+(o.value?r===_?-1:1:0)}else{if(r===_)return t;i=1,n=new Wt(t.__ownerID,[[e,r]])}return t.__ownerID?(t.size=i,t._root=n,t.__hash=void 0,t.__altered=!0,t):n?Qt(i,n):te()}function re(t,e,r,n,i,o,a,s){return t?t.update(e,r,n,i,o,a,s):o===_?t:(x(s),x(a),new Xt(e,n,[i,o]))}function ne(t){return t.constructor===Xt||t.constructor===Jt}function ie(t,e,r,n,i){if(t.keyHash===n)return new Jt(e,n,[t.entry,i]);var o,a=(0===r?t.keyHash:t.keyHash>>>r)&m,s=(0===r?n:n>>>r)&m;return new Vt(e,1<<a|1<<s,a===s?[ie(t,e,r+y,n,i)]:(o=new Xt(e,n,i),a<s?[t,o]:[o,t]))}function oe(t,e,r){for(var i=[],o=0;o<r.length;o++){var s=r[o],u=n(s);a(s)||(u=u.map((function(t){return pt(t)}))),i.push(u)}return ue(t,e,i)}function ae(t,e,r){return t&&t.mergeDeep&&a(e)?t.mergeDeep(e):yt(t,e)?t:e}function se(t){return function(e,r,n){if(e&&e.mergeDeepWith&&a(r))return e.mergeDeepWith(t,r);var i=t(e,r,n);return yt(e,i)?e:i}}function ue(t,e,r){return 0===(r=r.filter((function(t){return 0!==t.size}))).length?t:0!==t.size||t.__ownerID||1!==r.length?t.withMutations((function(t){for(var n=e?function(r,n){t.update(n,_,(function(t){return t===_?r:e(t,r,n)}))}:function(e,r){t.set(r,e)},i=0;i<r.length;i++)r[i].forEach(n)})):t.constructor(r[0])}function ce(t,e,r,n){var i=t===_,o=e.next();if(o.done){var a=i?r:t,s=n(a);return s===a?t:s}_t(i||t&&t.set,"invalid keyPath");var u=o.value,c=i?_:t.get(u,_),l=ce(c,e,r,n);return l===c?t:l===_?t.remove(u):(i?te():t).set(u,l)}function le(t){return t=(t=(858993459&(t-=t>>1&1431655765))+(t>>2&858993459))+(t>>4)&252645135,127&(t+=t>>8)+(t>>16)}function fe(t,e,r,n){var i=n?t:C(t);return i[e]=r,i}Ht[qt]=!0,Ht[g]=Ht.remove,Ht.removeIn=Ht.deleteIn,Wt.prototype.get=function(t,e,r,n){for(var i=this.entries,o=0,a=i.length;o<a;o++)if(yt(r,i[o][0]))return i[o][1];return n},Wt.prototype.update=function(t,e,r,n,i,o,a){for(var s=i===_,u=this.entries,c=0,l=u.length;c<l&&!yt(n,u[c][0]);c++);var f=c<l;if(f?u[c][1]===i:s)return this;if(x(a),(s||!f)&&x(o),!s||1!==u.length){if(!f&&!s&&u.length>=pe)return function(t,e,r,n){t||(t=new k);for(var i=new Xt(t,Ot(r),[r,n]),o=0;o<e.length;o++){var a=e[o];i=i.update(t,0,void 0,a[0],a[1])}return i}(t,u,n,i);var p=t&&t===this.ownerID,h=p?u:C(u);return f?s?c===l-1?h.pop():h[c]=h.pop():h[c]=[n,i]:h.push([n,i]),p?(this.entries=h,this):new Wt(t,h)}},Vt.prototype.get=function(t,e,r,n){void 0===e&&(e=Ot(r));var i=1<<((0===t?e:e>>>t)&m),o=this.bitmap;return 0==(o&i)?n:this.nodes[le(o&i-1)].get(t+y,e,r,n)},Vt.prototype.update=function(t,e,r,n,i,o,a){void 0===r&&(r=Ot(n));var s=(0===e?r:r>>>e)&m,u=1<<s,c=this.bitmap,l=0!=(c&u);if(!l&&i===_)return this;var f=le(c&u-1),p=this.nodes,h=l?p[f]:void 0,d=re(h,t,e+y,r,n,i,o,a);if(d===h)return this;if(!l&&d&&p.length>=he)return function(t,e,r,n,i){for(var o=0,a=new Array(v),s=0;0!==r;s++,r>>>=1)a[s]=1&r?e[o++]:void 0;return a[n]=i,new Gt(t,o+1,a)}(t,p,c,s,d);if(l&&!d&&2===p.length&&ne(p[1^f]))return p[1^f];if(l&&d&&1===p.length&&ne(d))return d;var g=t&&t===this.ownerID,b=l?d?c:c^u:c|u,S=l?d?fe(p,f,d,g):function(t,e,r){var n=t.length-1;if(r&&e===n)return t.pop(),t;for(var i=new Array(n),o=0,a=0;a<n;a++)a===e&&(o=1),i[a]=t[a+o];return i}(p,f,g):function(t,e,r,n){var i=t.length+1;if(n&&e+1===i)return t[e]=r,t;for(var o=new Array(i),a=0,s=0;s<i;s++)s===e?(o[s]=r,a=-1):o[s]=t[s+a];return o}(p,f,d,g);return g?(this.bitmap=b,this.nodes=S,this):new Vt(t,b,S)},Gt.prototype.get=function(t,e,r,n){void 0===e&&(e=Ot(r));var i=(0===t?e:e>>>t)&m,o=this.nodes[i];return o?o.get(t+y,e,r,n):n},Gt.prototype.update=function(t,e,r,n,i,o,a){void 0===r&&(r=Ot(n));var s=(0===e?r:r>>>e)&m,u=i===_,c=this.nodes,l=c[s];if(u&&!l)return this;var f=re(l,t,e+y,r,n,i,o,a);if(f===l)return this;var p=this.count;if(l){if(!f&&--p<de)return function(t,e,r,n){for(var i=0,o=0,a=new Array(r),s=0,u=1,c=e.length;s<c;s++,u<<=1){var l=e[s];void 0!==l&&s!==n&&(i|=u,a[o++]=l)}return new Vt(t,i,a)}(t,c,p,s)}else p++;var h=t&&t===this.ownerID,d=fe(c,s,f,h);return h?(this.count=p,this.nodes=d,this):new Gt(t,p,d)},Jt.prototype.get=function(t,e,r,n){for(var i=this.entries,o=0,a=i.length;o<a;o++)if(yt(r,i[o][0]))return i[o][1];return n},Jt.prototype.update=function(t,e,r,n,i,o,a){void 0===r&&(r=Ot(n));var s=i===_;if(r!==this.keyHash)return s?this:(x(a),x(o),ie(this,t,e,r,[n,i]));for(var u=this.entries,c=0,l=u.length;c<l&&!yt(n,u[c][0]);c++);var f=c<l;if(f?u[c][1]===i:s)return this;if(x(a),(s||!f)&&x(o),s&&2===l)return new Xt(t,this.keyHash,u[1^c]);var p=t&&t===this.ownerID,h=p?u:C(u);return f?s?c===l-1?h.pop():h[c]=h.pop():h[c]=[n,i]:h.push([n,i]),p?(this.entries=h,this):new Jt(t,this.keyHash,h)},Xt.prototype.get=function(t,e,r,n){return yt(r,this.entry[0])?this.entry[1]:n},Xt.prototype.update=function(t,e,r,n,i,o,a){var s=i===_,u=yt(n,this.entry[0]);return(u?i===this.entry[1]:s)?this:(x(a),s?void x(o):u?t&&t===this.ownerID?(this.entry[1]=i,this):new Xt(t,this.keyHash,[n,i]):(x(o),ie(this,t,e,Ot(n),[n,i])))},Wt.prototype.iterate=Jt.prototype.iterate=function(t,e){for(var r=this.entries,n=0,i=r.length-1;n<=i;n++)if(!1===t(r[e?i-n:n]))return!1},Vt.prototype.iterate=Gt.prototype.iterate=function(t,e){for(var r=this.nodes,n=0,i=r.length-1;n<=i;n++){var o=r[e?i-n:n];if(o&&!1===o.iterate(t,e))return!1}},Xt.prototype.iterate=function(t,e){return t(this.entry)},e(Yt,P),Yt.prototype.next=function(){for(var t=this._type,e=this._stack;e;){var r,n=e.node,i=e.index++;if(n.entry){if(0===i)return $t(t,n.entry)}else if(n.entries){if(i<=(r=n.entries.length-1))return $t(t,n.entries[this._reverse?r-i:i])}else if(i<=(r=n.nodes.length-1)){var o=n.nodes[this._reverse?r-i:i];if(o){if(o.entry)return $t(t,o.entry);e=this._stack=Zt(o,e)}continue}e=this._stack=this._stack.__prev}return{value:void 0,done:!0}};var pe=v/4,he=v/2,de=v/4;function ge(t){var e=Ce();if(null==t)return e;if(ye(t))return t;var r=i(t),n=r.size;return 0===n?e:(Pt(n),n>0&&n<v?ke(0,n,y,null,new _e(r.toArray())):e.withMutations((function(t){t.setSize(n),r.forEach((function(e,r){return t.set(r,e)}))})))}function ye(t){return!(!t||!t[ve])}e(ge,xt),ge.of=function(){return this(arguments)},ge.prototype.toString=function(){return this.__toString("List [","]")},ge.prototype.get=function(t,e){if((t=O(this,t))>=0&&t<this.size){var r=De(this,t+=this._origin);return r&&r.array[t&m]}return e},ge.prototype.set=function(t,e){return function(t,e,r){if((e=O(t,e))!=e)return t;if(e>=t.size||e<0)return t.withMutations((function(t){e<0?Ke(t,e).set(0,r):Ke(t,0,e+1).set(e,r)}));e+=t._origin;var n=t._tail,i=t._root,o=w(S);return e>=Me(t._capacity)?n=Ee(n,t.__ownerID,0,e,r,o):i=Ee(i,t.__ownerID,t._level,e,r,o),o.value?t.__ownerID?(t._root=i,t._tail=n,t.__hash=void 0,t.__altered=!0,t):ke(t._origin,t._capacity,t._level,i,n):t}(this,t,e)},ge.prototype.remove=function(t){return this.has(t)?0===t?this.shift():t===this.size-1?this.pop():this.splice(t,1):this},ge.prototype.insert=function(t,e){return this.splice(t,0,e)},ge.prototype.clear=function(){return 0===this.size?this:this.__ownerID?(this.size=this._origin=this._capacity=0,this._level=y,this._root=this._tail=null,this.__hash=void 0,this.__altered=!0,this):Ce()},ge.prototype.push=function(){var t=arguments,e=this.size;return this.withMutations((function(r){Ke(r,0,e+t.length);for(var n=0;n<t.length;n++)r.set(e+n,t[n])}))},ge.prototype.pop=function(){return Ke(this,0,-1)},ge.prototype.unshift=function(){var t=arguments;return this.withMutations((function(e){Ke(e,-t.length);for(var r=0;r<t.length;r++)e.set(r,t[r])}))},ge.prototype.shift=function(){return Ke(this,1)},ge.prototype.merge=function(){return Te(this,void 0,arguments)},ge.prototype.mergeWith=function(e){return Te(this,e,t.call(arguments,1))},ge.prototype.mergeDeep=function(){return Te(this,ae,arguments)},ge.prototype.mergeDeepWith=function(e){var r=t.call(arguments,1);return Te(this,se(e),r)},ge.prototype.setSize=function(t){return Ke(this,0,t)},ge.prototype.slice=function(t,e){var r=this.size;return K(t,e,r)?this:Ke(this,T(t,r),M(e,r))},ge.prototype.__iterator=function(t,e){var r=0,n=xe(this,e);return new P((function(){var e=n();return e===we?{value:void 0,done:!0}:z(t,r++,e)}))},ge.prototype.__iterate=function(t,e){for(var r,n=0,i=xe(this,e);(r=i())!==we&&!1!==t(r,n++,this););return n},ge.prototype.__ensureOwner=function(t){return t===this.__ownerID?this:t?ke(this._origin,this._capacity,this._level,this._root,this._tail,t,this.__hash):(this.__ownerID=t,this)},ge.isList=ye;var ve="@@__IMMUTABLE_LIST__@@",me=ge.prototype;function _e(t,e){this.array=t,this.ownerID=e}me[ve]=!0,me[g]=me.remove,me.setIn=Ht.setIn,me.deleteIn=me.removeIn=Ht.removeIn,me.update=Ht.update,me.updateIn=Ht.updateIn,me.mergeIn=Ht.mergeIn,me.mergeDeepIn=Ht.mergeDeepIn,me.withMutations=Ht.withMutations,me.asMutable=Ht.asMutable,me.asImmutable=Ht.asImmutable,me.wasAltered=Ht.wasAltered,_e.prototype.removeBefore=function(t,e,r){if(r===e?1<<e:0===this.array.length)return this;var n=r>>>e&m;if(n>=this.array.length)return new _e([],t);var i,o=0===n;if(e>0){var a=this.array[n];if((i=a&&a.removeBefore(t,e-y,r))===a&&o)return this}if(o&&!i)return this;var s=Oe(this,t);if(!o)for(var u=0;u<n;u++)s.array[u]=void 0;return i&&(s.array[n]=i),s},_e.prototype.removeAfter=function(t,e,r){if(r===(e?1<<e:0)||0===this.array.length)return this;var n,i=r-1>>>e&m;if(i>=this.array.length)return this;if(e>0){var o=this.array[i];if((n=o&&o.removeAfter(t,e-y,r))===o&&i===this.array.length-1)return this}var a=Oe(this,t);return a.array.splice(i+1),n&&(a.array[i]=n),a};var be,Se,we={};function xe(t,e){var r=t._origin,n=t._capacity,i=Me(n),o=t._tail;return a(t._root,t._level,0);function a(t,s,u){return 0===s?function(t,a){var s=a===i?o&&o.array:t&&t.array,u=a>r?0:r-a,c=n-a;return c>v&&(c=v),function(){if(u===c)return we;var t=e?--c:u++;return s&&s[t]}}(t,u):function(t,i,o){var s,u=t&&t.array,c=o>r?0:r-o>>i,l=1+(n-o>>i);return l>v&&(l=v),function(){for(;;){if(s){var t=s();if(t!==we)return t;s=null}if(c===l)return we;var r=e?--l:c++;s=a(u&&u[r],i-y,o+(r<<i))}}}(t,s,u)}}function ke(t,e,r,n,i,o,a){var s=Object.create(me);return s.size=e-t,s._origin=t,s._capacity=e,s._level=r,s._root=n,s._tail=i,s.__ownerID=o,s.__hash=a,s.__altered=!1,s}function Ce(){return be||(be=ke(0,0,y))}function Ee(t,e,r,n,i,o){var a,s=n>>>r&m,u=t&&s<t.array.length;if(!u&&void 0===i)return t;if(r>0){var c=t&&t.array[s],l=Ee(c,e,r-y,n,i,o);return l===c?t:((a=Oe(t,e)).array[s]=l,a)}return u&&t.array[s]===i?t:(x(o),a=Oe(t,e),void 0===i&&s===a.array.length-1?a.array.pop():a.array[s]=i,a)}function Oe(t,e){return e&&t&&e===t.ownerID?t:new _e(t?t.array.slice():[],e)}function De(t,e){if(e>=Me(t._capacity))return t._tail;if(e<1<<t._level+y){for(var r=t._root,n=t._level;r&&n>0;)r=r.array[e>>>n&m],n-=y;return r}}function Ke(t,e,r){void 0!==e&&(e|=0),void 0!==r&&(r|=0);var n=t.__ownerID||new k,i=t._origin,o=t._capacity,a=i+e,s=void 0===r?o:r<0?o+r:i+r;if(a===i&&s===o)return t;if(a>=s)return t.clear();for(var u=t._level,c=t._root,l=0;a+l<0;)c=new _e(c&&c.array.length?[void 0,c]:[],n),l+=1<<(u+=y);l&&(a+=l,i+=l,s+=l,o+=l);for(var f=Me(o),p=Me(s);p>=1<<u+y;)c=new _e(c&&c.array.length?[c]:[],n),u+=y;var h=t._tail,d=p<f?De(t,s-1):p>f?new _e([],n):h;if(h&&p>f&&a<o&&h.array.length){for(var g=c=Oe(c,n),v=u;v>y;v-=y){var _=f>>>v&m;g=g.array[_]=Oe(g.array[_],n)}g.array[f>>>y&m]=h}if(s<o&&(d=d&&d.removeAfter(n,0,s)),a>=p)a-=p,s-=p,u=y,c=null,d=d&&d.removeBefore(n,0,a);else if(a>i||p<f){for(l=0;c;){var b=a>>>u&m;if(b!==p>>>u&m)break;b&&(l+=(1<<u)*b),u-=y,c=c.array[b]}c&&a>i&&(c=c.removeBefore(n,u,a-l)),c&&p<f&&(c=c.removeAfter(n,u,p-l)),l&&(a-=l,s-=l)}return t.__ownerID?(t.size=s-a,t._origin=a,t._capacity=s,t._level=u,t._root=c,t._tail=d,t.__hash=void 0,t.__altered=!0,t):ke(a,s,u,c,d)}function Te(t,e,r){for(var n=[],o=0,s=0;s<r.length;s++){var u=r[s],c=i(u);c.size>o&&(o=c.size),a(u)||(c=c.map((function(t){return pt(t)}))),n.push(c)}return o>t.size&&(t=t.setSize(o)),ue(t,e,n)}function Me(t){return t<v?0:t-1>>>y<<y}function Ae(t){return null==t?Le():Ie(t)?t:Le().withMutations((function(e){var r=n(t);Pt(r.size),r.forEach((function(t,r){return e.set(r,t)}))}))}function Ie(t){return jt(t)&&l(t)}function Be(t,e,r,n){var i=Object.create(Ae.prototype);return i.size=t?t.size:0,i._map=t,i._list=e,i.__ownerID=r,i.__hash=n,i}function Le(){return Se||(Se=Be(te(),Ce()))}function Re(t,e,r){var n,i,o=t._map,a=t._list,s=o.get(e),u=void 0!==s;if(r===_){if(!u)return t;a.size>=v&&a.size>=2*o.size?(n=(i=a.filter((function(t,e){return void 0!==t&&s!==e}))).toKeyedSeq().map((function(t){return t[0]})).flip().toMap(),t.__ownerID&&(n.__ownerID=i.__ownerID=t.__ownerID)):(n=o.remove(e),i=s===a.size-1?a.pop():a.set(s,void 0))}else if(u){if(r===a.get(s)[1])return t;n=o,i=a.set(s,[e,r])}else n=o.set(e,a.size),i=a.set(a.size,[e,r]);return t.__ownerID?(t.size=n.size,t._map=n,t._list=i,t.__hash=void 0,t):Be(n,i)}function Ne(t,e){this._iter=t,this._useKeys=e,this.size=t.size}function Fe(t){this._iter=t,this.size=t.size}function Pe(t){this._iter=t,this.size=t.size}function ze(t){this._iter=t,this.size=t.size}function je(t){var e=rr(t);return e._iter=t,e.size=t.size,e.flip=function(){return t},e.reverse=function(){var e=t.reverse.apply(this);return e.flip=function(){return t.reverse()},e},e.has=function(e){return t.includes(e)},e.includes=function(e){return t.has(e)},e.cacheResult=nr,e.__iterateUncached=function(e,r){var n=this;return t.__iterate((function(t,r){return!1!==e(r,t,n)}),r)},e.__iteratorUncached=function(e,r){if(e===L){var n=t.__iterator(e,r);return new P((function(){var t=n.next();if(!t.done){var e=t.value[0];t.value[0]=t.value[1],t.value[1]=e}return t}))}return t.__iterator(e===B?I:B,r)},e}function Ue(t,e,r){var n=rr(t);return n.size=t.size,n.has=function(e){return t.has(e)},n.get=function(n,i){var o=t.get(n,_);return o===_?i:e.call(r,o,n,t)},n.__iterateUncached=function(n,i){var o=this;return t.__iterate((function(t,i,a){return!1!==n(e.call(r,t,i,a),i,o)}),i)},n.__iteratorUncached=function(n,i){var o=t.__iterator(L,i);return new P((function(){var i=o.next();if(i.done)return i;var a=i.value,s=a[0];return z(n,s,e.call(r,a[1],s,t),i)}))},n}function qe(t,e){var r=rr(t);return r._iter=t,r.size=t.size,r.reverse=function(){return t},t.flip&&(r.flip=function(){var e=je(t);return e.reverse=function(){return t.flip()},e}),r.get=function(r,n){return t.get(e?r:-1-r,n)},r.has=function(r){return t.has(e?r:-1-r)},r.includes=function(e){return t.includes(e)},r.cacheResult=nr,r.__iterate=function(e,r){var n=this;return t.__iterate((function(t,r){return e(t,r,n)}),!r)},r.__iterator=function(e,r){return t.__iterator(e,!r)},r}function He(t,e,r,n){var i=rr(t);return n&&(i.has=function(n){var i=t.get(n,_);return i!==_&&!!e.call(r,i,n,t)},i.get=function(n,i){var o=t.get(n,_);return o!==_&&e.call(r,o,n,t)?o:i}),i.__iterateUncached=function(i,o){var a=this,s=0;return t.__iterate((function(t,o,u){if(e.call(r,t,o,u))return s++,i(t,n?o:s-1,a)}),o),s},i.__iteratorUncached=function(i,o){var a=t.__iterator(L,o),s=0;return new P((function(){for(;;){var o=a.next();if(o.done)return o;var u=o.value,c=u[0],l=u[1];if(e.call(r,l,c,t))return z(i,n?c:s++,l,o)}}))},i}function We(t,e,r,n){var i=t.size;if(void 0!==e&&(e|=0),void 0!==r&&(r|=0),K(e,r,i))return t;var o=T(e,i),a=M(r,i);if(o!=o||a!=a)return We(t.toSeq().cacheResult(),e,r,n);var s,u=a-o;u==u&&(s=u<0?0:u);var c=rr(t);return c.size=0===s?s:t.size&&s||void 0,!n&&ot(t)&&s>=0&&(c.get=function(e,r){return(e=O(this,e))>=0&&e<s?t.get(e+o,r):r}),c.__iterateUncached=function(e,r){var i=this;if(0===s)return 0;if(r)return this.cacheResult().__iterate(e,r);var a=0,u=!0,c=0;return t.__iterate((function(t,r){if(!u||!(u=a++<o))return c++,!1!==e(t,n?r:c-1,i)&&c!==s})),c},c.__iteratorUncached=function(e,r){if(0!==s&&r)return this.cacheResult().__iterator(e,r);var i=0!==s&&t.__iterator(e,r),a=0,u=0;return new P((function(){for(;a++<o;)i.next();if(++u>s)return{value:void 0,done:!0};var t=i.next();return n||e===B?t:z(e,u-1,e===I?void 0:t.value[1],t)}))},c}function Ve(t,e,r,n){var i=rr(t);return i.__iterateUncached=function(i,o){var a=this;if(o)return this.cacheResult().__iterate(i,o);var s=!0,u=0;return t.__iterate((function(t,o,c){if(!s||!(s=e.call(r,t,o,c)))return u++,i(t,n?o:u-1,a)})),u},i.__iteratorUncached=function(i,o){var a=this;if(o)return this.cacheResult().__iterator(i,o);var s=t.__iterator(L,o),u=!0,c=0;return new P((function(){var t,o,l;do{if((t=s.next()).done)return n||i===B?t:z(i,c++,i===I?void 0:t.value[1],t);var f=t.value;o=f[0],l=f[1],u&&(u=e.call(r,l,o,a))}while(u);return i===L?t:z(i,o,l,t)}))},i}function Ge(t,e,r){var n=rr(t);return n.__iterateUncached=function(n,i){var o=0,s=!1;return function t(u,c){var l=this;u.__iterate((function(i,u){return(!e||c<e)&&a(i)?t(i,c+1):!1===n(i,r?u:o++,l)&&(s=!0),!s}),i)}(t,0),o},n.__iteratorUncached=function(n,i){var o=t.__iterator(n,i),s=[],u=0;return new P((function(){for(;o;){var t=o.next();if(!1===t.done){var c=t.value;if(n===L&&(c=c[1]),e&&!(s.length<e)||!a(c))return r?t:z(n,u++,c,t);s.push(o),o=c.__iterator(n,i)}else o=s.pop()}return{value:void 0,done:!0}}))},n}function Je(t,e,r){e||(e=ir);var n=s(t),i=0,o=t.toSeq().map((function(e,n){return[n,e,i++,r?r(e,n,t):e]})).toArray();return o.sort((function(t,r){return e(t[3],r[3])||t[2]-r[2]})).forEach(n?function(t,e){o[e].length=2}:function(t,e){o[e]=t[1]}),n?J(o):u(t)?X(o):Y(o)}function Xe(t,e,r){if(e||(e=ir),r){var n=t.toSeq().map((function(e,n){return[e,r(e,n,t)]})).reduce((function(t,r){return Ye(e,t[1],r[1])?r:t}));return n&&n[0]}return t.reduce((function(t,r){return Ye(e,t,r)?r:t}))}function Ye(t,e,r){var n=t(r,e);return 0===n&&r!==e&&(null==r||r!=r)||n>0}function $e(t,e,n){var i=rr(t);return i.size=new et(n).map((function(t){return t.size})).min(),i.__iterate=function(t,e){for(var r,n=this.__iterator(B,e),i=0;!(r=n.next()).done&&!1!==t(r.value,i++,this););return i},i.__iteratorUncached=function(t,i){var o=n.map((function(t){return t=r(t),H(i?t.reverse():t)})),a=0,s=!1;return new P((function(){var r;return s||(r=o.map((function(t){return t.next()})),s=r.some((function(t){return t.done}))),s?{value:void 0,done:!0}:z(t,a++,e.apply(null,r.map((function(t){return t.value}))))}))},i}function Ze(t,e){return ot(t)?e:t.constructor(e)}function Qe(t){if(t!==Object(t))throw new TypeError("Expected [K, V] tuple: "+t)}function tr(t){return Pt(t.size),E(t)}function er(t){return s(t)?n:u(t)?i:o}function rr(t){return Object.create((s(t)?J:u(t)?X:Y).prototype)}function nr(){return this._iter.cacheResult?(this._iter.cacheResult(),this.size=this._iter.size,this):G.prototype.cacheResult.call(this)}function ir(t,e){return t>e?1:t<e?-1:0}function or(t){var e=H(t);if(!e){if(!V(t))throw new TypeError("Expected iterable or array-like: "+t);e=H(r(t))}return e}function ar(t,e){var r,n=function(o){if(o instanceof n)return o;if(!(this instanceof n))return new n(o);if(!r){r=!0;var a=Object.keys(t);(function(t,e){try{e.forEach(lr.bind(void 0,t))}catch(t){}})(i,a),i.size=a.length,i._name=e,i._keys=a,i._defaultValues=t}this._map=zt(o)},i=n.prototype=Object.create(sr);return i.constructor=n,n}e(Ae,zt),Ae.of=function(){return this(arguments)},Ae.prototype.toString=function(){return this.__toString("OrderedMap {","}")},Ae.prototype.get=function(t,e){var r=this._map.get(t);return void 0!==r?this._list.get(r)[1]:e},Ae.prototype.clear=function(){return 0===this.size?this:this.__ownerID?(this.size=0,this._map.clear(),this._list.clear(),this):Le()},Ae.prototype.set=function(t,e){return Re(this,t,e)},Ae.prototype.remove=function(t){return Re(this,t,_)},Ae.prototype.wasAltered=function(){return this._map.wasAltered()||this._list.wasAltered()},Ae.prototype.__iterate=function(t,e){var r=this;return this._list.__iterate((function(e){return e&&t(e[1],e[0],r)}),e)},Ae.prototype.__iterator=function(t,e){return this._list.fromEntrySeq().__iterator(t,e)},Ae.prototype.__ensureOwner=function(t){if(t===this.__ownerID)return this;var e=this._map.__ensureOwner(t),r=this._list.__ensureOwner(t);return t?Be(e,r,t,this.__hash):(this.__ownerID=t,this._map=e,this._list=r,this)},Ae.isOrderedMap=Ie,Ae.prototype[d]=!0,Ae.prototype[g]=Ae.prototype.remove,e(Ne,J),Ne.prototype.get=function(t,e){return this._iter.get(t,e)},Ne.prototype.has=function(t){return this._iter.has(t)},Ne.prototype.valueSeq=function(){return this._iter.valueSeq()},Ne.prototype.reverse=function(){var t=this,e=qe(this,!0);return this._useKeys||(e.valueSeq=function(){return t._iter.toSeq().reverse()}),e},Ne.prototype.map=function(t,e){var r=this,n=Ue(this,t,e);return this._useKeys||(n.valueSeq=function(){return r._iter.toSeq().map(t,e)}),n},Ne.prototype.__iterate=function(t,e){var r,n=this;return this._iter.__iterate(this._useKeys?function(e,r){return t(e,r,n)}:(r=e?tr(this):0,function(i){return t(i,e?--r:r++,n)}),e)},Ne.prototype.__iterator=function(t,e){if(this._useKeys)return this._iter.__iterator(t,e);var r=this._iter.__iterator(B,e),n=e?tr(this):0;return new P((function(){var i=r.next();return i.done?i:z(t,e?--n:n++,i.value,i)}))},Ne.prototype[d]=!0,e(Fe,X),Fe.prototype.includes=function(t){return this._iter.includes(t)},Fe.prototype.__iterate=function(t,e){var r=this,n=0;return this._iter.__iterate((function(e){return t(e,n++,r)}),e)},Fe.prototype.__iterator=function(t,e){var r=this._iter.__iterator(B,e),n=0;return new P((function(){var e=r.next();return e.done?e:z(t,n++,e.value,e)}))},e(Pe,Y),Pe.prototype.has=function(t){return this._iter.includes(t)},Pe.prototype.__iterate=function(t,e){var r=this;return this._iter.__iterate((function(e){return t(e,e,r)}),e)},Pe.prototype.__iterator=function(t,e){var r=this._iter.__iterator(B,e);return new P((function(){var e=r.next();return e.done?e:z(t,e.value,e.value,e)}))},e(ze,J),ze.prototype.entrySeq=function(){return this._iter.toSeq()},ze.prototype.__iterate=function(t,e){var r=this;return this._iter.__iterate((function(e){if(e){Qe(e);var n=a(e);return t(n?e.get(1):e[1],n?e.get(0):e[0],r)}}),e)},ze.prototype.__iterator=function(t,e){var r=this._iter.__iterator(B,e);return new P((function(){for(;;){var e=r.next();if(e.done)return e;var n=e.value;if(n){Qe(n);var i=a(n);return z(t,i?n.get(0):n[0],i?n.get(1):n[1],e)}}}))},Fe.prototype.cacheResult=Ne.prototype.cacheResult=Pe.prototype.cacheResult=ze.prototype.cacheResult=nr,e(ar,wt),ar.prototype.toString=function(){return this.__toString(cr(this)+" {","}")},ar.prototype.has=function(t){return this._defaultValues.hasOwnProperty(t)},ar.prototype.get=function(t,e){if(!this.has(t))return e;var r=this._defaultValues[t];return this._map?this._map.get(t,r):r},ar.prototype.clear=function(){if(this.__ownerID)return this._map&&this._map.clear(),this;var t=this.constructor;return t._empty||(t._empty=ur(this,te()))},ar.prototype.set=function(t,e){if(!this.has(t))throw new Error('Cannot set unknown key "'+t+'" on '+cr(this));var r=this._map&&this._map.set(t,e);return this.__ownerID||r===this._map?this:ur(this,r)},ar.prototype.remove=function(t){if(!this.has(t))return this;var e=this._map&&this._map.remove(t);return this.__ownerID||e===this._map?this:ur(this,e)},ar.prototype.wasAltered=function(){return this._map.wasAltered()},ar.prototype.__iterator=function(t,e){var r=this;return n(this._defaultValues).map((function(t,e){return r.get(e)})).__iterator(t,e)},ar.prototype.__iterate=function(t,e){var r=this;return n(this._defaultValues).map((function(t,e){return r.get(e)})).__iterate(t,e)},ar.prototype.__ensureOwner=function(t){if(t===this.__ownerID)return this;var e=this._map&&this._map.__ensureOwner(t);return t?ur(this,e,t):(this.__ownerID=t,this._map=e,this)};var sr=ar.prototype;function ur(t,e,r){var n=Object.create(Object.getPrototypeOf(t));return n._map=e,n.__ownerID=r,n}function cr(t){return t._name||t.constructor.name||"Record"}function lr(t,e){Object.defineProperty(t,e,{get:function(){return this.get(e)},set:function(t){_t(this.__ownerID,"Cannot set on an immutable record."),this.set(e,t)}})}function fr(t){return null==t?mr():pr(t)&&!l(t)?t:mr().withMutations((function(e){var r=o(t);Pt(r.size),r.forEach((function(t){return e.add(t)}))}))}function pr(t){return!(!t||!t[dr])}sr[g]=sr.remove,sr.deleteIn=sr.removeIn=Ht.removeIn,sr.merge=Ht.merge,sr.mergeWith=Ht.mergeWith,sr.mergeIn=Ht.mergeIn,sr.mergeDeep=Ht.mergeDeep,sr.mergeDeepWith=Ht.mergeDeepWith,sr.mergeDeepIn=Ht.mergeDeepIn,sr.setIn=Ht.setIn,sr.update=Ht.update,sr.updateIn=Ht.updateIn,sr.withMutations=Ht.withMutations,sr.asMutable=Ht.asMutable,sr.asImmutable=Ht.asImmutable,e(fr,kt),fr.of=function(){return this(arguments)},fr.fromKeys=function(t){return this(n(t).keySeq())},fr.prototype.toString=function(){return this.__toString("Set {","}")},fr.prototype.has=function(t){return this._map.has(t)},fr.prototype.add=function(t){return yr(this,this._map.set(t,!0))},fr.prototype.remove=function(t){return yr(this,this._map.remove(t))},fr.prototype.clear=function(){return yr(this,this._map.clear())},fr.prototype.union=function(){var e=t.call(arguments,0);return 0===(e=e.filter((function(t){return 0!==t.size}))).length?this:0!==this.size||this.__ownerID||1!==e.length?this.withMutations((function(t){for(var r=0;r<e.length;r++)o(e[r]).forEach((function(e){return t.add(e)}))})):this.constructor(e[0])},fr.prototype.intersect=function(){var e=t.call(arguments,0);if(0===e.length)return this;e=e.map((function(t){return o(t)}));var r=this;return this.withMutations((function(t){r.forEach((function(r){e.every((function(t){return t.includes(r)}))||t.remove(r)}))}))},fr.prototype.subtract=function(){var e=t.call(arguments,0);if(0===e.length)return this;e=e.map((function(t){return o(t)}));var r=this;return this.withMutations((function(t){r.forEach((function(r){e.some((function(t){return t.includes(r)}))&&t.remove(r)}))}))},fr.prototype.merge=function(){return this.union.apply(this,arguments)},fr.prototype.mergeWith=function(e){var r=t.call(arguments,1);return this.union.apply(this,r)},fr.prototype.sort=function(t){return _r(Je(this,t))},fr.prototype.sortBy=function(t,e){return _r(Je(this,e,t))},fr.prototype.wasAltered=function(){return this._map.wasAltered()},fr.prototype.__iterate=function(t,e){var r=this;return this._map.__iterate((function(e,n){return t(n,n,r)}),e)},fr.prototype.__iterator=function(t,e){return this._map.map((function(t,e){return e})).__iterator(t,e)},fr.prototype.__ensureOwner=function(t){if(t===this.__ownerID)return this;var e=this._map.__ensureOwner(t);return t?this.__make(e,t):(this.__ownerID=t,this._map=e,this)},fr.isSet=pr;var hr,dr="@@__IMMUTABLE_SET__@@",gr=fr.prototype;function yr(t,e){return t.__ownerID?(t.size=e.size,t._map=e,t):e===t._map?t:0===e.size?t.__empty():t.__make(e)}function vr(t,e){var r=Object.create(gr);return r.size=t?t.size:0,r._map=t,r.__ownerID=e,r}function mr(){return hr||(hr=vr(te()))}function _r(t){return null==t?kr():br(t)?t:kr().withMutations((function(e){var r=o(t);Pt(r.size),r.forEach((function(t){return e.add(t)}))}))}function br(t){return pr(t)&&l(t)}gr[dr]=!0,gr[g]=gr.remove,gr.mergeDeep=gr.merge,gr.mergeDeepWith=gr.mergeWith,gr.withMutations=Ht.withMutations,gr.asMutable=Ht.asMutable,gr.asImmutable=Ht.asImmutable,gr.__empty=mr,gr.__make=vr,e(_r,fr),_r.of=function(){return this(arguments)},_r.fromKeys=function(t){return this(n(t).keySeq())},_r.prototype.toString=function(){return this.__toString("OrderedSet {","}")},_r.isOrderedSet=br;var Sr,wr=_r.prototype;function xr(t,e){var r=Object.create(wr);return r.size=t?t.size:0,r._map=t,r.__ownerID=e,r}function kr(){return Sr||(Sr=xr(Le()))}function Cr(t){return null==t?Mr():Er(t)?t:Mr().unshiftAll(t)}function Er(t){return!(!t||!t[Dr])}wr[d]=!0,wr.__empty=kr,wr.__make=xr,e(Cr,xt),Cr.of=function(){return this(arguments)},Cr.prototype.toString=function(){return this.__toString("Stack [","]")},Cr.prototype.get=function(t,e){var r=this._head;for(t=O(this,t);r&&t--;)r=r.next;return r?r.value:e},Cr.prototype.peek=function(){return this._head&&this._head.value},Cr.prototype.push=function(){if(0===arguments.length)return this;for(var t=this.size+arguments.length,e=this._head,r=arguments.length-1;r>=0;r--)e={value:arguments[r],next:e};return this.__ownerID?(this.size=t,this._head=e,this.__hash=void 0,this.__altered=!0,this):Tr(t,e)},Cr.prototype.pushAll=function(t){if(0===(t=i(t)).size)return this;Pt(t.size);var e=this.size,r=this._head;return t.reverse().forEach((function(t){e++,r={value:t,next:r}})),this.__ownerID?(this.size=e,this._head=r,this.__hash=void 0,this.__altered=!0,this):Tr(e,r)},Cr.prototype.pop=function(){return this.slice(1)},Cr.prototype.unshift=function(){return this.push.apply(this,arguments)},Cr.prototype.unshiftAll=function(t){return this.pushAll(t)},Cr.prototype.shift=function(){return this.pop.apply(this,arguments)},Cr.prototype.clear=function(){return 0===this.size?this:this.__ownerID?(this.size=0,this._head=void 0,this.__hash=void 0,this.__altered=!0,this):Mr()},Cr.prototype.slice=function(t,e){if(K(t,e,this.size))return this;var r=T(t,this.size);if(M(e,this.size)!==this.size)return xt.prototype.slice.call(this,t,e);for(var n=this.size-r,i=this._head;r--;)i=i.next;return this.__ownerID?(this.size=n,this._head=i,this.__hash=void 0,this.__altered=!0,this):Tr(n,i)},Cr.prototype.__ensureOwner=function(t){return t===this.__ownerID?this:t?Tr(this.size,this._head,t,this.__hash):(this.__ownerID=t,this.__altered=!1,this)},Cr.prototype.__iterate=function(t,e){if(e)return this.reverse().__iterate(t);for(var r=0,n=this._head;n&&!1!==t(n.value,r++,this);)n=n.next;return r},Cr.prototype.__iterator=function(t,e){if(e)return this.reverse().__iterator(t);var r=0,n=this._head;return new P((function(){if(n){var e=n.value;return n=n.next,z(t,r++,e)}return{value:void 0,done:!0}}))},Cr.isStack=Er;var Or,Dr="@@__IMMUTABLE_STACK__@@",Kr=Cr.prototype;function Tr(t,e,r,n){var i=Object.create(Kr);return i.size=t,i._head=e,i.__ownerID=r,i.__hash=n,i.__altered=!1,i}function Mr(){return Or||(Or=Tr(0))}function Ar(t,e){var r=function(r){t.prototype[r]=e[r]};return Object.keys(e).forEach(r),Object.getOwnPropertySymbols&&Object.getOwnPropertySymbols(e).forEach(r),t}Kr[Dr]=!0,Kr.withMutations=Ht.withMutations,Kr.asMutable=Ht.asMutable,Kr.asImmutable=Ht.asImmutable,Kr.wasAltered=Ht.wasAltered,r.Iterator=P,Ar(r,{toArray:function(){Pt(this.size);var t=new Array(this.size||0);return this.valueSeq().__iterate((function(e,r){t[r]=e})),t},toIndexedSeq:function(){return new Fe(this)},toJS:function(){return this.toSeq().map((function(t){return t&&"function"==typeof t.toJS?t.toJS():t})).__toJS()},toJSON:function(){return this.toSeq().map((function(t){return t&&"function"==typeof t.toJSON?t.toJSON():t})).__toJS()},toKeyedSeq:function(){return new Ne(this,!0)},toMap:function(){return zt(this.toKeyedSeq())},toObject:function(){Pt(this.size);var t={};return this.__iterate((function(e,r){t[r]=e})),t},toOrderedMap:function(){return Ae(this.toKeyedSeq())},toOrderedSet:function(){return _r(s(this)?this.valueSeq():this)},toSet:function(){return fr(s(this)?this.valueSeq():this)},toSetSeq:function(){return new Pe(this)},toSeq:function(){return u(this)?this.toIndexedSeq():s(this)?this.toKeyedSeq():this.toSetSeq()},toStack:function(){return Cr(s(this)?this.valueSeq():this)},toList:function(){return ge(s(this)?this.valueSeq():this)},toString:function(){return"[Iterable]"},__toString:function(t,e){return 0===this.size?t+e:t+" "+this.toSeq().map(this.__toStringMapper).join(", ")+" "+e},concat:function(){return Ze(this,function(t,e){var r=s(t),i=[t].concat(e).map((function(t){return a(t)?r&&(t=n(t)):t=r?st(t):ut(Array.isArray(t)?t:[t]),t})).filter((function(t){return 0!==t.size}));if(0===i.length)return t;if(1===i.length){var o=i[0];if(o===t||r&&s(o)||u(t)&&u(o))return o}var c=new et(i);return r?c=c.toKeyedSeq():u(t)||(c=c.toSetSeq()),(c=c.flatten(!0)).size=i.reduce((function(t,e){if(void 0!==t){var r=e.size;if(void 0!==r)return t+r}}),0),c}(this,t.call(arguments,0)))},includes:function(t){return this.some((function(e){return yt(e,t)}))},entries:function(){return this.__iterator(L)},every:function(t,e){Pt(this.size);var r=!0;return this.__iterate((function(n,i,o){if(!t.call(e,n,i,o))return r=!1,!1})),r},filter:function(t,e){return Ze(this,He(this,t,e,!0))},find:function(t,e,r){var n=this.findEntry(t,e);return n?n[1]:r},findEntry:function(t,e){var r;return this.__iterate((function(n,i,o){if(t.call(e,n,i,o))return r=[i,n],!1})),r},findLastEntry:function(t,e){return this.toSeq().reverse().findEntry(t,e)},forEach:function(t,e){return Pt(this.size),this.__iterate(e?t.bind(e):t)},join:function(t){Pt(this.size),t=void 0!==t?""+t:",";var e="",r=!0;return this.__iterate((function(n){r?r=!1:e+=t,e+=null!=n?n.toString():""})),e},keys:function(){return this.__iterator(I)},map:function(t,e){return Ze(this,Ue(this,t,e))},reduce:function(t,e,r){var n,i;return Pt(this.size),arguments.length<2?i=!0:n=e,this.__iterate((function(e,o,a){i?(i=!1,n=e):n=t.call(r,n,e,o,a)})),n},reduceRight:function(t,e,r){var n=this.toKeyedSeq().reverse();return n.reduce.apply(n,arguments)},reverse:function(){return Ze(this,qe(this,!0))},slice:function(t,e){return Ze(this,We(this,t,e,!0))},some:function(t,e){return!this.every(Nr(t),e)},sort:function(t){return Ze(this,Je(this,t))},values:function(){return this.__iterator(B)},butLast:function(){return this.slice(0,-1)},isEmpty:function(){return void 0!==this.size?0===this.size:!this.some((function(){return!0}))},count:function(t,e){return E(t?this.toSeq().filter(t,e):this)},countBy:function(t,e){return function(t,e,r){var n=zt().asMutable();return t.__iterate((function(i,o){n.update(e.call(r,i,o,t),0,(function(t){return t+1}))})),n.asImmutable()}(this,t,e)},equals:function(t){return vt(this,t)},entrySeq:function(){var t=this;if(t._cache)return new et(t._cache);var e=t.toSeq().map(Rr).toIndexedSeq();return e.fromEntrySeq=function(){return t.toSeq()},e},filterNot:function(t,e){return this.filter(Nr(t),e)},findLast:function(t,e,r){return this.toKeyedSeq().reverse().find(t,e,r)},first:function(){return this.find(D)},flatMap:function(t,e){return Ze(this,function(t,e,r){var n=er(t);return t.toSeq().map((function(i,o){return n(e.call(r,i,o,t))})).flatten(!0)}(this,t,e))},flatten:function(t){return Ze(this,Ge(this,t,!0))},fromEntrySeq:function(){return new ze(this)},get:function(t,e){return this.find((function(e,r){return yt(r,t)}),void 0,e)},getIn:function(t,e){for(var r,n=this,i=or(t);!(r=i.next()).done;){var o=r.value;if((n=n&&n.get?n.get(o,_):_)===_)return e}return n},groupBy:function(t,e){return function(t,e,r){var n=s(t),i=(l(t)?Ae():zt()).asMutable();t.__iterate((function(o,a){i.update(e.call(r,o,a,t),(function(t){return(t=t||[]).push(n?[a,o]:o),t}))}));var o=er(t);return i.map((function(e){return Ze(t,o(e))}))}(this,t,e)},has:function(t){return this.get(t,_)!==_},hasIn:function(t){return this.getIn(t,_)!==_},isSubset:function(t){return t="function"==typeof t.includes?t:r(t),this.every((function(e){return t.includes(e)}))},isSuperset:function(t){return(t="function"==typeof t.isSubset?t:r(t)).isSubset(this)},keySeq:function(){return this.toSeq().map(Lr).toIndexedSeq()},last:function(){return this.toSeq().reverse().first()},max:function(t){return Xe(this,t)},maxBy:function(t,e){return Xe(this,e,t)},min:function(t){return Xe(this,t?Fr(t):jr)},minBy:function(t,e){return Xe(this,e?Fr(e):jr,t)},rest:function(){return this.slice(1)},skip:function(t){return this.slice(Math.max(0,t))},skipLast:function(t){return Ze(this,this.toSeq().reverse().skip(t).reverse())},skipWhile:function(t,e){return Ze(this,Ve(this,t,e,!0))},skipUntil:function(t,e){return this.skipWhile(Nr(t),e)},sortBy:function(t,e){return Ze(this,Je(this,e,t))},take:function(t){return this.slice(0,Math.max(0,t))},takeLast:function(t){return Ze(this,this.toSeq().reverse().take(t).reverse())},takeWhile:function(t,e){return Ze(this,function(t,e,r){var n=rr(t);return n.__iterateUncached=function(n,i){var o=this;if(i)return this.cacheResult().__iterate(n,i);var a=0;return t.__iterate((function(t,i,s){return e.call(r,t,i,s)&&++a&&n(t,i,o)})),a},n.__iteratorUncached=function(n,i){var o=this;if(i)return this.cacheResult().__iterator(n,i);var a=t.__iterator(L,i),s=!0;return new P((function(){if(!s)return{value:void 0,done:!0};var t=a.next();if(t.done)return t;var i=t.value,u=i[0],c=i[1];return e.call(r,c,u,o)?n===L?t:z(n,u,c,t):(s=!1,{value:void 0,done:!0})}))},n}(this,t,e))},takeUntil:function(t,e){return this.takeWhile(Nr(t),e)},valueSeq:function(){return this.toIndexedSeq()},hashCode:function(){return this.__hash||(this.__hash=function(t){if(t.size===1/0)return 0;var e=l(t),r=s(t),n=e?1:0;return function(t,e){return e=Ct(e,3432918353),e=Ct(e<<15|e>>>-15,461845907),e=Ct(e<<13|e>>>-13,5),e=Ct((e=(e+3864292196|0)^t)^e>>>16,2246822507),Et((e=Ct(e^e>>>13,3266489909))^e>>>16)}(t.__iterate(r?e?function(t,e){n=31*n+Ur(Ot(t),Ot(e))|0}:function(t,e){n=n+Ur(Ot(t),Ot(e))|0}:e?function(t){n=31*n+Ot(t)|0}:function(t){n=n+Ot(t)|0}),n)}(this))}});var Ir=r.prototype;Ir[f]=!0,Ir[F]=Ir.values,Ir.__toJS=Ir.toArray,Ir.__toStringMapper=Pr,Ir.inspect=Ir.toSource=function(){return this.toString()},Ir.chain=Ir.flatMap,Ir.contains=Ir.includes,function(){try{Object.defineProperty(Ir,"length",{get:function(){if(!r.noLengthWarning){var t;try{throw new Error}catch(e){t=e.stack}if(-1===t.indexOf("_wrapObject"))return console&&console.warn&&console.warn("iterable.length has been deprecated, use iterable.size or iterable.count(). This warning will become a silent error in a future version. "+t),this.size}}})}catch(t){}}(),Ar(n,{flip:function(){return Ze(this,je(this))},findKey:function(t,e){var r=this.findEntry(t,e);return r&&r[0]},findLastKey:function(t,e){return this.toSeq().reverse().findKey(t,e)},keyOf:function(t){return this.findKey((function(e){return yt(e,t)}))},lastKeyOf:function(t){return this.findLastKey((function(e){return yt(e,t)}))},mapEntries:function(t,e){var r=this,n=0;return Ze(this,this.toSeq().map((function(i,o){return t.call(e,[o,i],n++,r)})).fromEntrySeq())},mapKeys:function(t,e){var r=this;return Ze(this,this.toSeq().flip().map((function(n,i){return t.call(e,n,i,r)})).flip())}});var Br=n.prototype;function Lr(t,e){return e}function Rr(t,e){return[e,t]}function Nr(t){return function(){return!t.apply(this,arguments)}}function Fr(t){return function(){return-t.apply(this,arguments)}}function Pr(t){return"string"==typeof t?JSON.stringify(t):t}function zr(){return C(arguments)}function jr(t,e){return t<e?1:t>e?-1:0}function Ur(t,e){return t^e+2654435769+(t<<6)+(t>>2)|0}return Br[p]=!0,Br[F]=Ir.entries,Br.__toJS=Ir.toObject,Br.__toStringMapper=function(t,e){return JSON.stringify(e)+": "+Pr(t)},Ar(i,{toKeyedSeq:function(){return new Ne(this,!1)},filter:function(t,e){return Ze(this,He(this,t,e,!1))},findIndex:function(t,e){var r=this.findEntry(t,e);return r?r[0]:-1},indexOf:function(t){var e=this.toKeyedSeq().keyOf(t);return void 0===e?-1:e},lastIndexOf:function(t){var e=this.toKeyedSeq().reverse().keyOf(t);return void 0===e?-1:e},reverse:function(){return Ze(this,qe(this,!1))},slice:function(t,e){return Ze(this,We(this,t,e,!1))},splice:function(t,e){var r=arguments.length;if(e=Math.max(0|e,0),0===r||2===r&&!e)return this;t=T(t,t<0?this.count():this.size);var n=this.slice(0,t);return Ze(this,1===r?n:n.concat(C(arguments,2),this.slice(t+e)))},findLastIndex:function(t,e){var r=this.toKeyedSeq().findLastKey(t,e);return void 0===r?-1:r},first:function(){return this.get(0)},flatten:function(t){return Ze(this,Ge(this,t,!1))},get:function(t,e){return(t=O(this,t))<0||this.size===1/0||void 0!==this.size&&t>this.size?e:this.find((function(e,r){return r===t}),void 0,e)},has:function(t){return(t=O(this,t))>=0&&(void 0!==this.size?this.size===1/0||t<this.size:-1!==this.indexOf(t))},interpose:function(t){return Ze(this,function(t,e){var r=rr(t);return r.size=t.size&&2*t.size-1,r.__iterateUncached=function(r,n){var i=this,o=0;return t.__iterate((function(t,n){return(!o||!1!==r(e,o++,i))&&!1!==r(t,o++,i)}),n),o},r.__iteratorUncached=function(r,n){var i,o=t.__iterator(B,n),a=0;return new P((function(){return(!i||a%2)&&(i=o.next()).done?i:a%2?z(r,a++,e):z(r,a++,i.value,i)}))},r}(this,t))},interleave:function(){var t=[this].concat(C(arguments)),e=$e(this.toSeq(),X.of,t),r=e.flatten(!0);return e.size&&(r.size=e.size*t.length),Ze(this,r)},last:function(){return this.get(-1)},skipWhile:function(t,e){return Ze(this,Ve(this,t,e,!1))},zip:function(){return Ze(this,$e(this,zr,[this].concat(C(arguments))))},zipWith:function(t){var e=C(arguments);return e[0]=this,Ze(this,$e(this,t,e))}}),i.prototype[h]=!0,i.prototype[d]=!0,Ar(o,{get:function(t,e){return this.has(t)?t:e},includes:function(t){return this.has(t)},keySeq:function(){return this.valueSeq()}}),o.prototype.has=Ir.includes,Ar(J,n.prototype),Ar(X,i.prototype),Ar(Y,o.prototype),Ar(wt,n.prototype),Ar(xt,i.prototype),Ar(kt,o.prototype),{Iterable:r,Seq:G,Collection:St,Map:zt,OrderedMap:Ae,List:ge,Stack:Cr,Set:fr,OrderedSet:_r,Record:ar,Range:bt,Repeat:mt,is:yt,fromJS:pt}}()},27418:t=>{"use strict";var e=Object.getOwnPropertySymbols,r=Object.prototype.hasOwnProperty,n=Object.prototype.propertyIsEnumerable;t.exports=function(){try{if(!Object.assign)return!1;var t=new String("abc");if(t[5]="de","5"===Object.getOwnPropertyNames(t)[0])return!1;for(var e={},r=0;r<10;r++)e["_"+String.fromCharCode(r)]=r;if("0123456789"!==Object.getOwnPropertyNames(e).map((function(t){return e[t]})).join(""))return!1;var n={};return"abcdefghijklmnopqrst".split("").forEach((function(t){n[t]=t})),"abcdefghijklmnopqrst"===Object.keys(Object.assign({},n)).join("")}catch(t){return!1}}()?Object.assign:function(t,i){for(var o,a,s=function(t){if(null==t)throw new TypeError("Object.assign cannot be called with null or undefined");return Object(t)}(t),u=1;u<arguments.length;u++){for(var c in o=Object(arguments[u]))r.call(o,c)&&(s[c]=o[c]);if(e){a=e(o);for(var l=0;l<a.length;l++)n.call(o,a[l])&&(s[a[l]]=o[a[l]])}}return s}},24889:function(t,e,r){!function(t,e){"use strict";if(!t.setImmediate){var r,n,i,o,a,s=1,u={},c=!1,l=t.document,f=Object.getPrototypeOf&&Object.getPrototypeOf(t);f=f&&f.setTimeout?f:t,"[object process]"==={}.toString.call(t.process)?r=function(t){process.nextTick((function(){h(t)}))}:function(){if(t.postMessage&&!t.importScripts){var e=!0,r=t.onmessage;return t.onmessage=function(){e=!1},t.postMessage("","*"),t.onmessage=r,e}}()?(o="setImmediate$"+Math.random()+"$",a=function(e){e.source===t&&"string"==typeof e.data&&0===e.data.indexOf(o)&&h(+e.data.slice(o.length))},t.addEventListener?t.addEventListener("message",a,!1):t.attachEvent("onmessage",a),r=function(e){t.postMessage(o+e,"*")}):t.MessageChannel?((i=new MessageChannel).port1.onmessage=function(t){h(t.data)},r=function(t){i.port2.postMessage(t)}):l&&"onreadystatechange"in l.createElement("script")?(n=l.documentElement,r=function(t){var e=l.createElement("script");e.onreadystatechange=function(){h(t),e.onreadystatechange=null,n.removeChild(e),e=null},n.appendChild(e)}):r=function(t){setTimeout(h,0,t)},f.setImmediate=function(t){"function"!=typeof t&&(t=new Function(""+t));for(var e=new Array(arguments.length-1),n=0;n<e.length;n++)e[n]=arguments[n+1];var i={callback:t,args:e};return u[s]=i,r(s),s++},f.clearImmediate=p}function p(t){delete u[t]}function h(t){if(c)setTimeout(h,0,t);else{var e=u[t];if(e){c=!0;try{!function(t){var e=t.callback,r=t.args;switch(r.length){case 0:e();break;case 1:e(r[0]);break;case 2:e(r[0],r[1]);break;case 3:e(r[0],r[1],r[2]);break;default:e.apply(undefined,r)}}(e)}finally{p(t),c=!1}}}}}("undefined"==typeof self?void 0===r.g?this:r.g:self)},42238:function(t,e,r){var n;!function(i,o){"use strict";var a="function",s="object",u="model",c="name",l="type",f="vendor",p="version",h="architecture",d="console",g="mobile",y="tablet",v="smarttv",m="wearable",_={extend:function(t,e){var r={};for(var n in t)e[n]&&e[n].length%2==0?r[n]=e[n].concat(t[n]):r[n]=t[n];return r},has:function(t,e){return"string"==typeof t&&-1!==e.toLowerCase().indexOf(t.toLowerCase())},lowerize:function(t){return t.toLowerCase()},major:function(t){return"string"==typeof t?t.replace(/[^\d\.]/g,"").split(".")[0]:o},trim:function(t){return t.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"")}},b={rgx:function(t,e){for(var r,n,i,u,c,l,f=0;f<e.length&&!c;){var p=e[f],h=e[f+1];for(r=n=0;r<p.length&&!c;)if(c=p[r++].exec(t))for(i=0;i<h.length;i++)l=c[++n],typeof(u=h[i])===s&&u.length>0?2==u.length?typeof u[1]==a?this[u[0]]=u[1].call(this,l):this[u[0]]=u[1]:3==u.length?typeof u[1]!==a||u[1].exec&&u[1].test?this[u[0]]=l?l.replace(u[1],u[2]):o:this[u[0]]=l?u[1].call(this,l,u[2]):o:4==u.length&&(this[u[0]]=l?u[3].call(this,l.replace(u[1],u[2])):o):this[u]=l||o;f+=2}},str:function(t,e){for(var r in e)if(typeof e[r]===s&&e[r].length>0){for(var n=0;n<e[r].length;n++)if(_.has(e[r][n],t))return"?"===r?o:r}else if(_.has(e[r],t))return"?"===r?o:r;return t}},S={browser:{oldsafari:{version:{"1.0":"/8",1.2:"/1",1.3:"/3","2.0":"/412","2.0.2":"/416","2.0.3":"/417","2.0.4":"/419","?":"/"}}},device:{amazon:{model:{"Fire Phone":["SD","KF"]}},sprint:{model:{"Evo Shift 4G":"7373KT"},vendor:{HTC:"APA",Sprint:"Sprint"}}},os:{windows:{version:{ME:"4.90","NT 3.11":"NT3.51","NT 4.0":"NT4.0",2e3:"NT 5.0",XP:["NT 5.1","NT 5.2"],Vista:"NT 6.0",7:"NT 6.1",8:"NT 6.2",8.1:"NT 6.3",10:["NT 6.4","NT 10.0"],RT:"ARM"}}}},w={browser:[[/(opera\smini)\/([\w\.-]+)/i,/(opera\s[mobiletab]{3,6}).+version\/([\w\.-]+)/i,/(opera).+version\/([\w\.]+)/i,/(opera)[\/\s]+([\w\.]+)/i],[c,p],[/(opios)[\/\s]+([\w\.]+)/i],[[c,"Opera Mini"],p],[/\s(opr)\/([\w\.]+)/i],[[c,"Opera"],p],[/(kindle)\/([\w\.]+)/i,/(lunascape|maxthon|netfront|jasmine|blazer|instagram)[\/\s]?([\w\.]*)/i,/(avant\s|iemobile|slim)(?:browser)?[\/\s]?([\w\.]*)/i,/(bidubrowser|baidubrowser)[\/\s]?([\w\.]+)/i,/(?:ms|\()(ie)\s([\w\.]+)/i,/(rekonq)\/([\w\.]*)/i,/(chromium|flock|rockmelt|midori|epiphany|silk|skyfire|ovibrowser|bolt|iron|vivaldi|iridium|phantomjs|bowser|quark|qupzilla|falkon)\/([\w\.-]+)/i,/(puffin|brave|whale|qqbrowserlite|qq)\/([\w\.]+)/i],[c,p],[/(konqueror)\/([\w\.]+)/i],[[c,"Konqueror"],p],[/(trident).+rv[:\s]([\w\.]{1,9}).+like\sgecko/i],[[c,"IE"],p],[/(edge|edgios|edga|edg)\/((\d+)?[\w\.]+)/i],[[c,"Edge"],p],[/(yabrowser)\/([\w\.]+)/i],[[c,"Yandex"],p],[/(Avast)\/([\w\.]+)/i],[[c,"Avast Secure Browser"],p],[/(AVG)\/([\w\.]+)/i],[[c,"AVG Secure Browser"],p],[/(focus)\/([\w\.]+)/i],[[c,"Firefox Focus"],p],[/(opt)\/([\w\.]+)/i],[[c,"Opera Touch"],p],[/((?:[\s\/])uc?\s?browser|(?:juc.+)ucweb)[\/\s]?([\w\.]+)/i],[[c,"UCBrowser"],p],[/(comodo_dragon)\/([\w\.]+)/i],[[c,/_/g," "],p],[/((?:windowswechat)? qbcore)\/([\w\.]+).*(?:windowswechat)?/i],[[c,"WeChat(Win) Desktop"],p],[/(micromessenger)\/([\w\.]+)/i],[[c,"WeChat"],p],[/m?(qqbrowser|baiduboxapp|2345Explorer)[\/\s]?([\w\.]+)/i],[c,p],[/(MetaSr)[\/\s]?([\w\.]+)/i],[c],[/(LBBROWSER)/i],[c],[/(weibo)__([\d\.]+)/i],[c,p],[/xiaomi\/miuibrowser\/([\w\.]+)/i],[p,[c,"MIUI Browser"]],[/;fbav\/([\w\.]+);/i],[p,[c,"Facebook"]],[/FBAN\/FBIOS|FB_IAB\/FB4A/i],[[c,"Facebook"]],[/safari\s(line)\/([\w\.]+)/i,/droid.+(line)\/([\w\.]+)\/iab/i],[c,p],[/headlesschrome(?:\/([\w\.]+)|\s)/i],[p,[c,"Chrome Headless"]],[/\swv\).+(chrome)\/([\w\.]+)/i],[[c,/(.+)/,"$1 WebView"],p],[/((?:oculus|samsung)browser)\/([\w\.]+)/i],[[c,/(.+(?:g|us))(.+)/,"$1 $2"],p],[/droid.+version\/([\w\.]+)\s+(?:mobile\s?safari|safari)*/i],[p,[c,"Android Browser"]],[/(coc_coc_browser)\/([\w\.]+)/i],[[c,"Coc Coc"],p],[/(sailfishbrowser)\/([\w\.]+)/i],[[c,"Sailfish Browser"],p],[/(chrome|omniweb|arora|[tizenoka]{5}\s?browser)\/v?([\w\.]+)/i],[c,p],[/(dolfin)\/([\w\.]+)/i],[[c,"Dolphin"],p],[/(qihu|qhbrowser|qihoobrowser|360browser)/i],[[c,"360 Browser"]],[/((?:android.+)crmo|crios)\/([\w\.]+)/i],[[c,"Chrome"],p],[/(coast)\/([\w\.]+)/i],[[c,"Opera Coast"],p],[/fxios\/([\w\.-]+)/i],[p,[c,"Firefox"]],[/version\/([\w\.]+)\s.*mobile\/\w+\s(safari)/i],[p,[c,"Mobile Safari"]],[/version\/([\w\.]+)\s.*(mobile\s?safari|safari)/i],[p,c],[/webkit.+?(gsa)\/([\w\.]+)\s.*(mobile\s?safari|safari)(\/[\w\.]+)/i],[[c,"GSA"],p],[/webkit.+?(mobile\s?safari|safari)(\/[\w\.]+)/i],[c,[p,b.str,S.browser.oldsafari.version]],[/(webkit|khtml)\/([\w\.]+)/i],[c,p],[/(navigator|netscape)\/([\w\.-]+)/i],[[c,"Netscape"],p],[/(swiftfox)/i,/(icedragon|iceweasel|camino|chimera|fennec|maemo\sbrowser|minimo|conkeror)[\/\s]?([\w\.\+]+)/i,/(firefox|seamonkey|k-meleon|icecat|iceape|firebird|phoenix|palemoon|basilisk|waterfox)\/([\w\.-]+)$/i,/(firefox)\/([\w\.]+)\s[\w\s\-]+\/[\w\.]+$/i,/(mozilla)\/([\w\.]+)\s.+rv\:.+gecko\/\d+/i,/(polaris|lynx|dillo|icab|doris|amaya|w3m|netsurf|sleipnir)[\/\s]?([\w\.]+)/i,/(links)\s\(([\w\.]+)/i,/(gobrowser)\/?([\w\.]*)/i,/(ice\s?browser)\/v?([\w\._]+)/i,/(mosaic)[\/\s]([\w\.]+)/i],[c,p]],cpu:[[/(?:(amd|x(?:(?:86|64)[_-])?|wow|win)64)[;\)]/i],[[h,"amd64"]],[/(ia32(?=;))/i],[[h,_.lowerize]],[/((?:i[346]|x)86)[;\)]/i],[[h,"ia32"]],[/windows\s(ce|mobile);\sppc;/i],[[h,"arm"]],[/((?:ppc|powerpc)(?:64)?)(?:\smac|;|\))/i],[[h,/ower/,"",_.lowerize]],[/(sun4\w)[;\)]/i],[[h,"sparc"]],[/((?:avr32|ia64(?=;))|68k(?=\))|arm(?:64|(?=v\d+[;l]))|(?=atmel\s)avr|(?:irix|mips|sparc)(?:64)?(?=;)|pa-risc)/i],[[h,_.lowerize]]],device:[[/\((ipad|playbook);[\w\s\),;-]+(rim|apple)/i],[u,f,[l,y]],[/applecoremedia\/[\w\.]+ \((ipad)/],[u,[f,"Apple"],[l,y]],[/(apple\s{0,1}tv)/i],[[u,"Apple TV"],[f,"Apple"],[l,v]],[/(archos)\s(gamepad2?)/i,/(hp).+(touchpad)/i,/(hp).+(tablet)/i,/(kindle)\/([\w\.]+)/i,/\s(nook)[\w\s]+build\/(\w+)/i,/(dell)\s(strea[kpr\s\d]*[\dko])/i],[f,u,[l,y]],[/(alexa)webm/i,/(kf[A-z]+)(\sbuild\/|\)).+silk\//i],[u,[f,"Amazon"],[l,y]],[/(sd|kf)[0349hijorstuw]+(\sbuild\/|\)).+silk\//i],[[u,b.str,S.device.amazon.model],[f,"Amazon"],[l,g]],[/droid.+aft([\w])(\sbuild\/|\))/i],[u,[f,"Amazon"],[l,v]],[/\((ip(?:hone|od)[\s\w]*);/i],[u,[f,"Apple"],[l,g]],[/(blackberry)[\s-]?(\w+)/i,/(blackberry|benq|palm(?=\-)|sonyericsson|acer|asus|dell|meizu|motorola|polytron)[\s_-]?([\w-]*)/i,/(hp)\s([\w\s]+\w)/i,/(asus)-?(\w+)/i],[f,u,[l,g]],[/\(bb10;\s(\w+)/i],[u,[f,"BlackBerry"],[l,g]],[/droid.+(transfo[prime\s]{4,10}\s\w+|eeepc|slider\s\w+|nexus 7|padfone|p00c)/i],[u,[f,"Asus"],[l,y]],[/sony\stablet\s[ps]\sbuild\//i,/(?:sony)?sgp\w+(?:\sbuild\/|\))/i],[[u,"Xperia Tablet"],[f,"Sony"],[l,y]],[/droid.+\s([c-g]\d{4}|so[-l]\w+|xq-a\w[4-7][12])(?=\sbuild\/|\).+chrome\/(?![1-6]{0,1}\d\.))/i],[u,[f,"Sony"],[l,g]],[/\s(ouya)\s/i,/(nintendo)\s([wids3utch]+)/i],[f,u,[l,d]],[/droid.+;\s(shield)\sbuild/i],[u,[f,"Nvidia"],[l,d]],[/(playstation\s[345portablevi]+)/i],[u,[f,"Sony"],[l,d]],[/(sprint\s(\w+))/i],[[f,b.str,S.device.sprint.vendor],[u,b.str,S.device.sprint.model],[l,g]],[/(htc)[;_\s-]{1,2}([\w\s]+(?=\)|\sbuild)|\w+)/i,/(zte)-(\w*)/i,/(alcatel|geeksphone|nexian|panasonic|(?=;\s)sony)[_\s-]?([\w-]*)/i],[f,[u,/_/g," "],[l,g]],[/(nexus\s9)/i],[u,[f,"HTC"],[l,y]],[/d\/huawei([\w\s-]+)[;\)]/i,/droid.+\s(nexus\s6p|vog-[at]?l\d\d|ane-[at]?l[x\d]\d|eml-a?l\d\da?|lya-[at]?l\d[\dc]|clt-a?l\d\di?)/i,/droid.+\s((?:A(?:GS2?|KA|LP|N[AE]|QM|RE|SK|TH)|B(?:A(?:C|H2)|G2|KL|LA|MH|Z[AKT])|C(?:AZ|DY|LT|OL|[MOR]R)|DUK|E(?:BG|DI|L[ES]|ML|V[AR])|FRD|G(?:LK|RA)|H(?:D[LN]|MA|LK|RY|WI)|INE|J(?:DN2|MM|NY|SN)|K(?:NT|OB|SA)|L(?:IO|LD|ON|[RY]A)|M(?:AR|ED|[HL]A|ON|RX|T7)|N(?:EO|TS|XT)|OXF|P(?:AR|CT|IC|LK|RA)|R(?:IO|VL)|S(?:C[ML]|EA|HT|PN|TF)|T(?:A[HS]|NY)|V(?:[CI]E|KY|OG|RD)|W(?:AS|LZ)|Y(?:635|AL))-[ATU][LN][01259][019])[;\)\s]/i],[u,[f,"Huawei"],[l,g]],[/droid.+(bah2?-a?[lw]\d{2})/i],[u,[f,"Huawei"],[l,y]],[/(microsoft);\s(lumia[\s\w]+)/i],[f,u,[l,g]],[/[\s\(;](xbox(?:\sone)?)[\s\);]/i],[u,[f,"Microsoft"],[l,d]],[/(kin\.[onetw]{3})/i],[[u,/\./g," "],[f,"Microsoft"],[l,g]],[/\s(milestone|droid(?:[2-4x]|\s(?:bionic|x2|pro|razr))?:?(\s4g)?)[\w\s]+build\//i,/\smot[\s-](\w*)/i,/(moto[\s\w\(\)]+(?=\sbuild|\)))/i,/(XT\d{3,4}) build\//i,/(nexus\s6)/i],[u,[f,"Motorola"],[l,g]],[/droid.+\s(mz60\d|xoom[\s2]{0,2})\sbuild\//i],[u,[f,"Motorola"],[l,y]],[/hbbtv\/\d+\.\d+\.\d+\s+\([\w\s]*;\s*(\w[^;]*);([^;]*)/i],[[f,_.trim],[u,_.trim],[l,v]],[/hbbtv.+maple;(\d+)/i],[[u,/^/,"SmartTV"],[f,"Samsung"],[l,v]],[/\(dtv[\);].+(aquos)/i],[u,[f,"Sharp"],[l,v]],[/droid.+((sch-i[89]0\d|shw-m380s|SM-P605|SM-P610|SM-P587|gt-p\d{4}|gt-n\d+|sgh-t8[56]9|nexus 10))/i,/((SM-T\w+))/i],[[f,"Samsung"],u,[l,y]],[/smart-tv.+(samsung)/i],[f,[l,v],u],[/((s[cgp]h-\w+|gt-\w+|galaxy\snexus|sm-\w[\w\d]+))/i,/\s(sam)(?:sung)[\s-]([\w-]+)/i,/sec-((sgh\w+))/i],[[f,"Samsung"],u,[l,g]],[/sie-(\w*)/i],[u,[f,"Siemens"],[l,g]],[/(maemo|nokia).*(n900|lumia\s\d+)/i,/(nokia)[\s_-]?([\w\.-]*)/i],[[f,"Nokia"],u,[l,g]],[/droid[x\d\.\s;]+\s([ab][1-7]\-?[0178a]\d\d?)/i],[u,[f,"Acer"],[l,y]],[/droid.+([vl]k\-?\d{3})\s+build/i],[u,[f,"LG"],[l,y]],[/droid\s3\.[\s\w;-]{10}(lg?)-([06cv9]{3,4})/i],[[f,"LG"],u,[l,y]],[/linux;\snetcast.+smarttv/i,/lg\snetcast\.tv-201\d/i],[[f,"LG"],u,[l,v]],[/(nexus\s[45])/i,/lg[e;\s\/-]+(\w*)/i,/droid.+lg(\-?[\d\w]+)\s+build/i],[u,[f,"LG"],[l,g]],[/(lenovo)\s?(s(?:5000|6000)(?:[\w-]+)|tab(?:[\s\w]+)|[\w-]+)/i],[f,u,[l,y]],[/droid.+(ideatab[a-z0-9\-\s]+)/i],[u,[f,"Lenovo"],[l,y]],[/(lenovo)[_\s-]?([\w-]+)/i],[f,u,[l,g]],[/linux;.+((jolla));/i],[f,u,[l,g]],[/((pebble))app\/[\d\.]+\s/i],[f,u,[l,m]],[/droid.+;\s(oppo)\s?([\w\s]+)\sbuild/i],[f,u,[l,g]],[/crkey/i],[[u,"Chromecast"],[f,"Google"],[l,v]],[/droid.+;\s(glass)\s\d/i],[u,[f,"Google"],[l,m]],[/droid.+;\s(pixel c)[\s)]/i],[u,[f,"Google"],[l,y]],[/droid.+;\s(pixel( [2-9]a?)?( xl)?)[\s)]/i],[u,[f,"Google"],[l,g]],[/droid.+;\s(\w+)\s+build\/hm\1/i,/droid.+(hm[\s\-_]?note?[\s_]?(?:\d\w)?)\sbuild/i,/droid.+(redmi[\s\-_]?(?:note|k)?(?:[\s_]?[\w\s]+))(?:\sbuild|\))/i,/droid.+(mi[\s\-_]?(?:a\d|one|one[\s_]plus|note lte)?[\s_]?(?:\d?\w?)[\s_]?(?:plus)?)\sbuild/i],[[u,/_/g," "],[f,"Xiaomi"],[l,g]],[/droid.+(mi[\s\-_]?(?:pad)(?:[\s_]?[\w\s]+))(?:\sbuild|\))/i],[[u,/_/g," "],[f,"Xiaomi"],[l,y]],[/droid.+;\s(m[1-5]\snote)\sbuild/i],[u,[f,"Meizu"],[l,g]],[/(mz)-([\w-]{2,})/i],[[f,"Meizu"],u,[l,g]],[/droid.+a000(1)\s+build/i,/droid.+oneplus\s(a\d{4})[\s)]/i],[u,[f,"OnePlus"],[l,g]],[/droid.+[;\/]\s*(RCT[\d\w]+)\s+build/i],[u,[f,"RCA"],[l,y]],[/droid.+[;\/\s](Venue[\d\s]{2,7})\s+build/i],[u,[f,"Dell"],[l,y]],[/droid.+[;\/]\s*(Q[T|M][\d\w]+)\s+build/i],[u,[f,"Verizon"],[l,y]],[/droid.+[;\/]\s+(Barnes[&\s]+Noble\s+|BN[RT])(\S(?:.*\S)?)\s+build/i],[[f,"Barnes & Noble"],u,[l,y]],[/droid.+[;\/]\s+(TM\d{3}.*\b)\s+build/i],[u,[f,"NuVision"],[l,y]],[/droid.+;\s(k88)\sbuild/i],[u,[f,"ZTE"],[l,y]],[/droid.+;\s(nx\d{3}j)\sbuild/i],[u,[f,"ZTE"],[l,g]],[/droid.+[;\/]\s*(gen\d{3})\s+build.*49h/i],[u,[f,"Swiss"],[l,g]],[/droid.+[;\/]\s*(zur\d{3})\s+build/i],[u,[f,"Swiss"],[l,y]],[/droid.+[;\/]\s*((Zeki)?TB.*\b)\s+build/i],[u,[f,"Zeki"],[l,y]],[/(android).+[;\/]\s+([YR]\d{2})\s+build/i,/droid.+[;\/]\s+(Dragon[\-\s]+Touch\s+|DT)(\w{5})\sbuild/i],[[f,"Dragon Touch"],u,[l,y]],[/droid.+[;\/]\s*(NS-?\w{0,9})\sbuild/i],[u,[f,"Insignia"],[l,y]],[/droid.+[;\/]\s*((NXA|Next)-?\w{0,9})\s+build/i],[u,[f,"NextBook"],[l,y]],[/droid.+[;\/]\s*(Xtreme\_)?(V(1[045]|2[015]|30|40|60|7[05]|90))\s+build/i],[[f,"Voice"],u,[l,g]],[/droid.+[;\/]\s*(LVTEL\-)?(V1[12])\s+build/i],[[f,"LvTel"],u,[l,g]],[/droid.+;\s(PH-1)\s/i],[u,[f,"Essential"],[l,g]],[/droid.+[;\/]\s*(V(100MD|700NA|7011|917G).*\b)\s+build/i],[u,[f,"Envizen"],[l,y]],[/droid.+[;\/]\s*(Le[\s\-]+Pan)[\s\-]+(\w{1,9})\s+build/i],[f,u,[l,y]],[/droid.+[;\/]\s*(Trio[\s\w\-\.]+)\s+build/i],[u,[f,"MachSpeed"],[l,y]],[/droid.+[;\/]\s*(Trinity)[\-\s]*(T\d{3})\s+build/i],[f,u,[l,y]],[/droid.+[;\/]\s*TU_(1491)\s+build/i],[u,[f,"Rotor"],[l,y]],[/droid.+(Gigaset)[\s\-]+(Q\w{1,9})\s+build/i],[f,u,[l,y]],[/[\s\/\(](android\stv|smart-?tv)[;\)\s]/i],[[l,v]],[/droid .+?; ([^;]+?)(?: build|\) applewebkit).+? mobile safari/i],[u,[l,g]],[/droid .+?;\s([^;]+?)(?: build|\) applewebkit).+?(?! mobile) safari/i],[u,[l,y]],[/\s(tablet|tab)[;\/]/i,/\s(mobile)(?:[;\/]|\ssafari)/i],[[l,_.lowerize],f,u],[/(android[\w\.\s\-]{0,9});.+build/i],[u,[f,"Generic"]],[/(phone)/i],[[l,g]]],engine:[[/windows.+\sedge\/([\w\.]+)/i],[p,[c,"EdgeHTML"]],[/webkit\/537\.36.+chrome\/(?!27)([\w\.]+)/i],[p,[c,"Blink"]],[/(presto)\/([\w\.]+)/i,/(webkit|trident|netfront|netsurf|amaya|lynx|w3m|goanna)\/([\w\.]+)/i,/(khtml|tasman|links)[\/\s]\(?([\w\.]+)/i,/(icab)[\/\s]([23]\.[\d\.]+)/i],[c,p],[/rv\:([\w\.]{1,9}).+(gecko)/i],[p,c]],os:[[/(xbox);\s+xbox\s([^\);]+)/i,/microsoft\s(windows)\s(vista|xp)/i],[c,p],[/(windows)\snt\s6\.2;\s(arm)/i,/(windows\sphone(?:\sos)*)[\s\/]?([\d\.\s\w]*)/i,/(windows\smobile|windows)[\s\/]?([ntce\d\.\s]+\w)/i],[c,[p,b.str,S.os.windows.version]],[/(win(?=3|9|n)|win\s9x\s)([nt\d\.]+)/i],[[c,"Windows"],[p,b.str,S.os.windows.version]],[/\((bb)(10);/i],[[c,"BlackBerry"],p],[/(blackberry)\w*\/?([\w\.]*)/i,/(tizen|kaios)[\/\s]([\w\.]+)/i,/(android|webos|palm\sos|qnx|bada|rim\stablet\sos|meego|sailfish|contiki)[\/\s-]?([\w\.]*)/i],[c,p],[/(symbian\s?os|symbos|s60(?=;))[\/\s-]?([\w\.]*)/i],[[c,"Symbian"],p],[/\((series40);/i],[c],[/mozilla.+\(mobile;.+gecko.+firefox/i],[[c,"Firefox OS"],p],[/crkey\/([\d\.]+)/i],[p,[c,"Chromecast"]],[/(nintendo|playstation)\s([wids345portablevuch]+)/i,/(mint)[\/\s\(]?(\w*)/i,/(mageia|vectorlinux)[;\s]/i,/(joli|[kxln]?ubuntu|debian|suse|opensuse|gentoo|(?=\s)arch|slackware|fedora|mandriva|centos|pclinuxos|redhat|zenwalk|linpus)[\/\s-]?(?!chrom)([\w\.-]*)/i,/(hurd|linux)\s?([\w\.]*)/i,/(gnu)\s?([\w\.]*)/i],[c,p],[/(cros)\s[\w]+\s([\w\.]+\w)/i],[[c,"Chromium OS"],p],[/(sunos)\s?([\w\.\d]*)/i],[[c,"Solaris"],p],[/\s([frentopc-]{0,4}bsd|dragonfly)\s?([\w\.]*)/i],[c,p],[/(haiku)\s(\w+)/i],[c,p],[/cfnetwork\/.+darwin/i,/ip[honead]{2,4}(?:.*os\s([\w]+)\slike\smac|;\sopera)/i],[[p,/_/g,"."],[c,"iOS"]],[/(mac\sos\sx)\s?([\w\s\.]*)/i,/(macintosh|mac(?=_powerpc)\s)/i],[[c,"Mac OS"],[p,/_/g,"."]],[/((?:open)?solaris)[\/\s-]?([\w\.]*)/i,/(aix)\s((\d)(?=\.|\)|\s)[\w\.])*/i,/(plan\s9|minix|beos|os\/2|amigaos|morphos|risc\sos|openvms|fuchsia)/i,/(unix)\s?([\w\.]*)/i],[c,p]]},x=function(t,e){if("object"==typeof t&&(e=t,t=o),!(this instanceof x))return new x(t,e).getResult();var r=t||(void 0!==i&&i.navigator&&i.navigator.userAgent?i.navigator.userAgent:""),n=e?_.extend(w,e):w;return this.getBrowser=function(){var t={name:o,version:o};return b.rgx.call(t,r,n.browser),t.major=_.major(t.version),t},this.getCPU=function(){var t={architecture:o};return b.rgx.call(t,r,n.cpu),t},this.getDevice=function(){var t={vendor:o,model:o,type:o};return b.rgx.call(t,r,n.device),t},this.getEngine=function(){var t={name:o,version:o};return b.rgx.call(t,r,n.engine),t},this.getOS=function(){var t={name:o,version:o};return b.rgx.call(t,r,n.os),t},this.getResult=function(){return{ua:this.getUA(),browser:this.getBrowser(),engine:this.getEngine(),os:this.getOS(),device:this.getDevice(),cpu:this.getCPU()}},this.getUA=function(){return r},this.setUA=function(t){return r=t,this},this};x.VERSION="0.7.25",x.BROWSER={NAME:c,MAJOR:"major",VERSION:p},x.CPU={ARCHITECTURE:h},x.DEVICE={MODEL:u,VENDOR:f,TYPE:l,CONSOLE:d,MOBILE:g,SMARTTV:v,TABLET:y,WEARABLE:m,EMBEDDED:"embedded"},x.ENGINE={NAME:c,VERSION:p},x.OS={NAME:c,VERSION:p},void 0!==e?(t.exports&&(e=t.exports=x),e.UAParser=x):(n=function(){return x}.call(e,r,e,t))===o||(t.exports=n);var k=void 0!==i&&(i.jQuery||i.Zepto);if(k&&!k.ua){var C=new x;k.ua=C.getResult(),k.ua.get=function(){return C.getUA()},k.ua.set=function(t){C.setUA(t);var e=C.getResult();for(var r in e)k.ua[r]=e[r]}}}("object"==typeof window?window:this)},99196:t=>{"use strict";t.exports=window.React},91850:t=>{"use strict";t.exports=window.ReactDOM}},e={};function r(n){var i=e[n];if(void 0!==i)return i.exports;var o=e[n]={exports:{}};return t[n].call(o.exports,o,o.exports,r),o.exports}r.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(t){if("object"==typeof window)return window}}();var n=r(9041);(window.yoast=window.yoast||{}).draftJs=n})();