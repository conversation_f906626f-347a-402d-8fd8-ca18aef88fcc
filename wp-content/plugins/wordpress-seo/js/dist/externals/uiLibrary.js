(()=>{var e={35800:function(e,t,n){!function(e,t){"use strict";function n(e){if(e&&e.__esModule)return e;var t=Object.create(null);return e&&Object.keys(e).forEach((function(n){if("default"!==n){var a=Object.getOwnPropertyDescriptor(e,n);Object.defineProperty(t,n,a.get?a:{enumerable:!0,get:function(){return e[n]}})}})),t.default=e,Object.freeze(t)}var a=n(t);function r(e,t){return r=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e},r(e,t)}var s={error:null},o=function(e){function t(){for(var t,n=arguments.length,a=new Array(n),r=0;r<n;r++)a[r]=arguments[r];return(t=e.call.apply(e,[this].concat(a))||this).state=s,t.resetErrorBoundary=function(){for(var e,n=arguments.length,a=new Array(n),r=0;r<n;r++)a[r]=arguments[r];null==t.props.onReset||(e=t.props).onReset.apply(e,a),t.reset()},t}var n,o;o=e,(n=t).prototype=Object.create(o.prototype),n.prototype.constructor=n,r(n,o),t.getDerivedStateFromError=function(e){return{error:e}};var i=t.prototype;return i.reset=function(){this.setState(s)},i.componentDidCatch=function(e,t){var n,a;null==(n=(a=this.props).onError)||n.call(a,e,t)},i.componentDidUpdate=function(e,t){var n,a,r,s,o=this.state.error,i=this.props.resetKeys;null!==o&&null!==t.error&&(void 0===(r=e.resetKeys)&&(r=[]),void 0===(s=i)&&(s=[]),r.length!==s.length||r.some((function(e,t){return!Object.is(e,s[t])})))&&(null==(n=(a=this.props).onResetKeysChange)||n.call(a,e.resetKeys,i),this.reset())},i.render=function(){var e=this.state.error,t=this.props,n=t.fallbackRender,r=t.FallbackComponent,s=t.fallback;if(null!==e){var o={error:e,resetErrorBoundary:this.resetErrorBoundary};if(a.isValidElement(s))return s;if("function"==typeof n)return n(o);if(r)return a.createElement(r,o);throw new Error("react-error-boundary requires either a fallback, fallbackRender, or FallbackComponent prop")}return this.props.children},t}(a.Component);e.ErrorBoundary=o,e.useErrorHandler=function(e){var t=a.useState(null),n=t[0],r=t[1];if(null!=e)throw e;if(null!=n)throw n;return r},e.withErrorBoundary=function(e,t){var n=function(n){return a.createElement(o,t,a.createElement(e,n))},r=e.displayName||e.name||"Unknown";return n.displayName="withErrorBoundary("+r+")",n},Object.defineProperty(e,"__esModule",{value:!0})}(t,n(99196))},44896:(e,t)=>{var n;!function(){"use strict";var a={}.hasOwnProperty;function r(){for(var e=[],t=0;t<arguments.length;t++){var n=arguments[t];if(n){var s=typeof n;if("string"===s||"number"===s)e.push(n);else if(Array.isArray(n)){if(n.length){var o=r.apply(null,n);o&&e.push(o)}}else if("object"===s){if(n.toString!==Object.prototype.toString&&!n.toString.toString().includes("[native code]")){e.push(n.toString());continue}for(var i in n)a.call(n,i)&&n[i]&&e.push(i)}}}return e.join(" ")}e.exports?(r.default=r,e.exports=r):void 0===(n=function(){return r}.apply(t,[]))||(e.exports=n)}()},99196:e=>{"use strict";e.exports=window.React}},t={};function n(a){var r=t[a];if(void 0!==r)return r.exports;var s=t[a]={exports:{}};return e[a].call(s.exports,s,s.exports,n),s.exports}n.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return n.d(t,{a:t}),t},n.d=(e,t)=>{for(var a in t)n.o(t,a)&&!n.o(e,a)&&Object.defineProperty(e,a,{enumerable:!0,get:t[a]})},n.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),n.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var a={};(()=>{"use strict";function e(){return e=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var a in n)Object.prototype.hasOwnProperty.call(n,a)&&(e[a]=n[a])}return e},e.apply(this,arguments)}n.r(a),n.d(a,{Alert:()=>C,Autocomplete:()=>Pt,AutocompleteField:()=>Ta,Badge:()=>It,Button:()=>Bt,Card:()=>Pa,Checkbox:()=>Vt,CheckboxGroup:()=>La,ChildrenLimiter:()=>Ha,Code:()=>Gt,ErrorBoundary:()=>Kt,FILE_IMPORT_STATUS:()=>Ya,FeatureUpsell:()=>Ua,FileImport:()=>tr,Label:()=>zt,Link:()=>Zt,Modal:()=>Kr,Notifications:()=>ts,Pagination:()=>ms,Paper:()=>an,ProgressBar:()=>sn,Radio:()=>ln,RadioGroup:()=>bs,Root:()=>Ns,Select:()=>Ln,SelectField:()=>xs,SidebarNavigation:()=>qs,SkeletonLoader:()=>In,Spinner:()=>qt,Table:()=>zn,TagField:()=>js,TagInput:()=>Wn,TextField:()=>Hs,TextInput:()=>Kn,Textarea:()=>Yn,TextareaField:()=>$s,Title:()=>Jn,Toast:()=>oa,Toggle:()=>ha,ToggleField:()=>Us,Tooltip:()=>xa,VALIDATION_ICON_MAP:()=>b,VALIDATION_VARIANTS:()=>y,ValidationIcon:()=>h,ValidationInput:()=>Rt,ValidationMessage:()=>x,useBeforeUnload:()=>Ws,useDescribedBy:()=>Ra,useMediaQuery:()=>Qs,useModalContext:()=>$r,useNavigationContext:()=>Ms,useNotificationsContext:()=>Yr,usePrevious:()=>Gs,useRootContext:()=>Ks,useSvgAria:()=>u,useToggleState:()=>ja});var t=n(44896),r=n.n(t);const s=window.yoast.propTypes;var o=n.n(s),i=n(99196),l=n.n(i);const c=window.lodash,u=(e=null)=>(0,i.useMemo)((()=>{const t={role:"img","aria-hidden":"true"};return null!==e&&(t.focusable=e?"true":"false"),t}),[e]),d=i.forwardRef((function(e,t){return i.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true",ref:t},e),i.createElement("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z",clipRule:"evenodd"}))})),p=i.forwardRef((function(e,t){return i.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true",ref:t},e),i.createElement("path",{fillRule:"evenodd",d:"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z",clipRule:"evenodd"}))})),m=i.forwardRef((function(e,t){return i.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true",ref:t},e),i.createElement("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z",clipRule:"evenodd"}))})),f=i.forwardRef((function(e,t){return i.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true",ref:t},e),i.createElement("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z",clipRule:"evenodd"}))})),y={success:"success",warning:"warning",info:"info",error:"error"},b={success:d,warning:p,info:m,error:f},v={variant:{success:"yst-validation-icon--success",warning:"yst-validation-icon--warning",info:"yst-validation-icon--info",error:"yst-validation-icon--error"}},g=({variant:t="info",className:n="",...a})=>{const s=(0,i.useMemo)((()=>b[t]),[t]),o=u();return s?l().createElement(s,e({},o,a,{className:r()("yst-validation-icon",v.variant[t],n)})):null};g.propTypes={variant:o().oneOf((0,c.values)(y)),className:o().string};const h=g,N={variant:{success:"yst-validation-message--success",warning:"yst-validation-message--warning",info:"yst-validation-message--info",error:"yst-validation-message--error"}},E=({as:t="p",variant:n="info",children:a,className:s="",...o})=>l().createElement(t,e({},o,{className:r()("yst-validation-message",N.variant[n],s)}),a);E.propTypes={as:o().elementType,variant:o().oneOf((0,c.keys)(N.variant)),message:o().node,className:o().string,children:o().node.isRequired};const x=E,R={variant:{info:"yst-alert--info",warning:"yst-alert--warning",success:"yst-alert--success",error:"yst-alert--error"}},w={alert:"alert",status:"status"},T=(0,i.forwardRef)((({children:t,role:n="status",as:a="span",variant:s="info",className:o="",...i},c)=>l().createElement(a,e({ref:c,className:r()("yst-alert",R.variant[s],o),role:w[n]},i),l().createElement(h,{variant:s,className:"yst-alert__icon"}),l().createElement(x,{as:"div",variant:s,className:"yst-alert__message"},t)))),O={children:o().node.isRequired,as:o().elementType,variant:o().oneOf(Object.keys(R.variant)),className:o().string,role:o().oneOf(Object.keys(w))};T.displayName="Alert",T.propTypes=O,T.defaultProps={as:"span",variant:"info",className:"",role:"status"};const C=T;var S=Object.defineProperty,k=(e,t,n)=>(((e,t,n)=>{t in e?S(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n})(e,"symbol"!=typeof t?t+"":t,n),n);let P=new class{constructor(){k(this,"current",this.detect()),k(this,"handoffState","pending"),k(this,"currentId",0)}set(e){this.current!==e&&(this.handoffState="pending",this.currentId=0,this.current=e)}reset(){this.set(this.detect())}nextId(){return++this.currentId}get isServer(){return"server"===this.current}get isClient(){return"client"===this.current}detect(){return"undefined"==typeof window||"undefined"==typeof document?"server":"client"}handoff(){"pending"===this.handoffState&&(this.handoffState="complete")}get isHandoffComplete(){return"complete"===this.handoffState}},_=(e,t)=>{P.isServer?(0,i.useEffect)(e,t):(0,i.useLayoutEffect)(e,t)};function L(e){let t=(0,i.useRef)(e);return _((()=>{t.current=e}),[e]),t}function F(e,t){let[n,a]=(0,i.useState)(e),r=L(e);return _((()=>a(r.current)),[r,a,...t]),n}function I(e){"function"==typeof queueMicrotask?queueMicrotask(e):Promise.resolve().then(e).catch((e=>setTimeout((()=>{throw e}))))}function M(){let e=[],t=[],n={enqueue(e){t.push(e)},addEventListener:(e,t,a,r)=>(e.addEventListener(t,a,r),n.add((()=>e.removeEventListener(t,a,r)))),requestAnimationFrame(...e){let t=requestAnimationFrame(...e);return n.add((()=>cancelAnimationFrame(t)))},nextFrame:(...e)=>n.requestAnimationFrame((()=>n.requestAnimationFrame(...e))),setTimeout(...e){let t=setTimeout(...e);return n.add((()=>clearTimeout(t)))},microTask(...e){let t={current:!0};return I((()=>{t.current&&e[0]()})),n.add((()=>{t.current=!1}))},add:t=>(e.push(t),()=>{let n=e.indexOf(t);if(n>=0){let[t]=e.splice(n,1);t()}}),dispose(){for(let t of e.splice(0))t()},async workQueue(){for(let e of t.splice(0))await e()}};return n}function D(){let[e]=(0,i.useState)(M);return(0,i.useEffect)((()=>()=>e.dispose()),[e]),e}let q=function(e){let t=L(e);return i.useCallback(((...e)=>t.current(...e)),[t])};function A(){let[e,t]=(0,i.useState)(P.isHandoffComplete);return e&&!1===P.isHandoffComplete&&t(!1),(0,i.useEffect)((()=>{!0!==e&&t(!0)}),[e]),(0,i.useEffect)((()=>P.handoff()),[]),e}var j;let B=null!=(j=i.useId)?j:function(){let e=A(),[t,n]=i.useState(e?()=>P.nextId():null);return _((()=>{null===t&&n(P.nextId())}),[t]),null!=t?""+t:void 0};function H(e,t,...n){if(e in t){let a=t[e];return"function"==typeof a?a(...n):a}let a=new Error(`Tried to handle "${e}" but there is no handler defined. Only defined handlers are: ${Object.keys(t).map((e=>`"${e}"`)).join(", ")}.`);throw Error.captureStackTrace&&Error.captureStackTrace(a,H),a}function z(e){return P.isServer?null:e instanceof Node?e.ownerDocument:null!=e&&e.hasOwnProperty("current")&&e.current instanceof Node?e.current.ownerDocument:document}let $=["[contentEditable=true]","[tabindex]","a[href]","area[href]","button:not([disabled])","iframe","input:not([disabled])","select:not([disabled])","textarea:not([disabled])"].map((e=>`${e}:not([tabindex='-1'])`)).join(",");var V,U,W=(e=>(e[e.First=1]="First",e[e.Previous=2]="Previous",e[e.Next=4]="Next",e[e.Last=8]="Last",e[e.WrapAround=16]="WrapAround",e[e.NoScroll=32]="NoScroll",e))(W||{}),G=((U=G||{})[U.Error=0]="Error",U[U.Overflow=1]="Overflow",U[U.Success=2]="Success",U[U.Underflow=3]="Underflow",U),K=((V=K||{})[V.Previous=-1]="Previous",V[V.Next=1]="Next",V);var Q=(e=>(e[e.Strict=0]="Strict",e[e.Loose=1]="Loose",e))(Q||{});function Y(e,t=0){var n;return e!==(null==(n=z(e))?void 0:n.body)&&H(t,{0:()=>e.matches($),1(){let t=e;for(;null!==t;){if(t.matches($))return!0;t=t.parentElement}return!1}})}function Z(e){null==e||e.focus({preventScroll:!0})}let X=["textarea","input"].join(",");function J(e,t=(e=>e)){return e.slice().sort(((e,n)=>{let a=t(e),r=t(n);if(null===a||null===r)return 0;let s=a.compareDocumentPosition(r);return s&Node.DOCUMENT_POSITION_FOLLOWING?-1:s&Node.DOCUMENT_POSITION_PRECEDING?1:0}))}function ee(e,t,{sorted:n=!0,relativeTo:a=null,skipElements:r=[]}={}){let s=Array.isArray(e)?e.length>0?e[0].ownerDocument:document:e.ownerDocument,o=Array.isArray(e)?n?J(e):e:function(e=document.body){return null==e?[]:Array.from(e.querySelectorAll($)).sort(((e,t)=>Math.sign((e.tabIndex||Number.MAX_SAFE_INTEGER)-(t.tabIndex||Number.MAX_SAFE_INTEGER))))}(e);r.length>0&&o.length>1&&(o=o.filter((e=>!r.includes(e)))),a=null!=a?a:s.activeElement;let i,l=(()=>{if(5&t)return 1;if(10&t)return-1;throw new Error("Missing Focus.First, Focus.Previous, Focus.Next or Focus.Last")})(),c=(()=>{if(1&t)return 0;if(2&t)return Math.max(0,o.indexOf(a))-1;if(4&t)return Math.max(0,o.indexOf(a))+1;if(8&t)return o.length-1;throw new Error("Missing Focus.First, Focus.Previous, Focus.Next or Focus.Last")})(),u=32&t?{preventScroll:!0}:{},d=0,p=o.length;do{if(d>=p||d+p<=0)return 0;let e=c+d;if(16&t)e=(e+p)%p;else{if(e<0)return 3;if(e>=p)return 1}i=o[e],null==i||i.focus(u),d+=l}while(i!==s.activeElement);return 6&t&&function(e){var t,n;return null!=(n=null==(t=null==e?void 0:e.matches)?void 0:t.call(e,X))&&n}(i)&&i.select(),i.hasAttribute("tabindex")||i.setAttribute("tabindex","0"),2}function te(e,t,n){let a=L(t);(0,i.useEffect)((()=>{function t(e){a.current(e)}return document.addEventListener(e,t,n),()=>document.removeEventListener(e,t,n)}),[e,n])}function ne(e,t,n=!0){let a=(0,i.useRef)(!1);function r(n,r){if(!a.current||n.defaultPrevented)return;let s=function e(t){return"function"==typeof t?e(t()):Array.isArray(t)||t instanceof Set?t:[t]}(e),o=r(n);if(null!==o&&o.getRootNode().contains(o)){for(let e of s){if(null===e)continue;let t=e instanceof HTMLElement?e:e.current;if(null!=t&&t.contains(o)||n.composed&&n.composedPath().includes(t))return}return!Y(o,Q.Loose)&&-1!==o.tabIndex&&n.preventDefault(),t(n,o)}}(0,i.useEffect)((()=>{requestAnimationFrame((()=>{a.current=n}))}),[n]);let s=(0,i.useRef)(null);te("mousedown",(e=>{var t,n;a.current&&(s.current=(null==(n=null==(t=e.composedPath)?void 0:t.call(e))?void 0:n[0])||e.target)}),!0),te("click",(e=>{!s.current||(r(e,(()=>s.current)),s.current=null)}),!0),te("blur",(e=>r(e,(()=>window.document.activeElement instanceof HTMLIFrameElement?window.document.activeElement:null))),!0)}function ae(e){var t;if(e.type)return e.type;let n=null!=(t=e.as)?t:"button";return"string"==typeof n&&"button"===n.toLowerCase()?"button":void 0}function re(e,t){let[n,a]=(0,i.useState)((()=>ae(e)));return _((()=>{a(ae(e))}),[e.type,e.as]),_((()=>{n||!t.current||t.current instanceof HTMLButtonElement&&!t.current.hasAttribute("type")&&a("button")}),[n,t]),n}let se=Symbol();function oe(e,t=!0){return Object.assign(e,{[se]:t})}function ie(...e){let t=(0,i.useRef)(e);(0,i.useEffect)((()=>{t.current=e}),[e]);let n=q((e=>{for(let n of t.current)null!=n&&("function"==typeof n?n(e):n.current=e)}));return e.every((e=>null==e||(null==e?void 0:e[se])))?void 0:n}var le=(e=>(e[e.First=0]="First",e[e.Previous=1]="Previous",e[e.Next=2]="Next",e[e.Last=3]="Last",e[e.Specific=4]="Specific",e[e.Nothing=5]="Nothing",e))(le||{});function ce(e,t){let n=t.resolveItems();if(n.length<=0)return null;let a=t.resolveActiveIndex(),r=null!=a?a:-1,s=(()=>{switch(e.focus){case 0:return n.findIndex((e=>!t.resolveDisabled(e)));case 1:{let e=n.slice().reverse().findIndex(((e,n,a)=>!(-1!==r&&a.length-n-1>=r||t.resolveDisabled(e))));return-1===e?e:n.length-1-e}case 2:return n.findIndex(((e,n)=>!(n<=r||t.resolveDisabled(e))));case 3:{let e=n.slice().reverse().findIndex((e=>!t.resolveDisabled(e)));return-1===e?e:n.length-1-e}case 4:return n.findIndex((n=>t.resolveId(n)===e.id));case 5:return null;default:!function(e){throw new Error("Unexpected object: "+e)}(e)}})();return-1===s?a:s}function ue(...e){return e.filter(Boolean).join(" ")}var de,pe=((de=pe||{})[de.None=0]="None",de[de.RenderStrategy=1]="RenderStrategy",de[de.Static=2]="Static",de),me=(e=>(e[e.Unmount=0]="Unmount",e[e.Hidden=1]="Hidden",e))(me||{});function fe({ourProps:e,theirProps:t,slot:n,defaultTag:a,features:r,visible:s=!0,name:o}){let i=be(t,e);if(s)return ye(i,n,a,o);let l=null!=r?r:0;if(2&l){let{static:e=!1,...t}=i;if(e)return ye(t,n,a,o)}if(1&l){let{unmount:e=!0,...t}=i;return H(e?0:1,{0:()=>null,1:()=>ye({...t,hidden:!0,style:{display:"none"}},n,a,o)})}return ye(i,n,a,o)}function ye(e,t={},n,a){var r;let{as:s=n,children:o,refName:l="ref",...c}=he(e,["unmount","static"]),u=void 0!==e.ref?{[l]:e.ref}:{},d="function"==typeof o?o(t):o;c.className&&"function"==typeof c.className&&(c.className=c.className(t));let p={};if(t){let e=!1,n=[];for(let[a,r]of Object.entries(t))"boolean"==typeof r&&(e=!0),!0===r&&n.push(a);e&&(p["data-headlessui-state"]=n.join(" "))}if(s===i.Fragment&&Object.keys(ge(c)).length>0){if(!(0,i.isValidElement)(d)||Array.isArray(d)&&d.length>1)throw new Error(['Passing props on "Fragment"!',"",`The current component <${a} /> is rendering a "Fragment".`,"However we need to passthrough the following props:",Object.keys(c).map((e=>`  - ${e}`)).join("\n"),"","You can apply a few solutions:",['Add an `as="..."` prop, to ensure that we render an actual element instead of a "Fragment".',"Render a single element as the child so that we can forward the props onto that element."].map((e=>`  - ${e}`)).join("\n")].join("\n"));let e=ue(null==(r=d.props)?void 0:r.className,c.className),t=e?{className:e}:{};return(0,i.cloneElement)(d,Object.assign({},be(d.props,ge(he(c,["ref"]))),p,u,function(...e){return{ref:e.every((e=>null==e))?void 0:t=>{for(let n of e)null!=n&&("function"==typeof n?n(t):n.current=t)}}}(d.ref,u.ref),t))}return(0,i.createElement)(s,Object.assign({},he(c,["ref"]),s!==i.Fragment&&u,s!==i.Fragment&&p),d)}function be(...e){if(0===e.length)return{};if(1===e.length)return e[0];let t={},n={};for(let a of e)for(let e in a)e.startsWith("on")&&"function"==typeof a[e]?(null!=n[e]||(n[e]=[]),n[e].push(a[e])):t[e]=a[e];if(t.disabled||t["aria-disabled"])return Object.assign(t,Object.fromEntries(Object.keys(n).map((e=>[e,void 0]))));for(let e in n)Object.assign(t,{[e](t,...a){let r=n[e];for(let e of r){if((t instanceof Event||(null==t?void 0:t.nativeEvent)instanceof Event)&&t.defaultPrevented)return;e(t,...a)}}});return t}function ve(e){var t;return Object.assign((0,i.forwardRef)(e),{displayName:null!=(t=e.displayName)?t:e.name})}function ge(e){let t=Object.assign({},e);for(let e in t)void 0===t[e]&&delete t[e];return t}function he(e,t=[]){let n=Object.assign({},e);for(let e of t)e in n&&delete n[e];return n}function Ne(e){let t=e.parentElement,n=null;for(;t&&!(t instanceof HTMLFieldSetElement);)t instanceof HTMLLegendElement&&(n=t),t=t.parentElement;let a=""===(null==t?void 0:t.getAttribute("disabled"));return(!a||!function(e){if(!e)return!1;let t=e.previousElementSibling;for(;null!==t;){if(t instanceof HTMLLegendElement)return!1;t=t.previousElementSibling}return!0}(n))&&a}function Ee(e={},t=null,n=[]){for(let[a,r]of Object.entries(e))Re(n,xe(t,a),r);return n}function xe(e,t){return e?e+"["+t+"]":t}function Re(e,t,n){if(Array.isArray(n))for(let[a,r]of n.entries())Re(e,xe(t,a.toString()),r);else n instanceof Date?e.push([t,n.toISOString()]):"boolean"==typeof n?e.push([t,n?"1":"0"]):"string"==typeof n?e.push([t,n]):"number"==typeof n?e.push([t,`${n}`]):null==n?e.push([t,""]):Ee(n,t,e)}var we=(e=>(e[e.None=1]="None",e[e.Focusable=2]="Focusable",e[e.Hidden=4]="Hidden",e))(we||{});let Te=ve((function(e,t){let{features:n=1,...a}=e;return fe({ourProps:{ref:t,"aria-hidden":2==(2&n)||void 0,style:{position:"fixed",top:1,left:1,width:1,height:0,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",borderWidth:"0",...4==(4&n)&&2!=(2&n)&&{display:"none"}}},theirProps:a,slot:{},defaultTag:"div",name:"Hidden"})})),Oe=(0,i.createContext)(null);Oe.displayName="OpenClosedContext";var Ce=(e=>(e[e.Open=0]="Open",e[e.Closed=1]="Closed",e))(Ce||{});function Se(){return(0,i.useContext)(Oe)}function ke({value:e,children:t}){return i.createElement(Oe.Provider,{value:e},t)}var Pe=(e=>(e.Space=" ",e.Enter="Enter",e.Escape="Escape",e.Backspace="Backspace",e.Delete="Delete",e.ArrowLeft="ArrowLeft",e.ArrowUp="ArrowUp",e.ArrowRight="ArrowRight",e.ArrowDown="ArrowDown",e.Home="Home",e.End="End",e.PageUp="PageUp",e.PageDown="PageDown",e.Tab="Tab",e))(Pe||{});function _e(e,t,n){let[a,r]=(0,i.useState)(n),s=void 0!==e,o=(0,i.useRef)(s),l=(0,i.useRef)(!1),c=(0,i.useRef)(!1);return!s||o.current||l.current?!s&&o.current&&!c.current&&(c.current=!0,o.current=s,console.error("A component is changing from controlled to uncontrolled. This may be caused by the value changing from a defined value to undefined, which should not happen.")):(l.current=!0,o.current=s,console.error("A component is changing from uncontrolled to controlled. This may be caused by the value changing from undefined to a defined value, which should not happen.")),[s?e:a,q((e=>(s||r(e),null==t?void 0:t(e))))]}function Le(e,t){let n=(0,i.useRef)([]),a=q(e);(0,i.useEffect)((()=>{let e=[...n.current];for(let[r,s]of t.entries())if(n.current[r]!==s){let r=a(t,e);return n.current=t,r}}),[a,...t])}function Fe(e){return[e.screenX,e.screenY]}function Ie(){let e=(0,i.useRef)([-1,-1]);return{wasMoved(t){let n=Fe(t);return(e.current[0]!==n[0]||e.current[1]!==n[1])&&(e.current=n,!0)},update(t){e.current=Fe(t)}}}var Me=(e=>(e[e.Open=0]="Open",e[e.Closed=1]="Closed",e))(Me||{}),De=(e=>(e[e.Single=0]="Single",e[e.Multi=1]="Multi",e))(De||{}),qe=(e=>(e[e.Pointer=0]="Pointer",e[e.Other=1]="Other",e))(qe||{}),Ae=(e=>(e[e.OpenCombobox=0]="OpenCombobox",e[e.CloseCombobox=1]="CloseCombobox",e[e.GoToOption=2]="GoToOption",e[e.RegisterOption=3]="RegisterOption",e[e.UnregisterOption=4]="UnregisterOption",e[e.RegisterLabel=5]="RegisterLabel",e))(Ae||{});function je(e,t=(e=>e)){let n=null!==e.activeOptionIndex?e.options[e.activeOptionIndex]:null,a=J(t(e.options.slice()),(e=>e.dataRef.current.domRef.current)),r=n?a.indexOf(n):null;return-1===r&&(r=null),{options:a,activeOptionIndex:r}}let Be={1:e=>e.dataRef.current.disabled||1===e.comboboxState?e:{...e,activeOptionIndex:null,comboboxState:1},0(e){if(e.dataRef.current.disabled||0===e.comboboxState)return e;let t=e.activeOptionIndex,{isSelected:n}=e.dataRef.current,a=e.options.findIndex((e=>n(e.dataRef.current.value)));return-1!==a&&(t=a),{...e,comboboxState:0,activeOptionIndex:t}},2(e,t){var n;if(e.dataRef.current.disabled||e.dataRef.current.optionsRef.current&&!e.dataRef.current.optionsPropsRef.current.static&&1===e.comboboxState)return e;let a=je(e);if(null===a.activeOptionIndex){let e=a.options.findIndex((e=>!e.dataRef.current.disabled));-1!==e&&(a.activeOptionIndex=e)}let r=ce(t,{resolveItems:()=>a.options,resolveActiveIndex:()=>a.activeOptionIndex,resolveId:e=>e.id,resolveDisabled:e=>e.dataRef.current.disabled});return{...e,...a,activeOptionIndex:r,activationTrigger:null!=(n=t.trigger)?n:1}},3:(e,t)=>{let n={id:t.id,dataRef:t.dataRef},a=je(e,(e=>[...e,n]));null===e.activeOptionIndex&&e.dataRef.current.isSelected(t.dataRef.current.value)&&(a.activeOptionIndex=a.options.indexOf(n));let r={...e,...a,activationTrigger:1};return e.dataRef.current.__demoMode&&void 0===e.dataRef.current.value&&(r.activeOptionIndex=0),r},4:(e,t)=>{let n=je(e,(e=>{let n=e.findIndex((e=>e.id===t.id));return-1!==n&&e.splice(n,1),e}));return{...e,...n,activationTrigger:1}},5:(e,t)=>({...e,labelId:t.id})},He=(0,i.createContext)(null);function ze(e){let t=(0,i.useContext)(He);if(null===t){let t=new Error(`<${e} /> is missing a parent <Combobox /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(t,ze),t}return t}He.displayName="ComboboxActionsContext";let $e=(0,i.createContext)(null);function Ve(e){let t=(0,i.useContext)($e);if(null===t){let t=new Error(`<${e} /> is missing a parent <Combobox /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(t,Ve),t}return t}function Ue(e,t){return H(t.type,Be,e,t)}$e.displayName="ComboboxDataContext";let We=i.Fragment,Ge=ve((function(e,t){let{value:n,defaultValue:a,onChange:r,name:s,by:o=((e,t)=>e===t),disabled:l=!1,__demoMode:c=!1,nullable:u=!1,multiple:d=!1,...p}=e,[m=(d?[]:void 0),f]=_e(n,r,a),[y,b]=(0,i.useReducer)(Ue,{dataRef:(0,i.createRef)(),comboboxState:c?0:1,options:[],activeOptionIndex:null,activationTrigger:1,labelId:null}),v=(0,i.useRef)(!1),g=(0,i.useRef)({static:!1,hold:!1}),h=(0,i.useRef)(null),N=(0,i.useRef)(null),E=(0,i.useRef)(null),x=(0,i.useRef)(null),R=q("string"==typeof o?(e,t)=>{let n=o;return(null==e?void 0:e[n])===(null==t?void 0:t[n])}:o),w=(0,i.useCallback)((e=>H(T.mode,{1:()=>m.some((t=>R(t,e))),0:()=>R(m,e)})),[m]),T=(0,i.useMemo)((()=>({...y,optionsPropsRef:g,labelRef:h,inputRef:N,buttonRef:E,optionsRef:x,value:m,defaultValue:a,disabled:l,mode:d?1:0,get activeOptionIndex(){if(v.current&&null===y.activeOptionIndex&&y.options.length>0){let e=y.options.findIndex((e=>!e.dataRef.current.disabled));if(-1!==e)return e}return y.activeOptionIndex},compare:R,isSelected:w,nullable:u,__demoMode:c})),[m,a,l,d,u,c,y]);_((()=>{y.dataRef.current=T}),[T]),ne([T.buttonRef,T.inputRef,T.optionsRef],(()=>A.closeCombobox()),0===T.comboboxState);let O=(0,i.useMemo)((()=>({open:0===T.comboboxState,disabled:l,activeIndex:T.activeOptionIndex,activeOption:null===T.activeOptionIndex?null:T.options[T.activeOptionIndex].dataRef.current.value,value:m})),[T,l,m]),C=q((e=>{let t=T.options.find((t=>t.id===e));!t||M(t.dataRef.current.value)})),S=q((()=>{if(null!==T.activeOptionIndex){let{dataRef:e,id:t}=T.options[T.activeOptionIndex];M(e.current.value),A.goToOption(le.Specific,t)}})),k=q((()=>{b({type:0}),v.current=!0})),P=q((()=>{b({type:1}),v.current=!1})),L=q(((e,t,n)=>(v.current=!1,e===le.Specific?b({type:2,focus:le.Specific,id:t,trigger:n}):b({type:2,focus:e,trigger:n})))),F=q(((e,t)=>(b({type:3,id:e,dataRef:t}),()=>b({type:4,id:e})))),I=q((e=>(b({type:5,id:e}),()=>b({type:5,id:null})))),M=q((e=>H(T.mode,{0:()=>null==f?void 0:f(e),1(){let t=T.value.slice(),n=t.findIndex((t=>R(t,e)));return-1===n?t.push(e):t.splice(n,1),null==f?void 0:f(t)}}))),A=(0,i.useMemo)((()=>({onChange:M,registerOption:F,registerLabel:I,goToOption:L,closeCombobox:P,openCombobox:k,selectActiveOption:S,selectOption:C})),[]),j=null===t?{}:{ref:t},B=(0,i.useRef)(null),z=D();return(0,i.useEffect)((()=>{!B.current||void 0!==a&&z.addEventListener(B.current,"reset",(()=>{M(a)}))}),[B,M]),i.createElement(He.Provider,{value:A},i.createElement($e.Provider,{value:T},i.createElement(ke,{value:H(T.comboboxState,{0:Ce.Open,1:Ce.Closed})},null!=s&&null!=m&&Ee({[s]:m}).map((([e,t],n)=>i.createElement(Te,{features:we.Hidden,ref:0===n?e=>{var t;B.current=null!=(t=null==e?void 0:e.closest("form"))?t:null}:void 0,...ge({key:e,as:"input",type:"hidden",hidden:!0,readOnly:!0,name:e,value:t})}))),fe({ourProps:j,theirProps:p,slot:O,defaultTag:We,name:"Combobox"}))))})),Ke=ve((function(e,t){var n,a,r,s;let o=B(),{id:l=`headlessui-combobox-input-${o}`,onChange:c,displayValue:u,type:d="text",...p}=e,m=Ve("Combobox.Input"),f=ze("Combobox.Input"),y=ie(m.inputRef,t),b=(0,i.useRef)(!1),v=D();var g;Le((([e,t],[n,a])=>{b.current||!m.inputRef.current||(0===a&&1===t||e!==n)&&(m.inputRef.current.value=e)}),["function"==typeof u&&void 0!==m.value?null!=(g=u(m.value))?g:"":"string"==typeof m.value?m.value:"",m.comboboxState]),Le((([e],[t])=>{if(0===e&&1===t){let e=m.inputRef.current;if(!e)return;let t=e.value,{selectionStart:n,selectionEnd:a,selectionDirection:r}=e;e.value="",e.value=t,null!==r?e.setSelectionRange(n,a,r):e.setSelectionRange(n,a)}}),[m.comboboxState]);let h=(0,i.useRef)(!1),N=q((()=>{h.current=!0})),E=q((()=>{setTimeout((()=>{h.current=!1}))})),x=q((e=>{switch(b.current=!0,e.key){case Pe.Backspace:case Pe.Delete:if(0!==m.mode||!m.nullable)return;let t=e.currentTarget;v.requestAnimationFrame((()=>{""===t.value&&(f.onChange(null),m.optionsRef.current&&(m.optionsRef.current.scrollTop=0),f.goToOption(le.Nothing))}));break;case Pe.Enter:if(b.current=!1,0!==m.comboboxState||h.current)return;if(e.preventDefault(),e.stopPropagation(),null===m.activeOptionIndex)return void f.closeCombobox();f.selectActiveOption(),0===m.mode&&f.closeCombobox();break;case Pe.ArrowDown:return b.current=!1,e.preventDefault(),e.stopPropagation(),H(m.comboboxState,{0:()=>{f.goToOption(le.Next)},1:()=>{f.openCombobox()}});case Pe.ArrowUp:return b.current=!1,e.preventDefault(),e.stopPropagation(),H(m.comboboxState,{0:()=>{f.goToOption(le.Previous)},1:()=>{f.openCombobox(),v.nextFrame((()=>{m.value||f.goToOption(le.Last)}))}});case Pe.Home:if(e.shiftKey)break;return b.current=!1,e.preventDefault(),e.stopPropagation(),f.goToOption(le.First);case Pe.PageUp:return b.current=!1,e.preventDefault(),e.stopPropagation(),f.goToOption(le.First);case Pe.End:if(e.shiftKey)break;return b.current=!1,e.preventDefault(),e.stopPropagation(),f.goToOption(le.Last);case Pe.PageDown:return b.current=!1,e.preventDefault(),e.stopPropagation(),f.goToOption(le.Last);case Pe.Escape:return b.current=!1,0!==m.comboboxState?void 0:(e.preventDefault(),m.optionsRef.current&&!m.optionsPropsRef.current.static&&e.stopPropagation(),f.closeCombobox());case Pe.Tab:if(b.current=!1,0!==m.comboboxState)return;0===m.mode&&f.selectActiveOption(),f.closeCombobox()}})),R=q((e=>{f.openCombobox(),null==c||c(e)})),w=q((()=>{b.current=!1})),T=F((()=>{if(m.labelId)return[m.labelId].join(" ")}),[m.labelId]),O=(0,i.useMemo)((()=>({open:0===m.comboboxState,disabled:m.disabled})),[m]);return fe({ourProps:{ref:y,id:l,role:"combobox",type:d,"aria-controls":null==(n=m.optionsRef.current)?void 0:n.id,"aria-expanded":m.disabled?void 0:0===m.comboboxState,"aria-activedescendant":null===m.activeOptionIndex||null==(a=m.options[m.activeOptionIndex])?void 0:a.id,"aria-multiselectable":1===m.mode||void 0,"aria-labelledby":T,"aria-autocomplete":"list",defaultValue:null!=(s=null!=(r=e.defaultValue)?r:void 0!==m.defaultValue?null==u?void 0:u(m.defaultValue):null)?s:m.defaultValue,disabled:m.disabled,onCompositionStart:N,onCompositionEnd:E,onKeyDown:x,onChange:R,onBlur:w},theirProps:p,slot:O,defaultTag:"input",name:"Combobox.Input"})})),Qe=ve((function(e,t){var n;let a=Ve("Combobox.Button"),r=ze("Combobox.Button"),s=ie(a.buttonRef,t),o=B(),{id:l=`headlessui-combobox-button-${o}`,...c}=e,u=D(),d=q((e=>{switch(e.key){case Pe.ArrowDown:return e.preventDefault(),e.stopPropagation(),1===a.comboboxState&&r.openCombobox(),u.nextFrame((()=>{var e;return null==(e=a.inputRef.current)?void 0:e.focus({preventScroll:!0})}));case Pe.ArrowUp:return e.preventDefault(),e.stopPropagation(),1===a.comboboxState&&(r.openCombobox(),u.nextFrame((()=>{a.value||r.goToOption(le.Last)}))),u.nextFrame((()=>{var e;return null==(e=a.inputRef.current)?void 0:e.focus({preventScroll:!0})}));case Pe.Escape:return 0!==a.comboboxState?void 0:(e.preventDefault(),a.optionsRef.current&&!a.optionsPropsRef.current.static&&e.stopPropagation(),r.closeCombobox(),u.nextFrame((()=>{var e;return null==(e=a.inputRef.current)?void 0:e.focus({preventScroll:!0})})));default:return}})),p=q((e=>{if(Ne(e.currentTarget))return e.preventDefault();0===a.comboboxState?r.closeCombobox():(e.preventDefault(),r.openCombobox()),u.nextFrame((()=>{var e;return null==(e=a.inputRef.current)?void 0:e.focus({preventScroll:!0})}))})),m=F((()=>{if(a.labelId)return[a.labelId,l].join(" ")}),[a.labelId,l]),f=(0,i.useMemo)((()=>({open:0===a.comboboxState,disabled:a.disabled,value:a.value})),[a]);return fe({ourProps:{ref:s,id:l,type:re(e,a.buttonRef),tabIndex:-1,"aria-haspopup":"listbox","aria-controls":null==(n=a.optionsRef.current)?void 0:n.id,"aria-expanded":a.disabled?void 0:0===a.comboboxState,"aria-labelledby":m,disabled:a.disabled,onClick:p,onKeyDown:d},theirProps:c,slot:f,defaultTag:"button",name:"Combobox.Button"})})),Ye=ve((function(e,t){let n=B(),{id:a=`headlessui-combobox-label-${n}`,...r}=e,s=Ve("Combobox.Label"),o=ze("Combobox.Label"),l=ie(s.labelRef,t);_((()=>o.registerLabel(a)),[a]);let c=q((()=>{var e;return null==(e=s.inputRef.current)?void 0:e.focus({preventScroll:!0})})),u=(0,i.useMemo)((()=>({open:0===s.comboboxState,disabled:s.disabled})),[s]);return fe({ourProps:{ref:l,id:a,onClick:c},theirProps:r,slot:u,defaultTag:"label",name:"Combobox.Label"})})),Ze=pe.RenderStrategy|pe.Static,Xe=ve((function(e,t){let n=B(),{id:a=`headlessui-combobox-options-${n}`,hold:r=!1,...s}=e,o=Ve("Combobox.Options"),l=ie(o.optionsRef,t),c=Se(),u=null!==c?c===Ce.Open:0===o.comboboxState;_((()=>{var t;o.optionsPropsRef.current.static=null!=(t=e.static)&&t}),[o.optionsPropsRef,e.static]),_((()=>{o.optionsPropsRef.current.hold=r}),[o.optionsPropsRef,r]),function({container:e,accept:t,walk:n,enabled:a=!0}){let r=(0,i.useRef)(t),s=(0,i.useRef)(n);(0,i.useEffect)((()=>{r.current=t,s.current=n}),[t,n]),_((()=>{if(!e||!a)return;let t=z(e);if(!t)return;let n=r.current,o=s.current,i=Object.assign((e=>n(e)),{acceptNode:n}),l=t.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,i,!1);for(;l.nextNode();)o(l.currentNode)}),[e,a,r,s])}({container:o.optionsRef.current,enabled:0===o.comboboxState,accept:e=>"option"===e.getAttribute("role")?NodeFilter.FILTER_REJECT:e.hasAttribute("role")?NodeFilter.FILTER_SKIP:NodeFilter.FILTER_ACCEPT,walk(e){e.setAttribute("role","none")}});let d=F((()=>{var e,t;return null!=(t=o.labelId)?t:null==(e=o.buttonRef.current)?void 0:e.id}),[o.labelId,o.buttonRef.current]);return fe({ourProps:{"aria-labelledby":d,role:"listbox",id:a,ref:l},theirProps:s,slot:(0,i.useMemo)((()=>({open:0===o.comboboxState})),[o]),defaultTag:"ul",features:Ze,visible:u,name:"Combobox.Options"})})),Je=ve((function(e,t){var n,a;let r=B(),{id:s=`headlessui-combobox-option-${r}`,disabled:o=!1,value:l,...c}=e,u=Ve("Combobox.Option"),d=ze("Combobox.Option"),p=null!==u.activeOptionIndex&&u.options[u.activeOptionIndex].id===s,m=u.isSelected(l),f=(0,i.useRef)(null),y=L({disabled:o,value:l,domRef:f,textValue:null==(a=null==(n=f.current)?void 0:n.textContent)?void 0:a.toLowerCase()}),b=ie(t,f),v=q((()=>d.selectOption(s)));_((()=>d.registerOption(s,y)),[y,s]);let g=(0,i.useRef)(!u.__demoMode);_((()=>{if(!u.__demoMode)return;let e=M();return e.requestAnimationFrame((()=>{g.current=!0})),e.dispose}),[]),_((()=>{if(0!==u.comboboxState||!p||!g.current||0===u.activationTrigger)return;let e=M();return e.requestAnimationFrame((()=>{var e,t;null==(t=null==(e=f.current)?void 0:e.scrollIntoView)||t.call(e,{block:"nearest"})})),e.dispose}),[f,p,u.comboboxState,u.activationTrigger,u.activeOptionIndex]);let h=q((e=>{if(o)return e.preventDefault();v(),0===u.mode&&d.closeCombobox()})),N=q((()=>{if(o)return d.goToOption(le.Nothing);d.goToOption(le.Specific,s)})),E=Ie(),x=q((e=>E.update(e))),R=q((e=>{!E.wasMoved(e)||o||p||d.goToOption(le.Specific,s,0)})),w=q((e=>{!E.wasMoved(e)||o||!p||u.optionsPropsRef.current.hold||d.goToOption(le.Nothing)})),T=(0,i.useMemo)((()=>({active:p,selected:m,disabled:o})),[p,m,o]);return fe({ourProps:{id:s,ref:b,role:"option",tabIndex:!0===o?void 0:-1,"aria-disabled":!0===o||void 0,"aria-selected":m,disabled:void 0,onClick:h,onFocus:N,onPointerEnter:x,onMouseEnter:x,onPointerMove:R,onMouseMove:R,onPointerLeave:w,onMouseLeave:w},theirProps:c,slot:T,defaultTag:"li",name:"Combobox.Option"})})),et=Object.assign(Ge,{Input:Ke,Button:Qe,Label:Ye,Options:Xe,Option:Je});function tt(){let e=(0,i.useRef)(!1);return _((()=>(e.current=!0,()=>{e.current=!1})),[]),e}function nt(e,...t){e&&t.length>0&&e.classList.add(...t)}function at(e,...t){e&&t.length>0&&e.classList.remove(...t)}function rt(e=""){return e.split(" ").filter((e=>e.trim().length>1))}let st=(0,i.createContext)(null);st.displayName="TransitionContext";var ot=(e=>(e.Visible="visible",e.Hidden="hidden",e))(ot||{});let it=(0,i.createContext)(null);function lt(e){return"children"in e?lt(e.children):e.current.filter((({el:e})=>null!==e.current)).filter((({state:e})=>"visible"===e)).length>0}function ct(e,t){let n=L(e),a=(0,i.useRef)([]),r=tt(),s=D(),o=q(((e,t=me.Hidden)=>{let o=a.current.findIndex((({el:t})=>t===e));-1!==o&&(H(t,{[me.Unmount](){a.current.splice(o,1)},[me.Hidden](){a.current[o].state="hidden"}}),s.microTask((()=>{var e;!lt(a)&&r.current&&(null==(e=n.current)||e.call(n))})))})),l=q((e=>{let t=a.current.find((({el:t})=>t===e));return t?"visible"!==t.state&&(t.state="visible"):a.current.push({el:e,state:"visible"}),()=>o(e,me.Unmount)})),c=(0,i.useRef)([]),u=(0,i.useRef)(Promise.resolve()),d=(0,i.useRef)({enter:[],leave:[],idle:[]}),p=q(((e,n,a)=>{c.current.splice(0),t&&(t.chains.current[n]=t.chains.current[n].filter((([t])=>t!==e))),null==t||t.chains.current[n].push([e,new Promise((e=>{c.current.push(e)}))]),null==t||t.chains.current[n].push([e,new Promise((e=>{Promise.all(d.current[n].map((([e,t])=>t))).then((()=>e()))}))]),"enter"===n?u.current=u.current.then((()=>null==t?void 0:t.wait.current)).then((()=>a(n))):a(n)})),m=q(((e,t,n)=>{Promise.all(d.current[t].splice(0).map((([e,t])=>t))).then((()=>{var e;null==(e=c.current.shift())||e()})).then((()=>n(t)))}));return(0,i.useMemo)((()=>({children:a,register:l,unregister:o,onStart:p,onStop:m,wait:u,chains:d})),[l,o,a,p,m,d,u])}function ut(){}it.displayName="NestingContext";let dt=["beforeEnter","afterEnter","beforeLeave","afterLeave"];function pt(e){var t;let n={};for(let a of dt)n[a]=null!=(t=e[a])?t:ut;return n}let mt=pe.RenderStrategy,ft=ve((function(e,t){let{beforeEnter:n,afterEnter:a,beforeLeave:r,afterLeave:s,enter:o,enterFrom:l,enterTo:c,entered:u,leave:d,leaveFrom:p,leaveTo:m,...f}=e,y=(0,i.useRef)(null),b=ie(y,t),v=f.unmount?me.Unmount:me.Hidden,{show:g,appear:h,initial:N}=function(){let e=(0,i.useContext)(st);if(null===e)throw new Error("A <Transition.Child /> is used but it is missing a parent <Transition /> or <Transition.Root />.");return e}(),[E,x]=(0,i.useState)(g?"visible":"hidden"),R=function(){let e=(0,i.useContext)(it);if(null===e)throw new Error("A <Transition.Child /> is used but it is missing a parent <Transition /> or <Transition.Root />.");return e}(),{register:w,unregister:T}=R,O=(0,i.useRef)(null);(0,i.useEffect)((()=>w(y)),[w,y]),(0,i.useEffect)((()=>{if(v===me.Hidden&&y.current)return g&&"visible"!==E?void x("visible"):H(E,{hidden:()=>T(y),visible:()=>w(y)})}),[E,y,w,T,g,v]);let C=L({enter:rt(o),enterFrom:rt(l),enterTo:rt(c),entered:rt(u),leave:rt(d),leaveFrom:rt(p),leaveTo:rt(m)}),S=function(e){let t=(0,i.useRef)(pt(e));return(0,i.useEffect)((()=>{t.current=pt(e)}),[e]),t}({beforeEnter:n,afterEnter:a,beforeLeave:r,afterLeave:s}),k=A();(0,i.useEffect)((()=>{if(k&&"visible"===E&&null===y.current)throw new Error("Did you forget to passthrough the `ref` to the actual DOM node?")}),[y,E,k]);let F=N&&!h,I=!k||F||O.current===g?"idle":g?"enter":"leave",j=q((e=>H(e,{enter:()=>S.current.beforeEnter(),leave:()=>S.current.beforeLeave(),idle:()=>{}}))),B=q((e=>H(e,{enter:()=>S.current.afterEnter(),leave:()=>S.current.afterLeave(),idle:()=>{}}))),z=ct((()=>{x("hidden"),T(y)}),R);(function({container:e,direction:t,classes:n,onStart:a,onStop:r}){let s=tt(),o=D(),i=L(t);_((()=>{let t=M();o.add(t.dispose);let l=e.current;if(l&&"idle"!==i.current&&s.current)return t.dispose(),a.current(i.current),t.add(function(e,t,n,a){let r=n?"enter":"leave",s=M(),o=void 0!==a?function(e){let t={called:!1};return(...n)=>{if(!t.called)return t.called=!0,e(...n)}}(a):()=>{};"enter"===r&&(e.removeAttribute("hidden"),e.style.display="");let i=H(r,{enter:()=>t.enter,leave:()=>t.leave}),l=H(r,{enter:()=>t.enterTo,leave:()=>t.leaveTo}),c=H(r,{enter:()=>t.enterFrom,leave:()=>t.leaveFrom});return at(e,...t.enter,...t.enterTo,...t.enterFrom,...t.leave,...t.leaveFrom,...t.leaveTo,...t.entered),nt(e,...i,...c),s.nextFrame((()=>{at(e,...c),nt(e,...l),function(e,t){let n=M();if(!e)return n.dispose;let{transitionDuration:a,transitionDelay:r}=getComputedStyle(e),[s,o]=[a,r].map((e=>{let[t=0]=e.split(",").filter(Boolean).map((e=>e.includes("ms")?parseFloat(e):1e3*parseFloat(e))).sort(((e,t)=>t-e));return t}));if(s+o!==0){let a=n.addEventListener(e,"transitionend",(e=>{e.target===e.currentTarget&&(t(),a())}))}else t();n.add((()=>t())),n.dispose}(e,(()=>(at(e,...i),nt(e,...t.entered),o())))})),s.dispose}(l,n.current,"enter"===i.current,(()=>{t.dispose(),r.current(i.current)}))),t.dispose}),[t])})({container:y,classes:C,direction:I,onStart:L((e=>{z.onStart(y,e,j)})),onStop:L((e=>{z.onStop(y,e,B),"leave"===e&&!lt(z)&&(x("hidden"),T(y))}))}),(0,i.useEffect)((()=>{!F||(v===me.Hidden?O.current=null:O.current=g)}),[g,F,E]);let $=f,V={ref:b};return h&&g&&P.isServer&&($={...$,className:ue(f.className,...C.current.enter,...C.current.enterFrom)}),i.createElement(it.Provider,{value:z},i.createElement(ke,{value:H(E,{visible:Ce.Open,hidden:Ce.Closed})},fe({ourProps:V,theirProps:$,defaultTag:"div",features:mt,visible:"visible"===E,name:"Transition.Child"})))})),yt=ve((function(e,t){let{show:n,appear:a=!1,unmount:r,...s}=e,o=(0,i.useRef)(null),l=ie(o,t);A();let c=Se();if(void 0===n&&null!==c&&(n=H(c,{[Ce.Open]:!0,[Ce.Closed]:!1})),![!0,!1].includes(n))throw new Error("A <Transition /> is used but it is missing a `show={true | false}` prop.");let[u,d]=(0,i.useState)(n?"visible":"hidden"),p=ct((()=>{d("hidden")})),[m,f]=(0,i.useState)(!0),y=(0,i.useRef)([n]);_((()=>{!1!==m&&y.current[y.current.length-1]!==n&&(y.current.push(n),f(!1))}),[y,n]);let b=(0,i.useMemo)((()=>({show:n,appear:a,initial:m})),[n,a,m]);(0,i.useEffect)((()=>{if(n)d("visible");else if(lt(p)){let e=o.current;if(!e)return;let t=e.getBoundingClientRect();0===t.x&&0===t.y&&0===t.width&&0===t.height&&d("hidden")}else d("hidden")}),[n,p]);let v={unmount:r};return i.createElement(it.Provider,{value:p},i.createElement(st.Provider,{value:b},fe({ourProps:{...v,as:i.Fragment,children:i.createElement(ft,{ref:l,...v,...s})},theirProps:{},defaultTag:i.Fragment,features:mt,visible:"visible"===u,name:"Transition"})))})),bt=ve((function(e,t){let n=null!==(0,i.useContext)(st),a=null!==Se();return i.createElement(i.Fragment,null,!n&&a?i.createElement(yt,{ref:t,...e}):i.createElement(ft,{ref:t,...e}))})),vt=Object.assign(yt,{Child:bt,Root:yt});const gt=i.forwardRef((function(e,t){return i.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:2,stroke:"currentColor","aria-hidden":"true",ref:t},e),i.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M6 18L18 6M6 6l12 12"}))})),ht=i.forwardRef((function(e,t){return i.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true",ref:t},e),i.createElement("path",{fillRule:"evenodd",d:"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z",clipRule:"evenodd"}))})),Nt=i.forwardRef((function(e,t){return i.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true",ref:t},e),i.createElement("path",{fillRule:"evenodd",d:"M10 3a1 1 0 01.707.293l3 3a1 1 0 01-1.414 1.414L10 5.414 7.707 7.707a1 1 0 01-1.414-1.414l3-3A1 1 0 0110 3zm-3.707 9.293a1 1 0 011.414 0L10 14.586l2.293-2.293a1 1 0 011.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z",clipRule:"evenodd"}))})),Et={variant:{success:"yst-validation-input--success",warning:"yst-validation-input--warning",info:"yst-validation-input--info",error:"yst-validation-input--error"}},xt=(0,i.forwardRef)((({as:t,validation:n={},className:a="",...s},o)=>l().createElement("div",{className:r()("yst-validation-input",(null==n?void 0:n.message)&&Et.variant[null==n?void 0:n.variant])},l().createElement(t,e({ref:o},s,{className:r()("yst-validation-input__input",a)})),(null==n?void 0:n.message)&&l().createElement(h,{variant:null==n?void 0:n.variant,className:"yst-validation-input__icon"}))));xt.displayName="ValidationInput",xt.propTypes={as:o().elementType.isRequired,validation:o().shape({variant:o().string,message:o().node}),className:o().string},xt.defaultProps={validation:{},className:""};const Rt=xt,wt=(0,i.forwardRef)(((t,n)=>l().createElement(et.Button,e({as:"div",ref:n},t))));wt.displayName="AutocompleteButton";const Tt=({children:t,value:n})=>{const a=u(),s=(0,i.useCallback)((({active:e,selected:t})=>r()("yst-autocomplete__option",t&&"yst-autocomplete__option--selected",e&&!t&&"yst-autocomplete__option--active")),[]);return l().createElement(et.Option,{className:s,value:n},(({selected:n})=>l().createElement(l().Fragment,null,l().createElement("span",{className:r()("yst-autocomplete__option-label",n&&"yst-font-semibold")},t),n&&l().createElement(ht,e({className:"yst-autocomplete__option-check"},a)))))},Ot={children:o().node,value:o().oneOfType([o().string,o().number,o().bool]).isRequired};Tt.propTypes=Ot;const Ct=({onChange:t,svgAriaProps:n,screenReaderText:a})=>{const r=(0,i.useCallback)((e=>{e.preventDefault(),t(null)}),[t]);return l().createElement("button",{type:"button",className:"yst-mr-4 yst-flex yst-items-center",onClick:r},l().createElement("span",{className:"yst-sr-only"},a),l().createElement(gt,e({className:"yst-text-slate-400 yst-w-5 yst-h-5"},n)),l().createElement("div",{className:"yst-w-2 yst-mr-2 yst-border-r-slate-200 yst-border-r yst-h-7"}))};Ct.propTypes={onChange:o().func.isRequired,svgAriaProps:o().object.isRequired,screenReaderText:o().string.isRequired};const St=(0,i.forwardRef)((({id:t,value:n,children:a,selectedLabel:s,label:o,labelProps:d,labelSuffix:p,onChange:m,onQueryChange:f,validation:y,placeholder:b,className:v,buttonProps:g,clearButtonScreenReaderText:h,disabled:N,...E},x)=>{const R=(0,i.useCallback)((0,c.constant)(s),[s]),w=u();return l().createElement(et,e({ref:x,as:"div",value:n,onChange:m,className:r()("yst-autocomplete",N&&"yst-autocomplete--disabled",v),disabled:N},E),o&&l().createElement("div",{className:"yst-flex yst-items-center yst-mb-2"},l().createElement(et.Label,d,o),p),l().createElement("div",{className:"yst-relative"},l().createElement(Rt,e({as:wt,"data-id":t,validation:y,className:"yst-autocomplete__button"},g),l().createElement(et.Input,{className:"yst-autocomplete__input",autoComplete:"off",placeholder:b,displayValue:R,onChange:f}),E.nullable&&s&&l().createElement(Ct,{onChange:m,svgAriaProps:w,screenReaderText:h}),!(null!=y&&y.message)&&l().createElement(Nt,e({className:"yst-autocomplete__button-icon"},w))),l().createElement(vt,{as:i.Fragment,enter:"yst-transition yst-duration-100 yst-ease-out",enterFrom:"yst-transform yst-scale-95 yst-opacity-0",enterTo:"yst-transform yst-scale-100 yst-opacity-100",leave:"yst-transition yst-duration-75 yst-ease-out",leaveFrom:"yst-transform yst-scale-100 yst-opacity-100",leaveTo:"yst-transform yst-scale-95 yst-opacity-0"},l().createElement(et.Options,{className:"yst-autocomplete__options"},a))))})),kt={id:o().string.isRequired,value:o().oneOfType([o().string,o().number,o().bool]),children:o().node,selectedLabel:o().string,label:o().string,labelProps:o().object,labelSuffix:o().node,onChange:o().func.isRequired,onQueryChange:o().func.isRequired,validation:o().shape({variant:o().string,message:o().node}),placeholder:o().string,className:o().string,buttonProps:o().object,clearButtonScreenReaderText:o().string,nullable:o().bool,disabled:o().bool};St.displayName="Autocomplete",St.propTypes=kt,St.defaultProps={children:null,value:null,selectedLabel:"",label:"",labelProps:{},labelSuffix:null,validation:{},placeholder:"",className:"",buttonProps:{},clearButtonScreenReaderText:"Clear",nullable:!1,disabled:!1},St.Option=Tt,St.Option.displayName="Autocomplete.Option";const Pt=St,_t={variant:{info:"yst-badge--info",upsell:"yst-badge--upsell",plain:"yst-badge--plain"},size:{default:"",small:"yst-badge--small",large:"yst-badge--large"}},Lt=(0,i.forwardRef)((({children:t,as:n,variant:a,size:s,className:o,...i},c)=>l().createElement(n,e({ref:c,className:r()("yst-badge",_t.variant[a],_t.size[s],o)},i),t))),Ft={children:o().node.isRequired,as:o().elementType,variant:o().oneOf(Object.keys(_t.variant)),size:o().oneOf(Object.keys(_t.size)),className:o().string};Lt.displayName="Badge",Lt.propTypes=Ft,Lt.defaultProps={as:"span",variant:"info",size:"default",className:""};const It=Lt,Mt={variant:{default:"",primary:"yst-text-primary-500",white:"yst-text-white"},size:{3:"yst-w-3 yst-h-3",4:"yst-w-4 yst-h-4",8:"yst-w-8 yst-h-8"}},Dt=(0,i.forwardRef)((({variant:t,size:n,className:a},s)=>{const o=u();return l().createElement("svg",e({ref:s,xmlns:"http://www.w3.org/2000/svg/",fill:"none",viewBox:"0 0 24 24",className:r()("yst-animate-spin",Mt.variant[t],Mt.size[n],a)},o),l().createElement("circle",{className:"yst-opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),l().createElement("path",{className:"yst-opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"}))}));Dt.displayName="Spinner",Dt.propTypes={variant:o().oneOf((0,c.keys)(Mt.variant)),size:o().oneOf((0,c.keys)(Mt.size)),className:o().string},Dt.defaultProps={variant:"default",size:"4",className:""};const qt=Dt,At={variant:{primary:"yst-button--primary",secondary:"yst-button--secondary",tertiary:"yst-button--tertiary",error:"yst-button--error",upsell:"yst-button--upsell"},size:{default:"",small:"yst-button--small",large:"yst-button--large","extra-large":"yst-button--extra-large"}},jt=(0,i.forwardRef)((({children:t,as:n,type:a,variant:s,size:o,isLoading:i,disabled:c,className:u,...d},p)=>l().createElement(n,e({type:a||"button"===n&&"button"||void 0,disabled:c,ref:p,className:r()("yst-button",At.variant[s],At.size[o],i&&"yst-cursor-wait",c&&"yst-button--disabled",u)},d),i&&l().createElement(qt,{size:"small"===o?"3":"4",className:"yst--ml-1 yst-mr-2"}),t)));jt.displayName="Button",jt.propTypes={children:o().node.isRequired,as:o().elementType,type:o().oneOf(["button","submit","reset"]),variant:o().oneOf((0,c.keys)(At.variant)),size:o().oneOf((0,c.keys)(At.size)),isLoading:o().bool,disabled:o().bool,className:o().string},jt.defaultProps={as:"button",type:void 0,variant:"primary",size:"default",isLoading:!1,disabled:!1,className:""};const Bt=jt,Ht=(0,i.forwardRef)((({as:t,className:n,label:a,children:s,...o},i)=>l().createElement(t,e({ref:i,className:r()("yst-label",n)},o),a||s||null)));Ht.displayName="Label",Ht.propTypes={label:o().string,children:o().string,as:o().elementType,className:o().string},Ht.defaultProps={label:"",children:"",as:"label",className:""};const zt=Ht,$t=(0,i.forwardRef)((({id:t,name:n,value:a,label:s,disabled:o,className:i,...c},u)=>l().createElement("div",{className:r()("yst-checkbox",o&&"yst-checkbox--disabled",i)},l().createElement("input",e({ref:u,type:"checkbox",id:t,name:n,value:a,disabled:o,className:"yst-checkbox__input"},c)),l().createElement(zt,{htmlFor:t,className:"yst-checkbox__label",label:s}))));$t.displayName="Checkbox",$t.propTypes={id:o().string.isRequired,name:o().string.isRequired,value:o().string.isRequired,label:o().string.isRequired,className:o().string,disabled:o().bool},$t.defaultProps={className:"",disabled:!1};const Vt=$t,Ut={variant:{default:"",block:"yst-code--block"}},Wt=(0,i.forwardRef)((({children:t,variant:n="default",className:a="",...s},o)=>l().createElement("code",e({ref:o,className:r()("yst-code",Ut.variant[n],a)},s),t)));Wt.displayName="Code",Wt.propTypes={children:o().node.isRequired,variant:o().oneOf(Object.keys(Ut.variant)),className:o().string},Wt.defaultProps={variant:"default",className:""};const Gt=Wt,Kt=n(35800).ErrorBoundary,Qt={variant:{default:"yst-link--default",primary:"yst-link--primary",error:"yst-link--error"}},Yt=(0,i.forwardRef)((({as:t,variant:n,className:a,children:s,...o},i)=>l().createElement(t,e({ref:i,className:r()("yst-link",Qt.variant[n],a)},o),s)));Yt.displayName="Link",Yt.propTypes={children:o().node.isRequired,variant:o().oneOf(Object.keys(Qt.variant)),as:o().elementType,className:o().string},Yt.defaultProps={as:"a",variant:"default",className:""};const Zt=Yt,Xt=({as:e,className:t,children:n})=>l().createElement(e,{className:r()("yst-paper__content",t)},n);Xt.propTypes={as:o().node,className:o().string,children:o().node.isRequired},Xt.defaultProps={as:"div",className:""};const Jt=Xt,en=({as:e,className:t,children:n})=>l().createElement(e,{className:r()("yst-paper__header",t)},n);en.propTypes={as:o().node,className:o().string,children:o().node.isRequired},en.defaultProps={as:"header",className:""};const tn=en,nn=(0,i.forwardRef)((({as:e="div",className:t="",children:n},a)=>l().createElement(e,{ref:a,className:r()("yst-paper",t)},n)));nn.displayName="Paper",nn.propTypes={as:o().node,className:o().string,children:o().node.isRequired},nn.defaultProps={as:"div",className:""},nn.Header=tn,nn.Header.displayName="Paper.Header",nn.Content=Jt,nn.Content.displayName="Paper.Content";const an=nn,rn=(0,i.forwardRef)((({min:t,max:n,progress:a,className:s,...o},c)=>{const u=(0,i.useMemo)((()=>a/(n-t)*100),[t,n,a]);return l().createElement("div",e({ref:c,"aria-hidden":"true",className:r()("yst-progress-bar",s)},o),l().createElement("div",{className:"yst-progress-bar__progress",style:{width:`${u}%`}}))}));rn.displayName="ProgressBar",rn.propTypes={min:o().number.isRequired,max:o().number.isRequired,progress:o().number.isRequired,className:o().string},rn.defaultProps={className:""};const sn=rn,on=(0,i.forwardRef)((({id:t,name:n,value:a,label:s,screenReaderLabel:o,variant:i,disabled:c,className:p,isLabelDangerousHtml:m,...f},y)=>{const b=u();return"inline-block"===i?l().createElement("div",{className:r()("yst-radio","yst-radio--inline-block",c&&"yst-radio--disabled",p)},l().createElement("input",e({type:"radio",id:t,name:n,value:a,disabled:c,className:"yst-radio__input","aria-label":o},f)),l().createElement("span",{className:"yst-radio__content"},l().createElement(zt,{htmlFor:t,className:"yst-radio__label",label:m?null:s,dangerouslySetInnerHTML:m?{__html:s}:null}),l().createElement(d,e({className:"yst-radio__check"},b)))):l().createElement("div",{className:r()("yst-radio",c&&"yst-radio--disabled",p)},l().createElement("input",e({ref:y,type:"radio",id:t,name:n,value:a,disabled:c,className:"yst-radio__input"},f)),l().createElement(zt,{htmlFor:t,className:"yst-radio__label",label:m?null:s,dangerouslySetInnerHTML:m?{__html:s}:null}))}));on.displayName="Radio",on.propTypes={name:o().string.isRequired,id:o().string.isRequired,value:o().string.isRequired,label:o().string.isRequired,isLabelDangerousHtml:o().bool,screenReaderLabel:o().string,variant:o().oneOf(Object.keys({default:"","inline-block":"yst-radio--inline-block"})),disabled:o().bool,className:o().string},on.defaultProps={screenReaderLabel:"",variant:"default",disabled:!1,className:"",isLabelDangerousHtml:!1};const ln=on;var cn=(e=>(e[e.Open=0]="Open",e[e.Closed=1]="Closed",e))(cn||{}),un=(e=>(e[e.Single=0]="Single",e[e.Multi=1]="Multi",e))(un||{}),dn=(e=>(e[e.Pointer=0]="Pointer",e[e.Other=1]="Other",e))(dn||{}),pn=(e=>(e[e.OpenListbox=0]="OpenListbox",e[e.CloseListbox=1]="CloseListbox",e[e.GoToOption=2]="GoToOption",e[e.Search=3]="Search",e[e.ClearSearch=4]="ClearSearch",e[e.RegisterOption=5]="RegisterOption",e[e.UnregisterOption=6]="UnregisterOption",e[e.RegisterLabel=7]="RegisterLabel",e))(pn||{});function mn(e,t=(e=>e)){let n=null!==e.activeOptionIndex?e.options[e.activeOptionIndex]:null,a=J(t(e.options.slice()),(e=>e.dataRef.current.domRef.current)),r=n?a.indexOf(n):null;return-1===r&&(r=null),{options:a,activeOptionIndex:r}}let fn={1:e=>e.dataRef.current.disabled||1===e.listboxState?e:{...e,activeOptionIndex:null,listboxState:1},0(e){if(e.dataRef.current.disabled||0===e.listboxState)return e;let t=e.activeOptionIndex,{isSelected:n}=e.dataRef.current,a=e.options.findIndex((e=>n(e.dataRef.current.value)));return-1!==a&&(t=a),{...e,listboxState:0,activeOptionIndex:t}},2(e,t){var n;if(e.dataRef.current.disabled||1===e.listboxState)return e;let a=mn(e),r=ce(t,{resolveItems:()=>a.options,resolveActiveIndex:()=>a.activeOptionIndex,resolveId:e=>e.id,resolveDisabled:e=>e.dataRef.current.disabled});return{...e,...a,searchQuery:"",activeOptionIndex:r,activationTrigger:null!=(n=t.trigger)?n:1}},3:(e,t)=>{if(e.dataRef.current.disabled||1===e.listboxState)return e;let n=""!==e.searchQuery?0:1,a=e.searchQuery+t.value.toLowerCase(),r=(null!==e.activeOptionIndex?e.options.slice(e.activeOptionIndex+n).concat(e.options.slice(0,e.activeOptionIndex+n)):e.options).find((e=>{var t;return!e.dataRef.current.disabled&&(null==(t=e.dataRef.current.textValue)?void 0:t.startsWith(a))})),s=r?e.options.indexOf(r):-1;return-1===s||s===e.activeOptionIndex?{...e,searchQuery:a}:{...e,searchQuery:a,activeOptionIndex:s,activationTrigger:1}},4:e=>e.dataRef.current.disabled||1===e.listboxState||""===e.searchQuery?e:{...e,searchQuery:""},5:(e,t)=>{let n={id:t.id,dataRef:t.dataRef},a=mn(e,(e=>[...e,n]));return null===e.activeOptionIndex&&e.dataRef.current.isSelected(t.dataRef.current.value)&&(a.activeOptionIndex=a.options.indexOf(n)),{...e,...a}},6:(e,t)=>{let n=mn(e,(e=>{let n=e.findIndex((e=>e.id===t.id));return-1!==n&&e.splice(n,1),e}));return{...e,...n,activationTrigger:1}},7:(e,t)=>({...e,labelId:t.id})},yn=(0,i.createContext)(null);function bn(e){let t=(0,i.useContext)(yn);if(null===t){let t=new Error(`<${e} /> is missing a parent <Listbox /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(t,bn),t}return t}yn.displayName="ListboxActionsContext";let vn=(0,i.createContext)(null);function gn(e){let t=(0,i.useContext)(vn);if(null===t){let t=new Error(`<${e} /> is missing a parent <Listbox /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(t,gn),t}return t}function hn(e,t){return H(t.type,fn,e,t)}vn.displayName="ListboxDataContext";let Nn=i.Fragment,En=ve((function(e,t){let{value:n,defaultValue:a,name:r,onChange:s,by:o=((e,t)=>e===t),disabled:l=!1,horizontal:c=!1,multiple:u=!1,...d}=e;const p=c?"horizontal":"vertical";let m=ie(t),[f=(u?[]:void 0),y]=_e(n,s,a),[b,v]=(0,i.useReducer)(hn,{dataRef:(0,i.createRef)(),listboxState:1,options:[],searchQuery:"",labelId:null,activeOptionIndex:null,activationTrigger:1}),g=(0,i.useRef)({static:!1,hold:!1}),h=(0,i.useRef)(null),N=(0,i.useRef)(null),E=(0,i.useRef)(null),x=q("string"==typeof o?(e,t)=>{let n=o;return(null==e?void 0:e[n])===(null==t?void 0:t[n])}:o),R=(0,i.useCallback)((e=>H(w.mode,{1:()=>f.some((t=>x(t,e))),0:()=>x(f,e)})),[f]),w=(0,i.useMemo)((()=>({...b,value:f,disabled:l,mode:u?1:0,orientation:p,compare:x,isSelected:R,optionsPropsRef:g,labelRef:h,buttonRef:N,optionsRef:E})),[f,l,u,b]);_((()=>{b.dataRef.current=w}),[w]),ne([w.buttonRef,w.optionsRef],((e,t)=>{var n;v({type:1}),Y(t,Q.Loose)||(e.preventDefault(),null==(n=w.buttonRef.current)||n.focus())}),0===w.listboxState);let T=(0,i.useMemo)((()=>({open:0===w.listboxState,disabled:l,value:f})),[w,l,f]),O=q((e=>{let t=w.options.find((t=>t.id===e));!t||I(t.dataRef.current.value)})),C=q((()=>{if(null!==w.activeOptionIndex){let{dataRef:e,id:t}=w.options[w.activeOptionIndex];I(e.current.value),v({type:2,focus:le.Specific,id:t})}})),S=q((()=>v({type:0}))),k=q((()=>v({type:1}))),P=q(((e,t,n)=>e===le.Specific?v({type:2,focus:le.Specific,id:t,trigger:n}):v({type:2,focus:e,trigger:n}))),L=q(((e,t)=>(v({type:5,id:e,dataRef:t}),()=>v({type:6,id:e})))),F=q((e=>(v({type:7,id:e}),()=>v({type:7,id:null})))),I=q((e=>H(w.mode,{0:()=>null==y?void 0:y(e),1(){let t=w.value.slice(),n=t.findIndex((t=>x(t,e)));return-1===n?t.push(e):t.splice(n,1),null==y?void 0:y(t)}}))),M=q((e=>v({type:3,value:e}))),A=q((()=>v({type:4}))),j=(0,i.useMemo)((()=>({onChange:I,registerOption:L,registerLabel:F,goToOption:P,closeListbox:k,openListbox:S,selectActiveOption:C,selectOption:O,search:M,clearSearch:A})),[]),B={ref:m},z=(0,i.useRef)(null),$=D();return(0,i.useEffect)((()=>{!z.current||void 0!==a&&$.addEventListener(z.current,"reset",(()=>{I(a)}))}),[z,I]),i.createElement(yn.Provider,{value:j},i.createElement(vn.Provider,{value:w},i.createElement(ke,{value:H(w.listboxState,{0:Ce.Open,1:Ce.Closed})},null!=r&&null!=f&&Ee({[r]:f}).map((([e,t],n)=>i.createElement(Te,{features:we.Hidden,ref:0===n?e=>{var t;z.current=null!=(t=null==e?void 0:e.closest("form"))?t:null}:void 0,...ge({key:e,as:"input",type:"hidden",hidden:!0,readOnly:!0,name:e,value:t})}))),fe({ourProps:B,theirProps:d,slot:T,defaultTag:Nn,name:"Listbox"}))))})),xn=ve((function(e,t){var n;let a=B(),{id:r=`headlessui-listbox-button-${a}`,...s}=e,o=gn("Listbox.Button"),l=bn("Listbox.Button"),c=ie(o.buttonRef,t),u=D(),d=q((e=>{switch(e.key){case Pe.Space:case Pe.Enter:case Pe.ArrowDown:e.preventDefault(),l.openListbox(),u.nextFrame((()=>{o.value||l.goToOption(le.First)}));break;case Pe.ArrowUp:e.preventDefault(),l.openListbox(),u.nextFrame((()=>{o.value||l.goToOption(le.Last)}))}})),p=q((e=>{e.key===Pe.Space&&e.preventDefault()})),m=q((e=>{if(Ne(e.currentTarget))return e.preventDefault();0===o.listboxState?(l.closeListbox(),u.nextFrame((()=>{var e;return null==(e=o.buttonRef.current)?void 0:e.focus({preventScroll:!0})}))):(e.preventDefault(),l.openListbox())})),f=F((()=>{if(o.labelId)return[o.labelId,r].join(" ")}),[o.labelId,r]),y=(0,i.useMemo)((()=>({open:0===o.listboxState,disabled:o.disabled,value:o.value})),[o]);return fe({ourProps:{ref:c,id:r,type:re(e,o.buttonRef),"aria-haspopup":"listbox","aria-controls":null==(n=o.optionsRef.current)?void 0:n.id,"aria-expanded":o.disabled?void 0:0===o.listboxState,"aria-labelledby":f,disabled:o.disabled,onKeyDown:d,onKeyUp:p,onClick:m},theirProps:s,slot:y,defaultTag:"button",name:"Listbox.Button"})})),Rn=ve((function(e,t){let n=B(),{id:a=`headlessui-listbox-label-${n}`,...r}=e,s=gn("Listbox.Label"),o=bn("Listbox.Label"),l=ie(s.labelRef,t);_((()=>o.registerLabel(a)),[a]);let c=q((()=>{var e;return null==(e=s.buttonRef.current)?void 0:e.focus({preventScroll:!0})})),u=(0,i.useMemo)((()=>({open:0===s.listboxState,disabled:s.disabled})),[s]);return fe({ourProps:{ref:l,id:a,onClick:c},theirProps:r,slot:u,defaultTag:"label",name:"Listbox.Label"})})),wn=pe.RenderStrategy|pe.Static,Tn=ve((function(e,t){var n;let a=B(),{id:r=`headlessui-listbox-options-${a}`,...s}=e,o=gn("Listbox.Options"),l=bn("Listbox.Options"),c=ie(o.optionsRef,t),u=D(),d=D(),p=Se(),m=null!==p?p===Ce.Open:0===o.listboxState;(0,i.useEffect)((()=>{var e;let t=o.optionsRef.current;!t||0===o.listboxState&&t!==(null==(e=z(t))?void 0:e.activeElement)&&t.focus({preventScroll:!0})}),[o.listboxState,o.optionsRef]);let f=q((e=>{switch(d.dispose(),e.key){case Pe.Space:if(""!==o.searchQuery)return e.preventDefault(),e.stopPropagation(),l.search(e.key);case Pe.Enter:if(e.preventDefault(),e.stopPropagation(),null!==o.activeOptionIndex){let{dataRef:e}=o.options[o.activeOptionIndex];l.onChange(e.current.value)}0===o.mode&&(l.closeListbox(),M().nextFrame((()=>{var e;return null==(e=o.buttonRef.current)?void 0:e.focus({preventScroll:!0})})));break;case H(o.orientation,{vertical:Pe.ArrowDown,horizontal:Pe.ArrowRight}):return e.preventDefault(),e.stopPropagation(),l.goToOption(le.Next);case H(o.orientation,{vertical:Pe.ArrowUp,horizontal:Pe.ArrowLeft}):return e.preventDefault(),e.stopPropagation(),l.goToOption(le.Previous);case Pe.Home:case Pe.PageUp:return e.preventDefault(),e.stopPropagation(),l.goToOption(le.First);case Pe.End:case Pe.PageDown:return e.preventDefault(),e.stopPropagation(),l.goToOption(le.Last);case Pe.Escape:return e.preventDefault(),e.stopPropagation(),l.closeListbox(),u.nextFrame((()=>{var e;return null==(e=o.buttonRef.current)?void 0:e.focus({preventScroll:!0})}));case Pe.Tab:e.preventDefault(),e.stopPropagation();break;default:1===e.key.length&&(l.search(e.key),d.setTimeout((()=>l.clearSearch()),350))}})),y=F((()=>{var e,t,n;return null!=(n=null==(e=o.labelRef.current)?void 0:e.id)?n:null==(t=o.buttonRef.current)?void 0:t.id}),[o.labelRef.current,o.buttonRef.current]),b=(0,i.useMemo)((()=>({open:0===o.listboxState})),[o]);return fe({ourProps:{"aria-activedescendant":null===o.activeOptionIndex||null==(n=o.options[o.activeOptionIndex])?void 0:n.id,"aria-multiselectable":1===o.mode||void 0,"aria-labelledby":y,"aria-orientation":o.orientation,id:r,onKeyDown:f,role:"listbox",tabIndex:0,ref:c},theirProps:s,slot:b,defaultTag:"ul",features:wn,visible:m,name:"Listbox.Options"})})),On=ve((function(e,t){let n=B(),{id:a=`headlessui-listbox-option-${n}`,disabled:r=!1,value:s,...o}=e,l=gn("Listbox.Option"),c=bn("Listbox.Option"),u=null!==l.activeOptionIndex&&l.options[l.activeOptionIndex].id===a,d=l.isSelected(s),p=(0,i.useRef)(null),m=L({disabled:r,value:s,domRef:p,get textValue(){var e,t;return null==(t=null==(e=p.current)?void 0:e.textContent)?void 0:t.toLowerCase()}}),f=ie(t,p);_((()=>{if(0!==l.listboxState||!u||0===l.activationTrigger)return;let e=M();return e.requestAnimationFrame((()=>{var e,t;null==(t=null==(e=p.current)?void 0:e.scrollIntoView)||t.call(e,{block:"nearest"})})),e.dispose}),[p,u,l.listboxState,l.activationTrigger,l.activeOptionIndex]),_((()=>c.registerOption(a,m)),[m,a]);let y=q((e=>{if(r)return e.preventDefault();c.onChange(s),0===l.mode&&(c.closeListbox(),M().nextFrame((()=>{var e;return null==(e=l.buttonRef.current)?void 0:e.focus({preventScroll:!0})})))})),b=q((()=>{if(r)return c.goToOption(le.Nothing);c.goToOption(le.Specific,a)})),v=Ie(),g=q((e=>v.update(e))),h=q((e=>{!v.wasMoved(e)||r||u||c.goToOption(le.Specific,a,0)})),N=q((e=>{!v.wasMoved(e)||r||!u||c.goToOption(le.Nothing)})),E=(0,i.useMemo)((()=>({active:u,selected:d,disabled:r})),[u,d,r]);return fe({ourProps:{id:a,ref:f,role:"option",tabIndex:!0===r?void 0:-1,"aria-disabled":!0===r||void 0,"aria-selected":d,disabled:void 0,onClick:y,onFocus:b,onPointerEnter:g,onMouseEnter:g,onPointerMove:h,onMouseMove:h,onPointerLeave:N,onMouseLeave:N},theirProps:o,slot:E,defaultTag:"li",name:"Listbox.Option"})})),Cn=Object.assign(En,{Button:xn,Label:Rn,Options:Tn,Option:On});const Sn={value:o().oneOfType([o().string,o().number,o().bool]).isRequired,label:o().string.isRequired},kn=({value:t,label:n})=>{const a=u(),s=(0,i.useCallback)((({active:e,selected:t})=>r()("yst-select__option",e&&"yst-select__option--active",t&&"yst-select__option--selected")),[]);return l().createElement(Cn.Option,{value:t,className:s},(({selected:t})=>l().createElement(l().Fragment,null,l().createElement("span",{className:r()("yst-select__option-label",t&&"yst-font-semibold")},n),t&&l().createElement(ht,e({className:"yst-select__option-check"},a)))))};kn.propTypes=Sn;const Pn=(0,i.forwardRef)((({id:t,value:n,options:a,children:s,selectedLabel:o,label:c,labelProps:d,labelSuffix:p,onChange:m,disabled:f,validation:y,className:b,buttonProps:v,...g},h)=>{const N=(0,i.useMemo)((()=>a.find((e=>n===(null==e?void 0:e.value)))||a[0]),[n,a]),E=u();return l().createElement(Cn,e({ref:h,as:"div",value:n,onChange:m,disabled:f,className:r()("yst-select",f&&"yst-select--disabled",b)},g),c&&l().createElement("div",{className:"yst-flex yst-items-center yst-mb-2"},l().createElement(Cn.Label,e({as:zt},d),c),p),l().createElement(Rt,e({as:Cn.Button,"data-id":t,className:"yst-select__button",validation:y},v),l().createElement("span",{className:"yst-select__button-label"},o||(null==N?void 0:N.label)||""),!(null!=y&&y.message)&&l().createElement(Nt,e({className:"yst-select__button-icon"},E))),l().createElement(vt,{as:i.Fragment,enter:"yst-transition yst-duration-100 yst-ease-out",enterFrom:"yst-transform yst-scale-95 yst-opacity-0",enterTo:"yst-transform yst-scale-100 yst-opacity-100",leave:"yst-transition yst-duration-75 yst-ease-out",leaveFrom:"yst-transform yst-scale-100 yst-opacity-100",leaveTo:"yst-transform yst-scale-95 yst-opacity-0"},l().createElement(Cn.Options,{className:"yst-select__options"},s||a.map((t=>l().createElement(kn,e({key:t.value},t)))))))}));Pn.displayName="Select",Pn.propTypes={id:o().string.isRequired,value:o().oneOfType([o().string,o().number,o().bool]).isRequired,options:o().arrayOf(o().shape(Sn)),children:o().node,selectedLabel:o().string,label:o().string,labelProps:o().object,labelSuffix:o().node,onChange:o().func.isRequired,disabled:o().bool,validation:o().shape({variant:o().string,message:o().node}),className:o().string,buttonProps:o().object},Pn.defaultProps={options:[],children:null,selectedLabel:"",label:"",labelProps:{},labelSuffix:null,disabled:!1,validation:{},className:"",buttonProps:{}},Pn.Option=kn,Pn.Option.displayName="Select.Option";const Ln=Pn,Fn=({as:e,className:t,children:n})=>l().createElement(e,{className:r()("yst-skeleton-loader",t)},n&&l().createElement("div",{className:"yst-pointer-events-none yst-invisible"},n));Fn.propTypes={as:o().elementType,className:o().string,children:o().node},Fn.defaultProps={as:"span",className:"",children:null};const In=Fn,Mn={variant:{striped:"even:yst-bg-slate-50 odd:yst-bg-white",plain:""}},Dn=({children:t,className:n="",...a})=>l().createElement("td",e({className:r()("yst-px-3 yst-py-4 yst-text-sm yst-text-slate-500",n)},a),t);Dn.propTypes={children:o().node.isRequired,variant:o().oneOf(Object.keys(Mn.variant)),className:o().string};const qn=({children:t,variant:n="plain",className:a="",...s})=>l().createElement("tr",e({className:r()(Mn.variant[n],a)},s),t);qn.propTypes={children:o().node.isRequired,variant:o().oneOf(Object.keys(Mn.variant)),className:o().string};const An=({children:t,className:n="",...a})=>l().createElement("th",e({className:r()("yst-px-3 yst-py-4 yst-text-left yst-text-sm yst-font-semibold yst-text-slate-900",n)},a),t);An.propTypes={children:o().node.isRequired,className:o().string};const jn=({children:t,className:n="",...a})=>l().createElement("thead",e({className:r()("yst-bg-slate-50",n)},a),t);jn.propTypes={children:o().node.isRequired,className:o().string};const Bn=({children:t,className:n="",...a})=>l().createElement("tbody",e({className:r()("yst-divide-y yst-divide-gray-200 yst-bg-white",n)},a),t);Bn.propTypes={children:o().node.isRequired,className:o().string};const Hn=(0,i.forwardRef)((({children:t,className:n="",...a},s)=>l().createElement("div",{className:"yst-overflow-hidden yst-shadow yst-ring-1 yst-ring-black yst-ring-opacity-5 yst-rounded-lg"},l().createElement("table",e({className:r()("yst-min-w-full yst-divide-y yst-divide-slate-300",n)},a,{ref:s}),t))));Hn.displayName="Table",Hn.propTypes={children:o().node.isRequired,className:o().string},Hn.defaultProps={className:""},Hn.Head=jn,Hn.Head.displayName="Table.Head",Hn.Body=Bn,Hn.Body.displayName="Table.Body",Hn.Header=An,Hn.Header.displayName="Table.Header",Hn.Row=qn,Hn.Row.displayName="Table.Row",Hn.Cell=Dn,Hn.Cell.displayName="Table.Cell";const zn=Hn,$n=i.forwardRef((function(e,t){return i.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true",ref:t},e),i.createElement("path",{fillRule:"evenodd",d:"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z",clipRule:"evenodd"}))})),Vn=({tag:t,index:n,disabled:a=!1,onRemoveTag:r,screenReaderRemoveTag:s,...o})=>{const c=(0,i.useCallback)((e=>{if(!a)switch(null==e?void 0:e.key){case"Delete":case"Backspace":return r(n),e.preventDefault(),!0}}),[n,a,r]),u=(0,i.useCallback)((e=>{if(!a)return r(n),e.preventDefault(),!0}),[n,a,r]);return l().createElement(It,e({onKeyDown:c},o,{variant:"plain",className:"yst-tag-input__tag"}),l().createElement("span",{className:"yst-mb-px"},t),l().createElement("button",{type:"button",onClick:u,className:"yst-tag-input__remove-tag"},l().createElement("span",{className:"yst-sr-only"},s),l().createElement($n,{className:"yst-h-3 yst-w-3"})))};Vn.propTypes={tag:o().string.isRequired,index:o().number.isRequired,disabled:o().bool,onRemoveTag:o().func.isRequired,screenReaderRemoveTag:o().string.isRequired};const Un=(0,i.forwardRef)((({tags:t=[],children:n,className:a,disabled:s,onAddTag:o,onRemoveTag:u,onSetTags:d,onBlur:p,screenReaderRemoveTag:m,...f},y)=>{const[b,v]=(0,i.useState)(""),g=(0,i.useCallback)((e=>{var t;(0,c.isString)(null==e||null===(t=e.target)||void 0===t?void 0:t.value)&&v(e.target.value)}),[v]),h=(0,i.useCallback)((e=>{switch(e.key){case",":case"Enter":return b.length>0&&(o(b),v("")),e.preventDefault(),!0;case"Backspace":if(0!==b.length||0===t.length)break;return u(t.length-1),e.ctrlKey&&d([]),e.preventDefault(),!0}}),[b,t,v,o]),N=(0,i.useCallback)((e=>{b.length>0&&(o(b),v("")),p(e)}),[b,o,v,p]);return l().createElement("div",{className:r()("yst-tag-input",s&&"yst-tag-input--disabled",a)},n||(0,c.map)(t,((e,t)=>l().createElement(Vn,{key:`tag-${t}`,tag:e,index:t,disabled:s,onRemoveTag:u,screenReaderRemoveTag:m}))),l().createElement("input",e({ref:y,type:"text",disabled:s,className:"yst-tag-input__input",onKeyDown:h},f,{onChange:g,onBlur:N,value:b})))}));Un.displayName="TagInput",Un.propTypes={tags:o().arrayOf(o().string),children:o().node,className:o().string,disabled:o().bool,onAddTag:o().func,onRemoveTag:o().func,onSetTags:o().func,onBlur:o().func,screenReaderRemoveTag:o().string},Un.defaultProps={tags:[],children:null,className:"",disabled:!1,onAddTag:c.noop,onRemoveTag:c.noop,onSetTags:c.noop,onBlur:c.noop,screenReaderRemoveTag:"Remove tag"},Un.Tag=Vn,Un.Tag.displayName="TagInput.Tag";const Wn=Un,Gn=(0,i.forwardRef)((({type:t,className:n,disabled:a,readOnly:s,...o},i)=>l().createElement("input",e({ref:i,type:t,className:r()("yst-text-input",a&&"yst-text-input--disabled",s&&"yst-text-input--read-only",n),disabled:a,readOnly:s},o))));Gn.displayName="TextInput",Gn.propTypes={type:o().string,className:o().string,disabled:o().bool,readOnly:o().bool},Gn.defaultProps={type:"text",className:"",disabled:!1,readOnly:!1};const Kn=Gn,Qn=(0,i.forwardRef)((({disabled:t,cols:n,rows:a,className:s,...o},i)=>l().createElement("textarea",e({ref:i,disabled:t,cols:n,rows:a,className:r()("yst-textarea",t&&"yst-textarea--disabled",s)},o))));Qn.displayName="Textarea",Qn.propTypes={className:o().string,disabled:o().bool,cols:o().number,rows:o().number},Qn.defaultProps={className:"",disabled:!1,cols:20,rows:2};const Yn=Qn,Zn={size:{1:"yst-title--1",2:"yst-title--2",3:"yst-title--3",4:"yst-title--4",5:"yst-title--5"}},Xn=(0,i.forwardRef)((({children:t,as:n,size:a,className:s,...o},i)=>l().createElement(n,e({ref:i,className:r()("yst-title",Zn.size[a||n[1]],s)},o),t)));Xn.displayName="Title",Xn.propTypes={children:o().node.isRequired,as:o().elementType,size:o().oneOf(Object.keys(Zn.size)),className:o().string},Xn.defaultProps={as:"h1",size:void 0,className:""};const Jn=Xn,ea=(0,i.createContext)({handleDismiss:c.noop}),ta={position:{"bottom-center":"yst-translate-y-full","bottom-left":"yst-translate-y-full","top-center":"yst--translate-y-full"}},na=({dismissScreenReaderLabel:e})=>{const{handleDismiss:t}=(0,i.useContext)(ea);return l().createElement("div",{className:"yst-flex-shrink-0 yst-flex"},l().createElement("button",{type:"button",onClick:t,className:"yst-bg-white yst-rounded-md yst-inline-flex yst-text-slate-400 hover:yst-text-slate-500 focus:yst-outline-none focus:yst-ring-2 focus:yst-ring-offset-2 focus:yst-ring-primary-500"},l().createElement("span",{className:"yst-sr-only"},e),l().createElement(gt,{className:"yst-h-5 yst-w-5"})))};na.propTypes={dismissScreenReaderLabel:o().string.isRequired};const aa=({description:e,className:t=""})=>(0,c.isArray)(e)?l().createElement("ul",{className:r()("yst-list-disc yst-ml-4",t)},e.map(((e,t)=>l().createElement("li",{className:"yst-pt-1",key:`${e}-${t}`},e)))):l().createElement("p",{className:t},e);aa.propTypes={description:o().oneOfType([o().node,o().arrayOf(o().node)]),className:o().string};const ra=({title:e,className:t=""})=>l().createElement("p",{className:r()("yst-text-sm yst-font-medium yst-text-slate-800",t)},e);ra.propTypes={title:o().string.isRequired,className:o().string};const sa=({children:e,id:t,className:n="",position:a="bottom-left",onDismiss:s=c.noop,autoDismiss:o=null,isVisible:u,setIsVisible:d})=>{const p=(0,i.useCallback)((()=>{d(!1),setTimeout((()=>{s(t)}),150)}),[s,t]);return(0,i.useEffect)((()=>{let e;return d(!0),o&&(e=setTimeout((()=>{p()}),o)),()=>clearTimeout(e)}),[]),l().createElement(ea.Provider,{value:{handleDismiss:p}},l().createElement(vt,{show:u,enter:"yst-transition yst-ease-in-out yst-duration-150",enterFrom:r()("yst-opacity-0",ta.position[a]),enterTo:"yst-translate-y-0",leave:"yst-transition yst-ease-in-out yst-duration-150",leaveFrom:"yst-translate-y-0",leaveTo:r()("yst-opacity-0",ta.position[a]),className:r()("yst-toast",n),role:"alert"},e))};sa.propTypes={children:o().node,id:o().string.isRequired,className:o().string,position:o().string,onDismiss:o().func,autoDismiss:o().oneOfType([o().number,null]),isVisible:o().bool.isRequired,setIsVisible:o().func.isRequired},sa.Close=na,sa.Description=aa,sa.Title=ra;const oa=sa;let ia=(0,i.createContext)(null);function la(){let e=(0,i.useContext)(ia);if(null===e){let e=new Error("You used a <Label /> component, but it is not inside a relevant parent.");throw Error.captureStackTrace&&Error.captureStackTrace(e,la),e}return e}let ca=ve((function(e,t){let n=B(),{id:a=`headlessui-label-${n}`,passive:r=!1,...s}=e,o=la(),i=ie(t);_((()=>o.register(a)),[a,o.register]);let l={ref:i,...o.props,id:a};return r&&("onClick"in l&&delete l.onClick,"onClick"in s&&delete s.onClick),fe({ourProps:l,theirProps:s,slot:o.slot||{},defaultTag:"label",name:o.name||"Label"})})),ua=(0,i.createContext)(null);function da(){let e=(0,i.useContext)(ua);if(null===e){let e=new Error("You used a <Description /> component, but it is not inside a relevant parent.");throw Error.captureStackTrace&&Error.captureStackTrace(e,da),e}return e}function pa(){let[e,t]=(0,i.useState)([]);return[e.length>0?e.join(" "):void 0,(0,i.useMemo)((()=>function(e){let n=q((e=>(t((t=>[...t,e])),()=>t((t=>{let n=t.slice(),a=n.indexOf(e);return-1!==a&&n.splice(a,1),n}))))),a=(0,i.useMemo)((()=>({register:n,slot:e.slot,name:e.name,props:e.props})),[n,e.slot,e.name,e.props]);return i.createElement(ua.Provider,{value:a},e.children)}),[t])]}let ma=ve((function(e,t){let n=B(),{id:a=`headlessui-description-${n}`,...r}=e,s=da(),o=ie(t);return _((()=>s.register(a)),[a,s.register]),fe({ourProps:{ref:o,...s.props,id:a},theirProps:r,slot:s.slot||{},defaultTag:"p",name:s.name||"Description"})})),fa=(0,i.createContext)(null);fa.displayName="GroupContext";let ya=i.Fragment,ba=ve((function(e,t){let n=B(),{id:a=`headlessui-switch-${n}`,checked:r,defaultChecked:s=!1,onChange:o,name:l,value:c,...u}=e,d=(0,i.useContext)(fa),p=(0,i.useRef)(null),m=ie(p,t,null===d?null:d.setSwitch),[f,y]=_e(r,o,s),b=q((()=>null==y?void 0:y(!f))),v=q((e=>{if(Ne(e.currentTarget))return e.preventDefault();e.preventDefault(),b()})),g=q((e=>{e.key===Pe.Space?(e.preventDefault(),b()):e.key===Pe.Enter&&function(e){var t;let n=null!=(t=null==e?void 0:e.form)?t:e.closest("form");if(n)for(let e of n.elements)if("INPUT"===e.tagName&&"submit"===e.type||"BUTTON"===e.tagName&&"submit"===e.type||"INPUT"===e.nodeName&&"image"===e.type)return void e.click()}(e.currentTarget)})),h=q((e=>e.preventDefault())),N=(0,i.useMemo)((()=>({checked:f})),[f]),E={id:a,ref:m,role:"switch",type:re(e,p),tabIndex:0,"aria-checked":f,"aria-labelledby":null==d?void 0:d.labelledby,"aria-describedby":null==d?void 0:d.describedby,onClick:v,onKeyUp:g,onKeyPress:h},x=D();return(0,i.useEffect)((()=>{var e;let t=null==(e=p.current)?void 0:e.closest("form");!t||void 0!==s&&x.addEventListener(t,"reset",(()=>{y(s)}))}),[p,y]),i.createElement(i.Fragment,null,null!=l&&f&&i.createElement(Te,{features:we.Hidden,...ge({as:"input",type:"checkbox",hidden:!0,readOnly:!0,checked:f,name:l,value:c})}),fe({ourProps:E,theirProps:u,slot:N,defaultTag:"button",name:"Switch"}))})),va=Object.assign(ba,{Group:function(e){let[t,n]=(0,i.useState)(null),[a,r]=function(){let[e,t]=(0,i.useState)([]);return[e.length>0?e.join(" "):void 0,(0,i.useMemo)((()=>function(e){let n=q((e=>(t((t=>[...t,e])),()=>t((t=>{let n=t.slice(),a=n.indexOf(e);return-1!==a&&n.splice(a,1),n}))))),a=(0,i.useMemo)((()=>({register:n,slot:e.slot,name:e.name,props:e.props})),[n,e.slot,e.name,e.props]);return i.createElement(ia.Provider,{value:a},e.children)}),[t])]}(),[s,o]=pa(),l=(0,i.useMemo)((()=>({switch:t,setSwitch:n,labelledby:a,describedby:s})),[t,n,a,s]),c=e;return i.createElement(o,{name:"Switch.Description"},i.createElement(r,{name:"Switch.Label",props:{onClick(){!t||(t.click(),t.focus({preventScroll:!0}))}}},i.createElement(fa.Provider,{value:l},fe({ourProps:{},theirProps:c,defaultTag:ya,name:"Switch.Group"}))))},Label:ca,Description:ma});const ga=(0,i.forwardRef)((({id:t,as:n,checked:a,screenReaderLabel:s,onChange:o,disabled:i,className:d,type:p,...m},f)=>{const y=u();return l().createElement(va,e({ref:f,as:n,checked:a,disabled:i,onChange:i?c.noop:o,className:r()("yst-toggle",a&&"yst-toggle--checked",i&&"yst-toggle--disabled",d),"data-id":t},m,{type:"button"===n?"button":p}),l().createElement("span",{className:"yst-sr-only"},s),l().createElement("span",{className:"yst-toggle__handle"},l().createElement(vt,{show:a,unmount:!1,as:"span","aria-hidden":!a,enter:"",enterFrom:"yst-opacity-0 yst-hidden",enterTo:"yst-opacity-100",leaveFrom:"yst-opacity-100",leaveTo:"yst-opacity-0 yst-hidden"},l().createElement(ht,e({className:"yst-toggle__icon yst-toggle__icon--check"},y))),l().createElement(vt,{show:!a,unmount:!1,as:"span","aria-hidden":a,enterFrom:"yst-opacity-0 yst-hidden",enterTo:"yst-opacity-100",leaveFrom:"yst-opacity-100",leaveTo:"yst-opacity-0 yst-hidden"},l().createElement($n,e({className:"yst-toggle__icon yst-toggle__icon--x"},y)))))}));ga.displayName="Toggle",ga.propTypes={as:o().elementType,id:o().string.isRequired,checked:o().bool,screenReaderLabel:o().string.isRequired,onChange:o().func.isRequired,disabled:o().bool,type:o().string,className:o().string},ga.defaultProps={as:"button",checked:!1,disabled:!1,type:"",className:""};const ha=ga,Na={top:"yst-tooltip--top",right:"yst-tooltip--right",bottom:"yst-tooltip--bottom",left:"yst-tooltip--left"},Ea=(0,i.forwardRef)((({children:t,as:n,className:a,position:s,...o},i)=>l().createElement(n,e({ref:i,className:r()("yst-tooltip",Na[s],a),role:"tooltip"},o),t)));Ea.displayName="Tooltip",Ea.propTypes={as:o().elementType,children:o().node,className:o().string,position:o().oneOf(Object.keys(Na))},Ea.defaultProps={as:"div",children:null,className:"",position:"top"};const xa=Ea,Ra=(e,t)=>{const n=(0,i.useMemo)((()=>(0,c.reduce)(t,((t,n,a)=>n?(t[a]=`${e}__${a}`,t):t),{})),[e,t]),a=(0,i.useMemo)((()=>(0,c.values)(n).join(" ")||null),[n]);return{ids:n,describedBy:a}},wa=(0,i.forwardRef)((({id:t,label:n,description:a,validation:s,className:o,...i},c)=>{const{ids:u,describedBy:d}=Ra(t,{validation:null==s?void 0:s.message,description:a});return l().createElement("div",{className:r()("yst-autocomplete-field",o)},l().createElement(Pt,e({ref:c,id:t,label:n,labelProps:{as:"label",className:"yst-label yst-autocomplete-field__label"},validation:s,className:"yst-autocomplete-field__select",buttonProps:{"aria-describedby":d}},i)),(null==s?void 0:s.message)&&l().createElement(x,{variant:null==s?void 0:s.variant,id:u.validation,className:"yst-autocomplete-field__validation"},s.message),a&&l().createElement("div",{id:u.description,className:"yst-autocomplete-field__description"},a))}));wa.displayName="AutocompleteField",wa.propTypes={id:o().string.isRequired,label:o().string.isRequired,description:o().node,validation:o().shape({variant:o().string,message:o().node}),className:o().string},wa.defaultProps={description:null,validation:{},className:""},wa.Option=Pt.Option,wa.Option.displayName="AutocompleteField.Option";const Ta=wa,Oa=({as:t="div",children:n,className:a="",...s})=>l().createElement(t,e({},s,{className:r()("yst-card__header",a)}),n);Oa.propTypes={as:s.PropTypes.element,children:s.PropTypes.node.isRequired,className:s.PropTypes.string};const Ca=({as:t="div",children:n,className:a="",...s})=>l().createElement(t,e({},s,{className:r()("yst-card__content",a)}),n);Ca.propTypes={as:s.PropTypes.element,children:s.PropTypes.node.isRequired,className:s.PropTypes.string};const Sa=({as:t="div",children:n,className:a="",...s})=>l().createElement(t,e({},s,{className:r()("yst-card__footer",a)}),n);Sa.propTypes={as:s.PropTypes.element,children:s.PropTypes.node.isRequired,className:s.PropTypes.string};const ka=(0,i.forwardRef)((({as:t,children:n,className:a,...s},o)=>l().createElement(t,e({},s,{className:r()("yst-card",a),ref:o}),n)));ka.displayName="Card",ka.propTypes={as:s.PropTypes.elementType,children:s.PropTypes.node.isRequired,className:s.PropTypes.string},ka.defaultProps={as:"div",className:""},ka.Header=Oa,ka.Header.displayName="Card.Header",ka.Content=Ca,ka.Content.displayName="Card.Content",ka.Footer=Sa,ka.Footer.displayName="Card.Footer";const Pa=ka,_a=({children:t=null,id:n="",name:a="",values:s=[],label:o="",description:u="",disabled:d=!1,options:p,onChange:m=c.noop,className:f="",...y})=>{const b=(0,i.useCallback)((({target:e})=>{if(e.checked&&!(0,c.includes)(s,e.value))return m([...s,e.value]);m((0,c.without)(s,e.value))}),[s,m]);return l().createElement("fieldset",{id:`checkbox-group-${n}`,className:r()("yst-checkbox-group",d&&"yst-checkbox-group--disabled",f)},l().createElement(zt,{as:"legend",className:"yst-checkbox-group__label",label:o}),u&&l().createElement("div",{className:"yst-checkbox-group__description"},u),l().createElement("div",{className:"yst-checkbox-group__options"},t||p.map(((t,r)=>{const o=`checkbox-${n}-${r}`;return l().createElement(Vt,e({key:o,id:o,name:a,value:t.value,label:t.label,checked:(0,c.includes)(s,t.value),disabled:d,onChange:b},y))}))))};_a.propTypes={children:o().node,id:o().string,name:o().string,values:o().arrayOf(o().string),label:o().string,disabled:o().bool,description:o().string,options:o().arrayOf(o().shape({value:o().string.isRequired,label:o().string.isRequired})),onChange:o().func,className:o().string},(_a.Checkbox=Vt).displayName="CheckboxGroup.Checkbox";const La=_a,Fa=window.yoast.reduxJsToolkit;function Ia(e){return"string"==typeof e&&"%"===e[e.length-1]&&function(e){const t=parseFloat(e);return!isNaN(t)&&isFinite(t)}(e.substring(0,e.length-1))}function Ma(e,t){0===t&&(null==e?void 0:e.style)&&(e.style.display="none")}const Da={animating:"rah-animating",animatingUp:"rah-animating--up",animatingDown:"rah-animating--down",animatingToHeightZero:"rah-animating--to-height-zero",animatingToHeightAuto:"rah-animating--to-height-auto",animatingToHeightSpecific:"rah-animating--to-height-specific",static:"rah-static",staticHeightZero:"rah-static--height-zero",staticHeightAuto:"rah-static--height-auto",staticHeightSpecific:"rah-static--height-specific"};function qa(e,t){return[e.static,0===t&&e.staticHeightZero,t>0&&e.staticHeightSpecific,"auto"===t&&e.staticHeightAuto].filter((e=>e)).join(" ")}const Aa=e=>{var{animateOpacity:t=!1,animationStateClasses:n={},applyInlineTransitions:a=!0,children:r,className:s="",contentClassName:o,delay:l=0,duration:c=500,easing:u="ease",height:d,onHeightAnimationEnd:p,onHeightAnimationStart:m,style:f}=e,y=function(e,t){var n={};for(var a in e)Object.prototype.hasOwnProperty.call(e,a)&&t.indexOf(a)<0&&(n[a]=e[a]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var r=0;for(a=Object.getOwnPropertySymbols(e);r<a.length;r++)t.indexOf(a[r])<0&&Object.prototype.propertyIsEnumerable.call(e,a[r])&&(n[a[r]]=e[a[r]])}return n}(e,["animateOpacity","animationStateClasses","applyInlineTransitions","children","className","contentClassName","delay","duration","easing","height","onHeightAnimationEnd","onHeightAnimationStart","style"]);const b=(0,i.useRef)(d),v=(0,i.useRef)(null),g=(0,i.useRef)(),h=(0,i.useRef)(),N=(0,i.useRef)(Object.assign(Object.assign({},Da),n)),E="undefined"!=typeof window,x=(0,i.useRef)(!(!E||!window.matchMedia)&&window.matchMedia("(prefers-reduced-motion)").matches),R=x.current?0:l,w=x.current?0:c;let T=d,O="visible";"number"==typeof T?(T=d<0?0:d,O="hidden"):Ia(T)&&(T="0%"===d?0:d,O="hidden");const[C,S]=(0,i.useState)(T),[k,P]=(0,i.useState)(O),[_,L]=(0,i.useState)(!1),[F,I]=(0,i.useState)(qa(N.current,d));(0,i.useEffect)((()=>{Ma(v.current,C)}),[]),(0,i.useEffect)((()=>{if(d!==b.current&&v.current){!function(e,t){0===t&&(null==e?void 0:e.style)&&(e.style.display="")}(v.current,b.current),v.current.style.overflow="hidden";const e=v.current.offsetHeight;v.current.style.overflow="";const t=w+R;let n,a,r,s="hidden";const o="auto"===b.current;"number"==typeof d?(n=d<0?0:d,a=n):Ia(d)?(n="0%"===d?0:d,a=n):(n=e,a="auto",s=void 0),o&&(a=n,n=e);const i=[N.current.animating,("auto"===b.current||d<b.current)&&N.current.animatingUp,("auto"===d||d>b.current)&&N.current.animatingDown,0===a&&N.current.animatingToHeightZero,"auto"===a&&N.current.animatingToHeightAuto,a>0&&N.current.animatingToHeightSpecific].filter((e=>e)).join(" "),l=qa(N.current,a);S(n),P("hidden"),L(!o),I(i),clearTimeout(h.current),clearTimeout(g.current),o?(r=!0,h.current=setTimeout((()=>{S(a),P(s),L(r),null==m||m(a)}),50),g.current=setTimeout((()=>{L(!1),I(l),Ma(v.current,a),null==p||p(a)}),t)):(null==m||m(n),h.current=setTimeout((()=>{S(a),P(s),L(!1),I(l),"auto"!==d&&Ma(v.current,n),null==p||p(n)}),t))}return b.current=d,()=>{clearTimeout(h.current),clearTimeout(g.current)}}),[d]);const M=Object.assign(Object.assign({},f),{height:C,overflow:k||(null==f?void 0:f.overflow)});_&&a&&(M.transition=`height ${w}ms ${u} ${R}ms`,(null==f?void 0:f.transition)&&(M.transition=`${f.transition}, ${M.transition}`),M.WebkitTransition=M.transition);const D={};t&&(D.transition=`opacity ${w}ms ${u} ${R}ms`,D.WebkitTransition=D.transition,0===C&&(D.opacity=0));const q=void 0!==y["aria-hidden"]?y["aria-hidden"]:0===d;return i.createElement("div",Object.assign({},y,{"aria-hidden":q,className:`${F} ${s}`,style:M}),i.createElement("div",{className:o,style:D,ref:v},r))},ja=(e=!0)=>{const[t,n]=(0,i.useState)(e),a=(0,i.useCallback)((()=>n(!t)),[t,n]),r=(0,i.useCallback)((()=>n(!0)),[n]),s=(0,i.useCallback)((()=>n(!1)),[n]);return[t,a,n,r,s]},Ba=({limit:e,children:t,renderButton:n,initialShow:a=!1,id:r=""})=>{const[s,o]=ja(a),u=(0,i.useMemo)((()=>(0,c.flatten)(t)),[t]),d=(0,i.useMemo)((()=>(0,c.slice)(u,0,e)),[u]),p=(0,i.useMemo)((()=>(0,c.slice)(u,e)),[u]),m=(0,i.useMemo)((()=>r||`yst-animate-height-${(0,Fa.nanoid)()}`),[r]),f=(0,i.useMemo)((()=>({"aria-expanded":s,"aria-controls":m})),[s,m]);return e<0||u.length<=e?t:l().createElement(l().Fragment,null,d,l().createElement(Aa,{id:m,easing:"ease-in-out",duration:300,height:s?"auto":0,animateOpacity:!0},p),n({show:s,toggle:o,ariaProps:f}))};Ba.propTypes={limit:o().number.isRequired,children:o().arrayOf(o().node).isRequired,renderButton:o().func.isRequired,initialShow:o().bool,id:o().string};const Ha=Ba,za=i.forwardRef((function(e,t){return i.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:2,stroke:"currentColor","aria-hidden":"true",ref:t},e),i.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M8 11V7a4 4 0 118 0m-4 8v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2z"}))})),$a={variant:{default:"yst-feature-upsell--default",card:"yst-feature-upsell--card"}},Va=({children:t,shouldUpsell:n=!0,className:a="",variant:s="card",cardLink:o="",cardText:i="",...c})=>{const d=u();return n?l().createElement("div",{className:r()("yst-feature-upsell",$a.variant[s],a)},l().createElement("div",{className:"yst-space-y-8 yst-grayscale"},t),l().createElement("div",{className:"yst-absolute yst-inset-0 yst-ring-1 yst-ring-black yst-ring-opacity-5 yst-shadow-lg yst-rounded-md"}),l().createElement("div",{className:"yst-absolute yst-inset-0 yst-flex yst-items-center yst-justify-center"},l().createElement(Bt,e({as:"a",className:"yst-gap-2 yst-shadow-lg yst-shadow-amber-700/30",variant:"upsell",href:o,target:"_blank",rel:"noopener"},c),l().createElement(za,e({className:"yst-w-5 yst-h-5 yst--ml-1 yst-shrink-0"},d)),i))):t};Va.propTypes={children:o().node.isRequired,shouldUpsell:o().bool,className:o().string,variant:o().oneOf(Object.keys($a.variant)),cardLink:o().string,cardText:o().string};const Ua=Va,Wa=i.forwardRef((function(e,t){return i.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:2,stroke:"currentColor","aria-hidden":"true",ref:t},e),i.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"}))})),Ga=i.forwardRef((function(e,t){return i.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:2,stroke:"currentColor","aria-hidden":"true",ref:t},e),i.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M9 13h6m-3-3v6m5 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"}))})),Ka=(0,i.forwardRef)((({id:t,name:n,value:a,selectLabel:s,dropLabel:o,screenReaderLabel:u,selectDescription:d,disabled:p,iconAs:m,onChange:f,onDrop:y,className:b,...v},g)=>{const[h,N]=(0,i.useState)(!1),E=(0,i.useCallback)((e=>{e.preventDefault(),(0,c.isEmpty)(e.dataTransfer.items)||N(!0)}),[N]),x=(0,i.useCallback)((e=>{e.preventDefault(),N(!1)}),[N]),R=(0,i.useCallback)((e=>{e.preventDefault()}),[]),w=(0,i.useCallback)((e=>{e.preventDefault(),N(!1),y(e)}),[N,y]);return l().createElement("div",{onDragEnter:E,onDragLeave:x,onDragOver:R,onDrop:w,className:r()("yst-file-input",{"yst-is-drag-over":h,"yst-is-disabled":p,className:b})},l().createElement("div",{className:"yst-file-input__content"},l().createElement(m,{className:"yst-file-input__icon"}),l().createElement("div",{className:"yst-file-input__labels"},l().createElement("input",e({ref:g,type:"file",id:t,name:n,value:a,onChange:f,className:"yst-file-input__input","aria-labelledby":u,disabled:p},v)),l().createElement(Zt,{as:"label",htmlFor:t,className:"yst-file-input__select-label"},s),l().createElement("span",null," "),o),d&&l().createElement("span",null,d)))}));Ka.displayName="FileInput",Ka.propTypes={id:o().string.isRequired,name:o().string.isRequired,value:o().string.isRequired,selectLabel:o().string.isRequired,dropLabel:o().string.isRequired,screenReaderLabel:o().string.isRequired,selectDescription:o().string,disabled:o().bool,iconAs:o().elementType,onChange:o().func.isRequired,onDrop:o().func,className:o().string},Ka.defaultProps={selectDescription:"",disabled:!1,iconAs:Ga,className:"",onDrop:c.noop};const Qa=Ka,Ya={idle:"idle",selected:"selected",loading:"loading",success:"success",aborted:"aborted",error:"error"},Za=(0,i.createContext)({status:Ya.idle}),Xa={enter:"yst-transition-opacity yst-ease-in-out yst-duration-1000 yst-delay-200",enterFrom:"yst-opacity-0",enterTo:"yst-opacity-100",leave:"yst-transition-opacity yst-ease-in-out yst-duration-200",leaveFrom:"yst-opacity-0",leaveTo:"yst-opacity-100",className:"yst-absolute"},Ja=e=>{const t=({children:t})=>{const{status:n}=(0,i.useContext)(Za);return l().createElement(vt,{show:n===e,enter:"yst-transition-opacity yst-ease-in-out yst-duration-1000 yst-delay-200",enterFrom:"yst-opacity-0",enterTo:"yst-opacity-100",className:"yst-mt-6"},t)};return t.propTypes={children:o().node},t.displayName=`FileImport.${(0,c.capitalize)(e)}`,t},er=(0,i.forwardRef)((({children:t="",id:n,name:a,selectLabel:r,dropLabel:s,screenReaderLabel:o,abortScreenReaderLabel:u,selectDescription:d,status:p,onChange:m,onAbort:f,feedbackTitle:y,feedbackDescription:b,progressMin:v,progressMax:g,progress:N},E)=>{const x=(0,i.useMemo)((()=>p===Ya.selected),[p]),R=(0,i.useMemo)((()=>p===Ya.loading),[p]),w=(0,i.useMemo)((()=>p===Ya.success),[p]),T=(0,i.useMemo)((()=>p===Ya.aborted),[p]),O=(0,i.useMemo)((()=>p===Ya.error),[p]),C=(0,i.useMemo)((()=>(0,c.includes)([Ya.selected,Ya.loading,Ya.success,Ya.aborted,Ya.error],p)),[p]),S=(0,i.useCallback)((e=>{(0,c.isEmpty)(e.target.files)||m(e.target.files[0])}),[m]),k=(0,i.useCallback)((e=>{if(!(0,c.isEmpty)(e.dataTransfer.files)){const t=e.dataTransfer.files[0];t&&m(t)}}),[m]);return l().createElement(Za.Provider,{value:{status:p}},l().createElement("div",{className:"yst-file-import"},l().createElement(Qa,{ref:E,id:n,name:a,value:"",onChange:S,onDrop:k,className:"yst-file-import__input","aria-labelledby":o,disabled:R,selectLabel:r,dropLabel:s,screenReaderLabel:o,selectDescription:d}),l().createElement(vt,{show:C,enter:"yst-transition-opacity yst-ease-in-out yst-duration-1000 yst-delay-200",enterFrom:"yst-opacity-0",enterTo:"yst-opacity-100"},l().createElement("div",{className:"yst-file-import__feedback"},l().createElement("header",{className:"yst-file-import__feedback-header"},l().createElement("div",{className:"yst-file-import__feedback-figure"},l().createElement(Wa,null)),l().createElement("div",{className:"yst-flex-1"},l().createElement("span",{className:"yst-file-import__feedback-title"},y),l().createElement("p",{className:"yst-file-import__feedback-description"},b),!(0,c.isNull)(N)&&l().createElement(sn,{min:v,max:g,progress:N,className:"yst-mt-1.5"})),l().createElement("div",{className:"yst-relative yst-h-5 yst-w-5"},l().createElement(vt,e({show:x},Xa),l().createElement(h,{variant:"info",className:"yst-w-5 yst-h-5"})),l().createElement(vt,e({show:R},Xa),l().createElement("button",{type:"button",onClick:f,className:"yst-file-import__abort-button"},l().createElement("span",{className:"yst-sr-only"},u),l().createElement(gt,null))),l().createElement(vt,e({show:w},Xa),l().createElement(h,{variant:"success",className:"yst-w-5 yst-h-5"})),l().createElement(vt,e({show:T},Xa),l().createElement(h,{variant:"warning",className:"yst-w-5 yst-h-5"})),l().createElement(vt,e({show:O},Xa),l().createElement(h,{variant:"error",className:"yst-w-5 yst-h-5"})))),t))))}));er.displayName="FileImport",er.propTypes={children:o().node,id:o().string.isRequired,name:o().string.isRequired,selectLabel:o().string.isRequired,dropLabel:o().string.isRequired,screenReaderLabel:o().string.isRequired,abortScreenReaderLabel:o().string.isRequired,selectDescription:o().string,feedbackTitle:o().string.isRequired,feedbackDescription:o().string,progressMin:o().number,progressMax:o().number,progress:o().number,status:o().oneOf((0,c.values)(Ya)),onChange:o().func.isRequired,onAbort:o().func.isRequired},er.defaultProps={children:null,selectDescription:"",feedbackDescription:"",progressMin:null,progressMax:null,progress:null,status:Ya.idle},er.Selected=Ja(Ya.selected),er.Loading=Ja(Ya.loading),er.Success=Ja(Ya.success),er.Aborted=Ja(Ya.aborted),er.Error=Ja(Ya.error);const tr=er;var nr=(e=>(e[e.Forwards=0]="Forwards",e[e.Backwards=1]="Backwards",e))(nr||{});function ar(...e){return(0,i.useMemo)((()=>z(...e)),[...e])}function rr(e,t,n,a){let r=L(n);(0,i.useEffect)((()=>{function n(e){r.current(e)}return(e=null!=e?e:window).addEventListener(t,n,a),()=>e.removeEventListener(t,n,a)}),[e,t,a])}var sr=(e=>(e[e.None=1]="None",e[e.InitialFocus=2]="InitialFocus",e[e.TabLock=4]="TabLock",e[e.FocusLock=8]="FocusLock",e[e.RestoreFocus=16]="RestoreFocus",e[e.All=30]="All",e))(sr||{});let or=Object.assign(ve((function(e,t){let n=(0,i.useRef)(null),a=ie(n,t),{initialFocus:r,containers:s,features:o=30,...l}=e;A()||(o=1);let c=ar(n);!function({ownerDocument:e},t){let n=(0,i.useRef)(null);rr(null==e?void 0:e.defaultView,"focusout",(e=>{!t||n.current||(n.current=e.target)}),!0),Le((()=>{t||((null==e?void 0:e.activeElement)===(null==e?void 0:e.body)&&Z(n.current),n.current=null)}),[t]);let a=(0,i.useRef)(!1);(0,i.useEffect)((()=>(a.current=!1,()=>{a.current=!0,I((()=>{!a.current||(Z(n.current),n.current=null)}))})),[])}({ownerDocument:c},Boolean(16&o));let u=function({ownerDocument:e,container:t,initialFocus:n},a){let r=(0,i.useRef)(null),s=tt();return Le((()=>{if(!a)return;let o=t.current;!o||I((()=>{if(!s.current)return;let t=null==e?void 0:e.activeElement;if(null!=n&&n.current){if((null==n?void 0:n.current)===t)return void(r.current=t)}else if(o.contains(t))return void(r.current=t);null!=n&&n.current?Z(n.current):ee(o,W.First)===G.Error&&console.warn("There are no focusable elements inside the <FocusTrap />"),r.current=null==e?void 0:e.activeElement}))}),[a]),r}({ownerDocument:c,container:n,initialFocus:r},Boolean(2&o));!function({ownerDocument:e,container:t,containers:n,previousActiveElement:a},r){let s=tt();rr(null==e?void 0:e.defaultView,"focus",(e=>{if(!r||!s.current)return;let o=new Set(null==n?void 0:n.current);o.add(t);let i=a.current;if(!i)return;let l=e.target;l&&l instanceof HTMLElement?ir(o,l)?(a.current=l,Z(l)):(e.preventDefault(),e.stopPropagation(),Z(i)):Z(a.current)}),!0)}({ownerDocument:c,container:n,containers:s,previousActiveElement:u},Boolean(8&o));let d=function(){let e=(0,i.useRef)(0);return function(e,t,n){let a=L(t);(0,i.useEffect)((()=>{function t(e){a.current(e)}return window.addEventListener(e,t,n),()=>window.removeEventListener(e,t,n)}),[e,n])}("keydown",(t=>{"Tab"===t.key&&(e.current=t.shiftKey?1:0)}),!0),e}(),p=q((e=>{let t=n.current;t&&H(d.current,{[nr.Forwards]:()=>{ee(t,W.First,{skipElements:[e.relatedTarget]})},[nr.Backwards]:()=>{ee(t,W.Last,{skipElements:[e.relatedTarget]})}})})),m=D(),f=(0,i.useRef)(!1),y={ref:a,onKeyDown(e){"Tab"==e.key&&(f.current=!0,m.requestAnimationFrame((()=>{f.current=!1})))},onBlur(e){let t=new Set(null==s?void 0:s.current);t.add(n);let a=e.relatedTarget;a instanceof HTMLElement&&"true"!==a.dataset.headlessuiFocusGuard&&(ir(t,a)||(f.current?ee(n.current,H(d.current,{[nr.Forwards]:()=>W.Next,[nr.Backwards]:()=>W.Previous})|W.WrapAround,{relativeTo:e.target}):e.target instanceof HTMLElement&&Z(e.target)))}};return i.createElement(i.Fragment,null,Boolean(4&o)&&i.createElement(Te,{as:"button",type:"button","data-headlessui-focus-guard":!0,onFocus:p,features:we.Focusable}),fe({ourProps:y,theirProps:l,defaultTag:"div",name:"FocusTrap"}),Boolean(4&o)&&i.createElement(Te,{as:"button",type:"button","data-headlessui-focus-guard":!0,onFocus:p,features:we.Focusable}))})),{features:sr});function ir(e,t){var n;for(let a of e)if(null!=(n=a.current)&&n.contains(t))return!0;return!1}let lr=new Set,cr=new Map;function ur(e){e.setAttribute("aria-hidden","true"),e.inert=!0}function dr(e){let t=cr.get(e);!t||(null===t["aria-hidden"]?e.removeAttribute("aria-hidden"):e.setAttribute("aria-hidden",t["aria-hidden"]),e.inert=t.inert)}const pr=window.ReactDOM;let mr=(0,i.createContext)(!1);function fr(){return(0,i.useContext)(mr)}function yr(e){return i.createElement(mr.Provider,{value:e.force},e.children)}let br=i.Fragment,vr=ve((function(e,t){let n=e,a=(0,i.useRef)(null),r=ie(oe((e=>{a.current=e})),t),s=ar(a),o=function(e){let t=fr(),n=(0,i.useContext)(hr),a=ar(e),[r,s]=(0,i.useState)((()=>{if(!t&&null!==n||P.isServer)return null;let e=null==a?void 0:a.getElementById("headlessui-portal-root");if(e)return e;if(null===a)return null;let r=a.createElement("div");return r.setAttribute("id","headlessui-portal-root"),a.body.appendChild(r)}));return(0,i.useEffect)((()=>{null!==r&&(null!=a&&a.body.contains(r)||null==a||a.body.appendChild(r))}),[r,a]),(0,i.useEffect)((()=>{t||null!==n&&s(n.current)}),[n,s,t]),r}(a),[l]=(0,i.useState)((()=>{var e;return P.isServer?null:null!=(e=null==s?void 0:s.createElement("div"))?e:null})),c=A(),u=(0,i.useRef)(!1);return _((()=>{if(u.current=!1,o&&l)return o.contains(l)||(l.setAttribute("data-headlessui-portal",""),o.appendChild(l)),()=>{u.current=!0,I((()=>{var e;!u.current||!o||!l||(l instanceof Node&&o.contains(l)&&o.removeChild(l),o.childNodes.length<=0&&(null==(e=o.parentElement)||e.removeChild(o)))}))}}),[o,l]),c&&o&&l?(0,pr.createPortal)(fe({ourProps:{ref:r},theirProps:n,defaultTag:br,name:"Portal"}),l):null})),gr=i.Fragment,hr=(0,i.createContext)(null),Nr=ve((function(e,t){let{target:n,...a}=e,r={ref:ie(t)};return i.createElement(hr.Provider,{value:n},fe({ourProps:r,theirProps:a,defaultTag:gr,name:"Popover.Group"}))})),Er=Object.assign(vr,{Group:Nr}),xr=(0,i.createContext)((()=>{}));xr.displayName="StackContext";var Rr=(e=>(e[e.Add=0]="Add",e[e.Remove=1]="Remove",e))(Rr||{});function wr({children:e,onUpdate:t,type:n,element:a,enabled:r}){let s=(0,i.useContext)(xr),o=q(((...e)=>{null==t||t(...e),s(...e)}));return _((()=>{let e=void 0===r||!0===r;return e&&o(0,n,a),()=>{e&&o(1,n,a)}}),[o,n,a,r]),i.createElement(xr.Provider,{value:o},e)}var Tr=(e=>(e[e.Open=0]="Open",e[e.Closed=1]="Closed",e))(Tr||{}),Or=(e=>(e[e.SetTitleId=0]="SetTitleId",e))(Or||{});let Cr={0:(e,t)=>e.titleId===t.id?e:{...e,titleId:t.id}},Sr=(0,i.createContext)(null);function kr(e){let t=(0,i.useContext)(Sr);if(null===t){let t=new Error(`<${e} /> is missing a parent <Dialog /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(t,kr),t}return t}function Pr(e,t){return H(t.type,Cr,e,t)}Sr.displayName="DialogContext";let _r=pe.RenderStrategy|pe.Static,Lr=ve((function(e,t){let n=B(),{id:a=`headlessui-dialog-${n}`,open:r,onClose:s,initialFocus:o,__demoMode:l=!1,...c}=e,[u,d]=(0,i.useState)(0),p=Se();void 0===r&&null!==p&&(r=H(p,{[Ce.Open]:!0,[Ce.Closed]:!1}));let m=(0,i.useRef)(new Set),f=(0,i.useRef)(null),y=ie(f,t),b=(0,i.useRef)(null),v=ar(f),g=e.hasOwnProperty("open")||null!==p,h=e.hasOwnProperty("onClose");if(!g&&!h)throw new Error("You have to provide an `open` and an `onClose` prop to the `Dialog` component.");if(!g)throw new Error("You provided an `onClose` prop to the `Dialog`, but forgot an `open` prop.");if(!h)throw new Error("You provided an `open` prop to the `Dialog`, but forgot an `onClose` prop.");if("boolean"!=typeof r)throw new Error(`You provided an \`open\` prop to the \`Dialog\`, but the value is not a boolean. Received: ${r}`);if("function"!=typeof s)throw new Error(`You provided an \`onClose\` prop to the \`Dialog\`, but the value is not a function. Received: ${s}`);let N=r?0:1,[E,x]=(0,i.useReducer)(Pr,{titleId:null,descriptionId:null,panelRef:(0,i.createRef)()}),R=q((()=>s(!1))),w=q((e=>x({type:0,id:e}))),T=!!A()&&!l&&0===N,O=u>1,C=null!==(0,i.useContext)(Sr),S=O?"parent":"leaf";!function(e,t=!0){_((()=>{if(!t||!e.current)return;let n=e.current,a=z(n);if(a){lr.add(n);for(let e of cr.keys())e.contains(n)&&(dr(e),cr.delete(e));return a.querySelectorAll("body > *").forEach((e=>{if(e instanceof HTMLElement){for(let t of lr)if(e.contains(t))return;1===lr.size&&(cr.set(e,{"aria-hidden":e.getAttribute("aria-hidden"),inert:e.inert}),ur(e))}})),()=>{if(lr.delete(n),lr.size>0)a.querySelectorAll("body > *").forEach((e=>{if(e instanceof HTMLElement&&!cr.has(e)){for(let t of lr)if(e.contains(t))return;cr.set(e,{"aria-hidden":e.getAttribute("aria-hidden"),inert:e.inert}),ur(e)}}));else for(let e of cr.keys())dr(e),cr.delete(e)}}}),[t])}(f,!!O&&T);let k=q((()=>{var e,t;return[...Array.from(null!=(e=null==v?void 0:v.querySelectorAll("html > *, body > *, [data-headlessui-portal]"))?e:[]).filter((e=>!(e===document.body||e===document.head||!(e instanceof HTMLElement)||e.contains(b.current)||E.panelRef.current&&e.contains(E.panelRef.current)))),null!=(t=E.panelRef.current)?t:f.current]}));ne((()=>k()),R,T&&!O),rr(null==v?void 0:v.defaultView,"keydown",(e=>{e.defaultPrevented||e.key===Pe.Escape&&0===N&&(O||(e.preventDefault(),e.stopPropagation(),R()))})),function(e,t,n=(()=>[document.body])){(0,i.useEffect)((()=>{var a;if(!t||!e)return;let r=M(),s=window.pageYOffset;function o(e,t,n){let a=e.style.getPropertyValue(t);return Object.assign(e.style,{[t]:n}),r.add((()=>{Object.assign(e.style,{[t]:a})}))}let i=e.documentElement,l=(null!=(a=e.defaultView)?a:window).innerWidth-i.clientWidth;if(o(i,"overflow","hidden"),l>0&&o(i,"paddingRight",l-(i.clientWidth-i.offsetWidth)+"px"),/iPhone/gi.test(window.navigator.platform)||/Mac/gi.test(window.navigator.platform)&&window.navigator.maxTouchPoints>0){o(e.body,"marginTop",`-${s}px`),window.scrollTo(0,0);let t=null;r.addEventListener(e,"click",(a=>{if(a.target instanceof HTMLElement)try{let r=a.target.closest("a");if(!r)return;let{hash:s}=new URL(r.href),o=e.querySelector(s);o&&!n().some((e=>e.contains(o)))&&(t=o)}catch{}}),!0),r.addEventListener(e,"touchmove",(e=>{e.target instanceof HTMLElement&&!n().some((t=>t.contains(e.target)))&&e.preventDefault()}),{passive:!1}),r.add((()=>{window.scrollTo(0,window.pageYOffset+s),t&&t.isConnected&&(t.scrollIntoView({block:"nearest"}),t=null)}))}return r.dispose}),[e,t])}(v,0===N&&!C,k),(0,i.useEffect)((()=>{if(0!==N||!f.current)return;let e=new IntersectionObserver((e=>{for(let t of e)0===t.boundingClientRect.x&&0===t.boundingClientRect.y&&0===t.boundingClientRect.width&&0===t.boundingClientRect.height&&R()}));return e.observe(f.current),()=>e.disconnect()}),[N,f,R]);let[P,L]=pa(),F=(0,i.useMemo)((()=>[{dialogState:N,close:R,setTitleId:w},E]),[N,E,R,w]),I=(0,i.useMemo)((()=>({open:0===N})),[N]),D={ref:y,id:a,role:"dialog","aria-modal":0===N||void 0,"aria-labelledby":E.titleId,"aria-describedby":P};return i.createElement(wr,{type:"Dialog",enabled:0===N,element:f,onUpdate:q(((e,t,n)=>{"Dialog"===t&&H(e,{[Rr.Add](){m.current.add(n),d((e=>e+1))},[Rr.Remove](){m.current.add(n),d((e=>e-1))}})}))},i.createElement(yr,{force:!0},i.createElement(Er,null,i.createElement(Sr.Provider,{value:F},i.createElement(Er.Group,{target:f},i.createElement(yr,{force:!1},i.createElement(L,{slot:I,name:"Dialog.Description"},i.createElement(or,{initialFocus:o,containers:m,features:T?H(S,{parent:or.features.RestoreFocus,leaf:or.features.All&~or.features.FocusLock}):or.features.None},fe({ourProps:D,theirProps:c,slot:I,defaultTag:"div",features:_r,visible:0===N,name:"Dialog"})))))))),i.createElement(Te,{features:we.Hidden,ref:b}))})),Fr=ve((function(e,t){let n=B(),{id:a=`headlessui-dialog-overlay-${n}`,...r}=e,[{dialogState:s,close:o}]=kr("Dialog.Overlay");return fe({ourProps:{ref:ie(t),id:a,"aria-hidden":!0,onClick:q((e=>{if(e.target===e.currentTarget){if(Ne(e.currentTarget))return e.preventDefault();e.preventDefault(),e.stopPropagation(),o()}}))},theirProps:r,slot:(0,i.useMemo)((()=>({open:0===s})),[s]),defaultTag:"div",name:"Dialog.Overlay"})})),Ir=ve((function(e,t){let n=B(),{id:a=`headlessui-dialog-backdrop-${n}`,...r}=e,[{dialogState:s},o]=kr("Dialog.Backdrop"),l=ie(t);(0,i.useEffect)((()=>{if(null===o.panelRef.current)throw new Error("A <Dialog.Backdrop /> component is being used, but a <Dialog.Panel /> component is missing.")}),[o.panelRef]);let c=(0,i.useMemo)((()=>({open:0===s})),[s]);return i.createElement(yr,{force:!0},i.createElement(Er,null,fe({ourProps:{ref:l,id:a,"aria-hidden":!0},theirProps:r,slot:c,defaultTag:"div",name:"Dialog.Backdrop"})))})),Mr=ve((function(e,t){let n=B(),{id:a=`headlessui-dialog-panel-${n}`,...r}=e,[{dialogState:s},o]=kr("Dialog.Panel"),l=ie(t,o.panelRef),c=(0,i.useMemo)((()=>({open:0===s})),[s]);return fe({ourProps:{ref:l,id:a,onClick:q((e=>{e.stopPropagation()}))},theirProps:r,slot:c,defaultTag:"div",name:"Dialog.Panel"})})),Dr=ve((function(e,t){let n=B(),{id:a=`headlessui-dialog-title-${n}`,...r}=e,[{dialogState:s,setTitleId:o}]=kr("Dialog.Title"),l=ie(t);(0,i.useEffect)((()=>(o(a),()=>o(null))),[a,o]);let c=(0,i.useMemo)((()=>({open:0===s})),[s]);return fe({ourProps:{ref:l,id:a},theirProps:r,slot:c,defaultTag:"h2",name:"Dialog.Title"})})),qr=Object.assign(Lr,{Backdrop:Ir,Panel:Mr,Overlay:Fr,Title:Dr,Description:ma});const Ar=(0,i.forwardRef)((({children:e,className:t},n)=>l().createElement("div",{ref:n,className:r()("yst-modal__container-header",t)},e)));Ar.displayName="Modal.Container.Header",Ar.propTypes={children:o().node.isRequired,className:o().string},Ar.defaultProps={className:""};const jr=(0,i.forwardRef)((({children:e,className:t},n)=>l().createElement("div",{ref:n,className:r()("yst-modal__container-content",t)},e)));jr.displayName="Modal.Container.Content",jr.propTypes={children:o().node.isRequired,className:o().string},jr.defaultProps={className:""};const Br=(0,i.forwardRef)((({children:e,className:t},n)=>l().createElement("div",{ref:n,className:r()("yst-modal__container-footer",t)},e)));Br.displayName="Modal.Container.Footer",Br.propTypes={children:o().node.isRequired,className:o().string},Br.defaultProps={className:""};const Hr=(0,i.forwardRef)((({children:e,className:t},n)=>l().createElement("div",{ref:n,className:r()("yst-modal__container",t)},e)));Hr.displayName="Modal.Container",Hr.propTypes={children:o().node.isRequired,className:o().string},Hr.defaultProps={className:""},Hr.Header=Ar,Hr.Content=jr,Hr.Footer=Br;const zr=(0,i.createContext)({isOpen:!1,onClose:c.noop}),$r=()=>(0,i.useContext)(zr),Vr=(0,i.forwardRef)((({children:t,size:n,className:a,as:s,...o},i)=>l().createElement(qr.Title,e({as:s,ref:i,className:r()("yst-title",n?Zn.size[n]:"",a)},o),t)));Vr.displayName="Modal.Title",Vr.propTypes={size:o().oneOf(Object.keys(Zn.size)),className:o().string,children:o().node.isRequired,as:o().elementType},Vr.defaultProps={size:void 0,className:"",as:"h1"};const Ur=(0,i.forwardRef)((({children:t,className:n="",hasCloseButton:a=!0,closeButtonScreenReaderText:s="Close",...o},i)=>{const{onClose:c}=$r(),d=u();return l().createElement(qr.Panel,e({ref:i,className:r()("yst-modal__panel",n)},o),a&&l().createElement("div",{className:"yst-modal__close"},l().createElement("button",{type:"button",onClick:c,className:"yst-modal__close-button"},l().createElement("span",{className:"yst-sr-only"},s),l().createElement(gt,e({className:"yst-h-6 yst-w-6"},d)))),t)}));Ur.displayName="Modal.Panel",Ur.propTypes={children:o().node.isRequired,className:o().string,hasCloseButton:o().bool,closeButtonScreenReaderText:o().string},Ur.defaultProps={className:"",hasCloseButton:!0,closeButtonScreenReaderText:"Close"};const Wr={position:{center:"yst-modal--center","top-center":"yst-modal--top-center"}},Gr=(0,i.forwardRef)((({isOpen:t,onClose:n,children:a,className:s="",position:o="center",initialFocus:c=null,...u},d)=>l().createElement(zr.Provider,{value:{isOpen:t,onClose:n,initialFocus:c}},l().createElement(vt.Root,{show:t,as:i.Fragment},l().createElement(qr,e({as:"div",ref:d,className:"yst-root",open:t,onClose:n,initialFocus:c},u),l().createElement("div",{className:r()("yst-modal",Wr.position[o],s)},l().createElement(vt.Child,{as:i.Fragment,enter:"yst-ease-out yst-duration-300",enterFrom:"yst-opacity-0",enterTo:"yst-opacity-100",leave:"yst-ease-in yst-duration-200",leaveFrom:"yst-opacity-100",leaveTo:"yst-opacity-0"},l().createElement("div",{className:"yst-modal__overlay"})),l().createElement("div",{className:"yst-modal__layout"},l().createElement(vt.Child,{as:i.Fragment,enter:"yst-ease-out yst-duration-300",enterFrom:"yst-opacity-0 yst-translate-y-4 sm:yst-translate-y-0 sm:yst-scale-95",enterTo:"yst-opacity-100 yst-translate-y-0 sm:yst-scale-100",leave:"yst-ease-in yst-duration-200",leaveFrom:"yst-opacity-100 yst-translate-y-0 sm:yst-scale-100",leaveTo:"yst-opacity-0 yst-translate-y-4 sm:yst-translate-y-0 sm:yst-scale-95"},a))))))));Gr.displayName="Modal",Gr.propTypes={isOpen:o().bool.isRequired,onClose:o().func.isRequired,children:o().node.isRequired,className:o().string,position:o().oneOf(Object.keys(Wr.position)),initialFocus:o().oneOfType([o().func,o().object])},Gr.defaultProps={className:"",position:"center",initialFocus:null},Gr.Panel=Ur,Gr.Title=Vr,Gr.Description=qr.Description,Gr.Description.displayName="Modal.Description",Gr.Container=Hr;const Kr=Gr,Qr=(0,i.createContext)({position:"bottom-left"}),Yr=()=>(0,i.useContext)(Qr),Zr={variant:{info:"yst-notification--info",warning:"yst-notification--warning",success:"yst-notification--success",error:"yst-notification--error"},size:{default:"",large:"yst-notification--large"}},Xr=({children:e,id:t,variant:n="info",size:a="default",title:s="",description:o="",onDismiss:u=c.noop,autoDismiss:d=null,dismissScreenReaderLabel:p})=>{const{position:m}=Yr(),[f,y]=(0,i.useState)(!1);return l().createElement(oa,{id:t,className:r()("yst-notification",Zr.variant[n],Zr.size[a]),position:m,size:a,onDismiss:u,autoDismiss:d,dismissScreenReaderLabel:p,isVisible:f,setIsVisible:y},l().createElement("div",{className:"yst-flex yst-items-start yst-gap-3"},l().createElement("div",{className:"yst-flex-shrink-0"},l().createElement(h,{variant:n,className:"yst-notification__icon"})),l().createElement("div",{className:"yst-w-0 yst-flex-1"},s&&l().createElement(oa.Title,{title:s}),e||o&&l().createElement(oa.Description,{description:o})),u&&l().createElement(oa.Close,{dismissScreenReaderLabel:p})))};Xr.propTypes={children:o().node,id:o().string.isRequired,variant:o().oneOf((0,c.keys)(Zr.variant)),size:o().oneOf((0,c.keys)(Zr.size)),title:o().string,description:o().oneOfType([o().node,o().arrayOf(o().node)]),onDismiss:o().func,autoDismiss:o().oneOfType([o().number,null]),dismissScreenReaderLabel:o().string.isRequired};const Jr={position:{"bottom-center":"yst-notifications--bottom-center","bottom-left":"yst-notifications--bottom-left","top-center":"yst-notifications--top-center"}},es=({children:t,className:n="",position:a="bottom-left",...s})=>l().createElement(Qr.Provider,{value:{position:a}},l().createElement("aside",e({className:r()("yst-notifications",Jr.position[a],n)},s),t));es.propTypes={children:o().node,className:o().string,position:o().oneOf((0,c.keys)(Jr.position))},(es.Notification=Xr).displayName="Notifications.Notification";const ts=es,ns=i.forwardRef((function(e,t){return i.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true",ref:t},e),i.createElement("path",{fillRule:"evenodd",d:"M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z",clipRule:"evenodd"}))})),as=i.forwardRef((function(e,t){return i.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true",ref:t},e),i.createElement("path",{fillRule:"evenodd",d:"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z",clipRule:"evenodd"}))})),rs=({className:t,children:n,active:a,disabled:s,...o})=>l().createElement("button",e({type:"button",className:r()("yst-pagination__button",t,a&&!s&&"yst-pagination__button--active",s&&"yst-pagination__button--disabled"),disabled:s},o),n);rs.displayName="Pagination.Button",rs.propTypes={className:o().string,children:o().node.isRequired,active:o().bool,disabled:o().bool},rs.defaultProps={className:"",active:!1,disabled:void 0};const ss=rs,os=()=>l().createElement("span",{className:"yst-pagination-display__truncated"},"..."),is=({current:e,total:t,onNavigate:n,maxPageButtons:a,disabled:r})=>{const s=(0,i.useMemo)((()=>(0,c.clamp)(t,1,a)),[t,a]),o=(0,i.useMemo)((()=>(0,c.round)(s/2,0)),[s]),u=(0,i.useMemo)((()=>t>a&&a>1&&e!==o+1),[t,a,o]),d=(0,i.useMemo)((()=>t-(s-o)+1),[t,s,o]),p=(0,i.useMemo)((()=>e>o&&e<d),[e,o,d]);return l().createElement(l().Fragment,null,(0,c.range)(o).map((t=>{const a=t+1;return l().createElement(ss,{key:a,className:"yst-px-4",onClick:n,"data-page":a,active:a===e,disabled:r},a)})),u&&l().createElement(os,null),p&&l().createElement(l().Fragment,null,l().createElement(ss,{className:"yst-px-4",onClick:n,"data-page":e,active:!0,disabled:r},e),e!==d-1&&l().createElement(os,null)),(0,c.rangeRight)(s-o).map((a=>{const s=t-a;return l().createElement(ss,{key:s,className:"yst-px-4",onClick:n,"data-page":s,active:s===e,disabled:r},s)})))};is.displayName="Pagination.DisplayButtons",is.propTypes={current:o().number.isRequired,total:o().number.isRequired,onNavigate:o().func.isRequired,maxPageButtons:o().number.isRequired,disabled:o().bool.isRequired};const ls=is,cs=({current:e,total:t})=>l().createElement("div",{className:"yst-pagination-display__text"},l().createElement("span",{className:"yst-pagination-display__current-text"},e)," / ",t);cs.displayName="Pagination.DisplayText",cs.propTypes={current:o().number.isRequired,total:o().number.isRequired};const us=cs,ds={buttons:"buttons",text:"text"},ps=({className:t,current:n,total:a,onNavigate:s,variant:o,maxPageButtons:d,disabled:p,screenReaderTextPrevious:m,screenReaderTextNext:f,...y})=>{const b=u(),v=(0,i.useCallback)((({target:e})=>s((0,c.parseInt)(e.dataset.page))),[s]);return l().createElement("nav",e({className:r()("yst-pagination",t)},y),l().createElement(ss,{className:"yst-rounded-l-md",onClick:v,"data-page":n-1,disabled:p||n-1<1},l().createElement("span",{className:"yst-pointer-events-none yst-sr-only"},m),l().createElement(ns,e({className:"yst-pointer-events-none yst-h-5 yst-w-5 rtl:yst-rotate-180"},b))),o===ds.text&&l().createElement(us,{current:n,total:a}),o===ds.buttons&&l().createElement(ls,{current:n,total:a,maxPageButtons:d,onNavigate:v,disabled:p}),l().createElement(ss,{className:"yst-rounded-r-md",onClick:v,"data-page":n+1,disabled:p||n+1>a},l().createElement("span",{className:"yst-pointer-events-none yst-sr-only"},f),l().createElement(as,e({className:"yst-pointer-events-none yst-h-5 yst-w-5 rtl:yst-rotate-180"},b))))};ps.propTypes={className:o().string,current:o().number.isRequired,total:o().number.isRequired,onNavigate:o().func.isRequired,variant:o().oneOf(Object.keys(ds)),maxPageButtons:o().number,disabled:o().bool,screenReaderTextPrevious:o().string.isRequired,screenReaderTextNext:o().string.isRequired},ps.defaultProps={className:"",variant:ds.buttons,maxPageButtons:6,disabled:!1};const ms=ps,fs={variant:{default:"","inline-block":"yst-radio-group--inline-block"}},ys=({children:t=null,id:n="",name:a="",value:s="",label:o,description:u,options:d,onChange:p=c.noop,variant:m="default",disabled:f=!1,className:y="",...b})=>{const v=(0,i.useCallback)((({target:e})=>e.checked&&p(e.value)),[p]);return l().createElement("fieldset",{id:`radio-group-${n}`,className:r()("yst-radio-group",f&&"yst-radio-group--disabled",fs.variant[m],y)},o&&l().createElement(zt,{as:"legend",className:"yst-radio-group__label",label:o}),u&&l().createElement("div",{className:"yst-radio-group__description"},u),l().createElement("div",{className:"yst-radio-group__options"},t||d.map(((t,r)=>{const o=`radio-${n}-${r}`;return l().createElement(ln,e({key:o,id:o,name:a,value:t.value,label:t.label,screenReaderLabel:t.screenReaderLabel,variant:m,checked:s===t.value,onChange:v,disabled:f},b))}))))};ys.propTypes={children:o().node,id:o().string,name:o().string,value:o().string,label:o().string,description:o().string,options:o().arrayOf(o().shape({value:o().string.isRequired,label:o().string.isRequired,screenReaderLabel:o().string})),onChange:o().func,variant:o().oneOf(Object.keys(fs.variant)),disabled:o().bool,className:o().string},(ys.Radio=ln).displayName="RadioGroup.Radio";const bs=ys,vs={isRtl:!1},gs=(0,i.createContext)(vs),hs=({children:t,context:n={},...a})=>l().createElement(gs.Provider,{value:{...vs,...n}},l().createElement("div",e({className:"yst-root"},a),t));hs.propTypes={children:o().node.isRequired,context:o().shape({isRtl:o().bool})};const Ns=hs,Es=(0,i.forwardRef)((({id:t,label:n,description:a,disabled:s,validation:o,className:i,...c},u)=>{const{ids:d,describedBy:p}=Ra(t,{validation:null==o?void 0:o.message,description:a});return l().createElement("div",{className:r()("yst-select-field",s&&"yst-select-field--disabled",i)},l().createElement(Ln,e({ref:u,id:t,label:n,labelProps:{as:"label",className:"yst-label yst-select-field__label"},disabled:s,validation:o,className:"yst-select-field__select",buttonProps:{"aria-describedby":p}},c)),(null==o?void 0:o.message)&&l().createElement(x,{variant:null==o?void 0:o.variant,id:d.validation,className:"yst-select-field__validation"},o.message),a&&l().createElement("div",{id:d.description,className:"yst-select-field__description"},a))}));Es.displayName="SelectField",Es.propTypes={id:o().string.isRequired,label:o().string.isRequired,description:o().node,disabled:o().bool,validation:o().shape({variant:o().string,message:o().node}),className:o().string},Es.defaultProps={description:null,disabled:!1,validation:{},className:""},Es.Option=Ln.Option,Es.Option.displayName="SelectField.Option";const xs=Es,Rs=i.forwardRef((function(e,t){return i.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:2,stroke:"currentColor","aria-hidden":"true",ref:t},e),i.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M5 15l7-7 7 7"}))})),ws=i.forwardRef((function(e,t){return i.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:2,stroke:"currentColor","aria-hidden":"true",ref:t},e),i.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M19 9l-7 7-7-7"}))})),Ts=({label:t,icon:n=null,children:a=null,defaultOpen:r=!0,...s})=>{const[o,i]=ja(r),c=o?Rs:ws;return l().createElement("div",null,l().createElement("button",e({type:"button",className:"yst-group yst-flex yst-w-full yst-items-center yst-justify-between yst-gap-3 yst-px-3 yst-py-2 yst-text-sm yst-font-medium yst-text-slate-800 yst-rounded-md yst-no-underline hover:yst-text-slate-900 hover:yst-bg-slate-50 focus:yst-outline-none focus:yst-ring-2 focus:yst-ring-primary-500",onClick:i,"aria-expanded":o},s),l().createElement("span",{className:"yst-flex yst-items-center yst-gap-3"},n&&l().createElement(n,{className:"yst-flex-shrink-0 yst--ml-1 yst-h-6 yst-w-6 yst-text-slate-400 group-hover:yst-text-slate-500"}),t),l().createElement(c,{className:"yst-h-4 yst-w-4 yst-text-slate-400 group-hover:yst-text-slate-500 yst-stroke-3"})),o&&a&&l().createElement("ul",{className:"yst-ml-8 yst-mt-1 yst-space-y-1"},a))};Ts.propTypes={label:o().string.isRequired,icon:o().elementType,defaultOpen:o().bool,children:o().node};const Os=Ts,Cs=i.forwardRef((function(e,t){return i.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:2,stroke:"currentColor","aria-hidden":"true",ref:t},e),i.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M4 6h16M4 12h16M4 18h7"}))})),Ss=({children:e,openButtonId:t,closeButtonId:n,openButtonScreenReaderText:a="Open",closeButtonScreenReaderText:r="Close","aria-label":s})=>{const{isMobileMenuOpen:o,setMobileMenuOpen:c}=Ms(),u=(0,i.useCallback)((()=>c(!0)),[c]),d=(0,i.useCallback)((()=>c(!1)),[c]);return l().createElement(l().Fragment,null,l().createElement(qr,{className:"yst-root",open:o,onClose:d,"aria-label":s},l().createElement("div",{className:"yst-mobile-navigation__dialog"},l().createElement("div",{className:"yst-fixed yst-inset-0 yst-bg-slate-600 yst-bg-opacity-75 yst-z-30","aria-hidden":"true"}),l().createElement(qr.Panel,{className:"yst-relative yst-flex yst-flex-1 yst-flex-col yst-max-w-xs yst-w-full yst-z-40 yst-bg-slate-100"},l().createElement("div",{className:"yst-absolute yst-top-0 yst-right-0 yst--mr-14 yst-p-1"},l().createElement("button",{type:"button",id:n,className:"yst-flex yst-h-12 yst-w-12 yst-items-center yst-justify-center yst-rounded-full focus:yst-outline-none yst-bg-slate-600 focus:yst-ring-2 focus:yst-ring-inset focus:yst-ring-primary-500",onClick:d},l().createElement("span",{className:"yst-sr-only"},r),l().createElement(gt,{className:"yst-h-6 yst-w-6 yst-text-white"}))),l().createElement("div",{className:"yst-flex-1 yst-h-0 yst-overflow-y-auto"},l().createElement("nav",{className:"yst-h-full yst-flex yst-flex-col yst-py-6 yst-px-2"},e))))),l().createElement("div",{className:"yst-mobile-navigation__top"},l().createElement("div",{className:"yst-flex yst-relative yst-flex-shrink-0 yst-h-16 yst-z-10 yst-bg-white yst-border-b yst-border-slate-200"},l().createElement("button",{type:"button",id:t,className:"yst-px-4 yst-border-r yst-border-slate-200 yst-text-slate-500 focus:yst-outline-none focus:yst-ring-2 focus:yst-ring-inset focus:yst-ring-primary-500",onClick:u},l().createElement("span",{className:"yst-sr-only"},a),l().createElement(Cs,{className:"yst-w-6 yst-h-6"})))))};Ss.propTypes={children:o().node.isRequired,openButtonId:o().string,closeButtonId:o().string,openButtonScreenReaderText:o().string,closeButtonScreenReaderText:o().string,"aria-label":o().string};const ks=Ss,Ps=({children:e,className:t=""})=>l().createElement("nav",{className:t},e);Ps.propTypes={children:o().node.isRequired,className:o().string};const _s=Ps,Ls=({as:t="a",pathProp:n="href",label:a,...s})=>{const{activePath:o,setMobileMenuOpen:c}=Ms(),u=(0,i.useCallback)((()=>c(!1)),[c]);return l().createElement("li",{className:"yst-m-0 yst-pb-1"},l().createElement(t,e({className:r()("yst-group yst-flex yst-items-center yst-px-3 yst-py-2 yst-text-sm yst-font-medium yst-rounded-md yst-no-underline focus:yst-outline-none focus:yst-ring-1 focus:yst-ring-offset-1 focus:yst-ring-offset-transparent focus:yst-ring-primary-500",o===s[n]?"yst-bg-slate-200 yst-text-slate-900":"yst-text-slate-600 hover:yst-text-slate-900 hover:yst-bg-slate-50"),"aria-current":o===s[n]?"page":null,onClick:u},s),a))};Ls.propTypes={as:o().elementType,pathProp:o().string,label:o().node.isRequired,isActive:o().bool};const Fs=Ls,Is=(0,i.createContext)({activePath:"",isMobileMenuOpen:!1,setMobileMenuOpen:c.noop}),Ms=()=>(0,i.useContext)(Is),Ds=({activePath:e="",children:t})=>{const[n,a]=(0,i.useState)(!1);return l().createElement(Is.Provider,{value:{activePath:e,isMobileMenuOpen:n,setMobileMenuOpen:a}},t)};Ds.propTypes={activePath:o().string,children:o().node.isRequired},(Ds.Sidebar=_s).displayName="SidebarNavigation.Sidebar",(Ds.Mobile=ks).displayName="SidebarNavigation.Mobile",(Ds.MenuItem=Os).displayName="SidebarNavigation.MenuItem",(Ds.SubmenuItem=Fs).displayName="SidebarNavigation.SubmenuItem";const qs=Ds,As=(0,i.forwardRef)((({id:t,label:n,labelSuffix:a,disabled:s,className:o,description:i,validation:c,...u},d)=>{const{ids:p,describedBy:m}=Ra(t,{validation:null==c?void 0:c.message,description:i});return l().createElement("div",{className:r()("yst-tag-field",s&&"yst-tag-field--disabled",o)},l().createElement("div",{className:"yst-flex yst-items-center yst-mb-2"},l().createElement(zt,{className:"yst-tag-field__label",htmlFor:t,label:n}),a),l().createElement(Rt,e({as:Wn,ref:d,id:t,disabled:s,className:"yst-tag-field__input","aria-describedby":m,validation:c},u)),(null==c?void 0:c.message)&&l().createElement(x,{variant:null==c?void 0:c.variant,id:p.validation,className:"yst-tag-field__validation"},c.message),i&&l().createElement("p",{id:p.description,className:"yst-tag-field__description"},i))}));As.displayName="TagField",As.propTypes={id:o().string.isRequired,label:o().string.isRequired,labelSuffix:o().node,disabled:o().bool,className:o().string,description:o().node,validation:o().shape({variant:o().string,message:o().node})},As.defaultProps={labelSuffix:null,disabled:!1,className:"",description:null,validation:{}};const js=As,Bs=(0,i.forwardRef)((({id:t,onChange:n,label:a,labelSuffix:s,disabled:o,readOnly:i,className:c,description:u,validation:d,...p},m)=>{const{ids:f,describedBy:y}=Ra(t,{validation:null==d?void 0:d.message,description:u});return l().createElement("div",{className:r()("yst-text-field",o&&"yst-text-field--disabled",i&&"yst-text-field--read-only",c)},l().createElement("div",{className:"yst-flex yst-items-center yst-mb-2"},l().createElement(zt,{className:"yst-text-field__label",htmlFor:t},a),s),l().createElement(Rt,e({as:Kn,ref:m,id:t,onChange:n,disabled:o,readOnly:i,className:"yst-text-field__input","aria-describedby":y,validation:d},p)),(null==d?void 0:d.message)&&l().createElement(x,{variant:null==d?void 0:d.variant,id:f.validation,className:"yst-text-field__validation"},d.message),u&&l().createElement("p",{id:f.description,className:"yst-text-field__description"},u))}));Bs.displayName="TextField",Bs.propTypes={id:o().string.isRequired,onChange:o().func.isRequired,label:o().string.isRequired,labelSuffix:o().node,disabled:o().bool,readOnly:o().bool,className:o().string,description:o().node,validation:o().shape({variant:o().string,message:o().node})},Bs.defaultProps={labelSuffix:null,disabled:!1,readOnly:!1,className:"",description:null,validation:{}};const Hs=Bs,zs=(0,i.forwardRef)((({id:t,label:n,className:a="",description:s="",validation:o={},disabled:i,readOnly:c,...u},d)=>{const{ids:p,describedBy:m}=Ra(t,{validation:null==o?void 0:o.message,description:s});return l().createElement("div",{className:r()("yst-textarea-field",i&&"yst-textarea-field--disabled",c&&"yst-textarea-field--read-only",a)},l().createElement("div",{className:"yst-flex yst-items-center yst-mb-2"},l().createElement(zt,{className:"yst-textarea-field__label",htmlFor:t},n)),l().createElement(Rt,e({as:Yn,ref:d,id:t,className:"yst-textarea-field__input","aria-describedby":m,validation:o,disabled:i,readOnly:c},u)),(null==o?void 0:o.message)&&l().createElement(x,{variant:null==o?void 0:o.variant,id:p.validation,className:"yst-textarea-field__validation"},o.message),s&&l().createElement("p",{id:p.description,className:"yst-textarea-field__description"},s))}));zs.displayName="TextareaField",zs.propTypes={id:o().string.isRequired,label:o().string.isRequired,className:o().string,description:o().node,disabled:o().bool,readOnly:o().bool,validation:o().shape({variant:o().string,message:o().node})},zs.defaultProps={className:"",description:null,disabled:!1,readOnly:!1,validation:{}};const $s=zs,Vs=(0,i.forwardRef)((({id:t,children:n,label:a,labelSuffix:s,description:o,checked:i,disabled:c,onChange:u,className:d,"aria-label":p,...m},f)=>l().createElement(va.Group,{as:"div",className:r()("yst-toggle-field",c&&"yst-toggle-field--disabled",d)},l().createElement("div",{className:"yst-toggle-field__header"},a&&l().createElement("div",{className:"yst-toggle-field__label-wrapper"},l().createElement(zt,{as:va.Label,className:"yst-toggle-field__label",label:a,"aria-label":p}),s),l().createElement(ha,e({id:t,ref:f,checked:i,onChange:u,screenReaderLabel:a,disabled:c},m))),(o||n)&&l().createElement(va.Description,{as:"div",className:"yst-toggle-field__description"},o||n))));Vs.displayName="ToggleField",Vs.propTypes={id:o().string.isRequired,children:o().node,label:o().string.isRequired,labelSuffix:o().node,description:o().node,checked:o().bool.isRequired,disabled:o().bool,onChange:o().func.isRequired,className:o().string,"aria-label":o().string},Vs.defaultProps={children:null,labelSuffix:null,description:null,disabled:!1,className:"","aria-label":null};const Us=Vs,Ws=(e,t=!0)=>{const n=(0,i.useCallback)((e=>((e||window.event).returnValue=t,t)),[t]);(0,i.useEffect)((()=>(e&&window.addEventListener("beforeunload",n),()=>window.removeEventListener("beforeunload",n))),[e,n])},Gs=e=>{const t=(0,i.useRef)(e);return(0,i.useEffect)((()=>{t.current=e}),[e]),t.current},Ks=()=>(0,i.useContext)(gs),Qs=e=>{const t=(0,i.useMemo)((()=>window.matchMedia(e)),[e]),[n,a]=(0,i.useState)(t.matches),r=(0,i.useCallback)((e=>{a(e.matches)}),[a]);return(0,i.useEffect)((()=>(t.addEventListener("change",r),()=>{t.removeEventListener("change",r)})),[t,r]),{matches:n}}})(),(window.yoast=window.yoast||{}).uiLibrary=a})();