(()=>{var e={30888:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.YoastSlideToggle=void 0;var r=l(n(99196)),i=l(n(85890)),o=l(n(98487)),s=n(64317),a=n(90876);function l(e){return e&&e.__esModule?e:{default:e}}const d=o.default.div`
	& > :first-child {
		overflow: hidden;
		transition: height ${e=>`${e.duration}ms`} ease-out;
	}
`;class u extends r.default.Component{resetHeight(e){e.style.height="0"}setHeight(e){const t=(0,a.getHeight)(e);e.style.height=t+"px"}removeHeight(e){e.style.height=null}render(){return r.default.createElement(d,{duration:this.props.duration},r.default.createElement(s.CSSTransition,{in:this.props.isOpen,timeout:this.props.duration,classNames:"slide",unmountOnExit:!0,onEnter:this.resetHeight,onEntering:this.setHeight,onEntered:this.removeHeight,onExit:this.setHeight,onExiting:this.resetHeight},this.props.children))}}t.YoastSlideToggle=u,u.propTypes={isOpen:i.default.bool.isRequired,duration:i.default.number,children:i.default.node},u.defaultProps={duration:300,children:[]}},90876:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.getHeight=function(e){return Math.max(e.clientHeight,e.offsetHeight,e.scrollHeight)}},76990:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.getTitleProgress=t.getDescriptionProgress=void 0;var r=n(42982);t.getTitleProgress=e=>{const t=r.helpers.measureTextWidth(e),n=new r.assessments.seo.PageTitleWidthAssessment({scores:{widthTooShort:9}},!0),i=n.calculateScore(t);return{max:n.getMaximumLength(),actual:t,score:i}},t.getDescriptionProgress=(e,t,n,i,o)=>{const s=r.languageProcessing.countMetaDescriptionLength(t,e),a=n&&!i?new r.assessments.seo.MetaDescriptionLengthAssessment({scores:{tooLong:3,tooShort:3}}):new r.assessments.seo.MetaDescriptionLengthAssessment,l=a.calculateScore(s,o);return{max:a.getMaximumLength(o),actual:s,score:l}}},90695:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r=function(e,t){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var n=c(t);if(n&&n.has(e))return n.get(e);var r={__proto__:null},i=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var o in e)if("default"!==o&&Object.prototype.hasOwnProperty.call(e,o)){var s=i?Object.getOwnPropertyDescriptor(e,o):null;s&&(s.get||s.set)?Object.defineProperty(r,o,s):r[o]=e[o]}return r.default=e,n&&n.set(e,r),r}(n(99196)),i=p(n(98487)),o=n(65736),s=p(n(85890)),a=n(81413),l=n(23695),d=n(37188),u=n(99806);function p(e){return e&&e.__esModule?e:{default:e}}function c(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,n=new WeakMap;return(c=function(e){return e?n:t})(e)}const f=i.default.fieldset`
	border: 0;
	padding: 0;
	margin: 0 0 16px;
`,h=i.default.legend`
	margin: 8px 0;
	padding: 0;
	color: ${d.colors.$color_headings};
	font-size: 14px;
	font-weight: 600;
`,g=(0,i.default)(a.Label)`
	${(0,l.getDirectionalStyle)("margin-right: 16px","margin-left: 16px")};
	color: inherit;
	font-size: 14px;
	line-height: 1.71428571;
	cursor: pointer;
	/* Helps RTL in Chrome */
	display: inline-block;
`,m=(0,i.default)(a.Input)`
	&& {
		${(0,l.getDirectionalStyle)("margin: 0 8px 0 0","margin: 0 0 0 8px")};
		cursor: pointer;
	}
`;class v extends r.Component{constructor(e){super(e),this.switchToMobile=this.props.onChange.bind(this,"mobile"),this.switchToDesktop=this.props.onChange.bind(this,"desktop")}render(){const{active:e,mobileModeInputId:t,desktopModeInputId:n}=this.props,i=t.length>0?t:"yoast-google-preview-mode-mobile",s=n.length>0?n:"yoast-google-preview-mode-desktop";return r.default.createElement(f,null,r.default.createElement(h,null,(0,o.__)("Preview as:","wordpress-seo")),r.default.createElement(m,{onChange:this.switchToMobile,type:"radio",name:"screen",value:"mobile",optionalAttributes:{id:i,checked:e===u.MODE_MOBILE}}),r.default.createElement(g,{for:i},(0,o.__)("Mobile result","wordpress-seo")),r.default.createElement(m,{onChange:this.switchToDesktop,type:"radio",name:"screen",value:"desktop",optionalAttributes:{id:s,checked:e===u.MODE_DESKTOP}}),r.default.createElement(g,{for:s},(0,o.__)("Desktop result","wordpress-seo")))}}v.propTypes={onChange:s.default.func.isRequired,active:s.default.oneOf(u.MODES),mobileModeInputId:s.default.string,desktopModeInputId:s.default.string},v.defaultProps={active:u.MODE_MOBILE,mobileModeInputId:"",desktopModeInputId:""},t.default=v},24861:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r=b(n(98487)),i=b(n(99196)),o=b(n(85890)),s=n(65736),a=n(92819),l=n(42982),d=n(81413),u=n(37188),p=n(23695),c=n(10224),f=n(76990),h=n(99806),g=b(n(64475)),m=b(n(17582)),v=n(95157),E=b(n(90695));function b(e){return e&&e.__esModule?e:{default:e}}function y(){return y=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},y.apply(this,arguments)}const x=r.default.legend`
	margin: 0 0 16px;
	padding: 0;
	color: ${u.colors.$color_headings};
	font-size: 12px;
	font-weight: 300;
`,w=(0,r.default)(d.Button)`
	height: 33px;
	border: 1px solid #dbdbdb;
	box-shadow: none;
	font-family: Arial, Roboto-Regular, HelveticaNeue, sans-serif;
`,_=(0,r.default)(w)`
	margin: ${(0,p.getDirectionalStyle)("10px 0 0 4px","10px 4px 0 0")};
	fill: ${u.colors.$color_grey_dark};
	padding-left: 8px;

	& svg {
		${(0,p.getDirectionalStyle)("margin-right","margin-left")}: 7px;
	}
`,S=(0,r.default)(w)`
	margin-top: 24px;
`,M=new RegExp("(%%sep%%|%%sitename%%)","g");class O extends i.default.Component{constructor(e){super(e);const t=this.mapDataToMeasurements(e.data);this.state={isOpen:!e.showCloseButton,activeField:null,hoveredField:null,titleLengthProgress:(0,f.getTitleProgress)(t.filteredSEOTitle),descriptionLengthProgress:(0,f.getDescriptionProgress)(t.description,this.props.date,this.props.isCornerstone,this.props.isTaxonomy,this.props.locale)},this.setFieldFocus=this.setFieldFocus.bind(this),this.unsetFieldFocus=this.unsetFieldFocus.bind(this),this.onChangeMode=this.onChangeMode.bind(this),this.onMouseUp=this.onMouseUp.bind(this),this.onMouseEnter=this.onMouseEnter.bind(this),this.onMouseLeave=this.onMouseLeave.bind(this),this.open=this.open.bind(this),this.close=this.close.bind(this),this.setEditButtonRef=this.setEditButtonRef.bind(this),this.handleChange=this.handleChange.bind(this),this.haveReplaceVarsChanged=this.haveReplaceVarsChanged.bind(this)}shallowCompareData(e,t){let n=!1;return e.data.description===t.data.description&&e.data.slug===t.data.slug&&e.data.title===t.data.title&&e.isCornerstone===t.isCornerstone&&e.isTaxonomy===t.isTaxonomy&&e.locale===t.locale||(n=!0),this.haveReplaceVarsChanged(e.replacementVariables,t.replacementVariables)&&(n=!0),n}haveReplaceVarsChanged(e,t){return JSON.stringify(e)!==JSON.stringify(t)}componentDidUpdate(e){if(this.shallowCompareData(this.props,e)){const e=this.mapDataToMeasurements(this.props.data,this.props.replacementVariables);this.setState({titleLengthProgress:(0,f.getTitleProgress)(e.filteredSEOTitle),descriptionLengthProgress:(0,f.getDescriptionProgress)(e.description,this.props.date,this.props.isCornerstone,this.props.isTaxonomy,this.props.locale)}),this.props.onChangeAnalysisData(e)}}handleChange(e,t){this.props.onChange(e,t);const n=this.mapDataToMeasurements({...this.props.data,[e]:t});this.props.onChangeAnalysisData(n)}renderEditor(){const{data:e,descriptionEditorFieldPlaceholder:t,onReplacementVariableSearchChange:n,replacementVariables:r,recommendedReplacementVariables:o,hasPaperStyle:a,showCloseButton:l,idSuffix:d}=this.props,{activeField:u,hoveredField:c,isOpen:f,titleLengthProgress:h,descriptionLengthProgress:g}=this.state;return f?i.default.createElement(i.default.Fragment,null,i.default.createElement(m.default,{data:e,activeField:u,hoveredField:c,onChange:this.handleChange,onFocus:this.setFieldFocus,onBlur:this.unsetFieldFocus,onReplacementVariableSearchChange:n,replacementVariables:r,recommendedReplacementVariables:o,titleLengthProgress:h,descriptionLengthProgress:g,descriptionEditorFieldPlaceholder:t,containerPadding:a?"0 20px":"0",titleInputId:(0,p.join)(["yoast-google-preview-title",d]),slugInputId:(0,p.join)(["yoast-google-preview-slug",d]),descriptionInputId:(0,p.join)(["yoast-google-preview-description",d])}),l&&i.default.createElement(S,{onClick:this.close},(0,s.__)("Close snippet editor","wordpress-seo"))):null}setFieldFocus(e){e=this.mapFieldToEditor(e),this.setState({activeField:e})}unsetFieldFocus(){this.setState({activeField:null})}onChangeMode(e){this.props.onChange("mode",e)}onMouseUp(e){this.state.isOpen?this.setFieldFocus(e):this.open().then(this.setFieldFocus.bind(this,e))}onMouseEnter(e){this.setState({hoveredField:this.mapFieldToEditor(e)})}onMouseLeave(){this.setState({hoveredField:null})}open(){return new Promise((e=>{this.setState({isOpen:!0},e)}))}close(){this.setState({isOpen:!1,activeField:null},(()=>{this._editButton.focus()}))}processReplacementVariables(e,t=this.props.replacementVariables){if(this.props.applyReplacementVariables)return this.props.applyReplacementVariables(e);for(const{name:n,value:r}of t)e=e.replace(new RegExp("%%"+(0,a.escapeRegExp)(n)+"%%","g"),r);return e}mapDataToMeasurements(e,t=this.props.replacementVariables){const{baseUrl:n,mapEditorDataToPreview:r}=this.props;let i=this.processReplacementVariables(e.description,t);i=l.languageProcessing.stripSpaces(i);const o=n.replace(/^https?:\/\//i,""),s=e.title.replace(M,""),a={title:this.processReplacementVariables(e.title,t),url:n+e.slug,description:i,filteredSEOTitle:this.processReplacementVariables(s,t)};return r?r(a,{shortenedBaseUrl:o}):a}mapDataToPreview(e){return{title:e.title,url:e.url,description:e.description}}mapFieldToPreview(e){return"slug"===e&&(e="url"),e}mapFieldToEditor(e){return"url"===e&&(e="slug"),e}setEditButtonRef(e){this._editButton=e}render(){const{data:e,mode:t,date:n,locale:r,keyword:o,wordsToHighlight:a,showCloseButton:l,faviconSrc:u,mobileImageSrc:c,idSuffix:f,shoppingData:h,siteName:m}=this.props,{activeField:v,hoveredField:b,isOpen:w}=this.state,S=this.mapDataToMeasurements(e),M=this.mapDataToPreview(S);return i.default.createElement(d.ErrorBoundary,null,i.default.createElement("div",null,i.default.createElement(x,null,(0,s.__)("Determine how your post should look in the search results.","wordpress-seo")),i.default.createElement(E.default,{onChange:this.onChangeMode,active:t,mobileModeInputId:(0,p.join)(["yoast-google-preview-mode-mobile",f]),desktopModeInputId:(0,p.join)(["yoast-google-preview-mode-desktop",f])}),i.default.createElement(g.default,y({keyword:o,wordsToHighlight:a,mode:t,date:n,siteName:m,activeField:this.mapFieldToPreview(v),hoveredField:this.mapFieldToPreview(b),onMouseEnter:this.onMouseEnter,onMouseLeave:this.onMouseLeave,onMouseUp:this.onMouseUp,locale:r,faviconSrc:u,mobileImageSrc:c,shoppingData:h},M)),l&&i.default.createElement(_,{onClick:w?this.close:this.open,"aria-expanded":w,ref:this.setEditButtonRef},i.default.createElement(d.SvgIcon,{icon:"edit"}),(0,s.__)("Edit snippet","wordpress-seo")),this.renderEditor()))}}O.propTypes={onReplacementVariableSearchChange:o.default.func,replacementVariables:c.replacementVariablesShape,recommendedReplacementVariables:c.recommendedReplacementVariablesShape,data:o.default.shape({title:o.default.string.isRequired,slug:o.default.string.isRequired,description:o.default.string.isRequired}).isRequired,descriptionEditorFieldPlaceholder:o.default.string,baseUrl:o.default.string.isRequired,mode:o.default.oneOf(h.MODES),date:o.default.string,onChange:o.default.func.isRequired,onChangeAnalysisData:o.default.func,titleLengthProgress:v.lengthProgressShape,descriptionLengthProgress:v.lengthProgressShape,applyReplacementVariables:o.default.func,mapEditorDataToPreview:o.default.func,keyword:o.default.string,wordsToHighlight:o.default.array,locale:o.default.string,hasPaperStyle:o.default.bool,showCloseButton:o.default.bool,faviconSrc:o.default.string,mobileImageSrc:o.default.string,idSuffix:o.default.string,shoppingData:o.default.object,isCornerstone:o.default.bool,isTaxonomy:o.default.bool,siteName:o.default.string.isRequired},O.defaultProps={mode:h.DEFAULT_MODE,date:"",wordsToHighlight:[],onReplacementVariableSearchChange:null,replacementVariables:[],recommendedReplacementVariables:[],titleLengthProgress:{max:600,actual:0,score:0},descriptionLengthProgress:{max:156,actual:0,score:0},applyReplacementVariables:null,mapEditorDataToPreview:null,keyword:"",locale:"en",descriptionEditorFieldPlaceholder:"",onChangeAnalysisData:a.noop,hasPaperStyle:!0,showCloseButton:!0,faviconSrc:"",mobileImageSrc:"",idSuffix:"",shoppingData:{},isCornerstone:!1,isTaxonomy:!1},t.default=O},17582:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r=c(n(99196)),i=c(n(98487)),o=c(n(85890)),s=c(n(12049)),a=n(65736),l=n(37188),d=n(10224),u=n(81413),p=n(95157);function c(e){return e&&e.__esModule?e:{default:e}}const f=i.default.input`
	border: none;
	width: 100%;
	height: inherit;
	line-height: 1.71428571; // 24px based on 14px font-size
	font-family: inherit;
	font-size: inherit;
	color: inherit;

	&:focus {
		outline: 0;
	}
`,h=(0,l.withCaretStyles)(u.VariableEditorInputContainer);class g extends r.default.Component{constructor(e){super(e),this.elements={title:null,slug:null,description:null},this.uniqueId=(0,s.default)("snippet-editor-field-"),this.setRef=this.setRef.bind(this),this.setTitleRef=this.setTitleRef.bind(this),this.setSlugRef=this.setSlugRef.bind(this),this.setDescriptionRef=this.setDescriptionRef.bind(this),this.triggerReplacementVariableSuggestions=this.triggerReplacementVariableSuggestions.bind(this),this.onFocusTitle=this.onFocusTitle.bind(this),this.onChangeTitle=this.onChangeTitle.bind(this),this.onFocusSlug=this.onFocusSlug.bind(this),this.focusSlug=this.focusSlug.bind(this),this.onChangeSlug=this.onChangeSlug.bind(this),this.onFocusDescription=this.onFocusDescription.bind(this),this.onChangeDescription=this.onChangeDescription.bind(this)}setRef(e,t){this.elements[e]=t}setTitleRef(e){this.setRef("title",e)}setSlugRef(e){this.setRef("slug",e)}setDescriptionRef(e){this.setRef("description",e)}componentDidUpdate(e){e.activeField!==this.props.activeField&&this.focusOnActiveFieldChange()}focusOnActiveFieldChange(){const{activeField:e}=this.props,t=e?this.elements[e]:null;t&&t.focus()}triggerReplacementVariableSuggestions(e){this.elements[e].triggerReplacementVariableSuggestions()}onFocusTitle(){this.props.onFocus("title")}onChangeTitle(e){this.props.onChange("title",e)}onFocusSlug(){this.props.onFocus("slug")}focusSlug(){this.elements.slug.focus()}onChangeSlug(e){this.props.onChange("slug",e.target.value)}onFocusDescription(){this.props.onFocus("description")}onChangeDescription(e){this.props.onChange("description",e)}render(){const{activeField:e,hoveredField:t,onReplacementVariableSearchChange:n,replacementVariables:i,recommendedReplacementVariables:o,titleLengthProgress:s,descriptionLengthProgress:l,onBlur:p,descriptionEditorFieldPlaceholder:c,data:{title:g,slug:m,description:v},containerPadding:E,titleInputId:b,slugInputId:y,descriptionInputId:x}=this.props,w=`${this.uniqueId}-slug`;return r.default.createElement(d.StyledEditor,{padding:E},r.default.createElement(d.ReplacementVariableEditor,{withCaret:!0,label:(0,a.__)("SEO title","wordpress-seo"),onFocus:this.onFocusTitle,onBlur:p,isActive:"title"===e,isHovered:"title"===t,editorRef:this.setTitleRef,replacementVariables:i,recommendedReplacementVariables:o,content:g,onChange:this.onChangeTitle,onSearchChange:n,fieldId:b,type:"title"}),r.default.createElement(u.ProgressBar,{max:s.max,value:s.actual,progressColor:this.getProgressColor(s.score)}),r.default.createElement(u.SimulatedLabel,{id:w,onClick:this.onFocusSlug},(0,a.__)("Slug","wordpress-seo")),r.default.createElement(h,{onClick:this.focusSlug,isActive:"slug"===e,isHovered:"slug"===t},r.default.createElement(f,{value:m,onChange:this.onChangeSlug,onFocus:this.onFocusSlug,onBlur:p,ref:this.setSlugRef,"aria-labelledby":this.uniqueId+"-slug",id:y})),r.default.createElement(d.ReplacementVariableEditor,{withCaret:!0,type:"description",placeholder:c,label:(0,a.__)("Meta description","wordpress-seo"),onFocus:this.onFocusDescription,onBlur:p,isActive:"description"===e,isHovered:"description"===t,editorRef:this.setDescriptionRef,replacementVariables:i,recommendedReplacementVariables:o,content:v,onChange:this.onChangeDescription,onSearchChange:n,fieldId:x}),r.default.createElement(u.ProgressBar,{max:l.max,value:l.actual,progressColor:this.getProgressColor(l.score)}))}getProgressColor(e){return e>=7?l.colors.$color_good:e>=5?l.colors.$color_ok:l.colors.$color_bad}}g.propTypes={replacementVariables:d.replacementVariablesShape,recommendedReplacementVariables:d.recommendedReplacementVariablesShape,onChange:o.default.func.isRequired,onFocus:o.default.func,onBlur:o.default.func,onReplacementVariableSearchChange:o.default.func,data:o.default.shape({title:o.default.string.isRequired,slug:o.default.string.isRequired,description:o.default.string.isRequired}).isRequired,activeField:o.default.oneOf(["title","slug","description"]),hoveredField:o.default.oneOf(["title","slug","description"]),titleLengthProgress:p.lengthProgressShape,descriptionLengthProgress:p.lengthProgressShape,descriptionEditorFieldPlaceholder:o.default.string,containerPadding:o.default.string,titleInputId:o.default.string,slugInputId:o.default.string,descriptionInputId:o.default.string},g.defaultProps={replacementVariables:[],recommendedReplacementVariables:[],onFocus:()=>{},onBlur:()=>{},onReplacementVariableSearchChange:null,activeField:null,hoveredField:null,titleLengthProgress:{max:600,actual:0,score:0},descriptionLengthProgress:{max:156,actual:0,score:0},descriptionEditorFieldPlaceholder:null,containerPadding:"0 20px",titleInputId:"yoast-google-preview-title",slugInputId:"yoast-google-preview-slug",descriptionInputId:"yoast-google-preview-description"},t.default=g},95157:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.lengthProgressShape=void 0;var r,i=(r=n(85890))&&r.__esModule?r:{default:r};t.lengthProgressShape=i.default.shape({max:i.default.number,actual:i.default.number,score:i.default.number})},12330:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r=function(e,t){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var n=d(t);if(n&&n.has(e))return n.get(e);var r={__proto__:null},i=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var o in e)if("default"!==o&&Object.prototype.hasOwnProperty.call(e,o)){var s=i?Object.getOwnPropertyDescriptor(e,o):null;s&&(s.get||s.set)?Object.defineProperty(r,o,s):r[o]=e[o]}return r.default=e,n&&n.set(e,r),r}(n(99196)),i=l(n(98487)),o=l(n(85890)),s=l(n(25853)),a=n(65736);function l(e){return e&&e.__esModule?e:{default:e}}function d(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,n=new WeakMap;return(d=function(e){return e?n:t})(e)}const u=i.default.div`
	overflow: auto;
	width: ${e=>e.widthValue}px;
	padding: 0 ${e=>e.paddingValue}px;
	max-width: 100%;
	box-sizing: border-box;
`,p=i.default.div`
	width: ${e=>e.widthValue}px;
`,c=i.default.div`
	text-align: center;
	margin: 1em 0 5px;
`,f=i.default.div`
	display: inline-block;
	box-sizing: border-box;

	&:before{
		display: inline-block;
		margin-right: 10px;
		font-size: 20px;
		line-height: inherit;
		vertical-align: text-top;
		content: "\\21c4";
		box-sizing: border-box;
	}
`;class h extends r.Component{constructor(e){super(e),this.state={showScrollHint:!1},this.setContainerRef=this.setContainerRef.bind(this),this.determineSize=(0,s.default)(this.determineSize.bind(this),100)}setContainerRef(e){if(!e)return null;this._container=e,this.determineSize(),window.addEventListener("resize",this.determineSize)}determineSize(){const e=this._container.offsetWidth;this.setState({showScrollHint:e<this.props.width})}componentWillUnmount(){window.removeEventListener("resize",this.determineSize)}render(){const{width:e,padding:t,children:n,className:i,id:o}=this.props,s=e-2*t;return r.default.createElement(r.default.Fragment,null,r.default.createElement(u,{id:o,className:i,widthValue:e,paddingValue:t,ref:this.setContainerRef},r.default.createElement(p,{widthValue:s},n)),this.state.showScrollHint&&r.default.createElement(c,null,r.default.createElement(f,null,(0,a.__)("Scroll to see the preview content.","wordpress-seo"))))}}t.default=h,h.propTypes={id:o.default.string,width:o.default.number.isRequired,padding:o.default.number,children:o.default.node.isRequired,className:o.default.string},h.defaultProps={id:"",padding:0,className:""}},97775:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r=p(n(99196)),i=p(n(85890)),o=p(n(98487)),s=p(n(12049)),a=n(81413),l=n(23695),d=n(37188),u=n(30888);function p(e){return e&&e.__esModule?e:{default:e}}const c=o.default.div`
	max-width: 600px;
	font-weight: normal;
	// Don't apply a bottom margin to avoid "jumpiness".
	margin: ${(0,l.getDirectionalStyle)("0 20px 0 25px","0 20px 0 15px")};
`,f=o.default.div`
	max-width: ${e=>e.panelMaxWidth};
`,h=(0,o.default)(a.Button)`
	min-width: 14px;
	min-height: 14px;
	width: 30px;
	height: 30px;
	border-radius: 50%;
	border: 1px solid transparent;
	box-shadow: none;
	display: block;
	margin: -44px -10px 10px 0;
	background-color: transparent;
	float: ${(0,l.getDirectionalStyle)("right","left")};
	padding: ${(0,l.getDirectionalStyle)("3px 0 0 6px","3px 0 0 5px")};

	&:hover {
		color: ${d.colors.$color_blue};
	}
	&:focus {
		border: 1px solid ${d.colors.$color_blue};
		outline: none;
		box-shadow: 0 0 3px ${(0,d.rgba)(d.colors.$color_blue_dark,.8)};

		svg {
			fill: ${d.colors.$color_blue};
			color: ${d.colors.$color_blue};
		}
	}
	&:active {
		box-shadow: none;
	}
`,g=(0,o.default)(a.SvgIcon)`
	&:hover {
		fill: ${d.colors.$color_blue};
	}
`;class m extends r.default.Component{constructor(e){super(e),this.state={isExpanded:!1},this.uniqueId=(0,s.default)("yoast-help-"),this.onButtonClick=this.onButtonClick.bind(this)}onButtonClick(){this.setState((e=>({isExpanded:!e.isExpanded})))}render(){const e=`${this.uniqueId}-panel`,{isExpanded:t}=this.state;return r.default.createElement(c,{className:this.props.className},r.default.createElement(h,{className:this.props.className+"__button",onClick:this.onButtonClick,"aria-expanded":t,"aria-controls":t?e:null,"aria-label":this.props.helpTextButtonLabel},r.default.createElement(g,{size:"16px",color:d.colors.$color_grey_text,icon:"question-circle"})),r.default.createElement(u.YoastSlideToggle,{isOpen:t},r.default.createElement(f,{id:e,className:this.props.className+"__panel",panelMaxWidth:this.props.panelMaxWidth},r.default.createElement(a.HelpText,null,this.props.helpText))))}}m.propTypes={className:i.default.string,helpTextButtonLabel:i.default.string.isRequired,panelMaxWidth:i.default.string,helpText:i.default.oneOfType([i.default.string,i.default.array])},m.defaultProps={className:"yoast-help",panelMaxWidth:null,helpText:""},t.default=m},72676:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r=function(e,t){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var n=u(t);if(n&&n.has(e))return n.get(e);var r={__proto__:null},i=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var o in e)if("default"!==o&&Object.prototype.hasOwnProperty.call(e,o)){var s=i?Object.getOwnPropertyDescriptor(e,o):null;s&&(s.get||s.set)?Object.defineProperty(r,o,s):r[o]=e[o]}return r.default=e,n&&n.set(e,r),r}(n(99196)),i=d(n(85890)),o=d(n(98487)),s=n(65736),a=n(92819),l=n(81413);function d(e){return e&&e.__esModule?e:{default:e}}function u(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,n=new WeakMap;return(u=function(e){return e?n:t})(e)}const p=o.default.span`
	color: #70757a;
	line-height: 1.7;
`;function c(e){const{shoppingData:t}=e,n=(0,s.sprintf)((0,s.__)("Rating: %s","wordpress-seo"),(0,a.round)(2*t.rating,1)+"/10"),i=(0,s.sprintf)((0,s.__)("%s reviews","wordpress-seo"),t.reviewCount);
/* Translators: %s expands to the actual rating, e.g. 8/10. */return r.default.createElement(p,null,t.reviewCount>0&&r.default.createElement(r.Fragment,null,r.default.createElement(l.StarRating,{rating:t.rating}),r.default.createElement("span",null," ",n," · "),r.default.createElement("span",null,i," · ")),t.price&&r.default.createElement(r.Fragment,null,r.default.createElement("span",{dangerouslySetInnerHTML:{__html:t.price}})),t.availability&&r.default.createElement("span",null,` · ${(0,a.capitalize)(t.availability)}`))}t.default=c,c.propTypes={shoppingData:i.default.shape({rating:i.default.number,reviewCount:i.default.number,availability:i.default.string,price:i.default.string}).isRequired}},98463:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r=d(n(99196)),i=d(n(85890)),o=d(n(98487)),s=n(65736),a=n(92819),l=n(81413);function d(e){return e&&e.__esModule?e:{default:e}}const u=o.default.div`
	display: flex;
	margin-top: -16px;
	line-height: 1.6;
`,p=o.default.div`
	flex: 1;
	max-width: 50%;
`,c=o.default.div`
	flex: 1;
	max-width: 25%;
`,f=o.default.div`
	color: #70757a;
`;function h(e){const{shoppingData:t}=e;return r.default.createElement(u,null,t.rating>0&&r.default.createElement(p,{className:"yoast-shopping-data-preview__column"},r.default.createElement("div",{className:"yoast-shopping-data-preview__upper"},(0,s.__)("Rating","wordpress-seo")),r.default.createElement(f,{className:"yoast-shopping-data-preview__lower"},r.default.createElement("span",null,(0,a.round)(2*t.rating,1),"/10 "),r.default.createElement(l.StarRating,{rating:t.rating}),r.default.createElement("span",null," (",t.reviewCount,")"))),t.price&&r.default.createElement(c,{className:"yoast-shopping-data-preview__column"},r.default.createElement("div",{className:"yoast-shopping-data-preview__upper"},(0,s.__)("Price","wordpress-seo")),r.default.createElement(f,{className:"yoast-shopping-data-preview__lower",dangerouslySetInnerHTML:{__html:t.price}})),t.availability&&r.default.createElement(c,{className:"yoast-shopping-data-preview__column"},r.default.createElement("div",{className:"yoast-shopping-data-preview__upper"},(0,s.__)("Availability","wordpress-seo")),r.default.createElement(f,{className:"yoast-shopping-data-preview__lower"},(0,a.capitalize)(t.availability))))}t.default=h,h.propTypes={shoppingData:i.default.shape({rating:i.default.number,reviewCount:i.default.number,availability:i.default.string,price:i.default.string}).isRequired}},64475:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r=function(e,t){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var n=E(t);if(n&&n.has(e))return n.get(e);var r={__proto__:null},i=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var o in e)if("default"!==o&&Object.prototype.hasOwnProperty.call(e,o)){var s=i?Object.getOwnPropertyDescriptor(e,o):null;s&&(s.get||s.set)?Object.defineProperty(r,o,s):r[o]=e[o]}return r.default=e,n&&n.set(e,r),r}(n(99196)),i=v(n(98487)),o=v(n(96746)),s=v(n(85890)),a=v(n(38550)),l=n(65736),d=n(37188),u=n(42982),p=n(23695),c=n(81413),f=v(n(12330)),h=v(n(72676)),g=v(n(98463)),m=n(99806);function v(e){return e&&e.__esModule?e:{default:e}}function E(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,n=new WeakMap;return(E=function(e){return e?n:t})(e)}function b(){return b=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},b.apply(this,arguments)}const{transliterate:y,createRegexFromArray:x,replaceDiacritics:w}=u.languageProcessing,_=600,S=(0,i.default)(f.default)`
	background-color: #fff;
	font-family: arial, sans-serif;
	box-sizing: border-box;
`,M=i.default.div`
	border-bottom: 1px hidden #fff;
	border-radius: 8px;
	box-shadow: 0 1px 6px rgba(32, 33, 36, 0.28);
	font-family: Arial, Roboto-Regular, HelveticaNeue, sans-serif;
	max-width: ${400}px;
	box-sizing: border-box;
	font-size: 14px;
`,O=i.default.div`
	cursor: pointer;
	position: relative;
`;function P(e,t,n){return(0,i.default)(e)`
		&::before {
			display: block;
			position: absolute;
			top: 0;
			${(0,p.getDirectionalStyle)("left","right")}: ${()=>n===m.MODE_DESKTOP?"-22px":"-40px"};
			width: 22px;
			height: 22px;
			background-image: url( ${(0,p.getDirectionalStyle)((0,d.angleRight)(t),(0,d.angleLeft)(t))} );
			background-size: 24px;
			background-repeat: no-repeat;
			background-position: center;
			content: "";
		}
	`}const C=i.default.div`
	color: ${e=>e.screenMode===m.MODE_DESKTOP?"#1a0dab":"#1558d6"};
	text-decoration: none;
	font-size: ${e=>(e.screenMode,m.MODE_DESKTOP,"20px")};
	line-height: ${e=>e.screenMode===m.MODE_DESKTOP?"1.3":"26px"};
	font-weight: normal;
	margin: 0;
	display: inline-block;
	overflow: hidden;
	max-width: ${_}px;
	vertical-align: top;
	text-overflow: ellipsis;
`,D=(0,i.default)(C)`
	max-width: ${_}px;
	vertical-align: top;
	text-overflow: ellipsis;
`,T=i.default.span`
	display: inline-block;
	max-width: ${e=>e.screenMode===m.MODE_DESKTOP?240:100}px;
	overflow: hidden;
	vertical-align: top;

	text-overflow: ellipsis;
	margin-left: 4px;
`,R=i.default.span`
	white-space: nowrap;
`,F=i.default.span`
	display: inline-block;
	max-height: 52px; // max two lines of text
	padding-top: 1px;
	vertical-align: top;
	overflow: hidden;
	text-overflow: ellipsis;
`,A=i.default.div`
	display: inline-block;
	cursor: pointer;
	position: relative;
	width: calc( 100% + 7px );
	white-space: nowrap;
	font-size: 14px;
	line-height: 16px;
	vertical-align: top;
`;A.displayName="BaseUrl";const j=(0,i.default)(A)`
	display: flex;
	align-items: center;
	overflow: hidden;
	justify-content: space-between;
	text-overflow: ellipsis;
	max-width: 100%;
	margin-bottom: 12px;
	padding-top: 1px;
	line-height: 20px;
	vertical-align: bottom;
`;j.displayName="BaseUrlOverflowContainer";const k=i.default.span`
	font-size: ${e=>e.screenMode===m.MODE_DESKTOP?"14px":"12px"};
	line-height: ${e=>e.screenMode===m.MODE_DESKTOP?"1.3":"20px"};
	color: ${e=>e.screenMode===m.MODE_DESKTOP?"#4d5156":"#3c4043"};
	flex-grow: 1;
`,N=i.default.span`
	color: ${e=>e.screenMode===m.MODE_DESKTOP?"#4d5156":"#70757a"};
`,I=i.default.div`
width: 28px;
height: 28px;
margin-right: 12px;
border-radius: 50px;
display: flex;
align-items: center;
justify-content: center;
background: #f1f3f4;
min-width: 28px;
`;j.displayName="SnippetPreview__BaseUrlOverflowContainer";const L=i.default.div`
	color: ${e=>(e.isDescriptionPlaceholder,"#4d5156")};
	cursor: pointer;
	position: relative;
	max-width: ${_}px;
	padding-top: ${e=>e.screenMode===m.MODE_DESKTOP?"0":"1px"};
	font-size: 14px;
	line-height: 1.58;
`,V=i.default.div`
	color: ${"#3c4043"};
	font-size: 14px;
	cursor: pointer;
	position: relative;
	line-height: 1.4;
	max-width: ${_}px;

	/* Clearing pseudo element to contain the floated image. */
	&:after {
		display: table;
		content: "";
		clear: both;
	}
`,U=i.default.div`
	float: right;
	width: 104px;
	height: 104px;
	margin: 4px 0 4px 16px;
	border-radius: 8px;
	overflow: hidden;
`,B=i.default.img`
	/* Higher specificity is necessary to make sure inherited CSS rules don't alter the image ratio. */
	&&& {
		display: block;
		width: 104px;
		height: 104px;
		object-fit: cover;
	}
`,$=i.default.div`
	padding: 12px 16px;

	&:first-child {
		margin-bottom: -16px;
	}
`,W=i.default.div`
	line-height: 18x; 
	font-size: 14px; 
	color: black;
	max-width: ${e=>e.screenMode===m.MODE_DESKTOP?"100%":"300px"};
	overflow: hidden;
`,z=i.default.div`
`,H=i.default.span`
	display: inline-block;
	height: 18px;
	line-height: 18px;
	padding-left: 8px;
	vertical-align:bottom;
`,K=i.default.span`
	color: ${e=>e.screenMode===m.MODE_DESKTOP?"#777":"#70757a"};
`,q=i.default.img`
	width: 18px;
	height: 18px;
	margin: 0 5px;
	vertical-align: middle;
`,G=i.default.div`
	background-size: 100% 100%;
	display: inline-block;
	height: 12px;
	width: 12px;
	margin-bottom: -1px;
	opacity: 0.46;
	margin-right: 6px;
	background-image: url( ${"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACQAAAAkCAQAAABLCVATAAABr0lEQVR4AbWWJYCUURhFD04Zi7hrLzgFd4nzV9x6wKHinmYb7g4zq71gIw2LWBnZ3Q8df/fh96Tn/t2HVIw4CVKk+fSFNCkSxInxW1pFkhLmoMRjVvFLmkEX5ocuZuBVPw5jv8hh+iEU5QEmuMK+prz7RN3dPMMEGQYzxpH/lGjzou5jgl7mAvOdZfcbF+jbm3MAbFZ7VX9SJnlL1D8UMyjLe+BrAYDb+jJUr59JrlNWRtcqX9GkrPCR4QBAf4qYJAkQoyQrbKKs8RiaEjEI0GvvQ1mLMC9xaBFFBaZS1TbMSwJSomg39erDF+TxpCCNOXjGQJTCvG6qn4ZPzkcxA61Tjhaf4KMj+6Q3XvW6Lopraa8IozRQxIi0a7NXorULc5JyHX/3F3q+0PsFYytVTaGgjz/AvCyiegE69IUsPxHNBMpa738i6tGWlzkAABjKe/+j9YeRHGVd9oWRnwe2ewDASp/L/UqoPQ5AmFeYZMavBP8dAJz0GWWDHQlzXApMdz4KYUfKICcxkKeOfGmQyrIPcgE9m+g/+kT812/Nr3+0kqzitxQjoKXh6xfor99nlEdFjyvH15gAAAAASUVORK5CYII="} );
`,Y=e=>{try{return decodeURI(e)}catch(t){return e}},Q=({screenMode:e})=>r.default.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:e===m.MODE_DESKTOP?"#4d5156":"#70757a",style:{width:"18px"}},r.default.createElement("path",{d:"M12 8c1.1 0 2-.9 2-2s-.9-2-2-2-2 .9-2 2 .9 2 2 2zm0 2c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2zm0 6c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2z"}));Q.propTypes={screenMode:s.default.string.isRequired};class X extends r.PureComponent{constructor(e){super(e),this.state={title:e.title,description:e.description,isDescriptionPlaceholder:!0},this.setTitleRef=this.setTitleRef.bind(this),this.setDescriptionRef=this.setDescriptionRef.bind(this)}setTitleRef(e){this._titleElement=e}setDescriptionRef(e){this._descriptionElement=e}hasOverflowedContent(e){return Math.abs(e.clientHeight-e.scrollHeight)>=2}fitTitle(){const e=this._titleElement;if(this.hasOverflowedContent(e)){let t=this.state.title;const n=e.clientWidth/3;t.length>n&&(t=t.substring(0,n));const r=this.dropLastWord(t);this.setState({title:r})}}dropLastWord(e){const t=e.split(" ");return t.pop(),t.join(" ")}getTitle(){return this.props.title!==this.state.title?this.state.title+" ...":this.props.title}getDescription(){return this.props.description?(0,a.default)(this.props.description,{length:156,separator:" ",omission:" ..."}):(0,l.__)("Please provide a meta description by editing the snippet below. If you don’t, Google will try to find a relevant part of your post to show in the search results.","wordpress-seo")}renderDate(){const e=this.props.mode===m.MODE_DESKTOP?"—":"－";return this.props.date&&r.default.createElement(K,{screenMode:this.props.mode},this.props.date," ",e," ")}addCaretStyles(e,t){const{mode:n,hoveredField:r,activeField:i}=this.props;return i===e?P(t,d.colors.$color_snippet_active,n):r===e?P(t,d.colors.$color_snippet_hover,n):t}getBreadcrumbs(e){const{breadcrumbs:t}=this.props;let n;try{n=new URL(e)}catch(t){return{hostname:e,breadcrumbs:""}}const r=Y(n.hostname);let i=t||n.pathname.split("/");return i=i.filter((e=>Boolean(e))).map((e=>Y(e))),{hostname:r,breadcrumbs:" › "+i.join(" › ")}}renderUrl(){const{url:e,onMouseUp:t,onMouseEnter:n,onMouseLeave:i,mode:o,faviconSrc:s,siteName:a}=this.props,d=o===m.MODE_MOBILE,{hostname:u,breadcrumbs:p}=this.getBreadcrumbs(e),f=this.addCaretStyles("url",A);return r.default.createElement(r.default.Fragment,null,r.default.createElement(c.ScreenReaderText,null,/* translators: Hidden accessibility text. */
(0,l.__)("Url preview","wordpress-seo")+":"),r.default.createElement(f,null,r.default.createElement(j,{onMouseUp:t.bind(null,"url"),onMouseEnter:n.bind(null,"url"),onMouseLeave:i.bind(null),screenMode:o},r.default.createElement(I,null,r.default.createElement(q,{src:s||"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAABs0lEQVR4AWL4//8/RRjO8Iucx+noO0MWUDo16FYABMGP6ZfUcRnWtm27jVPbtm3bttuH2t3eFPcY9pLz7NxiLjCyVd87pKnHyqXyxtCs8APd0rnyxiu4qSeA3QEDrAwBDrT1s1Rc/OrjLZwqVmOSu6+Lamcpp2KKMA9PH1BYXMe1mUP5qotvXTywsOEEYHXxrY+3cqk6TMkYpNr2FeoY3KIr0RPtn9wQ2unlA+GMkRw6+9TFw4YTwDUzx/JVvARj9KaedXRO8P5B1Du2S32smzqUrcKGEyA+uAgQjKX7zf0boWHGfn71jIKj2689gxp7OAGShNcBUmLMPVjZuiKcA2vuWHHDCQxMCz629kXAIU4ApY15QwggAFbfOP9DhgBJ+nWVJ1AZAfICAj1pAlY6hCADZnveQf7bQIwzVONGJonhLIlS9gr5mFg44Xd+4S3XHoGNPdJl1INIwKyEgHckEhgTe1bGiFY9GSFBYUwLh1IkiJUbY407E7syBSFxKTszEoiE/YdrgCEayDmtaJwCI9uu8TKMuZSVfSa4BpGgzvomBR/INhLGzrqDotp01ZR8pn/1L0JN9d9XNyx0AAAAAElFTkSuQmCC",alt:""})),r.default.createElement(k,{screenMode:o},r.default.createElement(W,{screenMode:o},a),r.default.createElement(N,{screenMode:o},u),r.default.createElement(T,{screenMode:o},p),!d&&r.default.createElement(H,null,r.default.createElement(Q,{screenMode:o}))),d&&r.default.createElement(Q,{screenMode:o}))))}componentDidUpdate(e){const t={};this.props.title!==e.title&&(t.title=this.props.title),this.props.description!==e.description&&(t.description=this.props.description),this.setState({...t,isDescriptionPlaceholder:!this.props.description}),this.props.mode===m.MODE_MOBILE&&(clearTimeout(this.fitTitleTimeout),this.fitTitleTimeout=setTimeout((()=>{this.fitTitle()}),10))}componentDidMount(){this.setState({isDescriptionPlaceholder:!this.props.description})}componentWillUnmount(){clearTimeout(this.fitTitleTimeout)}renderDescription(){const{wordsToHighlight:e,locale:t,onMouseUp:n,onMouseLeave:i,onMouseEnter:s,mode:a,mobileImageSrc:l}=this.props,d=this.renderDate(),u={isDescriptionPlaceholder:this.state.isDescriptionPlaceholder,onMouseUp:n.bind(null,"description"),onMouseEnter:s.bind(null,"description"),onMouseLeave:i.bind(null)};if(a===m.MODE_DESKTOP){const n=this.addCaretStyles("description",L);return r.default.createElement(n,b({},u,{ref:this.setDescriptionRef}),d,function(e,t,n,i){if(0===t.length)return n;let s=n;const a=[];t.forEach((function(t){a.push(t);const n=y(t,e);n!==t&&a.push(n)}));const l=x(a,!1,"",!1);return s=s.replace(l,(function(e){return`{{strong}}${e}{{/strong}}`})),(0,o.default)({mixedString:s,components:{strong:r.default.createElement("strong",null)}})}(t,e,this.getDescription()))}if(a===m.MODE_MOBILE){const e=this.addCaretStyles("description",V);return r.default.createElement(e,u,r.default.createElement(V,{isDescriptionPlaceholder:this.state.isDescriptionPlaceholder,ref:this.setDescriptionRef},l&&r.default.createElement(U,null,r.default.createElement(B,{src:l,alt:""})),d,this.getDescription()))}return null}renderProductData(e){const{mode:t,shoppingData:n}=this.props;if(0===Object.values(n).length)return null;const i={availability:n.availability||"",price:n.price?(0,p.decodeHTML)(n.price):"",rating:n.rating||0,reviewCount:n.reviewCount||0};return t===m.MODE_DESKTOP?r.default.createElement(e,{className:"yoast-shopping-data-preview--desktop"},r.default.createElement(c.ScreenReaderText,null,/* translators: Hidden accessibility text. */
(0,l.__)("Shopping data preview:","wordpress-seo")),r.default.createElement(h.default,{shoppingData:i})):t===m.MODE_MOBILE?r.default.createElement(e,{className:"yoast-shopping-data-preview--mobile"},r.default.createElement(c.ScreenReaderText,null,/* translators: Hidden accessibility text. */
(0,l.__)("Shopping data preview:","wordpress-seo")),r.default.createElement(g.default,{shoppingData:i})):null}render(){const{onMouseUp:e,onMouseLeave:t,onMouseEnter:n,mode:i,isAmp:o}=this.props,{PartContainer:s,Container:a,TitleUnbounded:d,SnippetTitle:u}=this.getPreparedComponents(i),p=i===m.MODE_DESKTOP,f=p||!o?null:r.default.createElement(G,null);return r.default.createElement("section",null,r.default.createElement(a,{id:"yoast-snippet-preview-container",width:p?640:null,padding:20},r.default.createElement(s,null,this.renderUrl(),r.default.createElement(c.ScreenReaderText,null,(0,l.__)("SEO title preview","wordpress-seo")+":"),r.default.createElement(u,{onMouseUp:e.bind(null,"title"),onMouseEnter:n.bind(null,"title"),onMouseLeave:t.bind(null)},r.default.createElement(D,{screenMode:i},r.default.createElement(d,{ref:this.setTitleRef},this.getTitle()))),f),r.default.createElement(s,null,r.default.createElement(c.ScreenReaderText,null,(0,l.__)("Meta description preview:","wordpress-seo")),this.renderDescription()),this.renderProductData(s)))}getPreparedComponents(e){return{PartContainer:e===m.MODE_DESKTOP?z:$,Container:e===m.MODE_DESKTOP?S:M,TitleUnbounded:e===m.MODE_DESKTOP?R:F,SnippetTitle:this.addCaretStyles("title",O)}}}t.default=X,X.propTypes={title:s.default.string.isRequired,url:s.default.string.isRequired,siteName:s.default.string.isRequired,description:s.default.string.isRequired,date:s.default.string,breadcrumbs:s.default.array,hoveredField:s.default.string,activeField:s.default.string,keyword:s.default.string,wordsToHighlight:s.default.array,locale:s.default.string,mode:s.default.oneOf(m.MODES),isAmp:s.default.bool,faviconSrc:s.default.string,mobileImageSrc:s.default.string,shoppingData:s.default.object,onMouseUp:s.default.func.isRequired,onHover:s.default.func,onMouseEnter:s.default.func,onMouseLeave:s.default.func},X.defaultProps={date:"",keyword:"",wordsToHighlight:[],breadcrumbs:null,locale:"en",hoveredField:"",activeField:"",mode:m.DEFAULT_MODE,isAmp:!1,faviconSrc:"",mobileImageSrc:"",shoppingData:{},onHover:()=>{},onMouseEnter:()=>{},onMouseLeave:()=>{}}},99806:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=t.MODE_MOBILE=t.MODE_DESKTOP=t.MODES=t.DEFAULT_MODE=void 0;const n=t.MODE_MOBILE="mobile",r=t.MODE_DESKTOP="desktop",i=t.MODES=[r,n],o=t.DEFAULT_MODE=n;t.default={MODE_MOBILE:n,MODE_DESKTOP:r,MODES:i,DEFAULT_MODE:o}},98141:(e,t,n)=>{"use strict";var r=n(64836);t.__esModule=!0,t.default=function(e,t){e.classList?e.classList.add(t):(0,i.default)(e,t)||("string"==typeof e.className?e.className=e.className+" "+t:e.setAttribute("class",(e.className&&e.className.baseVal||"")+" "+t))};var i=r(n(90404));e.exports=t.default},90404:(e,t)=>{"use strict";t.__esModule=!0,t.default=function(e,t){return e.classList?!!t&&e.classList.contains(t):-1!==(" "+(e.className.baseVal||e.className)+" ").indexOf(" "+t+" ")},e.exports=t.default},10602:e=>{"use strict";function t(e,t){return e.replace(new RegExp("(^|\\s)"+t+"(?:\\s|$)","g"),"$1").replace(/\s+/g," ").replace(/^\s*|\s*$/g,"")}e.exports=function(e,n){e.classList?e.classList.remove(n):"string"==typeof e.className?e.className=t(e.className,n):e.setAttribute("class",t(e.className&&e.className.baseVal||"",n))}},96746:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},i=a(n(99196)),o=a(n(49156)),s=a(n(76743));function a(e){return e&&e.__esModule?e:{default:e}}var l=void 0;function d(e,t){var n,s,a,u,p,c,f,h,g=[],m={};for(c=0;c<e.length;c++)if("string"!==(p=e[c]).type){if(!t.hasOwnProperty(p.value)||void 0===t[p.value])throw new Error("Invalid interpolation, missing component node: `"+p.value+"`");if("object"!==r(t[p.value]))throw new Error("Invalid interpolation, component node must be a ReactElement or null: `"+p.value+"`","\n> "+l);if("componentClose"===p.type)throw new Error("Missing opening component token: `"+p.value+"`");if("componentOpen"===p.type){n=t[p.value],a=c;break}g.push(t[p.value])}else g.push(p.value);return n&&(u=function(e,t){var n,r,i=t[e],o=0;for(r=e+1;r<t.length;r++)if((n=t[r]).value===i.value){if("componentOpen"===n.type){o++;continue}if("componentClose"===n.type){if(0===o)return r;o--}}throw new Error("Missing closing component token `"+i.value+"`")}(a,e),f=d(e.slice(a+1,u),t),s=i.default.cloneElement(n,{},f),g.push(s),u<e.length-1&&(h=d(e.slice(u+1),t),g=g.concat(h))),1===g.length?g[0]:(g.forEach((function(e,t){e&&(m["interpolation-child-"+t]=e)})),(0,o.default)(m))}t.default=function(e){var t=e.mixedString,n=e.components,i=e.throwErrors;if(l=t,!n)return t;if("object"!==(void 0===n?"undefined":r(n))){if(i)throw new Error("Interpolation Error: unable to process `"+t+"` because components is not an object");return t}var o=(0,s.default)(t);try{return d(o,n)}catch(e){if(i)throw new Error("Interpolation Error: unable to process `"+t+"` because of error `"+e.message+"`");return t}}},76743:e=>{"use strict";function t(e){return e.match(/^\{\{\//)?{type:"componentClose",value:e.replace(/\W/g,"")}:e.match(/\/\}\}$/)?{type:"componentSelfClosing",value:e.replace(/\W/g,"")}:e.match(/^\{\{/)?{type:"componentOpen",value:e.replace(/\W/g,"")}:{type:"string",value:e}}e.exports=function(e){return e.split(/(\{\{\/?\s*\w+\s*\/?\}\})/g).map(t)}},49156:(e,t,n)=>{"use strict";var r=n(99196),i="function"==typeof Symbol&&Symbol.for&&Symbol.for("react.element")||60103,o=n(47942),s=n(29179),a=n(70397),l=".",d=":",u="function"==typeof Symbol&&Symbol.iterator,p="@@iterator";function c(e,t){return e&&"object"==typeof e&&null!=e.key?(n=e.key,r={"=":"=0",":":"=2"},"$"+(""+n).replace(/[=:]/g,(function(e){return r[e]}))):t.toString(36);var n,r}function f(e,t,n,r){var o,a=typeof e;if("undefined"!==a&&"boolean"!==a||(e=null),null===e||"string"===a||"number"===a||"object"===a&&e.$$typeof===i)return n(r,e,""===t?l+c(e,0):t),1;var h=0,g=""===t?l:t+d;if(Array.isArray(e))for(var m=0;m<e.length;m++)h+=f(o=e[m],g+c(o,m),n,r);else{var v=function(e){var t=e&&(u&&e[u]||e[p]);if("function"==typeof t)return t}(e);if(v)for(var E,b=v.call(e),y=0;!(E=b.next()).done;)h+=f(o=E.value,g+c(o,y++),n,r);else if("object"===a){var x=""+e;s(!1,"Objects are not valid as a React child (found: %s).%s","[object Object]"===x?"object with keys {"+Object.keys(e).join(", ")+"}":x,"")}}return h}var h=/\/+/g;function g(e){return(""+e).replace(h,"$&/")}var m,v,E=b,b=function(e){var t=this;if(t.instancePool.length){var n=t.instancePool.pop();return t.call(n,e),n}return new t(e)};function y(e,t,n,r){this.result=e,this.keyPrefix=t,this.func=n,this.context=r,this.count=0}function x(e,t,n){var i,s,a=e.result,l=e.keyPrefix,d=e.func,u=e.context,p=d.call(u,t,e.count++);Array.isArray(p)?w(p,a,n,o.thatReturnsArgument):null!=p&&(r.isValidElement(p)&&(i=p,s=l+(!p.key||t&&t.key===p.key?"":g(p.key)+"/")+n,p=r.cloneElement(i,{key:s},void 0!==i.props?i.props.children:void 0)),a.push(p))}function w(e,t,n,r,i){var o="";null!=n&&(o=g(n)+"/");var s=y.getPooled(t,o,r,i);!function(e,t,n){null==e||f(e,"",t,n)}(e,x,s),y.release(s)}y.prototype.destructor=function(){this.result=null,this.keyPrefix=null,this.func=null,this.context=null,this.count=0},m=function(e,t,n,r){var i=this;if(i.instancePool.length){var o=i.instancePool.pop();return i.call(o,e,t,n,r),o}return new i(e,t,n,r)},(v=y).instancePool=[],v.getPooled=m||E,v.poolSize||(v.poolSize=10),v.release=function(e){var t=this;s(e instanceof t,"Trying to release an instance into a pool of a different type."),e.destructor(),t.instancePool.length<t.poolSize&&t.instancePool.push(e)},e.exports=function(e){if("object"!=typeof e||!e||Array.isArray(e))return a(!1,"React.addons.createFragment only accepts a single object. Got: %s",e),e;if(r.isValidElement(e))return a(!1,"React.addons.createFragment does not accept a ReactElement without a wrapper object."),e;s(1!==e.nodeType,"React.addons.createFragment(...): Encountered an invalid child; DOM elements are not valid children of React components.");var t=[];for(var n in e)w(e[n],t,n,o.thatReturnsArgument);return t}},47942:e=>{"use strict";function t(e){return function(){return e}}var n=function(){};n.thatReturns=t,n.thatReturnsFalse=t(!1),n.thatReturnsTrue=t(!0),n.thatReturnsNull=t(null),n.thatReturnsThis=function(){return this},n.thatReturnsArgument=function(e){return e},e.exports=n},29179:e=>{"use strict";e.exports=function(e,t,n,r,i,o,s,a){if(!e){var l;if(void 0===t)l=new Error("Minified exception occurred; use the non-minified dev environment for the full error message and additional helpful warnings.");else{var d=[n,r,i,o,s,a],u=0;(l=new Error(t.replace(/%s/g,(function(){return d[u++]})))).name="Invariant Violation"}throw l.framesToPop=1,l}}},70397:(e,t,n)=>{"use strict";var r=n(47942);e.exports=r},46871:(e,t,n)=>{"use strict";function r(){var e=this.constructor.getDerivedStateFromProps(this.props,this.state);null!=e&&this.setState(e)}function i(e){this.setState(function(t){var n=this.constructor.getDerivedStateFromProps(e,t);return null!=n?n:null}.bind(this))}function o(e,t){try{var n=this.props,r=this.state;this.props=e,this.state=t,this.__reactInternalSnapshotFlag=!0,this.__reactInternalSnapshot=this.getSnapshotBeforeUpdate(n,r)}finally{this.props=n,this.state=r}}function s(e){var t=e.prototype;if(!t||!t.isReactComponent)throw new Error("Can only polyfill class components");if("function"!=typeof e.getDerivedStateFromProps&&"function"!=typeof t.getSnapshotBeforeUpdate)return e;var n=null,s=null,a=null;if("function"==typeof t.componentWillMount?n="componentWillMount":"function"==typeof t.UNSAFE_componentWillMount&&(n="UNSAFE_componentWillMount"),"function"==typeof t.componentWillReceiveProps?s="componentWillReceiveProps":"function"==typeof t.UNSAFE_componentWillReceiveProps&&(s="UNSAFE_componentWillReceiveProps"),"function"==typeof t.componentWillUpdate?a="componentWillUpdate":"function"==typeof t.UNSAFE_componentWillUpdate&&(a="UNSAFE_componentWillUpdate"),null!==n||null!==s||null!==a){var l=e.displayName||e.name,d="function"==typeof e.getDerivedStateFromProps?"getDerivedStateFromProps()":"getSnapshotBeforeUpdate()";throw Error("Unsafe legacy lifecycles will not be called for components using new component APIs.\n\n"+l+" uses "+d+" but also contains the following legacy lifecycles:"+(null!==n?"\n  "+n:"")+(null!==s?"\n  "+s:"")+(null!==a?"\n  "+a:"")+"\n\nThe above lifecycles should be removed. Learn more about this warning here:\nhttps://fb.me/react-async-component-lifecycle-hooks")}if("function"==typeof e.getDerivedStateFromProps&&(t.componentWillMount=r,t.componentWillReceiveProps=i),"function"==typeof t.getSnapshotBeforeUpdate){if("function"!=typeof t.componentDidUpdate)throw new Error("Cannot polyfill getSnapshotBeforeUpdate() for components that do not define componentDidUpdate() on the prototype");t.componentWillUpdate=o;var u=t.componentDidUpdate;t.componentDidUpdate=function(e,t,n){var r=this.__reactInternalSnapshotFlag?this.__reactInternalSnapshot:n;u.call(this,e,t,r)}}return e}n.r(t),n.d(t,{polyfill:()=>s}),r.__suppressDeprecationWarning=!0,i.__suppressDeprecationWarning=!0,o.__suppressDeprecationWarning=!0},80129:(e,t,n)=>{"use strict";t.__esModule=!0,t.default=void 0,function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){var r=Object.defineProperty&&Object.getOwnPropertyDescriptor?Object.getOwnPropertyDescriptor(e,n):{};r.get||r.set?Object.defineProperty(t,n,r):t[n]=e[n]}t.default=e}(n(85890));var r=a(n(98141)),i=a(n(10602)),o=a(n(99196)),s=a(n(60644));function a(e){return e&&e.__esModule?e:{default:e}}function l(){return l=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},l.apply(this,arguments)}n(54726);var d=function(e,t){return e&&t&&t.split(" ").forEach((function(t){return(0,r.default)(e,t)}))},u=function(e,t){return e&&t&&t.split(" ").forEach((function(t){return(0,i.default)(e,t)}))},p=function(e){var t,n;function r(){for(var t,n=arguments.length,r=new Array(n),i=0;i<n;i++)r[i]=arguments[i];return(t=e.call.apply(e,[this].concat(r))||this).onEnter=function(e,n){var r=t.getClassNames(n?"appear":"enter").className;t.removeClasses(e,"exit"),d(e,r),t.props.onEnter&&t.props.onEnter(e,n)},t.onEntering=function(e,n){var r=t.getClassNames(n?"appear":"enter").activeClassName;t.reflowAndAddClass(e,r),t.props.onEntering&&t.props.onEntering(e,n)},t.onEntered=function(e,n){var r=t.getClassNames("appear").doneClassName,i=t.getClassNames("enter").doneClassName,o=n?r+" "+i:i;t.removeClasses(e,n?"appear":"enter"),d(e,o),t.props.onEntered&&t.props.onEntered(e,n)},t.onExit=function(e){var n=t.getClassNames("exit").className;t.removeClasses(e,"appear"),t.removeClasses(e,"enter"),d(e,n),t.props.onExit&&t.props.onExit(e)},t.onExiting=function(e){var n=t.getClassNames("exit").activeClassName;t.reflowAndAddClass(e,n),t.props.onExiting&&t.props.onExiting(e)},t.onExited=function(e){var n=t.getClassNames("exit").doneClassName;t.removeClasses(e,"exit"),d(e,n),t.props.onExited&&t.props.onExited(e)},t.getClassNames=function(e){var n=t.props.classNames,r="string"==typeof n,i=r?(r&&n?n+"-":"")+e:n[e];return{className:i,activeClassName:r?i+"-active":n[e+"Active"],doneClassName:r?i+"-done":n[e+"Done"]}},t}n=e,(t=r).prototype=Object.create(n.prototype),t.prototype.constructor=t,t.__proto__=n;var i=r.prototype;return i.removeClasses=function(e,t){var n=this.getClassNames(t),r=n.className,i=n.activeClassName,o=n.doneClassName;r&&u(e,r),i&&u(e,i),o&&u(e,o)},i.reflowAndAddClass=function(e,t){t&&(e&&e.scrollTop,d(e,t))},i.render=function(){var e=l({},this.props);return delete e.classNames,o.default.createElement(s.default,l({},e,{onEnter:this.onEnter,onEntered:this.onEntered,onEntering:this.onEntering,onExit:this.onExit,onExiting:this.onExiting,onExited:this.onExited}))},r}(o.default.Component);p.defaultProps={classNames:""},p.propTypes={};var c=p;t.default=c,e.exports=t.default},26093:(e,t,n)=>{"use strict";t.__esModule=!0,t.default=void 0,s(n(85890));var r=s(n(99196)),i=n(91850),o=s(n(92381));function s(e){return e&&e.__esModule?e:{default:e}}var a=function(e){var t,n;function s(){for(var t,n=arguments.length,r=new Array(n),i=0;i<n;i++)r[i]=arguments[i];return(t=e.call.apply(e,[this].concat(r))||this).handleEnter=function(){for(var e=arguments.length,n=new Array(e),r=0;r<e;r++)n[r]=arguments[r];return t.handleLifecycle("onEnter",0,n)},t.handleEntering=function(){for(var e=arguments.length,n=new Array(e),r=0;r<e;r++)n[r]=arguments[r];return t.handleLifecycle("onEntering",0,n)},t.handleEntered=function(){for(var e=arguments.length,n=new Array(e),r=0;r<e;r++)n[r]=arguments[r];return t.handleLifecycle("onEntered",0,n)},t.handleExit=function(){for(var e=arguments.length,n=new Array(e),r=0;r<e;r++)n[r]=arguments[r];return t.handleLifecycle("onExit",1,n)},t.handleExiting=function(){for(var e=arguments.length,n=new Array(e),r=0;r<e;r++)n[r]=arguments[r];return t.handleLifecycle("onExiting",1,n)},t.handleExited=function(){for(var e=arguments.length,n=new Array(e),r=0;r<e;r++)n[r]=arguments[r];return t.handleLifecycle("onExited",1,n)},t}n=e,(t=s).prototype=Object.create(n.prototype),t.prototype.constructor=t,t.__proto__=n;var a=s.prototype;return a.handleLifecycle=function(e,t,n){var o,s=this.props.children,a=r.default.Children.toArray(s)[t];a.props[e]&&(o=a.props)[e].apply(o,n),this.props[e]&&this.props[e]((0,i.findDOMNode)(this))},a.render=function(){var e=this.props,t=e.children,n=e.in,i=function(e,t){if(null==e)return{};var n,r,i={},o=Object.keys(e);for(r=0;r<o.length;r++)n=o[r],t.indexOf(n)>=0||(i[n]=e[n]);return i}(e,["children","in"]),s=r.default.Children.toArray(t),a=s[0],l=s[1];return delete i.onEnter,delete i.onEntering,delete i.onEntered,delete i.onExit,delete i.onExiting,delete i.onExited,r.default.createElement(o.default,i,n?r.default.cloneElement(a,{key:"first",onEnter:this.handleEnter,onEntering:this.handleEntering,onEntered:this.handleEntered}):r.default.cloneElement(l,{key:"second",onEnter:this.handleExit,onEntering:this.handleExiting,onEntered:this.handleExited}))},s}(r.default.Component);a.propTypes={};var l=a;t.default=l,e.exports=t.default},60644:(e,t,n)=>{"use strict";t.__esModule=!0,t.default=t.EXITING=t.ENTERED=t.ENTERING=t.EXITED=t.UNMOUNTED=void 0;var r=function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){var r=Object.defineProperty&&Object.getOwnPropertyDescriptor?Object.getOwnPropertyDescriptor(e,n):{};r.get||r.set?Object.defineProperty(t,n,r):t[n]=e[n]}return t.default=e,t}(n(85890)),i=a(n(99196)),o=a(n(91850)),s=n(46871);function a(e){return e&&e.__esModule?e:{default:e}}n(54726);var l="unmounted";t.UNMOUNTED=l;var d="exited";t.EXITED=d;var u="entering";t.ENTERING=u;var p="entered";t.ENTERED=p;var c="exiting";t.EXITING=c;var f=function(e){var t,n;function r(t,n){var r;r=e.call(this,t,n)||this;var i,o=n.transitionGroup,s=o&&!o.isMounting?t.enter:t.appear;return r.appearStatus=null,t.in?s?(i=d,r.appearStatus=u):i=p:i=t.unmountOnExit||t.mountOnEnter?l:d,r.state={status:i},r.nextCallback=null,r}n=e,(t=r).prototype=Object.create(n.prototype),t.prototype.constructor=t,t.__proto__=n;var s=r.prototype;return s.getChildContext=function(){return{transitionGroup:null}},r.getDerivedStateFromProps=function(e,t){return e.in&&t.status===l?{status:d}:null},s.componentDidMount=function(){this.updateStatus(!0,this.appearStatus)},s.componentDidUpdate=function(e){var t=null;if(e!==this.props){var n=this.state.status;this.props.in?n!==u&&n!==p&&(t=u):n!==u&&n!==p||(t=c)}this.updateStatus(!1,t)},s.componentWillUnmount=function(){this.cancelNextCallback()},s.getTimeouts=function(){var e,t,n,r=this.props.timeout;return e=t=n=r,null!=r&&"number"!=typeof r&&(e=r.exit,t=r.enter,n=void 0!==r.appear?r.appear:t),{exit:e,enter:t,appear:n}},s.updateStatus=function(e,t){if(void 0===e&&(e=!1),null!==t){this.cancelNextCallback();var n=o.default.findDOMNode(this);t===u?this.performEnter(n,e):this.performExit(n)}else this.props.unmountOnExit&&this.state.status===d&&this.setState({status:l})},s.performEnter=function(e,t){var n=this,r=this.props.enter,i=this.context.transitionGroup?this.context.transitionGroup.isMounting:t,o=this.getTimeouts(),s=i?o.appear:o.enter;t||r?(this.props.onEnter(e,i),this.safeSetState({status:u},(function(){n.props.onEntering(e,i),n.onTransitionEnd(e,s,(function(){n.safeSetState({status:p},(function(){n.props.onEntered(e,i)}))}))}))):this.safeSetState({status:p},(function(){n.props.onEntered(e)}))},s.performExit=function(e){var t=this,n=this.props.exit,r=this.getTimeouts();n?(this.props.onExit(e),this.safeSetState({status:c},(function(){t.props.onExiting(e),t.onTransitionEnd(e,r.exit,(function(){t.safeSetState({status:d},(function(){t.props.onExited(e)}))}))}))):this.safeSetState({status:d},(function(){t.props.onExited(e)}))},s.cancelNextCallback=function(){null!==this.nextCallback&&(this.nextCallback.cancel(),this.nextCallback=null)},s.safeSetState=function(e,t){t=this.setNextCallback(t),this.setState(e,t)},s.setNextCallback=function(e){var t=this,n=!0;return this.nextCallback=function(r){n&&(n=!1,t.nextCallback=null,e(r))},this.nextCallback.cancel=function(){n=!1},this.nextCallback},s.onTransitionEnd=function(e,t,n){this.setNextCallback(n);var r=null==t&&!this.props.addEndListener;e&&!r?(this.props.addEndListener&&this.props.addEndListener(e,this.nextCallback),null!=t&&setTimeout(this.nextCallback,t)):setTimeout(this.nextCallback,0)},s.render=function(){var e=this.state.status;if(e===l)return null;var t=this.props,n=t.children,r=function(e,t){if(null==e)return{};var n,r,i={},o=Object.keys(e);for(r=0;r<o.length;r++)n=o[r],t.indexOf(n)>=0||(i[n]=e[n]);return i}(t,["children"]);if(delete r.in,delete r.mountOnEnter,delete r.unmountOnExit,delete r.appear,delete r.enter,delete r.exit,delete r.timeout,delete r.addEndListener,delete r.onEnter,delete r.onEntering,delete r.onEntered,delete r.onExit,delete r.onExiting,delete r.onExited,"function"==typeof n)return n(e,r);var o=i.default.Children.only(n);return i.default.cloneElement(o,r)},r}(i.default.Component);function h(){}f.contextTypes={transitionGroup:r.object},f.childContextTypes={transitionGroup:function(){}},f.propTypes={},f.defaultProps={in:!1,mountOnEnter:!1,unmountOnExit:!1,appear:!1,enter:!0,exit:!0,onEnter:h,onEntering:h,onEntered:h,onExit:h,onExiting:h,onExited:h},f.UNMOUNTED=0,f.EXITED=1,f.ENTERING=2,f.ENTERED=3,f.EXITING=4;var g=(0,s.polyfill)(f);t.default=g},92381:(e,t,n)=>{"use strict";t.__esModule=!0,t.default=void 0;var r=a(n(85890)),i=a(n(99196)),o=n(46871),s=n(40537);function a(e){return e&&e.__esModule?e:{default:e}}function l(){return l=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},l.apply(this,arguments)}function d(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}var u=Object.values||function(e){return Object.keys(e).map((function(t){return e[t]}))},p=function(e){var t,n;function r(t,n){var r,i=(r=e.call(this,t,n)||this).handleExited.bind(d(d(r)));return r.state={handleExited:i,firstRender:!0},r}n=e,(t=r).prototype=Object.create(n.prototype),t.prototype.constructor=t,t.__proto__=n;var o=r.prototype;return o.getChildContext=function(){return{transitionGroup:{isMounting:!this.appeared}}},o.componentDidMount=function(){this.appeared=!0,this.mounted=!0},o.componentWillUnmount=function(){this.mounted=!1},r.getDerivedStateFromProps=function(e,t){var n=t.children,r=t.handleExited;return{children:t.firstRender?(0,s.getInitialChildMapping)(e,r):(0,s.getNextChildMapping)(e,n,r),firstRender:!1}},o.handleExited=function(e,t){var n=(0,s.getChildMapping)(this.props.children);e.key in n||(e.props.onExited&&e.props.onExited(t),this.mounted&&this.setState((function(t){var n=l({},t.children);return delete n[e.key],{children:n}})))},o.render=function(){var e=this.props,t=e.component,n=e.childFactory,r=function(e,t){if(null==e)return{};var n,r,i={},o=Object.keys(e);for(r=0;r<o.length;r++)n=o[r],t.indexOf(n)>=0||(i[n]=e[n]);return i}(e,["component","childFactory"]),o=u(this.state.children).map(n);return delete r.appear,delete r.enter,delete r.exit,null===t?o:i.default.createElement(t,r,o)},r}(i.default.Component);p.childContextTypes={transitionGroup:r.default.object.isRequired},p.propTypes={},p.defaultProps={component:"div",childFactory:function(e){return e}};var c=(0,o.polyfill)(p);t.default=c,e.exports=t.default},64317:(e,t,n)=>{"use strict";var r=a(n(80129)),i=a(n(26093)),o=a(n(92381)),s=a(n(60644));function a(e){return e&&e.__esModule?e:{default:e}}e.exports={Transition:s.default,TransitionGroup:o.default,ReplaceTransition:i.default,CSSTransition:r.default}},40537:(e,t,n)=>{"use strict";t.__esModule=!0,t.getChildMapping=i,t.mergeChildMappings=o,t.getInitialChildMapping=function(e,t){return i(e.children,(function(n){return(0,r.cloneElement)(n,{onExited:t.bind(null,n),in:!0,appear:s(n,"appear",e),enter:s(n,"enter",e),exit:s(n,"exit",e)})}))},t.getNextChildMapping=function(e,t,n){var a=i(e.children),l=o(t,a);return Object.keys(l).forEach((function(i){var o=l[i];if((0,r.isValidElement)(o)){var d=i in t,u=i in a,p=t[i],c=(0,r.isValidElement)(p)&&!p.props.in;!u||d&&!c?u||!d||c?u&&d&&(0,r.isValidElement)(p)&&(l[i]=(0,r.cloneElement)(o,{onExited:n.bind(null,o),in:p.props.in,exit:s(o,"exit",e),enter:s(o,"enter",e)})):l[i]=(0,r.cloneElement)(o,{in:!1}):l[i]=(0,r.cloneElement)(o,{onExited:n.bind(null,o),in:!0,exit:s(o,"exit",e),enter:s(o,"enter",e)})}})),l};var r=n(99196);function i(e,t){var n=Object.create(null);return e&&r.Children.map(e,(function(e){return e})).forEach((function(e){n[e.key]=function(e){return t&&(0,r.isValidElement)(e)?t(e):e}(e)})),n}function o(e,t){function n(n){return n in t?t[n]:e[n]}e=e||{},t=t||{};var r,i=Object.create(null),o=[];for(var s in e)s in t?o.length&&(i[s]=o,o=[]):o.push(s);var a={};for(var l in t){if(i[l])for(r=0;r<i[l].length;r++){var d=i[l][r];a[i[l][r]]=n(d)}a[l]=n(l)}for(r=0;r<o.length;r++)a[o[r]]=n(o[r]);return a}function s(e,t,n){return null!=n[t]?n[t]:e.props[t]}},54726:(e,t,n)=>{"use strict";var r;t.__esModule=!0,t.classNamesShape=t.timeoutsShape=void 0,(r=n(85890))&&r.__esModule,t.timeoutsShape=null,t.classNamesShape=null},99196:e=>{"use strict";e.exports=window.React},91850:e=>{"use strict";e.exports=window.ReactDOM},92819:e=>{"use strict";e.exports=window.lodash},25853:e=>{"use strict";e.exports=window.lodash.debounce},38550:e=>{"use strict";e.exports=window.lodash.truncate},12049:e=>{"use strict";e.exports=window.lodash.uniqueId},65736:e=>{"use strict";e.exports=window.wp.i18n},42982:e=>{"use strict";e.exports=window.yoast.analysis},81413:e=>{"use strict";e.exports=window.yoast.componentsNew},23695:e=>{"use strict";e.exports=window.yoast.helpers},85890:e=>{"use strict";e.exports=window.yoast.propTypes},10224:e=>{"use strict";e.exports=window.yoast.replacementVariableEditor},37188:e=>{"use strict";e.exports=window.yoast.styleGuide},98487:e=>{"use strict";e.exports=window.yoast.styledComponents},64836:e=>{e.exports=function(e){return e&&e.__esModule?e:{default:e}},e.exports.__esModule=!0,e.exports.default=e.exports}},t={};function n(r){var i=t[r];if(void 0!==i)return i.exports;var o=t[r]={exports:{}};return e[r](o,o.exports,n),o.exports}n.d=(e,t)=>{for(var r in t)n.o(t,r)&&!n.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},n.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),n.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var r={};(()=>{"use strict";var e=r;Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"FixedWidthContainer",{enumerable:!0,get:function(){return t.default}}),Object.defineProperty(e,"HelpTextWrapper",{enumerable:!0,get:function(){return i.default}}),Object.defineProperty(e,"SnippetEditor",{enumerable:!0,get:function(){return s.default}}),Object.defineProperty(e,"SnippetPreview",{enumerable:!0,get:function(){return o.default}}),Object.defineProperty(e,"getDescriptionProgress",{enumerable:!0,get:function(){return l.getDescriptionProgress}}),Object.defineProperty(e,"getTitleProgress",{enumerable:!0,get:function(){return l.getTitleProgress}}),Object.defineProperty(e,"lengthProgressShape",{enumerable:!0,get:function(){return a.lengthProgressShape}});var t=d(n(12330)),i=d(n(97775)),o=d(n(64475)),s=d(n(24861)),a=n(95157),l=n(76990);function d(e){return e&&e.__esModule?e:{default:e}}})(),(window.yoast=window.yoast||{}).searchMetadataPreviews=r})();