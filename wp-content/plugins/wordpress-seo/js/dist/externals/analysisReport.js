(()=>{"use strict";var e={n:t=>{var s=t&&t.__esModule?()=>t.default:()=>t;return e.d(s,{a:s}),s},d:(t,s)=>{for(var o in s)e.o(s,o)&&!e.o(t,o)&&Object.defineProperty(t,o,{enumerable:!0,get:s[o]})},o:(e,t)=>Object.prototype.hasOwnProperty.call(e,t),r:e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})}},t={};e.r(t),e.d(t,{AnalysisList:()=>w,AnalysisResult:()=>f,ContentAnalysis:()=>M,SiteSEOReport:()=>P,renderRatingToColor:()=>x});const s=window.React;var o=e.n(s);const r=window.wp.i18n,n=window.yoast.styledComponents;var a=e.n(n);const i=window.yoast.propTypes;var l=e.n(i);const u=window.lodash.noop;var d=e.n(u);const g=window.yoast.styleGuide,m=window.lodash,p=window.yoast.componentsNew,c=window.yoast.helpers,{stripTagsFromHtmlString:h}=c.strings,k=["a","b","strong","em","i"],B=a().li`
	// This is the height of the IconButtonToggle.
	min-height: 24px;
	padding: 0;
	display: flex;
	align-items: flex-start;
	position: relative;
`,b=a()(p.SvgIcon)`
	margin: 3px 11px 0 0; // icon 13 + 11 right margin = 24 for the 8px grid.
`,C=a().p`
	margin: 0 16px 0 0;
	flex: 1 1 auto;
	color: ${e=>e.suppressedText?"rgba(30,30,30,0.5)":"inherit"};
`,y=({ariaLabel:e,id:t,className:o,status:r,onClick:n,isPressed:a})=>(0,s.createElement)(p.IconButtonToggle,{marksButtonStatus:r,className:o,onClick:n,id:t,icon:"eye",pressed:a,ariaLabel:e}),R=({markButtonFactory:e,...t})=>{const[o,r]=(0,s.useState)(!1),n=(0,s.useCallback)((()=>r(!1)),[]),a=(0,s.useCallback)((()=>r(!0)),[]);e=e||y;const{id:i,marker:l,hasMarksButton:u}=t;let d=null;return function(e){return!e.hasMarksButton||"hidden"===e.marksButtonStatus}(t)||(d=e({onClick:t.shouldUpsellHighlighting?a:t.onButtonClickMarks,status:t.marksButtonStatus,className:t.marksButtonClassName,id:t.buttonIdMarks,isPressed:t.pressed,ariaLabel:t.ariaLabelMarks})),(0,s.useEffect)((()=>{t.onResultChange(i,l,u)}),[i,l,u]),(0,s.createElement)(B,null,(0,s.createElement)(b,{icon:"circle",color:t.bulletColor,size:"13px"}),(0,s.createElement)(C,{suppressedText:t.suppressedText},t.hasBetaBadgeLabel&&(0,s.createElement)(p.BetaBadge,null),(0,s.createElement)("span",{dangerouslySetInnerHTML:{__html:h(t.text,k)}})),d,t.renderHighlightingUpsell(o,n),t.hasEditButton&&t.isPremium&&(0,s.createElement)(p.IconCTAEditButton,{className:t.editButtonClassName,onClick:t.onButtonClickEdit,id:t.buttonIdEdit,icon:"edit",ariaLabel:t.ariaLabelEdit}))};R.propTypes={text:l().string.isRequired,suppressedText:l().bool,bulletColor:l().string.isRequired,hasMarksButton:l().bool.isRequired,hasEditButton:l().bool,buttonIdMarks:l().string.isRequired,buttonIdEdit:l().string,pressed:l().bool.isRequired,ariaLabelMarks:l().string.isRequired,ariaLabelEdit:l().string,onButtonClickMarks:l().func.isRequired,onButtonClickEdit:l().func,marksButtonStatus:l().string,marksButtonClassName:l().string,markButtonFactory:l().func,editButtonClassName:l().string,hasBetaBadgeLabel:l().bool,isPremium:l().bool,onResultChange:l().func,id:l().string,marker:l().oneOfType([l().func,l().array]),shouldUpsellHighlighting:l().bool,renderHighlightingUpsell:l().func},R.defaultProps={suppressedText:!1,marksButtonStatus:"enabled",marksButtonClassName:"",editButtonClassName:"",hasBetaBadgeLabel:!1,hasEditButton:!1,buttonIdEdit:"",ariaLabelEdit:"",onButtonClickEdit:m.noop,isPremium:!1,onResultChange:m.noop,id:"",marker:m.noop,shouldUpsellHighlighting:!1,renderHighlightingUpsell:m.noop};const f=R,E=a().ul`
	margin: 8px 0;
	padding: 0;
	list-style: none;
`;function x(e){switch(e){case"good":return g.colors.$color_good;case"OK":return g.colors.$color_ok;case"bad":return g.colors.$color_bad;default:return g.colors.$color_score_icon}}function w(e){return(0,s.createElement)(E,{role:"list"},e.results.map((t=>{const o=x(t.rating),n=t.markerId===e.marksButtonActivatedResult,a=t.id+"Mark",i=t.id+"Edit";let l="";l="disabled"===e.marksButtonStatus?(0,r.__)("Marks are disabled in current view","wordpress-seo"):n?(0,r.__)("Remove highlight from the text","wordpress-seo"):(0,r.__)("Highlight this result in the text","wordpress-seo");const u=t.editFieldName,d=""===u?"":(0,r.sprintf)(
/* Translators: %1$s refers to the name of the field that should be edited (keyphrase, meta description,
       slug or SEO title). */
(0,r.__)("Edit your %1$s","wordpress-seo"),u);return(0,s.createElement)(f,{key:t.id,id:t.id,text:t.text,marker:t.marker,bulletColor:o,hasMarksButton:t.hasMarks,hasEditButton:t.hasJumps,ariaLabelMarks:l,ariaLabelEdit:d,pressed:n,suppressedText:"upsell"===t.rating,buttonIdMarks:a,buttonIdEdit:i,onButtonClickMarks:()=>e.onMarksButtonClick(t.id,t.marker),onButtonClickEdit:()=>e.onEditButtonClick(t.id),marksButtonClassName:e.marksButtonClassName,editButtonClassName:e.editButtonClassName,marksButtonStatus:e.marksButtonStatus,hasBetaBadgeLabel:t.hasBetaBadge,isPremium:e.isPremium,onResultChange:e.onResultChange,markButtonFactory:e.markButtonFactory,shouldUpsellHighlighting:e.shouldUpsellHighlighting,renderHighlightingUpsell:e.renderHighlightingUpsell})})))}w.propTypes={results:l().array.isRequired,marksButtonActivatedResult:l().string,marksButtonStatus:l().string,marksButtonClassName:l().string,editButtonClassName:l().string,markButtonFactory:l().func,onMarksButtonClick:l().func,onEditButtonClick:l().func,isPremium:l().bool,onResultChange:l().func,shouldUpsellHighlighting:l().bool,renderHighlightingUpsell:l().func},w.defaultProps={marksButtonActivatedResult:"",marksButtonStatus:"enabled",marksButtonClassName:"",editButtonClassName:"",onMarksButtonClick:d(),onEditButtonClick:d(),isPremium:!1,onResultChange:d(),shouldUpsellHighlighting:!1,renderHighlightingUpsell:d()};const v=a().div`
	width: 100%;
	background-color: white;
	border-bottom: 1px solid transparent; // Avoid parent and child margin collapsing.
`,N=a()(p.Collapsible)`
	margin-bottom: 8px;

	button:first-child svg {
		margin: -2px 8px 0 -2px; // Compensate icon size set to 18px.
	}

	${p.StyledIconsButton} {
		padding: 8px 0;
		color: ${g.colors.$color_blue}
	}
`;class _ extends o().Component{renderCollapsible(e,t,o){return(0,s.createElement)(N,{initialIsOpen:!0,title:`${e} (${o.length})`,prefixIcon:{icon:"angle-up",color:g.colors.$color_grey_dark,size:"18px"},prefixIconCollapsed:{icon:"angle-down",color:g.colors.$color_grey_dark,size:"18px"},suffixIcon:null,suffixIconCollapsed:null,headingProps:{level:t,fontSize:"13px",fontWeight:"500",color:"#1e1e1e"}},(0,s.createElement)(w,{results:o,marksButtonActivatedResult:this.props.activeMarker,marksButtonStatus:this.props.marksButtonStatus,marksButtonClassName:this.props.marksButtonClassName,editButtonClassName:this.props.editButtonClassName,markButtonFactory:this.props.markButtonFactory,onMarksButtonClick:this.props.onMarkButtonClick,onEditButtonClick:this.props.onEditButtonClick,isPremium:this.props.isPremium,onResultChange:this.props.onResultChange,shouldUpsellHighlighting:this.props.shouldUpsellHighlighting,renderHighlightingUpsell:this.props.renderHighlightingUpsell}))}render(){const{problemsResults:e,improvementsResults:t,goodResults:o,considerationsResults:n,errorsResults:a,upsellResults:i,headingLevel:l,resultCategoryLabels:u}=this.props,d=a.length,g=e.length,m=t.length,p=n.length,c=o.length,h=i.length,k={errors:(0,r.__)("Errors","wordpress-seo"),problems:(0,r.__)("Problems","wordpress-seo"),improvements:(0,r.__)("Improvements","wordpress-seo"),considerations:(0,r.__)("Considerations","wordpress-seo"),goodResults:(0,r.__)("Good results","wordpress-seo")},B=Object.assign(k,u);return(0,s.createElement)(v,null,d>0&&this.renderCollapsible(B.errors,l,a),g+h>0&&this.renderCollapsible(B.problems,l,[...i,...e]),m>0&&this.renderCollapsible(B.improvements,l,t),p>0&&this.renderCollapsible(B.considerations,l,n),c>0&&this.renderCollapsible(B.goodResults,l,o))}}_.propTypes={onMarkButtonClick:l().func,onEditButtonClick:l().func,problemsResults:l().array,improvementsResults:l().array,goodResults:l().array,considerationsResults:l().array,errorsResults:l().array,upsellResults:l().array,headingLevel:l().number,marksButtonStatus:l().string,marksButtonClassName:l().string,markButtonFactory:l().func,editButtonClassName:l().string,activeMarker:l().string,isPremium:l().bool,resultCategoryLabels:l().shape({errors:l().string,problems:l().string,improvements:l().string,considerations:l().string,goodResults:l().string}),onResultChange:l().func,shouldUpsellHighlighting:l().bool,renderHighlightingUpsell:l().func},_.defaultProps={onMarkButtonClick:()=>{},onEditButtonClick:()=>{},problemsResults:[],improvementsResults:[],goodResults:[],considerationsResults:[],errorsResults:[],upsellResults:[],headingLevel:4,marksButtonStatus:"enabled",marksButtonClassName:"",markButtonFactory:null,editButtonClassName:"",activeMarker:"",isPremium:!1,resultCategoryLabels:{},onResultChange:()=>{},shouldUpsellHighlighting:!1,renderHighlightingUpsell:()=>{}};const M=_,S=a().div`
`,H=a().p`
	font-size: 14px;
`,I=e=>(0,s.createElement)(S,{className:e.className},(0,s.createElement)(H,{className:`${e.className}__text`},e.seoAssessmentText),(0,s.createElement)(p.StackedProgressBar,{className:"progress",items:e.seoAssessmentItems,barHeight:e.barHeight}),(0,s.createElement)(p.ScoreAssessments,{className:"assessments",items:e.seoAssessmentItems}));I.propTypes={className:l().string,seoAssessmentText:l().string,seoAssessmentItems:l().arrayOf(l().shape({html:l().string.isRequired,value:l().number.isRequired,color:l().string.isRequired})),barHeight:l().string},I.defaultProps={className:"seo-assessment",seoAssessmentText:"SEO Assessment",seoAssessmentItems:null,barHeight:"24px"};const P=I;(window.yoast=window.yoast||{}).analysisReport=t})();