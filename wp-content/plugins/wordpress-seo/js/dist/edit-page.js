(()=>{"use strict";var t={n:a=>{var o=a&&a.__esModule?()=>a.default:()=>a;return t.d(o,{a:o}),o},d:(a,o)=>{for(var e in o)t.o(o,e)&&!t.o(a,e)&&Object.defineProperty(a,e,{enumerable:!0,get:o[e]})},o:(t,a)=>Object.prototype.hasOwnProperty.call(t,a)};const a=window.jQuery;var o;(o=t.n(a)())(".yoast-column-header-has-tooltip").each((function(){o(this).closest("th").find("a").addClass("yoast-tooltip yoast-tooltip-alt yoast-tooltip-n yoast-tooltip-multiline").attr("data-label",o(this).data("tooltip-text")).attr("aria-label",o(this).text())}))})();