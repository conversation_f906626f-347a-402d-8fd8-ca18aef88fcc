(()=>{var e={6746:(e,t,s)=>{"use strict";var r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},a=i(s(9196)),n=i(s(9156)),o=i(s(6743));function i(e){return e&&e.__esModule?e:{default:e}}var l=void 0;function c(e,t){var s,o,i,d,p,u,m,h,g=[],y={};for(u=0;u<e.length;u++)if("string"!==(p=e[u]).type){if(!t.hasOwnProperty(p.value)||void 0===t[p.value])throw new Error("Invalid interpolation, missing component node: `"+p.value+"`");if("object"!==r(t[p.value]))throw new Error("Invalid interpolation, component node must be a ReactElement or null: `"+p.value+"`","\n> "+l);if("componentClose"===p.type)throw new Error("Missing opening component token: `"+p.value+"`");if("componentOpen"===p.type){s=t[p.value],i=u;break}g.push(t[p.value])}else g.push(p.value);return s&&(d=function(e,t){var s,r,a=t[e],n=0;for(r=e+1;r<t.length;r++)if((s=t[r]).value===a.value){if("componentOpen"===s.type){n++;continue}if("componentClose"===s.type){if(0===n)return r;n--}}throw new Error("Missing closing component token `"+a.value+"`")}(i,e),m=c(e.slice(i+1,d),t),o=a.default.cloneElement(s,{},m),g.push(o),d<e.length-1&&(h=c(e.slice(d+1),t),g=g.concat(h))),1===g.length?g[0]:(g.forEach((function(e,t){e&&(y["interpolation-child-"+t]=e)})),(0,n.default)(y))}t.Z=function(e){var t=e.mixedString,s=e.components,a=e.throwErrors;if(l=t,!s)return t;if("object"!==(void 0===s?"undefined":r(s))){if(a)throw new Error("Interpolation Error: unable to process `"+t+"` because components is not an object");return t}var n=(0,o.default)(t);try{return c(n,s)}catch(e){if(a)throw new Error("Interpolation Error: unable to process `"+t+"` because of error `"+e.message+"`");return t}}},6743:e=>{"use strict";function t(e){return e.match(/^\{\{\//)?{type:"componentClose",value:e.replace(/\W/g,"")}:e.match(/\/\}\}$/)?{type:"componentSelfClosing",value:e.replace(/\W/g,"")}:e.match(/^\{\{/)?{type:"componentOpen",value:e.replace(/\W/g,"")}:{type:"string",value:e}}e.exports=function(e){return e.split(/(\{\{\/?\s*\w+\s*\/?\}\})/g).map(t)}},9156:(e,t,s)=>{"use strict";var r=s(9196),a="function"==typeof Symbol&&Symbol.for&&Symbol.for("react.element")||60103,n=s(7942),o=s(9179),i=s(397),l=".",c=":",d="function"==typeof Symbol&&Symbol.iterator,p="@@iterator";function u(e,t){return e&&"object"==typeof e&&null!=e.key?(s=e.key,r={"=":"=0",":":"=2"},"$"+(""+s).replace(/[=:]/g,(function(e){return r[e]}))):t.toString(36);var s,r}function m(e,t,s,r){var n,i=typeof e;if("undefined"!==i&&"boolean"!==i||(e=null),null===e||"string"===i||"number"===i||"object"===i&&e.$$typeof===a)return s(r,e,""===t?l+u(e,0):t),1;var h=0,g=""===t?l:t+c;if(Array.isArray(e))for(var y=0;y<e.length;y++)h+=m(n=e[y],g+u(n,y),s,r);else{var f=function(e){var t=e&&(d&&e[d]||e[p]);if("function"==typeof t)return t}(e);if(f)for(var w,b=f.call(e),E=0;!(w=b.next()).done;)h+=m(n=w.value,g+u(n,E++),s,r);else if("object"===i){var v=""+e;o(!1,"Objects are not valid as a React child (found: %s).%s","[object Object]"===v?"object with keys {"+Object.keys(e).join(", ")+"}":v,"")}}return h}var h=/\/+/g;function g(e){return(""+e).replace(h,"$&/")}var y,f,w=b,b=function(e){var t=this;if(t.instancePool.length){var s=t.instancePool.pop();return t.call(s,e),s}return new t(e)};function E(e,t,s,r){this.result=e,this.keyPrefix=t,this.func=s,this.context=r,this.count=0}function v(e,t,s){var a,o,i=e.result,l=e.keyPrefix,c=e.func,d=e.context,p=c.call(d,t,e.count++);Array.isArray(p)?k(p,i,s,n.thatReturnsArgument):null!=p&&(r.isValidElement(p)&&(a=p,o=l+(!p.key||t&&t.key===p.key?"":g(p.key)+"/")+s,p=r.cloneElement(a,{key:o},void 0!==a.props?a.props.children:void 0)),i.push(p))}function k(e,t,s,r,a){var n="";null!=s&&(n=g(s)+"/");var o=E.getPooled(t,n,r,a);!function(e,t,s){null==e||m(e,"",t,s)}(e,v,o),E.release(o)}E.prototype.destructor=function(){this.result=null,this.keyPrefix=null,this.func=null,this.context=null,this.count=0},y=function(e,t,s,r){var a=this;if(a.instancePool.length){var n=a.instancePool.pop();return a.call(n,e,t,s,r),n}return new a(e,t,s,r)},(f=E).instancePool=[],f.getPooled=y||w,f.poolSize||(f.poolSize=10),f.release=function(e){var t=this;o(e instanceof t,"Trying to release an instance into a pool of a different type."),e.destructor(),t.instancePool.length<t.poolSize&&t.instancePool.push(e)},e.exports=function(e){if("object"!=typeof e||!e||Array.isArray(e))return i(!1,"React.addons.createFragment only accepts a single object. Got: %s",e),e;if(r.isValidElement(e))return i(!1,"React.addons.createFragment does not accept a ReactElement without a wrapper object."),e;o(1!==e.nodeType,"React.addons.createFragment(...): Encountered an invalid child; DOM elements are not valid children of React components.");var t=[];for(var s in e)k(e[s],t,s,n.thatReturnsArgument);return t}},7942:e=>{"use strict";function t(e){return function(){return e}}var s=function(){};s.thatReturns=t,s.thatReturnsFalse=t(!1),s.thatReturnsTrue=t(!0),s.thatReturnsNull=t(null),s.thatReturnsThis=function(){return this},s.thatReturnsArgument=function(e){return e},e.exports=s},9179:e=>{"use strict";e.exports=function(e,t,s,r,a,n,o,i){if(!e){var l;if(void 0===t)l=new Error("Minified exception occurred; use the non-minified dev environment for the full error message and additional helpful warnings.");else{var c=[s,r,a,n,o,i],d=0;(l=new Error(t.replace(/%s/g,(function(){return c[d++]})))).name="Invariant Violation"}throw l.framesToPop=1,l}}},397:(e,t,s)=>{"use strict";var r=s(7942);e.exports=r},4530:(e,t)=>{var s;!function(){"use strict";var r={}.hasOwnProperty;function a(){for(var e=[],t=0;t<arguments.length;t++){var s=arguments[t];if(s){var n=typeof s;if("string"===n||"number"===n)e.push(s);else if(Array.isArray(s)){if(s.length){var o=a.apply(null,s);o&&e.push(o)}}else if("object"===n){if(s.toString!==Object.prototype.toString&&!s.toString.toString().includes("[native code]")){e.push(s.toString());continue}for(var i in s)r.call(s,i)&&s[i]&&e.push(i)}}}return e.join(" ")}e.exports?(a.default=a,e.exports=a):void 0===(s=function(){return a}.apply(t,[]))||(e.exports=s)}()},9196:e=>{"use strict";e.exports=window.React}},t={};function s(r){var a=t[r];if(void 0!==a)return a.exports;var n=t[r]={exports:{}};return e[r](n,n.exports,s),n.exports}s.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return s.d(t,{a:t}),t},s.d=(e,t)=>{for(var r in t)s.o(t,r)&&!s.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},s.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"==typeof window)return window}}(),s.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),s.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},(()=>{"use strict";var e={};s.r(e),s.d(e,{authorFirstName:()=>ci,authorLastName:()=>di,category:()=>hi,categoryTitle:()=>gi,currentDate:()=>pi,currentDay:()=>ui,currentMonth:()=>mi,currentYear:()=>yi,date:()=>fi,excerpt:()=>wi,focusKeyphrase:()=>bi,id:()=>Ei,modified:()=>vi,name:()=>ki,page:()=>_i,pageNumber:()=>xi,pageTotal:()=>Si,permalink:()=>Ti,postContent:()=>Ri,postDay:()=>Ci,postMonth:()=>Ii,postTypeNamePlural:()=>Li,postTypeNameSingular:()=>Pi,postYear:()=>Ai,primaryCategory:()=>Fi,searchPhrase:()=>Oi,separator:()=>Mi,siteDescription:()=>Di,siteName:()=>qi,tag:()=>Ni,term404:()=>Bi,termDescription:()=>$i,termHierarchy:()=>Ui,termTitle:()=>Wi,title:()=>Ki,userDescription:()=>Hi});var t=s(9196),r=s.n(t);const a=window.wp.blocks,n=window.wp.data,o=window.wp.editPost,i=window.wp.element,l=window.wp.i18n,c=window.wp.plugins,d=window.wp.richText,p=window.yoast.externals.contexts,u=window.yoast.externals.redux,m=window.lodash;function h(){return(0,m.get)(window,"wpseoScriptData.metabox",{intl:{},isRtl:!1})}const g=window.yoast.propTypes;var y=s.n(g);const f=window.yoast.styledComponents;var w=s.n(f);const b=w().svg`
	width: ${e=>e.size}px;
	height: ${e=>e.size}px;
	&&& path {
		fill: ${e=>e.color};
	}
	&&& circle.yoast-icon-readability-score {
		fill: ${e=>e.readabilityScoreColor};
		display: ${e=>e.isContentAnalysisActive?"inline":"none"};
	}
	
	&&& circle.yoast-icon-seo-score {
		fill: ${e=>e.seoScoreColor};
		display: ${e=>e.isKeywordAnalysisActive?"inline":"none"};
	}
`,E=function(e){return(0,t.createElement)(b,{...e,xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 646.66 456.27"},(0,t.createElement)("path",{d:"M73,405.26a68.53,68.53,0,0,1-12.82-4c-1-.42-2-.89-3-1.37-1.49-.72-3-1.56-4.77-2.56-1.5-.88-2.71-1.64-3.83-2.39-.9-.61-1.8-1.26-2.68-1.92q-2.64-2-5.08-4.19a68.26,68.26,0,0,1-8.4-9.17c-.92-1.2-1.68-2.25-2.35-3.24q-1.84-2.73-3.44-5.64a68.26,68.26,0,0,1-8.29-32.55V142.13a68.29,68.29,0,0,1,8.29-32.55,58.6,58.6,0,0,1,3.44-5.64,57.53,57.53,0,0,1,4-5.27A69.64,69.64,0,0,1,48.56,85.42,56.06,56.06,0,0,1,54.2,82,67.78,67.78,0,0,1,73,75.09,69.79,69.79,0,0,1,86.75,73.7H256.41L263,55.39H86.75A86.84,86.84,0,0,0,0,142.13V338.22A86.83,86.83,0,0,0,86.75,425H98.07V406.65H86.75A68.31,68.31,0,0,1,73,405.26ZM368.55,60.85l-1.41-.53L360.73,77.5l1.41.53a68.58,68.58,0,0,1,8.66,4,58.65,58.65,0,0,1,5.65,3.43A69.49,69.49,0,0,1,391,98.67c1.4,1.68,2.72,3.46,3.95,5.27s2.39,3.72,3.44,5.64a68.32,68.32,0,0,1,8.29,32.55V406.65H233.55l-.44.76c-3.07,5.37-6.26,10.48-9.49,15.19L222,425H425V142.13A87.19,87.19,0,0,0,368.55,60.85Z",fill:"#000001"}),(0,t.createElement)("path",{d:"M303.66,0l-96.8,268.87-47.58-149H101.1l72.72,186.78a73.61,73.61,0,0,1,0,53.73c-7.07,18.07-19.63,39.63-54.36,46l-1.56.29v49.57l2-.08c29-1.14,51.57-10.72,70.89-30.14,19.69-19.79,36.55-50.52,53-96.68L366.68,0Z",fill:"#000001"}),(0,t.createElement)("circle",{className:"yoast-icon-readability-score",cx:"561.26",cy:"142.43",r:"85.04",fill:"#000001",stroke:"#181716",strokeMiterlimit:"10",strokeWidth:"0.72"}),(0,t.createElement)("circle",{className:"yoast-icon-seo-score",cx:"561.26",cy:"341.96",r:"85.04",fill:"#000001",stroke:"#181716",strokeMiterlimit:"10",strokeWidth:"0.72"}))};E.propTypes={readabilityScoreColor:y().string,isContentAnalysisActive:y().bool,seoScoreColor:y().string,isKeywordAnalysisActive:y().bool,size:y().number,color:y().string},E.defaultProps={readabilityScoreColor:"#000000",isContentAnalysisActive:!1,seoScoreColor:"#000000",isKeywordAnalysisActive:!1,size:20,color:"#000001"};const v=E,k=window.wp.components;function _(e){return void 0===e.length?e:(0,m.flatten)(e).sort(((e,t)=>void 0===e.props.renderPriority?1:e.props.renderPriority-t.props.renderPriority))}const x=({theme:e,location:s,children:r})=>(0,t.createElement)(p.LocationProvider,{value:s},(0,t.createElement)(f.ThemeProvider,{theme:e},r));x.propTypes={theme:y().object.isRequired,location:y().oneOf(["sidebar","metabox","modal"]).isRequired,children:y().element.isRequired};const S=x,T=window.yoast.uiLibrary;const R=window.yoast.componentsNew,C=window.yoast.styleGuide,I=window.yoast.analysis;function A(e){switch(e){case"loading":return{icon:"loading-spinner",color:C.colors.$color_green_medium_light};case"not-set":return{icon:"seo-score-none",color:C.colors.$color_score_icon};case"noindex":return{icon:"seo-score-none",color:C.colors.$color_noindex};case"good":return{icon:"seo-score-good",color:C.colors.$color_green_medium};case"ok":return{icon:"seo-score-ok",color:C.colors.$color_ok};default:return{icon:"seo-score-bad",color:C.colors.$color_red}}}function L({target:e,children:t}){let s=e;return"string"==typeof e&&(s=document.getElementById(e)),s?(0,i.createPortal)(t,s):null}L.propTypes={target:y().oneOfType([y().string,y().object]).isRequired,children:y().node.isRequired};const P=({target:e,scoreIndicator:s})=>(0,t.createElement)(L,{target:e},(0,t.createElement)(R.SvgIcon,{...A(s)}));P.propTypes={target:y().string.isRequired,scoreIndicator:y().string.isRequired};const F=P,O=({handleRefreshClick:e,supportLink:s})=>(0,t.createElement)("div",{className:"yst-flex yst-gap-2"},(0,t.createElement)(T.Button,{onClick:e},(0,l.__)("Refresh this page","wordpress-seo")),(0,t.createElement)(T.Button,{variant:"secondary",as:"a",href:s,target:"_blank",rel:"noopener"},(0,l.__)("Contact support","wordpress-seo")));O.propTypes={handleRefreshClick:y().func.isRequired,supportLink:y().string.isRequired};const M=({handleRefreshClick:e,supportLink:s})=>(0,t.createElement)("div",{className:"yst-grid yst-grid-cols-1 yst-gap-y-2"},(0,t.createElement)(T.Button,{className:"yst-order-last",onClick:e},(0,l.__)("Refresh this page","wordpress-seo")),(0,t.createElement)(T.Button,{variant:"secondary",as:"a",href:s,target:"_blank",rel:"noopener"},(0,l.__)("Contact support","wordpress-seo")));M.propTypes={handleRefreshClick:y().func.isRequired,supportLink:y().string.isRequired};const D=({error:e,children:s})=>(0,t.createElement)("div",{role:"alert",className:"yst-max-w-screen-sm yst-p-8 yst-space-y-4"},(0,t.createElement)(T.Title,null,(0,l.__)("Something went wrong. An unexpected error occurred.","wordpress-seo")),(0,t.createElement)("p",null,(0,l.__)("We're very sorry, but it seems like the following error has interrupted our application:","wordpress-seo")),(0,t.createElement)(T.Alert,{variant:"error"},(null==e?void 0:e.message)||(0,l.__)("Undefined error message.","wordpress-seo")),(0,t.createElement)("p",null,(0,l.__)("Unfortunately, this means that any unsaved changes in this section will be lost. You can try and refresh this page to resolve the problem. If this error still occurs, please get in touch with our support team, and we'll get you all the help you need!","wordpress-seo")),s);D.propTypes={error:y().object.isRequired,children:y().node},D.VerticalButtons=M,D.HorizontalButtons=O;const q=({error:e})=>{const s=(0,i.useCallback)((()=>{var e,t;return null===(e=window)||void 0===e||null===(t=e.location)||void 0===t?void 0:t.reload()}),[]),r=(0,n.useSelect)((e=>e("yoast-seo/editor").selectLink("https://yoa.st/metabox-error-support")),[]),a=(0,n.useSelect)((e=>e("yoast-seo/editor").getPreference("isRtl",!1)),[]);return(0,i.useEffect)((()=>{document.querySelectorAll('[id^="wpseo-meta-tab-"]').forEach((e=>{!function(e){const t=document.querySelector(`#${e}`);null!==t&&(t.style.opacity="0.5",t.style.pointerEvents="none",t.setAttribute("aria-disabled","true"),t.classList.contains("yoast-active-tab")&&t.classList.remove("yoast-active-tab"))}(e.id)}))}),[]),(0,t.createElement)(T.Root,{context:{isRtl:a}},(0,t.createElement)(D,{error:e},(0,t.createElement)(D.HorizontalButtons,{supportLink:r,handleRefreshClick:s}),(0,t.createElement)(F,{target:"wpseo-seo-score-icon",scoreIndicator:"not-set"}),(0,t.createElement)(F,{target:"wpseo-readability-score-icon",scoreIndicator:"not-set"}),(0,t.createElement)(F,{target:"wpseo-inclusive-language-score-icon",scoreIndicator:"not-set"})))};function N({theme:e}){return(0,t.createElement)(S,{theme:e,location:"metabox"},(0,t.createElement)(T.ErrorBoundary,{FallbackComponent:q},(0,t.createElement)(k.Slot,{name:"YoastMetabox"},(e=>_(e)))))}q.propTypes={error:y().object.isRequired};const B=window.wp.compose,$=t.forwardRef((function(e,s){return t.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true",ref:s},e),t.createElement("path",{d:"M2 11a1 1 0 011-1h2a1 1 0 011 1v5a1 1 0 01-1 1H3a1 1 0 01-1-1v-5zM8 7a1 1 0 011-1h2a1 1 0 011 1v9a1 1 0 01-1 1H9a1 1 0 01-1-1V7zM14 4a1 1 0 011-1h2a1 1 0 011 1v12a1 1 0 01-1 1h-2a1 1 0 01-1-1V4z"}))})),U=(e=null)=>(0,t.useMemo)((()=>{const t={role:"img","aria-hidden":"true"};return null!==e&&(t.focusable=e?"true":"false"),t}),[e]);var W=s(4530),K=s.n(W);const H=({className:e,...s})=>(0,t.createElement)("span",{className:K()("yst-grow yst-overflow-hidden yst-overflow-ellipsis yst-whitespace-nowrap yst-font-wp yst-text-[#555] yst-text-base yst-leading-[normal] yst-subpixel-antialiased yst-text-left",e),...s});H.displayName="MetaboxButton.Text",H.propTypes={className:y().string},H.defaultProps={className:""};const V=({className:e,...s})=>(0,t.createElement)("button",{type:"button",className:K()("yst-flex yst-items-center yst-w-full yst-pt-4 yst-pb-4 yst-pr-4 yst-pl-6 yst-space-x-2 yst-border-t yst-border-t-[rgb(0,0,0,0.2)] yst-rounded-none yst-transition-all hover:yst-bg-[#f0f0f0] focus:yst-outline focus:yst-outline-[1px] focus:yst-outline-[color:#0066cd] focus:-yst-outline-offset-1 focus:yst-shadow-[0_0_3px_rgba(8,74,103,0.8)]",e),...s});V.propTypes={className:y().string},V.defaultProps={className:""},V.Text=H;const Y=window.yoast.helpers,j=w().div`
	min-width: 600px;

	@media screen and ( max-width: 680px ) {
		min-width: 0;
		width: 86vw;
	}
`,z=w().div`
	@media screen and ( min-width: 600px ) {
		max-width: 420px;
	}
`,G=(w()(R.Icon)`
	float: ${(0,Y.getDirectionalStyle)("right","left")};
	margin: ${(0,Y.getDirectionalStyle)("0 0 16px 16px","0 16px 16px 0")};

	&& {
		width: 150px;
		height: 150px;

		@media screen and ( max-width: 680px ) {
			width: 80px;
			height: 80px;
		}
	}
`,"yoast yoast-gutenberg-modal"),Z=e=>{const{title:s,className:r,showYoastIcon:a,additionalClassName:n,...o}=e,i=a?(0,t.createElement)("span",{className:"yoast-icon"}):null;return(0,t.createElement)(k.Modal,{title:s,className:`${r} ${n}`,icon:i,...o},e.children)};Z.propTypes={title:y().string,className:y().string,showYoastIcon:y().bool,children:y().oneOfType([y().node,y().arrayOf(y().node)]),additionalClassName:y().string},Z.defaultProps={title:"Yoast SEO",className:G,showYoastIcon:!0,children:null,additionalClassName:""};const X=Z;var Q,J;function ee(){return ee=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var s=arguments[t];for(var r in s)Object.prototype.hasOwnProperty.call(s,r)&&(e[r]=s[r])}return e},ee.apply(this,arguments)}const te=e=>t.createElement("svg",ee({xmlns:"http://www.w3.org/2000/svg","aria-hidden":"true",viewBox:"0 0 425 456.27"},e),Q||(Q=t.createElement("path",{d:"M73 405.26a66.79 66.79 0 0 1-6.54-1.7 64.75 64.75 0 0 1-6.28-2.31c-1-.42-2-.89-3-1.37-1.49-.72-3-1.56-4.77-2.56-1.5-.88-2.71-1.64-3.83-2.39-.9-.61-1.8-1.26-2.68-1.92a70.154 70.154 0 0 1-5.08-4.19 69.21 69.21 0 0 1-8.4-9.17c-.92-1.2-1.68-2.25-2.35-3.24a70.747 70.747 0 0 1-3.44-5.64 68.29 68.29 0 0 1-8.29-32.55V142.13a68.26 68.26 0 0 1 8.29-32.55c1-1.92 2.21-3.82 3.44-5.64s2.55-3.58 4-5.27a69.26 69.26 0 0 1 14.49-13.25C50.37 84.19 52.27 83 54.2 82A67.59 67.59 0 0 1 73 75.09a68.75 68.75 0 0 1 13.75-1.39h169.66L263 55.39H86.75A86.84 86.84 0 0 0 0 142.13v196.09A86.84 86.84 0 0 0 86.75 425h11.32v-18.35H86.75A68.75 68.75 0 0 1 73 405.26zM368.55 60.85l-1.41-.53-6.41 17.18 1.41.53a68.06 68.06 0 0 1 8.66 4c1.93 1 3.82 2.2 5.65 3.43A69.19 69.19 0 0 1 391 98.67c1.4 1.68 2.72 3.46 3.95 5.27s2.39 3.72 3.44 5.64a68.29 68.29 0 0 1 8.29 32.55v264.52H233.55l-.44.76c-3.07 5.37-6.26 10.48-9.49 15.19L222 425h203V142.13a87.2 87.2 0 0 0-56.45-81.28z"})),J||(J=t.createElement("path",{stroke:"#000",strokeMiterlimit:10,strokeWidth:3.81,d:"M119.8 408.28v46c28.49-1.12 50.73-10.6 69.61-29.58 19.45-19.55 36.17-50 52.61-96L363.94 1.9H305l-98.25 272.89-48.86-153h-54l71.7 184.18a75.67 75.67 0 0 1 0 55.12c-7.3 18.68-20.25 40.66-55.79 47.19z"}))),se=e=>(0,t.createElement)("div",{className:"yoast components-panel__body"},(0,t.createElement)("h2",{className:"components-panel__body-title"},(0,t.createElement)("button",{id:e.id,onClick:e.onClick,className:"components-button components-panel__body-toggle",type:"button"},e.prefixIcon&&(0,t.createElement)("span",{className:"yoast-icon-span",style:{fill:`${e.prefixIcon&&e.prefixIcon.color||""}`}},(0,t.createElement)(R.SvgIcon,{size:e.prefixIcon.size,icon:e.prefixIcon.icon})),(0,t.createElement)("span",{className:"yoast-title-container"},(0,t.createElement)("div",{className:"yoast-title"},e.title),(0,t.createElement)("div",{className:"yoast-subtitle"},e.subTitle)),e.children,e.suffixIcon&&(0,t.createElement)(R.SvgIcon,{size:e.suffixIcon.size,icon:e.suffixIcon.icon}),e.SuffixHeroIcon))),re=se;se.propTypes={onClick:y().func.isRequired,title:y().string.isRequired,id:y().string,subTitle:y().string,suffixIcon:y().object,SuffixHeroIcon:y().object,prefixIcon:y().object,children:y().node},se.defaultProps={id:"",suffixIcon:null,SuffixHeroIcon:null,prefixIcon:null,subTitle:"",children:null};const ae=window.moment;var ne=s.n(ae),oe=s(6746);const ie=(0,Y.makeOutboundLink)(),le=e=>{const s=(0,l.sprintf)(/* translators: %d expands to the amount of allowed keyphrases on a free account, %s expands to a link to Wincher plans. */
(0,l.__)("You've reached the maximum amount of %d keyphrases you can add to your Wincher account. If you wish to add more keyphrases, please %s.","wordpress-seo"),e.limit,"{{updateWincherPlanLink/}}");return(0,t.createElement)(R.Alert,{type:"error"},(0,oe.Z)({mixedString:s,components:{updateWincherPlanLink:(0,t.createElement)(ie,{href:wpseoAdminGlobalL10n["links.wincher.pricing"]},(0,l.sprintf)(/* translators: %s : Expands to "Wincher". */
(0,l.__)("upgrade your %s plan","wordpress-seo"),"Wincher"))}}))};le.propTypes={limit:y().number},le.defaultProps={limit:10};const ce=le,de=()=>(0,t.createElement)(R.Alert,{type:"error"},(0,l.__)("Something went wrong while tracking the ranking position(s) of your page. Please try again later.","wordpress-seo")),pe=window.wp.apiFetch;var ue=s.n(pe);async function me(e,t,s,r=200){try{const a=await e();return!!a&&(a.status===r?t(a):s(a))}catch(e){console.error(e.message)}}async function he(e){try{return await ue()(e)}catch(e){return e.error&&e.status?e:e instanceof Response&&await e.json()}}async function ge(e){return(0,m.isArray)(e)||(e=[e]),await he({path:"yoast/v1/wincher/keyphrases/track",method:"POST",data:{keyphrases:e}})}const ye=w().p`
	color: ${C.colors.$color_pink_dark};
	font-size: 14px;
	font-weight: 700;
	margin: 13px 0 10px;
`,fe=w()(R.SvgIcon)`
	margin-right: 5px;
	vertical-align: middle;
`,we=w().button`
	position: absolute;
	top: 9px;
	right: 9px;
	border: none;
    background: none;
    cursor: pointer;
`,be=w().p`
	font-size: 13px;
	font-weight: 500;
	margin: 10px 0 13px;
`,Ee=w().div`
	position: relative;
	background: ${e=>e.isTitleShortened?"#F5F7F7":"transparent"};
	border: 1px solid #C7C7C7;
	border-left: 4px solid${C.colors.$color_pink_dark};
	padding: 0 16px;
	margin-bottom: 1.5em;
`,ve=e=>{const[t,s]=(0,i.useState)(null);return(0,i.useEffect)((()=>{e&&!t&&async function(){return await he({path:"yoast/v1/wincher/account/limit",method:"GET"})}().then((e=>s(e)))}),[t]),t};ve.propTypes={limit:y().bool.isRequired};const ke=({limit:e,usage:s,isTitleShortened:r,isFreeAccount:a})=>{const n=(0,l.sprintf)(
/* Translators: %1$s expands to the number of used keywords.
   * %2$s expands to the account keywords limit.
   */
(0,l.__)("Your are tracking %1$s out of %2$s keyphrases included in your free account.","wordpress-seo"),s,e),o=(0,l.sprintf)(
/* Translators: %1$s expands to the number of used keywords.
   * %2$s expands to the account keywords limit.
   */
(0,l.__)("Your are tracking %1$s out of %2$s keyphrases included in your account.","wordpress-seo"),s,e),i=a?n:o,c=(0,l.sprintf)(
/* Translators: %1$s expands to the number of used keywords.
   * %2$s expands to the account keywords limit.
   */
(0,l.__)("Keyphrases tracked: %1$s/%2$s","wordpress-seo"),s,e),d=r?c:i;return(0,t.createElement)(ye,null,r&&(0,t.createElement)(fe,{icon:"exclamation-triangle",color:C.colors.$color_pink_dark,size:"14px"}),d)};ke.propTypes={limit:y().number.isRequired,usage:y().number.isRequired,isTitleShortened:y().bool,isFreeAccount:y().bool};const _e=(0,Y.makeOutboundLink)(),xe=({discount:e,months:s})=>{const r=(0,t.createElement)(_e,{href:wpseoAdminGlobalL10n["links.wincher.upgrade"],style:{fontWeight:600}},(0,l.sprintf)(/* Translators: %s : Expands to "Wincher". */
(0,l.__)("Click here to upgrade your %s plan","wordpress-seo"),"Wincher"));if(!e||!s)return(0,t.createElement)(be,null,r);const a=100*e,n=(0,l.sprintf)(
/* Translators: %1$s expands to upgrade account link.
   * %2$s expands to the upgrade discount value.
   * %3$s expands to the upgrade discount duration e.g. 2 months.
   */
(0,l.__)("%1$s and get an exclusive %2$s discount for %3$s month(s).","wordpress-seo"),"{{wincherAccountUpgradeLink/}}",a+"%",s);return(0,t.createElement)(be,null,(0,oe.Z)({mixedString:n,components:{wincherAccountUpgradeLink:r}}))};xe.propTypes={discount:y().number,months:y().number};const Se=({onClose:e,isTitleShortened:s,trackingInfo:r})=>{const a=(()=>{const[e,t]=(0,i.useState)(null);return(0,i.useEffect)((()=>{e||async function(){return await he({path:"yoast/v1/wincher/account/upgrade-campaign",method:"GET"})}().then((e=>t(e)))}),[e]),e})();if(null===r)return null;const{limit:n,usage:o}=r;if(!(n&&o/n>=.8))return null;const c=Boolean(null==a?void 0:a.discount);return(0,t.createElement)(Ee,{isTitleShortened:s},e&&(0,t.createElement)(we,{type:"button","aria-label":(0,l.__)("Close the upgrade callout","wordpress-seo"),onClick:e},(0,t.createElement)(R.SvgIcon,{icon:"times-circle",color:C.colors.$color_pink_dark,size:"14px"})),(0,t.createElement)(ke,{...r,isTitleShortened:s,isFreeAccount:c}),(0,t.createElement)(xe,{discount:null==a?void 0:a.discount,months:null==a?void 0:a.months}))};Se.propTypes={onClose:y().func,isTitleShortened:y().bool,trackingInfo:y().object};const Te=Se,Re=()=>(0,t.createElement)(R.Alert,{type:"success"},(0,l.sprintf)(/* translators: %s: Expands to "Wincher". */
(0,l.__)("You have successfully connected to %s! You can now track the SEO performance for the keyphrase(s) of this page.","wordpress-seo"),"Wincher")),Ce=()=>(0,t.createElement)(R.Alert,{type:"info"},(0,l.sprintf)(/* translators: %s: Expands to "Wincher". */
(0,l.__)("%s is currently tracking the ranking position(s) of your page. This may take a few minutes. Please wait or check back later.","wordpress-seo"),"Wincher")),Ie=({data:e,mapChartDataToTableData:s,dataTableCaption:r,dataTableHeaderLabels:a,isDataTableVisuallyHidden:n})=>e.length!==a.length?(0,t.createElement)("p",null,(0,l.__)("The number of headers and header labels don't match.","wordpress-seo")):(0,t.createElement)("div",{className:n?"screen-reader-text":null},(0,t.createElement)("table",null,(0,t.createElement)("caption",null,r),(0,t.createElement)("thead",null,(0,t.createElement)("tr",null,a.map(((e,s)=>(0,t.createElement)("th",{key:s},e))))),(0,t.createElement)("tbody",null,(0,t.createElement)("tr",null,e.map(((e,r)=>(0,t.createElement)("td",{key:r},s(e.y))))))));Ie.propTypes={data:y().arrayOf(y().shape({x:y().number,y:y().number})).isRequired,mapChartDataToTableData:y().func,dataTableCaption:y().string.isRequired,dataTableHeaderLabels:y().array.isRequired,isDataTableVisuallyHidden:y().bool},Ie.defaultProps={mapChartDataToTableData:null,isDataTableVisuallyHidden:!0};const Ae=Ie,Le=({data:e,width:s,height:r,fillColor:a,strokeColor:n,strokeWidth:o,className:l,mapChartDataToTableData:c,dataTableCaption:d,dataTableHeaderLabels:p,isDataTableVisuallyHidden:u})=>{const m=Math.max(1,Math.max(...e.map((e=>e.x)))),h=Math.max(1,Math.max(...e.map((e=>e.y)))),g=r-o,y=e.map((e=>`${e.x/m*s},${g-e.y/h*g+o}`)).join(" "),f=`0,${g+o} `+y+` ${s},${g+o}`;return(0,t.createElement)(i.Fragment,null,(0,t.createElement)("svg",{width:s,height:r,viewBox:`0 0 ${s} ${r}`,className:l,role:"img","aria-hidden":"true",focusable:"false"},(0,t.createElement)("polygon",{fill:a,points:f}),(0,t.createElement)("polyline",{fill:"none",stroke:n,strokeWidth:o,strokeLinejoin:"round",strokeLinecap:"round",points:y})),c&&(0,t.createElement)(Ae,{data:e,mapChartDataToTableData:c,dataTableCaption:d,dataTableHeaderLabels:p,isDataTableVisuallyHidden:u}))};Le.propTypes={data:y().arrayOf(y().shape({x:y().number,y:y().number})).isRequired,width:y().number.isRequired,height:y().number.isRequired,fillColor:y().string,strokeColor:y().string,strokeWidth:y().number,className:y().string,mapChartDataToTableData:y().func,dataTableCaption:y().string.isRequired,dataTableHeaderLabels:y().array.isRequired,isDataTableVisuallyHidden:y().bool},Le.defaultProps={fillColor:null,strokeColor:"#000000",strokeWidth:1,className:"",mapChartDataToTableData:null,isDataTableVisuallyHidden:!0};const Pe=Le,Fe=()=>(0,t.createElement)("p",{className:"yoast-wincher-seo-performance-modal__loading-message"},(0,l.__)("Tracking the ranking position...","wordpress-seo")," ",(0,t.createElement)(R.SvgIcon,{icon:"loading-spinner"})),Oe=w()(R.SvgIcon)`
	margin-left: 2px;
	flex-shrink: 0;
	rotate: ${e=>e.isImproving?"-90deg":"90deg"};
`,Me=w().span`
	color: ${e=>e.isImproving?"#69AB56":"#DC3332"};
	font-size: 13px;
	font-weight: 600;
	line-height: 20px;
	margin-right: 2px;
	margin-left: 12px;
`,De=w().td`
	padding-right: 0 !important;

	& > div {
		margin: 0px;
	}
`,qe=w().td`
	padding-left: 2px !important;
`,Ne=w().td.attrs({className:"yoast-table--nopadding"})`
	& > div {
		justify-content: center;
	}
`,Be=w().div`
	display: flex;
	align-items: center;
	& > a {
		box-sizing: border-box;
	}
`,$e=w().button`
	background: none;
	color: inherit;
	border: none;
	padding: 0;
	font: inherit;
	cursor: pointer;
	outline: inherit;
    display: flex;
    align-items: center;
`,Ue=w().tr`
	background-color: ${e=>e.isEnabled?"#FFFFFF":"#F9F9F9"} !important;
`;function We(e){return Math.round(100*e)}function Ke({chartData:e}){if((0,m.isEmpty)(e)||(0,m.isEmpty)(e.position))return"?";const s=function(e){return Array.from({length:e.position.history.length},((e,t)=>t+1)).map((e=>(0,l.sprintf)((0,l._n)("%d day","%d days",e,"wordpress-seo"),e)))}(e),r=e.position.history.map(((e,t)=>({x:t,y:101-e.value})));return(0,t.createElement)(Pe,{width:66,height:24,data:r,strokeWidth:1.8,strokeColor:"#498afc",fillColor:"#ade3fc",mapChartDataToTableData:We,dataTableCaption:(0,l.__)("Keyphrase position in the last 90 days on a scale from 0 to 100.","wordpress-seo"),dataTableHeaderLabels:s})}Ke.propTypes={chartData:y().object},Ke.defaultProps={chartData:{}};const He=({rowData:e})=>{var s;if(null==e||null===(s=e.position)||void 0===s||!s.change)return(0,t.createElement)(Ke,{chartData:e});const r=e.position.change<0;return(0,t.createElement)(i.Fragment,null,(0,t.createElement)(Ke,{chartData:e}),(0,t.createElement)(Me,{isImproving:r},Math.abs(e.position.change)),(0,t.createElement)(Oe,{icon:"caret-right",color:r?"#69AB56":"#DC3332",size:"14px",isImproving:r}))};function Ve(e){var s;const{keyphrase:r,rowData:a,onTrackKeyphrase:n,onUntrackKeyphrase:o,isFocusKeyphrase:c,isDisabled:d,isLoading:p,isSelected:u,onSelectKeyphrases:h}=e,g=!(0,m.isEmpty)(a),y=!(0,m.isEmpty)(null==a||null===(s=a.position)||void 0===s?void 0:s.history),f=(0,i.useCallback)((()=>{d||(g?o(r,a.id):n(r))}),[r,n,o,g,a,d]),w=(0,i.useCallback)((()=>{h((e=>u?e.filter((e=>e!==r)):e.concat(r)))}),[h,u,r]);return(0,t.createElement)(Ue,{isEnabled:g},(0,t.createElement)(De,null,y&&(0,t.createElement)(R.Checkbox,{id:"select-"+r,onChange:w,checked:u,label:""})),(0,t.createElement)(qe,null,r,c&&(0,t.createElement)("span",null,"*")),function(e){const{rowData:s,websiteId:r,keyphrase:a,onSelectKeyphrases:n}=e,o=(0,i.useCallback)((()=>{n([a])}),[n,a]),c=!(0,m.isEmpty)(s),d=s&&s.updated_at&&ne()(s.updated_at)>=ne()().subtract(7,"days"),p=s?(0,l.sprintf)("https://app.wincher.com/websites/%s/keywords?serp=%s&utm_medium=plugin&utm_source=yoast&referer=yoast&partner=yoast",r,s.id):null;return c?d?(0,t.createElement)(i.Fragment,null,(0,t.createElement)("td",null,(0,t.createElement)(Be,null,function(e){return!e||!e.position||e.position.value>100?"> 100":e.position.value}(s),(0,t.createElement)(R.ButtonStyledLink,{variant:"secondary",href:p,style:{height:28,marginLeft:12},rel:"noopener",target:"_blank"},(0,l.__)("View","wordpress-seo")))),(0,t.createElement)("td",{className:"yoast-table--nopadding"},(0,t.createElement)($e,{type:"button",onClick:o},(0,t.createElement)(He,{rowData:s}))),(0,t.createElement)("td",null,(u=s.updated_at,ne()(u).fromNow()))):(0,t.createElement)("td",{className:"yoast-table--nopadding",colSpan:"3"},(0,t.createElement)(Fe,null)):(0,t.createElement)("td",{className:"yoast-table--nopadding",colSpan:"3"},(0,t.createElement)("i",null,(0,l.__)("Activate tracking to show the ranking position","wordpress-seo")));var u}(e),(0,t.createElement)(Ne,null,function({keyphrase:e,isEnabled:s,toggleAction:r,isLoading:a}){return a?(0,t.createElement)(R.SvgIcon,{icon:"loading-spinner"}):(0,t.createElement)(R.Toggle,{id:`toggle-keyphrase-tracking-${e}`,className:"wincher-toggle",isEnabled:s,onSetToggleState:r,showToggleStateLabel:!1})}({keyphrase:r,isEnabled:g,toggleAction:f,isLoading:p})))}He.propTypes={rowData:y().object},Ve.propTypes={rowData:y().object,keyphrase:y().string.isRequired,onTrackKeyphrase:y().func,onUntrackKeyphrase:y().func,isFocusKeyphrase:y().bool,isDisabled:y().bool,isLoading:y().bool,websiteId:y().string,isSelected:y().bool.isRequired,onSelectKeyphrases:y().func.isRequired},Ve.defaultProps={rowData:{},onTrackKeyphrase:()=>{},onUntrackKeyphrase:()=>{},isFocusKeyphrase:!1,isDisabled:!1,isLoading:!1,websiteId:""};const Ye=(0,Y.makeOutboundLink)(),je=w().span`
	display: block;
	font-style: italic;

	@media (min-width: 782px) {
		display: inline;
		position: absolute;
		${(0,Y.getDirectionalStyle)("right","left")}: 8px;
	}
`,ze=w().div`
	width: 100%;
	overflow-y: auto;
`,Ge=w().th`
	pointer-events: ${e=>e.isDisabled?"none":"initial"};
	padding-right: 0 !important;

	& > div {
		margin: 0px;
	}
`,Ze=w().th`
	padding-left: 2px !important;
`,Xe=e=>{const t=(0,i.useRef)();return(0,i.useEffect)((()=>{t.current=e})),t.current},Qe=(0,m.debounce)((async function(e=null,t=null,s=null,r){return await he({path:"yoast/v1/wincher/keyphrases",method:"POST",data:{keyphrases:e,permalink:s,startAt:t},signal:r})}),500,{leading:!0}),Je=e=>{const{addTrackedKeyphrase:s,isLoggedIn:r,keyphrases:a,permalink:n,removeTrackedKeyphrase:o,setKeyphraseLimitReached:c,setRequestFailed:d,setRequestSucceeded:p,setTrackedKeyphrases:u,setHasTrackedAll:h,trackAll:g,trackedKeyphrases:y,isNewlyAuthenticated:f,websiteId:w,focusKeyphrase:b,newRequest:E,startAt:v,selectedKeyphrases:k,onSelectKeyphrases:_}=e,x=(0,i.useRef)(),S=(0,i.useRef)(),T=(0,i.useRef)(!1),[C,I]=(0,i.useState)([]),A=(0,i.useCallback)((e=>{const t=e.toLowerCase();return y&&!(0,m.isEmpty)(y)&&y.hasOwnProperty(t)?y[t]:null}),[y]),L=(0,i.useMemo)((()=>async()=>{await me((()=>(S.current&&S.current.abort(),S.current="undefined"==typeof AbortController?null:new AbortController,Qe(a,v,n,S.current.signal))),(e=>{p(e),u(e.results)}),(e=>{d(e)}))}),[p,d,u,a,n,v]),P=(0,i.useCallback)((async e=>{const t=(Array.isArray(e)?e:[e]).map((e=>e.toLowerCase()));I((e=>[...e,...t])),await me((()=>ge(t)),(e=>{p(e),s(e.results),L()}),(e=>{400===e.status&&e.limit&&c(e.limit),d(e)}),201),I((e=>(0,m.without)(e,...t)))}),[p,d,c,s,L]),F=(0,i.useCallback)((async(e,t)=>{e=e.toLowerCase(),I((t=>[...t,e])),await me((()=>async function(e){return await he({path:"yoast/v1/wincher/keyphrases/untrack",method:"DELETE",data:{keyphraseID:e}})}(t)),(t=>{p(t),o(e)}),(e=>{d(e)})),I((t=>(0,m.without)(t,e)))}),[p,o,d]),O=(0,i.useCallback)((async e=>{E(),await P(e)}),[E,P]),M=Xe(n),D=Xe(a),q=Xe(v),N=n&&v;(0,i.useEffect)((()=>{r&&N&&(n!==M||(0,m.difference)(a,D).length||v!==q)&&L()}),[r,n,M,a,D,L,N,v,q]),(0,i.useEffect)((()=>{if(r&&g&&null!==y){const e=a.filter((e=>!A(e)));e.length&&P(e),h()}}),[r,g,y,P,h,A,a]),(0,i.useEffect)((()=>{f&&!T.current&&(L(),T.current=!0)}),[f,L]),(0,i.useEffect)((()=>{if(r&&!(0,m.isEmpty)(y))return(0,m.filter)(y,(e=>(0,m.isEmpty)(e.updated_at))).length>0&&(x.current=setInterval((()=>{L()}),1e4)),()=>{clearInterval(x.current)}}),[r,y,L]);const B=r&&null===y,$=(0,i.useMemo)((()=>(0,m.isEmpty)(y)?[]:Object.values(y).filter((e=>{var t;return!(0,m.isEmpty)(null==e||null===(t=e.position)||void 0===t?void 0:t.history)})).map((e=>e.keyword))),[y]),U=(0,i.useMemo)((()=>k.length>0&&$.length>0&&$.every((e=>k.includes(e)))),[k,$]),W=(0,i.useCallback)((()=>{_(U?[]:$)}),[_,U,$]),K=(0,i.useMemo)((()=>(0,m.orderBy)(a,[e=>Object.values(y||{}).map((e=>e.keyword)).includes(e)],["desc"])),[a,y]);return a&&!(0,m.isEmpty)(a)&&(0,t.createElement)(i.Fragment,null,(0,t.createElement)(ze,null,(0,t.createElement)("table",{className:"yoast yoast-table"},(0,t.createElement)("thead",null,(0,t.createElement)("tr",null,(0,t.createElement)(Ge,{isDisabled:0===$.length},(0,t.createElement)(R.Checkbox,{id:"select-all",onChange:W,checked:U,label:""})),(0,t.createElement)(Ze,{scope:"col",abbr:(0,l.__)("Keyphrase","wordpress-seo")},(0,l.__)("Keyphrase","wordpress-seo")),(0,t.createElement)("th",{scope:"col",abbr:(0,l.__)("Position","wordpress-seo")},(0,l.__)("Position","wordpress-seo")),(0,t.createElement)("th",{scope:"col",abbr:(0,l.__)("Position over time","wordpress-seo")},(0,l.__)("Position over time","wordpress-seo")),(0,t.createElement)("th",{scope:"col",abbr:(0,l.__)("Last updated","wordpress-seo")},(0,l.__)("Last updated","wordpress-seo")),(0,t.createElement)("th",{scope:"col",abbr:(0,l.__)("Tracking","wordpress-seo")},(0,l.__)("Tracking","wordpress-seo")))),(0,t.createElement)("tbody",null,K.map(((e,s)=>(0,t.createElement)(Ve,{key:`trackable-keyphrase-${s}`,keyphrase:e,onTrackKeyphrase:O,onUntrackKeyphrase:F,rowData:A(e),isFocusKeyphrase:e===b.trim().toLowerCase(),websiteId:w,isDisabled:!r,isLoading:B||C.indexOf(e.toLowerCase())>=0,isSelected:k.includes(e),onSelectKeyphrases:_})))))),(0,t.createElement)("p",{style:{marginBottom:0,position:"relative"}},(0,t.createElement)(Ye,{href:wpseoAdminGlobalL10n["links.wincher.login"]},(0,l.sprintf)(/* translators: %s expands to Wincher */
(0,l.__)("Get more insights over at %s","wordpress-seo"),"Wincher")),(0,t.createElement)(je,null,(0,l.__)("* focus keyphrase","wordpress-seo"))))};Je.propTypes={addTrackedKeyphrase:y().func.isRequired,isLoggedIn:y().bool,isNewlyAuthenticated:y().bool,keyphrases:y().array,newRequest:y().func.isRequired,removeTrackedKeyphrase:y().func.isRequired,setRequestFailed:y().func.isRequired,setKeyphraseLimitReached:y().func.isRequired,setRequestSucceeded:y().func.isRequired,setTrackedKeyphrases:y().func.isRequired,setHasTrackedAll:y().func.isRequired,trackAll:y().bool,trackedKeyphrases:y().object,websiteId:y().string,permalink:y().string.isRequired,focusKeyphrase:y().string,startAt:y().string,selectedKeyphrases:y().arrayOf(y().string).isRequired,onSelectKeyphrases:y().func.isRequired},Je.defaultProps={isLoggedIn:!1,isNewlyAuthenticated:!1,keyphrases:[],trackAll:!1,websiteId:"",focusKeyphrase:""};const et=Je,tt=(0,B.compose)([(0,n.withSelect)((e=>{const{getWincherWebsiteId:t,getWincherTrackableKeyphrases:s,getWincherLoginStatus:r,getWincherPermalink:a,getFocusKeyphrase:n,isWincherNewlyAuthenticated:o,shouldWincherTrackAll:i}=e("yoast-seo/editor");return{focusKeyphrase:n(),keyphrases:s(),isLoggedIn:r(),trackAll:i(),websiteId:t(),isNewlyAuthenticated:o(),permalink:a()}})),(0,n.withDispatch)((e=>{const{setWincherNewRequest:t,setWincherRequestSucceeded:s,setWincherRequestFailed:r,setWincherSetKeyphraseLimitReached:a,setWincherTrackedKeyphrases:n,setWincherTrackingForKeyphrase:o,setWincherTrackAllKeyphrases:i,unsetWincherTrackingForKeyphrase:l}=e("yoast-seo/editor");return{newRequest:()=>{t()},setRequestSucceeded:e=>{s(e)},setRequestFailed:e=>{r(e)},setKeyphraseLimitReached:e=>{a(e)},addTrackedKeyphrase:e=>{o(e)},removeTrackedKeyphrase:e=>{l(e)},setTrackedKeyphrases:e=>{n(e)},setHasTrackedAll:()=>{i(!1)}}}))])(et),st=(0,Y.makeOutboundLink)(),rt=(0,Y.makeOutboundLink)(),at=()=>{const e=(0,l.sprintf)((0,l.__)(/* translators: %1$s expands to a link to Wincher, %2$s expands to a link to the keyphrase tracking article on Yoast.com */
"With %1$s you can track the ranking position of your page in the search results based on your keyphrase(s). %2$s","wordpress-seo"),"{{wincherLink/}}","{{wincherReadMoreLink/}}");return(0,t.createElement)("p",null,(0,oe.Z)({mixedString:e,components:{wincherLink:(0,t.createElement)(st,{href:wpseoAdminGlobalL10n["links.wincher.website"]},"Wincher"),wincherReadMoreLink:(0,t.createElement)(rt,{href:wpseoAdminL10n["shortlinks.wincher.seo_performance"]},(0,l.__)("Read more about keyphrase tracking with Wincher","wordpress-seo"))}}))},nt=()=>(0,t.createElement)(R.Alert,{type:"error"},(0,l.__)("No keyphrase has been set. Please set a keyphrase first.","wordpress-seo")),ot=()=>(0,t.createElement)(R.Alert,{type:"info"},(0,l.sprintf)(/* translators: %s: Expands to "Wincher". */
(0,l.__)("Automatic tracking of keyphrases is enabled. Your keyphrase(s) will automatically be tracked by %s when you publish your post.","wordpress-seo"),"Wincher"));class it{constructor(e,t={},s={}){this.url=e,this.origin=new URL(e).origin,this.eventHandlers=Object.assign({success:{type:"",callback:()=>{}},error:{type:"",callback:()=>{}}},t),this.options=Object.assign({height:570,width:340,title:""},s),this.popup=null,this.createPopup=this.createPopup.bind(this),this.messageHandler=this.messageHandler.bind(this),this.getPopup=this.getPopup.bind(this)}createPopup(){const{height:e,width:t,title:s}=this.options,r=["top="+(window.top.outerHeight/2+window.top.screenY-e/2),"left="+(window.top.outerWidth/2+window.top.screenX-t/2),"width="+t,"height="+e,"resizable=1","scrollbars=1","status=0"];this.popup&&!this.popup.closed||(this.popup=window.open(this.url,s,r.join(","))),this.popup&&this.popup.focus(),window.addEventListener("message",this.messageHandler,!1)}async messageHandler(e){const{data:t,source:s,origin:r}=e;r===this.origin&&this.popup===s&&(t.type===this.eventHandlers.success.type&&(this.popup.close(),window.removeEventListener("message",this.messageHandler,!1),await this.eventHandlers.success.callback(t)),t.type===this.eventHandlers.error.type&&(this.popup.close(),window.removeEventListener("message",this.messageHandler,!1),await this.eventHandlers.error.callback(t)))}getPopup(){return this.popup}isClosed(){return!this.popup||this.popup.closed}focus(){this.isClosed()||this.popup.focus()}}const lt=e=>{const s=(0,l.sprintf)(/* translators: %s expands to a link to open the Wincher login popup. */
(0,l.__)("It seems like something went wrong when retrieving your website's data. Please %s and try again.","wordpress-seo"),"{{reconnectToWincher/}}","Wincher");return(0,t.createElement)(R.Alert,{type:"error",className:e.className},(0,oe.Z)({mixedString:s,components:{reconnectToWincher:(0,t.createElement)("a",{href:"#",onClick:t=>{t.preventDefault(),e.onReconnect()}},(0,l.sprintf)(/* translators: %s : Expands to "Wincher". */
(0,l.__)("reconnect to %s","wordpress-seo"),"Wincher"))}}))};lt.propTypes={onReconnect:y().func.isRequired,className:y().string},lt.defaultProps={className:""};const ct=lt,dt=()=>(0,t.createElement)(R.Alert,{type:"error"},(0,l.__)("Before you can track your SEO performance make sure to set either the post’s title and save it as a draft or manually set the post’s slug.","wordpress-seo")),pt=window.yoast["chart.js"],ut="label";function mt(e,t){"function"==typeof e?e(t):e&&(e.current=t)}function ht(e,t){e.labels=t}function gt(e,t){let s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:ut;const r=[];e.datasets=t.map((t=>{const a=e.datasets.find((e=>e[s]===t[s]));return a&&t.data&&!r.includes(a)?(r.push(a),Object.assign(a,t),a):{...t}}))}function yt(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:ut;const s={labels:[],datasets:[]};return ht(s,e.labels),gt(s,e.datasets,t),s}function ft(e,s){const{height:r=150,width:a=300,redraw:n=!1,datasetIdKey:o,type:i,data:l,options:c,plugins:d=[],fallbackContent:p,updateMode:u,...m}=e,h=(0,t.useRef)(null),g=(0,t.useRef)(),y=()=>{h.current&&(g.current=new pt.Chart(h.current,{type:i,data:yt(l,o),options:c&&{...c},plugins:d}),mt(s,g.current))},f=()=>{mt(s,null),g.current&&(g.current.destroy(),g.current=null)};return(0,t.useEffect)((()=>{!n&&g.current&&c&&function(e,t){const s=e.options;s&&t&&Object.assign(s,t)}(g.current,c)}),[n,c]),(0,t.useEffect)((()=>{!n&&g.current&&ht(g.current.config.data,l.labels)}),[n,l.labels]),(0,t.useEffect)((()=>{!n&&g.current&&l.datasets&&gt(g.current.config.data,l.datasets,o)}),[n,l.datasets]),(0,t.useEffect)((()=>{g.current&&(n?(f(),setTimeout(y)):g.current.update(u))}),[n,c,l.labels,l.datasets,u]),(0,t.useEffect)((()=>{g.current&&(f(),setTimeout(y))}),[i]),(0,t.useEffect)((()=>(y(),()=>f())),[]),t.createElement("canvas",Object.assign({ref:h,role:"img",height:r,width:a},m),p)}const wt=(0,t.forwardRef)(ft);function bt(e,s){return pt.Chart.register(s),(0,t.forwardRef)(((s,r)=>t.createElement(wt,Object.assign({},s,{ref:r,type:e}))))}const Et=bt("line",pt.LineController),vt={datetime:"MMM D, YYYY, h:mm:ss a",millisecond:"h:mm:ss.SSS a",second:"h:mm:ss a",minute:"h:mm a",hour:"hA",day:"MMM D",week:"ll",month:"MMM YYYY",quarter:"[Q]Q - YYYY",year:"YYYY"};pt._adapters._date.override("function"==typeof ne()?{_id:"moment",formats:function(){return vt},parse:function(e,t){return"string"==typeof e&&"string"==typeof t?e=ne()(e,t):e instanceof ne()||(e=ne()(e)),e.isValid()?e.valueOf():null},format:function(e,t){return ne()(e).format(t)},add:function(e,t,s){return ne()(e).add(t,s).valueOf()},diff:function(e,t,s){return ne()(e).diff(ne()(t),s)},startOf:function(e,t,s){return e=ne()(e),"isoWeek"===t?(s=Math.trunc(Math.min(Math.max(0,s),6)),e.isoWeekday(s).startOf("day").valueOf()):e.startOf(t).valueOf()},endOf:function(e,t){return ne()(e).endOf(t).valueOf()}}:{}),Math.PI,Number.POSITIVE_INFINITY,Math.log10,Math.sign,"undefined"==typeof window||window.requestAnimationFrame,new Map,Object.create(null),Object.create(null),Number.EPSILON;const kt=["top","right","bottom","left"];function _t(e,t,s){const r={};s=s?"-"+s:"";for(let a=0;a<4;a++){const n=kt[a];r[n]=parseFloat(e[t+"-"+n+s])||0}return r.width=r.left+r.right,r.height=r.top+r.bottom,r}!function(){let e=!1;try{const t={get passive(){return e=!0,!1}};window.addEventListener("test",null,t),window.removeEventListener("test",null,t)}catch(e){}}(),pt.Chart.register(pt.CategoryScale,pt.LineController,pt.LineElement,pt.PointElement,pt.LinearScale,pt.TimeScale,pt.Legend,pt.Tooltip);const xt=["#ff983b","#ffa3f7","#3798ff","#ff3b3b","#acce81","#b51751","#3949ab","#26c6da","#ccb800","#de66ff","#4db6ac","#ffab91","#45f5f1","#77f210","#90a4ae","#ffd54f","#006b5e","#8ec7d2","#b1887c","#cc9300"];function St({datasets:e,isChartShown:s,keyphrases:r}){if(!s)return null;const a=(0,i.useMemo)((()=>Object.fromEntries([...r].sort().map(((e,t)=>[e,xt[t%xt.length]])))),[r]),n=e.map((e=>{const t=a[e.label];return{...e,data:e.data.map((({datetime:e,value:t})=>({x:e,y:t}))),lineTension:0,pointRadius:1,pointHoverRadius:4,borderWidth:2,pointHitRadius:6,backgroundColor:t,borderColor:t}})).filter((e=>!1!==e.selected));return(0,t.createElement)(Et,{height:100,data:{datasets:n},options:{plugins:{legend:{display:!0,position:"bottom",labels:{color:"black",usePointStyle:!0,boxHeight:7,boxWidth:7},onClick:m.noop},tooltip:{enabled:!0,callbacks:{title:e=>ne()(e[0].raw.x).utc().format("YYYY-MM-DD")},titleAlign:"center",mode:"xPoint",position:"nearest",usePointStyle:!0,boxHeight:7,boxWidth:7,boxPadding:2}},scales:{x:{bounds:"ticks",type:"time",time:{unit:"day",minUnit:"day"},grid:{display:!1},ticks:{autoSkipPadding:50,maxRotation:0,color:"black"}},y:{bounds:"ticks",offset:!0,reverse:!0,ticks:{precision:0,color:"black"},max:101}}}})}pt.Interaction.modes.xPoint=(e,t,s,r)=>{const a=function(e,t){if("native"in e)return e;const{canvas:s,currentDevicePixelRatio:r}=t,a=(m=s).ownerDocument.defaultView.getComputedStyle(m,null),n="border-box"===a.boxSizing,o=_t(a,"padding"),i=_t(a,"border","width"),{x:l,y:c,box:d}=function(e,t){const s=e.touches,r=s&&s.length?s[0]:e,{offsetX:a,offsetY:n}=r;let o,i,l=!1;if(((e,t,s)=>(e>0||t>0)&&(!s||!s.shadowRoot))(a,n,e.target))o=a,i=n;else{const e=t.getBoundingClientRect();o=r.clientX-e.left,i=r.clientY-e.top,l=!0}return{x:o,y:i,box:l}}(e,s),p=o.left+(d&&i.left),u=o.top+(d&&i.top);var m;let{width:h,height:g}=t;return n&&(h-=o.width+i.width,g-=o.height+i.height),{x:Math.round((l-p)/h*s.width/r),y:Math.round((c-u)/g*s.height/r)}}(t,e);let n=[];if(pt.Interaction.evaluateInteractionItems(e,"x",a,((e,t,s)=>{e.inXRange(a.x,r)&&n.push({element:e,datasetIndex:t,index:s})})),0===n.length)return n;const o=n.reduce(((e,t)=>Math.abs(a.x-e.element.x)<Math.abs(a.x-t.element.x)?e:t)).element.x;return n=n.filter((e=>e.element.x===o)),n.some((e=>Math.abs(e.element.y-a.y)<10))?n:[]},St.propTypes={datasets:y().arrayOf(y().shape({label:y().string.isRequired,data:y().arrayOf(y().shape({datetime:y().string.isRequired,value:y().number.isRequired})).isRequired,selected:y().bool})).isRequired,isChartShown:y().bool.isRequired,keyphrases:y().array.isRequired};const Tt=({response:e,onLogin:s})=>[401,403,404].includes(e.status)?(0,t.createElement)(ct,{onReconnect:s}):(0,t.createElement)(de,null);Tt.propTypes={response:y().object.isRequired,onLogin:y().func.isRequired};const Rt=({isSuccess:e,response:s,allKeyphrasesMissRanking:r,onLogin:a,keyphraseLimitReached:n,limit:o})=>n?(0,t.createElement)(ce,{limit:o}):(0,m.isEmpty)(s)||e?r?(0,t.createElement)(Ce,null):null:(0,t.createElement)(Tt,{response:s,onLogin:a});Rt.propTypes={isSuccess:y().bool.isRequired,allKeyphrasesMissRanking:y().bool.isRequired,response:y().object,onLogin:y().func.isRequired,keyphraseLimitReached:y().bool.isRequired,limit:y().number.isRequired},Rt.defaultProps={response:{}};let Ct=null;const It=async e=>{if(Ct&&!Ct.isClosed())return void Ct.focus();const{url:t}=await async function(){return await he({path:"yoast/v1/wincher/authorization-url",method:"GET"})}();Ct=new it(t,{success:{type:"wincher:oauth:success",callback:t=>(async(e,t)=>{const{onAuthentication:s,setRequestSucceeded:r,setRequestFailed:a,keyphrases:n,addTrackedKeyphrase:o,setKeyphraseLimitReached:i}=e;await me((()=>async function(e){const{code:t,websiteId:s}=e;return await he({path:"yoast/v1/wincher/authenticate",method:"POST",data:{code:t,websiteId:s}})}(t)),(async e=>{s(!0,!0,t.websiteId.toString()),r(e);const l=(Array.isArray(n)?n:[n]).map((e=>e.toLowerCase()));await me((()=>ge(l)),(e=>{r(e),o(e.results)}),(e=>{400===e.status&&e.limit&&i(e.limit),a(e)}),201);const c=Ct.getPopup();c&&c.close()}),(async e=>a(e)))})(e,t)},error:{type:"wincher:oauth:error",callback:()=>e.onAuthentication(!1,!1)}},{title:"Wincher_login",width:500,height:700}),Ct.createPopup()},At=e=>e.isLoggedIn?null:(0,t.createElement)("p",null,(0,t.createElement)(R.NewButton,{onClick:e.onLogin,variant:"primary"},(0,l.sprintf)(/* translators: %s expands to Wincher */
(0,l.__)("Connect with %s","wordpress-seo"),"Wincher")));At.propTypes={isLoggedIn:y().bool.isRequired,onLogin:y().func.isRequired};const Lt=w().div`
	p {
		margin: 1em 0;
	}
`,Pt=w().div`
	${e=>e.isDisabled&&"\n\t\topacity: .5;\n\t\tpointer-events: none;\n\t"};
`,Ft=w().div`
	font-weight: var(--yoast-font-weight-bold);
	color: var(--yoast-color-label);
	font-size: var(--yoast-font-size-default);
`,Ot=w().div.attrs({className:"yoast-field-group"})`
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 14px;
`,Mt=w().div`
	margin: 8px 0;
`,Dt=ne().utc().startOf("day"),qt=[{name:(0,l.__)("Last day","wordpress-seo"),value:ne()(Dt).subtract(1,"days").format(),defaultIndex:1},{name:(0,l.__)("Last week","wordpress-seo"),value:ne()(Dt).subtract(1,"week").format(),defaultIndex:2},{name:(0,l.__)("Last month","wordpress-seo"),value:ne()(Dt).subtract(1,"month").format(),defaultIndex:3},{name:(0,l.__)("Last year","wordpress-seo"),value:ne()(Dt).subtract(1,"year").format(),defaultIndex:0}],Nt=e=>{const{onSelect:s,selected:r,options:a,isLoggedIn:n}=e;return n?a.length<1?null:(0,t.createElement)("select",{className:"components-select-control__input",id:"wincher-period-picker",value:(null==r?void 0:r.value)||a[0].value,onChange:s},a.map((e=>(0,t.createElement)("option",{key:e.name,value:e.value},e.name)))):null};Nt.propTypes={onSelect:y().func.isRequired,selected:y().object,options:y().array.isRequired,isLoggedIn:y().bool.isRequired};const Bt=e=>{const{trackedKeyphrases:s,isLoggedIn:r,keyphrases:a,shouldTrackAll:n,permalink:o,historyDaysLimit:c}=e;if(!o&&r)return(0,t.createElement)(dt,null);if(0===a.length)return(0,t.createElement)(nt,null);const d=ne()(Dt).subtract(c,"days"),p=qt.filter((e=>ne()(e.value).isSameOrAfter(d))),u=(0,m.orderBy)(p,(e=>e.defaultIndex),"desc")[0],[h,g]=(0,i.useState)(u),[y,f]=(0,i.useState)([]),w=y.length>0,b=(0,B.usePrevious)(s);(0,i.useEffect)((()=>{if(!(0,m.isEmpty)(s)&&(0,m.difference)(Object.keys(s),Object.keys(b||[])).length){const e=Object.values(s).map((e=>e.keyword));f(e)}}),[s,b]),(0,i.useEffect)((()=>{g(u)}),[null==u?void 0:u.name]);const E=(0,i.useCallback)((e=>{const t=qt.find((t=>t.value===e.target.value));t&&g(t)}),[g]),v=(0,i.useMemo)((()=>(0,m.isEmpty)(y)||(0,m.isEmpty)(s)?[]:Object.values(s).filter((e=>{var t;return!(null==e||null===(t=e.position)||void 0===t||!t.history)})).map((e=>{var t;return{label:e.keyword,data:e.position.history,selected:y.includes(e.keyword)&&!(0,m.isEmpty)(null===(t=e.position)||void 0===t?void 0:t.history)}}))),[y,s]);return(0,t.createElement)(Pt,{isDisabled:!r},(0,t.createElement)("p",null,(0,l.__)("You can enable / disable tracking the SEO performance for each keyphrase below.","wordpress-seo")),r&&n&&(0,t.createElement)(ot,null),(0,t.createElement)(Ot,null,(0,t.createElement)(Nt,{selected:h,onSelect:E,options:p,isLoggedIn:r})),(0,t.createElement)(Mt,null,(0,t.createElement)(St,{isChartShown:w,datasets:v,keyphrases:a})),(0,t.createElement)(tt,{startAt:null==h?void 0:h.value,selectedKeyphrases:y,onSelectKeyphrases:f,trackedKeyphrases:s}))};function $t(e){const{isNewlyAuthenticated:s,isLoggedIn:r}=e,a=(0,i.useCallback)((()=>{It(e)}),[It,e]),n=ve(r);return(0,t.createElement)(Lt,null,s&&(0,t.createElement)(Re,null),r&&(0,t.createElement)(Te,{trackingInfo:n}),(0,t.createElement)(Ft,null,(0,l.__)("SEO performance","wordpress-seo"),(0,t.createElement)(R.HelpIcon,{linkTo:wpseoAdminL10n["shortlinks.wincher.seo_performance"]
/* translators: Hidden accessibility text. */,linkText:(0,l.__)("Learn more about the SEO performance feature.","wordpress-seo")})),(0,t.createElement)(at,null),(0,t.createElement)(At,{isLoggedIn:r,onLogin:a}),(0,t.createElement)(Rt,{...e,onLogin:a}),(0,t.createElement)(Bt,{...e,historyDaysLimit:(null==n?void 0:n.historyDays)||31}))}Bt.propTypes={trackedKeyphrases:y().object,keyphrases:y().array.isRequired,isLoggedIn:y().bool.isRequired,shouldTrackAll:y().bool.isRequired,permalink:y().string.isRequired,historyDaysLimit:y().number},$t.propTypes={trackedKeyphrases:y().object,addTrackedKeyphrase:y().func.isRequired,isLoggedIn:y().bool,isNewlyAuthenticated:y().bool,keyphrases:y().array,response:y().object,shouldTrackAll:y().bool,permalink:y().string,historyDaysLimit:y().number},$t.defaultProps={trackedKeyphrases:null,isLoggedIn:!1,isNewlyAuthenticated:!1,keyphrases:[],response:{},shouldTrackAll:!1,permalink:"",historyDaysLimit:0};const Ut=(0,B.compose)([(0,n.withSelect)((e=>{const{isWincherNewlyAuthenticated:t,getWincherKeyphraseLimitReached:s,getWincherLimit:r,getWincherHistoryDaysLimit:a,getWincherLoginStatus:n,getWincherRequestIsSuccess:o,getWincherRequestResponse:i,getWincherTrackableKeyphrases:l,getWincherTrackedKeyphrases:c,getWincherAllKeyphrasesMissRanking:d,getWincherPermalink:p,shouldWincherAutomaticallyTrackAll:u}=e("yoast-seo/editor");return{keyphrases:l(),trackedKeyphrases:c(),allKeyphrasesMissRanking:d(),isLoggedIn:n(),isNewlyAuthenticated:t(),isSuccess:o(),keyphraseLimitReached:s(),limit:r(),response:i(),shouldTrackAll:u(),permalink:p(),historyDaysLimit:a()}})),(0,n.withDispatch)((e=>{const{setWincherWebsiteId:t,setWincherRequestSucceeded:s,setWincherRequestFailed:r,setWincherTrackingForKeyphrase:a,setWincherSetKeyphraseLimitReached:n,setWincherLoginStatus:o}=e("yoast-seo/editor");return{setRequestSucceeded:e=>{s(e)},setRequestFailed:e=>{r(e)},addTrackedKeyphrase:e=>{a(e)},setKeyphraseLimitReached:e=>{n(e)},onAuthentication:(e,s,r)=>{t(r),o(e,s)}}}))])($t),Wt=w()($)`
	width: 18px;
	height: 18px;
	margin: 3px;
`;function Kt(e){const{keyphrases:t,onNoKeyphraseSet:s,onOpen:r,location:a}=e;if(!t.length){let e=document.querySelector("#focus-keyword-input-metabox");return e||(e=document.querySelector("#focus-keyword-input-sidebar")),e.focus(),void s()}r(a)}function Ht(e){const{location:s,whichModalOpen:r,shouldCloseOnClickOutside:a}=e,n=(0,i.useCallback)((()=>{Kt(e)}),[Kt,e]),o=(0,l.__)("Track SEO performance","wordpress-seo"),c=U();return(0,t.createElement)(i.Fragment,null,r===s&&(0,t.createElement)(X,{title:o,onRequestClose:e.onClose,icon:(0,t.createElement)(te,null),additionalClassName:"yoast-wincher-seo-performance-modal yoast-gutenberg-modal__no-padding",shouldCloseOnClickOutside:a},(0,t.createElement)(j,{className:"yoast-gutenberg-modal__content yoast-wincher-seo-performance-modal__content"},(0,t.createElement)(Ut,null))),"sidebar"===s&&(0,t.createElement)(re,{id:`wincher-open-button-${s}`,title:o,SuffixHeroIcon:(0,t.createElement)(Wt,{className:"yst-text-slate-500",...c}),onClick:n}),"metabox"===s&&(0,t.createElement)("div",{className:"yst-root"},(0,t.createElement)(V,{id:`wincher-open-button-${s}`,onClick:n},(0,t.createElement)(V.Text,null,o),(0,t.createElement)($,{className:"yst-h-5 yst-w-5 yst-text-slate-500",...c}))))}Ht.propTypes={location:y().string,whichModalOpen:y().oneOf(["none","metabox","sidebar","postpublish"]),shouldCloseOnClickOutside:y().bool,keyphrases:y().array.isRequired,onNoKeyphraseSet:y().func.isRequired,onOpen:y().func.isRequired,onClose:y().func.isRequired},Ht.defaultProps={location:"",whichModalOpen:"none",shouldCloseOnClickOutside:!0};const Vt=(0,B.compose)([(0,n.withSelect)((e=>{const{getWincherModalOpen:t,getWincherTrackableKeyphrases:s}=e("yoast-seo/editor");return{keyphrases:s(),whichModalOpen:t()}})),(0,n.withDispatch)((e=>{const{setWincherOpenModal:t,setWincherDismissModal:s,setWincherNoKeyphrase:r}=e("yoast-seo/editor");return{onOpen:e=>{t(e)},onClose:()=>{s()},onNoKeyphraseSet:()=>{r()}}}))])(Ht),Yt=window.yoast.externals.components;function jt(){return(0,B.createHigherOrderComponent)((function(e){return(0,B.pure)((function(t){const s=(0,i.useContext)(p.LocationContext);return(0,i.createElement)(e,{...t,location:s})}))}),"withLocation")}const zt=(0,B.compose)([(0,n.withSelect)((e=>{const{isCornerstoneContent:t}=e("yoast-seo/editor");return{isCornerstone:t(),learnMoreUrl:wpseoAdminL10n["shortlinks.cornerstone_content_info"]}})),(0,n.withDispatch)((e=>{const{toggleCornerstoneContent:t}=e("yoast-seo/editor");return{onChange:t}})),jt()])(Yt.CollapsibleCornerstone),Gt=window.yoast.searchMetadataPreviews,Zt=w()(R.StyledSection)`
	&${R.StyledSectionBase} {
		padding: 0;

		& ${R.StyledHeading} {
			${(0,Y.getDirectionalStyle)("padding-left","padding-right")}: 20px;
			margin-left: ${(0,Y.getDirectionalStyle)("0","20px")};
		}
	}
`,Xt=({children:e,title:s,icon:r,hasPaperStyle:a,shoppingData:n})=>(0,t.createElement)(Zt,{headingLevel:3,headingText:s,headingIcon:r,headingIconColor:"#555",hasPaperStyle:a,shoppingData:n},e);Xt.propTypes={children:y().element,title:y().string,icon:y().string,hasPaperStyle:y().bool,shoppingData:y().object},Xt.defaultProps={hasPaperStyle:!0,shoppingData:null};const Qt=Xt,Jt=window.wp.sanitize,es="SNIPPET_EDITOR_UPDATE_REPLACEMENT_VARIABLE";function ts(e,t,s="",r=!1){const a="string"==typeof t?(0,Y.decodeHTML)(t):t;return{type:es,name:e,value:a,label:s,hidden:r}}function ss(e){return e.charAt(0).toUpperCase()+e.slice(1)}const{stripHTMLTags:rs}=Y.strings,as=["slug","content","contentImage","snippetPreviewImageURL"];function ns(e,t="_"){return e.replace(/\s/g,t)}function os(e,t=156){return(e=(e=(0,Jt.stripTags)(e)).trim()).length<=t||(e=e.substring(0,t),/\s/.test(e)&&(e=e.substring(0,e.lastIndexOf(" ")))),e}const is=(0,m.memoize)(((e,t)=>0===e?m.noop:(0,m.debounce)((s=>t(s,e)),500))),ls=t.forwardRef((function(e,s){return t.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:2,stroke:"currentColor","aria-hidden":"true",ref:s},e),t.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M8 11V7a4 4 0 118 0m-4 8v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2z"}))})),cs=({link:e,text:s})=>(0,t.createElement)(T.Root,null,(0,t.createElement)("p",null,s),(0,t.createElement)(T.Button,{href:e,as:"a",className:"yst-gap-2 yst-mb-5 yst-mt-2",variant:"upsell",target:"_blank",rel:"noopener"},(0,t.createElement)(ls,{className:"yst-w-4 yst-h-4 yst--ml-1 yst-shrink-0"}),(0,l.sprintf)(/* translators: %1$s expands to Yoast WooCommerce SEO. */
(0,l.__)("Unlock with %1$s","wordpress-seo"),"Yoast WooCommerce SEO")));cs.propTypes={link:y().string.isRequired,text:y().string.isRequired};const ds=cs,ps=function(e,t){let s=0;return t.shortenedBaseUrl&&"string"==typeof t.shortenedBaseUrl&&(s=t.shortenedBaseUrl.length),e.url=e.url.replace(/\s+/g,"-"),"-"===e.url[e.url.length-1]&&(e.url=e.url.slice(0,-1)),"-"===e.url[s]&&(e.url=e.url.slice(0,s)+e.url.slice(s+1)),function(e){const t=(0,m.get)(window,["YoastSEO","app","pluggable"],!1);if(!t||!(0,m.get)(window,["YoastSEO","app","pluggable","loaded"],!1))return function(e){const t=(0,m.get)(window,["YoastSEO","wp","replaceVarsPlugin","replaceVariables"],m.identity);return{url:e.url,title:rs(t(e.title)),description:rs(t(e.description)),filteredSEOTitle:e.filteredSEOTitle?rs(t(e.filteredSEOTitle)):""}}(e);const s=t._applyModifications.bind(t);return{url:e.url,title:rs(s("data_page_title",e.title)),description:rs(s("data_meta_desc",e.description)),filteredSEOTitle:e.filteredSEOTitle?rs(s("data_page_title",e.filteredSEOTitle)):""}}(e)},us=(0,B.compose)([(0,n.withSelect)((function(e){const{getBaseUrlFromSettings:t,getDateFromSettings:s,getFocusKeyphrase:r,getRecommendedReplaceVars:a,getReplaceVars:n,getShoppingData:o,getSiteIconUrlFromSettings:i,getSnippetEditorData:l,getSnippetEditorMode:c,getSnippetEditorPreviewImageUrl:d,getSnippetEditorWordsToHighlight:p,isCornerstoneContent:u,getIsTerm:m,getContentLocale:h,getSiteName:g}=e("yoast-seo/editor"),y=n();return y.forEach((e=>{""!==e.value||["title","excerpt","excerpt_only"].includes(e.name)||(e.value="%%"+e.name+"%%")})),{baseUrl:t(),data:l(),date:s(),faviconSrc:i(),keyword:r(),mobileImageSrc:d(),mode:c(),recommendedReplacementVariables:a(),replacementVariables:y,shoppingData:o(),wordsToHighlight:p(),isCornerstone:u(),isTaxonomy:m(),locale:h(),siteName:g()}})),(0,n.withDispatch)((function(e,t,{select:s}){const{updateData:r,switchMode:a,updateAnalysisData:n,findCustomFields:o}=e("yoast-seo/editor"),i=e("core/editor"),l=s("yoast-seo/editor").getPostId();return{onChange:(e,t)=>{switch(e){case"mode":a(t);break;case"slug":r({slug:t}),i&&i.editPost({slug:t});break;default:r({[e]:t})}},onChangeAnalysisData:n,onReplacementVariableSearchChange:is(l,o)}}))])((e=>{const s=(0,m.get)(window,"wpseoScriptData.metabox.woocommerceUpsellGooglePreviewLink",""),r=(0,m.get)(window,"wpseoScriptData.woocommerceUpsell",""),a=(0,l.__)("Want an enhanced Google preview of how your WooCommerce products look in the search results?","wordpress-seo");return(0,t.createElement)(p.LocationConsumer,null,(n=>(0,t.createElement)(Qt,{icon:"eye",hasPaperStyle:e.hasPaperStyle},(0,t.createElement)(t.Fragment,null,r&&(0,t.createElement)(ds,{link:s,text:a}),(0,t.createElement)(Gt.SnippetEditor,{...e,descriptionPlaceholder:(0,l.__)("Please provide a meta description by editing the snippet below.","wordpress-seo"),mapEditorDataToPreview:ps,showCloseButton:!1,idSuffix:n})))))})),ms=(0,n.withSelect)((e=>{const{getWarningMessage:t}=e("yoast-seo/editor");return{message:t()}}))(R.Warning),hs=window.yoast.featureFlag,gs=w()(R.Collapsible)`
	h2 > button {
		padding-left: 24px;
		padding-top: 16px;

		&:hover {
			background-color: #f0f0f0;
		}
	}

	div[class^="collapsible_content"] {
		padding: 24px 0;
		margin: 0 24px;
		border-top: 1px solid rgba(0,0,0,0.2);
	}

`,ys=e=>(0,t.createElement)(gs,{hasPadding:!0,hasSeparator:!0,...e}),fs=()=>{const e=(0,n.useSelect)((e=>e("yoast-seo/editor").getEstimatedReadingTime()),[]),s=(0,i.useMemo)((()=>(0,m.get)(window,"wpseoAdminL10n.shortlinks-insights-estimated_reading_time","")),[]);return(0,t.createElement)(R.InsightsCard,{amount:e,unit:(0,l._n)("minute","minutes",e,"wordpress-seo"),title:(0,l.__)("Reading time","wordpress-seo"),linkTo:s
/* translators: Hidden accessibility text. */,linkText:(0,l.__)("Learn more about reading time","wordpress-seo")})},ws=(0,Y.makeOutboundLink)();function bs(e,s,r){const a=function(e){switch(e){case I.DIFFICULTY.FAIRLY_DIFFICULT:case I.DIFFICULTY.DIFFICULT:case I.DIFFICULTY.VERY_DIFFICULT:return(0,l.__)("Try to make shorter sentences, using less difficult words to improve readability","wordpress-seo");case I.DIFFICULTY.NO_DATA:return(0,l.__)("Continue writing to get insight into the readability of your text!","wordpress-seo");default:return(0,l.__)("Good job!","wordpress-seo")}}(s);return(0,t.createElement)("span",null,function(e,t){return-1===e?(0,l.sprintf)((0,l.__)("Your text should be slightly longer to calculate your Flesch reading ease score.","wordpress-seo")):(0,l.sprintf)(
/* Translators: %1$s expands to the numeric Flesch reading ease score,
  		%2$s expands to the easiness of reading (e.g. 'easy' or 'very difficult').
  	 */
(0,l.__)("The copy scores %1$s in the test, which is considered %2$s to read.","wordpress-seo"),e,function(e){switch(e){case I.DIFFICULTY.NO_DATA:return(0,l.__)("no data","wordpress-seo");case I.DIFFICULTY.VERY_EASY:return(0,l.__)("very easy","wordpress-seo");case I.DIFFICULTY.EASY:return(0,l.__)("easy","wordpress-seo");case I.DIFFICULTY.FAIRLY_EASY:return(0,l.__)("fairly easy","wordpress-seo");case I.DIFFICULTY.OKAY:return(0,l.__)("okay","wordpress-seo");case I.DIFFICULTY.FAIRLY_DIFFICULT:return(0,l.__)("fairly difficult","wordpress-seo");case I.DIFFICULTY.DIFFICULT:return(0,l.__)("difficult","wordpress-seo");case I.DIFFICULTY.VERY_DIFFICULT:return(0,l.__)("very difficult","wordpress-seo")}}(t))}(e,s)," ",s>=I.DIFFICULTY.FAIRLY_DIFFICULT?(0,t.createElement)(ws,{href:r},a+"."):a)}const Es=()=>{let e=(0,n.useSelect)((e=>e("yoast-seo/editor").getFleschReadingEaseScore()),[]);const s=(0,i.useMemo)((()=>(0,m.get)(window,"wpseoAdminL10n.shortlinks-insights-flesch_reading_ease","")),[]),r=(0,n.useSelect)((e=>e("yoast-seo/editor").getFleschReadingEaseDifficulty()),[e]),a=(0,i.useMemo)((()=>{const t=(0,m.get)(window,"wpseoAdminL10n.shortlinks-insights-flesch_reading_ease_article","");return bs(e,r,t)}),[e,r]);return-1===e&&(e="?"),(0,t.createElement)(R.InsightsCard,{amount:e,unit:(0,l.__)("out of 100","wordpress-seo"),title:(0,l.__)("Flesch reading ease","wordpress-seo"),linkTo:s
/* translators: Hidden accessibility text. */,linkText:(0,l.__)("Learn more about Flesch reading ease","wordpress-seo"),description:a})};let vs,ks,_s,xs;const Ss=/<(\/)?(\w+)\s*(\/)?>/g;function Ts(e,t,s,r,a){return{element:e,tokenStart:t,tokenLength:s,prevOffset:r,leadingTextStart:a,children:[]}}function Rs(){const e=vs.length-ks;0!==e&&_s.push(vs.substring(ks,ks+e))}function Cs(e){const{element:t,tokenStart:s,tokenLength:r,prevOffset:a,children:n}=e,o=xs[xs.length-1],l=vs.substring(o.prevOffset,s);l&&o.children.push(l),o.children.push((0,i.cloneElement)(t,null,...n)),o.prevOffset=a||s+r}function Is(e){const t=function(){const e=Ss.exec(vs);if(null===e)return["no-more-tokens"];const t=e.index,[s,r,a,n]=e,o=s.length;return n?["self-closed",a,t,o]:r?["closer",a,t,o]:["opener",a,t,o]}(),[s,r,a,n]=t,o=xs.length,l=a>ks?ks:null;if(!e[r])return Rs(),!1;switch(s){case"no-more-tokens":if(0!==o){const{leadingTextStart:e,tokenStart:t}=xs.pop();_s.push(vs.substring(e,e+t))}return Rs(),!1;case"self-closed":return 0===o?(null!==l&&_s.push(vs.substring(l,a)),_s.push(e[r]),ks=a+n,!0):(Cs(Ts(e[r],a,n)),ks=a+n,!0);case"opener":return xs.push(Ts(e[r],a,n,a+n,l)),ks=a+n,!0;case"closer":if(1===o)return function(e){const{element:t,leadingTextStart:s,prevOffset:r,tokenStart:a,children:n}=xs.pop(),o=e?vs.substring(r,e):vs.substring(r);o&&n.push(o),null!==s&&_s.push(vs.substring(s,a)),_s.push((0,i.cloneElement)(t,null,...n))}(a),ks=a+n,!0;const t=xs.pop(),s=vs.substring(t.prevOffset,a);t.children.push(s),t.prevOffset=a+n;const c=Ts(t.element,t.tokenStart,t.tokenLength,a+n);return c.children=t.children,Cs(c),ks=a+n,!0;default:return Rs(),!1}}const As=(e,t)=>{if(vs=e,ks=0,_s=[],xs=[],Ss.lastIndex=0,!(e=>{const t="object"==typeof e,s=t&&Object.values(e);return t&&s.length&&s.every((e=>(0,i.isValidElement)(e)))})(t))throw new TypeError("The conversionMap provided is not valid. It must be an object with values that are WPElements");do{}while(Is(t));return(0,i.createElement)(i.Fragment,null,..._s)},Ls=({data:e,itemScreenReaderText:s,className:r,...a})=>{const n=(0,i.useMemo)((()=>{var t,s;return null!==(t=null===(s=(0,m.maxBy)(e,"number"))||void 0===s?void 0:s.number)&&void 0!==t?t:0}),[e]);return(0,t.createElement)("ul",{className:K()("yoast-data-model",r),...a},e.map((({name:e,number:r})=>(0,t.createElement)("li",{key:`${e}_dataItem`,style:{"--yoast-width":r/n*100+"%"}},e,(0,t.createElement)("span",null,r),s&&(0,t.createElement)("span",{className:"screen-reader-text"},(0,l.sprintf)(s,r))))))};Ls.propTypes={data:y().arrayOf(y().shape({name:y().string.isRequired,number:y().number.isRequired})),itemScreenReaderText:y().string,className:y().string},Ls.defaultProps={data:[],itemScreenReaderText:"",className:""};const Ps=Ls,Fs=window.wp.url,Os=(0,Y.makeOutboundLink)(),Ms=({location:e})=>{const s=(0,n.useSelect)((e=>{var t,s;return null===(t=null===(s=e("yoast-seo-premium/editor"))||void 0===s?void 0:s.getPreference("isProminentWordsAvailable",!1))||void 0===t||t}),[]),r=(0,n.useSelect)((e=>e("yoast-seo/editor").getPreference("shouldUpsell",!1)),[]),a=(0,i.useMemo)((()=>(0,m.get)(window,`wpseoAdminL10n.shortlinks-insights-upsell-${e}-prominent_words`,"")),[e]),o=(0,i.useMemo)((()=>{const e=(0,m.get)(window,"wpseoAdminL10n.shortlinks-insights-keyword_research_link","");return As((0,l.sprintf)(
// translators: %1$s and %2$s are replaced by opening and closing <a> tags.
(0,l.__)("Read our %1$sultimate guide to keyword research%2$s to learn more about keyword research and keyword strategy.","wordpress-seo"),"<a>","</a>"),{a:(0,t.createElement)(Os,{href:e})})}),[]),c=(0,i.useMemo)((()=>As((0,l.sprintf)(
// translators: %1$s expands to a starting `b` tag, %1$s expands to a closing `b` tag and %3$s expands to `Yoast SEO Premium`.
(0,l.__)("With %1$s%3$s%2$s, this section will show you which words occur most often in your text. By checking these prominent words against your intended keyword(s), you'll know how to edit your text to be more focused.","wordpress-seo"),"<b>","</b>","Yoast SEO Premium"),{b:(0,t.createElement)("b",null)})),[]),d=(0,n.useSelect)((e=>{var t,s;return null!==(t=null===(s=e("yoast-seo-premium/editor"))||void 0===s?void 0:s.getProminentWords())&&void 0!==t?t:[]}),[]),u=(0,i.useMemo)((()=>{const e=(0,l.sprintf)(
// translators: %1$s expands to Yoast SEO Premium.
(0,l.__)("Get %s to enjoy the benefits of prominent words","wordpress-seo"),"Yoast SEO Premium").split(/\s+/);return e.map(((t,s)=>({name:t,number:e.length-s})))}),[]),h=(0,i.useMemo)((()=>r?u:d.map((({word:e,occurrence:t})=>({name:e,number:t})))),[d,u]);if(!s)return null;const{locationContext:g}=(0,p.useRootContext)();return(0,t.createElement)("div",{className:"yoast-prominent-words"},(0,t.createElement)("div",{className:"yoast-field-group__title"},(0,t.createElement)("b",null,(0,l.__)("Prominent words","wordpress-seo"))),!r&&(0,t.createElement)("p",null,0===h.length?(0,l.__)("Once you add a bit more copy, we'll give you a list of words that occur the most in the content. These give an indication of what your content focuses on.","wordpress-seo"):(0,l.__)("The following words occur the most in the content. These give an indication of what your content focuses on. If the words differ a lot from your topic, you might want to rewrite your content accordingly.","wordpress-seo")),r&&(0,t.createElement)("p",null,c),r&&(0,t.createElement)(Os,{href:(0,Fs.addQueryArgs)(a,{context:g}),"data-action":"load-nfd-ctb","data-ctb-id":"f6a84663-465f-4cb5-8ba5-f7a6d72224b2",className:"yoast-button yoast-button-upsell"},(0,l.sprintf)(
// translators: %s expands to `Premium` (part of add-on name).
(0,l.__)("Unlock with %s","wordpress-seo"),"Premium"),(0,t.createElement)("span",{"aria-hidden":"true",className:"yoast-button-upsell__caret"})),(0,t.createElement)("p",null,o),(0,t.createElement)(Ps,{data:h,itemScreenReaderText:/* translators: Hidden accessibility text; %d expands to the number of occurrences. */
(0,l.__)("%d occurrences","wordpress-seo"),"aria-label":(0,l.__)("Prominent words","wordpress-seo"),className:r?"yoast-data-model--upsell":null}))};Ms.propTypes={location:y().string.isRequired};const Ds=Ms,qs=()=>{const e=(0,n.useSelect)((e=>e("yoast-seo/editor").getTextLength()),[]),s=(0,i.useMemo)((()=>(0,m.get)(window,"wpseoAdminL10n.shortlinks-insights-word_count","")),[]);let r=(0,l._n)("word","words",e.count,"wordpress-seo"),a=(0,l.__)("Word count","wordpress-seo"),o=(0,l.__)("Learn more about word count","wordpress-seo");return"character"===e.unit&&(r=(0,l._n)("character","characters",e.count,"wordpress-seo"),a=(0,l.__)("Character count","wordpress-seo"),
/* translators: Hidden accessibility text. */
o=(0,l.__)("Learn more about character count","wordpress-seo")),(0,t.createElement)(R.InsightsCard,{amount:e.count,unit:r,title:a,linkTo:s,linkText:o})},Ns=(0,Y.makeOutboundLink)(),Bs=({location:e})=>{const s=(0,i.useMemo)((()=>(0,m.get)(window,`wpseoAdminL10n.shortlinks-insights-upsell-${e}-text_formality`,"")),[e]),r=(0,i.useMemo)((()=>As((0,l.sprintf)(
// Translators: %1$s expands to a starting `b` tag, %2$s expands to a closing `b` tag and %3$s expands to `Yoast SEO Premium`.
(0,l.__)("%1$s%3$s%2$s will help you assess the formality level of your text.","wordpress-seo"),"<b>","</b>","Yoast SEO Premium"),{b:(0,t.createElement)("b",null)})),[]);return(0,t.createElement)(i.Fragment,null,(0,t.createElement)("div",null,(0,t.createElement)("p",null,r),(0,t.createElement)(Ns,{href:s,className:"yoast-button yoast-button-upsell"},(0,l.sprintf)(
// Translators: %s expands to `Premium` (part of add-on name).
(0,l.__)("Unlock with %s","wordpress-seo"),"Premium"),(0,t.createElement)("span",{"aria-hidden":"true",className:"yoast-button-upsell__caret"}))))};Bs.propTypes={location:y().string.isRequired};const $s=Bs,Us=({location:e,name:s})=>{const r=(0,n.useSelect)((e=>e("yoast-seo/editor").isFormalitySupported()),[]),a=h().isPremium,o=a?(0,m.get)(window,"wpseoAdminL10n.shortlinks-insights-text_formality_info_premium",""):(0,m.get)(window,"wpseoAdminL10n.shortlinks-insights-text_formality_info_free",""),i=(0,l.__)("Read more about text formality.","wordpress-seo");return r?(0,t.createElement)("div",{className:"yoast-text-formality"},(0,t.createElement)("div",{className:"yoast-field-group__title"},(0,t.createElement)("b",null,(0,l.__)("Text formality","wordpress-seo")),(0,t.createElement)(R.HelpIcon,{linkTo:o,linkText:i})),a?(0,t.createElement)(k.Slot,{name:s}):(0,t.createElement)($s,{location:e})):null};Us.propTypes={location:y().string.isRequired,name:y().string.isRequired};const Ws=Us,Ks=({location:e})=>{const s=(0,n.useSelect)((e=>e("yoast-seo/editor").isFleschReadingEaseAvailable()),[]);return(0,t.createElement)(ys,{title:(0,l.__)("Insights","wordpress-seo"),id:`yoast-insights-collapsible-${e}`,className:"yoast-insights"},(0,t.createElement)(Ds,{location:e}),(0,t.createElement)("div",null,s&&(0,t.createElement)("div",{className:"yoast-insights-row"},(0,t.createElement)(Es,null)),(0,t.createElement)("div",{className:"yoast-insights-row yoast-insights-row--columns"},(0,t.createElement)(fs,null),(0,t.createElement)(qs,null)),(0,hs.isFeatureEnabled)("TEXT_FORMALITY")&&(0,t.createElement)(Ws,{location:e,name:"YoastTextFormalityMetabox"})))};Ks.propTypes={location:y().string},Ks.defaultProps={location:"metabox"};const Hs=Ks,Vs=t.forwardRef((function(e,s){return t.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true",ref:s},e),t.createElement("path",{fillRule:"evenodd",d:"M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z",clipRule:"evenodd"}))})),Ys=()=>[(0,l.sprintf)(/* translators: %1$s expands to a strong opening tag, %2$s expands to a strong closing tag. */
(0,l.__)("%1$sAI%2$s: Better SEO titles and meta descriptions, faster.","wordpress-seo"),"<strong>","</strong>"),(0,l.sprintf)(/* translators: %1$s expands to a strong opening tag, %2$s expands to a strong closing tag. */
(0,l.__)("%1$sMultiple keywords%2$s: Rank higher for more searches.","wordpress-seo"),"<strong>","</strong>"),(0,l.sprintf)(/* translators: %1$s expands to a strong opening tag, %2$s expands to a strong closing tag. */
(0,l.__)("%1$sSuper fast%2$s internal linking suggestions.","wordpress-seo"),"<strong>","</strong>"),(0,l.sprintf)(/* translators: %1$s expands to a strong opening tag, %2$s expands to a strong closing tag. */
(0,l.__)("%1$sNo more broken links%2$s: Automatic redirect manager.","wordpress-seo"),"<strong>","</strong>"),(0,l.sprintf)(/* translators: %1$s expands to a strong opening tag, %2$s expands to a strong closing tag. */
(0,l.__)("%1$sAppealing social previews%2$s people actually want to click on.","wordpress-seo"),"<strong>","</strong>"),(0,l.sprintf)(/* translators: %1$s expands to a strong opening tag, %2$s expands to a strong closing tag. */
(0,l.__)("%1$s24/7 support%2$s: Also on evenings and weekends.","wordpress-seo"),"<strong>","</strong>")],js=w().div`
  padding: 25px 32px 32px;
  color: #303030;
`,zs=w().ul`
  margin: 0;
  padding: 0;

  li {
    list-style-image: var(--yoast-svg-icon-check);
    margin: 0.5rem 0 0 1.5rem;
    line-height: 1.4em;

    &::marker {
      font-size: 1.5rem;
    }
  }
`,Gs=w().span`
  display: block;
  margin-top: 4px;
`,Zs=w().h2`
  margin-top: 0;
  margin-bottom: 0.25rem;
  color: #303030;
  font-size: 0.8125rem;
  font-weight: 600;
`,Xs=w().p`
  display: block;
  margin: 0.25rem 0 1rem 0 !important;
  max-width: 420px;
`,Qs=w().hr`
  margin-top: 1.5rem;
  margin-bottom: 1rem;
  border-top: 0;
  border-bottom: 1px solid #E2E8F0;
`,Js=w().div`
  text-align: center;
`,er=w().a`
  width: 100%;
`,tr=(0,Y.makeOutboundLink)(er);class sr extends i.Component{constructor(e){super(e),this.state={defaultPrice:"99"}}createBenefitsList(e){return e.length>0&&(0,t.createElement)(zs,{role:"list"},e.map(((e,s)=>(0,t.createElement)("li",{key:`upsell-benefit-${s}`},(0,i.createInterpolateElement)(e,{strong:(0,t.createElement)("strong",null)})))))}render(){const e=(0,n.select)("yoast-seo/editor").isPromotionActive("black-friday-2023-promotion"),{defaultPrice:s}=this.state,r=e?"69.30":null,a=r||s;return(0,t.createElement)(i.Fragment,null,e&&(0,t.createElement)("div",{className:"yst-flex yst-justify-between yst-items-center yst-text-lg yst-content-between yst-bg-black yst-text-amber-300 yst-h-9 yst-border-amber-300 yst-border-y yst-border-x-0 yst-border-solid yst-px-6"},(0,t.createElement)("div",null,(0,l.__)("BLACK FRIDAY","wordpress-seo")),(0,t.createElement)("div",null,(0,l.__)("30% OFF","wordpress-seo"))),(0,t.createElement)(js,null,(0,t.createElement)(Zs,null,this.props.title),(0,t.createElement)(Xs,null,this.props.description),(0,t.createElement)(Js,null,(0,t.createElement)(tr,{...this.props.upsellButton},this.props.upsellButtonText,this.props.upsellButtonHasCaret&&(0,t.createElement)("span",{"aria-hidden":"true",className:"yoast-button-upsell__caret"})),(0,t.createElement)("div",{className:"yst-text-slate-600 yst-my-4"},r&&(0,t.createElement)(i.Fragment,null,(0,t.createElement)("span",{className:"yst-text-slate-500 yst-line-through"},s)," "),(0,t.createElement)("span",{className:"yst-text-slate-900 yst-text-2xl yst-font-bold"},a)," ",(0,l.__)("$ USD / € EUR / £ GBP per year (ex. VAT)","wordpress-seo")),(0,t.createElement)(Gs,{id:this.props.upsellButton["aria-describedby"]},this.props.upsellButtonLabel)),(0,t.createElement)(Qs,null),(0,t.createElement)(Zs,null,this.props.benefitsTitle),this.createBenefitsList(this.props.benefits)))}}sr.propTypes={title:y().node,benefits:y().array,benefitsTitle:y().node,description:y().node,upsellButton:y().object,upsellButtonText:y().string.isRequired,upsellButtonLabel:y().string,upsellButtonHasCaret:y().bool},sr.defaultProps={title:null,description:null,benefits:[],benefitsTitle:null,upsellButton:{href:"",className:"button button-primary"},upsellButtonLabel:"",upsellButtonHasCaret:!0};const rr=sr,ar=()=>{const[e,,,s,r]=(0,T.useToggleState)(!1),{locationContext:a}=(0,p.useRootContext)(),n=(0,T.useSvgAria)(),o=a.includes("sidebar"),i=a.includes("metabox"),c=wpseoAdminL10n[o?"shortlinks.upsell.sidebar.internal_linking_suggestions":"shortlinks.upsell.metabox.internal_linking_suggestions"];return(0,t.createElement)(t.Fragment,null,e&&(0,t.createElement)(X,{title:(0,l.__)("Get internal linking suggestions","wordpress-seo"),onRequestClose:r,additionalClassName:"",id:"yoast-internal-linking-suggestions-upsell",className:`${G} yoast-gutenberg-modal__box yoast-gutenberg-modal__no-padding`,shouldCloseOnClickOutside:!0},(0,t.createElement)(z,null,(0,t.createElement)(rr,{title:(0,l.__)("Rank higher by connecting your content","wordpress-seo"),description:(0,l.sprintf)(/* translators: %s expands to Yoast SEO Premium. */
(0,l.__)("%s automatically suggests to what content you can link with easy drag-and-drop functionality, which is good for your SEO!","wordpress-seo"),"Yoast SEO Premium"),benefitsTitle:/* translators: %s expands to 'Yoast SEO Premium'. */
(0,l.sprintf)("%s also gives you:","Yoast SEO Premium"),benefits:Ys(),upsellButtonText:(0,l.sprintf)(/* translators: %s expands to 'Yoast SEO Premium'. */
(0,l.__)("Unlock with %s","wordpress-seo"),"Yoast SEO Premium"),upsellButton:{href:(0,Fs.addQueryArgs)(c,{context:a}),className:"yoast-button-upsell",rel:null,"data-ctb-id":"f6a84663-465f-4cb5-8ba5-f7a6d72224b2","data-action":"load-nfd-ctb"},upsellButtonLabel:(0,l.__)("1 year free support and updates included!","wordpress-seo")}))),o&&(0,t.createElement)(re,{id:"yoast-internal-linking-suggestions-sidebar-modal-open-button",title:(0,l.__)("Internal linking suggestions","wordpress-seo"),onClick:s},(0,t.createElement)("div",{className:"yst-root"},(0,t.createElement)(T.Badge,{size:"small",variant:"upsell"},(0,t.createElement)(Vs,{className:"yst-w-2.5 yst-h-2.5 yst-shrink-0",...n})))),i&&(0,t.createElement)("div",{className:"yst-root"},(0,t.createElement)(V,{id:"yoast-internal-linking-suggestions-metabox-modal-open-button",onClick:s},(0,t.createElement)(V.Text,null,(0,l.__)("Internal linking suggestions","wordpress-seo")),(0,t.createElement)(T.Badge,{size:"small",variant:"upsell"},(0,t.createElement)(Vs,{className:"yst-w-2.5 yst-h-2.5 yst-mr-1 yst-shrink-0",...n}),(0,t.createElement)("span",null,"Premium")))))},nr=({children:e})=>(0,t.createElement)("div",null,e);nr.propTypes={renderPriority:y().number.isRequired,children:y().node.isRequired};const or=nr,ir=({noIndex:e,onNoIndexChange:s,editorContext:r,isPrivateBlog:a})=>{const n=(e=>{const t=(0,l.__)("No","wordpress-seo"),s=(0,l.__)("Yes","wordpress-seo"),r=e.noIndex?t:s;return window.wpseoScriptData.isPost?[{name:(0,l.sprintf)(/* translators: the first %s translates to "yes" or "no", the second %s translates to the content type label in plural form */
(0,l.__)("%s (current default for %s)","wordpress-seo"),r,e.postTypeNamePlural),value:"0"},{name:t,value:"1"},{name:s,value:"2"}]:[{name:(0,l.sprintf)(/* translators: the first %s translates to "yes" or "no", the second %s translates to the content type label in plural form */
(0,l.__)("%s (current default for %s)","wordpress-seo"),r,e.postTypeNamePlural),value:"default"},{name:s,value:"index"},{name:t,value:"noindex"}]})(r);return(0,t.createElement)(p.LocationConsumer,null,(r=>(0,t.createElement)(i.Fragment,null,a&&(0,t.createElement)(R.Alert,{type:"warning"},(0,l.__)("Even though you can set the meta robots setting here, the entire site is set to noindex in the sitewide privacy settings, so these settings won't have an effect.","wordpress-seo")),(0,t.createElement)(R.Select,{label:(0,l.__)("Allow search engines to show this content in search results?","wordpress-seo"),onChange:s,id:(0,Y.join)(["yoast-meta-robots-noindex",r]),options:n,selected:e,linkTo:wpseoAdminL10n["shortlinks.advanced.allow_search_engines"]
/* translators: Hidden accessibility text. */,linkText:(0,l.__)("Learn more about the no-index setting on our help page.","wordpress-seo")}))))};ir.propTypes={noIndex:y().string.isRequired,onNoIndexChange:y().func.isRequired,editorContext:y().object.isRequired,isPrivateBlog:y().bool},ir.defaultProps={isPrivateBlog:!1};const lr=({noFollow:e,onNoFollowChange:s})=>(0,t.createElement)(p.LocationConsumer,null,(r=>{const a=(0,Y.join)(["yoast-meta-robots-nofollow",r]);return(0,t.createElement)(R.RadioButtonGroup,{id:a,options:[{value:"0",label:"Yes"},{value:"1",label:"No"}],label:(0,l.__)("Should search engines follow links on this content?","wordpress-seo"),groupName:a,onChange:s,selected:e,linkTo:wpseoAdminL10n["shortlinks.advanced.follow_links"]
/* translators: Hidden accessibility text. */,linkText:(0,l.__)("Learn more about the no-follow setting on our help page.","wordpress-seo")})}));lr.propTypes={noFollow:y().string.isRequired,onNoFollowChange:y().func.isRequired};const cr=({advanced:e,onAdvancedChange:s})=>(0,t.createElement)(p.LocationConsumer,null,(r=>{const a=(0,Y.join)(["yoast-meta-robots-advanced",r]),n=`${a}-input`;return(0,t.createElement)(R.MultiSelect,{label:(0,l.__)("Meta robots advanced","wordpress-seo"),onChange:s,id:a,inputId:n,options:[{name:(0,l.__)("No Image Index","wordpress-seo"),value:"noimageindex"},{name:(0,l.__)("No Archive","wordpress-seo"),value:"noarchive"},{name:(0,l.__)("No Snippet","wordpress-seo"),value:"nosnippet"}],selected:e,linkTo:wpseoAdminL10n["shortlinks.advanced.meta_robots"]
/* translators: Hidden accessibility text. */,linkText:(0,l.__)("Learn more about advanced meta robots settings on our help page.","wordpress-seo")})}));cr.propTypes={advanced:y().array.isRequired,onAdvancedChange:y().func.isRequired};const dr=({breadcrumbsTitle:e,onBreadcrumbsTitleChange:s})=>(0,t.createElement)(p.LocationConsumer,null,(r=>(0,t.createElement)(R.TextInput,{label:(0,l.__)("Breadcrumbs Title","wordpress-seo"),id:(0,Y.join)(["yoast-breadcrumbs-title",r]),onChange:s,value:e,linkTo:wpseoAdminL10n["shortlinks.advanced.breadcrumbs_title"]
/* translators: Hidden accessibility text. */,linkText:(0,l.__)("Learn more about the breadcrumbs title setting on our help page.","wordpress-seo")})));dr.propTypes={breadcrumbsTitle:y().string.isRequired,onBreadcrumbsTitleChange:y().func.isRequired};const pr=({canonical:e,onCanonicalChange:s})=>(0,t.createElement)(p.LocationConsumer,null,(r=>(0,t.createElement)(R.TextInput,{label:(0,l.__)("Canonical URL","wordpress-seo"),id:(0,Y.join)(["yoast-canonical",r]),onChange:s,value:e,linkTo:"https://yoa.st/canonical-url"
/* translators: Hidden accessibility text. */,linkText:(0,l.__)("Learn more about canonical URLs on our help page.","wordpress-seo")})));pr.propTypes={canonical:y().string.isRequired,onCanonicalChange:y().func.isRequired};const ur=e=>{const{noIndex:s,noFollow:r,advanced:a,breadcrumbsTitle:n,canonical:o,onNoIndexChange:l,onNoFollowChange:c,onAdvancedChange:d,onBreadcrumbsTitleChange:p,onCanonicalChange:u,onLoad:m,isLoading:h,editorContext:g,isBreadcrumbsDisabled:y,isPrivateBlog:f}=e;(0,i.useEffect)((()=>{setTimeout((()=>{h&&m()}))}));const w={noIndex:s,onNoIndexChange:l,editorContext:g,isPrivateBlog:f},b={noFollow:r,onNoFollowChange:c},E={advanced:a,onAdvancedChange:d},v={breadcrumbsTitle:n,onBreadcrumbsTitleChange:p},k={canonical:o,onCanonicalChange:u};return h?null:(0,t.createElement)(i.Fragment,null,(0,t.createElement)(ir,{...w}),g.isPost&&(0,t.createElement)(lr,{...b}),g.isPost&&(0,t.createElement)(cr,{...E}),!y&&(0,t.createElement)(dr,{...v}),(0,t.createElement)(pr,{...k}))};ur.propTypes={noIndex:y().string.isRequired,canonical:y().string.isRequired,onNoIndexChange:y().func.isRequired,onCanonicalChange:y().func.isRequired,onLoad:y().func.isRequired,isLoading:y().bool.isRequired,editorContext:y().object.isRequired,isBreadcrumbsDisabled:y().bool.isRequired,isPrivateBlog:y().bool,advanced:y().array,onAdvancedChange:y().func,noFollow:y().string,onNoFollowChange:y().func,breadcrumbsTitle:y().string,onBreadcrumbsTitleChange:y().func},ur.defaultProps={advanced:[],onAdvancedChange:()=>{},noFollow:"",onNoFollowChange:()=>{},breadcrumbsTitle:"",onBreadcrumbsTitleChange:()=>{},isPrivateBlog:!1};const mr=ur,hr=(0,B.compose)([(0,n.withSelect)((e=>{const{getNoIndex:t,getNoFollow:s,getAdvanced:r,getBreadcrumbsTitle:a,getCanonical:n,getIsLoading:o,getEditorContext:i,getPreferences:l}=e("yoast-seo/editor"),{isBreadcrumbsDisabled:c,isPrivateBlog:d}=l();return{noIndex:t(),noFollow:s(),advanced:r(),breadcrumbsTitle:a(),canonical:n(),isLoading:o(),editorContext:i(),isBreadcrumbsDisabled:c,isPrivateBlog:d}})),(0,n.withDispatch)((e=>{const{setNoIndex:t,setNoFollow:s,setAdvanced:r,setBreadcrumbsTitle:a,setCanonical:n,loadAdvancedSettingsData:o}=e("yoast-seo/editor");return{onNoIndexChange:t,onNoFollowChange:s,onAdvancedChange:r,onBreadcrumbsTitleChange:a,onCanonicalChange:n,onLoad:o}}))])(mr),gr=w().p`
	color: #606770;
	flex-shrink: 0;
	font-size: 12px;
	line-height: 16px;
	overflow: hidden;
	padding: 0;
	text-overflow: ellipsis;
	text-transform: uppercase;
	white-space: nowrap;
	margin: 0;
	position: ${e=>"landscape"===e.mode?"relative":"static"};
`,yr=e=>{const{siteUrl:s}=e;return(0,t.createElement)(t.Fragment,null,(0,t.createElement)("span",{className:"screen-reader-text"},s),(0,t.createElement)(gr,{"aria-hidden":"true"},(0,t.createElement)("span",null,s)))};yr.propTypes={siteUrl:y().string.isRequired};const fr=yr,wr=window.yoast.socialMetadataForms,br=w().img`
	&& {
		max-width: ${e=>e.width}px;
		height: ${e=>e.height}px;
		position: absolute;
		top: 50%;
		left: 50%;
		transform: translate(-50%, -50%);
		max-width: none;
	}
`,Er=w().img`
	&&{
		height: 100%;
		position: absolute;
		width: 100%;
		object-fit: cover;
	}
`,vr=w().div`
	padding-bottom: ${e=>e.aspectRatio}%;
`,kr=e=>{const{imageProps:s,width:r,height:a,imageMode:n}=e;return"landscape"===n?(0,t.createElement)(vr,{aspectRatio:s.aspectRatio},(0,t.createElement)(Er,{src:s.src,alt:s.alt})):(0,t.createElement)(br,{src:s.src,alt:s.alt,width:r,height:a,imageProperties:s})};function _r(e,t,s){return"landscape"===s?{widthRatio:t.width/e.landscapeWidth,heightRatio:t.height/e.landscapeHeight}:"portrait"===s?{widthRatio:t.width/e.portraitWidth,heightRatio:t.height/e.portraitHeight}:{widthRatio:t.width/e.squareWidth,heightRatio:t.height/e.squareHeight}}function xr(e,t){return t.widthRatio<=t.heightRatio?{width:Math.round(e.width/t.widthRatio),height:Math.round(e.height/t.widthRatio)}:{width:Math.round(e.width/t.heightRatio),height:Math.round(e.height/t.heightRatio)}}async function Sr(e,t,s=!1){const r=await function(e){return new Promise(((t,s)=>{const r=new Image;r.onload=()=>{t({width:r.width,height:r.height})},r.onerror=s,r.src=e}))}(e);let a=s?"landscape":"square";"Facebook"===t&&(a=(0,wr.determineFacebookImageMode)(r));const n=function(e){return"Twitter"===e?wr.TWITTER_IMAGE_SIZES:wr.FACEBOOK_IMAGE_SIZES}(t),o=function(e,t,s){return"square"===s&&t.width===t.height?{width:e.squareWidth,height:e.squareHeight}:xr(t,_r(e,t,s))}(n,r,a);return{mode:a,height:o.height,width:o.width}}async function Tr(e,t,s=!1){try{return{imageProperties:await Sr(e,t,s),status:"loaded"}}catch(e){return{imageProperties:null,status:"errored"}}}kr.propTypes={imageProps:y().shape({src:y().string.isRequired,alt:y().string.isRequired,aspectRatio:y().number.isRequired}).isRequired,width:y().number.isRequired,height:y().number.isRequired,imageMode:y().string},kr.defaultProps={imageMode:"landscape"};const Rr=w().div`
	position: relative;
	${e=>"landscape"===e.mode?`max-width: ${e.dimensions.width}`:`min-width: ${e.dimensions.width}; height: ${e.dimensions.height}`};
	overflow: hidden;
	background-color: ${C.colors.$color_white};
`,Cr=w().div`
	box-sizing: border-box;
	max-width: ${wr.FACEBOOK_IMAGE_SIZES.landscapeWidth}px;
	height: ${wr.FACEBOOK_IMAGE_SIZES.landscapeHeight}px;
	background-color: ${C.colors.$color_grey};
	border-style: dashed;
	border-width: 1px;
	// We're not using standard colors to increase contrast for accessibility.
	color: #006DAC;
	// We're not using standard colors to increase contrast for accessibility.
	background-color: #f1f1f1;
	display: flex;
	justify-content: center;
	align-items: center;
	text-decoration: underline;
	font-size: 14px;
	cursor: pointer;
`;class Ir extends t.Component{constructor(e){super(e),this.state={imageProperties:null,status:"loading"},this.socialMedium="Facebook",this.handleFacebookImage=this.handleFacebookImage.bind(this),this.setState=this.setState.bind(this)}async handleFacebookImage(){try{const e=await Tr(this.props.src,this.socialMedium);this.setState(e),this.props.onImageLoaded(e.imageProperties.mode||"landscape")}catch(e){this.setState(e),this.props.onImageLoaded("landscape")}}componentDidUpdate(e){e.src!==this.props.src&&this.handleFacebookImage()}componentDidMount(){this.handleFacebookImage()}retrieveContainerDimensions(e){switch(e){case"square":return{height:wr.FACEBOOK_IMAGE_SIZES.squareHeight+"px",width:wr.FACEBOOK_IMAGE_SIZES.squareWidth+"px"};case"portrait":return{height:wr.FACEBOOK_IMAGE_SIZES.portraitHeight+"px",width:wr.FACEBOOK_IMAGE_SIZES.portraitWidth+"px"};case"landscape":return{height:wr.FACEBOOK_IMAGE_SIZES.landscapeHeight+"px",width:wr.FACEBOOK_IMAGE_SIZES.landscapeWidth+"px"}}}render(){const{imageProperties:e,status:s}=this.state;if("loading"===s||""===this.props.src||"errored"===s)return(0,t.createElement)(Cr,{onClick:this.props.onImageClick,onMouseEnter:this.props.onMouseEnter,onMouseLeave:this.props.onMouseLeave},(0,l.__)("Select image","wordpress-seo"));const r=this.retrieveContainerDimensions(e.mode);return(0,t.createElement)(Rr,{mode:e.mode,dimensions:r,onMouseEnter:this.props.onMouseEnter,onMouseLeave:this.props.onMouseLeave,onClick:this.props.onImageClick},(0,t.createElement)(kr,{imageProps:{src:this.props.src,alt:this.props.alt,aspectRatio:wr.FACEBOOK_IMAGE_SIZES.aspectRatio},width:e.width,height:e.height,imageMode:e.mode}))}}Ir.propTypes={src:y().string,alt:y().string,onImageLoaded:y().func,onImageClick:y().func,onMouseEnter:y().func,onMouseLeave:y().func},Ir.defaultProps={src:"",alt:"",onImageLoaded:m.noop,onImageClick:m.noop,onMouseEnter:m.noop,onMouseLeave:m.noop};const Ar=Ir,Lr=w().span`
	line-height: ${20}px;
	min-height : ${20}px;
	color: #1d2129;
	font-weight: 600;
	overflow: hidden;
	font-size: 16px;
	margin: 3px 0 0;
	letter-spacing: normal;
	white-space: normal;
	flex-shrink: 0;
	cursor: pointer;
	display: -webkit-box;
	-webkit-line-clamp: ${e=>e.lineCount};
	-webkit-box-orient: vertical;
	overflow: hidden;
`,Pr=w().p`
	line-height: ${16}px;
	min-height : ${16}px;
	color: #606770;
	font-size: 14px;
	padding: 0;
	text-overflow: ellipsis;
	margin: 3px 0 0 0;
	display: -webkit-box;
	cursor: pointer;
	-webkit-line-clamp: ${e=>e.lineCount};
	-webkit-box-orient: vertical;
	overflow: hidden;

	@media all and ( max-width: ${e=>e.maxWidth} ) {
		display: none;
	}
`,Fr=e=>{switch(e){case"landscape":return"527px";case"square":case"portrait":return"369px";default:return"476px"}},Or=w().div`
	box-sizing: border-box;
	display: flex;
	flex-direction: ${e=>"landscape"===e.mode?"column":"row"};
	background-color: #f2f3f5;
	max-width: 527px;
`,Mr=w().div`
	box-sizing: border-box;
	background-color: #f2f3f5;
	margin: 0;
	padding: 10px 12px;
	position: relative;
	border-bottom: ${e=>"landscape"===e.mode?"":"1px solid #dddfe2"};
	border-top: ${e=>"landscape"===e.mode?"":"1px solid #dddfe2"};
	border-right: ${e=>"landscape"===e.mode?"":"1px solid #dddfe2"};
	border: ${e=>"landscape"===e.mode?"1px solid #dddfe2":""};
	display: flex;
	flex-direction: column;
	flex-grow: 1;
	justify-content: ${e=>"landscape"===e.mode?"flex-start":"center"};
	font-size: 12px;
	overflow: hidden;
`;class Dr extends t.Component{constructor(e){super(e),this.state={imageMode:null,maxLineCount:0,descriptionLineCount:0},this.facebookTitleRef=r().createRef(),this.onImageLoaded=this.onImageLoaded.bind(this),this.onImageEnter=this.props.onMouseHover.bind(this,"image"),this.onTitleEnter=this.props.onMouseHover.bind(this,"title"),this.onDescriptionEnter=this.props.onMouseHover.bind(this,"description"),this.onLeave=this.props.onMouseHover.bind(this,""),this.onSelectTitle=this.props.onSelect.bind(this,"title"),this.onSelectDescription=this.props.onSelect.bind(this,"description")}onImageLoaded(e){this.setState({imageMode:e})}getTitleLineCount(){return this.facebookTitleRef.current.offsetHeight/20}maybeSetMaxLineCount(){const{imageMode:e,maxLineCount:t}=this.state,s="landscape"===e?2:5;s!==t&&this.setState({maxLineCount:s})}maybeSetDescriptionLineCount(){const{descriptionLineCount:e,maxLineCount:t,imageMode:s}=this.state,r=this.getTitleLineCount();let a=t-r;"portrait"===s&&(a=5===r?0:4),a!==e&&this.setState({descriptionLineCount:a})}componentDidUpdate(){this.maybeSetMaxLineCount(),this.maybeSetDescriptionLineCount()}render(){const{imageMode:e,maxLineCount:s,descriptionLineCount:r}=this.state;return(0,t.createElement)(Or,{id:"facebookPreview",mode:e},(0,t.createElement)(Ar,{src:this.props.imageUrl||this.props.imageFallbackUrl,alt:this.props.alt,onImageLoaded:this.onImageLoaded,onImageClick:this.props.onImageClick,onMouseEnter:this.onImageEnter,onMouseLeave:this.onLeave}),(0,t.createElement)(Mr,{mode:e},(0,t.createElement)(fr,{siteUrl:this.props.siteUrl,mode:e}),(0,t.createElement)(Lr,{ref:this.facebookTitleRef,onMouseEnter:this.onTitleEnter,onMouseLeave:this.onLeave,onClick:this.onSelectTitle,lineCount:s},this.props.title),r>0&&(0,t.createElement)(Pr,{maxWidth:Fr(e),onMouseEnter:this.onDescriptionEnter,onMouseLeave:this.onLeave,onClick:this.onSelectDescription,lineCount:r},this.props.description)))}}Dr.propTypes={siteUrl:y().string.isRequired,title:y().string.isRequired,description:y().string,imageUrl:y().string,imageFallbackUrl:y().string,alt:y().string,onSelect:y().func,onImageClick:y().func,onMouseHover:y().func},Dr.defaultProps={description:"",alt:"",imageUrl:"",imageFallbackUrl:"",onSelect:()=>{},onImageClick:()=>{},onMouseHover:()=>{}};const qr=Dr,Nr=w().div`
	text-transform: lowercase;
	color: rgb(83, 100, 113);
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
	margin: 0;
	fill: currentcolor;
	display: flex;
	flex-direction: row;
	align-items: flex-end;
`,Br=e=>(0,t.createElement)(Nr,null,(0,t.createElement)("span",null,e.siteUrl));Br.propTypes={siteUrl:y().string.isRequired};const $r=Br,Ur=(e,t=!0)=>e?`\n\t\t\tmax-width: ${wr.TWITTER_IMAGE_SIZES.landscapeWidth}px;\n\t\t\t${t?"border-bottom: 1px solid #E1E8ED;":""}\n\t\t\tborder-radius: 14px 14px 0 0;\n\t\t\t`:`\n\t\twidth: ${wr.TWITTER_IMAGE_SIZES.squareWidth}px;\n\t\t${t?"border-right: 1px solid #E1E8ED;":""}\n\t\tborder-radius: 14px 0 0 14px;\n\t\t`,Wr=w().div`
	position: relative;
	box-sizing: content-box;
	overflow: hidden;
	background-color: #e1e8ed;
	flex-shrink: 0;
	${e=>Ur(e.isLarge)}
`,Kr=w().div`
	display: flex;
	justify-content: center;
	align-items: center;
	box-sizing: border-box;
	max-width: 100%;
	margin: 0;
	padding: 1em;
	text-align: center;
	font-size: 1rem;
	${e=>Ur(e.isLarge,!1)}
`,Hr=w()(Kr)`
	${e=>e.isLarge&&`height: ${wr.TWITTER_IMAGE_SIZES.landscapeHeight}px;`}
	border-top-left-radius: 14px;
	${e=>e.isLarge?"border-top-right-radius":"border-bottom-left-radius"}: 14px;
	border-style: dashed;
	border-width: 1px;
	// We're not using standard colors to increase contrast for accessibility.
	color: #006DAC;
	// We're not using standard colors to increase contrast for accessibility.
	background-color: #f1f1f1;
	text-decoration: underline;
	font-size: 14px;
	cursor: pointer;
`;class Vr extends r().Component{constructor(e){super(e),this.state={status:"loading"},this.socialMedium="Twitter",this.handleTwitterImage=this.handleTwitterImage.bind(this),this.setState=this.setState.bind(this)}async handleTwitterImage(){if(null===this.props.src)return;const e=await Tr(this.props.src,this.socialMedium,this.props.isLarge);this.setState(e)}componentDidUpdate(e){e.src!==this.props.src&&this.handleTwitterImage()}componentDidMount(){this.handleTwitterImage()}render(){const{status:e,imageProperties:s}=this.state;return"loading"===e||""===this.props.src||"errored"===e?(0,t.createElement)(Hr,{isLarge:this.props.isLarge,onClick:this.props.onImageClick,onMouseEnter:this.props.onMouseEnter,onMouseLeave:this.props.onMouseLeave},(0,l.__)("Select image","wordpress-seo")):(0,t.createElement)(Wr,{isLarge:this.props.isLarge,onClick:this.props.onImageClick,onMouseEnter:this.props.onMouseEnter,onMouseLeave:this.props.onMouseLeave},(0,t.createElement)(kr,{imageProps:{src:this.props.src,alt:this.props.alt,aspectRatio:wr.TWITTER_IMAGE_SIZES.aspectRatio},width:s.width,height:s.height,imageMode:s.mode}))}}Vr.propTypes={isLarge:y().bool.isRequired,src:y().string,alt:y().string,onImageClick:y().func,onMouseEnter:y().func,onMouseLeave:y().func},Vr.defaultProps={src:"",alt:"",onMouseEnter:m.noop,onImageClick:m.noop,onMouseLeave:m.noop};const Yr=w().div`
	display: flex;
	flex-direction: column;
	padding: 12px;
	justify-content: center;
	margin: 0;
	box-sizing: border-box;
	flex: auto;
	min-width: 0px;
	gap:2px;
	> * {
		line-height:20px;
		min-height:20px;
		font-size:15px;
    }
`,jr=e=>(0,t.createElement)(Yr,null,e.children);jr.propTypes={children:y().array.isRequired};const zr=jr,Gr=w().p`
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
	margin: 0;
	color: rgb(15, 20, 25);
	cursor: pointer;
`,Zr=w().p`
	max-height: 55px;
	overflow: hidden;
	text-overflow: ellipsis;
	margin: 0;
	color: rgb(83, 100, 113);
	display: -webkit-box;
	cursor: pointer;
	-webkit-line-clamp: 2;
	-webkit-box-orient: vertical;

	@media all and ( max-width: ${wr.TWITTER_IMAGE_SIZES.landscapeWidth}px ) {
		display: none;
	}
`,Xr=w().div`
	font-family: system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Ubuntu, "Helvetica Neue", sans-serif;
	font-size: 15px;
	font-weight: 400;
	line-height: 20px;
	max-width: 507px;
	border: 1px solid #E1E8ED;
	box-sizing: border-box;
	border-radius: 14px;
	color: #292F33;
	background: #FFFFFF;
	text-overflow: ellipsis;
	display: flex;

	&:hover {
		background: #f5f8fa;
		border: 1px solid rgba(136,153,166,.5);
	}
`,Qr=w()(Xr)`
	flex-direction: column;
	max-height: 370px;
`,Jr=w()(Xr)`
	flex-direction: row;
	height: 125px;
`;class ea extends t.Component{constructor(e){super(e),this.onImageEnter=this.props.onMouseHover.bind(this,"image"),this.onTitleEnter=this.props.onMouseHover.bind(this,"title"),this.onDescriptionEnter=this.props.onMouseHover.bind(this,"description"),this.onLeave=this.props.onMouseHover.bind(this,""),this.onSelectTitle=this.props.onSelect.bind(this,"title"),this.onSelectDescription=this.props.onSelect.bind(this,"description")}render(){const{isLarge:e,imageUrl:s,imageFallbackUrl:r,alt:a,title:n,description:o,siteUrl:i}=this.props,l=e?Qr:Jr;return(0,t.createElement)(l,{id:"twitterPreview"},(0,t.createElement)(Vr,{src:s||r,alt:a,isLarge:e,onImageClick:this.props.onImageClick,onMouseEnter:this.onImageEnter,onMouseLeave:this.onLeave}),(0,t.createElement)(zr,null,(0,t.createElement)($r,{siteUrl:i}),(0,t.createElement)(Gr,{onMouseEnter:this.onTitleEnter,onMouseLeave:this.onLeave,onClick:this.onSelectTitle},n),(0,t.createElement)(Zr,{onMouseEnter:this.onDescriptionEnter,onMouseLeave:this.onLeave,onClick:this.onSelectDescription},o)))}}ea.propTypes={siteUrl:y().string.isRequired,title:y().string.isRequired,description:y().string,isLarge:y().bool,imageUrl:y().string,imageFallbackUrl:y().string,alt:y().string,onSelect:y().func,onImageClick:y().func,onMouseHover:y().func},ea.defaultProps={description:"",alt:"",imageUrl:"",imageFallbackUrl:"",onSelect:()=>{},onImageClick:()=>{},onMouseHover:()=>{},isLarge:!0};const ta=ea,sa=window.yoast.replacementVariableEditor;class ra extends t.Component{constructor(e){super(e),this.state={activeField:"",hoveredField:""},this.SocialPreview="Social"===e.socialMediumName?qr:ta,this.setHoveredField=this.setHoveredField.bind(this),this.setActiveField=this.setActiveField.bind(this),this.setEditorRef=this.setEditorRef.bind(this),this.setEditorFocus=this.setEditorFocus.bind(this)}setHoveredField(e){e!==this.state.hoveredField&&this.setState({hoveredField:e})}setActiveField(e){e!==this.state.activeField&&this.setState({activeField:e},(()=>this.setEditorFocus(e)))}setEditorFocus(e){switch(e){case"title":this.titleEditorRef.focus();break;case"description":this.descriptionEditorRef.focus()}}setEditorRef(e,t){switch(e){case"title":this.titleEditorRef=t;break;case"description":this.descriptionEditorRef=t}}render(){const{onDescriptionChange:e,onTitleChange:s,onSelectImageClick:a,onRemoveImageClick:n,socialMediumName:o,imageWarnings:i,siteUrl:l,description:c,descriptionInputPlaceholder:d,descriptionPreviewFallback:p,imageUrl:u,imageFallbackUrl:m,alt:h,title:g,titleInputPlaceholder:y,titlePreviewFallback:f,replacementVariables:w,recommendedReplacementVariables:b,applyReplacementVariables:E,onReplacementVariableSearchChange:v,isPremium:k,isLarge:_,socialPreviewLabel:x,idSuffix:S,activeMetaTabId:T}=this.props,C=E({title:g||f,description:c||p});return(0,t.createElement)(r().Fragment,null,x&&(0,t.createElement)(R.SimulatedLabel,null,x),(0,t.createElement)(this.SocialPreview,{onMouseHover:this.setHoveredField,onSelect:this.setActiveField,onImageClick:a,siteUrl:l,title:C.title,description:C.description,imageUrl:u,imageFallbackUrl:m,alt:h,isLarge:_,activeMetaTabId:T}),(0,t.createElement)(wr.SocialMetadataPreviewForm,{onDescriptionChange:e,socialMediumName:o,title:g,titleInputPlaceholder:y,onRemoveImageClick:n,imageSelected:!!u,imageUrl:u,onTitleChange:s,onSelectImageClick:a,description:c,descriptionInputPlaceholder:d,imageWarnings:i,replacementVariables:w,recommendedReplacementVariables:b,onReplacementVariableSearchChange:v,onMouseHover:this.setHoveredField,hoveredField:this.state.hoveredField,onSelect:this.setActiveField,activeField:this.state.activeField,isPremium:k,setEditorRef:this.setEditorRef,idSuffix:S}))}}ra.propTypes={title:y().string.isRequired,onTitleChange:y().func.isRequired,description:y().string.isRequired,onDescriptionChange:y().func.isRequired,imageUrl:y().string.isRequired,imageFallbackUrl:y().string.isRequired,onSelectImageClick:y().func.isRequired,onRemoveImageClick:y().func.isRequired,socialMediumName:y().string.isRequired,alt:y().string,isPremium:y().bool,imageWarnings:y().array,isLarge:y().bool,siteUrl:y().string,descriptionInputPlaceholder:y().string,titleInputPlaceholder:y().string,descriptionPreviewFallback:y().string,titlePreviewFallback:y().string,replacementVariables:sa.replacementVariablesShape,recommendedReplacementVariables:sa.recommendedReplacementVariablesShape,applyReplacementVariables:y().func,onReplacementVariableSearchChange:y().func,socialPreviewLabel:y().string,idSuffix:y().string,activeMetaTabId:y().string},ra.defaultProps={imageWarnings:[],recommendedReplacementVariables:[],replacementVariables:[],isPremium:!1,isLarge:!0,siteUrl:"",descriptionInputPlaceholder:"",titleInputPlaceholder:"",descriptionPreviewFallback:"",titlePreviewFallback:"",alt:"",applyReplacementVariables:e=>e,onReplacementVariableSearchChange:null,socialPreviewLabel:"",idSuffix:"",activeMetaTabId:""};const aa={},na=(e,t,{log:s=console.warn}={})=>{aa[e]||(aa[e]=!0,s(t))},oa=(e,t=m.noop)=>{const s={};for(const r in e)Object.hasOwn(e,r)&&Object.defineProperty(s,r,{set:s=>{e[r]=s,t("set",r,s)},get:()=>(t("get",r),e[r])});return s};oa({squareWidth:125,squareHeight:125,landscapeWidth:506,landscapeHeight:265,aspectRatio:50.2},((e,t)=>na(`@yoast/social-metadata-previews/TWITTER_IMAGE_SIZES/${e}/${t}`,`[@yoast/social-metadata-previews] "TWITTER_IMAGE_SIZES.${t}" is deprecated and will be removed in the future, please use this from @yoast/social-metadata-forms instead.`))),oa({squareWidth:158,squareHeight:158,landscapeWidth:527,landscapeHeight:273,portraitWidth:158,portraitHeight:237,aspectRatio:52.2,largeThreshold:{width:446,height:233}},((e,t)=>na(`@yoast/social-metadata-previews/FACEBOOK_IMAGE_SIZES/${e}/${t}`,`[@yoast/social-metadata-previews] "FACEBOOK_IMAGE_SIZES.${t}" is deprecated and will be removed in the future, please use this from @yoast/social-metadata-forms instead.`)));const ia=w().div`
	max-width: calc(527px + 1.5rem);
`,la=e=>{const s="X"===e.socialMediumName?(0,l.__)("X share preview","wordpress-seo"):(0,l.__)("Social share preview","wordpress-seo"),{locationContext:r}=(0,T.useRootContext)();return(0,t.createElement)(T.Root,null,(0,t.createElement)(ia,null,(0,t.createElement)(T.FeatureUpsell,{shouldUpsell:!0,variant:"card",cardLink:(0,Fs.addQueryArgs)(wpseoAdminL10n["shortlinks.upsell.social_preview."+e.socialMediumName.toLowerCase()],{context:r}),cardText:(0,l.sprintf)(/* translators: %1$s expands to Yoast SEO Premium. */
(0,l.__)("Unlock with %1$s","wordpress-seo"),"Yoast SEO Premium"),"data-action":"load-nfd-ctb","data-ctb-id":"f6a84663-465f-4cb5-8ba5-f7a6d72224b2"},(0,t.createElement)("div",{className:"yst-grayscale yst-opacity-50"},(0,t.createElement)(T.Label,null,s),(0,t.createElement)(qr,{title:"",description:"",siteUrl:"",imageUrl:"",imageFallbackUrl:"",alt:"",onSelect:m.noop,onImageClick:m.noop,onMouseHover:m.noop})))))};la.propTypes={socialMediumName:y().oneOf(["Social","Twitter","X"]).isRequired};const ca=la;class da extends i.Component{constructor(e){super(e),this.state={activeField:"",hoveredField:""},this.setHoveredField=this.setHoveredField.bind(this),this.setActiveField=this.setActiveField.bind(this),this.setEditorRef=this.setEditorRef.bind(this),this.setEditorFocus=this.setEditorFocus.bind(this)}setHoveredField(e){e!==this.state.hoveredField&&this.setState({hoveredField:e})}setActiveField(e){e!==this.state.activeField&&this.setState({activeField:e},(()=>this.setEditorFocus(e)))}setEditorFocus(e){switch(e){case"title":this.titleEditorRef.focus();break;case"description":this.descriptionEditorRef.focus()}}setEditorRef(e,t){switch(e){case"title":this.titleEditorRef=t;break;case"description":this.descriptionEditorRef=t}}render(){const{onDescriptionChange:e,onTitleChange:s,onSelectImageClick:r,onRemoveImageClick:a,socialMediumName:n,imageWarnings:o,description:l,descriptionInputPlaceholder:c,imageUrl:d,alt:p,title:u,titleInputPlaceholder:m,replacementVariables:h,recommendedReplacementVariables:g,onReplacementVariableSearchChange:y,isPremium:f,location:w}=this.props;return(0,t.createElement)(i.Fragment,null,(0,t.createElement)(ca,{socialMediumName:n}),(0,t.createElement)(wr.SocialMetadataPreviewForm,{onDescriptionChange:e,socialMediumName:n,title:u,titleInputPlaceholder:m,onRemoveImageClick:a,imageSelected:!!d,imageUrl:d,imageAltText:p,onTitleChange:s,onSelectImageClick:r,description:l,descriptionInputPlaceholder:c,imageWarnings:o,replacementVariables:h,recommendedReplacementVariables:g,onReplacementVariableSearchChange:y,onMouseHover:this.setHoveredField,hoveredField:this.state.hoveredField,onSelect:this.setActiveField,activeField:this.state.activeField,isPremium:f,setEditorRef:this.setEditorRef,idSuffix:w}))}}da.propTypes={title:y().string.isRequired,onTitleChange:y().func.isRequired,description:y().string.isRequired,onDescriptionChange:y().func.isRequired,imageUrl:y().string.isRequired,onSelectImageClick:y().func.isRequired,onRemoveImageClick:y().func.isRequired,socialMediumName:y().string.isRequired,isPremium:y().bool,imageWarnings:y().array,descriptionInputPlaceholder:y().string,titleInputPlaceholder:y().string,replacementVariables:sa.replacementVariablesShape,recommendedReplacementVariables:sa.recommendedReplacementVariablesShape,onReplacementVariableSearchChange:y().func,location:y().string,alt:y().string},da.defaultProps={imageWarnings:[],recommendedReplacementVariables:[],replacementVariables:[],isPremium:!1,descriptionInputPlaceholder:"",titleInputPlaceholder:"",onReplacementVariableSearchChange:null,location:"",alt:""};const pa=da,ua=e=>{const[s,r]=(0,i.useState)(""),a=(0,i.useCallback)((e=>{r(e.detail.metaTabId)}),[r]);(0,i.useEffect)((()=>(setTimeout(e.onLoad),window.addEventListener("YoastSEO:metaTabChange",a),()=>{window.removeEventListener("YoastSEO:metaTabChange",a)})),[]);const n=(0,i.useMemo)((()=>({...e,activeMetaTabId:s})),[e,s]);return e.isPremium?(0,t.createElement)(k.Slot,{name:`YoastFacebookPremium${e.location.charAt(0).toUpperCase()+e.location.slice(1)}`,fillProps:n}):(0,t.createElement)(pa,{...n})};ua.propTypes={isPremium:y().bool.isRequired,onLoad:y().func.isRequired,location:y().string.isRequired};const ma=ua;function ha(e){(function(e){const t=window.wp.media();return t.on("select",(()=>{const s=t.state().get("selection").first();var r;e({type:(r=s.attributes).subtype,width:r.width,height:r.height,url:r.url,id:r.id,sizes:r.sizes,alt:r.alt||r.title||r.name})})),t})(e).open()}const ga=()=>{ha((e=>(0,n.dispatch)("yoast-seo/editor").setFacebookPreviewImage((e=>{const{width:t,height:s}=e,r=(0,wr.determineFacebookImageMode)({width:t,height:s}),a=wr.FACEBOOK_IMAGE_SIZES[r+"Width"],n=wr.FACEBOOK_IMAGE_SIZES[r+"Height"],o=Object.values(e.sizes).find((e=>e.width>=a&&e.height>=n));return{url:o?o.url:e.url,id:e.id,warnings:(0,Y.validateFacebookImage)(e),alt:e.alt||""}})(e))))},ya=(0,B.compose)([(0,n.withSelect)((e=>{const{getFacebookDescription:t,getDescription:s,getFacebookTitle:r,getSeoTitle:a,getFacebookImageUrl:n,getImageFallback:o,getFacebookWarnings:i,getRecommendedReplaceVars:l,getReplaceVars:c,getSiteUrl:d,getSeoTitleTemplate:p,getSeoTitleTemplateNoFallback:u,getSocialTitleTemplate:m,getSeoDescriptionTemplate:g,getSocialDescriptionTemplate:y,getReplacedExcerpt:f,getFacebookAltText:w}=e("yoast-seo/editor");return{imageUrl:n(),imageFallbackUrl:o(),recommendedReplacementVariables:l(),replacementVariables:c(),description:t(),descriptionPreviewFallback:y()||s()||g()||f()||"",title:r(),titlePreviewFallback:m()||a()||u()||p()||"",imageWarnings:i(),siteUrl:d(),isPremium:!!h().isPremium,titleInputPlaceholder:"",descriptionInputPlaceholder:"",socialMediumName:"Social",alt:w()}})),(0,n.withDispatch)(((e,t,{select:s})=>{const{setFacebookPreviewTitle:r,setFacebookPreviewDescription:a,clearFacebookPreviewImage:n,loadFacebookPreviewData:o,findCustomFields:i}=e("yoast-seo/editor"),l=s("yoast-seo/editor").getPostId();return{onSelectImageClick:ga,onRemoveImageClick:n,onDescriptionChange:a,onTitleChange:r,onLoad:o,onReplacementVariableSearchChange:is(l,i)}})),jt()])(ma),fa=e=>((0,i.useEffect)((()=>{setTimeout(e.onLoad)}),[]),e.isPremium?(0,t.createElement)(k.Slot,{name:`YoastTwitterPremium${e.location.charAt(0).toUpperCase()+e.location.slice(1)}`,fillProps:e}):(0,t.createElement)(pa,{...e}));fa.propTypes={isPremium:y().bool.isRequired,onLoad:y().func.isRequired,location:y().string.isRequired};const wa=fa,ba=()=>{ha((e=>(0,n.dispatch)("yoast-seo/editor").setTwitterPreviewImage((e=>{const t="summary"!==(0,m.get)(window,"wpseoScriptData.metabox.twitterCardType")?"landscape":"square",s=wr.TWITTER_IMAGE_SIZES[t+"Width"],r=wr.TWITTER_IMAGE_SIZES[t+"Height"],a=Object.values(e.sizes).find((e=>e.width>=s&&e.height>=r));return{url:a?a.url:e.url,id:e.id,warnings:(0,Y.validateTwitterImage)(e),alt:e.alt||""}})(e))))},Ea=(0,B.compose)([(0,n.withSelect)((e=>{const{getTwitterDescription:t,getTwitterTitle:s,getTwitterImageUrl:r,getFacebookImageUrl:a,getFacebookTitle:n,getFacebookDescription:o,getDescription:i,getSeoTitle:l,getTwitterWarnings:c,getTwitterImageType:d,getImageFallback:p,getRecommendedReplaceVars:u,getReplaceVars:m,getSiteUrl:g,getSeoTitleTemplate:y,getSeoTitleTemplateNoFallback:f,getSocialTitleTemplate:w,getSeoDescriptionTemplate:b,getSocialDescriptionTemplate:E,getReplacedExcerpt:v,getTwitterAltText:k}=e("yoast-seo/editor");return{imageUrl:r(),imageFallbackUrl:a()||p(),recommendedReplacementVariables:u(),replacementVariables:m(),description:t(),descriptionPreviewFallback:E()||o()||i()||b()||v()||"",title:s(),titlePreviewFallback:w()||n()||l()||f()||y()||"",imageWarnings:c(),siteUrl:g(),isPremium:!!h().isPremium,isLarge:"summary"!==d(),titleInputPlaceholder:"",descriptionInputPlaceholder:"",socialMediumName:"X",alt:k()}})),(0,n.withDispatch)(((e,t,{select:s})=>{const{setTwitterPreviewTitle:r,setTwitterPreviewDescription:a,clearTwitterPreviewImage:n,loadTwitterPreviewData:o,findCustomFields:i}=e("yoast-seo/editor"),l=s("yoast-seo/editor").getPostId();return{onSelectImageClick:ba,onRemoveImageClick:n,onDescriptionChange:a,onTitleChange:r,onLoad:o,onReplacementVariableSearchChange:is(l,i)}})),jt()])(wa),va=w().legend`
	margin: 16px 0;
	padding: 0;
	color: ${C.colors.$color_headings};
	font-size: 12px;
	font-weight: 300;
`,ka=w().legend`
	margin: 0 0 16px;
	padding: 0;
	color: ${C.colors.$color_headings};
	font-size: 12px;
	font-weight: 300;
`,_a=w().div`
	padding: 16px;
`,xa=({useOpenGraphData:e,useTwitterData:s})=>(0,t.createElement)(i.Fragment,null,s&&e&&(0,t.createElement)(i.Fragment,null,(0,t.createElement)(ys,{hasSeparator:!1
/* translators: Social media appearance refers to a preview of how a page will be represented on social media. */,title:(0,l.__)("Social media appearance","wordpress-seo"),initialIsOpen:!0},(0,t.createElement)(ka,null,(0,l.__)("Determine how your post should look on social media like Facebook, X, Instagram, WhatsApp, Threads, LinkedIn, Slack, and more.","wordpress-seo")),(0,t.createElement)(ya,null),(0,t.createElement)(va,null,(0,l.__)("To customize the appearance of your post specifically for X, please fill out the 'X appearance' settings below. If you leave these settings untouched, the 'Social media appearance' settings mentioned above will also be applied for sharing on X.","wordpress-seo"))),(0,t.createElement)(ys,{title:(0,l.__)("X appearance","wordpress-seo"),hasSeparator:!0,initialIsOpen:!1},(0,t.createElement)(Ea,null))),e&&!s&&(0,t.createElement)(_a,null,(0,t.createElement)(ka,null,(0,l.__)("Determine how your post should look on social media like Facebook, X, Instagram, WhatsApp, Threads, LinkedIn, Slack, and more.","wordpress-seo")),(0,t.createElement)(ya,null)),!e&&s&&(0,t.createElement)(_a,null,(0,t.createElement)(ka,null,(0,l.__)("To customize the appearance of your post specifically for X, please fill out the 'X appearance' settings below.","wordpress-seo")),(0,t.createElement)(Ea,null)));xa.propTypes={useOpenGraphData:y().bool.isRequired,useTwitterData:y().bool.isRequired};const Sa=xa,Ta=(0,n.withSelect)((e=>{const{getPreferences:t}=e("yoast-seo/editor"),{useOpenGraphData:s,useTwitterData:r}=t();return{useOpenGraphData:s,useTwitterData:r}}))(Sa);function Ra({target:e}){return(0,t.createElement)(L,{target:e},(0,t.createElement)(Ta,null))}Ra.propTypes={target:y().string.isRequired};const Ca=(0,Y.makeOutboundLink)(),Ia=w().div`
	padding: 16px;
`,Aa="yoast-seo/editor";function La({location:e,show:s}){return s?(0,t.createElement)(R.Alert,{type:"info"},(0,l.sprintf)(/* translators: %s Expands to "Yoast News SEO" */
(0,l.__)("Are you working on a news article? %s helps you optimize your site for Google News.","wordpress-seo"),"Yoast News SEO")+" ",(0,t.createElement)(Ca,{href:window.wpseoAdminL10n[`shortlinks.upsell.${e}.news`]},(0,l.sprintf)(/* translators: %s: Expands to "Yoast News SEO". */
(0,l.__)("Buy %s now!","wordpress-seo"),"Yoast News SEO"))):null}La.propTypes={show:y().bool.isRequired,location:y().string.isRequired};const Pa=(e,t,s)=>{const r=(0,n.useSelect)((e=>e(Aa).getIsProduct()),[]),a=(0,n.useSelect)((e=>e(Aa).getIsWooSeoActive()),[]),o=r&&a?{name:(0,l.__)("Item Page","wordpress-seo"),value:"ItemPage"}:e.find((e=>e.value===t));return[{name:(0,l.sprintf)(/* translators: %1$s expands to the plural name of the current post type, %2$s expands to the current site wide default. */
(0,l.__)("Default for %1$s (%2$s)","wordpress-seo"),s,o?o.name:""),value:""},...e]},Fa=e=>(0,l.sprintf)(/* translators: %1$s expands to the plural name of the current post type, %2$s and %3$s expand to a link to the Settings page */
(0,l.__)("You can change the default type for %1$s under Content types in the %2$sSettings%3$s.","wordpress-seo"),e,"{{link}}","{{/link}}");y().string.isRequired,y().string.isRequired,y().string.isRequired;const Oa=e=>{const s=Pa(e.pageTypeOptions,e.defaultPageType,e.postTypeName),r=Pa(e.articleTypeOptions,e.defaultArticleType,e.postTypeName),a=(0,m.get)(window,"wpseoScriptData.metabox.woocommerceUpsellSchemaLink",""),o=(0,m.get)(window,"wpseoScriptData.woocommerceUpsell",""),[c,d]=(0,i.useState)(e.schemaArticleTypeSelected),p=(0,l.__)("Want your products stand out in search results with rich results like price, reviews and more?","wordpress-seo"),u=(0,n.useSelect)((e=>e(Aa).getIsProduct()),[]),h=(0,n.useSelect)((e=>e(Aa).getIsWooSeoActive()),[]),g=u&&h,y=(0,i.useCallback)(((e,t)=>{d(t)}),[c]);return(0,i.useEffect)((()=>{y(null,e.schemaArticleTypeSelected)}),[e.schemaArticleTypeSelected]),(0,t.createElement)(i.Fragment,null,(0,t.createElement)(R.FieldGroup,{label:(0,l.__)("What type of page or content is this?","wordpress-seo"),linkTo:e.additionalHelpTextLink
/* translators: Hidden accessibility text. */,linkText:(0,l.__)("Learn more about page or content types","wordpress-seo")}),o&&(0,t.createElement)(ds,{link:a,text:p}),(0,t.createElement)(R.Select,{id:(0,Y.join)(["yoast-schema-page-type",e.location]),options:s,label:(0,l.__)("Page type","wordpress-seo"),onChange:e.schemaPageTypeChange,selected:g?"ItemPage":e.schemaPageTypeSelected,disabled:g}),e.showArticleTypeInput&&(0,t.createElement)(R.Select,{id:(0,Y.join)(["yoast-schema-article-type",e.location]),options:r,label:(0,l.__)("Article type","wordpress-seo"),onChange:e.schemaArticleTypeChange,selected:e.schemaArticleTypeSelected,onOptionFocus:y}),(0,t.createElement)(La,{location:e.location,show:!e.isNewsEnabled&&(w=c,b=e.defaultArticleType,"NewsArticle"===w||""===w&&"NewsArticle"===b)}),e.displayFooter&&!g&&(0,t.createElement)("p",null,(f=e.postTypeName,(0,oe.Z)({mixedString:Fa(f),components:{link:(0,t.createElement)("a",{href:"/wp-admin/admin.php?page=wpseo_page_settings",target:"_blank"})}}))),g&&(0,t.createElement)("p",null,(0,l.sprintf)(/* translators: %1$s expands to Yoast WooCommerce SEO. */
(0,l.__)("You have %1$s activated on your site, automatically setting the Page type for your products to 'Item Page'. As a result, the Page type selection is disabled.","wordpress-seo"),"Yoast WooCommerce SEO")));var f,w,b},Ma=y().arrayOf(y().shape({name:y().string,value:y().string}));Oa.propTypes={schemaPageTypeChange:y().func,schemaPageTypeSelected:y().string,pageTypeOptions:Ma.isRequired,schemaArticleTypeChange:y().func,schemaArticleTypeSelected:y().string,articleTypeOptions:Ma.isRequired,showArticleTypeInput:y().bool.isRequired,additionalHelpTextLink:y().string.isRequired,helpTextLink:y().string.isRequired,helpTextTitle:y().string.isRequired,helpTextDescription:y().string.isRequired,postTypeName:y().string.isRequired,displayFooter:y().bool,defaultPageType:y().string.isRequired,defaultArticleType:y().string.isRequired,location:y().string.isRequired,isNewsEnabled:y().bool},Oa.defaultProps={schemaPageTypeChange:()=>{},schemaPageTypeSelected:null,schemaArticleTypeChange:()=>{},schemaArticleTypeSelected:null,displayFooter:!1,isNewsEnabled:!1};const Da=e=>e.isMetabox?(0,i.createPortal)((0,t.createElement)(Ia,null,(0,t.createElement)(Oa,{...e})),document.getElementById("wpseo-meta-section-schema")):(0,t.createElement)(Oa,{...e});Da.propTypes={showArticleTypeInput:y().bool,articleTypeLabel:y().string,additionalHelpTextLink:y().string,pageTypeLabel:y().string.isRequired,helpTextLink:y().string.isRequired,helpTextTitle:y().string.isRequired,helpTextDescription:y().string.isRequired,isMetabox:y().bool.isRequired,postTypeName:y().string.isRequired,displayFooter:y().bool,loadSchemaArticleData:y().func.isRequired,loadSchemaPageData:y().func.isRequired,location:y().string.isRequired},Da.defaultProps={showArticleTypeInput:!1,articleTypeLabel:"",additionalHelpTextLink:"",displayFooter:!1};const qa=Da;class Na{static get articleTypeInput(){return document.getElementById("yoast_wpseo_schema_article_type")}static get defaultArticleType(){return Na.articleTypeInput.getAttribute("data-default")}static get articleType(){return Na.articleTypeInput.value}static set articleType(e){Na.articleTypeInput.value=e}static get pageTypeInput(){return document.getElementById("yoast_wpseo_schema_page_type")}static get defaultPageType(){return Na.pageTypeInput.getAttribute("data-default")}static get pageType(){return Na.pageTypeInput.value}static set pageType(e){Na.pageTypeInput.value=e}}const Ba=e=>{const s=null!==Na.articleTypeInput;(0,i.useEffect)((()=>{e.loadSchemaPageData(),s&&e.loadSchemaArticleData()}),[]);const{pageTypeOptions:r,articleTypeOptions:a}=window.wpseoScriptData.metabox.schema,n={articleTypeLabel:(0,l.__)("Article type","wordpress-seo"),pageTypeLabel:(0,l.__)("Page type","wordpress-seo"),postTypeName:window.wpseoAdminL10n.postTypeNamePlural,helpTextTitle:(0,l.__)("Yoast SEO automatically describes your pages using schema.org","wordpress-seo"),helpTextDescription:(0,l.__)("This helps search engines understand your website and your content. You can change some of your settings for this page below.","wordpress-seo"),showArticleTypeInput:s,pageTypeOptions:r,articleTypeOptions:a},o={...e,...n,...(c=e.location,"metabox"===c?{helpTextLink:wpseoAdminL10n["shortlinks.metabox.schema.explanation"],additionalHelpTextLink:wpseoAdminL10n["shortlinks.metabox.schema.page_type"],isMetabox:!0}:{helpTextLink:wpseoAdminL10n["shortlinks.sidebar.schema.explanation"],additionalHelpTextLink:wpseoAdminL10n["shortlinks.sidebar.schema.page_type"],isMetabox:!1})};var c;return(0,t.createElement)(qa,{...o})};Ba.propTypes={displayFooter:y().bool.isRequired,schemaPageTypeSelected:y().string.isRequired,schemaArticleTypeSelected:y().string.isRequired,defaultArticleType:y().string.isRequired,defaultPageType:y().string.isRequired,loadSchemaPageData:y().func.isRequired,loadSchemaArticleData:y().func.isRequired,schemaPageTypeChange:y().func.isRequired,schemaArticleTypeChange:y().func.isRequired,location:y().string.isRequired};const $a=(0,B.compose)([(0,n.withSelect)((e=>{const{getPreferences:t,getPageType:s,getDefaultPageType:r,getArticleType:a,getDefaultArticleType:n}=e("yoast-seo/editor"),{displaySchemaSettingsFooter:o,isNewsEnabled:i}=t();return{displayFooter:o,isNewsEnabled:i,schemaPageTypeSelected:s(),schemaArticleTypeSelected:a(),defaultArticleType:n(),defaultPageType:r()}})),(0,n.withDispatch)((e=>{const{setPageType:t,setArticleType:s,getSchemaPageData:r,getSchemaArticleData:a}=e("yoast-seo/editor");return{loadSchemaPageData:r,loadSchemaArticleData:a,schemaPageTypeChange:t,schemaArticleTypeChange:s}})),jt()])(Ba),Ua=()=>(0,t.createElement)("p",{className:"yoast-related-keyphrases-modal__loading-message"},(0,l.sprintf)(/* translators: %1$s expands to "Yoast SEO", %2$s expands to "Semrush". */
(0,l.__)("Please wait while %1$s connects to %2$s to get related keyphrases...","wordpress-seo"),"Yoast SEO","Semrush")," ",(0,t.createElement)(R.SvgIcon,{icon:"loading-spinner"})),Wa=(0,Y.makeOutboundLink)(),Ka=()=>(0,t.createElement)(i.Fragment,null,(0,t.createElement)("p",null,(0,l.sprintf)(/* translators: %s : Expands to "Semrush". */
(0,l.__)("You've reached your request limit for today. Check back tomorrow or upgrade your plan over at %s.","wordpress-seo"),"Semrush")),(0,t.createElement)(Wa,{href:window.wpseoAdminL10n["shortlinks.semrush.prices"],className:"yoast-button-upsell"},(0,l.sprintf)(/* translators: %s : Expands to "Semrush". */
(0,l.__)("Upgrade your %s plan","wordpress-seo"),"Semrush"),(0,t.createElement)("span",{"aria-hidden":"true",className:"yoast-button-upsell__caret"}))),Ha="yoast-semrush-country-selector",Va=[{value:"us",name:"United States - US"},{value:"uk",name:"United Kingdom - UK"},{value:"ca",name:"Canada - CA"},{value:"ru",name:"Russia - RU"},{value:"de",name:"Germany - DE"},{value:"fr",name:"France - FR"},{value:"es",name:"Spain - ES"},{value:"it",name:"Italy - IT"},{value:"br",name:"Brazil - BR"},{value:"au",name:"Australia - AU"},{value:"ar",name:"Argentina - AR"},{value:"be",name:"Belgium - BE"},{value:"ch",name:"Switzerland - CH"},{value:"dk",name:"Denmark - DK"},{value:"fi",name:"Finland - FI"},{value:"hk",name:"Hong Kong - HK"},{value:"ie",name:"Ireland - IE"},{value:"il",name:"Israel - IL"},{value:"mx",name:"Mexico - MX"},{value:"nl",name:"Netherlands - NL"},{value:"no",name:"Norway - NO"},{value:"pl",name:"Poland - PL"},{value:"se",name:"Sweden - SE"},{value:"sg",name:"Singapore - SG"},{value:"tr",name:"Turkey - TR"},{value:"jp",name:"Japan - JP"},{value:"in",name:"India - IN"},{value:"hu",name:"Hungary - HU"},{value:"af",name:"Afghanistan - AF"},{value:"al",name:"Albania - AL"},{value:"dz",name:"Algeria - DZ"},{value:"ao",name:"Angola - AO"},{value:"am",name:"Armenia - AM"},{value:"at",name:"Austria - AT"},{value:"az",name:"Azerbaijan - AZ"},{value:"bh",name:"Bahrain - BH"},{value:"bd",name:"Bangladesh - BD"},{value:"by",name:"Belarus - BY"},{value:"bz",name:"Belize - BZ"},{value:"bo",name:"Bolivia - BO"},{value:"ba",name:"Bosnia and Herzegovina - BA"},{value:"bw",name:"Botswana - BW"},{value:"bn",name:"Brunei - BN"},{value:"bg",name:"Bulgaria - BG"},{value:"cv",name:"Cabo Verde - CV"},{value:"kh",name:"Cambodia - KH"},{value:"cm",name:"Cameroon - CM"},{value:"cl",name:"Chile - CL"},{value:"co",name:"Colombia - CO"},{value:"cr",name:"Costa Rica - CR"},{value:"hr",name:"Croatia - HR"},{value:"cy",name:"Cyprus - CY"},{value:"cz",name:"Czech Republic - CZ"},{value:"cd",name:"Congo - CD"},{value:"do",name:"Dominican Republic - DO"},{value:"ec",name:"Ecuador - EC"},{value:"eg",name:"Egypt - EG"},{value:"sv",name:"El Salvador - SV"},{value:"ee",name:"Estonia - EE"},{value:"et",name:"Ethiopia - ET"},{value:"ge",name:"Georgia - GE"},{value:"gh",name:"Ghana - GH"},{value:"gr",name:"Greece - GR"},{value:"gt",name:"Guatemala - GT"},{value:"gy",name:"Guyana - GY"},{value:"ht",name:"Haiti - HT"},{value:"hn",name:"Honduras - HN"},{value:"is",name:"Iceland - IS"},{value:"id",name:"Indonesia - ID"},{value:"jm",name:"Jamaica - JM"},{value:"jo",name:"Jordan - JO"},{value:"kz",name:"Kazakhstan - KZ"},{value:"kw",name:"Kuwait - KW"},{value:"lv",name:"Latvia - LV"},{value:"lb",name:"Lebanon - LB"},{value:"lt",name:"Lithuania - LT"},{value:"lu",name:"Luxembourg - LU"},{value:"mg",name:"Madagascar - MG"},{value:"my",name:"Malaysia - MY"},{value:"mt",name:"Malta - MT"},{value:"mu",name:"Mauritius - MU"},{value:"md",name:"Moldova - MD"},{value:"mn",name:"Mongolia - MN"},{value:"me",name:"Montenegro - ME"},{value:"ma",name:"Morocco - MA"},{value:"mz",name:"Mozambique - MZ"},{value:"na",name:"Namibia - NA"},{value:"np",name:"Nepal - NP"},{value:"nz",name:"New Zealand - NZ"},{value:"ni",name:"Nicaragua - NI"},{value:"ng",name:"Nigeria - NG"},{value:"om",name:"Oman - OM"},{value:"py",name:"Paraguay - PY"},{value:"pe",name:"Peru - PE"},{value:"ph",name:"Philippines - PH"},{value:"pt",name:"Portugal - PT"},{value:"ro",name:"Romania - RO"},{value:"sa",name:"Saudi Arabia - SA"},{value:"sn",name:"Senegal - SN"},{value:"rs",name:"Serbia - RS"},{value:"sk",name:"Slovakia - SK"},{value:"si",name:"Slovenia - SI"},{value:"za",name:"South Africa - ZA"},{value:"kr",name:"South Korea - KR"},{value:"lk",name:"Sri Lanka - LK"},{value:"th",name:"Thailand - TH"},{value:"bs",name:"Bahamas - BS"},{value:"tt",name:"Trinidad and Tobago - TT"},{value:"tn",name:"Tunisia - TN"},{value:"ua",name:"Ukraine - UA"},{value:"ae",name:"United Arab Emirates - AE"},{value:"uy",name:"Uruguay - UY"},{value:"ve",name:"Venezuela - VE"},{value:"vn",name:"Vietnam - VN"},{value:"zm",name:"Zambia - ZM"},{value:"zw",name:"Zimbabwe - ZW"},{value:"ly",name:"Libya - LY"}];class Ya extends i.Component{constructor(e){super(e),this.relatedKeyphrasesRequest=this.relatedKeyphrasesRequest.bind(this),this.onChangeHandler=this.onChangeHandler.bind(this)}componentDidMount(){this.props.response&&this.props.keyphrase===this.props.lastRequestKeyphrase||this.relatedKeyphrasesRequest()}storeCountryCode(e){ue()({path:"yoast/v1/semrush/country_code",method:"POST",data:{country_code:e}})}async relatedKeyphrasesRequest(){const{keyphrase:e,countryCode:t,newRequest:s}=this.props;s(t,e),this.storeCountryCode(t);const r=await this.doRequest(e,t);200!==r.status?this.handleFailedResponse(r):this.handleSuccessResponse(r)}handleSuccessResponse(e){const{setNoResultsFound:t,setRequestSucceeded:s}=this.props;0!==e.results.rows.length?s(e):t()}handleFailedResponse(e){const{setRequestLimitReached:t,setRequestFailed:s}=this.props;"error"in e&&(e.error.includes("TOTAL LIMIT EXCEEDED")?t():s(e))}async doRequest(e,t){return await ue()({path:(0,Fs.addQueryArgs)("/yoast/v1/semrush/related_keyphrases",{keyphrase:e,country_code:t})})}onChangeHandler(e){this.props.setCountry(e)}render(){return(0,t.createElement)("div",{id:Ha},(0,t.createElement)(R.SingleSelect,{id:Ha+"-select",label:(0,l.__)("Show results for:","wordpress-seo"),name:"semrush-country-code",options:Va,selected:this.props.countryCode,onChange:this.onChangeHandler,wrapperClassName:"yoast-field-group yoast-field-group--inline"}),(0,t.createElement)(R.NewButton,{id:Ha+"-button",variant:"secondary",onClick:this.relatedKeyphrasesRequest},(0,l.__)("Select country","wordpress-seo")))}}Ya.propTypes={keyphrase:y().string,countryCode:y().string,response:y().object,lastRequestKeyphrase:y().string,setCountry:y().func.isRequired,newRequest:y().func.isRequired,setNoResultsFound:y().func.isRequired,setRequestSucceeded:y().func.isRequired,setRequestLimitReached:y().func.isRequired,setRequestFailed:y().func.isRequired},Ya.defaultProps={keyphrase:"",countryCode:"us",response:{},lastRequestKeyphrase:""};const ja=Ya,za=(0,Y.makeOutboundLink)(w().a`
	display: inline-block;
	position: relative;
	outline: none;
	text-decoration: none;
	border-radius: 100%;
	width: 24px;
	height: 24px;
	margin: -4px 0;
	vertical-align: middle;

	color: ${C.colors.$color_help_text};
	
	&:hover,
	&:focus {
		color: ${C.colors.$color_snippet_focus};	
	}
	
	// Overwrite the default blue active color for links.
	&:active {
		color: ${C.colors.$color_help_text};	
	}

	&::before {
		position: absolute;
		top: 0;
		left: 0;
		padding: 2px;
		content: "\f223";
	}
`),Ga=(0,Y.makeOutboundLink)();class Za extends i.Component{constructor(e){super(e),this.transformTrendDataToChartPoints=this.transformTrendDataToChartPoints.bind(this),this.getAreaChartDataTableHeaderLabels=this.getAreaChartDataTableHeaderLabels.bind(this),this.mapAreaChartDataToTableData=this.mapAreaChartDataToTableData.bind(this)}transformTrendDataToChartPoints(e){return e.split(",").map(((e,t)=>({x:t,y:parseFloat(e)})))}getAreaChartDataTableHeaderLabels(){return[(0,l.__)("Twelve months ago","wordpress-seo"),(0,l.__)("Eleven months ago","wordpress-seo"),(0,l.__)("Ten months ago","wordpress-seo"),(0,l.__)("Nine months ago","wordpress-seo"),(0,l.__)("Eight months ago","wordpress-seo"),(0,l.__)("Seven months ago","wordpress-seo"),(0,l.__)("Six months ago","wordpress-seo"),(0,l.__)("Five months ago","wordpress-seo"),(0,l.__)("Four months ago","wordpress-seo"),(0,l.__)("Three months ago","wordpress-seo"),(0,l.__)("Two months ago","wordpress-seo"),(0,l.__)("Last month","wordpress-seo")]}mapAreaChartDataToTableData(e){return Math.round(100*e)}render(){const{keyphrase:e,relatedKeyphrases:s,countryCode:r,data:a,renderAction:n}=this.props,o="https://www.semrush.com/analytics/keywordoverview/?q="+encodeURIComponent(e)+"&db="+encodeURIComponent(r);return a&&!(0,m.isEmpty)(a.results)&&(0,t.createElement)(i.Fragment,null,(0,t.createElement)("table",{className:"yoast yoast-table"},(0,t.createElement)("thead",null,(0,t.createElement)("tr",null,(0,t.createElement)("th",{scope:"col",className:"yoast-table--primary"},(0,l.__)("Related keyphrase","wordpress-seo")),(0,t.createElement)("th",{scope:"col",abbr:(0,l.__)("Volume","wordpress-seo")},(0,l.__)("Volume","wordpress-seo"),(0,t.createElement)(za,{href:window.wpseoAdminL10n["shortlinks.semrush.volume_help"],className:"dashicons"},(0,t.createElement)("span",{className:"screen-reader-text"},/* translators: Hidden accessibility text. */
(0,l.__)("Learn more about the related keyphrases volume","wordpress-seo")))),(0,t.createElement)("th",{scope:"col",abbr:(0,l.__)("Trend","wordpress-seo")},(0,l.__)("Trend","wordpress-seo"),(0,t.createElement)(za,{href:window.wpseoAdminL10n["shortlinks.semrush.trend_help"],className:"dashicons"},(0,t.createElement)("span",{className:"screen-reader-text"},/* translators: Hidden accessibility text. */
(0,l.__)("Learn more about the related keyphrases trend","wordpress-seo")))),n&&(0,t.createElement)("td",{className:"yoast-table--nobreak"}))),(0,t.createElement)("tbody",null,a.results.rows.map(((e,r)=>{const a=e[0],o=this.transformTrendDataToChartPoints(e[2]),i=this.getAreaChartDataTableHeaderLabels();return(0,t.createElement)("tr",{key:r},(0,t.createElement)("td",null,a),(0,t.createElement)("td",null,e[1]),(0,t.createElement)("td",{className:"yoast-table--nopadding"},(0,t.createElement)(Pe,{width:66,height:24,data:o,strokeWidth:1.8,strokeColor:"#498afc",fillColor:"#ade3fc",className:"yoast-related-keyphrases-modal__chart",mapChartDataToTableData:this.mapAreaChartDataToTableData,dataTableCaption:(0,l.__)("Keyphrase volume in the last 12 months on a scale from 0 to 100.","wordpress-seo"),dataTableHeaderLabels:i})),n&&(0,t.createElement)("td",{className:"yoast-table--nobreak"},n(a,s)))})))),(0,t.createElement)("p",{style:{marginBottom:0}},(0,t.createElement)(Ga,{href:o},(0,l.sprintf)(/* translators: %s expands to Semrush */
(0,l.__)("Get more insights at %s","wordpress-seo"),"Semrush"))))}}Za.propTypes={data:y().object,keyphrase:y().string,relatedKeyphrases:y().array,countryCode:y().string,renderAction:y().func},Za.defaultProps={data:{},keyphrase:"",relatedKeyphrases:[],countryCode:"us",renderAction:null};const Xa=Za,Qa=(0,Y.makeOutboundLink)(),Ja=()=>(0,t.createElement)(R.Alert,{type:"info"},(0,l.sprintf)(/* translators: %s: Expands to "Yoast SEO". */
(0,l.__)("You’ll reach more people with multiple keyphrases! Want to quickly add these related keyphrases to the %s analyses for even better content optimization?","wordpress-seo"),"Yoast SEO")+" ",(0,t.createElement)(Qa,{href:window.wpseoAdminL10n["shortlinks.semrush.premium_landing_page"]},(0,l.sprintf)(/* translators: %s: Expands to "Yoast SEO Premium". */
(0,l.__)("Explore %s!","wordpress-seo"),"Yoast SEO Premium"))),en=()=>(0,t.createElement)(R.Alert,{type:"error"},(0,l.__)("We've encountered a problem trying to get related keyphrases. Please try again later.","wordpress-seo")),tn=()=>(0,t.createElement)(R.Alert,{type:"warning"},(0,l.sprintf)(/* translators: %s: Expands to "Yoast SEO". */
(0,l.__)("You've reached the maximum amount of 4 related keyphrases. You can change or remove related keyphrases in the %s metabox or sidebar.","wordpress-seo"),"Yoast SEO"));function sn(e){const{response:s,lastRequestKeyphrase:r,keyphrase:a,newRequest:n,setCountry:o,renderAction:c,countryCode:d,requestLimitReached:p,setRequestFailed:u,setNoResultsFound:g,relatedKeyphrases:y,setRequestSucceeded:f,setRequestLimitReached:w}=e,b=h().isPremium;return(0,t.createElement)(i.Fragment,null,!p&&(0,t.createElement)(i.Fragment,null,!b&&(0,t.createElement)(Ja,null),b&&function(e){return e&&e.length>=4}(y)&&(0,t.createElement)(tn,null),(0,t.createElement)(ja,{countryCode:d,setCountry:o,newRequest:n,keyphrase:a,setRequestFailed:u,setNoResultsFound:g,setRequestSucceeded:f,setRequestLimitReached:w,response:s,lastRequestKeyphrase:r})),function(e){const{isPending:s,requestLimitReached:r,isSuccess:a,response:n,requestHasData:o}=e;return s?(0,t.createElement)(Ua,null):r?(0,t.createElement)(Ka,null):!a&&function(e){return!(0,m.isEmpty)(e)&&"error"in e}(n)?(0,t.createElement)(en,null):o?void 0:(0,t.createElement)("p",null,(0,l.__)("Sorry, there's no data available for that keyphrase/country combination.","wordpress-seo"))}(e),(0,t.createElement)(Xa,{keyphrase:a,relatedKeyphrases:y,countryCode:d,renderAction:c,data:s}))}sn.propTypes={keyphrase:y().string,relatedKeyphrases:y().array,renderAction:y().func,requestLimitReached:y().bool,countryCode:y().string.isRequired,setCountry:y().func.isRequired,newRequest:y().func.isRequired,setRequestSucceeded:y().func.isRequired,setRequestLimitReached:y().func.isRequired,setRequestFailed:y().func.isRequired,setNoResultsFound:y().func.isRequired,response:y().object,lastRequestKeyphrase:y().string},sn.defaultProps={keyphrase:"",relatedKeyphrases:[],renderAction:null,requestLimitReached:!1,response:{},lastRequestKeyphrase:""};const rn=(0,B.compose)([(0,n.withSelect)((e=>{const{getFocusKeyphrase:t,getSEMrushSelectedCountry:s,getSEMrushRequestLimitReached:r,getSEMrushRequestResponse:a,getSEMrushRequestIsSuccess:n,getSEMrushIsRequestPending:o,getSEMrushRequestHasData:i,getSEMrushRequestKeyphrase:l}=e("yoast-seo/editor");return{keyphrase:t(),countryCode:s(),requestLimitReached:r(),response:a(),isSuccess:n(),isPending:o(),requestHasData:i(),lastRequestKeyphrase:l()}})),(0,n.withDispatch)((e=>{const{setSEMrushChangeCountry:t,setSEMrushNewRequest:s,setSEMrushRequestSucceeded:r,setSEMrushRequestFailed:a,setSEMrushSetRequestLimitReached:n,setSEMrushNoResultsFound:o}=e("yoast-seo/editor");return{setCountry:e=>{t(e)},newRequest:(e,t)=>{s(e,t)},setRequestSucceeded:e=>{r(e)},setRequestFailed:e=>{a(e)},setRequestLimitReached:()=>{n()},setNoResultsFound:()=>{o()}}}))])(sn),an=(0,l.__)("Check your text on even more SEO criteria and get an enhanced keyphrase analysis, making it easier to optimize your content.","wordpress-seo"),nn=e=>{const{locationContext:s}=(0,p.useRootContext)(),r=(0,Fs.addQueryArgs)(wpseoAdminL10n[e.buyLink],{context:s});return(0,t.createElement)(rr,{title:(0,l.__)("Get more help with writing content that ranks","wordpress-seo"),description:e.description,benefitsTitle:/* translators: %s expands to 'Yoast SEO Premium'. */
(0,l.sprintf)("%s also gives you:","Yoast SEO Premium"),benefits:Ys(),upsellButtonText:(0,l.sprintf)(/* translators: %s expands to 'Yoast SEO Premium'. */
(0,l.__)("Unlock with %s","wordpress-seo"),"Yoast SEO Premium"),upsellButton:{href:r,className:"yoast-button-upsell",rel:null,"data-ctb-id":"f6a84663-465f-4cb5-8ba5-f7a6d72224b2","data-action":"load-nfd-ctb"},upsellButtonLabel:(0,l.__)("1 year of premium support and updates included!","wordpress-seo")})};nn.propTypes={buyLink:y().string.isRequired,description:y().string},nn.defaultProps={description:an};const on=nn,ln=({location:e})=>{const[s,r]=(0,i.useState)(!1),a=(0,i.useCallback)((()=>r(!1)),[]),n=(0,i.useCallback)((()=>r(!0)),[]),o=(0,T.useSvgAria)();return(0,t.createElement)(i.Fragment,null,s&&(0,t.createElement)(X,{title:(0,l.__)("Unlock Premium SEO analysis","wordpress-seo"),onRequestClose:a,additionalClassName:"",className:`${G} yoast-gutenberg-modal__box yoast-gutenberg-modal__no-padding`,id:"yoast-premium-seo-analysis-modal",shouldCloseOnClickOutside:!0},(0,t.createElement)(z,null,(0,t.createElement)(on,{buyLink:`shortlinks.upsell.${e}.premium_seo_analysis_button`}))),"sidebar"===e&&(0,t.createElement)(re,{id:"yoast-premium-seo-analysis-modal-open-button",title:(0,l.__)("Premium SEO analysis","wordpress-seo"),prefixIcon:{icon:"seo-score-none",color:C.colors.$color_grey},onClick:n},(0,t.createElement)("div",{className:"yst-root"},(0,t.createElement)(T.Badge,{size:"small",variant:"upsell"},(0,t.createElement)(Vs,{className:"yst-w-2.5 yst-h-2.5 yst-shrink-0",...o})))),"metabox"===e&&(0,t.createElement)("div",{className:"yst-root"},(0,t.createElement)(V,{id:"yoast-premium-seo-analysis-metabox-modal-open-button",onClick:n},(0,t.createElement)(R.SvgIcon,{icon:"seo-score-none",color:C.colors.$color_grey}),(0,t.createElement)(V.Text,null,(0,l.__)("Premium SEO analysis","wordpress-seo")),(0,t.createElement)(T.Badge,{size:"small",variant:"upsell"},(0,t.createElement)(Vs,{className:"yst-w-2.5 yst-h-2.5 yst-mr-1 yst-shrink-0",...o}),(0,t.createElement)("span",null,"Premium")))))};ln.propTypes={location:y().string},ln.defaultProps={location:"sidebar"};const cn=ln,dn=e=>(0,t.createElement)(rr,{title:(0,l.__)("Reach a wider audience","wordpress-seo"),description:(0,l.__)("Get help optimizing for up to 5 related keyphrases. This helps you reach a wider audience and get more traffic.","wordpress-seo"),benefitsTitle:/* translators: %s expands to 'Yoast SEO Premium'. */
(0,l.sprintf)("%s also gives you:","Yoast SEO Premium"),benefits:Ys(),upsellButtonText:(0,l.sprintf)(/* translators: %s expands to 'Yoast SEO Premium'. */
(0,l.__)("Unlock with %s","wordpress-seo"),"Yoast SEO Premium"),upsellButton:{href:e.buyLink,className:"yoast-button-upsell",rel:null,"data-ctb-id":"f6a84663-465f-4cb5-8ba5-f7a6d72224b2","data-action":"load-nfd-ctb"},upsellButtonLabel:(0,l.__)("1 year free support and updates included!","wordpress-seo")});dn.propTypes={buyLink:y().string.isRequired};const pn=dn,un=()=>{const[e,,,s,r]=(0,T.useToggleState)(!1),a=(0,i.useContext)(p.LocationContext),{locationContext:n}=(0,p.useRootContext)(),o=(0,T.useSvgAria)(),c=wpseoAdminL10n["sidebar"===a.toLowerCase()?"shortlinks.upsell.sidebar.additional_button":"shortlinks.upsell.metabox.additional_button"];return(0,t.createElement)(t.Fragment,null,e&&(0,t.createElement)(X,{title:(0,l.__)("Add related keyphrases","wordpress-seo"),onRequestClose:r,additionalClassName:"",id:"yoast-additional-keyphrases-modal",className:`${G} yoast-gutenberg-modal__box yoast-gutenberg-modal__no-padding`,shouldCloseOnClickOutside:!0},(0,t.createElement)(z,null,(0,t.createElement)(pn,{buyLink:(0,Fs.addQueryArgs)(c,{context:n})}))),"sidebar"===a&&(0,t.createElement)(re,{id:"yoast-additional-keyphrase-modal-open-button",title:(0,l.__)("Add related keyphrase","wordpress-seo"),prefixIcon:{icon:"plus",color:C.colors.$color_grey_medium_dark},onClick:s},(0,t.createElement)("div",{className:"yst-root"},(0,t.createElement)(T.Badge,{size:"small",variant:"upsell"},(0,t.createElement)(Vs,{className:"yst-w-2.5 yst-h-2.5 yst-shrink-0",...o})))),"metabox"===a&&(0,t.createElement)("div",{className:"yst-root"},(0,t.createElement)(V,{id:"yoast-additional-keyphrase-metabox-modal-open-button",onClick:s},(0,t.createElement)(R.SvgIcon,{icon:"plus",color:C.colors.$color_grey_medium_dark}),(0,t.createElement)(V.Text,null,(0,l.__)("Add related keyphrase","wordpress-seo")),(0,t.createElement)(T.Badge,{size:"small",variant:"upsell"},(0,t.createElement)(Vs,{className:"yst-w-2.5 yst-h-2.5 yst-mr-1 yst-shrink-0",...o}),(0,t.createElement)("span",null,"Premium")))))};var mn,hn,gn,yn,fn,wn,bn,En,vn,kn,xn,Sn,Tn,Rn,Cn,In,An,Ln,Pn,Fn,On,Mn,Dn,qn,Nn,Bn,$n,Un,Wn,Kn,Hn,Vn,Yn,jn,zn,Gn,Zn,Xn,Qn,Jn,eo,to,so,ro,ao,no,oo;function io(){return io=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var s=arguments[t];for(var r in s)Object.prototype.hasOwnProperty.call(s,r)&&(e[r]=s[r])}return e},io.apply(this,arguments)}const lo=e=>t.createElement("svg",io({xmlns:"http://www.w3.org/2000/svg","aria-hidden":"true",viewBox:"0 0 448 360"},e),mn||(mn=t.createElement("circle",{cx:226,cy:211,r:149,fill:"#f0ecf0"})),hn||(hn=t.createElement("path",{fill:"#fbd2a6",d:"M173.53 189.38s-35.47-5.3-41.78-11c-9.39-24.93-29.61-48-35.47-66.21-.71-2.24 3.72-11.39 3.53-15.41s-5.34-11.64-5.23-14-.09-15.27-.09-15.27l-4.75-.72s-5.13 6.07-3.56 9.87c-1.73-4.19 4.3 7.93.5 9.35 0 0-6-5.94-11.76-8.27s-19.57-3.65-19.57-3.65L43.19 73l-4.42.6L31 69.7l-2.85 5.12 7.53 5.29L40.86 92l17.19 10.2 10.2 10.56 9.86 3.56s26.49 79.67 45 92c17 11.33 37.23 15.92 37.23 15.92z"})),gn||(gn=t.createElement("path",{fill:"#a4286a",d:"M270.52 345.13c2.76-14.59 15.94-35.73 30.24-54.58 16.22-21.39 14-79.66-33.19-91.46-17.3-4.32-52.25-1-59.85-3.41C186.54 189 170 187 168 190.17c-5 10.51-7.73 27.81-5.51 36.26 1.18 4.73 3.54 5.91 20.49 13.4-5.12 15-16.35 26.3-22.86 37s7.88 27.2 7.1 33.51c-.48 3.8-4.26 21.13-7.18 34.25a149.47 149.47 0 0 0 110.3 8.66 25.66 25.66 0 0 1 .18-8.12z"})),yn||(yn=t.createElement("path",{fill:"#9a5815",d:"M206.76 66.43c-5 14.4-1.42 25.67-3.93 40.74-10 60.34-24.08 43.92-31.44 93.6 7.24-14.19 14.32-15.82 20.63-23.11-.83 3.09-10.25 13.75-8.05 34.81 9.85-8.51 6.35-8.75 11.86-8.54.36 3.25 3.53 3.22-3.59 10.53 2.52.69 17.42-14.32 20.16-12.66s0 5.72-6 7.76c2.15 2.2 30.47-3.87 43.81-14.71 4.93-4 10-13.16 13.38-18.2 7.17-10.62 12.38-24.77 17.71-36.6 8.94-19.87 15.09-39.34 16.11-61.31.53-10.44-3.41-18.44-4.41-28.86-2.57-27.8-67.63-37.26-86.24 16.55z"})),fn||(fn=t.createElement("path",{fill:"#efb17c",d:"M277.74 179.06c.62-.79 1.24-1.59 1.84-2.39-.85 2.59-1.52 3.73-1.84 2.39z"})),wn||(wn=t.createElement("path",{fill:"#fbd2a6",d:"M216.1 206.72c3.69-5.42 8.28-3.35 15.57-8.28 3.76-3.06 1.57-9.46 1.77-11.82 18.25 4.56 37.38-1.18 49.07-16 .62 5.16-2.77 22.27-.2 27 4.73 8.67 13.4 18.92 13.4 18.92-35.47-2.76-63.45 39-89.86 44.54 5.52-28.74-2.36-35.84 10.25-54.36z"})),bn||(bn=t.createElement("path",{fill:"#f6b488",d:"m235.21 167.9 53.21-25.23s-3.65 24-6.5 32.72c-64.05 62.66-46.47-7.33-46.71-7.49z"})),En||(En=t.createElement("path",{fill:"#fbd2a6",d:"M226.86 50.64C215 59.31 206.37 93.21 204 95.57c-19.46 19.47-3.59 41.39-3.94 51.24-.2 5.52-4.14 25.42 5.72 29.36 22.22 8.89 60-3.48 67.19-12.61 13.28-16.75 40.89-94.78 17.74-108.19-7.92-4.58-42.78-20.18-63.85-4.73z"})),vn||(vn=t.createElement("path",{fill:"#e5766c",d:"M243.69 143.66c-10.7-6.16-8.56-6.73-19.76-12.71-3.86-2.07-3.94.64-6.32 0-2.91-.79-1.39-2.74-5.37-3.48-6.52-1.21-3.67 3.63-3.15 6 1.32 6.15-8.17 17.3 3.26 21.42 12.65 4.55 21.38-9.41 31.34-11.23z"})),kn||(kn=t.createElement("path",{fill:"#fff",d:"M240.68 143.9c-11.49-5.53-11.65-8.17-24.64-11.69-8.6-2.32-5.53 1-5.69 4.42-.2 4.16-1.26 9.87 4.9 12.66 9 4.09 18.16-6.02 25.43-5.39zm.7-40.9c-.16 1.26-.06 4.9 5.46 8.25 11.43-4.73 16.36-2.56 17-3.33 1.48-1.76-2-8.87-7.88-9.85-5.58-.94-14.14 1.24-14.58 4.93z"})),xn||(xn=t.createElement("path",{fill:"#000001",d:"M263.53 108.19c-4.32-4.33-6.85-6.24-12.26-8.21-2.77-1-6.18.18-8.65 1.67a3.65 3.65 0 0 0-1.24 1.23h-.12a3.73 3.73 0 0 1 1-1.52 12.53 12.53 0 0 1 11.93-3c4.73 1 9.43 4.63 9.42 9.82z"})),Sn||(Sn=t.createElement("circle",{cx:254.13,cy:104.05,r:4.19,fill:"#000001"})),Tn||(Tn=t.createElement("path",{fill:"#fff",d:"M225.26 99.22c-.29 1-6.6 3.45-10.92 1.48-1.15-3.24-5-6.43-5.25-6.71-.5-2.86 5.55-8 10.06-6.3a10.21 10.21 0 0 1 6.11 11.53z"})),Rn||(Rn=t.createElement("path",{fill:"#000001",d:"M209.29 94.21c-.19-2.34 1.84-4.1 3.65-5.2 7-3.87 13.18 3 12.43 10h-.12c-.14-4-2.38-8.44-6.47-9.11a3.19 3.19 0 0 0-2.42.31c-1.37.85-2.38 2-3.89 2.56-1 .45-1.92.42-3 1.4h-.22z"})),Cn||(Cn=t.createElement("circle",{cx:219.55,cy:95.28,r:4,fill:"#000001"})),In||(In=t.createElement("path",{fill:"#efb17c",d:"M218.66 120.27a27.32 27.32 0 0 0 4.54 3.45c-2.29-.72-4.28-.69-6.32-2.27-2.53-2-3.39-5.16-.73-7.72 10.24-9.82 12.56-13.82 14.77-24.42-1 12.37-6 17.77-10.63 23.18-2.53 2.97-4.68 5.06-1.63 7.78z"})),An||(An=t.createElement("path",{fill:"#a57c52",d:"M231.22 69.91c-.67-3.41-8.78-2.83-11.06-1.93-3.48 1.39-6.08 5.22-7.13 8.53 2.9-4.3 6.74-8.12 12.46-6 1.16.42 3.18 2.35 4.48 1.85s1.03-2.2 1.25-2.45zm32.16 8.56c-2.75-1.66-12.24-5.08-12.18.82 2.56.24 5-.19 7.64.95 11.22 4.76 12.77 17.61 12.85 17.86.2-.53.1 1.26.23.7-.02.2.95-12.12-8.54-20.33z"})),Ln||(Ln=t.createElement("path",{fill:"#fbd2a6",d:"M53.43 250.73c6.29 0-.6-.17 7.34 0 1.89.05-2.38-.7 0-.69 4.54-4.2 12.48-.74 20.6-2.45 4.55.35 3.93 1.35 5.59 4.19 4.89 8.38 4.78 14.21 14 19.56 16.42 8.38 66 12.92 88.49 18.86 5.52.83 42.64-20.15 61-23.75 6.51 10.74 11.46 28.68 8.39 34.93-6.54 13.3-57.07 25.4-75.91 25.15C156.47 326.18 94 294 92.2 293c-.94-.57.7-.7-7.68 0s-10.15.72-17.47-1.4c-3-.87-4.61-1.33-6.33-3.54-2 .22-3.39.2-4.78-1-3.15-2.74-4.84-6.61-2.73-10.06h-.12c-3.35-2.48-6.54-7.69-3.08-11.72 1-1.18 6.06-1.94 7.77-2.28-1.58-.29-6.37.19-7.49-.72-3.06-2.5-4.96-11.55 3.14-11.55z"})),Pn||(Pn=t.createElement("path",{fill:"#a4286a",d:"M303.22 237.52c-9.87-11.88-41.59 8.19-47.8 12.34s-14.89 17.95-14.89 17.95c6 9.43 8.36 31 5.65 46.34l30.51-3s18-15.62 22.59-28.7 6.3-42.54 6.3-42.54"})),Fn||(Fn=t.createElement("path",{fill:"#cb9833",d:"M278.63 31.67c-6.08 0-22.91 4.07-22.93 12.91 0 11 47.9 38.38 16.14 85.85 10.21-.79 10.79-8.12 14.92-14.93-3.66 77-49.38 93.58-40.51 142.25 7.68-25.81 20.3-11.62 38.13-33.84 3.45 4.88 9 18.28-9.46 33.78 50-31.26 57.31-56.6 51.92-95C319.93 113.53 348.7 42 278.63 31.67z"})),On||(On=t.createElement("path",{fill:"#fbd2a6",d:"M283.64 126.83c-2.42 9.67-8 15.76-1.48 16.46A21.26 21.26 0 0 0 302 132.6c5.17-8.52 3.93-16.44-2.46-18s-13.48 2.56-15.9 12.23z"})),Mn||(Mn=t.createElement("path",{fill:"#efb17c",d:"M38 73.45c1.92 2 4.25 9.21 6.32 10.91 2.25 1.85 5.71 2.12 8.1 4.45 3.66-2 6-8.72 10-9.31-2.59 1.31-4.42 3.5-6.93 4.88-1.42.8-3 1.31-4.38 2.25-2.16-1.46-4.27-1.77-6.26-3.38-2.52-2.02-5.31-8-6.85-9.8z"})),Dn||(Dn=t.createElement("path",{fill:"#efb17c",d:"M39 74.4c4.83 1.1 12.52 6.44 15.89 10-3.22-1.34-14.73-6.15-15.89-10zm.62-1.5c6.71-.79 18 1.54 23.29 5.9-3.85-.2-5.42-1.48-9-2.94-4.08-1.69-8.83-2.03-14.29-2.96zm46.43 14.58c-3.72-1.32-10.52-1.13-13.22 3.52 2-1.16 1.84-2.11 4.18-1.72-3.81-4.15 8.16-.74 11.6-.24m-2.78 13.15c.56-3.29-8-7.81-10.58-9.17-6.25-3.29-12.16 1.36-19.33-4.53 5.94 6.1 14.23 2.5 19.55 5.76 3.06 1.88 8.65 6.09 9.35 9.38-.23-.4 1.29-1.44 1.01-1.44z"})),qn||(qn=t.createElement("circle",{cx:38.13,cy:30.03,r:3.14,fill:"#b89ac8"})),Nn||(Nn=t.createElement("circle",{cx:60.26,cy:39.96,r:3.14,fill:"#e31e0c"})),Bn||(Bn=t.createElement("circle",{cx:50.29,cy:25.63,r:3.14,fill:"#3baa45"})),$n||($n=t.createElement("circle",{cx:22.19,cy:19.21,r:3.14,fill:"#2ca9e1"})),Un||(Un=t.createElement("circle",{cx:22.19,cy:30.03,r:3.14,fill:"#e31e0c"})),Wn||(Wn=t.createElement("circle",{cx:26.86,cy:8.28,r:3.14,fill:"#3baa45"})),Kn||(Kn=t.createElement("circle",{cx:49.32,cy:39.99,r:3.14,fill:"#e31e0c"})),Hn||(Hn=t.createElement("circle",{cx:63.86,cy:59.52,r:3.14,fill:"#f8ad39"})),Vn||(Vn=t.createElement("circle",{cx:50.88,cy:50.72,r:3.14,fill:"#3baa45"})),Yn||(Yn=t.createElement("circle",{cx:63.47,cy:76.17,r:3.14,fill:"#e31e0c"})),jn||(jn=t.createElement("circle",{cx:38.34,cy:14.83,r:3.14,fill:"#2ca9e1"})),zn||(zn=t.createElement("circle",{cx:44.44,cy:5.92,r:3.14,fill:"#f8ad39"})),Gn||(Gn=t.createElement("circle",{cx:57.42,cy:10.24,r:3.14,fill:"#e31e0c"})),Zn||(Zn=t.createElement("circle",{cx:66.81,cy:12.4,r:3.14,fill:"#2ca9e1"})),Xn||(Xn=t.createElement("circle",{cx:77.95,cy:5.14,r:3.14,fill:"#b89ac8"})),Qn||(Qn=t.createElement("circle",{cx:77.95,cy:30.34,r:3.14,fill:"#e31e0c"})),Jn||(Jn=t.createElement("circle",{cx:80.97,cy:16.55,r:3.14,fill:"#f8ad39"})),eo||(eo=t.createElement("circle",{cx:62.96,cy:27.27,r:3.14,fill:"#3baa45"})),to||(to=t.createElement("circle",{cx:75.36,cy:48.67,r:3.14,fill:"#2ca9e1"})),so||(so=t.createElement("circle",{cx:76.11,cy:65.31,r:3.14,fill:"#3baa45"})),ro||(ro=t.createElement("path",{fill:"#71b026",d:"M78.58 178.43C54.36 167.26 32 198.93 5 198.93c19.56 20.49 63.53 1.52 69 15.5 1.48-14.01 4.11-30.9 4.58-36z"})),ao||(ao=t.createElement("path",{fill:"#074a67",d:"M67.75 251.08c0-4.65 10.13-72.65 10.13-72.65h2.8l-9.09 72.3z"})),no||(no=t.createElement("ellipse",{cx:255.38,cy:103.18,fill:"#fff",rx:1.84,ry:1.77})),oo||(oo=t.createElement("ellipse",{cx:221.24,cy:94.75,fill:"#fff",rx:1.84,ry:1.77}))),co=(0,B.compose)([(0,n.withSelect)(((e,t)=>{const{isAlertDismissed:s}=e(t.store||"yoast-seo/editor");return{isAlertDismissed:s(t.alertKey)}})),(0,n.withDispatch)(((e,t)=>{const{dismissAlert:s}=e(t.store||"yoast-seo/editor");return{onDismissed:()=>s(t.alertKey)}}))]),po=({children:e,id:s,hasIcon:r=!0,title:a,image:n=null,isAlertDismissed:o,onDismissed:i})=>o?null:(0,t.createElement)("div",{id:s,className:"notice-yoast yoast is-dismissible"},(0,t.createElement)("div",{className:"notice-yoast__container"},(0,t.createElement)("div",null,(0,t.createElement)("div",{className:"notice-yoast__header"},r&&(0,t.createElement)("span",{className:"yoast-icon"}),(0,t.createElement)("h2",{className:"notice-yoast__header-heading"},a)),(0,t.createElement)("p",null,e)),n&&(0,t.createElement)(n,{height:"60"})),(0,t.createElement)("button",{type:"button",className:"notice-dismiss",onClick:i},(0,t.createElement)("span",{className:"screen-reader-text"},/* translators: Hidden accessibility text. */
(0,l.__)("Dismiss this notice.","wordpress-seo"))));po.propTypes={children:y().node.isRequired,id:y().string.isRequired,hasIcon:y().bool,title:y().any.isRequired,image:y().elementType,isAlertDismissed:y().bool.isRequired,onDismissed:y().func.isRequired};const uo=co(po),mo=({store:e="yoast-seo/editor",image:s=null,title:r,promoId:a,alertKey:o,children:i,...l})=>(0,n.select)(e).isPromotionActive(a)&&(0,t.createElement)(uo,{alertKey:o,store:e,id:o,title:r,image:s,...l},i);mo.propTypes={store:y().string,image:y().elementType,title:y().any.isRequired,promoId:y().string.isRequired,alertKey:y().string.isRequired,children:y().node};const ho=({store:e="yoast-seo/editor",location:s="sidebar",...r})=>{const a=(0,n.useSelect)((t=>t(e).getIsPremium()),[e]),o=(0,n.useSelect)((t=>t(e).selectLinkParams()),[e]),c="sidebar"===s?(0,l.sprintf)(/* translators: %1$s expands to YOAST SEO PREMIUM */
(0,l.__)("BLACK FRIDAY SALE: %1$s","wordpress-seo"),"YOAST SEO PREMIUM"):(0,i.createInterpolateElement)((0,l.sprintf)(/* translators: %1$s expands to YOAST SEO PREMIUM, %2$s expands to a link on yoast.com, %3$s expands to the anchor end tag. */
(0,l.__)("BLACK FRIDAY SALE: %1$s %2$sBuy now!%3$s","wordpress-seo"),"YOAST SEO PREMIUM","<a>","</a>"),{a:(0,t.createElement)("a",{href:(0,Fs.addQueryArgs)("https://yoa.st/black-friday-sale",o),target:"_blank",rel:"noreferrer"})});return a?null:(0,t.createElement)(mo,{id:`black-friday-2023-promotion-${s}`,promoId:"black-friday-2023-promotion",alertKey:"black-friday-2023-promotion",store:e,title:c,image:Image,...r},(0,t.createElement)("span",{className:"yoast-bf-sale-badge"},(0,l.__)("30% OFF!","wordpress-seo")," "),"sidebar"===s&&(0,t.createElement)("a",{className:"yst-block yst--mb-[1em]",href:(0,Fs.addQueryArgs)("https://yoa.st/black-friday-sale",o),target:"_blank",rel:"noreferrer"},(0,l.__)("Buy now!","wordpress-seo")))};ho.propTypes={store:y().string,location:y().oneOf(["sidebar","metabox"])};const go=()=>window.wpseoScriptData&&"1"===window.wpseoScriptData.isWooCommerceActive,yo=e=>s=>!(()=>{var e,t;const s=(0,n.select)("yoast-seo/editor").getIsPremium(),r=(0,n.select)("yoast-seo/editor").getWarningMessage();return(s&&null!==(e=null===(t=(0,n.select)("yoast-seo-premium/editor"))||void 0===t?void 0:t.getMetaboxWarning())&&void 0!==e?e:[]).length>0||r.length>0})()&&(0,t.createElement)(e,{...s}),fo=yo((()=>{const e=(0,n.useSelect)((e=>e("yoast-seo/editor").selectLinkParams()),[]),s=(0,l.sprintf)(/* translators: %1$s expands to 'WooCommerce'. */
(0,l.__)("Is your %1$s store ready for Black Friday?","wordpress-seo"),"WooCommerce");return(0,t.createElement)(mo,{id:"black-friday-2023-product-editor-checklist",alertKey:"black-friday-2023-product-editor-checklist",promoId:"black-friday-2023-checklist",store:"yoast-seo/editor",title:s,image:lo},(0,i.createInterpolateElement)((0,l.sprintf)(/* translators: %1$s expands to a 'strong' start tag, %2$s to a 'strong' end tag. */
(0,l.__)("The Yoast %1$sultimate Black Friday checklist%2$s helps you prepare in time, so you can boost your results during this sale.","wordpress-seo"),"<strong>","</strong>"),{strong:(0,t.createElement)("strong",null)})," ",(0,t.createElement)("a",{href:(0,Fs.addQueryArgs)("https://yoa.st/black-friday-checklist",e),target:"_blank",rel:"noreferrer"},(0,l.__)("Get the checklist and start optimizing now!","wordpress-seo")))})),wo=yo(ho);function bo({settings:e}){const s=(0,n.useSelect)((e=>e("yoast-seo/editor").getIsTerm()),[]),r=(0,n.useSelect)((e=>e("yoast-seo/editor").getIsProduct()),[])&&go();return(0,t.createElement)(i.Fragment,null,(0,t.createElement)(k.Fill,{name:"YoastMetabox"},(0,t.createElement)(or,{key:"warning",renderPriority:1},(0,t.createElement)(ms,null)),(0,t.createElement)(or,{key:"time-constrained-notification",renderPriority:2},r&&(0,t.createElement)(fo,null),(0,t.createElement)(wo,{image:null,hasIcon:!1,location:"metabox"})),e.isKeywordAnalysisActive&&(0,t.createElement)(or,{key:"keyword-input",renderPriority:8},(0,t.createElement)(Yt.KeywordInput,{isSEMrushIntegrationActive:e.isSEMrushIntegrationActive}),!window.wpseoScriptData.metabox.isPremium&&(0,t.createElement)(k.Fill,{name:"YoastRelatedKeyphrases"},(0,t.createElement)(rn,null))),(0,t.createElement)(or,{key:"search-appearance",renderPriority:9},(0,t.createElement)(ys,{id:"yoast-snippet-editor-metabox",title:(0,l.__)("Search appearance","wordpress-seo"),initialIsOpen:!0},(0,t.createElement)(us,{hasPaperStyle:!1}))),e.isContentAnalysisActive&&(0,t.createElement)(or,{key:"readability-analysis",renderPriority:10},(0,t.createElement)(Yt.ReadabilityAnalysis,{shouldUpsell:e.shouldUpsell})),e.isKeywordAnalysisActive&&(0,t.createElement)(or,{key:"seo-analysis",renderPriority:20},(0,t.createElement)(i.Fragment,null,(0,t.createElement)(Yt.SeoAnalysis,{shouldUpsell:e.shouldUpsell,shouldUpsellWordFormRecognition:e.isWordFormRecognitionActive}),e.shouldUpsell&&(0,t.createElement)(cn,{location:"metabox"}))),e.isInclusiveLanguageAnalysisActive&&(0,t.createElement)(or,{key:"inclusive-language-analysis",renderPriority:21},(0,t.createElement)(Yt.InclusiveLanguageAnalysis,null)),e.isKeywordAnalysisActive&&(0,t.createElement)(or,{key:"additional-keywords-upsell",renderPriority:22},e.shouldUpsell&&(0,t.createElement)(un,null)),e.isKeywordAnalysisActive&&e.isWincherIntegrationActive&&(0,t.createElement)(or,{key:"wincher-seo-performance",renderPriority:23},(0,t.createElement)(Vt,{location:"metabox"})),e.shouldUpsell&&!s&&(0,t.createElement)(or,{key:"internal-linking-suggestions-upsell",renderPriority:25},(0,t.createElement)(ar,null)),e.isCornerstoneActive&&(0,t.createElement)(or,{key:"cornerstone",renderPriority:30},(0,t.createElement)(zt,null)),e.displayAdvancedTab&&(0,t.createElement)(or,{key:"advanced",renderPriority:40},(0,t.createElement)(ys,{id:"collapsible-advanced-settings",title:(0,l.__)("Advanced","wordpress-seo")},(0,t.createElement)(hr,null))),e.displaySchemaSettings&&(0,t.createElement)(or,{key:"schema",renderPriority:50},(0,t.createElement)($a,null)),(0,t.createElement)(or,{key:"social",renderPriority:-1},(0,t.createElement)(Ra,{target:"wpseo-section-social"})),e.isInsightsEnabled&&(0,t.createElement)(or,{key:"insights",renderPriority:52},(0,t.createElement)(Hs,{location:"metabox"}))))}bo.propTypes={settings:y().object.isRequired};const Eo=(0,B.compose)([(0,n.withSelect)(((e,t)=>{const{getPreferences:s}=e("yoast-seo/editor");return{settings:s(),store:t.store}}))])(bo);function vo({target:e,store:s,theme:r}){return(0,t.createElement)(L,{target:e},(0,t.createElement)(N,{store:s,theme:r}),(0,t.createElement)(Eo,{store:s,theme:r}))}vo.propTypes={target:y().string.isRequired,store:y().object,theme:y().object};const ko=({error:e})=>{const s=(0,i.useCallback)((()=>{var e,t;return null===(e=window)||void 0===e||null===(t=e.location)||void 0===t?void 0:t.reload()}),[]),r=(0,n.useSelect)((e=>e("yoast-seo/editor").selectLink("https://yoa.st/sidebar-error-support")),[]),a=(0,n.useSelect)((e=>e("yoast-seo/editor").getPreference("isRtl",!1)),[]);return(0,t.createElement)(T.Root,{context:{isRtl:a}},(0,t.createElement)(D,{error:e},(0,t.createElement)(D.VerticalButtons,{supportLink:r,handleRefreshClick:s})))};function _o({theme:e}){return(0,t.createElement)(S,{theme:e,location:"sidebar"},(0,t.createElement)(T.ErrorBoundary,{FallbackComponent:ko},(0,t.createElement)(k.Slot,{name:"YoastSidebar"},(e=>_(e)))))}function xo({score:e,label:s,scoreValue:r}){return(0,t.createElement)("div",{className:"yoast-analysis-check"},(0,t.createElement)(R.SvgIcon,{...A(e)}),(0,t.createElement)("span",null," ",s," ",r&&(0,t.createElement)("strong",null,r)))}function So({checklist:e,onClick:s}){const r=e.every((e=>"good"===e.score));return(0,t.createElement)(i.Fragment,null,e.map((e=>(0,t.createElement)(xo,{key:e.label,...e}))),(0,t.createElement)("br",null),!r&&(0,t.createElement)(R.Button,{onClick:s},(0,l.__)("Improve your post with Yoast SEO","wordpress-seo")))}function To(e){return(0,m.isNil)(e)||(e/=10),function(e){switch(e){case"feedback":return{className:"na",screenReaderText:(0,l.__)("Feedback","wordpress-seo"),screenReaderReadabilityText:"",screenReaderInclusiveLanguageText:""};case"bad":return{className:"bad",screenReaderText:(0,l.__)("Needs improvement","wordpress-seo"),screenReaderReadabilityText:(0,l.__)("Needs improvement","wordpress-seo"),screenReaderInclusiveLanguageText:(0,l.__)("Needs improvement","wordpress-seo")};case"ok":return{className:"ok",screenReaderText:(0,l.__)("OK SEO score","wordpress-seo"),screenReaderReadabilityText:(0,l.__)("OK","wordpress-seo"),screenReaderInclusiveLanguageText:(0,l.__)("Potentially non-inclusive","wordpress-seo")};case"good":return{className:"good",screenReaderText:(0,l.__)("Good SEO score","wordpress-seo"),screenReaderReadabilityText:(0,l.__)("Good","wordpress-seo"),screenReaderInclusiveLanguageText:(0,l.__)("Good","wordpress-seo")};default:return{className:"loading",screenReaderText:"",screenReaderReadabilityText:"",screenReaderInclusiveLanguageText:""}}}(I.interpreters.scoreToRating(e))}function Ro(e,t){const{isKeywordAnalysisActive:s}=t.getPreferences();if(s){const s=To(t.getReadabilityResults().overallScore);e.push({label:(0,l.__)("Readability analysis:","wordpress-seo"),score:s.className,scoreValue:s.screenReaderReadabilityText})}}function Co(e,t){const{isContentAnalysisActive:s}=t.getPreferences();if(s){const s=To(t.getResultsForFocusKeyword().overallScore),r=h().isPremium;e.push({label:r?(0,l.__)("Premium SEO analysis:","wordpress-seo"):(0,l.__)("SEO analysis:","wordpress-seo"),score:s.className,scoreValue:s.screenReaderReadabilityText})}}function Io(e,t){const{isInclusiveLanguageAnalysisActive:s}=t.getPreferences();if(s){const s=To(t.getInclusiveLanguageResults().overallScore);e.push({label:(0,l.__)("Inclusive language:","wordpress-seo"),score:s.className,scoreValue:s.screenReaderInclusiveLanguageText})}}ko.propTypes={error:y().object.isRequired},xo.propTypes={score:g.string.isRequired,label:g.string.isRequired,scoreValue:g.string},xo.defaultProps={scoreValue:""},So.propTypes={checklist:y().array.isRequired,onClick:y().func.isRequired};const Ao=(0,B.compose)([(0,n.withSelect)((function(e){const t=e("yoast-seo/editor"),s=[];return Ro(s,t),Co(s,t),Io(s,t),s.push(...Object.values(t.getChecklistItems())),{checklist:s}})),(0,n.withDispatch)((function(e){const{openGeneralSidebar:t}=e("core/edit-post");return{onClick:()=>{t("yoast-seo/seo-sidebar")}}}))])(So),Lo=(0,B.compose)([(0,n.withSelect)((e=>{const t=e("yoast-seo/editor"),s=To(t.getResultsForFocusKeyword().overallScore),r=To(t.getReadabilityResults().overallScore),{isKeywordAnalysisActive:a,isContentAnalysisActive:n}=t.getPreferences();let o,i;switch(r.className){case"good":o=C.colors.$color_good;break;case"ok":o=C.colors.$color_ok;break;default:o=C.colors.$color_bad}switch(s.className){case"good":i=C.colors.$color_good;break;case"ok":i=C.colors.$color_ok;break;default:i=C.colors.$color_bad}return{readabilityScoreColor:o,seoScoreColor:i,isKeywordAnalysisActive:a,isContentAnalysisActive:n}}))])(v);var Po;function Fo(){return Fo=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var s=arguments[t];for(var r in s)Object.prototype.hasOwnProperty.call(s,r)&&(e[r]=s[r])}return e},Fo.apply(this,arguments)}const Oo=e=>t.createElement("svg",Fo({xmlns:"http://www.w3.org/2000/svg","aria-hidden":"true",viewBox:"0 0 1600 1600"},e),Po||(Po=t.createElement("g",{fill:"none",fillRule:"evenodd"},t.createElement("path",{fill:"#1877f2",d:"M1600 800a800 800 0 1 0-925 790v-559H472V800h203V624c0-201 119-311 302-311 88 0 179 15 179 15v197h-101c-99 0-130 62-130 125v150h222l-36 231H925v559a800 800 0 0 0 675-790"}),t.createElement("path",{fill:"#fff",d:"M1147 800H925V650c0-63 31-125 130-125h101V328s-91-15-179-15c-183 0-302 110-302 311v176H472v231h203v559a806 806 0 0 0 250 0v-559h186z"}))));var Mo;function Do(){return Do=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var s=arguments[t];for(var r in s)Object.prototype.hasOwnProperty.call(s,r)&&(e[r]=s[r])}return e},Do.apply(this,arguments)}const qo=e=>t.createElement("svg",Do({xmlns:"http://www.w3.org/2000/svg",fill:"current",viewBox:"0 0 1200 1227"},e),Mo||(Mo=t.createElement("path",{d:"M714.163 519.284 1160.89 0h-105.86L667.137 450.887 357.328 0H0l468.492 681.821L0 1226.37h105.866l409.625-476.152 327.181 476.152H1200L714.137 519.284h.026ZM569.165 687.828l-47.468-67.894-377.686-540.24h162.604l304.797 435.991 47.468 67.894 396.2 566.721H892.476L569.165 687.854v-.026Z"})));function No({permalink:e}){const s=encodeURI(e);return(0,t.createElement)(i.Fragment,null,(0,t.createElement)("div",null,(0,l.__)("Share your post!","wordpress-seo")),(0,t.createElement)("ul",{className:"yoast-seo-social-share-buttons"},(0,t.createElement)("li",null,(0,t.createElement)("a",{href:"https://www.facebook.com/sharer/sharer.php?u="+s,target:"_blank",rel:"noopener noreferrer"},(0,t.createElement)(Oo,null),(0,l.__)("Facebook","wordpress-seo"),(0,t.createElement)("span",{className:"screen-reader-text"},/* translators: Hidden accessibility text. */
(0,l.__)("(Opens in a new browser tab)","wordpress-seo")))),(0,t.createElement)("li",null,(0,t.createElement)("a",{href:"https://twitter.com/share?url="+s,target:"_blank",rel:"noopener noreferrer",className:"x-share"},(0,t.createElement)(qo,null),(0,l.__)("X","wordpress-seo"),(0,t.createElement)("span",{className:"screen-reader-text"},/* translators: Hidden accessibility text. */
(0,l.__)("(Opens in a new browser tab)","wordpress-seo"))))))}No.propTypes={permalink:y().string.isRequired};const Bo=(0,B.compose)([(0,n.withSelect)((e=>({permalink:e("core/editor").getPermalink()})))])(No);function $o({checklist:e,onClick:s}){let r;return r=e.every((e=>"good"===e.score))?(0,l.__)("We've analyzed your post. Everything looks good. Well done!","wordpress-seo"):(0,l.__)("We've analyzed your post. There is still room for improvement!","wordpress-seo"),(0,t.createElement)(i.Fragment,null,(0,t.createElement)("p",null,r),(0,t.createElement)(So,{checklist:e,onClick:s}))}$o.propTypes={checklist:y().array.isRequired,onClick:y().func.isRequired};const Uo=(0,B.compose)([(0,n.withSelect)((function(e){const t=e("yoast-seo/editor"),s=[];return function(e,t){t.getFocusKeyphrase()||e.push({label:(0,l.__)("No focus keyword was entered","wordpress-seo"),score:"bad"})}(s,t),Ro(s,t),Co(s,t),Io(s,t),s.push(...Object.values(t.getChecklistItems())),{checklist:s}})),(0,n.withDispatch)((function(e){const{closePublishSidebar:t,openGeneralSidebar:s}=e("core/edit-post");return{onClick:()=>{t(),s("yoast-seo/seo-sidebar")}}}))])($o),Wo=({store:e="yoast-seo/editor",...s})=>{const r=(0,n.useSelect)((t=>t(e).selectLinkParams()),[e]),a=(0,i.createInterpolateElement)((0,l.sprintf)(/* translators:  %1$s expands to Yoast, %2$s expands to a 'strong' start tag, %2$s to a 'strong' end tag. */
(0,l.__)("The %1$s %2$sultimate Black Friday checklist%3$s helps you prepare in time, so you can boost your results during this sale.","wordpress-seo"),"Yoast","<strong>","</strong>"),{strong:(0,t.createElement)("strong",null)});return(0,t.createElement)(mo,{id:"black-friday-2023-sidebar-checklist",promoId:"black-friday-2023-checklist",alertKey:"black-friday-2023-sidebar-checklist",store:e,title:(0,l.__)("Is your WooCommerce store ready for Black Friday?","wordpress-seo"),...s},a," ",(0,t.createElement)("a",{href:(0,Fs.addQueryArgs)("https://yoa.st/black-friday-checklist",r),target:"_blank",rel:"noreferrer"},(0,l.__)("Get the checklist and start optimizing now!","wordpress-seo")))};Wo.propTypes={store:y().string};const Ko="trustpilot-review-notification",Ho="yoast-seo/editor",Vo=()=>{const e=(0,n.useSelect)((e=>e(Ho).getIsPremium()),[]),t=(0,n.useSelect)((e=>e(Ho).isAlertDismissed(Ko)),[]),{overallScore:s}=(0,n.useSelect)((e=>e(Ho).getResultsForFocusKeyword()),[]),{dismissAlert:r}=(0,n.useDispatch)(Ho),a=(0,i.useCallback)((()=>r(Ko)),[r]),[o,l]=(0,i.useState)(!1);return(0,i.useEffect)((()=>{var e;"good"===(null===(e=To(s))||void 0===e?void 0:e.className)&&l(!0)}),[s]),{shouldShow:!e&&!t&&o,dismiss:a}},Yo=(0,Y.makeOutboundLink)(),jo=()=>{const{shouldShow:e,dismiss:s}=Vo(),{locationContext:r}=(0,p.useRootContext)(),a=(0,n.useSelect)((e=>e(Ho).selectLink("https://yoa.st/trustpilot-review",{context:r})),[r]);return(0,t.createElement)(po,{alertKey:Ko,store:Ho,id:Ko,title:(0,l.__)("Show Yoast SEO some love!","wordpress-seo"),hasIcon:!1,isAlertDismissed:!e,onDismissed:s},(0,l.__)("Happy with the plugin?","wordpress-seo")," ",(0,t.createElement)(Yo,{href:a,rel:"noopener noreferrer"},(0,l.__)("Leave a quick review","wordpress-seo")),".")},zo=({store:e="yoast-seo/editor",image:s=lo,url:r,...a})=>(0,n.useSelect)((t=>t(e).getIsPremium()))?null:(0,t.createElement)(uo,{alertKey:"webinar-promo-notification",store:e,id:"webinar-promo-notification",title:(0,l.__)("Join our FREE webinar for SEO success","wordpress-seo"),image:s,url:r,...a},(0,l.__)("Feeling lost when it comes to optimizing your site for the search engines? Join our FREE webinar to gain the confidence that you need in order to start optimizing like a pro! You'll obtain the knowledge and tools to start effectively implementing SEO.","wordpress-seo")," ",(0,t.createElement)("a",{href:r,target:"_blank",rel:"noreferrer"},(0,l.__)("Sign up today!","wordpress-seo")));zo.propTypes={store:y().string,image:y().elementType,url:y().string.isRequired};const Go=zo,Zo=(e="yoast-seo/editor")=>{const t=(0,n.select)(e).isPromotionActive("black-friday-2023-promotion"),s=(0,n.select)(e).isAlertDismissed("black-friday-2023-promotion");return t?s:((e="yoast-seo/editor")=>{const t=(0,n.select)(e).isPromotionActive("black-friday-2023-checklist"),s=(0,n.select)(e).isAlertDismissed("black-friday-2023-sidebar-checklist");return!t||s})(e)},Xo=t.forwardRef((function(e,s){return t.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true",ref:s},e),t.createElement("path",{d:"M11 3a1 1 0 10-2 0v1a1 1 0 102 0V3zM15.657 5.757a1 1 0 00-1.414-1.414l-.707.707a1 1 0 001.414 1.414l.707-.707zM18 10a1 1 0 01-1 1h-1a1 1 0 110-2h1a1 1 0 011 1zM5.05 6.464A1 1 0 106.464 5.05l-.707-.707a1 1 0 00-1.414 1.414l.707.707zM5 10a1 1 0 01-1 1H3a1 1 0 110-2h1a1 1 0 011 1zM8 16v-1h4v1a2 2 0 11-4 0zM12 14c.015-.34.208-.646.477-.859a4 4 0 10-4.954 0c.27.213.462.519.476.859h4.002z"}))})),Qo=({id:e,postTypeName:s,children:r,title:a,isOpen:n,close:o,open:c,shouldCloseOnClickOutside:d,showChangesWarning:u,SuffixHeroIcon:m})=>(0,t.createElement)(i.Fragment,null,n&&(0,t.createElement)(p.LocationProvider,{value:"modal"},(0,t.createElement)(X,{title:a,onRequestClose:o,additionalClassName:"yoast-collapsible-modal yoast-post-settings-modal",id:"id",shouldCloseOnClickOutside:d},(0,t.createElement)("div",{className:"yoast-content-container"},(0,t.createElement)("div",{className:"yoast-modal-content"},r)),(0,t.createElement)("div",{className:"yoast-notice-container"},(0,t.createElement)("hr",null),(0,t.createElement)("div",{className:"yoast-button-container"},u&&(0,t.createElement)("p",null,/* Translators: %s translates to the Post Label in singular form */
(0,l.sprintf)((0,l.__)("Make sure to save your %s for changes to take effect","wordpress-seo"),s)),(0,t.createElement)("button",{className:"yoast-button yoast-button--primary yoast-button--post-settings-modal",type:"button",onClick:o},/* Translators: %s translates to the Post Label in singular form */
(0,l.sprintf)((0,l.__)("Return to your %s","wordpress-seo"),s)))))),(0,t.createElement)(re,{id:e+"-open-button",title:a,SuffixHeroIcon:m,suffixIcon:m?null:{size:"20px",icon:"pencil-square"},onClick:c}));Qo.propTypes={id:y().string.isRequired,postTypeName:y().string.isRequired,children:y().oneOfType([y().node,y().arrayOf(y().node)]).isRequired,title:y().string.isRequired,isOpen:y().bool.isRequired,open:y().func.isRequired,close:y().func.isRequired,shouldCloseOnClickOutside:y().bool,showChangesWarning:y().bool,SuffixHeroIcon:y().object},Qo.defaultProps={shouldCloseOnClickOutside:!0,showChangesWarning:!0};const Jo=Qo,ei=(0,B.compose)([(0,n.withSelect)(((e,t)=>{const{getPostOrPageString:s,getIsModalOpen:r}=e("yoast-seo/editor");return{postTypeName:s(),isOpen:r(t.id)}})),(0,n.withDispatch)(((e,t)=>{const{openEditorModal:s,closeEditorModal:r}=e("yoast-seo/editor");return{open:()=>s(t.id),close:r}}))])(Jo),ti=w()(Xo)`
	width: 18px;
	height: 18px;
	margin: 3px;
`,si=({location:e})=>{const s=(0,n.useSelect)((e=>e("yoast-seo/editor").getIsElementorEditor()),[]),r=(0,n.useSelect)((e=>e("yoast-seo/editor").isFleschReadingEaseAvailable()),[]),a=U();return(0,t.createElement)(ei,{title:(0,l.__)("Insights","wordpress-seo"),id:`yoast-insights-modal-${e}`,shouldCloseOnClickOutside:!s,showChangesWarning:!1,SuffixHeroIcon:(0,t.createElement)(ti,{className:"yst-text-slate-500",...a})},(0,t.createElement)("div",{className:"yoast-insights yoast-modal-content--columns"},(0,t.createElement)(Ds,{location:e}),(0,t.createElement)("div",null,r&&(0,t.createElement)("div",{className:"yoast-insights-row"},(0,t.createElement)(Es,null)),(0,t.createElement)("div",{className:"yoast-insights-row yoast-insights-row--columns"},(0,t.createElement)(fs,null),(0,t.createElement)(qs,null)),(0,hs.isFeatureEnabled)("TEXT_FORMALITY")&&(0,t.createElement)(Ws,{location:e,name:"YoastTextFormalityMetabox"}))))};si.propTypes={location:y().string},si.defaultProps={location:"sidebar"};const ri=si,ai=t.forwardRef((function(e,s){return t.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true",ref:s},e),t.createElement("path",{fillRule:"evenodd",d:"M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z",clipRule:"evenodd"}))}));class ni{constructor(e){this.refresh=e,this.loaded=!1,this.preloadThreshold=3e3,this.plugins={},this.modifications={},this._registerPlugin=this._registerPlugin.bind(this),this._ready=this._ready.bind(this),this._reloaded=this._reloaded.bind(this),this._registerModification=this._registerModification.bind(this),this._registerAssessment=this._registerAssessment.bind(this),this._applyModifications=this._applyModifications.bind(this),setTimeout(this._pollLoadingPlugins.bind(this),1500)}_registerPlugin(e,t){return(0,m.isString)(e)?(0,m.isUndefined)(t)||(0,m.isObject)(t)?!1===this._validateUniqueness(e)?(console.error("Failed to register plugin. Plugin with name "+e+" already exists"),!1):(this.plugins[e]=t,!0):(console.error("Failed to register plugin "+e+". Expected parameters `options` to be a object."),!1):(console.error("Failed to register plugin. Expected parameter `pluginName` to be a string."),!1)}_ready(e){return(0,m.isString)(e)?(0,m.isUndefined)(this.plugins[e])?(console.error("Failed to modify status for plugin "+e+". The plugin was not properly registered."),!1):(this.plugins[e].status="ready",!0):(console.error("Failed to modify status for plugin "+e+". Expected parameter `pluginName` to be a string."),!1)}_reloaded(e){return(0,m.isString)(e)?(0,m.isUndefined)(this.plugins[e])?(console.error("Failed to reload Content Analysis for plugin "+e+". The plugin was not properly registered."),!1):(this.refresh(),!0):(console.error("Failed to reload Content Analysis for "+e+". Expected parameter `pluginName` to be a string."),!1)}_registerModification(e,t,s,r){if(!(0,m.isString)(e))return console.error("Failed to register modification for plugin "+s+". Expected parameter `modification` to be a string."),!1;if(!(0,m.isFunction)(t))return console.error("Failed to register modification for plugin "+s+". Expected parameter `callable` to be a function."),!1;if(!(0,m.isString)(s))return console.error("Failed to register modification for plugin "+s+". Expected parameter `pluginName` to be a string."),!1;if(!1===this._validateOrigin(s))return console.error("Failed to register modification for plugin "+s+". The integration has not finished loading yet."),!1;const a={callable:t,origin:s,priority:(0,m.isNumber)(r)?r:10};return(0,m.isUndefined)(this.modifications[e])&&(this.modifications[e]=[]),this.modifications[e].push(a),!0}_registerAssessment(e,t,s,r){return(0,m.isString)(t)?(0,m.isObject)(s)?(0,m.isString)(r)?(t=r+"-"+t,e.addAssessment(t,s),!0):(console.error("Failed to register assessment for plugin "+r+". Expected parameter `pluginName` to be a string."),!1):(console.error("Failed to register assessment for plugin "+r+". Expected parameter `assessment` to be a function."),!1):(console.error("Failed to register test for plugin "+r+". Expected parameter `name` to be a string."),!1)}_applyModifications(e,t,s){let r=this.modifications[e];return!(0,m.isArray)(r)||r.length<1||(r=this._stripIllegalModifications(r),r.sort(((e,t)=>e.priority-t.priority)),(0,m.forEach)(r,(function(r){const a=r.callable(t,s);typeof a==typeof t?t=a:console.error("Modification with name "+e+" performed by plugin with name "+r.origin+" was ignored because the data that was returned by it was of a different type than the data we had passed it.")}))),t}_pollLoadingPlugins(e){e=(0,m.isUndefined)(e)?0:e,!0===this._allReady()?(this.loaded=!0,this.refresh()):e>=this.preloadThreshold?(this._pollTimeExceeded(),this.loaded=!0,this.refresh()):(e+=50,setTimeout(this._pollLoadingPlugins.bind(this,e),50))}_allReady(){return(0,m.reduce)(this.plugins,(function(e,t){return e&&"ready"===t.status}),!0)}_pollTimeExceeded(){(0,m.forEach)(this.plugins,(function(e,t){(0,m.isUndefined)(e.options)||"ready"===e.options.status||(console.error("Error: Plugin "+t+". did not finish loading in time."),delete this.plugins[t])}))}_stripIllegalModifications(e){return(0,m.forEach)(e,((t,s)=>{!1===this._validateOrigin(t.origin)&&delete e[s]})),e}_validateOrigin(e){return"ready"===this.plugins[e].status}_validateUniqueness(e){return(0,m.isUndefined)(this.plugins[e])}}let oi=null;const ii=()=>{if(null===oi){const e=(0,n.dispatch)("yoast-seo/editor").runAnalysis;oi=window.YoastSEO.app&&window.YoastSEO.app.pluggable?window.YoastSEO.app.pluggable:new ni(e)}return oi},li=(e,t,s)=>ii().loaded?ii()._applyModifications(e,t,s):t,ci={name:"author_first_name",label:"Author first name",placeholder:"%%author_first_name%%",aliases:[],getReplacement:function(){return(0,m.get)(window,"wpseoScriptData.analysis.plugins.replaceVars.replace_vars.author_first_name","")},regexp:new RegExp("%%author_first_name%%","g")},di={name:"author_last_name",label:"Author last name",placeholder:"%%author_last_name%%",aliases:[],getReplacement:function(){return(0,m.get)(window,"wpseoScriptData.analysis.plugins.replaceVars.replace_vars.author_last_name","")},regexp:new RegExp("%%author_last_name%%","g")},pi={name:"currentdate",label:"Current date",placeholder:"%%currentdate%%",aliases:[],getReplacement:function(){return(0,m.get)(window,"wpseoScriptData.analysis.plugins.replaceVars.replace_vars.currentdate","")},regexp:new RegExp("%%currentdate%%","g")},ui={name:"currentday",label:"Current day",placeholder:"%%currentday%%",aliases:[],getReplacement:function(){return(0,m.get)(window,"wpseoScriptData.analysis.plugins.replaceVars.replace_vars.currentday","")},regexp:new RegExp("%%currentday%%","g")},mi={name:"currentmonth",label:"Current month",placeholder:"%%currentmonth%%",aliases:[],getReplacement:function(){return(0,m.get)(window,"wpseoScriptData.analysis.plugins.replaceVars.replace_vars.currentmonth","")},regexp:new RegExp("%%currentmonth%%","g")},hi={name:"category",label:"Category",placeholder:"%%category%%",aliases:[],getReplacement:function(){return(0,m.get)(window,"wpseoScriptData.analysis.plugins.replaceVars.replace_vars.category","")},regexp:new RegExp("%%category%%","g")},gi={name:"category_title",label:"Category Title",placeholder:"%%category_title%%",aliases:[],getReplacement:function(){return(0,m.get)(window,"wpseoScriptData.analysis.plugins.replaceVars.replace_vars.category_title","")},regexp:new RegExp("%%category_title%%","g")},yi={name:"currentyear",label:"Current year",placeholder:"%%currentyear%%",aliases:[],getReplacement:function(){return(0,m.get)(window,"wpseoScriptData.analysis.plugins.replaceVars.replace_vars.currentyear","")},regexp:new RegExp("%%currentyear%%","g")},fi={name:"date",label:"Date",placeholder:"%%date%%",aliases:[],getReplacement:function(){return(0,m.get)(window,"wpseoScriptData.analysis.plugins.replaceVars.replace_vars.date","")},regexp:new RegExp("%%date%%","g")},wi={name:"excerpt",label:"Excerpt",placeholder:"%%excerpt%%",aliases:[{name:"excerpt_only",label:"Excerpt only",placeholder:"%%excerpt_only%%"}],getReplacement:function(){return(0,n.select)("yoast-seo/editor").getEditorDataExcerptWithFallback()},regexp:new RegExp("%%excerpt%%|%%excerpt_only%%","g")},bi={name:"focuskw",label:"Focus keyphrase",placeholder:"%%focuskw%%",aliases:[],getReplacement:function(){return(0,n.select)("yoast-seo/editor").getFocusKeyphrase()},regexp:new RegExp("%%focuskw%%|%%keyword%%","g")},Ei={name:"id",label:"ID",placeholder:"%%id%%",aliases:[],getReplacement:function(){return(0,m.get)(window,"wpseoScriptData.analysis.plugins.replaceVars.replace_vars.id","")},regexp:new RegExp("%%id%%","g")},vi={name:"modified",label:"Modified",placeholder:"%%modified%%",aliases:[],getReplacement:function(){return(0,m.get)(window,"wpseoScriptData.analysis.plugins.replaceVars.replace_vars.modified","")},regexp:new RegExp("%%modified%%","g")},ki={name:"name",label:"Name",placeholder:"%%name%%",aliases:[],getReplacement:function(){return(0,m.get)(window,"wpseoScriptData.analysis.plugins.replaceVars.replace_vars.name","")},regexp:new RegExp("%%name%%","g")},_i={name:"page",label:"Page",placeholder:"%%page%%",aliases:[],getReplacement:function(){return(0,m.get)(window,"wpseoScriptData.analysis.plugins.replaceVars.replace_vars.page","")},regexp:new RegExp("%%page%%","g")},xi={name:"pagenumber",label:"Pagenumber",placeholder:"%%pagenumber%%",aliases:[],getReplacement:function(){return(0,m.get)(window,"wpseoScriptData.analysis.plugins.replaceVars.replace_vars.pagenumber","")},regexp:new RegExp("%%pagenumber%%","g")},Si={name:"pagetotal",label:"Pagetotal",placeholder:"%%pagetotal%%",aliases:[],getReplacement:function(){return(0,m.get)(window,"wpseoScriptData.analysis.plugins.replaceVars.replace_vars.pagetotal","")},regexp:new RegExp("%%pagetotal%%","g")},Ti={name:"permalink",label:"Permalink",placeholder:"%%permalink%%",aliases:[],getReplacement:function(){return(0,m.get)(window,"wpseoScriptData.analysis.plugins.replaceVars.replace_vars.permalink","")},regexp:new RegExp("%%permalink%%","g")},Ri={name:"post_content",label:"Post Content",placeholder:"%%post_content%%",aliases:[],getReplacement:function(){return(0,m.get)(window,"wpseoScriptData.analysis.plugins.replaceVars.replace_vars.post_content","")},regexp:new RegExp("%%post_content%%","g")},Ci={name:"post_day",label:"Post Day",placeholder:"%%post_day%%",aliases:[],getReplacement:function(){return(0,m.get)(window,"wpseoScriptData.analysis.plugins.replaceVars.replace_vars.post_day","")},regexp:new RegExp("%%post_day%%","g")},Ii={name:"post_month",label:"Post Month",placeholder:"%%post_month%%",aliases:[],getReplacement:function(){return(0,m.get)(window,"wpseoScriptData.analysis.plugins.replaceVars.replace_vars.post_month","")},regexp:new RegExp("%%post_month%%","g")},Ai={name:"post_year",label:"Post Year",placeholder:"%%post_year%%",aliases:[],getReplacement:function(){return(0,m.get)(window,"wpseoScriptData.analysis.plugins.replaceVars.replace_vars.post_year","")},regexp:new RegExp("%%post_year%%","g")},Li={name:"pt_plural",label:"Post type (plural)",placeholder:"%%pt_plural%%",aliases:[],getReplacement:function(){return(0,m.get)(window,"wpseoScriptData.analysis.plugins.replaceVars.replace_vars.pt_plural","")},regexp:new RegExp("%%pt_plural%%","g")},Pi={name:"pt_single",label:"Post type (singular)",placeholder:"%%pt_single%%",aliases:[],getReplacement:function(){return(0,m.get)(window,"wpseoScriptData.analysis.plugins.replaceVars.replace_vars.pt_single","")},regexp:new RegExp("%%pt_single%%","g")},Fi={name:"primary_category",label:"Primary category",placeholder:"%%primary_category%%",aliases:[],getReplacement:function(){return(0,m.get)(window,"wpseoScriptData.analysis.plugins.replaceVars.replace_vars.primary_category","")},regexp:new RegExp("%%primary_category%%","g")},Oi={name:"searchphrase",label:"Search phrase",placeholder:"%%searchphrase%%",aliases:[],getReplacement:function(){return(0,m.get)(window,"wpseoScriptData.analysis.plugins.replaceVars.replace_vars.searchphrase","")},regexp:new RegExp("%%searchphrase%%","g")},Mi={name:"sep",label:"Separator",placeholder:"%%sep%%",aliases:[],getReplacement:function(){return(0,m.get)(window,"wpseoScriptData.analysis.plugins.replaceVars.replace_vars.sep","")},regexp:new RegExp("%%sep%%(\\s*%%sep%%)*","g")},Di={name:"sitedesc",label:"Tagline",placeholder:"%%sitedesc%%",aliases:[],getReplacement:function(){return(0,m.get)(window,"wpseoScriptData.analysis.plugins.replaceVars.replace_vars.sitedesc","")},regexp:new RegExp("%%sitedesc%%","g")},qi={name:"sitename",label:"Site title",placeholder:"%%sitename%%",aliases:[],getReplacement:function(){return(0,m.get)(window,"wpseoScriptData.analysis.plugins.replaceVars.replace_vars.sitename","")},regexp:new RegExp("%%sitename%%","g")},Ni={name:"tag",label:"Tag",placeholder:"%%tag%%",aliases:[],getReplacement:function(){return(0,m.get)(window,"wpseoScriptData.analysis.plugins.replaceVars.replace_vars.tag","")},regexp:new RegExp("%%tag%%","g")},Bi={name:"term404",label:"Term404",placeholder:"%%term404%%",aliases:[],getReplacement:function(){return(0,m.get)(window,"wpseoScriptData.analysis.plugins.replaceVars.replace_vars.term404","")},regexp:new RegExp("%%term404%%","g")},$i={name:"term_description",label:"Term description",placeholder:"%%term_description%%",aliases:[{name:"tag_description",label:"Tag description",placeholder:"%%tag_description%%"},{name:"category_description",label:"Category description",placeholder:"%%category_description%%"}],getReplacement:function(){return(0,m.get)(window,"YoastSEO.app.rawData.text","")},regexp:new RegExp("%%term_description%%|%%tag_description%%|%%category_description%%","g")},Ui={name:"term_hierarchy",label:"Term hierarchy",placeholder:"%%term_hierarchy%%",aliases:[],getReplacement:function(){return(0,m.get)(window,"wpseoScriptData.analysis.plugins.replaceVars.replace_vars.term_hierarchy","")},regexp:new RegExp("%%term_hierarchy%%","g")},Wi={name:"term_title",label:"Term title",placeholder:"%%term_title%%",aliases:[],getReplacement:function(){return(0,m.get)(window,"wpseoScriptData.analysis.plugins.replaceVars.replace_vars.term_title","")},regexp:new RegExp("%%term_title%%","g")},Ki={name:"title",label:"Title",placeholder:"%%title%%",aliases:[],getReplacement:function(){return(0,n.select)("yoast-seo/editor").getEditorDataTitle()},regexp:new RegExp("%%title%%","g")},Hi={name:"user_description",label:"User description",placeholder:"%%user_description%%",aliases:[],getReplacement:function(){return(0,m.get)(window,"wpseoScriptData.analysis.plugins.replaceVars.replace_vars.user_description","")},regexp:new RegExp("%%user_description%%","g")};var Vi={source:"wpseoScriptData.analysis.plugins.replaceVars",scope:[],aliases:[]},Yi=function(e,t,s){this.placeholder=e,this.replacement=t,this.options=(0,m.defaults)(s,Vi)};Yi.prototype.getPlaceholder=function(e){return(e=e||!1)&&this.hasAlias()?this.placeholder+"|"+this.getAliases().join("|"):this.placeholder},Yi.prototype.setSource=function(e){this.options.source=e},Yi.prototype.hasScope=function(){return!(0,m.isEmpty)(this.options.scope)},Yi.prototype.addScope=function(e){this.hasScope()||(this.options.scope=[]),this.options.scope.push(e)},Yi.prototype.inScope=function(e){return!this.hasScope()||(0,m.indexOf)(this.options.scope,e)>-1},Yi.prototype.hasAlias=function(){return!(0,m.isEmpty)(this.options.aliases)},Yi.prototype.addAlias=function(e){this.hasAlias()||(this.options.aliases=[]),this.options.aliases.push(e)},Yi.prototype.getAliases=function(){return this.options.aliases};let ji=null,zi=null;const Gi=()=>{if(null===zi){zi=[];const t=(0,m.get)(window,"wpseoScriptData.analysis.plugins.replaceVars.hidden_replace_vars",[]);(null===ji&&(ji=((e="")=>{switch(""===e&&(e=(0,m.get)(window,"wpseoScriptData.analysis.plugins.replaceVars.scope","")),e){case"post":case"page":return["authorFirstName","authorLastName","category","categoryTitle","currentDate","currentDay","currentMonth","currentYear","date","excerpt","id","focusKeyphrase","modified","name","page","primaryCategory","pageNumber","pageTotal","permalink","postContent","postDay","postMonth","postYear","postTypeNamePlural","postTypeNameSingular","searchPhrase","separator","siteDescription","siteName","tag","title","userDescription"]}return[]})().map((t=>null==e?void 0:e[t])).filter(Boolean)),ji).forEach((e=>{const s=t.includes(e.name);zi.push({name:e.name,label:e.label,value:e.placeholder,hidden:s}),e.aliases.forEach((e=>{zi.push({name:e.name,label:e.label,value:e.placeholder,hidden:s})}))}))}return zi},{stripHTMLTags:Zi}=Y.strings,Xi=(e,t)=>{const s=(0,n.select)("yoast-seo/editor").getSnippetEditorTemplates();""===e.title&&(e.title=s.title),""===e.description&&(e.description=s.description);let r=0;return t.shortenedBaseUrl&&"string"==typeof t.shortenedBaseUrl&&(r=t.shortenedBaseUrl.length),e.url=e.url.replace(/\s+/g,"-"),"-"===e.url[e.url.length-1]&&(e.url=e.url.slice(0,-1)),"-"===e.url[r]&&(e.url=e.url.slice(0,r)+e.url.slice(r+1)),{url:e.url,title:Zi(li("data_page_title",e.title)),description:Zi(li("data_meta_desc",e.description)),filteredSEOTitle:Zi(li("data_page_title",e.filteredSEOTitle))}},Qi=({isLoading:e,onLoad:s,location:r,...a})=>((0,i.useEffect)((()=>{setTimeout((()=>{e&&s()}))})),e?null:(0,t.createElement)(Qt,{icon:"eye",hasPaperStyle:a.hasPaperStyle},(0,t.createElement)(Gt.SnippetEditor,{...a,descriptionPlaceholder:(0,l.__)("Please provide a meta description by editing the snippet below.","wordpress-seo"),mapEditorDataToPreview:Xi,showCloseButton:!1,idSuffix:r})));Qi.propTypes={isLoading:y().bool.isRequired,onLoad:y().func.isRequired,hasPaperStyle:y().bool.isRequired,location:y().string.isRequired};const Ji=(0,B.compose)([(0,n.withSelect)((e=>{const{getBaseUrlFromSettings:t,getDateFromSettings:s,getEditorDataImageUrl:r,getFocusKeyphrase:a,getRecommendedReplaceVars:n,getSiteIconUrlFromSettings:o,getSnippetEditorData:i,getSnippetEditorIsLoading:l,getSnippetEditorMode:c,getSnippetEditorWordsToHighlight:d,isCornerstoneContent:p,getContentLocale:u,getSiteName:m}=e("yoast-seo/editor");return{baseUrl:t(),data:i(),date:s(),faviconSrc:o(),isLoading:l(),keyword:a(),mobileImageSrc:r(),mode:c(),recommendedReplacementVariables:n(),replacementVariables:Gi(),wordsToHighlight:d(),isCornerstone:p(),locale:u(),siteName:m()}})),(0,n.withDispatch)((e=>{const{updateData:t,switchMode:s,updateAnalysisData:r,loadSnippetEditorData:a}=e("yoast-seo/editor");return{onChange:(e,r)=>{switch(e){case"mode":s(r);break;case"slug":t({slug:r});break;default:t({[e]:r})}},onChangeAnalysisData:r,onLoad:a}})),jt()])(Qi),el=w()(ai)`
	width: 18px;
	height: 18px;
	margin: 3px;
`,tl=()=>{const e=U(),s=(0,n.useSelect)((e=>e("yoast-seo/editor").getIsElementorEditor()),[]);return(0,t.createElement)(ei,{title:(0,l.__)("Search appearance","wordpress-seo"),id:"yoast-search-appearance-modal",shouldCloseOnClickOutside:!1,SuffixHeroIcon:(0,t.createElement)(el,{className:"yst-text-slate-500",...e})},!0===s&&(0,t.createElement)(Ji,{showCloseButton:!1,hasPaperStyle:!1}),!1===s&&(0,t.createElement)(us,{showCloseButton:!1,hasPaperStyle:!1}))},sl=t.forwardRef((function(e,s){return t.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true",ref:s},e),t.createElement("path",{d:"M15 8a3 3 0 10-2.977-2.63l-4.94 2.47a3 3 0 100 4.319l4.94 2.47a3 3 0 10.895-1.789l-4.94-2.47a3.027 3.027 0 000-.74l4.94-2.47C13.456 7.68 14.19 8 15 8z"}))})),rl=w()(R.Collapsible)`
	h2 > button {
		padding-left: 0;
		padding-top: 16px;

		&:hover {
			background-color: #f0f0f0;
		}
	}

	div[class^="collapsible_content"] {
		padding: 24px 0;
		margin: 0 24px;
		border-top: 1px solid rgba(0,0,0,0.2);
	}

`,al=e=>(0,t.createElement)(rl,{hasPadding:!1,hasSeparator:!0,...e}),nl=w()(sl)`
	width: 18px;
	height: 18px;
	margin: 3px;
`,ol=e=>{const{useOpenGraphData:s,useTwitterData:r}=e;if(!s&&!r)return;const a=U();return(0,t.createElement)(ei
/* translators: Social media appearance refers to a preview of how a page will be represented on social media. */,{title:(0,l.__)("Social media appearance","wordpress-seo"),id:"yoast-social-appearance-modal",shouldCloseOnClickOutside:!1,SuffixHeroIcon:(0,t.createElement)(nl,{className:"yst-text-slate-500",...a})},s&&(0,t.createElement)(i.Fragment,null,(0,t.createElement)(ka,null,(0,l.__)("Determine how your post should look on social media like Facebook, X, Instagram, WhatsApp, Threads, LinkedIn, Slack, and more.","wordpress-seo")),(0,t.createElement)(ya,null),r&&(0,t.createElement)(va,null,(0,l.__)("To customize the appearance of your post specifically for X, please fill out the 'X appearance' settings below. If you leave these settings untouched, the 'Social media appearance' settings mentioned above will also be applied for sharing on X.","wordpress-seo"))),s&&r&&(0,t.createElement)(al,{title:(0,l.__)("X appearance","wordpress-seo"),hasSeparator:!0,initialIsOpen:!1},(0,t.createElement)(Ea,null)),!s&&r&&(0,t.createElement)(i.Fragment,null,(0,t.createElement)(ka,null,(0,l.__)("To customize the appearance of your post specifically for X, please fill out the 'X appearance' settings below.","wordpress-seo")),(0,t.createElement)(Ea,null)))};ol.propTypes={useOpenGraphData:y().bool.isRequired,useTwitterData:y().bool.isRequired};const il=ol,ll=e=>{const[s,r]=(0,i.useState)(!1),{prefixIcon:a}=e;return(0,t.createElement)("div",{className:"yoast components-panel__body "+(s?"is-opened":"")},(0,t.createElement)("h2",{className:"components-panel__body-title"},(0,t.createElement)("button",{onClick:function(){r(!s)},className:"components-button components-panel__body-toggle",type:"button",id:e.buttonId},(0,t.createElement)("span",{className:"yoast-icon-span",style:{fill:`${a&&a.color||""}`}},a&&(0,t.createElement)(R.SvgIcon,{icon:a.icon,color:a.color,size:a.size})),(0,t.createElement)("span",{className:"yoast-title-container"},(0,t.createElement)("div",{className:"yoast-title"},e.title),(0,t.createElement)("div",{className:"yoast-subtitle"},e.subTitle)),e.hasBetaBadgeLabel&&(0,t.createElement)(R.BetaBadge,null),(0,t.createElement)("span",{className:"yoast-chevron","aria-hidden":"true"}))),s&&e.children)},cl=ll;function dl({settings:e}){const s=(({webinarIntroUrl:e})=>{const{shouldShow:s}=Vo(),r=(e=>{for(const t of e)if(null!=t&&t.getIsEligible())return t;return null})([{getIsEligible:()=>s,component:jo},{getIsEligible:Zo,component:()=>(0,t.createElement)(Go,{hasIcon:!1,image:null,url:e})},{getIsEligible:go,component:()=>(0,t.createElement)(Wo,{hasIcon:!1})},{getIsEligible:()=>!0,component:()=>(0,t.createElement)(ho,{hasIcon:!1})}]);return(null==r?void 0:r.component)||null})({webinarIntroUrl:(0,m.get)(window,"wpseoScriptData.webinarIntroBlockEditorUrl","https://yoa.st/webinar-intro-block-editor")});return(0,t.createElement)(i.Fragment,null,(0,t.createElement)(k.Fill,{name:"YoastSidebar"},(0,t.createElement)(or,{key:"warning",renderPriority:1},(0,t.createElement)(ms,null),(0,t.createElement)("div",{style:{margin:"0 16px"}},s&&(0,t.createElement)(s,null))),e.isKeywordAnalysisActive&&(0,t.createElement)(or,{key:"keyword-input",renderPriority:8},(0,t.createElement)(Yt.KeywordInput,{isSEMrushIntegrationActive:e.isSEMrushIntegrationActive})),e.isKeywordAnalysisActive&&(0,t.createElement)(or,{key:"seo",renderPriority:10},(0,t.createElement)(i.Fragment,null,(0,t.createElement)(Yt.SeoAnalysis,{shouldUpsell:e.shouldUpsell,shouldUpsellWordFormRecognition:e.isWordFormRecognitionActive}),e.shouldUpsell&&(0,t.createElement)(cn,{location:"sidebar"}))),e.isContentAnalysisActive&&(0,t.createElement)(or,{key:"readability",renderPriority:20},(0,t.createElement)(Yt.ReadabilityAnalysis,{shouldUpsell:e.shouldUpsell})),e.isInclusiveLanguageAnalysisActive&&(0,t.createElement)(or,{key:"inclusive-language-analysis",renderPriority:21},(0,t.createElement)(Yt.InclusiveLanguageAnalysis,null)),e.isKeywordAnalysisActive&&(0,t.createElement)(or,{key:"additional-keywords-upsell",renderPriority:22},e.shouldUpsell&&(0,t.createElement)(un,null)),e.isKeywordAnalysisActive&&e.isWincherIntegrationActive&&(0,t.createElement)(or,{renderPriority:23},(0,t.createElement)(Vt,{location:"sidebar"})),e.shouldUpsell&&(0,t.createElement)(or,{key:"internal-linking-suggestions-upsell",renderPriority:25},(0,t.createElement)(ar,null)),(0,t.createElement)(or,{key:"search-appearance",renderPriority:26},(0,t.createElement)(tl,null)),(e.useOpenGraphData||e.useTwitterData)&&(0,t.createElement)(or,{key:"social-appearance",renderPriority:27},(0,t.createElement)(il,{useOpenGraphData:e.useOpenGraphData,useTwitterData:e.useTwitterData})),e.displaySchemaSettings&&(0,t.createElement)(or,{key:"schema",renderPriority:28},(0,t.createElement)(cl,{title:(0,l.__)("Schema","wordpress-seo")},(0,t.createElement)($a,null))),e.displayAdvancedTab&&(0,t.createElement)(or,{key:"advanced",renderPriority:29},(0,t.createElement)(cl,{title:(0,l.__)("Advanced","wordpress-seo")},(0,t.createElement)(hr,null))),e.isCornerstoneActive&&(0,t.createElement)(or,{key:"cornerstone",renderPriority:30},(0,t.createElement)(zt,null)),e.isInsightsEnabled&&(0,t.createElement)(or,{renderPriority:32},(0,t.createElement)(ri,{location:"sidebar"}))))}ll.propTypes={title:y().string.isRequired,children:y().oneOfType([y().node,y().arrayOf(y().node)]).isRequired,prefixIcon:y().object,subTitle:y().string,hasBetaBadgeLabel:y().bool,buttonId:y().string},ll.defaultProps={prefixIcon:null,subTitle:"",hasBetaBadgeLabel:!1,buttonId:null},dl.propTypes={settings:y().object.isRequired};const pl=(0,n.withSelect)(((e,t)=>{const{getPreferences:s}=e("yoast-seo/editor");return{settings:s(),store:t.store}}))(dl);function ul(e){const{hasTrackedKeyphrases:s,trackAll:r}=e;return(0,t.createElement)(i.Fragment,null,(0,t.createElement)(R.FieldGroup,{label:(0,l.__)("SEO performance","wordpress-seo"),linkTo:wpseoAdminL10n["shortlinks.wincher.seo_performance"]
/* translators: Hidden accessibility text. */,linkText:(0,l.__)("Learn more about the SEO performance feature.","wordpress-seo"),wrapperClassName:"yoast-field-group yoast-wincher-post-publish"}),(0,t.createElement)(at,null),s&&(0,t.createElement)("p",null,(0,l.__)("Tracking has already been enabled for one or more keyphrases of this page. Clicking the button below will enable tracking for all of its keyphrases.","wordpress-seo")),(0,t.createElement)("div",{className:"yoast"},(0,t.createElement)(R.NewButton,{variant:"secondary",small:!0,onClick:r},(0,l.__)("Track all keyphrases on this page","wordpress-seo"))),(0,t.createElement)(Vt,{location:"postpublish"}))}ul.propTypes={trackAll:y().func,hasTrackedKeyphrases:y().bool},ul.defaultProps={trackAll:()=>{},hasTrackedKeyphrases:!1};const ml=(0,B.compose)([(0,n.withSelect)((e=>{const{getWincherTrackedKeyphrases:t,hasWincherTrackedKeyphrases:s}=e("yoast-seo/editor");return{trackedKeyphrases:t(),hasTrackedKeyphrases:s()}})),(0,n.withDispatch)((e=>{const{setWincherOpenModal:t,setWincherTrackAllKeyphrases:s}=e("yoast-seo/editor");return{trackAll:()=>{s(!0),t("postpublish")}}}))])(ul);window.wp.annotations;const hl=/(<([a-z]|\/)[^<>]+>)/gi,{htmlEntitiesRegex:gl}=I.helpers.htmlEntities,yl=e=>{let t=0;return(0,m.forEachRight)(e,(e=>{const[s]=e;let r=s.length;/^<\/?br/.test(s)&&(r-=1),t+=r})),t},fl="<yoastmark class='yoast-text-mark'>",wl="</yoastmark>",bl='<yoastmark class="yoast-text-mark">';function El(e,t,s,r,a){const n=r.clientId,o=(0,d.create)({html:e,multilineTag:s.multilineTag,multilineWrapperTag:s.multilineWrapperTag}).text;return(0,m.flatMap)(a,(s=>{let a;return a=s.hasBlockPosition&&s.hasBlockPosition()?function(e,t,s,r,a){if(t===e.getBlockClientId()){let t=e.getBlockPositionStart(),n=e.getBlockPositionEnd();if(e.isMarkForFirstBlockSection()){const e=((e,t,s)=>{const r="yoast/faq-block"===s?'<strong class="schema-faq-question">':'<strong class="schema-how-to-step-name">';return{blockStartOffset:e-=r.length,blockEndOffset:t-=r.length}})(t,n,s);t=e.blockStartOffset,n=e.blockEndOffset}if(r.slice(t,n)===a.slice(t,n))return[{startOffset:t,endOffset:n}];const o=((e,t,s)=>{const r=s.slice(0,e),a=s.slice(0,t),n=((e,t,s,r)=>{const a=[...e.matchAll(hl)];s-=yl(a);const n=[...t.matchAll(hl)];return{blockStartOffset:s,blockEndOffset:r-=yl(n)}})(r,a,e,t),o=((e,t,s,r)=>{let a=[...e.matchAll(gl)];return(0,m.forEachRight)(a,(e=>{const[,t]=e;s-=t.length})),a=[...t.matchAll(gl)],(0,m.forEachRight)(a,(e=>{const[,t]=e;r-=t.length})),{blockStartOffset:s,blockEndOffset:r}})(r,a,e=n.blockStartOffset,t=n.blockEndOffset);return{blockStartOffset:e=o.blockStartOffset,blockEndOffset:t=o.blockEndOffset}})(t,n,r);return[{startOffset:o.blockStartOffset,endOffset:o.blockEndOffset}]}return[]}(s,n,r.name,e,o):function(e,t){const s=t.getOriginal().replace(/(<([^>]+)>)/gi,""),r=t.getMarked().replace(/(<(?!\/?yoastmark)[^>]+>)/gi,""),a=function(e,t,s=!0){const r=[];if(0===e.length)return r;let a,n=0;for(s||(t=t.toLowerCase(),e=e.toLowerCase());(a=e.indexOf(t,n))>-1;)r.push(a),n=a+t.length;return r}(e,s);if(0===a.length)return[];const n=function(e){let t=e.indexOf(fl);const s=t>=0;s||(t=e.indexOf(bl));let r=null;const a=[];for(;t>=0;){if(r=(e=s?e.replace(fl,""):e.replace(bl,"")).indexOf(wl),r<t)return[];e=e.replace(wl,""),a.push({startOffset:t,endOffset:r}),t=s?e.indexOf(fl):e.indexOf(bl),r=null}return a}(r),o=[];return n.forEach((e=>{a.forEach((r=>{const a=r+e.startOffset;let n=r+e.endOffset;0===e.startOffset&&e.endOffset===t.getOriginal().length&&(n=r+s.length),o.push({startOffset:a,endOffset:n})}))})),o}(o,s),a?a.map((e=>({...e,block:n,richTextIdentifier:t}))):[]}))}const vl=e=>e[0].toUpperCase()+e.slice(1),kl=(e,t,s,r,a)=>(e=e.map((e=>{const n=`${e.id}-${a[0]}`,o=`${e.id}-${a[1]}`,i=vl(a[0]),l=vl(a[1]),c=e[`json${i}`],d=e[`json${l}`],{marksForFirstSection:p,marksForSecondSection:u}=((e,t)=>({marksForFirstSection:e.filter((e=>e.hasBlockPosition&&e.hasBlockPosition()?e.getBlockAttributeId()===t.id&&e.isMarkForFirstBlockSection():e)),marksForSecondSection:e.filter((e=>e.hasBlockPosition&&e.hasBlockPosition()?e.getBlockAttributeId()===t.id&&!e.isMarkForFirstBlockSection():e))}))(t,e),m=El(c,n,s,r,p),h=El(d,o,s,r,u);return m.concat(h)})),(0,m.flattenDeep)(e)),_l="yoast";let xl=[];const Sl={"core/paragraph":[{key:"content"}],"core/list":[{key:"values",multilineTag:"li",multilineWrapperTag:["ul","ol"]}],"core/list-item":[{key:"content"}],"core/heading":[{key:"content"}],"core/audio":[{key:"caption"}],"core/embed":[{key:"caption"}],"core/gallery":[{key:"caption"}],"core/image":[{key:"caption"}],"core/table":[{key:"caption"}],"core/video":[{key:"caption"}],"yoast/faq-block":[{key:"questions"}],"yoast/how-to-block":[{key:"steps"},{key:"jsonDescription"}]};function Tl(){const e=xl.shift();e&&((0,n.dispatch)("core/annotations").__experimentalAddAnnotation(e),Rl())}function Rl(){(0,m.isFunction)(window.requestIdleCallback)?window.requestIdleCallback(Tl,{timeout:1e3}):setTimeout(Tl,150)}function Cl(){const e=(0,n.select)("core/block-editor").getSelectedBlock(),t=(0,n.select)("yoast-seo/editor").getActiveMarker();if(!e||!t)return;var s;s=e.clientId,(0,n.select)("core/annotations").__experimentalGetAnnotations().filter((e=>e.blockClientId===s&&e.source===_l)).forEach((e=>{(0,n.dispatch)("core/annotations").__experimentalRemoveAnnotation(e.id)}));const r=(0,n.select)("yoast-seo/editor").getResultById(t);if(void 0===r)return;const a=r.marks;var o;o=((e,t)=>{return(0,m.flatMap)((s=e.name,Sl.hasOwnProperty(s)?Sl[s]:[]),(s=>"yoast/faq-block"===e.name?((e,t,s)=>{const r=t.attributes[e.key];return 0===r.length?[]:kl(r,s,e,t,["question","answer"])})(s,e,t):"yoast/how-to-block"===e.name?((e,t,s)=>{const r=t.attributes[e.key];if(r&&0===r.length)return[];const a=[];return"steps"===e.key&&a.push(kl(r,s,e,t,["name","text"])),"jsonDescription"===e.key&&(s=s.filter((e=>e.hasBlockPosition&&e.hasBlockPosition()?!e.getBlockAttributeId():e)),a.push(El(r,"description",e,t,s))),(0,m.flattenDeep)(a)})(s,e,t):function(e,t,s){const r=e.key,a=((e,t)=>{const s=e.attributes[t];return"string"==typeof s?s:s.toString()})(t,r);return El(a,r,e,t,s)}(s,e,t)));var s})(e,a),xl=o.map((e=>({blockClientId:e.block,source:_l,richTextIdentifier:e.richTextIdentifier,range:{start:e.startOffset,end:e.endOffset}}))),Rl()}const Il=window.wp.htmlEntities;function Al({isActive:e,activeAttributes:s,addingLink:r,value:a,onChange:n,speak:o,stopAddingLink:c}){const p=(0,i.useMemo)(m.uniqueId,[r]),[u,h]=(0,i.useState)(),g=(0,i.useMemo)((()=>{const t=window.getSelection();if(!t.rangeCount)return;const s=t.getRangeAt(0);if(r&&!e)return s;let a=s.startContainer;for(a=a.nextElementSibling||a;a.nodeType!==window.Node.ELEMENT_NODE;)a=a.parentNode;return a.closest("a")}),[r,a.start,a.end]),y={url:s.url,type:s.type,id:s.id,opensInNewTab:"_blank"===s.target,noFollow:s.rel&&s.rel.split(" ").includes("nofollow"),sponsored:s.rel&&s.rel.split(" ").includes("sponsored"),...u},f=e=>y.url===e.url&&y.opensInNewTab!==e.opensInNewTab||y.noFollow!==e.noFollow||y.sponsored!==e.sponsored,w=e=>{if("number"==typeof e||"string"==typeof e)return String(e)},b=(0,i.useCallback)((t=>{t={...u,...t};const s=f(y);if((e=>f(e)&&!0===e.sponsored&&!0!==y.Sponsored)(t)&&(t.noFollow=!0),(e=>f(e)&&!1===e.noFollow&&!1!==y.noFollow)(t)&&(t.sponsored=!1),(e=>f(e)&&!e.url)(t))return void h(t);const r=(0,Fs.prependHTTP)(t.url),i=function({url:e,opensInNewWindow:t,noFollow:s,sponsored:r}){const a={type:"core/link",attributes:{url:e}};let n=[];return t&&(a.attributes.target="_blank",n.push("noreferrer noopener")),r&&(n.push("sponsored"),n.push("nofollow")),s&&n.push("nofollow"),n.length>0&&(n=(0,m.uniq)(n),a.attributes.rel=n.join(" ")),a}({url:r,type:t.type,id:w(t.id),opensInNewWindow:t.opensInNewTab,noFollow:t.noFollow,sponsored:t.sponsored});if((0,d.isCollapsed)(a)&&!e){const e=((e,t)=>e.title?e.title:t)(t,r),s=(0,d.applyFormat)((0,d.create)({text:e}),i,0,e.length);n((0,d.insert)(a,s))}else{const e=(0,d.applyFormat)(a,i);e.start=e.end,e.activeFormats=[],n(e)}s||c(),(t=>{!function(e){if(!e)return!1;const t=e.trim();if(!t)return!1;if(/^\S+:/.test(t)){const e=(0,Fs.getProtocol)(t);if(!(0,Fs.isValidProtocol)(e))return!1;if((0,m.startsWith)(e,"http")&&!/^https?:\/\/[^\/\s]/i.test(t))return!1;const s=(0,Fs.getAuthority)(t);if(!(0,Fs.isValidAuthority)(s))return!1;const r=(0,Fs.getPath)(t);if(r&&!(0,Fs.isValidPath)(r))return!1;const a=(0,Fs.getQueryString)(t);if(a&&!(0,Fs.isValidQueryString)(a))return!1;const n=(0,Fs.getFragment)(t);if(n&&!(0,Fs.isValidFragment)(n))return!1}return!((0,m.startsWith)(t,"#")&&!(0,Fs.isValidFragment)(t))}(t)?o((0,l.__)("Warning: the link has been inserted but may have errors. Please test it.","wordpress-seo"),"assertive"):o(e?(0,l.__)("Link edited.","wordpress-seo"):(0,l.__)("Link inserted.","wordpress-seo"),"assertive")})(r)}),[]),E=(0,t.createElement)(za,{href:window.wpseoAdminL10n["shortlinks.nofollow_sponsored"],className:"dashicons"},(0,t.createElement)("span",{className:"screen-reader-text"},/* translators: Hidden accessibility text. */
(0,l.__)("Learn more about marking a link as nofollow or sponsored.","wordpress-seo"))),v=As((0,l.sprintf)((0,l.__)("Search engines should ignore this link (mark as %1$snofollow%2$s)%3$s","wordpress-seo"),"<code>","</code>","<helplink />"),{code:(0,t.createElement)("code",null),helplink:E}),_=As((0,l.sprintf)((0,l.__)("This is a sponsored link or advert (mark as %1$ssponsored%2$s)%3$s","wordpress-seo"),"<code>","</code>","<helplink />"),{code:(0,t.createElement)("code",null),helplink:E}),x=[{id:"opensInNewTab",title:(0,l.__)("Open in new tab","wordpress-seo")},{id:"noFollow",title:v},{id:"sponsored",title:_}],{__experimentalLinkControl:S}=window.wp.blockEditor;return(0,t.createElement)(k.Popover,{key:p,anchor:g,focusOnMount:!!r&&"firstElement",onClose:c,position:"bottom center",placement:"bottom",shift:!0},(0,t.createElement)(S,{value:y,onChange:b,forceIsEditingLink:r,settings:x}))}Al.propTypes={isActive:y().bool,activeAttributes:y().object,addingLink:y().bool,value:y().object,onChange:y().func,speak:y().func.isRequired,stopAddingLink:y().func.isRequired};const Ll=(0,k.withSpokenMessages)(Al),Pl="core/link",Fl=(0,l.__)("Link","wordpress-seo"),Ol={name:Pl,title:Fl,tagName:"a",className:null,attributes:{url:"href",target:"target",rel:"rel"},replaces:"core/link",__unstablePasteRule(e,{html:t,plainText:s}){if((0,d.isCollapsed)(e))return e;const r=(t||s).replace(/<[^>]+>/g,"").trim();return(0,Fs.isURL)(r)?(window.console.log("Created link:\n\n",r),(0,d.applyFormat)(e,{type:Pl,attributes:{url:(0,Il.decodeEntities)(r)}})):e},edit:(0,k.withSpokenMessages)(class extends i.Component{constructor(){super(...arguments),this.addLink=this.addLink.bind(this),this.stopAddingLink=this.stopAddingLink.bind(this),this.onRemoveFormat=this.onRemoveFormat.bind(this),this.state={addingLink:!1}}addLink(){const{value:e,onChange:t}=this.props,s=(0,d.getTextContent)((0,d.slice)(e));s&&(0,Fs.isURL)(s)?t((0,d.applyFormat)(e,{type:Pl,attributes:{url:s}})):s&&(0,Fs.isEmail)(s)?t((0,d.applyFormat)(e,{type:Pl,attributes:{url:`mailto:${s}`}})):this.setState({addingLink:!0})}stopAddingLink(){this.setState({addingLink:!1}),this.props.onFocus()}onRemoveFormat(){const{value:e,onChange:t,speak:s}=this.props;t((0,d.removeFormat)(e,Pl)),s((0,l.__)("Link removed.","wordpress-seo"),"assertive")}render(){const{isActive:e,activeAttributes:s,value:r,onChange:a}=this.props,{RichTextToolbarButton:n,RichTextShortcut:o}=window.wp.blockEditor;return(0,t.createElement)(i.Fragment,null,(0,t.createElement)(o,{type:"primary",character:"k",onUse:this.addLink}),(0,t.createElement)(o,{type:"primaryShift",character:"k",onUse:this.onRemoveFormat}),e&&(0,t.createElement)(n,{name:"link",icon:"editor-unlink",title:(0,l.__)("Unlink","wordpress-seo"),onClick:this.onRemoveFormat,isActive:e,shortcutType:"primaryShift",shortcutCharacter:"k"}),!e&&(0,t.createElement)(n,{name:"link",icon:"admin-links",title:Fl,onClick:this.addLink,isActive:e,shortcutType:"primary",shortcutCharacter:"k"}),(this.state.addingLink||e)&&(0,t.createElement)(Ll,{addingLink:this.state.addingLink,stopAddingLink:this.stopAddingLink,isActive:e,activeAttributes:s,value:r,onChange:a}))}})};function Ml(){const e=h();return(0,m.get)(e,"contentLocale","en_US")}const{updateReplacementVariable:Dl,updateData:ql,hideReplacementVariables:Nl,setContentImage:Bl,updateSettings:$l,setEditorDataContent:Ul,setEditorDataTitle:Wl,setEditorDataExcerpt:Kl,setEditorDataImageUrl:Hl,setEditorDataSlug:Vl}=u.actions,Yl=s.g.jQuery;window.yoast=window.yoast||{},window.yoast.initEditorIntegration=function(e){(function(e){const s=h(),r=s.isPremium?"Yoast SEO Premium":"Yoast SEO",d=(0,t.createElement)(v,null);(0,a.updateCategory)("yoast-structured-data-blocks",{icon:d}),(0,a.updateCategory)("yoast-internal-linking-blocks",{icon:d});const u={isRtl:s.isRtl},m=e.getState().preferences,g=m.isKeywordAnalysisActive||m.isContentAnalysisActive,y=m.isKeywordAnalysisActive&&m.isWincherIntegrationActive;!function(){var e,t,s;const r="yoast-seo/document-panel";var a,o,i,l;Boolean(null===(e=(0,n.dispatch)("core/editor"))||void 0===e?void 0:e.toggleEditorPanelOpened)?((null===(t=(0,n.select)("core/preferences"))||void 0===t?void 0:t.get("core","openPanels"))||(null===(s=(0,n.select)("core/preferences"))||void 0===s?void 0:s.get("core/edit-post","openPanels"))).includes(r)||null===(a=(0,n.dispatch)("core/editor"))||void 0===a||a.toggleEditorPanelOpened(r):null!==(o=(0,n.select)("core/preferences"))&&void 0!==o&&null!==(i=o.get("core/edit-post","openPanels"))&&void 0!==i&&i.includes(r)||null===(l=(0,n.dispatch)("core/edit-post"))||void 0===l||l.toggleEditorPanelOpened(r)}();const f={locationContext:"block-sidebar"},w={locationContext:"block-metabox"};(0,c.registerPlugin)("yoast-seo",{render:()=>(0,t.createElement)(i.Fragment,null,(0,t.createElement)(o.PluginSidebarMoreMenuItem,{target:"seo-sidebar",icon:(0,t.createElement)(Lo,null)},r),(0,t.createElement)(o.PluginSidebar,{name:"seo-sidebar",title:r},(0,t.createElement)(p.Root,{context:f},(0,t.createElement)(_o,{store:e,theme:u}))),(0,t.createElement)(i.Fragment,null,(0,t.createElement)(pl,{store:e,theme:u}),(0,t.createElement)(p.Root,{context:w},(0,t.createElement)(vo,{target:"wpseo-metabox-root",store:e,theme:u}))),g&&(0,t.createElement)(o.PluginPrePublishPanel,{className:"yoast-seo-sidebar-panel",title:(0,l.__)("Yoast SEO","wordpress-seo"),initialOpen:!0,icon:(0,t.createElement)(i.Fragment,null)},(0,t.createElement)(Uo,null)),(0,t.createElement)(o.PluginPostPublishPanel,{className:"yoast-seo-sidebar-panel",title:(0,l.__)("Yoast SEO","wordpress-seo"),initialOpen:!0,icon:(0,t.createElement)(i.Fragment,null)},(0,t.createElement)(Bo,null),y&&(0,t.createElement)(ml,null)),g&&(0,t.createElement)(o.PluginDocumentSettingPanel,{name:"document-panel",className:"yoast-seo-sidebar-panel",title:(0,l.__)("Yoast SEO","wordpress-seo"),icon:(0,t.createElement)(i.Fragment,null)},(0,t.createElement)(Ao,null))),icon:(0,t.createElement)(Lo,null)})})(e),function(){if("function"==typeof(0,m.get)(window,"wp.blockEditor.__experimentalLinkControl")){const e=(0,n.select)("core/rich-text").getFormatType("core/unknown");void 0!==e&&(0,n.dispatch)("core/rich-text").removeFormatTypes("core/unknown"),[Ol].forEach((({name:e,replaces:t,...s})=>{t&&(0,n.dispatch)("core/rich-text").removeFormatTypes(t),e&&(0,d.registerFormatType)(e,s)})),void 0!==e&&(0,d.registerFormatType)("core/unknown",e)}else console.warn((0,l.__)("Marking links with nofollow/sponsored has been disabled for WordPress installs < 5.4.","wordpress-seo")+" "+(0,l.sprintf)(
// translators: %1$s expands to Yoast SEO.
(0,l.__)("Please upgrade your WordPress version or install the Gutenberg plugin to get this %1$s feature.","wordpress-seo"),"Yoast SEO"))}(),function(e){(0,n.select)("core/block-editor")&&(0,m.isFunction)((0,n.select)("core/block-editor").getBlocks)&&(0,n.select)("core/annotations")&&(0,m.isFunction)((0,n.dispatch)("core/annotations").__experimentalAddAnnotation)&&e.dispatch(u.actions.setMarkerStatus("enabled"))}(e)},window.yoast.EditorData=class{constructor(e,t){this._refresh=e,this._store=t,this._data={},this.getPostAttribute=this.getPostAttribute.bind(this),this.refreshYoastSEO=this.refreshYoastSEO.bind(this)}initialize(e,t=[]){var s,r;this._data=this.getInitialData(e),s=this._data,r=this._store,(0,m.forEach)(s,((e,t)=>{as.includes(t)||r.dispatch(ts(t,e))})),this._store.dispatch(Nl(t)),this.subscribeToGutenberg(),this.subscribeToYoastSEO()}getInitialData(e){const t=this.collectGutenbergData();return e=function(e,t){if(!e.custom_taxonomies)return e;const s={};return(0,m.forEach)(e.custom_taxonomies,((e,t)=>{const{name:r,label:a,descriptionName:n,descriptionLabel:o}=function(e){const t=ns(e);return{name:"ct_"+t,label:ss(e+" (custom taxonomy)"),descriptionName:"ct_desc_"+t,descriptionLabel:ss(e+" description (custom taxonomy)")}}(t),i="string"==typeof e.name?(0,Y.decodeHTML)(e.name):e.name,l="string"==typeof e.description?(0,Y.decodeHTML)(e.description):e.description;s[r]={value:i,label:a},s[n]={value:l,label:o}})),t.dispatch(function(e){return{type:"SNIPPET_EDITOR_UPDATE_REPLACEMENT_VARIABLES_BATCH",updatedVariables:e}}(s)),(0,m.omit)({...e},"custom_taxonomies")}(e=function(e,t){return e.custom_fields?((0,m.forEach)(e.custom_fields,((e,s)=>{const{name:r,label:a}=function(e){return{name:"cf_"+ns(e),label:ss(e+" (custom field)")}}(s);t.dispatch(ts(r,e,a))})),(0,m.omit)({...e},"custom_fields")):e}(e,this._store),this._store),{...e,...t}}setRefresh(e){this._refresh=e}isShallowEqual(e,t){if(Object.keys(e).length!==Object.keys(t).length)return!1;for(const s in e)if(e.hasOwnProperty(s)&&(!(s in t)||e[s]!==t[s]))return!1;return!0}getMediaById(e){return this._coreDataSelect||(this._coreDataSelect=(0,n.select)("core")),this._coreDataSelect.getMedia(e)}getPostAttribute(e){return this._coreEditorSelect||(this._coreEditorSelect=(0,n.select)("core/editor")),this._coreEditorSelect.getEditedPostAttribute(e)}getSlug(){if("auto-draft"===this.getPostAttribute("status"))return"";let e=this.getPostAttribute("generated_slug")||"";"auto-draft"===e&&(e="");const t=this.getPostAttribute("slug")||e;try{return decodeURI(t)}catch(e){return t}}getPostBaseUrl(){const e=(0,n.select)("core/editor").getPermalinkParts();if(null===e||null==e||!e.prefix)return window.wpseoScriptData.metabox.base_url;let t=e.prefix;if((0,n.select)("core/editor").isEditedPostNew())try{const e=new URL(t);t=e.origin+e.pathname}catch(e){}return t.endsWith("/")||(t+="/"),t}collectGutenbergData(){let e=(0,n.select)("core/editor").getEditedPostContent();const t=(0,n.select)("core/block-editor").getBlocks();1===t.length&&"core/freeform"===t[0].name&&(e=(0,a.getBlockContent)(t[0]));const s=this.calculateContentImage(e),r=this.getPostAttribute("excerpt")||"";return{content:e,title:this.getPostAttribute("title")||"",slug:this.getSlug(),excerpt:r||os(e,"ja"===Ml()?80:156),excerpt_only:r,snippetPreviewImageURL:this.getFeaturedImage()||s,contentImage:s,baseUrl:this.getPostBaseUrl()}}getFeaturedImage(){const e=this.getPostAttribute("featured_media");if(e){const t=this.getMediaById(e);if(t)return t.source_url}return null}calculateContentImage(e){const t=I.languageProcessing.imageInText(e);if(0===t.length)return"";const s=Yl.parseHTML(t.join(""));for(const e of s)if(e.src)return e.src;return""}handleEditorChange(e){this._data.content!==e.content&&this._store.dispatch(Ul(e.content)),this._data.title!==e.title&&(this._store.dispatch(Wl(e.title)),this._store.dispatch(Dl("title",e.title))),this._data.excerpt!==e.excerpt&&(this._store.dispatch(Kl(e.excerpt)),this._store.dispatch(Dl("excerpt",e.excerpt)),this._store.dispatch(Dl("excerpt_only",e.excerpt_only))),this._data.slug!==e.slug&&(this._store.dispatch(Vl(e.slug)),this._store.dispatch(ql({slug:e.slug}))),this._data.snippetPreviewImageURL!==e.snippetPreviewImageURL&&(this._store.dispatch(Hl(e.snippetPreviewImageURL)),this._store.dispatch(ql({snippetPreviewImageURL:e.snippetPreviewImageURL}))),this._data.contentImage!==e.contentImage&&this._store.dispatch(Bl(e.contentImage)),this._data.baseUrl!==e.baseUrl&&this._store.dispatch($l({baseUrl:e.baseUrl}))}reapplyMarkers(){const{getActiveMarker:e,getMarkerPauseStatus:t}=(0,n.select)("yoast-seo/editor"),s=e(),r=t();s&&!r&&Cl()}refreshYoastSEO(){const e=this.collectGutenbergData();!this.isShallowEqual(this._data,e)&&(this.handleEditorChange(e),this._data=e,this._refresh())}areNewAnalysisResultsAvailable(){const e=(0,n.select)("yoast-seo/editor"),t=e.getReadabilityResults(),s=e.getResultsForFocusKeyword();return(this._previousReadabilityResults!==t||this._previousSeoResults!==s)&&(this._previousReadabilityResults=t,this._previousSeoResults=s,!0)}onNewAnalysisResultsAvailable(){this.reapplyMarkers()}subscribeToGutenberg(){this.subscriber=(0,m.debounce)(this.refreshYoastSEO,500),(0,n.subscribe)(this.subscriber)}subscribeToYoastSEO(){this.yoastSubscriber=()=>{this.areNewAnalysisResultsAvailable()&&this.onNewAnalysisResultsAvailable()},(0,n.subscribe)(this.yoastSubscriber)}getData(){return this._data}}})()})();