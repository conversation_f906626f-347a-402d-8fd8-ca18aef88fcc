(()=>{"use strict";var i={d:(e,r)=>{for(var a in r)i.o(r,a)&&!i.o(e,a)&&Object.defineProperty(e,a,{enumerable:!0,get:r[a]})},o:(i,e)=>Object.prototype.hasOwnProperty.call(i,e),r:i=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(i,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(i,"__esModule",{value:!0})}},e={};i.r(e),i.d(e,{default:()=>p});const r=window.yoast.analysis,a=["bunlar","şunlar","onlar”, “burası","orası","şurası","burayı","orayı","şurayı”, “buraya","oraya","şuraya”, “burada","orada","şurada”, “buradan","oradan","şuradan","bu","şu","o","bir","íki","üç","dört","beş","altı","yedi","sekiz","dokuz","on"],t=["fakat","halbuki","hatta","üstelik","ancak","oysa","sonuçta","yalnız","çünkü","oysaki","kısacası","özetle","böylelikle","ama","lakin","ayrıca","açıkcası","yani","sonucunda","böylece","kısaca","veya","veyahut","zira","öyleyse","sonrasında","ardından","vakıa","gerçi","karşın","tümüyle","bütünüyle","tamamıyla","genelde","diğer","başka","önce","öncesinde","sonra","yanısıra","ama","muhakkak","kesinlikle","şüphesiz","elbet","elbette","kuşkusuz","başlıca","bilakis","aksine","tersine","devamında","özellikle","bilhassa","nihayet","nihayetinde","neticede","ayrıyeten","dahası","çoğunlukla","genellikle","genelde","dolayısıyla","gelgelelim","aslında","doğrusu","mamafih","binaenaleyh","evvelce","önceden","şöylelikle","örneğin","mesela","nitekim","mademki","şimdi","halihazırda","i̇laveten","aynen","nazaran","nedeniyle","yüzünden","umumiyetle","ekseriye","amacıyla","gayesiyle","velhasıl","ezcümle","özetlersek","etraflıca","tafsilatlı","genişçe","bilfiil","filhakika","evvela","i̇lkin","en önce","birincisi","i̇kincisi","üçüncüsü","sonuncusu","tıpkı","topyekun","hem","ne","kah","ister","ya","gerek","ha"],s=t.concat(["o halde","bundan böyle","demek ki","ne yazık ki","görüldüğü gibi","i̇lk olarak","son olarak","ne var ki","buna rağmen","yine de","başka bir deyişle","açıklamak gerekirse","özetlemek gerekirse","kısaca söylemek gerekirse","görüldüğü gibi","ve bunun gibi","halbu ki","buna göre","ona göre","ek olarak","her ne kadar","velev ki","olmakla beraber","bile olsa","i̇le beaber","i̇le birlikte","her şeye rağmen","bütün yanlarıyla","bütün yönleriyle","ele alacak olursak","baştan sona","diğer bir","başka bir","daha önce","daha sonra","bundan başka","bunun yanında","bunun yanı sıra","bununla birlikte","buna ilaveten","bunun dışında","elbette ki","muhakkak ki","belli başlı","karşılaştırmak gerekirse","karşılaştırmalı olarak","aynı zamanda","sonuç olarak","diğer taraftan","diğer bir taraftan","buna karşılık","tam tersine","buna bağlı olarak","buna parelel olarak","i̇kinci olarak","üçüncü olarak","aynı derecede","eşit olarak","başta olmak üzere","en sonunda","açık bir şekilde","ana hatlarıyla","genel itibariyle","genel anlamda","genel olarak","bunun için","bu nedenle","bundan dolayı","bu sebeple","dolayısı ile","her halükarda","aynı biçimde","aynı şekilde","bu esnada","bu arada","hal böyleyken","bağlı kalmaksızın","açık olarak","belli ki","ayrıntılı olarak","bundan önce","sözün kısası","az ve öz bir şekilde","tüm ayrıntılarıyla","bu şekilde","o yüzden","bu sayede","buradan hareketle","buna mukabil","en önemlisi","her şeyden önce","esas olarak","hepsinden önce","hepsinden öte","hepsinden ötesi","her şeyin üzerinde","her şeyin ötesinde","hepsinden önemlisi","asıl önemlisi","her şeyi hesaba katarak","bütün olarak","her şey göz önüne alındığında","pararel olarak","diğer bir nokta","diğer açıdan","öyle ya da böyle","doğrusunu söylemek gerekirse","i̇şin doğrusu","aslına bakılırsa","gerçek şu ki","hattı zatında","aslına bakıldığında","aslına bakarsak","i̇şin aslı","sonuç itibariyle","örnek olarak","örneleyecek olursak","görülebileceği gibi","görülebileceği üzere","görüldüğü üzere","söylendiği gibi","söylenildiği gibi","söylediği gibi","söylediğim gibi","olduğu kadar","önceden belirtildiği gibi","önceden söylendiği gibi","yukarıda gösterildiği gibi","eninde sonunda","önünde sonunda","şu anda","bu sırada","bununla beraber","bu noktada","bunun ışığında","bunların ışığında","aşikar olarak","aynı sebeple","bir de","doğru da olsa","doğru bile olsa","öyle bile olsa","öyle de olsa","i̇le ilgili","olsa bile","eğer ki","olsa dahi","ondan dolayı","o sebepten dolayı","bu yüzden","onun için","esas itibarıyla","aynı sebepten dolayı","bu amaçla","zaman zaman","arada sırada","dönem dönem","arada bir","diyelim ki","farz edelim ki","farz edersek","kısaca söylecek olursak","tek kelimeyle","birkaç kelimeyle","sözün özü","en nihayetinde","uzun uzadıya","her iki durumda da","özü itibariyle","amacı ile","olması için","başka bir ifadeyle","diğer bir deyişle","i̇lk önce","bir yandan","bir taraftan","hatırlatmak gerekirse","bu bağlamda","gel gelelim","her şey hesaba katılırsa","bütüne bakıldığında","belirtildiği gibi","bir başka ifadeyle","lafı toparlamak gerekirse","bu düşünceyle","bu maksatla","bu doğrultuda","bu niyetle","hem de","ne de","ya da","gerekse de"]),n=function(i){let e=i;return i.forEach((r=>{(r=r.split("-")).length>0&&r.filter((e=>!i.includes(e))).length>0&&(e=e.concat(r))})),e}([].concat(["bir"],["i̇ki","üç","dört","beş","altı","yedi","sekiz","dokuz","on","on bir","on i̇ki","on üç","on dört","on beş","on altı","on yedi","on sekiz","on dokuz","yirmi","yirmi bir","yirmi i̇ki","yirmi üç","yirmi dört","yirmi beş","yirmi altı","yirmi yedi","yirmi sekiz","yirmi dokuz","otuz","kırk","elli","altmış","yetmiş","seksen","doksan","yüz","bin","milyon","milyar"],["birinci","i̇kinci","üçüncü","dördüncü","beşinci","altıncı","yedinci","sekizinci","dokuzuncu","onuncu"],["tam","yarım","çeyrek","üçte biri","üçte ikisi","tamamı","yarısı","çeyreği","üçte biri","üçte ikisi"],["ben","sen","o","biz","siz","onlar","beni","seni","onu","bizi","sizi","onları","bizleri","sizleri","bana","sana","ona","bize","size","onlara","bizlere","sizlere","bende","sende","onda","bizde","sizde","onlarda","bizlerde","sizlerde","benden","senden","ondan","bizden","sizden","onlardan","bizlerden","sizlerden","benim","senin","onun","bizim","sizin","onların","bizlerin","sizlerin","bu","şu","o","öteki","beriki","bura","şura","ora","burası","şurası","orası","böylesi","şöylesi","öylesi","bunlar","şunlar","onlar","ötekiler","berikiler","buralar","şuralar","oralar","birbiri","birbirimiz","birbiriniz","birbirleri","birbirimizi","birbirinizi","birbirlerini","birbirimize","birbirinize","birbirlerine","birbirimizde","birbirinizde","birbirlerinde","birbirimizden","birbirinizden","birbirlerinden","birbirimizle","birbirinizle","birbirleriyle"],["kim","kimi","kime","kimde","kimden","kimin","kiminle","ne","neyi","neyde","neyden","neyle","ne için","niçin","niye","ne diye","nere","nereyi","nereye","nerede","nereden","neresi","neden","hangi","hangisi","kaç","kaçı","kaçıncı","kaçta","nasıl","ne kadar","ne zaman","mı","hangi","hangisi","kimler","kimleri","kimlere","kimlerde","kimlerden","neler","neleri","nelere","nelerde","nelerden","hangiler","hangileri","hangilere"],["hepsi","bazısı","çoğu","birçoğu","birazı","hepsi","bütünü","yeteri kadarı","birkaçı","biri","her ikisi","i̇kisinden biri","hiç biri","diğeri","tümü","bir kısmı","pek çoğu","her biri","bazı","çok","çoğu","birçok","biraz","bütün","yeteri kadar","birkaç","bir","her iki","hiç bir","diğer","tüm","bir kısım","pek çok","her bir"],["kendi","kendim","kendimi","kendime","kendimde","kendimden","kendin","kendini","kendine","kendinde","kendinden","kendisi","kendiyle","kendileri","kendilerine","kendilerinde","kendilerinden","kendileriyle"],["kimi","kimse","biri","birisi","başkası","bazısı","bir çoğu","bir takımı","birkaçı","birazı","herkes","hepsi","hepimiz","hiçbiri","herhangi biri","her biri","şey","falan","filan","falanca","öteberi","tümü","bütünü","kimileri","kimseler","birileri","başkaları","bazıları","bir çokları","herkesler"],["i̇çin","gibi","kadar","ancak","yalnız","i̇le","sadece","sanki","değil","üzere","dair","karşın","rağmen","özgü","doğru","dek","değin","ait","beri","başka","itibaren","dolayı","ötürü","adeta","sırf","diye","tek","karşı"],["ve","i̇le","veya","ya da","yahut","veyahut","ama","fakat","lakin","yalnız","ancak","oysa","oysaki","halbu ki","ne var ki","çünkü","zira","de","da","ki","meğer","madem","mademki","demek","demek ki","üstelik","hatta","yani","hem...hem","hem de","ne","kah","i̇ster","ister","açıkcası","bile","ya","da","ise","öyleyse","kim bilir","gerek","gerekse de","ta ki","zati"],["demek","dedim","dedin","dedi","dedik","dediniz","dediler","der","söylemek","söyledim","söyledin","söyledi","söyledik","söylediniz","söylediler","söyler","söylerler","sormak","sordum","sordun","sordu","sorduk","sordunuz","sordular","sorar","sorarlar","belirtmek","belirttim","belirttin","belirtti","belirttik","belirttiniz","belirttiler","belirtir","belirtirler","açıklamak","açıkladım","açıkladın","açıkladı","açıkladık","açıkladınız","açıkladılar","açıklar","açıklarlar","düşünmek","düşündüm","düşündün","düşündü","düşündük","düşündünüz","düşündüler","düşünür","düşünürler","konuşmak","konuşdum","konuştun","konuştu","konuştuk","konuştunuz","konuştular","konuşur","konuşurlar","bildirmek","bildirdim","bildirdin","bildirdi","bildirdik","bildirdiniz","bildirdiler","birdirir","bildirirler","ele","almak","aldım","aldın","aldı","aldık","aldınız","aldılar","önermek","önerdim","önerdin","önerdi","önerdik","önerdiniz","önerdiler","önerir","önerirler","anlamak","anladım","anladın","anladı","anladık","anladınız","anladılar","anlar","anlarlar"],["en","daha","pek çok","en çok","fazla","epey","epeyce","bayağı","oldukça","pek","gayet","fazlaca","fevkalede","tamamen","fena halde","fena şekilde","gerçekten","zerre kadar","biraz","son derece","deli gibi","en","çok","azıcık"],["etmek","olmak","yapmak","kalmak","gelmek","kalmak","bulunmak","demek","dilemek","söylemek","durmak","eylemek","yazmak","durmak","vermek","kabul","teşekkür","memnun","seyir","zan","bilmek"],["yeni","eski","önceki","i̇yi","büyük","küçük","kolay","zor","hızlı","yavaş","yüksek","alçak","kısa","uzun","i̇nce","kalın","gerçek","yalan","yanlış","basit","zor","aynı","farklı","belli","belirsiz","modern","geleneksel","muhtemel","yaygın","genç","yaşlı","zamansız","acı","tatlı","tuzlu","sıcak","soğuk","kalabalık","sakin","yalnız","dar","geniş","siyah","beyaz","mavi","kırmızı","sarı","temiz","kirli","muhteşem","nazik","kibar","akıllı","zeki","gizli","açık","kapalı","dikkatli","gürültülü","sevinçli","eski","önce","i̇yi","büyük","küçük","kolay","zor","hızlı","yavaş","yüksek","alçak","kısa","uzun","i̇nce","kalın","gerçek","yanlış","basit","zor","aynı","farklı","belli","belirsiz","modern","geleneksel","muhtemel","yaygın","nadir","genç","yaşlı","zamansız","acı","tatlı","tuzlu","sıcak","soğuk","kalabalık","sakin","yalnız","dar","geniş","siyah","beyaz","mavi","kırmızı","sarı","temiz","kirli","muhteşem","nazik","kibar","akıllı","zeki","gizli","açık","kapalı","dikkatle","gürültülü","uzun","sevinçle","aşağı","yukarı","sağa","sola","i̇çeri dışarı","bugün","yarın","haftaya","seneye","ne zaman","nereye","neden","niye","ne kadar","nasıl","ne"],["ey","hey","bre","hişt","şşt","ah","ahh","ee","vay","i̇mdat","hah","ay","aa","aaa","hay allah","aman","aman dikkat","vah","ya","yaa","ooo","of","tüh","peh","aman","haydi","sakın","yuh"],["çay kaşığı","çay k.","yemek kaşığı","yemek k.","tatlı kaşığı","tatlı k.","çay bardağı","çay b.","su bardağı","su b.","kahve fincanı","kahve f.","tepeleme","tepeleme kaşık","tepeleme bardak","gr","ml","kg","mg","cl","oz","çeyrek","tam","yarım","üçte biri","üçte ikisi","parmak"],["saniye","saniyeler","dakika","dakikalar","saat","saatler","gün","günler","hafta","haftalar","ay","aylar","yıl","yıllar","bugün","yarın","dün","sabah","öğlen","akşam","gece","gündüz"],["şey","şeyler","olasılık","çeşit","kişi"],["hapşu","hapşırık","hapşurmak","horr","horultu","horlamak","şırıl","şırıltı","şırıldamak","hışır","hışırtı","hışırdamak","gıcır","gıcırtı","gıcırdamak","çatır","çatırtı","çatırdamak","pat","patlamak","vın","vınlamak","zırr","zırıltı","zırlamak","tık","tıkırtı","tıkırdamak","çıt","çıtırtı","çıtırdamak","fokur","fokurtu","fokurdamak","kıt","kıtırtı","kıtırdamak","patırtı"],["bayan","bay","hanımefendi","hanfendi","hanım","beyefendi","beyfendi","bey","sayın","profesör","prof.","doktor","dr."],t)),h=[["hem","hem de"],["ne","ne de"],["ya","ya da"],["gerek","gerekse de"]],o={recommendedLength:15,percentages:{slightlyTooMany:20,farTooMany:25}},l=window.lodash;class u{get b(){return Object.prototype.hasOwnProperty.call(this,"_$esjava$b")?this._$esjava$b:this._$esjava$b=""}set b(i){this._$esjava$b=i}length$esjava$0(){return this.b.length}replace$esjava$3(i,e,r){if(0===i&&e===this.b.length)this.b=r;else{const a=this.b.substring(0,i),t=this.b.substring(e);this.b=a+r+t}}substring$esjava$2(i,e){return this.b.substring(i,e)}charAt$esjava$1(i){return this.b.charCodeAt(i)}subSequence$esjava$2(i,e){throw new Error("NotImpl: CharSequence::subSequence")}toString$esjava$0(){return this.b}length(...i){return 0===i.length?this.length$esjava$0(...i):super.length(...i)}replace(...i){return 3===i.length?this.replace$esjava$3(...i):super.replace(...i)}substring(...i){return 2===i.length?this.substring$esjava$2(...i):super.substring(...i)}charAt(...i){return 1===i.length?this.charAt$esjava$1(...i):super.charAt(...i)}subSequence(...i){return 2===i.length?this.subSequence$esjava$2(...i):super.subSequence(...i)}toString(...i){return 0===i.length?this.toString$esjava$0(...i):super.toString(...i)}}class m{static toCharArray$esjava$1(i){const e=i.length,r=new Array(e);for(let a=0;a<e;a++)r[a]=i.charCodeAt(a);return r}constructor(i,e,r,a,t){this.s_size=i.length,this.s=m.toCharArray$esjava$1(i),this.substring_i=e,this.result=r,this.methodobject=t,this.method=a?t[a]:null}get s_size(){return Object.prototype.hasOwnProperty.call(this,"_$esjava$s_size")?this._$esjava$s_size:this._$esjava$s_size=0}set s_size(i){this._$esjava$s_size=i}get s(){return Object.prototype.hasOwnProperty.call(this,"_$esjava$s")?this._$esjava$s:this._$esjava$s=null}set s(i){this._$esjava$s=i}get substring_i(){return Object.prototype.hasOwnProperty.call(this,"_$esjava$substring_i")?this._$esjava$substring_i:this._$esjava$substring_i=0}set substring_i(i){this._$esjava$substring_i=i}get result(){return Object.prototype.hasOwnProperty.call(this,"_$esjava$result")?this._$esjava$result:this._$esjava$result=0}set result(i){this._$esjava$result=i}get method(){return Object.prototype.hasOwnProperty.call(this,"_$esjava$method")?this._$esjava$method:this._$esjava$method=null}set method(i){this._$esjava$method=i}get methodobject(){return Object.prototype.hasOwnProperty.call(this,"_$esjava$methodobject")?this._$esjava$methodobject:this._$esjava$methodobject=null}set methodobject(i){this._$esjava$methodobject=i}}class _{constructor(){this.current=new u,this.setCurrent$esjava$1("")}setCurrent$esjava$1(i){this.current.replace(0,this.current.length(),i),this.cursor=0,this.limit=this.current.length(),this.limit_backward=0,this.bra=this.cursor,this.ket=this.limit}getCurrent$esjava$0(){const i=this.current.toString();return this.current=new u,i}get current(){return Object.prototype.hasOwnProperty.call(this,"_$esjava$current")?this._$esjava$current:this._$esjava$current=null}set current(i){this._$esjava$current=i}get cursor(){return Object.prototype.hasOwnProperty.call(this,"_$esjava$cursor")?this._$esjava$cursor:this._$esjava$cursor=0}set cursor(i){this._$esjava$cursor=i}get limit(){return Object.prototype.hasOwnProperty.call(this,"_$esjava$limit")?this._$esjava$limit:this._$esjava$limit=0}set limit(i){this._$esjava$limit=i}get limit_backward(){return Object.prototype.hasOwnProperty.call(this,"_$esjava$limit_backward")?this._$esjava$limit_backward:this._$esjava$limit_backward=0}set limit_backward(i){this._$esjava$limit_backward=i}get bra(){return Object.prototype.hasOwnProperty.call(this,"_$esjava$bra")?this._$esjava$bra:this._$esjava$bra=0}set bra(i){this._$esjava$bra=i}get ket(){return Object.prototype.hasOwnProperty.call(this,"_$esjava$ket")?this._$esjava$ket:this._$esjava$ket=0}set ket(i){this._$esjava$ket=i}copy_from$esjava$1(i){this.current=i.current,this.cursor=i.cursor,this.limit=i.limit,this.limit_backward=i.limit_backward,this.bra=i.bra,this.ket=i.ket}in_grouping$esjava$3(i,e,r){if(this.cursor>=this.limit)return!1;let a=this.current.charAt(this.cursor);return!(a>r||a<e||(a-=e,0==(i[a>>3]&1<<(7&a))||(this.cursor++,0)))}in_grouping_b$esjava$3(i,e,r){if(this.cursor<=this.limit_backward)return!1;let a=this.current.charAt(this.cursor-1);return!(a>r||a<e||(a-=e,0==(i[a>>3]&1<<(7&a))||(this.cursor--,0)))}out_grouping$esjava$3(i,e,r){if(this.cursor>=this.limit)return!1;let a=this.current.charAt(this.cursor);return a>r||a<e?(this.cursor++,!0):(a-=e,0==(i[a>>3]&1<<(7&a))&&(this.cursor++,!0))}out_grouping_b$esjava$3(i,e,r){if(this.cursor<=this.limit_backward)return!1;let a=this.current.charAt(this.cursor-1);return a>r||a<e?(this.cursor--,!0):(a-=e,0==(i[a>>3]&1<<(7&a))&&(this.cursor--,!0))}in_range$esjava$2(i,e){if(this.cursor>=this.limit)return!1;const r=this.current.charAt(this.cursor);return!(r>e||r<i||(this.cursor++,0))}in_range_b$esjava$2(i,e){if(this.cursor<=this.limit_backward)return!1;const r=this.current.charAt(this.cursor-1);return!(r>e||r<i||(this.cursor--,0))}out_range$esjava$2(i,e){if(this.cursor>=this.limit)return!1;const r=this.current.charAt(this.cursor);return(r>e||r<i)&&(this.cursor++,!0)}out_range_b$esjava$2(i,e){if(this.cursor<=this.limit_backward)return!1;const r=this.current.charAt(this.cursor-1);return(r>e||r<i)&&(this.cursor--,!0)}eq_s$esjava$2(i,e){if(this.limit-this.cursor<i)return!1;let r;for(r=0;r!==i;r++)if(this.current.charAt(this.cursor+r)!==e.charCodeAt(r))return!1;return this.cursor+=i,!0}eq_s_b$esjava$2(i,e){if(this.cursor-this.limit_backward<i)return!1;let r;for(r=0;r!==i;r++)if(this.current.charAt(this.cursor-i+r)!==e.charCodeAt(r))return!1;return this.cursor-=i,!0}eq_v$esjava$1(i){return this.eq_s$esjava$2(i.length(),i.toString())}eq_v_b$esjava$1(i){return this.eq_s_b$esjava$2(i.length(),i.toString())}find_among$esjava$2(i,e){let r=0,a=e;const t=this.cursor,s=this.limit;let n=0,h=0,o=!1;for(;;){const e=r+(a-r>>1);let l=0,u=n<h?n:h;const m=i[e];let _;for(_=u;_<m.s_size;_++){if(t+u===s){l=-1;break}if(l=this.current.charAt(t+u)-m.s[_],0!==l)break;u++}if(l<0?(a=e,h=u):(r=e,n=u),a-r<=1){if(r>0)break;if(a===r)break;if(o)break;o=!0}}for(;;){const e=i[r];if(n>=e.s_size){if(this.cursor=t+e.s_size,null===e.method)return e.result;let i;if(i=e.method.call(e.methodobject),this.cursor=t+e.s_size,i)return e.result}if(r=e.substring_i,r<0)return 0}}find_among_b$esjava$2(i,e){let r=0,a=e;const t=this.cursor,s=this.limit_backward;let n=0,h=0,o=!1;for(;;){const e=r+(a-r>>1);let l=0,u=n<h?n:h;const m=i[e];let _;for(_=m.s_size-1-u;_>=0;_--){if(t-u===s){l=-1;break}if(l=this.current.charAt(t-1-u)-m.s[_],0!==l)break;u++}if(l<0?(a=e,h=u):(r=e,n=u),a-r<=1){if(r>0)break;if(a===r)break;if(o)break;o=!0}}for(;;){const e=i[r];if(n>=e.s_size){if(this.cursor=t-e.s_size,null===e.method)return e.result;let i;if(i=e.method.call(e.methodobject),this.cursor=t-e.s_size,i)return e.result}if(r=e.substring_i,r<0)return 0}}replace_s$esjava$3(i,e,r){const a=r.length-(e-i);return this.current.replace(i,e,r),this.limit+=a,this.cursor>=e?this.cursor+=a:this.cursor>i&&(this.cursor=i),a}slice_check$esjava$0(){if(this.bra<0||this.bra>this.ket||this.ket>this.limit||this.limit>this.current.length())throw new Error("Snowball: faulty slice operation")}slice_from$esjava$1(i){this.slice_check$esjava$0(),this.replace_s$esjava$3(this.bra,this.ket,i)}slice_del$esjava$0(){this.slice_from$esjava$1("")}insert$esjava$3(i,e,r){const a=this.replace_s$esjava$3(i,e,r);i<=this.bra&&(this.bra+=a),i<=this.ket&&(this.ket+=a)}slice_to$esjava$1(i){return this.slice_check$esjava$0(),i.replace(0,i.length(),this.current.substring(this.bra,this.ket)),i}setCurrent(...i){return 1===i.length?this.setCurrent$esjava$1(...i):super.setCurrent(...i)}getCurrent(...i){return 0===i.length?this.getCurrent$esjava$0(...i):super.getCurrent(...i)}copy_from(...i){return 1===i.length?this.copy_from$esjava$1(...i):super.copy_from(...i)}in_grouping(...i){return 3===i.length?this.in_grouping$esjava$3(...i):super.in_grouping(...i)}in_grouping_b(...i){return 3===i.length?this.in_grouping_b$esjava$3(...i):super.in_grouping_b(...i)}out_grouping(...i){return 3===i.length?this.out_grouping$esjava$3(...i):super.out_grouping(...i)}out_grouping_b(...i){return 3===i.length?this.out_grouping_b$esjava$3(...i):super.out_grouping_b(...i)}in_range(...i){return 2===i.length?this.in_range$esjava$2(...i):super.in_range(...i)}in_range_b(...i){return 2===i.length?this.in_range_b$esjava$2(...i):super.in_range_b(...i)}out_range(...i){return 2===i.length?this.out_range$esjava$2(...i):super.out_range(...i)}out_range_b(...i){return 2===i.length?this.out_range_b$esjava$2(...i):super.out_range_b(...i)}eq_s(...i){return 2===i.length?this.eq_s$esjava$2(...i):super.eq_s(...i)}eq_s_b(...i){return 2===i.length?this.eq_s_b$esjava$2(...i):super.eq_s_b(...i)}eq_v(...i){return 1===i.length?this.eq_v$esjava$1(...i):super.eq_v(...i)}eq_v_b(...i){return 1===i.length?this.eq_v_b$esjava$1(...i):super.eq_v_b(...i)}find_among(...i){return 2===i.length?this.find_among$esjava$2(...i):super.find_among(...i)}find_among_b(...i){return 2===i.length?this.find_among_b$esjava$2(...i):super.find_among_b(...i)}replace_s(...i){return 3===i.length?this.replace_s$esjava$3(...i):super.replace_s(...i)}slice_check(...i){return 0===i.length?this.slice_check$esjava$0(...i):super.slice_check(...i)}slice_from(...i){return 1===i.length?this.slice_from$esjava$1(...i):super.slice_from(...i)}slice_del(...i){return 0===i.length?this.slice_del$esjava$0(...i):super.slice_del(...i)}insert(...i){return 3===i.length?this.insert$esjava$3(...i):super.insert(...i)}slice_to(...i){return 1===i.length?this.slice_to$esjava$1(...i):super.slice_to(...i)}}class k extends _{stem$esjava$0(){throw"NotImpl < stem$esjava$0 >"}stem(...i){return 0===i.length?this.stem$esjava$0(...i):super.stem(...i)}}class c extends k{constructor(i){super(),c.morphologyData=i.externalStemmer}static get methodObject(){return delete c.methodObject,c.methodObject=null}static get a_0(){return delete c.a_0,c.a_0=[new m(c.morphologyData.a_0.SuffixM,-1,-1,"",c.methodObject),new m(c.morphologyData.a_0.SuffixN,-1,-1,"",c.methodObject),new m(c.morphologyData.a_0.SuffixMiz,-1,-1,"",c.methodObject),new m(c.morphologyData.a_0.SuffixNiz,-1,-1,"",c.methodObject),new m(c.morphologyData.a_0.SuffixMuz,-1,-1,"",c.methodObject),new m(c.morphologyData.a_0.SuffixNuz,-1,-1,"",c.methodObject),new m(c.morphologyData.a_0.SuffixMuzDieresis,-1,-1,"",c.methodObject),new m(c.morphologyData.a_0.SuffixNuzDieresis,-1,-1,"",c.methodObject),new m(c.morphologyData.a_0.SuffixMizUndotted,-1,-1,"",c.methodObject),new m(c.morphologyData.a_0.SuffixNizUndotted,-1,-1,"",c.methodObject)]}static get a_1(){return delete c.a_1,c.a_1=[new m(c.morphologyData.a_1.SuffixLeri,-1,-1,"",c.methodObject),new m(c.morphologyData.a_1.SuffixLariUndotted,-1,-1,"",c.methodObject)]}static get a_2(){return delete c.a_2,c.a_2=[new m(c.morphologyData.a_2.SuffixNi,-1,-1,"",c.methodObject),new m(c.morphologyData.a_2.SuffixNu,-1,-1,"",c.methodObject),new m(c.morphologyData.a_2.SuffixNuDieresis,-1,-1,"",c.methodObject),new m(c.morphologyData.a_2.SuffixNiUndotted,-1,-1,"",c.methodObject)]}static get a_3(){return delete c.a_3,c.a_3=[new m(c.morphologyData.a_3.SuffixInDotted,-1,-1,"",c.methodObject),new m(c.morphologyData.a_3.SuffixUn,-1,-1,"",c.methodObject),new m(c.morphologyData.a_3.SuffixUnDieresis,-1,-1,"",c.methodObject),new m(c.morphologyData.a_3.SuffixInUndotted,-1,-1,"",c.methodObject)]}static get a_4(){return delete c.a_4,c.a_4=[new m(c.morphologyData.a_4.SuffixA,-1,-1,"",c.methodObject),new m(c.morphologyData.a_4.SuffixE,-1,-1,"",c.methodObject)]}static get a_5(){return delete c.a_5,c.a_5=[new m(c.morphologyData.a_5.SuffixNa,-1,-1,"",c.methodObject),new m(c.morphologyData.a_5.SuffixNe,-1,-1,"",c.methodObject)]}static get a_6(){return delete c.a_6,c.a_6=[new m(c.morphologyData.a_6.SuffixDa,-1,-1,"",c.methodObject),new m(c.morphologyData.a_6.SuffixTa,-1,-1,"",c.methodObject),new m(c.morphologyData.a_6.SuffixDe,-1,-1,"",c.methodObject),new m(c.morphologyData.a_6.SuffixTe,-1,-1,"",c.methodObject)]}static get a_7(){return delete c.a_7,c.a_7=[new m(c.morphologyData.a_7.SuffixNda,-1,-1,"",c.methodObject),new m(c.morphologyData.a_7.SuffixNde,-1,-1,"",c.methodObject)]}static get a_8(){return delete c.a_8,c.a_8=[new m(c.morphologyData.a_8.SuffixDan,-1,-1,"",c.methodObject),new m(c.morphologyData.a_8.SuffixTan,-1,-1,"",c.methodObject),new m(c.morphologyData.a_8.SuffixDen,-1,-1,"",c.methodObject),new m(c.morphologyData.a_8.SuffixTen,-1,-1,"",c.methodObject)]}static get a_9(){return delete c.a_9,c.a_9=[new m(c.morphologyData.a_9.SuffixNdan,-1,-1,"",c.methodObject),new m(c.morphologyData.a_9.SuffixNden,-1,-1,"",c.methodObject)]}static get a_10(){return delete c.a_10,c.a_10=[new m(c.morphologyData.a_10.SuffixLa,-1,-1,"",c.methodObject),new m(c.morphologyData.a_10.SuffixLe,-1,-1,"",c.methodObject)]}static get a_11(){return delete c.a_11,c.a_11=[new m(c.morphologyData.a_11.SuffixCa,-1,-1,"",c.methodObject),new m(c.morphologyData.a_11.SuffixCe,-1,-1,"",c.methodObject)]}static get a_12(){return delete c.a_12,c.a_12=[new m(c.morphologyData.a_12.SuffixImDotted,-1,-1,"",c.methodObject),new m(c.morphologyData.a_12.SuffixUm,-1,-1,"",c.methodObject),new m(c.morphologyData.a_12.SuffixUmDieresis,-1,-1,"",c.methodObject),new m(c.morphologyData.a_12.SuffixImUndotted,-1,-1,"",c.methodObject)]}static get a_13(){return delete c.a_13,c.a_13=[new m(c.morphologyData.a_13.SuffixSin,-1,-1,"",c.methodObject),new m(c.morphologyData.a_13.SuffixSun,-1,-1,"",c.methodObject),new m(c.morphologyData.a_13.SuffixSunDieresis,-1,-1,"",c.methodObject),new m(c.morphologyData.a_13.SuffixSinUndotted,-1,-1,"",c.methodObject)]}static get a_14(){return delete c.a_14,c.a_14=[new m(c.morphologyData.a_14.SuffixIzDotted,-1,-1,"",c.methodObject),new m(c.morphologyData.a_14.SuffixUz,-1,-1,"",c.methodObject),new m(c.morphologyData.a_14.SuffixUzDieresis,-1,-1,"",c.methodObject),new m(c.morphologyData.a_14.SuffixIzUndotted,-1,-1,"",c.methodObject)]}static get a_15(){return delete c.a_15,c.a_15=[new m(c.morphologyData.a_15.SuffixSiniz,-1,-1,"",c.methodObject),new m(c.morphologyData.a_15.SuffixSunuz,-1,-1,"",c.methodObject),new m(c.morphologyData.a_15.SuffixSunuzDieresis,-1,-1,"",c.methodObject),new m(c.morphologyData.a_15.SuffixSinizUndotted,-1,-1,"",c.methodObject)]}static get a_16(){return delete c.a_16,c.a_16=[new m(c.morphologyData.a_16.SuffixLar,-1,-1,"",c.methodObject),new m(c.morphologyData.a_16.SuffixLer,-1,-1,"",c.methodObject)]}static get a_17(){return delete c.a_17,c.a_17=[new m(c.morphologyData.a_17.SuffixNiz,-1,-1,"",c.methodObject),new m(c.morphologyData.a_17.SuffixNuz,-1,-1,"",c.methodObject),new m(c.morphologyData.a_17.SuffixNuzDieresis,-1,-1,"",c.methodObject),new m(c.morphologyData.a_17.SuffixNizUndotted,-1,-1,"",c.methodObject)]}static get a_18(){return delete c.a_18,c.a_18=[new m(c.morphologyData.a_18.SuffixDir,-1,-1,"",c.methodObject),new m(c.morphologyData.a_18.SuffixTir,-1,-1,"",c.methodObject),new m(c.morphologyData.a_18.SuffixDur,-1,-1,"",c.methodObject),new m(c.morphologyData.a_18.SuffixTur,-1,-1,"",c.methodObject),new m(c.morphologyData.a_18.SuffixDurDieresis,-1,-1,"",c.methodObject),new m(c.morphologyData.a_18.SuffixTurDieresis,-1,-1,"",c.methodObject),new m(c.morphologyData.a_18.SuffixDirUndotted,-1,-1,"",c.methodObject),new m(c.morphologyData.a_18.SuffixTirUndotted,-1,-1,"",c.methodObject)]}static get a_19(){return delete c.a_19,c.a_19=[new m(c.morphologyData.a_19.SuffixCasinaUndotted,-1,-1,"",c.methodObject),new m(c.morphologyData.a_19.SuffixCesine,-1,-1,"",c.methodObject)]}static get a_20(){return delete c.a_20,c.a_20=[new m(c.morphologyData.a_20.SuffixDi,-1,-1,"",c.methodObject),new m(c.morphologyData.a_20.SuffixTi,-1,-1,"",c.methodObject),new m(c.morphologyData.a_20.SuffixDik,-1,-1,"",c.methodObject),new m(c.morphologyData.a_20.SuffixTik,-1,-1,"",c.methodObject),new m(c.morphologyData.a_20.SuffixDuk,-1,-1,"",c.methodObject),new m(c.morphologyData.a_20.SuffixTuk,-1,-1,"",c.methodObject),new m(c.morphologyData.a_20.SuffixDukDieresis,-1,-1,"",c.methodObject),new m(c.morphologyData.a_20.SuffixTukDieresis,-1,-1,"",c.methodObject),new m(c.morphologyData.a_20.SuffixDikUndotted,-1,-1,"",c.methodObject),new m(c.morphologyData.a_20.SuffixTikUndotted,-1,-1,"",c.methodObject),new m(c.morphologyData.a_20.SuffixDim,-1,-1,"",c.methodObject),new m(c.morphologyData.a_20.SuffixTim,-1,-1,"",c.methodObject),new m(c.morphologyData.a_20.SuffixDum,-1,-1,"",c.methodObject),new m(c.morphologyData.a_20.SuffixTum,-1,-1,"",c.methodObject),new m(c.morphologyData.a_20.SuffixDumDieresis,-1,-1,"",c.methodObject),new m(c.morphologyData.a_20.SuffixTumDieresis,-1,-1,"",c.methodObject),new m(c.morphologyData.a_20.SuffixDimUndotted,-1,-1,"",c.methodObject),new m(c.morphologyData.a_20.SuffixTimUndotted,-1,-1,"",c.methodObject),new m(c.morphologyData.a_20.SuffixDin,-1,-1,"",c.methodObject),new m(c.morphologyData.a_20.SuffixTin,-1,-1,"",c.methodObject),new m(c.morphologyData.a_20.SuffixDun,-1,-1,"",c.methodObject),new m(c.morphologyData.a_20.SuffixTun,-1,-1,"",c.methodObject),new m(c.morphologyData.a_20.SuffixDunDieresis,-1,-1,"",c.methodObject),new m(c.morphologyData.a_20.SuffixTunDieresis,-1,-1,"",c.methodObject),new m(c.morphologyData.a_20.SuffixDinUndotted,-1,-1,"",c.methodObject),new m(c.morphologyData.a_20.SuffixTinUndotted,-1,-1,"",c.methodObject),new m(c.morphologyData.a_20.SuffixDu,-1,-1,"",c.methodObject),new m(c.morphologyData.a_20.SuffixTu,-1,-1,"",c.methodObject),new m(c.morphologyData.a_20.SuffixDuDieresis,-1,-1,"",c.methodObject),new m(c.morphologyData.a_20.SuffixTuDieresis,-1,-1,"",c.methodObject),new m(c.morphologyData.a_20.SuffixDiUndotted,-1,-1,"",c.methodObject),new m(c.morphologyData.a_20.SuffixTiUndotted,-1,-1,"",c.methodObject)]}static get a_21(){return delete c.a_21,c.a_21=[new m(c.morphologyData.a_21.SuffixSa,-1,-1,"",c.methodObject),new m(c.morphologyData.a_21.SuffixSe,-1,-1,"",c.methodObject),new m(c.morphologyData.a_21.SuffixSak,-1,-1,"",c.methodObject),new m(c.morphologyData.a_21.SuffixSek,-1,-1,"",c.methodObject),new m(c.morphologyData.a_21.SuffixSam,-1,-1,"",c.methodObject),new m(c.morphologyData.a_21.SuffixSem,-1,-1,"",c.methodObject),new m(c.morphologyData.a_21.SuffixSan,-1,-1,"",c.methodObject),new m(c.morphologyData.a_21.SuffixSen,-1,-1,"",c.methodObject)]}static get a_22(){return delete c.a_22,c.a_22=[new m(c.morphologyData.a_22.SuffixMisCedilla,-1,-1,"",c.methodObject),new m(c.morphologyData.a_22.SuffixMusCedilla,-1,-1,"",c.methodObject),new m(c.morphologyData.a_22.SuffixMusDieresisCedilla,-1,-1,"",c.methodObject),new m(c.morphologyData.a_22.SuffixMisUndottedCedilla,-1,-1,"",c.methodObject)]}static get a_23(){return delete c.a_23,c.a_23=[new m(c.morphologyData.a_23.SuffixB,-1,1,"",c.methodObject),new m(c.morphologyData.a_23.SuffixC,-1,2,"",c.methodObject),new m(c.morphologyData.a_23.SuffixD,-1,3,"",c.methodObject),new m(c.morphologyData.a_23.SuffixGSoft,-1,4,"",c.methodObject)]}static get g_vowel(){return delete c.g_vowel,c.g_vowel=[17,65,16,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,32,8,0,0,0,0,0,0,1]}static get g_U(){return delete c.g_U,c.g_U=[1,16,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,8,0,0,0,0,0,0,1]}static get g_vowel1(){return delete c.g_vowel1,c.g_vowel1=[1,64,16,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1]}static get g_vowel2(){return delete c.g_vowel2,c.g_vowel2=[17,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,130]}static get g_vowel3(){return delete c.g_vowel3,c.g_vowel3=[1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1]}static get g_vowel4(){return delete c.g_vowel4,c.g_vowel4=[17]}static get g_vowel5(){return delete c.g_vowel5,c.g_vowel5=[65]}static get g_vowel6(){return delete c.g_vowel6,c.g_vowel6=[65]}get B_continue_stemming_noun_suffixes(){return Object.prototype.hasOwnProperty.call(this,"_$esjava$B_continue_stemming_noun_suffixes")?this._$esjava$B_continue_stemming_noun_suffixes:this._$esjava$B_continue_stemming_noun_suffixes=!1}set B_continue_stemming_noun_suffixes(i){this._$esjava$B_continue_stemming_noun_suffixes=i}get I_strlen(){return Object.prototype.hasOwnProperty.call(this,"_$esjava$I_strlen")?this._$esjava$I_strlen:this._$esjava$I_strlen=0}set I_strlen(i){this._$esjava$I_strlen=i}r_check_vowel_harmony$esjava$0(){let i,e,r,a,t,s,n,h,o,l,u;i=this.limit-this.cursor;i:for(;;){e=this.limit-this.cursor;e:do{if(!this.in_grouping_b$esjava$3(c.g_vowel,97,305))break e;this.cursor=this.limit-e;break i}while(0);if(this.cursor=this.limit-e,this.cursor<=this.limit_backward)return!1;this.cursor--}i:do{r=this.limit-this.cursor;e:do{if(!this.eq_s_b$esjava$2(1,"a"))break e;r:for(;;){a=this.limit-this.cursor;a:do{if(!this.in_grouping_b$esjava$3(c.g_vowel1,97,305))break a;this.cursor=this.limit-a;break r}while(0);if(this.cursor=this.limit-a,this.cursor<=this.limit_backward)break e;this.cursor--}break i}while(0);this.cursor=this.limit-r;e:do{if(!this.eq_s_b$esjava$2(1,"e"))break e;r:for(;;){t=this.limit-this.cursor;a:do{if(!this.in_grouping_b$esjava$3(c.g_vowel2,101,252))break a;this.cursor=this.limit-t;break r}while(0);if(this.cursor=this.limit-t,this.cursor<=this.limit_backward)break e;this.cursor--}break i}while(0);this.cursor=this.limit-r;e:do{if(!this.eq_s_b$esjava$2(1,"ı"))break e;r:for(;;){s=this.limit-this.cursor;a:do{if(!this.in_grouping_b$esjava$3(c.g_vowel3,97,305))break a;this.cursor=this.limit-s;break r}while(0);if(this.cursor=this.limit-s,this.cursor<=this.limit_backward)break e;this.cursor--}break i}while(0);this.cursor=this.limit-r;e:do{if(!this.eq_s_b$esjava$2(1,"i"))break e;r:for(;;){n=this.limit-this.cursor;a:do{if(!this.in_grouping_b$esjava$3(c.g_vowel4,101,105))break a;this.cursor=this.limit-n;break r}while(0);if(this.cursor=this.limit-n,this.cursor<=this.limit_backward)break e;this.cursor--}break i}while(0);this.cursor=this.limit-r;e:do{if(!this.eq_s_b$esjava$2(1,"o"))break e;r:for(;;){h=this.limit-this.cursor;a:do{if(!this.in_grouping_b$esjava$3(c.g_vowel5,111,117))break a;this.cursor=this.limit-h;break r}while(0);if(this.cursor=this.limit-h,this.cursor<=this.limit_backward)break e;this.cursor--}break i}while(0);this.cursor=this.limit-r;e:do{if(!this.eq_s_b$esjava$2(1,"ö"))break e;r:for(;;){o=this.limit-this.cursor;a:do{if(!this.in_grouping_b$esjava$3(c.g_vowel6,246,252))break a;this.cursor=this.limit-o;break r}while(0);if(this.cursor=this.limit-o,this.cursor<=this.limit_backward)break e;this.cursor--}break i}while(0);this.cursor=this.limit-r;e:do{if(!this.eq_s_b$esjava$2(1,"u"))break e;r:for(;;){l=this.limit-this.cursor;a:do{if(!this.in_grouping_b$esjava$3(c.g_vowel5,111,117))break a;this.cursor=this.limit-l;break r}while(0);if(this.cursor=this.limit-l,this.cursor<=this.limit_backward)break e;this.cursor--}break i}while(0);if(this.cursor=this.limit-r,!this.eq_s_b$esjava$2(1,"ü"))return!1;e:for(;;){u=this.limit-this.cursor;r:do{if(!this.in_grouping_b$esjava$3(c.g_vowel6,246,252))break r;this.cursor=this.limit-u;break e}while(0);if(this.cursor=this.limit-u,this.cursor<=this.limit_backward)return!1;this.cursor--}}while(0);return this.cursor=this.limit-i,!0}r_mark_suffix_with_optional_n_consonant$esjava$0(){let i,e,r,a,t,s,n;i:do{i=this.limit-this.cursor;e:do{if(e=this.limit-this.cursor,!this.eq_s_b$esjava$2(1,"n"))break e;if(this.cursor=this.limit-e,this.cursor<=this.limit_backward)break e;if(this.cursor--,r=this.limit-this.cursor,!this.in_grouping_b$esjava$3(c.g_vowel,97,305))break e;this.cursor=this.limit-r;break i}while(0);this.cursor=this.limit-i,a=this.limit-this.cursor;e:do{if(t=this.limit-this.cursor,!this.eq_s_b$esjava$2(1,"n"))break e;return this.cursor=this.limit-t,!1}while(0);if(this.cursor=this.limit-a,s=this.limit-this.cursor,this.cursor<=this.limit_backward)return!1;if(this.cursor--,n=this.limit-this.cursor,!this.in_grouping_b$esjava$3(c.g_vowel,97,305))return!1;this.cursor=this.limit-n,this.cursor=this.limit-s}while(0);return!0}r_mark_suffix_with_optional_s_consonant$esjava$0(){let i,e,r,a,t,s,n;i:do{i=this.limit-this.cursor;e:do{if(e=this.limit-this.cursor,!this.eq_s_b$esjava$2(1,"s"))break e;if(this.cursor=this.limit-e,this.cursor<=this.limit_backward)break e;if(this.cursor--,r=this.limit-this.cursor,!this.in_grouping_b$esjava$3(c.g_vowel,97,305))break e;this.cursor=this.limit-r;break i}while(0);this.cursor=this.limit-i,a=this.limit-this.cursor;e:do{if(t=this.limit-this.cursor,!this.eq_s_b$esjava$2(1,"s"))break e;return this.cursor=this.limit-t,!1}while(0);if(this.cursor=this.limit-a,s=this.limit-this.cursor,this.cursor<=this.limit_backward)return!1;if(this.cursor--,n=this.limit-this.cursor,!this.in_grouping_b$esjava$3(c.g_vowel,97,305))return!1;this.cursor=this.limit-n,this.cursor=this.limit-s}while(0);return!0}r_mark_suffix_with_optional_y_consonant$esjava$0(){let i,e,r,a,t,s,n;i:do{i=this.limit-this.cursor;e:do{if(e=this.limit-this.cursor,!this.eq_s_b$esjava$2(1,"y"))break e;if(this.cursor=this.limit-e,this.cursor<=this.limit_backward)break e;if(this.cursor--,r=this.limit-this.cursor,!this.in_grouping_b$esjava$3(c.g_vowel,97,305))break e;this.cursor=this.limit-r;break i}while(0);this.cursor=this.limit-i,a=this.limit-this.cursor;e:do{if(t=this.limit-this.cursor,!this.eq_s_b$esjava$2(1,"y"))break e;return this.cursor=this.limit-t,!1}while(0);if(this.cursor=this.limit-a,s=this.limit-this.cursor,this.cursor<=this.limit_backward)return!1;if(this.cursor--,n=this.limit-this.cursor,!this.in_grouping_b$esjava$3(c.g_vowel,97,305))return!1;this.cursor=this.limit-n,this.cursor=this.limit-s}while(0);return!0}r_mark_suffix_with_optional_U_vowel$esjava$0(){let i,e,r,a,t,s,n;i:do{i=this.limit-this.cursor;e:do{if(e=this.limit-this.cursor,!this.in_grouping_b$esjava$3(c.g_U,105,305))break e;if(this.cursor=this.limit-e,this.cursor<=this.limit_backward)break e;if(this.cursor--,r=this.limit-this.cursor,!this.out_grouping_b$esjava$3(c.g_vowel,97,305))break e;this.cursor=this.limit-r;break i}while(0);this.cursor=this.limit-i,a=this.limit-this.cursor;e:do{if(t=this.limit-this.cursor,!this.in_grouping_b$esjava$3(c.g_U,105,305))break e;return this.cursor=this.limit-t,!1}while(0);if(this.cursor=this.limit-a,s=this.limit-this.cursor,this.cursor<=this.limit_backward)return!1;if(this.cursor--,n=this.limit-this.cursor,!this.out_grouping_b$esjava$3(c.g_vowel,97,305))return!1;this.cursor=this.limit-n,this.cursor=this.limit-s}while(0);return!0}r_mark_possessives$esjava$0(){return 0!==this.find_among_b$esjava$2(c.a_0,10)&&!!this.r_mark_suffix_with_optional_U_vowel$esjava$0()}r_mark_sU$esjava$0(){return!!this.r_check_vowel_harmony$esjava$0()&&!!this.in_grouping_b$esjava$3(c.g_U,105,305)&&!!this.r_mark_suffix_with_optional_s_consonant$esjava$0()}r_mark_lArI$esjava$0(){return 0!==this.find_among_b$esjava$2(c.a_1,2)}r_mark_yU$esjava$0(){return!!this.r_check_vowel_harmony$esjava$0()&&!!this.in_grouping_b$esjava$3(c.g_U,105,305)&&!!this.r_mark_suffix_with_optional_y_consonant$esjava$0()}r_mark_nU$esjava$0(){return!!this.r_check_vowel_harmony$esjava$0()&&0!==this.find_among_b$esjava$2(c.a_2,4)}r_mark_nUn$esjava$0(){return!!this.r_check_vowel_harmony$esjava$0()&&0!==this.find_among_b$esjava$2(c.a_3,4)&&!!this.r_mark_suffix_with_optional_n_consonant$esjava$0()}r_mark_yA$esjava$0(){return!!this.r_check_vowel_harmony$esjava$0()&&0!==this.find_among_b$esjava$2(c.a_4,2)&&!!this.r_mark_suffix_with_optional_y_consonant$esjava$0()}r_mark_nA$esjava$0(){return!!this.r_check_vowel_harmony$esjava$0()&&0!==this.find_among_b$esjava$2(c.a_5,2)}r_mark_DA$esjava$0(){return!!this.r_check_vowel_harmony$esjava$0()&&0!==this.find_among_b$esjava$2(c.a_6,4)}r_mark_ndA$esjava$0(){return!!this.r_check_vowel_harmony$esjava$0()&&0!==this.find_among_b$esjava$2(c.a_7,2)}r_mark_DAn$esjava$0(){return!!this.r_check_vowel_harmony$esjava$0()&&0!==this.find_among_b$esjava$2(c.a_8,4)}r_mark_ndAn$esjava$0(){return!!this.r_check_vowel_harmony$esjava$0()&&0!==this.find_among_b$esjava$2(c.a_9,2)}r_mark_ylA$esjava$0(){return!!this.r_check_vowel_harmony$esjava$0()&&0!==this.find_among_b$esjava$2(c.a_10,2)&&!!this.r_mark_suffix_with_optional_y_consonant$esjava$0()}r_mark_ki$esjava$0(){return!!this.eq_s_b$esjava$2(2,"ki")}r_mark_ncA$esjava$0(){return!!this.r_check_vowel_harmony$esjava$0()&&0!==this.find_among_b$esjava$2(c.a_11,2)&&!!this.r_mark_suffix_with_optional_n_consonant$esjava$0()}r_mark_yUm$esjava$0(){return!!this.r_check_vowel_harmony$esjava$0()&&0!==this.find_among_b$esjava$2(c.a_12,4)&&!!this.r_mark_suffix_with_optional_y_consonant$esjava$0()}r_mark_sUn$esjava$0(){return!!this.r_check_vowel_harmony$esjava$0()&&0!==this.find_among_b$esjava$2(c.a_13,4)}r_mark_yUz$esjava$0(){return!!this.r_check_vowel_harmony$esjava$0()&&0!==this.find_among_b$esjava$2(c.a_14,4)&&!!this.r_mark_suffix_with_optional_y_consonant$esjava$0()}r_mark_sUnUz$esjava$0(){return 0!==this.find_among_b$esjava$2(c.a_15,4)}r_mark_lAr$esjava$0(){return!!this.r_check_vowel_harmony$esjava$0()&&0!==this.find_among_b$esjava$2(c.a_16,2)}r_mark_nUz$esjava$0(){return!!this.r_check_vowel_harmony$esjava$0()&&0!==this.find_among_b$esjava$2(c.a_17,4)}r_mark_DUr$esjava$0(){return!!this.r_check_vowel_harmony$esjava$0()&&0!==this.find_among_b$esjava$2(c.a_18,8)}r_mark_cAsInA$esjava$0(){return 0!==this.find_among_b$esjava$2(c.a_19,2)}r_mark_yDU$esjava$0(){return!!this.r_check_vowel_harmony$esjava$0()&&0!==this.find_among_b$esjava$2(c.a_20,32)&&!!this.r_mark_suffix_with_optional_y_consonant$esjava$0()}r_mark_ysA$esjava$0(){return 0!==this.find_among_b$esjava$2(c.a_21,8)&&!!this.r_mark_suffix_with_optional_y_consonant$esjava$0()}r_mark_ymUs_$esjava$0(){return!!this.r_check_vowel_harmony$esjava$0()&&0!==this.find_among_b$esjava$2(c.a_22,4)&&!!this.r_mark_suffix_with_optional_y_consonant$esjava$0()}r_mark_yken$esjava$0(){return!!this.eq_s_b$esjava$2(3,"ken")&&!!this.r_mark_suffix_with_optional_y_consonant$esjava$0()}r_stem_nominal_verb_suffixes$esjava$0(){let i,e,r,a,t,s,n,h,o,l;this.ket=this.cursor,this.B_continue_stemming_noun_suffixes=!0;i:do{i=this.limit-this.cursor;e:do{r:do{e=this.limit-this.cursor;a:do{if(!this.r_mark_ymUs_$esjava$0())break a;break r}while(0);this.cursor=this.limit-e;a:do{if(!this.r_mark_yDU$esjava$0())break a;break r}while(0);this.cursor=this.limit-e;a:do{if(!this.r_mark_ysA$esjava$0())break a;break r}while(0);if(this.cursor=this.limit-e,!this.r_mark_yken$esjava$0())break e}while(0);break i}while(0);this.cursor=this.limit-i;e:do{if(!this.r_mark_cAsInA$esjava$0())break e;r:do{r=this.limit-this.cursor;a:do{if(!this.r_mark_sUnUz$esjava$0())break a;break r}while(0);this.cursor=this.limit-r;a:do{if(!this.r_mark_lAr$esjava$0())break a;break r}while(0);this.cursor=this.limit-r;a:do{if(!this.r_mark_yUm$esjava$0())break a;break r}while(0);this.cursor=this.limit-r;a:do{if(!this.r_mark_sUn$esjava$0())break a;break r}while(0);this.cursor=this.limit-r;a:do{if(!this.r_mark_yUz$esjava$0())break a;break r}while(0);this.cursor=this.limit-r}while(0);if(!this.r_mark_ymUs_$esjava$0())break e;break i}while(0);this.cursor=this.limit-i;e:do{if(!this.r_mark_lAr$esjava$0())break e;this.bra=this.cursor,this.slice_del$esjava$0(),a=this.limit-this.cursor;r:do{this.ket=this.cursor;a:do{t=this.limit-this.cursor;t:do{if(!this.r_mark_DUr$esjava$0())break t;break a}while(0);this.cursor=this.limit-t;t:do{if(!this.r_mark_yDU$esjava$0())break t;break a}while(0);this.cursor=this.limit-t;t:do{if(!this.r_mark_ysA$esjava$0())break t;break a}while(0);if(this.cursor=this.limit-t,!this.r_mark_ymUs_$esjava$0()){this.cursor=this.limit-a;break r}}while(0)}while(0);this.B_continue_stemming_noun_suffixes=!1;break i}while(0);this.cursor=this.limit-i;e:do{if(!this.r_mark_nUz$esjava$0())break e;r:do{s=this.limit-this.cursor;a:do{if(!this.r_mark_yDU$esjava$0())break a;break r}while(0);if(this.cursor=this.limit-s,!this.r_mark_ysA$esjava$0())break e}while(0);break i}while(0);this.cursor=this.limit-i;e:do{r:do{n=this.limit-this.cursor;a:do{if(!this.r_mark_sUnUz$esjava$0())break a;break r}while(0);this.cursor=this.limit-n;a:do{if(!this.r_mark_yUz$esjava$0())break a;break r}while(0);this.cursor=this.limit-n;a:do{if(!this.r_mark_sUn$esjava$0())break a;break r}while(0);if(this.cursor=this.limit-n,!this.r_mark_yUm$esjava$0())break e}while(0);this.bra=this.cursor,this.slice_del$esjava$0(),h=this.limit-this.cursor;r:do{if(this.ket=this.cursor,!this.r_mark_ymUs_$esjava$0()){this.cursor=this.limit-h;break r}}while(0);break i}while(0);if(this.cursor=this.limit-i,!this.r_mark_DUr$esjava$0())return!1;this.bra=this.cursor,this.slice_del$esjava$0(),o=this.limit-this.cursor;e:do{this.ket=this.cursor;r:do{l=this.limit-this.cursor;a:do{if(!this.r_mark_sUnUz$esjava$0())break a;break r}while(0);this.cursor=this.limit-l;a:do{if(!this.r_mark_lAr$esjava$0())break a;break r}while(0);this.cursor=this.limit-l;a:do{if(!this.r_mark_yUm$esjava$0())break a;break r}while(0);this.cursor=this.limit-l;a:do{if(!this.r_mark_sUn$esjava$0())break a;break r}while(0);this.cursor=this.limit-l;a:do{if(!this.r_mark_yUz$esjava$0())break a;break r}while(0);this.cursor=this.limit-l}while(0);if(!this.r_mark_ymUs_$esjava$0()){this.cursor=this.limit-o;break e}}while(0)}while(0);return this.bra=this.cursor,this.slice_del$esjava$0(),!0}r_stem_suffix_chain_before_ki$esjava$0(){let i,e,r,a,t,s,n,h,o,l,u;if(this.ket=this.cursor,!this.r_mark_ki$esjava$0())return!1;i:do{i=this.limit-this.cursor;e:do{if(!this.r_mark_DA$esjava$0())break e;this.bra=this.cursor,this.slice_del$esjava$0(),e=this.limit-this.cursor;r:do{this.ket=this.cursor;a:do{r=this.limit-this.cursor;t:do{if(!this.r_mark_lAr$esjava$0())break t;this.bra=this.cursor,this.slice_del$esjava$0(),a=this.limit-this.cursor;s:do{if(!this.r_stem_suffix_chain_before_ki$esjava$0()){this.cursor=this.limit-a;break s}}while(0);break a}while(0);if(this.cursor=this.limit-r,!this.r_mark_possessives$esjava$0()){this.cursor=this.limit-e;break r}this.bra=this.cursor,this.slice_del$esjava$0(),t=this.limit-this.cursor;t:do{if(this.ket=this.cursor,!this.r_mark_lAr$esjava$0()){this.cursor=this.limit-t;break t}if(this.bra=this.cursor,this.slice_del$esjava$0(),!this.r_stem_suffix_chain_before_ki$esjava$0()){this.cursor=this.limit-t;break t}}while(0)}while(0)}while(0);break i}while(0);this.cursor=this.limit-i;e:do{if(!this.r_mark_nUn$esjava$0())break e;this.bra=this.cursor,this.slice_del$esjava$0(),s=this.limit-this.cursor;r:do{this.ket=this.cursor;a:do{n=this.limit-this.cursor;t:do{if(!this.r_mark_lArI$esjava$0())break t;this.bra=this.cursor,this.slice_del$esjava$0();break a}while(0);this.cursor=this.limit-n;t:do{this.ket=this.cursor;s:do{h=this.limit-this.cursor;n:do{if(!this.r_mark_possessives$esjava$0())break n;break s}while(0);if(this.cursor=this.limit-h,!this.r_mark_sU$esjava$0())break t}while(0);this.bra=this.cursor,this.slice_del$esjava$0(),o=this.limit-this.cursor;s:do{if(this.ket=this.cursor,!this.r_mark_lAr$esjava$0()){this.cursor=this.limit-o;break s}if(this.bra=this.cursor,this.slice_del$esjava$0(),!this.r_stem_suffix_chain_before_ki$esjava$0()){this.cursor=this.limit-o;break s}}while(0);break a}while(0);if(this.cursor=this.limit-n,!this.r_stem_suffix_chain_before_ki$esjava$0()){this.cursor=this.limit-s;break r}}while(0)}while(0);break i}while(0);if(this.cursor=this.limit-i,!this.r_mark_ndA$esjava$0())return!1;e:do{l=this.limit-this.cursor;r:do{if(!this.r_mark_lArI$esjava$0())break r;this.bra=this.cursor,this.slice_del$esjava$0();break e}while(0);this.cursor=this.limit-l;r:do{if(!this.r_mark_sU$esjava$0())break r;this.bra=this.cursor,this.slice_del$esjava$0(),u=this.limit-this.cursor;a:do{if(this.ket=this.cursor,!this.r_mark_lAr$esjava$0()){this.cursor=this.limit-u;break a}if(this.bra=this.cursor,this.slice_del$esjava$0(),!this.r_stem_suffix_chain_before_ki$esjava$0()){this.cursor=this.limit-u;break a}}while(0);break e}while(0);if(this.cursor=this.limit-l,!this.r_stem_suffix_chain_before_ki$esjava$0())return!1}while(0)}while(0);return!0}r_stem_noun_suffixes$esjava$0(){let i,e,r,a,t,s,n,h,o,l,u,m,_,k,c,d,b,$,f,y,g,j,v,w,p,z,D;i:do{i=this.limit-this.cursor;e:do{if(this.ket=this.cursor,!this.r_mark_lAr$esjava$0())break e;this.bra=this.cursor,this.slice_del$esjava$0(),e=this.limit-this.cursor;r:do{if(!this.r_stem_suffix_chain_before_ki$esjava$0()){this.cursor=this.limit-e;break r}}while(0);break i}while(0);this.cursor=this.limit-i;e:do{if(this.ket=this.cursor,!this.r_mark_ncA$esjava$0())break e;this.bra=this.cursor,this.slice_del$esjava$0(),r=this.limit-this.cursor;r:do{a:do{a=this.limit-this.cursor;t:do{if(this.ket=this.cursor,!this.r_mark_lArI$esjava$0())break t;this.bra=this.cursor,this.slice_del$esjava$0();break a}while(0);this.cursor=this.limit-a;t:do{this.ket=this.cursor;s:do{t=this.limit-this.cursor;n:do{if(!this.r_mark_possessives$esjava$0())break n;break s}while(0);if(this.cursor=this.limit-t,!this.r_mark_sU$esjava$0())break t}while(0);this.bra=this.cursor,this.slice_del$esjava$0(),s=this.limit-this.cursor;s:do{if(this.ket=this.cursor,!this.r_mark_lAr$esjava$0()){this.cursor=this.limit-s;break s}if(this.bra=this.cursor,this.slice_del$esjava$0(),!this.r_stem_suffix_chain_before_ki$esjava$0()){this.cursor=this.limit-s;break s}}while(0);break a}while(0);if(this.cursor=this.limit-a,this.ket=this.cursor,!this.r_mark_lAr$esjava$0()){this.cursor=this.limit-r;break r}if(this.bra=this.cursor,this.slice_del$esjava$0(),!this.r_stem_suffix_chain_before_ki$esjava$0()){this.cursor=this.limit-r;break r}}while(0)}while(0);break i}while(0);this.cursor=this.limit-i;e:do{this.ket=this.cursor;r:do{n=this.limit-this.cursor;a:do{if(!this.r_mark_ndA$esjava$0())break a;break r}while(0);if(this.cursor=this.limit-n,!this.r_mark_nA$esjava$0())break e}while(0);r:do{h=this.limit-this.cursor;a:do{if(!this.r_mark_lArI$esjava$0())break a;this.bra=this.cursor,this.slice_del$esjava$0();break r}while(0);this.cursor=this.limit-h;a:do{if(!this.r_mark_sU$esjava$0())break a;this.bra=this.cursor,this.slice_del$esjava$0(),o=this.limit-this.cursor;t:do{if(this.ket=this.cursor,!this.r_mark_lAr$esjava$0()){this.cursor=this.limit-o;break t}if(this.bra=this.cursor,this.slice_del$esjava$0(),!this.r_stem_suffix_chain_before_ki$esjava$0()){this.cursor=this.limit-o;break t}}while(0);break r}while(0);if(this.cursor=this.limit-h,!this.r_stem_suffix_chain_before_ki$esjava$0())break e}while(0);break i}while(0);this.cursor=this.limit-i;e:do{this.ket=this.cursor;r:do{l=this.limit-this.cursor;a:do{if(!this.r_mark_ndAn$esjava$0())break a;break r}while(0);if(this.cursor=this.limit-l,!this.r_mark_nU$esjava$0())break e}while(0);r:do{u=this.limit-this.cursor;a:do{if(!this.r_mark_sU$esjava$0())break a;this.bra=this.cursor,this.slice_del$esjava$0(),m=this.limit-this.cursor;t:do{if(this.ket=this.cursor,!this.r_mark_lAr$esjava$0()){this.cursor=this.limit-m;break t}if(this.bra=this.cursor,this.slice_del$esjava$0(),!this.r_stem_suffix_chain_before_ki$esjava$0()){this.cursor=this.limit-m;break t}}while(0);break r}while(0);if(this.cursor=this.limit-u,!this.r_mark_lArI$esjava$0())break e}while(0);break i}while(0);this.cursor=this.limit-i;e:do{if(this.ket=this.cursor,!this.r_mark_DAn$esjava$0())break e;this.bra=this.cursor,this.slice_del$esjava$0(),_=this.limit-this.cursor;r:do{this.ket=this.cursor;a:do{k=this.limit-this.cursor;t:do{if(!this.r_mark_possessives$esjava$0())break t;this.bra=this.cursor,this.slice_del$esjava$0(),c=this.limit-this.cursor;s:do{if(this.ket=this.cursor,!this.r_mark_lAr$esjava$0()){this.cursor=this.limit-c;break s}if(this.bra=this.cursor,this.slice_del$esjava$0(),!this.r_stem_suffix_chain_before_ki$esjava$0()){this.cursor=this.limit-c;break s}}while(0);break a}while(0);this.cursor=this.limit-k;t:do{if(!this.r_mark_lAr$esjava$0())break t;this.bra=this.cursor,this.slice_del$esjava$0(),d=this.limit-this.cursor;s:do{if(!this.r_stem_suffix_chain_before_ki$esjava$0()){this.cursor=this.limit-d;break s}}while(0);break a}while(0);if(this.cursor=this.limit-k,!this.r_stem_suffix_chain_before_ki$esjava$0()){this.cursor=this.limit-_;break r}}while(0)}while(0);break i}while(0);this.cursor=this.limit-i;e:do{this.ket=this.cursor;r:do{b=this.limit-this.cursor;a:do{if(!this.r_mark_nUn$esjava$0())break a;break r}while(0);if(this.cursor=this.limit-b,!this.r_mark_ylA$esjava$0())break e}while(0);this.bra=this.cursor,this.slice_del$esjava$0(),$=this.limit-this.cursor;r:do{a:do{f=this.limit-this.cursor;t:do{if(this.ket=this.cursor,!this.r_mark_lAr$esjava$0())break t;if(this.bra=this.cursor,this.slice_del$esjava$0(),!this.r_stem_suffix_chain_before_ki$esjava$0())break t;break a}while(0);this.cursor=this.limit-f;t:do{this.ket=this.cursor;s:do{y=this.limit-this.cursor;n:do{if(!this.r_mark_possessives$esjava$0())break n;break s}while(0);if(this.cursor=this.limit-y,!this.r_mark_sU$esjava$0())break t}while(0);this.bra=this.cursor,this.slice_del$esjava$0(),g=this.limit-this.cursor;s:do{if(this.ket=this.cursor,!this.r_mark_lAr$esjava$0()){this.cursor=this.limit-g;break s}if(this.bra=this.cursor,this.slice_del$esjava$0(),!this.r_stem_suffix_chain_before_ki$esjava$0()){this.cursor=this.limit-g;break s}}while(0);break a}while(0);if(this.cursor=this.limit-f,!this.r_stem_suffix_chain_before_ki$esjava$0()){this.cursor=this.limit-$;break r}}while(0)}while(0);break i}while(0);this.cursor=this.limit-i;e:do{if(this.ket=this.cursor,!this.r_mark_lArI$esjava$0())break e;this.bra=this.cursor,this.slice_del$esjava$0();break i}while(0);this.cursor=this.limit-i;e:do{if(!this.r_stem_suffix_chain_before_ki$esjava$0())break e;break i}while(0);this.cursor=this.limit-i;e:do{this.ket=this.cursor;r:do{j=this.limit-this.cursor;a:do{if(!this.r_mark_DA$esjava$0())break a;break r}while(0);this.cursor=this.limit-j;a:do{if(!this.r_mark_yU$esjava$0())break a;break r}while(0);if(this.cursor=this.limit-j,!this.r_mark_yA$esjava$0())break e}while(0);this.bra=this.cursor,this.slice_del$esjava$0(),v=this.limit-this.cursor;r:do{this.ket=this.cursor;a:do{w=this.limit-this.cursor;t:do{if(!this.r_mark_possessives$esjava$0())break t;this.bra=this.cursor,this.slice_del$esjava$0(),p=this.limit-this.cursor;s:do{if(this.ket=this.cursor,!this.r_mark_lAr$esjava$0()){this.cursor=this.limit-p;break s}}while(0);break a}while(0);if(this.cursor=this.limit-w,!this.r_mark_lAr$esjava$0()){this.cursor=this.limit-v;break r}}while(0);if(this.bra=this.cursor,this.slice_del$esjava$0(),this.ket=this.cursor,!this.r_stem_suffix_chain_before_ki$esjava$0()){this.cursor=this.limit-v;break r}}while(0);break i}while(0);this.cursor=this.limit-i,this.ket=this.cursor;e:do{z=this.limit-this.cursor;r:do{if(!this.r_mark_possessives$esjava$0())break r;break e}while(0);if(this.cursor=this.limit-z,!this.r_mark_sU$esjava$0())return!1}while(0);this.bra=this.cursor,this.slice_del$esjava$0(),D=this.limit-this.cursor;e:do{if(this.ket=this.cursor,!this.r_mark_lAr$esjava$0()){this.cursor=this.limit-D;break e}if(this.bra=this.cursor,this.slice_del$esjava$0(),!this.r_stem_suffix_chain_before_ki$esjava$0()){this.cursor=this.limit-D;break e}}while(0)}while(0);return!0}r_post_process_last_consonants$esjava$0(){let i;if(this.ket=this.cursor,i=this.find_among_b$esjava$2(c.a_23,4),0===i)return!1;switch(this.bra=this.cursor,i){case 0:return!1;case 1:this.slice_from$esjava$1("p");break;case 2:this.slice_from$esjava$1("ç");break;case 3:this.slice_from$esjava$1("t");break;case 4:this.slice_from$esjava$1("k")}return!0}r_append_U_to_stems_ending_with_d_or_g$esjava$0(){let i,e,r,a,t,s,n,h,o,l,u,m,_,k,d;i=this.limit-this.cursor;i:do{e=this.limit-this.cursor;e:do{if(!this.eq_s_b$esjava$2(1,"d"))break e;break i}while(0);if(this.cursor=this.limit-e,!this.eq_s_b$esjava$2(1,"g"))return!1}while(0);this.cursor=this.limit-i;i:do{r=this.limit-this.cursor;e:do{a=this.limit-this.cursor;r:for(;;){t=this.limit-this.cursor;a:do{if(!this.in_grouping_b$esjava$3(c.g_vowel,97,305))break a;this.cursor=this.limit-t;break r}while(0);if(this.cursor=this.limit-t,this.cursor<=this.limit_backward)break e;this.cursor--}r:do{s=this.limit-this.cursor;a:do{if(!this.eq_s_b$esjava$2(1,"a"))break a;break r}while(0);if(this.cursor=this.limit-s,!this.eq_s_b$esjava$2(1,"ı"))break e}while(0);this.cursor=this.limit-a;{const i=this.cursor;this.insert$esjava$3(this.cursor,this.cursor,"ı"),this.cursor=i}break i}while(0);this.cursor=this.limit-r;e:do{n=this.limit-this.cursor;r:for(;;){h=this.limit-this.cursor;a:do{if(!this.in_grouping_b$esjava$3(c.g_vowel,97,305))break a;this.cursor=this.limit-h;break r}while(0);if(this.cursor=this.limit-h,this.cursor<=this.limit_backward)break e;this.cursor--}r:do{o=this.limit-this.cursor;a:do{if(!this.eq_s_b$esjava$2(1,"e"))break a;break r}while(0);if(this.cursor=this.limit-o,!this.eq_s_b$esjava$2(1,"i"))break e}while(0);this.cursor=this.limit-n;{const i=this.cursor;this.insert$esjava$3(this.cursor,this.cursor,"i"),this.cursor=i}break i}while(0);this.cursor=this.limit-r;e:do{l=this.limit-this.cursor;r:for(;;){u=this.limit-this.cursor;a:do{if(!this.in_grouping_b$esjava$3(c.g_vowel,97,305))break a;this.cursor=this.limit-u;break r}while(0);if(this.cursor=this.limit-u,this.cursor<=this.limit_backward)break e;this.cursor--}r:do{m=this.limit-this.cursor;a:do{if(!this.eq_s_b$esjava$2(1,"o"))break a;break r}while(0);if(this.cursor=this.limit-m,!this.eq_s_b$esjava$2(1,"u"))break e}while(0);this.cursor=this.limit-l;{const i=this.cursor;this.insert$esjava$3(this.cursor,this.cursor,"u"),this.cursor=i}break i}while(0);this.cursor=this.limit-r,_=this.limit-this.cursor;e:for(;;){k=this.limit-this.cursor;r:do{if(!this.in_grouping_b$esjava$3(c.g_vowel,97,305))break r;this.cursor=this.limit-k;break e}while(0);if(this.cursor=this.limit-k,this.cursor<=this.limit_backward)return!1;this.cursor--}e:do{d=this.limit-this.cursor;r:do{if(!this.eq_s_b$esjava$2(1,"ö"))break r;break e}while(0);if(this.cursor=this.limit-d,!this.eq_s_b$esjava$2(1,"ü"))return!1}while(0);this.cursor=this.limit-_;{const i=this.cursor;this.insert$esjava$3(this.cursor,this.cursor,"ü"),this.cursor=i}}while(0);return!0}r_more_than_one_syllable_word$esjava$0(){let i,e;i=this.cursor;{let i=2;i:for(;;){e=this.cursor;e:do{r:for(;;){a:do{if(!this.in_grouping$esjava$3(c.g_vowel,97,305))break a;break r}while(0);if(this.cursor>=this.limit)break e;this.cursor++}i--;continue i}while(0);this.cursor=e;break i}if(i>0)return!1}return this.cursor=i,!0}r_is_reserved_word$esjava$0(){let i,e,r;i:do{i=this.cursor;e:do{e=this.cursor;r:for(;;){a:do{if(!this.eq_s$esjava$2(2,"ad"))break a;break r}while(0);if(this.cursor>=this.limit)break e;this.cursor++}if(this.I_strlen=2,this.I_strlen!==this.limit)break e;this.cursor=e;break i}while(0);this.cursor=i,r=this.cursor;e:for(;;){r:do{if(!this.eq_s$esjava$2(5,"soyad"))break r;break e}while(0);if(this.cursor>=this.limit)return!1;this.cursor++}if(this.I_strlen=5,this.I_strlen!==this.limit)return!1;this.cursor=r}while(0);return!0}r_postlude$esjava$0(){let i,e,r;i=this.cursor;i:do{if(!this.r_is_reserved_word$esjava$0())break i;return!1}while(0);this.cursor=i,this.limit_backward=this.cursor,this.cursor=this.limit,e=this.limit-this.cursor;i:do{if(!this.r_append_U_to_stems_ending_with_d_or_g$esjava$0())break i}while(0);this.cursor=this.limit-e,r=this.limit-this.cursor;i:do{if(!this.r_post_process_last_consonants$esjava$0())break i}while(0);return this.cursor=this.limit-r,this.cursor=this.limit_backward,!0}stem$esjava$0(){let i,e;if(!this.r_more_than_one_syllable_word$esjava$0())return!1;this.limit_backward=this.cursor,this.cursor=this.limit,i=this.limit-this.cursor;i:do{if(!this.r_stem_nominal_verb_suffixes$esjava$0())break i}while(0);if(this.cursor=this.limit-i,!this.B_continue_stemming_noun_suffixes)return!1;e=this.limit-this.cursor;i:do{if(!this.r_stem_noun_suffixes$esjava$0())break i}while(0);return this.cursor=this.limit-e,this.cursor=this.limit_backward,!!this.r_postlude$esjava$0()}stem(...i){return 0===i.length?this.stem$esjava$0(...i):super.stem(...i)}}const d=c,{baseStemmer:b}=r.languageProcessing;function $(i){const e=(0,l.get)(i.getData("morphology"),"tr",!1);return e?i=>function(i,e){i=(i=i.toLowerCase()).replace("'","");const r=new d(e);return r.setCurrent(i),r.stem(),r.getCurrent()}(i,e):b}const f=["nmak","nmek","nir","nır","nür","nur","nıyor","niyor","ndı","ndi","ndu","ndü","nmış","nmiş","nmuş","nmüş","necek","nacak","nmıştı","nmişti","nmuştu","nmüştü","nıyordu","niyordu","nuyordu","nüyordu","necekti","nacaktı","nsa","nse","nmalı","nmeli","nmaz","nmez","anmak","enmek","ınmak","inmek","unmak","ünmek","anır","enir","ınır","inir","unur","ünür","anıyor","eniyor","ınıyor","iniyor","unuyor","ünüyor","andı","endi","ındı","indi","undu","ündü","anmış","enmiş","ınmış","inmiş","unmuş","ünmüş","anacak","enecek","ınacak","inecek","unacak","ünecek","ınmıştı","inmişti","unmuştu","ünmüştü","ınıyordu","iniyordu","unuyordu","ünüyordu","necekti","nacaktı","ansa","ense","ınsa","inse","unsa","ünse","anmalı","enmeli","ınmalı","inmeli","unmalı","ünmeli","anmaz","enmez","ınmaz","inmez","unmaz","ünmez","ılmak","ilmek","ulmak","ülmek","ılır","ilir","ulur","ülür","ılınıyor","iliniyor","ulunuyor","ülüyor","ıldı","ildi","uldu","üldü","ılmış","ilmiş","ulmuş","ülmül","ılacak","ilecek","ulacak","ülecek","ılmıştı","ilmişti","ulmuştu","ülmüştü","ılıyordu","iliyordu","uluyordu","ülüyordu","necekti","nacaktı","ılsa","ilse","ulsa","ülse","ılmalı","ilmeli","ulmalı","ülmeli","ılmaz","ilmez","ulmaz","ülmez"],y=["kullanmak","ulanmak","bağlanmak","alınmak","boşanmak","kaçınmak","hazırlanmak","olunmak","sığınmak","taşınmak","arlanmak","sakınmak","zanmak","tırmanmak","i̇nanmak","arınmak","kullanmak","isınmak","yıkanmak","öğrenmek","öğrenmek","düşünmek","renmek","düşünmek","ünmek","dönmek","değinmek","eğlenmek","lenmek","öğünmek","deyinmek","örenmek","görünmek","öğrenmek","güvenmek","beğenmek","sünmek","geçinmek","tükenmek","kabullenmek","öğrenmek","kabullenmek","sinir","peynir","münir","alınır","kazanır","yorumlanır","kullanır","uygulanır","dayanır","sağlanır","i̇nanır","özenir","elenir","öğrenir","tersinir","yaşanır","toplanır","tanır","senir","rastlanır","renir","münir","kaynaklanır","bağlanır","hazırlanır","güvenir","enir","söylenir","başlanır","davranır","kapanır","oynanır","uzanır","tanımlanır","tanınır","souvenir","öğrenir","taşınır","konteyner","uyanır","beğenir","hesaplanır","sanır","saklanır","yakalanır","aranır","algılanır","hoşlanır","karşılanır","tamamlanır","münir","yayınlanır","yıkanır","tekrarlanır","atanır","bir","karasenir","i̇ndüklenir","zorlanır","avenir","erdenir","kas-sinir","utanır","üstenir","katlanır","beyazpeynir","şekillenir","sonuçlanır","doğranır","narin","faydalanır","kilinir","hızlanır","yararlanır","kutlanır","saptanır","nedendir","kalınır","ayarlanır","kıskanır","hastalanır","suvenir","yapılabilinir","canlanır","ekillenir","hacklenir","haşlanır","sonuçlanır","resetlenir","beğenir","açıklanır","programming-sinir","i̇sindir","odaklanır","pionir","çalınır","peynir","tutuklanır","sınır","taşımalık","anır","kanır","adanır","lanır","ültanır","rastlanır","haktanır","güneysınır","i̇nanır","açılır-kapanır","sağlanır","tanrı tanır","bağlanır","tanır","yansır","kullanır","açıklanır","dizaynır","düşünür","görünür","siyanür","dünür","düşünür","ünür","çürür","ömür","nür","öğünür","onur","aynur","i̇lknur","ayşenur","öznur","konur","binnur","alinur","gülnur","hükmolunur","atanur","rıza nur","yurdanur","şennur","fatmanur","şennur","zinnur","adanur","semanur","elanur","düşünür","baykonur","edanur","göknur","günnur","beyzanur","görünür","nisanur","saynur","mecnur","lunur","stem","cemalnur","i̇lknur","aynur","elnur","addolunur","ayşenur","birnur","sedanur","alanur","esmanur","elifnur","şahnur","aydanur","senanur","ecenur","havvanur","bozunur","bennur","en-nur","tennur","konur","reddolunur","sondur","olunur","şeymanur","şerefnur","fernur","stem","ceynur","zeynur","gökçenur","mervenur","ernur","sonunur","biyobozunur","şemsinur","haşrolunur","incinur","lanıyor","kulanıyor","nıyor","rastlanıyor","kullanıyor","kaynaklanıyor","kazanıyor","yaşanıyor","alınıyor","i̇nanıyor","tanıyor","hazırlanıyor","dayanıyor","söyleniyor","sanıyor","uygulanıyor","yanıyor","eleniyor","davranıyor","aranıyor","öğreniyor","sağlanıyor","kapanıyor","zorlanıyor","tanınıyor","kombinleniyor","yayınlanıyor","oynanıyor","beğeniyor","uyanıyor","planlanıyor","toplanıyor","reniyor","niyor","öğreniyor","bağlanıyor","uzanıyor","algılanıyor","söyleniyor","tanımlanıyor","vurgulanıyor","karşılanıyor","kınıyor","saklanıyor","başlanıyor","yükleniyor","sıralanıyor","alındı","kendi","pazubandı","nındı","irgandı","yapsındı","yarabandı","açıklandı","bağlandı","kendi","efendi","beyefendi","hanımefendi","i̇kindi","hocaefendi","hindi","bindi","gandi","nakşibendi","selendi","beyefendi","bondi","alindi","kazandı","veliefendi","hanendi","hacklendi","burundi","kullandı","i̇vrindi","başlandı","yasandı","lendi","yayınlandı","andı","merkezefendi","demirhindi","vivendi","grandi","ögrendi","aczmendi","mundi","kapandı","hanendi","kandil","i̇nsandı","çinhindi","randi","yandı","şimdi","semerkant","açıklandı","ravalpindi","tamamlandı","kadınefendi","landi","brendi","beğendi","gecekondu","hindu","soundu","katmandu","kundu","olsundu","poundu","katmandu","duşundu","emrolundu","vahyolundu","hindu","lundu","candu","roundu","göründü","paundu","fırdöndü","düşündü","fondü","üründü","gündöndü","mumsöndü","kendü","i̇nanmış","ınmış","i̇spatlanmış","nınmış","bağlanmış","hazırlanmış","lenmiş","hacklenmiş","öğrenmiş","i̇ndüklenmiş","sinterlenmiş","begenmiş","alinmiş","kombinlenmiş","lânetlenmiş","editlenmiş","yenmiş","temperlenmiş","beyenmiş","kazanmiş","olsunmuş","emrolunmuş","lunmuş","yunmuş","özüdönmüş","söylenecek","düşünecek","öğrenecek","lenecek","düşünecek","düzenlenecek","öğrenecek","renecek","nacak","alınacak","lanacak","kazanmıştı","lenmişti","emrolunmuştu","i̇nanıyordu","kullanıyordu","tanıyordu","dayanıyordu","görünüyordu","düşünüyordu","nuyordu","düşünüyordu","ünüyordu","fransa","floransa","lufthansa","prensa","yakınsa","konsa","hansa","sansa","mensa","türkiye-fransa","türbülansa","ofansa","hiltonsa","almanya-fransa","yünsa","jinsa","ınsa","nınsa","winsa","hünsa","extensa","demansa","fıransa","advansa","tnsa","ingiltere-fransa","ambiyansa","ünsa","rönesansa","cheonsa","malpensa","densa","finanse","ense","lanse","adsense","response","defense","sübvanse","pense","intense","expense","alphonse","kompanse","mumkunse","odense","fluminense","ninse","offense","intellisense","nonsense","anse","pfsense","immense","gorunse","hernedense","danse","mightyadsense","hisense","hortense","adriaanse","süspanse","önmeli","ögrenmeli","taşınmaz","kuşkonmaz","sınmaz","alınmaz","taşınmaz","lanmaz","osanmaz","hoşlanmaz","sönmez","dönmez","bölünmez","ersönmez","dönmez","görünmez","üşenmez","sönmez","görünmez","yinmez","sönmez","kullanmak","bağlanmak","boşanmak","hazırlanmak","öğrenmek","öğrenmek","renmek","eğlenmek","lenmek","örenmek","öğrenmek","sığınmak","değinmek","deyinmek","düşünmek","erişilebilir","güvenilir","aktarabilecek"],g=["sevi","giyi","gezi","ayrı","tıka","ayrı","sarı","övü","boşa","besle","kırı","soyu","yıka","süsle","içle","besle","tara","çeki","çözü","hazırla","üzü","yıkı","yıka","kovu","sıkı","söyle","kaçı","kapa","kası","koru","sarsı","sığı","kurula","yakı","yoru","taşı","uza","takı","yala","atı","iyileş","sinirle","dövü"],{getWords:j}=r.languageProcessing;function v(i){let e=j(i).filter((i=>i.length>5));return e=e.filter((i=>!y.includes(i))),e=function(i){return i.filter((i=>g.some((e=>f.some((function(r){return!new RegExp("^"+e+r+"$").test(i)}))))))}(e),e.some((i=>f.some((e=>i.endsWith(e)))))}const{AbstractResearcher:w}=r.languageProcessing;class p extends w{constructor(i){super(i),delete this.defaultResearches.getFleschReadingScore,Object.assign(this.config,{language:"tr",passiveConstructionType:"morphological",firstWordExceptions:a,functionWords:n,transitionWords:s,twoPartTransitionWords:h,sentenceLength:o}),Object.assign(this.helpers,{getStemmer:$,isPassiveSentence:v})}}(window.yoast=window.yoast||{}).Researcher=e})();