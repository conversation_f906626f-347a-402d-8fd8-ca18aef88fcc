(()=>{"use strict";var n={d:(e,o)=>{for(var a in o)n.o(o,a)&&!n.o(e,a)&&Object.defineProperty(e,a,{enumerable:!0,get:o[a]})},o:(n,e)=>Object.prototype.hasOwnProperty.call(n,e),r:n=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(n,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(n,"__esModule",{value:!0})}},e={};n.r(e),n.d(e,{default:()=>S});const o=window.yoast.analysis,a=["ktor<PERSON>","ktorých","ktorými","ktor<PERSON>","ktorého","ktoré<PERSON>","ktorom","ktorým","ktorý","ktor<PERSON>","ktorej","ktor<PERSON>","ktor<PERSON>","lebo","keby","že","aby","alebo","keďže","kedže","lenže","ale","nakoľko","pretože","či","ak","kedy"],t=["kvôli","miesto","pre","oproti","aj","i","ani","ale","avšak","však","preto","tak","teda","hoci","aby","ako","keď","keďže","kým","pokiaľ","ohľadne","takto","tiež ","potom","takže","odtiaľ","odteraz","lebo","akonáhle","lenže","okrem","nakoľko","pokým","pretože","čiže","jednako","doteraz","dosiaľ","najmä","napríklad","napr.","napokon","predsa","určite","dodatočne","ďalej","následne","napriek","hlavne","nakoniec","medzitým","inak ","ináč","obdobne","podobne","predovšetkým","naozaj","spočiatku ","najprv","najskôr","stručne","všeobecne","samozrejme","pravdaže","doposiaľ","nielen","než","síce","pričom","až","jednak","zato","nielenže","ibaže","skôr","prv","hoc","namiesto","buďto","inakšie","hneď","kedykoľvek"],i=t.concat(["ako aj","ako i","a tak ďalej","a tak","aj tak","a jednako","a naopak","a predsa","ale jednako","ale predsa","a tým","a to","i to","jednako však","predsa však","to jest","a preto","i keby","i keď","čo aj","keby aj","pretor, aby","odvtedy čo","zatiar čo","vzhľadom na to, že","berúc do úvahy, že","napriek tomu, že","preto, aby","za účelom","za týmto účelom","skôr či neskôr ","hneď ako","len čo","pokiaľ ide o","pokiaľ nie","pokiaľ viem","po prvé","čo sa týka","až do","až na","až po","z tohto dôvodu","z toho dôvodu","no predsa","iba ak","ak nie","keď nie","tobôž nie","pravdu povediac","ako napríklad","okrem toho ","v podstate","ako je uvedené ","v každom prípade","na rozdiel od","v porovnaní s","v oboch prípadoch","stručne povedané","inými slovami","na jednej strane","na druhej strane","s týmto cieľom"]),r=function(n){let e=n;return n.forEach((o=>{(o=o.split("-")).length>0&&o.filter((e=>!n.includes(e))).length>0&&(e=e.concat(o))})),e}(["to","sa","je","si","som","a","na","že","čo","nie","v","ako","tak","ale","by","s","mi","o","tu","do","ja","sme","ste","áno","z","len","ma","už","aby","dobre","ho","keď","ak","ty","ti","za","ťa","bol","pre","sú","tam","prečo","niečo","toto","no","teraz","aj","hej","mám","byť","ich","bude","ju","takže","ten","všetko","tom","nič","vás","kde","kto","k","po","bolo","bola","ešte","vám","toho","alebo","jej","má","môj","jeho","máš","viem","vieš","mňa","tým","veľmi","mal","prosím","potom","nikdy","možno","od","nás","ani","so","povedal","chcem","neviem","mu","ďakujem","ísť","vy","naozaj","stále","teba","pretože","viac","chceš","oh","nám","ahoj","on","pred","máme","moja","musím","tie","vo","sem","môžem","dnes","budem","ktorý","niekto","práve","pane","mali","pán","poďme","všetci","pozri","myslíš","či","mať","nemôžem","asi","ktoré","keby","veľa","vždy","tiež","moje","poď","mala","stalo","trochu","my","ľudia","tomu","pri","deň","máte","tú","musíme","iba","tvoj","tento","tá","jeden","bez","chcel","mohol","veci","zo","robiť","ide","dobrý","budeš","ok","mnou","môže","viete","kým","mne","až","presne","môžeš","dosť","vážne","preto","dobré","späť","všetky","tebou","urobiť","deje","robíš","vedieť","prepáč","vďaka","však","musíš","povedala","ona","budeme","nikto","kvôli","lebo","teda","vec","nech","hneď","im","každý","svoje","než","kedy","tvoja","prepáčte","nechcem","rokov","choď","povedz","potrebujem","daj","nemám","svoju","samozrejme","raz","chce","takto","také","mohli","preč","ním","nejaké","idem","spolu","vlastne","problém","musí","žiadne","chcete","vôbec","lepšie","vidieť","môžeme","urobil","tvoje","tebe","dostať","prišiel","hovoriť","vyzerá","ktorá","dlho","kam","niekedy","von","príliš","nich","sám","celý","úplne","tej","určite","nuž","môžete","pod","fajn","váš","seba","aký","nebude","cez","niekoho","u","všetkých","aké","majú","mojej","tých","rýchlo","taký","istý","znamená","môjho","tieto","koľko","predtým","medzi","dať","tejto","čom","chcela","neho","ideme","budú","dva","tri","nebol","nejaký","svoj","podľa","mohla","nájsť","pani","vaše","budete","ľúto","pozrite","zajtra","moju","hovorí","ktorú","túto","hore","dostal","super","ďalej","naše","vedel","tomto","náš","chvíľu","dal","proste","vie","okej","jedno","pohode","dole","aspoň","vaša","nej","táto","jedna","i","oni","robí","poviem","nemôžeš","dve","ku","skôr","ktorí","radšej","zlé","páči","nami","dúfam","skoro","čože","ňou","nemá","proti","nemal","neskôr","jednu","iné","odtiaľto","nad","hovoril","prvý","vami","svojho","musíte","zdá","skvelé","dobrá","znova","koho","och","minút","sebe","nebolo","musieť","nebudem","okolo","dvaja","príde","nemôže","veľký","vidíš","prišli","tohto","ideš","tvoju","uvidíme","celé","sama","haló","čokoľvek","nemáš","musel","jedného","vrátiť","buď","potrebujeme","taká","nebola","vonku","spraviť","prišla","moc","pokiaľ","skutočne","žiadny","svojej","dám","malý","pekne","co","ňu","odísť","nechať","inak","prísť","zatiaľ","vtedy","najlepšie","sebou","celú","možné","povedali","iste","znovu","dajte","páni","predsa","dobrú","čím","istá","ach","dokonca","videli","žiť","poďte","niekde","ďalší","iný","spôsob","nemáme","toľko","ňom","nemôžeme","nový","spravil","robíte","zle","tvojej","naša","akoby","robím","pekné","pôjdem","cestu","zase","hodín","nimi","oci","oči","dá","okrem","chápem","pripravený","chceli","konečne","aká","ano","mojom","mojich","veľké","priamo","počas","nechceš","jediný","týchto","rozumiem","zdravím","môžu","urobila","mohlo","nové","robil","chcú","jednoducho","choďte","prípad","mimo","nepovedal","isté","neskoro","povedzte","tvojho","dostali","pravdepodobne","vašu","vecí","svojich","nechcel","ďalšie","ze","nejakú","týmto","vziať","dni","jo","päť","nevieš","odkiaľ","malé","mrzí","našej","ďaleko","úžasné","nemohol","nevidel","okay","obaja","všetkým","dvoch","nemali","žiadna","dní","dr.","nemala","potrebovať","zostať","ktorého","sam","rovnako","rokmi","malo","vyzeráš","potrebuješ","strane","dňa","jediná","oveľa","urobím","ó","zastaviť","vašej","nikoho","najprv","nevedel","najlepší","našich","takmer","čau","dostala","ktorých","začal","nemusíš","opäť","urobili","ktorej","aha","stačí","išiel","zlý","čase","chceme","začať","moji","niekoľko","nevie","povieš","našu","nemôžete","naspäť","pôjdeme","štyri","poznáš","sveta","robia","chlape","rozprávať","spať","pekný","veľká","nemáte","nechce","nakoniec","ono","mysli","všade","vzal","blízko","chcieť","nášho","akú","vášho","naposledy","vidíte","šiel","odkedy","neboli","vedeli","možnosť","ah","stať","každého","vnútri","hodinu","prvé","vrátim","menej","nehovor","nebudeš","čakať","urob","žiaden","um","odtiaľ","malá","musela","nejako","okamžite","mojou","poslal","prvá","záleží","iného","ne","ha","skvelý","čoho","š","new","šťastie","jedlo","zmysel","čoskoro","snažím","nechajte","you","treba","hodiny","ocko","dala","yeah","nechal","zomrel","pracovať","madam","priatelia","časť","ruku","počuť","telefón","krv","zem","chyba","mesta","správy","práci","charlie","láska","mesto","jack","strach","volám","školy","kamoš","našla","neboj","tvár","počula","syna","zavolať","zemi","rodinu","pamätáš","polícia","roku","odišiel","párty","verím","nerob","skutočnosti","meste","zbrane","dali","auta","cesty","uveriť","zistiť","chlapče","dcéra","pána","tou","zavolám","dievčatá","volal","vypadni","myslieť","šťastný","radi","chlapík","hovorila","tím","hlavy","nejde","cesta","jasne","peňazí","muži","vrátil","škole","pol","hovoríte","večeru","sex","miesta","druhý","odo","snažil","michael","sľubujem","hovoria","šéf","žije","zachrániť","šesť","nikomu","rovno","dostane","dolu","musia","výborne","posledný","vezmi","posledné","jediné","náhodou","každú","dávno","začína","jednej","nevadí","napríklad","svojom","mesiac","dostanem","zobrať","tvojom","zabudol","môcť","často","existuje","dostaneme","povie","celá","druhej","mimochodom","žiadnu","pôjde","nejaká","snáď","nechcela","mesiacov","ostatní","navždy","desať","museli","urobíme","horšie","keďže","sami","najskôr","robíme","všetkom","pozrime","hovorili","tvojich","vezmem","zober","nedá","trošku","chvíľku","ktorým","nemu","mojím","lepší","dáš","sto","dvadsať","devätnásť","osemnásť","sedemnásť","šestnásť","dvakrát","pätnásť","štrnásť","trinásty","trinásť","dvanásty","dvanásť","jedenásty","jedenásť","desiaty","deviaty","deväť","ôsmy","osem","siedmy","sedem","šiesty","piaty","štvrtý","tretí","tí","tými","mojim","môjmu","mojimi","nim","ony","akonáhle","kedže","hoci","lenže","nakoľko","pokým","tobôž","čiže","vedľa","napriek","nadol","oproti","plus","nahor","dvojslovné","namiesto","trojslovné","navyše","tamto","včera","nedávno","ihneď","kdekoľvek","nikde","celkom","tvrdo","pomaly","opatrne","ťažko","sotva","väčšinou","absolútne","spoločne","osamote","zvyčajne","príležitostne","zriedka","tisíc","milión"].concat(t)),s=[["buď, buď"],["buď, alebo"],["ani, ani"],["aj, aj"],["tak, ako"],["nielenže, lež aj"],["nielen, lež aj"],["nielen, lež i"],["nielenže, lež ai"],["či, alebo"],["i, i"],["nielen, ale i"],["síce, ale"]],d=["nejaký","nejaká","nejaké","jeden","jedna","jediný","dva","dvaja","dve","tri","trojka","traja","štyri ","štvoro","štyria","päť","pät","šesť","sedem","osem","deväť","desať","sto","tisíc","ten","tá","to","tento","táto","toto","tamten","tamtá","tamto","tí","tie","tieto","toho","tej","tomu","tú","tom","tým","tou","tých","tými","títo","tamtí","tamtie","tamtoho","tomuto","tohto"],{getWords:v}=o.languageProcessing,l=["letá","skriptá","dvojitá","autá","kráľovná","princezná","príbuzná","premenná","trstená","zelená","ošípaná","lesná","vyvolená","dobšiná","hádzaná","gazdiná","šrotovná","švagriná","výborná","záverečná","recepčná","konečná","dotyčná","černá","jediný","posledný","ostatný","neposledný","predposledný","štvrtý","dvojitý","postihnutý","svätý","zlotý","dôležitý","istý","určitý","svätý","bohatý","čistý","zlatý","častý","postihnutý","zložitý","okolitý","žltý","dohodnutý","skrytý","hustý","okamžitý","zvyknutý","krutý","zahrnutý","vzniknutý","vyvinutý","dotknutý","rozhodnutý","rozmanitý","rozvinutý","pokrytý","krytý","opitý","tekutý","spätý","neistý","prostý","nepretržitý","osobitý","prevzatý","jedovatý","zapnutý","ukradnutý","mletý","ženatý","sprostý","trávnatý","uhličitý","maloletý","nevyužitý","prežitý","skalnatý","ponúknutý","rozbehnutý","vydatý","náležitý","napätý","pustý","prenajatý","zvládnutý","vypnutý","pracovitý","zasiahnutý","neurčitý","piesočnatý","šitý","šťavnatý","zamrznutý","posadnutý","posunutý","urcitý","listnatý","guľatý","nečistý","dutý","ihličnatý","chlpatý","nápaditý","zaujatý","nedotknutý","členitý","presunutý","menovitý","hranatý","odobratý","zamknutý","zdvihnutý","natiahnutý","rovinatý","zabehnutý","novovzniknutý","potiahnutý","odtrhnutý","hornatý","zamietnutý","vyzretý","opretý","kamenistý","kľukatý","svalnatý","zarytý","prehratý","zajatý","rozpačitý","pohnutý","rozkvitnutý","stojatý","húževnatý","zlatistý","opuchnutý","hmlistý","prekrytý","vychudnutý","napnutý","plnoletý","odumretý","očitý","dojatý","strapatý","korenistý","stuhnutý","ostnatý","neplnoletý","odetý","zákonitý","vyňatý","vyschnutý","obutý","ohnutý","vlnitý","nafúknutý","zapadnutý","vystretý","mäsitý","svedomitý","spadnutý","vytiahnutý","špicatý","znamenitý","nepoužitý","ostražitý","tretý","nekrytý","uzamknutý","tienistý","zovretý","nultý","tlstý","rázovitý","ľudnatý","pospolitý","hlasitý","vychladnutý","rozpadnutý","odňatý","pritiahnutý","nedožitý","klenutý","pretiahnutý","podlhovastý","dvojitý","zaťatý","podnapitý","prasknutý","prikrytý","padnutý","vypätý","podčiarknutý","roztiahnutý","svatý","mrzutý","kopcovitý","svahovitý","guľovitý","zásaditý","bradatý","zmrznutý","zubatý","pomletý","zaniknutý","zažitý","piesčitý","zahnutý","nasiaknutý","zhnitý","iný","posledný","jediný","vlastný","hlavný","pekný","povinný","určený","vhodný","schopný","plný","samotný","silný","pripravený","voľný","podobný","spokojný","pracovný","súčasný","presvedčený","uvedený","medzinárodný","osobný","spoločný","daný","národný","základný","úspešný","potrebný","neregistrovaný","rodinný","kvalitný","finančný","zodpovedný","šťastný","skutočný","pôvodný","dnešný","otvorený","zameraný","príjemný","ročný","bežný","životný","výborný","možný","stavebný","ochotný","významný","zdravotný","vnútorný","obyčajný","hudobný","duchovný","presný","jasný","verejný","vybavený","príslušný","priemerný","červený","výrazný","samostatný","spojený","odborný","výkonný","trestný","umiestnený","moderný","schválený","obchodný","cestovný","informačný","spomínaný","vytvorený","nádherný","dostatočný","oprávnený","mobilný","zelený","náročný","úžasný","obľúbený","jedinečný","prirodzený","prítomný","obecný","slušný","kompletný","prekvapený","dostupný","operačný","pevný","večný","dolný","zvýšený","výnimočný","krvný","stanovený","súkromný","konečný","vianočný","vážený","ústavný","úplný","obmedzený","považovaný","skúsený","platný","slobodný","vyrobený","tradičný","nebezpečný","verný","vodný","všeobecný","smutný","dopravný","letný","mesačný","prírodný","drevený","osobitný","komplexný","nočný","vzdialený","denný","farebný","okresný","študijný","účinný","volebný","policajný","používaný","jemný","záverečný","sklamaný","unavený","menovaný","pokojný","zaradený","rozšírený","poškodený","odlišný","pravidelný","poverený","rozdelený","bezpečný","územný","zahraničný","slnečný","nepríjemný","horný","jednotný","zásadný","inteligentný","opačný","zimný","dotyčný","vďačný","víťazný","stručný","každodenný","slávnostný","podrobný","imunitný","stredný","značný","akčný","šikovný","prístupný","výsledný","funkčný","tohtoročný","nevyhnutný","orientovaný","ostatný","nadšený","bezpečnostný","studený","štandardný","zverejnený","situovaný","plánovaný","ochranný","podstatný","dlhoročný","perfektný","cirkevný","takzvaný","zložený","nevhodný","úprimný","stolný","stabilný","požadovaný","čestný","anonymný","lacný","reklamný","úvodný","kontrolný","nasledovný","výrobný","zábavný","viditeľný","divadelný","písomný","predpokladaný","medziročný","lesný","odolný","registrovaný","prípadný","nešťastný","jednoznačný","spôsobený","chudobný","udržateľný","luxusný","zadný","rozumný","tanečný","organizačný","drobný","zranený","zasvätený","rovný","užitočný","investičný","milovaný","hodný","hladný","bezplatný","pripojený","nekonečný","zemný","elegantný","pomocný","zbytočný","priemyselný","pohodlný","obvodný","mocný","pitný","oblečený","neobmedzený","strávený","plnohodnotný","rodný","vzájomný","prípravný","zaznamenaný","kamenný","kompaktný","vstupný","zabudovaný","peňažný","skromný","mohutný","externý","výskumný","ohrozený","predný","reprezentačný","primeraný","herný","výhodný","strašný","ný","polovičný","nezabudnuteľný","invalidný","narodený","cenný","následný","opatrný","ocenený","ústredný","sprievodný","svadobný","prepracovaný","neuveriteľný","zákonný","variabilný","využívaný","zariadený","napojený","strieborný","nazvaný","tajný","komunikačný","novotný","západný","zachovaný","nenávratný","vzdelaný","kladný","poistený","dobrovoľný","ucelený","označovaný","komerčný","vydarený","dočasný","prihlásený","hrozný","vtipný","chladný","kontaktný","komplikovaný","znížený","záväzný","jarný","večerný","odvodený","pohlavný","obklopený","zamestnaný","dôstojný","odkázaný","liečebný","netradičný","celodenný","naivný","riadený","severný","falošný","náhodný","južný","prechodný","talentovaný","závažný","dvojnásobný","jubilejný","nominovaný","nedostatočný","telekomunikačný","nainštalovaný","použiteľný","dodávaný","súťažný","prispôsobený","pripravovaný","nespokojný","získaný","tajomný","financovaný","ponúkaný","výtvarný","svetelný","zmenený","položený","vykonaný","zmluvný","vyvážený","vysvätený","potvrdený","sprevádzaný","limitovaný","služobný","postupný","podporený","registračný","vrchný","nezamestnaný","obytný","zostavený","vyplnený","príbuzný","prezentovaný","duševný","podaný","nevinný","tepelný","priestranný","ľahostajný","pridelený","náučný","zaručený","pozoruhodný","rekordný","zaslaný","nahnevaný","platený","ľubovoľný","platobný","navrhovaný","volený","ozajstný","podporovaný","úradný","pozorný","záručný","predčasný","týždenný","prepojený","nutný","popredný","vymenovaný","pilotný","požehnaný","kombinovaný","redakčný","zubný","telesný","minuloročný","ranný","temný","realitný","vyriešený","neschopný","zázračný","revolučný","interný","parný","bočný","zbavený","cestný","čudný","urobený","šialený","animovaný","veľkonočný","nudný","predbežný","oslobodený","divný","hraničný","prenosný","kvalifikovaný","prvotný","motivačný","obžalovaný","záhradný","odovzdaný","podmienený","východný","dominantný","spätný","nákladný","nenápadný","jesenný","výchovný","predmetný","detailný","drsný","špecializovaný","obranný","prehľadný","vyhradený","pyšný","spustený","podpivničený","osadený","nečakaný","porovnateľný","prepustený","nadriadený","povolaný","ovocný","výmenný","vyčerpaný","obnovený","písaný","overený","konkurenčný","kompatibilný","neúspešný","starobný","konverzný","záchranný","totožný","zastúpený","kompetentný","spodný","obohatený","chutný","firemný","relevantný","navigačný","uvoľnený","nasadený","vyjadrený","čiastočný","pamätný","posvätný","uznaný","pomenovaný","očný","kontroverzný","flexibilný","všestranný","neskutočný","zapojený","kladený","spotrebný","oddelený","nákupný","prijateľný","vyvolený","vinný","vyrábaný","nájdený","rekreačný","chybný","nemenovaný","tučný","neviditeľný","poradný","skalný","celovečerný","naladený","zateplený","hmotný","colný","zamilovaný","polyfunkčný","knižný","podriadený","hraný","vymedzený","nastaviteľný","nedeľný","priložený","odstránený","uzatvorený","renesančný","pružný","regulačný","poháňaný","vstavaný","hodnotný","splnený","vzdušný","putovný","zatvorený","porazený","vytúžený","skrátený","stíhaný","knižničný","realizačný","zmiešaný","pokrstený","aplikovaný","motivovaný","testovaný","vnímaný","milosrdný","úsporný","vítaný","čarovný","zaslúžený","nezvyčajný","pokorný","neopakovateľný","protimonopolný","učebný","odhodlaný","nádejný","povestný","železničný","podporný","obsadený","zmätený","výstižný","oboznámený","skúšobný","nadmerný","ozbrojený","rodený","čitateľný","opozičný","železný","orientačný","zavraždený","zhodný","kvalifikačný","ukrižovaný","autorizovaný","ladený","odporúčaný","oddaný","ohromný","znechutený","šokovaný","predajný","nenáročný","smrteľný","činný","uväznený","objavený","sledovaný","nosný","vecný","arogantný","hradný","zdatný","vymyslený","ohraničený","počiatočný","zanedbateľný","radostný","zrozumiteľný","hybridný","usporiadaný","multifunkčný","univerzitný","palubný","naklonený","zadaný","predposledný","narušený","naozajstný","spasený","udržiavaný","zabalený","komorný","spáchaný","stabilizačný","záhadný","osamotený","stavaný","dobrodružný","nakrútený","izolovaný","zaťažený","žiadaný","reklamačný","sviatočný","premyslený","vyhotovený","bezprostredný","údajný","korektný","dvojročný","krstný","obdobný","vyvolaný","nezmenený","koncipovaný","dodatočný","opísaný","žitný","odoslaný","zachytený","všedný","evidovaný","jazdný","černý","vylepšený","zaplatený","porušený","nevšedný","odporný","prospešný","opakovaný","trojročný","prenesený","slovný","prerobený","charakterizovaný","začarovaný","sobotný","oplotený","transparentný","uskutočnený","certifikovaný","nekompromisný","hromadný","murovaný","toaletný","varovný","diaľničný","zavesený","neplatný","hnusný","zlomený","pokazený","opravný","kúpeľný","nejasný","zaľúbený","akceptovaný","servisný","sústredený","kožený","výstavný","nápomocný","bezmocný","mravný","zaskočený","kľudný","enormný","predaný","rastlinný","nepatrný","odhalený","spisovný","preplnený","oslabený","žalovaný","útočný","nadaný","smädný","voliteľný","satelitný","účtovný","záložný","brušný","predvolebný","pravdepodobný","benefičný","relaxačný","nižný","diskusný","vyslaný","komfortný","povýšený","zaužívaný","renomovaný","zaistený","vzkriesený","výstupný","poslušný","nežný","osobnostný","hľadaný","predurčený","nezmyselný","predvedený","poistný","upozornený","poľný","úložný","referenčný","robustný","nenahraditeľný","zhubný","bezchybný","dvojpodlažný","vyšný","nájomný","predkladaný","neškodný","celoročný","stabilizovaný","nefunkčný","záporný","súhrnný","opätovný","vznešený","zasadený","celoživotný","tolerantný","statočný","zmysluplný","sprístupnený","odmenený","textilný","zhotovený","strešný","konštantný","priznaný","vyradený","bojovný","vyvíjaný","zakopaný","permanentný","nevídaný","koaličný","odložený","teplotný","priebežný","rovnocenný","pripútaný","uvádzaný","obrátený","zreteľný","adresovaný","vymastený","kultivovaný","dlžný","vytlačený","blahoslavený","zverený","umožnený","percentný","prenasledovaný","zjavný","pozáručný","zaneprázdnený","chápaný","ubytovaný","nerozhodný","neautorizovaný","prerokovaný","vypredaný","vyzvaný","približný","nemožný","operný","zjednodušený","prezývaný","vyznačený","zvyšný","recyklačný","komunitný","trojnásobný","excelentný","zablokovaný","koncentrovaný","stlačený","jednosmerný","posilnený","jednostranný","neobyčajný","vymenený","totalitný","kúpený","garantovaný","zadržaný","neprijateľný","zrealizovaný","želaný","dôsledný","rýchlostný","robený","ručný","využiteľný","zachránený","nerušený","parlamentný","dedičný","predávaný","vysnívaný","vysielaný","rekonštruovaný","útulný","doživotný","ropný","propagačný","poučený","mastný","koncertný","aktivovaný","zberný","prerušený","otočený","civilný","šetrný"],{values:p}=o.languageProcessing,{Clause:u}=p,{getClausesSplitOnStopWords:m,createRegexFromArray:k}=o.languageProcessing,c={Clause:class extends u{constructor(n,e){super(n,e),this._participles=function(n){const e=v(n),o=new RegExp("(ný|ní|tý|ná|tá|né|té)$");return e.filter((n=>o.test(n)))}(this.getClauseText()),this.checkParticiples()}checkParticiples(){const n=this.getParticiples().filter((n=>!l.includes(n)));this.setPassive(n.length>0)}},regexes:{auxiliaryRegex:k(["byť","som","si","je","sme","ste","sú","bol","bola","boli","bolo","budem","budeš","bude","budeme","budete","budú"]),stopCharacterRegex:/([:,])(?=[ \n\r\t'"+\-»«‹›<>])/gi,stopwordRegex:k(a)}};function h(n){return m(n,c)}const z=window.lodash;function b(n,e){const o=e.externalStemmer.palatalEndingsRegexes.find((e=>new RegExp(e[0]).test(n)));return o?n.replace(new RegExp(o[0]),o[1]):n.slice(0,-1)}const j=function(n,e){for(const o of e)if(o[1].includes(n))return o[0];return null},f=function(n,e){for(const o of e)if(o.includes(n))return o[0];return null};const{baseStemmer:y}=o.languageProcessing;function g(n){const e=(0,z.get)(n.getData("morphology"),"sk",!1);return e?n=>function(n,e){const o=j(n,e.exceptionLists.exceptionStemsWithFullForms);return o||(n=function(n,e){const o=e.externalStemmer.caseSuffixes,a=e.externalStemmer.caseRegexes;if(n.length>7&&n.endsWith(o.caseSuffix1))return n.slice(0,-5);if(n.length>6&&n.endsWith(o.caseSuffix2))return b(n.slice(0,-3),e);if(n.length>5){if(o.caseSuffixes3.includes(n.slice(-3)))return b(n.slice(0,-2),e);if(o.caseSuffixes4.includes(n.slice(-3)))return n.slice(0,-3)}if(n.length>4){if(n.endsWith(o.caseSuffix5))return b(n.slice(0,-1),e);if(o.caseSuffixes6.includes(n.slice(-2)))return b(n.slice(0,-2),e);if(o.caseSuffixes7.includes(n.slice(-2)))return n.slice(0,-2)}if(n.length>3){if(new RegExp(a.caseRegex1).test(n))return b(n,e);if(new RegExp(a.caseRegex2).test(n))return n.slice(0,-1)}return n}(n,e),n=function(n,e){const o=e.externalStemmer.possessiveSuffixes;if(n.length>5){if(n.endsWith(o.posSuffixOv))return n.slice(0,-2);if(n.endsWith(o.posSuffixIn))return b(n.slice(0,-1),e)}return n}(n,e),n=function(n,e){const o=e.externalStemmer.superlativePrefix;return n.length>6&&n.startsWith(o)&&(n=n.slice(3,n.length)),n.length>5&&e.externalStemmer.comparativeSuffixes.includes(n.slice(-3))&&(n=b(n.slice(0,-2),e)),n}(n,e),n=function(n,e){const o=e.externalStemmer.diminutiveSuffixes;if(n.length>7&&n.endsWith(o.diminutiveSuffix1))return n.slice(0,-5);if(n.length>6){if(o.diminutiveSuffixes2.includes(n.slice(-4)))return b(n.slice(0,-3),e);if(o.diminutiveSuffixes3.includes(n.slice(-4)))return b(n.slice(0,-4),e)}if(n.length>5){if(o.diminutiveSuffixes4.includes(n.slice(-3)))return b(n.slice(0,-3),e);if(o.diminutiveSuffixes5.includes(n.slice(-3)))return n.slice(0,-3)}if(n.length>4){if(o.diminutiveSuffixes6.includes(n.slice(-2)))return b(n.slice(0,-1),e);if(o.diminutiveSuffixes7.includes(n.slice(-2)))return n.slice(0,-1)}return n.length>3&&n.endsWith("k")&&!n.endsWith("isk")?n.slice(0,-1):n}(n,e),n=function(n,e){const o=e.externalStemmer.augmentativeSuffixes;return n.length>6&&n.endsWith(o.augmentativeSuffix1)?n.slice(0,-4):n.length>5&&o.augmentativeSuffixes2.includes(n.slice(-3))?b(n.slice(0,-2),e):n}(n,e),n=function(n,e){const o=e.externalStemmer.derivationalSuffixes;if(n.length>8&&n.endsWith(o.derivationalSuffix1))return n.slice(0,-6);if(n.length>7){if(n.endsWith(o.derivationalSuffix2))return b(n.slice(0,-4),e);if(o.derivationalSuffixes3.includes(n.slice(-5)))return n.slice(0,-5)}if(n.length>6){if(o.derivationalSuffixes4.includes(n.slice(-4)))return n.slice(0,-4);if(o.derivationalSuffixes5.includes(n.slice(-4)))return b(n.slice(0,-3),e)}if(n.length>5){if(n.endsWith(o.derivationalSuffix6))return n.slice(0,-3);if(o.derivationalSuffixes7.includes(n.slice(-3)))return b(n.slice(0,-2),e);if(o.derivationalSuffixes8.includes(n.slice(-3)))return n.slice(0,-3)}if(n.length>4){if(o.derivationalSuffixes9.includes(n.slice(-2)))return n.slice(0,-2);if(o.derivationalSuffixes10.includes(n.slice(-2)))return b(n.slice(0,-1),e)}const a=new RegExp(e.externalStemmer.derivationalRegex);return n.length>3&&a.test(n)?n.slice(0,-1):n}(n,e),f(n,e.exceptionLists.stemsThatBelongToOneWord)||n)}(n,e):y}const{AbstractResearcher:x}=o.languageProcessing;class S extends x{constructor(n){super(n),delete this.defaultResearches.getFleschReadingScore,Object.assign(this.config,{language:"sk",passiveConstructionType:"periphrastic",stopWords:a,functionWords:r,transitionWords:i,twoPartTransitionWords:s,firstWordExceptions:d}),Object.assign(this.helpers,{getClauses:h,getStemmer:g})}}(window.yoast=window.yoast||{}).Researcher=e})();