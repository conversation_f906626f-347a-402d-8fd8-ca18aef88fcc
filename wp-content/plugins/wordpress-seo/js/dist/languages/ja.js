(()=>{var e={771:e=>{function t(){var e={"[一二三四五六七八九十百千万億兆]":"M","[一-龠々〆ヵヶ]":"H","[ぁ-ん]":"I","[ァ-ヴーｱ-ﾝﾞｰ]":"K","[a-zA-Zａ-ｚＡ-Ｚ]":"A","[0-9０-９]":"N"};for(var t in this.chartype_=[],e){var r=new RegExp;r.compile(t),this.chartype_.push([r,e[t]])}return this.BIAS__=-332,this.BC1__={HH:6,II:2461,KH:406,OH:-1378},this.BC2__={AA:-3267,AI:2744,AN:-878,HH:-4070,HM:-1711,HN:4012,HO:3761,IA:1327,IH:-1184,II:-1332,IK:1721,IO:5492,KI:3831,KK:-8741,MH:-3132,MK:3334,OO:-2920},this.BC3__={HH:996,HI:626,HK:-721,HN:-1307,HO:-836,IH:-301,KK:2762,MK:1079,MM:4034,OA:-1652,OH:266},this.BP1__={BB:295,OB:304,OO:-125,UB:352},this.BP2__={BO:60,OO:-1762},this.BQ1__={BHH:1150,BHM:1521,BII:-1158,BIM:886,BMH:1208,BNH:449,BOH:-91,BOO:-2597,OHI:451,OIH:-296,OKA:1851,OKH:-1020,OKK:904,OOO:2965},this.BQ2__={BHH:118,BHI:-1159,BHM:466,BIH:-919,BKK:-1720,BKO:864,OHH:-1139,OHM:-181,OIH:153,UHI:-1146},this.BQ3__={BHH:-792,BHI:2664,BII:-299,BKI:419,BMH:937,BMM:8335,BNN:998,BOH:775,OHH:2174,OHM:439,OII:280,OKH:1798,OKI:-793,OKO:-2242,OMH:-2402,OOO:11699},this.BQ4__={BHH:-3895,BIH:3761,BII:-4654,BIK:1348,BKK:-1806,BMI:-3385,BOO:-12396,OAH:926,OHH:266,OHK:-2036,ONN:-973},this.BW1__={",と":660,",同":727,B1あ:1404,B1同:542,"、と":660,"、同":727,"」と":1682,あっ:1505,いう:1743,いっ:-2055,いる:672,うし:-4817,うん:665,から:3472,がら:600,こう:-790,こと:2083,こん:-1262,さら:-4143,さん:4573,した:2641,して:1104,すで:-3399,そこ:1977,それ:-871,たち:1122,ため:601,った:3463,つい:-802,てい:805,てき:1249,でき:1127,です:3445,では:844,とい:-4915,とみ:1922,どこ:3887,ない:5713,なっ:3015,など:7379,なん:-1113,にし:2468,には:1498,にも:1671,に対:-912,の一:-501,の中:741,ませ:2448,まで:1711,まま:2600,まる:-2155,やむ:-1947,よっ:-2565,れた:2369,れで:-913,をし:1860,を見:731,亡く:-1886,京都:2558,取り:-2784,大き:-2604,大阪:1497,平方:-2314,引き:-1336,日本:-195,本当:-2423,毎日:-2113,目指:-724,Ｂ１あ:1404,Ｂ１同:542,"｣と":1682},this.BW2__={"..":-11822,11:-669,"――":-5730,"−−":-13175,いう:-1609,うか:2490,かし:-1350,かも:-602,から:-7194,かれ:4612,がい:853,がら:-3198,きた:1941,くな:-1597,こと:-8392,この:-4193,させ:4533,され:13168,さん:-3977,しい:-1819,しか:-545,した:5078,して:972,しな:939,その:-3744,たい:-1253,たた:-662,ただ:-3857,たち:-786,たと:1224,たは:-939,った:4589,って:1647,っと:-2094,てい:6144,てき:3640,てく:2551,ては:-3110,ても:-3065,でい:2666,でき:-1528,でし:-3828,です:-4761,でも:-4203,とい:1890,とこ:-1746,とと:-2279,との:720,とみ:5168,とも:-3941,ない:-2488,なが:-1313,など:-6509,なの:2614,なん:3099,にお:-1615,にし:2748,にな:2454,によ:-7236,に対:-14943,に従:-4688,に関:-11388,のか:2093,ので:-7059,のに:-6041,のの:-6125,はい:1073,はが:-1033,はず:-2532,ばれ:1813,まし:-1316,まで:-6621,まれ:5409,めて:-3153,もい:2230,もの:-10713,らか:-944,らし:-1611,らに:-1897,りし:651,りま:1620,れた:4270,れて:849,れば:4114,ろう:6067,われ:7901,を通:-11877,んだ:728,んな:-4115,一人:602,一方:-1375,一日:970,一部:-1051,上が:-4479,会社:-1116,出て:2163,分の:-7758,同党:970,同日:-913,大阪:-2471,委員:-1250,少な:-1050,年度:-8669,年間:-1626,府県:-2363,手権:-1982,新聞:-4066,日新:-722,日本:-7068,日米:3372,曜日:-601,朝鮮:-2355,本人:-2697,東京:-1543,然と:-1384,社会:-1276,立て:-990,第に:-1612,米国:-4268,"１１":-669},this.BW3__={あた:-2194,あり:719,ある:3846,"い.":-1185,"い。":-1185,いい:5308,いえ:2079,いく:3029,いた:2056,いっ:1883,いる:5600,いわ:1527,うち:1117,うと:4798,えと:1454,"か.":2857,"か。":2857,かけ:-743,かっ:-4098,かに:-669,から:6520,かり:-2670,"が,":1816,"が、":1816,がき:-4855,がけ:-1127,がっ:-913,がら:-4977,がり:-2064,きた:1645,けど:1374,こと:7397,この:1542,ころ:-2757,さい:-714,さを:976,"し,":1557,"し、":1557,しい:-3714,した:3562,して:1449,しな:2608,しま:1200,"す.":-1310,"す。":-1310,する:6521,"ず,":3426,"ず、":3426,ずに:841,そう:428,"た.":8875,"た。":8875,たい:-594,たの:812,たり:-1183,たる:-853,"だ.":4098,"だ。":4098,だっ:1004,った:-4748,って:300,てい:6240,てお:855,ても:302,です:1437,でに:-1482,では:2295,とう:-1387,とし:2266,との:541,とも:-3543,どう:4664,ない:1796,なく:-903,など:2135,"に,":-1021,"に、":-1021,にし:1771,にな:1906,には:2644,"の,":-724,"の、":-724,の子:-1e3,"は,":1337,"は、":1337,べき:2181,まし:1113,ます:6943,まっ:-1549,まで:6154,まれ:-793,らし:1479,られ:6820,るる:3818,"れ,":854,"れ、":854,れた:1850,れて:1375,れば:-3246,れる:1091,われ:-605,んだ:606,んで:798,カ月:990,会議:860,入り:1232,大会:2217,始め:1681,市:965,新聞:-5055,"日,":974,"日、":974,社会:2024,ｶ月:990},this.TC1__={AAA:1093,HHH:1029,HHM:580,HII:998,HOH:-390,HOM:-331,IHI:1169,IOH:-142,IOI:-1015,IOM:467,MMH:187,OOI:-1832},this.TC2__={HHO:2088,HII:-1023,HMM:-1154,IHI:-1965,KKH:703,OII:-2649},this.TC3__={AAA:-294,HHH:346,HHI:-341,HII:-1088,HIK:731,HOH:-1486,IHH:128,IHI:-3041,IHO:-1935,IIH:-825,IIM:-1035,IOI:-542,KHH:-1216,KKA:491,KKH:-1217,KOK:-1009,MHH:-2694,MHM:-457,MHO:123,MMH:-471,NNH:-1689,NNO:662,OHO:-3393},this.TC4__={HHH:-203,HHI:1344,HHK:365,HHM:-122,HHN:182,HHO:669,HIH:804,HII:679,HOH:446,IHH:695,IHO:-2324,IIH:321,III:1497,IIO:656,IOO:54,KAK:4845,KKA:3386,KKK:3065,MHH:-405,MHI:201,MMH:-241,MMM:661,MOM:841},this.TQ1__={BHHH:-227,BHHI:316,BHIH:-132,BIHH:60,BIII:1595,BNHH:-744,BOHH:225,BOOO:-908,OAKK:482,OHHH:281,OHIH:249,OIHI:200,OIIH:-68},this.TQ2__={BIHH:-1401,BIII:-1033,BKAK:-543,BOOO:-5591},this.TQ3__={BHHH:478,BHHM:-1073,BHIH:222,BHII:-504,BIIH:-116,BIII:-105,BMHI:-863,BMHM:-464,BOMH:620,OHHH:346,OHHI:1729,OHII:997,OHMH:481,OIHH:623,OIIH:1344,OKAK:2792,OKHH:587,OKKA:679,OOHH:110,OOII:-685},this.TQ4__={BHHH:-721,BHHM:-3604,BHII:-966,BIIH:-607,BIII:-2181,OAAA:-2763,OAKK:180,OHHH:-294,OHHI:2446,OHHO:480,OHIH:-1573,OIHH:1935,OIHI:-493,OIIH:626,OIII:-4007,OKAK:-8156},this.TW1__={につい:-4681,東京都:2026},this.TW2__={ある程:-2049,いった:-1256,ころが:-2434,しょう:3873,その後:-4430,だって:-1049,ていた:1833,として:-4657,ともに:-4517,もので:1882,一気に:-792,初めて:-1512,同時に:-8097,大きな:-1255,対して:-2721,社会党:-3216},this.TW3__={いただ:-1734,してい:1314,として:-4314,につい:-5483,にとっ:-5989,に当た:-6247,"ので,":-727,"ので、":-727,のもの:-600,れから:-3752,十二月:-2287},this.TW4__={"いう.":8576,"いう。":8576,からな:-2348,してい:2958,"たが,":1516,"たが、":1516,ている:1538,という:1349,ました:5543,ません:1097,ようと:-4258,よると:5865},this.UC1__={A:484,K:93,M:645,O:-505},this.UC2__={A:819,H:1059,I:409,M:3987,N:5775,O:646},this.UC3__={A:-1370,I:2311},this.UC4__={A:-2643,H:1809,I:-1032,K:-3450,M:3565,N:3876,O:6646},this.UC5__={H:313,I:-1238,K:-799,M:539,O:-831},this.UC6__={H:-506,I:-253,K:87,M:247,O:-387},this.UP1__={O:-214},this.UP2__={B:69,O:935},this.UP3__={B:189},this.UQ1__={BH:21,BI:-12,BK:-99,BN:142,BO:-56,OH:-95,OI:477,OK:410,OO:-2422},this.UQ2__={BH:216,BI:113,OK:1759},this.UQ3__={BA:-479,BH:42,BI:1913,BK:-7198,BM:3160,BN:6427,BO:14761,OI:-827,ON:-3212},this.UW1__={",":156,"、":156,"「":-463,あ:-941,う:-127,が:-553,き:121,こ:505,で:-201,と:-547,ど:-123,に:-789,の:-185,は:-847,も:-466,や:-470,よ:182,ら:-292,り:208,れ:169,を:-446,ん:-137,"・":-135,主:-402,京:-268,区:-912,午:871,国:-460,大:561,委:729,市:-411,日:-141,理:361,生:-408,県:-386,都:-718,"｢":-463,"･":-135},this.UW2__={",":-829,"、":-829,〇:892,"「":-645,"」":3145,あ:-538,い:505,う:134,お:-502,か:1454,が:-856,く:-412,こ:1141,さ:878,ざ:540,し:1529,す:-675,せ:300,そ:-1011,た:188,だ:1837,つ:-949,て:-291,で:-268,と:-981,ど:1273,な:1063,に:-1764,の:130,は:-409,ひ:-1273,べ:1261,ま:600,も:-1263,や:-402,よ:1639,り:-579,る:-694,れ:571,を:-2516,ん:2095,ア:-587,カ:306,キ:568,ッ:831,三:-758,不:-2150,世:-302,中:-968,主:-861,事:492,人:-123,会:978,保:362,入:548,初:-3025,副:-1566,北:-3414,区:-422,大:-1769,天:-865,太:-483,子:-1519,学:760,実:1023,小:-2009,市:-813,年:-1060,強:1067,手:-1519,揺:-1033,政:1522,文:-1355,新:-1682,日:-1815,明:-1462,最:-630,朝:-1843,本:-1650,東:-931,果:-665,次:-2378,民:-180,気:-1740,理:752,発:529,目:-1584,相:-242,県:-1165,立:-763,第:810,米:509,自:-1353,行:838,西:-744,見:-3874,調:1010,議:1198,込:3041,開:1758,間:-1257,"｢":-645,"｣":3145,ｯ:831,ｱ:-587,ｶ:306,ｷ:568},this.UW3__={",":4889,1:-800,"−":-1723,"、":4889,々:-2311,〇:5827,"」":2670,"〓":-3573,あ:-2696,い:1006,う:2342,え:1983,お:-4864,か:-1163,が:3271,く:1004,け:388,げ:401,こ:-3552,ご:-3116,さ:-1058,し:-395,す:584,せ:3685,そ:-5228,た:842,ち:-521,っ:-1444,つ:-1081,て:6167,で:2318,と:1691,ど:-899,な:-2788,に:2745,の:4056,は:4555,ひ:-2171,ふ:-1798,へ:1199,ほ:-5516,ま:-4384,み:-120,め:1205,も:2323,や:-788,よ:-202,ら:727,り:649,る:5905,れ:2773,わ:-1207,を:6620,ん:-518,ア:551,グ:1319,ス:874,ッ:-1350,ト:521,ム:1109,ル:1591,ロ:2201,ン:278,"・":-3794,一:-1619,下:-1759,世:-2087,両:3815,中:653,主:-758,予:-1193,二:974,人:2742,今:792,他:1889,以:-1368,低:811,何:4265,作:-361,保:-2439,元:4858,党:3593,全:1574,公:-3030,六:755,共:-1880,円:5807,再:3095,分:457,初:2475,別:1129,前:2286,副:4437,力:365,動:-949,務:-1872,化:1327,北:-1038,区:4646,千:-2309,午:-783,協:-1006,口:483,右:1233,各:3588,合:-241,同:3906,和:-837,員:4513,国:642,型:1389,場:1219,外:-241,妻:2016,学:-1356,安:-423,実:-1008,家:1078,小:-513,少:-3102,州:1155,市:3197,平:-1804,年:2416,広:-1030,府:1605,度:1452,建:-2352,当:-3885,得:1905,思:-1291,性:1822,戸:-488,指:-3973,政:-2013,教:-1479,数:3222,文:-1489,新:1764,日:2099,旧:5792,昨:-661,時:-1248,曜:-951,最:-937,月:4125,期:360,李:3094,村:364,東:-805,核:5156,森:2438,業:484,氏:2613,民:-1694,決:-1073,法:1868,海:-495,無:979,物:461,特:-3850,生:-273,用:914,町:1215,的:7313,直:-1835,省:792,県:6293,知:-1528,私:4231,税:401,立:-960,第:1201,米:7767,系:3066,約:3663,級:1384,統:-4229,総:1163,線:1255,者:6457,能:725,自:-2869,英:785,見:1044,調:-562,財:-733,費:1777,車:1835,軍:1375,込:-1504,通:-1136,選:-681,郎:1026,郡:4404,部:1200,金:2163,長:421,開:-1432,間:1302,関:-1282,雨:2009,電:-1045,非:2066,駅:1620,"１":-800,"｣":2670,"･":-3794,ｯ:-1350,ｱ:551,ｸﾞ:1319,ｽ:874,ﾄ:521,ﾑ:1109,ﾙ:1591,ﾛ:2201,ﾝ:278},this.UW4__={",":3930,".":3508,"―":-4841,"、":3930,"。":3508,〇:4999,"「":1895,"」":3798,"〓":-5156,あ:4752,い:-3435,う:-640,え:-2514,お:2405,か:530,が:6006,き:-4482,ぎ:-3821,く:-3788,け:-4376,げ:-4734,こ:2255,ご:1979,さ:2864,し:-843,じ:-2506,す:-731,ず:1251,せ:181,そ:4091,た:5034,だ:5408,ち:-3654,っ:-5882,つ:-1659,て:3994,で:7410,と:4547,な:5433,に:6499,ぬ:1853,ね:1413,の:7396,は:8578,ば:1940,ひ:4249,び:-4134,ふ:1345,へ:6665,べ:-744,ほ:1464,ま:1051,み:-2082,む:-882,め:-5046,も:4169,ゃ:-2666,や:2795,ょ:-1544,よ:3351,ら:-2922,り:-9726,る:-14896,れ:-2613,ろ:-4570,わ:-1783,を:13150,ん:-2352,カ:2145,コ:1789,セ:1287,ッ:-724,ト:-403,メ:-1635,ラ:-881,リ:-541,ル:-856,ン:-3637,"・":-4371,ー:-11870,一:-2069,中:2210,予:782,事:-190,井:-1768,人:1036,以:544,会:950,体:-1286,作:530,側:4292,先:601,党:-2006,共:-1212,内:584,円:788,初:1347,前:1623,副:3879,力:-302,動:-740,務:-2715,化:776,区:4517,協:1013,参:1555,合:-1834,和:-681,員:-910,器:-851,回:1500,国:-619,園:-1200,地:866,場:-1410,塁:-2094,士:-1413,多:1067,大:571,子:-4802,学:-1397,定:-1057,寺:-809,小:1910,屋:-1328,山:-1500,島:-2056,川:-2667,市:2771,年:374,庁:-4556,後:456,性:553,感:916,所:-1566,支:856,改:787,政:2182,教:704,文:522,方:-856,日:1798,時:1829,最:845,月:-9066,木:-485,来:-442,校:-360,業:-1043,氏:5388,民:-2716,気:-910,沢:-939,済:-543,物:-735,率:672,球:-1267,生:-1286,産:-1101,田:-2900,町:1826,的:2586,目:922,省:-3485,県:2997,空:-867,立:-2112,第:788,米:2937,系:786,約:2171,経:1146,統:-1169,総:940,線:-994,署:749,者:2145,能:-730,般:-852,行:-792,規:792,警:-1184,議:-244,谷:-1e3,賞:730,車:-1481,軍:1158,輪:-1433,込:-3370,近:929,道:-1291,選:2596,郎:-4866,都:1192,野:-1100,銀:-2213,長:357,間:-2344,院:-2297,際:-2604,電:-878,領:-1659,題:-792,館:-1984,首:1749,高:2120,"｢":1895,"｣":3798,"･":-4371,ｯ:-724,ｰ:-11870,ｶ:2145,ｺ:1789,ｾ:1287,ﾄ:-403,ﾒ:-1635,ﾗ:-881,ﾘ:-541,ﾙ:-856,ﾝ:-3637},this.UW5__={",":465,".":-299,1:-514,E2:-32768,"]":-2762,"、":465,"。":-299,"「":363,あ:1655,い:331,う:-503,え:1199,お:527,か:647,が:-421,き:1624,ぎ:1971,く:312,げ:-983,さ:-1537,し:-1371,す:-852,だ:-1186,ち:1093,っ:52,つ:921,て:-18,で:-850,と:-127,ど:1682,な:-787,に:-1224,の:-635,は:-578,べ:1001,み:502,め:865,ゃ:3350,ょ:854,り:-208,る:429,れ:504,わ:419,を:-1264,ん:327,イ:241,ル:451,ン:-343,中:-871,京:722,会:-1153,党:-654,務:3519,区:-901,告:848,員:2104,大:-1296,学:-548,定:1785,嵐:-1304,市:-2991,席:921,年:1763,思:872,所:-814,挙:1618,新:-1682,日:218,月:-4353,査:932,格:1356,機:-1508,氏:-1347,田:240,町:-3912,的:-3149,相:1319,省:-1052,県:-4003,研:-997,社:-278,空:-813,統:1955,者:-2233,表:663,語:-1073,議:1219,選:-1018,郎:-368,長:786,間:1191,題:2368,館:-689,"１":-514,Ｅ２:-32768,"｢":363,ｲ:241,ﾙ:451,ﾝ:-343},this.UW6__={",":227,".":808,1:-270,E1:306,"、":227,"。":808,あ:-307,う:189,か:241,が:-73,く:-121,こ:-200,じ:1782,す:383,た:-428,っ:573,て:-1014,で:101,と:-105,な:-253,に:-149,の:-417,は:-236,も:-206,り:187,る:-135,を:195,ル:-673,ン:-496,一:-277,中:201,件:-800,会:624,前:302,区:1792,員:-1212,委:798,学:-960,市:887,広:-695,後:535,業:-697,相:753,社:-507,福:974,空:-822,者:1811,連:463,郎:1082,"１":-270,Ｅ１:306,ﾙ:-673,ﾝ:-496},this}t.prototype.ctype_=function(e){for(var t in this.chartype_)if(e.match(this.chartype_[t][0]))return this.chartype_[t][1];return"O"},t.prototype.ts_=function(e){return e||0},t.prototype.segment=function(e){if(null==e||null==e||""==e)return[];var t=[],r=["B3","B2","B1"],a=["O","O","O"],l=e.split("");for(v=0;v<l.length;++v)r.push(l[v]),a.push(this.ctype_(l[v]));r.push("E1"),r.push("E2"),r.push("E3"),a.push("O"),a.push("O"),a.push("O");for(var u=r[3],n="U",i="U",g="U",v=4;v<r.length-3;++v){var s=this.BIAS__,c=r[v-3],o=r[v-2],E=r[v-1],h=r[v],d=r[v+1],f=r[v+2],C=a[v-3],A=a[v-2],F=a[v-1],p=a[v],D=a[v+1],m=a[v+2];s+=this.ts_(this.UP1__[n]),s+=this.ts_(this.UP2__[i]),s+=this.ts_(this.UP3__[g]),s+=this.ts_(this.BP1__[n+i]),s+=this.ts_(this.BP2__[i+g]),s+=this.ts_(this.UW1__[c]),s+=this.ts_(this.UW2__[o]),s+=this.ts_(this.UW3__[E]),s+=this.ts_(this.UW4__[h]),s+=this.ts_(this.UW5__[d]),s+=this.ts_(this.UW6__[f]),s+=this.ts_(this.BW1__[o+E]),s+=this.ts_(this.BW2__[E+h]),s+=this.ts_(this.BW3__[h+d]),s+=this.ts_(this.TW1__[c+o+E]),s+=this.ts_(this.TW2__[o+E+h]),s+=this.ts_(this.TW3__[E+h+d]),s+=this.ts_(this.TW4__[h+d+f]),s+=this.ts_(this.UC1__[C]),s+=this.ts_(this.UC2__[A]),s+=this.ts_(this.UC3__[F]),s+=this.ts_(this.UC4__[p]),s+=this.ts_(this.UC5__[D]),s+=this.ts_(this.UC6__[m]),s+=this.ts_(this.BC1__[A+F]),s+=this.ts_(this.BC2__[F+p]),s+=this.ts_(this.BC3__[p+D]),s+=this.ts_(this.TC1__[C+A+F]),s+=this.ts_(this.TC2__[A+F+p]),s+=this.ts_(this.TC3__[F+p+D]),s+=this.ts_(this.TC4__[p+D+m]),s+=this.ts_(this.UQ1__[n+C]),s+=this.ts_(this.UQ2__[i+A]),s+=this.ts_(this.UQ3__[g+F]),s+=this.ts_(this.BQ1__[i+A+F]),s+=this.ts_(this.BQ2__[i+F+p]),s+=this.ts_(this.BQ3__[g+A+F]),s+=this.ts_(this.BQ4__[g+F+p]),s+=this.ts_(this.TQ1__[i+C+A+F]),s+=this.ts_(this.TQ2__[i+A+F+p]),s+=this.ts_(this.TQ3__[g+C+A+F]);var B="O";(s+=this.ts_(this.TQ4__[g+A+F+p]))>0&&(t.push(u),u="",B="B"),n=i,i=g,g=B,u+=r[v]}return t.push(u),t},e.exports=t},429:e=>{var t=function(e,t){var r;for(r=0;r<e.length;r++)if(e[r].regex.test(t))return e[r]},r=function(e,r){var a,l,u;for(a=0;a<r.length;a++)if(l=t(e,r.substring(0,a+1)))u=l;else if(u)return{max_index:a,rule:u};return u?{max_index:r.length,rule:u}:void 0};e.exports=function(e){var a="",l=[],u=1,n=1,i=function(t,r){e({type:r,src:t,line:u,col:n});var a=t.split("\n");u+=a.length-1,n=(a.length>1?1:n)+a[a.length-1].length};return{addRule:function(e,t){l.push({regex:e,type:t})},onText:function(e){for(var t=a+e,u=r(l,t);u&&u.max_index!==t.length;)i(t.substring(0,u.max_index),u.rule.type),t=t.substring(u.max_index),u=r(l,t);a=t},end:function(){if(0!==a.length){var e=t(l,a);if(!e){var r=new Error("unable to tokenize");throw r.tokenizer2={buffer:a,line:u,col:n},r}i(a,e.type)}}}}}},t={};function r(a){var l=t[a];if(void 0!==l)return l.exports;var u=t[a]={exports:{}};return e[a](u,u.exports,r),u.exports}r.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return r.d(t,{a:t}),t},r.d=(e,t)=>{for(var a in t)r.o(t,a)&&!r.o(e,a)&&Object.defineProperty(e,a,{enumerable:!0,get:t[a]})},r.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),r.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var a={};(()=>{"use strict";r.r(a),r.d(a,{default:()=>qe});const e=window.yoast.analysis,t=window.lodash;var l=r(771),u=r.n(l);const{sanitizeString:n,removePunctuation:i}=e.languageProcessing,g=new(u());function v(e){if(""===(e=n(e)))return[];let r=g.segment(e);return r=(0,t.map)(r,(function(e){return i(e)})),(0,t.filter)(r,(function(e){return""!==e.trim()}))}const s=["が","を","に","へ","と","から","より","まで","で","か","の","や","やら","だ","なり","わ","とも","かしら","かな","かい","っけ","さ","よ","ね","ばかり","ばっかり","ばっか","ばかし","だけ","きり","っきり","ほど","くらい","ぐらい","ころ","ごろ","など","は","も","こそ","でも","しか","さえ","ば","て","のに","ので","ところ","けれども","けれど","くせ","もの","もん","ものか","もんか","な","なあ","なんか","なんて","って","し","ずつ","すら","ともに","ぜ","ぞ","じゃん","ながら","たり","だり","つつ","まま","ものの","つまり","しかし","よって","に関する","に関し","について","における","において","関","する","関し","ついて","おえる","おける","という","といっ","た","に対する","対","に対して","対して","にかけ","による","により","によって","および","これ","それ","あれ","どれ","こちら","こっち","そちら","そっち","あちら","あっち","どちら","どっち","ここ","そこ","あそこ","どこ","こう","そう","ああ","どう","こんな","そんな","あんな","どんな","この","よう","その","あの","ど","こうやって","そうやって","ああやって","やっ","どの","こっ","そっから","どっ","さま","あちらさま","どちらさま","そちらさま","こんだけ","そんだけ","あんだけ","どん","なに","なん","何","どいつ","どなた","だれ","誰","いつ","なぜ","どうして","どれくらい","どれぐらい","位","いくら","いくつ","一","二","三","四","五","六","七","八","九","十","百","千","万","億","兆","ひとつ","ふたつ","みっつ","よっつ","いつつ","むっつ","ななつ","やっつ","ここのつ","とお","つ","こ","コ","個","人","ひき","匹","まい","枚","さつ","冊","杯","回","キロ","グラム","適量","少々","大匙","大さじ","小匙","小さじ","g","cc","ml","l","kg","番目","め","週","時間","週間","時","分","秒","か月","カ月","ヶ月","部分","一部","ほか","他","それぞれ","まっ","たく","全く","ぜんぶ","全部","すべて","全","すごく","最高","最悪","可能","良い","良く","良さ","いい","よい","悪い","悪く","悪さ","大きな","おおき","小さ","ちいさ","だめ","ダメ","駄目","ただ","ちょっと","すこし","少し","なか","たくさん","よく","たまに","ときどき","時々","いつも","およそ","やく","だい","たい","約","程","大体","また","もう","とて","おそらく","たぶん","恐らく","多分","のみ","多少","本当","ほんとう","まじ","マジ","勿論","もちろん","やっと","しっかり","さっき","ほんと","ホント","きっと","かならず","必ず","絶対","ぜっ","ゼッタイ","にかく","やっぱり","やっぱ","たっ","はっきり","すでに","なる","いっしょ","緒","だいじょうぶ","ダイジョウブ","大丈夫","年","月","日","今日","明日","明後","昨日","一昨日","きょう","あす","あし","あさっ","き","のう","おととい","今年","来年","去年","ことし","らいねん","きょねん","わたし","わたくし","あたし","私","あたくし","うち","うちら","おら","おいら","わたしら","たち","わたしど","われら","われわ","れ","私ら","私達","達","私共","我ら","我々","おれ","俺","オレ","おれら","ぼく","ボクら","僕","じぶん","俺ら","僕ら","僕達","自分","俺達","ボク","あなた","貴方","貴女","貴男","君","きみ","おまえ","お","前","あんた","お宅","てめえ","貴殿","彼","彼ら","彼女","ら","こいつ","そいつ","あいつ","アイツ","これら","それら","あれら","あいつら","ども","みな","みなさま","おのおの","共","みんな","皆様","各々","皆","皆さま","方","当方","自身","さん","様","殿","ちゃん","くん","こと","事","物","コト","やつ","ヤツ","奴","まえ","あと","うえ","後","上","下","中","先","さらに","更","とく","特に","ほとんど","再び","ふたたび","ほぼ","そのまま","すぐ","あまり","相当","しばしば","わずか","僅か","比較","的","まだ","かなり","つい","まず","やが","やや","つねに","常","ひきつづき","引き続き","きわめて","極めて","ごく","別","べつ","はり","必ずし","かならずしも","むしろ","がい","まも","なく","あら","ためて","けっし","おも","互い","間","改めて","決し","主","主として","もっと","とりわけ","あく","おおむね","おおい","概ね","大い","そうし","それほど","ちょうど","とえ","まさに","なんと","とか","あえて","まる","おおよそ","ます","ぜんぜん","全然","じゃ","ません","です","あり","ませ","ん","ない","しませ","なら","ある","ありませ","いる","い","いませ","できる","でき","れる","られる","せる","させる","思わ","考え","られ","おっしゃい","述べ","言わ","話","なられ","お思い","らしい","らしく","でしょ","う","だろ","ご","御","ハイ","はい","いいえ","うん","うーん","ええ","よし","いや","まあ","おい","ねえ","どうぞ","ほら","おお","あー","さあ","まし","でし","だっ","なかっ","しまし","なっ","あっ","いた","せ","させ","ますか","み","みませ","ましょ","でみ","でみませ","なられる","なろ"];function c(e){let t=v(e);return t=t.filter((e=>!s.includes(e))),t=t.map((e=>e.endsWith("じゃ")?e.slice(0,-2):e)),t}const o=["“","”","〝","〞","〟","‟","„",'"',"「","」","『","』"],E=e=>(0,t.includes)(o,e[0])&&(0,t.includes)(o,e[e.length-1]);function h(e){const t={exactMatchRequested:!1,keyphrase:e};return E(e)&&(t.keyphrase=e.substring(1,e.length-1),t.exactMatchRequested=!0),t}const d=new RegExp("["+["'","‘","’","‛","`","‹","›"].join("")+"]","g");function f(e){return e.replace(d,"'")}function C(e){return function(e){return e.replace(/[“”〝〞〟‟„『』«»]/g,'"')}(f(e))}function A(e,t){e=e.toLowerCase();const r=h(t);if(r.exactMatchRequested){e=f(e);const t=r.keyphrase,a=[];let l=e.indexOf(t);for(;-1!==l;)a.push(t),l=e.indexOf(t,l+t.length);return a}return c(e).filter((e=>t===e))}function F(e,r){return e.length<=1?[e]:function(e,r){const a=r.paradigmGroups;let l=(0,t.uniq)((0,t.flatten)(a));l=l.sort(((e,t)=>t.length-e.length||e.localeCompare(t)));const u=l.filter((t=>e.endsWith(t))),n=[];if(0===u.length)n.push(e);else{const t=u[0],r=e.slice(0,-t.length);for(const e of a)e.includes(t)&&n.push(e.map((e=>r+e)))}return e.endsWith("る")&&n.push(e.slice(0,-1)),(0,t.uniq)((0,t.flatten)(n))}(e,r)}const{baseStemmer:p}=e.languageProcessing;function D(e){const r=(0,t.get)(e.getData("morphology"),"ja",!1);return r?e=>function(e,t){let r=F(e,t);return r=r.sort(((e,t)=>e.length-t.length||e.localeCompare(t))),r[0]}(e,r):p}function m(e){const t=[];return e.map((e=>t.push(e.length))),0===e.length?0:t.reduce(((e,t)=>e+t))}const B=new RegExp("(ftp|http(s)?:\\/\\/.)(www\\\\.)?[-a-zA-Z0-9@:%._\\/+~#=]{2,256}\\.[a-z]{2,6}\\b([-a-zA-Z0-9@:;%_\\/+.~#?&()=]*)|www\\.[-a-zA-Z0-9@:%._\\/+~#=]{2,256}\\.[a-z]{2,6}\\b([-a-zA-Z0-9@:;%_\\/+.~#?&()=]*)","igm"),{sanitizeString:O}=e.languageProcessing;function _(e){return e=function(e){return e.replace(B,"")}(e),(e=(e=O(e)).replace(/\s/g,"")).length}function I(e,t){const r=v(e).join("|"),a=[];return t.forEach((function(e){const t=e.join("|");r.includes(t)&&a.push(e)})),a}var b=r(429),H=r.n(b);function y(e,t=!1,r="",a=""){let l,u;return l="id"===a?'[ \\u00a0\\n\\r\\t.,()”“〝〞〟‟„"+;!¡?¿:/»«‹›'+r+"<>":'[ \\u00a0\\u2014\\u06d4\\u061f\\u060C\\u061B\\n\\r\\t.,()”“〝〞〟‟„"+\\-;!¡?¿:/»«‹›'+r+"<>",u=t?"($|((?="+l+"]))|((['‘’‛`])("+l+"])))":"($|("+l+"])|((['‘’‛`])("+l+"])))","(^|"+l+"'‘’‛`])"+e+u}function U(e){return(e=(e=(e=(e=e.replace(/\s{2,}/g," ")).replace(/\s\.$/,".")).replace(/^\s+|\s+$/g,"")).replace(/\s。/g,"。")).replace(/。\s/g,"。")}const M=["address","article","aside","blockquote","canvas","dd","div","dl","fieldset","figcaption","figure","footer","form","h1","h2","h3","h4","h5","h6","header","hgroup","hr","li","main","nav","noscript","ol","output","p","pre","section","table","tfoot","ul","video"],S=["b","big","i","small","tt","abbr","acronym","cite","code","dfn","em","kbd","strong","samp","time","var","a","bdo","br","img","map","object","q","script","span","sub","sup","button","input","label","select","textarea"],x=(new RegExp("^("+M.join("|")+")$","i"),new RegExp("^("+S.join("|")+")$","i"),new RegExp("^<("+M.join("|")+")[^>]*?>$","i")),w=new RegExp("^</("+M.join("|")+")[^>]*?>$","i"),k=new RegExp("^<("+S.join("|")+")[^>]*>$","i"),K=new RegExp("^</("+S.join("|")+")[^>]*>$","i"),T=/^<([^>\s/]+)[^>]*>$/,R=/^<\/([^>\s]+)[^>]*>$/,z=/^[^<]+$/,N=/^<[^><]*$/,W=/<!--(.|[\r\n])*?-->/g;let j,L=[];(0,t.memoize)((function(e){const r=[];let a=0,l="",u="",n="";return e=e.replace(W,""),L=[],j=H()((function(e){L.push(e)})),j.addRule(z,"content"),j.addRule(N,"greater-than-sign-content"),j.addRule(x,"block-start"),j.addRule(w,"block-end"),j.addRule(k,"inline-start"),j.addRule(K,"inline-end"),j.addRule(T,"other-element-start"),j.addRule(R,"other-element-end"),j.onText(e),j.end(),(0,t.forEach)(L,(function(e,t){const i=L[t+1];switch(e.type){case"content":case"greater-than-sign-content":case"inline-start":case"inline-end":case"other-tag":case"other-element-start":case"other-element-end":case"greater than sign":i&&(0!==a||"block-start"!==i.type&&"block-end"!==i.type)?u+=e.src:(u+=e.src,r.push(u),l="",u="",n="");break;case"block-start":0!==a&&(""!==u.trim()&&r.push(u),u="",n=""),a++,l=e.src;break;case"block-end":a--,n=e.src,""!==l&&""!==n?r.push(l+u+n):""!==u.trim()&&r.push(u),l="",u="",n=""}a<0&&(a=0)})),r})),new RegExp("^<("+M.join("|")+")[^>]*?>","i"),new RegExp("</("+M.join("|")+")[^>]*?>$","i");const $=function(e){return U(e=e.replace(/(<([^>]+)>)/gi," "))},P=function(e){return e.replace(/&nbsp;/g," ")},Y=function(e){return function(e){return e.replace(/\s/g," ")}(e=function(e){return e.replace(/\u2014/g," ")}(e=P(e)))};function Z(e){return e=Y(e),$(e)}const Q=new RegExp("^[.]$"),G=/^<[^><]*$/,q=/^<([^>\s/]+)[^>]*>$/im,V=/^<\/([^>\s]+)[^>]*>$/im,J=/^\s*[[({]\s*$/,X=/^\s*[\])}]\s*$/,ee=function(e,r=!1,a="",l=!1){const u="("+(e=(0,t.map)(e,(function(e){return l&&(e=function(e){const t=[{base:"a",letters:/[\u0061\u24D0\uFF41\u1E9A\u00E0\u00E1\u00E2\u1EA7\u1EA5\u1EAB\u1EA9\u00E3\u0101\u0103\u1EB1\u1EAF\u1EB5\u1EB3\u0227\u01E1\u00E4\u01DF\u1EA3\u00E5\u01FB\u01CE\u0201\u0203\u1EA1\u1EAD\u1EB7\u1E01\u0105\u2C65\u0250]/g},{base:"aa",letters:/[\uA733]/g},{base:"ae",letters:/[\u00E6\u01FD\u01E3]/g},{base:"ao",letters:/[\uA735]/g},{base:"au",letters:/[\uA737]/g},{base:"av",letters:/[\uA739\uA73B]/g},{base:"ay",letters:/[\uA73D]/g},{base:"b",letters:/[\u0062\u24D1\uFF42\u1E03\u1E05\u1E07\u0180\u0183\u0253]/g},{base:"c",letters:/[\u0063\u24D2\uFF43\u0107\u0109\u010B\u010D\u00E7\u1E09\u0188\u023C\uA73F\u2184]/g},{base:"d",letters:/[\u0064\u24D3\uFF44\u1E0B\u010F\u1E0D\u1E11\u1E13\u1E0F\u0111\u018C\u0256\u0257\uA77A]/g},{base:"dz",letters:/[\u01F3\u01C6]/g},{base:"e",letters:/[\u0065\u24D4\uFF45\u00E8\u00E9\u00EA\u1EC1\u1EBF\u1EC5\u1EC3\u1EBD\u0113\u1E15\u1E17\u0115\u0117\u00EB\u1EBB\u011B\u0205\u0207\u1EB9\u1EC7\u0229\u1E1D\u0119\u1E19\u1E1B\u0247\u025B\u01DD]/g},{base:"f",letters:/[\u0066\u24D5\uFF46\u1E1F\u0192\uA77C]/g},{base:"g",letters:/[\u0067\u24D6\uFF47\u01F5\u011D\u1E21\u011F\u0121\u01E7\u0123\u01E5\u0260\uA7A1\u1D79\uA77F]/g},{base:"h",letters:/[\u0068\u24D7\uFF48\u0125\u1E23\u1E27\u021F\u1E25\u1E29\u1E2B\u1E96\u0127\u2C68\u2C76\u0265]/g},{base:"hv",letters:/[\u0195]/g},{base:"i",letters:/[\u0069\u24D8\uFF49\u00EC\u00ED\u00EE\u0129\u012B\u012D\u00EF\u1E2F\u1EC9\u01D0\u0209\u020B\u1ECB\u012F\u1E2D\u0268\u0131]/g},{base:"j",letters:/[\u006A\u24D9\uFF4A\u0135\u01F0\u0249]/g},{base:"k",letters:/[\u006B\u24DA\uFF4B\u1E31\u01E9\u1E33\u0137\u1E35\u0199\u2C6A\uA741\uA743\uA745\uA7A3]/g},{base:"l",letters:/[\u006C\u24DB\uFF4C\u0140\u013A\u013E\u1E37\u1E39\u013C\u1E3D\u1E3B\u017F\u0142\u019A\u026B\u2C61\uA749\uA781\uA747]/g},{base:"lj",letters:/[\u01C9]/g},{base:"m",letters:/[\u006D\u24DC\uFF4D\u1E3F\u1E41\u1E43\u0271\u026F]/g},{base:"n",letters:/[\u006E\u24DD\uFF4E\u01F9\u0144\u00F1\u1E45\u0148\u1E47\u0146\u1E4B\u1E49\u019E\u0272\u0149\uA791\uA7A5]/g},{base:"nj",letters:/[\u01CC]/g},{base:"o",letters:/[\u006F\u24DE\uFF4F\u00F2\u00F3\u00F4\u1ED3\u1ED1\u1ED7\u1ED5\u00F5\u1E4D\u022D\u1E4F\u014D\u1E51\u1E53\u014F\u022F\u0231\u00F6\u022B\u1ECF\u0151\u01D2\u020D\u020F\u01A1\u1EDD\u1EDB\u1EE1\u1EDF\u1EE3\u1ECD\u1ED9\u01EB\u01ED\u00F8\u01FF\u0254\uA74B\uA74D\u0275]/g},{base:"oi",letters:/[\u01A3]/g},{base:"ou",letters:/[\u0223]/g},{base:"oo",letters:/[\uA74F]/g},{base:"p",letters:/[\u0070\u24DF\uFF50\u1E55\u1E57\u01A5\u1D7D\uA751\uA753\uA755]/g},{base:"q",letters:/[\u0071\u24E0\uFF51\u024B\uA757\uA759]/g},{base:"r",letters:/[\u0072\u24E1\uFF52\u0155\u1E59\u0159\u0211\u0213\u1E5B\u1E5D\u0157\u1E5F\u024D\u027D\uA75B\uA7A7\uA783]/g},{base:"s",letters:/[\u0073\u24E2\uFF53\u00DF\u015B\u1E65\u015D\u1E61\u0161\u1E67\u1E63\u1E69\u0219\u015F\u023F\uA7A9\uA785\u1E9B]/g},{base:"t",letters:/[\u0074\u24E3\uFF54\u1E6B\u1E97\u0165\u1E6D\u021B\u0163\u1E71\u1E6F\u0167\u01AD\u0288\u2C66\uA787]/g},{base:"tz",letters:/[\uA729]/g},{base:"u",letters:/[\u0075\u24E4\uFF55\u00F9\u00FA\u00FB\u0169\u1E79\u016B\u1E7B\u016D\u00FC\u01DC\u01D8\u01D6\u01DA\u1EE7\u016F\u0171\u01D4\u0215\u0217\u01B0\u1EEB\u1EE9\u1EEF\u1EED\u1EF1\u1EE5\u1E73\u0173\u1E77\u1E75\u0289]/g},{base:"v",letters:/[\u0076\u24E5\uFF56\u1E7D\u1E7F\u028B\uA75F\u028C]/g},{base:"vy",letters:/[\uA761]/g},{base:"w",letters:/[\u0077\u24E6\uFF57\u1E81\u1E83\u0175\u1E87\u1E85\u1E98\u1E89\u2C73]/g},{base:"x",letters:/[\u0078\u24E7\uFF58\u1E8B\u1E8D]/g},{base:"y",letters:/[\u0079\u24E8\uFF59\u1EF3\u00FD\u0177\u1EF9\u0233\u1E8F\u00FF\u1EF7\u1E99\u1EF5\u01B4\u024F\u1EFF]/g},{base:"z",letters:/[\u007A\u24E9\uFF5A\u017A\u1E91\u017C\u017E\u1E93\u1E95\u01B6\u0225\u0240\u2C6C\uA763]/g}];for(let r=0;r<t.length;r++)e=e.replace(t[r].letters,t[r].base);return e}(e)),e=Z(e),r?e:y(e,!0,a)}))).join(")|(")+")";return new RegExp(u,"ig")}(["A.D.","Adm.","Adv.","B.C.","Br.","Brig.","Cmrd.","Col.","Cpl.","Cpt.","Dr.","Esq.","Fr.","Gen.","Gov.","Hon.","Jr.","Lieut.","Lt.","Maj.","Mr.","Mrs.","Ms.","Msgr.","Mx.","No.","Pfc.","Pr.","Prof.","Pvt.","Rep.","Reps.","Rev.","Rt. Hon.","Sen.","Sens.","Sgt.","Sps.","Sr.","St.","vs.","i.e.","e.g.","viz.","Mt."].map((e=>e.replace(".","\\.")))),te="(^|$|["+[" ","\\n","\\r","\\t"," ","۔","؟","،","؛"," ",".",",","'","(",")",'"',"+","-",";","!","?",":","/","»","«","‹","›","<",">","”","“","〝","〞","〟","‟","„"].map((e=>"\\"+e)).join("")+"])",re=new RegExp(te+"[A-Za-z]$"),ae=/<\/?([^\s]+?)(\s|>)/,le=["p","div","h1","h2","h3","h4","h5","h6","span","li","main"];class ue{constructor(){this.sentenceDelimiters='”〞〟„』›»’‛`"?!…۔؟'}getSentenceDelimiters(){return this.sentenceDelimiters}isNumber(e){return!(0,t.isNaN)(parseInt(e,10))}isBreakTag(e){return/<\/?br/.test(e)}isQuotation(e){return"'"===(e=C(e))||'"'===e}endsWithOrdinalDot(){return!1}isPunctuation(e){return"¿"===e||"¡"===e}removeDuplicateWhitespace(e){return e.replace(/\s+/," ")}isCapitalLetter(e){return e!==e.toLocaleLowerCase()}isSmallerThanSign(e){return"<"===e}getNextTwoCharacters(e){let r="";return(0,t.isUndefined)(e[0])||(r+=e[0].src),(0,t.isUndefined)(e[1])||(r+=e[1].src),r=this.removeDuplicateWhitespace(r),r}isLetterFromSpecificLanguage(e){return[/^[\u0590-\u05fe]+$/i,/^[\u0600-\u06FF]+$/i,/^[\uFB8A\u067E\u0686\u06AF]+$/i].some((t=>t.test(e)))}isValidSentenceBeginning(e){return this.isCapitalLetter(e)||this.isLetterFromSpecificLanguage(e)||this.isNumber(e)||this.isQuotation(e)||this.isPunctuation(e)||this.isSmallerThanSign(e)}isSentenceStart(e){return!(0,t.isUndefined)(e)&&("html-start"===e.type||"html-end"===e.type||"block-start"===e.type)}isSentenceEnding(e){return!(0,t.isUndefined)(e)&&("full-stop"===e.type||"sentence-delimiter"===e.type)}isPartOfPersonInitial(e,r,a,l){return!(0,t.isUndefined)(e)&&!(0,t.isUndefined)(a)&&!(0,t.isUndefined)(l)&&!(0,t.isUndefined)(r)&&"full-stop"===e.type&&"sentence"===r.type&&re.test(r.src)&&"sentence"===a.type&&1===a.src.trim().length&&"full-stop"===l.type}tokenizeSmallerThanContent(e,r,a){const l=e.src.substring(1),u=this.createTokenizer();this.tokenize(u.tokenizer,l);const n=this.getSentencesFromTokens(u.tokens,!1);if(n[0]=(0,t.isUndefined)(n[0])?"<":"<"+n[0],this.isValidSentenceBeginning(n[0])&&(r.push(a),a=""),a+=n[0],n.length>1){r.push(a),a="",n.shift();const e=n.pop();n.forEach((e=>{r.push(e)}));const t=new RegExp("[."+this.getSentenceDelimiters()+"]$");e.match(t)?r.push(e):a=e}return{tokenSentences:r,currentSentence:a}}createTokenizer(){const e=new RegExp("^["+this.getSentenceDelimiters()+"]$"),t=new RegExp("^[^."+this.getSentenceDelimiters()+"<\\(\\)\\[\\]]+$"),r=[],a=H()((function(e){r.push(e)}));return a.addRule(Q,"full-stop"),a.addRule(G,"smaller-than-sign-content"),a.addRule(q,"html-start"),a.addRule(V,"html-end"),a.addRule(J,"block-start"),a.addRule(X,"block-end"),a.addRule(e,"sentence-delimiter"),a.addRule(t,"sentence"),{tokenizer:a,tokens:r}}tokenize(e,t){e.onText(t);try{e.end()}catch(e){console.error("Tokenizer end error:",e,e.tokenizer2)}}endsWithAbbreviation(e){const t=e.match(ee);if(!t)return!1;const r=t.pop();return e.endsWith(r)}isValidTagPair(e,t){const r=e.src,a=t.src,l=r.match(ae)[1];return l===a.match(ae)[1]&&le.includes(l)}getSentencesFromTokens(e,r=!0){let a,l,u=[],n="";do{l=!1;const t=e[0],r=e[e.length-1];t&&r&&"html-start"===t.type&&"html-end"===r.type&&this.isValidTagPair(t,r)&&(e=e.slice(1,e.length-1),l=!0)}while(l&&e.length>1);return e.forEach(((r,l)=>{let i,g,v;const s=e[l+1],c=e[l-1],o=e[l+2];switch(g=this.getNextTwoCharacters([s,o]),i=g.length>=2,a=i?g[1]:"",r.type){case"html-start":case"html-end":this.isBreakTag(r.src)?(u.push(n),n=""):n+=r.src;break;case"smaller-than-sign-content":v=this.tokenizeSmallerThanContent(r,u,n),u=v.tokenSentences,n=v.currentSentence;break;case"sentence":case"block-start":n+=r.src;break;case"sentence-delimiter":if(n+=r.src,!(0,t.isUndefined)(s)&&"block-end"!==s.type&&"sentence-delimiter"!==s.type&&this.isCharacterASpace(s.src[0])){if(this.isQuotation(r.src)&&c&&"."!==c.src)break;this.isQuotation(r.src)||"…"===r.src?n=this.getValidSentence(i,a,g,s,u,n):(u.push(n),n="")}break;case"full-stop":if(n+=r.src,g=this.getNextTwoCharacters([s,o]),i=g.length>=2,a=i?g[1]:"",this.endsWithAbbreviation(n))break;if(i&&this.isNumber(g[0]))break;if(this.isPartOfPersonInitial(r,c,s,o))break;if(this.endsWithOrdinalDot(n))break;n=this.getValidSentence(i,a,g,s,u,n);break;case"block-end":if(n+=r.src,g=this.getNextTwoCharacters([s,o]),i=g.length>=2,a=i?g[0]:"",i&&this.isNumber(g[0])||this.isSentenceEnding(c)&&!this.isValidSentenceBeginning(a)&&!this.isSentenceStart(s))break;this.isSentenceEnding(c)&&(this.isSentenceStart(s)||this.isValidSentenceBeginning(a))&&(u.push(n),n="")}})),""!==n&&u.push(n),r&&(u=(0,t.map)(u,(function(e){return e.trim()}))),u}getValidSentence(e,t,r,a,l,u){return(e&&this.isValidSentenceBeginning(t)&&this.isCharacterASpace(r[0])||this.isSentenceStart(a))&&(l.push(u),u=""),u}isCharacterASpace(e){return/\s/.test(e)}}class ne extends ue{constructor(){super(),this.sentenceDelimiters="?!…。｡！‼？⁇⁉⁈⁉‥"}isNumber(e){return!(0,t.isNaN)(parseInt(e,10))||[/^[\uFF10-\uFF19]+$/i,/^[\u2460-\u249B]+$/i,/^[\u3220-\u3229]+$/i,/^[\u3280-\u3289]+$/i].some((t=>t.test(e)))}isQuotation(e){return"'"===(e=C(e))||'"'===e||/^[\u300C\u300E\u3008\u3014\u3010\uFF5B\uFF3B]+$/i.test(e)}isLetterFromSpecificLanguage(e){return[/^[\u3040-\u3096]+$/i,/^[\u30A1-\u30FA]+$/i,/^[\u31F0-\u31FF]+$/i,/^[\uFF66-\uFF9D]+$/i,/^[\u4E00-\u9FFC]+$/i].some((t=>t.test(e)))}isCharacterASpace(){return!0}}const ie=(0,t.memoize)((function(e,t=!0){const r=new ne,{tokenizer:a,tokens:l}=r.createTokenizer();return r.tokenize(a,e),0===l.length?[]:r.getSentencesFromTokens(l,t)}),((...e)=>JSON.stringify(e))),ge=function(e){if(""===e)return[];const r=(new(u())).segment(e);return(0,t.map)(r)},ve=["この","その","あの","こんな","そんな","あんな","こう","そう","ああ"],se=[["だから"],["その","ため"],["この","ため"],["それ","で"],["そこ","で"],["よって"],["する","と"],["だと","する","と"],["ゆえ","に"],["それゆえ","に"],["し","た","がっ","て"],["それゆえ"],["それ","なら"],["それ","で","は"],["ならば"],["だ","と","し","たら"],["そう","する","と"],["そう","し","たら"],["さも","ない","と"],["そうし","ない","と"],["そう","で","ない","なら"],["だと","すれ","ば"],["そう","なる","と"],["と","なる","と"],["と","なれ","ば"],["そうして","みる","と"],["そう","なれ","ば"],["そうして"],["そのけっか"],["その","結果"],["しかし"],["けど"],["ただ"],["だ","が"],["しかし","ながら"],["けれど"],["けれども"],["だけど"],["だけども"],["そう","で","は","ある","が"],["それ","でも"],["でも"],["で","は","ある","が"],["に","も","かかわらず"],["それ","に","も","かかわらず"],["ところ","が"],["しかる","に"],["と","はいう","もの","の"],["と","は","言う","もの","の"],["な","の","に"],["それ","な","の","に"],["と","は","いえ"],["そう","はいう","もの","の"],["そう","は","言う","もの","の"],["そのくせ"],["さり","と","て"],["さ","れど"],["これ","に","はんし","て"],["これ","に","反し","て"],["それ","に","し","て","は"],["そのわり","に","は"],["そのわり","に"],["それ","なら"],["ならび","に"],["おなじく"],["同じく"],["また"],["どう","よう","に"],["同様","に"],["さ","れど","も"],["さらに"],["おなじ","よう","に"],["同じ","よう","に"],["のみ","なら","ず"],["しかも"],["おまけ","に"],["そのうえ"],["その","上"],["そして"],["それ","から"],["それどころか"],["どころか"],["それ","に"],["それ","に","し","て","も"],["くわえ","て"],["加え","て"],["それ","にくわえ","て"],["それ","に","加え","て"],["ひいて","は"],["なお"],["それ","ばかり","で","なく"],["それ","ばかりか"],["とも","あれ"],["その","うえ","に"],["その","上","に"],["その","うえ","で"],["その","上","で"],["あまつさえ"],["いっぽう"],["一方"],["たほう"],["他方"],["ぎゃく","に"],["逆","に"],["それ","に","たいし","て"],["それ","に対して"],["たいし","て"],["対して"],["はん","たい","に"],["反対","に"],["はんめん"],["反面"],["その","は","んめん"],["その","反面"],["また","は"],["もしく","は"],["あるい","は"],["それとも"],["ほか","に","は"],["他","に","は"],["ほか","に","も"],["他","に","も"],["だいいち","に"],["第一","に"],["だい","に","に"],["第二","に"],["だい","さん","に"],["第三","に"],["だい","よん","に"],["第四","に"],["ひとつめ","は"],["一つめ","は"],["一つ","目","は"],["１つめ","は"],["1つ","目","は"],["ふたつめ","は"],["二つめ","は"],["二つ","目","は"],["２つめ","は"],["2つ","目","は"],["みっつめ","は"],["三つめ","は"],["三つ","目","は"],["３つめ","は"],["3つ","目","は"],["よっつめ","は"],["四つめ","は"],["四つ","目","は"],["４つめ","は"],["4つ","目","は"],["いって","んめ","は"],["一点目","は"],["1","点目","は"],["に","てんめ","は"],["二点目","は"],["2","点目","は"],["さん","て","んめ","は"],["三点目","は"],["3","点目","は"],["よん","て","んめ","は"],["四点目","は"],["4","点目","は"],["ひとつ","は"],["一つ","は"],["１つ","は"],["もう","ひ","とつ","は"],["もう","一つ","は"],["もう","１","つ","は"],["いってん","は"],["一点","は"],["1点","は"],["もういってん","は"],["もう","一点","は"],["もう","1","点","は"],["はじめ","に"],["さいしょ","に"],["最初","に"],["つづい","て"],["続い","て"],["つい","で"],["次い","で"],["さいごに"],["最後","に"],["おわり","に"],["終わり","に"],["終り","に"],["その","ご"],["その","後"],["まず"],["つぎ","に"],["次","に"],["さらに"],["その","あと"],["その","あと","に"],["その後","に"],["なぜ","なら"],["なぜ","なら","ば"],["なぜか","という","と"],["という","の","は"],["という","の","も"],["だっ","て"],["なに","しろ"],["なにせ"],["どう","して","か","という","と"],["なん","で","か","という","と"],["ち","なみ","に"],["ただ"],["もっとも"],["その","かわり"],["ただし"],["そも","そも"],["じつ","は"],["実","は"],["じつ","の","ところ"],["実","の","ところ"],["じつ","は","という","と"],["実","は","という","と"],["実","は","と","言う","と"],["つまり"],["いいかえる","と"],["言い","かえる","と"],["言い換える","と"],["す","なわち"],["よう","は"],["要","は"],["とど","の","つまり"],["よう","する","に"],["要","する","に"],["むしろ"],["かんげん","する","と"],["換言","する","と"],["かえっ","て"],["かわり","に"],["その","かわり"],["いわば"],["いって","みれ","ば"],["言っ","て","みれ","ば"],["という","より"],["と言う","より"],["という","より","は"],["と言う","より","は"],["という","より","も"],["と言う","より","も"],["という","か"],["ぐたい","て","き","に","は"],["具体","的","に","は"],["た","とえば"],["例え","ば"],["とりわけ"],["なか","で","も"],["こと","に"],["殊","に"],["とく","に"],["特に"],["それ","に","は"],["その","ため","に","は"],["そう","する","ば","あい"],["そう","する","場合"],["その","ば","あい"],["その","場合"],["そうすれ","ば"],["それ","によって"],["そう","する","こと","で"],["さ","れ","ば"],["さすれ","ば"],["さて"],["それ","につけて","も"],["ところ","で"],["とき","に"],["時","に"],["それ","は","さ","て","おき"],["で","は"],["それ","で","は"],["じゃ","あ"],["とも","あれ"],["それ","は","そう","と"],["そういえ","ば"],["それ","に","し","たっ","て"],["しょせん"],["所詮"],["ど","の","みち"],["どの","道"],["どっちみち"],["どっち道"],["この","よう","に"],["こうして"],["いずれ","に","せ","よ"],["いずれ","に","し","て","も"],["どちら","に","せ","よ"],["どっち","に","し","て","も"],["どっち","に","せ","よ"],["どちら","に","し","て","も"],["とも","あれ"],["いじょう","の","よう","に"],["以上","の","よう","に"],["たしか","に"],["確か","に"],["いっぽう","で"],["一方","で"],["いっぽう","で","は"],["一方","で","は"],["たほう","で"],["他方","で"],["たほう","で","は"],["他方","で","は"],["かり","に"],["仮","に"],["た","とえ"],["よしんば"],["と","する","と"],["とすれ","ば"],["し","から","ば"],["に","も","かかわらず"],["に","も","拘わらず"],["といえども"],["と","言え","ど","も"],["といって","も"],["と言っ","て","も"],["と","はいう","もの","の"],["と","は","言う","もの","の"],["こと","に","は"],["まも","なく"],["やが","て"],["と","たん","に"],["つづい","て"],["続い","て"],["ひきつづき"],["引き続き"],["かと","おも","う","と"],["かと","思う","と"],["かと","おもえば"],["かと","思え","ば"],["かと","おもうまも","なく"],["かと","思う間も","なく"],["かと","思う","まも","なく"],["と","たん"],["そのしゅんかん"],["その","瞬間"],["どうじ","に"],["同","時に"],["まし","て"],["まし","て","や"],["これ","に","たいし","て"],["これ","に対して"],["もちろん"],["も","とより"],["それ","だ","から"],["これ","だ","から"],["とうぜん"],["当然"]],ce={lengthCriteria:7},oe={defaultAnalysis:{recommendedMinimum:600,slightlyBelowMinimum:500,belowMinimum:400,veryFarBelowMinimum:200},defaultCornerstone:{recommendedMinimum:1800,slightlyBelowMinimum:800,belowMinimum:600,scores:{belowMinimum:-20,farBelowMinimum:-20}},taxonomyAssessor:{recommendedMinimum:60,slightlyBelowMinimum:20,veryFarBelowMinimum:1},productSEOAssessor:{recommendedMinimum:400,slightlyBelowMinimum:300,belowMinimum:200,veryFarBelowMinimum:100},productCornerstoneSEOAssessor:{recommendedMinimum:800,slightlyBelowMinimum:600,belowMinimum:400,scores:{belowMinimum:-20,farBelowMinimum:-20}},collectionSEOAssessor:{recommendedMinimum:60,slightlyBelowMinimum:20,veryFarBelowMinimum:1},collectionCornerstoneSEOAssessor:{recommendedMinimum:60,slightlyBelowMinimum:20,veryFarBelowMinimum:1}},Ee={defaultPageParams:{recommendedLength:300,maximumRecommendedLength:400},productPageParams:{recommendedLength:140,maximumRecommendedLength:200}},he={transitionWords:400,keyphraseDensity:200},de={recommendedLength:40},fe={defaultParameters:{parameters:{recommendedMaximumLength:600,slightlyTooMany:600,farTooMany:700},applicableIfTextLongerThan:600},cornerstoneParameters:{parameters:{recommendedMaximumLength:500,slightlyTooMany:500,farTooMany:600},applicableIfTextLongerThan:500}},Ce={defaultAnalysis:{parameters:{recommendedMaximum:12,acceptableMaximum:18}},productPages:{parameters:{recommendedMinimum:8,recommendedMaximum:12,acceptableMaximum:18,acceptableMinimum:4}}},Ae={recommendedMaximumLength:60,maximumLength:80},Fe="[\\–\\-\\(\\)_\\[\\]’'.?!:;,¿¡«»‹›—×+&<>]+",pe=new RegExp("^"+Fe),De=new RegExp(Fe+"$");function me(e,r){if(o.includes(e[0])&&o.includes(e[e.length-1]))return[[e]];const a=c(e);if(0===a.length)return[[]];const l=(0,t.get)(r.getData("morphology"),"ja",!1);return a.map((e=>l?F(e,l):[e]))}function Be(e,t){let r=e.getKeyword().toLocaleLowerCase("ja").trim();r=r.replace(/\s/g,"");const a=function(e){let t=e.split(",");return t=t.map((e=>U(e).replace(pe,"").replace(De,""))).filter((e=>e)),t}(e.getSynonyms().toLocaleLowerCase("ja").trim());return{keyphraseForms:me(r,t),synonymsForms:a.map((e=>me(e,t)))}}function Oe(e){return{keyphraseLength:m(v(e.getKeyword())),functionWords:[]}}function _e(e){return{text:e.getText(),count:_(e.getText()),unit:"character"}}const Ie="\\–\\-\\(\\)_\\[\\]’‘“”〝〞〟‟„\"'.?!:;,¿¡«»‹›—×+&۔؟،؛。｡！‼？⁇⁉⁈‥…・ー、〃〄〆〇〈〉《》「」『』【】〒〓〔〕〖〗〘〙〚〛〜〝〞〟〠〶〼〽｛｝｜～｟｠｢｣､［］･￥＄％＠＆＇（）＊／：；＜＞＼\\<>",be=(Ie.split(""),new RegExp("^["+Ie+"]+")),He=new RegExp("["+Ie+"]+$");function ye(e){e=(e=P(e)).replace("&amp","");const t=new RegExp("(\\\\)","g");return(e=(e=e.replace(t,"")).replace(be,"")).replace(He,"")}function Ue(e){return e.split("_")[0]}const Me={es:[{letter:/[\u00F1]/g,alternative:"n"},{letter:/[\u00D1]/g,alternative:"N"},{letter:/[\u00E1]/g,alternative:"a"},{letter:/[\u00C1]/g,alternative:"A"},{letter:/[\u00E9]/g,alternative:"e"},{letter:/[\u00C9]/g,alternative:"E"},{letter:/[\u00ED]/g,alternative:"i"},{letter:/[\u00CD]/g,alternative:"I"},{letter:/[\u00F3]/g,alternative:"o"},{letter:/[\u00D3]/g,alternative:"O"},{letter:/[\u00FA\u00FC]/g,alternative:"u"},{letter:/[\u00DA\u00DC]/g,alternative:"U"}],pl:[{letter:/[\u0105]/g,alternative:"a"},{letter:/[\u0104]/g,alternative:"A"},{letter:/[\u0107]/g,alternative:"c"},{letter:/[\u0106]/g,alternative:"C"},{letter:/[\u0119]/g,alternative:"e"},{letter:/[\u0118]/g,alternative:"E"},{letter:/[\u0142]/g,alternative:"l"},{letter:/[\u0141]/g,alternative:"L"},{letter:/[\u0144]/g,alternative:"n"},{letter:/[\u0143]/g,alternative:"N"},{letter:/[\u00F3]/g,alternative:"o"},{letter:/[\u00D3]/g,alternative:"O"},{letter:/[\u015B]/g,alternative:"s"},{letter:/[\u015A]/g,alternative:"S"},{letter:/[\u017A\u017C]/g,alternative:"z"},{letter:/[\u0179\u017B]/g,alternative:"Z"}],de:[{letter:/[\u00E4]/g,alternative:"ae"},{letter:/[\u00C4]/g,alternative:"Ae"},{letter:/[\u00FC]/g,alternative:"ue"},{letter:/[\u00DC]/g,alternative:"Ue"},{letter:/[\u00F6]/g,alternative:"oe"},{letter:/[\u00D6]/g,alternative:"Oe"},{letter:/[\u00DF]/g,alternative:"ss"},{letter:/[\u1E9E]/g,alternative:"SS"}],nbnn:[{letter:/[\u00E6\u04D5]/g,alternative:"ae"},{letter:/[\u00C6\u04D4]/g,alternative:"Ae"},{letter:/[\u00E5]/g,alternative:"aa"},{letter:/[\u00C5]/g,alternative:"Aa"},{letter:/[\u00F8]/g,alternative:"oe"},{letter:/[\u00D8]/g,alternative:"Oe"},{letter:/[\u00E9\u00E8\u00EA]/g,alternative:"e"},{letter:/[\u00C9\u00C8\u00CA]/g,alternative:"E"},{letter:/[\u00F3\u00F2\u00F4]/g,alternative:"o"},{letter:/[\u00D3\u00D2\u00D4]/g,alternative:"O"}],sv:[{letter:/[\u00E5]/g,alternative:"aa"},{letter:/[\u00C5]/g,alternative:"Aa"},{letter:/[\u00E4]/g,alternative:"ae"},{letter:/[\u00C4]/g,alternative:"Ae"},{letter:/[\u00F6]/g,alternative:"oe"},{letter:/[\u00D6]/g,alternative:"Oe"},{letter:/[\u00E9]/g,alternative:"e"},{letter:/[\u00C9]/g,alternative:"E"},{letter:/[\u00E0]/g,alternative:"a"},{letter:/[\u00C0]/g,alternative:"A"}],fi:[{letter:/[\u00E5]/g,alternative:"aa"},{letter:/[\u00C5]/g,alternative:"Aa"},{letter:/[\u00E4]/g,alternative:"a"},{letter:/[\u00C4]/g,alternative:"A"},{letter:/[\u00F6]/g,alternative:"o"},{letter:/[\u00D6]/g,alternative:"O"},{letter:/[\u017E]/g,alternative:"zh"},{letter:/[\u017D]/g,alternative:"Zh"},{letter:/[\u0161]/g,alternative:"sh"},{letter:/[\u0160]/g,alternative:"Sh"}],da:[{letter:/[\u00E5]/g,alternative:"aa"},{letter:/[\u00C5]/g,alternative:"Aa"},{letter:/[\u00E6\u04D5]/g,alternative:"ae"},{letter:/[\u00C6\u04D4]/g,alternative:"Ae"},{letter:/[\u00C4]/g,alternative:"Ae"},{letter:/[\u00F8]/g,alternative:"oe"},{letter:/[\u00D8]/g,alternative:"Oe"},{letter:/[\u00E9]/g,alternative:"e"},{letter:/[\u00C9]/g,alternative:"E"}],tr:[{letter:/[\u00E7]/g,alternative:"c"},{letter:/[\u00C7]/g,alternative:"C"},{letter:/[\u011F]/g,alternative:"g"},{letter:/[\u011E]/g,alternative:"G"},{letter:/[\u00F6]/g,alternative:"o"},{letter:/[\u00D6]/g,alternative:"O"},{letter:/[\u015F]/g,alternative:"s"},{letter:/[\u015E]/g,alternative:"S"},{letter:/[\u00E2]/g,alternative:"a"},{letter:/[\u00C2]/g,alternative:"A"},{letter:/[\u0131\u00EE]/g,alternative:"i"},{letter:/[\u0130\u00CE]/g,alternative:"I"},{letter:/[\u00FC\u00FB]/g,alternative:"u"},{letter:/[\u00DC\u00DB]/g,alternative:"U"}],lv:[{letter:/[\u0101]/g,alternative:"a"},{letter:/[\u0100]/g,alternative:"A"},{letter:/[\u010D]/g,alternative:"c"},{letter:/[\u010C]/g,alternative:"C"},{letter:/[\u0113]/g,alternative:"e"},{letter:/[\u0112]/g,alternative:"E"},{letter:/[\u0123]/g,alternative:"g"},{letter:/[\u0122]/g,alternative:"G"},{letter:/[\u012B]/g,alternative:"i"},{letter:/[\u012A]/g,alternative:"I"},{letter:/[\u0137]/g,alternative:"k"},{letter:/[\u0136]/g,alternative:"K"},{letter:/[\u013C]/g,alternative:"l"},{letter:/[\u013B]/g,alternative:"L"},{letter:/[\u0146]/g,alternative:"n"},{letter:/[\u0145]/g,alternative:"N"},{letter:/[\u0161]/g,alternative:"s"},{letter:/[\u0160]/g,alternative:"S"},{letter:/[\u016B]/g,alternative:"u"},{letter:/[\u016A]/g,alternative:"U"},{letter:/[\u017E]/g,alternative:"z"},{letter:/[\u017D]/g,alternative:"Z"}],is:[{letter:/[\u00E1]/g,alternative:"a"},{letter:/[\u00C1]/g,alternative:"A"},{letter:/[\u00F0]/g,alternative:"d"},{letter:/[\u00D0]/g,alternative:"D"},{letter:/[\u00E9]/g,alternative:"e"},{letter:/[\u00C9]/g,alternative:"E"},{letter:/[\u00ED]/g,alternative:"i"},{letter:/[\u00CD]/g,alternative:"I"},{letter:/[\u00F3\u00F6]/g,alternative:"o"},{letter:/[\u00D3\u00D6]/g,alternative:"O"},{letter:/[\u00FA]/g,alternative:"u"},{letter:/[\u00DA]/g,alternative:"U"},{letter:/[\u00FD]/g,alternative:"y"},{letter:/[\u00DD]/g,alternative:"Y"},{letter:/[\u00FE]/g,alternative:"th"},{letter:/[\u00DE]/g,alternative:"Th"},{letter:/[\u00E6\u04D5]/g,alternative:"ae"},{letter:/[\u00C6\u04D4]/g,alternative:"Ae"}],fa:[{letter:/[\u00E1]/g,alternative:"a"},{letter:/[\u00C1]/g,alternative:"A"},{letter:/[\u00F0]/g,alternative:"d"},{letter:/[\u00D0]/g,alternative:"D"},{letter:/[\u00ED]/g,alternative:"i"},{letter:/[\u00CD]/g,alternative:"I"},{letter:/[\u00FD]/g,alternative:"y"},{letter:/[\u00DD]/g,alternative:"Y"},{letter:/[\u00FA]/g,alternative:"u"},{letter:/[\u00DA]/g,alternative:"U"},{letter:/[\u00F3\u00F8]/g,alternative:"o"},{letter:/[\u00D3\u00D8]/g,alternative:"O"},{letter:/[\u00E6\u04D5]/g,alternative:"ae"},{letter:/[\u00C6\u04D4]/g,alternative:"Ae"}],cs:[{letter:/[\u00E1]/g,alternative:"a"},{letter:/[\u00C1]/g,alternative:"A"},{letter:/[\u010D]/g,alternative:"c"},{letter:/[\u010C]/g,alternative:"C"},{letter:/[\u010F]/g,alternative:"d"},{letter:/[\u010E]/g,alternative:"D"},{letter:/[\u00ED]/g,alternative:"i"},{letter:/[\u00CD]/g,alternative:"I"},{letter:/[\u0148]/g,alternative:"n"},{letter:/[\u0147]/g,alternative:"N"},{letter:/[\u00F3]/g,alternative:"o"},{letter:/[\u00D3]/g,alternative:"O"},{letter:/[\u0159]/g,alternative:"r"},{letter:/[\u0158]/g,alternative:"R"},{letter:/[\u0161]/g,alternative:"s"},{letter:/[\u0160]/g,alternative:"S"},{letter:/[\u0165]/g,alternative:"t"},{letter:/[\u0164]/g,alternative:"T"},{letter:/[\u00FD]/g,alternative:"y"},{letter:/[\u00DD]/g,alternative:"Y"},{letter:/[\u017E]/g,alternative:"z"},{letter:/[\u017D]/g,alternative:"Z"},{letter:/[\u00E9\u011B]/g,alternative:"e"},{letter:/[\u00C9\u011A]/g,alternative:"E"},{letter:/[\u00FA\u016F]/g,alternative:"u"},{letter:/[\u00DA\u016E]/g,alternative:"U"}],ru:[{letter:/[\u0430]/g,alternative:"a"},{letter:/[\u0410]/g,alternative:"A"},{letter:/[\u0431]/g,alternative:"b"},{letter:/[\u0411]/g,alternative:"B"},{letter:/[\u0432]/g,alternative:"v"},{letter:/[\u0412]/g,alternative:"V"},{letter:/[\u0433]/g,alternative:"g"},{letter:/[\u0413]/g,alternative:"G"},{letter:/[\u0434]/g,alternative:"d"},{letter:/[\u0414]/g,alternative:"D"},{letter:/[\u0435]/g,alternative:"e"},{letter:/[\u0415]/g,alternative:"E"},{letter:/[\u0436]/g,alternative:"zh"},{letter:/[\u0416]/g,alternative:"Zh"},{letter:/[\u0437]/g,alternative:"z"},{letter:/[\u0417]/g,alternative:"Z"},{letter:/[\u0456\u0438\u0439]/g,alternative:"i"},{letter:/[\u0406\u0418\u0419]/g,alternative:"I"},{letter:/[\u043A]/g,alternative:"k"},{letter:/[\u041A]/g,alternative:"K"},{letter:/[\u043B]/g,alternative:"l"},{letter:/[\u041B]/g,alternative:"L"},{letter:/[\u043C]/g,alternative:"m"},{letter:/[\u041C]/g,alternative:"M"},{letter:/[\u043D]/g,alternative:"n"},{letter:/[\u041D]/g,alternative:"N"},{letter:/[\u0440]/g,alternative:"r"},{letter:/[\u0420]/g,alternative:"R"},{letter:/[\u043E]/g,alternative:"o"},{letter:/[\u041E]/g,alternative:"O"},{letter:/[\u043F]/g,alternative:"p"},{letter:/[\u041F]/g,alternative:"P"},{letter:/[\u0441]/g,alternative:"s"},{letter:/[\u0421]/g,alternative:"S"},{letter:/[\u0442]/g,alternative:"t"},{letter:/[\u0422]/g,alternative:"T"},{letter:/[\u0443]/g,alternative:"u"},{letter:/[\u0423]/g,alternative:"U"},{letter:/[\u0444]/g,alternative:"f"},{letter:/[\u0424]/g,alternative:"F"},{letter:/[\u0445]/g,alternative:"kh"},{letter:/[\u0425]/g,alternative:"Kh"},{letter:/[\u0446]/g,alternative:"ts"},{letter:/[\u0426]/g,alternative:"Ts"},{letter:/[\u0447]/g,alternative:"ch"},{letter:/[\u0427]/g,alternative:"Ch"},{letter:/[\u0448]/g,alternative:"sh"},{letter:/[\u0428]/g,alternative:"Sh"},{letter:/[\u0449]/g,alternative:"shch"},{letter:/[\u0429]/g,alternative:"Shch"},{letter:/[\u044A]/g,alternative:"ie"},{letter:/[\u042A]/g,alternative:"Ie"},{letter:/[\u044B]/g,alternative:"y"},{letter:/[\u042B]/g,alternative:"Y"},{letter:/[\u044C]/g,alternative:""},{letter:/[\u042C]/g,alternative:""},{letter:/[\u0451\u044D]/g,alternative:"e"},{letter:/[\u0401\u042D]/g,alternative:"E"},{letter:/[\u044E]/g,alternative:"iu"},{letter:/[\u042E]/g,alternative:"Iu"},{letter:/[\u044F]/g,alternative:"ia"},{letter:/[\u042F]/g,alternative:"Ia"}],eo:[{letter:/[\u0109]/g,alternative:"ch"},{letter:/[\u0108]/g,alternative:"Ch"},{letter:/[\u011d]/g,alternative:"gh"},{letter:/[\u011c]/g,alternative:"Gh"},{letter:/[\u0125]/g,alternative:"hx"},{letter:/[\u0124]/g,alternative:"Hx"},{letter:/[\u0135]/g,alternative:"jx"},{letter:/[\u0134]/g,alternative:"Jx"},{letter:/[\u015d]/g,alternative:"sx"},{letter:/[\u015c]/g,alternative:"Sx"},{letter:/[\u016d]/g,alternative:"ux"},{letter:/[\u016c]/g,alternative:"Ux"}],af:[{letter:/[\u00E8\u00EA\u00EB]/g,alternative:"e"},{letter:/[\u00CB\u00C8\u00CA]/g,alternative:"E"},{letter:/[\u00EE\u00EF]/g,alternative:"i"},{letter:/[\u00CE\u00CF]/g,alternative:"I"},{letter:/[\u00F4\u00F6]/g,alternative:"o"},{letter:/[\u00D4\u00D6]/g,alternative:"O"},{letter:/[\u00FB\u00FC]/g,alternative:"u"},{letter:/[\u00DB\u00DC]/g,alternative:"U"}],ca:[{letter:/[\u00E0]/g,alternative:"a"},{letter:/[\u00C0]/g,alternative:"A"},{letter:/[\u00E9|\u00E8]/g,alternative:"e"},{letter:/[\u00C9|\u00C8]/g,alternative:"E"},{letter:/[\u00ED|\u00EF]/g,alternative:"i"},{letter:/[\u00CD|\u00CF]/g,alternative:"I"},{letter:/[\u00F3|\u00F2]/g,alternative:"o"},{letter:/[\u00D3|\u00D2]/g,alternative:"O"},{letter:/[\u00FA|\u00FC]/g,alternative:"u"},{letter:/[\u00DA|\u00DC]/g,alternative:"U"},{letter:/[\u00E7]/g,alternative:"c"},{letter:/[\u00C7]/g,alternative:"C"}],ast:[{letter:/[\u00F1]/g,alternative:"n"},{letter:/[\u00D1]/g,alternative:"N"}],an:[{letter:/[\u00FC]/g,alternative:"u"},{letter:/[\u00F1]/g,alternative:"ny"},{letter:/[\u00E7]/g,alternative:"c"},{letter:/[\u00ED]/g,alternative:"i"},{letter:/[\u00F3]/g,alternative:"o"},{letter:/[\u00E1]/g,alternative:"a"},{letter:/[\u00DC]/g,alternative:"U"},{letter:/[\u00D1]/g,alternative:"Ny"},{letter:/[\u00C7]/g,alternative:"C"},{letter:/[\u00CD]/g,alternative:"I"},{letter:/[\u00D3]/g,alternative:"O"},{letter:/[\u00C1]/g,alternative:"A"}],ay:[{letter:/(([\u00EF])|([\u00ED]))/g,alternative:"i"},{letter:/(([\u00CF])|([\u00CD]))/g,alternative:"I"},{letter:/[\u00E4]/g,alternative:"a"},{letter:/[\u00C4]/g,alternative:"A"},{letter:/[\u00FC]/g,alternative:"u"},{letter:/[\u00DC]/g,alternative:"U"},{letter:/[\u0027]/g,alternative:""},{letter:/[\u00F1]/g,alternative:"n"},{letter:/[\u00D1]/g,alternative:"N"}],en:[{letter:/[\u00E6\u04D5]/g,alternative:"ae"},{letter:/[\u00C6\u04D4]/g,alternative:"Ae"},{letter:/[\u0153]/g,alternative:"oe"},{letter:/[\u0152]/g,alternative:"Oe"},{letter:/[\u00EB\u00E9]/g,alternative:"e"},{letter:/[\u00C9\u00CB]/g,alternative:"E"},{letter:/[\u00F4\u00F6]/g,alternative:"o"},{letter:/[\u00D4\u00D6]/g,alternative:"O"},{letter:/[\u00EF]/g,alternative:"i"},{letter:/[\u00CF]/g,alternative:"I"},{letter:/[\u00E7]/g,alternative:"c"},{letter:/[\u00C7]/g,alternative:"C"},{letter:/[\u00F1]/g,alternative:"n"},{letter:/[\u00D1]/g,alternative:"N"},{letter:/[\u00FC]/g,alternative:"u"},{letter:/[\u00DC]/g,alternative:"U"},{letter:/[\u00E4]/g,alternative:"a"},{letter:/[\u00C4]/g,alternative:"A"}],fr:[{letter:/[\u00E6\u04D5]/g,alternative:"ae"},{letter:/[\u00C6\u04D4]/g,alternative:"Ae"},{letter:/[\u0153]/g,alternative:"oe"},{letter:/[\u0152]/g,alternative:"Oe"},{letter:/[\u00E9\u00E8\u00EB\u00EA]/g,alternative:"e"},{letter:/[\u00C9\u00C8\u00CB\u00CA]/g,alternative:"E"},{letter:/[\u00E0\u00E2]/g,alternative:"a"},{letter:/[\u00C0\u00C2]/g,alternative:"A"},{letter:/[\u00EF\u00EE]/g,alternative:"i"},{letter:/[\u00CF\u00CE]/g,alternative:"I"},{letter:/[\u00F9\u00FB\u00FC]/g,alternative:"u"},{letter:/[\u00D9\u00DB\u00DC]/g,alternative:"U"},{letter:/[\u00F4]/g,alternative:"o"},{letter:/[\u00D4]/g,alternative:"O"},{letter:/[\u00FF]/g,alternative:"y"},{letter:/[\u0178]/g,alternative:"Y"},{letter:/[\u00E7]/g,alternative:"c"},{letter:/[\u00C7]/g,alternative:"C"},{letter:/[\u00F1]/g,alternative:"n"},{letter:/[\u00D1]/g,alternative:"N"}],it:[{letter:/[\u00E0]/g,alternative:"a"},{letter:/[\u00C0]/g,alternative:"A"},{letter:/[\u00E9\u00E8]/g,alternative:"e"},{letter:/[\u00C9\u00C8]/g,alternative:"E"},{letter:/[\u00EC\u00ED\u00EE]/g,alternative:"i"},{letter:/[\u00CC\u00CD\u00CE]/g,alternative:"I"},{letter:/[\u00F3\u00F2]/g,alternative:"o"},{letter:/[\u00D3\u00D2]/g,alternative:"O"},{letter:/[\u00F9\u00FA]/g,alternative:"u"},{letter:/[\u00D9\u00DA]/g,alternative:"U"}],nl:[{letter:/[\u00E7]/g,alternative:"c"},{letter:/[\u00C7]/g,alternative:"C"},{letter:/[\u00F1]/g,alternative:"n"},{letter:/[\u00D1]/g,alternative:"N"},{letter:/[\u00E9\u00E8\u00EA\u00EB]/g,alternative:"e"},{letter:/[\u00C9\u00C8\u00CA\u00CB]/g,alternative:"E"},{letter:/[\u00F4\u00F6]/g,alternative:"o"},{letter:/[\u00D4\u00D6]/g,alternative:"O"},{letter:/[\u00EF]/g,alternative:"i"},{letter:/[\u00CF]/g,alternative:"I"},{letter:/[\u00FC]/g,alternative:"u"},{letter:/[\u00DC]/g,alternative:"U"},{letter:/[\u00E4]/g,alternative:"a"},{letter:/[\u00C4]/g,alternative:"A"}],bm:[{letter:/[\u025B]/g,alternative:"e"},{letter:/[\u0190]/g,alternative:"E"},{letter:/[\u0272]/g,alternative:"ny"},{letter:/[\u019D]/g,alternative:"Ny"},{letter:/[\u014B]/g,alternative:"ng"},{letter:/[\u014A]/g,alternative:"Ng"},{letter:/[\u0254]/g,alternative:"o"},{letter:/[\u0186]/g,alternative:"O"}],uk:[{letter:/[\u0431]/g,alternative:"b"},{letter:/[\u0411]/g,alternative:"B"},{letter:/[\u0432]/g,alternative:"v"},{letter:/[\u0412]/g,alternative:"V"},{letter:/[\u0433]/g,alternative:"h"},{letter:/[\u0413]/g,alternative:"H"},{letter:/[\u0491]/g,alternative:"g"},{letter:/[\u0490]/g,alternative:"G"},{letter:/[\u0434]/g,alternative:"d"},{letter:/[\u0414]/g,alternative:"D"},{letter:/[\u043A]/g,alternative:"k"},{letter:/[\u041A]/g,alternative:"K"},{letter:/[\u043B]/g,alternative:"l"},{letter:/[\u041B]/g,alternative:"L"},{letter:/[\u043C]/g,alternative:"m"},{letter:/[\u041C]/g,alternative:"M"},{letter:/[\u0070]/g,alternative:"r"},{letter:/[\u0050]/g,alternative:"R"},{letter:/[\u043F]/g,alternative:"p"},{letter:/[\u041F]/g,alternative:"P"},{letter:/[\u0441]/g,alternative:"s"},{letter:/[\u0421]/g,alternative:"S"},{letter:/[\u0442]/g,alternative:"t"},{letter:/[\u0422]/g,alternative:"T"},{letter:/[\u0443]/g,alternative:"u"},{letter:/[\u0423]/g,alternative:"U"},{letter:/[\u0444]/g,alternative:"f"},{letter:/[\u0424]/g,alternative:"F"},{letter:/[\u0445]/g,alternative:"kh"},{letter:/[\u0425]/g,alternative:"Kh"},{letter:/[\u0446]/g,alternative:"ts"},{letter:/[\u0426]/g,alternative:"Ts"},{letter:/[\u0447]/g,alternative:"ch"},{letter:/[\u0427]/g,alternative:"Ch"},{letter:/[\u0448]/g,alternative:"sh"},{letter:/[\u0428]/g,alternative:"Sh"},{letter:/[\u0449]/g,alternative:"shch"},{letter:/[\u0429]/g,alternative:"Shch"},{letter:/[\u044C\u042C]/g,alternative:""},{letter:/[\u0436]/g,alternative:"zh"},{letter:/[\u0416]/g,alternative:"Zh"},{letter:/[\u0437]/g,alternative:"z"},{letter:/[\u0417]/g,alternative:"Z"},{letter:/[\u0438]/g,alternative:"y"},{letter:/[\u0418]/g,alternative:"Y"},{letter:/^[\u0454]/g,alternative:"ye"},{letter:/[\s][\u0454]/g,alternative:" ye"},{letter:/[\u0454]/g,alternative:"ie"},{letter:/^[\u0404]/g,alternative:"Ye"},{letter:/[\s][\u0404]/g,alternative:" Ye"},{letter:/[\u0404]/g,alternative:"IE"},{letter:/^[\u0457]/g,alternative:"yi"},{letter:/[\s][\u0457]/g,alternative:" yi"},{letter:/[\u0457]/g,alternative:"i"},{letter:/^[\u0407]/g,alternative:"Yi"},{letter:/[\s][\u0407]/g,alternative:" Yi"},{letter:/[\u0407]/g,alternative:"I"},{letter:/^[\u0439]/g,alternative:"y"},{letter:/[\s][\u0439]/g,alternative:" y"},{letter:/[\u0439]/g,alternative:"i"},{letter:/^[\u0419]/g,alternative:"Y"},{letter:/[\s][\u0419]/g,alternative:" Y"},{letter:/[\u0419]/g,alternative:"I"},{letter:/^[\u044E]/g,alternative:"yu"},{letter:/[\s][\u044E]/g,alternative:" yu"},{letter:/[\u044E]/g,alternative:"iu"},{letter:/^[\u042E]/g,alternative:"Yu"},{letter:/[\s][\u042E]/g,alternative:" Yu"},{letter:/[\u042E]/g,alternative:"IU"},{letter:/^[\u044F]/g,alternative:"ya"},{letter:/[\s][\u044F]/g,alternative:" ya"},{letter:/[\u044F]/g,alternative:"ia"},{letter:/^[\u042F]/g,alternative:"Ya"},{letter:/[\s][\u042F]/g,alternative:" Ya"},{letter:/[\u042F]/g,alternative:"IA"}],br:[{letter:/\u0063\u0027\u0068/g,alternative:"ch"},{letter:/\u0043\u0027\u0048/g,alternative:"CH"},{letter:/[\u00e2]/g,alternative:"a"},{letter:/[\u00c2]/g,alternative:"A"},{letter:/[\u00ea]/g,alternative:"e"},{letter:/[\u00ca]/g,alternative:"E"},{letter:/[\u00ee]/g,alternative:"i"},{letter:/[\u00ce]/g,alternative:"I"},{letter:/[\u00f4]/g,alternative:"o"},{letter:/[\u00d4]/g,alternative:"O"},{letter:/[\u00fb\u00f9\u00fc]/g,alternative:"u"},{letter:/[\u00db\u00d9\u00dc]/g,alternative:"U"},{letter:/[\u00f1]/g,alternative:"n"},{letter:/[\u00d1]/g,alternative:"N"}],ch:[{letter:/[\u0027]/g,alternative:""},{letter:/[\u00e5]/g,alternative:"a"},{letter:/[\u00c5]/g,alternative:"A"},{letter:/[\u00f1]/g,alternative:"n"},{letter:/[\u00d1]/g,alternative:"N"}],co:[{letter:/[\u00e2\u00e0]/g,alternative:"a"},{letter:/[\u00c2\u00c0]/g,alternative:"A"},{letter:/[\u00e6\u04d5]/g,alternative:"ae"},{letter:/[\u00c6\u04d4]/g,alternative:"Ae"},{letter:/[\u00e7]/g,alternative:"c"},{letter:/[\u00c7]/g,alternative:"C"},{letter:/[\u00e9\u00ea\u00e8\u00eb]/g,alternative:"e"},{letter:/[\u00c9\u00ca\u00c8\u00cb]/g,alternative:"E"},{letter:/[\u00ec\u00ee\u00ef]/g,alternative:"i"},{letter:/[\u00cc\u00ce\u00cf]/g,alternative:"I"},{letter:/[\u00f1]/g,alternative:"n"},{letter:/[\u00d1]/g,alternative:"N"},{letter:/[\u00f4\u00f2]/g,alternative:"o"},{letter:/[\u00d4\u00d2]/g,alternative:"O"},{letter:/[\u0153]/g,alternative:"oe"},{letter:/[\u0152]]/g,alternative:"Oe"},{letter:/[\u00f9\u00fc]/g,alternative:"u"},{letter:/[\u00d9\u00dc]/g,alternative:"U"},{letter:/[\u00ff]/g,alternative:"y"},{letter:/[\u0178]/g,alternative:"Y"}],csb:[{letter:/[\u0105\u00e3]/g,alternative:"a"},{letter:/[\u0104\u00c3]/g,alternative:"A"},{letter:/[\u00e9\u00eb]/g,alternative:"e"},{letter:/[\u00c9\u00cb]/g,alternative:"E"},{letter:/[\u0142]/g,alternative:"l"},{letter:/[\u0141]/g,alternative:"L"},{letter:/[\u0144]/g,alternative:"n"},{letter:/[\u0143]/g,alternative:"N"},{letter:/[\u00f2\u00f3\u00f4]/g,alternative:"o"},{letter:/[\u00d2\u00d3\u00d4]/g,alternative:"O"},{letter:/[\u00f9]/g,alternative:"u"},{letter:/[\u00d9]/g,alternative:"U"},{letter:/[\u017c]/g,alternative:"z"},{letter:/[\u017b]/g,alternative:"Z"}],cy:[{letter:/[\u00e2]/g,alternative:"a"},{letter:/[\u00c2]/g,alternative:"A"},{letter:/[\u00ea]/g,alternative:"e"},{letter:/[\u00ca]/g,alternative:"E"},{letter:/[\u00ee]/g,alternative:"i"},{letter:/[\u00ce]/g,alternative:"I"},{letter:/[\u00f4]/g,alternative:"o"},{letter:/[\u00d4]/g,alternative:"O"},{letter:/[\u00fb]/g,alternative:"u"},{letter:/[\u00db]/g,alternative:"U"},{letter:/[\u0175]/g,alternative:"w"},{letter:/[\u0174]/g,alternative:"W"},{letter:/[\u0177]/g,alternative:"y"},{letter:/[\u0176]/g,alternative:"Y"}],ee:[{letter:/[\u0256]/g,alternative:"d"},{letter:/[\u0189]/g,alternative:"D"},{letter:/[\u025b]/g,alternative:"e"},{letter:/[\u0190]/g,alternative:"E"},{letter:/[\u0192]/g,alternative:"f"},{letter:/[\u0191]/g,alternative:"F"},{letter:/[\u0263]/g,alternative:"g"},{letter:/[\u0194]/g,alternative:"G"},{letter:/[\u014b]/g,alternative:"ng"},{letter:/[\u014a]/g,alternative:"Ng"},{letter:/[\u0254]/g,alternative:"o"},{letter:/[\u0186]/g,alternative:"O"},{letter:/[\u028b]/g,alternative:"w"},{letter:/[\u01b2]/g,alternative:"W"},{letter:/\u0061\u0303/g,alternative:"a"},{letter:/[\u00e1\u00e0\u01ce\u00e2\u00e3]/g,alternative:"a"},{letter:/\u0041\u0303/g,alternative:"A"},{letter:/[\u00c1\u00c0\u01cd\u00c2\u00c3]/g,alternative:"A"},{letter:/[\u00e9\u00e8\u011b\u00ea]/g,alternative:"e"},{letter:/[\u00c9\u00c8\u011a\u00ca]/g,alternative:"E"},{letter:/[\u00f3\u00f2\u01d2\u00f4]/g,alternative:"o"},{letter:/[\u00d3\u00d2\u01d1\u00d4]/g,alternative:"O"},{letter:/[\u00fa\u00f9\u01d4\u00fb]/g,alternative:"u"},{letter:/[\u00da\u00d9\u01d3\u00db]/g,alternative:"U"},{letter:/[\u00ed\u00ec\u01d0\u00ee]/g,alternative:"i"},{letter:/[\u00cd\u00cc\u01cf\u00ce]/g,alternative:"I"}],et:[{letter:/[\u0161]/g,alternative:"sh"},{letter:/[\u0160]/g,alternative:"Sh"},{letter:/[\u017e]/g,alternative:"zh"},{letter:/[\u017d]/g,alternative:"Zh"},{letter:/[\u00f5\u00f6]/g,alternative:"o"},{letter:/[\u00d6\u00d5]/g,alternative:"O"},{letter:/[\u00e4]/g,alternative:"a"},{letter:/[\u00c4]/g,alternative:"A"},{letter:/[\u00fc]/g,alternative:"u"},{letter:/[\u00dc]/g,alternative:"U"}],eu:[{letter:/[\u00f1]/g,alternative:"n"},{letter:/[\u00d1]/g,alternative:"N"},{letter:/[\u00e7]/g,alternative:"c"},{letter:/[\u00c7]/g,alternative:"C"},{letter:/[\u00fc]/g,alternative:"u"},{letter:/[\u00dc]/g,alternative:"U"}],fuc:[{letter:/[\u0253]/g,alternative:"b"},{letter:/[\u0181]/g,alternative:"B"},{letter:/[\u0257]/g,alternative:"d"},{letter:/[\u018a]/g,alternative:"D"},{letter:/[\u014b]/g,alternative:"ng"},{letter:/[\u014a]/g,alternative:"Ng"},{letter:/[\u0272\u00f1]/g,alternative:"ny"},{letter:/[\u019d\u00d1]/g,alternative:"Ny"},{letter:/[\u01b4]/g,alternative:"y"},{letter:/[\u01b3]/g,alternative:"Y"},{letter:/[\u0260]/g,alternative:"g"},{letter:/[\u0193]/g,alternative:"G"}],fj:[{letter:/[\u0101]/g,alternative:"a"},{letter:/[\u0100]/g,alternative:"A"},{letter:/[\u0113]/g,alternative:"e"},{letter:/[\u0112]/g,alternative:"E"},{letter:/[\u012b]/g,alternative:"i"},{letter:/[\u012a]/g,alternative:"I"},{letter:/[\u016b]/g,alternative:"u"},{letter:/[\u016a]/g,alternative:"U"},{letter:/[\u014d]/g,alternative:"o"},{letter:/[\u014c]/g,alternative:"O"}],frp:[{letter:/[\u00e2]/g,alternative:"a"},{letter:/[\u00c2]/g,alternative:"A"},{letter:/[\u00ea\u00e8\u00e9]/g,alternative:"e"},{letter:/[\u00ca\u00c8\u00c9]/g,alternative:"E"},{letter:/[\u00ee]/g,alternative:"i"},{letter:/[\u00ce]/g,alternative:"I"},{letter:/[\u00fb\u00fc]/g,alternative:"u"},{letter:/[\u00db\u00dc]/g,alternative:"U"},{letter:/[\u00f4]/g,alternative:"o"},{letter:/[\u00d4]/g,alternative:"O"}],fur:[{letter:/[\u00E7]/g,alternative:"c"},{letter:/[\u00C7]/g,alternative:"C"},{letter:/[\u00e0\u00e2]/g,alternative:"a"},{letter:/[\u00c0\u00c2]/g,alternative:"A"},{letter:/[\u00e8\u00ea]/g,alternative:"e"},{letter:/[\u00c8\u00ca]/g,alternative:"E"},{letter:/[\u00ec\u00ee]/g,alternative:"i"},{letter:/[\u00cc\u00ce]/g,alternative:"I"},{letter:/[\u00f2\u00f4]/g,alternative:"o"},{letter:/[\u00d2\u00d4]/g,alternative:"O"},{letter:/[\u00f9\u00fb]/g,alternative:"u"},{letter:/[\u00d9\u00db]/g,alternative:"U"},{letter:/[\u010d]/g,alternative:"c"},{letter:/[\u010c]/g,alternative:"C"},{letter:/[\u011f]/g,alternative:"g"},{letter:/[\u011e]/g,alternative:"G"},{letter:/[\u0161]/g,alternative:"s"},{letter:/[\u0160]/g,alternative:"S"}],fy:[{letter:/[\u00e2\u0101\u00e4\u00e5]/g,alternative:"a"},{letter:/[\u00c2\u0100\u00c4\u00c5]/g,alternative:"A"},{letter:/[\u00ea\u00e9\u0113]/g,alternative:"e"},{letter:/[\u00ca\u00c9\u0112]/g,alternative:"E"},{letter:/[\u00f4\u00f6]/g,alternative:"o"},{letter:/[\u00d4\u00d6]/g,alternative:"O"},{letter:/[\u00fa\u00fb\u00fc]/g,alternative:"u"},{letter:/[\u00da\u00db\u00dc]/g,alternative:"U"},{letter:/[\u00ed]/g,alternative:"i"},{letter:/[\u00cd]/g,alternative:"I"},{letter:/[\u0111\u00f0]/g,alternative:"d"},{letter:/[\u0110\u00d0]/g,alternative:"D"}],ga:[{letter:/[\u00e1]/g,alternative:"a"},{letter:/[\u00c1]/g,alternative:"A"},{letter:/[\u00e9]/g,alternative:"e"},{letter:/[\u00c9]/g,alternative:"E"},{letter:/[\u00f3]/g,alternative:"o"},{letter:/[\u00d3]/g,alternative:"O"},{letter:/[\u00fa]/g,alternative:"u"},{letter:/[\u00da]/g,alternative:"U"},{letter:/[\u00ed]/g,alternative:"i"},{letter:/[\u00cd]/g,alternative:"I"}],gd:[{letter:/[\u00e0]/g,alternative:"a"},{letter:/[\u00c0]/g,alternative:"A"},{letter:/[\u00e8]/g,alternative:"e"},{letter:/[\u00c8]/g,alternative:"E"},{letter:/[\u00f2]/g,alternative:"o"},{letter:/[\u00d2]/g,alternative:"O"},{letter:/[\u00f9]/g,alternative:"u"},{letter:/[\u00d9]/g,alternative:"U"},{letter:/[\u00ec]/g,alternative:"i"},{letter:/[\u00cc]/g,alternative:"I"}],gl:[{letter:/[\u00e1\u00e0]/g,alternative:"a"},{letter:/[\u00c1\u00c0]/g,alternative:"A"},{letter:/[\u00e9\u00ea]/g,alternative:"e"},{letter:/[\u00c9\u00ca]/g,alternative:"E"},{letter:/[\u00ed\u00ef]/g,alternative:"i"},{letter:/[\u00cd\u00cf]/g,alternative:"I"},{letter:/[\u00f3]/g,alternative:"o"},{letter:/[\u00d3]/g,alternative:"O"},{letter:/[\u00fa\u00fc]/g,alternative:"u"},{letter:/[\u00da\u00dc]/g,alternative:"U"},{letter:/[\u00e7]/g,alternative:"c"},{letter:/[\u00c7]/g,alternative:"C"},{letter:/[\u00f1]/g,alternative:"n"},{letter:/[\u00d1]/g,alternative:"N"}],gn:[{letter:/[\u2019]/g,alternative:""},{letter:/\u0067\u0303/g,alternative:"g"},{letter:/\u0047\u0303/g,alternative:"G"},{letter:/[\u00e3]/g,alternative:"a"},{letter:/[\u00c3]/g,alternative:"A"},{letter:/[\u1ebd]/g,alternative:"e"},{letter:/[\u1ebc]/g,alternative:"E"},{letter:/[\u0129]/g,alternative:"i"},{letter:/[\u0128]/g,alternative:"I"},{letter:/[\u00f5]/g,alternative:"o"},{letter:/[\u00d5]/g,alternative:"O"},{letter:/[\u00f1]/g,alternative:"n"},{letter:/[\u00d1]/g,alternative:"N"},{letter:/[\u0169]/g,alternative:"u"},{letter:/[\u0168]/g,alternative:"U"},{letter:/[\u1ef9]/g,alternative:"y"},{letter:/[\u1ef8]/g,alternative:"Y"}],gsw:[{letter:/[\u00e4]/g,alternative:"a"},{letter:/[\u00c4]/g,alternative:"A"},{letter:/[\u00f6]/g,alternative:"o"},{letter:/[\u00d6]/g,alternative:"O"},{letter:/[\u00fc]/g,alternative:"u"},{letter:/[\u00dc]/g,alternative:"U"}],hat:[{letter:/[\u00e8]/g,alternative:"e"},{letter:/[\u00c8]/g,alternative:"E"},{letter:/[\u00f2]/g,alternative:"o"},{letter:/[\u00d2]/g,alternative:"O"}],haw:[{letter:/[\u02bb\u0027\u2019]/g,alternative:""},{letter:/[\u0101]/g,alternative:"a"},{letter:/[\u0113]/g,alternative:"e"},{letter:/[\u012b]/g,alternative:"i"},{letter:/[\u014d]/g,alternative:"o"},{letter:/[\u016b]/g,alternative:"u"},{letter:/[\u0100]/g,alternative:"A"},{letter:/[\u0112]/g,alternative:"E"},{letter:/[\u012a]/g,alternative:"I"},{letter:/[\u014c]/g,alternative:"O"},{letter:/[\u016a]/g,alternative:"U"}],hr:[{letter:/[\u010d\u0107]/g,alternative:"c"},{letter:/[\u010c\u0106]/g,alternative:"C"},{letter:/[\u0111]/g,alternative:"dj"},{letter:/[\u0110]/g,alternative:"Dj"},{letter:/[\u0161]/g,alternative:"s"},{letter:/[\u0160]/g,alternative:"S"},{letter:/[\u017e]/g,alternative:"z"},{letter:/[\u017d]/g,alternative:"Z"},{letter:/[\u01c4]/g,alternative:"DZ"},{letter:/[\u01c5]/g,alternative:"Dz"},{letter:/[\u01c6]/g,alternative:"dz"}],ka:[{letter:/[\u10d0]/g,alternative:"a"},{letter:/[\u10d1]/g,alternative:"b"},{letter:/[\u10d2]/g,alternative:"g"},{letter:/[\u10d3]/g,alternative:"d"},{letter:/[\u10d4]/g,alternative:"e"},{letter:/[\u10d5]/g,alternative:"v"},{letter:/[\u10d6]/g,alternative:"z"},{letter:/[\u10d7]/g,alternative:"t"},{letter:/[\u10d8]/g,alternative:"i"},{letter:/[\u10d9]/g,alternative:"k"},{letter:/[\u10da]/g,alternative:"l"},{letter:/[\u10db]/g,alternative:"m"},{letter:/[\u10dc]/g,alternative:"n"},{letter:/[\u10dd]/g,alternative:"o"},{letter:/[\u10de]/g,alternative:"p"},{letter:/[\u10df]/g,alternative:"zh"},{letter:/[\u10e0]/g,alternative:"r"},{letter:/[\u10e1]/g,alternative:"s"},{letter:/[\u10e2]/g,alternative:"t"},{letter:/[\u10e3]/g,alternative:"u"},{letter:/[\u10e4]/g,alternative:"p"},{letter:/[\u10e5]/g,alternative:"k"},{letter:/[\u10e6]/g,alternative:"gh"},{letter:/[\u10e7]/g,alternative:"q"},{letter:/[\u10e8]/g,alternative:"sh"},{letter:/[\u10e9]/g,alternative:"ch"},{letter:/[\u10ea]/g,alternative:"ts"},{letter:/[\u10eb]/g,alternative:"dz"},{letter:/[\u10ec]/g,alternative:"ts"},{letter:/[\u10ed]/g,alternative:"ch"},{letter:/[\u10ee]/g,alternative:"kh"},{letter:/[\u10ef]/g,alternative:"j"},{letter:/[\u10f0]/g,alternative:"h"}],kal:[{letter:/[\u00E5]/g,alternative:"aa"},{letter:/[\u00C5]/g,alternative:"Aa"},{letter:/[\u00E6\u04D5]/g,alternative:"ae"},{letter:/[\u00C6\u04D4]/g,alternative:"Ae"},{letter:/[\u00C4]/g,alternative:"Ae"},{letter:/[\u00F8]/g,alternative:"oe"},{letter:/[\u00D8]/g,alternative:"Oe"}],kin:[{letter:/[\u2019\u0027]/g,alternative:""}],lb:[{letter:/[\u00e4]/g,alternative:"a"},{letter:/[\u00c4]/g,alternative:"A"},{letter:/[\u00eb\u00e9]/g,alternative:"e"},{letter:/[\u00cb\u00c9]/g,alternative:"E"}],li:[{letter:/[\u00e1\u00e2\u00e0\u00e4]/g,alternative:"a"},{letter:/[\u00c1\u00c2\u00c0\u00c4]/g,alternative:"A"},{letter:/[\u00eb\u00e8\u00ea]/g,alternative:"e"},{letter:/[\u00cb\u00c8\u00ca]/g,alternative:"E"},{letter:/[\u00f6\u00f3]/g,alternative:"o"},{letter:/[\u00d6\u00d3]/g,alternative:"O"}],lin:[{letter:/[\u00e1\u00e2\u01ce]/g,alternative:"a"},{letter:/[\u00c1\u00c2\u01cd]/g,alternative:"A"},{letter:/\u025b\u0301/g,alternative:"e"},{letter:/\u025b\u0302/g,alternative:"e"},{letter:/\u025b\u030c/g,alternative:"e"},{letter:/[\u00e9\u00ea\u011b\u025b]/g,alternative:"e"},{letter:/\u0190\u0301/g,alternative:"E"},{letter:/\u0190\u0302/g,alternative:"E"},{letter:/\u0190\u030c/g,alternative:"E"},{letter:/[\u00c9\u00ca\u011a\u0190]/g,alternative:"E"},{letter:/[\u00ed\u00ee\u01d0]/g,alternative:"i"},{letter:/[\u00cd\u00ce\u01cf]/g,alternative:"I"},{letter:/\u0254\u0301/g,alternative:"o"},{letter:/\u0254\u0302/g,alternative:"o"},{letter:/\u0254\u030c/g,alternative:"o"},{letter:/[\u00f3\u00f4\u01d2\u0254]/g,alternative:"o"},{letter:/\u0186\u0301/g,alternative:"O"},{letter:/\u0186\u0302/g,alternative:"O"},{letter:/\u0186\u030c/g,alternative:"O"},{letter:/[\u00d3\u00d4\u01d1\u0186]/g,alternative:"O"},{letter:/[\u00fa]/g,alternative:"u"},{letter:/[\u00da]/g,alternative:"U"}],lt:[{letter:/[\u0105]/g,alternative:"a"},{letter:/[\u0104]/g,alternative:"A"},{letter:/[\u010d]/g,alternative:"c"},{letter:/[\u010c]/g,alternative:"C"},{letter:/[\u0119\u0117]/g,alternative:"e"},{letter:/[\u0118\u0116]/g,alternative:"E"},{letter:/[\u012f]/g,alternative:"i"},{letter:/[\u012e]/g,alternative:"I"},{letter:/[\u0161]/g,alternative:"s"},{letter:/[\u0160]/g,alternative:"S"},{letter:/[\u0173\u016b]/g,alternative:"u"},{letter:/[\u0172\u016a]/g,alternative:"U"},{letter:/[\u017e]/g,alternative:"z"},{letter:/[\u017d]/g,alternative:"Z"}],mg:[{letter:/[\u00f4]/g,alternative:"ao"},{letter:/[\u00d4]/g,alternative:"Ao"}],mk:[{letter:/[\u0430]/g,alternative:"a"},{letter:/[\u0410]/g,alternative:"A"},{letter:/[\u0431]/g,alternative:"b"},{letter:/[\u0411]/g,alternative:"B"},{letter:/[\u0432]/g,alternative:"v"},{letter:/[\u0412]/g,alternative:"V"},{letter:/[\u0433]/g,alternative:"g"},{letter:/[\u0413]/g,alternative:"G"},{letter:/[\u0434]/g,alternative:"d"},{letter:/[\u0414]/g,alternative:"D"},{letter:/[\u0453]/g,alternative:"gj"},{letter:/[\u0403]/g,alternative:"Gj"},{letter:/[\u0435]/g,alternative:"e"},{letter:/[\u0415]/g,alternative:"E"},{letter:/[\u0436]/g,alternative:"zh"},{letter:/[\u0416]/g,alternative:"Zh"},{letter:/[\u0437]/g,alternative:"z"},{letter:/[\u0417]/g,alternative:"Z"},{letter:/[\u0455]/g,alternative:"dz"},{letter:/[\u0405]/g,alternative:"Dz"},{letter:/[\u0438]/g,alternative:"i"},{letter:/[\u0418]/g,alternative:"I"},{letter:/[\u0458]/g,alternative:"j"},{letter:/[\u0408]/g,alternative:"J"},{letter:/[\u043A]/g,alternative:"k"},{letter:/[\u041A]/g,alternative:"K"},{letter:/[\u043B]/g,alternative:"l"},{letter:/[\u041B]/g,alternative:"L"},{letter:/[\u0459]/g,alternative:"lj"},{letter:/[\u0409]/g,alternative:"Lj"},{letter:/[\u043C]/g,alternative:"m"},{letter:/[\u041C]/g,alternative:"M"},{letter:/[\u043D]/g,alternative:"n"},{letter:/[\u041D]/g,alternative:"N"},{letter:/[\u045A]/g,alternative:"nj"},{letter:/[\u040A]/g,alternative:"Nj"},{letter:/[\u043E]/g,alternative:"o"},{letter:/[\u041E]/g,alternative:"O"},{letter:/[\u0440]/g,alternative:"r"},{letter:/[\u0420]/g,alternative:"R"},{letter:/[\u043F]/g,alternative:"p"},{letter:/[\u041F]/g,alternative:"P"},{letter:/[\u0441]/g,alternative:"s"},{letter:/[\u0421]/g,alternative:"S"},{letter:/[\u0442]/g,alternative:"t"},{letter:/[\u0422]/g,alternative:"T"},{letter:/[\u045C]/g,alternative:"kj"},{letter:/[\u040C]/g,alternative:"Kj"},{letter:/[\u0443]/g,alternative:"u"},{letter:/[\u0423]/g,alternative:"U"},{letter:/[\u0444]/g,alternative:"f"},{letter:/[\u0424]/g,alternative:"F"},{letter:/[\u0445]/g,alternative:"h"},{letter:/[\u0425]/g,alternative:"H"},{letter:/[\u0446]/g,alternative:"c"},{letter:/[\u0426]/g,alternative:"C"},{letter:/[\u0447]/g,alternative:"ch"},{letter:/[\u0427]/g,alternative:"Ch"},{letter:/[\u045F]/g,alternative:"dj"},{letter:/[\u040F]/g,alternative:"Dj"},{letter:/[\u0448]/g,alternative:"sh"},{letter:/[\u0428]/g,alternative:"Sh"}],mri:[{letter:/[\u0101]/g,alternative:"aa"},{letter:/[\u0100]/g,alternative:"Aa"},{letter:/[\u0113]/g,alternative:"ee"},{letter:/[\u0112]/g,alternative:"Ee"},{letter:/[\u012b]/g,alternative:"ii"},{letter:/[\u012a]/g,alternative:"Ii"},{letter:/[\u014d]/g,alternative:"oo"},{letter:/[\u014c]/g,alternative:"Oo"},{letter:/[\u016b]/g,alternative:"uu"},{letter:/[\u016a]/g,alternative:"Uu"}],mwl:[{letter:/[\u00e7]/g,alternative:"c"},{letter:/[\u00c7]/g,alternative:"C"},{letter:/[\u00e1]/g,alternative:"a"},{letter:/[\u00c1]/g,alternative:"A"},{letter:/[\u00e9\u00ea]/g,alternative:"e"},{letter:/[\u00c9\u00ca]/g,alternative:"E"},{letter:/[\u00ed]/g,alternative:"i"},{letter:/[\u00cd]/g,alternative:"I"},{letter:/[\u00f3\u00f4]/g,alternative:"o"},{letter:/[\u00d3\u00d4]/g,alternative:"O"},{letter:/[\u00fa\u0169]/g,alternative:"u"},{letter:/[\u00da\u0168]/g,alternative:"U"}],oci:[{letter:/[\u00e7]/g,alternative:"c"},{letter:/[\u00c7]/g,alternative:"C"},{letter:/[\u00e0\u00e1]/g,alternative:"a"},{letter:/[\u00c0\u00c1]/g,alternative:"A"},{letter:/[\u00e8\u00e9]/g,alternative:"e"},{letter:/[\u00c8\u00c9]/g,alternative:"E"},{letter:/[\u00ed\u00ef]/g,alternative:"i"},{letter:/[\u00cd\u00cf]/g,alternative:"I"},{letter:/[\u00f2\u00f3]/g,alternative:"o"},{letter:/[\u00d2\u00d3]/g,alternative:"O"},{letter:/[\u00fa\u00fc]/g,alternative:"u"},{letter:/[\u00da\u00dc]/g,alternative:"U"},{letter:/[\u00b7]/g,alternative:""}],orm:[{letter:/[\u0027]/g,alternative:""}],pt:[{letter:/[\u00e7]/g,alternative:"c"},{letter:/[\u00c7]/g,alternative:"C"},{letter:/[\u00e1\u00e2\u00e3\u00e0]/g,alternative:"a"},{letter:/[\u00c1\u00c2\u00c3\u00c0]/g,alternative:"A"},{letter:/[\u00e9\u00ea]/g,alternative:"e"},{letter:/[\u00c9\u00ca]/g,alternative:"E"},{letter:/[\u00ed]/g,alternative:"i"},{letter:/[\u00cd]/g,alternative:"I"},{letter:/[\u00f3\u00f4\u00f5]/g,alternative:"o"},{letter:/[\u00d3\u00d4\u00d5]/g,alternative:"O"},{letter:/[\u00fa]/g,alternative:"u"},{letter:/[\u00da]/g,alternative:"U"}],roh:[{letter:/[\u00e9\u00e8\u00ea]/g,alternative:"e"},{letter:/[\u00c9\u00c8\u00ca]/g,alternative:"E"},{letter:/[\u00ef]/g,alternative:"i"},{letter:/[\u00cf]/g,alternative:"I"},{letter:/[\u00f6]/g,alternative:"oe"},{letter:/[\u00d6]/g,alternative:"Oe"},{letter:/[\u00fc]/g,alternative:"ue"},{letter:/[\u00dc]/g,alternative:"Ue"},{letter:/[\u00e4]/g,alternative:"ae"},{letter:/[\u00c4]/g,alternative:"Ae"}],rup:[{letter:/[\u00e3]/g,alternative:"a"},{letter:/[\u00c3]/g,alternative:"A"}],ro:[{letter:/[\u0103\u00e2]/g,alternative:"a"},{letter:/[\u0102\u00c2]/g,alternative:"A"},{letter:/[\u00ee]/g,alternative:"i"},{letter:/[\u00ce]/g,alternative:"I"},{letter:/[\u0219\u015f]/g,alternative:"s"},{letter:/[\u0218\u015e]/g,alternative:"S"},{letter:/[\u021b\u0163]/g,alternative:"t"},{letter:/[\u021a\u0162]/g,alternative:"T"}],tlh:[{letter:/[\u2019\u0027]/g,alternative:""}],sk:[{letter:/[\u01c4]/g,alternative:"DZ"},{letter:/[\u01c5]/g,alternative:"Dz"},{letter:/[\u01c6]/g,alternative:"dz"},{letter:/[\u00e1\u00e4]/g,alternative:"a"},{letter:/[\u00c1\u00c4]/g,alternative:"A"},{letter:/[\u010d]/g,alternative:"c"},{letter:/[\u010c]/g,alternative:"C"},{letter:/[\u010f]/g,alternative:"d"},{letter:/[\u010e]/g,alternative:"D"},{letter:/[\u00e9]/g,alternative:"e"},{letter:/[\u00c9]/g,alternative:"E"},{letter:/[\u00ed]/g,alternative:"i"},{letter:/[\u00cd]/g,alternative:"I"},{letter:/[\u013e\u013a]/g,alternative:"l"},{letter:/[\u013d\u0139]/g,alternative:"L"},{letter:/[\u0148]/g,alternative:"n"},{letter:/[\u0147]/g,alternative:"N"},{letter:/[\u00f3\u00f4]/g,alternative:"o"},{letter:/[\u00d3\u00d4]/g,alternative:"O"},{letter:/[\u0155]/g,alternative:"r"},{letter:/[\u0154]/g,alternative:"R"},{letter:/[\u0161]/g,alternative:"s"},{letter:/[\u0160]/g,alternative:"S"},{letter:/[\u0165]/g,alternative:"t"},{letter:/[\u0164]/g,alternative:"T"},{letter:/[\u00fa]/g,alternative:"u"},{letter:/[\u00da]/g,alternative:"U"},{letter:/[\u00fd]/g,alternative:"y"},{letter:/[\u00dd]/g,alternative:"Y"},{letter:/[\u017e]/g,alternative:"z"},{letter:/[\u017d]/g,alternative:"Z"}],sl:[{letter:/[\u010d\u0107]/g,alternative:"c"},{letter:/[\u010c\u0106]/g,alternative:"C"},{letter:/[\u0111]/g,alternative:"d"},{letter:/[\u0110]/g,alternative:"D"},{letter:/[\u0161]/g,alternative:"s"},{letter:/[\u0160]/g,alternative:"S"},{letter:/[\u017e]/g,alternative:"z"},{letter:/[\u017d]/g,alternative:"Z"},{letter:/[\u00e0\u00e1\u0203\u0201]/g,alternative:"a"},{letter:/[\u00c0\u00c1\u0202\u0200]/g,alternative:"A"},{letter:/[\u00e8\u00e9\u0207\u0205]/g,alternative:"e"},{letter:/\u01dd\u0300/g,alternative:"e"},{letter:/\u01dd\u030f/g,alternative:"e"},{letter:/\u1eb9\u0301/g,alternative:"e"},{letter:/\u1eb9\u0311/g,alternative:"e"},{letter:/[\u00c8\u00c9\u0206\u0204]/g,alternative:"E"},{letter:/\u018e\u030f/g,alternative:"E"},{letter:/\u018e\u0300/g,alternative:"E"},{letter:/\u1eb8\u0311/g,alternative:"E"},{letter:/\u1eb8\u0301/g,alternative:"E"},{letter:/[\u00ec\u00ed\u020b\u0209]/g,alternative:"i"},{letter:/[\u00cc\u00cd\u020a\u0208]/g,alternative:"I"},{letter:/[\u00f2\u00f3\u020f\u020d]/g,alternative:"o"},{letter:/\u1ecd\u0311/g,alternative:"o"},{letter:/\u1ecd\u0301/g,alternative:"o"},{letter:/\u1ecc\u0311/g,alternative:"O"},{letter:/\u1ecc\u0301/g,alternative:"O"},{letter:/[\u00d2\u00d3\u020e\u020c]/g,alternative:"O"},{letter:/[\u00f9\u00fa\u0217\u0215]/g,alternative:"u"},{letter:/[\u00d9\u00da\u0216\u0214]/g,alternative:"U"},{letter:/[\u0155\u0213]/g,alternative:"r"},{letter:/[\u0154\u0212]/g,alternative:"R"}],sq:[{letter:/[\u00e7]/g,alternative:"c"},{letter:/[\u00c7]/g,alternative:"C"},{letter:/[\u00eb]/g,alternative:"e"},{letter:/[\u00cb]/g,alternative:"E"}],hu:[{letter:/[\u00e1]/g,alternative:"a"},{letter:/[\u00c1]/g,alternative:"A"},{letter:/[\u00e9]/g,alternative:"e"},{letter:/[\u00c9]/g,alternative:"E"},{letter:/[\u00ed]/g,alternative:"i"},{letter:/[\u00cd]/g,alternative:"I"},{letter:/[\u00f3\u00f6\u0151]/g,alternative:"o"},{letter:/[\u00d3\u00d6\u0150]/g,alternative:"O"},{letter:/[\u00fa\u00fc\u0171]/g,alternative:"u"},{letter:/[\u00da\u00dc\u0170]/g,alternative:"U"}],srd:[{letter:/[\u00e7]/g,alternative:"c"},{letter:/[\u00c7]/g,alternative:"C"},{letter:/[\u00e0\u00e1]/g,alternative:"a"},{letter:/[\u00c0\u00c1]/g,alternative:"A"},{letter:/[\u00e8\u00e9]/g,alternative:"e"},{letter:/[\u00c8\u00c9]/g,alternative:"E"},{letter:/[\u00ed\u00ef]/g,alternative:"i"},{letter:/[\u00cd\u00cf]/g,alternative:"I"},{letter:/[\u00f2\u00f3]/g,alternative:"o"},{letter:/[\u00d2\u00d3]/g,alternative:"O"},{letter:/[\u00fa\u00f9]/g,alternative:"u"},{letter:/[\u00da\u00d9]/g,alternative:"U"}],szl:[{letter:/[\u0107]/g,alternative:"c"},{letter:/[\u0106]/g,alternative:"C"},{letter:/[\u00e3]/g,alternative:"a"},{letter:/[\u00c3]/g,alternative:"A"},{letter:/[\u0142]/g,alternative:"u"},{letter:/[\u0141]/g,alternative:"U"},{letter:/[\u006e]/g,alternative:"n"},{letter:/[\u004e]/g,alternative:"N"},{letter:/[\u014f\u014d\u00f4\u00f5]/g,alternative:"o"},{letter:/[\u014e\u014c\u00d4\u00d5]/g,alternative:"O"},{letter:/[\u015b]/g,alternative:"s"},{letter:/[\u015a]/g,alternative:"S"},{letter:/[\u017a\u017c\u017e]/g,alternative:"z"},{letter:/[\u0179\u017b\u017d]/g,alternative:"Z"},{letter:/[\u016f]/g,alternative:"u"},{letter:/[\u016e]/g,alternative:"U"},{letter:/[\u010d]/g,alternative:"cz"},{letter:/[\u010c]/g,alternative:"Cz"},{letter:/[\u0159]/g,alternative:"rz"},{letter:/[\u0158]/g,alternative:"Rz"},{letter:/[\u0161]/g,alternative:"sz"},{letter:/[\u0160]/g,alternative:"Sz"}],tah:[{letter:/[\u0101\u00e2\u00e0]/g,alternative:"a"},{letter:/[\u0100\u00c2\u00c0]/g,alternative:"A"},{letter:/[\u00ef\u00ee\u00ec]/g,alternative:"i"},{letter:/[\u00cf\u00ce\u00cc]/g,alternative:"I"},{letter:/[\u0113\u00ea\u00e9]/g,alternative:"e"},{letter:/[\u0112\u00ca\u00c9]/g,alternative:"E"},{letter:/[\u016b\u00fb\u00fa]/g,alternative:"u"},{letter:/[\u016a\u00db\u00da]/g,alternative:"U"},{letter:/[\u00e7]/g,alternative:"c"},{letter:/[\u00c7]/g,alternative:"C"},{letter:/[\u00f2\u00f4\u014d]/g,alternative:"o"},{letter:/[\u00d2\u00d4\u014c]/g,alternative:"O"},{letter:/[\u2019\u0027\u2018]/g,alternative:""}],vec:[{letter:/\u0073\u002d\u0063/g,alternative:"sc"},{letter:/\u0053\u002d\u0043/g,alternative:"SC"},{letter:/\u0073\u0027\u0063/g,alternative:"sc"},{letter:/\u0053\u0027\u0043/g,alternative:"SC"},{letter:/\u0073\u2019\u0063/g,alternative:"sc"},{letter:/\u0053\u2019\u0043/g,alternative:"SC"},{letter:/\u0073\u2018\u0063/g,alternative:"sc"},{letter:/\u0053\u2018\u0043/g,alternative:"SC"},{letter:/\u0053\u002d\u0063/g,alternative:"Sc"},{letter:/\u0053\u0027\u0063/g,alternative:"Sc"},{letter:/\u0053\u2019\u0063/g,alternative:"Sc"},{letter:/\u0053\u2018\u0063/g,alternative:"Sc"},{letter:/\u0063\u2019/g,alternative:"c"},{letter:/\u0043\u2019/g,alternative:"C"},{letter:/\u0063\u2018/g,alternative:"c"},{letter:/\u0043\u2018/g,alternative:"C"},{letter:/\u0063\u0027/g,alternative:"c"},{letter:/\u0043\u0027/g,alternative:"C"},{letter:/[\u00e0\u00e1\u00e2]/g,alternative:"a"},{letter:/[\u00c0\u00c1\u00c2]/g,alternative:"A"},{letter:/[\u00e8\u00e9]/g,alternative:"e"},{letter:/[\u00c8\u00c9]/g,alternative:"E"},{letter:/[\u00f2\u00f3]/g,alternative:"o"},{letter:/[\u00d2\u00d3]/g,alternative:"O"},{letter:/[\u00f9\u00fa]/g,alternative:"u"},{letter:/[\u00d9\u00da]/g,alternative:"U"},{letter:/[\u00e7\u010d\u010b]/g,alternative:"c"},{letter:/[\u00c7\u010c\u010a]/g,alternative:"C"},{letter:/[\u0142]/g,alternative:"l"},{letter:/[\u00a3\u0141]/g,alternative:"L"},{letter:/\ud835\udeff/g,alternative:"dh"},{letter:/[\u0111\u03b4]/g,alternative:"dh"},{letter:/[\u0110\u0394]/g,alternative:"Dh"}],wa:[{letter:/[\u00e2\u00e5]/g,alternative:"a"},{letter:/[\u00c2\u00c5]/g,alternative:"A"},{letter:/[\u00e7]/g,alternative:"c"},{letter:/[\u00c7]/g,alternative:"C"},{letter:/\u0065\u030a/g,alternative:"e"},{letter:/\u0045\u030a/g,alternative:"E"},{letter:/[\u00eb\u00ea\u00e8\u00e9]/g,alternative:"e"},{letter:/[\u00c9\u00c8\u00ca\u00cb]/g,alternative:"E"},{letter:/[\u00ee]/g,alternative:"i"},{letter:/[\u00ce]/g,alternative:"I"},{letter:/[\u00f4\u00f6]/g,alternative:"o"},{letter:/[\u00d6\u00d4]/g,alternative:"O"},{letter:/[\u00fb]/g,alternative:"u"},{letter:/[\u00db]/g,alternative:"U"}],yor:[{letter:/[\u00e1\u00e0]/g,alternative:"a"},{letter:/[\u00c1\u00c0]/g,alternative:"A"},{letter:/[\u00ec\u00ed]/g,alternative:"i"},{letter:/[\u00cc\u00cd]/g,alternative:"I"},{letter:/\u1ecd\u0301/g,alternative:"o"},{letter:/\u1ecc\u0301/g,alternative:"O"},{letter:/\u1ecd\u0300/g,alternative:"o"},{letter:/\u1ecc\u0300/g,alternative:"O"},{letter:/[\u00f3\u00f2\u1ecd]/g,alternative:"o"},{letter:/[\u00d3\u00d2\u1ecc]/g,alternative:"O"},{letter:/[\u00fa\u00f9]/g,alternative:"u"},{letter:/[\u00da\u00d9]/g,alternative:"U"},{letter:/\u1eb9\u0301/g,alternative:"e"},{letter:/\u1eb8\u0301/g,alternative:"E"},{letter:/\u1eb9\u0300/g,alternative:"e"},{letter:/\u1eb8\u0300/g,alternative:"E"},{letter:/[\u00e9\u00e8\u1eb9]/g,alternative:"e"},{letter:/[\u00c9\u00c8\u1eb8]/g,alternative:"E"},{letter:/[\u1e63]/g,alternative:"s"},{letter:/[\u1e62]/g,alternative:"S"}]};const Se=[{letter:/[\u00A3]/g,alternative:""},{letter:/[\u20AC]/g,alternative:"E"},{letter:/[\u00AA]/g,alternative:"a"},{letter:/[\u00BA]/g,alternative:"o"},{letter:/[\u00C0]/g,alternative:"A"},{letter:/[\u00C1]/g,alternative:"A"},{letter:/[\u00C2]/g,alternative:"A"},{letter:/[\u00C3]/g,alternative:"A"},{letter:/[\u00C4]/g,alternative:"A"},{letter:/[\u00C5]/g,alternative:"A"},{letter:/[\u00C6]/g,alternative:"AE"},{letter:/[\u00C7]/g,alternative:"C"},{letter:/[\u00C8]/g,alternative:"E"},{letter:/[\u00C9]/g,alternative:"E"},{letter:/[\u00CA]/g,alternative:"E"},{letter:/[\u00CB]/g,alternative:"E"},{letter:/[\u00CC]/g,alternative:"I"},{letter:/[\u00CD]/g,alternative:"I"},{letter:/[\u00CE]/g,alternative:"I"},{letter:/[\u00CF]/g,alternative:"I"},{letter:/[\u00D0]/g,alternative:"D"},{letter:/[\u00D1]/g,alternative:"N"},{letter:/[\u00D2]/g,alternative:"O"},{letter:/[\u00D3]/g,alternative:"O"},{letter:/[\u00D4]/g,alternative:"O"},{letter:/[\u00D5]/g,alternative:"O"},{letter:/[\u00D6]/g,alternative:"O"},{letter:/[\u00D8]/g,alternative:"O"},{letter:/[\u00D9]/g,alternative:"U"},{letter:/[\u00DA]/g,alternative:"U"},{letter:/[\u00DB]/g,alternative:"U"},{letter:/[\u00DC]/g,alternative:"U"},{letter:/[\u00DD]/g,alternative:"Y"},{letter:/[\u00DE]/g,alternative:"TH"},{letter:/[\u00DF]/g,alternative:"s"},{letter:/[\u00E0]/g,alternative:"a"},{letter:/[\u00E1]/g,alternative:"a"},{letter:/[\u00E2]/g,alternative:"a"},{letter:/[\u00E3]/g,alternative:"a"},{letter:/[\u00E4]/g,alternative:"a"},{letter:/[\u00E5]/g,alternative:"a"},{letter:/[\u00E6]/g,alternative:"ae"},{letter:/[\u00E7]/g,alternative:"c"},{letter:/[\u00E8]/g,alternative:"e"},{letter:/[\u00E9]/g,alternative:"e"},{letter:/[\u00EA]/g,alternative:"e"},{letter:/[\u00EB]/g,alternative:"e"},{letter:/[\u00EC]/g,alternative:"i"},{letter:/[\u00ED]/g,alternative:"i"},{letter:/[\u00EE]/g,alternative:"i"},{letter:/[\u00EF]/g,alternative:"i"},{letter:/[\u00F0]/g,alternative:"d"},{letter:/[\u00F1]/g,alternative:"n"},{letter:/[\u00F2]/g,alternative:"o"},{letter:/[\u00F3]/g,alternative:"o"},{letter:/[\u00F4]/g,alternative:"o"},{letter:/[\u00F5]/g,alternative:"o"},{letter:/[\u00F6]/g,alternative:"o"},{letter:/[\u00F8]/g,alternative:"o"},{letter:/[\u00F9]/g,alternative:"u"},{letter:/[\u00FA]/g,alternative:"u"},{letter:/[\u00FB]/g,alternative:"u"},{letter:/[\u00FC]/g,alternative:"u"},{letter:/[\u00FD]/g,alternative:"y"},{letter:/[\u00FE]/g,alternative:"th"},{letter:/[\u00FF]/g,alternative:"y"},{letter:/[\u0100]/g,alternative:"A"},{letter:/[\u0101]/g,alternative:"a"},{letter:/[\u0102]/g,alternative:"A"},{letter:/[\u0103]/g,alternative:"a"},{letter:/[\u0104]/g,alternative:"A"},{letter:/[\u0105]/g,alternative:"a"},{letter:/[\u0106]/g,alternative:"C"},{letter:/[\u0107]/g,alternative:"c"},{letter:/[\u0108]/g,alternative:"C"},{letter:/[\u0109]/g,alternative:"c"},{letter:/[\u010A]/g,alternative:"C"},{letter:/[\u010B]/g,alternative:"c"},{letter:/[\u010C]/g,alternative:"C"},{letter:/[\u010D]/g,alternative:"c"},{letter:/[\u010E]/g,alternative:"D"},{letter:/[\u010F]/g,alternative:"d"},{letter:/[\u0110]/g,alternative:"D"},{letter:/[\u0111]/g,alternative:"d"},{letter:/[\u0112]/g,alternative:"E"},{letter:/[\u0113]/g,alternative:"e"},{letter:/[\u0114]/g,alternative:"E"},{letter:/[\u0115]/g,alternative:"e"},{letter:/[\u0116]/g,alternative:"E"},{letter:/[\u0117]/g,alternative:"e"},{letter:/[\u0118]/g,alternative:"E"},{letter:/[\u0119]/g,alternative:"e"},{letter:/[\u011A]/g,alternative:"E"},{letter:/[\u011B]/g,alternative:"e"},{letter:/[\u011C]/g,alternative:"G"},{letter:/[\u011D]/g,alternative:"g"},{letter:/[\u011E]/g,alternative:"G"},{letter:/[\u011F]/g,alternative:"g"},{letter:/[\u0120]/g,alternative:"G"},{letter:/[\u0121]/g,alternative:"g"},{letter:/[\u0122]/g,alternative:"G"},{letter:/[\u0123]/g,alternative:"g"},{letter:/[\u0124]/g,alternative:"H"},{letter:/[\u0125]/g,alternative:"h"},{letter:/[\u0126]/g,alternative:"H"},{letter:/[\u0127]/g,alternative:"h"},{letter:/[\u0128]/g,alternative:"I"},{letter:/[\u0129]/g,alternative:"i"},{letter:/[\u012A]/g,alternative:"I"},{letter:/[\u012B]/g,alternative:"i"},{letter:/[\u012C]/g,alternative:"I"},{letter:/[\u012D]/g,alternative:"i"},{letter:/[\u012E]/g,alternative:"I"},{letter:/[\u012F]/g,alternative:"i"},{letter:/[\u0130]/g,alternative:"I"},{letter:/[\u0131]/g,alternative:"i"},{letter:/[\u0132]/g,alternative:"IJ"},{letter:/[\u0133]/g,alternative:"ij"},{letter:/[\u0134]/g,alternative:"J"},{letter:/[\u0135]/g,alternative:"j"},{letter:/[\u0136]/g,alternative:"K"},{letter:/[\u0137]/g,alternative:"k"},{letter:/[\u0138]/g,alternative:"k"},{letter:/[\u0139]/g,alternative:"L"},{letter:/[\u013A]/g,alternative:"l"},{letter:/[\u013B]/g,alternative:"L"},{letter:/[\u013C]/g,alternative:"l"},{letter:/[\u013D]/g,alternative:"L"},{letter:/[\u013E]/g,alternative:"l"},{letter:/[\u013F]/g,alternative:"L"},{letter:/[\u0140]/g,alternative:"l"},{letter:/[\u0141]/g,alternative:"L"},{letter:/[\u0142]/g,alternative:"l"},{letter:/[\u0143]/g,alternative:"N"},{letter:/[\u0144]/g,alternative:"n"},{letter:/[\u0145]/g,alternative:"N"},{letter:/[\u0146]/g,alternative:"n"},{letter:/[\u0147]/g,alternative:"N"},{letter:/[\u0148]/g,alternative:"n"},{letter:/[\u0149]/g,alternative:"n"},{letter:/[\u014A]/g,alternative:"N"},{letter:/[\u014B]/g,alternative:"n"},{letter:/[\u014C]/g,alternative:"O"},{letter:/[\u014D]/g,alternative:"o"},{letter:/[\u014E]/g,alternative:"O"},{letter:/[\u014F]/g,alternative:"o"},{letter:/[\u0150]/g,alternative:"O"},{letter:/[\u0151]/g,alternative:"o"},{letter:/[\u0152]/g,alternative:"OE"},{letter:/[\u0153]/g,alternative:"oe"},{letter:/[\u0154]/g,alternative:"R"},{letter:/[\u0155]/g,alternative:"r"},{letter:/[\u0156]/g,alternative:"R"},{letter:/[\u0157]/g,alternative:"r"},{letter:/[\u0158]/g,alternative:"R"},{letter:/[\u0159]/g,alternative:"r"},{letter:/[\u015A]/g,alternative:"S"},{letter:/[\u015B]/g,alternative:"s"},{letter:/[\u015C]/g,alternative:"S"},{letter:/[\u015D]/g,alternative:"s"},{letter:/[\u015E]/g,alternative:"S"},{letter:/[\u015F]/g,alternative:"s"},{letter:/[\u0160]/g,alternative:"S"},{letter:/[\u0161]/g,alternative:"s"},{letter:/[\u0162]/g,alternative:"T"},{letter:/[\u0163]/g,alternative:"t"},{letter:/[\u0164]/g,alternative:"T"},{letter:/[\u0165]/g,alternative:"t"},{letter:/[\u0166]/g,alternative:"T"},{letter:/[\u0167]/g,alternative:"t"},{letter:/[\u0168]/g,alternative:"U"},{letter:/[\u0169]/g,alternative:"u"},{letter:/[\u016A]/g,alternative:"U"},{letter:/[\u016B]/g,alternative:"u"},{letter:/[\u016C]/g,alternative:"U"},{letter:/[\u016D]/g,alternative:"u"},{letter:/[\u016E]/g,alternative:"U"},{letter:/[\u016F]/g,alternative:"u"},{letter:/[\u0170]/g,alternative:"U"},{letter:/[\u0171]/g,alternative:"u"},{letter:/[\u0172]/g,alternative:"U"},{letter:/[\u0173]/g,alternative:"u"},{letter:/[\u0174]/g,alternative:"W"},{letter:/[\u0175]/g,alternative:"w"},{letter:/[\u0176]/g,alternative:"Y"},{letter:/[\u0177]/g,alternative:"y"},{letter:/[\u0178]/g,alternative:"Y"},{letter:/[\u0179]/g,alternative:"Z"},{letter:/[\u017A]/g,alternative:"z"},{letter:/[\u017B]/g,alternative:"Z"},{letter:/[\u017C]/g,alternative:"z"},{letter:/[\u017D]/g,alternative:"Z"},{letter:/[\u017E]/g,alternative:"z"},{letter:/[\u017F]/g,alternative:"s"},{letter:/[\u01A0]/g,alternative:"O"},{letter:/[\u01A1]/g,alternative:"o"},{letter:/[\u01AF]/g,alternative:"U"},{letter:/[\u01B0]/g,alternative:"u"},{letter:/[\u01CD]/g,alternative:"A"},{letter:/[\u01CE]/g,alternative:"a"},{letter:/[\u01CF]/g,alternative:"I"},{letter:/[\u01D0]/g,alternative:"i"},{letter:/[\u01D1]/g,alternative:"O"},{letter:/[\u01D2]/g,alternative:"o"},{letter:/[\u01D3]/g,alternative:"U"},{letter:/[\u01D4]/g,alternative:"u"},{letter:/[\u01D5]/g,alternative:"U"},{letter:/[\u01D6]/g,alternative:"u"},{letter:/[\u01D7]/g,alternative:"U"},{letter:/[\u01D8]/g,alternative:"u"},{letter:/[\u01D9]/g,alternative:"U"},{letter:/[\u01DA]/g,alternative:"u"},{letter:/[\u01DB]/g,alternative:"U"},{letter:/[\u01DC]/g,alternative:"u"},{letter:/[\u0218]/g,alternative:"S"},{letter:/[\u0219]/g,alternative:"s"},{letter:/[\u021A]/g,alternative:"T"},{letter:/[\u021B]/g,alternative:"t"},{letter:/[\u0251]/g,alternative:"a"},{letter:/[\u1EA0]/g,alternative:"A"},{letter:/[\u1EA1]/g,alternative:"a"},{letter:/[\u1EA2]/g,alternative:"A"},{letter:/[\u1EA3]/g,alternative:"a"},{letter:/[\u1EA4]/g,alternative:"A"},{letter:/[\u1EA5]/g,alternative:"a"},{letter:/[\u1EA6]/g,alternative:"A"},{letter:/[\u1EA7]/g,alternative:"a"},{letter:/[\u1EA8]/g,alternative:"A"},{letter:/[\u1EA9]/g,alternative:"a"},{letter:/[\u1EAA]/g,alternative:"A"},{letter:/[\u1EAB]/g,alternative:"a"},{letter:/[\u1EA6]/g,alternative:"A"},{letter:/[\u1EAD]/g,alternative:"a"},{letter:/[\u1EAE]/g,alternative:"A"},{letter:/[\u1EAF]/g,alternative:"a"},{letter:/[\u1EB0]/g,alternative:"A"},{letter:/[\u1EB1]/g,alternative:"a"},{letter:/[\u1EB2]/g,alternative:"A"},{letter:/[\u1EB3]/g,alternative:"a"},{letter:/[\u1EB4]/g,alternative:"A"},{letter:/[\u1EB5]/g,alternative:"a"},{letter:/[\u1EB6]/g,alternative:"A"},{letter:/[\u1EB7]/g,alternative:"a"},{letter:/[\u1EB8]/g,alternative:"E"},{letter:/[\u1EB9]/g,alternative:"e"},{letter:/[\u1EBA]/g,alternative:"E"},{letter:/[\u1EBB]/g,alternative:"e"},{letter:/[\u1EBC]/g,alternative:"E"},{letter:/[\u1EBD]/g,alternative:"e"},{letter:/[\u1EBE]/g,alternative:"E"},{letter:/[\u1EBF]/g,alternative:"e"},{letter:/[\u1EC0]/g,alternative:"E"},{letter:/[\u1EC1]/g,alternative:"e"},{letter:/[\u1EC2]/g,alternative:"E"},{letter:/[\u1EC3]/g,alternative:"e"},{letter:/[\u1EC4]/g,alternative:"E"},{letter:/[\u1EC5]/g,alternative:"e"},{letter:/[\u1EC6]/g,alternative:"E"},{letter:/[\u1EC7]/g,alternative:"e"},{letter:/[\u1EC8]/g,alternative:"I"},{letter:/[\u1EC9]/g,alternative:"i"},{letter:/[\u1ECA]/g,alternative:"I"},{letter:/[\u1ECB]/g,alternative:"i"},{letter:/[\u1ECC]/g,alternative:"O"},{letter:/[\u1ECD]/g,alternative:"o"},{letter:/[\u1ECE]/g,alternative:"O"},{letter:/[\u1ECF]/g,alternative:"o"},{letter:/[\u1ED0]/g,alternative:"O"},{letter:/[\u1ED1]/g,alternative:"o"},{letter:/[\u1ED2]/g,alternative:"O"},{letter:/[\u1ED3]/g,alternative:"o"},{letter:/[\u1ED4]/g,alternative:"O"},{letter:/[\u1ED5]/g,alternative:"o"},{letter:/[\u1ED6]/g,alternative:"O"},{letter:/[\u1ED7]/g,alternative:"o"},{letter:/[\u1ED8]/g,alternative:"O"},{letter:/[\u1ED9]/g,alternative:"o"},{letter:/[\u1EDA]/g,alternative:"O"},{letter:/[\u1EDB]/g,alternative:"o"},{letter:/[\u1EDC]/g,alternative:"O"},{letter:/[\u1EDD]/g,alternative:"o"},{letter:/[\u1EDE]/g,alternative:"O"},{letter:/[\u1EDF]/g,alternative:"o"},{letter:/[\u1EE0]/g,alternative:"O"},{letter:/[\u1EE1]/g,alternative:"o"},{letter:/[\u1EE2]/g,alternative:"O"},{letter:/[\u1EE3]/g,alternative:"o"},{letter:/[\u1EE4]/g,alternative:"U"},{letter:/[\u1EE5]/g,alternative:"u"},{letter:/[\u1EE6]/g,alternative:"U"},{letter:/[\u1EE7]/g,alternative:"u"},{letter:/[\u1EE8]/g,alternative:"U"},{letter:/[\u1EE9]/g,alternative:"u"},{letter:/[\u1EEA]/g,alternative:"U"},{letter:/[\u1EEB]/g,alternative:"u"},{letter:/[\u1EEC]/g,alternative:"U"},{letter:/[\u1EED]/g,alternative:"u"},{letter:/[\u1EEE]/g,alternative:"U"},{letter:/[\u1EEF]/g,alternative:"u"},{letter:/[\u1EF0]/g,alternative:"U"},{letter:/[\u1EF1]/g,alternative:"u"},{letter:/[\u1EF2]/g,alternative:"Y"},{letter:/[\u1EF3]/g,alternative:"y"},{letter:/[\u1EF4]/g,alternative:"Y"},{letter:/[\u1EF5]/g,alternative:"y"},{letter:/[\u1EF6]/g,alternative:"Y"},{letter:/[\u1EF7]/g,alternative:"y"},{letter:/[\u1EF8]/g,alternative:"Y"},{letter:/[\u1EF9]/g,alternative:"y"}],xe=[{letter:/[\u00C4]/g,alternative:"Ae"},{letter:/[\u00E4]/g,alternative:"ae"},{letter:/[\u00D6]/g,alternative:"Oe"},{letter:/[\u00F6]/g,alternative:"oe"},{letter:/[\u00DC]/g,alternative:"Ue"},{letter:/[\u00FC]/g,alternative:"ue"},{letter:/[\u1E9E]/g,alternative:"SS"},{letter:/[\u00DF]/g,alternative:"ss"}],we=[{letter:/[\u00C6]/g,alternative:"Ae"},{letter:/[\u00E6]/g,alternative:"ae"},{letter:/[\u00D8]/g,alternative:"Oe"},{letter:/[\u00F8]/g,alternative:"oe"},{letter:/[\u00C5]/g,alternative:"Aa"},{letter:/[\u00E5]/g,alternative:"aa"}],ke=[{letter:/[\u00B7]/g,alternative:"ll"}],Ke=[{letter:/[\u0110]/g,alternative:"DJ"},{letter:/[\u0111]/g,alternative:"dj"}],Te=function(e){switch(e){case"de":return xe;case"da":return we;case"ca":return ke;case"sr":case"bs":return Ke;default:return[]}};const Re=new RegExp(`([${Ie}])`,"g");function ze(e,t){const r=[];if(e.indexOf(t)>-1)for(let a=0;a<e.length;a++)e[a]===t&&r.push(a);return r}function Ne(e,r){return(0,t.filter)(e,(function(e){return!(0,t.includes)(r,e)}))}function We(e){return function e(t,r){const a=t[0];if(void 0===a)return r;for(let e=0,t=r.length;e<t;++e)r.push(r[e].concat(a));return e(t.slice(1),r)}(e,[[]]).slice(1).concat([[]])}function je(e,t,r){const a=e.split("");return t.forEach((function(e){a.splice(e,1,r)})),a.join("")}const Le=(0,t.memoize)((function(e){const r=ze(e,"İ").concat(ze(e,"I"),ze(e,"i"),ze(e,"ı"));if(r.sort(),0===r.length)return[e];const a=(l=function(e){const r=[],a=function(e,r="\\s",a=!0){if(""===(e=Z(e)))return[];const l=new RegExp(r,"g");let u=e.split(l);return u=a?u.map(ye):(0,t.flatMap)(u,(e=>e.replace(Re," $1 ").split(" "))),(0,t.filter)(u,(function(e){return""!==e.trim()}))}(e);let l=0;return a.forEach((function(t){const a=e.indexOf(t,l);r.push(a),l=a+t.length})),r}(e),u=r,(0,t.filter)(l,(function(e){return(0,t.includes)(u,e)})));var l,u;const n=[];We(a).forEach((function(e){if((0,t.isEqual)(e,a))n.push([e,[],[],[]]);else{const r=Ne(a,e);We(r).forEach((function(a){if((0,t.isEqual)(a,r))n.push([e,a,[],[]]);else{const l=Ne(r,a);We(l).forEach((function(r){if((0,t.isEqual)(r,l))n.push([e,a,r,[]]);else{const t=Ne(l,r);n.push([e,a,r,t])}}))}}))}}));const i=[];return n.forEach((function(t){const r=je(e,t[0],"İ"),a=je(r,t[1],"I"),l=je(a,t[2],"i"),u=je(l,t[3],"ı");i.push(u)})),i})),$e=function(e,t){return e=y(e,!1,"",t),new RegExp(e,"ig")};function Pe(e,r,a,l){e=function(e){return U(e=e.replace(/<(?!li|\/li|p|\/p|h1|\/h1|h2|\/h2|h3|\/h3|h4|\/h4|h5|\/h5|h6|\/h6|dd).*?>/g,""))}(e),e=C(e=Y(e)),r=C(r);let u=l?l(e,r):function(e,r,a){const l=Ue(a);let u=$e(r,l);if("tr"===l){const e=Le(r);u=new RegExp(e.map((e=>y(e))).join("|"),"ig")}const n=e.match(u)||[];e=e.replace(u,"");const i=function(e,r){const a=function(e){if((0,t.isUndefined)(e))return[];const r=Ue(e);return"nb"===r||"nn"===r?Me.nbnn:"bal"===r||"ca"===r?Me.ca:Me[r]||[]}(r);for(let t=0;t<a.length;t++)e=e.replace(a[t].letter,a[t].alternative);return e}(r,a),g=$e(i,l),v=e.match(g)||[];let s=n.concat(v);const c=function(e,r){const a=function(e){if((0,t.isUndefined)(e))return[];let r=Se;return r=r.concat(Te(Ue(e))),r}(r);for(let t=a.length-1;t>=0;t--)e=e.replace(a[t].letter,a[t].alternative);return e}(r,a);if(c!==i){const t=$e(c,l),r=e.match(t)||[];s=s.concat(r)}return(0,t.map)(s,(function(e){return U(e)}))}(e,r,a);u=(0,t.map)(u,(function(e){return U(ye(e))}));const n=(0,t.map)(u,(function(t){return e.indexOf(t)}));return{count:u.length,matches:u,position:0===n.length?-1:Math.min(...n)}}function Ye(e,r,a="en_EN",l){let u=0,n=[],i=[];return(0,t.uniq)(r).forEach((function(t){const r=Pe(e,t,a,l);u+=r.count,n=n.concat(r.matches),i.push(r.position)})),i=i.filter((e=>e>=0)),{count:u,matches:n,position:0===i.length?-1:Math.min(...i)}}function Ze(e,r){if(0===r)return r;let a=e.substring(0,r);return a=v(a),a=a.filter((e=>!s.includes(e))),(0,t.isEmpty)(a)?0:r}function Qe(e,r){const a=e.getTitle();let l=e.getKeyword();const u={allWordsFound:!1,position:-1,exactMatchKeyphrase:!1},n=h(l);if(n.exactMatchRequested){if(u.exactMatchKeyphrase=!0,!a.includes(n.keyphrase))return u;l=c(n.keyphrase);const e=Ye(a,l,"ja",A);return e.matches.length===l.length&&(u.allWordsFound=!0,u.position=Ze(a,e.position)),u}const i=function(e,r,a,l){const u=e.length,n=Array(u);let i=[],g=[];for(let t=0;t<u;t++){const u=Ye(r,e[t],a,l);n[t]=u.count>0?1:0,i.push(u.position),g=g.concat(u.matches)}const v=(0,t.sum)(n),s={countWordMatches:v,percentWordMatches:0,matches:g};return u>0&&(s.percentWordMatches=Math.round(v/u*100)),i=i.filter((e=>e>=0)),s.position=0===i.length?-1:Math.min(...i),s}(r.getResearch("morphology").keyphraseForms,a,"ja",A);return 100===i.percentWordMatches&&(u.allWordsFound=!0,u.position=Ze(a,i.position)),u}const{AbstractResearcher:Ge}=e.languageProcessing;class qe extends Ge{constructor(e){super(e),delete this.defaultResearches.getFleschReadingScore,delete this.defaultResearches.getPassiveVoiceResult,delete this.defaultResearches.keywordCountInSlug,Object.assign(this.config,{language:"ja",firstWordExceptions:ve,functionWords:s,transitionWords:se,topicLength:ce,textLength:oe,paragraphLength:Ee,assessmentApplicability:he,sentenceLength:de,keyphraseLength:Ce,subheadingsTooLong:fe,countCharacters:!0,metaDescriptionLength:Ae}),Object.assign(this.helpers,{matchWordCustomHelper:A,getWordsCustomHelper:v,getContentWords:c,customGetStemmer:D,wordsCharacterCount:m,customCountLength:_,matchTransitionWordsHelper:I,memoizedTokenizer:ie,splitIntoTokensCustom:ge}),Object.assign(this.defaultResearches,{morphology:Be,keyphraseLength:Oe,wordCountInText:_e,findKeyphraseInSEOTitle:Qe})}}})(),(window.yoast=window.yoast||{}).Researcher=a})();