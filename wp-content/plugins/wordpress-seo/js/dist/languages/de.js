(()=>{var e={429:e=>{var t=function(e,t){var r;for(r=0;r<e.length;r++)if(e[r].regex.test(t))return e[r]},r=function(e,r){var s,n,l;for(s=0;s<r.length;s++)if(n=t(e,r.substring(0,s+1)))l=n;else if(l)return{max_index:s,rule:l};return l?{max_index:r.length,rule:l}:void 0};e.exports=function(e){var s="",n=[],l=1,a=1,i=function(t,r){e({type:r,src:t,line:l,col:a});var s=t.split("\n");l+=s.length-1,a=(s.length>1?1:a)+s[s.length-1].length};return{addRule:function(e,t){n.push({regex:e,type:t})},onText:function(e){for(var t=s+e,l=r(n,t);l&&l.max_index!==t.length;)i(t.substring(0,l.max_index),l.rule.type),t=t.substring(l.max_index),l=r(n,t);s=t},end:function(){if(0!==s.length){var e=t(n,s);if(!e){var r=new Error("unable to tokenize");throw r.tokenizer2={buffer:s,line:l,col:a},r}i(s,e.type)}}}}}},t={};function r(s){var n=t[s];if(void 0!==n)return n.exports;var l=t[s]={exports:{}};return e[s](l,l.exports,r),l.exports}r.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return r.d(t,{a:t}),t},r.d=(e,t)=>{for(var s in t)r.o(t,s)&&!r.o(e,s)&&Object.defineProperty(e,s,{enumerable:!0,get:t[s]})},r.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),r.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var s={};(()=>{"use strict";r.r(s),r.d(s,{default:()=>jt});const e=window.yoast.analysis,t=["das","dem","den","der","des","die","ein","eine","einem","einen","einer","eines","eins","zwei","drei","vier","fünf","sechs","sieben","acht","neun","zehn","denen","deren","derer","dessen","diese","diesem","diesen","dieser","dieses","jene","jenem","jenen","jener","jenes","welch","welcher","welches"],n=["bekommst","bekommt","bekamst","bekommest","bekommet","bekämest","bekämst","bekämet","bekämt","gekriegt","gehörst","gehört","gehörtest","gehörtet","gehörest","gehöret","erhältst","erhält","erhaltet","erhielt","erhieltest","erhieltst","erhieltet","erhaltest"],l=["werde","wirst","wird","werden","werdet","wurde","ward","wurdest","wardst","wurden","wurdet","worden","werdest","würde","würdest","würden","würdet","bekomme","bekommen","bekam","bekamen","bekäme","bekämen","kriege","kriegst","kriegt","kriegen","kriegte","kriegtest","kriegten","kriegtet","kriegest","krieget","gehöre","gehören","gehörte","gehörten","erhalte","erhalten","erhielten","erhielte"],a=["werden","bekommen","kriegen","gehören","erhalten"],i={participleLike:n,otherAuxiliaries:l.concat(a),filteredAuxiliaries:n.concat(l),infinitiveAuxiliaries:a,all:n.concat(l,a)},b=["aber","abschließend","abschliessend","alldieweil","allerdings","also","anderenteils","andererseits","andernteils","anfaenglich","anfänglich","anfangs","angenommen","anschliessend","anschließend","aufgrund","ausgenommen","ausserdem","außerdem","beispielsweise","bevor","beziehungsweise","bspw","bzw","d.h","da","dabei","dadurch","dafuer","dafür","dagegen","daher","dahingegen","danach","dann","darauf","darum","dass","davor","dazu","dementgegen","dementsprechend","demgegenüber","demgegenueber","demgemaess","demgemäß","demzufolge","denn","dennoch","dergestalt","derweil","desto","deshalb","desungeachtet","deswegen","doch","dort","drittens","ebenfalls","ebenso","endlich","ehe","einerseits","einesteils","entsprechend","entweder","erst","erstens","falls","ferner","folgerichtig","folglich","fürderhin","fuerderhin","genauso","hierdurch","hierzu","hingegen","immerhin","indem","indes","indessen","infolge","infolgedessen","insofern","insoweit","inzwischen","jedenfalls","jedoch","kurzum","m.a.w","mitnichten","mitunter","möglicherweise","moeglicherweise","nachdem","nebenher","nichtsdestotrotz","nichtsdestoweniger","ob","obenrein","obgleich","obschon","obwohl","obzwar","ohnehin","richtigerweise","schliesslich","schließlich","seit","seitdem","sobald","sodass","so dass","sofern","sogar","solang","solange","somit","sondern","sooft","soviel","soweit","sowie","sowohl","statt","stattdessen","trotz","trotzdem","überdies","übrigens","ueberdies","uebrigens","ungeachtet","vielmehr","vorausgesetzt","vorher","waehrend","während","währenddessen","waehrenddessen","weder","wegen","weil","weiter","weiterhin","wenn","wenngleich","wennschon","wennzwar","weshalb","widrigenfalls","wiewohl","wobei","wohingegen","z.b","zudem","zuerst","zufolge","zuletzt","zumal","zuvor","zwar","zweitens"],u=b.concat(["abgesehen von","abgesehen davon","als dass","als ob","als wenn","anders ausgedrückt","anders ausgedrueckt","anders formuliert","anders gefasst","anders gefragt","anders gesagt","anders gesprochen","anstatt dass","auch wenn","auf grund","auf jeden fall","aus diesem grund","ausser dass","außer dass","ausser wenn","außer wenn","besser ausgedrückt","besser ausgedrueckt","besser formuliert","besser gesagt","besser gesprochen","bloss dass","bloß dass","darüber hinaus","das heisst","das heißt","des weiteren","dessen ungeachtet","ebenso wie","genauso wie","geschweige denn","im fall","im falle","im folgenden","im gegensatz dazu","im gegenteil","im grunde genommen","in diesem sinne","je nachdem","kurz gesagt","mit anderen worten","ohne dass","so dass","umso mehr als","umso weniger als","umso mehr, als","umso weniger, als","unbeschadet dessen","und zwar","ungeachtet dessen","unter dem strich","zum beispiel","zunächst einmal"]);function g(e){let t=e;return e.forEach((r=>{(r=r.split("-")).length>0&&r.filter((t=>!e.includes(t))).length>0&&(t=t.concat(r))})),t}const h=i.filteredAuxiliaries,o=["das","dem","den","der","des","die","ein","eine","einem","einen","einer","eines"],c=["eins","zwei","drei","vier","fünf","sechs","sieben","acht","neun","zehn","elf","zwölf","zwoelf","dreizehn","vierzehn","fünfzehn","fuenfzehn","sechzehn","siebzehn","achtzehn","neunzehn","zwanzig","hundert","einhundert","zweihundert","dreihundert","vierhundert","fünfhundert","fuenfhundert","sechshundert","siebenhundert","achthundert","neunhundert","tausend","million","milliarde","billion","billiarde"],d=["erste","erster","ersten","erstem","erstes","zweite","zweites","zweiter","zweitem","zweiten","dritte","dritter","drittes","dritten","drittem","vierter","vierten","viertem","viertes","vierte","fünfte","fünfter","fünftes","fünften","fünftem","fuenfte","fuenfter","fuenftem","fuenften","fuenftes","sechste","sechster","sechstes","sechsten","sechstem","siebte","siebter","siebten","siebtem","siebtes","achte","achter","achten","achtem","achtes","neunte","neunter","neuntes","neunten","neuntem","zehnte","zehnter","zehnten","zehntem","zehntes","elfte","elfter","elftes","elften","elftem","zwölfte","zwölfter","zwölften","zwölftem","zwölftes","zwoelfte","zwoelfter","zwoelften","zwoelftem","zwoelftes","dreizehnte","dreizehnter","dreizehntes","dreizehnten","dreizehntem","vierzehnte","vierzehnter","vierzehntes","vierzehnten","vierzehntem","fünfzehnte","fünfzehnten","fünfzehntem","fünfzehnter","fünfzehntes","fuenfzehnte","fuenfzehnten","fuenfzehntem","fuenfzehnter","fuenfzehntes","sechzehnte","sechzehnter","sechzehnten","sechzehntes","sechzehntem","siebzehnte","siebzehnter","siebzehntes","siebzehntem","siebzehnten","achtzehnter","achtzehnten","achtzehntem","achtzehntes","achtzehnte","nehnzehnte","nehnzehnter","nehnzehntem","nehnzehnten","nehnzehntes","zwanzigste","zwanzigster","zwanzigstem","zwanzigsten","zwanzigstes"],w=["ich","du","er","sie","es","wir","ihr"],m=["mich","dich","ihn","uns","euch"],f=["denen","deren","derer","dessen","diese","diesem","diesen","dieser","dieses","jene","jenem","jenen","jener","jenes","welch","welcher","welches","derjenige","desjenigen","demjenigen","denjenigen","diejenige","derjenigen","dasjenige","diejenigen"],p=["mein","meine","meinem","meiner","meines","meinen","dein","deine","deinem","deiner","deines","deinen","sein","seine","seinem","seiner","seines","ihre","ihrem","ihren","ihrer","ihres","unser","unsere","unserem","unseren","unserer","unseres","euer","eure","eurem","euren","eurer","eures","einanders"],k=["manche","manch","viele","viel","vieler","vielen","vielem","all","alle","aller","alles","allen","allem","allerlei","solcherlei","einige","etliche","wenige","weniger","wenigen","wenigem","weniges","wenig","wenigerer","wenigeren","wenigerem","wenigere","wenigeres","wenig","bisschen","paar","kein","keines","keinem","keinen","keine","mehr","genug","mehrere","mehrerer","mehreren","mehrerem","mehreres","verschiedene","verschiedener","verschiedenen","verschiedenem","verschiedenes","verschiedne","verschiedner","verschiednen","verschiednem","verschiednes","art","arten","sorte","sorten"],y=["sich"],z=["einander"],v=["andere","anderer","anderem","anderen","anderes","andren","andern","andrem","anderm","andre","andrer","andres","beide","beides","beidem","beider","beiden","etwas","irgendetwas","irgendein","irgendeinen","irgendeinem","irgendeines","irgendeine","irgendeiner","irgendwas","irgendwessen","irgendwer","irgendwen","irgendwem","irgendwelche","irgendwelcher","irgendwelchem","irgendwelchen","irgendwelches","irgendjemand","irgendjemanden","irgendjemandem","irgendjemandes","irgendwie","wer","wen","wem","wessen","was","welchen","welchem","welche","jeder","jedes","jedem","jeden","jede","jedweder","jedweden","jedwedem","jedwedes","jedwede","jeglicher","jeglichen","jeglichem","jegliches","jegliche","jedermann","jedermanns","jemand","jemanden","jemandem","jemands","jemandes","man","meinesgleichen","sämtlich","saemtlich","sämtlicher","saemtlicher","sämtlichen","saemtlichen","sämtlichem","saemtlichem","sämtliches","saemtliches","sämtliche","saemtliche","solche","solcher","solchen","solchem","solches","niemand","niemanden","niemandem","niemandes","niemands","nichts","zweiter"],E=["warum","wie","wo","woher","wohin","wann"],F=["dahinter","damit","daneben","daran","daraus","darin","darunter","darüber","darueber","davon","dazwischen","hieran","hierauf","hieraus","hierbei","hierfuer","hierfür","hiergegen","hierhinter","hierin","hiermit","hiernach","hierum","hierunter","hierueber","hierüber","hiervor","hierzwischen","hierneben","hiervon","wodurch","wofür","wofuer","wogegen","wohinter","womit","wonach","woneben","woran","worauf","woraus","worin","worum","worunter","worüber","worueber","wovon","wovor","wozu","wozwischen"],B=["hier","dorthin","hierher","dorther"],j=["allenfalls","keinesfalls","anderenfalls","andernfalls","andrenfalls","äußerstenfalls","bejahendenfalls","bestenfalls","eintretendenfalls","entgegengesetztenfalls","erforderlichenfalls","gegebenenfalls","geringstenfalls","gleichfalls","günstigenfalls","günstigstenfalls","höchstenfalls","möglichenfalls","notfalls","nötigenfalls","notwendigenfalls","schlimmstenfalls","vorkommendenfalls","zutreffendenfalls","keineswegs","durchwegs","geradenwegs","geradeswegs","geradewegs","gradenwegs","halbwegs","mittwegs","unterwegs"],x=["habe","hast","hat","habt","habest","habet","hatte","hattest","hatten","hätte","haette","hättest","haettest","hätten","haetten","haettet","hättet","hab","bin","bist","ist","sind","sei","seiest","seien","seiet","war","warst","waren","wart","wäre","waere","wärest","waerest","wärst","waerst","wären","waeren","wäret","waeret","wärt","waert","seid","darf","darfst","dürft","duerft","dürfe","duerfe","dürfest","duerfest","dürfet","duerfet","durfte","durftest","durften","durftet","dürfte","duerfte","dürftest","duerftest","dürften","duerften","dürftet","duerftet","kann","kannst","könnt","koennt","könne","koenne","könnest","koennest","könnet","koennet","konnte","konntest","konnten","konntet","könnte","koennte","könntest","koenntest","könnten","koennten","könntet","koenntet","mag","magst","mögt","moegt","möge","moege","mögest","moegest","möget","moeget","mochte","mochtest","mochten","mochtet","möchte","moechte","möchtest","moechtest","möchten","moechten","möchtet","moechtet","muss","muß","musst","mußt","müsst","muesst","müßt","mueßt","müsse","muesse","müssest","muessest","müsset","muesset","musste","mußte","musstest","mußtest","mussten","mußten","musstet","mußtet","müsste","muesste","müßte","mueßte","müsstest","muesstest","müßtest","mueßtest","müssten","muessten","müßten","mueßten","müsstet","muesstet","müßtet","mueßtet","soll","sollst","sollt","solle","sollest","sollet","sollte","solltest","sollten","solltet","will","willst","wollt","wolle","wollest","wollet","wollte","wolltest","wollten","wolltet","lasse","lässt","laesst","läßt","laeßt","lasst","laßt","lassest","lasset","ließ","ließest","ließt","ließen","ließe","ließet","liess","liessest","liesst","liessen","liesse","liesset"],A=["haben","dürfen","duerfen","können","koennen","mögen","moegen","müssen","muessen","sollen","wollen","lassen"],D=["bleibe","bleibst","bleibt","bleibest","bleibet","blieb","bliebst","bliebt","blieben","bliebe","bliebest","bliebet","heiße","heißt","heißest","heißet","heisse","heisst","heissest","heisset","hieß","hießest","hießt","hießen","hieße","hießet","hiess","hiessest","hiesst","hiessen","hiesse","hiesset","giltst","gilt","geltet","gelte","geltest","galt","galtest","galtst","galten","galtet","gälte","gaelte","gölte","goelte","gältest","gaeltest","göltest","goeltest","gälten","gaelten","gölten","goelten","gältet","gaeltet","göltet","goeltet","aussehe","aussiehst","aussieht","ausseht","aussehest","aussehet","aussah","aussahst","aussahen","aussaht","aussähe","aussaehe","aussähest","aussaehest","aussähst","aussaehst","aussähet","aussaehet","aussäht","aussaeht","aussähen","aussaehen","scheine","scheinst","scheint","scheinest","scheinet","schien","schienst","schienen","schient","schiene","schienest","schienet","erscheine","erscheinst","erscheint","erscheinest","erscheinet","erschien","erschienst","erschienen","erschient","erschiene","erschienest","erschienet"],S=["bleiben","heißen","heissen","gelten","aussehen","scheinen","erscheinen"],$=["a","à","ab","abseits","abzüglich","abzueglich","als","am","an","angelegentlich","angesichts","anhand","anlässlich","anlaesslich","ans","anstatt","anstelle","auf","aufs","aufseiten","aus","ausgangs","ausschließlich","ausschliesslich","außerhalb","ausserhalb","ausweislich","bar","behufs","bei","beidseits","beiderseits","beim","betreffs","bezüglich","bezueglich","binnen","bis","contra","dank","diesseits","durch","einbezüglich","einbezueglich","eingangs","eingedenk","einschließlich","einschliesslich","entgegen","entlang","exklusive","fern","fernab","fuer","für","fuers","fürs","gegen","gegenüber","gegenueber","gelegentlich","gemäß","gemaeß","gen","getreu","gleich","halber","hinsichtlich","hinter","hinterm","hinters","im","in","inklusive","inmitten","innerhalb","innert","ins","je","jenseits","kontra","kraft","längs","laengs","längsseits","laengsseits","laut","links","mangels","minus","mit","mithilfe","mitsamt","mittels","nach","nächst","naechst","nah","namens","neben","nebst","nördlich","noerdlich","nordöstlich","nordoestlich","nordwestlich","oberhalb","ohne","östlich","oestlich","per","plus","pro","quer","rechts","rücksichtlich","ruecksichtlich","samt","seitens","seitlich","seitwärts","seitwaerts","südlich","suedlich","südöstlich","suedoestlich","südwestlich","suedwestlich","über","ueber","überm","ueberm","übern","uebern","übers","uebers","um","ums","unbeschadet","unerachtet","unfern","unter","unterhalb","unterm","untern","unters","unweit","vermittels","vermittelst","vermöge","vermoege","via","vom","von","vonseiten","vor","vorbehaltlich","wegen","wider","zeit","zu","zugunsten","zulieb","zuliebe","zum","zur","zusätzlich","zusaetzlich","zuungunsten","zuwider","zuzüglich","zuzueglich","zwecks","zwischen"],C=["und","oder","umso"],R=["auch","noch","nur"],q=["nun","so","gleichwohl"],W=["sage","sagst","sagt","sagest","saget","sagte","sagtest","sagten","sagtet","gesagt","fragst","fragt","fragest","fraget","fragte","fragtest","fragten","fragtet","gefragt","erkläre","erklärst","erklärt","erklaere","erklaerst","erklaert","erklärte","erklärtest","erklärtet","erklärten","erklaerte","erklaertest","erklaertet","erklaerten","denke","denkst","denkt","denkest","denket","dachte","dachtest","dachten","dachtet","dächte","dächtest","dächten","dächtet","daechte","daechtest","daechten","daechtet","finde","findest","findet","gefunden"],T=["sagen","erklären","erklaeren","denken","finden"],P=["sehr","recht","überaus","ueberaus","ungemein","weitaus","einigermaßen","einigermassen","ganz","schwer","tierisch","ungleich","ziemlich","übelst","uebelst","stark","volkommen","durchaus","gar"],M=["geschienen","meinst","meint","meinest","meinet","meinte","meintest","meinten","meintet","gemeint","stehe","stehst","steht","gehe","gehst","geht","gegangen","ging","gingst","gingen","gingt"],O=["tun","machen","stehen","wissen","gehen","kommen"],U=["einerlei","egal","neu","neue","neuer","neuen","neues","neuem","neuerer","neueren","neuerem","neueres","neuere","neuester","neuster","neuesten","neusten","neuestem","neustem","neuestes","neustes","neueste","neuste","alt","alter","alten","altem","altes","alte","ältere","älteren","älterer","älteres","ältester","ältesten","ältestem","ältestes","älteste","aeltere","aelteren","aelterer","aelteres","aeltester","aeltesten","aeltestem","aeltestes","aelteste","gut","guter","gutem","guten","gutes","gute","besser","besserer","besseren","besserem","besseres","bester","besten","bestem","bestes","beste","größte","grösste","groß","großer","großen","großem","großes","große","großerer","großerem","großeren","großeres","großere","großter","großten","großtem","großtes","großte","gross","grosser","grossen","grossem","grosses","grosse","grosserer","grosserem","grosseren","grosseres","grossere","grosster","grossten","grosstem","grosstes","grosste","einfacher","einfachen","einfachem","einfaches","einfache","einfacherer","einfacheren","einfacherem","einfacheres","einfachere","einfachste","einfachster","einfachsten","einfachstes","einfachstem","schnell","schneller","schnellen","schnellem","schnelles","schnelle","schnellere","schnellerer","schnelleren","schnelleres","schnellerem","schnellster","schnellste","schnellsten","schnellstem","schnellstes","weit","weiten","weitem","weites","weiterer","weiteren","weiterem","weiteres","weitere","weitester","weitesten","weitestem","weitestes","weiteste","eigen","eigener","eigenen","eigenes","eigenem","eigene","eigenerer","eignerer","eigeneren","eigneren","eigenerem","eignerem","eigeneres","eigneres","eigenere","eignere","eigenster","eigensten","eigenstem","eigenstes","eigenste","wenigster","wenigsten","wenigstem","wenigstes","wenigste","minderer","minderen","minderem","mindere","minderes","mindester","mindesten","mindestes","mindestem","mindeste","lang","langer","langen","langem","langes","längerer","längeren","längerem","längeres","längere","längster","längsten","längstem","längstes","längste","laengerer","laengeren","laengerem","laengeres","laengere","laengster","laengsten","laengstem","laengstes","laengste","tief","tiefer","tiefen","tiefem","tiefes","tiefe","tieferer","tieferen","tieferem","tieferes","tiefere","tiefster","tiefsten","tiefstem","tiefste","tiefstes","hoch","hoher","hohen","hohem","hohes","hohe","höher","höherer","höhere","höheren","höherem","höheres","hoeherer","hoehere","hoeheren","hoeherem","hoeheres","höchster","höchste","höchsten","höchstem","höchstes","hoechster","hoechste","hoechsten","hoechstem","hoechstes","regulär","regulärer","regulären","regulärem","reguläres","reguläre","regulaer","regulaerer","regulaeren","regulaerem","regulaeres","regulaere","regulärerer","reguläreren","regulärerem","reguläreres","regulärere","regulaererer","regulaereren","regulaererem","regulaereres","regulaerere","regulärster","regulärsten","regulärstem","regulärstes","regulärste","regulaerster","regulaersten","regulaerstem","regulaerstes","regulaerste","normal","normaler","normalen","normalem","normales","normale","normalerer","normaleren","normalerem","normaleres","normalere","normalster","normalsten","normalstem","normalstes","normalste","klein","kleiner","kleinen","kleinem","kleines","kleine","kleinerer","kleineres","kleineren","kleinerem","kleinere","kleinster","kleinsten","kleinstem","kleinstes","kleinste","winzig","winziger","winzigen","winzigem","winziges","winzigerer","winzigeren","winzigerem","winzigeres","winzigere","winzigster","winzigsten","winzigstem","winzigste","winzigstes","sogenannt","sogenannter","sogenannten","sogenanntem","sogenanntes","sogenannte","kurz","kurzer","kurzen","kurzem","kurzes","kurze","kürzerer","kürzeres","kürzeren","kürzerem","kürzere","kuerzerer","kuerzeres","kuerzeren","kuerzerem","kuerzere","kürzester","kürzesten","kürzestem","kürzestes","kürzeste","kuerzester","kuerzesten","kuerzestem","kuerzestes","kuerzeste","wirklicher","wirklichen","wirklichem","wirkliches","wirkliche","wirklicherer","wirklicheren","wirklicherem","wirklicheres","wirklichere","wirklichster","wirklichsten","wirklichstes","wirklichstem","wirklichste","eigentlicher","eigentlichen","eigentlichem","eigentliches","eigentliche","schön","schöner","schönen","schönem","schönes","schöne","schönerer","schöneren","schönerem","schöneres","schönere","schönster","schönsten","schönstem","schönstes","schönste","real","realer","realen","realem","reales","realerer","realeren","realerem","realeres","realere","realster","realsten","realstem","realstes","realste","derselbe","denselben","demselben","desselben","dasselbe","dieselbe","derselben","dieselben","gleicher","gleichen","gleichem","gleiches","gleiche","gleicherer","gleicheren","gleicherem","gleicheres","gleichere","gleichster","gleichsten","gleichstem","gleichstes","gleichste","bestimmter","bestimmten","bestimmtem","bestimmtes","bestimmte","bestimmtere","bestimmterer","bestimmterem","bestimmteren","bestimmteres","bestimmtester","bestimmtesten","bestimmtestem","bestimmtestes","bestimmteste","überwiegend","ueberwiegend","zumeist","meistens","meisten","meiste","meistem","meistes","großenteils","grossenteils","meistenteils","weithin","ständig","staendig","laufend","dauernd","andauernd","immerfort","irgendwo","irgendwann","ähnlicher","ähnlichen","ähnlichem","ähnliches","ähnliche","ähnlich","ähnlicherer","ähnlicheren","ähnlicherem","ähnlicheres","ähnlichere","ähnlichster","ähnlichsten","ähnlichstem","ähnlichstes","ähnlichste","schlecht","schlechter","schlechten","schlechtem","schlechtes","schlechte","schlechterer","schlechteren","schlechterem","schlechteres","schlechtere","schlechtester","schlechtesten","schlechtestem","schlechtestes","schlechteste","schlimm","schlimmer","schlimmen","schlimmem","schlimmes","schlimme","schlimmerer","schlimmeren","schlimmerem","schlimmeres","schlimmere","schlimmster","schlimmsten","schlimmstem","schlimmstes","schlimmste","toll","toller","tollen","tollem","tolles","tolle","tollerer","tolleren","tollerem","tollere","tolleres","tollster","tollsten","tollstem","tollstes","tollste","super","mögliche","möglicher","mögliches","möglichen","möglichem","möglich","moegliche","moeglicher","moegliches","moeglichen","moeglichem","moeglich","nächsten","nächster","nächstem","nächste","nächstes","naechsten","voll","voller","vollen","vollem","volle","volles","vollerer","volleren","vollerem","vollere","volleres","vollster","vollsten","vollstem","vollste","vollstes","außen","ganzer","ganzen","ganzem","ganze","ganzes","gern","gerne","oben","unten","zurück","zurueck","nicht","eher","ehere","eherem","eheren","eheres","eheste","ehestem","ehensten","ehesten"],I=["ach","aha","oh","au","bäh","baeh","igitt","huch","hurra","hoppla","nanu","oha","olala","pfui","tja","uups","wow","grr","äh","aeh","ähm","aehm","öhm","oehm","hm","mei","mhm","okay","richtig","eijeijeijei"],N=["g","el","tl","wg","be","bd","cl","dl","dag","do","gl","gr","kg","kl","cb","ccm","l","ms","mg","ml","mi","pk","pr","pp","sc","sp","st","sk","ta","tr","cm","mass"],V=["sekunde","sekunden","minute","minuten","stunde","stunden","uhr","tag","tages","tags","tage","tagen","woche","wochen","monat","monate","monates","monats","monaten","jahr","jahres","jahrs","jahre","jahren","morgens","mittags","abends","nachts","heute","gestern","morgen","vorgestern","übermorgen","uebermorgen"],L=["ding","dinge","dinges","dinger","dingern","dingen","sache","sachen","weise","weisen","wahrscheinlichkeit","zeug","zeuge","zeuges","zeugen","mal","einmal","teil","teile","teiles","teilen","prozent","prozents","prozentes","prozente","prozenten","beispiel","beispiele","beispieles","beispiels","beispielen","aspekt","aspekte","aspektes","aspekts","aspekten","idee","ideen","ahnung","ahnungen","thema","themas","themata","themen","fall","falle","falles","fälle","fällen","faelle","faellen","mensch","menschen","leute"],_=["nix","nixe","nixes","nixen","usw.","amen","ja","nein","euro"],H=(g([].concat(A,a,O,S,T)),g([].concat(d,U)),g([].concat(o,$,C,f,P,k)),g([].concat(b,j,w,m,["mir","dir","ihm","ihnen"],y,I,c,D,W,x,h,M,v,R,q,E,B,_,F,N,V,L,z,p)),g([].concat(o,c,d,f,p,y,z,w,m,k,v,E,F,B,j,h,a,x,A,D,S,$,C,R,q,W,T,b,["etwa","absolut","unbedingt","wieder","definitiv","bestimmt","immer","äußerst","aeußerst","höchst","hoechst","sofort","augenblicklich","umgehend","direkt","unmittelbar","nämlich","naemlich","natürlich","natuerlich","besonders","hauptsächlich","hauptsaechlich","jetzt","eben","heutzutage","eindeutig","wirklich","echt","wahrhaft","ehrlich","aufrichtig","wahrheitsgemäß","letztlich","einmalig","unübertrefflich","normalerweise","gewöhnlich","gewoehnlich","üblicherweise","ueblicherweise","sonst","fast","nahezu","beinahe","knapp","annähernd","annaehernd","geradezu","bald","vielleicht","wahrscheinlich","wohl","voraussichtlich","zugegeben","ursprünglich","insgesamt","tatsächlich","eigentlich","wahrhaftig","bereits","schon","oft","häufig","haeufig","regelmäßig","regelmaeßig","gleichmäßig","gleichmaeßig","einfach","lediglich","bloß","bloss","halt","wahlweise","eventuell","manchmal","teilweise","nie","niemals","nimmer","jemals","allzeit","irgendeinmal","anders","momentan","gegenwärtig","gegenwaertig","nebenbei","anderswo","woanders","anderswohin","anderorts","insbesondere","namentlich","sonderlich","ausdrücklich","ausdruecklich","vollends","kürzlich","kuerzlich","jüngst","juengst","unlängst","unlaengst","neuerdings","neulich","letztens","neuerlich","verhältnismäßig","verhaeltnismaessig","deutlich","klar","offenbar","anscheinend","genau","u.a","damals","zumindest"],P,M,O,I,U,N,L,_,V,["fr","hr","dr","prof"],["jr","jun","sen","sr"]))),G=[":","aber","als","bevor","bis","da","damit","daß","dass","denn","doch","ehe","falls","gleichwohl","indem","indes","indessen","insofern","insoweit","nachdem","nun","ob","obgleich","obschon","obwohl","obzwar","oder","seitdem","sobald","sodass","sofern","solange","sondern","sooft","soviel","soweit","sowie","trotz","und","ungeachtet","waehrend","während","weil","welche","welchem","welchen","welcher","welches","wem","wen","wenn","wenngleich","wennschon","wer","wes","wessen","wie","wiewohl","wohingegen","zumal"],Z=[["anstatt","dass"],["bald","bald"],["dadurch","dass"],["dessen ungeachtet","dass"],["entweder","oder"],["einerseits","andererseits"],["erst","wenn"],["je","desto"],["je","umso"],["umso","umso"],["mal","mal"],["nicht nur","sondern auch"],["ob","oder"],["ohne","dass"],["so","dass"],["sowohl","als auch"],["sowohl","wie auch"],["teils","teils"],["unbeschadet dessen","dass"],["weder","noch"],["wenn","auch"],["wenn","schon"],["nicht weil","sondern"]],Q=JSON.parse('{"vowels":"aeiouyäöüáéâàèîêâûôœ","deviations":{"vowels":[{"fragments":["ouil","deaux","deau$","oard","äthiop","euil","veau","eau$","ueue","lienisch","ance$","ence$","time$","once$","ziat","guette","ête","ôte$","[hp]omme$","[qdscn]ue$","aire$","ture$","êpe$","[^q]ui$","tiche$","vice$","oile$","zial","cruis","leas","coa[ct]","[^i]deal","[fw]eat","[lsx]ed$"],"countModifier":-1},{"fragments":["aau","a[äöüo]","äue","äeu","aei","aue","aeu","ael","ai[aeo]","saik","aismus","ä[aeoi]","auä","éa","e[äaoö]","ei[eo]","ee[aeiou]","eu[aäe]","eum$","eü","o[aäöü]","poet","oo[eo]","oie","oei[^l]","oeu[^f]","öa","[fgrz]ieu","mieun","tieur","ieum","i[aiuü]","[^l]iä","[^s]chien","io[bcdfhjkmpqtuvwx]","[bdhmprv]ion","[lr]ior","[^g]io[gs]","[dr]ioz","elioz","zioni","bio[lnorz]","iö[^s]","ie[ei]","rier$","öi[eg]","[^r]öisch","[^gqv]u[aeéioöuü]","quie$","quie[^s]","uäu","^us-","^it-","üe","naiv","aisch$","aische$","aische[nrs]$","[lst]ien","dien$","gois","[^g]rient","[aeiou]y[aeiou]","byi","yä","[a-z]y[ao]","yau","koor","scient","eriel","[dg]oing"],"countModifier":1},{"fragments":["eauü","ioi","ioo","ioa","iii","oai","eueu"],"countModifier":1}],"words":{"full":[{"word":"beach","syllables":1},{"word":"beat","syllables":1},{"word":"beau","syllables":1},{"word":"beaune","syllables":1},{"word":"belle","syllables":1},{"word":"bouche","syllables":1},{"word":"brake","syllables":1},{"word":"cache","syllables":1},{"word":"chaiselongue","syllables":2},{"word":"choke","syllables":1},{"word":"cordiale","syllables":3},{"word":"core","syllables":1},{"word":"dope","syllables":1},{"word":"eat","syllables":1},{"word":"eye","syllables":1},{"word":"fake","syllables":1},{"word":"fame","syllables":1},{"word":"fatigue","syllables":2},{"word":"femme","syllables":1},{"word":"force","syllables":1},{"word":"game","syllables":1},{"word":"games","syllables":1},{"word":"gate","syllables":1},{"word":"grande","syllables":1},{"word":"ice","syllables":1},{"word":"ion","syllables":2},{"word":"joke","syllables":1},{"word":"jupe","syllables":1},{"word":"maisch","syllables":1},{"word":"maische","syllables":2},{"word":"move","syllables":1},{"word":"native","syllables":2},{"word":"nice","syllables":1},{"word":"one","syllables":1},{"word":"pipe","syllables":1},{"word":"prime","syllables":1},{"word":"rate","syllables":1},{"word":"rhythm","syllables":2},{"word":"ride","syllables":1},{"word":"rides","syllables":1},{"word":"rien","syllables":2},{"word":"save","syllables":1},{"word":"science","syllables":2},{"word":"siècle","syllables":1},{"word":"site","syllables":1},{"word":"suite","syllables":1},{"word":"take","syllables":1},{"word":"taupe","syllables":1},{"word":"universe","syllables":3},{"word":"vogue","syllables":1},{"word":"wave","syllables":1},{"word":"zion","syllables":2}],"fragments":{"global":[{"word":"abreaktion","syllables":4},{"word":"adware","syllables":2},{"word":"affaire","syllables":3},{"word":"aiguière","syllables":2},{"word":"anisette","syllables":3},{"word":"appeal","syllables":2},{"word":"backstage","syllables":2},{"word":"bankrate","syllables":2},{"word":"baseball","syllables":2},{"word":"basejump","syllables":2},{"word":"beachcomber","syllables":3},{"word":"beachvolleyball","syllables":4},{"word":"beagle","syllables":2},{"word":"beamer","syllables":2},{"word":"beamer","syllables":2},{"word":"béarnaise","syllables":3},{"word":"beaufort","syllables":2},{"word":"beaujolais","syllables":3},{"word":"beauté","syllables":2},{"word":"beauty","syllables":2},{"word":"belgier","syllables":3},{"word":"bestien","syllables":2},{"word":"biskuit","syllables":2},{"word":"bleach","syllables":1},{"word":"blue","syllables":1},{"word":"board","syllables":1},{"word":"boat","syllables":1},{"word":"bodysuit","syllables":3},{"word":"bordelaise","syllables":3},{"word":"break","syllables":1},{"word":"build","syllables":1},{"word":"bureau","syllables":2},{"word":"business","syllables":2},{"word":"cabrio","syllables":3},{"word":"cabriolet","syllables":4},{"word":"cachesexe","syllables":2},{"word":"camaieu","syllables":3},{"word":"canyon","syllables":2},{"word":"case","syllables":1},{"word":"catsuit","syllables":2},{"word":"centime","syllables":3},{"word":"chaise","syllables":2},{"word":"champion","syllables":2},{"word":"championat","syllables":3},{"word":"chapiteau","syllables":3},{"word":"chateau","syllables":2},{"word":"château","syllables":2},{"word":"cheat","syllables":1},{"word":"cheese","syllables":1},{"word":"chihuahua","syllables":3},{"word":"choice","syllables":1},{"word":"circonflexe","syllables":3},{"word":"clean","syllables":1},{"word":"cloche","syllables":1},{"word":"close","syllables":1},{"word":"clothes","syllables":1},{"word":"commerce","syllables":2},{"word":"crime","syllables":1},{"word":"crossrate","syllables":2},{"word":"cuisine","syllables":2},{"word":"culotte","syllables":2},{"word":"death","syllables":1},{"word":"defense","syllables":2},{"word":"détente","syllables":2},{"word":"dread","syllables":1},{"word":"dream","syllables":1},{"word":"dresscode","syllables":2},{"word":"dungeon","syllables":2},{"word":"easy","syllables":2},{"word":"engagement","syllables":3},{"word":"entente","syllables":2},{"word":"eye-catcher","syllables":3},{"word":"eyecatcher","syllables":3},{"word":"eyeliner","syllables":3},{"word":"eyeword","syllables":2},{"word":"fashion","syllables":2},{"word":"feature","syllables":2},{"word":"ferien","syllables":3},{"word":"fineliner","syllables":3},{"word":"fisheye","syllables":2},{"word":"flake","syllables":1},{"word":"flambeau","syllables":2},{"word":"flatrate","syllables":2},{"word":"fleece","syllables":1},{"word":"fraîche","syllables":1},{"word":"freak","syllables":1},{"word":"frites","syllables":1},{"word":"future","syllables":2},{"word":"gaelic","syllables":2},{"word":"game-show","syllables":2},{"word":"gameboy","syllables":2},{"word":"gamepad","syllables":2},{"word":"gameplay","syllables":2},{"word":"gameport","syllables":2},{"word":"gameshow","syllables":2},{"word":"garigue","syllables":2},{"word":"garrigue","syllables":2},{"word":"gatefold","syllables":2},{"word":"gateway","syllables":2},{"word":"geflashed","syllables":2},{"word":"georgier","syllables":4},{"word":"goal","syllables":1},{"word":"grapefruit","syllables":2},{"word":"great","syllables":1},{"word":"groupware","syllables":2},{"word":"gueule","syllables":1},{"word":"guide","syllables":1},{"word":"guilloche","syllables":2},{"word":"gynäzeen","syllables":4},{"word":"gynözeen","syllables":4},{"word":"haircare","syllables":2},{"word":"hardcore","syllables":2},{"word":"hardware","syllables":2},{"word":"head","syllables":1},{"word":"hearing","syllables":2},{"word":"heart","syllables":1},{"word":"heavy","syllables":2},{"word":"hedge","syllables":1},{"word":"heroin","syllables":3},{"word":"inclusive","syllables":3},{"word":"initiative","syllables":4},{"word":"inside","syllables":2},{"word":"jaguar","syllables":3},{"word":"jalousette","syllables":3},{"word":"jeans","syllables":1},{"word":"jeunesse","syllables":2},{"word":"juice","syllables":1},{"word":"jukebox","syllables":2},{"word":"jumpsuit","syllables":2},{"word":"kanarien","syllables":4},{"word":"kapriole","syllables":4},{"word":"karosserielinie","syllables":6},{"word":"konopeen","syllables":4},{"word":"lacrosse","syllables":2},{"word":"laplace","syllables":2},{"word":"late-","syllables":1},{"word":"lead","syllables":1},{"word":"league","syllables":1},{"word":"learn","syllables":1},{"word":"légière","syllables":2},{"word":"lizenziat","syllables":4},{"word":"load","syllables":1},{"word":"lotterielos","syllables":4},{"word":"lounge","syllables":1},{"word":"lyzeen","syllables":3},{"word":"madame","syllables":2},{"word":"mademoiselle","syllables":3},{"word":"magier","syllables":3},{"word":"make-up","syllables":2},{"word":"malware","syllables":2},{"word":"management","syllables":3},{"word":"manteau","syllables":2},{"word":"mausoleen","syllables":4},{"word":"mauve","syllables":1},{"word":"medien","syllables":3},{"word":"mesdames","syllables":2},{"word":"mesopotamien","syllables":6},{"word":"milliarde","syllables":3},{"word":"missile","syllables":2},{"word":"miszellaneen","syllables":5},{"word":"mousse","syllables":1},{"word":"mousseline","syllables":3},{"word":"museen","syllables":3},{"word":"musette","syllables":2},{"word":"nahuatl","syllables":2},{"word":"noisette","syllables":2},{"word":"notebook","syllables":2},{"word":"nuance","syllables":3},{"word":"nuklease","syllables":4},{"word":"odeen","syllables":3},{"word":"offline","syllables":2},{"word":"offside","syllables":2},{"word":"oleaster","syllables":4},{"word":"on-stage","syllables":2},{"word":"online","syllables":2},{"word":"orpheen","syllables":3},{"word":"parforceritt","syllables":3},{"word":"patiens","syllables":2},{"word":"patient","syllables":2},{"word":"peace","syllables":1},{"word":"peace","syllables":1},{"word":"peanuts","syllables":2},{"word":"people","syllables":2},{"word":"perineen","syllables":4},{"word":"peritoneen","syllables":5},{"word":"picture","syllables":2},{"word":"piece","syllables":1},{"word":"pipeline","syllables":2},{"word":"plateau","syllables":2},{"word":"poesie","syllables":3},{"word":"poleposition","syllables":4},{"word":"portemanteau","syllables":3},{"word":"portemonnaie","syllables":3},{"word":"primerate","syllables":2},{"word":"primerate","syllables":2},{"word":"primetime","syllables":2},{"word":"protease","syllables":4},{"word":"protein","syllables":3},{"word":"prytaneen","syllables":4},{"word":"quotient","syllables":2},{"word":"radio","syllables":3},{"word":"reader","syllables":2},{"word":"ready","syllables":2},{"word":"reallife","syllables":2},{"word":"repeat","syllables":2},{"word":"retake","syllables":2},{"word":"rigole","syllables":2},{"word":"risolle","syllables":2},{"word":"road","syllables":1},{"word":"roaming","syllables":2},{"word":"roquefort","syllables":2},{"word":"safe","syllables":1},{"word":"savonette","syllables":3},{"word":"sciencefiction","syllables":3},{"word":"search","syllables":1},{"word":"selfmade","syllables":2},{"word":"septime","syllables":3},{"word":"serapeen","syllables":4},{"word":"service","syllables":2},{"word":"serviette","syllables":2},{"word":"share","syllables":1},{"word":"shave","syllables":1},{"word":"shore","syllables":1},{"word":"sidebar","syllables":2},{"word":"sideboard","syllables":2},{"word":"sidekick","syllables":2},{"word":"silhouette","syllables":3},{"word":"sitemap","syllables":2},{"word":"slide","syllables":1},{"word":"sneak","syllables":1},{"word":"soap","syllables":1},{"word":"softcore","syllables":2},{"word":"software","syllables":2},{"word":"soutanelle","syllables":3},{"word":"speak","syllables":1},{"word":"special","syllables":2},{"word":"spracheinstellung","syllables":5},{"word":"spyware","syllables":2},{"word":"square","syllables":1},{"word":"stagediving","syllables":3},{"word":"stakeholder","syllables":3},{"word":"statement","syllables":2},{"word":"steady","syllables":2},{"word":"steak","syllables":1},{"word":"stealth","syllables":1},{"word":"steam","syllables":1},{"word":"stoned","syllables":1},{"word":"stracciatella","syllables":4},{"word":"stream","syllables":1},{"word":"stride","syllables":1},{"word":"strike","syllables":1},{"word":"suitcase","syllables":2},{"word":"sweepstake","syllables":2},{"word":"t-bone","syllables":2},{"word":"t-shirt","syllables":1},{"word":"tailgate","syllables":2},{"word":"take-off","syllables":2},{"word":"take-over","syllables":3},{"word":"takeaway","syllables":3},{"word":"takeoff","syllables":2},{"word":"takeover","syllables":3},{"word":"throat","syllables":1},{"word":"time-out","syllables":2},{"word":"timelag","syllables":2},{"word":"timeline","syllables":2},{"word":"timesharing","syllables":3},{"word":"toast","syllables":1},{"word":"traubenmaische","syllables":4},{"word":"tristesse","syllables":2},{"word":"usenet","syllables":2},{"word":"varietät","syllables":4},{"word":"varieté","syllables":4},{"word":"vinaigrette","syllables":3},{"word":"vintage","syllables":2},{"word":"violett","syllables":3},{"word":"voice","syllables":1},{"word":"wakeboard","syllables":2},{"word":"washed","syllables":1},{"word":"waveboard","syllables":2},{"word":"wear","syllables":1},{"word":"wear","syllables":1},{"word":"website","syllables":2},{"word":"white","syllables":1},{"word":"widescreen","syllables":2},{"word":"wire","syllables":1},{"word":"yacht","syllables":1},{"word":"yorkshire","syllables":2},{"word":"éprouvette","syllables":3,"notFollowedBy":["n"]},{"word":"galette","syllables":2,"notFollowedBy":["n"]},{"word":"gigue","syllables":1,"notFollowedBy":["n"]},{"word":"groove","syllables":1,"notFollowedBy":["n"]},{"word":"morgue","syllables":1,"notFollowedBy":["n"]},{"word":"paillette","syllables":2,"notFollowedBy":["n"]},{"word":"raclette","syllables":2,"notFollowedBy":["n"]},{"word":"roulette","syllables":2,"notFollowedBy":["n"]},{"word":"spike","syllables":1,"notFollowedBy":["n"]},{"word":"style","syllables":1,"notFollowedBy":["n"]},{"word":"tablette","syllables":2,"notFollowedBy":["n"]},{"word":"grunge","syllables":1,"notFollowedBy":["r"]},{"word":"size","syllables":1,"notFollowedBy":["r"]},{"word":"value","syllables":1,"notFollowedBy":["r"]},{"word":"quiche","syllables":1,"notFollowedBy":["s"]},{"word":"house","syllables":1,"notFollowedBy":["n","s"]},{"word":"sauce","syllables":1,"notFollowedBy":["n","s"]},{"word":"space","syllables":1,"notFollowedBy":["n","s"]},{"word":"airline","syllables":2,"notFollowedBy":["n","r"]},{"word":"autosave","syllables":3,"notFollowedBy":["n","r"]},{"word":"bagpipe","syllables":2,"notFollowedBy":["n","r"]},{"word":"bike","syllables":1,"notFollowedBy":["n","r"]},{"word":"dance","syllables":1,"notFollowedBy":["n","r"]},{"word":"deadline","syllables":2,"notFollowedBy":["n","r"]},{"word":"halfpipe","syllables":2,"notFollowedBy":["n","r"]},{"word":"headline","syllables":2,"notFollowedBy":["n","r"]},{"word":"home","syllables":1,"notFollowedBy":["n","r"]},{"word":"hornpipe","syllables":2,"notFollowedBy":["n","r"]},{"word":"hotline","syllables":2,"notFollowedBy":["n","r"]},{"word":"infoline","syllables":3,"notFollowedBy":["n","r"]},{"word":"inline","syllables":2,"notFollowedBy":["n","r"]},{"word":"kite","syllables":1,"notFollowedBy":["n","r"]},{"word":"rollerblade","syllables":1,"notFollowedBy":["n","r"]},{"word":"score","syllables":1,"notFollowedBy":["n","r"]},{"word":"skyline","syllables":2,"notFollowedBy":["n","r"]},{"word":"slackline","syllables":2,"notFollowedBy":["n","r"]},{"word":"slice","syllables":1,"notFollowedBy":["n","r","s"]},{"word":"snooze","syllables":1,"notFollowedBy":["n","r"]},{"word":"storyline","syllables":3,"notFollowedBy":["n","r"]},{"word":"office","syllables":2,"notFollowedBy":["s","r"]},{"word":"space","syllables":1,"notFollowedBy":["n","s","r"]},{"word":"tease","syllables":1,"notFollowedBy":["n","s","r"]},{"word":"cache","syllables":1,"notFollowedBy":["t"]}],"atBeginningOrEnd":[{"word":"case","syllables":1},{"word":"life","syllables":1},{"word":"teak","syllables":1},{"word":"team","syllables":1},{"word":"creme","syllables":1,"notFollowedBy":["n","r"]},{"word":"crème","syllables":1,"notFollowedBy":["n","r"]},{"word":"drive","syllables":1,"notFollowedBy":["n","r"]},{"word":"skate","syllables":1,"notFollowedBy":["n","r"]},{"word":"update","syllables":2,"notFollowedBy":["n","r"]},{"word":"upgrade","syllables":2,"notFollowedBy":["n","r"]}],"atBeginning":[{"word":"anion","syllables":3},{"word":"facelift","syllables":2},{"word":"jiu","syllables":1},{"word":"pace","syllables":1},{"word":"shake","syllables":1},{"word":"tea","syllables":1},{"word":"trade","syllables":1},{"word":"deal","syllables":1}],"atEnd":[{"word":"face","syllables":1},{"word":"file","syllables":1},{"word":"mousse","syllables":1},{"word":"plate","syllables":1},{"word":"tape","syllables":1},{"word":"byte","syllables":1,"alsoFollowedBy":["s"]},{"word":"cape","syllables":1,"alsoFollowedBy":["s"]},{"word":"five","syllables":1,"alsoFollowedBy":["s"]},{"word":"hype","syllables":1,"alsoFollowedBy":["s"]},{"word":"leak","syllables":1,"alsoFollowedBy":["s"]},{"word":"like","syllables":1,"alsoFollowedBy":["s"]},{"word":"make","syllables":1,"alsoFollowedBy":["s"]},{"word":"phone","syllables":1,"alsoFollowedBy":["s"]},{"word":"rave","syllables":1,"alsoFollowedBy":["s"]},{"word":"regime","syllables":2,"alsoFollowedBy":["s"]},{"word":"statue","syllables":2,"alsoFollowedBy":["s"]},{"word":"store","syllables":1,"alsoFollowedBy":["s"]},{"word":"wave","syllables":1,"alsoFollowedBy":["s"]},{"word":"date","syllables":1,"notFollowedBy":["n"]},{"word":"image","syllables":2,"notFollowedBy":["s"]}]}}}}'),J={productPages:{parameters:{recommendedMinimum:3,recommendedMaximum:6,acceptableMaximum:7,acceptableMinimum:1}}},Y=window.lodash;var K=r(429),X=r.n(K);const ee=new RegExp("["+["'","‘","’","‛","`","‹","›"].join("")+"]","g");function te(e){return function(e){return e.replace(/[“”〝〞〟‟„『』«»]/g,'"')}(function(e){return e.replace(ee,"'")}(e))}const re=["address","article","aside","blockquote","canvas","dd","div","dl","fieldset","figcaption","figure","footer","form","h1","h2","h3","h4","h5","h6","header","hgroup","hr","li","main","nav","noscript","ol","output","p","pre","section","table","tfoot","ul","video"],se=["b","big","i","small","tt","abbr","acronym","cite","code","dfn","em","kbd","strong","samp","time","var","a","bdo","br","img","map","object","q","script","span","sub","sup","button","input","label","select","textarea"],ne=(new RegExp("^("+re.join("|")+")$","i"),new RegExp("^("+se.join("|")+")$","i"),new RegExp("^<("+re.join("|")+")[^>]*?>$","i")),le=new RegExp("^</("+re.join("|")+")[^>]*?>$","i"),ae=new RegExp("^<("+se.join("|")+")[^>]*>$","i"),ie=new RegExp("^</("+se.join("|")+")[^>]*>$","i"),be=/^<([^>\s/]+)[^>]*>$/,ue=/^<\/([^>\s]+)[^>]*>$/,ge=/^[^<]+$/,he=/^<[^><]*$/,oe=/<!--(.|[\r\n])*?-->/g;let ce,de=[];(0,Y.memoize)((function(e){const t=[];let r=0,s="",n="",l="";return e=e.replace(oe,""),de=[],ce=X()((function(e){de.push(e)})),ce.addRule(ge,"content"),ce.addRule(he,"greater-than-sign-content"),ce.addRule(ne,"block-start"),ce.addRule(le,"block-end"),ce.addRule(ae,"inline-start"),ce.addRule(ie,"inline-end"),ce.addRule(be,"other-element-start"),ce.addRule(ue,"other-element-end"),ce.onText(e),ce.end(),(0,Y.forEach)(de,(function(e,a){const i=de[a+1];switch(e.type){case"content":case"greater-than-sign-content":case"inline-start":case"inline-end":case"other-tag":case"other-element-start":case"other-element-end":case"greater than sign":i&&(0!==r||"block-start"!==i.type&&"block-end"!==i.type)?n+=e.src:(n+=e.src,t.push(n),s="",n="",l="");break;case"block-start":0!==r&&(""!==n.trim()&&t.push(n),n="",l=""),r++,s=e.src;break;case"block-end":r--,l=e.src,""!==s&&""!==l?t.push(s+n+l):""!==n.trim()&&t.push(n),s="",n="",l=""}r<0&&(r=0)})),t})),new RegExp("^<("+re.join("|")+")[^>]*?>","i"),new RegExp("</("+re.join("|")+")[^>]*?>$","i");const we=new RegExp("^[.]$"),me=/^<[^><]*$/,fe=/^<([^>\s/]+)[^>]*>$/im,pe=/^<\/([^>\s]+)[^>]*>$/im,ke=/^\s*[[({]\s*$/,ye=/^\s*[\])}]\s*$/,ze=function(e,t=!1,r="",s=!1){const n="("+(e=(0,Y.map)(e,(function(e){return s&&(e=function(e){const t=[{base:"a",letters:/[\u0061\u24D0\uFF41\u1E9A\u00E0\u00E1\u00E2\u1EA7\u1EA5\u1EAB\u1EA9\u00E3\u0101\u0103\u1EB1\u1EAF\u1EB5\u1EB3\u0227\u01E1\u00E4\u01DF\u1EA3\u00E5\u01FB\u01CE\u0201\u0203\u1EA1\u1EAD\u1EB7\u1E01\u0105\u2C65\u0250]/g},{base:"aa",letters:/[\uA733]/g},{base:"ae",letters:/[\u00E6\u01FD\u01E3]/g},{base:"ao",letters:/[\uA735]/g},{base:"au",letters:/[\uA737]/g},{base:"av",letters:/[\uA739\uA73B]/g},{base:"ay",letters:/[\uA73D]/g},{base:"b",letters:/[\u0062\u24D1\uFF42\u1E03\u1E05\u1E07\u0180\u0183\u0253]/g},{base:"c",letters:/[\u0063\u24D2\uFF43\u0107\u0109\u010B\u010D\u00E7\u1E09\u0188\u023C\uA73F\u2184]/g},{base:"d",letters:/[\u0064\u24D3\uFF44\u1E0B\u010F\u1E0D\u1E11\u1E13\u1E0F\u0111\u018C\u0256\u0257\uA77A]/g},{base:"dz",letters:/[\u01F3\u01C6]/g},{base:"e",letters:/[\u0065\u24D4\uFF45\u00E8\u00E9\u00EA\u1EC1\u1EBF\u1EC5\u1EC3\u1EBD\u0113\u1E15\u1E17\u0115\u0117\u00EB\u1EBB\u011B\u0205\u0207\u1EB9\u1EC7\u0229\u1E1D\u0119\u1E19\u1E1B\u0247\u025B\u01DD]/g},{base:"f",letters:/[\u0066\u24D5\uFF46\u1E1F\u0192\uA77C]/g},{base:"g",letters:/[\u0067\u24D6\uFF47\u01F5\u011D\u1E21\u011F\u0121\u01E7\u0123\u01E5\u0260\uA7A1\u1D79\uA77F]/g},{base:"h",letters:/[\u0068\u24D7\uFF48\u0125\u1E23\u1E27\u021F\u1E25\u1E29\u1E2B\u1E96\u0127\u2C68\u2C76\u0265]/g},{base:"hv",letters:/[\u0195]/g},{base:"i",letters:/[\u0069\u24D8\uFF49\u00EC\u00ED\u00EE\u0129\u012B\u012D\u00EF\u1E2F\u1EC9\u01D0\u0209\u020B\u1ECB\u012F\u1E2D\u0268\u0131]/g},{base:"j",letters:/[\u006A\u24D9\uFF4A\u0135\u01F0\u0249]/g},{base:"k",letters:/[\u006B\u24DA\uFF4B\u1E31\u01E9\u1E33\u0137\u1E35\u0199\u2C6A\uA741\uA743\uA745\uA7A3]/g},{base:"l",letters:/[\u006C\u24DB\uFF4C\u0140\u013A\u013E\u1E37\u1E39\u013C\u1E3D\u1E3B\u017F\u0142\u019A\u026B\u2C61\uA749\uA781\uA747]/g},{base:"lj",letters:/[\u01C9]/g},{base:"m",letters:/[\u006D\u24DC\uFF4D\u1E3F\u1E41\u1E43\u0271\u026F]/g},{base:"n",letters:/[\u006E\u24DD\uFF4E\u01F9\u0144\u00F1\u1E45\u0148\u1E47\u0146\u1E4B\u1E49\u019E\u0272\u0149\uA791\uA7A5]/g},{base:"nj",letters:/[\u01CC]/g},{base:"o",letters:/[\u006F\u24DE\uFF4F\u00F2\u00F3\u00F4\u1ED3\u1ED1\u1ED7\u1ED5\u00F5\u1E4D\u022D\u1E4F\u014D\u1E51\u1E53\u014F\u022F\u0231\u00F6\u022B\u1ECF\u0151\u01D2\u020D\u020F\u01A1\u1EDD\u1EDB\u1EE1\u1EDF\u1EE3\u1ECD\u1ED9\u01EB\u01ED\u00F8\u01FF\u0254\uA74B\uA74D\u0275]/g},{base:"oi",letters:/[\u01A3]/g},{base:"ou",letters:/[\u0223]/g},{base:"oo",letters:/[\uA74F]/g},{base:"p",letters:/[\u0070\u24DF\uFF50\u1E55\u1E57\u01A5\u1D7D\uA751\uA753\uA755]/g},{base:"q",letters:/[\u0071\u24E0\uFF51\u024B\uA757\uA759]/g},{base:"r",letters:/[\u0072\u24E1\uFF52\u0155\u1E59\u0159\u0211\u0213\u1E5B\u1E5D\u0157\u1E5F\u024D\u027D\uA75B\uA7A7\uA783]/g},{base:"s",letters:/[\u0073\u24E2\uFF53\u00DF\u015B\u1E65\u015D\u1E61\u0161\u1E67\u1E63\u1E69\u0219\u015F\u023F\uA7A9\uA785\u1E9B]/g},{base:"t",letters:/[\u0074\u24E3\uFF54\u1E6B\u1E97\u0165\u1E6D\u021B\u0163\u1E71\u1E6F\u0167\u01AD\u0288\u2C66\uA787]/g},{base:"tz",letters:/[\uA729]/g},{base:"u",letters:/[\u0075\u24E4\uFF55\u00F9\u00FA\u00FB\u0169\u1E79\u016B\u1E7B\u016D\u00FC\u01DC\u01D8\u01D6\u01DA\u1EE7\u016F\u0171\u01D4\u0215\u0217\u01B0\u1EEB\u1EE9\u1EEF\u1EED\u1EF1\u1EE5\u1E73\u0173\u1E77\u1E75\u0289]/g},{base:"v",letters:/[\u0076\u24E5\uFF56\u1E7D\u1E7F\u028B\uA75F\u028C]/g},{base:"vy",letters:/[\uA761]/g},{base:"w",letters:/[\u0077\u24E6\uFF57\u1E81\u1E83\u0175\u1E87\u1E85\u1E98\u1E89\u2C73]/g},{base:"x",letters:/[\u0078\u24E7\uFF58\u1E8B\u1E8D]/g},{base:"y",letters:/[\u0079\u24E8\uFF59\u1EF3\u00FD\u0177\u1EF9\u0233\u1E8F\u00FF\u1EF7\u1E99\u1EF5\u01B4\u024F\u1EFF]/g},{base:"z",letters:/[\u007A\u24E9\uFF5A\u017A\u1E91\u017C\u017E\u1E93\u1E95\u01B6\u0225\u0240\u2C6C\uA763]/g}];for(let r=0;r<t.length;r++)e=e.replace(t[r].letters,t[r].base);return e}(e)),e=function(e){return function(e){return(e=(e=(e=(e=e.replace(/\s{2,}/g," ")).replace(/\s\.$/,".")).replace(/^\s+|\s+$/g,"")).replace(/\s。/g,"。")).replace(/。\s/g,"。")}(e=e.replace(/(<([^>]+)>)/gi," "))}(function(e){return function(e){return e.replace(/\s/g," ")}(e=function(e){return e.replace(/\u2014/g," ")}(e=function(e){return e.replace(/&nbsp;/g," ")}(e)))}(e)),t?e:function(e,t=!1,r="",s=""){let n,l;return n="id"===s?'[ \\u00a0\\n\\r\\t.,()”“〝〞〟‟„"+;!¡?¿:/»«‹›'+r+"<>":'[ \\u00a0\\u2014\\u06d4\\u061f\\u060C\\u061B\\n\\r\\t.,()”“〝〞〟‟„"+\\-;!¡?¿:/»«‹›'+r+"<>",l=t?"($|((?="+n+"]))|((['‘’‛`])("+n+"])))":"($|("+n+"])|((['‘’‛`])("+n+"])))","(^|"+n+"'‘’‛`])"+e+l}(e,!0,r)}))).join(")|(")+")";return new RegExp(n,"ig")}(["A.D.","Adm.","Adv.","B.C.","Br.","Brig.","Cmrd.","Col.","Cpl.","Cpt.","Dr.","Esq.","Fr.","Gen.","Gov.","Hon.","Jr.","Lieut.","Lt.","Maj.","Mr.","Mrs.","Ms.","Msgr.","Mx.","No.","Pfc.","Pr.","Prof.","Pvt.","Rep.","Reps.","Rev.","Rt. Hon.","Sen.","Sens.","Sgt.","Sps.","Sr.","St.","vs.","i.e.","e.g.","viz.","Mt."].map((e=>e.replace(".","\\.")))),ve="(^|$|["+[" ","\\n","\\r","\\t"," ","۔","؟","،","؛"," ",".",",","'","(",")",'"',"+","-",";","!","?",":","/","»","«","‹","›","<",">","”","“","〝","〞","〟","‟","„"].map((e=>"\\"+e)).join("")+"])",Ee=new RegExp(ve+"[A-Za-z]$"),Fe=/<\/?([^\s]+?)(\s|>)/,Be=["p","div","h1","h2","h3","h4","h5","h6","span","li","main"];class je{constructor(){this.sentenceDelimiters='”〞〟„』›»’‛`"?!…۔؟'}getSentenceDelimiters(){return this.sentenceDelimiters}isNumber(e){return!(0,Y.isNaN)(parseInt(e,10))}isBreakTag(e){return/<\/?br/.test(e)}isQuotation(e){return"'"===(e=te(e))||'"'===e}endsWithOrdinalDot(){return!1}isPunctuation(e){return"¿"===e||"¡"===e}removeDuplicateWhitespace(e){return e.replace(/\s+/," ")}isCapitalLetter(e){return e!==e.toLocaleLowerCase()}isSmallerThanSign(e){return"<"===e}getNextTwoCharacters(e){let t="";return(0,Y.isUndefined)(e[0])||(t+=e[0].src),(0,Y.isUndefined)(e[1])||(t+=e[1].src),t=this.removeDuplicateWhitespace(t),t}isLetterFromSpecificLanguage(e){return[/^[\u0590-\u05fe]+$/i,/^[\u0600-\u06FF]+$/i,/^[\uFB8A\u067E\u0686\u06AF]+$/i].some((t=>t.test(e)))}isValidSentenceBeginning(e){return this.isCapitalLetter(e)||this.isLetterFromSpecificLanguage(e)||this.isNumber(e)||this.isQuotation(e)||this.isPunctuation(e)||this.isSmallerThanSign(e)}isSentenceStart(e){return!(0,Y.isUndefined)(e)&&("html-start"===e.type||"html-end"===e.type||"block-start"===e.type)}isSentenceEnding(e){return!(0,Y.isUndefined)(e)&&("full-stop"===e.type||"sentence-delimiter"===e.type)}isPartOfPersonInitial(e,t,r,s){return!(0,Y.isUndefined)(e)&&!(0,Y.isUndefined)(r)&&!(0,Y.isUndefined)(s)&&!(0,Y.isUndefined)(t)&&"full-stop"===e.type&&"sentence"===t.type&&Ee.test(t.src)&&"sentence"===r.type&&1===r.src.trim().length&&"full-stop"===s.type}tokenizeSmallerThanContent(e,t,r){const s=e.src.substring(1),n=this.createTokenizer();this.tokenize(n.tokenizer,s);const l=this.getSentencesFromTokens(n.tokens,!1);if(l[0]=(0,Y.isUndefined)(l[0])?"<":"<"+l[0],this.isValidSentenceBeginning(l[0])&&(t.push(r),r=""),r+=l[0],l.length>1){t.push(r),r="",l.shift();const e=l.pop();l.forEach((e=>{t.push(e)}));const s=new RegExp("[."+this.getSentenceDelimiters()+"]$");e.match(s)?t.push(e):r=e}return{tokenSentences:t,currentSentence:r}}createTokenizer(){const e=new RegExp("^["+this.getSentenceDelimiters()+"]$"),t=new RegExp("^[^."+this.getSentenceDelimiters()+"<\\(\\)\\[\\]]+$"),r=[],s=X()((function(e){r.push(e)}));return s.addRule(we,"full-stop"),s.addRule(me,"smaller-than-sign-content"),s.addRule(fe,"html-start"),s.addRule(pe,"html-end"),s.addRule(ke,"block-start"),s.addRule(ye,"block-end"),s.addRule(e,"sentence-delimiter"),s.addRule(t,"sentence"),{tokenizer:s,tokens:r}}tokenize(e,t){e.onText(t);try{e.end()}catch(e){console.error("Tokenizer end error:",e,e.tokenizer2)}}endsWithAbbreviation(e){const t=e.match(ze);if(!t)return!1;const r=t.pop();return e.endsWith(r)}isValidTagPair(e,t){const r=e.src,s=t.src,n=r.match(Fe)[1];return n===s.match(Fe)[1]&&Be.includes(n)}getSentencesFromTokens(e,t=!0){let r,s,n=[],l="";do{s=!1;const t=e[0],r=e[e.length-1];t&&r&&"html-start"===t.type&&"html-end"===r.type&&this.isValidTagPair(t,r)&&(e=e.slice(1,e.length-1),s=!0)}while(s&&e.length>1);return e.forEach(((t,s)=>{let a,i,b;const u=e[s+1],g=e[s-1],h=e[s+2];switch(i=this.getNextTwoCharacters([u,h]),a=i.length>=2,r=a?i[1]:"",t.type){case"html-start":case"html-end":this.isBreakTag(t.src)?(n.push(l),l=""):l+=t.src;break;case"smaller-than-sign-content":b=this.tokenizeSmallerThanContent(t,n,l),n=b.tokenSentences,l=b.currentSentence;break;case"sentence":case"block-start":l+=t.src;break;case"sentence-delimiter":if(l+=t.src,!(0,Y.isUndefined)(u)&&"block-end"!==u.type&&"sentence-delimiter"!==u.type&&this.isCharacterASpace(u.src[0])){if(this.isQuotation(t.src)&&g&&"."!==g.src)break;this.isQuotation(t.src)||"…"===t.src?l=this.getValidSentence(a,r,i,u,n,l):(n.push(l),l="")}break;case"full-stop":if(l+=t.src,i=this.getNextTwoCharacters([u,h]),a=i.length>=2,r=a?i[1]:"",this.endsWithAbbreviation(l))break;if(a&&this.isNumber(i[0]))break;if(this.isPartOfPersonInitial(t,g,u,h))break;if(this.endsWithOrdinalDot(l))break;l=this.getValidSentence(a,r,i,u,n,l);break;case"block-end":if(l+=t.src,i=this.getNextTwoCharacters([u,h]),a=i.length>=2,r=a?i[0]:"",a&&this.isNumber(i[0])||this.isSentenceEnding(g)&&!this.isValidSentenceBeginning(r)&&!this.isSentenceStart(u))break;this.isSentenceEnding(g)&&(this.isSentenceStart(u)||this.isValidSentenceBeginning(r))&&(n.push(l),l="")}})),""!==l&&n.push(l),t&&(n=(0,Y.map)(n,(function(e){return e.trim()}))),n}getValidSentence(e,t,r,s,n,l){return(e&&this.isValidSentenceBeginning(t)&&this.isCharacterASpace(r[0])||this.isSentenceStart(s))&&(n.push(l),l=""),l}isCharacterASpace(e){return/\s/.test(e)}}const xe="(^|["+[" ","\\n","\\r","\\t"," ","۔","؟","،","؛"," ",".",",","'","(",")",'"',"+","-",";","!","?",":","/","»","«","‹","›","<",">","”","“","〝","〞","〟","‟","„"].map((e=>"\\"+e)).join("")+"])",Ae=new RegExp(xe+"\\d{1,3}\\.$");class De extends je{constructor(){super()}endsWithOrdinalDot(e){return Ae.test(e.trim())}}const Se=(0,Y.memoize)((function(e,t=!0){const r=new De,{tokenizer:s,tokens:n}=r.createTokenizer();return r.tokenize(s,e),0===n.length?[]:r.getSentencesFromTokens(n,t)}),((...e)=>JSON.stringify(e))),$e=/^((ge)\S+t($|[ \n\r\t.,'()"+\-;!?:/»«‹›<>]))/gi,Ce=/^(((be|ent|er|her|ver|zer|über|ueber)\S+([^s]t|sst))($|[ \n\r\t.,'()"+\-;!?:/»«‹›<>]))/gi,Re=/(ab|an|auf|aus|vor|wieder|zurück)(ge)\S+t($|[ \n\r\t.,'()"+\-;!?:/»«‹›<>])/gi,qe=/((ab|an|auf|aus|vor|wieder|zurück)(be|ent|er|her|ver|zer|über|ueber)\S+([^s]t|sst))($|[ \n\r\t.,'()"+\-;!?:/»«‹›<>])/gi,We=/\S+iert($|[ \n\r\t.,'()"+\-;!?:/»«‹›<>])/gi,Te=function(e){return e.match($e)||[]},Pe=function(e){return e.match(Ce)||[]},Me=function(e){return e.match(Re)||[]},Oe=function(e){return e.match(qe)||[]},Ue=function(e){return e.match(We)||[]};function Ie(){return{verbsBeginningWithGe:Te,verbsBeginningWithErVerEntBeZerHerUber:Pe,verbsWithGeInMiddle:Me,verbsWithErVerEntBeZerHerUberInMiddle:Oe,verbsEndingWithIert:Ue}}const Ne=["angefangen","aufgerissen","ausgesehen","befohlen","befunden","begonnen","bekommen","bewiesen","beworben","empfohlen","empfunden","entschieden","erschrocken","erwogen","gebacken","gebeten","gebissen","geblasen","geblieben","gebogen","geboren","geborgen","geboten","gebraten","gebrochen","gebunden","gediehen","gedroschen","gedrungen","gefahren","gefallen","gefangen","geflogen","geflohen","geflossen","gefressen","gefroren","gefunden","gegangen","gegeben","gegessen","geglichen","geglitten","gelungen","gegolten","gegoren","gegossen","gegraben","gegriffen","gehalten","gehangen","gehauen","geheissen","geheißen","gehoben","geholfen","geklungen","gekniffen","gekommen","gekrochen","geladen","gelassen","gelaufen","gelegen","gelesen","geliehen","gelitten","gelogen","gelungen","gemessen","gemieden","genesen","genommen","genossen","gepfiffen","gepriesen","gequollen","geraten","gerieben","gerissen","geritten","gerochen","geronnen","gerufen","gerungen","geschaffen","geschehen","geschieden","geschienen","geschlafen","geschlagen","geschlichen","geschliffen","geschlossen","geschlungen","geschmissen","geschmolzen","geschnitten","geschoben","gescholten","geschoren","geschossen","geschrieben","geschrien","geschritten","geschunden","geschwiegen","geschwollen","geschwommen","geschworen","geschwunden","geschwungen","gesehen","gesessen","gesoffen","gesonnen","gespien","gesponnen","gesprochen","gesprossen","gesprungen","gestanden","gestiegen","gestochen","gestohlen","gestorben","gestoßen","gestossen","gestrichen","gestritten","gesungen","gesunken","getan","getragen","getreten","getrieben","getroffen","getrogen","getrunken","gewachsen","gewaschen","gewichen","gewiesen","gewoben","gewogen","gewonnen","geworben","geworfen","gewrungen","gezogen","gezwungen","misslungen","überbacken","ueberbacken","überbehalten","ueberbehalten","überbekommen","ueberbekommen","überbelegen","ueberbelegen","überbezahlen","ueberbezahlen","überboten","ueberboten","übergebunden","uebergebunden","überbunden","ueberbunden","überblasen","ueberblasen","überbraten","ueberbraten","übergebraten","uebergebraten","überbremst","ueberbremst","übergeblieben","uebergeblieben","übereinandergelegen","uebereinandergelegen","übereinandergeschlagen","uebereinandergeschlagen","übereinandergesessen","uebereinandergesessen","übereinandergestanden","uebereinandergestanden","übereingefallen","uebereingefallen","übereingekommen","uebereingekommen","übereingetroffen","uebereingetroffen","übergefallen","uebergefallen","übergessen","uebergessen","überfahren","ueberfahren","übergefahren","uebergefahren","überfallen","ueberfallen","überfangen","ueberfangen","überflogen","ueberflogen","überflossen","ueberflossen","übergeflossen","uebergeflossen","überfressen","ueberfressen","überfroren","ueberfroren","übergegeben","uebergegeben","übergeben","uebergeben","übergegangen","uebergegangen","übergangen","uebergangen","übergangen","uebergangen","übergossen","uebergossen","übergriffen","uebergriffen","übergegriffen","uebergegriffen","übergehalten","uebergehalten","überhandgenommen","ueberhandgenommen","überhangen","ueberhangen","übergehangen","uebergehangen","übergehoben","uebergehoben","überhoben","ueberhoben","überkommen","ueberkommen","übergekommen","uebergekommen","überladen","ueberladen","übergeladen","uebergeladen","überlassen","ueberlassen","übergelassen","uebergelassen","überlaufen","ueberlaufen","übergelaufen","uebergelaufen","überlesen","ueberlesen","übergelegen","uebergelegen","übergenommen","uebergenommen","übernommen","uebernommen","übergequollen","uebergequollen","überrissen","ueberrissen","überritten","ueberritten","übergeschossen","uebergeschossen","überschlafen","ueberschlafen","überschlagen","ueberschlagen","übergeschlagen","uebergeschlagen","übergeschlossen","uebergeschlossen","überschnitten","ueberschnitten","überschrieben","ueberschrieben","überschrieen","ueberschrieen","überschrien","ueberschrien","überschritten","ueberschritten","überschwungen","ueberschwungen","übergesehen","uebergesehen","übersehen","uebersehen","übergesotten","uebergesotten","übergesotten","uebergesotten","übersponnen","uebersponnen","übersprochen","uebersprochen","übersprungen","uebersprungen","übergesprungen","uebergesprungen","überstochen","ueberstochen","übergestochen","uebergestochen","überstanden","ueberstanden","übergestanden","uebergestanden","überstiegen","ueberstiegen","übergestiegen","uebergestiegen","übergestrichen","uebergestrichen","überstrichen","ueberstrichen","übertragen","uebertragen","übertroffen","uebertroffen","übertrieben","uebertrieben","übertreten","uebertreten","übergetreten","uebergetreten","überwachsen","ueberwachsen","überwiesen","ueberwiesen","überworfen","ueberworfen","übergeworfen","uebergeworfen","überwogen","ueberwogen","überwunden","ueberwunden","überzogen","ueberzogen","übergezogen","uebergezogen","verdorben","vergessen","verglichen","verloren","verstanden","verschwunden","vorgeschlagen"],{getWords:Ve}=e.languageProcessing,Le=Ie(),_e=Le.verbsBeginningWithErVerEntBeZerHerUber,He=Le.verbsBeginningWithGe,Ge=Le.verbsWithGeInMiddle,Ze=Le.verbsWithErVerEntBeZerHerUberInMiddle,Qe=Le.verbsEndingWithIert,Je=["geht","gämsbart","gemsbart","geäst","gebarungsbericht","geähnelt","geartet","gebäudetrakt","gebet","gebiet","gebietsrepräsentant","gebildbrot","gebirgsart","gebirgsgrat","gebirgskurort","gebirgsluft","gebirgsschlucht","geblüt","geblütsrecht","gebohntkraut","gebot","gebrauchsgut","gebrauchstext","gebrauchsverlust","gebrauchtgerät","gebrauchtwagengeschäft","gebrauchtwagenmarkt","geburt","geburtsakt","geburtsgeschwulst","geburtsgewicht","geburtsort","geburtsrecht","geburtsstadt","geburtstagsfest","geckenart","gedächtniskonzert","gedächtniskunst","gedächtnisverlust","gedankenarmut","gedankenexperiment","gedankenflucht","gedankengut","gedankenschritt","gedankenwelt","gedenkkonzert","gedicht","geest","gefahrengebiet","gefahrenmoment","gefahrenpunkt","gefahrgut","gefahrguttransport","gefährt","gefälligkeitsakzept","gefallsucht","gefangenenanstalt","gefangenentransport","gefängnisarzt","gefängniskluft","gefäßnaht","gefecht","gefechtsabschnitt","gefechtsbereit","gefechtsgebiet","gefechtsgewicht","gefechtshut","gefechtsmast","gefechtsmast","geflecht","geflügelaufzucht","geflügelleberwurst","geflügelmarkt","geflügelmast","geflügelpest","geflügelsalat","geflügelwurst","geflügelzucht","gefolgsleute","gefrett","gefriergerät","gefriergut","gefrierobst","gefrierpunkt","gefrierschnitt","gefühlsarmut","gefühlswelt","gegenangebot","gegenansicht","gegenargument","gegengeschäft","gegengewalt","gegengewicht","gegenkandidat","gegenkompliment","gegenkonzept","gegenlicht","gegenmacht","gegenpapst","gegenpart","gegensatzwort","gegenstandpunkt","gegenstandsgebiet","gegenwart","gegenwartskunst","gegenwelt","gegenwort","gehaart","gehandicapt","gehandikapt","geheimagent","geheimbericht","geheimdokument","geheimfavorit","geheimkontakt","geheimkult","geheimnisverrat","geheimpolizist","geheimrat","geheimrezept","geheimtext","gehirnakrobat","gehirngeschwulst","gehirnhaut","gehirnsandgeschwulst","gehirntot","gehirntrust","gehöft","gehörlosensport","geigenkonzert","geißbart","geißblatt","geißhirte","geißhirt","geist","geisterfahrt","geisterstadt","geisterwelt","geistesarmut","geistesart","geistesfürst","geistesgegenwart","geistesgestört","geistesprodukt","geistestat","geistesverwandt","geisteswelt","geklüft","geländefahrt","geländeritt","geländesport","gelangweilt","gelaut","geläut","gelblicht","gelbrost","gelbsucht","gelbwurst","gelcoat","geldausgabeautomat","geldautomat","geldgeschäft","geldheirat","geldinstitut","geldmarkt","geldsurrogat","geldtransport","geldverlust","gelehrtenstreit","gelehrtenwelt","geleit","geleitboot","geleitwort","gelenkgicht","gelenkwassersucht","geleucht","geltungssucht","gelüst","gemächt","gemeindeamt","gemeindebürgerrecht","gemeindegut","gemeindekirchenrat","gemeindepräsident","gemeinderat","gemeingeist","gemeingut","gemeinschaftsgeist","gemeinschaftsprojekt","gemeinschaftsunterkunft","gemengesaat","gemüseart","gemüsebeet","gemüsegeschäft","gemüsemarkt","gemüsesaft","gemüsesalat","gemüsezucht","gemüt","gemütsarmut","gemütsart","gemütsathlet","gemütskalt","genausogut","genausooft","genausoweit","gendefekt","generalagent","generalarzt","generalat","generalbassinstrument","generalbaßinstrument","generalbundesanwalt","generalgouvernement","generalintendant","generalist","generalkonsulat","generalleutnant","generaloberst","generalresident","generalsekretariat","generalstaaten","generalstaatsanwalt","generalsuperintendent","generalüberholt","generalvikariat","generalvollmacht","generationenkonflikt","generativist","genist","genitivattribut","genitivobjekt","genmanipuliert","gennesaret","genotzüchtigt","gent","genuasamt","genussgift","genußgift","genusssucht","genuss-sucht","genußsucht","genverändert","geobiont","geodät","geografieunterricht","geographieunterricht","geokrat","geophyt","gepäckfracht","geradeausfahrt","geradesogut","gefälligst","gerant","gerät","gerätewart","geräuschlaut","gerbextrakt","gericht","gerichtsarzt","gerichtsort","gerichtspräsident","germanisiert","germanist","germanistikstudent","gerodelt","geröllschicht","geröllschutt","geront","gerontokrat","gerstenbrot","gerstensaft","gerstenschrot","gerücht","gerüst","gesamtansicht","gesamtaspekt","gesamtdurchschnitt","gesamtgewicht","gesamtgut","gesamt","gesamtklassement","gesamtunterricht","gesandtschaftsrat","gesangskunst","gesangspart","gesangssolist","gesangsunterricht","gesangunterricht","geschäft","geschäftsaufsicht","geschäftsbericht","geschäftsgeist","geschäftswelt","geschenkpaket","geschichtsunterricht","geschicklichkeitstest","geschicklichkeitstest","geschlecht","geschlechtsakt","geschlechtslust","geschlechtsprodukt","geschlechtswort","geschmackstest","geschwindigkeitslimit","geschworenengericht","geschwulst","gesellschaftsfahrt","gesellschaftsschicht","gesetzblatt","gesetzespaket","gesetzestext","gesicht","gesichtshaut","gesichtspunkt","gesichtsschnitt","gesichtsverlust","gespenst","gespensterfurcht","gespinst","gespött","gesprächstherapeut","gestalt","gestaltungselement","gesteinsart","gesteinschutt","gesteinsschicht","gestüt","gestüthengst","verantwortungsbewusst","verantwortungsbewußt","getast","getränkeabholmarkt","getränkeautomat","getränkemarkt","getreideart","getreideaussaat","getreideexport","getreideimport","getreideprodukt","getreideschnitt","getreidevorrat","gewährfrist","gewalt","gewaltakt","gewaltbereit","gewalttat","gesprächsbereit","gewaltverbot","gewaltverzicht","gewässerbett","gewässerwart","gewebeschicht","gewebsrest","gewicht","gewichtsprozent","gewichtsverlust","gewerbeamt","gewerbearzt","gewerbeaufsicht","gewerbeaufsichtsamt","gewerbegebiet","gewerberecht","gewerbsunzucht","gewerkschaft","gewerkschaftsjournalist","gewindestift","gewinnsucht","gewinst","gewissensangst","gewissenskonflikt","gewitterfront","gewitterluft","gewohnheitsrecht","gewürzextrakt","gewürzkraut","gezücht","erbbaurecht","erbfolgerecht","erbfolgestreit","erbgut","erbhofrecht","erblast","erbpacht","erbrecht","erbschaftsstreit","erbsenkraut","erbbedingt","erbberechtigt","erblasst","erblaßt","erbswurst","erbverzicht","erbwort","erbzinsgut","erdbebengebiet","erdbeerjogurt","erdbeerjoghurt","erdbeeryoghurt","erdbeerkompott","erdbeerrot","erdbeersaft","erdbeersekt","erdengut","erdenlust","erdfrucht","erdgeist","erdkundeunterricht","erdlicht","erdmittelpunkt","erdnussfett","erdölprodukt","erdölproduzent","erdsatellit","erdschicht","erdsicht","erdtrabant","erdverhaftet","eremit","erfahrungsbericht","erfahrungshorizont","erfahrungswelt","erfindergeist","erfolgsaussicht","erfolgsorientiert","erfolgsrezept","erfolgsverwöhnt","erfüllungsort","erfurt","ergänzungsheft","ergänzungssport","ergänzungstest","ergostat","ergotherapeut","erholungsgebiet","erholungsort","erkundungsfahrt","erlaucht","erläuterungstext","erlebnisbericht","erlebnisorientiert","erlebniswelt","ernährungsamt","ernst","ernstgemeint","ernteaussicht","erntedankfest","erntefest","erntemonat","ernteresultat","eroberungsabsicht","eroberungsgeist","eroberungslust","eroberungssucht","eröffnungskonzert","ersatzgeschwächt","ersatzgut","ersatzkandidat","ersatzobjekt","ersatzpräparat","ersatzreservist","ersatztorwart","erscheinungsfest","erscheinungsort","erscheinungswelt","erschließungsgebiet","erst","erstbundesligist","erstfahrt","erstgebot","erstgeburt","erstgeburtsrecht","erstklassbillett","erstklaßbillett","erstkommunikant","erstkonsument","erstligist","erstplatziert","erstplaciert","erstplaziert","erstrecht","ertragsaussicht","erwartungsangst","erwartungshorizont","erwerbseinkünfte","erythrit","erythroblast","erythrozyt","erzählertalent","erzählgut","erzählkunst","erzähltalent","erzamt","erzdemokrat","erzeugungsschlacht","erzfaschist","erziehungsanstalt","erziehungsberechtigt","erziehungsinstitut","erzkommunist","erzprotestant","veranlassungswort","veranschaulicht","veranschlagt","verantwortungsbewusst","verantwortungsbewußt","veräußerungsverbot","verbalist","verbalkontrakt","verbändestaat","verbannungsort","verbildlicht","verbindungspunkt","verbindungsstudent","verbraucherkredit","verbrauchermarkt","verbrauchsgut","verbrechernest","verbrechersyndikat","verbrecherwelt","verbreitungsgebiet","verbrennungsprodukt","verdachtsmoment","verdampfungsgerät","verdauungstrakt","verdikt","veredelungsprodukt","verehrerpost","vereinspräsident","vereinsrecht","vereinssport","verfahrensrecht","verfassungsfahrt","verfassungsgericht","verfassungsrecht","verfassungsstaat","verfolgungsrecht","verfremdungseffekt","verfügungsgewalt","verfügungsrecht","verfügungsberechtigt","verführungskunst","vergegenständlicht","vergegenwärtigt","vergeltungsakt","vergenossenschaftlicht","vergissmeinnicht","vergißmeinnicht","vergleichsmonat","vergleichsobjekt","vergleichspunkt","vergnügungsetablissement","vergnügungsfahrt","vergnügungssucht","vergrößerungsgerät","verhaltensgestört","verhältniswahlrecht","verhältniswort","verhandlungsangebot","verhandlungsbereit","versandbereit","verteidigungsbereit","verhandlungsmandat","verhandlungsort","verhandlungspunkt","verhöramt","verist","verjährungsfrist","verkaufsagent","verkaufsangebot","verkaufsargument","verkaufsautomat","verkaufsfront","verkaufshit","verkaufsobjekt","verkaufsorientiert","verkaufspunkt","verkehrsamt","verkehrsdelikt","verkehrsinfarkt","verkehrsknotenpunkt","verkehrslicht","verkehrsnachricht","verkehrspolizist","verkehrsrecht","verkehrsunterricht","verkehrsverbot","verklarungsbericht","verknüpfungspunkt","verkündungsblatt","verlagsanstalt","verlagsprospekt","verlagsrecht","verlagsrepräsentant","verlagssignet","verlust","verlustgeschäft","verlust","verlustgeschäft","verlustpunkt","vermessungsamt","vermittlungsamt","vermögensrecht","vermont","vermummungsverbot","verneinungswort","vernichtungswut","vernunft","vernunftheirat","verordnungsblatt","verpackungsflut","verpflichtungsgeschäft","verrat","versammlungsort","versammlungsrecht","versandgeschäft","versandgut","versart","verschlusslaut","verschnitt","verschwendungssucht","versehrtensport","versicherungsagent","versicherungsanstalt","versicherungsrecht","verskunst","versöhnungsfest","versorgungsamt","versorgungsberechtigt","versorgungsgebiet","versorgungsgut","versorgungsstaat","verstakt","verständigungsbereit","verstellungskunst","verstürznaht","versuchsanstalt","versuchsobjekt","versuchsprojekt","vertebrat","verteidigungsbudget","verteidigungsetat","verteidigungspakt","verteilungskonflikt","verteilungszahlwort","vertikalschnitt","vertikutiergerät","vertragsgerecht","vertragspunkt","vertragsrecht","vertragsstaat","vertragstext","vertragswerkstatt","vertrauensanwalt","vertrauensarzt","vertrauensverlust","vertriebsrecht","vervielfältigungsrecht","vervielfältigungszahlwort","verwaltungsakt","verwaltungsgericht","verwaltungsrat","verwaltungsrecht","verwundetentransport","verzicht","verzweiflungsakt","verzweiflungstat","entbindungsanstalt","entdeckungsfahrt","entenbrust","entenfett","entertainment","enthusiast","entlastungsmoment","entlüftungsschacht","entnazifizierungsgericht","entoblast","entoparasit","entrechat","entrefilet","entrepot","entscheidungsfurcht","entscheidungsgewalt","entscheidungsrecht","entscheidungsschlacht","entstehungsort","entsteht","entwässerungsschacht","entwicklungsabschnitt","entwicklungsinstitut","entwicklungsprojekt","entwicklungsschritt","entziehungsanstalt","zerat","zerebrallaut","zerfallsprodukt","zergliederungskunst","zerit","zermatt","zersetzungsprodukt","zerstörungslust","zerstörungswut","zertifikat","zerussit","zervelat","zervelatwurst","beamtenrecht","beamtenschicht","beamtenstaat","beat","beatmungsgerät","beaufort","becherfrucht","beckengurt","becquereleffekt","bedarfsgut","bedenkfrist","bedienungselement","bedienungsgerät","bedienungskomfort","bedingtgut","bedürfnisanstalt","beeinflusst","beeinflußt","beerdigungsanstalt","beerdigungsinstitut","beerenfrucht","beerenobst","beerensaft","beet","befasst","befaßt","befehlsgewalt","beförderungsentgelt","beförderungsrecht","begabungstest","begegnungsort","begleitinstrument","begleittext","begleitwort","begnadigungsrecht","begräbt","begrenzungslicht","begriffswelt","begriffswort","begrüßungswort","behaviorist","behebungsfrist","behelfsausfahrt","behelfsunterkunft","behindertengerecht","behindertensport","behindertentransport","behmlot","beiblatt","beiboot","beignet","beiheft","beikost","beilast","beileidswort","beinamputiert","beinhaut","beirat","beirut","beistandskredit","beistandspakt","beitritt","beitrittsabsicht","beitrittsgebiet","beiwacht","beiwort","beizgerät","bekehrungswut","bekennergeist","bekennermut","bekleidungsamt","bekommen","belegarzt","belegbett","belegfrist","belehrungssucht","belemnit","belesprit","beleuchtungseffekt","beleuchtungsgerät","belfast","belkantist","belcantist","belletrist","bellizist","belt","benedikt","benediktenkraut","benefiziant","benefiziat","benefizkonzert","beneluxstaat","bentonit","benzindunst","beratungspunkt","bereit","bereicherungsabsicht","bereitschaftsarzt","bergamt","bergeslast","bergfahrt","bergfest","berggeist","berggrat","bergluft","bergpredigt","bergsport","berg-und-Tal-Fahrt","bergwacht","bergwelt","bericht","berichtsmonat","beritt","bermudashort","bernbiet","berserkerwut","berufsaussicht","berufssoldat","berufssport","berufsstart","berufstracht","berufsverbot","berufungsfrist","berufungsgericht","berufungsrecht","berührungsangst","berührungspunkt","besanmast","besatzungsgebiet","besatzungsmacht","besatzungsrecht","besatzungssoldat","besatzungsstatut","beschaffungsamt","beschäftigungstherapeut","beschlächt","beschlussrecht","beschlußrecht","beschmet","beschneidungsfest","beschlächt","beschlussrecht","beschlußrecht","beschmet","beschneidungsfest","beschwerdefrist","beschwerderecht","beschwörungskunst","beseitigungsanstalt","besetzungsgebiet","besetzungsmacht","besetzungsstatut","besichtigungsfahrt","besitzrecht","besoldungsrecht","besprechungspunkt","besserungsanstalt","bestattungsinstitut","bestimmungsort","bestimmungswort","bestinformiert","bestqualifiziert","bestrahlungsgerät","bestrenommiert","bestsituiert","bestverkauft","besucherrat","besuchsrecht","betpult","betracht","betreibungsamt","betriebsarzt","betriebsfest","betriebsrat","betriebswirt","bett","bettelmusikant","bettelvogt","bettstatt","bettwurst","beulenpest","beutegut","beutekunst","beuterecht","bevölkerungsschicht","bewahranstalt","bewährungsfrist","bewegungsarmut","beweislast","bewußt","bewusst","beziehungsgeflecht","bezirksamt","bezirksarzt","bezirksgericht","bezirkskabinett","bezirksschulrat","bezirksstadt","bezugspunkt","bezugsrecht","heraklit","herat","herbalist","herbst","herbstmonat","herbstpunkt","herdbuchzucht","herdeninstinkt","herfahrt","heringsfilet","heringssalat","herkuleskraut","herkunft","herkunftsort","hermaphrodit","heroenkult","heroinsucht","heroldsamt","heroldskunst","herostrat","herrenabfahrt","herrenbrot","herrendienst","herrenfest","herrenhut","herrenrecht","herrenschnitt","herrenwelt","herrgott","herrnhut","herrschaftsgebiet","herrschaftsgewalt","herrschaftsinstrument","herrschergeschlecht","herrscherkult","herrschsucht","herstellungsart","herzacht","herzangst","herzblatt","herzblut","herzensangst","herzensgut","herzenslust","herzenstrost","herzgeliebt","herzinfarkt","herzinnenhaut","herzklappendefekt","herzogshut","herzlichst","herzpatient","herzpunkt","herzspezialist","überbackt","ueberbackt","überbacktet","ueberbacktet","überbietet","ueberbietet","überbot","ueberbot","überbotet","ueberbotet","überbindet","ueberbindet","überbandet","ueberbandet","überbläst","ueberblaest","überbliest","ueberbliest","überbrät","ueberbraet","überbratet","ueberbratet","überbriet","ueberbriet","überbrietet","ueberbrietet","überbringt","ueberbringt","überbrachtet","ueberbrachtet","überbrücktet","ueberbruecktet","überbrühtet","ueberbrühtet","überbrülltet","ueberbruelltet","überbuchtet","ueberbuchtet","überbürdetet","ueberbuerdetet","überdecktet","ueberdecktet","überdehntet","ueberdehntet","überdenkt","ueberdenkt","überdachtet","ueberdachtet","überdosiertet","ueberdosiertet","überdrehtet","ueberdrehtet","überdrucktet","ueberdrucktet","überdüngtet","ueberdüngtet","übereignetet","uebereignetet","übereiltet","uebereiltet","übererfülltet","uebererfuelltet","überißt","ueberisst","ueberißt","überisst","überesst","ueberesst","übereßt","uebereßt","überaßt","ueberaßt","überesset","ueberesset","überäßet","ueberaesset","überfährt","ueberfaehrt","überfahrt","ueberfahrt","überfuhrt","ueberfuhrt","überfällt","ueberfaellt","überfallet","ueberfallet","überfielt","ueberfielt","überfielet","ueberfielet","überfängt","ueberfaengt","überfingt","ueberfingt","überfinget","ueberfinget","überfärbet","ueberfaerbet","überfettetet","ueberfettetet","überfirnisset","ueberfirnisset","überfirnißtet","ueberfirnisstet","überfischet","ueberfischet","überfischtet","ueberfischtet","überflanktet","ueberflanktet","überflanktet","ueberflanktet","überfliegt","ueberfliegt","überflieget","ueberflieget","überflöget","ueberflöget","überflösset","ueberfloesset","überflosst","ueberflosst","überfloßt","ueberflosst","überfließt","ueberfliesst","überflutetet","ueberflutetet","überformet","ueberformet","überformtet","ueberformtet","überfrachtetet","ueberfrachtetet","überfracht","ueberfracht","überfraget","ueberfraget","überfragtet","ueberfragtet","überfremdetet","ueberfremdetet","überfrisst","ueberfrisst","überfrißt","ueberfrißt","überfresst","ueberfresst","überfreßt","ueberfreßt","überfresset","ueberfresset","überfraßt","ueberfraßt","ueberfrasst","überfräßet","ueberfraesset","überfriert","ueberfriert","überfrieret","ueberfrieret","überfrort","ueberfrort","überfröret","ueberfroeret","überfrört","ueberfroert","überführet","ueberfuehret","überführtet","ueberfuehrtet","überfüllet","ueberfuellet","übergibt","uebergibt","übergebt","uebergebt","übergebet","uebergebet","übergabt","uebergabt","übergäbet","uebergaebet","übergäbt","uebergaebt","übergeht","uebergeht","übergehet","uebergehet","übergingt","uebergingt","übergewichtetet","uebergewichtetet","übergießet","uebergiesset","übergießt","uebergiesst","übergösset","uebergoesset","übergosst","uebergosst","uebergoßt","übergipset","uebergipset","übergipstet","uebergipstet","übergipset","uebergipset","übergipstet","uebergipstet","überglänzet","ueberglaenzet","überglänztet","ueberglaenztet","überglaset","ueberglaset","überglastet","ueberglastet","überglühet","uebergluehet","überglühtet","uebergluehtet","übergoldetet","uebergoldetet","übergraset","uebergraset","übergrastet","uebergrastet","übergrätschet","uebergraetschet","übergrätschtet","uebergraetschtet","übergreift","uebergreift","übergreifet","uebergreifet","übergrifft","uebergrifft","übergriffet","uebergriffet","übergreift","uebergreift","übergreifet","uebergreifet","übergriffet","uebergriffet","übergrifft","uebergrifft","übergrünet","uebergruenet","übergrüntet","uebergruentet","überhat","ueberhat","überhabt","ueberhabt","überhabet","ueberhabet","überhattet","ueberhattet","überhättet","ueberhaettet","überhält","ueberhaelt","überhaltet","ueberhaltet","überhielt","ueberhielt","überhieltet","ueberhieltet","überhändiget","ueberhaendiget","überhändigtet","ueberhaendigtet","überhängt","ueberhaengt","überhänget","ueberhaenget","überhingt","ueberhingt","überhinget","ueberhinget","überhängt","ueberhaengt","überhänget","ueberhaenget","überhängtet","ueberhaengtet","überhänget","ueberhaenget","überhängtet","ueberhaengtet","überhängt","ueberhaengt","überhänget","ueberhaenget","überhingt","ueberhingt","überhinget","ueberhinget","überhastetet","ueberhastetet","überhäufet","ueberhaeufet","überhäuftet","ueberhaeuftet","überhebt","ueberhebt","überhebet","ueberhebet","überhobt","ueberhobt","überhöbet","ueberhoebet","überhebt","ueberhebt","überhebet","ueberhebet","überhobt","ueberhobt","überheiztet","ueberheiztet","überheizet","ueberheizet","überhöhet","ueberhoehet","überhöhtet","ueberhoehtet","überhitzet","ueberhitzet","überhitztet","ueberhitztet","überholet","ueberholet","überholtet","ueberholtet","überhöret","ueberhoeret","überhörtet","ueberhoertet","überinterpretieret","ueberinterpretieret","überinterpretiertet","ueberinterpretiertet","überinterpretieret","ueberinterpretieret","überinterpretiertet","ueberinterpretiertet","überklebet","ueberklebet","überklebtet","ueberklebtet","überkleidetet","ueberkleidetet","überkochet","ueberkochet","überkochtet","ueberkochtet","überkommet","ueberkommet","überkamt","ueberkamt","überkämet","ueberkaemet","überkämt","ueberkaemt","überkompensieret","ueberkompensieret","überkompensiertet","ueberkompensiertet","überkreuzet","ueberkreuzet","überkreuztet","ueberkreuztet","überkronet","ueberkronet","überkrontet","ueberkrontet","überkrustetet","ueberkrustetet","überladet","ueberladet","überludet","ueberludet","überlüdet","ueberluedet","überlappet","ueberlappet","überlapptet","ueberlapptet","überlasset","ueberlasset","überlaßt","ueberlaßt","ueberlasst","ueberlasst","überlässt","ueberlaesst","überließt","ueberließt","ueberliesst","überließet","ueberließet","ueberliesset","überlastet","ueberlastet","überlastetet","ueberlastetet","überläuft","ueberlaeuft","überlaufet","ueberlaufet","überlieft","ueberlieft","überliefet","ueberliefet","überlebet","ueberlebet","überlebtet","ueberlebtet","überleget","ueberleget","überlegtet","ueberlegtet","überlegt","ueberlegt","überleget","ueberleget","überlegtet","ueberlegtet","überleitet","ueberleitet","überleitetet","ueberleitetet","überleset","ueberleset","überlast","ueberlast","überläset","ueberlaeset","überliegt","ueberliegt","überlieget","ueberlieget","überlagt","ueberlagt","überläget","ueberlaeget","überlägt","ueberlaegt","überlistetet","ueberlistetet","übermachet","uebermachet","übermachtet","uebermachtet","übermalet","uebermalet","übermaltet","uebermaltet","übermalet","uebermalet","übermaltet","uebermaltet","übermannet","uebermannet","übermanntet","uebermanntet","übermarchtet","uebermarchtet","übermarchet","uebermarchet","übermästetet","uebermaestetet","übermüdetet","uebermuedetet","übernächtiget","uebernaechtiget","übernächtigtet","uebernaechtigtet","übernimmt","uebernimmt","übernehmt","uebernehmt","übernehmet","uebernehmet","übernahmt","uebernahmt","übernähmet","uebernaehmet","übernähmt","uebernaehmt","übernutzet","uebernutzet","übernutztet","uebernutztet","überpflanzt","ueberpflanzt","überpflanzet","ueberpflanzet","überpflanztet","ueberpflanztet","überplanet","ueberplanet","überplantet","ueberplantet","überprüfet","ueberpruefet","überprüftet","ueberprueftet","überquillt","ueberquillt","überquellt","ueberquellt","überquellet","ueberquellet","überquollt","ueberquollt","überquöllet","ueberquoellet","ueberquöllt","ueberquoellt","überqueret","ueberqueret","überquertet","ueberquertet","überraget","ueberraget","überragtet","ueberragtet","überragt","ueberragt","überraget","ueberraget","überragtet","ueberragtet","überraschet","ueberraschet","überraschtet","ueberraschtet","überreagieret","ueberreagieret","überreagiertet","ueberreagiertet","überrechnetet","ueberrechnetet","überredetet","ueberredetet","überreglementieret","ueberreglementieret","überreglementiertet","ueberreglementiertet","überregulieret","ueberregulieret","überreguliertet","ueberreguliertet","überreichet","ueberreichet","überreichtet","ueberreichtet","überreißet","ueberreisset","überrisset","ueberrisset","überreitet","ueberreitet","überrittet","ueberrittet","überreizet","ueberreizet","überreiztet","ueberreiztet","überrennet","ueberrennet","überrenntet","ueberrenntet","überrollet","ueberrollet","überrolltet","ueberrolltet","überrundetet","ueberrundetet","übersäet","uebersaeet","übersätet","uebersaetet","übersättiget","uebersaettiget","uebersaettigtet","übersättigtet","überschattetet","ueberschattetet","überschätzet","ueberschaetzet","überschätztet","ueberschaetztet","überschauet","ueberschauet","überschautet","ueberschautet","überschäumt","ueberschaeumt","überschäumet","ueberschaeumet","überschäumtet","ueberschaeumtet","überschießt","ueberschießt","ueberschiesst","überschießet","ueberschiesset","ueberschießet","überschosst","ueberschosst","überschosst","ueberschosst","überschoßt","ueberschoßt","überschösset","ueberschoesset","überschlafet","ueberschlafet","überschliefet","ueberschliefet","überschlieft","ueberschlieft","überschlaget","ueberschlaget","überschlüget","ueberschlueget","überschlügt","ueberschluegt","überschlägt","ueberschlaegt","überschlagt","ueberschlagt","überschlaget","ueberschlaget","überschlugt","ueberschlugt","überschlüget","ueberschlueget","überschlügt","ueberschluegt","überschlägt","ueberschlaegt","überschlagt","ueberschlagt","überschlaget","ueberschlaget","überschlugt","ueberschlugt","überschlüget","ueberschlueget","ueberschluegt","überschlügt","überschließt","ueberschließt","ueberschliesst","überschließet","ueberschliesset","überschlosst","ueberschlosst","überschloßt","ueberschlosst","überschlösset","ueberschloesset","überschmieret","ueberschmieret","überschmiertet","ueberschmiertet","überschminket","ueberschminket","überschminktet","ueberschminktet","überschnappt","ueberschnappt","überschnappet","ueberschnappet","überschnapptet","ueberschnapptet","überschneidet","ueberschneidet","überschnittet","ueberschnittet","überschneiet","ueberschneiet","überschneitet","ueberschneitet","überschreibet","ueberschreibet","überschriebet","ueberschriebet","überschriebt","ueberschriebt","überschreiet","ueberschreiet","überschrieet","ueberschrieet","überschriet","ueberschriet","überschriet","ueberschriet","überschreitet","ueberschreitet","überschritt","ueberschritt","überschrittet","ueberschrittet","überschuldetet","ueberschuldetet","überschüttet","ueberschüttet","überschüttetet","ueberschüttetet","überschüttetet","ueberschuettetet","überschwappt","ueberschwappt","überschwappet","ueberschwappet","überschwapptet","ueberschwapptet","überschwemmet","ueberschwemmet","überschwemmtet","ueberschwemmtet","überschwinget","ueberschwinget","überschwangt","ueberschwangt","überschwänget","ueberschwaenget","überschwängt","ueberschwaengt","übersieht","uebersieht","überseht","ueberseht","übersehet","uebersehet","übersaht","uebersaht","übersähet","uebersaehet","übersäht","uebersaeht","übersähet","uebersaehet","übersäht","uebersaeht","übersandtet","uebersandtet","übersendetet","uebersendetet","übersensibilisieret","uebersensibilisieret","übersensibilisiertet","uebersensibilisiertet","übersetzt","uebersetzt","übersetzet","uebersetzet","übersetztet","uebersetztet","übersetzet","uebersetzet","übersetztet","uebersetztet","übersiedet","uebersiedet","übersiedetet","uebersiedetet","übersott","uebersott","übersottet","uebersottet","übersöttet","uebersoettet","übersiedet","uebersiedet","übersiedetet","uebersiedetet","übersott","uebersott","übersottet","uebersottet","übersöttet","uebersoettet","überspannet","ueberspannet","überspanntet","ueberspanntet","überspielet","ueberspielet","überspieltet","ueberspieltet","überspinnet","ueberspinnet","überspännet","ueberspaennet","überspännt","ueberspaennt","überspönnet","ueberspoennet","überspönnt","ueberspoennt","überspitzet","ueberspitzet","überspitztet","ueberspitztet","übersprechet","uebersprechet","überspracht","ueberspracht","übersprächet","ueberspraechet","übersprächt","ueberspraecht","überspringt","ueberspringt","überspringet","ueberspringet","überspränget","ueberspraenget","übersprängt","ueberspraengt","überspringt","ueberspringt","überspringet","ueberspringet","übersprangt","uebersprangt","überspränget","ueberspraenget","übersprängt","ueberspraengt","übersprühet","ueberspruehet","übersprühtet","ueberspruehtet","übersprühet","ueberspruehet","übersprühtet","ueberspruehtet","überspület","ueberspuelet","überspültet","überspueltet","übersticht","uebersticht","überstecht","ueberstecht","überstechet","ueberstechet","überstacht","ueberstacht","überstächet","ueberstaechet","überstächt","ueberstaecht","übersticht","uebersticht","überstecht","ueberstecht","überstechet","ueberstechet","überstacht","ueberstacht","überstächet","ueberstaechet","überstächt","ueberstaecht","überstehet","ueberstehet","überstandet","überstandet","überständet","überstaendet","überstündet","überstuendet","übersteht","uebersteht","überstehet","ueberstehet","überstandet","ueberstandet","überständet","ueberstaendet","überstündet","ueberstuendet","übersteiget","uebersteiget","überstieget","ueberstieget","überstiegt","ueberstiegt","übersteigt","uebersteigt","übersteiget","uebersteiget","überstiegt","ueberstiegt","überstieget","ueberstieget","überstellet","ueberstellet","überstilisieret","ueberstilisieret","überstimmet","ueberstimmet","überstimmtet","ueberstimmtet","überstrahlet","ueberstrahlet","überstrahltet","ueberstrahltet","überstrapazieret","ueberstrapazieret","überstrapaziertet","ueberstrapaziertet","überstreicht","ueberstreicht","überstreichet","ueberstreichet","überstricht","ueberstricht","überstrichet","ueberstrichet","überstreichet","ueberstreichet","überstrichet","ueberstrichet","überstricht","ueberstricht","überstreift","ueberstreift","überstreifet","ueberstreifet","überstreiftet","ueberstreiftet","überstreuet","ueberstreuet","überstreutet","ueberstreutet","überströmet","ueberstroemet","überströmtet","überstroemtet","überstülpt","überstuelpt","ueberstuelpet","überstülpet","überstülptet","ueberstuelptet","überstürzet","ueberstuerzet","überstürztet","ueberstuerztet","übertäubet","uebertaeubet","übertäubtet","uebertaeubtet","übertauchet","uebertauchet","übertauchtet","uebertauchtet","übertippet","uebertippet","übertipptet","uebertipptet","übertönet","uebertoenet","übertöntet","uebertoentet","übertouret","uebertouret","übertourtet","uebertourtet","überträgt","uebertraegt","übertragt","uebertragt","übertraget","uebertraget","übertrugt","uebertrugt","übertrüget","uebertrueget","übertrügt","uebertruegt","übertrainieret","uebertrainieret","übertrainiertet","uebertrainiertet","übertreffet","uebertreffet","übertraft","uebertraft","überträfet","uebertraefet","überträft","uebertraeft","übertreibt","uebertreibt","übertreibet","uebertreibet","übertriebet","uebertriebet","übertriebt","uebertriebt","übertritt","uebertritt","übertretet","uebertretet","übertrat","uebertrat","übertratet","uebertratet","überträtet","uebertraetet","übertritt","uebertritt","übertretet","uebertretet","übertrat","uebertrat","übertratet","uebertratet","überträtet","uebertraetet","übertrumpfet","uebertrumpfet","übertrumpftet","uebertrumpftet","übertünchet","uebertuenchet","übertünchtet","überversorget","ueberversorget","überversorgtet","ueberversorgtet","übervorteilet","uebervorteilet","übervorteiltet","uebervorteiltet","überwachet","ueberwachet","überwachtet","ueberwachtet","überwachset","ueberwachset","überwüchset","ueberwuechset","überwallt","ueberwallt","überwallet","ueberwallet","überwalltet","ueberwalltet","überwallet","ueberwallet","überwalltet","ueberwalltet","überwältiget","ueberwaeltiget","überwältigtet","ueberwaeltigtet","überwalzet","ueberwalzet","überwalztet","ueberwalztet","überwälzet","ueberwaelzet","überwälztet","ueberwaelztet","überwechtetet","ueberwechtetet","überwächtetet","ueberwaechtetet","überwehet","ueberwehet","überwehtet","ueberwehtet","überweidetet","ueberweidetet","überweist","ueberweist","überweiset","ueberweiset","überwiest","ueberwiest","überwieset","ueberwieset","überweißet","ueberweisset","überweißtet","ueberweisstet","überwirft","ueberwirft","überwerft","ueberwerft","überwerfet","ueberwerfet","überwarft","ueberwarft","überwürfet","ueberwuerfet","überwürft","ueberwuerft","überwirft","ueberwirft","überwerft","ueberwerft","überwerfet","ueberwerfet","überwarft","ueberwarft","überwürfet","ueberwuerfet","überwürft","ueberwuerft","überwertetet","ueberwertetet","überwiegt","ueberwiegt","überwieget","ueberwieget","überwogt","ueberwogt","überwöget","ueberwoeget","überwögt","ueberwoegt","überwindet","ueberwindet","überwandet","ueberwandet","überwändet","ueberwaendet","überwölbet","ueberwoelbet","überwölbtet","ueberwoelbtet","ueberwuerzet","ueberwuerzet","überwürztet","ueberwuerztet","überzahlet","ueberzahlet","überzahltet","ueberzahltet","überzahltet","ueberzahltet","überzeichnetet","ueberzeichnetet","überzeuget","ueberzeuget","überzeugtet","ueberzeugtet","überzieht","ueberzieht","überziehet","ueberziehet","überzogt","ueberzogt","überzöget","ueberzoeget","überzögt","ueberzoegt","überzüchtetet","ueberzuechtetet","überangebot","ueberangebot","überbrückungskredit","ueberbrückungskredit","übereinkunft","uebereinkunft","überfahrt","ueberfahrt","überflugverbot","ueberflugverbot","überflutungsgebiet","ueberflutungsgebiet","überfracht","ueberfracht","überfrucht","ueberfrucht","übergangslaut","uebergangslaut","übergebot","uebergebot","übergewicht","uebergewicht","überhangmandat","ueberhangmandat","überhangsrecht","ueberhangsrecht","überholverbot","ueberholverbot","überladenheit","ueberladenheit","überlandfahrt","ueberlandfahrt","überlast","ueberlast","überlegenheit","ueberlegenheit","übermacht","uebermacht","übermaßverbot","uebermassverbot","übermut","uebermut","überraschungseffekt","ueberraschungseffekt","überraschungsgast","ueberraschungsgast","überraschungsmoment","ueberraschungsmoment","überredungskunst","ueberredungskunst","überreiztheit","ueberreiztheit","überrest","ueberrest","überschicht","ueberschicht","überschnitt","ueberschnitt","überschrift","ueberschrift","überschwemmungsgebiet","ueberschwemmungsgebiet","überseegebiet","ueberseegebiet","überseegeschäft","ueberseegeschaeft","übersicht","uebersicht","überspanntheit","ueberspanntheit","überspitztheit","ueberspitztheit","übertragungsrecht","uebertragungsrecht","übertriebenheit","uebertriebenheit","übertritt","uebertritt","überwachungsdienst","ueberwachungsdienst","überwachungsstaat","ueberwachungsstaat","überwelt","ueberwelt","überwinterungsgebiet","ueberwinterungsgebiet","überzeugtheit","ueberzeugtheit","überzeugungstat","ueberzeugungstat","überziehungskredit","ueberziehungskredit"],{indices:Ye,values:Ke}=e.languageProcessing,{getIndicesByWord:Xe,getIndicesByWordList:et}=Ye,{Clause:tt}=Ke,rt=/\S+(apparat|arbeit|dienst|haft|halt|keit|kraft|not|pflicht|schaft|schrift|tät|wert|zeit)($|[ \n\r\t.,'()"+-;!?:/»«‹›<>])/gi,{getClausesSplitOnStopWords:st,createRegexFromArray:nt}=e.languageProcessing,lt={Clause:class extends tt{constructor(e,t){super(e,t),this._participles=function(e){const t=Ve(e),r=[];return(0,Y.forEach)(t,(function(e){(0!==He(e).length||0!==Ge(e).length||0!==_e(e).length||0!==Ze(e).length||0!==Qe(e).length||Ne.includes(e))&&r.push(e)})),r}(this.getClauseText()),this.checkParticiples()}checkParticiples(){const e=this.getParticiples().filter((e=>!(this.hasNounSuffix(e)||(0,Y.includes)(Je,e)||this.hasHabenSeinException(e)||(0,Y.includes)(n,e))));this.setPassive(e.length>0)}hasNounSuffix(e){return null!==e.match(rt)}hasHabenSeinException(e){const t=Xe(e,this.getClauseText());let r=et(["haben","sein"],this.getClauseText());if(0===t.length||0===r.length)return!1;r=(0,Y.map)(r,"index");const s=t[0];return(0,Y.includes)(r,s.index+s.match.length+1)}},regexes:{auxiliaryRegex:nt(i.all),stopwordRegex:nt(G)}};function at(e){return st(e,lt)}const it=function(e,t){const r=new RegExp("^"+e.participleStemmingClasses[1].regex);return new RegExp("^"+e.participleStemmingClasses[0].regex).test(t)?t.slice(2,t.length-2):r.test(t)?t.slice(2,t.length-1):null},bt=function(e,t,r,s,n){for(const l of t)if(new RegExp("^"+l+r).test(e)){const t=e.slice(l.length-e.length);return l+t.slice(s,t.length-n)}return null},ut=function(e,t){const r=e.prefixes.separableOrInseparable;for(const s of e.participleStemmingClasses){const n=s.regex,l=s.startStem,a=s.endStem,i=s.separable?e.prefixes.separable:e.prefixes.inseparable;let b=bt(t,i,n,l,a);if(b)return b;if(b=bt(t,r,n,l,a),b)return b}return null},gt=function(e){let t=e.search(/[aeiouyäöü][^aeiouyäöü]/);return-1!==t&&(t+=2),-1!==t&&t<3&&(t=3),t},ht=function(e){const t=e.search(/(em|ern|er)$/g),r=e.search(/(e|en|es)$/g);let s=e.search(/([bdfghklmnrt]s)$/g);-1!==s&&s++;let n="",l=1e4;return-1!==t?(n="a",l=t,{index1:l,optionUsed1:n}):-1!==r?(n="b",l=r,{index1:l,optionUsed1:n}):-1!==s?(n="c",l=s,{index1:l,optionUsed1:n}):{index1:l,optionUsed1:n}},ot=function(e){const t=e.search(/(en|er|est)$/g);let r=e.search(/(.{3}[bdfghklmnt]st)$/g);-1!==r&&(r+=4);let s=1e4;return-1!==t?s=t:-1!==r&&(s=r),s},ct=function(e,t,r,s){return 1e4!==t&&-1!==s&&t>=s&&(e=e.substring(0,t),"b"===r&&-1!==e.search(/niss$/)&&(e=e.substring(0,e.length-1))),e},dt=function(e,t,r){return 1e4!==t&&-1!==r&&t>=r&&(e=e.substring(0,t)),e},wt=function(e,t){const r=e.veryIrregularVerbs.find((e=>e.forms.includes(t)));return r?r.stem:null},{flattenSortLength:mt}=e.languageProcessing,ft=function(e,t){const r=e.exceptionStems;for(const e of r){const r=e.find((e=>t.endsWith(e)));if(r)return t.slice(0,t.length-r.length)+e[0]}return null},pt=function(e,t){const r=e.exceptions;for(const e of Object.keys(r)){const s=r[e];for(const e of s)if(e.includes(t))return e[0]}return null},kt=function(e,t){let r=t;const s=e.strongAndIrregularVerbs.stems;let n=mt(e.prefixes).find((e=>t.startsWith(e)));if(n){const e=r.slice(n.length,r.length);e.length>2?r=e:n=null}for(const e of s){let t=e.stems;if(t=(0,Y.flatten)(Object.values(t)),t.includes(r))return n?n+e.stems.present:e.stems.present}return null};const{baseStemmer:yt}=e.languageProcessing;function zt(e){const t=(0,Y.get)(e.getData("morphology"),"de",!1);return t?e=>function(e,t){const r=t.verbs,s=function(e,t){const r=wt(e,t);if(r)return r;t=(t=(t=(t=t.replace(/([aeiouyäöü])u([aeiouyäöü])/g,"$1U$2")).replace(/([aeiouyäöü])y([aeiouyäöü])/g,"$1Y$2")).replace(/([aeiouyäöü])i([aeiouyäöü])/g,"$1I$2")).replace(/([aeiouyäöü])e([aeiouyäöü])/g,"$1E$2");const s=gt(t),n=ht(t).index1,l=ht(t).optionUsed1;t=ct(t,n,l,s);const a=ot(t);return(t=(t=(t=(t=dt(t,a,s)).replace(/U/g,"u")).replace(/Y/g,"y")).replace(/I/g,"i")).replace(/E/g,"e")}(r,e);return ft(t.nouns,s)||pt(t.adjectives,s)||kt(r,s)||function(e,t){if(Ie().length>0||Je.includes(t))return"";let r=it(e,t);return r||(r=ut(e,t),r||null)}(r,e)||s}(e,t):yt}const{formatNumber:vt}=e.helpers;function Et(e){const t=180-e.averageWordsPerSentence-58.5*e.numberOfSyllables/e.numberOfWords;return vt(t)}function Ft(e){return e=e.toLowerCase(),H.includes(e)}const{AbstractResearcher:Bt}=e.languageProcessing;class jt extends Bt{constructor(e){super(e),Object.assign(this.config,{language:"de",passiveConstructionType:"periphrastic",firstWordExceptions:t,functionWords:H,stopWords:G,transitionWords:u,twoPartTransitionWords:Z,syllables:Q,keyphraseLength:J}),Object.assign(this.helpers,{getClauses:at,getStemmer:zt,fleschReadingScore:Et,memoizedTokenizer:Se,checkIfWordIsFunction:Ft})}}})(),(window.yoast=window.yoast||{}).Researcher=s})();