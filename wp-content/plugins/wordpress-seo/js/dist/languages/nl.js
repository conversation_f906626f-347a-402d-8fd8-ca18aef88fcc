(()=>{"use strict";var e={d:(r,n)=>{for(var t in n)e.o(n,t)&&!e.o(r,t)&&Object.defineProperty(r,t,{enumerable:!0,get:n[t]})},o:(e,r)=>Object.prototype.hasOwnProperty.call(e,r),r:e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})}},r={};e.r(r),e.d(r,{default:()=>Ze});const n=window.yoast.analysis,t=["de","het","een","één","eén","twee","drie","vier","vijf","zes","zeven","acht","negen","tien","dit","dat","die","deze"],o=["aangezien","al","aldus","allereerst","als","alsook","anderzij<PERSON>","bijgevolg","bijvoorbeeld","bovendien","concluderend","daardoor","daarentegen","daarmee","daarna","daarnaast","daarom","daartoe","daarvoor","dadelijk","dan","desondanks","dienovereenkomstig","dientegevolge","doch","doordat","dus","echter","eerst","evenals","eveneens","evenzeer","hierom","hoewel","immers","indien","integendeel","intussen","kortom","later","maar","mits","nadat","namelijk","net als","niettemin","noch","ofschoon","omdat","ondanks","ondertussen","ook","opdat","resumerend","samengevat","samenvattend","tegenwoordig","teneinde","tenzij","terwijl","tevens","toch","toen","uiteindelijk","vanwege","vervolgens","voorafgaand","vooralsnog","voordat","voorts","vroeger","waardoor","waarmee","waaronder","wanneer","want","zoals","zodat","zodoende","zodra"],d=o.concat(["aan de andere kant","aan de ene kant","aangenomen dat","al met al","alles afwegend","alles bij elkaar","alles in aanmerking nemend","als gevolg van","anders gezegd","daar staat tegenover","daarbij komt","daaruit volgt","dat betekent","dat blijkt uit","de oorzaak daarvan is","de oorzaak hiervan is","door middel van","een voorbeeld hiervan","een voorbeeld van","gesteld dat","hetzelfde als","hieruit kunnen we afleiden","hieruit volgt","hoe het ook zij","in de derde plaats","in de eerste plaats","in de tweede plaats","in één woord","in het bijzonder","in het geval dat","in plaats van","in tegenstelling tot","in vergelijking met","maar ook","met als doel","met andere woorden","met behulp van","met de bedoeling","neem nou","net als","om kort te gaan","onder andere","op dezelfde wijze","stel dat","te danken aan","te wijten aan","ten derde","ten eerste","ten gevolge van","ten slotte","ten tweede","ter conclusie","ter illustratie","ter verduidelijking","tot nog toe","tot slot","vandaar dat","vergeleken met","voor het geval dat"]);function a(e){let r=e;return e.forEach((n=>{(n=n.split("-")).length>0&&n.filter((r=>!e.includes(r))).length>0&&(r=r.concat(n))})),r}const l=["de","het","een","der","des","den"],s=["eén","één","twee","drie","vier","vijf","zes","zeven","acht","negen","tien","elf","twaalf","dertien","veertien","vijftien","zestien","zeventien","achttien","negentien","twintig","honderd","honderden","duizend","duizenden","miljoen","miljoenen","biljoen","biljoenen"],i=["eerste","tweede","derde","vierde","vijfde","zesde","zevende","achtste","negende","tiende","elfde","twaalfde","dertiende","veertiende","vijftiende","zestiende","zeventiende","achttiende","negentiende","twinstigste"],g=["ik","je","jij","hij","ze","we","wij","jullie","zij","u","ge","gij","men"],b=["mij","jou","hem","haar","hen","hun","uw"],v=["dit","dat","deze","die","zelf"],u=["mijn","mijne","jouw","jouwe","zijne","hare","ons","onze","hunne","uwe","elkaars","elkanders"],c=["alle","sommige","sommigen","weinig","weinige","weinigen","veel","vele","velen","geen","beetje","elke","elk","genoeg","meer","meest","meeste","meesten","paar","zoveel","enkele","enkelen","zoveelste","hoeveelste","laatste","laatsten","iedere","allemaal","zekere","ander","andere","gene","enig","enige","verscheidene","verschillende","voldoende","allerlei","allerhande","enerlei","enerhande","beiderlei","beiderhande","tweeërlei","tweeërhande","drieërlei","drieërhande","velerlei","velerhande","menigerlei","menigerhande","enigerlei","enigerhande","generlei","generhande"],h=["mezelf","mijzelf","jezelf","jouzelf","zichzelf","haarzelf","hemzelf","onszelf","julliezelf","henzelf","hunzelf","uzelf","zich"],m=["mekaar","elkaar","elkander","mekander"],w=["iedereen","ieder","eenieder","alleman","allen","alles","iemand","niemand","iets","niets","menigeen"],k=["ieders","aller","iedereens","eenieders"],p=["welke","welk","wat","wie","wiens","wier"],f=["hoe","waarom","waar","hoezo","hoeveel"],y=["daaraan","daarachter","daaraf","daarbij","daarbinnen","daarboven","daarbuiten","daardoorheen","daarheen","daarin","daarjegens","daarmede","daarnaar","daarnaartoe","daaromtrent","daaronder","daarop","daarover","daaroverheen","daarrond","daartegen","daartussen","daartussenuit","daaruit","daarvan","daarvandaan","eraan","erachter","erachteraan","eraf","erbij","erbinnen","erboven","erbuiten","erdoor","erdoorheen","erheen","erin","erjegens","ermede","ermee","erna","ernaar","ernaartoe","ernaast","erom","eromtrent","eronder","eronderdoor","erop","eropaf","eropuit","erover","eroverheen","errond","ertegen","ertegenaan","ertoe","ertussen","ertussenuit","eruit","ervan","ervandaan","ervandoor","ervoor","hieraan","hierachter","hieraf","hierbij","hierbinnen","hierboven","hierbuiten","hierdoor","hierdoorheen","hierheen","hierin","hierjegens","hierlangs","hiermede","hiermee","hierna","hiernaar","hiernaartoe","hiernaast","hieromheen","hieromtrent","hieronder","hierop","hierover","hieroverheen","hierrond","hiertegen","hiertoe","hiertussen","hiertussenuit","hieruit","hiervan","hiervandaan","hiervoor","vandaan","waaraan","waarachter","waaraf","waarbij","waarboven","waarbuiten","waardoorheen","waarheen","waarin","waarjegens","waarmede","waarna","waarnaar","waarnaartoe","waarnaast","waarop","waarover","waaroverheen","waarrond","waartegen","waartegenin","waartoe","waartussen","waartussenuit","waaruit","waarvan","waarvandaan","waarvoor"],j=["daar","hier","ginder","daarginds","ginds","ver","veraf","ergens","nergens","overal","dichtbij","kortbij"],z=["word","wordt","werd","werden","ben","bent","is","was","waren"],x=["worden","zijn"],S=["heb","hebt","heeft","hadden","had","kun","kan","kunt","kon","konden","mag","mocht","mochten","dien","dient","diende","dienden","moet","moest","moesten","ga","gaat","ging","gingen"],P=["hebben","kunnen","mogen","dienen","moeten","gaan"],E=["blijkt","blijk","bleek","bleken","gebleken","dunkt","dunk","dunkte","dunkten","gedunkt","heet","heette","heetten","geheten","lijkt","lijk","geleken","leek","leken","schijn","schijnt","scheen","schenen","toescheen","toeschijnt","toeschijn","toeschenen"],F=["blijken","dunken","heten","lijken","schijnen","toeschijnen"],B=["à","aan","aangaande","achter","behalve","behoudens","beneden","benevens","benoorden","benoordoosten","benoordwesten","beoosten","betreffende","bewesten","bezijden","bezuiden","bezuidoosten","bezuidwesten","bij","binnen","blijkens","boven","bovenaan","buiten","circa","conform","contra","cum","dankzij","door","gedurende","gezien","in","ingevolge","inzake","jegens","krachtens","langs","luidens","met","middels","na","naar","naast","nabij","namens","nevens","niettegenstaande","nopens","om","omstreeks","omtrent","onder","onderaan","ongeacht","onverminderd","op","over","overeenkomstig","per","plus","post","richting","rond","rondom","spijts","staande","te","tegen","tegenover","ten","ter","tijdens","tot","tussen","uit","van","vanaf","vanuit","versus","via","vis-à-vis","volgens","voor","voorbij","wegens","zijdens","zonder"],M=["af","heen","mee","toe","achterop","onderin","voorin","bovenop","buitenop","achteraan","onderop","binnenin","tevoren"],W=["en","alsmede","of","ofwel","en/of"],O=["zowel","evenmin","zomin","hetzij"],T=["vermits","dewijl","dorodien","naardien","nademaal","overmits","wijl","eer","eerdat","aleer","vooraleer","alvorens","totdat","zolang","sinds","sedert","ingeval","tenware","alhoewel","hoezeer","uitgezonderd","zoverre","zover","naargelang","naarmate","alsof"],I=["zegt","zei","vraagt","vroeg","denkt","dacht","stelt","pleit","pleitte"],V=["zeer","erg","redelijk","flink","tikkeltje","bijzonder","ernstig","enigszins","zo","tamelijk","nogal","behoorlijk","zwaar","heel","hele","reuze","buitengewoon","ontzettend","vreselijk"],A=["laat","liet","lieten","kom","komt","kwam","kwamen","maakt","maak","maakte","maakten","doe","doet","deed","deden","vindt","vind","vond","vonden"],D=["laten","komen","maken","doen","vinden"],C=["nieuw","nieuwe","nieuwer","nieuwere","nieuwst","nieuwste","oud","oude","ouder","oudere","oudst","oudste","vorig","vorige","goed","goede","beter","betere","best","beste","groot","grote","groter","grotere","grootst","grootste","makkelijk","makkelijke","makkelijker","makkelijkere","makkelijkst","makkelijste","gemakkelijk","gemakkelijke","gemakkelijker","gemakkelijkere","gemakkelijkst","gemakkelijste","simpel","simpele","simpeler","simpelere","simpelst","simpelste","snel","snelle","sneller","snellere","snelst","snelste","verre","verder","verdere","verst","verste","lang","lange","langer","langere","langst","langste","hard","harde","harder","hardere","hardst","hardste","minder","mindere","minst","minste","eigen","laag","lage","lager","lagere","laagst","laagste","hoog","hoge","hoger","hogere","hoogst","hoogste","klein","kleine","kleiner","kleinere","kleinst","kleinste","kort","korte","korter","kortere","kortst","kortste","herhaaldelijke","directe","ongeveer","slecht","slechte","slechter","slechtere","slechtst","slechtste","zulke","zulk","zo'n","zulks","er","extreem","extreme","bijbehorende","bijbehorend","niet"],R=["oh","wauw","hèhè","hè","hé","au","ai","jaja","welja","jawel","ssst","heremijntijd","hemeltjelief","aha","foei","hmm","nou","nee","tja","nja","okido","ho","halt","komaan","komop","verrek","nietwaar","brr","oef","ach","och","bah","enfin","afijn","haha","hihi","hatsjie","hatsjoe","hm","tring","vroem","boem","hopla"],N=["ml","cl","dl","l","tl","el","mg","g","gr","kg","ca","theel","min","sec","uur"],L=["seconde","secondes","seconden","minuut","minuten","uur","uren","dag","dagen","week","weken","maand","maanden","jaar","jaren","vandaag","morgen","overmorgen","gisteren","eergisteren","'s","morgens","avonds","middags","nachts"],H=["ding","dingen","manier","manieren","item","items","keer","maal","procent","geval","aspect","persoon","personen","deel"],$=["wel","ja","neen","oké","oke","okee","ok","zoiets","€","euro"],q=(a([].concat(x,P,F,D)),a([].concat(i,C)),a([].concat(l,B,W,v,V,c)),a([].concat(o,g,b,h,R,s,z,S,E,I,A,w,O,T,f,p,j,$,M,y,N,L,H,m,u)),a([].concat(l,f,s,u,h,k,E,F,B))),_=a([].concat(l,s,i,v,u,h,m,g,b,c,w,k,p,f,y,j,M,z,x,S,P,E,F,B,W,O,T,I,o,["absoluut","zeker","ongetwijfeld","sowieso","onmiddelijk","meteen","inclusief","direct","ogenblikkelijk","terstond","natuurlijk","vanzelfsprekend","gewoonlijk","normaliter","doorgaans","werkelijk","daadwerkelijk","inderdaad","waarachtig","oprecht","bijna","meestal","misschien","waarschijnlijk","wellicht","mogelijk","vermoedelijk","allicht","aannemelijk","oorspronkelijk","aanvankelijk","initieel","eigenlijk","feitelijk","wezenlijk","juist","reeds","alvast","bijv.","vaak","dikwijls","veelal","geregeld","menigmaal","regelmatig","veelvuldig","eenvoudigweg","simpelweg","louter","kortweg","stomweg","domweg","zomaar","eventueel","mogelijkerwijs","eens","weleens","nooit","ooit","anders","momenteel","thans","incidenteel","trouwens","elders","volgend","recent","onlangs","recentelijk","laatst","zojuist","relatief","duidelijk","overduidelijk","klaarblijkelijk","nadrukkelijk","ogenschijnlijk","kennelijk","schijnbaar","alweer","continu","herhaaldelijk","nog","steeds","nu"],V,A,D,R,C,N,H,$,L,["mevr","dhr","mr","dr","prof"],["jr","sr"])),G=["alhoewel","als","dan","doordat","hoewel","hoezeer","indien","mits","naargelang","naarmate","nadat","ofschoon","omdat","opdat","tenzij","toen","voordat","voorzover","wanneer","zoals","zodat","zodra","zolang","wie","wiens","wier","welke","welk"],J=[["aan de ene kant","aan de andere kant"],["enerzijds","anderzijds"],["natuurlijk","maar"],["niet alleen","maar ook"],["noch","noch"],["zowel","als"]],U=JSON.parse('{"vowels":"aáäâeéëêiíïîoóöôuúüûy","deviations":{"vowels":[{"fragments":["ue$","dge$","[tcp]iënt","ace$","[br]each","[ainpr]tiaal","[io]tiaan","gua[yc]","[^i]deal","tive$","load","[^e]coke","[^s]core$"],"countModifier":-1},{"fragments":["aä","aeu","aie","ao","ë","eo","eú","ieau","ea$","ea[^u]","ei[ej]","eu[iu]","ï","iei","ienne","[^l]ieu[^w]","[^l]ieu$","i[auiy]","stion","[^cstx]io","^sion","riè","oö","oa","oeing","oie","[eu]ü","[^q]u[aeèo]","uie","[bhnpr]ieel","[bhnpr]iël"],"countModifier":1},{"fragments":["[aeolu]y[aeéèoóu]"],"countModifier":1}],"words":{"full":[{"word":"bye","syllables":1},{"word":"core","syllables":1},{"word":"cure","syllables":1},{"word":"dei","syllables":2},{"word":"dope","syllables":1},{"word":"dude","syllables":1},{"word":"fake","syllables":1},{"word":"fame","syllables":1},{"word":"five","syllables":1},{"word":"hole","syllables":1},{"word":"least","syllables":1},{"word":"lone","syllables":1},{"word":"minute","syllables":2},{"word":"move","syllables":1},{"word":"nice","syllables":1},{"word":"one","syllables":1},{"word":"state","syllables":1},{"word":"surplace","syllables":2},{"word":"take","syllables":1},{"word":"trade","syllables":1},{"word":"wide","syllables":1}],"fragments":{"global":[{"word":"adieu","syllables":2},{"word":"airline","syllables":2},{"word":"airmiles","syllables":2},{"word":"alien","syllables":3},{"word":"ambient","syllables":3},{"word":"announcement","syllables":3},{"word":"appearance","syllables":3},{"word":"appeasement","syllables":3},{"word":"atheneum","syllables":4},{"word":"awesome","syllables":2},{"word":"baccalaurei","syllables":5},{"word":"baccalaureus","syllables":5},{"word":"baseball","syllables":3},{"word":"basejump","syllables":2},{"word":"banlieue","syllables":3},{"word":"bapao","syllables":2},{"word":"barbecue","syllables":3},{"word":"beamer","syllables":2},{"word":"beanie","syllables":2},{"word":"beat","syllables":1},{"word":"belle","syllables":2},{"word":"bête","syllables":1},{"word":"bingewatch","syllables":2},{"word":"blocnote","syllables":2},{"word":"blue","syllables":1},{"word":"board","syllables":1},{"word":"break","syllables":1},{"word":"broad","syllables":1},{"word":"bulls-eye","syllables":2},{"word":"business","syllables":2},{"word":"byebye","syllables":2},{"word":"cacao","syllables":2},{"word":"caesar","syllables":2},{"word":"camaieu","syllables":3},{"word":"caoutchouc","syllables":2},{"word":"carbolineum","syllables":5},{"word":"catchphrase","syllables":1},{"word":"carrier","syllables":3},{"word":"cheat","syllables":1},{"word":"cheese","syllables":1},{"word":"circonflexe","syllables":3},{"word":"clean","syllables":1},{"word":"cloak","syllables":1},{"word":"cobuying","syllables":3},{"word":"comeback","syllables":2},{"word":"comfortzone","syllables":3},{"word":"communiqué","syllables":4},{"word":"conopeum","syllables":4},{"word":"console","syllables":2},{"word":"corporate","syllables":3},{"word":"coûte","syllables":1},{"word":"creamer","syllables":2},{"word":"crime","syllables":1},{"word":"cruesli","syllables":2},{"word":"deadline","syllables":2},{"word":"deautoriseren","syllables":6},{"word":"deuce","syllables":1},{"word":"deum","syllables":2},{"word":"dirndl","syllables":2},{"word":"dread","syllables":2},{"word":"dreamteam","syllables":2},{"word":"drone","syllables":1},{"word":"enquête","syllables":3},{"word":"escape","syllables":2},{"word":"exposure","syllables":3},{"word":"extranei","syllables":4},{"word":"extraneus","syllables":4},{"word":"eyecatcher","syllables":3},{"word":"eyeliner","syllables":3},{"word":"eyeopener","syllables":4},{"word":"eyetracker","syllables":3},{"word":"eyetracking","syllables":3},{"word":"fairtrade","syllables":2},{"word":"fauteuil","syllables":2},{"word":"feature","syllables":2},{"word":"feuilletee","syllables":3},{"word":"feuilleton","syllables":3},{"word":"fisheye","syllables":2},{"word":"fineliner","syllables":3},{"word":"finetunen","syllables":3},{"word":"forehand","syllables":2},{"word":"freak","syllables":1},{"word":"fusioneren","syllables":4},{"word":"gayparade","syllables":3},{"word":"gaypride","syllables":2},{"word":"goal","syllables":1},{"word":"grapefruit","syllables":2},{"word":"gruyère","syllables":3},{"word":"guele","syllables":1},{"word":"guerrilla","syllables":3},{"word":"guest","syllables":1},{"word":"hardware","syllables":2},{"word":"haute","syllables":1},{"word":"healing","syllables":2},{"word":"heater","syllables":2},{"word":"heavy","syllables":2},{"word":"hoax","syllables":1},{"word":"hotline","syllables":2},{"word":"idee-fixe","syllables":3},{"word":"inclusive","syllables":3},{"word":"inline","syllables":2},{"word":"intake","syllables":2},{"word":"intensive","syllables":3},{"word":"jeans","syllables":1},{"word":"Jones","syllables":1},{"word":"jubileum","syllables":4},{"word":"kalfsribeye","syllables":3},{"word":"kraaiennest","syllables":3},{"word":"lastminute","syllables":3},{"word":"learning","syllables":2},{"word":"league","syllables":1},{"word":"line-up","syllables":2},{"word":"linoleum","syllables":4},{"word":"load","syllables":1},{"word":"loafer","syllables":2},{"word":"longread","syllables":2},{"word":"lookalike","syllables":3},{"word":"louis","syllables":3},{"word":"lyceum","syllables":3},{"word":"magazine","syllables":3},{"word":"mainstream","syllables":2},{"word":"make-over","syllables":3},{"word":"make-up","syllables":2},{"word":"malware","syllables":2},{"word":"marmoleum","syllables":4},{"word":"mausoleum","syllables":4},{"word":"medeauteur","syllables":4},{"word":"midlifecrisis","syllables":4},{"word":"migraineaura","syllables":5},{"word":"milkshake","syllables":2},{"word":"millefeuille","syllables":4},{"word":"mixed","syllables":1},{"word":"muesli","syllables":2},{"word":"museum","syllables":3},{"word":"must-have","syllables":2},{"word":"must-read","syllables":2},{"word":"notebook","syllables":2},{"word":"nonsense","syllables":2},{"word":"nowhere","syllables":2},{"word":"nurture","syllables":2},{"word":"offline","syllables":2},{"word":"oneliner","syllables":3},{"word":"onesie","syllables":2},{"word":"online","syllables":2},{"word":"opinion","syllables":3},{"word":"paella","syllables":3},{"word":"pacemaker","syllables":3},{"word":"panache","syllables":2},{"word":"papegaaienneus","syllables":5},{"word":"passe-partout","syllables":3},{"word":"peanuts","syllables":2},{"word":"perigeum","syllables":4},{"word":"perineum","syllables":4},{"word":"perpetuum","syllables":4},{"word":"petroleum","syllables":4},{"word":"phone","syllables":3},{"word":"picture","syllables":2},{"word":"placemat","syllables":2},{"word":"porte-manteau","syllables":3},{"word":"portefeuille","syllables":4},{"word":"presse-papier","syllables":3},{"word":"primetime","syllables":2},{"word":"queen","syllables":1},{"word":"questionnaire","syllables":3},{"word":"queue","syllables":1},{"word":"reader","syllables":2},{"word":"reality","syllables":3},{"word":"reallife","syllables":2},{"word":"remake","syllables":2},{"word":"repeat","syllables":2},{"word":"repertoire","syllables":3},{"word":"research","syllables":2},{"word":"reverence","syllables":3},{"word":"ribeye","syllables":2},{"word":"ringtone","syllables":3},{"word":"road","syllables":1},{"word":"roaming","syllables":2},{"word":"sciencefiction","syllables":4},{"word":"selfmade","syllables":2},{"word":"sidekick","syllables":2},{"word":"sightseeing","syllables":3},{"word":"skyline","syllables":2},{"word":"smile","syllables":1},{"word":"sneaky","syllables":2},{"word":"software","syllables":2},{"word":"sparerib","syllables":2},{"word":"speaker","syllables":2},{"word":"spread","syllables":1},{"word":"statement","syllables":2},{"word":"steak","syllables":1},{"word":"steeplechase","syllables":3},{"word":"stonewash","syllables":2},{"word":"store","syllables":1},{"word":"streaken","syllables":2},{"word":"stream","syllables":1},{"word":"streetware","syllables":1},{"word":"supersoaker","syllables":4},{"word":"surprise-party","syllables":4},{"word":"sweater","syllables":2},{"word":"teaser","syllables":2},{"word":"tenue","syllables":2},{"word":"template","syllables":2},{"word":"timeline","syllables":2},{"word":"tissue","syllables":2},{"word":"toast","syllables":1},{"word":"tête-à-tête","syllables":3},{"word":"typecast","syllables":2},{"word":"unique","syllables":2},{"word":"ureum","syllables":3},{"word":"vibe","syllables":1},{"word":"vieux","syllables":1},{"word":"ville","syllables":1},{"word":"vintage","syllables":2},{"word":"wandelyup","syllables":3},{"word":"wiseguy","syllables":2},{"word":"wake-up-call","syllables":3},{"word":"webcare","syllables":2},{"word":"winegum","syllables":2},{"word":"base","syllables":1,"notFollowedBy":["e","n","r"]},{"word":"game","syllables":1,"notFollowedBy":["n","l","r"]},{"word":"style","syllables":1,"notFollowedBy":["n","s"]},{"word":"douche","syllables":1,"notFollowedBy":["n","s"]},{"word":"space","syllables":1,"notFollowedBy":["n","s"]},{"word":"striptease","syllables":2,"notFollowedBy":["n","s"]},{"word":"jive","syllables":1,"notFollowedBy":["n","r"]},{"word":"keynote","syllables":2,"notFollowedBy":["n","r"]},{"word":"mountainbike","syllables":3,"notFollowedBy":["n","r"]},{"word":"face","syllables":1,"notFollowedBy":["n","t"]},{"word":"challenge","syllables":2,"notFollowedBy":["n","r","s"]},{"word":"cruise","syllables":1,"notFollowedBy":["n","r","s"]},{"word":"house","syllables":1,"notFollowedBy":["n","r","s"]},{"word":"dance","syllables":1,"notFollowedBy":["n","r","s"]},{"word":"franchise","syllables":2,"notFollowedBy":["n","r","s"]},{"word":"freelance","syllables":2,"notFollowedBy":["n","r","s"]},{"word":"lease","syllables":1,"notFollowedBy":["n","r","s"]},{"word":"linedance","syllables":2,"notFollowedBy":["n","r","s"]},{"word":"lounge","syllables":1,"notFollowedBy":["n","r","s"]},{"word":"merchandise","syllables":3,"notFollowedBy":["n","r","s"]},{"word":"performance","syllables":3,"notFollowedBy":["n","r","s"]},{"word":"release","syllables":2,"notFollowedBy":["n","r","s"]},{"word":"resource","syllables":2,"notFollowedBy":["n","r","s"]},{"word":"cache","syllables":1,"notFollowedBy":["c","l","n","t","x"]},{"word":"office","syllables":2,"notFollowedBy":["r","s"]},{"word":"close","syllables":1,"notFollowedBy":["r","t"]}],"atBeginningOrEnd":[{"word":"byte","syllables":1},{"word":"cake","syllables":1},{"word":"care","syllables":1},{"word":"coach","syllables":1},{"word":"coat","syllables":1},{"word":"earl","syllables":1},{"word":"foam","syllables":1},{"word":"gate","syllables":1},{"word":"head","syllables":1},{"word":"home","syllables":1},{"word":"live","syllables":1},{"word":"safe","syllables":1},{"word":"site","syllables":1},{"word":"soap","syllables":1},{"word":"teak","syllables":1},{"word":"team","syllables":1},{"word":"wave","syllables":1},{"word":"brace","syllables":1,"notFollowedBy":["s"]},{"word":"case","syllables":1,"notFollowedBy":["s"]},{"word":"fleece","syllables":1,"notFollowedBy":["s"]},{"word":"service","syllables":2,"notFollowedBy":["s"]},{"word":"voice","syllables":1,"notFollowedBy":["s"]},{"word":"kite","syllables":1,"notFollowedBy":["n","r"]},{"word":"skate","syllables":1,"notFollowedBy":["n","r"]},{"word":"race","syllables":1,"notFollowedBy":["n","r","s"]}],"atBeginning":[{"word":"coke","syllables":1},{"word":"deal","syllables":1},{"word":"image","syllables":2,"notFollowedBy":["s"]}],"atEnd":[{"word":"force","syllables":1},{"word":"tea","syllables":1},{"word":"time","syllables":1},{"word":"date","syllables":1,"alsoFollowedBy":["s"]},{"word":"hype","syllables":1,"alsoFollowedBy":["s"]},{"word":"quote","syllables":1,"alsoFollowedBy":["s"]},{"word":"tape","syllables":1,"alsoFollowedBy":["s"]},{"word":"upgrade","syllables":2,"alsoFollowedBy":["s"]}]}}}}'),Y={productPages:{parameters:{recommendedMinimum:3,recommendedMaximum:6,acceptableMaximum:7,acceptableMinimum:1}}},K=window.lodash,Q=["gebraad","gemoed","gebed","gebied","gebod","gebodsbord","geboorte-eiland","geboortestad","gebruikspaard","gedachtewereld","gedenkblad","gedenknaald","gedichtenwedstrijd","gedoogakkoord","gedoogbeleid","geduld","geestenwereld","geesteskind","geestestoestand","geesteswereld","gehandicaptenbeleid","gehoorafstand","gehoorsafstand","geitenbaard","geitenhuid","geld","geldhond","geldvoorraad","geleidehond","gelijkekansenbeleid","geloofsdaad","geloofsinhoud","geluidswand","gelukskind","gemeenschapsraad","gemeentebeleid","gemeenteraad","gemeenteraadslid","gemoedstoestand","genadeverbond","genderbeleid","geneesmiddelenbeleid","generaalsbewind","geslachtsdaad","gespreksavond","gespreksflard","getijdengebied","gevangenisbeleid","gevangeniswereld","gevechtsafstand","gevelwand","gevoelstoestand","gevoelswereld","gewelddaad","geweldigaard","geweldverbod","gezelschapshond","gezichtsafstand","gezichtshuid","gezinsbeleid","gezinsbond","gezinshoofd","gezinslid","gezinspaard","gezinstoestand","gezondheidsbeleid","gezondheidstoestand","gezondheidszorgbeleid","gecentreerd","geserreerd","gepolitoerd","gebocheld","gebrild","gegleufd","gekarteld","gemeubeld","gesausd","geaccidenteerd","geaccrediteerd","geacheveerd","geaderd","geaggregeerd","geagiteerd","geallieerd","geanimeerd","geanticipeerd","gearticuleerd","geassorteerd","gebenedijd","gebiedend","geblaseerd","geblindeerd","geborneerd","gebronzeerd","gebrouilleerd","gebruind","gecharmeerd","gechromeerd","geciviliseerd","geclausuleerd","gecoiffeerd","geconditioneerd","geconstipeerd","gecontinueerd","gecoöpteerd","gecrispeerd","gecultiveerd","gedecideerd","gedecolleteerd","gedegouteerd","gedemilitariseerd","gedemodeerd","gedesillusioneerd","gedesinteresseerd","gedetailleerd","gediplomeerd","gedisciplineerd","gedisponeerd","gedistingeerd","gedomicilieerd","gedoteerd","gedupeerd","geëigend","geestdodend","geestverruimend","geëxalteerd","geëxponeerd","gefigureerd","gefingeerd","geflatteerd","geforceerd","gefumeerd","gegeerd","gegeneerd","gegradueerd","gegriepeerd","gehaaid","gehandschoend","gehavend","gehomologeerd","gehorend","geïllustreerd","geïmponeerd","geïmproviseerd","geïncrimineerd","geïrriteerd","geklasseerd","gekmakend","gekuifd","gekwalificeerd","gelardeerd","geldend","geldverslindend","geleed","geleidend","gelieerd","geliefkoosd","gelijkluidend","gelinieerd","geluiddempend","geluidswerend","geluidwerend","gemarineerd","gematteerd","gemiddeld","geoccupeerd","geoutilleerd","geparaffineerd","geparfumeerd","gepatenteerd","gepermitteerd","geplafonneerd","geplisseerd","gepredisponeerd","geprefabriceerd","gepreoccupeerd","geproportioneerd","geraffineerd","gerandomiseerd","gereformeerd","gereglementeerd","geresigneerd","geresponsabiliseerd","gerimpeld","geringschattend","geruchtmakend","geruststellend","gesatureerd","gesauteerd","geschakeerd","gesepareerd","geseponeerd","gesofisticeerd","gesoigneerd","gespeend","gespikkeld","gestresseerd","geurenblind","gevergeerd","geverseerd","gezaghebbend","gezagsondermijnend","gezichtsbepalend","gezinsvervangend","gezwind","geit","gedragstherapeut","geveltoerist","gezant","gerant","gerst","gerstenat","geut","gebarenkunst","gebedsbijeenkomst","gebekvecht","gebiedsagent","gebit","geboorterecht","gebruikersovereenkomst","gebruiksrecht","gebruiksvoorschrift","gedragsvoorschrift","geest","geestdrift","geesteskracht","geestesproduct","geestkracht","gefluit","gehandicaptensport","geheimhoudingsplicht","geheimschrift","geheugenkunst","gehoorapparaat","geitenteelt","gekloot","geldautomaat","geldingskracht","geldingszucht","geldkist","geldmarkt","geldmarkttekort","geldpot","geldsoort","geldtekort","geldtransport","gelduitgifteautomaat","geldzucht","gelegenheidsargument","geloofsgenoot","geluidseffect","geluidsoverlast","geluidspoort","gemaksproduct","gemakzucht","gemberpot","gemeenschapsrecht","gemeenteadvocaat","gemeenteraadsbesluit","gemeenterecht","gemeentewet","gemeentewiet","gemoedsrust","geneeskracht","geneeskundestudent","geneeskunst","geneesmiddelenfabrikant","geneesmiddelenmarkt","generatieconflict","generatiegenoot","generatiepact","generatiestudent","genetkat","genocidewet","genot","genotsproduct","genotzucht","gent","geodeet","geologiedocent","gereedschapskist","gerucht","geruchtencircuit","geschiedenisdocent","geschiedenisstudent","geschiet","geschrift","gespreksgenoot","gesprekspunt","getijdenkracht","gevangenispoort","gevecht","gevechtskracht","gevechtssport","gevellijst","gevelornament","gewest","gewetensangst","gewetensconflict","gewicht","gewinzucht","gewondentransport","gewoonterecht","gewricht","gezagsapparaat","gezinsbudget","gezinsrapport","gezondheidseffect","gezondheidsklacht","gezondheidsproduct","gezondheidsrecht","gezondheidswet","gezondheidswinst","gerokt","gevlekt","gebuikt","gesaust","gebiedsgericht","geel-zwart","gehandicapt","gereformeerd-vrijgemaakt","gestuikt","geëtst","bed","bediendevakbond","bedrijfsbeleid","bedrijfsblad","bedrijfspand","bedrijfswereld","bedrijvenbond","beekdonderpad","beeld","beginselakkoord","begintoestand","begripsinhoud","begrotingsakkoord","begrotingsbeleid","behandelaanbod","beheerraad","beheersgebied","behoud","beiaard","bejaardenbeleid","bekerwedstrijd","belastinggebied","belastinggeld","belastingschuld","beleggingsbeleid","beleggingspand","beleid","beleidsdaad","beleidsgebied","belevingswereld","belplafond","beltegoed","bemanningslid","Bemiddelingsraad","bendehoofd","bendelid","benedenstad","benefietwedstrijd","benoemingenbeleid","benuttingsgraad","berberpaard","beregeningsverbod","bergeend","berggebied","bergland","bergpaard","bergpad","bergwand","beroepsarbeid","beroepsverbod","beroepswereld","beschermingsbeleid","beschermingsgebied","beslissingswedstrijd","besparingsbeleid","bestand","bestandsakkoord","besteleend","besturenbond","bestuursakkoord","bestuursbeleid","bestuurshoofd","bestuurslid","beukenblad","beursmaand","beursrecord","beurswaakhond","beurswereld","beveiligingsbeleid","bevolkingsbeleid","bewind","bewustzijnsinhoud","bewustzijnstoestand","bezuinigingsbeleid","beenhard","bebrild","beangstigend","bebaard","bedeesd","bederfwerend","bedreigend","bedrijvend","bedroevend","beduidend","beduusd","bedwelmend","beeldbepalend","beeldend","beeldvormend","beeldvullend","begeleidend","begerenswaard","begrijpend","behartenswaard","behartigenswaard","behoudend","bejaard","beklagenswaard","beklemmend","belanghebbend","belangstellend","belangwekkend","belastingbesparend","belastingbetalend","beledigend","beleerd","beleidsadviserend","belendend","belerend","bemoedigend","benauwend","benijdenswaard","bepalend","beperkend","beregoed","berekenend","beroemd","beroepsblind","beschaamd","beschamend","beschouwend","beschrijvend","besdragend","beslissend","bestaand","bestverkopend","beteuterd","betoverend","betraand","betreffend","betreurenswaard","bevelend","bevelhebbend","bevestigend","bevoegd","bevredigend","bevreemdend","bevriend","bewonderenswaard","bewustzijnsverruimend","bezwarend","beest","berggeit","betaalkracht","beerput","bergamot","beschuit","beademingsapparaat","beddenfabrikant","bedeltocht","bedevaart","bedevaartstocht","bediendecontract","bedieningsfout","bedilzucht","bedoeïenentent","bedrijfsadvocaat","bedrijfsfeest","bedrijfsfysiotherapeut","bedrijfsmanagement","bedrijfsopbrengst","bedrijfsrestaurant","bedrijfsresultaat","bedrijfssport","bedrijfswinst","bedrijvenmarkt","bedrust","beeldhouwkunst","beeldmoment","beeldrecht","beeldsnijkunst","beestenmarkt","beet","begeleidwonenproject","beginnersfout","beginpunt","begrippenapparaat","begrotingsdebat","begrotingsrecht","begrotingstekort","behaagzucht","behandelingsresultaat","behoudzucht","bejaardenpaspoort","bekerplant","bekerwinst","beklagrecht","beklemrecht","belangenconflict","belastingafdracht","belastingbiljet","belastingconsulent","belastingdienst","belastingexpert","belastingopbrengst","belastingplicht","belastingrecht","belastingspecialist","belastingwet","beleggersmarkt","beleggingsexpert","beleggingsmarkt","beleggingsopbrengst","beleggingsproduct","beleggingsresultaat","beleidsaspect","beleidsdebat","beleidsfout","beleidsresultaat","beleidsspecialist","belevingsrestaurant","belgicist","belminuut","beltegoedkaart","bemoeizucht","benefiet","benefietconcert","benoemingsbesluit","benzinelucht","benzinemarkt","benzinetekort","beoordelingsfout","beoordelingsrapport","berghut","bergklimaat","berglucht","bergrit","bergsport","bergtijdrit","bergtocht","berichtendienst","berkenhout","bermmonument","bermrecreant","bermsloot","bermtoerist","beroepsdiplomaat","beroepsernst","beroepsfout","beroepsgenoot","beroepsjournalist","beroepskracht","beroepsrecht","beroepssoldaat","beroepssport","berufsverbot","beschermingsbesluit","beschikkingsrecht","beslismoment","beslissingsrecht","besluit","bestaansrecht","bestandsformaat","bestelbiljet","bestelkaart","bestuursapparaat","bestuursassistent","bestuursbesluit","bestuursconflict","bestuurskracht","bestuurskundedocent","bestuursmandaat","bestuursprocesrecht","bestuursrecht","betaalautomaat","betaaldienst","betaalkaart","betaalopdracht","betalingsbalanstekort","betalingsopdracht","bètastudent","beterschapskaart","betrouwbaarheidsrit","beukenhout","beursapparaat","beursklimaat","beurskrant","beursmarkt","beursstudent","beurt","beverrat","bevoegdheidsconflict","bevrijdingsconcert","bevrijdingsfeest","bewaarplicht","bewegingsapparaat","bewegingsdocent","bewegingskunst","bewijskracht","bewijsrecht","bewustwordingsproject","bezemkast","bezit","bezitsrecht","bezoekrecht","bezuinigingsdrift","bezuinigingsopdracht","bezwaarschrift","beroepsgericht","bedompt","bedrijfsgericht","beginselvast","beleidsgericht","bewolkt","bezweet","verbeterblad","verband","verbeeldingswereld","verbod","verbodsbord","verbond","verdwaalarmband","verdwijnwoord","verenigingsblad","verenigingslid","verfhuid","vergismoord","vergunningenbeleid","verhalenpad","verhalenwedstrijd","verkeersaanbod","verkeersbeleid","verkeersbord","verkiezingsavond","verkleinwoord","verkoopbeleid","verkoopverbod","vernieuwingsbeleid","verpleeghuisbed","verraad","verschijningsverbod","verstand","vertoningsverbod","vertrekbeleid","vervalmaand","vervoerbeleid","vervoersaanbod","vervoersbeleid","vervoersbond","vervoersverbod","vervolgingsbeleid","verwijderingsbeleid","verzamelbeleid","verzekeringswereld","verzetsdaad","verzetsheld","verzuimbeleid","verdragend","verkeersremmend","verbazend","verbazingwekkend","verbijsterend","verblindend","verbluffend","verbouwereerd","verdaagd","verdedigend","verdovend","vereend","verfrissend","vergelijkend","verhalend","verheffend","verheugend","verkikkerd","verklarend","verkwikkend","verkwistend","verlammend","verlangend","verliesgevend","verlieslatend","verlieslijdend","verlokkend","verlossend","vermeend","vermeldenswaard","vermeldingswaard","vermoeiend","vermogend","vernederend","vernietigend","verontrustend","verpletterend","verrassend","verscheurend","verschillend","verslaafd","verspringend","verstikkend","verstrekkend","verstrooid","vertederend","vertrouwenwekkend","vertwijfeld","vervelend","verwaand","verwarrend","verwoestend","verzachtend","verziend","verzoenend","verwant","verantwoordingsplicht","verbandkist","verbeeldingskracht","verbintenissenrecht","verblijfsrecht","verbrandingsproduct","verbroederingsfeest","verdedigingsfout","verdragsrecht","verdriet","verdringingseffect","veredelingsproduct","verenigingsrecht","verffabrikant","verfpot","verfrest","vergiet","vergoedingslimiet","vergrotingsapparaat","vergunningplicht","verhaalsrecht","verhuiskist","verhuurboot","verjaardagsfeest","verjaardagsgast","verjaardagstaart","verjaarfeest","verjaringsfeest","verkeersagent","verkeersinfarct","verkeersmanagement","verkeersmarkt","verkeersoverlast","verkeerswet","verkenningstocht","verkiezingsbijeenkomst","verkiezingsbiljet","verkiezingsdebat","verkiezingsinkt","verkiezingsresultaat","verkiezingswinst","verkleedkist","verkoopapparaat","verkoopargument","verkoopopbrengst","verkoopopdracht","verkooprecht","verkoopresultaat","verkopersmarkt","verlatingsangst","verlovingsfeest","verminderingskaart","vermogensrecht","vermogenstekort","vermogenswinst","vernielzucht","vernietigingskracht","vernieuwingsdebat","vernieuwingsproject","veroveringstocht","veroveringszucht","verpleegassistent","verrassingseffect","verrassingsfeest","verrijkingsmarkt","verruimingskandidaat","verschoningsrecht","verschot","versproduct","versterfrecht","vertaalfout","vertaalproject","vertaalrecht","vertebraat","vertegenwoordigingsrecht","vervangingsmarkt","vervoersmanagement","vervoersmarkt","vervolgbijeenkomst","vervolgingsapparaat","vervolgopdracht","vervolgproject","vervreemdingseffect","verwijt","verzakingsrecht","verzamelkrant","verzekeringsagent","verzekeringsmarkt","verzekeringsproduct","verzekeringsrecht","verzekeringsresultaat","verzetskrant","verzoeningsbijeenkomst","verzorgingsproduct","slingerpad","avondgebed","bibbergeld","dageraad","drinkgeld","kalfsgebraad","leefgeld","ochtendgebed","ongelukskind","vluggerd","voltigeerpaard","voltigepaard","aandachtsgebied","aanlijngebod","aardbevingsgebied","abonnementsgeld","achtergrondgeluid","achterstandsgebied","actiegebied","afzetgebied","akkerbouwgebied","alpengebied","amazonegebied","ambtsgebied","ambtsgewaad","antigeluid","aspergebed","autonomiegebied","baggereiland","bangerd","bijgeluid","bijstandsgeld","binnenduingebied","blindengeleidehond","blowgebodsbord","boezemgebied","bongerd","bosgebied","bridgeavond","bridgebond","bridgewedstrijd","broedgebied","brongebied","budgetbeleid","burgerbewind","centrumgebied","collegelid","computergebied","concentratiegebied","conceptregeerakkoord","concessiegebied","conflictgebied","contactgeluid","crisisgebied","cultuurgebied","dankgebed","deelgebied","deelnemingenbeleid","deltagebied","deskundigheidsgebied","dierengeluid","doelgebied","doodsgewaad","doorgangsgebied","dopgeld","douanegebied","drempelgeld","driekoningenavond","duinengebied","duingebied","eigendomsvoorbehoud","energiegebied","engerd","eurogebied","feestgewaad","filmgebied","foerageergebied","formuliergebed","frequentiegebied","frontgebied","functioneringsgebied","gangenpaard","gitaargeluid","gitaargeweld","golfgebied","golflengtegebied","graangebied","grachtengebied","grensgebied","groeigebied","groengebied","groepsgeluid","groepsgeweld","grondgebied","grondwaterbeschermingsgebied","haflingerpaard","handelsgebied","havengebied","heidegebied","helikoptergeld","herkomstgebied","herwaarderingsgebied","hogedrukgebied","hogeronderwijsbeleid","hongersnood","hoogveengebied","ICT-gebied","immigratiegebied","inburgeringsbeleid","indicatiegebied","industriegebied","ingeland","inkomgeld","interessegebied","jachtgebied","jagershond","jongerenbeleid","jongerenblad","kantorengebied","kassengebied","keelgeluid","kennisgebied","kerngebied","kernwinkelgebied","kijkgeld","kindergeld","kleigebied","kloostergewaad","knipooggeweld","kogelwond","koorgebed","krapgeldbeleid","krijgsgeweld","krimpgebied","kruisgebed","kunstgebied","kustgebied","kwelgebied","lagedrukgebied","landbouwgebied","langeafstandspaard","langebaanwedstrijd","langetermijnbeleid","leefgebied","leergebied","leerstofgebied","legerpaard","legervoorraad","levensgebied","lidgeld","logeerbed","luchtvaartgebied","luistergeld","machtsgebied","managementbeleid","mandaatgebied","manegepaard","marktgebied","mededelingenblad","mededelingenbord","mediageweld","merengebied","middaggebed","middengebied","mijngebied","milieubeschermingsgebied","milieugebied","misgewaad","missiegebied","modegebied","moerasgebied","morgengebed","Morgenland","morgenstond","moslimgebied","motorgeluid","muilkorfgebod","nachtgewaad","nagelbed","natuurbeschermingsgebied","natuurgebied","natuurgeweld","natuurontwikkelingsgebied","NAVO-gebied","NAVO-grondgebied","nederzettingenbeleid","neerslaggebied","negerkind","no-gogebied","noodgebied","noordpoolgebied","Noordzeegebied","oceaangebied","octrooigebied","oefengebied","oerwoudgeluid","oliegebied","omgevingsbeleid","omgevingsgeluid","onderwijsgebied","onderzoeksgebied","onrustgebied","ontwikkelingsgebied","oorlogsgebied","oorlogsgeweld","oorsprongsgebied","operatiegebied","opleidingenaanbod","opmarsgebied","overgangsgebied","overlastgebied","overstromingsgebied","overwinteringsgebied","paaigebied","partnergeweld","ploegenwedstrijd","poldergebied","politiegeweld","potpoldergebied","presentiegeld","priestergewaad","regeerakkoord","regelafstand","regenboogkind","regenboogzebrapad","regenwoud","regeringsaanbod","regeringsbeleid","regeringsraad","regeringsstad","reizigersaanbod","richtingenstrijd","roggebrood","rouwgewaad","rugzakgeld","rustgebied","rustgeld","sabotagedaad","samenwerkingsgebied","schandegeld","Schengenakkoord","schietgebed","schoolgeld","servicegeweld","slangenhuid","sleutelgeld","slotgebed","smeekgebed","smeergeldstad","spaargeld","spanningsgebied","spiegelbeeld","spiegelwand","sportgebied","spraakgeluid","stemgeluid","stiltegebied","stoelgeld","stormgeweld","straatgeluid","straatgeweld","strafschopgebied","supportersgeweld","taalgebied","tegelpad","tegelwand","tegenbod","tegengeluid","tegengeweld","tegenspoed","tegenwind","televisiegeweld","tussengebied","uitgaansgeweld","uitgeefbeleid","uitgeversverbond","uitgeverswereld","ultrageluid","vaargebied","vagebond","vakantiegeld","veertigurengebed","vegetariërsbond","vingerhoed","vliegtuiggeluid","vluchtelingenbeleid","voetbalgeweld","vogelgeluid","vogelwereld","volksgezondheidsbeleid","voorzieningenaanbod","vormgevingsbeleid","vredesgeluid","vreemdelingenbeleid","vrijdaggebed","vrijgezellenavond","vrijwilligersbeleid","vuurwapengeweld","wapengeweld","waterbergingsgebied","watergebied","watergeweld","werkgelegenheidsbeleid","werkgeversaanbod","werkgeversbond","werkgeversverbond","wetgevingsbeleid","wiegenkind","wijngebied","wintersportgebied","wisselgeld","woestijngebied","zakgeld","zangersbond","zeegebied","zeehavengebied","ziektegeld","zigeunerkind","zigeunerpaard","zondegeld","zorgenkind","zwangerschapsmaand","zwijggeld","agent","afgezant","dirigent","echtgenoot","morgendienst","apologeet","budgetsupermarkt","burgerdienst","changement","dorpsgenoot","huisgenoot","krankzinnigengesticht","muggenbeet","nagerecht","omgevingsportret","politieagent","tijgerkat","tussengerecht","vogelmijt","voorgerecht","wegenwacht","wegenzout","wijkagent","wisselagent","zeegezicht","zorgbudget","aankoopbudget","aardappelgerecht","accountmanagement","achterhoedegevecht","adoptieagent","advertentiebudget","afspiegelingskabinet","agendahedonist","algemenebijstandswet","amandelgeest","ambtenarengerecht","apengezicht","arbeidsgerecht","aspergerobot","aspergeteelt","assetmanagement","baggerboot","baggermarkt","baggeropdracht","baggerproject","baggerschuit","baggervloot","balkanvergeet-mij-niet","barricadegevecht","bijgerecht","boemerangeffect","bouwmanagement","bovengebit","branchegenoot","bridgejournalist","bridgesport","budget","budgetrecht","budgettekort","bugnugget","burgemeestersambt","burgemeesterspost","burgerdocent","burgerplicht","burgerpot","burgerpresident","burgerrecht","burgerschapsrecht","buurtagent","buurtgenoot","capaciteitsmanagement","casemanagement","celgenoot","chef-dirigent","CIA-agent","clubgenoot","coalitiegenoot","collectiemanagement","collegebesluit","collegekaart","collegestudent","competentiemanagement","crisismanagement","defensiebudget","depannagedienst","deskundigenrapport","disgenoot","dopingexpert","draagvleugelboot","dreigement","driekoningenfeest","dubbelagent","dwerggeit","eerstgeboorterecht","eigendomsrecht","elftalgenoot","enkelgewricht","etalageruit","ex-agent","ex-echtgenoot","exploitatiebudget","FBI-agent","fractiegenoot","gadget","garagepoort","glogetuigschrift","groentegerecht","groentenugget","grondgevecht","halfgeleiderfabrikant","halsgerecht","halsgewricht","hamburgerrestaurant","hamburgertent","handelsagent","handgewricht","hanengevecht","hengelsport","hersengadget","heupgewricht","hogeschooldocent","hogeschoolstudent","hokjesgeest","hondengevecht","hoofdagent","hoofdgerecht","horlogekast","hotelmanagement","huishoudbudget","hulpagent","huwelijksvermogensrecht","inburgeringsplicht","inburgeringstraject","informatiemanagement","ingenieursdienst","ingenieursstudent","inlichtingenrapport","interim-management","internetevangelist","investeringsbudget","inzagerecht","jaarbudget","jongerenkrant","jongerenpaspoort","kaakgewricht","kaasgerecht","kaasnugget","kalfsgehakt","kamergenoot","kant-en-klaargerecht","kantongerecht","kennismanagement","kipnugget","klasgenoot","kniegewricht","kogelgewricht","kooigevecht","kredietmanagement","kroegentocht","kruidnagelsigaret","kunstbudget","kunstgeschiedenisdocent","kunstgeschiedenisstudent","kunstmanagement","kussengevecht","kwaliteitsmanagement","kwelgeest","lamsgehakt","langetermijneffect","leeftijdgenoot","leeftijdsgenoot","legercommandant","legerdienst","legerkrant","legerpredikant","legertent","lievelingsgerecht","logeergast","lotgenotencontact","loungerestaurant","low budget","lozingenbesluit","luchtagent","luchtgevecht","lunchgerecht","macrobudget","management","managementfout","melkgeit","mens-erger-je-niet","mergelgrot","milieumanagement","miljoenenbudget","mobiliteitsbudget","moddergevecht","monumentenbudget","morgenlicht","morgenpost","motoragent","muggenbult","narcotica-agent","NAVO-bondgenoot","negerhut","nepagent","nugget","ondergebit","onderwijsbudget","onderwijsmanagement","onderzoeksbudget","onderzoeksgerecht","on-en-minvermogenkaart","ongevallenwet","onteigeningswet","orgelconcert","orgeldocent","orgelkast","overheidsbudget","overheidsmanagement","overnamegevecht","overnemingsgevecht","paardengebit","passagebiljet","pastagerecht","persagent","personeelsbudget","personeelsmanagement","plaggenhut","ploegentijdrit","pluimgewicht","politiebudget","polsgewricht","postzegelformaat","prestigeproject","prins-regent","procesmanagement","productiebudget","projectmanagement","pseudovogelpest","publiciteitsagent","raffinageproduct","reclamebudget","reegeit","regeerambt","regelzucht","regenboogtricot","regenput","regent","regentaat","regenwaterput","regeringsapparaat","regeringsbesluit","regeringsbudget","regeringskrant","regeringsrapport","regeringssoldaat","reisagent","reisbudget","restauratiebudget","rijksbudget","rijstgerecht","risicomanagement","röntgenapparaat","ruggenmergsvocht","rundergehakt","scharniergewricht","scheidsgerecht","schijngevecht","schimmengevecht","schoolagent","schoolbegeleidingsdienst","schoolgenoot","schoolwijkagent","schoudergewricht","sergeant","slangenbeet","slangenhout","slingerplant","slowfoodgerecht","soortgenoot","spiegelgevecht","spiegelkast","spiegelruit","spiegelschrift","spiegeltent","spinazienugget","sportmanagement","spronggewricht","stagedocent","stageopdracht","stagerapport","stierengevecht","straatgevecht","streekgerecht","stressmanagement","studentenbudget","subsidiebudget","taalgenoot","tafelgenoot","tafelgenot","teamgeest","tegenargument","tegeneffect","tegenkracht","tentoonstellingsbudget","tijdgeest","tijdgenoot","tijdmanagement","tijdsgewricht","tijgerpunt","timemanagement","titanengevecht","titelgevecht","topdirigent","topmanagement","totaalbudget","totaalgewicht","tweegevecht","tweevingertest","twintigeurobiljet","undercoveragent","urgentierecht","veiligheidsagent","veiligheidsarrangement","veiligheidsmanagement","vijftigeurobiljet","vingerplant","visgerecht","visnugget","vleesgerecht","vleugelboot","vliegenkast","vliegerfeest","vluchtelingenrecht","vluchtelingentransport","VN-gezant","vogelmarkt","vogeltjesmarkt","vogelvangst","vogelvlucht","volksgericht","voorlichtingsbudget","vrachtwagenfabrikant","vrachtwagenmarkt","vragersmarkt","vredegerecht","vreemdelingenangst","vreemdelingenbesluit","vreemdelingendebat","vreemdelingenrecht","vreemdelingenstemrecht","vuistgevecht","vuurgevecht","watergeest","watergevecht","watermanagement","wegenbouwproject","wereldtitelgevecht","werkgelegenheidseffect","werkgelegenheidsproject","werkingsbudget","wervelgewricht","wetenschapsbudget","wetgevingsproject","wintergerst","wintergezicht","wrevelagent","zadelgewricht","zagevent","zanger-componist","zanger-gitarist","zangerscast","zangvogelsport","zeegevecht","zegelrecht","zegetocht","zelfmanagement","ziekenhuisbudget","zwangerschapstest","goedgevuld","aangebrand","welgevuld","afgeborsteld","donkergekleurd","goedgevormd","welgevormd","allesverzengend","bontgekleurd","doorgewinterd","goedgehumeurd","goedgeluimd","goedgezind","haatdragend","kegeldragend","lichtgekleurd","nagelbijtend","ongekleurd","ongemanierd","ongeverfd","rentedragend","risicodragend","roodgekleurd","slechtgehumeurd","slechtgezind","vruchtdragend","welgemanierd","welgezind","welopgevoed","woldragend","zaaddragend","zorgdragend","aanbodgestuurd","aangehuwd","aangetekend","aangetrouwd","aanliggend","aanmatigend","aanvoegend","achtereenvolgend","achterliggend","afgewend","allesdoordringend","allesvernietigend","alleszeggend","almogend","alvermogend","angstaanjagend","bijstandsgerechtigd","bloeddrukverhogend","bloeddrukverlagend","bloemdragend","braakliggend","brandvertragend","breedgerand","brildragend","cholesterolverlagend","christelijk-gereformeerd","computergestuurd","diepliggend","doodgemoedereerd","doordringend","doorslaggevend","dreigend","drempelverlagend","dringend","dwingend","eerstvolgend","eierleggend","Engelssprekend","ergerniswekkend","felgekleurd","godtergend","goedgekleed","goedgemanierd","goudgerand","grensverleggend","handenwringend","hemeltergend","hiernavolgend","hogergenoemd","hoogdringend","hoopgevend","indringend","ingebeeld","ingekankerd","ingekeerd","ingenaaid","ingewikkeld","ingeworteld","intrigerend","knoldragend","kogelwerend","laaggeletterd","leidinggevend","levensbedreigend","levensbeëindigend","levensverlengend","lichtgevend","lichtgewond","liggend","losliggend","maatgevend","meedogend","minvermogend","moedgevend","naastliggend","navolgend","neerbuigend","niet-geleidend","nietszeggend","normgevend","oergezond","omliggend","onaangediend","onbevredigend","ondergewaardeerd","onderliggend","ondeugend","ongeaccepteerd","ongeanimeerd","ongearticuleerd","ongeautoriseerd","ongecensureerd","ongeciviliseerd","ongeclausuleerd","ongecompliceerd","ongeconcentreerd","ongeconditioneerd","ongecontroleerd","ongecoördineerd","ongecorrigeerd","ongecultiveerd","ongedateerd","ongedefinieerd","ongedifferentieerd","ongediplomeerd","ongedisciplineerd","ongedoubleerd","ongeëmancipeerd","ongeëmotioneerd","ongeforceerd","ongefrankeerd","ongefundeerd","ongegeneerd","ongehavend","ongehonoreerd","ongeïdentificeerd","ongeïnformeerd","ongeïnspireerd","ongeïnteresseerd","ongekend","ongekwalificeerd","ongeleerd","ongelimiteerd","ongelinieerd","ongematteerd","ongemeend","ongemeubileerd","ongemonteerd","ongemotiveerd","ongemotoriseerd","ongenuanceerd","ongeoefend","ongeopend","ongeordend","ongeorganiseerd","ongepaneerd","ongepermitteerd","ongeprepareerd","ongepubliceerd","ongeraffineerd","ongerealiseerd","ongeregistreerd","ongereglementeerd","ongereguleerd","ongesigneerd","ongespecificeerd","ongestoffeerd","ongestructureerd","ongestudeerd","ongesubsidieerd","ongevaccineerd","ongewapend","onsamenhangend","onuitgenodigd","onuitgevoerd","onvermogend","onwelgezind","opeenvolgend","opvliegend","opvolgend","orthodox-gereformeerd","overtuigend","overwegend","overweldigend","plaatsvervangend","prangend","raadgevend","redengevend","rentegevend","rolbevestigend","roodgeverfd","rustgevend","samenhangend","schermdragend","schrikaanjagend","slechtgekleed","sneldrogend","statusverhogend","stilzwijgend","supergezond","tegemoetkomend","tergend","toegevend","toonaangevend","tussenliggend","uitdagend","uitgekiend","uitgeregend","uitgerekend","uitnodigend","vakoverstijgend","veelzeggend","vigerend","vleesvervangend","vliegend","volgend","voorbijgestreefd","vraaggestuurd","vreesaanjagend","Wajonggerechtigd","waterbergend","watergekoeld","welgekend","welgemeend","werkgelegenheidsbevorderend","wetgevend","winstgevend","witgehandschoend","witgepleisterd","witgeschilderd","witgeverfd","zelfcorrigerend","zelfdragend","zelfreinigend","zelfvernietigend","zelfverzorgend","zieltogend","zingevend","zoetgeurend","zogenaamd","zogenoemd","zwaargehavend","zwaargewapend","zwaargewond","zwaarwegend","zwartgeverfd","zwijgend","doelgericht","ontwikkelingsgericht","zwartgerokt","arbeidsmarktgericht","functiegericht","goedgemutst","kindgericht","aanbodgericht","aangedampt","actiegericht","arbeidsongeschikt","brongericht","buurtgericht","cliëntgericht","competentiegericht","consumentgericht","divergent","doelgroepgericht","doodongerust","effectgericht","ervaringsgericht","exportgericht","groepsgericht","ingemaakt","ingeroest","innovatiegericht","intelligent","klantgericht","kortgerokt","maatschappijgericht","marktgericht","mensgericht","nagelvast","natuurgericht","niet-gericht","ongekuist","ongericht","onuitgebracht","onuitgepakt","onuitgewerkt","oplossingsgericht","persoonsgericht","praktijkgericht","prestatiegericht","probleemgericht","procesgericht","productgericht","publieksgericht","resultaatgericht","roodgelakt","taakgericht","themagericht","toekomstgericht","toepassingsgericht","vakgericht","voortgezet","vraaggericht","wijkgericht","witgekalkt","witgelakt","zelfgemaakt","zwartgelakt","morgennacht","negenduizend","negenentwintigduizend","negenhonderd","negenhonderdduizend","negentienduizend","negentienhonderd","negentigduizend","morgenochtend","desgevallend","morgenavond","zogezegd","nergensland","ontbijtbord","onthaalbeleid","onthaalkind","ontmoedigingsbeleid","ontmoetingsavond","ontwapeningsakkoord","ontwerpakkoord","ontwerplandbouwakkoord","ontwerpwedstrijd","ontwikkelingsbeleid","ontwikkelingshulpbeleid","ontwikkelingsland","ontbeend","ontbrekend","onthullend","onthutsend","ontkennend","ontluisterend","ontoereikend","ontslagnemend","ontsmettend","ontspannend","ontstekingsremmend","ontstellend","ontwapenend","ontwijkend","ontwikkeld","ontzagwekkend","ontzettend","ontbijt","onthardingszout","ontzet","ontbijtbuffet","ontbindingsrecht","ontdekkingstocht","onterecht","ontkoppelingsbesluit","ontmijningsdienst","ontslagbesluit","ontslagdecreet","ontslagrecht","ontvangst","ontwerpbesluit","ontwerpfout","ontwerpgrondwet","ontwerpopdracht","ontwerprapport","ontwerpwet","ontwikkelingspot","ontwikkelingsproject","herdershond","herenakkoord","herenblad","herfstavond","herfstblad","herfstdraad","herfstmaand","herfstochtend","herfstwind","herkeuringsraad","heroïnehond","herseninhoud","herstelbeleid","hervormingsbeleid","herfst","hermafrodiet","hert","heraut","herfstlucht","heraanplant","herdenkingsbijeenkomst","herdenkingsconcert","herdenkingsfeest","heremietkreeft","herfstnacht","herfsttint","herinneringskunst","herkomst","heroïnespuit","heroïnetransport","heroïnevangst","herroepingsrecht","hersenkracht","hersenvlucht","hersenvocht","hersteldienst","herstelrecht","hervormingsproject","erwt","ernst","erbovenuit","ereambt","eregast","erepunt","erfenisrecht","erfrecht","ergotherapeut","ernaast","eronderuit","eropuit","ertussenuit","eruit","ervanuit","erytrocyt","eredivisiewedstrijd","erelid","erfgoedbeleid","erkenningsbeleid","errond","ervaringswereld","gebaart","gebeurt","gebiedt","gebood","gedenkt","gedraagt","geeuwt","gehoorzamt","geilt","geldt","geelt","gelooft","geneest","geniet","genoot","gerust","geurt","geeft","besnuffelt","bedeelt","bedelt","bekeert","beugelt","beamt","beantwoordt","beargumenteert","beatblogt","becijfert","becommentariërt","beconcurreert","bedaart","bedelft","bedenkt","bederft","bedient","bediscussiërt","bedoelt","bedraagt","bedreigt","bedriegt","bedrijft","bedroeft","bedwingt","beëindigt","beeldbelt","beetneemt","beft","begaat","begeleidt","begeert","begeeft","begint","begraaft","begrijpt","begroeit","behaalt","behandelt","behangt","beheert","behoedt","behoeft","behoort","behoudt","beïnvloedt","bekent","bekeurt","bekijkt","beklaagt","bekleedt","beklemt","beklimt","bekomt","bekritiseert","bekroont","belandt","beledigt","belegt","belemmert","beleeft","belt","beloont","belooft","belparkeert","beluistert","bemeesteert","bemeubelt","bemoedigt","bemoeit","benadert","benauwt","beneemt","bengelt","benieuwt","benoemt","beogt","beoordeelt","bepaalt","bepoteelt","bereidt","berekent","berooft","beschaamt","beschaaft","beschermt","beschildert","beschouwt","beschrijft","beschuldigt","beslaat","besloot","besnijdt","bespaart","bespeurt","bespioneert","bespreekt","bespringt","bestaat","bestempelt","bestrijdt","bestreed","bestudeert","bestuurt","beswaffeelt","betekent","betert","betont","betonneert","betovert","betreedt","betreft","betrekt","betreurt","betwijfelt","beult","bevalt","beeft","bevindt","bevoordeliigt","bevordert","bevraagt","bevriest","bewapent","beweert","bewijst","bewondert","bewoont","bewonersparkeert","bezaait","bezeert","beziet","bezat","bezoekt","bezorgt","bezuinigt","bezweert","verlaat","verliet","verschaalt","verspringt","vertelt","veraangenaamt","verabsoluteert","verachtvoudiigt","veradeemt","verafgoodt","verafschuwt","veralgemeent","verandert","verankert","verantwoordt","verarmt","verbabbelt","verbaliseert","verbant","verbaast","verbeeldt","verbeidt","verbergt","verbetert","verbeuzelt","verbiedt","verbood","verbijstert","verbindt","verblijft","verblindt","verbouwt","verbrandt","verbreekt","verdappert","verdedigt","verdeelt","verdenkt","verdient","verdort","verdooft","verdraait","verdraagt","verdrijft","verdringt","verdrinkt","verdroogt","verdubbelt","verdwaalt","verdwijnt","vereenvoudigt","vet","verenigt","vereert","vergaat","vergadeert","vergelijkt","vergt","vergeet","vergat","vergeeft","vergiftigt","vergoedt","vergrendelt","verhaalt","verhangt","verheldert","verheugt","verhindert","verhoogt","verhongert","verhoudt","verhuist","verhuurt","verifiërt","verjaagt","verkent","verkeert","verkiest","verklaart","verkleedt","verkleint","verkleurt","verknoeit","verkoopt","verkreukelt","verkrijgt","verlaagt","verlamt","verlangt","verleidt","verleent","verlengt","verliest","verloocheent","verloopt","verlooft","verluiert","verlummelt","vermagert","vermaalt","vermangelt","vermeldt","vermengt","vermenigvuldigt","vermijdt","vermindert","vermoedt","vermoeit","vermolmt","vermomt","vermoordt","vernauwt","verneemt","vernevelt","vernielt","vernietigt","vernieuwt","vernikkelt","vernoemt","vernummert","veronaangenaamt","veronachtzaamt","veronderstelt","verontheiliigt","verontreinigt","verontschuldigt","veroordeelt","veroorlooft","verootmoediigt","veropenbaart","verordonneert","verovert","verpandt","verpaupert","verpietert","verplegt","verplettert","verpulvert","verraadt","verried","verrechtvaardiigt","verregeent","verreist","verrekeent","verrijdt","verrijst","verroert","verrolt","verronselt","verruigt","verruilt","verruuwt","verscheurt","verschijnt","verschilt","verschimmelt","verschoont","verschraalt","verschrijft","verschroeit","verschrompelt","verschuilt","versiert","versimpelt","versjachert","versjouwt","verslaat","verslechtert","versleutelt","verslijt","versleet","verslindt","verslond","versluiert","versluist","versmaadt","versmalt","versmoort","versnelt","versnijdt","versnippert","versobert","versoepelt","versombert","verspeelt","verspeent","verspert","verspiedt","verspilt","verspint","versplintert","verspreidt","verstaat","verstond","verstaalt","verstart","verstelt","versteent","versterft","versteviigt","verstijft","verstilt","verstomt","verstoort","verstoot","verstiet","verstouwt","verstramt","verstrengt","verstrijkt","verstrooit","verstuift","verstuurt","verstuuwt","versuikert","versukkelt","vertaalt","vertedert","vertegenwoordigt","vertekeent","verteert","vertienvoudiigt","vertilt","vertimmert","vertint","vertoeft","vertoont","vertoornt","vertraagt","vertreedt","vertroebelt","vertroetelt","vertrouwt","vertwijfelt","vervaagt","vervaalt","vervalt","vervangt","vervelt","verft","verveent","verviervoudiigt","vervijfvoudiigt","vervliegt","vervloeit","vervluchtiigt","vervoedeert","vervoegt","vervoert","vervolgt","vervollediigt","vervordert","vervormt","vervreemdt","vervroegt","vervuilt","vervult","verwaait","verwaardiigt","verwaarloost","verwarmt","verwart","verwaseemt","verwatert","verwedt","verwelkoomt","verweert","verwerpt","verwerft","verweeft","verwijdt","verwijdert","verwijlt","verwijft","verwikkelt","verwildert","verwint","verwintert","verwisselt","verwittiigt","verwondt","verwondert","verwoont","verwoordt","verwringt","verwurgt","verzaagt","verzandt","verzegelt","verzegt","verzeilt","verzekert","verzelfstandiigt","verzendt","verzengt","verzesvoudiigt","verzilvert","verzinnebeeldt","verzint","verzoekt","verzoent","verzoolt","verzuilt","verzuurt","verzusteert","verzwagert","verzwaart","verzwelgt","verzwendelt","verzweert","verzwijgt","ontbiedt","ontbood","ontbeet","ontbindt","ontbolstert","ontbraamt","ontbreekt","ontcijfert","ontdoet","ontdeed","ontdooit","ontdubbelt","onteert","onterft","ontgaat","ontgeldt","ontglijdt","ontgloeit","ontgraaft","ontgrendelt","ontgroeit","ontgroent","onthaalt","onthalst","onthardt","onthaart","ontheft","ontheiligt","onthoofdt","onthoudt","onthield","onthult","ontkent","ontketeent","ontkiemt","ontkleurt","ontkoomt","ontkoppelt","ontlaadt","ontleent","ontleert","ontloopt","ontluist","ontmengt","ontmijnt","ontmoedigt","ontmythologiseert","ontneemt","ontradicaliseert","ontroert","ontrommeelt","ontruimt","ontslaat","ontspant","ontspult","ontstaat","ontstond","ontsteekt","ontvangt","ontvoert","ontvolgt","ontvoogdt","ontvriendt","ontvriest","ontwerpt","ontwijkt","ontwikkelt","ontzwavelt","herdenkt","herdacht","ergert","ekent","eruitzit","ervaart","erft"],X=["aaneengedraaid","aaneengeschakeld","aanschouwd","aanvaard","achtergebleven","achtergelaten","achterhaald","achteromgekeken","achteropgekomen","achteruitgegaan","achtervolgd","ademgehaald","bedolven","bedongen","bedorven","bedragen","bedreven","bedrogen","bedropen","bedwongen","beetgenomen","begeven","begonnen","begraven","begrepen","behangen","behouden","bekeken","beklommen","bekomen","bekropen","beleden","belezen","benomen","beraden","beschenen","beschoten","beschreven","beslagen","beslopen","besloten","besneden","besproken","besprongen","bestegen","bestolen","bestorven","bestreden","bestreken","betreden","betroffen","betrokken","bevallen","bevochten","bevolen","bevonden","bevroren","bewezen","bewogen","bezeten","bezien","beziggehouden","bezonnen","bezweken","bezworen","bijeengehouden","bijeengeroepen","blootgelegd","blootgesteld","bovengehaald","brandgesticht","buitengesloten","buitgemaakt","deelgenomen","dichtgebonden","dichtgedaan","diepgevroren","doodgegaan","doorbladerd","doorboord","doorbroken","doordacht","doordrongen","doorgrond","doorkruist","doorlopen","doorsneden","doorstaan","doorverteld","doorzien","doorzocht","drooggelegd","dwarsgezeten","ervaren","flauwgevallen","gebakken","gebannen","gebarsten","gebeden","gebersten","gebeten","geblazen","gebleken","gebleven","geblonken","geboden","gebogen","gebonden","geboren","geborgen","geborsten","gebraden","gebroken","gebrouwen","gedaan","gedoken","gedolven","gedongen","gedragen","gedreten","gedreven","gedrongen","gedronken","gedropen","gedwongen","gefloten","gegeten","gegeven","gegleden","geglommen","gegolden","gegoten","gegraven","gegrepen","gehangen","gehesen","geheven","geholpen","gehouden","gehouwen","gekeken","geklommen","geklonken","gekloven","geknepen","gekomen","gekorven","gekozen","gekregen","gekresen","gekreten","gekrompen","gekrooien","gekropen","gekunnen","gekweten","gelachen","geladen","gelaten","geleden","gelegen","geleken","gelezen","gelogen","geloken","gelopen","gemalen","gemeden","gemeten","gemoeten","gemogen","gemolken","genegen","genezen","genomen","genoten","geprezen","geraden","gereden","geregen","gereten","gerezen","geroepen","geroken","geschapen","gescheiden","geschenen","gescheten","gescholden","gescholen","geschonden","geschonken","geschoren","geschoten","geschoven","geschreden","geschreven","geschrokken","geslagen","geslapen","geslepen","gesleten","geslonken","geslopen","gesloten","gesmeten","gesmolten","gesneden","gesnoten","gesnoven","gespannen","gespeten","gespleten","gesponnen","gespoten","gesproken","gesprongen","gesproten","gestegen","gestoken","gestolen","gestonken","gestoten","gestoven","gestreden","gestreken","getreden","getroffen","getrokken","gevallen","gevangen","gevangengenomen","gevaren","gevezen","gevlochten","gevloden","gevlogen","gevloten","gevochten","gevonden","gevouwen","gevreten","gevroren","gewassen","geweken","geweten","geweven","gewezen","gewogen","gewonden","gewonnen","geworden","geworpen","geworven","gewoven","gewreten","gewreven","gewrongen","gezegen","gezeken","gezeten","gezien","gezoden","gezogen","gezonden","gezongen","gezonken","gezonnen","gezopen","gezouten","gezwegen","gezwolgen","gezwollen","gezwommen","gezwonden","gezworen","gezworven","hardgelopen","herladen","hernomen","herwonnen","herzien","huisgehouden","kennisgemaakt","klaargekomen","kortgesloten","kwaadgesproken","kwijtgeraakt","kwijtgescholden","langsgekomen","leeggelopen","leeggemaakt","lesgegeven","liefgehad","lipgelezen","meebetaald","misbruikt","misleid","mislukt","misprezen","nabewerkt","nedergedaald","omarmd","omfloerst","omhelsd","omkleed","omklemd","ommuurd","omringd","omschreven","omsingeld","omsloten","omvat","omvergeworpen","omwikkeld","omwonden","omzeild","omzoomd","omzworven","onderbouwd","onderbroken","onderdrukt","ondergaan","ondergraven","onderhandeld","onderhouden","onderkend","ondermijnd","ondernomen","onderscheiden","onderschept","ondersteund","onderstreept","ondertekend","onderverdeeld","ondervonden","ondervraagd","onderwezen","onderworpen","onderzocht","ontbeten","ontboden","ontbonden","ontbroken","ontdoken","ontgonnen","onthouden","ontkomen","ontladen","ontloken","ontlopen","ontnomen","ontraden","ontslagen","ontsloten","ontspannen","ontsprongen","ontsproten","ontstoken","onttrokken","ontvangen","ontweken","schoongemaakt","schoongewassen","stilgestaan","tandengepoetst","tegemoetgekomen","teleurgesteld","teloorgegaan","terechtgekomen","terechtgesteld","teweeggebracht","thuisbezorgd","thuisgekomen","toebehoord","toevertrouwd","tussengekomen","tussengeworpen","uitbesteed","uitbetaald","uitvergroot","uitverkocht","valsgespeeld","verbannen","verbleven","verboden","verbogen","verbonden","verborgen","verbroken","verdragen","verdreven","verdrongen","verdronken","verdroten","verdwenen","vergeleken","vergeten","vergeven","vergleden","vergolden","vergoten","vergrepen","verhangen","verheven","verholpen","verhouden","verkozen","verkregen","verladen","verlaten","verlopen","verloren","vermeden","vermogen","vernomen","verraden","verrezen","verscheiden","verschenen","verscholen","verschoten","verschoven","verschreven","verschrokken","verslagen","verslapen","versleten","verslonden","versmolten","verstoten","verstreken","vertrokken","vervallen","vervangen","vervlogen","verweten","verweven","verwezen","verworpen","verworven","verwrongen","verzonden","verzonken","verzonnen","verzopen","verzouten","verzwonden","volbracht","voldaan","voleindigd","volhard","volmaakt","volstaan","voltooid","voltrokken","voorbehouden","voorkomen","voorspeld","voorzien","wederhaald","weergalmd","weerhouden","weerkaatst","weerlegd","weerstaan"],{getWords:Z,matchRegularParticiples:ee}=n.languageProcessing,{directPrecedenceException:re,values:ne}=n.languageProcessing,{Clause:te}=ne,{getClausesSplitOnStopWords:oe,createRegexFromArray:de}=n.languageProcessing,ae={Clause:class extends te{constructor(e,r){super(e,r),this._participles=function(e){const r=Z(e),n=[/^(ge|be|ont|ver|her|er)\S+([dt])($|[ \n\r\t.,'()"+\-;!?:/»«‹›<>])/gi,/^(aan|af|bij|binnen|los|mee|na|neer|om|onder|samen|terug|tegen|toe|uit|vast)(ge)\S+([dtn])($|[ \n\r\t.,'()"+\-;!?:/»«‹›<>])/gi];return r.filter((e=>0!==ee(e,n).length||(0,K.includes)(X,e)))}(this.getClauseText()),this.checkParticiples()}checkParticiples(){const e=this.getParticiples().filter((e=>!(0,K.includes)(Q,e)&&!this.hasNonParticipleEnding(e)&&!re(this.getClauseText(),e,q)));this.setPassive(e.length>0)}hasNonParticipleEnding(e){return/\S+(heid|teit|tijd)($|[ \n\r\t.,'()"+\-;!?:/»«‹›<>])/gi.test(e)}},regexes:{auxiliaryRegex:de(["word","wordt","worden","werd","werden","wordend"]),stopwordRegex:de(G)}};function le(e){return oe(e,ae)}const{exceptionListHelpers:{checkIfWordEndingIsOnExceptionList:se,checkIfWordIsOnListThatCanHavePrefix:ie}}=n.languageProcessing;function ge(e,r){const n=r.find((r=>-1!==e.search(new RegExp(r[0]))));return void 0!==n&&(e=e.replace(new RegExp(n[0]),n[1])),e}function be(e,r,n){const t=ie(e,r.getVowelDoubling,n),o=function(e,r,n){if(se(e,r.endingMatch)||ie(e,r.verbs,n)||r.exactMatch.includes(e))return!0}(e,r.noVowelDoubling,n),d=function(e){return e.charAt(e.length-4)!==e.charAt(e.length-3)}(e),a=function(e,r){return-1===e.search(new RegExp(r))}(e,r.noVowelDoubling.rule);return t||!o&&d&&a}const ve=function(e){let r=e.search(/[aeiouyèäüëïöáéíóú][^aeiouyèäüëïöáéíóú]/);return-1!==r&&(r+=2),-1!==r&&r<3&&(r=3),r},ue=function(e,r,n,t){const o=function(e,r,n){const t=Object.entries(r);for(const r of t){const t=r[1].suffixes.find((r=>new RegExp(r).exec(e)));if(t){const o=new RegExp(t).exec(e),d=o[o.length-1],a=e.lastIndexOf(d);if(-1!==n&&a>=n)return{suffixIndex:a,stemModification:r[1].stemModification}}}}(e,r,n);return void 0!==o&&(e=function(e,r,n,t,o){return"hedenToHeid"===t?ge(e,o.regularStemmer.stemModifications.hedenToHeid):(e=e.substring(0,n),"changeIedtoId"===t?ge(e,o.regularStemmer.stemModifications.iedToId):"changeInktoIng"===t&&e.endsWith("ink")?ge(e,o.regularStemmer.stemModifications.inkToIng):"vowelDoubling"===t&&be(e,o.regularStemmer.stemModifications.exceptionsStemModifications,o.pastParticipleStemmer.compoundVerbsPrefixes)?ge(e,o.regularStemmer.stemModifications.doubleVowel):e)}(e,0,o.suffixIndex,o.stemModification,t)),e},ce=function(e,r,n,t){const o=Object.entries(r);for(const r of o)e=ue(e,r[1],n,t);return e},{regexHelpers:{searchAndReplaceWithRegex:he,doesWordMatchRegex:me},exceptionListHelpers:{checkIfWordEndingIsOnExceptionList:we,checkIfWordIsOnListThatCanHavePrefix:ke}}=n.languageProcessing,pe=function(e,r,n){if(me(n,r[0])){const t=n.replace(new RegExp(r[0]),r[1]);if(be(t,e.regularStemmer.stemModifications.exceptionsStemModifications,e.pastParticipleStemmer.compoundVerbsPrefixes)){return he(t,e.regularStemmer.stemModifications.doubleVowel)||t}return t}return null},fe=function(e,r){const n=r.ambiguousTAndDEndings.tOrDArePartOfStem;let t=he(e,n.firstTOrDPartOfStem);if(t)return t;if(n.verbsDenShouldBeStemmed.includes(e))return e.slice(0,-3);if(we(e,n.wordsStemOnlyEnEnding.endingMatch)||ke(e,n.wordsStemOnlyEnEnding.verbs,r.pastParticipleStemmer.compoundVerbsPrefixes)||me(e,n.denEnding)){if(t=e.slice(0,-2),be(t,r.regularStemmer.stemModifications.exceptionsStemModifications,r.pastParticipleStemmer.compoundVerbsPrefixes)){return he(t,r.regularStemmer.stemModifications.doubleVowel)||t}return t}const o=n.deEnding;if(t=pe(r,o,e),t)return t;const d=n.teAndTenEndings;return t=pe(r,d,e),t||null};function ye(e,r){if(we(r,e.ambiguousTAndDEndings.wordsTShouldBeStemmed))return r.slice(0,-1);if(me(r,e.ambiguousTAndDEndings.tOrDArePartOfStem.tEnding))return r;return fe(r,e)||null}const{flattenSortLength:je}=n.languageProcessing,ze=function(e,r,n){const t=je(n).find((r=>e.startsWith(r)));"string"==typeof t&&(e=e.slice(t.length));for(let n=0;n<r.length;n++){const o=(0,K.flatten)(r[n]);for(let r=0;r<o.length;r++)if(o.includes(e))return"string"==typeof t?t+o[0]:o[0]}return null},xe=function(e,r){for(let n=0;n<r.length;n++){const t=(0,K.flatten)(r[n]);for(let r=0;r<t.length;r++)if(e.endsWith(t[r])){const n=e.slice(0,-t[r].length);return 1===n.length?null:n.length>1?n+t[0]:t[0]}}return null},Se=function(e,r){for(let n=0;n<r.length;n++){const t=(0,K.flatten)(r[n]);for(let r=0;r<t.length;r++)if(t.includes(e))return t[0]}return null};function Pe(e,r){const n=e.stemExceptions.stemmingExceptionStemsWithFullForms;let t=ze(r,n.verbs,e.pastParticipleStemmer.compoundVerbsPrefixes);return t||(t=xe(r,n.endingMatch),t||(t=Se(r,n.exactMatch),t||null))}const{regexHelpers:{doesWordMatchRegex:Ee}}=n.languageProcessing,Fe=function(e,r){return e.includes(r)?r.slice(0,-1):null},Be=function(e,r){return e.endsWith("t")?!!r.ambiguousTAndDEndings.wordsTShouldBeStemmed.includes(e)||!Ee(e,r.ambiguousTAndDEndings.tOrDArePartOfStem.tEnding)&&!r.stemExceptions.wordsNotToBeStemmedExceptions.verbs.includes(e):!r.pastParticipleStemmer.doNotStemD.includes(e)},Me=function(e,r){if(new RegExp("^"+e.pastParticipleStemmer.participleStemmingClasses[0].regex).test(r)){const n=Fe(e.pastParticipleStemmer.doNotStemGe,r);if(n)return n;let t=r.slice(2);return t.startsWith("ë")&&(t="e"+t.slice(1)),Be(t,e)?t.slice(0,-1):t}return null},We=function(e,r,n,t,o){for(const d of t)if(new RegExp("^"+d+o).test(r)){let t=r.slice(d.length-r.length);if(n){const r=Fe(e.pastParticipleStemmer.doNotStemGe,t);if(r)return d+r;t=t.slice(2)}return t.startsWith("ë")&&(t="e"+t.slice(1)),Be(t,e)?d+t.slice(0,-1):d+t}return null},Oe=function(e,r){for(const n of e.pastParticipleStemmer.participleStemmingClasses){const t=n.regex,o=n.separable,d=o?e.pastParticipleStemmer.compoundVerbsPrefixes.separable:e.pastParticipleStemmer.compoundVerbsPrefixes.inseparable,a=We(e,r,o,d,t);if(a)return a}return null},Te=function(e,r){return e.includes(r)},Ie=function(e,r,n,t){return e.map((e=>t.startsWith(e))).some((e=>!0===e))&&t.endsWith("end")&&!r.includes(t)?ge(t.slice(0,-3),n):null};function Ve(e,r){if(r.endsWith("heid")||r.endsWith("teit")||r.endsWith("tijd")||Q.includes(r))return"";if(Te(e.pastParticipleStemmer.inseparableCompoundVerbsNotToBeStemmed,r))return r;let n=Me(e,r);return n||(n=Fe(e.pastParticipleStemmer.inseparableCompoundVerbs,r),n||(n=Ie(e.pastParticipleStemmer.compoundVerbsPrefixes.inseparable,e.pastParticipleStemmer.pastParticiplesEndingOnEnd,e.regularStemmer.stemModifications.finalChanges,r),n||(n=Oe(e,r),n||null)))}const{exceptionListHelpers:{checkIfWordEndingIsOnExceptionList:Ae,checkIfWordIsOnListThatCanHavePrefix:De},stemHelpers:{removeSuffixFromFullForm:Ce,removeSuffixesFromFullForm:Re}}=n.languageProcessing,Ne=function(e,r){let n=function(e,r){for(const n of e.stemExceptions.removeSuffixesFromFullForms){const e=Re(n.forms,n.suffixes,r);if(e)return e}for(const n of e.stemExceptions.removeSuffixFromFullForms){const e=Ce(n.forms,n.suffix,r);if(e)return e}}(r,e);return n?be(n,r.regularStemmer.stemModifications.exceptionsStemModifications,r.pastParticipleStemmer.compoundVerbsPrefixes)?(n=ge(n,r.regularStemmer.stemModifications.doubleVowel),ge(n,r.regularStemmer.stemModifications.finalChanges)):ge(n,r.regularStemmer.stemModifications.finalChanges):null};const{exceptionListHelpers:{checkIfWordEndingIsOnExceptionList:Le,checkIfWordIsOnListThatCanHavePrefix:He}}=n.languageProcessing,$e=function(e,r,n){const t=e.stemExceptions.wordsNotToBeStemmedExceptions,o=e.stemExceptions.removeSuffixesFromFullForms[1].forms,d=e.ambiguousTAndDEndings.tOrDArePartOfStem.doNotStemTOrD;if(Ve(e,n)||ye(e,n)||He(n,t.verbs,e.pastParticipleStemmer.compoundVerbsPrefixes)||Le(n,t.endingMatch)||t.exactMatch.includes(n)||o.includes(r)||Pe(e,n)||r.endsWith("heid")||Le(r,d))return!0};function qe(e,r,n){return $e(e,r,n)?null:r.slice(0,-1)}const{flattenSortLength:_e,exceptionListHelpers:{checkExceptionListWithTwoStems:Ge}}=n.languageProcessing,Je=function(e,r){for(const n of Object.keys(e))for(const t of e[n]){const e=(0,K.flatten)(Object.values(t));if(e.includes(r))return e[0]}},{baseStemmer:Ue}=n.languageProcessing;function Ye(e){const r=(0,K.get)(e.getData("morphology"),"nl",!1);return r?e=>function(e,r){const n=function(e,r){let n=Pe(r,e);if(n)return n;if(n=Ve(r,e),n)return n;const t=r.stemExceptions.wordsNotToBeStemmedExceptions;if(De(e,t.verbs,r.pastParticipleStemmer.compoundVerbsPrefixes)||Ae(e,t.endingMatch)||t.exactMatch.includes(e))return e;const o=r.ambiguousTAndDEndings.otherTAndDEndings;for(const t of o)if(e.endsWith(t)&&(n=ye(r,e),n))return n;return n=Ne(e,r),n||function(e,r){e=ge(e,r.regularStemmer.stemModifications.IAndYToUppercase);const n=ve(e),t=r.regularStemmer.suffixes;return ge(e=ce(e,t,n,r),r.regularStemmer.stemModifications.finalChanges)}(e,r)}(e,r);let t=Ge(r.stemExceptions.stemmingExceptionsWithMultipleStems.stemmingExceptionsWithTwoStems,n);if(t)return t;if(t=function(e,r){let n=_e(e.pastParticipleStemmer.compoundVerbsPrefixes).find((e=>r.startsWith(e))),t="";e.stemExceptions.stemmingExceptionsWithMultipleStems.strongAndIrregularVerbs.doNotStemPrefix.find((e=>r.endsWith(e)))?n=null:n&&(t=r.slice(n.length,r.length),t.length>2?r=t:n=null);const o=e.stemExceptions.stemmingExceptionsWithMultipleStems.strongAndIrregularVerbs.strongVerbStems,d=[o.irregularStrongVerbs,o.regularStrongVerbs,o.bothRegularAndIrregularStrongVerbs];for(let e=0;e<d.length;e++)if(Je(d[e],r))return n?n+Je(d[e],r):Je(d[e],r)}(r,n),t)return t;const o=r.ambiguousTAndDEndings.tAndDEndings;for(const t of o)if(n.endsWith(t)){const t=qe(r,n,e);if(t)return t}return n}(e,r):Ue}const{formatNumber:Ke}=n.helpers;function Qe(e){const r=206.84-.77*e.syllablesPer100Words-.93*e.averageWordsPerSentence;return Ke(r)}const{AbstractResearcher:Xe}=n.languageProcessing;class Ze extends Xe{constructor(e){super(e),Object.assign(this.config,{language:"nl",passiveConstructionType:"periphrastic",firstWordExceptions:t,functionWords:_,stopWords:G,transitionWords:d,twoPartTransitionWords:J,syllables:U,keyphraseLength:Y}),Object.assign(this.helpers,{getClauses:le,getStemmer:Ye,fleschReadingScore:Qe})}}(window.yoast=window.yoast||{}).Researcher=r})();