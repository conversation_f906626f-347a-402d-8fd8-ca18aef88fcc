(()=>{"use strict";var i={d:(e,t)=>{for(var n in t)i.o(t,n)&&!i.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},o:(i,e)=>Object.prototype.hasOwnProperty.call(i,e),r:i=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(i,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(i,"__esModule",{value:!0})}},e={};i.r(e),i.d(e,{default:()=>h});const t=window.yoast.analysis,n=["ten","nula","jeden","jedné","jedna","jedno","dva","dvě","dvou","tři","čtyři","pět","šest","sedm","osm","devět","deset","sto","tisíc","tento","ta","tato","to","toto","ti","tito","kdo","co"],s=["protože","když","sbohem","sotva","kdo","co","kde","odkud","kdy","odkdy","ačkoli","navzdory","ačkoli","když","kde","aby","pořádku","kdyby","jak","do","že","jako","přesně","jako","než","aby","kdo","kde","kdo","koho","kde","kolik","odkud","proč","kolik","nebo"],a=["předtím","vždyť","definitely","konečně","jasné","možné","ale","demzufolge","však","ačkoliv","protože","ovšem","zkrátka","potom","stejně","tím","jinak","zatímco","když","co","kdežto","ačkoli","přestože","čas","chvíle","chvilka","avšak","jenže","nicméně","přitom","aniž","a","proto","tedy","teda","totiž","mimoto","čímž","což","než","nejenže","také","jenom","přesto","jak","jelikož","takže","zda","sice","tudíž","jakoby","nýbrž","neboli","jen","čili","pak","jenomže","kdežto","leč","poněvadž","třeba","přece","nežli","zdali","buďto","totiž","jenom","leda","pakliže","třebaže","jakože","jakkoli","nechť","sotva","kterak","sic","jakkoliv","ledaže","ježto","třebas","jakž","pakli","zdalipak","takž","jakže","pokavaď","jakby","pokudž","sotvaže","pokad","kdyžtě","mezitímco","buďsi","byťsi","pokadž","tedyť","buďže","dle","vzhledem","místo","vedle","okolo","uprostřed","namísto","navzdory","krom","poblíž","blízko","nedaleko","začátkem","naproti","počátkem","počínaje","postupem","vlivem","vyjma","následkem","dík","zpoza","zásluhou","nevyjímaje","doprostřed","zpod","zespoda","závěrem","úvodem","přese","prostřed","nepočítaje","úměrně","vprostřed","navrch","vevnitř","zespodu","poblíže","počínajíc","nadtoť","zpozad","vyjímaje","začínaje","zespod","navrchu","vyjímajíc","navzdor","dál","veprostřed","končíc","začínajíc","nepočítajíc","zvíce","vprostředku","opodále","podále","naprostřed","vlastně","podle","samozřejmě","vždyť","zatím","dřív","radši","spíš","poprvé","nakonec","navíc","záleží","zbytek","kým","jakmile","skutečně","tentokrát","představit","jménem"],o=a.concat(["a proto","i když","i přestože","z tohoto důvodu","kromě toho","nějaký čas","k tomu","na jedné straně","stručně řečeno","jinými slovy","důvod je","důvodem je","hlavně protože","možným důvodem je","a potom","mimo to","z uvedených důvodů","z těchto důvodů","důvod je jednoduchý","teprve potom","hlavní důvod proč","nejdřív potom","přesto však","ale zároveň","ale také","během toho"]),u=function(i){let e=i;return i.forEach((t=>{(t=t.split("-")).length>0&&t.filter((e=>!i.includes(e))).length>0&&(e=e.concat(t))})),e}([].concat([],["nula","jeden","jedné","jedna","jedno","dva","dvě","dvou","tři","čtyři","pět","šest","sedm","osm","devět","deset","jedenáct","dvanáct","třináct","čtrnáct","patnáct","šestnáct","sedmnáct","osmnáct","devatenáct","dvacet","dvacet jedna","dvacet dva","dvacet tři","třicet","čtyřicet","padesát","šedesát","sedmdesát","osmdesát","devadesát","sto","dvě stě","tři sta","čtyři sta","pět set","šest set","sedm set","osm set","devět set","tisíc","dva tisíce","jedenáct tisíc","dvacet pět tisíc","sto třicet osm tisíc","milión","dva milióny","pět miliónů","šest miliónů","sedm miliónů","miliarda"],["první","druhý","druhé","třetí","čtvrtý","pátý","šestý","sedmý","osmý","devátý","desátý"],["já","ty","on","ona","ono","my","mě","mne","mi","mně","vy","oni","ony","tě","ti","tebe","tobě","jeho","něho","ho","jej","něj","ji","jí","ní","je","ně","jim","nim","jimi","nimi","jich","nich","jemu","němu","něm","mém","mým","mých","mou","mými","ním","mu","nás","nám","námi","vás","vám","mnou","námi","tebou","vámi","našich","tys","naši","můj","má","mé","mí","moje","mého","mojí","mých","mému","moji","tvůj","tvoje","tvá","tvé","tví","tvoji","tvého","tvojí","tvých","tvojích","tvému","tvým","tvou","tvém","tvých","tvými","jeho","její","náš","naše","váš","vaše","jejich","vaší","naší","ten","tento","ta","tato","to","toto","ti","tito","tyto","ty","tato","tohle","toho","abych","těch","tenhle","abyste","abychom","tyhle","tuhle","tohoto","čeho","čemu","téhle","těmi","této","tomhle","tou","tahle","žes","tímhle","těm","těchto","tomu","tu","ten","tom","tím","který","která","které","kterého","kterému","kterou","kterém","kterým","kteří","kterých","kterými","jenž","jež","jehož","jejž","něhož","nějž","jíž","níž","jemuž","němuž","jež","něž","němž","jímž","nímž","již","jichž","nichž","jimž","nimž","jimiž","nimiž","kdo","co","koho","čeho","komu","čemu","koho","kom","čem","kým","čím","cože","což","koho","jakou"],["co","čí","čím","jak","jaký","jaké","kde","kdo","kdý","kolik","který","jenž","proč"],["nějaký","nějaká","nějaké","žádný","nijaký","lecjaký","ledajaký","ledasjaký","kdejaký","kdekterý","všelijaký","veškerý","pár","hodně","celý","tolik","celou","celé","oba","buď","zbytek","žádná","nějakou","spoustu","několik"],["se","si","sebe","sobě","sebou","svůj","svoje","svá","své","svého","svojí","svému","svoji","svou","svém","svým","sví","svých","svými"],["někdo","někoho","někomu","někom","někým","něco","nic","něčeho","něčemu","něco","cokoli","cokoliv","něčem","něčím","některá","některé","některého","některému","některý","některou","některém","některým","někteří","některých","některými","nějaká","nějaké","nějakého","nějakému","nějaký","nějakou","nějakém","nějakým","nějací","nějakých","nějakými","něčí","něčího","něčímu","něčím","něčí","ničí","něčích","něčími","ledakdo","ledaco","ledajaký","ledakterý","kdokoliv","kdokoli","kohokoli","komukoli","kohokoli","komkoli","kýmkoli","cokoli","jakýkoli","jakýkoliv","kterýkoli","číkoli","kdos","kdosi","cosi","kterýsi","jakýsi","nikdo","čísi","leckdo","leckdos","ledakdo","ledaskdo","kdekdo","lecco","leccos","ledaco","ledacos","ledaco","ledasco","leckterý","kdekdo","kdečí","kdeco","lecčí","ledačí","ledasčí","někde","nikde","kdekoliv","kdekoli","všude","leckde","ledaskde","ledakde","někudy","kudysi","nikudy","kdekudy","odněkud","odkudsi","odnikud","odevšad","kdesi","všechen","málokdo","máloco","málokterý","zřídkakdo","zřídkaco","sotvakdo","sotvaco","sotva který","každý","každá","každé","každého","každému","každému","každou","každém","každým","každí","každých","každým","každými","všechen","všechna","všechno","vše","všeho","vší","všemu","všechnu","vším","všichni","všechny","všech","všem","všemi","takový","takové ","takového","takovou","cokoliv","jiného","jiný","taková","jiné","odtud"],["během","bez","blízko","do","od","okolo","kolem","u","vedle","z","ze","k","ke","kvůli","navzdor","navzdory","krom, vedle","kromě, vedle","místo","namísto","ohledně","podél","pomocí","oproti","naproti","proti","prostřednictvím","s","u","vlivem","vyjma","využitím","stran","díky","kvůli","podle","vůči","na","té","o","pro","přes","za","po","v","ve","mezi","s","se","nad","pod","před","mimo","skrz","při","jako","asi","dokud","ven","běž","odkud","ode","nahoře","nahoru","dovnitř","dne","beze","napříč","versus","via","vně","dovnitř","vpředu","vůkol","vespod","opodál","vepředu","svrchu","vnitř","zprostřed","naspodu","zdéli","okol","podál","naspod","kontra","vespodu","zponad","ponad","nadtož","kolkolem","zdélí","veskrz","popod","daleko","vůkolem"],["a","i","aby","ale","že","protože","neboť","když","až","jestli","jestliže","pokud","kdyby","nebo","anebo","či","proto","který","jenž","aniž","než","tak","takže","kvůli","kdybych","ach","zdá","zatím","během","kdybyste","jakožto","jakož","neb"],["řekl","říkala","řekla","řekne","říkal","říká","podle","neřekl","říkat","chtějí","neviděl","vypadáš","mluvil","rozumím","znám","cítím","nemyslím","víme","nevěřím","myslíte"],["jasně","velmi","vůbec","přesně","určitě","úplně","samozřejmě","docela","skutečně","rozhodně","vážně","spolu","jistě","naprosto","velice","hrozně","strašně","opravdu"],["mělo","přijít","podívat","dělej","dá","dala","přijde","stojí","udělám","mohlo","nechte","nemáme","dám","přišla","dělal","dejte"],["dobře","dobrý","dobrá","dobré","dost","dlouho","dlouha","nejlepší","poslední","rychle","lepší","vlastní","ostatní","velký","starý","líp","malé","špatný","lépe","hlavní","právo","úžasné","pěkný","stejné","spousta","skvělá","dobrej","horší","novou","stará","nového","nejdřív","druhou","naposledy","hezký","dlouhý","dobrý","malý","těžký","velký","zlý","delší","lepší","menší","těžší","větší","horší","nejdelší","nejlepší","nejmenší","nejtěžší","největší","nejhorší","pěkně","všelijak","nějak","jaksi","tak nějak","ijak","nikterak","akkoli","akkoliv","kdejak","už","jen","tady","teď","ještě","možná","nikdy","ani","taky","pak","trochu","prostě","víc","jenom","další","právě","zpátky","vždycky","pryč","zase","někdy","také","chvíli","znovu","snad","třeba","stále","zrovna","příliš","nějak","vždy","skoro","kolem","později","zpět","najednou","támhle","někam","hlavně","často","občas","společně","dokonce","zde","aspoň","jediný","pouze","stačí","mnohem","zas","nikam","dávno","již","dvakrát","vzhůru","pomalu","bohužel","raději","nejspíš","náhodou","okamžitě"],["jo","hej","oh","uh ","hele","fajn","ok","proboha","ah","okay"],[],["den","dnes","čas","ráno","zítra","dneska","minut","včera","času","dní","dni","dny","hodinu","hodin","týdny","měsíce","roku","měsíců"],["věc","věci","můžeš","člověk","lidi","člověka","člověku","člověče","člověku","člověkovi","lidech","lidem","lidé","lidí","člověkem","lidmi","chlap","místa"],["atd.","bůhvíkdo","bůhvíjaký","bůhvíčí","nevímco","nevímkdo a podobně","si","ne","ně","pan","pane","pana","paní","prosím","pořádku","líto","chlape","slečno","mimochodem"],a)),r=[["buď","nebo"],["buď","anebo"],["ani","ani"],["nejen","ale i"],["jak","tak"],["sice","ale"],["sice","však"],["jednak","jednak"]],d=["án","ána","áno","áni","ány","ován","ána","áno","áni","ány","en","ena","eno","eni","eny","ěn","ěna","ěno","ěni","ěny","et","eta","eto","eti","ety","it","ita","ito","iti","ity","at","ata","ato","ati","aty","yt","yta","yto","yti","yty","ut","uta","uto","uti","uty"],{getWords:f}=t.languageProcessing,{values:l}=t.languageProcessing,{Clause:c}=l,{getClausesSplitOnStopWords:k,createRegexFromArray:v}=t.languageProcessing,m={Clause:class extends c{constructor(i,e){super(i,e),this._participles=function(i){return f(i).filter((i=>d.some((e=>i.endsWith(e)))))}(this.getClauseText()),this.checkParticiples()}checkParticiples(){this.setPassive(this.getParticiples().length>0)}},regexes:{auxiliaryRegex:v(["být","byl","byla","bylo","byli","byly","je","jsem","jsi","jste","jste","jsme","jste","jsou","budu","budeš","budete","bude","budeme","budete","budou","nebyl","nebyla","nebylo","nebyli","nebily","nebudu","nebudeš","nebudete","nebude","nebudeme","nebudete","nebudou"]),stopCharacterRegex:/([:,])(?=[ \n\r\t'"+\-»«‹›<>])/gi,stopwordRegex:v(s)}};function b(i){return k(i,m)}const g=window.lodash,S=function(i,e){const t=e.externalStemmer.palataliseSuffixes,n=i.length;return i.substring(n-2,n)===t.palataliseSuffixCi||i.substring(n-2,n)===t.palataliseSuffixCe||i.substring(n-2,n)===t.palataliseSuffixCiCaron||i.substring(n-2,n)===t.palataliseSuffixCeCaron?i.replace(i.substring(n-2,n),t.palataliseSuffixK):i.substring(n-2,n)===t.palataliseSuffixZi||i.substring(n-2,n)===t.palataliseSuffixZe||i.substring(n-2,n)===t.palataliseSuffixZiCaron||i.substring(n-2,n)===t.palataliseSuffixZeCaron?i.replace(i.substring(n-2,n),t.palataliseSuffixH):i.substring(n-3,n)===t.palataliseSuffixCte||i.substring(n-3,n)===t.palataliseSuffixCti||i.substring(n-3,n)===t.palataliseSuffixCtiAccented?i.replace(i.substring(n-3,n),t.palataliseSuffixCk):i.substring(n-3,n)===t.palataliseSuffixSte||i.substring(n-3,n)===t.palataliseSuffixSti||i.substring(n-3,n)===t.palataliseSuffixStiAccented?i.replace(i.substring(n-3,n),t.palataliseSuffixSk):i.slice(0,-1)},{baseStemmer:x}=t.languageProcessing;function p(i){const e=(0,g.get)(i.getData("morphology"),"cs",!1);return e?i=>function(i,e){return i=function(i,e){for(const t of e.externalStemmer.exceptionStemsWithFullForms)if(t[1].includes(i))return t[0];return i}(i=i.toLowerCase(),e),i=function(i,e){const t=e.externalStemmer.caseSuffixes,n=i.length;if(n>7&&i.substring(n-5,n)===t.caseSuffixAtech)return i.slice(0,-5);if(n>6){if(i.substring(n-4,n)===t.caseSuffixEtem)return i=i.slice(0,-3),S(i,e);if(i.substring(n-4,n)===t.caseSuffixAtum)return i.slice(0,-4)}if(n>5){if(i.substring(n-3,n)===t.caseSuffixEch||i.substring(n-3,n)===t.caseSuffixIch||i.substring(n-3,n)===t.caseSuffixIchAccented||i.substring(n-3,n)===t.caseSuffixEho||i.substring(n-3,n)===t.caseSuffixEmiCaron||i.substring(n-3,n)===t.caseSuffixEmi||i.substring(n-3,n)===t.caseSuffixEmuAccented||i.substring(n-3,n)===t.caseSuffixEte||i.substring(n-3,n)===t.caseSuffixEti||i.substring(n-3,n)===t.caseSuffixIho||i.substring(n-3,n)===t.caseSuffixIhoAccented||i.substring(n-3,n)===t.caseSuffixImi||i.substring(n-3,n)===t.caseSuffixImu)return i=i.slice(0,-2),S(i,e);if(i.substring(n-3,n)===t.caseSuffixAchAccented||i.substring(n-3,n)===t.caseSuffixAta||i.substring(n-3,n)===t.caseSuffixAty||i.substring(n-3,n)===t.caseSuffixYch||i.substring(n-3,n)===t.caseSuffixAma||i.substring(n-3,n)===t.caseSuffixAmi||i.substring(n-3,n)===t.caseSuffixOve||i.substring(n-3,n)===t.caseSuffixOvi||i.substring(n-3,n)===t.caseSuffixYmi)return i.slice(0,-3)}if(n>4){if(i.substring(n-2,n)===t.caseSuffixEm)return i=i.slice(0,-1),S(i,e);if(i.substring(n-2,n)===t.caseSuffixEs||i.substring(n-2,n)===t.caseSuffixEmAccented||i.substring(n-2,n)===t.caseSuffixIm)return i=i.slice(0,-2),S(i,e);if(i.substring(n-2,n)===t.caseSuffixUm||i.substring(n-2,n)===t.caseSuffixAt||i.substring(n-2,n)===t.caseSuffixAm||i.substring(n-2,n)===t.caseSuffixOs||i.substring(n-2,n)===t.caseSuffixUs||i.substring(n-2,n)===t.caseSuffixYm||i.substring(n-2,n)===t.caseSuffixMi||i.substring(n-2,n)===t.caseSuffixOu)return i.slice(0,-2)}if(n>3){if(i.substring(n-1,n)===t.caseSuffixE||i.substring(n-1,n)===t.caseSuffixI||i.substring(n-1,n)===t.caseSuffixIAccented||i.substring(n-1,n)===t.caseSuffixECaron)return S(i,e);if(i.substring(n-1,n)===t.caseSuffixU||i.substring(n-1,n)===t.caseSuffixY||i.substring(n-1,n)===t.caseSuffixURing||i.substring(n-1,n)===t.caseSuffixA||i.substring(n-1,n)===t.caseSuffixO||i.substring(n-1,n)===t.caseSuffixAAccented||i.substring(n-1,n)===t.caseSuffixEAccented||i.substring(n-1,n)===t.caseSuffixYAccented)return i.slice(0,-1)}return i}(i,e),i=function(i,e){const t=e.externalStemmer.possessiveSuffixes,n=i.length;if(n>5){if(i.substring(n-2,n)===t.possessiveSuffixOv)return i.slice(0,-2);if(i.substring(n-2,n)===t.possessiveSuffixesUv)return i.slice(0,-2);if(i.substring(n-2,n)===t.possessiveSuffixIn)return i=i.slice(0,-1),S(i,e)}return i}(i,e),i=function(i,e){const t=e.externalStemmer.comparativeSuffixes,n=i.length;return n>5&&i.substring(n-3,n)===t.comparativeSuffixesEjs||i.substring(n-3,n)===t.comparativeSuffixesEjsCaron?(i=i.slice(0,-2),S(i,e)):i}(i,e),i=function(i,e){const t=e.externalStemmer.diminutiveSuffixes,n=i.length;if(n>7&&i.substring(n-5,n)===t.diminutiveSuffixOusek)return i.slice(0,-5);if(n>6){if(i.substring(n-4,n)===t.diminutiveSuffixEcek||i.substring(n-4,n)===t.diminutiveSuffixEcekAccented||i.substring(n-4,n)===t.diminutiveSuffixIcek||i.substring(n-4,n)===t.diminutiveSuffixIcekAccented||i.substring(n-4,n)===t.diminutiveSuffixEnek||i.substring(n-4,n)===t.diminutiveSuffixEnekAccented||i.substring(n-4,n)===t.diminutiveSuffixInek||i.substring(n-4,n)===t.diminutiveSuffixInekAccented)return i=i.slice(0,-3),S(i,e);if(i.substring(n-4,n)===t.diminutiveSuffixAcekAccented||i.substring(n-4,n)===t.diminutiveSuffixAcek||i.substring(n-4,n)===t.diminutiveSuffixOcek||i.substring(n-4,n)===t.diminutiveSuffixUcek||i.substring(n-4,n)===t.diminutiveSuffixAnek||i.substring(n-4,n)===t.diminutiveSuffixOnek||i.substring(n-4,n)===t.diminutiveSuffixUnek||i.substring(n-4,n)===t.diminutiveSuffixAnekAccented)return i.slice(0,-4)}if(n>5){if(i.substring(n-3,n)===t.diminutiveSuffixEck||i.substring(n-3,n)===t.diminutiveSuffixEckAccented||i.substring(n-3,n)===t.diminutiveSuffixIck||i.substring(n-3,n)===t.diminutiveSuffixIckAccented||i.substring(n-3,n)===t.diminutiveSuffixEnk||i.substring(n-3,n)===t.diminutiveSuffixEnkAccented||i.substring(n-3,n)===t.diminutiveSuffixInk||i.substring(n-3,n)===t.diminutiveSuffixInkAccented)return i=i.slice(0,-3),S(i,e);if(i.substring(n-3,n)===t.diminutiveSuffixAckAccented||i.substring(n-3,n)===t.diminutiveSuffixAck||i.substring(n-3,n)===t.diminutiveSuffixOck||i.substring(n-3,n)===t.diminutiveSuffixUck||i.substring(n-3,n)===t.diminutiveSuffixAnk||i.substring(n-3,n)===t.diminutiveSuffixOnk||i.substring(n-3,n)===t.diminutiveSuffixUnk)return i.slice(0,-3);if(i.substring(n-3,n)===t.diminutiveSuffixAtk||i.substring(n-3,n)===t.diminutiveSuffixAnkAccented||i.substring(n-3,n)===t.diminutiveSuffixUsk)return i.slice(0,-3)}if(n>4){if(i.substring(n-2,n)===t.diminutiveSuffixEk||i.substring(n-2,n)===t.diminutiveSuffixEkAccented||i.substring(n-2,n)===t.diminutiveSuffixIkAccented||i.substring(n-2,n)===t.diminutiveSuffixIk)return i=i.slice(0,-1),S(i,e);if(i.substring(n-2,n)===t.diminutiveSuffixAkAccented||i.substring(n-2,n)===t.diminutiveSuffixAk||i.substring(n-2,n)===t.diminutiveSuffixOk||i.substring(n-2,n)===t.diminutiveSuffixUk)return i.slice(0,-1)}return n>3&&i.substring(n-1,n)===t.diminutiveSuffixK?i.slice(0,-1):i}(i,e),i=function(i,e){const t=e.externalStemmer.augmentativeSuffixes,n=i.length;return n>6&&i.substring(n-4,n)===t.augmentativeSuffixAjzn?i.slice(0,-4):n>5&&i.substring(n-3,n)===t.augmentativeSuffixIzn||i.substring(n-3,n)===t.augmentativeSuffixIsk?(i=i.slice(0,-2),S(i,e)):i}(i,e),i=function(i,e){const t=e.externalStemmer.derivationalSuffixes,n=i.length;if(n>8&&i.substring(n-6,n)===t.derivationalSuffixObinec)return i.slice(0,-6);if(n>7){if(i.substring(n-5,n)===t.derivationalSuffixIonar)return i=i.slice(0,-4),S(i,e);if(i.substring(n-5,n)===t.derivationalSuffixOvisk||i.substring(n-5,n)===t.derivationalSuffixOvstv||i.substring(n-5,n)===t.derivationalSuffixOvist||i.substring(n-5,n)===t.derivationalSuffixOvnik)return i.slice(0,-5)}if(n>6){if(i.substring(n-4,n)===t.derivationalSuffixAsek||i.substring(n-4,n)===t.derivationalSuffixLoun||i.substring(n-4,n)===t.derivationalSuffixNost||i.substring(n-4,n)===t.derivationalSuffixTeln||i.substring(n-4,n)===t.derivationalSuffixOvec||i.substring(n-5,n)===t.derivationalSuffixOvik||i.substring(n-4,n)===t.derivationalSuffixOvtv||i.substring(n-4,n)===t.derivationalSuffixOvin||i.substring(n-4,n)===t.derivationalSuffixStin)return i.slice(0,-4);if(i.substring(n-4,n)===t.derivationalSuffixEnic||i.substring(n-4,n)===t.derivationalSuffixInec||i.substring(n-4,n)===t.derivationalSuffixItel)return i=i.slice(0,-3),S(i,e)}if(n>5){if(i.substring(n-3,n)===t.derivationalSuffixEnk||i.substring(n-3,n)===t.derivationalSuffixIan||i.substring(n-3,n)===t.derivationalSuffixIst||i.substring(n-3,n)===t.derivationalSuffixIsk||i.substring(n-3,n)===t.derivationalSuffixIstCaron||i.substring(n-3,n)===t.derivationalSuffixItb||i.substring(n-3,n)===t.derivationalSuffixIrn)return i=i.slice(0,-2),S(i,e);if(i.substring(n-3,n)===t.derivationalSuffixArn||i.substring(n-3,n)===t.derivationalSuffixOch||i.substring(n-3,n)===t.derivationalSuffixOst||i.substring(n-3,n)===t.derivationalSuffixOvn||i.substring(n-3,n)===t.derivationalSuffixOun||i.substring(n-3,n)===t.derivationalSuffixOut||i.substring(n-3,n)===t.derivationalSuffixOus||i.substring(n-3,n)===t.derivationalSuffixUsk||i.substring(n-3,n)===t.derivationalSuffixKyn||i.substring(n-3,n)===t.derivationalSuffixCan||i.substring(n-3,n)===t.derivationalSuffixKar||i.substring(n-3,n)===t.derivationalSuffixNer||i.substring(n-3,n)===t.derivationalSuffixNik||i.substring(n-3,n)===t.derivationalSuffixCtv||i.substring(n-3,n)===t.derivationalSuffixStv)return i.slice(0,-3)}if(n>4){if(i.substring(n-2,n)===t.derivationalSuffixAcAccented||i.substring(n-2,n)===t.derivationalSuffixAc||i.substring(n-2,n)===t.derivationalSuffixAnAccented||i.substring(n-2,n)===t.derivationalSuffixAn||i.substring(n-2,n)===t.derivationalSuffixAr||i.substring(n-2,n)===t.derivationalSuffixAs)return i.slice(0,-2);if(i.substring(n-2,n)===t.derivationalSuffixEc||i.substring(n-2,n)===t.derivationalSuffixEn||i.substring(n-2,n)===t.derivationalSuffixEnCaron||i.substring(n-2,n)===t.derivationalSuffixEr||i.substring(n-2,n)===t.derivationalSuffixIr||i.substring(n-2,n)===t.derivationalSuffixIc||i.substring(n-2,n)===t.derivationalSuffixIn||i.substring(n-2,n)===t.derivationalSuffixInAccented||i.substring(n-2,n)===t.derivationalSuffixIt||i.substring(n-2,n)===t.derivationalSuffixIv)return i=i.slice(0,-1),S(i,e);if(i.substring(n-2,n)===t.derivationalSuffixOb||i.substring(n-2,n)===t.derivationalSuffixOt||i.substring(n-2,n)===t.derivationalSuffixOv||i.substring(n-2,n)===t.derivationalSuffixOn||i.substring(n-2,n)===t.derivationalSuffixUl||i.substring(n-2,n)===t.derivationalSuffixYn||i.substring(n-2,n)===t.derivationalSuffixCk||i.substring(n-2,n)===t.derivationalSuffixCn||i.substring(n-2,n)===t.derivationalSuffixDl||i.substring(n-2,n)===t.derivationalSuffixNk||i.substring(n-2,n)===t.derivationalSuffixTv||i.substring(n-2,n)===t.derivationalSuffixTk||i.substring(n-2,n)===t.derivationalSuffixVk)return i.slice(0,-2)}return n>3&&(i.charAt(i.length-1)===t.derivationalSuffixC||i.charAt(i.length-1)===t.derivationalSuffixCCaron||i.charAt(i.length-1)===t.derivationalSuffixK||i.charAt(i.length-1)===t.derivationalSuffixL||i.charAt(i.length-1)===t.derivationalSuffixN||i.charAt(i.length-1)===t.derivationalSuffixT)?i.slice(0,-1):i}(i,e),function(i,e){for(const t of e.externalStemmer.stemsThatBelongToOneWord.nouns)if(t.includes(i))return t[0];return i}(i,e)}(i,e):x}const{AbstractResearcher:j}=t.languageProcessing;class h extends j{constructor(i){super(i),delete this.defaultResearches.getFleschReadingScore,Object.assign(this.config,{language:"cs",passiveConstructionType:"periphrastic",firstWordExceptions:n,stopWords:s,functionWords:u,transitionWords:o,twoPartTransitionWords:r}),Object.assign(this.helpers,{getClauses:b,getStemmer:p})}}(window.yoast=window.yoast||{}).Researcher=e})();