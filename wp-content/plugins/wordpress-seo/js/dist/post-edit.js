(()=>{var e={2322:e=>{var t,s,n="",i=function(e){e=e||"polite";var t=document.createElement("div");return t.id="a11y-speak-"+e,t.className="a11y-speak-region",t.setAttribute("style","clip: rect(1px, 1px, 1px, 1px); position: absolute; height: 1px; width: 1px; overflow: hidden; word-wrap: normal;"),t.setAttribute("aria-live",e),t.setAttribute("aria-relevant","additions text"),t.setAttribute("aria-atomic","true"),document.querySelector("body").appendChild(t),t};!function(e){if("complete"===document.readyState||"loading"!==document.readyState&&!document.documentElement.doScroll)return e();document.addEventListener("DOMContentLoaded",e)}((function(){t=document.getElementById("a11y-speak-polite"),s=document.getElementById("a11y-speak-assertive"),null===t&&(t=i("polite")),null===s&&(s=i("assertive"))})),e.exports=function(e,i){!function(){for(var e=document.querySelectorAll(".a11y-speak-region"),t=0;t<e.length;t++)e[t].textContent=""}(),e=e.replace(/<[^<>]+>/g," "),n===e&&(e+=" "),n=e,s&&"assertive"===i?s.textContent=e:t&&(t.textContent=e)}},7084:function(e,t,s){!function(t){"use strict";var s={newline:/^\n+/,code:/^( {4}[^\n]+\n*)+/,fences:/^ {0,3}(`{3,}|~{3,})([^`~\n]*)\n(?:|([\s\S]*?)\n)(?: {0,3}\1[~`]* *(?:\n+|$)|$)/,hr:/^ {0,3}((?:- *){3,}|(?:_ *){3,}|(?:\* *){3,})(?:\n+|$)/,heading:/^ {0,3}(#{1,6}) +([^\n]*?)(?: +#+)? *(?:\n+|$)/,blockquote:/^( {0,3}> ?(paragraph|[^\n]*)(?:\n|$))+/,list:/^( {0,3})(bull) [\s\S]+?(?:hr|def|\n{2,}(?! )(?!\1bull )\n*|\s*$)/,html:"^ {0,3}(?:<(script|pre|style)[\\s>][\\s\\S]*?(?:</\\1>[^\\n]*\\n+|$)|comment[^\\n]*(\\n+|$)|<\\?[\\s\\S]*?\\?>\\n*|<![A-Z][\\s\\S]*?>\\n*|<!\\[CDATA\\[[\\s\\S]*?\\]\\]>\\n*|</?(tag)(?: +|\\n|/?>)[\\s\\S]*?(?:\\n{2,}|$)|<(?!script|pre|style)([a-z][\\w-]*)(?:attribute)*? */?>(?=[ \\t]*(?:\\n|$))[\\s\\S]*?(?:\\n{2,}|$)|</(?!script|pre|style)[a-z][\\w-]*\\s*>(?=[ \\t]*(?:\\n|$))[\\s\\S]*?(?:\\n{2,}|$))",def:/^ {0,3}\[(label)\]: *\n? *<?([^\s>]+)>?(?:(?: +\n? *| *\n *)(title))? *(?:\n+|$)/,nptable:f,table:f,lheading:/^([^\n]+)\n {0,3}(=+|-+) *(?:\n+|$)/,_paragraph:/^([^\n]+(?:\n(?!hr|heading|lheading|blockquote|fences|list|html)[^\n]+)*)/,text:/^[^\n]+/};function n(e){this.tokens=[],this.tokens.links=Object.create(null),this.options=e||v.defaults,this.rules=s.normal,this.options.pedantic?this.rules=s.pedantic:this.options.gfm&&(this.rules=s.gfm)}s._label=/(?!\s*\])(?:\\[\[\]]|[^\[\]])+/,s._title=/(?:"(?:\\"?|[^"\\])*"|'[^'\n]*(?:\n[^'\n]+)*\n?'|\([^()]*\))/,s.def=u(s.def).replace("label",s._label).replace("title",s._title).getRegex(),s.bullet=/(?:[*+-]|\d{1,9}\.)/,s.item=/^( *)(bull) ?[^\n]*(?:\n(?!\1bull ?)[^\n]*)*/,s.item=u(s.item,"gm").replace(/bull/g,s.bullet).getRegex(),s.list=u(s.list).replace(/bull/g,s.bullet).replace("hr","\\n+(?=\\1?(?:(?:- *){3,}|(?:_ *){3,}|(?:\\* *){3,})(?:\\n+|$))").replace("def","\\n+(?="+s.def.source+")").getRegex(),s._tag="address|article|aside|base|basefont|blockquote|body|caption|center|col|colgroup|dd|details|dialog|dir|div|dl|dt|fieldset|figcaption|figure|footer|form|frame|frameset|h[1-6]|head|header|hr|html|iframe|legend|li|link|main|menu|menuitem|meta|nav|noframes|ol|optgroup|option|p|param|section|source|summary|table|tbody|td|tfoot|th|thead|title|tr|track|ul",s._comment=/<!--(?!-?>)[\s\S]*?-->/,s.html=u(s.html,"i").replace("comment",s._comment).replace("tag",s._tag).replace("attribute",/ +[a-zA-Z:_][\w.:-]*(?: *= *"[^"\n]*"| *= *'[^'\n]*'| *= *[^\s"'=<>`]+)?/).getRegex(),s.paragraph=u(s._paragraph).replace("hr",s.hr).replace("heading"," {0,3}#{1,6} +").replace("|lheading","").replace("blockquote"," {0,3}>").replace("fences"," {0,3}(?:`{3,}|~{3,})[^`\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|!--)").replace("tag",s._tag).getRegex(),s.blockquote=u(s.blockquote).replace("paragraph",s.paragraph).getRegex(),s.normal=y({},s),s.gfm=y({},s.normal,{nptable:/^ *([^|\n ].*\|.*)\n *([-:]+ *\|[-| :]*)(?:\n((?:.*[^>\n ].*(?:\n|$))*)\n*|$)/,table:/^ *\|(.+)\n *\|?( *[-:]+[-| :]*)(?:\n((?: *[^>\n ].*(?:\n|$))*)\n*|$)/}),s.pedantic=y({},s.normal,{html:u("^ *(?:comment *(?:\\n|\\s*$)|<(tag)[\\s\\S]+?</\\1> *(?:\\n{2,}|\\s*$)|<tag(?:\"[^\"]*\"|'[^']*'|\\s[^'\"/>\\s]*)*?/?> *(?:\\n{2,}|\\s*$))").replace("comment",s._comment).replace(/tag/g,"(?!(?:a|em|strong|small|s|cite|q|dfn|abbr|data|time|code|var|samp|kbd|sub|sup|i|b|u|mark|ruby|rt|rp|bdi|bdo|span|br|wbr|ins|del|img)\\b)\\w+(?!:|[^\\w\\s@]*@)\\b").getRegex(),def:/^ *\[([^\]]+)\]: *<?([^\s>]+)>?(?: +(["(][^\n]+[")]))? *(?:\n+|$)/,heading:/^ *(#{1,6}) *([^\n]+?) *(?:#+ *)?(?:\n+|$)/,fences:f,paragraph:u(s.normal._paragraph).replace("hr",s.hr).replace("heading"," *#{1,6} *[^\n]").replace("lheading",s.lheading).replace("blockquote"," {0,3}>").replace("|fences","").replace("|list","").replace("|html","").getRegex()}),n.rules=s,n.lex=function(e,t){return new n(t).lex(e)},n.prototype.lex=function(e){return e=e.replace(/\r\n|\r/g,"\n").replace(/\t/g,"    ").replace(/\u00a0/g," ").replace(/\u2424/g,"\n"),this.token(e,!0)},n.prototype.token=function(e,t){var n,i,r,o,a,l,c,d,u,h,g,m,f,y,_,k;for(e=e.replace(/^ +$/gm,"");e;)if((r=this.rules.newline.exec(e))&&(e=e.substring(r[0].length),r[0].length>1&&this.tokens.push({type:"space"})),r=this.rules.code.exec(e)){var v=this.tokens[this.tokens.length-1];e=e.substring(r[0].length),v&&"paragraph"===v.type?v.text+="\n"+r[0].trimRight():(r=r[0].replace(/^ {4}/gm,""),this.tokens.push({type:"code",codeBlockStyle:"indented",text:this.options.pedantic?r:b(r,"\n")}))}else if(r=this.rules.fences.exec(e))e=e.substring(r[0].length),this.tokens.push({type:"code",lang:r[2]?r[2].trim():r[2],text:r[3]||""});else if(r=this.rules.heading.exec(e))e=e.substring(r[0].length),this.tokens.push({type:"heading",depth:r[1].length,text:r[2]});else if((r=this.rules.nptable.exec(e))&&(l={type:"table",header:w(r[1].replace(/^ *| *\| *$/g,"")),align:r[2].replace(/^ *|\| *$/g,"").split(/ *\| */),cells:r[3]?r[3].replace(/\n$/,"").split("\n"):[]}).header.length===l.align.length){for(e=e.substring(r[0].length),g=0;g<l.align.length;g++)/^ *-+: *$/.test(l.align[g])?l.align[g]="right":/^ *:-+: *$/.test(l.align[g])?l.align[g]="center":/^ *:-+ *$/.test(l.align[g])?l.align[g]="left":l.align[g]=null;for(g=0;g<l.cells.length;g++)l.cells[g]=w(l.cells[g],l.header.length);this.tokens.push(l)}else if(r=this.rules.hr.exec(e))e=e.substring(r[0].length),this.tokens.push({type:"hr"});else if(r=this.rules.blockquote.exec(e))e=e.substring(r[0].length),this.tokens.push({type:"blockquote_start"}),r=r[0].replace(/^ *> ?/gm,""),this.token(r,t),this.tokens.push({type:"blockquote_end"});else if(r=this.rules.list.exec(e)){for(e=e.substring(r[0].length),c={type:"list_start",ordered:y=(o=r[2]).length>1,start:y?+o:"",loose:!1},this.tokens.push(c),d=[],n=!1,f=(r=r[0].match(this.rules.item)).length,g=0;g<f;g++)h=(l=r[g]).length,~(l=l.replace(/^ *([*+-]|\d+\.) */,"")).indexOf("\n ")&&(h-=l.length,l=this.options.pedantic?l.replace(/^ {1,4}/gm,""):l.replace(new RegExp("^ {1,"+h+"}","gm"),"")),g!==f-1&&(a=s.bullet.exec(r[g+1])[0],(o.length>1?1===a.length:a.length>1||this.options.smartLists&&a!==o)&&(e=r.slice(g+1).join("\n")+e,g=f-1)),i=n||/\n\n(?!\s*$)/.test(l),g!==f-1&&(n="\n"===l.charAt(l.length-1),i||(i=n)),i&&(c.loose=!0),k=void 0,(_=/^\[[ xX]\] /.test(l))&&(k=" "!==l[1],l=l.replace(/^\[[ xX]\] +/,"")),u={type:"list_item_start",task:_,checked:k,loose:i},d.push(u),this.tokens.push(u),this.token(l,!1),this.tokens.push({type:"list_item_end"});if(c.loose)for(f=d.length,g=0;g<f;g++)d[g].loose=!0;this.tokens.push({type:"list_end"})}else if(r=this.rules.html.exec(e))e=e.substring(r[0].length),this.tokens.push({type:this.options.sanitize?"paragraph":"html",pre:!this.options.sanitizer&&("pre"===r[1]||"script"===r[1]||"style"===r[1]),text:this.options.sanitize?this.options.sanitizer?this.options.sanitizer(r[0]):p(r[0]):r[0]});else if(t&&(r=this.rules.def.exec(e)))e=e.substring(r[0].length),r[3]&&(r[3]=r[3].substring(1,r[3].length-1)),m=r[1].toLowerCase().replace(/\s+/g," "),this.tokens.links[m]||(this.tokens.links[m]={href:r[2],title:r[3]});else if((r=this.rules.table.exec(e))&&(l={type:"table",header:w(r[1].replace(/^ *| *\| *$/g,"")),align:r[2].replace(/^ *|\| *$/g,"").split(/ *\| */),cells:r[3]?r[3].replace(/\n$/,"").split("\n"):[]}).header.length===l.align.length){for(e=e.substring(r[0].length),g=0;g<l.align.length;g++)/^ *-+: *$/.test(l.align[g])?l.align[g]="right":/^ *:-+: *$/.test(l.align[g])?l.align[g]="center":/^ *:-+ *$/.test(l.align[g])?l.align[g]="left":l.align[g]=null;for(g=0;g<l.cells.length;g++)l.cells[g]=w(l.cells[g].replace(/^ *\| *| *\| *$/g,""),l.header.length);this.tokens.push(l)}else if(r=this.rules.lheading.exec(e))e=e.substring(r[0].length),this.tokens.push({type:"heading",depth:"="===r[2].charAt(0)?1:2,text:r[1]});else if(t&&(r=this.rules.paragraph.exec(e)))e=e.substring(r[0].length),this.tokens.push({type:"paragraph",text:"\n"===r[1].charAt(r[1].length-1)?r[1].slice(0,-1):r[1]});else if(r=this.rules.text.exec(e))e=e.substring(r[0].length),this.tokens.push({type:"text",text:r[0]});else if(e)throw new Error("Infinite loop on byte: "+e.charCodeAt(0));return this.tokens};var i={escape:/^\\([!"#$%&'()*+,\-./:;<=>?@\[\]\\^_`{|}~])/,autolink:/^<(scheme:[^\s\x00-\x1f<>]*|email)>/,url:f,tag:"^comment|^</[a-zA-Z][\\w:-]*\\s*>|^<[a-zA-Z][\\w-]*(?:attribute)*?\\s*/?>|^<\\?[\\s\\S]*?\\?>|^<![a-zA-Z]+\\s[\\s\\S]*?>|^<!\\[CDATA\\[[\\s\\S]*?\\]\\]>",link:/^!?\[(label)\]\(\s*(href)(?:\s+(title))?\s*\)/,reflink:/^!?\[(label)\]\[(?!\s*\])((?:\\[\[\]]?|[^\[\]\\])+)\]/,nolink:/^!?\[(?!\s*\])((?:\[[^\[\]]*\]|\\[\[\]]|[^\[\]])*)\](?:\[\])?/,strong:/^__([^\s_])__(?!_)|^\*\*([^\s*])\*\*(?!\*)|^__([^\s][\s\S]*?[^\s])__(?!_)|^\*\*([^\s][\s\S]*?[^\s])\*\*(?!\*)/,em:/^_([^\s_])_(?!_)|^\*([^\s*<\[])\*(?!\*)|^_([^\s<][\s\S]*?[^\s_])_(?!_|[^\spunctuation])|^_([^\s_<][\s\S]*?[^\s])_(?!_|[^\spunctuation])|^\*([^\s<"][\s\S]*?[^\s\*])\*(?!\*|[^\spunctuation])|^\*([^\s*"<\[][\s\S]*?[^\s])\*(?!\*)/,code:/^(`+)([^`]|[^`][\s\S]*?[^`])\1(?!`)/,br:/^( {2,}|\\)\n(?!\s*$)/,del:f,text:/^(`+|[^`])(?:[\s\S]*?(?:(?=[\\<!\[`*]|\b_|$)|[^ ](?= {2,}\n))|(?= {2,}\n))/};function r(e,t){if(this.options=t||v.defaults,this.links=e,this.rules=i.normal,this.renderer=this.options.renderer||new o,this.renderer.options=this.options,!this.links)throw new Error("Tokens array requires a `links` property.");this.options.pedantic?this.rules=i.pedantic:this.options.gfm&&(this.options.breaks?this.rules=i.breaks:this.rules=i.gfm)}function o(e){this.options=e||v.defaults}function a(){}function l(e){this.tokens=[],this.token=null,this.options=e||v.defaults,this.options.renderer=this.options.renderer||new o,this.renderer=this.options.renderer,this.renderer.options=this.options,this.slugger=new c}function c(){this.seen={}}function p(e,t){if(t){if(p.escapeTest.test(e))return e.replace(p.escapeReplace,(function(e){return p.replacements[e]}))}else if(p.escapeTestNoEncode.test(e))return e.replace(p.escapeReplaceNoEncode,(function(e){return p.replacements[e]}));return e}function d(e){return e.replace(/&(#(?:\d+)|(?:#x[0-9A-Fa-f]+)|(?:\w+));?/gi,(function(e,t){return"colon"===(t=t.toLowerCase())?":":"#"===t.charAt(0)?"x"===t.charAt(1)?String.fromCharCode(parseInt(t.substring(2),16)):String.fromCharCode(+t.substring(1)):""}))}function u(e,t){return e=e.source||e,t=t||"",{replace:function(t,s){return s=(s=s.source||s).replace(/(^|[^\[])\^/g,"$1"),e=e.replace(t,s),this},getRegex:function(){return new RegExp(e,t)}}}function h(e,t,s){if(e){try{var n=decodeURIComponent(d(s)).replace(/[^\w:]/g,"").toLowerCase()}catch(e){return null}if(0===n.indexOf("javascript:")||0===n.indexOf("vbscript:")||0===n.indexOf("data:"))return null}t&&!m.test(s)&&(s=function(e,t){return g[" "+e]||(/^[^:]+:\/*[^/]*$/.test(e)?g[" "+e]=e+"/":g[" "+e]=b(e,"/",!0)),e=g[" "+e],"//"===t.slice(0,2)?e.replace(/:[\s\S]*/,":")+t:"/"===t.charAt(0)?e.replace(/(:\/*[^/]*)[\s\S]*/,"$1")+t:e+t}(t,s));try{s=encodeURI(s).replace(/%25/g,"%")}catch(e){return null}return s}i._punctuation="!\"#$%&'()*+,\\-./:;<=>?@\\[^_{|}~",i.em=u(i.em).replace(/punctuation/g,i._punctuation).getRegex(),i._escapes=/\\([!"#$%&'()*+,\-./:;<=>?@\[\]\\^_`{|}~])/g,i._scheme=/[a-zA-Z][a-zA-Z0-9+.-]{1,31}/,i._email=/[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+(@)[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)+(?![-_])/,i.autolink=u(i.autolink).replace("scheme",i._scheme).replace("email",i._email).getRegex(),i._attribute=/\s+[a-zA-Z:_][\w.:-]*(?:\s*=\s*"[^"]*"|\s*=\s*'[^']*'|\s*=\s*[^\s"'=<>`]+)?/,i.tag=u(i.tag).replace("comment",s._comment).replace("attribute",i._attribute).getRegex(),i._label=/(?:\[[^\[\]]*\]|\\.|`[^`]*`|[^\[\]\\`])*?/,i._href=/<(?:\\[<>]?|[^\s<>\\])*>|[^\s\x00-\x1f]*/,i._title=/"(?:\\"?|[^"\\])*"|'(?:\\'?|[^'\\])*'|\((?:\\\)?|[^)\\])*\)/,i.link=u(i.link).replace("label",i._label).replace("href",i._href).replace("title",i._title).getRegex(),i.reflink=u(i.reflink).replace("label",i._label).getRegex(),i.normal=y({},i),i.pedantic=y({},i.normal,{strong:/^__(?=\S)([\s\S]*?\S)__(?!_)|^\*\*(?=\S)([\s\S]*?\S)\*\*(?!\*)/,em:/^_(?=\S)([\s\S]*?\S)_(?!_)|^\*(?=\S)([\s\S]*?\S)\*(?!\*)/,link:u(/^!?\[(label)\]\((.*?)\)/).replace("label",i._label).getRegex(),reflink:u(/^!?\[(label)\]\s*\[([^\]]*)\]/).replace("label",i._label).getRegex()}),i.gfm=y({},i.normal,{escape:u(i.escape).replace("])","~|])").getRegex(),_extended_email:/[A-Za-z0-9._+-]+(@)[a-zA-Z0-9-_]+(?:\.[a-zA-Z0-9-_]*[a-zA-Z0-9])+(?![-_])/,url:/^((?:ftp|https?):\/\/|www\.)(?:[a-zA-Z0-9\-]+\.?)+[^\s<]*|^email/,_backpedal:/(?:[^?!.,:;*_~()&]+|\([^)]*\)|&(?![a-zA-Z0-9]+;$)|[?!.,:;*_~)]+(?!$))+/,del:/^~+(?=\S)([\s\S]*?\S)~+/,text:/^(`+|[^`])(?:[\s\S]*?(?:(?=[\\<!\[`*~]|\b_|https?:\/\/|ftp:\/\/|www\.|$)|[^ ](?= {2,}\n)|[^a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-](?=[a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-]+@))|(?= {2,}\n|[a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-]+@))/}),i.gfm.url=u(i.gfm.url,"i").replace("email",i.gfm._extended_email).getRegex(),i.breaks=y({},i.gfm,{br:u(i.br).replace("{2,}","*").getRegex(),text:u(i.gfm.text).replace("\\b_","\\b_| {2,}\\n").replace(/\{2,\}/g,"*").getRegex()}),r.rules=i,r.output=function(e,t,s){return new r(t,s).output(e)},r.prototype.output=function(e){for(var t,s,n,i,o,a,l="";e;)if(o=this.rules.escape.exec(e))e=e.substring(o[0].length),l+=p(o[1]);else if(o=this.rules.tag.exec(e))!this.inLink&&/^<a /i.test(o[0])?this.inLink=!0:this.inLink&&/^<\/a>/i.test(o[0])&&(this.inLink=!1),!this.inRawBlock&&/^<(pre|code|kbd|script)(\s|>)/i.test(o[0])?this.inRawBlock=!0:this.inRawBlock&&/^<\/(pre|code|kbd|script)(\s|>)/i.test(o[0])&&(this.inRawBlock=!1),e=e.substring(o[0].length),l+=this.options.sanitize?this.options.sanitizer?this.options.sanitizer(o[0]):p(o[0]):o[0];else if(o=this.rules.link.exec(e)){var c=_(o[2],"()");if(c>-1){var d=4+o[1].length+c;o[2]=o[2].substring(0,c),o[0]=o[0].substring(0,d).trim(),o[3]=""}e=e.substring(o[0].length),this.inLink=!0,n=o[2],this.options.pedantic?(t=/^([^'"]*[^\s])\s+(['"])(.*)\2/.exec(n))?(n=t[1],i=t[3]):i="":i=o[3]?o[3].slice(1,-1):"",n=n.trim().replace(/^<([\s\S]*)>$/,"$1"),l+=this.outputLink(o,{href:r.escapes(n),title:r.escapes(i)}),this.inLink=!1}else if((o=this.rules.reflink.exec(e))||(o=this.rules.nolink.exec(e))){if(e=e.substring(o[0].length),t=(o[2]||o[1]).replace(/\s+/g," "),!(t=this.links[t.toLowerCase()])||!t.href){l+=o[0].charAt(0),e=o[0].substring(1)+e;continue}this.inLink=!0,l+=this.outputLink(o,t),this.inLink=!1}else if(o=this.rules.strong.exec(e))e=e.substring(o[0].length),l+=this.renderer.strong(this.output(o[4]||o[3]||o[2]||o[1]));else if(o=this.rules.em.exec(e))e=e.substring(o[0].length),l+=this.renderer.em(this.output(o[6]||o[5]||o[4]||o[3]||o[2]||o[1]));else if(o=this.rules.code.exec(e))e=e.substring(o[0].length),l+=this.renderer.codespan(p(o[2].trim(),!0));else if(o=this.rules.br.exec(e))e=e.substring(o[0].length),l+=this.renderer.br();else if(o=this.rules.del.exec(e))e=e.substring(o[0].length),l+=this.renderer.del(this.output(o[1]));else if(o=this.rules.autolink.exec(e))e=e.substring(o[0].length),n="@"===o[2]?"mailto:"+(s=p(this.mangle(o[1]))):s=p(o[1]),l+=this.renderer.link(n,null,s);else if(this.inLink||!(o=this.rules.url.exec(e))){if(o=this.rules.text.exec(e))e=e.substring(o[0].length),this.inRawBlock?l+=this.renderer.text(this.options.sanitize?this.options.sanitizer?this.options.sanitizer(o[0]):p(o[0]):o[0]):l+=this.renderer.text(p(this.smartypants(o[0])));else if(e)throw new Error("Infinite loop on byte: "+e.charCodeAt(0))}else{if("@"===o[2])n="mailto:"+(s=p(o[0]));else{do{a=o[0],o[0]=this.rules._backpedal.exec(o[0])[0]}while(a!==o[0]);s=p(o[0]),n="www."===o[1]?"http://"+s:s}e=e.substring(o[0].length),l+=this.renderer.link(n,null,s)}return l},r.escapes=function(e){return e?e.replace(r.rules._escapes,"$1"):e},r.prototype.outputLink=function(e,t){var s=t.href,n=t.title?p(t.title):null;return"!"!==e[0].charAt(0)?this.renderer.link(s,n,this.output(e[1])):this.renderer.image(s,n,p(e[1]))},r.prototype.smartypants=function(e){return this.options.smartypants?e.replace(/---/g,"—").replace(/--/g,"–").replace(/(^|[-\u2014/(\[{"\s])'/g,"$1‘").replace(/'/g,"’").replace(/(^|[-\u2014/(\[{\u2018\s])"/g,"$1“").replace(/"/g,"”").replace(/\.{3}/g,"…"):e},r.prototype.mangle=function(e){if(!this.options.mangle)return e;for(var t,s="",n=e.length,i=0;i<n;i++)t=e.charCodeAt(i),Math.random()>.5&&(t="x"+t.toString(16)),s+="&#"+t+";";return s},o.prototype.code=function(e,t,s){var n=(t||"").match(/\S*/)[0];if(this.options.highlight){var i=this.options.highlight(e,n);null!=i&&i!==e&&(s=!0,e=i)}return n?'<pre><code class="'+this.options.langPrefix+p(n,!0)+'">'+(s?e:p(e,!0))+"</code></pre>\n":"<pre><code>"+(s?e:p(e,!0))+"</code></pre>"},o.prototype.blockquote=function(e){return"<blockquote>\n"+e+"</blockquote>\n"},o.prototype.html=function(e){return e},o.prototype.heading=function(e,t,s,n){return this.options.headerIds?"<h"+t+' id="'+this.options.headerPrefix+n.slug(s)+'">'+e+"</h"+t+">\n":"<h"+t+">"+e+"</h"+t+">\n"},o.prototype.hr=function(){return this.options.xhtml?"<hr/>\n":"<hr>\n"},o.prototype.list=function(e,t,s){var n=t?"ol":"ul";return"<"+n+(t&&1!==s?' start="'+s+'"':"")+">\n"+e+"</"+n+">\n"},o.prototype.listitem=function(e){return"<li>"+e+"</li>\n"},o.prototype.checkbox=function(e){return"<input "+(e?'checked="" ':"")+'disabled="" type="checkbox"'+(this.options.xhtml?" /":"")+"> "},o.prototype.paragraph=function(e){return"<p>"+e+"</p>\n"},o.prototype.table=function(e,t){return t&&(t="<tbody>"+t+"</tbody>"),"<table>\n<thead>\n"+e+"</thead>\n"+t+"</table>\n"},o.prototype.tablerow=function(e){return"<tr>\n"+e+"</tr>\n"},o.prototype.tablecell=function(e,t){var s=t.header?"th":"td";return(t.align?"<"+s+' align="'+t.align+'">':"<"+s+">")+e+"</"+s+">\n"},o.prototype.strong=function(e){return"<strong>"+e+"</strong>"},o.prototype.em=function(e){return"<em>"+e+"</em>"},o.prototype.codespan=function(e){return"<code>"+e+"</code>"},o.prototype.br=function(){return this.options.xhtml?"<br/>":"<br>"},o.prototype.del=function(e){return"<del>"+e+"</del>"},o.prototype.link=function(e,t,s){if(null===(e=h(this.options.sanitize,this.options.baseUrl,e)))return s;var n='<a href="'+p(e)+'"';return t&&(n+=' title="'+t+'"'),n+">"+s+"</a>"},o.prototype.image=function(e,t,s){if(null===(e=h(this.options.sanitize,this.options.baseUrl,e)))return s;var n='<img src="'+e+'" alt="'+s+'"';return t&&(n+=' title="'+t+'"'),n+(this.options.xhtml?"/>":">")},o.prototype.text=function(e){return e},a.prototype.strong=a.prototype.em=a.prototype.codespan=a.prototype.del=a.prototype.text=function(e){return e},a.prototype.link=a.prototype.image=function(e,t,s){return""+s},a.prototype.br=function(){return""},l.parse=function(e,t){return new l(t).parse(e)},l.prototype.parse=function(e){this.inline=new r(e.links,this.options),this.inlineText=new r(e.links,y({},this.options,{renderer:new a})),this.tokens=e.reverse();for(var t="";this.next();)t+=this.tok();return t},l.prototype.next=function(){return this.token=this.tokens.pop(),this.token},l.prototype.peek=function(){return this.tokens[this.tokens.length-1]||0},l.prototype.parseText=function(){for(var e=this.token.text;"text"===this.peek().type;)e+="\n"+this.next().text;return this.inline.output(e)},l.prototype.tok=function(){switch(this.token.type){case"space":return"";case"hr":return this.renderer.hr();case"heading":return this.renderer.heading(this.inline.output(this.token.text),this.token.depth,d(this.inlineText.output(this.token.text)),this.slugger);case"code":return this.renderer.code(this.token.text,this.token.lang,this.token.escaped);case"table":var e,t,s,n,i="",r="";for(s="",e=0;e<this.token.header.length;e++)s+=this.renderer.tablecell(this.inline.output(this.token.header[e]),{header:!0,align:this.token.align[e]});for(i+=this.renderer.tablerow(s),e=0;e<this.token.cells.length;e++){for(t=this.token.cells[e],s="",n=0;n<t.length;n++)s+=this.renderer.tablecell(this.inline.output(t[n]),{header:!1,align:this.token.align[n]});r+=this.renderer.tablerow(s)}return this.renderer.table(i,r);case"blockquote_start":for(r="";"blockquote_end"!==this.next().type;)r+=this.tok();return this.renderer.blockquote(r);case"list_start":r="";for(var o=this.token.ordered,a=this.token.start;"list_end"!==this.next().type;)r+=this.tok();return this.renderer.list(r,o,a);case"list_item_start":r="";var l=this.token.loose,c=this.token.checked,p=this.token.task;for(this.token.task&&(r+=this.renderer.checkbox(c));"list_item_end"!==this.next().type;)r+=l||"text"!==this.token.type?this.tok():this.parseText();return this.renderer.listitem(r,p,c);case"html":return this.renderer.html(this.token.text);case"paragraph":return this.renderer.paragraph(this.inline.output(this.token.text));case"text":return this.renderer.paragraph(this.parseText());default:var u='Token with "'+this.token.type+'" type was not found.';if(!this.options.silent)throw new Error(u);console.log(u)}},c.prototype.slug=function(e){var t=e.toLowerCase().trim().replace(/[\u2000-\u206F\u2E00-\u2E7F\\'!"#$%&()*+,./:;<=>?@[\]^`{|}~]/g,"").replace(/\s/g,"-");if(this.seen.hasOwnProperty(t)){var s=t;do{this.seen[s]++,t=s+"-"+this.seen[s]}while(this.seen.hasOwnProperty(t))}return this.seen[t]=0,t},p.escapeTest=/[&<>"']/,p.escapeReplace=/[&<>"']/g,p.replacements={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"},p.escapeTestNoEncode=/[<>"']|&(?!#?\w+;)/,p.escapeReplaceNoEncode=/[<>"']|&(?!#?\w+;)/g;var g={},m=/^$|^[a-z][a-z0-9+.-]*:|^[?#]/i;function f(){}function y(e){for(var t,s,n=1;n<arguments.length;n++)for(s in t=arguments[n])Object.prototype.hasOwnProperty.call(t,s)&&(e[s]=t[s]);return e}function w(e,t){var s=e.replace(/\|/g,(function(e,t,s){for(var n=!1,i=t;--i>=0&&"\\"===s[i];)n=!n;return n?"|":" |"})).split(/ \|/),n=0;if(s.length>t)s.splice(t);else for(;s.length<t;)s.push("");for(;n<s.length;n++)s[n]=s[n].trim().replace(/\\\|/g,"|");return s}function b(e,t,s){if(0===e.length)return"";for(var n=0;n<e.length;){var i=e.charAt(e.length-n-1);if(i!==t||s){if(i===t||!s)break;n++}else n++}return e.substr(0,e.length-n)}function _(e,t){if(-1===e.indexOf(t[1]))return-1;for(var s=0,n=0;n<e.length;n++)if("\\"===e[n])n++;else if(e[n]===t[0])s++;else if(e[n]===t[1]&&--s<0)return n;return-1}function k(e){e&&e.sanitize&&!e.silent&&console.warn("marked(): sanitize and sanitizer parameters are deprecated since version 0.7.0, should not be used and will be removed in the future. Read more here: https://marked.js.org/#/USING_ADVANCED.md#options")}function v(e,t,s){if(null==e)throw new Error("marked(): input parameter is undefined or null");if("string"!=typeof e)throw new Error("marked(): input parameter is of type "+Object.prototype.toString.call(e)+", string expected");if(s||"function"==typeof t){s||(s=t,t=null),k(t=y({},v.defaults,t||{}));var i,r,o=t.highlight,a=0;try{i=n.lex(e,t)}catch(e){return s(e)}r=i.length;var c=function(e){if(e)return t.highlight=o,s(e);var n;try{n=l.parse(i,t)}catch(t){e=t}return t.highlight=o,e?s(e):s(null,n)};if(!o||o.length<3)return c();if(delete t.highlight,!r)return c();for(;a<i.length;a++)!function(e){"code"!==e.type?--r||c():o(e.text,e.lang,(function(t,s){return t?c(t):null==s||s===e.text?--r||c():(e.text=s,e.escaped=!0,void(--r||c()))}))}(i[a])}else try{return t&&(t=y({},v.defaults,t)),k(t),l.parse(n.lex(e,t),t)}catch(e){if(e.message+="\nPlease report this to https://github.com/markedjs/marked.",(t||v.defaults).silent)return"<p>An error occurred:</p><pre>"+p(e.message+"",!0)+"</pre>";throw e}}f.exec=f,v.options=v.setOptions=function(e){return y(v.defaults,e),v},v.getDefaults=function(){return{baseUrl:null,breaks:!1,gfm:!0,headerIds:!0,headerPrefix:"",highlight:null,langPrefix:"language-",mangle:!0,pedantic:!1,renderer:new o,sanitize:!1,sanitizer:null,silent:!1,smartLists:!1,smartypants:!1,xhtml:!1}},v.defaults=v.getDefaults(),v.Parser=l,v.parser=l.parse,v.Renderer=o,v.TextRenderer=a,v.Lexer=n,v.lexer=n.lex,v.InlineLexer=r,v.inlineLexer=r.output,v.Slugger=c,v.parse=v,e.exports=v}(this||("undefined"!=typeof window?window:s.g))}},t={};function s(n){var i=t[n];if(void 0!==i)return i.exports;var r=t[n]={exports:{}};return e[n].call(r.exports,r,r.exports,s),r.exports}s.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return s.d(t,{a:t}),t},s.d=(e,t)=>{for(var n in t)s.o(t,n)&&!s.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},s.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"==typeof window)return window}}(),s.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),s.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},(()=>{"use strict";var e={};s.r(e),s.d(e,{DISMISS_ALERT:()=>ye,SNIPPET_EDITOR_FIND_CUSTOM_FIELDS:()=>we,wistiaEmbedPermission:()=>be});var t={};s.r(t),s.d(t,{addEventHandler:()=>pt,disableMarkerButtons:()=>dt,enableMarkerButtons:()=>ut,getContentTinyMce:()=>ct,isTextViewActive:()=>mt,isTinyMCEAvailable:()=>lt,isTinyMCELoaded:()=>at,pauseMarkers:()=>ht,resumeMarkers:()=>gt,setStore:()=>ot,termsTmceId:()=>rt,tinyMceEventBinder:()=>yt,tmceId:()=>it,wpTextViewOnInitCheck:()=>ft});var n={};s.r(n),s.d(n,{createScoresInPublishBox:()=>St,initialize:()=>Ct,scrollToCollapsible:()=>Rt,updateScore:()=>Et});const i=window.wp.domReady;var r=s.n(i);const o=window.jQuery;var a=s.n(o);const l=window.lodash,c=window.React,p=window.wp.components,d=window.wp.data,u=window.wp.element,h=window.wp.hooks,g=window.wp.i18n,m=window.yoast.uiLibrary,f=window.yoast.propTypes;var y=s.n(f);y().string.isRequired;const w=c.forwardRef((function(e,t){return c.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:2,stroke:"currentColor","aria-hidden":"true",ref:t},e),c.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M8 11V7a4 4 0 118 0m-4 8v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2z"}))})),b=c.forwardRef((function(e,t){return c.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true",ref:t},e),c.createElement("path",{fillRule:"evenodd",d:"M12.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-2.293-2.293a1 1 0 010-1.414z",clipRule:"evenodd"}))})),k=({learnMoreLink:e,thumbnail:t,wistiaEmbedPermission:s,upsellLink:n,isProductCopy:i,title:r,upsellLabel:o,newToText:a,bundleNote:l})=>{const{onClose:p,initialFocus:d}=(0,m.useModalContext)(),h={a:(0,c.createElement)(S,{href:e,className:"yst-inline-flex yst-items-center yst-gap-1 yst-no-underline yst-font-medium",variant:"primary"}),ArrowNarrowRightIcon:(0,c.createElement)(b,{className:"yst-w-4 yst-h-4 rtl:yst-rotate-180"})};return(0,c.createElement)("div",{className:"yst-flex yst-flex-col yst-items-center yst-p-10"},(0,c.createElement)("div",{className:"yst-relative yst-w-full"},(0,c.createElement)(G,{videoId:"vmrahpfjxp",thumbnail:t,wistiaEmbedPermission:s}),(0,c.createElement)(m.Badge,{className:"yst-absolute yst-top-0 yst-right-2 yst-mt-2 yst-ml-2",variant:"info"},"Beta")),(0,c.createElement)("div",{className:"yst-mt-6 yst-text-xs yst-font-medium"},(0,c.createElement)("span",{className:"yst-introduction-modal-uppercase"},a)),(0,c.createElement)("div",{className:"yst-mt-4 yst-mx-1.5 yst-text-center"},(0,c.createElement)("h3",{className:"yst-text-slate-900 yst-text-lg yst-font-medium"},r),(0,c.createElement)("div",{className:"yst-mt-2 yst-text-slate-600 yst-text-sm"},i?(0,u.createInterpolateElement)((0,g.sprintf)(/* translators: %1$s and %2$s are anchor tags; %3$s is the arrow icon. */
(0,g.__)("Let AI do some of the thinking for you and help you save time. Get high-quality suggestions for product titles and meta descriptions to make your content rank high and look good on social media. %1$sLearn more%2$s%3$s","wordpress-seo"),"<a>","<ArrowNarrowRightIcon />","</a>"),h):(0,u.createInterpolateElement)((0,g.sprintf)(/* translators: %1$s and %2$s are anchor tags; %3$s is the arrow icon. */
(0,g.__)("Let AI do some of the thinking for you and help you save time. Get high-quality suggestions for titles and meta descriptions to make your content rank high and look good on social media. %1$sLearn more%2$s%3$s","wordpress-seo"),"<a>","<ArrowNarrowRightIcon />","</a>"),h))),(0,c.createElement)("div",{className:"yst-w-full yst-flex yst-mt-10"},(0,c.createElement)(m.Button,{as:"a",className:"yst-grow",size:"extra-large",variant:"upsell",href:n,target:"_blank",ref:d},(0,c.createElement)(w,{className:"yst--ml-1 yst-mr-2 yst-h-5 yst-w-5"}),o,(0,c.createElement)("span",{className:"yst-sr-only"},/* translators: Hidden accessibility text. */
(0,g.__)("(Opens in a new browser tab)","wordpress-seo")))),l,(0,c.createElement)(m.Button,{as:"a",className:"yst-mt-4",variant:"tertiary",onClick:p},(0,g.__)("Close","wordpress-seo")))};k.propTypes={learnMoreLink:y().string.isRequired,upsellLink:y().string.isRequired,thumbnail:y().shape({src:y().string.isRequired,width:y().string,height:y().string}).isRequired,wistiaEmbedPermission:y().shape({value:y().bool.isRequired,status:y().string.isRequired,set:y().func.isRequired}).isRequired,title:y().string,upsellLabel:y().string,newToText:y().string,isProductCopy:y().bool,bundleNote:y().oneOfType([y().string,y().element])},k.defaultProps={title:(0,g.__)("Use AI to write your titles & meta descriptions!","wordpress-seo"),upsellLabel:(0,g.sprintf)(/* translators: %1$s expands to Yoast SEO Premium. */
(0,g.__)("Unlock with %1$s","wordpress-seo"),"Yoast SEO Premium"),newToText:(0,g.sprintf)(/* translators: %1$s expands to Yoast SEO Premium. */
(0,g.__)("New in %1$s","wordpress-seo"),"Yoast SEO Premium"),isProductCopy:!1,bundleNote:""};var v;function x(){return x=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var s=arguments[t];for(var n in s)Object.prototype.hasOwnProperty.call(s,n)&&(e[n]=s[n])}return e},x.apply(this,arguments)}y().string,y().node.isRequired,y().node.isRequired,y().node,y().oneOf(Object.keys({lg:{grid:"yst-grid lg:yst-grid-cols-3 lg:yst-gap-12",col1:"yst-col-span-1",col2:"lg:yst-mt-0 lg:yst-col-span-2"},xl:{grid:"yst-grid xl:yst-grid-cols-3 xl:yst-gap-12",col1:"yst-col-span-1",col2:"xl:yst-mt-0 xl:yst-col-span-2"},"2xl":{grid:"yst-grid 2xl:yst-grid-cols-3 2xl:yst-gap-12",col1:"yst-col-span-1",col2:"2xl:yst-mt-0 2xl:yst-col-span-2"}}));const E=e=>c.createElement("svg",x({xmlns:"http://www.w3.org/2000/svg","aria-hidden":"true",viewBox:"0 0 1000 1000"},e),v||(v=c.createElement("path",{fill:"#fff",d:"M500 0C223.9 0 0 223.9 0 500s223.9 500 500 500 500-223.9 500-500S776.1 0 500 0Zm87.2 412.4c0-21.9 4.3-40.2 13.1-54.4s24-27.1 45.9-38.2l10.1-4.9c17.8-9 22.4-16.7 22.4-26 0-11.1-9.5-19.1-25-19.1-18.3 0-32.2 9.5-41.8 28.9l-24.7-24.8c5.4-11.6 14.1-20.9 25.8-28.1a70.8 70.8 0 0 1 38.9-11.1c17.8 0 33.3 4.6 45.9 14.2s19.4 22.7 19.4 39.4c0 26.6-15 42.9-43.1 57.3l-15.7 8c-16.8 8.5-25.1 16-27.4 29.4h85.4v35.4H587.2Zm-82.1 373.3c-157.8 0-285.7-127.9-285.7-285.7s127.9-285.7 285.7-285.7a286.4 286.4 0 0 1 55.9 5.5l-55.9 116.9c-90 0-163.3 73.3-163.3 163.3s73.3 163.3 163.3 163.3a162.8 162.8 0 0 0 106.4-39.6l61.8 107.2a283.9 283.9 0 0 1-168.2 54.8ZM705 704.1l-70.7-122.5H492.9l70.7-122.4H705l70.7 122.4Z"}))),S=({href:e,children:t,...s})=>(0,c.createElement)(m.Link,{target:"_blank",rel:"noopener noreferrer",...s,href:e},t,(0,c.createElement)("span",{className:"yst-sr-only"},/* translators: Hidden accessibility text. */
(0,g.__)("(Opens in a new browser tab)","wordpress-seo")));S.propTypes={href:y().string.isRequired,children:y().node},S.defaultProps={children:null};const R=c.forwardRef((function(e,t){return c.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:2,stroke:"currentColor","aria-hidden":"true",ref:t},e),c.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M17 8l4 4m0 0l-4 4m4-4H3"}))}));var C,T,P;function O(){return O=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var s=arguments[t];for(var n in s)Object.prototype.hasOwnProperty.call(s,n)&&(e[n]=s[n])}return e},O.apply(this,arguments)}const A=e=>c.createElement("svg",O({xmlns:"http://www.w3.org/2000/svg",id:"star-rating-half_svg__Layer_1","data-name":"Layer 1",viewBox:"0 0 500 475.53"},e),C||(C=c.createElement("defs",null,c.createElement("style",null,".star-rating-half_svg__cls-1{fill:#fbbf24}"))),T||(T=c.createElement("path",{d:"M250 392.04 98.15 471.87l29-169.09L4.3 183.03l169.77-24.67L250 4.52l75.93 153.84 169.77 24.67-122.85 119.75 29 169.09L250 392.04z",className:"star-rating-half_svg__cls-1"})),P||(P=c.createElement("path",{d:"m250 9.04 73.67 149.27.93 1.88 2.08.3 164.72 23.94-119.19 116.19-1.51 1.47.36 2.07 28.14 164.06-147.34-77.46-1.86-1-1.86 1-147.34 77.46 28.14-164.06.36-2.07-1.51-1.47L8.6 184.43l164.72-23.9 2.08-.3.93-1.88L250 9.04m0-9-77.25 156.49L0 181.64l125 121.89-29.51 172L250 394.3l154.51 81.23-29.51-172 125-121.89-172.75-25.11L250 0Z",className:"star-rating-half_svg__cls-1"})),c.createElement("path",{d:"m500 181.64-172.75-25.11L250 0v394.3l154.51 81.23L375 303.48l125-121.84z",style:{fill:"#f3f4f6"}}));function I(){return I=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var s=arguments[t];for(var n in s)Object.prototype.hasOwnProperty.call(s,n)&&(e[n]=s[n])}return e},I.apply(this,arguments)}const M=e=>c.createElement("svg",I({xmlns:"http://www.w3.org/2000/svg","data-name":"Layer 1",viewBox:"0 0 500 475.53"},e),c.createElement("path",{d:"m250 0 77.25 156.53L500 181.64 375 303.48l29.51 172.05L250 394.3 95.49 475.53 125 303.48 0 181.64l172.75-25.11L250 0z",style:{fill:"#fbbf24"}}));var D,L,B,N,F,$,U,j,z;function q(){return q=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var s=arguments[t];for(var n in s)Object.prototype.hasOwnProperty.call(s,n)&&(e[n]=s[n])}return e},q.apply(this,arguments)}const Y=e=>c.createElement("svg",q({xmlns:"http://www.w3.org/2000/svg","aria-hidden":"true",viewBox:"0 0 500 500"},e),D||(D=c.createElement("path",{fill:"#a4286a",d:"M80 0h340a80 80 0 0 1 80 80v420H80a80 80 0 0 1-80-80V80A80 80 0 0 1 80 0z"})),L||(L=c.createElement("path",{fill:"#6c2548",d:"M437.61 2 155.89 500H500V80a80 80 0 0 0-62.39-78z"})),B||(B=c.createElement("path",{fill:"#fff",d:"M74.4 337.3v34.9c21.6-.9 38.5-8 52.8-22.5s27.4-38 39.9-72.9l92.6-248h-44.8L140.3 236l-37-116.2h-41l54.4 139.8a57.54 57.54 0 0 1 0 41.8c-5.5 14.2-15.4 30.9-42.3 35.9z"})),N||(N=c.createElement("circle",{cx:368.33,cy:124.68,r:97.34,fill:"#9fda4f",transform:"rotate(-45 368.335 124.68)"})),F||(F=c.createElement("path",{fill:"#77b227",d:"m416.2 39.93-95.74 169.51A97.34 97.34 0 1 0 416.2 39.93z"})),$||($=c.createElement("path",{fill:"#fec228",d:"m294.78 254.75-.15-.08-.13-.07a63.6 63.6 0 0 0-62.56 110.76h.13a63.6 63.6 0 0 0 62.71-110.67z"})),U||(U=c.createElement("path",{fill:"#f49a00",d:"m294.5 254.59-62.56 110.76a63.6 63.6 0 1 0 62.56-110.76z"})),j||(j=c.createElement("path",{fill:"#ff4e47",d:"M222.31 450.07A38.16 38.16 0 0 0 203 416.83a38.18 38.18 0 1 0 19.41 33.27z"})),z||(z=c.createElement("path",{fill:"#ed261f",d:"m202.9 416.8-37.54 66.48a38.17 38.17 0 0 0 37.54-66.48z"}))),V=({link:e,linkProps:t,promotions:s})=>{const n=(0,u.useMemo)((()=>(0,g.__)("Use AI to generate titles and meta descriptions, automatically redirect deleted pages, get 24/7 support, and much, much more!","wordpress-seo")),[]),i=(0,u.createInterpolateElement)((0,g.sprintf)(/* translators: %1$s and %2$s expand to a span wrap to avoid linebreaks. %3$s expands to "Yoast SEO Premium". */
(0,g.__)("%1$sGet%2$s %3$s","wordpress-seo"),"<nowrap>","</nowrap>","Yoast SEO Premium"),{nowrap:(0,c.createElement)("span",{className:"yst-whitespace-nowrap"})}),r=s.includes("black-friday-2023-promotion"),o=(0,u.createInterpolateElement)((0,g.sprintf)(/* translators: %1$s and %2$s expand to strong tags. */
(0,g.__)("%1$sSAVE 30%%%2$s on your 12 month subscription","wordpress-seo"),"<strong>","</strong>"),{strong:(0,c.createElement)("strong",null)});return(0,c.createElement)("div",{className:"yst-p-6 yst-rounded-lg yst-text-white yst-bg-primary-500 yst-shadow"},(0,c.createElement)("figure",{className:"yst-logo-square yst-w-16 yst-h-16 yst-mx-auto yst-overflow-hidden yst-border yst-border-white yst-rounded-xl yst-rounded-br-none yst-relative yst-z-10 yst-mt-[-2.6rem]"},(0,c.createElement)(Y,null)),r&&(0,c.createElement)("div",{className:"sidebar__sale_banner_container"},(0,c.createElement)("div",{className:"sidebar__sale_banner"},(0,c.createElement)("span",{className:"banner_text"},(0,g.__)("BLACK FRIDAY - 30% OFF","wordpress-seo")))),(0,c.createElement)(m.Title,{as:"h2",className:"yst-mt-6 yst-text-base yst-font-extrabold yst-text-white"},i),(0,c.createElement)("p",{className:"yst-mt-2"},n),r&&(0,c.createElement)("div",{className:"yst-text-center yst-border-t-[1px] yst-border-white yst-italic yst-mt-3"},(0,c.createElement)("p",{className:"yst-text-[10px] yst-my-3 yst-mx-0"},o)),(0,c.createElement)(m.Button,{as:"a",variant:"upsell",href:e,target:"_blank",rel:"noopener",className:"yst-flex yst-justify-center yst-gap-2 yst-mt-4 focus:yst-ring-offset-primary-500",...t},(0,c.createElement)("span",null,r?(0,g.__)("Claim your 30% off now!","wordpress-seo"):i),(0,c.createElement)(R,{className:"yst-w-4 yst-h-4 yst-icon-rtl"})),(0,c.createElement)("p",{className:"yst-text-center yst-text-xs yst-mx-2 yst-font-light yst-leading-5 yst-mt-2"},(0,g.__)("Only $/€/£99 per year (ex VAT)","wordpress-seo"),(0,c.createElement)("br",null),(0,g.__)("30-day money back guarantee.","wordpress-seo")),(0,c.createElement)("hr",{className:"yst-border-t yst-border-primary-300 yst-my-4"}),(0,c.createElement)("a",{className:"yst-block yst-mt-4 yst-no-underline",href:"https://www.g2.com/products/yoast-yoast/reviews",target:"_blank",rel:"noopener noreferrer"},(0,c.createElement)("span",{className:"yst-font-medium yst-text-white hover:yst-underline"},(0,g.__)("Read reviews from real users","wordpress-seo")),(0,c.createElement)("span",{className:"yst-flex yst-gap-2 yst-mt-2 yst-items-center"},(0,c.createElement)(E,{className:"yst-w-5 yst-h-5"}),(0,c.createElement)("span",{className:"yst-flex yst-gap-1"},(0,c.createElement)(M,{className:"yst-w-5 yst-h-5"}),(0,c.createElement)(M,{className:"yst-w-5 yst-h-5"}),(0,c.createElement)(M,{className:"yst-w-5 yst-h-5"}),(0,c.createElement)(M,{className:"yst-w-5 yst-h-5"}),(0,c.createElement)(A,{className:"yst-w-5 yst-h-5"})),(0,c.createElement)("span",{className:"yst-text-sm yst-font-semibold yst-text-white"},"4.6 / 5"))))};V.propTypes={link:y().string.isRequired,linkProps:y().object,promotions:y().array},V.defaultProps={linkProps:{},promotions:[]},y().node.isRequired;const K=window.yoast.reactHelmet,W="loading",H="showPlay",Q="askPermission",Z="isPlaying",G=({videoId:e,thumbnail:t,wistiaEmbedPermission:s})=>{const[n,i]=(0,u.useState)(s.value?Z:H),r=(0,u.useCallback)((()=>i(Z)),[i]),o=(0,u.useCallback)((()=>{s.value?r():i(Q)}),[s.value,r,i]),a=(0,u.useCallback)((()=>i(H)),[i]),l=(0,u.useCallback)((()=>{s.set(!0),r()}),[s.set,r]);return(0,c.createElement)(c.Fragment,null,s.value&&(0,c.createElement)(K.Helmet,null,(0,c.createElement)("script",{src:"https://fast.wistia.com/assets/external/E-v1.js",async:!0})),(0,c.createElement)("div",{className:"yst-relative yst-w-full yst-h-0 yst-pt-[56.25%] yst-overflow-hidden yst-rounded-md yst-drop-shadow-md yst-bg-white"},n===H&&(0,c.createElement)("button",{type:"button",className:"yst-absolute yst-inset-0 yst-button yst-p-0 yst-border-none yst-bg-white yst-transition-opacity yst-duration-1000 yst-opacity-100",onClick:o},(0,c.createElement)("img",{className:"yst-w-full yst-h-auto",alt:"",loading:"lazy",decoding:"async",...t})),n===Q&&(0,c.createElement)("div",{className:"yst-absolute yst-inset-0 yst-flex yst-flex-col yst-items-center yst-justify-center yst-bg-white"},(0,c.createElement)("p",{className:"yst-max-w-xs yst-mx-auto yst-text-center"},s.status===W&&(0,c.createElement)(m.Spinner,null),s.status!==W&&(0,g.sprintf)(/* translators: %1$s expands to Yoast SEO. %2$s expands to Wistia. */
(0,g.__)("To see this video, you need to allow %1$s to load embedded videos from %2$s.","wordpress-seo"),"Yoast SEO","Wistia")),(0,c.createElement)("div",{className:"yst-flex yst-mt-6 yst-gap-x-4"},(0,c.createElement)(m.Button,{type:"button",variant:"secondary",onClick:a,disabled:s.status===W},(0,g.__)("Deny","wordpress-seo")),(0,c.createElement)(m.Button,{type:"button",variant:"primary",onClick:l,disabled:s.status===W},(0,g.__)("Allow","wordpress-seo")))),s.value&&n===Z&&(0,c.createElement)("div",{className:"yst-absolute yst-w-full yst-h-full yst-top-0 yst-left-0"},null===e&&(0,c.createElement)(m.Spinner,{className:"yst-h-full yst-mx-auto"}),null!==e&&(0,c.createElement)("div",{className:`wistia_embed wistia_async_${e} videoFoam=true`}))))};G.propTypes={videoId:y().string.isRequired,thumbnail:y().shape({src:y().string.isRequired,width:y().string,height:y().string}).isRequired,wistiaEmbedPermission:y().shape({value:y().bool.isRequired,status:y().string.isRequired,set:y().func.isRequired}).isRequired};const J="yoast-seo/editor",X=()=>{const e=(0,d.useSelect)((e=>e(J).selectLink("https://yoa.st/ai-generator-learn-more")),[]),t=(0,d.useSelect)((e=>e(J).selectLink("https://yoa.st/ai-generator-upsell")),[]),s=(0,d.useSelect)((e=>e(J).selectLink("https://yoa.st/ai-generator-upsell-woo-seo-premium-bundle")),[]),n=(0,d.useSelect)((e=>e(J).selectLink("https://yoa.st/ai-generator-upsell-woo-seo")),[]),i=(0,d.useSelect)((e=>e(J).getIsPremium()),[]),r=(0,d.useSelect)((e=>e(J).getIsWooSeoUpsell()),[]),o=(0,d.useSelect)((e=>e(J).getIsProduct()),[]),a=!(!r&&(!o||r||i)),l={isProductCopy:a,upsellLink:t};if(a){const e=(0,g.sprintf)(/* translators: %1$s expands to Yoast SEO Premium, %2$s expands to Yoast WooCommerce SEO. */
(0,g.__)("%1$s + %2$s","wordpress-seo"),"Yoast SEO Premium","Yoast WooCommerce SEO");l.newToText=(0,g.sprintf)(/* translators: %1$s expands to Yoast SEO Premium and Yoast WooCommerce SEO. */
(0,g.__)("New in %1$s","wordpress-seo"),e),l.title=(0,g.__)("Generate product titles & descriptions with AI!","wordpress-seo"),!i&&r&&(l.upsellLabel=`${(0,g.sprintf)(/* translators: %1$s expands to Woo Premium bundle. */
(0,g.__)("Unlock with the %1$s","wordpress-seo"),"Woo Premium bundle")}*`,l.bundleNote=(0,c.createElement)("div",{className:"yst-text-xs yst-text-slate-500 yst-mt-2"},`*${e}`),l.upsellLink=s),i&&(l.upsellLabel=(0,g.sprintf)(/* translators: %1$s expands to Yoast WooCommerce SEO. */
(0,g.__)("Unlock with %1$s","wordpress-seo"),"Yoast WooCommerce SEO"),l.upsellLink=n)}const p=(0,d.useSelect)((e=>e(J).selectImageLink("ai-generator-preview.png")),[]),h=(0,u.useMemo)((()=>({src:p,width:"432",height:"244"})),[p]),m=(0,d.useSelect)((e=>e(J).selectWistiaEmbedPermissionValue()),[]),f=(0,d.useSelect)((e=>e(J).selectWistiaEmbedPermissionStatus()),[]),{setWistiaEmbedPermission:y}=(0,d.useDispatch)(J),w=(0,u.useMemo)((()=>({value:m,status:f,set:y})),[m,f,y]);return(0,c.createElement)(k,{learnMoreLink:e,thumbnail:h,wistiaEmbedPermission:w,...l})},ee=({fieldId:e})=>{const[t,,,s,n]=(0,m.useToggleState)(!1),i=(0,u.useCallback)((()=>{s()}),[s]),r=(0,u.useRef)(null);return(0,c.createElement)(c.Fragment,null,(0,c.createElement)("button",{type:"button",id:`yst-replacevar__use-ai-button__${e}`,className:"yst-replacevar__use-ai-button-upsell",onClick:i},(0,g.__)("Use AI","wordpress-seo")),(0,c.createElement)(m.Modal,{className:"yst-introduction-modal",isOpen:t,onClose:n,initialFocus:r},(0,c.createElement)(m.Modal.Panel,{className:"yst-max-w-lg yst-p-0 yst-rounded-3xl yst-introduction-modal-panel"},(0,c.createElement)(X,{onClose:n,focusElementRef:r}))))};ee.propTypes={fieldId:y().string.isRequired};const te="yoast-seo/editor";const se=window.yoast.externals.redux,ne=window.yoast.reduxJsToolkit,ie=window.wp.url,re="linkParams",oe=(0,ne.createSlice)({name:re,initialState:{},reducers:{setLinkParams:(e,{payload:t})=>t}}),ae=(oe.getInitialState,{selectLinkParam:(e,t,s={})=>(0,l.get)(e,`${re}.${t}`,s),selectLinkParams:e=>(0,l.get)(e,re,{})});ae.selectLink=(0,ne.createSelector)([ae.selectLinkParams,(e,t)=>t,(e,t,s={})=>s],((e,t,s)=>(0,ie.addQueryArgs)(t,{...e,...s}))),oe.actions,oe.reducer;const le=(0,ne.createSlice)({name:"notifications",initialState:{},reducers:{addNotification:{reducer:(e,{payload:t})=>{e[t.id]={id:t.id,variant:t.variant,size:t.size,title:t.title,description:t.description}},prepare:({id:e,variant:t="info",size:s="default",title:n,description:i})=>({payload:{id:e||(0,ne.nanoid)(),variant:t,size:s,title:n||"",description:i}})},removeNotification:(e,{payload:t})=>(0,l.omit)(e,t)}}),ce=(le.getInitialState,le.actions,le.reducer,"pluginUrl"),pe=(0,ne.createSlice)({name:ce,initialState:"",reducers:{setPluginUrl:(e,{payload:t})=>t}}),de=(pe.getInitialState,{selectPluginUrl:e=>(0,l.get)(e,ce,"")});de.selectImageLink=(0,ne.createSelector)([de.selectPluginUrl,(e,t,s="images")=>s,(e,t)=>t],((e,t,s)=>[(0,l.trimEnd)(e,"/"),(0,l.trim)(t,"/"),(0,l.trimStart)(s,"/")].join("/"))),pe.actions,pe.reducer;const ue=window.wp.apiFetch;var he=s.n(ue);const ge="wistiaEmbedPermission",me=(0,ne.createSlice)({name:ge,initialState:{value:!1,status:"idle",error:{}},reducers:{setWistiaEmbedPermissionValue:(e,{payload:t})=>{e.value=Boolean(t)}},extraReducers:e=>{e.addCase(`${ge}/request`,(e=>{e.status=W})),e.addCase(`${ge}/success`,((e,{payload:t})=>{e.status="success",e.value=Boolean(t&&t.value)})),e.addCase(`${ge}/error`,((e,{payload:t})=>{e.status="error",e.value=Boolean(t&&t.value),e.error={code:(0,l.get)(t,"error.code",500),message:(0,l.get)(t,"error.message","Unknown")}}))}}),fe=(me.getInitialState,me.actions,{[ge]:async({payload:e})=>he()({path:"/yoast/v1/wistia_embed_permission",method:"POST",data:{value:Boolean(e)}})});function ye({alertKey:e}){return new Promise((t=>wpseoApi.post("alerts/dismiss",{key:e},(()=>t()))))}function we({query:e,postId:t}){return new Promise((s=>{wpseoApi.get("meta/search",{query:e,post_id:t},(e=>{s(e.meta)}))}))}me.reducer;const be=fe[ge];var _e=s(2322),ke=s.n(_e);function ve(){return window.wpseoScriptData&&"1"===window.wpseoScriptData.isBlockEditor}const xe=window.yoast.analysis,Ee=window.wp.isShallowEqual,Se=window.wp.api;var Re={source:"wpseoScriptData.analysis.plugins.replaceVars",scope:[],aliases:[]},Ce=function(e,t,s){this.placeholder=e,this.replacement=t,this.options=(0,l.defaults)(s,Re)};Ce.prototype.getPlaceholder=function(e){return(e=e||!1)&&this.hasAlias()?this.placeholder+"|"+this.getAliases().join("|"):this.placeholder},Ce.prototype.setSource=function(e){this.options.source=e},Ce.prototype.hasScope=function(){return!(0,l.isEmpty)(this.options.scope)},Ce.prototype.addScope=function(e){this.hasScope()||(this.options.scope=[]),this.options.scope.push(e)},Ce.prototype.inScope=function(e){return!this.hasScope()||(0,l.indexOf)(this.options.scope,e)>-1},Ce.prototype.hasAlias=function(){return!(0,l.isEmpty)(this.options.aliases)},Ce.prototype.addAlias=function(e){this.hasAlias()||(this.options.aliases=[]),this.options.aliases.push(e)},Ce.prototype.getAliases=function(){return this.options.aliases};const Te=Ce,{removeReplacementVariable:Pe,updateReplacementVariable:Oe,refreshSnippetEditor:Ae}=se.actions;var Ie=["content","title","snippet_title","snippet_meta","primary_category","data_page_title","data_meta_desc","excerpt"],Me={},De={},Le=function(e,t){this._app=e,this._app.registerPlugin("replaceVariablePlugin",{status:"ready"}),this._store=t,this.replaceVariables=this.replaceVariables.bind(this),this.registerReplacements(),this.registerModifications(),this.registerEvents(),this.subscribeToGutenberg()};Le.prototype.registerReplacements=function(){this.addReplacement(new Te("%%author_first_name%%","author_first_name")),this.addReplacement(new Te("%%author_last_name%%","author_last_name")),this.addReplacement(new Te("%%category%%","category")),this.addReplacement(new Te("%%category_title%%","category_title")),this.addReplacement(new Te("%%currentdate%%","currentdate")),this.addReplacement(new Te("%%currentday%%","currentday")),this.addReplacement(new Te("%%currentmonth%%","currentmonth")),this.addReplacement(new Te("%%currenttime%%","currenttime")),this.addReplacement(new Te("%%currentyear%%","currentyear")),this.addReplacement(new Te("%%date%%","date")),this.addReplacement(new Te("%%id%%","id")),this.addReplacement(new Te("%%page%%","page")),this.addReplacement(new Te("%%permalink%%","permalink")),this.addReplacement(new Te("%%post_content%%","post_content")),this.addReplacement(new Te("%%post_month%%","post_month")),this.addReplacement(new Te("%%post_year%%","post_year")),this.addReplacement(new Te("%%searchphrase%%","searchphrase")),this.addReplacement(new Te("%%sitedesc%%","sitedesc")),this.addReplacement(new Te("%%sitename%%","sitename")),this.addReplacement(new Te("%%userid%%","userid")),this.addReplacement(new Te("%%focuskw%%","keyword",{source:"app",aliases:["%%keyword%%"]})),this.addReplacement(new Te("%%term_description%%","text",{source:"app",scope:["term","category","tag"],aliases:["%%tag_description%%","%%category_description%%"]})),this.addReplacement(new Te("%%term_title%%","term_title",{scope:["term"]})),this.addReplacement(new Te("%%term_hierarchy%%","term_hierarchy",{scope:["term"]})),this.addReplacement(new Te("%%title%%","title",{source:"app",scope:["post","term","page"]})),this.addReplacement(new Te("%%parent_title%%","title",{source:"app",scope:["page","category"]})),this.addReplacement(new Te("%%excerpt%%","excerpt",{source:"app",scope:["post"],aliases:["%%excerpt_only%%"]})),this.addReplacement(new Te("%%primary_category%%","primaryCategory",{source:"app",scope:["post"]})),this.addReplacement(new Te("%%sep%%(\\s*%%sep%%)*","sep"))},Le.prototype.registerEvents=function(){const e=wpseoScriptData.analysis.plugins.replaceVars.scope;"post"===e&&jQuery(".categorydiv").each(this.bindTaxonomyEvents.bind(this)),"post"!==e&&"page"!==e||jQuery("#postcustomstuff > #list-table").each(this.bindFieldEvents.bind(this))},Le.prototype.subscribeToGutenberg=function(){if(!ve())return;const e={0:""};let t=null;const s=wp.data;s.subscribe((()=>{const n=s.select("core/editor").getEditedPostAttribute("parent");if(void 0!==n&&t!==n)return t=n,n<1?(this._currentParentPageTitle="",void this.declareReloaded()):(0,l.isUndefined)(e[n])?void Se.loadPromise.done((()=>{new Se.models.Page({id:n}).fetch().then((t=>{this._currentParentPageTitle=t.title.rendered,e[n]=this._currentParentPageTitle,this.declareReloaded()})).fail((()=>{this._currentParentPageTitle="",this.declareReloaded()}))})):(this._currentParentPageTitle=e[n],void this.declareReloaded())}))},Le.prototype.addReplacement=function(e){Me[e.placeholder]=e},Le.prototype.removeReplacement=function(e){delete Me[e.getPlaceholder()]},Le.prototype.registerModifications=function(){var e=this.replaceVariables.bind(this);(0,l.forEach)(Ie,function(t){this._app.registerModification(t,e,"replaceVariablePlugin",10)}.bind(this))},Le.prototype.replaceVariables=function(e){return(0,l.isUndefined)(e)||(e=this.parentReplace(e),e=this.replaceCustomTaxonomy(e),e=this.replaceByStore(e),e=this.replacePlaceholders(e)),e},Le.prototype.replaceByStore=function(e){const t=this._store.getState().snippetEditor.replacementVariables;return(0,l.forEach)(t,(t=>{""!==t.value&&(e=e.replace("%%"+t.name+"%%",t.value))})),e},Le.prototype.getReplacementSource=function(e){return"app"===e.source?this._app.rawData:"direct"===e.source?"direct":wpseoScriptData.analysis.plugins.replaceVars.replace_vars},Le.prototype.getReplacement=function(e){var t=this.getReplacementSource(e.options);return!1===e.inScope(wpseoScriptData.analysis.plugins.replaceVars.scope)?"":"direct"===t?e.replacement:t[e.replacement]||""},Le.prototype.replacePlaceholders=function(e){return(0,l.forEach)(Me,function(t){e=e.replace(new RegExp(t.getPlaceholder(!0),"g"),this.getReplacement(t))}.bind(this)),e},Le.prototype.declareReloaded=function(){this._app.pluginReloaded("replaceVariablePlugin"),this._store.dispatch(Ae())},Le.prototype.getCategoryName=function(e){var t=e.parent("label").clone();return t.children().remove(),t.text().trim()},Le.prototype.parseTaxonomies=function(e,t){(0,l.isUndefined)(De[t])&&(De[t]={});const s=[];(0,l.forEach)(e,function(e){const n=(e=jQuery(e)).val(),i=this.getCategoryName(e),r=e.prop("checked");De[t][n]={label:i,checked:r},r&&-1===s.indexOf(i)&&s.push(i)}.bind(this)),"category"!==t&&(t="ct_"+t),this._store.dispatch(Oe(t,s.join(", ")))},Le.prototype.getAvailableTaxonomies=function(e){var t=jQuery(e).find("input[type=checkbox]"),s=jQuery(e).attr("id").replace("taxonomy-","");t.length>0&&this.parseTaxonomies(t,s),this.declareReloaded()},Le.prototype.bindTaxonomyEvents=function(e,t){(t=jQuery(t)).on("wpListAddEnd",".categorychecklist",this.getAvailableTaxonomies.bind(this,t)),t.on("change","input[type=checkbox]",this.getAvailableTaxonomies.bind(this,t)),this.getAvailableTaxonomies(t)},Le.prototype.replaceCustomTaxonomy=function(e){return(0,l.forEach)(De,function(t,s){var n="%%ct_"+s+"%%";"category"===s&&(n="%%"+s+"%%"),e=e.replace(n,this.getTaxonomyReplaceVar(s))}.bind(this)),e},Le.prototype.getTaxonomyReplaceVar=function(e){var t=[],s=De[e];return!0===(0,l.isUndefined)(s)?"":((0,l.forEach)(s,(function(e){!1!==e.checked&&t.push(e.label)})),jQuery.uniqueSort(t).join(", "))},Le.prototype.parseFields=function(e){jQuery(e).each(function(e,t){var s=jQuery("#"+t.id+"-key").val(),n=jQuery("#"+t.id+"-value").val();const i="cf_"+this.sanitizeCustomFieldNames(s),r=s+" (custom field)";this._store.dispatch(Oe(i,n,r)),this.addReplacement(new Te(`%%${i}%%`,n,{source:"direct"}))}.bind(this))},Le.prototype.removeFields=function(e){jQuery(e).each(function(e,t){var s=jQuery("#"+t.id+"-key").val();this.removeReplacement("%%cf_"+this.sanitizeCustomFieldNames(s)+"%%")}.bind(this))},Le.prototype.sanitizeCustomFieldNames=function(e){return e.replace(/\s/g,"_")},Le.prototype.getAvailableFields=function(e){this.removeCustomFields();var t=jQuery(e).find("#the-list > tr:visible[id]");t.length>0&&this.parseFields(t),this.declareReloaded()},Le.prototype.bindFieldEvents=function(e,t){var s=(t=jQuery(t)).find("#the-list");s.on("wpListDelEnd.wpseoCustomFields",this.getAvailableFields.bind(this,t)),s.on("wpListAddEnd.wpseoCustomFields",this.getAvailableFields.bind(this,t)),s.on("input.wpseoCustomFields",".textarea",this.getAvailableFields.bind(this,t)),s.on("click.wpseoCustomFields",".button + .updatemeta",this.getAvailableFields.bind(this,t)),this.getAvailableFields(t)},Le.prototype.removeCustomFields=function(){var e=(0,l.filter)(Me,(function(e,t){return t.indexOf("%%cf_")>-1}));(0,l.forEach)(e,function(e){this._store.dispatch(Pe((0,l.trim)(e.placeholder,"%%"))),this.removeReplacement(e)}.bind(this))},Le.prototype.parentReplace=function(e){const t=jQuery("#parent_id, #parent").eq(0);return this.hasParentTitle(t)&&(e=e.replace(/%%parent_title%%/,this.getParentTitleReplacement(t))),ve()&&!(0,l.isUndefined)(this._currentParentPageTitle)&&(e=e.replace(/%%parent_title%%/,this._currentParentPageTitle)),e},Le.prototype.hasParentTitle=function(e){return!(0,l.isUndefined)(e)&&!(0,l.isUndefined)(e.prop("options"))},Le.prototype.getParentTitleReplacement=function(e){var t=e.find("option:selected").text();return t===wpseoScriptData.analysis.plugins.replaceVars.no_parent_text?"":t},Le.ReplaceVar=Te;const Be=Le,Ne=window.wp.blocks,Fe=class{constructor(e,t,s){this._registerPlugin=e,this._registerModification=t,this._refreshAnalysis=s,this._reusableBlocks={},this._selectCore=(0,d.select)("core"),this._selectCoreEditor=(0,d.select)("core/editor"),this.reusableBlockChangeListener=this.reusableBlockChangeListener.bind(this),this.parseReusableBlocks=this.parseReusableBlocks.bind(this)}register(){this._registerPlugin("YoastReusableBlocksPlugin",{status:"ready"}),this._registerModification("content",this.parseReusableBlocks,"YoastReusableBlocksPlugin",1),(0,d.subscribe)((0,l.debounce)(this.reusableBlockChangeListener,500))}reusableBlockChangeListener(){const{blocks:e}=this._selectCoreEditor.getPostEdits();if(!e)return;let t=!1;e.forEach((e=>{if(!(0,Ne.isReusableBlock)(e))return;const s=this.getBlockContent(e.attributes.ref);this._reusableBlocks[e.attributes.ref]?this._reusableBlocks[e.attributes.ref].content!==s&&(this._reusableBlocks[e.attributes.ref].content=s,t=!0):(this._reusableBlocks[e.attributes.ref]={id:e.attributes.ref,clientId:e.clientId,content:s},t=!0)})),t&&this._refreshAnalysis()}parseReusableBlocks(e){const t=/<!-- wp:block {"ref":(\d+)} \/-->/g;return e.match(t)?e.replace(t,((t,s)=>this._reusableBlocks[s]&&this._reusableBlocks[s].content?this._reusableBlocks[s].content:e)):e}getBlockContent(e){const t=this._selectCore.getEditedEntityRecord("postType","wp_block",e);if(t){if((0,l.isFunction)(t.content))return t.content(t);if(t.blocks)return(0,Ne.__unstableSerializeAndClean)(t.blocks);if(t.content)return t.content}return""}},$e="[^<>&/\\[\\]\0- =]+?",Ue=new RegExp("\\["+$e+"( [^\\]]+?)?\\]","g"),je=new RegExp("\\[/"+$e+"\\]","g");class ze{constructor({registerPlugin:e,registerModification:t,pluginReady:s,pluginReloaded:n},i){this._registerModification=t,this._pluginReady=s,this._pluginReloaded=n,e("YoastShortcodePlugin",{status:"loading"}),this.bindElementEvents();const r="("+i.join("|")+")";this.shortcodesRegex=new RegExp(r,"g"),this.closingTagRegex=new RegExp("\\[\\/"+r+"\\]","g"),this.nonCaptureRegex=new RegExp("\\["+r+"[^\\]]*?\\]","g"),this.parsedShortcodes=[],this.loadShortcodes(this.declareReady.bind(this))}declareReady(){this._pluginReady("YoastShortcodePlugin"),this.registerModifications()}declareReloaded(){this._pluginReloaded("YoastShortcodePlugin")}registerModifications(){this._registerModification("content",this.replaceShortcodes.bind(this),"YoastShortcodePlugin")}removeUnknownShortCodes(e){return(e=e.replace(Ue,"")).replace(je,"")}replaceShortcodes(e){return"string"==typeof e&&this.parsedShortcodes.forEach((({shortcode:t,output:s})=>{e=e.replace(t,s)})),e=this.removeUnknownShortCodes(e)}loadShortcodes(e){const t=this.getUnparsedShortcodes(this.getShortcodes(this.getContentTinyMCE()));if(!(t.length>0))return e();this.parseShortcodes(t,e)}bindElementEvents(){const e=document.querySelector(".wp-editor-area"),t=(0,l.debounce)(this.loadShortcodes.bind(this,this.declareReloaded.bind(this)),500);e&&(e.addEventListener("keyup",t),e.addEventListener("change",t)),"undefined"!=typeof tinyMCE&&"function"==typeof tinyMCE.on&&tinyMCE.on("addEditor",(function(e){e.editor.on("change",t),e.editor.on("keyup",t)}))}getContentTinyMCE(){let e=document.querySelector(".wp-editor-area")?document.querySelector(".wp-editor-area").value:"";return"undefined"!=typeof tinyMCE&&void 0!==tinyMCE.editors&&0!==tinyMCE.editors.length&&(e=tinyMCE.get("content")?tinyMCE.get("content").getContent():""),e}getUnparsedShortcodes(e){return"object"!=typeof e?(console.error("Failed to get unparsed shortcodes. Expected parameter to be an array, instead received "+typeof e),!1):e.filter((e=>this.isUnparsedShortcode(e)))}isUnparsedShortcode(e){return!this.parsedShortcodes.some((({shortcode:t})=>t===e))}getShortcodes(e){if("string"!=typeof e)return console.error("Failed to get shortcodes. Expected parameter to be a string, instead received"+typeof e),!1;const t=this.matchCapturingShortcodes(e);t.forEach((t=>{e=e.replace(t,"")}));const s=this.matchNonCapturingShortcodes(e);return t.concat(s)}matchCapturingShortcodes(e){const t=(e.match(this.closingTagRegex)||[]).join(" ").match(this.shortcodesRegex)||[];return(0,l.flatten)(t.map((t=>{const s="\\["+t+"[^\\]]*?\\].*?\\[\\/"+t+"\\]";return e.match(new RegExp(s,"g"))||[]})))}matchNonCapturingShortcodes(e){return e.match(this.nonCaptureRegex)||[]}parseShortcodes(e,t){return"function"!=typeof t?(console.error("Failed to parse shortcodes. Expected parameter to be a function, instead received "+typeof t),!1):"object"==typeof e&&e.length>0?void jQuery.post(ajaxurl,{action:"wpseo_filter_shortcodes",_wpnonce:wpseoScriptData.analysis.plugins.shortcodes.wpseo_filter_shortcodes_nonce,data:e},function(e){this.saveParsedShortcodes(e,t)}.bind(this)):t()}saveParsedShortcodes(e,t){const s=JSON.parse(e);this.parsedShortcodes.push(...s),t()}}const qe=ze,{updateShortcodesForParsing:Ye}=se.actions;var Ve=s(7084),Ke=s.n(Ve);const We=class{constructor(e,t){this._registerPlugin=e,this._registerModification=t}register(){this._registerPlugin("YoastMarkdownPlugin",{status:"ready"}),this._registerModification("content",this.parseMarkdown.bind(this),"YoastMarkdownPlugin",1)}parseMarkdown(e){return Ke()(e)}},He="yoastmark";function Qe(e,t){return e._properties.position.startOffset>t.length||e._properties.position.endOffset>t.length}function Ze(e,t,s){const n=e.dom;let i=e.getContent();if(i=xe.markers.removeMarks(i),(0,l.isEmpty)(s))return void e.setContent(i);i=s[0].hasPosition()?function(e,t){if(!t)return"";for(let s=(e=(0,l.orderBy)(e,(e=>e._properties.position.startOffset),["asc"])).length-1;s>=0;s--){const n=e[s];Qe(n,t)||(t=n.applyWithPosition(t))}return t}(s,i):function(e,t,s,n){const{fieldsToMark:i,selectedHTML:r}=xe.languageProcessing.getFieldsToMark(s,n);return(0,l.forEach)(s,(function(t){"acf_content"!==e.id&&(t._properties.marked=xe.languageProcessing.normalizeHTML(t._properties.marked),t._properties.original=xe.languageProcessing.normalizeHTML(t._properties.original)),i.length>0?r.forEach((e=>{const s=t.applyWithReplace(e);n=n.replace(e,s)})):n=t.applyWithReplace(n)})),n}(e,0,s,i),e.setContent(i),function(e){let t=e.getContent();t=t.replace(new RegExp("&lt;yoastmark.+?&gt;","g"),"").replace(new RegExp("&lt;/yoastmark&gt;","g"),""),e.setContent(t)}(e);const r=n.select(He);(0,l.forEach)(r,(function(e){e.setAttribute("data-mce-bogus","1")}))}function Ge(e){return window.test=e,Ze.bind(null,e)}const Je="et_pb_main_editor_wrap",Xe=class{static isActive(){return!!document.getElementById(Je)}static isTinyMCEHidden(){const e=document.getElementById(Je);return!!e&&e.classList.contains("et_pb_hidden")}listen(e){this.classicEditorContainer=document.getElementById(Je),this.classicEditorContainer&&new MutationObserver((t=>{(0,l.forEach)(t,(t=>{"attributes"===t.type&&"class"===t.attributeName&&(t.target.classList.contains("et_pb_hidden")?e.classicEditorHidden():e.classicEditorShown())}))})).observe(this.classicEditorContainer,{attributes:!0})}},et=class{static isActive(){return!!window.VCV_I18N}},tt={classicEditorHidden:l.noop,classicEditorShown:l.noop,pageBuilderLoaded:l.noop},st=class{constructor(){this.determineActivePageBuilders()}determineActivePageBuilders(){Xe.isActive()&&(this.diviActive=!0),et.isActive()&&(this.vcActive=!0)}isPageBuilderActive(){return this.diviActive||this.vcActive}listen(e){this.callbacks=(0,l.defaults)(e,tt),this.diviActive&&(new Xe).listen(e)}isClassicEditorHidden(){return!(!this.diviActive||!Xe.isTinyMCEHidden())}};let nt;const it="content",rt="description";function ot(e){nt=e}function at(){return"undefined"!=typeof tinyMCE&&void 0!==tinyMCE.editors&&0!==tinyMCE.editors.length}function lt(e){if(!at())return!1;const t=tinyMCE.get(e);return null!==t&&!t.isHidden()}function ct(e){let t="";var s;return t=!1===lt(e)||0==(s=e,null!==document.getElementById(s+"_ifr"))?function(e){return document.getElementById(e)&&document.getElementById(e).value||""}(e):tinyMCE.get(e).getContent(),t}function pt(e,t,s){"undefined"!=typeof tinyMCE&&"function"==typeof tinyMCE.on&&tinyMCE.on("addEditor",(function(n){const i=n.editor;i.id===e&&(0,l.forEach)(t,(function(e){i.on(e,s)}))}))}function dt(){(0,l.isUndefined)(nt)||nt.dispatch(se.actions.setMarkerStatus("disabled"))}function ut(){(0,l.isUndefined)(nt)||nt.dispatch(se.actions.setMarkerStatus("enabled"))}function ht(){(0,l.isUndefined)(nt)||nt.dispatch(se.actions.setMarkerPauseStatus(!0))}function gt(){(0,l.isUndefined)(nt)||nt.dispatch(se.actions.setMarkerPauseStatus(!1))}function mt(){const e=document.getElementById("wp-content-wrap");return!!e&&e.classList.contains("html-active")}function ft(){mt()&&(dt(),at()&&tinyMCE.on("AddEditor",(function(){ut()})))}function yt(e,t){pt(t,["input","change","cut","paste"],e),pt(t,["hide"],dt);const s=["show"];(new st).isPageBuilderActive()||s.push("init"),pt(t,s,ut),pt("content",["focus"],(function(e){const t=e.target;(function(e){return-1!==e.getContent({format:"raw"}).indexOf("<"+He)})(t)&&(function(e){Ge(e)(null,[])}(t),YoastSEO.app.disableMarkers()),ht()})),pt("content",["blur"],(function(){gt()}))}class wt{constructor(e){this.refresh=e,this.loaded=!1,this.preloadThreshold=3e3,this.plugins={},this.modifications={},this._registerPlugin=this._registerPlugin.bind(this),this._ready=this._ready.bind(this),this._reloaded=this._reloaded.bind(this),this._registerModification=this._registerModification.bind(this),this._registerAssessment=this._registerAssessment.bind(this),this._applyModifications=this._applyModifications.bind(this),setTimeout(this._pollLoadingPlugins.bind(this),1500)}_registerPlugin(e,t){return(0,l.isString)(e)?(0,l.isUndefined)(t)||(0,l.isObject)(t)?!1===this._validateUniqueness(e)?(console.error("Failed to register plugin. Plugin with name "+e+" already exists"),!1):(this.plugins[e]=t,!0):(console.error("Failed to register plugin "+e+". Expected parameters `options` to be a object."),!1):(console.error("Failed to register plugin. Expected parameter `pluginName` to be a string."),!1)}_ready(e){return(0,l.isString)(e)?(0,l.isUndefined)(this.plugins[e])?(console.error("Failed to modify status for plugin "+e+". The plugin was not properly registered."),!1):(this.plugins[e].status="ready",!0):(console.error("Failed to modify status for plugin "+e+". Expected parameter `pluginName` to be a string."),!1)}_reloaded(e){return(0,l.isString)(e)?(0,l.isUndefined)(this.plugins[e])?(console.error("Failed to reload Content Analysis for plugin "+e+". The plugin was not properly registered."),!1):(this.refresh(),!0):(console.error("Failed to reload Content Analysis for "+e+". Expected parameter `pluginName` to be a string."),!1)}_registerModification(e,t,s,n){if(!(0,l.isString)(e))return console.error("Failed to register modification for plugin "+s+". Expected parameter `modification` to be a string."),!1;if(!(0,l.isFunction)(t))return console.error("Failed to register modification for plugin "+s+". Expected parameter `callable` to be a function."),!1;if(!(0,l.isString)(s))return console.error("Failed to register modification for plugin "+s+". Expected parameter `pluginName` to be a string."),!1;if(!1===this._validateOrigin(s))return console.error("Failed to register modification for plugin "+s+". The integration has not finished loading yet."),!1;const i={callable:t,origin:s,priority:(0,l.isNumber)(n)?n:10};return(0,l.isUndefined)(this.modifications[e])&&(this.modifications[e]=[]),this.modifications[e].push(i),!0}_registerAssessment(e,t,s,n){return(0,l.isString)(t)?(0,l.isObject)(s)?(0,l.isString)(n)?(t=n+"-"+t,e.addAssessment(t,s),!0):(console.error("Failed to register assessment for plugin "+n+". Expected parameter `pluginName` to be a string."),!1):(console.error("Failed to register assessment for plugin "+n+". Expected parameter `assessment` to be a function."),!1):(console.error("Failed to register test for plugin "+n+". Expected parameter `name` to be a string."),!1)}_applyModifications(e,t,s){let n=this.modifications[e];return!(0,l.isArray)(n)||n.length<1||(n=this._stripIllegalModifications(n),n.sort(((e,t)=>e.priority-t.priority)),(0,l.forEach)(n,(function(n){const i=n.callable(t,s);typeof i==typeof t?t=i:console.error("Modification with name "+e+" performed by plugin with name "+n.origin+" was ignored because the data that was returned by it was of a different type than the data we had passed it.")}))),t}_pollLoadingPlugins(e){e=(0,l.isUndefined)(e)?0:e,!0===this._allReady()?(this.loaded=!0,this.refresh()):e>=this.preloadThreshold?(this._pollTimeExceeded(),this.loaded=!0,this.refresh()):(e+=50,setTimeout(this._pollLoadingPlugins.bind(this,e),50))}_allReady(){return(0,l.reduce)(this.plugins,(function(e,t){return e&&"ready"===t.status}),!0)}_pollTimeExceeded(){(0,l.forEach)(this.plugins,(function(e,t){(0,l.isUndefined)(e.options)||"ready"===e.options.status||(console.error("Error: Plugin "+t+". did not finish loading in time."),delete this.plugins[t])}))}_stripIllegalModifications(e){return(0,l.forEach)(e,((t,s)=>{!1===this._validateOrigin(t.origin)&&delete e[s]})),e}_validateOrigin(e){return"ready"===this.plugins[e].status}_validateUniqueness(e){return(0,l.isUndefined)(this.plugins[e])}}function bt(e,t,s){e("morphology",new xe.Paper("",{keyword:s})).then((e=>{const s=e.result.keyphraseForms;t.dispatch(se.actions.updateWordsToHighlight((0,l.uniq)((0,l.flatten)(s))))})).catch((()=>{t.dispatch(se.actions.updateWordsToHighlight([]))}))}var _t="score-text",kt="image yoast-logo svg",vt=jQuery;function xt(e,t,s=null){return null!==s?(0,l.get)(s,t,""):(0,l.get)(wpseoScriptData,`metabox.publish_box.labels.${e}.${t}`,"")}function Et(e,t,s=null){var n=vt("#"+e+"-score"),i=kt+" "+t;n.children(".image").attr("class",i);var r=xt(e,t,s);n.children("."+_t).html(r)}function St(e,t,s=null){const n=vt("<div />",{class:"misc-pub-section yoast yoast-seo-score "+e+"-score",id:e+"-score"}),i=vt("<span />",{class:_t,html:xt(e,t,s)}),r=vt("<span>").attr("class",kt+" na");n.append(r).append(i),vt("#yoast-seo-publishbox-section").append(n)}function Rt(e){const t=vt("#wpadminbar"),s=vt(e);if(!t||!s)return;const n="fixed"===t.css("position")?t.height():0;vt([document.documentElement,document.body]).animate({scrollTop:s.offset().top-n},1e3),s.trigger("focus"),0===s.parent().siblings().length&&s.trigger("click")}function Ct(){var e="na";wpseoScriptData.metabox.keywordAnalysisActive&&St("keyword",e),wpseoScriptData.metabox.contentAnalysisActive&&St("content",e),wpseoScriptData.metabox.inclusiveLanguageAnalysisActive&&St("inclusive-language",e),vt("#content-score").on("click","[href='#yoast-readability-analysis-collapsible-metabox']",(function(e){e.preventDefault(),document.querySelector("#wpseo-meta-tab-readability").click(),Rt("#wpseo-meta-section-readability")})),vt("#keyword-score").on("click","[href='#yoast-seo-analysis-collapsible-metabox']",(function(e){e.preventDefault(),document.querySelector("#wpseo-meta-tab-content").click(),Rt("#yoast-seo-analysis-collapsible-metabox")})),vt("#inclusive-language-score").on("click","[href='#yoast-inclusive-language-analysis-collapsible-metabox']",(function(e){e.preventDefault(),document.querySelector("#wpseo-meta-tab-inclusive-language").click(),Rt("#wpseo-meta-section-inclusive-language")}))}function Tt(e){var t=jQuery(".yst-traffic-light"),s=t.closest(".wpseo-meta-section-link"),n=jQuery("#wpseo-traffic-light-desc"),i=e.className||"na";t.attr("class","yst-traffic-light "+i),s.attr("aria-describedby","wpseo-traffic-light-desc"),n.length>0?n.text(e.screenReaderText):s.closest("li").append("<span id='wpseo-traffic-light-desc' class='screen-reader-text'>"+e.screenReaderText+"</span>")}function Pt(e){jQuery("#wp-admin-bar-wpseo-menu .wpseo-score-icon").attr("title",e.screenReaderText).attr("class","wpseo-score-icon "+e.className).find(".wpseo-score-text").text(e.screenReaderText)}function Ot(){return(0,l.get)(window,"wpseoScriptData.metabox",{intl:{},isRtl:!1})}function At(){const e=Ot();return(0,l.get)(e,"contentLocale","en_US")}function It(){const e=Ot();return(0,l.get)(e,"translations",{domain:"wordpress-seo",locale_data:{"wordpress-seo":{"":{}}}})}function Mt(){const e=Ot();return!0===(0,l.get)(e,"contentAnalysisActive",!1)}function Dt(){const e=Ot();return!0===(0,l.get)(e,"keywordAnalysisActive",!1)}function Lt(){const e=Ot();return!0===(0,l.get)(e,"inclusiveLanguageAnalysisActive",!1)}const Bt=window.yoast.featureFlag;function Nt(){}let Ft=!1;function $t(e){return e.sort(((e,t)=>e._identifier.localeCompare(t._identifier)))}function Ut(e,t,s,n,i){if(!Ft)return;const r=xe.Paper.parse(t());e.analyze(r).then((o=>{const{result:{seo:a,readability:l,inclusiveLanguage:c}}=o;if(a){const e=a[""];e.results.forEach((e=>{e.getMarker=()=>()=>s(r,e.marks)})),e.results=$t(e.results),n.dispatch(se.actions.setSeoResultsForKeyword(r.getKeyword(),e.results)),n.dispatch(se.actions.setOverallSeoScore(e.score,r.getKeyword())),n.dispatch(se.actions.refreshSnippetEditor()),i.saveScores(e.score,r.getKeyword())}l&&(l.results.forEach((e=>{e.getMarker=()=>()=>s(r,e.marks)})),l.results=$t(l.results),n.dispatch(se.actions.setReadabilityResults(l.results)),n.dispatch(se.actions.setOverallReadabilityScore(l.score)),n.dispatch(se.actions.refreshSnippetEditor()),i.saveContentScore(l.score)),c&&(c.results.forEach((e=>{e.getMarker=()=>()=>s(r,e.marks)})),c.results=$t(c.results),n.dispatch(se.actions.setInclusiveLanguageResults(c.results)),n.dispatch(se.actions.setOverallInclusiveLanguageScore(c.score)),n.dispatch(se.actions.refreshSnippetEditor()),i.saveInclusiveLanguageScore(c.score)),(0,h.doAction)("yoast.analysis.refresh",o,{paper:r,worker:e,collectData:t,applyMarks:s,store:n,dataCollector:i})})).catch(Nt)}const jt="yoast-measurement-element";function zt(e){let t=document.getElementById(jt);return t||(t=function(){const e=document.createElement("div");return e.id=jt,e.style.position="absolute",e.style.left="-9999em",e.style.top=0,e.style.height=0,e.style.overflow="hidden",e.style.fontFamily="arial, sans-serif",e.style.fontSize="20px",e.style.fontWeight="400",document.body.appendChild(e),e}()),t.innerText=e,t.offsetWidth}function qt(e){return(0,l.isNil)(e)||(e/=10),function(e){switch(e){case"feedback":return{className:"na",screenReaderText:(0,g.__)("Feedback","wordpress-seo"),screenReaderReadabilityText:"",screenReaderInclusiveLanguageText:""};case"bad":return{className:"bad",screenReaderText:(0,g.__)("Needs improvement","wordpress-seo"),screenReaderReadabilityText:(0,g.__)("Needs improvement","wordpress-seo"),screenReaderInclusiveLanguageText:(0,g.__)("Needs improvement","wordpress-seo")};case"ok":return{className:"ok",screenReaderText:(0,g.__)("OK SEO score","wordpress-seo"),screenReaderReadabilityText:(0,g.__)("OK","wordpress-seo"),screenReaderInclusiveLanguageText:(0,g.__)("Potentially non-inclusive","wordpress-seo")};case"good":return{className:"good",screenReaderText:(0,g.__)("Good SEO score","wordpress-seo"),screenReaderReadabilityText:(0,g.__)("Good","wordpress-seo"),screenReaderInclusiveLanguageText:(0,g.__)("Good","wordpress-seo")};default:return{className:"loading",screenReaderText:"",screenReaderReadabilityText:"",screenReaderInclusiveLanguageText:""}}}(xe.interpreters.scoreToRating(e))}const{tmceId:Yt}=t,Vt=jQuery,Kt=function(e){"object"==typeof CKEDITOR&&console.warn("YoastSEO currently doesn't support ckEditor. The content analysis currently only works with the HTML editor or TinyMCE."),this._data=e.data,this._store=e.store};Kt.prototype.getData=function(){const e=this._data.getData(),t=this._store.getState();return{keyword:Dt()?this.getKeyword():"",meta:this.getMeta(),text:e.content,title:e.title,url:e.slug,excerpt:e.excerpt,snippetTitle:this.getSnippetTitle(),snippetMeta:this.getSnippetMeta(),snippetCite:this.getSnippetCite(),primaryCategory:this.getPrimaryCategory(),searchUrl:this.getSearchUrl(),postUrl:this.getPostUrl(),permalink:this.getPermalink(),titleWidth:zt(this.getSnippetTitle()),metaTitle:(0,l.get)(t,["analysisData","snippet","title"],this.getSnippetTitle()),url:(0,l.get)(t,["snippetEditor","data","slug"],e.slug),meta:this.getMetaDescForAnalysis(t)}},Kt.prototype.getKeyword=function(){return document.getElementById("yoast_wpseo_focuskw")&&document.getElementById("yoast_wpseo_focuskw").value||""},Kt.prototype.getMetaDescForAnalysis=function(e){let t=(0,l.get)(e,["analysisData","snippet","description"],this.getSnippetMeta());return""!==wpseoScriptData.metabox.metaDescriptionDate&&(t=wpseoScriptData.metabox.metaDescriptionDate+" - "+t),t},Kt.prototype.getMeta=function(){return document.getElementById("yoast_wpseo_metadesc")&&document.getElementById("yoast_wpseo_metadesc").value||""},Kt.prototype.getText=function(){return xe.markers.removeMarks(ct(Yt))},Kt.prototype.getTitle=function(){return document.getElementById("title")&&document.getElementById("title").value||""},Kt.prototype.getUrl=function(){const e=(0,d.select)("core/editor");if(e&&e.getCurrentPostAttribute("slug"))return e.getCurrentPostAttribute("slug");var t="",s=Vt("#new-post-slug");return 0<s.length?t=s.val():null!==document.getElementById("editable-post-name-full")&&(t=document.getElementById("editable-post-name-full").textContent),t},Kt.prototype.getExcerpt=function(){var e="";return null!==document.getElementById("excerpt")&&(e=document.getElementById("excerpt")&&document.getElementById("excerpt").value||""),e},Kt.prototype.getSnippetTitle=function(){return document.getElementById("yoast_wpseo_title")&&document.getElementById("yoast_wpseo_title").value||""},Kt.prototype.getSnippetMeta=function(){return document.getElementById("yoast_wpseo_metadesc")&&document.getElementById("yoast_wpseo_metadesc").value||""},Kt.prototype.getSnippetCite=function(){return this.getUrl()||""},Kt.prototype.getPrimaryCategory=function(){var e="",t=Vt("#category-all").find("ul.categorychecklist"),s=t.find("li input:checked");if(1===s.length)return this.getCategoryName(s.parent());var n=t.find(".wpseo-primary-term > label");return n.length?e=this.getCategoryName(n):e},Kt.prototype.getSearchUrl=function(){return wpseoScriptData.metabox.search_url},Kt.prototype.getPostUrl=function(){return wpseoScriptData.metabox.post_edit_url},Kt.prototype.getPermalink=function(){var e=this.getUrl();return wpseoScriptData.metabox.base_url+e},Kt.prototype.getCategoryName=function(e){var t=e.clone();return t.children().remove(),t.text().trim()},Kt.prototype.setDataFromSnippet=function(e,t){switch(t){case"snippet_meta":document.getElementById("yoast_wpseo_metadesc").value=e;break;case"snippet_cite":if(this.leavePostNameUntouched)return void(this.leavePostNameUntouched=!1);null!==document.getElementById("post_name")&&(document.getElementById("post_name").value=e),null!==document.getElementById("editable-post-name")&&null!==document.getElementById("editable-post-name-full")&&(document.getElementById("editable-post-name").textContent=e,document.getElementById("editable-post-name-full").textContent=e);break;case"snippet_title":document.getElementById("yoast_wpseo_title").value=e}},Kt.prototype.saveSnippetData=function(e){this.setDataFromSnippet(e.title,"snippet_title"),this.setDataFromSnippet(e.urlPath,"snippet_cite"),this.setDataFromSnippet(e.metaDesc,"snippet_meta")},Kt.prototype.bindElementEvents=function(e){this.inputElementEventBinder(e),this.changeElementEventBinder(e)},Kt.prototype.changeElementEventBinder=function(e){for(var t=["#yoast-wpseo-primary-category",'.categorychecklist input[name="post_category[]"]'],s=0;s<t.length;s++)Vt(t[s]).on("change",e)},Kt.prototype.inputElementEventBinder=function(e){for(var t=["excerpt","content","title"],s=0;s<t.length;s++)null!==document.getElementById(t[s])&&document.getElementById(t[s]).addEventListener("input",e);yt(e,Yt)},Kt.prototype.saveScores=function(e,t){var s=qt(e);Et("content",s.className),document.getElementById("yoast_wpseo_linkdex").value=e,""===t&&(s.className="na",s.screenReaderText=(0,g.__)("Enter a focus keyphrase to calculate the SEO score","wordpress-seo")),Tt(s),Pt(s),Et("keyword",s.className),jQuery(window).trigger("YoastSEO:numericScore",e)},Kt.prototype.saveContentScore=function(e){var t=qt(e);Et("content",t.className),Dt()||(Tt(t),Pt(t)),Vt("#yoast_wpseo_content_score").val(e)},Kt.prototype.saveInclusiveLanguageScore=function(e){const t=qt(e);Et("inclusive-language",t.className),Dt()||Mt()||(Tt(t),Pt(t)),Vt("#yoast_wpseo_inclusive_language_score").val(e)};const Wt=Kt;class Ht{constructor(){this._callbacks=[],this.register=this.register.bind(this)}register(e){(0,l.isFunction)(e)&&this._callbacks.push(e)}getData(){let e={};return this._callbacks.forEach((t=>{e=(0,l.merge)(e,t())})),e}}window.wp.annotations;const Qt=function(e){return(0,l.uniq)((0,l.flatten)(e.map((e=>{if(!(0,l.isUndefined)(e.getFieldsToMark()))return e.getFieldsToMark()}))))},Zt=window.wp.richText,Gt=/(<([a-z]|\/)[^<>]+>)/gi,{htmlEntitiesRegex:Jt}=xe.helpers.htmlEntities,Xt=e=>{let t=0;return(0,l.forEachRight)(e,(e=>{const[s]=e;let n=s.length;/^<\/?br/.test(s)&&(n-=1),t+=n})),t},es="<yoastmark class='yoast-text-mark'>",ts="</yoastmark>",ss='<yoastmark class="yoast-text-mark">';function ns(e,t,s,n,i){const r=n.clientId,o=(0,Zt.create)({html:e,multilineTag:s.multilineTag,multilineWrapperTag:s.multilineWrapperTag}).text;return(0,l.flatMap)(i,(s=>{let i;return i=s.hasBlockPosition&&s.hasBlockPosition()?function(e,t,s,n,i){if(t===e.getBlockClientId()){let t=e.getBlockPositionStart(),r=e.getBlockPositionEnd();if(e.isMarkForFirstBlockSection()){const e=((e,t,s)=>{const n="yoast/faq-block"===s?'<strong class="schema-faq-question">':'<strong class="schema-how-to-step-name">';return{blockStartOffset:e-=n.length,blockEndOffset:t-=n.length}})(t,r,s);t=e.blockStartOffset,r=e.blockEndOffset}if(n.slice(t,r)===i.slice(t,r))return[{startOffset:t,endOffset:r}];const o=((e,t,s)=>{const n=s.slice(0,e),i=s.slice(0,t),r=((e,t,s,n)=>{const i=[...e.matchAll(Gt)];s-=Xt(i);const r=[...t.matchAll(Gt)];return{blockStartOffset:s,blockEndOffset:n-=Xt(r)}})(n,i,e,t),o=((e,t,s,n)=>{let i=[...e.matchAll(Jt)];return(0,l.forEachRight)(i,(e=>{const[,t]=e;s-=t.length})),i=[...t.matchAll(Jt)],(0,l.forEachRight)(i,(e=>{const[,t]=e;n-=t.length})),{blockStartOffset:s,blockEndOffset:n}})(n,i,e=r.blockStartOffset,t=r.blockEndOffset);return{blockStartOffset:e=o.blockStartOffset,blockEndOffset:t=o.blockEndOffset}})(t,r,n);return[{startOffset:o.blockStartOffset,endOffset:o.blockEndOffset}]}return[]}(s,r,n.name,e,o):function(e,t){const s=t.getOriginal().replace(/(<([^>]+)>)/gi,""),n=t.getMarked().replace(/(<(?!\/?yoastmark)[^>]+>)/gi,""),i=function(e,t,s=!0){const n=[];if(0===e.length)return n;let i,r=0;for(s||(t=t.toLowerCase(),e=e.toLowerCase());(i=e.indexOf(t,r))>-1;)n.push(i),r=i+t.length;return n}(e,s);if(0===i.length)return[];const r=function(e){let t=e.indexOf(es);const s=t>=0;s||(t=e.indexOf(ss));let n=null;const i=[];for(;t>=0;){if(n=(e=s?e.replace(es,""):e.replace(ss,"")).indexOf(ts),n<t)return[];e=e.replace(ts,""),i.push({startOffset:t,endOffset:n}),t=s?e.indexOf(es):e.indexOf(ss),n=null}return i}(n),o=[];return r.forEach((e=>{i.forEach((n=>{const i=n+e.startOffset;let r=n+e.endOffset;0===e.startOffset&&e.endOffset===t.getOriginal().length&&(r=n+s.length),o.push({startOffset:i,endOffset:r})}))})),o}(o,s),i?i.map((e=>({...e,block:r,richTextIdentifier:t}))):[]}))}const is=e=>e[0].toUpperCase()+e.slice(1),rs=(e,t,s,n,i)=>(e=e.map((e=>{const r=`${e.id}-${i[0]}`,o=`${e.id}-${i[1]}`,a=is(i[0]),l=is(i[1]),c=e[`json${a}`],p=e[`json${l}`],{marksForFirstSection:d,marksForSecondSection:u}=((e,t)=>({marksForFirstSection:e.filter((e=>e.hasBlockPosition&&e.hasBlockPosition()?e.getBlockAttributeId()===t.id&&e.isMarkForFirstBlockSection():e)),marksForSecondSection:e.filter((e=>e.hasBlockPosition&&e.hasBlockPosition()?e.getBlockAttributeId()===t.id&&!e.isMarkForFirstBlockSection():e))}))(t,e),h=ns(c,r,s,n,d),g=ns(p,o,s,n,u);return h.concat(g)})),(0,l.flattenDeep)(e)),os="yoast";let as=[];const ls={"core/paragraph":[{key:"content"}],"core/list":[{key:"values",multilineTag:"li",multilineWrapperTag:["ul","ol"]}],"core/list-item":[{key:"content"}],"core/heading":[{key:"content"}],"core/audio":[{key:"caption"}],"core/embed":[{key:"caption"}],"core/gallery":[{key:"caption"}],"core/image":[{key:"caption"}],"core/table":[{key:"caption"}],"core/video":[{key:"caption"}],"yoast/faq-block":[{key:"questions"}],"yoast/how-to-block":[{key:"steps"},{key:"jsonDescription"}]};function cs(){const e=as.shift();e&&((0,d.dispatch)("core/annotations").__experimentalAddAnnotation(e),ps())}function ps(){(0,l.isFunction)(window.requestIdleCallback)?window.requestIdleCallback(cs,{timeout:1e3}):setTimeout(cs,150)}const ds=(e,t)=>{return(0,l.flatMap)((s=e.name,ls.hasOwnProperty(s)?ls[s]:[]),(s=>"yoast/faq-block"===e.name?((e,t,s)=>{const n=t.attributes[e.key];return 0===n.length?[]:rs(n,s,e,t,["question","answer"])})(s,e,t):"yoast/how-to-block"===e.name?((e,t,s)=>{const n=t.attributes[e.key];if(n&&0===n.length)return[];const i=[];return"steps"===e.key&&i.push(rs(n,s,e,t,["name","text"])),"jsonDescription"===e.key&&(s=s.filter((e=>e.hasBlockPosition&&e.hasBlockPosition()?!e.getBlockAttributeId():e)),i.push(ns(n,"description",e,t,s))),(0,l.flattenDeep)(i)})(s,e,t):function(e,t,s){const n=e.key,i=((e,t)=>{const s=e.attributes[t];return"string"==typeof s?s:s.toString()})(t,n);return ns(i,n,e,t,s)}(s,e,t)));var s};function us(e,t){return(0,l.flatMap)(e,(e=>{const s=function(e){return e.innerBlocks.length>0}(e)?us(e.innerBlocks,t):[];return ds(e,t).concat(s)}))}function hs(e){as=[],(0,d.dispatch)("core/annotations").__experimentalRemoveAnnotationsBySource(os);const t=Qt(e);if(0===e.length)return;let s=(0,d.select)("core/block-editor").getBlocks();var n;t.length>0&&(s=s.filter((e=>t.some((t=>"core/"+t===e.name))))),n=us(s,e),as=n.map((e=>({blockClientId:e.block,source:os,richTextIdentifier:e.richTextIdentifier,range:{start:e.startOffset,end:e.endOffset}}))),ps()}function gs(e,t){let s;lt(it)&&((0,l.isUndefined)(s)&&(s=Ge(tinyMCE.get(it))),s(e,t)),(0,d.select)("core/block-editor")&&(0,l.isFunction)((0,d.select)("core/block-editor").getBlocks)&&(0,d.select)("core/annotations")&&(0,l.isFunction)((0,d.dispatch)("core/annotations").__experimentalAddAnnotation)&&(function(e,t){tinyMCE.editors.map((e=>Ge(e))).forEach((s=>s(e,t)))}(e,t),hs(t)),(0,h.doAction)("yoast.analysis.applyMarks",t)}function ms(){const e=(0,d.select)("yoast-seo/editor").isMarkingAvailable(),t=(0,d.select)("yoast-seo/editor").getMarkerPauseStatus();return!e||t?l.noop:gs}var fs=jQuery;function ys(e,t,s,n,i){this._scriptUrl=n,this._options={usedKeywords:t.keyword_usage,usedKeywordsPostTypes:t.keyword_usage_post_types,searchUrl:t.search_url,postUrl:t.post_edit_url},this._keywordUsage=t.keyword_usage,this._usedKeywordsPostTypes=t.keyword_usage_post_types,this._postID=fs("#post_ID, [name=tag_ID]").val(),this._taxonomy=fs("[name=taxonomy]").val()||"",this._nonce=i,this._ajaxAction=e,this._refreshAnalysis=s,this._initialized=!1}ys.prototype.init=function(){const{worker:e}=window.YoastSEO.analysis;this.requestKeywordUsage=(0,l.debounce)(this.requestKeywordUsage.bind(this),500),e.loadScript(this._scriptUrl).then((()=>{e.sendMessage("initialize",this._options,"used-keywords-assessment")})).then((()=>{this._initialized=!0,(0,l.isEqual)(this._options.usedKeywords,this._keywordUsage)?this._refreshAnalysis():e.sendMessage("updateKeywordUsage",this._keywordUsage,"used-keywords-assessment").then((()=>this._refreshAnalysis()))})).catch((e=>console.error(e)))},ys.prototype.setKeyword=function(e){(0,l.has)(this._keywordUsage,e)||this.requestKeywordUsage(e)},ys.prototype.requestKeywordUsage=function(e){fs.post(ajaxurl,{action:this._ajaxAction,post_id:this._postID,keyword:e,taxonomy:this._taxonomy,nonce:this._nonce},this.updateKeywordUsage.bind(this,e),"json")},ys.prototype.updateKeywordUsage=function(e,t){const{worker:s}=window.YoastSEO.analysis,n=t.keyword_usage,i=t.post_types;n&&(0,l.isArray)(n)&&(this._keywordUsage[e]=n,this._usedKeywordsPostTypes[e]=i,this._initialized&&s.sendMessage("updateKeywordUsage",{usedKeywords:this._keywordUsage,usedKeywordsPostTypes:this._usedKeywordsPostTypes},"used-keywords-assessment").then((()=>this._refreshAnalysis())))};const{setFocusKeyword:ws,setMarkerStatus:bs,updateData:_s,setCornerstoneContent:ks,refreshSnippetEditor:vs,setReadabilityResults:xs,setSeoResultsForKeyword:Es}=se.actions;function Ss(e,s,i){if("undefined"==typeof wpseoScriptData)return;let r,o,a,c;const p=new Ht;function u(e){return""===e.responseText?o.val():jQuery("<div>"+e.responseText+"</div>").find("#editable-post-name-full").text()}function g(){const e={};return Dt()&&(e.output="does-not-really-exist-but-it-needs-something"),Mt()&&(e.contentOutput="also-does-not-really-exist-but-it-needs-something"),e}function m(e){(0,l.isUndefined)(e.seoAssessorPresenter)||(e.seoAssessorPresenter.render=function(){}),(0,l.isUndefined)(e.contentAssessorPresenter)||(e.contentAssessorPresenter.render=function(){},e.contentAssessorPresenter.renderIndividualRatings=function(){})}let f;function y(e,t){const s=f||"";f=e.getState().analysisData.snippet,!(0,Ee.isShallowEqualObjects)(s,f)&&t()}function w(e,t){"visual"!==e?t.dispatch(bs("disabled")):t.dispatch(bs("enabled"))}function b(){return(0,d.select)("core/edit-post").getEditorMode()}jQuery(document).on("ajaxComplete",(function(e,t,n){if("/admin-ajax.php"===n.url.substring(n.url.length-15)&&"string"==typeof n.data&&-1!==n.data.indexOf("action=sample-permalink")){c.leavePostNameUntouched=!0;const e={slug:u(t)};s.dispatch(_s(e))}})),function(){if(r=e("#wpseo_meta"),ot(s),ft(),function(){const e=new st;e.isClassicEditorHidden()&&dt(),e.vcActive?dt():e.listen({classicEditorHidden:()=>{dt()},classicEditorShown:()=>{mt()||ut()}})}(),0===r.length)return;c=function(e){const t=new Wt({data:e,store:s});return t.leavePostNameUntouched=!1,t}(i),Ct();const u=function(t){const s={elementTarget:[it,"yoast_wpseo_focuskw_text_input","yoast_wpseo_metadesc","excerpt","editable-post-name","editable-post-name-full"],targets:g(),callbacks:{getData:c.getData.bind(c)},locale:wpseoScriptData.metabox.contentLocale,marker:ms(),contentAnalysisActive:Mt(),keywordAnalysisActive:Dt(),debouncedRefresh:!1,researcher:new window.yoast.Researcher.default};Dt()&&(t.dispatch(ws(e("#yoast_wpseo_focuskw").val())),s.callbacks.saveScores=c.saveScores.bind(c),s.callbacks.updatedKeywordsResults=function(e){const s=t.getState().focusKeyword;t.dispatch(Es(s,e)),t.dispatch(vs())}),Mt()&&(s.callbacks.saveContentScore=c.saveContentScore.bind(c),s.callbacks.updatedContentResults=function(e){t.dispatch(xs(e)),t.dispatch(vs())}),o=e("#title");const n=It();return(0,l.isUndefined)(n)||(0,l.isUndefined)(n.domain)||(s.translations=n),s}(s);a=new xe.App(u),window.YoastSEO=window.YoastSEO||{},window.YoastSEO.app=a,window.YoastSEO.store=s,window.YoastSEO.analysis={},window.YoastSEO.analysis.worker=function(){const e=(0,l.get)(window,["wpseoScriptData","analysis","worker","url"],"analysis-worker.js"),t=(0,xe.createWorker)(e),s=(0,l.get)(window,["wpseoScriptData","analysis","worker","dependencies"],[]),n=[];for(const e in s){if(!Object.prototype.hasOwnProperty.call(s,e))continue;const t=window.document.getElementById(`${e}-js-translations`);if(!t)continue;const i=t.innerHTML.slice(214),r=i.indexOf(","),o=i.slice(0,r-1);try{const e=JSON.parse(i.slice(r+1,-4));n.push([o,e])}catch(t){console.warn(`Failed to parse translation data for ${e} to send to the Yoast SEO worker`);continue}}return t.postMessage({dependencies:s,translations:n}),new xe.AnalysisWorkerWrapper(t)}(),window.YoastSEO.analysis.collectData=()=>function(e,t,s,n,i){const r=(0,l.cloneDeep)(t.getState());(0,l.merge)(r,s.getData());const o=e.getData();let a=null;i&&(a=i.getBlocks()||[],a=a.filter((e=>e.isValid)));const c={text:o.content,textTitle:o.title,keyword:r.focusKeyword,synonyms:r.synonyms,description:r.analysisData.snippet.description||r.snippetEditor.data.description,title:r.analysisData.snippet.title||r.snippetEditor.data.title,slug:r.snippetEditor.data.slug,permalink:r.settings.snippetEditor.baseUrl+r.snippetEditor.data.slug,wpBlocks:a,date:r.settings.snippetEditor.date};n.loaded&&(c.title=n._applyModifications("data_page_title",c.title),c.title=n._applyModifications("title",c.title),c.description=n._applyModifications("data_meta_desc",c.description),c.text=n._applyModifications("content",c.text),c.wpBlocks=n._applyModifications("wpBlocks",c.wpBlocks));const p=r.analysisData.snippet.filteredSEOTitle;return c.titleWidth=zt(p||r.snippetEditor.data.title),c.locale=At(),c.writingDirection=function(){let e="LTR";return Ot().isRtl&&(e="RTL"),e}(),c.shortcodes=window.wpseoScriptData.analysis.plugins.shortcodes?window.wpseoScriptData.analysis.plugins.shortcodes.wpseo_shortcode_tags:[],xe.Paper.parse((0,h.applyFilters)("yoast.analysis.data",c))}(i,s,p,a.pluggable,(0,d.select)("core/block-editor")),window.YoastSEO.analysis.applyMarks=(e,t)=>ms()(e,t),window.YoastSEO.app.refresh=(0,l.debounce)((()=>Ut(window.YoastSEO.analysis.worker,window.YoastSEO.analysis.collectData,window.YoastSEO.analysis.applyMarks,s,c)),500),window.YoastSEO.app.registerCustomDataCallback=p.register,window.YoastSEO.app.pluggable=new wt(window.YoastSEO.app.refresh),window.YoastSEO.app.registerPlugin=window.YoastSEO.app.pluggable._registerPlugin,window.YoastSEO.app.pluginReady=window.YoastSEO.app.pluggable._ready,window.YoastSEO.app.pluginReloaded=window.YoastSEO.app.pluggable._reloaded,window.YoastSEO.app.registerModification=window.YoastSEO.app.pluggable._registerModification,window.YoastSEO.app.registerAssessment=(e,t,s)=>{if(!(0,l.isUndefined)(a.seoAssessor))return window.YoastSEO.app.pluggable._registerAssessment(a.defaultSeoAssessor,e,t,s)&&window.YoastSEO.app.pluggable._registerAssessment(a.cornerStoneSeoAssessor,e,t,s)},window.YoastSEO.app.changeAssessorOptions=function(e){window.YoastSEO.analysis.worker.initialize(e).catch(Nt),window.YoastSEO.app.refresh()},function(e,t,s){const n=Ot();if(!n.previouslyUsedKeywordActive)return;const i=new ys("get_focus_keyword_usage_and_post_types",n,e,(0,l.get)(window,["wpseoScriptData","analysis","worker","keywords_assessment_url"],"used-keywords-assessment.js"),(0,l.get)(window,["wpseoScriptData","usedKeywordsNonce"],""));i.init();let r={};s.subscribe((()=>{const e=s.getState()||{};e.focusKeyword!==r.focusKeyword&&(r=e,i.setKeyword(e.focusKeyword))}))}(a.refresh,0,s),s.subscribe(y.bind(null,s,a.refresh)),window.YoastSEO.analyzerArgs=u,window.YoastSEO.wp={},window.YoastSEO.wp.replaceVarsPlugin=new Be(a,s),function(e,t){let s=[];s=(0,h.applyFilters)("yoast.analysis.shortcodes",s);const n=wpseoScriptData.analysis.plugins.shortcodes.wpseo_shortcode_tags;s=s.filter((e=>n.includes(e))),s.length>0&&(t.dispatch(Ye(s)),window.YoastSEO.wp.shortcodePlugin=new ze({registerPlugin:e.registerPlugin,registerModification:e.registerModification,pluginReady:e.pluginReady,pluginReloaded:e.pluginReloaded},s))}(a,s),ve()&&new Fe(a.registerPlugin,a.registerModification,window.YoastSEO.app.refresh).register(),wpseoScriptData.metabox.markdownEnabled&&new We(a.registerPlugin,a.registerModification).register(),window.YoastSEO.wp._tinyMCEHelper=t,Dt()&&function(t){const s=qt(e("#yoast_wpseo_linkdex").val());Tt(s),Pt(s),t.updateScore("keyword",s.className)}(n),Mt()&&function(t){const s=qt(e("#yoast_wpseo_content_score").val());Pt(s),t.updateScore("content",s.className)}(n),Lt()&&function(t){const s=qt(e("#yoast_wpseo_inclusive_language_score").val());Pt(s),t.updateScore("inclusive-language",s.className)}(n),window.YoastSEO.analysis.worker.initialize(function(e={}){let t={locale:At(),contentAnalysisActive:Mt(),keywordAnalysisActive:Dt(),inclusiveLanguageAnalysisActive:Lt(),defaultQueryParams:(0,l.get)(window,["wpseoAdminL10n","default_query_params"],{}),logLevel:(0,l.get)(window,["wpseoScriptData","analysis","worker","log_level"],"ERROR"),enabledFeatures:(0,Bt.enabledFeatures)()};t=(0,l.merge)(t,e);const s=It();return(0,l.isUndefined)(s)||(0,l.isUndefined)(s.domain)||(t.translations=s),t}()).then((()=>{jQuery(window).trigger("YoastSEO:ready")})).catch(Nt),c.bindElementEvents((0,l.debounce)((()=>Ut(window.YoastSEO.analysis.worker,window.YoastSEO.analysis.collectData,window.YoastSEO.analysis.applyMarks,s,c)),500)),m(a);const f=a.initAssessorPresenters.bind(a);a.initAssessorPresenters=function(){f(),m(a)},i.setRefresh&&i.setRefresh(a.refresh);let _={title:(k=c).getSnippetTitle(),slug:k.getSnippetCite(),description:k.getSnippetMeta()};var k;const v=function(e){const t={};if((0,l.isUndefined)(e))return t;t.title=e.title_template;const s=e.metadesc_template;return(0,l.isEmpty)(s)||(t.description=s),t}(wpseoScriptData.metabox);_=function(e,t){const s={...e};return(0,l.forEach)(t,((t,n)=>{(0,l.has)(e,n)&&""===e[n]&&(s[n]=t)})),s}(_,v),s.dispatch(_s(_)),s.dispatch(ks("1"===document.getElementById("yoast_wpseo_is_cornerstone").value));let x=s.getState().focusKeyword;bt(window.YoastSEO.analysis.worker.runResearch,s,x);const E=(0,l.debounce)((()=>{a.refresh()}),50);let S=null;if(s.subscribe((()=>{const t=s.getState().focusKeyword;x!==t&&(x=t,bt(window.YoastSEO.analysis.worker.runResearch,s,x),e("#yoast_wpseo_focuskw").val(x),E());const n=function(e){const t=e.getState().snippetEditor.data;return{title:t.title,slug:t.slug,description:t.description}}(s),i=function(e,t){const s={...e};return(0,l.forEach)(t,((t,n)=>{(0,l.has)(e,n)&&e[n].trim()===t&&(s[n]="")})),s}(n,v);_.title!==n.title&&c.setDataFromSnippet(i.title,"snippet_title"),_.slug!==n.slug&&c.setDataFromSnippet(i.slug,"snippet_cite"),_.description!==n.description&&c.setDataFromSnippet(i.description,"snippet_meta");const r=s.getState();S!==r.isCornerstone&&(S=r.isCornerstone,document.getElementById("yoast_wpseo_is_cornerstone").value=r.isCornerstone,a.changeAssessorOptions({useCornerstone:r.isCornerstone})),_.title=n.title,_.slug=n.slug,_.description=n.description})),ve()){let e=b();w(e,s),(0,d.subscribe)((()=>{const t=b();t!==e&&(e=t,w(e,s))}))}Ft=!0,window.YoastSEO.app.refresh()}()}window.YoastReplaceVarPlugin=Be,window.YoastShortcodePlugin=qe;const Rs=window.yoast.styledComponents;var Cs=s.n(Rs);const Ts=window.wp.compose,Ps=Cs().div`
	padding-top: 6px;
`,Os=e=>{const{value:t,id:s,terms:n,onChange:i}=e,r=(0,u.useCallback)((e=>{i(parseInt(e.target.value,10))}),[i]);return(0,c.createElement)(Ps,null,(0,c.createElement)("select",{className:"components-select-control__input",id:s,value:t,onChange:r},n.map((e=>(0,c.createElement)("option",{key:e.id,value:e.id},(0,l.unescape)(e.name))))))};Os.propTypes={terms:y().arrayOf(y().shape({id:y().number.isRequired,name:y().string.isRequired})),onChange:y().func.isRequired,id:y().string,value:y().number};const As=Os,Is=Cs().div`
	padding-top: 16px;
`;class Ms extends u.Component{constructor(e){super(e),this.onChange=this.onChange.bind(this),this.updateReplacementVariable=this.updateReplacementVariable.bind(this);const{fieldId:t,name:s}=e.taxonomy;this.input=document.getElementById(t),e.setPrimaryTaxonomyId(s,parseInt(this.input.value,10)),this.state={selectedTerms:[],terms:[]}}componentDidMount(){this.fetchTerms()}componentDidUpdate(e,t){if(e.selectedTermIds.length<this.props.selectedTermIds.length){const t=(0,l.difference)(this.props.selectedTermIds,e.selectedTermIds)[0];if(!this.termIsAvailable(t))return void this.fetchTerms()}e.selectedTermIds!==this.props.selectedTermIds&&this.updateSelectedTerms(this.state.terms,this.props.selectedTermIds),t.selectedTerms!==this.state.selectedTerms&&this.handleSelectedTermsChange()}handleSelectedTermsChange(){const{selectedTerms:e}=this.state,{primaryTaxonomyId:t}=this.props;e.find((e=>e.id===t))||this.onChange(e.length?e[0].id:-1)}termIsAvailable(e){return!!this.state.terms.find((t=>t.id===e))}fetchTerms(){const{taxonomy:e}=this.props;e&&(this.fetchRequest=he()({path:(0,ie.addQueryArgs)(`/wp/v2/${e.restBase}`,{per_page:-1,orderby:"count",order:"desc",_fields:"id,name"})}),this.fetchRequest.then((e=>{const t=this.state;this.setState({terms:e,selectedTerms:this.getSelectedTerms(e,this.props.selectedTermIds)},(()=>{0===t.terms.length&&this.state.terms.length>0&&this.updateReplacementVariable(this.props.primaryTaxonomyId)}))})))}getSelectedTerms(e,t){return e.filter((e=>t.includes(e.id)))}updateSelectedTerms(e,t){const s=this.getSelectedTerms(e,t);this.setState({selectedTerms:s})}onChange(e){const{name:t}=this.props.taxonomy;this.updateReplacementVariable(e),this.props.setPrimaryTaxonomyId(t,e),this.input.value=-1===e?"":e}updateReplacementVariable(e){if("category"!==this.props.taxonomy.name)return;const t=this.state.selectedTerms.find((t=>t.id===e));this.props.updateReplacementVariable(`primary_${this.props.taxonomy.name}`,t?t.name:"")}render(){const{primaryTaxonomyId:e,taxonomy:t}=this.props;if(this.state.selectedTerms.length<2)return null;const s=`yoast-primary-${t.name}-picker`;return(0,c.createElement)(Is,{className:"components-base-control__field"},(0,c.createElement)("label",{htmlFor:s,className:"components-base-control__label"},(0,g.sprintf)(/* translators: %s expands to the taxonomy name. */
(0,g.__)("Select the primary %s","wordpress-seo"),t.singularLabel.toLowerCase())),(0,c.createElement)(As,{value:e,onChange:this.onChange,id:s,terms:this.state.selectedTerms}))}}Ms.propTypes={selectedTermIds:y().arrayOf(y().number),primaryTaxonomyId:y().number,setPrimaryTaxonomyId:y().func,updateReplacementVariable:y().func,taxonomy:y().shape({name:y().string,fieldId:y().string,restBase:y().string,singularLabel:y().string})},Ms.defaultProps={selectedTermIds:[],primaryTaxonomyId:-1,setPrimaryTaxonomyId:l.noop,updateReplacementVariable:l.noop,taxonomy:{}};const Ds=Ms,Ls=(0,Ts.compose)([(0,d.withSelect)(((e,t)=>{const s=e("core/editor"),n=e("yoast-seo/editor"),{taxonomy:i}=t;return{selectedTermIds:s.getEditedPostAttribute(i.restBase)||[],primaryTaxonomyId:n.getPrimaryTaxonomyId(i.name)}})),(0,d.withDispatch)((e=>{const{setPrimaryTaxonomyId:t,updateReplacementVariable:s}=e("yoast-seo/editor");return{setPrimaryTaxonomyId:t,updateReplacementVariable:s}}))])(Ds);let Bs=null,Ns=null;const Fs=Cs().div`
	margin: 16px 0 8px;
`;class $s extends u.Component{constructor(){super(),Bs&&Ns||(Bs=(0,l.get)(window.wpseoPrimaryCategoryL10n,"taxonomies",{}),Ns=(0,l.values)(Bs).map((e=>e.name))),this.state={exceptionCaught:!1,error:null}}componentDidCatch(e){this.setState({exceptionCaught:!0,error:e})}taxonomyHasPrimaryTermSupport(){return Ns.includes(this.props.slug)}render(){const{slug:e,OriginalComponent:t}=this.props;if(this.state.exceptionCaught){const e=(0,l.get)(this.state,"error.stack");return(0,c.createElement)(u.Fragment,null,(0,c.createElement)(t,{...this.props}),(0,c.createElement)(Fs,null,(0,g.sprintf)(/* translators: %s expands to Yoast SEO. */
(0,g.__)("An error occurred loading the %s primary taxonomy picker.","wordpress-seo"),"Yoast SEO")),e&&(0,c.createElement)(p.ClipboardButton,{isLarge:!0,text:e},(0,g.__)("Copy error","wordpress-seo")))}return this.taxonomyHasPrimaryTermSupport()?(0,c.createElement)(u.Fragment,null,(0,c.createElement)(t,{...this.props}),(0,c.createElement)(Ls,{taxonomy:Bs[e]})):(0,c.createElement)(t,{...this.props})}}$s.propTypes={OriginalComponent:y().func.isRequired,slug:y().string.isRequired};const Us=$s;let js=null;const zs=()=>{if(null===js){const e=(0,d.dispatch)("yoast-seo/editor").runAnalysis;js=window.YoastSEO.app&&window.YoastSEO.app.pluggable?window.YoastSEO.app.pluggable:new wt(e)}return js},qs=(e,t,s)=>zs().loaded?zs()._applyModifications(e,t,s):t;function Ys(){const{getAnalysisData:e,getEditorDataTitle:t}=(0,d.select)("yoast-seo/editor");let s=e();s={...s,textTitle:t()};const n=function(e){return e.title=qs("data_page_title",e.title),e.title=qs("title",e.title),e.description=qs("data_meta_desc",e.description),e.text=qs("content",e.text),e}(s);return(0,h.applyFilters)("yoast.analysis.data",n)}(0,l.debounce)((async function(e,t){const{text:s,...n}=t,i=new xe.Paper(s,n);try{const t=await e.analyze(i),{seo:s,readability:n,inclusiveLanguage:r}=t.result;if(s){const e=s[""];e.results.forEach((e=>{e.getMarker=()=>()=>window.YoastSEO.analysis.applyMarks(i,e.marks)})),e.results=$t(e.results),(0,d.dispatch)("yoast-seo/editor").setSeoResultsForKeyword(i.getKeyword(),e.results),(0,d.dispatch)("yoast-seo/editor").setOverallSeoScore(e.score,i.getKeyword())}n&&(n.results.forEach((e=>{e.getMarker=()=>()=>window.YoastSEO.analysis.applyMarks(i,e.marks)})),n.results=$t(n.results),(0,d.dispatch)("yoast-seo/editor").setReadabilityResults(n.results),(0,d.dispatch)("yoast-seo/editor").setOverallReadabilityScore(n.score)),r&&(r.results.forEach((e=>{e.getMarker=()=>()=>window.YoastSEO.analysis.applyMarks(i,e.marks)})),r.results=$t(r.results),(0,d.dispatch)("yoast-seo/editor").setInclusiveLanguageResults(r.results),(0,d.dispatch)("yoast-seo/editor").setOverallInclusiveLanguageScore(r.score)),(0,h.doAction)("yoast.analysis.run",t,{paper:i})}catch(e){}}),500);const Vs=()=>{const{getContentLocale:e}=(0,d.select)("yoast-seo/editor"),t=((...e)=>()=>e.map((e=>e())))(e,Ys),s=(()=>{const{setEstimatedReadingTime:e,setFleschReadingEase:t,setTextLength:s}=(0,d.dispatch)("yoast-seo/editor"),n=(0,l.get)(window,"YoastSEO.analysis.worker.runResearch",l.noop);return()=>{const i=xe.Paper.parse(Ys());n("readingTime",i).then((t=>e(t.result))),n("getFleschReadingScore",i).then((e=>{e.result&&t(e.result)})),n("wordCountInText",i).then((e=>s(e.result)))}})();return setTimeout(s,1500),((e,t)=>{let s=e();return()=>{const n=e();(0,l.isEqual)(n,s)||(s=n,t((0,l.clone)(n)))}})(t,s)};window.wpseoPostScraperL10n=window.wpseoScriptData.metabox,window.wpseoShortcodePluginL10n=window.wpseoScriptData.analysis.plugins.shortcodes,window.YoastSEO=window.YoastSEO||{},r()((()=>{(function(e){function t(e){e&&(e.focus(),e.click())}function s(){if(e(".wpseo-meta-section").length>0){const t=e(".wpseo-meta-section-link");e(".wpseo-metabox-menu li").filter((function(){return"#wpseo-meta-section-content"===e(this).find(".wpseo-meta-section-link").attr("href")})).addClass("active").find("[role='tab']").addClass("yoast-active-tab"),e("#wpseo-meta-section-content, .wpseo-meta-section-react").addClass("active"),t.on("click",(function(s){var n=e(this).attr("id"),i=e(this).attr("href"),r=e(i);s.preventDefault(),e(".wpseo-metabox-menu li").removeClass("active").find("[role='tab']").removeClass("yoast-active-tab"),e(".wpseo-meta-section").removeClass("active"),e(".wpseo-meta-section-react.active").removeClass("active"),"#wpseo-meta-section-content"===i&&e(".wpseo-meta-section-react").addClass("active"),r.addClass("active"),e(this).parent("li").addClass("active").find("[role='tab']").addClass("yoast-active-tab");const o=function(e,t={}){return new CustomEvent("YoastSEO:metaTabChange",{detail:t})}(0,{metaTabId:n});window.dispatchEvent(o),this&&(t.attr({"aria-selected":"false",tabIndex:"-1"}),this.removeAttribute("tabindex"),this.setAttribute("aria-selected","true"))}))}}window.wpseoInitTabs=s,window.wpseo_init_tabs=s,e(".wpseo-meta-section").each((function(t,s){e(s).find(".wpseotab:first").addClass("active")})),window.wpseo_init_tabs(),function(){const s=e(".yoast-aria-tabs"),n=s.find("[role='tab']"),i=s.attr("aria-orientation")||"horizontal";n.attr({"aria-selected":!1,tabIndex:"-1"}),n.filter(".yoast-active-tab").removeAttr("tabindex").attr("aria-selected","true"),n.on("keydown",(function(s){-1!==[32,35,36,37,38,39,40].indexOf(s.which)&&("horizontal"===i&&-1!==[38,40].indexOf(s.which)||"vertical"===i&&-1!==[37,39].indexOf(s.which)||function(s,n){const i=s.which,r=n.index(e(s.target));switch(i){case 32:s.preventDefault(),t(n[r]);break;case 35:s.preventDefault(),t(n[n.length-1]);break;case 36:s.preventDefault(),t(n[0]);break;case 37:case 38:s.preventDefault(),t(n[r-1<0?n.length-1:r-1]);break;case 39:case 40:s.preventDefault(),t(n[r+1===n.length?0:r+1])}}(s,n))}))}()})(a()),"undefined"!=typeof wpseoPrimaryCategoryL10n&&function(e){var t,s,n=wpseoPrimaryCategoryL10n.taxonomies;function i(t){return e("#yoast-wpseo-primary-"+t).val()}function r(t,s){e("#yoast-wpseo-primary-"+t).val(s).trigger("change");const n=(0,d.dispatch)("yoast-seo/editor");if(n){const i=parseInt(s,10);n.setPrimaryTaxonomyId(t,i),"category"===t&&n.updateReplacementVariable("primary_category",function(t){const s=e("#category-all").find(`#category-${t} > label`);if(0===s.length)return"";const n=s.clone();return n.children().remove(),n.text().trim()}(i))}}function o(r){var o,a,l;o=e("#"+r+'checklist input[type="checkbox"]:checked');var c=e("#"+r+"checklist li");c.removeClass("wpseo-term-unchecked wpseo-primary-term wpseo-non-primary-term"),e(".wpseo-primary-category-label").remove(),c.addClass("wpseo-term-unchecked"),o.length<=1||o.each((function(o,c){c=e(c),(a=c.closest("li")).removeClass("wpseo-term-unchecked"),1!==e(c).closest("li").children(".wpseo-make-primary-term").length&&function(s,i){var r,o;r=e(i).closest("label"),o=t({taxonomy:n[s],term:r.text()}),r.after(o)}(r,c),c.val()===i(r)?(a.addClass("wpseo-primary-term"),(l=c.closest("label")).find(".wpseo-primary-category-label").remove(),l.append(s({taxonomy:n[r]}))):a.addClass("wpseo-non-primary-term")}))}function a(t){r(t,e("#"+t+'checklist input[type="checkbox"]:checked:first').val()),o(t)}function p(e){""===i(e)&&a(e)}e.fn.initYstSEOPrimaryCategory=function(){return this.each((function(t,s){const n=e("#"+s.name+"div");var l;o(s.name),n.on("click",'input[type="checkbox"]',(l=s.name,function(){!1===e(this).prop("checked")&&e(this).val()===i(l)&&a(l),p(l),o(l)})),n.on("wpListAddEnd","#"+s.name+"checklist",function(e){return function(){p(e),o(e)}}(s.name)),n.on("click",".wpseo-make-primary-term",function(t){return function(s){var n;n=e(s.currentTarget).siblings("label").find("input"),r(t,n.val()),o(t),n.trigger("focus")}}(s.name))}))},t=wp.template("primary-term-ui"),s=wp.template("primary-term-screen-reader"),e(_.values(n)).initYstSEOPrimaryCategory(),ve()&&(0,l.get)(window,"wp.hooks.addFilter",l.noop)("editor.PostTaxonomyType","yoast-seo",(e=>class extends u.Component{render(){return(0,c.createElement)(Us,{OriginalComponent:e,...this.props})}}))}(a());const t=function(){const t=(0,d.registerStore)("yoast-seo/editor",{reducer:(0,d.combineReducers)(se.reducers),selectors:se.selectors,actions:(0,l.pickBy)(se.actions,(e=>"function"==typeof e)),controls:e});return(e=>{e.dispatch(se.actions.setSettings({socialPreviews:{sitewideImage:window.wpseoScriptData.metabox.sitewide_social_image,siteName:window.wpseoScriptData.metabox.site_name,contentImage:window.wpseoScriptData.metabox.first_content_image,twitterCardType:window.wpseoScriptData.metabox.twitterCardType},snippetEditor:{baseUrl:window.wpseoScriptData.metabox.base_url,date:window.wpseoScriptData.metabox.metaDescriptionDate,recommendedReplacementVariables:window.wpseoScriptData.analysis.plugins.replaceVars.recommended_replace_vars,siteIconUrl:window.wpseoScriptData.metabox.siteIconUrl}})),e.dispatch(se.actions.setSEMrushChangeCountry(window.wpseoScriptData.metabox.countryCode)),e.dispatch(se.actions.setSEMrushLoginStatus(window.wpseoScriptData.metabox.SEMrushLoginStatus)),e.dispatch(se.actions.setWincherLoginStatus(window.wpseoScriptData.metabox.wincherLoginStatus,!1)),e.dispatch(se.actions.setWincherWebsiteId(window.wpseoScriptData.metabox.wincherWebsiteId)),e.dispatch(se.actions.setWincherAutomaticKeyphaseTracking(window.wpseoScriptData.metabox.wincherAutoAddKeyphrases)),e.dispatch(se.actions.setDismissedAlerts((0,l.get)(window,"wpseoScriptData.dismissedAlerts",{}))),e.dispatch(se.actions.setCurrentPromotions((0,l.get)(window,"wpseoScriptData.currentPromotions",[]))),e.dispatch(se.actions.setIsPremium(Boolean((0,l.get)(window,"wpseoScriptData.metabox.isPremium",!1)))),e.dispatch(se.actions.setPostId(Number((0,l.get)(window,"wpseoScriptData.postId",null)))),e.dispatch(se.actions.setLinkParams((0,l.get)(window,"wpseoScriptData.linkParams",{}))),e.dispatch(se.actions.setPluginUrl((0,l.get)(window,"wpseoScriptData.pluginUrl",""))),e.dispatch(se.actions.setWistiaEmbedPermissionValue("1"===(0,l.get)(window,"wpseoScriptData.wistiaEmbedPermission",!1)))})(t),t}();window.yoast.initEditorIntegration(t);const s=new window.yoast.EditorData(l.noop,t);s.initialize(window.wpseoScriptData.analysis.plugins.replaceVars.replace_vars,window.wpseoScriptData.analysis.plugins.replaceVars.hidden_replace_vars),Ss(a(),t,s),window.wpseoScriptData&&void 0!==window.wpseoScriptData.featuredImage&&function(e){var t,s,n,i=function(e){this._app=e,this.featuredImage=null,this.pluginName="addFeaturedImagePlugin",this.registerPlugin(),this.registerModifications()};function r(){e("#yst_opengraph_image_warning").remove(),s.removeClass("yoast-opengraph-image-notice")}i.prototype.setFeaturedImage=function(e){this.featuredImage=e,this._app.pluginReloaded(this.pluginName)},i.prototype.removeFeaturedImage=function(){this.setFeaturedImage(null)},i.prototype.registerPlugin=function(){this._app.registerPlugin(this.pluginName,{status:"ready"})},i.prototype.registerModifications=function(){this._app.registerModification("content",this.addImageToContent.bind(this),this.pluginName,10)},i.prototype.addImageToContent=function(e){return null!==this.featuredImage&&(e+=this.featuredImage),e};var o=wp.media.featuredImage.frame();if("undefined"==typeof YoastSEO)return;if(t=new i(YoastSEO.app),s=e("#postimagediv"),n=s.find(".hndle"),o.on("select",(function(){var i,a,l;!function(t){var i=t.state().get("selection").first().toJSON();i.width<200||i.height<200?0===e("#yst_opengraph_image_warning").length&&(e('<div id="yst_opengraph_image_warning" class="notice notice-error notice-alt"><p>'+wpseoScriptData.featuredImage.featured_image_notice+"</p></div>").insertAfter(n),s.addClass("yoast-opengraph-image-notice"),ke()(wpseoScriptData.featuredImage.featured_image_notice,"assertive")):r()}(o),l=(a=o.state().get("selection").first()).get("alt"),i='<img src="'+a.get("url")+'" width="'+a.get("width")+'" height="'+a.get("height")+'" alt="'+l+'"/>',t.setFeaturedImage(i)})),s.on("click","#remove-post-thumbnail",(function(){t.removeFeaturedImage(),r()})),void 0!==e("#set-post-thumbnail > img").prop("src")&&t.setFeaturedImage(e("#set-post-thumbnail ").html()),!ve())return;let a,l;(0,d.subscribe)((()=>{const e=(0,d.select)("core/editor").getEditedPostAttribute("featured_media");if(function(e){return"number"==typeof e&&e>0}(e)&&(a=(0,d.select)("core").getMedia(e),void 0!==a&&a!==l)){l=a;const e=`<img src="${a.source_url}" alt="${a.alt_text}" >`;t.setFeaturedImage(e)}}))}(a()),function(e){e(document).ready((function(e){void 0!==wp.media&&e(".wpseo_image_upload_button").each((function(t,s){const n=function(t){let s=(t=e(t)).data("target");return s&&""!==s||(s=e(t).attr("id").replace(/_button$/,"")),s}(s),i=e(s).data("target-id"),r=e("#"+n),o=e("#"+i);var a=wp.media.frames.file_frame=wp.media({title:wpseoScriptData.media.choose_image,button:{text:wpseoScriptData.media.choose_image},multiple:!1,library:{type:"image"}});a.on("select",(function(){var e=a.state().get("selection").first().toJSON();r.val(e.url),o.val(e.id)}));const l=e(s);l.click((function(e){e.preventDefault(),a.open()})),l.siblings(".wpseo_image_remove_button").on("click",(e=>{e.preventDefault(),r.val(""),o.val("")}))}))}))}(a()),function(e){function t(){e("#copy-home-meta-description").on("click",(function(){e("#open_graph_frontpage_desc").val(e("#meta_description").val())}))}function s(){var t=e("#wpseo-conf");if(t.length){var s=t.attr("action").split("#")[0];t.attr("action",s+window.location.hash)}}function n(){var t=window.location.hash.replace("#top#","");-1!==t.search("#top")&&(t=window.location.hash.replace("#top%23","")),""!==t&&"#"!==t.charAt(0)||(t=e(".wpseotab").attr("id")),e("#"+t).addClass("active"),e("#"+t+"-tab").addClass("nav-tab-active").trigger("click")}function i(t){const s=e("#noindex-author-noposts-wpseo-container");t?s.show():s.hide()}e.fn._wpseoIsInViewport=function(){const t=e(this).offset().top,s=t+e(this).outerHeight(),n=e(window).scrollTop(),i=n+e(window).height();return t>n&&s<i},e(window).on("hashchange",(function(){n(),s()})),window.setWPOption=function(t,s,n,i){e.post(ajaxurl,{action:"wpseo_set_option",option:t,newval:s,_wpnonce:i},(function(t){t&&e("#"+n).hide()}))},window.wpseoCopyHomeMeta=t,window.wpseoSetTabHash=s,e(document).ready((function(){s(),"function"==typeof window.wpseoRedirectOldFeaturesTabToNewSettings&&window.wpseoRedirectOldFeaturesTabToNewSettings(),e("#disable-author input[type='radio']").on("change",(function(){e(this).is(":checked")&&e("#author-archives-titles-metas-content").toggle("off"===e(this).val())})).trigger("change");const r=e("#noindex-author-wpseo-off"),o=e("#noindex-author-wpseo-on");r.is(":checked")||i(!1),o.on("change",(()=>{e(this).is(":checked")||i(!1)})),r.on("change",(()=>{e(this).is(":checked")||i(!0)})),e("#disable-date input[type='radio']").on("change",(function(){e(this).is(":checked")&&e("#date-archives-titles-metas-content").toggle("off"===e(this).val())})).trigger("change"),e("#disable-attachment input[type='radio']").on("change",(function(){e(this).is(":checked")&&e("#media_settings").toggle("off"===e(this).val())})).trigger("change"),e("#disable-post_format").on("change",(function(){e("#post_format-titles-metas").toggle(e(this).is(":not(:checked)"))})).trigger("change"),e("#zapier_integration_active input[type='radio']").on("change",(function(){e(this).is(":checked")&&e("#zapier-connection").toggle("on"===e(this).val())})).trigger("change"),e("#wpseo-tabs").find("a").on("click",(function(t){var s,n,i,r=!0;if(s=e(this),n=!!e("#first-time-configuration-tab").filter(".nav-tab-active").length,i=!!s.filter("#first-time-configuration-tab").length,n&&!i&&window.isStepBeingEdited&&(r=confirm((0,g.__)("There are unsaved changes in one or more steps. Leaving means that those changes may not be saved. Are you sure you want to leave?","wordpress-seo"))),r){window.isStepBeingEdited=!1,e("#wpseo-tabs").find("a").removeClass("nav-tab-active"),e(".wpseotab").removeClass("active");var o=e(this).attr("id").replace("-tab",""),a=e("#"+o);a.addClass("active"),e(this).addClass("nav-tab-active"),a.hasClass("nosave")?e("#wpseo-submit-container").hide():e("#wpseo-submit-container").show(),e(window).trigger("yoast-seo-tab-change"),"first-time-configuration"===o?(e(".notice-yoast").slideUp(),e(".yoast_premium_upsell").slideUp(),e("#sidebar-container").hide()):(e(".notice-yoast").slideDown(),e(".yoast_premium_upsell").slideDown(),e("#sidebar-container").show())}else t.preventDefault(),e("#first-time-configuration-tab").trigger("focus")})),e("#yoast-first-time-configuration-notice a").on("click",(function(){e("#first-time-configuration-tab").click()})),e("#company_or_person").on("change",(function(){var t=e(this).val();"company"===t?(e("#knowledge-graph-company").show(),e("#knowledge-graph-person").hide()):"person"===t?(e("#knowledge-graph-company").hide(),e("#knowledge-graph-person").show()):(e("#knowledge-graph-company").hide(),e("#knowledge-graph-person").hide())})).trigger("change"),e(".switch-yoast-seo input").on("keydown",(function(e){"keydown"===e.type&&13===e.which&&e.preventDefault()})),e("body").on("click","button.toggleable-container-trigger",(t=>{const s=e(t.currentTarget),n=s.parent().siblings(".toggleable-container");n.toggleClass("toggleable-container-hidden"),s.attr("aria-expanded",!n.hasClass("toggleable-container-hidden")).find("span").toggleClass("dashicons-arrow-up-alt2 dashicons-arrow-down-alt2")}));const a=e("#opengraph"),c=e("#wpseo-opengraph-settings");a.length&&c.length&&(c.toggle(a[0].checked),a.on("change",(e=>{c.toggle(e.target.checked)}))),t(),n(),function(){if(!e("#enable_xml_sitemap input[type=radio]").length)return;const t=e("#yoast-seo-sitemaps-disabled-warning");e("#enable_xml_sitemap input[type=radio]").on("change",(function(){"off"===this.value?t.show():t.hide()}))}(),function(){const t=e("#wpseo-submit-container-float"),s=e("#wpseo-submit-container-fixed");if(!t.length||!s.length)return;function n(){t._wpseoIsInViewport()?s.hide():s.show()}e(window).on("resize scroll",(0,l.debounce)(n,100)),e(window).on("yoast-seo-tab-change",n);const i=e(".wpseo-message");i.length&&window.setTimeout((()=>{i.fadeOut()}),5e3),n()}(),"undefined"!=typeof ClipboardJS&&new ClipboardJS("#copy-zapier-api-key").on("success",(function(t){t.clearSelection(),e(t.trigger).trigger("focus")}))}))}(a()),(()=>{if((0,d.select)("yoast-seo/editor").getPreference("isInsightsEnabled",!1))(0,d.dispatch)("yoast-seo/editor").loadEstimatedReadingTime(),(0,d.subscribe)((0,l.debounce)(Vs(),1500,{maxWait:3e3}))})(),window.wpseoScriptData.postType&&!["attachment"].includes(window.wpseoScriptData.postType)&&(()=>{const e=(0,d.select)(te).getIsPremium(),t=(0,d.select)(te).getIsWooSeoUpsell(),s=(0,d.select)(te).getIsProduct()?!e||t:!e;(0,h.addFilter)("yoast.replacementVariableEditor.additionalButtons","yoast/yoast-seo-premium/AiGenerator",((e,{fieldId:t})=>(s&&e.push((0,c.createElement)(p.Fill,{name:`yoast.replacementVariableEditor.additionalButtons.${t}`},(0,c.createElement)(ee,{fieldId:t}))),e)))})()}))})()})();