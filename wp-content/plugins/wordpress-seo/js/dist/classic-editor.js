(()=>{var e={6746:(e,t,s)=>{"use strict";var n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},r=i(s(9196)),a=i(s(9156)),o=i(s(6743));function i(e){return e&&e.__esModule?e:{default:e}}var l=void 0;function c(e,t){var s,o,i,d,u,p,m,h,g=[],y={};for(p=0;p<e.length;p++)if("string"!==(u=e[p]).type){if(!t.hasOwnProperty(u.value)||void 0===t[u.value])throw new Error("Invalid interpolation, missing component node: `"+u.value+"`");if("object"!==n(t[u.value]))throw new Error("Invalid interpolation, component node must be a ReactElement or null: `"+u.value+"`","\n> "+l);if("componentClose"===u.type)throw new Error("Missing opening component token: `"+u.value+"`");if("componentOpen"===u.type){s=t[u.value],i=p;break}g.push(t[u.value])}else g.push(u.value);return s&&(d=function(e,t){var s,n,r=t[e],a=0;for(n=e+1;n<t.length;n++)if((s=t[n]).value===r.value){if("componentOpen"===s.type){a++;continue}if("componentClose"===s.type){if(0===a)return n;a--}}throw new Error("Missing closing component token `"+r.value+"`")}(i,e),m=c(e.slice(i+1,d),t),o=r.default.cloneElement(s,{},m),g.push(o),d<e.length-1&&(h=c(e.slice(d+1),t),g=g.concat(h))),1===g.length?g[0]:(g.forEach((function(e,t){e&&(y["interpolation-child-"+t]=e)})),(0,a.default)(y))}t.Z=function(e){var t=e.mixedString,s=e.components,r=e.throwErrors;if(l=t,!s)return t;if("object"!==(void 0===s?"undefined":n(s))){if(r)throw new Error("Interpolation Error: unable to process `"+t+"` because components is not an object");return t}var a=(0,o.default)(t);try{return c(a,s)}catch(e){if(r)throw new Error("Interpolation Error: unable to process `"+t+"` because of error `"+e.message+"`");return t}}},6743:e=>{"use strict";function t(e){return e.match(/^\{\{\//)?{type:"componentClose",value:e.replace(/\W/g,"")}:e.match(/\/\}\}$/)?{type:"componentSelfClosing",value:e.replace(/\W/g,"")}:e.match(/^\{\{/)?{type:"componentOpen",value:e.replace(/\W/g,"")}:{type:"string",value:e}}e.exports=function(e){return e.split(/(\{\{\/?\s*\w+\s*\/?\}\})/g).map(t)}},9156:(e,t,s)=>{"use strict";var n=s(9196),r="function"==typeof Symbol&&Symbol.for&&Symbol.for("react.element")||60103,a=s(7942),o=s(9179),i=s(397),l=".",c=":",d="function"==typeof Symbol&&Symbol.iterator,u="@@iterator";function p(e,t){return e&&"object"==typeof e&&null!=e.key?(s=e.key,n={"=":"=0",":":"=2"},"$"+(""+s).replace(/[=:]/g,(function(e){return n[e]}))):t.toString(36);var s,n}function m(e,t,s,n){var a,i=typeof e;if("undefined"!==i&&"boolean"!==i||(e=null),null===e||"string"===i||"number"===i||"object"===i&&e.$$typeof===r)return s(n,e,""===t?l+p(e,0):t),1;var h=0,g=""===t?l:t+c;if(Array.isArray(e))for(var y=0;y<e.length;y++)h+=m(a=e[y],g+p(a,y),s,n);else{var f=function(e){var t=e&&(d&&e[d]||e[u]);if("function"==typeof t)return t}(e);if(f)for(var b,w=f.call(e),E=0;!(b=w.next()).done;)h+=m(a=b.value,g+p(a,E++),s,n);else if("object"===i){var v=""+e;o(!1,"Objects are not valid as a React child (found: %s).%s","[object Object]"===v?"object with keys {"+Object.keys(e).join(", ")+"}":v,"")}}return h}var h=/\/+/g;function g(e){return(""+e).replace(h,"$&/")}var y,f,b=w,w=function(e){var t=this;if(t.instancePool.length){var s=t.instancePool.pop();return t.call(s,e),s}return new t(e)};function E(e,t,s,n){this.result=e,this.keyPrefix=t,this.func=s,this.context=n,this.count=0}function v(e,t,s){var r,o,i=e.result,l=e.keyPrefix,c=e.func,d=e.context,u=c.call(d,t,e.count++);Array.isArray(u)?k(u,i,s,a.thatReturnsArgument):null!=u&&(n.isValidElement(u)&&(r=u,o=l+(!u.key||t&&t.key===u.key?"":g(u.key)+"/")+s,u=n.cloneElement(r,{key:o},void 0!==r.props?r.props.children:void 0)),i.push(u))}function k(e,t,s,n,r){var a="";null!=s&&(a=g(s)+"/");var o=E.getPooled(t,a,n,r);!function(e,t,s){null==e||m(e,"",t,s)}(e,v,o),E.release(o)}E.prototype.destructor=function(){this.result=null,this.keyPrefix=null,this.func=null,this.context=null,this.count=0},y=function(e,t,s,n){var r=this;if(r.instancePool.length){var a=r.instancePool.pop();return r.call(a,e,t,s,n),a}return new r(e,t,s,n)},(f=E).instancePool=[],f.getPooled=y||b,f.poolSize||(f.poolSize=10),f.release=function(e){var t=this;o(e instanceof t,"Trying to release an instance into a pool of a different type."),e.destructor(),t.instancePool.length<t.poolSize&&t.instancePool.push(e)},e.exports=function(e){if("object"!=typeof e||!e||Array.isArray(e))return i(!1,"React.addons.createFragment only accepts a single object. Got: %s",e),e;if(n.isValidElement(e))return i(!1,"React.addons.createFragment does not accept a ReactElement without a wrapper object."),e;o(1!==e.nodeType,"React.addons.createFragment(...): Encountered an invalid child; DOM elements are not valid children of React components.");var t=[];for(var s in e)k(e[s],t,s,a.thatReturnsArgument);return t}},7942:e=>{"use strict";function t(e){return function(){return e}}var s=function(){};s.thatReturns=t,s.thatReturnsFalse=t(!1),s.thatReturnsTrue=t(!0),s.thatReturnsNull=t(null),s.thatReturnsThis=function(){return this},s.thatReturnsArgument=function(e){return e},e.exports=s},9179:e=>{"use strict";e.exports=function(e,t,s,n,r,a,o,i){if(!e){var l;if(void 0===t)l=new Error("Minified exception occurred; use the non-minified dev environment for the full error message and additional helpful warnings.");else{var c=[s,n,r,a,o,i],d=0;(l=new Error(t.replace(/%s/g,(function(){return c[d++]})))).name="Invariant Violation"}throw l.framesToPop=1,l}}},397:(e,t,s)=>{"use strict";var n=s(7942);e.exports=n},4530:(e,t)=>{var s;!function(){"use strict";var n={}.hasOwnProperty;function r(){for(var e=[],t=0;t<arguments.length;t++){var s=arguments[t];if(s){var a=typeof s;if("string"===a||"number"===a)e.push(s);else if(Array.isArray(s)){if(s.length){var o=r.apply(null,s);o&&e.push(o)}}else if("object"===a){if(s.toString!==Object.prototype.toString&&!s.toString.toString().includes("[native code]")){e.push(s.toString());continue}for(var i in s)n.call(s,i)&&s[i]&&e.push(i)}}}return e.join(" ")}e.exports?(r.default=r,e.exports=r):void 0===(s=function(){return r}.apply(t,[]))||(e.exports=s)}()},9196:e=>{"use strict";e.exports=window.React}},t={};function s(n){var r=t[n];if(void 0!==r)return r.exports;var a=t[n]={exports:{}};return e[n](a,a.exports,s),a.exports}s.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return s.d(t,{a:t}),t},s.d=(e,t)=>{for(var n in t)s.o(t,n)&&!s.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},s.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),(()=>{"use strict";var e=s(9196),t=s.n(e);const n=window.wp.element,r=window.wp.components,a=window.yoast.propTypes;var o=s.n(a);const i=window.lodash,l=window.yoast.styledComponents;var c=s.n(l);const d=window.yoast.externals.contexts,u=({theme:t,location:s,children:n})=>(0,e.createElement)(d.LocationProvider,{value:s},(0,e.createElement)(l.ThemeProvider,{theme:t},n));u.propTypes={theme:o().object.isRequired,location:o().oneOf(["sidebar","metabox","modal"]).isRequired,children:o().element.isRequired};const p=u,m=window.yoast.uiLibrary,h=window.wp.data;const g=window.yoast.componentsNew,y=window.yoast.styleGuide,f=window.yoast.analysis;function b(e){switch(e){case"loading":return{icon:"loading-spinner",color:y.colors.$color_green_medium_light};case"not-set":return{icon:"seo-score-none",color:y.colors.$color_score_icon};case"noindex":return{icon:"seo-score-none",color:y.colors.$color_noindex};case"good":return{icon:"seo-score-good",color:y.colors.$color_green_medium};case"ok":return{icon:"seo-score-ok",color:y.colors.$color_ok};default:return{icon:"seo-score-bad",color:y.colors.$color_red}}}function w({target:e,children:t}){let s=e;return"string"==typeof e&&(s=document.getElementById(e)),s?(0,n.createPortal)(t,s):null}w.propTypes={target:o().oneOfType([o().string,o().object]).isRequired,children:o().node.isRequired};const E=({target:t,scoreIndicator:s})=>(0,e.createElement)(w,{target:t},(0,e.createElement)(g.SvgIcon,{...b(s)}));E.propTypes={target:o().string.isRequired,scoreIndicator:o().string.isRequired};const v=E,k=window.wp.i18n,_=({handleRefreshClick:t,supportLink:s})=>(0,e.createElement)("div",{className:"yst-flex yst-gap-2"},(0,e.createElement)(m.Button,{onClick:t},(0,k.__)("Refresh this page","wordpress-seo")),(0,e.createElement)(m.Button,{variant:"secondary",as:"a",href:s,target:"_blank",rel:"noopener"},(0,k.__)("Contact support","wordpress-seo")));_.propTypes={handleRefreshClick:o().func.isRequired,supportLink:o().string.isRequired};const T=({handleRefreshClick:t,supportLink:s})=>(0,e.createElement)("div",{className:"yst-grid yst-grid-cols-1 yst-gap-y-2"},(0,e.createElement)(m.Button,{className:"yst-order-last",onClick:t},(0,k.__)("Refresh this page","wordpress-seo")),(0,e.createElement)(m.Button,{variant:"secondary",as:"a",href:s,target:"_blank",rel:"noopener"},(0,k.__)("Contact support","wordpress-seo")));T.propTypes={handleRefreshClick:o().func.isRequired,supportLink:o().string.isRequired};const x=({error:t,children:s})=>(0,e.createElement)("div",{role:"alert",className:"yst-max-w-screen-sm yst-p-8 yst-space-y-4"},(0,e.createElement)(m.Title,null,(0,k.__)("Something went wrong. An unexpected error occurred.","wordpress-seo")),(0,e.createElement)("p",null,(0,k.__)("We're very sorry, but it seems like the following error has interrupted our application:","wordpress-seo")),(0,e.createElement)(m.Alert,{variant:"error"},(null==t?void 0:t.message)||(0,k.__)("Undefined error message.","wordpress-seo")),(0,e.createElement)("p",null,(0,k.__)("Unfortunately, this means that any unsaved changes in this section will be lost. You can try and refresh this page to resolve the problem. If this error still occurs, please get in touch with our support team, and we'll get you all the help you need!","wordpress-seo")),s);x.propTypes={error:o().object.isRequired,children:o().node},x.VerticalButtons=T,x.HorizontalButtons=_;const S=({error:t})=>{const s=(0,n.useCallback)((()=>{var e,t;return null===(e=window)||void 0===e||null===(t=e.location)||void 0===t?void 0:t.reload()}),[]),r=(0,h.useSelect)((e=>e("yoast-seo/editor").selectLink("https://yoa.st/metabox-error-support")),[]),a=(0,h.useSelect)((e=>e("yoast-seo/editor").getPreference("isRtl",!1)),[]);return(0,n.useEffect)((()=>{document.querySelectorAll('[id^="wpseo-meta-tab-"]').forEach((e=>{!function(e){const t=document.querySelector(`#${e}`);null!==t&&(t.style.opacity="0.5",t.style.pointerEvents="none",t.setAttribute("aria-disabled","true"),t.classList.contains("yoast-active-tab")&&t.classList.remove("yoast-active-tab"))}(e.id)}))}),[]),(0,e.createElement)(m.Root,{context:{isRtl:a}},(0,e.createElement)(x,{error:t},(0,e.createElement)(x.HorizontalButtons,{supportLink:r,handleRefreshClick:s}),(0,e.createElement)(v,{target:"wpseo-seo-score-icon",scoreIndicator:"not-set"}),(0,e.createElement)(v,{target:"wpseo-readability-score-icon",scoreIndicator:"not-set"}),(0,e.createElement)(v,{target:"wpseo-inclusive-language-score-icon",scoreIndicator:"not-set"})))};function R({theme:t}){return(0,e.createElement)(p,{theme:t,location:"metabox"},(0,e.createElement)(m.ErrorBoundary,{FallbackComponent:S},(0,e.createElement)(r.Slot,{name:"YoastMetabox"},(e=>{return void 0===(t=e).length?t:(0,i.flatten)(t).sort(((e,t)=>void 0===e.props.renderPriority?1:e.props.renderPriority-t.props.renderPriority));var t}))))}S.propTypes={error:o().object.isRequired};const C=window.wp.compose,I=e.forwardRef((function(t,s){return e.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true",ref:s},t),e.createElement("path",{d:"M2 11a1 1 0 011-1h2a1 1 0 011 1v5a1 1 0 01-1 1H3a1 1 0 01-1-1v-5zM8 7a1 1 0 011-1h2a1 1 0 011 1v9a1 1 0 01-1 1H9a1 1 0 01-1-1V7zM14 4a1 1 0 011-1h2a1 1 0 011 1v12a1 1 0 01-1 1h-2a1 1 0 01-1-1V4z"}))}));var L=s(4530),A=s.n(L);const P=({className:t,...s})=>(0,e.createElement)("span",{className:A()("yst-grow yst-overflow-hidden yst-overflow-ellipsis yst-whitespace-nowrap yst-font-wp yst-text-[#555] yst-text-base yst-leading-[normal] yst-subpixel-antialiased yst-text-left",t),...s});P.displayName="MetaboxButton.Text",P.propTypes={className:o().string},P.defaultProps={className:""};const F=({className:t,...s})=>(0,e.createElement)("button",{type:"button",className:A()("yst-flex yst-items-center yst-w-full yst-pt-4 yst-pb-4 yst-pr-4 yst-pl-6 yst-space-x-2 yst-border-t yst-border-t-[rgb(0,0,0,0.2)] yst-rounded-none yst-transition-all hover:yst-bg-[#f0f0f0] focus:yst-outline focus:yst-outline-[1px] focus:yst-outline-[color:#0066cd] focus:-yst-outline-offset-1 focus:yst-shadow-[0_0_3px_rgba(8,74,103,0.8)]",t),...s});F.propTypes={className:o().string},F.defaultProps={className:""},F.Text=P;const M=window.yoast.helpers,q=c().div`
	min-width: 600px;

	@media screen and ( max-width: 680px ) {
		min-width: 0;
		width: 86vw;
	}
`,D=c().div`
	@media screen and ( min-width: 600px ) {
		max-width: 420px;
	}
`,O=(c()(g.Icon)`
	float: ${(0,M.getDirectionalStyle)("right","left")};
	margin: ${(0,M.getDirectionalStyle)("0 0 16px 16px","0 16px 16px 0")};

	&& {
		width: 150px;
		height: 150px;

		@media screen and ( max-width: 680px ) {
			width: 80px;
			height: 80px;
		}
	}
`,"yoast yoast-gutenberg-modal"),N=t=>{const{title:s,className:n,showYoastIcon:a,additionalClassName:o,...i}=t,l=a?(0,e.createElement)("span",{className:"yoast-icon"}):null;return(0,e.createElement)(r.Modal,{title:s,className:`${n} ${o}`,icon:l,...i},t.children)};N.propTypes={title:o().string,className:o().string,showYoastIcon:o().bool,children:o().oneOfType([o().node,o().arrayOf(o().node)]),additionalClassName:o().string},N.defaultProps={title:"Yoast SEO",className:O,showYoastIcon:!0,children:null,additionalClassName:""};const $=N;var B,U;function W(){return W=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var s=arguments[t];for(var n in s)Object.prototype.hasOwnProperty.call(s,n)&&(e[n]=s[n])}return e},W.apply(this,arguments)}const K=t=>e.createElement("svg",W({xmlns:"http://www.w3.org/2000/svg","aria-hidden":"true",viewBox:"0 0 425 456.27"},t),B||(B=e.createElement("path",{d:"M73 405.26a66.79 66.79 0 0 1-6.54-1.7 64.75 64.75 0 0 1-6.28-2.31c-1-.42-2-.89-3-1.37-1.49-.72-3-1.56-4.77-2.56-1.5-.88-2.71-1.64-3.83-2.39-.9-.61-1.8-1.26-2.68-1.92a70.154 70.154 0 0 1-5.08-4.19 69.21 69.21 0 0 1-8.4-9.17c-.92-1.2-1.68-2.25-2.35-3.24a70.747 70.747 0 0 1-3.44-5.64 68.29 68.29 0 0 1-8.29-32.55V142.13a68.26 68.26 0 0 1 8.29-32.55c1-1.92 2.21-3.82 3.44-5.64s2.55-3.58 4-5.27a69.26 69.26 0 0 1 14.49-13.25C50.37 84.19 52.27 83 54.2 82A67.59 67.59 0 0 1 73 75.09a68.75 68.75 0 0 1 13.75-1.39h169.66L263 55.39H86.75A86.84 86.84 0 0 0 0 142.13v196.09A86.84 86.84 0 0 0 86.75 425h11.32v-18.35H86.75A68.75 68.75 0 0 1 73 405.26zM368.55 60.85l-1.41-.53-6.41 17.18 1.41.53a68.06 68.06 0 0 1 8.66 4c1.93 1 3.82 2.2 5.65 3.43A69.19 69.19 0 0 1 391 98.67c1.4 1.68 2.72 3.46 3.95 5.27s2.39 3.72 3.44 5.64a68.29 68.29 0 0 1 8.29 32.55v264.52H233.55l-.44.76c-3.07 5.37-6.26 10.48-9.49 15.19L222 425h203V142.13a87.2 87.2 0 0 0-56.45-81.28z"})),U||(U=e.createElement("path",{stroke:"#000",strokeMiterlimit:10,strokeWidth:3.81,d:"M119.8 408.28v46c28.49-1.12 50.73-10.6 69.61-29.58 19.45-19.55 36.17-50 52.61-96L363.94 1.9H305l-98.25 272.89-48.86-153h-54l71.7 184.18a75.67 75.67 0 0 1 0 55.12c-7.3 18.68-20.25 40.66-55.79 47.19z"}))),H=t=>(0,e.createElement)("div",{className:"yoast components-panel__body"},(0,e.createElement)("h2",{className:"components-panel__body-title"},(0,e.createElement)("button",{id:t.id,onClick:t.onClick,className:"components-button components-panel__body-toggle",type:"button"},t.prefixIcon&&(0,e.createElement)("span",{className:"yoast-icon-span",style:{fill:`${t.prefixIcon&&t.prefixIcon.color||""}`}},(0,e.createElement)(g.SvgIcon,{size:t.prefixIcon.size,icon:t.prefixIcon.icon})),(0,e.createElement)("span",{className:"yoast-title-container"},(0,e.createElement)("div",{className:"yoast-title"},t.title),(0,e.createElement)("div",{className:"yoast-subtitle"},t.subTitle)),t.children,t.suffixIcon&&(0,e.createElement)(g.SvgIcon,{size:t.suffixIcon.size,icon:t.suffixIcon.icon}),t.SuffixHeroIcon))),Y=H;H.propTypes={onClick:o().func.isRequired,title:o().string.isRequired,id:o().string,subTitle:o().string,suffixIcon:o().object,SuffixHeroIcon:o().object,prefixIcon:o().object,children:o().node},H.defaultProps={id:"",suffixIcon:null,SuffixHeroIcon:null,prefixIcon:null,subTitle:"",children:null};const z=window.moment;var j=s.n(z),V=s(6746);const G=(0,M.makeOutboundLink)(),Z=t=>{const s=(0,k.sprintf)(/* translators: %d expands to the amount of allowed keyphrases on a free account, %s expands to a link to Wincher plans. */
(0,k.__)("You've reached the maximum amount of %d keyphrases you can add to your Wincher account. If you wish to add more keyphrases, please %s.","wordpress-seo"),t.limit,"{{updateWincherPlanLink/}}");return(0,e.createElement)(g.Alert,{type:"error"},(0,V.Z)({mixedString:s,components:{updateWincherPlanLink:(0,e.createElement)(G,{href:wpseoAdminGlobalL10n["links.wincher.pricing"]},(0,k.sprintf)(/* translators: %s : Expands to "Wincher". */
(0,k.__)("upgrade your %s plan","wordpress-seo"),"Wincher"))}}))};Z.propTypes={limit:o().number},Z.defaultProps={limit:10};const X=Z,Q=()=>(0,e.createElement)(g.Alert,{type:"error"},(0,k.__)("Something went wrong while tracking the ranking position(s) of your page. Please try again later.","wordpress-seo")),J=window.wp.apiFetch;var ee=s.n(J);async function te(e,t,s,n=200){try{const r=await e();return!!r&&(r.status===n?t(r):s(r))}catch(e){console.error(e.message)}}async function se(e){try{return await ee()(e)}catch(e){return e.error&&e.status?e:e instanceof Response&&await e.json()}}async function ne(e){return(0,i.isArray)(e)||(e=[e]),await se({path:"yoast/v1/wincher/keyphrases/track",method:"POST",data:{keyphrases:e}})}const re=c().p`
	color: ${y.colors.$color_pink_dark};
	font-size: 14px;
	font-weight: 700;
	margin: 13px 0 10px;
`,ae=c()(g.SvgIcon)`
	margin-right: 5px;
	vertical-align: middle;
`,oe=c().button`
	position: absolute;
	top: 9px;
	right: 9px;
	border: none;
    background: none;
    cursor: pointer;
`,ie=c().p`
	font-size: 13px;
	font-weight: 500;
	margin: 10px 0 13px;
`,le=c().div`
	position: relative;
	background: ${e=>e.isTitleShortened?"#F5F7F7":"transparent"};
	border: 1px solid #C7C7C7;
	border-left: 4px solid${y.colors.$color_pink_dark};
	padding: 0 16px;
	margin-bottom: 1.5em;
`,ce=e=>{const[t,s]=(0,n.useState)(null);return(0,n.useEffect)((()=>{e&&!t&&async function(){return await se({path:"yoast/v1/wincher/account/limit",method:"GET"})}().then((e=>s(e)))}),[t]),t};ce.propTypes={limit:o().bool.isRequired};const de=({limit:t,usage:s,isTitleShortened:n,isFreeAccount:r})=>{const a=(0,k.sprintf)(
/* Translators: %1$s expands to the number of used keywords.
   * %2$s expands to the account keywords limit.
   */
(0,k.__)("Your are tracking %1$s out of %2$s keyphrases included in your free account.","wordpress-seo"),s,t),o=(0,k.sprintf)(
/* Translators: %1$s expands to the number of used keywords.
   * %2$s expands to the account keywords limit.
   */
(0,k.__)("Your are tracking %1$s out of %2$s keyphrases included in your account.","wordpress-seo"),s,t),i=r?a:o,l=(0,k.sprintf)(
/* Translators: %1$s expands to the number of used keywords.
   * %2$s expands to the account keywords limit.
   */
(0,k.__)("Keyphrases tracked: %1$s/%2$s","wordpress-seo"),s,t),c=n?l:i;return(0,e.createElement)(re,null,n&&(0,e.createElement)(ae,{icon:"exclamation-triangle",color:y.colors.$color_pink_dark,size:"14px"}),c)};de.propTypes={limit:o().number.isRequired,usage:o().number.isRequired,isTitleShortened:o().bool,isFreeAccount:o().bool};const ue=(0,M.makeOutboundLink)(),pe=({discount:t,months:s})=>{const n=(0,e.createElement)(ue,{href:wpseoAdminGlobalL10n["links.wincher.upgrade"],style:{fontWeight:600}},(0,k.sprintf)(/* Translators: %s : Expands to "Wincher". */
(0,k.__)("Click here to upgrade your %s plan","wordpress-seo"),"Wincher"));if(!t||!s)return(0,e.createElement)(ie,null,n);const r=100*t,a=(0,k.sprintf)(
/* Translators: %1$s expands to upgrade account link.
   * %2$s expands to the upgrade discount value.
   * %3$s expands to the upgrade discount duration e.g. 2 months.
   */
(0,k.__)("%1$s and get an exclusive %2$s discount for %3$s month(s).","wordpress-seo"),"{{wincherAccountUpgradeLink/}}",r+"%",s);return(0,e.createElement)(ie,null,(0,V.Z)({mixedString:a,components:{wincherAccountUpgradeLink:n}}))};pe.propTypes={discount:o().number,months:o().number};const me=({onClose:t,isTitleShortened:s,trackingInfo:r})=>{const a=(()=>{const[e,t]=(0,n.useState)(null);return(0,n.useEffect)((()=>{e||async function(){return await se({path:"yoast/v1/wincher/account/upgrade-campaign",method:"GET"})}().then((e=>t(e)))}),[e]),e})();if(null===r)return null;const{limit:o,usage:i}=r;if(!(o&&i/o>=.8))return null;const l=Boolean(null==a?void 0:a.discount);return(0,e.createElement)(le,{isTitleShortened:s},t&&(0,e.createElement)(oe,{type:"button","aria-label":(0,k.__)("Close the upgrade callout","wordpress-seo"),onClick:t},(0,e.createElement)(g.SvgIcon,{icon:"times-circle",color:y.colors.$color_pink_dark,size:"14px"})),(0,e.createElement)(de,{...r,isTitleShortened:s,isFreeAccount:l}),(0,e.createElement)(pe,{discount:null==a?void 0:a.discount,months:null==a?void 0:a.months}))};me.propTypes={onClose:o().func,isTitleShortened:o().bool,trackingInfo:o().object};const he=me,ge=()=>(0,e.createElement)(g.Alert,{type:"success"},(0,k.sprintf)(/* translators: %s: Expands to "Wincher". */
(0,k.__)("You have successfully connected to %s! You can now track the SEO performance for the keyphrase(s) of this page.","wordpress-seo"),"Wincher")),ye=()=>(0,e.createElement)(g.Alert,{type:"info"},(0,k.sprintf)(/* translators: %s: Expands to "Wincher". */
(0,k.__)("%s is currently tracking the ranking position(s) of your page. This may take a few minutes. Please wait or check back later.","wordpress-seo"),"Wincher")),fe=({data:t,mapChartDataToTableData:s,dataTableCaption:n,dataTableHeaderLabels:r,isDataTableVisuallyHidden:a})=>t.length!==r.length?(0,e.createElement)("p",null,(0,k.__)("The number of headers and header labels don't match.","wordpress-seo")):(0,e.createElement)("div",{className:a?"screen-reader-text":null},(0,e.createElement)("table",null,(0,e.createElement)("caption",null,n),(0,e.createElement)("thead",null,(0,e.createElement)("tr",null,r.map(((t,s)=>(0,e.createElement)("th",{key:s},t))))),(0,e.createElement)("tbody",null,(0,e.createElement)("tr",null,t.map(((t,n)=>(0,e.createElement)("td",{key:n},s(t.y))))))));fe.propTypes={data:o().arrayOf(o().shape({x:o().number,y:o().number})).isRequired,mapChartDataToTableData:o().func,dataTableCaption:o().string.isRequired,dataTableHeaderLabels:o().array.isRequired,isDataTableVisuallyHidden:o().bool},fe.defaultProps={mapChartDataToTableData:null,isDataTableVisuallyHidden:!0};const be=fe,we=({data:t,width:s,height:r,fillColor:a,strokeColor:o,strokeWidth:i,className:l,mapChartDataToTableData:c,dataTableCaption:d,dataTableHeaderLabels:u,isDataTableVisuallyHidden:p})=>{const m=Math.max(1,Math.max(...t.map((e=>e.x)))),h=Math.max(1,Math.max(...t.map((e=>e.y)))),g=r-i,y=t.map((e=>`${e.x/m*s},${g-e.y/h*g+i}`)).join(" "),f=`0,${g+i} `+y+` ${s},${g+i}`;return(0,e.createElement)(n.Fragment,null,(0,e.createElement)("svg",{width:s,height:r,viewBox:`0 0 ${s} ${r}`,className:l,role:"img","aria-hidden":"true",focusable:"false"},(0,e.createElement)("polygon",{fill:a,points:f}),(0,e.createElement)("polyline",{fill:"none",stroke:o,strokeWidth:i,strokeLinejoin:"round",strokeLinecap:"round",points:y})),c&&(0,e.createElement)(be,{data:t,mapChartDataToTableData:c,dataTableCaption:d,dataTableHeaderLabels:u,isDataTableVisuallyHidden:p}))};we.propTypes={data:o().arrayOf(o().shape({x:o().number,y:o().number})).isRequired,width:o().number.isRequired,height:o().number.isRequired,fillColor:o().string,strokeColor:o().string,strokeWidth:o().number,className:o().string,mapChartDataToTableData:o().func,dataTableCaption:o().string.isRequired,dataTableHeaderLabels:o().array.isRequired,isDataTableVisuallyHidden:o().bool},we.defaultProps={fillColor:null,strokeColor:"#000000",strokeWidth:1,className:"",mapChartDataToTableData:null,isDataTableVisuallyHidden:!0};const Ee=we,ve=()=>(0,e.createElement)("p",{className:"yoast-wincher-seo-performance-modal__loading-message"},(0,k.__)("Tracking the ranking position...","wordpress-seo")," ",(0,e.createElement)(g.SvgIcon,{icon:"loading-spinner"})),ke=c()(g.SvgIcon)`
	margin-left: 2px;
	flex-shrink: 0;
	rotate: ${e=>e.isImproving?"-90deg":"90deg"};
`,_e=c().span`
	color: ${e=>e.isImproving?"#69AB56":"#DC3332"};
	font-size: 13px;
	font-weight: 600;
	line-height: 20px;
	margin-right: 2px;
	margin-left: 12px;
`,Te=c().td`
	padding-right: 0 !important;

	& > div {
		margin: 0px;
	}
`,xe=c().td`
	padding-left: 2px !important;
`,Se=c().td.attrs({className:"yoast-table--nopadding"})`
	& > div {
		justify-content: center;
	}
`,Re=c().div`
	display: flex;
	align-items: center;
	& > a {
		box-sizing: border-box;
	}
`,Ce=c().button`
	background: none;
	color: inherit;
	border: none;
	padding: 0;
	font: inherit;
	cursor: pointer;
	outline: inherit;
    display: flex;
    align-items: center;
`,Ie=c().tr`
	background-color: ${e=>e.isEnabled?"#FFFFFF":"#F9F9F9"} !important;
`;function Le(e){return Math.round(100*e)}function Ae({chartData:t}){if((0,i.isEmpty)(t)||(0,i.isEmpty)(t.position))return"?";const s=function(e){return Array.from({length:e.position.history.length},((e,t)=>t+1)).map((e=>(0,k.sprintf)((0,k._n)("%d day","%d days",e,"wordpress-seo"),e)))}(t),n=t.position.history.map(((e,t)=>({x:t,y:101-e.value})));return(0,e.createElement)(Ee,{width:66,height:24,data:n,strokeWidth:1.8,strokeColor:"#498afc",fillColor:"#ade3fc",mapChartDataToTableData:Le,dataTableCaption:(0,k.__)("Keyphrase position in the last 90 days on a scale from 0 to 100.","wordpress-seo"),dataTableHeaderLabels:s})}Ae.propTypes={chartData:o().object},Ae.defaultProps={chartData:{}};const Pe=({rowData:t})=>{var s;if(null==t||null===(s=t.position)||void 0===s||!s.change)return(0,e.createElement)(Ae,{chartData:t});const r=t.position.change<0;return(0,e.createElement)(n.Fragment,null,(0,e.createElement)(Ae,{chartData:t}),(0,e.createElement)(_e,{isImproving:r},Math.abs(t.position.change)),(0,e.createElement)(ke,{icon:"caret-right",color:r?"#69AB56":"#DC3332",size:"14px",isImproving:r}))};function Fe(t){var s;const{keyphrase:r,rowData:a,onTrackKeyphrase:o,onUntrackKeyphrase:l,isFocusKeyphrase:c,isDisabled:d,isLoading:u,isSelected:p,onSelectKeyphrases:m}=t,h=!(0,i.isEmpty)(a),y=!(0,i.isEmpty)(null==a||null===(s=a.position)||void 0===s?void 0:s.history),f=(0,n.useCallback)((()=>{d||(h?l(r,a.id):o(r))}),[r,o,l,h,a,d]),b=(0,n.useCallback)((()=>{m((e=>p?e.filter((e=>e!==r)):e.concat(r)))}),[m,p,r]);return(0,e.createElement)(Ie,{isEnabled:h},(0,e.createElement)(Te,null,y&&(0,e.createElement)(g.Checkbox,{id:"select-"+r,onChange:b,checked:p,label:""})),(0,e.createElement)(xe,null,r,c&&(0,e.createElement)("span",null,"*")),function(t){const{rowData:s,websiteId:r,keyphrase:a,onSelectKeyphrases:o}=t,l=(0,n.useCallback)((()=>{o([a])}),[o,a]),c=!(0,i.isEmpty)(s),d=s&&s.updated_at&&j()(s.updated_at)>=j()().subtract(7,"days"),u=s?(0,k.sprintf)("https://app.wincher.com/websites/%s/keywords?serp=%s&utm_medium=plugin&utm_source=yoast&referer=yoast&partner=yoast",r,s.id):null;return c?d?(0,e.createElement)(n.Fragment,null,(0,e.createElement)("td",null,(0,e.createElement)(Re,null,function(e){return!e||!e.position||e.position.value>100?"> 100":e.position.value}(s),(0,e.createElement)(g.ButtonStyledLink,{variant:"secondary",href:u,style:{height:28,marginLeft:12},rel:"noopener",target:"_blank"},(0,k.__)("View","wordpress-seo")))),(0,e.createElement)("td",{className:"yoast-table--nopadding"},(0,e.createElement)(Ce,{type:"button",onClick:l},(0,e.createElement)(Pe,{rowData:s}))),(0,e.createElement)("td",null,(p=s.updated_at,j()(p).fromNow()))):(0,e.createElement)("td",{className:"yoast-table--nopadding",colSpan:"3"},(0,e.createElement)(ve,null)):(0,e.createElement)("td",{className:"yoast-table--nopadding",colSpan:"3"},(0,e.createElement)("i",null,(0,k.__)("Activate tracking to show the ranking position","wordpress-seo")));var p}(t),(0,e.createElement)(Se,null,function({keyphrase:t,isEnabled:s,toggleAction:n,isLoading:r}){return r?(0,e.createElement)(g.SvgIcon,{icon:"loading-spinner"}):(0,e.createElement)(g.Toggle,{id:`toggle-keyphrase-tracking-${t}`,className:"wincher-toggle",isEnabled:s,onSetToggleState:n,showToggleStateLabel:!1})}({keyphrase:r,isEnabled:h,toggleAction:f,isLoading:u})))}Pe.propTypes={rowData:o().object},Fe.propTypes={rowData:o().object,keyphrase:o().string.isRequired,onTrackKeyphrase:o().func,onUntrackKeyphrase:o().func,isFocusKeyphrase:o().bool,isDisabled:o().bool,isLoading:o().bool,websiteId:o().string,isSelected:o().bool.isRequired,onSelectKeyphrases:o().func.isRequired},Fe.defaultProps={rowData:{},onTrackKeyphrase:()=>{},onUntrackKeyphrase:()=>{},isFocusKeyphrase:!1,isDisabled:!1,isLoading:!1,websiteId:""};const Me=(0,M.makeOutboundLink)(),qe=c().span`
	display: block;
	font-style: italic;

	@media (min-width: 782px) {
		display: inline;
		position: absolute;
		${(0,M.getDirectionalStyle)("right","left")}: 8px;
	}
`,De=c().div`
	width: 100%;
	overflow-y: auto;
`,Oe=c().th`
	pointer-events: ${e=>e.isDisabled?"none":"initial"};
	padding-right: 0 !important;

	& > div {
		margin: 0px;
	}
`,Ne=c().th`
	padding-left: 2px !important;
`,$e=e=>{const t=(0,n.useRef)();return(0,n.useEffect)((()=>{t.current=e})),t.current},Be=(0,i.debounce)((async function(e=null,t=null,s=null,n){return await se({path:"yoast/v1/wincher/keyphrases",method:"POST",data:{keyphrases:e,permalink:s,startAt:t},signal:n})}),500,{leading:!0}),Ue=t=>{const{addTrackedKeyphrase:s,isLoggedIn:r,keyphrases:a,permalink:o,removeTrackedKeyphrase:l,setKeyphraseLimitReached:c,setRequestFailed:d,setRequestSucceeded:u,setTrackedKeyphrases:p,setHasTrackedAll:m,trackAll:h,trackedKeyphrases:y,isNewlyAuthenticated:f,websiteId:b,focusKeyphrase:w,newRequest:E,startAt:v,selectedKeyphrases:_,onSelectKeyphrases:T}=t,x=(0,n.useRef)(),S=(0,n.useRef)(),R=(0,n.useRef)(!1),[C,I]=(0,n.useState)([]),L=(0,n.useCallback)((e=>{const t=e.toLowerCase();return y&&!(0,i.isEmpty)(y)&&y.hasOwnProperty(t)?y[t]:null}),[y]),A=(0,n.useMemo)((()=>async()=>{await te((()=>(S.current&&S.current.abort(),S.current="undefined"==typeof AbortController?null:new AbortController,Be(a,v,o,S.current.signal))),(e=>{u(e),p(e.results)}),(e=>{d(e)}))}),[u,d,p,a,o,v]),P=(0,n.useCallback)((async e=>{const t=(Array.isArray(e)?e:[e]).map((e=>e.toLowerCase()));I((e=>[...e,...t])),await te((()=>ne(t)),(e=>{u(e),s(e.results),A()}),(e=>{400===e.status&&e.limit&&c(e.limit),d(e)}),201),I((e=>(0,i.without)(e,...t)))}),[u,d,c,s,A]),F=(0,n.useCallback)((async(e,t)=>{e=e.toLowerCase(),I((t=>[...t,e])),await te((()=>async function(e){return await se({path:"yoast/v1/wincher/keyphrases/untrack",method:"DELETE",data:{keyphraseID:e}})}(t)),(t=>{u(t),l(e)}),(e=>{d(e)})),I((t=>(0,i.without)(t,e)))}),[u,l,d]),M=(0,n.useCallback)((async e=>{E(),await P(e)}),[E,P]),q=$e(o),D=$e(a),O=$e(v),N=o&&v;(0,n.useEffect)((()=>{r&&N&&(o!==q||(0,i.difference)(a,D).length||v!==O)&&A()}),[r,o,q,a,D,A,N,v,O]),(0,n.useEffect)((()=>{if(r&&h&&null!==y){const e=a.filter((e=>!L(e)));e.length&&P(e),m()}}),[r,h,y,P,m,L,a]),(0,n.useEffect)((()=>{f&&!R.current&&(A(),R.current=!0)}),[f,A]),(0,n.useEffect)((()=>{if(r&&!(0,i.isEmpty)(y))return(0,i.filter)(y,(e=>(0,i.isEmpty)(e.updated_at))).length>0&&(x.current=setInterval((()=>{A()}),1e4)),()=>{clearInterval(x.current)}}),[r,y,A]);const $=r&&null===y,B=(0,n.useMemo)((()=>(0,i.isEmpty)(y)?[]:Object.values(y).filter((e=>{var t;return!(0,i.isEmpty)(null==e||null===(t=e.position)||void 0===t?void 0:t.history)})).map((e=>e.keyword))),[y]),U=(0,n.useMemo)((()=>_.length>0&&B.length>0&&B.every((e=>_.includes(e)))),[_,B]),W=(0,n.useCallback)((()=>{T(U?[]:B)}),[T,U,B]),K=(0,n.useMemo)((()=>(0,i.orderBy)(a,[e=>Object.values(y||{}).map((e=>e.keyword)).includes(e)],["desc"])),[a,y]);return a&&!(0,i.isEmpty)(a)&&(0,e.createElement)(n.Fragment,null,(0,e.createElement)(De,null,(0,e.createElement)("table",{className:"yoast yoast-table"},(0,e.createElement)("thead",null,(0,e.createElement)("tr",null,(0,e.createElement)(Oe,{isDisabled:0===B.length},(0,e.createElement)(g.Checkbox,{id:"select-all",onChange:W,checked:U,label:""})),(0,e.createElement)(Ne,{scope:"col",abbr:(0,k.__)("Keyphrase","wordpress-seo")},(0,k.__)("Keyphrase","wordpress-seo")),(0,e.createElement)("th",{scope:"col",abbr:(0,k.__)("Position","wordpress-seo")},(0,k.__)("Position","wordpress-seo")),(0,e.createElement)("th",{scope:"col",abbr:(0,k.__)("Position over time","wordpress-seo")},(0,k.__)("Position over time","wordpress-seo")),(0,e.createElement)("th",{scope:"col",abbr:(0,k.__)("Last updated","wordpress-seo")},(0,k.__)("Last updated","wordpress-seo")),(0,e.createElement)("th",{scope:"col",abbr:(0,k.__)("Tracking","wordpress-seo")},(0,k.__)("Tracking","wordpress-seo")))),(0,e.createElement)("tbody",null,K.map(((t,s)=>(0,e.createElement)(Fe,{key:`trackable-keyphrase-${s}`,keyphrase:t,onTrackKeyphrase:M,onUntrackKeyphrase:F,rowData:L(t),isFocusKeyphrase:t===w.trim().toLowerCase(),websiteId:b,isDisabled:!r,isLoading:$||C.indexOf(t.toLowerCase())>=0,isSelected:_.includes(t),onSelectKeyphrases:T})))))),(0,e.createElement)("p",{style:{marginBottom:0,position:"relative"}},(0,e.createElement)(Me,{href:wpseoAdminGlobalL10n["links.wincher.login"]},(0,k.sprintf)(/* translators: %s expands to Wincher */
(0,k.__)("Get more insights over at %s","wordpress-seo"),"Wincher")),(0,e.createElement)(qe,null,(0,k.__)("* focus keyphrase","wordpress-seo"))))};Ue.propTypes={addTrackedKeyphrase:o().func.isRequired,isLoggedIn:o().bool,isNewlyAuthenticated:o().bool,keyphrases:o().array,newRequest:o().func.isRequired,removeTrackedKeyphrase:o().func.isRequired,setRequestFailed:o().func.isRequired,setKeyphraseLimitReached:o().func.isRequired,setRequestSucceeded:o().func.isRequired,setTrackedKeyphrases:o().func.isRequired,setHasTrackedAll:o().func.isRequired,trackAll:o().bool,trackedKeyphrases:o().object,websiteId:o().string,permalink:o().string.isRequired,focusKeyphrase:o().string,startAt:o().string,selectedKeyphrases:o().arrayOf(o().string).isRequired,onSelectKeyphrases:o().func.isRequired},Ue.defaultProps={isLoggedIn:!1,isNewlyAuthenticated:!1,keyphrases:[],trackAll:!1,websiteId:"",focusKeyphrase:""};const We=Ue,Ke=(0,C.compose)([(0,h.withSelect)((e=>{const{getWincherWebsiteId:t,getWincherTrackableKeyphrases:s,getWincherLoginStatus:n,getWincherPermalink:r,getFocusKeyphrase:a,isWincherNewlyAuthenticated:o,shouldWincherTrackAll:i}=e("yoast-seo/editor");return{focusKeyphrase:a(),keyphrases:s(),isLoggedIn:n(),trackAll:i(),websiteId:t(),isNewlyAuthenticated:o(),permalink:r()}})),(0,h.withDispatch)((e=>{const{setWincherNewRequest:t,setWincherRequestSucceeded:s,setWincherRequestFailed:n,setWincherSetKeyphraseLimitReached:r,setWincherTrackedKeyphrases:a,setWincherTrackingForKeyphrase:o,setWincherTrackAllKeyphrases:i,unsetWincherTrackingForKeyphrase:l}=e("yoast-seo/editor");return{newRequest:()=>{t()},setRequestSucceeded:e=>{s(e)},setRequestFailed:e=>{n(e)},setKeyphraseLimitReached:e=>{r(e)},addTrackedKeyphrase:e=>{o(e)},removeTrackedKeyphrase:e=>{l(e)},setTrackedKeyphrases:e=>{a(e)},setHasTrackedAll:()=>{i(!1)}}}))])(We),He=(0,M.makeOutboundLink)(),Ye=(0,M.makeOutboundLink)(),ze=()=>{const t=(0,k.sprintf)((0,k.__)(/* translators: %1$s expands to a link to Wincher, %2$s expands to a link to the keyphrase tracking article on Yoast.com */
"With %1$s you can track the ranking position of your page in the search results based on your keyphrase(s). %2$s","wordpress-seo"),"{{wincherLink/}}","{{wincherReadMoreLink/}}");return(0,e.createElement)("p",null,(0,V.Z)({mixedString:t,components:{wincherLink:(0,e.createElement)(He,{href:wpseoAdminGlobalL10n["links.wincher.website"]},"Wincher"),wincherReadMoreLink:(0,e.createElement)(Ye,{href:wpseoAdminL10n["shortlinks.wincher.seo_performance"]},(0,k.__)("Read more about keyphrase tracking with Wincher","wordpress-seo"))}}))},je=()=>(0,e.createElement)(g.Alert,{type:"error"},(0,k.__)("No keyphrase has been set. Please set a keyphrase first.","wordpress-seo")),Ve=()=>(0,e.createElement)(g.Alert,{type:"info"},(0,k.sprintf)(/* translators: %s: Expands to "Wincher". */
(0,k.__)("Automatic tracking of keyphrases is enabled. Your keyphrase(s) will automatically be tracked by %s when you publish your post.","wordpress-seo"),"Wincher"));class Ge{constructor(e,t={},s={}){this.url=e,this.origin=new URL(e).origin,this.eventHandlers=Object.assign({success:{type:"",callback:()=>{}},error:{type:"",callback:()=>{}}},t),this.options=Object.assign({height:570,width:340,title:""},s),this.popup=null,this.createPopup=this.createPopup.bind(this),this.messageHandler=this.messageHandler.bind(this),this.getPopup=this.getPopup.bind(this)}createPopup(){const{height:e,width:t,title:s}=this.options,n=["top="+(window.top.outerHeight/2+window.top.screenY-e/2),"left="+(window.top.outerWidth/2+window.top.screenX-t/2),"width="+t,"height="+e,"resizable=1","scrollbars=1","status=0"];this.popup&&!this.popup.closed||(this.popup=window.open(this.url,s,n.join(","))),this.popup&&this.popup.focus(),window.addEventListener("message",this.messageHandler,!1)}async messageHandler(e){const{data:t,source:s,origin:n}=e;n===this.origin&&this.popup===s&&(t.type===this.eventHandlers.success.type&&(this.popup.close(),window.removeEventListener("message",this.messageHandler,!1),await this.eventHandlers.success.callback(t)),t.type===this.eventHandlers.error.type&&(this.popup.close(),window.removeEventListener("message",this.messageHandler,!1),await this.eventHandlers.error.callback(t)))}getPopup(){return this.popup}isClosed(){return!this.popup||this.popup.closed}focus(){this.isClosed()||this.popup.focus()}}const Ze=t=>{const s=(0,k.sprintf)(/* translators: %s expands to a link to open the Wincher login popup. */
(0,k.__)("It seems like something went wrong when retrieving your website's data. Please %s and try again.","wordpress-seo"),"{{reconnectToWincher/}}","Wincher");return(0,e.createElement)(g.Alert,{type:"error",className:t.className},(0,V.Z)({mixedString:s,components:{reconnectToWincher:(0,e.createElement)("a",{href:"#",onClick:e=>{e.preventDefault(),t.onReconnect()}},(0,k.sprintf)(/* translators: %s : Expands to "Wincher". */
(0,k.__)("reconnect to %s","wordpress-seo"),"Wincher"))}}))};Ze.propTypes={onReconnect:o().func.isRequired,className:o().string},Ze.defaultProps={className:""};const Xe=Ze,Qe=()=>(0,e.createElement)(g.Alert,{type:"error"},(0,k.__)("Before you can track your SEO performance make sure to set either the post’s title and save it as a draft or manually set the post’s slug.","wordpress-seo")),Je=window.yoast["chart.js"],et="label";function tt(e,t){"function"==typeof e?e(t):e&&(e.current=t)}function st(e,t){e.labels=t}function nt(e,t){let s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:et;const n=[];e.datasets=t.map((t=>{const r=e.datasets.find((e=>e[s]===t[s]));return r&&t.data&&!n.includes(r)?(n.push(r),Object.assign(r,t),r):{...t}}))}function rt(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:et;const s={labels:[],datasets:[]};return st(s,e.labels),nt(s,e.datasets,t),s}function at(t,s){const{height:n=150,width:r=300,redraw:a=!1,datasetIdKey:o,type:i,data:l,options:c,plugins:d=[],fallbackContent:u,updateMode:p,...m}=t,h=(0,e.useRef)(null),g=(0,e.useRef)(),y=()=>{h.current&&(g.current=new Je.Chart(h.current,{type:i,data:rt(l,o),options:c&&{...c},plugins:d}),tt(s,g.current))},f=()=>{tt(s,null),g.current&&(g.current.destroy(),g.current=null)};return(0,e.useEffect)((()=>{!a&&g.current&&c&&function(e,t){const s=e.options;s&&t&&Object.assign(s,t)}(g.current,c)}),[a,c]),(0,e.useEffect)((()=>{!a&&g.current&&st(g.current.config.data,l.labels)}),[a,l.labels]),(0,e.useEffect)((()=>{!a&&g.current&&l.datasets&&nt(g.current.config.data,l.datasets,o)}),[a,l.datasets]),(0,e.useEffect)((()=>{g.current&&(a?(f(),setTimeout(y)):g.current.update(p))}),[a,c,l.labels,l.datasets,p]),(0,e.useEffect)((()=>{g.current&&(f(),setTimeout(y))}),[i]),(0,e.useEffect)((()=>(y(),()=>f())),[]),e.createElement("canvas",Object.assign({ref:h,role:"img",height:n,width:r},m),u)}const ot=(0,e.forwardRef)(at);function it(t,s){return Je.Chart.register(s),(0,e.forwardRef)(((s,n)=>e.createElement(ot,Object.assign({},s,{ref:n,type:t}))))}const lt=it("line",Je.LineController),ct={datetime:"MMM D, YYYY, h:mm:ss a",millisecond:"h:mm:ss.SSS a",second:"h:mm:ss a",minute:"h:mm a",hour:"hA",day:"MMM D",week:"ll",month:"MMM YYYY",quarter:"[Q]Q - YYYY",year:"YYYY"};Je._adapters._date.override("function"==typeof j()?{_id:"moment",formats:function(){return ct},parse:function(e,t){return"string"==typeof e&&"string"==typeof t?e=j()(e,t):e instanceof j()||(e=j()(e)),e.isValid()?e.valueOf():null},format:function(e,t){return j()(e).format(t)},add:function(e,t,s){return j()(e).add(t,s).valueOf()},diff:function(e,t,s){return j()(e).diff(j()(t),s)},startOf:function(e,t,s){return e=j()(e),"isoWeek"===t?(s=Math.trunc(Math.min(Math.max(0,s),6)),e.isoWeekday(s).startOf("day").valueOf()):e.startOf(t).valueOf()},endOf:function(e,t){return j()(e).endOf(t).valueOf()}}:{}),Math.PI,Number.POSITIVE_INFINITY,Math.log10,Math.sign,"undefined"==typeof window||window.requestAnimationFrame,new Map,Object.create(null),Object.create(null),Number.EPSILON;const dt=["top","right","bottom","left"];function ut(e,t,s){const n={};s=s?"-"+s:"";for(let r=0;r<4;r++){const a=dt[r];n[a]=parseFloat(e[t+"-"+a+s])||0}return n.width=n.left+n.right,n.height=n.top+n.bottom,n}!function(){let e=!1;try{const t={get passive(){return e=!0,!1}};window.addEventListener("test",null,t),window.removeEventListener("test",null,t)}catch(e){}}(),Je.Chart.register(Je.CategoryScale,Je.LineController,Je.LineElement,Je.PointElement,Je.LinearScale,Je.TimeScale,Je.Legend,Je.Tooltip);const pt=["#ff983b","#ffa3f7","#3798ff","#ff3b3b","#acce81","#b51751","#3949ab","#26c6da","#ccb800","#de66ff","#4db6ac","#ffab91","#45f5f1","#77f210","#90a4ae","#ffd54f","#006b5e","#8ec7d2","#b1887c","#cc9300"];function mt({datasets:t,isChartShown:s,keyphrases:r}){if(!s)return null;const a=(0,n.useMemo)((()=>Object.fromEntries([...r].sort().map(((e,t)=>[e,pt[t%pt.length]])))),[r]),o=t.map((e=>{const t=a[e.label];return{...e,data:e.data.map((({datetime:e,value:t})=>({x:e,y:t}))),lineTension:0,pointRadius:1,pointHoverRadius:4,borderWidth:2,pointHitRadius:6,backgroundColor:t,borderColor:t}})).filter((e=>!1!==e.selected));return(0,e.createElement)(lt,{height:100,data:{datasets:o},options:{plugins:{legend:{display:!0,position:"bottom",labels:{color:"black",usePointStyle:!0,boxHeight:7,boxWidth:7},onClick:i.noop},tooltip:{enabled:!0,callbacks:{title:e=>j()(e[0].raw.x).utc().format("YYYY-MM-DD")},titleAlign:"center",mode:"xPoint",position:"nearest",usePointStyle:!0,boxHeight:7,boxWidth:7,boxPadding:2}},scales:{x:{bounds:"ticks",type:"time",time:{unit:"day",minUnit:"day"},grid:{display:!1},ticks:{autoSkipPadding:50,maxRotation:0,color:"black"}},y:{bounds:"ticks",offset:!0,reverse:!0,ticks:{precision:0,color:"black"},max:101}}}})}Je.Interaction.modes.xPoint=(e,t,s,n)=>{const r=function(e,t){if("native"in e)return e;const{canvas:s,currentDevicePixelRatio:n}=t,r=(m=s).ownerDocument.defaultView.getComputedStyle(m,null),a="border-box"===r.boxSizing,o=ut(r,"padding"),i=ut(r,"border","width"),{x:l,y:c,box:d}=function(e,t){const s=e.touches,n=s&&s.length?s[0]:e,{offsetX:r,offsetY:a}=n;let o,i,l=!1;if(((e,t,s)=>(e>0||t>0)&&(!s||!s.shadowRoot))(r,a,e.target))o=r,i=a;else{const e=t.getBoundingClientRect();o=n.clientX-e.left,i=n.clientY-e.top,l=!0}return{x:o,y:i,box:l}}(e,s),u=o.left+(d&&i.left),p=o.top+(d&&i.top);var m;let{width:h,height:g}=t;return a&&(h-=o.width+i.width,g-=o.height+i.height),{x:Math.round((l-u)/h*s.width/n),y:Math.round((c-p)/g*s.height/n)}}(t,e);let a=[];if(Je.Interaction.evaluateInteractionItems(e,"x",r,((e,t,s)=>{e.inXRange(r.x,n)&&a.push({element:e,datasetIndex:t,index:s})})),0===a.length)return a;const o=a.reduce(((e,t)=>Math.abs(r.x-e.element.x)<Math.abs(r.x-t.element.x)?e:t)).element.x;return a=a.filter((e=>e.element.x===o)),a.some((e=>Math.abs(e.element.y-r.y)<10))?a:[]},mt.propTypes={datasets:o().arrayOf(o().shape({label:o().string.isRequired,data:o().arrayOf(o().shape({datetime:o().string.isRequired,value:o().number.isRequired})).isRequired,selected:o().bool})).isRequired,isChartShown:o().bool.isRequired,keyphrases:o().array.isRequired};const ht=({response:t,onLogin:s})=>[401,403,404].includes(t.status)?(0,e.createElement)(Xe,{onReconnect:s}):(0,e.createElement)(Q,null);ht.propTypes={response:o().object.isRequired,onLogin:o().func.isRequired};const gt=({isSuccess:t,response:s,allKeyphrasesMissRanking:n,onLogin:r,keyphraseLimitReached:a,limit:o})=>a?(0,e.createElement)(X,{limit:o}):(0,i.isEmpty)(s)||t?n?(0,e.createElement)(ye,null):null:(0,e.createElement)(ht,{response:s,onLogin:r});gt.propTypes={isSuccess:o().bool.isRequired,allKeyphrasesMissRanking:o().bool.isRequired,response:o().object,onLogin:o().func.isRequired,keyphraseLimitReached:o().bool.isRequired,limit:o().number.isRequired},gt.defaultProps={response:{}};let yt=null;const ft=async e=>{if(yt&&!yt.isClosed())return void yt.focus();const{url:t}=await async function(){return await se({path:"yoast/v1/wincher/authorization-url",method:"GET"})}();yt=new Ge(t,{success:{type:"wincher:oauth:success",callback:t=>(async(e,t)=>{const{onAuthentication:s,setRequestSucceeded:n,setRequestFailed:r,keyphrases:a,addTrackedKeyphrase:o,setKeyphraseLimitReached:i}=e;await te((()=>async function(e){const{code:t,websiteId:s}=e;return await se({path:"yoast/v1/wincher/authenticate",method:"POST",data:{code:t,websiteId:s}})}(t)),(async e=>{s(!0,!0,t.websiteId.toString()),n(e);const l=(Array.isArray(a)?a:[a]).map((e=>e.toLowerCase()));await te((()=>ne(l)),(e=>{n(e),o(e.results)}),(e=>{400===e.status&&e.limit&&i(e.limit),r(e)}),201);const c=yt.getPopup();c&&c.close()}),(async e=>r(e)))})(e,t)},error:{type:"wincher:oauth:error",callback:()=>e.onAuthentication(!1,!1)}},{title:"Wincher_login",width:500,height:700}),yt.createPopup()},bt=t=>t.isLoggedIn?null:(0,e.createElement)("p",null,(0,e.createElement)(g.NewButton,{onClick:t.onLogin,variant:"primary"},(0,k.sprintf)(/* translators: %s expands to Wincher */
(0,k.__)("Connect with %s","wordpress-seo"),"Wincher")));bt.propTypes={isLoggedIn:o().bool.isRequired,onLogin:o().func.isRequired};const wt=c().div`
	p {
		margin: 1em 0;
	}
`,Et=c().div`
	${e=>e.isDisabled&&"\n\t\topacity: .5;\n\t\tpointer-events: none;\n\t"};
`,vt=c().div`
	font-weight: var(--yoast-font-weight-bold);
	color: var(--yoast-color-label);
	font-size: var(--yoast-font-size-default);
`,kt=c().div.attrs({className:"yoast-field-group"})`
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 14px;
`,_t=c().div`
	margin: 8px 0;
`,Tt=j().utc().startOf("day"),xt=[{name:(0,k.__)("Last day","wordpress-seo"),value:j()(Tt).subtract(1,"days").format(),defaultIndex:1},{name:(0,k.__)("Last week","wordpress-seo"),value:j()(Tt).subtract(1,"week").format(),defaultIndex:2},{name:(0,k.__)("Last month","wordpress-seo"),value:j()(Tt).subtract(1,"month").format(),defaultIndex:3},{name:(0,k.__)("Last year","wordpress-seo"),value:j()(Tt).subtract(1,"year").format(),defaultIndex:0}],St=t=>{const{onSelect:s,selected:n,options:r,isLoggedIn:a}=t;return a?r.length<1?null:(0,e.createElement)("select",{className:"components-select-control__input",id:"wincher-period-picker",value:(null==n?void 0:n.value)||r[0].value,onChange:s},r.map((t=>(0,e.createElement)("option",{key:t.name,value:t.value},t.name)))):null};St.propTypes={onSelect:o().func.isRequired,selected:o().object,options:o().array.isRequired,isLoggedIn:o().bool.isRequired};const Rt=t=>{const{trackedKeyphrases:s,isLoggedIn:r,keyphrases:a,shouldTrackAll:o,permalink:l,historyDaysLimit:c}=t;if(!l&&r)return(0,e.createElement)(Qe,null);if(0===a.length)return(0,e.createElement)(je,null);const d=j()(Tt).subtract(c,"days"),u=xt.filter((e=>j()(e.value).isSameOrAfter(d))),p=(0,i.orderBy)(u,(e=>e.defaultIndex),"desc")[0],[m,h]=(0,n.useState)(p),[g,y]=(0,n.useState)([]),f=g.length>0,b=(0,C.usePrevious)(s);(0,n.useEffect)((()=>{if(!(0,i.isEmpty)(s)&&(0,i.difference)(Object.keys(s),Object.keys(b||[])).length){const e=Object.values(s).map((e=>e.keyword));y(e)}}),[s,b]),(0,n.useEffect)((()=>{h(p)}),[null==p?void 0:p.name]);const w=(0,n.useCallback)((e=>{const t=xt.find((t=>t.value===e.target.value));t&&h(t)}),[h]),E=(0,n.useMemo)((()=>(0,i.isEmpty)(g)||(0,i.isEmpty)(s)?[]:Object.values(s).filter((e=>{var t;return!(null==e||null===(t=e.position)||void 0===t||!t.history)})).map((e=>{var t;return{label:e.keyword,data:e.position.history,selected:g.includes(e.keyword)&&!(0,i.isEmpty)(null===(t=e.position)||void 0===t?void 0:t.history)}}))),[g,s]);return(0,e.createElement)(Et,{isDisabled:!r},(0,e.createElement)("p",null,(0,k.__)("You can enable / disable tracking the SEO performance for each keyphrase below.","wordpress-seo")),r&&o&&(0,e.createElement)(Ve,null),(0,e.createElement)(kt,null,(0,e.createElement)(St,{selected:m,onSelect:w,options:u,isLoggedIn:r})),(0,e.createElement)(_t,null,(0,e.createElement)(mt,{isChartShown:f,datasets:E,keyphrases:a})),(0,e.createElement)(Ke,{startAt:null==m?void 0:m.value,selectedKeyphrases:g,onSelectKeyphrases:y,trackedKeyphrases:s}))};function Ct(t){const{isNewlyAuthenticated:s,isLoggedIn:r}=t,a=(0,n.useCallback)((()=>{ft(t)}),[ft,t]),o=ce(r);return(0,e.createElement)(wt,null,s&&(0,e.createElement)(ge,null),r&&(0,e.createElement)(he,{trackingInfo:o}),(0,e.createElement)(vt,null,(0,k.__)("SEO performance","wordpress-seo"),(0,e.createElement)(g.HelpIcon,{linkTo:wpseoAdminL10n["shortlinks.wincher.seo_performance"]
/* translators: Hidden accessibility text. */,linkText:(0,k.__)("Learn more about the SEO performance feature.","wordpress-seo")})),(0,e.createElement)(ze,null),(0,e.createElement)(bt,{isLoggedIn:r,onLogin:a}),(0,e.createElement)(gt,{...t,onLogin:a}),(0,e.createElement)(Rt,{...t,historyDaysLimit:(null==o?void 0:o.historyDays)||31}))}Rt.propTypes={trackedKeyphrases:o().object,keyphrases:o().array.isRequired,isLoggedIn:o().bool.isRequired,shouldTrackAll:o().bool.isRequired,permalink:o().string.isRequired,historyDaysLimit:o().number},Ct.propTypes={trackedKeyphrases:o().object,addTrackedKeyphrase:o().func.isRequired,isLoggedIn:o().bool,isNewlyAuthenticated:o().bool,keyphrases:o().array,response:o().object,shouldTrackAll:o().bool,permalink:o().string,historyDaysLimit:o().number},Ct.defaultProps={trackedKeyphrases:null,isLoggedIn:!1,isNewlyAuthenticated:!1,keyphrases:[],response:{},shouldTrackAll:!1,permalink:"",historyDaysLimit:0};const It=(0,C.compose)([(0,h.withSelect)((e=>{const{isWincherNewlyAuthenticated:t,getWincherKeyphraseLimitReached:s,getWincherLimit:n,getWincherHistoryDaysLimit:r,getWincherLoginStatus:a,getWincherRequestIsSuccess:o,getWincherRequestResponse:i,getWincherTrackableKeyphrases:l,getWincherTrackedKeyphrases:c,getWincherAllKeyphrasesMissRanking:d,getWincherPermalink:u,shouldWincherAutomaticallyTrackAll:p}=e("yoast-seo/editor");return{keyphrases:l(),trackedKeyphrases:c(),allKeyphrasesMissRanking:d(),isLoggedIn:a(),isNewlyAuthenticated:t(),isSuccess:o(),keyphraseLimitReached:s(),limit:n(),response:i(),shouldTrackAll:p(),permalink:u(),historyDaysLimit:r()}})),(0,h.withDispatch)((e=>{const{setWincherWebsiteId:t,setWincherRequestSucceeded:s,setWincherRequestFailed:n,setWincherTrackingForKeyphrase:r,setWincherSetKeyphraseLimitReached:a,setWincherLoginStatus:o}=e("yoast-seo/editor");return{setRequestSucceeded:e=>{s(e)},setRequestFailed:e=>{n(e)},addTrackedKeyphrase:e=>{r(e)},setKeyphraseLimitReached:e=>{a(e)},onAuthentication:(e,s,n)=>{t(n),o(e,s)}}}))])(Ct),Lt=c()(I)`
	width: 18px;
	height: 18px;
	margin: 3px;
`;function At(e){const{keyphrases:t,onNoKeyphraseSet:s,onOpen:n,location:r}=e;if(!t.length){let e=document.querySelector("#focus-keyword-input-metabox");return e||(e=document.querySelector("#focus-keyword-input-sidebar")),e.focus(),void s()}n(r)}function Pt(t){const{location:s,whichModalOpen:r,shouldCloseOnClickOutside:a}=t,o=(0,n.useCallback)((()=>{At(t)}),[At,t]),i=(0,k.__)("Track SEO performance","wordpress-seo"),l=((t=null)=>(0,e.useMemo)((()=>{const e={role:"img","aria-hidden":"true"};return null!==t&&(e.focusable=t?"true":"false"),e}),[t]))();return(0,e.createElement)(n.Fragment,null,r===s&&(0,e.createElement)($,{title:i,onRequestClose:t.onClose,icon:(0,e.createElement)(K,null),additionalClassName:"yoast-wincher-seo-performance-modal yoast-gutenberg-modal__no-padding",shouldCloseOnClickOutside:a},(0,e.createElement)(q,{className:"yoast-gutenberg-modal__content yoast-wincher-seo-performance-modal__content"},(0,e.createElement)(It,null))),"sidebar"===s&&(0,e.createElement)(Y,{id:`wincher-open-button-${s}`,title:i,SuffixHeroIcon:(0,e.createElement)(Lt,{className:"yst-text-slate-500",...l}),onClick:o}),"metabox"===s&&(0,e.createElement)("div",{className:"yst-root"},(0,e.createElement)(F,{id:`wincher-open-button-${s}`,onClick:o},(0,e.createElement)(F.Text,null,i),(0,e.createElement)(I,{className:"yst-h-5 yst-w-5 yst-text-slate-500",...l}))))}Pt.propTypes={location:o().string,whichModalOpen:o().oneOf(["none","metabox","sidebar","postpublish"]),shouldCloseOnClickOutside:o().bool,keyphrases:o().array.isRequired,onNoKeyphraseSet:o().func.isRequired,onOpen:o().func.isRequired,onClose:o().func.isRequired},Pt.defaultProps={location:"",whichModalOpen:"none",shouldCloseOnClickOutside:!0};const Ft=(0,C.compose)([(0,h.withSelect)((e=>{const{getWincherModalOpen:t,getWincherTrackableKeyphrases:s}=e("yoast-seo/editor");return{keyphrases:s(),whichModalOpen:t()}})),(0,h.withDispatch)((e=>{const{setWincherOpenModal:t,setWincherDismissModal:s,setWincherNoKeyphrase:n}=e("yoast-seo/editor");return{onOpen:e=>{t(e)},onClose:()=>{s()},onNoKeyphraseSet:()=>{n()}}}))])(Pt),Mt=window.yoast.externals.components;function qt(){return(0,C.createHigherOrderComponent)((function(e){return(0,C.pure)((function(t){const s=(0,n.useContext)(d.LocationContext);return(0,n.createElement)(e,{...t,location:s})}))}),"withLocation")}const Dt=(0,C.compose)([(0,h.withSelect)((e=>{const{isCornerstoneContent:t}=e("yoast-seo/editor");return{isCornerstone:t(),learnMoreUrl:wpseoAdminL10n["shortlinks.cornerstone_content_info"]}})),(0,h.withDispatch)((e=>{const{toggleCornerstoneContent:t}=e("yoast-seo/editor");return{onChange:t}})),qt()])(Mt.CollapsibleCornerstone),Ot=window.yoast.searchMetadataPreviews,Nt=c()(g.StyledSection)`
	&${g.StyledSectionBase} {
		padding: 0;

		& ${g.StyledHeading} {
			${(0,M.getDirectionalStyle)("padding-left","padding-right")}: 20px;
			margin-left: ${(0,M.getDirectionalStyle)("0","20px")};
		}
	}
`,$t=({children:t,title:s,icon:n,hasPaperStyle:r,shoppingData:a})=>(0,e.createElement)(Nt,{headingLevel:3,headingText:s,headingIcon:n,headingIconColor:"#555",hasPaperStyle:r,shoppingData:a},t);$t.propTypes={children:o().element,title:o().string,icon:o().string,hasPaperStyle:o().bool,shoppingData:o().object},$t.defaultProps={hasPaperStyle:!0,shoppingData:null};const Bt=$t,Ut=window.wp.sanitize,Wt="SNIPPET_EDITOR_UPDATE_REPLACEMENT_VARIABLE";function Kt(e,t,s="",n=!1){const r="string"==typeof t?(0,M.decodeHTML)(t):t;return{type:Wt,name:e,value:r,label:s,hidden:n}}function Ht(e){return e.charAt(0).toUpperCase()+e.slice(1)}const{stripHTMLTags:Yt}=M.strings,zt=["slug","content","contentImage","snippetPreviewImageURL"];function jt(e,t="_"){return e.replace(/\s/g,t)}const Vt=(0,i.memoize)(((e,t)=>0===e?i.noop:(0,i.debounce)((s=>t(s,e)),500))),Gt=e.forwardRef((function(t,s){return e.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:2,stroke:"currentColor","aria-hidden":"true",ref:s},t),e.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M8 11V7a4 4 0 118 0m-4 8v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2z"}))})),Zt=({link:t,text:s})=>(0,e.createElement)(m.Root,null,(0,e.createElement)("p",null,s),(0,e.createElement)(m.Button,{href:t,as:"a",className:"yst-gap-2 yst-mb-5 yst-mt-2",variant:"upsell",target:"_blank",rel:"noopener"},(0,e.createElement)(Gt,{className:"yst-w-4 yst-h-4 yst--ml-1 yst-shrink-0"}),(0,k.sprintf)(/* translators: %1$s expands to Yoast WooCommerce SEO. */
(0,k.__)("Unlock with %1$s","wordpress-seo"),"Yoast WooCommerce SEO")));Zt.propTypes={link:o().string.isRequired,text:o().string.isRequired};const Xt=Zt,Qt=function(e,t){let s=0;return t.shortenedBaseUrl&&"string"==typeof t.shortenedBaseUrl&&(s=t.shortenedBaseUrl.length),e.url=e.url.replace(/\s+/g,"-"),"-"===e.url[e.url.length-1]&&(e.url=e.url.slice(0,-1)),"-"===e.url[s]&&(e.url=e.url.slice(0,s)+e.url.slice(s+1)),function(e){const t=(0,i.get)(window,["YoastSEO","app","pluggable"],!1);if(!t||!(0,i.get)(window,["YoastSEO","app","pluggable","loaded"],!1))return function(e){const t=(0,i.get)(window,["YoastSEO","wp","replaceVarsPlugin","replaceVariables"],i.identity);return{url:e.url,title:Yt(t(e.title)),description:Yt(t(e.description)),filteredSEOTitle:e.filteredSEOTitle?Yt(t(e.filteredSEOTitle)):""}}(e);const s=t._applyModifications.bind(t);return{url:e.url,title:Yt(s("data_page_title",e.title)),description:Yt(s("data_meta_desc",e.description)),filteredSEOTitle:e.filteredSEOTitle?Yt(s("data_page_title",e.filteredSEOTitle)):""}}(e)},Jt=(0,C.compose)([(0,h.withSelect)((function(e){const{getBaseUrlFromSettings:t,getDateFromSettings:s,getFocusKeyphrase:n,getRecommendedReplaceVars:r,getReplaceVars:a,getShoppingData:o,getSiteIconUrlFromSettings:i,getSnippetEditorData:l,getSnippetEditorMode:c,getSnippetEditorPreviewImageUrl:d,getSnippetEditorWordsToHighlight:u,isCornerstoneContent:p,getIsTerm:m,getContentLocale:h,getSiteName:g}=e("yoast-seo/editor"),y=a();return y.forEach((e=>{""!==e.value||["title","excerpt","excerpt_only"].includes(e.name)||(e.value="%%"+e.name+"%%")})),{baseUrl:t(),data:l(),date:s(),faviconSrc:i(),keyword:n(),mobileImageSrc:d(),mode:c(),recommendedReplacementVariables:r(),replacementVariables:y,shoppingData:o(),wordsToHighlight:u(),isCornerstone:p(),isTaxonomy:m(),locale:h(),siteName:g()}})),(0,h.withDispatch)((function(e,t,{select:s}){const{updateData:n,switchMode:r,updateAnalysisData:a,findCustomFields:o}=e("yoast-seo/editor"),i=e("core/editor"),l=s("yoast-seo/editor").getPostId();return{onChange:(e,t)=>{switch(e){case"mode":r(t);break;case"slug":n({slug:t}),i&&i.editPost({slug:t});break;default:n({[e]:t})}},onChangeAnalysisData:a,onReplacementVariableSearchChange:Vt(l,o)}}))])((t=>{const s=(0,i.get)(window,"wpseoScriptData.metabox.woocommerceUpsellGooglePreviewLink",""),n=(0,i.get)(window,"wpseoScriptData.woocommerceUpsell",""),r=(0,k.__)("Want an enhanced Google preview of how your WooCommerce products look in the search results?","wordpress-seo");return(0,e.createElement)(d.LocationConsumer,null,(a=>(0,e.createElement)(Bt,{icon:"eye",hasPaperStyle:t.hasPaperStyle},(0,e.createElement)(e.Fragment,null,n&&(0,e.createElement)(Xt,{link:s,text:r}),(0,e.createElement)(Ot.SnippetEditor,{...t,descriptionPlaceholder:(0,k.__)("Please provide a meta description by editing the snippet below.","wordpress-seo"),mapEditorDataToPreview:Qt,showCloseButton:!1,idSuffix:a})))))})),es=(0,h.withSelect)((e=>{const{getWarningMessage:t}=e("yoast-seo/editor");return{message:t()}}))(g.Warning),ts=window.yoast.featureFlag,ss=c()(g.Collapsible)`
	h2 > button {
		padding-left: 24px;
		padding-top: 16px;

		&:hover {
			background-color: #f0f0f0;
		}
	}

	div[class^="collapsible_content"] {
		padding: 24px 0;
		margin: 0 24px;
		border-top: 1px solid rgba(0,0,0,0.2);
	}

`,ns=t=>(0,e.createElement)(ss,{hasPadding:!0,hasSeparator:!0,...t}),rs=()=>{const t=(0,h.useSelect)((e=>e("yoast-seo/editor").getEstimatedReadingTime()),[]),s=(0,n.useMemo)((()=>(0,i.get)(window,"wpseoAdminL10n.shortlinks-insights-estimated_reading_time","")),[]);return(0,e.createElement)(g.InsightsCard,{amount:t,unit:(0,k._n)("minute","minutes",t,"wordpress-seo"),title:(0,k.__)("Reading time","wordpress-seo"),linkTo:s
/* translators: Hidden accessibility text. */,linkText:(0,k.__)("Learn more about reading time","wordpress-seo")})},as=(0,M.makeOutboundLink)();function os(t,s,n){const r=function(e){switch(e){case f.DIFFICULTY.FAIRLY_DIFFICULT:case f.DIFFICULTY.DIFFICULT:case f.DIFFICULTY.VERY_DIFFICULT:return(0,k.__)("Try to make shorter sentences, using less difficult words to improve readability","wordpress-seo");case f.DIFFICULTY.NO_DATA:return(0,k.__)("Continue writing to get insight into the readability of your text!","wordpress-seo");default:return(0,k.__)("Good job!","wordpress-seo")}}(s);return(0,e.createElement)("span",null,function(e,t){return-1===e?(0,k.sprintf)((0,k.__)("Your text should be slightly longer to calculate your Flesch reading ease score.","wordpress-seo")):(0,k.sprintf)(
/* Translators: %1$s expands to the numeric Flesch reading ease score,
  		%2$s expands to the easiness of reading (e.g. 'easy' or 'very difficult').
  	 */
(0,k.__)("The copy scores %1$s in the test, which is considered %2$s to read.","wordpress-seo"),e,function(e){switch(e){case f.DIFFICULTY.NO_DATA:return(0,k.__)("no data","wordpress-seo");case f.DIFFICULTY.VERY_EASY:return(0,k.__)("very easy","wordpress-seo");case f.DIFFICULTY.EASY:return(0,k.__)("easy","wordpress-seo");case f.DIFFICULTY.FAIRLY_EASY:return(0,k.__)("fairly easy","wordpress-seo");case f.DIFFICULTY.OKAY:return(0,k.__)("okay","wordpress-seo");case f.DIFFICULTY.FAIRLY_DIFFICULT:return(0,k.__)("fairly difficult","wordpress-seo");case f.DIFFICULTY.DIFFICULT:return(0,k.__)("difficult","wordpress-seo");case f.DIFFICULTY.VERY_DIFFICULT:return(0,k.__)("very difficult","wordpress-seo")}}(t))}(t,s)," ",s>=f.DIFFICULTY.FAIRLY_DIFFICULT?(0,e.createElement)(as,{href:n},r+"."):r)}const is=()=>{let t=(0,h.useSelect)((e=>e("yoast-seo/editor").getFleschReadingEaseScore()),[]);const s=(0,n.useMemo)((()=>(0,i.get)(window,"wpseoAdminL10n.shortlinks-insights-flesch_reading_ease","")),[]),r=(0,h.useSelect)((e=>e("yoast-seo/editor").getFleschReadingEaseDifficulty()),[t]),a=(0,n.useMemo)((()=>{const e=(0,i.get)(window,"wpseoAdminL10n.shortlinks-insights-flesch_reading_ease_article","");return os(t,r,e)}),[t,r]);return-1===t&&(t="?"),(0,e.createElement)(g.InsightsCard,{amount:t,unit:(0,k.__)("out of 100","wordpress-seo"),title:(0,k.__)("Flesch reading ease","wordpress-seo"),linkTo:s
/* translators: Hidden accessibility text. */,linkText:(0,k.__)("Learn more about Flesch reading ease","wordpress-seo"),description:a})};let ls,cs,ds,us;const ps=/<(\/)?(\w+)\s*(\/)?>/g;function ms(e,t,s,n,r){return{element:e,tokenStart:t,tokenLength:s,prevOffset:n,leadingTextStart:r,children:[]}}function hs(){const e=ls.length-cs;0!==e&&ds.push(ls.substring(cs,cs+e))}function gs(e){const{element:t,tokenStart:s,tokenLength:r,prevOffset:a,children:o}=e,i=us[us.length-1],l=ls.substring(i.prevOffset,s);l&&i.children.push(l),i.children.push((0,n.cloneElement)(t,null,...o)),i.prevOffset=a||s+r}function ys(e){const t=function(){const e=ps.exec(ls);if(null===e)return["no-more-tokens"];const t=e.index,[s,n,r,a]=e,o=s.length;return a?["self-closed",r,t,o]:n?["closer",r,t,o]:["opener",r,t,o]}(),[s,r,a,o]=t,i=us.length,l=a>cs?cs:null;if(!e[r])return hs(),!1;switch(s){case"no-more-tokens":if(0!==i){const{leadingTextStart:e,tokenStart:t}=us.pop();ds.push(ls.substring(e,e+t))}return hs(),!1;case"self-closed":return 0===i?(null!==l&&ds.push(ls.substring(l,a)),ds.push(e[r]),cs=a+o,!0):(gs(ms(e[r],a,o)),cs=a+o,!0);case"opener":return us.push(ms(e[r],a,o,a+o,l)),cs=a+o,!0;case"closer":if(1===i)return function(e){const{element:t,leadingTextStart:s,prevOffset:r,tokenStart:a,children:o}=us.pop(),i=e?ls.substring(r,e):ls.substring(r);i&&o.push(i),null!==s&&ds.push(ls.substring(s,a)),ds.push((0,n.cloneElement)(t,null,...o))}(a),cs=a+o,!0;const t=us.pop(),s=ls.substring(t.prevOffset,a);t.children.push(s),t.prevOffset=a+o;const c=ms(t.element,t.tokenStart,t.tokenLength,a+o);return c.children=t.children,gs(c),cs=a+o,!0;default:return hs(),!1}}const fs=(e,t)=>{if(ls=e,cs=0,ds=[],us=[],ps.lastIndex=0,!(e=>{const t="object"==typeof e,s=t&&Object.values(e);return t&&s.length&&s.every((e=>(0,n.isValidElement)(e)))})(t))throw new TypeError("The conversionMap provided is not valid. It must be an object with values that are WPElements");do{}while(ys(t));return(0,n.createElement)(n.Fragment,null,...ds)},bs=({data:t,itemScreenReaderText:s,className:r,...a})=>{const o=(0,n.useMemo)((()=>{var e,s;return null!==(e=null===(s=(0,i.maxBy)(t,"number"))||void 0===s?void 0:s.number)&&void 0!==e?e:0}),[t]);return(0,e.createElement)("ul",{className:A()("yoast-data-model",r),...a},t.map((({name:t,number:n})=>(0,e.createElement)("li",{key:`${t}_dataItem`,style:{"--yoast-width":n/o*100+"%"}},t,(0,e.createElement)("span",null,n),s&&(0,e.createElement)("span",{className:"screen-reader-text"},(0,k.sprintf)(s,n))))))};bs.propTypes={data:o().arrayOf(o().shape({name:o().string.isRequired,number:o().number.isRequired})),itemScreenReaderText:o().string,className:o().string},bs.defaultProps={data:[],itemScreenReaderText:"",className:""};const ws=bs,Es=window.wp.url,vs=(0,M.makeOutboundLink)(),ks=({location:t})=>{const s=(0,h.useSelect)((e=>{var t,s;return null===(t=null===(s=e("yoast-seo-premium/editor"))||void 0===s?void 0:s.getPreference("isProminentWordsAvailable",!1))||void 0===t||t}),[]),r=(0,h.useSelect)((e=>e("yoast-seo/editor").getPreference("shouldUpsell",!1)),[]),a=(0,n.useMemo)((()=>(0,i.get)(window,`wpseoAdminL10n.shortlinks-insights-upsell-${t}-prominent_words`,"")),[t]),o=(0,n.useMemo)((()=>{const t=(0,i.get)(window,"wpseoAdminL10n.shortlinks-insights-keyword_research_link","");return fs((0,k.sprintf)(
// translators: %1$s and %2$s are replaced by opening and closing <a> tags.
(0,k.__)("Read our %1$sultimate guide to keyword research%2$s to learn more about keyword research and keyword strategy.","wordpress-seo"),"<a>","</a>"),{a:(0,e.createElement)(vs,{href:t})})}),[]),l=(0,n.useMemo)((()=>fs((0,k.sprintf)(
// translators: %1$s expands to a starting `b` tag, %1$s expands to a closing `b` tag and %3$s expands to `Yoast SEO Premium`.
(0,k.__)("With %1$s%3$s%2$s, this section will show you which words occur most often in your text. By checking these prominent words against your intended keyword(s), you'll know how to edit your text to be more focused.","wordpress-seo"),"<b>","</b>","Yoast SEO Premium"),{b:(0,e.createElement)("b",null)})),[]),c=(0,h.useSelect)((e=>{var t,s;return null!==(t=null===(s=e("yoast-seo-premium/editor"))||void 0===s?void 0:s.getProminentWords())&&void 0!==t?t:[]}),[]),u=(0,n.useMemo)((()=>{const e=(0,k.sprintf)(
// translators: %1$s expands to Yoast SEO Premium.
(0,k.__)("Get %s to enjoy the benefits of prominent words","wordpress-seo"),"Yoast SEO Premium").split(/\s+/);return e.map(((t,s)=>({name:t,number:e.length-s})))}),[]),p=(0,n.useMemo)((()=>r?u:c.map((({word:e,occurrence:t})=>({name:e,number:t})))),[c,u]);if(!s)return null;const{locationContext:m}=(0,d.useRootContext)();return(0,e.createElement)("div",{className:"yoast-prominent-words"},(0,e.createElement)("div",{className:"yoast-field-group__title"},(0,e.createElement)("b",null,(0,k.__)("Prominent words","wordpress-seo"))),!r&&(0,e.createElement)("p",null,0===p.length?(0,k.__)("Once you add a bit more copy, we'll give you a list of words that occur the most in the content. These give an indication of what your content focuses on.","wordpress-seo"):(0,k.__)("The following words occur the most in the content. These give an indication of what your content focuses on. If the words differ a lot from your topic, you might want to rewrite your content accordingly.","wordpress-seo")),r&&(0,e.createElement)("p",null,l),r&&(0,e.createElement)(vs,{href:(0,Es.addQueryArgs)(a,{context:m}),"data-action":"load-nfd-ctb","data-ctb-id":"f6a84663-465f-4cb5-8ba5-f7a6d72224b2",className:"yoast-button yoast-button-upsell"},(0,k.sprintf)(
// translators: %s expands to `Premium` (part of add-on name).
(0,k.__)("Unlock with %s","wordpress-seo"),"Premium"),(0,e.createElement)("span",{"aria-hidden":"true",className:"yoast-button-upsell__caret"})),(0,e.createElement)("p",null,o),(0,e.createElement)(ws,{data:p,itemScreenReaderText:/* translators: Hidden accessibility text; %d expands to the number of occurrences. */
(0,k.__)("%d occurrences","wordpress-seo"),"aria-label":(0,k.__)("Prominent words","wordpress-seo"),className:r?"yoast-data-model--upsell":null}))};ks.propTypes={location:o().string.isRequired};const _s=ks,Ts=()=>{const t=(0,h.useSelect)((e=>e("yoast-seo/editor").getTextLength()),[]),s=(0,n.useMemo)((()=>(0,i.get)(window,"wpseoAdminL10n.shortlinks-insights-word_count","")),[]);let r=(0,k._n)("word","words",t.count,"wordpress-seo"),a=(0,k.__)("Word count","wordpress-seo"),o=(0,k.__)("Learn more about word count","wordpress-seo");return"character"===t.unit&&(r=(0,k._n)("character","characters",t.count,"wordpress-seo"),a=(0,k.__)("Character count","wordpress-seo"),
/* translators: Hidden accessibility text. */
o=(0,k.__)("Learn more about character count","wordpress-seo")),(0,e.createElement)(g.InsightsCard,{amount:t.count,unit:r,title:a,linkTo:s,linkText:o})},xs=(0,M.makeOutboundLink)(),Ss=({location:t})=>{const s=(0,n.useMemo)((()=>(0,i.get)(window,`wpseoAdminL10n.shortlinks-insights-upsell-${t}-text_formality`,"")),[t]),r=(0,n.useMemo)((()=>fs((0,k.sprintf)(
// Translators: %1$s expands to a starting `b` tag, %2$s expands to a closing `b` tag and %3$s expands to `Yoast SEO Premium`.
(0,k.__)("%1$s%3$s%2$s will help you assess the formality level of your text.","wordpress-seo"),"<b>","</b>","Yoast SEO Premium"),{b:(0,e.createElement)("b",null)})),[]);return(0,e.createElement)(n.Fragment,null,(0,e.createElement)("div",null,(0,e.createElement)("p",null,r),(0,e.createElement)(xs,{href:s,className:"yoast-button yoast-button-upsell"},(0,k.sprintf)(
// Translators: %s expands to `Premium` (part of add-on name).
(0,k.__)("Unlock with %s","wordpress-seo"),"Premium"),(0,e.createElement)("span",{"aria-hidden":"true",className:"yoast-button-upsell__caret"}))))};Ss.propTypes={location:o().string.isRequired};const Rs=Ss;function Cs(){return(0,i.get)(window,"wpseoScriptData.metabox",{intl:{},isRtl:!1})}const Is=({location:t,name:s})=>{const n=(0,h.useSelect)((e=>e("yoast-seo/editor").isFormalitySupported()),[]),a=Cs().isPremium,o=a?(0,i.get)(window,"wpseoAdminL10n.shortlinks-insights-text_formality_info_premium",""):(0,i.get)(window,"wpseoAdminL10n.shortlinks-insights-text_formality_info_free",""),l=(0,k.__)("Read more about text formality.","wordpress-seo");return n?(0,e.createElement)("div",{className:"yoast-text-formality"},(0,e.createElement)("div",{className:"yoast-field-group__title"},(0,e.createElement)("b",null,(0,k.__)("Text formality","wordpress-seo")),(0,e.createElement)(g.HelpIcon,{linkTo:o,linkText:l})),a?(0,e.createElement)(r.Slot,{name:s}):(0,e.createElement)(Rs,{location:t})):null};Is.propTypes={location:o().string.isRequired,name:o().string.isRequired};const Ls=Is,As=({location:t})=>{const s=(0,h.useSelect)((e=>e("yoast-seo/editor").isFleschReadingEaseAvailable()),[]);return(0,e.createElement)(ns,{title:(0,k.__)("Insights","wordpress-seo"),id:`yoast-insights-collapsible-${t}`,className:"yoast-insights"},(0,e.createElement)(_s,{location:t}),(0,e.createElement)("div",null,s&&(0,e.createElement)("div",{className:"yoast-insights-row"},(0,e.createElement)(is,null)),(0,e.createElement)("div",{className:"yoast-insights-row yoast-insights-row--columns"},(0,e.createElement)(rs,null),(0,e.createElement)(Ts,null)),(0,ts.isFeatureEnabled)("TEXT_FORMALITY")&&(0,e.createElement)(Ls,{location:t,name:"YoastTextFormalityMetabox"})))};As.propTypes={location:o().string},As.defaultProps={location:"metabox"};const Ps=As,Fs=e.forwardRef((function(t,s){return e.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true",ref:s},t),e.createElement("path",{fillRule:"evenodd",d:"M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z",clipRule:"evenodd"}))})),Ms=()=>[(0,k.sprintf)(/* translators: %1$s expands to a strong opening tag, %2$s expands to a strong closing tag. */
(0,k.__)("%1$sAI%2$s: Better SEO titles and meta descriptions, faster.","wordpress-seo"),"<strong>","</strong>"),(0,k.sprintf)(/* translators: %1$s expands to a strong opening tag, %2$s expands to a strong closing tag. */
(0,k.__)("%1$sMultiple keywords%2$s: Rank higher for more searches.","wordpress-seo"),"<strong>","</strong>"),(0,k.sprintf)(/* translators: %1$s expands to a strong opening tag, %2$s expands to a strong closing tag. */
(0,k.__)("%1$sSuper fast%2$s internal linking suggestions.","wordpress-seo"),"<strong>","</strong>"),(0,k.sprintf)(/* translators: %1$s expands to a strong opening tag, %2$s expands to a strong closing tag. */
(0,k.__)("%1$sNo more broken links%2$s: Automatic redirect manager.","wordpress-seo"),"<strong>","</strong>"),(0,k.sprintf)(/* translators: %1$s expands to a strong opening tag, %2$s expands to a strong closing tag. */
(0,k.__)("%1$sAppealing social previews%2$s people actually want to click on.","wordpress-seo"),"<strong>","</strong>"),(0,k.sprintf)(/* translators: %1$s expands to a strong opening tag, %2$s expands to a strong closing tag. */
(0,k.__)("%1$s24/7 support%2$s: Also on evenings and weekends.","wordpress-seo"),"<strong>","</strong>")],qs=c().div`
  padding: 25px 32px 32px;
  color: #303030;
`,Ds=c().ul`
  margin: 0;
  padding: 0;

  li {
    list-style-image: var(--yoast-svg-icon-check);
    margin: 0.5rem 0 0 1.5rem;
    line-height: 1.4em;

    &::marker {
      font-size: 1.5rem;
    }
  }
`,Os=c().span`
  display: block;
  margin-top: 4px;
`,Ns=c().h2`
  margin-top: 0;
  margin-bottom: 0.25rem;
  color: #303030;
  font-size: 0.8125rem;
  font-weight: 600;
`,$s=c().p`
  display: block;
  margin: 0.25rem 0 1rem 0 !important;
  max-width: 420px;
`,Bs=c().hr`
  margin-top: 1.5rem;
  margin-bottom: 1rem;
  border-top: 0;
  border-bottom: 1px solid #E2E8F0;
`,Us=c().div`
  text-align: center;
`,Ws=c().a`
  width: 100%;
`,Ks=(0,M.makeOutboundLink)(Ws);class Hs extends n.Component{constructor(e){super(e),this.state={defaultPrice:"99"}}createBenefitsList(t){return t.length>0&&(0,e.createElement)(Ds,{role:"list"},t.map(((t,s)=>(0,e.createElement)("li",{key:`upsell-benefit-${s}`},(0,n.createInterpolateElement)(t,{strong:(0,e.createElement)("strong",null)})))))}render(){const t=(0,h.select)("yoast-seo/editor").isPromotionActive("black-friday-2023-promotion"),{defaultPrice:s}=this.state,r=t?"69.30":null,a=r||s;return(0,e.createElement)(n.Fragment,null,t&&(0,e.createElement)("div",{className:"yst-flex yst-justify-between yst-items-center yst-text-lg yst-content-between yst-bg-black yst-text-amber-300 yst-h-9 yst-border-amber-300 yst-border-y yst-border-x-0 yst-border-solid yst-px-6"},(0,e.createElement)("div",null,(0,k.__)("BLACK FRIDAY","wordpress-seo")),(0,e.createElement)("div",null,(0,k.__)("30% OFF","wordpress-seo"))),(0,e.createElement)(qs,null,(0,e.createElement)(Ns,null,this.props.title),(0,e.createElement)($s,null,this.props.description),(0,e.createElement)(Us,null,(0,e.createElement)(Ks,{...this.props.upsellButton},this.props.upsellButtonText,this.props.upsellButtonHasCaret&&(0,e.createElement)("span",{"aria-hidden":"true",className:"yoast-button-upsell__caret"})),(0,e.createElement)("div",{className:"yst-text-slate-600 yst-my-4"},r&&(0,e.createElement)(n.Fragment,null,(0,e.createElement)("span",{className:"yst-text-slate-500 yst-line-through"},s)," "),(0,e.createElement)("span",{className:"yst-text-slate-900 yst-text-2xl yst-font-bold"},a)," ",(0,k.__)("$ USD / € EUR / £ GBP per year (ex. VAT)","wordpress-seo")),(0,e.createElement)(Os,{id:this.props.upsellButton["aria-describedby"]},this.props.upsellButtonLabel)),(0,e.createElement)(Bs,null),(0,e.createElement)(Ns,null,this.props.benefitsTitle),this.createBenefitsList(this.props.benefits)))}}Hs.propTypes={title:o().node,benefits:o().array,benefitsTitle:o().node,description:o().node,upsellButton:o().object,upsellButtonText:o().string.isRequired,upsellButtonLabel:o().string,upsellButtonHasCaret:o().bool},Hs.defaultProps={title:null,description:null,benefits:[],benefitsTitle:null,upsellButton:{href:"",className:"button button-primary"},upsellButtonLabel:"",upsellButtonHasCaret:!0};const Ys=Hs,zs=()=>{const[t,,,s,n]=(0,m.useToggleState)(!1),{locationContext:r}=(0,d.useRootContext)(),a=(0,m.useSvgAria)(),o=r.includes("sidebar"),i=r.includes("metabox"),l=wpseoAdminL10n[o?"shortlinks.upsell.sidebar.internal_linking_suggestions":"shortlinks.upsell.metabox.internal_linking_suggestions"];return(0,e.createElement)(e.Fragment,null,t&&(0,e.createElement)($,{title:(0,k.__)("Get internal linking suggestions","wordpress-seo"),onRequestClose:n,additionalClassName:"",id:"yoast-internal-linking-suggestions-upsell",className:`${O} yoast-gutenberg-modal__box yoast-gutenberg-modal__no-padding`,shouldCloseOnClickOutside:!0},(0,e.createElement)(D,null,(0,e.createElement)(Ys,{title:(0,k.__)("Rank higher by connecting your content","wordpress-seo"),description:(0,k.sprintf)(/* translators: %s expands to Yoast SEO Premium. */
(0,k.__)("%s automatically suggests to what content you can link with easy drag-and-drop functionality, which is good for your SEO!","wordpress-seo"),"Yoast SEO Premium"),benefitsTitle:/* translators: %s expands to 'Yoast SEO Premium'. */
(0,k.sprintf)("%s also gives you:","Yoast SEO Premium"),benefits:Ms(),upsellButtonText:(0,k.sprintf)(/* translators: %s expands to 'Yoast SEO Premium'. */
(0,k.__)("Unlock with %s","wordpress-seo"),"Yoast SEO Premium"),upsellButton:{href:(0,Es.addQueryArgs)(l,{context:r}),className:"yoast-button-upsell",rel:null,"data-ctb-id":"f6a84663-465f-4cb5-8ba5-f7a6d72224b2","data-action":"load-nfd-ctb"},upsellButtonLabel:(0,k.__)("1 year free support and updates included!","wordpress-seo")}))),o&&(0,e.createElement)(Y,{id:"yoast-internal-linking-suggestions-sidebar-modal-open-button",title:(0,k.__)("Internal linking suggestions","wordpress-seo"),onClick:s},(0,e.createElement)("div",{className:"yst-root"},(0,e.createElement)(m.Badge,{size:"small",variant:"upsell"},(0,e.createElement)(Fs,{className:"yst-w-2.5 yst-h-2.5 yst-shrink-0",...a})))),i&&(0,e.createElement)("div",{className:"yst-root"},(0,e.createElement)(F,{id:"yoast-internal-linking-suggestions-metabox-modal-open-button",onClick:s},(0,e.createElement)(F.Text,null,(0,k.__)("Internal linking suggestions","wordpress-seo")),(0,e.createElement)(m.Badge,{size:"small",variant:"upsell"},(0,e.createElement)(Fs,{className:"yst-w-2.5 yst-h-2.5 yst-mr-1 yst-shrink-0",...a}),(0,e.createElement)("span",null,"Premium")))))},js=({children:t})=>(0,e.createElement)("div",null,t);js.propTypes={renderPriority:o().number.isRequired,children:o().node.isRequired};const Vs=js,Gs=({noIndex:t,onNoIndexChange:s,editorContext:r,isPrivateBlog:a})=>{const o=(e=>{const t=(0,k.__)("No","wordpress-seo"),s=(0,k.__)("Yes","wordpress-seo"),n=e.noIndex?t:s;return window.wpseoScriptData.isPost?[{name:(0,k.sprintf)(/* translators: the first %s translates to "yes" or "no", the second %s translates to the content type label in plural form */
(0,k.__)("%s (current default for %s)","wordpress-seo"),n,e.postTypeNamePlural),value:"0"},{name:t,value:"1"},{name:s,value:"2"}]:[{name:(0,k.sprintf)(/* translators: the first %s translates to "yes" or "no", the second %s translates to the content type label in plural form */
(0,k.__)("%s (current default for %s)","wordpress-seo"),n,e.postTypeNamePlural),value:"default"},{name:s,value:"index"},{name:t,value:"noindex"}]})(r);return(0,e.createElement)(d.LocationConsumer,null,(r=>(0,e.createElement)(n.Fragment,null,a&&(0,e.createElement)(g.Alert,{type:"warning"},(0,k.__)("Even though you can set the meta robots setting here, the entire site is set to noindex in the sitewide privacy settings, so these settings won't have an effect.","wordpress-seo")),(0,e.createElement)(g.Select,{label:(0,k.__)("Allow search engines to show this content in search results?","wordpress-seo"),onChange:s,id:(0,M.join)(["yoast-meta-robots-noindex",r]),options:o,selected:t,linkTo:wpseoAdminL10n["shortlinks.advanced.allow_search_engines"]
/* translators: Hidden accessibility text. */,linkText:(0,k.__)("Learn more about the no-index setting on our help page.","wordpress-seo")}))))};Gs.propTypes={noIndex:o().string.isRequired,onNoIndexChange:o().func.isRequired,editorContext:o().object.isRequired,isPrivateBlog:o().bool},Gs.defaultProps={isPrivateBlog:!1};const Zs=({noFollow:t,onNoFollowChange:s})=>(0,e.createElement)(d.LocationConsumer,null,(n=>{const r=(0,M.join)(["yoast-meta-robots-nofollow",n]);return(0,e.createElement)(g.RadioButtonGroup,{id:r,options:[{value:"0",label:"Yes"},{value:"1",label:"No"}],label:(0,k.__)("Should search engines follow links on this content?","wordpress-seo"),groupName:r,onChange:s,selected:t,linkTo:wpseoAdminL10n["shortlinks.advanced.follow_links"]
/* translators: Hidden accessibility text. */,linkText:(0,k.__)("Learn more about the no-follow setting on our help page.","wordpress-seo")})}));Zs.propTypes={noFollow:o().string.isRequired,onNoFollowChange:o().func.isRequired};const Xs=({advanced:t,onAdvancedChange:s})=>(0,e.createElement)(d.LocationConsumer,null,(n=>{const r=(0,M.join)(["yoast-meta-robots-advanced",n]),a=`${r}-input`;return(0,e.createElement)(g.MultiSelect,{label:(0,k.__)("Meta robots advanced","wordpress-seo"),onChange:s,id:r,inputId:a,options:[{name:(0,k.__)("No Image Index","wordpress-seo"),value:"noimageindex"},{name:(0,k.__)("No Archive","wordpress-seo"),value:"noarchive"},{name:(0,k.__)("No Snippet","wordpress-seo"),value:"nosnippet"}],selected:t,linkTo:wpseoAdminL10n["shortlinks.advanced.meta_robots"]
/* translators: Hidden accessibility text. */,linkText:(0,k.__)("Learn more about advanced meta robots settings on our help page.","wordpress-seo")})}));Xs.propTypes={advanced:o().array.isRequired,onAdvancedChange:o().func.isRequired};const Qs=({breadcrumbsTitle:t,onBreadcrumbsTitleChange:s})=>(0,e.createElement)(d.LocationConsumer,null,(n=>(0,e.createElement)(g.TextInput,{label:(0,k.__)("Breadcrumbs Title","wordpress-seo"),id:(0,M.join)(["yoast-breadcrumbs-title",n]),onChange:s,value:t,linkTo:wpseoAdminL10n["shortlinks.advanced.breadcrumbs_title"]
/* translators: Hidden accessibility text. */,linkText:(0,k.__)("Learn more about the breadcrumbs title setting on our help page.","wordpress-seo")})));Qs.propTypes={breadcrumbsTitle:o().string.isRequired,onBreadcrumbsTitleChange:o().func.isRequired};const Js=({canonical:t,onCanonicalChange:s})=>(0,e.createElement)(d.LocationConsumer,null,(n=>(0,e.createElement)(g.TextInput,{label:(0,k.__)("Canonical URL","wordpress-seo"),id:(0,M.join)(["yoast-canonical",n]),onChange:s,value:t,linkTo:"https://yoa.st/canonical-url"
/* translators: Hidden accessibility text. */,linkText:(0,k.__)("Learn more about canonical URLs on our help page.","wordpress-seo")})));Js.propTypes={canonical:o().string.isRequired,onCanonicalChange:o().func.isRequired};const en=t=>{const{noIndex:s,noFollow:r,advanced:a,breadcrumbsTitle:o,canonical:i,onNoIndexChange:l,onNoFollowChange:c,onAdvancedChange:d,onBreadcrumbsTitleChange:u,onCanonicalChange:p,onLoad:m,isLoading:h,editorContext:g,isBreadcrumbsDisabled:y,isPrivateBlog:f}=t;(0,n.useEffect)((()=>{setTimeout((()=>{h&&m()}))}));const b={noIndex:s,onNoIndexChange:l,editorContext:g,isPrivateBlog:f},w={noFollow:r,onNoFollowChange:c},E={advanced:a,onAdvancedChange:d},v={breadcrumbsTitle:o,onBreadcrumbsTitleChange:u},k={canonical:i,onCanonicalChange:p};return h?null:(0,e.createElement)(n.Fragment,null,(0,e.createElement)(Gs,{...b}),g.isPost&&(0,e.createElement)(Zs,{...w}),g.isPost&&(0,e.createElement)(Xs,{...E}),!y&&(0,e.createElement)(Qs,{...v}),(0,e.createElement)(Js,{...k}))};en.propTypes={noIndex:o().string.isRequired,canonical:o().string.isRequired,onNoIndexChange:o().func.isRequired,onCanonicalChange:o().func.isRequired,onLoad:o().func.isRequired,isLoading:o().bool.isRequired,editorContext:o().object.isRequired,isBreadcrumbsDisabled:o().bool.isRequired,isPrivateBlog:o().bool,advanced:o().array,onAdvancedChange:o().func,noFollow:o().string,onNoFollowChange:o().func,breadcrumbsTitle:o().string,onBreadcrumbsTitleChange:o().func},en.defaultProps={advanced:[],onAdvancedChange:()=>{},noFollow:"",onNoFollowChange:()=>{},breadcrumbsTitle:"",onBreadcrumbsTitleChange:()=>{},isPrivateBlog:!1};const tn=en,sn=(0,C.compose)([(0,h.withSelect)((e=>{const{getNoIndex:t,getNoFollow:s,getAdvanced:n,getBreadcrumbsTitle:r,getCanonical:a,getIsLoading:o,getEditorContext:i,getPreferences:l}=e("yoast-seo/editor"),{isBreadcrumbsDisabled:c,isPrivateBlog:d}=l();return{noIndex:t(),noFollow:s(),advanced:n(),breadcrumbsTitle:r(),canonical:a(),isLoading:o(),editorContext:i(),isBreadcrumbsDisabled:c,isPrivateBlog:d}})),(0,h.withDispatch)((e=>{const{setNoIndex:t,setNoFollow:s,setAdvanced:n,setBreadcrumbsTitle:r,setCanonical:a,loadAdvancedSettingsData:o}=e("yoast-seo/editor");return{onNoIndexChange:t,onNoFollowChange:s,onAdvancedChange:n,onBreadcrumbsTitleChange:r,onCanonicalChange:a,onLoad:o}}))])(tn),nn=c().p`
	color: #606770;
	flex-shrink: 0;
	font-size: 12px;
	line-height: 16px;
	overflow: hidden;
	padding: 0;
	text-overflow: ellipsis;
	text-transform: uppercase;
	white-space: nowrap;
	margin: 0;
	position: ${e=>"landscape"===e.mode?"relative":"static"};
`,rn=t=>{const{siteUrl:s}=t;return(0,e.createElement)(e.Fragment,null,(0,e.createElement)("span",{className:"screen-reader-text"},s),(0,e.createElement)(nn,{"aria-hidden":"true"},(0,e.createElement)("span",null,s)))};rn.propTypes={siteUrl:o().string.isRequired};const an=rn,on=window.yoast.socialMetadataForms,ln=c().img`
	&& {
		max-width: ${e=>e.width}px;
		height: ${e=>e.height}px;
		position: absolute;
		top: 50%;
		left: 50%;
		transform: translate(-50%, -50%);
		max-width: none;
	}
`,cn=c().img`
	&&{
		height: 100%;
		position: absolute;
		width: 100%;
		object-fit: cover;
	}
`,dn=c().div`
	padding-bottom: ${e=>e.aspectRatio}%;
`,un=t=>{const{imageProps:s,width:n,height:r,imageMode:a}=t;return"landscape"===a?(0,e.createElement)(dn,{aspectRatio:s.aspectRatio},(0,e.createElement)(cn,{src:s.src,alt:s.alt})):(0,e.createElement)(ln,{src:s.src,alt:s.alt,width:n,height:r,imageProperties:s})};function pn(e,t,s){return"landscape"===s?{widthRatio:t.width/e.landscapeWidth,heightRatio:t.height/e.landscapeHeight}:"portrait"===s?{widthRatio:t.width/e.portraitWidth,heightRatio:t.height/e.portraitHeight}:{widthRatio:t.width/e.squareWidth,heightRatio:t.height/e.squareHeight}}function mn(e,t){return t.widthRatio<=t.heightRatio?{width:Math.round(e.width/t.widthRatio),height:Math.round(e.height/t.widthRatio)}:{width:Math.round(e.width/t.heightRatio),height:Math.round(e.height/t.heightRatio)}}async function hn(e,t,s=!1){const n=await function(e){return new Promise(((t,s)=>{const n=new Image;n.onload=()=>{t({width:n.width,height:n.height})},n.onerror=s,n.src=e}))}(e);let r=s?"landscape":"square";"Facebook"===t&&(r=(0,on.determineFacebookImageMode)(n));const a=function(e){return"Twitter"===e?on.TWITTER_IMAGE_SIZES:on.FACEBOOK_IMAGE_SIZES}(t),o=function(e,t,s){return"square"===s&&t.width===t.height?{width:e.squareWidth,height:e.squareHeight}:mn(t,pn(e,t,s))}(a,n,r);return{mode:r,height:o.height,width:o.width}}async function gn(e,t,s=!1){try{return{imageProperties:await hn(e,t,s),status:"loaded"}}catch(e){return{imageProperties:null,status:"errored"}}}un.propTypes={imageProps:o().shape({src:o().string.isRequired,alt:o().string.isRequired,aspectRatio:o().number.isRequired}).isRequired,width:o().number.isRequired,height:o().number.isRequired,imageMode:o().string},un.defaultProps={imageMode:"landscape"};const yn=c().div`
	position: relative;
	${e=>"landscape"===e.mode?`max-width: ${e.dimensions.width}`:`min-width: ${e.dimensions.width}; height: ${e.dimensions.height}`};
	overflow: hidden;
	background-color: ${y.colors.$color_white};
`,fn=c().div`
	box-sizing: border-box;
	max-width: ${on.FACEBOOK_IMAGE_SIZES.landscapeWidth}px;
	height: ${on.FACEBOOK_IMAGE_SIZES.landscapeHeight}px;
	background-color: ${y.colors.$color_grey};
	border-style: dashed;
	border-width: 1px;
	// We're not using standard colors to increase contrast for accessibility.
	color: #006DAC;
	// We're not using standard colors to increase contrast for accessibility.
	background-color: #f1f1f1;
	display: flex;
	justify-content: center;
	align-items: center;
	text-decoration: underline;
	font-size: 14px;
	cursor: pointer;
`;class bn extends e.Component{constructor(e){super(e),this.state={imageProperties:null,status:"loading"},this.socialMedium="Facebook",this.handleFacebookImage=this.handleFacebookImage.bind(this),this.setState=this.setState.bind(this)}async handleFacebookImage(){try{const e=await gn(this.props.src,this.socialMedium);this.setState(e),this.props.onImageLoaded(e.imageProperties.mode||"landscape")}catch(e){this.setState(e),this.props.onImageLoaded("landscape")}}componentDidUpdate(e){e.src!==this.props.src&&this.handleFacebookImage()}componentDidMount(){this.handleFacebookImage()}retrieveContainerDimensions(e){switch(e){case"square":return{height:on.FACEBOOK_IMAGE_SIZES.squareHeight+"px",width:on.FACEBOOK_IMAGE_SIZES.squareWidth+"px"};case"portrait":return{height:on.FACEBOOK_IMAGE_SIZES.portraitHeight+"px",width:on.FACEBOOK_IMAGE_SIZES.portraitWidth+"px"};case"landscape":return{height:on.FACEBOOK_IMAGE_SIZES.landscapeHeight+"px",width:on.FACEBOOK_IMAGE_SIZES.landscapeWidth+"px"}}}render(){const{imageProperties:t,status:s}=this.state;if("loading"===s||""===this.props.src||"errored"===s)return(0,e.createElement)(fn,{onClick:this.props.onImageClick,onMouseEnter:this.props.onMouseEnter,onMouseLeave:this.props.onMouseLeave},(0,k.__)("Select image","wordpress-seo"));const n=this.retrieveContainerDimensions(t.mode);return(0,e.createElement)(yn,{mode:t.mode,dimensions:n,onMouseEnter:this.props.onMouseEnter,onMouseLeave:this.props.onMouseLeave,onClick:this.props.onImageClick},(0,e.createElement)(un,{imageProps:{src:this.props.src,alt:this.props.alt,aspectRatio:on.FACEBOOK_IMAGE_SIZES.aspectRatio},width:t.width,height:t.height,imageMode:t.mode}))}}bn.propTypes={src:o().string,alt:o().string,onImageLoaded:o().func,onImageClick:o().func,onMouseEnter:o().func,onMouseLeave:o().func},bn.defaultProps={src:"",alt:"",onImageLoaded:i.noop,onImageClick:i.noop,onMouseEnter:i.noop,onMouseLeave:i.noop};const wn=bn,En=c().span`
	line-height: ${20}px;
	min-height : ${20}px;
	color: #1d2129;
	font-weight: 600;
	overflow: hidden;
	font-size: 16px;
	margin: 3px 0 0;
	letter-spacing: normal;
	white-space: normal;
	flex-shrink: 0;
	cursor: pointer;
	display: -webkit-box;
	-webkit-line-clamp: ${e=>e.lineCount};
	-webkit-box-orient: vertical;
	overflow: hidden;
`,vn=c().p`
	line-height: ${16}px;
	min-height : ${16}px;
	color: #606770;
	font-size: 14px;
	padding: 0;
	text-overflow: ellipsis;
	margin: 3px 0 0 0;
	display: -webkit-box;
	cursor: pointer;
	-webkit-line-clamp: ${e=>e.lineCount};
	-webkit-box-orient: vertical;
	overflow: hidden;

	@media all and ( max-width: ${e=>e.maxWidth} ) {
		display: none;
	}
`,kn=e=>{switch(e){case"landscape":return"527px";case"square":case"portrait":return"369px";default:return"476px"}},Tn=c().div`
	box-sizing: border-box;
	display: flex;
	flex-direction: ${e=>"landscape"===e.mode?"column":"row"};
	background-color: #f2f3f5;
	max-width: 527px;
`,xn=c().div`
	box-sizing: border-box;
	background-color: #f2f3f5;
	margin: 0;
	padding: 10px 12px;
	position: relative;
	border-bottom: ${e=>"landscape"===e.mode?"":"1px solid #dddfe2"};
	border-top: ${e=>"landscape"===e.mode?"":"1px solid #dddfe2"};
	border-right: ${e=>"landscape"===e.mode?"":"1px solid #dddfe2"};
	border: ${e=>"landscape"===e.mode?"1px solid #dddfe2":""};
	display: flex;
	flex-direction: column;
	flex-grow: 1;
	justify-content: ${e=>"landscape"===e.mode?"flex-start":"center"};
	font-size: 12px;
	overflow: hidden;
`;class Sn extends e.Component{constructor(e){super(e),this.state={imageMode:null,maxLineCount:0,descriptionLineCount:0},this.facebookTitleRef=t().createRef(),this.onImageLoaded=this.onImageLoaded.bind(this),this.onImageEnter=this.props.onMouseHover.bind(this,"image"),this.onTitleEnter=this.props.onMouseHover.bind(this,"title"),this.onDescriptionEnter=this.props.onMouseHover.bind(this,"description"),this.onLeave=this.props.onMouseHover.bind(this,""),this.onSelectTitle=this.props.onSelect.bind(this,"title"),this.onSelectDescription=this.props.onSelect.bind(this,"description")}onImageLoaded(e){this.setState({imageMode:e})}getTitleLineCount(){return this.facebookTitleRef.current.offsetHeight/20}maybeSetMaxLineCount(){const{imageMode:e,maxLineCount:t}=this.state,s="landscape"===e?2:5;s!==t&&this.setState({maxLineCount:s})}maybeSetDescriptionLineCount(){const{descriptionLineCount:e,maxLineCount:t,imageMode:s}=this.state,n=this.getTitleLineCount();let r=t-n;"portrait"===s&&(r=5===n?0:4),r!==e&&this.setState({descriptionLineCount:r})}componentDidUpdate(){this.maybeSetMaxLineCount(),this.maybeSetDescriptionLineCount()}render(){const{imageMode:t,maxLineCount:s,descriptionLineCount:n}=this.state;return(0,e.createElement)(Tn,{id:"facebookPreview",mode:t},(0,e.createElement)(wn,{src:this.props.imageUrl||this.props.imageFallbackUrl,alt:this.props.alt,onImageLoaded:this.onImageLoaded,onImageClick:this.props.onImageClick,onMouseEnter:this.onImageEnter,onMouseLeave:this.onLeave}),(0,e.createElement)(xn,{mode:t},(0,e.createElement)(an,{siteUrl:this.props.siteUrl,mode:t}),(0,e.createElement)(En,{ref:this.facebookTitleRef,onMouseEnter:this.onTitleEnter,onMouseLeave:this.onLeave,onClick:this.onSelectTitle,lineCount:s},this.props.title),n>0&&(0,e.createElement)(vn,{maxWidth:kn(t),onMouseEnter:this.onDescriptionEnter,onMouseLeave:this.onLeave,onClick:this.onSelectDescription,lineCount:n},this.props.description)))}}Sn.propTypes={siteUrl:o().string.isRequired,title:o().string.isRequired,description:o().string,imageUrl:o().string,imageFallbackUrl:o().string,alt:o().string,onSelect:o().func,onImageClick:o().func,onMouseHover:o().func},Sn.defaultProps={description:"",alt:"",imageUrl:"",imageFallbackUrl:"",onSelect:()=>{},onImageClick:()=>{},onMouseHover:()=>{}};const Rn=Sn,Cn=c().div`
	text-transform: lowercase;
	color: rgb(83, 100, 113);
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
	margin: 0;
	fill: currentcolor;
	display: flex;
	flex-direction: row;
	align-items: flex-end;
`,In=t=>(0,e.createElement)(Cn,null,(0,e.createElement)("span",null,t.siteUrl));In.propTypes={siteUrl:o().string.isRequired};const Ln=In,An=(e,t=!0)=>e?`\n\t\t\tmax-width: ${on.TWITTER_IMAGE_SIZES.landscapeWidth}px;\n\t\t\t${t?"border-bottom: 1px solid #E1E8ED;":""}\n\t\t\tborder-radius: 14px 14px 0 0;\n\t\t\t`:`\n\t\twidth: ${on.TWITTER_IMAGE_SIZES.squareWidth}px;\n\t\t${t?"border-right: 1px solid #E1E8ED;":""}\n\t\tborder-radius: 14px 0 0 14px;\n\t\t`,Pn=c().div`
	position: relative;
	box-sizing: content-box;
	overflow: hidden;
	background-color: #e1e8ed;
	flex-shrink: 0;
	${e=>An(e.isLarge)}
`,Fn=c().div`
	display: flex;
	justify-content: center;
	align-items: center;
	box-sizing: border-box;
	max-width: 100%;
	margin: 0;
	padding: 1em;
	text-align: center;
	font-size: 1rem;
	${e=>An(e.isLarge,!1)}
`,Mn=c()(Fn)`
	${e=>e.isLarge&&`height: ${on.TWITTER_IMAGE_SIZES.landscapeHeight}px;`}
	border-top-left-radius: 14px;
	${e=>e.isLarge?"border-top-right-radius":"border-bottom-left-radius"}: 14px;
	border-style: dashed;
	border-width: 1px;
	// We're not using standard colors to increase contrast for accessibility.
	color: #006DAC;
	// We're not using standard colors to increase contrast for accessibility.
	background-color: #f1f1f1;
	text-decoration: underline;
	font-size: 14px;
	cursor: pointer;
`;class qn extends t().Component{constructor(e){super(e),this.state={status:"loading"},this.socialMedium="Twitter",this.handleTwitterImage=this.handleTwitterImage.bind(this),this.setState=this.setState.bind(this)}async handleTwitterImage(){if(null===this.props.src)return;const e=await gn(this.props.src,this.socialMedium,this.props.isLarge);this.setState(e)}componentDidUpdate(e){e.src!==this.props.src&&this.handleTwitterImage()}componentDidMount(){this.handleTwitterImage()}render(){const{status:t,imageProperties:s}=this.state;return"loading"===t||""===this.props.src||"errored"===t?(0,e.createElement)(Mn,{isLarge:this.props.isLarge,onClick:this.props.onImageClick,onMouseEnter:this.props.onMouseEnter,onMouseLeave:this.props.onMouseLeave},(0,k.__)("Select image","wordpress-seo")):(0,e.createElement)(Pn,{isLarge:this.props.isLarge,onClick:this.props.onImageClick,onMouseEnter:this.props.onMouseEnter,onMouseLeave:this.props.onMouseLeave},(0,e.createElement)(un,{imageProps:{src:this.props.src,alt:this.props.alt,aspectRatio:on.TWITTER_IMAGE_SIZES.aspectRatio},width:s.width,height:s.height,imageMode:s.mode}))}}qn.propTypes={isLarge:o().bool.isRequired,src:o().string,alt:o().string,onImageClick:o().func,onMouseEnter:o().func,onMouseLeave:o().func},qn.defaultProps={src:"",alt:"",onMouseEnter:i.noop,onImageClick:i.noop,onMouseLeave:i.noop};const Dn=c().div`
	display: flex;
	flex-direction: column;
	padding: 12px;
	justify-content: center;
	margin: 0;
	box-sizing: border-box;
	flex: auto;
	min-width: 0px;
	gap:2px;
	> * {
		line-height:20px;
		min-height:20px;
		font-size:15px;
    }
`,On=t=>(0,e.createElement)(Dn,null,t.children);On.propTypes={children:o().array.isRequired};const Nn=On,$n=c().p`
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
	margin: 0;
	color: rgb(15, 20, 25);
	cursor: pointer;
`,Bn=c().p`
	max-height: 55px;
	overflow: hidden;
	text-overflow: ellipsis;
	margin: 0;
	color: rgb(83, 100, 113);
	display: -webkit-box;
	cursor: pointer;
	-webkit-line-clamp: 2;
	-webkit-box-orient: vertical;

	@media all and ( max-width: ${on.TWITTER_IMAGE_SIZES.landscapeWidth}px ) {
		display: none;
	}
`,Un=c().div`
	font-family: system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Ubuntu, "Helvetica Neue", sans-serif;
	font-size: 15px;
	font-weight: 400;
	line-height: 20px;
	max-width: 507px;
	border: 1px solid #E1E8ED;
	box-sizing: border-box;
	border-radius: 14px;
	color: #292F33;
	background: #FFFFFF;
	text-overflow: ellipsis;
	display: flex;

	&:hover {
		background: #f5f8fa;
		border: 1px solid rgba(136,153,166,.5);
	}
`,Wn=c()(Un)`
	flex-direction: column;
	max-height: 370px;
`,Kn=c()(Un)`
	flex-direction: row;
	height: 125px;
`;class Hn extends e.Component{constructor(e){super(e),this.onImageEnter=this.props.onMouseHover.bind(this,"image"),this.onTitleEnter=this.props.onMouseHover.bind(this,"title"),this.onDescriptionEnter=this.props.onMouseHover.bind(this,"description"),this.onLeave=this.props.onMouseHover.bind(this,""),this.onSelectTitle=this.props.onSelect.bind(this,"title"),this.onSelectDescription=this.props.onSelect.bind(this,"description")}render(){const{isLarge:t,imageUrl:s,imageFallbackUrl:n,alt:r,title:a,description:o,siteUrl:i}=this.props,l=t?Wn:Kn;return(0,e.createElement)(l,{id:"twitterPreview"},(0,e.createElement)(qn,{src:s||n,alt:r,isLarge:t,onImageClick:this.props.onImageClick,onMouseEnter:this.onImageEnter,onMouseLeave:this.onLeave}),(0,e.createElement)(Nn,null,(0,e.createElement)(Ln,{siteUrl:i}),(0,e.createElement)($n,{onMouseEnter:this.onTitleEnter,onMouseLeave:this.onLeave,onClick:this.onSelectTitle},a),(0,e.createElement)(Bn,{onMouseEnter:this.onDescriptionEnter,onMouseLeave:this.onLeave,onClick:this.onSelectDescription},o)))}}Hn.propTypes={siteUrl:o().string.isRequired,title:o().string.isRequired,description:o().string,isLarge:o().bool,imageUrl:o().string,imageFallbackUrl:o().string,alt:o().string,onSelect:o().func,onImageClick:o().func,onMouseHover:o().func},Hn.defaultProps={description:"",alt:"",imageUrl:"",imageFallbackUrl:"",onSelect:()=>{},onImageClick:()=>{},onMouseHover:()=>{},isLarge:!0};const Yn=Hn,zn=window.yoast.replacementVariableEditor;class jn extends e.Component{constructor(e){super(e),this.state={activeField:"",hoveredField:""},this.SocialPreview="Social"===e.socialMediumName?Rn:Yn,this.setHoveredField=this.setHoveredField.bind(this),this.setActiveField=this.setActiveField.bind(this),this.setEditorRef=this.setEditorRef.bind(this),this.setEditorFocus=this.setEditorFocus.bind(this)}setHoveredField(e){e!==this.state.hoveredField&&this.setState({hoveredField:e})}setActiveField(e){e!==this.state.activeField&&this.setState({activeField:e},(()=>this.setEditorFocus(e)))}setEditorFocus(e){switch(e){case"title":this.titleEditorRef.focus();break;case"description":this.descriptionEditorRef.focus()}}setEditorRef(e,t){switch(e){case"title":this.titleEditorRef=t;break;case"description":this.descriptionEditorRef=t}}render(){const{onDescriptionChange:s,onTitleChange:n,onSelectImageClick:r,onRemoveImageClick:a,socialMediumName:o,imageWarnings:i,siteUrl:l,description:c,descriptionInputPlaceholder:d,descriptionPreviewFallback:u,imageUrl:p,imageFallbackUrl:m,alt:h,title:y,titleInputPlaceholder:f,titlePreviewFallback:b,replacementVariables:w,recommendedReplacementVariables:E,applyReplacementVariables:v,onReplacementVariableSearchChange:k,isPremium:_,isLarge:T,socialPreviewLabel:x,idSuffix:S,activeMetaTabId:R}=this.props,C=v({title:y||b,description:c||u});return(0,e.createElement)(t().Fragment,null,x&&(0,e.createElement)(g.SimulatedLabel,null,x),(0,e.createElement)(this.SocialPreview,{onMouseHover:this.setHoveredField,onSelect:this.setActiveField,onImageClick:r,siteUrl:l,title:C.title,description:C.description,imageUrl:p,imageFallbackUrl:m,alt:h,isLarge:T,activeMetaTabId:R}),(0,e.createElement)(on.SocialMetadataPreviewForm,{onDescriptionChange:s,socialMediumName:o,title:y,titleInputPlaceholder:f,onRemoveImageClick:a,imageSelected:!!p,imageUrl:p,onTitleChange:n,onSelectImageClick:r,description:c,descriptionInputPlaceholder:d,imageWarnings:i,replacementVariables:w,recommendedReplacementVariables:E,onReplacementVariableSearchChange:k,onMouseHover:this.setHoveredField,hoveredField:this.state.hoveredField,onSelect:this.setActiveField,activeField:this.state.activeField,isPremium:_,setEditorRef:this.setEditorRef,idSuffix:S}))}}jn.propTypes={title:o().string.isRequired,onTitleChange:o().func.isRequired,description:o().string.isRequired,onDescriptionChange:o().func.isRequired,imageUrl:o().string.isRequired,imageFallbackUrl:o().string.isRequired,onSelectImageClick:o().func.isRequired,onRemoveImageClick:o().func.isRequired,socialMediumName:o().string.isRequired,alt:o().string,isPremium:o().bool,imageWarnings:o().array,isLarge:o().bool,siteUrl:o().string,descriptionInputPlaceholder:o().string,titleInputPlaceholder:o().string,descriptionPreviewFallback:o().string,titlePreviewFallback:o().string,replacementVariables:zn.replacementVariablesShape,recommendedReplacementVariables:zn.recommendedReplacementVariablesShape,applyReplacementVariables:o().func,onReplacementVariableSearchChange:o().func,socialPreviewLabel:o().string,idSuffix:o().string,activeMetaTabId:o().string},jn.defaultProps={imageWarnings:[],recommendedReplacementVariables:[],replacementVariables:[],isPremium:!1,isLarge:!0,siteUrl:"",descriptionInputPlaceholder:"",titleInputPlaceholder:"",descriptionPreviewFallback:"",titlePreviewFallback:"",alt:"",applyReplacementVariables:e=>e,onReplacementVariableSearchChange:null,socialPreviewLabel:"",idSuffix:"",activeMetaTabId:""};const Vn={},Gn=(e,t,{log:s=console.warn}={})=>{Vn[e]||(Vn[e]=!0,s(t))},Zn=(e,t=i.noop)=>{const s={};for(const n in e)Object.hasOwn(e,n)&&Object.defineProperty(s,n,{set:s=>{e[n]=s,t("set",n,s)},get:()=>(t("get",n),e[n])});return s};Zn({squareWidth:125,squareHeight:125,landscapeWidth:506,landscapeHeight:265,aspectRatio:50.2},((e,t)=>Gn(`@yoast/social-metadata-previews/TWITTER_IMAGE_SIZES/${e}/${t}`,`[@yoast/social-metadata-previews] "TWITTER_IMAGE_SIZES.${t}" is deprecated and will be removed in the future, please use this from @yoast/social-metadata-forms instead.`))),Zn({squareWidth:158,squareHeight:158,landscapeWidth:527,landscapeHeight:273,portraitWidth:158,portraitHeight:237,aspectRatio:52.2,largeThreshold:{width:446,height:233}},((e,t)=>Gn(`@yoast/social-metadata-previews/FACEBOOK_IMAGE_SIZES/${e}/${t}`,`[@yoast/social-metadata-previews] "FACEBOOK_IMAGE_SIZES.${t}" is deprecated and will be removed in the future, please use this from @yoast/social-metadata-forms instead.`)));const Xn=c().div`
	max-width: calc(527px + 1.5rem);
`,Qn=t=>{const s="X"===t.socialMediumName?(0,k.__)("X share preview","wordpress-seo"):(0,k.__)("Social share preview","wordpress-seo"),{locationContext:n}=(0,m.useRootContext)();return(0,e.createElement)(m.Root,null,(0,e.createElement)(Xn,null,(0,e.createElement)(m.FeatureUpsell,{shouldUpsell:!0,variant:"card",cardLink:(0,Es.addQueryArgs)(wpseoAdminL10n["shortlinks.upsell.social_preview."+t.socialMediumName.toLowerCase()],{context:n}),cardText:(0,k.sprintf)(/* translators: %1$s expands to Yoast SEO Premium. */
(0,k.__)("Unlock with %1$s","wordpress-seo"),"Yoast SEO Premium"),"data-action":"load-nfd-ctb","data-ctb-id":"f6a84663-465f-4cb5-8ba5-f7a6d72224b2"},(0,e.createElement)("div",{className:"yst-grayscale yst-opacity-50"},(0,e.createElement)(m.Label,null,s),(0,e.createElement)(Rn,{title:"",description:"",siteUrl:"",imageUrl:"",imageFallbackUrl:"",alt:"",onSelect:i.noop,onImageClick:i.noop,onMouseHover:i.noop})))))};Qn.propTypes={socialMediumName:o().oneOf(["Social","Twitter","X"]).isRequired};const Jn=Qn;class er extends n.Component{constructor(e){super(e),this.state={activeField:"",hoveredField:""},this.setHoveredField=this.setHoveredField.bind(this),this.setActiveField=this.setActiveField.bind(this),this.setEditorRef=this.setEditorRef.bind(this),this.setEditorFocus=this.setEditorFocus.bind(this)}setHoveredField(e){e!==this.state.hoveredField&&this.setState({hoveredField:e})}setActiveField(e){e!==this.state.activeField&&this.setState({activeField:e},(()=>this.setEditorFocus(e)))}setEditorFocus(e){switch(e){case"title":this.titleEditorRef.focus();break;case"description":this.descriptionEditorRef.focus()}}setEditorRef(e,t){switch(e){case"title":this.titleEditorRef=t;break;case"description":this.descriptionEditorRef=t}}render(){const{onDescriptionChange:t,onTitleChange:s,onSelectImageClick:r,onRemoveImageClick:a,socialMediumName:o,imageWarnings:i,description:l,descriptionInputPlaceholder:c,imageUrl:d,alt:u,title:p,titleInputPlaceholder:m,replacementVariables:h,recommendedReplacementVariables:g,onReplacementVariableSearchChange:y,isPremium:f,location:b}=this.props;return(0,e.createElement)(n.Fragment,null,(0,e.createElement)(Jn,{socialMediumName:o}),(0,e.createElement)(on.SocialMetadataPreviewForm,{onDescriptionChange:t,socialMediumName:o,title:p,titleInputPlaceholder:m,onRemoveImageClick:a,imageSelected:!!d,imageUrl:d,imageAltText:u,onTitleChange:s,onSelectImageClick:r,description:l,descriptionInputPlaceholder:c,imageWarnings:i,replacementVariables:h,recommendedReplacementVariables:g,onReplacementVariableSearchChange:y,onMouseHover:this.setHoveredField,hoveredField:this.state.hoveredField,onSelect:this.setActiveField,activeField:this.state.activeField,isPremium:f,setEditorRef:this.setEditorRef,idSuffix:b}))}}er.propTypes={title:o().string.isRequired,onTitleChange:o().func.isRequired,description:o().string.isRequired,onDescriptionChange:o().func.isRequired,imageUrl:o().string.isRequired,onSelectImageClick:o().func.isRequired,onRemoveImageClick:o().func.isRequired,socialMediumName:o().string.isRequired,isPremium:o().bool,imageWarnings:o().array,descriptionInputPlaceholder:o().string,titleInputPlaceholder:o().string,replacementVariables:zn.replacementVariablesShape,recommendedReplacementVariables:zn.recommendedReplacementVariablesShape,onReplacementVariableSearchChange:o().func,location:o().string,alt:o().string},er.defaultProps={imageWarnings:[],recommendedReplacementVariables:[],replacementVariables:[],isPremium:!1,descriptionInputPlaceholder:"",titleInputPlaceholder:"",onReplacementVariableSearchChange:null,location:"",alt:""};const tr=er,sr=t=>{const[s,a]=(0,n.useState)(""),o=(0,n.useCallback)((e=>{a(e.detail.metaTabId)}),[a]);(0,n.useEffect)((()=>(setTimeout(t.onLoad),window.addEventListener("YoastSEO:metaTabChange",o),()=>{window.removeEventListener("YoastSEO:metaTabChange",o)})),[]);const i=(0,n.useMemo)((()=>({...t,activeMetaTabId:s})),[t,s]);return t.isPremium?(0,e.createElement)(r.Slot,{name:`YoastFacebookPremium${t.location.charAt(0).toUpperCase()+t.location.slice(1)}`,fillProps:i}):(0,e.createElement)(tr,{...i})};sr.propTypes={isPremium:o().bool.isRequired,onLoad:o().func.isRequired,location:o().string.isRequired};const nr=sr;function rr(e){(function(e){const t=window.wp.media();return t.on("select",(()=>{const s=t.state().get("selection").first();var n;e({type:(n=s.attributes).subtype,width:n.width,height:n.height,url:n.url,id:n.id,sizes:n.sizes,alt:n.alt||n.title||n.name})})),t})(e).open()}const ar=()=>{rr((e=>(0,h.dispatch)("yoast-seo/editor").setFacebookPreviewImage((e=>{const{width:t,height:s}=e,n=(0,on.determineFacebookImageMode)({width:t,height:s}),r=on.FACEBOOK_IMAGE_SIZES[n+"Width"],a=on.FACEBOOK_IMAGE_SIZES[n+"Height"],o=Object.values(e.sizes).find((e=>e.width>=r&&e.height>=a));return{url:o?o.url:e.url,id:e.id,warnings:(0,M.validateFacebookImage)(e),alt:e.alt||""}})(e))))},or=(0,C.compose)([(0,h.withSelect)((e=>{const{getFacebookDescription:t,getDescription:s,getFacebookTitle:n,getSeoTitle:r,getFacebookImageUrl:a,getImageFallback:o,getFacebookWarnings:i,getRecommendedReplaceVars:l,getReplaceVars:c,getSiteUrl:d,getSeoTitleTemplate:u,getSeoTitleTemplateNoFallback:p,getSocialTitleTemplate:m,getSeoDescriptionTemplate:h,getSocialDescriptionTemplate:g,getReplacedExcerpt:y,getFacebookAltText:f}=e("yoast-seo/editor");return{imageUrl:a(),imageFallbackUrl:o(),recommendedReplacementVariables:l(),replacementVariables:c(),description:t(),descriptionPreviewFallback:g()||s()||h()||y()||"",title:n(),titlePreviewFallback:m()||r()||p()||u()||"",imageWarnings:i(),siteUrl:d(),isPremium:!!Cs().isPremium,titleInputPlaceholder:"",descriptionInputPlaceholder:"",socialMediumName:"Social",alt:f()}})),(0,h.withDispatch)(((e,t,{select:s})=>{const{setFacebookPreviewTitle:n,setFacebookPreviewDescription:r,clearFacebookPreviewImage:a,loadFacebookPreviewData:o,findCustomFields:i}=e("yoast-seo/editor"),l=s("yoast-seo/editor").getPostId();return{onSelectImageClick:ar,onRemoveImageClick:a,onDescriptionChange:r,onTitleChange:n,onLoad:o,onReplacementVariableSearchChange:Vt(l,i)}})),qt()])(nr),ir=t=>((0,n.useEffect)((()=>{setTimeout(t.onLoad)}),[]),t.isPremium?(0,e.createElement)(r.Slot,{name:`YoastTwitterPremium${t.location.charAt(0).toUpperCase()+t.location.slice(1)}`,fillProps:t}):(0,e.createElement)(tr,{...t}));ir.propTypes={isPremium:o().bool.isRequired,onLoad:o().func.isRequired,location:o().string.isRequired};const lr=ir,cr=()=>{rr((e=>(0,h.dispatch)("yoast-seo/editor").setTwitterPreviewImage((e=>{const t="summary"!==(0,i.get)(window,"wpseoScriptData.metabox.twitterCardType")?"landscape":"square",s=on.TWITTER_IMAGE_SIZES[t+"Width"],n=on.TWITTER_IMAGE_SIZES[t+"Height"],r=Object.values(e.sizes).find((e=>e.width>=s&&e.height>=n));return{url:r?r.url:e.url,id:e.id,warnings:(0,M.validateTwitterImage)(e),alt:e.alt||""}})(e))))},dr=(0,C.compose)([(0,h.withSelect)((e=>{const{getTwitterDescription:t,getTwitterTitle:s,getTwitterImageUrl:n,getFacebookImageUrl:r,getFacebookTitle:a,getFacebookDescription:o,getDescription:i,getSeoTitle:l,getTwitterWarnings:c,getTwitterImageType:d,getImageFallback:u,getRecommendedReplaceVars:p,getReplaceVars:m,getSiteUrl:h,getSeoTitleTemplate:g,getSeoTitleTemplateNoFallback:y,getSocialTitleTemplate:f,getSeoDescriptionTemplate:b,getSocialDescriptionTemplate:w,getReplacedExcerpt:E,getTwitterAltText:v}=e("yoast-seo/editor");return{imageUrl:n(),imageFallbackUrl:r()||u(),recommendedReplacementVariables:p(),replacementVariables:m(),description:t(),descriptionPreviewFallback:w()||o()||i()||b()||E()||"",title:s(),titlePreviewFallback:f()||a()||l()||y()||g()||"",imageWarnings:c(),siteUrl:h(),isPremium:!!Cs().isPremium,isLarge:"summary"!==d(),titleInputPlaceholder:"",descriptionInputPlaceholder:"",socialMediumName:"X",alt:v()}})),(0,h.withDispatch)(((e,t,{select:s})=>{const{setTwitterPreviewTitle:n,setTwitterPreviewDescription:r,clearTwitterPreviewImage:a,loadTwitterPreviewData:o,findCustomFields:i}=e("yoast-seo/editor"),l=s("yoast-seo/editor").getPostId();return{onSelectImageClick:cr,onRemoveImageClick:a,onDescriptionChange:r,onTitleChange:n,onLoad:o,onReplacementVariableSearchChange:Vt(l,i)}})),qt()])(lr),ur=c().legend`
	margin: 16px 0;
	padding: 0;
	color: ${y.colors.$color_headings};
	font-size: 12px;
	font-weight: 300;
`,pr=c().legend`
	margin: 0 0 16px;
	padding: 0;
	color: ${y.colors.$color_headings};
	font-size: 12px;
	font-weight: 300;
`,mr=c().div`
	padding: 16px;
`,hr=({useOpenGraphData:t,useTwitterData:s})=>(0,e.createElement)(n.Fragment,null,s&&t&&(0,e.createElement)(n.Fragment,null,(0,e.createElement)(ns,{hasSeparator:!1
/* translators: Social media appearance refers to a preview of how a page will be represented on social media. */,title:(0,k.__)("Social media appearance","wordpress-seo"),initialIsOpen:!0},(0,e.createElement)(pr,null,(0,k.__)("Determine how your post should look on social media like Facebook, X, Instagram, WhatsApp, Threads, LinkedIn, Slack, and more.","wordpress-seo")),(0,e.createElement)(or,null),(0,e.createElement)(ur,null,(0,k.__)("To customize the appearance of your post specifically for X, please fill out the 'X appearance' settings below. If you leave these settings untouched, the 'Social media appearance' settings mentioned above will also be applied for sharing on X.","wordpress-seo"))),(0,e.createElement)(ns,{title:(0,k.__)("X appearance","wordpress-seo"),hasSeparator:!0,initialIsOpen:!1},(0,e.createElement)(dr,null))),t&&!s&&(0,e.createElement)(mr,null,(0,e.createElement)(pr,null,(0,k.__)("Determine how your post should look on social media like Facebook, X, Instagram, WhatsApp, Threads, LinkedIn, Slack, and more.","wordpress-seo")),(0,e.createElement)(or,null)),!t&&s&&(0,e.createElement)(mr,null,(0,e.createElement)(pr,null,(0,k.__)("To customize the appearance of your post specifically for X, please fill out the 'X appearance' settings below.","wordpress-seo")),(0,e.createElement)(dr,null)));hr.propTypes={useOpenGraphData:o().bool.isRequired,useTwitterData:o().bool.isRequired};const gr=hr,yr=(0,h.withSelect)((e=>{const{getPreferences:t}=e("yoast-seo/editor"),{useOpenGraphData:s,useTwitterData:n}=t();return{useOpenGraphData:s,useTwitterData:n}}))(gr);function fr({target:t}){return(0,e.createElement)(w,{target:t},(0,e.createElement)(yr,null))}fr.propTypes={target:o().string.isRequired};const br=(0,M.makeOutboundLink)(),wr=c().div`
	padding: 16px;
`,Er="yoast-seo/editor";function vr({location:t,show:s}){return s?(0,e.createElement)(g.Alert,{type:"info"},(0,k.sprintf)(/* translators: %s Expands to "Yoast News SEO" */
(0,k.__)("Are you working on a news article? %s helps you optimize your site for Google News.","wordpress-seo"),"Yoast News SEO")+" ",(0,e.createElement)(br,{href:window.wpseoAdminL10n[`shortlinks.upsell.${t}.news`]},(0,k.sprintf)(/* translators: %s: Expands to "Yoast News SEO". */
(0,k.__)("Buy %s now!","wordpress-seo"),"Yoast News SEO"))):null}vr.propTypes={show:o().bool.isRequired,location:o().string.isRequired};const kr=(e,t,s)=>{const n=(0,h.useSelect)((e=>e(Er).getIsProduct()),[]),r=(0,h.useSelect)((e=>e(Er).getIsWooSeoActive()),[]),a=n&&r?{name:(0,k.__)("Item Page","wordpress-seo"),value:"ItemPage"}:e.find((e=>e.value===t));return[{name:(0,k.sprintf)(/* translators: %1$s expands to the plural name of the current post type, %2$s expands to the current site wide default. */
(0,k.__)("Default for %1$s (%2$s)","wordpress-seo"),s,a?a.name:""),value:""},...e]},_r=e=>(0,k.sprintf)(/* translators: %1$s expands to the plural name of the current post type, %2$s and %3$s expand to a link to the Settings page */
(0,k.__)("You can change the default type for %1$s under Content types in the %2$sSettings%3$s.","wordpress-seo"),e,"{{link}}","{{/link}}");o().string.isRequired,o().string.isRequired,o().string.isRequired;const Tr=t=>{const s=kr(t.pageTypeOptions,t.defaultPageType,t.postTypeName),r=kr(t.articleTypeOptions,t.defaultArticleType,t.postTypeName),a=(0,i.get)(window,"wpseoScriptData.metabox.woocommerceUpsellSchemaLink",""),o=(0,i.get)(window,"wpseoScriptData.woocommerceUpsell",""),[l,c]=(0,n.useState)(t.schemaArticleTypeSelected),d=(0,k.__)("Want your products stand out in search results with rich results like price, reviews and more?","wordpress-seo"),u=(0,h.useSelect)((e=>e(Er).getIsProduct()),[]),p=(0,h.useSelect)((e=>e(Er).getIsWooSeoActive()),[]),m=u&&p,y=(0,n.useCallback)(((e,t)=>{c(t)}),[l]);return(0,n.useEffect)((()=>{y(null,t.schemaArticleTypeSelected)}),[t.schemaArticleTypeSelected]),(0,e.createElement)(n.Fragment,null,(0,e.createElement)(g.FieldGroup,{label:(0,k.__)("What type of page or content is this?","wordpress-seo"),linkTo:t.additionalHelpTextLink
/* translators: Hidden accessibility text. */,linkText:(0,k.__)("Learn more about page or content types","wordpress-seo")}),o&&(0,e.createElement)(Xt,{link:a,text:d}),(0,e.createElement)(g.Select,{id:(0,M.join)(["yoast-schema-page-type",t.location]),options:s,label:(0,k.__)("Page type","wordpress-seo"),onChange:t.schemaPageTypeChange,selected:m?"ItemPage":t.schemaPageTypeSelected,disabled:m}),t.showArticleTypeInput&&(0,e.createElement)(g.Select,{id:(0,M.join)(["yoast-schema-article-type",t.location]),options:r,label:(0,k.__)("Article type","wordpress-seo"),onChange:t.schemaArticleTypeChange,selected:t.schemaArticleTypeSelected,onOptionFocus:y}),(0,e.createElement)(vr,{location:t.location,show:!t.isNewsEnabled&&(b=l,w=t.defaultArticleType,"NewsArticle"===b||""===b&&"NewsArticle"===w)}),t.displayFooter&&!m&&(0,e.createElement)("p",null,(f=t.postTypeName,(0,V.Z)({mixedString:_r(f),components:{link:(0,e.createElement)("a",{href:"/wp-admin/admin.php?page=wpseo_page_settings",target:"_blank"})}}))),m&&(0,e.createElement)("p",null,(0,k.sprintf)(/* translators: %1$s expands to Yoast WooCommerce SEO. */
(0,k.__)("You have %1$s activated on your site, automatically setting the Page type for your products to 'Item Page'. As a result, the Page type selection is disabled.","wordpress-seo"),"Yoast WooCommerce SEO")));var f,b,w},xr=o().arrayOf(o().shape({name:o().string,value:o().string}));Tr.propTypes={schemaPageTypeChange:o().func,schemaPageTypeSelected:o().string,pageTypeOptions:xr.isRequired,schemaArticleTypeChange:o().func,schemaArticleTypeSelected:o().string,articleTypeOptions:xr.isRequired,showArticleTypeInput:o().bool.isRequired,additionalHelpTextLink:o().string.isRequired,helpTextLink:o().string.isRequired,helpTextTitle:o().string.isRequired,helpTextDescription:o().string.isRequired,postTypeName:o().string.isRequired,displayFooter:o().bool,defaultPageType:o().string.isRequired,defaultArticleType:o().string.isRequired,location:o().string.isRequired,isNewsEnabled:o().bool},Tr.defaultProps={schemaPageTypeChange:()=>{},schemaPageTypeSelected:null,schemaArticleTypeChange:()=>{},schemaArticleTypeSelected:null,displayFooter:!1,isNewsEnabled:!1};const Sr=t=>t.isMetabox?(0,n.createPortal)((0,e.createElement)(wr,null,(0,e.createElement)(Tr,{...t})),document.getElementById("wpseo-meta-section-schema")):(0,e.createElement)(Tr,{...t});Sr.propTypes={showArticleTypeInput:o().bool,articleTypeLabel:o().string,additionalHelpTextLink:o().string,pageTypeLabel:o().string.isRequired,helpTextLink:o().string.isRequired,helpTextTitle:o().string.isRequired,helpTextDescription:o().string.isRequired,isMetabox:o().bool.isRequired,postTypeName:o().string.isRequired,displayFooter:o().bool,loadSchemaArticleData:o().func.isRequired,loadSchemaPageData:o().func.isRequired,location:o().string.isRequired},Sr.defaultProps={showArticleTypeInput:!1,articleTypeLabel:"",additionalHelpTextLink:"",displayFooter:!1};const Rr=Sr;class Cr{static get articleTypeInput(){return document.getElementById("yoast_wpseo_schema_article_type")}static get defaultArticleType(){return Cr.articleTypeInput.getAttribute("data-default")}static get articleType(){return Cr.articleTypeInput.value}static set articleType(e){Cr.articleTypeInput.value=e}static get pageTypeInput(){return document.getElementById("yoast_wpseo_schema_page_type")}static get defaultPageType(){return Cr.pageTypeInput.getAttribute("data-default")}static get pageType(){return Cr.pageTypeInput.value}static set pageType(e){Cr.pageTypeInput.value=e}}const Ir=t=>{const s=null!==Cr.articleTypeInput;(0,n.useEffect)((()=>{t.loadSchemaPageData(),s&&t.loadSchemaArticleData()}),[]);const{pageTypeOptions:r,articleTypeOptions:a}=window.wpseoScriptData.metabox.schema,o={articleTypeLabel:(0,k.__)("Article type","wordpress-seo"),pageTypeLabel:(0,k.__)("Page type","wordpress-seo"),postTypeName:window.wpseoAdminL10n.postTypeNamePlural,helpTextTitle:(0,k.__)("Yoast SEO automatically describes your pages using schema.org","wordpress-seo"),helpTextDescription:(0,k.__)("This helps search engines understand your website and your content. You can change some of your settings for this page below.","wordpress-seo"),showArticleTypeInput:s,pageTypeOptions:r,articleTypeOptions:a},i={...t,...o,...(l=t.location,"metabox"===l?{helpTextLink:wpseoAdminL10n["shortlinks.metabox.schema.explanation"],additionalHelpTextLink:wpseoAdminL10n["shortlinks.metabox.schema.page_type"],isMetabox:!0}:{helpTextLink:wpseoAdminL10n["shortlinks.sidebar.schema.explanation"],additionalHelpTextLink:wpseoAdminL10n["shortlinks.sidebar.schema.page_type"],isMetabox:!1})};var l;return(0,e.createElement)(Rr,{...i})};Ir.propTypes={displayFooter:o().bool.isRequired,schemaPageTypeSelected:o().string.isRequired,schemaArticleTypeSelected:o().string.isRequired,defaultArticleType:o().string.isRequired,defaultPageType:o().string.isRequired,loadSchemaPageData:o().func.isRequired,loadSchemaArticleData:o().func.isRequired,schemaPageTypeChange:o().func.isRequired,schemaArticleTypeChange:o().func.isRequired,location:o().string.isRequired};const Lr=(0,C.compose)([(0,h.withSelect)((e=>{const{getPreferences:t,getPageType:s,getDefaultPageType:n,getArticleType:r,getDefaultArticleType:a}=e("yoast-seo/editor"),{displaySchemaSettingsFooter:o,isNewsEnabled:i}=t();return{displayFooter:o,isNewsEnabled:i,schemaPageTypeSelected:s(),schemaArticleTypeSelected:r(),defaultArticleType:a(),defaultPageType:n()}})),(0,h.withDispatch)((e=>{const{setPageType:t,setArticleType:s,getSchemaPageData:n,getSchemaArticleData:r}=e("yoast-seo/editor");return{loadSchemaPageData:n,loadSchemaArticleData:r,schemaPageTypeChange:t,schemaArticleTypeChange:s}})),qt()])(Ir),Ar=()=>(0,e.createElement)("p",{className:"yoast-related-keyphrases-modal__loading-message"},(0,k.sprintf)(/* translators: %1$s expands to "Yoast SEO", %2$s expands to "Semrush". */
(0,k.__)("Please wait while %1$s connects to %2$s to get related keyphrases...","wordpress-seo"),"Yoast SEO","Semrush")," ",(0,e.createElement)(g.SvgIcon,{icon:"loading-spinner"})),Pr=(0,M.makeOutboundLink)(),Fr=()=>(0,e.createElement)(n.Fragment,null,(0,e.createElement)("p",null,(0,k.sprintf)(/* translators: %s : Expands to "Semrush". */
(0,k.__)("You've reached your request limit for today. Check back tomorrow or upgrade your plan over at %s.","wordpress-seo"),"Semrush")),(0,e.createElement)(Pr,{href:window.wpseoAdminL10n["shortlinks.semrush.prices"],className:"yoast-button-upsell"},(0,k.sprintf)(/* translators: %s : Expands to "Semrush". */
(0,k.__)("Upgrade your %s plan","wordpress-seo"),"Semrush"),(0,e.createElement)("span",{"aria-hidden":"true",className:"yoast-button-upsell__caret"}))),Mr="yoast-semrush-country-selector",qr=[{value:"us",name:"United States - US"},{value:"uk",name:"United Kingdom - UK"},{value:"ca",name:"Canada - CA"},{value:"ru",name:"Russia - RU"},{value:"de",name:"Germany - DE"},{value:"fr",name:"France - FR"},{value:"es",name:"Spain - ES"},{value:"it",name:"Italy - IT"},{value:"br",name:"Brazil - BR"},{value:"au",name:"Australia - AU"},{value:"ar",name:"Argentina - AR"},{value:"be",name:"Belgium - BE"},{value:"ch",name:"Switzerland - CH"},{value:"dk",name:"Denmark - DK"},{value:"fi",name:"Finland - FI"},{value:"hk",name:"Hong Kong - HK"},{value:"ie",name:"Ireland - IE"},{value:"il",name:"Israel - IL"},{value:"mx",name:"Mexico - MX"},{value:"nl",name:"Netherlands - NL"},{value:"no",name:"Norway - NO"},{value:"pl",name:"Poland - PL"},{value:"se",name:"Sweden - SE"},{value:"sg",name:"Singapore - SG"},{value:"tr",name:"Turkey - TR"},{value:"jp",name:"Japan - JP"},{value:"in",name:"India - IN"},{value:"hu",name:"Hungary - HU"},{value:"af",name:"Afghanistan - AF"},{value:"al",name:"Albania - AL"},{value:"dz",name:"Algeria - DZ"},{value:"ao",name:"Angola - AO"},{value:"am",name:"Armenia - AM"},{value:"at",name:"Austria - AT"},{value:"az",name:"Azerbaijan - AZ"},{value:"bh",name:"Bahrain - BH"},{value:"bd",name:"Bangladesh - BD"},{value:"by",name:"Belarus - BY"},{value:"bz",name:"Belize - BZ"},{value:"bo",name:"Bolivia - BO"},{value:"ba",name:"Bosnia and Herzegovina - BA"},{value:"bw",name:"Botswana - BW"},{value:"bn",name:"Brunei - BN"},{value:"bg",name:"Bulgaria - BG"},{value:"cv",name:"Cabo Verde - CV"},{value:"kh",name:"Cambodia - KH"},{value:"cm",name:"Cameroon - CM"},{value:"cl",name:"Chile - CL"},{value:"co",name:"Colombia - CO"},{value:"cr",name:"Costa Rica - CR"},{value:"hr",name:"Croatia - HR"},{value:"cy",name:"Cyprus - CY"},{value:"cz",name:"Czech Republic - CZ"},{value:"cd",name:"Congo - CD"},{value:"do",name:"Dominican Republic - DO"},{value:"ec",name:"Ecuador - EC"},{value:"eg",name:"Egypt - EG"},{value:"sv",name:"El Salvador - SV"},{value:"ee",name:"Estonia - EE"},{value:"et",name:"Ethiopia - ET"},{value:"ge",name:"Georgia - GE"},{value:"gh",name:"Ghana - GH"},{value:"gr",name:"Greece - GR"},{value:"gt",name:"Guatemala - GT"},{value:"gy",name:"Guyana - GY"},{value:"ht",name:"Haiti - HT"},{value:"hn",name:"Honduras - HN"},{value:"is",name:"Iceland - IS"},{value:"id",name:"Indonesia - ID"},{value:"jm",name:"Jamaica - JM"},{value:"jo",name:"Jordan - JO"},{value:"kz",name:"Kazakhstan - KZ"},{value:"kw",name:"Kuwait - KW"},{value:"lv",name:"Latvia - LV"},{value:"lb",name:"Lebanon - LB"},{value:"lt",name:"Lithuania - LT"},{value:"lu",name:"Luxembourg - LU"},{value:"mg",name:"Madagascar - MG"},{value:"my",name:"Malaysia - MY"},{value:"mt",name:"Malta - MT"},{value:"mu",name:"Mauritius - MU"},{value:"md",name:"Moldova - MD"},{value:"mn",name:"Mongolia - MN"},{value:"me",name:"Montenegro - ME"},{value:"ma",name:"Morocco - MA"},{value:"mz",name:"Mozambique - MZ"},{value:"na",name:"Namibia - NA"},{value:"np",name:"Nepal - NP"},{value:"nz",name:"New Zealand - NZ"},{value:"ni",name:"Nicaragua - NI"},{value:"ng",name:"Nigeria - NG"},{value:"om",name:"Oman - OM"},{value:"py",name:"Paraguay - PY"},{value:"pe",name:"Peru - PE"},{value:"ph",name:"Philippines - PH"},{value:"pt",name:"Portugal - PT"},{value:"ro",name:"Romania - RO"},{value:"sa",name:"Saudi Arabia - SA"},{value:"sn",name:"Senegal - SN"},{value:"rs",name:"Serbia - RS"},{value:"sk",name:"Slovakia - SK"},{value:"si",name:"Slovenia - SI"},{value:"za",name:"South Africa - ZA"},{value:"kr",name:"South Korea - KR"},{value:"lk",name:"Sri Lanka - LK"},{value:"th",name:"Thailand - TH"},{value:"bs",name:"Bahamas - BS"},{value:"tt",name:"Trinidad and Tobago - TT"},{value:"tn",name:"Tunisia - TN"},{value:"ua",name:"Ukraine - UA"},{value:"ae",name:"United Arab Emirates - AE"},{value:"uy",name:"Uruguay - UY"},{value:"ve",name:"Venezuela - VE"},{value:"vn",name:"Vietnam - VN"},{value:"zm",name:"Zambia - ZM"},{value:"zw",name:"Zimbabwe - ZW"},{value:"ly",name:"Libya - LY"}];class Dr extends n.Component{constructor(e){super(e),this.relatedKeyphrasesRequest=this.relatedKeyphrasesRequest.bind(this),this.onChangeHandler=this.onChangeHandler.bind(this)}componentDidMount(){this.props.response&&this.props.keyphrase===this.props.lastRequestKeyphrase||this.relatedKeyphrasesRequest()}storeCountryCode(e){ee()({path:"yoast/v1/semrush/country_code",method:"POST",data:{country_code:e}})}async relatedKeyphrasesRequest(){const{keyphrase:e,countryCode:t,newRequest:s}=this.props;s(t,e),this.storeCountryCode(t);const n=await this.doRequest(e,t);200!==n.status?this.handleFailedResponse(n):this.handleSuccessResponse(n)}handleSuccessResponse(e){const{setNoResultsFound:t,setRequestSucceeded:s}=this.props;0!==e.results.rows.length?s(e):t()}handleFailedResponse(e){const{setRequestLimitReached:t,setRequestFailed:s}=this.props;"error"in e&&(e.error.includes("TOTAL LIMIT EXCEEDED")?t():s(e))}async doRequest(e,t){return await ee()({path:(0,Es.addQueryArgs)("/yoast/v1/semrush/related_keyphrases",{keyphrase:e,country_code:t})})}onChangeHandler(e){this.props.setCountry(e)}render(){return(0,e.createElement)("div",{id:Mr},(0,e.createElement)(g.SingleSelect,{id:Mr+"-select",label:(0,k.__)("Show results for:","wordpress-seo"),name:"semrush-country-code",options:qr,selected:this.props.countryCode,onChange:this.onChangeHandler,wrapperClassName:"yoast-field-group yoast-field-group--inline"}),(0,e.createElement)(g.NewButton,{id:Mr+"-button",variant:"secondary",onClick:this.relatedKeyphrasesRequest},(0,k.__)("Select country","wordpress-seo")))}}Dr.propTypes={keyphrase:o().string,countryCode:o().string,response:o().object,lastRequestKeyphrase:o().string,setCountry:o().func.isRequired,newRequest:o().func.isRequired,setNoResultsFound:o().func.isRequired,setRequestSucceeded:o().func.isRequired,setRequestLimitReached:o().func.isRequired,setRequestFailed:o().func.isRequired},Dr.defaultProps={keyphrase:"",countryCode:"us",response:{},lastRequestKeyphrase:""};const Or=Dr,Nr=(0,M.makeOutboundLink)(c().a`
	display: inline-block;
	position: relative;
	outline: none;
	text-decoration: none;
	border-radius: 100%;
	width: 24px;
	height: 24px;
	margin: -4px 0;
	vertical-align: middle;

	color: ${y.colors.$color_help_text};
	
	&:hover,
	&:focus {
		color: ${y.colors.$color_snippet_focus};	
	}
	
	// Overwrite the default blue active color for links.
	&:active {
		color: ${y.colors.$color_help_text};	
	}

	&::before {
		position: absolute;
		top: 0;
		left: 0;
		padding: 2px;
		content: "\f223";
	}
`),$r=(0,M.makeOutboundLink)();class Br extends n.Component{constructor(e){super(e),this.transformTrendDataToChartPoints=this.transformTrendDataToChartPoints.bind(this),this.getAreaChartDataTableHeaderLabels=this.getAreaChartDataTableHeaderLabels.bind(this),this.mapAreaChartDataToTableData=this.mapAreaChartDataToTableData.bind(this)}transformTrendDataToChartPoints(e){return e.split(",").map(((e,t)=>({x:t,y:parseFloat(e)})))}getAreaChartDataTableHeaderLabels(){return[(0,k.__)("Twelve months ago","wordpress-seo"),(0,k.__)("Eleven months ago","wordpress-seo"),(0,k.__)("Ten months ago","wordpress-seo"),(0,k.__)("Nine months ago","wordpress-seo"),(0,k.__)("Eight months ago","wordpress-seo"),(0,k.__)("Seven months ago","wordpress-seo"),(0,k.__)("Six months ago","wordpress-seo"),(0,k.__)("Five months ago","wordpress-seo"),(0,k.__)("Four months ago","wordpress-seo"),(0,k.__)("Three months ago","wordpress-seo"),(0,k.__)("Two months ago","wordpress-seo"),(0,k.__)("Last month","wordpress-seo")]}mapAreaChartDataToTableData(e){return Math.round(100*e)}render(){const{keyphrase:t,relatedKeyphrases:s,countryCode:r,data:a,renderAction:o}=this.props,l="https://www.semrush.com/analytics/keywordoverview/?q="+encodeURIComponent(t)+"&db="+encodeURIComponent(r);return a&&!(0,i.isEmpty)(a.results)&&(0,e.createElement)(n.Fragment,null,(0,e.createElement)("table",{className:"yoast yoast-table"},(0,e.createElement)("thead",null,(0,e.createElement)("tr",null,(0,e.createElement)("th",{scope:"col",className:"yoast-table--primary"},(0,k.__)("Related keyphrase","wordpress-seo")),(0,e.createElement)("th",{scope:"col",abbr:(0,k.__)("Volume","wordpress-seo")},(0,k.__)("Volume","wordpress-seo"),(0,e.createElement)(Nr,{href:window.wpseoAdminL10n["shortlinks.semrush.volume_help"],className:"dashicons"},(0,e.createElement)("span",{className:"screen-reader-text"},/* translators: Hidden accessibility text. */
(0,k.__)("Learn more about the related keyphrases volume","wordpress-seo")))),(0,e.createElement)("th",{scope:"col",abbr:(0,k.__)("Trend","wordpress-seo")},(0,k.__)("Trend","wordpress-seo"),(0,e.createElement)(Nr,{href:window.wpseoAdminL10n["shortlinks.semrush.trend_help"],className:"dashicons"},(0,e.createElement)("span",{className:"screen-reader-text"},/* translators: Hidden accessibility text. */
(0,k.__)("Learn more about the related keyphrases trend","wordpress-seo")))),o&&(0,e.createElement)("td",{className:"yoast-table--nobreak"}))),(0,e.createElement)("tbody",null,a.results.rows.map(((t,n)=>{const r=t[0],a=this.transformTrendDataToChartPoints(t[2]),i=this.getAreaChartDataTableHeaderLabels();return(0,e.createElement)("tr",{key:n},(0,e.createElement)("td",null,r),(0,e.createElement)("td",null,t[1]),(0,e.createElement)("td",{className:"yoast-table--nopadding"},(0,e.createElement)(Ee,{width:66,height:24,data:a,strokeWidth:1.8,strokeColor:"#498afc",fillColor:"#ade3fc",className:"yoast-related-keyphrases-modal__chart",mapChartDataToTableData:this.mapAreaChartDataToTableData,dataTableCaption:(0,k.__)("Keyphrase volume in the last 12 months on a scale from 0 to 100.","wordpress-seo"),dataTableHeaderLabels:i})),o&&(0,e.createElement)("td",{className:"yoast-table--nobreak"},o(r,s)))})))),(0,e.createElement)("p",{style:{marginBottom:0}},(0,e.createElement)($r,{href:l},(0,k.sprintf)(/* translators: %s expands to Semrush */
(0,k.__)("Get more insights at %s","wordpress-seo"),"Semrush"))))}}Br.propTypes={data:o().object,keyphrase:o().string,relatedKeyphrases:o().array,countryCode:o().string,renderAction:o().func},Br.defaultProps={data:{},keyphrase:"",relatedKeyphrases:[],countryCode:"us",renderAction:null};const Ur=Br,Wr=(0,M.makeOutboundLink)(),Kr=()=>(0,e.createElement)(g.Alert,{type:"info"},(0,k.sprintf)(/* translators: %s: Expands to "Yoast SEO". */
(0,k.__)("You’ll reach more people with multiple keyphrases! Want to quickly add these related keyphrases to the %s analyses for even better content optimization?","wordpress-seo"),"Yoast SEO")+" ",(0,e.createElement)(Wr,{href:window.wpseoAdminL10n["shortlinks.semrush.premium_landing_page"]},(0,k.sprintf)(/* translators: %s: Expands to "Yoast SEO Premium". */
(0,k.__)("Explore %s!","wordpress-seo"),"Yoast SEO Premium"))),Hr=()=>(0,e.createElement)(g.Alert,{type:"error"},(0,k.__)("We've encountered a problem trying to get related keyphrases. Please try again later.","wordpress-seo")),Yr=()=>(0,e.createElement)(g.Alert,{type:"warning"},(0,k.sprintf)(/* translators: %s: Expands to "Yoast SEO". */
(0,k.__)("You've reached the maximum amount of 4 related keyphrases. You can change or remove related keyphrases in the %s metabox or sidebar.","wordpress-seo"),"Yoast SEO"));function zr(t){const{response:s,lastRequestKeyphrase:r,keyphrase:a,newRequest:o,setCountry:l,renderAction:c,countryCode:d,requestLimitReached:u,setRequestFailed:p,setNoResultsFound:m,relatedKeyphrases:h,setRequestSucceeded:g,setRequestLimitReached:y}=t,f=Cs().isPremium;return(0,e.createElement)(n.Fragment,null,!u&&(0,e.createElement)(n.Fragment,null,!f&&(0,e.createElement)(Kr,null),f&&function(e){return e&&e.length>=4}(h)&&(0,e.createElement)(Yr,null),(0,e.createElement)(Or,{countryCode:d,setCountry:l,newRequest:o,keyphrase:a,setRequestFailed:p,setNoResultsFound:m,setRequestSucceeded:g,setRequestLimitReached:y,response:s,lastRequestKeyphrase:r})),function(t){const{isPending:s,requestLimitReached:n,isSuccess:r,response:a,requestHasData:o}=t;return s?(0,e.createElement)(Ar,null):n?(0,e.createElement)(Fr,null):!r&&function(e){return!(0,i.isEmpty)(e)&&"error"in e}(a)?(0,e.createElement)(Hr,null):o?void 0:(0,e.createElement)("p",null,(0,k.__)("Sorry, there's no data available for that keyphrase/country combination.","wordpress-seo"))}(t),(0,e.createElement)(Ur,{keyphrase:a,relatedKeyphrases:h,countryCode:d,renderAction:c,data:s}))}zr.propTypes={keyphrase:o().string,relatedKeyphrases:o().array,renderAction:o().func,requestLimitReached:o().bool,countryCode:o().string.isRequired,setCountry:o().func.isRequired,newRequest:o().func.isRequired,setRequestSucceeded:o().func.isRequired,setRequestLimitReached:o().func.isRequired,setRequestFailed:o().func.isRequired,setNoResultsFound:o().func.isRequired,response:o().object,lastRequestKeyphrase:o().string},zr.defaultProps={keyphrase:"",relatedKeyphrases:[],renderAction:null,requestLimitReached:!1,response:{},lastRequestKeyphrase:""};const jr=(0,C.compose)([(0,h.withSelect)((e=>{const{getFocusKeyphrase:t,getSEMrushSelectedCountry:s,getSEMrushRequestLimitReached:n,getSEMrushRequestResponse:r,getSEMrushRequestIsSuccess:a,getSEMrushIsRequestPending:o,getSEMrushRequestHasData:i,getSEMrushRequestKeyphrase:l}=e("yoast-seo/editor");return{keyphrase:t(),countryCode:s(),requestLimitReached:n(),response:r(),isSuccess:a(),isPending:o(),requestHasData:i(),lastRequestKeyphrase:l()}})),(0,h.withDispatch)((e=>{const{setSEMrushChangeCountry:t,setSEMrushNewRequest:s,setSEMrushRequestSucceeded:n,setSEMrushRequestFailed:r,setSEMrushSetRequestLimitReached:a,setSEMrushNoResultsFound:o}=e("yoast-seo/editor");return{setCountry:e=>{t(e)},newRequest:(e,t)=>{s(e,t)},setRequestSucceeded:e=>{n(e)},setRequestFailed:e=>{r(e)},setRequestLimitReached:()=>{a()},setNoResultsFound:()=>{o()}}}))])(zr),Vr=(0,k.__)("Check your text on even more SEO criteria and get an enhanced keyphrase analysis, making it easier to optimize your content.","wordpress-seo"),Gr=t=>{const{locationContext:s}=(0,d.useRootContext)(),n=(0,Es.addQueryArgs)(wpseoAdminL10n[t.buyLink],{context:s});return(0,e.createElement)(Ys,{title:(0,k.__)("Get more help with writing content that ranks","wordpress-seo"),description:t.description,benefitsTitle:/* translators: %s expands to 'Yoast SEO Premium'. */
(0,k.sprintf)("%s also gives you:","Yoast SEO Premium"),benefits:Ms(),upsellButtonText:(0,k.sprintf)(/* translators: %s expands to 'Yoast SEO Premium'. */
(0,k.__)("Unlock with %s","wordpress-seo"),"Yoast SEO Premium"),upsellButton:{href:n,className:"yoast-button-upsell",rel:null,"data-ctb-id":"f6a84663-465f-4cb5-8ba5-f7a6d72224b2","data-action":"load-nfd-ctb"},upsellButtonLabel:(0,k.__)("1 year of premium support and updates included!","wordpress-seo")})};Gr.propTypes={buyLink:o().string.isRequired,description:o().string},Gr.defaultProps={description:Vr};const Zr=Gr,Xr=({location:t})=>{const[s,r]=(0,n.useState)(!1),a=(0,n.useCallback)((()=>r(!1)),[]),o=(0,n.useCallback)((()=>r(!0)),[]),i=(0,m.useSvgAria)();return(0,e.createElement)(n.Fragment,null,s&&(0,e.createElement)($,{title:(0,k.__)("Unlock Premium SEO analysis","wordpress-seo"),onRequestClose:a,additionalClassName:"",className:`${O} yoast-gutenberg-modal__box yoast-gutenberg-modal__no-padding`,id:"yoast-premium-seo-analysis-modal",shouldCloseOnClickOutside:!0},(0,e.createElement)(D,null,(0,e.createElement)(Zr,{buyLink:`shortlinks.upsell.${t}.premium_seo_analysis_button`}))),"sidebar"===t&&(0,e.createElement)(Y,{id:"yoast-premium-seo-analysis-modal-open-button",title:(0,k.__)("Premium SEO analysis","wordpress-seo"),prefixIcon:{icon:"seo-score-none",color:y.colors.$color_grey},onClick:o},(0,e.createElement)("div",{className:"yst-root"},(0,e.createElement)(m.Badge,{size:"small",variant:"upsell"},(0,e.createElement)(Fs,{className:"yst-w-2.5 yst-h-2.5 yst-shrink-0",...i})))),"metabox"===t&&(0,e.createElement)("div",{className:"yst-root"},(0,e.createElement)(F,{id:"yoast-premium-seo-analysis-metabox-modal-open-button",onClick:o},(0,e.createElement)(g.SvgIcon,{icon:"seo-score-none",color:y.colors.$color_grey}),(0,e.createElement)(F.Text,null,(0,k.__)("Premium SEO analysis","wordpress-seo")),(0,e.createElement)(m.Badge,{size:"small",variant:"upsell"},(0,e.createElement)(Fs,{className:"yst-w-2.5 yst-h-2.5 yst-mr-1 yst-shrink-0",...i}),(0,e.createElement)("span",null,"Premium")))))};Xr.propTypes={location:o().string},Xr.defaultProps={location:"sidebar"};const Qr=Xr,Jr=t=>(0,e.createElement)(Ys,{title:(0,k.__)("Reach a wider audience","wordpress-seo"),description:(0,k.__)("Get help optimizing for up to 5 related keyphrases. This helps you reach a wider audience and get more traffic.","wordpress-seo"),benefitsTitle:/* translators: %s expands to 'Yoast SEO Premium'. */
(0,k.sprintf)("%s also gives you:","Yoast SEO Premium"),benefits:Ms(),upsellButtonText:(0,k.sprintf)(/* translators: %s expands to 'Yoast SEO Premium'. */
(0,k.__)("Unlock with %s","wordpress-seo"),"Yoast SEO Premium"),upsellButton:{href:t.buyLink,className:"yoast-button-upsell",rel:null,"data-ctb-id":"f6a84663-465f-4cb5-8ba5-f7a6d72224b2","data-action":"load-nfd-ctb"},upsellButtonLabel:(0,k.__)("1 year free support and updates included!","wordpress-seo")});Jr.propTypes={buyLink:o().string.isRequired};const ea=Jr,ta=()=>{const[t,,,s,r]=(0,m.useToggleState)(!1),a=(0,n.useContext)(d.LocationContext),{locationContext:o}=(0,d.useRootContext)(),i=(0,m.useSvgAria)(),l=wpseoAdminL10n["sidebar"===a.toLowerCase()?"shortlinks.upsell.sidebar.additional_button":"shortlinks.upsell.metabox.additional_button"];return(0,e.createElement)(e.Fragment,null,t&&(0,e.createElement)($,{title:(0,k.__)("Add related keyphrases","wordpress-seo"),onRequestClose:r,additionalClassName:"",id:"yoast-additional-keyphrases-modal",className:`${O} yoast-gutenberg-modal__box yoast-gutenberg-modal__no-padding`,shouldCloseOnClickOutside:!0},(0,e.createElement)(D,null,(0,e.createElement)(ea,{buyLink:(0,Es.addQueryArgs)(l,{context:o})}))),"sidebar"===a&&(0,e.createElement)(Y,{id:"yoast-additional-keyphrase-modal-open-button",title:(0,k.__)("Add related keyphrase","wordpress-seo"),prefixIcon:{icon:"plus",color:y.colors.$color_grey_medium_dark},onClick:s},(0,e.createElement)("div",{className:"yst-root"},(0,e.createElement)(m.Badge,{size:"small",variant:"upsell"},(0,e.createElement)(Fs,{className:"yst-w-2.5 yst-h-2.5 yst-shrink-0",...i})))),"metabox"===a&&(0,e.createElement)("div",{className:"yst-root"},(0,e.createElement)(F,{id:"yoast-additional-keyphrase-metabox-modal-open-button",onClick:s},(0,e.createElement)(g.SvgIcon,{icon:"plus",color:y.colors.$color_grey_medium_dark}),(0,e.createElement)(F.Text,null,(0,k.__)("Add related keyphrase","wordpress-seo")),(0,e.createElement)(m.Badge,{size:"small",variant:"upsell"},(0,e.createElement)(Fs,{className:"yst-w-2.5 yst-h-2.5 yst-mr-1 yst-shrink-0",...i}),(0,e.createElement)("span",null,"Premium")))))};var sa,na,ra,aa,oa,ia,la,ca,da,ua,pa,ma,ha,ga,ya,fa,ba,wa,Ea,va,ka,_a,Ta,xa,Sa,Ra,Ca,Ia,La,Aa,Pa,Fa,Ma,qa,Da,Oa,Na,$a,Ba,Ua,Wa,Ka,Ha,Ya,za,ja,Va;function Ga(){return Ga=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var s=arguments[t];for(var n in s)Object.prototype.hasOwnProperty.call(s,n)&&(e[n]=s[n])}return e},Ga.apply(this,arguments)}const Za=t=>e.createElement("svg",Ga({xmlns:"http://www.w3.org/2000/svg","aria-hidden":"true",viewBox:"0 0 448 360"},t),sa||(sa=e.createElement("circle",{cx:226,cy:211,r:149,fill:"#f0ecf0"})),na||(na=e.createElement("path",{fill:"#fbd2a6",d:"M173.53 189.38s-35.47-5.3-41.78-11c-9.39-24.93-29.61-48-35.47-66.21-.71-2.24 3.72-11.39 3.53-15.41s-5.34-11.64-5.23-14-.09-15.27-.09-15.27l-4.75-.72s-5.13 6.07-3.56 9.87c-1.73-4.19 4.3 7.93.5 9.35 0 0-6-5.94-11.76-8.27s-19.57-3.65-19.57-3.65L43.19 73l-4.42.6L31 69.7l-2.85 5.12 7.53 5.29L40.86 92l17.19 10.2 10.2 10.56 9.86 3.56s26.49 79.67 45 92c17 11.33 37.23 15.92 37.23 15.92z"})),ra||(ra=e.createElement("path",{fill:"#a4286a",d:"M270.52 345.13c2.76-14.59 15.94-35.73 30.24-54.58 16.22-21.39 14-79.66-33.19-91.46-17.3-4.32-52.25-1-59.85-3.41C186.54 189 170 187 168 190.17c-5 10.51-7.73 27.81-5.51 36.26 1.18 4.73 3.54 5.91 20.49 13.4-5.12 15-16.35 26.3-22.86 37s7.88 27.2 7.1 33.51c-.48 3.8-4.26 21.13-7.18 34.25a149.47 149.47 0 0 0 110.3 8.66 25.66 25.66 0 0 1 .18-8.12z"})),aa||(aa=e.createElement("path",{fill:"#9a5815",d:"M206.76 66.43c-5 14.4-1.42 25.67-3.93 40.74-10 60.34-24.08 43.92-31.44 93.6 7.24-14.19 14.32-15.82 20.63-23.11-.83 3.09-10.25 13.75-8.05 34.81 9.85-8.51 6.35-8.75 11.86-8.54.36 3.25 3.53 3.22-3.59 10.53 2.52.69 17.42-14.32 20.16-12.66s0 5.72-6 7.76c2.15 2.2 30.47-3.87 43.81-14.71 4.93-4 10-13.16 13.38-18.2 7.17-10.62 12.38-24.77 17.71-36.6 8.94-19.87 15.09-39.34 16.11-61.31.53-10.44-3.41-18.44-4.41-28.86-2.57-27.8-67.63-37.26-86.24 16.55z"})),oa||(oa=e.createElement("path",{fill:"#efb17c",d:"M277.74 179.06c.62-.79 1.24-1.59 1.84-2.39-.85 2.59-1.52 3.73-1.84 2.39z"})),ia||(ia=e.createElement("path",{fill:"#fbd2a6",d:"M216.1 206.72c3.69-5.42 8.28-3.35 15.57-8.28 3.76-3.06 1.57-9.46 1.77-11.82 18.25 4.56 37.38-1.18 49.07-16 .62 5.16-2.77 22.27-.2 27 4.73 8.67 13.4 18.92 13.4 18.92-35.47-2.76-63.45 39-89.86 44.54 5.52-28.74-2.36-35.84 10.25-54.36z"})),la||(la=e.createElement("path",{fill:"#f6b488",d:"m235.21 167.9 53.21-25.23s-3.65 24-6.5 32.72c-64.05 62.66-46.47-7.33-46.71-7.49z"})),ca||(ca=e.createElement("path",{fill:"#fbd2a6",d:"M226.86 50.64C215 59.31 206.37 93.21 204 95.57c-19.46 19.47-3.59 41.39-3.94 51.24-.2 5.52-4.14 25.42 5.72 29.36 22.22 8.89 60-3.48 67.19-12.61 13.28-16.75 40.89-94.78 17.74-108.19-7.92-4.58-42.78-20.18-63.85-4.73z"})),da||(da=e.createElement("path",{fill:"#e5766c",d:"M243.69 143.66c-10.7-6.16-8.56-6.73-19.76-12.71-3.86-2.07-3.94.64-6.32 0-2.91-.79-1.39-2.74-5.37-3.48-6.52-1.21-3.67 3.63-3.15 6 1.32 6.15-8.17 17.3 3.26 21.42 12.65 4.55 21.38-9.41 31.34-11.23z"})),ua||(ua=e.createElement("path",{fill:"#fff",d:"M240.68 143.9c-11.49-5.53-11.65-8.17-24.64-11.69-8.6-2.32-5.53 1-5.69 4.42-.2 4.16-1.26 9.87 4.9 12.66 9 4.09 18.16-6.02 25.43-5.39zm.7-40.9c-.16 1.26-.06 4.9 5.46 8.25 11.43-4.73 16.36-2.56 17-3.33 1.48-1.76-2-8.87-7.88-9.85-5.58-.94-14.14 1.24-14.58 4.93z"})),pa||(pa=e.createElement("path",{fill:"#000001",d:"M263.53 108.19c-4.32-4.33-6.85-6.24-12.26-8.21-2.77-1-6.18.18-8.65 1.67a3.65 3.65 0 0 0-1.24 1.23h-.12a3.73 3.73 0 0 1 1-1.52 12.53 12.53 0 0 1 11.93-3c4.73 1 9.43 4.63 9.42 9.82z"})),ma||(ma=e.createElement("circle",{cx:254.13,cy:104.05,r:4.19,fill:"#000001"})),ha||(ha=e.createElement("path",{fill:"#fff",d:"M225.26 99.22c-.29 1-6.6 3.45-10.92 1.48-1.15-3.24-5-6.43-5.25-6.71-.5-2.86 5.55-8 10.06-6.3a10.21 10.21 0 0 1 6.11 11.53z"})),ga||(ga=e.createElement("path",{fill:"#000001",d:"M209.29 94.21c-.19-2.34 1.84-4.1 3.65-5.2 7-3.87 13.18 3 12.43 10h-.12c-.14-4-2.38-8.44-6.47-9.11a3.19 3.19 0 0 0-2.42.31c-1.37.85-2.38 2-3.89 2.56-1 .45-1.92.42-3 1.4h-.22z"})),ya||(ya=e.createElement("circle",{cx:219.55,cy:95.28,r:4,fill:"#000001"})),fa||(fa=e.createElement("path",{fill:"#efb17c",d:"M218.66 120.27a27.32 27.32 0 0 0 4.54 3.45c-2.29-.72-4.28-.69-6.32-2.27-2.53-2-3.39-5.16-.73-7.72 10.24-9.82 12.56-13.82 14.77-24.42-1 12.37-6 17.77-10.63 23.18-2.53 2.97-4.68 5.06-1.63 7.78z"})),ba||(ba=e.createElement("path",{fill:"#a57c52",d:"M231.22 69.91c-.67-3.41-8.78-2.83-11.06-1.93-3.48 1.39-6.08 5.22-7.13 8.53 2.9-4.3 6.74-8.12 12.46-6 1.16.42 3.18 2.35 4.48 1.85s1.03-2.2 1.25-2.45zm32.16 8.56c-2.75-1.66-12.24-5.08-12.18.82 2.56.24 5-.19 7.64.95 11.22 4.76 12.77 17.61 12.85 17.86.2-.53.1 1.26.23.7-.02.2.95-12.12-8.54-20.33z"})),wa||(wa=e.createElement("path",{fill:"#fbd2a6",d:"M53.43 250.73c6.29 0-.6-.17 7.34 0 1.89.05-2.38-.7 0-.69 4.54-4.2 12.48-.74 20.6-2.45 4.55.35 3.93 1.35 5.59 4.19 4.89 8.38 4.78 14.21 14 19.56 16.42 8.38 66 12.92 88.49 18.86 5.52.83 42.64-20.15 61-23.75 6.51 10.74 11.46 28.68 8.39 34.93-6.54 13.3-57.07 25.4-75.91 25.15C156.47 326.18 94 294 92.2 293c-.94-.57.7-.7-7.68 0s-10.15.72-17.47-1.4c-3-.87-4.61-1.33-6.33-3.54-2 .22-3.39.2-4.78-1-3.15-2.74-4.84-6.61-2.73-10.06h-.12c-3.35-2.48-6.54-7.69-3.08-11.72 1-1.18 6.06-1.94 7.77-2.28-1.58-.29-6.37.19-7.49-.72-3.06-2.5-4.96-11.55 3.14-11.55z"})),Ea||(Ea=e.createElement("path",{fill:"#a4286a",d:"M303.22 237.52c-9.87-11.88-41.59 8.19-47.8 12.34s-14.89 17.95-14.89 17.95c6 9.43 8.36 31 5.65 46.34l30.51-3s18-15.62 22.59-28.7 6.3-42.54 6.3-42.54"})),va||(va=e.createElement("path",{fill:"#cb9833",d:"M278.63 31.67c-6.08 0-22.91 4.07-22.93 12.91 0 11 47.9 38.38 16.14 85.85 10.21-.79 10.79-8.12 14.92-14.93-3.66 77-49.38 93.58-40.51 142.25 7.68-25.81 20.3-11.62 38.13-33.84 3.45 4.88 9 18.28-9.46 33.78 50-31.26 57.31-56.6 51.92-95C319.93 113.53 348.7 42 278.63 31.67z"})),ka||(ka=e.createElement("path",{fill:"#fbd2a6",d:"M283.64 126.83c-2.42 9.67-8 15.76-1.48 16.46A21.26 21.26 0 0 0 302 132.6c5.17-8.52 3.93-16.44-2.46-18s-13.48 2.56-15.9 12.23z"})),_a||(_a=e.createElement("path",{fill:"#efb17c",d:"M38 73.45c1.92 2 4.25 9.21 6.32 10.91 2.25 1.85 5.71 2.12 8.1 4.45 3.66-2 6-8.72 10-9.31-2.59 1.31-4.42 3.5-6.93 4.88-1.42.8-3 1.31-4.38 2.25-2.16-1.46-4.27-1.77-6.26-3.38-2.52-2.02-5.31-8-6.85-9.8z"})),Ta||(Ta=e.createElement("path",{fill:"#efb17c",d:"M39 74.4c4.83 1.1 12.52 6.44 15.89 10-3.22-1.34-14.73-6.15-15.89-10zm.62-1.5c6.71-.79 18 1.54 23.29 5.9-3.85-.2-5.42-1.48-9-2.94-4.08-1.69-8.83-2.03-14.29-2.96zm46.43 14.58c-3.72-1.32-10.52-1.13-13.22 3.52 2-1.16 1.84-2.11 4.18-1.72-3.81-4.15 8.16-.74 11.6-.24m-2.78 13.15c.56-3.29-8-7.81-10.58-9.17-6.25-3.29-12.16 1.36-19.33-4.53 5.94 6.1 14.23 2.5 19.55 5.76 3.06 1.88 8.65 6.09 9.35 9.38-.23-.4 1.29-1.44 1.01-1.44z"})),xa||(xa=e.createElement("circle",{cx:38.13,cy:30.03,r:3.14,fill:"#b89ac8"})),Sa||(Sa=e.createElement("circle",{cx:60.26,cy:39.96,r:3.14,fill:"#e31e0c"})),Ra||(Ra=e.createElement("circle",{cx:50.29,cy:25.63,r:3.14,fill:"#3baa45"})),Ca||(Ca=e.createElement("circle",{cx:22.19,cy:19.21,r:3.14,fill:"#2ca9e1"})),Ia||(Ia=e.createElement("circle",{cx:22.19,cy:30.03,r:3.14,fill:"#e31e0c"})),La||(La=e.createElement("circle",{cx:26.86,cy:8.28,r:3.14,fill:"#3baa45"})),Aa||(Aa=e.createElement("circle",{cx:49.32,cy:39.99,r:3.14,fill:"#e31e0c"})),Pa||(Pa=e.createElement("circle",{cx:63.86,cy:59.52,r:3.14,fill:"#f8ad39"})),Fa||(Fa=e.createElement("circle",{cx:50.88,cy:50.72,r:3.14,fill:"#3baa45"})),Ma||(Ma=e.createElement("circle",{cx:63.47,cy:76.17,r:3.14,fill:"#e31e0c"})),qa||(qa=e.createElement("circle",{cx:38.34,cy:14.83,r:3.14,fill:"#2ca9e1"})),Da||(Da=e.createElement("circle",{cx:44.44,cy:5.92,r:3.14,fill:"#f8ad39"})),Oa||(Oa=e.createElement("circle",{cx:57.42,cy:10.24,r:3.14,fill:"#e31e0c"})),Na||(Na=e.createElement("circle",{cx:66.81,cy:12.4,r:3.14,fill:"#2ca9e1"})),$a||($a=e.createElement("circle",{cx:77.95,cy:5.14,r:3.14,fill:"#b89ac8"})),Ba||(Ba=e.createElement("circle",{cx:77.95,cy:30.34,r:3.14,fill:"#e31e0c"})),Ua||(Ua=e.createElement("circle",{cx:80.97,cy:16.55,r:3.14,fill:"#f8ad39"})),Wa||(Wa=e.createElement("circle",{cx:62.96,cy:27.27,r:3.14,fill:"#3baa45"})),Ka||(Ka=e.createElement("circle",{cx:75.36,cy:48.67,r:3.14,fill:"#2ca9e1"})),Ha||(Ha=e.createElement("circle",{cx:76.11,cy:65.31,r:3.14,fill:"#3baa45"})),Ya||(Ya=e.createElement("path",{fill:"#71b026",d:"M78.58 178.43C54.36 167.26 32 198.93 5 198.93c19.56 20.49 63.53 1.52 69 15.5 1.48-14.01 4.11-30.9 4.58-36z"})),za||(za=e.createElement("path",{fill:"#074a67",d:"M67.75 251.08c0-4.65 10.13-72.65 10.13-72.65h2.8l-9.09 72.3z"})),ja||(ja=e.createElement("ellipse",{cx:255.38,cy:103.18,fill:"#fff",rx:1.84,ry:1.77})),Va||(Va=e.createElement("ellipse",{cx:221.24,cy:94.75,fill:"#fff",rx:1.84,ry:1.77}))),Xa=(0,C.compose)([(0,h.withSelect)(((e,t)=>{const{isAlertDismissed:s}=e(t.store||"yoast-seo/editor");return{isAlertDismissed:s(t.alertKey)}})),(0,h.withDispatch)(((e,t)=>{const{dismissAlert:s}=e(t.store||"yoast-seo/editor");return{onDismissed:()=>s(t.alertKey)}}))]),Qa=({children:t,id:s,hasIcon:n=!0,title:r,image:a=null,isAlertDismissed:o,onDismissed:i})=>o?null:(0,e.createElement)("div",{id:s,className:"notice-yoast yoast is-dismissible"},(0,e.createElement)("div",{className:"notice-yoast__container"},(0,e.createElement)("div",null,(0,e.createElement)("div",{className:"notice-yoast__header"},n&&(0,e.createElement)("span",{className:"yoast-icon"}),(0,e.createElement)("h2",{className:"notice-yoast__header-heading"},r)),(0,e.createElement)("p",null,t)),a&&(0,e.createElement)(a,{height:"60"})),(0,e.createElement)("button",{type:"button",className:"notice-dismiss",onClick:i},(0,e.createElement)("span",{className:"screen-reader-text"},/* translators: Hidden accessibility text. */
(0,k.__)("Dismiss this notice.","wordpress-seo"))));Qa.propTypes={children:o().node.isRequired,id:o().string.isRequired,hasIcon:o().bool,title:o().any.isRequired,image:o().elementType,isAlertDismissed:o().bool.isRequired,onDismissed:o().func.isRequired};const Ja=Xa(Qa),eo=({store:t="yoast-seo/editor",image:s=null,title:n,promoId:r,alertKey:a,children:o,...i})=>(0,h.select)(t).isPromotionActive(r)&&(0,e.createElement)(Ja,{alertKey:a,store:t,id:a,title:n,image:s,...i},o);eo.propTypes={store:o().string,image:o().elementType,title:o().any.isRequired,promoId:o().string.isRequired,alertKey:o().string.isRequired,children:o().node};const to=({store:t="yoast-seo/editor",location:s="sidebar",...r})=>{const a=(0,h.useSelect)((e=>e(t).getIsPremium()),[t]),o=(0,h.useSelect)((e=>e(t).selectLinkParams()),[t]),i="sidebar"===s?(0,k.sprintf)(/* translators: %1$s expands to YOAST SEO PREMIUM */
(0,k.__)("BLACK FRIDAY SALE: %1$s","wordpress-seo"),"YOAST SEO PREMIUM"):(0,n.createInterpolateElement)((0,k.sprintf)(/* translators: %1$s expands to YOAST SEO PREMIUM, %2$s expands to a link on yoast.com, %3$s expands to the anchor end tag. */
(0,k.__)("BLACK FRIDAY SALE: %1$s %2$sBuy now!%3$s","wordpress-seo"),"YOAST SEO PREMIUM","<a>","</a>"),{a:(0,e.createElement)("a",{href:(0,Es.addQueryArgs)("https://yoa.st/black-friday-sale",o),target:"_blank",rel:"noreferrer"})});return a?null:(0,e.createElement)(eo,{id:`black-friday-2023-promotion-${s}`,promoId:"black-friday-2023-promotion",alertKey:"black-friday-2023-promotion",store:t,title:i,image:Image,...r},(0,e.createElement)("span",{className:"yoast-bf-sale-badge"},(0,k.__)("30% OFF!","wordpress-seo")," "),"sidebar"===s&&(0,e.createElement)("a",{className:"yst-block yst--mb-[1em]",href:(0,Es.addQueryArgs)("https://yoa.st/black-friday-sale",o),target:"_blank",rel:"noreferrer"},(0,k.__)("Buy now!","wordpress-seo")))};to.propTypes={store:o().string,location:o().oneOf(["sidebar","metabox"])};const so=t=>s=>!(()=>{var e,t;const s=(0,h.select)("yoast-seo/editor").getIsPremium(),n=(0,h.select)("yoast-seo/editor").getWarningMessage();return(s&&null!==(e=null===(t=(0,h.select)("yoast-seo-premium/editor"))||void 0===t?void 0:t.getMetaboxWarning())&&void 0!==e?e:[]).length>0||n.length>0})()&&(0,e.createElement)(t,{...s}),no=so((()=>{const t=(0,h.useSelect)((e=>e("yoast-seo/editor").selectLinkParams()),[]),s=(0,k.sprintf)(/* translators: %1$s expands to 'WooCommerce'. */
(0,k.__)("Is your %1$s store ready for Black Friday?","wordpress-seo"),"WooCommerce");return(0,e.createElement)(eo,{id:"black-friday-2023-product-editor-checklist",alertKey:"black-friday-2023-product-editor-checklist",promoId:"black-friday-2023-checklist",store:"yoast-seo/editor",title:s,image:Za},(0,n.createInterpolateElement)((0,k.sprintf)(/* translators: %1$s expands to a 'strong' start tag, %2$s to a 'strong' end tag. */
(0,k.__)("The Yoast %1$sultimate Black Friday checklist%2$s helps you prepare in time, so you can boost your results during this sale.","wordpress-seo"),"<strong>","</strong>"),{strong:(0,e.createElement)("strong",null)})," ",(0,e.createElement)("a",{href:(0,Es.addQueryArgs)("https://yoa.st/black-friday-checklist",t),target:"_blank",rel:"noreferrer"},(0,k.__)("Get the checklist and start optimizing now!","wordpress-seo")))})),ro=so(to);function ao({settings:t}){const s=(0,h.useSelect)((e=>e("yoast-seo/editor").getIsTerm()),[]),a=(0,h.useSelect)((e=>e("yoast-seo/editor").getIsProduct()),[])&&window.wpseoScriptData&&"1"===window.wpseoScriptData.isWooCommerceActive;return(0,e.createElement)(n.Fragment,null,(0,e.createElement)(r.Fill,{name:"YoastMetabox"},(0,e.createElement)(Vs,{key:"warning",renderPriority:1},(0,e.createElement)(es,null)),(0,e.createElement)(Vs,{key:"time-constrained-notification",renderPriority:2},a&&(0,e.createElement)(no,null),(0,e.createElement)(ro,{image:null,hasIcon:!1,location:"metabox"})),t.isKeywordAnalysisActive&&(0,e.createElement)(Vs,{key:"keyword-input",renderPriority:8},(0,e.createElement)(Mt.KeywordInput,{isSEMrushIntegrationActive:t.isSEMrushIntegrationActive}),!window.wpseoScriptData.metabox.isPremium&&(0,e.createElement)(r.Fill,{name:"YoastRelatedKeyphrases"},(0,e.createElement)(jr,null))),(0,e.createElement)(Vs,{key:"search-appearance",renderPriority:9},(0,e.createElement)(ns,{id:"yoast-snippet-editor-metabox",title:(0,k.__)("Search appearance","wordpress-seo"),initialIsOpen:!0},(0,e.createElement)(Jt,{hasPaperStyle:!1}))),t.isContentAnalysisActive&&(0,e.createElement)(Vs,{key:"readability-analysis",renderPriority:10},(0,e.createElement)(Mt.ReadabilityAnalysis,{shouldUpsell:t.shouldUpsell})),t.isKeywordAnalysisActive&&(0,e.createElement)(Vs,{key:"seo-analysis",renderPriority:20},(0,e.createElement)(n.Fragment,null,(0,e.createElement)(Mt.SeoAnalysis,{shouldUpsell:t.shouldUpsell,shouldUpsellWordFormRecognition:t.isWordFormRecognitionActive}),t.shouldUpsell&&(0,e.createElement)(Qr,{location:"metabox"}))),t.isInclusiveLanguageAnalysisActive&&(0,e.createElement)(Vs,{key:"inclusive-language-analysis",renderPriority:21},(0,e.createElement)(Mt.InclusiveLanguageAnalysis,null)),t.isKeywordAnalysisActive&&(0,e.createElement)(Vs,{key:"additional-keywords-upsell",renderPriority:22},t.shouldUpsell&&(0,e.createElement)(ta,null)),t.isKeywordAnalysisActive&&t.isWincherIntegrationActive&&(0,e.createElement)(Vs,{key:"wincher-seo-performance",renderPriority:23},(0,e.createElement)(Ft,{location:"metabox"})),t.shouldUpsell&&!s&&(0,e.createElement)(Vs,{key:"internal-linking-suggestions-upsell",renderPriority:25},(0,e.createElement)(zs,null)),t.isCornerstoneActive&&(0,e.createElement)(Vs,{key:"cornerstone",renderPriority:30},(0,e.createElement)(Dt,null)),t.displayAdvancedTab&&(0,e.createElement)(Vs,{key:"advanced",renderPriority:40},(0,e.createElement)(ns,{id:"collapsible-advanced-settings",title:(0,k.__)("Advanced","wordpress-seo")},(0,e.createElement)(sn,null))),t.displaySchemaSettings&&(0,e.createElement)(Vs,{key:"schema",renderPriority:50},(0,e.createElement)(Lr,null)),(0,e.createElement)(Vs,{key:"social",renderPriority:-1},(0,e.createElement)(fr,{target:"wpseo-section-social"})),t.isInsightsEnabled&&(0,e.createElement)(Vs,{key:"insights",renderPriority:52},(0,e.createElement)(Ps,{location:"metabox"}))))}ao.propTypes={settings:o().object.isRequired};const oo=(0,C.compose)([(0,h.withSelect)(((e,t)=>{const{getPreferences:s}=e("yoast-seo/editor");return{settings:s(),store:t.store}}))])(ao);function io({target:t,store:s,theme:n}){return(0,e.createElement)(w,{target:t},(0,e.createElement)(R,{store:s,theme:n}),(0,e.createElement)(oo,{store:s,theme:n}))}io.propTypes={target:o().string.isRequired,store:o().object,theme:o().object};const lo=[];let co=null;class uo extends n.Component{constructor(e){super(e),this.state={registeredComponents:[]}}registerComponent(e,t){this.setState({registeredComponents:[...this.state.registeredComponents,{key:e,Component:t}]})}render(){return this.state.registeredComponents.map((({Component:t,key:s})=>(0,e.createElement)(t,{key:s})))}}function po(e,t){null===co||null===co.current?lo.push({key:e,Component:t}):co.current.registerComponent(e,t)}const mo=window.yoast.externals.redux,ho=window.jQuery;var go=s.n(ho);function yo(e){let t="";var s;return t=!1===function(e){if("undefined"==typeof tinyMCE||void 0===tinyMCE.editors||0===tinyMCE.editors.length)return!1;const t=tinyMCE.get(e);return null!==t&&!t.isHidden()}(e)||0==(s=e,null!==document.getElementById(s+"_ifr"))?function(e){return document.getElementById(e)&&document.getElementById(e).value||""}(e):tinyMCE.get(e).getContent(),t}i.noop,i.noop,i.noop;const{removeMarks:fo}=f.markers,{updateReplacementVariable:bo,updateData:wo,hideReplacementVariables:Eo,setContentImage:vo,setEditorDataContent:ko,setEditorDataTitle:_o,setEditorDataExcerpt:To,setEditorDataImageUrl:xo,setEditorDataSlug:So}=mo.actions;window.yoast=window.yoast||{},window.yoast.initEditorIntegration=function(t){window.YoastSEO=window.YoastSEO||{},window.YoastSEO._registerReactComponent=po,function(t){const s=Cs();co=(0,n.createRef)();const a={isRtl:s.isRtl};(0,n.render)((0,e.createElement)(r.SlotFillProvider,null,(0,e.createElement)(d.Root,{context:{locationContext:"classic-metabox"}},(0,e.createElement)(io,{target:"wpseo-metabox-root",store:t,theme:a})),(0,e.createElement)(uo,{ref:co})),document.getElementById("wpseo-metabox-root")),lo.forEach((e=>{co.current.registerComponent(e.key,e.Component)}))}(t)},window.yoast.EditorData=class{constructor(e,t,s="content"){this._refresh=e,this._store=t,this._tinyMceId=s,this._previousData={},this._previousEditorData={},this.updateReplacementData=this.updateReplacementData.bind(this),this.refreshYoastSEO=this.refreshYoastSEO.bind(this)}initialize(e,t=[]){const s=this.getInitialData(e);var n,r;n=s,r=this._store,(0,i.forEach)(n,((e,t)=>{zt.includes(t)||r.dispatch(Kt(t,e))})),this._store.dispatch(Eo(t)),this._previousEditorData.content=s.content,this._store.dispatch(ko(s.content)),this._previousEditorData.contentImage=s.contentImage,this._store.dispatch(vo(s.contentImage)),this.setImageInSnippetPreview(s.snippetPreviewImageURL||s.contentImage),this._previousEditorData.slug=s.slug,this._store.dispatch(So(s.slug)),this.updateReplacementData({target:{value:s.title}},"title"),this.updateReplacementData({target:{value:s.excerpt}},"excerpt"),this.updateReplacementData({target:{value:s.excerpt_only}},"excerpt_only"),this.subscribeToElements(),this.subscribeToStore(),this.subscribeToSnippetPreviewImage(),this.subscribeToTinyMceEditor(),this.subscribeToSlug()}subscribeToTinyMceEditor(){const e=e=>{if((0,i.isString)(e)||(e=this.getContent()),this._previousEditorData.content===e)return;if(this._previousEditorData.content=e,this._store.dispatch(ko(e)),this.featuredImageIsSet)return;const t=this.getContentImage(e);this._previousEditorData.contentImage!==t&&(this._previousEditorData.contentImage=t,this._store.dispatch(vo(t)),this.setImageInSnippetPreview(t))};go()(document).on("tinymce-editor-init",((t,s)=>{s.id===this._tinyMceId&&(e(this.getContent()),["input","change","cut","paste"].forEach((t=>s.on(t,(0,i.debounce)(e,1e3)))))}));const t=document.getElementById("attachment_content");t&&(e(t.value),t.addEventListener("input",(t=>e(t.target.value))))}subscribeToSlug(){const e=e=>{this._previousEditorData.slug!==e&&(this._previousEditorData.slug=e,this._store.dispatch(So(e)),this._store.dispatch(wo({slug:e})))},t=document.getElementById("slug");t&&t.addEventListener("input",(t=>e(t.target.value)));const s=document.getElementById("post_name");s&&s.addEventListener("input",(t=>e(t.target.value)));const n=document.getElementById("edit-slug-buttons");n&&new MutationObserver(((t,s)=>t.forEach((t=>{t.addedNodes.forEach((t=>{var n,r;if(null==t||null===(n=t.classList)||void 0===n||!n.contains("edit-slug"))return;const a=null===(r=document.getElementById("editable-post-name-full"))||void 0===r?void 0:r.innerText;a&&(e(a),s.disconnect(),this.subscribeToSlug())}))})))).observe(n,{childList:!0})}subscribeToSnippetPreviewImage(){if((0,i.isUndefined)(wp.media)||(0,i.isUndefined)(wp.media.featuredImage))return;go()("#postimagediv").on("click","#remove-post-thumbnail",(()=>{this.featuredImageIsSet=!1,this.setImageInSnippetPreview(this.getContentImage(this.getContent()))}));const e=wp.media.featuredImage.frame();var t,s,n;e.on("select",(()=>{const t=e.state().get("selection").first().attributes.url;t&&(this.featuredImageIsSet=!0,this.setImageInSnippetPreview(t))})),t=this._tinyMceId,s=["init"],n=()=>{const e=this.getContentImage(this.getContent()),t=this.getFeaturedImage()||e||"";this._store.dispatch(vo(e)),this.setImageInSnippetPreview(t)},"undefined"!=typeof tinyMCE&&"function"==typeof tinyMCE.on&&tinyMCE.on("addEditor",(function(e){const r=e.editor;r.id===t&&(0,i.forEach)(s,(function(e){r.on(e,n)}))}))}getFeaturedImage(){const e=go()("#set-post-thumbnail img").attr("src");return e?(this.featuredImageIsSet=!0,e):(this.featuredImageIsSet=!1,null)}setImageInSnippetPreview(e){this._store.dispatch(xo(e)),this._store.dispatch(wo({snippetPreviewImageURL:e}))}getContentImage(e){if(this.featuredImageIsSet)return"";const t=f.languageProcessing.imageInText(e);if(0===t.length)return"";const s=go().parseHTML(t.join(""));for(const e of s)if(e.src)return e.src;return""}getTitle(){const e=document.getElementById("title")||document.getElementById("name");return e&&e.value||""}getExcerpt(e=!0){const t=document.getElementById("excerpt"),s=t&&t.value||"",n="ja"===function(){const e=Cs();return(0,i.get)(e,"contentLocale","en_US")}()?80:156;return""!==s||!1===e?s:function(e,t=156){return(e=(e=(0,Ut.stripTags)(e)).trim()).length<=t||(e=e.substring(0,t),/\s/.test(e)&&(e=e.substring(0,e.lastIndexOf(" ")))),e}(this.getContent(),n)}getSlug(){let e="";const t=document.getElementById("new-post-slug")||document.getElementById("slug");return t?e=t.value:null!==document.getElementById("editable-post-name-full")&&(e=document.getElementById("editable-post-name-full").textContent),e}getContent(){return fo(yo(this._tinyMceId))}subscribeToElements(){this.subscribeToInputElement("title","title"),this.subscribeToInputElement("excerpt","excerpt"),this.subscribeToInputElement("excerpt","excerpt_only")}subscribeToInputElement(e,t){const s=document.getElementById(e);s&&s.addEventListener("input",(e=>{this.updateReplacementData(e,t)}))}updateReplacementData(e,t){let s=e.target.value;if("excerpt"===t&&""===s&&(s=this.getExcerpt()),this._previousEditorData[t]!==s){switch(this._previousEditorData[t]=s,t){case"title":this._store.dispatch(_o(s));break;case"excerpt":this._store.dispatch(To(s))}this._store.dispatch(bo(t,s))}}isShallowEqual(e,t){if(Object.keys(e).length!==Object.keys(t).length)return!1;for(const s in e)if(e.hasOwnProperty(s)&&(!(s in t)||e[s]!==t[s]))return!1;return!0}refreshYoastSEO(){const e=this.getData();!this.isShallowEqual(this._previousData,e)&&(this.handleEditorChange(e),this._previousData=e,window.YoastSEO&&window.YoastSEO.app&&window.YoastSEO.app.refresh())}handleEditorChange(e){this._previousData.excerpt!==e.excerpt&&(this._store.dispatch(bo("excerpt",e.excerpt)),this._store.dispatch(bo("excerpt_only",e.excerpt_only))),this._previousData.snippetPreviewImageURL!==e.snippetPreviewImageURL&&this.setImageInSnippetPreview(e.snippetPreviewImageURL),this._previousData.slug!==e.slug&&this._store.dispatch(So(e.slug)),this._previousData.title!==e.title&&this._store.dispatch(_o(e.title))}subscribeToStore(){this.subscriber=(0,i.debounce)(this.refreshYoastSEO,500),this._store.subscribe(this.subscriber)}getInitialData(e){e=function(e,t){if(!e.custom_taxonomies)return e;const s={};return(0,i.forEach)(e.custom_taxonomies,((e,t)=>{const{name:n,label:r,descriptionName:a,descriptionLabel:o}=function(e){const t=jt(e);return{name:"ct_"+t,label:Ht(e+" (custom taxonomy)"),descriptionName:"ct_desc_"+t,descriptionLabel:Ht(e+" description (custom taxonomy)")}}(t),i="string"==typeof e.name?(0,M.decodeHTML)(e.name):e.name,l="string"==typeof e.description?(0,M.decodeHTML)(e.description):e.description;s[n]={value:i,label:r},s[a]={value:l,label:o}})),t.dispatch(function(e){return{type:"SNIPPET_EDITOR_UPDATE_REPLACEMENT_VARIABLES_BATCH",updatedVariables:e}}(s)),(0,i.omit)({...e},"custom_taxonomies")}(e=function(e,t){return e.custom_fields?((0,i.forEach)(e.custom_fields,((e,s)=>{const{name:n,label:r}=function(e){return{name:"cf_"+jt(e),label:Ht(e+" (custom field)")}}(s);t.dispatch(Kt(n,e,r))})),(0,i.omit)({...e},"custom_fields")):e}(e,this._store),this._store);const t=this.getContent(),s=this.getFeaturedImage();return{...e,title:this.getTitle(),excerpt:this.getExcerpt(),excerpt_only:this.getExcerpt(!1),slug:this.getSlug(),content:t,snippetPreviewImageURL:s,contentImage:this.getContentImage(t)}}getData(){return{...this._store.getState().snippetEditor.data,title:this.getTitle(),content:this.getContent(),excerpt:this.getExcerpt(),excerpt_only:this.getExcerpt(!1)}}}})()})();