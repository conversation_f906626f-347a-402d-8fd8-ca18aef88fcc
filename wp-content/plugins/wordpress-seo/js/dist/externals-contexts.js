(()=>{"use strict";var e={n:o=>{var t=o&&o.__esModule?()=>o.default:()=>o;return e.d(t,{a:t}),t},d:(o,t)=>{for(var n in t)e.o(t,n)&&!e.o(o,n)&&Object.defineProperty(o,n,{enumerable:!0,get:t[n]})},o:(e,o)=>Object.prototype.hasOwnProperty.call(e,o)};const o=window.wp.element,t=(0,o.createContext)("location"),n=t.Provider,r=t.Consumer,a=window.React,s=window.yoast.propTypes;var i=e.n(s);const c={},d=(0,o.createContext)(c),w=({children:e,context:o={}})=>(0,a.createElement)(d.Provider,{value:{...c,...o}},e);w.propTypes={children:i().node.isRequired,context:i().object};const l=w;window.yoast=window.yoast||{},window.yoast.externals=window.yoast.externals||{},window.yoast.externals.contexts={LocationContext:t,LocationProvider:n,LocationConsumer:r,RootContext:d,Root:l,useRootContext:()=>(0,o.useContext)(d)}})();