(()=>{"use strict";var e={n:t=>{var s=t&&t.__esModule?()=>t.default:()=>t;return e.d(s,{a:s}),s},d:(t,s)=>{for(var n in s)e.o(s,n)&&!e.o(t,n)&&Object.defineProperty(t,n,{enumerable:!0,get:s[n]})},o:(e,t)=>Object.prototype.hasOwnProperty.call(e,t),r:e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})}},t={};e.r(t),e.d(t,{DISMISS_ALERT:()=>P,SNIPPET_EDITOR_FIND_CUSTOM_FIELDS:()=>T,wistiaEmbedPermission:()=>C});const s=window.wp.domReady;var n=e.n(s);const i=window.jQuery;var o=e.n(i);const a=window.lodash,r=window.wp.i18n;const c=window.wp.data,l=window.yoast.externals.redux,d=window.yoast.reduxJsToolkit,p=window.wp.url,u="linkParams",h=(0,d.createSlice)({name:u,initialState:{},reducers:{setLinkParams:(e,{payload:t})=>t}}),g=(h.getInitialState,{selectLinkParam:(e,t,s={})=>(0,a.get)(e,`${u}.${t}`,s),selectLinkParams:e=>(0,a.get)(e,u,{})});g.selectLink=(0,d.createSelector)([g.selectLinkParams,(e,t)=>t,(e,t,s={})=>s],((e,t,s)=>(0,p.addQueryArgs)(t,{...e,...s}))),h.actions,h.reducer;const m=(0,d.createSlice)({name:"notifications",initialState:{},reducers:{addNotification:{reducer:(e,{payload:t})=>{e[t.id]={id:t.id,variant:t.variant,size:t.size,title:t.title,description:t.description}},prepare:({id:e,variant:t="info",size:s="default",title:n,description:i})=>({payload:{id:e||(0,d.nanoid)(),variant:t,size:s,title:n||"",description:i}})},removeNotification:(e,{payload:t})=>(0,a.omit)(e,t)}}),y=(m.getInitialState,m.actions,m.reducer,"pluginUrl"),w=(0,d.createSlice)({name:y,initialState:"",reducers:{setPluginUrl:(e,{payload:t})=>t}}),f=(w.getInitialState,{selectPluginUrl:e=>(0,a.get)(e,y,"")});f.selectImageLink=(0,d.createSelector)([f.selectPluginUrl,(e,t,s="images")=>s,(e,t)=>t],((e,t,s)=>[(0,a.trimEnd)(e,"/"),(0,a.trim)(t,"/"),(0,a.trimStart)(s,"/")].join("/"))),w.actions,w.reducer;const b=window.wp.apiFetch;var _=e.n(b);const v="loading",E="showPlay",S="askPermission",k="isPlaying",x="wistiaEmbedPermission",R=(0,d.createSlice)({name:x,initialState:{value:!1,status:"idle",error:{}},reducers:{setWistiaEmbedPermissionValue:(e,{payload:t})=>{e.value=Boolean(t)}},extraReducers:e=>{e.addCase(`${x}/request`,(e=>{e.status=v})),e.addCase(`${x}/success`,((e,{payload:t})=>{e.status="success",e.value=Boolean(t&&t.value)})),e.addCase(`${x}/error`,((e,{payload:t})=>{e.status="error",e.value=Boolean(t&&t.value),e.error={code:(0,a.get)(t,"error.code",500),message:(0,a.get)(t,"error.message","Unknown")}}))}}),O=(R.getInitialState,R.actions,{[x]:async({payload:e})=>_()({path:"/yoast/v1/wistia_embed_permission",method:"POST",data:{value:Boolean(e)}})});function P({alertKey:e}){return new Promise((t=>wpseoApi.post("alerts/dismiss",{key:e},(()=>t()))))}function T({query:e,postId:t}){return new Promise((s=>{wpseoApi.get("meta/search",{query:e,post_id:t},(e=>{s(e.meta)}))}))}R.reducer;const C=O[x];const A=window.yoast.analysis,M=window.wp.isShallowEqual,I="yoastmark";function D(e,t){return e._properties.position.startOffset>t.length||e._properties.position.endOffset>t.length}function N(e,t,s){const n=e.dom;let i=e.getContent();if(i=A.markers.removeMarks(i),(0,a.isEmpty)(s))return void e.setContent(i);i=s[0].hasPosition()?function(e,t){if(!t)return"";for(let s=(e=(0,a.orderBy)(e,(e=>e._properties.position.startOffset),["asc"])).length-1;s>=0;s--){const n=e[s];D(n,t)||(t=n.applyWithPosition(t))}return t}(s,i):function(e,t,s,n){const{fieldsToMark:i,selectedHTML:o}=A.languageProcessing.getFieldsToMark(s,n);return(0,a.forEach)(s,(function(t){"acf_content"!==e.id&&(t._properties.marked=A.languageProcessing.normalizeHTML(t._properties.marked),t._properties.original=A.languageProcessing.normalizeHTML(t._properties.original)),i.length>0?o.forEach((e=>{const s=t.applyWithReplace(e);n=n.replace(e,s)})):n=t.applyWithReplace(n)})),n}(e,0,s,i),e.setContent(i),function(e){let t=e.getContent();t=t.replace(new RegExp("&lt;yoastmark.+?&gt;","g"),"").replace(new RegExp("&lt;/yoastmark&gt;","g"),""),e.setContent(t)}(e);const o=n.select(I);(0,a.forEach)(o,(function(e){e.setAttribute("data-mce-bogus","1")}))}function L(e){return window.test=e,N.bind(null,e)}const F="et_pb_main_editor_wrap",B=class{static isActive(){return!!document.getElementById(F)}static isTinyMCEHidden(){const e=document.getElementById(F);return!!e&&e.classList.contains("et_pb_hidden")}listen(e){this.classicEditorContainer=document.getElementById(F),this.classicEditorContainer&&new MutationObserver((t=>{(0,a.forEach)(t,(t=>{"attributes"===t.type&&"class"===t.attributeName&&(t.target.classList.contains("et_pb_hidden")?e.classicEditorHidden():e.classicEditorShown())}))})).observe(this.classicEditorContainer,{attributes:!0})}},U=class{static isActive(){return!!window.VCV_I18N}},j={classicEditorHidden:a.noop,classicEditorShown:a.noop,pageBuilderLoaded:a.noop},Y=class{constructor(){this.determineActivePageBuilders()}determineActivePageBuilders(){B.isActive()&&(this.diviActive=!0),U.isActive()&&(this.vcActive=!0)}isPageBuilderActive(){return this.diviActive||this.vcActive}listen(e){this.callbacks=(0,a.defaults)(e,j),this.diviActive&&(new B).listen(e)}isClassicEditorHidden(){return!(!this.diviActive||!B.isTinyMCEHidden())}};let q;const K="content",z="description";function $(e){if("undefined"==typeof tinyMCE||void 0===tinyMCE.editors||0===tinyMCE.editors.length)return!1;const t=tinyMCE.get(e);return null!==t&&!t.isHidden()}function V(e,t,s){"undefined"!=typeof tinyMCE&&"function"==typeof tinyMCE.on&&tinyMCE.on("addEditor",(function(n){const i=n.editor;i.id===e&&(0,a.forEach)(t,(function(e){i.on(e,s)}))}))}function W(){(0,a.isUndefined)(q)||q.dispatch(l.actions.setMarkerStatus("disabled"))}function Q(){(0,a.isUndefined)(q)||q.dispatch(l.actions.setMarkerStatus("enabled"))}class H{constructor(e){this.refresh=e,this.loaded=!1,this.preloadThreshold=3e3,this.plugins={},this.modifications={},this._registerPlugin=this._registerPlugin.bind(this),this._ready=this._ready.bind(this),this._reloaded=this._reloaded.bind(this),this._registerModification=this._registerModification.bind(this),this._registerAssessment=this._registerAssessment.bind(this),this._applyModifications=this._applyModifications.bind(this),setTimeout(this._pollLoadingPlugins.bind(this),1500)}_registerPlugin(e,t){return(0,a.isString)(e)?(0,a.isUndefined)(t)||(0,a.isObject)(t)?!1===this._validateUniqueness(e)?(console.error("Failed to register plugin. Plugin with name "+e+" already exists"),!1):(this.plugins[e]=t,!0):(console.error("Failed to register plugin "+e+". Expected parameters `options` to be a object."),!1):(console.error("Failed to register plugin. Expected parameter `pluginName` to be a string."),!1)}_ready(e){return(0,a.isString)(e)?(0,a.isUndefined)(this.plugins[e])?(console.error("Failed to modify status for plugin "+e+". The plugin was not properly registered."),!1):(this.plugins[e].status="ready",!0):(console.error("Failed to modify status for plugin "+e+". Expected parameter `pluginName` to be a string."),!1)}_reloaded(e){return(0,a.isString)(e)?(0,a.isUndefined)(this.plugins[e])?(console.error("Failed to reload Content Analysis for plugin "+e+". The plugin was not properly registered."),!1):(this.refresh(),!0):(console.error("Failed to reload Content Analysis for "+e+". Expected parameter `pluginName` to be a string."),!1)}_registerModification(e,t,s,n){if(!(0,a.isString)(e))return console.error("Failed to register modification for plugin "+s+". Expected parameter `modification` to be a string."),!1;if(!(0,a.isFunction)(t))return console.error("Failed to register modification for plugin "+s+". Expected parameter `callable` to be a function."),!1;if(!(0,a.isString)(s))return console.error("Failed to register modification for plugin "+s+". Expected parameter `pluginName` to be a string."),!1;if(!1===this._validateOrigin(s))return console.error("Failed to register modification for plugin "+s+". The integration has not finished loading yet."),!1;const i={callable:t,origin:s,priority:(0,a.isNumber)(n)?n:10};return(0,a.isUndefined)(this.modifications[e])&&(this.modifications[e]=[]),this.modifications[e].push(i),!0}_registerAssessment(e,t,s,n){return(0,a.isString)(t)?(0,a.isObject)(s)?(0,a.isString)(n)?(t=n+"-"+t,e.addAssessment(t,s),!0):(console.error("Failed to register assessment for plugin "+n+". Expected parameter `pluginName` to be a string."),!1):(console.error("Failed to register assessment for plugin "+n+". Expected parameter `assessment` to be a function."),!1):(console.error("Failed to register test for plugin "+n+". Expected parameter `name` to be a string."),!1)}_applyModifications(e,t,s){let n=this.modifications[e];return!(0,a.isArray)(n)||n.length<1||(n=this._stripIllegalModifications(n),n.sort(((e,t)=>e.priority-t.priority)),(0,a.forEach)(n,(function(n){const i=n.callable(t,s);typeof i==typeof t?t=i:console.error("Modification with name "+e+" performed by plugin with name "+n.origin+" was ignored because the data that was returned by it was of a different type than the data we had passed it.")}))),t}_pollLoadingPlugins(e){e=(0,a.isUndefined)(e)?0:e,!0===this._allReady()?(this.loaded=!0,this.refresh()):e>=this.preloadThreshold?(this._pollTimeExceeded(),this.loaded=!0,this.refresh()):(e+=50,setTimeout(this._pollLoadingPlugins.bind(this,e),50))}_allReady(){return(0,a.reduce)(this.plugins,(function(e,t){return e&&"ready"===t.status}),!0)}_pollTimeExceeded(){(0,a.forEach)(this.plugins,(function(e,t){(0,a.isUndefined)(e.options)||"ready"===e.options.status||(console.error("Error: Plugin "+t+". did not finish loading in time."),delete this.plugins[t])}))}_stripIllegalModifications(e){return(0,a.forEach)(e,((t,s)=>{!1===this._validateOrigin(t.origin)&&delete e[s]})),e}_validateOrigin(e){return"ready"===this.plugins[e].status}_validateUniqueness(e){return(0,a.isUndefined)(this.plugins[e])}}function G(e,t,s){e("morphology",new A.Paper("",{keyword:s})).then((e=>{const s=e.result.keyphraseForms;t.dispatch(l.actions.updateWordsToHighlight((0,a.uniq)((0,a.flatten)(s))))})).catch((()=>{t.dispatch(l.actions.updateWordsToHighlight([]))}))}const J=window.wp.api;function Z(){return window.wpseoScriptData&&"1"===window.wpseoScriptData.isBlockEditor}var X={source:"wpseoScriptData.analysis.plugins.replaceVars",scope:[],aliases:[]},ee=function(e,t,s){this.placeholder=e,this.replacement=t,this.options=(0,a.defaults)(s,X)};ee.prototype.getPlaceholder=function(e){return(e=e||!1)&&this.hasAlias()?this.placeholder+"|"+this.getAliases().join("|"):this.placeholder},ee.prototype.setSource=function(e){this.options.source=e},ee.prototype.hasScope=function(){return!(0,a.isEmpty)(this.options.scope)},ee.prototype.addScope=function(e){this.hasScope()||(this.options.scope=[]),this.options.scope.push(e)},ee.prototype.inScope=function(e){return!this.hasScope()||(0,a.indexOf)(this.options.scope,e)>-1},ee.prototype.hasAlias=function(){return!(0,a.isEmpty)(this.options.aliases)},ee.prototype.addAlias=function(e){this.hasAlias()||(this.options.aliases=[]),this.options.aliases.push(e)},ee.prototype.getAliases=function(){return this.options.aliases};const te=ee,{removeReplacementVariable:se,updateReplacementVariable:ne,refreshSnippetEditor:ie}=l.actions;var oe=["content","title","snippet_title","snippet_meta","primary_category","data_page_title","data_meta_desc","excerpt"],ae={},re={},ce=function(e,t){this._app=e,this._app.registerPlugin("replaceVariablePlugin",{status:"ready"}),this._store=t,this.replaceVariables=this.replaceVariables.bind(this),this.registerReplacements(),this.registerModifications(),this.registerEvents(),this.subscribeToGutenberg()};ce.prototype.registerReplacements=function(){this.addReplacement(new te("%%author_first_name%%","author_first_name")),this.addReplacement(new te("%%author_last_name%%","author_last_name")),this.addReplacement(new te("%%category%%","category")),this.addReplacement(new te("%%category_title%%","category_title")),this.addReplacement(new te("%%currentdate%%","currentdate")),this.addReplacement(new te("%%currentday%%","currentday")),this.addReplacement(new te("%%currentmonth%%","currentmonth")),this.addReplacement(new te("%%currenttime%%","currenttime")),this.addReplacement(new te("%%currentyear%%","currentyear")),this.addReplacement(new te("%%date%%","date")),this.addReplacement(new te("%%id%%","id")),this.addReplacement(new te("%%page%%","page")),this.addReplacement(new te("%%permalink%%","permalink")),this.addReplacement(new te("%%post_content%%","post_content")),this.addReplacement(new te("%%post_month%%","post_month")),this.addReplacement(new te("%%post_year%%","post_year")),this.addReplacement(new te("%%searchphrase%%","searchphrase")),this.addReplacement(new te("%%sitedesc%%","sitedesc")),this.addReplacement(new te("%%sitename%%","sitename")),this.addReplacement(new te("%%userid%%","userid")),this.addReplacement(new te("%%focuskw%%","keyword",{source:"app",aliases:["%%keyword%%"]})),this.addReplacement(new te("%%term_description%%","text",{source:"app",scope:["term","category","tag"],aliases:["%%tag_description%%","%%category_description%%"]})),this.addReplacement(new te("%%term_title%%","term_title",{scope:["term"]})),this.addReplacement(new te("%%term_hierarchy%%","term_hierarchy",{scope:["term"]})),this.addReplacement(new te("%%title%%","title",{source:"app",scope:["post","term","page"]})),this.addReplacement(new te("%%parent_title%%","title",{source:"app",scope:["page","category"]})),this.addReplacement(new te("%%excerpt%%","excerpt",{source:"app",scope:["post"],aliases:["%%excerpt_only%%"]})),this.addReplacement(new te("%%primary_category%%","primaryCategory",{source:"app",scope:["post"]})),this.addReplacement(new te("%%sep%%(\\s*%%sep%%)*","sep"))},ce.prototype.registerEvents=function(){const e=wpseoScriptData.analysis.plugins.replaceVars.scope;"post"===e&&jQuery(".categorydiv").each(this.bindTaxonomyEvents.bind(this)),"post"!==e&&"page"!==e||jQuery("#postcustomstuff > #list-table").each(this.bindFieldEvents.bind(this))},ce.prototype.subscribeToGutenberg=function(){if(!Z())return;const e={0:""};let t=null;const s=wp.data;s.subscribe((()=>{const n=s.select("core/editor").getEditedPostAttribute("parent");if(void 0!==n&&t!==n)return t=n,n<1?(this._currentParentPageTitle="",void this.declareReloaded()):(0,a.isUndefined)(e[n])?void J.loadPromise.done((()=>{new J.models.Page({id:n}).fetch().then((t=>{this._currentParentPageTitle=t.title.rendered,e[n]=this._currentParentPageTitle,this.declareReloaded()})).fail((()=>{this._currentParentPageTitle="",this.declareReloaded()}))})):(this._currentParentPageTitle=e[n],void this.declareReloaded())}))},ce.prototype.addReplacement=function(e){ae[e.placeholder]=e},ce.prototype.removeReplacement=function(e){delete ae[e.getPlaceholder()]},ce.prototype.registerModifications=function(){var e=this.replaceVariables.bind(this);(0,a.forEach)(oe,function(t){this._app.registerModification(t,e,"replaceVariablePlugin",10)}.bind(this))},ce.prototype.replaceVariables=function(e){return(0,a.isUndefined)(e)||(e=this.parentReplace(e),e=this.replaceCustomTaxonomy(e),e=this.replaceByStore(e),e=this.replacePlaceholders(e)),e},ce.prototype.replaceByStore=function(e){const t=this._store.getState().snippetEditor.replacementVariables;return(0,a.forEach)(t,(t=>{""!==t.value&&(e=e.replace("%%"+t.name+"%%",t.value))})),e},ce.prototype.getReplacementSource=function(e){return"app"===e.source?this._app.rawData:"direct"===e.source?"direct":wpseoScriptData.analysis.plugins.replaceVars.replace_vars},ce.prototype.getReplacement=function(e){var t=this.getReplacementSource(e.options);return!1===e.inScope(wpseoScriptData.analysis.plugins.replaceVars.scope)?"":"direct"===t?e.replacement:t[e.replacement]||""},ce.prototype.replacePlaceholders=function(e){return(0,a.forEach)(ae,function(t){e=e.replace(new RegExp(t.getPlaceholder(!0),"g"),this.getReplacement(t))}.bind(this)),e},ce.prototype.declareReloaded=function(){this._app.pluginReloaded("replaceVariablePlugin"),this._store.dispatch(ie())},ce.prototype.getCategoryName=function(e){var t=e.parent("label").clone();return t.children().remove(),t.text().trim()},ce.prototype.parseTaxonomies=function(e,t){(0,a.isUndefined)(re[t])&&(re[t]={});const s=[];(0,a.forEach)(e,function(e){const n=(e=jQuery(e)).val(),i=this.getCategoryName(e),o=e.prop("checked");re[t][n]={label:i,checked:o},o&&-1===s.indexOf(i)&&s.push(i)}.bind(this)),"category"!==t&&(t="ct_"+t),this._store.dispatch(ne(t,s.join(", ")))},ce.prototype.getAvailableTaxonomies=function(e){var t=jQuery(e).find("input[type=checkbox]"),s=jQuery(e).attr("id").replace("taxonomy-","");t.length>0&&this.parseTaxonomies(t,s),this.declareReloaded()},ce.prototype.bindTaxonomyEvents=function(e,t){(t=jQuery(t)).on("wpListAddEnd",".categorychecklist",this.getAvailableTaxonomies.bind(this,t)),t.on("change","input[type=checkbox]",this.getAvailableTaxonomies.bind(this,t)),this.getAvailableTaxonomies(t)},ce.prototype.replaceCustomTaxonomy=function(e){return(0,a.forEach)(re,function(t,s){var n="%%ct_"+s+"%%";"category"===s&&(n="%%"+s+"%%"),e=e.replace(n,this.getTaxonomyReplaceVar(s))}.bind(this)),e},ce.prototype.getTaxonomyReplaceVar=function(e){var t=[],s=re[e];return!0===(0,a.isUndefined)(s)?"":((0,a.forEach)(s,(function(e){!1!==e.checked&&t.push(e.label)})),jQuery.uniqueSort(t).join(", "))},ce.prototype.parseFields=function(e){jQuery(e).each(function(e,t){var s=jQuery("#"+t.id+"-key").val(),n=jQuery("#"+t.id+"-value").val();const i="cf_"+this.sanitizeCustomFieldNames(s),o=s+" (custom field)";this._store.dispatch(ne(i,n,o)),this.addReplacement(new te(`%%${i}%%`,n,{source:"direct"}))}.bind(this))},ce.prototype.removeFields=function(e){jQuery(e).each(function(e,t){var s=jQuery("#"+t.id+"-key").val();this.removeReplacement("%%cf_"+this.sanitizeCustomFieldNames(s)+"%%")}.bind(this))},ce.prototype.sanitizeCustomFieldNames=function(e){return e.replace(/\s/g,"_")},ce.prototype.getAvailableFields=function(e){this.removeCustomFields();var t=jQuery(e).find("#the-list > tr:visible[id]");t.length>0&&this.parseFields(t),this.declareReloaded()},ce.prototype.bindFieldEvents=function(e,t){var s=(t=jQuery(t)).find("#the-list");s.on("wpListDelEnd.wpseoCustomFields",this.getAvailableFields.bind(this,t)),s.on("wpListAddEnd.wpseoCustomFields",this.getAvailableFields.bind(this,t)),s.on("input.wpseoCustomFields",".textarea",this.getAvailableFields.bind(this,t)),s.on("click.wpseoCustomFields",".button + .updatemeta",this.getAvailableFields.bind(this,t)),this.getAvailableFields(t)},ce.prototype.removeCustomFields=function(){var e=(0,a.filter)(ae,(function(e,t){return t.indexOf("%%cf_")>-1}));(0,a.forEach)(e,function(e){this._store.dispatch(se((0,a.trim)(e.placeholder,"%%"))),this.removeReplacement(e)}.bind(this))},ce.prototype.parentReplace=function(e){const t=jQuery("#parent_id, #parent").eq(0);return this.hasParentTitle(t)&&(e=e.replace(/%%parent_title%%/,this.getParentTitleReplacement(t))),Z()&&!(0,a.isUndefined)(this._currentParentPageTitle)&&(e=e.replace(/%%parent_title%%/,this._currentParentPageTitle)),e},ce.prototype.hasParentTitle=function(e){return!(0,a.isUndefined)(e)&&!(0,a.isUndefined)(e.prop("options"))},ce.prototype.getParentTitleReplacement=function(e){var t=e.find("option:selected").text();return t===wpseoScriptData.analysis.plugins.replaceVars.no_parent_text?"":t},ce.ReplaceVar=te;const le=ce,de=window.wp.hooks,pe="[^<>&/\\[\\]\0- =]+?",ue=new RegExp("\\["+pe+"( [^\\]]+?)?\\]","g"),he=new RegExp("\\[/"+pe+"\\]","g");class ge{constructor({registerPlugin:e,registerModification:t,pluginReady:s,pluginReloaded:n},i){this._registerModification=t,this._pluginReady=s,this._pluginReloaded=n,e("YoastShortcodePlugin",{status:"loading"}),this.bindElementEvents();const o="("+i.join("|")+")";this.shortcodesRegex=new RegExp(o,"g"),this.closingTagRegex=new RegExp("\\[\\/"+o+"\\]","g"),this.nonCaptureRegex=new RegExp("\\["+o+"[^\\]]*?\\]","g"),this.parsedShortcodes=[],this.loadShortcodes(this.declareReady.bind(this))}declareReady(){this._pluginReady("YoastShortcodePlugin"),this.registerModifications()}declareReloaded(){this._pluginReloaded("YoastShortcodePlugin")}registerModifications(){this._registerModification("content",this.replaceShortcodes.bind(this),"YoastShortcodePlugin")}removeUnknownShortCodes(e){return(e=e.replace(ue,"")).replace(he,"")}replaceShortcodes(e){return"string"==typeof e&&this.parsedShortcodes.forEach((({shortcode:t,output:s})=>{e=e.replace(t,s)})),e=this.removeUnknownShortCodes(e)}loadShortcodes(e){const t=this.getUnparsedShortcodes(this.getShortcodes(this.getContentTinyMCE()));if(!(t.length>0))return e();this.parseShortcodes(t,e)}bindElementEvents(){const e=document.querySelector(".wp-editor-area"),t=(0,a.debounce)(this.loadShortcodes.bind(this,this.declareReloaded.bind(this)),500);e&&(e.addEventListener("keyup",t),e.addEventListener("change",t)),"undefined"!=typeof tinyMCE&&"function"==typeof tinyMCE.on&&tinyMCE.on("addEditor",(function(e){e.editor.on("change",t),e.editor.on("keyup",t)}))}getContentTinyMCE(){let e=document.querySelector(".wp-editor-area")?document.querySelector(".wp-editor-area").value:"";return"undefined"!=typeof tinyMCE&&void 0!==tinyMCE.editors&&0!==tinyMCE.editors.length&&(e=tinyMCE.get("content")?tinyMCE.get("content").getContent():""),e}getUnparsedShortcodes(e){return"object"!=typeof e?(console.error("Failed to get unparsed shortcodes. Expected parameter to be an array, instead received "+typeof e),!1):e.filter((e=>this.isUnparsedShortcode(e)))}isUnparsedShortcode(e){return!this.parsedShortcodes.some((({shortcode:t})=>t===e))}getShortcodes(e){if("string"!=typeof e)return console.error("Failed to get shortcodes. Expected parameter to be a string, instead received"+typeof e),!1;const t=this.matchCapturingShortcodes(e);t.forEach((t=>{e=e.replace(t,"")}));const s=this.matchNonCapturingShortcodes(e);return t.concat(s)}matchCapturingShortcodes(e){const t=(e.match(this.closingTagRegex)||[]).join(" ").match(this.shortcodesRegex)||[];return(0,a.flatten)(t.map((t=>{const s="\\["+t+"[^\\]]*?\\].*?\\[\\/"+t+"\\]";return e.match(new RegExp(s,"g"))||[]})))}matchNonCapturingShortcodes(e){return e.match(this.nonCaptureRegex)||[]}parseShortcodes(e,t){return"function"!=typeof t?(console.error("Failed to parse shortcodes. Expected parameter to be a function, instead received "+typeof t),!1):"object"==typeof e&&e.length>0?void jQuery.post(ajaxurl,{action:"wpseo_filter_shortcodes",_wpnonce:wpseoScriptData.analysis.plugins.shortcodes.wpseo_filter_shortcodes_nonce,data:e},function(e){this.saveParsedShortcodes(e,t)}.bind(this)):t()}saveParsedShortcodes(e,t){const s=JSON.parse(e);this.parsedShortcodes.push(...s),t()}}const me=ge,{updateShortcodesForParsing:ye}=l.actions;function we(e){var t=jQuery(".yst-traffic-light"),s=t.closest(".wpseo-meta-section-link"),n=jQuery("#wpseo-traffic-light-desc"),i=e.className||"na";t.attr("class","yst-traffic-light "+i),s.attr("aria-describedby","wpseo-traffic-light-desc"),n.length>0?n.text(e.screenReaderText):s.closest("li").append("<span id='wpseo-traffic-light-desc' class='screen-reader-text'>"+e.screenReaderText+"</span>")}function fe(e){jQuery("#wp-admin-bar-wpseo-menu .wpseo-score-icon").attr("title",e.screenReaderText).attr("class","wpseo-score-icon "+e.className).find(".wpseo-score-text").text(e.screenReaderText)}function be(){return(0,a.get)(window,"wpseoScriptData.metabox",{intl:{},isRtl:!1})}function _e(){const e=be();return(0,a.get)(e,"contentLocale","en_US")}function ve(){const e=be();return(0,a.get)(e,"translations",{domain:"wordpress-seo",locale_data:{"wordpress-seo":{"":{}}}})}function Ee(){const e=be();return!0===(0,a.get)(e,"contentAnalysisActive",!1)}function Se(){const e=be();return!0===(0,a.get)(e,"keywordAnalysisActive",!1)}function ke(){const e=be();return!0===(0,a.get)(e,"inclusiveLanguageAnalysisActive",!1)}const xe=window.yoast.featureFlag;function Re(){}let Oe=!1;function Pe(e){return e.sort(((e,t)=>e._identifier.localeCompare(t._identifier)))}function Te(e,t,s,n,i){if(!Oe)return;const o=A.Paper.parse(t());e.analyze(o).then((a=>{const{result:{seo:r,readability:c,inclusiveLanguage:d}}=a;if(r){const e=r[""];e.results.forEach((e=>{e.getMarker=()=>()=>s(o,e.marks)})),e.results=Pe(e.results),n.dispatch(l.actions.setSeoResultsForKeyword(o.getKeyword(),e.results)),n.dispatch(l.actions.setOverallSeoScore(e.score,o.getKeyword())),n.dispatch(l.actions.refreshSnippetEditor()),i.saveScores(e.score,o.getKeyword())}c&&(c.results.forEach((e=>{e.getMarker=()=>()=>s(o,e.marks)})),c.results=Pe(c.results),n.dispatch(l.actions.setReadabilityResults(c.results)),n.dispatch(l.actions.setOverallReadabilityScore(c.score)),n.dispatch(l.actions.refreshSnippetEditor()),i.saveContentScore(c.score)),d&&(d.results.forEach((e=>{e.getMarker=()=>()=>s(o,e.marks)})),d.results=Pe(d.results),n.dispatch(l.actions.setInclusiveLanguageResults(d.results)),n.dispatch(l.actions.setOverallInclusiveLanguageScore(d.score)),n.dispatch(l.actions.refreshSnippetEditor()),i.saveInclusiveLanguageScore(d.score)),(0,de.doAction)("yoast.analysis.refresh",a,{paper:o,worker:e,collectData:t,applyMarks:s,store:n,dataCollector:i})})).catch(Re)}const Ce="yoast-measurement-element";function Ae(e){let t=document.getElementById(Ce);return t||(t=function(){const e=document.createElement("div");return e.id=Ce,e.style.position="absolute",e.style.left="-9999em",e.style.top=0,e.style.height=0,e.style.overflow="hidden",e.style.fontFamily="arial, sans-serif",e.style.fontSize="20px",e.style.fontWeight="400",document.body.appendChild(e),e}()),t.innerText=e,t.offsetWidth}function Me(e){return(0,a.isNil)(e)||(e/=10),function(e){switch(e){case"feedback":return{className:"na",screenReaderText:(0,r.__)("Feedback","wordpress-seo"),screenReaderReadabilityText:"",screenReaderInclusiveLanguageText:""};case"bad":return{className:"bad",screenReaderText:(0,r.__)("Needs improvement","wordpress-seo"),screenReaderReadabilityText:(0,r.__)("Needs improvement","wordpress-seo"),screenReaderInclusiveLanguageText:(0,r.__)("Needs improvement","wordpress-seo")};case"ok":return{className:"ok",screenReaderText:(0,r.__)("OK SEO score","wordpress-seo"),screenReaderReadabilityText:(0,r.__)("OK","wordpress-seo"),screenReaderInclusiveLanguageText:(0,r.__)("Potentially non-inclusive","wordpress-seo")};case"good":return{className:"good",screenReaderText:(0,r.__)("Good SEO score","wordpress-seo"),screenReaderReadabilityText:(0,r.__)("Good","wordpress-seo"),screenReaderInclusiveLanguageText:(0,r.__)("Good","wordpress-seo")};default:return{className:"loading",screenReaderText:"",screenReaderReadabilityText:"",screenReaderInclusiveLanguageText:""}}}(A.interpreters.scoreToRating(e))}const Ie=jQuery,De=function(e){"object"==typeof CKEDITOR&&console.warn("YoastSEO currently doesn't support ckEditor. The content analysis currently only works with the HTML editor or TinyMCE."),this._store=e.store};De.prototype.getData=function(){const e={title:this.getSnippetTitle(),keyword:Se()?this.getKeyword():"",text:this.getText(),permalink:this.getPermalink(),snippetCite:this.getSnippetCite(),snippetTitle:this.getSnippetTitle(),snippetMeta:this.getSnippetMeta(),name:this.getName(),baseUrl:this.getBaseUrl(),pageTitle:this.getSnippetTitle(),titleWidth:Ae(this.getSnippetTitle())},t=this._store.getState();return{...e,metaTitle:(0,a.get)(t,["analysisData","snippet","title"],this.getSnippetTitle()),url:(0,a.get)(t,["snippetEditor","data","slug"],this.getSlug()),meta:(0,a.get)(t,["analysisData","snippet","description"],this.getSnippetMeta())}},De.prototype.getKeyword=function(){return document.getElementById("hidden_wpseo_focuskw").value},De.prototype.getText=function(){return function(e){let t="";var s;return t=!1===$(e)||0==(s=e,null!==document.getElementById(s+"_ifr"))?function(e){return document.getElementById(e)&&document.getElementById(e).value||""}(e):tinyMCE.get(e).getContent(),t}(z)},De.prototype.getSlug=function(){return document.getElementById("slug").value},De.prototype.getPermalink=function(){const e=this.getSlug();return this.getBaseUrl()+e+"/"},De.prototype.getSnippetCite=function(){return this.getSlug()},De.prototype.getSnippetTitle=function(){return document.getElementById("hidden_wpseo_title").value},De.prototype.getSnippetMeta=function(){const e=document.getElementById("hidden_wpseo_desc");return e?e.value:""},De.prototype.getName=function(){return document.getElementById("name").value},De.prototype.getBaseUrl=function(){return wpseoScriptData.metabox.base_url},De.prototype.setDataFromSnippet=function(e,t){switch(t){case"snippet_meta":document.getElementById("hidden_wpseo_desc").value=e;break;case"snippet_cite":document.getElementById("slug").value=e;break;case"snippet_title":document.getElementById("hidden_wpseo_title").value=e}},De.prototype.saveSnippetData=function(e){this.setDataFromSnippet(e.title,"snippet_title"),this.setDataFromSnippet(e.urlPath,"snippet_cite"),this.setDataFromSnippet(e.metaDesc,"snippet_meta")},De.prototype.bindElementEvents=function(e){this.inputElementEventBinder(e)},De.prototype.inputElementEventBinder=function(e){const t=["name",z,"slug","wpseo_focuskw"];for(let s=0;s<t.length;s++)null!==document.getElementById(t[s])&&document.getElementById(t[s]).addEventListener("input",e);!function(e,t){V(t,["input","change","cut","paste"],e),V(t,["hide"],W);const s=["show"];(new Y).isPageBuilderActive()||s.push("init"),V(t,s,Q),V("content",["focus"],(function(e){const t=e.target;(function(e){return-1!==e.getContent({format:"raw"}).indexOf("<"+I)})(t)&&(function(e){L(e)(null,[])}(t),YoastSEO.app.disableMarkers()),(0,a.isUndefined)(q)||q.dispatch(l.actions.setMarkerPauseStatus(!0))})),V("content",["blur"],(function(){(0,a.isUndefined)(q)||q.dispatch(l.actions.setMarkerPauseStatus(!1))}))}(e,z)},De.prototype.saveScores=function(e){const t=Me(e);document.getElementById("hidden_wpseo_linkdex").value=e,jQuery(window).trigger("YoastSEO:numericScore",e),we(t),fe(t)},De.prototype.saveContentScore=function(e){const t=Me(e);Se()||(we(t),fe(t)),Ie("#hidden_wpseo_content_score").val(e)},De.prototype.saveInclusiveLanguageScore=function(e){const t=Me(e);Se()||Ee()||(we(t),fe(t)),Ie("#hidden_wpseo_inclusive_language_score").val(e)};const Ne=De;class Le{constructor(){this._callbacks=[],this.register=this.register.bind(this)}register(e){(0,a.isFunction)(e)&&this._callbacks.push(e)}getData(){let e={};return this._callbacks.forEach((t=>{e=(0,a.merge)(e,t())})),e}}window.wp.annotations;const Fe=function(e){return(0,a.uniq)((0,a.flatten)(e.map((e=>{if(!(0,a.isUndefined)(e.getFieldsToMark()))return e.getFieldsToMark()}))))},Be=window.wp.richText,Ue=/(<([a-z]|\/)[^<>]+>)/gi,{htmlEntitiesRegex:je}=A.helpers.htmlEntities,Ye=e=>{let t=0;return(0,a.forEachRight)(e,(e=>{const[s]=e;let n=s.length;/^<\/?br/.test(s)&&(n-=1),t+=n})),t},qe="<yoastmark class='yoast-text-mark'>",Ke="</yoastmark>",ze='<yoastmark class="yoast-text-mark">';function $e(e,t,s,n,i){const o=n.clientId,r=(0,Be.create)({html:e,multilineTag:s.multilineTag,multilineWrapperTag:s.multilineWrapperTag}).text;return(0,a.flatMap)(i,(s=>{let i;return i=s.hasBlockPosition&&s.hasBlockPosition()?function(e,t,s,n,i){if(t===e.getBlockClientId()){let t=e.getBlockPositionStart(),o=e.getBlockPositionEnd();if(e.isMarkForFirstBlockSection()){const e=((e,t,s)=>{const n="yoast/faq-block"===s?'<strong class="schema-faq-question">':'<strong class="schema-how-to-step-name">';return{blockStartOffset:e-=n.length,blockEndOffset:t-=n.length}})(t,o,s);t=e.blockStartOffset,o=e.blockEndOffset}if(n.slice(t,o)===i.slice(t,o))return[{startOffset:t,endOffset:o}];const r=((e,t,s)=>{const n=s.slice(0,e),i=s.slice(0,t),o=((e,t,s,n)=>{const i=[...e.matchAll(Ue)];s-=Ye(i);const o=[...t.matchAll(Ue)];return{blockStartOffset:s,blockEndOffset:n-=Ye(o)}})(n,i,e,t),r=((e,t,s,n)=>{let i=[...e.matchAll(je)];return(0,a.forEachRight)(i,(e=>{const[,t]=e;s-=t.length})),i=[...t.matchAll(je)],(0,a.forEachRight)(i,(e=>{const[,t]=e;n-=t.length})),{blockStartOffset:s,blockEndOffset:n}})(n,i,e=o.blockStartOffset,t=o.blockEndOffset);return{blockStartOffset:e=r.blockStartOffset,blockEndOffset:t=r.blockEndOffset}})(t,o,n);return[{startOffset:r.blockStartOffset,endOffset:r.blockEndOffset}]}return[]}(s,o,n.name,e,r):function(e,t){const s=t.getOriginal().replace(/(<([^>]+)>)/gi,""),n=t.getMarked().replace(/(<(?!\/?yoastmark)[^>]+>)/gi,""),i=function(e,t,s=!0){const n=[];if(0===e.length)return n;let i,o=0;for(s||(t=t.toLowerCase(),e=e.toLowerCase());(i=e.indexOf(t,o))>-1;)n.push(i),o=i+t.length;return n}(e,s);if(0===i.length)return[];const o=function(e){let t=e.indexOf(qe);const s=t>=0;s||(t=e.indexOf(ze));let n=null;const i=[];for(;t>=0;){if(n=(e=s?e.replace(qe,""):e.replace(ze,"")).indexOf(Ke),n<t)return[];e=e.replace(Ke,""),i.push({startOffset:t,endOffset:n}),t=s?e.indexOf(qe):e.indexOf(ze),n=null}return i}(n),a=[];return o.forEach((e=>{i.forEach((n=>{const i=n+e.startOffset;let o=n+e.endOffset;0===e.startOffset&&e.endOffset===t.getOriginal().length&&(o=n+s.length),a.push({startOffset:i,endOffset:o})}))})),a}(r,s),i?i.map((e=>({...e,block:o,richTextIdentifier:t}))):[]}))}const Ve=e=>e[0].toUpperCase()+e.slice(1),We=(e,t,s,n,i)=>(e=e.map((e=>{const o=`${e.id}-${i[0]}`,a=`${e.id}-${i[1]}`,r=Ve(i[0]),c=Ve(i[1]),l=e[`json${r}`],d=e[`json${c}`],{marksForFirstSection:p,marksForSecondSection:u}=((e,t)=>({marksForFirstSection:e.filter((e=>e.hasBlockPosition&&e.hasBlockPosition()?e.getBlockAttributeId()===t.id&&e.isMarkForFirstBlockSection():e)),marksForSecondSection:e.filter((e=>e.hasBlockPosition&&e.hasBlockPosition()?e.getBlockAttributeId()===t.id&&!e.isMarkForFirstBlockSection():e))}))(t,e),h=$e(l,o,s,n,p),g=$e(d,a,s,n,u);return h.concat(g)})),(0,a.flattenDeep)(e)),Qe="yoast";let He=[];const Ge={"core/paragraph":[{key:"content"}],"core/list":[{key:"values",multilineTag:"li",multilineWrapperTag:["ul","ol"]}],"core/list-item":[{key:"content"}],"core/heading":[{key:"content"}],"core/audio":[{key:"caption"}],"core/embed":[{key:"caption"}],"core/gallery":[{key:"caption"}],"core/image":[{key:"caption"}],"core/table":[{key:"caption"}],"core/video":[{key:"caption"}],"yoast/faq-block":[{key:"questions"}],"yoast/how-to-block":[{key:"steps"},{key:"jsonDescription"}]};function Je(){const e=He.shift();e&&((0,c.dispatch)("core/annotations").__experimentalAddAnnotation(e),Ze())}function Ze(){(0,a.isFunction)(window.requestIdleCallback)?window.requestIdleCallback(Je,{timeout:1e3}):setTimeout(Je,150)}const Xe=(e,t)=>{return(0,a.flatMap)((s=e.name,Ge.hasOwnProperty(s)?Ge[s]:[]),(s=>"yoast/faq-block"===e.name?((e,t,s)=>{const n=t.attributes[e.key];return 0===n.length?[]:We(n,s,e,t,["question","answer"])})(s,e,t):"yoast/how-to-block"===e.name?((e,t,s)=>{const n=t.attributes[e.key];if(n&&0===n.length)return[];const i=[];return"steps"===e.key&&i.push(We(n,s,e,t,["name","text"])),"jsonDescription"===e.key&&(s=s.filter((e=>e.hasBlockPosition&&e.hasBlockPosition()?!e.getBlockAttributeId():e)),i.push($e(n,"description",e,t,s))),(0,a.flattenDeep)(i)})(s,e,t):function(e,t,s){const n=e.key,i=((e,t)=>{const s=e.attributes[t];return"string"==typeof s?s:s.toString()})(t,n);return $e(i,n,e,t,s)}(s,e,t)));var s};function et(e,t){return(0,a.flatMap)(e,(e=>{const s=function(e){return e.innerBlocks.length>0}(e)?et(e.innerBlocks,t):[];return Xe(e,t).concat(s)}))}function tt(e){He=[],(0,c.dispatch)("core/annotations").__experimentalRemoveAnnotationsBySource(Qe);const t=Fe(e);if(0===e.length)return;let s=(0,c.select)("core/block-editor").getBlocks();var n;t.length>0&&(s=s.filter((e=>t.some((t=>"core/"+t===e.name))))),n=et(s,e),He=n.map((e=>({blockClientId:e.block,source:Qe,richTextIdentifier:e.richTextIdentifier,range:{start:e.startOffset,end:e.endOffset}}))),Ze()}function st(e,t){let s;$(K)&&((0,a.isUndefined)(s)&&(s=L(tinyMCE.get(K))),s(e,t)),(0,c.select)("core/block-editor")&&(0,a.isFunction)((0,c.select)("core/block-editor").getBlocks)&&(0,c.select)("core/annotations")&&(0,a.isFunction)((0,c.dispatch)("core/annotations").__experimentalAddAnnotation)&&(function(e,t){tinyMCE.editors.map((e=>L(e))).forEach((s=>s(e,t)))}(e,t),tt(t)),(0,de.doAction)("yoast.analysis.applyMarks",t)}var nt=jQuery;function it(e,t,s,n,i){this._scriptUrl=n,this._options={usedKeywords:t.keyword_usage,usedKeywordsPostTypes:t.keyword_usage_post_types,searchUrl:t.search_url,postUrl:t.post_edit_url},this._keywordUsage=t.keyword_usage,this._usedKeywordsPostTypes=t.keyword_usage_post_types,this._postID=nt("#post_ID, [name=tag_ID]").val(),this._taxonomy=nt("[name=taxonomy]").val()||"",this._nonce=i,this._ajaxAction=e,this._refreshAnalysis=s,this._initialized=!1}it.prototype.init=function(){const{worker:e}=window.YoastSEO.analysis;this.requestKeywordUsage=(0,a.debounce)(this.requestKeywordUsage.bind(this),500),e.loadScript(this._scriptUrl).then((()=>{e.sendMessage("initialize",this._options,"used-keywords-assessment")})).then((()=>{this._initialized=!0,(0,a.isEqual)(this._options.usedKeywords,this._keywordUsage)?this._refreshAnalysis():e.sendMessage("updateKeywordUsage",this._keywordUsage,"used-keywords-assessment").then((()=>this._refreshAnalysis()))})).catch((e=>console.error(e)))},it.prototype.setKeyword=function(e){(0,a.has)(this._keywordUsage,e)||this.requestKeywordUsage(e)},it.prototype.requestKeywordUsage=function(e){nt.post(ajaxurl,{action:this._ajaxAction,post_id:this._postID,keyword:e,taxonomy:this._taxonomy,nonce:this._nonce},this.updateKeywordUsage.bind(this,e),"json")},it.prototype.updateKeywordUsage=function(e,t){const{worker:s}=window.YoastSEO.analysis,n=t.keyword_usage,i=t.post_types;n&&(0,a.isArray)(n)&&(this._keywordUsage[e]=n,this._usedKeywordsPostTypes[e]=i,this._initialized&&s.sendMessage("updateKeywordUsage",{usedKeywords:this._keywordUsage,usedKeywordsPostTypes:this._usedKeywordsPostTypes},"used-keywords-assessment").then((()=>this._refreshAnalysis())))};const{refreshSnippetEditor:ot,updateData:at,setFocusKeyword:rt,setCornerstoneContent:ct,setMarkerStatus:lt,setReadabilityResults:dt,setSeoResultsForKeyword:pt}=l.actions;function ut(e,t,s){var n,i;const o=new Le;function r(){const e={slug:i.val()};window.YoastSEO.store.dispatch(at(e))}function l(e){(0,a.isUndefined)(e.seoAssessorPresenter)||(e.seoAssessorPresenter.render=function(){}),(0,a.isUndefined)(e.contentAssessorPresenter)||(e.contentAssessorPresenter.render=function(){},e.contentAssessorPresenter.renderIndividualRatings=function(){})}let d;function p(e,t){const s=d||"";d=e.getState().analysisData.snippet,!(0,M.isShallowEqualObjects)(s,d)&&t()}!function(){var d,u,h,g,m,y,w,f,b,_;g=jQuery(".term-description-wrap").find("td"),m=jQuery(".term-description-wrap").find("label"),y=g.find("textarea").val(),w=document.getElementById("wp-description-wrap"),f=g.find("p"),g.html(""),g.append(w).append(f),document.getElementById("description").value=y,m.replaceWith(m.html()),u=new Ne({store:t}),d={elementTarget:[z,"yoast_wpseo_focuskw","yoast_wpseo_metadesc","excerpt","editable-post-name","editable-post-name-full"],targets:(b={},Se()&&(b.output="does-not-really-exist-but-it-needs-something"),Ee()&&(b.contentOutput="also-does-not-really-exist-but-it-needs-something"),b),callbacks:{getData:u.getData.bind(u)},locale:wpseoScriptData.metabox.contentLocale,contentAnalysisActive:Ee(),keywordAnalysisActive:Se(),debouncedRefresh:!1,researcher:new window.yoast.Researcher.default},Se()&&(t.dispatch(rt(u.getKeyword())),d.callbacks.saveScores=u.saveScores.bind(u),d.callbacks.updatedKeywordsResults=function(e){const s=t.getState().focusKeyword;t.dispatch(pt(s,e)),t.dispatch(ot())}),Ee()&&(t.dispatch(lt("hidden")),d.callbacks.saveContentScore=u.saveContentScore.bind(u),d.callbacks.updatedContentResults=function(e){t.dispatch(dt(e)),t.dispatch(ot())}),h=ve(),(0,a.isUndefined)(h)||(0,a.isUndefined)(h.domain)||(d.translations=h),n=new A.App(d),window.YoastSEO=window.YoastSEO||{},window.YoastSEO.app=n,window.YoastSEO.store=t,window.YoastSEO.analysis={},window.YoastSEO.analysis.worker=function(){const e=(0,a.get)(window,["wpseoScriptData","analysis","worker","url"],"analysis-worker.js"),t=(0,A.createWorker)(e),s=(0,a.get)(window,["wpseoScriptData","analysis","worker","dependencies"],[]),n=[];for(const e in s){if(!Object.prototype.hasOwnProperty.call(s,e))continue;const t=window.document.getElementById(`${e}-js-translations`);if(!t)continue;const i=t.innerHTML.slice(214),o=i.indexOf(","),a=i.slice(0,o-1);try{const e=JSON.parse(i.slice(o+1,-4));n.push([a,e])}catch(t){console.warn(`Failed to parse translation data for ${e} to send to the Yoast SEO worker`);continue}}return t.postMessage({dependencies:s,translations:n}),new A.AnalysisWorkerWrapper(t)}(),window.YoastSEO.analysis.collectData=()=>function(e,t,s,n,i){const o=(0,a.cloneDeep)(t.getState());(0,a.merge)(o,s.getData());const r=e.getData();let c=null;i&&(c=i.getBlocks()||[],c=c.filter((e=>e.isValid)));const l={text:r.content,textTitle:r.title,keyword:o.focusKeyword,synonyms:o.synonyms,description:o.analysisData.snippet.description||o.snippetEditor.data.description,title:o.analysisData.snippet.title||o.snippetEditor.data.title,slug:o.snippetEditor.data.slug,permalink:o.settings.snippetEditor.baseUrl+o.snippetEditor.data.slug,wpBlocks:c,date:o.settings.snippetEditor.date};n.loaded&&(l.title=n._applyModifications("data_page_title",l.title),l.title=n._applyModifications("title",l.title),l.description=n._applyModifications("data_meta_desc",l.description),l.text=n._applyModifications("content",l.text),l.wpBlocks=n._applyModifications("wpBlocks",l.wpBlocks));const d=o.analysisData.snippet.filteredSEOTitle;return l.titleWidth=Ae(d||o.snippetEditor.data.title),l.locale=_e(),l.writingDirection=function(){let e="LTR";return be().isRtl&&(e="RTL"),e}(),l.shortcodes=window.wpseoScriptData.analysis.plugins.shortcodes?window.wpseoScriptData.analysis.plugins.shortcodes.wpseo_shortcode_tags:[],A.Paper.parse((0,de.applyFilters)("yoast.analysis.data",l))}(s,window.YoastSEO.store,o,window.YoastSEO.app.pluggable),window.YoastSEO.analysis.applyMarks=(e,t)=>function(){const e=(0,c.select)("yoast-seo/editor").isMarkingAvailable(),t=(0,c.select)("yoast-seo/editor").getMarkerPauseStatus();return!e||t?a.noop:st}()(e,t),window.YoastSEO.app.refresh=(0,a.debounce)((()=>Te(window.YoastSEO.analysis.worker,window.YoastSEO.analysis.collectData,window.YoastSEO.analysis.applyMarks,window.YoastSEO.store,u)),500),window.YoastSEO.app.registerCustomDataCallback=o.register,window.YoastSEO.app.pluggable=new H(window.YoastSEO.app.refresh),window.YoastSEO.app.registerPlugin=window.YoastSEO.app.pluggable._registerPlugin,window.YoastSEO.app.pluginReady=window.YoastSEO.app.pluggable._ready,window.YoastSEO.app.pluginReloaded=window.YoastSEO.app.pluggable._reloaded,window.YoastSEO.app.registerModification=window.YoastSEO.app.pluggable._registerModification,window.YoastSEO.app.registerAssessment=(e,t,s)=>{if(!(0,a.isUndefined)(n.seoAssessor))return window.YoastSEO.app.pluggable._registerAssessment(n.defaultSeoAssessor,e,t,s)&&window.YoastSEO.app.pluggable._registerAssessment(n.cornerStoneSeoAssessor,e,t,s)},window.YoastSEO.app.changeAssessorOptions=function(e){window.YoastSEO.analysis.worker.initialize(e).catch(Re),window.YoastSEO.app.refresh()},function(e,t,s){const n=be();if(!n.previouslyUsedKeywordActive)return;const i=new it("get_term_keyword_usage",n,e,(0,a.get)(window,["wpseoScriptData","analysis","worker","keywords_assessment_url"],"used-keywords-assessment.js"),(0,a.get)(window,["wpseoScriptData","usedKeywordsNonce"],""));i.init();let o={};s.subscribe((()=>{const e=s.getState()||{};e.focusKeyword!==o.focusKeyword&&(o=e,i.setKeyword(e.focusKeyword))}))}(n.refresh,0,t),t.subscribe(p.bind(null,t,n.refresh)),Se()&&(n.seoAssessor=new A.TaxonomyAssessor(n.config.researcher),n.seoAssessorPresenter.assessor=n.seoAssessor),window.YoastSEO.wp={},window.YoastSEO.wp.replaceVarsPlugin=new le(n,t),function(e,t){let s=[];s=(0,de.applyFilters)("yoast.analysis.shortcodes",s);const n=wpseoScriptData.analysis.plugins.shortcodes.wpseo_shortcode_tags;s=s.filter((e=>n.includes(e))),s.length>0&&(t.dispatch(ye(s)),window.YoastSEO.wp.shortcodePlugin=new ge({registerPlugin:e.registerPlugin,registerModification:e.registerModification,pluginReady:e.pluginReady,pluginReloaded:e.pluginReloaded},s))}(n,t),window.YoastSEO.analyzerArgs=d,(i=e("#slug")).on("change",r),u.bindElementEvents((0,a.debounce)((()=>Te(window.YoastSEO.analysis.worker,window.YoastSEO.analysis.collectData,window.YoastSEO.analysis.applyMarks,window.YoastSEO.store,u)),500)),Se()&&(we(_=Me(e("#hidden_wpseo_linkdex").val())),fe(_)),Ee()&&function(){var t=Me(e("#hidden_wpseo_content_score").val());we(t),fe(t)}(),ke()&&function(){const t=Me(e("#hidden_wpseo_inclusive_language_score").val());we(t),fe(t)}(),window.YoastSEO.analysis.worker.initialize(function(e={}){let t={locale:_e(),contentAnalysisActive:Ee(),keywordAnalysisActive:Se(),inclusiveLanguageAnalysisActive:ke(),defaultQueryParams:(0,a.get)(window,["wpseoAdminL10n","default_query_params"],{}),logLevel:(0,a.get)(window,["wpseoScriptData","analysis","worker","log_level"],"ERROR"),enabledFeatures:(0,xe.enabledFeatures)()};t=(0,a.merge)(t,e);const s=ve();return(0,a.isUndefined)(s)||(0,a.isUndefined)(s.domain)||(t.translations=s),t}({useTaxonomy:!0})).then((()=>{jQuery(window).trigger("YoastSEO:ready")})).catch(Re),l(n);const v=n.initAssessorPresenters.bind(n);n.initAssessorPresenters=function(){v(),l(n)};let E={title:(S=u).getSnippetTitle(),slug:S.getSnippetCite(),description:S.getSnippetMeta()};var S;!function(e){const s=document.getElementById("hidden_wpseo_is_cornerstone");let n="1"===s.value;t.dispatch(ct(n)),e.changeAssessorOptions({useCornerstone:n}),t.subscribe((()=>{const i=t.getState();i.isCornerstone!==n&&(n=i.isCornerstone,s.value=n?"1":"0",e.changeAssessorOptions({useCornerstone:n}))}))}(n);const k=function(e){const t={};if((0,a.isUndefined)(e))return t;t.title=e.title_template;const s=e.metadesc_template;return(0,a.isEmpty)(s)||(t.description=s),t}(wpseoScriptData.metabox);E=function(e,t){const s={...e};return(0,a.forEach)(t,((t,n)=>{(0,a.has)(e,n)&&""===e[n]&&(s[n]=t)})),s}(E,k),t.dispatch(at(E));let x=t.getState().focusKeyword;G(window.YoastSEO.analysis.worker.runResearch,window.YoastSEO.store,x);const R=(0,a.debounce)((()=>{n.refresh()}),50);t.subscribe((()=>{const e=t.getState().focusKeyword;x!==e&&(x=e,G(window.YoastSEO.analysis.worker.runResearch,window.YoastSEO.store,x),document.getElementById("hidden_wpseo_focuskw").value=x,R());const s=function(e){const t=e.getState().snippetEditor.data;return{title:t.title,slug:t.slug,description:t.description}}(t),n=function(e,t){const s={...e};return(0,a.forEach)(t,((t,n)=>{(0,a.has)(e,n)&&e[n].trim()===t&&(s[n]="")})),s}(s,k);E.title!==s.title&&u.setDataFromSnippet(n.title,"snippet_title"),E.slug!==s.slug&&u.setDataFromSnippet(n.slug,"snippet_cite"),E.description!==s.description&&u.setDataFromSnippet(n.description,"snippet_meta"),E.title=s.title,E.slug=s.slug,E.description=s.description})),Oe=!0,window.YoastSEO.app.refresh()}()}window.yoastHideMarkers=!0,window.YoastReplaceVarPlugin=le,window.YoastShortcodePlugin=me;let ht=null;const gt=()=>{if(null===ht){const e=(0,c.dispatch)("yoast-seo/editor").runAnalysis;ht=window.YoastSEO.app&&window.YoastSEO.app.pluggable?window.YoastSEO.app.pluggable:new H(e)}return ht},mt=(e,t,s)=>gt().loaded?gt()._applyModifications(e,t,s):t;function yt(){const{getAnalysisData:e,getEditorDataTitle:t}=(0,c.select)("yoast-seo/editor");let s=e();s={...s,textTitle:t()};const n=function(e){return e.title=mt("data_page_title",e.title),e.title=mt("title",e.title),e.description=mt("data_meta_desc",e.description),e.text=mt("content",e.text),e}(s);return(0,de.applyFilters)("yoast.analysis.data",n)}(0,a.debounce)((async function(e,t){const{text:s,...n}=t,i=new A.Paper(s,n);try{const t=await e.analyze(i),{seo:s,readability:n,inclusiveLanguage:o}=t.result;if(s){const e=s[""];e.results.forEach((e=>{e.getMarker=()=>()=>window.YoastSEO.analysis.applyMarks(i,e.marks)})),e.results=Pe(e.results),(0,c.dispatch)("yoast-seo/editor").setSeoResultsForKeyword(i.getKeyword(),e.results),(0,c.dispatch)("yoast-seo/editor").setOverallSeoScore(e.score,i.getKeyword())}n&&(n.results.forEach((e=>{e.getMarker=()=>()=>window.YoastSEO.analysis.applyMarks(i,e.marks)})),n.results=Pe(n.results),(0,c.dispatch)("yoast-seo/editor").setReadabilityResults(n.results),(0,c.dispatch)("yoast-seo/editor").setOverallReadabilityScore(n.score)),o&&(o.results.forEach((e=>{e.getMarker=()=>()=>window.YoastSEO.analysis.applyMarks(i,e.marks)})),o.results=Pe(o.results),(0,c.dispatch)("yoast-seo/editor").setInclusiveLanguageResults(o.results),(0,c.dispatch)("yoast-seo/editor").setOverallInclusiveLanguageScore(o.score)),(0,de.doAction)("yoast.analysis.run",t,{paper:i})}catch(e){}}),500);const wt=()=>{const{getContentLocale:e}=(0,c.select)("yoast-seo/editor"),t=((...e)=>()=>e.map((e=>e())))(e,yt),s=(()=>{const{setEstimatedReadingTime:e,setFleschReadingEase:t,setTextLength:s}=(0,c.dispatch)("yoast-seo/editor"),n=(0,a.get)(window,"YoastSEO.analysis.worker.runResearch",a.noop);return()=>{const i=A.Paper.parse(yt());n("readingTime",i).then((t=>e(t.result))),n("getFleschReadingScore",i).then((e=>{e.result&&t(e.result)})),n("wordCountInText",i).then((e=>s(e.result)))}})();return setTimeout(s,1500),((e,t)=>{let s=e();return()=>{const n=e();(0,a.isEqual)(n,s)||(s=n,t((0,a.clone)(n)))}})(t,s)},ft=window.React,bt=window.wp.components,_t=window.wp.element,vt=window.yoast.uiLibrary,Et=window.yoast.propTypes;var St=e.n(Et);St().string.isRequired;const kt=ft.forwardRef((function(e,t){return ft.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:2,stroke:"currentColor","aria-hidden":"true",ref:t},e),ft.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M8 11V7a4 4 0 118 0m-4 8v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2z"}))})),xt=ft.forwardRef((function(e,t){return ft.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true",ref:t},e),ft.createElement("path",{fillRule:"evenodd",d:"M12.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-2.293-2.293a1 1 0 010-1.414z",clipRule:"evenodd"}))})),Rt=({learnMoreLink:e,thumbnail:t,wistiaEmbedPermission:s,upsellLink:n,isProductCopy:i,title:o,upsellLabel:a,newToText:c,bundleNote:l})=>{const{onClose:d,initialFocus:p}=(0,vt.useModalContext)(),u={a:(0,ft.createElement)(Ct,{href:e,className:"yst-inline-flex yst-items-center yst-gap-1 yst-no-underline yst-font-medium",variant:"primary"}),ArrowNarrowRightIcon:(0,ft.createElement)(xt,{className:"yst-w-4 yst-h-4 rtl:yst-rotate-180"})};return(0,ft.createElement)("div",{className:"yst-flex yst-flex-col yst-items-center yst-p-10"},(0,ft.createElement)("div",{className:"yst-relative yst-w-full"},(0,ft.createElement)(Zt,{videoId:"vmrahpfjxp",thumbnail:t,wistiaEmbedPermission:s}),(0,ft.createElement)(vt.Badge,{className:"yst-absolute yst-top-0 yst-right-2 yst-mt-2 yst-ml-2",variant:"info"},"Beta")),(0,ft.createElement)("div",{className:"yst-mt-6 yst-text-xs yst-font-medium"},(0,ft.createElement)("span",{className:"yst-introduction-modal-uppercase"},c)),(0,ft.createElement)("div",{className:"yst-mt-4 yst-mx-1.5 yst-text-center"},(0,ft.createElement)("h3",{className:"yst-text-slate-900 yst-text-lg yst-font-medium"},o),(0,ft.createElement)("div",{className:"yst-mt-2 yst-text-slate-600 yst-text-sm"},i?(0,_t.createInterpolateElement)((0,r.sprintf)(/* translators: %1$s and %2$s are anchor tags; %3$s is the arrow icon. */
(0,r.__)("Let AI do some of the thinking for you and help you save time. Get high-quality suggestions for product titles and meta descriptions to make your content rank high and look good on social media. %1$sLearn more%2$s%3$s","wordpress-seo"),"<a>","<ArrowNarrowRightIcon />","</a>"),u):(0,_t.createInterpolateElement)((0,r.sprintf)(/* translators: %1$s and %2$s are anchor tags; %3$s is the arrow icon. */
(0,r.__)("Let AI do some of the thinking for you and help you save time. Get high-quality suggestions for titles and meta descriptions to make your content rank high and look good on social media. %1$sLearn more%2$s%3$s","wordpress-seo"),"<a>","<ArrowNarrowRightIcon />","</a>"),u))),(0,ft.createElement)("div",{className:"yst-w-full yst-flex yst-mt-10"},(0,ft.createElement)(vt.Button,{as:"a",className:"yst-grow",size:"extra-large",variant:"upsell",href:n,target:"_blank",ref:p},(0,ft.createElement)(kt,{className:"yst--ml-1 yst-mr-2 yst-h-5 yst-w-5"}),a,(0,ft.createElement)("span",{className:"yst-sr-only"},/* translators: Hidden accessibility text. */
(0,r.__)("(Opens in a new browser tab)","wordpress-seo")))),l,(0,ft.createElement)(vt.Button,{as:"a",className:"yst-mt-4",variant:"tertiary",onClick:d},(0,r.__)("Close","wordpress-seo")))};Rt.propTypes={learnMoreLink:St().string.isRequired,upsellLink:St().string.isRequired,thumbnail:St().shape({src:St().string.isRequired,width:St().string,height:St().string}).isRequired,wistiaEmbedPermission:St().shape({value:St().bool.isRequired,status:St().string.isRequired,set:St().func.isRequired}).isRequired,title:St().string,upsellLabel:St().string,newToText:St().string,isProductCopy:St().bool,bundleNote:St().oneOfType([St().string,St().element])},Rt.defaultProps={title:(0,r.__)("Use AI to write your titles & meta descriptions!","wordpress-seo"),upsellLabel:(0,r.sprintf)(/* translators: %1$s expands to Yoast SEO Premium. */
(0,r.__)("Unlock with %1$s","wordpress-seo"),"Yoast SEO Premium"),newToText:(0,r.sprintf)(/* translators: %1$s expands to Yoast SEO Premium. */
(0,r.__)("New in %1$s","wordpress-seo"),"Yoast SEO Premium"),isProductCopy:!1,bundleNote:""};var Ot;function Pt(){return Pt=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var s=arguments[t];for(var n in s)Object.prototype.hasOwnProperty.call(s,n)&&(e[n]=s[n])}return e},Pt.apply(this,arguments)}St().string,St().node.isRequired,St().node.isRequired,St().node,St().oneOf(Object.keys({lg:{grid:"yst-grid lg:yst-grid-cols-3 lg:yst-gap-12",col1:"yst-col-span-1",col2:"lg:yst-mt-0 lg:yst-col-span-2"},xl:{grid:"yst-grid xl:yst-grid-cols-3 xl:yst-gap-12",col1:"yst-col-span-1",col2:"xl:yst-mt-0 xl:yst-col-span-2"},"2xl":{grid:"yst-grid 2xl:yst-grid-cols-3 2xl:yst-gap-12",col1:"yst-col-span-1",col2:"2xl:yst-mt-0 2xl:yst-col-span-2"}}));const Tt=e=>ft.createElement("svg",Pt({xmlns:"http://www.w3.org/2000/svg","aria-hidden":"true",viewBox:"0 0 1000 1000"},e),Ot||(Ot=ft.createElement("path",{fill:"#fff",d:"M500 0C223.9 0 0 223.9 0 500s223.9 500 500 500 500-223.9 500-500S776.1 0 500 0Zm87.2 412.4c0-21.9 4.3-40.2 13.1-54.4s24-27.1 45.9-38.2l10.1-4.9c17.8-9 22.4-16.7 22.4-26 0-11.1-9.5-19.1-25-19.1-18.3 0-32.2 9.5-41.8 28.9l-24.7-24.8c5.4-11.6 14.1-20.9 25.8-28.1a70.8 70.8 0 0 1 38.9-11.1c17.8 0 33.3 4.6 45.9 14.2s19.4 22.7 19.4 39.4c0 26.6-15 42.9-43.1 57.3l-15.7 8c-16.8 8.5-25.1 16-27.4 29.4h85.4v35.4H587.2Zm-82.1 373.3c-157.8 0-285.7-127.9-285.7-285.7s127.9-285.7 285.7-285.7a286.4 286.4 0 0 1 55.9 5.5l-55.9 116.9c-90 0-163.3 73.3-163.3 163.3s73.3 163.3 163.3 163.3a162.8 162.8 0 0 0 106.4-39.6l61.8 107.2a283.9 283.9 0 0 1-168.2 54.8ZM705 704.1l-70.7-122.5H492.9l70.7-122.4H705l70.7 122.4Z"}))),Ct=({href:e,children:t,...s})=>(0,ft.createElement)(vt.Link,{target:"_blank",rel:"noopener noreferrer",...s,href:e},t,(0,ft.createElement)("span",{className:"yst-sr-only"},/* translators: Hidden accessibility text. */
(0,r.__)("(Opens in a new browser tab)","wordpress-seo")));Ct.propTypes={href:St().string.isRequired,children:St().node},Ct.defaultProps={children:null};const At=ft.forwardRef((function(e,t){return ft.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:2,stroke:"currentColor","aria-hidden":"true",ref:t},e),ft.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M17 8l4 4m0 0l-4 4m4-4H3"}))}));var Mt,It,Dt;function Nt(){return Nt=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var s=arguments[t];for(var n in s)Object.prototype.hasOwnProperty.call(s,n)&&(e[n]=s[n])}return e},Nt.apply(this,arguments)}const Lt=e=>ft.createElement("svg",Nt({xmlns:"http://www.w3.org/2000/svg",id:"star-rating-half_svg__Layer_1","data-name":"Layer 1",viewBox:"0 0 500 475.53"},e),Mt||(Mt=ft.createElement("defs",null,ft.createElement("style",null,".star-rating-half_svg__cls-1{fill:#fbbf24}"))),It||(It=ft.createElement("path",{d:"M250 392.04 98.15 471.87l29-169.09L4.3 183.03l169.77-24.67L250 4.52l75.93 153.84 169.77 24.67-122.85 119.75 29 169.09L250 392.04z",className:"star-rating-half_svg__cls-1"})),Dt||(Dt=ft.createElement("path",{d:"m250 9.04 73.67 149.27.93 1.88 2.08.3 164.72 23.94-119.19 116.19-1.51 1.47.36 2.07 28.14 164.06-147.34-77.46-1.86-1-1.86 1-147.34 77.46 28.14-164.06.36-2.07-1.51-1.47L8.6 184.43l164.72-23.9 2.08-.3.93-1.88L250 9.04m0-9-77.25 156.49L0 181.64l125 121.89-29.51 172L250 394.3l154.51 81.23-29.51-172 125-121.89-172.75-25.11L250 0Z",className:"star-rating-half_svg__cls-1"})),ft.createElement("path",{d:"m500 181.64-172.75-25.11L250 0v394.3l154.51 81.23L375 303.48l125-121.84z",style:{fill:"#f3f4f6"}}));function Ft(){return Ft=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var s=arguments[t];for(var n in s)Object.prototype.hasOwnProperty.call(s,n)&&(e[n]=s[n])}return e},Ft.apply(this,arguments)}const Bt=e=>ft.createElement("svg",Ft({xmlns:"http://www.w3.org/2000/svg","data-name":"Layer 1",viewBox:"0 0 500 475.53"},e),ft.createElement("path",{d:"m250 0 77.25 156.53L500 181.64 375 303.48l29.51 172.05L250 394.3 95.49 475.53 125 303.48 0 181.64l172.75-25.11L250 0z",style:{fill:"#fbbf24"}}));var Ut,jt,Yt,qt,Kt,zt,$t,Vt,Wt;function Qt(){return Qt=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var s=arguments[t];for(var n in s)Object.prototype.hasOwnProperty.call(s,n)&&(e[n]=s[n])}return e},Qt.apply(this,arguments)}const Ht=e=>ft.createElement("svg",Qt({xmlns:"http://www.w3.org/2000/svg","aria-hidden":"true",viewBox:"0 0 500 500"},e),Ut||(Ut=ft.createElement("path",{fill:"#a4286a",d:"M80 0h340a80 80 0 0 1 80 80v420H80a80 80 0 0 1-80-80V80A80 80 0 0 1 80 0z"})),jt||(jt=ft.createElement("path",{fill:"#6c2548",d:"M437.61 2 155.89 500H500V80a80 80 0 0 0-62.39-78z"})),Yt||(Yt=ft.createElement("path",{fill:"#fff",d:"M74.4 337.3v34.9c21.6-.9 38.5-8 52.8-22.5s27.4-38 39.9-72.9l92.6-248h-44.8L140.3 236l-37-116.2h-41l54.4 139.8a57.54 57.54 0 0 1 0 41.8c-5.5 14.2-15.4 30.9-42.3 35.9z"})),qt||(qt=ft.createElement("circle",{cx:368.33,cy:124.68,r:97.34,fill:"#9fda4f",transform:"rotate(-45 368.335 124.68)"})),Kt||(Kt=ft.createElement("path",{fill:"#77b227",d:"m416.2 39.93-95.74 169.51A97.34 97.34 0 1 0 416.2 39.93z"})),zt||(zt=ft.createElement("path",{fill:"#fec228",d:"m294.78 254.75-.15-.08-.13-.07a63.6 63.6 0 0 0-62.56 110.76h.13a63.6 63.6 0 0 0 62.71-110.67z"})),$t||($t=ft.createElement("path",{fill:"#f49a00",d:"m294.5 254.59-62.56 110.76a63.6 63.6 0 1 0 62.56-110.76z"})),Vt||(Vt=ft.createElement("path",{fill:"#ff4e47",d:"M222.31 450.07A38.16 38.16 0 0 0 203 416.83a38.18 38.18 0 1 0 19.41 33.27z"})),Wt||(Wt=ft.createElement("path",{fill:"#ed261f",d:"m202.9 416.8-37.54 66.48a38.17 38.17 0 0 0 37.54-66.48z"}))),Gt=({link:e,linkProps:t,promotions:s})=>{const n=(0,_t.useMemo)((()=>(0,r.__)("Use AI to generate titles and meta descriptions, automatically redirect deleted pages, get 24/7 support, and much, much more!","wordpress-seo")),[]),i=(0,_t.createInterpolateElement)((0,r.sprintf)(/* translators: %1$s and %2$s expand to a span wrap to avoid linebreaks. %3$s expands to "Yoast SEO Premium". */
(0,r.__)("%1$sGet%2$s %3$s","wordpress-seo"),"<nowrap>","</nowrap>","Yoast SEO Premium"),{nowrap:(0,ft.createElement)("span",{className:"yst-whitespace-nowrap"})}),o=s.includes("black-friday-2023-promotion"),a=(0,_t.createInterpolateElement)((0,r.sprintf)(/* translators: %1$s and %2$s expand to strong tags. */
(0,r.__)("%1$sSAVE 30%%%2$s on your 12 month subscription","wordpress-seo"),"<strong>","</strong>"),{strong:(0,ft.createElement)("strong",null)});return(0,ft.createElement)("div",{className:"yst-p-6 yst-rounded-lg yst-text-white yst-bg-primary-500 yst-shadow"},(0,ft.createElement)("figure",{className:"yst-logo-square yst-w-16 yst-h-16 yst-mx-auto yst-overflow-hidden yst-border yst-border-white yst-rounded-xl yst-rounded-br-none yst-relative yst-z-10 yst-mt-[-2.6rem]"},(0,ft.createElement)(Ht,null)),o&&(0,ft.createElement)("div",{className:"sidebar__sale_banner_container"},(0,ft.createElement)("div",{className:"sidebar__sale_banner"},(0,ft.createElement)("span",{className:"banner_text"},(0,r.__)("BLACK FRIDAY - 30% OFF","wordpress-seo")))),(0,ft.createElement)(vt.Title,{as:"h2",className:"yst-mt-6 yst-text-base yst-font-extrabold yst-text-white"},i),(0,ft.createElement)("p",{className:"yst-mt-2"},n),o&&(0,ft.createElement)("div",{className:"yst-text-center yst-border-t-[1px] yst-border-white yst-italic yst-mt-3"},(0,ft.createElement)("p",{className:"yst-text-[10px] yst-my-3 yst-mx-0"},a)),(0,ft.createElement)(vt.Button,{as:"a",variant:"upsell",href:e,target:"_blank",rel:"noopener",className:"yst-flex yst-justify-center yst-gap-2 yst-mt-4 focus:yst-ring-offset-primary-500",...t},(0,ft.createElement)("span",null,o?(0,r.__)("Claim your 30% off now!","wordpress-seo"):i),(0,ft.createElement)(At,{className:"yst-w-4 yst-h-4 yst-icon-rtl"})),(0,ft.createElement)("p",{className:"yst-text-center yst-text-xs yst-mx-2 yst-font-light yst-leading-5 yst-mt-2"},(0,r.__)("Only $/€/£99 per year (ex VAT)","wordpress-seo"),(0,ft.createElement)("br",null),(0,r.__)("30-day money back guarantee.","wordpress-seo")),(0,ft.createElement)("hr",{className:"yst-border-t yst-border-primary-300 yst-my-4"}),(0,ft.createElement)("a",{className:"yst-block yst-mt-4 yst-no-underline",href:"https://www.g2.com/products/yoast-yoast/reviews",target:"_blank",rel:"noopener noreferrer"},(0,ft.createElement)("span",{className:"yst-font-medium yst-text-white hover:yst-underline"},(0,r.__)("Read reviews from real users","wordpress-seo")),(0,ft.createElement)("span",{className:"yst-flex yst-gap-2 yst-mt-2 yst-items-center"},(0,ft.createElement)(Tt,{className:"yst-w-5 yst-h-5"}),(0,ft.createElement)("span",{className:"yst-flex yst-gap-1"},(0,ft.createElement)(Bt,{className:"yst-w-5 yst-h-5"}),(0,ft.createElement)(Bt,{className:"yst-w-5 yst-h-5"}),(0,ft.createElement)(Bt,{className:"yst-w-5 yst-h-5"}),(0,ft.createElement)(Bt,{className:"yst-w-5 yst-h-5"}),(0,ft.createElement)(Lt,{className:"yst-w-5 yst-h-5"})),(0,ft.createElement)("span",{className:"yst-text-sm yst-font-semibold yst-text-white"},"4.6 / 5"))))};Gt.propTypes={link:St().string.isRequired,linkProps:St().object,promotions:St().array},Gt.defaultProps={linkProps:{},promotions:[]},St().node.isRequired;const Jt=window.yoast.reactHelmet,Zt=({videoId:e,thumbnail:t,wistiaEmbedPermission:s})=>{const[n,i]=(0,_t.useState)(s.value?k:E),o=(0,_t.useCallback)((()=>i(k)),[i]),a=(0,_t.useCallback)((()=>{s.value?o():i(S)}),[s.value,o,i]),c=(0,_t.useCallback)((()=>i(E)),[i]),l=(0,_t.useCallback)((()=>{s.set(!0),o()}),[s.set,o]);return(0,ft.createElement)(ft.Fragment,null,s.value&&(0,ft.createElement)(Jt.Helmet,null,(0,ft.createElement)("script",{src:"https://fast.wistia.com/assets/external/E-v1.js",async:!0})),(0,ft.createElement)("div",{className:"yst-relative yst-w-full yst-h-0 yst-pt-[56.25%] yst-overflow-hidden yst-rounded-md yst-drop-shadow-md yst-bg-white"},n===E&&(0,ft.createElement)("button",{type:"button",className:"yst-absolute yst-inset-0 yst-button yst-p-0 yst-border-none yst-bg-white yst-transition-opacity yst-duration-1000 yst-opacity-100",onClick:a},(0,ft.createElement)("img",{className:"yst-w-full yst-h-auto",alt:"",loading:"lazy",decoding:"async",...t})),n===S&&(0,ft.createElement)("div",{className:"yst-absolute yst-inset-0 yst-flex yst-flex-col yst-items-center yst-justify-center yst-bg-white"},(0,ft.createElement)("p",{className:"yst-max-w-xs yst-mx-auto yst-text-center"},s.status===v&&(0,ft.createElement)(vt.Spinner,null),s.status!==v&&(0,r.sprintf)(/* translators: %1$s expands to Yoast SEO. %2$s expands to Wistia. */
(0,r.__)("To see this video, you need to allow %1$s to load embedded videos from %2$s.","wordpress-seo"),"Yoast SEO","Wistia")),(0,ft.createElement)("div",{className:"yst-flex yst-mt-6 yst-gap-x-4"},(0,ft.createElement)(vt.Button,{type:"button",variant:"secondary",onClick:c,disabled:s.status===v},(0,r.__)("Deny","wordpress-seo")),(0,ft.createElement)(vt.Button,{type:"button",variant:"primary",onClick:l,disabled:s.status===v},(0,r.__)("Allow","wordpress-seo")))),s.value&&n===k&&(0,ft.createElement)("div",{className:"yst-absolute yst-w-full yst-h-full yst-top-0 yst-left-0"},null===e&&(0,ft.createElement)(vt.Spinner,{className:"yst-h-full yst-mx-auto"}),null!==e&&(0,ft.createElement)("div",{className:`wistia_embed wistia_async_${e} videoFoam=true`}))))};Zt.propTypes={videoId:St().string.isRequired,thumbnail:St().shape({src:St().string.isRequired,width:St().string,height:St().string}).isRequired,wistiaEmbedPermission:St().shape({value:St().bool.isRequired,status:St().string.isRequired,set:St().func.isRequired}).isRequired};const Xt="yoast-seo/editor",es=()=>{const e=(0,c.useSelect)((e=>e(Xt).selectLink("https://yoa.st/ai-generator-learn-more")),[]),t=(0,c.useSelect)((e=>e(Xt).selectLink("https://yoa.st/ai-generator-upsell")),[]),s=(0,c.useSelect)((e=>e(Xt).selectLink("https://yoa.st/ai-generator-upsell-woo-seo-premium-bundle")),[]),n=(0,c.useSelect)((e=>e(Xt).selectLink("https://yoa.st/ai-generator-upsell-woo-seo")),[]),i=(0,c.useSelect)((e=>e(Xt).getIsPremium()),[]),o=(0,c.useSelect)((e=>e(Xt).getIsWooSeoUpsell()),[]),a=(0,c.useSelect)((e=>e(Xt).getIsProduct()),[]),l=!(!o&&(!a||o||i)),d={isProductCopy:l,upsellLink:t};if(l){const e=(0,r.sprintf)(/* translators: %1$s expands to Yoast SEO Premium, %2$s expands to Yoast WooCommerce SEO. */
(0,r.__)("%1$s + %2$s","wordpress-seo"),"Yoast SEO Premium","Yoast WooCommerce SEO");d.newToText=(0,r.sprintf)(/* translators: %1$s expands to Yoast SEO Premium and Yoast WooCommerce SEO. */
(0,r.__)("New in %1$s","wordpress-seo"),e),d.title=(0,r.__)("Generate product titles & descriptions with AI!","wordpress-seo"),!i&&o&&(d.upsellLabel=`${(0,r.sprintf)(/* translators: %1$s expands to Woo Premium bundle. */
(0,r.__)("Unlock with the %1$s","wordpress-seo"),"Woo Premium bundle")}*`,d.bundleNote=(0,ft.createElement)("div",{className:"yst-text-xs yst-text-slate-500 yst-mt-2"},`*${e}`),d.upsellLink=s),i&&(d.upsellLabel=(0,r.sprintf)(/* translators: %1$s expands to Yoast WooCommerce SEO. */
(0,r.__)("Unlock with %1$s","wordpress-seo"),"Yoast WooCommerce SEO"),d.upsellLink=n)}const p=(0,c.useSelect)((e=>e(Xt).selectImageLink("ai-generator-preview.png")),[]),u=(0,_t.useMemo)((()=>({src:p,width:"432",height:"244"})),[p]),h=(0,c.useSelect)((e=>e(Xt).selectWistiaEmbedPermissionValue()),[]),g=(0,c.useSelect)((e=>e(Xt).selectWistiaEmbedPermissionStatus()),[]),{setWistiaEmbedPermission:m}=(0,c.useDispatch)(Xt),y=(0,_t.useMemo)((()=>({value:h,status:g,set:m})),[h,g,m]);return(0,ft.createElement)(Rt,{learnMoreLink:e,thumbnail:u,wistiaEmbedPermission:y,...d})},ts=({fieldId:e})=>{const[t,,,s,n]=(0,vt.useToggleState)(!1),i=(0,_t.useCallback)((()=>{s()}),[s]),o=(0,_t.useRef)(null);return(0,ft.createElement)(ft.Fragment,null,(0,ft.createElement)("button",{type:"button",id:`yst-replacevar__use-ai-button__${e}`,className:"yst-replacevar__use-ai-button-upsell",onClick:i},(0,r.__)("Use AI","wordpress-seo")),(0,ft.createElement)(vt.Modal,{className:"yst-introduction-modal",isOpen:t,onClose:n,initialFocus:o},(0,ft.createElement)(vt.Modal.Panel,{className:"yst-max-w-lg yst-p-0 yst-rounded-3xl yst-introduction-modal-panel"},(0,ft.createElement)(es,{onClose:n,focusElementRef:o}))))};ts.propTypes={fieldId:St().string.isRequired};const ss="yoast-seo/editor";n()((()=>{window.wpseoTermScraperL10n=window.wpseoScriptData.metabox,function(e){function t(){e("#copy-home-meta-description").on("click",(function(){e("#open_graph_frontpage_desc").val(e("#meta_description").val())}))}function s(){var t=e("#wpseo-conf");if(t.length){var s=t.attr("action").split("#")[0];t.attr("action",s+window.location.hash)}}function n(){var t=window.location.hash.replace("#top#","");-1!==t.search("#top")&&(t=window.location.hash.replace("#top%23","")),""!==t&&"#"!==t.charAt(0)||(t=e(".wpseotab").attr("id")),e("#"+t).addClass("active"),e("#"+t+"-tab").addClass("nav-tab-active").trigger("click")}function i(t){const s=e("#noindex-author-noposts-wpseo-container");t?s.show():s.hide()}e.fn._wpseoIsInViewport=function(){const t=e(this).offset().top,s=t+e(this).outerHeight(),n=e(window).scrollTop(),i=n+e(window).height();return t>n&&s<i},e(window).on("hashchange",(function(){n(),s()})),window.setWPOption=function(t,s,n,i){e.post(ajaxurl,{action:"wpseo_set_option",option:t,newval:s,_wpnonce:i},(function(t){t&&e("#"+n).hide()}))},window.wpseoCopyHomeMeta=t,window.wpseoSetTabHash=s,e(document).ready((function(){s(),"function"==typeof window.wpseoRedirectOldFeaturesTabToNewSettings&&window.wpseoRedirectOldFeaturesTabToNewSettings(),e("#disable-author input[type='radio']").on("change",(function(){e(this).is(":checked")&&e("#author-archives-titles-metas-content").toggle("off"===e(this).val())})).trigger("change");const o=e("#noindex-author-wpseo-off"),c=e("#noindex-author-wpseo-on");o.is(":checked")||i(!1),c.on("change",(()=>{e(this).is(":checked")||i(!1)})),o.on("change",(()=>{e(this).is(":checked")||i(!0)})),e("#disable-date input[type='radio']").on("change",(function(){e(this).is(":checked")&&e("#date-archives-titles-metas-content").toggle("off"===e(this).val())})).trigger("change"),e("#disable-attachment input[type='radio']").on("change",(function(){e(this).is(":checked")&&e("#media_settings").toggle("off"===e(this).val())})).trigger("change"),e("#disable-post_format").on("change",(function(){e("#post_format-titles-metas").toggle(e(this).is(":not(:checked)"))})).trigger("change"),e("#zapier_integration_active input[type='radio']").on("change",(function(){e(this).is(":checked")&&e("#zapier-connection").toggle("on"===e(this).val())})).trigger("change"),e("#wpseo-tabs").find("a").on("click",(function(t){var s,n,i,o=!0;if(s=e(this),n=!!e("#first-time-configuration-tab").filter(".nav-tab-active").length,i=!!s.filter("#first-time-configuration-tab").length,n&&!i&&window.isStepBeingEdited&&(o=confirm((0,r.__)("There are unsaved changes in one or more steps. Leaving means that those changes may not be saved. Are you sure you want to leave?","wordpress-seo"))),o){window.isStepBeingEdited=!1,e("#wpseo-tabs").find("a").removeClass("nav-tab-active"),e(".wpseotab").removeClass("active");var a=e(this).attr("id").replace("-tab",""),c=e("#"+a);c.addClass("active"),e(this).addClass("nav-tab-active"),c.hasClass("nosave")?e("#wpseo-submit-container").hide():e("#wpseo-submit-container").show(),e(window).trigger("yoast-seo-tab-change"),"first-time-configuration"===a?(e(".notice-yoast").slideUp(),e(".yoast_premium_upsell").slideUp(),e("#sidebar-container").hide()):(e(".notice-yoast").slideDown(),e(".yoast_premium_upsell").slideDown(),e("#sidebar-container").show())}else t.preventDefault(),e("#first-time-configuration-tab").trigger("focus")})),e("#yoast-first-time-configuration-notice a").on("click",(function(){e("#first-time-configuration-tab").click()})),e("#company_or_person").on("change",(function(){var t=e(this).val();"company"===t?(e("#knowledge-graph-company").show(),e("#knowledge-graph-person").hide()):"person"===t?(e("#knowledge-graph-company").hide(),e("#knowledge-graph-person").show()):(e("#knowledge-graph-company").hide(),e("#knowledge-graph-person").hide())})).trigger("change"),e(".switch-yoast-seo input").on("keydown",(function(e){"keydown"===e.type&&13===e.which&&e.preventDefault()})),e("body").on("click","button.toggleable-container-trigger",(t=>{const s=e(t.currentTarget),n=s.parent().siblings(".toggleable-container");n.toggleClass("toggleable-container-hidden"),s.attr("aria-expanded",!n.hasClass("toggleable-container-hidden")).find("span").toggleClass("dashicons-arrow-up-alt2 dashicons-arrow-down-alt2")}));const l=e("#opengraph"),d=e("#wpseo-opengraph-settings");l.length&&d.length&&(d.toggle(l[0].checked),l.on("change",(e=>{d.toggle(e.target.checked)}))),t(),n(),function(){if(!e("#enable_xml_sitemap input[type=radio]").length)return;const t=e("#yoast-seo-sitemaps-disabled-warning");e("#enable_xml_sitemap input[type=radio]").on("change",(function(){"off"===this.value?t.show():t.hide()}))}(),function(){const t=e("#wpseo-submit-container-float"),s=e("#wpseo-submit-container-fixed");if(!t.length||!s.length)return;function n(){t._wpseoIsInViewport()?s.hide():s.show()}e(window).on("resize scroll",(0,a.debounce)(n,100)),e(window).on("yoast-seo-tab-change",n);const i=e(".wpseo-message");i.length&&window.setTimeout((()=>{i.fadeOut()}),5e3),n()}(),"undefined"!=typeof ClipboardJS&&new ClipboardJS("#copy-zapier-api-key").on("success",(function(t){t.clearSelection(),e(t.trigger).trigger("focus")}))}))}(o()),function(e){function t(e){e&&(e.focus(),e.click())}function s(){if(e(".wpseo-meta-section").length>0){const t=e(".wpseo-meta-section-link");e(".wpseo-metabox-menu li").filter((function(){return"#wpseo-meta-section-content"===e(this).find(".wpseo-meta-section-link").attr("href")})).addClass("active").find("[role='tab']").addClass("yoast-active-tab"),e("#wpseo-meta-section-content, .wpseo-meta-section-react").addClass("active"),t.on("click",(function(s){var n=e(this).attr("id"),i=e(this).attr("href"),o=e(i);s.preventDefault(),e(".wpseo-metabox-menu li").removeClass("active").find("[role='tab']").removeClass("yoast-active-tab"),e(".wpseo-meta-section").removeClass("active"),e(".wpseo-meta-section-react.active").removeClass("active"),"#wpseo-meta-section-content"===i&&e(".wpseo-meta-section-react").addClass("active"),o.addClass("active"),e(this).parent("li").addClass("active").find("[role='tab']").addClass("yoast-active-tab");const a=function(e,t={}){return new CustomEvent("YoastSEO:metaTabChange",{detail:t})}(0,{metaTabId:n});window.dispatchEvent(a),this&&(t.attr({"aria-selected":"false",tabIndex:"-1"}),this.removeAttribute("tabindex"),this.setAttribute("aria-selected","true"))}))}}window.wpseoInitTabs=s,window.wpseo_init_tabs=s,e(".wpseo-meta-section").each((function(t,s){e(s).find(".wpseotab:first").addClass("active")})),window.wpseo_init_tabs(),function(){const s=e(".yoast-aria-tabs"),n=s.find("[role='tab']"),i=s.attr("aria-orientation")||"horizontal";n.attr({"aria-selected":!1,tabIndex:"-1"}),n.filter(".yoast-active-tab").removeAttr("tabindex").attr("aria-selected","true"),n.on("keydown",(function(s){-1!==[32,35,36,37,38,39,40].indexOf(s.which)&&("horizontal"===i&&-1!==[38,40].indexOf(s.which)||"vertical"===i&&-1!==[37,39].indexOf(s.which)||function(s,n){const i=s.which,o=n.index(e(s.target));switch(i){case 32:s.preventDefault(),t(n[o]);break;case 35:s.preventDefault(),t(n[n.length-1]);break;case 36:s.preventDefault(),t(n[0]);break;case 37:case 38:s.preventDefault(),t(n[o-1<0?n.length-1:o-1]);break;case 39:case 40:s.preventDefault(),t(n[o+1===n.length?0:o+1])}}(s,n))}))}()}(o());const e=function(){const e=(0,c.registerStore)("yoast-seo/editor",{reducer:(0,c.combineReducers)(l.reducers),selectors:l.selectors,actions:(0,a.pickBy)(l.actions,(e=>"function"==typeof e)),controls:t});return(e=>{e.dispatch(l.actions.setSettings({socialPreviews:{sitewideImage:window.wpseoScriptData.metabox.sitewide_social_image,siteName:window.wpseoScriptData.metabox.site_name,contentImage:window.wpseoScriptData.metabox.first_content_image,twitterCardType:window.wpseoScriptData.metabox.twitterCardType},snippetEditor:{baseUrl:window.wpseoScriptData.metabox.base_url,date:window.wpseoScriptData.metabox.metaDescriptionDate,recommendedReplacementVariables:window.wpseoScriptData.analysis.plugins.replaceVars.recommended_replace_vars,siteIconUrl:window.wpseoScriptData.metabox.siteIconUrl}})),e.dispatch(l.actions.setSEMrushChangeCountry(window.wpseoScriptData.metabox.countryCode)),e.dispatch(l.actions.setSEMrushLoginStatus(window.wpseoScriptData.metabox.SEMrushLoginStatus)),e.dispatch(l.actions.setWincherLoginStatus(window.wpseoScriptData.metabox.wincherLoginStatus,!1)),e.dispatch(l.actions.setWincherWebsiteId(window.wpseoScriptData.metabox.wincherWebsiteId)),e.dispatch(l.actions.setWincherAutomaticKeyphaseTracking(window.wpseoScriptData.metabox.wincherAutoAddKeyphrases)),e.dispatch(l.actions.setDismissedAlerts((0,a.get)(window,"wpseoScriptData.dismissedAlerts",{}))),e.dispatch(l.actions.setCurrentPromotions((0,a.get)(window,"wpseoScriptData.currentPromotions",[]))),e.dispatch(l.actions.setIsPremium(Boolean((0,a.get)(window,"wpseoScriptData.metabox.isPremium",!1)))),e.dispatch(l.actions.setPostId(Number((0,a.get)(window,"wpseoScriptData.postId",null)))),e.dispatch(l.actions.setLinkParams((0,a.get)(window,"wpseoScriptData.linkParams",{}))),e.dispatch(l.actions.setPluginUrl((0,a.get)(window,"wpseoScriptData.pluginUrl",""))),e.dispatch(l.actions.setWistiaEmbedPermissionValue("1"===(0,a.get)(window,"wpseoScriptData.wistiaEmbedPermission",!1)))})(e),e}();window.yoast.initEditorIntegration(e);const s=new window.yoast.EditorData(a.noop,e,z);s.initialize(window.wpseoScriptData.analysis.plugins.replaceVars.replace_vars),ut(o(),e,s),function(e){e(document).ready((function(e){void 0!==wp.media&&e(".wpseo_image_upload_button").each((function(t,s){const n=function(t){let s=(t=e(t)).data("target");return s&&""!==s||(s=e(t).attr("id").replace(/_button$/,"")),s}(s),i=e(s).data("target-id"),o=e("#"+n),a=e("#"+i);var r=wp.media.frames.file_frame=wp.media({title:wpseoScriptData.media.choose_image,button:{text:wpseoScriptData.media.choose_image},multiple:!1,library:{type:"image"}});r.on("select",(function(){var e=r.state().get("selection").first().toJSON();o.val(e.url),a.val(e.id)}));const c=e(s);c.click((function(e){e.preventDefault(),r.open()})),c.siblings(".wpseo_image_remove_button").on("click",(e=>{e.preventDefault(),o.val(""),a.val("")}))}))}))}(o()),(()=>{if((0,c.select)("yoast-seo/editor").getPreference("isInsightsEnabled",!1))(0,c.dispatch)("yoast-seo/editor").loadEstimatedReadingTime(),(0,c.subscribe)((0,a.debounce)(wt(),1500,{maxWait:3e3}))})(),window.wpseoScriptData.termType&&!["product_cat","product_tag"].includes(window.wpseoScriptData.termType)&&(()=>{const e=(0,c.select)(ss).getIsPremium(),t=(0,c.select)(ss).getIsWooSeoUpsell(),s=(0,c.select)(ss).getIsProduct()?!e||t:!e;(0,de.addFilter)("yoast.replacementVariableEditor.additionalButtons","yoast/yoast-seo-premium/AiGenerator",((e,{fieldId:t})=>(s&&e.push((0,ft.createElement)(bt.Fill,{name:`yoast.replacementVariableEditor.additionalButtons.${t}`},(0,ft.createElement)(ts,{fieldId:t}))),e)))})()}))})();