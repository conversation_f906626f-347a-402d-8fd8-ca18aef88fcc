(()=>{self.window=self;const e=["lodash","regenerator-runtime","wp-hooks","wp-i18n"];self.onmessage=({data:s})=>{if(!s||!s.dependencies)return;!function(s){for(const o in s)Object.prototype.hasOwnProperty.call(s,o)&&(e.includes(o)||o.startsWith("yoast-seo"))&&(self.importScripts(s[o]),"lodash"===o&&(self.lodash=_.noConflict()))}(s.dependencies),s.translations&&function(e){for(const[s,o]of e){const e=o.locale_data[s]||o.locale_data.messages;e[""].domain=s,self.wp.i18n.setLocaleData(e,s)}}(s.translations);const o=self.yoast.Researcher.default;new self.yoast.analysis.AnalysisWebWorker(self,new o).register()}})();