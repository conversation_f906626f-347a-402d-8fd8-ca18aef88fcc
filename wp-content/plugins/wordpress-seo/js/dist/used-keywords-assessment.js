(()=>{"use strict";const s=window.yoast.analysis.bundledPlugins.usedKeywords;(new class{constructor(){this._initialized=!1}register(){analysisWorker.registerMessageHandler("updateKeywordUsage",this.updateKeywordUsage.bind(this),"used-keywords-assessment"),analysisWorker.registerMessageHandler("initialize",this.initialize.bind(this),"used-keywords-assessment")}initialize(e){this._plugin=new s(analysisWorker,e),this._plugin.registerPlugin(),this._initialized=!0}updateKeywordUsage(s){if(!this._initialized)throw new Error("UsedKeywordsAssessment must be initialized before keyphrases can be updated.");const e=s.usedKeywords,i=s.usedKeywordsPostTypes;this._plugin.updateKeywordUsage(e,i),analysisWorker.refreshAssessment("usedKeywords","previouslyUsedKeywords")}}).register()})();