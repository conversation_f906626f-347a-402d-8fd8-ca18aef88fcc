(()=>{"use strict";var t={n:e=>{var s=e&&e.__esModule?()=>e.default:()=>e;return t.d(s,{a:s}),s},d:(e,s)=>{for(var n in s)t.o(s,n)&&!t.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:s[n]})},o:(t,e)=>Object.prototype.hasOwnProperty.call(t,e)};const e=window.jQuery;var s=t.n(e);const n=window.wp.i18n;class a extends Error{constructor(t){super(t),this.name="ImportValidationError"}}const i=window.lodash;class o extends Error{constructor(t,e,s,n,a){super(t),this.name="RequestError",this.url=e,this.method=s,this.statusCode=n,this.stackTrace=a}}class r extends Error{constructor(t,e){super(t),this.name="ParseError",this.parseString=e}}class d{constructor(t,e=[],s=[]){this.settings=t,(0,i.isObject)(e)?this.preIndexingActions=(0,i.pickBy)(e,i.isFunction):this.preIndexingActions={},(0,i.isObject)(s)?this.postIndexingActions=(0,i.pickBy)(s,i.isFunction):this.postIndexingActions={}}async index(t,e){if(!(0,i.isObject)(t))return 0;let s=0;for(const n of Object.keys(t)){const a=t[n];s=await this.handleEndpoint(n,a,s,e)}return s}async handleEndpoint(t,e,s,n){let a=this.settings.restApi.root+e;for(;!1!==a;){await this.doPreIndexingAction(t);const e=await this.doIndexingRequest(a);await this.doPostIndexingAction(t,e),n(s+=e.objects.length),a=!!e.next_url&&this.settings.restApi.root+e.next_url}return s}async doIndexingRequest(t){const e=await fetch(t,{method:"POST",headers:{"X-WP-Nonce":this.settings.restApi.nonce}}),s=await e.text();let n;try{n=JSON.parse(s)}catch(t){throw new r("Error parsing the response to JSON.",s)}if(!e.ok){const s=n.data?n.data.stackTrace:"";throw"wpseo_error_validation"===(n.code?n.code:"")?new a(n.message):new o(n.message,t,"POST",e.status,s)}return n}async doPreIndexingAction(t){this.preIndexingActions[t]&&await this.preIndexingActions[t](this.settings)}async doPostIndexingAction(t,e){this.postIndexingActions[t]&&await this.postIndexingActions[t](e.objects,this.settings)}}const p="WPSEO_Import_AIOSEO_V4";let c,l,m,h,u,g,w,f,y,x,_,I;function v(t){var e=m,s=f,n=c;"import"===t&&(e=g,s=y,n=h),e.children(".yoast-import-spinner").show(),s.show(),e.closest("div").find(".yoast-import-failure").remove(),n.prop("disabled",!0)}function b(t){v("import")}function D(t){v("cleanup")}function A(t){var e=m,n=f,a=c,i=l;"import"===t&&(e=g,n=y,a=h,i=u),e.children(".yoast-import-spinner").hide(),n.hide(),e.children(".yoast-import-success-mark").show(),e.closest("div").find(".yoast-import-failure").remove(),a.prop("disabled",!1),s()("option:selected",i).remove(),s()("option[value='']",i).prop("selected",!0),i.trigger("change"),i.children("option").length<2&&(i.prop("disabled",!0),e.after(s()("<p></p>").text(window.yoastImportData.assets.no_data_msg)))}function O(){A("import")}function E(){A("cleanup")}function P(t,e){var i,o=m,r=f,d=c,p=window.yoastImportData.assets.cleanup_failure;"import"===e&&(o=g,r=y,d=h,p=window.yoastImportData.assets.import_failure),o.children(".yoast-import-spinner").hide(),r.hide(),d.prop("disabled",!1),i=t instanceof a?window.yoastImportData.assets.validation_failure:(0,n.sprintf)(p,"<strong>"+t+"</strong>");var l=s()("<div>").addClass("yoast-measure yoast-import-failure").html(i);o.after(l)}function j(t){P(t,"import")}function k(t){P(t,"cleanup")}function S(t){u.val()===p&&(t.preventDefault(),new d(window.yoastImportData).index(window.yoastImportData.restApi.importing_endpoints.aioseo,b).then(O).catch(j))}function C(t){l.val()===p&&(t.preventDefault(),new d(window.yoastImportData).index(window.yoastImportData.restApi.cleanup_endpoints.aioseo,D).then(E).catch(k))}function T(t){var e,a,i,o=t.closest("form").find("input[type=submit]");t.on("change",(function(){""!==(e=s()(this).find("option:selected").attr("value"))?(o.prop("disabled",!1),t===u&&(a=(0,n.sprintf)(window.yoastImportData.assets.replacing_texts.select_header,s()(this).find("option:selected").text()),i=e===p?window.yoastImportData.assets.replacing_texts.plugins.aioseo:window.yoastImportData.assets.replacing_texts.plugins.other,a+="<ul style='list-style: disc; padding: 0 15px;'>",i.forEach((function(t){a+="<li>"+t.data_name+"<br/><i>"+t.data_note+"</i></li>"})),a+="</ul>",I.html(a))):o.prop("disabled",!0)}))}s()((function(){c=s()("[name='clean_external']"),c.val(window.yoastImportData.assets.replacing_texts.cleanup_button),l=s()("[name='clean_external_plugin']"),m=s()(c).parents("form:first"),h=s()("[name='import_external']"),u=s()("[name='import_external_plugin']"),g=s()(h).parents("form:first"),g.after(s()("<p></p>").html("<strong>"+window.yoastImportData.assets.note+"</strong>"+window.yoastImportData.assets.cleanup_after_import_msg)),w=s()("<img>").addClass("yoast-import-spinner").attr("src",window.yoastImportData.assets.spinner).css({display:"inline-block","margin-left":"10px","vertical-align":"middle"}).hide(),y=s()("<span>").html(window.yoastImportData.assets.loading_msg_import).css({"margin-left":"5px","vertical-align":"middle"}).hide(),f=s()("<span>").html(window.yoastImportData.assets.loading_msg_cleanup).css({"margin-left":"5px","vertical-align":"middle"}).hide(),x=s()("<span>").addClass("dashicons dashicons-yes-alt yoast-import-success-mark").css({"margin-left":"10px","vertical-align":"middle",color:"green"}).hide(),I=s()(".yoast-import-explanation"),I.html(window.yoastImportData.assets.replacing_texts.import_explanation),_=s()(".yoast-cleanup-explanation"),_.html(window.yoastImportData.assets.replacing_texts.cleanup_explanation),u&&(T(u),u.append("<option value='' disabled='disabled' selected hidden>&mdash; "+window.yoastImportData.assets.select_placeholder+" &mdash;</option>").trigger("change")),l&&(T(l),l.append("<option value='' disabled='disabled' selected hidden>&mdash; "+window.yoastImportData.assets.select_placeholder+" &mdash;</option>").trigger("change")),g&&g.on("submit",S),m&&m.on("submit",C),s()(x).insertAfter([h,c]),s()(y).insertAfter(h),s()(f).insertAfter(c),s()(w).insertAfter([h,c])}))})();