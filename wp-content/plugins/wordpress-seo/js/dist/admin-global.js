(()=>{"use strict";var t={n:o=>{var a=o&&o.__esModule?()=>o.default:()=>o;return t.d(a,{a}),a},d:(o,a)=>{for(var e in a)t.o(a,e)&&!t.o(o,e)&&Object.defineProperty(o,e,{enumerable:!0,get:a[e]})},o:(t,o)=>Object.prototype.hasOwnProperty.call(t,o)};const o=window.jQuery;var a=t.n(o);!function(t){function o(t,o,e){const s=new FormData,n={action:"wpseo_set_ignore",option:t,_wpnonce:e};for(const[t,o]of Object.entries(n))s.append(t,o);return fetch(ajaxurl,{method:"POST",body:s}).then((e=>(e&&(a()("#"+o).hide(),a()("#hidden_ignore_"+t).val("ignore")),e)))}function e(){t("#wp-admin-bar-root-default > li").off("mouseenter.yoastalertpopup mouseleave.yoastalertpopup"),t(".yoast-issue-added").fadeOut(200)}function s(o,a){if(t(".yoast-notification-holder").off("click",".restore").off("click",".dismiss"),void 0!==a.html){a.html&&(o.closest(".yoast-container").html(a.html),n());var e=t("#wp-admin-bar-wpseo-menu"),s=e.find(".yoast-issue-counter");s.length||(e.find("> a:first-child").append('<div class="yoast-issue-counter"/>'),s=e.find(".yoast-issue-counter")),s.html(a.total),0===a.total?s.hide():s.show(),t("#toplevel_page_wpseo_dashboard .update-plugins").removeClass().addClass("update-plugins count-"+a.total),t("#toplevel_page_wpseo_dashboard .plugin-count").html(a.total)}}function n(){var o=t(".yoast-notification-holder");o.on("click",".dismiss",(function(){var o=t(this),a=o.closest(".yoast-notification-holder");o.closest(".yoast-container").append('<div class="yoast-container-disabled"/>'),t.post(ajaxurl,{action:"yoast_dismiss_notification",notification:a.attr("id"),nonce:a.data("nonce"),data:o.data("json")||a.data("json")},s.bind(this,a),"json")})),o.on("click",".restore",(function(){var o=t(this),a=o.closest(".yoast-notification-holder");o.closest(".yoast-container").append('<div class="yoast-container-disabled"/>'),t.post(ajaxurl,{action:"yoast_restore_notification",notification:a.attr("id"),nonce:a.data("nonce"),data:a.data("json")},s.bind(this,a),"json")}))}function i(t){t.is(":hidden")||(t.outerWidth()>t.parent().outerWidth()?(t.data("scrollHint").addClass("yoast-has-scroll"),t.data("scrollContainer").addClass("yoast-has-scroll")):(t.data("scrollHint").removeClass("yoast-has-scroll"),t.data("scrollContainer").removeClass("yoast-has-scroll")))}function l(){window.wpseoScrollableTables=t(".yoast-table-scrollable"),window.wpseoScrollableTables.length&&window.wpseoScrollableTables.each((function(){var o=t(this);if(!o.data("scrollContainer")){var a=t("<div />",{class:"yoast-table-scrollable__hintwrapper",html:"<span class='yoast-table-scrollable__hint' aria-hidden='true' />"}).insertBefore(o),e=t("<div />",{class:"yoast-table-scrollable__container",html:"<div class='yoast-table-scrollable__inner' />"}).insertBefore(o);a.find(".yoast-table-scrollable__hint").text(wpseoAdminGlobalL10n.scrollable_table_hint),o.data("scrollContainer",e),o.data("scrollHint",a),o.appendTo(e.find(".yoast-table-scrollable__inner")),i(o)}}))}a()(document).ready((function(){a()(".yoast-dismissible").on("click",".yoast-notice-dismiss",(function(){var t=a()(this).parent();return a().post(ajaxurl,{action:t.attr("id").replace(/-/g,"_"),_wpnonce:t.data("nonce"),data:t.data("json")}),a().post(ajaxurl,{action:"yoast_dismiss_notification",notification:t.attr("id"),nonce:t.data("nonce"),data:t.data("json")}),t.fadeTo(100,0,(function(){t.slideUp(100,(function(){t.remove()}))})),!1})),a()(".yoast-help-button").on("click",(function(){var t=a()(this),o=a()("#"+t.attr("aria-controls")),e=o.is(":visible");a()(o).slideToggle(200,(function(){t.attr("aria-expanded",!e)}))})),a()("button#robotsmessage-dismiss-button").on("click",(function(){o("search_engines_discouraged_notice","robotsmessage",a()(this).data("nonce")).then((()=>{window.location.href.includes("page=wpseo_dashboard")&&window.location.reload()}))}))})),window.wpseoSetIgnore=o,window.wpseoDismissLink=function(t){return a()('<a href="'+t+'" type="button" class="notice-dismiss"><span class="screen-reader-text">Dismiss this notice.</span></a>')},t(window).on("wp-window-resized orientationchange",(function(){window.wpseoScrollableTables&&window.wpseoScrollableTables.length&&window.wpseoScrollableTables.each((function(){i(t(this))}))})),t(window).on({"Yoast:YoastTabsMounted":function(){setTimeout((function(){l()}),100)},"Yoast:YoastTabsSelected":function(){setTimeout((function(){l()}),100)}}),t(document).ready((function(){t(".yoast-issue-added").on("mouseenter mouseleave",(function(t){t.stopPropagation(),e()})).fadeIn(),t("#wp-admin-bar-root-default > li").on("mouseenter.yoastalertpopup mouseleave.yoastalertpopup",e),setTimeout(e,3e3),n(),function(){const t=a()(".wpseo-js-premium-indicator"),o=t.find("svg");if(t.hasClass("wpseo-premium-indicator--no")){const a=o.find("path"),e=t.css("backgroundColor");a.css("fill",e)}o.css("display","block"),t.css({backgroundColor:"transparent",width:"20px",height:"20px"})}(),l(),function(){const t=a()(".yoast-issue-counter .yoast-issues-count").first(),o=a()("#toplevel_page_wpseo_dashboard .plugin-count");if(t.text()===o.first().text())return;const e=a()("#toplevel_page_wpseo_dashboard .update-plugins"),s=a()(".yoast-issue-counter .screen-reader-text").first(),n=a()("#toplevel_page_wpseo_dashboard .update-plugins .screen-reader-text");if(t.length)return o.text(t.text()),e.removeClass().addClass("update-plugins count-"+t.text()),void n.text(s.text());o.text("0"),e.removeClass().addClass("update-plugins count-0"),n.remove()}()}))}(a())})();