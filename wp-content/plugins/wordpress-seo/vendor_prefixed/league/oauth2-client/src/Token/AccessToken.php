<?php

/**
 * This file is part of the league/oauth2-client library
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 *
 * @copyright Copyright (c) <PERSON> <<EMAIL>>
 * @license http://opensource.org/licenses/MIT MIT
 * @link http://thephpleague.com/oauth2-client/ Documentation
 * @link https://packagist.org/packages/league/oauth2-client Packagist
 * @link https://github.com/thephpleague/oauth2-client GitHub
 */
namespace YoastSEO_Vendor\League\OAuth2\Client\Token;

use InvalidArgumentException;
use RuntimeException;
/**
 * Represents an access token.
 *
 * @link http://tools.ietf.org/html/rfc6749#section-1.4 Access Token (RFC 6749, §1.4)
 */
class AccessToken implements \YoastSEO_Vendor\League\OAuth2\Client\Token\AccessTokenInterface, \YoastSEO_Vendor\League\OAuth2\Client\Token\ResourceOwnerAccessTokenInterface
{
    /**
     * @var string
     */
    protected $accessToken;
    /**
     * @var int
     */
    protected $expires;
    /**
     * @var string
     */
    protected $refreshToken;
    /**
     * @var string
     */
    protected $resourceOwnerId;
    /**
     * @var array
     */
    protected $values = [];
    /**
     * @var int
     */
    private static $timeNow;
    /**
     * Set the time now. This should only be used for testing purposes.
     *
     * @param int $timeNow the time in seconds since epoch
     * @return void
     */
    public static function setTimeNow($timeNow)
    {
        self::$timeNow = $timeNow;
    }
    /**
     * Reset the time now if it was set for test purposes.
     *
     * @return void
     */
    public static function resetTimeNow()
    {
        self::$timeNow = null;
    }
    /**
     * @return int
     */
    public function getTimeNow()
    {
        return self::$timeNow ? self::$timeNow : \time();
    }
    /**
     * Constructs an access token.
     *
     * @param array $options An array of options returned by the service provider
     *     in the access token request. The `access_token` option is required.
     * @throws InvalidArgumentException if `access_token` is not provided in `$options`.
     */
    public function __construct(array $options = [])
    {
        if (empty($options['access_token'])) {
            throw new \InvalidArgumentException('Required option not passed: "access_token"');
        }
        $this->accessToken = $options['access_token'];
        if (!empty($options['resource_owner_id'])) {
            $this->resourceOwnerId = $options['resource_owner_id'];
        }
        if (!empty($options['refresh_token'])) {
            $this->refreshToken = $options['refresh_token'];
        }
        // We need to know when the token expires. Show preference to
        // 'expires_in' since it is defined in RFC6749 Section 5.1.
        // Defer to 'expires' if it is provided instead.
        if (isset($options['expires_in'])) {
            if (!\is_numeric($options['expires_in'])) {
                throw new \InvalidArgumentException('expires_in value must be an integer');
            }
            $this->expires = $options['expires_in'] != 0 ? $this->getTimeNow() + $options['expires_in'] : 0;
        } elseif (!empty($options['expires'])) {
            // Some providers supply the seconds until expiration rather than
            // the exact timestamp. Take a best guess at which we received.
            $expires = $options['expires'];
            if (!$this->isExpirationTimestamp($expires)) {
                $expires += $this->getTimeNow();
            }
            $this->expires = $expires;
        }
        // Capture any additional values that might exist in the token but are
        // not part of the standard response. Vendors will sometimes pass
        // additional user data this way.
        $this->values = \array_diff_key($options, \array_flip(['access_token', 'resource_owner_id', 'refresh_token', 'expires_in', 'expires']));
    }
    /**
     * Check if a value is an expiration timestamp or second value.
     *
     * @param integer $value
     * @return bool
     */
    protected function isExpirationTimestamp($value)
    {
        // If the given value is larger than the original OAuth 2 draft date,
        // assume that it is meant to be a (possible expired) timestamp.
        $oauth2InceptionDate = 1349067600;
        // 2012-10-01
        return $value > $oauth2InceptionDate;
    }
    /**
     * @inheritdoc
     */
    public function getToken()
    {
        return $this->accessToken;
    }
    /**
     * @inheritdoc
     */
    public function getRefreshToken()
    {
        return $this->refreshToken;
    }
    /**
     * @inheritdoc
     */
    public function getExpires()
    {
        return $this->expires;
    }
    /**
     * @inheritdoc
     */
    public function getResourceOwnerId()
    {
        return $this->resourceOwnerId;
    }
    /**
     * @inheritdoc
     */
    public function hasExpired()
    {
        $expires = $this->getExpires();
        if (empty($expires)) {
            throw new \RuntimeException('"expires" is not set on the token');
        }
        return $expires < \time();
    }
    /**
     * @inheritdoc
     */
    public function getValues()
    {
        return $this->values;
    }
    /**
     * @inheritdoc
     */
    public function __toString()
    {
        return (string) $this->getToken();
    }
    /**
     * @inheritdoc
     */
    public function jsonSerialize()
    {
        $parameters = $this->values;
        if ($this->accessToken) {
            $parameters['access_token'] = $this->accessToken;
        }
        if ($this->refreshToken) {
            $parameters['refresh_token'] = $this->refreshToken;
        }
        if ($this->expires) {
            $parameters['expires'] = $this->expires;
        }
        if ($this->resourceOwnerId) {
            $parameters['resource_owner_id'] = $this->resourceOwnerId;
        }
        return $parameters;
    }
}
