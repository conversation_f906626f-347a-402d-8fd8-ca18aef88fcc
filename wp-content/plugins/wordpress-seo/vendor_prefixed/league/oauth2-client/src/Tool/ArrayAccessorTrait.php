<?php

/**
 * This file is part of the league/oauth2-client library
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 *
 * @copyright Copyright (c) <PERSON> <<EMAIL>>
 * @license http://opensource.org/licenses/MIT MIT
 * @link http://thephpleague.com/oauth2-client/ Documentation
 * @link https://packagist.org/packages/league/oauth2-client Packagist
 * @link https://github.com/thephpleague/oauth2-client GitHub
 */
namespace YoastSEO_Vendor\League\OAuth2\Client\Tool;

/**
 * Provides generic array navigation tools.
 */
trait ArrayAccessorTrait
{
    /**
     * Returns a value by key using dot notation.
     *
     * @param  array      $data
     * @param  string     $key
     * @param  mixed|null $default
     * @return mixed
     */
    private function getValueByKey(array $data, $key, $default = null)
    {
        if (!\is_string($key) || empty($key) || !\count($data)) {
            return $default;
        }
        if (\strpos($key, '.') !== \false) {
            $keys = \explode('.', $key);
            foreach ($keys as $innerKey) {
                if (!\is_array($data) || !\array_key_exists($innerKey, $data)) {
                    return $default;
                }
                $data = $data[$innerKey];
            }
            return $data;
        }
        return \array_key_exists($key, $data) ? $data[$key] : $default;
    }
}
