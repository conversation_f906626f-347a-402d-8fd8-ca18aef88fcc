<?php return array('addon-installation.js' => array('dependencies' => array('react', 'wp-components', 'wp-element', 'wp-i18n', 'wp-polyfill', 'yoast-seo-components-new-package', 'yoast-seo-prop-types-package', 'yoast-seo-styled-components-package'), 'version' => 'b9f59c3fa79d3c219f77'), 'admin-global.js' => array('dependencies' => array('jquery', 'wp-polyfill'), 'version' => 'ee3745f3fdc45e43fb51'), 'admin-modules.js' => array('dependencies' => array('react', 'wp-data', 'wp-element', 'wp-i18n', 'wp-polyfill', 'yoast-seo-components-new-package', 'yoast-seo-helpers-package', 'yoast-seo-prop-types-package'), 'version' => 'fdbe5d62f7cf6543e337'), 'analysis-worker.js' => array('dependencies' => array('wp-polyfill'), 'version' => 'aa04978fbd423b404462'), 'api-client.js' => array('dependencies' => array('wp-polyfill'), 'version' => 'f56d7de163fa219c67e2'), 'block-editor.js' => array('dependencies' => array('lodash', 'moment', 'react', 'wp-annotations', 'wp-api-fetch', 'wp-blocks', 'wp-components', 'wp-compose', 'wp-data', 'wp-edit-post', 'wp-element', 'wp-html-entities', 'wp-i18n', 'wp-plugins', 'wp-polyfill', 'wp-rich-text', 'wp-sanitize', 'wp-url', 'yoast-seo-analysis-package', 'yoast-seo-chart.js-package', 'yoast-seo-components-new-package', 'yoast-seo-externals-components', 'yoast-seo-externals-contexts', 'yoast-seo-externals-redux', 'yoast-seo-feature-flag-package', 'yoast-seo-helpers-package', 'yoast-seo-prop-types-package', 'yoast-seo-replacement-variable-editor-package', 'yoast-seo-search-metadata-previews-package', 'yoast-seo-social-metadata-forms-package', 'yoast-seo-style-guide-package', 'yoast-seo-styled-components-package', 'yoast-seo-ui-library-package'), 'version' => '5d9eb0a02f8c7b68e3ca'), 'bulk-editor.js' => array('dependencies' => array('jquery', 'wp-polyfill'), 'version' => '308d4f19cc8fcb346d3d'), 'classic-editor.js' => array('dependencies' => array('jquery', 'lodash', 'moment', 'react', 'wp-api-fetch', 'wp-components', 'wp-compose', 'wp-data', 'wp-element', 'wp-i18n', 'wp-polyfill', 'wp-sanitize', 'wp-url', 'yoast-seo-analysis-package', 'yoast-seo-chart.js-package', 'yoast-seo-components-new-package', 'yoast-seo-externals-components', 'yoast-seo-externals-contexts', 'yoast-seo-externals-redux', 'yoast-seo-feature-flag-package', 'yoast-seo-helpers-package', 'yoast-seo-prop-types-package', 'yoast-seo-replacement-variable-editor-package', 'yoast-seo-search-metadata-previews-package', 'yoast-seo-social-metadata-forms-package', 'yoast-seo-style-guide-package', 'yoast-seo-styled-components-package', 'yoast-seo-ui-library-package'), 'version' => 'dc1bbc157ee19ee1bd7a'), 'crawl-settings.js' => array('dependencies' => array('wp-polyfill'), 'version' => 'd511931b46d0b74648b4'), 'dashboard-widget.js' => array('dependencies' => array('react', 'wp-element', 'wp-polyfill', 'yoast-seo-analysis-report-package', 'yoast-seo-components-new-package', 'yoast-seo-helpers-package', 'yoast-seo-style-guide-package'), 'version' => 'ad6147e367cc50376cc5'), 'wincher-dashboard-widget.js' => array('dependencies' => array('lodash', 'moment', 'react', 'wp-api-fetch', 'wp-element', 'wp-i18n', 'wp-polyfill', 'yoast-seo-components-new-package', 'yoast-seo-helpers-package', 'yoast-seo-prop-types-package', 'yoast-seo-style-guide-package', 'yoast-seo-styled-components-package'), 'version' => '3e855acf6ae73ab6d1ed'), 'dynamic-blocks.js' => array('dependencies' => array('react', 'wp-block-editor', 'wp-blocks', 'wp-polyfill', 'wp-server-side-render'), 'version' => '739ebde8b3e418b9886a'), 'edit-page.js' => array('dependencies' => array('jquery', 'wp-polyfill'), 'version' => 'afab9d8fdff1d98c8ca9'), 'editor-modules.js' => array('dependencies' => array('lodash', 'moment', 'react', 'wp-api-fetch', 'wp-components', 'wp-compose', 'wp-data', 'wp-element', 'wp-hooks', 'wp-i18n', 'wp-polyfill', 'wp-sanitize', 'wp-url', 'yoast-seo-analysis-package', 'yoast-seo-analysis-report-package', 'yoast-seo-chart.js-package', 'yoast-seo-components-new-package', 'yoast-seo-externals-contexts', 'yoast-seo-externals-redux', 'yoast-seo-helpers-package', 'yoast-seo-prop-types-package', 'yoast-seo-social-metadata-forms-package', 'yoast-seo-style-guide-package', 'yoast-seo-styled-components-package', 'yoast-seo-ui-library-package'), 'version' => '1be6af7fb88f02d0efd5'), 'elementor.js' => array('dependencies' => array('lodash', 'moment', 'react', 'wp-annotations', 'wp-api-fetch', 'wp-components', 'wp-compose', 'wp-data', 'wp-dom-ready', 'wp-element', 'wp-hooks', 'wp-i18n', 'wp-polyfill', 'wp-rich-text', 'wp-sanitize', 'wp-url', 'yoast-seo-analysis-package', 'yoast-seo-chart.js-package', 'yoast-seo-components-new-package', 'yoast-seo-externals-components', 'yoast-seo-externals-contexts', 'yoast-seo-externals-redux', 'yoast-seo-feature-flag-package', 'yoast-seo-helpers-package', 'yoast-seo-prop-types-package', 'yoast-seo-react-helmet-package', 'yoast-seo-redux-js-toolkit-package', 'yoast-seo-replacement-variable-editor-package', 'yoast-seo-search-metadata-previews-package', 'yoast-seo-social-metadata-forms-package', 'yoast-seo-style-guide-package', 'yoast-seo-styled-components-package', 'yoast-seo-ui-library-package'), 'version' => 'dab4737f64b89ca4e0a1'), 'externals-components.js' => array('dependencies' => array('lodash', 'react', 'wp-api-fetch', 'wp-components', 'wp-compose', 'wp-data', 'wp-element', 'wp-hooks', 'wp-i18n', 'wp-polyfill', 'wp-url', 'yoast-seo-analysis-package', 'yoast-seo-analysis-report-package', 'yoast-seo-components-new-package', 'yoast-seo-externals-contexts', 'yoast-seo-helpers-package', 'yoast-seo-prop-types-package', 'yoast-seo-style-guide-package', 'yoast-seo-styled-components-package', 'yoast-seo-ui-library-package'), 'version' => 'b8ccb6f0a8b0f6c9e690'), 'externals-contexts.js' => array('dependencies' => array('react', 'wp-element', 'wp-polyfill', 'yoast-seo-prop-types-package'), 'version' => '10ecaeb7fee15b420938'), 'externals-redux.js' => array('dependencies' => array('lodash', 'react', 'wp-api-fetch', 'wp-data', 'wp-element', 'wp-hooks', 'wp-i18n', 'wp-polyfill', 'wp-sanitize', 'wp-url', 'yoast-seo-helpers-package', 'yoast-seo-redux-js-toolkit-package'), 'version' => '7f3dc7acc44e2c8f84c2'), 'filter-explanation.js' => array('dependencies' => array('wp-polyfill'), 'version' => '8b3042cee26c58eb9be7'), 'first-time-configuration.js' => array('dependencies' => array('lodash', 'react', 'wp-api-fetch', 'wp-dom-ready', 'wp-element', 'wp-i18n', 'wp-polyfill', 'wp-url', 'yoast-seo-helpers-package', 'yoast-seo-prop-types-package', 'yoast-seo-social-metadata-forms-package', 'yoast-seo-ui-library-package'), 'version' => 'ae111865916ddf8520c7'), 'help-scout-beacon.js' => array('dependencies' => array('react', 'wp-element', 'wp-i18n', 'wp-polyfill', 'yoast-seo-styled-components-package'), 'version' => '4cff4f48af6493046757'), 'import.js' => array('dependencies' => array('jquery', 'lodash', 'wp-i18n', 'wp-polyfill'), 'version' => 'cbe848d7253c616f3a75'), 'indexation.js' => array('dependencies' => array('jquery', 'react', 'wp-element', 'wp-i18n', 'wp-polyfill', 'yoast-seo-components-new-package', 'yoast-seo-helpers-package', 'yoast-seo-prop-types-package', 'yoast-seo-style-guide-package', 'yoast-seo-styled-components-package'), 'version' => '06817d20ccce29675844'), 'installation-success.js' => array('dependencies' => array('lodash', 'react', 'wp-components', 'wp-dom-ready', 'wp-element', 'wp-i18n', 'wp-polyfill', 'yoast-seo-externals-contexts', 'yoast-seo-prop-types-package', 'yoast-seo-styled-components-package'), 'version' => '72f4f44c8846afcf4236'), 'integrations-page.js' => array('dependencies' => array('lodash', 'react', 'wp-api-fetch', 'wp-components', 'wp-dom-ready', 'wp-element', 'wp-i18n', 'wp-polyfill', 'yoast-seo-externals-contexts', 'yoast-seo-prop-types-package', 'yoast-seo-styled-components-package', 'yoast-seo-ui-library-package'), 'version' => 'fa7eed1ce031aa8ded1d'), 'introductions.js' => array('dependencies' => array('lodash', 'react', 'wp-api-fetch', 'wp-data', 'wp-dom-ready', 'wp-element', 'wp-hooks', 'wp-i18n', 'wp-polyfill', 'wp-url', 'yoast-seo-prop-types-package', 'yoast-seo-react-helmet-package', 'yoast-seo-redux-js-toolkit-package', 'yoast-seo-ui-library-package'), 'version' => '884e9170969d434ebfc0'), 'network-admin.js' => array('dependencies' => array('jquery', 'wp-polyfill'), 'version' => 'c28de4314d03147fca4a'), 'post-edit.js' => array('dependencies' => array('jquery', 'lodash', 'react', 'wp-annotations', 'wp-api', 'wp-api-fetch', 'wp-blocks', 'wp-components', 'wp-compose', 'wp-data', 'wp-dom-ready', 'wp-element', 'wp-hooks', 'wp-i18n', 'wp-is-shallow-equal', 'wp-polyfill', 'wp-rich-text', 'wp-url', 'yoast-seo-analysis-package', 'yoast-seo-externals-redux', 'yoast-seo-feature-flag-package', 'yoast-seo-prop-types-package', 'yoast-seo-react-helmet-package', 'yoast-seo-redux-js-toolkit-package', 'yoast-seo-styled-components-package', 'yoast-seo-ui-library-package'), 'version' => '8af776c3fcbf30801984'), 'quick-edit-handler.js' => array('dependencies' => array('wp-polyfill'), 'version' => 'e7d3f8a9873afbfd1425'), 'reindex-links.js' => array('dependencies' => array('jquery', 'wp-polyfill'), 'version' => 'e4694eb7292052d53fc4'), 'redirect-old-features-tab.js' => array('dependencies' => array('wp-polyfill'), 'version' => 'a792fdd4c0d1c2ef737c'), 'settings.js' => array('dependencies' => array('jquery', 'lodash', 'react', 'wp-compose', 'wp-data', 'wp-dom-ready', 'wp-element', 'wp-i18n', 'wp-polyfill', 'wp-url', 'yoast-seo-externals-redux', 'yoast-seo-prop-types-package', 'yoast-seo-styled-components-package'), 'version' => '8756fcebaf4d52ad1086'), 'new-settings.js' => array('dependencies' => array('lodash', 'react', 'wp-api-fetch', 'wp-components', 'wp-data', 'wp-dom-ready', 'wp-element', 'wp-html-entities', 'wp-i18n', 'wp-polyfill', 'wp-url', 'yoast-seo-prop-types-package', 'yoast-seo-react-helmet-package', 'yoast-seo-redux-js-toolkit-package', 'yoast-seo-replacement-variable-editor-package', 'yoast-seo-styled-components-package', 'yoast-seo-ui-library-package'), 'version' => 'a5f86be4f500ef806907'), 'academy.js' => array('dependencies' => array('lodash', 'react', 'wp-api-fetch', 'wp-components', 'wp-data', 'wp-dom-ready', 'wp-element', 'wp-i18n', 'wp-polyfill', 'wp-url', 'yoast-seo-redux-js-toolkit-package', 'yoast-seo-ui-library-package'), 'version' => 'dcd9152448a2432e4ffb'), 'support.js' => array('dependencies' => array('lodash', 'react', 'wp-api-fetch', 'wp-components', 'wp-data', 'wp-dom-ready', 'wp-element', 'wp-i18n', 'wp-polyfill', 'wp-url', 'yoast-seo-prop-types-package', 'yoast-seo-react-helmet-package', 'yoast-seo-redux-js-toolkit-package', 'yoast-seo-ui-library-package'), 'version' => '494918afe38ee2453887'), 'how-to-block.js' => array('dependencies' => array('lodash', 'react', 'wp-a11y', 'wp-block-editor', 'wp-blocks', 'wp-components', 'wp-element', 'wp-i18n', 'wp-is-shallow-equal', 'wp-polyfill', 'yoast-seo-prop-types-package', 'yoast-seo-styled-components-package'), 'version' => '3c48eb9c2bda3f650887'), 'faq-block.js' => array('dependencies' => array('react', 'wp-a11y', 'wp-block-editor', 'wp-blocks', 'wp-components', 'wp-element', 'wp-i18n', 'wp-is-shallow-equal', 'wp-polyfill', 'yoast-seo-prop-types-package'), 'version' => '34ebc93abf046084fe1c'), 'term-edit.js' => array('dependencies' => array('jquery', 'lodash', 'react', 'wp-annotations', 'wp-api', 'wp-api-fetch', 'wp-components', 'wp-data', 'wp-dom-ready', 'wp-element', 'wp-hooks', 'wp-i18n', 'wp-is-shallow-equal', 'wp-polyfill', 'wp-rich-text', 'wp-url', 'yoast-seo-analysis-package', 'yoast-seo-externals-redux', 'yoast-seo-feature-flag-package', 'yoast-seo-prop-types-package', 'yoast-seo-react-helmet-package', 'yoast-seo-redux-js-toolkit-package', 'yoast-seo-ui-library-package'), 'version' => 'b68e3c34d52911f89726'), 'used-keywords-assessment.js' => array('dependencies' => array('wp-polyfill', 'yoast-seo-analysis-package'), 'version' => 'f2d934f4e70fdace40fc'), 'react-select.js' => array('dependencies' => array('react', 'react-dom', 'wp-polyfill', 'yoast-seo-prop-types-package'), 'version' => '016b19f563b56deb8ad5'), 'workouts.js' => array('dependencies' => array('lodash', 'react', 'wp-api-fetch', 'wp-components', 'wp-compose', 'wp-data', 'wp-dom-ready', 'wp-element', 'wp-i18n', 'wp-polyfill', 'yoast-seo-components-new-package', 'yoast-seo-externals-contexts', 'yoast-seo-helpers-package', 'yoast-seo-prop-types-package', 'yoast-seo-styled-components-package'), 'version' => '5285008d6544c5f1c0d4'), 'frontend-inspector-resources.js' => array('dependencies' => array('lodash', 'react', 'wp-i18n', 'wp-polyfill', 'yoast-seo-analysis-package', 'yoast-seo-components-new-package', 'yoast-seo-prop-types-package', 'yoast-seo-style-guide-package'), 'version' => 'bd58767c5c438a57b506'));
