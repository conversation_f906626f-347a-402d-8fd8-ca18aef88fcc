<?php

namespace Yoast\WP\SEO\Presentations;

/**
 * Class Indexable_Home_Page_Presentation.
 *
 * Presentation object for indexables.
 */
class Indexable_Home_Page_Presentation extends Indexable_Presentation {

	use Archive_Adjacent;

	/**
	 * Generates the canonical.
	 *
	 * @return string The canonical.
	 */
	public function generate_canonical() {
		if ( $this->model->canonical ) {
			return $this->model->canonical;
		}

		if ( ! $this->permalink ) {
			return '';
		}

		$current_page = $this->pagination->get_current_archive_page_number();
		if ( $current_page > 1 ) {
			return $this->pagination->get_paginated_url( $this->permalink, $current_page );
		}

		return $this->permalink;
	}

	/**
	 * Generates the meta description.
	 *
	 * @return string The meta description.
	 */
	public function generate_meta_description() {
		if ( $this->model->description ) {
			return $this->model->description;
		}

		return $this->options->get( 'metadesc-home-wpseo' );
	}

	/**
	 * Generates the title.
	 *
	 * @return string The title.
	 */
	public function generate_title() {
		if ( $this->model->title ) {
			return $this->model->title;
		}

		return $this->options->get_title_default( 'title-home-wpseo' );
	}
}
