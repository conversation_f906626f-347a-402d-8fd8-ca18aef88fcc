os: linux

dist: bionic

language: php

notifications:
  email:
    on_success: never
    on_failure: change

branches:
  only:
    - master

php:
  - 7.3
  - 7.4
  - 8.0
  - 8.1
  - 8.2

services:
  - mysql

env:
  - WP_VERSION=latest WP_MULTISITE=0

jobs:
  include:
    - php: 7.3
      env: WP_VERSION=latest WP_MULTISITE=1 WPSNIFF=1
    - php: 8.2
      env: WP_VERSION=latest WP_MULTISITE=1 WPSNIFF=1
    - php: 8.0
      env: WP_VERSION=6.0 WP_MULTISITE=0

before_script:
  - export PATH="$HOME/.config/composer/vendor/bin:$PATH"
  - phpenv config-rm xdebug.ini
  - bash bin/install-wp-tests.sh wordpress_test root '' localhost $WP_VERSION
  - composer global require --dev yoast/phpunit-polyfills:"^1.0"
  - |
    if [[ "$WPSNIFF" == "1" ]]; then
      composer global config allow-plugins.dealerdirect/phpcodesniffer-composer-installer true
      composer global require --dev wp-coding-standards/wpcs phpcompatibility/phpcompatibility-wp
      phpcs -i
    fi
  - |

script:
  - if [[ "$WPSNIFF" == "1" ]]; then phpcs --standard=phpcs.ruleset.xml --extensions=php .; fi
  - phpunit
