{"_readme": ["This file locks the dependencies of your project to a known state", "Read more about it at https://getcomposer.org/doc/01-basic-usage.md#installing-dependencies", "This file is @generated automatically"], "content-hash": "3d1a26d43e65eba3a0bd49c016586678", "packages": [{"name": "lsolesen/pel", "version": "0.9.12", "source": {"type": "git", "url": "https://github.com/pel/pel.git", "reference": "b95fe29cdacf9d36330da277f10910a13648c84c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/pel/pel/zipball/b95fe29cdacf9d36330da277f10910a13648c84c", "reference": "b95fe29cdacf9d36330da277f10910a13648c84c", "shasum": ""}, "require": {"php": ">=7.1.0"}, "require-dev": {"ext-exif": "*", "ext-gd": "*", "php-coveralls/php-coveralls": ">2.4", "squizlabs/php_codesniffer": ">3.5", "symfony/phpunit-bridge": "^4 || ^5"}, "type": "library", "autoload": {"psr-4": {"lsolesen\\pel\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://intraface.dk", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://geisler.net", "role": "Developer"}], "description": "PHP Exif Library. A library for reading and writing Exif headers in JPEG and TIFF images using PHP.", "homepage": "http://pel.github.com/pel/", "keywords": ["exif", "image"], "support": {"issues": "https://github.com/pel/pel/issues", "source": "https://github.com/pel/pel/tree/0.9.12"}, "time": "2022-02-18T13:20:54+00:00"}], "packages-dev": [], "aliases": [], "minimum-stability": "stable", "stability-flags": [], "prefer-stable": false, "prefer-lowest": false, "platform": [], "platform-dev": [], "plugin-api-version": "2.1.0"}