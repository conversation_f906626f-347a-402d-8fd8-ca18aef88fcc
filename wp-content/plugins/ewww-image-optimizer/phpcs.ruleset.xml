<?xml version="1.0"?>
<ruleset name="WordPress Coding Standards for Plugins">
	<description>Generally-applicable sniffs for WordPress plugins</description>
	<!-- Only sniff PHP files -->
	<arg name="extensions" value="php"/>

	<exclude-pattern>/vendor/*</exclude-pattern>
	<exclude-pattern>/tests/*</exclude-pattern>

	<rule ref="WordPress-Core" />
	<rule ref="WordPress-Docs" />

	<rule ref="Squiz.PHP.CommentedOutCode">
		<exclude name="Squiz.PHP.CommentedOutCode.Found" />
	</rule>

	<rule ref="Generic.PHP.NoSilencedErrors">
		<exclude name="Generic.PHP.NoSilencedErrors.Discouraged" />
	</rule>

	<rule ref="WordPress.PHP.NoSilencedErrors">
		<properties>
			<property name="customAllowedFunctionsList" type="array" value="ini_get,exif_read_data,ob_end_flush"/>
		</properties>
	</rule>
	<rule ref="WordPress.NamingConventions.ValidVariableName">
		<properties>
			<property name="allowed_custom_properties" type="array" value="formatOutput,imgQuality,parentNode,nextSibling,documentElement,imagePath,webimagePath,thumbPath,fileName,Version,Name" />
		</properties>
	</rule>

	<config name="testVersion">7.3-</config>
	<rule ref="PHPCompatibilityWP">
		<severity>10</severity>
	</rule>

	<rule ref="WordPress.Security">
		<severity>10</severity>
	</rule>

	<rule ref="WordPress.DB.PreparedSQL">
		<severity>10</severity>
	</rule>
</ruleset>
