{"name": "lsolesen/pel", "type": "library", "description": "PHP Exif Library. A library for reading and writing Exif headers in JPEG and TIFF images using PHP.", "keywords": ["image", "exif"], "homepage": "http://pel.github.com/pel/", "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://intraface.dk", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://geisler.net", "role": "Developer"}], "license": "GPL-2.0", "require": {"php": ">=7.1.0"}, "require-dev": {"ext-gd": "*", "ext-exif": "*", "squizlabs/php_codesniffer": ">3.5", "php-coveralls/php-coveralls": ">2.4", "symfony/phpunit-bridge": "^4 || ^5"}, "autoload": {"psr-4": {"lsolesen\\pel\\": "src/"}}, "autoload-dev": {"psr-4": {"Pel\\Test\\": "test/"}}}