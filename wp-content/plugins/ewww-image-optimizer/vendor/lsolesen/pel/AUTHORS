AUTHORS file for PEL: PHP Exif Library.  A library with support for
reading and writing Exif headers in JPEG and TIFF images using PHP.

Copyright (C) 2004, 2005, 2006, 2007  <PERSON>.
Copyright (c) 2008, 2009 <PERSON>
Copyright (c) 2015 <PERSON>
Licensed under the GNU GPL, see COPYING for details.


Credits
*******

PEL is written by <PERSON> <mg<PERSON><PERSON>@users.sourceforge.net> and
is now maintained by <PERSON> <<EMAIL>>
and <jwe<PERSON><PERSON><PERSON>@weberhofer.at>

The source started out as a port of the nice, clean C code found in
libexif.  Most of the translations included with PEL also originates
from the libexif project, see below for details.


Test Image Contributors
***********************

<PERSON>: Nikon E950 and E5000 test images.

Stéphanie <PERSON>tel: Canon IXUS II test image.

<PERSON>: Nikon Coolscan IV ED test image.

<PERSON><PERSON><PERSON>: Canon PowerShot S60 test image.

<PERSON>: Pentax *ist DS and Olympus C-5050z test images.

<PERSON><PERSON><PERSON>: Leica D-LUX test image.

<PERSON>: Olympus C-50z and C-765z test images.


Translators
***********

Danish: <PERSON>.

French: <PERSON><PERSON><PERSON>nay (for libexif) and <PERSON> <PERSON>r.

German: Hans <PERSON> Niedermann, Lu<PERSON> M<PERSON> (for libexif).

Japanese: Tadashi Jokagi.

Polish: Jakub Bogusz (for libexif).

Spanish: Fabian Mandelbaum (for libexif).
