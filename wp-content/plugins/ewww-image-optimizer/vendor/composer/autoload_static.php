<?php

// autoload_static.php @generated by Composer

namespace Composer\Autoload;

class ComposerStaticInitba6324246c1d906b530ebcb60468d7b9
{
    public static $prefixLengthsPsr4 = array (
        'l' => 
        array (
            'lsolesen\\pel\\' => 13,
        ),
    );

    public static $prefixDirsPsr4 = array (
        'lsolesen\\pel\\' => 
        array (
            0 => __DIR__ . '/..' . '/lsolesen/pel/src',
        ),
    );

    public static $classMap = array (
        'Composer\\InstalledVersions' => __DIR__ . '/..' . '/composer/InstalledVersions.php',
    );

    public static function getInitializer(ClassLoader $loader)
    {
        return \Closure::bind(function () use ($loader) {
            $loader->prefixLengthsPsr4 = ComposerStaticInitba6324246c1d906b530ebcb60468d7b9::$prefixLengthsPsr4;
            $loader->prefixDirsPsr4 = ComposerStaticInitba6324246c1d906b530ebcb60468d7b9::$prefixDirsPsr4;
            $loader->classMap = ComposerStaticInitba6324246c1d906b530ebcb60468d7b9::$classMap;

        }, null, ClassLoader::class);
    }
}
