!function(){var e={52:function(){jQuery((function(){var e=document.querySelector(".frash-notice"),t=e.querySelector("input[name=notice_nonce]").value,n=e.querySelector("input[name=type]").value,r=e.querySelector("input[name=email]"),i=e.querySelector("input[name=source]"),o=e.querySelector(".frash-notice-act"),a=e.querySelector(".frash-notice-dismiss");function u(r,i){e.dataset.message=i,e.classList.add("loading");var o={action:"wpmudev_notices_action",nonce:t,plugin_id:e.querySelector("input[name=plugin_id]").value,notice_action:r,notice_type:n};jQuery.post(window.ajaxurl,o,c)}function c(){!function t(){e.style.opacity=e.style.opacity-.05,+e.style.opacity>0?window.requestAnimationFrame&&requestAnimationFrame(t)||setTimeout(t,10):e.remove()}()}o&&o.addEventListener("click",(function(t){t.preventDefault();var a=e.querySelector('input[type="email"]');if(a&&a.value||"email"!==n){switch(n){case"rate":s=e.querySelector("input[name=url_wp]").value.replace(/\/plugins\//,"/support/plugin/")+"/reviews/?rate=5#new-post",(l=document.createElement("a")).setAttribute("href",s),l.setAttribute("target","_blank"),l.innerHTML="Rate",document.querySelector("body").appendChild(l),l.click(),l.remove();break;case"email":c=r.closest("form"),jQuery.ajax({type:c.getAttribute("method"),url:c.getAttribute("action"),data:{source:i.value,email:r.value},cache:!1,dataType:"json",success:function(e){window.console.log(e.msg)}})}var c,s,l;u("dismiss_notice",o.dataset.msg)}})),a&&a.addEventListener("click",(function(e){e.preventDefault(),u("dismiss_notice",a.dataset.msg)})),window.setTimeout((function(){setTimeout((function(){e.classList.remove("hidden")}),500),function(){if(!r)return;function e(){var e=document.createElement("span");e.setAttribute("class","input-field"),e.innerHTML=r.value,document.querySelector("body").appendChild(e);var t=Math.ceil(e.getBoundingClientRect().width);e.remove(),r.style.width=t+34+"px"}r.addEventListener("keypress",(function(t){if(!t.defaultPrevented){var n=!1;(void 0!==t.key&&"Enter"===t.key||void 0!==t.keyCode&&13===t.keyCode)&&(o.click(),n=!0),n?t.preventDefault():e()}})),e()}()}),500)}))},840:function(){},379:function(e){"use strict";var t=[];function n(e){for(var n=-1,r=0;r<t.length;r++)if(t[r].identifier===e){n=r;break}return n}function r(e,r){for(var o={},a=[],u=0;u<e.length;u++){var c=e[u],s=r.base?c[0]+r.base:c[0],l=o[s]||0,d="".concat(s," ").concat(l);o[s]=l+1;var p=n(d),f={css:c[1],media:c[2],sourceMap:c[3],supports:c[4],layer:c[5]};if(-1!==p)t[p].references++,t[p].updater(f);else{var m=i(f,r);r.byIndex=u,t.splice(u,0,{identifier:d,updater:m,references:1})}a.push(d)}return a}function i(e,t){var n=t.domAPI(t);n.update(e);return function(t){if(t){if(t.css===e.css&&t.media===e.media&&t.sourceMap===e.sourceMap&&t.supports===e.supports&&t.layer===e.layer)return;n.update(e=t)}else n.remove()}}e.exports=function(e,i){var o=r(e=e||[],i=i||{});return function(e){e=e||[];for(var a=0;a<o.length;a++){var u=n(o[a]);t[u].references--}for(var c=r(e,i),s=0;s<o.length;s++){var l=n(o[s]);0===t[l].references&&(t[l].updater(),t.splice(l,1))}o=c}}},569:function(e){"use strict";var t={};e.exports=function(e,n){var r=function(e){if(void 0===t[e]){var n=document.querySelector(e);if(window.HTMLIFrameElement&&n instanceof window.HTMLIFrameElement)try{n=n.contentDocument.head}catch(e){n=null}t[e]=n}return t[e]}(e);if(!r)throw new Error("Couldn't find a style target. This probably means that the value for the 'insert' parameter is invalid.");r.appendChild(n)}},216:function(e){"use strict";e.exports=function(e){var t=document.createElement("style");return e.setAttributes(t,e.attributes),e.insert(t,e.options),t}},565:function(e,t,n){"use strict";e.exports=function(e){var t=n.nc;t&&e.setAttribute("nonce",t)}},795:function(e){"use strict";e.exports=function(e){if("undefined"==typeof document)return{update:function(){},remove:function(){}};var t=e.insertStyleElement(e);return{update:function(n){!function(e,t,n){var r="";n.supports&&(r+="@supports (".concat(n.supports,") {")),n.media&&(r+="@media ".concat(n.media," {"));var i=void 0!==n.layer;i&&(r+="@layer".concat(n.layer.length>0?" ".concat(n.layer):""," {")),r+=n.css,i&&(r+="}"),n.media&&(r+="}"),n.supports&&(r+="}");var o=n.sourceMap;o&&"undefined"!=typeof btoa&&(r+="\n/*# sourceMappingURL=data:application/json;base64,".concat(btoa(unescape(encodeURIComponent(JSON.stringify(o))))," */")),t.styleTagTransform(r,e,t.options)}(t,e,n)},remove:function(){!function(e){if(null===e.parentNode)return!1;e.parentNode.removeChild(e)}(t)}}}},589:function(e){"use strict";e.exports=function(e,t){if(t.styleSheet)t.styleSheet.cssText=e;else{for(;t.firstChild;)t.removeChild(t.firstChild);t.appendChild(document.createTextNode(e))}}}},t={};function n(r){var i=t[r];if(void 0!==i)return i.exports;var o=t[r]={exports:{}};return e[r](o,o.exports,n),o.exports}n.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return n.d(t,{a:t}),t},n.d=function(e,t){for(var r in t)n.o(t,r)&&!n.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},n.nc=void 0,function(){"use strict";var e=n(379),t=n.n(e),r=n(795),i=n.n(r),o=n(569),a=n.n(o),u=n(565),c=n.n(u),s=n(216),l=n.n(s),d=n(589),p=n.n(d),f=n(840),m=n.n(f),v={};v.styleTagTransform=p(),v.setAttributes=c(),v.insert=a().bind(null,"head"),v.domAPI=i(),v.insertStyleElement=l();t()(m(),v),m()&&m().locals&&m().locals,n(52)}()}();