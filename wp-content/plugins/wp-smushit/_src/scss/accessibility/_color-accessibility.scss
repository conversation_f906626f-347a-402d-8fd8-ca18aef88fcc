@include body-class(true) {
    &.sui-color-accessible {
        .smush-final-log .smush-bulk-error-row {
            box-shadow: inset 2px 0 0 0 $accessible-dark;
            .smush-bulk-image-data:before {
                color: $accessible-dark;
            }
        }
        // Bulk Smush Fancy Tree
        ul.fancytree-container {
            .fancytree-selected {
                background-color: #F8F8F8;
                span.fancytree-checkbox {
                    border: 1px solid $accessible-dark;
                    background-color: $accessible-dark;
                }
            }
            span.fancytree-expander:before,
            span.fancytree-icon:before,
            span.fancytree-title {
                color: $accessible-dark;
            }
        }
        // CDN
        .smush-filename-extension {
            background-color: $accessible-dark;
        }
        // Check images button.
        .sui-button {
            &.smush-button-check-success:before {
                color: $accessible-light;
            }
        }
        // Smush submit note.
        .smush-submit-note {
            color: $accessible-dark;
        }

        // Hightlight lazyload spinner.
        .sui-lazyload .sui-box-selector [name="animation[spinner-icon]"]:checked+span {
            background-color: rgba(220,220,222, 0.7)!important;
        }
    }
}

@media all and (-ms-high-contrast: none), (-ms-high-contrast: active) {
    .sui-wrap .sui-toggle-slider {
        -ms-high-contrast-adjust: none;
    }
}
