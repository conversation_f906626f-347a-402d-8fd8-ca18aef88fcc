/**
 * Common styles that are used on all the WP pages in the backend
 */
@import "modules/media";

.sui-wrap .smush-upsell-link,
.sui-wrap a.smush-upsell-link {
	color: $purple;
	> span:before {
		color: $purple;
	}
	&:hover:not(.sui-button),
	&:focus:not(.sui-button),
	&:active:not(.sui-button) {
		color: #64007e;
		> span:before {
			color: #64007e;
		}
	}
}

/**
 * Media details (grid layout)
 * @since 3.4.0
 */
.attachment-info .smush-stats .value {
	display: flex;
	flex-wrap: wrap;

	.smush-status {
		margin: 0 0 10px;
		flex-basis: 100%;
		font-size: 12px;
		line-height: 1.33333;
	}

	.smush-status-links {
		width: 100%;
	}

	span.sui-tooltip {
		float: none;
	}

	a {
		margin-left: 5px;
	}

	a:first-of-type {
		margin-left: 0;
		margin-right: 5px;
	}
}

.attachment-info .smush-status-links,
.column-smushit .smush-status-links {
	color: #ddd;
}

.column-smushit .smush-status-links > a {
	box-shadow: none;
	outline: none;
}

.wp-smush-progress {
	padding-left: 25px;
	margin: 0;
	background-size: 17px 17px;
	visibility: visible;
	vertical-align: initial !important; /* prevent from jumping on a line */
	display: inline;
	color: #32373c;
	cursor: default;
}

// Fix grid view links.
.attachment-details .setting span.wp-smush-progress {
	width: auto;
	line-height: 0;
	margin-right: 5px;
}

/** Settings Page **/
.smush-status.fail {
	color: #dd3d36;
}

.smush-status.success {
	color: #0074a2;
}

.smush-status.error {
	color: red;
}

#wpbody-content .wp-smush-error {
	color: red;
}

.wp-smush-action[disabled] {
	opacity: 0.6;
}

#post-body-content .smush-status {
	margin: 4px 0;
}

.attachment-info .wp-smush-error-message {
	margin: 0 0 1em;
}

.smush-stats-wrapper .row {
	padding: 8px 0;
}

.smush-stats-wrapper .row:first-child {
	padding-top: 0;
}

.smush-stats-wrapper td, .smush-stats-wrapper th {
	font-size: 11px;
}

.smush-skipped .dashicons-editor-help {
	margin-top: -2px;
	margin-left: 5px;
}

.smush-skipped {
	a:focus { box-shadow: 0 0 black; }
	.sui-tag.sui-tag-purple {
		min-height: 18px;
		padding: 2px 10px;
		font-size: 10px;
		line-height: 12px;
		font-weight: 700;
		background-color: #8d00b1;
		color: #fff;
		border: 2px solid transparent;
		border-radius: 13px;
	}
}

/** Help Tip **/
.ui-tooltip-content {
	font-size: 12px;
}

/** All Smushed **/
.wp-smush-notice {
	background-color: #D1F1EA;
	border-radius: 5px;
	color: #333333;
	font-family: 'Roboto', sans-serif;
	font-size: 15px;
	line-height: 30px;
	margin-bottom: 30px;
	padding: 15px 30px;
	letter-spacing: -0.015em;
}

div.smush-notice-cta a.smush-notice-act.button-primary {
	padding: 3px 23px;
	background-color: #00B0DB;
	box-shadow: none;
	border-radius: 4px;
	border: none;
	text-shadow: none;
	font-weight: normal;
	-webkit-font-smoothing: antialiased;

	&:hover {
		border: none;
	}
}

a.wp-smush-resize-enable:hover,
a.wp-smush-lossy-enable:hover {
	color: #0A9BD6;
}

.wp-smush-bulk-wrapper {
	#wp-smush-bulk-image-count {
		color: #333333;
		font-size: 28px;
		line-height: 40px;
		letter-spacing: -0.5px;
		font-weight: 600;
	}

	#wp-smush-bulk-image-count-description {
		color: #333333;
		font-size: 13px;
		margin-top: 0;
		margin-bottom: 10px;
	}

	.sui-tooltip,
	.sui-tooltip > .sui-icon-info {
		vertical-align: top;
	}
}

/** Image Remaining **/
div.wp-smush-dir-limit,
div.smush-s3-setup-message {
	background-color: #FFF5D5;
	border: none;
	color: #333333;
	line-height: 30px;
	font-size: 15px;
	letter-spacing: -0.015em;
}

div.smush-s3-setup-message {
	background-color: #DFF6FA;
}

div.wp-smush-dir-limit {
	background-color: #dff6fa;
}

.wp-smush-count {
	color: #888888;
	font-size: 13px;
	line-height: 1.5;
	margin-top: 15px;
}

/** Stats Container **/

a.wp-smush-lossy-enable {
	cursor: pointer;
}

/** Re Smush **/
.wp-smush-settings-changed {
	background: #dff6fa;
	border-radius: 5px;
	font-size: 13px;
	line-height: 1.7;
	padding: 20px;
}

.compat-item .compat-field-wp_smush {
	display: table-row;
}

.manage-column.column-smushit {
	width: 260px;
}

.smushit [tooltip],
label.setting.smush-stats [tooltip],
.compat-field-wp_smush [tooltip] {
	position: relative;
	overflow: visible;
}

.smushit [tooltip]:before,
label.setting.smush-stats [tooltip]:before,
.compat-field-wp_smush [tooltip]:before {
	content: '';
	position: absolute;
	border: 5px solid transparent;
	border-top-color: #0B2F3F;
	bottom: 100%;
	left: 50%;
	margin-left: -5px;
	margin-bottom: -5px;
	opacity: 0;
	z-index: -1;
	transition: margin .2s, opacity .2s, z-index .2s linear .2s;
	pointer-events: none;
}

.smushit [tooltip]:after,
label.setting.smush-stats [tooltip]:after,
.compat-field-wp_smush [tooltip]:after {
	background: #0B2F3F;
	border-radius: 4px;
	bottom: 100%;
	color: #FFF;
	content: attr(tooltip);
	font-size: 13px;
	font-weight: 400;
	left: 50%;
	line-height: 20px;
	margin-left: -100px;
	margin-bottom: 5px;
	opacity: 0;
	padding: 5px;
	pointer-events: none;
	position: absolute;
	width: 180px;
	text-align: center;
	transition: margin .2s, opacity .2s, z-index .2s linear .2s;
	white-space: pre-wrap;
	z-index: -1;
}

.smushit .smush-skipped [tooltip]:before,
label.setting.smush-stats .smush-skipped [tooltip]:before,
.compat-field-wp_smush .smush-skipped [tooltip]:before {
	border-top-color: transparent;
	border-left-color: #0B2F3F;
	bottom: 0;
	left: 0;
}

.smushit .smush-skipped [tooltip]:after,
label.setting.smush-stats .smush-skipped [tooltip]:after,
.compat-field-wp_smush .smush-skipped [tooltip]:after {
	margin-left: 0;
	left: -195px;
	top: -35px;
	bottom: inherit;
	margin-bottom: 5px;
}

label.setting.smush-stats .smush-skipped [tooltip]:after {
	top: -98px;
}

div.media-sidebar label.setting.smush-stats .smush-skipped [tooltip]:after {
	left: -188px;
	padding-left: 10px;
	width: 170px;
}

div.media-sidebar label.setting.smush-stats .smush-skipped [tooltip]:before {
	margin-left: -3px;
}

.smushit [tooltip].tooltip-s:after,
label.setting.smush-stats [tooltip].tooltip-s:after,
.compat-field-wp_smush [tooltip].tooltip-s:after {
	width: 150px;
	margin-left: -75px;
}

.smushit [tooltip].tooltip-l:after,
label.setting.smush-stats [tooltip].tooltip-l:after,
.compat-field-wp_smush [tooltip].tooltip-l:after {
	width: 280px;
	margin-left: -140px;
}

.smushit [tooltip].tooltip-right:after, .compat-field-wp_smush [tooltip].tooltip-right:after {
	margin-left: -180px;
}

.smushit [tooltip].tooltip-s.tooltip-right:after, .compat-field-wp_smush [tooltip].tooltip-s.tooltip-right:after {
	margin-left: -130px;
}

.smushit [tooltip].tooltip-l.tooltip-right:after, .compat-field-wp_smush [tooltip].tooltip-l.tooltip-right:after {
	margin-left: -260px;
}

.smushit [tooltip].tooltip-bottom:before, .compat-field-wp_smush [tooltip].tooltip-bottom:before {
	border-color: transparent;
	border-bottom-color: #0B2F3F;
	top: 100%;
	bottom: auto;
	margin-top: -5px;
	margin-bottom: 0;
}

.smushit [tooltip].tooltip-bottom:after, .compat-field-wp_smush [tooltip].tooltip-bottom:after {
	bottom: auto;
	top: 100%;
	margin-top: 5px;
	margin-bottom: 0;
}

.smushit [tooltip]:hover:before,
label.setting.smush-stats [tooltip]:hover:before,
.compat-field-wp_smush [tooltip]:hover:before {
	z-index: 1;
	margin-bottom: 0;
	opacity: 1;
	transition: margin .2s, opacity .2s;
}

.smushit [tooltip]:hover:after,
label.setting.smush-stats [tooltip]:hover:after,
.compat-field-wp_smush [tooltip]:hover:after {
	opacity: 1;
	z-index: 1;
	margin-bottom: 10px;
	transition: margin .2s, opacity .2s;
}

.smushit .disabled[tooltip]:before,
.smushit .disabled[tooltip]:after,
label.setting.smush-stats .disabled[tooltip]:before,
label.setting.smush-stats .disabled[tooltip]:after,
.compat-field-wp_smush .disabled[tooltip]:before,
.compat-field-wp_smush .disabled[tooltip]:after {
	display: none;
}

/** Image List **/
div.wp-smush-scan-result {
	background: white;

	div.wp-smush-notice {
		margin-top: 14px;
		padding: 15px 30px;
	}

	div.content {
		overflow: hidden;
		width: 100%;
	}
}

div.wp-smush-info.notice {
	font-size: 15px;
	letter-spacing: -0.015em;
	margin: 0 0 30px;
	padding: 15px;
}

/** Media Queries **/

@media screen and (max-width: 1024px) and (min-width: 800px) {
	/** Stats Section **/
	.smush-stats-wrapper h3 {
		padding: 6px 0;
	}
}

/** Media Queries for resolution below 782px **/
@media only screen and (max-width: 800px) {
	.dev-box.bulk-smush-wrapper.wp-smush-container {
		padding: 20px 10px;
	}
}

/**
 * CSS styles used Admin notice
 */
.smush-notice.notice {
	padding: 0;
	margin: 5px 0 10px;
	border: 1px solid #E5E5E5;
	background: #FFF;
	overflow: hidden;
	-webkit-border-radius: 6px;
	border-radius: 6px;
	-webkit-box-shadow: 0 1px 1px 0 rgba(0, 0, 0, 0.05);
	box-shadow: 0 1px 1px 0 rgba(0, 0, 0, 0.05);
	position: relative;
	z-index: 1;
	min-height: 80px;
	display: table; /* The magic ingredient! */
	font: 13px "Roboto", sans-serif;
}

.smush-notice.notice.loading:before {
	content: attr(data-message);
	position: absolute;
	left: 0;
	right: 0;
	top: 0;
	bottom: 0;
	background-color: rgba(255, 255, 255, 0.7);
	z-index: 5;
	text-align: center;
	line-height: 80px;
	font-size: 22px;
	font-weight: bold;
}

.smush-notice > div {
	display: table-cell; /* The magic ingredient! */
	vertical-align: middle;
	cursor: default;
	line-height: 1.5;
}

.smush-notice.notice.loading > div {
	-webkit-filter: blur(2px);
	-moz-filter: blur(2px);
	-o-filter: blur(2px);
	-ms-filter: blur(2px);
	filter: blur(2px);
}

.smush-notice-logo {
	padding-left: 30px;
}

.smush-notice-message {
	color: #23282D;
	font-size: 13px;
	font-weight: normal;
	line-height: 20px;
	padding: 20px;
	-webkit-font-smoothing: antialiased;
	width: 100%;
}

.smush-notice-cta {
	background: #F8F8F8;
	padding: 0 30px;
	position: relative;
	white-space: nowrap;
}

.wp-core-ui .smush-notice-cta button,
.wp-core-ui .smush-notice-cta .button-primary:active {
	vertical-align: middle;
}

.wp-core-ui .smush-notice-cta input[type="email"] {
	vertical-align: middle;
	line-height: 20px;
	margin: 0;
	min-width: 50px;
	max-width: 320px;
	text-align: center;
	padding-left: 0;
	padding-right: 0;
}

/**
 * Upsell lists.
 * @since 3.9.1
 */
#smush-box-cdn-upsell,
#smush-box-webp-wizard {
	.sui-upsell-list {
		max-width: 490px;
		text-align: left;
		margin: 0 auto;
		li {
			font-size:12px;
			border-bottom:1px solid #f2f2f2;
			padding-bottom: 10px;
			margin-bottom: 15px;
			letter-spacing: -0.23px;
			&:last-child {
				border-bottom: none;
			}
		}
	}
}

#smush-box-cdn-upsell .sui-upsell-list {
	max-width:350px;
}

@media only all and (max-width: 1000px) {
	.smush-notice.notice {
		display: block;
		font-size: 13px;
	}

	.smush-notice > .smush-notice-logo {
		float: left;
		display: inline-block;
		height: 80px;
		margin: 10px;
		border-radius: 4px;
	}

	.smush-notice > .smush-notice-message {
		width: auto;
		display: block;
		min-height: 80px;
	}

	.smush-notice > .smush-notice-cta {
		display: block;
		border-top: 1px solid #E5E5E5;
		border-left: 0;
		text-align: center;
		white-space: normal;
		line-height: 30px;
		padding: 10px 20px;
	}

	.wp-core-ui .smush-notice > .smush-notice-cta > input[type="email"],
	.smush-notice > .smush-notice-cta > button {
		font-size: 14px;
	}
}

@media only all and (max-width: 500px) {
	.wp-core-ui .smush-notice > .smush-notice-cta > input[type="email"],
	.smush-notice > .smush-notice-cta > button {
		display: block;
		width: 100% !important;
		max-width: none;
		margin-bottom: 4px;
		font-size: 16px;
		height: 34px;
	}
}

.smush-dismissible-notice {
	position: relative;
}
