 /**
 * Webp styles
 *
 * @since 3.8.0
 */

@include body-class(true) {

	#smush-box-webp-webp {
		.sui-box-header {
			.smush-sui-tag-new {
				font-size: 10px;
				line-height: 12px;
			}
		}
		.smush-webp-supported-browser {
			height: 30px;
			width: 30px;
			padding: 5px;
			margin-right: 10px;
			border-radius: 4px;
			background-color: #F2F2F2;
			display: inline-block;

			img {
				height: 20px;
				width: 20px;
			}
		}
	}

	#smush-box-webp-wizard {
		.sui-row-with-sidenav {
			margin-bottom: 0;

			.sui-sidenav {
				padding: 30px;
				border-top-left-radius: $border-radius;
				background-color: #F8F8F8;

				.smush-wizard-bar-subtitle {
					font-size: 11px;
					line-height: 20px;
					font-weight: 700;
					color: #AAAAAA;
					text-transform: uppercase;
				}

				.smush-sidenav-title {
					display: flex;
					align-items: center;
					margin-bottom: 33px;

					h4 {
						margin: 0;
						line-height: 20px;
					}
				}

				.smush-wizard-steps-container {

					ul {
						margin: 0;

						@include media(max-width, lg) {
							display: flex;
							flex-direction: row;
							justify-content: space-between;
						}
					}

					.smush-wizard-bar-step {
						display: inline-block;
						font-size: 13px;
						color: #AAAAAA;
						line-height: 22px;
						font-weight: 500;
						margin-bottom: 0;

						.smush-wizard-bar-step-number {
							display: inline-block;
							margin-right: 10px;
							text-align: center;
							border-radius: 50%;
							width: 22px;
							height: 22px;
							font-size: 11px;
							background-color: #F2F2F2;
							border: 1px solid #DDDDDD;

							@include media(max-width, lg) {
								display: block;
								margin: 0 auto 5px auto;
							}
						}

						&.disabled {
							color: #DDDDDD;
						}

						&.current {
							color: #333333;

							.smush-wizard-bar-step-number {
								background-color: #FFFFFF;
								border-color: #333333;
							}
						}

						&.done .smush-wizard-bar-step-number {
							background-color: #1ABC9C;
							border-color: #1ABC9C;

							.sui-icon-check::before {
								color: #FFFFFF;
							}
						}

						@include media(min-width, lg) {
							display: block;
						}

						@include media(max-width, lg) {
							width: 70px;
							text-align: center;
						}
					}

					svg {

						line {
							stroke-width: 4px;
						}

						&.smush-svg-desktop {
							height: 40px;
							width: 22px;
							margin-left: 10px;
							display: none;

							@include media(min-width, lg) {
								display: block;
							}
						}

						&.smush-svg-mobile {
							width: 100%;
							height: 4px;
							display: block;
							margin-bottom: -14px;
							padding: 0 35px;

							@include media(min-width, lg) {
								display: none;
							}
						}
					}
				}

				@include media(max-width, sm) {
					padding: 20px;
				}
			}

			.smush-wizard-steps-content-wrapper {
				padding: 20px;

				.smush-wizard-steps-content {
					padding: 0 70px;
					text-align: center;

					&:first-child {
						padding-top: 30px;
					}

					.smush-step-indicator {
						font-size: 11px;
						font-weight: 500;
						color: #888888;
					}

					h2 {
						margin: 0;
					}

					@include media(max-width, sm) {
						padding: 0;
					}
				}

				&.smush-wizard-step-1 {

					.sui-box-selectors {
						padding-left: 115px;
						padding-right: 115px;
						margin-bottom: 15px;
					}
				}

				&.smush-wizard-step-2 {

					.smush-wizard-rules-wrapper {
						text-align: left;
					}

					.sui-tabs-menu {
						justify-content: center;
					}
				}

				@include media(min-width, sm) {
					padding: 30px;
				}
			}
		}
	}
}
