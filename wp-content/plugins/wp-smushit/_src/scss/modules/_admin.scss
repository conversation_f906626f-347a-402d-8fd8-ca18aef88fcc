@import "variables";

@import "~@wpmudev/shared-ui/scss/mixins";

@include body-class {

	/**
	 * Dashboard meta boxes
	 *
	 * @since 3.8.6
	 */
	.wrap-smush {
		// Images.
		div[class^="sui-dashboard-"] .sui-box-title:before {
			color: #333333;
			float: left;
			font-size: 20px;
			margin-right: 10px;
			line-height: 30px;
		}

		// Meta boxes.
		.sui-dashboard-bulk .sui-box-title,
		.sui-dashboard-upsell-upsell .sui-box-title { @include icon(before, 'smush'); }
		.sui-dashboard-directory .sui-box-title { @include icon(before, 'folder'); }
		.sui-dashboard-integrations .sui-box-title { @include icon(before, 'plugin-2'); }
		.sui-dashboard-lazy-load .sui-box-title { @include icon(before, 'update'); }
		.sui-dashboard-cdn .sui-box-title { @include icon(before, 'web-globe-world'); }
		.sui-dashboard-webp .sui-box-title { @include icon(before, 'photo-picture'); }
		.sui-dashboard-tools .sui-box-title { @include icon(before, 'wrench-tool'); }

		// Dashboard summary meta box.
		#smush-box-dashboard-summary {
			.sui-list-detail {
				display: flex;
				align-items: center;
				& > span.sui-tooltip:first-of-type,
				& > span.sui-tooltip:nth-of-type(2) {
					line-height: 14px;
					margin-right: 5px;
				}
				a > .sui-tag { cursor: pointer; }
			}
			.smush-upgrade-text {
				color: $purple;
				margin-right: 10px;
			}
		}

		// Dashboard tutorials.
		#smush-dash-tutorials,
		#smush-dash-tutorials > .sui-notice { margin-bottom: 30px; }

		// Dashboard lazy load meta box.
		#smush-box-dashboard-lazy-load {
			.sui-settings-label {
				color: #333333;
			}
			.sui-box-settings-row.sui-flushed {
				border-top: 1px solid #E6E6E6;
				padding: 14px 30px;
				justify-content: space-between;
				align-items: center;
			}
		}

		// Dashboard CDN meta box.
		#smush-box-dashboard-cdn {
			.sui-table-item-title {
				display: flex;
				align-items: center;

				.smush-filename-extension { margin-right: 10px; }
			}

			.sui-table.sui-table-flushed tbody tr td:last-of-type { text-align: right; }
		}

		// Dashboard integrations meta box.
		#smush-box-dashboard-integrations {
			.smush-disabled-table-row {
				opacity: 0.5;
				pointer-events: none;
				background-color: rgba(242,242,242,0.5);
			}
		}
	}

	.sui-wrap {
		.sui-box-upsell-row .sui-toggle input[disabled] ~ .sui-description {
			pointer-events: auto !important;
		}
		/**
		 * Bulk smush meta boxes
		 *
		 * @since 3.10.0
		 */
		.sui-dashboard-summary, .sui-summary-smush, .sui-summary-smush-nextgen {
			background-image: none !important;
		}

		.sui-settings-label > .sui-tag {
			vertical-align: middle;
			margin-left: 3px;
		}

		.sui-summary:not(.sui-unbranded):not(.sui-summary-sm) .sui-summary-image-space {
			display: flex;
			justify-content: center;
			flex-direction: column;
			align-items: center;

			background-color: #F8F8F8;
			margin: 20px 10px;
			min-width: 266px;
			flex: 0 0 266px;

			@include media(min-width, lg) {
				min-width: 388px;
				flex: 0 0 388px;
			}

			.sui-circle-score {
				height: 110px;
				margin-bottom: 10px;

				circle {
					stroke-width: 10;
					&:last-of-type {
						stroke-dasharray: var(--metric-array);
					}
				}

				.sui-circle-score-label {
					position: absolute;
					display: flex;
					align-content: space-between;
					justify-content: center;
					align-items: center;
					width: 110px;
					margin: 0;
					font-weight: 700;
					font-size: 30px;
					letter-spacing: -1px;
					color: #333;

					&:after {
						content: '%';
					}
				}
			}

			& > small {
				color: #333 !important;
			}
		}

		@media screen and (min-width: 1140px) {
			.sui-upgrade-page-header__image {
				width: 45% !important;
			}
		}
		.sui-upgrade-page .sui-upgrade-page-header__image{
			background-size: contain;
		}

		.sui-upgrade-page {
			.sui-upgrade-page-cta__inner {
				a.sui-button-purple {
					display: inline-block;
					margin-top: 0;
					margin-bottom: 5px;
					&:hover {
						color: #fff !important;
					}
				}
			}

			.smush-stats {
				position: relative;
				display: flex;
				justify-content: space-between;
				width: 600px;
				height: 100px;
				border-radius: 4px;
				background-color: #FFFFFF;
				box-shadow: 0 0 20px 0 rgba(0,0,0,0.05);
				margin: -100px auto 30px;
				padding: 20px 40px;
				text-align: center;
				font-size: 15px;
				font-weight: 500;
				color: #333333;

				.smush-stats-description {
					color: #888888;
					font-size: 12px;
					text-transform: uppercase;
				}

				span {
					font-size: 24px;
				}
			}

			@media (max-width: 800px) {
				.smush-stats {
					margin-top: 0;
					height: auto;
					width: 75%;
					flex-wrap: wrap;
				}

				.smush-stats-item {
					flex-basis: 100%;
				}

				.smush-stats-item:nth-child(2) {
					margin: 20px 0;
				}
			}
		}

		/**
		 * Integrations
		 *
		 * @since 3.4.0
		 */
		.sui-integrations {
			.sui-settings-label {
				display: flex;
				align-items: center;

				.sui-tag { margin-left: 10px; }
			}

			.sui-toggle-content .sui-notice {
				margin-top: 10px;
			}

			.sui-box-settings-row.sui-disabled {
				margin-bottom: 0;
			}
		}

		/**
		 * Lazy loading
		 *
		 * @since 3.2.2
		 */
		.sui-lazyload {
			#smush-lazy-load-spinners span.sui-description:first-of-type,
			#smush-lazy-load-placeholder span.sui-description:first-of-type {
				margin-bottom: 20px;
			}

			#smush-lazy-load-spinners .sui-box-selectors,
			#smush-lazy-load-placeholder .sui-box-selectors {
				background-color: #FFF;
				padding: 0 0 20px 0;

				&:not([class*="sui-box-selectors-col-"]) ul li {
					flex: 0;
				}

				.sui-box-selector {
					border: 1px solid #DDD;
				}

				.remove-selector {
					display: none;
					top: 0;
					right: 0;
					position: absolute;
					background-color: #AAA;
					width: 18px;
					height: 18px;
					margin-right: 0;
					border-radius: 0 3px 0 3px;

					i {
						margin: 0;
					}

					i:before {
						color: #FFF;
						font-size: 12px;
						line-height: 18px;
					}

					&:hover {
						background-color: #FF6D6D;
					}
				}

				.sui-box-selector:hover .remove-selector {
					display: flex;
				}
			}

			#smush-lazy-load-spinners .sui-box-selectors {
				label.sui-box-selector {
					width: 50px;
					height: 50px;
					overflow: visible;
				}

				.sui-box-selector input + span {
					padding-top: 17px;
					padding-bottom: 17px;
					position: relative;

					img {
						max-width: 16px;
						margin: 0;
					}
				}

				.sui-box-selector input ~ span {
					padding-right: 17px;
					padding-left: 17px;
					height: 48px;
					border-radius: 4px;
					overflow: visible;
				}

				.sui-box-selector input:checked + span:before,
				.sui-box-selector input:checked + span:after {
					content: none;
				}
			}

			#smush-lazy-load-placeholder .sui-box-selectors {
				label.sui-box-selector {
					width: 80px;
					height: 60px;
					overflow: visible;

					&:hover {
						background-color: #E5E5E5;
					}

					input + span {
						height: 40px;
						padding: 0;
						margin: 10px;
						overflow: visible;
						position: relative;

						&:before {
							top: -5px;
							right: -5px;
						}

						&:after {
							top: -10px;
							right: -10px;
						}
					}

					input + span img {
						margin: 0 auto;
					}

					input[id^="placeholder-icon-1"] + span {
						background-color: #FAFAFA;
					}

					input[id^="placeholder-icon-2"] + span {
						background-color: #333333;
					}

					.remove-selector {
						top: -10px;
						right: -10px;
					}
				}
			}
		}

		/**
		 * Onboarding modals
		 *
		 * @since 3.1
		 */
		.smush-onboarding-dialog {
			@keyframes fadeInLeft {
				from {
					opacity: 0;
					transform: translate3d(-50px, 0, 0);
				}
				to {
					opacity: 1;
					transform: none;
				}
			}

			@keyframes fadeInRight {
				from {
					opacity: 0;
					transform: translate3d(50px, 0, 0);
				}
				to {
					opacity: 1;
					transform: none;
				}
			}

			.sui-box { background-color: transparent; }

			#smush-onboarding-content {
				opacity: 0;
				background-color: #fff;
				animation-duration: 0.7s;
				animation-fill-mode: both;
				transform-origin: center;
				transform-style: preserve-3d;

				&.loaded, &.fadeInLeft, &.fadeInRight { opacity: 1; }
				&.fadeInLeft { animation-name: fadeInLeft; }
				&.fadeInRight { animation-name: fadeInRight; }

				.sui-box-selectors, .smush-onboarding-sharing-data {
					display: flex;
					justify-content: center;
					align-items: center;

					label:last-of-type {
						top: 0;
						font-size: 13px;
					}
				}

				.smush-onboarding-tracking-box {
					background: #f8f8f8;
					margin: 2px 0 30px;
					padding: 20px 30px;
					line-height: 22px;
					letter-spacing: -0.25px;
					text-align: center;
					input{
						border:1px solid #ddd;
						background-color: #FAFAFA;
						border-radius: 4px;
						box-shadow: none;
						margin: 0 16px 0 0;
						width: 16px;
						height:16px;
						display: inline-block;
						vertical-align: middle;
						+ span{
							display: inline-block;
						}
					}
					label{
						display: block;
						margin:0 auto;
						max-width: 325px;
						width: 100%;
						span {
							letter-spacing:0;
							font-size: 12px!important;
							font-weight: 400;;
						}
					}
				}
			}

			.smush-onboarding-arrows a {
				position: absolute;
				top: 45%;
				width: 40px;
				height: 40px;
				border-radius: 20px;
				padding-top: 3px;

				&:not(.sui-hidden) {
					display: flex;
					justify-content: center;
					align-items: center;
				}

				&:hover { background-color: rgba(217, 217, 217, 0.2); }

				&:first-of-type {
					left: -55px;
					padding-right: 2px;
				}

				&:last-of-type {
					right: -55px;
					padding-left: 2px;
				}

				i:before { color: #fff; }
			}

			@media screen and (max-width: 782px) {
				.smush-onboarding-arrows { display: none; }
			}
		} // End .smush-onboarding-dialog

		// Bulk smush and directory smush (overwrite when the limit is exceeded)
		.wp-smush-exceed-limit {
			.wp-smush-progress-inner {
				background: #FECF2F !important;
			}

			.sui-icon-info:before {
				color: #FECF2F;
			}
		}
		// Directory smush.
		.wp-smush-progress-dialog {

			// When limit is exceeded.
			&.wp-smush-exceed-limit {

				#smush-limit-reached-notice {
					display: block;
				}

				.wp-smush-resume-scan {
					display: inline;
				}

				.sui-progress {
					.sui-progress-icon {
						.sui-icon-loader {
							@include icon( before, info );
							&:before {
								color: #FECF2F;
								animation: none;
							}
						}
					}
				}

				.sui-progress-bar span {
					background: #888888;
				}

				.sui-box-footer .sui-actions-right:not(.sui-hidden) {
					margin-left: 10px;
					margin-right: auto;
					float: left;

					.sui-button {
						padding: 5px 16px 7px;
						border: 2px solid #ddd;
						background: transparent;
						color: #888;
					}
				}
				.sui-box-footer {
					justify-content: space-between;
				}
			}

			// When the request fails.
			&.wp-smush-scan-error {

				#smush-scan-error-notice {
					display: block;
				}

				.sui-progress-block, .sui-progress-state {
					display: none;
				}
			}

			#smush-limit-reached-notice, .wp-smush-resume-scan, #smush-scan-error-notice {
				display: none;
			}
		}

		.wp-smush-bulk-progress-bar-wrapper {
			margin-bottom: 30px;
			.sui-progress-state span { display: inline-block; }
			.sui-progress-bar {
				border-radius: 5px;
			}
		}

		#sui-cross-sell-footer h3 {
			text-align: center;
		}

		// Bulk Smush error messages: Start.
		.smush-final-log {
			margin-top: 30px;

			.smush-bulk-errors {
				&.overflow-box{
					overflow-y: scroll;
					height: 260px;
				}
				max-height: 260px;
				overflow-y: auto;
				overflow-x:hidden;
				padding-left: 30px;
				padding-right: 30px;
				margin: 0 -30px;

				.smush-bulk-error-row {
					align-content: center;
					align-items: center;
					border-bottom: 1px solid #E6E6E6;
					box-shadow: inset 2px 0 0 0 #FECF2F;
					display: flex;
					height: 52px;
					justify-content: space-between;
					margin: 0 -30px;
					padding: 0 30px 0 15px;
					font-size: 13px;
					letter-spacing: -0.25px;

					&:first-child {
						border-top: 1px solid #E6E6E6;
					}

					.smush-bulk-image-data {
						display: flex;
						align-content: center;
						align-items: center;
						// flex-basis: 100%;
						.sui-icon-photo-picture {
							font-size: 12px;
							border-radius: 4px;
							background-color: #E6E6E6;
							margin-right: 10px;
							padding-left: 9px;
							padding-top: 1px;
							width: 30px;
							height: 30px;

							&:before {
								width: 30px;
								height: 30px;
							}
						}

						.attachment-thumbnail {
							border-radius: 4px;
							width: 30px;
							height: 30px;
							margin-right: 10px;
						}

						.smush-image-name {
							line-height: 20px;
							font-weight: 500;
							margin-right: 10px;
							color:#333;
							word-break: break-all;
						}
					}
					.smush-bulk-image-title{
						display:flex;
						align-items: center;
						justify-content: center;
						&:before {
							content: "I";
							font-family: wpmudev-plugin-icons !important;
							speak: none;
							font-size: 16px;
							font-style: normal;
							font-weight: 400;
							font-variant: normal;
							text-transform: none;
							line-height: 1;
							text-rendering: auto;
							display: inline-block;
							margin-right: 10px;
							color: #FECF2F;
						}
					}
					.smush-image-error {
						line-height: 23px;
						word-break: break-all;
						margin-right: 10px;
						color:#888;
					}

					.smush-bulk-image-actions {
						// flex-basis: auto;
						white-space: nowrap;

						button:disabled {
							background-color: #F2F2F2;
							cursor: default;

							.sui-icon-eye-hide:before {
								color: lighten( #333333, 50% );
							}
						}
						a{
							&.smush-link-detail {
								padding-left: 15px;
								margin-left: 15px;
								position: relative;
								&:before{
									content: "";
									position: absolute;
									border-left:1px solid #ddd;
									left:0;
									top:0;
									bottom:0;
								}
							}
							&.disabled {
								color: currentColor;
								display: inline-block;
								pointer-events: none;
								text-decoration: none;
								cursor: not-allowed;
								opacity: 0.5;
							}
						}
					}

					&.smush-error-upsell {
						box-shadow: inset 2px 0 0 0 #8D00B1;
						.smush-bulk-image-title {
							&:before{
								color:#8D00B1;
							}
							padding:10px 0;
							.smush-image-error {
								margin-top:0;
							}
						}
						a {
							color:#8D00B1;
						}
					}

					@media screen and (max-width: 782px) {
						margin:0 -20px;
					}

					@media screen and (max-width: 767px) {
						height: auto;
						align-items: flex-start;

						.smush-bulk-image-data {
							padding: 10px 0;
							flex-flow: column;
							align-items: flex-start;
							.smush-image-error {
								margin-left: 25px;
							}
						}

						.smush-image-error {
							margin-top: 10px;
						}

						.smush-bulk-image-actions{
							margin-top: 10px;
							max-height: 20px;
							a {
								line-height: 24px;
							}
							.smush-link-detail:before {
								top:9px;
							}
						}
						&.smush-error-upsell .smush-bulk-image-title {
							align-items: flex-start;
							&:before{
								margin-top: 5px;
							}
						}
					}
					@media screen and (max-width: 480px) {
						flex-flow: column;
						align-items: flex-start;
						align-content: flex-start;
						padding-bottom: 14px;
						// .smush-image-error,.smush-bulk-image-title,.smush-bulk-image-actions a {
						// 	font-size: 12px;
						// }
						.smush-bulk-image-actions{
							padding-left: 25px;
							margin-top: 0;
						}
						.smush-bulk-image-data {
							margin-bottom: 5px;
							padding-bottom: 0;
							.smush-image-error {
								margin-top: 10px;
								margin-bottom: 0;
							}
						}

						&.smush-error-upsell .smush-bulk-image-title {
							padding-bottom: 0;
						}

					}
				}
			}

			.smush-bulk-errors-actions {
				margin: 30px 0 10px;
				@media screen and (max-width:320px) {
					a{
						padding-left:10px!important;
						padding-right: 10px!important;
						font-size: 11.5px;
					}
				}
			}

		} // Bulk Smush error messages: End.

		// Settings - keep data.
		.smush-keep-data-form-row {
			.sui-tabs-menu,
			.sui-button-ghost {
				margin-top: 15px;
			}
		}

		.sui-summary-smush {
			background-size: 180px;
		}
		.sui-summary-smush-nextgen {
			background-size: 170px;
		}

		.bulk-smush-wrapper {
			.wp-smush-upsell-on-completed {
				&.sui-hidden{
					display: none!important;
				}
				background: #F8F8F8;
				padding:20px 15px;
				border-radius: 4px;
				.smush-box-image {
					-webkit-box-flex: 0;
					-ms-flex: 0 0 auto;
					flex: 0 0 auto;
					margin-right: 15px;
					img {
						max-width: 44px;
					}
				}
				.sui-box-content {
					p {
						font-size:13px;
						color:#333;
						margin-bottom:0;
						line-height: 22px;
					}
					a {
						font-size:13px;
					}
				}
				@media screen and (min-width: 361px) {
					display:flex;
				}
			}
			.wp-smush-upsell-cdn {
				&.sui-hidden{
					display: none!important;
				}
				padding:20px 30px;
				background: #F8F8F8;
				border-radius: 4px;
				.sui-image-icon{
					width:40px;
					margin-right:15px;
					display: inline-block;
				}
				.sui-box-content {
					p {
						font-size:13px;
						color:#333;
						margin-bottom:5px;
						line-height: 22px;
					}
					a {
						font-size:13px;
					}
				}
				@media screen and ( max-width: 600px ) {
					padding: 20px;
				}
				@media screen and (min-width: 361px) {
					display:flex;
				}
			}
		}

		.bulk-smush-unlimited {
			position:relative;
			.sui-box-title {
				font-size: 16px;
				line-height: 22px;
				margin-bottom:14px;
				@media screen and ( max-width: 480px ) {
					margin-top: 5px;
				}
				background: #F8F8F8;
				padding:20px 15px;
				border-radius: 4px;
				.smush-box-image {
					-webkit-box-flex: 0;
					-ms-flex: 0 0 auto;
					flex: 0 0 auto;
					margin-right: 15px;
					img {
						max-width: 44px;
					}
				}
				.sui-box-content {
					p {
						font-size:13px;
						color:#333;
						margin-bottom:0;
						line-height: 22px;
					}
					a {
						font-size:13px;
					}
				}
				@media screen and (min-width: 361px) {
					display:flex;
				}
			}
		}

		/**
		 * Smush video upsell on dedicated upgrade page and dashboard widget.
		 * @since 3.6.0
		 */
		.sui-upgrade-page .thumbnail-container {
			img {
				cursor: pointer;
				width: 640px;
				@media screen and (max-width: 600px) {
					width: 100%;
				}
				transition: opacity 0.5s;
				&:hover
				{
					opacity: 0.68;
					transition: opacity 0.5s;
				}
			}
		}

		.sui-progress-close {
			border: 0;
			background: 0;
			text-transform: uppercase;
			color: #888888;
			font-size: 12px;
			font-weight: 500;
			letter-spacing: -0.25px;
			margin-left: 10px;
			cursor: pointer;
		}

		.sui-progress-close.wp-smush-cancel-bulk.sui-hidden,
		.sui-progress-close.wp-smush-all.sui-hidden {
			display: none !important;
		}

		.sui-notice {
			&.smush-highlighting-notice,
			&.smush-highlighting-warning {
				margin-top: 10px;
				margin-bottom: 10px;
			}
		}

		.sui-hidden, button.sui-hidden {
			display: none;
		}

		.sui-loading {
			font-size: 18px;
		}

		.sui-button {

			&.smush-button-check-success,
			&.smush-button-check-success:hover {
				background: #d1f1ea !important;
				color: #1abc9c !important;
				pointer-events: none;

				&:before {
					color: $notice-success-icon-color;
				}
			}
		}

		.sui-toggle + label {
			font-weight: 500;
		}
	}

	.smush-align-left {
		float: left;
	}

	.smush-align-right {
		float: right;
	}

	.sui-summary.sui-summary-smush,
	.sui-summary.sui-dashboard-summary {
		.sui-summary-detail.wp-smush-savings .wp-smush-stats-human,
		.sui-summary-large.wp-smush-stats-human,
		.sui-summary-details .sui-summary-detail {
			font-size: 22px;
			line-height: 22px;
			font-weight: 700;
		}

		.sui-summary-detail.wp-smush-savings,
		.sui-summary-detail.wp-smush-savings .wp-smush-stats-percent {
			font-size: 15px;
			font-weight: 400;
		}

		.smushed-items-count {
			.wp-smush-count-resize-total {
				margin-top: 16px;
				display: block;
				&.sui-hidden{
					display:none;
				}
			}
			.wp-smush-count-total {
				display: block;
			}
		}

		.sui-summary-segment {
			overflow: visible;
		}

		.sui-summary-details {
			.sui-summary-sub {
				margin-bottom: 16px;
			}

			.sui-summary-detail {
				display: inline-flex;

				+ .sui-summary-sub { margin-top: 0; }
				.wp-smush-stats-percent { margin-left: 5px; }
				.wp-smush-stats-human { margin-right: 5px; }
			}

			.sui-tooltip {
				position: absolute;
				margin-top: 25px;
				margin-left: -5px;

				&:before {
					margin-bottom: 20px;
					margin-left: 5px;
				}
				&:after {
					margin-bottom: 30px;
					margin-left: 5px;
				}
			}
		}

		.wp-smush-stats-label-message {
			font-size: 13px;
			line-height: 22px;
		}

		.smush-stats-list {
			.sui-tag-pro {
				top: -1px;
				margin-left: 5px;
			}
			li {
				border-bottom: none!important;
				border-top: 1px solid #E6E6E6;
				&:first-child {
					border-top: none;
				}
				&.sui-hidden {
					display: none !important
				}
			}
		}

		.smush-summary-row-compression-type {
			.sui-list-detail {
				display: flex;
				flex-direction: column;
				align-items: flex-end!important;
				a {
					font-size: 11px;
				}
			}
		}
	}

	.sui-box-footer .sui-icon-loader {
		line-height: 18px;
		vertical-align: middle;
	}

	.sui-modal-content {
		&.wp-smush-modal-dark-background{
			> .sui-box {
				background: transparent!important;
				border-radius: 8px;
				overflow: hidden;
				.sui-box-header figure {
					margin-top: -14px!important;
					margin-bottom: 0;
				}
				.sui-box-body, .sui-box-footer {
					background-color:#fff;
				}

			}
		}
	}

	.wp-smush-ultra-compression-link {
		background-color: #F1E7FF!important;
		color: $purple!important;
		span:before {
			color: $purple;
		}

		&:hover {
			color: #64007e!important;
			span:before {
				color: #64007e;
			}
		}
	}

	.wp-smush-scan-progress-bar-wrapper {
		--progress-bar-height: 12px;
		--progress-transition-duration: 1.2s;
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background-color: #fff;
		text-align: center;
		display: flex;
		justify-content: center;
		align-items: center;
		flex-direction: column;
		color:$text-color;
		border-radius: $border-radius;
		overflow: hidden;
		z-index:10;
		.wp-smush-scan-progress-bar-inner {
			display: flex;
			align-items: center;
			text-align: left;
			justify-content: space-between;
			width: 100%;
			padding: 5px 30px;

			.sui-button{
				background-color: transparent;
				color: $main-color;
				&:hover,&:focus{
					background-color: $main-color;
					color:#fff;
					box-shadow: none;
				}
			}
		}
		.wp-smush-progress-status{
			line-height: 30px;
			font-weight: 700;
			// max-height: 100px;
			.wp-smush-progress-percent {
				font-size: 28px;
			}
			h4 {
				font-size: 20px;
				margin: 0 0 15px;
			}
			p {
				font-size: 13px;
				margin:0 0 6px;
				&.wp-smush-scan-hold-on-notice {
					font-size: 14px;
					margin-bottom:0;
					opacity:1;
					animation: fadeIn 3s;
					&.sui-hidden {
						animation:none;
					}

				}
			}
			strong {
				font-weight: 700;
			}
		}
		.sui-progress {
			position: absolute;
			bottom: 0;
			left: 0;
			right: 0;
			.sui-progress-bar{
				height: var( --progress-bar-height );
			}
			.wp-smush-progress-inner{
				transition: width var(--progress-transition-duration) ease-in-out;
			}
		}
	}

	.smush-settings-wrapper {
		.smush-png2jpg-setting-note {
			p span {
				display: block;
				margin-top:20px;
			}
		}
	}

	.wp-smush-recheck-images-notice-box {
		> .sui-notice {
			animation: fadeIn 3s;
			&.sui-hidden {
				animation: none;
			}
		}
	}

	.sui-tag.smush-sui-tag-new {
		min-height: 12px;
		padding: 2px 8px 1px;
		border: 0;
		border-radius: 6px;
		font-size: 8px;
		line-height: 9px;
		text-align: center;
		text-transform: uppercase;
		background-color: #1ABC9C;
		color:#fff;
	}
	.wp-smush-sui-cross-sell-modules {
		[class*=sui-cross-] {
			position: relative;
			>span {
				width: 60px!important;
				height: 60px!important;
				border:none !important;
				border-radius: unset !important;
				box-shadow: none !important;
				background-size: 60px 60px!important;
			}
			@media screen and ( min-width:600px ) {
				&>span {
					position: absolute!important;
					top: 50% !important;
					left: 50% !important;
					transform: translate( -50%, -50% )!important;
				}
			}
		}
	}
	.wp-smush-link-in-progress {
		cursor: progress !important;
	}

	.wp-smush-lossy-level-tabs {
		margin-top:8px!important;
		.sui-tab-content {
			p {
				font-size: 13px;
				line-height: 22px;
				.sui-notice-icon {
					position: relative;
					top:2px;
					margin-right: 5px;
				}
			}
		}
	}

	.sui-modal .smush-retry-modal .sui-box {
		.sui-notice-icon {
			&:before {
				color:$red;
			}
			&.sui-warning-icon:before {
				color:$yellow;
			}
		}
		.sui-box-title {
			margin-top: 7px;
			margin-bottom: 5px;
		}
		.sui-description a {
			text-decoration: underline;
		}
		.sui-box-footer {
			.sui-button {
				min-width: 0;
				+ .sui-button-ghost {
					margin-left: 17px;
				}
			}
		}
	}

	/**
	 * Fixes the headers and footers with .sui-actions-right on mobiles.
	 *
	 * @todo Remove when there's a fix for this in SUI, which isn't planned atm.
	 * @since 3.9.0
	 */
	@include media(max-width, sm) {
		.sui-wrap {

			.sui-box-header {

				.sui-box-title {
					flex: 1;
				}

				.sui-actions-right {
					flex: 1;
					flex-direction: column;
					align-items: flex-end;
				}
			}

			.sui-box-footer .sui-actions-right {
				flex-direction: column;
				align-items: flex-end;
			}
		}
	}
}
