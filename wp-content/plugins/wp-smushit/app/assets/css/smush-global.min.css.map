{"version": 3, "file": "css/smush-global.min.css", "mappings": "AAEC,8BACC,kBAEA,yEAEC,YACA,UACA,2BACA,oBACA,kBACA,aACA,eAGD,qCACC,SACA,YACA,+BACA,qBCkRqC,CDjRrC,2BAGD,oCACC,2BACA,qCACA,SACA,YACA,mBACA,iBACA,kBACA,qBCsQqC,CDrQrC,sBACA,2BACA,UCwDqC,CDvDrC,6CACA,sBEnBoB,CFoBpB,oBACA,6CG8CD,yBH7DA,oCAkBE,oBAOD,4DACC,sCACA,mBAUA,yDACC,OACA,wBAOD,0DACC,QACA,WACA,wBAYD,mLACC,SACA,aACA,+BACA,wBC6MmC,CD1MpC,gLACC,SACA,aACA,gBACA,gBAOD,4DACC,OACA,uBAOD,6DACC,WACA,QACA,uBAQF,sDACC,QACA,WACA,aACA,WACA,+BACA,sBCqKoC,CDpKpC,2BAGD,qDACC,QACA,WACA,aACA,WACA,kBACA,gBACA,2BAOD,uDACC,QACA,aACA,UACA,+BACA,uBC8IoC,CD7IpC,2BAGD,sDACC,QACA,aACA,UACA,iBACA,gBACA,2BGvEF,yBHmFE,uDAGE,6CACA,oBGvFJ,yBHmGG,+LAGE,UACA,YACA,SACA,YACA,qBCiGiC,CDhGjC,iCACA,kCACA,gCACA,4BG9GL,yBHkHG,4LAGE,6CACA,UACA,YACA,aACA,eACA,mBACA,cACA,oBG5HL,yBHiIE,2DAGE,YACA,SACA,4BGtIJ,yBH0IE,gEAGE,YACA,OACA,yBG/IJ,yBHmJE,iEAGE,QACA,WACA,yBGxJJ,yBHoKG,wMAGE,SACA,aACA,SACA,YACA,+BACA,iCACA,wBC8BiC,CD7BjC,gCACA,4BG/KL,yBHmLG,qMAGE,6CACA,SACA,aACA,gBACA,eACA,gBACA,cACA,oBG7LL,yBHkME,8DAGE,YACA,SACA,4BGvMJ,yBH2ME,mEAGE,YACA,OACA,yBGhNJ,yBHoNE,oEAGE,QACA,WACA,yBGzNJ,yBHiOE,6DAGE,QACA,WACA,aACA,WACA,+BACA,iCACA,kCACA,sBChCkC,CDiClC,4BG5OJ,yBHgPE,4DAGE,6CACA,QACA,WACA,aACA,WACA,aACA,kBACA,gBACA,cACA,2BACA,oBG7PJ,yBHqQE,8DAGE,QACA,YACA,aACA,UACA,+BACA,uBClEkC,CDmElC,kCACA,gCACA,4BGhRJ,yBHoRE,6DAGE,6CACA,QACA,YACA,aACA,UACA,aACA,eACA,gBACA,iBACA,2BACA,oBAWH,0KAEC,UACA,4BIzXJ,8BACC,YAKA,yBACC,iBACA,sCACC,eAQF,kDACC,WAEA,kEACC,mBAKF,wCACC,cACA,eAED,4EACC,cACA,iBAED,sBACC,qBAED,yBACC,sBAED,sCACC,cACA,wCACC,cACA,8CACC,gBACA,WAMH,oFACC,kBACA,kBACA,yGACC,WACA,iEACA,kBACA,WACA,YACA,wBACA,OACA,QAID,mCACC,oEAID,mCACC,oEAGF,0CACC,iBACA,kBACA,QAGF,8BACC,cAIA,6CACC,WACA,yBACA,kBACA,eACA,yBACA,iBAEC,yEACC,iBACA,2CACA,cACA,eACA,iBACA,uBACA,iBACA,gBAGF,gDACC,yBAED,gDACC,yBACA,sBACA,iBACA,WACA,eACA,uBACA,iBACA,gCACA,8DACC,gBACA,gBAUD,uDACC,2CAOJ,sCAGG,uDACC,mBACA,sBAED,0DACC,qBACA,8BACA,sEACC,kBACA,sBAED,qEACC,sBACA,WACA,kBAQL,oDAEC,eAKA,4CACC,mBACA,yBACA,yBACA,WACA,8FAEC,yBAGF,0BACC,YAED,yBACC,WACA,kBAKF,iBACC,aAID,uBACC,kBACA,UCjND,2DAEC,aJoEc,CInEd,mFACC,aJkEa,CIhEd,6TAGC,cACA,qYACC,cASH,qCACC,iCACA,kCAEA,mDACC,gBACA,6CACA,eACA,oBAGD,yDACC,WAGD,sDACC,WAGD,uCACC,gBAGD,qDACC,cACA,iBAIF,yEAEC,WAGD,sCACC,gBACA,aAGD,mBACC,kBACA,SACA,0BACA,mBACA,kCACA,eACA,cACA,eAID,oDACC,WACA,cACA,iBAID,mBACC,cAGD,sBACC,cAGD,oBACC,UAGD,gCACC,UAGD,2BACC,WAGD,iCACC,aAGD,yCACC,eAGD,0BACC,cAGD,sCACC,cAGD,gDACC,eAGD,sCACC,gBACA,gBAIA,2CACA,uCACC,gBACA,iBACA,eACA,iBACA,gBACA,yBACA,WACA,+BACA,mBAKF,oBACC,eAID,iBACC,yBACA,kBACA,WACA,gCACA,eACA,iBACA,mBACA,kBACA,wBAGD,uDACC,iBACA,yBACA,gBACA,kBACA,YACA,iBACA,mBACA,mCAEA,6DACC,YAIF,6DAEC,cAIA,kDACC,WACA,eACA,iBACA,sBACA,gBAGD,8DACC,WACA,eACA,aACA,mBAGD,uFAEC,mBAKF,kDAEC,yBACA,YACA,WACA,iBACA,eACA,wBAGD,2BACC,yBAGD,uBACC,yBAGD,gBACC,WACA,eACA,gBACA,gBAKD,wBACC,eAID,2BACC,mBACA,kBACA,eACA,gBACA,aAGD,oCACC,kBAGD,8BACC,YAGD,wFAGC,kBACA,iBAGD,6GAGC,WACA,kBACA,+BACA,yBACA,YACA,SACA,iBACA,mBACA,UACA,WACA,yDACA,oBAGD,0GAGC,mBACA,kBACA,YACA,WACA,sBACA,eACA,gBACA,SACA,iBACA,mBACA,kBACA,UACA,YACA,oBACA,kBACA,YACA,kBACA,yDACA,qBACA,WAGD,0JAGC,+BACA,0BACA,SACA,OAGD,uJAGC,cACA,YACA,UACA,eACA,kBAGD,yDACC,UAGD,2EACC,YACA,kBACA,YAGD,4EACC,iBAGD,wIAGC,YACA,kBAGD,wIAGC,YACA,mBAGD,4FACC,mBAGD,gHACC,mBAGD,gHACC,mBAGD,gGACC,2BACA,4BACA,SACA,YACA,gBACA,gBAGD,8FACC,YACA,SACA,eACA,gBAGD,+HAGC,UACA,gBACA,UACA,kCAGD,4HAGC,UACA,UACA,mBACA,kCAGD,6QAMC,aAID,yBACC,gBAEA,6CACC,gBACA,kBAGD,qCACC,gBACA,WAIF,yBACC,eACA,wBACA,gBACA,aAKD,4DAEC,wBACC,eAKF,0CACC,+CACC,mBAOF,qBACC,UACA,kBACA,yBACA,gBACA,gBAEA,kBAEA,uCACA,kBACA,UACA,gBACA,cACA,8BAGD,oCACC,2BACA,kBACA,OACA,QACA,MACA,SACA,sCACA,UACA,kBACA,iBACA,eACA,iBAGD,kBACC,mBACA,sBACA,eACA,gBAGD,iCAEC,sBACA,oBACA,qBACA,iBAGD,mBACC,kBAGD,sBACC,cACA,eACA,mBACA,iBACA,aACA,mCACA,WAGD,kBACC,mBACA,eACA,kBACA,mBAGD,0FAEC,sBAGD,gDACC,sBACA,iBACA,SACA,eACA,gBACA,kBACA,eACA,gBASA,+EACC,gBACA,gBACA,cACA,qFACC,eACA,gCACA,oBACA,mBACA,uBACA,2GACC,mBAMJ,uCACC,gBAGD,wCACC,qBACC,cACA,eAGD,iCACC,WACA,qBACA,YACA,YACA,kBAGD,oCACC,WACA,cACA,gBAGD,gCACC,cACA,6BACA,cACA,kBACA,mBACA,iBACA,kBAGD,qGAEC,gBAIF,uCACC,qGAEC,cACA,sBACA,eACA,kBACA,eACA,aAIF,oB", "sources": ["webpack://wp-smushit/./node_modules/@wpmudev/shared-ui/scss/_tooltips.scss", "webpack://wp-smushit/./node_modules/@wpmudev/shared-ui/scss/_colors.scss", "webpack://wp-smushit/./node_modules/@wpmudev/shared-ui/scss/_variables.scss", "webpack://wp-smushit/./node_modules/@wpmudev/shared-ui/scss/_mixins.scss", "webpack://wp-smushit/./_src/scss/modules/_media.scss", "webpack://wp-smushit/./_src/scss/common.scss"], "sourcesContent": ["@include body-class(true) {\n\n\t.sui-tooltip {\n\t\tposition: relative;\n\n\t\t&:before,\n\t\t&:after {\n\t\t\tcontent: \" \";\n\t\t\topacity: 0;\n\t\t\tbackface-visibility: hidden;\n\t\t\tpointer-events: none;\n\t\t\tposition: absolute;\n\t\t\tz-index: 9990;\n\t\t\ttransition: 0.2s;\n\t\t}\n\n\t\t&:before {\n\t\t\tleft: 50%;\n\t\t\tbottom: 100%;\n\t\t\tborder: 5px solid transparent;\n\t\t\tborder-top-color: $tooltips-color;\n\t\t\ttransform: translateX(-50%);\n\t\t}\n\n\t\t&:after {\n\t\t\tcontent: attr(data-tooltip);\n\t\t\tmin-width: var(--tooltip-width, 40px);\n\t\t\tleft: 50%;\n\t\t\tbottom: 100%;\n\t\t\tmargin-bottom: 10px;\n\t\t\tpadding: 8px 12px;\n\t\t\tborder-radius: $border-radius;\n\t\t\tbackground-color: $tooltips-color;\n\t\t\tbox-sizing: border-box;\n\t\t\ttransform: translateX(-50%);\n\t\t\tcolor: $white;\n\t\t\tfont: 500 12px/18px $font;\n\t\t\tletter-spacing: $font--letter-spacing;\n\t\t\ttext-transform: none;\n\t\t\ttext-align: var(--tooltip-text-align, center);\n\n\t\t\t@include media(min-width, md) {\n\t\t\t\twhite-space: nowrap;\n\t\t\t}\n\t\t}\n\n\t\t// VARIATION: Constrained tooltip\n\t\t&.sui-tooltip-constrained {\n\n\t\t\t&:after {\n\t\t\t\tmin-width: var(--tooltip-width, 240px);\n\t\t\t\twhite-space: normal;\n\t\t\t}\n\t\t}\n\n\t\t// POSITION: Top\n\t\t&.sui-tooltip-top {\n\n\t\t\t// POSITION: Left\n\t\t\t&-left {\n\n\t\t\t\t&:after {\n\t\t\t\t\tleft: 0;\n\t\t\t\t\ttransform: translateX(0);\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t// POSITION: Right\n\t\t\t&-right {\n\n\t\t\t\t&:after {\n\t\t\t\t\tright: 0;\n\t\t\t\t\tleft: unset;\n\t\t\t\t\ttransform: translateX(0);\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t// POSITION: Bottom\n\t\t&.sui-tooltip-bottom {\n\n\t\t\t&,\n\t\t\t&-left,\n\t\t\t&-right {\n\n\t\t\t\t&:before {\n\t\t\t\t\ttop: 100%;\n\t\t\t\t\tbottom: unset;\n\t\t\t\t\tborder-top-color: transparent;\n\t\t\t\t\tborder-bottom-color: $tooltips-color;\n\t\t\t\t}\n\t\n\t\t\t\t&:after {\n\t\t\t\t\ttop: 100%;\n\t\t\t\t\tbottom: unset;\n\t\t\t\t\tmargin-top: 10px;\n\t\t\t\t\tmargin-bottom: 0;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t// POSITION: Left\n\t\t\t&-left {\n\n\t\t\t\t&:after {\n\t\t\t\t\tleft: 0;\n\t\t\t\t\ttransform: translate(0);\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t// POSITION: Right\n\t\t\t&-right {\n\n\t\t\t\t&:after {\n\t\t\t\t\tleft: unset;\n\t\t\t\t\tright: 0;\n\t\t\t\t\ttransform: translate(0);\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t// POSITION: Left\n\t\t&.sui-tooltip-left {\n\n\t\t\t&:before {\n\t\t\t\ttop: 50%;\n\t\t\t\tright: 100%;\n\t\t\t\tbottom: unset;\n\t\t\t\tleft: unset;\n\t\t\t\tborder-top-color: transparent;\n\t\t\t\tborder-left-color: $tooltips-color;\n\t\t\t\ttransform: translateY(-50%);\n\t\t\t}\n\n\t\t\t&:after {\n\t\t\t\ttop: 50%;\n\t\t\t\tright: 100%;\n\t\t\t\tbottom: unset;\n\t\t\t\tleft: unset;\n\t\t\t\tmargin-right: 10px;\n\t\t\t\tmargin-bottom: 0;\n\t\t\t\ttransform: translateY(-50%);\n\t\t\t}\n\t\t}\n\n\t\t// POSITION: Right\n\t\t&.sui-tooltip-right {\n\n\t\t\t&:before {\n\t\t\t\ttop: 50%;\n\t\t\t\tbottom: unset;\n\t\t\t\tleft: 100%;\n\t\t\t\tborder-top-color: transparent;\n\t\t\t\tborder-right-color: $tooltips-color;\n\t\t\t\ttransform: translateY(-50%);\n\t\t\t}\n\n\t\t\t&:after {\n\t\t\t\ttop: 50%;\n\t\t\t\tbottom: unset;\n\t\t\t\tleft: 100%;\n\t\t\t\tmargin-left: 10px;\n\t\t\t\tmargin-bottom: 0;\n\t\t\t\ttransform: translateY(-50%);\n\t\t\t}\n\t\t}\n\n\t\t// VARIATION: Mobile\n\t\t&.sui-tooltip {\n\n\t\t\t// VARIATION: Constrained tooltip\n\t\t\t// Use this variation in case you need to keep tooltip position but\n\t\t\t// constrain its content.\n\t\t\t&-mobile {\n\n\t\t\t\t&:after {\n\n\t\t\t\t\t@include media(max-width, md) {\n\t\t\t\t\t\tmin-width: var(--tooltip-width-mobile, 120px);\n\t\t\t\t\t\twhite-space: normal;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t// POSITION: Top\n\t\t\t&-top {\n\n\t\t\t\t&-mobile,\n\t\t\t\t&-left-mobile,\n\t\t\t\t&-right-mobile {\n\n\t\t\t\t\t&:before {\n\n\t\t\t\t\t\t@include media(max-width, md) {\n\t\t\t\t\t\t\ttop: unset;\n\t\t\t\t\t\t\tbottom: 100%;\n\t\t\t\t\t\t\tleft: 50%;\n\t\t\t\t\t\t\tright: unset;\n\t\t\t\t\t\t\tborder-top-color: $tooltips-color;\n\t\t\t\t\t\t\tborder-right-color: transparent;\n\t\t\t\t\t\t\tborder-bottom-color: transparent;\n\t\t\t\t\t\t\tborder-left-color: transparent;\n\t\t\t\t\t\t\ttransform: translateX(-50%);\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\n\t\t\t\t\t&:after {\n\n\t\t\t\t\t\t@include media(max-width, md) {\n\t\t\t\t\t\t\tmin-width: var(--tooltip-width-mobile, 120px);\n\t\t\t\t\t\t\ttop: unset;\n\t\t\t\t\t\t\tbottom: 100%;\n\t\t\t\t\t\t\tmargin-top: 0;\n\t\t\t\t\t\t\tmargin-right: 0;\n\t\t\t\t\t\t\tmargin-bottom: 10px;\n\t\t\t\t\t\t\tmargin-left: 0;\n\t\t\t\t\t\t\twhite-space: normal;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t&-mobile:after {\n\n\t\t\t\t\t@include media(max-width, md) {\n\t\t\t\t\t\tright: unset;\n\t\t\t\t\t\tleft: 50%;\n\t\t\t\t\t\ttransform: translateX(-50%);\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t&-left-mobile:after {\n\n\t\t\t\t\t@include media(max-width, md) {\n\t\t\t\t\t\tright: unset;\n\t\t\t\t\t\tleft: 0;\n\t\t\t\t\t\ttransform: translateX(0);\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t&-right-mobile:after {\n\n\t\t\t\t\t@include media(max-width, md) {\n\t\t\t\t\t\tright: 0;\n\t\t\t\t\t\tleft: unset;\n\t\t\t\t\t\ttransform: translateX(0);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t// POSITION: Bottom\n\t\t\t&-bottom {\n\n\t\t\t\t&-mobile,\n\t\t\t\t&-left-mobile,\n\t\t\t\t&-right-mobile {\n\n\t\t\t\t\t&:before {\n\n\t\t\t\t\t\t@include media(max-width, md) {\n\t\t\t\t\t\t\ttop: 100%;\n\t\t\t\t\t\t\tbottom: unset;\n\t\t\t\t\t\t\tleft: 50%;\n\t\t\t\t\t\t\tright: unset;\n\t\t\t\t\t\t\tborder-top-color: transparent;\n\t\t\t\t\t\t\tborder-right-color: transparent;\n\t\t\t\t\t\t\tborder-bottom-color: $tooltips-color;\n\t\t\t\t\t\t\tborder-left-color: transparent;\n\t\t\t\t\t\t\ttransform: translateX(-50%);\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\n\t\t\t\t\t&:after {\n\n\t\t\t\t\t\t@include media(max-width, md) {\n\t\t\t\t\t\t\tmin-width: var(--tooltip-width-mobile, 120px);\n\t\t\t\t\t\t\ttop: 100%;\n\t\t\t\t\t\t\tbottom: unset;\n\t\t\t\t\t\t\tmargin-top: 10px;\n\t\t\t\t\t\t\tmargin-right: 0;\n\t\t\t\t\t\t\tmargin-bottom: 0;\n\t\t\t\t\t\t\tmargin-left: 0;\n\t\t\t\t\t\t\twhite-space: normal;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t&-mobile:after {\n\n\t\t\t\t\t@include media(max-width, md) {\n\t\t\t\t\t\tright: unset;\n\t\t\t\t\t\tleft: 50%;\n\t\t\t\t\t\ttransform: translateX(-50%);\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t&-left-mobile:after {\n\n\t\t\t\t\t@include media(max-width, md) {\n\t\t\t\t\t\tright: unset;\n\t\t\t\t\t\tleft: 0;\n\t\t\t\t\t\ttransform: translateX(0);\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t&-right-mobile:after {\n\n\t\t\t\t\t@include media(max-width, md) {\n\t\t\t\t\t\tright: 0;\n\t\t\t\t\t\tleft: unset;\n\t\t\t\t\t\ttransform: translateX(0);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t// POSITION: Left\n\t\t\t&-left-mobile {\n\n\t\t\t\t&:before {\n\n\t\t\t\t\t@include media(max-width, md) {\n\t\t\t\t\t\ttop: 50%;\n\t\t\t\t\t\tright: 100%;\n\t\t\t\t\t\tbottom: unset;\n\t\t\t\t\t\tleft: unset;\n\t\t\t\t\t\tborder-top-color: transparent;\n\t\t\t\t\t\tborder-right-color: transparent;\n\t\t\t\t\t\tborder-bottom-color: transparent;\n\t\t\t\t\t\tborder-left-color: $tooltips-color;\n\t\t\t\t\t\ttransform: translateY(-50%);\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t&:after {\n\n\t\t\t\t\t@include media(max-width, md) {\n\t\t\t\t\t\tmin-width: var(--tooltip-width-mobile, 120px);\n\t\t\t\t\t\ttop: 50%;\n\t\t\t\t\t\tright: 100%;\n\t\t\t\t\t\tbottom: unset;\n\t\t\t\t\t\tleft: unset;\n\t\t\t\t\t\tmargin-top: 0;\n\t\t\t\t\t\tmargin-right: 10px;\n\t\t\t\t\t\tmargin-bottom: 0;\n\t\t\t\t\t\tmargin-left: 0;\n\t\t\t\t\t\ttransform: translateY(-50%);\n\t\t\t\t\t\twhite-space: normal;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t// POSITION: Right\n\t\t\t&-right-mobile {\n\n\t\t\t\t&:before {\n\n\t\t\t\t\t@include media(max-width, md) {\n\t\t\t\t\t\ttop: 50%;\n\t\t\t\t\t\tright: unset;\n\t\t\t\t\t\tbottom: unset;\n\t\t\t\t\t\tleft: 100%;\n\t\t\t\t\t\tborder-top-color: transparent;\n\t\t\t\t\t\tborder-right-color: $tooltips-color;\n\t\t\t\t\t\tborder-bottom-color: transparent;\n\t\t\t\t\t\tborder-left-color: transparent;\n\t\t\t\t\t\ttransform: translateY(-50%);\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t&:after {\n\n\t\t\t\t\t@include media(max-width, md) {\n\t\t\t\t\t\tmin-width: var(--tooltip-width-mobile, 120px);\n\t\t\t\t\t\ttop: 50%;\n\t\t\t\t\t\tright: unset;\n\t\t\t\t\t\tbottom: unset;\n\t\t\t\t\t\tleft: 100%;\n\t\t\t\t\t\tmargin-top: 0;\n\t\t\t\t\t\tmargin-right: 0;\n\t\t\t\t\t\tmargin-bottom: 0;\n\t\t\t\t\t\tmargin-left: 10px;\n\t\t\t\t\t\ttransform: translateY(-50%);\n\t\t\t\t\t\twhite-space: normal;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t// STATE: Hover\n\t\t// Show tooltips when user hovers on it.\n\t\t&:hover,\n\t\t&:focus {\n\n\t\t\t&:before,\n\t\t\t&:after {\n\t\t\t\topacity: 1;\n\t\t\t\tbackface-visibility: visible;\n\t\t\t}\n\t\t}\n\t}\n}", "// Colors\n$palettes: (\n\tmono: (\n\t\tdefault: #000000,\n\t\tblack:   #000000,\n\t\twhite:   #FFFFFF,\n\t),\n\tgray: (\n\t\tdefault: #666666,\n\t\tlighter: #DDDDDD,\n\t\tlight:   #888888,\n\t\tdark:    #333333,\n\t),\n\tsilver: (\n\t\tdefault: #F2F2F2,\n\t\tlight:   #F8F8F8,\n\t\tsoft:    #E6E6E6,\n\t\tmedium:  #AAAAAA,\n\t),\n\tblue: (\n\t\tdefault: #17A8E3,\n\t\tlight:   #E1F6FF,\n\t\tghost:   #E1F6FF,\n\t),\n\tred: (\n\t\tdefault: #FF6D6D,\n\t\tlight:   #FFE5E9,\n\t\tghost:   #FFE5E9,\n\t),\n\tyellow: (\n\t\tdefault: #FECF2F,\n\t\tlight:   #FFF5D5,\n\t\tghost:   #FFF5D5,\n\t),\n\tgreen: (\n\t\tdefault: #1ABC9C,\n\t\tlight:   #D1F1EA,\n\t\tghost:   #D1F1EA,\n\t),\n\torange: (\n\t\tdefault: #FF7E41,\n\t\tlight:   #FFE5D9,\n\t\tghost:   #FFE5D9,\n\t),\n\tpurple: (\n\t\tdefault: #8D00B1,\n\t\tlight:   #F9E1FF,\n\t\tghost:   #F9E1FF,\n\t),\n\tmonos: (\n\t\tcloud:       #FAFAFA,\n\t\thaze:        #F8F8F8,\n\t\tsmoke:       #F2F2F2,\n\t\tsilver:      #E6E6E6,\n\t\tovercast:    #DDDDDD,\n\t\tfiftyshades: #AAAAAA,\n\t\tgrey:        #888888,\n\t\tironmike:    #666666,\n\t\tnightrider:  #333333,\n\t),\n) !default;\n\n$cloud:        palette(monos, cloud)       !default;\n$haze:         palette(monos, haze)        !default;\n$smoke:        palette(monos, smoke)       !default;\n$silver:       palette(monos, silver)    !default;\n$overcast:     palette(monos, overcast)    !default;\n$fiftyshades:  palette(monos, fiftyshades) !default;\n$grey:         palette(monos, grey)      !default;\n$ironmike:     palette(monos, ironmike)    !default;\n$nightrider:   palette(monos, nightrider)  !default;\n$blue:         palette(blue, default)    !default;\n$green:        palette(green, default)   !default;\n$yellow:       palette(yellow, default)  !default;\n$red:          palette(red, default)     !default;\n$purple:       palette(purple, default)  !default;\n$blue-ghost:   palette(blue, ghost)      !default;\n$green-ghost:  palette(green, ghost)     !default;\n$yellow-ghost: palette(yellow, ghost)    !default;\n$red-ghost:    palette(red, ghost)       !default;\n$purple-ghost: palette(purple, ghost)    !default;\n\n// ============================================================\n// Old colors (organization)\n\n$success:                               palette(green)                !default;\n$warning:                               palette(yellow)               !default;\n$error:                                 palette(red)                  !default;\n$info:                                  palette(blue)                 !default;\n\n// Primary Colors\n$white:                                 palette(mono, white)          !default;\n$black:                                 palette(mono, black)          !default;\n$gray:                                  palette(gray)                 !default;\n$gray-alt:                              palette(gray, dark)           !default;\n\n// Boxes Colors (_boxes.scss)\n$box-bg-color:                          palette(mono, white)          !default;\n$box-box-shadow-color:                  palette(silver, soft)          !default;\n$box-header-border-color:               palette(silver, soft)          !default;\n$box-footer-border-color:               palette(silver, soft)          !default;\n$box-settings-box-border-color:         palette(silver, soft)         !default;\n$box-settings-label-color:              palette(gray, dark)           !default;\n$box-upsell-p-color:                    palette(gray, dark)           !default;\n$box-upsell-border-color:               palette(purple)                !default;\n\n// ============================================================\n// Buttons Colors (_buttons.scss)\n\n$button-colors: blue green red orange yellow purple white;\n\n$button-shadow: (\n\tdefault: $overcast,\n\tblue:    $blue-ghost,\n\tgreen:   $green-ghost,\n\tred:     $red-ghost,\n\torange:  palette(orange, ghost),\n\tyellow:  $yellow-ghost,\n\tpurple:  $purple-ghost,\n\twhite:   $white\n) !default;\n\n$button-border: (\n\tdefault: palette(gray, lighter),\n\tblue:    palette(blue, light),\n\tgreen:   palette(green, light),\n\tred:     palette(red, light),\n\torange:  palette(orange, light),\n\tyellow:  palette(yellow, light),\n\tpurple:  palette(purple, light),\n\twhite:   palette(mono, white)\n) !default;\n\n$button-background: (\n\tdefault: palette(gray, light),\n\tblue:    palette(blue, default),\n\tgreen:   palette(green, default),\n\tred:     palette(red, default),\n\torange:  palette(orange, default),\n\tyellow:  palette(yellow, default),\n\tpurple:  palette(purple, default),\n\twhite:   palette(mono, white)\n) !default;\n\n$button-text-color: (\n\tdefault: palette(mono, white),\n\tblue:    palette(mono, white),\n\tgreen:   palette(mono, white),\n\tred:     palette(mono, white),\n\torange:  palette(mono, white),\n\tyellow:  palette(gray, dark),\n\tpurple:  palette(mono, white),\n\twhite:   palette(mono, black)\n) !default;\n\n$button-disabled--background: palette(silver, soft);\n$button-disabled--color:      palette(silver, medium);\n\n// Upsell Button\n$button-upsell--border-static: palette(green, light)   !default;\n$button-upsell--border-active: palette(green, default) !default;\n$button-upsell--color-static:  palette(green, default) !default;\n$button-upsell--color-active:  palette(mono, white)    !default;\n\n// Dashed Button\n$button-dashed--border:        palette(silver, medium) !default;\n$button-dashed--background:    transparent               !default;\n$button-dashed--color:         palette(gray, light)    !default;\n\n// ============================================================\n// Icon Buttons Colors (_buttons.scss)\n\n$button-icon--shadow: (\n\tdefault: $overcast,\n\tblue:    $blue-ghost,\n\tgreen:   $green-ghost,\n\tred:     $red-ghost,\n\torange:  palette(orange, ghost),\n\tyellow:  $yellow-ghost,\n\tpurple:  $purple-ghost,\n\twhite:   $white\n) !default;\n\n$button-icon--border: (\n\tdefault: palette(gray, lighter),\n\tblue:    palette(blue, light),\n\tgreen:   palette(green, light),\n\tred:     palette(red, light),\n\torange:  palette(orange, light),\n\tyellow:  palette(yellow, light),\n\tpurple:  palette(purple, light),\n\twhite:   palette(mono, white)\n) !default;\n\n$button-icon--background: (\n\tdefault: palette(silver, default),\n\tblue:    palette(blue, light),\n\tgreen:   palette(green, light),\n\tred:     palette(red, light),\n\torange:  palette(orange, light),\n\tyellow:  palette(yellow, light),\n\tpurple:  palette(purple, light),\n\twhite:   rgba(0,0,0,0.2)\n) !default;\n\n$button-icon--background-hover: (\n\tdefault: palette(gray, light),\n\tblue:    palette(blue, default),\n\tgreen:   palette(green, default),\n\tred:     palette(red, default),\n\torange:  palette(orange, default),\n\tyellow:  palette(yellow, default),\n\tpurple:  palette(purple, default),\n\twhite:   palette(mono, white)\n) !default;\n\n$button-icon--background-filled: (\n\tdefault: palette(silver, soft),\n\tblue:    palette(blue, default),\n\tgreen:   palette(green, default),\n\tred:     palette(red, default),\n\torange:  palette(orange, default),\n\tyellow:  palette(yellow, default),\n\tpurple:  palette(purple, default),\n\twhite:   palette(mono, white)\n) !default;\n\n$button-icon--color: (\n\tdefault: palette(gray, light),\n\tblue:    palette(blue, default),\n\tgreen:   palette(green, default),\n\tred:     palette(red, default),\n\torange:  palette(orange, default),\n\tyellow:  palette(yellow, default),\n\tpurple:  palette(purple, default),\n\twhite:   palette(mono, white)\n) !default;\n\n$button-icon--color-hover: (\n\tdefault: palette(gray, dark),\n\tblue:    palette(blue, default),\n\tgreen:   palette(green, default),\n\tred:     palette(red, default),\n\torange:  palette(orange, default),\n\tyellow:  palette(yellow, default),\n\tpurple:  palette(purple, default),\n\twhite:   palette(mono, white)\n) !default;\n\n$button-icon--alt: (\n\tdefault: palette(mono, white),\n\tblue:    palette(mono, white),\n\tgreen:   palette(mono, white),\n\tred:     palette(mono, white),\n\torange:  palette(mono, white),\n\tyellow:  palette(gray, dark),\n\tpurple:  palette(mono, white),\n\twhite:   palette(mono, black)\n) !default;\n\n$button-icon--color-filled: (\n\tdefault: palette(silver, medium),\n\tblue:    palette(mono, white),\n\tgreen:   palette(mono, white),\n\tred:     palette(mono, white),\n\torange:  palette(mono, white),\n\tyellow:  palette(gray, dark),\n\tpurple:  palette(mono, white),\n\twhite:   palette(mono, white)\n) !default;\n\n// ============================================================\n// Typography Colors (_typography.scss)\n$headings-color:                        palette(gray, dark)           !default;\n$font-color:                            $gray !default;\n$p-small-color:                         palette(gray, light)          !default;\n$a-color:                               palette(blue)                 !default;\n$a-action-color:                        darken($a-color, 10%)         !default;\n$a-disabled-color:                      palette(gray, light)          !default;\n$code-bg-color:                         palette(silver, light)        !default;\n$code-border-color:                     palette(silver, soft)         !default;\n$dfn-border-color:                      palette(gray, light)          !default;\n$pre-color:                             palette(gray, dark)           !default;\n\n// Notifications Colors (_notifications.scss)\n$notice-font-color:                     palette(gray, dark)           !default;\n$notice-default-bg-color:               $white                        !default;\n$notice-default-icon-color:             palette(gray, light)          !default;\n$notice-warning-icon-color:             $warning                      !default;\n$notice-success-icon-color:             $success                      !default;\n$notice-error-icon-color:               $error                        !default;\n$notice-info-icon-color:                palette(blue)                 !default;\n\n// Tooltips (_tooltips.scss)\n$tooltips-color:                        palette(gray, dark)           !default;\n\n// ============================================================\n// Box Selectors (_box-selectors.scss)\n\n// Container\n$box-selectors--background: palette(silver, light) !default;\n\n// Item\n$box-selector--border-color: rgba(230, 230, 230, 0.5) !default;\n$box-selector--background: $white !default;\n$box-selector--color: palette(gray, light) !default;\n$box-selector--box-shadow: 0 2px 7px 0 rgba(0, 0, 0, 0.05) !default;\n\n$box-selector--active-background: palette(blue, light) !default;\n$box-selector--active-color: palette(blue, default) !default;\n$box-selector--active-box-shadow:  0 0 0 4px rgba(0, 0, 0, 0.02), 0 4px 15px 0 rgba(0, 0, 0, 0.05);\n\n// Item icon\n$box-selector--icon-color: palette(silver, medium) !default;\n\n// Item ribbon\n// Used as blue triangle on top-right corner when item is selected\n$box-selector--ribbon-color: $white !default;\n$box-selector--ribbon-background: palette(blue, default) !default;\n\n// ============================================================\n// Box Builder (_box-builder.scss)\n\n// Main container\n$box-builder--background:   $white !default;\n$box-builder--border-color: palette(silver, soft) !default;\n\n// Body\n$box-builder--body-background: palette(silver, light) !default;\n$box-builder--body-message:    palette(gray, light) !default;\n\n// Fields\n$box-builder--field-background: $white                   !default;\n$box-builder--field-color:      palette(gray, dark)    !default;\n$box-builder--field-move-color: palette(gray, lighter) !default;\n$box-builder--field-move-color-hover: palette(gray, light) !default;\n$box-builder--field-border:     palette(silver, soft)  !default;\n\n// Field - Notice\n$box-builder--notice-background: palette(silver, default) !default;\n$box-builder--notice-color:      $blue !default;\n\n// ============================================================\n// Select (_select.scss)\n\n// Container\n$select-container--border-color:        palette(gray, lighter)  !default;\n$select-container--background:          #FAFAFA                 !default;\n$select-container--background-active:   $white                    !default;\n\n// List value\n$select-value--color:                   palette(gray, dark)     !default;\n\n// List results\n$select-results--border-color:          palette(gray, lighter)  !default;\n$select-results--background:            $white                    !default;\n$select-results--color:                 palette(gray, light)    !default;\n$select-results--background-hover:      rgba(51, 51, 51, 0.05)  !default;\n$select-results--color-hover:           palette(gray, default)  !default;\n$select-results--background-current:    palette(gray, light)    !default;\n$select-results--color-current:         $white                    !default;\n$select-results--optgroup-color:        palette(silver, medium) !default;\n\n// Search results\n$select-search--border-color:           palette(gray, lighter)  !default;\n$select-search--background:             #FAFAFA                 !default;\n$select-search--background-focus:       $white                    !default;\n$select-search--color:                  palette(gray, dark)     !default;\n$select-search--placeholder:            palette(silver, medium) !default;\n$select-search--result:                 palette(gray, light)    !default;\n\n// Mobile select\n$select-mobile-nav-handle-color:        palette(gray, lighter)  !default;\n$select-mobile-nav-handle-hover-color:  palette(gray, light)    !default;\n\n// ============================================================\n// Sidebar (_sidebar.scss)\n$sidenav-tab-color:                     palette(gray)                 !default;\n$sidenav-tab-color-hover:               palette(gray, dark)           !default;\n$sidenav-tab-active-color:              palette(gray, dark)           !default;\n$sidenav-tab-active-bg-color:           palette(silver, soft)         !default;\n$sidenav-tab-icon-color:                palette(green)                !default;\n\n// ============================================================\n// Summary (_summary.scss)\n$summary-details--color:     palette(gray, dark)   !default;\n$summary-details--sub-color: palette(gray, light)  !default;\n\n$summary-list--border-color: palette(silver, soft) !default;\n$summary-list--color:        palette(gray,light)   !default;\n$summary-list--label:        palette(gray, dark)   !default;\n\n// ============================================================\n// Tags (_tags.scss)\n\n// Default tags\n$tag--default-background: palette(silver) !default;\n$tag--default-color: palette(gray, dark) !default;\n\n// Red tags\n$tag--red-border-color: palette(red, light) !default;\n$tag--red-background: $error !default;\n$tag--red-color: $white !default;\n\n// Yellow tags\n$tag--yellow-border-color: palette(yellow, light) !default;\n$tag--yellow-background: $warning !default;\n$tag--yellow-color: palette(gray, dark) !default;\n\n// Green tags\n$tag--green-border-color: palette(green, light) !default;\n$tag--green-background: $success !default;\n$tag--green-color: $white !default;\n\n// Blue tags\n$tag--blue-border-color: palette(blue, light) !default;\n$tag--blue-background: palette(blue) !default;\n$tag--blue-color: $white !default;\n\n// Purple tags\n$tag--purple-border-color: palette(purple, light) !default;\n$tag--purple-background: palette(purple) !default;\n$tag--purple-color: $white;\n\n// Disabled tags\n$tag--disabled-background: palette(silver) !default;\n$tag--disabled-color: palette(silver, medium) !default;\n\n// Pro tags\n$tag--pro-background: palette(purple) !default;\n$tag--pro-color: $white !default;\n\n// Beta tags\n$tag--beta-background: palette(orange) !default;\n$tag--beta-color: $white !default;\n\n// Forms (_forms.scss)\n$form--input--disabled:\t\t\t\t\tpalette(silver, default)\t\t!default;\n$form--input-icon:\t\t\t\t\t\tpalette(silver, medium)\t\t\t!default;\n$form--input-icon-right:\t\t\t\tpalette(gray, light)\t\t\t!default;\n$form--description-color:\t\t\t\tpalette(gray, light)\t\t\t!default;\n$form--input-error-color:               $error                        !default;\n$form--input-icon-color:                palette(gray, light)          !default;\n$form--label-color:                     $fiftyshades                      !default;\n\n// ============================================================\n// Radio & Checkbox (_radio-checkbox.scss)\n$radio-checkbox--background:            #FAFAFA                !default;\n$radio-checkbox--border-color:          palette(gray, lighter) !default;\n$radio-checkbox--color:                 palette(gray)          !default;\n$radio-checkbox--checked-border-color:  $blue                    !default;\n$radio-checkbox--checked-background:    $blue                    !default;\n$radio-checkbox--disabled-border-color: palette(silver)        !default;\n$radio-checkbox--disabled-background:   palette(silver)        !default;\n\n$radio-checkbox--check-color:          $white                    !default;\n$radio-checkbox--disabled-check-color: palette(silver, medium) !default;\n\n$radio-checkbox--checked-hover-background: rgba(23, 168, 227, 0.2)    !default;\n$radio-checkbox--hover-background:   palette(silver)        !default;\n\n// ============================================================\n// Upload (_upload.scss)\n\n// Image container\n$file-upload--image-border-color:       palette(silver, soft)   !default;\n$file-upload--image-mask-background:    palette(gray, lighter)  !default;\n$file-upload--image-preview-background: rgba(0, 0, 0, 0.5)      !default;\n\n// Button: Add\n$file-upload--add-border-color:         palette(gray, lighter)  !default;\n$file-upload--add-background:           transparent               !default;\n$file-upload--add-color:                palette(silver, medium) !default;\n\n// File name\n$file-upload--file-border-color:        palette(gray, lighter)  !default;\n$file-upload--file-background:          $white                    !default;\n$file-upload--file-color:               palette(gray, dark)     !default;\n$file-upload--file-hover-background:    #FAFAFA                 !default;\n\n// ============================================================\n// Color Pickers (_colorpickers.scss)\n\n// Value\n$colorpicker--border-color:                    palette(gray, lighter) !default;\n$colorpicker--background:                      #FAFAFA                !default;\n$colorpicker--color:                           palette(gray, dark)    !default;\n\n// Preview\n$colorpicker--preview-background:              palette(gray, lighter) !default;\n\n// Iris\n$colorpicker--iris-border-color:               palette(gray, lighter) !default;\n$colorpicker--iris-background:                 $white                   !default;\n$colorpicker--iris-palette-border-color:       palette(silver, soft)  !default;\n$colorpicker--iris-square-handle-border-color: $white                   !default;\n$colorpicker--iris-slider-handle-border-color: $white                   !default;\n\n// ============================================================\n// Tabs Colors (_tabs.scss)\n$tabs-label-color:                      palette(gray, light)   !default;\n$tabs-label-checked-color:              palette(gray, light)   !default;\n$tabs-label-active-color:               palette(gray, dark)    !default;\n$tabs-label-active-border-color:        palette(gray, dark)    !default;\n$tabs-content-border-color:             palette(silver, soft)  !default;\n$side-tabs--label-color:                palette(gray, default) !default;\n$side-tabs--label-background:           palette(silver, light) !default;\n$side-tabs--label-active-color:         palette(blue, default) !default;\n$side-tabs--label-active-background:    palette(blue, light)   !default;\n\n// Modals (_modals.scss)\n$modal-overlay-bg-color:                rgba(51, 51, 51, 0.95)        !default;\n$modal-box-shadow-color:                rgba(0, 0, 0, 0.2)            !default;\n$modal-close-color:                     palette(silver, medium)       !default;\n$modal-close-action-color:              palette(gray, light)          !default;\n\n// Dropdowns (_dropdowns.scss)\n$dropdown-anchor-color:                 palette(gray, light)          !default;\n$dropdown-ul-border-color:              palette(gray, lighter)        !default;\n$dropdown-label-border-color:           palette(silver, soft)         !default;\n$dropdown-ul-before-border-color:       palette(silver, soft)         !default;\n\n// Scores (_scores.scss)\n$circle-score-success-color:            $success                      !default;\n$circle-score-warning-color:            $warning                      !default;\n$circle-score-error-color:              $error                        !default;\n$circle-score-disabled-color:           palette(silver, medium)       !default;\n$circle-score-bg-color:                 $silver                       !default;\n$circle-score-default-dial-color:       $success                      !default;\n\n// Footer (_footer.scss)\n$footer-color:                          palette(silver, medium)       !default;\n$footer-color-hover:                    palette(gray, default)        !default;\n$footer-cross-sell-border-color:        palette(silver, soft)         !default;\n$footer-cross-sell-icon-color:          palette(gray, light)          !default;\n$footer-cross-sell-p-color:             palette(gray, light)          !default;\n\n// ============================================================\n// Progress Bars (_progress-bars.scss)\n$progress-block--background:   $white;\n$progress-block--border-color: palette(silver, soft);\n\n$progress-text--color:         palette(gray, light);\n$progress-status--color:       palette(gray, light);\n\n$progress-bar--background:     palette(silver, soft);\n$progress-bar--loading-color:  $blue;\n\n// ============================================================\n// Tables (_tables.scss)\n$table--border-color:                   palette(silver, soft)       !default;\n$table--th-color:                       $headings-color               !default;\n$table--text-color:                     $font-color                   !default;\n$table--field-list-title-color:         $headings-color               !default;\n$table--title-color:                    $headings-color               !default;\n\n// ============================================================\n// Accordions (_accordions.scss)\n$accordion--content-bg-color:           palette(silver,light)         !default;\n$accordion--open-indicator-color:       palette(gray,light)           !default;\n$accordion--disabled-color:             palette(silver, medium)       !default;\n$accordion--disabled-icon:              palette(gray, lighter)        !default;\n\n$accordion--block-background:           $white                          !default;\n$accordion--block-shadow:               palette(silver, soft)         !default;\n$accordion--block-color:                palette(gray, light)          !default;\n\n// ============================================================\n// Icons (_icons.scss)\n$icon-color:                            palette(gray,light)           !default;\n$icon-lighter-color:                            palette(gray,lighter)           !default;\n\n// Lists (_list.scss)\n$list-detail-color:                     palette(gray,light)           !default;\n\n// Pagination (_pagination.scss)\n$pagination-border:                     palette(silver, soft)         !default;\n$pagination-background:                 palette(mono, white)          !default;\n$pagination-item-color-static:          palette(gray, light)          !default;\n$pagination-item-color-hover:           palette(blue, default)        !default;\n$pagination-item-color-active:          palette(gray, dark)           !default;\n$pagination-item-color-disabled:        palette(gray, lighter)        !default;\n$pagination-item-bg-active:             palette(silver, light)        !default;\n$pagination-filter-border:              palette(silver, soft)         !default;\n$pagination-results:                    palette(gray, light)          !default;\n\n// ACE Editor (_ace-editor.scss)\n$ace-selector-background:       palette(gray, default) !default;\n$ace-selector-background-hover: palette(gray, dark)    !default;\n$ace-selector-color:            $white                   !default;\n\n// Recipient (_recipient.scss)\n$recipient-border-color:       palette(silver, soft) !default;\n\n// Color Accessibility (_color-accessibility.scss)\n$accessible-light:     $white;\n$accessible-dark:      $black;\n$accessible-dark-alt:  #555555;", "@use \"sass:math\";\n\n$sui-version: \"2.12.23\";\n$sui-wrap-class: \"sui-wrap\";\n\n// Import Google Fonts\n$import-font: true !default;\n$google-fonts-url: \"https://fonts.bunny.net/css?family=Roboto:400,500,700\" !default;\n\n// Typography\n$font: \"Roboto\", Arial, sans-serif !default;\n$font--size: 15px !default;\n\n$font--weight: 400;\n$font--medium: 500;\n$font--weight-bold: 700;\n\n$font--line-height: 30px !default;\n$font--letter-spacing: -0.25px !default;\n\n$h1-font-size: 32px !default;\n$h2-font-size: 22px !default;\n$h3-font-size: 18px !default;\n$h4-font-size: 15px !default;\n$h5-font-size: 15px !default;\n$h6-font-size: 15px !default;\n\n$h1-line-height: 40px;\n$h2-line-height: 35px;\n$h3-line-height: 40px;\n\n$border-radius: 4px !default;\n$transition: all 0.3s ease !default;\n\n// Layout\n$sui-gutter: 30px !default;\n$sui-gutter-md: 20px !default;\n$sui-total-grid-cols: 12 !default;\n$sui-breakpoints: (\n\txs: 0px,\n\tsm: 480px,\n\tmd: 783px,\n\tlg: 1200px,\n) !default;\n\n// ============================================================\n// Summary\n\n// SIZE: Regular\n$summary-size-width: 222px !default;\n$summary-size-height: 212px !default;\n\n$summary-image--width: 96px !default;\n$summary-image--height: 96px !default;\n\n$summary-image--position-x: 80px !default;\n$summary-image--position-y: center !default;\n$summary-image--position: $summary-image--position-x $summary-image--position-y !default;\n\n// SIZE: Small\n$summary-size-sm: 151px !default;\n\n$summary-image-sm--width: 96px !default;\n$summary-image-sm--height: 96px !default;\n\n$summary-image-sm--position-x: 30px !default;\n$summary-image-sm--position-y: center !default;\n$summary-image-sm--position: $summary-image-sm--position-x\n\t$summary-image-sm--position-y !default;\n\n// ============================================================\n// Forms (_forms.scss)\n$form--input-height-base: 40px !default;\n$form--input-border-radius: $border-radius !default;\n$form--input-line-height: 20px !default;\n$form--input-font-weight: 500 !default;\n\n$form--input-error-font-size: 12px !default;\n$form--input-error-line-height: 16px !default;\n$form--input-error-font-weight: 500 !default;\n$form--label-font: $font !default;\n$form--label-font-weight: 600 !default;\n$form--label-font-size: 12px !default;\n$form--label-line-height: 16px !default;\n$form--description-font-weight: 400 !default;\n$form--description-font-size: 13px !default;\n\n// ============================================================\n// Radio & Checkbox (_radio-checkbox.scss)\n\n// SIZE: Default\n$radio-checkbox--size: 16px !default;\n$radio-checkbox--font-size: 15px !default;\n$radio-checkbox--line-height: 22px !default;\n$radio-checkbox--check-size: 6px !default;\n$radio-checkbox--icon-size: 10px !default;\n\n// SIZE: Small\n$radio-checkbox--font-size-sm: 13px !default;\n\n// ============================================================\n// Paths\n$sui-image-path: \"../images/\" !default;\n$sui-font-path: \"../fonts/\" !default;\n\n// ============================================================\n// Scores\n$circle-score-sm: 30px !default;\n$circle-score-lg: 120px !default;\n$circle-score-label-spacing: 10px !default;\n\n// ============================================================\n// Sidenav\n$sidenav-width: 220px !default;\n\n// ============================================================\n// Margin\n$default-margin: 30px !default;\n\n// ============================================================\n// Padding\n$default-padding: 30px !default;\n\n// ============================================================\n// Tables\n$table--border-width: 1px !default;\n$table--border-style: solid !default;\n$table--text-font-size: 13px !default;\n$table--text-line-height: 22px !default;\n\n// ============================================================\n// Select\n$select-dropdown-handle-size: 40px !default;\n$select-dropdown-handle-size-sm: 30px !default;\n\n// ============================================================\n// Accordions\n$accordion--grid: 12 !default;\n\n// ============================================================\n// Upload (_upload.scss)\n\n// Image container\n$file-upload--image-size: 40px !default;\n$file-upload--image-border-width: 1px !default;\n$file-upload--image-border-style: solid !default;\n$file-upload--image-padding: 1px !default;\n\n// Button: Add\n$file-upload--add-border-width: 1px !default;\n$file-upload--add-border-style: dashed !default;\n\n// ============================================================\n// Color Pickers (_colorpickers.scss)\n\n// Default\n$colorpicker--hex-width: 135px !default;\n$colorpicker--rgba-width: 215px !default;\n$colorpicker--button-size: 30px !default;\n$colorpicker--border-width: 1px !default;\n\n// Input\n$colorpicker--input-height: 30px !default;\n$colorpicker--input-font-size: 12px !default;\n$colorpicker--input-line-height: 16px !default;\n\n// Iris\n$colorpicker--iris-hex-width: 210px !default;\n$colorpicker--iris-rgba-width: 240px !default;\n$colorpicker--iris-square-size: 160px !default;\n$colorpicker--iris-square-size-sm: 140px !default;\n$colorpicker--iris-square-value-size: 0 !default;\n$colorpicker--iris-square-handle-size: 16px !default;\n$colorpicker--iris-square-handle-border-width: 3px !default;\n$colorpicker--iris-slider-size: 190px !default;\n$colorpicker--iris-slider-handle-size: 10px !default;\n$colorpicker--iris-slider-handle-border-width: 2px !default;\n$colorpicker--iris-palette-size: 20px !default;\n\n// ============================================================\n// Box Selectors (_box-selectors.scss)\n\n// Container\n$box-selectors--spacing: 20px !default;\n$box-selectors--columns: (\n\tcol-1: 100%,\n\tcol-2: 50%,\n\tcol-3: 33.33%,\n\tcol-4: 25%,\n\tcol-5: 20%,\n) !default;\n\n// Item\n$box-selector--height: 60px !default;\n$box-selector--padding: 10px !default;\n$box-selector--font-size: 12px !default;\n$box-selector--line-height: 20px !default;\n\n$box-selector--font-size-lg: 13px !default;\n$box-selector--line-height-lg: 22px !default;\n\n// Item icon\n$box-selector--icon-width: 30px !default;\n$box-selector--icon-size: 16px !default;\n\n// Item image\n$box-selector--image-width: 24px !default;\n\n// Item ribbon\n// Used as blue triangle on top-right corner when item is selected\n$box-selector--ribbon-height: 80px !default;\n\n// Item (vertical)\n$box-selector-vertical--height: 80px !default;\n$box-selector-vertical--padding-top: 16px !default;\n$box-selector-vertical--icon-spacing: 7px !default;\n$box-selector-vertical--padding-bottom: #{$box-selector-vertical--height -\n\t(\n\t\t$box-selector--line-height + $box-selector--icon-size +\n\t\t\t$box-selector-vertical--icon-spacing +\n\t\t\t$box-selector-vertical--padding-top\n\t)} !default;\n\n// ============================================================\n// Progress Bars (_progress-bars.scss)\n$progress-block--size: 60px;\n$progress-bar--size: 10px;\n\n// ============================================================\n// Buttons (_buttons.scss)\n$button-dashed-height-lg: 70px !default;\n$button-dashed-height: 60px !default;\n$button-dashed-height-md: 50px !default;\n\n// ============================================================\n// Box Builder (_box-builder.scss)\n\n// Field - Basic\n$box-builder--field-height: 60px !default;\n$box-builder--field-height-md: 50px !default;\n$box-builder--field-spacing: 20px !default;\n$box-builder--field-shadow: 0 2px 7px 0 rgba(0, 0, 0, 0.05) !default;\n$box-builder--field-shadow-hover: 0 0 0 4px rgba(0, 0, 0, 0.02),\n\t0 4px 15px 0 rgba(0, 0, 0, 0.05) !default;\n\n// Field - Icon\n$box-builder--field-icon-size: 30px !default;\n\n// ============================================================\n// Dialogs (_modals.scss)\n$wordpress: (\n\tadminbar: 32px,\n\tadminbar-sm: 32px,\n\tadminmenu: 160px,\n\t// Size for unfolded admin menu.\n\tadminmenu-sm: 36px,\n\t// Size for folded admin menu.\n) !default;\n\n$scrollbar--width: 20px !default;\n\n$modal: (\n\tz-index: 13,\n) !default;\n\n$modal-size: (\n\txl: 980px,\n\tlg: 600px,\n\tmd: 500px,\n\tsm: 400px,\n) !default;\n\n$modal-spacing: (\n\t0: (\n\t\t0,\n\t\t0,\n\t),\n\t10: (\n\t\t10px,\n\t\t10px,\n\t),\n\t20: (\n\t\t20px,\n\t\t20px,\n\t),\n\t30: (\n\t\t30px,\n\t\t20px,\n\t),\n\t40: (\n\t\t40px,\n\t\t20px,\n\t),\n\t50: (\n\t\t50px,\n\t\t40px,\n\t),\n\t60: (\n\t\t60px,\n\t\t40px,\n\t),\n\t70: (\n\t\t70px,\n\t\t40px,\n\t),\n\t80: (\n\t\t80px,\n\t\t40px,\n\t),\n\t90: (\n\t\t90px,\n\t\t50px,\n\t),\n\t100: (\n\t\t100px,\n\t\t50px,\n\t),\n) !default;\n\n$modal-steps: (\n\tlg: 14px,\n\tmd: 10px,\n\tsm: 7px,\n) !default;\n\n$modal-logo: (\n\tsize: 70px,\n\tframe-width: 5px,\n\tframe-color: $white,\n) !default;\n\n$onboard: (\n\tspacing: 40px,\n\tbutton: 40px,\n\tbutton-md: 30px,\n) !default;\n\n// ============================================================\n// Toggles (_toggles.scss)\n\n$toggle-width: 34px !default;\n$toggle-height: 16px !default;\n$toggle-font-size: 15px !default;\n$toggle-font-height: 22px !default;\n\n// ************************************************************\n// Datepicker - (_calendar.scss)\n\n$calendar: (\n\tpadding: #{math.div($sui-gutter, 2) - 1px},\n\tborder-width: 1px,\n\tborder-style: solid,\n\tborder-color: palette(gray, lighter),\n\tborder-radius: $border-radius,\n\tbackground: $white,\n\tshadow: 0 3px 7px 0 rgba(0, 0, 0, 0.07),\n\tcell-size: 30px,\n);\n\n$calendar-padding: map-get($calendar, padding) !default;\n$calendar-border--width: map-get($calendar, border-width) !default;\n$calendar-border--style: map-get($calendar, border-style) !default;\n$calendar-border--color: map-get($calendar, border-color) !default;\n$calendar-border--radius: map-get($calendar, border-radius) !default;\n$calendar-background: map-get($calendar, background) !default;\n$calendar-shadow: map-get($calendar, shadow) !default;\n$calendar-cell: map-get($calendar, cell-size) !default;\n\n$month: (\n\tcolor: palette(gray, dark),\n\tfont-size: 12px,\n\tline-height: map-get($calendar, cell-size),\n\tfont-family: $font,\n\tfont-weight: bold,\n\tletter-spacing: $font--letter-spacing,\n\ttext-align: center,\n);\n\n$month-color: map-get($month, color) !default;\n$month-size: map-get($month, font-size) !default;\n$month-height: map-get($month, line-height) !default;\n$month-family: map-get($month, font-family) !default;\n$month-weight: map-get($month, font-weight) !default;\n$month-spacing: map-get($month, letter-spacing) !default;\n$month-align: map-get($month, text-align) !default;\n\n$day: (\n\tfont-size: 12px,\n\tline-height: map-get($calendar, cell-size),\n\tfont-family: $font,\n\tfont-weight: 400,\n\tletter-spacing: $font--letter-spacing,\n\ttext-align: center,\n\tdefault-color: palette(gray, light),\n\tdefault-background: $white,\n\thover-color: palette(gray, dark),\n\thover-background: palette(silver, light),\n\tactive-color: $blue,\n\tactive-background: palette(blue, light),\n\tinactive-color: palette(gray, lighter),\n\tinactive-background: $white,\n\ttoday-color: palette(gray, default),\n\ttoday-background: palette(yellow, light),\n);\n\n$day-size: map-get($day, font-size) !default;\n$day-height: map-get($day, line-height) !default;\n$day-family: map-get($day, font-family) !default;\n$day-weight: map-get($day, font-weight) !default;\n$day-spacing: map-get($day, letter-spacing) !default;\n$day-align: map-get($day, text-align) !default;\n\n$day-default--color: map-get($day, default-color) !default;\n$day-default--background: map-get($day, default-background) !default;\n$day-hover--color: map-get($day, hover-color) !default;\n$day-hover--background: map-get($day, hover-background) !default;\n$day-active--color: map-get($day, active-color) !default;\n$day-active--background: map-get($day, active-background) !default;\n$day-inactive--color: map-get($day, inactive-color) !default;\n$day-inactive--background: map-get($day, inactive-background) !default;\n$day-today--color: map-get($day, today-color) !default;\n$day-today--background: map-get($day, today-background) !default;\n", "// ==================================================\n// Generates the required versioned body class.\n//\n// $wrap: true\n// $rtl: false\n// $monochrome: false\n// ==================================================\n@mixin body-class($wrap: false, $rtl: false, $monochrome: false) {\n\n\t$formatted-version: str-replace($sui-version, '.', '-');\n\n\t@if ( $wrap and $sui-wrap-class ) {\n\n\t\t@if $rtl {\n\n\t\t\t.sui-#{$formatted-version}.rtl {\n\n\t\t\t\t.#{$sui-wrap-class} {\n\n\t\t\t\t\t@if $monochrome {\n\n\t\t\t\t\t\t&.sui-color-accessible {\n\t\t\t\t\t\t\t@content;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\n\t\t\t\t\t@else {\n\t\t\t\t\t\t@content;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t@else {\n\n\t\t\t.sui-#{$formatted-version} .#{$sui-wrap-class} {\n\n\t\t\t\t@if $monochrome {\n\n\t\t\t\t\t&.sui-color-accessible {\n\t\t\t\t\t\t@content;\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t@else {\n\t\t\t\t\t@content;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\n\t@else {\n\n\t\t@if $rtl {\n\n\t\t\t.sui-#{$formatted-version}.rtl {\n\t\t\t\t@content;\n\t\t\t}\n\t\t}\n\n\t\t@else {\n\n\t\t\t.sui-#{$formatted-version} {\n\t\t\t\t@content;\n\t\t\t}\n\t\t}\n\t}\n}\n\n// Prevent text such as titles from wrapping.\n@mixin text-truncate {\n\twhite-space: nowrap;\n\toverflow: hidden;\n\ttext-overflow: ellipsis;\n}\n\n// Media queries.\n// Examples:\n//   @include media(min-width, lg) {}\n//   @include media(max-width, sm) {}\n//   @include media(between, sm, lg) {}\n//\n@mixin media($type, $breakpoint-name-1, $breakpoint-name-2: null) {\n\t@if ($type == min-width) {\n\t\t$min-breakpoint-width: #{map-get($sui-breakpoints, $breakpoint-name-1)};\n\t\t@media (min-width: $min-breakpoint-width) {\n\t\t\t@content;\n\t\t}\n\t}\n\t@else if ($type == max-width) {\n\t\t$max-breakpoint-width: map-get($sui-breakpoints, $breakpoint-name-1) - 1px;\n\t\t@media (max-width: $max-breakpoint-width) {\n\t\t\t@content;\n\t\t}\n\t}\n\t@else if ($type == between) {\n\t\t$min-breakpoint-width: map-get($sui-breakpoints, $breakpoint-name-1);\n\t\t$max-breakpoint-width: map-get($sui-breakpoints, $breakpoint-name-2) - 1px;\n\t\t@media (min-width: $min-breakpoint-width) and (max-width: $max-breakpoint-width) {\n\t\t\t@content;\n\t\t}\n\t}\n\t@else {\n\t\t@warn \"Unfortunately, no type could be retrieved from `#{$type}`. \"\n\t\t+ \"Use either `min-width`, `max-width`, or `between`.\";\n\t}\n}\n\n// High PPI display background\n@mixin background-2x($path, $ext: \"png\", $w: auto, $h: auto, $pos: left top, $repeat: no-repeat) {\n\t$at1x_path: \"#{$path}.#{$ext}\";\n\t$at2x_path: \"#{$path}@2x.#{$ext}\";\n\n\tbackground: url(\"#{$at1x_path}\") $repeat $pos;\n\tbackground-size: $w $h;\n\n\t@media only screen and (-webkit-min-device-pixel-ratio: 2),\n\tonly screen and (min--moz-device-pixel-ratio: 2),\n\tonly screen and (-o-min-device-pixel-ratio: 2/1),\n\tonly screen and (min-device-pixel-ratio: 2),\n\tonly screen and (min-resolution: 192dpi),\n\tonly screen and (min-resolution: 2dppx) {\n\t\tbackground-image: url(\"#{$at2x_path}\");\n\t}\n}\n", "/* ****************************************************************************\n * MEDIA AREA SCSS FILE\n */\n@import \"~@wpmudev/shared-ui/scss/functions\";\n@import \"~@wpmudev/shared-ui/scss/colors\";\n@import \"~@wpmudev/shared-ui/scss/variables\";\n// Override body class\n$sui-version: 'smush-media';\n$sui-wrap-class: false;\n@import \"~@wpmudev/shared-ui/scss/mixins\";\n@import \"~@wpmudev/shared-ui/scss/tooltips\";\n\n/* ****************************************************************************\n * MEDIA AREA STYLES\n */\n\n// Set column width.\n.manage-column.column-smushit {\n\twidth: 260px;\n}\n\n// Margin for buttons.\n.sui-smush-media {\n\t.button {\n\t\tmargin-right: 5px;\n\t\t&:last-of-type {\n\t\t\tmargin-right: 0;\n\t\t}\n\t}\n}\n\n// Smush button loading icon.\n#ngg-listimages,\n.column-smushit {\n\t.spinner {\n\t\tfloat: none;\n\n\t\t&.visible {\n\t\t\tvisibility: visible;\n\t\t}\n\t}\n}\n.smush-status-links{\n\t.smush-upgrade-link {\n\t\tcolor: #8D00B1;\n\t\tfont-size: 12px;\n\t}\n\t.smush-ignore-utm,.smush-revert-utm{\n\t\tdisplay: block;\n\t\tmargin: 6px 0 4px;\n\t}\n\ta {\n\t\ttext-decoration: none;\n\t}\n\tspan {\n\t\tfloat: none !important;;\n\t}\n\t.smush-cdn-notice {\n\t\tcolor: #50575E;\n\t\ta {\n\t\t\tcolor:#2271B1;\n\t\t\t&:focus {\n\t\t\t\tbox-shadow: none;\n\t\t\t\topacity: 0.7;\n\t\t\t}\n\t\t}\n\t}\n}\n.smush-status {\n\t&.smush-warning,&.smush-ignored,&.smush-success{\n\t\tpadding-left:17px;\n\t\tposition: relative;\n\t\t&:before{\n\t\t\tcontent:\"\";\n\t\t\tbackground: url('../images/icon-warning.png' ) no-repeat 0 0;\n\t\t\tposition: absolute;\n\t\t\twidth:12px;\n\t\t\theight:12px;\n\t\t\tbackground-size: contain;\n\t\t\tleft: 0;\n\t\t\ttop:3px;\n\t\t}\n\t}\n\t&.smush-ignored{\n\t\t&:before{\n\t\t\tbackground-image: url('../images/icon-ignored.png' ) !important;\n\t\t}\n\t}\n\t&.smush-success{\n\t\t&:before{\n\t\t\tbackground-image: url('../images/icon-success.png' ) !important;\n\t\t}\n\t}\n\t.sui-icon-warning-media-lib {\n\t\tmargin-right:4px;\n\t\tposition:relative;\n\t\ttop:1px;\n\t}\t\n}\n.column-smushit .smush-status{\n\tcolor:#50575E;\n}\n// Stats table.\n.sui-smush-media {\n\ttable.wp-smush-stats-holder {\n\t\twidth: 100%;\n\t\tborder: 1px solid #E6E6E6;\n\t\tborder-radius: 4px;\n\t\tmargin-top: 6px;\n\t\tborder-collapse: collapse;\n\t\tborder-spacing: 0;\n\t\tthead {\n\t\t\tth.smush-stats-header {\n\t\t\t\tpadding: 8px 10px;\n\t\t\t\tborder-bottom: 1px solid #E6E6E6 !important;\n\t\t\t\tcolor: #32373D;\n\t\t\t\tfont-size: 12px;\n\t\t\t\tfont-weight: bold;\n\t\t\t\tletter-spacing: -0.23px;\n\t\t\t\tline-height: 16px;\n\t\t\t\ttext-align: left;\n\t\t\t}\n\t\t}\n\t\ttr {\n\t\t\tborder: 1px solid #E6E6E6;\n\t\t}\n\t\ttd {\n\t\t\toverflow-wrap: break-word;\n\t\t\tvertical-align: middle;\n\t\t\tpadding: 8px 10px;\n\t\t\tcolor: #555555;\n\t\t\tfont-size: 11px;\n\t\t\tletter-spacing: -0.21px;\n\t\t\tline-height: 16px;\n\t\t\tborder-bottom: 1px solid #E6E6E6;\n\t\t\t&:first-of-type {\n\t\t\t\tmax-width: 110px;\n\t\t\t\tfont-weight: 500;\n\t\t\t}\n\t\t}\n\t}\n}\n\n// Override !important set from WordPress.\n#the-list {\n\t.sui-smush-media {\n\t\tthead {\n\t\t\tth.smush-stats-header {\n\t\t\t\tborder-bottom: 1px solid #E6E6E6 !important;\n\t\t\t}\n\t\t}\n\t}\n}\n\n// Responsive table for list mode.\n@media screen and (max-width: 1024px) {\n\t.wp-list-table .smushit {\n\t\ttable.wp-smush-stats-holder {\n\t\t\tth {\n\t\t\t\tdisplay: table-cell;\n\t\t\t\tbox-sizing: border-box;\n\t\t\t}\n\t\t\ttr td {\n\t\t\t\tword-wrap: break-word;\n\t\t\t\tdisplay: table-cell !important;\n\t\t\t\t&:first-child {\n\t\t\t\t\tborder-right: none;\n\t\t\t\t\tbox-sizing: border-box;\n\t\t\t\t}\n\t\t\t\t&:last-child {\n\t\t\t\t\tbox-sizing: border-box;\n\t\t\t\t\tfloat: none;\n\t\t\t\t\toverflow: visible;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n}\n\n// NextGen Integration.\n.iedit .wp-smush-action,\n.iedit .smush-stats-details {\n\tfont-size: 11px;\n}\n\n/*NextGen Gallery stats*/\n#ngg-listimages {\n\ttable.wp-smush-stats-holder {\n\t\ttable-layout: fixed;\n\t\tborder: 1px solid lightgray;\n\t\tborder-collapse: collapse;\n\t\twidth: 100%;\n\t\ttd,\n\t\tth {\n\t\t\tborder: 1px solid #CECECE;\n\t\t}\n\t}\n\t.column-7 {\n\t\twidth: 300px;\n\t}\n\t.spinner {\n\t\twidth: auto;\n\t\tpadding-left: 30px;\n\t}\n}\n\n/** NextGen Gallery tr height, to show the progress bar properly for alternate rows **/\n.alternate.iedit {\n\theight: 120px;\n}\n\n/** Allows to click on button, otherwise row-actions from NextGen interferes **/\n.wp-smush-nextgen-send {\n\tposition: relative;\n\tz-index: 2;\n}\n", "/**\n * Common styles that are used on all the WP pages in the backend\n */\n@import \"modules/media\";\n\n.sui-wrap .smush-upsell-link,\n.sui-wrap a.smush-upsell-link {\n\tcolor: $purple;\n\t> span:before {\n\t\tcolor: $purple;\n\t}\n\t&:hover:not(.sui-button),\n\t&:focus:not(.sui-button),\n\t&:active:not(.sui-button) {\n\t\tcolor: #64007e;\n\t\t> span:before {\n\t\t\tcolor: #64007e;\n\t\t}\n\t}\n}\n\n/**\n * Media details (grid layout)\n * @since 3.4.0\n */\n.attachment-info .smush-stats .value {\n\tdisplay: flex;\n\tflex-wrap: wrap;\n\n\t.smush-status {\n\t\tmargin: 0 0 10px;\n\t\tflex-basis: 100%;\n\t\tfont-size: 12px;\n\t\tline-height: 1.33333;\n\t}\n\n\t.smush-status-links {\n\t\twidth: 100%;\n\t}\n\n\tspan.sui-tooltip {\n\t\tfloat: none;\n\t}\n\n\ta {\n\t\tmargin-left: 5px;\n\t}\n\n\ta:first-of-type {\n\t\tmargin-left: 0;\n\t\tmargin-right: 5px;\n\t}\n}\n\n.attachment-info .smush-status-links,\n.column-smushit .smush-status-links {\n\tcolor: #ddd;\n}\n\n.column-smushit .smush-status-links > a {\n\tbox-shadow: none;\n\toutline: none;\n}\n\n.wp-smush-progress {\n\tpadding-left: 25px;\n\tmargin: 0;\n\tbackground-size: 17px 17px;\n\tvisibility: visible;\n\tvertical-align: initial !important; /* prevent from jumping on a line */\n\tdisplay: inline;\n\tcolor: #32373c;\n\tcursor: default;\n}\n\n// Fix grid view links.\n.attachment-details .setting span.wp-smush-progress {\n\twidth: auto;\n\tline-height: 0;\n\tmargin-right: 5px;\n}\n\n/** Settings Page **/\n.smush-status.fail {\n\tcolor: #dd3d36;\n}\n\n.smush-status.success {\n\tcolor: #0074a2;\n}\n\n.smush-status.error {\n\tcolor: red;\n}\n\n#wpbody-content .wp-smush-error {\n\tcolor: red;\n}\n\n.wp-smush-action[disabled] {\n\topacity: 0.6;\n}\n\n#post-body-content .smush-status {\n\tmargin: 4px 0;\n}\n\n.attachment-info .wp-smush-error-message {\n\tmargin: 0 0 1em;\n}\n\n.smush-stats-wrapper .row {\n\tpadding: 8px 0;\n}\n\n.smush-stats-wrapper .row:first-child {\n\tpadding-top: 0;\n}\n\n.smush-stats-wrapper td, .smush-stats-wrapper th {\n\tfont-size: 11px;\n}\n\n.smush-skipped .dashicons-editor-help {\n\tmargin-top: -2px;\n\tmargin-left: 5px;\n}\n\n.smush-skipped {\n\ta:focus { box-shadow: 0 0 black; }\n\t.sui-tag.sui-tag-purple {\n\t\tmin-height: 18px;\n\t\tpadding: 2px 10px;\n\t\tfont-size: 10px;\n\t\tline-height: 12px;\n\t\tfont-weight: 700;\n\t\tbackground-color: #8d00b1;\n\t\tcolor: #fff;\n\t\tborder: 2px solid transparent;\n\t\tborder-radius: 13px;\n\t}\n}\n\n/** Help Tip **/\n.ui-tooltip-content {\n\tfont-size: 12px;\n}\n\n/** All Smushed **/\n.wp-smush-notice {\n\tbackground-color: #D1F1EA;\n\tborder-radius: 5px;\n\tcolor: #333333;\n\tfont-family: 'Roboto', sans-serif;\n\tfont-size: 15px;\n\tline-height: 30px;\n\tmargin-bottom: 30px;\n\tpadding: 15px 30px;\n\tletter-spacing: -0.015em;\n}\n\ndiv.smush-notice-cta a.smush-notice-act.button-primary {\n\tpadding: 3px 23px;\n\tbackground-color: #00B0DB;\n\tbox-shadow: none;\n\tborder-radius: 4px;\n\tborder: none;\n\ttext-shadow: none;\n\tfont-weight: normal;\n\t-webkit-font-smoothing: antialiased;\n\n\t&:hover {\n\t\tborder: none;\n\t}\n}\n\na.wp-smush-resize-enable:hover,\na.wp-smush-lossy-enable:hover {\n\tcolor: #0A9BD6;\n}\n\n.wp-smush-bulk-wrapper {\n\t#wp-smush-bulk-image-count {\n\t\tcolor: #333333;\n\t\tfont-size: 28px;\n\t\tline-height: 40px;\n\t\tletter-spacing: -0.5px;\n\t\tfont-weight: 600;\n\t}\n\n\t#wp-smush-bulk-image-count-description {\n\t\tcolor: #333333;\n\t\tfont-size: 13px;\n\t\tmargin-top: 0;\n\t\tmargin-bottom: 10px;\n\t}\n\n\t.sui-tooltip,\n\t.sui-tooltip > .sui-icon-info {\n\t\tvertical-align: top;\n\t}\n}\n\n/** Image Remaining **/\ndiv.wp-smush-dir-limit,\ndiv.smush-s3-setup-message {\n\tbackground-color: #FFF5D5;\n\tborder: none;\n\tcolor: #333333;\n\tline-height: 30px;\n\tfont-size: 15px;\n\tletter-spacing: -0.015em;\n}\n\ndiv.smush-s3-setup-message {\n\tbackground-color: #DFF6FA;\n}\n\ndiv.wp-smush-dir-limit {\n\tbackground-color: #dff6fa;\n}\n\n.wp-smush-count {\n\tcolor: #888888;\n\tfont-size: 13px;\n\tline-height: 1.5;\n\tmargin-top: 15px;\n}\n\n/** Stats Container **/\n\na.wp-smush-lossy-enable {\n\tcursor: pointer;\n}\n\n/** Re Smush **/\n.wp-smush-settings-changed {\n\tbackground: #dff6fa;\n\tborder-radius: 5px;\n\tfont-size: 13px;\n\tline-height: 1.7;\n\tpadding: 20px;\n}\n\n.compat-item .compat-field-wp_smush {\n\tdisplay: table-row;\n}\n\n.manage-column.column-smushit {\n\twidth: 260px;\n}\n\n.smushit [tooltip],\nlabel.setting.smush-stats [tooltip],\n.compat-field-wp_smush [tooltip] {\n\tposition: relative;\n\toverflow: visible;\n}\n\n.smushit [tooltip]:before,\nlabel.setting.smush-stats [tooltip]:before,\n.compat-field-wp_smush [tooltip]:before {\n\tcontent: '';\n\tposition: absolute;\n\tborder: 5px solid transparent;\n\tborder-top-color: #0B2F3F;\n\tbottom: 100%;\n\tleft: 50%;\n\tmargin-left: -5px;\n\tmargin-bottom: -5px;\n\topacity: 0;\n\tz-index: -1;\n\ttransition: margin .2s, opacity .2s, z-index .2s linear .2s;\n\tpointer-events: none;\n}\n\n.smushit [tooltip]:after,\nlabel.setting.smush-stats [tooltip]:after,\n.compat-field-wp_smush [tooltip]:after {\n\tbackground: #0B2F3F;\n\tborder-radius: 4px;\n\tbottom: 100%;\n\tcolor: #FFF;\n\tcontent: attr(tooltip);\n\tfont-size: 13px;\n\tfont-weight: 400;\n\tleft: 50%;\n\tline-height: 20px;\n\tmargin-left: -100px;\n\tmargin-bottom: 5px;\n\topacity: 0;\n\tpadding: 5px;\n\tpointer-events: none;\n\tposition: absolute;\n\twidth: 180px;\n\ttext-align: center;\n\ttransition: margin .2s, opacity .2s, z-index .2s linear .2s;\n\twhite-space: pre-wrap;\n\tz-index: -1;\n}\n\n.smushit .smush-skipped [tooltip]:before,\nlabel.setting.smush-stats .smush-skipped [tooltip]:before,\n.compat-field-wp_smush .smush-skipped [tooltip]:before {\n\tborder-top-color: transparent;\n\tborder-left-color: #0B2F3F;\n\tbottom: 0;\n\tleft: 0;\n}\n\n.smushit .smush-skipped [tooltip]:after,\nlabel.setting.smush-stats .smush-skipped [tooltip]:after,\n.compat-field-wp_smush .smush-skipped [tooltip]:after {\n\tmargin-left: 0;\n\tleft: -195px;\n\ttop: -35px;\n\tbottom: inherit;\n\tmargin-bottom: 5px;\n}\n\nlabel.setting.smush-stats .smush-skipped [tooltip]:after {\n\ttop: -98px;\n}\n\ndiv.media-sidebar label.setting.smush-stats .smush-skipped [tooltip]:after {\n\tleft: -188px;\n\tpadding-left: 10px;\n\twidth: 170px;\n}\n\ndiv.media-sidebar label.setting.smush-stats .smush-skipped [tooltip]:before {\n\tmargin-left: -3px;\n}\n\n.smushit [tooltip].tooltip-s:after,\nlabel.setting.smush-stats [tooltip].tooltip-s:after,\n.compat-field-wp_smush [tooltip].tooltip-s:after {\n\twidth: 150px;\n\tmargin-left: -75px;\n}\n\n.smushit [tooltip].tooltip-l:after,\nlabel.setting.smush-stats [tooltip].tooltip-l:after,\n.compat-field-wp_smush [tooltip].tooltip-l:after {\n\twidth: 280px;\n\tmargin-left: -140px;\n}\n\n.smushit [tooltip].tooltip-right:after, .compat-field-wp_smush [tooltip].tooltip-right:after {\n\tmargin-left: -180px;\n}\n\n.smushit [tooltip].tooltip-s.tooltip-right:after, .compat-field-wp_smush [tooltip].tooltip-s.tooltip-right:after {\n\tmargin-left: -130px;\n}\n\n.smushit [tooltip].tooltip-l.tooltip-right:after, .compat-field-wp_smush [tooltip].tooltip-l.tooltip-right:after {\n\tmargin-left: -260px;\n}\n\n.smushit [tooltip].tooltip-bottom:before, .compat-field-wp_smush [tooltip].tooltip-bottom:before {\n\tborder-color: transparent;\n\tborder-bottom-color: #0B2F3F;\n\ttop: 100%;\n\tbottom: auto;\n\tmargin-top: -5px;\n\tmargin-bottom: 0;\n}\n\n.smushit [tooltip].tooltip-bottom:after, .compat-field-wp_smush [tooltip].tooltip-bottom:after {\n\tbottom: auto;\n\ttop: 100%;\n\tmargin-top: 5px;\n\tmargin-bottom: 0;\n}\n\n.smushit [tooltip]:hover:before,\nlabel.setting.smush-stats [tooltip]:hover:before,\n.compat-field-wp_smush [tooltip]:hover:before {\n\tz-index: 1;\n\tmargin-bottom: 0;\n\topacity: 1;\n\ttransition: margin .2s, opacity .2s;\n}\n\n.smushit [tooltip]:hover:after,\nlabel.setting.smush-stats [tooltip]:hover:after,\n.compat-field-wp_smush [tooltip]:hover:after {\n\topacity: 1;\n\tz-index: 1;\n\tmargin-bottom: 10px;\n\ttransition: margin .2s, opacity .2s;\n}\n\n.smushit .disabled[tooltip]:before,\n.smushit .disabled[tooltip]:after,\nlabel.setting.smush-stats .disabled[tooltip]:before,\nlabel.setting.smush-stats .disabled[tooltip]:after,\n.compat-field-wp_smush .disabled[tooltip]:before,\n.compat-field-wp_smush .disabled[tooltip]:after {\n\tdisplay: none;\n}\n\n/** Image List **/\ndiv.wp-smush-scan-result {\n\tbackground: white;\n\n\tdiv.wp-smush-notice {\n\t\tmargin-top: 14px;\n\t\tpadding: 15px 30px;\n\t}\n\n\tdiv.content {\n\t\toverflow: hidden;\n\t\twidth: 100%;\n\t}\n}\n\ndiv.wp-smush-info.notice {\n\tfont-size: 15px;\n\tletter-spacing: -0.015em;\n\tmargin: 0 0 30px;\n\tpadding: 15px;\n}\n\n/** Media Queries **/\n\n@media screen and (max-width: 1024px) and (min-width: 800px) {\n\t/** Stats Section **/\n\t.smush-stats-wrapper h3 {\n\t\tpadding: 6px 0;\n\t}\n}\n\n/** Media Queries for resolution below 782px **/\n@media only screen and (max-width: 800px) {\n\t.dev-box.bulk-smush-wrapper.wp-smush-container {\n\t\tpadding: 20px 10px;\n\t}\n}\n\n/**\n * CSS styles used Admin notice\n */\n.smush-notice.notice {\n\tpadding: 0;\n\tmargin: 5px 0 10px;\n\tborder: 1px solid #E5E5E5;\n\tbackground: #FFF;\n\toverflow: hidden;\n\t-webkit-border-radius: 6px;\n\tborder-radius: 6px;\n\t-webkit-box-shadow: 0 1px 1px 0 rgba(0, 0, 0, 0.05);\n\tbox-shadow: 0 1px 1px 0 rgba(0, 0, 0, 0.05);\n\tposition: relative;\n\tz-index: 1;\n\tmin-height: 80px;\n\tdisplay: table; /* The magic ingredient! */\n\tfont: 13px \"Roboto\", sans-serif;\n}\n\n.smush-notice.notice.loading:before {\n\tcontent: attr(data-message);\n\tposition: absolute;\n\tleft: 0;\n\tright: 0;\n\ttop: 0;\n\tbottom: 0;\n\tbackground-color: rgba(255, 255, 255, 0.7);\n\tz-index: 5;\n\ttext-align: center;\n\tline-height: 80px;\n\tfont-size: 22px;\n\tfont-weight: bold;\n}\n\n.smush-notice > div {\n\tdisplay: table-cell; /* The magic ingredient! */\n\tvertical-align: middle;\n\tcursor: default;\n\tline-height: 1.5;\n}\n\n.smush-notice.notice.loading > div {\n\t-webkit-filter: blur(2px);\n\t-moz-filter: blur(2px);\n\t-o-filter: blur(2px);\n\t-ms-filter: blur(2px);\n\tfilter: blur(2px);\n}\n\n.smush-notice-logo {\n\tpadding-left: 30px;\n}\n\n.smush-notice-message {\n\tcolor: #23282D;\n\tfont-size: 13px;\n\tfont-weight: normal;\n\tline-height: 20px;\n\tpadding: 20px;\n\t-webkit-font-smoothing: antialiased;\n\twidth: 100%;\n}\n\n.smush-notice-cta {\n\tbackground: #F8F8F8;\n\tpadding: 0 30px;\n\tposition: relative;\n\twhite-space: nowrap;\n}\n\n.wp-core-ui .smush-notice-cta button,\n.wp-core-ui .smush-notice-cta .button-primary:active {\n\tvertical-align: middle;\n}\n\n.wp-core-ui .smush-notice-cta input[type=\"email\"] {\n\tvertical-align: middle;\n\tline-height: 20px;\n\tmargin: 0;\n\tmin-width: 50px;\n\tmax-width: 320px;\n\ttext-align: center;\n\tpadding-left: 0;\n\tpadding-right: 0;\n}\n\n/**\n * Upsell lists.\n * @since 3.9.1\n */\n#smush-box-cdn-upsell,\n#smush-box-webp-wizard {\n\t.sui-upsell-list {\n\t\tmax-width: 490px;\n\t\ttext-align: left;\n\t\tmargin: 0 auto;\n\t\tli {\n\t\t\tfont-size:12px;\n\t\t\tborder-bottom:1px solid #f2f2f2;\n\t\t\tpadding-bottom: 10px;\n\t\t\tmargin-bottom: 15px;\n\t\t\tletter-spacing: -0.23px;\n\t\t\t&:last-child {\n\t\t\t\tborder-bottom: none;\n\t\t\t}\n\t\t}\n\t}\n}\n\n#smush-box-cdn-upsell .sui-upsell-list {\n\tmax-width:350px;\n}\n\n@media only all and (max-width: 1000px) {\n\t.smush-notice.notice {\n\t\tdisplay: block;\n\t\tfont-size: 13px;\n\t}\n\n\t.smush-notice > .smush-notice-logo {\n\t\tfloat: left;\n\t\tdisplay: inline-block;\n\t\theight: 80px;\n\t\tmargin: 10px;\n\t\tborder-radius: 4px;\n\t}\n\n\t.smush-notice > .smush-notice-message {\n\t\twidth: auto;\n\t\tdisplay: block;\n\t\tmin-height: 80px;\n\t}\n\n\t.smush-notice > .smush-notice-cta {\n\t\tdisplay: block;\n\t\tborder-top: 1px solid #E5E5E5;\n\t\tborder-left: 0;\n\t\ttext-align: center;\n\t\twhite-space: normal;\n\t\tline-height: 30px;\n\t\tpadding: 10px 20px;\n\t}\n\n\t.wp-core-ui .smush-notice > .smush-notice-cta > input[type=\"email\"],\n\t.smush-notice > .smush-notice-cta > button {\n\t\tfont-size: 14px;\n\t}\n}\n\n@media only all and (max-width: 500px) {\n\t.wp-core-ui .smush-notice > .smush-notice-cta > input[type=\"email\"],\n\t.smush-notice > .smush-notice-cta > button {\n\t\tdisplay: block;\n\t\twidth: 100% !important;\n\t\tmax-width: none;\n\t\tmargin-bottom: 4px;\n\t\tfont-size: 16px;\n\t\theight: 34px;\n\t}\n}\n\n.smush-dismissible-notice {\n\tposition: relative;\n}\n"], "names": [], "sourceRoot": ""}