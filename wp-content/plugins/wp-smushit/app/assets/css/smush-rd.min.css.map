{"version": 3, "file": "css/smush-rd.min.css", "mappings": ";AAAQ,WAIR,kCACC,4CACA,wSACA,gBAKA,kBACA,sCAGD,yCACC,YAEC,uCAIF,oBACC,iBACC,wBACA,6BACA,yBAGD,cACC,SACA,YACA,YACA,WACA,gBACA,0BACA,sBACA,mEACA,kBACA,eACA,wBACA,wBACA,sDACA,gCAEA,OACC,8CAGD,kEACC,mFAGD,6CAEC,kBACA,eACA,iBACA,cACA,gDAGD,WACC,8DAGD,aACC,qEACA,WACC,kDAKD,aACC,YACA,CACA,mCACE,CAWF,gBAIF,GAEE,sBACA,MAGA,wBACA,mBAKH,cACC,MACA,QACA,YACA,YACA,sBACA,qCACA,eACA,iBACA,gBACA,kBACA,gBACA,wBACA,wBACA,sDACA,yBAEA,WACC,kBACA,sFAGD,+BACC,uBACA,qBAGD,UACC,eACA,iBACA,iBACA,yBACA,kBACA,SACA,oBAGD,UACC,eACA,iBACA,eACA,yBAGD,UACC,eACA,iBACA,iBACA,eACA,oCAGD,wBACC,kDAEA,4BACC,eACA,iDAGD,kBACC,uDAGD,UACC,YACA,WACA,eACA,iBACA,iBACA,kBACA,sBACA,kBACA,kBACA,sDAGD,qBACC,oBACA,CADA,YACA,sBACA,CADA,kBACA,0BACA,CADA,oBACA,sBACA,CADA,6BACA,kBACA,gCACA,eACA,uDAGD,aACC,gBACA,8DAEA,6CACC,WACA,eACA,kBACA,gBACA,oBACA,oBACA,oBACA,WACA,mCACA,kCACA,yFAIA,WACC,+CAKH,wBACC,WACA,mBACA,YACA,YACA,eACA,uBACA,iBACA,gBACA,oBACA,CADA,YACA,sBACA,CADA,kBACA,qBACA,CADA,sBACA,iEAEA,wBACC,WACA,kDAIF,iBACC,gHAEA,UAEC,UACA,2BACA,oBACA,kBACA,UACA,kCACA,yDAGD,8BACC,YACA,SACA,sBACA,2BACA,wDAGD,0BACC,eACA,iBACA,kBACA,gBACA,sBACA,WACA,6CACA,oBACA,kBACA,mBACA,YACA,SACA,gBACA,2BACA,kFAIA,eACC,mBACA,0KAKD,SAEC,yJAOF,mLAEA,wBACC,oBACA,yKACA,6CACC,gBACA,YACA,WACA,kBACA,6DAKH,YACC,kBACA,sBACA,0BACA,mBACA,aACA,WACA,gCACA,eACA,uBACA,iBACA,qDAGD,kBACC,gCACA,uEAEA,6FACA,qFAEA,wBACC,qBACA,+EACA,oDAKH,YACC,YACA,yBACA,8BACA,kBACA,4BACA,mDAEA,iBACC,UACA,8CACA,gBACA,YACA,cACA,iB", "sources": ["webpack://wp-smushit/./_src/scss/resize-detection.scss"], "sourcesContent": ["@import url('https://fonts.bunny.net/css?family=Roboto:400,500,700');\n@import \"modules/variables\";\n$font--path: '~@wpmudev/shared-ui/dist/fonts/';\n\n@font-face {\n\tfont-family: \"wpmudev-plugin-icons\";\n\tsrc: url('#{$font--path}/wpmudev-plugin-icons.eot?');\n\tsrc: url('#{$font--path}/wpmudev-plugin-icons.eot?') format('embedded-opentype'),\n\t \t url('#{$font--path}/wpmudev-plugin-icons.ttf') format('truetype'),\n\t\t url('#{$font--path}/wpmudev-plugin-icons.woff') format('woff'),\n\t\t url('#{$font--path}/wpmudev-plugin-icons.woff2') format('woff2'),\n\t\t url('#{$font--path}/wpmudev-plugin-icons.svg') format('svg');\n\tfont-weight: 400;\n\tfont-style: normal\n}\n\n@media screen and ( max-width: 800px ) {\n\t#smush-image-bar-toggle,\n\t#smush-image-bar {\n\t\tdisplay: none;\n\t}\n}\n\n@media screen and ( min-width: 800px ) {\n\t.smush-detected-img {\n\t\tborder-radius: 5px;\n\t\ttransition: all 0.5s ease;\n\t\tbox-shadow: 0 0 0 5px #FECF2F;\n\t}\n\n\t#smush-image-bar-toggle {\n\t\tposition: fixed;\n\t\ttop: 60px;\n\t\tright: 330px;\n\t\theight: 50px;\n\t\twidth: 60px;\n\t\tz-index: 9999999;\n\t\tborder-radius: 4px 0 0 4px;\n\t\tbackground-color: #FFF;\n\t\tbox-shadow: inset 2px 0 0 0 #FECF2F, -13px 5px 20px 0 rgba(0, 0, 0, 0.1);\n\t\ttext-align: center;\n\t\tcursor: pointer;\n\t\ttransition-property: all;\n\t\ttransition-duration: .5s;\n\t\ttransition-timing-function: cubic-bezier(0, 1, 0.5, 1);\n\n\t\t&.closed {\n\t\t\tright: 0;\n\t\t}\n\n\t\t&.smush-toggle-success {\n\t\t\tbox-shadow: inset 2px 0 0 0 #1abc9c, -13px 5px 20px 0 rgba(0, 0, 0, 0.1);\n\t\t}\n\n\t\ti.sui-icon-info,\n\t\ti.sui-icon-loader {\n\t\t\tfont-family: \"wpmudev-plugin-icons\" !important;\n\t\t\tfont-style: normal;\n\t\t\tfont-size: 16px;\n\t\t\tline-height: 50px;\n\t\t\tcolor: #FECF2F;\n\t\t}\n\n\t\ti.sui-icon-info:before {\n\t\t\tcontent: \"I\";\n\t\t}\n\n\t\t&.smush-toggle-success i.sui-icon-info {\n\t\t\tcolor: #1abc9c;\n\t\t\t&:before {\n\t\t\t\tcontent: \"_\";\n\t\t\t}\n\t\t}\n\n\t\ti.sui-icon-loader {\n\t\t\t&:before {\n\t\t\t\tdisplay: block;\n\t\t\t\tcontent: \"N\";\n\t\t\t\t-webkit-animation: spin 1.3s linear infinite;\n\t\t\t\t\t\tanimation: spin 1.3s linear infinite;\n\t\t\t}\n\t\t}\n\n\t\t@-webkit-keyframes spin {\n\t\t\t0% {\n\t\t\t\t-webkit-transform: rotate(0deg);\n\t\t\t\ttransform: rotate(0deg);\n\t\t\t}\n\t\t\t100% {\n\t\t\t\t-webkit-transform: rotate(360deg);\n\t\t\t\ttransform: rotate(360deg);\n\t\t\t}\n\t\t}\n\n\t\t@keyframes spin {\n\t\t\t0% {\n\t\t\t\t-webkit-transform: rotate(0deg);\n\t\t\t\ttransform: rotate(0deg);\n\t\t\t}\n\t\t\t100% {\n\t\t\t\t-webkit-transform: rotate(360deg);\n\t\t\t\ttransform: rotate(360deg);\n\t\t\t}\n\t\t}\n\t}\n\n\t#smush-image-bar {\n\t\tposition: fixed;\n\t\ttop: 0;\n\t\tright: 0;\n\t\twidth: 330px;\n\t\theight: 100%;\n\t\tbackground-color: #FFF;\n\t\tbox-shadow: 0 0 40px 0 rgba(0,0,0,0.1);\n\t\tz-index: 999999;\n\t\tpadding: 0 0 20px;\n\t\toverflow-y: auto;\n\t\toverflow-x: hidden;\n\t\tmax-width: 330px;\n\t\ttransition-property: all;\n\t\ttransition-duration: .5s;\n\t\ttransition-timing-function: cubic-bezier(0, 1, 0.5, 1);\n\n\t\t&.closed {\n\t\t\tmax-width: 0;\n\t\t\toverflow-y: hidden;\n\t\t}\n\n\t\th3, p, strong, span {\n\t\t\tfont-family: 'Roboto', sans-serif;\n\t\t\tletter-spacing: -0.25px;\n\t\t}\n\n\t\th3 {\n\t\t\tcolor: #333333;\n\t\t\tfont-size: 15px;\n\t\t\tfont-weight: bold;\n\t\t\tline-height: 30px;\n\t\t\tbackground-color: #FAFAFA;\n\t\t\tpadding: 15px 20px;\n\t\t\tmargin: 0;\n\t\t}\n\n\t\tp {\n\t\t\tcolor: #888888;\n\t\t\tfont-size: 13px;\n\t\t\tline-height: 22px;\n\t\t\tpadding: 0 20px;\n\t\t}\n\n\t\tstrong {\n\t\t\tcolor: #AAAAAA;\n\t\t\tfont-size: 12px;\n\t\t\tfont-weight: bold;\n\t\t\tline-height: 22px;\n\t\t\tpadding: 0 20px;\n\t\t}\n\n\t\t.smush-resize-box {\n\t\t\tbackground-color: #F8F8F8;\n\n\t\t\t&:first-of-type {\n\t\t\t\tborder-top: 1px solid #E6E6E6;\n\t\t\t\tmargin-top: 5px;\n\t\t\t}\n\n\t\t\t&:last-of-type {\n\t\t\t\tmargin-bottom: 20px;\n\t\t\t}\n\n\t\t\tspan:first-of-type {\n\t\t\t\tcolor: #888;\n\t\t\t\theight: 34px;\n\t\t\t\twidth: 40px;\n\t\t\t\tfont-size: 13px;\n\t\t\t\tfont-weight: bold;\n\t\t\t\tline-height: 32px;\n\t\t\t\ttext-align: center;\n\t\t\t\tborder: 1px solid #DDDDDD;\n\t\t\t\tborder-radius: 50%;\n\t\t\t\tmargin-right: 10px;\n\t\t\t}\n\n\t\t\t.smush-image-info {\n\t\t\t\tbackground-color: #FFF;\n\t\t\t\tdisplay: flex;\n\t\t\t\talign-items: center;\n\t\t\t\talign-content: center;\n\t\t\t\tjustify-content: space-between;\n\t\t\t\tpadding: 17px 20px;\n\t\t\t\tborder-bottom: 1px solid #E6E6E6;\n\t\t\t\tcursor: pointer;\n\t\t\t}\n\n\t\t\t.smush-front-icons {\n\t\t\t\tmargin: 0 10px;\n\t\t\t\tline-height: 5px;\n\n\t\t\t\t&:before {\n\t\t\t\t\tfont-family: \"wpmudev-plugin-icons\" !important;\n\t\t\t\t\tspeak: none;\n\t\t\t\t\tfont-size: 12px;\n\t\t\t\t\tfont-style: normal;\n\t\t\t\t\tfont-weight: 400;\n\t\t\t\t\tfont-variant: normal;\n\t\t\t\t\ttext-transform: none;\n\t\t\t\t\ttext-rendering: auto;\n\t\t\t\t\tcolor: #AAA;\n\t\t\t\t\t-webkit-font-smoothing: antialiased;\n\t\t\t\t\t-moz-osx-font-smoothing: grayscale;\n\t\t\t\t}\n\n\t\t\t\t&.smush-front-icon-arrows-in {\n\t\t\t\t\t&:before {\n\t\t\t\t\t\tcontent: '\\2264';\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t.smush-tag {\n\t\t\t\tbackground-color: #fecf2f;\n\t\t\t\tcolor: #333;\n\t\t\t\tborder-radius: 13px;\n\t\t\t\theight: 26px;\n\t\t\t\twidth: 116px;\n\t\t\t\tfont-size: 12px;\n\t\t\t\tletter-spacing: -0.25px;\n\t\t\t\tline-height: 16px;\n\t\t\t\tfont-weight: 500;\n\t\t\t\tdisplay: flex;\n\t\t\t\talign-items: center;\n\t\t\t\tjustify-content: center;\n\n\t\t\t\t&.smush-tag-success {\n\t\t\t\t\tbackground-color: #1abc9c;\n\t\t\t\t\tcolor: #fff;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t&.smush-tooltip {\n\t\t\t\tposition: relative;\n\n\t\t\t\t&:before,\n\t\t\t\t&:after {\n\t\t\t\t\tcontent: \"\";\n\t\t\t\t\topacity: 0;\n\t\t\t\t\tbackface-visibility: hidden;\n\t\t\t\t\tpointer-events: none;\n\t\t\t\t\tposition: absolute;\n\t\t\t\t\tz-index: 1;\n\t\t\t\t\ttransition: margin .2s, opacity .2s;\n\t\t\t\t}\n\n\t\t\t\t&:before {\n\t\t\t\t\tborder: 5px solid transparent;\n\t\t\t\t\tbottom: 100%;\n\t\t\t\t\tleft: 50%;\n\t\t\t\t\tborder-top-color: #000000;\n\t\t\t\t\ttransform: translateX(-50%);\n\t\t\t\t}\n\n\t\t\t\t&:after {\n\t\t\t\t\tcontent: attr(data-tooltip);\n\t\t\t\t\tmin-width: 40px;\n\t\t\t\t\tpadding: 8px 12px;\n\t\t\t\t\tborder-radius: 4px;\n\t\t\t\t\tbackground: #000000;\n\t\t\t\t\tbox-sizing: border-box;\n\t\t\t\t\tcolor: #FFFFFF;\n\t\t\t\t\tfont: 400 12px/18px \"Roboto\", Arial, sans-serif;\n\t\t\t\t\ttext-transform: none;\n\t\t\t\t\ttext-align: center;\n\t\t\t\t\twhite-space: nowrap;\n\t\t\t\t\tbottom: 100%;\n\t\t\t\t\tleft: 50%;\n\t\t\t\t\tmargin: 0 0 10px;\n\t\t\t\t\ttransform: translateX(-50%);\n\t\t\t\t}\n\n\t\t\t\t&.smush-tooltip-constrained {\n\t\t\t\t\t&:after {\n\t\t\t\t\t\tmin-width: 240px;\n\t\t\t\t\t\twhite-space: normal;\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t&:not(.show-description):hover {\n\t\t\t\t\t&:before,\n\t\t\t\t\t&:after {\n\t\t\t\t\t\topacity: 1;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t&:not(.show-description):hover,\n\t\t\t&.show-description {\n\t\t\t\t.smush-image-info { background-color: #F8F8F8; }\n\n\t\t\t\tspan:first-of-type {\n\t\t\t\t\tbackground-color: #E6E6E6;\n\t\t\t\t\tcolor: transparent;\n\t\t\t\t\t&:before {\n\t\t\t\t\t\tfont-family: \"wpmudev-plugin-icons\" !important;\n\t\t\t\t\t\tfont-weight: 400;\n\t\t\t\t\t\tcontent: \"\";\n\t\t\t\t\t\tcolor: #666;\n\t\t\t\t\t\tmargin-right: -7px;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t.smush-image-description {\n\t\t\t\tdisplay: none;\n\t\t\t\tborder-radius: 4px;\n\t\t\t\tbackground-color: #FFFFFF;\n\t\t\t\tbox-shadow: 0 2px 0 0 #DDDDDD;\n\t\t\t\tmargin: 0 20px 20px;\n\t\t\t\tpadding: 20px;\n\t\t\t\tcolor: #888888;\n\t\t\t\tfont-family: 'Roboto', sans-serif;\n\t\t\t\tfont-size: 13px;\n\t\t\t\tletter-spacing: -0.25px;\n\t\t\t\tline-height: 22px;\n\t\t\t}\n\n\t\t\t&.show-description {\n\t\t\t\tpadding-bottom: 1px;\n\t\t\t\tborder-bottom: 1px solid #E6E6E6;\n\n\t\t\t\t.smush-image-info { border-bottom: 0; }\n\t\t\t\t.smush-image-description { display: block; }\n\n\t\t\t\tspan:first-of-type {\n\t\t\t\t\tbackground-color: #FECF2F;\n\t\t\t\t\tborder-color: #FECF2F;\n\t\t\t\t\t&:before { color: #333; }\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t#smush-image-bar-notice {\n\t\t\tdisplay: none;\n\t\t\tmargin: 20px;\n\t\t\tborder: 1px solid #E6E6E6;\n\t\t\tborder-left: 2px solid #1abc9c;\n\t\t\tborder-radius: 4px;\n\t\t\tpadding: 15px 20px 15px 25px;\n\n\t\t\tp:before {\n\t\t\t\tposition: absolute;\n\t\t\t\tleft: 42px;\n\t\t\t\tfont-family: \"wpmudev-plugin-icons\" !important;\n\t\t\t\tfont-weight: 400;\n\t\t\t\tcontent: \"_\";\n\t\t\t\tcolor: #1abc9c;\n\t\t\t\tmargin-right: -7px;\n\t\t\t}\n\t\t}\n\t}\n}\n"], "names": [], "sourceRoot": ""}