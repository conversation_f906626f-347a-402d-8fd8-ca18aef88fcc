{"version": 3, "file": "css/smush-admin.min.css", "mappings": ";AAGS,0r<PERSON><PERSON>,SACC,cACA,WACA,eACA,gBACA,8BACA,qBACA,6BACA,YACA,UACA,SACA,4BACA,qCCVA,qCACC,oDAEA,oCAHD,SAIE,eACA,uCAIF,sCACC,oDAEA,qCAHD,SAIE,eACA,wCAIF,6CACC,oDAEA,sCAHD,SAIE,eACA,yCAIF,+CACC,oDAEA,uCAHD,SAIE,eACA,8CAIF,0CACC,oDAEA,4CAHD,SAIE,eACA,+CAIF,2CACC,oDAEA,6CAHD,SAIE,eACA,0BAKH,oBACC,6DACC,IAED,SACC,qCACA,KAED,SACC,kCACA,KAED,kCACC,KAED,gCACC,IAED,cACC,4BAIF,GACC,qDACA,cACA,4DACA,8CACA,4CACA,iEAGD,GACC,4BACA,gDACA,4CACA,cACA,yEACA,mCAGD,IACC,gCACC,SAED,SACC,iCACA,IAED,SACC,iCACA,oBAIF,GACC,SACC,KAED,SACC,yBAIF,GACC,SACC,KAED,SACC,6CACA,MAED,uCACC,qBAIF,GACC,SACC,KAED,SACC,MAED,SACC,0BAIF,GACC,SACC,wCACA,KAED,SACC,6CACA,MAED,SACC,wBAIF,KACC,SACC,mCACA,IAED,SACC,eACA,yBAIF,KACC,SACC,kCACA,IAED,SACC,eACA,kBAIF,GACC,sBACC,MAED,wBACC,yBCxJC,qCCzBI,gBAGQ,eAFF,iBAMO,WC4EoB,uBD3EjB,wCJFtB,uBACC,0BG0EA,uCH3ED,sBAIE,2BAIF,2BACC,oCACA,kCACA,kCACA,2BACA,mCACA,oCACA,6JAKD,aACC,iBACA,UACA,iBItBkB,WCgQoB,gBDlQpB,sCALb,2BJoCN,cI1Bc,iBAOE,WCsPuB,SL/NtC,2BAED,cI/Bc,iBAOE,2BJ6BhB,cInCc,iBAOE,2BJiChB,cIvCc,2BJ2Cd,cI1Cc,2BJ8Cd,cI7Cc,0BJmDd,qCIlEM,gBAGQ,eAFF,iBAMO,WC4EoB,uBD3EjB,kCJiErB,sCAEA,cACC,iBACA,WK4LqC,aL1LrC,sCAGD,YACC,wDAGD,kBI7DW,0BDwDX,uDHKA,kBI5Dc,sCJoEd,eACC,8BAKF,oBACC,eACA,iBACA,WKkKsC,kBLhKtC,0BAKD,oBACC,cK2JsC,gBLzJtC,4BACA,mBACA,gBACA,kJAGC,aKoJqC,mCL/ItC,mBACC,0HAEA,UK6IqC,eL3IpC,wDASH,eAEC,eACA,iBACA,WKkIsC,yBAHA,yBL5HtC,kBMzIc,SN2Id,kBACA,gBACA,cACA,eACA,sBACA,WACA,6BAGD,oBACC,gBACA,wDAID,eACC,4BAGD,iBACC,mBACA,8BACA,2BAGD,WACC,cACA,WACA,mBACA,cACA,yCAOD,aACC,aACA,WKhIa,6CLkIb,uBIpLqB,qDJuLrB,YACC,oDAGD,eACC,8DAQF,UK1HuC,+hDE5FxC,6CACC,WACA,cACA,kBACA,mBACA,oBACA,oBACA,cACA,oBACA,qBACA,WF+iBuC,UE7iBvC,cACA,mCAGA,kCACA,0CACA,8hDAlBD,UFyjBwC,6CEpiBvC,8hDArBD,UF0jBgD,aEjVhD,kCACC,4CACA,wSACA,mBAKA,kBACA,kCAKA,mCACC,iCAID,oBACC,+CAMA,cACC,+CAKD,cACC,+CAKD,cACC,+CAKD,cACC,oDAMD,aACC,kDAKD,aACC,oDAKD,aACC,iDAKD,aACC,wCAKF,WACC,kBACA,eACA,qCAKA,WA/QM,qCA+QN,WA/QM,0CA+QN,WA/QM,2CA+QN,WA/QM,2CA+QN,WA/QM,6CA+QN,WA/QM,oCA+QN,WA/QM,wCA+QN,WA/QM,qCA+QN,WA/QM,sCA+QN,WA/QM,sCA+QN,WA/QM,oCA+QN,WA/QM,mCA+QN,WA/QM,oCA+QN,WA/QM,uDA+QN,WA/QM,oCA+QN,WA/QM,2CA+QN,WA/QM,sDA+QN,WA/QM,uDA+QN,WA/QM,sCA+QN,WA/QM,wCA+QN,WA/QM,qCA+QN,WA/QM,8CA+QN,WA/QM,yCA+QN,WA/QM,sCA+QN,WA/QM,wCA+QN,WA/QM,0CA+QN,WA/QM,6CA+QN,WA/QM,4CA+QN,WA/QM,4CA+QN,WA/QM,wCA+QN,WA/QM,2CA+QN,WA/QM,0CA+QN,WA/QM,0CA+QN,WA/QM,+CA+QN,WA/QM,kDA+QN,WA/QM,gDA+QN,WA/QM,8CA+QN,WA/QM,oCA+QN,WA/QM,qCA+QN,WA/QM,0CA+QN,WA/QM,yCA+QN,WA/QM,6CA+QN,WA/QM,+CA+QN,WA/QM,wCA+QN,WA/QM,mCA+QN,WA/QM,uCA+QN,WA/QM,qCA+QN,WA/QM,yCA+QN,WA/QM,0CA+QN,WA/QM,qCA+QN,WA/QM,wCA+QN,WA/QM,qCA+QN,WA/QM,oCA+QN,WA/QM,2CA+QN,WA/QM,sCA+QN,WA/QM,0CA+QN,WA/QM,oCA+QN,WA/QM,0CA+QN,WA/QM,4CA+QN,WA/QM,2CA+QN,WA/QM,6CA+QN,WA/QM,2CA+QN,WA/QM,2CA+QN,WA/QM,oCA+QN,WA/QM,2CA+QN,WA/QM,2CA+QN,WA/QM,oCA+QN,WA/QM,uCA+QN,WA/QM,0CA+QN,WA/QM,0CA+QN,WA/QM,oCA+QN,WA/QM,sCA+QN,WA/QM,yCA+QN,WA/QM,oCA+QN,WA/QM,qDA+QN,WA/QM,oCA+QN,WA/QM,4CA+QN,WA/QM,sCA+QN,WA/QM,6CA+QN,WA/QM,+CA+QN,WA/QM,oCA+QN,WA/QM,oCA+QN,WA/QM,oCA+QN,WA/QM,qCA+QN,WA/QM,mCA+QN,WA/QM,wCA+QN,WA/QM,0CA+QN,WA/QM,gDA+QN,WA/QM,6CA+QN,WA/QM,8CA+QN,WA/QM,8CA+QN,WA/QM,yCA+QN,WA/QM,sCA+QN,WA/QM,2CA+QN,WA/QM,uCA+QN,WA/QM,oCA+QN,WA/QM,6CA+QN,WA/QM,sCA+QN,WA/QM,oCA+QN,WA/QM,yCA+QN,WA/QM,2CA+QN,WA/QM,0CA+QN,WA/QM,iDA+QN,WA/QM,oCA+QN,WA/QM,+CA+QN,WA/QM,wCA+QN,WA/QM,qCA+QN,WA/QM,oCA+QN,WA/QM,wCA+QN,WA/QM,qCA+QN,WA/QM,8CA+QN,WA/QM,iDA+QN,WA/QM,4CA+QN,WA/QM,sCA+QN,WA/QM,4CA+QN,WA/QM,8CA+QN,WA/QM,qCA+QN,WA/QM,+CA+QN,WA/QM,yCA+QN,WA/QM,wCA+QN,WA/QM,oCA+QN,WA/QM,yCA+QN,WA/QM,uCA+QN,WA/QM,mDA+QN,WA/QM,sDA+QN,WA/QM,+CA+QN,WA/QM,sCA+QN,WA/QM,uCA+QN,WA/QM,mCA+QN,WA/QM,wCA+QN,WA/QM,sCA+QN,WA/QM,uCA+QN,WA/QM,oCA+QN,WA/QM,+CA+QN,WA/QM,oCA+QN,WA/QM,uCA+QN,WA/QM,4CA+QN,WA/QM,gDA+QN,WA/QM,4CA+QN,WA/QM,oCA+QN,WA/QM,oCA+QN,WA/QM,uCA+QN,WA/QM,4CA+QN,WA/QM,+CA+QN,WA/QM,qCA+QN,WA/QM,qCA+QN,WA/QM,yCA+QN,WA/QM,+CA+QN,WA/QM,8CA+QN,WA/QM,+CA+QN,WA/QM,8CA+QN,WA/QM,4CA+QN,WA/QM,kDA+QN,WA/QM,8CA+QN,WA/QM,yCA+QN,WA/QM,0CA+QN,WA/QM,6CA+QN,WA/QM,4CA+QN,WA/QM,wCA+QN,WA/QM,qCA+QN,WA/QM,uCA+QN,WA/QM,2CA+QN,WA/QM,sCA+QN,WA/QM,2CA+QN,WA/QM,0CA+QN,WA/QM,8CA+QN,WA/QM,wCA+QN,WA/QM,sCA+QN,WA/QM,2CA+QN,WA/QM,mCA+QN,WA/QM,wCA+QN,WA/QM,8CA+QN,WA/QM,2CA+QN,WA/QM,8CA+QN,WA/QM,8CA+QN,WA/QM,6CA+QN,WA/QM,4CA+QN,WA/QM,sCA+QN,WA/QM,sCA+QN,WA/QM,uCA+QN,WA/QM,qCA+QN,WA/QM,2CA+QN,WA/QM,6CA+QN,WA/QM,gPCvCN,cACC,qBACA,kBACA,SACA,iBACA,mBACA,2BACA,kBFKa,qBEHb,kBACA,wBJgBU,8TIbV,UACC,YACA,aACA,0BACA,CADA,qBACA,sBACA,CADA,kBACA,qBACA,CADA,sBACA,kBACA,SACA,wWAEA,aACC,iuBAIF,aAEC,oBACA,0BLsDF,w7BKpDG,eACC,uBACA,iYAKH,YACC,kWAGD,cACC,4YAEA,aACC,cACA,oRAIF,YACC,gBACA,oRAGD,YACC,0kDAGD,cAKC,oBACA,4VAGD,mBACC,2BACA,CADA,mBACA,0BACA,CADA,qBACA,sBACA,CADA,kBACA,qBACA,CADA,sBACA,0aAEA,mBACC,CADD,YACC,wcAGD,SACC,0XAIF,mBACC,ghBAEA,YACC,0gBAGD,aACC,wlBAEA,UACC,YACA,qBACA,kBACA,qBACA,iBACA,koBAEA,oBACC,sVAMJ,iBACC,iHAMH,UAGC,eACA,iBACA,sBACA,WACA,6CACA,uBJnHqB,yBIqHrB,0KAEA,qBACC,gOAEA,UACC,kBACA,SACA,iBACA,qQAIF,wBAEC,mIAGD,YACC,0BACA,gwBAGD,wBHH4B,WACA,kSGgB1B,iBACC,cACA,iKAQF,wBACC,WACA,qWAEA,wBAEC,mLAGD,YACC,6BACA,g/BAGD,wBHzC0B,WACA,oKG0B3B,wBACC,WACA,2WAEA,wBAEC,sLAGD,YACC,6BACA,+/BAGD,wBHzC0B,WACA,8JG0B3B,wBACC,WACA,+VAEA,wBAEC,gLAGD,YACC,6BACA,i+BAGD,wBHzC0B,WACA,uKG0B3B,wBACC,WACA,iXAEA,wBAEC,yLAGD,YACC,6BACA,8gCAGD,wBHzC0B,WACA,uKG0B3B,wBACC,WACA,iXAEA,wBAEC,yLAGD,YACC,6BACA,8gCAGD,wBHzC0B,WACA,uKG0B3B,wBACC,WACA,iXAEA,wBAEC,yLAGD,YACC,6BACA,8gCAGD,wBHzC0B,WACA,oKG0B3B,qBACC,WACA,2WAEA,wBAEC,sLAGD,YACC,0BACA,+/BAGD,wBHzC0B,WACA,uKGoD5B,UACC,YACA,iBACA,qBHpD4B,mBGsD5B,+BACA,cHrD4B,iBGuD5B,oBACA,iXAEA,SAEC,qBH7D2B,oCAEA,yLGiE5B,YACC,6BACA,uKAKF,UACC,oBACA,CADA,YACA,sBACA,CADA,kBACA,qBACA,CADA,sBACA,iBACA,oBACA,kBH3E4B,+BACA,WACA,iBG6E5B,4LAEA,WJhBuB,0BD3IxB,2LK2JC,WJduB,2BD7IxB,sKK+IA,WJFwB,2BDnJxB,sKKqJA,WJHqB,qKIiCrB,iBACC,+BACA,WACA,2WAEA,iBAEC,sBACA,WACA,sLAGD,YACC,0BACA,+/BAGD,oBH9H2B,oCACA,oNGyI1B,oBACC,cACA,2cAEA,oBAEC,yBACA,WACA,sOAGD,YACC,6BACA,u9BAGD,oBH1JyB,oCACA,uNGyI1B,oBACC,cACA,idAEA,oBAEC,yBACA,WACA,yOAGD,YACC,6BACA,m+BAGD,oBH1JyB,oCACA,iNGyI1B,oBACC,cACA,qcAEA,oBAEC,yBACA,WACA,mOAGD,YACC,6BACA,28BAGD,oBH1JyB,oCACA,0NGyI1B,oBACC,cACA,udAEA,oBAEC,yBACA,WACA,4OAGD,YACC,6BACA,++BAGD,oBH1JyB,oCACA,0NGyI1B,oBACC,cACA,udAEA,oBAEC,yBACA,WACA,4OAGD,YACC,6BACA,++BAGD,oBH1JyB,oCACA,0NGyI1B,oBACC,cACA,udAEA,oBAEC,yBACA,WACA,4OAGD,YACC,6BACA,++BAGD,oBH1JyB,oCACA,uNGyI1B,iBACC,WACA,idAEA,iBAEC,sBACA,WACA,yOAGD,YACC,0BACA,m+BAGD,oBH1JyB,oCACA,0BFjE5B,8OKyOC,YAGE,2BLlPH,8OK+OC,aAOE,2BLhPH,oSKsPE,cAGE,cACA,2BL1PJ,2OKoPC,aAWE,2BLrQH,2OK0PC,YAeE,2BLnQH,4KKuOA,cAiCE,kBACA,iBACA,4JAKF,gBACC,eACA,iBACA,oNAEA,cACC,gIAOH,UAGC,YACA,2BACA,CADA,mBACA,sBACA,CADA,kBACA,qBACA,CADA,sBACA,cACA,+BACA,WACA,kBACA,mBACA,+OAIC,aACC,mcAIF,wBAGC,WACA,kJAGD,YACC,0BACA,20BAGD,UH5P4B,yBADA,gLGwQ3B,aACC,mlBAEA,wBAGC,cACA,kMAGD,YACC,6BACA,2jCAGD,UHtR0B,yBADA,mLGwQ3B,aACC,4lBAEA,wBAGC,cACA,qMAGD,YACC,6BACA,0kCAGD,UHtR0B,yBADA,6KGwQ3B,aACC,0kBAEA,wBAGC,cACA,+LAGD,YACC,6BACA,4iCAGD,UHtR0B,yBADA,sLGwQ3B,aACC,qmBAEA,wBAGC,cACA,wMAGD,YACC,6BACA,ylCAGD,UHtR0B,yBADA,sLGwQ3B,aACC,qmBAEA,wBAGC,cACA,wMAGD,YACC,6BACA,ylCAGD,UHtR0B,yBADA,sLGwQ3B,aACC,qmBAEA,wBAGC,cACA,wMAGD,YACC,6BACA,ylCAGD,UHtR0B,yBADA,mLGwQ3B,UACC,4lBAEA,+BAGC,WACA,qMAGD,YACC,0BACA,0kCAGD,UHtR0B,yBADA,4LGmS5B,UACC,kBACA,unBAEA,iBAGC,sBACA,WACA,8MAGD,YACC,0BACA,unCAGD,oBHpT2B,4OG8T1B,oBACC,uwBAEA,oBAGC,yBACA,WACA,8PAGD,YACC,6BACA,u2CAGD,oBH9UyB,+OG8T1B,oBACC,gxBAEA,oBAGC,yBACA,WACA,iQAGD,YACC,6BACA,s3CAGD,oBH9UyB,yOG8T1B,oBACC,8vBAEA,oBAGC,yBACA,WACA,2PAGD,YACC,6BACA,w1CAGD,oBH9UyB,kPG8T1B,oBACC,yxBAEA,oBAGC,yBACA,WACA,oQAGD,YACC,6BACA,q4CAGD,oBH9UyB,kPG8T1B,oBACC,yxBAEA,oBAGC,yBACA,WACA,oQAGD,YACC,6BACA,q4CAGD,oBH9UyB,kPG8T1B,oBACC,yxBAEA,oBAGC,yBACA,WACA,oQAGD,YACC,6BACA,q4CAGD,oBH9UyB,+OG8T1B,iBACC,gxBAEA,iBAGC,sBACA,WACA,iQAGD,YACC,0BACA,s3CAGD,oBH9UyB,sLG0V5B,wBACC,WACA,qmBAEA,wBAGC,wMAGD,YACC,0BACA,ylCAGD,wBHzW2B,WACA,sOGmX1B,wBACC,WACA,qvBAEA,wBAGC,wPAGD,YACC,6BACA,y0CAGD,wBHnYyB,WACA,yOGmX1B,wBACC,WACA,8vBAEA,wBAGC,2PAGD,YACC,6BACA,w1CAGD,wBHnYyB,WACA,mOGmX1B,wBACC,WACA,4uBAEA,wBAGC,qPAGD,YACC,6BACA,0zCAGD,wBHnYyB,WACA,4OGmX1B,wBACC,WACA,uwBAEA,wBAGC,8PAGD,YACC,6BACA,u2CAGD,wBHnYyB,WACA,4OGmX1B,wBACC,WACA,uwBAEA,wBAGC,8PAGD,YACC,6BACA,u2CAGD,wBHnYyB,WACA,4OGmX1B,wBACC,WACA,uwBAEA,wBAGC,8PAGD,YACC,6BACA,u2CAGD,wBHnYyB,WACA,yOGmX1B,qBACC,WACA,8vBAEA,wBAGC,2PAGD,YACC,0BACA,w1CAGD,wBHnYyB,WACA,0KG+Y5B,UACC,YACA,mOAEA,cACC,0aAeA,cACC,oCC5jBJ,mBACC,CADD,YACC,0BACA,CADA,qBACA,qBACA,CADA,sBACA,kBACA,UACA,oBACA,qJAEA,kBAGC,4GAGD,cAEC,qEAQC,wBJ2CW,4EIxCV,0BACC,uEAOF,kBACC,sBJ0BU,8EIvBV,qBJwBU,0IInBX,cAEC,oBACA,mEAMD,4BACC,2EAGD,4BACC,sDAGD,4BACC,iFAEA,4BACC,6EAIF,eACC,mEAMD,4BACC,2EAGD,4BACC,+BAIF,4DACC,wBACC,oGAIF,oEAEE,mCACC,4EACA,wBJrCS,wDI8Cb,ULwOa,YACC,kBKtOb,OACA,MACA,aACA,UACA,SACA,kBACA,sBJlDY,wBDnCF,UKwFV,8DAEA,WACC,WACA,YACA,kBACA,QACA,SACA,mBLqNY,sBC1PwB,sBIwCpC,gJAGD,iBAEC,gJAGD,gBAEC,mBACA,sDAKF,aACC,6CACA,uBLnIoB,+FKwIrB,iBAEC,SACA,iBACA,gBACA,4CAIF,SACC,wGACA,gBACC,0BClKF,qBACC,gEAGD,qBAEC,iCAID,eACC,kBACA,kBJEc,sBD+EwB,2BK9EtC,iDAGA,+BACC,kBACA,oBACA,CADA,YACA,sBACA,CADA,kBACA,0BPiED,gDOrEA,iBAOE,wEAGD,gBACC,0BP0DF,8COrDA,YNFc,2BDiDd,8CO/CA,YNHW,kDMeX,4BACC,aACA,oBACA,CADA,YACA,sBACA,CADA,kBACA,6DACA,gBACC,0BPmCF,gDOzCA,YNdc,kDM6Bd,mBACC,6DAEA,mBACC,CADD,YACC,kFAEA,cACC,iBACA,sCN/DE,uBAQgB,2FM2DlB,eACC,qGAGD,gBACC,eACA,4GAEA,aACC,mGAIF,iBACC,oGAGD,iBACC,8BACA,gFAIF,UACC,iBACA,iFAGD,UACC,gBACA,uBNzFkB,6GM4FlB,aACC,yGAGD,UACC,0BPxBJ,yEO4BE,oBAGE,gBACA,2BPtCJ,yEOkCE,gBAQE,2BP1CJ,4DOjBC,UAgEE,CAhEF,MAgEE,2BPzCH,4DOvBC,0BAoEE,CApEF,mBAoEE,kBACA,gBACA,2BPrDH,6DOyDC,iBAGE,CAHF,aAGE,2BP5DH,gDOpBA,mBAqFE,CArFF,YAqFE,kBACA,oDAOD,cACC,kBACA,CADA,aACA,0BP3EF,kFOgFE,iBAGE,2BPnFJ,iEO8EC,mBAUE,CAVF,YAUE,WACA,CADA,MACA,2BPzFH,6EO+FE,UAGE,CAHF,MAGE,2BPlGJ,oGOwGG,iBAGE,2BP3GL,mFOsGE,iBAUE,CAVF,aAUE,2BPhHJ,gDOuEA,mBA+CE,CA/CF,YA+CE,2BPtHF,+CO2HA,0BAGE,SACA,WACA,+DAEM,qCACI,iDAIZ,gBACC,oBACA,0BPnID,gDOiIA,kBNxLc,6CDiDd,gDOuIA,kBNzLW,6CDwDX,2COgJA,eAGE,2BPzJF,2COsJA,eAOE,2BPvJF,gCO7ED,kBNsBe,2BDiDd,gCOvED,kBNqBY,sCM+NZ,aACC,kBACA,0DAEA,eACC,kBACA,iBACA,yWAEA,eACC,2aAEA,eACC,4DAIF,eACC,uBACA,uEAEA,eACC,0BP7LH,yDO2KA,eNlOc,8CDiDd,yDOiLA,eNnOW,yFMuQV,gBACC,2FAKA,YACC,0FAGD,eACC,+DAQF,gBACC,iDAIF,YACC,gDAGD,eACC,0BP5OD,oCOuKD,YN9Ne,2BDiDd,oCO6KD,YN/NY,iDMmTX,aACC,SACA,UACA,SACA,WACA,6CACA,oBACA,mBPtRF,gBACA,uBACA,kDOuRE,qBACC,mEAGD,UACC,cACA,kBACA,0EAEA,aACC,eACA,2BACA,sDASH,iBACC,wEAEA,WACC,kBACA,SACA,0BPhSF,uEO6RC,YAME,4EAMD,iBACC,4BACA,kBJpXW,WDqFwB,eKkSnC,iBACA,6BACA,+BACA,gCACA,8BACA,6EAEA,ULzSmC,gBK2SlC,gGHnXL,WAGE,gGGoXG,aL9SkC,kBKgTjC,kBACA,0BPpUL,wEO6SC,kBA6BE,wDAKH,SACC,wDAOD,YNxYc,yBM0Yb,kBJ5Za,0BHmEd,uDOuVA,YNzYW,iDMoZZ,kBNlUgB,oBAIC,gCMiUhB,0BP/VA,+CO4VD,kBNnZe,+DM8Zf,eACC,yBACA,kBJlbc,0BHyEd,0DO4WA,kBNnac,2BDiDd,0DOkXA,kBNpaW,2BDwDX,yCOuWD,YN9Ze,2BDiDd,yCO6WD,YN/ZY,6DO9BX,mBACC,CADD,YACC,kBACA,mBP4BU,oDOzBV,0BRiFD,8FQ7EE,gBPsBY,2BDiDd,8FQvEE,gBPqBS,wEOTV,eACC,iBACA,sBACA,yEAID,UACC,oBACA,sCACA,wEAID,kBACC,kBACA,mBPNY,4CDiDd,uEQ7CC,kBAOE,kBACA,mBPbQ,6CDwDX,2DQtFA,aAkDE,kEAKF,mBACC,CADD,YACC,kBACA,mBP1Ba,0BDuDd,mGQxBE,gBP/BY,2BDiDd,mGQlBE,gBPhCS,2BDkDX,uMQJE,YAGE,iBACA,6EAKH,eACC,0FA/BF,aAmCE,+DAKF,eACC,mBACA,CADA,cACA,4KAEA,UACC,0BRdF,6DQSA,cASE,2BRxBF,6DQ6BA,WAGE,WACA,CADA,MACA,6HAKF,aACC,eACA,UACA,SACA,WACA,6CACA,uBP/GoB,kJOkHpB,QACC,8IAGD,UACC,mEAGD,mBAEC,CAFD,YAEC,qBACA,CADA,sBACA,4EAEA,eACC,iBACA,uFAEA,cACC,gLAGD,cAEC,yEASH,eACC,6EAOD,QACC,iBACA,mBP1IY,qCO6IZ,mFAEA,UACC,WACA,WACA,cACA,kBACA,QACA,SACA,OACA,yBACA,iLAGD,mBAEC,6LAEA,aACC,0BRzGJ,gLQoGE,gBP3JY,2BDiDd,4EQuFC,gBAiCE,mBP1KQ,mNOwLX,kBACC,kBACA,yBACA,wJAGD,SACC,WACA,0BR9ID,6KQiJA,aAGE,kBPtMS,+DO6MX,gBACC,kBACA,0EChOF,cACC,kBACA,mBACA,kBACA,0BTqEA,yESzED,iBAOE,mBACA,kCAIF,mBACC,CADD,YACC,mBACA,CADA,cACA,mBACA,mBREW,8DQEX,eACC,0BTqDD,2CStDA,eAIE,2BTkDF,gCS7DD,kBAgBE,mBRVa,kBQYb,sCAIF,8BACC,CADD,0BACC,iCAGD,UAEC,CAFD,MAEC,0CAGD,iBAEC,CAFD,aAEC,WACA,eACA,6BACA,CADA,eACA,uCA7DA,UADQ,0GAMR,gBANQ,uCACR,oBADQ,wIAMR,0BANQ,uCACR,oBADQ,wIAMR,0BANQ,sCACR,SADQ,sGAMR,eANQ,sCACR,oBADQ,uIAMR,0BANQ,sCACR,oBADQ,uIAMR,0BANQ,sCACR,SADQ,sGAMR,eANQ,sCACR,oBADQ,uIAMR,0BANQ,sCACR,oBADQ,uIAMR,0BANQ,sCACR,SADQ,sGAMR,eANQ,sCACR,oBADQ,uIAMR,0BANQ,sCACR,mBADQ,oIAMR,yBANQ,wBAuER,sCAtEA,UADQ,0GAMR,gBANQ,uCACR,oBADQ,wIAMR,0BANQ,uCACR,oBADQ,wIAMR,0BANQ,sCACR,SADQ,sGAMR,eANQ,sCACR,oBADQ,uIAMR,0BANQ,sCACR,oBADQ,uIAMR,0BANQ,sCACR,SADQ,sGAMR,eANQ,sCACR,oBADQ,uIAMR,0BANQ,sCACR,oBADQ,uIAMR,0BANQ,sCACR,SADQ,sGAMR,eANQ,sCACR,oBADQ,uIAMR,0BANQ,sCACR,mBADQ,oIAMR,yBANQ,2BAuER,sCAtEA,UADQ,0GAMR,gBANQ,uCACR,oBADQ,wIAMR,0BANQ,uCACR,oBADQ,wIAMR,0BANQ,sCACR,SADQ,sGAMR,eANQ,sCACR,oBADQ,uIAMR,0BANQ,sCACR,oBADQ,uIAMR,0BANQ,sCACR,SADQ,sGAMR,eANQ,sCACR,oBADQ,uIAMR,0BANQ,sCACR,oBADQ,uIAMR,0BANQ,sCACR,SADQ,sGAMR,eANQ,sCACR,oBADQ,uIAMR,0BANQ,sCACR,mBADQ,oIAMR,yBANQ,2BAuER,sCAtEA,UADQ,0GAMR,gBANQ,uCACR,oBADQ,wIAMR,0BANQ,uCACR,oBADQ,wIAMR,0BANQ,sCACR,SADQ,sGAMR,eANQ,sCACR,oBADQ,uIAMR,0BANQ,sCACR,oBADQ,uIAMR,0BANQ,sCACR,SADQ,sGAMR,eANQ,sCACR,oBADQ,uIAMR,0BANQ,sCACR,oBADQ,uIAMR,0BANQ,sCACR,SADQ,sGAMR,eANQ,sCACR,oBADQ,uIAMR,0BANQ,sCACR,mBADQ,oIAMR,yBANQ,4BAuER,sCAtEA,UADQ,0GAMR,gBANQ,uCACR,oBADQ,wIAMR,0BANQ,uCACR,oBADQ,wIAMR,0BANQ,sCACR,SADQ,sGAMR,eANQ,sCACR,oBADQ,uIAMR,0BANQ,sCACR,oBADQ,uIAMR,0BANQ,sCACR,SADQ,sGAMR,eANQ,sCACR,oBADQ,uIAMR,0BANQ,sCACR,oBADQ,uIAMR,0BANQ,sCACR,SADQ,sGAMR,eANQ,sCACR,oBADQ,uIAMR,0BANQ,sCACR,mBADQ,oIAMR,yBANQ,4BAgFP,uDAFD,eAGE,4BAIF,4CATD,kBR7CY,2BQ0DX,4CAbD,kBR5Ce,2BQkEb,uDAFD,eAGE,2BAIF,4CATD,kBR9De,2BQgFb,uDAFD,eAGE,2BAIF,4CATD,kBR5Ee,yBQ8Fb,uDAFD,eAGE,yBAIF,4CATD,kBR1Fe,qCShCf,eACC,wDAEA,mBACC,CADD,YACC,qBACA,CADA,sBACA,SACA,kBACA,SACA,kBPMa,sBDyEwB,wDQ5ErC,0DAEA,eACC,UACA,SACA,WACA,eACA,iBACA,qEAEA,eACC,yEAIF,UACC,cACA,WACA,kBACA,gFAEA,aACC,cACA,iGAGD,iBACC,4EAIF,UACC,CADD,MACC,kBACA,kBACA,6FAEA,iBACC,QACA,UACA,oGAEA,OACC,oGAGD,OACC,oGAGD,OACC,gGAIF,iBACC,CADD,aACC,kBACA,iBACA,wGAKH,YAEC,+IAMA,0DACC,iLAEA,aRjBW,mJQ0BZ,0DACC,qLAEA,aR5BW,oJQqCZ,0DACC,sLAEA,aACC,+IAQF,0DACC,iLAEA,aRlDW,mJQ2DZ,0DACC,qLAEA,aACC,+CAKH,QACC,8CAIF,mBACC,eACA,cACA,MACA,QACA,SACA,aACA,0DAEA,UACC,gBACA,uBACA,UACA,mBACA,8EAEA,4DACC,2LAMA,+DACC,+LAOD,+DACC,gMAOD,+DACC,2LAOD,+DACC,+LAOD,+DACC,qEAIF,eACC,2CAGD,yDAvDD,cAwDE,8EAUD,uDACC,+EAEA,URrIoC,s6BQqJpC,uDACC,mGASD,4DACC,koCAcA,4DACC,wCAWL,aACC,4DAEA,sBACC,CADD,kBACC,yDACA,8DAEA,kBACC,eACA,iGAKA,SACC,WACA,oGAGD,iBACC,iBACA,uJAQF,2DACC,2JAOD,2DACC,4JAOD,2DACC,uJAOD,2DACC,2JAOD,2DACC,kFASD,6DACC,mMAMA,gEACC,uMAOD,gEACC,wMAOD,gEACC,mMAOD,gEACC,uMAOD,gEACC,iFAWF,wDACC,mFAEA,URvToC,88BQuUpC,wDACC,uGASD,6DACC,0qCAcA,6DACC,6CAaJ,UACC,oDAMA,SACC,2CASA,sDAFD,SAGE,4CAGD,sDAND,MAOE,qCC5dJ,mBACC,CADD,YACC,mBACA,CADA,cACA,sBACA,CADA,kBACA,kBACA,mBV2BW,cUzBX,0BXiFA,sCW/EA,iBAGE,CAHF,aAGE,2BX4EF,sDWxEA,eAGE,kBACA,cACA,2BXmEF,qDW/DA,eAGE,eACA,cACA,2BX0DF,0DWlDE,gBAGE,2BX+CJ,wDWpDC,UAUE,CAVF,MAUE,2BX0CH,+IWtCC,iBAIE,CAJF,aAIE,aACA,eACA,4BX0BH,8DWnBC,mBAGE,2BXsBH,2DW3BA,kBAUE,2BXiBF,mCWvFD,kBVgCe,2CU+Cf,UT8LuC,SS5LtC,gBACA,iBACA,eACA,mBACA,gBACA,uBACA,qCCtFD,iBACC,uHAEA,cAEC,0BZ2ED,sHY7EA,cAKE,+DAKF,YACC,0BZkED,6DYnEA,eX+BmB,iBACC,mBW1BlB,CX0BkB,cW1BlB,2DAKF,aACC,0BZuDD,mEYrDC,kBAEE,kBACA,2BZkDH,8EY9CC,kBXJU,6CDkDX,yKYtCE,cAGE,2BZmCJ,uKY7BE,eAGE,2BZ0BJ,yDYxDA,eAoCE,WACA,CADA,MACA,uBACA,mBACA,2DAKF,UVwT2B,eUtT1B,iBACA,sCXlEI,uBAQgB,kBW6DpB,+DAEA,oBACC,2EAGD,aACC,aACA,kBACA,WV0SyB,sFUvSzB,eACC,0BZPH,qFYME,eAIE,2BZVJ,4FYeG,mBACA,gFAIF,gBACC,6EAGD,cACC,iBACA,0BZ1BF,6FY4BE,eAEE,+EAKH,aACC,eACA,gBACA,kFAEA,iBACC,sBACA,SACA,iBACA,0BZ5CH,iFYwCE,UAOE,qBACA,QACA,gGAIF,cACC,0BZtDH,6EYmCC,eAuBE,uBACA,mBACA,8EAIF,iBACC,UACA,SACA,eACA,kGAEA,iBACC,0BZvEH,yDYZA,gBXtCW,oCWgIT,wDAMD,kBACC,0BZrFF,8CYmFA,gBXrIW,+CDkDX,4EYiGC,eAEE,iBX5Ha,mBW8Hb,CX9Ha,cW8Hb,2BZrGH,sGYgHE,aAII,2BZpHN,iGYwJE,wDAGG,0BACA,gCXxLmB,4BW2LnB,2BZ/JL,iSYsMK,cAGE,2BZzMP,uPYoOG,cAGE,2BZvOL,2EYkOC,YAaI,2BZ/OL,gGY4PE,uBAGG,2BACA,4BACA,2BZjQL,oCYhFD,mBAsWE,CAtWF,YAsWE,sBACA,CADA,kBACA,eACA,2BZxRD,iLYoSE,eAGE,kBACA,2BZxSJ,+KY8SE,kBAGE,eACA,+DAOJ,gBACC,mFAEA,iBACC,cACA,iFAGD,MACC,sGAEA,kBACC,cACA,0BZtUH,iFY+UC,qCAGG,gFAqBJ,UVhWsC,gGUmWrC,UVnWqC,kCWxFvC,aACC,UACA,SACA,sEAEA,eACC,qCAGD,mBACC,CADD,YACC,SACA,cACA,SACA,gCACA,WXmX0B,eWjX1B,iBACA,sCZXI,gBYaJ,uBZLoB,wDYQpB,cACC,oEAEA,cACC,0BbuDH,mEaxDE,cAIE,oEAIF,eACC,0Bb+CH,kEahDE,eAIE,2Bb4CJ,uDa3DC,cAoBE,sDAIF,UACC,CADD,MACC,WXmVyB,sDW/U1B,iBACC,CADD,aACC,iBACA,iDAGD,aACC,0BbwBF,gDazBC,aAIE,iDAIF,gBACC,gBACA,0BbeF,+CajBC,gBAKE,2BbYH,oCaxEA,cAiEE,+CAIF,YACC,0BbED,6CaHA,YAIE,8CAIF,eACC,0BbND,4CaKA,eAIE,0EAOF,YACC,0BbjBD,iCajFD,aAsGE,yEAaC,eACC,kBACA,0BbpCH,uEakCE,eAKE,kBACA,wEAIF,kBACC,eACA,0Bb9CH,sEa4CE,kBAKE,eACA,sCCrIL,iBACC,sFAEA,WAEC,UACA,2BACA,oBACA,kBACA,aACA,eACA,4CAGD,QACC,YACA,+BACA,sBZmRqC,2BYjRrC,2CAGD,0BACC,qCACA,SACA,YACA,mBACA,iBACA,kBXZa,sBDoRwB,sBYrQrC,2BACA,WZyDqC,6CYvDrC,uBblBoB,oBaoBpB,6CACA,0Bd8CD,0Cc7DA,kBAkBE,oEAOD,qCACC,mBACA,gEAUA,MACC,wBACA,iEAOD,OACC,WACA,wBACA,sMAYD,QACC,aACA,+BACA,yBZ8MmC,mMY1MpC,QACC,aACA,gBACA,gBACA,mEAOD,MACC,uBACA,oEAOD,UACC,QACA,uBACA,6DAQF,OACC,WACA,aACA,WACA,+BACA,uBZsKoC,2BYpKpC,4DAGD,OACC,WACA,aACA,WACA,kBACA,gBACA,2BACA,8DAOD,OACC,aACA,UACA,+BACA,wBZ+IoC,2BY7IpC,6DAGD,OACC,aACA,UACA,iBACA,gBACA,2BACA,0BdvEF,6DcmFE,4CAGE,mBACA,2BdvFJ,iNcmGG,SAGE,YACA,SACA,YACA,sBZkGiC,iCYhGjC,kCACA,gCACA,2BACA,2Bd9GL,8MckHG,4CAGE,UACA,YACA,aACA,eACA,mBACA,cACA,mBACA,2Bd5HL,iEciIE,WAGE,SACA,2BACA,2BdtIJ,sEc0IE,WAGE,OACA,wBACA,2Bd/IJ,uEcmJE,OAGE,WACA,wBACA,2BdxJJ,0NcoKG,QAGE,aACA,SACA,YACA,+BACA,iCACA,yBZ+BiC,gCY7BjC,2BACA,2Bd/KL,uNcmLG,4CAGE,SACA,aACA,gBACA,eACA,gBACA,cACA,mBACA,2Bd7LL,oEckME,WAGE,SACA,2BACA,2BdvMJ,yEc2ME,WAGE,OACA,wBACA,2BdhNJ,0EcoNE,OAGE,WACA,wBACA,2BdzNJ,mEciOE,OAGE,WACA,aACA,WACA,+BACA,iCACA,kCACA,uBZ/BkC,2BYiClC,2Bd5OJ,kEcgPE,4CAGE,QACA,WACA,aACA,WACA,aACA,kBACA,gBACA,cACA,2BACA,mBACA,2Bd7PJ,oEcqQE,OAGE,YACA,aACA,UACA,+BACA,wBZjEkC,kCYmElC,gCACA,2BACA,2BdhRJ,mEcoRE,4CAGE,QACA,YACA,aACA,UACA,aACA,eACA,gBACA,iBACA,2BACA,mBACA,oMAWH,SAEC,4BACA,sDCpYF,cACC,eACA,cACA,gBACA,iEAGA,aACC,+GAIC,WACC,eACA,yBACA,CADA,oBACA,CADA,gBACA,oBACA,CADA,YACA,yBACA,CADA,oBACA,sBACA,CADA,kBACA,kBACA,SACA,UACA,sBACA,kBZVU,yBYYV,wBdEO,4IcEP,cACC,gBACA,cACA,WACA,CADA,MACA,yBACA,Wb+BQ,6Ca7BR,sBACA,uBACA,mBACA,4KAEA,UbqBQ,yIafT,UACC,YACA,oBACA,CADA,YACA,uBACA,CADA,kBACA,sBACA,CADA,kBACA,kBACA,CADA,aACA,kBACA,SACA,UACA,SACA,WbMQ,4JaHR,aACC,cACA,mKAEA,aACC,cACA,iHAOJ,mBACC,CADD,YACC,uBACA,CADA,kBACA,qBACA,CADA,sBACA,SACA,gBACA,sBACA,kBZlEU,yBD4CD,8Ia2BT,aACC,oBACA,CADA,YACA,kBACA,CADA,aACA,uBACA,CADA,kBACA,sBACA,CADA,kBACA,SACA,UACA,SACA,yKAEA,eACC,oBAEA,CAFA,YAEA,yBACA,CADA,oBACA,sBACA,CADA,kBACA,WACA,UACA,SACA,kBZzFQ,sBDmDD,WAsByB,6CaoBhC,uBd7Fe,6McgGf,aACC,gBACA,cACA,WACA,CADA,MACA,iBACA,iBACA,uBACA,mBACA,wJAOF,eACC,WACA,CADA,MACA,WACA,UACA,SACA,cACA,+KAEA,qBACC,YACA,YACA,cACA,SACA,UACA,SACA,gBACA,+BACA,Wb5EM,6Ca8EN,uBdlIc,qMcqId,UbpFM,CDjDQ,2LcqId,UbpFM,gMawFN,UbxFM,iMa4FN,Ub5FM,qMagGN,UbhGM,0MaoGN,UbpGM,qHagHT,qBbxFkC,yNasGhC,UACC,YACA,eACA,oBACA,CADA,YACA,4BACA,CADA,uBACA,sBACA,CADA,kBACA,qBACA,CADA,sBACA,SACA,UACA,SACA,yBACA,4BACA,yBACA,Wb5IM,kBa8IN,+NAEA,eACC,mKAgBH,iBACC,kBACA,sLAEA,UACC,YACA,oBACA,CADA,YACA,uBACA,CADA,kBACA,sBACA,CADA,kBACA,qBACA,CADA,sBACA,kBACA,QACA,UACA,2BACA,kBACA,6LAEA,aACC,eACA,mMAIF,iBACC,oKAeF,iBACC,kBACA,+KAEA,UACC,YACA,kBACA,QACA,SACA,iBACA,mBACA,kBACA,2BACA,oMAGD,iBACC,0IAaH,iBACC,iJX1QN,WAGE,WW0QK,kBACA,QACA,SACA,2BACA,kBACA,uKAGD,iBACC,kMAEA,aACC,eACA,oBACA,CADA,YACA,yBACA,CADA,oBACA,WACA,CADA,MACA,SACA,UACA,+BACA,gBACA,Wb1PM,6Ca4PN,uBdhTc,2LcoTf,UACC,2MAEA,YACC,oHAWJ,eb1PkC,uIamQlC,oBbvRS,gBAoByB,6BasQjC,sKAIE,YACC,oMAUF,qBb9SO,yBagTN,0BACA,oMAOD,wBbxTO,4Ba0TN,6BACA,wIAaH,qBb/SkC,iKa6TjC,wBACC,6FASJ,kBACC,2IAIC,mBACC,qBACA,yBACA,wKAGA,Ub1WQ,qKa+WR,Ub/WQ,6IaqXT,mBACC,0KAEA,oBACC,yBACA,0JAiBD,wBACC,eACA,iBACA,uJAID,UACC,YACA,iLAaA,iBACC,oMAEA,QACC,2MAEA,cACC,oLAKH,iBACC,kLAcD,iBACC,6LAEA,UACC,YACA,UACA,kNAGD,iBACC,kEAYP,cACC,wEAMD,aACC,qBACA,kBACA,mFAEA,cACC,oJAeE,2BbveQ,6BayeP,mLAKA,2Bb9eO,6BagfN,0DAcP,aACC,iBACA,gJAOG,kBACC,iBACA,uKAaA,gBACC,mBACA,0LAEA,SACC,WACA,uMAGD,gBACC,mBACA,oIAmBJ,iBbliBmC,sBADA,iKawiBlC,UbviBkC,iMa0iBjC,Ub1iBiC,8JagjBlC,UbhjBkC,mKawjBlC,iBbxjBkC,sBADA,yLa8jBhC,Ub7jBgC,+MagkB/B,UbhkB+B,sMagkB/B,UbhkB+B,0MaokB/B,UbpkB+B,2MawkB/B,UbxkB+B,+Ma4kB/B,Ub5kB+B,oNaglB/B,UbhlB+B,8LaslBjC,qBbtlBiC,+MaylBhC,qBbzlBgC,WADA,uNa2mBhC,Ub1mBgC,wOa6mB/B,Ub7mB+B,gKa4nBlC,iBbppBS,sBAuByB,6LakoBjC,UbzpBQ,0La8pBR,Ub9pBQ,+LaqqBR,iBbrqBQ,+LayrBR,wBbjqBiC,0BamqBhC,wMAKA,wBbxqBgC,0Ba0qB/B,8DAeP,aACC,gEAEA,qBACC,4BACA,oCACA,kCACA,kCACA,2BACA,mCACA,oCACA,mFAGD,aACC,SACA,eACA,sBACA,kBZrxBY,sBDyEwB,uCa+sBpC,mGAGA,aACC,kBACA,eACA,yGAEA,UACC,eACA,cACA,SACA,iBACA,sBACA,kBZtyBU,WDoDD,6CaqvBT,sBACA,+HAEA,Ub3vBS,CayvBT,qHAEA,Ub3vBS,0Ha+vBT,Ub/vBS,2HamwBT,UbnwBS,+HauwBT,UbvwBS,oIa2wBT,Ub3wBS,6Na+wBT,iBAEC,aACA,gBACA,wHAIF,uBACC,oGAKF,aACC,SACA,UACA,SACA,8HAEA,gBACC,gBACA,cACA,SACA,UACA,SACA,gBACA,uBACA,uJAEA,wBACC,CADD,oBACC,CADD,gBACC,cACA,SACA,eACA,SACA,+BACA,WblzBQ,6CaozBR,sBACA,gBACA,wBd11BM,gLc61BN,iBACC,cACA,kBACA,kBACA,SACA,kBZh3BQ,yBYk3BR,Wb9zBO,6Cag0BP,uBdp3Be,uLGatB,WAGE,WWw2BM,kBACA,QACA,UACA,2BACA,Wb10BM,ea40BN,kBACA,+KAIF,Ubl1BQ,eao1BP,gBACA,+LAGD,cACC,oOAEA,wBb/1BO,WAOA,yLa81BR,qBbh2BQ,WAuByB,8Na60BhC,qBbp2BO,WAuByB,0Gau1BpC,cACC,qJAIC,gBACC,8KAEA,cACC,iBACA,8KAaD,iBACC,iMAEA,UACC,YACA,oBACA,CADA,YACA,yBACA,CADA,oBACA,sBACA,CADA,kBACA,qBACA,CADA,sBACA,kBACA,MACA,UACA,kBACA,wMAEA,aACC,kBACA,CADA,aACA,cACA,eACA,2LAIF,iBACC,wNAYA,WACC,SACA,+NAEA,cACC,kNAIF,iBACC,+KAeH,iBACC,0LAEA,UACC,YACA,kBACA,QACA,UACA,iBACA,mBACA,kBACA,2BACA,kBACA,4LAGD,iBACC,0GAUL,sBACC,mBACA,2LAMG,mBACC,CADD,YACC,yBACA,CADA,oBACA,sBACA,CADA,kBACA,kBACA,8MAEA,eACC,WACA,CADA,MACA,+MAGD,iBACC,CADD,aACC,Wbl/BK,eao/BL,iMAIF,cACC,gBACA,yNAEA,cACC,kBACA,mBACA,4GAWN,oBbtgCW,uIaygCV,wBACC,4BACA,6BACA,gCACA,uIAGD,qBACC,yBACA,0BACA,+BACA,kEAWJ,aACC,6GAKE,gBACC,2JAOA,gBACC,qMAaC,UACC,UACA,+LAGD,iBACC,mBACA,8LAeD,UACC,UACA,gMAGD,kBACC,kBACA,iCC7pCR,WACC,gBACA,eACA,qBACA,aACA,eACA,+BACA,mBACA,yBdkYwB,WACL,6CchYnB,uBfKqB,kBeHrB,mDAGA,cACC,wDAEA,cACC,gBACA,cACA,uBACA,mBACA,2FAKF,wBdwDsC,WAIA,gGcrDtC,wBdgDsC,WAiUpB,8Lc1WlB,wBdwCsC,WAMA,8FcrCtC,wBd0WqB,WArUiB,gDc9BtC,wBdwWuB,WA1Ue,mGcxBtC,wBdsWyB,WACL,mDchWpB,wBACC,0FAGD,eAEC,oBACA,SACA,kBACA,cACA,gBACA,kBACA,yBACA,6CAID,wBdkVoB,WAlVkB,8CcMtC,wBdgVqB,WAtViB,4CcYtC,eACC,iBACA,eACA,iBACA,iBACA,uHAOA,oBd6RqB,+Bc1RpB,cd/BoC,4HcoCrC,oBd0RwB,+BcvRvB,cdxCoC,sPc6CrC,oBduRuB,+BclRtB,cdnDoC,0HcwDrC,oBdkRsB,+Bc/QrB,cdgRmB,8Dc3QpB,oBd+QwB,+Bc7QvB,cd8QqB,6CczQvB,aACC,4CAGD,cACC,4EC9JF,0BACC,CADD,mBACC,UACA,SACA,Wf6Da,6Ce3Db,qFAID,UACC,YhB2DwB,SgBzDxB,iBACA,sBACA,kBACA,yBf4Ca,sBe1Cb,WfkDa,6CehDb,uBACA,2BACA,aACA,gBACA,kIAEA,UfuCa,CezCb,8GAEA,UfuCa,iGenCb,iBfmCa,sBAwByB,aexDrC,gBACA,iGAGD,oBfgCa,sBAoByB,aejDrC,6BACA,8IAEA,UfqBY,CevBZ,0HAEA,UfqBY,iVefb,mBAGC,WfaY,mBAFA,kBACA,6GeJb,QACC,+BACA,8BAKF,cACC,mCAEA,cACC,mCAKF,mBAEC,CAFD,YAEC,sBACA,CADA,kBACA,eACA,4CAEA,eACC,qDAEA,gBACC,cACA,8CAIF,QACC,wCAID,cACC,iBACA,WfjCY,gBemCZ,0CAID,QAEC,wCAID,cACC,iBACA,qFAMF,aAEC,+GAEA,cACC,+GAED,eACC,6PAID,UAEC,qBACA,4CAKF,WAEC,iBACA,gBACA,YACA,kDAEA,YACC,qEAKC,wBflFW,WAoByB,uDeoErC,aACC,aACA,8DAGD,YACC,mEAEA,yBACC,6DAIF,eACC,kEAEA,yBACC,kDAOJ,aACC,eACA,Wf+P2B,eDnWC,iBgBuG5B,gBhBxG8B,uBAlET,yEgB8KrB,gBACC,kJAGD,iBAEC,0IAGD,eAEC,2CAKF,aACC,eACA,cf3HsC,eDVV,iBACE,gBACA,qDgB6I9B,cACC,qDAED,eACC,0BjB3ID,iEiBgJC,iBAGE,iBACA,2BjBpJH,4DiBwJC,YAGE,2BjB3JH,gDiB8IA,eAkBE,kBACA,gBACA,iBACA,oDAIF,eACC,0BjBlKD,kDiBiKA,eAIE,gTAQD,oBf9LY,uUeiMX,4BACC,oNAMD,afxMW,0BFiBb,uCiB+HD,kBhBtLe,2BDiDd,uCiBqID,kBhBvLY,oFgB8PZ,oBAEC,WACA,eACA,iBACA,0CAGD,iBACC,0CAED,gBACC,0BjBxNA,oJiBiOC,aAGE,2BjBpOH,kJiBwOC,cAGE,2BjB3OH,4HiB8NA,iBAkBE,CAlBF,aAkBE,aACA,gBACA,kBACA,iBACA,2BjBpPF,8DiBwPA,iBAGE,CAHF,aAGE,gBACA,2BjB5PF,4JiBgQA,iBAKE,CALF,aAKE,SACA,2BjBhQF,yDiBoQA,eAGE,2BjB7QF,yDiB0QA,eAOE,2BjBjRF,8CiB4ND,mBA0DE,CA1DF,YA0DE,sBACA,CADA,kBACA,mBhBzUU,2BDwDX,8CiBsND,kBhB7Qe,gDgBmVf,iBACC,mIAEA,iBACC,iHAID,iBACC,kEAGD,UACC,YACA,oBACA,oBACA,CADA,YACA,0BACA,CADA,qBACA,sBACA,CADA,kBACA,qBACA,CADA,sBACA,kBACA,SACA,UACA,yEAEA,UfuCqB,cerCpB,iKAMD,kBACC,kBACA,iFAGD,UACC,UACA,WfyBwB,yCelB3B,mBACC,CADD,YACC,sBACA,CADA,kBACA,qDAEA,iBACC,CADD,aACC,uHAGD,UACC,CADD,MACC,aACA,+IAEA,aACC,6IAGD,cACC,mHAKF,aAEC,kBACA,+RAEA,iBAEC,QACA,UACA,iJAGD,KACC,QACA,yBACA,4BACA,iTAGD,QACC,mBACA,8DAKF,aACC,kBACA,wJAEA,iBAEC,QACA,UACA,6EAGD,KACC,QACA,iKAGD,QACC,mBACA,yCAMH,UACC,oBACA,CADA,YACA,uBACA,CADA,mBACA,kBACA,4CAID,iBACC,iEAEA,UACC,YACA,eACA,kBACA,QACA,UACA,UACA,SACA,kBd9ea,yBcgfb,WfxEqC,ee0ErC,gBACA,2BACA,oNAEA,SAGC,uEAGD,gCACC,iGAEA,UfpboC,kCe4bvC,iBACC,iIAIC,kBACC,+HAGD,iBACC,qDAIF,UACC,YACA,oBACA,oBACA,CADA,YACA,0BACA,CADA,qBACA,sBACA,CADA,kBACA,qBACA,CADA,sBACA,kBACA,QACA,4DAEA,aACC,iEAGD,QACC,gEAGD,SACC,wCAKH,mBACC,CADD,YACC,sBACA,CADA,kBACA,wDAEA,UACC,CADD,MACC,+DAEA,iBACC,CADD,aACC,iBACA,0BjB9eF,uDiByeA,eASE,2BjBxfF,uDiB+eA,eAaE,0DAIF,kBACC,qGAMF,cACC,YACA,gBACA,iBACA,iGAQD,cACC,gBACA,yDACA,4BACA,sCACA,cACA,wEC3mBF,wBACC,CADD,oBACC,CADD,gBACC,qBACA,CADA,sBACA,oBACA,8FAKC,UjBgFqB,2BiB7EpB,cACA,oBACA,CADA,aACA,kBACA,aACA,sBACA,kBfDa,yBD6awB,oBgBzarC,mBACA,4GAEA,SACC,oBACA,uOAGD,cAEC,kBACA,WhBgaoC,eDrWZ,iBACE,gBA/Ef,mBiBwBX,mTAEA,cjB0D2B,gHiBrD5B,eACC,mBACA,iBACA,mNAMD,kCACC,8GAMD,oBhBUY,qJgBNX,SACC,+BAEA,2HAHD,wBAIE,qGAGD,2HAPD,qBhB2BoC,oPgBZpC,8CACC,wVASF,kBACC,qBhByWoC,yBACA,mzBgBtWpC,kBAEC,uvBAMD,gBACC,0BlBbH,6FkBmBD,eAGE,2BlB5BD,6FkByBD,cAOE,2BlB1BD,uEkBxFF,mBAuHE,CAvHF,YAuHE,gBACA,2BlBtCA,uEkBlFF,0BA4HE,CA5HF,mBA4HE,kBACA,+CAaC,iBACC,kBACA,qDAEA,WACC,UjBpDwB,yBiBuDxB,kBACA,QACA,SACA,mBjB7DkB,sBCAiB,6LgBuEpC,qBhBsSmC,0BFnXrC,+DkBuFC,QAGE,2BlB1FH,oDkBqFA,mBAUE,CAVF,YAUE,gBACA,4IAUA,cjB7F2B,yCiBuG9B,yBACC,CADD,qBACC,sBACA,CADA,kBACA,6CAEA,aACC,SACA,UACA,SACA,wDAEA,eACC,0BlBzHF,0CkB6HA,ejBpLc,2BDiDd,0CkBmIA,gBjBpLc,2BDiDd,4DkBkJC,ejBpMU,ciBwMR,2BlBtJH,0DkBgJA,0BAWE,CAXF,mBAWE,2BlBrJF,wCkB8GD,mBA4CE,CA5CF,YA4CE,2BlBhKD,wCkBoHD,0BAgDE,CAhDF,mBAgDE,yDd1NF,WAGE,WcqOE,YACA,oBACA,CADA,YACA,sBACA,CADA,kBACA,qBACA,CADA,sBACA,WhBhLmC,eDIZ,kBiB+KvB,sMAQD,UhBkLmC,0BFnXrC,qEkB2MC,QAGE,2BlB9MH,0DkByMA,mBAUE,CAVF,YAUE,gBACA,wJAUA,cjBjN2B,4CiB2N9B,yBACC,CADD,qBACC,sBACA,CADA,kBACA,gDAEA,aACC,SACA,UACA,SACA,8DAEA,eACC,0BlB7OF,6CkBiPA,ejBxSc,2BDiDd,6CkBuPA,gBjBxSc,2BDiDd,kEkBsQC,ejBxTU,ciB4TR,2BlB1QH,gEkBoQA,0BAWE,CAXF,mBAWE,2BlBzQF,2CkBkOD,mBA4CE,CA5CF,YA4CE,2BlBpRD,2CkBwOD,0BAgDE,CAhDF,mBAgDE,wPAUC,iBAEC,0BlBpSH,+EkB+RD,iBAWE,2BlB1SD,yEkBiTC,QAGE,2BlBpTH,8DkB+SA,eAUE,2BlBzTF,mEkBiUC,QAGE,2BlBpUH,wDkB+TA,eAUE,uICzZF,mBAEC,CAFD,YAEC,SACA,UACA,6BACA,gBACA,2SAEA,cAEC,cACA,oBACA,SACA,+BACA,6CACA,uBlBFmB,yBkBInB,qBACA,+jDAEA,YAKC,sCACA,qnBAGD,UjBkdoC,28BiB7cpC,UjBgdoC,2ViB1cpC,aACC,uVAGD,cACC,uUAGD,UjBkcoC,yBADA,+BiB5bpC,0SA7CD,wBA8CE,oGAGD,0SAjDD,wBAkDE,uUACA,wBACC,2CACA,0TASH,YAEC,gBACA,iBACA,6BACA,qVAEA,aACC,0BnBEH,wTmBVC,gBAYE,yKAQF,kBAEC,wEAKF,iBACC,0KAEA,aAEC,qBACA,sBACA,gNAEA,OACC,SACA,yBACA,mXAGD,iBAEC,CAFD,aAEC,6FAKF,iBACC,WACA,oBACA,CADA,YACA,sBACA,CADA,6BACA,YACA,sBACA,CADA,kBACA,QACA,oBACA,UACA,sMAEA,+BAEC,mBACA,mBACA,qGAED,iBACC,UACA,+FAIF,wBACC,kBhBlIY,wNgBqIZ,iBlBnHY,oCkBuHX,0BnBtEH,uNmBkEE,iBlBpHS,+ekBmIR,kBlBlIW,gEDiDd,4dmBiFG,kBlBnIQ,8EkBqJZ,eACC,wGAEA,mBAEC,CAFD,YAEC,mBACA,CADA,cACA,SACA,UACA,gBACA,+OAEA,cAEC,kBACA,CADA,aACA,iBACA,iBACA,SACA,6CACA,uBlBxLmB,qBkB0LnB,mSACA,mBACC,mxCAGD,YAKC,yBjBoSmC,WADA,+RiB1RpC,yBACC,2RAGD,QACC,0BACA,2QAGD,wBjBoRoC,cADA,+BiB9QpC,8OA1CD,wBA2CE,oGAGD,8OA9CD,gCA+CE,yBACA,2QACA,gCACC,yBACA,8PASH,YAEC,gBACA,qTAEA,YlBhOY,yBkBkOX,kBhBpPW,0BHmEd,oTmB+KE,YlBjOS,0RkB2OT,aACC,6UASD,eAEC,kDAKH,QACC,0BnB3MD,iDmB0MA,QAIE,gEAKF,SACC,YACA,0BnBrND,sCmBmGD,eAsHE,2IASA,aAEC,0BnBpOF,yImBkOC,aAKE,kUAOD,YlB/RY,0BDiDd,gUmB8OE,YlBhSS,2BDwDX,uDmB0NA,uBAyBE,2BnBzPF,uDmBgOA,uBA6BE,yBnB/SD,iBoBjCF,8CAEA,UACC,cACA,mBACA,WACA,mBACA,2DAEA,kBACC,8EAEA,eACC,UACA,SACA,gBACA,gGAEA,iBACC,eACA,iBACA,kBACA,gBACA,wGAEA,wBlBiWmC,mBkB/VlC,0GAEA,UlB4VkC,2NkBvVnC,iBAEC,yGAGD,OACC,UACA,mHAGD,OACC,UACA,0HAEA,cACC,gFAKH,aACC,WlBgUmC,gBkB9TnC,oBACA,2KAEA,UlB4TmC,+GkBpTnC,WACC,UACA,+BACA,iBACA,iHAEA,gBACC,mBACA,+BACA,yHAEA,wBlB2SiC,WADA,2BFhStC,6EoB7EC,kBA4EE,4BpBLH,6EoBvEC,kBAgFE,4BpBHH,mFoBOC,iBAGE,4BpBhBH,mFoBaC,WAOE,kBACA,MACA,WACA,SACA,kGAMD,eACC,+HAEA,eACC,4GAGD,QACC,2BpBhCJ,gFoBsBC,kBnB7Ea,4BDiDd,gFoB4BC,iBAmBE,4BpBzCH,+EoB+CC,YAGE,4BpBxDH,+EoB8DC,YAGE,4BpB3DH,0DoBhFA,UAgJE,cACA,4BpBvEF,0DoB1EA,WnBsGc,mBmBgDZ,mBnB9HS,4BDkDX,iEoBiFA,0BAGE,SACA,WACA,qEAIF,aACC,kBACA,mBACA,gBACA,2BAGD,6CAlLD,aAmLE,oOAgBE,UAEC,cACA,6GAGD,MACC,uHAGD,QACC,2BpB7HJ,uFoBkIC,WAGE,UACA,4BpBtIH,8DoB4GA,eA+BE,kBnB7LS,uCoBjCZ,iBACC,qBACA,cACA,yCAEA,eACC,aACA,kBACA,WACA,SACA,YACA,UACA,gBACA,eACA,sBACA,kBACA,sBnByEqC,uCmBvErC,oBACA,mBACA,8FAEA,WAEC,QACA,kBACA,WACA,wBACA,mBACA,sBACA,gDAGD,UACC,UACA,gCACA,+CAGD,UACC,SACA,gCACA,oFAGD,eACC,4CAGD,aACC,SACA,SACA,snBAIC,QAIC,eACA,SACA,aACA,gBACA,gGAIF,UACC,eACA,cACA,6CACA,gBACA,oBACA,+MAEA,8BAEC,WnBZS,uNmBgBV,mCAEC,WnBjBS,oZmBuBT,8BAGC,cACA,qRAGD,wBAEC,+DAKH,UACC,iBACA,eACA,kBACA,sEAEA,aACC,cACA,oBACA,6NAOD,qBnBtDU,WAuByB,+DmBqCpC,+BACC,iBACA,eACA,WnBtCmC,iDmB6CpC,aACC,SACA,UACA,SACA,kDAKH,aACC,iDAGD,cACC,4DAMA,WACC,WACA,oIAEA,WAEC,UACA,6DAQF,WACC,SACA,2BACA,sIAEA,WAEC,SACA,2BACA,uDAMH,cACC,4EAEA,iBACC,uVASA,wBnBxIW,WmB8IV,8CAIF,aACC,iBCzMH,GACC,2CACC,kBAFF,GACC,2CACC,kBAFF,GACC,2CACC,kBAFF,GACC,8CACC,kBAFF,GACC,8CACC,kBAFF,GACC,8CACC,kBAFF,GACC,8CACC,kBAFF,GACC,8CACC,kBAFF,GACC,6CACC,mBAFF,GACC,8CACC,mBAFF,GACC,8CACC,mBAFF,GACC,8CACC,mBAFF,GACC,8CACC,mBAFF,GACC,8CACC,mBAFF,GACC,8CACC,mBAFF,GACC,6CACC,mBAFF,GACC,8CACC,mBAFF,GACC,8CACC,mBAFF,GACC,8CACC,mBAFF,GACC,8CACC,mBAFF,GACC,8CACC,mBAFF,GACC,8CACC,mBAFF,GACC,8CACC,mBAFF,GACC,8CACC,mBAFF,GACC,6CACC,mBAFF,GACC,8CACC,mBAFF,GACC,8CACC,mBAFF,GACC,8CACC,mBAFF,GACC,8CACC,mBAFF,GACC,8CACC,mBAFF,GACC,8CACC,mBAFF,GACC,8CACC,mBAFF,GACC,8CACC,mBAFF,GACC,6CACC,mBAFF,GACC,8CACC,mBAFF,GACC,8CACC,mBAFF,GACC,8CACC,mBAFF,GACC,+CACC,mBAFF,GACC,gDACC,mBAFF,GACC,gDACC,mBAFF,GACC,+CACC,mBAFF,GACC,gDACC,mBAFF,GACC,gDACC,mBAFF,GACC,gDACC,mBAFF,GACC,gDACC,mBAFF,GACC,gDACC,mBAFF,GACC,gDACC,mBAFF,GACC,gDACC,mBAFF,GACC,gDACC,mBAFF,GACC,+CACC,mBAFF,GACC,gDACC,mBAFF,GACC,gDACC,mBAFF,GACC,gDACC,mBAFF,GACC,gDACC,mBAFF,GACC,gDACC,mBAFF,GACC,gDACC,mBAFF,GACC,gDACC,mBAFF,GACC,gDACC,mBAFF,GACC,+CACC,mBAFF,GACC,gDACC,mBAFF,GACC,8CACC,mBAFF,GACC,+CACC,mBAFF,GACC,+CACC,mBAFF,GACC,+CACC,mBAFF,GACC,+CACC,mBAFF,GACC,8CACC,mBAFF,GACC,+CACC,mBAFF,GACC,gDACC,mBAFF,GACC,gDACC,mBAFF,GACC,gDACC,mBAFF,GACC,gDACC,mBAFF,GACC,gDACC,mBAFF,GACC,gDACC,mBAFF,GACC,gDACC,mBAFF,GACC,+CACC,mBAFF,GACC,gDACC,mBAFF,GACC,gDACC,mBAFF,GACC,gDACC,mBAFF,GACC,gDACC,mBAFF,GACC,gDACC,mBAFF,GACC,gDACC,mBAFF,GACC,gDACC,mBAFF,GACC,gDACC,mBAFF,GACC,+CACC,mBAFF,GACC,gDACC,mBAFF,GACC,gDACC,mBAFF,GACC,gDACC,mBAFF,GACC,gDACC,mBAFF,GACC,gDACC,mBAFF,GACC,gDACC,mBAFF,GACC,+CACC,mBAFF,GACC,gDACC,mBAFF,GACC,gDACC,mBAFF,GACC,gDACC,mBAFF,GACC,gDACC,mBAFF,GACC,gDACC,mBAFF,GACC,gDACC,mBAFF,GACC,gDACC,mBAFF,GACC,gDACC,oBAFF,GACC,+CACC,2CAOF,mBACC,CADD,YACC,sBACA,CADA,kBACA,YrBqFgB,WqBnFhB,UACA,mBACA,8CAEA,WACC,yBACA,wBACA,qDAEA,SACC,oBACA,iEAGA,cpB2BW,gEoBtBX,cpB0CoC,iDoBpCtC,SACC,gDAIF,gBrBuD4B,gBqBrD3B,6CAGD,WrBgDiB,6CqB5CjB,YrB6CiB,qEqB1ChB,YACC,mPAQD,cpBUsC,qLoBFtC,cpBGsC,mLoBKtC,cpBJsC,oIoBWtC,WpB4asC,iEoBtatC,WpBXsC,oCqB3FvC,eACC,WrBghBsC,6CqB9gBtC,uBtBaqB,kBsBXrB,uDAEA,YACC,eACA,8DAEA,aACC,WrBqgBoC,0BF9btC,mCuBnFD,eAkBE,mFAIF,mBAEC,CAFD,YAEC,qBACA,CADA,sBACA,gBACA,kBACA,yKAEA,SACC,SACA,gBACA,wFAGD,0BACC,CADD,mBACC,SACA,WrB4eqC,6CqB1erC,4FAEA,aACC,WrBueoC,gBqBrepC,wBtBdS,kIsBiBT,UACC,kBACA,gJAEA,aACC,cACA,4SAIF,YAGC,gBACA,iNAGD,UrBkdoC,0BFzbtC,iFuBnED,kBAkDE,CAlDF,cAkDE,2BvBWD,iFuB7DD,etBWY,mEsBmDV,WACC,iBACA,gBACA,8CAOF,YACC,0DAEA,aACC,yDAGD,cACC,mDAOF,UACC,kBACA,gCACA,iBACA,mBACA,wDACA,iBACC,WrBgaoC,yBqB9ZpC,eACA,eACA,QACA,kDAIF,cACC,iBACA,iBACA,oBACA,gDAIF,eACC,gDAIC,6DAFD,mBAGE,CAHF,YAGE,qEAIF,gBACC,0BACA,sBACA,2BACA,kBACA,yEAEA,UACC,YACA,cACA,kBACA,QACA,SACA,gCACA,0BAEA,wEATD,SAUE,SACA,SACA,2BACA,iDAIF,mEAxBD,WAyBE,cACA,CADA,SACA,0BACA,2BAGD,mEA9BD,WA+BE,gBACA,8DAQA,wDAMC,kEAJA,0EvB1EJ,0BACA,qIAEA,iEuBuEI,wDvBjEH,8DuB8EE,yDAMC,kEAJA,2EvBzFJ,0BACA,qIAEA,iEuBsFI,yDvBhFH,8DuB6FE,yDAMC,kEAJA,2EvBxGJ,0BACA,qIAEA,iEuBqGI,yDvB/FH,0DuBwGA,eACC,4DAEA,YACC,eACA,iBACA,gBACA,oBACA,gDAEA,2DAPD,QAQE,gBACA,2BAGD,2DAZD,eAaE,wEAIF,YACC,0BACA,kBACA,yEAEA,kBACC,WrB2RmC,eqBzRnC,iBACA,gDAEA,wEAND,kBAOE,2BAGD,wEAVD,kBAWE,iDAIF,sEApBD,eAqBE,2BAGD,sEAxBD,2BAyBE,iDAIF,wDAjDD,yBAkDE,gDAKH,iBACC,gBACA,kDAEA,cACC,iBACA,iBACA,oBACA,iDAGD,eACC,sBACA,2DAED,QACC,mDAGD,aACC,YACA,eACA,mBACA,wBAQF,YACC,sCCrTD,mBACC,CADD,YACC,sBACA,CADA,kBACA,yDAEA,UACC,kBACA,4EAEA,aACC,eACA,mFAEA,aACC,4EAIF,aACC,2EAGD,eACC,yDAIF,cACC,kBACA,CADA,aACA,WtB8f4B,8CsB5f5B,uBvBhBoB,kBuBkBpB,8DAEA,aACC,2EAGD,eACC,wDAIF,WvBmLmB,gBuBjLlB,WACA,CADA,MACA,2BACA,yBtB8e4B,6DsB3e5B,WACC,cACA,yBtBeW,4CsBTd,UACC,eACA,gBvBiKqB,oBuB/JrB,CvB+JqB,YuB/JrB,sBACA,CADA,kBACA,kBACA,yBACA,kBrBnDc,sBDyEwB,0DsBlBtC,UACC,CADD,MACC,gJAEA,gBAEC,oHAIF,iBAEC,CAFD,aAEC,qPAOA,cACC,gEAIF,eACC,4CAIF,aACC,WtByb6B,6CsBvb7B,uBvBtFqB,kBuBwFrB,iDAEA,aACC,yBCxGF,wBACC,0BACA,kBACA,gBACA,yBACA,CADA,oBACA,CADA,gBACA,aACA,0BACA,CADA,qBACA,sBACA,CADA,kBACA,eACA,WACA,SACA,WACA,eACA,oCvB6esC,mDuB1etC,qBACC,4CAID,yBAEC,eACA,WACA,SACA,WACA,WACA,0BzB0DD,2CyBjEA,OAUE,mDAGD,cACC,aACA,gBACA,+DAGD,UACC,4CAKF,UACC,aACA,kBACA,CADA,aACA,kBACA,YACA,eACA,0BzBkCD,2CyBxCA,cASE,2CAKF,YACC,qDAEA,SACC,cACA,uBACA,yBACA,wBACA,4BACA,gEAEA,SACC,gEAGD,SACC,sBACA,oDAEA,+DAJD,SAKE,oBACA,sEAIF,SACC,0BACA,oDAEA,oEAJD,SAKE,oBACA,uEAIF,SACC,2BACA,oDAEA,qEAJD,SAKE,oBACA,qDAIF,oDA1CD,yBA2CE,yjBAOD,UAKC,oBACA,kCAMH,eACC,oEAGA,eACC,wDAMA,cACC,iBACA,iEAGD,kBACC,kEAGD,eACC,0BzB5DH,iEyB2DE,eAIE,mDAMH,UACC,eACA,YACA,gBACA,cACA,SACA,UACA,SACA,gBACA,sDAEA,cACC,cACA,cACA,UACA,SACA,gDAKF,UACC,YACA,gBACA,cACA,cACA,UACA,sBACA,kBtB1KY,sBsB4KZ,oDAEA,UACC,YACA,cACA,SACA,UACA,SACA,kBtBpLW,kDsB0Lb,iBACC,kEAGA,UACC,eACA,iBACA,mBACA,mBACA,kBACA,2BtBpMW,sDHyEd,iEyBqHE,gBAWE,mBACA,mBACA,kBACA,iEAKF,iBACC,UACA,SACA,2BACA,2EAOA,iBACC,SACA,WACA,SACA,0BzBvJJ,0EyBmJG,QAOE,WACA,2EAKF,iBACC,SACA,UACA,SACA,0BzBpKJ,yEyBgKG,QAOE,UACA,oEAMH,iBACC,SACA,SACA,SACA,2BACA,0BzBnLH,kEyB8KE,QAQE,+DAKF,gBACC,sBACA,qEAOA,aACC,qFAGA,aACC,uEAKF,eACC,uFAGA,eACC,wEAmBF,gBACC,sEAWD,cACC,sFAGA,cACC,uEAmBF,cACC,gBACA,uFAGA,cACC,gBACA,sEA7EF,gBACC,sFAGA,gBACC,wEAKF,kBACC,wFAGA,kBACC,yEAmBF,mBACC,uEAWD,iBACC,uFAGA,iBACC,wEAmBF,iBACC,mBACA,wFAGA,iBACC,mBACA,sEA7EF,gBACC,sFAGA,gBACC,wEAKF,kBACC,wFAGA,kBACC,yEAmBF,mBACC,uEAWD,iBACC,uFAGA,iBACC,wEAmBF,iBACC,mBACA,wFAGA,iBACC,mBACA,sEA7EF,gBACC,sFAGA,gBACC,wEAKF,kBACC,wFAGA,kBACC,0BzBnNL,uFyBkNI,kBAMG,2BzBxNP,uEyB8MG,kBAkBG,0EAMH,mBACC,0BzBvOJ,wEyBsOG,mBAMG,wEAMH,iBACC,uFAGA,iBACC,0BzBvPL,sFyBsPI,iBAMG,2BzB5PP,sEyBkPG,iBAkBG,yEAMH,iBACC,mBACA,wFAGA,iBACC,mBACA,0BzBjRL,uFyB+QI,iBAOG,mBACA,2BzBvRP,uEyB0QG,iBAqBG,mBACA,uEA5FH,gBACC,sFAGA,gBACC,wEAKF,kBACC,wFAGA,kBACC,0BzBnNL,uFyBkNI,kBAMG,2BzBxNP,uEyB8MG,kBAkBG,0EAMH,mBACC,0BzBvOJ,wEyBsOG,mBAMG,wEAMH,iBACC,uFAGA,iBACC,0BzBvPL,sFyBsPI,iBAMG,2BzB5PP,sEyBkPG,iBAkBG,yEAMH,iBACC,mBACA,wFAGA,iBACC,mBACA,0BzBjRL,uFyB+QI,iBAOG,mBACA,2BzBvRP,uEyB0QG,iBAqBG,mBACA,uEA5FH,gBACC,sFAGA,gBACC,wEAKF,kBACC,wFAGA,kBACC,0BzBnNL,uFyBkNI,kBAMG,2BzBxNP,uEyB8MG,kBAkBG,0EAMH,mBACC,0BzBvOJ,wEyBsOG,mBAMG,wEAMH,iBACC,uFAGA,iBACC,0BzBvPL,sFyBsPI,iBAMG,2BzB5PP,sEyBkPG,iBAkBG,yEAMH,iBACC,mBACA,wFAGA,iBACC,mBACA,0BzBjRL,uFyB+QI,iBAOG,mBACA,2BzBvRP,uEyB0QG,iBAqBG,mBACA,uEA5FH,gBACC,sFAGA,gBACC,wEAKF,kBACC,wFAGA,kBACC,0BzBnNL,uFyBkNI,kBAMG,2BzBxNP,uEyB8MG,kBAkBG,0EAMH,mBACC,0BzBvOJ,wEyBsOG,mBAMG,wEAMH,iBACC,uFAGA,iBACC,0BzBvPL,sFyBsPI,iBAMG,2BzB5PP,sEyBkPG,iBAkBG,yEAMH,iBACC,mBACA,wFAGA,iBACC,mBACA,0BzBjRL,uFyB+QI,iBAOG,mBACA,2BzBvRP,uEyB0QG,iBAqBG,mBACA,uEA5FH,gBACC,sFAGA,gBACC,wEAKF,kBACC,wFAGA,kBACC,0BzBnNL,uFyBkNI,kBAMG,2BzBxNP,uEyB8MG,kBAkBG,0EAMH,mBACC,0BzBvOJ,wEyBsOG,mBAMG,wEAMH,iBACC,uFAGA,iBACC,0BzBvPL,sFyBsPI,iBAMG,2BzB5PP,sEyBkPG,iBAkBG,yEAMH,iBACC,mBACA,wFAGA,iBACC,mBACA,0BzBjRL,uFyB+QI,iBAOG,mBACA,2BzBvRP,uEyB0QG,iBAqBG,mBACA,uEA5FH,gBACC,sFAGA,gBACC,wEAKF,kBACC,wFAGA,kBACC,0BzBnNL,uFyBkNI,kBAMG,2BzBxNP,uEyB8MG,kBAkBG,0EAMH,mBACC,0BzBvOJ,wEyBsOG,mBAMG,wEAMH,iBACC,uFAGA,iBACC,0BzBvPL,sFyBsPI,iBAMG,2BzB5PP,sEyBkPG,iBAkBG,yEAMH,iBACC,mBACA,wFAGA,iBACC,mBACA,0BzBjRL,uFyB+QI,iBAOG,mBACA,2BzBvRP,uEyB0QG,iBAqBG,mBACA,uEA5FH,gBACC,sFAGA,gBACC,wEAKF,kBACC,wFAGA,kBACC,0BzBnNL,uFyBkNI,kBAMG,2BzBxNP,uEyB8MG,kBAkBG,0EAMH,mBACC,0BzBvOJ,wEyBsOG,mBAMG,wEAMH,iBACC,uFAGA,iBACC,0BzBvPL,sFyBsPI,iBAMG,2BzB5PP,sEyBkPG,iBAkBG,yEAMH,iBACC,mBACA,wFAGA,iBACC,mBACA,0BzBjRL,uFyB+QI,iBAOG,mBACA,2BzBvRP,uEyB0QG,iBAqBG,mBACA,wEA5FH,iBACC,uFAGA,iBACC,yEAKF,mBACC,yFAGA,mBACC,0BzBnNL,wFyBkNI,kBAMG,2BzBxNP,wEyB8MG,kBAkBG,2EAMH,oBACC,0BzBvOJ,yEyBsOG,mBAMG,yEAMH,kBACC,wFAGA,kBACC,0BzBvPL,uFyBsPI,iBAMG,2BzB5PP,uEyBkPG,iBAkBG,0EAMH,kBACC,oBACA,yFAGA,kBACC,oBACA,0BzBjRL,wFyB+QI,iBAOG,mBACA,2BzBvRP,wEyB0QG,iBAqBG,mBACA,sEAUH,aACC,kBACA,4EAID,oBACC,CADD,sBACC,8EAIF,gBxB7WS,0BDwDX,6EyBqTE,gBxB5WY,qFwBoXZ,exBrXS,0BDwDX,mFyB6TE,exBpXY,oEwBoYX,iBACC,0EAID,gBACC,mEAQD,aACC,qEAWD,eACC,sEAWD,gBACC,oEAWD,cACC,qEAWD,cACC,gBACA,oEAlDD,gBACC,sEAWD,kBACC,uEAWD,mBACC,qEAWD,iBACC,sEAWD,iBACC,mBACA,oEAlDD,gBACC,sEAWD,kBACC,uEAWD,mBACC,qEAWD,iBACC,sEAWD,iBACC,mBACA,oEAlDD,gBACC,0BzB5VJ,mEyB2VG,gBAMG,uEAMH,kBACC,0BzBxWJ,qEyBuWG,kBAMG,wEAMH,mBACC,0BzBpXJ,sEyBmXG,mBAMG,sEAMH,iBACC,0BzBhYJ,oEyB+XG,iBAMG,uEAMH,iBACC,mBACA,0BzB7YJ,qEyB2YG,iBAOG,mBACA,qEAxDH,gBACC,0BzB5VJ,mEyB2VG,gBAMG,uEAMH,kBACC,0BzBxWJ,qEyBuWG,kBAMG,wEAMH,mBACC,0BzBpXJ,sEyBmXG,mBAMG,sEAMH,iBACC,0BzBhYJ,oEyB+XG,iBAMG,uEAMH,iBACC,mBACA,0BzB7YJ,qEyB2YG,iBAOG,mBACA,qEAxDH,gBACC,0BzB5VJ,mEyB2VG,gBAMG,uEAMH,kBACC,0BzBxWJ,qEyBuWG,kBAMG,wEAMH,mBACC,0BzBpXJ,sEyBmXG,mBAMG,sEAMH,iBACC,0BzBhYJ,oEyB+XG,iBAMG,uEAMH,iBACC,mBACA,0BzB7YJ,qEyB2YG,iBAOG,mBACA,qEAxDH,gBACC,0BzB5VJ,mEyB2VG,gBAMG,uEAMH,kBACC,0BzBxWJ,qEyBuWG,kBAMG,wEAMH,mBACC,0BzBpXJ,sEyBmXG,mBAMG,sEAMH,iBACC,0BzBhYJ,oEyB+XG,iBAMG,uEAMH,iBACC,mBACA,0BzB7YJ,qEyB2YG,iBAOG,mBACA,qEAxDH,gBACC,0BzB5VJ,mEyB2VG,gBAMG,uEAMH,kBACC,0BzBxWJ,qEyBuWG,kBAMG,wEAMH,mBACC,0BzBpXJ,sEyBmXG,mBAMG,sEAMH,iBACC,0BzBhYJ,oEyB+XG,iBAMG,uEAMH,iBACC,mBACA,0BzB7YJ,qEyB2YG,iBAOG,mBACA,qEAxDH,gBACC,0BzB5VJ,mEyB2VG,gBAMG,uEAMH,kBACC,0BzBxWJ,qEyBuWG,kBAMG,wEAMH,mBACC,0BzBpXJ,sEyBmXG,mBAMG,sEAMH,iBACC,0BzBhYJ,oEyB+XG,iBAMG,uEAMH,iBACC,mBACA,0BzB7YJ,qEyB2YG,iBAOG,mBACA,qEAxDH,gBACC,0BzB5VJ,mEyB2VG,gBAMG,uEAMH,kBACC,0BzBxWJ,qEyBuWG,kBAMG,wEAMH,mBACC,0BzBpXJ,sEyBmXG,mBAMG,sEAMH,iBACC,0BzBhYJ,oEyB+XG,iBAMG,uEAMH,iBACC,mBACA,0BzB7YJ,qEyB2YG,iBAOG,mBACA,sEAxDH,iBACC,0BzB5VJ,oEyB2VG,gBAMG,wEAMH,mBACC,0BzBxWJ,sEyBuWG,kBAMG,yEAMH,oBACC,0BzBpXJ,uEyBmXG,mBAMG,uEAMH,kBACC,0BzBhYJ,qEyB+XG,iBAMG,wEAMH,kBACC,oBACA,0BzB7YJ,sEyB2YG,iBAOG,mBACA,sDAQL,aACC,iEAEA,YACC,0BzB/ZH,gEyB8ZE,YAIE,iEAIF,eACC,0BzBvaH,+DyBsaE,eAIE,2BzB1aJ,oDyB2ZC,aAoBE,+DAQD,aACC,mBACA,qEAOA,oBACC,CADD,sBACC,oEAID,iBACC,CADD,wBACC,wEAID,qBACC,CADD,6BACC,qEAQD,aACC,uEAWD,eACC,wEAWD,gBACC,sEAID,cACC,uEAWD,cACC,gBACA,sEA3CD,gBACC,wEAWD,kBACC,yEAWD,mBACC,uEAID,iBACC,wEAWD,iBACC,mBACA,sEA3CD,gBACC,wEAWD,kBACC,yEAWD,mBACC,uEAID,iBACC,wEAWD,iBACC,mBACA,sEA3CD,gBACC,0BzBpdJ,qEyBmdG,gBAMG,yEAMH,kBACC,0BzBheJ,uEyB+dG,kBAMG,0EAMH,mBACC,uEAID,iBACC,0BzBjfJ,sEyBgfG,iBAMG,yEAMH,iBACC,mBACA,0BzB9fJ,uEyB4fG,iBAOG,mBACA,uEAjDH,gBACC,0BzBpdJ,qEyBmdG,gBAMG,yEAMH,kBACC,0BzBheJ,uEyB+dG,kBAMG,0EAMH,mBACC,uEAID,iBACC,0BzBjfJ,sEyBgfG,iBAMG,yEAMH,iBACC,mBACA,0BzB9fJ,uEyB4fG,iBAOG,mBACA,uEAjDH,gBACC,0BzBpdJ,qEyBmdG,gBAMG,yEAMH,kBACC,0BzBheJ,uEyB+dG,kBAMG,0EAMH,mBACC,uEAID,iBACC,0BzBjfJ,sEyBgfG,iBAMG,yEAMH,iBACC,mBACA,0BzB9fJ,uEyB4fG,iBAOG,mBACA,uEAjDH,gBACC,0BzBpdJ,qEyBmdG,gBAMG,yEAMH,kBACC,0BzBheJ,uEyB+dG,kBAMG,0EAMH,mBACC,uEAID,iBACC,0BzBjfJ,sEyBgfG,iBAMG,yEAMH,iBACC,mBACA,0BzB9fJ,uEyB4fG,iBAOG,mBACA,uEAjDH,gBACC,0BzBpdJ,qEyBmdG,gBAMG,yEAMH,kBACC,0BzBheJ,uEyB+dG,kBAMG,0EAMH,mBACC,uEAID,iBACC,0BzBjfJ,sEyBgfG,iBAMG,yEAMH,iBACC,mBACA,0BzB9fJ,uEyB4fG,iBAOG,mBACA,uEAjDH,gBACC,0BzBpdJ,qEyBmdG,gBAMG,yEAMH,kBACC,0BzBheJ,uEyB+dG,kBAMG,0EAMH,mBACC,uEAID,iBACC,0BzBjfJ,sEyBgfG,iBAMG,yEAMH,iBACC,mBACA,0BzB9fJ,uEyB4fG,iBAOG,mBACA,uEAjDH,gBACC,0BzBpdJ,qEyBmdG,gBAMG,yEAMH,kBACC,0BzBheJ,uEyB+dG,kBAMG,0EAMH,mBACC,uEAID,iBACC,0BzBjfJ,sEyBgfG,iBAMG,yEAMH,iBACC,mBACA,0BzB9fJ,uEyB4fG,iBAOG,mBACA,wEAjDH,iBACC,0BzBpdJ,sEyBmdG,gBAMG,0EAMH,mBACC,0BzBheJ,wEyB+dG,kBAMG,2EAMH,oBACC,wEAID,kBACC,0BzBjfJ,uEyBgfG,iBAMG,0EAMH,kBACC,oBACA,0BzB9fJ,wEyB4fG,iBAOG,mBACA,2DASN,UACC,eACA,eACA,cACA,cACA,UACA,SACA,+BACA,sBACA,gBACA,WACA,6CACA,uBACA,kBACA,+LAEA,UvB7hBqC,wCuBqiBtC,mBACC,CADD,YACC,mBACA,CADA,cACA,sBACA,CADA,kBACA,qBACA,CADA,sBACA,cACA,UACA,SACA,oIAEA,eAGC,cACA,kBACA,CADA,aACA,aACA,UACA,SACA,yBACA,mBACA,mBACA,oBACA,wKAEA,wBvBhlBW,0euBolBX,kBAGC,oBACA,iJAIF,cAGC,+CAKA,aACC,yJAEA,UxB/WS,+BwBoXR,+CARF,cACC,yJAEA,UxB/WS,+BwBoXR,+CARF,gBACC,yJAEA,SxB/WS,8BwBoXR,yDAUF,gBACC,0BzBnmBH,wDyBkmBE,gBAIE,0DAJF,eACC,0BzBnmBH,wDyBkmBE,eAIE,0DAJF,eACC,0BzBnmBH,wDyBkmBE,eAIE,0DAJF,eACC,0BzBnmBH,wDyBkmBE,eAIE,qCAOJ,mBACC,CADD,YACC,0BzB9mBD,wByBtFD,UAwsBE,OACA,eACA,2BzB1nBD,wCyBioBA,uBAGE,UACA,4CAWA,2CAFD,uBAGE,UACA,4CAGD,2CAPD,UAQE,OACA,wCASJ,WACC,UACA,0DAEA,WACC,UACA,0BzBtqBD,yEyB+qBC,UAGE,UACA,2BzBnrBH,sDyB6qBA,UAWE,UACA,4CAaC,4EAFD,UAGE,UACA,4CAGD,4EAPD,OAQE,UACA,4CAIF,yDAfD,UAgBE,UACA,4CAGD,yDApBD,OAqBE,UACA,0CAOL,eAEC,oCC7yBA,YACC,8CAGD,uBACC,kDAGD,iBACC,mCAGD,aACC,YACA,eACA,0CAGD,iBACC,iBACA,0CAGD,mBACC,CADD,YACC,sBACA,CADA,kBACA,iBACA,kBACA,2CAGD,mBACC,CADD,YACC,sBACA,CADA,kBACA,iBACA,eACA,2CAID,qBACC,CADD,6BACC,yCAED,mBACC,CADD,0BACC,uCAED,iBACC,CADD,wBACC,qCAGD,axB4BuC,qCwBxBvC,axByBuC,mCwBrBvC,axBsBuC,kCwBlBvC,axBmBuC,uCwBfvC,mBACC,2CAGD,uBACC,8CAGD,0BACC,4CAGD,wBACC,6CAGD,yBACC,wCAGD,oBACC,4CAGD,wBACC,+CAGD,2BACC,6CAGD,yBACC,8CAGD,0BACC,oCAGD,sBACC,wCAGD,0BACC,2CAGD,6BACC,yCAGD,2BACC,0CAGD,4BACC,qCAGD,YzBjGe,0BDiDd,oC0BgDD,YzBlGY,8CyByGX,SACC,0B1BxDD,4C0BuDA,SAIE,2B1B3DF,6C0B+DA,YzBjHW,2BDkDX,qD0BqEC,SAGE,2B1BlEH,4C0BuEA,YzB9Hc,2BDuDd,oD0B6EC,SAGE,0CAQH,gBzB/Ic,0BDiDd,wC0B8FA,gBzBhJW,kDyBuJV,aACC,0B1BtGF,gD0BqGC,aAIE,2B1BzGH,iD0B6GC,gBzB/JU,2BDkDX,yD0BmHE,aAGE,2B1BhHJ,gD0BqHC,gBzB5Ka,2BDuDd,wD0B2HE,aAGE,4CAtCJ,kBzB/Ic,0BDiDd,0C0B8FA,kBzBhJW,oDyBuJV,eACC,0B1BtGF,kD0BqGC,eAIE,2B1BzGH,mD0B6GC,kBzB/JU,2BDkDX,2D0BmHE,eAGE,2B1BhHJ,kD0BqHC,kBzB5Ka,2BDuDd,0D0B2HE,eAGE,6CAtCJ,mBzB/Ic,0BDiDd,2C0B8FA,mBzBhJW,qDyBuJV,gBACC,0B1BtGF,mD0BqGC,gBAIE,2B1BzGH,oD0B6GC,mBzB/JU,2BDkDX,4D0BmHE,gBAGE,2B1BhHJ,mD0BqHC,mBzB5Ka,2BDuDd,2D0B2HE,gBAGE,2CAtCJ,iBzB/Ic,0BDiDd,yC0B8FA,iBzBhJW,mDyBuJV,cACC,0B1BtGF,iD0BqGC,cAIE,2B1BzGH,kD0B6GC,iBzB/JU,2BDkDX,0D0BmHE,cAGE,2B1BhHJ,iD0BqHC,iBzB5Ka,2BDuDd,yD0B2HE,cAGE,2BAgBJ,eACC,uBACC,iDAiBD,eACA,uBACC,kDAFD,eACA,uBACC,4BAZF,eACC,uBACC,yDAiBH,UxB/Lc,sCyB/Dd,0BACC,0B3BmFA,qC2BpFD,sBAGE,oCCNF,UACC,mBACA,cACA,sBACA,qBACA,yBACA,kBzBQc,WD2EwB,eDkChB,iBACE,uBA9GH,6hB2BGnB,U1B4PoC,gB0BxPnC,qRAGD,WACC,sBACA,gBACA,SACA,gCACA,6VAEA,iBACC,0B5BiDJ,4V4BlDG,iBAIE,wVAIF,kBACC,0B5ByCJ,sV4B1CG,kBAIE,2B5BsCL,oR4BzDE,WAwBE,mBACA,kBACA,0ZAOF,eACC,+CAOF,gBACC,mBACA,qHASA,uBACC,mIAGD,iBACC,eACA,iKAEA,cACC,iBACA,qLAKH,eAGC,6KAMA,iBACC,eACA,sBACA,2LAEA,oBACC,iBACA,2MAGD,cACC,iBACA,moBASF,U1BoJoC,6K0B9IpC,U1BtCoC,wD0B2CrC,6BACC,sDAID,gCACC,wDAGD,gCACC,wDAGD,gCACC,sGAIF,YAEC,0B5BvED,qG4BqEA,YAKE,+CAIF,eACC,0B5B/ED,6C4B8EA,eAIE,sDAKF,QACC,SACA,gBACA,yBACA,0B5B3FD,kC4BjFD,aAgLE,0CAIF,YACC,W1BwFsC,8C0BtFtC,iBACA,qmBAaE,WACC,cACA,wCAQJ,wBACC,kBzBlMc,+CyBqMd,iBACC,6CAGD,mBACC,CADD,YACC,sBACA,CADA,kBACA,kBACA,6BACA,mDAGD,iBACC,cACA,8CAGD,QACC,W1ByCqC,eDlJhB,iB2B4GrB,sC3BjOI,gB2BmOJ,0BACA,kICxOD,cAEC,iBACA,wLAEA,aACC,eACA,iBACA,gBACA,aACA,6UAIA,eAEC,YACA,mCACA,2hBAUD,mBAGC,CAHD,YAGC,sBACA,CADA,kBACA,e5ByFmB,iBACE,sCAtHnB,gB4BgCF,uB5BxBkB,mjB4B2BlB,mBACC,CADD,YACC,sBACA,CADA,kBACA,WACA,CADA,MACA,cACA,2nBAEA,iBACC,0B7BiCL,0nB6BlCI,iBAIE,snBAIF,kBACC,0B7ByBL,onB6B1BI,kBAIE,2B7BsBN,kjB6BxCG,kBAuBE,kBACA,osBAIF,iBACC,CADD,aACC,ypBAQA,uBACC,sCACA,CADA,wBACA,ypBAFD,wBACC,uCACA,CADA,yBACA,ypBAFD,aACC,4BACA,CADA,cACA,ypBAFD,wBACC,uCACA,CADA,yBACA,ypBAFD,wBACC,uCACA,CADA,yBACA,ypBAFD,aACC,4BACA,CADA,cACA,ypBAFD,wBACC,uCACA,CADA,yBACA,ypBAFD,wBACC,uCACA,CADA,yBACA,ypBAFD,aACC,4BACA,CADA,cACA,+pBAFD,wBACC,uCACA,CADA,yBACA,+pBAFD,wBACC,uCACA,CADA,yBACA,+pBAFD,cACC,6BACA,CADA,eACA,mOAOF,cACC,qUAIC,qBACC,+NAKH,YACC,uXAUE,wBACC,iRAKH,aACC,0GAQL,aACC,yBACA,kB1BhHa,mY0BmHb,eAGC,cACA,0B7BpDF,kY6BgDC,eAOE,iIAIF,+BACC,oIAEA,eACC,W3B6HmC,iB2B3HnC,uBACA,mBACA,2IAGD,qBACC,8HAIF,+BACC,yJAEA,U3BtEoC,oB2BwEnC,kKAEA,iBACC,CADD,aACC,4KAGD,iBACC,eACA,mLAEA,aACC,2LAGD,cACC,iBACA,mLAIF,U3BwFmC,gB2BtFlC,+LAEA,iBACC,CADD,aACC,qBACA,CADA,sBACA,oMAEA,gBACC,sMAIF,cACC,oOAKA,cACC,uLAKH,yBACC,CADD,qBACC,eACA,iBACA,0MAEA,QACC,gMAMD,gBACC,0B7B1IL,+L6ByII,gBAIE,8TAKH,wB3BuUmC,uJ2BjUpC,mBACC,yB3BgUmC,2K2B7TnC,eACC,0B7B7JJ,sJ6BwJE,mBASE,sKAMD,0BACC,4BACA,yIAIF,eACC,qUAEA,8BAEC,8BACA,uUAOD,2BAEC,mUAOD,8BAEC,uUAOD,8BAEC,uUAOD,8BAEC,kLAOD,4BACC,4BACA,yB3ByPkC,sL2BjPnC,cACC,yB3BgPkC,qY2B7OlC,U3B+OkC,yY2B3OlC,mBACC,kMAGD,sBACC,8NAGD,W3BoOkC,gN2BhOlC,U3BgOkC,kkB2BvNlC,2BACC,6MAMD,YACC,gIAOJ,QACC,qBACA,oBACA,gBACA,4IAEA,kBACC,2IAGD,qBACC,6MAOC,wBACC,0BACA,mZASD,4BAEC,4BACA,0B7BpTL,+H6BoRC,QAsCE,uHAIF,YACC,0B7B/TF,qH6B8TC,YAIE,sHAIF,eACC,0B7BvUF,oH6BsUC,eAIE,2B7B1UH,yG6B2CA,e5B7FW,+F4B4YT,cACC,gCACA,2JAEA,+BACC,mQAEA,iBACC,uSAKA,W5B5TmB,sU4BgUlB,gB5BhUkB,yG4BuUrB,WACC,iBACA,4HAEA,aACC,eACA,eACA,iKAIF,wB3B4FmC,mH2BtFnC,6BACC,iHAID,gCACC,mHAID,gCACC,mHAID,gCACC,oGAID,wB3BkEmC,6M2B/DlC,iCACC,qJAGD,wBACC,gIAGD,iBACC,kBACA,mBACA,UACA,wGAKF,mBACC,yB3B6CkC,qN2B1ClC,U3B4CkC,6O2BzCjC,sBACC,qSAGD,W3BsCiC,yQ2BlCjC,U3BkCiC,wV2B7BlC,6BAGC,oIAGD,YACC,mFAKH,iBACC,kBACA,WACA,yB3BWmC,S2BTnC,gBACA,2KAEA,mBACC,0B7BpdJ,0K6BmdG,mBAIE,sMAWF,yBAEC,wHAKA,eACC,6EAOH,yBACC,iEAQF,eACC,kB1B9jBY,sBDyEwB,6B2BwfpC,4FAEA,eACC,eACA,W3BlCmC,e2BoCnC,iBACA,sC5BhlBE,uBAQgB,mG4B4kBlB,iBACC,W3B9UkC,sH2BkVnC,U3BlVmC,e2BoVlC,iBACA,sC5B5lBC,iB4B8lBD,0B7B7gBJ,8H6B+gBI,aAGE,YACA,SACA,kBACA,sIAIF,eACC,oBACA,CADA,YACA,sBACA,CADA,kBACA,uBACA,mBACA,0IAEA,iBACC,CADD,aACC,oJAGD,eACC,cACA,kBACA,CADA,aACA,sBACA,CADA,kBACA,uBACA,mBACA,0B7BhjBN,+G6BqjBG,YAGE,2B7BljBL,gH6BsjBG,YAGE,2B7BzjBL,oH6B6jBG,YAGE,8TAIF,YAGC,gXAEA,QACC,iWAGD,aACC,8VAGD,cACC,qGAIF,gBACC,mHAEA,iBACC,cACA,0B7B3lBL,qI6BimBI,eAGE,2B7BpmBN,mH6B+lBG,iBAUE,CAVF,aAUE,kBACA,CADA,wBACA,2B7B1mBL,2F6B0fE,kBAqHE,CArHF,cAqHE,kBACA,2B7BtnBJ,2F6BggBE,eA0HE,2FAIF,mBACC,mHAEA,mBACC,CADD,YACC,mBACA,CADA,cACA,cACA,UACA,wOAEA,QACC,gBACA,sHAGD,cACC,iBACA,sC5BztBA,uBAQgB,oB4BotBhB,kBACA,uPAEA,aACC,0B7BrpBN,sP6BopBK,iBAIE,8HAIF,U3BheiC,gB2BkehC,0B7B9pBN,qI6BiqBK,eAGE,kIAIF,QACC,0B7BnqBN,qH6BsoBI,aAiCE,iBACA,CADA,YACA,iBACA,2B7B/qBN,qH6B4oBI,iBAuCE,CAvCF,aAuCE,SACA,cACA,gBACA,oIAOD,aACC,eACA,yBACA,oBACA,+IAEA,yBACC,8IAGD,yBACC,0B7BxsBP,yF6B8nBE,mBAiFE,6EAIF,QACC,0B7BptBH,2E6BmtBE,QAIE,+IASA,YACC,0B7BjuBL,gE6B0fC,eA6OE,yEAYF,kBACC,gGAQA,kBACC,kBACA,0B7B9vBH,+F6B4vBE,kBAKE,kBACA,iIASD,mBACC,0B7B5wBJ,+H6B2wBG,mBAIE,8sCAuBD,kBACC,kBACA,0B7BxyBL,4sC6BsyBI,kBAKE,kBACA,2SAsBF,kBACC,kBACA,0B7Bp0BL,yS6Bk0BI,kBAKE,kBACA,4CCz5BP,wB5BwS2B,2F4BrS1B,eACC,8CAGD,mBACC,CADD,YACC,mBACA,CADA,cACA,aACA,mBACA,gBACA,kBACA,UACA,SACA,iDAEA,QACC,aACA,SACA,kEAKA,WACC,gFAOF,iBACC,CADD,aACC,sFAMD,UACC,CADD,MACC,0B9B8CF,wE8BtCE,cAGE,6BACA,CADA,eACA,2B9B4BJ,wE8BhCE,c7BmIqB,wED7FvB,wE8BtCE,cAGE,6BACA,CADA,eACA,2B9B4BJ,wE8BhCE,a7BmIqB,sED7FvB,wE8BtCE,cAGE,6BACA,CADA,eACA,2B9B4BJ,wE8BhCE,gB7BmIqB,4ED7FvB,wE8BtCE,cAGE,6BACA,CADA,eACA,2B9B4BJ,wE8BhCE,a7BmIqB,sED7FvB,wE8BtCE,cAGE,6BACA,CADA,eACA,2B9B4BJ,wE8BhCE,a7BmIqB,sED7FvB,0C8BvFD,iBAiEE,2B9BgBD,0C8BjFD,iBAqEE,2CAIF,eACC,cACA,kBACA,kB3B9Dc,sBDyEwB,uCAuNb,WADL,sCDvSf,uBAQgB,wBAcV,qD6B4DV,aACC,mBACA,kBACA,gCACA,kB3B9EY,wBFcH,sE6BoET,4CACC,4BACA,6BACA,gEAGD,wBACC,0BACA,qDAIF,mBACC,CADD,YACC,sBACA,CADA,kBACA,iBACA,oBACA,e7B+EsB,iBACE,gBAtLb,uE6B2GX,U7BiFuB,kB6B/EtB,C7B+EsB,a6B/EtB,0BACA,CADA,qBACA,iBACA,e7B8EqB,kB6B5ErB,8EAEA,aACC,W5BuLqB,iBDrHC,wBApKf,yD6ByGT,c7BqEwB,Y6BnEvB,cACA,iBACA,0DAGD,gBACC,oBACA,0CACA,e7BmDwB,iBACE,6D6B5C3B,iBACC,yB5BwJ6B,cACL,oEEtR5B,WAGE,kB0B+HG,UACA,QACA,UACA,W5BzEkC,e4B2ElC,mEAGD,WACC,kBACA,SACA,WACA,8BACA,qCACA,sFAGD,a5BmIwB,kE4B/HxB,wB5B8H6B,gD4BvHhC,iE5ByHiC,uD4BrHjC,mBACC,kBACA,+BACA,gBACA,kEAIC,iBACC,8JAEA,iBAEC,QACA,UACA,uEAGD,iBACC,wBACA,sBACA,6DAKH,eACC,+EASA,iBACC,+EAGD,aACC,iB7BlBmC,mBAtBhB,oBAoBa,kBApBb,iG6B8CnB,aACC,kBACA,wGAEA,gB7BzCoB,mF6B8CrB,aACC,oCCxPL,cACC,oBACA,CADA,YACA,2CAMA,cACC,cACA,oBACA,4LAEA,YAIC,gBACA,sDAIF,U9BuHwB,8B8BpHvB,C9BoHuB,a8BpHvB,kBACA,SACA,Y9BqH0B,yB8BnH1B,kB5Bba,0E4BgBb,UACC,YACA,cACA,kBACA,sEAGD,qB7ByaqC,yM6BvapC,0BACA,gCACA,yEAGD,cACC,aACA,kBACA,Q9B+FyB,wC8B5FzB,sBACA,4BACA,2BACA,gF3B1BH,WAGE,U2B0BE,oBACA,CADA,YACA,sBACA,CADA,kBACA,qBACA,CADA,sBACA,kBACA,MACA,QACA,SACA,OACA,kBACA,W7BsBmC,e6BpBnC,iBACA,gC7B2YmC,oB6BzYnC,oTAGD,YAIC,gBACA,iQAOA,SACC,+IAKH,eAEC,uEAGD,2BACC,eACA,kBACA,8EAEA,aACC,0JAGD,qB7BlBoC,qD6ByBtC,cACC,eACA,kBACA,CADA,aACA,aACA,kBACA,4BACA,sBACA,kB5BzGa,sBDyEwB,oB6BmCrC,yBACA,4D3BhGF,WAGE,W2BgGC,cACA,kBACA,UACA,eACA,iBACA,kBACA,0DAGD,wBACC,CADD,oBACC,CADD,gBACC,cACA,W7BgVoC,6C6B9UpC,uB9B7HmB,gE8BgInB,YACC,gBACA,4DAIF,UACC,kBACA,MACA,QACA,SACA,oBACA,sBACA,0BACA,sB7BrEoC,e6BuEpC,kBACA,+EAEA,aACC,sFAEA,aACC,oIAIF,wBAEC,kEAGD,kBACC,gLAIF,wB7BuSqC,uD6BhStC,QACC,kBACA,uBACA,kB5B/Ka,+BDqcwB,WACA,6C6BnRrC,uB9BnLoB,4P8BsLpB,iBAIC,yBACA,6DAGD,kCACC,0EAGD,UACC,iBACA,WACA,eACA,iBACA,sBACA,kBACA,iFAEA,aACC,cACA,sFAUD,aACC,kEAIF,aACC,oEAGD,YACC,gDAKF,eACC,gDAID,eACC,8CC1PF,aACC,+DAEA,mBACC,CADD,YACC,sBACA,CADA,kBACA,sFAEA,aACC,kBACA,+RAEA,aAGC,SACA,sBACA,kB7BFW,+3B6BKX,YAGC,gBACA,oMAIF,U/B+HuB,2B+B3HtB,kBACA,MACA,UACA,wLAGD,wB9B4b2C,wG8Btb3C,MACC,sB9Byb0C,yM8Bvb1C,0BACA,gCACA,6GAEA,UACC,YACA,cACA,kBACA,QACA,SACA,kBACA,kUAGD,+BAGC,4FAKF,UACC,Y/ByFuB,iB+BvFvB,W9B2Z0C,6C8BzZ1C,uB/B3DkB,6F+BgEnB,OACC,yBACA,4BACA,gHAEA,cACC,2EAKH,eACC,0GAKA,kBACC,CADD,cACC,2GAMD,kBACC,CADD,cACC,mEAKH,YACC,eACA,YACA,sBACA,kB7BlGa,sBDyEwB,uC8B4BrC,yFAMA,aACC,2SAEA,aAGC,gHAGD,qBACC,Y/B2BuB,S+BzBvB,iBACA,sBACA,kB7BzHW,yBDmd+B,WACA,6C8BvV1C,uB/B7HkB,0V+BgIlB,YAGC,gBACA,kGAOF,qBACC,uBACA,kBACA,gBACA,qBACA,SACA,qHAEA,mBACC,CADD,YACC,eACA,sEAEA,oHAJD,iBAKE,cACA,2HAIF,mBACC,CADD,YACC,OACA,SACA,0BhCvFJ,yHgCoFG,iBAME,gBACA,sBACA,CADA,6BACA,2BhClGL,yHgC0FG,W/BN2B,uE+BqB1B,yHAfD,gBAgBE,6UAIF,UAGC,gBACA,+GAKD,qBACC,uBACA,eACA,kB7B7LU,0BHmEd,8GgCuHG,kBAOE,sHAGD,eACC,uIAEA,cACC,cACA,0BhChIN,sIgC8HK,W/B/C4B,wCDrFjC,sIgCoIK,W/BhDyB,oF+B8DxB,sIAdD,iBAeE,uEAKH,8GAjCD,oBAkCE,gHAOF,qBACC,4BACA,kBACA,0BhC7JJ,8GgC0JG,uBAME,2BhCtKL,8GgCgKG,uBAUE,uEAGD,8GAbD,oBAcE,uHAKF,Q/BzFkC,S+B2FjC,oHAMD,W/BjGkC,U+BmGjC,WACA,UACA,iB/BpGyC,kBCrFP,kB8B4LlC,wCACA,2HAEA,aACC,gHAMF,qBACC,e/BjH2B,sC+BoH3B,cACA,kBACA,CADA,aACA,yBACA,4HAEA,wBACC,+HAGD,YACC,yBACA,0BhCrNL,+GgCsMG,qCAmBE,2BhC/NL,+GgC4MG,qCAuBE,sHAOF,O/BpJiC,+H+B4JjC,U/B3JkC,sB+B8JjC,SACA,UACA,iB/B/JyC,kBClFP,mB8BoPlC,sCACA,4HAEA,aACC,oFAOJ,0BACC,CADD,mBACC,0BACA,CADA,qBACA,sEAEA,mFAJD,WAKE,kBACA,cACA,2CChWJ,kBACC,wBACA,0BAEA,yCAJD,oBAKE,6DAGD,eACC,qDAGD,gBACC,YACA,oBACA,CADA,YACA,0BACA,oDAJD,kBAKE,CALF,cAKE,kDAKF,e/BoEsC,mB+BlErC,0BACA,gDAHD,kBAIE,2DAED,wBACC,UACA,2BACA,yDAHD,2BAIE,2BAED,yDAND,gBAOE,CAPF,OAOE,WACA,2BAED,yDAVD,iBAWE,8DAED,cACC,sChCjCE,0EgCoCH,eACC,wDAGF,yDAEE,sBAED,2BACA,4BACA,UACA,0BACA,uDARD,yDAUG,WACA,iBACA,CADA,OACA,aACA,+DAIH,eACC,0BACA,6DAFD,eAGE,4DAOF,iBACC,8DAEA,qChCtEG,egCwEF,WACA,iBACA,gBACA,6DAED,cACC,0DAGF,mBACC,CADD,YACC,mBACA,CADA,cACA,aACA,2BAEA,yDALD,cAME,2BAGD,yDATD,SAUE,cACA,0DAGF,gBACC,CADD,YACC,cACA,aACA,0BAEA,wDALD,iBAME,CANF,aAME,eACA,aACA,kBACA,6EAGD,WACC,WACA,kBACA,mBACA,kBACA,eACA,mFACA,aACC,iBACA,4DAIF,qChCxHG,gBgC0HF,yEAGD,YACC,8CAMH,e/BnDsC,a+BqDrC,mBACA,0BAEA,6CALD,iBAME,mBACA,sDAED,eACC,YACA,kBACA,wDACA,qChCjJG,egCmJF,WACA,iBACA,gBACA,uDAED,aACC,eACA,WACA,iBACA,gBACA,yHACA,UAEC,yEAGF,YACC,0BAED,wEACC,oBACC,0FAED,YACC,sCCpLL,gBACC,4CACA,iBACC,iBACA,mD9B0BF,WAGE,mD8B3BA,iBACC,UACA,WACA,WACA,sEAGA,aACC,6CAIH,UACC,eACA,iBACA,2BACA,qBACA,yDAED,aACC,WACA,eACA,iBACA,mBACA,kBACA,6HACA,UAEC,iLAEC,UACC,4EAIH,eACC,eACA,iBACA,sBACA,mFACA,UACC,kDC9CJ,iBACC,0EAEA,iBACC,WACA,SACA,SACA,eACA,yCCND,QACC,UACA,gBACA,4CAEA,iBACC,gBACA,mBACA,WlC0DW,6CkCxDX,uBnCImB,+DmCDnB,aACC,kBACA,QACA,OACA,clCsDU,sEkCnDV,aACC,cACA,uDAIF,QACC,0BpCsDH,0CoCjDA,mBAGE,CAHF,YAGE,mDAGD,iBnCNa,0BDuDd,iDoCjDC,YAIE,2BpCuCH,iDoC3CC,iBAQE,CARF,aAQE,wBACA,CADA,mBACA,kBnChBQ,uDmCoBT,aACC,cACA,0BpC4BH,mDoCxBC,UAGE,CAHF,MAGE,0BACA,CADA,qBACA,mBnC7BW,iDmC2Cb,kBACC,eACA,mEAEA,OACC,UACA,sDAOF,cACC,iBnCzDY,0BDiDd,qDoCOC,cAKE,iBnC9DQ,kEmC4EV,UlCnBqC,2EmC/EtC,UACC,WACA,eACA,kBACA,iBACA,gJjCaF,WAGE,yEAHF,WAGE,4EAHF,WAGE,yEAHF,WAGE,mEAHF,WAGE,oEAHF,WAGE,qEAHF,WAGE,wEiCDA,mBACC,CADD,YACC,sBACA,CADA,kBACA,8MACA,gBAEC,iBACA,mFAED,yFAED,anCgCY,kBmC9BX,2GAKF,kBACsC,uSAIrC,UACC,2FAED,4BACC,kBACA,sBACA,CADA,6BACA,sBACA,CADA,kBACA,yEAMD,mBACC,CADD,YACC,sBACA,CADA,kBACA,mGAEA,0HAGD,sGAKA,UACC,oBACA,sCACA,yFAMF,8BACC,2IAOD,gCACC,gIAGD,qBACC,gBACA,uGAGD,mBACC,CADD,YACC,qBACA,CADA,sBACA,0BACA,CADA,qBACA,sBACA,CADA,kBACA,yBAEA,iBACA,gBACA,mBACA,CADA,cACA,2BrC9BD,sGqCqBA,eAYE,mBACA,CADA,cACA,0HAGD,YACC,mBACA,gIAEA,eACC,6IACA,oCACC,iJAIF,iBACC,oBACA,CADA,YACA,2BACA,CADA,2BACA,qBACA,CADA,sBACA,sBACA,CADA,kBACA,YACA,SACA,gBACA,eACA,oBACA,WACA,uJAEA,WACC,6GAKH,qBACC,uCAIF,uDACC,oBACC,2EAGF,uBACC,2FAKC,oBACC,aACA,kBACA,iGACA,qBACC,uDAKH,iBACC,oBACA,CADA,YACA,sBACA,CADA,6BACA,YACA,aACA,kBACA,sBACA,sCACA,wBACA,kBACA,kBACA,eACA,gBACA,WACA,gFAEA,UACC,eACA,yBACA,4DAGD,cACC,0BAIF,sDACC,YACC,YACA,UACA,mBACA,CADA,cACA,4DAGD,4BACC,CADD,eACC,yEAGD,aACC,uOAWF,mBACC,CADD,YACC,sBACA,CADA,kBACA,iQAEA,0FAGD,eACC,6EAGD,eACC,wMAUD,kBAEC,wKAGD,qBAEC,mBACA,8PAEA,UACC,CADD,MACC,4MAGD,qBACC,0MAGD,YACC,MACA,QACA,kBACA,sBACA,WACA,YACA,eACA,0BACA,8MAEA,QACC,4NAGD,UACC,eACA,iBACA,sNAGD,wBACC,0PAIF,mBACC,CADD,YACC,0GAKD,UACC,YACA,iBACA,gHAGD,gBACC,oBACA,kBACA,oHAEA,cACC,SACA,gHAIF,kBACC,kBACA,YACA,kBACA,iBACA,4PAGD,YAEC,6GAKD,UACC,YACA,iBACA,mHAEA,wBACC,wHAGD,WACC,UACA,YACA,iBACA,kBACA,+HAEA,QACC,WACA,8HAGD,SACC,YACA,4HAIF,aACC,gJAGD,wBACC,gJAGD,qBACC,8HAGD,SACC,YACA,uBAYH,KACC,SACC,mCACA,IAED,SACC,eACA,yBAIF,KACC,SACC,kCACA,IAED,SACC,eACA,2DAIF,yGAEA,SACC,sBACA,uBACA,yBACA,wBACA,4BACA,6PAEA,+FACA,gHACA,iOAEA,mBACC,CADD,YACC,qBACA,CADA,sBACA,sBACA,CADA,kBACA,6OAEA,KACC,eACA,0GAIF,kBACC,kBACA,kBACA,iBACA,uBACA,kBACA,gHACA,qBACC,yBACA,kBACA,gBACA,kBACA,WACA,YACA,qBACA,sBACA,qHACA,oBACC,gHAGF,aACC,cACA,gBACA,WACA,qHACA,gBACC,0BACA,gBACA,4EAMJ,iBACC,QACA,WACA,YACA,mBACA,gBACA,6FAEA,mBACC,CADD,YACC,qBACA,CADA,sBACA,sBACA,CADA,kBACA,kFAGD,+HAEA,UACC,kBACA,yFAGD,WACC,iBACA,qFAGD,gDAGD,yEACC,qFAMD,6BACC,qEAGD,aACC,oGASA,aACC,8FAGD,cACC,iIjC5eJ,WAGE,iIiCgfI,aACC,eACA,+FAMJ,eACC,4HAGD,gBACC,kBACA,WACA,wIAEA,oBACC,sBACA,yBACA,WACA,wFAGF,qBACC,CADD,6BACC,+FAOD,aACC,mLAGD,YACC,+NAIF,YACC,4DAIF,kBACC,qFACA,kGACA,iBACC,kDAIF,iBACC,yCAID,eACC,4DAEA,gBAKC,gBACA,kBACA,kBACA,mBACA,eACA,yEATA,iBACC,aACA,kFASD,yBACC,CADD,oBACC,sBACA,CADA,kBACA,gCACA,mCACA,oBACA,CADA,YACA,YACA,sBACA,CADA,6BACA,eACA,sBACA,eACA,uBACA,8FAEA,4BACC,yGAGD,mBACC,CADD,YACC,0BACA,CADA,oBACA,sBACA,CADA,kBACA,iIAEA,cACC,kBACA,yBACA,kBACA,iBACA,gBACA,WACA,YACA,wIAEA,UACC,YACA,+HAIF,iBACC,WACA,YACA,kBACA,2HAGD,gBACC,gBACA,kBACA,WACA,qBACA,0GAGF,mBACC,CADD,YACC,sBACA,CADA,kBACA,qBACA,CADA,sBACA,iHACA,WACC,4CACA,WACA,eACA,kBACA,gBACA,oBACA,oBACA,cACA,oBACA,qBACA,kBACA,cACA,qGAGF,gBACC,qBACA,kBACA,WACA,4GAGD,kBAEC,4HAEA,wBACC,eACA,sJAEA,aACC,gIAID,iBACC,iBACA,kBACA,uIACA,UACC,kBACA,2BACA,OACA,MACA,SACA,uHAGF,kBACC,qBACA,oBACA,qBACA,mBACA,WACA,qGAKH,kCACC,6HACA,cAIC,oIAHA,aACC,gJAGD,YACC,uGAGF,aACC,sCAIF,iFArID,cAsIE,uCAGD,iFAzID,WA0IE,qBACA,CADA,sBACA,yGAEA,cACC,qBACA,CADA,gBACA,qBACA,CADA,sBACA,4HACA,gBACC,qGAIF,eACC,4GAGD,eACC,gBACA,8GACA,gBACC,sIAED,OACC,6HAGF,oBACC,CADD,sBACC,oIACA,cACC,uCAIH,iFA3KD,oBA4KE,CA5KF,gBA4KE,qBACA,CADA,sBACA,yBACA,CADA,wBACA,oBACA,4GAIA,iBACC,aACA,yGAED,iBACC,iBACA,4HACA,eACC,gBACA,6HAIF,gBACC,qEAOJ,kBACC,sCACA,qEACC,4BACC,8BACA,iBACA,qIASH,eAEC,2CAIF,qBACC,mDAED,qBACC,0EAIA,kBAIC,kBACA,kBACA,qFALA,uBACC,2FAMA,iBACA,cACA,kBACA,+FACA,cACC,6FAID,cACC,WACA,gBACA,iBACA,6FAED,cACC,sCAGF,yEA3BD,mBA4BE,CA5BF,YA4BE,kEAGF,iBAIC,mBACA,kBACA,4EALA,uBACC,iFAKD,UACC,kBACA,qBACA,oFAGA,cACC,WACA,kBACA,iBACA,oFAED,cACC,sCAGF,gEAvBD,YAwBE,uCAED,gEA1BD,mBA2BE,CA3BF,YA2BE,+CAKH,iBACC,6DACA,cACC,iBACA,mBACA,mBAIA,kBACA,kBACA,sCALA,4DAJD,cAKE,+EAMA,iBACA,cACA,kBACA,kFACA,cACC,gFAID,cACC,WACA,gBACA,iBACA,gFAED,cACC,sCAGF,4DA9BD,mBA+BE,CA/BF,YA+BE,oEAUF,cACC,YACA,uBAIA,sCAHA,kEAHD,UAIE,0EAGD,WAEC,uBACA,4CAKH,QACC,aACA,yBACA,WACA,eACA,gBACA,uBACA,iBACA,eACA,+IAGD,uBAEC,4HAIA,eAEC,mBACA,6EAIF,YACC,qCAGD,cACC,mIAKA,6BAEC,yBACA,oBACA,iJAEA,anC94BoC,0CmCo5BtC,eACC,gCAIF,UACC,iCAGD,WACC,6iBAKA,cAGC,iBACA,gBACA,6XAGD,cAEC,gBACA,iMAIA,eACC,cACA,uNACA,YACC,mLAGF,aACC,uIAIF,gBACC,yKAIA,kBACC,+KAGD,0BACC,CADD,mBACC,iNAEA,2OACA,0OACA,iLAGD,iBACC,gBACA,iBACA,+KAEA,kBACC,gBACA,6KAED,kBACC,gBACA,yJAKH,cACC,iBACA,2JAIA,QACC,gBACA,uIAED,6BACC,6BACA,+JACA,eACC,6JAED,uBACC,uMAMF,mBACC,CADD,YACC,0BACA,CADA,qBACA,8BACA,CADA,+BACA,2MACA,cACC,+CAMJ,gBACC,sBACA,yEAKC,mCACC,kBACA,gBACA,gGACA,2BACC,gBACA,+KAED,qBACC,+CAOJ,mCACC,yBACA,2DACA,anCriCa,qDmCyiCb,wBACC,iEACA,aACC,kDAKH,2BACC,qCACA,kBACA,MACA,OACA,QACA,SACA,sBACA,kBACA,oBACA,CADA,YACA,qBACA,CADA,sBACA,sBACA,CADA,kBACA,0BACA,CADA,qBACA,WlCxnCW,kBACG,gBkC0nCd,WACA,oFACA,mBACC,CADD,YACC,sBACA,CADA,kBACA,gBACA,sBACA,CADA,6BACA,WACA,iBACA,gGAEA,8BACC,clCvoCS,2MkCyoCT,wBlCzoCS,WkC2oCR,gBACA,4EAIH,gBACC,gBACA,uGAEA,cACC,+EAED,cACC,gBACA,8EAED,cACC,eACA,2GACA,cACC,gBACA,UACA,oBACA,sHACA,cACC,mFAKH,eACC,gEAGF,iBACC,SACA,OACA,QACA,kFACA,iCACC,yFAED,gEACC,yEAOD,aACC,gBACA,8DAMF,mBACC,yEACA,cACC,yCAKH,eACC,oBACA,SACA,kBACA,cACA,gBACA,kBACA,yBACA,yBACA,WACA,mEAGA,iBACC,wEACA,qBACC,uBACA,uBACA,+BACA,2BACA,qCACA,sCAED,uEACC,4BACC,mBACA,oBACA,2CACA,0CAKJ,0BACC,yCAGD,yBACC,4DAEC,cACC,iBACA,6EACA,iBACC,QACA,iBACA,6EAQF,anCzsCY,8FmC4sCZ,anC7sCY,oEmCitCb,cACC,kBACA,wEAED,yBACC,iFAGA,WACC,mGACA,gBACC,0BrC1sCH,sDqC2tCE,UACC,CADD,MACC,2DAGD,UACC,CADD,MACC,0BACA,CADA,qBACA,mBACA,CADA,oBACA,2DAIF,yBACC,CADD,qBACC,mBACA,CADA,oBACA,4EC7zCA,eAEI,yCAGJ,eACI,8DAEA,iBACI,8CAIR,eACI,+DAEA,eACI,qCAIR,UACI,gCACA,eACA,gBACA,uBACA,iBACA,UACA,SACA,8BACA,cACA,kBACA,wCAEA,kBACI,SACA,cACA,6DAcJ,cACI,eACA,iBACA,WACA,oEAEA,2CACI,oSAIR,gBAGI,kMAKJ,UAEI,qBACA,gNAEA,qBACI,eACA,4JAIR,UAEI,qBACA,sFAGJ,WACI,eACA,yFAGJ,UACI,eACA,yqBAKJ,UAQI,YACA,qVAKJ,WAII,4FAKJ,WACI,6FAKA,uBACI,UACA,+bAEJ,sBAII,UACA,6DAQR,gBACI,iBACA,kBACA,sBACA,yBACA,qBACA,WACA,YACA,QACA,kBACA,eACA,oElCnIX,WAGE,oEkCmIS,SACI,WACA,eACA,iBACA,kBACA,WACA,kBACA,eACA,iFAIR,wBACI,yBACA,wFAEA,SACI,iFAIR,gBACI,6DAIJ,yCACI,gFAGA,gBACI,wGAEA,gBACI,wGAGJ,qBACI,yBACA,+GAEA,wBACI,qGAIR,UACI,0UAIR,UAII,yBACA,sWAEA,qBACI,gEAeZ,gBACI,4CACA,eACA,WACA,YACA,kBACA,QACA,iFAIJ,WACI,wGAEJ,WACI,iFAEJ,WACI,qRAIJ,WAGI,yGAEJ,WACI,kFAEJ,WACI,oLAIJ,WAEI,qBACA,oCACA,yDAOJ,eACI,WACA,eACA,gBACA,4FAEA,wBACI,0DAGR,UACI,eACA,qBACA,mBACA,gBACA,oBACA,iBACA,+BACA,kBACA,gBACA,yHAMJ,iBAEI,yBACA,cACA,mKAEA,UACI,6DAIR,wBACI,oQAEA,aAGI,4DAIR,mCACI,sECjUJ,kBACI,yFAIA,cACI,kBACA,UACA,sDAKJ,YACI,wCAGR,mBACI,CADJ,YACI,sBACA,CADA,kBACA,cACA,qDAEA,gBACI,kBACA,kDAKR,iBACI,qBACA,cACA,gBACA,WACA,yBACA,kBACA,iBACA,YACA,iBACA,WACA,4IAEA,wBACwB,sEACxB,8FACA,+FACA,8FACA,iGACA,wBACI,cACA,gFCpDb,cACC,iBACA,2EAGF,WACC,WACA,YACA,kBACA,kBACA,yBACA,qBACA,+EAEA,WACC,WACA,qEAMF,eACC,kFAEA,YACC,2BrClBY,yBqCoBZ,6GAEA,cACC,iBACA,gBACA,WACA,yBACA,uGAGD,mBACC,CADD,YACC,sBACA,CADA,kBACA,mBACA,0GAEA,QACC,iBACA,mHAMD,QACC,2BxC6BJ,kHwC9BG,mBAIE,CAJF,YAIE,uBACA,CADA,kBACA,sBACA,CADA,6BACA,wIAIF,oBACC,eACA,WACA,iBACA,gBACA,gBACA,qKAEA,oBACC,kBACA,kBACA,kBACA,WACA,YACA,eACA,yBACA,sBACA,2BxCGL,oKwCZI,aAYE,uBACA,iJAIF,UACC,+IAGD,UACC,6KAEA,qBACC,kBACA,0KAIF,wBACC,qBACA,kMAEA,UACC,2BxC7BN,sIwCdG,aAgDE,4BxC5BL,sIwCpBG,UAoDE,kBACA,0HAMD,gBACC,sIAGD,WACC,WACA,iBACA,aACA,2BxCrDL,qIwCiDI,aAOE,sIAIF,UACC,WACA,cACA,oBACA,eACA,2BxCjEL,oIwC4DI,YAQE,2BxC9DN,iFwCxDC,YA6HE,0GAIF,YACC,qIAEA,cACC,kBACA,iJAEA,gBACC,2JAGD,cACC,gBACA,WACA,wIAGD,QACC,0BxC3FJ,oIwC4EE,SAmBE,iJAMD,kBACC,oBACA,mBACA,yJAMD,eACC,4IAGD,oBACC,CADD,sBACC,0BxCzHJ,wGwC+EC,YA+CE,8CCjNJ,UvC0FuC,6gBuCtFtC,UvCsFsC,+CuCnFtC,wBACC,qDACA,qBACC,kGAIF,evC2EsC,wJuCrEtC,UvCsEsC,qPuCjEtC,iBvCiEsC,iCADA,gVuC1DrC,UvCojBoB,CA1fiB,wSuC1DrC,UvCojBoB,wIuC7iBpB,iBvCoDqC,kJuClDpC,UvCkDoC,gEuC5CtC,UvC4CsC,gBADA,wFuCpCpC,UvCoCoC,gBACA,iEuC5BrC,UvC4BqC,qGuCnBnC,UvCmBmC,+SuCPpC,UvCOoC,uUuCJnC,UvC6fkB,mUuClfnB,UvCkfmB,+VuC/elB,UvCXmC,sBACA,sDuCmBtC,evCnBsC,WADA,oEuCuBrC,evCvBqC,WACA,uIuC+BtC,sBvC/BsC,mUuCkCrC,UvClCqC,kFuCyCpC,0BACC,uGACA,UvC3CmC,4IuCqDrC,qBACC,4FAMC,qBACC,sGAUD,evCvEmC,uGuC4EnC,UvC5EmC,sEuCmFrC,UvCnFqC,wDuCyFtC,iBvCzFsC,qHuC4FrC,UvC5FqC,0tBuCoGnC,UvCpGmC,mZuC0GnC,wBvC1GmC,g0BuCsHnC,UvCtHmC,4OuC4HnC,UvC5HmC,+SuCkIpC,eAIC,8DAIF,UvC1IqC,+HuCmJrC,iBvCnJqC,qJuCsJpC,wBvCtJoC,yJuCyJnC,UvCzJmC,mJuC8JpC,wBvC9JoC,8KuCiKnC,UvCjKmC,wMuCoKlC,UvCpKkC,67CuC+KlC,eAEC,+aASA,UvC+TgB,2hBuCzShB,eACC,+PAOD,UvCiSgB,uZuC7RhB,eAGC,sFAUJ,qBvC1OoC,iHuC6OnC,UvC5OmC,wHuC+OlC,aACC,2IAGD,UvCnPkC,kJuC4PjC,aACC,wJAMA,8BACC,2dAgBN,WvCpRqC,0MuC6RnC,UvC7RmC,qpBuCiSlC,UvCjSkC,oCuCqSjC,uIASL,qBAEC,2BACA,mJACA,qBACC,2LAGA,qBACC,yKAGF,gBACC,SACA,2KAED,0BACC,sBACA,kBvChUoC,uLuCkUpC,iBvClUoC,8IuC4UrC,UvC5UqC,uGuCqVpC,eACC,yGAGF,qBvCzVqC,+GuC2VpC,eACC,wJAQF,iBvCpWqC,wFuC4WrC,qBvC7WqC,sBuC+WpC,gFAGA,qBvCjXoC,YuCmXnC,sGASA,UvC7XmC,sBACA,sGuCoYnC,UvCrYmC,sBACA,6FuC2YpC,gCACC,+EAOF,qBvCnZqC,sBuCqZpC,kEAMD,evC3ZqC,sEuC8ZrC,UvC9ZqC,mEuCiarC,qBvClaqC,gEuCqarC,qBACC,sVAOA,qBvC5aoC,qEuCsbrC,qBACC,0EAMD,UvC4DoB,+EuC1DnB,UvC0DmB,kFuCtDpB,gCACC,WvCpcoC,wFuCscpC,0BACC,uFAED,qBvCgDmB,2GuC5CpB,UvC7cqC,wLuCqdpC,UvCoCmB,6HuCzBlB,yBACC,+BACA,yDAOJ,UvCzesC,uEuCkftC,4BACC,+DAGD,qBACC,yEAGD,gCACC,sBACA,oFCtlBK,+BACI,kHACA,UxCwF4B,wFwClF5B,wBACI,gHACA,qBACI,sBxC+EoB,yRwC3E5B,UxC2E4B,uEwCpEhC,qBxCoEgC,2FwC/D5B,UxC8D4B,gEwCzDhC,UxC0DgC,2HwCrDhC,gDACI,sEAKZ,6BACI,6BACI,sS", "sources": ["webpack://wp-smushit/./node_modules/@wpmudev/shared-ui/scss/_typography.scss", "webpack://wp-smushit/./node_modules/@wpmudev/shared-ui/scss/_accessibility.scss", "webpack://wp-smushit/./node_modules/@wpmudev/shared-ui/scss/_animations.scss", "webpack://wp-smushit/./node_modules/@wpmudev/shared-ui/scss/_mixins.scss", "webpack://wp-smushit/./node_modules/@wpmudev/shared-ui/scss/_variables.scss", "webpack://wp-smushit/./node_modules/@wpmudev/shared-ui/scss/_colors.scss", "webpack://wp-smushit/./_src/scss/modules/_variables.scss", "webpack://wp-smushit/./node_modules/@wpmudev/shared-ui/scss/_icons.scss", "webpack://wp-smushit/./node_modules/@wpmudev/shared-ui/scss/_buttons.scss", "webpack://wp-smushit/./node_modules/@wpmudev/shared-ui/scss/_toggles.scss", "webpack://wp-smushit/./node_modules/@wpmudev/shared-ui/scss/_boxes.scss", "webpack://wp-smushit/./node_modules/@wpmudev/shared-ui/scss/_box-settings.scss", "webpack://wp-smushit/./node_modules/@wpmudev/shared-ui/scss/_layout.scss", "webpack://wp-smushit/./node_modules/@wpmudev/shared-ui/scss/_notifications.scss", "webpack://wp-smushit/./node_modules/@wpmudev/shared-ui/scss/_header.scss", "webpack://wp-smushit/./node_modules/@wpmudev/shared-ui/scss/_summary.scss", "webpack://wp-smushit/./node_modules/@wpmudev/shared-ui/scss/_list.scss", "webpack://wp-smushit/./node_modules/@wpmudev/shared-ui/scss/_tooltips.scss", "webpack://wp-smushit/./node_modules/@wpmudev/shared-ui/scss/_select2.scss", "webpack://wp-smushit/./node_modules/@wpmudev/shared-ui/scss/_tags.scss", "webpack://wp-smushit/./node_modules/@wpmudev/shared-ui/scss/_forms.scss", "webpack://wp-smushit/./node_modules/@wpmudev/shared-ui/scss/_radio-checkbox.scss", "webpack://wp-smushit/./node_modules/@wpmudev/shared-ui/scss/_tabs.scss", "webpack://wp-smushit/./node_modules/@wpmudev/shared-ui/scss/_sidenav.scss", "webpack://wp-smushit/./node_modules/@wpmudev/shared-ui/scss/_dropdowns.scss", "webpack://wp-smushit/./node_modules/@wpmudev/shared-ui/scss/_scores.scss", "webpack://wp-smushit/./node_modules/@wpmudev/shared-ui/scss/_footer.scss", "webpack://wp-smushit/./node_modules/@wpmudev/shared-ui/scss/_progress-bars.scss", "webpack://wp-smushit/./node_modules/@wpmudev/shared-ui/scss/_modals.scss", "webpack://wp-smushit/./node_modules/@wpmudev/shared-ui/scss/_utility.scss", "webpack://wp-smushit/./node_modules/@wpmudev/shared-ui/scss/_wp-admin-notices.scss", "webpack://wp-smushit/./node_modules/@wpmudev/shared-ui/scss/_tables.scss", "webpack://wp-smushit/./node_modules/@wpmudev/shared-ui/scss/_accordions.scss", "webpack://wp-smushit/./node_modules/@wpmudev/shared-ui/scss/_box-selectors.scss", "webpack://wp-smushit/./node_modules/@wpmudev/shared-ui/scss/_upload.scss", "webpack://wp-smushit/./node_modules/@wpmudev/shared-ui/scss/_colorpickers.scss", "webpack://wp-smushit/./node_modules/@wpmudev/shared-ui/scss/_upgrade-page.scss", "webpack://wp-smushit/./node_modules/@wpmudev/shared-ui/scss/_reviews.scss", "webpack://wp-smushit/./node_modules/@wpmudev/shared-ui/scss/_code-snippet.scss", "webpack://wp-smushit/./node_modules/@wpmudev/shared-ui/scss/_upsells.scss", "webpack://wp-smushit/./_src/scss/modules/_admin.scss", "webpack://wp-smushit/./_src/scss/modules/_directory-smush.scss", "webpack://wp-smushit/./_src/scss/modules/_cdn.scss", "webpack://wp-smushit/./_src/scss/modules/_webp.scss", "webpack://wp-smushit/./node_modules/@wpmudev/shared-ui/scss/_color-accessibility.scss", "webpack://wp-smushit/./_src/scss/accessibility/_color-accessibility.scss"], "sourcesContent": ["// Typography\n\n@if variable-exists(google-fonts-url) and $import-font == true {\n\t@import url($google-fonts-url);\n}\n\n// Base\n@include body-class(true) {\n\tfont-family: $font;\n\tfont-weight: $font--weight;\n\tfont-size: $font--size;\n\tline-height: $font--line-height;\n\tcolor: $font-color;\n\tletter-spacing: $font--letter-spacing;\n\n\t// FIX: Prevent SUI React modal from getting these margins.\n\t&:not(.sui-modal) {\n\t\tmargin: $sui-gutter $sui-gutter 0 ($sui-gutter - 20px); // Account for padding from #wpcontent.\n\n\t\t@include media( max-width, md ) {\n\t\t\tmargin: $sui-gutter-md ($sui-gutter-md - 10px) 0 ($sui-gutter-md - 20px); // Account for padding from #wpcontent.\n\t\t}\n\t}\n\n\t* {\n\t\tfont-variant-ligatures: none;\n\t\t-webkit-font-variant-ligatures: none;\n\t\ttext-rendering: optimizeLegibility;\n\t\t-moz-osx-font-smoothing: grayscale;\n\t\tfont-smoothing: antialiased;\n\t\t-webkit-font-smoothing: antialiased;\n\t\ttext-shadow: rgba(0, 0, 0, .01) 0 0 1px;\n\t}\n\n\t// Headings\n\n\th1, h2, h3, h4, h5, h6 {\n\t\tdisplay: block;\n\t\tmargin: 0.5em auto;\n\t\tpadding: 0;\n\t\tline-height: $font--line-height;\n\t\tcolor: $headings-color;\n\t\tfont-weight: $font--weight-bold;\n\t\tfont-family: $font;\n\t}\n\n\th1 {\n\t\tfont-size: $h1-font-size;\n\t\tline-height: $h1-line-height;\n\t\tcolor: $headings-color;\n\t\tmargin: 0;\n\t}\n\th2 {\n\t\tfont-size: $h2-font-size;\n\t\tline-height: $h2-line-height;\n\t}\n\n\th3 {\n\t\tfont-size: $h3-font-size;\n\t\tline-height: $h3-line-height;\n\t}\n\n\th4 {\n\t\tfont-size: $h4-font-size;\n\t}\n\n\th5 {\n\t\tfont-size: $h5-font-size;\n\t}\n\n\th6 {\n\t\tfont-size: $h6-font-size;\n\t}\n\n\t// Paragraph\n\n\tp {\n\t\tfont-family: $font;\n\t\tfont-weight: $font--weight;\n\t\tfont-size: $font--size;\n\t\tline-height: $font--line-height;\n\t\tcolor: $font-color;\n\t\tletter-spacing: $font--letter-spacing;\n\t\ttext-rendering: optimizeLegibility;\n\n\t\t&.sui-p-small {\n\t\t\tfont-size: 13px;\n\t\t\tline-height: 22px;\n\t\t\tcolor: $p-small-color;\n\t\t\tmargin: 5px 0;\n\t\t}\n\n\t\t&:first-child {\n\t\t\tmargin-top: 0;\n\t\t}\n\n\t\t&:last-of-type:not(:last-child) {\n\t\t\tmargin-bottom: $sui-gutter;\n\n\t\t\t@include media( max-width, md ) {\n\t\t\t\tmargin-bottom: $sui-gutter-md;\n\t\t\t}\n\t\t}\n\n\t\t&:last-child {\n\t\t\tmargin-bottom: 0;\n\t\t}\n\n\t}\n\n\tsmall {\n\t\tdisplay: inline-block;\n\t\tfont-size: 13px;\n\t\tline-height: 22px;\n\t\tcolor: $p-small-color;\n\t\tmargin-bottom: 5px;\n\t}\n\n\t// Links\n\n\ta {\n\t\ttext-decoration: none;\n\t\tcolor: $a-color;\n\t\tfont-weight: 500;\n\t\toutline-color: transparent;\n\t\toutline-style: none;\n\t\tbox-shadow: none;\n\n\t\t&:hover, &:focus, &:active {\n\t\t\t&:not(.sui-button) {\n\t\t\t\tcolor: $a-action-color;\n\t\t\t}\n\t\t}\n\n\t\t&.disabled {\n\t\t\tpointer-events: none;\n\n\t\t\t&:hover, &:focus, &:active {\n\t\t\t\tcolor: $a-disabled-color;\n\t\t\t\tcursor: default;\n\t\t\t}\n\n\t\t}\n\n\t}\n\n\t// Code\n\n\tcode,\n\tpre {\n\t\tfont-weight: 500;\n\t\tfont-size: 13px;\n\t\tline-height: 18px;\n\t\tcolor: $pre-color;\n\t\tbackground-color: $code-bg-color;\n\t\tborder: 1px solid $code-border-color;\n\t\tborder-radius: $border-radius;\n\t\tmargin: 0;\n\t\tpadding: 10px 15px;\n\t\twhite-space: pre;\n\t\toverflow: auto;\n\t\tmax-width: 100%;\n\t\tvertical-align: middle;\n\t\ttab-size: 4;\n\t}\n\n\tcode {\n\t\tdisplay: inline-block;\n\t\tpadding: 2px 5px;\n\t}\n\n\t// Misc\n\tb, strong {\n\t\tfont-weight: 500;\n\t}\n\n\tdfn {\n\t\tfont-style: normal;\n\t\tfont-weight: normal;\n\t\tborder-bottom: 1px dotted $dfn-border-color;\n\t}\n\n\thr {\n\t\tborder: none;\n\t\tdisplay: block;\n\t\theight: 1px;\n\t\tbackground: #e6e6e6;\n\t\tmargin: 30px 0;\n\t}\n}\n\n@include body-class($wrap: true, $rtl: false, $monochrome: false) {\n\n\t// BLOCK: Description.\n\t.sui-description {\n\t\tdisplay: block;\n\t\tmargin: 5px 0;\n\t\tcolor: $grey;\n\t\tfont: 400 13px/22px $font;\n\t\tletter-spacing: $font--letter-spacing;\n\n\t\t&:first-child {\n\t\t\tmargin-top: 0;\n\t\t}\n\n\t\t&:last-child {\n\t\t\tmargin-bottom: 0;\n\t\t}\n\t}\n}\n\n@include body-class($wrap: true, $rtl: false, $monochrome: true) {\n\n\t// BLOCK: Description.\n\t.sui-description {\n\t\tcolor: $black;\n\t}\n}", "%sui-screen-reader-text {\n\twidth: 1px;\n\tmin-width: 1px;\n\theight: 1px;\n\tmin-height: 1px;\n\toverflow: hidden;\n\tclip: rect(1px, 1px, 1px, 1px);\n\tclip-path: inset(50%);\n\tposition: absolute !important;\n\tmargin: -1px;\n\tpadding: 0;\n\tborder: 0;\n\tword-wrap: normal !important;\n}\n\n@include body-class() {\n\n\t.sui-screen-reader-text {\n\t\t@extend %sui-screen-reader-text;\n\t}\n}\n\n@include body-class(true) {\n\n\t.sui-screen-reader-text {\n\t\t@extend %sui-screen-reader-text;\n\t}\n}", "@include body-class(true) {\n\n\t.sui-fade-in {\n\t\tanimation: fadeIn 0.3s ease-in forwards;\n\n\t\t@media screen and (prefers-reduced-motion: reduce) {\n\t\t\topacity: 1;\n\t\t\tanimation: none;\n\t\t}\n\t}\n\n\t.sui-fade-out {\n\t\tanimation: fadeOut 0.3s ease-in forwards;\n\n\t\t@media screen and (prefers-reduced-motion: reduce) {\n\t\t\topacity: 0;\n\t\t\tanimation: none;\n\t\t}\n\t}\n\n\t.sui-bounce-in {\n\t\tanimation: bounceInJiggle 0.8s ease-in forwards;\n\n\t\t@media screen and (prefers-reduced-motion: reduce) {\n\t\t\topacity: 1;\n\t\t\tanimation: none;\n\t\t}\n\t}\n\n\t.sui-bounce-out {\n\t\tanimation: bounceOutJiggle 0.6s ease-out forwards;\n\n\t\t@media screen and (prefers-reduced-motion: reduce) {\n\t\t\topacity: 0;\n\t\t\tanimation: none;\n\t\t}\n\t}\n\n\t.sui-content-fade-in {\n\t\tanimation: fadeScaleIn 0.5s ease-in forwards;\n\n\t\t@media screen and (prefers-reduced-motion: reduce) {\n\t\t\topacity: 1;\n\t\t\tanimation: none;\n\t\t}\n\t}\n\n\t.sui-content-fade-out {\n\t\tanimation: fadeScaleOut 0.9s ease-in forwards;\n\n\t\t@media screen and (prefers-reduced-motion: reduce) {\n\t\t\topacity: 0;\n\t\t\tanimation: none;\n\t\t}\n\t}\n}\n\n@keyframes bounceInDown {\n\tfrom, 60%, 75%, 90%, to {\n\t\tanimation-timing-function: cubic-bezier(0.215, 0.610, 0.355, 1.000);\n\t}\n\t0% {\n\t\topacity: 0;\n\t\ttransform: translate3d(0, -3000px, 0);\n\t}\n\t60% {\n\t\topacity: 1;\n\t\ttransform: translate3d(0, 25px, 0);\n\t}\n\t75% {\n\t\ttransform: translate3d(0, -10px, 0);\n\t}\n\t90% {\n\t\ttransform: translate3d(0, 5px, 0);\n\t}\n\tto {\n\t\ttransform: none;\n\t}\n}\n\n@keyframes bounceInJiggle {\n\t0%   { transform: translateY(-200%) scale(1.5); opacity:0; }\n\t30%  { opacity:1; }\n\t58%  { transform: translateY(0) rotate(1deg) scale(1); opacity: 1 }\n\t72%  { transform: translateY(-30px) rotate(-1deg); }\n\t80%  { transform: translateY(0) rotate(0.5deg); }\n\t100% { transform: translateY(0) rotate(0deg); }\n}\n\n@keyframes bounceOutJiggle {\n\t0%   { transform: translateY(0); }\n\t10%  { transform: translateY(-10px) rotate(-0.5deg); }\n\t30%  { transform: translateY(20px) rotate(8deg); }\n\t70%  { opacity: 1; }\n\t90%  { transform: translateY(300%) translateX(40px) rotate(35deg); opacity: 0; }\n\t100% { display: none; }\n}\n\n@keyframes bounceOut {\n\t20% {\n\t\ttransform: scale3d(.9, .9, .9);\n\t}\n\t50%, 55% {\n\t\topacity: 1;\n\t\ttransform: scale3d(1.1, 1.1, 1.1);\n\t}\n\tto {\n\t\topacity: 0;\n\t\ttransform: scale3d(.3, .3, .3);\n\t}\n}\n\n@keyframes fadeIn {\n\t0% {\n\t\topacity: 0;\n\t}\n\t25% {\n\t\topacity: 1;\n\t}\n}\n\n@keyframes fadeScaleIn {\n\t0% {\n\t\topacity: 0;\n\t}\n\t25% {\n\t\topacity: 0;\n\t\ttransform: translate3d(0,10px,0) scale(0.9);\n\t}\n\t100% {\n\t\ttransform: translate3d(0,0,0) scale(1);\n\t}\n}\n\n@keyframes fadeOut {\n\t0% {\n\t\topacity: 1;\n\t}\n\t75% {\n\t\topacity: 1;\n\t}\n\t100% {\n\t\topacity: 0;\n\t}\n}\n\n@keyframes fadeScaleOut {\n\t0% {\n\t\topacity: 1;\n\t\ttransform: translate3d(0,0,0) scale(1);\n\t}\n\t25% {\n\t\topacity: 0;\n\t\ttransform: translate3d(0,10px,0) scale(0.9);\n\t}\n\t100% {\n\t\topacity: 0;\n\t}\n}\n\n@keyframes fadeInLeft {\n\tfrom {\n\t\topacity: 0;\n\t\ttransform: translate3d(-50px, 0, 0);\n\t}\n\tto {\n\t\topacity: 1;\n\t\ttransform: none;\n\t}\n}\n\n@keyframes fadeInRight {\n\tfrom {\n\t\topacity: 0;\n\t\ttransform: translate3d(50px, 0, 0);\n\t}\n\tto {\n\t\topacity: 1;\n\t\ttransform: none;\n\t}\n}\n\n@keyframes spin {\n\t0% {\n\t\ttransform: rotate(0deg);\n\t}\n\t100% {\n\t\ttransform: rotate(360deg);\n\t}\n}\n", "// ==================================================\n// Generates the required versioned body class.\n//\n// $wrap: true\n// $rtl: false\n// $monochrome: false\n// ==================================================\n@mixin body-class($wrap: false, $rtl: false, $monochrome: false) {\n\n\t$formatted-version: str-replace($sui-version, '.', '-');\n\n\t@if ( $wrap and $sui-wrap-class ) {\n\n\t\t@if $rtl {\n\n\t\t\t.sui-#{$formatted-version}.rtl {\n\n\t\t\t\t.#{$sui-wrap-class} {\n\n\t\t\t\t\t@if $monochrome {\n\n\t\t\t\t\t\t&.sui-color-accessible {\n\t\t\t\t\t\t\t@content;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\n\t\t\t\t\t@else {\n\t\t\t\t\t\t@content;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t@else {\n\n\t\t\t.sui-#{$formatted-version} .#{$sui-wrap-class} {\n\n\t\t\t\t@if $monochrome {\n\n\t\t\t\t\t&.sui-color-accessible {\n\t\t\t\t\t\t@content;\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t@else {\n\t\t\t\t\t@content;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\n\t@else {\n\n\t\t@if $rtl {\n\n\t\t\t.sui-#{$formatted-version}.rtl {\n\t\t\t\t@content;\n\t\t\t}\n\t\t}\n\n\t\t@else {\n\n\t\t\t.sui-#{$formatted-version} {\n\t\t\t\t@content;\n\t\t\t}\n\t\t}\n\t}\n}\n\n// Prevent text such as titles from wrapping.\n@mixin text-truncate {\n\twhite-space: nowrap;\n\toverflow: hidden;\n\ttext-overflow: ellipsis;\n}\n\n// Media queries.\n// Examples:\n//   @include media(min-width, lg) {}\n//   @include media(max-width, sm) {}\n//   @include media(between, sm, lg) {}\n//\n@mixin media($type, $breakpoint-name-1, $breakpoint-name-2: null) {\n\t@if ($type == min-width) {\n\t\t$min-breakpoint-width: #{map-get($sui-breakpoints, $breakpoint-name-1)};\n\t\t@media (min-width: $min-breakpoint-width) {\n\t\t\t@content;\n\t\t}\n\t}\n\t@else if ($type == max-width) {\n\t\t$max-breakpoint-width: map-get($sui-breakpoints, $breakpoint-name-1) - 1px;\n\t\t@media (max-width: $max-breakpoint-width) {\n\t\t\t@content;\n\t\t}\n\t}\n\t@else if ($type == between) {\n\t\t$min-breakpoint-width: map-get($sui-breakpoints, $breakpoint-name-1);\n\t\t$max-breakpoint-width: map-get($sui-breakpoints, $breakpoint-name-2) - 1px;\n\t\t@media (min-width: $min-breakpoint-width) and (max-width: $max-breakpoint-width) {\n\t\t\t@content;\n\t\t}\n\t}\n\t@else {\n\t\t@warn \"Unfortunately, no type could be retrieved from `#{$type}`. \"\n\t\t+ \"Use either `min-width`, `max-width`, or `between`.\";\n\t}\n}\n\n// High PPI display background\n@mixin background-2x($path, $ext: \"png\", $w: auto, $h: auto, $pos: left top, $repeat: no-repeat) {\n\t$at1x_path: \"#{$path}.#{$ext}\";\n\t$at2x_path: \"#{$path}@2x.#{$ext}\";\n\n\tbackground: url(\"#{$at1x_path}\") $repeat $pos;\n\tbackground-size: $w $h;\n\n\t@media only screen and (-webkit-min-device-pixel-ratio: 2),\n\tonly screen and (min--moz-device-pixel-ratio: 2),\n\tonly screen and (-o-min-device-pixel-ratio: 2/1),\n\tonly screen and (min-device-pixel-ratio: 2),\n\tonly screen and (min-resolution: 192dpi),\n\tonly screen and (min-resolution: 2dppx) {\n\t\tbackground-image: url(\"#{$at2x_path}\");\n\t}\n}\n", "@use \"sass:math\";\n\n$sui-version: \"2.12.23\";\n$sui-wrap-class: \"sui-wrap\";\n\n// Import Google Fonts\n$import-font: true !default;\n$google-fonts-url: \"https://fonts.bunny.net/css?family=Roboto:400,500,700\" !default;\n\n// Typography\n$font: \"Roboto\", Arial, sans-serif !default;\n$font--size: 15px !default;\n\n$font--weight: 400;\n$font--medium: 500;\n$font--weight-bold: 700;\n\n$font--line-height: 30px !default;\n$font--letter-spacing: -0.25px !default;\n\n$h1-font-size: 32px !default;\n$h2-font-size: 22px !default;\n$h3-font-size: 18px !default;\n$h4-font-size: 15px !default;\n$h5-font-size: 15px !default;\n$h6-font-size: 15px !default;\n\n$h1-line-height: 40px;\n$h2-line-height: 35px;\n$h3-line-height: 40px;\n\n$border-radius: 4px !default;\n$transition: all 0.3s ease !default;\n\n// Layout\n$sui-gutter: 30px !default;\n$sui-gutter-md: 20px !default;\n$sui-total-grid-cols: 12 !default;\n$sui-breakpoints: (\n\txs: 0px,\n\tsm: 480px,\n\tmd: 783px,\n\tlg: 1200px,\n) !default;\n\n// ============================================================\n// Summary\n\n// SIZE: Regular\n$summary-size-width: 222px !default;\n$summary-size-height: 212px !default;\n\n$summary-image--width: 96px !default;\n$summary-image--height: 96px !default;\n\n$summary-image--position-x: 80px !default;\n$summary-image--position-y: center !default;\n$summary-image--position: $summary-image--position-x $summary-image--position-y !default;\n\n// SIZE: Small\n$summary-size-sm: 151px !default;\n\n$summary-image-sm--width: 96px !default;\n$summary-image-sm--height: 96px !default;\n\n$summary-image-sm--position-x: 30px !default;\n$summary-image-sm--position-y: center !default;\n$summary-image-sm--position: $summary-image-sm--position-x\n\t$summary-image-sm--position-y !default;\n\n// ============================================================\n// Forms (_forms.scss)\n$form--input-height-base: 40px !default;\n$form--input-border-radius: $border-radius !default;\n$form--input-line-height: 20px !default;\n$form--input-font-weight: 500 !default;\n\n$form--input-error-font-size: 12px !default;\n$form--input-error-line-height: 16px !default;\n$form--input-error-font-weight: 500 !default;\n$form--label-font: $font !default;\n$form--label-font-weight: 600 !default;\n$form--label-font-size: 12px !default;\n$form--label-line-height: 16px !default;\n$form--description-font-weight: 400 !default;\n$form--description-font-size: 13px !default;\n\n// ============================================================\n// Radio & Checkbox (_radio-checkbox.scss)\n\n// SIZE: Default\n$radio-checkbox--size: 16px !default;\n$radio-checkbox--font-size: 15px !default;\n$radio-checkbox--line-height: 22px !default;\n$radio-checkbox--check-size: 6px !default;\n$radio-checkbox--icon-size: 10px !default;\n\n// SIZE: Small\n$radio-checkbox--font-size-sm: 13px !default;\n\n// ============================================================\n// Paths\n$sui-image-path: \"../images/\" !default;\n$sui-font-path: \"../fonts/\" !default;\n\n// ============================================================\n// Scores\n$circle-score-sm: 30px !default;\n$circle-score-lg: 120px !default;\n$circle-score-label-spacing: 10px !default;\n\n// ============================================================\n// Sidenav\n$sidenav-width: 220px !default;\n\n// ============================================================\n// Margin\n$default-margin: 30px !default;\n\n// ============================================================\n// Padding\n$default-padding: 30px !default;\n\n// ============================================================\n// Tables\n$table--border-width: 1px !default;\n$table--border-style: solid !default;\n$table--text-font-size: 13px !default;\n$table--text-line-height: 22px !default;\n\n// ============================================================\n// Select\n$select-dropdown-handle-size: 40px !default;\n$select-dropdown-handle-size-sm: 30px !default;\n\n// ============================================================\n// Accordions\n$accordion--grid: 12 !default;\n\n// ============================================================\n// Upload (_upload.scss)\n\n// Image container\n$file-upload--image-size: 40px !default;\n$file-upload--image-border-width: 1px !default;\n$file-upload--image-border-style: solid !default;\n$file-upload--image-padding: 1px !default;\n\n// Button: Add\n$file-upload--add-border-width: 1px !default;\n$file-upload--add-border-style: dashed !default;\n\n// ============================================================\n// Color Pickers (_colorpickers.scss)\n\n// Default\n$colorpicker--hex-width: 135px !default;\n$colorpicker--rgba-width: 215px !default;\n$colorpicker--button-size: 30px !default;\n$colorpicker--border-width: 1px !default;\n\n// Input\n$colorpicker--input-height: 30px !default;\n$colorpicker--input-font-size: 12px !default;\n$colorpicker--input-line-height: 16px !default;\n\n// Iris\n$colorpicker--iris-hex-width: 210px !default;\n$colorpicker--iris-rgba-width: 240px !default;\n$colorpicker--iris-square-size: 160px !default;\n$colorpicker--iris-square-size-sm: 140px !default;\n$colorpicker--iris-square-value-size: 0 !default;\n$colorpicker--iris-square-handle-size: 16px !default;\n$colorpicker--iris-square-handle-border-width: 3px !default;\n$colorpicker--iris-slider-size: 190px !default;\n$colorpicker--iris-slider-handle-size: 10px !default;\n$colorpicker--iris-slider-handle-border-width: 2px !default;\n$colorpicker--iris-palette-size: 20px !default;\n\n// ============================================================\n// Box Selectors (_box-selectors.scss)\n\n// Container\n$box-selectors--spacing: 20px !default;\n$box-selectors--columns: (\n\tcol-1: 100%,\n\tcol-2: 50%,\n\tcol-3: 33.33%,\n\tcol-4: 25%,\n\tcol-5: 20%,\n) !default;\n\n// Item\n$box-selector--height: 60px !default;\n$box-selector--padding: 10px !default;\n$box-selector--font-size: 12px !default;\n$box-selector--line-height: 20px !default;\n\n$box-selector--font-size-lg: 13px !default;\n$box-selector--line-height-lg: 22px !default;\n\n// Item icon\n$box-selector--icon-width: 30px !default;\n$box-selector--icon-size: 16px !default;\n\n// Item image\n$box-selector--image-width: 24px !default;\n\n// Item ribbon\n// Used as blue triangle on top-right corner when item is selected\n$box-selector--ribbon-height: 80px !default;\n\n// Item (vertical)\n$box-selector-vertical--height: 80px !default;\n$box-selector-vertical--padding-top: 16px !default;\n$box-selector-vertical--icon-spacing: 7px !default;\n$box-selector-vertical--padding-bottom: #{$box-selector-vertical--height -\n\t(\n\t\t$box-selector--line-height + $box-selector--icon-size +\n\t\t\t$box-selector-vertical--icon-spacing +\n\t\t\t$box-selector-vertical--padding-top\n\t)} !default;\n\n// ============================================================\n// Progress Bars (_progress-bars.scss)\n$progress-block--size: 60px;\n$progress-bar--size: 10px;\n\n// ============================================================\n// Buttons (_buttons.scss)\n$button-dashed-height-lg: 70px !default;\n$button-dashed-height: 60px !default;\n$button-dashed-height-md: 50px !default;\n\n// ============================================================\n// Box Builder (_box-builder.scss)\n\n// Field - Basic\n$box-builder--field-height: 60px !default;\n$box-builder--field-height-md: 50px !default;\n$box-builder--field-spacing: 20px !default;\n$box-builder--field-shadow: 0 2px 7px 0 rgba(0, 0, 0, 0.05) !default;\n$box-builder--field-shadow-hover: 0 0 0 4px rgba(0, 0, 0, 0.02),\n\t0 4px 15px 0 rgba(0, 0, 0, 0.05) !default;\n\n// Field - Icon\n$box-builder--field-icon-size: 30px !default;\n\n// ============================================================\n// Dialogs (_modals.scss)\n$wordpress: (\n\tadminbar: 32px,\n\tadminbar-sm: 32px,\n\tadminmenu: 160px,\n\t// Size for unfolded admin menu.\n\tadminmenu-sm: 36px,\n\t// Size for folded admin menu.\n) !default;\n\n$scrollbar--width: 20px !default;\n\n$modal: (\n\tz-index: 13,\n) !default;\n\n$modal-size: (\n\txl: 980px,\n\tlg: 600px,\n\tmd: 500px,\n\tsm: 400px,\n) !default;\n\n$modal-spacing: (\n\t0: (\n\t\t0,\n\t\t0,\n\t),\n\t10: (\n\t\t10px,\n\t\t10px,\n\t),\n\t20: (\n\t\t20px,\n\t\t20px,\n\t),\n\t30: (\n\t\t30px,\n\t\t20px,\n\t),\n\t40: (\n\t\t40px,\n\t\t20px,\n\t),\n\t50: (\n\t\t50px,\n\t\t40px,\n\t),\n\t60: (\n\t\t60px,\n\t\t40px,\n\t),\n\t70: (\n\t\t70px,\n\t\t40px,\n\t),\n\t80: (\n\t\t80px,\n\t\t40px,\n\t),\n\t90: (\n\t\t90px,\n\t\t50px,\n\t),\n\t100: (\n\t\t100px,\n\t\t50px,\n\t),\n) !default;\n\n$modal-steps: (\n\tlg: 14px,\n\tmd: 10px,\n\tsm: 7px,\n) !default;\n\n$modal-logo: (\n\tsize: 70px,\n\tframe-width: 5px,\n\tframe-color: $white,\n) !default;\n\n$onboard: (\n\tspacing: 40px,\n\tbutton: 40px,\n\tbutton-md: 30px,\n) !default;\n\n// ============================================================\n// Toggles (_toggles.scss)\n\n$toggle-width: 34px !default;\n$toggle-height: 16px !default;\n$toggle-font-size: 15px !default;\n$toggle-font-height: 22px !default;\n\n// ************************************************************\n// Datepicker - (_calendar.scss)\n\n$calendar: (\n\tpadding: #{math.div($sui-gutter, 2) - 1px},\n\tborder-width: 1px,\n\tborder-style: solid,\n\tborder-color: palette(gray, lighter),\n\tborder-radius: $border-radius,\n\tbackground: $white,\n\tshadow: 0 3px 7px 0 rgba(0, 0, 0, 0.07),\n\tcell-size: 30px,\n);\n\n$calendar-padding: map-get($calendar, padding) !default;\n$calendar-border--width: map-get($calendar, border-width) !default;\n$calendar-border--style: map-get($calendar, border-style) !default;\n$calendar-border--color: map-get($calendar, border-color) !default;\n$calendar-border--radius: map-get($calendar, border-radius) !default;\n$calendar-background: map-get($calendar, background) !default;\n$calendar-shadow: map-get($calendar, shadow) !default;\n$calendar-cell: map-get($calendar, cell-size) !default;\n\n$month: (\n\tcolor: palette(gray, dark),\n\tfont-size: 12px,\n\tline-height: map-get($calendar, cell-size),\n\tfont-family: $font,\n\tfont-weight: bold,\n\tletter-spacing: $font--letter-spacing,\n\ttext-align: center,\n);\n\n$month-color: map-get($month, color) !default;\n$month-size: map-get($month, font-size) !default;\n$month-height: map-get($month, line-height) !default;\n$month-family: map-get($month, font-family) !default;\n$month-weight: map-get($month, font-weight) !default;\n$month-spacing: map-get($month, letter-spacing) !default;\n$month-align: map-get($month, text-align) !default;\n\n$day: (\n\tfont-size: 12px,\n\tline-height: map-get($calendar, cell-size),\n\tfont-family: $font,\n\tfont-weight: 400,\n\tletter-spacing: $font--letter-spacing,\n\ttext-align: center,\n\tdefault-color: palette(gray, light),\n\tdefault-background: $white,\n\thover-color: palette(gray, dark),\n\thover-background: palette(silver, light),\n\tactive-color: $blue,\n\tactive-background: palette(blue, light),\n\tinactive-color: palette(gray, lighter),\n\tinactive-background: $white,\n\ttoday-color: palette(gray, default),\n\ttoday-background: palette(yellow, light),\n);\n\n$day-size: map-get($day, font-size) !default;\n$day-height: map-get($day, line-height) !default;\n$day-family: map-get($day, font-family) !default;\n$day-weight: map-get($day, font-weight) !default;\n$day-spacing: map-get($day, letter-spacing) !default;\n$day-align: map-get($day, text-align) !default;\n\n$day-default--color: map-get($day, default-color) !default;\n$day-default--background: map-get($day, default-background) !default;\n$day-hover--color: map-get($day, hover-color) !default;\n$day-hover--background: map-get($day, hover-background) !default;\n$day-active--color: map-get($day, active-color) !default;\n$day-active--background: map-get($day, active-background) !default;\n$day-inactive--color: map-get($day, inactive-color) !default;\n$day-inactive--background: map-get($day, inactive-background) !default;\n$day-today--color: map-get($day, today-color) !default;\n$day-today--background: map-get($day, today-background) !default;\n", "// Colors\n$palettes: (\n\tmono: (\n\t\tdefault: #000000,\n\t\tblack:   #000000,\n\t\twhite:   #FFFFFF,\n\t),\n\tgray: (\n\t\tdefault: #666666,\n\t\tlighter: #DDDDDD,\n\t\tlight:   #888888,\n\t\tdark:    #333333,\n\t),\n\tsilver: (\n\t\tdefault: #F2F2F2,\n\t\tlight:   #F8F8F8,\n\t\tsoft:    #E6E6E6,\n\t\tmedium:  #AAAAAA,\n\t),\n\tblue: (\n\t\tdefault: #17A8E3,\n\t\tlight:   #E1F6FF,\n\t\tghost:   #E1F6FF,\n\t),\n\tred: (\n\t\tdefault: #FF6D6D,\n\t\tlight:   #FFE5E9,\n\t\tghost:   #FFE5E9,\n\t),\n\tyellow: (\n\t\tdefault: #FECF2F,\n\t\tlight:   #FFF5D5,\n\t\tghost:   #FFF5D5,\n\t),\n\tgreen: (\n\t\tdefault: #1ABC9C,\n\t\tlight:   #D1F1EA,\n\t\tghost:   #D1F1EA,\n\t),\n\torange: (\n\t\tdefault: #FF7E41,\n\t\tlight:   #FFE5D9,\n\t\tghost:   #FFE5D9,\n\t),\n\tpurple: (\n\t\tdefault: #8D00B1,\n\t\tlight:   #F9E1FF,\n\t\tghost:   #F9E1FF,\n\t),\n\tmonos: (\n\t\tcloud:       #FAFAFA,\n\t\thaze:        #F8F8F8,\n\t\tsmoke:       #F2F2F2,\n\t\tsilver:      #E6E6E6,\n\t\tovercast:    #DDDDDD,\n\t\tfiftyshades: #AAAAAA,\n\t\tgrey:        #888888,\n\t\tironmike:    #666666,\n\t\tnightrider:  #333333,\n\t),\n) !default;\n\n$cloud:        palette(monos, cloud)       !default;\n$haze:         palette(monos, haze)        !default;\n$smoke:        palette(monos, smoke)       !default;\n$silver:       palette(monos, silver)    !default;\n$overcast:     palette(monos, overcast)    !default;\n$fiftyshades:  palette(monos, fiftyshades) !default;\n$grey:         palette(monos, grey)      !default;\n$ironmike:     palette(monos, ironmike)    !default;\n$nightrider:   palette(monos, nightrider)  !default;\n$blue:         palette(blue, default)    !default;\n$green:        palette(green, default)   !default;\n$yellow:       palette(yellow, default)  !default;\n$red:          palette(red, default)     !default;\n$purple:       palette(purple, default)  !default;\n$blue-ghost:   palette(blue, ghost)      !default;\n$green-ghost:  palette(green, ghost)     !default;\n$yellow-ghost: palette(yellow, ghost)    !default;\n$red-ghost:    palette(red, ghost)       !default;\n$purple-ghost: palette(purple, ghost)    !default;\n\n// ============================================================\n// Old colors (organization)\n\n$success:                               palette(green)                !default;\n$warning:                               palette(yellow)               !default;\n$error:                                 palette(red)                  !default;\n$info:                                  palette(blue)                 !default;\n\n// Primary Colors\n$white:                                 palette(mono, white)          !default;\n$black:                                 palette(mono, black)          !default;\n$gray:                                  palette(gray)                 !default;\n$gray-alt:                              palette(gray, dark)           !default;\n\n// Boxes Colors (_boxes.scss)\n$box-bg-color:                          palette(mono, white)          !default;\n$box-box-shadow-color:                  palette(silver, soft)          !default;\n$box-header-border-color:               palette(silver, soft)          !default;\n$box-footer-border-color:               palette(silver, soft)          !default;\n$box-settings-box-border-color:         palette(silver, soft)         !default;\n$box-settings-label-color:              palette(gray, dark)           !default;\n$box-upsell-p-color:                    palette(gray, dark)           !default;\n$box-upsell-border-color:               palette(purple)                !default;\n\n// ============================================================\n// Buttons Colors (_buttons.scss)\n\n$button-colors: blue green red orange yellow purple white;\n\n$button-shadow: (\n\tdefault: $overcast,\n\tblue:    $blue-ghost,\n\tgreen:   $green-ghost,\n\tred:     $red-ghost,\n\torange:  palette(orange, ghost),\n\tyellow:  $yellow-ghost,\n\tpurple:  $purple-ghost,\n\twhite:   $white\n) !default;\n\n$button-border: (\n\tdefault: palette(gray, lighter),\n\tblue:    palette(blue, light),\n\tgreen:   palette(green, light),\n\tred:     palette(red, light),\n\torange:  palette(orange, light),\n\tyellow:  palette(yellow, light),\n\tpurple:  palette(purple, light),\n\twhite:   palette(mono, white)\n) !default;\n\n$button-background: (\n\tdefault: palette(gray, light),\n\tblue:    palette(blue, default),\n\tgreen:   palette(green, default),\n\tred:     palette(red, default),\n\torange:  palette(orange, default),\n\tyellow:  palette(yellow, default),\n\tpurple:  palette(purple, default),\n\twhite:   palette(mono, white)\n) !default;\n\n$button-text-color: (\n\tdefault: palette(mono, white),\n\tblue:    palette(mono, white),\n\tgreen:   palette(mono, white),\n\tred:     palette(mono, white),\n\torange:  palette(mono, white),\n\tyellow:  palette(gray, dark),\n\tpurple:  palette(mono, white),\n\twhite:   palette(mono, black)\n) !default;\n\n$button-disabled--background: palette(silver, soft);\n$button-disabled--color:      palette(silver, medium);\n\n// Upsell Button\n$button-upsell--border-static: palette(green, light)   !default;\n$button-upsell--border-active: palette(green, default) !default;\n$button-upsell--color-static:  palette(green, default) !default;\n$button-upsell--color-active:  palette(mono, white)    !default;\n\n// Dashed Button\n$button-dashed--border:        palette(silver, medium) !default;\n$button-dashed--background:    transparent               !default;\n$button-dashed--color:         palette(gray, light)    !default;\n\n// ============================================================\n// Icon Buttons Colors (_buttons.scss)\n\n$button-icon--shadow: (\n\tdefault: $overcast,\n\tblue:    $blue-ghost,\n\tgreen:   $green-ghost,\n\tred:     $red-ghost,\n\torange:  palette(orange, ghost),\n\tyellow:  $yellow-ghost,\n\tpurple:  $purple-ghost,\n\twhite:   $white\n) !default;\n\n$button-icon--border: (\n\tdefault: palette(gray, lighter),\n\tblue:    palette(blue, light),\n\tgreen:   palette(green, light),\n\tred:     palette(red, light),\n\torange:  palette(orange, light),\n\tyellow:  palette(yellow, light),\n\tpurple:  palette(purple, light),\n\twhite:   palette(mono, white)\n) !default;\n\n$button-icon--background: (\n\tdefault: palette(silver, default),\n\tblue:    palette(blue, light),\n\tgreen:   palette(green, light),\n\tred:     palette(red, light),\n\torange:  palette(orange, light),\n\tyellow:  palette(yellow, light),\n\tpurple:  palette(purple, light),\n\twhite:   rgba(0,0,0,0.2)\n) !default;\n\n$button-icon--background-hover: (\n\tdefault: palette(gray, light),\n\tblue:    palette(blue, default),\n\tgreen:   palette(green, default),\n\tred:     palette(red, default),\n\torange:  palette(orange, default),\n\tyellow:  palette(yellow, default),\n\tpurple:  palette(purple, default),\n\twhite:   palette(mono, white)\n) !default;\n\n$button-icon--background-filled: (\n\tdefault: palette(silver, soft),\n\tblue:    palette(blue, default),\n\tgreen:   palette(green, default),\n\tred:     palette(red, default),\n\torange:  palette(orange, default),\n\tyellow:  palette(yellow, default),\n\tpurple:  palette(purple, default),\n\twhite:   palette(mono, white)\n) !default;\n\n$button-icon--color: (\n\tdefault: palette(gray, light),\n\tblue:    palette(blue, default),\n\tgreen:   palette(green, default),\n\tred:     palette(red, default),\n\torange:  palette(orange, default),\n\tyellow:  palette(yellow, default),\n\tpurple:  palette(purple, default),\n\twhite:   palette(mono, white)\n) !default;\n\n$button-icon--color-hover: (\n\tdefault: palette(gray, dark),\n\tblue:    palette(blue, default),\n\tgreen:   palette(green, default),\n\tred:     palette(red, default),\n\torange:  palette(orange, default),\n\tyellow:  palette(yellow, default),\n\tpurple:  palette(purple, default),\n\twhite:   palette(mono, white)\n) !default;\n\n$button-icon--alt: (\n\tdefault: palette(mono, white),\n\tblue:    palette(mono, white),\n\tgreen:   palette(mono, white),\n\tred:     palette(mono, white),\n\torange:  palette(mono, white),\n\tyellow:  palette(gray, dark),\n\tpurple:  palette(mono, white),\n\twhite:   palette(mono, black)\n) !default;\n\n$button-icon--color-filled: (\n\tdefault: palette(silver, medium),\n\tblue:    palette(mono, white),\n\tgreen:   palette(mono, white),\n\tred:     palette(mono, white),\n\torange:  palette(mono, white),\n\tyellow:  palette(gray, dark),\n\tpurple:  palette(mono, white),\n\twhite:   palette(mono, white)\n) !default;\n\n// ============================================================\n// Typography Colors (_typography.scss)\n$headings-color:                        palette(gray, dark)           !default;\n$font-color:                            $gray !default;\n$p-small-color:                         palette(gray, light)          !default;\n$a-color:                               palette(blue)                 !default;\n$a-action-color:                        darken($a-color, 10%)         !default;\n$a-disabled-color:                      palette(gray, light)          !default;\n$code-bg-color:                         palette(silver, light)        !default;\n$code-border-color:                     palette(silver, soft)         !default;\n$dfn-border-color:                      palette(gray, light)          !default;\n$pre-color:                             palette(gray, dark)           !default;\n\n// Notifications Colors (_notifications.scss)\n$notice-font-color:                     palette(gray, dark)           !default;\n$notice-default-bg-color:               $white                        !default;\n$notice-default-icon-color:             palette(gray, light)          !default;\n$notice-warning-icon-color:             $warning                      !default;\n$notice-success-icon-color:             $success                      !default;\n$notice-error-icon-color:               $error                        !default;\n$notice-info-icon-color:                palette(blue)                 !default;\n\n// Tooltips (_tooltips.scss)\n$tooltips-color:                        palette(gray, dark)           !default;\n\n// ============================================================\n// Box Selectors (_box-selectors.scss)\n\n// Container\n$box-selectors--background: palette(silver, light) !default;\n\n// Item\n$box-selector--border-color: rgba(230, 230, 230, 0.5) !default;\n$box-selector--background: $white !default;\n$box-selector--color: palette(gray, light) !default;\n$box-selector--box-shadow: 0 2px 7px 0 rgba(0, 0, 0, 0.05) !default;\n\n$box-selector--active-background: palette(blue, light) !default;\n$box-selector--active-color: palette(blue, default) !default;\n$box-selector--active-box-shadow:  0 0 0 4px rgba(0, 0, 0, 0.02), 0 4px 15px 0 rgba(0, 0, 0, 0.05);\n\n// Item icon\n$box-selector--icon-color: palette(silver, medium) !default;\n\n// Item ribbon\n// Used as blue triangle on top-right corner when item is selected\n$box-selector--ribbon-color: $white !default;\n$box-selector--ribbon-background: palette(blue, default) !default;\n\n// ============================================================\n// Box Builder (_box-builder.scss)\n\n// Main container\n$box-builder--background:   $white !default;\n$box-builder--border-color: palette(silver, soft) !default;\n\n// Body\n$box-builder--body-background: palette(silver, light) !default;\n$box-builder--body-message:    palette(gray, light) !default;\n\n// Fields\n$box-builder--field-background: $white                   !default;\n$box-builder--field-color:      palette(gray, dark)    !default;\n$box-builder--field-move-color: palette(gray, lighter) !default;\n$box-builder--field-move-color-hover: palette(gray, light) !default;\n$box-builder--field-border:     palette(silver, soft)  !default;\n\n// Field - Notice\n$box-builder--notice-background: palette(silver, default) !default;\n$box-builder--notice-color:      $blue !default;\n\n// ============================================================\n// Select (_select.scss)\n\n// Container\n$select-container--border-color:        palette(gray, lighter)  !default;\n$select-container--background:          #FAFAFA                 !default;\n$select-container--background-active:   $white                    !default;\n\n// List value\n$select-value--color:                   palette(gray, dark)     !default;\n\n// List results\n$select-results--border-color:          palette(gray, lighter)  !default;\n$select-results--background:            $white                    !default;\n$select-results--color:                 palette(gray, light)    !default;\n$select-results--background-hover:      rgba(51, 51, 51, 0.05)  !default;\n$select-results--color-hover:           palette(gray, default)  !default;\n$select-results--background-current:    palette(gray, light)    !default;\n$select-results--color-current:         $white                    !default;\n$select-results--optgroup-color:        palette(silver, medium) !default;\n\n// Search results\n$select-search--border-color:           palette(gray, lighter)  !default;\n$select-search--background:             #FAFAFA                 !default;\n$select-search--background-focus:       $white                    !default;\n$select-search--color:                  palette(gray, dark)     !default;\n$select-search--placeholder:            palette(silver, medium) !default;\n$select-search--result:                 palette(gray, light)    !default;\n\n// Mobile select\n$select-mobile-nav-handle-color:        palette(gray, lighter)  !default;\n$select-mobile-nav-handle-hover-color:  palette(gray, light)    !default;\n\n// ============================================================\n// Sidebar (_sidebar.scss)\n$sidenav-tab-color:                     palette(gray)                 !default;\n$sidenav-tab-color-hover:               palette(gray, dark)           !default;\n$sidenav-tab-active-color:              palette(gray, dark)           !default;\n$sidenav-tab-active-bg-color:           palette(silver, soft)         !default;\n$sidenav-tab-icon-color:                palette(green)                !default;\n\n// ============================================================\n// Summary (_summary.scss)\n$summary-details--color:     palette(gray, dark)   !default;\n$summary-details--sub-color: palette(gray, light)  !default;\n\n$summary-list--border-color: palette(silver, soft) !default;\n$summary-list--color:        palette(gray,light)   !default;\n$summary-list--label:        palette(gray, dark)   !default;\n\n// ============================================================\n// Tags (_tags.scss)\n\n// Default tags\n$tag--default-background: palette(silver) !default;\n$tag--default-color: palette(gray, dark) !default;\n\n// Red tags\n$tag--red-border-color: palette(red, light) !default;\n$tag--red-background: $error !default;\n$tag--red-color: $white !default;\n\n// Yellow tags\n$tag--yellow-border-color: palette(yellow, light) !default;\n$tag--yellow-background: $warning !default;\n$tag--yellow-color: palette(gray, dark) !default;\n\n// Green tags\n$tag--green-border-color: palette(green, light) !default;\n$tag--green-background: $success !default;\n$tag--green-color: $white !default;\n\n// Blue tags\n$tag--blue-border-color: palette(blue, light) !default;\n$tag--blue-background: palette(blue) !default;\n$tag--blue-color: $white !default;\n\n// Purple tags\n$tag--purple-border-color: palette(purple, light) !default;\n$tag--purple-background: palette(purple) !default;\n$tag--purple-color: $white;\n\n// Disabled tags\n$tag--disabled-background: palette(silver) !default;\n$tag--disabled-color: palette(silver, medium) !default;\n\n// Pro tags\n$tag--pro-background: palette(purple) !default;\n$tag--pro-color: $white !default;\n\n// Beta tags\n$tag--beta-background: palette(orange) !default;\n$tag--beta-color: $white !default;\n\n// Forms (_forms.scss)\n$form--input--disabled:\t\t\t\t\tpalette(silver, default)\t\t!default;\n$form--input-icon:\t\t\t\t\t\tpalette(silver, medium)\t\t\t!default;\n$form--input-icon-right:\t\t\t\tpalette(gray, light)\t\t\t!default;\n$form--description-color:\t\t\t\tpalette(gray, light)\t\t\t!default;\n$form--input-error-color:               $error                        !default;\n$form--input-icon-color:                palette(gray, light)          !default;\n$form--label-color:                     $fiftyshades                      !default;\n\n// ============================================================\n// Radio & Checkbox (_radio-checkbox.scss)\n$radio-checkbox--background:            #FAFAFA                !default;\n$radio-checkbox--border-color:          palette(gray, lighter) !default;\n$radio-checkbox--color:                 palette(gray)          !default;\n$radio-checkbox--checked-border-color:  $blue                    !default;\n$radio-checkbox--checked-background:    $blue                    !default;\n$radio-checkbox--disabled-border-color: palette(silver)        !default;\n$radio-checkbox--disabled-background:   palette(silver)        !default;\n\n$radio-checkbox--check-color:          $white                    !default;\n$radio-checkbox--disabled-check-color: palette(silver, medium) !default;\n\n$radio-checkbox--checked-hover-background: rgba(23, 168, 227, 0.2)    !default;\n$radio-checkbox--hover-background:   palette(silver)        !default;\n\n// ============================================================\n// Upload (_upload.scss)\n\n// Image container\n$file-upload--image-border-color:       palette(silver, soft)   !default;\n$file-upload--image-mask-background:    palette(gray, lighter)  !default;\n$file-upload--image-preview-background: rgba(0, 0, 0, 0.5)      !default;\n\n// Button: Add\n$file-upload--add-border-color:         palette(gray, lighter)  !default;\n$file-upload--add-background:           transparent               !default;\n$file-upload--add-color:                palette(silver, medium) !default;\n\n// File name\n$file-upload--file-border-color:        palette(gray, lighter)  !default;\n$file-upload--file-background:          $white                    !default;\n$file-upload--file-color:               palette(gray, dark)     !default;\n$file-upload--file-hover-background:    #FAFAFA                 !default;\n\n// ============================================================\n// Color Pickers (_colorpickers.scss)\n\n// Value\n$colorpicker--border-color:                    palette(gray, lighter) !default;\n$colorpicker--background:                      #FAFAFA                !default;\n$colorpicker--color:                           palette(gray, dark)    !default;\n\n// Preview\n$colorpicker--preview-background:              palette(gray, lighter) !default;\n\n// Iris\n$colorpicker--iris-border-color:               palette(gray, lighter) !default;\n$colorpicker--iris-background:                 $white                   !default;\n$colorpicker--iris-palette-border-color:       palette(silver, soft)  !default;\n$colorpicker--iris-square-handle-border-color: $white                   !default;\n$colorpicker--iris-slider-handle-border-color: $white                   !default;\n\n// ============================================================\n// Tabs Colors (_tabs.scss)\n$tabs-label-color:                      palette(gray, light)   !default;\n$tabs-label-checked-color:              palette(gray, light)   !default;\n$tabs-label-active-color:               palette(gray, dark)    !default;\n$tabs-label-active-border-color:        palette(gray, dark)    !default;\n$tabs-content-border-color:             palette(silver, soft)  !default;\n$side-tabs--label-color:                palette(gray, default) !default;\n$side-tabs--label-background:           palette(silver, light) !default;\n$side-tabs--label-active-color:         palette(blue, default) !default;\n$side-tabs--label-active-background:    palette(blue, light)   !default;\n\n// Modals (_modals.scss)\n$modal-overlay-bg-color:                rgba(51, 51, 51, 0.95)        !default;\n$modal-box-shadow-color:                rgba(0, 0, 0, 0.2)            !default;\n$modal-close-color:                     palette(silver, medium)       !default;\n$modal-close-action-color:              palette(gray, light)          !default;\n\n// Dropdowns (_dropdowns.scss)\n$dropdown-anchor-color:                 palette(gray, light)          !default;\n$dropdown-ul-border-color:              palette(gray, lighter)        !default;\n$dropdown-label-border-color:           palette(silver, soft)         !default;\n$dropdown-ul-before-border-color:       palette(silver, soft)         !default;\n\n// Scores (_scores.scss)\n$circle-score-success-color:            $success                      !default;\n$circle-score-warning-color:            $warning                      !default;\n$circle-score-error-color:              $error                        !default;\n$circle-score-disabled-color:           palette(silver, medium)       !default;\n$circle-score-bg-color:                 $silver                       !default;\n$circle-score-default-dial-color:       $success                      !default;\n\n// Footer (_footer.scss)\n$footer-color:                          palette(silver, medium)       !default;\n$footer-color-hover:                    palette(gray, default)        !default;\n$footer-cross-sell-border-color:        palette(silver, soft)         !default;\n$footer-cross-sell-icon-color:          palette(gray, light)          !default;\n$footer-cross-sell-p-color:             palette(gray, light)          !default;\n\n// ============================================================\n// Progress Bars (_progress-bars.scss)\n$progress-block--background:   $white;\n$progress-block--border-color: palette(silver, soft);\n\n$progress-text--color:         palette(gray, light);\n$progress-status--color:       palette(gray, light);\n\n$progress-bar--background:     palette(silver, soft);\n$progress-bar--loading-color:  $blue;\n\n// ============================================================\n// Tables (_tables.scss)\n$table--border-color:                   palette(silver, soft)       !default;\n$table--th-color:                       $headings-color               !default;\n$table--text-color:                     $font-color                   !default;\n$table--field-list-title-color:         $headings-color               !default;\n$table--title-color:                    $headings-color               !default;\n\n// ============================================================\n// Accordions (_accordions.scss)\n$accordion--content-bg-color:           palette(silver,light)         !default;\n$accordion--open-indicator-color:       palette(gray,light)           !default;\n$accordion--disabled-color:             palette(silver, medium)       !default;\n$accordion--disabled-icon:              palette(gray, lighter)        !default;\n\n$accordion--block-background:           $white                          !default;\n$accordion--block-shadow:               palette(silver, soft)         !default;\n$accordion--block-color:                palette(gray, light)          !default;\n\n// ============================================================\n// Icons (_icons.scss)\n$icon-color:                            palette(gray,light)           !default;\n$icon-lighter-color:                            palette(gray,lighter)           !default;\n\n// Lists (_list.scss)\n$list-detail-color:                     palette(gray,light)           !default;\n\n// Pagination (_pagination.scss)\n$pagination-border:                     palette(silver, soft)         !default;\n$pagination-background:                 palette(mono, white)          !default;\n$pagination-item-color-static:          palette(gray, light)          !default;\n$pagination-item-color-hover:           palette(blue, default)        !default;\n$pagination-item-color-active:          palette(gray, dark)           !default;\n$pagination-item-color-disabled:        palette(gray, lighter)        !default;\n$pagination-item-bg-active:             palette(silver, light)        !default;\n$pagination-filter-border:              palette(silver, soft)         !default;\n$pagination-results:                    palette(gray, light)          !default;\n\n// ACE Editor (_ace-editor.scss)\n$ace-selector-background:       palette(gray, default) !default;\n$ace-selector-background-hover: palette(gray, dark)    !default;\n$ace-selector-color:            $white                   !default;\n\n// Recipient (_recipient.scss)\n$recipient-border-color:       palette(silver, soft) !default;\n\n// Color Accessibility (_color-accessibility.scss)\n$accessible-light:     $white;\n$accessible-dark:      $black;\n$accessible-dark-alt:  #555555;", "/* ****************************************************************************\n * MODULE: VARIABLES\n */\n\n$font--path: \"../fonts\" !default;\n$img--path: \"../images\" !default;\n\n$sui-font-path: '~@wpmudev/shared-ui/dist/fonts/';\n\n$summary-image: '#{$img--path}/smush-graphic-dashboard-summary.svg';\n\n// Promo banners for free footer\n$cross-sell-1:       'hummingbird';\n$cross-sell-2:       'defender';\n$cross-sell-3:       'smartcrawl';\n\n$main-color: #17A8E3;\n$text-color: #333;\n$border-radius: 4px;\n\n$upgrade-image: '../../app/assets/images/<EMAIL>';\n$upgrade-image-mobile: '../../app/assets/images/hero.png';", "%sui-icons {\n\tfont-family: 'wpmudev-plugin-icons' !important; // Use !important to prevent issues with browser extensions that change fonts.\n\tspeak: none;\n\tfont-size: 1em;\n\tfont-style: normal;\n\tfont-weight: normal;\n\tfont-variant: normal;\n\ttext-transform: none;\n\tline-height: 1;\n\ttext-rendering: auto;\n\tdisplay: inline-block;\n\tcolor: $icon-color;\n\topacity: 1;\n\tdirection: ltr;\n\n\t// Better Font Rendering\n\t-webkit-font-smoothing: antialiased;\n\t-moz-osx-font-smoothing: grayscale;\n\t@media all and (-ms-high-contrast: none) {\n\t\tcolor: $icon-color;\n\t}\n\t@media all and (-ms-high-contrast: active) {\n\t\tcolor: $icon-lighter-color;\n\t}\n}\n\n@mixin icon($position: before, $icon: false, $styles: true) {\n\t@if $position == both {\n\t\t$position: 'before, &:after';\n\t}\n\t// Either a :before or :after pseudo-element, or both, defaulting to :before.\n\t&:#{$position} {\n\t\t@if $icon {\n\t\t\t// A particular icon has been specified.\n\t\t\tcontent: \"#{map-get($icons, $icon)}\";\n\t\t}\n\t\t@if $styles {\n\t\t\t@extend %sui-icons;\n\t\t}\n\t\t// Include any extra rules supplied for the pseudo-element.\n\t\t@content;\n\t}\n}\n\n// Map icon names to font unicode characters.\n$icons: (\n\t\tcheck: \"\\28\",\n\t\tclose: \"\\29\",\n\t\tcheck-tick: \"\\5f\",\n\t\tcross-close: \"\\2b\",\n\t\tplus-circle: \"\\40\",\n\t\twarning-alert: \"\\21\",\n\t\tinfo: \"\\49\",\n\t\tquestion: \"\\3f\",\n\t\ttrash: \"\\51\",\n\t\tpencil: \"\\2f\",\n\t\tunlock: \"\\30\",\n\t\tlock: \"\\39\",\n\t\tkey: \"\\25ca\",\n\t\tplus: \"\\3d\",\n\t\tmagnifying-glass-search: \"\\ba\",\n\t\tmore: \"\\2026\",\n\t\twrench-tool: \"\\2044\",\n\t\twidget-settings-config: \"\\78\",\n\t\tsettings-slider-control: \"\\153\",\n\t\tloader: \"\\4e\",\n\t\tcalendar: \"\\220f\",\n\t\tclock: \"\\2c\",\n\t\tspeed-optimize: \"\\f8\",\n\t\tstopwatch: \"\\56\",\n\t\tfilter: \"\\7a\",\n\t\tbookmark: \"\\221a\",\n\t\tchevron-up: \"\\2dd\",\n\t\tchevron-right: \"\\2dc\",\n\t\tchevron-down: \"\\131\",\n\t\tchevron-left: \"\\d3\",\n\t\tarrow-up: \"\\d4\",\n\t\tarrow-right: \"\\af\",\n\t\tarrow-down: \"\\c2\",\n\t\tarrow-left: \"\\f8ff\",\n\t\tarrow-skip-back: \"\\7b\",\n\t\tarrow-skip-forward: \"\\7d\",\n\t\tarrow-skip-start: \"\\3a\",\n\t\tarrow-skip-end: \"\\22\",\n\t\tplay: \"\\b4\",\n\t\tpause: \"\\2020\",\n\t\tarrows-out: \"\\2da\",\n\t\tarrows-in: \"\\2264\",\n\t\tarrows-expand: \"\\ac\",\n\t\tarrows-compress: \"\\2265\",\n\t\trefresh2: \"\\c1\",\n\t\tzip: \"\\61\",\n\t\tcombine: \"\\6f\",\n\t\tdefer: \"\\70\",\n\t\tinlinecss: \"\\63\",\n\t\tmovefooter: \"\\75\",\n\t\tpopup: \"\\31\",\n\t\tslide-in: \"\\32\",\n\t\tembed: \"\\33\",\n\t\tlist: \"\\60\",\n\t\tlayout-grid: \"\\221e\",\n\t\tlayout: \"\\a9\",\n\t\tthumbnails: \"\\47\",\n\t\tdrag: \"\\201e\",\n\t\talign-left: \"\\25\",\n\t\talign-center: \"\\5e\",\n\t\talign-right: \"\\26\",\n\t\talign-justify: \"\\23\",\n\t\tindent-more: \"\\2019\",\n\t\tindent-less: \"\\201d\",\n\t\tblog: \"\\59\",\n\t\tlist-number: \"\\37\",\n\t\tlist-bullet: \"\\38\",\n\t\tbold: \"\\42\",\n\t\tquote-2: \"\\27\",\n\t\tstyle-type: \"\\3c\",\n\t\twand-magic: \"\\5a\",\n\t\tlink: \"\\35\",\n\t\tunlink: \"\\36\",\n\t\tpaperclip: \"\\41\",\n\t\tcode: \"\\3b\",\n\t\tcolor-pick-eyedropper: \"\\a5\",\n\t\tcrop: \"\\43\",\n\t\tpaint-bucket: \"\\222b\",\n\t\tcamera: \"\\d8\",\n\t\tphoto-picture: \"\\44\",\n\t\tanimation-video: \"\\46\",\n\t\thome: \"\\4a\",\n\t\tmail: \"\\6d\",\n\t\tsend: \"\\201c\",\n\t\tphone: \"\\3e\",\n\t\tpin: \"\\152\",\n\t\tpost-pin: \"\\2c7\",\n\t\tcalculator: \"\\a8\",\n\t\telement-checkbox: \"\\c5\",\n\t\telement-radio: \"\\cd\",\n\t\telement-select: \"\\cf\",\n\t\telement-number: \"\\da\",\n\t\trecaptcha: \"\\2013\",\n\t\tfolder: \"\\2d8\",\n\t\tfolder-open: \"\\bb\",\n\t\tarchive: \"\\62\",\n\t\tbook: \"\\2206\",\n\t\tpage-multiple: \"\\e7\",\n\t\tlayers: \"\\e6\",\n\t\tcopy: \"\\34\",\n\t\tgraph-bar: \"\\c7\",\n\t\tgraph-bar-2: \"\\2db\",\n\t\tgraph-line: \"\\a1\",\n\t\ttracking-disabled: \"\\20ac\",\n\t\tpage: \"\\d2\",\n\t\tclipboard-notes: \"\\bf\",\n\t\tpage-pdf: \"\\c6\",\n\t\theart: \"\\4b\",\n\t\tstar: \"\\53\",\n\t\tplugin-2: \"\\4f\",\n\t\tbrush: \"\\7e\",\n\t\tplug-connected: \"\\52\",\n\t\tplug-disconnected: \"\\2e\",\n\t\tpower-on-off: \"\\5b\",\n\t\ttarget: \"\\2260\",\n\t\tupload-cloud: \"\\a2\",\n\t\tdownload-cloud: \"\\a3\",\n\t\tcloud: \"\\2122\",\n\t\tcloud-migration: \"\\6c\",\n\t\tunpublish: \"\\2c6\",\n\t\tdownload: \"\\58\",\n\t\tsave: \"\\df\",\n\t\tlightbulb: \"\\4c\",\n\t\tsitemap: \"\\b8\",\n\t\tstorage-server-data: \"\\ce\",\n\t\tuser-reputation-points: \"\\45\",\n\t\ttablet-portrait: \"\\5d\",\n\t\tlaptop: \"\\ab\",\n\t\tmonitor: \"\\24\",\n\t\teye: \"\\65\",\n\t\teye-hide: \"\\71\",\n\t\tupdate: \"\\ae\",\n\t\trefresh: \"\\48\",\n\t\tundo: \"\\2030\",\n\t\tweb-globe-world: \"\\57\",\n\t\tflag: \"\\7c\",\n\t\tacademy: \"\\3c0\",\n\t\tprofile-male: \"\\b5\",\n\t\tcommunity-people: \"\\2018\",\n\t\thelp-support: \"\\4d\",\n\t\tgdpr: \"\\2211\",\n\t\tlike: \"\\6a\",\n\t\tdislike: \"\\6b\",\n\t\tfinger-point: \"\\2248\",\n\t\topen-new-window: \"\\6e\",\n\t\treply: \"\\72\",\n\t\tshare: \"\\73\",\n\t\twordpress: \"\\77\",\n\t\tsocial-facebook: \"\\66\",\n\t\tsocial-twitter: \"\\74\",\n\t\tsocial-linkedin: \"\\69\",\n\t\tsocial-dropbox: \"\\64\",\n\t\tsocial-drive: \"\\76\",\n\t\tsocial-google-plus: \"\\67\",\n\t\tsocial-youtube: \"\\79\",\n\t\tinstagram: \"\\2d\",\n\t\tcloudflare: \"\\d0\",\n\t\tsocial-github: \"\\68\",\n\t\twpmudev-logo: \"\\2039\",\n\t\tdefender: \"\\b7\",\n\t\tsmush: \"\\2021\",\n\t\tupfront: \"\\201a\",\n\t\thummingbird: \"\\b0\",\n\t\tuptime: \"\\b1\",\n\t\tsmart-crawl: \"\\2202\",\n\t\tforminator: \"\\50\",\n\t\tshipper-anchor: \"\\54\",\n\t\tsnapshot: \"\\fb01\",\n\t\thustle: \"\\2014\",\n\t\tperformance: \"\\55\",\n\t\thub: \"\\fb02\",\n\t\tautomate: \"\\2d9\",\n\t\talign-y-center: \"\\2a\",\n\t\talign-y-top: \"\\a7\",\n\t\talign-y-bottom: \"\\b6\",\n\t\talign-x-center: \"\\192\",\n\t\talign-x-right: \"\\2022\",\n\t\talign-x-left: \"\\e5\",\n\t\tlogout: \"\\203a\",\n\t\tbranda: \"\\aa\",\n\t\tbeehive: \"\\3a9\",\n\t\tasync: \"\\2909\",\n\t\tsite-health: \"\\2764\",\n\t\timport-export: \"\\296e\"\n);\n\n\n@font-face {\n\tfont-family: 'wpmudev-plugin-icons';\n\tsrc: url('#{$sui-font-path}wpmudev-plugin-icons.eot?#{$sui-version}');\n\tsrc: url('#{$sui-font-path}wpmudev-plugin-icons.eot?#{$sui-version}') format('embedded-opentype'),\n\turl('#{$sui-font-path}wpmudev-plugin-icons.ttf?#{$sui-version}') format('truetype'),\n\turl('#{$sui-font-path}wpmudev-plugin-icons.woff?#{$sui-version}') format('woff'),\n\turl('#{$sui-font-path}wpmudev-plugin-icons.woff2?#{$sui-version}') format('woff2'),\n\turl('#{$sui-font-path}wpmudev-plugin-icons.svg?#{$sui-version}') format('svg');\n\tfont-weight: normal;\n\tfont-style: normal;\n}\n\n@include body-class(false) {\n\n\t.sui-loading:before {\n\t\tanimation: spin 1.3s linear infinite;\n\t}\n\n\t// Set the required styles on all icons.\n\t[class*=\"sui-icon-\"] {\n\t\tdisplay: inline-block;\n\t\t@include icon(both);\n\t}\n\n\t// Icon sizes.\n\t[class*=\"sui-icon-\"].sui-xl {\n\t\t&:before {\n\t\t\tfont-size: 30px;\n\t\t}\n\t}\n\n\t[class*=\"sui-icon-\"].sui-lg {\n\t\t&:before {\n\t\t\tfont-size: 20px;\n\t\t}\n\t}\n\n\t[class*=\"sui-icon-\"].sui-md {\n\t\t&:before {\n\t\t\tfont-size: 16px;\n\t\t}\n\t}\n\n\t[class*=\"sui-icon-\"].sui-sm {\n\t\t&:before {\n\t\t\tfont-size: 12px;\n\t\t}\n\t}\n\n\t// Colors.\n\t[class*=\"sui-icon-\"].sui-success {\n\t\t&:before {\n\t\t\tcolor: inherit;\n\t\t}\n\t}\n\n\t[class*=\"sui-icon-\"].sui-error {\n\t\t&:before {\n\t\t\tcolor: inherit;\n\t\t}\n\t}\n\n\t[class*=\"sui-icon-\"].sui-warning {\n\t\t&:before {\n\t\t\tcolor: inherit;\n\t\t}\n\t}\n\n\t[class*=\"sui-icon-\"].sui-info {\n\t\t&:before {\n\t\t\tcolor: inherit;\n\t\t}\n\t}\n\n\t// Fixed width icons.\n\t[class*=\"sui-icon-\"].sui-fw {\n\t\twidth: 1.8em;\n\t\ttext-align: center;\n\t\tmin-height: 1em;\n\t}\n\n\t// Setup a class name for each icon.\n\t@each $name, $char in $icons {\n\t\t.sui-icon-#{$name}:before {\n\t\t\tcontent: $char;\n\t\t}\n\t}\n}\n", "@include body-class(true) {\n\n\t.sui-button,\n\ta.sui-button,\n\tbutton.sui-button {\n\n\t\t&, &-icon {\n\t\t\tcursor: pointer;\n\t\t\tdisplay: inline-block;\n\t\t\tposition: relative;\n\t\t\tmargin: 0;\n\t\t\tborder-width: 2px;\n\t\t\tborder-style: solid;\n\t\t\tborder-color: transparent;\n\t\t\tborder-radius: $border-radius;\n\t\t\ttext-decoration: none;\n\t\t\ttext-align: center;\n\t\t\ttransition: $transition;\n\n\t\t\t.sui-loading {\n\t\t\t\twidth: 16px;\n\t\t\t\theight: 16px;\n\t\t\t\tdisplay: none;\n\t\t\t\tflex-direction: column;\n\t\t\t\talign-items: center;\n\t\t\t\tjustify-content: center;\n\t\t\t\tposition: absolute;\n\t\t\t\tmargin: 0;\n\n\t\t\t\t&:before {\n\t\t\t\t\tdisplay: block;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t.sui-loading-text,\n\t\t\t.sui-button-text-default {\n\t\t\t\tdisplay: block;\n\t\t\t\tpointer-events: none;\n\t\t\t\t@include media(max-width, md) {\n\t\t\t\t\t.sui-loading-text {\n\t\t\t\t\t\toverflow: hidden;\n\t\t\t\t\t\ttext-overflow: ellipsis;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t.sui-button-text-onload {\n\t\t\t\tdisplay: none;\n\t\t\t}\n\n\t\t\t[class*=\"sui-icon-\"] {\n\t\t\t\tfont-size: 12px;\n\n\t\t\t\t&:before {\n\t\t\t\t\tdisplay: block;\n\t\t\t\t\tcolor: inherit;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t&:hover {\n\t\t\t\toutline: none;\n\t\t\t\tbox-shadow: none;\n\t\t\t}\n\n\t\t\t&:focus {\n\t\t\t\toutline: none;\n\t\t\t}\n\n\t\t\t&.disabled,\n\t\t\t&:disabled,\n\t\t\t&[disabled],\n\t\t\t&.sui-button-onload,\n\t\t\t&.sui-button-onload-text {\n\t\t\t\tcursor: default;\n\t\t\t\tpointer-events: none;\n\t\t\t}\n\n\t\t\t&.sui-button-onload {\n\t\t\t\tpointer-events: none;\n\t\t\t\tdisplay: inline-flex;\n\t\t\t\tflex-direction: column;\n\t\t\t\talign-items: center;\n\t\t\t\tjustify-content: center;\n\n\t\t\t\t.sui-loading {\n\t\t\t\t\tdisplay: flex;\n\t\t\t\t}\n\n\t\t\t\t.sui-loading-text {\n\t\t\t\t\topacity: 0;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t&.sui-button-onload-text {\n\t\t\t\tpointer-events: none;\n\n\t\t\t\t.sui-button-text-default {\n\t\t\t\t\tdisplay: none;\n\t\t\t\t}\n\n\t\t\t\t.sui-button-text-onload {\n\t\t\t\t\tdisplay: block;\n\n\t\t\t\t\t.sui-loading {\n\t\t\t\t\t\twidth: auto;\n\t\t\t\t\t\theight: auto;\n\t\t\t\t\t\tdisplay: inline-block;\n\t\t\t\t\t\tposition: relative;\n\t\t\t\t\t\tvertical-align: unset;\n\t\t\t\t\t\tmargin-right: 4px;\n\n\t\t\t\t\t\t&:before {\n\t\t\t\t\t\t\tdisplay: inline-block;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t&:not(:last-child) {\n\t\t\t\tmargin-right: 10px;\n\t\t\t}\n\t\t}\n\t}\n\n\t// VARIATION: Buttons with text\n\t.sui-button,\n\ta.sui-button,\n\tbutton.sui-button {\n\t\twidth: auto;\n\t\tmin-width: 80px;\n\t\tpadding: 5px 14px;\n\t\tbackground-color: map-get($button-background, default);\n\t\tcolor: map-get($button-text-color, default);\n\t\tfont: 500 12px/16px $font;\n\t\tletter-spacing: $font--letter-spacing;\n\t\ttext-transform: uppercase;\n\n\t\t[class*=\"sui-icon-\"] {\n\t\t\tvertical-align: middle;\n\n\t\t\t&:not(.sui-loading) {\n\t\t\t\twidth: 22px;\n\t\t\t\tposition: relative;\n\t\t\t\ttop: -1px;\n\t\t\t\tmargin-left: -7px;\n\t\t\t}\n\t\t}\n\n\t\t&:hover,\n\t\t&:focus {\n\t\t\tbackground-color: darken(map-get($button-background, default), 10%);\n\t\t}\n\n\t\t&:focus {\n\t\t\toutline: none;\n\t\t\tbox-shadow: 0 0 0 2px map-get($button-shadow, default);\n\t\t}\n\n\t\t&.disabled,\n\t\t&:disabled,\n\t\t&[disabled],\n\t\t&.sui-button-onload,\n\t\t&.sui-button-onload-text {\n\t\t\tbackground-color: $button-disabled--background;\n\t\t\tcolor: $button-disabled--color;\n\t\t}\n\n\t\t// VARIATION: Button with right icon\n\t\t&.sui-button-icon-right {\n\n\t\t\t[class*=\"sui-icon-\"] {\n\n\t\t\t\t&:not(.sui-loading) {\n\t\t\t\t\tmargin-right: -7px;\n\t\t\t\t\tmargin-left: 0;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t// VARIATION: Colorize buttons\n\t\t@each $color in $button-colors {\n\n\t\t\t&.sui-button-#{\"\" + $color} {\n\t\t\t\tbackground-color: map-get($button-background, $color);\n\t\t\t\tcolor: map-get($button-text-color, $color);\n\n\t\t\t\t&:hover,\n\t\t\t\t&:focus {\n\t\t\t\t\tbackground-color: darken(map-get($button-background, $color), 10%);\n\t\t\t\t}\n\n\t\t\t\t&:focus {\n\t\t\t\t\toutline: none;\n\t\t\t\t\tbox-shadow: 0 0 0 2px map-get($button-shadow, $color);\n\t\t\t\t}\n\n\t\t\t\t&.disabled,\n\t\t\t\t&:disabled,\n\t\t\t\t&[disabled],\n\t\t\t\t&.sui-button-onload,\n\t\t\t\t&.sui-button-onload-text {\n\t\t\t\t\tbackground-color: $button-disabled--background;\n\t\t\t\t\tcolor: $button-disabled--color;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t// VARIATION: Upsell button\n\t\t&.sui-button-upsell {\n\t\t\twidth: auto;\n\t\t\theight: 26px;\n\t\t\tpadding: 4px 14px;\n\t\t\tborder-color: $button-upsell--border-static;\n\t\t\tborder-radius: 14px;\n\t\t\tbackground-color: transparent;\n\t\t\tcolor: $button-upsell--color-static;\n\t\t\tline-height: 14px;\n\t\t\ttext-transform: none;\n\n\t\t\t&:hover,\n\t\t\t&:focus {\n\t\t\t\topacity: 1;\n\t\t\t\tborder-color: $button-upsell--border-active;\n\t\t\t\tbackground-color: $button-upsell--border-active;\n\t\t\t\tcolor: $button-upsell--color-active;\n\t\t\t}\n\n\t\t\t&:focus {\n\t\t\t\toutline: none;\n\t\t\t\tbox-shadow: 0 0 0 2px $purple-ghost;\n\t\t\t}\n\t\t}\n\n\t\t// VARIATION: Dashed button\n\t\t&.sui-button-dashed {\n\t\t\twidth: 100%;\n\t\t\tdisplay: flex;\n\t\t\talign-items: center;\n\t\t\tjustify-content: center;\n\t\t\tborder-width: 1px;\n\t\t\tborder-style: dashed;\n\t\t\tborder-color: $button-dashed--border;\n\t\t\tbackground-color: $button-dashed--background;\n\t\t\tcolor: $button-dashed--color;\n\t\t\tline-height: 18px;\n\n\t\t\t&.sui-lg {\n\t\t\t\theight: $button-dashed-height-lg;\n\n\t\t\t\t@include media(max-width, md) {\n\t\t\t\t\theight: $button-dashed-height-md;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t@include media(max-width, md) {\n\t\t\t\theight: $button-dashed-height-md;\n\t\t\t}\n\n\t\t\t@include media(min-width, md) {\n\t\t\t\theight: $button-dashed-height;\n\t\t\t}\n\t\t}\n\n\t\t// VARIATION: Ghost button\n\t\t&.sui-button-ghost {\n\t\t\tborder-color: map-get($button-border, default);\n\t\t\tbackground-color: transparent;\n\t\t\tcolor: map-get($button-background, default);\n\n\t\t\t&:hover,\n\t\t\t&:focus {\n\t\t\t\tborder-color: map-get($button-background, default);\n\t\t\t\tbackground-color: map-get($button-background, default);\n\t\t\t\tcolor: map-get($button-text-color, default);\n\t\t\t}\n\n\t\t\t&:focus {\n\t\t\t\toutline: none;\n\t\t\t\tbox-shadow: 0 0 0 2px map-get($button-shadow, default);\n\t\t\t}\n\n\t\t\t&.disabled,\n\t\t\t&:disabled,\n\t\t\t&[disabled],\n\t\t\t&.sui-button-onload,\n\t\t\t&.sui-button-onload-text {\n\t\t\t\tborder-color: $button-disabled--background;\n\t\t\t\tbackground-color: $button-disabled--background;\n\t\t\t\tcolor: $button-disabled--color;\n\t\t\t}\n\n\t\t\t@each $color in $button-colors {\n\n\t\t\t\t&.sui-button-#{\"\" + $color} {\n\t\t\t\t\tborder-color: map-get($button-border, $color);\n\t\t\t\t\tcolor: map-get($button-background, $color);\n\n\t\t\t\t\t&:hover,\n\t\t\t\t\t&:focus {\n\t\t\t\t\t\tborder-color: map-get($button-background, $color);\n\t\t\t\t\t\tbackground-color: map-get($button-background, $color);\n\t\t\t\t\t\tcolor: map-get($button-text-color, $color);\n\t\t\t\t\t}\n\n\t\t\t\t\t&:focus {\n\t\t\t\t\t\toutline: none;\n\t\t\t\t\t\tbox-shadow: 0 0 0 2px map-get($button-shadow, $color);\n\t\t\t\t\t}\n\n\t\t\t\t\t&.disabled,\n\t\t\t\t\t&:disabled,\n\t\t\t\t\t&[disabled],\n\t\t\t\t\t&.sui-button-onload {\n\t\t\t\t\t\tborder-color: $button-disabled--background;\n\t\t\t\t\t\tbackground-color: $button-disabled--background;\n\t\t\t\t\t\tcolor: $button-disabled--color;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t// VARIATION: Compound button\n\t\t&.sui-button-compound {\n\n\t\t\t.sui-compound-desktop {\n\n\t\t\t\t@include media(max-width, md) {\n\t\t\t\t\tdisplay: none;\n\t\t\t\t}\n\n\t\t\t\t@include media(min-width, md) {\n\t\t\t\t\tdisplay: block;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t.sui-compound-mobile {\n\n\t\t\t\t[class*=\"sui-icon-\"] {\n\n\t\t\t\t\t@include media(max-width, md) {\n\t\t\t\t\t\tmargin-right: 0;\n\t\t\t\t\t\tmargin-left: 0;\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t@include media(max-width, md) {\n\t\t\t\t\tdisplay: block;\n\t\t\t\t}\n\n\t\t\t\t@include media(min-width, md) {\n\t\t\t\t\tdisplay: none;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t@include media(max-width, md) {\n\t\t\t\tmin-width: 44px;\n\t\t\t\tpadding-right: 5px;\n\t\t\t\tpadding-left: 5px;\n\t\t\t}\n\t\t}\n\n\t\t// SIZE: Large\n\t\t&.sui-button-lg {\n\t\t\tpadding: 8px 20px;\n\t\t\tfont-size: 15px;\n\t\t\tline-height: 20px;\n\n\t\t\t[class*=\"sui-icon-\"] {\n\t\t\t\tfont-size: 16px;\n\t\t\t}\n\t\t}\n\t}\n\n\t// VARIATION: Buttons with icons (only)\n\t// This variation it's not related and must not be used with .sui-button class\n\t.sui-button-icon,\n\ta.sui-button-icon,\n\tbutton.sui-button-icon {\n\t\twidth: 30px;\n\t\theight: 30px;\n\t\tdisplay: inline-flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tpadding: 0 4px;\n\t\tbackground-color: transparent;\n\t\tcolor: map-get($button-icon--color, default);\n\t\ttext-align: center;\n\t\twhite-space: nowrap;\n\n\t\t[class*=\"sui-icon-\"] {\n\n\t\t\t&:not(.sui-loading) {\n\t\t\t\tdisplay: block;\n\t\t\t}\n\t\t}\n\n\t\t&:hover,\n\t\t&:focus,\n\t\t&.sui-active {\n\t\t\tbackground-color: map-get($button-icon--background, default);\n\t\t\tcolor: map-get($button-icon--color-hover, default);\n\t\t}\n\n\t\t&:focus {\n\t\t\toutline: none;\n\t\t\tbox-shadow: 0 0 0 2px map-get($button-icon--shadow, default);\n\t\t}\n\n\t\t&.disabled,\n\t\t&:disabled,\n\t\t&[disabled],\n\t\t&.sui-button-onload,\n\t\t&.sui-button-onload-text {\n\t\t\tcolor: $button-disabled--color;\n\t\t\tbackground-color: $button-disabled--background;\n\t\t}\n\n\t\t@each $color in $button-colors {\n\n\t\t\t&.sui-button-#{\"\" + $color} {\n\t\t\t\tcolor: map-get($button-icon--color, $color);\n\n\t\t\t\t&:hover,\n\t\t\t\t&:focus,\n\t\t\t\t&.sui-active {\n\t\t\t\t\tbackground-color: map-get($button-icon--background, $color);\n\t\t\t\t\tcolor: map-get($button-icon--color-hover, $color);\n\t\t\t\t}\n\n\t\t\t\t&:focus {\n\t\t\t\t\toutline: none;\n\t\t\t\t\tbox-shadow: 0 0 0 2px map-get($button-icon--shadow, $color);\n\t\t\t\t}\n\n\t\t\t\t&.disabled,\n\t\t\t\t&:disabled,\n\t\t\t\t&[disabled],\n\t\t\t\t&.sui-button-onload,\n\t\t\t\t&.sui-button-onload-text {\n\t\t\t\t\tcolor: $button-disabled--color;\n\t\t\t\t\tbackground-color: $button-disabled--background;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t// DESIGN: Outlined\n\t\t&.sui-button-outlined {\n\t\t\twidth: 44px;\n\t\t\tborder-color: map-get($button-icon--border, default);\n\n\t\t\t&:hover,\n\t\t\t&:focus,\n\t\t\t&.sui-active {\n\t\t\t\tborder-color: map-get($button-icon--background-hover, default);\n\t\t\t\tbackground-color: map-get($button-icon--background-hover, default);\n\t\t\t\tcolor: map-get($button-icon--alt, default);\n\t\t\t}\n\n\t\t\t&:focus {\n\t\t\t\toutline: none;\n\t\t\t\tbox-shadow: 0 0 0 2px map-get($button-icon--shadow, default);\n\t\t\t}\n\n\t\t\t&.disabled,\n\t\t\t&:disabled,\n\t\t\t&[disabled],\n\t\t\t&.sui-button-onload,\n\t\t\t&.sui-button-onload-text {\n\t\t\t\tborder-color: $button-disabled--background;\n\t\t\t}\n\n\t\t\t@each $color in $button-colors {\n\n\t\t\t\t&.sui-button-#{\"\" + $color} {\n\t\t\t\t\tborder-color: map-get($button-icon--border, $color);\n\n\t\t\t\t\t&:hover,\n\t\t\t\t\t&:focus,\n\t\t\t\t\t&.sui-active {\n\t\t\t\t\t\tborder-color: map-get($button-icon--background-hover, $color);\n\t\t\t\t\t\tbackground-color: map-get($button-icon--background-hover, $color);\n\t\t\t\t\t\tcolor: map-get($button-icon--alt, $color);\n\t\t\t\t\t}\n\n\t\t\t\t\t&:focus {\n\t\t\t\t\t\toutline: none;\n\t\t\t\t\t\tbox-shadow: 0 0 0 2px map-get($button-icon--shadow, $color);\n\t\t\t\t\t}\n\n\t\t\t\t\t&.disabled,\n\t\t\t\t\t&:disabled,\n\t\t\t\t\t&[disabled],\n\t\t\t\t\t&.sui-button-onload,\n\t\t\t\t\t&.sui-button-onload-text {\n\t\t\t\t\t\tborder-color: $button-disabled--background;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t// DESIGN: Filled\n\t\t&.sui-button-filled {\n\t\t\tbackground-color: map-get($button-icon--background-filled, default);\n\t\t\tcolor: map-get($button-icon--color-filled, default);\n\n\t\t\t&:hover,\n\t\t\t&:focus,\n\t\t\t&.sui-active {\n\t\t\t\tbackground-color: darken(map-get($button-icon--background-filled, default), 8%);\n\t\t\t}\n\n\t\t\t&:focus {\n\t\t\t\toutline: none;\n\t\t\t\tbox-shadow: 0 0 0 2px map-get($button-icon--shadow, default);\n\t\t\t}\n\n\t\t\t&.disabled,\n\t\t\t&:disabled,\n\t\t\t&[disabled],\n\t\t\t&.sui-button-onload,\n\t\t\t&.sui-button-onload-text {\n\t\t\t\tbackground-color: $button-disabled--background;\n\t\t\t\tcolor: $button-disabled--color;\n\t\t\t}\n\n\t\t\t@each $color in $button-colors {\n\n\t\t\t\t&.sui-button-#{\"\" + $color} {\n\t\t\t\t\tbackground-color: map-get($button-icon--background-filled, $color);\n\t\t\t\t\tcolor: map-get($button-icon--color-filled, $color);\n\n\t\t\t\t\t&:hover,\n\t\t\t\t\t&:focus,\n\t\t\t\t\t&.sui-active {\n\t\t\t\t\t\tbackground-color: darken(map-get($button-icon--background-filled, $color), 10%);\n\t\t\t\t\t}\n\n\t\t\t\t\t&:focus {\n\t\t\t\t\t\toutline: none;\n\t\t\t\t\t\tbox-shadow: 0 0 0 2px map-get($button-icon--shadow, $color);\n\t\t\t\t\t}\n\n\t\t\t\t\t&.disabled,\n\t\t\t\t\t&:disabled,\n\t\t\t\t\t&[disabled],\n\t\t\t\t\t&.sui-button-onload,\n\t\t\t\t\t&.sui-button-onload-text {\n\t\t\t\t\t\tbackground-color: $button-disabled--background;\n\t\t\t\t\t\tcolor: $button-disabled--color;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t// SIZE: Large\n\t\t&.sui-button-lg {\n\t\t\twidth: 50px;\n\t\t\theight: 40px;\n\n\t\t\t[class*=\"sui-icon-\"] {\n\t\t\t\tfont-size: 13px;\n\t\t\t}\n\t\t}\n\t}\n\n\t// FIX: Dropdown button\n\t// Remove margin when using sui-buttons inside dropdown element\n\t.sui-dropdown {\n\n\t\t.sui-button,\n\t\ta.sui-button,\n\t\tbutton.sui-button {\n\n\t\t\t&, &-icon {\n\n\t\t\t\t&:not(:last-child) {\n\t\t\t\t\tmargin-right: 0;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n}", "@use \"sass:math\";\n\n@include body-class(true) {\n\n\t.sui-toggle {\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\talign-items: flex-start;\n\t\tposition: relative;\n\t\topacity: 1;\n\t\tpointer-events: none;\n\n\t\tinput,\n\t\t.sui-toggle-slider,\n\t\t.sui-toggle-label {\n\t\t\tpointer-events: all;\n\t\t}\n\n\t\t.sui-toggle-slider,\n\t\t.sui-toggle-label {\n\t\t\tcursor: pointer;\n\t\t}\n\n\t\tinput {\n\t\t\t@extend %sui-screen-reader-text;\n\n\t\t\t&:checked {\n\n\t\t\t\t~ .sui-toggle-slider {\n\t\t\t\t\tbackground-color: $blue;\n\n\t\t\t\t\t&:before {\n\t\t\t\t\t\ttransform: translateX(#{$toggle-width - ($toggle-height - 2px) - 2px});\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t&[disabled] {\n\n\t\t\t\t~ .sui-toggle-slider {\n\t\t\t\t\tcursor: not-allowed;\n\t\t\t\t\tbackground-color: $overcast;\n\n\t\t\t\t\t&:before {\n\t\t\t\t\t\tbackground-color: $fiftyshades;\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t~ .sui-description,\n\t\t\t\t~ .sui-toggle-label {\n\t\t\t\t\tcursor: initial;\n\t\t\t\t\tpointer-events: none;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t&:hover {\n\n\t\t\t\t~ .sui-toggle-slider {\n\t\t\t\t\tbox-shadow: 0 0 0 5px $smoke;\n\t\t\t\t}\n\n\t\t\t\t&:checked ~ .sui-toggle-slider {\n\t\t\t\t\tbox-shadow: 0 0 0 5px $blue-ghost;\n\t\t\t\t}\n\n\t\t\t\t&:focus {\n\t\t\t\t\tbox-shadow: 0 0 0 5px $silver;\n\n\t\t\t\t\t&:checked ~ .sui-toggle-slider {\n\t\t\t\t\t\tbox-shadow: 0 0 0 5px #D1EAF4;\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t&[disabled] ~ .sui-toggle-slider {\n\t\t\t\t\tbox-shadow: none;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t&:focus {\n\n\t\t\t\t~ .sui-toggle-slider {\n\t\t\t\t\tbox-shadow: 0 0 0 5px $silver;\n\t\t\t\t}\n\n\t\t\t\t&:checked ~ .sui-toggle-slider {\n\t\t\t\t\tbox-shadow: 0 0 0 5px #D1EAF4;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t@media (forced-colors: active) {\n\t\t\t\t~ .sui-toggle-slider {\n\t\t\t\t\tforced-color-adjust: none;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t@media (forced-colors: active) and (prefers-color-scheme: dark), (-ms-high-contrast: white-on-black) {\n\t\t\t\t&:checked {\n\t\t\t\t\t~ .sui-toggle-slider {\n\t\t\t\t\t\tbackground-color: map-get($button-background, orange) !important;\n\t\t\t\t\t\t&:before {\n\t\t\t\t\t\t\tbackground-color: $cloud;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t// ELEMENT: Slider.\n\t\t.sui-toggle-slider {\n\t\t\twidth: $toggle-width;\n\t\t\theight: $toggle-height;\n\t\t\tposition: absolute;\n\t\t\tleft: 0;\n\t\t\ttop: 0;\n\t\t\tmargin: math.div(($toggle-font-height - $toggle-height), 2) 0;\n\t\t\tpadding: 0;\n\t\t\tborder: 0;\n\t\t\tborder-radius: math.div($toggle-height, 2);\n\t\t\tbackground-color: $fiftyshades;\n\t\t\ttransition: $transition;\n\t\t\topacity: 1;\n\n\t\t\t&:before {\n\t\t\t\tcontent: \" \";\n\t\t\t\twidth: #{$toggle-height - 2px};\n\t\t\t\theight: #{$toggle-height - 2px};\n\t\t\t\tposition: absolute;\n\t\t\t\ttop: 1px;\n\t\t\t\tleft: 1px;\n\t\t\t\tborder-radius: $toggle-height;\n\t\t\t\tbackground-color: $white;\n\t\t\t\ttransition: 0.2s linear;\n\t\t\t}\n\n\t\t\t&:last-child,\n\t\t\t&.sui-toggle-slider--only {\n\t\t\t\tposition: relative;\n\t\t\t}\n\n\t\t\t~ .sui-toggle-label,\n\t\t\t~ .sui-description {\n\t\t\t\tmargin-left: #{$toggle-width + math.div($sui-gutter-md, 2)};\n\t\t\t\tpointer-events: all;\n\t\t\t}\n\t\t}\n\n\t\t// ELEMENT: Label.\n\t\t.sui-toggle-label {\n\t\t\tdisplay: block;\n\t\t\tfont: 500 #{$toggle-font-size}/#{$toggle-font-height} $font;\n\t\t\tletter-spacing: $font--letter-spacing;\n\t\t}\n\n\t\t// Support for old markup.\n\t\t+ label,\n\t\t+ .sui-toggle-label {\n\t\t\tposition: relative;\n\t\t\ttop: -3px;\n\t\t\tmargin-left: math.div($sui-gutter-md, 2);\n\t\t\tfont-weight: 500;\n\t\t}\n\t}\n\n\t.sui-toggle-content {\n\t\topacity: 1;\n\t\t&, &.sui-border-frame {\n\t\t\tmargin-left: #{$toggle-width + math.div($sui-gutter-md, 2)};\n\t\t}\n\t}\n}\n", "@use \"sass:math\";\n\n@include body-class(true) {\n\n\t* {\n\t\tbox-sizing: border-box;\n\t}\n\n\t*:before,\n\t*:after {\n\t\tbox-sizing: border-box;\n\t}\n\n\t// ELEMENT: Box\n\t.sui-box {\n\t\tmin-height: 20px;\n\t\tposition: relative;\n\t\tborder-radius: $border-radius;\n\t\tbackground-color: $box-bg-color;\n\t\tbox-shadow: 0 2px 0 $box-box-shadow-color;\n\n\t\t// BOX: Header\n\t\t.sui-box-header {\n\t\t\tborder-bottom: 1px solid $box-header-border-color;\n\t\t\tpadding: 15px 30px;\n\t\t\tdisplay: flex;\n\t\t\talign-items: center;\n\n\t\t\t@include media( max-width, md ) {\n\t\t\t\tpadding: 15px $sui-gutter-md;\n\t\t\t}\n\n\t\t\t& > h3 > [class*=\"sui-icon-\"] {\n\t\t\t\tline-height: 30px;\n\t\t\t}\n\t\t}\n\n\t\t// BOX: Body\n\t\t.sui-box-body {\n\n\t\t\t@include media(max-width, md) {\n\t\t\t\tpadding: $sui-gutter-md;\n\t\t\t}\n\n\t\t\t@include media(min-width, md) {\n\t\t\t\tpadding: $sui-gutter;\n\t\t\t}\n\t\t}\n\n\t\t// BOX: Footer\n\t\t.sui-box-footer {\n\t\t\tborder-top: 1px solid $box-footer-border-color;\n\t\t\tpadding: 30px;\n\t\t\tdisplay: flex;\n\t\t\talign-items: center;\n\t\t\t&.sui-pull-up {\n\t\t\t\tmargin-top: -15px;\n\t\t\t}\n\n\t\t\t@include media( max-width, md ) {\n\t\t\t\tpadding: $sui-gutter-md;\n\t\t\t}\n\t\t}\n\n\t\t// BOX: Status Bar\n\t\t.sui-box-status {\n\t\t\tpadding: 0 $sui-gutter-md $sui-gutter-md;\n\n\t\t\t.sui-status {\n\t\t\t\tdisplay: flex;\n\n\t\t\t\t[class*=\"sui-status-\"] {\n\t\t\t\t\tfont-size: 12px;\n\t\t\t\t\tline-height: 30px;\n\t\t\t\t\tfont-family: $font;\n\t\t\t\t\tletter-spacing: $font--letter-spacing;\n\n\t\t\t\t\t.sui-tag {\n\t\t\t\t\t\tmargin-left: 8px;\n\t\t\t\t\t}\n\n\t\t\t\t\t[class*=\"sui-icon-\"] {\n\t\t\t\t\t\tmargin-right: 4px;\n\t\t\t\t\t\tfont-size: 12px;\n\n\t\t\t\t\t\t&:before {\n\t\t\t\t\t\t\tdisplay: block;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\n\t\t\t\t\t&:not(:last-child) {\n\t\t\t\t\t\tmargin-right: 20px;\n\t\t\t\t\t}\n\n\t\t\t\t\t&:not(:first-child) {\n\t\t\t\t\t\tpadding-left: 20px;\n\t\t\t\t\t\tborder-left: 1px solid palette(silver, soft);\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t.sui-status-module {\n\t\t\t\t\tcolor: palette(gray, light);\n\t\t\t\t\tfont-weight: bold;\n\t\t\t\t}\n\n\t\t\t\t.sui-status-changes {\n\t\t\t\t\tcolor: palette(silver, medium);\n\t\t\t\t\tfont-weight: 500;\n\t\t\t\t\tletter-spacing: $font--letter-spacing;\n\n\t\t\t\t\t.sui-icon-check-tick:before {\n\t\t\t\t\t\tcolor: palette(green, default);\n\t\t\t\t\t}\n\n\t\t\t\t\t.sui-icon-update:before {\n\t\t\t\t\t\tcolor: palette(gray, light);\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t+ .sui-actions {\n\n\t\t\t\t\t@include media(max-width, md) {\n\t\t\t\t\t\tdisplay: inline-block;\n\t\t\t\t\t\tmargin-top: math.div($sui-gutter, 2);\n\t\t\t\t\t}\n\n\t\t\t\t\t@include media(min-width, md) {\n\t\t\t\t\t\tmargin-left: math.div($sui-gutter, 2);\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t@include media(min-width, md) {\n\t\t\t\t\tflex: 1;\n\t\t\t\t}\n\n\t\t\t\t@include media(max-width, md) {\n\t\t\t\t\tdisplay: inline-flex;\n\t\t\t\t\tmargin-right: math.div($sui-gutter, 2);\n\t\t\t\t\tmargin-top: math.div($sui-gutter, 2);\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t.sui-actions {\n\n\t\t\t\t@include media(min-width, md) {\n\t\t\t\t\tflex: 0 0 auto;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t@include media(min-width, md) {\n\t\t\t\tdisplay: flex;\n\t\t\t\tpadding: math.div($sui-gutter, 2) $sui-gutter;\n\t\t\t}\n\t\t}\n\n\t\t// BOX: Search Bar\n\t\t.sui-box-search {\n\n\t\t\t> * {\n\t\t\t\tmax-width: 100%;\n\t\t\t\tflex: 0 1 auto;\n\t\t\t}\n\n\t\t\t> .sui-search-left {\n\n\t\t\t\t&:not(:last-child) {\n\n\t\t\t\t\t@include media(min-width, md) {\n\t\t\t\t\t\tmargin-right: math.div($sui-gutter, 2);\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t@include media(min-width, md) {\n\t\t\t\t\tdisplay: flex;\n\t\t\t\t\tflex: 1;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t> .sui-search-right {\n\n\t\t\t\t&:only-child {\n\n\t\t\t\t\t@include media(min-width, md) {\n\t\t\t\t\t\tflex: 1;\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t&:not(:only-child) {\n\n\t\t\t\t\t&:not(:last-child) {\n\n\t\t\t\t\t\t@include media(min-width, md) {\n\t\t\t\t\t\t\tmargin-right: math.div($sui-gutter, 2);\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\n\t\t\t\t\t@include media(min-width, md) {\n\t\t\t\t\t\tflex: 0 0 auto;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t@include media(min-width, md) {\n\t\t\t\tdisplay: flex;\n\t\t\t}\n\t\t}\n\n\t\t// BOX: Sticky box\n\t\t&.sui-box-sticky {\n\n\t\t\t@include media(min-width, md) {\n\t\t\t\tposition: sticky !important;\n\t\t\t\ttop: 32px;\n\t\t\t\tz-index: 12;\n\t\t\t}\n          &.sui-is-sticky {\n              box-shadow: 0 5px 25px rgba(0, 0, 0, .15);\n          }\n\t\t}\n\n\t\t> .sui-box-search {\n\t\t\tpadding-top: math.div($sui-gutter, 2);\n\t\t\tpadding-bottom: math.div($sui-gutter, 2);\n\n\t\t\t@include media(max-width, md) {\n\t\t\t\tpadding-right: $sui-gutter-md;\n\t\t\t\tpadding-left: $sui-gutter-md;\n\t\t\t}\n\n\t\t\t@include media(min-width, md) {\n\t\t\t\tpadding-right: $sui-gutter;\n\t\t\t\tpadding-left: $sui-gutter;\n\t\t\t}\n\t\t}\n\n\t\t&:last-child {\n\n\t\t\t@include media(max-width, md) {\n\t\t\t\tmargin-bottom: 0;\n\t\t\t}\n\n\t\t\t@include media(min-width, md) {\n\t\t\t\tmargin-bottom: 0;\n\t\t\t}\n\t\t}\n\n\t\t@include media(max-width, md) {\n\t\t\tmargin-bottom: $sui-gutter-md;\n\t\t}\n\n\t\t@include media(min-width, md) {\n\t\t\tmargin-bottom: $sui-gutter;\n\t\t}\n\t}\n\n\t// ELEMENT: Box Message\n\t// Commonly used for \"empty message\" or text with image on top.\n\t// To use with sui-box preferrably.\n\t.sui-message {\n\t\tdisplay: block;\n\t\ttext-align: center;\n\n\t\t.sui-message-content {\n\t\t\tmax-width: 600px;\n\t\t\tmargin-right: auto;\n\t\t\tmargin-left: auto;\n\n\t\t\th1, h2, h3, h4, h5, h6 {\n\t\t\t\tmargin: 0 0 $sui-gutter-md;\n\n\t\t\t\t&:last-child {\n\t\t\t\t\tmargin-bottom: 0;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tp {\n\t\t\t\tmargin: 0 0 $sui-gutter;\n\t\t\t\tletter-spacing: -0.25px;\n\n\t\t\t\t&:last-child {\n\t\t\t\t\tmargin-bottom: 0;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t@include media(max-width, md) {\n\t\t\t\tmargin-top: $sui-gutter-md;\n\t\t\t\tmargin-bottom: $sui-gutter-md;\n\t\t\t}\n\n\t\t\t@include media(min-width, md) {\n\t\t\t\tmargin-top: $sui-gutter;\n\t\t\t\tmargin-bottom: $sui-gutter;\n\t\t\t}\n\t\t}\n\n\t\t// SIZE: Regular\n\t\t&:not(.sui-message-lg) {\n\n\t\t\t.sui-image {\n\t\t\t\tmargin: 10px auto;\n\t\t\t}\n\n\t\t\t.sui-message-content {\n\n\t\t\t\t&:first-child {\n\t\t\t\t\tmargin-top: 0;\n\t\t\t\t}\n\n\t\t\t\t&:last-child {\n\t\t\t\t\tmargin-bottom: 0;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t// SIZE: Large\n\t\t&.sui-message-lg {\n\n\t\t\t.sui-image {\n\t\t\t\tmargin: $sui-gutter auto;\n\t\t\t}\n\t\t}\n\n\t\t&:first-child {\n\t\t\tmargin-top: 0;\n\t\t}\n\n\t\t&:last-child {\n\t\t\tmargin-bottom: 0;\n\t\t}\n\n\t\t@include media(max-width, md) {\n\t\t\tpadding: $sui-gutter-md;\n\t\t}\n\n\t\t@include media(min-width, md) {\n\t\t\tpadding: $sui-gutter;\n\t\t}\n\t}\n\n\t// ELEMENT: Box Title\n\t.sui-box {\n\n\t\t.sui-box-title {\n\t\t\tdisplay: block;\n\t\t\tmargin: 0;\n\t\t\tpadding: 0;\n\t\t\tborder: 0;\n\t\t\tcolor: palette(gray, dark);\n\t\t\tfont: $font--weight-bold 15px/30px $font;\n\t\t\ttext-transform: none;\n\t\t\t@include text-truncate;\n\n\t\t\t* {\n\t\t\t\tvertical-align: middle;\n\t\t\t}\n\n\t\t\t[class*=\"sui-icon-\"] {\n\t\t\t\tfloat: left;\n\t\t\t\tdisplay: block;\n\t\t\t\tmargin-right: 10px;\n\n\t\t\t\t&:before {\n\t\t\t\t\tcolor: inherit;\n\t\t\t\t\tfont-size: 20px;\n\t\t\t\t\tvertical-align: text-bottom;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\n\t// ELEMENT: Upsell\n\t.sui-box-body {\n\n\t\t.sui-upsell-row {\n\t\t\tposition: relative;\n\n\t\t\t.sui-upsell-image {\n\t\t\t\twidth: 100px;\n\t\t\t\tposition: absolute;\n\t\t\t\tbottom: 0;\n\n\t\t\t\t@include media(max-width, md) {\n\t\t\t\t\tdisplay: none;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t.sui-upsell-notice {\n\n\t\t\t\tp {\n\t\t\t\t\tposition: relative;\n\t\t\t\t\tpadding: 15px 20px 15px 43px;\n\t\t\t\t\tborder-radius: $border-radius;\n\t\t\t\t\tcolor: $box-upsell-p-color;\n\t\t\t\t\tfont-size: 13px;\n\t\t\t\t\tline-height: 22px;\n\t\t\t\t\tborder-top: 1px solid #e6e6e6;\n\t\t\t\t\tborder-right: 1px solid #e6e6e6;\n\t\t\t\t\tborder-bottom: 1px solid #e6e6e6;\n\t\t\t\t\tborder-left: 2px solid $box-upsell-border-color;\n\n\t\t\t\t\ta {\n\t\t\t\t\t\tcolor: $box-upsell-p-color;\n\t\t\t\t\t\tfont-weight: 500;\n\t\t\t\t\t}\n\t\t\t\t\t&:first-of-type {\n\t\t\t\t\t\t@include icon( before, info );\n\t\t\t\t\t\t&:before {\n\t\t\t\t\t\t\tcolor: $box-upsell-border-color;\n\t\t\t\t\t\t\tmargin-left: -23px;\n\t\t\t\t\t\t\tmargin-right: 10px;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t@include media(min-width, md) {\n\t\t\t\t\tpadding-left: 130px;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t&.sui-upsell-items {\n\t\t\tpadding: 0;\n\t\t}\n\t}\n\n\t// ELEMENT: Other(s)\n\t.sui-box-body {\n\n\t\t.sui-settings-box {\n\t\t\tpadding: $sui-gutter-md;\n\t\t\tborder: 1px solid $box-settings-box-border-color;\n\t\t\tborder-radius: $border-radius;\n\n\t\t\t@include media(min-width, md) {\n\t\t\t\tpadding: $sui-gutter;\n\t\t\t}\n\t\t}\n\t}\n\n\t.sui-with-bottom-border {\n\t\tmargin-bottom: $default-margin;\n\t\tpadding-bottom: $default-padding;\n\t\tborder-bottom: 1px solid $box-settings-box-border-color;\n\n\t\t@include media(max-width, md) {\n\t\t\tmargin-bottom: $sui-gutter-md;\n\t\t\tpadding-bottom: $sui-gutter-md;\n\t\t}\n\t}\n\n\t.sui-border-frame {\n\t\tmargin: 10px 0 0;\n\t\tborder: 1px solid palette(silver, soft);\n\t\tborder-radius: $border-radius;\n\n\t\t&:not(:last-child) {\n\n\t\t\t@include media(max-width, md) {\n\t\t\t\tmargin-bottom: $sui-gutter-md;\n\t\t\t}\n\n\t\t\t@include media(min-width, md) {\n\t\t\t\tmargin-bottom: $sui-gutter;\n\t\t\t}\n\t\t}\n\n\t\t@include media(max-width, md) {\n\t\t\tpadding: $sui-gutter-md;\n\t\t}\n\n\t\t@include media(min-width, md) {\n\t\t\tpadding: $sui-gutter;\n\t\t}\n\t}\n}", "@include body-class(true) {\n\n\t.sui-box-body {\n\n\t\t// ELEMENT: Row\n\t\t.sui-box-settings-row {\n\t\t\tdisplay: flex;\n\t\t\tposition: relative;\n\t\t\tmargin-bottom: $sui-gutter;\n\t\t\tpadding-bottom: $sui-gutter;\n\t\t\tborder-bottom: 1px solid palette(silver, soft);\n\n\t\t\t[class*=\"sui-box-settings-col-\"] {\n\n\t\t\t\t+ div {\n\n\t\t\t\t\t@include media(max-width, md) {\n\t\t\t\t\t\tpadding-top: $sui-gutter-md;\n\t\t\t\t\t}\n\n\t\t\t\t\t@include media(min-width, md) {\n\t\t\t\t\t\tmargin-left: $sui-gutter;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t&:last-child {\n\t\t\t\tmargin-bottom: 0;\n\t\t\t\tpadding-bottom: 0;\n\t\t\t\tborder-bottom-width: 0;\n\t\t\t}\n\n\t\t\t// STATUS: Disable\n\t\t\t&.sui-disabled {\n\t\t\t\topacity: 0.5;\n\t\t\t\tpointer-events: none;\n\t\t\t\tbackground-color: rgba(242, 242, 242, 0.5);\n\t\t\t}\n\n\t\t\t// VARIATION: Flushed\n\t\t\t&.sui-flushed {\n\t\t\t\tmargin-right: -#{$sui-gutter-md};\n\t\t\t\tmargin-left: -#{$sui-gutter-md};\n\t\t\t\tpadding-right: $sui-gutter-md;\n\t\t\t\tpadding-left: $sui-gutter-md;\n\n\t\t\t\t@include media(min-width, md) {\n\t\t\t\t\tmargin-right: -#{$sui-gutter};\n\t\t\t\t\tmargin-left: -#{$sui-gutter};\n\t\t\t\t\tpadding-right: $sui-gutter;\n\t\t\t\t\tpadding-left: $sui-gutter;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t@include media(max-width, md) {\n\t\t\t\tdisplay: block;\n\t\t\t}\n\t\t}\n\n\t\t// ELEMENT: Row (Slim)\n\t\t.sui-box-settings-slim-row {\n\t\t\tdisplay: flex;\n\t\t\tposition: relative;\n\t\t\tmargin-bottom: $sui-gutter-md;\n\n\t\t\t[class*=\"sui-box-settings-col-\"] {\n\n\t\t\t\t+ div {\n\n\t\t\t\t\t@include media(max-width, md) {\n\t\t\t\t\t\tpadding-top: $sui-gutter-md;\n\t\t\t\t\t}\n\n\t\t\t\t\t@include media(min-width, md) {\n\t\t\t\t\t\tmargin-left: $sui-gutter;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t.sui-settings-label {\n\n\t\t\t\t&:only-child {\n\n\t\t\t\t\t@include media(min-width, md) {\n\t\t\t\t\t\tmargin: 5px 0;\n\t\t\t\t\t\tline-height: 30px;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t&:last-child {\n\t\t\t\tmargin-bottom: 0;\n\t\t\t}\n\n\t\t\t@include media(max-width, md) {\n\t\t\t\tdisplay: block;\n\t\t\t}\n\t\t}\n\n\t\t// ELEMENT: Fixed width content\n\t\t.sui-box-settings-col-1 {\n\t\t\tmax-width: 200px;\n\t\t\tflex: 0 1 200px;\n\n\t\t\t.sui-settings-label {\n\t\t\t\tcolor: palette(gray, dark);\n\t\t\t}\n\n\t\t\t@include media(max-width, md) {\n\t\t\t\tmax-width: 100%;\n\t\t\t}\n\t\t}\n\n\t\t// ELEMENT: Adjustable content\n\t\t.sui-box-settings-col-2 {\n\n\t\t\t@include media(min-width, md) {\n\t\t\t\tmin-width: 0;\n\t\t\t\tflex: 1;\n\t\t\t}\n\t\t}\n\n\t\t// ELEMENT: Label\n\t\t.sui-settings-label {\n\t\t\tdisplay: block;\n\t\t\tmargin: 0 0 5px;\n\t\t\tpadding: 0;\n\t\t\tborder: 0;\n\t\t\tcolor: palette(gray, default);\n\t\t\tfont: 500 15px/22px $font;\n\t\t\tletter-spacing: $font--letter-spacing;\n\n\t\t\t&:last-child {\n\t\t\t\tmargin: 0;\n\t\t\t}\n\n\t\t\t&.sui-dark {\n\t\t\t\tcolor: palette(gray, dark);\n\t\t\t}\n\n\t\t\t&-with-tag {\n\t\t\t\t@extend .sui-settings-label;\n\t\t\t\tdisplay: flex;\n\t\t\t\talign-items: flex-start;\n\n\t\t\t\t.sui-tag {\n\t\t\t\t\tmargin-top: -2px;\n\t\t\t\t\tmargin-left: 10px;\n\n\t\t\t\t\t&.sui-tag-sm {\n\t\t\t\t\t\tmargin-top: 4px;\n\t\t\t\t\t}\n\n\t\t\t\t\t&.sui-tag-pro,\n\t\t\t\t\t&.sui-tag-beta {\n\t\t\t\t\t\tmargin-top: 5px;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t// Element: Description\n\t\t.sui-description {\n\n\t\t\t+ .sui-settings-box {\n\t\t\t\tmargin-top: 10px;\n\t\t\t}\n\t\t}\n\n\t\t// FIX: Upsell\n\t\t&.sui-upsell-items {\n\n\t\t\t.sui-box-settings-row {\n\t\t\t\tmargin: 0;\n\t\t\t\tpadding-top: #{$sui-gutter-md + 1px};\n\t\t\t\tpadding-right: $sui-gutter-md;\n\t\t\t\tpadding-left: $sui-gutter-md;\n\t\t\t\tborder-bottom: none;\n\n\t\t\t\t&:after {\n\t\t\t\t\tcontent: '';\n\t\t\t\t\theight: 1px;\n\t\t\t\t\tclear: both;\n\t\t\t\t\tdisplay: block;\n\t\t\t\t\tposition: absolute;\n\t\t\t\t\tright: 0;\n\t\t\t\t\tbottom: 0;\n\t\t\t\t\tleft: 0;\n\t\t\t\t\tbackground-color: palette(silver, soft);\n\t\t\t\t}\n\n\t\t\t\t&:last-child,\n\t\t\t\t&:last-of-type {\n\t\t\t\t\tpadding-bottom: 30px;\n\n\t\t\t\t\t&:after {\n\t\t\t\t\t\tcontent: unset;\n\t\t\t\t\t}\n\n\t\t\t\t\t@include media(max-width, md) {\n\t\t\t\t\t\tpadding-top: $sui-gutter-md;\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t@include media(min-width, md) {\n\t\t\t\t\tpadding-top: #{$sui-gutter + 1px};\n\t\t\t\t\tpadding-right: $sui-gutter;\n\t\t\t\t\tpadding-left: $sui-gutter;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n}\n\n@include body-class($wrap: true, $rtl: true) {\n\n\t.sui-box-settings-row,\n\t.sui-box-settings-slim-row {\n\t\n\t\tselect:not(.sui-select):not(.sui-variables) {\n\t\t\tpadding-right: 14px;\n\t\t\tpadding-left: 40px;\n\t\t\tbackground-position: 14px;\n\t\t}\n\n\t\t[data-clipboard-target] {\n\t\t\tleft: 15px;\n\t\t\tright: auto;\n\t\t}\n\n\t\t[class*=sui-box-settings-col-] + div {\n\n\t\t\t@include media(min-width, md) {\n\t\t\t\tmargin-left: 0;\n\t\t\t\tmargin-right: $sui-gutter;\n\t\t\t}\n\t\t}\n\t}\n\n\t.sui-box-header {\n\t\t.sui-actions-left {\n\t\t\tmargin-left: auto;\n\t\t\tmargin-right: 10px;\n\t\t}\n\t}\n}\n", "@use \"sass:math\";\n\n@mixin generate-column-class($size) {\n\t@for $i from $sui-total-grid-cols through 1 {\n\t\t$width: percentage(math.div($i, $sui-total-grid-cols));\n\t\t.sui-col-#{$size}-#{$i} {\n\t\t\twidth: $width;\n\t\t\tmax-width: $width;\n\t\t\tflex-basis: $width;\n\t\t}\n\t\t.sui-col-#{$size}-offset-#{$i} {\n\t\t\tmargin-left: $width;\n\t\t}\n\t}\n}\n\n@include body-class(true) {\n\n\t%column {\n\t\tmin-height: 1px;\n\t\tpadding-left: math.div($sui-gutter, 2);\n\t\tpadding-right: math.div($sui-gutter, 2);\n\t\tposition: relative;\n\n\t\t@include media( max-width, md ) {\n\t\t\tpadding-left: math.div($sui-gutter-md, 2);\n\t\t\tpadding-right: math.div($sui-gutter-md, 2);\n\t\t}\n\t}\n\n\t.sui-row {\n\t\tdisplay: flex;\n\t\tflex-flow: wrap;\n\t\tmargin-right: -#{math.div($sui-gutter, 2)};\n\t\tmargin-bottom: $sui-gutter;\n\t\tmargin-left: -#{math.div($sui-gutter, 2)};\n\n\t\t&:last-child {\n\t\t\tmargin-bottom: 0;\n\n\t\t\t@include media( max-width, md ) {\n\t\t\t\tmargin-bottom: 0;\n\t\t\t}\n\t\t}\n\n\t\t@include media( max-width, md ) {\n\t\t\tmargin-right: -#{math.div($sui-gutter-md, 2)};\n\t\t\tmargin-bottom: $sui-gutter-md;\n\t\t\tmargin-left: -#{math.div($sui-gutter-md, 2)};\n\t\t}\n\t}\n\n\t.sui-reverse {\n\t\tflex-direction: row-reverse;\n\t}\n\n\t.sui-col {\n\t\t@extend %column;\n\t\tflex: 1;\n\t}\n\n\t[class*=\"sui-col-\"] {\n\t\t@extend %column;\n\t\tflex: 0 0 auto;\n\t\twidth: 100%;\n\t\tmax-width: 100%;\n\t\tflex-basis: 100%;\n\t}\n\n\t@for $i from 1 through length($sui-breakpoints) {\n\t\t$size: nth(nth($sui-breakpoints, $i), 1);\n\t\t$screen-width: nth(nth($sui-breakpoints, $i), 2);\n\t\t@if ($i == 1) {\n\t\t\t@include generate-column-class($size);\n\t\t}\n\t\t@media (min-width: $screen-width) {\n\t\t\t@include generate-column-class($size);\n\t\t}\n\t}\n\n\t[class*=\"sui-col-lg-\"] {\n\n\t\t&:last-child {\n\n\t\t\t@media (max-width: map-get($sui-breakpoints, 'lg')) {\n\t\t\t\tmargin-bottom: 0;\n\t\t\t}\n\t\t}\n\n\t\t@media (max-width: map-get($sui-breakpoints, 'lg')) {\n\t\t\tmargin-bottom: $sui-gutter;\n\t\t}\n\n\t\t@media (max-width: map-get($sui-breakpoints, 'md')) {\n\t\t\tmargin-bottom: $sui-gutter-md;\n\t\t}\n\t}\n\n\t[class*=\"sui-col-md-\"] {\n\n\t\t&:last-child {\n\n\t\t\t@media (max-width: map-get($sui-breakpoints, 'md')) {\n\t\t\t\tmargin-bottom: 0;\n\t\t\t}\n\t\t}\n\n\t\t@media (max-width: map-get($sui-breakpoints, 'md')) {\n\t\t\tmargin-bottom: $sui-gutter-md;\n\t\t}\n\t}\n\n\t[class*=\"sui-col-sm-\"] {\n\n\t\t&:last-child {\n\n\t\t\t@media (max-width: map-get($sui-breakpoints, 'sm')) {\n\t\t\t\tmargin-bottom: 0;\n\t\t\t}\n\t\t}\n\n\t\t@media (max-width: map-get($sui-breakpoints, 'sm')) {\n\t\t\tmargin-bottom: $sui-gutter-md;\n\t\t}\n\t}\n\n\t[class*=\"sui-col-xs-\"] {\n\n\t\t&:last-child {\n\n\t\t\t@media (max-width: map-get($sui-breakpoints, 'xs')) {\n\t\t\t\tmargin-bottom: 0;\n\t\t\t}\n\t\t}\n\n\t\t@media (max-width: map-get($sui-breakpoints, 'xs')) {\n\t\t\tmargin-bottom: $sui-gutter-md;\n\t\t}\n\t}\n}\n", "@use \"sass:math\";\n\n@include body-class(true) {\n\n\t.sui-notice {\n\t\tmargin: 0 0 $sui-gutter;\n\n\t\t.sui-notice-content {\n\t\t\tdisplay: flex;\n\t\t\talign-items: flex-start;\n\t\t\tmargin: 0;\n\t\t\tpadding: #{math.div($sui-gutter-md, 2) + 1px} $sui-gutter-md;\n\t\t\tborder: 0;\n\t\t\tborder-radius: $border-radius;\n\t\t\tbackground-color: $white;\n\t\t\tbox-shadow: inset 2px 0 0 0 palette(silver, medium), inset 0 0 0 1px palette(silver, soft);\n\n\t\t\tp {\n\t\t\t\tmargin: 0 0 math.div($sui-gutter-md, 2);\n\t\t\t\tpadding: 0;\n\t\t\t\tborder: 0;\n\t\t\t\tcolor: palette(gray, dark);\n\t\t\t\tfont-size: 13px;\n\t\t\t\tline-height: 22px;\n\n\t\t\t\t&:last-child {\n\t\t\t\t\tmargin-bottom: 0;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t.sui-notice-icon {\n\t\t\t\twidth: 30px;\n\t\t\t\tdisplay: block;\n\t\t\t\tcolor: palette(gray, light);\n\t\t\t\ttext-align: center;\n\n\t\t\t\t&:before {\n\t\t\t\t\tdisplay: block;\n\t\t\t\t\tcolor: inherit;\n\t\t\t\t}\n\n\t\t\t\t~ *:not(.sui-notice-icon) {\n\t\t\t\t\tpadding-left: #{$sui-gutter - 4px};\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t.sui-notice-message {\n\t\t\t\tflex: 1;\n\t\t\t\tposition: relative;\n\t\t\t\tpadding: 5px 0 3px;\n\n\t\t\t\t.sui-notice-icon {\n\t\t\t\t\tposition: absolute;\n\t\t\t\t\ttop: 8px;\n\t\t\t\t\tleft: -7px;\n\n\t\t\t\t\t&.sui-sm {\n\t\t\t\t\t\ttop: 9px;\n\t\t\t\t\t}\n\n\t\t\t\t\t&.sui-md {\n\t\t\t\t\t\ttop: 7px;\n\t\t\t\t\t}\n\n\t\t\t\t\t&.sui-lg {\n\t\t\t\t\t\ttop: 5px;\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t+ .sui-notice-actions {\n\t\t\t\t\tflex: 0 0 auto;\n\t\t\t\t\tmargin-right: -#{math.div($sui-gutter-md, 2) - 1px};\n\t\t\t\t\tmargin-left: math.div($sui-gutter-md, 2);\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t&[role=\"alert\"],\n\t\t&[aria-live=\"assertive\"] {\n\t\t\tdisplay: none;\n\t\t}\n\n\t\t&.sui-notice-blue,\n\t\t&.sui-notice-info {\n\n\t\t\t.sui-notice-content {\n\t\t\t\tbox-shadow: inset 2px 0 0 0 $blue, inset 0 0 0 1px palette(silver, soft);\n\n\t\t\t\t.sui-notice-icon {\n\t\t\t\t\tcolor: $blue;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t&.sui-notice-green,\n\t\t&.sui-notice-success {\n\n\t\t\t.sui-notice-content {\n\t\t\t\tbox-shadow: inset 2px 0 0 0 $green, inset 0 0 0 1px palette(silver, soft);\n\n\t\t\t\t.sui-notice-icon {\n\t\t\t\t\tcolor: $green;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t&.sui-notice-yellow,\n\t\t&.sui-notice-warning {\n\n\t\t\t.sui-notice-content {\n\t\t\t\tbox-shadow: inset 2px 0 0 0 palette(yellow, default), inset 0 0 0 1px palette(silver, soft);\n\n\t\t\t\t.sui-notice-icon {\n\t\t\t\t\tcolor: palette(yellow, default);\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t&.sui-notice-red,\n\t\t&.sui-notice-error {\n\n\t\t\t.sui-notice-content {\n\t\t\t\tbox-shadow: inset 2px 0 0 0 $red, inset 0 0 0 1px palette(silver, soft);\n\n\t\t\t\t.sui-notice-icon {\n\t\t\t\t\tcolor: $red;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t&.sui-notice-purple,\n\t\t&.sui-notice-upsell {\n\n\t\t\t.sui-notice-content {\n\t\t\t\tbox-shadow: inset 2px 0 0 0 palette(purple, default), inset 0 0 0 1px palette(silver, soft);\n\n\t\t\t\t.sui-notice-icon {\n\t\t\t\t\tcolor: palette(purple, default);\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t&:last-child {\n\t\t\tmargin: 0;\n\t\t}\n\t}\n\n\t.sui-floating-notices {\n\t\tpointer-events: none;\n\t\tposition: fixed;\n\t\tz-index: 99999;\n\t\ttop: 0;\n\t\tright: 0;\n\t\tbottom: 0;\n\t\tpadding: math.div($sui-gutter-md, 2);\n\n\t\t.sui-notice {\n\t\t\twidth: 100%;\n\t\t\tmax-width: 600px;\n\t\t\tpointer-events: initial;\n\t\t\tz-index: 1;\n\t\t\tmargin: 0 auto math.div($sui-gutter-md, 2);\n\n\t\t\t.sui-notice-content {\n\t\t\t\tbox-shadow: 0 5px 25px 0 rgba(0,0,0,0.15), inset 2px 0 0 0 palette(silver, medium);\n\t\t\t}\n\n\t\t\t&.sui-notice-blue,\n\t\t\t&.sui-notice-info {\n\n\t\t\t\t.sui-notice-content {\n\t\t\t\t\tbox-shadow: 0 5px 25px 0 rgba(0,0,0,0.15), inset 2px 0 0 0 $blue;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t&.sui-notice-green,\n\t\t\t&.sui-notice-success {\n\n\t\t\t\t.sui-notice-content {\n\t\t\t\t\tbox-shadow: 0 5px 25px 0 rgba(0,0,0,0.15), inset 2px 0 0 0 $green;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t&.sui-notice-yellow,\n\t\t\t&.sui-notice-warning {\n\n\t\t\t\t.sui-notice-content {\n\t\t\t\t\tbox-shadow: 0 5px 25px 0 rgba(0,0,0,0.15), inset 2px 0 0 0 palette(yellow, default);\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t&.sui-notice-red,\n\t\t\t&.sui-notice-error {\n\n\t\t\t\t.sui-notice-content {\n\t\t\t\t\tbox-shadow: 0 5px 25px 0 rgba(0,0,0,0.15), inset 2px 0 0 0 $red;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t&.sui-notice-purple,\n\t\t\t&.sui-notice-upsell {\n\n\t\t\t\t.sui-notice-content {\n\t\t\t\t\tbox-shadow: 0 5px 25px 0 rgba(0,0,0,0.15), inset 2px 0 0 0 palette(purple, default);\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t&:last-child {\n\t\t\t\tmargin-bottom: 0;\n\t\t\t}\n\n\t\t\t@media only screen and (max-width: 640px) {\n\t\t\t\tmax-width: 100%;\n\t\t\t}\n\t\t}\n\t}\n\n\t// VARIATION: Color Accessibility.\n\t&.sui-color-accessible {\n\n\t\t.sui-notice {\n\n\t\t\t.sui-notice-content {\n\t\t\t\tbox-shadow: inset 2px 0 0 0 $black, inset 0 0 0 1px palette(silver, soft);\n\n\t\t\t\tp {\n\t\t\t\t\tcolor: $black;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t&.sui-notice-blue,\n\t\t\t&.sui-notice-info,\n\t\t\t&.sui-notice-green,\n\t\t\t&.sui-notice-success,\n\t\t\t&.sui-notice-yellow,\n\t\t\t&.sui-notice-warning,\n\t\t\t&.sui-notice-red,\n\t\t\t&.sui-notice-error,\n\t\t\t&.sui-notice-purple,\n\t\t\t&.sui-notice-upsell {\n\n\t\t\t\t.sui-notice-content {\n\t\t\t\t\tbox-shadow: inset 2px 0 0 0 $black, inset 0 0 0 1px palette(silver, soft);\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t.sui-floating-notices {\n\n\t\t\t.sui-notice {\n\n\t\t\t\t.sui-notice-content {\n\t\t\t\t\tbox-shadow: 0 5px 25px 0 rgba(0,0,0,0.15), inset 2px 0 0 0 $black;\n\t\t\t\t}\n\n\t\t\t\t&.sui-notice-blue,\n\t\t\t\t&.sui-notice-info,\n\t\t\t\t&.sui-notice-green,\n\t\t\t\t&.sui-notice-success,\n\t\t\t\t&.sui-notice-yellow,\n\t\t\t\t&.sui-notice-warning,\n\t\t\t\t&.sui-notice-red,\n\t\t\t\t&.sui-notice-error,\n\t\t\t\t&.sui-notice-purple,\n\t\t\t\t&.sui-notice-upsell {\n\n\t\t\t\t\t.sui-notice-content {\n\t\t\t\t\t\tbox-shadow: 0 5px 25px 0 rgba(0,0,0,0.15), inset 2px 0 0 0 $black;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n}\n\n// RTL Support.\n@include body-class(true, true) {\n\n\t.sui-notice {\n\t\tdirection: rtl;\n\n\t\t.sui-notice-content {\n\t\t\tflex-direction: row;\n\t\t\tbox-shadow: inset -2px 0 0 0 palette(silver, medium), inset 0 0 0 1px palette(silver, soft);\n\n\t\t\tp {\n\t\t\t\tpadding-right: #{$sui-gutter - 4px};\n\t\t\t\tpadding-left: 0;\n\t\t\t}\n\n\t\t\t.sui-notice-message {\n\n\t\t\t\t.sui-notice-icon {\n\t\t\t\t\tleft: auto;\n\t\t\t\t\tright: -7px;\n\t\t\t\t}\n\n\t\t\t\t+ .sui-notice-actions {\n\t\t\t\t\tmargin-right: math.div($sui-gutter-md, 2);\n\t\t\t\t\tmargin-left: -#{math.div($sui-gutter-md, 2) - 1px};\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t&.sui-notice-blue,\n\t\t&.sui-notice-info {\n\n\t\t\t.sui-notice-content {\n\t\t\t\tbox-shadow: inset -2px 0 0 0 $blue, inset 0 0 0 1px palette(silver, soft);\n\t\t\t}\n\t\t}\n\n\t\t&.sui-notice-green,\n\t\t&.sui-notice-success {\n\n\t\t\t.sui-notice-content {\n\t\t\t\tbox-shadow: inset -2px 0 0 0 $green, inset 0 0 0 1px palette(silver, soft);\n\t\t\t}\n\t\t}\n\n\t\t&.sui-notice-yellow,\n\t\t&.sui-notice-warning {\n\n\t\t\t.sui-notice-content {\n\t\t\t\tbox-shadow: inset -2px 0 0 0 palette(yellow, default), inset 0 0 0 1px palette(silver, soft);\n\t\t\t}\n\t\t}\n\n\t\t&.sui-notice-red,\n\t\t&.sui-notice-error {\n\n\t\t\t.sui-notice-content {\n\t\t\t\tbox-shadow: inset -2px 0 0 0 $red, inset 0 0 0 1px palette(silver, soft);\n\t\t\t}\n\t\t}\n\n\t\t&.sui-notice-purple,\n\t\t&.sui-notice-upsell {\n\n\t\t\t.sui-notice-content {\n\t\t\t\tbox-shadow: inset -2px 0 0 0 palette(purple, default), inset 0 0 0 1px palette(silver, soft);\n\t\t\t}\n\t\t}\n\t}\n\n\t.sui-floating-notices {\n\n\t\t.sui-notice {\n\n\t\t\t.sui-notice-content {\n\t\t\t\tbox-shadow: 0 5px 25px 0 rgba(0,0,0,0.15), inset -2px 0 0 0 palette(silver, medium);\n\t\t\t}\n\n\t\t\t&.sui-notice-blue,\n\t\t\t&.sui-notice-info {\n\n\t\t\t\t.sui-notice-content {\n\t\t\t\t\tbox-shadow: 0 5px 25px 0 rgba(0,0,0,0.15), inset -2px 0 0 0 $blue;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t&.sui-notice-green,\n\t\t\t&.sui-notice-success {\n\n\t\t\t\t.sui-notice-content {\n\t\t\t\t\tbox-shadow: 0 5px 25px 0 rgba(0,0,0,0.15), inset -2px 0 0 0 $green;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t&.sui-notice-yellow,\n\t\t\t&.sui-notice-warning {\n\n\t\t\t\t.sui-notice-content {\n\t\t\t\t\tbox-shadow: 0 5px 25px 0 rgba(0,0,0,0.15), inset -2px 0 0 0 palette(yellow, default);\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t&.sui-notice-red,\n\t\t\t&.sui-notice-error {\n\n\t\t\t\t.sui-notice-content {\n\t\t\t\t\tbox-shadow: 0 5px 25px 0 rgba(0,0,0,0.15), inset -2px 0 0 0 $red;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t&.sui-notice-purple,\n\t\t\t&.sui-notice-upsell {\n\n\t\t\t\t.sui-notice-content {\n\t\t\t\t\tbox-shadow: 0 5px 25px 0 rgba(0,0,0,0.15), inset -2px 0 0 0 palette(purple, default);\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\n\t// VARIATION: Color Accessibility.\n\t&.sui-color-accessible {\n\n\t\t.sui-notice {\n\n\t\t\t.sui-notice-content {\n\t\t\t\tbox-shadow: inset -2px 0 0 0 $black, inset 0 0 0 1px palette(silver, soft);\n\n\t\t\t\tp {\n\t\t\t\t\tcolor: $black;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t&.sui-notice-blue,\n\t\t\t&.sui-notice-info,\n\t\t\t&.sui-notice-green,\n\t\t\t&.sui-notice-success,\n\t\t\t&.sui-notice-yellow,\n\t\t\t&.sui-notice-warning,\n\t\t\t&.sui-notice-red,\n\t\t\t&.sui-notice-error,\n\t\t\t&.sui-notice-purple,\n\t\t\t&.sui-notice-upsell {\n\n\t\t\t\t.sui-notice-content {\n\t\t\t\t\tbox-shadow: inset -2px 0 0 0 $black, inset 0 0 0 1px palette(silver, soft);\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t.sui-floating-notices {\n\n\t\t\t.sui-notice {\n\n\t\t\t\t.sui-notice-content {\n\t\t\t\t\tbox-shadow: 0 5px 25px 0 rgba(0,0,0,0.15), inset -2px 0 0 0 $black;\n\t\t\t\t}\n\n\t\t\t\t&.sui-notice-blue,\n\t\t\t\t&.sui-notice-info,\n\t\t\t\t&.sui-notice-green,\n\t\t\t\t&.sui-notice-success,\n\t\t\t\t&.sui-notice-yellow,\n\t\t\t\t&.sui-notice-warning,\n\t\t\t\t&.sui-notice-red,\n\t\t\t\t&.sui-notice-error,\n\t\t\t\t&.sui-notice-purple,\n\t\t\t\t&.sui-notice-upsell {\n\n\t\t\t\t\t.sui-notice-content {\n\t\t\t\t\t\tbox-shadow: 0 5px 25px 0 rgba(0,0,0,0.15), inset -2px 0 0 0 $black;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n}\n\n// WordPress Support.\n@include body-class {\n\n\t&.wp-admin {\n\n\t\t.sui-floating-notices {\n\t\t\tleft: map-get($wordpress, adminmenu);\n\t\t}\n\n\t\t// Folded sidebar.\n\t\t&.folded {\n\n\t\t\t.sui-floating-notices {\n\t\t\t\tleft: map-get($wordpress, adminmenu-sm);\n\t\t\t}\n\t\t}\n\n\t\t// Auto-folded sidebar.\n\t\t&.auto-fold {\n\n\t\t\t.sui-floating-notices {\n\n\t\t\t\t@media only screen and (max-width: 960px) {\n\t\t\t\t\tleft: map-get($wordpress, adminmenu-sm);\n\t\t\t\t}\n\n\t\t\t\t@media only screen and (max-width: 782px) {\n\t\t\t\t\tleft: 0;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n}", "@use \"sass:math\";\n\n@include body-class(true) {\n\n\t.sui-header {\n\t\tdisplay: flex;\n\t\tflex-wrap: wrap;\n\t\talign-items: center;\n\t\tposition: relative;\n\t\tmargin-bottom: $sui-gutter;\n\t\tline-height: 1;\n\n\t\th1 {\n\n\t\t\t@include media(max-width, md) {\n\t\t\t\tflex: 0 0 100%;\n\t\t\t}\n\t\t}\n\n\t\t.sui-actions-right {\n\n\t\t\t@include media(max-width, md) {\n\t\t\t\tmargin-top: 10px;\n\t\t\t\tmargin-right: auto;\n\t\t\t\tmargin-left: 0;\n\t\t\t}\n\t\t}\n\n\t\t.sui-actions-left {\n\n\t\t\t@include media(max-width, md) {\n\t\t\t\tmargin-top: 10px;\n\t\t\t\tmargin-right: 0;\n\t\t\t\tmargin-left: 0;\n\t\t\t}\n\t\t}\n\n\t\t&.sui-header-inline {\n\n\t\t\th1 {\n\n\t\t\t\t+ * {\n\n\t\t\t\t\t@include media(max-width, md) {\n\t\t\t\t\t\tmargin-left: 10px;\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t@include media(max-width, md) {\n\t\t\t\t\tflex: 1;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t.sui-actions-right,\n\t\t\t.sui-actions-left {\n\n\t\t\t\t@include media(max-width, md) {\n\t\t\t\t\tflex: 0 0 auto;\n\t\t\t\t\tmargin-top: 0;\n\t\t\t\t\tmargin-right: 0;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t&.sui-with-floating-input {\n\n\t\t\th1 {\n\n\t\t\t\t@include media(min-width, lg) {\n\t\t\t\t\tpadding-right: 260px;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t@include media(max-width, md) {\n\t\t\t\tmargin-bottom: math.div($sui-gutter, 2);\n\t\t\t}\n\t\t}\n\n\t\t@include media(max-width, md) {\n\t\t\tmargin-bottom: $sui-gutter-md;\n\t\t}\n\t}\n\n\t.sui-header-title {\n\t\tcolor: $headings-color;\n\t\tmargin: 0;\n\t\ttext-align: left;\n\t\tfont-weight: bold;\n\t\tmax-width: none;\n\t\twhite-space: nowrap;\n\t\toverflow: hidden;\n\t\ttext-overflow: ellipsis;\n\t}\n}\n", "@use \"sass:math\";\n\n// Summary: Base styles.\n// $wrap: true | $rtl: false | $monochrome: false\n@include body-class(true, false) {\n\t.sui-summary {\n\t\tpadding: math.div($sui-gutter-md, 2) $sui-gutter-md;\n\n\t\t.sui-summary-image-space,\n\t\t.sui-summary-segment {\n\t\t\tpadding: math.div($sui-gutter-md, 2) 0;\n\n\t\t\t@include media(min-width, md) {\n\t\t\t\tpadding: 0 math.div($sui-gutter, 2);\n\t\t\t}\n\t\t}\n\n\t\t// ELEMENT: Image\n\t\t.sui-summary-image-space {\n\t\t\tdisplay: none;\n\n\t\t\t@include media(min-width, md) {\n\t\t\t\tmin-width: $summary-size-width;\n\t\t\t\tmin-height: $summary-size-height;\n\t\t\t\tflex: 0 0 $summary-size-width;\n\t\t\t}\n\t\t}\n\n\t\t// ELEMENT: Content\n\t\t.sui-summary-segment {\n\t\t\tdisplay: block;\n\n\t\t\t.sui-list {\n\t\t\t\t@include media(min-width, md) {\n\t\t\t\t\tpadding-right: math.div($sui-gutter, 2);\n\t\t\t\t\tpadding-left: math.div($sui-gutter, 2);\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t.sui-summary-details {\n\t\t\t\t@include media(min-width, md) {\n\t\t\t\t\tpadding-right: $sui-gutter;\n\t\t\t\t\tpadding-left: $sui-gutter;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t&:first-child {\n\t\t\t\t.sui-list,\n\t\t\t\t.sui-summary-details {\n\t\t\t\t\t@include media(min-width, md) {\n\t\t\t\t\t\tpadding-left: 0;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t&:last-child {\n\t\t\t\t.sui-list,\n\t\t\t\t.sui-summary-details {\n\t\t\t\t\t@include media(min-width, md) {\n\t\t\t\t\t\tpadding-right: 0;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t@include media(min-width, md) {\n\t\t\t\toverflow: hidden;\n\t\t\t\tflex: 1;\n\t\t\t\ttext-overflow: ellipsis;\n\t\t\t\twhite-space: nowrap;\n\t\t\t}\n\t\t}\n\n\t\t// ELEMENT: Summary details\n\t\t.sui-summary-details {\n\t\t\tcolor: $summary-details--color;\n\t\t\tfont-size: 13px;\n\t\t\tline-height: 22px;\n\t\t\tfont-family: $font;\n\t\t\tletter-spacing: $font--letter-spacing;\n\t\t\ttext-align: center;\n\n\t\t\tspan {\n\t\t\t\tdisplay: inline-block;\n\t\t\t}\n\n\t\t\t.sui-summary-sub {\n\t\t\t\tdisplay: block;\n\t\t\t\tmargin-top: 0;\n\t\t\t\tmargin-bottom: 5px;\n\t\t\t\tcolor: $summary-details--sub-color;\n\n\t\t\t\t&:last-child {\n\t\t\t\t\tmargin-bottom: 0;\n\n\t\t\t\t\t@include media(min-width, md) {\n\t\t\t\t\t\tmargin-bottom: 0;\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t@include media(min-width, md) {\n\t\t\t\t\tmargin-bottom: 24px;\n\t\t\t\t\twhite-space: normal;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t.sui-summary-percent {\n\t\t\t\tmargin-left: -5px;\n\t\t\t}\n\n\t\t\t.sui-summary-large {\n\t\t\t\tfont-size: 50px;\n\t\t\t\tline-height: 55px;\n\n\t\t\t\t+ .sui-summary-sub {\n\t\t\t\t\t@include media(min-width, md) {\n\t\t\t\t\t\tmargin-top: -1px;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t.sui-summary-detail {\n\t\t\t\tdisplay: block;\n\t\t\t\tfont-size: 15px;\n\t\t\t\tfont-weight: 500;\n\n\t\t\t\timg {\n\t\t\t\t\tposition: relative;\n\t\t\t\t\tvertical-align: middle;\n\t\t\t\t\ttop: -1px;\n\t\t\t\t\tmargin-right: 6px;\n\n\t\t\t\t\t@include media(min-width, md) {\n\t\t\t\t\t\tfloat: left;\n\t\t\t\t\t\tvertical-align: unset;\n\t\t\t\t\t\ttop: 1px;\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t+ .sui-summary-sub {\n\t\t\t\t\tmargin-top: 5px;\n\t\t\t\t}\n\n\t\t\t\t@include media(min-width, md) {\n\t\t\t\t\toverflow: hidden;\n\t\t\t\t\ttext-overflow: ellipsis;\n\t\t\t\t\twhite-space: nowrap;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t[class*=\"sui-icon-\"] {\n\t\t\t\tposition: relative;\n\t\t\t\ttop: -22px;\n\t\t\t\tleft: 5px;\n\t\t\t\tfont-size: 16px;\n\n\t\t\t\t+ .sui-summary-percent {\n\t\t\t\t\tmargin-left: -20px;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t@include media(min-width, md) {\n\t\t\t\tpadding-top: $sui-gutter;\n\t\t\t\tpadding-bottom: $sui-gutter;\n\t\t\t\ttext-align: left;\n\t\t\t}\n\t\t}\n\n\t\t// ELEMENT: List of data\n\t\t.sui-list {\n\t\t\tli span {\n\t\t\t\twhite-space: normal;\n\t\t\t}\n\n\t\t\t@include media(min-width, md) {\n\t\t\t\tpadding-top: $sui-gutter;\n\t\t\t\tpadding-bottom: $sui-gutter;\n\t\t\t}\n\t\t}\n\n\t\t// SIZE: Small\n\t\t&.sui-summary-sm {\n\t\t\t// ELEMENT: Image\n\t\t\t.sui-summary-image-space {\n\t\t\t\t@include media(min-width, md) {\n\t\t\t\t\tmin-width: #{$summary-image-sm--width + $sui-gutter};\n\t\t\t\t\tmin-height: $summary-size-sm;\n\t\t\t\t\tflex: 0 0 #{$summary-image-sm--width + $sui-gutter};\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t// VARIATION: Branded\n\t\t// When box is using white labelling settings from WPMU DEV Dashboard.\n\t\t&:not(.sui-unbranded) {\n\t\t\t// SIZE: Regular\n\t\t\t&:not(.sui-summary-sm) {\n\t\t\t\t// ELEMENT: Image\n\t\t\t\t.sui-summary-image-space {\n\t\t\t\t\t@include media(min-width, md) {\n\t\t\t\t\t\t@if variable-exists(summary-image) {\n\t\t\t\t\t\t\t@if $summary-image != \"\" {\n\t\t\t\t\t\t\t\tdisplay: block;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t// FIX:\n\t\t\t\t// This fil will allow us to add the correct margins\n\t\t\t\t// in case summary-image variable doesn't exist or\n\t\t\t\t// variable is empty.\n\t\t\t\t@if variable-exists(summary-image) {\n\t\t\t\t\t@if $summary-image == \"\" {\n\t\t\t\t\t\t.sui-summary-image-space {\n\t\t\t\t\t\t\t&:first-child + .sui-summary-segment {\n\t\t\t\t\t\t\t\t.sui-list,\n\t\t\t\t\t\t\t\t.sui-summary-details {\n\t\t\t\t\t\t\t\t\t@include media(min-width, md) {\n\t\t\t\t\t\t\t\t\t\tpadding-left: 0;\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t} @else {\n\t\t\t\t\t.sui-summary-image-space {\n\t\t\t\t\t\t&:first-child + .sui-summary-segment {\n\t\t\t\t\t\t\t.sui-list,\n\t\t\t\t\t\t\t.sui-summary-details {\n\t\t\t\t\t\t\t\t@include media(min-width, md) {\n\t\t\t\t\t\t\t\t\tpadding-left: 0;\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t&:not(.sui-rebranded) {\n\t\t\t\t\t@include media(min-width, md) {\n\t\t\t\t\t\t@if variable-exists(summary-image) {\n\t\t\t\t\t\t\tbackground-image: url($summary-image);\n\t\t\t\t\t\t\tbackground-size: $summary-image--width\n\t\t\t\t\t\t\t\t$summary-image--height;\n\t\t\t\t\t\t\tbackground-position: $summary-image--position;\n\t\t\t\t\t\t\tbackground-repeat: no-repeat;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t// SIZE: Small\n\t\t\t&.sui-summary-sm {\n\t\t\t\t// ELEMENT: Image\n\t\t\t\t.sui-summary-image-space {\n\t\t\t\t\t@include media(min-width, md) {\n\t\t\t\t\t\t@if variable-exists(summary-image-sm) {\n\t\t\t\t\t\t\t@if $summary-image-sm != \"\" {\n\t\t\t\t\t\t\t\tdisplay: block;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t// FIX:\n\t\t\t\t// This fil will allow us to add the correct margins\n\t\t\t\t// in case summary-image-sm variable doesn't exist or\n\t\t\t\t// variable is empty.\n\t\t\t\t@if variable-exists(summary-image-sm) {\n\t\t\t\t\t@if $summary-image-sm == \"\" {\n\t\t\t\t\t\t.sui-summary-image-space {\n\t\t\t\t\t\t\t&:first-child + .sui-summary-segment {\n\t\t\t\t\t\t\t\t.sui-list,\n\t\t\t\t\t\t\t\t.sui-summary-details {\n\t\t\t\t\t\t\t\t\t@include media(min-width, md) {\n\t\t\t\t\t\t\t\t\t\tpadding-left: 0;\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t} @else {\n\t\t\t\t\t.sui-summary-image-space {\n\t\t\t\t\t\t&:first-child + .sui-summary-segment {\n\t\t\t\t\t\t\t.sui-list,\n\t\t\t\t\t\t\t.sui-summary-details {\n\t\t\t\t\t\t\t\t@include media(min-width, md) {\n\t\t\t\t\t\t\t\t\tpadding-left: 0;\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t&:not(.sui-rebranded) {\n\t\t\t\t\t@include media(min-width, md) {\n\t\t\t\t\t\t@if variable-exists(summary-image-sm) {\n\t\t\t\t\t\t\tbackground-image: url($summary-image-sm);\n\t\t\t\t\t\t\tbackground-size: $summary-image-sm--width\n\t\t\t\t\t\t\t\t$summary-image-sm--height;\n\t\t\t\t\t\t\tbackground-position: $summary-image-sm--position;\n\t\t\t\t\t\t\tbackground-repeat: no-repeat;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t// VARIATION: Unbranded\n\t\t// When box is using white labelling settings from WPMU DEV Dashboard\n\t\t// but no image has been added.\n\t\t&.sui-unbranded {\n\t\t\t.sui-summary-image-space {\n\t\t\t\t&:first-child + .sui-summary-segment {\n\t\t\t\t\t.sui-list,\n\t\t\t\t\t.sui-summary-details {\n\t\t\t\t\t\t@include media(min-width, md) {\n\t\t\t\t\t\t\tpadding-left: 0;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t@include media(min-width, md) {\n\t\t\t\t\t@if variable-exists(summary-image) {\n\t\t\t\t\t\t@if $summary-image != \"\" {\n\t\t\t\t\t\t\tdisplay: none;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t// VARIATION: Rebranded\n\t\t// When box is using white labelling settings from WPMU DEV Dashboard\n\t\t// and an image has been assigned by user.\n\t\t&.sui-rebranded {\n\t\t\t// SIZE: Regular\n\t\t\t&:not(.sui-summary-sm) {\n\t\t\t\t.sui-summary-image-space {\n\t\t\t\t\t@include media(min-width, md) {\n\t\t\t\t\t\t@if variable-exists(summary-image) {\n\t\t\t\t\t\t\tbackground-size: contain;\n\t\t\t\t\t\t\tbackground-position: center;\n\t\t\t\t\t\t\tbackground-repeat: no-repeat;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t// SIZE: Small\n\t\t\t&.sui-summary-sm {\n\t\t\t\t.sui-summary-image-space {\n\t\t\t\t\t@include media(min-width, md) {\n\t\t\t\t\t\t@if variable-exists(summary-image-sm) {\n\t\t\t\t\t\t\tbackground-size: contain;\n\t\t\t\t\t\t\tbackground-position: center;\n\t\t\t\t\t\t\tbackground-repeat: no-repeat;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t@include media(min-width, md) {\n\t\t\tdisplay: flex;\n\t\t\talign-items: center;\n\t\t\tpadding: 0 math.div($sui-gutter, 2);\n\t\t}\n\t}\n}\n\n// Summary: RTL styles.\n// $wrap: true | $rtl: true | $monochrome: false\n@include body-class(true, true) {\n\t.sui-summary {\n\t\t// ELEMENT: Content\n\t\t.sui-summary-segment {\n\t\t\t&:first-child {\n\t\t\t\t.sui-list,\n\t\t\t\t.sui-summary-details {\n\t\t\t\t\t@include media(min-width, md) {\n\t\t\t\t\t\tpadding-right: 0;\n\t\t\t\t\t\tpadding-left: math.div($sui-gutter, 2);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t&:last-child {\n\t\t\t\t.sui-list,\n\t\t\t\t.sui-summary-details {\n\t\t\t\t\t@include media(min-width, md) {\n\t\t\t\t\t\tpadding-right: math.div($sui-gutter, 2);\n\t\t\t\t\t\tpadding-left: 0;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t// ELEMENT: Summary details\n\t\t.sui-summary-details {\n\t\t\ttext-align: right;\n\n\t\t\t.sui-summary-percent {\n\t\t\t\tmargin-right: -5px;\n\t\t\t\tmargin-left: 0;\n\t\t\t}\n\n\t\t\t[class*=\"sui-icon-\"] {\n\t\t\t\tleft: 0;\n\n\t\t\t\t+ .sui-summary-percent {\n\t\t\t\t\tmargin-right: -20px;\n\t\t\t\t\tmargin-left: 0;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t// VARIATION: Branded\n\t\t// When box is using white labelling settings from WPMU DEV Dashboard.\n\t\t&:not(.sui-unbranded) {\n\t\t\t// SIZE: Regular\n\t\t\t&:not(.sui-summary-sm) {\n\t\t\t\t@include media(min-width, md) {\n\t\t\t\t\t@if variable-exists(summary-image) {\n\t\t\t\t\t\tbackground-position: right $summary-image--position;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t// SIZE: Small\n\t\t\t&.sui-summary-sm {\n\t\t\t\t@include media(min-width, md) {\n\t\t\t\t\t@if variable-exists(summary-image-sm) {\n\t\t\t\t\t\tbackground-position: right $summary-image-sm--position;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n}\n\n// Summary: Monochrome styles for color accessibility.\n// $wrap: true | $rtl: false | $monochrome: true\n@include body-class(true, false, true) {\n\t.sui-summary {\n\t\t.sui-summary-details {\n\t\t\tcolor: $accessible-dark;\n\n\t\t\t.sui-summary-sub {\n\t\t\t\tcolor: $accessible-dark;\n\t\t\t}\n\t\t}\n\t}\n}\n", "@use \"sass:math\";\n\n@include body-class(true, false) {\n\n\t.sui-list {\n\t\tmargin: $sui-gutter-md 0;\n\t\tpadding: 0;\n\t\tborder: 0;\n\n\t\t&, li {\n\t\t\tlist-style: none;\n\t\t}\n\n\t\tli {\n\t\t\tdisplay: flex;\n\t\t\tmargin: 0;\n\t\t\tpadding: 9px 0;\n\t\t\tborder: 0;\n\t\t\tborder-bottom: 1px solid palette(silver, soft);\n\t\t\tcolor: $summary-list--color;\n\t\t\tfont-size: 13px;\n\t\t\tline-height: 22px;\n\t\t\tfont-family: $font;\n\t\t\tfont-weight: 500;\n\t\t\tletter-spacing: $font--letter-spacing;\n\n\t\t\t[class*=\"sui-list-\"] {\n\t\t\t\tpadding: 0 math.div($sui-gutter-md, 2);\n\n\t\t\t\t&:first-child {\n\t\t\t\t\tpadding-left: 0;\n\n\t\t\t\t\t@include media(min-width, md) {\n\t\t\t\t\t\tpadding-left: 0;\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t&:last-child {\n\t\t\t\t\tpadding-right: 0;\n\n\t\t\t\t\t@include media(min-width, md) {\n\t\t\t\t\t\tpadding-right: 0;\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t@include media(min-width, md) {\n\t\t\t\t\tpadding: 0 math.div($sui-gutter, 2);\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t.sui-list-label {\n\t\t\t\tflex: 1;\n\t\t\t\tcolor: $summary-list--label;\n\t\t\t}\n\n\t\t\t.sui-list-detail {\n\t\t\t\tflex: 0 0 auto;\n\t\t\t\ttext-align: right;\n\t\t\t}\n\n\t\t\t&:first-child {\n\t\t\t\tpadding-top: 0;\n\n\t\t\t\t@include media(min-width, md) {\n\t\t\t\t\tpadding-top: 0;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t&:last-child {\n\t\t\t\tpadding-bottom: 0;\n\t\t\t\tborder-bottom: 0;\n\n\t\t\t\t@include media(min-width, md) {\n\t\t\t\t\tpadding-bottom: 0;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t@include media(min-width, md) {\n\t\t\t\tpadding: 19px 0;\n\t\t\t}\n\t\t}\n\n\t\t&:first-child {\n\t\t\tmargin-top: 0;\n\n\t\t\t@include media(min-width, md) {\n\t\t\t\tmargin-top: 0;\n\t\t\t}\n\t\t}\n\n\t\t&:last-child {\n\t\t\tmargin-bottom: 0;\n\n\t\t\t@include media(min-width, md) {\n\t\t\t\tmargin-bottom: 0;\n\t\t\t}\n\t\t}\n\n\t\t// FIX:\n\t\t// Prevent .sui-list from having nested lists\n\t\t// since this element doesn't support it.\n\t\tul, ol {\n\t\t\tdisplay: none;\n\t\t}\n\n\t\t@include media(min-width, md) {\n\t\t\tmargin: $sui-gutter 0;\n\t\t}\n\t}\n}\n\n@include body-class(true, true) {\n\n\t.sui-list {\n\n\t\tli {\n\n\t\t\t[class*=\"sui-list-\"] {\n\n\t\t\t\t&:first-child {\n\t\t\t\t\tpadding-right: 0;\n\t\t\t\t\tpadding-left: math.div($sui-gutter-md, 2);\n\n\t\t\t\t\t@include media(min-width, md) {\n\t\t\t\t\t\tpadding-right: 0;\n\t\t\t\t\t\tpadding-left: math.div($sui-gutter, 2);\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t&:last-child {\n\t\t\t\t\tpadding-right: math.div($sui-gutter-md, 2);\n\t\t\t\t\tpadding-left: 0;\n\n\t\t\t\t\t@include media(min-width, md) {\n\t\t\t\t\t\tpadding-right: math.div($sui-gutter, 2);\n\t\t\t\t\t\tpadding-left: 0;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n}", "@include body-class(true) {\n\n\t.sui-tooltip {\n\t\tposition: relative;\n\n\t\t&:before,\n\t\t&:after {\n\t\t\tcontent: \" \";\n\t\t\topacity: 0;\n\t\t\tbackface-visibility: hidden;\n\t\t\tpointer-events: none;\n\t\t\tposition: absolute;\n\t\t\tz-index: 9990;\n\t\t\ttransition: 0.2s;\n\t\t}\n\n\t\t&:before {\n\t\t\tleft: 50%;\n\t\t\tbottom: 100%;\n\t\t\tborder: 5px solid transparent;\n\t\t\tborder-top-color: $tooltips-color;\n\t\t\ttransform: translateX(-50%);\n\t\t}\n\n\t\t&:after {\n\t\t\tcontent: attr(data-tooltip);\n\t\t\tmin-width: var(--tooltip-width, 40px);\n\t\t\tleft: 50%;\n\t\t\tbottom: 100%;\n\t\t\tmargin-bottom: 10px;\n\t\t\tpadding: 8px 12px;\n\t\t\tborder-radius: $border-radius;\n\t\t\tbackground-color: $tooltips-color;\n\t\t\tbox-sizing: border-box;\n\t\t\ttransform: translateX(-50%);\n\t\t\tcolor: $white;\n\t\t\tfont: 500 12px/18px $font;\n\t\t\tletter-spacing: $font--letter-spacing;\n\t\t\ttext-transform: none;\n\t\t\ttext-align: var(--tooltip-text-align, center);\n\n\t\t\t@include media(min-width, md) {\n\t\t\t\twhite-space: nowrap;\n\t\t\t}\n\t\t}\n\n\t\t// VARIATION: Constrained tooltip\n\t\t&.sui-tooltip-constrained {\n\n\t\t\t&:after {\n\t\t\t\tmin-width: var(--tooltip-width, 240px);\n\t\t\t\twhite-space: normal;\n\t\t\t}\n\t\t}\n\n\t\t// POSITION: Top\n\t\t&.sui-tooltip-top {\n\n\t\t\t// POSITION: Left\n\t\t\t&-left {\n\n\t\t\t\t&:after {\n\t\t\t\t\tleft: 0;\n\t\t\t\t\ttransform: translateX(0);\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t// POSITION: Right\n\t\t\t&-right {\n\n\t\t\t\t&:after {\n\t\t\t\t\tright: 0;\n\t\t\t\t\tleft: unset;\n\t\t\t\t\ttransform: translateX(0);\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t// POSITION: Bottom\n\t\t&.sui-tooltip-bottom {\n\n\t\t\t&,\n\t\t\t&-left,\n\t\t\t&-right {\n\n\t\t\t\t&:before {\n\t\t\t\t\ttop: 100%;\n\t\t\t\t\tbottom: unset;\n\t\t\t\t\tborder-top-color: transparent;\n\t\t\t\t\tborder-bottom-color: $tooltips-color;\n\t\t\t\t}\n\t\n\t\t\t\t&:after {\n\t\t\t\t\ttop: 100%;\n\t\t\t\t\tbottom: unset;\n\t\t\t\t\tmargin-top: 10px;\n\t\t\t\t\tmargin-bottom: 0;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t// POSITION: Left\n\t\t\t&-left {\n\n\t\t\t\t&:after {\n\t\t\t\t\tleft: 0;\n\t\t\t\t\ttransform: translate(0);\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t// POSITION: Right\n\t\t\t&-right {\n\n\t\t\t\t&:after {\n\t\t\t\t\tleft: unset;\n\t\t\t\t\tright: 0;\n\t\t\t\t\ttransform: translate(0);\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t// POSITION: Left\n\t\t&.sui-tooltip-left {\n\n\t\t\t&:before {\n\t\t\t\ttop: 50%;\n\t\t\t\tright: 100%;\n\t\t\t\tbottom: unset;\n\t\t\t\tleft: unset;\n\t\t\t\tborder-top-color: transparent;\n\t\t\t\tborder-left-color: $tooltips-color;\n\t\t\t\ttransform: translateY(-50%);\n\t\t\t}\n\n\t\t\t&:after {\n\t\t\t\ttop: 50%;\n\t\t\t\tright: 100%;\n\t\t\t\tbottom: unset;\n\t\t\t\tleft: unset;\n\t\t\t\tmargin-right: 10px;\n\t\t\t\tmargin-bottom: 0;\n\t\t\t\ttransform: translateY(-50%);\n\t\t\t}\n\t\t}\n\n\t\t// POSITION: Right\n\t\t&.sui-tooltip-right {\n\n\t\t\t&:before {\n\t\t\t\ttop: 50%;\n\t\t\t\tbottom: unset;\n\t\t\t\tleft: 100%;\n\t\t\t\tborder-top-color: transparent;\n\t\t\t\tborder-right-color: $tooltips-color;\n\t\t\t\ttransform: translateY(-50%);\n\t\t\t}\n\n\t\t\t&:after {\n\t\t\t\ttop: 50%;\n\t\t\t\tbottom: unset;\n\t\t\t\tleft: 100%;\n\t\t\t\tmargin-left: 10px;\n\t\t\t\tmargin-bottom: 0;\n\t\t\t\ttransform: translateY(-50%);\n\t\t\t}\n\t\t}\n\n\t\t// VARIATION: Mobile\n\t\t&.sui-tooltip {\n\n\t\t\t// VARIATION: Constrained tooltip\n\t\t\t// Use this variation in case you need to keep tooltip position but\n\t\t\t// constrain its content.\n\t\t\t&-mobile {\n\n\t\t\t\t&:after {\n\n\t\t\t\t\t@include media(max-width, md) {\n\t\t\t\t\t\tmin-width: var(--tooltip-width-mobile, 120px);\n\t\t\t\t\t\twhite-space: normal;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t// POSITION: Top\n\t\t\t&-top {\n\n\t\t\t\t&-mobile,\n\t\t\t\t&-left-mobile,\n\t\t\t\t&-right-mobile {\n\n\t\t\t\t\t&:before {\n\n\t\t\t\t\t\t@include media(max-width, md) {\n\t\t\t\t\t\t\ttop: unset;\n\t\t\t\t\t\t\tbottom: 100%;\n\t\t\t\t\t\t\tleft: 50%;\n\t\t\t\t\t\t\tright: unset;\n\t\t\t\t\t\t\tborder-top-color: $tooltips-color;\n\t\t\t\t\t\t\tborder-right-color: transparent;\n\t\t\t\t\t\t\tborder-bottom-color: transparent;\n\t\t\t\t\t\t\tborder-left-color: transparent;\n\t\t\t\t\t\t\ttransform: translateX(-50%);\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\n\t\t\t\t\t&:after {\n\n\t\t\t\t\t\t@include media(max-width, md) {\n\t\t\t\t\t\t\tmin-width: var(--tooltip-width-mobile, 120px);\n\t\t\t\t\t\t\ttop: unset;\n\t\t\t\t\t\t\tbottom: 100%;\n\t\t\t\t\t\t\tmargin-top: 0;\n\t\t\t\t\t\t\tmargin-right: 0;\n\t\t\t\t\t\t\tmargin-bottom: 10px;\n\t\t\t\t\t\t\tmargin-left: 0;\n\t\t\t\t\t\t\twhite-space: normal;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t&-mobile:after {\n\n\t\t\t\t\t@include media(max-width, md) {\n\t\t\t\t\t\tright: unset;\n\t\t\t\t\t\tleft: 50%;\n\t\t\t\t\t\ttransform: translateX(-50%);\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t&-left-mobile:after {\n\n\t\t\t\t\t@include media(max-width, md) {\n\t\t\t\t\t\tright: unset;\n\t\t\t\t\t\tleft: 0;\n\t\t\t\t\t\ttransform: translateX(0);\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t&-right-mobile:after {\n\n\t\t\t\t\t@include media(max-width, md) {\n\t\t\t\t\t\tright: 0;\n\t\t\t\t\t\tleft: unset;\n\t\t\t\t\t\ttransform: translateX(0);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t// POSITION: Bottom\n\t\t\t&-bottom {\n\n\t\t\t\t&-mobile,\n\t\t\t\t&-left-mobile,\n\t\t\t\t&-right-mobile {\n\n\t\t\t\t\t&:before {\n\n\t\t\t\t\t\t@include media(max-width, md) {\n\t\t\t\t\t\t\ttop: 100%;\n\t\t\t\t\t\t\tbottom: unset;\n\t\t\t\t\t\t\tleft: 50%;\n\t\t\t\t\t\t\tright: unset;\n\t\t\t\t\t\t\tborder-top-color: transparent;\n\t\t\t\t\t\t\tborder-right-color: transparent;\n\t\t\t\t\t\t\tborder-bottom-color: $tooltips-color;\n\t\t\t\t\t\t\tborder-left-color: transparent;\n\t\t\t\t\t\t\ttransform: translateX(-50%);\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\n\t\t\t\t\t&:after {\n\n\t\t\t\t\t\t@include media(max-width, md) {\n\t\t\t\t\t\t\tmin-width: var(--tooltip-width-mobile, 120px);\n\t\t\t\t\t\t\ttop: 100%;\n\t\t\t\t\t\t\tbottom: unset;\n\t\t\t\t\t\t\tmargin-top: 10px;\n\t\t\t\t\t\t\tmargin-right: 0;\n\t\t\t\t\t\t\tmargin-bottom: 0;\n\t\t\t\t\t\t\tmargin-left: 0;\n\t\t\t\t\t\t\twhite-space: normal;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t&-mobile:after {\n\n\t\t\t\t\t@include media(max-width, md) {\n\t\t\t\t\t\tright: unset;\n\t\t\t\t\t\tleft: 50%;\n\t\t\t\t\t\ttransform: translateX(-50%);\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t&-left-mobile:after {\n\n\t\t\t\t\t@include media(max-width, md) {\n\t\t\t\t\t\tright: unset;\n\t\t\t\t\t\tleft: 0;\n\t\t\t\t\t\ttransform: translateX(0);\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t&-right-mobile:after {\n\n\t\t\t\t\t@include media(max-width, md) {\n\t\t\t\t\t\tright: 0;\n\t\t\t\t\t\tleft: unset;\n\t\t\t\t\t\ttransform: translateX(0);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t// POSITION: Left\n\t\t\t&-left-mobile {\n\n\t\t\t\t&:before {\n\n\t\t\t\t\t@include media(max-width, md) {\n\t\t\t\t\t\ttop: 50%;\n\t\t\t\t\t\tright: 100%;\n\t\t\t\t\t\tbottom: unset;\n\t\t\t\t\t\tleft: unset;\n\t\t\t\t\t\tborder-top-color: transparent;\n\t\t\t\t\t\tborder-right-color: transparent;\n\t\t\t\t\t\tborder-bottom-color: transparent;\n\t\t\t\t\t\tborder-left-color: $tooltips-color;\n\t\t\t\t\t\ttransform: translateY(-50%);\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t&:after {\n\n\t\t\t\t\t@include media(max-width, md) {\n\t\t\t\t\t\tmin-width: var(--tooltip-width-mobile, 120px);\n\t\t\t\t\t\ttop: 50%;\n\t\t\t\t\t\tright: 100%;\n\t\t\t\t\t\tbottom: unset;\n\t\t\t\t\t\tleft: unset;\n\t\t\t\t\t\tmargin-top: 0;\n\t\t\t\t\t\tmargin-right: 10px;\n\t\t\t\t\t\tmargin-bottom: 0;\n\t\t\t\t\t\tmargin-left: 0;\n\t\t\t\t\t\ttransform: translateY(-50%);\n\t\t\t\t\t\twhite-space: normal;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t// POSITION: Right\n\t\t\t&-right-mobile {\n\n\t\t\t\t&:before {\n\n\t\t\t\t\t@include media(max-width, md) {\n\t\t\t\t\t\ttop: 50%;\n\t\t\t\t\t\tright: unset;\n\t\t\t\t\t\tbottom: unset;\n\t\t\t\t\t\tleft: 100%;\n\t\t\t\t\t\tborder-top-color: transparent;\n\t\t\t\t\t\tborder-right-color: $tooltips-color;\n\t\t\t\t\t\tborder-bottom-color: transparent;\n\t\t\t\t\t\tborder-left-color: transparent;\n\t\t\t\t\t\ttransform: translateY(-50%);\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t&:after {\n\n\t\t\t\t\t@include media(max-width, md) {\n\t\t\t\t\t\tmin-width: var(--tooltip-width-mobile, 120px);\n\t\t\t\t\t\ttop: 50%;\n\t\t\t\t\t\tright: unset;\n\t\t\t\t\t\tbottom: unset;\n\t\t\t\t\t\tleft: 100%;\n\t\t\t\t\t\tmargin-top: 0;\n\t\t\t\t\t\tmargin-right: 0;\n\t\t\t\t\t\tmargin-bottom: 0;\n\t\t\t\t\t\tmargin-left: 10px;\n\t\t\t\t\t\ttransform: translateY(-50%);\n\t\t\t\t\t\twhite-space: normal;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t// STATE: Hover\n\t\t// Show tooltips when user hovers on it.\n\t\t&:hover,\n\t\t&:focus {\n\n\t\t\t&:before,\n\t\t\t&:after {\n\t\t\t\topacity: 1;\n\t\t\t\tbackface-visibility: visible;\n\t\t\t}\n\t\t}\n\t}\n}", "@use \"sass:math\";\n\n// ELEMENT: Select.\n// $sui-wrap: true | $rtl: false\n@include body-class(true, false) {\n\tselect.sui-select {\n\t\t+ .sui-select {\n\t\t\tmin-width: 100%;\n\t\t\tmax-width: 100%;\n\t\t\tdisplay: block;\n\t\t\ttext-align: left;\n\n\t\t\t// STATE: Default.\n\t\t\t.selection {\n\t\t\t\tdisplay: block;\n\n\t\t\t\tspan[role=\"combobox\"] {\n\t\t\t\t\t// TYPE: Single Select.\n\t\t\t\t\t&.select2-selection--single {\n\t\t\t\t\t\theight: auto;\n\t\t\t\t\t\tcursor: pointer;\n\t\t\t\t\t\tuser-select: none;\n\t\t\t\t\t\tdisplay: flex;\n\t\t\t\t\t\tflex-flow: row nowrap;\n\t\t\t\t\t\talign-items: center;\n\t\t\t\t\t\tposition: relative;\n\t\t\t\t\t\tmargin: 0;\n\t\t\t\t\t\tpadding: 0;\n\t\t\t\t\t\tborder: 1px solid palette(gray, lighter);\n\t\t\t\t\t\tborder-radius: $border-radius;\n\t\t\t\t\t\tbackground-color: #fafafa;\n\t\t\t\t\t\ttransition: $transition;\n\n\t\t\t\t\t\t// Label.\n\t\t\t\t\t\t.select2-selection__rendered {\n\t\t\t\t\t\t\tmin-width: 10px;\n\t\t\t\t\t\t\toverflow: hidden;\n\t\t\t\t\t\t\tdisplay: block;\n\t\t\t\t\t\t\tflex: 1;\n\t\t\t\t\t\t\tpadding: 8px 5px 8px 14px;\n\t\t\t\t\t\t\tcolor: $nightrider;\n\t\t\t\t\t\t\tfont: 500 15px/22px $font;\n\t\t\t\t\t\t\tletter-spacing: -0.2px;\n\t\t\t\t\t\t\ttext-overflow: ellipsis;\n\t\t\t\t\t\t\twhite-space: nowrap;\n\n\t\t\t\t\t\t\t.select2-selection__placeholder {\n\t\t\t\t\t\t\t\tcolor: $fiftyshades;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t// Icon.\n\t\t\t\t\t\t.select2-selection__arrow {\n\t\t\t\t\t\t\twidth: 38px;\n\t\t\t\t\t\t\theight: 38px;\n\t\t\t\t\t\t\tdisplay: flex;\n\t\t\t\t\t\t\tflex-flow: row wrap;\n\t\t\t\t\t\t\talign-items: center;\n\t\t\t\t\t\t\tflex: 0 0 auto;\n\t\t\t\t\t\t\tposition: relative;\n\t\t\t\t\t\t\tmargin: 0;\n\t\t\t\t\t\t\tpadding: 0;\n\t\t\t\t\t\t\tborder: 0;\n\t\t\t\t\t\t\tcolor: $grey;\n\n\t\t\t\t\t\t\t[class*=\"sui-icon-\"] {\n\t\t\t\t\t\t\t\tdisplay: block;\n\t\t\t\t\t\t\t\tmargin: 0 auto;\n\n\t\t\t\t\t\t\t\t&:before {\n\t\t\t\t\t\t\t\t\tcolor: inherit;\n\t\t\t\t\t\t\t\t\tdisplay: block;\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\n\t\t\t\t\t// TYPE: Multi Select.\n\t\t\t\t\t&.select2-selection--multiple {\n\t\t\t\t\t\tdisplay: flex;\n\t\t\t\t\t\tflex-flow: row wrap;\n\t\t\t\t\t\talign-items: flex-start;\n\t\t\t\t\t\tmargin: 0;\n\t\t\t\t\t\tpadding: 3px 9px;\n\t\t\t\t\t\tborder: 1px solid $overcast;\n\t\t\t\t\t\tborder-radius: $border-radius;\n\t\t\t\t\t\tbackground-color: $cloud;\n\n\t\t\t\t\t\t// Options.\n\t\t\t\t\t\t.select2-selection__rendered {\n\t\t\t\t\t\t\tmin-width: 1px;\n\t\t\t\t\t\t\tdisplay: flex;\n\t\t\t\t\t\t\tflex: 0 1 auto;\n\t\t\t\t\t\t\tflex-flow: row wrap;\n\t\t\t\t\t\t\talign-items: center;\n\t\t\t\t\t\t\tmargin: 0;\n\t\t\t\t\t\t\tpadding: 0;\n\t\t\t\t\t\t\tborder: 0;\n\n\t\t\t\t\t\t\t.select2-selection__choice {\n\t\t\t\t\t\t\t\tmax-width: 200px;\n\t\t\t\t\t\t\t\t// overflow: hidden;\n\t\t\t\t\t\t\t\tdisplay: flex;\n\t\t\t\t\t\t\t\tflex-flow: row nowrap;\n\t\t\t\t\t\t\t\talign-items: center;\n\t\t\t\t\t\t\t\tmargin: 2px;\n\t\t\t\t\t\t\t\tpadding: 0;\n\t\t\t\t\t\t\t\tborder: 0;\n\t\t\t\t\t\t\t\tborder-radius: $border-radius;\n\t\t\t\t\t\t\t\tbackground-color: $ironmike;\n\t\t\t\t\t\t\t\tcolor: $white;\n\t\t\t\t\t\t\t\tfont: 500 12px/16px $font;\n\t\t\t\t\t\t\t\tletter-spacing: $font--letter-spacing;\n\n\t\t\t\t\t\t\t\t.select2-selection__choice__display {\n\t\t\t\t\t\t\t\t\tmin-width: 1px;\n\t\t\t\t\t\t\t\t\toverflow: hidden;\n\t\t\t\t\t\t\t\t\tdisplay: block;\n\t\t\t\t\t\t\t\t\tflex: 1;\n\t\t\t\t\t\t\t\t\tpadding: 5px 10px;\n\t\t\t\t\t\t\t\t\tpadding-left: 5px;\n\t\t\t\t\t\t\t\t\ttext-overflow: ellipsis;\n\t\t\t\t\t\t\t\t\twhite-space: nowrap;\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t// Search input.\n\t\t\t\t\t\t.select2-search {\n\t\t\t\t\t\t\t&.select2-search--inline {\n\t\t\t\t\t\t\t\tmin-width: 100px;\n\t\t\t\t\t\t\t\tflex: 1;\n\t\t\t\t\t\t\t\tmargin: 5px;\n\t\t\t\t\t\t\t\tpadding: 0;\n\t\t\t\t\t\t\t\tborder: 0;\n\t\t\t\t\t\t\t\tdisplay: block;\n\n\t\t\t\t\t\t\t\t.select2-search__field {\n\t\t\t\t\t\t\t\t\twidth: 100% !important;\n\t\t\t\t\t\t\t\t\theight: 22px;\n\t\t\t\t\t\t\t\t\tresize: none;\n\t\t\t\t\t\t\t\t\tdisplay: block;\n\t\t\t\t\t\t\t\t\tmargin: 0;\n\t\t\t\t\t\t\t\t\tpadding: 0;\n\t\t\t\t\t\t\t\t\tborder: 0;\n\t\t\t\t\t\t\t\t\tborder-radius: 0;\n\t\t\t\t\t\t\t\t\tbackground-color: transparent;\n\t\t\t\t\t\t\t\t\tcolor: $nightrider;\n\t\t\t\t\t\t\t\t\tfont: 500 15px/22px $font;\n\t\t\t\t\t\t\t\t\tletter-spacing: $font--letter-spacing;\n\n\t\t\t\t\t\t\t\t\t&::placeholder {\n\t\t\t\t\t\t\t\t\t\tcolor: $fiftyshades;\n\t\t\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t\t\t&:-moz-placeholder {\n\t\t\t\t\t\t\t\t\t\tcolor: $fiftyshades;\n\t\t\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t\t\t&::-moz-placeholder {\n\t\t\t\t\t\t\t\t\t\tcolor: $fiftyshades;\n\t\t\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t\t\t&:-ms-input-placeholder {\n\t\t\t\t\t\t\t\t\t\tcolor: $fiftyshades;\n\t\t\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t\t\t&::-webkit-input-placeholder {\n\t\t\t\t\t\t\t\t\t\tcolor: $fiftyshades;\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t&:hover {\n\t\t\t\t\tspan[role=\"combobox\"] {\n\t\t\t\t\t\t// TYPE: Single Select.\n\t\t\t\t\t\t&.select2-selection--single {\n\t\t\t\t\t\t\tbackground-color: $white;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t&:not(.sui-select-theme--search) .selection {\n\t\t\t\tspan[role=\"combobox\"] {\n\t\t\t\t\t// TYPE: Multi Select.\n\t\t\t\t\t&.select2-selection--multiple {\n\t\t\t\t\t\t// Options.\n\t\t\t\t\t\t.select2-selection__rendered {\n\t\t\t\t\t\t\t.select2-selection__choice {\n\t\t\t\t\t\t\t\t.sui-button-icon {\n\t\t\t\t\t\t\t\t\twidth: 26px;\n\t\t\t\t\t\t\t\t\theight: 26px;\n\t\t\t\t\t\t\t\t\tcursor: pointer;\n\t\t\t\t\t\t\t\t\tdisplay: flex;\n\t\t\t\t\t\t\t\t\tflex-flow: column nowrap;\n\t\t\t\t\t\t\t\t\talign-items: center;\n\t\t\t\t\t\t\t\t\tjustify-content: center;\n\t\t\t\t\t\t\t\t\tmargin: 0;\n\t\t\t\t\t\t\t\t\tpadding: 0;\n\t\t\t\t\t\t\t\t\tborder: 0;\n\t\t\t\t\t\t\t\t\tborder-top-left-radius: 0;\n\t\t\t\t\t\t\t\t\tborder-bottom-left-radius: 0;\n\t\t\t\t\t\t\t\t\tbackground-color: #5e5e5e;\n\t\t\t\t\t\t\t\t\tcolor: $overcast;\n\t\t\t\t\t\t\t\t\ttext-align: center;\n\n\t\t\t\t\t\t\t\t\t&:focus {\n\t\t\t\t\t\t\t\t\t\tbox-shadow: none;\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t// DESIGN: Icon.\n\t\t\t&.sui-select-theme--icon {\n\t\t\t\t.selection {\n\t\t\t\t\tspan[role=\"combobox\"] {\n\t\t\t\t\t\t// TYPE: Single Select.\n\t\t\t\t\t\t&.select2-selection--single {\n\t\t\t\t\t\t\t// Label.\n\t\t\t\t\t\t\t.select2-selection__rendered {\n\t\t\t\t\t\t\t\tposition: relative;\n\t\t\t\t\t\t\t\tpadding-left: 46px;\n\n\t\t\t\t\t\t\t\t[class*=\"sui-icon-\"] {\n\t\t\t\t\t\t\t\t\twidth: 30px;\n\t\t\t\t\t\t\t\t\theight: 30px;\n\t\t\t\t\t\t\t\t\tdisplay: flex;\n\t\t\t\t\t\t\t\t\tflex-flow: row wrap;\n\t\t\t\t\t\t\t\t\talign-items: center;\n\t\t\t\t\t\t\t\t\tjustify-content: center;\n\t\t\t\t\t\t\t\t\tposition: absolute;\n\t\t\t\t\t\t\t\t\ttop: 50%;\n\t\t\t\t\t\t\t\t\tleft: 10px;\n\t\t\t\t\t\t\t\t\ttransform: translateY(-50%);\n\t\t\t\t\t\t\t\t\ttext-align: center;\n\n\t\t\t\t\t\t\t\t\t&:before {\n\t\t\t\t\t\t\t\t\t\tdisplay: block;\n\t\t\t\t\t\t\t\t\t\tfont-size: 16px;\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t\t.select2-selection__placeholder {\n\t\t\t\t\t\t\t\t\tmargin-left: -32px;\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t// DESIGN: Color.\n\t\t\t&.sui-select-theme--color {\n\t\t\t\t.selection {\n\t\t\t\t\tspan[role=\"combobox\"] {\n\t\t\t\t\t\t// TYPE: Single Select.\n\t\t\t\t\t\t&.select2-selection--single {\n\t\t\t\t\t\t\t// Label.\n\t\t\t\t\t\t\t.select2-selection__rendered {\n\t\t\t\t\t\t\t\tposition: relative;\n\t\t\t\t\t\t\t\tpadding-left: 45px;\n\n\t\t\t\t\t\t\t\t.sui-color {\n\t\t\t\t\t\t\t\t\twidth: 30px;\n\t\t\t\t\t\t\t\t\theight: 30px;\n\t\t\t\t\t\t\t\t\tposition: absolute;\n\t\t\t\t\t\t\t\t\ttop: 50%;\n\t\t\t\t\t\t\t\t\tleft: 5px;\n\t\t\t\t\t\t\t\t\tborder-width: 1px;\n\t\t\t\t\t\t\t\t\tborder-style: solid;\n\t\t\t\t\t\t\t\t\tborder-radius: math.div($border-radius, 2);\n\t\t\t\t\t\t\t\t\ttransform: translateY(-50%);\n\t\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t\t.select2-selection__placeholder {\n\t\t\t\t\t\t\t\t\tmargin-left: -31px;\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t// DESIGN: Smart Search.\n\t\t\t&.sui-select-theme--search {\n\t\t\t\t.selection {\n\t\t\t\t\tspan[role=\"combobox\"] {\n\t\t\t\t\t\t// TYPE: Multi Select.\n\t\t\t\t\t\t&.select2-selection--multiple {\n\t\t\t\t\t\t\tposition: relative;\n\n\t\t\t\t\t\t\t@include icon(before, magnifying-glass-search, true) {\n\t\t\t\t\t\t\t\twidth: 30px;\n\t\t\t\t\t\t\t\tposition: absolute;\n\t\t\t\t\t\t\t\ttop: 50%;\n\t\t\t\t\t\t\t\tleft: 9px;\n\t\t\t\t\t\t\t\ttransform: translateY(-50%);\n\t\t\t\t\t\t\t\ttext-align: center;\n\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t.select2-selection__rendered {\n\t\t\t\t\t\t\t\tpadding-left: 40px;\n\n\t\t\t\t\t\t\t\t.select2-selection__choice {\n\t\t\t\t\t\t\t\t\tmin-width: 1px;\n\t\t\t\t\t\t\t\t\tmax-width: none;\n\t\t\t\t\t\t\t\t\tdisplay: flex;\n\t\t\t\t\t\t\t\t\tflex-flow: row nowrap;\n\t\t\t\t\t\t\t\t\tflex: 1;\n\t\t\t\t\t\t\t\t\tmargin: 0;\n\t\t\t\t\t\t\t\t\tpadding: 0;\n\t\t\t\t\t\t\t\t\tbackground-color: transparent;\n\t\t\t\t\t\t\t\t\tborder-radius: 0;\n\t\t\t\t\t\t\t\t\tcolor: $nightrider;\n\t\t\t\t\t\t\t\t\tfont: 500 15px/22px $font;\n\t\t\t\t\t\t\t\t\tletter-spacing: $font--letter-spacing;\n\t\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t\t&.has-option-selected {\n\t\t\t\t\t\t\t\t\twidth: 100%;\n\n\t\t\t\t\t\t\t\t\t+ .select2-search {\n\t\t\t\t\t\t\t\t\t\tdisplay: none;\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t// STATE: Hover\n\t\t\t\t&:hover {\n\t\t\t\t\t.selection {\n\t\t\t\t\t\tspan[role=\"combobox\"] {\n\t\t\t\t\t\t\tbackground: $white;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t// STATE: Focus\n\t\t\t\t&.select2-container--focus {\n\t\t\t\t\t.selection {\n\t\t\t\t\t\tspan[role=\"combobox\"] {\n\t\t\t\t\t\t\tborder-color: $blue;\n\t\t\t\t\t\t\tbackground: $white;\n\t\t\t\t\t\t\tbox-shadow: 0 0 0 2px $blue-ghost;\n\n\t\t\t\t\t\t\t.select2-search {\n\t\t\t\t\t\t\t\ttextarea {\n\t\t\t\t\t\t\t\t\t&:focus {\n\t\t\t\t\t\t\t\t\t\toutline: none;\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\n\t\t\t\t\t&.select2-container--open {\n\t\t\t\t\t\t&.sui-select-dropdown-container--above {\n\t\t\t\t\t\t\t.selection {\n\t\t\t\t\t\t\t\tspan[role=\"combobox\"] {\n\t\t\t\t\t\t\t\t\tborder-top-color: $overcast;\n\t\t\t\t\t\t\t\t\tborder-top-left-radius: 0;\n\t\t\t\t\t\t\t\t\tborder-top-right-radius: 0;\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t&.sui-select-dropdown-container--below {\n\t\t\t\t\t\t\t.selection {\n\t\t\t\t\t\t\t\tspan[role=\"combobox\"] {\n\t\t\t\t\t\t\t\t\tborder-bottom-color: $overcast;\n\t\t\t\t\t\t\t\t\tborder-bottom-left-radius: 0;\n\t\t\t\t\t\t\t\t\tborder-bottom-right-radius: 0;\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t// STATE: Focus.\n\t\t\t&.select2-container--focus {\n\t\t\t\t.selection {\n\t\t\t\t\tspan[role=\"combobox\"] {\n\t\t\t\t\t\t// TYPE: Single Select.\n\t\t\t\t\t\t&.select2-selection--single {\n\t\t\t\t\t\t\tbackground-color: $white;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t// STATE: Dropdown open.\n\t\t\t&.select2-container--open {\n\t\t\t\t.selection {\n\t\t\t\t\tspan[role=\"combobox\"] {\n\t\t\t\t\t\t// TYPE: Single Select.\n\t\t\t\t\t\t&.select2-selection--single {\n\t\t\t\t\t\t\t// Icon.\n\t\t\t\t\t\t\t.select2-selection__arrow {\n\t\t\t\t\t\t\t\ttransform: rotate(180deg);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t// STATE: Disabled.\n\t\t\t&.select2-container--disabled {\n\t\t\t\t.selection {\n\t\t\t\t\tcursor: not-allowed;\n\n\t\t\t\t\tspan[role=\"combobox\"] {\n\t\t\t\t\t\t// TYPE: Single Select.\n\t\t\t\t\t\t&.select2-selection--single {\n\t\t\t\t\t\t\tpointer-events: none;\n\t\t\t\t\t\t\tborder-color: palette(silver, default);\n\t\t\t\t\t\t\tbackground-color: palette(silver, default);\n\n\t\t\t\t\t\t\t// Label.\n\t\t\t\t\t\t\t.select2-selection__rendered {\n\t\t\t\t\t\t\t\tcolor: $fiftyshades;\n\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t// Icon.\n\t\t\t\t\t\t\t.select2-selection__arrow {\n\t\t\t\t\t\t\t\tcolor: $fiftyshades;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t// TYPE: Multi Select.\n\t\t\t\t\t\t&.select2-selection--multiple {\n\t\t\t\t\t\t\tpointer-events: none;\n\n\t\t\t\t\t\t\t.select2-selection__rendered {\n\t\t\t\t\t\t\t\tborder-color: palette(silver, default);\n\t\t\t\t\t\t\t\tbackground-color: palette(silver, default);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t// SIZE: Small height.\n\t\t&.sui-select-sm {\n\t\t\t+ .sui-select {\n\t\t\t\t// STATE: Default.\n\t\t\t\t.selection {\n\t\t\t\t\tspan[role=\"combobox\"] {\n\t\t\t\t\t\t// TYPE: Single Select.\n\t\t\t\t\t\t&.select2-selection--single {\n\t\t\t\t\t\t\t// Label.\n\t\t\t\t\t\t\t.select2-selection__rendered {\n\t\t\t\t\t\t\t\tpadding: 6px 6px 6px 12px;\n\t\t\t\t\t\t\t\tfont-size: 13px;\n\t\t\t\t\t\t\t\tline-height: 16px;\n\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t// Icon.\n\t\t\t\t\t\t\t.select2-selection__arrow {\n\t\t\t\t\t\t\t\twidth: 28px;\n\t\t\t\t\t\t\t\theight: 28px;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t// DESIGN: Icon.\n\t\t\t\t&.sui-select-theme--icon {\n\t\t\t\t\t.selection {\n\t\t\t\t\t\tspan[role=\"combobox\"] {\n\t\t\t\t\t\t\t// TYPE: Single Select.\n\t\t\t\t\t\t\t&.select2-selection--single {\n\t\t\t\t\t\t\t\t// Label.\n\t\t\t\t\t\t\t\t.select2-selection__rendered {\n\t\t\t\t\t\t\t\t\tpadding-left: 36px;\n\n\t\t\t\t\t\t\t\t\t[class*=\"sui-icon-\"] {\n\t\t\t\t\t\t\t\t\t\tleft: 5px;\n\n\t\t\t\t\t\t\t\t\t\t&:before {\n\t\t\t\t\t\t\t\t\t\t\tfont-size: 12px;\n\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t\t.select2-selection__placeholder {\n\t\t\t\t\t\t\t\t\tmargin-left: -24px;\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t// DESIGN: Color.\n\t\t\t\t&.sui-select-theme--color {\n\t\t\t\t\t.selection {\n\t\t\t\t\t\tspan[role=\"combobox\"] {\n\t\t\t\t\t\t\t// TYPE: Single Select.\n\t\t\t\t\t\t\t&.select2-selection--single {\n\t\t\t\t\t\t\t\t// Label.\n\t\t\t\t\t\t\t\t.select2-selection__rendered {\n\t\t\t\t\t\t\t\t\tpadding-left: 40px;\n\n\t\t\t\t\t\t\t\t\t.sui-color {\n\t\t\t\t\t\t\t\t\t\twidth: 20px;\n\t\t\t\t\t\t\t\t\t\theight: 20px;\n\t\t\t\t\t\t\t\t\t\tleft: 10px;\n\t\t\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t\t\t.select2-selection__placeholder {\n\t\t\t\t\t\t\t\t\t\tmargin-left: -28px;\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t// SIZE: Auto width.\n\t\t&[data-width] {\n\t\t\t+ .sui-select {\n\t\t\t\tmin-width: auto;\n\t\t\t}\n\t\t}\n\n\t\t// DESIGN: Inline select.\n\t\t&.sui-select-inline {\n\t\t\t+ .sui-select {\n\t\t\t\tmin-width: 1px;\n\t\t\t\tdisplay: inline-block;\n\t\t\t\tmargin-right: 10px;\n\n\t\t\t\t&:last-child {\n\t\t\t\t\tmargin-right: 0;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\n\t// STATE: Error.\n\t.sui-form-field {\n\t\t&.sui-form-field-error {\n\t\t\tselect.sui-select {\n\t\t\t\t+ .sui-select {\n\t\t\t\t\t// STATE: Default.\n\t\t\t\t\t.selection {\n\t\t\t\t\t\tspan[role=\"combobox\"] {\n\t\t\t\t\t\t\t// TYPE: Single Select.\n\t\t\t\t\t\t\t&.select2-selection--single {\n\t\t\t\t\t\t\t\tborder-bottom-color: $red;\n\t\t\t\t\t\t\t\tbox-shadow: 0 1px 0 0 $red;\n\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t// TYPE: Multi Select.\n\t\t\t\t\t\t\t&.select2-selection--multiple {\n\t\t\t\t\t\t\t\t.select2-selection__rendered {\n\t\t\t\t\t\t\t\t\tborder-bottom-color: $red;\n\t\t\t\t\t\t\t\t\tbox-shadow: 0 1px 0 0 $red;\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n}\n\n// $sui-wrap: true | $rtl: true\n@include body-class(true, true) {\n\tselect.sui-select {\n\t\t+ .sui-select {\n\t\t\tdirection: rtl;\n\t\t\ttext-align: right;\n\n\t\t\t.selection {\n\t\t\t\tspan[role=\"combobox\"] {\n\t\t\t\t\t// TYPE: Single Select.\n\t\t\t\t\t&.select2-selection--single {\n\t\t\t\t\t\t// Label.\n\t\t\t\t\t\t.select2-selection__rendered {\n\t\t\t\t\t\t\tpadding-right: 14px;\n\t\t\t\t\t\t\tpadding-left: 5px;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t// DESIGN: Icon.\n\t\t\t&.sui-select-theme--icon {\n\t\t\t\t.selection {\n\t\t\t\t\tspan[role=\"combobox\"] {\n\t\t\t\t\t\t// TYPE: Single Select.\n\t\t\t\t\t\t&.select2-selection--single {\n\t\t\t\t\t\t\t// Label.\n\t\t\t\t\t\t\t.select2-selection__rendered {\n\t\t\t\t\t\t\t\tpadding-left: 5px;\n\t\t\t\t\t\t\t\tpadding-right: 46px;\n\n\t\t\t\t\t\t\t\t[class*=\"sui-icon-\"] {\n\t\t\t\t\t\t\t\t\tleft: auto;\n\t\t\t\t\t\t\t\t\tright: 10px;\n\t\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t\t.select2-selection__placeholder {\n\t\t\t\t\t\t\t\t\tmargin-left: auto;\n\t\t\t\t\t\t\t\t\tmargin-right: -32px;\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n}\n\n// $sui-wrap: true | $rtl: false | $monochrome: true\n@include body-class(true, false, true) {\n\tselect.sui-select {\n\t\t+ .sui-select {\n\t\t\t// STATE: Default.\n\t\t\t.selection {\n\t\t\t\tspan[role=\"combobox\"] {\n\t\t\t\t\t// TYPE: Single Select.\n\t\t\t\t\t&.select2-selection--single {\n\t\t\t\t\t\tborder-color: $black;\n\t\t\t\t\t\tbackground-color: $white;\n\n\t\t\t\t\t\t// Label.\n\t\t\t\t\t\t.select2-selection__rendered {\n\t\t\t\t\t\t\tcolor: $black;\n\n\t\t\t\t\t\t\t.select2-selection__placeholder {\n\t\t\t\t\t\t\t\tcolor: $black;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t// Icon.\n\t\t\t\t\t\t.select2-selection__arrow {\n\t\t\t\t\t\t\tcolor: $black;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\n\t\t\t\t\t// TYPE: Multi Select.\n\t\t\t\t\t&.select2-selection--multiple {\n\t\t\t\t\t\t// Options.\n\t\t\t\t\t\t.select2-selection__rendered {\n\t\t\t\t\t\t\tborder-color: $black;\n\t\t\t\t\t\t\tbackground-color: $white;\n\n\t\t\t\t\t\t\t.select2-search {\n\t\t\t\t\t\t\t\tinput {\n\t\t\t\t\t\t\t\t\tcolor: $black;\n\n\t\t\t\t\t\t\t\t\t&::placeholder {\n\t\t\t\t\t\t\t\t\t\tcolor: $black;\n\t\t\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t\t\t&:-moz-placeholder {\n\t\t\t\t\t\t\t\t\t\tcolor: $black;\n\t\t\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t\t\t&::-moz-placeholder {\n\t\t\t\t\t\t\t\t\t\tcolor: $black;\n\t\t\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t\t\t&:-ms-input-placeholder {\n\t\t\t\t\t\t\t\t\t\tcolor: $black;\n\t\t\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t\t\t&::-webkit-input-placeholder {\n\t\t\t\t\t\t\t\t\t\tcolor: $black;\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t.select2-selection__choice {\n\t\t\t\t\t\t\t\tbackground-color: $black;\n\n\t\t\t\t\t\t\t\t.sui-button-icon {\n\t\t\t\t\t\t\t\t\tbackground-color: $black;\n\t\t\t\t\t\t\t\t\tcolor: $white;\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t// DESIGN: Smart Search.\n\t\t\t&.sui-select-theme--search {\n\t\t\t\t.selection {\n\t\t\t\t\tspan[role=\"combobox\"] {\n\t\t\t\t\t\t// TYPE: Multi Select.\n\t\t\t\t\t\t&.select2-selection--multiple {\n\t\t\t\t\t\t\t.select2-selection__rendered {\n\t\t\t\t\t\t\t\t.select2-selection__choice {\n\t\t\t\t\t\t\t\t\tcolor: $black;\n\n\t\t\t\t\t\t\t\t\t.sui-button-icon {\n\t\t\t\t\t\t\t\t\t\tcolor: $black;\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t// STATE: Disabled.\n\t\t\t&.select2-container--disabled {\n\t\t\t\t.selection {\n\t\t\t\t\tspan[role=\"combobox\"] {\n\t\t\t\t\t\t// TYPE: Single Select.\n\t\t\t\t\t\t&.select2-selection--single {\n\t\t\t\t\t\t\tborder-color: $grey;\n\t\t\t\t\t\t\tbackground-color: $white;\n\n\t\t\t\t\t\t\t// Label.\n\t\t\t\t\t\t\t.select2-selection__rendered {\n\t\t\t\t\t\t\t\tcolor: $grey;\n\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t// Icon.\n\t\t\t\t\t\t\t.select2-selection__arrow {\n\t\t\t\t\t\t\t\tcolor: $grey;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t// TYPE: Multi Select.\n\t\t\t\t\t\t&.select2-selection--multiple {\n\t\t\t\t\t\t\t.select2-selection__rendered {\n\t\t\t\t\t\t\t\tborder-color: $grey;\n\t\t\t\t\t\t\t\tbackground-color: $grey;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\n\t// STATE: Error.\n\t.sui-form-field {\n\t\t&.sui-form-field-error {\n\t\t\tselect.sui-select {\n\t\t\t\t+ .sui-select {\n\t\t\t\t\t// STATE: Default.\n\t\t\t\t\t.selection {\n\t\t\t\t\t\tspan[role=\"combobox\"] {\n\t\t\t\t\t\t\t// TYPE: Single Select.\n\t\t\t\t\t\t\t&.select2-selection--single {\n\t\t\t\t\t\t\t\tborder-bottom-color: $black;\n\t\t\t\t\t\t\t\tbox-shadow: 0 1px 0 0 $black;\n\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t// TYPE: Multi Select.\n\t\t\t\t\t\t\t&.select2-selection--multiple {\n\t\t\t\t\t\t\t\t.select2-selection__rendered {\n\t\t\t\t\t\t\t\t\tborder-bottom-color: $black;\n\t\t\t\t\t\t\t\t\tbox-shadow: 0 1px 0 0 $black;\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n}\n\n// ELEMENT: Dropdown.\n// $sui-wrap: false | $rtl: false\n@include body-class(false, false) {\n\t.sui-select {\n\t\t&.sui-select-dropdown-container--open {\n\t\t\tdisplay: block;\n\n\t\t\t* {\n\t\t\t\tbox-sizing: border-box;\n\t\t\t\tfont-variant-ligatures: none;\n\t\t\t\t-webkit-font-variant-ligatures: none;\n\t\t\t\ttext-rendering: optimizeLegibility;\n\t\t\t\t-moz-osx-font-smoothing: grayscale;\n\t\t\t\tfont-smoothing: antialiased;\n\t\t\t\t-webkit-font-smoothing: antialiased;\n\t\t\t\ttext-shadow: rgba(0, 0, 0, 0.01) 0 0 1px;\n\t\t\t}\n\n\t\t\t.sui-select-dropdown {\n\t\t\t\tdisplay: block;\n\t\t\t\tmargin: 0;\n\t\t\t\tpadding: 10px 0;\n\t\t\t\tborder: 1px solid palette(gray, lighter);\n\t\t\t\tborder-radius: $border-radius;\n\t\t\t\tbackground-color: $white;\n\t\t\t\tbox-shadow: 0 3px 7px 0 rgba(0, 0, 0, 0.07);\n\n\t\t\t\t// Search input.\n\t\t\t\t.select2-search {\n\t\t\t\t\tdisplay: block;\n\t\t\t\t\tmargin: 4px 0 14px;\n\t\t\t\t\tpadding: 0 14px;\n\n\t\t\t\t\tinput {\n\t\t\t\t\t\twidth: 100%;\n\t\t\t\t\t\tmax-width: 100%;\n\t\t\t\t\t\tdisplay: block;\n\t\t\t\t\t\tmargin: 0;\n\t\t\t\t\t\tpadding: 9px 14px;\n\t\t\t\t\t\tborder: 1px solid palette(gray, lighter);\n\t\t\t\t\t\tborder-radius: $border-radius;\n\t\t\t\t\t\tcolor: $nightrider;\n\t\t\t\t\t\tfont: 500 15px/20px $font;\n\t\t\t\t\t\tletter-spacing: -0.2px;\n\n\t\t\t\t\t\t&::placeholder {\n\t\t\t\t\t\t\tcolor: $fiftyshades;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t&:-moz-placeholder {\n\t\t\t\t\t\t\tcolor: $fiftyshades;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t&::-moz-placeholder {\n\t\t\t\t\t\t\tcolor: $fiftyshades;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t&:-ms-input-placeholder {\n\t\t\t\t\t\t\tcolor: $fiftyshades;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t&::-webkit-input-placeholder {\n\t\t\t\t\t\t\tcolor: $fiftyshades;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t&:hover,\n\t\t\t\t\t\t&:focus {\n\t\t\t\t\t\t\tborder-color: palette(gray, lighter);\n\t\t\t\t\t\t\toutline: none;\n\t\t\t\t\t\t\tbox-shadow: none;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\n\t\t\t\t\t&.select2-search--hide {\n\t\t\t\t\t\tdisplay: none !important;\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t// Dropdown list.\n\t\t\t\t.select2-results {\n\t\t\t\t\tdisplay: block;\n\t\t\t\t\tmargin: 0;\n\t\t\t\t\tpadding: 0;\n\t\t\t\t\tborder: 0;\n\n\t\t\t\t\t.select2-results__options {\n\t\t\t\t\t\tmax-height: 200px;\n\t\t\t\t\t\toverflow-y: auto;\n\t\t\t\t\t\tdisplay: block;\n\t\t\t\t\t\tmargin: 0;\n\t\t\t\t\t\tpadding: 0;\n\t\t\t\t\t\tborder: 0;\n\t\t\t\t\t\tlist-style: none;\n\t\t\t\t\t\tpadding-inline-start: 0;\n\n\t\t\t\t\t\t.select2-results__option {\n\t\t\t\t\t\t\tuser-select: none;\n\t\t\t\t\t\t\tdisplay: block;\n\t\t\t\t\t\t\tmargin: 0;\n\t\t\t\t\t\t\tpadding: 0 14px;\n\t\t\t\t\t\t\tborder: 0;\n\t\t\t\t\t\t\tbackground-color: transparent;\n\t\t\t\t\t\t\tcolor: $grey;\n\t\t\t\t\t\t\tfont: 500 15px/35px $font;\n\t\t\t\t\t\t\tletter-spacing: -0.2px;\n\t\t\t\t\t\t\tlist-style: none;\n\t\t\t\t\t\t\ttransition: $transition;\n\n\t\t\t\t\t\t\t&.select2-results__message {\n\t\t\t\t\t\t\t\tposition: relative;\n\t\t\t\t\t\t\t\tmargin: 0 15px;\n\t\t\t\t\t\t\t\tpadding: 15px 20px;\n\t\t\t\t\t\t\t\tpadding-left: 46px;\n\t\t\t\t\t\t\t\tborder: 0;\n\t\t\t\t\t\t\t\tborder-radius: $border-radius;\n\t\t\t\t\t\t\t\tbackground-color: palette(silver, default);\n\t\t\t\t\t\t\t\tcolor: $nightrider;\n\t\t\t\t\t\t\t\tfont: 400 13px/22px $font;\n\t\t\t\t\t\t\t\tletter-spacing: $font--letter-spacing;\n\n\t\t\t\t\t\t\t\t@include icon(before, profile-male, true) {\n\t\t\t\t\t\t\t\t\twidth: 30px;\n\t\t\t\t\t\t\t\t\tposition: absolute;\n\t\t\t\t\t\t\t\t\ttop: 50%;\n\t\t\t\t\t\t\t\t\tleft: 10px;\n\t\t\t\t\t\t\t\t\ttransform: translateY(-50%);\n\t\t\t\t\t\t\t\t\tcolor: $grey;\n\t\t\t\t\t\t\t\t\tfont-size: 16px;\n\t\t\t\t\t\t\t\t\ttext-align: center;\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t.select2-results__group {\n\t\t\t\t\t\t\t\tcolor: $fiftyshades;\n\t\t\t\t\t\t\t\tfont-size: 12px;\n\t\t\t\t\t\t\t\tfont-weight: 500;\n\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t&:not(.select2-results__option--selected) {\n\t\t\t\t\t\t\t\tcursor: pointer;\n\n\t\t\t\t\t\t\t\t&.select2-results__option--highlighted {\n\t\t\t\t\t\t\t\t\tbackground-color: $haze;\n\t\t\t\t\t\t\t\t\tcolor: $nightrider;\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t&.select2-results__option--selected {\n\t\t\t\t\t\t\t\tbackground-color: $grey;\n\t\t\t\t\t\t\t\tcolor: $white;\n\n\t\t\t\t\t\t\t\t&.select2-results__option--highlighted {\n\t\t\t\t\t\t\t\t\tbackground-color: $grey;\n\t\t\t\t\t\t\t\t\tcolor: $white;\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t// SIZE: Small.\n\t\t\t\t&.sui-select-dropdown-sm {\n\t\t\t\t\tpadding: 15px 0;\n\n\t\t\t\t\t// Dropdown list.\n\t\t\t\t\t.select2-results {\n\t\t\t\t\t\t.select2-results__options {\n\t\t\t\t\t\t\tmax-height: 208px;\n\n\t\t\t\t\t\t\t.select2-results__option {\n\t\t\t\t\t\t\t\tfont-size: 13px;\n\t\t\t\t\t\t\t\tline-height: 30px;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t// DESIGN: Select with icon.\n\t\t\t&.sui-select-theme--icon {\n\t\t\t\t.sui-select-dropdown {\n\t\t\t\t\t// Dropdown list.\n\t\t\t\t\t.select2-results {\n\t\t\t\t\t\t.select2-results__options {\n\t\t\t\t\t\t\t.select2-results__option {\n\t\t\t\t\t\t\t\tposition: relative;\n\n\t\t\t\t\t\t\t\t[class*=\"sui-icon-\"] {\n\t\t\t\t\t\t\t\t\twidth: 30px;\n\t\t\t\t\t\t\t\t\theight: 30px;\n\t\t\t\t\t\t\t\t\tdisplay: flex;\n\t\t\t\t\t\t\t\t\tflex-flow: row nowrap;\n\t\t\t\t\t\t\t\t\talign-items: center;\n\t\t\t\t\t\t\t\t\tjustify-content: center;\n\t\t\t\t\t\t\t\t\tposition: absolute;\n\t\t\t\t\t\t\t\t\ttop: 0;\n\t\t\t\t\t\t\t\t\tleft: 10px;\n\t\t\t\t\t\t\t\t\ttext-align: center;\n\n\t\t\t\t\t\t\t\t\t&:before {\n\t\t\t\t\t\t\t\t\t\tdisplay: block;\n\t\t\t\t\t\t\t\t\t\tflex: 0 0 auto;\n\t\t\t\t\t\t\t\t\t\tcolor: inherit;\n\t\t\t\t\t\t\t\t\t\tfont-size: 16px;\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t\t&[role=\"option\"] {\n\t\t\t\t\t\t\t\t\tpadding-left: 46px;\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\n\t\t\t\t\t// SIZE: Small.\n\t\t\t\t\t&.sui-select-dropdown-sm {\n\t\t\t\t\t\t// Dropdown list.\n\t\t\t\t\t\t.select2-results {\n\t\t\t\t\t\t\t.select2-results__options {\n\t\t\t\t\t\t\t\t.select2-results__option {\n\t\t\t\t\t\t\t\t\t[class*=\"sui-icon-\"] {\n\t\t\t\t\t\t\t\t\t\theight: 28px;\n\t\t\t\t\t\t\t\t\t\tleft: 5px;\n\n\t\t\t\t\t\t\t\t\t\t&:before {\n\t\t\t\t\t\t\t\t\t\t\tfont-size: 12px;\n\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t\t\t&[role=\"option\"] {\n\t\t\t\t\t\t\t\t\t\tpadding-left: 36px;\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t// DESIGN: Select with color.\n\t\t\t&.sui-select-theme--color {\n\t\t\t\t.sui-select-dropdown {\n\t\t\t\t\t// Dropdown list.\n\t\t\t\t\t.select2-results {\n\t\t\t\t\t\t.select2-results__options {\n\t\t\t\t\t\t\t.select2-results__option {\n\t\t\t\t\t\t\t\tposition: relative;\n\n\t\t\t\t\t\t\t\t.sui-color {\n\t\t\t\t\t\t\t\t\twidth: 20px;\n\t\t\t\t\t\t\t\t\theight: 20px;\n\t\t\t\t\t\t\t\t\tposition: absolute;\n\t\t\t\t\t\t\t\t\ttop: 50%;\n\t\t\t\t\t\t\t\t\tleft: 10px;\n\t\t\t\t\t\t\t\t\tborder-width: 1px;\n\t\t\t\t\t\t\t\t\tborder-style: solid;\n\t\t\t\t\t\t\t\t\tborder-radius: math.div($border-radius, 2);\n\t\t\t\t\t\t\t\t\ttransform: translateY(-50%);\n\t\t\t\t\t\t\t\t\ttext-align: center;\n\t\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t\t&[role=\"option\"] {\n\t\t\t\t\t\t\t\t\tpadding-left: 40px;\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t// DESIGN: Select with variables.\n\t\t\t&.sui-select-theme--vars {\n\t\t\t\t.sui-select-dropdown {\n\t\t\t\t\twidth: 240px !important;\n\t\t\t\t\tmargin-left: -200px;\n\n\t\t\t\t\t// Dropdown list.\n\t\t\t\t\t.select2-results {\n\t\t\t\t\t\t.select2-results__options {\n\t\t\t\t\t\t\t.select2-results__option {\n\t\t\t\t\t\t\t\t&[role=\"option\"] {\n\t\t\t\t\t\t\t\t\tdisplay: flex;\n\t\t\t\t\t\t\t\t\tflex-flow: row nowrap;\n\t\t\t\t\t\t\t\t\talign-items: center;\n\t\t\t\t\t\t\t\t\tposition: relative;\n\n\t\t\t\t\t\t\t\t\t.sui-variable-name {\n\t\t\t\t\t\t\t\t\t\tmin-width: 100px;\n\t\t\t\t\t\t\t\t\t\tflex: 1;\n\t\t\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t\t\t.sui-variable-value {\n\t\t\t\t\t\t\t\t\t\tflex: 0 0 auto;\n\t\t\t\t\t\t\t\t\t\tcolor: $fiftyshades;\n\t\t\t\t\t\t\t\t\t\tfont-size: 12px;\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t\t&--group[role=\"group\"] {\n\t\t\t\t\t\t\t\t\tpadding-left: 0;\n\t\t\t\t\t\t\t\t\tpadding-right: 0;\n\n\t\t\t\t\t\t\t\t\t.select2-results__group {\n\t\t\t\t\t\t\t\t\t\tcursor: initial;\n\t\t\t\t\t\t\t\t\t\tpadding-left: 14px;\n\t\t\t\t\t\t\t\t\t\tpadding-right: 14px;\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t// DESIGN: Smart Search.\n\t\t\t&.sui-select-theme--search {\n\t\t\t\t.sui-select-dropdown {\n\t\t\t\t\tborder-color: $blue;\n\n\t\t\t\t\t&.sui-select-dropdown--above {\n\t\t\t\t\t\tborder-bottom-style: none;\n\t\t\t\t\t\tborder-bottom-left-radius: 0;\n\t\t\t\t\t\tborder-bottom-right-radius: 0;\n\t\t\t\t\t\tbox-shadow: 0 -2px 0 2px $blue-ghost;\n\t\t\t\t\t}\n\n\t\t\t\t\t&.sui-select-dropdown--below {\n\t\t\t\t\t\tborder-top-style: none;\n\t\t\t\t\t\tborder-top-left-radius: 0;\n\t\t\t\t\t\tborder-top-right-radius: 0;\n\t\t\t\t\t\tbox-shadow: 0 2px 0 2px $blue-ghost;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n}\n\n// $sui-wrap: false | $rtl: true\n@include body-class(false, true) {\n\t.sui-select {\n\t\t&.sui-select-dropdown-container--open {\n\t\t\tdirection: rtl;\n\n\t\t\t.sui-select-dropdown {\n\t\t\t\t// Search input.\n\t\t\t\t.select2-search {\n\t\t\t\t\tinput {\n\t\t\t\t\t\ttext-align: right;\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t// Dropdown list.\n\t\t\t\t.select2-results {\n\t\t\t\t\t.select2-results__options {\n\t\t\t\t\t\t.select2-results__option {\n\t\t\t\t\t\t\ttext-align: right;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t// DESIGN: Select with icon.\n\t\t\t&.sui-select-theme--icon {\n\t\t\t\t.sui-select-dropdown {\n\t\t\t\t\t// Dropdown list.\n\t\t\t\t\t.select2-results {\n\t\t\t\t\t\t.select2-results__options {\n\t\t\t\t\t\t\t.select2-results__option {\n\t\t\t\t\t\t\t\t[class*=\"sui-icon-\"] {\n\t\t\t\t\t\t\t\t\tright: 10px;\n\t\t\t\t\t\t\t\t\tleft: auto;\n\t\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t\t&[role=\"option\"] {\n\t\t\t\t\t\t\t\t\tpadding-left: 14px;\n\t\t\t\t\t\t\t\t\tpadding-right: 46px;\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t// DESIGN: Select with color.\n\t\t\t&.sui-select-theme--color {\n\t\t\t\t.sui-select-dropdown {\n\t\t\t\t\t// Dropdown list.\n\t\t\t\t\t.select2-results {\n\t\t\t\t\t\t.select2-results__options {\n\t\t\t\t\t\t\t.select2-results__option {\n\t\t\t\t\t\t\t\t.sui-color {\n\t\t\t\t\t\t\t\t\tright: 10px;\n\t\t\t\t\t\t\t\t\tleft: auto;\n\t\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t\t&[role=\"option\"] {\n\t\t\t\t\t\t\t\t\tpadding-right: 40px;\n\t\t\t\t\t\t\t\t\tpadding-left: 14px;\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n}\n", "@include body-class(true) {\n\n\t.sui-tag {\n\t\theight: auto;\n\t\tmin-height: 26px;\n\t\tcursor: default;\n\t\tdisplay: inline-block;\n\t\tmargin: 0 3px;\n\t\tpadding: 0 16px;\n\t\tborder: 2px solid transparent;\n\t\tborder-radius: 13px;\n\t\tbackground-color: $tag--default-background;\n\t\tcolor: $tag--default-color;\n\t\tfont: 500 12px/22px $font;\n\t\tletter-spacing: $font--letter-spacing;\n\t\ttext-align: center;\n\n\t\t// VARIATION: One line tag.\n\t\t&.sui-tag-truncated {\n\t\t\tmax-width: 100%;\n\n\t\t\tspan {\n\t\t\t\tmax-width: 100%;\n\t\t\t\toverflow: hidden;\n\t\t\t\tdisplay: block;\n\t\t\t\ttext-overflow: ellipsis;\n\t\t\t\twhite-space: nowrap;\n\t\t\t}\n\t\t}\n\n\t\t// TAG: Red\n\t\t&.sui-tag-red,\n\t\t&.sui-tag-error {\n\t\t\tbackground-color: $tag--red-background;\n\t\t\tcolor: $tag--red-color;\n\t\t}\n\n\t\t// TAG: Yellow\n\t\t&.sui-tag-yellow,\n\t\t&.sui-tag-warning {\n\t\t\tbackground-color: $tag--yellow-background;\n\t\t\tcolor: $tag--yellow-color;\n\t\t}\n\n\t\t// TAG: Green\n\t\t&.sui-tag-green,\n\t\t&.sui-tag-upsell,\n\t\t&.sui-tag-success,\n\t\t&.sui-tag-upgrade {\n\t\t\tbackground-color: $tag--green-background;\n\t\t\tcolor: $tag--green-color;\n\t\t}\n\n\t\t// TAG: Blue\n\t\t&.sui-tag-blue,\n\t\t&.sui-tag-branded {\n\t\t\tbackground-color: $tag--blue-background;\n\t\t\tcolor: $tag--blue-color;\n\t\t}\n\n\t\t// TAG: Purple\n\t\t&.sui-tag-purple {\n\t\t\tbackground-color: $tag--purple-background;\n\t\t\tcolor: $tag--purple-color;\n\t\t}\n\n\t\t// TAG: Disabled\n\t\t&.sui-tag-disabled,\n\t\t&.sui-tag-inactive {\n\t\t\tbackground-color: $tag--disabled-background;\n\t\t\tcolor: $tag--disabled-color;\n\t\t}\n\n\t\t// TAG: Uppercase\n\t\t&.sui-tag-uppercase {\n\t\t\ttext-transform: uppercase;\n\t\t}\n\n\t\t&.sui-tag-pro,\n\t\t&.sui-tag-beta {\n\t\t\tmin-height: 12px;\n\t\t\tpadding: 2px 8px 1px;\n\t\t\tborder: 0;\n\t\t\tborder-radius: 6px;\n\t\t\tfont-size: 8px;\n\t\t\tline-height: 9px;\n\t\t\ttext-align: center;\n\t\t\ttext-transform: uppercase;\n\t\t}\n\n\t\t// TAG: Pro\n\t\t&.sui-tag-pro {\n\t\t\tbackground-color: $tag--pro-background;\n\t\t\tcolor: $tag--pro-color;\n\t\t}\n\n\t\t// TAG: Beta\n\t\t&.sui-tag-beta {\n\t\t\tbackground-color: $tag--beta-background;\n\t\t\tcolor: $tag--beta-color;\n\t\t}\n\n\t\t// SIZE: Small\n\t\t&.sui-tag-sm {\n\t\t\tmin-height: 18px;\n\t\t\tpadding: 2px 10px;\n\t\t\tfont-size: 10px;\n\t\t\tline-height: 12px;\n\t\t\tfont-weight: bold;\n\t\t}\n\n\t\t// GHOST TAGS\n\t\t&.sui-tag-ghost {\n\n\t\t\t// TAG: Red\n\t\t\t&.sui-tag-red,\n\t\t\t&.sui-tag-error {\n\t\t\t\tborder-color: $tag--red-border-color;\n\t\t\t\tbackground-color: transparent;\n\t\t\t\tcolor: $tag--red-background;\n\t\t\t}\n\n\t\t\t// TAG: Yellow\n\t\t\t&.sui-tag-yellow,\n\t\t\t&.sui-tag-warning {\n\t\t\t\tborder-color: $tag--yellow-border-color;\n\t\t\t\tbackground-color: transparent;\n\t\t\t\tcolor: $tag--yellow-background;\n\t\t\t}\n\n\t\t\t// TAG: Green\n\t\t\t&.sui-tag-green,\n\t\t\t&.sui-tag-upsell,\n\t\t\t&.sui-tag-success,\n\t\t\t&.sui-tag-upgrade {\n\t\t\t\tborder-color: $tag--green-border-color;\n\t\t\t\tbackground-color: transparent;\n\t\t\t\tcolor: $tag--green-background;\n\t\t\t}\n\n\t\t\t// TAG: Blue\n\t\t\t&.sui-tag-blue,\n\t\t\t&.sui-tag-branded {\n\t\t\t\tborder-color: $tag--blue-border-color;\n\t\t\t\tbackground-color: transparent;\n\t\t\t\tcolor: $tag--blue-background;\n\t\t\t}\n\n\t\t\t// TAG: Purple\n\t\t\t&.sui-tag-purple {\n\t\t\t\tborder-color: $tag--purple-border-color;\n\t\t\t\tbackground-color: transparent;\n\t\t\t\tcolor: $tag--purple-background;\n\t\t\t}\n\t\t}\n\n\t\t&:first-child {\n\t\t\tmargin-left: 0;\n\t\t}\n\n\t\t&:last-child {\n\t\t\tmargin-right: 0;\n\t\t}\n\t}\n}\n", "@include body-class(true) {\n\n\t// HELPER: Label\n\t%label {\n\t\tdisplay: inline-flex;\n\t\tpadding: 0;\n\t\tborder: 0;\n\t\tcolor: $form--label-color;\n\t\tfont: $form--label-font-weight #{$form--label-font-size}/#{$form--label-line-height} $form--label-font;\n\t}\n\n\t// HELPER: Form control\n\t%form-control {\n\t\twidth: 100%;\n\t\theight: $form--input-height-base;\n\t\tmargin: 0;\n\t\tpadding: 9px 14px;\n\t\tborder: 1px solid $overcast;\n\t\tborder-radius: $border-radius;\n\t\tbackground-color: $cloud;\n\t\tbackground-image: none;\n\t\tcolor: $nightrider;\n\t\tfont: 500 15px/20px $font;\n\t\tletter-spacing: -0.25px;\n\t\ttransition: 0.2s ease-in-out;\n\t\toutline: none;\n\t\tbox-shadow: none;\n\n\t\t&::placeholder {\n\t\t\tcolor: $fiftyshades;\n\t\t}\n\n\t\t&:hover {\n\t\t\tborder-color: $fiftyshades;\n\t\t\tbackground-color: $white;\n\t\t\toutline: none;\n\t\t\tbox-shadow: none;\n\t\t}\n\n\t\t&:focus {\n\t\t\tborder-color: $blue;\n\t\t\tbackground-color: $white;\n\t\t\toutline: none;\n\t\t\tbox-shadow: 0 0 0 2px $blue-ghost;\n\n\t\t\t&::placeholder {\n\t\t\t\tcolor: $overcast;\n\t\t\t}\n\t\t}\n\n\t\t// STATE: Disabled\n\t\t&[disabled],\n\t\t&.sui-disabled,\n\t\tfieldset[disabled] & {\n\t\t\tpointer-events: none;\n\t\t\tcolor: $fiftyshades;\n\t\t\tbackground: $silver;\n\t\t\tborder-color: $overcast;\n\t\t}\n\n\t\t// FIX:\n\t\t// Unstyle the caret on <select> in IE10+\n\t\t&::-ms-expand {\n\t\t\tborder: 0;\n\t\t\tbackground-color: transparent;\n\t\t}\n\t}\n\n\t// ELEMENT: Label (Global)\n\tlabel {\n\t\tcursor: default;\n\n\t\t&[for] {\n\t\t\tcursor: pointer;\n\t\t}\n\t}\n\n\t// ELEMENT: Label\n\t.sui-label {\n\t\t@extend %label;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tmargin: 0 0 5px;\n\n\t\t.sui-tag {\n\t\t\tmargin-left: 5px;\n\n\t\t\t&.sui-left {\n\t\t\t\tmargin-right: 5px;\n\t\t\t\tmargin-left: 0;\n\t\t\t}\n\t\t}\n\n\t\t&:last-child {\n\t\t\tmargin: 0;\n\t\t}\n\n\t\t// VARIATION: Label link\n\t\t&-link {\n\t\t\tmargin-right: 0;\n\t\t\tmargin-left: auto;\n\t\t\tcolor: $form--label-color;\n\t\t\tfont-weight: 400;\n\t\t}\n\n\t\t// VARIATION: Inline label\n\t\t&-inline {\n\t\t\t@extend %label;\n\t\t\tmargin: 0;\n\t\t}\n\n\t\t// VARIATION: Label note\n\t\t&-note {\n\t\t\tmargin-right: 0;\n\t\t\tmargin-left: auto;\n\t\t}\n\t}\n\n\t// ELEMENT: Form control\n\t// This element allow us to style textarea and inputs (all except checkbox and radio)\n\t.sui-form-control {\n\t\t@extend %form-control;\n\t\tdisplay: block;\n\n\t\t&.sui-input-sm {\n\t\t\tmax-width: 80px;\n\t\t}\n\t\t&.sui-input-md {\n\t\t\tmax-width: 240px;\n\t\t}\n\n\t\t// Form field prefix and suffix\n\t\t&.sui-field-has-prefix,\n\t\t&.sui-field-has-suffix {\n\t\t\twidth: auto;\n\t\t\tdisplay: inline-block;\n\t\t}\n\t}\n\n\t// ELEMENT: Multi checkbox\n\t.sui-multi-checkbox {\n\t\t@extend .sui-form-control;\n\t\theight: auto;\n\t\tmax-height: 114px;\n\t\toverflow-y: auto;\n\t\tpadding: 1px;\n\n\t\tlabel {\n\t\t\tmargin: 1px 0;\n\n\t\t\tinput {\n\t\t\t\t@extend %sui-screen-reader-text;\n\n\t\t\t\t&:checked + span {\n\t\t\t\t\tbackground-color: $blue;\n\t\t\t\t\tcolor: $white;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tspan {\n\t\t\t\tdisplay: block;\n\t\t\t\tpadding: 10px;\n\t\t\t}\n\n\t\t\t&:first-child {\n\t\t\t\tmargin-top: 0;\n\n\t\t\t\tspan {\n\t\t\t\t\tborder-radius: #{$border-radius - 1px} #{$border-radius - 1px} 0 0;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t&:last-child {\n\t\t\t\tmargin-bottom: 0;\n\n\t\t\t\tspan {\n\t\t\t\t\tborder-radius: 0 0 #{$border-radius - 1px} #{$border-radius - 1px};\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\n\t// ELEMENT: Field description\n\t.sui-multi-checkbox label {\n\t\tdisplay: block;\n\t\tmargin-top: 5px;\n\t\tcolor: $form--description-color;\n\t\tfont-size: $form--description-font-size;\n\t\tline-height: 22px;\n\t\tfont-weight: $form--description-font-weight;\n\t\tletter-spacing: $font--letter-spacing;\n\n\t\t&.sui-toggle-description {\n\t\t\tmargin-left: 48px;\n\t\t}\n\n\t\t&.sui-checkbox-description,\n\t\t&.sui-radio-description {\n\t\t\tmargin: 0 27px 5px;\n\t\t}\n\n\t\t+ .sui-form-field,\n\t\t+ .sui-form-field-inline {\n\t\t\tmargin-top: 20px;\n\t\t}\n\t}\n\n\t// ELEMENT: Error message\n\t.sui-error-message {\n\t\tdisplay: block;\n\t\tmargin-top: 8px;\n\t\tcolor: $form--input-error-color;\n\t\tfont-size: $form--input-error-font-size;\n\t\tline-height: $form--input-error-line-height;\n\t\tfont-weight: $form--input-error-font-weight;\n\t}\n\n\t// GROUP: Form field\n\t.sui-form-field {\n\n\t\t&.sui-input-sm {\n\t\t\tmax-width: 80px;\n\t\t}\n\t\t&.sui-input-md {\n\t\t\tmax-width: 240px;\n\t\t}\n\n\t\t> .sui-row {\n\n\t\t\t[class^=\"sui-col\"] {\n\n\t\t\t\t@include media(min-width, md) {\n\t\t\t\t\tpadding-right: 5px;\n\t\t\t\t\tpadding-left: 5px;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t&:first-child {\n\n\t\t\t\t@include media(min-width, md) {\n\t\t\t\t\tmargin-top: 0;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t@include media(min-width, md) {\n\t\t\t\tmargin-top: 10px;\n\t\t\t\tmargin-right: -5px;\n\t\t\t\tmargin-bottom: 0;\n\t\t\t\tmargin-left: -5px;\n\t\t\t}\n\t\t}\n\n\t\t&:last-child {\n\t\t\tmargin-bottom: 0;\n\n\t\t\t@include media( max-width, md ) {\n\t\t\t\tmargin-bottom: 0;\n\t\t\t}\n\t\t}\n\n\t\t// STATE: Error\n\t\t&.sui-has_error,\n\t\t&.sui-form-field-error {\n\n\t\t\t.sui-form-control {\n\t\t\t\tborder-color: $red;\n\n\t\t\t\t&:focus {\n\t\t\t\t\tbox-shadow: 0 0 0 2px $red-ghost;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t.sui-control-with-icon {\n\n\t\t\t\t[class*=\"sui-icon-\"]:before {\n\t\t\t\t\tcolor: $red;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t@include media(max-width, md) {\n\t\t\tmargin-bottom: $sui-gutter-md;\n\t\t}\n\n\t\t@include media(min-width, md) {\n\t\t\tmargin-bottom: $sui-gutter;\n\t\t}\n\t}\n\n\t.sui-field-suffix,\n\t.sui-field-prefix {\n\t\tdisplay: inline-block;\n\t\tcolor: #888888;\n\t\tfont-size: 13px;\n\t\tline-height: 22px;\n\t}\n\n\t.sui-field-prefix {\n\t\tmargin-right: 10px;\n\t}\n\t.sui-field-suffix {\n\t\tmargin-left: 10px;\n\t}\n\n\t// GROUP: Form field (inline)\n\t.sui-form-field-inline {\n\n\t\t> .sui-form-field,\n\t\t> span.sui-select {\n\n\t\t\t&:first-child {\n\n\t\t\t\t@include media(min-width, md) {\n\t\t\t\t\tmargin-left: 0;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t&:last-child {\n\n\t\t\t\t@include media(min-width, md) {\n\t\t\t\t\tmargin-right: 0;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t@include media(min-width, md) {\n\t\t\t\tflex: 0 0 auto;\n\t\t\t\tmargin-top: 0;\n\t\t\t\tmargin-bottom: 0;\n\t\t\t\tmargin-right: 10px;\n\t\t\t\tmargin-left: 10px;\n\t\t\t}\n\t\t}\n\n\t\t> .sui-form-field {\n\n\t\t\t@include media(min-width, md) {\n\t\t\t\tflex: 0 0 auto;\n\t\t\t\tmargin-bottom: 0;\n\t\t\t}\n\t\t}\n\n\t\t> p,\n\t\t> span,\n\t\t> .sui-label {\n\n\t\t\t@include media(min-width, md) {\n\t\t\t\tflex: 0 0 auto;\n\t\t\t\tmargin: 0;\n\t\t\t}\n\t\t}\n\n\t\t&:last-child {\n\n\t\t\t@include media(max-width, md) {\n\t\t\t\tmargin-bottom: 0;\n\t\t\t}\n\n\t\t\t@include media(min-width, md) {\n\t\t\t\tmargin-bottom: 0;\n\t\t\t}\n\t\t}\n\n\t\t@include media(min-width, md) {\n\t\t\tdisplay: flex;\n\t\t\talign-items: center;\n\t\t\tmargin-bottom: $sui-gutter;\n\t\t}\n\n\t\t@include media(max-width, md) {\n\t\t\tmargin-bottom: $sui-gutter-md;\n\t\t}\n\t}\n\n\t// GROUP: Input with icon\n\t// This group doesn't work with inline form control\n\t.sui-control-with-icon {\n\t\tposition: relative;\n\n\t\t.sui-form-control {\n\t\t\tpadding-left: 40px;\n\t\t}\n\n\t\t// Select 2\n\t\tselect.sui-select+.select2-container .select2-selection__rendered {\n\t\t\tpadding-left: 40px;\n\t\t}\n\n\t\t[class*=\"sui-icon-\"] {\n\t\t\twidth: 16px;\n\t\t\theight: 16px;\n\t\t\tpointer-events: none;\n\t\t\tdisplay: flex;\n\t\t\tflex-direction: column;\n\t\t\talign-items: center;\n\t\t\tjustify-content: center;\n\t\t\tposition: absolute;\n\t\t\ttop: 11px;\n\t\t\tleft: 14px;\n\n\t\t\t&:before {\n\t\t\t\tcolor: $form--input-icon;\n\t\t\t\tdisplay: block;\n\t\t\t}\n\t\t}\n\n\t\t&.sui-right-icon {\n\n\t\t\t.sui-form-control {\n\t\t\t\tpadding-right: 40px;\n\t\t\t\tpadding-left: 14px;\n\t\t\t}\n\n\t\t\t[class*=\"sui-icon-\"] {\n\t\t\t\tright: 14px;\n\t\t\t\tleft: auto;\n\t\t\t\tcolor: $form--input-icon-right;\n\t\t\t}\n\t\t}\n\t}\n\n\t// GROUP: Input with button\n\t.sui-with-button {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\n\t\t.sui-button {\n\t\t\tflex: 0 0 auto;\n\t\t}\n\n\t\t.sui-form-control {\n\t\t\tflex: 1;\n\t\t\tmargin: 0 5px;\n\n\t\t\t&:first-child {\n\t\t\t\tmargin-left: 0;\n\t\t\t}\n\n\t\t\t&:last-child {\n\t\t\t\tmargin-right: 0;\n\t\t\t}\n\t\t}\n\n\t\t// VARIATION: Inside button\n\t\t&.sui-inside,\n\t\t&.sui-with-button-inside {\n\t\t\tdisplay: block;\n\t\t\tposition: relative;\n\n\t\t\t.sui-button,\n\t\t\t.sui-button-icon {\n\t\t\t\tposition: absolute;\n\t\t\t\ttop: 5px;\n\t\t\t\tright: 5px;\n\t\t\t}\n\n\t\t\t.sui-button-lg {\n\t\t\t\ttop: 0;\n\t\t\t\tright: 0;\n\t\t\t\tborder-top-left-radius: 0;\n\t\t\t\tborder-bottom-left-radius: 0;\n\t\t\t}\n\n\t\t\t.sui-form-control {\n\t\t\t\tmargin: 0;\n\t\t\t\tpadding-right: 90px;\n\t\t\t}\n\t\t}\n\n\t\t// VARIATION: Icon button\n\t\t&.sui-with-button-icon {\n\t\t\tdisplay: block;\n\t\t\tposition: relative;\n\n\t\t\t.sui-button,\n\t\t\t.sui-button-icon {\n\t\t\t\tposition: absolute;\n\t\t\t\ttop: 5px;\n\t\t\t\tright: 5px;\n\t\t\t}\n\n\t\t\t.sui-button-lg {\n\t\t\t\ttop: 0;\n\t\t\t\tright: 0;\n\t\t\t}\n\n\t\t\t.sui-form-control {\n\t\t\t\tmargin: 0;\n\t\t\t\tpadding-right: 40px;\n\t\t\t}\n\t\t}\n\t}\n\n\t// GROUP: Inputs\n\t.sui-input-group {\n\t\twidth: 100%;\n\t\tdisplay: flex;\n\t\talign-items: stretch;\n\t\tposition: relative;\n\t}\n\n\t// GROUP: Password\n\t.sui-password-group {\n\t\tposition: relative;\n\n\t\t.sui-password-toggle {\n\t\t\twidth: 30px;\n\t\t\theight: 30px;\n\t\t\tcursor: pointer;\n\t\t\tposition: absolute;\n\t\t\ttop: 50%;\n\t\t\tright: 7px;\n\t\t\tpadding: 0;\n\t\t\tborder: 0;\n\t\t\tborder-radius: $border-radius;\n\t\t\tbackground: transparent;\n\t\t\tcolor: $form--input-icon-color;\n\t\t\tfont-size: 15px;\n\t\t\tline-height: 1em;\n\t\t\ttransform: translateY(-50%);\n\n\t\t\t&:hover,\n\t\t\t&:focus,\n\t\t\t&:active {\n\t\t\t\toutline: 0;\n\t\t\t}\n\n\t\t\t&:hover {\n\t\t\t\tbackground-color: rgba(0, 0, 0, .03);\n\n\t\t\t\t[class*=\"sui-icon-\"]:before {\n\t\t\t\t\tcolor: $gray;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\n\t// GROUP: Date\n\t.sui-date {\n\t\tposition: relative;\n\n\t\t.sui-form-control {\n\n\t\t\t&:first-child {\n\t\t\t\tpadding-right: 40px;\n\t\t\t}\n\n\t\t\t&:last-child {\n\t\t\t\tpadding-left: 40px;\n\t\t\t}\n\t\t}\n\n\t\t[class*=\"sui-icon-\"] {\n\t\t\twidth: 30px;\n\t\t\theight: 30px;\n\t\t\tpointer-events: none;\n\t\t\tdisplay: flex;\n\t\t\tflex-direction: column;\n\t\t\talign-items: center;\n\t\t\tjustify-content: center;\n\t\t\tposition: absolute;\n\t\t\ttop: 5px;\n\n\t\t\t&:before {\n\t\t\t\tdisplay: block;\n\t\t\t}\n\n\t\t\t&:first-child {\n\t\t\t\tleft: 5px;\n\t\t\t}\n\n\t\t\t&:last-child {\n\t\t\t\tright: 5px;\n\t\t\t}\n\t\t}\n\t}\n\n\t.sui-multi-date {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\n\t\t.sui-form-field {\n\t\t\tflex: 1;\n\n\t\t\t+ button {\n\t\t\t\tflex: 0 0 auto;\n\t\t\t\tmargin-left: 10px;\n\t\t\t}\n\n\t\t\t@include media(max-width, md) {\n\t\t\t\tmargin-bottom: 0;\n\t\t\t}\n\n\t\t\t@include media(min-width, md) {\n\t\t\t\tmargin-bottom: 0;\n\t\t\t}\n\t\t}\n\n\t\t&:not(:last-child) {\n\t\t\tmargin-bottom: 10px;\n\t\t}\n\t}\n\n\t// FIX:\n\t// Reset height for textarea\n\ttextarea.sui-form-control {\n\t\tmax-width: 100%;\n\t\theight: auto;\n\t\tresize: vertical;\n\t\tline-height: 20px;\n\t}\n\n\t// FIX:\n\t// Add special styles if <select> is being used with .sui-form-control class.\n\t//\n\t// NOTE: There's no need to use <select> with this class but just in case,\n\t// a fix for it must be included.\n\tselect.sui-form-control {\n\t\tcursor: pointer;\n\t\tappearance: none;\n\t\tbackground-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA4AAAAOCAYAAAAfSC3RAAAAs0lEQVQ4T6WS2w0CIRBF59KA2oklaAdbgpbgB48SIBThduKWYClrA2CGgEEXNWH5moR7Zu48QJ0PnRytA6WUe67svb//clDrUkWt9UxEMYRw/AYzJIS4sd45t0ugMeZERNcY49yCCwRgS0Rna+346rGGAVz4s06aW0gQx2/DUUoNAEYAG86cxezkAWCw1k5lBoupZltThomhEMLhs/fmOgrM2VvQwmq9in8rWncAPWfXXfEJ6RpWD7sJ1JwAAAAASUVORK5CYII=);\n\t\tbackground-repeat: no-repeat;\n\t\tbackground-position: center right 10px;\n\t\tline-height: 1;\n\t}\n}", "@use \"sass:math\";\n\n// HELPER: Radio\n%radio-checkbox {\n\tuser-select: none;\n\talign-items: flex-start;\n\tpointer-events: none;\n\n\tinput {\n\t\t@extend %sui-screen-reader-text;\n\n\t\t+ span {\n\t\t\twidth: $radio-checkbox--size;\n\t\t\theight: $radio-checkbox--size;\n\t\t\tcursor: pointer;\n\t\t\tdisplay: block;\n\t\t\tflex-shrink: 0;\n\t\t\tposition: relative;\n\t\t\tmargin: math.div(($radio-checkbox--line-height - $radio-checkbox--size), 2) 0;\n\t\t\tborder: 1px solid $radio-checkbox--border-color;\n\t\t\tborder-radius: $border-radius;\n\t\t\tbackground-color: $radio-checkbox--background;\n\t\t\ttransition: 0.3s ease;\n\t\t\tpointer-events: all;\n\n\t\t\t&:before {\n\t\t\t\topacity: 0;\n\t\t\t\ttransition: 0.3s ease;\n\t\t\t}\n\n\t\t\t+ span,\n\t\t\t+ .sui-description {\n\t\t\t\tcursor: pointer;\n\t\t\t\tmargin: 0 0 0 10px;\n\t\t\t\tcolor: $radio-checkbox--color;\n\t\t\t\tfont-size: $radio-checkbox--font-size;\n\t\t\t\tline-height: $radio-checkbox--line-height;\n\t\t\t\tfont-weight: $font--medium;\n\t\t\t\tpointer-events: all;\n\n\t\t\t\t&.sui-description-sm {\n\t\t\t\t\tfont-size: $radio-checkbox--font-size-sm;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t~ .sui-tag {\n\t\t\t\tmargin-top: auto;\n\t\t\t\tmargin-bottom: auto;\n\t\t\t\tmargin-left: 10px;\n\t\t\t}\n\t\t}\n\n\t\t&:hover,\n\t\t&:focus {\n\t\t\t+ span {\n\t\t\t\tbox-shadow: 0px 0px 0px 4px $radio-checkbox--hover-background;\n\t\t\t}\n\t\t}\n\n\t\t&:checked {\n\n\t\t\t+ span {\n\t\t\t\tborder-color: $radio-checkbox--checked-border-color;\n\t\t\t\tbackground-color: $radio-checkbox--checked-background;\n\n\t\t\t\t&:before {\n\t\t\t\t\topacity: 1;\n\n\t\t\t\t\t@media (forced-colors: active) {\n\t\t\t\t\t\tforced-color-adjust: none;\n\t\t\t\t\t}\n\n\t\t\t\t\t@media (forced-colors: active) and (prefers-color-scheme: light), (-ms-high-contrast: black-on-white) {\n\t\t\t\t\t\tbackground-color: $accessible-dark;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t&:hover,\n\t\t\t&:focus {\n\t\t\t\t+ span {\n\t\t\t\t\tbox-shadow: 0px 0px 0px 4px $radio-checkbox--checked-hover-background;\n\t\t\t\t}\n\t\t\t}\t\t\t\n\t\t}\n\n\t\t&:disabled,\n\t\t&[disabled],\n\t\t&.sui-disabled {\n\n\t\t\t+ span {\n\t\t\t\tcursor: not-allowed;\n\t\t\t\tborder-color: $radio-checkbox--disabled-border-color;\n\t\t\t\tbackground-color: $radio-checkbox--disabled-background;\n\t\t\t\t\n\t\t\t\t+ span,\n\t\t\t\t+ .sui-description {\n\t\t\t\t\tcursor: not-allowed;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t&:hover,\n\t\t\t&:focus {\n\t\t\t\t+ span {\n\t\t\t\t\tbox-shadow: unset;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\n\t&:last-child {\n\n\t\t@include media(max-width, md) {\n\t\t\tmargin-bottom: 0;\n\t\t}\n\n\t\t@include media(min-width, md) {\n\t\t\tmargin-right: 0;\n\t\t}\n\t}\n\n\t@include media(max-width, md) {\n\t\tdisplay: flex;\n\t\tmargin: 0 0 math.div($sui-gutter-md, 2);\n\t}\n\n\t@include media(min-width, md) {\n\t\tdisplay: inline-flex;\n\t\tmargin: 0 math.div($sui-gutter, 2) 0 0;\n\t}\n}\n\n@include body-class(true) {\n\n\t// ============================================================\n\t// ELEMENT: Radio\n\t.sui-radio {\n\t\t@extend %radio-checkbox;\n\n\t\tinput {\n\n\t\t\t+ span {\n\t\t\t\tposition: relative;\n\t\t\t\tborder-radius: 50%;\n\n\t\t\t\t&:before {\n\t\t\t\t\tcontent: \" \";\n\t\t\t\t\twidth: $radio-checkbox--check-size;\n\t\t\t\t\theight: $radio-checkbox--check-size;\n\t\t\t\t\tdisplay: block;\n\t\t\t\t\tposition: absolute;\n\t\t\t\t\ttop: #{math.div(($radio-checkbox--size - $radio-checkbox--check-size), 2) - 1px};\n\t\t\t\t\tleft: #{math.div(($radio-checkbox--size - $radio-checkbox--check-size), 2) - 1px};\n\t\t\t\t\tborder-radius: $radio-checkbox--size;\n\t\t\t\t\tbackground-color: $radio-checkbox--check-color;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t&:disabled,\n\t\t\t&[disabled],\n\t\t\t&.sui-disabled {\n\n\t\t\t\t+ span:before {\n\t\t\t\t\tbackground-color: $radio-checkbox--disabled-check-color;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t// VARIATION: Stacked\n\t\t// To pile one element per-line\n\t\t&.sui-radio-stacked {\n\n\t\t\t&:last-child {\n\n\t\t\t\t@include media(min-width, md) {\n\t\t\t\t\tmargin: 0;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t@include media(min-width, md) {\n\t\t\t\tdisplay: flex;\n\t\t\t\tmargin: 0 0 math.div($sui-gutter-md, 2);\n\t\t\t}\n\t\t}\n\n\t\t// VARIATION: Small\n\t\t// This variation will affect label (text) only.\n\t\t&.sui-radio-sm {\n\n\t\t\tinput + span {\n\n\t\t\t\t+ span,\n\t\t\t\t+ .sui-description {\n\t\t\t\t\tfont-size: $radio-checkbox--font-size-sm;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\n\t// ============================================================\n\t// ELEMENT: Radio Image\n\t.sui-radio-image {\n\t\tflex-direction: column;\n\t\talign-items: center;\n\n\t\timg {\n\t\t\tdisplay: block;\n\t\t\tmargin: 0;\n\t\t\tpadding: 0;\n\t\t\tborder: 0;\n\n\t\t\t+ .sui-radio {\n\t\t\t\tmargin-top: 15px;\n\t\t\t}\n\t\t}\n\n\t\t+ * {\n\n\t\t\t@include media(max-width, md) {\n\t\t\t\tmargin-top: $sui-gutter-md;\n\t\t\t}\n\n\t\t\t@include media(min-width, md) {\n\t\t\t\tmargin-left: $sui-gutter-md;\n\t\t\t}\n\t\t}\n\n\t\t// VARIATION: Stacked\n\t\t// To pile one element per-line\n\t\t&.sui-radio-stacked {\n\n\t\t\t+ * {\n\n\t\t\t\t@include media(min-width, md) {\n\t\t\t\t\tmargin-top: $sui-gutter;\n\t\t\t\t\tmargin-left: 0;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t@include media(min-width, md) {\n\t\t\t\tdisplay: inline-flex;\n\t\t\t}\n\t\t}\n\n\t\t@include media(max-width, md) {\n\t\t\tdisplay: flex;\n\t\t}\n\n\t\t@include media(min-width, md) {\n\t\t\tdisplay: inline-flex;\n\t\t}\n\t}\n\n\t// ============================================================\n\t// ELEMENT: Checkbox\n\t.sui-checkbox {\n\t\t@extend %radio-checkbox;\n\n\t\tinput {\n\n\t\t\t+ span {\n\n\t\t\t\t@include icon(before, check) {\n\t\t\t\t\twidth: #{$radio-checkbox--size - 2px};\n\t\t\t\t\theight: #{$radio-checkbox--size - 2px};\n\t\t\t\t\tdisplay: flex;\n\t\t\t\t\talign-items: center;\n\t\t\t\t\tjustify-content: center;\n\t\t\t\t\tcolor: $radio-checkbox--check-color;\n\t\t\t\t\tfont-size: $radio-checkbox--icon-size;\n\t\t\t\t\ttext-align: center;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t&:disabled,\n\t\t\t&[disabled],\n\t\t\t&.sui-disabled {\n\n\t\t\t\t+ span:before {\n\t\t\t\t\tcolor: $radio-checkbox--disabled-check-color;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t// VARIATION: Stacked\n\t\t// To pile one element per-line\n\t\t&.sui-checkbox-stacked {\n\n\t\t\t&:last-child {\n\n\t\t\t\t@include media(min-width, md) {\n\t\t\t\t\tmargin: 0;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t@include media(min-width, md) {\n\t\t\t\tdisplay: flex;\n\t\t\t\tmargin: 0 0 math.div($sui-gutter-md, 2);\n\t\t\t}\n\t\t}\n\n\t\t// VARIATION: Small\n\t\t// This variation will affect label (text) only.\n\t\t&.sui-checkbox-sm {\n\n\t\t\tinput + span {\n\n\t\t\t\t+ span,\n\t\t\t\t+ .sui-description {\n\t\t\t\t\tfont-size: $radio-checkbox--font-size-sm;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\n\t// ============================================================\n\t// ELEMENT: Checkbox Image\n\t.sui-checkbox-image {\n\t\tflex-direction: column;\n\t\talign-items: center;\n\n\t\timg {\n\t\t\tdisplay: block;\n\t\t\tmargin: 0;\n\t\t\tpadding: 0;\n\t\t\tborder: 0;\n\n\t\t\t+ .sui-checkbox {\n\t\t\t\tmargin-top: 15px;\n\t\t\t}\n\t\t}\n\n\t\t+ * {\n\n\t\t\t@include media(max-width, md) {\n\t\t\t\tmargin-top: $sui-gutter-md;\n\t\t\t}\n\n\t\t\t@include media(min-width, md) {\n\t\t\t\tmargin-left: $sui-gutter-md;\n\t\t\t}\n\t\t}\n\n\t\t// VARIATION: Stacked\n\t\t// To pile one element per-line\n\t\t&.sui-checkbox-stacked {\n\n\t\t\t+ * {\n\n\t\t\t\t@include media(min-width, md) {\n\t\t\t\t\tmargin-top: $sui-gutter;\n\t\t\t\t\tmargin-left: 0;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t@include media(min-width, md) {\n\t\t\t\tdisplay: inline-flex;\n\t\t\t}\n\t\t}\n\n\t\t@include media(max-width, md) {\n\t\t\tdisplay: flex;\n\t\t}\n\n\t\t@include media(min-width, md) {\n\t\t\tdisplay: inline-flex;\n\t\t}\n\t}\n}\n\n@include body-class($wrap: true, $rtl: true) {\n\n\t%radio-checkbox {\n\t\tinput {\n\t\t\t+ span {\n\t\t\t\t+ span,\n\t\t\t\t+ .sui-description {\n\t\t\t\t\tmargin: 0 10px 0 0;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t\n\t\t@include media(min-width, md) {\n\t\t\tmargin: 0 0 0 math.div($sui-gutter, 2);\n\t\t}\n\t}\n\n\t.sui-checkbox {\n\t\t&.sui-checkbox-stacked {\n\n\t\t\t&:last-child {\n\n\t\t\t\t@include media(min-width, md) {\n\t\t\t\t\tmargin: 0;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t@include media(min-width, md) {\n\t\t\t\tmargin: 0 0 math.div($sui-gutter-md, 2);\n\t\t\t}\n\t\t}\n\t}\n\n\t.sui-radio {\n\t\t&.sui-radio-stacked {\n\n\t\t\t&:last-child {\n\n\t\t\t\t@include media(min-width, md) {\n\t\t\t\t\tmargin: 0;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t@include media(min-width, md) {\n\t\t\t\tmargin: 0 0 math.div($sui-gutter-md, 2);\n\t\t\t}\n\t\t}\n\t}\n}", "@include body-class(true) {\n\n\t// Tabs\n\t.sui-tabs:not(.sui-side-tabs) {\n\n\t\t> [data-tabs],\n\t\t> .sui-tabs-menu {\n\t\t\tdisplay: flex;\n\t\t\tmargin: 0;\n\t\t\tpadding: 0;\n\t\t\tborder-top: 1px solid $tabs-content-border-color;\n\t\t\tlist-style: none;\n\n\t\t\t> *,\n\t\t\t.sui-tab-item {\n\t\t\t\tcursor: pointer;\n\t\t\t\tmargin: 0 10px;\n\t\t\t\tpadding: 14px 0 12px;\n\t\t\t\tborder: 0; // Make sure borders are disabled when using \"button\" element for tabs.\n\t\t\t\tbackground-color: transparent;\n\t\t\t\tfont: 500 13px/22px $font;\n\t\t\t\tletter-spacing: $font--letter-spacing;\n\t\t\t\ttext-transform: uppercase;\n\t\t\t\ttext-decoration: none;\n\n\t\t\t\t&,\n\t\t\t\t&:focus,\n\t\t\t\t&:hover,\n\t\t\t\t&:active,\n\t\t\t\t&:visited {\n\t\t\t\t\toutline: none; // Make sure outline blur is disabled when using \"button\" element for tabs.\n\t\t\t\t\tborder-bottom: 2px solid transparent;\n\t\t\t\t}\n\n\t\t\t\t&,\n\t\t\t\t&:visited {\n\t\t\t\t\tcolor: $tabs-label-color;\n\t\t\t\t}\n\n\t\t\t\t&:focus,\n\t\t\t\t&:hover,\n\t\t\t\t&:active {\n\t\t\t\t\tcolor: $tabs-label-active-border-color;\n\t\t\t\t}\n\n\t\t\t\t&:first-child {\n\t\t\t\t\tmargin-left: 0;\n\t\t\t\t}\n\n\t\t\t\t&:last-child {\n\t\t\t\t\tmargin-right: 0;\n\t\t\t\t}\n\n\t\t\t\t&.active {\n\t\t\t\t\tcolor: $tabs-label-active-border-color;\n\t\t\t\t\tborder-bottom-color: $tabs-label-active-color;\n\t\t\t\t}\n\n\t\t\t\t@media (forced-colors: active) {\n\t\t\t\t\tforced-color-adjust: none;\n\t\t\t\t}\n\t\t\n\t\t\t\t@media (forced-colors: active) and (prefers-color-scheme: dark), (-ms-high-contrast: white-on-black) {\n\t\t\t\t\tcolor: $cloud !important;\n\t\t\t\t\t&.active {\n\t\t\t\t\t\tcolor: map-get($button-background, orange) !important;\n\t\t\t\t\t\tborder-bottom: 2px solid map-get($button-background, orange) !important;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t> [data-panes],\n\t\t> .sui-tabs-content {\n\n\t\t\t> *,\n\t\t\t> .sui-tab-content {\n\t\t\t\tdisplay: none;\n\t\t\t\tmargin-top: -1px;\n\t\t\t\tpadding: $sui-gutter-md 0 0;\n\t\t\t\tborder-top: 1px solid #E6E6E6;\n\n\t\t\t\t&.active {\n\t\t\t\t\tdisplay: block;\n\t\t\t\t}\n\n\t\t\t\t@include media(min-width, md) {\n\t\t\t\t\tpadding: $sui-gutter 0 0;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t// Flushed tabs\n\t\t&.sui-tabs-flushed {\n\n\t\t\t> [data-tabs],\n\t\t\t> .sui-tabs-menu {\n\t\t\t\tborder-top-width: 0;\n\t\t\t}\n\t\t}\n\n\t\t// Overflowed tabs\n\t\t&.sui-tabs-overflow {\n\t\t\tposition: relative;\n\n\t\t\t> [data-tabs],\n\t\t\t> .sui-tabs-menu {\n\t\t\t\toverflow: auto;\n\t\t\t\tscrollbar-width: none;\n\t\t\t\tscrollbar-height: none;\n\n\t\t\t\t&::-webkit-scrollbar {\n\t\t\t\t\twidth: 0;\n\t\t\t\t\theight: 0;\n\t\t\t\t\tbackground: transparent;\n\t\t\t\t}\n\n\t\t\t\t> *,\n\t\t\t\t.sui-tab-item {\n\t\t\t\t\tflex: 0 0 auto;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t// Navigation for overflow.\n\t\t\t.sui-tabs-navigation {\n\t\t\t\tposition: absolute;\n\t\t\t\twidth: 100%;\n\t\t\t\tdisplay: flex;\n\t\t\t\tjustify-content: space-between;\n\t\t\t\theight: 50px;\n\t\t\t\talign-items: center;\n\t\t\t\ttop: 1px;\n\t\t\t\tpointer-events: none;\n\t\t\t\topacity: 1;\n\n\t\t\t\t&--left,\n\t\t\t\t&--right {\n\t\t\t\t\tbackground: rgba(255, 255, 255, 0.8);\n\t\t\t\t\tpointer-events: all;\n\t\t\t\t\ttransition: all .2s;\n\t\t\t\t}\n\t\t\t\t&--hidden {\n\t\t\t\t\tvisibility: hidden;\n\t\t\t\t\topacity: 0;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t&:not(.sui-tabs-flushed) {\n\t\t\t\tborder: 1px solid $tabs-content-border-color;\n\t\t\t\tborder-radius: $border-radius;\n\n\t\t\t\t> [data-tabs],\n\t\t\t\t> .sui-tabs-menu {\n\t\t\t\t\tmargin-right: $sui-gutter-md;\n\t\t\t\t\tmargin-left: $sui-gutter-md;\n\t\t\t\t\tborder-top-width: 0;\n\n\t\t\t\t\t@include media(min-width, md) {\n\t\t\t\t\t\tmargin-right: $sui-gutter;\n\t\t\t\t\t\tmargin-left: $sui-gutter;\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t> [data-panes],\n\t\t\t\t> .sui-tabs-content {\n\n\t\t\t\t\t> *,\n\t\t\t\t\t> .sui-tab-content {\n\t\t\t\t\t\tpadding-right: $sui-gutter-md;\n\t\t\t\t\t\tpadding-bottom: $sui-gutter-md;\n\t\t\t\t\t\tpadding-left: $sui-gutter-md;\n\n\t\t\t\t\t\t@include media(min-width, md) {\n\t\t\t\t\t\t\tpadding-right: $sui-gutter;\n\t\t\t\t\t\t\tpadding-bottom: $sui-gutter;\n\t\t\t\t\t\t\tpadding-left: $sui-gutter;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\n\t// Side Tabs\n\t.sui-side-tabs {\n\t\tmargin: 0 0 $sui-gutter-md;\n\n\t\t> [data-tabs],\n\t\t> .sui-tabs-menu {\n\t\t\tdisplay: flex;\n\t\t\tflex-wrap: wrap;\n\t\t\tmargin: 0;\n\t\t\tpadding: 0;\n\t\t\tlist-style: none;\n\n\t\t\t> *,\n\t\t\t.sui-tab-item {\n\t\t\t\tcursor: pointer;\n\t\t\t\tflex: 0 0 auto;\n\t\t\t\tmargin: 0 1px 0 0;\n\t\t\t\tpadding: 9px 20px;\n\t\t\t\tborder: 0; // Make sure borders are disabled when using \"button\" element for tabs.\n\t\t\t\tfont: 500 13px/22px $font;\n\t\t\t\tletter-spacing: $font--letter-spacing;\n\t\t\t\ttext-decoration: none;\n\t\t\t\t> *:not(input) {\n\t\t\t\t\tpointer-events: none;\n\t\t\t\t}\n\n\t\t\t\t&,\n\t\t\t\t&:focus,\n\t\t\t\t&:hover,\n\t\t\t\t&:active,\n\t\t\t\t&:visited {\n\t\t\t\t\toutline: none; // Make sure outline blur is disabled when using \"button\" element for tabs.\n\t\t\t\t\tbackground-color: $side-tabs--label-background;\n\t\t\t\t\tcolor: $side-tabs--label-color;\n\t\t\t\t}\n\n\t\t\t\tinput {\n\t\t\t\t\t@extend %sui-screen-reader-text;\n\t\t\t\t}\n\n\t\t\t\t&:first-child {\n\t\t\t\t\tborder-radius: $border-radius 0 0 $border-radius;\n\t\t\t\t}\n\n\t\t\t\t&:last-child {\n\t\t\t\t\tmargin: 0;\n\t\t\t\t\tborder-radius: 0 $border-radius $border-radius 0;\n\t\t\t\t}\n\n\t\t\t\t&.active {\n\t\t\t\t\tbackground-color: $side-tabs--label-active-background;\n\t\t\t\t\tcolor: $side-tabs--label-active-color;\n\t\t\t\t}\n\n\t\t\t\t@media (forced-colors: active) {\n\t\t\t\t\tforced-color-adjust: none;\n\t\t\t\t}\n\t\t\n\t\t\t\t@media (forced-colors: active) and (prefers-color-scheme: dark), (-ms-high-contrast: white-on-black) {\n\t\t\t\t\tbackground-color: $grey !important;\n\t\t\t\t\tcolor: $cloud !important;\n\t\t\t\t\t&.active {\n\t\t\t\t\t\tbackground-color: $nightrider !important;\n\t\t\t\t\t\tcolor: map-get($button-background, orange) !important;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t> [data-panes],\n\t\t> .sui-tabs-content {\n\n\t\t\t> *,\n\t\t\t> .sui-tab-content {\n\t\t\t\tdisplay: none;\n\t\t\t\tmargin: 10px 0 0;\n\n\t\t\t\t&.sui-tab-boxed {\n\t\t\t\t\tpadding: $sui-gutter-md;\n\t\t\t\t\tborder: 1px solid #E6E6E6;\n\t\t\t\t\tborder-radius: $border-radius;\n\n\t\t\t\t\t@include media(min-width, md) {\n\t\t\t\t\t\tpadding: $sui-gutter;\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t&.active {\n\t\t\t\t\tdisplay: block;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t// VARIATION: Large\n\t\t\t// This class will increase the space\n\t\t\t// between data-tabs and data-panes.\n\t\t\t&.sui-tabs-content-lg {\n\n\t\t\t\t> *,\n\t\t\t\t> .sui-tab-content {\n\t\t\t\t\tmargin-top: 30px;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t&:last-child {\n\t\t\tmargin: 0;\n\n\t\t\t@include media(min-width, md) {\n\t\t\t\tmargin: 0;\n\t\t\t}\n\t\t}\n    \n\t\t// Removing unnecessary padding and border\n\t\tfieldset.sui-form-field{\n\t\t\tpadding: 0;\n\t\t\tborder: none;\n\t\t}\n\n\t\t@include media(min-width, md) {\n\t\t\tmargin: 0 0 $sui-gutter;\n\t\t}\n\t}\n\n\t// Flushed tabs inside box\n\t.sui-box-body {\n\n\t\t> .sui-tabs-flushed {\n\n\t\t\t> [data-tabs],\n\t\t\t> .sui-tabs-menu {\n\t\t\t\tmargin: 0 $sui-gutter-md;\n\n\t\t\t\t@include media(min-width, md) {\n\t\t\t\t\tmargin: 0 $sui-gutter;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t> [data-panes],\n\t\t\t> .sui-tabs-content {\n\n\t\t\t\t> *,\n\t\t\t\t> .sui-tab-content {\n\t\t\t\t\tpadding: $sui-gutter-md;\n\n\t\t\t\t\t@include media(min-width, md) {\n\t\t\t\t\t\tpadding: $sui-gutter;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t@include media(max-width, md) {\n\t\t\t\tmargin: -#{$sui-gutter-md} !important;\n\t\t\t}\n\n\t\t\t@include media(min-width, md) {\n\t\t\t\tmargin: -#{$sui-gutter} !important;\n\t\t\t}\n\t\t}\n\t}\n}\n", "// $sui-wrap: true | $rtl: false\n@include body-class(true, false) {\n\tposition: relative;\n\n\t.sui-row-with-sidenav {\n\t\twidth: 100%;\n\t\tdisplay: table;\n\t\ttable-layout: fixed;\n\t\tclear: both;\n\t\tmargin-bottom: 30px;\n\n\t\t.sui-sidenav {\n\t\t\tvertical-align: top;\n\n\t\t\t.sui-vertical-tabs {\n\t\t\t\tmargin: 15px 0 0;\n\t\t\t\tpadding: 0;\n\t\t\t\tborder: 0;\n\t\t\t\tlist-style: none;\n\n\t\t\t\t.sui-vertical-tab {\n\t\t\t\t\tposition: relative;\n\t\t\t\t\tmargin: 0 0 6px;\n\t\t\t\t\tpadding: 5px 15px;\n\t\t\t\t\tline-height: 1.5em;\n\t\t\t\t\tlist-style: none;\n\n\t\t\t\t\t&.current {\n\t\t\t\t\t\tbackground-color: $sidenav-tab-active-bg-color;\n\t\t\t\t\t\tborder-radius: 20px;\n\n\t\t\t\t\t\ta {\n\t\t\t\t\t\t\tcolor: $sidenav-tab-active-color;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\n\t\t\t\t\t.sui-tag,\n\t\t\t\t\t[class*=\"sui-icon-\"] {\n\t\t\t\t\t\tposition: absolute;\n\t\t\t\t\t}\n\n\t\t\t\t\t.sui-tag {\n\t\t\t\t\t\ttop: 2px;\n\t\t\t\t\t\tright: 3px;\n\t\t\t\t\t}\n\n\t\t\t\t\t[class*=\"sui-icon-\"] {\n\t\t\t\t\t\ttop: 6px;\n\t\t\t\t\t\tright: 7px;\n\n\t\t\t\t\t\t&:before {\n\t\t\t\t\t\t\tfont-size: 16px;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\ta {\n\t\t\t\t\tdisplay: block;\n\t\t\t\t\tcolor: $sidenav-tab-color;\n\t\t\t\t\tfont-weight: 500;\n\t\t\t\t\ttransition: 0.3s ease;\n\n\t\t\t\t\t&:hover,\n\t\t\t\t\t&:focus {\n\t\t\t\t\t\tcolor: $sidenav-tab-color-hover;\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t&.sui-alt-design {\n\n\t\t\t\t\t.sui-vertical-tab {\n\t\t\t\t\t\theight: auto;\n\t\t\t\t\t\tpadding: 0;\n\t\t\t\t\t\tbackground-color: transparent;\n\t\t\t\t\t\tline-height: auto;\n\n\t\t\t\t\t\ta {\n\t\t\t\t\t\t\tpadding: 5px 15px;\n\t\t\t\t\t\t\tborder-radius: 20px;\n\t\t\t\t\t\t\tbackground-color: transparent;\n\n\t\t\t\t\t\t\t&.current {\n\t\t\t\t\t\t\t\tbackground-color: $sidenav-tab-active-bg-color;\n\t\t\t\t\t\t\t\tcolor: $sidenav-tab-active-color;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t@include media(max-width, lg) {\n\t\t\t\t\tmargin-bottom: 15px;\n\t\t\t\t}\n\n\t\t\t\t@include media(min-width, lg) {\n\t\t\t\t\tmargin-bottom: 30px;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t.sui-with-floating-input {\n\n\t\t\t\t@include media(max-width, lg) {\n\t\t\t\t\tleft: 0 !important;\n\t\t\t\t}\n\n\t\t\t\t@include media(min-width, lg) {\n\t\t\t\t\twidth: 240px;\n\t\t\t\t\tposition: absolute;\n\t\t\t\t\ttop: 0;\n\t\t\t\t\tleft: #{$sidenav-width + 10px};\n\t\t\t\t\tmargin: 0;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t.sui-sidenav-settings {\n\n\t\t\t\t.sui-form-field {\n\t\t\t\t\tmargin: 0 0 10px;\n\n\t\t\t\t\tselect.sui-select + .sui-select {\n\t\t\t\t\t\tmargin-bottom: 0;\n\t\t\t\t\t}\n\n\t\t\t\t\t&:last-child {\n\t\t\t\t\t\tmargin: 0;\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t@include media(max-width, lg) {\n\t\t\t\t\tmargin-bottom: $sui-gutter-md;\n\t\t\t\t}\n\n\t\t\t\t@include media(min-width, lg) {\n\t\t\t\t\tpadding-left: 15px;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t// FIX:\n\t\t\t// Hide elements on large screens (only)\n\t\t\t.sui-sidenav-hide-md {\n\n\t\t\t\t@include media(max-width, lg) {\n\t\t\t\t\tdisplay: none;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t// FIX:\n\t\t\t// Show elements on large screens (only)\n\t\t\t.sui-sidenav-hide-lg {\n\n\t\t\t\t@include media(min-width, lg) {\n\t\t\t\t\tdisplay: none;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t@include media(max-width, lg) {\n\t\t\t\twidth: 100%;\n\t\t\t\tdisplay: block;\n\t\t\t}\n\n\t\t\t@include media(min-width, lg) {\n\t\t\t\twidth: $sidenav-width;\n\t\t\t\tdisplay: table-cell;\n\t\t\t\tpadding-right: $sui-gutter;\n\t\t\t}\n\t\t}\n\n\t\t.sui-sidenav-sticky {\n\n\t\t\t@include media(min-width, lg) {\n\t\t\t\tposition: sticky !important;\n\t\t\t\ttop: #{32px + 15px};\n\t\t\t\tz-index: 11;\n\t\t\t}\n\t\t}\n\n\t\t> div:not(.sui-sidenav) {\n\t\t\tdisplay: block;\n\t\t\tposition: relative;\n\t\t\tvertical-align: top;\n\t\t\tmargin-bottom: 0; // FIX: To prevent double margins at bottom\n\t\t}\n\n\t\t@media (max-width: 1100px) {\n\t\t\tdisplay: block;\n\t\t}\n\t}\n}\n\n// $sui-wrap: true | $rtl: true\n@include body-class(true, true) {\n\n\t.sui-row-with-sidenav {\n\n\t\t.sui-sidenav {\n\n\t\t\t.sui-vertical-tabs {\n\n\t\t\t\t.sui-vertical-tab {\n\n\t\t\t\t\t.sui-tag,\n\t\t\t\t\t[class*=\"sui-icon-\"] {\n\t\t\t\t\t\tright: auto;\n\t\t\t\t\t\tmargin-left: 0;\n\t\t\t\t\t}\n\n\t\t\t\t\t.sui-tag {\n\t\t\t\t\t\tleft: 0;\n\t\t\t\t\t}\n\n\t\t\t\t\t[class*=\"sui-icon-\"] {\n\t\t\t\t\t\tleft: 7px;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t.sui-with-floating-input {\n\n\t\t\t\t@include media(min-width, lg) {\n\t\t\t\t\tright: #{$sidenav-width + 10px};\n\t\t\t\t\tleft: auto;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t@include media(min-width, lg) {\n\t\t\t\tpadding-right: 0;\n\t\t\t\tpadding-left: $sui-gutter;\n\t\t\t}\n\t\t}\n\t}\n}\n", "@include body-class(true) {\n\n\t.sui-dropdown {\n\t\tposition: relative;\n\t\tdisplay: inline-block;\n\t\tmargin: 0 10px;\n\n\t\tul {\n\t\t\tmin-width: 170px;\n\t\t\tdisplay: none;\n\t\t\tposition: absolute;\n\t\t\tz-index: 10;\n\t\t\ttop: 100%;\n\t\t\tright: -10px;\n\t\t\tleft: auto;\n\t\t\tmargin: 15px 0 0;\n\t\t\tpadding: 15px 0;\n\t\t\tborder: 1px solid palette(gray, lighter);\n\t\t\tborder-radius: $border-radius;\n\t\t\tbackground-color: $white;\n\t\t\tbox-shadow: 0 3px 7px 0 rgba(0, 0, 0, 0.07);\n\t\t\ttransition: 0.3s ease;\n\t\t\twhite-space: nowrap;\n\n\t\t\t&:before,\n\t\t\t&:after {\n\t\t\t\tcontent: \" \";\n\t\t\t\twidth: 0;\n\t\t\t\tposition: absolute;\n\t\t\t\tright: 15px;\n\t\t\t\tborder-width: 0 8px 10px;\n\t\t\t\tborder-style: solid;\n\t\t\t\tborder-top-color: palette(gray, lighter);\n\t\t\t}\n\n\t\t\t&:before {\n\t\t\t\tz-index: 10;\n\t\t\t\ttop: -10px;\n\t\t\t\tborder-color: palette(gray, lighter) transparent;\n\t\t\t}\n\n\t\t\t&:after {\n\t\t\t\tz-index: 11;\n\t\t\t\ttop: -8px;\n\t\t\t\tborder-color: $white transparent;\n\t\t\t}\n\n\t\t\t&, li {\n\t\t\t\tlist-style: none;\n\t\t\t}\n\n\t\t\tli {\n\t\t\t\tdisplay: block;\n\t\t\t\tmargin: 0;\n\t\t\t\tborder: 0;\n\n\t\t\t\ta, button, > span {\n\n\t\t\t\t\t&,\n\t\t\t\t\t&:focus,\n\t\t\t\t\t&:hover,\n\t\t\t\t\t&:active {\n\t\t\t\t\t\tmargin: 0;\n\t\t\t\t\t\tpadding: 0 15px;\n\t\t\t\t\t\tborder: 0;\n\t\t\t\t\t\toutline: none;\n\t\t\t\t\t\tbox-shadow: none;\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\ta, button {\n\t\t\t\t\twidth: 100%;\n\t\t\t\t\tcursor: pointer;\n\t\t\t\t\tdisplay: block;\n\t\t\t\t\tfont: 500 13px/30px $font;\n\t\t\t\t\ttext-align: left;\n\t\t\t\t\ttransition: 0.3s ease;\n\n\t\t\t\t\t&,\n\t\t\t\t\t&:visited {\n\t\t\t\t\t\tbackground-color: transparent;\n\t\t\t\t\t\tcolor: $grey;\n\t\t\t\t\t}\n\n\t\t\t\t\t&:hover,\n\t\t\t\t\t&:focus {\n\t\t\t\t\t\tbackground-color: rgba(51, 51, 51, 0.05);\n\t\t\t\t\t\tcolor: $ironmike;\n\t\t\t\t\t}\n\n\t\t\t\t\t&.sui-option-red {\n\n\t\t\t\t\t\t&,\n\t\t\t\t\t\t&:focus,\n\t\t\t\t\t\t&:visited {\n\t\t\t\t\t\t\tbackground-color: transparent;\n\t\t\t\t\t\t\tcolor: palette(red, default);\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t&:hover,\n\t\t\t\t\t\t&:active {\n\t\t\t\t\t\t\tbackground-color: palette(red, light);\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t[class*=\"sui-icon-\"] {\n\t\t\t\t\twidth: 24px;\n\t\t\t\t\tmargin-right: 4px;\n\t\t\t\t\tfont-size: 12px;\n\t\t\t\t\ttext-align: center;\n\n\t\t\t\t\t&:before {\n\t\t\t\t\t\tdisplay: block;\n\t\t\t\t\t\tcolor: inherit;\n\t\t\t\t\t\ttransition: 0.3s ease;\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t&.active,\n\t\t\t\t&.current {\n\n\t\t\t\t\ta, button {\n\t\t\t\t\t\tbackground-color: $grey;\n\t\t\t\t\t\tcolor: $white;\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t&.sui-dropdown-label {\n\t\t\t\t\tborder-bottom: 1px solid $dropdown-label-border-color;\n\t\t\t\t\tline-height: 30px;\n\t\t\t\t\tcursor: default;\n\t\t\t\t\tcolor: $gray;\n\t\t\t\t}\n\n\t\t\t\t// FIX:\n\t\t\t\t// In some cases, there are buttons that are wrapped inside a form\n\t\t\t\t// to execute special actions.\n\t\t\t\tform {\n\t\t\t\t\tdisplay: block;\n\t\t\t\t\tmargin: 0;\n\t\t\t\t\tpadding: 0;\n\t\t\t\t\tborder: 0;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t&:first-child {\n\t\t\tmargin-left: 0;\n\t\t}\n\n\t\t&:last-child {\n\t\t\tmargin-right: 0;\n\t\t}\n\n\t\t// POSITION: Right\n\t\t&.sui-dropdown-right {\n\n\t\t\tul {\n\t\t\t\tright: unset;\n\t\t\t\tleft: -10px;\n\n\t\t\t\t&:before,\n\t\t\t\t&:after {\n\t\t\t\t\tright: unset;\n\t\t\t\t\tleft: 15px;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t// POSITION: Center\n\t\t&.sui-dropdown-center {\n\n\t\t\tul {\n\t\t\t\tright: unset;\n\t\t\t\tleft: 50%;\n\t\t\t\ttransform: translateX(-50%);\n\n\t\t\t\t&:before,\n\t\t\t\t&:after {\n\t\t\t\t\tright: unset;\n\t\t\t\t\tleft: 50%;\n\t\t\t\t\ttransform: translateX(-50%);\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t// Extra fixes\n\t\t&.wds-item-loading {\n\t\t\tfont-size: 18px;\n\n\t\t\t.sui-dropdown-anchor {\n\t\t\t\tvisibility: hidden;\n\t\t\t}\n\t\t}\n\n\t\t// When menu is open\n\t\t&.open {\n\n\t\t\t.sui-dropdown-anchor {\n\n\t\t\t\t&,\n\t\t\t\t&:hover,\n\t\t\t\t&:focus,\n\t\t\t\t&:active,\n\t\t\t\t&:visited {\n\t\t\t\t\tbackground-color: $silver;\n\t\t\t\t\tcolor: palette( gray, dark );\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tul {\n\t\t\t\tdisplay: block;\n\t\t\t}\n\t\t}\n\t}\n}", "@use \"sass:math\";\n\n// Calculate length of stroke in svg circle.\n$score-circle-radius: 42; // r attribute on the score svg circle\n$score-circle-circumference: 2 * pi() * $score-circle-radius;\n\n// Generate animation keyframes for score circles.\n@for $i from 1 through 100 {\n\t$dash-length: math.div($score-circle-circumference, 100) * $i;\n\t$gap-length: $dash-length * 100 - $i;\n\n\t@keyframes sui#{$i} {\n\t\tto {\n\t\t\tstroke-dasharray: $dash-length $gap-length;\n\t\t}\n\t}\n}\n\n@include body-class(true) {\n\n\t.sui-circle-score {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\theight: $circle-score-sm;\n\t\twidth: auto;\n\t\topacity: 0;\n\t\ttransition: all .4s;\n\n\t\tsvg {\n\t\t\theight: 100%;\n\t\t\ttransform: rotate(-90deg);\n\t\t\ttransform-origin: center;\n\n\t\t\tcircle {\n\t\t\t\tfill: none;\n\t\t\t\tstroke-linecap: butt;\n\n\t\t\t\t// Background.\n\t\t\t\t&:first-child {\n\t\t\t\t\tstroke: $circle-score-bg-color;\n\t\t\t\t}\n\n\t\t\t\t// Dial.\n\t\t\t\t&:last-child {\n\t\t\t\t\tstroke: $circle-score-default-dial-color;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t&.loaded {\n\t\t\topacity: 1;\n\t\t}\n\t}\n\n\t.sui-circle-score-label {\n\t\tmargin-left: $circle-score-label-spacing;\n\t\tfont-weight: 500;\n\t}\n\n\t.sui-circle-score-sm {\n\t\theight: $circle-score-sm;\n\t}\n\n\t.sui-circle-score-lg {\n\t\theight: $circle-score-lg;\n\n\t\t.sui-circle-score-label {\n\t\t\tdisplay: none;\n\t\t}\n\t}\n\n\t.sui-grade-success,\n\t.sui-grade-aplus,\n\t.sui-grade-a,\n\t.sui-grade-b {\n\t\tsvg circle:last-child {\n\t\t\tstroke: $circle-score-success-color;\n\t\t}\n\t}\n\n\t.sui-grade-warning,\n\t.sui-grade-c,\n\t.sui-grade-d {\n\t\tsvg circle:last-child {\n\t\t\tstroke: $circle-score-warning-color;\n\t\t}\n\t}\n\n\t.sui-grade-error,\n\t.sui-grade-e,\n\t.sui-grade-f {\n\t\tsvg circle:last-child {\n\t\t\tstroke: $circle-score-error-color;\n\t\t}\n\t}\n\n\t.sui-grade-dismissed,\n\t.sui-grade-disabled {\n\t\tsvg circle:last-child {\n\t\t\tstroke: $circle-score-disabled-color;\n\t\t}\n\t}\n\n\t.sui-grade-default {\n\t\tsvg circle:last-child {\n\t\t\tstroke: $gray;\n\t\t}\n\t}\n}\n", "@include body-class(true) {\n\n\t.sui-footer {\n\t\tmargin-top: #{$sui-gutter-md * 2};\n\t\tcolor: $footer-color;\n\t\tfont: $font--medium 12px/22px $font;\n\t\tletter-spacing: $font--letter-spacing;\n\t\ttext-align: center;\n\n\t\t[class*=\"sui-icon-\"] {\n\t\t\tmargin: 0 3px;\n\t\t\tfont-size: 11px;\n\n\t\t\t&:before {\n\t\t\t\tdisplay: block;\n\t\t\t\tcolor: $footer-color;\n\t\t\t}\n\t\t}\n\n\t\t@include media(min-width, md) {\n\t\t\tmargin-top: #{$sui-gutter * 2};\n\t\t}\n\t}\n\n\t.sui-footer-nav,\n\t.sui-footer-social {\n\t\tdisplay: flex;\n\t\tjustify-content: center;\n\t\tmargin: $sui-gutter-md 0 0;\n\t\ttext-align: center;\n\n\t\t&, li {\n\t\t\tpadding: 0;\n\t\t\tborder: 0;\n\t\t\tlist-style: none;\n\t\t}\n\n\t\tli {\n\t\t\tdisplay: inline-flex;\n\t\t\tmargin: 0;\n\t\t\tcolor: $footer-color;\n\t\t\tfont: 400 12px/16px $font;\n\n\t\t\ta {\n\t\t\t\tdisplay: block;\n\t\t\t\tcolor: $footer-color;\n\t\t\t\tfont-weight: 400;\n\t\t\t\ttransition: $transition;\n\n\t\t\t\t[class*=\"sui-icon-\"] {\n\t\t\t\t\twidth: 30px;\n\t\t\t\t\ttext-align: center;\n\n\t\t\t\t\t&:before {\n\t\t\t\t\t\tdisplay: block;\n\t\t\t\t\t\tcolor: inherit;\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t&,\n\t\t\t\t&:hover,\n\t\t\t\t&:active {\n\t\t\t\t\toutline: none;\n\t\t\t\t\tbox-shadow: none;\n\t\t\t\t}\n\n\t\t\t\t&:hover,\n\t\t\t\t&:active {\n\t\t\t\t\tcolor: $footer-color-hover;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t@include media(max-width, md) {\n\t\t\tflex-wrap: wrap;\n\t\t}\n\n\t\t@include media(min-width, md) {\n\t\t\tmargin-top: $sui-gutter;\n\t\t}\n\t}\n\n\t.sui-footer-nav {\n\n\t\tli:not(:last-child) {\n\n\t\t\t&:after {\n\t\t\t\tcontent: \"/\";\n\t\t\t\tmargin-right: 6px;\n\t\t\t\tmargin-left: 6px;\n\t\t\t}\n\t\t}\n\t}\n\n\t.sui-footer-social {\n\n\t\tli {\n\t\t\tmargin: 0 5px;\n\n\t\t\t&:first-child {\n\t\t\t\tmargin-left: 0;\n\t\t\t}\n\n\t\t\t&:last-child {\n\t\t\t\tmargin-right: 0;\n\t\t\t}\n\t\t}\n\t}\n\n\t#sui-cross-sell-footer {\n\n\t\t> div {\n\t\t\twidth: 100%;\n\t\t\ttext-align: center;\n\t\t\tborder-bottom: 1px solid $footer-cross-sell-border-color;\n\t\t\tline-height: 0.1em;\n\t\t\tmargin: 10px 0 40px;\n\t\t\t> span {\n\t\t\t\tposition: relative;\n\t\t\t\tcolor: $footer-cross-sell-icon-color;\n\t\t\t\tbackground-color: #f1f1f1;\n\t\t\t\tpadding: 0 30px;\n\t\t\t\tfont-size: 16px;\n\t\t\t\ttop: 8px;\n\t\t\t}\n\t\t}\n\n\t\th3 {\n\t\t\tfont-size: 22px;\n\t\t\tfont-weight: bold;\n\t\t\tline-height: 30px;\n\t\t\ttext-transform: none;\n\t\t}\n\t}\n\n\t.sui-cross-sell-modules {\n\t\tmargin-top: 30px;\n\n\t\t.sui-col-md-4 {\n\n\t\t\t@media (min-width: 600px) and (max-width: 783px) {\n\t\t\t\tdisplay: flex;\n\t\t\t}\n\t\t}\n\n\t\t[class*=\"sui-cross-\"] {\n\t\t\tmin-height: 150px;\n\t\t\tborder-radius: $border-radius $border-radius 0 0;\n\t\t\tbackground-size: cover;\n\t\t\tbackground-position: center;\n\t\t\tposition: relative;\n\n\t\t\t> span {\n\t\t\t\twidth: 56px;\n\t\t\t\theight: 56px;\n\t\t\t\tdisplay: block;\n\t\t\t\tposition: absolute;\n\t\t\t\ttop: 50%;\n\t\t\t\tleft: 50%;\n\t\t\t\ttransform: translate(-50%, -50%);\n\n\t\t\t\t@media (max-width: 600px) {\n\t\t\t\t\tz-index: 1;\n\t\t\t\t\ttop: 45px;\n\t\t\t\t\tleft: 50%;\n\t\t\t\t\ttransform: translateX(-50%);\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t@media (min-width: 600px) and (max-width: 783px) {\n\t\t\t\twidth: 180px;\n\t\t\t\tflex: none;\n\t\t\t\tborder-radius: $border-radius 0 0 $border-radius;\n\t\t\t}\n\n\t\t\t@media (max-width: 600px) {\n\t\t\t\theight: 80px;\n\t\t\t\tmin-height: auto;\n\t\t\t}\n\t\t}\n\n\t\t@if variable-exists(cross-sell-1) {\n\n\t\t\t@if $cross-sell-1 != '' {\n\n\t\t\t\t.sui-cross-1 {\n\n\t\t\t\t\t> span {\n\t\t\t\t\t\t@include background-2x(\"#{$sui-image-path}plugins-#{$cross-sell-1}-icon\", 'png', 50px, 50px, center center, no-repeat);\n\t\t\t\t\t}\n\t\t\n\t\t\t\t\tbackground-image: url(\"#{$sui-image-path}plugins-#{$cross-sell-1}.jpg\");\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t@if variable-exists(cross-sell-2) {\n\n\t\t\t@if $cross-sell-2 != '' {\n\n\t\t\t\t.sui-cross-2 {\n\n\t\t\t\t\t> span {\n\t\t\t\t\t\t@include background-2x(\"#{$sui-image-path}plugins-#{$cross-sell-2}-icon\", 'png', 50px, 50px, center center, no-repeat);\n\t\t\t\t\t}\n\t\t\n\t\t\t\t\tbackground-image: url(\"#{$sui-image-path}plugins-#{$cross-sell-2}.jpg\");\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t@if variable-exists(cross-sell-3) {\n\n\t\t\t@if $cross-sell-3 != '' {\n\n\t\t\t\t.sui-cross-3 {\n\n\t\t\t\t\t> span {\n\t\t\t\t\t\t@include background-2x(\"#{$sui-image-path}plugins-#{$cross-sell-3}-icon\", 'png', 50px, 50px, center center, no-repeat);\n\t\t\t\t\t}\n\t\t\n\t\t\t\t\tbackground-image: url(\"#{$sui-image-path}plugins-#{$cross-sell-3}.jpg\");\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t.sui-box {\n\t\t\tmargin-bottom: 0;\n\n\t\t\th3 {\n\t\t\t\tmargin-top: 0;\n\t\t\t\tfont-size: 18px;\n\t\t\t\tline-height: 30px;\n\t\t\t\tfont-weight: 700;\n\t\t\t\ttext-transform: none;\n\n\t\t\t\t@media(min-width: 600px) and (max-width: 783px) {\n\t\t\t\t\tmargin: 0;\n\t\t\t\t\tmin-height: auto;\n\t\t\t\t}\n\n\t\t\t\t@media(max-width: 600px) {\n\t\t\t\t\tmin-height: auto;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t.sui-box-body {\n\t\t\t\tpadding: 30px;\n\t\t\t\tborder-radius: 0 0 $border-radius $border-radius;\n\t\t\t\ttext-align: center;\n\n\t\t\t\tp {\n\t\t\t\t\tmargin-bottom: 30px;\n\t\t\t\t\tcolor: $footer-cross-sell-p-color;\n\t\t\t\t\tfont-size: 13px;\n\t\t\t\t\tline-height: 22px;\n\n\t\t\t\t\t@media(min-width: 600px) and (max-width: 783px) {\n\t\t\t\t\t\tmargin-bottom: 14px;\n\t\t\t\t\t}\n\n\t\t\t\t\t@media(max-width: 600px) {\n\t\t\t\t\t\tmargin-bottom: 20px;\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t@media(min-width: 600px) and (max-width: 783px) {\n\t\t\t\t\ttext-align: left;\n\t\t\t\t}\n\n\t\t\t\t@media(max-width: 600px) {\n\t\t\t\t\tpadding: 26px 20px 20px 20px;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t@media(min-width: 600px) and (max-width: 783px) {\n\t\t\t\tborder-radius: 0 $border-radius $border-radius 0;\n\t\t\t}\n\t\t}\n\t}\n\n\t.sui-cross-sell-bottom {\n\t\ttext-align: center;\n\t\tmargin-top: 50px;\n\n\t\th3 {\n\t\t\tfont-size: 22px;\n\t\t\tfont-weight: bold;\n\t\t\tline-height: 30px;\n\t\t\ttext-transform: none;\n\t\t}\n\n\t\tp {\n\t\t\tmax-width: 500px;\n\t\t\tmargin: 20px auto 30px;\n\t\t}\n\t\t.sui-button {\n\t\t\tmargin: 0;\n\t\t}\n\n\t\timg {\n\t\t\tdisplay: block;\n\t\t\theight: auto;\n\t\t\tmax-width: 100%;\n\t\t\tmargin: 30px auto 0;\n\t\t}\n\t}\n}\n\n// Remove WordPress footer\n@include body-class() {\n\n\t#wpfooter {\n\t\tdisplay: none;\n\t}\n}", "@use \"sass:math\";\n\n@include body-class(true) {\n\n\t.sui-progress {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\n\t\t.sui-progress-icon {\n\t\t\twidth: 26px;\n\t\t\ttext-align: center;\n\n\t\t\t[class*=\"sui-icon-\"] {\n\t\t\t\tdisplay: block;\n\t\t\t\tfont-size: 16px;\n\n\t\t\t\t&:before {\n\t\t\t\t\tdisplay: block;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t+ .sui-progress-text {\n\t\t\t\tmargin-left: 0;\n\t\t\t}\n\n\t\t\t+ .sui-progress-bar {\n\t\t\t\tmargin-left: 5px;\n\t\t\t}\n\t\t}\n\n\t\t.sui-progress-text {\n\t\t\tmin-width: 40px;\n\t\t\tflex: 0 0 auto;\n\t\t\tcolor: $progress-text--color;\n\t\t\tfont: bold 12px/20px $font;\n\t\t\tletter-spacing: $font--letter-spacing;\n\t\t\ttext-align: center;\n\n\t\t\tspan {\n\t\t\t\tdisplay: block;\n\t\t\t}\n\n\t\t\t+ .sui-progress-bar {\n\t\t\t\tmargin-left: 5px;\n\t\t\t}\n\t\t}\n\n\t\t.sui-progress-bar {\n\t\t\theight: $progress-bar--size;\n\t\t\toverflow: hidden;\n\t\t\tflex: 1;\n\t\t\tborder-radius: div($progress-bar--size, 2);\n\t\t\tbackground-color: $progress-bar--background;\n\n\t\t\tspan {\n\t\t\t\theight: 100%;\n\t\t\t\tdisplay: block;\n\t\t\t\tbackground-color: $progress-bar--loading-color;\n\t\t\t}\n\t\t}\n\t}\n\n\t.sui-progress-block {\n\t\twidth: 100%;\n\t\tmax-width: 100%;\n\t\tmin-height: $progress-block--size;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tpadding: math.div(($progress-block--size - 32px), 2) 14px;\n\t\tborder: 1px solid $progress-block--border-color;\n\t\tborder-radius: $border-radius;\n\t\tbackground-color: $progress-block--background;\n\n\t\t.sui-progress {\n\t\t\tflex: 1;\n\n\t\t\t+ .sui-button,\n\t\t\t+ .sui-button-icon {\n\t\t\t\tmargin-left: 10px;\n\t\t\t}\n\t\t}\n\n\t\t.sui-button,\n\t\t.sui-button-icon {\n\t\t\tflex: 0 0 auto;\n\t\t}\n\n\t\t.sui-button-icon,\n\t\ta.sui-button-icon,\n\t\tbutton.sui-button-icon {\n\n\t\t\t[class*=\"sui-icon-\"] {\n\t\t\t\tfont-size: 16px;\n\t\t\t}\n\t\t}\n\n\t\t+ .sui-progress-state {\n\t\t\tmargin-top: 10px;\n\t\t}\n\t}\n\n\t.sui-progress-state {\n\t\tdisplay: block;\n\t\tcolor: $progress-status--color;\n\t\tfont: 400 13px/22px $font;\n\t\tletter-spacing: $font--letter-spacing;\n\t\ttext-align: center;\n\n\t\tspan {\n\t\t\tdisplay: block;\n\t\t}\n\t}\n}\n", "@use \"sass:math\";\n\n@include body-class {\n\n\t// NOTE: New modal.\n\t.sui-modal {\n\t\twidth: calc(100% - #{map-get($wordpress, adminmenu)});\n\t\theight: calc(100vh - #{map-get($wordpress, adminbar)});\n\t\toverflow-x: hidden;\n\t\toverflow-y: auto;\n\t\tuser-select: auto;\n\t\tdisplay: none;\n\t\tflex-direction: column;\n\t\talign-items: center;\n\t\tposition: fixed;\n\t\tz-index: map-get($modal, z-index);\n\t\ttop: map-get($wordpress, adminbar);\n\t\tleft: map-get($wordpress, adminmenu);\n\t\tpadding: $sui-gutter 0;\n\t\tbackground-color: $modal-overlay-bg-color;\n\n\t\t&, * {\n\t\t\tbox-sizing: border-box;\n\t\t}\n\n\t\t// ELEMENT: Overlay mask.\n\t\t.sui-modal-overlay {\n\t\t\t// width: calc(100% - #{map-get($wordpress, adminmenu)});\n\t\t\theight: calc(100vh - #{map-get($wordpress, adminbar)});\n\t\t\tposition: fixed;\n\t\t\tz-index: #{map-get($modal, z-index) - 1};\n\t\t\ttop: map-get($wordpress, adminbar);\n\t\t\tleft: map-get($wordpress, adminmenu);\n\t\t\tright: #{($scrollbar--width)};\n\n\t\t\t@include media(max-width, md) {\n\t\t\t\tright: 0;\n\t\t\t}\n\n\t\t\t&:hover {\n\t\t\t\tcursor: pointer;\n\t\t\t\toutline: none;\n\t\t\t\tbox-shadow: none;\n\t\t\t}\n\n\t\t\t+ .sui-modal-content {\n\t\t\t\tz-index: map-get($modal, z-index);\n\t\t\t}\n\t\t}\n\n\t\t// ELEMENT: Content. \n\t\t.sui-modal-content {\n\t\t\twidth: 100%;\n\t\t\tmin-height: 0;\n\t\t\tflex: 0 0 auto;\n\t\t\tposition: relative;\n\t\t\tmargin: auto;\n\t\t\tpadding: 0 $sui-gutter;\n\n\t\t\t@include media(max-width, md) {\n\t\t\t\tpadding: 0 $sui-gutter-md;\n\t\t\t}\n\t\t}\n\n\t\t// ELEMENT: Slide.\n\t\t.sui-modal-slide {\n\t\t\tdisplay: none;\n\n\t\t\t&.sui-active {\n\t\t\t\topacity: 0;\n\t\t\t\tdisplay: block;\n\t\t\t\tanimation-duration: 0.7s;\n\t\t\t\tanimation-fill-mode: both;\n\t\t\t\ttransform-origin: center;\n\t\t\t\ttransform-style: preserve-3d;\n\n\t\t\t\t&.sui-loaded {\n\t\t\t\t\topacity: 1;\n\t\t\t\t}\n\n\t\t\t\t&.sui-fadein {\n\t\t\t\t\topacity: 1;\n\t\t\t\t\tanimation-name: fadeIn;\n\n\t\t\t\t\t@media screen and (prefers-reduced-motion: reduce) {\n\t\t\t\t\t\topacity: 1;\n\t\t\t\t\t\tanimation-name: none;\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t&.sui-fadein-left {\n\t\t\t\t\topacity: 1;\n\t\t\t\t\tanimation-name: fadeInLeft;\n\n\t\t\t\t\t@media screen and (prefers-reduced-motion: reduce) {\n\t\t\t\t\t\topacity: 1;\n\t\t\t\t\t\tanimation-name: none;\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t&.sui-fadein-right {\n\t\t\t\t\topacity: 1;\n\t\t\t\t\tanimation-name: fadeInRight;\n\n\t\t\t\t\t@media screen and (prefers-reduced-motion: reduce) {\n\t\t\t\t\t\topacity: 1;\n\t\t\t\t\t\tanimation-name: none;\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t@media screen and (prefers-reduced-motion: reduce) {\n\t\t\t\t\tanimation-duration: 0.001ms;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t&:disabled,\n\t\t\t&[disabled] {\n\n\t\t\t\ta,\n\t\t\t\tinput,\n\t\t\t\tselect,\n\t\t\t\tbutton,\n\t\t\t\ttextarea {\n\t\t\t\t\topacity: 0.5;\n\t\t\t\t\tpointer-events: none;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t// ELEMENT: Box.\n\t\t.sui-box {\n\t\t\tbox-shadow: none;\n\n\t\t\t// ELEMENT: Icon\n\t\t\t[class*=sui-icon-] + .sui-box-title{\n\t\t\t\tmargin-top: 15px;\n\t\t\t}\n\n\t\t\t// ELEMENT: Box Title.\n\t\t\t.sui-box-title {\n\n\t\t\t\t&.sui-lg {\n\t\t\t\t\tfont-size: 22px;\n\t\t\t\t\tline-height: 30px;\n\t\t\t\t}\n\n\t\t\t\t&.sui-no-ellipses {\n\t\t\t\t\twhite-space: normal;\n\t\t\t\t}\n\n\t\t\t\t+ .sui-description {\n\t\t\t\t\tmargin-top: math.div($sui-gutter, 2);\n\n\t\t\t\t\t@include media(max-width, md) {\n\t\t\t\t\t\tmargin-top: math.div($sui-gutter-md, 2);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t// ELEMENT: Box Banner.\n\t\t\t.sui-box-banner {\n\t\t\t\twidth: 100%;\n\t\t\t\tmax-width: 100%;\n\t\t\t\theight: auto;\n\t\t\t\toverflow: hidden;\n\t\t\t\tdisplay: block;\n\t\t\t\tmargin: 0;\n\t\t\t\tpadding: 0;\n\t\t\t\tborder: 0;\n\t\t\t\tborder-radius: 0;\n\n\t\t\t\timg {\n\t\t\t\t\tmax-width: 100%;\n\t\t\t\t\tdisplay: block;\n\t\t\t\t\tmargin: 0 auto;\n\t\t\t\t\tpadding: 0;\n\t\t\t\t\tborder: 0;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t// ELEMENT: Box Logo.\n\t\t\t.sui-box-logo {\n\t\t\t\twidth: map-get($modal-logo, size);\n\t\t\t\theight: map-get($modal-logo, size);\n\t\t\t\toverflow: hidden;\n\t\t\t\tdisplay: block;\n\t\t\t\tmargin: 0 auto;\n\t\t\t\tpadding: 0;\n\t\t\t\tborder: map-get($modal-logo, frame-width) solid map-get($modal-logo, frame-color);\n\t\t\t\tborder-radius: $border-radius;\n\t\t\t\tbackground-color: map-get($modal-logo, frame-color);\n\n\t\t\t\timg {\n\t\t\t\t\twidth: #{map-get($modal-logo, size) - (map-get($modal-logo, frame-width) * 2)};\n\t\t\t\t\theight: #{map-get($modal-logo, size) - (map-get($modal-logo, frame-width) * 2)};\n\t\t\t\t\tdisplay: block;\n\t\t\t\t\tmargin: 0;\n\t\t\t\t\tpadding: 0;\n\t\t\t\t\tborder: 0;\n\t\t\t\t\tborder-radius: $border-radius;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t// ELEMENT: Box Header.\n\t\t\t.sui-box-header {\n\t\t\t\tposition: relative;\n\n\t\t\t\t// ELEMENT: Box Banner.\n\t\t\t\t.sui-box-banner {\n\t\t\t\t\twidth: auto;\n\t\t\t\t\tmax-width: none;\n\t\t\t\t\tmargin-top: -#{math.div($sui-gutter, 2)};\n\t\t\t\t\tmargin-right: -#{$sui-gutter};\n\t\t\t\t\tmargin-bottom: #{$sui-gutter + 10px};\n\t\t\t\t\tmargin-left: -#{$sui-gutter};\n\t\t\t\t\tborder-top-left-radius: $border-radius;\n\t\t\t\t\tborder-top-right-radius: $border-radius;\n\n\t\t\t\t\t@include media(max-width, md) {\n\t\t\t\t\t\tmargin-top: -#{math.div($sui-gutter-md, 2)};\n\t\t\t\t\t\tmargin-right: -#{$sui-gutter-md};\n\t\t\t\t\t\tmargin-bottom: #{$sui-gutter-md + 10px};\n\t\t\t\t\t\tmargin-left: -#{$sui-gutter-md};\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t// ELEMENT: Box Logo.\n\t\t\t\t.sui-box-logo {\n\t\t\t\t\tposition: absolute;\n\t\t\t\t\ttop: -#{math.div(map-get($modal-logo, size), 2)};\n\t\t\t\t\tleft: 50%;\n\t\t\t\t\ttransform: translateX(-50%);\n\t\t\t\t}\n\n\t\t\t\t// ELEMENT: Floated button.\n\t\t\t\t.sui-button-float {\n\n\t\t\t\t\t// FLOAT: Right.\n\t\t\t\t\t&--right {\n\t\t\t\t\t\tposition: absolute;\n\t\t\t\t\t\ttop: #{$sui-gutter - 10px};\n\t\t\t\t\t\tright: #{$sui-gutter - 10px};\n\t\t\t\t\t\tmargin: 0;\n\n\t\t\t\t\t\t@include media(max-width, md) {\n\t\t\t\t\t\t\ttop: #{$sui-gutter-md - 5px};\n\t\t\t\t\t\t\tright: #{$sui-gutter-md - 5px};\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\n\t\t\t\t\t// FLOAT: Left.\n\t\t\t\t\t&--left {\n\t\t\t\t\t\tposition: absolute;\n\t\t\t\t\t\ttop: #{$sui-gutter - 10px};\n\t\t\t\t\t\tleft: #{$sui-gutter - 10px};\n\t\t\t\t\t\tmargin: 0;\n\n\t\t\t\t\t\t@include media(max-width, md) {\n\t\t\t\t\t\t\ttop: #{$sui-gutter-md - 5px};\n\t\t\t\t\t\t\tleft: #{$sui-gutter-md - 5px};\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t// ELEMENT: Floated steps.\n\t\t\t\t.sui-steps-float {\n\t\t\t\t\tposition: absolute;\n\t\t\t\t\ttop: #{$sui-gutter - 10px};\n\t\t\t\t\tleft: 50%;\n\t\t\t\t\tmargin: 0;\n\t\t\t\t\ttransform: translateX(-50%);\n\n\t\t\t\t\t@include media(max-width, md) {\n\t\t\t\t\t\ttop: #{$sui-gutter-md - 5px};\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t// VAR: Flatten.\n\t\t\t\t&.sui-flatten {\n\t\t\t\t\tpadding-bottom: 0;\n\t\t\t\t\tborder-bottom-width: 0;\n\t\t\t\t}\n\n\t\t\t\t// VAR: Spacing.\n\t\t\t\t@each $name, $spacing in $modal-spacing {\n\n\t\t\t\t\t// SPACING: Top.\n\t\t\t\t\t&.sui-spacing-top--#{$name} {\n\t\t\t\t\t\tpadding-top: nth($spacing, 1);\n\n\t\t\t\t\t\t// ELEMENT: Box image.\n\t\t\t\t\t\t.sui-box-banner {\n\t\t\t\t\t\t\tmargin-top: -#{nth($spacing, 1)};\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\n\t\t\t\t\t// SPACING: Right.\n\t\t\t\t\t&.sui-spacing-right--#{$name} {\n\t\t\t\t\t\tpadding-right: nth($spacing, 1);\n\n\t\t\t\t\t\t// ELEMENT: Box image.\n\t\t\t\t\t\t.sui-box-banner {\n\t\t\t\t\t\t\tmargin-right: -#{nth($spacing, 1)};\n\n\t\t\t\t\t\t\t@if nth($spacing, 1) != nth($spacing, 2) {\n\n\t\t\t\t\t\t\t\t@include media(max-width, md) {\n\t\t\t\t\t\t\t\t\tmargin-right: -#{nth($spacing, 2)};\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t@if nth($spacing, 1) != nth($spacing, 2) {\n\n\t\t\t\t\t\t\t@include media(max-width, md) {\n\t\t\t\t\t\t\t\tpadding-right: nth($spacing, 2);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\n\t\t\t\t\t// SPACING: Bottom.\n\t\t\t\t\t&.sui-spacing-bottom--#{$name} {\n\t\t\t\t\t\tpadding-bottom: nth($spacing, 1);\n\n\t\t\t\t\t\t@if nth($spacing, 1) != nth($spacing, 2) {\n\n\t\t\t\t\t\t\t@include media(max-width, md) {\n\t\t\t\t\t\t\t\tpadding-bottom: nth($spacing, 2);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\n\t\t\t\t\t// SPACING: Left.\n\t\t\t\t\t&.sui-spacing-left--#{$name} {\n\t\t\t\t\t\tpadding-left: nth($spacing, 1);\n\n\t\t\t\t\t\t// ELEMENT: Box image.\n\t\t\t\t\t\t.sui-box-banner {\n\t\t\t\t\t\t\tmargin-left: -#{nth($spacing, 1)};\n\n\t\t\t\t\t\t\t@if nth($spacing, 1) != nth($spacing, 2) {\n\n\t\t\t\t\t\t\t\t@include media(max-width, md) {\n\t\t\t\t\t\t\t\t\tmargin-left: -#{nth($spacing, 2)};\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t@if nth($spacing, 1) != nth($spacing, 2) {\n\n\t\t\t\t\t\t\t@include media(max-width, md) {\n\t\t\t\t\t\t\t\tpadding-left: nth($spacing, 2);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\n\t\t\t\t\t// SPACING: Sides.\n\t\t\t\t\t&.sui-spacing-sides--#{$name} {\n\t\t\t\t\t\tpadding-left: nth($spacing, 1);\n\t\t\t\t\t\tpadding-right: nth($spacing, 1);\n\n\t\t\t\t\t\t// ELEMENT: Box Image.\n\t\t\t\t\t\t.sui-box-banner {\n\t\t\t\t\t\t\tmargin-left: -#{nth($spacing, 1)};\n\t\t\t\t\t\t\tmargin-right: -#{nth($spacing, 1)};\n\n\t\t\t\t\t\t\t@if nth($spacing, 1) != nth($spacing, 2) {\n\n\t\t\t\t\t\t\t\t@include media(max-width, md) {\n\t\t\t\t\t\t\t\t\tmargin-left: -#{nth($spacing, 2)};\n\t\t\t\t\t\t\t\t\tmargin-right: -#{nth($spacing, 2)};\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t@if nth($spacing, 1) != nth($spacing, 2) {\n\n\t\t\t\t\t\t\t@include media(max-width, md) {\n\t\t\t\t\t\t\t\tpadding-left: nth($spacing, 2);\n\t\t\t\t\t\t\t\tpadding-right: nth($spacing, 2);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t// VAR: Content alignment.\n\t\t\t\t&.sui-content {\n\n\t\t\t\t\t// ALIGN: Center.\n\t\t\t\t\t&-center {\n\t\t\t\t\t\tdisplay: block;\n\t\t\t\t\t\ttext-align: center;\n\t\t\t\t\t}\n\n\t\t\t\t\t// ALIGN: Center Inline.\n\t\t\t\t\t&-center-inline {\n\t\t\t\t\t\tjustify-content: center;\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t+ .sui-box-footer.sui-flatten {\n\t\t\t\t\tpadding-top: $sui-gutter;\n\n\t\t\t\t\t@include media(max-width, md) {\n\t\t\t\t\t\tpadding-top: $sui-gutter-md;\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t+ .sui-box-footer:not(.sui-flatten) {\n\t\t\t\t\tmargin-top: $sui-gutter;\n\n\t\t\t\t\t@include media(max-width, md) {\n\t\t\t\t\t\tmargin-top: $sui-gutter-md;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t// ELEMENT: Box Body.\n\t\t\t.sui-box-body {\n\n\t\t\t\t// VAR: Content alignment.\n\t\t\t\t&.sui-content {\n\n\t\t\t\t\t// ALIGN: Center.\n\t\t\t\t\t&-center {\n\t\t\t\t\t\ttext-align: center;\n\t\t\t\t\t}\n\n\t\t\t\t\t// ALIGN: Right.\n\t\t\t\t\t&-center-inline {\n\t\t\t\t\t\ttext-align: right;\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t// VAR: Spacing.\n\t\t\t\t@each $name, $spacing in $modal-spacing {\n\n\t\t\t\t\t// SPACING: Top.\n\t\t\t\t\t&.sui-spacing-top--#{$name} {\n\t\t\t\t\t\tpadding-top: nth($spacing, 1);\n\n\t\t\t\t\t\t@if nth($spacing, 1) != nth($spacing, 2) {\n\n\t\t\t\t\t\t\t@include media(max-width, md) {\n\t\t\t\t\t\t\t\tpadding-top: nth($spacing, 2);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\n\t\t\t\t\t// SPACING: Right.\n\t\t\t\t\t&.sui-spacing-right--#{$name} {\n\t\t\t\t\t\tpadding-right: nth($spacing, 1);\n\n\t\t\t\t\t\t@if nth($spacing, 1) != nth($spacing, 2) {\n\n\t\t\t\t\t\t\t@include media(max-width, md) {\n\t\t\t\t\t\t\t\tpadding-right: nth($spacing, 2);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\n\t\t\t\t\t// SPACING: Bottom.\n\t\t\t\t\t&.sui-spacing-bottom--#{$name} {\n\t\t\t\t\t\tpadding-bottom: nth($spacing, 1);\n\n\t\t\t\t\t\t@if nth($spacing, 1) != nth($spacing, 2) {\n\n\t\t\t\t\t\t\t@include media(max-width, md) {\n\t\t\t\t\t\t\t\tpadding-bottom: nth($spacing, 2);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\n\t\t\t\t\t// SPACING: Left.\n\t\t\t\t\t&.sui-spacing-left--#{$name} {\n\t\t\t\t\t\tpadding-left: nth($spacing, 1);\n\n\t\t\t\t\t\t@if nth($spacing, 1) != nth($spacing, 2) {\n\n\t\t\t\t\t\t\t@include media(max-width, md) {\n\t\t\t\t\t\t\t\tpadding-left: nth($spacing, 2);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\n\t\t\t\t\t// SPACING: Sides.\n\t\t\t\t\t&.sui-spacing-sides--#{$name} {\n\t\t\t\t\t\tpadding-left: nth($spacing, 1);\n\t\t\t\t\t\tpadding-right: nth($spacing, 1);\n\n\t\t\t\t\t\t@if nth($spacing, 1) != nth($spacing, 2) {\n\n\t\t\t\t\t\t\t@include media(max-width, md) {\n\t\t\t\t\t\t\t\tpadding-left: nth($spacing, 2);\n\t\t\t\t\t\t\t\tpadding-right: nth($spacing, 2);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t// ELEMENT: Box Selectors.\n\t\t\t.sui-box-selectors {\n\t\t\t\tmargin: $sui-gutter 0;\n\n\t\t\t\t&:first-child {\n\t\t\t\t\tmargin-top: 0;\n\n\t\t\t\t\t@include media(max-width, md) {\n\t\t\t\t\t\tmargin-top: 0;\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t&:last-child {\n\t\t\t\t\tmargin-bottom: 0;\n\n\t\t\t\t\t@include media(max-width, md) {\n\t\t\t\t\t\tmargin-bottom: 0;\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t@include media(max-width, md) {\n\t\t\t\t\tmargin: $sui-gutter-md 0;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t// ELEMENT: Box Footer.\n\t\t\t.sui-box-footer {\n\n\t\t\t\t// VAR: Flatten.\n\t\t\t\t&.sui-flatten {\n\t\t\t\t\tpadding-top: 0;\n\t\t\t\t\tborder-top-width: 0;\n\t\t\t\t}\n\n\t\t\t\t// VAR: Content alignment.\n\t\t\t\t&.sui-content {\n\n\t\t\t\t\t// ALIGN: Center.\n\t\t\t\t\t&-center {\n\t\t\t\t\t\tjustify-content: center;\n\t\t\t\t\t}\n\n\t\t\t\t\t// ALIGN: Right.\n\t\t\t\t\t&-right {\n\t\t\t\t\t\tjustify-content: flex-end;\n\t\t\t\t\t}\n\n\t\t\t\t\t// ALIGN: Separated (space between).\n\t\t\t\t\t&-separated {\n\t\t\t\t\t\tjustify-content: space-between;\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t// VAR: Spacing.\n\t\t\t\t@each $name, $spacing in $modal-spacing {\n\n\t\t\t\t\t// SPACING: Top.\n\t\t\t\t\t&.sui-spacing-top--#{$name} {\n\t\t\t\t\t\tpadding-top: nth($spacing, 1);\n\n\t\t\t\t\t\t@if nth($spacing, 1) != nth($spacing, 2) {\n\n\t\t\t\t\t\t\t@include media(max-width, md) {\n\t\t\t\t\t\t\t\tpadding-top: nth($spacing, 2);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\n\t\t\t\t\t// SPACING: Right.\n\t\t\t\t\t&.sui-spacing-right--#{$name} {\n\t\t\t\t\t\tpadding-right: nth($spacing, 1);\n\n\t\t\t\t\t\t@if nth($spacing, 1) != nth($spacing, 2) {\n\n\t\t\t\t\t\t\t@include media(max-width, md) {\n\t\t\t\t\t\t\t\tpadding-right: nth($spacing, 2);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\n\t\t\t\t\t// SPACING: Bottom.\n\t\t\t\t\t&.sui-spacing-bottom--#{$name} {\n\t\t\t\t\t\tpadding-bottom: nth($spacing, 1);\n\t\t\t\t\t}\n\n\t\t\t\t\t// SPACING: Left.\n\t\t\t\t\t&.sui-spacing-left--#{$name} {\n\t\t\t\t\t\tpadding-left: nth($spacing, 1);\n\n\t\t\t\t\t\t@if nth($spacing, 1) != nth($spacing, 2) {\n\n\t\t\t\t\t\t\t@include media(max-width, md) {\n\t\t\t\t\t\t\t\tpadding-left: nth($spacing, 2);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\n\t\t\t\t\t// SPACING: Sides.\n\t\t\t\t\t&.sui-spacing-sides--#{$name} {\n\t\t\t\t\t\tpadding-left: nth($spacing, 1);\n\t\t\t\t\t\tpadding-right: nth($spacing, 1);\n\n\t\t\t\t\t\t@if nth($spacing, 1) != nth($spacing, 2) {\n\n\t\t\t\t\t\t\t@include media(max-width, md) {\n\t\t\t\t\t\t\t\tpadding-left: nth($spacing, 2);\n\t\t\t\t\t\t\t\tpadding-right: nth($spacing, 2);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t// ELEMENT: Skip modal.\n\t\t.sui-modal-skip:not(.sui-button) {\n\t\t\twidth: auto;\n\t\t\tmax-width: 100%;\n\t\t\tcursor: pointer;\n\t\t\tdisplay: block;\n\t\t\tmargin: 0 auto;\n\t\t\tpadding: 0;\n\t\t\tborder: 0;\n\t\t\tbackground-color: transparent;\n\t\t\tbackground-image: none;\n\t\t\tbox-shadow: none;\n\t\t\tcolor: palette(silver, medium);\n\t\t\tfont: 500 12px/16px $font;\n\t\t\tletter-spacing: -0.18px;\n\t\t\ttext-align: center;\n\n\t\t\t&:focus,\n\t\t\t&:hover,\n\t\t\t&:active {\n\t\t\t\tcolor: $white;\n\t\t\t}\n\t\t}\n\n\t\t// ELEMENT: Steps.\n\t\t.sui-box-steps {\n\t\t\tdisplay: flex;\n\t\t\tflex-wrap: wrap;\n\t\t\talign-items: center;\n\t\t\tjustify-content: center;\n\t\t\tmargin: 0 -5px;\n\t\t\tpadding: 0;\n\t\t\tborder: 0;\n\n\t\t\ta,\n\t\t\tspan,\n\t\t\tbutton {\n\t\t\t\toverflow: hidden;\n\t\t\t\tdisplay: block;\n\t\t\t\tflex: 0 0 auto;\n\t\t\t\tmargin: 0 5px;\n\t\t\t\tpadding: 0;\n\t\t\t\tborder: 0;\n\t\t\t\tbackground-color: palette(silver, soft);\n\t\t\t\twhite-space: nowrap;\n\t\t\t\ttext-overflow: clip;\n\t\t\t\tcolor: transparent;\n\n\t\t\t\t&.sui-current {\n\t\t\t\t\tbackground-color: $blue;\n\t\t\t\t}\n\n\t\t\t\t&:disabled,\n\t\t\t\t&[disabled],\n\t\t\t\t&.sui-disabled {\n\t\t\t\t\tcursor: not-allowed;\n\t\t\t\t\tpointer-events: none;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\ta,\n\t\t\tbutton,\n\t\t\tspan[role=\"button\"] {\n\t\t\t\tcursor: pointer;\n\t\t\t}\n\n\t\t\t@each $name, $size in $modal-steps {\n\n\t\t\t\t&.sui-#{$name} {\n\t\t\t\t\tpadding: math.div((30px - $size), 2) 0;\n\n\t\t\t\t\ta,\n\t\t\t\t\tspan,\n\t\t\t\t\tbutton {\n\t\t\t\t\t\twidth: $size;\n\t\t\t\t\t\theight: $size;\n\t\t\t\t\t\tborder-radius: #{$size * 2};\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t@each $name, $size in $modal-size {\n\n\t\t\t&.sui-modal-#{$name} {\n\n\t\t\t\t.sui-modal-content {\n\t\t\t\t\tmax-width: #{$size + ($sui-gutter * 2)};\n\n\t\t\t\t\t@include media(max-width, md) {\n\t\t\t\t\t\tmax-width: #{$size + ($sui-gutter-md * 2)};\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t// STATE: Hidden.\n\t\t&.sui-active {\n\t\t\tdisplay: flex;\n\t\t}\n\n\t\t@include media(max-width, md) {\n\t\t\twidth: 100%;\n\t\t\tleft: 0;\n\t\t\tpadding: $sui-gutter-md 0;\n\t\t}\n\t}\n\n\t// VAR: Folded sidebar.\n\t&.wp-admin.folded {\n\n\t\t.sui-modal {\n\n\t\t\t@include media(min-width, md) {\n\t\t\t\twidth: calc(100% - #{map-get($wordpress, adminmenu-sm)});\n\t\t\t\tleft: map-get($wordpress, adminmenu-sm);\n\t\t\t}\n\t\t}\n\t}\n\n\t&.wp-admin {\n\n\t\t&.auto-fold {\n\n\t\t\t.sui-modal {\n\n\t\t\t\t@media only screen and (max-width: 960px) {\n\t\t\t\t\twidth: calc(100% - #{map-get($wordpress, adminmenu-sm)});\n\t\t\t\t\tleft: map-get($wordpress, adminmenu-sm);\n\t\t\t\t}\n\n\t\t\t\t@media only screen and (max-width: 782px) {\n\t\t\t\t\twidth: 100%;\n\t\t\t\t\tleft: 0;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n}\n\n@include body-class(true, true) {\n\n\t.sui-modal {\n\t\tright: map-get($wordpress, adminmenu);\n\t\tleft: auto;\n\n\t\t.sui-modal-overlay {\n\t\t\tright: map-get($wordpress, adminmenu);\n\t\t\tleft: auto;\n\t\t}\n\t}\n\n\t// VAR: Folded sidebar.\n\t&.wp-admin.folded {\n\n\t\t.sui-modal {\n\n\t\t\t.sui-modal-overlay {\n\n\t\t\t\t@include media(min-width, md) {\n\t\t\t\t\tright: map-get($wordpress, adminmenu-sm);\n\t\t\t\t\tleft: auto;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t@include media(min-width, md) {\n\t\t\t\tright: map-get($wordpress, adminmenu-sm);\n\t\t\t\tleft: auto;\n\t\t\t}\n\t\t}\n\t}\n\n\t&.wp-admin {\n\n\t\t&.auto-fold {\n\n\t\t\t.sui-modal {\n\n\t\t\t\t.sui-modal-overlay {\n\n\t\t\t\t\t@media only screen and (max-width: 960px) {\n\t\t\t\t\t\tright: map-get($wordpress, adminmenu-sm);\n\t\t\t\t\t\tleft: auto;\n\t\t\t\t\t}\n\n\t\t\t\t\t@media only screen and (max-width: 782px) {\n\t\t\t\t\t\tright: 0;\n\t\t\t\t\t\tleft: auto;\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t@media only screen and (max-width: 960px) {\n\t\t\t\t\tright: map-get($wordpress, adminmenu-sm);\n\t\t\t\t\tleft: auto;\n\t\t\t\t}\n\n\t\t\t\t@media only screen and (max-width: 782px) {\n\t\t\t\t\tright: 0;\n\t\t\t\t\tleft: auto;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n}\n\nhtml.sui-has-modal,\nhtml.sui-has-overlay {\n\toverflow: hidden;\n}\n", "$positions: (\n\ttop,\n\tright,\n\tbottom,\n\tleft\n) !default;\n\n@include body-class(true) {\n\n\t.sui-hidden {\n\t\tdisplay: none;\n\t}\n\n\t.sui-hidden-important {\n\t\tdisplay: none !important;\n\t}\n\n\t.sui-block-content-center {\n\t\ttext-align: center;\n\t}\n\n\t.sui-image {\n\t\tdisplay: block;\n\t\theight: auto;\n\t\tmax-width: 100%;\n\t}\n\n\t.sui-image-center {\n\t\tmargin-right: auto;\n\t\tmargin-left: auto;\n\t}\n\n\t.sui-actions-left {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tmargin-left: 10px;\n\t\tmargin-right: auto;\n\t}\n\n\t.sui-actions-right {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tmargin-left: auto;\n\t\tmargin-right: 0;\n\t}\n\n\t// Flex alignment\n\t.sui-space-between {\n\t\tjustify-content: space-between;\n\t}\n\t.sui-align-start {\n\t\tjustify-content: flex-start;\n\t}\n\t.sui-align-end {\n\t\tjustify-content: flex-end;\n\t}\n\n\t.sui-success {\n\t\tcolor: $success;\n\t}\n\n\t.sui-warning {\n\t\tcolor: $warning;\n\t}\n\n\t.sui-error {\n\t\tcolor: $error;\n\t}\n\n\t.sui-info {\n\t\tcolor: $info;\n\t}\n\n\t.sui-no-margin {\n\t\tmargin: 0 !important;\n\t}\n\n\t.sui-no-margin-top {\n\t\tmargin-top: 0 !important;\n\t}\n\n\t.sui-no-margin-bottom {\n\t\tmargin-bottom: 0 !important;\n\t}\n\n\t.sui-no-margin-left {\n\t\tmargin-left: 0 !important;\n\t}\n\n\t.sui-no-margin-right {\n\t\tmargin-right: 0 !important;\n\t}\n\n\t.sui-no-padding {\n\t\tpadding: 0 !important;\n\t}\n\n\t.sui-no-padding-top {\n\t\tpadding-top: 0 !important;\n\t}\n\n\t.sui-no-padding-bottom {\n\t\tpadding-bottom: 0 !important;\n\t}\n\n\t.sui-no-padding-left {\n\t\tpadding-left: 0 !important;\n\t}\n\n\t.sui-no-padding-right {\n\t\tpadding-right: 0 !important;\n\t}\n\n\t.sui-margin {\n\t\tmargin: $default-margin !important;\n\t}\n\n\t.sui-margin-top {\n\t\tmargin-top: $default-margin !important;\n\t}\n\n\t.sui-margin-bottom {\n\t\tmargin-bottom: $default-margin !important;\n\t}\n\n\t.sui-margin-left {\n\t\tmargin-left: $default-margin !important;\n\t}\n\n\t.sui-margin-right {\n\t\tmargin-right: $default-margin !important;\n\t}\n\n\t.sui-padding {\n\t\tpadding: $sui-gutter-md;\n\n\t\t@include media(min-width, md) {\n\t\t\tpadding: $sui-gutter;\n\t\t}\n\n\t\t&--hidden {\n\t\t\tpadding: 0;\n\n\t\t\t@include media(min-width, md) {\n\t\t\t\tpadding: 0;\n\t\t\t}\n\t\t}\n\n\t\t&__desktop {\n\n\t\t\t@include media(min-width, md) {\n\t\t\t\tpadding: $sui-gutter;\n\t\t\t}\n\n\t\t\t&--hidden {\n\n\t\t\t\t@include media(min-width, md) {\n\t\t\t\t\tpadding: 0;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t&__mobile {\n\n\t\t\t@include media(max-width, md) {\n\t\t\t\tpadding: $sui-gutter-md;\n\t\t\t}\n\n\t\t\t&--hidden {\n\n\t\t\t\t@include media(max-width, md) {\n\t\t\t\t\tpadding: 0;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\n\t@each $position in $positions {\n\n\t\t.sui-padding-#{$position} {\n\t\t\tpadding-#{$position}: $sui-gutter-md;\n\n\t\t\t@include media(min-width, md) {\n\t\t\t\tpadding-#{$position}: $sui-gutter;\n\t\t\t}\n\n\t\t\t&--hidden {\n\t\t\t\tpadding-#{$position}: 0;\n\n\t\t\t\t@include media(min-width, md) {\n\t\t\t\t\tpadding-#{$position}: 0;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t&__desktop {\n\n\t\t\t\t@include media(min-width, md) {\n\t\t\t\t\tpadding-#{$position}: $sui-gutter;\n\t\t\t\t}\n\n\t\t\t\t&--hidden {\n\n\t\t\t\t\t@include media(min-width, md) {\n\t\t\t\t\t\tpadding-#{$position}: 0;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t&__mobile {\n\n\t\t\t\t@include media(max-width, md) {\n\t\t\t\t\tpadding-#{$position}: $sui-gutter-md;\n\t\t\t\t}\n\n\t\t\t\t&--hidden {\n\n\t\t\t\t\t@include media(max-width, md) {\n\t\t\t\t\t\tpadding-#{$position}: 0;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n}\n\n// Loop through breakpoints & generate hidden utility classes.\n@for $i from 1 through length($sui-breakpoints) {\n\t$size: nth(nth($sui-breakpoints, $i), 1);\n\t$screen-width-min: nth(nth($sui-breakpoints, $i), 2);\n\n\t// If lowest breakpoint in map.\n\t@if ($i == 1) {\n\t\t$screen-width-max: nth(nth($sui-breakpoints, ($i + 1)), 2) - 1px;\n\t\t@media (max-width: $screen-width-max) {\n\t\t\t.sui-hidden-#{$size} {\n\t\t\t\tdisplay: none !important;\n\t\t\t}\n\t\t}\n\t}\n\n\t// If highest breakpoint in map.\n\t@else if ($i == length($sui-breakpoints)) {\n\t\t@media (min-width: $screen-width-min) {\n\t\t\t.sui-hidden-#{$size} {\n\t\t\t\tdisplay: none !important;\n\t\t\t}\n\t\t}\n\t}\n\n\t// If breakpoint falls inbetween.\n\t@else {\n\t\t$screen-width-max: nth(nth($sui-breakpoints, ($i + 1)), 2) - 1px;\n\t\t\t@media (min-width: $screen-width-min) and (max-width: $screen-width-max) {\n\t\t\t.sui-hidden-#{$size} {\n\t\t\t\tdisplay: none !important;\n\t\t\t}\n\t\t}\n\t}\n}\n\n@include body-class(true, false, true) {\n\t.sui-error {\n\t\tcolor: $nightrider;\n\t}\n}\n", "/**\nWP Admin Notices\n\nThis is used to give admin notices that are loaded outside of the .wrap but inside .sui-*-*-* the proper styles\n\n */\n@include body-class {\n\t#wpbody-content > .notice {\n\t\tmargin: 10px 30px 15px 10px;\n\t\t@include media( max-width, md ) {\n\t\t\tmargin: $sui-gutter-md ($sui-gutter-md - 10px) 0 ($sui-gutter-md - 20px); // Account for padding from #wpcontent.\n\t\t}\n\t}\n}\n", "@use \"sass:math\";\n\n@include body-class(true) {\n\n\t.sui-table {\n\t\twidth: 100%;\n\t\ttable-layout: fixed;\n\t\tmargin: $sui-gutter-md 0;\n\t\tborder-collapse: unset;\n\t\tborder-spacing: unset;\n\t\tborder: 1px solid $table--border-color;\n\t\tborder-radius: $border-radius;\n\t\tcolor: $table--text-color;\n\t\tfont-size: $table--text-font-size;\n\t\tline-height: $table--text-line-height;\n\t\tletter-spacing: $font--letter-spacing;\n\n\t\tthead, tbody, tfoot {\n\n\t\t\t> tr > {\n\n\t\t\t\tth,\n\t\t\t\t.sui-table-item-title,\n\t\t\t\t.sui-accordion-item-title {\n\t\t\t\t\tcolor: $table--th-color;\n\t\t\t\t\ttext-align: left;\n\t\t\t\t}\n\n\t\t\t\tth, td {\n\t\t\t\t\theight: 40px;\n\t\t\t\t\tvertical-align: middle;\n\t\t\t\t\tpadding: 5px #{math.div($sui-gutter-md, 2) - $table--border-width};\n\t\t\t\t\tborder: 0;\n\t\t\t\t\tborder-bottom: $table--border-width $table--border-style $table--border-color;\n\n\t\t\t\t\t&:first-child {\n\t\t\t\t\t\tpadding-left: #{$sui-gutter-md - $table--border-width};\n\n\t\t\t\t\t\t@include media(min-width, md) {\n\t\t\t\t\t\t\tpadding-left: #{$sui-gutter - $table--border-width};\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\n\t\t\t\t\t&:last-child {\n\t\t\t\t\t\tpadding-right: #{$sui-gutter-md - $table--border-width};\n\n\t\t\t\t\t\t@include media(min-width, md) {\n\t\t\t\t\t\t\tpadding-right: #{$sui-gutter - $table--border-width};\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\n\t\t\t\t\t@include media(min-width, md) {\n\t\t\t\t\t\theight: 60px;\n\t\t\t\t\t\tpadding-right: #{math.div($sui-gutter, 2) - $table--border-width};\n\t\t\t\t\t\tpadding-left: #{math.div($sui-gutter, 2) - $table--border-width};\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t&:last-child > tr:last-child > {\n\n\t\t\t\tth, td {\n\t\t\t\t\tborder-bottom: 0;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\tthead {\n\n\t\t\ttr th {\n\t\t\t\tfont-weight: bold;\n\t\t\t\twhite-space: nowrap;\n\t\t\t}\n\t\t}\n\n\t\ttbody {\n\n\t\t\ttr th,\n\t\t\ttr td {\n\n\t\t\t\t.sui-toggle {\n\t\t\t\t\tvertical-align: text-top;\n\t\t\t\t}\n\n\t\t\t\t> [class*=\"sui-icon-\"] {\n\t\t\t\t\tmargin-right: 10px;\n\t\t\t\t\tfont-size: 10px;\n\n\t\t\t\t\t&.sui-icon-right {\n\t\t\t\t\t\tmargin-right: 0;\n\t\t\t\t\t\tmargin-left: 10px;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\n\t\t\ttr th,\n\t\t\ttr .sui-table-item-title,\n\t\t\ttr .sui-accordion-item-title {\n\t\t\t\tfont-weight: 500;\n\t\t\t}\n\n\t\t\ttr .sui-table-item-title,\n\t\t\ttr .sui-accordion-item-title {\n\n\t\t\t\t> [class*=\"sui-icon-\"] {\n\t\t\t\t\tmargin-right: 10px;\n\t\t\t\t\tfont-size: 16px;\n\t\t\t\t\tvertical-align: middle;\n\n\t\t\t\t\t&:before {\n\t\t\t\t\t\tdisplay: inline-block;\n\t\t\t\t\t\tline-height: 22px;\n\t\t\t\t\t}\n\n\t\t\t\t\t&.sui-icon-right {\n\t\t\t\t\t\tmargin-right: 0;\n\t\t\t\t\t\tmargin-left: 10px;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\n\t\t\ttr.sui-error,\n\t\t\ttr.sui-warning,\n\t\t\ttr.sui-success {\n\n\t\t\t\tth,\n\t\t\t\t.sui-table-item-title,\n\t\t\t\t.sui-accordion-item-title {\n\t\t\t\t\tcolor: $table--th-color;\n\t\t\t\t}\n\n\t\t\t\ttd {\n\t\t\t\t\tcolor: $table--text-color;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\ttr.sui-default {\n\t\t\t\tbox-shadow: inset 3px 0 0 $gray;\n\t\t\t}\n\n\n\t\t\ttr.sui-error {\n\t\t\t\tbox-shadow: inset 3px 0 0 $error;\n\t\t\t}\n\n\t\t\ttr.sui-warning {\n\t\t\t\tbox-shadow: inset 3px 0 0 $warning;\n\t\t\t}\n\n\t\t\ttr.sui-success {\n\t\t\t\tbox-shadow: inset 3px 0 0 $success;\n\t\t\t}\n\t\t}\n\n\t\t&:first-child,\n\t\t&.sui-table-with-title {\n\t\t\tmargin-top: 0;\n\n\t\t\t@include media(min-width, md) {\n\t\t\t\tmargin-top: 0;\n\t\t\t}\n\t\t}\n\n\t\t&:last-child {\n\t\t\tmargin-bottom: 0;\n\n\t\t\t@include media(min-width, md) {\n\t\t\t\tmargin-bottom: 0;\n\t\t\t}\n\t\t}\n\n\t\t// Flushed table\n\t\t&.sui-table-flushed {\n\t\t\tmargin: 0;\n\t\t\tborder: 0;\n\t\t\tborder-radius: 0;\n\t\t\tborder-collapse: collapse;\n\t\t}\n\n\t\t@include media(min-width, md) {\n\t\t\tmargin: $sui-gutter 0;\n\t\t}\n\t}\n\n\t.sui-table-title {\n\t\tmargin: 5px 0;\n\t\tcolor: $table--title-color;\n\t\tfont: bold 13px/22px $font;\n\t\tletter-spacing: 0;\n\t}\n\n\t// Flushed table\n\t// Remove top padding from thead when table is placed after .sui-box-body\n\t.sui-box-body + .sui-table.sui-table-flushed {\n\n\t\t> thead,\n\t\t> tbody,\n\t\t> tfoot {\n\n\t\t\t&:first-child > tr:first-child > {\n\n\t\t\t\tth, td {\n\t\t\t\t\theight: auto;\n\t\t\t\t\tpadding-top: 0;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\n\t// Field list\n\t// An alternative table based on flex-box\n\t.sui-field-list {\n\t\tborder: $table--border-width $table--border-style $table--border-color;\n\t\tborder-radius: $border-radius;\n\n\t\t&-header {\n\t\t\tpadding: 20px 30px;\n\t\t}\n\n\t\t&-item {\n\t\t\tdisplay: flex;\n\t\t\talign-items: center;\n\t\t\tpadding: 15px 30px;\n\t\t\tborder-top: $table--border-width $table--border-style $table--border-color;\n\t\t}\n\n\t\t&-item-label {\n\t\t\tmargin-right: auto;\n\t\t\tmargin-left: 0;\n\t\t}\n\n\t\t&-title {\n\t\t\tmargin: 0;\n\t\t\tcolor: $table--field-list-title-color;\n\t\t\tfont-size: $table--text-font-size;\n\t\t\tline-height: 30px;\n\t\t\tfont-family: $font;\n\t\t\tfont-weight: 500;\n\t\t\ttext-transform: capitalize;\n\t\t}\n\t}\n}", "@use \"sass:math\";\n\n@include body-class(true) {\n\n\t.sui-accordion {\n\n\t\t.sui-accordion-item-body,\n\t\t.sui-accordion-item-content {\n\t\t\tfont-size: 13px;\n\t\t\tline-height: 22px;\n\n\t\t\t.sui-accordion-body-header {\n\t\t\t\tdisplay: block;\n\t\t\t\tfont-size: 13px;\n\t\t\t\tline-height: 22px;\n\t\t\t\tfont-weight: 700;\n\t\t\t\tmargin: 5px 0;\n\t\t\t}\n\n\t\t\t.sui-box {\n\t\t\t\t&:focus,\n\t\t\t\t&:focus-within {\n\t\t\t\t\tborder-radius: 0;\n\t\t\t\t\tborder: none;\n\t\t\t\t\tbox-shadow: inset 0 0 0 2px palette(blue, default);\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t// Accordion Grid\n\t\t&, &-block {\n\n\t\t\t&:not(.sui-builder-fields) {\n\n\t\t\t\t.sui-accordion-header,\n\t\t\t\t.sui-accordion-item-header,\n\t\t\t\t.sui-accordion-footer {\n\t\t\t\t\tdisplay: flex;\n\t\t\t\t\talign-items: center;\n\t\t\t\t\tfont-size: $table--text-font-size;\n\t\t\t\t\tline-height: $table--text-line-height;\n\t\t\t\t\tfont-family: $font;\n\t\t\t\t\tfont-weight: 400;\n\t\t\t\t\tletter-spacing: $font--letter-spacing;\n\n\t\t\t\t\t> div {\n\t\t\t\t\t\tdisplay: flex;\n\t\t\t\t\t\talign-items: center;\n\t\t\t\t\t\tflex: 1;\n\t\t\t\t\t\tpadding: 0 #{math.div($sui-gutter-md, 2) - 1px};\n\n\t\t\t\t\t\t&:first-child {\n\t\t\t\t\t\t\tpadding-left: #{$sui-gutter-md - 1px};\n\n\t\t\t\t\t\t\t@include media(min-width, md) {\n\t\t\t\t\t\t\t\tpadding-left: #{$sui-gutter - 1px};\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t&:last-child {\n\t\t\t\t\t\t\tpadding-right: #{$sui-gutter-md - 1px};\n\n\t\t\t\t\t\t\t@include media(min-width, md) {\n\t\t\t\t\t\t\t\tpadding-right: #{$sui-gutter - 1px};\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t@include media(min-width, md) {\n\t\t\t\t\t\t\tpadding-right: #{math.div($sui-gutter, 2) - 1px};\n\t\t\t\t\t\t\tpadding-left: #{math.div($sui-gutter, 2) - 1px};\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\n\t\t\t\t\t> [class^=\"sui-accordion-col-\"] {\n\t\t\t\t\t\tflex: 0 0 auto;\n\t\t\t\t\t}\n\n\t\t\t\t\t// GRID\n\t\t\t\t\t// To guarantee accordion columns agree on size we can apply\n\t\t\t\t\t// below column classes based on a 12-columns grid.\n\t\t\t\t\t@for $i from 1 through $accordion--grid {\n\n\t\t\t\t\t\t.sui-accordion-col-#{$i} {\n\t\t\t\t\t\t\tmin-width: math.div((100% * $i), $accordion--grid);\n\t\t\t\t\t\t\tflex-basis: math.div((100% * $i), $accordion--grid);\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t.sui-accordion-item {\n\n\t\t\t\t\t.sui-accordion-item-header {\n\t\t\t\t\t\tcursor: pointer;\n\n\t\t\t\t\t\t.sui-accordion-open-indicator {\n\n\t\t\t\t\t\t\t[class*=\"sui-icon-\"] {\n\t\t\t\t\t\t\t\ttransition: 0.2s linear;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\n\t\t\t\t\t.sui-accordion-item-body {\n\t\t\t\t\t\tdisplay: none;\n\t\t\t\t\t}\n\n\t\t\t\t\t// When item is open\n\t\t\t\t\t&.sui-accordion-item--open {\n\n\t\t\t\t\t\t> .sui-accordion-item-header {\n\n\t\t\t\t\t\t\t.sui-accordion-open-indicator {\n\n\t\t\t\t\t\t\t\t[class*=\"sui-icon-\"] {\n\t\t\t\t\t\t\t\t\ttransform: rotate(180deg);\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t> .sui-accordion-item-body {\n\t\t\t\t\t\t\tdisplay: block;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t// DESIGN: Table (Flexbox)\n\t\t&:not(.sui-table):not(.sui-accordion-block):not(.sui-builder-fields) {\n\t\t\tmargin: $sui-gutter-md 0;\n\t\t\tborder: 1px solid $table--border-color;\n\t\t\tborder-radius: $border-radius;\n\n\t\t\t.sui-accordion-header,\n\t\t\t.sui-accordion-item-header,\n\t\t\t.sui-accordion-footer {\n\t\t\t\tmin-height: 40px;\n\t\t\t\tpadding: 5px 0;\n\n\t\t\t\t@include media(min-width, md) {\n\t\t\t\t\tmin-height: 60px;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t.sui-accordion-header {\n\t\t\t\tborder-bottom: 1px solid $table--border-color;\n\n\t\t\t\t> div {\n\t\t\t\t\toverflow: hidden;\n\t\t\t\t\tcolor: $table--th-color;\n\t\t\t\t\tfont-weight: bold;\n\t\t\t\t\ttext-overflow: ellipsis;\n\t\t\t\t\twhite-space: nowrap;\n\t\t\t\t}\n\n\t\t\t\t&:last-child {\n\t\t\t\t\tborder-bottom-width: 0;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t.sui-accordion-item {\n\t\t\t\tborder-bottom: 1px solid $table--border-color;\n\n\t\t\t\t.sui-accordion-item-header {\n\t\t\t\t\tcolor: $table--text-color;\n\t\t\t\t\ttransition: 0.3s ease;\n\n\t\t\t\t\t> div span {\n\t\t\t\t\t\tflex: 0 1 auto;\n\t\t\t\t\t}\n\n\t\t\t\t\t[class*=\"sui-icon-\"] {\n\t\t\t\t\t\tmargin-right: 10px;\n\t\t\t\t\t\tfont-size: 12px;\n\n\t\t\t\t\t\t&:before {\n\t\t\t\t\t\t\tdisplay: block;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t&.sui-icon-right {\n\t\t\t\t\t\t\tmargin-right: 0;\n\t\t\t\t\t\t\tmargin-left: 10px;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\n\t\t\t\t\t.sui-accordion-item-title {\n\t\t\t\t\t\tcolor: $table--th-color;\n\t\t\t\t\t\tfont-weight: 500;\n\n\t\t\t\t\t\t.sui-toggle {\n\t\t\t\t\t\t\tflex: 0 0 auto;\n\t\t\t\t\t\t\talign-items: flex-start;\n\n\t\t\t\t\t\t\t+ span {\n\t\t\t\t\t\t\t\tmargin-left: math.div($sui-gutter-md, 2);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t[class*=\"sui-icon-\"] {\n\t\t\t\t\t\t\tfont-size: 16px;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t.sui-accordion-open-indicator {\n\n\t\t\t\t\t\t\t[class*=\"sui-icon-\"] {\n\t\t\t\t\t\t\t\tfont-size: 12px;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\n\t\t\t\t\t.sui-accordion-open-indicator {\n\t\t\t\t\t\talign-self: flex-start;\n\t\t\t\t\t\tmargin-right: 0;\n\t\t\t\t\t\tmargin-left: auto;\n\n\t\t\t\t\t\t[class*=\"sui-icon-\"] {\n\t\t\t\t\t\t\tmargin: 0;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\n\t\t\t\t\t.sui-tag {\n\n\t\t\t\t\t\t+ .sui-accordion-open-indicator {\n\t\t\t\t\t\t\tmargin-left: 10px;\n\n\t\t\t\t\t\t\t@include media(min-width, md) {\n\t\t\t\t\t\t\t\tmargin-left: 20px;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\n\t\t\t\t\t&:hover,\n\t\t\t\t\t&:focus {\n\t\t\t\t\t\tbackground-color: $accordion--content-bg-color;\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t.sui-accordion-item-body {\n\t\t\t\t\tpadding: 0 #{$sui-gutter-md - 1px} #{$sui-gutter-md - 1px};\n\t\t\t\t\tbackground-color: $accordion--content-bg-color;\n\n\t\t\t\t\t.sui-box:last-child {\n\t\t\t\t\t\tmargin-bottom: 0;\n\t\t\t\t\t}\n\n\t\t\t\t\t@include media(min-width, md) {\n\t\t\t\t\t\tpadding: 0 #{$sui-gutter - 1px} #{$sui-gutter - 1px};\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t&:first-child {\n\n\t\t\t\t\t.sui-accordion-item-header {\n\t\t\t\t\t\tborder-top-left-radius: #{$border-radius - 1px};\n\t\t\t\t\t\tborder-top-right-radius: #{$border-radius - 1px};\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t&:last-child {\n\t\t\t\t\tborder-bottom: 0;\n\n\t\t\t\t\t.sui-accordion-item-header,\n\t\t\t\t\t.sui-accordion-item-body {\n\t\t\t\t\t\tborder-bottom-right-radius: #{$border-radius - 1px};\n\t\t\t\t\t\tborder-bottom-left-radius: #{$border-radius - 1px};\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t// STATUS: Default\n\t\t\t\t&.sui-default {\n\n\t\t\t\t\t> .sui-accordion-item-header,\n\t\t\t\t\t> .sui-accordion-item-body {\n\t\t\t\t\t\tbox-shadow: inset 3px 0 $gray;\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t// STATUS: Error\n\t\t\t\t&.sui-error {\n\n\t\t\t\t\t> .sui-accordion-item-header,\n\t\t\t\t\t> .sui-accordion-item-body {\n\t\t\t\t\t\tbox-shadow: inset 3px 0 $error;\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t// STATUS: Warning\n\t\t\t\t&.sui-warning {\n\n\t\t\t\t\t> .sui-accordion-item-header,\n\t\t\t\t\t> .sui-accordion-item-body {\n\t\t\t\t\t\tbox-shadow: inset 3px 0 $warning;\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t// STATUS: Success\n\t\t\t\t&.sui-success {\n\n\t\t\t\t\t> .sui-accordion-item-header,\n\t\t\t\t\t> .sui-accordion-item-body {\n\t\t\t\t\t\tbox-shadow: inset 3px 0 $success;\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t// When item is open\n\t\t\t\t&.sui-accordion-item--open {\n\n\t\t\t\t\t> .sui-accordion-item-header {\n\t\t\t\t\t\tborder-bottom-right-radius: 0;\n\t\t\t\t\t\tborder-bottom-left-radius: 0;\n\t\t\t\t\t\tbackground-color: $accordion--content-bg-color;\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t// When item is disabled\n\t\t\t\t&.sui-accordion-item--disabled {\n\n\t\t\t\t\t.sui-accordion-item-header {\n\t\t\t\t\t\tcursor: default;\n\t\t\t\t\t\tbackground-color: $accordion--content-bg-color;\n\n\t\t\t\t\t\t&, .sui-accordion-item-title {\n\t\t\t\t\t\t\tcolor: $accordion--disabled-color;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t&, .sui-accordion-open-indicator {\n\t\t\t\t\t\t\tpointer-events: none;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t.sui-toggle {\n\t\t\t\t\t\t\tpointer-events: initial;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t.sui-circle-score svg circle:last-child {\n\t\t\t\t\t\t\tstroke: $accordion--disabled-icon;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t[class*=\"sui-icon-\"]:before {\n\t\t\t\t\t\t\tcolor: $accordion--disabled-icon;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\n\t\t\t\t\t&.sui-error,\n\t\t\t\t\t&.sui-warning,\n\t\t\t\t\t&.sui-success {\n\n\t\t\t\t\t\t.sui-accordion-item-header {\n\t\t\t\t\t\t\tbox-shadow: inset 3px 0 $accordion--disabled-icon;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\n\t\t\t\t\t&.sui-accordion-item--open {\n\n\t\t\t\t\t\t> .sui-accordion-item-body {\n\t\t\t\t\t\t\tdisplay: none;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t// Flushed accordion\n\t\t\t&.sui-accordion-flushed {\n\t\t\t\tmargin: 0;\n\t\t\t\tborder-right-width: 0;\n\t\t\t\tborder-left-width: 0;\n\t\t\t\tborder-radius: 0;\n\n\t\t\t\t&:first-child {\n\t\t\t\t\tborder-top-width: 0;\n\t\t\t\t}\n\n\t\t\t\t&:last-child {\n\t\t\t\t\tborder-bottom-width: 0;\n\t\t\t\t}\n\n\t\t\t\t&:not(:first-child) {\n\n\t\t\t\t\t.sui-accordion-item:first-child {\n\n\t\t\t\t\t\t.sui-accordion-item-header {\n\t\t\t\t\t\t\tborder-top-left-radius: 0;\n\t\t\t\t\t\t\tborder-top-right-radius: 0;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t&:not(:last-child) {\n\n\t\t\t\t\t.sui-accordion-item:last-child {\n\n\t\t\t\t\t\t.sui-accordion-item-header,\n\t\t\t\t\t\t.sui-accordion-item-body {\n\t\t\t\t\t\t\tborder-bottom-right-radius: 0;\n\t\t\t\t\t\t\tborder-bottom-left-radius: 0;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t@include media(min-width, md) {\n\t\t\t\t\tmargin: 0;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t&:first-child {\n\t\t\t\tmargin-top: 0;\n\n\t\t\t\t@include media(min-width, md) {\n\t\t\t\t\tmargin-top: 0;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t&:last-child {\n\t\t\t\tmargin-bottom: 0;\n\n\t\t\t\t@include media(min-width, md) {\n\t\t\t\t\tmargin-bottom: 0;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t@include media(min-width, md) {\n\t\t\t\tmargin-top: $sui-gutter;\n\t\t\t\tmargin-bottom: $sui-gutter;\n\t\t\t}\n\t\t}\n\n\t\t// DESIGN: Table\n\t\t// Must use in conjunction with .sui-table on the <table> element.\n\t\t&.sui-table {\n\n\t\t\t> tbody {\n\n\t\t\t\t> .sui-accordion-item {\n\t\t\t\t\tcursor: pointer;\n\t\t\t\t\ttransition: background-color 0.3s;\n\n\t\t\t\t\tth, td {\n\t\t\t\t\t\ttransition: background-color 0.3s;\n\n\t\t\t\t\t\t&.sui-table-item-title .sui-toggle .sui-toggle-slider {\n\t\t\t\t\t\t\tmargin-right: 10px;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t&.sui-table-item-title .sui-accordion-open-indicator {\n\n\t\t\t\t\t\t\t[class*=\"sui-icon-\"] {\n\t\t\t\t\t\t\t\theight: $table--text-line-height;\n\t\t\t\t\t\t\t\tline-height: $table--text-line-height;\n\n\t\t\t\t\t\t\t\t&:before {\n\t\t\t\t\t\t\t\t\tline-height: $table--text-line-height;\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\n\t\t\t\t\t.sui-accordion-open-indicator {\n\t\t\t\t\t\tfloat: right;\n\t\t\t\t\t\tmargin-left: 10px;\n\n\t\t\t\t\t\t[class*=\"sui-icon-\"] {\n\t\t\t\t\t\t\tdisplay: block;\n\t\t\t\t\t\t\tmargin-right: 0;\n\t\t\t\t\t\t\tfont-size: 12px;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\n\t\t\t\t\t&:hover,\n\t\t\t\t\t&:focus {\n\t\t\t\t\t\tbackground-color: $accordion--content-bg-color;\n\t\t\t\t\t}\n\n\t\t\t\t\t// Default\n\t\t\t\t\t&.sui-default + .sui-accordion-item-content {\n\t\t\t\t\t\tbox-shadow: inset 3px 0 0 $gray;\n\t\t\t\t\t}\n\n\t\t\t\t\t// Error\n\t\t\t\t\t&.sui-error + .sui-accordion-item-content {\n\t\t\t\t\t\tbox-shadow: inset 3px 0 0 $error;\n\t\t\t\t\t}\n\n\t\t\t\t\t// Warning\n\t\t\t\t\t&.sui-warning + .sui-accordion-item-content {\n\t\t\t\t\t\tbox-shadow: inset 3px 0 0 $warning;\n\t\t\t\t\t}\n\n\t\t\t\t\t// Success\n\t\t\t\t\t&.sui-success + .sui-accordion-item-content {\n\t\t\t\t\t\tbox-shadow: inset 3px 0 0 $success;\n\t\t\t\t\t}\n\n\t\t\t\t\t// When item is open\n\t\t\t\t\t&.sui-accordion-item--open {\n\t\t\t\t\t\tbackground-color: $accordion--content-bg-color;\n\n\t\t\t\t\t\tth, td {\n\t\t\t\t\t\t\tborder-bottom-color: transparent;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t.sui-accordion-open-indicator [class*=\"sui-icon-\"] {\n\t\t\t\t\t\t\ttransform: rotate(180deg);\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t+ .sui-accordion-item-content {\n\t\t\t\t\t\t\tdisplay: table-row;\n\t\t\t\t\t\t\tposition: relative;\n\t\t\t\t\t\t\tvisibility: visible;\n\t\t\t\t\t\t\tz-index: 0;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\n\t\t\t\t\t// When item is disabled\n\t\t\t\t\t&.sui-accordion-item--disabled {\n\t\t\t\t\t\tpointer-events: none;\n\t\t\t\t\t\tbackground-color: $accordion--content-bg-color;\n\n\t\t\t\t\t\tth, td {\n\t\t\t\t\t\t\tcolor: $accordion--disabled-color;\n\n\t\t\t\t\t\t\t.sui-toggle {\n\t\t\t\t\t\t\t\tpointer-events: initial;\n\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t.sui-circle-score svg circle:last-child {\n\t\t\t\t\t\t\t\tstroke: $accordion--disabled-icon;\n\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t[class*=\"sui-icon-\"]:before {\n\t\t\t\t\t\t\t\tcolor: $accordion--disabled-icon;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t&.sui-error,\n\t\t\t\t\t\t&.sui-warning,\n\t\t\t\t\t\t&.sui-success {\n\t\t\t\t\t\t\tbox-shadow: inset 3px 0 0 $accordion--disabled-icon;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t+ .sui-accordion-item-content {\n\t\t\t\t\t\t\tdisplay: none;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t> .sui-accordion-item-content {\n\t\t\t\t\tvisibility: hidden;\n\t\t\t\t\tposition: absolute;\n\t\t\t\t\tz-index: -1;\n\t\t\t\t\tbackground-color: $accordion--content-bg-color;\n\t\t\t\t\theight: 0;\n\t\t\t\t\toverflow: hidden;\n\n\t\t\t\t\tth, td {\n\t\t\t\t\t\tpadding: 0 $sui-gutter-md $sui-gutter-md;\n\n\t\t\t\t\t\t@include media(min-width, md) {\n\t\t\t\t\t\t\tpadding: 0 $sui-gutter $sui-gutter;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t// FIX:\n\t\t\t\t// Assign border radius to last group of items on table.\n\t\t\t\t// Each group is defined by:\n\t\t\t\t// .sui-accordion-item and .sui-accordion-item-content\n\t\t\t\t.sui-table-item-last {\n\n\t\t\t\t\t&.sui-accordion-item,\n\t\t\t\t\t&.sui-accordion-item-content {\n\t\t\t\t\t\tborder-radius: 0 0 $border-radius $border-radius;\n\t\t\t\t\t}\n\n\t\t\t\t\t&.sui-accordion-item {\n\n\t\t\t\t\t\t&.sui-accordion-item--open {\n\t\t\t\t\t\t\tborder-radius: 0;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t// FIX:\n\t\t\t\t// Assign border radius to first row if thead doesn't exist.\n\t\t\t\t.sui-table-item-first {\n\t\t\t\t\tborder-radius: $border-radius $border-radius 0 0;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t// DESIGN: Blocks (Flexbox)\n\t\t&-block {\n\n\t\t\t.sui-accordion-item {\n\t\t\t\tmargin: 0 0 $sui-gutter-md;\n\t\t\t\tborder-radius: $border-radius;\n\t\t\t\tbackground-color: $accordion--block-background;\n\t\t\t\tbox-shadow: 0 2px 0 0 $accordion--block-shadow;\n\n\t\t\t\t.sui-accordion-item-header {\n\t\t\t\t\tmin-height: 60px;\n\t\t\t\t\tpadding: 15px 0;\n\t\t\t\t\tcolor: $accordion--block-color;\n\t\t\t\t\tfont-size: 13px;\n\t\t\t\t\tline-height: 22px;\n\t\t\t\t\tfont-family: $font;\n\t\t\t\t\tletter-spacing: $font--letter-spacing;\n\n\t\t\t\t\tstrong {\n\t\t\t\t\t\tmargin-right: 10px;\n\t\t\t\t\t\tcolor: $table--th-color;\n\t\t\t\t\t}\n\n\t\t\t\t\t.sui-accordion-item-title {\n\t\t\t\t\t\tcolor: $table--th-color;\n\t\t\t\t\t\tfont-size: 22px;\n\t\t\t\t\t\tline-height: 30px;\n\t\t\t\t\t\tfont-family: $font;\n\t\t\t\t\t\tfont-weight: bold;\n\n\t\t\t\t\t\t.sui-tag {\n\n\t\t\t\t\t\t\t@include media(max-width, md) {\n\t\t\t\t\t\t\t\tdisplay: block;\n\t\t\t\t\t\t\t\tbottom: #{math.div($sui-gutter-md, 2) + 7px};\n\t\t\t\t\t\t\t\tleft: #{math.div($sui-gutter-md, 2) - 1px};\n\t\t\t\t\t\t\t\tposition: absolute;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t&.sui-trim-title {\n\t\t\t\t\t\t\toverflow: hidden;\n\t\t\t\t\t\t\tdisplay: flex;\n\t\t\t\t\t\t\talign-items: center;\n\t\t\t\t\t\t\ttext-overflow: ellipsis;\n\t\t\t\t\t\t\twhite-space: nowrap;\n\n\t\t\t\t\t\t\tspan {\n\t\t\t\t\t\t\t\tflex: 0 0 auto;\n\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t.sui-trim-text {\n\t\t\t\t\t\t\t\toverflow: hidden;\n\t\t\t\t\t\t\t\tdisplay: block;\n\t\t\t\t\t\t\t\tflex: 0 1 auto;\n\t\t\t\t\t\t\t\talign-items: center;\n\t\t\t\t\t\t\t\ttext-overflow: ellipsis;\n\t\t\t\t\t\t\t\twhite-space: nowrap;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\n\t\t\t\t\t.sui-mobile-visible {\n\n\t\t\t\t\t\t@include media(min-width, md) {\n\t\t\t\t\t\t\tdisplay: none;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\n\t\t\t\t\t.sui-desktop-visible {\n\n\t\t\t\t\t\t@include media(max-width, md) {\n\t\t\t\t\t\t\tdisplay: none;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\n\t\t\t\t\t.sui-accordion-item-date {\n\n\t\t\t\t\t\t@include media(max-width, md) {\n\t\t\t\t\t\t\tdisplay: none;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\n\t\t\t\t\t.sui-button,\n\t\t\t\t\t.sui-dropdown,\n\t\t\t\t\t.sui-button-icon {\n\t\t\t\t\t\tmargin: 0 5px;\n\n\t\t\t\t\t\t.sui-button-icon {\n\t\t\t\t\t\t\tmargin: 0;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t&:first-child {\n\t\t\t\t\t\t\tmargin-left: 0;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t&:last-child {\n\t\t\t\t\t\t\tmargin-right: 0;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\n\t\t\t\t\t.sui-tag {\n\t\t\t\t\t\tmargin-left: 10px;\n\n\t\t\t\t\t\t&.sui-tag-right {\n\t\t\t\t\t\t\tmargin-right: 10px;\n\t\t\t\t\t\t\tmargin-left: 0;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\n\t\t\t\t\t.sui-accordion-col-auto {\n\n\t\t\t\t\t\t&:not(:first-child) {\n\n\t\t\t\t\t\t\t@include media(max-width, md) {\n\t\t\t\t\t\t\t\tmargin-top: 10px;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t@include media(max-width, md) {\n\t\t\t\t\t\t\tflex: 0 0 100%;\n\t\t\t\t\t\t\tjustify-content: flex-end;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\n\t\t\t\t\t@include media(max-width, md) {\n\t\t\t\t\t\tflex-wrap: wrap;\n\t\t\t\t\t\tposition: relative;\n\t\t\t\t\t}\n\n\t\t\t\t\t@include media(min-width, md) {\n\t\t\t\t\t\tmin-height: 90px;\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t.sui-accordion-item-body {\n\t\t\t\t\tpadding: 0 $sui-gutter-md $sui-gutter-md;\n\n\t\t\t\t\t.sui-accordion-item-data {\n\t\t\t\t\t\tdisplay: flex;\n\t\t\t\t\t\tflex-wrap: wrap;\n\t\t\t\t\t\tmargin: 0 -5px;\n\t\t\t\t\t\tpadding: 0;\n\n\t\t\t\t\t\t&, li {\n\t\t\t\t\t\t\tborder: 0;\n\t\t\t\t\t\t\tlist-style: none;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tli {\n\t\t\t\t\t\t\tfont-size: 13px;\n\t\t\t\t\t\t\tline-height: 22px;\n\t\t\t\t\t\t\tfont-family: $font;\n\t\t\t\t\t\t\tletter-spacing: $font--letter-spacing;\n\t\t\t\t\t\t\ttransition: 0.3s ease;\n\t\t\t\t\t\t\ttext-align: center;\n\n\t\t\t\t\t\t\tstrong, span {\n\t\t\t\t\t\t\t\tdisplay: block;\n\n\t\t\t\t\t\t\t\t@include media(min-width, md) {\n\t\t\t\t\t\t\t\t\tmargin-right: 20px;\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\tstrong {\n\t\t\t\t\t\t\t\tcolor: $table--th-color;\n\t\t\t\t\t\t\t\tfont-weight: 500;\n\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t&[data-col=\"large\"] {\n\n\t\t\t\t\t\t\t\t@include media(min-width, md) {\n\t\t\t\t\t\t\t\t\tmin-width: 200px;\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t&:last-child {\n\t\t\t\t\t\t\t\tmargin: 0;\n\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t@include media(max-width, md) {\n\t\t\t\t\t\t\t\tmin-width: 50%;\n\t\t\t\t\t\t\t\tflex: 0 0 50%;\n\t\t\t\t\t\t\t\tpadding: 10px 5px;\n\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t@include media(min-width, md) {\n\t\t\t\t\t\t\t\tflex: 0 0 auto;\n\t\t\t\t\t\t\t\tmargin: 0;\n\t\t\t\t\t\t\t\tpadding: 0 5px;\n\t\t\t\t\t\t\t\ttext-align: left;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t// Loading data animation when accordion opens\n\t\t\t\t\t\t&.sui-onload {\n\n\t\t\t\t\t\t\tli > * {\n\t\t\t\t\t\t\t\tdisplay: block;\n\t\t\t\t\t\t\t\tmargin-right: 0;\n\t\t\t\t\t\t\t\tbackground-color: palette(silver, light);\n\t\t\t\t\t\t\t\tcolor: transparent;\n\n\t\t\t\t\t\t\t\t&:first-child {\n\t\t\t\t\t\t\t\t\tborder-radius: $border-radius $border-radius 0 0;\n\t\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t\t&:last-child {\n\t\t\t\t\t\t\t\t\tborder-radius: 0 0 $border-radius $border-radius;\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\n\t\t\t\t\t@include media(min-width, md) {\n\t\t\t\t\t\tpadding: 0 $sui-gutter $sui-gutter;\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t&:last-child {\n\t\t\t\t\tmargin: 0;\n\n\t\t\t\t\t@include media(min-width, md) {\n\t\t\t\t\t\tmargin: 0;\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t// When item is open\n\t\t\t\t&.sui-accordion-item--open {\n\n\t\t\t\t\t.sui-accordion-item-header {\n\n\t\t\t\t\t\t.sui-accordion-item-date {\n\t\t\t\t\t\t\tdisplay: none;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t@include media(min-width, md) {\n\t\t\t\t\tmargin: 0 0 $sui-gutter;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\n\t// FIX:\n\t// Improve styles of flushed accordions inside .sui-box\n\t.sui-box {\n\n\t\t.sui-box-header {\n\n\t\t\t+ .sui-accordion-flushed {\n\t\t\t\tborder-top-width: 0;\n\t\t\t}\n\t\t}\n\n\t\t.sui-box-body {\n\n\t\t\t> .sui-accordion-flushed {\n\n\t\t\t\t&:not(.sui-accordion-block) {\n\t\t\t\t\tmargin-right: -#{$sui-gutter-md};\n\t\t\t\t\tmargin-left: -#{$sui-gutter-md};\n\n\t\t\t\t\t@include media(min-width, md) {\n\t\t\t\t\t\tmargin-right: -#{$sui-gutter};\n\t\t\t\t\t\tmargin-left: -#{$sui-gutter};\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t.sui-side-tabs .sui-tab-content {\n\n\t\t\t\t> .sui-accordion-flushed {\n\n\t\t\t\t\t&:not(.sui-accordion-block) {\n\t\t\t\t\t\tmargin-bottom: -#{$sui-gutter-md};\n\n\t\t\t\t\t\t@include media(min-width, md) {\n\t\t\t\t\t\t\tmargin-bottom: -#{$sui-gutter};\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\n\t// FIX:\n\t// Improve flushed accordions inside .sui-tabs\n\t.sui-tabs:not(.sui-side-tabs) {\n\n\t\t> [data-panes],\n\t\t> .sui-tabs-content {\n\n\t\t\t> *,\n\t\t\t> .sui-tab-content {\n\n\t\t\t\t.sui-accordion {\n\n\t\t\t\t\t&.sui-table,\n\t\t\t\t\t&:not(.sui-table):not(.sui-accordion-block):not(.sui-builder-fields) {\n\n\t\t\t\t\t\t&.sui-accordion-flushed {\n\t\t\t\t\t\t\tmargin-right: -#{$sui-gutter-md};\n\t\t\t\t\t\t\tmargin-left: -#{$sui-gutter-md};\n\n\t\t\t\t\t\t\t@include media(min-width, md) {\n\t\t\t\t\t\t\t\tmargin-right: -#{$sui-gutter};\n\t\t\t\t\t\t\t\tmargin-left: -#{$sui-gutter};\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\n\t// FIX:\n\t// Improve flushed accordions inside .sui-side-tabs\n\t.sui-side-tabs {\n\n\t\t> .sui-tabs-content {\n\n\t\t\t> .sui-tab-content {\n\n\t\t\t\t.sui-accordion {\n\n\t\t\t\t\t&.sui-table,\n\t\t\t\t\t&:not(.sui-table):not(.sui-accordion-block):not(.sui-builder-fields) {\n\n\t\t\t\t\t\t&.sui-accordion-flushed {\n\t\t\t\t\t\t\tmargin-right: -#{$sui-gutter-md};\n\t\t\t\t\t\t\tmargin-left: -#{$sui-gutter-md};\n\n\t\t\t\t\t\t\t@include media(min-width, md) {\n\t\t\t\t\t\t\t\tmargin-right: -#{$sui-gutter};\n\t\t\t\t\t\t\t\tmargin-left: -#{$sui-gutter};\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n}\n", "@use \"sass:math\";\n\n@include body-class(true) {\n\n\t.sui-box-selectors {\n\t\tbackground-color: $box-selectors--background;\n\n\t\tul, li {\n\t\t\tlist-style: none;\n\t\t}\n\n\t\tul {\n\t\t\tdisplay: flex;\n\t\t\tflex-wrap: wrap;\n\t\t\tmargin-top: 0;\n\t\t\tmargin-right: -#{math.div($box-selectors--spacing, 2)};\n\t\t\tmargin-bottom: 0;\n\t\t\tmargin-left: -#{math.div($box-selectors--spacing, 2)};\n\t\t\tpadding: 0;\n\t\t\tborder: 0;\n\n\t\t\tli {\n\t\t\t\tmargin: 0;\n\t\t\t\tpadding: math.div($box-selectors--spacing, 2);\n\t\t\t\tborder: 0;\n\t\t\t}\n\n\t\t\t&.sui-spacing-slim {\n\n\t\t\t\tli {\n\t\t\t\t\tpadding: math.div($box-selectors--spacing, 4);\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t&[class*=\"sui-box-selectors-col-\"] {\n\n\t\t\tul li {\n\t\t\t\tflex: 0 0 auto;\n\t\t\t}\n\t\t}\n\n\t\t&:not([class*=\"sui-box-selectors-col-\"]) {\n\n\t\t\tul li {\n\t\t\t\tflex: 1;\n\t\t\t}\n\t\t}\n\n\t\t@each $column, $size in $box-selectors--columns {\n\n\t\t\t&.sui-box-selectors-#{$column} {\n\n\t\t\t\tul li {\n\n\t\t\t\t\t@include media(max-width, md) {\n\t\t\t\t\t\tmin-width: 100%;\n\t\t\t\t\t\tflex-basis: 100%;\n\t\t\t\t\t}\n\n\t\t\t\t\t@include media(min-width, md) {\n\t\t\t\t\t\tmin-width: $size;\n\t\t\t\t\t\tflex-basis: $size;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t@include media(max-width, md) {\n\t\t\tpadding: #{$sui-gutter-md - math.div($box-selectors--spacing, 2)} $sui-gutter-md;\n\t\t}\n\n\t\t@include media(min-width, md) {\n\t\t\tpadding: #{$sui-gutter - math.div($box-selectors--spacing, 2)} $sui-gutter;\n\t\t}\n\t}\n\n\t.sui-box-selector {\n\t\toverflow: hidden;\n\t\tdisplay: block;\n\t\tposition: relative;\n\t\tborder-radius: $border-radius;\n\t\tbackground-color: $box-selector--background;\n\t\tbox-shadow: $box-selector--box-shadow;\n\t\tcolor: $box-selector--color;\n\t\tfont-family: $font;\n\t\tletter-spacing: $font--letter-spacing;\n\t\ttransition: $transition;\n\n\t\tinput {\n\t\t\t@extend %sui-screen-reader-text;\n\n\t\t\t~ span {\n\t\t\t\tdisplay: block;\n\t\t\t\tpadding-right: #{($box-selector--padding * 3) - 1px};\n\t\t\t\tpadding-left: #{($box-selector--padding * 2) - 1px};\n\t\t\t\tborder: 1px dashed transparent;\n\t\t\t\tborder-radius: $border-radius;\n\t\t\t\ttransition: $transition;\n\n\t\t\t\t&:not(:last-child) {\n\t\t\t\t\tborder-bottom-color: transparent !important;\n\t\t\t\t\tborder-bottom-left-radius: 0;\n\t\t\t\t\tborder-bottom-right-radius: 0;\n\t\t\t\t}\n\n\t\t\t\t&:last-child {\n\t\t\t\t\tborder-top-left-radius: 0;\n\t\t\t\t\tborder-top-right-radius: 0;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t+ span {\n\t\t\t\tdisplay: flex;\n\t\t\t\talign-items: center;\n\t\t\t\tpadding-top: #{math.div($box-selector--height - $box-selector--line-height, 2) - 1px};\n\t\t\t\tpadding-bottom: #{math.div($box-selector--height - $box-selector--line-height, 2) - 1px};\n\t\t\t\tfont-size: $box-selector--font-size;\n\t\t\t\tline-height: $box-selector--line-height;\n\t\t\t\tfont-weight: $font--medium;\n\n\t\t\t\t[class*=\"sui-icon\"] {\n\t\t\t\t\twidth: $box-selector--icon-width;\n\t\t\t\t\tflex: 0 0 $box-selector--icon-width;\n\t\t\t\t\talign-self: flex-start;\n\t\t\t\t\tmargin: 0 5px 0 0;\n\t\t\t\t\tfont-size: $box-selector--icon-size;\n\t\t\t\t\ttext-align: center;\n\n\t\t\t\t\t&:before {\n\t\t\t\t\t\tdisplay: block;\n\t\t\t\t\t\tcolor: $box-selector--icon-color;\n\t\t\t\t\t\tline-height: $box-selector--line-height;\n\t\t\t\t\t\ttransition: $transition;\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\timg {\n\t\t\t\t\tmax-width: $box-selector--image-width;\n\t\t\t\t\theight: auto;\n\t\t\t\t\tdisplay: block;\n\t\t\t\t\tmargin: 0 5px 0 0;\n\t\t\t\t}\n\n\t\t\t\t~ span {\n\t\t\t\t\tpadding-top: #{$box-selector--padding * 2};\n\t\t\t\t\tpadding-bottom: #{$box-selector--padding * 2};\n\t\t\t\t\tborder-top: 1px solid $box-selector--border-color;\n\t\t\t\t\tfont-size: $box-selector--font-size-lg;\n\t\t\t\t\tline-height: $box-selector--line-height-lg;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t&:checked {\n\n\t\t\t\t+ span {\n\t\t\t\t\tposition: relative;\n\t\t\t\t\tbackground-color: $box-selector--active-background;\n\t\t\t\t\tcolor: $box-selector--active-color;\n\n\t\t\t\t\t@include icon(before, check-tick) {\n\t\t\t\t\t\tposition: absolute;\n\t\t\t\t\t\tz-index: 1;\n\t\t\t\t\t\ttop: 5px;\n\t\t\t\t\t\tright: 5px;\n\t\t\t\t\t\tcolor: $box-selector--ribbon-color;\n\t\t\t\t\t\tfont-size: 12px;\n\t\t\t\t\t}\n\n\t\t\t\t\t&:after {\n\t\t\t\t\t\tcontent: \" \";\n\t\t\t\t\t\tposition: absolute;\n\t\t\t\t\t\ttop: -1px;\n\t\t\t\t\t\tright: -1px;\n\t\t\t\t\t\tborder-top: math.div($box-selector--ribbon-height, 2) solid $box-selector--ribbon-background;\n\t\t\t\t\t\tborder-left: math.div($box-selector--ribbon-height, 2) solid transparent;\n\t\t\t\t\t}\n\n\t\t\t\t\t[class*=\"sui-icon\"]:before {\n\t\t\t\t\t\tcolor: $box-selector--active-color;\n\t\t\t\t\t}\n\n\t\t\t\t\t~ span {\n\t\t\t\t\t\tborder-top-color: $box-selector--active-background;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t&:hover {\n\t\t\tbox-shadow: $box-selector--active-box-shadow;\n\t\t}\n\n\t\t&.sui-disabled {\n\t\t\tpointer-events: none;\n\t\t\tposition: relative;\n\t\t\tbackground-color: transparent;\n\t\t\tbox-shadow: none;\n\n\t\t\tinput {\n\n\t\t\t\t+ span {\n\t\t\t\t\tborder-color: palette(silver, medium);\n\n\t\t\t\t\t.sui-tag-pro,\n\t\t\t\t\t.sui-tag-beta {\n\t\t\t\t\t\tposition: absolute;\n\t\t\t\t\t\ttop: 5px;\n\t\t\t\t\t\tright: 5px;\n\t\t\t\t\t}\n\n\t\t\t\t\t~ span {\n\t\t\t\t\t\tborder-color: palette(silver, medium);\n\t\t\t\t\t\tborder-top-style: dashed;\n\t\t\t\t\t\tborder-top-color: palette(gray, lighter);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t&:hover {\n\t\t\t\tbox-shadow: none;\n\t\t\t}\n\t\t}\n\n\t\t// VARIATION: Vertical\n\t\t&.sui-box-selector-vertical {\n\n\t\t\tinput {\n\n\t\t\t\t~ span {\n\t\t\t\t\ttext-align: center;\n\t\t\t\t}\n\n\t\t\t\t+ span {\n\t\t\t\t\tdisplay: block;\n\t\t\t\t\tpadding-top: $box-selector-vertical--padding-bottom;\n\t\t\t\t\tpadding-right: $box-selector--padding;\n\t\t\t\t\tpadding-bottom: $box-selector-vertical--padding-top;\n\t\t\t\t\tpadding-left: $box-selector--padding;\n\n\t\t\t\t\t[class*=\"sui-icon\"] {\n\t\t\t\t\t\tdisplay: block;\n\t\t\t\t\t\tmargin: 0 auto $box-selector-vertical--icon-spacing;\n\n\t\t\t\t\t\t&:before {\n\t\t\t\t\t\t\tline-height: $box-selector--icon-size;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\n\t\t\t\t\timg {\n\t\t\t\t\t\tmargin: 0 auto;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n}", "@include body-class(true) {\n\n\t.sui-upload {\n\t\tmax-width: 100%;\n\t\tdisplay: flex;\n\n\t\tinput[type=\"file\"] {\n\t\t\t@extend %sui-screen-reader-text;\n\t\t}\n\n\t\tbutton {\n\t\t\tcursor: pointer;\n\t\t\tdisplay: block;\n\t\t\ttransition: 0.3s ease;\n\n\t\t\t&,\n\t\t\t&:hover,\n\t\t\t&:focus,\n\t\t\t&:active {\n\t\t\t\toutline: none;\n\t\t\t\tbox-shadow: none;\n\t\t\t}\n\t\t}\n\n\t\t.sui-upload-image {\n\t\t\twidth: $file-upload--image-size;\n\t\t\theight: $file-upload--image-size;\n\t\t\tflex: 0 0 auto;\n\t\t\tposition: relative;\n\t\t\tmargin: 0;\n\t\t\tpadding: $file-upload--image-padding;\n\t\t\tborder: $file-upload--image-border-width $file-upload--image-border-style $file-upload--image-border-color;\n\t\t\tborder-radius: $border-radius;\n\n\t\t\t[class*=\"sui-image-\"] {\n\t\t\t\twidth: #{$file-upload--image-size - (($file-upload--image-border-width * 2) + ($file-upload--image-padding * 2))};\n\t\t\t\theight: #{$file-upload--image-size - (($file-upload--image-border-width * 2) + ($file-upload--image-padding * 2))};\n\t\t\t\tdisplay: block;\n\t\t\t\tborder-radius: #{$border-radius - $file-upload--image-padding};\n\t\t\t}\n\n\t\t\t.sui-image-mask {\n\t\t\t\tbackground-color: $file-upload--image-mask-background;\n\t\t\t\tbackground-image: linear-gradient(45deg, $white 25%, transparent 25%, transparent 75%, $white 75%, $white), linear-gradient(45deg, $white 25%, transparent 25%, transparent 75%, $white 75%, $white);\n\t\t\t\tbackground-size: 12px 12px;\n\t\t\t\tbackground-position: 0 0, 6px 6px;\n\t\t\t}\n\n\t\t\t.sui-image-preview {\n\t\t\t\tcursor: pointer;\n\t\t\t\tdisplay: none;\n\t\t\t\tposition: absolute;\n\t\t\t\ttop: $file-upload--image-padding;\n\t\t\t\tleft: $file-upload--image-padding;\n\t\t\t\tbackground-color: transparent;\n\t\t\t\tbackground-size: cover;\n\t\t\t\tbackground-repeat: no-repeat;\n\t\t\t\tbackground-position: center;\n\n\t\t\t\t@include icon(before, upload-cloud) {\n\t\t\t\t\topacity: 0;\n\t\t\t\t\tdisplay: flex;\n\t\t\t\t\talign-items: center;\n\t\t\t\t\tjustify-content: center;\n\t\t\t\t\tposition: absolute;\n\t\t\t\t\ttop: 0;\n\t\t\t\t\tright: 0;\n\t\t\t\t\tbottom: 0;\n\t\t\t\t\tleft: 0;\n\t\t\t\t\tborder-radius: #{$border-radius - $file-upload--image-padding};\n\t\t\t\t\tcolor: $white;\n\t\t\t\t\tfont-size: 12px;\n\t\t\t\t\tline-height: 12px;\n\t\t\t\t\tbackground-color: $file-upload--image-preview-background;\n\t\t\t\t\ttransition: 0.2s ease;\n\t\t\t\t}\n\n\t\t\t\t&,\n\t\t\t\t&:hover,\n\t\t\t\t&:focus,\n\t\t\t\t&:active {\n\t\t\t\t\toutline: none;\n\t\t\t\t\tbox-shadow: none;\n\t\t\t\t}\n\n\t\t\t\t&:hover,\n\t\t\t\t&:focus,\n\t\t\t\t&:active {\n\n\t\t\t\t\t&:before {\n\t\t\t\t\t\topacity: 1;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t~ .sui-upload-button,\n\t\t\t~ .sui-upload-file {\n\t\t\t\tmargin-left: 5px;\n\t\t\t}\n\n\t\t\t~ .sui-upload-file {\n\t\t\t\tmax-width: calc(100% - #{$file-upload--image-size + 5px});\n\t\t\t\tcursor: initial;\n\t\t\t\tpadding-left: 19px;\n\n\t\t\t\t&:before {\n\t\t\t\t\tcontent: unset;\n\t\t\t\t}\n\n\t\t\t\t&:hover,\n\t\t\t\t&:active {\n\t\t\t\t\tbackground-color: $file-upload--file-background;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t.sui-upload-file {\n\t\t\tmax-width: 100%;\n\t\t\tcursor: pointer;\n\t\t\tflex: 0 0 auto;\n\t\t\tdisplay: none;\n\t\t\tposition: relative;\n\t\t\tpadding: 11px 61px 11px 41px;\n\t\t\tborder: 1px solid $file-upload--file-border-color;\n\t\t\tborder-radius: $border-radius;\n\t\t\tbackground-color: $file-upload--file-background;\n\t\t\ttransition: 0.3s ease;\n\t\t\toverflow-wrap: break-word;\n\n\t\t\t@include icon(before, page-pdf) {\n\t\t\t\twidth: 29px;\n\t\t\t\tdisplay: block;\n\t\t\t\tposition: absolute;\n\t\t\t\tleft: 14px;\n\t\t\t\tfont-size: 16px;\n\t\t\t\tline-height: 16px;\n\t\t\t\ttext-align: center;\n\t\t\t}\n\n\t\t\tspan {\n\t\t\t\tuser-select: none;\n\t\t\t\tdisplay: block;\n\t\t\t\tcolor: $file-upload--file-color;\n\t\t\t\tfont: $font--medium 12px/16px $font;\n\t\t\t\tletter-spacing: $font--letter-spacing;\n\n\t\t\t\t&:focus {\n\t\t\t\t\toutline: none;\n\t\t\t\t\tbox-shadow: none;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tbutton {\n\t\t\t\twidth: #{$file-upload--image-size - 1px};\n\t\t\t\tposition: absolute;\n\t\t\t\ttop: 0;\n\t\t\t\tright: 0;\n\t\t\t\tbottom: 0;\n\t\t\t\tborder: 0 solid $file-upload--file-border-color;\n\t\t\t\tborder-left-width: 1px;\n\t\t\t\tborder-radius: 0 #{$border-radius - 1px} #{$border-radius - 1px} 0;\n\t\t\t\tbackground-color: $file-upload--file-background;\n\t\t\t\tfont-size: 12px;\n\t\t\t\ttext-align: center;\n\n\t\t\t\t[class*=\"sui-icon-\"] {\n\t\t\t\t\tdisplay: block;\n\n\t\t\t\t\t&:before {\n\t\t\t\t\t\tdisplay: block;\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t&:hover,\n\t\t\t\t&:active {\n\t\t\t\t\tbackground-color: palette(silver, default);\n\t\t\t\t}\n\n\t\t\t\t&:focus {\n\t\t\t\t\tbackground: palette(silver, soft);\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t&:hover,\n\t\t\t&:active,\n\t\t\t&:focus {\n\t\t\t\tbackground-color: $file-upload--file-hover-background;\n\t\t\t}\n\t\t}\n\n\t\t.sui-upload-button {\n\t\t\tmargin: 0;\n\t\t\tpadding: 11px 19px;\n\t\t\tborder: $file-upload--add-border-width $file-upload--add-border-style $file-upload--add-border-color;\n\t\t\tborder-radius: $border-radius;\n\t\t\tbackground-color: $file-upload--add-background;\n\t\t\tcolor: $file-upload--add-color;\n\t\t\tfont: $font--medium 12px/16px $font;\n\t\t\tletter-spacing: $font--letter-spacing;\n\n\t\t\t&:hover,\n\t\t\t&:focus,\n\t\t\t&:active,\n\t\t\t&.sui-is-dragover {\n\t\t\t\tborder-color: palette(silver, medium);\n\t\t\t\tbackground-color: #FAFAFA;\n\t\t\t}\n\n\t\t\t&:focus {\n\t\t\t\tbox-shadow: 0px 0px 0px 2px palette(silver, default);\n\t\t\t}\n\n\t\t\t[class*=\"sui-icon-\"] {\n\t\t\t\twidth: 24px;\n\t\t\t\tmargin-left: -4px;\n\t\t\t\tcolor: palette(gray, light);\n\t\t\t\tfont-size: 16px;\n\t\t\t\tline-height: 16px;\n\t\t\t\tvertical-align: bottom;\n\t\t\t\ttext-align: center;\n\n\t\t\t\t&:before {\n\t\t\t\t\tdisplay: block;\n\t\t\t\t\tcolor: inherit;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t// STATUS: File uploaded\n\t\t&.sui-has_file {\n\n\t\t\t.sui-upload-image {\n\n\t\t\t\t.sui-image-preview {\n\t\t\t\t\tdisplay: block;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t.sui-upload-file {\n\t\t\t\tdisplay: block;\n\t\t\t}\n\n\t\t\t.sui-upload-button {\n\t\t\t\tdisplay: none;\n\t\t\t}\n\t\t}\n\n\t\t// STATUS: Error\n\t\t+ .sui-notice {\n\t\t\tmargin-top: 10px;\n\t\t}\n\n\t\t// Multiple uploads\n\t\t+ .sui-upload {\n\t\t\tmargin-top: 10px;\n\t\t}\n\t}\n}", "@use \"sass:math\";\n\n@include body-class(true) {\n\n\t.sui-colorpicker-wrap {\n\t\tdisplay: block;\n\n\t\t.sui-colorpicker {\n\t\t\tdisplay: flex;\n\t\t\talign-items: center;\n\n\t\t\t.sui-colorpicker-value {\n\t\t\t\tdisplay: block;\n\t\t\t\tposition: relative;\n\n\t\t\t\tspan[role=button],\n\t\t\t\tinput,\n\t\t\t\tbutton {\n\t\t\t\t\tdisplay: block;\n\t\t\t\t\tmargin: 0;\n\t\t\t\t\tborder: $colorpicker--border-width solid $colorpicker--border-color;\n\t\t\t\t\tborder-radius: $border-radius;\n\n\t\t\t\t\t&,\n\t\t\t\t\t&:hover,\n\t\t\t\t\t&:focus {\n\t\t\t\t\t\toutline: none;\n\t\t\t\t\t\tbox-shadow: none;\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\tspan[role=button],\n\t\t\t\tbutton {\n\t\t\t\t\twidth: $colorpicker--button-size;\n\t\t\t\t\theight: $colorpicker--button-size;\n\t\t\t\t\tcursor: pointer;\n\t\t\t\t\tposition: absolute;\n\t\t\t\t\ttop: 0;\n\t\t\t\t\tpadding: 0;\n\t\t\t\t}\n\n\t\t\t\tinput,\n\t\t\t\tbutton {\n\t\t\t\t\tbackground-color: $colorpicker--background;\n\t\t\t\t}\n\n\t\t\t\t// Color preview\n\t\t\t\tspan[role=button] {\n\t\t\t\t\tleft: 0;\n\t\t\t\t\tbackground-color: $colorpicker--preview-background;\n\t\t\t\t\tbackground-image: linear-gradient(45deg, $white 25%, transparent 25%, transparent 75%, $white 75%, $white), linear-gradient(45deg, $white 25%, transparent 25%, transparent 75%, $white 75%, $white);\n\t\t\t\t\tbackground-size: 12px 12px;\n\t\t\t\t\tbackground-position: 0 0, 6px 6px;\n\n\t\t\t\t\tspan {\n\t\t\t\t\t\twidth: #{$colorpicker--button-size - 4px};\n\t\t\t\t\t\theight: #{$colorpicker--button-size - 4px};\n\t\t\t\t\t\tdisplay: block;\n\t\t\t\t\t\tposition: absolute;\n\t\t\t\t\t\ttop: 1px;\n\t\t\t\t\t\tleft: 1px;\n\t\t\t\t\t\tborder-radius: #{$border-radius - 2px};\n\t\t\t\t\t}\n\n\t\t\t\t\t&,\n\t\t\t\t\t&:hover,\n\t\t\t\t\t&:focus {\n\t\t\t\t\t\tbox-shadow: inset 0 0 0 1px $white;\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t// Color value\n\t\t\t\tinput {\n\t\t\t\t\twidth: 100%;\n\t\t\t\t\theight: $colorpicker--input-height;\n\t\t\t\t\tpadding: math.div(($colorpicker--input-height - $colorpicker--input-line-height - ($colorpicker--border-width * 2)), 2) #{$colorpicker--button-size + 10px};\n\t\t\t\t\tcolor: $colorpicker--color;\n\t\t\t\t\tfont: $font--medium #{$colorpicker--input-font-size}/#{$colorpicker--input-line-height} $font;\n\t\t\t\t\tletter-spacing: $font--letter-spacing;\n\t\t\t\t}\n\n\t\t\t\t// Color clear\n\t\t\t\tbutton {\n\t\t\t\t\tright: 0;\n\t\t\t\t\tborder-top-left-radius: 0;\n\t\t\t\t\tborder-bottom-left-radius: 0;\n\n\t\t\t\t\t[class*=\"sui-icon-\"] {\n\t\t\t\t\t\tfont-size: 12px;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t.sui-button {\n\t\t\t\tmargin-left: 5px;\n\t\t\t}\n\n\t\t\t&.sui-colorpicker-hex {\n\n\t\t\t\t.sui-colorpicker-value {\n\t\t\t\t\tflex: 0 1 $colorpicker--hex-width;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t&.sui-colorpicker-rgba {\n\n\t\t\t\t.sui-colorpicker-value {\n\t\t\t\t\tflex: 0 1 $colorpicker--rgba-width;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t.wp-picker-container {\n\t\t\tdisplay: none;\n\t\t\tmargin: 5px 0 0;\n\t\t\tpadding: 9px;\n\t\t\tborder: $colorpicker--border-width solid $colorpicker--iris-border-color;\n\t\t\tborder-radius: $border-radius;\n\t\t\tbackground-color: $colorpicker--iris-background;\n\t\t\tbox-shadow: 0 3px 7px 0 rgba(0,0,0,0.07);\n\n\t\t\t.button {\n\t\t\t\t@extend %sui-screen-reader-text;\n\t\t\t}\n\n\t\t\t.wp-picker-input-wrap {\n\t\t\t\tdisplay: block;\n\n\t\t\t\tspan,\n\t\t\t\tlabel,\n\t\t\t\t.sui-colorpicker-input {\n\t\t\t\t\tdisplay: block;\n\t\t\t\t}\n\n\t\t\t\t.sui-colorpicker-input {\n\t\t\t\t\twidth: 100% !important;\n\t\t\t\t\theight: $colorpicker--input-height;\n\t\t\t\t\tmargin: 0;\n\t\t\t\t\tpadding: math.div(($colorpicker--input-height - $colorpicker--input-line-height - ($colorpicker--border-width * 2)), 2) 11px;\n\t\t\t\t\tborder: $colorpicker--border-width solid $colorpicker--border-color;\n\t\t\t\t\tborder-radius: $border-radius;\n\t\t\t\t\tbackground-color: $colorpicker--background;\n\t\t\t\t\tcolor: $colorpicker--color;\n\t\t\t\t\tfont: $font--medium #{$colorpicker--input-font-size}/#{$colorpicker--input-line-height} $font;\n\t\t\t\t\tletter-spacing: $font--letter-spacing;\n\n\t\t\t\t\t&,\n\t\t\t\t\t&:hover,\n\t\t\t\t\t&:focus {\n\t\t\t\t\t\toutline: none;\n\t\t\t\t\t\tbox-shadow: none;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t.wp-picker-holder {\n\n\t\t\t\t.iris-picker {\n\t\t\t\t\twidth: auto !important;\n\t\t\t\t\theight: auto !important;\n\t\t\t\t\tposition: relative;\n\t\t\t\t\tmargin: 10px 0 0;\n\t\t\t\t\tpadding: 0 !important;\n\t\t\t\t\tborder: 0;\n\n\t\t\t\t\t.iris-picker-inner {\n\t\t\t\t\t\tdisplay: flex;\n\t\t\t\t\t\tposition: unset;\n\n\t\t\t\t\t\t@media all and (-ms-high-contrast: none), (-ms-high-contrast: active) {\n\t\t\t\t\t\t\tposition: relative;\n\t\t\t\t\t\t\tdisplay: block;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\n\t\t\t\t\t.iris-palette-container {\n\t\t\t\t\t\tdisplay: flex;\n\t\t\t\t\t\tleft: 0;\n\t\t\t\t\t\tbottom: 0;\n\n\t\t\t\t\t\t@include media(max-width, md) {\n\t\t\t\t\t\t\tposition: relative;\n\t\t\t\t\t\t\tmargin-top: 10px;\n\t\t\t\t\t\t\tjustify-content: space-between;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t@include media(min-width, md) {\n\t\t\t\t\t\t\twidth: $colorpicker--iris-square-size;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t@media all and (-ms-high-contrast: none), (-ms-high-contrast: active) {\n\t\t\t\t\t\t\tmargin-left: 10px;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\n\t\t\t\t\t.iris-square,\n\t\t\t\t\t.iris-slider,\n\t\t\t\t\t.iris-palette {\n\t\t\t\t\t\tfloat: none;\n\t\t\t\t\t\tbox-shadow: none;\n\t\t\t\t\t}\n\n\t\t\t\t\t// ELEMENT: Rainbow\n\t\t\t\t\t// A square full of colors for user to pick.\n\t\t\t\t\t.iris-square {\n\t\t\t\t\t\twidth: auto !important;\n\t\t\t\t\t\theight: auto !important;\n\t\t\t\t\t\tmargin-right: 0;\n\t\t\t\t\t\tborder-radius: $border-radius;\n\n\t\t\t\t\t\t@include media(min-width, md) {\n\t\t\t\t\t\t\tmargin-bottom: #{$colorpicker--iris-slider-size - $colorpicker--iris-square-size};\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t&-inner {\n\t\t\t\t\t\t\tbox-shadow: none;\n\n\t\t\t\t\t\t\t&.iris-square-horiz {\n\t\t\t\t\t\t\t\tposition: unset;\n\t\t\t\t\t\t\t\tdisplay: block;\n\n\t\t\t\t\t\t\t\t@include media(max-width, md) {\n\t\t\t\t\t\t\t\t\twidth: $colorpicker--iris-square-size-sm;\n\t\t\t\t\t\t\t\t\theight: $colorpicker--iris-square-size-sm;\n\t\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t\t@include media(min-width, md) {\n\t\t\t\t\t\t\t\t\twidth: $colorpicker--iris-square-size;\n\t\t\t\t\t\t\t\t\theight: $colorpicker--iris-square-size;\n\t\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t\t@media all and (-ms-high-contrast: none), (-ms-high-contrast: active) {\n\t\t\t\t\t\t\t\t\tposition: relative;\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t@media all and (-ms-high-contrast: none), (-ms-high-contrast: active) {\n\t\t\t\t\t\t\tdisplay: inline-block;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\n\t\t\t\t\t// ELEMENT: Slider\n\t\t\t\t\t// Vertical rectangles that allow user to adjust\n\t\t\t\t\t// color opacity.\n\t\t\t\t\t.iris-slider {\n\t\t\t\t\t\twidth: $colorpicker--iris-palette-size !important;\n\t\t\t\t\t\tmargin-left: 10px !important;\n\t\t\t\t\t\tborder-radius: math.div($border-radius, 2);\n\n\t\t\t\t\t\t@include media(max-width, md) {\n\t\t\t\t\t\t\theight: $colorpicker--iris-square-size-sm !important;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t@include media(min-width, md) {\n\t\t\t\t\t\t\theight: $colorpicker--iris-slider-size !important;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t@media all and (-ms-high-contrast: none), (-ms-high-contrast: active) {\n\t\t\t\t\t\t\tdisplay: inline-block;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\n\t\t\t\t\t// ELEMENT: Slider offset\n\t\t\t\t\t.iris-slider-offset {\n\t\t\t\t\t\ttop: $colorpicker--iris-slider-handle-size;\n\t\t\t\t\t\tbottom: 0;\n\t\t\t\t\t}\n\n\t\t\t\t\t// ELEMENT: Slider handle\n\t\t\t\t\t// A visual element that allow user to find the\n\t\t\t\t\t// desired opacity or tone for the color.\n\t\t\t\t\t.ui-slider-handle {\n\t\t\t\t\t\theight: $colorpicker--iris-slider-handle-size;\n\t\t\t\t\t\topacity: 1;\n\t\t\t\t\t\tright: -#{$colorpicker--iris-slider-handle-border-width};\n\t\t\t\t\t\tleft: -#{$colorpicker--iris-slider-handle-border-width};\n\t\t\t\t\t\tborder-width: $colorpicker--iris-slider-handle-border-width;\n\t\t\t\t\t\tborder-color: $colorpicker--iris-slider-handle-border-color;\n\t\t\t\t\t\tborder-radius: #{$colorpicker--iris-slider-handle-border-width * 2};\n\t\t\t\t\t\tbox-shadow: 0 1px 3px 1px rgba(0,0,0,0.3);\n\n\t\t\t\t\t\t&:before {\n\t\t\t\t\t\t\tcontent: unset;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\n\t\t\t\t\t// ELEMENT: Palette\n\t\t\t\t\t// Predefined colors for user to quick-select.\n\t\t\t\t\t.iris-palette {\n\t\t\t\t\t\twidth: auto !important;\n\t\t\t\t\t\tmin-width: $colorpicker--iris-palette-size;\n\t\t\t\t\t\tmax-width: $colorpicker--iris-palette-size;\n\t\t\t\t\t\theight: $colorpicker--iris-palette-size !important;\n\t\t\t\t\t\tdisplay: block;\n\t\t\t\t\t\tflex: 0 0 $colorpicker--iris-palette-size;\n\t\t\t\t\t\tborder: 1px solid $colorpicker--iris-palette-border-color;\n\n\t\t\t\t\t\t&:first-child {\n\t\t\t\t\t\t\tmargin-left: 0 !important;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t&:nth-child(n+8) {\n\t\t\t\t\t\t\tdisplay: none;\n\t\t\t\t\t\t\tmargin-left: 0 !important;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t@include media(max-width, md) {\n\t\t\t\t\t\t\tmargin-left: math.div(($colorpicker--iris-square-size - ($colorpicker--iris-palette-size * 7)), 6) !important;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t@include media(min-width, md) {\n\t\t\t\t\t\t\tmargin-left: math.div(($colorpicker--iris-square-size - ($colorpicker--iris-palette-size * 7)), 6) !important;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\n\t\t\t\t\t// ELEMENT: Rainbow value\n\t\t\t\t\t// A hidden element that gets the correct value\n\t\t\t\t\t// from the rainbow.\n\t\t\t\t\t.iris-square-value {\n\t\t\t\t\t\twidth: $colorpicker--iris-square-value-size;\n\t\t\t\t\t\theight: $colorpicker--iris-square-value-size;\n\t\t\t\t\t}\n\n\t\t\t\t\t// ELEMENT: Rainbow donut\n\t\t\t\t\t// A visual element that allow user to find the\n\t\t\t\t\t// desired color from rainbow.\n\t\t\t\t\t.iris-square-handle {\n\t\t\t\t\t\twidth: $colorpicker--iris-square-handle-size;\n\t\t\t\t\t\theight: $colorpicker--iris-square-handle-size;\n\t\t\t\t\t\topacity: 1;\n\t\t\t\t\t\ttop: -#{math.div($colorpicker--iris-square-handle-size - $colorpicker--iris-square-value-size, 2)};\n\t\t\t\t\t\tleft: -#{math.div($colorpicker--iris-square-handle-size - $colorpicker--iris-square-value-size, 2)};\n\t\t\t\t\t\tborder-width: $colorpicker--iris-square-handle-border-width;\n\t\t\t\t\t\tborder-color: $colorpicker--iris-square-handle-border-color;\n\t\t\t\t\t\tborder-radius: 100%;\n\t\t\t\t\t\tbox-shadow: 0 1px 5px 0 rgba(0,0,0,0.3);\n\n\t\t\t\t\t\t&:after {\n\t\t\t\t\t\t\tcontent: unset;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t// STATUS: Iris open\n\t\t\t&.wp-picker-active {\n\t\t\t\tdisplay: inline-flex;\n\t\t\t\tflex-direction: column;\n\n\t\t\t\t@media all and (-ms-high-contrast: none), (-ms-high-contrast: active) {\n\t\t\t\t\twidth: 262px;\n\t\t\t\t\tposition: relative;\n\t\t\t\t\tdisplay: block;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n}", "@include body-class(true) {\n\t.sui-upgrade-page {\n\t\tbackground: #FAFAFA;\n\t\tmargin: -30px -30px 60px;\n\n\t\t@media (max-width: 783px) {\n\t\t\tmargin: -21px -10px 0;\n\t\t}\n\n\t\t&:not(:first-child) {\n\t\t\tmargin-top: 30px;\n\t\t}\n\n\t\t&__container {\n\t\t\tmax-width: 1140px;\n\t\t\tmargin: auto;\n\t\t\tdisplay: flex;\n\t\t\t@media (max-width: 800px) {\n\t\t\t\tflex-wrap: wrap;\n\t\t\t}\n\t\t}\n\n\t\t/** Header and Image **/\n\t\t&-header {\n\t\t\tbackground: $white;\n\t\t\tmargin-bottom: 60px;\n\t\t\t@media (max-width: 600px) {\n\t\t\t\tmargin-bottom: 40px;\n\t\t\t}\n\t\t\t&__content {\n\t\t\t\tpadding: 60px 60px 60px 0;\n\t\t\t\twidth: 50%;\n\t\t\t\t@media (max-width: 1440px) {\n\t\t\t\t\tpadding: 60px 30px 50px 60px;\n\t\t\t\t}\n\t\t\t\t@media (max-width: 800px) {\n\t\t\t\t\torder: 1;\n\t\t\t\t\twidth: 100%;\n\t\t\t\t}\n\t\t\t\t@media (max-width: 600px) {\n\t\t\t\t\tpadding: 40px 20px;\n\t\t\t\t}\n\t\t\t\th1 {\n\t\t\t\t\tfont-size: 28px;\n\t\t\t\t\tfont-family: $font;\n\t\t\t\t}\n\t\t\t\tp:first-of-type {\n\t\t\t\t\tmargin-top: 10px;\n\t\t\t\t}\n\t\t\t}\n\t\t\t&__image {\n\t\t\t\t@if variable-exists(upgrade-image) {\n\t\t\t\t\tbackground-image: url($upgrade-image);\n\t\t\t\t}\n\t\t\t\tbackground-size: cover;\n\t\t\t\tbackground-position: center;\n\t\t\t\tbackground-repeat: no-repeat;\n\t\t\t\twidth: 50%;\n\t\t\t\t@media (max-width: 800px) {\n\t\t\t\t\t@if variable-exists(upgrade-image-mobile) {\n\t\t\t\t\t\tbackground-image: url($upgrade-image-mobile);\n\t\t\t\t\t\twidth: 100%;\n\t\t\t\t\t\torder: 0;\n\t\t\t\t\t\theight: 250px;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t\t.sui-reviews {\n\t\t\t\tmargin-top: 60px;\n\t\t\t\t@media (max-width: 600px) {\n\t\t\t\t\tmargin-top: 40px;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t/** Pro Features **/\n\t\t&-features {\n\t\t\t&__header {\n\t\t\t\ttext-align: center;\n\n\t\t\t\th2 {\n\t\t\t\t\tfont-family: $font;\n\t\t\t\t\tfont-size: 28px;\n\t\t\t\t\tcolor: palette(gray, dark);\n\t\t\t\t\tline-height: 40px;\n\t\t\t\t\tmargin-bottom: 0;\n\t\t\t\t}\n\t\t\t\tp {\n\t\t\t\t\tmargin-top: 5px;\n\t\t\t\t}\n\t\t\t}\n\t\t\t&__items {\n\t\t\t\tdisplay: flex;\n\t\t\t\tflex-wrap: wrap;\n\t\t\t\tpadding: 30px;\n\n\t\t\t\t@media (min-width: 1440px) {\n\t\t\t\t\tmargin: 0 -60px;\n\t\t\t\t}\n\n\t\t\t\t@media (max-width: 600px) {\n\t\t\t\t\tpadding: 0;\n\t\t\t\t\tmargin: 40px 0;\n\t\t\t\t}\n\t\t\t}\n\t\t\t&__item {\n\t\t\t\tflex: 0 0 50%;\n\t\t\t\tmax-width: 50%;\n\t\t\t\tpadding: 30px;\n\n\t\t\t\t@media (max-width: 600px) {\n\t\t\t\t\tflex: 0 0 100%;\n\t\t\t\t\tmax-width: 100%;\n\t\t\t\t\tpadding: 20px;\n\t\t\t\t\ttext-align: center;\n\t\t\t\t}\n\n\t\t\t\t[class*=\"sui-icon-\"] {\n\t\t\t\t\theight: 44px;\n\t\t\t\t\twidth: 44px;\n\t\t\t\t\ttext-align: center;\n\t\t\t\t\tbackground: palette(purple, light);\n\t\t\t\t\tborder-radius: 50%;\n\t\t\t\t\tfont-size: 20px;\n\t\t\t\t\t&:before {\n\t\t\t\t\t\tcolor: palette(purple);\n\t\t\t\t\t\tline-height: 44px;\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\th3 {\n\t\t\t\t\tfont-family: $font;\n\t\t\t\t\tmargin-bottom: 0;\n\t\t\t\t}\n\n\t\t\t\tp:first-of-type {\n\t\t\t\t\tmargin-top: 0;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t/** CTA **/\n\t\t&-cta {\n\t\t\tbackground: $white;\n\t\t\tpadding: 60px;\n\t\t\tmargin-bottom: 60px;\n\n\t\t\t@media (max-width: 600px) {\n\t\t\t\tpadding: 40px 20px;\n\t\t\t\tmargin-bottom: 40px;\n\t\t\t}\n\t\t\t&__inner {\n\t\t\t\tmax-width: 600px;\n\t\t\t\tmargin: auto;\n\t\t\t\ttext-align: center;\n\t\t\t\th2 {\n\t\t\t\t\tfont-family: $font;\n\t\t\t\t\tfont-size: 28px;\n\t\t\t\t\tcolor: palette(gray, dark);\n\t\t\t\t\tline-height: 40px;\n\t\t\t\t\tmargin-bottom: 0;\n\t\t\t\t}\n\t\t\t\ta {\n\t\t\t\t\tdisplay: block;\n\t\t\t\t\tfont-size: 13px;\n\t\t\t\t\tcolor: palette(gray, light);\n\t\t\t\t\tline-height: 22px;\n\t\t\t\t\tmargin-top: 15px;\n\t\t\t\t\t&:focus,\n\t\t\t\t\t&:hover {\n\t\t\t\t\t\tcolor: palette(gray);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\t.sui-hidden-desktop {\n\t\t\t\t\tdisplay: none;\n\t\t\t\t}\n\t\t\t\t@media (max-width: 600px) {\n\t\t\t\t\t.sui-hidden-desktop {\n\t\t\t\t\t\tdisplay: inline-block;\n\t\t\t\t\t}\n\t\t\t\t\t.sui-button:not(.sui-hidden-desktop) {\n\t\t\t\t\t\tdisplay: none;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n}\n", "@include body-class(true) {\n\t.sui-reviews {\n\t\tmargin-left: 25px;\n\t\t&__stars {\n\t\t\tposition: relative;\n\t\t\tmargin-right: 5px;\n\t\t\t@include icon(before, quote-2);\n\t\t\t&:before {\n\t\t\t\tposition: absolute;\n\t\t\t\ttop: -12px;\n\t\t\t\tleft: -25px;\n\t\t\t\tcolor: palette(silver, medium);\n\t\t\t}\n\t\t\t[class*=\"sui-icon-\"] {\n\t\t\t\t&:before{\n\t\t\t\t\tcolor: palette(yellow);\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t\t&__rating {\n\t\t\tcolor: palette(gray, light);\n\t\t\tfont-size: 13px;\n\t\t\tline-height: 22px;\n\t\t\tvertical-align: text-bottom;\n\t\t\tdisplay: inline-block;\n\t\t}\n\t\ta.sui-reviews__link {\n\t\t\tdisplay: block;\n\t\t\tcolor: palette(gray, light);\n\t\t\tfont-size: 13px;\n\t\t\tline-height: 22px;\n\t\t\tfont-weight: normal;\n\t\t\tfont-style: italic;\n\t\t\t&:focus,\n\t\t\t&:hover {\n\t\t\t\tcolor: palette(gray);\n\t\t\t\t[class*=\"sui-icon-\"] {\n\t\t\t\t\t&:before {\n\t\t\t\t\t\tcolor: palette(gray);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t\t[class*=\"sui-icon-\"] {\n\t\t\t\tmargin-left: 5px;\n\t\t\t\tfont-size: 12px;\n\t\t\t\tline-height: 22px;\n\t\t\t\tvertical-align: middle;\n\t\t\t\t&:before {\n\t\t\t\t\tcolor: palette(gray, light);\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n}\n", "@include body-class(true) {\n\n\t.sui-code-snippet-wrapper {\n\t\tposition: relative;\n\n\t\t[data-clipboard-target] {\n\t\t\tposition: absolute;\n\t\t\tright: 15px;\n\t\t\ttop: 15px;\n\t\t\tmargin: 0;\n\t\t\tmin-width: auto;\n\t\t}\n\t}\n}\n", "@include body-class($wrap: true, $rtl: false, $monochrome: false) {\n\n\t.sui-upsell {\n\n\t\t&-list {\n\t\t\tmargin: 0;\n\t\t\tpadding: 0;\n\t\t\tlist-style: none;\n\n\t\t\tli {\n\t\t\t\tposition: relative;\n\t\t\t\tmargin: 0 0 22px;\n\t\t\t\tpadding: 0 0 0 26px;\n\t\t\t\tcolor: $nightrider;\n\t\t\t\tfont: 500 15px/22px $font;\n\t\t\t\tletter-spacing: $font--letter-spacing;\n\n\t\t\t\t[class*=\"sui-icon-\"] {\n\t\t\t\t\tdisplay: block;\n\t\t\t\t\tposition: absolute;\n\t\t\t\t\ttop: 3px;\n\t\t\t\t\tleft: 0;\n\t\t\t\t\tcolor: $purple;\n\n\t\t\t\t\t&:before {\n\t\t\t\t\t\tcolor: inherit;\n\t\t\t\t\t\tdisplay: block;\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t&:last-child {\n\t\t\t\t\tmargin: 0;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t&-notice {\n\n\t\t\t@include media(min-width, md) {\n\t\t\t\tdisplay: flex;\n\t\t\t}\n\n\t\t\t&__image {\n\t\t\t\tmargin-right: $sui-gutter-md;\n\n\t\t\t\t@include media(max-width, md) {\n\t\t\t\t\tdisplay: none;\n\t\t\t\t}\n\n\t\t\t\t@include media(min-width, md) {\n\t\t\t\t\tflex: 0 0 auto;\n\t\t\t\t\talign-self: flex-end;\n\t\t\t\t\tmargin-right: $sui-gutter;\n\t\t\t\t}\n\n\t\t\t\timg {\n\t\t\t\t\tdisplay: block;\n\t\t\t\t\tmargin: 0 auto;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t&__content {\n\n\t\t\t\t@include media(min-width, md) {\n\t\t\t\t\tflex: 1;\n\t\t\t\t\talign-self: flex-start;\n\t\t\t\t\tmargin-bottom: $sui-gutter-md;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n}\n\n@include body-class($wrap: true, $rtl: true, $monochrome: false) {\n\n\t.sui-upsell {\n\n\t\t&-list {\n\n\t\t\tli {\n\t\t\t\tpadding-right: 26px;\n\t\t\t\tpadding-left: 0;\n\n\t\t\t\t[class*=\"sui-icon-\"] {\n\t\t\t\t\tright: 0;\n\t\t\t\t\tleft: auto;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t&-notice {\n\n\t\t\t&__image {\n\t\t\t\tmargin-right: 0;\n\t\t\t\tmargin-left: $sui-gutter-md;\n\n\t\t\t\t@include media(min-width, md) {\n\t\t\t\t\tmargin-right: 0;\n\t\t\t\t\tmargin-left: $sui-gutter;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n}\n\n@include body-class($wrap: true, $rtl: false, $monochrome: true) {\n\n\t.sui-upsell {\n\n\t\t&-list {\n\n\t\t\tli {\n\t\t\t\tcolor: $black;\n\t\t\t}\n\t\t}\n\t}\n}", "@import \"variables\";\n\n@import \"~@wpmudev/shared-ui/scss/mixins\";\n\n@include body-class {\n\n\t/**\n\t * Dashboard meta boxes\n\t *\n\t * @since 3.8.6\n\t */\n\t.wrap-smush {\n\t\t// Images.\n\t\tdiv[class^=\"sui-dashboard-\"] .sui-box-title:before {\n\t\t\tcolor: #333333;\n\t\t\tfloat: left;\n\t\t\tfont-size: 20px;\n\t\t\tmargin-right: 10px;\n\t\t\tline-height: 30px;\n\t\t}\n\n\t\t// Meta boxes.\n\t\t.sui-dashboard-bulk .sui-box-title,\n\t\t.sui-dashboard-upsell-upsell .sui-box-title { @include icon(before, 'smush'); }\n\t\t.sui-dashboard-directory .sui-box-title { @include icon(before, 'folder'); }\n\t\t.sui-dashboard-integrations .sui-box-title { @include icon(before, 'plugin-2'); }\n\t\t.sui-dashboard-lazy-load .sui-box-title { @include icon(before, 'update'); }\n\t\t.sui-dashboard-cdn .sui-box-title { @include icon(before, 'web-globe-world'); }\n\t\t.sui-dashboard-webp .sui-box-title { @include icon(before, 'photo-picture'); }\n\t\t.sui-dashboard-tools .sui-box-title { @include icon(before, 'wrench-tool'); }\n\n\t\t// Dashboard summary meta box.\n\t\t#smush-box-dashboard-summary {\n\t\t\t.sui-list-detail {\n\t\t\t\tdisplay: flex;\n\t\t\t\talign-items: center;\n\t\t\t\t& > span.sui-tooltip:first-of-type,\n\t\t\t\t& > span.sui-tooltip:nth-of-type(2) {\n\t\t\t\t\tline-height: 14px;\n\t\t\t\t\tmargin-right: 5px;\n\t\t\t\t}\n\t\t\t\ta > .sui-tag { cursor: pointer; }\n\t\t\t}\n\t\t\t.smush-upgrade-text {\n\t\t\t\tcolor: $purple;\n\t\t\t\tmargin-right: 10px;\n\t\t\t}\n\t\t}\n\n\t\t// Dashboard tutorials.\n\t\t#smush-dash-tutorials,\n\t\t#smush-dash-tutorials > .sui-notice { margin-bottom: 30px; }\n\n\t\t// Dashboard lazy load meta box.\n\t\t#smush-box-dashboard-lazy-load {\n\t\t\t.sui-settings-label {\n\t\t\t\tcolor: #333333;\n\t\t\t}\n\t\t\t.sui-box-settings-row.sui-flushed {\n\t\t\t\tborder-top: 1px solid #E6E6E6;\n\t\t\t\tpadding: 14px 30px;\n\t\t\t\tjustify-content: space-between;\n\t\t\t\talign-items: center;\n\t\t\t}\n\t\t}\n\n\t\t// Dashboard CDN meta box.\n\t\t#smush-box-dashboard-cdn {\n\t\t\t.sui-table-item-title {\n\t\t\t\tdisplay: flex;\n\t\t\t\talign-items: center;\n\n\t\t\t\t.smush-filename-extension { margin-right: 10px; }\n\t\t\t}\n\n\t\t\t.sui-table.sui-table-flushed tbody tr td:last-of-type { text-align: right; }\n\t\t}\n\n\t\t// Dashboard integrations meta box.\n\t\t#smush-box-dashboard-integrations {\n\t\t\t.smush-disabled-table-row {\n\t\t\t\topacity: 0.5;\n\t\t\t\tpointer-events: none;\n\t\t\t\tbackground-color: rgba(242,242,242,0.5);\n\t\t\t}\n\t\t}\n\t}\n\n\t.sui-wrap {\n\t\t.sui-box-upsell-row .sui-toggle input[disabled] ~ .sui-description {\n\t\t\tpointer-events: auto !important;\n\t\t}\n\t\t/**\n\t\t * Bulk smush meta boxes\n\t\t *\n\t\t * @since 3.10.0\n\t\t */\n\t\t.sui-dashboard-summary, .sui-summary-smush, .sui-summary-smush-nextgen {\n\t\t\tbackground-image: none !important;\n\t\t}\n\n\t\t.sui-settings-label > .sui-tag {\n\t\t\tvertical-align: middle;\n\t\t\tmargin-left: 3px;\n\t\t}\n\n\t\t.sui-summary:not(.sui-unbranded):not(.sui-summary-sm) .sui-summary-image-space {\n\t\t\tdisplay: flex;\n\t\t\tjustify-content: center;\n\t\t\tflex-direction: column;\n\t\t\talign-items: center;\n\n\t\t\tbackground-color: #F8F8F8;\n\t\t\tmargin: 20px 10px;\n\t\t\tmin-width: 266px;\n\t\t\tflex: 0 0 266px;\n\n\t\t\t@include media(min-width, lg) {\n\t\t\t\tmin-width: 388px;\n\t\t\t\tflex: 0 0 388px;\n\t\t\t}\n\n\t\t\t.sui-circle-score {\n\t\t\t\theight: 110px;\n\t\t\t\tmargin-bottom: 10px;\n\n\t\t\t\tcircle {\n\t\t\t\t\tstroke-width: 10;\n\t\t\t\t\t&:last-of-type {\n\t\t\t\t\t\tstroke-dasharray: var(--metric-array);\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t.sui-circle-score-label {\n\t\t\t\t\tposition: absolute;\n\t\t\t\t\tdisplay: flex;\n\t\t\t\t\talign-content: space-between;\n\t\t\t\t\tjustify-content: center;\n\t\t\t\t\talign-items: center;\n\t\t\t\t\twidth: 110px;\n\t\t\t\t\tmargin: 0;\n\t\t\t\t\tfont-weight: 700;\n\t\t\t\t\tfont-size: 30px;\n\t\t\t\t\tletter-spacing: -1px;\n\t\t\t\t\tcolor: #333;\n\n\t\t\t\t\t&:after {\n\t\t\t\t\t\tcontent: '%';\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t& > small {\n\t\t\t\tcolor: #333 !important;\n\t\t\t}\n\t\t}\n\n\t\t@media screen and (min-width: 1140px) {\n\t\t\t.sui-upgrade-page-header__image {\n\t\t\t\twidth: 45% !important;\n\t\t\t}\n\t\t}\n\t\t.sui-upgrade-page .sui-upgrade-page-header__image{\n\t\t\tbackground-size: contain;\n\t\t}\n\n\t\t.sui-upgrade-page {\n\t\t\t.sui-upgrade-page-cta__inner {\n\t\t\t\ta.sui-button-purple {\n\t\t\t\t\tdisplay: inline-block;\n\t\t\t\t\tmargin-top: 0;\n\t\t\t\t\tmargin-bottom: 5px;\n\t\t\t\t\t&:hover {\n\t\t\t\t\t\tcolor: #fff !important;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t.smush-stats {\n\t\t\t\tposition: relative;\n\t\t\t\tdisplay: flex;\n\t\t\t\tjustify-content: space-between;\n\t\t\t\twidth: 600px;\n\t\t\t\theight: 100px;\n\t\t\t\tborder-radius: 4px;\n\t\t\t\tbackground-color: #FFFFFF;\n\t\t\t\tbox-shadow: 0 0 20px 0 rgba(0,0,0,0.05);\n\t\t\t\tmargin: -100px auto 30px;\n\t\t\t\tpadding: 20px 40px;\n\t\t\t\ttext-align: center;\n\t\t\t\tfont-size: 15px;\n\t\t\t\tfont-weight: 500;\n\t\t\t\tcolor: #333333;\n\n\t\t\t\t.smush-stats-description {\n\t\t\t\t\tcolor: #888888;\n\t\t\t\t\tfont-size: 12px;\n\t\t\t\t\ttext-transform: uppercase;\n\t\t\t\t}\n\n\t\t\t\tspan {\n\t\t\t\t\tfont-size: 24px;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t@media (max-width: 800px) {\n\t\t\t\t.smush-stats {\n\t\t\t\t\tmargin-top: 0;\n\t\t\t\t\theight: auto;\n\t\t\t\t\twidth: 75%;\n\t\t\t\t\tflex-wrap: wrap;\n\t\t\t\t}\n\n\t\t\t\t.smush-stats-item {\n\t\t\t\t\tflex-basis: 100%;\n\t\t\t\t}\n\n\t\t\t\t.smush-stats-item:nth-child(2) {\n\t\t\t\t\tmargin: 20px 0;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t/**\n\t\t * Integrations\n\t\t *\n\t\t * @since 3.4.0\n\t\t */\n\t\t.sui-integrations {\n\t\t\t.sui-settings-label {\n\t\t\t\tdisplay: flex;\n\t\t\t\talign-items: center;\n\n\t\t\t\t.sui-tag { margin-left: 10px; }\n\t\t\t}\n\n\t\t\t.sui-toggle-content .sui-notice {\n\t\t\t\tmargin-top: 10px;\n\t\t\t}\n\n\t\t\t.sui-box-settings-row.sui-disabled {\n\t\t\t\tmargin-bottom: 0;\n\t\t\t}\n\t\t}\n\n\t\t/**\n\t\t * Lazy loading\n\t\t *\n\t\t * @since 3.2.2\n\t\t */\n\t\t.sui-lazyload {\n\t\t\t#smush-lazy-load-spinners span.sui-description:first-of-type,\n\t\t\t#smush-lazy-load-placeholder span.sui-description:first-of-type {\n\t\t\t\tmargin-bottom: 20px;\n\t\t\t}\n\n\t\t\t#smush-lazy-load-spinners .sui-box-selectors,\n\t\t\t#smush-lazy-load-placeholder .sui-box-selectors {\n\t\t\t\tbackground-color: #FFF;\n\t\t\t\tpadding: 0 0 20px 0;\n\n\t\t\t\t&:not([class*=\"sui-box-selectors-col-\"]) ul li {\n\t\t\t\t\tflex: 0;\n\t\t\t\t}\n\n\t\t\t\t.sui-box-selector {\n\t\t\t\t\tborder: 1px solid #DDD;\n\t\t\t\t}\n\n\t\t\t\t.remove-selector {\n\t\t\t\t\tdisplay: none;\n\t\t\t\t\ttop: 0;\n\t\t\t\t\tright: 0;\n\t\t\t\t\tposition: absolute;\n\t\t\t\t\tbackground-color: #AAA;\n\t\t\t\t\twidth: 18px;\n\t\t\t\t\theight: 18px;\n\t\t\t\t\tmargin-right: 0;\n\t\t\t\t\tborder-radius: 0 3px 0 3px;\n\n\t\t\t\t\ti {\n\t\t\t\t\t\tmargin: 0;\n\t\t\t\t\t}\n\n\t\t\t\t\ti:before {\n\t\t\t\t\t\tcolor: #FFF;\n\t\t\t\t\t\tfont-size: 12px;\n\t\t\t\t\t\tline-height: 18px;\n\t\t\t\t\t}\n\n\t\t\t\t\t&:hover {\n\t\t\t\t\t\tbackground-color: #FF6D6D;\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t.sui-box-selector:hover .remove-selector {\n\t\t\t\t\tdisplay: flex;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t#smush-lazy-load-spinners .sui-box-selectors {\n\t\t\t\tlabel.sui-box-selector {\n\t\t\t\t\twidth: 50px;\n\t\t\t\t\theight: 50px;\n\t\t\t\t\toverflow: visible;\n\t\t\t\t}\n\n\t\t\t\t.sui-box-selector input + span {\n\t\t\t\t\tpadding-top: 17px;\n\t\t\t\t\tpadding-bottom: 17px;\n\t\t\t\t\tposition: relative;\n\n\t\t\t\t\timg {\n\t\t\t\t\t\tmax-width: 16px;\n\t\t\t\t\t\tmargin: 0;\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t.sui-box-selector input ~ span {\n\t\t\t\t\tpadding-right: 17px;\n\t\t\t\t\tpadding-left: 17px;\n\t\t\t\t\theight: 48px;\n\t\t\t\t\tborder-radius: 4px;\n\t\t\t\t\toverflow: visible;\n\t\t\t\t}\n\n\t\t\t\t.sui-box-selector input:checked + span:before,\n\t\t\t\t.sui-box-selector input:checked + span:after {\n\t\t\t\t\tcontent: none;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t#smush-lazy-load-placeholder .sui-box-selectors {\n\t\t\t\tlabel.sui-box-selector {\n\t\t\t\t\twidth: 80px;\n\t\t\t\t\theight: 60px;\n\t\t\t\t\toverflow: visible;\n\n\t\t\t\t\t&:hover {\n\t\t\t\t\t\tbackground-color: #E5E5E5;\n\t\t\t\t\t}\n\n\t\t\t\t\tinput + span {\n\t\t\t\t\t\theight: 40px;\n\t\t\t\t\t\tpadding: 0;\n\t\t\t\t\t\tmargin: 10px;\n\t\t\t\t\t\toverflow: visible;\n\t\t\t\t\t\tposition: relative;\n\n\t\t\t\t\t\t&:before {\n\t\t\t\t\t\t\ttop: -5px;\n\t\t\t\t\t\t\tright: -5px;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t&:after {\n\t\t\t\t\t\t\ttop: -10px;\n\t\t\t\t\t\t\tright: -10px;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\n\t\t\t\t\tinput + span img {\n\t\t\t\t\t\tmargin: 0 auto;\n\t\t\t\t\t}\n\n\t\t\t\t\tinput[id^=\"placeholder-icon-1\"] + span {\n\t\t\t\t\t\tbackground-color: #FAFAFA;\n\t\t\t\t\t}\n\n\t\t\t\t\tinput[id^=\"placeholder-icon-2\"] + span {\n\t\t\t\t\t\tbackground-color: #333333;\n\t\t\t\t\t}\n\n\t\t\t\t\t.remove-selector {\n\t\t\t\t\t\ttop: -10px;\n\t\t\t\t\t\tright: -10px;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t/**\n\t\t * Onboarding modals\n\t\t *\n\t\t * @since 3.1\n\t\t */\n\t\t.smush-onboarding-dialog {\n\t\t\t@keyframes fadeInLeft {\n\t\t\t\tfrom {\n\t\t\t\t\topacity: 0;\n\t\t\t\t\ttransform: translate3d(-50px, 0, 0);\n\t\t\t\t}\n\t\t\t\tto {\n\t\t\t\t\topacity: 1;\n\t\t\t\t\ttransform: none;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t@keyframes fadeInRight {\n\t\t\t\tfrom {\n\t\t\t\t\topacity: 0;\n\t\t\t\t\ttransform: translate3d(50px, 0, 0);\n\t\t\t\t}\n\t\t\t\tto {\n\t\t\t\t\topacity: 1;\n\t\t\t\t\ttransform: none;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t.sui-box { background-color: transparent; }\n\n\t\t\t#smush-onboarding-content {\n\t\t\t\topacity: 0;\n\t\t\t\tbackground-color: #fff;\n\t\t\t\tanimation-duration: 0.7s;\n\t\t\t\tanimation-fill-mode: both;\n\t\t\t\ttransform-origin: center;\n\t\t\t\ttransform-style: preserve-3d;\n\n\t\t\t\t&.loaded, &.fadeInLeft, &.fadeInRight { opacity: 1; }\n\t\t\t\t&.fadeInLeft { animation-name: fadeInLeft; }\n\t\t\t\t&.fadeInRight { animation-name: fadeInRight; }\n\n\t\t\t\t.sui-box-selectors, .smush-onboarding-sharing-data {\n\t\t\t\t\tdisplay: flex;\n\t\t\t\t\tjustify-content: center;\n\t\t\t\t\talign-items: center;\n\n\t\t\t\t\tlabel:last-of-type {\n\t\t\t\t\t\ttop: 0;\n\t\t\t\t\t\tfont-size: 13px;\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t.smush-onboarding-tracking-box {\n\t\t\t\t\tbackground: #f8f8f8;\n\t\t\t\t\tmargin: 2px 0 30px;\n\t\t\t\t\tpadding: 20px 30px;\n\t\t\t\t\tline-height: 22px;\n\t\t\t\t\tletter-spacing: -0.25px;\n\t\t\t\t\ttext-align: center;\n\t\t\t\t\tinput{\n\t\t\t\t\t\tborder:1px solid #ddd;\n\t\t\t\t\t\tbackground-color: #FAFAFA;\n\t\t\t\t\t\tborder-radius: 4px;\n\t\t\t\t\t\tbox-shadow: none;\n\t\t\t\t\t\tmargin: 0 16px 0 0;\n\t\t\t\t\t\twidth: 16px;\n\t\t\t\t\t\theight:16px;\n\t\t\t\t\t\tdisplay: inline-block;\n\t\t\t\t\t\tvertical-align: middle;\n\t\t\t\t\t\t+ span{\n\t\t\t\t\t\t\tdisplay: inline-block;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\tlabel{\n\t\t\t\t\t\tdisplay: block;\n\t\t\t\t\t\tmargin:0 auto;\n\t\t\t\t\t\tmax-width: 325px;\n\t\t\t\t\t\twidth: 100%;\n\t\t\t\t\t\tspan {\n\t\t\t\t\t\t\tletter-spacing:0;\n\t\t\t\t\t\t\tfont-size: 12px!important;\n\t\t\t\t\t\t\tfont-weight: 400;;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t.smush-onboarding-arrows a {\n\t\t\t\tposition: absolute;\n\t\t\t\ttop: 45%;\n\t\t\t\twidth: 40px;\n\t\t\t\theight: 40px;\n\t\t\t\tborder-radius: 20px;\n\t\t\t\tpadding-top: 3px;\n\n\t\t\t\t&:not(.sui-hidden) {\n\t\t\t\t\tdisplay: flex;\n\t\t\t\t\tjustify-content: center;\n\t\t\t\t\talign-items: center;\n\t\t\t\t}\n\n\t\t\t\t&:hover { background-color: rgba(217, 217, 217, 0.2); }\n\n\t\t\t\t&:first-of-type {\n\t\t\t\t\tleft: -55px;\n\t\t\t\t\tpadding-right: 2px;\n\t\t\t\t}\n\n\t\t\t\t&:last-of-type {\n\t\t\t\t\tright: -55px;\n\t\t\t\t\tpadding-left: 2px;\n\t\t\t\t}\n\n\t\t\t\ti:before { color: #fff; }\n\t\t\t}\n\n\t\t\t@media screen and (max-width: 782px) {\n\t\t\t\t.smush-onboarding-arrows { display: none; }\n\t\t\t}\n\t\t} // End .smush-onboarding-dialog\n\n\t\t// Bulk smush and directory smush (overwrite when the limit is exceeded)\n\t\t.wp-smush-exceed-limit {\n\t\t\t.wp-smush-progress-inner {\n\t\t\t\tbackground: #FECF2F !important;\n\t\t\t}\n\n\t\t\t.sui-icon-info:before {\n\t\t\t\tcolor: #FECF2F;\n\t\t\t}\n\t\t}\n\t\t// Directory smush.\n\t\t.wp-smush-progress-dialog {\n\n\t\t\t// When limit is exceeded.\n\t\t\t&.wp-smush-exceed-limit {\n\n\t\t\t\t#smush-limit-reached-notice {\n\t\t\t\t\tdisplay: block;\n\t\t\t\t}\n\n\t\t\t\t.wp-smush-resume-scan {\n\t\t\t\t\tdisplay: inline;\n\t\t\t\t}\n\n\t\t\t\t.sui-progress {\n\t\t\t\t\t.sui-progress-icon {\n\t\t\t\t\t\t.sui-icon-loader {\n\t\t\t\t\t\t\t@include icon( before, info );\n\t\t\t\t\t\t\t&:before {\n\t\t\t\t\t\t\t\tcolor: #FECF2F;\n\t\t\t\t\t\t\t\tanimation: none;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t.sui-progress-bar span {\n\t\t\t\t\tbackground: #888888;\n\t\t\t\t}\n\n\t\t\t\t.sui-box-footer .sui-actions-right:not(.sui-hidden) {\n\t\t\t\t\tmargin-left: 10px;\n\t\t\t\t\tmargin-right: auto;\n\t\t\t\t\tfloat: left;\n\n\t\t\t\t\t.sui-button {\n\t\t\t\t\t\tpadding: 5px 16px 7px;\n\t\t\t\t\t\tborder: 2px solid #ddd;\n\t\t\t\t\t\tbackground: transparent;\n\t\t\t\t\t\tcolor: #888;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\t.sui-box-footer {\n\t\t\t\t\tjustify-content: space-between;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t// When the request fails.\n\t\t\t&.wp-smush-scan-error {\n\n\t\t\t\t#smush-scan-error-notice {\n\t\t\t\t\tdisplay: block;\n\t\t\t\t}\n\n\t\t\t\t.sui-progress-block, .sui-progress-state {\n\t\t\t\t\tdisplay: none;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t#smush-limit-reached-notice, .wp-smush-resume-scan, #smush-scan-error-notice {\n\t\t\t\tdisplay: none;\n\t\t\t}\n\t\t}\n\n\t\t.wp-smush-bulk-progress-bar-wrapper {\n\t\t\tmargin-bottom: 30px;\n\t\t\t.sui-progress-state span { display: inline-block; }\n\t\t\t.sui-progress-bar {\n\t\t\t\tborder-radius: 5px;\n\t\t\t}\n\t\t}\n\n\t\t#sui-cross-sell-footer h3 {\n\t\t\ttext-align: center;\n\t\t}\n\n\t\t// Bulk Smush error messages: Start.\n\t\t.smush-final-log {\n\t\t\tmargin-top: 30px;\n\n\t\t\t.smush-bulk-errors {\n\t\t\t\t&.overflow-box{\n\t\t\t\t\toverflow-y: scroll;\n\t\t\t\t\theight: 260px;\n\t\t\t\t}\n\t\t\t\tmax-height: 260px;\n\t\t\t\toverflow-y: auto;\n\t\t\t\toverflow-x:hidden;\n\t\t\t\tpadding-left: 30px;\n\t\t\t\tpadding-right: 30px;\n\t\t\t\tmargin: 0 -30px;\n\n\t\t\t\t.smush-bulk-error-row {\n\t\t\t\t\talign-content: center;\n\t\t\t\t\talign-items: center;\n\t\t\t\t\tborder-bottom: 1px solid #E6E6E6;\n\t\t\t\t\tbox-shadow: inset 2px 0 0 0 #FECF2F;\n\t\t\t\t\tdisplay: flex;\n\t\t\t\t\theight: 52px;\n\t\t\t\t\tjustify-content: space-between;\n\t\t\t\t\tmargin: 0 -30px;\n\t\t\t\t\tpadding: 0 30px 0 15px;\n\t\t\t\t\tfont-size: 13px;\n\t\t\t\t\tletter-spacing: -0.25px;\n\n\t\t\t\t\t&:first-child {\n\t\t\t\t\t\tborder-top: 1px solid #E6E6E6;\n\t\t\t\t\t}\n\n\t\t\t\t\t.smush-bulk-image-data {\n\t\t\t\t\t\tdisplay: flex;\n\t\t\t\t\t\talign-content: center;\n\t\t\t\t\t\talign-items: center;\n\t\t\t\t\t\t// flex-basis: 100%;\n\t\t\t\t\t\t.sui-icon-photo-picture {\n\t\t\t\t\t\t\tfont-size: 12px;\n\t\t\t\t\t\t\tborder-radius: 4px;\n\t\t\t\t\t\t\tbackground-color: #E6E6E6;\n\t\t\t\t\t\t\tmargin-right: 10px;\n\t\t\t\t\t\t\tpadding-left: 9px;\n\t\t\t\t\t\t\tpadding-top: 1px;\n\t\t\t\t\t\t\twidth: 30px;\n\t\t\t\t\t\t\theight: 30px;\n\n\t\t\t\t\t\t\t&:before {\n\t\t\t\t\t\t\t\twidth: 30px;\n\t\t\t\t\t\t\t\theight: 30px;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t.attachment-thumbnail {\n\t\t\t\t\t\t\tborder-radius: 4px;\n\t\t\t\t\t\t\twidth: 30px;\n\t\t\t\t\t\t\theight: 30px;\n\t\t\t\t\t\t\tmargin-right: 10px;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t.smush-image-name {\n\t\t\t\t\t\t\tline-height: 20px;\n\t\t\t\t\t\t\tfont-weight: 500;\n\t\t\t\t\t\t\tmargin-right: 10px;\n\t\t\t\t\t\t\tcolor:#333;\n\t\t\t\t\t\t\tword-break: break-all;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\t.smush-bulk-image-title{\n\t\t\t\t\t\tdisplay:flex;\n\t\t\t\t\t\talign-items: center;\n\t\t\t\t\t\tjustify-content: center;\n\t\t\t\t\t\t&:before {\n\t\t\t\t\t\t\tcontent: \"I\";\n\t\t\t\t\t\t\tfont-family: wpmudev-plugin-icons !important;\n\t\t\t\t\t\t\tspeak: none;\n\t\t\t\t\t\t\tfont-size: 16px;\n\t\t\t\t\t\t\tfont-style: normal;\n\t\t\t\t\t\t\tfont-weight: 400;\n\t\t\t\t\t\t\tfont-variant: normal;\n\t\t\t\t\t\t\ttext-transform: none;\n\t\t\t\t\t\t\tline-height: 1;\n\t\t\t\t\t\t\ttext-rendering: auto;\n\t\t\t\t\t\t\tdisplay: inline-block;\n\t\t\t\t\t\t\tmargin-right: 10px;\n\t\t\t\t\t\t\tcolor: #FECF2F;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\t.smush-image-error {\n\t\t\t\t\t\tline-height: 23px;\n\t\t\t\t\t\tword-break: break-all;\n\t\t\t\t\t\tmargin-right: 10px;\n\t\t\t\t\t\tcolor:#888;\n\t\t\t\t\t}\n\n\t\t\t\t\t.smush-bulk-image-actions {\n\t\t\t\t\t\t// flex-basis: auto;\n\t\t\t\t\t\twhite-space: nowrap;\n\n\t\t\t\t\t\tbutton:disabled {\n\t\t\t\t\t\t\tbackground-color: #F2F2F2;\n\t\t\t\t\t\t\tcursor: default;\n\n\t\t\t\t\t\t\t.sui-icon-eye-hide:before {\n\t\t\t\t\t\t\t\tcolor: lighten( #333333, 50% );\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t\ta{\n\t\t\t\t\t\t\t&.smush-link-detail {\n\t\t\t\t\t\t\t\tpadding-left: 15px;\n\t\t\t\t\t\t\t\tmargin-left: 15px;\n\t\t\t\t\t\t\t\tposition: relative;\n\t\t\t\t\t\t\t\t&:before{\n\t\t\t\t\t\t\t\t\tcontent: \"\";\n\t\t\t\t\t\t\t\t\tposition: absolute;\n\t\t\t\t\t\t\t\t\tborder-left:1px solid #ddd;\n\t\t\t\t\t\t\t\t\tleft:0;\n\t\t\t\t\t\t\t\t\ttop:0;\n\t\t\t\t\t\t\t\t\tbottom:0;\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t&.disabled {\n\t\t\t\t\t\t\t\tcolor: currentColor;\n\t\t\t\t\t\t\t\tdisplay: inline-block;\n\t\t\t\t\t\t\t\tpointer-events: none;\n\t\t\t\t\t\t\t\ttext-decoration: none;\n\t\t\t\t\t\t\t\tcursor: not-allowed;\n\t\t\t\t\t\t\t\topacity: 0.5;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\n\t\t\t\t\t&.smush-error-upsell {\n\t\t\t\t\t\tbox-shadow: inset 2px 0 0 0 #8D00B1;\n\t\t\t\t\t\t.smush-bulk-image-title {\n\t\t\t\t\t\t\t&:before{\n\t\t\t\t\t\t\t\tcolor:#8D00B1;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tpadding:10px 0;\n\t\t\t\t\t\t\t.smush-image-error {\n\t\t\t\t\t\t\t\tmargin-top:0;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t\ta {\n\t\t\t\t\t\t\tcolor:#8D00B1;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\n\t\t\t\t\t@media screen and (max-width: 782px) {\n\t\t\t\t\t\tmargin:0 -20px;\n\t\t\t\t\t}\n\n\t\t\t\t\t@media screen and (max-width: 767px) {\n\t\t\t\t\t\theight: auto;\n\t\t\t\t\t\talign-items: flex-start;\n\n\t\t\t\t\t\t.smush-bulk-image-data {\n\t\t\t\t\t\t\tpadding: 10px 0;\n\t\t\t\t\t\t\tflex-flow: column;\n\t\t\t\t\t\t\talign-items: flex-start;\n\t\t\t\t\t\t\t.smush-image-error {\n\t\t\t\t\t\t\t\tmargin-left: 25px;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t.smush-image-error {\n\t\t\t\t\t\t\tmargin-top: 10px;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t.smush-bulk-image-actions{\n\t\t\t\t\t\t\tmargin-top: 10px;\n\t\t\t\t\t\t\tmax-height: 20px;\n\t\t\t\t\t\t\ta {\n\t\t\t\t\t\t\t\tline-height: 24px;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t.smush-link-detail:before {\n\t\t\t\t\t\t\t\ttop:9px;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t\t&.smush-error-upsell .smush-bulk-image-title {\n\t\t\t\t\t\t\talign-items: flex-start;\n\t\t\t\t\t\t\t&:before{\n\t\t\t\t\t\t\t\tmargin-top: 5px;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\t@media screen and (max-width: 480px) {\n\t\t\t\t\t\tflex-flow: column;\n\t\t\t\t\t\talign-items: flex-start;\n\t\t\t\t\t\talign-content: flex-start;\n\t\t\t\t\t\tpadding-bottom: 14px;\n\t\t\t\t\t\t// .smush-image-error,.smush-bulk-image-title,.smush-bulk-image-actions a {\n\t\t\t\t\t\t// \tfont-size: 12px;\n\t\t\t\t\t\t// }\n\t\t\t\t\t\t.smush-bulk-image-actions{\n\t\t\t\t\t\t\tpadding-left: 25px;\n\t\t\t\t\t\t\tmargin-top: 0;\n\t\t\t\t\t\t}\n\t\t\t\t\t\t.smush-bulk-image-data {\n\t\t\t\t\t\t\tmargin-bottom: 5px;\n\t\t\t\t\t\t\tpadding-bottom: 0;\n\t\t\t\t\t\t\t.smush-image-error {\n\t\t\t\t\t\t\t\tmargin-top: 10px;\n\t\t\t\t\t\t\t\tmargin-bottom: 0;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t&.smush-error-upsell .smush-bulk-image-title {\n\t\t\t\t\t\t\tpadding-bottom: 0;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t.smush-bulk-errors-actions {\n\t\t\t\tmargin: 30px 0 10px;\n\t\t\t\t@media screen and (max-width:320px) {\n\t\t\t\t\ta{\n\t\t\t\t\t\tpadding-left:10px!important;\n\t\t\t\t\t\tpadding-right: 10px!important;\n\t\t\t\t\t\tfont-size: 11.5px;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\n\t\t} // Bulk Smush error messages: End.\n\n\t\t// Settings - keep data.\n\t\t.smush-keep-data-form-row {\n\t\t\t.sui-tabs-menu,\n\t\t\t.sui-button-ghost {\n\t\t\t\tmargin-top: 15px;\n\t\t\t}\n\t\t}\n\n\t\t.sui-summary-smush {\n\t\t\tbackground-size: 180px;\n\t\t}\n\t\t.sui-summary-smush-nextgen {\n\t\t\tbackground-size: 170px;\n\t\t}\n\n\t\t.bulk-smush-wrapper {\n\t\t\t.wp-smush-upsell-on-completed {\n\t\t\t\t&.sui-hidden{\n\t\t\t\t\tdisplay: none!important;\n\t\t\t\t}\n\t\t\t\tbackground: #F8F8F8;\n\t\t\t\tpadding:20px 15px;\n\t\t\t\tborder-radius: 4px;\n\t\t\t\t.smush-box-image {\n\t\t\t\t\t-webkit-box-flex: 0;\n\t\t\t\t\t-ms-flex: 0 0 auto;\n\t\t\t\t\tflex: 0 0 auto;\n\t\t\t\t\tmargin-right: 15px;\n\t\t\t\t\timg {\n\t\t\t\t\t\tmax-width: 44px;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\t.sui-box-content {\n\t\t\t\t\tp {\n\t\t\t\t\t\tfont-size:13px;\n\t\t\t\t\t\tcolor:#333;\n\t\t\t\t\t\tmargin-bottom:0;\n\t\t\t\t\t\tline-height: 22px;\n\t\t\t\t\t}\n\t\t\t\t\ta {\n\t\t\t\t\t\tfont-size:13px;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\t@media screen and (min-width: 361px) {\n\t\t\t\t\tdisplay:flex;\n\t\t\t\t}\n\t\t\t}\n\t\t\t.wp-smush-upsell-cdn {\n\t\t\t\t&.sui-hidden{\n\t\t\t\t\tdisplay: none!important;\n\t\t\t\t}\n\t\t\t\tpadding:20px 30px;\n\t\t\t\tbackground: #F8F8F8;\n\t\t\t\tborder-radius: 4px;\n\t\t\t\t.sui-image-icon{\n\t\t\t\t\twidth:40px;\n\t\t\t\t\tmargin-right:15px;\n\t\t\t\t\tdisplay: inline-block;\n\t\t\t\t}\n\t\t\t\t.sui-box-content {\n\t\t\t\t\tp {\n\t\t\t\t\t\tfont-size:13px;\n\t\t\t\t\t\tcolor:#333;\n\t\t\t\t\t\tmargin-bottom:5px;\n\t\t\t\t\t\tline-height: 22px;\n\t\t\t\t\t}\n\t\t\t\t\ta {\n\t\t\t\t\t\tfont-size:13px;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\t@media screen and ( max-width: 600px ) {\n\t\t\t\t\tpadding: 20px;\n\t\t\t\t}\n\t\t\t\t@media screen and (min-width: 361px) {\n\t\t\t\t\tdisplay:flex;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t.bulk-smush-unlimited {\n\t\t\tposition:relative;\n\t\t\t.sui-box-title {\n\t\t\t\tfont-size: 16px;\n\t\t\t\tline-height: 22px;\n\t\t\t\tmargin-bottom:14px;\n\t\t\t\t@media screen and ( max-width: 480px ) {\n\t\t\t\t\tmargin-top: 5px;\n\t\t\t\t}\n\t\t\t\tbackground: #F8F8F8;\n\t\t\t\tpadding:20px 15px;\n\t\t\t\tborder-radius: 4px;\n\t\t\t\t.smush-box-image {\n\t\t\t\t\t-webkit-box-flex: 0;\n\t\t\t\t\t-ms-flex: 0 0 auto;\n\t\t\t\t\tflex: 0 0 auto;\n\t\t\t\t\tmargin-right: 15px;\n\t\t\t\t\timg {\n\t\t\t\t\t\tmax-width: 44px;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\t.sui-box-content {\n\t\t\t\t\tp {\n\t\t\t\t\t\tfont-size:13px;\n\t\t\t\t\t\tcolor:#333;\n\t\t\t\t\t\tmargin-bottom:0;\n\t\t\t\t\t\tline-height: 22px;\n\t\t\t\t\t}\n\t\t\t\t\ta {\n\t\t\t\t\t\tfont-size:13px;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\t@media screen and (min-width: 361px) {\n\t\t\t\t\tdisplay:flex;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t/**\n\t\t * Smush video upsell on dedicated upgrade page and dashboard widget.\n\t\t * @since 3.6.0\n\t\t */\n\t\t.sui-upgrade-page .thumbnail-container {\n\t\t\timg {\n\t\t\t\tcursor: pointer;\n\t\t\t\twidth: 640px;\n\t\t\t\t@media screen and (max-width: 600px) {\n\t\t\t\t\twidth: 100%;\n\t\t\t\t}\n\t\t\t\ttransition: opacity 0.5s;\n\t\t\t\t&:hover\n\t\t\t\t{\n\t\t\t\t\topacity: 0.68;\n\t\t\t\t\ttransition: opacity 0.5s;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t.sui-progress-close {\n\t\t\tborder: 0;\n\t\t\tbackground: 0;\n\t\t\ttext-transform: uppercase;\n\t\t\tcolor: #888888;\n\t\t\tfont-size: 12px;\n\t\t\tfont-weight: 500;\n\t\t\tletter-spacing: -0.25px;\n\t\t\tmargin-left: 10px;\n\t\t\tcursor: pointer;\n\t\t}\n\n\t\t.sui-progress-close.wp-smush-cancel-bulk.sui-hidden,\n\t\t.sui-progress-close.wp-smush-all.sui-hidden {\n\t\t\tdisplay: none !important;\n\t\t}\n\n\t\t.sui-notice {\n\t\t\t&.smush-highlighting-notice,\n\t\t\t&.smush-highlighting-warning {\n\t\t\t\tmargin-top: 10px;\n\t\t\t\tmargin-bottom: 10px;\n\t\t\t}\n\t\t}\n\n\t\t.sui-hidden, button.sui-hidden {\n\t\t\tdisplay: none;\n\t\t}\n\n\t\t.sui-loading {\n\t\t\tfont-size: 18px;\n\t\t}\n\n\t\t.sui-button {\n\n\t\t\t&.smush-button-check-success,\n\t\t\t&.smush-button-check-success:hover {\n\t\t\t\tbackground: #d1f1ea !important;\n\t\t\t\tcolor: #1abc9c !important;\n\t\t\t\tpointer-events: none;\n\n\t\t\t\t&:before {\n\t\t\t\t\tcolor: $notice-success-icon-color;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t.sui-toggle + label {\n\t\t\tfont-weight: 500;\n\t\t}\n\t}\n\n\t.smush-align-left {\n\t\tfloat: left;\n\t}\n\n\t.smush-align-right {\n\t\tfloat: right;\n\t}\n\n\t.sui-summary.sui-summary-smush,\n\t.sui-summary.sui-dashboard-summary {\n\t\t.sui-summary-detail.wp-smush-savings .wp-smush-stats-human,\n\t\t.sui-summary-large.wp-smush-stats-human,\n\t\t.sui-summary-details .sui-summary-detail {\n\t\t\tfont-size: 22px;\n\t\t\tline-height: 22px;\n\t\t\tfont-weight: 700;\n\t\t}\n\n\t\t.sui-summary-detail.wp-smush-savings,\n\t\t.sui-summary-detail.wp-smush-savings .wp-smush-stats-percent {\n\t\t\tfont-size: 15px;\n\t\t\tfont-weight: 400;\n\t\t}\n\n\t\t.smushed-items-count {\n\t\t\t.wp-smush-count-resize-total {\n\t\t\t\tmargin-top: 16px;\n\t\t\t\tdisplay: block;\n\t\t\t\t&.sui-hidden{\n\t\t\t\t\tdisplay:none;\n\t\t\t\t}\n\t\t\t}\n\t\t\t.wp-smush-count-total {\n\t\t\t\tdisplay: block;\n\t\t\t}\n\t\t}\n\n\t\t.sui-summary-segment {\n\t\t\toverflow: visible;\n\t\t}\n\n\t\t.sui-summary-details {\n\t\t\t.sui-summary-sub {\n\t\t\t\tmargin-bottom: 16px;\n\t\t\t}\n\n\t\t\t.sui-summary-detail {\n\t\t\t\tdisplay: inline-flex;\n\n\t\t\t\t+ .sui-summary-sub { margin-top: 0; }\n\t\t\t\t.wp-smush-stats-percent { margin-left: 5px; }\n\t\t\t\t.wp-smush-stats-human { margin-right: 5px; }\n\t\t\t}\n\n\t\t\t.sui-tooltip {\n\t\t\t\tposition: absolute;\n\t\t\t\tmargin-top: 25px;\n\t\t\t\tmargin-left: -5px;\n\n\t\t\t\t&:before {\n\t\t\t\t\tmargin-bottom: 20px;\n\t\t\t\t\tmargin-left: 5px;\n\t\t\t\t}\n\t\t\t\t&:after {\n\t\t\t\t\tmargin-bottom: 30px;\n\t\t\t\t\tmargin-left: 5px;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t.wp-smush-stats-label-message {\n\t\t\tfont-size: 13px;\n\t\t\tline-height: 22px;\n\t\t}\n\n\t\t.smush-stats-list {\n\t\t\t.sui-tag-pro {\n\t\t\t\ttop: -1px;\n\t\t\t\tmargin-left: 5px;\n\t\t\t}\n\t\t\tli {\n\t\t\t\tborder-bottom: none!important;\n\t\t\t\tborder-top: 1px solid #E6E6E6;\n\t\t\t\t&:first-child {\n\t\t\t\t\tborder-top: none;\n\t\t\t\t}\n\t\t\t\t&.sui-hidden {\n\t\t\t\t\tdisplay: none !important\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t.smush-summary-row-compression-type {\n\t\t\t.sui-list-detail {\n\t\t\t\tdisplay: flex;\n\t\t\t\tflex-direction: column;\n\t\t\t\talign-items: flex-end!important;\n\t\t\t\ta {\n\t\t\t\t\tfont-size: 11px;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\n\t.sui-box-footer .sui-icon-loader {\n\t\tline-height: 18px;\n\t\tvertical-align: middle;\n\t}\n\n\t.sui-modal-content {\n\t\t&.wp-smush-modal-dark-background{\n\t\t\t> .sui-box {\n\t\t\t\tbackground: transparent!important;\n\t\t\t\tborder-radius: 8px;\n\t\t\t\toverflow: hidden;\n\t\t\t\t.sui-box-header figure {\n\t\t\t\t\tmargin-top: -14px!important;\n\t\t\t\t\tmargin-bottom: 0;\n\t\t\t\t}\n\t\t\t\t.sui-box-body, .sui-box-footer {\n\t\t\t\t\tbackground-color:#fff;\n\t\t\t\t}\n\n\t\t\t}\n\t\t}\n\t}\n\n\t.wp-smush-ultra-compression-link {\n\t\tbackground-color: #F1E7FF!important;\n\t\tcolor: $purple!important;\n\t\tspan:before {\n\t\t\tcolor: $purple;\n\t\t}\n\n\t\t&:hover {\n\t\t\tcolor: #64007e!important;\n\t\t\tspan:before {\n\t\t\t\tcolor: #64007e;\n\t\t\t}\n\t\t}\n\t}\n\n\t.wp-smush-scan-progress-bar-wrapper {\n\t\t--progress-bar-height: 12px;\n\t\t--progress-transition-duration: 1.2s;\n\t\tposition: absolute;\n\t\ttop: 0;\n\t\tleft: 0;\n\t\tright: 0;\n\t\tbottom: 0;\n\t\tbackground-color: #fff;\n\t\ttext-align: center;\n\t\tdisplay: flex;\n\t\tjustify-content: center;\n\t\talign-items: center;\n\t\tflex-direction: column;\n\t\tcolor:$text-color;\n\t\tborder-radius: $border-radius;\n\t\toverflow: hidden;\n\t\tz-index:10;\n\t\t.wp-smush-scan-progress-bar-inner {\n\t\t\tdisplay: flex;\n\t\t\talign-items: center;\n\t\t\ttext-align: left;\n\t\t\tjustify-content: space-between;\n\t\t\twidth: 100%;\n\t\t\tpadding: 5px 30px;\n\n\t\t\t.sui-button{\n\t\t\t\tbackground-color: transparent;\n\t\t\t\tcolor: $main-color;\n\t\t\t\t&:hover,&:focus{\n\t\t\t\t\tbackground-color: $main-color;\n\t\t\t\t\tcolor:#fff;\n\t\t\t\t\tbox-shadow: none;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t\t.wp-smush-progress-status{\n\t\t\tline-height: 30px;\n\t\t\tfont-weight: 700;\n\t\t\t// max-height: 100px;\n\t\t\t.wp-smush-progress-percent {\n\t\t\t\tfont-size: 28px;\n\t\t\t}\n\t\t\th4 {\n\t\t\t\tfont-size: 20px;\n\t\t\t\tmargin: 0 0 15px;\n\t\t\t}\n\t\t\tp {\n\t\t\t\tfont-size: 13px;\n\t\t\t\tmargin:0 0 6px;\n\t\t\t\t&.wp-smush-scan-hold-on-notice {\n\t\t\t\t\tfont-size: 14px;\n\t\t\t\t\tmargin-bottom:0;\n\t\t\t\t\topacity:1;\n\t\t\t\t\tanimation: fadeIn 3s;\n\t\t\t\t\t&.sui-hidden {\n\t\t\t\t\t\tanimation:none;\n\t\t\t\t\t}\n\n\t\t\t\t}\n\t\t\t}\n\t\t\tstrong {\n\t\t\t\tfont-weight: 700;\n\t\t\t}\n\t\t}\n\t\t.sui-progress {\n\t\t\tposition: absolute;\n\t\t\tbottom: 0;\n\t\t\tleft: 0;\n\t\t\tright: 0;\n\t\t\t.sui-progress-bar{\n\t\t\t\theight: var( --progress-bar-height );\n\t\t\t}\n\t\t\t.wp-smush-progress-inner{\n\t\t\t\ttransition: width var(--progress-transition-duration) ease-in-out;\n\t\t\t}\n\t\t}\n\t}\n\n\t.smush-settings-wrapper {\n\t\t.smush-png2jpg-setting-note {\n\t\t\tp span {\n\t\t\t\tdisplay: block;\n\t\t\t\tmargin-top:20px;\n\t\t\t}\n\t\t}\n\t}\n\n\t.wp-smush-recheck-images-notice-box {\n\t\t> .sui-notice {\n\t\t\tanimation: fadeIn 3s;\n\t\t\t&.sui-hidden {\n\t\t\t\tanimation: none;\n\t\t\t}\n\t\t}\n\t}\n\n\t.sui-tag.smush-sui-tag-new {\n\t\tmin-height: 12px;\n\t\tpadding: 2px 8px 1px;\n\t\tborder: 0;\n\t\tborder-radius: 6px;\n\t\tfont-size: 8px;\n\t\tline-height: 9px;\n\t\ttext-align: center;\n\t\ttext-transform: uppercase;\n\t\tbackground-color: #1ABC9C;\n\t\tcolor:#fff;\n\t}\n\t.wp-smush-sui-cross-sell-modules {\n\t\t[class*=sui-cross-] {\n\t\t\tposition: relative;\n\t\t\t>span {\n\t\t\t\twidth: 60px!important;\n\t\t\t\theight: 60px!important;\n\t\t\t\tborder:none !important;\n\t\t\t\tborder-radius: unset !important;\n\t\t\t\tbox-shadow: none !important;\n\t\t\t\tbackground-size: 60px 60px!important;\n\t\t\t}\n\t\t\t@media screen and ( min-width:600px ) {\n\t\t\t\t&>span {\n\t\t\t\t\tposition: absolute!important;\n\t\t\t\t\ttop: 50% !important;\n\t\t\t\t\tleft: 50% !important;\n\t\t\t\t\ttransform: translate( -50%, -50% )!important;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\t.wp-smush-link-in-progress {\n\t\tcursor: progress !important;\n\t}\n\n\t.wp-smush-lossy-level-tabs {\n\t\tmargin-top:8px!important;\n\t\t.sui-tab-content {\n\t\t\tp {\n\t\t\t\tfont-size: 13px;\n\t\t\t\tline-height: 22px;\n\t\t\t\t.sui-notice-icon {\n\t\t\t\t\tposition: relative;\n\t\t\t\t\ttop:2px;\n\t\t\t\t\tmargin-right: 5px;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\n\t.sui-modal .smush-retry-modal .sui-box {\n\t\t.sui-notice-icon {\n\t\t\t&:before {\n\t\t\t\tcolor:$red;\n\t\t\t}\n\t\t\t&.sui-warning-icon:before {\n\t\t\t\tcolor:$yellow;\n\t\t\t}\n\t\t}\n\t\t.sui-box-title {\n\t\t\tmargin-top: 7px;\n\t\t\tmargin-bottom: 5px;\n\t\t}\n\t\t.sui-description a {\n\t\t\ttext-decoration: underline;\n\t\t}\n\t\t.sui-box-footer {\n\t\t\t.sui-button {\n\t\t\t\tmin-width: 0;\n\t\t\t\t+ .sui-button-ghost {\n\t\t\t\t\tmargin-left: 17px;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\n\t/**\n\t * Fixes the headers and footers with .sui-actions-right on mobiles.\n\t *\n\t * @todo Remove when there's a fix for this in SUI, which isn't planned atm.\n\t * @since 3.9.0\n\t */\n\t@include media(max-width, sm) {\n\t\t.sui-wrap {\n\n\t\t\t.sui-box-header {\n\n\t\t\t\t.sui-box-title {\n\t\t\t\t\tflex: 1;\n\t\t\t\t}\n\n\t\t\t\t.sui-actions-right {\n\t\t\t\t\tflex: 1;\n\t\t\t\t\tflex-direction: column;\n\t\t\t\t\talign-items: flex-end;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t.sui-box-footer .sui-actions-right {\n\t\t\t\tflex-direction: column;\n\t\t\t\talign-items: flex-end;\n\t\t\t}\n\t\t}\n\t}\n}\n", "/* ****************************************************************************\n * MODULE: Directory Smush styles.\n */\n\n@include body-class {\n\n    .wp-smush-progress-dialog,\n    .wp-smush-list-dialog {\n        text-align: left;\n    }\n\n    .sui-directory.sui-message {\n        text-align: left;\n\n        .sui-message-content {\n            text-align: center;\n        }\n    }\n\n    .sui-directory .smush-final-log {\n        margin-top: 30px;\n\n        .sui-description {\n            margin-top: 10px;\n        }\n    }\n\n    ul.fancytree-container {\n        color: #666;\n        font-family: \"Roboto\", sans-serif;\n        font-size: 13px;\n        font-weight: 500;\n        letter-spacing: -0.25px;\n        line-height: 40px;\n        padding: 0;\n        margin: 0;\n        outline: 0 solid transparent;\n        min-height: 0%;\n        position: relative;\n\n        ul {\n            padding: 0 0 0 16px;\n            margin: 0;\n            display: block;\n        }\n\n        /*------------------------------------------------------------------------------\n         * Expander icon\n         *\n         * Note: IE6 doesn't correctly evaluate multiples class names,\n         *\t\t so we create combined class names that can be used in the CSS.\n         *\n         * Prefix: fancytree-exp-\n         * 1st character: 'e': expanded, 'c': collapsed, 'n': no children\n         * 2nd character (optional): 'd': lazy (Delayed)\n         * 3rd character (optional): 'l': Last sibling\n         *----------------------------------------------------------------------------*/\n        span.fancytree-expander {\n            cursor: pointer;\n            font-size: 12px;\n            margin-left: 13px;\n            width: 15px;\n\n            &:before {\n                font-family: wpmudev-plugin-icons, sans-serif;\n            }\n        }\n\n        .fancytree-exp-c span.fancytree-expander,\n        .fancytree-exp-cd:not(.fancytree-unselectable) span.fancytree-expander,\n        .fancytree-exp-cf:not(.fancytree-unselectable) span.fancytree-expander {\n            margin-left: 13px;\n        }\n\n        // --- End nodes (use connectors instead of expanders)\n\n        .fancytree-expanded.fancytree-exp-n span.fancytree-expander,\n        .fancytree-expanded.fancytree-exp-nl span.fancytree-expander {\n            width: 13px;\n            display: inline-block;\n\n            &:before {\n                background-image: none;\n                cursor: default;\n            }\n        }\n\n        .fancytree-exp-n span.fancytree-expander,\n        .fancytree-exp-nl span.fancytree-expander {\n            width: 12px;\n            display: inline-block;\n        }\n\n        .fancytree-exp-nl span.fancytree-expander:before {\n            content: \"\\131\";\n            cursor: default;\n        }\n\n        span.fancytree-ico-c  span.fancytree-expander:before {\n            content: '';\n            cursor: default;\n        }\n\n        // --- Collapsed\n\n        .fancytree-exp-c span.fancytree-expander:before,\n        .fancytree-exp-cl span.fancytree-expander:before,\n        .fancytree-exp-cd span.fancytree-expander:before,\n        .fancytree-exp-cdl span.fancytree-expander:before,\n        .fancytree-exp-e span.fancytree-expander:before,\n        .fancytree-exp-ed span.fancytree-expander:before,\n        .fancytree-exp-el span.fancytree-expander:before,\n        .fancytree-exp-edl span.fancytree-expander:before {\n            color: #888888;\n            content: \"\\2DC\";\n        }\n\n        // --- Expanded\n\n        .fancytree-exp-e span.fancytree-expander:before,\n        .fancytree-exp-ed span.fancytree-expander:before,\n        .fancytree-exp-el span.fancytree-expander:before,\n        .fancytree-exp-edl span.fancytree-expander:before {\n            content: \"\\131\";\n        }\n\n        // --- Unselectable\n\n        .fancytree-unselectable span.fancytree-expander:before {\n            content: \"9\";\n        }\n\n        /* Fade out expanders, when container is not hovered or active */\n        .fancytree-fade-expander {\n            span.fancytree-expander:before {\n                transition: opacity 1.5s;\n                opacity: 0;\n            }\n            &:hover span.fancytree-expander:before,\n            &.fancytree-treefocus span.fancytree-expander:before,\n            .fancytree-treefocus span.fancytree-expander:before,\n            [class*='fancytree-statusnode-'] span.fancytree-expander:before {\n                transition: opacity 0.6s;\n                opacity: 1;\n            }\n        }\n\n        /*------------------------------------------------------------------------------\n         * Checkbox icon\n         *----------------------------------------------------------------------------*/\n\n        span.fancytree-checkbox {\n            margin-right: 5px;\n            margin-left: 12px;\n            border-radius: 3px;\n            border: 1px solid #ddd;\n            background-color: #e6e6e6;\n            display: inline-block;\n            width: 16px;\n            height: 16px;\n            top: 2px;\n            position: relative;\n            transition: .2s;\n\n            @include icon( before, check );\n            &:before {\n                opacity: 0;\n                color: #fff;\n                font-size: 10px;\n                line-height: 14px;\n                position: absolute;\n                width: 100%;\n                text-align: center;\n                transition: .2s;\n            }\n        }\n\n        .fancytree-selected span.fancytree-checkbox {\n            border: 1px solid #17a8e3;\n            background-color: #17a8e3;\n\n            &:before {\n                opacity: 1;\n            }\n        }\n\n        .fancytree-expanded span.fancytree-checkbox {\n            margin-left: 11px;\n        }\n\n        // Unselectable is dimmed, without hover effects\n        .fancytree-unselectable {\n            background-color: transparent !important;\n\n            // Fix for bug in library.\n            &.fancytree-selected {\n                margin-left: -9px;\n\n                span.fancytree-expander {\n                    margin-left: 10px;\n                }\n\n                span.fancytree-checkbox {\n                    border: 1px solid #ddd;\n                    background-color: #e6e6e6;\n\n                    &:before {\n                        color: #e6e6e6 !important;\n                    }\n                }\n\n                span.fancytree-title {\n                    color: #666;\n                }\n            }\n\n            span.fancytree-expander,\n            span.fancytree-icon,\n            span.fancytree-checkbox,\n            span.fancytree-title {\n                opacity: 0.4;\n                filter: alpha(opacity=40);\n\n                &:before {\n                    color: #666 !important;\n                }\n            }\n        }\n\n        /*------------------------------------------------------------------------------\n         * Node type icon\n         * Note: IE6 doesn't correctly evaluate multiples class names,\n         *\t\t so we create combined class names that can be used in the CSS.\n         *\n         * Prefix: fancytree-ico-\n         * 1st character: 'e': expanded, 'c': collapsed\n         * 2nd character (optional): 'f': folder\n         *----------------------------------------------------------------------------*/\n\n        span.fancytree-icon:before { // Default icon\n            margin-left: 10px;\n            font-family: wpmudev-plugin-icons, sans-serif;\n            font-size: 16px;\n            color: #AAA;\n            content: 'D';\n            position: relative;\n            top: 1px;\n        }\n\n        /* Documents */\n        .fancytree-ico-c span.fancytree-icon:before  { // Collapsed folder (empty)\n            content: 'D';\n        }\n        .fancytree-has-children.fancytree-ico-c span.fancytree-icon:before  { // Collapsed folder (not empty)\n            content: 'D';\n        }\n        .fancytree-ico-e span.fancytree-icon:before  { // Expanded folder\n            content: '\\BB';\n        }\n\n        /* Folders */\n        .fancytree-exp-n.fancytree-ico-ef span.fancytree-icon:before,\n        .fancytree-exp-nl.fancytree-ico-ef span.fancytree-icon:before,\n        .fancytree-ico-cf span.fancytree-icon:before  { // Collapsed folder (empty)\n            content: '\\2D8';\n        }\n        .fancytree-has-children.fancytree-ico-cf span.fancytree-icon:before  { // Collapsed folder (not empty)\n            content: '\\2D8';\n        }\n        .fancytree-ico-ef span.fancytree-icon:before  { // Expanded folder\n            content: '\\BB';\n        }\n\n        // 'Loading' status overrides all others\n        .fancytree-loading span.fancytree-expander:before,\n        .fancytree-statusnode-loading span.fancytree-icon:before {\n            content: 'N';\n            display: inline-block;\n            animation: spin 1.3s linear infinite;\n        }\n\n        /*------------------------------------------------------------------------------\n         * Node titles and highlighting\n         *----------------------------------------------------------------------------*/\n\n        span.fancytree-node {\n            display: inherit;\n            width: 100%;\n            margin-top: 5px;\n            min-height: 40px;\n\n            &:not(.fancytree-unselectable):hover {\n                background-color: #F8F8F8;\n            }\n        }\n        span.fancytree-title {\n            color: #666; // inherit doesn't work on IE\n            cursor: pointer;\n            display: inline-block; // Better alignment, when title contains <br>\n            vertical-align: top;\n            min-height: 20px;\n            padding: 0 3px 0 3px; // Otherwise italic font will be outside right bounds\n            margin: 0 0 0 5px;\n            border: 1px solid transparent;  // avoid jumping, when a border is added on hover\n            border-radius: 4px;\n            font-weight: 500;\n        }\n        span.fancytree-node.fancytree-error span.fancytree-title {\n            //color: @fancy-font-error-color;\n        }\n\n        span.fancytree-expanded,\n        span.fancytree-selected {\n            border-radius: 4px;\n            background-color: #F8F8F8;\n            color: #17A8E3;\n\n            span.fancytree-title {\n                color: #666666;\n            }\n        }\n\n        span.fancytree-selected {\n            background-color: #E1F6FF;\n\n            span.fancytree-expander:before,\n            span.fancytree-icon:before,\n            span.fancytree-title {\n                color: #17A8E3;\n            }\n        }\n\n        span.fancytree-focused {\n            background-color: #e1e1e1 !important;\n        }\n    }\n\n}\n", "@import \"variables\";\n\n/**\n * CDN styles\n *\n * @since 3.0\n */\n\n@include body-class {\n\n    .sui-wrap {\n\n        .sui-box-settings-row .sui-box-settings-col-1 {\n            vertical-align: top;\n        }\n\n        &.wrap-smush-cdn {\n            .sui-box-header .sui-actions-right .sui-icon-info{\n                font-size: 16px;\n                position: relative;\n                top: 1.5px;\n            }\n        }\n\n        .sui-cdn {\n            form p:first-of-type {\n                margin-top: 0;\n            }\n        }\n        .wp-smush-stats {\n            display: flex;\n            align-items: center;\n            line-height: 0;\n\n            .sui-tooltip {\n                line-height: 10px;\n                margin-right: 10px;\n            }\n        }\n\n        /* Filename Extensions Icons */\n        .smush-filename-extension {\n            border-radius: 4px;\n            display: inline-block;\n            font-size: 9px;\n            font-weight: 600;\n            color: #fff;\n            text-transform: uppercase;\n            text-align: center;\n            line-height: 43px;\n            height: 30px;\n            margin: 0 5px 0 0;\n            width: 30px;\n\n            &.smush-extension-jpeg,\n            &.smush-extension-jpg { background-color: #F7E100; }\n            &.smush-extension-png { background-color: #FFB694; }\n            &.smush-extension-gif { background-color: #72D5D4; }\n            &.smush-extension-webp { background-color: #72ADD5; }\n            &.smush-extension-svg { background-color: #88D572; }\n            &.smush-extension-iframe {\n                background-color: #8772D5;\n                font-size: 7px;\n            }\n        }\n    }\n\n}\n", " /**\n * Webp styles\n *\n * @since 3.8.0\n */\n\n@include body-class(true) {\n\n\t#smush-box-webp-webp {\n\t\t.sui-box-header {\n\t\t\t.smush-sui-tag-new {\n\t\t\t\tfont-size: 10px;\n\t\t\t\tline-height: 12px;\n\t\t\t}\n\t\t}\n\t\t.smush-webp-supported-browser {\n\t\t\theight: 30px;\n\t\t\twidth: 30px;\n\t\t\tpadding: 5px;\n\t\t\tmargin-right: 10px;\n\t\t\tborder-radius: 4px;\n\t\t\tbackground-color: #F2F2F2;\n\t\t\tdisplay: inline-block;\n\n\t\t\timg {\n\t\t\t\theight: 20px;\n\t\t\t\twidth: 20px;\n\t\t\t}\n\t\t}\n\t}\n\n\t#smush-box-webp-wizard {\n\t\t.sui-row-with-sidenav {\n\t\t\tmargin-bottom: 0;\n\n\t\t\t.sui-sidenav {\n\t\t\t\tpadding: 30px;\n\t\t\t\tborder-top-left-radius: $border-radius;\n\t\t\t\tbackground-color: #F8F8F8;\n\n\t\t\t\t.smush-wizard-bar-subtitle {\n\t\t\t\t\tfont-size: 11px;\n\t\t\t\t\tline-height: 20px;\n\t\t\t\t\tfont-weight: 700;\n\t\t\t\t\tcolor: #AAAAAA;\n\t\t\t\t\ttext-transform: uppercase;\n\t\t\t\t}\n\n\t\t\t\t.smush-sidenav-title {\n\t\t\t\t\tdisplay: flex;\n\t\t\t\t\talign-items: center;\n\t\t\t\t\tmargin-bottom: 33px;\n\n\t\t\t\t\th4 {\n\t\t\t\t\t\tmargin: 0;\n\t\t\t\t\t\tline-height: 20px;\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t.smush-wizard-steps-container {\n\n\t\t\t\t\tul {\n\t\t\t\t\t\tmargin: 0;\n\n\t\t\t\t\t\t@include media(max-width, lg) {\n\t\t\t\t\t\t\tdisplay: flex;\n\t\t\t\t\t\t\tflex-direction: row;\n\t\t\t\t\t\t\tjustify-content: space-between;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\n\t\t\t\t\t.smush-wizard-bar-step {\n\t\t\t\t\t\tdisplay: inline-block;\n\t\t\t\t\t\tfont-size: 13px;\n\t\t\t\t\t\tcolor: #AAAAAA;\n\t\t\t\t\t\tline-height: 22px;\n\t\t\t\t\t\tfont-weight: 500;\n\t\t\t\t\t\tmargin-bottom: 0;\n\n\t\t\t\t\t\t.smush-wizard-bar-step-number {\n\t\t\t\t\t\t\tdisplay: inline-block;\n\t\t\t\t\t\t\tmargin-right: 10px;\n\t\t\t\t\t\t\ttext-align: center;\n\t\t\t\t\t\t\tborder-radius: 50%;\n\t\t\t\t\t\t\twidth: 22px;\n\t\t\t\t\t\t\theight: 22px;\n\t\t\t\t\t\t\tfont-size: 11px;\n\t\t\t\t\t\t\tbackground-color: #F2F2F2;\n\t\t\t\t\t\t\tborder: 1px solid #DDDDDD;\n\n\t\t\t\t\t\t\t@include media(max-width, lg) {\n\t\t\t\t\t\t\t\tdisplay: block;\n\t\t\t\t\t\t\t\tmargin: 0 auto 5px auto;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t&.disabled {\n\t\t\t\t\t\t\tcolor: #DDDDDD;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t&.current {\n\t\t\t\t\t\t\tcolor: #333333;\n\n\t\t\t\t\t\t\t.smush-wizard-bar-step-number {\n\t\t\t\t\t\t\t\tbackground-color: #FFFFFF;\n\t\t\t\t\t\t\t\tborder-color: #333333;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t&.done .smush-wizard-bar-step-number {\n\t\t\t\t\t\t\tbackground-color: #1ABC9C;\n\t\t\t\t\t\t\tborder-color: #1ABC9C;\n\n\t\t\t\t\t\t\t.sui-icon-check::before {\n\t\t\t\t\t\t\t\tcolor: #FFFFFF;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t@include media(min-width, lg) {\n\t\t\t\t\t\t\tdisplay: block;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t@include media(max-width, lg) {\n\t\t\t\t\t\t\twidth: 70px;\n\t\t\t\t\t\t\ttext-align: center;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\n\t\t\t\t\tsvg {\n\n\t\t\t\t\t\tline {\n\t\t\t\t\t\t\tstroke-width: 4px;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t&.smush-svg-desktop {\n\t\t\t\t\t\t\theight: 40px;\n\t\t\t\t\t\t\twidth: 22px;\n\t\t\t\t\t\t\tmargin-left: 10px;\n\t\t\t\t\t\t\tdisplay: none;\n\n\t\t\t\t\t\t\t@include media(min-width, lg) {\n\t\t\t\t\t\t\t\tdisplay: block;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t&.smush-svg-mobile {\n\t\t\t\t\t\t\twidth: 100%;\n\t\t\t\t\t\t\theight: 4px;\n\t\t\t\t\t\t\tdisplay: block;\n\t\t\t\t\t\t\tmargin-bottom: -14px;\n\t\t\t\t\t\t\tpadding: 0 35px;\n\n\t\t\t\t\t\t\t@include media(min-width, lg) {\n\t\t\t\t\t\t\t\tdisplay: none;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t@include media(max-width, sm) {\n\t\t\t\t\tpadding: 20px;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t.smush-wizard-steps-content-wrapper {\n\t\t\t\tpadding: 20px;\n\n\t\t\t\t.smush-wizard-steps-content {\n\t\t\t\t\tpadding: 0 70px;\n\t\t\t\t\ttext-align: center;\n\n\t\t\t\t\t&:first-child {\n\t\t\t\t\t\tpadding-top: 30px;\n\t\t\t\t\t}\n\n\t\t\t\t\t.smush-step-indicator {\n\t\t\t\t\t\tfont-size: 11px;\n\t\t\t\t\t\tfont-weight: 500;\n\t\t\t\t\t\tcolor: #888888;\n\t\t\t\t\t}\n\n\t\t\t\t\th2 {\n\t\t\t\t\t\tmargin: 0;\n\t\t\t\t\t}\n\n\t\t\t\t\t@include media(max-width, sm) {\n\t\t\t\t\t\tpadding: 0;\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t&.smush-wizard-step-1 {\n\n\t\t\t\t\t.sui-box-selectors {\n\t\t\t\t\t\tpadding-left: 115px;\n\t\t\t\t\t\tpadding-right: 115px;\n\t\t\t\t\t\tmargin-bottom: 15px;\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t&.smush-wizard-step-2 {\n\n\t\t\t\t\t.smush-wizard-rules-wrapper {\n\t\t\t\t\t\ttext-align: left;\n\t\t\t\t\t}\n\n\t\t\t\t\t.sui-tabs-menu {\n\t\t\t\t\t\tjustify-content: center;\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t@include media(min-width, sm) {\n\t\t\t\t\tpadding: 30px;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n}\n", "@include body-class(true) {\n\n\t&.sui-color-accessible {\n\t\tcolor: $accessible-dark;\n\n\t\t// Typography.\n\t\th1, h2, h3, h4, h5, h6, p, p small, li, code, pre {\n\t\t\tcolor: $accessible-dark;\n\t\t}\n\t\ta {\n\t\t\tcolor: lighten($accessible-dark, 10%) !important;\n\t\t\t&:hover {\n\t\t\t\tcolor: $accessible-dark !important;\n\t\t\t}\n\t\t}\n\n\t\tpre,\n\t\tcode {\n\t\t\tbackground: $accessible-light;\n\t\t}\n\n\t\t// Box Settings\n\t\t.sui-settings-label {\n\t\t\tcolor: $accessible-dark;\n\t\t}\n\n\t\t// Forms.\n\t\t.sui-form-control,\n\t\t.sui-label,\n\t\t.sui-error-message {\n\t\t\tborder-color: $accessible-dark;\n\t\t\tcolor: $accessible-dark;\n\t\t\tbackground-color: $accessible-light;\n\t\t\t&::placeholder {\n\t\t\t\tcolor: $accessible-dark-alt;\n\t\t\t}\n\t\t}\n\n\t\t.sui-checkbox,\n\t\t.sui-radio {\n\t\t\tinput + span {\n\t\t\t\tborder-color: $accessible-dark;\n\t\t\t\t& + span {\n\t\t\t\t\tcolor: $accessible-dark;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t.sui-upload-button {\n\t\t\tcolor: $accessible-dark;\n\t\t\tbackground: $accessible-light;\n\t\t}\n\n\t\t.sui-upload-label {\n\t\t\t&:hover {\n\t\t\t\t.sui-upload-button {\n\t\t\t\t\tcolor: $accessible-light;\n\t\t\t\t\tbackground: $accessible-dark;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t// Recipient.\n\t\t.sui-recipient {\n\t\t\tspan {\n\t\t\t\tcolor: $accessible-dark;\n\t\t\t}\n\t\t}\n\n\t\t// Sidenav.\n\t\t.sui-row-with-sidenav {\n\t\t\t.sui-sidenav {\n\t\t\t\t.sui-vertical-tabs {\n\t\t\t\t\ta {\n\t\t\t\t\t\tcolor: $accessible-dark;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\n\t\t}\n\n\t\t// Tabs.\n\t\t.sui-tabs {\n\t\t\t> [data-tabs],\n\t\t\t> .sui-tabs-menu {\n\t\t\t\t> *,\n\t\t\t\t.sui-tab-item {\n\t\t\t\t\tcolor: $accessible-dark;\n\t\t\t\t\t&:hover {\n\t\t\t\t\t\tcolor: $accessible-dark-alt;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t// Toggle Tabs.\n\t\t.sui-side-tabs {\n\t\t\t> [data-tabs],\n\t\t\t> .sui-tabs-menu {\n\t\t\t\t> *,\n\t\t\t\t.sui-tab-item {\n\t\t\t\t\tcolor: $accessible-dark-alt;\n\t\t\t\t\t&.active {\n\t\t\t\t\t\tcolor: $accessible-light;\n\t\t\t\t\t\tbackground-color: $accessible-dark;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t// Tags.\n\t\t.sui-tag {\n\t\t\tbackground: $accessible-dark;\n\t\t\tcolor: $accessible-light;\n\t\t\t&.sui-tag-ghost {\n\t\t\t\tbackground: $accessible-light;\n\t\t\t\tcolor: $accessible-dark;\n\t\t\t\tborder-color: $accessible-dark;\n\n\t\t\t}\n\t\t}\n\n\t\t// Notifications.\n\t\t.sui-notice,\n\t\t.sui-notice-top {\n\t\t\tborder-left-color: $accessible-dark;\n\t\t\t.sui-notice-dismiss a,\n\t\t\tp:first-of-type:before {\n\t\t\t\tcolor: $accessible-dark;\n\t\t\t}\n\t\t}\n\t\t.sui-upsell-row {\n\t\t\t.sui-upsell-notice {\n\t\t\t\tp {\n\t\t\t\t\tborder-left: 2px solid $accessible-dark;\n\t\t\t\t\t&:first-of-type:before {\n\t\t\t\t\t\tcolor: $accessible-dark;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t// Icons.\n\t\t[class*=\"sui-icon-\"],\n\t\t.sui-dialog-close {\n\t\t\t&:before {\n\t\t\t\tcolor: $accessible-dark !important;\n\t\t\t}\n\t\t}\n\t\t.sui-tab-item {\n\t\t\t&.active {\n\t\t\t\t[class*=\"sui-icon-\"] {\n\t\t\t\t\t&:before {\n\t\t\t\t\t\tcolor: $accessible-light !important;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t// Progress bars.\n\t\t.sui-progress-block {\n\t\t\t.sui-progress {\n\t\t\t\t.sui-progress-bar {\n\t\t\t\t\tspan {\n\t\t\t\t\t\tbackground: $accessible-dark;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\t.sui-progress-text {\n\t\t\t\t\tspan {\n\t\t\t\t\t\tcolor: $accessible-dark;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t\t.sui-progress-state {\n\t\t\tspan {\n\t\t\t\tcolor: $accessible-dark;\n\t\t\t}\n\t\t}\n\n\t\t// ELEMENT: Tables\n\t\t.sui-table {\n\t\t\tborder-color: $accessible-dark;\n\n\t\t\tth, td {\n\t\t\t\tcolor: $accessible-dark;\n\t\t\t}\n\n\t\t\tthead, tbody, tfoot {\n\n\t\t\t\t> tr > {\n\n\t\t\t\t\tth,\n\t\t\t\t\t.sui-table-item-title,\n\t\t\t\t\t.sui-accordion-item-title {\n\t\t\t\t\t\tcolor: $accessible-dark;\n\t\t\t\t\t}\n\n\t\t\t\t\tth, td {\n\t\t\t\t\t\tborder-bottom-color: $accessible-dark;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\n\t\t\ttbody {\n\n\t\t\t\ttr.sui-error,\n\t\t\t\ttr.sui-warning,\n\t\t\t\ttr.sui-success {\n\n\t\t\t\t\tth,\n\t\t\t\t\t.sui-table-item-title,\n\t\t\t\t\t.sui-accordion-item-title {\n\t\t\t\t\t\tcolor: $accessible-dark;\n\t\t\t\t\t}\n\n\t\t\t\t\ttd {\n\t\t\t\t\t\tcolor: $accessible-dark;\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t// STATUS: All\n\t\t\t\ttr.sui-default,\n\t\t\t\ttr.sui-error,\n\t\t\t\ttr.sui-warning,\n\t\t\t\ttr.sui-success {\n\t\t\t\t\tbox-shadow: none;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t&-title {\n\t\t\t\tcolor: $accessible-dark;\n\t\t\t}\n\t\t}\n\n\t\t// ELEMENT: Accordions\n\t\t.sui-accordion {\n\n\t\t\t// DESIGN: Table (Flexbox)\n\t\t\t&:not(.sui-table):not(.sui-accordion-block):not(.sui-builder-fields) {\n\t\t\t\tborder-color: $accessible-dark;\n\n\t\t\t\t.sui-accordion-header {\n\t\t\t\t\tborder-bottom-color: $accessible-dark;\n\n\t\t\t\t\t> div {\n\t\t\t\t\t\tcolor: $accessible-dark;\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t.sui-accordion-item {\n\t\t\t\t\tborder-bottom-color: $accessible-dark;\n\n\t\t\t\t\t.sui-accordion-item-header {\n\t\t\t\t\t\tcolor: $accessible-dark;\n\n\t\t\t\t\t\t.sui-accordion-item-title {\n\t\t\t\t\t\t\tcolor: $accessible-dark;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\n\t\t\t\t\t// STATUS: All\n\t\t\t\t\t&.sui-default,\n\t\t\t\t\t&.sui-error,\n\t\t\t\t\t&.sui-warning,\n\t\t\t\t\t&.sui-success {\n\n\t\t\t\t\t\t> .sui-accordion-item-header,\n\t\t\t\t\t\t> .sui-accordion-item-body {\n\t\t\t\t\t\t\tbox-shadow: none;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\n\t\t\t\t\t// When item is disabled\n\t\t\t\t\t&.sui-accordion-item--disabled {\n\n\t\t\t\t\t\t.sui-accordion-item-header {\n\n\t\t\t\t\t\t\t&, .sui-accordion-item-title {\n\t\t\t\t\t\t\t\tcolor: $accessible-dark-alt;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t// DESIGN: Table\n\t\t\t// Must use in conjunction with .sui-table on the <table> element.\n\t\t\t&.sui-table {\n\n\t\t\t\t> tbody {\n\n\t\t\t\t\t> .sui-accordion-item {\n\n\t\t\t\t\t\t// STATUS: All\n\t\t\t\t\t\t&.sui-default,\n\t\t\t\t\t\t&.sui-error,\n\t\t\t\t\t\t&.sui-warning,\n\t\t\t\t\t\t&.sui-success {\n\n\t\t\t\t\t\t\t+ .sui-accordion-item-content {\n\t\t\t\t\t\t\t\tbox-shadow: none;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t// When item is disabled\n\t\t\t\t\t\t&.sui-accordion-item--disabled {\n\n\t\t\t\t\t\t\tth, td {\n\t\t\t\t\t\t\t\tcolor: $accessible-dark-alt;\n\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t&.sui-error,\n\t\t\t\t\t\t\t&.sui-warning,\n\t\t\t\t\t\t\t&.sui-success {\n\t\t\t\t\t\t\t\tbox-shadow: none;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t// DESIGN: Blocks (Flexbox)\n\t\t\t&-block {\n\n\t\t\t\t.sui-accordion-item {\n\t\t\t\t\tbackground-color: $accessible-light;\n\n\t\t\t\t\t.sui-accordion-item-header {\n\t\t\t\t\t\tcolor: $accessible-dark;\n\n\t\t\t\t\t\tstrong {\n\t\t\t\t\t\t\tcolor: inherit;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t.sui-accordion-item-title {\n\t\t\t\t\t\t\tcolor: $accessible-dark;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\n\t\t\t\t\t.sui-accordion-item-body {\n\n\t\t\t\t\t\t.sui-accordion-item-data {\n\n\t\t\t\t\t\t\tli strong {\n\t\t\t\t\t\t\t\tcolor: inherit;\n\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t// Loading data animation when accordion opens\n\t\t\t\t\t\t\t&.sui-onload {\n\n\t\t\t\t\t\t\t\tli > * {\n\t\t\t\t\t\t\t\t\tcolor: transparent !important;\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t// Scores.\n\t\t.sui-grade-a,\n\t\t.sui-grade-b,\n\t\t.sui-grade-c,\n\t\t.sui-grade-d,\n\t\t.sui-grade-e,\n\t\t.sui-grade-f  {\n\t\t\tsvg circle:last-child {\n\t\t\t\tstroke: $accessible-dark;\n\t\t\t}\n\t\t}\n\n\t\t// Dropdowns.\n\t\t.sui-dropdown {\n\t\t\tul {\n\t\t\t\tli {\n\t\t\t\t\t&,\n\t\t\t\t\tbutton,\n\t\t\t\t\ta {\n\t\t\t\t\t\tcolor: $accessible-dark;\n\t\t\t\t\t\t&:hover,\n\t\t\t\t\t\t&:focus,\n\t\t\t\t\t\t&:active {\n\t\t\t\t\t\t\tcolor: $accessible-dark;\n\t\t\t\t\t\t\tbackground-color: rgba(51, 51, 51, 0.05);\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\n\t\t}\n\n\t\t// Buttons.\n\t\t.sui-button-icon:not(.sui-button),\n\t\t.sui-button {\n\t\t\tcolor: $accessible-light !important;\n\t\t\tbackground: $accessible-dark !important;\n\t\t\t&:hover {\n\t\t\t\tcolor: $accessible-light !important;\n\t\t\t}\n\t\t\t[class*=\"sui-icon-\"] {\n\t\t\t\t&:before {\n\t\t\t\t\tcolor: $accessible-light !important;\n\t\t\t\t}\n\t\t\t}\n\t\t\t&.sui-button-ghost {\n\t\t\t\tpadding: 7px 16px;\n\t\t\t\tborder: 0;\n\t\t\t}\n\t\t\t&.sui-button-upsell {\n\t\t\t\tbackground: $accessible-light !important;\n\t\t\t\tcolor: $accessible-dark !important;\n\t\t\t\tborder-color: $accessible-dark;\n\t\t\t\t&:hover {\n\t\t\t\t\tborder-color: $accessible-dark;\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t}\n\n\t\t// Lists.\n\t\t.sui-list {\n\t\t\t.sui-list-label,\n\t\t\t.sui-list-detail {\n\t\t\t\tcolor: $accessible-dark;\n\t\t\t}\n\t\t}\n\n\t\t// Toggles.\n\t\t.sui-toggle {\n\t\t\tinput[type=checkbox] + .sui-toggle-slider {\n\t\t\t\t&:hover {\n\t\t\t\t\tbox-shadow: none;\n\t\t\t\t}\n\t\t\t}\n\t\t\tinput[type=checkbox]:checked + .sui-toggle-slider {\n\t\t\t\tbackground-color: $accessible-dark;\n\t\t\t\t&:hover {\n\t\t\t\t\tbox-shadow: none;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t// Checkbox and Radio.\n\t\t.sui-checkbox,\n\t\t.sui-radio {\n\t\t\tinput:checked+span {\n\t\t\t\tborder-color: $accessible-dark;\n\t\t\t\tbackground-color: $accessible-dark;\n\t\t\t}\n\t\t}\n\n\t\t// Status dot.\n\t\t.sui-status-dot {\n\t\t\tspan {\n\t\t\t\tbackground-color: $accessible-light;\n\t\t\t\tborder: 1px solid $accessible-dark;\n\t\t\t}\n\t\t\t&.sui-published {\n\t\t\t\tspan {\n\t\t\t\t\tbackground-color: $accessible-dark;\n\t\t\t\t\tborder: none;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t// WP Editor.\n\t\t.wp-editor-wrap {\n\t\t\t&.tmce-active {\n\t\t\t\t.wp-editor-tabs {\n\t\t\t\t\t.switch-tmce {\n\t\t\t\t\t\tcolor: $accessible-light;\n\t\t\t\t\t\tbackground-color: $accessible-dark;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t\t&.html-active {\n\t\t\t\t.wp-editor-tabs {\n\t\t\t\t\t.switch-html {\n\t\t\t\t\t\tcolor: $accessible-light;\n\t\t\t\t\t\tbackground-color: $accessible-dark;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t\t.wp-media-buttons {\n\t\t\t\t.insert-media {\n\t\t\t\t\tbackground-color: $accessible-dark !important;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t// Ace editor selectors.\n\t\t.sui-ace-selectors {\n\t\t\ta.sui-selector {\n\t\t\t\tbackground-color: $accessible-dark;\n\t\t\t\tcolor: $accessible-light !important;\n\t\t\t}\n\t\t}\n\n\t\t// ACE editor elements.\n\t\t.ace-sui {\n\t\t\t.ace_gutter {\n\t\t\t\tbackground: $accessible-dark;\n\t\t\t}\n\t\t\t.ace_text-layer {\n\t\t\t\tcolor: $accessible-dark;\n\t\t\t}\n\t\t\t.ace_content {\n\t\t\t\tbackground-color: $accessible-light;\n\t\t\t}\n\t\t\t.ace_line {\n\t\t\t\tfilter: brightness(0%);\n\t\t\t}\n\t\t}\n\n\t\t// Pagination filters.\n\t\t.sui-pagination-wrap {\n\t\t\t.sui-pagination-filter {\n\t\t\t\t.sui-active-filter,\n\t\t\t\t.sui-active-filter-remove,\n\t\t\t\t.sui-active-filter-remove:hover {\n\t\t\t\t\tbackground-color: $accessible-dark;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t// SUI box.\n\t\t.sui-box {\n\t\t\t.sui-box-title {\n\t\t\t\tcolor: $accessible-dark !important;\n\t\t\t}\n\t\t}\n\n\t\t// SUI selector box.\n\t\t.sui-box-selector {\n\t\t\tinput + span {\n\t\t\t\tcolor: $accessible-dark-alt;\n\t\t\t\t~span {\n\t\t\t\t\tcolor: $accessible-dark-alt;\n\t\t\t\t}\n\t\t\t}\n\t\t\tinput:checked+span {\n\t\t\t\tbackground-color: $accessible-light !important;\n\t\t\t\tcolor: $accessible-dark;\n\t\t\t\t&:after {\n\t\t\t\t\tborder-top: 40px solid $accessible-dark;\n\t\t\t\t}\n\t\t\t\t~span {\n\t\t\t\t\tborder-top-color: $accessible-dark-alt;\n\t\t\t\t}\n\t\t\t}\n\t\t\tinput:checked+span [class*=sui-icon]:before {\n\t\t\t\tcolor: $accessible-dark;\n\t\t\t}\n\t\t}\n\n\t\t// SUI status box.\n\t\t.sui-box-status {\n\t\t\t.sui-status {\n\t\t\t\t.sui-status-changes,\n\t\t\t\t.sui-status-module {\n\t\t\t\t\tcolor: $accessible-dark-alt;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t// Sidenav.\n\t\t.sui-row-with-sidenav {\n\t\t\t.sui-sidenav {\n\t\t\t\t.sui-vertical-tabs {\n\t\t\t\t\t.sui-vertical-tab.current {\n\t\t\t\t\t\tbox-shadow: 0 0 0 1px $accessible-dark;\n\t\t\t\t\t\tbackground-color: transparent;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t// Footer.\n\t\t.sui-footer {\n\t\t\tcolor: $accessible-dark;\n\t\t}\n\t}\n}\n\n@include body-class(false) {\n\t&.sui-elements-accessible {\n\t\t\n\t\t.sui-select .sui-select-dropdown {\n\t\t\tborder-color: $accessible-dark !important;\n\t\t}\n\n\t\t.select2-results__option {\n\t\t\tcolor: $accessible-dark !important;\n\t\t}\n\t\t\n\t\t.select2-results__option--selected{\n\t\t\tbackground-color: $accessible-dark !important;\n\t\t\tcolor: $accessible-light !important;\n\t\t}\n\t}\n}\n", "@include body-class(true) {\n    &.sui-color-accessible {\n        .smush-final-log .smush-bulk-error-row {\n            box-shadow: inset 2px 0 0 0 $accessible-dark;\n            .smush-bulk-image-data:before {\n                color: $accessible-dark;\n            }\n        }\n        // Bulk Smush Fancy Tree\n        ul.fancytree-container {\n            .fancytree-selected {\n                background-color: #F8F8F8;\n                span.fancytree-checkbox {\n                    border: 1px solid $accessible-dark;\n                    background-color: $accessible-dark;\n                }\n            }\n            span.fancytree-expander:before,\n            span.fancytree-icon:before,\n            span.fancytree-title {\n                color: $accessible-dark;\n            }\n        }\n        // CDN\n        .smush-filename-extension {\n            background-color: $accessible-dark;\n        }\n        // Check images button.\n        .sui-button {\n            &.smush-button-check-success:before {\n                color: $accessible-light;\n            }\n        }\n        // Smush submit note.\n        .smush-submit-note {\n            color: $accessible-dark;\n        }\n\n        // Hightlight lazyload spinner.\n        .sui-lazyload .sui-box-selector [name=\"animation[spinner-icon]\"]:checked+span {\n            background-color: rgba(220,220,222, 0.7)!important;\n        }\n    }\n}\n\n@media all and (-ms-high-contrast: none), (-ms-high-contrast: active) {\n    .sui-wrap .sui-toggle-slider {\n        -ms-high-contrast-adjust: none;\n    }\n}\n"], "names": [], "sourceRoot": ""}