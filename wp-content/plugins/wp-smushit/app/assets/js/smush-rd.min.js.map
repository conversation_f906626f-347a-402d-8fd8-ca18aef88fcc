{"version": 3, "file": "js/smush-rd.min.js", "mappings": "8gCAYE,IAGKA,IAAW,CAChBC,IAAKC,SAASC,eAAgB,mBAC9BC,OAAQF,SAASC,eAAgB,0BACjCE,OAAQ,CACPC,OAAQ,GACRC,QAAS,IAEVC,QAASC,OAAOC,qBAKhBC,KAAI,WAIIC,KAAKX,MACXW,KAAKX,IAAMC,SAASC,eAAgB,oBAE9BS,KAAKR,SACXQ,KAAKR,OAASF,SAASC,eACtB,2BAIFS,KAAKC,UAGLD,KAAKR,OAAOU,iBACX,QACAF,KAAKG,kBAAkBC,KAAMJ,MAE/B,EAKAC,QAAO,WACN,IAAMI,EAAOL,KAAKR,OAAOc,cAAe,KAExCD,EAAKE,UAAUC,IAAK,mBACpBH,EAAKE,UAAUE,OAAQ,iBAEvBT,KAAKU,eAEEV,KAAKP,OAAOC,OAAOiB,QAAYX,KAAKP,OAAOE,QAAQgB,QASzDX,KAAKR,OAAOe,UAAUE,OAAQ,wBAC9BnB,SAASC,eACR,0BACCqB,MAAMC,QAAU,OAClBvB,SAASC,eACR,+BACCqB,MAAMC,QAAU,QAClBb,KAAKc,eAAgB,UACrBd,KAAKc,eAAgB,aAhBrBd,KAAKR,OAAOe,UAAUC,IAAK,wBAC3BlB,SAASC,eACR,0BACCqB,MAAMC,QAAU,QAClBvB,SAASC,eACR,+BACCqB,MAAMC,QAAU,QAanBb,KAAKe,aAELV,EAAKE,UAAUE,OAAQ,mBACvBJ,EAAKE,UAAUC,IAAK,gBACrB,EAQAQ,gBAAe,SAAEC,GAEhB,QAAKA,EAAMV,UAAUW,SAAU,WAM9B,iBAAoBD,EAAME,aAAc,wBAOxCF,EAAMG,cAAgBH,EAAMI,cAC5B,IAAMJ,EAAMG,aAOZH,EAAMK,eAAiBL,EAAMM,eAC7B,IAAMN,EAAMK,cAMN,OAASL,EAAMG,aAAe,OAASH,EAAMI,YACrD,EAQAG,eAAc,SAAEC,GACf,IAAIC,EAAc,GAUlB,OARKD,EAAME,cAAgBF,EAAMG,cAEhCF,EAAc1B,KAAKJ,QAAQiC,aAChBJ,EAAMK,eAAiBL,EAAMM,kBAExCL,EAAc1B,KAAKJ,QAAQoC,aAGrBN,EACLO,QAAS,QAASR,EAAMS,YACxBD,QAAS,SAAUR,EAAMU,YAC5B,EAOArB,eAAc,SAAEsB,GAAO,IAAAC,EAAA,KACtBrC,KAAKP,OAAQ2C,GAAOE,SAAS,SAAErB,EAAOsB,GACrC,IAAMC,EAAOlD,SAASmD,cAAe,OACpCC,EAAUL,EAAKb,eAAgBP,EAAMQ,OAEtCe,EAAKG,aACJ,QACA,4DAEDH,EAAKG,aAAc,eAAgBD,GACnCF,EAAKG,aAAc,aAAc1B,EAAM2B,OACvCJ,EAAKtC,iBAAkB,SAAS,SAAE2C,GAAC,OAClCR,EAAKS,eAAgBD,EAAG,IAGzBL,EAAKO,UAAY,iEAAHC,OAEHT,EAAQ,EAAC,iDAAAS,OACS/B,EAAMQ,MAAMwB,eAAc,OAAAD,OAAQ/B,EAAMQ,MAAMyB,gBAAe,wKAAAF,OAE3C/B,EAAMQ,MAAMS,WAAU,OAAAc,OAAQ/B,EAAMQ,MAAMU,YAAW,0FAAAa,OAE3DN,EAAO,oBAGhDpD,SACEC,eAAgB,yBAA2B6C,GAC3Ce,YAAaX,EAChB,GACD,EAKAzB,WAAU,WAAG,IAAAqC,EAAA,KACE,CAAE,SAAU,WACpBd,SAAS,SAAEF,GAChB,IAAMiB,EAAM/D,SAASC,eACpB,yBAA2B6C,GAEvB,IAAMgB,EAAK3D,OAAQ2C,GAAOzB,OAC9B0C,EAAIzC,MAAMC,QAAU,OAEpBwC,EAAIzC,MAAMC,QAAU,OAEtB,GACD,EAOAiC,eAAc,SAAED,GACf7C,KAAKsD,kBAEL,IAAMC,EAAKjE,SAASkE,uBACnBX,EAAEY,cAAcC,QAAQzC,YAEpB,IAAuBsC,EAAI,KAE/BV,EAAEY,cAAclD,UAAUf,OAAQ,oBAGlC+D,EAAI,GAAII,eAAgB,CACvBC,SAAU,SACVC,MAAO,SACPC,OAAQ,YAETP,EAAI,GAAI3C,MAAMmD,QAAU,MACxBC,YAAY,WACXT,EAAI,GAAI3C,MAAMmD,QAAU,GACzB,GAAG,KAEL,EAKA5D,kBAAiB,WAChBH,KAAKX,IAAIkB,UAAUf,OAAQ,UAC3BQ,KAAKR,OAAOe,UAAUf,OAAQ,UAC9BQ,KAAKsD,iBACN,EAKAA,gBAAe,WACd,IAAMW,EAAQ3E,SAASkE,uBAAwB,oBAC1CS,EAAMtD,OAAS,GACnBuD,MAAMC,KAAMF,GAAQ3B,SAAS,SAAEe,GAAG,OACjCA,EAAI9C,UAAUE,OAAQ,mBAAoB,GAG7C,EAQAC,aAAY,WACX,IAC2B0D,EAD2BC,EAAAC,EAAvChF,SAASiF,qBAAsB,QACnB,IAA3B,IAAAF,EAAAG,MAAAJ,EAAAC,EAAAI,KAAAC,MAA8B,KAAlBzD,EAAKmD,EAAAO,MAChB,IAAK3E,KAAKgB,gBAAiBC,GAA3B,CAKA,IAAMQ,EAAQ,CACbS,WAAYjB,EAAMG,YAClBe,YAAalB,EAAMI,aACnB4B,eAAgBhC,EAAMK,aACtB4B,gBAAiBjC,EAAMM,cACvBI,aAAkC,IAApBV,EAAMG,YAAoBH,EAAMK,aAC9CM,cACsB,IAArBX,EAAMI,aAAqBJ,EAAMM,cAClCO,cAAeb,EAAMG,YAAcH,EAAMK,aACzCS,eAAgBd,EAAMI,aAAeJ,EAAMM,eAI5C,GACGE,EAAME,cACNF,EAAMG,eACNH,EAAMK,eACNL,EAAMM,eAJT,CASA,IAAM6C,EACJnD,EAAME,cAAgBF,EAAMG,cACzB,SACA,UACJiD,EACC,gBAAmB7E,KAAKP,OAAQmF,GAAUjE,OAAS,GAGrDX,KAAKP,OAAQmF,GAAUE,KAAM,CAC5BC,IAAK9D,EACLQ,MAAAA,EACAmB,MAAOiC,IAQR5D,EAAMV,UAAUC,IAAK,sBACrBS,EAAMV,UAAUC,IAAKqE,EAtBrB,CAvBA,CA8CD,CAAC,OAAAG,GAAAX,EAAAxB,EAAAmC,EAAA,SAAAX,EAAAY,GAAA,CACF,EAOAC,QAAO,WAEN,IAAM,IAAIC,KAAMnF,KAAKP,OAAOC,OACtBM,KAAKP,OAAOC,OAAO0F,eAAgBD,KACvCnF,KAAKP,OAAOC,OAAQyF,GAAKJ,IAAIxE,UAAUE,OACtC,sBAEDT,KAAKP,OAAOC,OAAQyF,GAAKJ,IAAIxE,UAAUE,OACtC,kBAAmB0E,IAKtB,IAAM,IAAIA,KAAMnF,KAAKP,OAAOE,QACtBK,KAAKP,OAAOE,QAAQyF,eAAgBD,KACxCnF,KAAKP,OAAOE,QAASwF,GAAKJ,IAAIxE,UAAUE,OACvC,sBAEDT,KAAKP,OAAOE,QAASwF,GAAKJ,IAAIxE,UAAUE,OACvC,kBAAmB0E,IAKtBnF,KAAKP,OAAS,CACbC,OAAQ,GACRC,QAAS,IAQV,IAHA,IAAM0F,EAAW/F,SAASkE,uBACzB,oBAEO6B,EAAS1E,OAAS,GACzB0E,EAAU,GAAI5E,SAGfT,KAAKC,SACN,GAMDJ,OAAOK,iBAAkB,oBAAoB,kBAAMd,EAASW,MAAM,IAClEF,OAAOK,iBAAkB,cAAc,kBAAMd,EAAS8F,SAAS,G", "sources": ["webpack://wp-smushit/./_src/js/frontend/resize-detection.js"], "sourcesContent": ["import '../../scss/resize-detection.scss';\n\n/**\n * Image resize detection (IRD).\n *\n * Show all wrongly scaled images with a highlighted border and resize box.\n *\n * Made in pure JS.\n * DO NOT ADD JQUERY SUPPORT!!!\n *\n * @since 2.9\n */\n( function() {\n\t'use strict';\n\n\tconst SmushIRS = {\n\t\tbar: document.getElementById( 'smush-image-bar' ),\n\t\ttoggle: document.getElementById( 'smush-image-bar-toggle' ),\n\t\timages: {\n\t\t\tbigger: [],\n\t\t\tsmaller: [],\n\t\t},\n\t\tstrings: window.wp_smush_resize_vars,\n\n\t\t/**\n\t\t * Init scripts.\n\t\t */\n\t\tinit() {\n\t\t\t/**\n\t\t\t * Make sure these are set, before we proceed.\n\t\t\t */\n\t\t\tif ( ! this.bar ) {\n\t\t\t\tthis.bar = document.getElementById( 'smush-image-bar' );\n\t\t\t}\n\t\t\tif ( ! this.toggle ) {\n\t\t\t\tthis.toggle = document.getElementById(\n\t\t\t\t\t'smush-image-bar-toggle'\n\t\t\t\t);\n\t\t\t}\n\n\t\t\tthis.process();\n\n\t\t\t// Register the event handler after everything is done.\n\t\t\tthis.toggle.addEventListener(\n\t\t\t\t'click',\n\t\t\t\tthis.handleToggleClick.bind( this )\n\t\t\t);\n\t\t},\n\n\t\t/**\n\t\t * Do image processing.\n\t\t */\n\t\tprocess() {\n\t\t\tconst icon = this.toggle.querySelector( 'i' );\n\n\t\t\ticon.classList.add( 'sui-icon-loader' );\n\t\t\ticon.classList.remove( 'sui-icon-info' );\n\n\t\t\tthis.detectImages();\n\n\t\t\tif ( ! this.images.bigger.length && ! this.images.smaller.length ) {\n\t\t\t\tthis.toggle.classList.add( 'smush-toggle-success' );\n\t\t\t\tdocument.getElementById(\n\t\t\t\t\t'smush-image-bar-notice'\n\t\t\t\t).style.display = 'block';\n\t\t\t\tdocument.getElementById(\n\t\t\t\t\t'smush-image-bar-notice-desc'\n\t\t\t\t).style.display = 'none';\n\t\t\t} else {\n\t\t\t\tthis.toggle.classList.remove( 'smush-toggle-success' );\n\t\t\t\tdocument.getElementById(\n\t\t\t\t\t'smush-image-bar-notice'\n\t\t\t\t).style.display = 'none';\n\t\t\t\tdocument.getElementById(\n\t\t\t\t\t'smush-image-bar-notice-desc'\n\t\t\t\t).style.display = 'block';\n\t\t\t\tthis.generateMarkup( 'bigger' );\n\t\t\t\tthis.generateMarkup( 'smaller' );\n\t\t\t}\n\n\t\t\tthis.toggleDivs();\n\n\t\t\ticon.classList.remove( 'sui-icon-loader' );\n\t\t\ticon.classList.add( 'sui-icon-info' );\n\t\t},\n\n\t\t/**\n\t\t * Various checks to see if the image should be processed.\n\t\t *\n\t\t * @param {Object} image\n\t\t * @return {boolean}  Should skip image or not.\n\t\t */\n\t\tshouldSkipImage( image ) {\n\t\t\t// Skip avatars.\n\t\t\tif ( image.classList.contains( 'avatar' ) ) {\n\t\t\t\treturn true;\n\t\t\t}\n\n\t\t\t// Skip images from Smush CDN with auto-resize feature.\n\t\t\tif (\n\t\t\t\t'string' === typeof image.getAttribute( 'no-resize-detection' )\n\t\t\t) {\n\t\t\t\treturn true;\n\t\t\t}\n\n\t\t\t// Skip 1x1px images.\n\t\t\tif (\n\t\t\t\timage.clientWidth === image.clientHeight &&\n\t\t\t\t1 === image.clientWidth\n\t\t\t) {\n\t\t\t\treturn true;\n\t\t\t}\n\n\t\t\t// Skip 1x1px placeholders.\n\t\t\tif (\n\t\t\t\timage.naturalWidth === image.naturalHeight &&\n\t\t\t\t1 === image.naturalWidth\n\t\t\t) {\n\t\t\t\treturn true;\n\t\t\t}\n\n\t\t\t// If width attribute is not set, do not continue.\n\t\t\treturn null === image.clientWidth || null === image.clientHeight;\n\t\t},\n\n\t\t/**\n\t\t * Get tooltip text.\n\t\t *\n\t\t * @param {Object} props\n\t\t * @return {string}  Tooltip.\n\t\t */\n\t\tgetTooltipText( props ) {\n\t\t\tlet tooltipText = '';\n\n\t\t\tif ( props.bigger_width || props.bigger_height ) {\n\t\t\t\t/** @param {string} strings.large_image */\n\t\t\t\ttooltipText = this.strings.large_image;\n\t\t\t} else if ( props.smaller_width || props.smaller_height ) {\n\t\t\t\t/** @param {string} strings.small_image */\n\t\t\t\ttooltipText = this.strings.small_image;\n\t\t\t}\n\n\t\t\treturn tooltipText\n\t\t\t\t.replace( 'width', props.real_width )\n\t\t\t\t.replace( 'height', props.real_height );\n\t\t},\n\n\t\t/**\n\t\t * Generate markup.\n\t\t *\n\t\t * @param {string} type Accepts: 'bigger' or 'smaller'.\n\t\t */\n\t\tgenerateMarkup( type ) {\n\t\t\tthis.images[ type ].forEach( ( image, index ) => {\n\t\t\t\tconst item = document.createElement( 'div' ),\n\t\t\t\t\ttooltip = this.getTooltipText( image.props );\n\n\t\t\t\titem.setAttribute(\n\t\t\t\t\t'class',\n\t\t\t\t\t'smush-resize-box smush-tooltip smush-tooltip-constrained'\n\t\t\t\t);\n\t\t\t\titem.setAttribute( 'data-tooltip', tooltip );\n\t\t\t\titem.setAttribute( 'data-image', image.class );\n\t\t\t\titem.addEventListener( 'click', ( e ) =>\n\t\t\t\t\tthis.highlightImage( e )\n\t\t\t\t);\n\n\t\t\t\titem.innerHTML = `\n\t\t\t\t\t<div class=\"smush-image-info\">\n\t\t\t\t\t\t<span>${ index + 1 }</span>\n\t\t\t\t\t\t<span class=\"smush-tag\">${ image.props.computed_width } x ${ image.props.computed_height }px</span>\n\t\t\t\t\t\t<i class=\"smush-front-icons smush-front-icon-arrows-in\" aria-hidden=\"true\">&nbsp;</i>\n\t\t\t\t\t\t<span class=\"smush-tag smush-tag-success\">${ image.props.real_width } × ${ image.props.real_height }px</span>\t\t\t\t\t\n\t\t\t\t\t</div>\n\t\t\t\t\t<div class=\"smush-image-description\">${ tooltip }</div>\n\t\t\t\t`;\n\n\t\t\t\tdocument\n\t\t\t\t\t.getElementById( 'smush-image-bar-items-' + type )\n\t\t\t\t\t.appendChild( item );\n\t\t\t} );\n\t\t},\n\n\t\t/**\n\t\t * Show/hide sections based on images.\n\t\t */\n\t\ttoggleDivs() {\n\t\t\tconst types = [ 'bigger', 'smaller' ];\n\t\t\ttypes.forEach( ( type ) => {\n\t\t\t\tconst div = document.getElementById(\n\t\t\t\t\t'smush-image-bar-items-' + type\n\t\t\t\t);\n\t\t\t\tif ( 0 === this.images[ type ].length ) {\n\t\t\t\t\tdiv.style.display = 'none';\n\t\t\t\t} else {\n\t\t\t\t\tdiv.style.display = 'block';\n\t\t\t\t}\n\t\t\t} );\n\t\t},\n\n\t\t/**\n\t\t * Scroll the selected image into view and highlight it.\n\t\t *\n\t\t * @param {Object} e\n\t\t */\n\t\thighlightImage( e ) {\n\t\t\tthis.removeSelection();\n\n\t\t\tconst el = document.getElementsByClassName(\n\t\t\t\te.currentTarget.dataset.image\n\t\t\t);\n\t\t\tif ( 'undefined' !== typeof el[ 0 ] ) {\n\t\t\t\t// Display description box.\n\t\t\t\te.currentTarget.classList.toggle( 'show-description' );\n\n\t\t\t\t// Scroll and flash image.\n\t\t\t\tel[ 0 ].scrollIntoView( {\n\t\t\t\t\tbehavior: 'smooth',\n\t\t\t\t\tblock: 'center',\n\t\t\t\t\tinline: 'nearest',\n\t\t\t\t} );\n\t\t\t\tel[ 0 ].style.opacity = '0.5';\n\t\t\t\tsetTimeout( () => {\n\t\t\t\t\tel[ 0 ].style.opacity = '1';\n\t\t\t\t}, 1000 );\n\t\t\t}\n\t\t},\n\n\t\t/**\n\t\t * Handle click on the toggle item.\n\t\t */\n\t\thandleToggleClick() {\n\t\t\tthis.bar.classList.toggle( 'closed' );\n\t\t\tthis.toggle.classList.toggle( 'closed' );\n\t\t\tthis.removeSelection();\n\t\t},\n\n\t\t/**\n\t\t * Remove selected items.\n\t\t */\n\t\tremoveSelection() {\n\t\t\tconst items = document.getElementsByClassName( 'show-description' );\n\t\t\tif ( items.length > 0 ) {\n\t\t\t\tArray.from( items ).forEach( ( div ) =>\n\t\t\t\t\tdiv.classList.remove( 'show-description' )\n\t\t\t\t);\n\t\t\t}\n\t\t},\n\n\t\t/**\n\t\t * Function to highlight all scaled images.\n\t\t *\n\t\t * Add yellow border and then show one small box to\n\t\t * resize the images as per the required size, on fly.\n\t\t */\n\t\tdetectImages() {\n\t\t\tconst images = document.getElementsByTagName( 'img' );\n\t\t\tfor ( const image of images ) {\n\t\t\t\tif ( this.shouldSkipImage( image ) ) {\n\t\t\t\t\tcontinue;\n\t\t\t\t}\n\n\t\t\t\t// Get defined width and height.\n\t\t\t\tconst props = {\n\t\t\t\t\treal_width: image.clientWidth,\n\t\t\t\t\treal_height: image.clientHeight,\n\t\t\t\t\tcomputed_width: image.naturalWidth,\n\t\t\t\t\tcomputed_height: image.naturalHeight,\n\t\t\t\t\tbigger_width: image.clientWidth * 1.5 < image.naturalWidth,\n\t\t\t\t\tbigger_height:\n\t\t\t\t\t\timage.clientHeight * 1.5 < image.naturalHeight,\n\t\t\t\t\tsmaller_width: image.clientWidth > image.naturalWidth,\n\t\t\t\t\tsmaller_height: image.clientHeight > image.naturalHeight,\n\t\t\t\t};\n\n\t\t\t\t// In case image is in correct size, do not continue.\n\t\t\t\tif (\n\t\t\t\t\t! props.bigger_width &&\n\t\t\t\t\t! props.bigger_height &&\n\t\t\t\t\t! props.smaller_width &&\n\t\t\t\t\t! props.smaller_height\n\t\t\t\t) {\n\t\t\t\t\tcontinue;\n\t\t\t\t}\n\n\t\t\t\tconst imgType =\n\t\t\t\t\t\tprops.bigger_width || props.bigger_height\n\t\t\t\t\t\t\t? 'bigger'\n\t\t\t\t\t\t\t: 'smaller',\n\t\t\t\t\timageClass =\n\t\t\t\t\t\t'smush-image-' + ( this.images[ imgType ].length + 1 );\n\n\t\t\t\t// Fill the images arrays.\n\t\t\t\tthis.images[ imgType ].push( {\n\t\t\t\t\tsrc: image,\n\t\t\t\t\tprops,\n\t\t\t\t\tclass: imageClass,\n\t\t\t\t} );\n\n\t\t\t\t/**\n\t\t\t\t * Add class to original image.\n\t\t\t\t * Can't add two classes in single add(), because no support in IE11.\n\t\t\t\t * image.classList.add('smush-detected-img', imageClass);\n\t\t\t\t */\n\t\t\t\timage.classList.add( 'smush-detected-img' );\n\t\t\t\timage.classList.add( imageClass );\n\t\t\t}\n\t\t}, // End detectImages()\n\n\t\t/**\n\t\t * Allows refreshing the list. A good way is to refresh on lazyload actions.\n\t\t *\n\t\t * @since 3.6.0\n\t\t */\n\t\trefresh() {\n\t\t\t// Clear out classes on DOM.\n\t\t\tfor ( let id in this.images.bigger ) {\n\t\t\t\tif ( this.images.bigger.hasOwnProperty( id ) ) {\n\t\t\t\t\tthis.images.bigger[ id ].src.classList.remove(\n\t\t\t\t\t\t'smush-detected-img'\n\t\t\t\t\t);\n\t\t\t\t\tthis.images.bigger[ id ].src.classList.remove(\n\t\t\t\t\t\t'smush-image-' + ++id\n\t\t\t\t\t);\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tfor ( let id in this.images.smaller ) {\n\t\t\t\tif ( this.images.smaller.hasOwnProperty( id ) ) {\n\t\t\t\t\tthis.images.smaller[ id ].src.classList.remove(\n\t\t\t\t\t\t'smush-detected-img'\n\t\t\t\t\t);\n\t\t\t\t\tthis.images.smaller[ id ].src.classList.remove(\n\t\t\t\t\t\t'smush-image-' + ++id\n\t\t\t\t\t);\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tthis.images = {\n\t\t\t\tbigger: [],\n\t\t\t\tsmaller: [],\n\t\t\t};\n\n\t\t\t// This might be overkill - there will probably never be a situation when there are less images than on\n\t\t\t// initial page load.\n\t\t\tconst elements = document.getElementsByClassName(\n\t\t\t\t'smush-resize-box'\n\t\t\t);\n\t\t\twhile ( elements.length > 0 ) {\n\t\t\t\telements[ 0 ].remove();\n\t\t\t}\n\n\t\t\tthis.process();\n\t\t},\n\t}; // End WP_Smush_IRS\n\n\t/**\n\t * After page load, initialize toggle event.\n\t */\n\twindow.addEventListener( 'DOMContentLoaded', () => SmushIRS.init() );\n\twindow.addEventListener( 'lazyloaded', () => SmushIRS.refresh() );\n}() );\n"], "names": ["SmushIRS", "bar", "document", "getElementById", "toggle", "images", "bigger", "smaller", "strings", "window", "wp_smush_resize_vars", "init", "this", "process", "addEventListener", "handleToggleClick", "bind", "icon", "querySelector", "classList", "add", "remove", "detectImages", "length", "style", "display", "generateMarkup", "toggleDivs", "shouldSkipImage", "image", "contains", "getAttribute", "clientWidth", "clientHeight", "naturalWidth", "naturalHeight", "getTooltipText", "props", "tooltipText", "bigger_width", "bigger_height", "large_image", "smaller_width", "smaller_height", "small_image", "replace", "real_width", "real_height", "type", "_this", "for<PERSON>ach", "index", "item", "createElement", "tooltip", "setAttribute", "class", "e", "highlightImage", "innerHTML", "concat", "computed_width", "computed_height", "append<PERSON><PERSON><PERSON>", "_this2", "div", "removeSelection", "el", "getElementsByClassName", "currentTarget", "dataset", "scrollIntoView", "behavior", "block", "inline", "opacity", "setTimeout", "items", "Array", "from", "_step", "_iterator", "_createForOfIteratorHelper", "getElementsByTagName", "s", "n", "done", "value", "imgType", "imageClass", "push", "src", "err", "f", "refresh", "id", "hasOwnProperty", "elements"], "sourceRoot": ""}