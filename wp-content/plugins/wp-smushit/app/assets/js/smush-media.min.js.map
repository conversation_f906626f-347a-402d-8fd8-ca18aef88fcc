{"version": 3, "file": "js/smush-media.min.js", "mappings": "CAMA,SAAWA,EAAGC,GACb,aAGA,IAAMC,EAAaC,GAAGC,MAMrBC,EAAWJ,EAAEI,SAJZ,+IAYIC,EAAkB,SAAUC,GAKjC,OAAOF,EAAS,CACfG,MAAOC,WAAWC,QAAQC,YAC1BC,MAAOL,GAET,EAEA,QACC,IAAuBL,EAAWW,WAClC,IAAuBX,EAAWW,KAAKC,WAAWC,QAAQC,UACzD,CAED,IAAMC,EACLf,EAAWW,KAAKC,WAAWC,QAAQC,UAUpCd,EAAWW,KAAKC,WAAWC,QAAQC,UAAYC,EAAoBC,OAClE,CACCC,WAAU,WACTF,EAAoBG,UAAUD,WAAWE,MAAMC,KAAMC,WACrDD,KAAKE,SAASF,KAAKG,MAAO,eAAgBH,KAAKI,OAChD,EAEAA,OAAM,WAELxB,EAAWW,KAAKC,WAAWM,UAAUM,OAAOL,MAC3CC,KACAC,WAGD,IAAMhB,EAAYe,KAAKG,MAAME,IAAI,SACjC,YAAyB,IAAdpB,IAIXe,KAAKG,MAAMG,QAMXN,KAAKO,MAAMC,SACXR,KAAKS,IACHC,KAAK,aACLC,OAAO3B,EAAgBC,IACzBe,KAAKO,MAAMH,UAbHJ,IAgBT,GAGH,CAGA,IAAMY,EAAyBhC,EAAWW,KAAKC,WAAWC,QAK1Db,EAAWW,KAAKC,WAAWC,QAAUmB,EAAuBhB,OAAO,CAClEC,WAAU,WACTe,EAAuBd,UAAUD,WAAWE,MAAMC,KAAMC,WACxDD,KAAKE,SAASF,KAAKG,MAAO,eAAgBH,KAAKI,OAChD,EAEAA,OAAM,WAELxB,EAAWW,KAAKC,WAAWM,UAAUM,OAAOL,MAAMC,KAAMC,WAExD,IAAMhB,EAAYe,KAAKG,MAAME,IAAI,SACjC,YAAyB,IAAdpB,IAIXe,KAAKG,MAAMG,QAMXN,KAAKO,MAAMC,SACXR,KAAKS,IAAIE,OAAO3B,EAAgBC,KAVxBe,IAaT,IAQD,IAAMa,EAA6BhC,GAAGC,MAAMS,KAAKuB,kBAAkBlB,OAAO,CACzEmB,GAAI,gCAEJC,cAAa,WACZhB,KAAKiB,QAAU,CACdC,IAAK,CACJC,KAAMhC,WAAWC,QAAQgC,WACzBC,MAAO,CAAEC,MAAO,OAChBC,SAAU,IAGXC,UAAW,CACVL,KAAMhC,WAAWC,QAAQqC,qBACzBJ,MAAO,CAAEC,MAAO,aAChBC,SAAU,IAGXG,SAAU,CACTP,KAAMhC,WAAWC,QAAQuC,YACzBN,MAAO,CAAEC,MAAO,YAChBC,SAAU,IAGXK,OAAQ,CACPT,KAAMhC,WAAWC,QAAQyC,cACzBR,MAAO,CAAEC,MAAO,qBAChBC,SAAU,IAGb,IAQKO,EAAqBjD,GAAGC,MAAMS,KAAKuC,mBACzCjD,GAAGC,MAAMS,KAAKuC,mBAAqBjD,GAAGC,MAAMS,KAAKuC,mBAAmBlC,OAAO,CAC1EmC,cAAa,WAEZD,EAAmBhC,UAAUiC,cAAcC,KAAKhC,MAChDA,KAAKiC,QAAQC,IACZ,6BACA,IAAIrB,EAA2B,CAC9BsB,WAAYnC,KAAKmC,WACjBhC,MAAOH,KAAKoC,WAAWf,MACvBE,UAAW,KACTnB,SAEL,GAED,CA7KD,CA6KGiC,OAAQ1D", "sources": ["webpack://wp-smushit/./_src/js/smush/media.js"], "sourcesContent": ["/* global smush_vars */\n/* global _ */\n\n/**\n * Adds a Smush Now button and displays stats in Media Attachment Details Screen\n */\n(function ($, _) {\n\t'use strict';\n\n\t// Local reference to the WordPress media namespace.\n\tconst smushMedia = wp.media,\n\t\tsharedTemplate =\n\t\t\t\"<span class='setting smush-stats' data-setting='smush'>\" +\n\t\t\t\"<span class='name'><%= label %></span>\" +\n\t\t\t\"<span class='value'><%= value %></span>\" +\n\t\t\t'</span>',\n\t\ttemplate = _.template(sharedTemplate);\n\n\t/**\n\t * Create the template.\n\t *\n\t * @param {string} smushHTML\n\t * @return {Object} Template object\n\t */\n\tconst prepareTemplate = function (smushHTML) {\n\t\t/**\n\t\t * @param {Array}  smush_vars.strings  Localization strings.\n\t\t * @param {Object} smush_vars          Object from wp_localize_script()\n\t\t */\n\t\treturn template({\n\t\t\tlabel: smush_vars.strings.stats_label,\n\t\t\tvalue: smushHTML,\n\t\t});\n\t};\n\n\tif (\n\t\t'undefined' !== typeof smushMedia.view &&\n\t\t'undefined' !== typeof smushMedia.view.Attachment.Details.TwoColumn\n\t) {\n\t\t// Local instance of the Attachment Details TwoColumn used in the edit attachment modal view\n\t\tconst smushMediaTwoColumn =\n\t\t\tsmushMedia.view.Attachment.Details.TwoColumn;\n\n\t\t/**\n\t\t * Add Smush details to attachment.\n\t\t *\n\t\t * A similar view to media.view.Attachment.Details\n\t\t * for use in the Edit Attachment modal.\n\t\t *\n\t\t * @see wp-includes/js/media-grid.js\n\t\t */\n\t\tsmushMedia.view.Attachment.Details.TwoColumn = smushMediaTwoColumn.extend(\n\t\t\t{\n\t\t\t\tinitialize() {\n\t\t\t\t\tsmushMediaTwoColumn.prototype.initialize.apply(this, arguments);\n\t\t\t\t\tthis.listenTo(this.model, 'change:smush', this.render);\n\t\t\t\t},\n\n\t\t\t\trender() {\n\t\t\t\t\t// Ensure that the main attachment fields are rendered.\n\t\t\t\t\tsmushMedia.view.Attachment.prototype.render.apply(\n\t\t\t\t\t\tthis,\n\t\t\t\t\t\targuments\n\t\t\t\t\t);\n\n\t\t\t\t\tconst smushHTML = this.model.get('smush');\n\t\t\t\t\tif (typeof smushHTML === 'undefined') {\n\t\t\t\t\t\treturn this;\n\t\t\t\t\t}\n\n\t\t\t\t\tthis.model.fetch();\n\n\t\t\t\t\t/**\n\t\t\t\t\t * Detach the views, append our custom fields, make sure that our data is fully updated\n\t\t\t\t\t * and re-render the updated view.\n\t\t\t\t\t */\n\t\t\t\t\tthis.views.detach();\n\t\t\t\t\tthis.$el\n\t\t\t\t\t\t.find('.settings')\n\t\t\t\t\t\t.append(prepareTemplate(smushHTML));\n\t\t\t\t\tthis.views.render();\n\n\t\t\t\t\treturn this;\n\t\t\t\t},\n\t\t\t}\n\t\t);\n\t}\n\n\t// Local instance of the Attachment Details TwoColumn used in the edit attachment modal view\n\tconst smushAttachmentDetails = smushMedia.view.Attachment.Details;\n\n\t/**\n\t * Add Smush details to attachment.\n\t */\n\tsmushMedia.view.Attachment.Details = smushAttachmentDetails.extend({\n\t\tinitialize() {\n\t\t\tsmushAttachmentDetails.prototype.initialize.apply(this, arguments);\n\t\t\tthis.listenTo(this.model, 'change:smush', this.render);\n\t\t},\n\n\t\trender() {\n\t\t\t// Ensure that the main attachment fields are rendered.\n\t\t\tsmushMedia.view.Attachment.prototype.render.apply(this, arguments);\n\n\t\t\tconst smushHTML = this.model.get('smush');\n\t\t\tif (typeof smushHTML === 'undefined') {\n\t\t\t\treturn this;\n\t\t\t}\n\n\t\t\tthis.model.fetch();\n\n\t\t\t/**\n\t\t\t * Detach the views, append our custom fields, make sure that our data is fully updated\n\t\t\t * and re-render the updated view.\n\t\t\t */\n\t\t\tthis.views.detach();\n\t\t\tthis.$el.append(prepareTemplate(smushHTML));\n\n\t\t\treturn this;\n\t\t},\n\t});\n\n\t/**\n\t * Create a new MediaLibraryTaxonomyFilter we later will instantiate\n\t *\n\t * @since 3.0\n\t */\n\tconst MediaLibraryTaxonomyFilter = wp.media.view.AttachmentFilters.extend({\n\t\tid: 'media-attachment-smush-filter',\n\n\t\tcreateFilters() {\n\t\t\tthis.filters = {\n\t\t\t\tall: {\n\t\t\t\t\ttext: smush_vars.strings.filter_all,\n\t\t\t\t\tprops: { stats: 'all' },\n\t\t\t\t\tpriority: 10,\n\t\t\t\t},\n\n\t\t\t\tunsmushed: {\n\t\t\t\t\ttext: smush_vars.strings.filter_not_processed,\n\t\t\t\t\tprops: { stats: 'unsmushed' },\n\t\t\t\t\tpriority: 20,\n\t\t\t\t},\n\n\t\t\t\texcluded: {\n\t\t\t\t\ttext: smush_vars.strings.filter_excl,\n\t\t\t\t\tprops: { stats: 'excluded' },\n\t\t\t\t\tpriority: 30,\n\t\t\t\t},\n\n\t\t\t\tfailed: {\n\t\t\t\t\ttext: smush_vars.strings.filter_failed,\n\t\t\t\t\tprops: { stats: 'failed_processing' },\n\t\t\t\t\tpriority: 40,\n\t\t\t\t},\n\t\t\t};\n\t\t},\n\t});\n\n\t/**\n\t * Extend and override wp.media.view.AttachmentsBrowser to include our new filter.\n\t *\n\t * @since 3.0\n\t */\n\tconst AttachmentsBrowser = wp.media.view.AttachmentsBrowser;\n\twp.media.view.AttachmentsBrowser = wp.media.view.AttachmentsBrowser.extend({\n\t\tcreateToolbar() {\n\t\t\t// Make sure to load the original toolbar\n\t\t\tAttachmentsBrowser.prototype.createToolbar.call(this);\n\t\t\tthis.toolbar.set(\n\t\t\t\t'MediaLibraryTaxonomyFilter',\n\t\t\t\tnew MediaLibraryTaxonomyFilter({\n\t\t\t\t\tcontroller: this.controller,\n\t\t\t\t\tmodel: this.collection.props,\n\t\t\t\t\tpriority: -75,\n\t\t\t\t}).render()\n\t\t\t);\n\t\t},\n\t});\n})(jQuery, _);\n"], "names": ["$", "_", "smushMedia", "wp", "media", "template", "prepareTemplate", "smushHTML", "label", "smush_vars", "strings", "stats_label", "value", "view", "Attachment", "Details", "TwoColumn", "smushMediaTwoColumn", "extend", "initialize", "prototype", "apply", "this", "arguments", "listenTo", "model", "render", "get", "fetch", "views", "detach", "$el", "find", "append", "smushAttachmentDetails", "MediaLibraryTaxonomyFilter", "AttachmentFilters", "id", "createFilters", "filters", "all", "text", "filter_all", "props", "stats", "priority", "unsmushed", "filter_not_processed", "excluded", "filter_excl", "failed", "filter_failed", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "createToolbar", "call", "toolbar", "set", "controller", "collection", "j<PERSON><PERSON><PERSON>"], "sourceRoot": ""}