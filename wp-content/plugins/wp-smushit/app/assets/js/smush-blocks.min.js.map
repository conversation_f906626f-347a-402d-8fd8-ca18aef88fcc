{"version": 3, "file": "js/smush-blocks.min.js", "mappings": "yBACA,ICEQA,EAA+BC,GAAGC,QAAlCF,2BACLG,EAAaF,GAAGG,QAAhBD,SACAE,EAAsBJ,GAAGK,YAAzBD,kBACAE,EAAcN,GAAGO,WAAjBD,UAgCI,SAASE,EAAYC,EAAIC,GAC/B,YAAK,IAAuBA,EACpBC,OAAOC,WAAWC,QAAQC,GAAGC,aACzB,iBAAoBL,EACxBA,EAIPM,MAAAC,cAAA,OACCR,GAAG,cACHS,UAAU,6CACVC,MAAQ,CAAEC,QAAS,UAEnBJ,MAAAC,cAAA,SAAOC,UAAU,yBAChBF,MAAAC,cAAA,aACCD,MAAAC,cAAA,UACCD,MAAAC,cAAA,MAAIC,UAAU,sBACXP,OAAOC,WAAWC,QAAQC,GAAGO,MAEhCL,MAAAC,cAAA,MAAIC,UAAU,sBACXP,OAAOC,WAAWC,QAAQC,GAAGQ,WAIlCN,MAAAC,cAAA,aACGM,OAAOC,KAAMd,EAAMe,OACnBC,QAAQ,SAAEC,GAAI,OAAM,EAAIjB,EAAMe,MAAOE,GAAOC,OAAO,IACnDC,KAAK,SAAEF,EAAMG,GAAC,OACdd,MAAAC,cAAA,MAAIc,IAAMD,GACTd,MAAAC,cAAA,UAAMU,EAAKK,eACXhB,MAAAC,cAAA,UAtDR,SAAwBgB,GACvB,IAAMC,EAAS,KACdC,EAAQ,CAAE,KAAM,KAAM,KAAM,MAE7B,GAAKC,KAAKC,IAAKJ,GAAUC,EACxB,OAAOD,EAAQ,KAGhB,IAAIK,GAAK,EACT,GACCL,GAASC,IACPI,QACOF,KAAKC,IAAKJ,IAAWC,GAAUI,EAAIH,EAAMI,OAAS,GAE5D,OAAON,EAAMO,QAAS,GAAM,IAAML,EAAOG,EAC1C,CAwCWG,CACD/B,EAAMe,MAAOE,GAAOM,OAChB,IAAK,KACNvB,EAAMe,MAAOE,GAAOC,QAAS,OAE9B,MAMZ,CA+BA,IAAMc,EAAoB3C,GAA4B,SAAE4C,GACvD,OAAO,SAAEC,GAER,GACC,eAAiBA,EAAMC,OACrBD,EAAME,iBACR,IAAuBF,EAAMG,WAAWtC,GAExC,OACCO,MAAAC,cAACf,EAAQ,KACRc,MAAAC,cAAC0B,EAAeC,IAKnB,IAAMI,EAAYJ,EAAMG,WAAWE,MAGnC,OAxCK,SAAqBL,GAC3B,IAAMM,EAAQ,IAAIlD,GAAGmD,IAAIC,OAAOC,MAAO,CAAE5C,GAAImC,EAAMG,WAAWtC,KAC7DuC,EAAYJ,EAAMG,WAAWE,MAE9BC,EAAMI,MAAO,CAAEC,UAAW,UAAYC,MAAM,SAAUC,GAChD,iBAAoBA,EAAIR,MAC5BL,EAAMc,cAAe,CAAET,MAAOQ,EAAIR,aAGlC,IAAuBQ,EAAIR,YACzB,IAAuBD,GACxBW,KAAKC,UAAWZ,KAAgBW,KAAKC,UAAWH,EAAIR,QAErDL,EAAMc,cAAe,CAAET,MAAOQ,EAAIR,OAEpC,GACD,CAsBEY,CAAYjB,GAGX5B,MAAAC,cAACf,EAAQ,KACRc,MAAAC,cAAC0B,EAAeC,GAChB5B,MAAAC,cAACb,EAAiB,KACjBY,MAAAC,cAACX,EAAS,CAACwD,MAAQnD,OAAOC,WAAWC,QAAQC,GAAGJ,OAC7CF,EAAYoC,EAAMG,WAAWtC,GAAIuC,KAKxC,CACD,GAAG,wBAEHhD,GAAG+D,MAAMC,UACR,mBACA,8BACAtB,E", "sources": ["webpack://wp-smushit/webpack/bootstrap", "webpack://wp-smushit/./_src/js/smush/blocks.js"], "sourcesContent": ["// The require scope\nvar __webpack_require__ = {};\n\n", "/**\n * BLOCK: extend image block\n */\nconst { create<PERSON>igherOrderComponent } = wp.compose,\n\t{ Fragment } = wp.element,\n\t{ InspectorControls } = wp.blockEditor,\n\t{ PanelBody } = wp.components;\n\n/**\n * Transform bytes to human readable format.\n *\n * @param {number} bytes\n * @return {string}  Readable size string.\n */\nfunction humanFileSize( bytes ) {\n\tconst thresh = 1024,\n\t\tunits = [ 'kB', 'MB', 'GB', 'TB' ];\n\n\tif ( Math.abs( bytes ) < thresh ) {\n\t\treturn bytes + ' B';\n\t}\n\n\tlet u = -1;\n\tdo {\n\t\tbytes /= thresh;\n\t\t++u;\n\t} while ( Math.abs( bytes ) >= thresh && u < units.length - 1 );\n\n\treturn bytes.toFixed( 1 ) + ' ' + units[ u ];\n}\n\n/**\n * Generate Smush stats table.\n *\n * @param {number} id\n * @param {Object} stats\n * @return {*}  Smush stats.\n */\nexport function smushStats( id, stats ) {\n\tif ( 'undefined' === typeof stats ) {\n\t\treturn window.smush_vars.strings.gb.select_image;\n\t} else if ( 'string' === typeof stats ) {\n\t\treturn stats;\n\t}\n\n\treturn (\n\t\t<div\n\t\t\tid=\"smush-stats\"\n\t\t\tclassName=\"sui-smush-media smush-stats-wrapper hidden\"\n\t\t\tstyle={ { display: 'block' } }\n\t\t>\n\t\t\t<table className=\"wp-smush-stats-holder\">\n\t\t\t\t<thead>\n\t\t\t\t\t<tr>\n\t\t\t\t\t\t<th className=\"smush-stats-header\">\n\t\t\t\t\t\t\t{ window.smush_vars.strings.gb.size }\n\t\t\t\t\t\t</th>\n\t\t\t\t\t\t<th className=\"smush-stats-header\">\n\t\t\t\t\t\t\t{ window.smush_vars.strings.gb.savings }\n\t\t\t\t\t\t</th>\n\t\t\t\t\t</tr>\n\t\t\t\t</thead>\n\t\t\t\t<tbody>\n\t\t\t\t\t{ Object.keys( stats.sizes )\n\t\t\t\t\t\t.filter( ( item ) => 0 < stats.sizes[ item ].percent )\n\t\t\t\t\t\t.map( ( item, i ) => (\n\t\t\t\t\t\t\t<tr key={ i }>\n\t\t\t\t\t\t\t\t<td>{ item.toUpperCase() }</td>\n\t\t\t\t\t\t\t\t<td>\n\t\t\t\t\t\t\t\t\t{ humanFileSize(\n\t\t\t\t\t\t\t\t\t\tstats.sizes[ item ].bytes\n\t\t\t\t\t\t\t\t\t) }{ ' ' }\n\t\t\t\t\t\t\t\t\t( { stats.sizes[ item ].percent }% )\n\t\t\t\t\t\t\t\t</td>\n\t\t\t\t\t\t\t</tr>\n\t\t\t\t\t\t) ) }\n\t\t\t\t</tbody>\n\t\t\t</table>\n\t\t</div>\n\t);\n}\n\n/**\n * Fetch image data. If image is Smushing, update in 3 seconds.\n *\n * TODO: this could be optimized not to query so much.\n *\n * @param {Object} props\n */\nexport function fetchProps( props ) {\n\tconst image = new wp.api.models.Media( { id: props.attributes.id } ),\n\t\tsmushData = props.attributes.smush;\n\n\timage.fetch( { attribute: 'smush' } ).done( function( img ) {\n\t\tif ( 'string' === typeof img.smush ) {\n\t\t\tprops.setAttributes( { smush: img.smush } );\n\t\t\t//setTimeout( () => fetch( props ), 3000 );\n\t\t} else if (\n\t\t\t'undefined' !== typeof img.smush &&\n\t\t\t( 'undefined' === typeof smushData ||\n\t\t\t\tJSON.stringify( smushData ) !== JSON.stringify( img.smush ) )\n\t\t) {\n\t\t\tprops.setAttributes( { smush: img.smush } );\n\t\t}\n\t} );\n}\n\n/**\n * Modify the block’s edit component.\n * Receives the original block BlockEdit component and returns a new wrapped component.\n */\nconst smushStatsControl = createHigherOrderComponent( ( BlockEdit ) => {\n\treturn ( props ) => {\n\t\t// If not image block or not selected, return unmodified block.\n\t\tif (\n\t\t\t'core/image' !== props.name ||\n\t\t\t! props.isSelected ||\n\t\t\t'undefined' === typeof props.attributes.id\n\t\t) {\n\t\t\treturn (\n\t\t\t\t<Fragment>\n\t\t\t\t\t<BlockEdit { ...props } />\n\t\t\t\t</Fragment>\n\t\t\t);\n\t\t}\n\n\t\tconst smushData = props.attributes.smush;\n\t\tfetchProps( props );\n\n\t\treturn (\n\t\t\t<Fragment>\n\t\t\t\t<BlockEdit { ...props } />\n\t\t\t\t<InspectorControls>\n\t\t\t\t\t<PanelBody title={ window.smush_vars.strings.gb.stats }>\n\t\t\t\t\t\t{ smushStats( props.attributes.id, smushData ) }\n\t\t\t\t\t</PanelBody>\n\t\t\t\t</InspectorControls>\n\t\t\t</Fragment>\n\t\t);\n\t};\n}, 'withInspectorControl' );\n\nwp.hooks.addFilter(\n\t'editor.BlockEdit',\n\t'wp-smush/smush-data-control',\n\tsmushStatsControl\n);\n"], "names": ["createHigherOrderComponent", "wp", "compose", "Fragment", "element", "InspectorCont<PERSON><PERSON>", "blockEditor", "PanelBody", "components", "smushStats", "id", "stats", "window", "smush_vars", "strings", "gb", "select_image", "React", "createElement", "className", "style", "display", "size", "savings", "Object", "keys", "sizes", "filter", "item", "percent", "map", "i", "key", "toUpperCase", "bytes", "thresh", "units", "Math", "abs", "u", "length", "toFixed", "humanFileSize", "smushStatsControl", "BlockEdit", "props", "name", "isSelected", "attributes", "smushData", "smush", "image", "api", "models", "Media", "fetch", "attribute", "done", "img", "setAttributes", "JSON", "stringify", "fetchProps", "title", "hooks", "addFilter"], "sourceRoot": ""}