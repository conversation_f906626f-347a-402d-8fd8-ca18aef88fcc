!function(){"use strict";var t,e,A;t="alpha",e=function(t){if(document.documentElement.classList.add(t?"webp":"no-webp"),!t){var e=Object.getOwnPropertyDescriptor(Element.prototype,"getAttribute");Object.defineProperty(Element.prototype,"getAttribute",{value:function(t){if(!this.dataset.smushWebpFallback)return e.value.call(this,t);var A=JSON.parse(this.dataset.smushWebpFallback);return t in A?A[t]:e.value.call(this,t)}});var A=document.querySelectorAll("[data-smush-webp-fallback]:not(.lazyload)");if(A.length){var a=["src","srcset"];A.forEach((function(t){var e=JSON.parse(t.dataset.smushWebpFallback);a.forEach((function(A){A in e&&t.setAttribute(A,e[A])})),"bg"in e&&(t.style.background=e.bg),"bg-image"in e&&(t.style.backgroundImage=e["bg-image"])}))}}},(A=new Image).onload=function(){var t=A.width>0&&A.height>0;e(t)},A.onerror=function(){e(!1)},A.src="data:image/webp;base64,"+{alpha:"UklGRkoAAABXRUJQVlA4WAoAAAAQAAAAAAAAAAAAQUxQSAwAAAARBxAR/Q9ERP8DAABWUDggGAAAABQBAJ0BKgEAAQAAAP4AAA3AAP7mtQAAAA=="}[t]}();
//# sourceMappingURL=smush-webp-fallback.min.js.map