{"version": 3, "file": "js/smush-clipboard.min.js", "mappings": "oCAMA,IAAiDA,IASxC,WACT,OAAgB,WACN,IAAIC,EAAsB,CAE9B,IACA,SAAUC,EAAyB,EAAqB,GAE9D,aAGA,EAAoBC,EAAE,EAAqB,CACzC,QAAW,WAAa,OAAqBC,CAAW,IAI1D,IAAIC,EAAe,EAAoB,KACnCC,EAAoC,EAAoBC,EAAEF,GAE1DG,EAAS,EAAoB,KAC7BC,EAA8B,EAAoBF,EAAEC,GAEpDE,EAAa,EAAoB,KACjCC,EAA8B,EAAoBJ,EAAEG,GAOxD,SAASE,EAAQC,GACf,IACE,OAAOC,SAASC,YAAYF,EAC9B,CAAE,MAAOG,GACP,OAAO,CACT,CACF,CAUA,IAMiCC,EANR,SAA4BC,GACnD,IAAIC,EAAeR,IAAiBO,GAEpC,OADAN,EAAQ,OACDO,CACT,EAuCIC,EAAiB,SAAwBC,EAAOC,GAClD,IAAIC,EA/BN,SAA2BF,GACzB,IAAIG,EAAyD,QAAjDV,SAASW,gBAAgBC,aAAa,OAC9CH,EAAcT,SAASa,cAAc,YAEzCJ,EAAYK,MAAMC,SAAW,OAE7BN,EAAYK,MAAME,OAAS,IAC3BP,EAAYK,MAAMG,QAAU,IAC5BR,EAAYK,MAAMI,OAAS,IAE3BT,EAAYK,MAAMK,SAAW,WAC7BV,EAAYK,MAAMJ,EAAQ,QAAU,QAAU,UAE9C,IAAIU,EAAYC,OAAOC,aAAetB,SAASW,gBAAgBY,UAI/D,OAHAd,EAAYK,MAAMU,IAAM,GAAGC,OAAOL,EAAW,MAC7CX,EAAYiB,aAAa,WAAY,IACrCjB,EAAYF,MAAQA,EACbE,CACT,CAaoBkB,CAAkBpB,GACpCC,EAAQoB,UAAUC,YAAYpB,GAC9B,IAAIJ,EAAeR,IAAiBY,GAGpC,OAFAX,EAAQ,QACRW,EAAYqB,SACLzB,CACT,EA4BiC0B,EAnBP,SAA6B3B,GACrD,IAAII,EAAUwB,UAAUC,OAAS,QAAsBC,IAAjBF,UAAU,GAAmBA,UAAU,GAAK,CAChFJ,UAAW5B,SAASmC,MAElB9B,EAAe,GAYnB,MAVsB,iBAAXD,EACTC,EAAeC,EAAeF,EAAQI,GAC7BJ,aAAkBgC,mBAAqB,CAAC,OAAQ,SAAU,MAAO,MAAO,YAAYC,SAASjC,aAAuC,EAASA,EAAOL,MAE7JM,EAAeC,EAAeF,EAAOG,MAAOC,IAE5CH,EAAeR,IAAiBO,GAChCN,EAAQ,SAGHO,CACT,EAIA,SAASiC,EAAQC,GAAmV,OAAtOD,EAArD,mBAAXE,QAAoD,iBAApBA,OAAOC,SAAmC,SAAiBF,GAAO,cAAcA,CAAK,EAAsB,SAAiBA,GAAO,OAAOA,GAAyB,mBAAXC,QAAyBD,EAAIG,cAAgBF,QAAUD,IAAQC,OAAOG,UAAY,gBAAkBJ,CAAK,EAAYD,EAAQC,EAAM,CAUzX,IA2CiCK,EA3CJ,WAC3B,IAAIpC,EAAUwB,UAAUC,OAAS,QAAsBC,IAAjBF,UAAU,GAAmBA,UAAU,GAAK,CAAC,EAE/Ea,EAAkBrC,EAAQsC,OAC1BA,OAA6B,IAApBD,EAA6B,OAASA,EAC/CjB,EAAYpB,EAAQoB,UACpBxB,EAASI,EAAQJ,OACjB2C,EAAOvC,EAAQuC,KAEnB,GAAe,SAAXD,GAAgC,QAAXA,EACvB,MAAM,IAAIE,MAAM,sDAIlB,QAAed,IAAX9B,EAAsB,CACxB,IAAIA,GAA8B,WAApBkC,EAAQlC,IAA4C,IAApBA,EAAO6C,SASnD,MAAM,IAAID,MAAM,+CARhB,GAAe,SAAXF,GAAqB1C,EAAO8C,aAAa,YAC3C,MAAM,IAAIF,MAAM,qFAGlB,GAAe,QAAXF,IAAqB1C,EAAO8C,aAAa,aAAe9C,EAAO8C,aAAa,aAC9E,MAAM,IAAIF,MAAM,yGAKtB,CAGA,OAAID,EACKhB,EAAagB,EAAM,CACxBnB,UAAWA,IAKXxB,EACgB,QAAX0C,EAAmB3C,EAAYC,GAAU2B,EAAa3B,EAAQ,CACnEwB,UAAWA,SAFf,CAKF,EAIA,SAASuB,EAAiBZ,GAAqW,OAAxPY,EAArD,mBAAXX,QAAoD,iBAApBA,OAAOC,SAA4C,SAAiBF,GAAO,cAAcA,CAAK,EAA+B,SAAiBA,GAAO,OAAOA,GAAyB,mBAAXC,QAAyBD,EAAIG,cAAgBF,QAAUD,IAAQC,OAAOG,UAAY,gBAAkBJ,CAAK,EAAYY,EAAiBZ,EAAM,CAI7Z,SAASa,EAAkBhD,EAAQiD,GAAS,IAAK,IAAIC,EAAI,EAAGA,EAAID,EAAMpB,OAAQqB,IAAK,CAAE,IAAIC,EAAaF,EAAMC,GAAIC,EAAWC,WAAaD,EAAWC,aAAc,EAAOD,EAAWE,cAAe,EAAU,UAAWF,IAAYA,EAAWG,UAAW,GAAMC,OAAOC,eAAexD,EAAQmD,EAAWM,IAAKN,EAAa,CAAE,CAM5T,SAASO,EAAgBC,EAAGC,GAA+G,OAA1GF,EAAkBH,OAAOM,gBAAkB,SAAyBF,EAAGC,GAAsB,OAAjBD,EAAEG,UAAYF,EAAUD,CAAG,EAAUD,EAAgBC,EAAGC,EAAI,CAEzK,SAASG,EAAaC,GAAW,IAAIC,EAMrC,WAAuC,GAAuB,oBAAZC,UAA4BA,QAAQC,UAAW,OAAO,EAAO,GAAID,QAAQC,UAAUC,KAAM,OAAO,EAAO,GAAqB,mBAAVC,MAAsB,OAAO,EAAM,IAAiF,OAA3EC,KAAK/B,UAAUgC,SAASC,KAAKN,QAAQC,UAAUG,KAAM,IAAI,WAAa,MAAY,CAAM,CAAE,MAAOG,GAAK,OAAO,CAAO,CAAE,CANlQC,GAA6B,OAAO,WAAkC,IAAsCC,EAEzIC,EAAMJ,EAFiGK,EAAQC,EAAgBd,GAAkB,GAAIC,EAA2B,CAAE,IAAIc,EAAYD,EAAgBE,MAAM1C,YAAaqC,EAAST,QAAQC,UAAUU,EAAOjD,UAAWmD,EAAY,MAASJ,EAASE,EAAMI,MAAMD,KAAMpD,WAAc,OAEhVgD,EAFkXI,OAE5WR,EAFkXG,IAElU,WAA3B5B,EAAiByB,IAAsC,mBAATA,EAE7G,SAAgCI,GAAQ,QAAa,IAATA,EAAmB,MAAM,IAAIM,eAAe,6DAAgE,OAAON,CAAM,CAFVO,CAAuBP,GAAtCJ,CAFyR,CAAG,CAQxa,SAASM,EAAgBnB,GAAwJ,OAAnJmB,EAAkBvB,OAAOM,eAAiBN,OAAO6B,eAAiB,SAAyBzB,GAAK,OAAOA,EAAEG,WAAaP,OAAO6B,eAAezB,EAAI,EAAUmB,EAAgBnB,EAAI,CAa5M,SAAS0B,EAAkBC,EAAQC,GACjC,IAAIC,EAAY,kBAAkBnE,OAAOiE,GAEzC,GAAKC,EAAQzC,aAAa0C,GAI1B,OAAOD,EAAQ/E,aAAagF,EAC9B,CAOA,IAAIC,EAAyB,SAAUC,IAxCvC,SAAmBC,EAAUC,GAAc,GAA0B,mBAAfA,GAA4C,OAAfA,EAAuB,MAAM,IAAIC,UAAU,sDAAyDF,EAASpD,UAAYgB,OAAOuC,OAAOF,GAAcA,EAAWrD,UAAW,CAAED,YAAa,CAAEnC,MAAOwF,EAAUrC,UAAU,EAAMD,cAAc,KAAeuC,GAAYlC,EAAgBiC,EAAUC,EAAa,CAyC9XG,CAAUN,EAAWC,GAErB,IA7CoBM,EAAaC,EAAYC,EA6CzCC,EAASpC,EAAa0B,GAM1B,SAASA,EAAUW,EAAShG,GAC1B,IAAIiG,EAUJ,OAlEJ,SAAyBC,EAAUN,GAAe,KAAMM,aAAoBN,GAAgB,MAAM,IAAIH,UAAU,oCAAwC,CA0DpJU,CAAgBvB,KAAMS,IAEtBY,EAAQF,EAAO3B,KAAKQ,OAEdwB,eAAepG,GAErBiG,EAAMI,YAAYL,GAEXC,CACT,CAqJA,OApNoBL,EAuEPP,EAvEoBQ,EAuET,CAAC,CACvBxC,IAAK,iBACLtD,MAAO,WACL,IAAIC,EAAUwB,UAAUC,OAAS,QAAsBC,IAAjBF,UAAU,GAAmBA,UAAU,GAAK,CAAC,EACnFoD,KAAKtC,OAAmC,mBAAnBtC,EAAQsC,OAAwBtC,EAAQsC,OAASsC,KAAK0B,cAC3E1B,KAAKhF,OAAmC,mBAAnBI,EAAQJ,OAAwBI,EAAQJ,OAASgF,KAAK2B,cAC3E3B,KAAKrC,KAA+B,mBAAjBvC,EAAQuC,KAAsBvC,EAAQuC,KAAOqC,KAAK4B,YACrE5B,KAAKxD,UAAoD,WAAxCuB,EAAiB3C,EAAQoB,WAA0BpB,EAAQoB,UAAY5B,SAASmC,IACnG,GAMC,CACD0B,IAAK,cACLtD,MAAO,SAAqBiG,GAC1B,IAAIS,EAAS7B,KAEbA,KAAK8B,SAAWvH,IAAiB6G,EAAS,SAAS,SAAU3B,GAC3D,OAAOoC,EAAOE,QAAQtC,EACxB,GACF,GAMC,CACDhB,IAAK,UACLtD,MAAO,SAAiBsE,GACtB,IAAI2B,EAAU3B,EAAEuC,gBAAkBvC,EAAEwC,cAChCvE,EAASsC,KAAKtC,OAAO0D,IAAY,OACjCzD,EAAOH,EAAgB,CACzBE,OAAQA,EACRlB,UAAWwD,KAAKxD,UAChBxB,OAAQgF,KAAKhF,OAAOoG,GACpBzD,KAAMqC,KAAKrC,KAAKyD,KAGlBpB,KAAKkC,KAAKvE,EAAO,UAAY,QAAS,CACpCD,OAAQA,EACRC,KAAMA,EACNyD,QAASA,EACTe,eAAgB,WACVf,GACFA,EAAQgB,QAGVnG,OAAOoG,eAAeC,iBACxB,GAEJ,GAMC,CACD7D,IAAK,gBACLtD,MAAO,SAAuBiG,GAC5B,OAAOf,EAAkB,SAAUe,EACrC,GAMC,CACD3C,IAAK,gBACLtD,MAAO,SAAuBiG,GAC5B,IAAImB,EAAWlC,EAAkB,SAAUe,GAE3C,GAAImB,EACF,OAAO3H,SAAS4H,cAAcD,EAElC,GAQC,CACD9D,IAAK,cAMLtD,MAAO,SAAqBiG,GAC1B,OAAOf,EAAkB,OAAQe,EACnC,GAKC,CACD3C,IAAK,UACLtD,MAAO,WACL6E,KAAK8B,SAASW,SAChB,IA7K2CvB,EA8KzC,CAAC,CACHzC,IAAK,OACLtD,MAAO,SAAcH,GACnB,IAAII,EAAUwB,UAAUC,OAAS,QAAsBC,IAAjBF,UAAU,GAAmBA,UAAU,GAAK,CAChFJ,UAAW5B,SAASmC,MAEtB,OAAOJ,EAAa3B,EAAQI,EAC9B,GAOC,CACDqD,IAAK,MACLtD,MAAO,SAAaH,GAClB,OAAOD,EAAYC,EACrB,GAOC,CACDyD,IAAK,cACLtD,MAAO,WACL,IAAIuC,EAASd,UAAUC,OAAS,QAAsBC,IAAjBF,UAAU,GAAmBA,UAAU,GAAK,CAAC,OAAQ,OACtF8F,EAA4B,iBAAXhF,EAAsB,CAACA,GAAUA,EAClDiF,IAAY/H,SAASgI,sBAIzB,OAHAF,EAAQG,SAAQ,SAAUnF,GACxBiF,EAAUA,KAAa/H,SAASgI,sBAAsBlF,EACxD,IACOiF,CACT,IAjN8D1B,GAAYjD,EAAkBgD,EAAYzD,UAAW0D,GAAiBC,GAAalD,EAAkBgD,EAAaE,GAoN3KT,CACT,CA3K6B,CA2K1BrG,KAE8BF,EAAY,CAEtC,EAED,IACA,SAAU4I,GAOhB,GAAuB,oBAAZC,UAA4BA,QAAQxF,UAAUyF,QAAS,CAC9D,IAAIC,EAAQF,QAAQxF,UAEpB0F,EAAMD,QAAUC,EAAMC,iBACND,EAAME,oBACNF,EAAMG,mBACNH,EAAMI,kBACNJ,EAAMK,qBAC1B,CAmBAR,EAAOS,QAVP,SAAkBhD,EAASgC,GACvB,KAAOhC,GAvBc,IAuBHA,EAAQ1C,UAAiC,CACvD,GAA+B,mBAApB0C,EAAQyC,SACfzC,EAAQyC,QAAQT,GAClB,OAAOhC,EAETA,EAAUA,EAAQiD,UACtB,CACJ,CAKO,EAED,IACA,SAAUV,EAAQW,EAA0B,GAElD,IAAIC,EAAU,EAAoB,KAYlC,SAASC,EAAUpD,EAASgC,EAAU5H,EAAMiJ,EAAUC,GAClD,IAAIC,EAAahC,EAAS7B,MAAMD,KAAMpD,WAItC,OAFA2D,EAAQwD,iBAAiBpJ,EAAMmJ,EAAYD,GAEpC,CACHpB,QAAS,WACLlC,EAAQyD,oBAAoBrJ,EAAMmJ,EAAYD,EAClD,EAER,CA6CA,SAAS/B,EAASvB,EAASgC,EAAU5H,EAAMiJ,GACvC,OAAO,SAASnE,GACZA,EAAEuC,eAAiB0B,EAAQjE,EAAEzE,OAAQuH,GAEjC9C,EAAEuC,gBACF4B,EAASpE,KAAKe,EAASd,EAE/B,CACJ,CAEAqD,EAAOS,QA3CP,SAAkBU,EAAU1B,EAAU5H,EAAMiJ,EAAUC,GAElD,MAAyC,mBAA9BI,EAASF,iBACTJ,EAAU1D,MAAM,KAAMrD,WAIb,mBAATjC,EAGAgJ,EAAUO,KAAK,KAAMtJ,UAAUqF,MAAM,KAAMrD,YAI9B,iBAAbqH,IACPA,EAAWrJ,SAASuJ,iBAAiBF,IAIlCG,MAAM7G,UAAU8G,IAAI7E,KAAKyE,GAAU,SAAU1D,GAChD,OAAOoD,EAAUpD,EAASgC,EAAU5H,EAAMiJ,EAAUC,EACxD,IACJ,CAwBO,EAED,IACA,SAAU7J,EAAyBuJ,GAQzCA,EAAQe,KAAO,SAASnJ,GACpB,YAAiB2B,IAAV3B,GACAA,aAAiBoJ,aACE,IAAnBpJ,EAAM0C,QACjB,EAQA0F,EAAQiB,SAAW,SAASrJ,GACxB,IAAIR,EAAO4D,OAAOhB,UAAUgC,SAASC,KAAKrE,GAE1C,YAAiB2B,IAAV3B,IACU,sBAATR,GAAyC,4BAATA,IAChC,WAAYQ,IACK,IAAjBA,EAAM0B,QAAgB0G,EAAQe,KAAKnJ,EAAM,IACrD,EAQAoI,EAAQkB,OAAS,SAAStJ,GACtB,MAAwB,iBAAVA,GACPA,aAAiBuJ,MAC5B,EAQAnB,EAAQoB,GAAK,SAASxJ,GAGlB,MAAgB,sBAFLoD,OAAOhB,UAAUgC,SAASC,KAAKrE,EAG9C,CAGO,EAED,IACA,SAAU2H,EAAQW,EAA0B,GAElD,IAAImB,EAAK,EAAoB,KACzBC,EAAW,EAAoB,KA6FnC/B,EAAOS,QAlFP,SAAgBvI,EAAQL,EAAMiJ,GAC1B,IAAK5I,IAAWL,IAASiJ,EACrB,MAAM,IAAIhG,MAAM,8BAGpB,IAAKgH,EAAGH,OAAO9J,GACX,MAAM,IAAIkG,UAAU,oCAGxB,IAAK+D,EAAGD,GAAGf,GACP,MAAM,IAAI/C,UAAU,qCAGxB,GAAI+D,EAAGN,KAAKtJ,GACR,OAsBR,SAAoBsJ,EAAM3J,EAAMiJ,GAG5B,OAFAU,EAAKP,iBAAiBpJ,EAAMiJ,GAErB,CACHnB,QAAS,WACL6B,EAAKN,oBAAoBrJ,EAAMiJ,EACnC,EAER,CA9BekB,CAAW9J,EAAQL,EAAMiJ,GAE/B,GAAIgB,EAAGJ,SAASxJ,GACjB,OAsCR,SAAwBwJ,EAAU7J,EAAMiJ,GAKpC,OAJAQ,MAAM7G,UAAUsF,QAAQrD,KAAKgF,GAAU,SAASF,GAC5CA,EAAKP,iBAAiBpJ,EAAMiJ,EAChC,IAEO,CACHnB,QAAS,WACL2B,MAAM7G,UAAUsF,QAAQrD,KAAKgF,GAAU,SAASF,GAC5CA,EAAKN,oBAAoBrJ,EAAMiJ,EACnC,GACJ,EAER,CAlDemB,CAAe/J,EAAQL,EAAMiJ,GAEnC,GAAIgB,EAAGH,OAAOzJ,GACf,OA0DR,SAAwBuH,EAAU5H,EAAMiJ,GACpC,OAAOiB,EAASjK,SAASmC,KAAMwF,EAAU5H,EAAMiJ,EACnD,CA5DeoB,CAAehK,EAAQL,EAAMiJ,GAGpC,MAAM,IAAI/C,UAAU,4EAE5B,CA4DO,EAED,IACA,SAAUiC,GA4ChBA,EAAOS,QA1CP,SAAgBhD,GACZ,IAAItF,EAEJ,GAAyB,WAArBsF,EAAQ0E,SACR1E,EAAQ6B,QAERnH,EAAesF,EAAQpF,WAEtB,GAAyB,UAArBoF,EAAQ0E,UAA6C,aAArB1E,EAAQ0E,SAAyB,CACtE,IAAIC,EAAa3E,EAAQzC,aAAa,YAEjCoH,GACD3E,EAAQjE,aAAa,WAAY,IAGrCiE,EAAQ4E,SACR5E,EAAQ6E,kBAAkB,EAAG7E,EAAQpF,MAAM0B,QAEtCqI,GACD3E,EAAQ8E,gBAAgB,YAG5BpK,EAAesF,EAAQpF,KAC3B,KACK,CACGoF,EAAQzC,aAAa,oBACrByC,EAAQ6B,QAGZ,IAAIkD,EAAYrJ,OAAOoG,eACnBkD,EAAQ3K,SAAS4K,cAErBD,EAAME,mBAAmBlF,GACzB+E,EAAUhD,kBACVgD,EAAUI,SAASH,GAEnBtK,EAAeqK,EAAU/F,UAC7B,CAEA,OAAOtE,CACX,CAKO,EAED,IACA,SAAU6H,GAEhB,SAAS6C,IAGT,CAEAA,EAAEpI,UAAY,CACZqI,GAAI,SAAUC,EAAMjC,EAAUkC,GAC5B,IAAIrG,EAAIO,KAAKP,IAAMO,KAAKP,EAAI,CAAC,GAO7B,OALCA,EAAEoG,KAAUpG,EAAEoG,GAAQ,KAAKE,KAAK,CAC/BpB,GAAIf,EACJkC,IAAKA,IAGA9F,IACT,EAEAgG,KAAM,SAAUH,EAAMjC,EAAUkC,GAC9B,IAAIlG,EAAOI,KACX,SAAS8B,IACPlC,EAAKqG,IAAIJ,EAAM/D,GACf8B,EAAS3D,MAAM6F,EAAKlJ,UACtB,CAGA,OADAkF,EAASoE,EAAItC,EACN5D,KAAK4F,GAAGC,EAAM/D,EAAUgE,EACjC,EAEA5D,KAAM,SAAU2D,GAMd,IALA,IAAIM,EAAO,GAAGC,MAAM5G,KAAK5C,UAAW,GAChCyJ,IAAWrG,KAAKP,IAAMO,KAAKP,EAAI,CAAC,IAAIoG,IAAS,IAAIO,QACjDlI,EAAI,EACJoI,EAAMD,EAAOxJ,OAETqB,EAAIoI,EAAKpI,IACfmI,EAAOnI,GAAGyG,GAAG1E,MAAMoG,EAAOnI,GAAG4H,IAAKK,GAGpC,OAAOnG,IACT,EAEAiG,IAAK,SAAUJ,EAAMjC,GACnB,IAAInE,EAAIO,KAAKP,IAAMO,KAAKP,EAAI,CAAC,GACzB8G,EAAO9G,EAAEoG,GACTW,EAAa,GAEjB,GAAID,GAAQ3C,EACV,IAAK,IAAI1F,EAAI,EAAGoI,EAAMC,EAAK1J,OAAQqB,EAAIoI,EAAKpI,IACtCqI,EAAKrI,GAAGyG,KAAOf,GAAY2C,EAAKrI,GAAGyG,GAAGuB,IAAMtC,GAC9C4C,EAAWT,KAAKQ,EAAKrI,IAY3B,OAJCsI,EAAiB,OACd/G,EAAEoG,GAAQW,SACH/G,EAAEoG,GAEN7F,IACT,GAGF8C,EAAOS,QAAUoC,EACjB7C,EAAOS,QAAQkD,YAAcd,CAGtB,GAKOe,EAA2B,CAAC,EAGhC,SAAS,EAAoBC,GAE5B,GAAGD,EAAyBC,GAC3B,OAAOD,EAAyBC,GAAUpD,QAG3C,IAAIT,EAAS4D,EAAyBC,GAAY,CAGjDpD,QAAS,CAAC,GAOX,OAHAxJ,EAAoB4M,GAAU7D,EAAQA,EAAOS,QAAS,GAG/CT,EAAOS,OACf,CAoCA,OA9BC,EAAoBlJ,EAAI,SAASyI,GAChC,IAAI8D,EAAS9D,GAAUA,EAAO+D,WAC7B,WAAa,OAAO/D,EAAgB,OAAG,EACvC,WAAa,OAAOA,CAAQ,EAE7B,OADA,EAAoB7I,EAAE2M,EAAQ,CAAEE,EAAGF,IAC5BA,CACR,EAMA,EAAoB3M,EAAI,SAASsJ,EAASwD,GACzC,IAAI,IAAItI,KAAOsI,EACX,EAAoBpI,EAAEoI,EAAYtI,KAAS,EAAoBE,EAAE4E,EAAS9E,IAC5EF,OAAOC,eAAe+E,EAAS9E,EAAK,CAAEL,YAAY,EAAM4I,IAAKD,EAAWtI,IAG3E,EAKA,EAAoBE,EAAI,SAASxB,EAAK8J,GAAQ,OAAO1I,OAAOhB,UAAU2J,eAAe1H,KAAKrC,EAAK8J,EAAO,EAOhG,EAAoB,IAC3B,CAv2BM,GAw2BfE,OACD,EAj3BErE,EAAOS,QAAUzJ,G,GCPf4M,EAA2B,CAAC,GAGhC,SAASU,EAAoBT,GAE5B,IAAIU,EAAeX,EAAyBC,GAC5C,QAAqB7J,IAAjBuK,EACH,OAAOA,EAAa9D,QAGrB,IAAIT,EAAS4D,EAAyBC,GAAY,CAGjDpD,QAAS,CAAC,GAOX,OAHAxJ,EAAoB4M,GAAUnH,KAAKsD,EAAOS,QAAST,EAAQA,EAAOS,QAAS6D,GAGpEtE,EAAOS,OACf,ECnB0B6D,CAAoB,K", "sources": ["webpack://wp-smushit/./node_modules/clipboard/dist/clipboard.js", "webpack://wp-smushit/webpack/bootstrap", "webpack://wp-smushit/webpack/startup"], "sourcesContent": ["/*!\n * clipboard.js v2.0.11\n * https://clipboardjs.com/\n *\n * Licensed MIT © Zeno Rocha\n */\n(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"ClipboardJS\"] = factory();\n\telse\n\t\troot[\"ClipboardJS\"] = factory();\n})(this, function() {\nreturn /******/ (function() { // webpackBootstrap\n/******/ \tvar __webpack_modules__ = ({\n\n/***/ 686:\n/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {\n\n\"use strict\";\n\n// EXPORTS\n__webpack_require__.d(__webpack_exports__, {\n  \"default\": function() { return /* binding */ clipboard; }\n});\n\n// EXTERNAL MODULE: ./node_modules/tiny-emitter/index.js\nvar tiny_emitter = __webpack_require__(279);\nvar tiny_emitter_default = /*#__PURE__*/__webpack_require__.n(tiny_emitter);\n// EXTERNAL MODULE: ./node_modules/good-listener/src/listen.js\nvar listen = __webpack_require__(370);\nvar listen_default = /*#__PURE__*/__webpack_require__.n(listen);\n// EXTERNAL MODULE: ./node_modules/select/src/select.js\nvar src_select = __webpack_require__(817);\nvar select_default = /*#__PURE__*/__webpack_require__.n(src_select);\n;// CONCATENATED MODULE: ./src/common/command.js\n/**\n * Executes a given operation type.\n * @param {String} type\n * @return {Boolean}\n */\nfunction command(type) {\n  try {\n    return document.execCommand(type);\n  } catch (err) {\n    return false;\n  }\n}\n;// CONCATENATED MODULE: ./src/actions/cut.js\n\n\n/**\n * Cut action wrapper.\n * @param {String|HTMLElement} target\n * @return {String}\n */\n\nvar ClipboardActionCut = function ClipboardActionCut(target) {\n  var selectedText = select_default()(target);\n  command('cut');\n  return selectedText;\n};\n\n/* harmony default export */ var actions_cut = (ClipboardActionCut);\n;// CONCATENATED MODULE: ./src/common/create-fake-element.js\n/**\n * Creates a fake textarea element with a value.\n * @param {String} value\n * @return {HTMLElement}\n */\nfunction createFakeElement(value) {\n  var isRTL = document.documentElement.getAttribute('dir') === 'rtl';\n  var fakeElement = document.createElement('textarea'); // Prevent zooming on iOS\n\n  fakeElement.style.fontSize = '12pt'; // Reset box model\n\n  fakeElement.style.border = '0';\n  fakeElement.style.padding = '0';\n  fakeElement.style.margin = '0'; // Move element out of screen horizontally\n\n  fakeElement.style.position = 'absolute';\n  fakeElement.style[isRTL ? 'right' : 'left'] = '-9999px'; // Move element to the same position vertically\n\n  var yPosition = window.pageYOffset || document.documentElement.scrollTop;\n  fakeElement.style.top = \"\".concat(yPosition, \"px\");\n  fakeElement.setAttribute('readonly', '');\n  fakeElement.value = value;\n  return fakeElement;\n}\n;// CONCATENATED MODULE: ./src/actions/copy.js\n\n\n\n/**\n * Create fake copy action wrapper using a fake element.\n * @param {String} target\n * @param {Object} options\n * @return {String}\n */\n\nvar fakeCopyAction = function fakeCopyAction(value, options) {\n  var fakeElement = createFakeElement(value);\n  options.container.appendChild(fakeElement);\n  var selectedText = select_default()(fakeElement);\n  command('copy');\n  fakeElement.remove();\n  return selectedText;\n};\n/**\n * Copy action wrapper.\n * @param {String|HTMLElement} target\n * @param {Object} options\n * @return {String}\n */\n\n\nvar ClipboardActionCopy = function ClipboardActionCopy(target) {\n  var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {\n    container: document.body\n  };\n  var selectedText = '';\n\n  if (typeof target === 'string') {\n    selectedText = fakeCopyAction(target, options);\n  } else if (target instanceof HTMLInputElement && !['text', 'search', 'url', 'tel', 'password'].includes(target === null || target === void 0 ? void 0 : target.type)) {\n    // If input type doesn't support `setSelectionRange`. Simulate it. https://developer.mozilla.org/en-US/docs/Web/API/HTMLInputElement/setSelectionRange\n    selectedText = fakeCopyAction(target.value, options);\n  } else {\n    selectedText = select_default()(target);\n    command('copy');\n  }\n\n  return selectedText;\n};\n\n/* harmony default export */ var actions_copy = (ClipboardActionCopy);\n;// CONCATENATED MODULE: ./src/actions/default.js\nfunction _typeof(obj) { \"@babel/helpers - typeof\"; if (typeof Symbol === \"function\" && typeof Symbol.iterator === \"symbol\") { _typeof = function _typeof(obj) { return typeof obj; }; } else { _typeof = function _typeof(obj) { return obj && typeof Symbol === \"function\" && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj; }; } return _typeof(obj); }\n\n\n\n/**\n * Inner function which performs selection from either `text` or `target`\n * properties and then executes copy or cut operations.\n * @param {Object} options\n */\n\nvar ClipboardActionDefault = function ClipboardActionDefault() {\n  var options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n  // Defines base properties passed from constructor.\n  var _options$action = options.action,\n      action = _options$action === void 0 ? 'copy' : _options$action,\n      container = options.container,\n      target = options.target,\n      text = options.text; // Sets the `action` to be performed which can be either 'copy' or 'cut'.\n\n  if (action !== 'copy' && action !== 'cut') {\n    throw new Error('Invalid \"action\" value, use either \"copy\" or \"cut\"');\n  } // Sets the `target` property using an element that will be have its content copied.\n\n\n  if (target !== undefined) {\n    if (target && _typeof(target) === 'object' && target.nodeType === 1) {\n      if (action === 'copy' && target.hasAttribute('disabled')) {\n        throw new Error('Invalid \"target\" attribute. Please use \"readonly\" instead of \"disabled\" attribute');\n      }\n\n      if (action === 'cut' && (target.hasAttribute('readonly') || target.hasAttribute('disabled'))) {\n        throw new Error('Invalid \"target\" attribute. You can\\'t cut text from elements with \"readonly\" or \"disabled\" attributes');\n      }\n    } else {\n      throw new Error('Invalid \"target\" value, use a valid Element');\n    }\n  } // Define selection strategy based on `text` property.\n\n\n  if (text) {\n    return actions_copy(text, {\n      container: container\n    });\n  } // Defines which selection strategy based on `target` property.\n\n\n  if (target) {\n    return action === 'cut' ? actions_cut(target) : actions_copy(target, {\n      container: container\n    });\n  }\n};\n\n/* harmony default export */ var actions_default = (ClipboardActionDefault);\n;// CONCATENATED MODULE: ./src/clipboard.js\nfunction clipboard_typeof(obj) { \"@babel/helpers - typeof\"; if (typeof Symbol === \"function\" && typeof Symbol.iterator === \"symbol\") { clipboard_typeof = function _typeof(obj) { return typeof obj; }; } else { clipboard_typeof = function _typeof(obj) { return obj && typeof Symbol === \"function\" && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj; }; } return clipboard_typeof(obj); }\n\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\nfunction _defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } }\n\nfunction _createClass(Constructor, protoProps, staticProps) { if (protoProps) _defineProperties(Constructor.prototype, protoProps); if (staticProps) _defineProperties(Constructor, staticProps); return Constructor; }\n\nfunction _inherits(subClass, superClass) { if (typeof superClass !== \"function\" && superClass !== null) { throw new TypeError(\"Super expression must either be null or a function\"); } subClass.prototype = Object.create(superClass && superClass.prototype, { constructor: { value: subClass, writable: true, configurable: true } }); if (superClass) _setPrototypeOf(subClass, superClass); }\n\nfunction _setPrototypeOf(o, p) { _setPrototypeOf = Object.setPrototypeOf || function _setPrototypeOf(o, p) { o.__proto__ = p; return o; }; return _setPrototypeOf(o, p); }\n\nfunction _createSuper(Derived) { var hasNativeReflectConstruct = _isNativeReflectConstruct(); return function _createSuperInternal() { var Super = _getPrototypeOf(Derived), result; if (hasNativeReflectConstruct) { var NewTarget = _getPrototypeOf(this).constructor; result = Reflect.construct(Super, arguments, NewTarget); } else { result = Super.apply(this, arguments); } return _possibleConstructorReturn(this, result); }; }\n\nfunction _possibleConstructorReturn(self, call) { if (call && (clipboard_typeof(call) === \"object\" || typeof call === \"function\")) { return call; } return _assertThisInitialized(self); }\n\nfunction _assertThisInitialized(self) { if (self === void 0) { throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\"); } return self; }\n\nfunction _isNativeReflectConstruct() { if (typeof Reflect === \"undefined\" || !Reflect.construct) return false; if (Reflect.construct.sham) return false; if (typeof Proxy === \"function\") return true; try { Date.prototype.toString.call(Reflect.construct(Date, [], function () {})); return true; } catch (e) { return false; } }\n\nfunction _getPrototypeOf(o) { _getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf : function _getPrototypeOf(o) { return o.__proto__ || Object.getPrototypeOf(o); }; return _getPrototypeOf(o); }\n\n\n\n\n\n\n/**\n * Helper function to retrieve attribute value.\n * @param {String} suffix\n * @param {Element} element\n */\n\nfunction getAttributeValue(suffix, element) {\n  var attribute = \"data-clipboard-\".concat(suffix);\n\n  if (!element.hasAttribute(attribute)) {\n    return;\n  }\n\n  return element.getAttribute(attribute);\n}\n/**\n * Base class which takes one or more elements, adds event listeners to them,\n * and instantiates a new `ClipboardAction` on each click.\n */\n\n\nvar Clipboard = /*#__PURE__*/function (_Emitter) {\n  _inherits(Clipboard, _Emitter);\n\n  var _super = _createSuper(Clipboard);\n\n  /**\n   * @param {String|HTMLElement|HTMLCollection|NodeList} trigger\n   * @param {Object} options\n   */\n  function Clipboard(trigger, options) {\n    var _this;\n\n    _classCallCheck(this, Clipboard);\n\n    _this = _super.call(this);\n\n    _this.resolveOptions(options);\n\n    _this.listenClick(trigger);\n\n    return _this;\n  }\n  /**\n   * Defines if attributes would be resolved using internal setter functions\n   * or custom functions that were passed in the constructor.\n   * @param {Object} options\n   */\n\n\n  _createClass(Clipboard, [{\n    key: \"resolveOptions\",\n    value: function resolveOptions() {\n      var options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n      this.action = typeof options.action === 'function' ? options.action : this.defaultAction;\n      this.target = typeof options.target === 'function' ? options.target : this.defaultTarget;\n      this.text = typeof options.text === 'function' ? options.text : this.defaultText;\n      this.container = clipboard_typeof(options.container) === 'object' ? options.container : document.body;\n    }\n    /**\n     * Adds a click event listener to the passed trigger.\n     * @param {String|HTMLElement|HTMLCollection|NodeList} trigger\n     */\n\n  }, {\n    key: \"listenClick\",\n    value: function listenClick(trigger) {\n      var _this2 = this;\n\n      this.listener = listen_default()(trigger, 'click', function (e) {\n        return _this2.onClick(e);\n      });\n    }\n    /**\n     * Defines a new `ClipboardAction` on each click event.\n     * @param {Event} e\n     */\n\n  }, {\n    key: \"onClick\",\n    value: function onClick(e) {\n      var trigger = e.delegateTarget || e.currentTarget;\n      var action = this.action(trigger) || 'copy';\n      var text = actions_default({\n        action: action,\n        container: this.container,\n        target: this.target(trigger),\n        text: this.text(trigger)\n      }); // Fires an event based on the copy operation result.\n\n      this.emit(text ? 'success' : 'error', {\n        action: action,\n        text: text,\n        trigger: trigger,\n        clearSelection: function clearSelection() {\n          if (trigger) {\n            trigger.focus();\n          }\n\n          window.getSelection().removeAllRanges();\n        }\n      });\n    }\n    /**\n     * Default `action` lookup function.\n     * @param {Element} trigger\n     */\n\n  }, {\n    key: \"defaultAction\",\n    value: function defaultAction(trigger) {\n      return getAttributeValue('action', trigger);\n    }\n    /**\n     * Default `target` lookup function.\n     * @param {Element} trigger\n     */\n\n  }, {\n    key: \"defaultTarget\",\n    value: function defaultTarget(trigger) {\n      var selector = getAttributeValue('target', trigger);\n\n      if (selector) {\n        return document.querySelector(selector);\n      }\n    }\n    /**\n     * Allow fire programmatically a copy action\n     * @param {String|HTMLElement} target\n     * @param {Object} options\n     * @returns Text copied.\n     */\n\n  }, {\n    key: \"defaultText\",\n\n    /**\n     * Default `text` lookup function.\n     * @param {Element} trigger\n     */\n    value: function defaultText(trigger) {\n      return getAttributeValue('text', trigger);\n    }\n    /**\n     * Destroy lifecycle.\n     */\n\n  }, {\n    key: \"destroy\",\n    value: function destroy() {\n      this.listener.destroy();\n    }\n  }], [{\n    key: \"copy\",\n    value: function copy(target) {\n      var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {\n        container: document.body\n      };\n      return actions_copy(target, options);\n    }\n    /**\n     * Allow fire programmatically a cut action\n     * @param {String|HTMLElement} target\n     * @returns Text cutted.\n     */\n\n  }, {\n    key: \"cut\",\n    value: function cut(target) {\n      return actions_cut(target);\n    }\n    /**\n     * Returns the support of the given action, or all actions if no action is\n     * given.\n     * @param {String} [action]\n     */\n\n  }, {\n    key: \"isSupported\",\n    value: function isSupported() {\n      var action = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : ['copy', 'cut'];\n      var actions = typeof action === 'string' ? [action] : action;\n      var support = !!document.queryCommandSupported;\n      actions.forEach(function (action) {\n        support = support && !!document.queryCommandSupported(action);\n      });\n      return support;\n    }\n  }]);\n\n  return Clipboard;\n}((tiny_emitter_default()));\n\n/* harmony default export */ var clipboard = (Clipboard);\n\n/***/ }),\n\n/***/ 828:\n/***/ (function(module) {\n\nvar DOCUMENT_NODE_TYPE = 9;\n\n/**\n * A polyfill for Element.matches()\n */\nif (typeof Element !== 'undefined' && !Element.prototype.matches) {\n    var proto = Element.prototype;\n\n    proto.matches = proto.matchesSelector ||\n                    proto.mozMatchesSelector ||\n                    proto.msMatchesSelector ||\n                    proto.oMatchesSelector ||\n                    proto.webkitMatchesSelector;\n}\n\n/**\n * Finds the closest parent that matches a selector.\n *\n * @param {Element} element\n * @param {String} selector\n * @return {Function}\n */\nfunction closest (element, selector) {\n    while (element && element.nodeType !== DOCUMENT_NODE_TYPE) {\n        if (typeof element.matches === 'function' &&\n            element.matches(selector)) {\n          return element;\n        }\n        element = element.parentNode;\n    }\n}\n\nmodule.exports = closest;\n\n\n/***/ }),\n\n/***/ 438:\n/***/ (function(module, __unused_webpack_exports, __webpack_require__) {\n\nvar closest = __webpack_require__(828);\n\n/**\n * Delegates event to a selector.\n *\n * @param {Element} element\n * @param {String} selector\n * @param {String} type\n * @param {Function} callback\n * @param {Boolean} useCapture\n * @return {Object}\n */\nfunction _delegate(element, selector, type, callback, useCapture) {\n    var listenerFn = listener.apply(this, arguments);\n\n    element.addEventListener(type, listenerFn, useCapture);\n\n    return {\n        destroy: function() {\n            element.removeEventListener(type, listenerFn, useCapture);\n        }\n    }\n}\n\n/**\n * Delegates event to a selector.\n *\n * @param {Element|String|Array} [elements]\n * @param {String} selector\n * @param {String} type\n * @param {Function} callback\n * @param {Boolean} useCapture\n * @return {Object}\n */\nfunction delegate(elements, selector, type, callback, useCapture) {\n    // Handle the regular Element usage\n    if (typeof elements.addEventListener === 'function') {\n        return _delegate.apply(null, arguments);\n    }\n\n    // Handle Element-less usage, it defaults to global delegation\n    if (typeof type === 'function') {\n        // Use `document` as the first parameter, then apply arguments\n        // This is a short way to .unshift `arguments` without running into deoptimizations\n        return _delegate.bind(null, document).apply(null, arguments);\n    }\n\n    // Handle Selector-based usage\n    if (typeof elements === 'string') {\n        elements = document.querySelectorAll(elements);\n    }\n\n    // Handle Array-like based usage\n    return Array.prototype.map.call(elements, function (element) {\n        return _delegate(element, selector, type, callback, useCapture);\n    });\n}\n\n/**\n * Finds closest match and invokes callback.\n *\n * @param {Element} element\n * @param {String} selector\n * @param {String} type\n * @param {Function} callback\n * @return {Function}\n */\nfunction listener(element, selector, type, callback) {\n    return function(e) {\n        e.delegateTarget = closest(e.target, selector);\n\n        if (e.delegateTarget) {\n            callback.call(element, e);\n        }\n    }\n}\n\nmodule.exports = delegate;\n\n\n/***/ }),\n\n/***/ 879:\n/***/ (function(__unused_webpack_module, exports) {\n\n/**\n * Check if argument is a HTML element.\n *\n * @param {Object} value\n * @return {Boolean}\n */\nexports.node = function(value) {\n    return value !== undefined\n        && value instanceof HTMLElement\n        && value.nodeType === 1;\n};\n\n/**\n * Check if argument is a list of HTML elements.\n *\n * @param {Object} value\n * @return {Boolean}\n */\nexports.nodeList = function(value) {\n    var type = Object.prototype.toString.call(value);\n\n    return value !== undefined\n        && (type === '[object NodeList]' || type === '[object HTMLCollection]')\n        && ('length' in value)\n        && (value.length === 0 || exports.node(value[0]));\n};\n\n/**\n * Check if argument is a string.\n *\n * @param {Object} value\n * @return {Boolean}\n */\nexports.string = function(value) {\n    return typeof value === 'string'\n        || value instanceof String;\n};\n\n/**\n * Check if argument is a function.\n *\n * @param {Object} value\n * @return {Boolean}\n */\nexports.fn = function(value) {\n    var type = Object.prototype.toString.call(value);\n\n    return type === '[object Function]';\n};\n\n\n/***/ }),\n\n/***/ 370:\n/***/ (function(module, __unused_webpack_exports, __webpack_require__) {\n\nvar is = __webpack_require__(879);\nvar delegate = __webpack_require__(438);\n\n/**\n * Validates all params and calls the right\n * listener function based on its target type.\n *\n * @param {String|HTMLElement|HTMLCollection|NodeList} target\n * @param {String} type\n * @param {Function} callback\n * @return {Object}\n */\nfunction listen(target, type, callback) {\n    if (!target && !type && !callback) {\n        throw new Error('Missing required arguments');\n    }\n\n    if (!is.string(type)) {\n        throw new TypeError('Second argument must be a String');\n    }\n\n    if (!is.fn(callback)) {\n        throw new TypeError('Third argument must be a Function');\n    }\n\n    if (is.node(target)) {\n        return listenNode(target, type, callback);\n    }\n    else if (is.nodeList(target)) {\n        return listenNodeList(target, type, callback);\n    }\n    else if (is.string(target)) {\n        return listenSelector(target, type, callback);\n    }\n    else {\n        throw new TypeError('First argument must be a String, HTMLElement, HTMLCollection, or NodeList');\n    }\n}\n\n/**\n * Adds an event listener to a HTML element\n * and returns a remove listener function.\n *\n * @param {HTMLElement} node\n * @param {String} type\n * @param {Function} callback\n * @return {Object}\n */\nfunction listenNode(node, type, callback) {\n    node.addEventListener(type, callback);\n\n    return {\n        destroy: function() {\n            node.removeEventListener(type, callback);\n        }\n    }\n}\n\n/**\n * Add an event listener to a list of HTML elements\n * and returns a remove listener function.\n *\n * @param {NodeList|HTMLCollection} nodeList\n * @param {String} type\n * @param {Function} callback\n * @return {Object}\n */\nfunction listenNodeList(nodeList, type, callback) {\n    Array.prototype.forEach.call(nodeList, function(node) {\n        node.addEventListener(type, callback);\n    });\n\n    return {\n        destroy: function() {\n            Array.prototype.forEach.call(nodeList, function(node) {\n                node.removeEventListener(type, callback);\n            });\n        }\n    }\n}\n\n/**\n * Add an event listener to a selector\n * and returns a remove listener function.\n *\n * @param {String} selector\n * @param {String} type\n * @param {Function} callback\n * @return {Object}\n */\nfunction listenSelector(selector, type, callback) {\n    return delegate(document.body, selector, type, callback);\n}\n\nmodule.exports = listen;\n\n\n/***/ }),\n\n/***/ 817:\n/***/ (function(module) {\n\nfunction select(element) {\n    var selectedText;\n\n    if (element.nodeName === 'SELECT') {\n        element.focus();\n\n        selectedText = element.value;\n    }\n    else if (element.nodeName === 'INPUT' || element.nodeName === 'TEXTAREA') {\n        var isReadOnly = element.hasAttribute('readonly');\n\n        if (!isReadOnly) {\n            element.setAttribute('readonly', '');\n        }\n\n        element.select();\n        element.setSelectionRange(0, element.value.length);\n\n        if (!isReadOnly) {\n            element.removeAttribute('readonly');\n        }\n\n        selectedText = element.value;\n    }\n    else {\n        if (element.hasAttribute('contenteditable')) {\n            element.focus();\n        }\n\n        var selection = window.getSelection();\n        var range = document.createRange();\n\n        range.selectNodeContents(element);\n        selection.removeAllRanges();\n        selection.addRange(range);\n\n        selectedText = selection.toString();\n    }\n\n    return selectedText;\n}\n\nmodule.exports = select;\n\n\n/***/ }),\n\n/***/ 279:\n/***/ (function(module) {\n\nfunction E () {\n  // Keep this empty so it's easier to inherit from\n  // (via https://github.com/lipsmack from https://github.com/scottcorgan/tiny-emitter/issues/3)\n}\n\nE.prototype = {\n  on: function (name, callback, ctx) {\n    var e = this.e || (this.e = {});\n\n    (e[name] || (e[name] = [])).push({\n      fn: callback,\n      ctx: ctx\n    });\n\n    return this;\n  },\n\n  once: function (name, callback, ctx) {\n    var self = this;\n    function listener () {\n      self.off(name, listener);\n      callback.apply(ctx, arguments);\n    };\n\n    listener._ = callback\n    return this.on(name, listener, ctx);\n  },\n\n  emit: function (name) {\n    var data = [].slice.call(arguments, 1);\n    var evtArr = ((this.e || (this.e = {}))[name] || []).slice();\n    var i = 0;\n    var len = evtArr.length;\n\n    for (i; i < len; i++) {\n      evtArr[i].fn.apply(evtArr[i].ctx, data);\n    }\n\n    return this;\n  },\n\n  off: function (name, callback) {\n    var e = this.e || (this.e = {});\n    var evts = e[name];\n    var liveEvents = [];\n\n    if (evts && callback) {\n      for (var i = 0, len = evts.length; i < len; i++) {\n        if (evts[i].fn !== callback && evts[i].fn._ !== callback)\n          liveEvents.push(evts[i]);\n      }\n    }\n\n    // Remove event from queue to prevent memory leak\n    // Suggested by https://github.com/lazd\n    // Ref: https://github.com/scottcorgan/tiny-emitter/commit/c6ebfaa9bc973b33d110a84a307742b7cf94c953#commitcomment-5024910\n\n    (liveEvents.length)\n      ? e[name] = liveEvents\n      : delete e[name];\n\n    return this;\n  }\n};\n\nmodule.exports = E;\nmodule.exports.TinyEmitter = E;\n\n\n/***/ })\n\n/******/ \t});\n/************************************************************************/\n/******/ \t// The module cache\n/******/ \tvar __webpack_module_cache__ = {};\n/******/ \t\n/******/ \t// The require function\n/******/ \tfunction __webpack_require__(moduleId) {\n/******/ \t\t// Check if module is in cache\n/******/ \t\tif(__webpack_module_cache__[moduleId]) {\n/******/ \t\t\treturn __webpack_module_cache__[moduleId].exports;\n/******/ \t\t}\n/******/ \t\t// Create a new module (and put it into the cache)\n/******/ \t\tvar module = __webpack_module_cache__[moduleId] = {\n/******/ \t\t\t// no module.id needed\n/******/ \t\t\t// no module.loaded needed\n/******/ \t\t\texports: {}\n/******/ \t\t};\n/******/ \t\n/******/ \t\t// Execute the module function\n/******/ \t\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n/******/ \t\n/******/ \t\t// Return the exports of the module\n/******/ \t\treturn module.exports;\n/******/ \t}\n/******/ \t\n/************************************************************************/\n/******/ \t/* webpack/runtime/compat get default export */\n/******/ \t!function() {\n/******/ \t\t// getDefaultExport function for compatibility with non-harmony modules\n/******/ \t\t__webpack_require__.n = function(module) {\n/******/ \t\t\tvar getter = module && module.__esModule ?\n/******/ \t\t\t\tfunction() { return module['default']; } :\n/******/ \t\t\t\tfunction() { return module; };\n/******/ \t\t\t__webpack_require__.d(getter, { a: getter });\n/******/ \t\t\treturn getter;\n/******/ \t\t};\n/******/ \t}();\n/******/ \t\n/******/ \t/* webpack/runtime/define property getters */\n/******/ \t!function() {\n/******/ \t\t// define getter functions for harmony exports\n/******/ \t\t__webpack_require__.d = function(exports, definition) {\n/******/ \t\t\tfor(var key in definition) {\n/******/ \t\t\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n/******/ \t\t\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n/******/ \t\t\t\t}\n/******/ \t\t\t}\n/******/ \t\t};\n/******/ \t}();\n/******/ \t\n/******/ \t/* webpack/runtime/hasOwnProperty shorthand */\n/******/ \t!function() {\n/******/ \t\t__webpack_require__.o = function(obj, prop) { return Object.prototype.hasOwnProperty.call(obj, prop); }\n/******/ \t}();\n/******/ \t\n/************************************************************************/\n/******/ \t// module exports must be returned from runtime so entry inlining is disabled\n/******/ \t// startup\n/******/ \t// Load entry module and return exports\n/******/ \treturn __webpack_require__(686);\n/******/ })()\n.default;\n});", "// The module cache\nvar __webpack_module_cache__ = {};\n\n// The require function\nfunction __webpack_require__(moduleId) {\n\t// Check if module is in cache\n\tvar cachedModule = __webpack_module_cache__[moduleId];\n\tif (cachedModule !== undefined) {\n\t\treturn cachedModule.exports;\n\t}\n\t// Create a new module (and put it into the cache)\n\tvar module = __webpack_module_cache__[moduleId] = {\n\t\t// no module.id needed\n\t\t// no module.loaded needed\n\t\texports: {}\n\t};\n\n\t// Execute the module function\n\t__webpack_modules__[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n\n\t// Return the exports of the module\n\treturn module.exports;\n}\n\n", "// startup\n// Load entry module and return exports\n// This entry module is referenced by other modules so it can't be inlined\nvar __webpack_exports__ = __webpack_require__(7576);\n"], "names": ["factory", "__webpack_modules__", "__unused_webpack_module", "d", "clipboard", "tiny_emitter", "tiny_emitter_default", "n", "listen", "listen_default", "src_select", "select_default", "command", "type", "document", "execCommand", "err", "actions_cut", "target", "selectedText", "fakeCopyAction", "value", "options", "fakeElement", "isRTL", "documentElement", "getAttribute", "createElement", "style", "fontSize", "border", "padding", "margin", "position", "yPosition", "window", "pageYOffset", "scrollTop", "top", "concat", "setAttribute", "createFakeElement", "container", "append<PERSON><PERSON><PERSON>", "remove", "actions_copy", "arguments", "length", "undefined", "body", "HTMLInputElement", "includes", "_typeof", "obj", "Symbol", "iterator", "constructor", "prototype", "actions_default", "_options$action", "action", "text", "Error", "nodeType", "hasAttribute", "clipboard_typeof", "_defineProperties", "props", "i", "descriptor", "enumerable", "configurable", "writable", "Object", "defineProperty", "key", "_setPrototypeOf", "o", "p", "setPrototypeOf", "__proto__", "_createSuper", "Derived", "hasNativeReflectConstruct", "Reflect", "construct", "sham", "Proxy", "Date", "toString", "call", "e", "_isNativeReflectConstruct", "result", "self", "Super", "_getPrototypeOf", "<PERSON><PERSON><PERSON><PERSON>", "this", "apply", "ReferenceError", "_assertThisInitialized", "getPrototypeOf", "getAttributeValue", "suffix", "element", "attribute", "Clipboard", "_Emitter", "subClass", "superClass", "TypeError", "create", "_inherits", "<PERSON><PERSON><PERSON><PERSON>", "protoProps", "staticProps", "_super", "trigger", "_this", "instance", "_classCallCheck", "resolveOptions", "listenClick", "defaultAction", "defaultTarget", "defaultText", "_this2", "listener", "onClick", "<PERSON><PERSON><PERSON><PERSON>", "currentTarget", "emit", "clearSelection", "focus", "getSelection", "removeAllRanges", "selector", "querySelector", "destroy", "actions", "support", "queryCommandSupported", "for<PERSON>ach", "module", "Element", "matches", "proto", "matchesSelector", "mozMatchesSelector", "msMatchesSelector", "oMatchesSelector", "webkitMatchesSelector", "exports", "parentNode", "__unused_webpack_exports", "closest", "_delegate", "callback", "useCapture", "listenerFn", "addEventListener", "removeEventListener", "elements", "bind", "querySelectorAll", "Array", "map", "node", "HTMLElement", "nodeList", "string", "String", "fn", "is", "delegate", "listenNode", "listenNodeList", "listenSelector", "nodeName", "isReadOnly", "select", "setSelectionRange", "removeAttribute", "selection", "range", "createRange", "selectNodeContents", "addRange", "E", "on", "name", "ctx", "push", "once", "off", "_", "data", "slice", "evtArr", "len", "evts", "liveEvents", "TinyEmitter", "__webpack_module_cache__", "moduleId", "getter", "__esModule", "a", "definition", "get", "prop", "hasOwnProperty", "default", "__webpack_require__", "cachedModule"], "sourceRoot": ""}