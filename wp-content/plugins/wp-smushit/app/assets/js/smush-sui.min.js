!function(){var e={3566:function(){function e(t){return e="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},e(t)}!function(e,t,i,n,s){"use strict";var o="SUICodeSnippet",r={copyText:"Copy",copiedText:"Copied!"};function a(t,i){this.element=t,this.$element=e(this.element),this.settings=e.extend({},r,i),this._defaults=r,this._name=o,this._clipboardJs=null,this._clipboardId="",this.init()}e.extend(a.prototype,{init:function(){var i=this,n="";0===this.$element.parent("sui-code-snippet-wrapper").length&&(this.$element.wrap('<div class="sui-code-snippet-wrapper"></div>'),this._clipboardId=this.generateUniqueId(),n='<button type="button" class="sui-button" id="sui-code-snippet-button-'+this._clipboardId+'" data-clipboard-target="#sui-code-snippet-'+this._clipboardId+'">'+this.settings.copyText+"</button>",this.$element.attr("id","sui-code-snippet-"+this._clipboardId).after(n),this._clipboardJs=new t("#sui-code-snippet-button-"+this._clipboardId),this._clipboardJs.on("success",(function(e){e.clearSelection(),i.showTooltip(e.trigger,i.settings.copiedText)})),e("#sui-code-snippet-button-"+this._clipboardId).on("mouseleave.SUICodeSnippet",(function(){e(this).removeClass("sui-tooltip"),e(this).removeAttr("aria-label"),e(this).removeAttr("data-tooltip")})))},getClipboardJs:function(){return this._clipboardJs},showTooltip:function(t,i){e(t).addClass("sui-tooltip"),e(t).attr("aria-label",i),e(t).attr("data-tooltip",i)},generateUniqueId:function(){return"_"+Math.random().toString(36).substr(2,9)},destroy:function(){null!==this._clipboardJs&&(this._clipboardJs.destroy(),this.$element.attr("id",""),this.$element.unwrap(".sui-code-snippet-wrapper"),e("#sui-code-snippet-button-"+this._clipboardId).remove())}}),e.fn[o]=function(t){return this.each((function(){e.data(this,o)||e.data(this,o,new a(this,t))}))}}(jQuery,ClipboardJS,window,document),function(t){"use strict";"object"!==e(window.SUI)&&(window.SUI={}),SUI.suiCodeSnippet=function(){t(".sui-2-12-23 .sui-code-snippet:not(.sui-no-copy)").each((function(){t(this).SUICodeSnippet({})}))},t(document).ready((function(){SUI.suiCodeSnippet()}))}(jQuery)},2579:function(){function e(t){return e="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},e(t)}!function(){"use strict";"object"!==e(window.SUI)&&(window.SUI={});var t=t||{};t.KeyCode={BACKSPACE:8,TAB:9,RETURN:13,ESC:27,SPACE:32,PAGE_UP:33,PAGE_DOWN:34,END:35,HOME:36,LEFT:37,UP:38,RIGHT:39,DOWN:40,DELETE:46},t.Utils=t.Utils||{},t.Utils.remove=function(e){return e.remove&&"function"==typeof e.remove?e.remove():!(!e.parentNode||!e.parentNode.removeChild||"function"!=typeof e.parentNode.removeChild)&&e.parentNode.removeChild(e)},t.Utils.isFocusable=function(e){if(0<e.tabIndex||0===e.tabIndex&&null!==e.getAttribute("tabIndex"))return!0;if(e.disabled)return!1;switch(e.nodeName){case"A":return!!e.href&&"ignore"!=e.rel;case"INPUT":return"hidden"!=e.type&&"file"!=e.type;case"BUTTON":case"SELECT":case"TEXTAREA":return!0;default:return!1}},t.Utils.simulateClick=function(e){var t=new MouseEvent("click",{bubbles:!0,cancelable:!0,view:window});e.dispatchEvent(t)},t.Utils.IgnoreUtilFocusChanges=!1,t.Utils.dialogOpenClass="sui-has-modal",t.Utils.focusFirstDescendant=function(e){for(var i=0;i<e.childNodes.length;i++){var n=e.childNodes[i];if(t.Utils.attemptFocus(n)||t.Utils.focusFirstDescendant(n))return!0}return!1},t.Utils.focusLastDescendant=function(e){for(var i=e.childNodes.length-1;0<=i;i--){var n=e.childNodes[i];if(t.Utils.attemptFocus(n)||t.Utils.focusLastDescendant(n))return!0}return!1},t.Utils.attemptFocus=function(e){if(!t.Utils.isFocusable(e))return!1;t.Utils.IgnoreUtilFocusChanges=!0;try{e.focus()}catch(e){}return t.Utils.IgnoreUtilFocusChanges=!1,document.activeElement===e},t.OpenDialogList=t.OpenDialogList||new Array(0),t.getCurrentDialog=function(){if(t.OpenDialogList&&t.OpenDialogList.length)return t.OpenDialogList[t.OpenDialogList.length-1]},t.closeCurrentDialog=function(){var e=t.getCurrentDialog();return!!e&&(e.close(),!0)},t.handleEscape=function(e){(e.which||e.keyCode)===t.KeyCode.ESC&&t.closeCurrentDialog()&&e.stopPropagation()},t.Dialog=function(i,n,s,o){var r=!(arguments.length>4&&void 0!==arguments[4])||arguments[4],a=!(arguments.length>5&&void 0!==arguments[5])||arguments[5];if(this.dialogNode=document.getElementById(i),null===this.dialogNode)throw new Error('No element found with id="'+i+'".');var l=["dialog","alertdialog"];if(!(this.dialogNode.getAttribute("role")||"").trim().split(/\s+/g).some((function(e){return l.some((function(t){return e===t}))})))throw new Error("Dialog() requires a DOM element with ARIA role of dialog or alertdialog.");this.isCloseOnEsc=r;var c=new Event("open");this.dialogNode.dispatchEvent(c);var u="sui-modal";if(this.dialogNode.parentNode.classList.contains(u)?this.backdropNode=this.dialogNode.parentNode:(this.backdropNode=document.createElement("div"),this.backdropNode.className=u,this.backdropNode.setAttribute("data-markup","new"),this.dialogNode.parentNode.insertBefore(this.backdropNode,this.dialogNodev),this.backdropNode.appendChild(this.dialogNode)),this.backdropNode.classList.add("sui-active"),document.body.parentNode.classList.add(t.Utils.dialogOpenClass),"string"==typeof n)this.focusAfterClosed=document.getElementById(n);else{if("object"!==e(n))throw new Error("the focusAfterClosed parameter is required for the aria.Dialog constructor.");this.focusAfterClosed=n}"string"==typeof s?this.focusFirst=document.getElementById(s):"object"===e(s)?this.focusFirst=s:this.focusFirst=null;var d=document.createElement("div");this.preNode=this.dialogNode.parentNode.insertBefore(d,this.dialogNode),this.preNode.tabIndex=0,"boolean"==typeof o&&!0===o&&(this.preNode.classList.add("sui-modal-overlay"),this.preNode.onclick=function(){t.getCurrentDialog().close()});var p=document.createElement("div");this.postNode=this.dialogNode.parentNode.insertBefore(p,this.dialogNode.nextSibling),this.postNode.tabIndex=0,0<t.OpenDialogList.length&&t.getCurrentDialog().removeListeners(),this.addListeners(),t.OpenDialogList.push(this),a?(this.dialogNode.classList.add("sui-content-fade-in"),this.dialogNode.classList.remove("sui-content-fade-out")):(this.dialogNode.classList.remove("sui-content-fade-in"),this.dialogNode.classList.remove("sui-content-fade-out")),this.focusFirst?this.focusFirst.focus():t.Utils.focusFirstDescendant(this.dialogNode),this.lastFocus=document.activeElement;var h=new Event("afterOpen");this.dialogNode.dispatchEvent(h)},t.Dialog.prototype.close=function(){var e=!(arguments.length>0&&void 0!==arguments[0])||arguments[0],i=this,n=new Event("close");this.dialogNode.dispatchEvent(n),t.OpenDialogList.pop(),this.removeListeners(),this.preNode.parentNode.removeChild(this.preNode),this.postNode.parentNode.removeChild(this.postNode),e?(this.dialogNode.classList.add("sui-content-fade-out"),this.dialogNode.classList.remove("sui-content-fade-in")):(this.dialogNode.classList.remove("sui-content-fade-in"),this.dialogNode.classList.remove("sui-content-fade-out")),this.focusAfterClosed.focus(),setTimeout((function(){i.backdropNode.classList.remove("sui-active")}),300),setTimeout((function(){var e=i.dialogNode.querySelectorAll(".sui-modal-slide");if(0<e.length){for(var t=0;t<e.length;t++)e[t].setAttribute("disabled",!0),e[t].classList.remove("sui-loaded"),e[t].classList.remove("sui-active"),e[t].setAttribute("tabindex","-1"),e[t].setAttribute("aria-hidden",!0);if(e[0].hasAttribute("data-modal-size")){var n=e[0].getAttribute("data-modal-size");switch(n){case"sm":case"small":n="sm";break;case"md":case"med":case"medium":n="md";break;case"lg":case"large":n="lg";break;case"xl":case"extralarge":case"extraLarge":case"extra-large":n="xl";break;default:n=void 0}void 0!==n&&(i.dialogNode.parentNode.classList.remove("sui-modal-sm"),i.dialogNode.parentNode.classList.remove("sui-modal-md"),i.dialogNode.parentNode.classList.remove("sui-modal-lg"),i.dialogNode.parentNode.classList.remove("sui-modal-xl"),i.dialogNode.parentNode.classList.add("sui-modal-"+n))}var s,o,r,a;if(e[0].classList.add("sui-active"),e[0].classList.add("sui-loaded"),e[0].removeAttribute("disabled"),e[0].removeAttribute("tabindex"),e[0].removeAttribute("aria-hidden"),e[0].hasAttribute("data-modal-labelledby"))s="",""===(o=e[0].getAttribute("data-modal-labelledby"))&&void 0===o||(s=o),i.dialogNode.setAttribute("aria-labelledby",s);if(e[0].hasAttribute("data-modal-describedby"))r="",""===(a=e[0].getAttribute("data-modal-describedby"))&&void 0===a||(r=a),i.dialogNode.setAttribute("aria-describedby",r)}}),350),0<t.OpenDialogList.length?t.getCurrentDialog().addListeners():document.body.parentNode.classList.remove(t.Utils.dialogOpenClass);var s=new Event("afterClose");this.dialogNode.dispatchEvent(s)},t.Dialog.prototype.replace=function(e,i,n,s){var o=!(arguments.length>4&&void 0!==arguments[4])||arguments[4],r=!(arguments.length>5&&void 0!==arguments[5])||arguments[5],a=this;t.OpenDialogList.pop(),this.removeListeners(),t.Utils.remove(this.preNode),t.Utils.remove(this.postNode),r?(this.dialogNode.classList.add("sui-content-fade-in"),this.dialogNode.classList.remove("sui-content-fade-out")):(this.dialogNode.classList.remove("sui-content-fade-in"),this.dialogNode.classList.remove("sui-content-fade-out")),this.backdropNode.classList.remove("sui-active"),setTimeout((function(){var e=a.dialogNode.querySelectorAll(".sui-modal-slide");if(0<e.length){for(var t=0;t<e.length;t++)e[t].setAttribute("disabled",!0),e[t].classList.remove("sui-loaded"),e[t].classList.remove("sui-active"),e[t].setAttribute("tabindex","-1"),e[t].setAttribute("aria-hidden",!0);if(e[0].hasAttribute("data-modal-size")){var i=e[0].getAttribute("data-modal-size");switch(i){case"sm":case"small":i="sm";break;case"md":case"med":case"medium":i="md";break;case"lg":case"large":i="lg";break;case"xl":case"extralarge":case"extraLarge":case"extra-large":i="xl";break;default:i=void 0}void 0!==i&&(a.dialogNode.parentNode.classList.remove("sui-modal-sm"),a.dialogNode.parentNode.classList.remove("sui-modal-md"),a.dialogNode.parentNode.classList.remove("sui-modal-lg"),a.dialogNode.parentNode.classList.remove("sui-modal-xl"),a.dialogNode.parentNode.classList.add("sui-modal-"+i))}var n,s,o,r;if(e[0].classList.add("sui-active"),e[0].classList.add("sui-loaded"),e[0].removeAttribute("disabled"),e[0].removeAttribute("tabindex"),e[0].removeAttribute("aria-hidden"),e[0].hasAttribute("data-modal-labelledby"))n="",""===(s=e[0].getAttribute("data-modal-labelledby"))&&void 0===s||(n=s),a.dialogNode.setAttribute("aria-labelledby",n);if(e[0].hasAttribute("data-modal-describedby"))o="",""===(r=e[0].getAttribute("data-modal-describedby"))&&void 0===r||(o=r),a.dialogNode.setAttribute("aria-describedby",o)}}),350);var l=i||this.focusAfterClosed;new t.Dialog(e,l,n,s,o,r)},t.Dialog.prototype.slide=function(i,n,s){var o,r,a,l,c="sui-fadein",u=(t.getCurrentDialog(),this.dialogNode.querySelectorAll(".sui-modal-slide")),d=document.getElementById(i);switch(s){case"back":case"left":c="sui-fadein-left";break;case"next":case"right":c="sui-fadein-right";break;default:c="sui-fadein"}for(var p=0;p<u.length;p++)u[p].setAttribute("disabled",!0),u[p].classList.remove("sui-loaded"),u[p].classList.remove("sui-active"),u[p].setAttribute("tabindex","-1"),u[p].setAttribute("aria-hidden",!0);if(d.hasAttribute("data-modal-size")){var h=d.getAttribute("data-modal-size");switch(h){case"sm":case"small":h="sm";break;case"md":case"med":case"medium":h="md";break;case"lg":case"large":h="lg";break;case"xl":case"extralarge":case"extraLarge":case"extra-large":h="xl";break;default:h=void 0}void 0!==h&&(this.dialogNode.parentNode.classList.remove("sui-modal-sm"),this.dialogNode.parentNode.classList.remove("sui-modal-md"),this.dialogNode.parentNode.classList.remove("sui-modal-lg"),this.dialogNode.parentNode.classList.remove("sui-modal-xl"),this.dialogNode.parentNode.classList.add("sui-modal-"+h))}d.hasAttribute("data-modal-labelledby")&&(o="",""===(r=d.getAttribute("data-modal-labelledby"))&&void 0===r||(o=r),this.dialogNode.setAttribute("aria-labelledby",o));d.hasAttribute("data-modal-describedby")&&(a="",""===(l=d.getAttribute("data-modal-describedby"))&&void 0===l||(a=l),this.dialogNode.setAttribute("aria-describedby",a));d.classList.add("sui-active"),d.classList.add(c),d.removeAttribute("tabindex"),d.removeAttribute("aria-hidden"),setTimeout((function(){d.classList.add("sui-loaded"),d.classList.remove(c),d.removeAttribute("disabled")}),600),"string"==typeof n?this.newSlideFocus=document.getElementById(n):"object"===e(n)?this.newSlideFocus=n:this.newSlideFocus=null,this.newSlideFocus?this.newSlideFocus.focus():t.Utils.focusFirstDescendant(this.dialogNode)},t.Dialog.prototype.addListeners=function(){document.addEventListener("focus",this.trapFocus,!0),this.isCloseOnEsc&&this.dialogNode.addEventListener("keyup",t.handleEscape)},t.Dialog.prototype.removeListeners=function(){document.removeEventListener("focus",this.trapFocus,!0)},t.Dialog.prototype.trapFocus=function(e){var i=e.target.parentElement;if(!(t.Utils.IgnoreUtilFocusChanges||i&&i.classList.contains("wp-link-input"))){var n=t.getCurrentDialog();n.dialogNode.contains(e.target)?n.lastFocus=e.target:(t.Utils.focusFirstDescendant(n.dialogNode),n.lastFocus==document.activeElement&&t.Utils.focusLastDescendant(n.dialogNode),n.lastFocus=document.activeElement)}},SUI.openModal=function(e,i,n,s){var o=!(arguments.length>4&&void 0!==arguments[4])||arguments[4],r=arguments.length>5?arguments[5]:void 0;new t.Dialog(e,i,n,s,o,r)},SUI.closeModal=function(e){t.getCurrentDialog().close(e)},SUI.replaceModal=function(e,i,n,s){var o=!(arguments.length>4&&void 0!==arguments[4])||arguments[4],r=arguments.length>5?arguments[5]:void 0;t.getCurrentDialog().replace(e,i,n,s,o,r)},SUI.slideModal=function(e,i,n){t.getCurrentDialog().slide(e,i,n)}}(),function(t){"use strict";"object"!==e(window.SUI)&&(window.SUI={}),SUI.modalDialog=function(){var i,n,s,o,r,a,l,c,u,d,p,h;return n=t("[data-modal-open]"),s=t("[data-modal-close]"),o=t("[data-modal-replace]"),r=t("[data-modal-slide]"),a=t(".sui-modal-overlay"),n.on("click",(function(n){i=t(this),l=i.attr("data-modal-open"),u=i.attr("data-modal-close-focus"),d=i.attr("data-modal-open-focus"),a=i.attr("data-modal-mask"),h=i.attr("data-modal-animated");var s="false"!==i.attr("data-esc-close");"undefined"!==e(u)&&!1!==u&&""!==u||(u=this),"undefined"!==e(d)&&!1!==d&&""!==d||(d=void 0),a="undefined"!==e(a)&&!1!==a&&"true"===a,h="undefined"===e(h)||!1===h||"false"!==h,"undefined"!==e(l)&&!1!==l&&""!==l&&SUI.openModal(l,u,d,a,s,h),n.preventDefault()})),o.on("click",(function(n){i=t(this),l=i.attr("data-modal-replace"),u=i.attr("data-modal-close-focus"),d=i.attr("data-modal-open-focus"),a=i.attr("data-modal-replace-mask");var s="false"!==i.attr("data-esc-close");"undefined"!==e(u)&&!1!==u&&""!==u||(u=void 0),"undefined"!==e(d)&&!1!==d&&""!==d||(d=void 0),a="undefined"!==e(a)&&!1!==a&&"true"===a,"undefined"!==e(l)&&!1!==l&&""!==l&&SUI.replaceModal(l,u,d,a,s,h),n.preventDefault()})),r.on("click",(function(n){i=t(this),c=i.attr("data-modal-slide"),d=i.attr("data-modal-slide-focus"),p=i.attr("data-modal-slide-intro"),"undefined"!==e(d)&&!1!==d&&""!==d||(d=void 0),"undefined"!==e(p)&&!1!==p&&""!==p||(p=""),"undefined"!==e(c)&&!1!==c&&""!==c&&SUI.slideModal(c,d,p),n.preventDefault()})),s.on("click",(function(e){SUI.closeModal(h),e.preventDefault()})),this},SUI.modalDialog()}(jQuery)},2067:function(){function e(e,t,i){return t in e?Object.defineProperty(e,t,{value:i,enumerable:!0,configurable:!0,writable:!0}):e[t]=i,e}function t(e){return t="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},t(e)}!function(i){"use strict";var n=this;"object"!==t(window.SUI)&&(window.SUI={}),SUI.openNotice=function(t,s,o){var r=i("#"+t),a=r.parent();if(null===typeof r||void 0===r)throw new Error('No element found with id="'+t+'".');if("alert"!==r.attr("role"))throw new Error("Notice requires a DOM element with ARIA role of alert.");if(null===typeof s||void 0===s||""===s)throw new Error("Notice requires a message to print.");var l=l||{};l.allowedNotices=["info","blue","green","success","yellow","warning","red","error","purple","upsell"],l.isObject=function(e){return!(null===e&&"undefined"===e||!i.isPlainObject(e))},l.deepMerge=function(t){for(var i=arguments.length,n=new Array(i>1?i-1:0),s=1;s<i;s++)n[s-1]=arguments[s];if(!n.length)return t;var o=n.shift();if(l.isObject(t)&&l.isObject(o))for(var r in o)l.isObject(o[r])?(t[r]||Object.assign(t,e({},r,{})),l.deepMerge(t[r],o[r])):Object.assign(t,e({},r,o[r]));return l.deepMerge.apply(l,[t].concat(n))},l.setProperties=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};l.options=[];l.options[0]=l.deepMerge({type:"default",icon:"info",dismiss:{show:!1,label:"Close this notice",tooltip:""},autoclose:{show:!0,timeout:3e3}},e)},l.setProperties(o),l.buildDismiss=function(){var e="",t=l.options[0].dismiss;if(!0===t.show){(e=document.createElement("div")).className="sui-notice-actions";var i="";""!==t.tooltip&&(a.hasClass("sui-floating-notices")?i+='<div class="sui-tooltip sui-tooltip-bottom" data-tooltip="'+t.tooltip+'">':i+='<div class="sui-tooltip" data-tooltip="'+t.tooltip+'">'),i+='<button class="sui-button-icon">',i+='<span class="sui-icon-check" aria-hidden="true"></span>',""!==t.label&&(i+='<span class="sui-screen-reader-text">'+t.label+"</span>"),i+="</button>",""!==t.tooltip&&(i+="</div>"),e.innerHTML=i}return e},l.buildIcon=function(){var e="",t=l.options[0].icon;return""===t&&void 0===t&&null===typeof t||((e=document.createElement("span")).className+="sui-notice-icon sui-icon-"+t+" sui-md",e.setAttribute("aria-hidden",!0),"loader"===t&&e.classList.add("sui-loading")),e},l.buildMessage=function(){var e=document.createElement("div");return e.className="sui-notice-message",e.innerHTML=s,e.prepend(l.buildIcon()),e},l.buildNotice=function(){var e=document.createElement("div");return e.className="sui-notice-content",e.append(l.buildMessage(),l.buildDismiss()),e},l.showNotice=function(e){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:300,s=l.options[0].type,o=l.options[0].dismiss,a=l.options[0].autoclose;r.addClass("sui-active"),i.each(l.allowedNotices,(function(e,t){t===s&&r.addClass("sui-notice-"+t)})),r.removeAttr("tabindex"),r.html(l.buildNotice()),"slide"===e?r.slideDown(n,(function(){!0===o.show?(r.find(".sui-notice-actions button").trigger("focus"),r.find(".sui-notice-actions button").on("click",(function(){SUI.closeNotice(t)}))):!0===a.show&&setTimeout((function(){return SUI.closeNotice(t)}),parseInt(a.timeout))})):"fade"===e?r.fadeIn(n,(function(){!0===o.show?(r.find(".sui-notice-actions button").trigger("focus"),r.find(".sui-notice-actions button").on("click",(function(){SUI.closeNotice(t)}))):!0===a.show&&setTimeout((function(){return SUI.closeNotice(t)}),parseInt(a.timeout))})):r.show(n,(function(){!0===o.show?(r.find(".sui-notice-actions button").trigger("focus"),r.find(".sui-notice-actions button").on("click",(function(){SUI.closeNotice(t)}))):!0===a.show&&setTimeout((function(){return SUI.closeNotice(t)}),parseInt(a.timeout))}))},l.openNotice=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:300;r.hasClass("sui-active")?"slide"===e?r.slideUp(t,(function(){l.showNotice("slide",t)})):"fade"===e?r.fadeOut(t,(function(){l.showNotice("fade",t)})):r.hide(t,(function(){l.showNotice(null,t)})):l.showNotice(e,t)};return a.hasClass("sui-floating-notices")?l.openNotice("slide"):l.openNotice("fade"),n},SUI.closeNotice=function(e){var t=i("#"+e),s=t.parent();if(null===typeof t||void 0===t)throw new Error('No element found with id="'+e+'".');var o=o||{};o.allowedNotices=["info","blue","green","success","yellow","warning","red","error","purple","upsell"],o.hideNotice=function(){t.removeClass("sui-active"),i.each(o.allowedNotices,(function(e,i){t.removeClass("sui-notice-"+i)})),t.attr("tabindex","-1"),t.empty()},o.closeNotice=function(e){var i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:300;"slide"===e?t.slideUp(i,(function(){return o.hideNotice()})):"fade"===e?t.fadeOut(i,(function(){return o.hideNotice()})):t.hide(i,(function(){return o.hideNotice()}))};return s.hasClass("sui-floating-notices")?o.closeNotice("slide"):o.closeNotice("fade"),n},SUI.notice=function(){var e=e||{};e.Utils=e.Utils||{},e.Utils.Open=function(e){e.on("click",(function(){self=i(this);var e,t=self.attr("data-notice-open"),n="",s={};if(self.is("[data-notice-message]")&&""!==self.attr("data-notice-message"))n+=self.attr("data-notice-message");else for(e=0;e<4;e++){var o="data-notice-paragraph-"+(e+1);self.is("["+o+"]")&&""!==self.attr(o)&&(n+="<p>"+self.attr(o)+"</p>")}self.is("[data-notice-type]")&&""!==self.attr("data-notice-dismiss-type")&&(s.type=self.attr("data-notice-type")),self.is("[data-notice-icon]")&&(s.icon=self.attr("data-notice-icon")),self.is("[data-notice-dismiss]")&&(s.dismiss={},"true"===self.attr("data-notice-dismiss")?s.dismiss.show=!0:"false"===self.attr("data-notice-dismiss")&&(s.dismiss.show=!1)),self.is("[data-notice-dismiss-label]")&&""!==self.attr("data-notice-dismiss-label")&&(s.dismiss.label=self.attr("data-notice-dismiss-label")),self.is("[data-notice-dismiss-tooltip]")&&""!==self.attr("data-notice-dismiss-tooltip")&&(s.dismiss.tooltip=self.attr("data-notice-dismiss-tooltip")),self.is("[data-notice-autoclose]")&&(s.autoclose={},"true"===self.attr("data-notice-autoclose")?s.autoclose.show=!0:"false"===self.attr("data-notice-autoclose")&&(s.autoclose.show=!1)),self.is("[data-notice-autoclose-timeout]")&&(s.autoclose=s.autoclose||{},s.autoclose.timeout=parseInt(self.attr("data-notice-autoclose-timeout"))),SUI.openNotice(t,n,s)}))},e.Utils.Close=function(e){e.on("click",(function(){var e;self=i(this),self.is("[data-notice-close]")&&(e=self.closest(".sui-notice").attr("id"),""!==self.attr("[data-notice-close]")&&(e=self.attr("data-notice-close")),SUI.closeNotice(e))}))};return function(){var t=i("[data-notice-open]");e.Utils.Open(t);var n=i("[data-notice-close]");e.Utils.Close(n)}(),n},SUI.notice()}(jQuery)},7094:function(){function e(t){return e="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},e(t)}!function(t){"use strict";"object"!==e(window.SUI)&&(window.SUI={}),SUI.reviews=function(e,t,i){if(!(t<=0))return function(){var n,s=Math.round(i),o=e.find(".sui-reviews__stars")[0];for(n=0;n<s;n++)o.innerHTML+='<span class="sui-icon-star" aria-hidden="true"></span> ';e.find(".sui-reviews-rating").replaceWith(i),e.find(".sui-reviews-customer-count").replaceWith(t)}(),this},t(".sui-2-12-23 .sui-reviews").each((function(){var e=t(this);t.ajax({url:"https://api.reviews.co.uk/merchant/reviews?store=wpmudev-org",success:function(t){SUI.reviews(e,t.stats.reviews,t.stats.average_rating)}})}))}(jQuery)},5990:function(){function e(t){return e="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},e(t)}!function(t){var i=function(){if(t&&t.fn&&t.fn.select2&&t.fn.select2.amd)var i=t.fn.select2.amd;var n,s,o;return i&&i.requirejs||(i?s=i:i={},function(t){var i,r,a,l,c={},u={},d={},p={},h=Object.prototype.hasOwnProperty,f=[].slice,g=/\.js$/;function m(e,t){return h.call(e,t)}function v(e,t){var i,n,s,o,r,a,l,c,u,p,h,f=t&&t.split("/"),m=d.map,v=m&&m["*"]||{};if(e){for(r=(e=e.split("/")).length-1,d.nodeIdCompat&&g.test(e[r])&&(e[r]=e[r].replace(g,"")),"."===e[0].charAt(0)&&f&&(e=f.slice(0,f.length-1).concat(e)),u=0;u<e.length;u++)if("."===(h=e[u]))e.splice(u,1),u-=1;else if(".."===h){if(0===u||1===u&&".."===e[2]||".."===e[u-1])continue;u>0&&(e.splice(u-1,2),u-=2)}e=e.join("/")}if((f||v)&&m){for(u=(i=e.split("/")).length;u>0;u-=1){if(n=i.slice(0,u).join("/"),f)for(p=f.length;p>0;p-=1)if((s=m[f.slice(0,p).join("/")])&&(s=s[n])){o=s,a=u;break}if(o)break;!l&&v&&v[n]&&(l=v[n],c=u)}!o&&l&&(o=l,a=c),o&&(i.splice(0,a,o),e=i.join("/"))}return e}function y(e,i){return function(){var n=f.call(arguments,0);return"string"!=typeof n[0]&&1===n.length&&n.push(null),r.apply(t,n.concat([e,i]))}}function b(e){return function(t){return v(t,e)}}function w(e){return function(t){c[e]=t}}function _(e){if(m(u,e)){var n=u[e];delete u[e],p[e]=!0,i.apply(t,n)}if(!m(c,e)&&!m(p,e))throw new Error("No "+e);return c[e]}function S(e){var t,i=e?e.indexOf("!"):-1;return i>-1&&(t=e.substring(0,i),e=e.substring(i+1,e.length)),[t,e]}function $(e){return e?S(e):[]}function A(e){return function(){return d&&d.config&&d.config[e]||{}}}a=function(e,t){var i,n=S(e),s=n[0],o=t[1];return e=n[1],s&&(i=_(s=v(s,o))),s?e=i&&i.normalize?i.normalize(e,b(o)):v(e,o):(s=(n=S(e=v(e,o)))[0],e=n[1],s&&(i=_(s))),{f:s?s+"!"+e:e,n:e,pr:s,p:i}},l={require:function(e){return y(e)},exports:function(e){var t=c[e];return void 0!==t?t:c[e]={}},module:function(e){return{id:e,uri:"",exports:c[e],config:A(e)}}},i=function(i,n,s,o){var r,d,h,f,g,v,b,S=[],A=e(s);if(v=$(o=o||i),"undefined"===A||"function"===A){for(n=!n.length&&s.length?["require","exports","module"]:n,g=0;g<n.length;g+=1)if("require"===(d=(f=a(n[g],v)).f))S[g]=l.require(i);else if("exports"===d)S[g]=l.exports(i),b=!0;else if("module"===d)r=S[g]=l.module(i);else if(m(c,d)||m(u,d)||m(p,d))S[g]=_(d);else{if(!f.p)throw new Error(i+" missing "+d);f.p.load(f.n,y(o,!0),w(d),{}),S[g]=c[d]}h=s?s.apply(c[i],S):void 0,i&&(r&&r.exports!==t&&r.exports!==c[i]?c[i]=r.exports:h===t&&b||(c[i]=h))}else i&&(c[i]=s)},n=s=r=function(e,n,s,o,c){if("string"==typeof e)return l[e]?l[e](n):_(a(e,$(n)).f);if(!e.splice){if((d=e).deps&&r(d.deps,d.callback),!n)return;n.splice?(e=n,n=s,s=null):e=t}return n=n||function(){},"function"==typeof s&&(s=o,o=c),o?i(t,e,n,s):setTimeout((function(){i(t,e,n,s)}),4),r},r.config=function(e){return r(e)},n._defined=c,(o=function(e,t,i){if("string"!=typeof e)throw new Error("See almond README: incorrect module build, no module name");t.splice||(i=t,t=[]),m(c,e)||m(u,e)||(u[e]=[e,t,i])}).amd={jQuery:!0}}(),i.requirejs=n,i.require=s,i.define=o),i.define("almond",(function(){})),i.define("jquery",[],(function(){var e=t||$;return null==e&&console&&console.error&&console.error("Select2: An instance of jQuery or a jQuery-compatible library was not found. Make sure that you are including jQuery before Select2 on your web page."),e})),i.define("select2/utils",["jquery"],(function(e){var t={};function i(e){var t=e.prototype,i=[];for(var n in t)"function"==typeof t[n]&&"constructor"!==n&&i.push(n);return i}t.Extend=function(e,t){var i={}.hasOwnProperty;function n(){this.constructor=e}for(var s in t)i.call(t,s)&&(e[s]=t[s]);return n.prototype=t.prototype,e.prototype=new n,e.__super__=t.prototype,e},t.Decorate=function(e,t){var n=i(t),s=i(e);function o(){var i=Array.prototype.unshift,n=t.prototype.constructor.length,s=e.prototype.constructor;n>0&&(i.call(arguments,e.prototype.constructor),s=t.prototype.constructor),s.apply(this,arguments)}function r(){this.constructor=o}t.displayName=e.displayName,o.prototype=new r;for(var a=0;a<s.length;a++){var l=s[a];o.prototype[l]=e.prototype[l]}for(var c=function(e){var i=function(){};e in o.prototype&&(i=o.prototype[e]);var n=t.prototype[e];return function(){return Array.prototype.unshift.call(arguments,i),n.apply(this,arguments)}},u=0;u<n.length;u++){var d=n[u];o.prototype[d]=c(d)}return o};var n=function(){this.listeners={}};n.prototype.on=function(e,t){this.listeners=this.listeners||{},e in this.listeners?this.listeners[e].push(t):this.listeners[e]=[t]},n.prototype.trigger=function(e){var t=Array.prototype.slice,i=t.call(arguments,1);this.listeners=this.listeners||{},null==i&&(i=[]),0===i.length&&i.push({}),i[0]._type=e,e in this.listeners&&this.invoke(this.listeners[e],t.call(arguments,1)),"*"in this.listeners&&this.invoke(this.listeners["*"],arguments)},n.prototype.invoke=function(e,t){for(var i=0,n=e.length;i<n;i++)e[i].apply(this,t)},t.Observable=n,t.generateChars=function(e){for(var t="",i=0;i<e;i++)t+=Math.floor(36*Math.random()).toString(36);return t},t.bind=function(e,t){return function(){e.apply(t,arguments)}},t._convertData=function(e){for(var t in e){var i=t.split("-"),n=e;if(1!==i.length){for(var s=0;s<i.length;s++){var o=i[s];(o=o.substring(0,1).toLowerCase()+o.substring(1))in n||(n[o]={}),s==i.length-1&&(n[o]=e[t]),n=n[o]}delete e[t]}}return e},t.hasScroll=function(t,i){var n=e(i),s=i.style.overflowX,o=i.style.overflowY;return(s!==o||"hidden"!==o&&"visible"!==o)&&("scroll"===s||"scroll"===o||n.innerHeight()<i.scrollHeight||n.innerWidth()<i.scrollWidth)},t.escapeMarkup=function(e){var t={"\\":"&#92;","&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;","/":"&#47;"};return"string"!=typeof e?e:String(e).replace(/[&<>"'\/\\]/g,(function(e){return t[e]}))},t.__cache={};var s=0;return t.GetUniqueElementId=function(e){var i=e.getAttribute("data-select2-id");return null!=i||(i=e.id?"select2-data-"+e.id:"select2-data-"+(++s).toString()+"-"+t.generateChars(4),e.setAttribute("data-select2-id",i)),i},t.StoreData=function(e,i,n){var s=t.GetUniqueElementId(e);t.__cache[s]||(t.__cache[s]={}),t.__cache[s][i]=n},t.GetData=function(i,n){var s=t.GetUniqueElementId(i);return n?t.__cache[s]&&null!=t.__cache[s][n]?t.__cache[s][n]:e(i).data(n):t.__cache[s]},t.RemoveData=function(e){var i=t.GetUniqueElementId(e);null!=t.__cache[i]&&delete t.__cache[i],e.removeAttribute("data-select2-id")},t.copyNonInternalCssClasses=function(e,t){var i=e.getAttribute("class").trim().split(/\s+/);i=i.filter((function(e){return 0===e.indexOf("select2-")}));var n=t.getAttribute("class").trim().split(/\s+/);n=n.filter((function(e){return 0!==e.indexOf("select2-")}));var s=i.concat(n);e.setAttribute("class",s.join(" "))},t})),i.define("select2/results",["jquery","./utils"],(function(e,t){function i(e,t,n){this.$element=e,this.data=n,this.options=t,i.__super__.constructor.call(this)}return t.Extend(i,t.Observable),i.prototype.render=function(){var t=e('<ul class="select2-results__options" role="listbox"></ul>');return this.options.get("multiple")&&t.attr("aria-multiselectable","true"),this.$results=t,t},i.prototype.clear=function(){this.$results.empty()},i.prototype.displayMessage=function(t){var i=this.options.get("escapeMarkup");this.clear(),this.hideLoading();var n=e('<li role="alert" aria-live="assertive" class="select2-results__option"></li>'),s=this.options.get("translations").get(t.message);n.append(i(s(t.args))),n[0].className+=" select2-results__message",this.$results.append(n)},i.prototype.hideMessages=function(){this.$results.find(".select2-results__message").remove()},i.prototype.append=function(e){this.hideLoading();var t=[];if(null!=e.results&&0!==e.results.length){e.results=this.sort(e.results);for(var i=0;i<e.results.length;i++){var n=e.results[i],s=this.option(n);t.push(s)}this.$results.append(t)}else 0===this.$results.children().length&&this.trigger("results:message",{message:"noResults"})},i.prototype.position=function(e,t){t.find(".select2-results").append(e)},i.prototype.sort=function(e){return this.options.get("sorter")(e)},i.prototype.highlightFirstItem=function(){var e=this.$results.find(".select2-results__option--selectable"),t=e.filter(".select2-results__option--selected");t.length>0?t.first().trigger("mouseenter"):e.first().trigger("mouseenter"),this.ensureHighlightVisible()},i.prototype.setClasses=function(){var i=this;this.data.current((function(n){var s=n.map((function(e){return e.id.toString()}));i.$results.find(".select2-results__option--selectable").each((function(){var i=e(this),n=t.GetData(this,"data"),o=""+n.id;null!=n.element&&n.element.selected||null==n.element&&s.indexOf(o)>-1?(this.classList.add("select2-results__option--selected"),i.attr("aria-selected","true")):(this.classList.remove("select2-results__option--selected"),i.attr("aria-selected","false"))}))}))},i.prototype.showLoading=function(e){this.hideLoading();var t={disabled:!0,loading:!0,text:this.options.get("translations").get("searching")(e)},i=this.option(t);i.className+=" loading-results",this.$results.prepend(i)},i.prototype.hideLoading=function(){this.$results.find(".loading-results").remove()},i.prototype.option=function(i){var n=document.createElement("li");n.classList.add("select2-results__option"),n.classList.add("select2-results__option--selectable");var s={role:"option"},o=window.Element.prototype.matches||window.Element.prototype.msMatchesSelector||window.Element.prototype.webkitMatchesSelector;for(var r in(null!=i.element&&o.call(i.element,":disabled")||null==i.element&&i.disabled)&&(s["aria-disabled"]="true",n.classList.remove("select2-results__option--selectable"),n.classList.add("select2-results__option--disabled")),null==i.id&&n.classList.remove("select2-results__option--selectable"),null!=i._resultId&&(n.id=i._resultId),i.title&&(n.title=i.title),i.children&&(s.role="group",s["aria-label"]=i.text,n.classList.remove("select2-results__option--selectable"),n.classList.add("select2-results__option--group")),s){var a=s[r];n.setAttribute(r,a)}if(i.children){var l=e(n),c=document.createElement("strong");c.className="select2-results__group",this.template(i,c);for(var u=[],d=0;d<i.children.length;d++){var p=i.children[d],h=this.option(p);u.push(h)}var f=e("<ul></ul>",{class:"select2-results__options select2-results__options--nested",role:"none"});f.append(u),l.append(c),l.append(f)}else this.template(i,n);return t.StoreData(n,"data",i),n},i.prototype.bind=function(i,n){var s=this,o=i.id+"-results";this.$results.attr("id",o),i.on("results:all",(function(e){s.clear(),s.append(e.data),i.isOpen()&&(s.setClasses(),s.highlightFirstItem())})),i.on("results:append",(function(e){s.append(e.data),i.isOpen()&&s.setClasses()})),i.on("query",(function(e){s.hideMessages(),s.showLoading(e)})),i.on("select",(function(){i.isOpen()&&(s.setClasses(),s.options.get("scrollAfterSelect")&&s.highlightFirstItem())})),i.on("unselect",(function(){i.isOpen()&&(s.setClasses(),s.options.get("scrollAfterSelect")&&s.highlightFirstItem())})),i.on("open",(function(){s.$results.attr("aria-expanded","true"),s.$results.attr("aria-hidden","false"),s.setClasses(),s.ensureHighlightVisible()})),i.on("close",(function(){s.$results.attr("aria-expanded","false"),s.$results.attr("aria-hidden","true"),s.$results.removeAttr("aria-activedescendant")})),i.on("results:toggle",(function(){var e=s.getHighlightedResults();0!==e.length&&e.trigger("mouseup")})),i.on("results:select",(function(){var e=s.getHighlightedResults();if(0!==e.length){var i=t.GetData(e[0],"data");e.hasClass("select2-results__option--selected")?s.trigger("close",{}):s.trigger("select",{data:i})}})),i.on("results:previous",(function(){var e=s.getHighlightedResults(),t=s.$results.find(".select2-results__option--selectable"),i=t.index(e);if(!(i<=0)){var n=i-1;0===e.length&&(n=0);var o=t.eq(n);o.trigger("mouseenter");var r=s.$results.offset().top,a=o.offset().top,l=s.$results.scrollTop()+(a-r);0===n?s.$results.scrollTop(0):a-r<0&&s.$results.scrollTop(l)}})),i.on("results:next",(function(){var e=s.getHighlightedResults(),t=s.$results.find(".select2-results__option--selectable"),i=t.index(e)+1;if(!(i>=t.length)){var n=t.eq(i);n.trigger("mouseenter");var o=s.$results.offset().top+s.$results.outerHeight(!1),r=n.offset().top+n.outerHeight(!1),a=s.$results.scrollTop()+r-o;0===i?s.$results.scrollTop(0):r>o&&s.$results.scrollTop(a)}})),i.on("results:focus",(function(e){e.element[0].classList.add("select2-results__option--highlighted"),e.element[0].setAttribute("aria-selected","true")})),i.on("results:message",(function(e){s.displayMessage(e)})),e.fn.mousewheel&&this.$results.on("mousewheel",(function(e){var t=s.$results.scrollTop(),i=s.$results.get(0).scrollHeight-t+e.deltaY,n=e.deltaY>0&&t-e.deltaY<=0,o=e.deltaY<0&&i<=s.$results.height();n?(s.$results.scrollTop(0),e.preventDefault(),e.stopPropagation()):o&&(s.$results.scrollTop(s.$results.get(0).scrollHeight-s.$results.height()),e.preventDefault(),e.stopPropagation())})),this.$results.on("mouseup",".select2-results__option--selectable",(function(i){var n=e(this),o=t.GetData(this,"data");n.hasClass("select2-results__option--selected")?s.options.get("multiple")?s.trigger("unselect",{originalEvent:i,data:o}):s.trigger("close",{}):s.trigger("select",{originalEvent:i,data:o})})),this.$results.on("mouseenter",".select2-results__option--selectable",(function(i){var n=t.GetData(this,"data");s.getHighlightedResults().removeClass("select2-results__option--highlighted").attr("aria-selected","false"),s.trigger("results:focus",{data:n,element:e(this)})}))},i.prototype.getHighlightedResults=function(){return this.$results.find(".select2-results__option--highlighted")},i.prototype.destroy=function(){this.$results.remove()},i.prototype.ensureHighlightVisible=function(){var e=this.getHighlightedResults();if(0!==e.length){var t=this.$results.find(".select2-results__option--selectable").index(e),i=this.$results.offset().top,n=e.offset().top,s=this.$results.scrollTop()+(n-i),o=n-i;s-=2*e.outerHeight(!1),t<=2?this.$results.scrollTop(0):(o>this.$results.outerHeight()||o<0)&&this.$results.scrollTop(s)}},i.prototype.template=function(t,i){var n=this.options.get("templateResult"),s=this.options.get("escapeMarkup"),o=n(t,i);null==o?i.style.display="none":"string"==typeof o?i.innerHTML=s(o):e(i).append(o)},i})),i.define("select2/keys",[],(function(){return{BACKSPACE:8,TAB:9,ENTER:13,SHIFT:16,CTRL:17,ALT:18,ESC:27,SPACE:32,PAGE_UP:33,PAGE_DOWN:34,END:35,HOME:36,LEFT:37,UP:38,RIGHT:39,DOWN:40,DELETE:46}})),i.define("select2/selection/base",["jquery","../utils","../keys"],(function(e,t,i){function n(e,t){this.$element=e,this.options=t,n.__super__.constructor.call(this)}return t.Extend(n,t.Observable),n.prototype.render=function(){var i=e('<span class="select2-selection" role="combobox"  aria-haspopup="true" aria-expanded="false"></span>');return this._tabindex=0,null!=t.GetData(this.$element[0],"old-tabindex")?this._tabindex=t.GetData(this.$element[0],"old-tabindex"):null!=this.$element.attr("tabindex")&&(this._tabindex=this.$element.attr("tabindex")),i.attr("title",this.$element.attr("title")),i.attr("tabindex",this._tabindex),i.attr("aria-disabled","false"),this.$selection=i,i},n.prototype.bind=function(e,t){var n=this,s=e.id+"-results";this.container=e,this.$selection.on("focus",(function(e){n.trigger("focus",e)})),this.$selection.on("blur",(function(e){n._handleBlur(e)})),this.$selection.on("keydown",(function(e){n.trigger("keypress",e),e.which===i.SPACE&&e.preventDefault()})),e.on("results:focus",(function(e){n.$selection.attr("aria-activedescendant",e.data._resultId)})),e.on("selection:update",(function(e){n.update(e.data)})),e.on("open",(function(){n.$selection.attr("aria-expanded","true"),n.$selection.attr("aria-owns",s),n._attachCloseHandler(e)})),e.on("close",(function(){n.$selection.attr("aria-expanded","false"),n.$selection.removeAttr("aria-activedescendant"),n.$selection.removeAttr("aria-owns"),n.$selection.trigger("focus"),n._detachCloseHandler(e)})),e.on("enable",(function(){n.$selection.attr("tabindex",n._tabindex),n.$selection.attr("aria-disabled","false")})),e.on("disable",(function(){n.$selection.attr("tabindex","-1"),n.$selection.attr("aria-disabled","true")}))},n.prototype._handleBlur=function(t){var i=this;window.setTimeout((function(){document.activeElement==i.$selection[0]||e.contains(i.$selection[0],document.activeElement)||i.trigger("blur",t)}),1)},n.prototype._attachCloseHandler=function(i){e(document.body).on("mousedown.select2."+i.id,(function(i){var n=e(i.target).closest(".select2");e(".select2.select2-container--open").each((function(){this!=n[0]&&t.GetData(this,"element").SUIselect2("close")}))}))},n.prototype._detachCloseHandler=function(t){e(document.body).off("mousedown.select2."+t.id)},n.prototype.position=function(e,t){t.find(".selection").append(e)},n.prototype.destroy=function(){this._detachCloseHandler(this.container)},n.prototype.update=function(e){throw new Error("The `update` method must be defined in child classes.")},n.prototype.isEnabled=function(){return!this.isDisabled()},n.prototype.isDisabled=function(){return this.options.get("disabled")},n})),i.define("select2/selection/single",["jquery","./base","../utils","../keys"],(function(e,t,i,n){function s(){s.__super__.constructor.apply(this,arguments)}return i.Extend(s,t),s.prototype.render=function(){var e=s.__super__.render.call(this);return e[0].classList.add("select2-selection--single"),e.html('<span class="select2-selection__rendered"></span><span class="select2-selection__arrow" role="presentation"><span class="sui-icon-chevron-down sui-sm" aria-hidden="true"></span></span>'),e},s.prototype.bind=function(e,t){var i=this;s.__super__.bind.apply(this,arguments);var n=e.id+"-container";this.$selection.find(".select2-selection__rendered").attr("id",n).attr("role","textbox").attr("aria-readonly","true"),this.$selection.attr("aria-labelledby",n),this.$selection.attr("aria-controls",n),this.$selection.on("mousedown",(function(e){1===e.which&&i.trigger("toggle",{originalEvent:e})})),this.$selection.on("focus",(function(e){})),this.$selection.on("blur",(function(e){})),e.on("focus",(function(t){e.isOpen()||i.$selection.trigger("focus")}))},s.prototype.clear=function(){var e=this.$selection.find(".select2-selection__rendered");e.empty(),e.removeAttr("title")},s.prototype.display=function(e,t){var i=this.options.get("templateSelection");return this.options.get("escapeMarkup")(i(e,t))},s.prototype.selectionContainer=function(){return e("<span></span>")},s.prototype.update=function(e){if(0===e.length)return this.clear(),void("vars"===this.options.get("theme")&&this.$selection.find(".select2-selection__rendered").html('<span class="sui-icon-plus-circle sui-md" aria-hidden="true"></span>'));var t=e[0],i=this.$selection.find(".select2-selection__rendered"),n=this.display(t,i);i.empty().append(n);var s=t.title||t.text;s?i.attr("title",s):i.removeAttr("title")},s})),i.define("select2/selection/multiple",["jquery","./base","../utils"],(function(e,t,i){function n(e,t){n.__super__.constructor.apply(this,arguments)}return i.Extend(n,t),n.prototype.render=function(){var e=n.__super__.render.call(this);return e[0].classList.add("select2-selection--multiple"),e.html('<ul class="select2-selection__rendered"></ul>'),e},n.prototype.bind=function(t,s){var o=this;n.__super__.bind.apply(this,arguments);var r=t.id+"-container";this.$selection.find(".select2-selection__rendered").attr("id",r),this.$selection.on("click",(function(e){o.trigger("toggle",{originalEvent:e})})),this.$selection.on("click",".sui-button-icon",(function(t){if(!o.isDisabled()){var n=e(this).parent(),s=i.GetData(n[0],"data");o.trigger("unselect",{originalEvent:t,data:s})}})),this.$selection.on("keydown",".sui-button-icon",(function(e){o.isDisabled()||e.stopPropagation()}))},n.prototype.clear=function(){var e=this.$selection.find(".select2-selection__rendered");e.empty(),e.removeAttr("title"),e.removeClass("has-option-selected")},n.prototype.display=function(e,t){var i=this.options.get("templateSelection");return this.options.get("escapeMarkup")(i(e,t))},n.prototype.selectionContainer=function(){return e('<li class="select2-selection__choice"><span class="select2-selection__choice__display"></span><button type="button" class="sui-button-icon" tabindex="-1"><span class="sui-icon-close sui-sm" aria-hidden="true"></span></button></li>')},n.prototype.update=function(e){if(this.clear(),0!==e.length){for(var t=[],n=this.$selection.find(".select2-selection__rendered").attr("id")+"-choice-",s=0;s<e.length;s++){var o=e[s],r=this.selectionContainer(),a=this.display(o,r),l=n+i.generateChars(4)+"-";o.id?l+=o.id:l+=i.generateChars(4),r.find(".select2-selection__choice__display").append(a).attr("id",l);var c=o.title||o.text;c&&r.attr("title",c);var u=this.options.get("translations").get("removeItem"),d=r.find(".sui-button-icon");d.attr("title",u()),d.attr("aria-label",u()),d.attr("aria-describedby",l),i.StoreData(r[0],"data",o),t.push(r)}this.$selection.find(".select2-selection__rendered").append(t).addClass("has-option-selected")}},n})),i.define("select2/selection/placeholder",[],(function(){function e(e,t,i){this.placeholder=this.normalizePlaceholder(i.get("placeholder")),e.call(this,t,i)}return e.prototype.normalizePlaceholder=function(e,t){return"string"==typeof t&&(t={id:"",text:t}),t},e.prototype.createPlaceholder=function(e,t){var i=this.selectionContainer();i.html(this.display(t)),i[0].classList.add("select2-selection__placeholder"),i[0].classList.remove("select2-selection__choice");var n=t.title||t.text||i.text();return this.$selection.find(".select2-selection__rendered").attr("title",n),i},e.prototype.update=function(e,t){var i=1==t.length&&t[0].id!=this.placeholder.id;if(t.length>1||i)return e.call(this,t);this.clear();var n=this.createPlaceholder(this.placeholder);this.$selection.find(".select2-selection__rendered").append(n)},e})),i.define("select2/selection/allowClear",["jquery","../keys","../utils"],(function(e,t,i){function n(){}return n.prototype.bind=function(e,t,i){var n=this;e.call(this,t,i),null==this.placeholder&&this.options.get("debug")&&window.console&&console.error&&console.error("Select2: The `allowClear` option should be used in combination with the `placeholder` option."),this.$selection.on("mousedown",".select2-selection__clear",(function(e){n._handleClear(e)})),t.on("keypress",(function(e){n._handleKeyboardClear(e,t)}))},n.prototype._handleClear=function(e,t){if(!this.isDisabled()){var n=this.$selection.find(".select2-selection__clear");if(0!==n.length){t.stopPropagation();var s=i.GetData(n[0],"data"),o=this.$element.val();this.$element.val(this.placeholder.id);var r={data:s};if(this.trigger("clear",r),r.prevented)this.$element.val(o);else{for(var a=0;a<s.length;a++)if(r={data:s[a]},this.trigger("unselect",r),r.prevented)return void this.$element.val(o);this.$element.trigger("input").trigger("change"),this.trigger("toggle",{})}}}},n.prototype._handleKeyboardClear=function(e,i,n){n.isOpen()||i.which!=t.DELETE&&i.which!=t.BACKSPACE||this._handleClear(i)},n.prototype.update=function(t,n){if(t.call(this,n),this.$selection.find(".select2-selection__clear").remove(),this.$selection[0].classList.remove("select2-selection--clearable"),!(this.$selection.find(".select2-selection__placeholder").length>0||0===n.length)){var s=this.$selection.find(".select2-selection__rendered").attr("id"),o=this.options.get("translations").get("removeAllItems"),r=e('<button type="button" class="select2-selection__clear" tabindex="-1"><span aria-hidden="true">&times;</span></button>');r.attr("title",o()),r.attr("aria-label",o()),r.attr("aria-describedby",s),i.StoreData(r[0],"data",n),this.$selection.prepend(r),this.$selection[0].classList.add("select2-selection--clearable")}},n})),i.define("select2/selection/search",["jquery","../utils","../keys"],(function(e,t,i){function n(e,t,i){e.call(this,t,i)}return n.prototype.render=function(t){var i=this.options.get("translations").get("search"),n=e('<span class="select2-search select2-search--inline"><textarea class="select2-search__field" type="search" tabindex="-1" autocorrect="off" autocapitalize="none" spellcheck="false" role="searchbox" aria-autocomplete="list" ></textarea></span>');this.$searchContainer=n,this.$search=n.find("textarea"),this.$search.prop("autocomplete",this.options.get("autocomplete")),this.$search.attr("aria-label",i());var s=t.call(this);return this._transferTabIndex(),s.append(this.$searchContainer),s},n.prototype.bind=function(e,n,s){var o=this,r=n.id+"-results",a=n.id+"-container";e.call(this,n,s),o.$search.attr("aria-describedby",a),n.on("open",(function(){o.$search.attr("aria-controls",r),o.$search.trigger("focus")})),n.on("close",(function(){o.$search.val(""),o.resizeSearch(),o.$search.removeAttr("aria-controls"),o.$search.removeAttr("aria-activedescendant"),o.$search.trigger("focus")})),n.on("enable",(function(){o.$search.prop("disabled",!1),o._transferTabIndex()})),n.on("disable",(function(){o.$search.prop("disabled",!0)})),n.on("focus",(function(e){o.$search.trigger("focus")})),n.on("results:focus",(function(e){e.data._resultId?o.$search.attr("aria-activedescendant",e.data._resultId):o.$search.removeAttr("aria-activedescendant")})),this.$selection.on("focusin",".select2-search--inline",(function(e){o.trigger("focus",e)})),this.$selection.on("focusout",".select2-search--inline",(function(e){o._handleBlur(e)})),this.$selection.on("keydown",".select2-search--inline",(function(e){if(e.stopPropagation(),o.trigger("keypress",e),o._keyUpPrevented=e.isDefaultPrevented(),e.which===i.BACKSPACE&&""===o.$search.val()){var n=o.$selection.find(".select2-selection__choice").last();if(n.length>0){var s=t.GetData(n[0],"data");o.searchRemoveChoice(s),e.preventDefault()}}})),this.$selection.on("click",".select2-search--inline",(function(e){o.$search.val()&&e.stopPropagation()}));var l=document.documentMode,c=l&&l<=11;this.$selection.on("input.searchcheck",".select2-search--inline",(function(e){c?o.$selection.off("input.search input.searchcheck"):o.$selection.off("keyup.search")})),this.$selection.on("keyup.search input.search",".select2-search--inline",(function(e){if(c&&"input"===e.type)o.$selection.off("input.search input.searchcheck");else{var t=e.which;t!=i.SHIFT&&t!=i.CTRL&&t!=i.ALT&&t!=i.TAB&&o.handleSearch(e)}}))},n.prototype._transferTabIndex=function(e){this.$search.attr("tabindex",this.$selection.attr("tabindex")),this.$selection.attr("tabindex","-1")},n.prototype.createPlaceholder=function(e,t){this.$search.attr("placeholder",t.text)},n.prototype.update=function(e,t){var i=this.$search[0]==document.activeElement;this.$search.attr("placeholder",""),e.call(this,t),this.resizeSearch(),i&&this.$search.trigger("focus")},n.prototype.handleSearch=function(){if(this.resizeSearch(),!this._keyUpPrevented){var e=this.$search.val();this.trigger("query",{term:e})}this._keyUpPrevented=!1},n.prototype.searchRemoveChoice=function(e,t){this.trigger("unselect",{data:t}),this.$search.val(t.text),this.handleSearch()},n.prototype.resizeSearch=function(){this.$search.css("width","25px");var e="100%";""===this.$search.attr("placeholder")&&(e=.75*(this.$search.val().length+1)+"em"),this.$search.css("width",e)},n})),i.define("select2/selection/selectionCss",["../utils"],(function(e){function t(){}return t.prototype.render=function(t){var i=t.call(this),n=this.options.get("selectionCssClass")||"";return-1!==n.indexOf(":all:")&&(n=n.replace(":all:",""),e.copyNonInternalCssClasses(i[0],this.$element[0])),i.addClass(n),i},t})),i.define("select2/selection/eventRelay",["jquery"],(function(e){function t(){}return t.prototype.bind=function(t,i,n){var s=this,o=["open","opening","close","closing","select","selecting","unselect","unselecting","clear","clearing"],r=["opening","closing","selecting","unselecting","clearing"];t.call(this,i,n),i.on("*",(function(t,i){if(-1!==o.indexOf(t)){i=i||{};var n=e.Event("select2:"+t,{params:i});s.$element.trigger(n),-1!==r.indexOf(t)&&(i.prevented=n.isDefaultPrevented())}}))},t})),i.define("select2/translation",["jquery","require"],(function(e,t){function i(e){this.dict=e||{}}return i.prototype.all=function(){return this.dict},i.prototype.get=function(e){return this.dict[e]},i.prototype.extend=function(t){this.dict=e.extend({},t.all(),this.dict)},i._cache={},i.loadPath=function(e){if(!(e in i._cache)){var n=t(e);i._cache[e]=n}return new i(i._cache[e])},i})),i.define("select2/diacritics",[],(function(){return{"Ⓐ":"A","Ａ":"A","À":"A","Á":"A","Â":"A","Ầ":"A","Ấ":"A","Ẫ":"A","Ẩ":"A","Ã":"A","Ā":"A","Ă":"A","Ằ":"A","Ắ":"A","Ẵ":"A","Ẳ":"A","Ȧ":"A","Ǡ":"A","Ä":"A","Ǟ":"A","Ả":"A","Å":"A","Ǻ":"A","Ǎ":"A","Ȁ":"A","Ȃ":"A","Ạ":"A","Ậ":"A","Ặ":"A","Ḁ":"A","Ą":"A","Ⱥ":"A","Ɐ":"A","Ꜳ":"AA","Æ":"AE","Ǽ":"AE","Ǣ":"AE","Ꜵ":"AO","Ꜷ":"AU","Ꜹ":"AV","Ꜻ":"AV","Ꜽ":"AY","Ⓑ":"B","Ｂ":"B","Ḃ":"B","Ḅ":"B","Ḇ":"B","Ƀ":"B","Ƃ":"B","Ɓ":"B","Ⓒ":"C","Ｃ":"C","Ć":"C","Ĉ":"C","Ċ":"C","Č":"C","Ç":"C","Ḉ":"C","Ƈ":"C","Ȼ":"C","Ꜿ":"C","Ⓓ":"D","Ｄ":"D","Ḋ":"D","Ď":"D","Ḍ":"D","Ḑ":"D","Ḓ":"D","Ḏ":"D","Đ":"D","Ƌ":"D","Ɗ":"D","Ɖ":"D","Ꝺ":"D","Ǳ":"DZ","Ǆ":"DZ","ǲ":"Dz","ǅ":"Dz","Ⓔ":"E","Ｅ":"E","È":"E","É":"E","Ê":"E","Ề":"E","Ế":"E","Ễ":"E","Ể":"E","Ẽ":"E","Ē":"E","Ḕ":"E","Ḗ":"E","Ĕ":"E","Ė":"E","Ë":"E","Ẻ":"E","Ě":"E","Ȅ":"E","Ȇ":"E","Ẹ":"E","Ệ":"E","Ȩ":"E","Ḝ":"E","Ę":"E","Ḙ":"E","Ḛ":"E","Ɛ":"E","Ǝ":"E","Ⓕ":"F","Ｆ":"F","Ḟ":"F","Ƒ":"F","Ꝼ":"F","Ⓖ":"G","Ｇ":"G","Ǵ":"G","Ĝ":"G","Ḡ":"G","Ğ":"G","Ġ":"G","Ǧ":"G","Ģ":"G","Ǥ":"G","Ɠ":"G","Ꞡ":"G","Ᵹ":"G","Ꝿ":"G","Ⓗ":"H","Ｈ":"H","Ĥ":"H","Ḣ":"H","Ḧ":"H","Ȟ":"H","Ḥ":"H","Ḩ":"H","Ḫ":"H","Ħ":"H","Ⱨ":"H","Ⱶ":"H","Ɥ":"H","Ⓘ":"I","Ｉ":"I","Ì":"I","Í":"I","Î":"I","Ĩ":"I","Ī":"I","Ĭ":"I","İ":"I","Ï":"I","Ḯ":"I","Ỉ":"I","Ǐ":"I","Ȉ":"I","Ȋ":"I","Ị":"I","Į":"I","Ḭ":"I","Ɨ":"I","Ⓙ":"J","Ｊ":"J","Ĵ":"J","Ɉ":"J","Ⓚ":"K","Ｋ":"K","Ḱ":"K","Ǩ":"K","Ḳ":"K","Ķ":"K","Ḵ":"K","Ƙ":"K","Ⱪ":"K","Ꝁ":"K","Ꝃ":"K","Ꝅ":"K","Ꞣ":"K","Ⓛ":"L","Ｌ":"L","Ŀ":"L","Ĺ":"L","Ľ":"L","Ḷ":"L","Ḹ":"L","Ļ":"L","Ḽ":"L","Ḻ":"L","Ł":"L","Ƚ":"L","Ɫ":"L","Ⱡ":"L","Ꝉ":"L","Ꝇ":"L","Ꞁ":"L","Ǉ":"LJ","ǈ":"Lj","Ⓜ":"M","Ｍ":"M","Ḿ":"M","Ṁ":"M","Ṃ":"M","Ɱ":"M","Ɯ":"M","Ⓝ":"N","Ｎ":"N","Ǹ":"N","Ń":"N","Ñ":"N","Ṅ":"N","Ň":"N","Ṇ":"N","Ņ":"N","Ṋ":"N","Ṉ":"N","Ƞ":"N","Ɲ":"N","Ꞑ":"N","Ꞥ":"N","Ǌ":"NJ","ǋ":"Nj","Ⓞ":"O","Ｏ":"O","Ò":"O","Ó":"O","Ô":"O","Ồ":"O","Ố":"O","Ỗ":"O","Ổ":"O","Õ":"O","Ṍ":"O","Ȭ":"O","Ṏ":"O","Ō":"O","Ṑ":"O","Ṓ":"O","Ŏ":"O","Ȯ":"O","Ȱ":"O","Ö":"O","Ȫ":"O","Ỏ":"O","Ő":"O","Ǒ":"O","Ȍ":"O","Ȏ":"O","Ơ":"O","Ờ":"O","Ớ":"O","Ỡ":"O","Ở":"O","Ợ":"O","Ọ":"O","Ộ":"O","Ǫ":"O","Ǭ":"O","Ø":"O","Ǿ":"O","Ɔ":"O","Ɵ":"O","Ꝋ":"O","Ꝍ":"O","Œ":"OE","Ƣ":"OI","Ꝏ":"OO","Ȣ":"OU","Ⓟ":"P","Ｐ":"P","Ṕ":"P","Ṗ":"P","Ƥ":"P","Ᵽ":"P","Ꝑ":"P","Ꝓ":"P","Ꝕ":"P","Ⓠ":"Q","Ｑ":"Q","Ꝗ":"Q","Ꝙ":"Q","Ɋ":"Q","Ⓡ":"R","Ｒ":"R","Ŕ":"R","Ṙ":"R","Ř":"R","Ȑ":"R","Ȓ":"R","Ṛ":"R","Ṝ":"R","Ŗ":"R","Ṟ":"R","Ɍ":"R","Ɽ":"R","Ꝛ":"R","Ꞧ":"R","Ꞃ":"R","Ⓢ":"S","Ｓ":"S","ẞ":"S","Ś":"S","Ṥ":"S","Ŝ":"S","Ṡ":"S","Š":"S","Ṧ":"S","Ṣ":"S","Ṩ":"S","Ș":"S","Ş":"S","Ȿ":"S","Ꞩ":"S","Ꞅ":"S","Ⓣ":"T","Ｔ":"T","Ṫ":"T","Ť":"T","Ṭ":"T","Ț":"T","Ţ":"T","Ṱ":"T","Ṯ":"T","Ŧ":"T","Ƭ":"T","Ʈ":"T","Ⱦ":"T","Ꞇ":"T","Ꜩ":"TZ","Ⓤ":"U","Ｕ":"U","Ù":"U","Ú":"U","Û":"U","Ũ":"U","Ṹ":"U","Ū":"U","Ṻ":"U","Ŭ":"U","Ü":"U","Ǜ":"U","Ǘ":"U","Ǖ":"U","Ǚ":"U","Ủ":"U","Ů":"U","Ű":"U","Ǔ":"U","Ȕ":"U","Ȗ":"U","Ư":"U","Ừ":"U","Ứ":"U","Ữ":"U","Ử":"U","Ự":"U","Ụ":"U","Ṳ":"U","Ų":"U","Ṷ":"U","Ṵ":"U","Ʉ":"U","Ⓥ":"V","Ｖ":"V","Ṽ":"V","Ṿ":"V","Ʋ":"V","Ꝟ":"V","Ʌ":"V","Ꝡ":"VY","Ⓦ":"W","Ｗ":"W","Ẁ":"W","Ẃ":"W","Ŵ":"W","Ẇ":"W","Ẅ":"W","Ẉ":"W","Ⱳ":"W","Ⓧ":"X","Ｘ":"X","Ẋ":"X","Ẍ":"X","Ⓨ":"Y","Ｙ":"Y","Ỳ":"Y","Ý":"Y","Ŷ":"Y","Ỹ":"Y","Ȳ":"Y","Ẏ":"Y","Ÿ":"Y","Ỷ":"Y","Ỵ":"Y","Ƴ":"Y","Ɏ":"Y","Ỿ":"Y","Ⓩ":"Z","Ｚ":"Z","Ź":"Z","Ẑ":"Z","Ż":"Z","Ž":"Z","Ẓ":"Z","Ẕ":"Z","Ƶ":"Z","Ȥ":"Z","Ɀ":"Z","Ⱬ":"Z","Ꝣ":"Z","ⓐ":"a","ａ":"a","ẚ":"a","à":"a","á":"a","â":"a","ầ":"a","ấ":"a","ẫ":"a","ẩ":"a","ã":"a","ā":"a","ă":"a","ằ":"a","ắ":"a","ẵ":"a","ẳ":"a","ȧ":"a","ǡ":"a","ä":"a","ǟ":"a","ả":"a","å":"a","ǻ":"a","ǎ":"a","ȁ":"a","ȃ":"a","ạ":"a","ậ":"a","ặ":"a","ḁ":"a","ą":"a","ⱥ":"a","ɐ":"a","ꜳ":"aa","æ":"ae","ǽ":"ae","ǣ":"ae","ꜵ":"ao","ꜷ":"au","ꜹ":"av","ꜻ":"av","ꜽ":"ay","ⓑ":"b","ｂ":"b","ḃ":"b","ḅ":"b","ḇ":"b","ƀ":"b","ƃ":"b","ɓ":"b","ⓒ":"c","ｃ":"c","ć":"c","ĉ":"c","ċ":"c","č":"c","ç":"c","ḉ":"c","ƈ":"c","ȼ":"c","ꜿ":"c","ↄ":"c","ⓓ":"d","ｄ":"d","ḋ":"d","ď":"d","ḍ":"d","ḑ":"d","ḓ":"d","ḏ":"d","đ":"d","ƌ":"d","ɖ":"d","ɗ":"d","ꝺ":"d","ǳ":"dz","ǆ":"dz","ⓔ":"e","ｅ":"e","è":"e","é":"e","ê":"e","ề":"e","ế":"e","ễ":"e","ể":"e","ẽ":"e","ē":"e","ḕ":"e","ḗ":"e","ĕ":"e","ė":"e","ë":"e","ẻ":"e","ě":"e","ȅ":"e","ȇ":"e","ẹ":"e","ệ":"e","ȩ":"e","ḝ":"e","ę":"e","ḙ":"e","ḛ":"e","ɇ":"e","ɛ":"e","ǝ":"e","ⓕ":"f","ｆ":"f","ḟ":"f","ƒ":"f","ꝼ":"f","ⓖ":"g","ｇ":"g","ǵ":"g","ĝ":"g","ḡ":"g","ğ":"g","ġ":"g","ǧ":"g","ģ":"g","ǥ":"g","ɠ":"g","ꞡ":"g","ᵹ":"g","ꝿ":"g","ⓗ":"h","ｈ":"h","ĥ":"h","ḣ":"h","ḧ":"h","ȟ":"h","ḥ":"h","ḩ":"h","ḫ":"h","ẖ":"h","ħ":"h","ⱨ":"h","ⱶ":"h","ɥ":"h","ƕ":"hv","ⓘ":"i","ｉ":"i","ì":"i","í":"i","î":"i","ĩ":"i","ī":"i","ĭ":"i","ï":"i","ḯ":"i","ỉ":"i","ǐ":"i","ȉ":"i","ȋ":"i","ị":"i","į":"i","ḭ":"i","ɨ":"i","ı":"i","ⓙ":"j","ｊ":"j","ĵ":"j","ǰ":"j","ɉ":"j","ⓚ":"k","ｋ":"k","ḱ":"k","ǩ":"k","ḳ":"k","ķ":"k","ḵ":"k","ƙ":"k","ⱪ":"k","ꝁ":"k","ꝃ":"k","ꝅ":"k","ꞣ":"k","ⓛ":"l","ｌ":"l","ŀ":"l","ĺ":"l","ľ":"l","ḷ":"l","ḹ":"l","ļ":"l","ḽ":"l","ḻ":"l","ſ":"l","ł":"l","ƚ":"l","ɫ":"l","ⱡ":"l","ꝉ":"l","ꞁ":"l","ꝇ":"l","ǉ":"lj","ⓜ":"m","ｍ":"m","ḿ":"m","ṁ":"m","ṃ":"m","ɱ":"m","ɯ":"m","ⓝ":"n","ｎ":"n","ǹ":"n","ń":"n","ñ":"n","ṅ":"n","ň":"n","ṇ":"n","ņ":"n","ṋ":"n","ṉ":"n","ƞ":"n","ɲ":"n","ŉ":"n","ꞑ":"n","ꞥ":"n","ǌ":"nj","ⓞ":"o","ｏ":"o","ò":"o","ó":"o","ô":"o","ồ":"o","ố":"o","ỗ":"o","ổ":"o","õ":"o","ṍ":"o","ȭ":"o","ṏ":"o","ō":"o","ṑ":"o","ṓ":"o","ŏ":"o","ȯ":"o","ȱ":"o","ö":"o","ȫ":"o","ỏ":"o","ő":"o","ǒ":"o","ȍ":"o","ȏ":"o","ơ":"o","ờ":"o","ớ":"o","ỡ":"o","ở":"o","ợ":"o","ọ":"o","ộ":"o","ǫ":"o","ǭ":"o","ø":"o","ǿ":"o","ɔ":"o","ꝋ":"o","ꝍ":"o","ɵ":"o","œ":"oe","ƣ":"oi","ȣ":"ou","ꝏ":"oo","ⓟ":"p","ｐ":"p","ṕ":"p","ṗ":"p","ƥ":"p","ᵽ":"p","ꝑ":"p","ꝓ":"p","ꝕ":"p","ⓠ":"q","ｑ":"q","ɋ":"q","ꝗ":"q","ꝙ":"q","ⓡ":"r","ｒ":"r","ŕ":"r","ṙ":"r","ř":"r","ȑ":"r","ȓ":"r","ṛ":"r","ṝ":"r","ŗ":"r","ṟ":"r","ɍ":"r","ɽ":"r","ꝛ":"r","ꞧ":"r","ꞃ":"r","ⓢ":"s","ｓ":"s","ß":"s","ś":"s","ṥ":"s","ŝ":"s","ṡ":"s","š":"s","ṧ":"s","ṣ":"s","ṩ":"s","ș":"s","ş":"s","ȿ":"s","ꞩ":"s","ꞅ":"s","ẛ":"s","ⓣ":"t","ｔ":"t","ṫ":"t","ẗ":"t","ť":"t","ṭ":"t","ț":"t","ţ":"t","ṱ":"t","ṯ":"t","ŧ":"t","ƭ":"t","ʈ":"t","ⱦ":"t","ꞇ":"t","ꜩ":"tz","ⓤ":"u","ｕ":"u","ù":"u","ú":"u","û":"u","ũ":"u","ṹ":"u","ū":"u","ṻ":"u","ŭ":"u","ü":"u","ǜ":"u","ǘ":"u","ǖ":"u","ǚ":"u","ủ":"u","ů":"u","ű":"u","ǔ":"u","ȕ":"u","ȗ":"u","ư":"u","ừ":"u","ứ":"u","ữ":"u","ử":"u","ự":"u","ụ":"u","ṳ":"u","ų":"u","ṷ":"u","ṵ":"u","ʉ":"u","ⓥ":"v","ｖ":"v","ṽ":"v","ṿ":"v","ʋ":"v","ꝟ":"v","ʌ":"v","ꝡ":"vy","ⓦ":"w","ｗ":"w","ẁ":"w","ẃ":"w","ŵ":"w","ẇ":"w","ẅ":"w","ẘ":"w","ẉ":"w","ⱳ":"w","ⓧ":"x","ｘ":"x","ẋ":"x","ẍ":"x","ⓨ":"y","ｙ":"y","ỳ":"y","ý":"y","ŷ":"y","ỹ":"y","ȳ":"y","ẏ":"y","ÿ":"y","ỷ":"y","ẙ":"y","ỵ":"y","ƴ":"y","ɏ":"y","ỿ":"y","ⓩ":"z","ｚ":"z","ź":"z","ẑ":"z","ż":"z","ž":"z","ẓ":"z","ẕ":"z","ƶ":"z","ȥ":"z","ɀ":"z","ⱬ":"z","ꝣ":"z","Ά":"Α","Έ":"Ε","Ή":"Η","Ί":"Ι","Ϊ":"Ι","Ό":"Ο","Ύ":"Υ","Ϋ":"Υ","Ώ":"Ω","ά":"α","έ":"ε","ή":"η","ί":"ι","ϊ":"ι","ΐ":"ι","ό":"ο","ύ":"υ","ϋ":"υ","ΰ":"υ","ώ":"ω","ς":"σ","’":"'"}})),i.define("select2/data/base",["../utils"],(function(e){function t(e,i){t.__super__.constructor.call(this)}return e.Extend(t,e.Observable),t.prototype.current=function(e){throw new Error("The `current` method must be defined in child classes.")},t.prototype.query=function(e,t){throw new Error("The `query` method must be defined in child classes.")},t.prototype.bind=function(e,t){},t.prototype.destroy=function(){},t.prototype.generateResultId=function(t,i){var n=t.id+"-result-";return n+=e.generateChars(4),null!=i.id?n+="-"+i.id.toString():n+="-"+e.generateChars(4),n},t})),i.define("select2/data/select",["./base","../utils","jquery"],(function(e,t,i){function n(e,t){this.$element=e,this.options=t,n.__super__.constructor.call(this)}return t.Extend(n,e),n.prototype.current=function(e){var t=this;e(Array.prototype.map.call(this.$element[0].querySelectorAll(":checked"),(function(e){return t.item(i(e))})))},n.prototype.select=function(e){var t=this;if(e.selected=!0,null!=e.element&&"option"===e.element.tagName.toLowerCase())return e.element.selected=!0,void this.$element.trigger("input").trigger("change");if(this.$element.prop("multiple"))this.current((function(i){var n=[];(e=[e]).push.apply(e,i);for(var s=0;s<e.length;s++){var o=e[s].id;-1===n.indexOf(o)&&n.push(o)}t.$element.val(n),t.$element.trigger("input").trigger("change")}));else{var i=e.id;this.$element.val(i),this.$element.trigger("input").trigger("change")}},n.prototype.unselect=function(e){var t=this;if(this.$element.prop("multiple")){if(e.selected=!1,null!=e.element&&"option"===e.element.tagName.toLowerCase())return e.element.selected=!1,void this.$element.trigger("input").trigger("change");this.current((function(i){for(var n=[],s=0;s<i.length;s++){var o=i[s].id;o!==e.id&&-1===n.indexOf(o)&&n.push(o)}t.$element.val(n),t.$element.trigger("input").trigger("change")}))}},n.prototype.bind=function(e,t){var i=this;this.container=e,e.on("select",(function(e){i.select(e.data)})),e.on("unselect",(function(e){i.unselect(e.data)}))},n.prototype.destroy=function(){this.$element.find("*").each((function(){t.RemoveData(this)}))},n.prototype.query=function(e,t){var n=[],s=this;this.$element.children().each((function(){if("option"===this.tagName.toLowerCase()||"optgroup"===this.tagName.toLowerCase()){var t=i(this),o=s.item(t),r=s.matches(e,o);null!==r&&n.push(r)}})),t({results:n})},n.prototype.addOptions=function(e){this.$element.append(e)},n.prototype.option=function(e){var n;e.children?(n=document.createElement("optgroup")).label=e.text:void 0!==(n=document.createElement("option")).textContent?n.textContent=e.text:n.innerText=e.text,void 0!==e.id&&(n.value=e.id),e.disabled&&(n.disabled=!0),e.selected&&(n.selected=!0),e.title&&(n.title=e.title);var s=this._normalizeItem(e);return s.element=n,t.StoreData(n,"data",s),i(n)},n.prototype.item=function(e){var n={};if(null!=(n=t.GetData(e[0],"data")))return n;var s=e[0];if("option"===s.tagName.toLowerCase())n={id:e.val(),text:e.text(),disabled:e.prop("disabled"),selected:e.prop("selected"),title:e.prop("title")};else if("optgroup"===s.tagName.toLowerCase()){n={text:e.prop("label"),children:[],title:e.prop("title")};for(var o=e.children("option"),r=[],a=0;a<o.length;a++){var l=i(o[a]),c=this.item(l);r.push(c)}n.children=r}return(n=this._normalizeItem(n)).element=e[0],t.StoreData(e[0],"data",n),n},n.prototype._normalizeItem=function(e){e!==Object(e)&&(e={id:e,text:e});var t={selected:!1,disabled:!1};return null!=(e=i.extend({},{text:""},e)).id&&(e.id=e.id.toString()),null!=e.text&&(e.text=e.text.toString()),null==e._resultId&&e.id&&null!=this.container&&(e._resultId=this.generateResultId(this.container,e)),i.extend({},t,e)},n.prototype.matches=function(e,t){return this.options.get("matcher")(e,t)},n})),i.define("select2/data/array",["./select","../utils","jquery"],(function(e,t,i){function n(e,t){this._dataToConvert=t.get("data")||[],n.__super__.constructor.call(this,e,t)}return t.Extend(n,e),n.prototype.bind=function(e,t){n.__super__.bind.call(this,e,t),this.addOptions(this.convertToOptions(this._dataToConvert))},n.prototype.select=function(e){var t=this.$element.find("option").filter((function(t,i){return i.value==e.id.toString()}));0===t.length&&(t=this.option(e),this.addOptions(t)),n.__super__.select.call(this,e)},n.prototype.convertToOptions=function(e){var t=this,n=this.$element.find("option"),s=n.map((function(){return t.item(i(this)).id})).get(),o=[];function r(e){return function(){return i(this).val()==e.id}}for(var a=0;a<e.length;a++){var l=this._normalizeItem(e[a]);if(s.indexOf(l.id)>=0){var c=n.filter(r(l)),u=this.item(c),d=i.extend(!0,{},l,u),p=this.option(d);c.replaceWith(p)}else{var h=this.option(l);if(l.children){var f=this.convertToOptions(l.children);h.append(f)}o.push(h)}}return o},n})),i.define("select2/data/ajax",["./array","../utils","jquery"],(function(e,t,i){function n(e,t){this.ajaxOptions=this._applyDefaults(t.get("ajax")),null!=this.ajaxOptions.processResults&&(this.processResults=this.ajaxOptions.processResults),n.__super__.constructor.call(this,e,t)}return t.Extend(n,e),n.prototype._applyDefaults=function(e){var t={data:function(e){return i.extend({},e,{q:e.term})},transport:function(e,t,n){var s=i.ajax(e);return s.then(t),s.fail(n),s}};return i.extend({},t,e,!0)},n.prototype.processResults=function(e){return e},n.prototype.query=function(e,t){var n=this;null!=this._request&&("function"==typeof this._request.abort&&this._request.abort(),this._request=null);var s=i.extend({type:"GET"},this.ajaxOptions);function o(){var i=s.transport(s,(function(i){var s=n.processResults(i,e);n.options.get("debug")&&window.console&&console.error&&(s&&s.results&&Array.isArray(s.results)||console.error("Select2: The AJAX results did not return an array in the `results` key of the response.")),t(s)}),(function(){(!("status"in i)||0!==i.status&&"0"!==i.status)&&n.trigger("results:message",{message:"errorLoading"})}));n._request=i}"function"==typeof s.url&&(s.url=s.url.call(this.$element,e)),"function"==typeof s.data&&(s.data=s.data.call(this.$element,e)),this.ajaxOptions.delay&&null!=e.term?(this._queryTimeout&&window.clearTimeout(this._queryTimeout),this._queryTimeout=window.setTimeout(o,this.ajaxOptions.delay)):o()},n})),i.define("select2/data/tags",["jquery"],(function(e){function t(e,t,i){var n=i.get("tags"),s=i.get("createTag");void 0!==s&&(this.createTag=s);var o=i.get("insertTag");if(void 0!==o&&(this.insertTag=o),e.call(this,t,i),Array.isArray(n))for(var r=0;r<n.length;r++){var a=n[r],l=this._normalizeItem(a),c=this.option(l);this.$element.append(c)}}return t.prototype.query=function(e,t,i){var n=this;function s(e,o){for(var r=e.results,a=0;a<r.length;a++){var l=r[a],c=null!=l.children&&!s({results:l.children},!0);if((l.text||"").toUpperCase()===(t.term||"").toUpperCase()||c)return!o&&(e.data=r,void i(e))}if(o)return!0;var u=n.createTag(t);if(null!=u){var d=n.option(u);d.attr("data-select2-tag","true"),n.addOptions([d]),n.insertTag(r,u)}e.results=r,i(e)}this._removeOldTags(),null!=t.term&&null==t.page?e.call(this,t,s):e.call(this,t,i)},t.prototype.createTag=function(e,t){if(null==t.term)return null;var i=t.term.trim();return""===i?null:{id:i,text:i}},t.prototype.insertTag=function(e,t,i){t.unshift(i)},t.prototype._removeOldTags=function(t){this.$element.find("option[data-select2-tag]").each((function(){this.selected||e(this).remove()}))},t})),i.define("select2/data/tokenizer",["jquery"],(function(e){function t(e,t,i){var n=i.get("tokenizer");void 0!==n&&(this.tokenizer=n),e.call(this,t,i)}return t.prototype.bind=function(e,t,i){e.call(this,t,i),this.$search=t.dropdown.$search||t.selection.$search||i.find(".select2-search__field")},t.prototype.query=function(t,i,n){var s=this;function o(t){var i=s._normalizeItem(t);if(!s.$element.find("option").filter((function(){return e(this).val()===i.id})).length){var n=s.option(i);n.attr("data-select2-tag",!0),s._removeOldTags(),s.addOptions([n])}r(i)}function r(e){s.trigger("select",{data:e})}i.term=i.term||"";var a=this.tokenizer(i,this.options,o);a.term!==i.term&&(this.$search.length&&(this.$search.val(a.term),this.$search.trigger("focus")),i.term=a.term),t.call(this,i,n)},t.prototype.tokenizer=function(t,i,n,s){for(var o=n.get("tokenSeparators")||[],r=i.term,a=0,l=this.createTag||function(e){return{id:e.term,text:e.term}};a<r.length;){var c=r[a];if(-1!==o.indexOf(c)){var u=r.substr(0,a),d=l(e.extend({},i,{term:u}));null!=d?(s(d),r=r.substr(a+1)||"",a=0):a++}else a++}return{term:r}},t})),i.define("select2/data/minimumInputLength",[],(function(){function e(e,t,i){this.minimumInputLength=i.get("minimumInputLength"),e.call(this,t,i)}return e.prototype.query=function(e,t,i){t.term=t.term||"",t.term.length<this.minimumInputLength?this.trigger("results:message",{message:"inputTooShort",args:{minimum:this.minimumInputLength,input:t.term,params:t}}):e.call(this,t,i)},e})),i.define("select2/data/maximumInputLength",[],(function(){function e(e,t,i){this.maximumInputLength=i.get("maximumInputLength"),e.call(this,t,i)}return e.prototype.query=function(e,t,i){t.term=t.term||"",this.maximumInputLength>0&&t.term.length>this.maximumInputLength?this.trigger("results:message",{message:"inputTooLong",args:{maximum:this.maximumInputLength,input:t.term,params:t}}):e.call(this,t,i)},e})),i.define("select2/data/maximumSelectionLength",[],(function(){function e(e,t,i){this.maximumSelectionLength=i.get("maximumSelectionLength"),e.call(this,t,i)}return e.prototype.bind=function(e,t,i){var n=this;e.call(this,t,i),t.on("select",(function(){n._checkIfMaximumSelected()}))},e.prototype.query=function(e,t,i){var n=this;this._checkIfMaximumSelected((function(){e.call(n,t,i)}))},e.prototype._checkIfMaximumSelected=function(e,t){var i=this;this.current((function(e){var n=null!=e?e.length:0;i.maximumSelectionLength>0&&n>=i.maximumSelectionLength?i.trigger("results:message",{message:"maximumSelected",args:{maximum:i.maximumSelectionLength}}):t&&t()}))},e})),i.define("select2/dropdown",["jquery","./utils"],(function(e,t){function i(e,t){this.$element=e,this.options=t,i.__super__.constructor.call(this)}return t.Extend(i,t.Observable),i.prototype.render=function(){var t=e('<span class="sui-select-dropdown"><span class="select2-results"></span></span>');return t.attr("dir",this.options.get("dir")),this.$dropdown=t,t},i.prototype.bind=function(){},i.prototype.position=function(e,t){},i.prototype.destroy=function(){this.$dropdown.remove()},i})),i.define("select2/dropdown/search",["jquery"],(function(e){function t(){}return t.prototype.render=function(t){var i=t.call(this),n=this.options.get("translations").get("search"),s=e('<span class="select2-search select2-search--dropdown"><input class="select2-search__field" type="search" tabindex="-1" autocorrect="off" autocapitalize="none" spellcheck="false" role="searchbox" aria-autocomplete="list" /></span>');return this.$searchContainer=s,this.$search=s.find("input"),this.$search.prop("autocomplete",this.options.get("autocomplete")),this.$search.attr("aria-label",n()),i.prepend(s),i},t.prototype.bind=function(t,i,n){var s=this,o=i.id+"-results";t.call(this,i,n),this.$search.on("keydown",(function(e){s.trigger("keypress",e),s._keyUpPrevented=e.isDefaultPrevented()})),this.$search.on("input",(function(t){e(this).off("keyup")})),this.$search.on("keyup input",(function(e){s.handleSearch(e)})),i.on("open",(function(){s.$search.attr("tabindex",0),s.$search.attr("aria-controls",o),s.$search.trigger("focus"),window.setTimeout((function(){s.$search.trigger("focus")}),0)})),i.on("close",(function(){s.$search.attr("tabindex",-1),s.$search.removeAttr("aria-controls"),s.$search.removeAttr("aria-activedescendant"),s.$search.val(""),s.$search.trigger("blur")})),i.on("focus",(function(){i.isOpen()||s.$search.trigger("focus")})),i.on("results:all",(function(e){null!=e.query.term&&""!==e.query.term||(s.showSearch(e)?s.$searchContainer[0].classList.remove("select2-search--hide"):s.$searchContainer[0].classList.add("select2-search--hide"))})),i.on("results:focus",(function(e){e.data._resultId?s.$search.attr("aria-activedescendant",e.data._resultId):s.$search.removeAttr("aria-activedescendant")}))},t.prototype.handleSearch=function(e){if(!this._keyUpPrevented){var t=this.$search.val();this.trigger("query",{term:t})}this._keyUpPrevented=!1},t.prototype.showSearch=function(e,t){return!0},t})),i.define("select2/dropdown/hidePlaceholder",[],(function(){function e(e,t,i,n){this.placeholder=this.normalizePlaceholder(i.get("placeholder")),e.call(this,t,i,n)}return e.prototype.append=function(e,t){t.results=this.removePlaceholder(t.results),e.call(this,t)},e.prototype.normalizePlaceholder=function(e,t){return"string"==typeof t&&(t={id:"",text:t}),t},e.prototype.removePlaceholder=function(e,t){for(var i=t.slice(0),n=t.length-1;n>=0;n--){var s=t[n];this.placeholder.id===s.id&&i.splice(n,1)}return i},e})),i.define("select2/dropdown/infiniteScroll",["jquery"],(function(e){function t(e,t,i,n){this.lastParams={},e.call(this,t,i,n),this.$loadingMore=this.createLoadingMore(),this.loading=!1}return t.prototype.append=function(e,t){this.$loadingMore.remove(),this.loading=!1,e.call(this,t),this.showLoadingMore(t)&&(this.$results.append(this.$loadingMore),this.loadMoreIfNeeded())},t.prototype.bind=function(e,t,i){var n=this;e.call(this,t,i),t.on("query",(function(e){n.lastParams=e,n.loading=!0})),t.on("query:append",(function(e){n.lastParams=e,n.loading=!0})),this.$results.on("scroll",this.loadMoreIfNeeded.bind(this))},t.prototype.loadMoreIfNeeded=function(){var t=e.contains(document.documentElement,this.$loadingMore[0]);!this.loading&&t&&this.$results.offset().top+this.$results.outerHeight(!1)+50>=this.$loadingMore.offset().top+this.$loadingMore.outerHeight(!1)&&this.loadMore()},t.prototype.loadMore=function(){this.loading=!0;var t=e.extend({},{page:1},this.lastParams);t.page++,this.trigger("query:append",t)},t.prototype.showLoadingMore=function(e,t){return t.pagination&&t.pagination.more},t.prototype.createLoadingMore=function(){var t=e('<li class="select2-results__option select2-results__option--load-more"role="option" aria-disabled="true"></li>'),i=this.options.get("translations").get("loadingMore");return t.html(i(this.lastParams)),t},t})),i.define("select2/dropdown/attachBody",["jquery","../utils"],(function(e,t){function i(t,i,n){this.$dropdownParent=e(n.get("dropdownParent")||document.body),t.call(this,i,n)}return i.prototype.bind=function(e,t,i){var n=this;e.call(this,t,i),t.on("open",(function(){n._showDropdown(),n._attachPositioningHandler(t),n._bindContainerResultHandlers(t)})),t.on("close",(function(){n._hideDropdown(),n._detachPositioningHandler(t)})),this.$dropdownContainer.on("mousedown",(function(e){e.stopPropagation()}))},i.prototype.destroy=function(e){e.call(this),this.$dropdownContainer.remove()},i.prototype.position=function(e,t,i){t.attr("class",i.attr("class")),t.removeClass("select2"),t.addClass("sui-select-dropdown-container--open"),t[0].classList.remove("select2"),t[0].classList.add("select2-container--open"),t.css({position:"absolute",top:-999999}),this.$container=i},i.prototype.render=function(t){var i=e("<span></span>"),n=t.call(this);return i.append(n),this.$dropdownContainer=i,i},i.prototype._hideDropdown=function(e){this.$dropdownContainer.detach()},i.prototype._bindContainerResultHandlers=function(e,t){if(!this._containerResultsHandlersBound){var i=this;t.on("results:all",(function(){i._positionDropdown(),i._resizeDropdown()})),t.on("results:append",(function(){i._positionDropdown(),i._resizeDropdown()})),t.on("results:message",(function(){i._positionDropdown(),i._resizeDropdown()})),t.on("select",(function(){i._positionDropdown(),i._resizeDropdown()})),t.on("unselect",(function(){i._positionDropdown(),i._resizeDropdown()})),this._containerResultsHandlersBound=!0}},i.prototype._attachPositioningHandler=function(i,n){var s=this,o="scroll.select2."+n.id,r="resize.select2."+n.id,a="orientationchange.select2."+n.id,l=this.$container.parents().filter(t.hasScroll);l.each((function(){t.StoreData(this,"select2-scroll-position",{x:e(this).scrollLeft(),y:e(this).scrollTop()})})),l.on(o,(function(i){var n=t.GetData(this,"select2-scroll-position");e(this).scrollTop(n.y)})),e(window).on(o+" "+r+" "+a,(function(e){s._positionDropdown(),s._resizeDropdown()}))},i.prototype._detachPositioningHandler=function(i,n){var s="scroll.select2."+n.id,o="resize.select2."+n.id,r="orientationchange.select2."+n.id;this.$container.parents().filter(t.hasScroll).off(s),e(window).off(s+" "+o+" "+r)},i.prototype._positionDropdown=function(){var t=e(window),i=this.$dropdown[0].classList.contains("sui-select-dropdown--above"),n=this.$dropdown[0].classList.contains("sui-select-dropdown--below"),s=null,o=this.$container.offset();o.bottom=o.top+this.$container.outerHeight(!1);var r={height:this.$container.outerHeight(!1)};r.top=o.top,r.bottom=o.top+r.height;var a={height:this.$dropdown.outerHeight(!1)},l={top:t.scrollTop(),bottom:t.scrollTop()+t.height()},c=l.top<o.top-a.height,u=l.bottom>o.bottom+a.height,d={left:o.left,top:r.bottom},p=this.$dropdownParent;"static"===p.css("position")&&(p=p.offsetParent());var h={top:0,left:0};(e.contains(document.body,p[0])||p[0].isConnected)&&(h=p.offset()),d.top-=h.top,d.left-=h.left,i||n||(s="below"),u||!c||i?!c&&u&&i&&(s="below"):s="above",("above"==s||i&&"below"!==s)&&(d.top=r.top-h.top-a.height),null!=s&&(this.$dropdown[0].classList.remove("sui-select-dropdown--below"),this.$dropdown[0].classList.remove("sui-select-dropdown--above"),this.$dropdown[0].classList.add("sui-select-dropdown--"+s),this.$container[0].classList.remove("sui-select-container--below"),this.$container[0].classList.remove("sui-select-container--above"),this.$container[0].classList.add("sui-select-dropdown-container--"+s)),this.$dropdownContainer.css(d)},i.prototype._resizeDropdown=function(){var e={width:this.$container.outerWidth(!1)+"px"};this.options.get("dropdownAutoWidth")&&(e.minWidth=e.width,e.position="relative",e.width="auto"),this.$dropdown.css(e)},i.prototype._showDropdown=function(e){this.$dropdownContainer.appendTo(this.$dropdownParent),this._positionDropdown(),this._resizeDropdown()},i})),i.define("select2/dropdown/minimumResultsForSearch",[],(function(){function e(t){for(var i=0,n=0;n<t.length;n++){var s=t[n];s.children?i+=e(s.children):i++}return i}function t(e,t,i,n){this.minimumResultsForSearch=i.get("minimumResultsForSearch"),this.minimumResultsForSearch<0&&(this.minimumResultsForSearch=1/0),e.call(this,t,i,n)}return t.prototype.showSearch=function(t,i){return!(e(i.data.results)<this.minimumResultsForSearch)&&t.call(this,i)},t})),i.define("select2/dropdown/selectOnClose",["../utils"],(function(e){function t(){}return t.prototype.bind=function(e,t,i){var n=this;e.call(this,t,i),t.on("close",(function(e){n._handleSelectOnClose(e)}))},t.prototype._handleSelectOnClose=function(t,i){if(i&&null!=i.originalSelect2Event){var n=i.originalSelect2Event;if("select"===n._type||"unselect"===n._type)return}var s=this.getHighlightedResults();if(!(s.length<1)){var o=e.GetData(s[0],"data");null!=o.element&&o.element.selected||null==o.element&&o.selected||this.trigger("select",{data:o})}},t})),i.define("select2/dropdown/closeOnSelect",[],(function(){function e(){}return e.prototype.bind=function(e,t,i){var n=this;e.call(this,t,i),t.on("select",(function(e){n._selectTriggered(e)})),t.on("unselect",(function(e){n._selectTriggered(e)}))},e.prototype._selectTriggered=function(e,t){var i=t.originalEvent;i&&(i.ctrlKey||i.metaKey)||this.trigger("close",{originalEvent:i,originalSelect2Event:t})},e})),i.define("select2/dropdown/dropdownCss",["../utils"],(function(e){function t(){}return t.prototype.render=function(t){var i=t.call(this),n=this.options.get("dropdownCssClass")||"";return-1!==n.indexOf(":all:")&&(n=n.replace(":all:",""),e.copyNonInternalCssClasses(i[0],this.$element[0])),i.addClass("sui-select-dropdown"),i.addClass(n),i},t})),i.define("select2/dropdown/tagsSearchHighlight",["../utils"],(function(e){function t(){}return t.prototype.highlightFirstItem=function(t){var i=this.$results.find(".select2-results__option--selectable:not(.select2-results__option--selected)");if(i.length>0){var n=i.first(),s=e.GetData(n[0],"data").element;if(s&&s.getAttribute&&"true"===s.getAttribute("data-select2-tag"))return void n.trigger("mouseenter")}t.call(this)},t})),i.define("select2/i18n/en",[],(function(){return{errorLoading:function(){return"The results could not be loaded."},inputTooLong:function(e){var t=e.input.length-e.maximum,i="Please delete "+t+" character";return 1!=t&&(i+="s"),i},inputTooShort:function(e){return"Please enter "+(e.minimum-e.input.length)+" or more characters"},loadingMore:function(){return"Loading more results…"},maximumSelected:function(e){var t="You can only select "+e.maximum+" item";return 1!=e.maximum&&(t+="s"),t},noResults:function(){return"No results found"},searching:function(){return"Searching…"},removeAllItems:function(){return"Remove all items"},removeItem:function(){return"Remove item"},search:function(){return"Search"}}})),i.define("select2/defaults",["jquery","./results","./selection/single","./selection/multiple","./selection/placeholder","./selection/allowClear","./selection/search","./selection/selectionCss","./selection/eventRelay","./utils","./translation","./diacritics","./data/select","./data/array","./data/ajax","./data/tags","./data/tokenizer","./data/minimumInputLength","./data/maximumInputLength","./data/maximumSelectionLength","./dropdown","./dropdown/search","./dropdown/hidePlaceholder","./dropdown/infiniteScroll","./dropdown/attachBody","./dropdown/minimumResultsForSearch","./dropdown/selectOnClose","./dropdown/closeOnSelect","./dropdown/dropdownCss","./dropdown/tagsSearchHighlight","./i18n/en"],(function(e,t,i,n,s,o,r,a,l,c,u,d,p,h,f,g,m,v,y,b,w,_,S,$,A,x,C,L,I,D,N){function U(){this.reset()}return U.prototype.apply=function(u){if(null==(u=e.extend(!0,{},this.defaults,u)).dataAdapter&&(null!=u.ajax?u.dataAdapter=f:null!=u.data?u.dataAdapter=h:u.dataAdapter=p,u.minimumInputLength>0&&(u.dataAdapter=c.Decorate(u.dataAdapter,v)),u.maximumInputLength>0&&(u.dataAdapter=c.Decorate(u.dataAdapter,y)),u.maximumSelectionLength>0&&(u.dataAdapter=c.Decorate(u.dataAdapter,b)),u.tags&&(u.dataAdapter=c.Decorate(u.dataAdapter,g)),null==u.tokenSeparators&&null==u.tokenizer||(u.dataAdapter=c.Decorate(u.dataAdapter,m))),null==u.resultsAdapter&&(u.resultsAdapter=t,null!=u.ajax&&(u.resultsAdapter=c.Decorate(u.resultsAdapter,$)),null!=u.placeholder&&(u.resultsAdapter=c.Decorate(u.resultsAdapter,S)),u.selectOnClose&&(u.resultsAdapter=c.Decorate(u.resultsAdapter,C)),u.tags&&(u.resultsAdapter=c.Decorate(u.resultsAdapter,D))),null==u.dropdownAdapter){if(u.multiple)u.dropdownAdapter=w;else{var d=c.Decorate(w,_);u.dropdownAdapter=d}0!==u.minimumResultsForSearch&&(u.dropdownAdapter=c.Decorate(u.dropdownAdapter,x)),u.closeOnSelect&&(u.dropdownAdapter=c.Decorate(u.dropdownAdapter,L)),null!=u.dropdownCssClass&&(u.dropdownAdapter=c.Decorate(u.dropdownAdapter,I)),u.dropdownAdapter=c.Decorate(u.dropdownAdapter,A)}null==u.selectionAdapter&&(u.multiple?u.selectionAdapter=n:u.selectionAdapter=i,null!=u.placeholder&&(u.selectionAdapter=c.Decorate(u.selectionAdapter,s)),u.allowClear&&(u.selectionAdapter=c.Decorate(u.selectionAdapter,o)),u.multiple&&(u.selectionAdapter=c.Decorate(u.selectionAdapter,r)),null!=u.selectionCssClass&&(u.selectionAdapter=c.Decorate(u.selectionAdapter,a)),u.selectionAdapter=c.Decorate(u.selectionAdapter,l)),u.language=this._resolveLanguage(u.language),u.language.push("en");for(var N=[],U=0;U<u.language.length;U++){var E=u.language[U];-1===N.indexOf(E)&&N.push(E)}return u.language=N,u.translations=this._processTranslations(u.language,u.debug),u},U.prototype.reset=function(){function t(e){function t(e){return d[e]||e}return e.replace(/[^\u0000-\u007E]/g,t)}function i(n,s){if(null==n.term||""===n.term.trim())return s;if(s.children&&s.children.length>0){for(var o=e.extend(!0,{},s),r=s.children.length-1;r>=0;r--)null==i(n,s.children[r])&&o.children.splice(r,1);return o.children.length>0?o:i(n,o)}var a=t(s.text).toUpperCase(),l=t(n.term).toUpperCase();return a.indexOf(l)>-1?s:null}this.defaults={amdLanguageBase:"./i18n/",autocomplete:"off",closeOnSelect:!0,debug:!1,dropdownAutoWidth:!1,escapeMarkup:c.escapeMarkup,language:{},matcher:i,minimumInputLength:0,maximumInputLength:0,maximumSelectionLength:0,minimumResultsForSearch:0,selectOnClose:!1,scrollAfterSelect:!1,sorter:function(e){return e},templateResult:function(e){return e.text},templateSelection:function(e){return e.text},theme:"default",width:"resolve"}},U.prototype.applyFromElement=function(e,t){var i=e.language,n=this.defaults.language,s=t.prop("lang"),o=t.closest("[lang]").prop("lang"),r=Array.prototype.concat.call(this._resolveLanguage(s),this._resolveLanguage(i),this._resolveLanguage(n),this._resolveLanguage(o));return e.language=r,e},U.prototype._resolveLanguage=function(t){if(!t)return[];if(e.isEmptyObject(t))return[];if(e.isPlainObject(t))return[t];var i;i=Array.isArray(t)?t:[t];for(var n=[],s=0;s<i.length;s++)if(n.push(i[s]),"string"==typeof i[s]&&i[s].indexOf("-")>0){var o=i[s].split("-")[0];n.push(o)}return n},U.prototype._processTranslations=function(t,i){for(var n=new u,s=0;s<t.length;s++){var o=new u,r=t[s];if("string"==typeof r)try{o=u.loadPath(r)}catch(e){try{r=this.defaults.amdLanguageBase+r,o=u.loadPath(r)}catch(e){i&&window.console&&console.warn&&console.warn('Select2: The language file for "'+r+'" could not be automatically loaded. A fallback will be used instead.')}}else o=e.isPlainObject(r)?new u(r):r;n.extend(o)}return n},U.prototype.set=function(t,i){var n={};n[e.camelCase(t)]=i;var s=c._convertData(n);e.extend(!0,this.defaults,s)},new U})),i.define("select2/options",["jquery","./defaults","./utils"],(function(e,t,i){function n(e,i){this.options=e,null!=i&&this.fromElement(i),null!=i&&(this.options=t.applyFromElement(this.options,i)),this.options=t.apply(this.options)}return n.prototype.fromElement=function(t){var n=["select2"];null==this.options.multiple&&(this.options.multiple=t.prop("multiple")),null==this.options.disabled&&(this.options.disabled=t.prop("disabled")),null==this.options.autocomplete&&t.prop("autocomplete")&&(this.options.autocomplete=t.prop("autocomplete")),null==this.options.dir&&(t.prop("dir")?this.options.dir=t.prop("dir"):t.closest("[dir]").prop("dir")?this.options.dir=t.closest("[dir]").prop("dir"):this.options.dir="ltr"),t.prop("disabled",this.options.disabled),t.prop("multiple",this.options.multiple),i.GetData(t[0],"select2Tags")&&(this.options.debug&&window.console&&console.warn&&console.warn('Select2: The `data-select2-tags` attribute has been changed to use the `data-data` and `data-tags="true"` attributes and will be removed in future versions of Select2.'),i.StoreData(t[0],"data",i.GetData(t[0],"select2Tags")),i.StoreData(t[0],"tags",!0)),i.GetData(t[0],"ajaxUrl")&&(this.options.debug&&window.console&&console.warn&&console.warn("Select2: The `data-ajax-url` attribute has been changed to `data-ajax--url` and support for the old attribute will be removed in future versions of Select2."),t.attr("ajax--url",i.GetData(t[0],"ajaxUrl")),i.StoreData(t[0],"ajax-Url",i.GetData(t[0],"ajaxUrl")));var s={};function o(e,t){return t.toUpperCase()}for(var r=0;r<t[0].attributes.length;r++){var a=t[0].attributes[r].name,l="data-";if(a.substr(0,l.length)==l){var c=a.substring(l.length),u=i.GetData(t[0],c);s[c.replace(/-([a-z])/g,o)]=u}}e.fn.jquery&&"1."==e.fn.jquery.substr(0,2)&&t[0].dataset&&(s=e.extend(!0,{},t[0].dataset,s));var d=e.extend(!0,{},i.GetData(t[0]),s);for(var p in d=i._convertData(d))n.indexOf(p)>-1||(e.isPlainObject(this.options[p])?e.extend(this.options[p],d[p]):this.options[p]=d[p]);return this},n.prototype.get=function(e){return this.options[e]},n.prototype.set=function(e,t){this.options[e]=t},n})),i.define("select2/core",["jquery","./options","./utils","./keys"],(function(e,t,i,n){var s=function e(n,s){null!=i.GetData(n[0],"select2")&&i.GetData(n[0],"select2").destroy(),this.$element=n,this.id=this._generateId(n),s=s||{},this.options=new t(s,n),e.__super__.constructor.call(this);var o=n.attr("tabindex")||0;i.StoreData(n[0],"old-tabindex",o),n.attr("tabindex","-1");var r=this.options.get("dataAdapter");this.dataAdapter=new r(n,this.options);var a=this.render();this._placeContainer(a);var l=this.options.get("selectionAdapter");this.selection=new l(n,this.options),this.$selection=this.selection.render(),this.selection.position(this.$selection,a);var c=this.options.get("dropdownAdapter");this.dropdown=new c(n,this.options),this.$dropdown=this.dropdown.render(),this.dropdown.position(this.$dropdown,a);var u=this.options.get("resultsAdapter");this.results=new u(n,this.options,this.dataAdapter),this.$results=this.results.render(),this.results.position(this.$results,this.$dropdown);var d=this;this._bindAdapters(),this._registerDomEvents(),this._registerDataEvents(),this._registerSelectionEvents(),this._registerDropdownEvents(),this._registerResultsEvents(),this._registerEvents(),this.dataAdapter.current((function(e){d.trigger("selection:update",{data:e})})),n[0].classList.add("select2-hidden-accessible"),n.attr("aria-hidden","true"),n.addClass("sui-screen-reader-text"),this._syncAttributes(),i.StoreData(n[0],"select2",this),n.data("select2",this)};return i.Extend(s,i.Observable),s.prototype._generateId=function(e){return"select2-"+(null!=e.attr("id")?e.attr("id"):null!=e.attr("name")?e.attr("name")+"-"+i.generateChars(2):i.generateChars(4)).replace(/(:|\.|\[|\]|,)/g,"")},s.prototype._placeContainer=function(e){e.insertAfter(this.$element);var t=this._resolveWidth(this.$element,this.options.get("width"));null!=t&&e.css("width",t)},s.prototype._resolveWidth=function(e,t){var i=/^width:(([-+]?([0-9]*\.)?[0-9]+)(px|em|ex|%|in|cm|mm|pt|pc))/i;if("resolve"==t){var n=this._resolveWidth(e,"style");return null!=n?n:this._resolveWidth(e,"element")}if("element"==t){var s=e.outerWidth(!1);return s<=0?"auto":s+"px"}if("style"==t){var o=e.attr("style");if("string"!=typeof o)return null;for(var r=o.split(";"),a=0,l=r.length;a<l;a+=1){var c=r[a].replace(/\s/g,"").match(i);if(null!==c&&c.length>=1)return c[1]}return null}return"computedstyle"==t?window.getComputedStyle(e[0]).width:t},s.prototype._bindAdapters=function(){this.dataAdapter.bind(this,this.$container),this.selection.bind(this,this.$container),this.dropdown.bind(this,this.$container),this.results.bind(this,this.$container)},s.prototype._registerDomEvents=function(){var e=this;this.$element.on("change.select2",(function(){e.dataAdapter.current((function(t){e.trigger("selection:update",{data:t})}))})),this.$element.on("focus.select2",(function(t){e.trigger("focus",t)})),this._syncA=i.bind(this._syncAttributes,this),this._syncS=i.bind(this._syncSubtree,this),this._observer=new window.MutationObserver((function(t){e._syncA(),e._syncS(t)})),this._observer.observe(this.$element[0],{attributes:!0,childList:!0,subtree:!1})},s.prototype._registerDataEvents=function(){var e=this;this.dataAdapter.on("*",(function(t,i){e.trigger(t,i)}))},s.prototype._registerSelectionEvents=function(){var e=this,t=["toggle","focus"];this.selection.on("toggle",(function(){e.toggleDropdown()})),this.selection.on("focus",(function(t){e.focus(t)})),this.selection.on("*",(function(i,n){-1===t.indexOf(i)&&e.trigger(i,n)}))},s.prototype._registerDropdownEvents=function(){var e=this;this.dropdown.on("*",(function(t,i){e.trigger(t,i)}))},s.prototype._registerResultsEvents=function(){var e=this;this.results.on("*",(function(t,i){e.trigger(t,i)}))},s.prototype._registerEvents=function(){var t=this;this.on("open",(function(){t.$container[0].classList.add("select2-container--open")})),this.on("close",(function(){t.$container[0].classList.remove("select2-container--open")})),this.on("enable",(function(){t.$container[0].classList.remove("select2-container--disabled")})),this.on("disable",(function(){t.$container[0].classList.add("select2-container--disabled")})),this.on("blur",(function(){t.$container[0].classList.remove("select2-container--focus")})),this.on("query",(function(e){t.isOpen()||t.trigger("open",{}),this.dataAdapter.query(e,(function(i){t.trigger("results:all",{data:i,query:e})}))})),this.on("query:append",(function(e){this.dataAdapter.query(e,(function(i){t.trigger("results:append",{data:i,query:e})}))})),this.on("keypress",(function(i){var s=i.which,o=this.$element[0].hasAttribute("multiple");if(t.isOpen())s===n.ENTER?(t.trigger("results:select"),i.preventDefault()):s===n.SPACE&&i.ctrlKey?(t.trigger("results:toggle"),i.preventDefault()):s===n.UP?(t.trigger("results:previous"),i.preventDefault()):s===n.DOWN?(t.trigger("results:next"),i.preventDefault()):s!==n.ESC&&s!==n.TAB||(t.close(),i.preventDefault());else if(!o)if(s===n.ENTER||s===n.SPACE||(s===n.DOWN||s===n.UP)&&i.altKey)t.open(),i.preventDefault();else if(s===n.DOWN)null!=this.$element.find("option:selected").next().val()&&(this.$element.val(this.$element.find("option:selected").next().val()),this.$element.trigger("change")),i.preventDefault();else if(s===n.UP)null!=this.$element.find("option:selected").prev().val()&&(this.$element.val(this.$element.find("option:selected").prev().val()),this.$element.trigger("change")),i.preventDefault();else{var r=this.$element.find("option:selected").text(),a=String.fromCharCode(s).toLowerCase(),l=this.$element.find("option").filter((function(){var t;return null===(t=e(this).text())||void 0===t?void 0:t.toLowerCase().startsWith(a)})),c=l.length-1,u=r;l.each((function(t){return""!==r&&r[0].toLowerCase()===a?e(this).text()===r&&t!==c?(u=e(l[t+1]).val(),!1):void 0:(u=e(this).val(),!1)})),u!==r&&(t.$element.val(u),t.$element.trigger("change"))}}))},s.prototype._syncAttributes=function(){this.options.set("disabled",this.$element.prop("disabled")),this.isDisabled()?(this.isOpen()&&this.close(),this.trigger("disable",{})):this.trigger("enable",{})},s.prototype._isChangeMutation=function(e){var t=this;if(e.addedNodes&&e.addedNodes.length>0){for(var i=0;i<e.addedNodes.length;i++)if(e.addedNodes[i].selected)return!0}else{if(e.removedNodes&&e.removedNodes.length>0)return!0;if(Array.isArray(e))return e.some((function(e){return t._isChangeMutation(e)}))}return!1},s.prototype._syncSubtree=function(e){var t=this._isChangeMutation(e),i=this;t&&this.dataAdapter.current((function(e){i.trigger("selection:update",{data:e})}))},s.prototype.trigger=function(e,t){var i=s.__super__.trigger,n={open:"opening",close:"closing",select:"selecting",unselect:"unselecting",clear:"clearing"};if(void 0===t&&(t={}),e in n){var o=n[e],r={prevented:!1,name:e,args:t};if(i.call(this,o,r),r.prevented)return void(t.prevented=!0)}i.call(this,e,t)},s.prototype.toggleDropdown=function(){this.isDisabled()||(this.isOpen()?this.close():this.open())},s.prototype.open=function(){this.isOpen()||this.isDisabled()||this.trigger("query",{})},s.prototype.close=function(e){this.isOpen()&&this.trigger("close",{originalEvent:e})},s.prototype.isEnabled=function(){return!this.isDisabled()},s.prototype.isDisabled=function(){return this.options.get("disabled")},s.prototype.isOpen=function(){return this.$container[0].classList.contains("select2-container--open")},s.prototype.hasFocus=function(){return this.$container[0].classList.contains("select2-container--focus")},s.prototype.focus=function(e){this.hasFocus()||(this.$container[0].classList.add("select2-container--focus"),this.trigger("focus",{}))},s.prototype.enable=function(e){this.options.get("debug")&&window.console&&console.warn&&console.warn('Select2: The `select2("enable")` method has been deprecated and will be removed in later Select2 versions. Use $element.prop("disabled") instead.'),null!=e&&0!==e.length||(e=[!0]);var t=!e[0];this.$element.prop("disabled",t)},s.prototype.data=function(){this.options.get("debug")&&arguments.length>0&&window.console&&console.warn&&console.warn('Select2: Data can no longer be set using `select2("data")`. You should consider setting the value instead using `$element.val()`.');var e=[];return this.dataAdapter.current((function(t){e=t})),e},s.prototype.val=function(e){if(this.options.get("debug")&&window.console&&console.warn&&console.warn('Select2: The `select2("val")` method has been deprecated and will be removed in later Select2 versions. Use $element.val() instead.'),null==e||0===e.length)return this.$element.val();var t=e[0];Array.isArray(t)&&(t=t.map((function(e){return e.toString()}))),this.$element.val(t).trigger("input").trigger("change")},s.prototype.destroy=function(){i.RemoveData(this.$container[0]),this.$container.remove(),this._observer.disconnect(),this._observer=null,this._syncA=null,this._syncS=null,this.$element.off(".select2"),this.$element.attr("tabindex",i.GetData(this.$element[0],"old-tabindex")),this.$element.removeClass("sui-screen-reader-text"),this.$element[0].classList.remove("select2-hidden-accessible"),this.$element.attr("aria-hidden","false"),i.RemoveData(this.$element[0]),this.$element.removeData("select2"),this.dataAdapter.destroy(),this.selection.destroy(),this.dropdown.destroy(),this.results.destroy(),this.dataAdapter=null,this.selection=null,this.dropdown=null,this.results=null},s.prototype.render=function(){var t=e('<span class="select2 select2-container"><span class="selection"></span><span class="dropdown-wrapper" aria-hidden="true"></span></span>');return t.attr("dir",this.options.get("dir")),this.$container=t,this.$container[0].classList.add("sui-select"),"default"!==this.options.get("theme")&&this.$container[0].classList.add("sui-select-theme--"+this.options.get("theme")),i.StoreData(t[0],"element",this.$element),t},s})),i.define("jquery-mousewheel",["jquery"],(function(e){return e})),i.define("sui.select2",["jquery","jquery-mousewheel","./select2/core","./select2/defaults","./select2/utils"],(function(t,i,n,s,o){if(null==t.fn.SUIselect2){var r=["open","close","destroy"];t.fn.SUIselect2=function(i){if("object"===e(i=i||{}))return this.each((function(){var e=t.extend(!0,{},i);new n(t(this),e)})),this;if("string"==typeof i){var s,a=Array.prototype.slice.call(arguments,1);return this.each((function(){var e=o.GetData(this,"select2");null==e&&window.console&&console.error&&console.error("The SUIselect2('"+i+"') method was called on an element that is not using Select2."),s=e[i].apply(e,a)})),r.indexOf(i)>-1?this:s}throw new Error("Invalid arguments for SUIselect2: "+i)}}return null==t.fn.SUIselect2.defaults&&(t.fn.SUIselect2.defaults=s),n})),{define:i.define,require:i.require}}();i.require("sui.select2")}(jQuery)},1673:function(){function e(t){return e="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},e(t)}!function(t){"object"!==e(window.SUI)&&(window.SUI={}),SUI.select={},SUI.select.escapeJS=function(e){return t("<div>").html(e).text().replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi,"")},SUI.select.formatIcon=function(e,i){var n=SUI.select.escapeJS(e.text),s=t(e.element).attr("data-icon");return e.id?void 0!==s?'<span class="sui-icon-'+s.toLowerCase()+'" aria-hidden="true"></span> '+n:n:n},SUI.select.formatIconSelection=function(e,i){var n=SUI.select.escapeJS(e.text),s=t(e.element).attr("data-icon");return void 0!==s?'<span class="sui-icon-'+s.toLowerCase()+'" aria-hidden="true"></span> '+n:n},SUI.select.formatColor=function(e,i){var n,s,o=SUI.select.escapeJS(e.text),r=t(e.element).attr("data-color");if(!e.id)return o;if(void 0!==r){switch(r){case"#FFF":case"white":case"#FFFFFF":s="#000";break;case"#FAFAFA":case"#F8F8F8":case"#F2F2F2":s="#333";break;default:s=r}n='<span class="sui-color" style="border-color: '+s+"; background-color: "+r+';" aria-hidden="true"></span> '+o}else n=o;return n},SUI.select.formatColorSelection=function(e,i){var n,s=SUI.select.escapeJS(e.text),o=t(e.element).attr("data-color");if(void 0!==o){switch(o){case"#FFF":case"white":case"#FFFFFF":border="#000";break;case"#FAFAFA":case"#F8F8F8":case"#F2F2F2":border="#333";break;default:border=o}n='<span class="sui-color" style="border-color: '+border+"; background-color: "+o+';" aria-hidden="true"></span> '+s}else n=s;return n},SUI.select.formatVars=function(e,i){var n=SUI.select.escapeJS(e.text),s=t(e.element).val();return e.id?void 0!==s?'<span class="sui-variable-name">'+n+'</span><span class="sui-variable-value">'+s+"</span> ":n:n},SUI.select.formatVarsSelection=function(e,t){var i;return i='<span class="sui-icon-plus-circle sui-md" aria-hidden="true"></span>',i+='<span class="sui-screen-reader-text">'+SUI.select.escapeJS(e.text)+"</span>"},SUI.select.init=function(e){var i=e.closest(".sui-modal-content"),n=i.attr("id"),s=i.length?t("#"+n):t(".sui-2-12-23"),o="true"===e.attr("data-search")?0:-1,r=e.hasClass("sui-select-sm")?"sui-select-dropdown-sm":"";e.SUIselect2({dropdownParent:s,minimumResultsForSearch:o,dropdownCssClass:r})},SUI.select.initIcon=function(e){var i=e.closest(".sui-modal-content"),n=i.attr("id"),s=i.length?t("#"+n):t(".sui-2-12-23"),o="true"===e.attr("data-search")?0:-1,r=e.hasClass("sui-select-sm")?"sui-select-dropdown-sm":"";e.SUIselect2({dropdownParent:s,templateResult:SUI.select.formatIcon,templateSelection:SUI.select.formatIconSelection,escapeMarkup:function(e){return e},minimumResultsForSearch:o,dropdownCssClass:r})},SUI.select.initColor=function(e){var i=e.closest(".sui-modal-content"),n=i.attr("id"),s=i.length?t("#"+n):t(".sui-2-12-23"),o="true"===e.attr("data-search")?0:-1,r=e.hasClass("sui-select-sm")?"sui-select-dropdown-sm":"";e.SUIselect2({dropdownParent:s,templateResult:SUI.select.formatColor,templateSelection:SUI.select.formatColorSelection,escapeMarkup:function(e){return e},minimumResultsForSearch:o,dropdownCssClass:r})},SUI.select.initSearch=function(e){var i=e.closest(".sui-modal-content"),n=i.attr("id"),s=i.length?t("#"+n):t(".sui-2-12-23"),o=e.hasClass("sui-select-sm")?"sui-select-dropdown-sm":"";e.SUIselect2({dropdownParent:s,minimumInputLength:2,maximumSelectionLength:1,dropdownCssClass:o})},SUI.select.initVars=function(e){var i=e.closest(".sui-modal-content"),n=i.attr("id"),s=i.length?t("#"+n):t(".sui-2-12-23"),o="true"===e.attr("data-search")?0:-1;e.SUIselect2({theme:"vars",dropdownParent:s,templateResult:SUI.select.formatVars,templateSelection:SUI.select.formatVarsSelection,escapeMarkup:function(e){return e},minimumResultsForSearch:o}).on("select2:open",(function(){t(this).val(null)})),e.val(null)},t(".sui-select").each((function(){var e=t(this);e.hasClass("select2-hidden-accessible")||e.hasClass("select2")||("icon"===e.data("theme")?SUI.select.initIcon(e):"color"===e.data("theme")?SUI.select.initColor(e):"search"===e.data("theme")?SUI.select.initSearch(e):SUI.select.init(e))})),t(".sui-variables").each((function(){var e=t(this);e.hasClass("select2-hidden-accessible")||e.hasClass("select2")||SUI.select.initVars(e)}))}(jQuery)},1307:function(){function e(t){return e="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},e(t)}!function(t){"use strict";"object"!==e(window.SUI)&&(window.SUI={}),SUI.suiTabs=function(e){var t,i,n,s,o=["tab","pane"],r=[],a=[],l=[],c=[],u=[];function d(e,i){p(e,i),"function"==typeof t.callback&&t.callback(c.tab,c.pane)}function p(e,t){var d;for(n=e,s=t,d=0;d<o.length;d++)i=o[d],a[i]=r[i][n],l[i]=a[i].children,c[i]=l[i][s],h();u[e]=[],u[e][t]=!0}function h(){var e;for(e=0;e<l[i].length;e++)l[i][e].classList.remove(t[i+"Active"]);c[i].classList.add(t[i+"Active"])}function f(e,i){(t=t||[])[e]=t[e]||i}!function(e){var a,l,c;for(t=e,function(){var e;for(e=0;e<o.length;e++)f((i=o[e])+"Group","[data-"+i+"s]"),f(i+"Active","active")}(),r.tab=document.querySelectorAll(t.tabGroup),r.pane=document.querySelectorAll(t.paneGroup),a=0;a<r.tab.length;a++)for(l=r.tab[a].children,c=0;c<l.length;c++)l[c].addEventListener("click",d.bind(this,a,c),!1),n=a,s=c,window.location.hash&&window.location.hash.replace(/[^\w-_]/g,"")===l[c].id&&p(a,c)}(e)},SUI.tabsOverflow=function(e){var i=e.closest(".sui-tabs").find('[data-tabs], [role="tablist"]'),n=e.find(".sui-tabs-navigation--left"),s=e.find(".sui-tabs-navigation--right");function o(){return i[0].scrollWidth>i.width()?(0===i.scrollLeft()?n.addClass("sui-tabs-navigation--hidden"):n.removeClass("sui-tabs-navigation--hidden"),r(0),!0):(n.addClass("sui-tabs-navigation--hidden"),s.addClass("sui-tabs-navigation--hidden"),!1)}function r(e){var t,n;t=i.scrollLeft()+e,n=i.outerWidth(),i.get(0).scrollWidth-t<=n?s.addClass("sui-tabs-navigation--hidden"):s.removeClass("sui-tabs-navigation--hidden")}o(),n.on("click",(function(){return s.removeClass("sui-tabs-navigation--hidden"),0>=i.scrollLeft()-150&&n.addClass("sui-tabs-navigation--hidden"),i.animate({scrollLeft:"-=150"},400,(function(){})),!1})),s.on("click",(function(){return n.removeClass("sui-tabs-navigation--hidden"),r(150),i.animate({scrollLeft:"+=150"},400,(function(){})),!1})),t(window).on("resize",(function(){o()})),i.on("scroll",(function(){o()}))},SUI.tabs=function(e){var i,n=t('.sui-tabs > div[role="tablist"]'),s=e,o={end:35,home:36,left:37,up:38,right:39,down:40,delete:46,enter:13,space:32},r={37:-1,38:-1,39:1,40:1};if(n.length)return(i=n.closest(".sui-tabs")).each((function(){i=t(this),(n=i.find('> [role="tablist"]')).find('> [role="tab"]').on("click",(function(e){c(e)})).on("keydown",(function(e){!function(e,t,i){switch(e.keyCode||e.which){case o.end:case o.home:e.preventDefault();break;case o.up:case o.down:l(e,t,i)}}(e,t(this).index(),n)})).on("keyup",(function(e){!function(e,t,i){switch(e.keyCode||e.which){case o.left:case o.right:l(e,t,i);break;case o.enter:case o.space:a(e)}}(e,t(this).index(),n)}))})),this;function a(e){var i=t(e).closest('[role="tablist"]').find('[role="tab"]'),n=t(e).closest('[role="tablist"]').find('input[type="radio"]'),s=t(e).closest(".sui-tabs").find('> .sui-tabs-content > [role="tabpanel"]'),o=t(e).attr("aria-controls"),r=t(e).next('input[type="radio"]'),a=t("#"+o);!function(e,t,i){e.removeClass("active"),e.attr("tabindex","-1"),e.attr("aria-selected",!1),i.prop("checked",!1),t.removeClass("active"),t.prop("hidden",!0)}(i,s,n),t(e).addClass("active"),t(e).removeAttr("tabindex"),t(e).attr("aria-selected",!0),r.prop("checked",!0),a.addClass("active"),a.prop("hidden",!1)}function l(e,i,n){var s=e.keyCode||e.which,a=!1;"vertical"===t(n).attr("aria-orientation")?o.up!==s&&o.down!==s||(e.preventDefault(),a=!0):o.left!==s&&o.right!==s||(a=!0),!0===a&&function(e,i){var n,s,a;n=e.keyCode||e.which,r[n]&&(s=e.target,a=t(s).closest('[role="tablist"]').find('> [role="tab"]'),void 0!==i&&(a[i+r[n]]?a[i+r[n]].focus():o.left===n||o.up===n?a[a.length-1].focus():o.right!==n&&o.down!==n||a[0].focus()))}(e,i)}function c(e){var i=e.target;a(i),void 0!==s&&"undefined"!==s&&function(e){var i=t(e),n=i.attr("aria-controls"),o=t("#"+n);"function"==typeof s.callback&&s.callback(i,o)}(i),e.preventDefault(),e.stopPropagation()}},0!==t(".sui-2-12-23 .sui-tabs").length&&(SUI.tabs(),SUI.suiTabs(),t(".sui-2-12-23 .sui-tabs-navigation").each((function(){SUI.tabsOverflow(t(this))})))}(jQuery)},3826:function(){function e(t){return e="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},e(t)}!function(t){"use strict";"object"!==e(window.SUI)&&(window.SUI={}),SUI.upload=function(){if(t('.sui-2-12-23 .sui-upload-group input[type="file"]').on("change",(function(e){var i=t(this)[0].files[0],n=t(this).find("~ .sui-upload-message");i&&n.text(i.name)})),t(".sui-2-12-23 .sui-file-upload").length){t('.sui-2-12-23 .sui-file-browser input[type="file"]').on("change",(function(){var e=t(this).parent(),i=t(this).val(),n=e.find(".sui-upload-image");if(i){var s=i.lastIndexOf("\\");if(s>=0){if(i=i.substring(s+1),n.length){var o=new FileReader,r=n.find(".sui-image-preview");o.onload=function(e){r.attr("style","background-image: url("+e.target.result+" );")},o.readAsDataURL(t(this)[0].files[0])}e.find(".sui-upload-file > span").text(i),e.addClass("sui-has_file")}}else{if(n.length)(r=n.find(".sui-image-preview")).attr("style","background-image: url();");e.find(".sui-upload-file > span").text(""),e.removeClass("sui-has_file")}})),t(".sui-2-12-23 .sui-file-browser .sui-upload-button").on("click",(function(){o(t(this))})),t('.sui-2-12-23 .sui-file-upload [aria-label="Remove file"]').on("click",(function(){r(t(this))})),t(".sui-2-12-23 .sui-file-browser .sui-upload-image").on("click",(function(){o(t(this))}));var e=("draggable"in(a=document.createElement("div"))||"ondragstart"in a&&"ondrop"in a)&&"FormData"in window&&"FileReader"in window,i=t(".sui-2-12-23 .sui-upload-button");if(e){var n=!1;i.on("drag dragstart dragend dragover dragenter dragleave drop",(function(e){e.preventDefault(),e.stopPropagation()})).on("dragover dragenter",(function(){i.addClass("sui-is-dragover")})).on("dragleave dragend drop",(function(){i.removeClass("sui-is-dragover")})).on("drop",(function(e){n=e.originalEvent.dataTransfer.files,s(t(this),n[0],n[0].name)}))}var s=function(e,t,i){var n=e.closest(".sui-upload"),s=n.find(".sui-upload-image");if(i){if(s.length){var o=new FileReader,r=s.find(".sui-image-preview");o.onload=function(e){r.attr("style","background-image: url("+e.target.result+" );")},o.readAsDataURL(t)}n.find(".sui-upload-file > span").text(i),n.addClass("sui-has_file")}},o=function(e){e.closest(".sui-upload").find('input[type="file"]').trigger("click")},r=function(e){e.closest(".sui-upload").find('input[type="file"]').val("").change()}}var a},SUI.upload()}(jQuery)}},t={};function i(n){var s=t[n];if(void 0!==s)return s.exports;var o=t[n]={exports:{}};return e[n](o,o.exports,i),o.exports}i(3566),i(2579),i(2067),i(5990),i(1673),i(1307),i(3826),i(7094)}();
//# sourceMappingURL=smush-sui.min.js.map