!function(){var e={5417:function(e,t,n){"use strict";n.d(t,{R:function(){return i},U:function(){return s}});var s={maybeShowCDNActivationNotice:function(){wp_smush_msgs.smush_cdn_activation_notice&&WP_Smush.helpers.renderActivationCDNNotice(wp_smush_msgs.smush_cdn_activation_notice)},maybeShowCDNUpsellForPreSiteOnStart:function(){var e=document.querySelector(".wp-smush-upsell-cdn");e&&(e.querySelector("p").innerHTML=wp_smush_msgs.processing_cdn_for_free,e.classList.remove("sui-hidden"))},maybeShowCDNUpsellForPreSiteOnCompleted:function(){var e=document.querySelector(".wp-smush-upsell-cdn");e&&(e.querySelector("p").innerHTML=wp_smush_msgs.processed_cdn_for_free,e.classList.remove("sui-hidden"))}},i=function(){var e=document.querySelector.bind(document),t=e(".sui-summary-smush-metabox");if(!t)return{};var n=window.wp_smushit_data.bo_stats,i={count_images:0,count_total:0,count_resize:0,count_skipped:0,count_smushed:0,savings_bytes:0,savings_resize:0,size_after:0,size_before:0,savings_percent:0,percent_grade:"sui-grade-dismissed",percent_metric:0,percent_optimized:0,remaining_count:0,human_bytes:"",savings_conversion_human:"",savings_conversion:0},o=e("#smush-image-score"),r=e(".smush-final-log .smush-bulk-errors"),a=e("#wp-smush-bulk-content"),u={},l=function(e){window.wp_smushit_data=Object.assign(window.wp_smushit_data,e||{}),i=Object.keys(i).reduce((function(e,t){return t in window.wp_smushit_data&&(e[t]=window.wp_smushit_data[t]),e}),{})};return l(window.wp_smushit_data),{isChangedStats:function(e){return["total_items","processed_items","failed_items","is_cancelled","is_completed","is_dead"].some((function(t){return e[t]!==n[t]}))},setBoStats:function(e){return n=Object.assign(n,e||{}),this},getBoStats:function(){return n},setGlobalStats:function(e){return i=Object.assign(i,e||{}),this},getGlobalStats:function(){return i},renderScoreProgress:function(){o.className=o.className.replace(/(^|\s)sui-grade-\S+/g,""),o.classList.add(i.percent_grade),o.dataset.score=i.percent_optimized,o.querySelector(".sui-circle-score-label").innerHTML=i.percent_optimized,o.querySelector("circle:last-child").setAttribute("style","--metric-array:"***************i.percent_metric+" "+(263.893782902-i.percent_metric))},renderSummaryDetail:function(){this.renderTotalStats(),this.renderResizedStats(),this.renderConversionSavings()},renderTotalStats:function(){t.querySelector(".sui-summary-large.wp-smush-stats-human").innerHTML=i.human_bytes,t.querySelector(".wp-smush-savings .wp-smush-stats-percent").innerHTML=i.savings_percent,t.querySelector(".wp-smush-count-total .wp-smush-total-optimised").innerHTML=i.count_images},renderResizedStats:function(){var e=t.querySelector(".wp-smush-count-resize-total");e&&(i.count_resize>0?e.classList.remove("sui-hidden"):e.classList.add("sui-hidden"),e.querySelector(".wp-smush-total-optimised").innerHTML=i.count_resize)},renderConversionSavings:function(){var e=t.querySelector(".smush-conversion-savings .wp-smush-stats");e&&(e.innerHTML=i.savings_conversion_human,i.savings_conversion>0?e.parentElement.classList.remove("sui-hidden"):e.parentElement.classList.add("sui-hidden"))},renderBoxSummary:function(){this.renderScoreProgress(),this.renderSummaryDetail()},setErrors:function(e){u=e||{}},getErrors:function(){return u},renderErrors:function(){if(Object.keys(u).length&&n.is_completed){var t=[],i=Object.keys(u),o=!1;i.map((function(e,n){var s=u[e].error_code;n<5&&"animated"===s&&(o=!0),t.push(WP_Smush.helpers.prepareBulkSmushErrorRow(u[e].error_message,u[e].file_name,u[e].thumbnail,e,"media",u[e].error_code))})),r.innerHTML=t.join(""),r.parentElement.classList.remove("sui-hidden"),r.parentElement.style.display=null,i.length>1&&e(".smush-bulk-errors-actions").classList.remove("sui-hidden"),o&&s.maybeShowCDNActivationNotice()}},resetAndHideBulkErrors:function(){r&&(this.resetErrors(),r.parentElement.classList.add("sui-hidden"),r.innerHTML="")},resetErrors:function(){u={}},renderStats:function(){this.renderBoxSummary(),this.renderErrors()},maybeUpdateBulkSmushCountContent:function(e){e&&a&&(a.innerHTML=e)},updateGlobalStatsFromSmushScriptData:function(e){return this.maybeUpdateBulkSmushCountContent(null==e?void 0:e.content),l(e),this}}}()},5630:function(e,t,n){"use strict";n.r(t),n.d(t,{default:function(){return c}});var s=n(9620),i=n(7119),o=n(5417),r=n(5326);function a(e){return a="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},a(e)}function u(e,t){for(var n=0;n<t.length;n++){var s=t[n];s.enumerable=s.enumerable||!1,s.configurable=!0,"value"in s&&(s.writable=!0),Object.defineProperty(e,l(s.key),s)}}function l(e){var t=function(e,t){if("object"!=a(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var s=n.call(e,t||"default");if("object"!=a(s))return s;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==a(t)?t:t+""}wp.i18n.__;var c=function(){return e=function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.autoSyncDuration=1500,this.progressTimeoutId=0,this.scanProgress=(0,i.u)(this.autoSyncDuration)},t=[{key:"startScan",value:function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]&&arguments[0];this.onStart();var n=t?"smush":"scan";r.A.performTest().then((function(i){(null==i?void 0:i.loopback)?s.A.scanMediaLibrary.start(t).then((function(t){if(null==t||!t.success)return e.showFailureNotice(t),void e.onStartFailure(t);e.showProgressBar().autoSyncStatus()})):(e.showLoopbackErrorModal(n),e.onStartFailure(i))})).catch((function(t){console.error("Error:",t),e.showLoopbackErrorModal(n),e.onStartFailure(t)}))}},{key:"onStart",value:function(){}},{key:"onStartFailure",value:function(e){}},{key:"showFailureNotice",value:function(e){WP_Smush.helpers.showNotice(e,{showdismiss:!0,autoclose:!1})}},{key:"showLoopbackErrorModal",value:function(e){var t=document.getElementById("smush-loopback-error-dialog");t&&window.SUI&&(t.dataset.processType=e||"scan",WP_Smush.helpers.showModal(t.id))}},{key:"showProgressBar",value:function(){return this.onShowProgressBar(),this.scanProgress.reset().setOnCancelCallback(this.showStopScanningModal.bind(this)).open(),this}},{key:"onShowProgressBar",value:function(){}},{key:"showStopScanningModal",value:function(){window.SUI&&(this.onShowStopScanningModal(),window.SUI.openModal("smush-stop-scanning-dialog","wpbody-content",void 0,!1))}},{key:"onShowStopScanningModal",value:function(){this.registerCancelProcessEvent()}},{key:"registerCancelProcessEvent",value:function(){var e=document.querySelector(".smush-stop-scanning-dialog-button");e&&e.addEventListener("click",this.cancelProgress.bind(this),{once:!0})}},{key:"closeStopScanningModal",value:function(){if(window.SUI){var e=document.querySelector("#smush-stop-scanning-dialog");!e||!e.classList.contains("sui-content-fade-in")||window.SUI.closeModal("smush-stop-scanning-dialog")}}},{key:"closeProgressBar",value:function(){this.onCloseProgressBar(),this.scanProgress.close()}},{key:"onCloseProgressBar",value:function(){}},{key:"updateProgress",value:function(e){var t=this.getTotalItems(e),n=this.getProcessedItems(e);return this.scanProgress.update(n,t)}},{key:"getProcessedItems",value:function(e){return(null==e?void 0:e.processed_items)||0}},{key:"getTotalItems",value:function(e){return(null==e?void 0:e.total_items)||0}},{key:"cancelProgress",value:function(){var e=this;return this.scanProgress.setCancelButtonOnCancelling(),s.A.scanMediaLibrary.cancel().then((function(t){null!=t&&t.success?e.onCancelled(t.data):e.onCancelFailure(t)}))}},{key:"onCancelFailure",value:function(e){WP_Smush.helpers.showNotice(e,{showdismiss:!0,autoclose:!1}),this.scanProgress.resetCancelButtonOnFailure()}},{key:"onDead",value:function(e){this.clearProgressTimeout(),this.closeProgressBar(),this.closeStopScanningModal(),this.showRetryScanModal()}},{key:"showRetryScanModal",value:function(){var e=document.getElementById("smush-retry-scan-notice");window.SUI&&e&&(e.querySelector(".smush-retry-scan-notice-button").addEventListener("click",(function(e){window.SUI.closeModal("smush-retry-scan-notice");var t=document.querySelector(".wp-smush-scan");t&&(e.preventDefault(),t.click())}),{once:!0}),window.SUI.openModal("smush-retry-scan-notice","wpbody-content",void 0,!1))}},{key:"onCompleted",value:function(e){this.onFinish(e)}},{key:"onCancelled",value:function(e){this.onFinish(e)}},{key:"onFinish",value:function(e){this.clearProgressTimeout();var t=null==e?void 0:e.global_stats;this.updateGlobalStatsAndBulkContent(t),this.closeProgressBar(),this.closeStopScanningModal()}},{key:"clearProgressTimeout",value:function(){this.progressTimeoutId&&clearTimeout(this.progressTimeoutId)}},{key:"updateGlobalStatsAndBulkContent",value:function(e){e&&(o.R.updateGlobalStatsFromSmushScriptData(e),o.R.renderStats())}},{key:"getStatus",value:function(){return s.A.scanMediaLibrary.getScanStatus()}},{key:"autoSyncStatus",value:function(){var e=this,t=(new Date).getTime();this.getStatus().then((function(n){if(null!=n&&n.success){var s=n.data;s.is_dead?e.onDead(n.data):(e.beforeUpdateStatus(s),e.updateProgress(s).then((function(){e.scanProgress.increaseDurationToHaveChangeOnProgress((new Date).getTime()-t),(null==s?void 0:s.is_completed)?e.onCompleted(s):(null==s?void 0:s.is_cancelled)?e.onCancelled(s):e.progressTimeoutId=setTimeout((function(){return e.autoSyncStatus()}),e.autoSyncDuration)})))}}))}},{key:"beforeUpdateStatus",value:function(){}},{key:"setInnerText",value:function(e,t){e&&(e.dataset.originalText=e.dataset.originalText||e.innerText.trim(),e.innerText=t)}},{key:"revertInnerText",value:function(e){e&&e.dataset.originalText&&(e.innerText=e.dataset.originalText.trim())}},{key:"hideAnElement",value:function(e){e&&e.classList.add("sui-hidden")}},{key:"showAnElement",value:function(e){e&&e.classList.remove("sui-hidden")}}],t&&u(e.prototype,t),n&&u(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e;var e,t,n}()},7119:function(e,t,n){"use strict";n.d(t,{u:function(){return s}});var s=function(e){var t,n=wp.i18n,s=n.__,i=n._n,o=document.querySelector(".wp-smush-scan-progress-bar-wrapper"),r=o.querySelector(".wp-smush-progress-percent"),a=o.querySelector(".wp-smush-progress-inner"),u=o.querySelector(".wp-smush-remaining-time"),l=o.querySelector(".wp-smush-cancel-scan-progress-btn"),c=o.querySelector(".wp-smush-scan-hold-on-notice"),d=function(){},h=0,p=e-300;o.style.setProperty("--progress-transition-duration",p/1e3+"s");var f=(null===(t=window.wp_smushit_data)||void 0===t||null===(t=t.media_library_scan)||void 0===t?void 0:t.processed_items)||0,m=[],g=e,v=10*e;return{update:function(e,t){this.updateRemainingTime(e,t);var n=t&&Math.floor(e/t*100)||0;n=Math.min(n,100);var s=a.style.width;return s=s&&s.replace("%","")||0,a.style.width=n+"%",this.animateProgressBar(s,n)},animateProgressBar:function(e,t){return h&&clearInterval(h),new Promise((function(n){h=setInterval((function(){r.innerHTML=e+"%",++e>t&&(n(),clearInterval(h))}),p/(t-e))}))},updateRemainingTime:function(e,t){if(u){var n=(this.calcProcessTimePerItem(e)||500)*(t-e);u.innerText=this.formatTime(n)}},calcProcessTimePerItem:function(t){if(t){if((f=f<=t?f:0)!=t){var n=Math.floor(g/(t-f));f=t,m.push(n),this.resetDurationToHaveChangeOnProgress()}else this.increaseDurationToHaveChangeOnProgress(e);if(m.length)return m.reduce((function(e,t){return e+t}),0)/m.length}},increaseDurationToHaveChangeOnProgress:function(e){(g+=e)>v&&this.showHoldOnNotice()},showHoldOnNotice:function(){c.classList.remove("sui-hidden"),v=1e8},resetHoldOnNoticeVisibility:function(){c.classList.add("sui-hidden")},resetDurationToHaveChangeOnProgress:function(){g=e},formatTime:function(e){var t=Math.floor((e+p)/1e3),n=t%60,s=Math.floor(t/60),o="";return s&&(o+=s+" "+i("minute","minutes",s,"wp-smushit")),(o+=" "+n+" "+i("second","seconds",n,"wp-smushit")).trim()},reset:function(){return a.style.width="0%",r.innerHTML="0%",this.resetCancelButton(),this.resetHoldOnNoticeVisibility(),this},open:function(){l.onclick=d,o.classList.remove("sui-hidden")},close:function(){o.classList.add("sui-hidden"),this.reset()},setOnCancelCallback:function(e){if("function"==typeof e)return d=e,this},setCancelButtonLabel:function(e){return l.textContent=e,this},setCancelButtonOnCancelling:function(){this.setCancelButtonLabel(wp_smush_msgs.cancelling),this.setOnCancelCallback((function(){return!1})),l.setAttribute("disabled",!0)},resetCancelButton:function(){this.setOnCancelCallback((function(){})),this.resetCancelButtonLabel(),l.removeAttribute("disabled")},resetCancelButtonLabel:function(){this.setCancelButtonLabel(s("Cancel Scan","wp-smushit"))},resetCancelButtonOnFailure:function(){this.resetCancelButtonLabel(),l.removeAttribute("disabled")}}};t.A=new function(){var e=document.querySelector(".wp-smush-bulk-progress-bar-wrapper");if(!e)return{isEmptyObject:!0};var t=e.querySelector(".wp-smush-cancel-btn"),n=document.querySelector(".wp-smush-bulk-wrapper"),s=e.querySelector("#wp-smush-running-notice"),i=document.querySelector(".wp-smush-all-done"),o=document.getElementById("smush-stop-bulk-smush-modal"),r=!1,a=function(){};return{update:function(t,n){var s=n&&Math.floor(t/n*100)||0;s=Math.min(s,100),e.querySelector(".wp-smush-images-percent").innerHTML=s+"%",e.querySelector(".wp-smush-progress-inner").style.width=s+"%";var i=e.querySelector(".sui-progress-state-text");return i.firstElementChild.innerHTML=t,i.lastElementChild.innerHTML=n,this},close:function(){return e.classList.add("sui-hidden"),this.setCancelButtonLabel(window.wp_smush_msgs.cancel).setOnCancelCallback((function(){})).update(0,0),this.resetOriginalNotice(),this.closeStopBulkSmushModal(),this},show:function(){e.classList.remove("sui-hidden"),t.onclick=this.showStopBulkSmushModal.bind(this),this.hideBulkSmushDescription(),this.hideBulkSmushAllDone(),this.hideRecheckImagesNotice()},showStopBulkSmushModal:function(){if(o){o.querySelector(".smush-stop-bulk-smush-button").addEventListener("click",a,{once:!0});var e=o.id;window.SUI.openModal(e,"wpbody-content",undefined,!1,!1,!0)}},closeStopBulkSmushModal:function(){window.SUI&&(!o||!o.classList.contains("sui-content-fade-in")||window.SUI.closeModal(o.id))},setCancelButtonLabel:function(e){return t.textContent=e,this},showBulkSmushDescription:function(){n.classList.remove("sui-hidden")},hideBulkSmushDescription:function(){n.classList.add("sui-hidden")},showBulkSmushAllDone:function(){i.classList.remove("sui-hidden")},hideBulkSmushAllDone:function(){i.classList.add("sui-hidden")},hideState:function(){return r||(r=!0,e.querySelector(".sui-progress-state").classList.add("sui-hidden")),this},showState:function(){return r?(r=!1,e.querySelector(".sui-progress-state").classList.remove("sui-hidden"),this):this},setNotice:function(e){var t=s.querySelector(".sui-notice-message p");return this.cacheOriginalNotice(t),t.innerHTML=e,this},cacheOriginalNotice:function(e){s.dataset.progressMessage||(s.dataset.progressMessage=e.innerHTML)},resetOriginalNotice:function(){s.dataset.progressMessage&&(s.querySelector(".sui-notice-message p").innerHTML=s.dataset.progressMessage)},hideBulkProcessingNotice:function(){return s.classList.add("sui-hidden"),this},showBulkProcessingNotice:function(){return s.classList.remove("sui-hidden"),this},setCountUnitText:function(t){e.querySelector(".sui-progress-state-unit").innerHTML=t},setOnCancelCallback:function(e){if("function"==typeof e)return a=e,this},disableExceedLimitMode:function(){e.classList.remove("wp-smush-exceed-limit"),e.querySelector("#bulk-smush-resume-button").classList.add("sui-hidden")},hideRecheckImagesNotice:function(){var e=document.querySelector(".wp-smush-recheck-images-notice-box");e&&e.classList.add("sui-hidden")}}}},5326:function(e,t,n){"use strict";var s=n(9620);function i(e){return i="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},i(e)}function o(e,t){for(var n=0;n<t.length;n++){var s=t[n];s.enumerable=s.enumerable||!1,s.configurable=!0,"value"in s&&(s.writable=!0),Object.defineProperty(e,r(s.key),s)}}function r(e){var t=function(e,t){if("object"!=i(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var s=n.call(e,t||"default");if("object"!=i(s))return s;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==i(t)?t:t+""}var a=function(){return e=function e(){var t,n,s;!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),t=this,s=5e3,(n=r(n="delayTimeOnFailure"))in t?Object.defineProperty(t,n,{value:s,enumerable:!0,configurable:!0,writable:!0}):t[n]=s},(t=[{key:"performTest",value:function(){var e=this;return new Promise((function(t,n){e.startTest().then((function(s){null!=s&&s.success?e.getResult(t,(function(){setTimeout((function(){e.getResult(t,n,n)}),e.delayTimeOnFailure)}),n):n(s)})).catch((function(e){n(e)}))}))}},{key:"startTest",value:function(){return s.A.background.backgroundHealthyCheck()}},{key:"getResult",value:function(e,t,n){return this.fetchResult().then((function(n){var s=null==n?void 0:n.data;null!=n&&n.success&&null!=s&&s.loopback?e(s):t(n)})).catch((function(e){n(e)}))}},{key:"fetchResult",value:function(){return s.A.background.backgroundHealthyStatus()}}])&&o(e.prototype,t),n&&o(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e;var e,t,n}();t.A=new a},596:function(){jQuery((function(e){"use strict";e("body").on("click","a.smush-stats-details",(function(t){if(e(this).prop("disabled"))return!1;t.preventDefault();var n=e(this).find(".stats-toggle");e(this).parents().eq(1).find(".smush-stats-wrapper").slideToggle(),n.text("+"==n.text()?"-":"+")}))}))},3908:function(e,t,n){"use strict";n.r(t);var s=n(9982),i=n(5417),o=n(7119),r=function(e,t){void 0===t&&(t=100),e.fadeTo(t,0,(function(){e.slideUp(t,(function(){e.remove()}))}))};jQuery((function(e){var t=function(t,n,i,o){if(t.preventDefault(),!n.attr("disabled")){e(".wp-smush-error").remove(),e(".smush-stats-wrapper").hide();var r="grid";"smush_restore_image"===i&&(r=e(document).find("div.media-modal.wp-core-ui").length>0||window.location.search.indexOf("item")>-1?"grid":"list");var a={action:i,attachment_id:n.data("id"),mode:r,_nonce:n.data("nonce")};!function(e){var t=e.parent();t.css({opacity:"0.5"}),t.find("a").prop("disabled",!0)}(n);var u=n.html();n.html('<span class="spinner wp-smush-progress">'+wp_smush_msgs[o]+"</span>"),e.post(ajaxurl,a,(function(e){if(function(e){var t=e.parent();t.css({opacity:"1"}),t.find("a").prop("disabled",!1)}(n),e.success&&void 0!==e.data){if(void 0!==this.data&&this.data.indexOf("nextgen")>-1)n.parents().eq(1).html(e.data.stats);else if("restore"===o)n.parents().eq(1).html(e.data.stats);else{var t=n.parents().eq(1);wp_smush_msgs.failed_item_smushed&&t.hasClass("smush-failed-processing")?(t.html('<p class="smush-status smush-success">'+wp_smush_msgs.failed_item_smushed+"</p>"),setTimeout((function(){t.html(e.data)}),2e3)):t.html(e.data)}void 0!==e.data&&"restore"===o&&s.A.updateImageStats(e.data.new_size)}else e.data&&e.data.error_msg&&(-1===this.data.indexOf("nextgen")?n.closest(".smushit").find(".smush-status").addClass("smush-warning").html(e.data.error_msg):n.parent().append(e.data.error_msg),n.attr("disabled",!0),n.html(u))}))}},n=function(e){if(e.notice){var t="success";void 0!==e.noticeType&&(t=e.noticeType),window.SUI.openNotice("wp-smush-ajax-notice","<p>"+e.notice+"</p>",{type:t,icon:"check-tick"})}},a=function(e){o.A.isEmptyObject||(o.A.update(0,e.remaining_count),e.remaining_count<1?(o.A.hideBulkSmushDescription(),o.A.showBulkSmushAllDone()):(o.A.showBulkSmushDescription(),o.A.hideBulkSmushAllDone()))},u=function(t){e(t).length>0&&e("html, body").animate({scrollTop:e(t).offset().top-100},"slow")};if(e("#all-image-sizes").on("change",(function(){e('input[name^="wp-smush-image_sizes"]').prop("checked",!0)})),e(".sui-mobile-nav").on("change",(function(t){window.location.assign(e(t.currentTarget).val())})),e("#update-api-status").on("click",(function(t){t.preventDefault(),e(this).addClass("sui-button-onload"),e.post(ajaxurl,{action:"recheck_api_status"},(function(){location.reload()}))})),e("body").on("click",".wp-smush-send:not(.wp-smush-resmush)",(function(t){t.preventDefault(),new s.A(e(this),!1)})),e("body").on("click",".wp-smush-remove-skipped",(function(t){t.preventDefault();var n=e(this);e.post(ajaxurl,{action:"remove_from_skip_list",id:n.attr("data-id"),_ajax_nonce:n.attr("data-nonce")}).done((function(e){e.success&&void 0!==e.data.html&&n.parent().parent().html(e.data.html)}))})),e("body").on("click",".wp-smush-action.wp-smush-restore",(function(n){var s=e(this);t(n,s,"smush_restore_image","restore")})),e("body").on("click",".wp-smush-action.wp-smush-resmush",(function(n){t(n,e(this),"smush_resmush_image","smushing")})),e("body").on("click",".wp-smush-action.wp-smush-nextgen-restore",(function(n){t(n,e(this),"smush_restore_nextgen_image","restore")})),e("body").on("click",".wp-smush-action.wp-smush-nextgen-resmush",(function(n){t(n,e(this),"smush_resmush_nextgen_image","smushing")})),e(".wp-smush-scan").on("click",(function(t){t.preventDefault(),e(this).hasClass("wp-smush-background-scan")||function(t){var s=e(".wp-smush-scan");s.addClass("sui-button-onload");var o=s.data("type");o=void 0===o?"media":o,e(".wp-smush-all").removeAttr("data-smush"),e(".wp-smush-all").prop("disabled",!0),e(".wp-smush-settings-changed").hide();var r={action:"scan_for_resmush",type:o,get_ui:!0,process_settings:t,wp_smush_options_nonce:jQuery("#wp_smush_options_nonce").val()};e.get(ajaxurl,r,(function(e){if(null!=e&&e.success){var t=e.data;n(t),i.R.updateGlobalStatsFromSmushScriptData(t),i.R.renderStats(),a(t)}else WP_Smush.helpers.showNotice(e,{showdismiss:!0,autoclose:!1})})).always((function(){jQuery(".bulk-smush-wrapper .wp-smush-bulk-progress-bar-wrapper").addClass("sui-hidden"),s.removeClass("sui-button-onload").addClass("smush-button-check-success");var t=s.find(".wp-smush-default-text"),n=s.find(".wp-smush-completed-text");t.addClass("sui-hidden-important"),n.removeClass("sui-hidden"),setTimeout((function(){s.removeClass("smush-button-check-success"),t.removeClass("sui-hidden-important"),n.addClass("sui-hidden")}),2e3),e(".wp-smush-all").prop("disabled",!1)}))}(!1)})),e("body").on("click",".wp-smush-notice .icon-fi-close",(function(t){t.preventDefault();var n=e(this).parent();r(n)})),e("a.wp-smush-lossy-enable").on("click",(function(e){e.preventDefault(),u("#column-lossy")})),e(".wp-smush-resize-enable").on("click",(function(e){e.preventDefault(),u("#column-resize")})),window.location.hash){var l="";switch(window.location.hash.substring(1)){case"enable-resize":l="#column-resize";break;case"backup-label":l="#backup";break;case"original-label":l="#original";break;case"enable-lossy":l="#column-lossy"}""!==l&&(u(l),document.getElementById(l.replace("#","")).focus())}if(e("body").on("click",".wp-smush-trigger-bulk",(function(t){t.preventDefault(),void 0!==t.target.dataset.type&&"nextgen"===t.target.dataset.type?e(".wp-smush-nextgen-bulk").trigger("click"):e(".wp-smush-all").trigger("click"),e("span.sui-notice-dismiss").trigger("click")})),e("body").on("click","#bulk-smush-top-notice-close",(function(t){t.preventDefault(),e(this).parent().parent().slideUp("slow")})),e(".wp-smush-setting-row .toggle-checkbox").on("focus",(function(){e(this).keypress((function(t){32==t.keyCode&&(t.preventDefault(),e(this).find(".toggle-checkbox").trigger("click"))}))})),e("body").on("blur",".wp-smush-resize-input",(function(){!function(e,t,n){var s=e.find("#resize");if(!n)var i=e.find("#wp-smush-resize_width"),o=e.find(".sui-notice-info.wp-smush-update-width");if(!t)var r=e.find("#wp-smush-resize_height"),a=e.find(".sui-notice-info.wp-smush-update-height");var u=!1,l=!1;!s.is(":checked")||void 0===wp_smushit_data.resize_sizes||void 0===wp_smushit_data.resize_sizes.width||(!n&&void 0!==i&&parseInt(wp_smushit_data.resize_sizes.width)>parseInt(i.val())?(i.parent().addClass("sui-form-field-error"),o.show("slow"),u=!0):(i.parent().removeClass("sui-form-field-error"),o.hide(),r.hasClass("error")&&a.show("slow")),!t&&void 0!==r&&parseInt(wp_smushit_data.resize_sizes.height)>parseInt(r.val())?(r.parent().addClass("sui-form-field-error"),u||a.show("slow"),l=!0):(r.parent().removeClass("sui-form-field-error"),a.hide(),i.hasClass("error")&&o.show("slow")))}(e(this).parents().eq(4),!1,!1)})),e("body").on("click","#resize",(function(){var t=e(this),n=e("#smush-resize-settings-wrap");t.is(":checked")?n.show():n.hide()})),e("#wp-smush-revalidate-member").on("click",(function(t){t.preventDefault();var n={action:"smush_show_warning",_ajax_nonce:window.wp_smush_msgs.nonce},s=e(this).parents().eq(1);s.addClass("loading-notice"),e.get(ajaxurl,n,(function(e){s.removeClass("loading-notice").addClass("loaded-notice"),0==e?(s.attr("data-message",wp_smush_msgs.membership_valid),r(s,1e3)):(s.attr("data-message",wp_smush_msgs.membership_invalid),setTimeout((function(){s.removeClass("loaded-notice")}),1e3))}))})),e("li.smush-dir-savings").length>0){var c={action:"get_dir_smush_stats",_ajax_nonce:window.wp_smush_msgs.nonce};e.get(ajaxurl,c,(function(t){e("li.smush-dir-savings .sui-icon-loader").hide(),t.success||void 0===t.data.message?void 0===t.data||void 0===t.data.dir_smush?(e("li.smush-dir-savings span.wp-smush-stats").append(wp_smush_msgs.ajax_error),e("li.smush-dir-savings span.wp-smush-stats span").hide()):function(t){if(void 0!==t.dir_smush){var n=e("li.smush-dir-savings span.wp-smush-stats span.wp-smush-stats-human"),s=e("li.smush-dir-savings span.wp-smush-stats span.wp-smush-stats-percent");t.dir_smush.bytes>0?(e(".wp-smush-dir-link").addClass("sui-hidden"),e("li.smush-dir-savings .wp-smush-stats-label-message").hide(),n.length>0&&n.html(t.dir_smush.human),t.dir_smush.percent>0&&(e("li.smush-dir-savings span.wp-smush-stats span.wp-smush-stats-sep").removeClass("sui-hidden"),s.length>0&&s.html(t.dir_smush.percent+"%"))):e(".wp-smush-dir-link").removeClass("sui-hidden")}if(void 0!==t.combined_stats&&t.combined_stats.length>0){var i=t.combined_stats,o=i.smushed/i.total_count*100;(o=WP_Smush.helpers.precise_round(o,1))&&e("div.wp-smush-count-total span.wp-smush-images-percent").html(o),i.total_count&&e("span.wp-smush-count-total span.wp-smush-total-optimised").html(i.total_count),i.savings&&e("span.wp-smush-savings span.wp-smush-stats-human").html(i.savings),i.percent&&e("span.wp-smush-savings span.wp-smush-stats-percent").html(i.percent)}}(t.data):e("div.wp-smush-scan-result div.content").prepend(t.data.message)}))}if(e("#smush-updated-dialog").length){window.SUI.openModal("smush-updated-dialog","wpbody-content",undefined,!1,!1,!0)}e("input#original").on("change",(function(){e("#backup-notice").toggleClass("sui-hidden",e(this).is(":checked"))}));!function(){var e=document.querySelector(".wp-smush-compression-type");if(e){var t=e.querySelector(".wp-smush-compression-type_note p");t&&e.querySelector(".wp-smush-compression-type_slider").addEventListener("change",(function(e){var n,s;if("INPUT"===(null==e||null===(n=e.target)||void 0===n?void 0:n.nodeName)){var i=null===(s=e.target.dataset)||void 0===s?void 0:s.note;i&&(t.innerHTML=i.trim())}}))}}(),e(".wp-smush-modal-link-close").on("click",(function(t){t.preventDefault(),SUI.closeModal();var n=e(this).attr("href"),s="_blank"===e(this).attr("target");n&&(s?window.open(n,"_blank"):window.location.href=n)}));document.addEventListener("onSavedSmushSettings",(function(e){var t;null!=e&&null!==(t=e.detail)&&void 0!==t&&t.is_outdated_stats&&function(){var e=document.querySelector(".wp-smush-current-compression-level"),t=document.querySelector(".wp-smush-lossy-level-tabs button.active");if(e&&t){e.innerText=t.innerText.trim();var n=e.nextElementSibling;n&&(t.id.includes("ultra")?n.classList.add("sui-hidden"):n.classList.remove("sui-hidden"))}}()}))}))},3199:function(e,t,n){"use strict";n.r(t);var s=n(9620),i=(n(7899),n(7119)),o=n(5417),r=n(5326),a=["global_stats","errors"];!function(){if(window.wp_smush_msgs){var e=document.querySelector.bind(document),t=function(){return{handle:function(e){return s.A.background[e]()},initState:function(){return s.A.background.initState()}}},n=function(){var n=window.wp_smushit_data&&window.wp_smushit_data.bo_stats&&e(".wp-smush-bo-start");if(n){var s=new t,u=e(".bulk-smush-wrapper"),l=e(".wp-smush-scan"),c=0,d=!1;return{hookStatusChecks:function(){var e=this;if(!c){var t=0,n=!1,s=!1;c=setInterval((function(){if(!n){n=!0,t++;var i=e.syncBackgroundStatus();t%3==0&&i.then((function(){s||(e.syncStats().then((function(){s=!1})),s=!0)})),i.finally((function(){n=!1}))}}),3e3)}},resetBOStatsOnStart:function(){o.R.setBoStats({is_cancelled:!1,is_completed:!1,processed_items:0,failed_items:0})},start:function(){var e=this;this.resetBOStatsOnStart(),this.onStart(),r.A.performTest().then((function(t){(null==t?void 0:t.loopback)?e.startBulkSmush():(e.showLoopbackErrorModal(),e.onStartFailure())})).catch((function(t){console.error("Error:",t),e.showLoopbackErrorModal(),e.onStartFailure()}))},startBulkSmush:function(){var e=this;s.handle("start").then((function(t){if(t.success){var n=e.updateStats(t.data,!1);e.showProgressBar(),e.hookStatusChecks(),n&&o.R.renderStats()}else e.showFailureNotice(t),e.onStartFailure(t)}))},showFailureNotice:function(e){WP_Smush.helpers.showNotice(e,{showdismiss:!0,autoclose:!1})},onStartFailure:function(e){this.cancelBulk()},showLoopbackErrorModal:function(){var e=document.getElementById("smush-loopback-error-dialog");e&&window.SUI&&(e.dataset.processType="smush",WP_Smush.helpers.showModal(e.id))},initState:function(){var e=this;o.R.getBoStats().in_processing&&(this.onStart(),s.initState().then((function(t){t.success?(e.updateStats(t.data,!1),e.showProgressBar(),e.hookStatusChecks(),t.data.errors&&!Object.keys(o.R.getErrors()).length&&o.R.setErrors(t.data.errors),o.R.renderStats()):WP_Smush.helpers.showNotice(t)})))},cancel:function(){var e=this;d=!0,this.setCancelButtonStateToStarted(),s.handle("cancel").then((function(t){t.success?e.cancelBulk():WP_Smush.helpers.showNotice(t)}))},hideProgressBar:function(){i.A.close().update(0,o.R.getBoStats().total_items)},showProgressBar:function(){i.A.update(o.R.getBoStats().processed_items,o.R.getBoStats().total_items),i.A.show()},updateStats:function(e,t){e.global_stats=e.global_stats||{},e.errors=e.errors||{};var n=e.global_stats,s=e.errors,i=function(e,t){if(null==e)return{};var n,s,i=function(e,t){if(null==e)return{};var n={};for(var s in e)if({}.hasOwnProperty.call(e,s)){if(t.indexOf(s)>=0)continue;n[s]=e[s]}return n}(e,t);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);for(s=0;s<o.length;s++)n=o[s],t.indexOf(n)>=0||{}.propertyIsEnumerable.call(e,n)&&(i[n]=e[n])}return i}(e,a);return!!o.R.isChangedStats(i)&&(o.R.setBoStats(i),t&&o.R.setGlobalStats(n),o.R.setErrors(s),!0)},cancelBulk:function(){var e=this;this.syncStats((function(){100===o.R.getGlobalStats().percent_optimized?(o.R.setBoStats({is_completed:!0}),e.onCompletedBulk()):(o.R.setBoStats({is_cancelled:!0}),e.onFinish(),i.A.showBulkSmushDescription()),d=!1}))},showCompletedMessage:function(){var e=u.querySelector(".wp-smush-all-done");if(o.R.getBoStats().failed_items){var t=wp_smush_msgs.all_failed;this.isFailedAllItems()||(t=wp_smush_msgs.error_in_bulk.replace("{{smushed}}",o.R.getBoStats().total_items-o.R.getBoStats().failed_items).replace("{{total}}",o.R.getBoStats().total_items).replace("{{errors}}",o.R.getBoStats().failed_items)),e.querySelector("p").innerHTML=t,e.classList.remove("sui-notice-success","sui-notice-warning");var n=this.getNoticeType(),s="warning"===n?"info":"check-tick",i=e.querySelector(".sui-notice-icon");e.classList.add("sui-notice-"+n),i.classList.remove("sui-icon-check-tick","sui-icon-info"),i.classList.add("sui-icon-"+s)}else e.querySelector("p").innerHTML=wp_smush_msgs.all_smushed;e.classList.remove("sui-hidden")},isFailedAllItems:function(){return o.R.getBoStats().failed_items===o.R.getBoStats().total_items},getNoticeType:function(){return this.isFailedAllItems()?"warning":"success"},onCompletedBulk:function(){this.onFinish(),i.A.hideBulkSmushDescription(),this.showCompletedMessage(),i.A.update(0,o.R.getBoStats().total_items)},completeBulk:function(){var e=this;this.syncStats((function(){return e.onCompletedBulk()}))},syncStats:function(){var t=this,n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:function(){return!1};return s.handle("getStats").then((function(s){if(s.success){var i=s.data.errors||{};t.updateStats({global_stats:s.data,errors:i},!0),o.R.renderStats(),s.data.content&&(e("#wp-smush-bulk-content").innerHTML=s.data.content),n()}else WP_Smush.helpers.showNotice(s)})).catch((function(e){return console.log("error",e)}))},syncBackgroundStatus:function(){var e=this;return s.handle("getStatus").then((function(t){(t.data||{}).in_process_notice&&i.A.setNotice(t.data.in_process_notice),t.success?(e.updateStats(t.data,!1)&&(i.A.update(o.R.getBoStats().processed_items,o.R.getBoStats().total_items),o.R.getBoStats().is_cancelled||o.R.getBoStats().is_completed||o.R.renderStats()),o.R.getBoStats().is_cancelled&&!d?e.cancelBulk():o.R.getBoStats().is_completed?e.completeBulk():o.R.getBoStats().is_dead&&e.onDead()):WP_Smush.helpers.showNotice(t)}))},onStart:function(){n.setAttribute("disabled",""),l&&l.setAttribute("disabled",""),e(".wp-smush-restore").setAttribute("disabled",""),o.U.maybeShowCDNUpsellForPreSiteOnStart(),this.hideBulkSmushFailedNotice(),this.setCancelButtonStateToInitial()},hideBulkSmushFailedNotice:function(){var e=document.querySelector(".wp-smush-inline-retry-bulk-smush-notice");e&&e.parentElement.classList.add("sui-hidden")},onFinish:function(){c&&(clearInterval(c),c=0),n.removeAttribute("disabled"),this.hideProgressBar(),l&&l.removeAttribute("disabled",""),e(".wp-smush-restore").removeAttribute("disabled",""),o.U.maybeShowCDNUpsellForPreSiteOnCompleted()},onDead:function(){this.onFinish(),i.A.showBulkSmushDescription(),this.showRetryBulkSmushModal()},showRetryBulkSmushModal:function(){var e=this,t=document.getElementById("smush-retry-bulk-smush-notice");window.SUI&&t&&(t.querySelector(".smush-retry-bulk-smush-notice-button").onclick=function(t){t.preventDefault(),window.SUI.closeModal("smush-retry-bulk-smush-notice"),e.start()},window.SUI.openModal("smush-retry-bulk-smush-notice","wpbody-content",void 0,!1))},init:function(){var e=this;if(n){n.onclick=function(){n.classList.contains("wp-smush-scan-and-bulk-smush")||e.start()};var t=document.querySelector(".wp-smush-inline-retry-bulk-smush-notice .wp-smush-trigger-bulk-smush");t&&t.addEventListener("click",(function(e){e.preventDefault(),n.click()})),this.initState()}},setCancelButtonStateToInitial:function(){i.A.setCancelButtonLabel(wp_smush_msgs.cancel),i.A.setOnCancelCallback(this.cancel.bind(this))},setCancelButtonStateToStarted:function(){i.A.setCancelButtonLabel(wp_smush_msgs.cancelling),i.A.setOnCancelCallback((function(){return!1}))}}}}();n&&n.init(),document.addEventListener("wpSmushAfterRecheckImages",(function(){o.R.updateGlobalStatsFromSmushScriptData()})),document.addEventListener("backgroundBulkSmushOnScanCompleted",(function(){n&&(o.R.setBoStats({in_processing:!0}),n.initState())}))}}()},9968:function(e,t,n){"use strict";n.r(t);var s=n(7899);WP_Smush.restore={modal:document.getElementById("smush-restore-images-dialog"),contentContainer:document.getElementById("smush-bulk-restore-content"),settings:{slide:"start",success:0,errors:[]},items:[],success:[],errors:[],currentStep:0,totalSteps:0,init:function(){this.modal&&(this.settings={slide:"start",success:0,errors:[]},this.resetModalWidth(),this.renderTemplate(),window.SUI.openModal("smush-restore-images-dialog","wpbody-content",void 0,!1))},renderTemplate:function(){var e=WP_Smush.onboarding.template("smush-bulk-restore")(this.settings);e&&(this.contentContainer.innerHTML=e),this.bindSubmit()},resetModalWidth:function(){this.modal.style.maxWidth="460px",this.modal.querySelector(".sui-box").style.maxWidth="460px"},bindSubmit:function(){var e=this.modal.querySelector('button[id="smush-bulk-restore-button"]'),t=this;e&&e.addEventListener("click",(function(e){e.preventDefault(),t.resetModalWidth(),t.settings={slide:"progress"},t.errors=[],t.renderTemplate(),t.initScan(),s.A.track("Bulk Restore Triggered")}))},cancel:function(){"start"===this.settings.slide||"finish"===this.settings.slide?window.SUI.closeModal():(this.updateProgressBar(!0),window.location.reload())},updateProgressBar:function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0],t=0;0<this.currentStep&&(t=Math.min(Math.round(100*this.currentStep/this.totalSteps),99)),t>100&&(t=100),this.modal.querySelector(".sui-progress-text span").innerHTML=t+"%",this.modal.querySelector(".sui-progress-bar span").style.width=t+"%";var n=this.modal.querySelector(".sui-progress-state-text");n.innerHTML=t>=90?"Finalizing...":e?"Cancelling...":this.currentStep+"/"+this.totalSteps+" images restored"},initScan:function(){var e=this,t=document.getElementById("_wpnonce"),n=new XMLHttpRequest;n.open("POST",ajaxurl+"?action=get_image_count",!0),n.setRequestHeader("Content-type","application/x-www-form-urlencoded"),n.onload=function(){if(200===n.status){var t=JSON.parse(n.response);void 0!==t.data.items&&(e.items=t.data.items,e.totalSteps=t.data.items.length,e.step())}else window.console.log("Request failed.  Returned status of "+n.status)},n.send("_ajax_nonce="+t.value)},step:function(){var e=this,t=this,n=document.getElementById("_wpnonce");if(0<this.items.length){var s=this.items.pop(),i=new XMLHttpRequest;i.open("POST",ajaxurl+"?action=restore_step",!0),i.setRequestHeader("Content-type","application/x-www-form-urlencoded"),i.onload=function(){if(e.currentStep++,200===i.status){var n=(JSON.parse(i.response)||{}).data||{};n.success?t.success.push(s):t.errors.push({id:s,src:n.src||"Error",thumb:n.thumb,link:n.link})}t.updateProgressBar(),t.step()},i.send("item="+s+"&_ajax_nonce="+n.value)}else this.settings={slide:"finish",success:this.success.length,errors:this.errors,total:this.totalSteps},t.renderTemplate(),0<this.errors.length&&(this.modal.style.maxWidth="660px",this.modal.querySelector(".sui-box").style.maxWidth="660px")}},WP_Smush.restore.template=_.memoize((function(e){var t,n={evaluate:/<#([\s\S]+?)#>/g,interpolate:/{{{([\s\S]+?)}}}/g,escape:/{{([^}]+?)}}(?!})/g,variable:"data"};return function(s){return _.templateSettings=n,(t=t||_.template(document.getElementById(e).innerHTML))(s)}}))},8686:function(e,t,n){"use strict";n.r(t);var s,i=n(9982),o=n(9620),r=n(7119);s=jQuery,WP_Smush.bulk={init:function(){this.onClickBulkSmushNow(),this.onClickIgnoreImage(),this.onClickIgnoreAllImages(),this.onScanCompleted()},onClickBulkSmushNow:function(){s(".wp-smush-all").on("click",(function(e){var t=s(this);t.hasClass("wp-smush-scan-and-bulk-smush")||(e.preventDefault(),WP_Smush.bulk.ajaxBulkSmushStart(t))}))},ajaxBulkSmushStart:function(e){var t=this;if(e=e||s("#wp-smush-bulk-content .wp-smush-all"),void 0===window.wp_smushit_data||0===window.wp_smushit_data.unsmushed.length&&0===window.wp_smushit_data.resmush.length)return!1;s(".wp-resmush.wp-smush-action, .wp-smush-scan, .wp-smush-all:not(.sui-progress-close), a.wp-smush-lossy-enable, button.wp-smush-resize-enable, button#save-settings-button").prop("disabled",!0),e.hasClass("wp-smush-resume-bulk-smush")&&this.bulkSmush?this.resumeBulkSmush():(this.bulkSmush=new i.A(e,!0),r.A.setOnCancelCallback((function(){t.bulkSmush.cancelAjax()})).update(0,this.bulkSmush.ids.length).show(),this.maybeShowCDNUpsellForPreSiteOnStart(),this.bulkSmush.run())},resumeBulkSmush:function(){r.A.disableExceedLimitMode(),r.A.hideBulkSmushDescription(),this.bulkSmush.onStart(),this.bulkSmush.callAjax()},onClickIgnoreImage:function(){s("body").on("click",".smush-ignore-image",(function(e){e.preventDefault();var t=s(this);t.prop("disabled",!0),t.attr("data-tooltip"),t.removeClass("sui-tooltip"),s.post(ajaxurl,{action:"ignore_bulk_image",id:t.attr("data-id"),_ajax_nonce:wp_smush_msgs.nonce}).done((function(n){t.is("a")&&n.success&&void 0!==n.data.html&&(t.closest(".smush-status-links")?t.closest(".smush-status-links").parent().html(n.data.html):e.target.closest(".smush-bulk-error-row")&&(t.addClass("disabled"),e.target.closest(".smush-bulk-error-row").style.opacity=.5))}))}))},onClickIgnoreAllImages:function(){var e=document.querySelector(".wp_smush_ignore_all_failed_items");e&&(e.onclick=function(e){e.preventDefault(),e.target.setAttribute("disabled",""),e.target.style.cursor="progress";var t=e.target.dataset.type||null;e.target.classList.remove("sui-tooltip"),o.A.smush.ignoreAll(t).then((function(t){t.success?window.location.reload():(e.target.style.cursor="pointer",e.target.removeAttribute("disabled"),WP_Smush.helpers.showNotice(t))}))})},onScanCompleted:function(){var e=this;document.addEventListener("ajaxBulkSmushOnScanCompleted",(function(t){e.ajaxBulkSmushStart()}))},maybeShowCDNUpsellForPreSiteOnStart:function(){var e=document.querySelector(".wp-smush-upsell-cdn");e&&e.classList.remove("sui-hidden")}},WP_Smush.bulk.init()},5994:function(e,t,n){"use strict";n.r(t);var s,i=n(6006),o=function(e,t){e=parseInt(e),t=parseInt(t);var n=!1,s=0,i=0,o={scan:function(){var n=this,s=e-t;0!==t?r(s).fail(this.showScanError):jQuery.post(ajaxurl,{action:"directory_smush_start",_ajax_nonce:window.wp_smush_msgs.nonce},(function(){return r(s).fail(n.showScanError)})).fail(this.showScanError)},cancel:function(){return n=!0,jQuery.post(ajaxurl,{action:"directory_smush_cancel",_ajax_nonce:window.wp_smush_msgs.nonce})},getProgress:function(){if(n)return 0;var s=e-t;return Math.min(Math.round(100*parseInt(e-s)/e),99)},onFinishStep:function(n){jQuery(".wp-smush-progress-dialog .sui-progress-state-text").html(t-s+"/"+e+" "+window.wp_smush_msgs.progress_smushed),WP_Smush.directory.updateProgressBar(n)},onFinish:function(){WP_Smush.directory.updateProgressBar(100),window.location.href=window.wp_smush_msgs.directory_url+"&scan=done"},showScanError:function(e){var t=jQuery("#wp-smush-progress-dialog");t.removeClass("wp-smush-exceed-limit").addClass("wp-smush-scan-error"),t.find("#smush-scan-error").text("".concat(e.status," ").concat(e.statusText));var n=t.find(".smush-403-error-message");403!==e.status?n.addClass("sui-hidden"):n.removeClass("sui-hidden")},limitReached:function(){var e=jQuery("#wp-smush-progress-dialog");e.addClass("wp-smush-exceed-limit"),e.find("#cancel-directory-smush").attr("data-tooltip",window.wp_smush_msgs.bulk_resume),e.find(".sui-box-body .sui-icon-close").removeClass("sui-icon-close").addClass("sui-icon-play"),e.find("#cancel-directory-smush").attr("id","cancel-directory-smush-disabled")},resume:function(){var e=jQuery("#wp-smush-progress-dialog"),t=e.find("#cancel-directory-smush-disabled");e.removeClass("wp-smush-exceed-limit"),e.find(".sui-box-body .sui-icon-play").removeClass("sui-icon-play").addClass("sui-icon-close"),t.attr("data-tooltip","Cancel"),t.attr("id","cancel-directory-smush"),o.scan()}},r=function n(r){return r>=0?(t=e-r,jQuery.post(ajaxurl,{action:"directory_smush_check_step",_ajax_nonce:window.wp_smush_msgs.nonce,step:t},(function(e){void 0!==e.success&&e.success?(void 0!==e.data&&void 0!==e.data.skipped&&!0===e.data.skipped&&i++,t++,r-=1,o.onFinishStep(o.getProgress()),n(r).fail(o.showScanError)):void 0!==e.data.error&&"dir_smush_limit_exceeded"===e.data.error?o.limitReached():(s++,t++,r-=1,o.onFinishStep(o.getProgress()),n(r).fail(o.showScanError))}))):jQuery.post(ajaxurl,{action:"directory_smush_finish",_ajax_nonce:window.wp_smush_msgs.nonce,items:e-(s+i),failed:s,skipped:i},(function(e){return o.onFinish(e)}))};return o};s=jQuery,WP_Smush.directory={selected:[],tree:[],wp_smush_msgs:[],triggered:!1,init:function(){var e=this,t=s("#wp-smush-progress-dialog"),n=0,i=0;void 0!==window.wp_smushit_data.dir_smush&&(n=window.wp_smushit_data.dir_smush.totalSteps,i=window.wp_smushit_data.dir_smush.currentScanStep),this.scanner=new o(n,i),this.wp_smush_msgs=window.wp_smush_msgs||{},s("button.wp-smush-browse, a#smush-directory-open-modal").on("click",(function(t){t.preventDefault(),s(t.currentTarget).hasClass("wp-smush-browse")&&(s("div.wp-smush-scan-result div.wp-smush-notice").hide(),s("div.wp-smush-info").remove()),window.SUI.openModal("wp-smush-list-dialog",t.currentTarget,s("#wp-smush-list-dialog .sui-box-header [data-modal-close]")[0],!0),e.initFileTree()})),s("#wp-smush-select-dir").on("click",(function(t){t.preventDefault(),s("div.wp-smush-list-dialog div.sui-box-body").css({opacity:"0.8"}),s("div.wp-smush-list-dialog div.sui-box-body a").off("click");var n=s(this);n.addClass("sui-button-onload");var i=e.tree.getSelectedNodes(),r=[];i.forEach((function(e){r.push(e.key)}));var a={action:"image_list",smush_path:r,image_list_nonce:s('input[name="image_list_nonce"]').val()};s.post(ajaxurl,a,(function(t){t.success?(window.SUI.closeModal(),e.scanner=new o(t.data,0),e.showProgressDialog(t.data),e.scanner.scan()):(n.removeClass("sui-button-onload"),window.SUI.openNotice("wp-smush-ajax-notice",t.data.message,{type:"warning"}))}))})),t.on("click","#cancel-directory-smush, #dialog-close-div, .wp-smush-cancel-dir",(function(t){t.preventDefault(),s(".wp-smush-cancel-dir").addClass("sui-button-onload"),e.scanner.cancel().done((function(){return window.location.href=e.wp_smush_msgs.directory_url}))})),t.on("click",".sui-icon-play, .wp-smush-resume-scan",(function(t){t.preventDefault(),e.scanner.resume()}));var r=window.location.search;new URLSearchParams(r).has("start")&&!this.triggered&&(this.triggered=!0,s("button.wp-smush-browse").trigger("click"))},initFileTree:function(){var e=this,t=s("button#wp-smush-select-dir"),n={type:"GET",url:ajaxurl,data:{action:"smush_get_directory_list",list_nonce:s('input[name="list_nonce"]').val()},cache:!1};Object.entries(e.tree).length>0||(e.tree=(0,i.createTree)(".wp-smush-list-dialog .content",{autoCollapse:!0,clickFolderMode:3,checkbox:!0,debugLevel:0,selectMode:3,tabindex:"0",keyboard:!0,quicksearch:!0,source:n,lazyLoad:function(e,t){t.result=new Promise((function(e,i){n.data.dir=t.node.key,s.ajax(n).done((function(t){return e(t)})).fail(i)}))},loadChildren:function(e,t){return t.node.fixSelection3AfterClick()},select:function(){return t.prop("disabled",!+e.tree.getSelectedNodes().length)},init:function(){return t.prop("disabled",!0)}}))},showProgressDialog:function(e){s(".wp-smush-progress-dialog .sui-progress-state-text").html("0/"+e+" "+self.wp_smush_msgs.progress_smushed),window.SUI.openModal("wp-smush-progress-dialog","dialog-close-div",void 0,!1)},updateProgressBar:function(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];e>100&&(e=100),s(".sui-progress-block .sui-progress-text span").text(e+"%"),s(".sui-progress-block .sui-progress-bar span").width(e+"%"),e>=90&&s(".sui-progress-state .sui-progress-state-text").text("Finalizing..."),t&&s(".sui-progress-state .sui-progress-state-text").text("Cancelling...")}},WP_Smush.directory.init()},770:function(){function e(t){return e="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},e(t)}!function(){"use strict";WP_Smush.helpers={init:function(){},cacheUpsellErrorCodes:[],formatBytes:function(e,t){var n=1024,s=["KB","MB","GB","TB","PB"];if(Math.abs(e)<n)return e+" B";var i=-1;do{e/=n,++i}while(Math.abs(e)>=n&&i<s.length-1);return e.toFixed(t)+" "+s[i]},getSizeFromString:function(e){return e.replace(/[a-zA-Z]/g,"").trim()},getFormatFromString:function(e){return e.replace(/[0-9.]/g,"").trim()},precise_round:function(e,t){var n=e>=0?1:-1;return e=e>100?100:e,Math.round(e*Math.pow(10,t)+.001*n)/Math.pow(10,t)},showErrorNotice:function(e){if(void 0!==e){var t="<p>".concat(e,"</p>");SUI.openNotice("wp-smush-ajax-notice",t,{type:"error",icon:"info"});var n=document.querySelector(".sui-button-onload");n&&n.classList.remove("sui-button-onload")}},resetSettings:function(){var e=document.getElementById("wp_smush_reset"),t=new XMLHttpRequest;t.open("POST",ajaxurl+"?action=reset_settings",!0),t.setRequestHeader("Content-type","application/x-www-form-urlencoded"),t.onload=function(){if(200===t.status){var e=JSON.parse(t.response);void 0!==e.success&&e.success&&(window.location.href=wp_smush_msgs.smush_url)}else window.console.log("Request failed.  Returned status of "+t.status)},t.send("_ajax_nonce="+e.value)},prepareBulkSmushErrorRow:function(e,t,n,s,i,o){var r=n&&void 0!==n?'<img class="attachment-thumbnail" src="'.concat(n,'" />'):'<i class="sui-icon-photo-picture" aria-hidden="true"></i>',a=window.wp_smush_msgs.edit_link.replace("{{id}}",s);t="undefined"===t||void 0===t?"undefined":t;var u='<div class="smush-bulk-error-row" data-error-code="'.concat(o,'">\n\t\t\t\t\t<div class="smush-bulk-image-data">\n\t\t\t\t\t\t<div class="smush-bulk-image-title">\n\t\t\t\t\t\t\t').concat(r,'\n\t\t\t\t\t\t\t<span class="smush-image-name">\n\t\t\t\t\t\t\t\t<a href="').concat(a,'">').concat(t,'</a>\n\t\t\t\t\t\t\t</span>\n\t\t\t\t\t\t</div>\n\t\t\t\t\t<div class="smush-image-error">\n\t\t\t\t\t\t').concat(e,"\n\t\t\t\t\t</div>\n\t\t\t\t</div>");return"media"===i&&(u+='<div class="smush-bulk-image-actions">\n\t\t\t\t\t\t<a href="javascript:void(0)" class="sui-tooltip sui-tooltip-constrained sui-tooltip-left smush-ignore-image" data-tooltip="'.concat(window.wp_smush_msgs.error_ignore,'" data-id="').concat(s,'">\n\t\t\t\t\t\t\t').concat(window.wp_smush_msgs.btn_ignore,'\n\t\t\t\t\t\t</a>\n\t\t\t\t\t\t<a class="smush-link-detail" href="').concat(a,'">\n\t\t\t\t\t\t\t').concat(window.wp_smush_msgs.view_detail,"\n\t\t\t\t\t\t</a>\n\t\t\t\t\t</div>")),u+="</div>",u+=WP_Smush.helpers.upsellWithError(o)},cacheUpsellErrorCode:function(e){this.cacheUpsellErrorCodes.push(e)},upsellWithError:function(e){return e&&window.wp_smush_msgs["error_"+e]&&!this.isUpsellRendered(e)?(this.cacheRenderedUpsell(e),'<div class="smush-bulk-error-row smush-error-upsell"><div class="smush-bulk-image-title"><span class="smush-image-error">'+window.wp_smush_msgs["error_"+e]+"</span></div></div>"):""},isUpsellRendered:function(e){return this.cacheUpsellErrorCodes.includes(e)},cacheRenderedUpsell:function(e){this.cacheUpsellErrorCodes.push(e)},getErrorMessage:function(e){return e.message||e.data&&e.data.message||e.responseJSON&&e.responseJSON.data&&e.responseJSON.data.message||window.wp_smush_msgs.generic_ajax_error||e.status&&"Request failed. Returned status of "+e.status},showNotice:function(t,n){var s;if(s="object"===e(t)?this.getErrorMessage(t):t){n=n||{},n={type:(n=Object.assign({showdismiss:!1,autoclose:!0},n)).type||"error",icon:n.icon||("success"===n.type?"check-tick":"info"),dismiss:{show:n.showdismiss,label:window.wp_smush_msgs.noticeDismiss,tooltip:window.wp_smush_msgs.noticeDismissTooltip},autoclose:{show:n.autoclose}};var i="<p>".concat(s,"</p>");return SUI.openNotice("wp-smush-ajax-notice",i,n),Promise.resolve("#wp-smush-ajax-notice")}},closeNotice:function(){window.SUI.closeNotice("wp-smush-ajax-notice")},renderActivationCDNNotice:function(e){if(!document.getElementById("wp-smush-animated-upsell-notice")){var t='<div class="sui-notice sui-notice-info sui-margin-top" id="wp-smush-animated-upsell-notice">\n\t\t\t\t\t\t\t\t\t<div class="sui-notice-content">\n\t\t\t\t\t\t\t\t\t\t<div class="sui-notice-message">\n\t\t\t\t\t\t\t\t\t\t\t<i class="sui-notice-icon sui-icon-info" aria-hidden="true"></i>\n\t\t\t\t\t\t\t\t\t\t\t<p>'.concat(e,"</p>\n\t\t\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t</div>");document.querySelector("#smush-box-bulk .wp-smush-bulk-wrapper").outerHTML+=t}},redirectToPage:function(e){e="page=smush-".concat(e),window.location.href.includes(e)?window.location.reload():window.location.search=e},showModal:function(e){if(window.SUI){window.SUI.openModal(e,"wpbody-content",undefined,!1,!1,!0)}}},WP_Smush.helpers.init()}()},4726:function(e,t,n){"use strict";n.r(t);var s=n(7119),i=n(5630),o=n(5417);function r(e){return r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},r(e)}function a(e,t){for(var n=0;n<t.length;n++){var s=t[n];s.enumerable=s.enumerable||!1,s.configurable=!0,"value"in s&&(s.writable=!0),Object.defineProperty(e,u(s.key),s)}}function u(e){var t=function(e,t){if("object"!=r(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var s=n.call(e,t||"default");if("object"!=r(s))return s;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==r(t)?t:t+""}function l(e,t,n){return t=h(t),function(e,t){if(t&&("object"==r(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(e,c()?Reflect.construct(t,n||[],h(e).constructor):t.apply(e,n))}function c(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(e){}return(c=function(){return!!e})()}function d(){return d="undefined"!=typeof Reflect&&Reflect.get?Reflect.get.bind():function(e,t,n){var s=function(e,t){for(;!{}.hasOwnProperty.call(e,t)&&null!==(e=h(e)););return e}(e,t);if(s){var i=Object.getOwnPropertyDescriptor(s,t);return i.get?i.get.call(arguments.length<3?e:n):i.value}},d.apply(null,arguments)}function h(e){return h=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},h(e)}function p(e,t){return p=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},p(e,t)}!function(){if(window.wp_smush_msgs){var e=document.querySelector.bind(document);if(e(".wp-smush-scan-progress-bar-wrapper")){var t=e(".wp-smush-scan");if(t){var n,r=e(".wp-smush-bo-start")||e(".wp-smush-bulk-wrapper .wp-smush-all"),u=wp.i18n.__,c=new(function(n){function i(){var t;return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,i),(t=l(this,i)).runBulkSmushOnComplete=!1,t.restoreButton=e(".wp-smush-restore"),t.autoBulkSmushNotification=e(".wp-smush-auto-bulk-smush-notification"),t}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&p(e,t)}(i,n),c=i,(f=[{key:"startScanThenBulkSmushOnComplete",value:function(){this.runBulkSmushOnComplete=!0,this.startScan(!0)}},{key:"onStart",value:function(){return this.hideRecheckNotice(),this.hideFailedBulkSmushNotice(),this.disableRelatedButtons(),this.setRecheckImagesButtonOnLoad(),this.toggleBulkSmushBoxContent(),this}},{key:"onStartFailure",value:function(e){d(h(i.prototype),"onStartFailure",this).call(this,e),this.revertRelatedButtons()}},{key:"onCloseProgressBar",value:function(){this.maybeHideAutoBulkSmushNotification()}},{key:"disableRelatedButtons",value:function(){this.restoreButton.setAttribute("disabled",!0),r&&(r.setAttribute("disabled",!0),this.setInnerText(r,u("Waiting for Re-check to finish","wp-smushit")))}},{key:"revertRelatedButtons",value:function(){return r&&(r.removeAttribute("disabled"),this.revertInnerText(r)),this.restoreButton.removeAttribute("disabled"),this.revertRecheckImagesButton(),this}},{key:"setRecheckImagesButtonOnLoad",value:function(){this.disableRecheckImagesButton(),this.setInnerText(t.querySelector(".wp-smush-inner-text"),u("Checking Images","wp-smushit"))}},{key:"disableRecheckImagesButton",value:function(){t.setAttribute("disabled",!0)}},{key:"revertRecheckImagesButton",value:function(){t.removeAttribute("disabled"),this.revertInnerText(t.querySelector(".wp-smush-inner-text"))}},{key:"beforeUpdateStatus",value:function(e){this.runBulkSmushOnComplete=null==e?void 0:e.optimize_on_scan_completed,this.maybeShowAutoBulkSmushNotification()}},{key:"onDead",value:function(e){d(h(i.prototype),"onDead",this).call(this,e),this.revertRelatedButtons(),this.setRequiredScanForBulkSmushButton()}},{key:"onFinish",value:function(e){var t=e.global_stats;d(h(i.prototype),"onFinish",this).call(this,e),this.revertRelatedButtons(),this.toggleBulkSmushDescription(t),t.is_outdated?this.setRequiredScanForBulkSmushButton():this.removeScanEventFromBulkSmushButton(),this.revertRecheckWarning()}},{key:"onCompleted",value:function(e){r?(this.onFinish(e),e.global_stats.remaining_count<1||(this.runBulkSmushOnComplete?(this.runBulkSmushOnComplete=!1,this.triggerBulkSmushEvent(e)):this.showRecheckNoticeSuccess())):window.location.reload()}},{key:"showNotice",value:function(e){if(e.notice){var t="success";void 0!==e.noticeType&&(t=e.noticeType),window.SUI.openNotice("wp-smush-ajax-notice","<p>"+e.notice+"</p>",{type:t,icon:"check-tick"})}}},{key:"showRecheckNoticeSuccess",value:function(){var t=e(".wp-smush-recheck-images-notice-box");t&&(this.hideFailedBulkSmushNotice(),this.showAnElement(t),this.hideAnElement(t.querySelector(".wp-smush-recheck-images-notice-warning")),this.showAnElement(t.querySelector(".wp-smush-recheck-images-notice-success")))}},{key:"showRecheckNoticeWarning",value:function(){var t=e(".wp-smush-recheck-images-notice-box");t&&(this.hideFailedBulkSmushNotice(),this.showAnElement(t),this.hideAnElement(t.querySelector(".wp-smush-recheck-images-notice-success")),this.showAnElement(t.querySelector(".wp-smush-recheck-images-notice-warning")))}},{key:"hideRecheckNotice",value:function(){this.hideAnElement(e(".wp-smush-recheck-images-notice-box"))}},{key:"hideFailedBulkSmushNotice",value:function(){this.hideAnElement(e("#smush-box-inline-retry-bulk-smush-notice"))}},{key:"showProgressErrorNoticeOnRecheckNotice",value:function(){var t=e(".wp-smush-recheck-images-notice-box .wp-smush-recheck-images-notice-warning");t&&(t.classList.add("sui-notice-error"),t.classList.remove("sui-notice-warning"),this.showRecheckNoticeWarning())}},{key:"revertRecheckWarning",value:function(){var t=e(".wp-smush-recheck-images-notice-box .wp-smush-recheck-images-notice-warning");t&&(t.classList.add("sui-notice-warning"),t.classList.remove("sui-notice-error"),this.revertInnerText(t.querySelector("span")))}},{key:"triggerBulkSmushEvent",value:function(e){this.disableRecheckImagesButton(),e.enabled_background_process?this.triggerBackgroundBulkSmushEvent(e.global_stats):this.triggerAjaxBulkSmushEvent(e.global_stats)}},{key:"toggleBulkSmushDescription",value:function(e){s.A.isEmptyObject||(e.remaining_count<1?(s.A.hideBulkSmushDescription(),s.A.showBulkSmushAllDone()):(s.A.showBulkSmushDescription(),s.A.hideBulkSmushAllDone()))}},{key:"setRequiredScanForBulkSmushButton",value:function(){r&&r.classList.add("wp-smush-scan-and-bulk-smush")}},{key:"removeScanEventFromBulkSmushButton",value:function(){r&&r.classList.remove("wp-smush-scan-and-bulk-smush")}},{key:"triggerBackgroundBulkSmushEvent",value:function(e){document.dispatchEvent(new CustomEvent("backgroundBulkSmushOnScanCompleted",{detail:e}))}},{key:"triggerAjaxBulkSmushEvent",value:function(e){document.dispatchEvent(new CustomEvent("ajaxBulkSmushOnScanCompleted",{detail:e}))}},{key:"onCancelled",value:function(e){this.onFinish(e),this.runBulkSmushOnComplete=!1,this.setRequiredScanForBulkSmushButton()}},{key:"maybeShowAutoBulkSmushNotification",value:function(){this.runBulkSmushOnComplete&&this.showAnElement(this.autoBulkSmushNotification)}},{key:"maybeHideAutoBulkSmushNotification",value:function(){this.runBulkSmushOnComplete&&this.hideAnElement(this.autoBulkSmushNotification)}},{key:"toggleBulkSmushBoxContent",value:function(){o.R.resetAndHideBulkErrors(),this.toggleBulkSmushDescription(o.R.getGlobalStats())}}])&&a(c.prototype,f),m&&a(c,m),Object.defineProperty(c,"prototype",{writable:!1}),c;var c,f,m}(i.default));t&&t.classList.contains("wp-smush-background-scan")&&(t.addEventListener("click",(function(){return c.startScan()})),null!==(n=window.wp_smushit_data.media_library_scan)&&void 0!==n&&n.in_processing&&c.onStart().showProgressBar().autoSyncStatus()),function(){if(r){r.addEventListener("click",(function(e){r.classList.contains("wp-smush-scan-and-bulk-smush")&&(e.preventDefault(),c.startScanThenBulkSmushOnComplete())}))}}(),function(){var n=e(".wp-smush-recheck-images-notice-box");if(n&&t){var s,i,o=n.querySelector(".wp-smush-trigger-background-scan");if(o)if(o.onclick=function(e){e.preventDefault(),t.click()},!window.location.search.includes("smush-action=start-scan-media"))null!==(s=window.wp_smushit_data.media_library_scan)&&void 0!==s&&s.is_dead?c.showProgressErrorNoticeOnRecheckNotice():!window.wp_smushit_data.is_outdated||null!==(i=window.wp_smushit_data)&&void 0!==i&&null!==(i=i.bo_stats)&&void 0!==i&&i.is_dead||c.showRecheckNoticeWarning();var a=n.querySelector(".wp-smush-trigger-bulk-smush");a&&r&&(a.onclick=function(e){e.preventDefault(),n.classList.add("sui-hidden"),r.click()});var u=n.querySelectorAll("button.sui-button-icon");u&&u.forEach((function(e){e.onclick=function(t){e.closest(".sui-recheck-images-notice").classList.add("sui-hidden")}})),document.addEventListener("onSavedSmushSettings",(function(e){var t;if(null!=e&&null!==(t=e.detail)&&void 0!==t&&t.is_outdated_stats){c.setRequiredScanForBulkSmushButton();var s=document.querySelector("#smush-box-inline-retry-bulk-smush-notice");s&&!s.classList.contains("sui-hidden")||(n.classList.remove("sui-hidden"),n.querySelector(".wp-smush-recheck-images-notice-success").classList.add("sui-hidden"),n.querySelector(".wp-smush-recheck-images-notice-warning").classList.remove("sui-hidden"))}}))}}(),function(){if(t&&window.location.search.includes("smush-action=start-scan-media")){t.click();var e;e=window.location.href.replace("&smush-action=start-scan-media",""),window.history.pushState(null,null,e)}}(),function(){if(r&&window.location.search.includes("smush-action=start-bulk-")){r.click();var e;e=window.location.href.replace(/&smush-action=start-bulk-(smush|webp-conversion)/i,""),window.history.pushState(null,null,e)}}(),function(){var e=document.getElementById("smush-loopback-error-dialog");if(e){var n=e.querySelector(".smush-retry-process-button");n&&n.addEventListener("click",(function(){var n;"scan"===((null===(n=e.dataset)||void 0===n?void 0:n.processType)||"scan")?t.click():r.click()}))}}()}}}}()},2339:function(e,t,n){"use strict";n.r(t);var s=n(5630);function i(e){return i="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},i(e)}function o(e,t){for(var n=0;n<t.length;n++){var s=t[n];s.enumerable=s.enumerable||!1,s.configurable=!0,"value"in s&&(s.writable=!0),Object.defineProperty(e,r(s.key),s)}}function r(e){var t=function(e,t){if("object"!=i(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var s=n.call(e,t||"default");if("object"!=i(s))return s;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==i(t)?t:t+""}function a(e,t,n){return t=l(t),function(e,t){if(t&&("object"==i(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(e,u()?Reflect.construct(t,n||[],l(e).constructor):t.apply(e,n))}function u(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(e){}return(u=function(){return!!e})()}function l(e){return l=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},l(e)}function c(e,t){return c=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},c(e,t)}!function(e){if(window.wp_smush_msgs){var t=document.querySelector.bind(document);if(t(".wp-smush-scan-progress-bar-wrapper"))if(!t(".wp-smush-scan"))if(null===(e=window.wp_smushit_data.media_library_scan)||void 0===e?void 0:e.in_processing){var n=wp.i18n.__;(new(function(e){function s(){var e;return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,s),(e=a(this,s)).bulkSmushLink=t(".wp-smush-bulk-smush-link"),e}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&c(e,t)}(s,e),i=s,(r=[{key:"onShowProgressBar",value:function(){this.disableBulkSmushLink()}},{key:"onCloseProgressBar",value:function(){this.revertBulkSmushLink()}},{key:"disableBulkSmushLink",value:function(){this.bulkSmushLink&&(this.bulkSmushLink.setAttribute("disabled",!0),this.setInnerText(this.bulkSmushLink,n("Waiting for Re-check to finish","wp-smushit")))}},{key:"revertBulkSmushLink",value:function(){this.bulkSmushLink&&(this.bulkSmushLink.removeAttribute("disabled"),this.revertInnerText(this.bulkSmushLink))}}])&&o(i.prototype,r),u&&o(i,u),Object.defineProperty(i,"prototype",{writable:!1}),i;var i,r,u}(s.default))).showProgressBar().autoSyncStatus()}}}()},8647:function(e,t,n){"use strict";n.r(t);var s,i=n(9982),o=n(7119);(s=window.jQuery)((function(){s("body").on("click",".wp-smush-nextgen-send",(function(e){e.preventDefault(),new i.A(s(this),!1,"nextgen")})),s("body").on("click",".wp-smush-nextgen-bulk",(function(e){if(e.preventDefault(),s(".wp-smush-resmush-notice").remove(),"undefined"==typeof wp_smushit_data||0===wp_smushit_data.unsmushed.length&&0===wp_smushit_data.resmush.length)return!1;var t=new i.A(s(this),!0,"nextgen");o.A.setOnCancelCallback((function(){t.cancelAjax()})).update(0,t.ids.length).show(),jQuery(".wp-smush-all, .wp-smush-scan").prop("disabled",!0),s(".wp-smush-notice.wp-smush-remaining").hide(),t.run()})).on("click",".wp-smush-trigger-nextgen-bulk",(function(e){e.preventDefault();var t=s(".wp-smush-nextgen-bulk");t.length&&(t.trigger("click"),SUI.closeNotice("wp-smush-ajax-notice"))}))}))},7811:function(){!function(e){"use strict";var t=e("#wp-smush-s3support-alert");if(t.length){var n={type:"warning",icon:"info",dismiss:{show:!0,label:wp_smush_msgs.noticeDismiss,tooltip:wp_smush_msgs.noticeDismissTooltip}};window.SUI.openNotice("wp-smush-s3support-alert",t.data("message"),n)}function s(t){var n=e(t.currentTarget).closest(".smush-notice");n.fadeTo(100,0,(function(){return n.slideUp(100,(function(){return n.remove()}))}))}t.on("click","button",(function(){e.post(ajaxurl,{action:"dismiss_s3support_alert",_ajax_nonce:window.wp_smush_msgs.nonce})})),e("#wp-smush-api-message button.sui-button-icon").on("click",(function(t){t.preventDefault();var n=e("#wp-smush-api-message");n.slideUp("slow",(function(){n.remove()})),e.post(ajaxurl,{action:"hide_api_message",_ajax_nonce:window.wp_smush_msgs.nonce})})),e(".smush-notice .smush-notice-act").on("click",(function(e){s(e)})),e(".wp-smush-update-info").on("click",".notice-dismiss",(function(t){t.preventDefault(),s(t),e.post(ajaxurl,{action:"dismiss_update_info",_ajax_nonce:window.wp_smush_msgs.nonce})}))}(jQuery)},234:function(e,t,n){"use strict";n.r(t);var s=n(7899);WP_Smush.onboarding={membership:"free",onboardingModal:document.getElementById("smush-onboarding-dialog"),first_slide:"usage",settings:{first:!0,last:!1,slide:"usage",value:!1},selection:{usage:!1,auto:!0,lossy:!0,strip_exif:!0,original:!1,lazy_load:!0},contentContainer:document.getElementById("smush-onboarding-content"),onboardingSlides:["usage","auto","lossy","strip_exif","original","lazy_load"],touchX:null,touchY:null,recheckImagesLink:"",init:function(){if(this.onboardingModal){var e=document.getElementById("smush-onboarding");this.membership=e.dataset.type,this.recheckImagesLink=e.dataset.ctaUrl,"pro"!==this.membership&&(this.onboardingSlides=["usage","auto","lossy","strip_exif","lazy_load"]),"false"===e.dataset.tracking&&this.onboardingSlides.pop(),this.renderTemplate();var t=this.onboardingModal.querySelector(".smush-onboarding-skip-link");t&&t.addEventListener("click",this.skipSetup.bind(this)),window.SUI.openModal("smush-onboarding-dialog","wpcontent",void 0,!1)}},handleTouchStart:function(e){var t=e.touches[0];this.touchX=t.clientX,this.touchY=t.clientY},handleTouchMove:function(e){if(this.touchX&&this.touchY){var t=e.touches[0].clientX,n=e.touches[0].clientY,s=this.touchX-t,i=this.touchY-n;Math.abs(s)>Math.abs(i)&&(s>0?!1===WP_Smush.onboarding.settings.last&&WP_Smush.onboarding.next(null,"next"):!1===WP_Smush.onboarding.settings.first&&WP_Smush.onboarding.next(null,"prev")),this.touchX=null,this.touchY=null}},renderTemplate:function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"none",n=this.onboardingModal.querySelector('input[type="checkbox"]');n&&(this.selection[n.id]=n.checked);var s=WP_Smush.onboarding.template("smush-onboarding")(this.settings);s&&(this.contentContainer.innerHTML=s,"none"===t?this.contentContainer.classList.add("loaded"):(this.contentContainer.classList.remove("loaded"),this.contentContainer.classList.add(t),setTimeout((function(){e.contentContainer.classList.add("loaded"),e.contentContainer.classList.remove(t)}),600))),this.onboardingModal.addEventListener("touchstart",this.handleTouchStart,!1),this.onboardingModal.addEventListener("touchmove",this.handleTouchMove,!1),this.bindSubmit()},bindSubmit:function(){var e=this.onboardingModal.querySelector('button[type="submit"]'),t=this;e&&e.addEventListener("click",(function(e){e.preventDefault();var n=t.onboardingModal.querySelector('input[type="checkbox"]');n&&(t.selection[n.id]=n.checked);var s=document.getElementById("smush_quick_setup_nonce"),i=new XMLHttpRequest;i.open("POST",ajaxurl+"?action=smush_setup",!0),i.setRequestHeader("Content-type","application/x-www-form-urlencoded"),i.onload=function(){200===i.status?t.onFinishingSetup():window.console.log("Request failed.  Returned status of "+i.status)},i.send("smush_settings="+JSON.stringify(t.selection)+"&_ajax_nonce="+s.value)}))},onFinishingSetup:function(){this.onFinish(),this.startRecheckImages()},onFinish:function(){window.SUI.closeModal()},startRecheckImages:function(){this.recheckImagesLink&&(window.location.href=this.recheckImagesLink)},next:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,n=this.onboardingSlides.indexOf(this.settings.slide),s=0;s=t?"next"===t?n+1:n-1:null!==e&&e.classList.contains("next")?n+1:n-1;var i=null!==e&&e.classList.contains("next")?"fadeInRight":"fadeInLeft";this.settings={first:0===s,last:s+1===this.onboardingSlides.length,slide:this.onboardingSlides[s],value:this.selection[this.onboardingSlides[s]]},this.renderTemplate(i)},goTo:function(e){var t=this.onboardingSlides.indexOf(e);this.settings={first:0===t,last:t+1===this.onboardingSlides.length,slide:e,value:this.selection[e]},this.renderTemplate()},skipSetup:function(){var e=this,t=document.getElementById("smush_quick_setup_nonce"),n=new XMLHttpRequest;n.open("POST",ajaxurl+"?action=skip_smush_setup&_ajax_nonce="+t.value),n.onload=function(){200===n.status?e.onSkipSetup():window.console.log("Request failed.  Returned status of "+n.status)},n.send()},onSkipSetup:function(){this.onFinish()},hideUpgradeModal:function(e,t){e.preventDefault(),t.classList.add("wp-smush-link-in-progress");var n=null==t?void 0:t.href,i=new XMLHttpRequest;i.open("POST",ajaxurl+"?action=hide_new_features&_ajax_nonce="+window.wp_smush_msgs.nonce),i.onload=function(){window.SUI.closeModal(),t.classList.remove("wp-smush-link-in-progress");var e=n?"cta_clicked":"closed";s.A.track("update_modal_displayed",{Action:e}),200===i.status?n&&(window.location.href=n):window.console.log("Request failed.  Returned status of "+i.status)},i.send()}},WP_Smush.onboarding.template=_.memoize((function(e){var t,n={evaluate:/<#([\s\S]+?)#>/g,interpolate:/{{{([\s\S]+?)}}}/g,escape:/{{([^}]+?)}}(?!})/g,variable:"data"};return function(s){return _.templateSettings=n,t=t||_.template(document.getElementById(e).innerHTML),s.first_slide=WP_Smush.onboarding.first_slide,t(s)}})),window.addEventListener("load",(function(){return WP_Smush.onboarding.init()}))},8425:function(){!function(){"use strict";WP_Smush.CDN={cdnEnableButton:document.getElementById("smush-enable-cdn"),cdnDisableButton:document.getElementById("smush-cancel-cdn"),cdnStatsBox:document.querySelector(".smush-cdn-stats"),init:function(){var e=this;this.cdnEnableButton&&this.cdnEnableButton.addEventListener("click",(function(t){t.preventDefault(),t.currentTarget.classList.add("sui-button-onload"),e.toggle_cdn(!0)})),this.cdnDisableButton&&this.cdnDisableButton.addEventListener("click",(function(t){t.preventDefault(),t.currentTarget.classList.add("sui-button-onload"),e.toggle_cdn(!1)})),this.updateStatsBox()},toggle_cdn:function(e){var t=document.getElementsByName("wp_smush_options_nonce"),n=new XMLHttpRequest;n.open("POST",ajaxurl+"?action=smush_toggle_cdn",!0),n.setRequestHeader("Content-type","application/x-www-form-urlencoded"),n.onload=function(){if(200===n.status){var e=JSON.parse(n.response);void 0!==e.success&&e.success?WP_Smush.helpers.redirectToPage("cdn"):void 0!==e.data.message&&WP_Smush.helpers.showErrorNotice(e.data.message)}else WP_Smush.helpers.showErrorNotice("Request failed.  Returned status of "+n.status)},n.send("param="+e+"&_ajax_nonce="+t[0].value)},updateStatsBox:function(){var e=this;if(void 0!==this.cdnStatsBox&&this.cdnStatsBox&&window.location.search.includes("page=smush-cdn")){this.toggleElements();var t=new XMLHttpRequest;t.open("POST",ajaxurl+"?action=get_cdn_stats",!0),t.onload=function(){if(200===t.status){var n=JSON.parse(t.response);void 0!==n.success&&n.success?e.toggleElements():void 0!==n.data.message&&WP_Smush.helpers.showErrorNotice(n.data.message)}else WP_Smush.helpers.showErrorNotice("Request failed.  Returned status of "+t.status)},t.send()}},toggleElements:function(){for(var e=this.cdnStatsBox.querySelector(".sui-icon-loader"),t=this.cdnStatsBox.querySelectorAll(".wp-smush-stats > :not(.sui-icon-loader)"),n=0;n<t.length;n++)t[n].classList.toggle("sui-hidden");e.classList.toggle("sui-hidden")}},WP_Smush.CDN.init()}()},8847:function(){!function(){"use strict";WP_Smush.Lazyload={lazyloadEnableButton:document.getElementById("smush-enable-lazyload"),lazyloadDisableButton:document.getElementById("smush-cancel-lazyload"),init:function(){var e=this,t=this;this.lazyloadEnableButton&&this.lazyloadEnableButton.addEventListener("click",(function(t){t.preventDefault(),t.currentTarget.classList.add("sui-button-onload"),e.toggle_lazy_load(!0)})),this.lazyloadDisableButton&&this.lazyloadDisableButton.addEventListener("click",(function(t){t.preventDefault(),t.currentTarget.classList.add("sui-button-onload"),e.toggle_lazy_load(!1)}));var n=document.getElementById("smush-remove-spinner");n&&n.addEventListener("click",(function(t){t.preventDefault(),e.removeLoaderIcon()}));var s=document.getElementById("smush-remove-placeholder");s&&s.addEventListener("click",(function(t){t.preventDefault(),e.removeLoaderIcon("placeholder")}));var i=document.querySelectorAll(".smush-ll-remove");i&&0<i.length&&i.forEach((function(e){e.addEventListener("click",(function(e){e.preventDefault(),e.target.closest("li").style.display="none",t.remove(e.target.dataset.id,e.target.dataset.type)}))})),this.handlePredefinedPlaceholders()},handlePredefinedPlaceholders:function(){var e=this,t=document.getElementById("placeholder-icon-1");t&&t.addEventListener("click",(function(){return e.changeColor("#F3F3F3")}));var n=document.getElementById("placeholder-icon-2");n&&n.addEventListener("click",(function(){return e.changeColor("#333333")}))},changeColor:function(e){document.getElementById("smush-color-picker").value=e,document.querySelector(".sui-colorpicker-hex .sui-colorpicker-value > span > span").style.backgroundColor=e,document.querySelector(".sui-colorpicker-hex .sui-colorpicker-value > input").value=e},toggle_lazy_load:function(e){var t=document.getElementsByName("wp_smush_options_nonce"),n=new XMLHttpRequest;n.open("POST",ajaxurl+"?action=smush_toggle_lazy_load",!0),n.setRequestHeader("Content-type","application/x-www-form-urlencoded"),n.onload=function(){if(200===n.status){var e=JSON.parse(n.response);void 0!==e.success&&e.success?WP_Smush.helpers.redirectToPage("lazy-load"):void 0!==e.data.message&&(WP_Smush.helpers.showErrorNotice(e.data.message),document.querySelector(".sui-button-onload").classList.remove("sui-button-onload"))}else WP_Smush.helpers.showErrorNotice("Request failed.  Returned status of "+n.status),document.querySelector(".sui-button-onload").classList.remove("sui-button-onload")},n.send("param="+e+"&_ajax_nonce="+t[0].value)},addLoaderIcon:function(){var e,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"spinner";e||(e=wp.media({title:"Select or upload an icon",button:{text:"Select icon"},multiple:!1})).on("select",(function(){var n=e.state().get("selection").first().toJSON(),s=document.getElementById("smush-"+t+"-icon-preview");s.style.backgroundImage='url("'+n.url+'")',s.style.display="block",document.getElementById("smush-"+t+"-icon-file").setAttribute("value",n.id),document.getElementById("smush-upload-"+t).style.display="none";var i=document.getElementById("smush-remove-"+t);i.querySelector("span").innerHTML=n.filename,i.style.display="block"})),e.open()},removeLoaderIcon:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"spinner",t=document.getElementById("smush-"+e+"-icon-preview");t.style.backgroundImage="",t.style.display="none",document.getElementById("smush-upload-"+e).style.display="block",document.getElementById("smush-remove-"+e).style.display="none",document.getElementById("smush-"+e+"-icon-file").setAttribute("value","")},remove:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"spinner",n=document.getElementsByName("wp_smush_options_nonce"),s=new XMLHttpRequest;s.open("POST",ajaxurl+"?action=smush_remove_icon",!0),s.setRequestHeader("Content-type","application/x-www-form-urlencoded"),s.send("id="+e+"&type="+t+"&_ajax_nonce="+n[0].value)}},WP_Smush.Lazyload.init()}()},3368:function(e,t,n){"use strict";n.r(t);var s=n(7899);function i(e){return i="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},i(e)}function o(e,t){for(var n=0;n<t.length;n++){var s=t[n];s.enumerable=s.enumerable||!1,s.configurable=!0,"value"in s&&(s.writable=!0),Object.defineProperty(e,a(s.key),s)}}function r(e,t,n){return(t=a(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function a(e){var t=function(e,t){if("object"!=i(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var s=n.call(e,t||"default");if("object"!=i(s))return s;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==i(t)?t:t+""}(new(function(){return e=function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),r(this,"troubleshootClicked",!1),r(this,"resumeBulkSmushCount",0),r(this,"missedEventsKey","wp_smush_missed_events")},t=[{key:"init",value:function(){s.A.allowToTrack()&&(this.trackUltraLinks(),this.trackUpsellLinks(),this.registerTroubleshootClickEvent(),this.trackScanInterruptedEventOnStopScanningModal(),this.trackScanInterruptedEventOnRetryScanModal(),this.trackBulkSmushInterruptedEventOnStopBulkSmushModal(),this.trackBulkSmushInterruptedEventOnRetryBulkSmushModal(),this.registerBulkSmushResumeClickEvent(),this.trackBulkSmushInterruptedEventWhenExitingAjaxBulkSmush(),this.trackInterruptedEventFromInlineNotice(),this.trackInterruptedEventFromLoopbackErrorModal(),this.maybeTrackMissedEventsOnLoad())}},{key:"trackUltraLinks",value:function(){var e=this,t=document.querySelectorAll(".wp-smush-upsell-ultra-compression");if(t){var n=function(t){return{settings:"bulksmush_settings",dashboard:"dash_summary",bulk:"bulksmush_summary",directory:"directory_summary","lazy-load":"lazy_summary",cdn:"cdn_summary",webp:"webp_summary"}[t.classList.contains("wp-smush-ultra-compression-link")?"settings":e.getCurrentPageSlug()]||"bulksmush_settings"};t.forEach((function(e){e.addEventListener("click",(function(e){s.A.track("ultra_upsell_modal",{Location:n(e.target),"Modal Action":"direct_cta"})}))}))}}},{key:"trackUpsellLinks",value:function(){var e=document.querySelectorAll('[href*="utm_source=smush"]');e&&e.forEach((function(e){e.addEventListener("click",(function(e){var t=new URL(e.target.href).searchParams;if(t){var n=t.get("utm_campaign"),i={summary_cdn:"dash_summary","smush-dashboard-cdn-upsell":"dash_widget",smush_bulksmush_cdn:"bulk_smush_progress",smush_cdn_upgrade_button:"cdn_page",smush_bulksmush_library_gif_cdn:"media_library",smush_bulk_smush_complete_global:"bulk_smush_complete",summary_local_webp:"dash_summary","smush-dashboard-local-webp-upsell":"dash_widget"};if(n in i){var o=i[n],r=n.match(/(cdn|webp)/i),a="webp"===(r&&r[0])?"local_webp_upsell":"cdn_upsell";s.A.track(a,{Location:o})}}}))}))}},{key:"trackScanInterruptedEventOnStopScanningModal",value:function(){var e=this,t=document.getElementById("smush-stop-scanning-dialog");t&&t.querySelectorAll("[data-modal-close]").forEach((function(t){t.addEventListener("click",(function(t){var n,s=(null===(n=t.target.dataset)||void 0===n?void 0:n.action)||"Close";e.trackScanInterruptedEvent({Trigger:"cancel_in_progress","Modal Action":s})}))}))}},{key:"trackBulkSmushInterruptedEventOnStopBulkSmushModal",value:function(){var e=this,t=document.getElementById("smush-stop-bulk-smush-modal");t&&t.querySelectorAll("[data-modal-close]").forEach((function(t){t.addEventListener("click",(function(t){var n,s=(null===(n=t.target.dataset)||void 0===n?void 0:n.action)||"Close";e.trackBulkSmushInterruptedEvent({Trigger:"cancel_in_progress","Modal Action":s})}))}))}},{key:"trackScanInterruptedEventOnRetryScanModal",value:function(){var e=this,t=document.getElementById("smush-retry-scan-notice");if(t){var n=t.querySelector(".smush-retry-scan-notice-button");n&&n.addEventListener("click",(function(t){if(document.querySelector(".wp-smush-scan"))e.trackScanInterruptedEvent({Trigger:"failed_modal","Modal Action":"Retry"});else{t.preventDefault();var n="Scan Interrupted",i=e.getScanInterruptedEventProperties({Trigger:"failed_modal","Modal Action":"Retry"});s.A.track(n,i).catch((function(){e.cacheMissedEvent({event:n,properties:i})})).finally((function(){window.location.href=t.target.href}))}})),t.querySelectorAll("[data-modal-close]").forEach((function(t){t.addEventListener("click",(function(t){var n,s=(null===(n=t.target.dataset)||void 0===n?void 0:n.action)||"Close";e.trackScanInterruptedEvent({Trigger:"failed_modal","Modal Action":s})}))}))}}},{key:"trackBulkSmushInterruptedEventOnRetryBulkSmushModal",value:function(){var e=this,t=document.getElementById("smush-retry-bulk-smush-notice");if(t){var n=t.querySelector(".smush-retry-bulk-smush-notice-button");n&&n.addEventListener("click",(function(){e.trackBulkSmushInterruptedEvent({Trigger:"failed_modal","Modal Action":"Retry"})})),t.querySelectorAll("[data-modal-close]").forEach((function(t){t.addEventListener("click",(function(t){var n,s=(null===(n=t.target.dataset)||void 0===n?void 0:n.action)||"Close";e.trackBulkSmushInterruptedEvent({Trigger:"failed_modal","Modal Action":s})}))}))}}},{key:"trackScanInterruptedEvent",value:function(e){return s.A.track("Scan Interrupted",this.getScanInterruptedEventProperties(e))}},{key:"getScanInterruptedEventProperties",value:function(e){return Object.assign({Troubleshoot:this.troubleshootClicked?"Yes":"No"},e)}},{key:"trackBulkSmushInterruptedEventWhenExitingAjaxBulkSmush",value:function(){var e=this;if(!this.canUseBackgroundOptimization()){var t=document.querySelector(".wp-smush-bulk-progress-bar-wrapper");t&&window.addEventListener("beforeunload",(function(){var n,i=null===(n=window.WP_Smush)||void 0===n||null===(n=n.bulk)||void 0===n?void 0:n.bulkSmush;if(i&&i.ids.length>0&&!t.classList.contains("sui-hidden")){var o=t.classList.contains("wp-smush-exceed-limit"),r="Bulk Smush Interrupted",a=e.getBulkSmushInterruptedEventProperties({Trigger:o?"exit_50_limit":"exit_in_progress","Modal Action":"Exit","Retry Attempts":e.resumeBulkSmushCount});s.A.track(r,a).catch((function(){e.cacheMissedEvent({event:r,properties:a})}))}}))}}},{key:"cacheMissedEvent",value:function(e){window.localStorage&&window.localStorage.setItem(this.missedEventsKey,JSON.stringify([e]))}},{key:"getMissedEvents",value:function(){if(!window.localStorage)return[];var e=window.localStorage.getItem(this.missedEventsKey);return e?JSON.parse(e):[]}},{key:"clearMissedEvents",value:function(){window.localStorage&&window.localStorage.removeItem(this.missedEventsKey)}},{key:"canUseBackgroundOptimization",value:function(){var e;return void 0!==(null===(e=window.wp_smushit_data)||void 0===e?void 0:e.bo_stats)}},{key:"trackBulkSmushInterruptedEvent",value:function(e){return s.A.track("Bulk Smush Interrupted",this.getBulkSmushInterruptedEventProperties(e))}},{key:"getBulkSmushInterruptedEventProperties",value:function(e){return Object.assign({Troubleshoot:this.troubleshootClicked?"Yes":"No"},this.getBulkSmushProcessStats(),e)}},{key:"getBulkSmushProcessStats",value:function(){var e;if(this.canUseBackgroundOptimization())return{};var t=null===(e=window.WP_Smush)||void 0===e||null===(e=e.bulk)||void 0===e?void 0:e.bulkSmush,n=(null==t?void 0:t.total)||0,s=(null==t?void 0:t.smushed)+(null==t?void 0:t.errors.length);return{"Total Enqueued Images":n,"Completion Percentage":n>0?Math.ceil(100*s/n):0}}},{key:"trackInterruptedEventFromInlineNotice",value:function(){this.trackInterruptedEventFromInlineNoticeOnDashboard(),this.trackBulkSmushInterruptedEventFromInlineNoticeOnBulkSmush(),this.trackScanInterruptedEventFromInlineNoticeOnBulkSmush()}},{key:"trackInterruptedEventFromInlineNoticeOnDashboard",value:function(){var e=document.getElementById("smush-box-dashboard-bulk");e&&(this.trackBulkSmushInterruptedEventFromInlineNoticeOnDashboard(e),this.trackScanInterruptedEventFromInlineNoticeOnDashboard(e))}},{key:"trackBulkSmushInterruptedEventFromInlineNoticeOnDashboard",value:function(e){var t=this,n=e.querySelector(".wp-smush-retry-bulk-smush-link");n&&n.addEventListener("click",(function(e){e.preventDefault();var n="Bulk Smush Interrupted",i=t.getBulkSmushInterruptedEventProperties({Trigger:"failed_notice","Modal Action":"Retry"});s.A.track(n,i).catch((function(){t.cacheMissedEvent({event:n,properties:i})})).finally((function(){window.location.href=e.target.href}))}))}},{key:"trackBulkSmushInterruptedInlineNoticeEvent",value:function(){return this.trackBulkSmushInterruptedEvent({Trigger:"failed_notice","Modal Action":"Retry"})}},{key:"trackBulkSmushInterruptedEventFromInlineNoticeOnBulkSmush",value:function(){var e=this,t=document.querySelector(".wp-smush-inline-retry-bulk-smush-notice .wp-smush-trigger-bulk-smush");t&&t.addEventListener("click",(function(){e.trackBulkSmushInterruptedInlineNoticeEvent()}))}},{key:"trackScanInterruptedEventFromInlineNoticeOnDashboard",value:function(e){var t=this,n=e.querySelector(".wp-smush-retry-scan-link");n&&n.addEventListener("click",(function(e){e.preventDefault();var n="Scan Interrupted",i=t.getScanInterruptedEventProperties({Trigger:"failed_notice","Modal Action":"Retry"});s.A.track(n,i).catch((function(){t.cacheMissedEvent({event:n,properties:i})})).finally((function(){window.location.href=e.target.href}))}))}},{key:"trackScanInterruptedEventFromInlineNoticeOnBulkSmush",value:function(){var e=this,t=document.querySelector(".wp-smush-recheck-images-notice-box");if(t){var n=t.querySelector(".wp-smush-trigger-background-scan");n&&n.addEventListener("click",(function(){var t;(null==n||null===(t=n.previousElementSibling)||void 0===t?void 0:t.querySelector("a"))&&e.trackScanInterruptedEvent({Trigger:"failed_notice","Modal Action":"Retry"})}))}}},{key:"trackInterruptedEventFromLoopbackErrorModal",value:function(){var e=this,t=document.getElementById("smush-loopback-error-dialog");if(t){var n=t.querySelector('a[href*="#loopback-request-issue"]'),s=!1;n&&n.addEventListener("click",(function(){s=!0}),{once:!0}),t.querySelectorAll("[data-modal-close]").forEach((function(n){n.addEventListener("click",(function(n){var i,o,r=(null===(i=n.target.dataset)||void 0===i?void 0:i.action)||"Close",a=(null===(o=t.dataset)||void 0===o?void 0:o.processType)||"scan",u={Trigger:"loopback_error","Modal Action":r,Troubleshoot:s?"Yes":"No"};"scan"===a?e.trackScanInterruptedEvent(u):e.trackBulkSmushInterruptedEvent(u)}))}))}}},{key:"registerTroubleshootClickEvent",value:function(){var e=this,t=document.querySelectorAll('a[href*="#troubleshooting-guide"]');t&&t.forEach((function(t){t.addEventListener("click",(function(){e.troubleshootClicked=!0}),{once:!0})}))}},{key:"maybeTrackMissedEventsOnLoad",value:function(){var e=this;window.addEventListener("load",(function(){var t=e.getMissedEvents();0!==t.length&&(e.clearMissedEvents(),t.forEach((function(e){s.A.track(e.event,e.properties)})))}))}},{key:"registerBulkSmushResumeClickEvent",value:function(){var e=this,t=document.querySelector(".wp-smush-resume-bulk-smush");t&&t.addEventListener("click",(function(){e.resumeBulkSmushCount+=1}))}},{key:"getCurrentPageSlug",value:function(){var e=new URLSearchParams(document.location.search).get("page");return"smush"===e?"dashboard":e.replace("smush-","")}}],t&&o(e.prototype,t),n&&o(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e;var e,t,n}())).init()},8945:function(){!function(e){"use strict";function t(t,n){var s=new XMLHttpRequest;s.open("POST",ajaxurl+"?action=smush_save_settings",!0),s.setRequestHeader("Content-type","application/x-www-form-urlencoded"),s.onload=function(){if(e("#save-settings-button").removeClass("sui-button-onload-text sui-button-onload"),200===s.status){var t=JSON.parse(s.response);void 0!==t.success&&t.success?(!function(e){var t="<p>".concat(e,"</p>"),n={type:"success",icon:"check"};SUI.openNotice("wp-smush-ajax-notice",t,n);var s=document.querySelector(".sui-button-onload");s&&s.classList.remove("sui-button-onload")}(wp_smush_msgs.settingsUpdated),n=t.data,document.dispatchEvent(new CustomEvent("onSavedSmushSettings",{detail:n}))):t.data&&t.data.message?WP_Smush.helpers.showErrorNotice(t.data.message):WP_Smush.helpers.showErrorNotice("Request failed.")}else WP_Smush.helpers.showErrorNotice("Request failed. Returned status of "+s.status);var n},s.send("page="+n+"&"+t+"&_ajax_nonce="+wp_smush_msgs.nonce)}e("form#smush-bulk-form").on("submit",(function(n){n.preventDefault(),e("#save-settings-button").addClass("sui-button-onload"),t(e(this).serialize(),"bulk")})),e("form#smush-lazy-load-form").on("submit",(function(n){n.preventDefault(),e("#save-settings-button").addClass("sui-button-onload-text"),t(e(this).serialize(),"lazy-load")})),e("form#smush-cdn-form").on("submit",(function(n){n.preventDefault(),e("#save-settings-button").addClass("sui-button-onload-text"),t(e(this).serialize(),"cdn")})),e("form#smush-webp-form").on("submit",(function(n){n.preventDefault(),e("#save-settings-button").addClass("sui-button-onload-text"),t(e(this).serialize(),"webp")})),e("form#smush-integrations-form").on("submit",(function(n){n.preventDefault(),e("#save-settings-button").addClass("sui-button-onload-text"),t(e(this).serialize(),"integrations")})),e("form#smush-settings-form").on("submit",(function(n){n.preventDefault(),e("#save-settings-button").addClass("sui-button-onload-text"),t(e(this).serialize(),"settings")})),e("input[name=keep_data]").on("change",(function(e){var t="keep_data-true"===e.target.id?"keep_data-false":"keep_data-true";e.target.parentNode.classList.add("active"),document.getElementById(t).parentNode.classList.remove("active")})),e("input#detection").on("click",(function(){var t=e(".smush-highlighting-notice"),n=e(".smush-highlighting-warning");e(this).is(":checked")?t.length>0?t.show():n.show():(t.hide(),n.hide())}))}(jQuery)},9982:function(e,t,n){"use strict";var s=n(5417),i=n(7119),o=n(7899);function r(e){return r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},r(e)}function a(e,t){for(var n=0;n<t.length;n++){var s=t[n];s.enumerable=s.enumerable||!1,s.configurable=!0,"value"in s&&(s.writable=!0),Object.defineProperty(e,u(s.key),s)}}function u(e){var t=function(e,t){if("object"!=r(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var s=n.call(e,t||"default");if("object"!=r(s))return s;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==r(t)?t:t+""}var l=0,c=function(){function e(t,n){var s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"media";return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.onStart(),this.skip_resmush=!(void 0===t.data("smush")||!t.data("smush")),this.button=jQuery(t[0]),this.is_bulk=!!r(n)&&n,this.url=ajaxurl,this.log=jQuery(".smush-final-log"),this.setIds(),this.is_bulk_resmush=0<wp_smushit_data.resmush.length&&!this.skip_resmush,this.status=this.button.parent().prev(".smush-status"),this.smush_type=s,this.single_ajax_suffix="nextgen"===this.smush_type?"smush_manual_nextgen":"wp_smushit_manual",this.bulk_ajax_suffix="nextgen"===this.smush_type?"wp_smushit_nextgen_bulk":"wp_smushit_bulk",this.get_stats_action="nextgen"===this.smush_type?"nextgen_get_stats":"get_stats",this.url=this.is_bulk?e.smushAddParams(this.url,{action:this.bulk_ajax_suffix}):e.smushAddParams(this.url,{action:this.single_ajax_suffix}),this.start(),!this.is_bulk&&this.run(),this.bindDeferredEvents(),this}return t=e,n=[{key:"setIds",value:function(){var e=[];"object"===r(e=0<wp_smushit_data.resmush.length&&!this.skip_resmush?0<wp_smushit_data.unsmushed.length?wp_smushit_data.resmush.concat(wp_smushit_data.unsmushed):wp_smushit_data.resmush:wp_smushit_data.unsmushed)?this.ids=e.filter((function(e,t,n){return t===n.indexOf(e)})):this.ids=e}},{key:"start",value:function(){this.button.prop("disabled",!0),this.button.addClass("wp-smush-started"),this.bulkStart(),this.singleStart()}},{key:"bulkStart",value:function(){this.is_bulk&&(jQuery(".wp-smush-bulk-wrapper").addClass("sui-hidden"),jQuery(".wp-smush-bulk-progress-bar-wrapper .sui-notice-warning:first-of-type").hide(),0>=jQuery("div.smush-final-log .smush-bulk-error-row").length&&jQuery("div.smush-final-log").addClass("sui-hidden"),jQuery(".bulk-smush-wrapper .wp-smush-bulk-progress-bar-wrapper, #wp-smush-running-notice").removeClass("sui-hidden"))}},{key:"singleStart",value:function(){this.is_bulk||(this.button.html('<span class="spinner wp-smush-progress">'+window.wp_smush_msgs.smushing+"</span>"),this.status.removeClass("error"))}},{key:"enableButton",value:function(){this.button.prop("disabled",!1),jQuery(".wp-smush-all").prop("disabled",!1),jQuery(".wp-smush-restore").prop("disabled",!1),jQuery("a.wp-smush-lossy-enable, button.wp-smush-resize-enable, button#save-settings-button").prop("disabled",!1)}},{key:"singleDone",value:function(){if(!this.is_bulk){var t=this;this.request.done((function(n){if(void 0!==n.data){var s=t.status.parent();e.membershipValidity(n.data),n.success?(s.html(n.data),t.button.html(window.wp_smush_msgs.all_done)):n.data.html_stats?s.html(n.data.html_stats):(t.status.addClass("smush-warning"),t.status.html(n.data.error_msg),t.button.html(window.smush_vars.strings.stats_label)),e.updateImageStats(n.data.new_size)}})).fail((function(e){t.status.html(e.data),t.status.addClass("smush-warning"),t.enableButton()}))}}},{key:"syncStats",value:function(){var e=jQuery("div.wp-smush-bulk-progress-bar-wrapper div.wp-smush-count.tc"),t=e.html();e.html(window.wp_smush_msgs.sync_stats);var n=this;return jQuery.ajax({type:"GET",url:ajaxurl,data:{action:this.get_stats_action,_ajax_nonce:window.wp_smush_msgs.nonce},success:function(e){null!=e&&e.success?(s.R.updateGlobalStatsFromSmushScriptData(e.data),s.R.renderStats(),i.A.update(0,e.data.remaining_count),jQuery(".wp-smush-scan").prop("disabled",!1),n.hideBulkFreeLimitReachedNotice()):WP_Smush.helpers.showNotice(e,{showdismiss:!0,autoclose:!1})}}).always((function(){return e.html(t)}))}},{key:"bulkDone",value:function(){this.is_bulk&&(this.enableButton(),0===this.ids.length&&(jQuery(".bulk-smush-wrapper .wp-smush-all-done").removeClass("sui-hidden"),jQuery(".wp-smush-bulk-wrapper").addClass("sui-hidden"),jQuery(".wp-smush-bulk-progress-bar-wrapper").addClass("sui-hidden"),this._updateProgress(0,0)),jQuery(".wp-resmush.wp-smush-action").removeProp("disabled"))}},{key:"showAnimatedUpsellNotice",value:function(){if(this.errors.length){var e=document.querySelector(".smush-bulk-errors");if(e){var t=e.querySelector('[data-error-code="animated"]');if(t)return Array.prototype.slice.call(e.childNodes,0,5).includes(t)}}}},{key:"maybeShowCDNActivationNotice",value:function(){wp_smush_msgs.smush_cdn_activation_notice&&this.showAnimatedUpsellNotice()&&WP_Smush.helpers.renderActivationCDNNotice(wp_smush_msgs.smush_cdn_activation_notice)}},{key:"maybeShowUnlimitedUpsellNotice",value:function(){var e=document.querySelector(".wp-smush-global-upsell");e&&e.classList.remove("sui-hidden")}},{key:"maybeShowBulkErrorActions",value:function(){if(this.errors.length){var e=document.querySelector(".smush-bulk-errors-actions");e&&e.classList.remove("sui-hidden")}}},{key:"freeExceeded",value:function(){var e=jQuery(".wp-smush-bulk-progress-bar-wrapper");e.addClass("wp-smush-exceed-limit").removeClass("sui-hidden"),e.find(".sui-progress-block .wp-smush-cancel-bulk").removeClass("sui-hidden"),e.find(".sui-progress-block .wp-smush-all").addClass("sui-hidden"),e.find("i.sui-icon-loader").addClass("sui-icon-info").removeClass("sui-icon-loader").removeClass("sui-loading"),document.getElementById("bulk-smush-resume-button").classList.remove("sui-hidden"),this.showBulkFreeLimitReachedNotice()}},{key:"showBulkFreeLimitReachedNotice",value:function(){var e=document.getElementById("smush-limit-reached-notice");e&&e.classList.remove("sui-hidden")}},{key:"hideBulkFreeLimitReachedNotice",value:function(){var e=document.getElementById("smush-limit-reached-notice");e&&e.classList.add("sui-hidden")}},{key:"updateProgress",value:function(t){if(this.is_bulk_resmush||this.is_bulk){var n=0;t&&void 0!==t.data&&void 0!==t.data.stats&&e.updateLocalizedStats(t.data.stats,this.smush_type),this.is_bulk_resmush?(t.success&&(wp_smushit_data.resmush.length>0?jQuery(".wp-smush-images-remaining").html(wp_smushit_data.resmush.length):0===wp_smushit_data.resmush.length&&0===this.ids.length&&(jQuery(".bulk-resmush-wrapper .wp-smush-all-done").removeClass("sui-hidden"),jQuery(".wp-smush-resmush-wrap, .wp-smush-bulk-progress-bar-wrapper").addClass("sui-hidden"))),void 0!==this.ids&&void 0!==this.total&&this.total>0&&(n=(this.smushed+this.errors.length)/this.total*100)):n=(this.smushed+this.errors.length)/this.total*100,"nextgen"===this.smush_type&&wp_smushit_data.resmush.length>0&&this.smushed+this.errors.length<=1&&(wp_smushit_data.count_images-=wp_smushit_data.resmush.length+1),0===this.ids.length&&(jQuery(".bulk-smush-wrapper .wp-smush-all-done").removeClass("sui-hidden"),jQuery(".wp-smush-bulk-wrapper").addClass("sui-hidden")),this._updateProgress(this.smushed+this.errors.length,WP_Smush.helpers.precise_round(n,1)),0===this.ids.length&&"nextgen"!==this.smush_type||e.updateStats(this.smush_type)}}},{key:"_updateProgress",value:function(e,t){(this.is_bulk||this.is_bulk_resmush)&&(jQuery("span.wp-smush-images-percent").html(t+"%"),jQuery(".bulk-smush-wrapper .wp-smush-progress-inner").css("width",t+"%"),jQuery(".bulk-smush-wrapper .sui-progress-state-text").find("span:first-child").html(e).find("span:last-child").html(this.total))}},{key:"continue",value:function(){return this.continueSmush&&this.ids.length>0&&this.is_bulk}},{key:"onStart",value:function(){this.deferred=jQuery.Deferred(),this.deferred.errors=[],this.continueSmush=!0,this.errors=[];var e=jQuery(".bulk-smush-wrapper .sui-progress-state-text");this.smushed=parseInt(e.find("span:first-child").html()),this.total=parseInt(e.find("span:last-child").html()),jQuery(".wp-smush-restore").prop("disabled",!0)}},{key:"callAjax",value:function(){var t=arguments.length>0&&void 0!==arguments[0]&&arguments[0];if(0!==l&&void 0!==l&&10>performance.now()-l)return this.freeExceeded(),this.deferred;var n=window.wp_smush_msgs.nonce;this.current_id=this.is_bulk?this.ids.shift():this.button.data("id"),e.updateSmushIds(this.current_id);var s=this.button.parent().find("#_wp_smush_nonce");s.length>0&&(n=s.val());var i=this;return this.request=e.ajax(this.is_bulk_resmush,this.current_id,this.url,n,t).done((function(t){if(void 0===t.success||void 0!==t.success&&!1===t.success&&void 0!==t.data&&"limit_exceeded"!==t.data.error){i.errors.push(i.current_id);var n=t.data.error,s=WP_Smush.helpers.prepareBulkSmushErrorRow(n.error_message,n.file_name,n.thumbnail,i.current_id,i.smush_type,n.error_code);i.log.removeClass("sui-hidden"),i.log.find(".smush-bulk-errors").append(s),i.errors.length>4&&(i.log.find(".smush-bulk-errors").addClass("overflow-box"),jQuery(".smush-bulk-errors-actions").removeClass("sui-hidden"))}else void 0!==t.success&&t.success&&i.smushed++;e.membershipValidity(t.data),void 0!==t.data&&"limit_exceeded"===t.data.error&&"resolved"!==i.deferred.state()?(document.getElementById("wp-smush-running-notice").classList.add("sui-hidden"),i.continueSmush=!1,wp_smushit_data.unsmushed.unshift(i.current_id),i.ids.unshift(i.current_id),l=performance.now(),i.freeExceeded()):i.is_bulk&&(i.updateProgress(t),e.updateScoreProgress()),0===i.ids.length&&i.is_bulk&&i.onBulkSmushCompleted(),i.singleDone()})).always((function(){i.continue()&&i.is_bulk?i.callAjax(!1):i.deferred.resolve()})),this.deferred.errors=this.errors,this.deferred}},{key:"maybeShowCDNUpsellForPreSiteOnCompleted",value:function(){var e=document.querySelector(".wp-smush-upsell-cdn");e&&(e.querySelector("p").innerHTML=wp_smush_msgs.processed_cdn_for_free,e.classList.remove("sui-hidden"))}},{key:"onBulkSmushCompleted",value:function(){var e=this;this.maybeShowUnlimitedUpsellNotice(),this.maybeShowCDNActivationNotice(),this.maybeShowCDNUpsellForPreSiteOnCompleted(),this.maybeShowBulkErrorActions();var t=this.is_bulk?function(){return e.trackBulkSmushCompleted()}:function(){return!1};this.syncStats().done(t)}},{key:"getPercentOptimized",value:function(e,t){return e===t||e<=0?100:Math.floor(100*(e-t)/e)}},{key:"trackBulkSmushCompleted",value:function(){var e=s.R.getGlobalStats(),t=e.savings_bytes,n=e.count_images,i=e.percent_optimized,r=e.savings_percent,a=e.count_resize,u=e.savings_resize;o.A.track("Bulk Smush Completed",{"Total Savings":this.convertToMegabytes(t),"Total Images":n,"Media Optimization Percentage":parseFloat(i),"Percentage of Savings":parseFloat(r),"Images Resized":a,"Resize Savings":this.convertToMegabytes(u)})}},{key:"convertToMegabytes",value:function(e){var t=e/Math.pow(1024,2);return t&&parseFloat(t.toFixed(2))||0}},{key:"run",value:function(){this.is_bulk&&this.ids.length>0&&this.callAjax(!0),this.is_bulk||this.callAjax()}},{key:"bindDeferredEvents",value:function(){var e=this;this.deferred.done((function(){if(e.continueSmush=!0,e.errors.length){var t=e.errors.length===e.total?window.wp_smush_msgs.all_failed:window.wp_smush_msgs.error_in_bulk.replace("{{errors}}",e.errors.length).replace("{{total}}",e.total).replace("{{smushed}}",e.smushed);jQuery(".wp-smush-all-done").addClass("sui-notice-warning").removeClass("sui-notice-success").find("p").html(t)}e.bulkDone(),jQuery(".wp-smush-all:not(.wp-smush-finished)").prop("disabled",!1)}))}},{key:"cancelAjax",value:function(){this.continueSmush=!1,this.syncStats(),this.request.abort(),this.enableButton(),this.button.removeClass("wp-smush-started"),wp_smushit_data.unsmushed.unshift(this.current_id),jQuery(".wp-smush-bulk-wrapper").removeClass("sui-hidden"),jQuery(".wp-smush-bulk-progress-bar-wrapper").addClass("sui-hidden"),this.hideBulkFreeLimitReachedNotice()}}],u=[{key:"smushAddParams",value:function(e,t){return jQuery.isEmptyObject(t)||(e+=(e.indexOf("?")>=0?"&":"?")+jQuery.param(t)),e}},{key:"membershipValidity",value:function(e){var t=jQuery("#wp-smush-invalid-member");void 0!==e&&void 0!==e.show_warning&&t.length>0&&(e.show_warning?t.show():t.hide())}},{key:"ajax",value:function(e,t,n,s){var i=arguments.length>4&&void 0!==arguments[4]&&arguments[4],o=jQuery.param({is_bulk_resmush:e,attachment_id:t,_nonce:s,new_bulk_smush_started:i});return jQuery.ajax({type:"GET",data:o,url:n,timeout:wp_smushit_data.timeout,dataType:"json"})}},{key:"getTotalImagesToSmush",value:function(){var e=wp_smushit_data.resmush.length,t=wp_smushit_data.count_total-wp_smushit_data.count_smushed;return t>0?e+t:e}},{key:"updateScoreProgress",value:function(){var t="sui-grade-dismissed",n=0,s=0,i=e.getTotalImagesToSmush(),o=parseInt(wp_smushit_data.count_total);o===i?(o>0&&(t="sui-grade-f"),s=100):0<o&&(s=n=Math.floor(100*(o-i)/o),t="sui-grade-f",n>=60&&n<90?t="sui-grade-c":n>=90&&(t="sui-grade-a"));var r=jQuery("#smush-image-score");r.removeClass((function(e,t){return(t.match(/(^|\s)sui-grade-\S+/g)||[]).join(" ")})).addClass(t).attr("data-score",n).find(".sui-circle-score-label").html(n),r.find("circle:last-child").attr("style","--metric-array:"***************s+" "+(263.893782902-s))}},{key:"updateStats",value:function(e){var t=void 0!==e&&"nextgen"===e;wp_smushit_data.savings_bytes=parseInt(wp_smushit_data.size_before)-parseInt(wp_smushit_data.size_after);var n=WP_Smush.helpers.formatBytes(wp_smushit_data.savings_bytes,0),s=jQuery(".wp-smush-savings .wp-smush-stats-human");t?s.html(n):(s.html(WP_Smush.helpers.getFormatFromString(n)),jQuery(".sui-summary-large.wp-smush-stats-human").html(WP_Smush.helpers.getSizeFromString(n))),wp_smushit_data.savings_percent=WP_Smush.helpers.precise_round(parseInt(wp_smushit_data.savings_bytes)/parseInt(wp_smushit_data.size_before)*100,1),isNaN(wp_smushit_data.savings_percent)||jQuery(".wp-smush-savings .wp-smush-stats-percent").html(wp_smushit_data.savings_percent),t?jQuery(".sui-summary-details span.wp-smush-total-optimised").html(wp_smushit_data.count_images):jQuery("span.smushed-items-count span.wp-smush-count-total span.wp-smush-total-optimised").html(wp_smushit_data.count_images),wp_smushit_data.count_resize>0&&(jQuery("span.smushed-items-count span.wp-smush-count-resize-total").removeClass("sui-hidden"),jQuery("span.smushed-items-count span.wp-smush-count-resize-total span.wp-smush-total-optimised").html(wp_smushit_data.count_resize));var i=jQuery("li.super-smush-attachments .smushed-count");i.length&&void 0!==wp_smushit_data.count_supersmushed&&i.html(wp_smushit_data.count_supersmushed);var o=jQuery(".smush-conversion-savings");if(o.length>0&&void 0!==wp_smushit_data.savings_conversion&&""!==wp_smushit_data.savings_conversion){var r=o.find(".wp-smush-stats");r.length>0&&r.html(WP_Smush.helpers.formatBytes(wp_smushit_data.savings_conversion,1))}}},{key:"updateImageStats",value:function(e){if(0!==e){var t=jQuery(".attachment-info .file-size");if(t.contents().filter((function(){return 3===this.nodeType})).text()!==" "+e){var n=t.contents().filter((function(){return 1===this.nodeType})).text();t.html("<strong>"+n+"</strong> "+e)}}}},{key:"updateLocalizedStats",value:function(e,t){void 0!==window.wp_smushit_data&&("media"===t?(wp_smushit_data.count_smushed=parseInt(wp_smushit_data.count_smushed)+1,wp_smushit_data.count_images=parseInt(wp_smushit_data.count_images)+parseInt(e.count),e.is_lossy&&(wp_smushit_data.count_supersmushed=parseInt(wp_smushit_data.count_supersmushed)+1),wp_smushit_data.savings_resize=void 0!==e.savings_resize.bytes?parseInt(wp_smushit_data.savings_resize)+parseInt(e.savings_resize.bytes):parseInt(wp_smushit_data.savings_resize),wp_smushit_data.count_resize=void 0!==e.savings_resize.bytes?parseInt(wp_smushit_data.count_resize)+1:wp_smushit_data.count_resize,wp_smushit_data.savings_conversion=void 0!==e.savings_conversion&&void 0!==e.savings_conversion.bytes?parseInt(wp_smushit_data.savings_conversion)+parseInt(e.savings_conversion.bytes):parseInt(wp_smushit_data.savings_conversion)):"directory_smush"===t?wp_smushit_data.count_images=parseInt(wp_smushit_data.count_images)+1:"nextgen"===t&&(wp_smushit_data.count_smushed=parseInt(wp_smushit_data.count_smushed)+1,wp_smushit_data.count_supersmushed=parseInt(wp_smushit_data.count_supersmushed)+1,wp_smushit_data.count_images=parseInt(wp_smushit_data.count_images)+parseInt(e.count)),e.size_before>e.size_after&&(wp_smushit_data.size_before=void 0!==e.size_before?parseInt(wp_smushit_data.size_before)+parseInt(e.size_before):parseInt(wp_smushit_data.size_before),wp_smushit_data.size_after=void 0!==e.size_after?parseInt(wp_smushit_data.size_after)+parseInt(e.size_after):parseInt(wp_smushit_data.size_after)),void 0!==e.savings_resize&&(wp_smushit_data.size_before=void 0!==e.savings_resize.size_before?parseInt(wp_smushit_data.size_before)+parseInt(e.savings_resize.size_before):parseInt(wp_smushit_data.size_before),wp_smushit_data.size_after=void 0!==e.savings_resize.size_after?parseInt(wp_smushit_data.size_after)+parseInt(e.savings_resize.size_after):parseInt(wp_smushit_data.size_after)),void 0!==e.savings_conversion&&(wp_smushit_data.size_before=void 0!==e.savings_conversion.size_before?parseInt(wp_smushit_data.size_before)+parseInt(e.savings_conversion.size_before):parseInt(wp_smushit_data.size_before),wp_smushit_data.size_after=void 0!==e.savings_conversion.size_after?parseInt(wp_smushit_data.size_after)+parseInt(e.savings_conversion.size_after):parseInt(wp_smushit_data.size_after)))}},{key:"prepareErrorRow",value:function(e,t,n,s,i){var o='<div class="smush-bulk-error-row"><div class="smush-bulk-image-data">'+(void 0===n?'<i class="sui-icon-photo-picture" aria-hidden="true"></i>':n)+'<span class="smush-image-name">'+("undefined"===t||void 0===t?"undefined":t)+'</span><span class="smush-image-error">'+e+"</span></div>";return"media"===i&&(o=o+'<div class="smush-bulk-image-actions"><button type="button" class="sui-button-icon sui-tooltip sui-tooltip-constrained sui-tooltip-left smush-ignore-image" data-tooltip="'+window.wp_smush_msgs.error_ignore+'" data-id="'+s+'"><i class="sui-icon-eye-hide" aria-hidden="true"></i></button></div>'),o+="</div>"}},{key:"updateSmushIds",value:function(e){if(void 0!==wp_smushit_data.unsmushed&&wp_smushit_data.unsmushed.length>0){var t=wp_smushit_data.unsmushed.indexOf(e);t>-1&&wp_smushit_data.unsmushed.splice(t,1)}if(void 0!==wp_smushit_data.resmush&&wp_smushit_data.resmush.length>0){var n=wp_smushit_data.resmush.indexOf(e);n>-1&&wp_smushit_data.resmush.splice(n,1)}}}],n&&a(t.prototype,n),u&&a(t,u),Object.defineProperty(t,"prototype",{writable:!1}),t;var t,n,u}();t.A=c},5914:function(e,t,n){"use strict";n.r(t);var s=n(9620);WP_Smush.WebP={nonceField:document.getElementsByName("wp_smush_options_nonce"),toggleModuleButton:document.getElementById("smush-toggle-webp-button"),recheckStatusButton:document.getElementById("smush-webp-recheck"),recheckStatusLink:document.getElementById("smush-webp-recheck-link"),showWizardButton:document.getElementById("smush-webp-toggle-wizard"),switchWebpMethod:document.getElementById("smush-switch-webp-method"),init:function(){var e=this;this.maybeShowDeleteAllSuccessNotice(),this.toggleModuleButton&&this.toggleModuleButton.addEventListener("click",(function(t){return e.toggleWebp(t)})),this.recheckStatusButton&&this.recheckStatusButton.addEventListener("click",(function(t){t.preventDefault(),e.recheckStatus()})),this.recheckStatusLink&&this.recheckStatusLink.addEventListener("click",(function(t){t.preventDefault(),e.recheckStatus()})),document.getElementById("wp-smush-webp-delete-all")&&document.getElementById("wp-smush-webp-delete-all").addEventListener("click",(function(t){return e.deleteAll(t)})),this.showWizardButton&&this.showWizardButton.addEventListener("click",this.toggleWizard),this.switchWebpMethod&&this.switchWebpMethod.addEventListener("click",(function(t){t.preventDefault(),t.target.classList.add("wp-smush-link-in-progress"),e.switchMethod(e.switchWebpMethod.dataset.method)}))},switchMethod:function(e){s.A.webp.switchMethod(e).then((function(e){null!=e&&e.success?window.location.reload():WP_Smush.helpers.showNotice(e)}))},toggleWebp:function(e){var t=this;e.preventDefault();var n=e.currentTarget,s="enable"===n.dataset.action;n.classList.add("sui-button-onload");var i=new XMLHttpRequest;i.open("POST",ajaxurl+"?action=smush_webp_toggle",!0),i.setRequestHeader("Content-type","application/x-www-form-urlencoded"),i.onload=function(){var e=JSON.parse(i.response);if(200===i.status)void 0!==e.success&&e.success?t.runScan().onload=function(){window.location.href=window.wp_smush_msgs.localWebpURL}:void 0!==e.data.message&&(t.showNotice(e.data.message),n.classList.remove("sui-button-onload"));else{var s=window.wp_smush_msgs.generic_ajax_error;e&&void 0!==e.data.message&&(s=e.data.message),t.showNotice(s),n.classList.remove("sui-button-onload")}},i.send("param="+s+"&_ajax_nonce="+this.nonceField[0].value)},recheckStatus:function(){var e=this;this.recheckStatusButton.classList.add("sui-button-onload");var t=new XMLHttpRequest;t.open("POST",ajaxurl+"?action=smush_webp_get_status",!0),t.setRequestHeader("Content-type","application/x-www-form-urlencoded"),t.onload=function(){e.recheckStatusButton.classList.remove("sui-button-onload");var n=!1,s=JSON.parse(t.response);200===t.status?(s.success?"1":"0")!==e.recheckStatusButton.dataset.isConfigured&&location.reload():n=window.wp_smush_msgs.generic_ajax_error,s&&s.data&&(n=s.data),n&&e.showNotice(n)},t.send("_ajax_nonce="+window.wp_smush_msgs.webp_nonce)},deleteAll:function(e){var t=this,n=e.currentTarget;n.classList.add("sui-button-onload");var s=!1,i=new XMLHttpRequest;i.open("POST",ajaxurl+"?action=smush_webp_delete_all",!0),i.setRequestHeader("Content-type","application/x-www-form-urlencoded"),i.onload=function(){var e=JSON.parse(i.response);if(200===i.status&&void 0!==e.success&&e.success?t.runScan().onload=function(){location.search=location.search+"&notice=webp-deleted"}:s=window.wp_smush_msgs.generic_ajax_error,e&&e.data&&e.data.message&&(s=e.data.message),s){n.classList.remove("sui-button-onload");var o='<p style="text-align: left;">'.concat(s,"</p>");window.SUI.openNotice("wp-smush-webp-delete-all-error-notice",o,{type:"error",icon:"info",autoclose:{show:!1}})}},i.send("_ajax_nonce="+this.nonceField[0].value)},toggleWizard:function(e){e.currentTarget.classList.add("sui-button-onload");var t=new XMLHttpRequest;t.open("GET",ajaxurl+"?action=smush_toggle_webp_wizard&_ajax_nonce="+window.wp_smush_msgs.webp_nonce,!0),t.onload=function(){return location.href=window.wp_smush_msgs.localWebpURL},t.send()},runScan:function(){var e=new XMLHttpRequest,t=document.getElementsByName("wp_smush_options_nonce");return e.open("POST",ajaxurl+"?action=scan_for_resmush",!0),e.setRequestHeader("Content-type","application/x-www-form-urlencoded"),e.send("_ajax_nonce="+t[0].value),e},showNotice:function(e,t){if(void 0!==e){var n="<p>".concat(e,"</p>"),s={type:t||"error",icon:"info",dismiss:{show:!0,label:window.wp_smush_msgs.noticeDismiss,tooltip:window.wp_smush_msgs.noticeDismissTooltip},autoclose:{show:!1}};window.SUI.openNotice("wp-smush-ajax-notice",n,s)}},maybeShowDeleteAllSuccessNotice:function(){if(document.getElementById("wp-smush-webp-delete-all-notice")){var e="<p>".concat(document.getElementById("wp-smush-webp-delete-all-notice").dataset.message,"</p>");window.SUI.openNotice("wp-smush-webp-delete-all-notice",e,{type:"success",icon:"check-tick",dismiss:{show:!0}})}}},WP_Smush.WebP.init()},9620:function(e,t,n){"use strict";var s=n(6139),i=n.n(s);function o(e){return o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},o(e)}var r=new function(){function e(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n={url:ajaxurl,method:arguments.length>2&&void 0!==arguments[2]?arguments[2]:"POST",cache:!1};return t instanceof FormData?(t.append("action",e),t.append("_ajax_nonce",window.wp_smush_msgs.nonce),n.contentType=!1,n.processData=!1):(t._ajax_nonce=t._ajax_nonce||window.smush_global.nonce||window.wp_smush_msgs.nonce,t.action=e),n.data=t,new Promise((function(e,t){jQuery.ajax(n).done(e).fail(t)})).then((function(e){return"object"!==o(e)&&(e=JSON.parse(e)),e})).catch((function(e){return console.error("Error:",e),e}))}var t={background:{start:function(){return e("bulk_smush_start")},cancel:function(){return e("bulk_smush_cancel")},initState:function(){return e("bulk_smush_get_status")},getStatus:function(){return e("bulk_smush_get_status")},getStats:function(){return e("bulk_smush_get_global_stats")},backgroundHealthyCheck:function(){return e("smush_start_background_pre_flight_check")},backgroundHealthyStatus:function(){return e("smush_get_background_pre_flight_status")}},smush:{syncStats:function(t){return e("get_stats",t=t||{})},ignoreAll:function(t){return e("wp_smush_ignore_all_failed_items",{type:t})}},common:{dismissNotice:function(t){return e("smush_dismiss_notice",{key:t})},hideModal:function(t){return e("hide_modal",{modal_id:t})},track:function(t,n){return e("smush_analytics_track_event",{event:t,properties:n})},request:function(t){return t.action&&e(t.action,t)}},scanMediaLibrary:{start:function(){var t=arguments.length>0&&void 0!==arguments[0]&&arguments[0];return e("wp_smush_start_background_scan",{optimize_on_scan_completed:t=t?1:0,_ajax_nonce:window.wp_smushit_data.media_library_scan.nonce})},cancel:function(){return e("wp_smush_cancel_background_scan",{_ajax_nonce:window.wp_smushit_data.media_library_scan.nonce})},getScanStatus:function(){return e("wp_smush_get_background_scan_status",{_ajax_nonce:window.wp_smushit_data.media_library_scan.nonce})}},webp:{switchMethod:function(t){return e("webp_switch_method",{method:t})}}};i()(this,t)};t.A=r},7899:function(e,t,n){"use strict";var s=n(9620);function i(e){return i="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},i(e)}function o(e,t){for(var n=0;n<t.length;n++){var s=t[n];s.enumerable=s.enumerable||!1,s.configurable=!0,"value"in s&&(s.writable=!0),Object.defineProperty(e,r(s.key),s)}}function r(e){var t=function(e,t){if("object"!=i(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var s=n.call(e,t||"default");if("object"!=i(s))return s;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==i(t)?t:t+""}var a=function(){return e=function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e)},t=[{key:"track",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(this.allowToTrack())return s.A.common.track(e,t)}},{key:"allowToTrack",value:function(){var e;return!(null===(e=window.wp_smush_mixpanel)||void 0===e||!e.opt_in)}}],t&&o(e.prototype,t),n&&o(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e;var e,t,n}(),u=new a;t.A=u},6006:function(e,t,n){var s,i,o;i=[n(1669),n(1503)],s=function(e){"use strict";if(!e.ui||!e.ui.fancytree){var t,n,s=null,i=new RegExp(/\.|\//),o=/[&<>"'/]/g,r=/[<>"'/]/g,a="$recursive_request",u="$request_target_invalid",l={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;","/":"&#x2F;"},c={16:!0,17:!0,18:!0},d={8:"backspace",9:"tab",10:"return",13:"return",19:"pause",20:"capslock",27:"esc",32:"space",33:"pageup",34:"pagedown",35:"end",36:"home",37:"left",38:"up",39:"right",40:"down",45:"insert",46:"del",59:";",61:"=",96:"0",97:"1",98:"2",99:"3",100:"4",101:"5",102:"6",103:"7",104:"8",105:"9",106:"*",107:"+",109:"-",110:".",111:"/",112:"f1",113:"f2",114:"f3",115:"f4",116:"f5",117:"f6",118:"f7",119:"f8",120:"f9",121:"f10",122:"f11",123:"f12",144:"numlock",145:"scroll",173:"-",186:";",187:"=",188:",",189:"-",190:".",191:"/",192:"`",219:"[",220:"\\",221:"]",222:"'"},h={16:"shift",17:"ctrl",18:"alt",91:"meta",93:"meta"},p={0:"",1:"left",2:"middle",3:"right"},f="active expanded focus folder lazy radiogroup selected unselectable unselectableIgnore".split(" "),m={},g="columns types".split(" "),v="checkbox expanded extraClasses folder icon iconTooltip key lazy partsel radiogroup refKey selected statusNodeType title tooltip type unselectable unselectableIgnore unselectableStatus".split(" "),y={},w={},_={active:!0,children:!0,data:!0,focus:!0};for(t=0;t<f.length;t++)m[f[t]]=!0;for(t=0;t<v.length;t++)n=v[t],y[n]=!0,n!==n.toLowerCase()&&(w[n.toLowerCase()]=n);var b=Array.isArray;return k(e.ui,"Fancytree requires jQuery UI (http://jqueryui.com)"),Date.now||(Date.now=function(){return(new Date).getTime()}),q.prototype={_findDirectChild:function(e){var t,n,s=this.children;if(s)if("string"==typeof e){for(t=0,n=s.length;t<n;t++)if(s[t].key===e)return s[t]}else{if("number"==typeof e)return this.children[e];if(e.parent===this)return e}return null},_setChildren:function(e){k(e&&(!this.children||0===this.children.length),"only init supported"),this.children=[];for(var t=0,n=e.length;t<n;t++)this.children.push(new q(this,e[t]));this.tree._callHook("treeStructureChanged",this.tree,"setChildren")},addChildren:function(t,n){var s,i,o,r=this.getFirstChild(),a=this.getLastChild(),u=null,l=[];for(e.isPlainObject(t)&&(t=[t]),this.children||(this.children=[]),s=0,i=t.length;s<i;s++)l.push(new q(this,t[s]));if(u=l[0],null==n?this.children=this.children.concat(l):(n=this._findDirectChild(n),k((o=e.inArray(n,this.children))>=0,"insertBefore must be an existing child"),this.children.splice.apply(this.children,[o,0].concat(l))),r&&!n){for(s=0,i=l.length;s<i;s++)l[s].render();r!==this.getFirstChild()&&r.renderStatus(),a!==this.getLastChild()&&a.renderStatus()}else(!this.parent||this.parent.ul||this.tr)&&this.render();return 3===this.tree.options.selectMode&&this.fixSelection3FromEndNodes(),this.triggerModifyChild("add",1===l.length?l[0]:null),u},addClass:function(e){return this.toggleClass(e,!0)},addNode:function(e,t){switch(void 0!==t&&"over"!==t||(t="child"),t){case"after":return this.getParent().addChildren(e,this.getNextSibling());case"before":return this.getParent().addChildren(e,this);case"firstChild":var n=this.children?this.children[0]:null;return this.addChildren(e,n);case"child":case"over":return this.addChildren(e)}k(!1,"Invalid mode: "+t)},addPagingNode:function(t,n){var s,i;if(n=n||"child",!1!==t)return t=e.extend({title:this.tree.options.strings.moreData,statusNodeType:"paging",icon:!1},t),this.partload=!0,this.addNode(t,n);for(s=this.children.length-1;s>=0;s--)"paging"===(i=this.children[s]).statusNodeType&&this.removeChild(i);this.partload=!1},appendSibling:function(e){return this.addNode(e,"after")},applyCommand:function(e,t){return this.tree.applyCommand(e,this,t)},applyPatch:function(e){if(null===e)return this.remove(),P(this);var t,n,s={children:!0,expanded:!0,parent:!0};for(t in e)S(e,t)&&(n=e[t],s[t]||x(n)||(y[t]?this[t]=n:this.data[t]=n));return S(e,"children")&&(this.removeChildren(),e.children&&this._setChildren(e.children)),this.isVisible()&&(this.renderTitle(),this.renderStatus()),S(e,"expanded")?this.setExpanded(e.expanded):P(this)},collapseSiblings:function(){return this.tree._callHook("nodeCollapseSiblings",this)},copyTo:function(e,t,n){return e.addNode(this.toDict(!0,n),t)},countChildren:function(e){var t,n,s,i=this.children;if(!i)return 0;if(s=i.length,!1!==e)for(t=0,n=s;t<n;t++)s+=i[t].countChildren();return s},debug:function(e){this.tree.options.debugLevel>=4&&(Array.prototype.unshift.call(arguments,this.toString()),E("log",arguments))},discard:function(){return this.warn("FancytreeNode.discard() is deprecated since 2014-02-16. Use .resetLazy() instead."),this.resetLazy()},discardMarkup:function(e){var t=e?"nodeRemoveMarkup":"nodeRemoveChildMarkup";this.tree._callHook(t,this)},error:function(e){this.tree.options.debugLevel>=1&&(Array.prototype.unshift.call(arguments,this.toString()),E("error",arguments))},findAll:function(e){e=x(e)?e:M(e);var t=[];return this.visit((function(n){e(n)&&t.push(n)})),t},findFirst:function(e){e=x(e)?e:M(e);var t=null;return this.visit((function(n){if(e(n))return t=n,!1})),t},findRelatedNode:function(e,t){return this.tree.findRelatedNode(this,e,t)},_changeSelectStatusAttrs:function(e){var t=!1,n=this.tree.options,i=s.evalOption("unselectable",this,this,n,!1),o=s.evalOption("unselectableStatus",this,this,n,void 0);switch(i&&null!=o&&(e=o),e){case!1:t=this.selected||this.partsel,this.selected=!1,this.partsel=!1;break;case!0:t=!this.selected||!this.partsel,this.selected=!0,this.partsel=!0;break;case void 0:t=this.selected||!this.partsel,this.selected=!1,this.partsel=!0;break;default:k(!1,"invalid state: "+e)}return t&&this.renderStatus(),t},fixSelection3AfterClick:function(e){var t=this.isSelected();this.visit((function(e){if(e._changeSelectStatusAttrs(t),e.radiogroup)return"skip"})),this.fixSelection3FromEndNodes(e)},fixSelection3FromEndNodes:function(e){var t=this.tree.options;function n(e){var i,o,r,a,u,l,c,d,h=e.children;if(h&&h.length){for(l=!0,c=!1,i=0,o=h.length;i<o;i++)a=n(r=h[i]),s.evalOption("unselectableIgnore",r,r,t,!1)||(!1!==a&&(c=!0),!0!==a&&(l=!1));u=!!l||!!c&&void 0}else u=null==(d=s.evalOption("unselectableStatus",e,e,t,void 0))?!!e.selected:!!d;return e.partsel&&!e.selected&&e.lazy&&null==e.children&&(u=void 0),e._changeSelectStatusAttrs(u),u}k(3===t.selectMode,"expected selectMode 3"),n(this),this.visitParents((function(e){var n,i,o,r,a,u=e.children,l=!0,c=!1;for(n=0,i=u.length;n<i;n++)o=u[n],s.evalOption("unselectableIgnore",o,o,t,!1)||(((r=null==(a=s.evalOption("unselectableStatus",o,o,t,void 0))?!!o.selected:!!a)||o.partsel)&&(c=!0),r||(l=!1));r=!!l||!!c&&void 0,e._changeSelectStatusAttrs(r)}))},fromDict:function(t){for(var n in t)y[n]?this[n]=t[n]:"data"===n?e.extend(this.data,t.data):x(t[n])||_[n]||(this.data[n]=t[n]);t.children&&(this.removeChildren(),this.addChildren(t.children)),this.renderTitle()},getChildren:function(){if(void 0!==this.hasChildren())return this.children},getFirstChild:function(){return this.children?this.children[0]:null},getIndex:function(){return e.inArray(this,this.parent.children)},getIndexHier:function(t,n){t=t||".";var s,i=[];return e.each(this.getParentList(!1,!0),(function(e,t){s=""+(t.getIndex()+1),n&&(s=("0000000"+s).substr(-n)),i.push(s)})),i.join(t)},getKeyPath:function(e){var t=this.tree.options.keyPathSeparator;return t+this.getPath(!e,"key",t)},getLastChild:function(){return this.children?this.children[this.children.length-1]:null},getLevel:function(){for(var e=0,t=this.parent;t;)e++,t=t.parent;return e},getNextSibling:function(){if(this.parent){var e,t,n=this.parent.children;for(e=0,t=n.length-1;e<t;e++)if(n[e]===this)return n[e+1]}return null},getParent:function(){return this.parent},getParentList:function(e,t){for(var n=[],s=t?this:this.parent;s;)(e||s.parent)&&n.unshift(s),s=s.parent;return n},getPath:function(e,t,n){e=!1!==e,n=n||"/";var s,i=[],o=x(t=t||"title");return this.visitParents((function(e){e.parent&&(s=o?t(e):e[t],i.unshift(s))}),e),i.join(n)},getPrevSibling:function(){if(this.parent){var e,t,n=this.parent.children;for(e=1,t=n.length;e<t;e++)if(n[e]===this)return n[e-1]}return null},getSelectedNodes:function(e){var t=[];return this.visit((function(n){if(n.selected&&(t.push(n),!0===e))return"skip"})),t},hasChildren:function(){if(this.lazy){if(null==this.children)return;if(0===this.children.length)return!1;if(1===this.children.length&&this.children[0].isStatusNode())return;return!0}return!(!this.children||!this.children.length)},hasClass:function(e){return(" "+(this.extraClasses||"")+" ").indexOf(" "+e+" ")>=0},hasFocus:function(){return this.tree.hasFocus()&&this.tree.focusNode===this},info:function(e){this.tree.options.debugLevel>=3&&(Array.prototype.unshift.call(arguments,this.toString()),E("info",arguments))},isActive:function(){return this.tree.activeNode===this},isBelowOf:function(e){return this.getIndexHier(".",5)>e.getIndexHier(".",5)},isChildOf:function(e){return this.parent&&this.parent===e},isDescendantOf:function(t){if(!t||t.tree!==this.tree)return!1;for(var n=this.parent;n;){if(n===t)return!0;n===n.parent&&e.error("Recursive parent link: "+n),n=n.parent}return!1},isExpanded:function(){return!!this.expanded},isFirstSibling:function(){var e=this.parent;return!e||e.children[0]===this},isFolder:function(){return!!this.folder},isLastSibling:function(){var e=this.parent;return!e||e.children[e.children.length-1]===this},isLazy:function(){return!!this.lazy},isLoaded:function(){return!this.lazy||void 0!==this.hasChildren()},isLoading:function(){return!!this._isLoading},isRoot:function(){return this.isRootNode()},isPartsel:function(){return!this.selected&&!!this.partsel},isPartload:function(){return!!this.partload},isRootNode:function(){return this.tree.rootNode===this},isSelected:function(){return!!this.selected},isStatusNode:function(){return!!this.statusNodeType},isPagingNode:function(){return"paging"===this.statusNodeType},isTopLevel:function(){return this.tree.rootNode===this.parent},isUndefined:function(){return void 0===this.hasChildren()},isVisible:function(){var e,t,n=this.tree.enableFilter,s=this.getParentList(!1,!1);if(n&&!this.match&&!this.subMatchCount)return!1;for(e=0,t=s.length;e<t;e++)if(!s[e].expanded)return!1;return!0},lazyLoad:function(t){e.error("FancytreeNode.lazyLoad() is deprecated since 2014-02-16. Use .load() instead.")},load:function(e){var t,n,s=this,i=this.isExpanded();return k(this.isLazy(),"load() requires a lazy node"),e||this.isUndefined()?(this.isLoaded()&&this.resetLazy(),!1===(n=this.tree._triggerNodeEvent("lazyLoad",this))?P(this):(k("boolean"!=typeof n,"lazyLoad event must return source in data.result"),t=this.tree._callHook("nodeLoadChildren",this,n),i?(this.expanded=!0,t.always((function(){s.render()}))):t.always((function(){s.renderStatus()})),t)):P(this)},makeVisible:function(t){var n,s=this,i=[],o=new e.Deferred,r=this.getParentList(!1,!1),a=r.length,u=!(t&&!0===t.noAnimation),l=!(t&&!1===t.scrollIntoView);for(n=a-1;n>=0;n--)i.push(r[n].setExpanded(!0,t));return e.when.apply(e,i).done((function(){l?s.scrollIntoView(u).done((function(){o.resolve()})):o.resolve()})),o.promise()},moveTo:function(t,n,s){void 0===n||"over"===n?n="child":"firstChild"===n&&(t.children&&t.children.length?(n="before",t=t.children[0]):n="child");var i,o=this.tree,r=this.parent,a="child"===n?t:t.parent;if(this!==t){if(this.parent?a.isDescendantOf(this)&&e.error("Cannot move a node to its own descendant"):e.error("Cannot move system root"),a!==r&&r.triggerModifyChild("remove",this),1===this.parent.children.length){if(this.parent===a)return;this.parent.children=this.parent.lazy?[]:null,this.parent.expanded=!1}else k((i=e.inArray(this,this.parent.children))>=0,"invalid source parent"),this.parent.children.splice(i,1);if(this.parent=a,a.hasChildren())switch(n){case"child":a.children.push(this);break;case"before":k((i=e.inArray(t,a.children))>=0,"invalid target parent"),a.children.splice(i,0,this);break;case"after":k((i=e.inArray(t,a.children))>=0,"invalid target parent"),a.children.splice(i+1,0,this);break;default:e.error("Invalid mode "+n)}else a.children=[this];s&&t.visit(s,!0),a===r?a.triggerModifyChild("move",this):a.triggerModifyChild("add",this),o!==t.tree&&(this.warn("Cross-tree moveTo is experimental!"),this.visit((function(e){e.tree=t.tree}),!0)),o._callHook("treeStructureChanged",o,"moveTo"),r.isDescendantOf(a)||r.render(),a.isDescendantOf(r)||a===r||a.render()}},navigate:function(t,n){var s,i=e.ui.keyCode;switch(t){case"left":case i.LEFT:if(this.expanded)return this.setExpanded(!1);break;case"right":case i.RIGHT:if(!this.expanded&&(this.children||this.lazy))return this.setExpanded()}if(s=this.findRelatedNode(t)){try{s.makeVisible({scrollIntoView:!1})}catch(e){}return!1===n?(s.setFocus(),P()):s.setActive()}return this.warn("Could not find related node '"+t+"'."),P()},remove:function(){return this.parent.removeChild(this)},removeChild:function(e){return this.tree._callHook("nodeRemoveChild",this,e)},removeChildren:function(){return this.tree._callHook("nodeRemoveChildren",this)},removeClass:function(e){return this.toggleClass(e,!1)},render:function(e,t){return this.tree._callHook("nodeRender",this,e,t)},renderTitle:function(){return this.tree._callHook("nodeRenderTitle",this)},renderStatus:function(){return this.tree._callHook("nodeRenderStatus",this)},replaceWith:function(n){var s,i=this.parent,o=e.inArray(this,i.children),r=this;return k(this.isPagingNode(),"replaceWith() currently requires a paging status node"),(s=this.tree._callHook("nodeLoadChildren",this,n)).done((function(e){var n=r.children;for(t=0;t<n.length;t++)n[t].parent=i;i.children.splice.apply(i.children,[o+1,0].concat(n)),r.children=null,r.remove(),i.render()})).fail((function(){r.setExpanded()})),s},resetLazy:function(){this.removeChildren(),this.expanded=!1,this.lazy=!0,this.children=void 0,this.renderStatus()},scheduleAction:function(t,n){this.tree.timer&&(clearTimeout(this.tree.timer),this.tree.debug("clearTimeout(%o)",this.tree.timer)),this.tree.timer=null;var s=this;switch(t){case"cancel":break;case"expand":this.tree.timer=setTimeout((function(){s.tree.debug("setTimeout: trigger expand"),s.setExpanded(!0)}),n);break;case"activate":this.tree.timer=setTimeout((function(){s.tree.debug("setTimeout: trigger activate"),s.setActive(!0)}),n);break;default:e.error("Invalid mode "+t)}},scrollIntoView:function(t,n){if(void 0!==n&&N(n))throw Error("scrollIntoView() with 'topNode' option is deprecated since 2014-05-08. Use 'options.topNode' instead.");var s=e.extend({effects:!0===t?{duration:200,queue:!1}:t,scrollOfs:this.tree.options.scrollOfs,scrollParent:this.tree.options.scrollParent,topNode:null},n),i=s.scrollParent,o=this.tree.$container,r=o.css("overflow-y");i?i.jquery||(i=e(i)):i=this.tree.tbody?o.scrollParent():"scroll"===r||"auto"===r?o:o.scrollParent(),i[0]!==document&&i[0]!==document.body||(this.debug("scrollIntoView(): normalizing scrollParent to 'window':",i[0]),i=e(window));var a,u,l,c=new e.Deferred,d=this,h=e(this.span).height(),p=s.scrollOfs.top||0,f=s.scrollOfs.bottom||0,m=i.height(),g=i.scrollTop(),v=i,y=i[0]===window,w=s.topNode||null,_=null;return this.isRootNode()||!this.isVisible()?(this.info("scrollIntoView(): node is invisible."),P()):(y?(u=e(this.span).offset().top,a=w&&w.span?e(w.span).offset().top:0,v=e("html,body")):(k(i[0]!==document&&i[0]!==document.body,"scrollParent should be a simple element or `window`, not document or body."),l=i.offset().top,u=e(this.span).offset().top-l+g,a=w?e(w.span).offset().top-l+g:0,m-=Math.max(0,i.innerHeight()-i[0].clientHeight)),u<g+p?_=u-p:u+h>g+m-f&&(_=u+h-m+f,w&&(k(w.isRootNode()||w.isVisible(),"topNode must be visible"),a<_&&(_=a-p))),null===_?c.resolveWith(this):s.effects?(s.effects.complete=function(){c.resolveWith(d)},v.stop(!0).animate({scrollTop:_},s.effects)):(v[0].scrollTop=_,c.resolveWith(this)),c.promise())},setActive:function(e,t){return this.tree._callHook("nodeSetActive",this,e,t)},setExpanded:function(e,t){return this.tree._callHook("nodeSetExpanded",this,e,t)},setFocus:function(e){return this.tree._callHook("nodeSetFocus",this,e)},setSelected:function(e,t){return this.tree._callHook("nodeSetSelected",this,e,t)},setStatus:function(e,t,n){return this.tree._callHook("nodeSetStatus",this,e,t,n)},setTitle:function(e){this.title=e,this.renderTitle(),this.triggerModify("rename")},sortChildren:function(e,t){var n,s,i=this.children;if(i){if(e=e||function(e,t){var n=e.title.toLowerCase(),s=t.title.toLowerCase();return n===s?0:n>s?1:-1},i.sort(e),t)for(n=0,s=i.length;n<s;n++)i[n].children&&i[n].sortChildren(e,"$norender$");"$norender$"!==t&&this.render(),this.triggerModifyChild("sort")}},toDict:function(t,n){var s,i,o,r,a={},u=this;if(e.each(v,(function(e,t){(u[t]||!1===u[t])&&(a[t]=u[t])})),e.isEmptyObject(this.data)||(a.data=e.extend({},this.data),e.isEmptyObject(a.data)&&delete a.data),n){if(!1===(r=n(a,u)))return!1;"skip"===r&&(t=!1)}if(t&&b(this.children))for(a.children=[],s=0,i=this.children.length;s<i;s++)(o=this.children[s]).isStatusNode()||!1!==(r=o.toDict(!0,n))&&a.children.push(r);return a},toggleClass:function(t,n){var s,i,o=/\S+/g,r=t.match(o)||[],a=0,u=!1,l=this[this.tree.statusClassPropName],c=" "+(this.extraClasses||"")+" ";for(l&&e(l).toggleClass(t,n);s=r[a++];)if(i=c.indexOf(" "+s+" ")>=0,n=void 0===n?!i:!!n)i||(c+=s+" ",u=!0);else for(;c.indexOf(" "+s+" ")>-1;)c=c.replace(" "+s+" "," ");return this.extraClasses=C(c),u},toggleExpanded:function(){return this.tree._callHook("nodeToggleExpanded",this)},toggleSelected:function(){return this.tree._callHook("nodeToggleSelected",this)},toString:function(){return"FancytreeNode@"+this.key+"[title='"+this.title+"']"},triggerModifyChild:function(t,n,s){var i,o=this.tree.options.modifyChild;o&&(n&&n.parent!==this&&e.error("childNode "+n+" is not a child of "+this),i={node:this,tree:this.tree,operation:t,childNode:n||null},s&&e.extend(i,s),o({type:"modifyChild"},i))},triggerModify:function(e,t){this.parent.triggerModifyChild(e,this,t)},visit:function(e,t){var n,s,i=!0,o=this.children;if(!0===t&&(!1===(i=e(this))||"skip"===i))return i;if(o)for(n=0,s=o.length;n<s&&!1!==(i=o[n].visit(e,!0));n++);return i},visitAndLoad:function(t,n,s){var i,o,r,a=this;return!t||!0!==n||!1!==(o=t(a))&&"skip"!==o?a.children||a.lazy?(i=new e.Deferred,r=[],a.load().done((function(){for(var n=0,s=a.children.length;n<s;n++){if(!1===(o=a.children[n].visitAndLoad(t,!0,!0))){i.reject();break}"skip"!==o&&r.push(o)}e.when.apply(this,r).then((function(){i.resolve()}))})),i.promise()):P():s?o:P()},visitParents:function(e,t){if(t&&!1===e(this))return!1;for(var n=this.parent;n;){if(!1===e(n))return!1;n=n.parent}return!0},visitSiblings:function(e,t){var n,s,i,o=this.parent.children;for(n=0,s=o.length;n<s;n++)if(i=o[n],(t||i!==this)&&!1===e(i))return!1;return!0},warn:function(e){this.tree.options.debugLevel>=2&&(Array.prototype.unshift.call(arguments,this.toString()),E("warn",arguments))}},F.prototype={_makeHookContext:function(t,n,s){var i,o;return void 0!==t.node?(n&&t.originalEvent!==n&&e.error("invalid args"),i=t):t.tree?i={node:t,tree:o=t.tree,widget:o.widget,options:o.widget.options,originalEvent:n,typeInfo:o.types[t.type]||{}}:t.widget?i={node:null,tree:t,widget:t.widget,options:t.widget.options,originalEvent:n}:e.error("invalid args"),s&&e.extend(i,s),i},_callHook:function(t,n,s){var i=this._makeHookContext(n),o=this[t],r=Array.prototype.slice.call(arguments,2);return x(o)||e.error("_callHook('"+t+"') is not a function"),r.unshift(i),o.apply(this,r)},_setExpiringValue:function(e,t,n){this._tempCache[e]={value:t,expire:Date.now()+(+n||50)}},_getExpiringValue:function(e){var t=this._tempCache[e];return t&&t.expire>Date.now()?t.value:(delete this._tempCache[e],null)},_usesExtension:function(t){return e.inArray(t,this.options.extensions)>=0},_requireExtension:function(t,n,s,i){null!=s&&(s=!!s);var o=this._local.name,r=this.options.extensions,a=e.inArray(t,r)<e.inArray(o,r),u=n&&null==this.ext[t],l=!u&&null!=s&&s!==a;return k(o&&o!==t,"invalid or same name '"+o+"' (require yourself?)"),!u&&!l||(i||(u||n?(i="'"+o+"' extension requires '"+t+"'",l&&(i+=" to be registered "+(s?"before":"after")+" itself")):i="If used together, `"+t+"` must be registered "+(s?"before":"after")+" `"+o+"`"),e.error(i),!1)},activateKey:function(e,t){var n=this.getNodeByKey(e);return n?n.setActive(!0,t):this.activeNode&&this.activeNode.setActive(!1,t),n},addPagingNode:function(e,t){return this.rootNode.addPagingNode(e,t)},applyCommand:function(t,n,s){var i;switch(n=n||this.getActiveNode(),t){case"moveUp":(i=n.getPrevSibling())&&(n.moveTo(i,"before"),n.setActive());break;case"moveDown":(i=n.getNextSibling())&&(n.moveTo(i,"after"),n.setActive());break;case"indent":(i=n.getPrevSibling())&&(n.moveTo(i,"child"),i.setExpanded(),n.setActive());break;case"outdent":n.isTopLevel()||(n.moveTo(n.getParent(),"after"),n.setActive());break;case"remove":i=n.getPrevSibling()||n.getParent(),n.remove(),i&&i.setActive();break;case"addChild":n.editCreateNode("child","");break;case"addSibling":n.editCreateNode("after","");break;case"rename":n.editStart();break;case"down":case"first":case"last":case"left":case"parent":case"right":case"up":return n.navigate(t);default:e.error("Unhandled command: '"+t+"'")}},applyPatch:function(t){var n,s,i,o,r,a,u=t.length,l=[];for(s=0;s<u;s++)k(2===(i=t[s]).length,"patchList must be an array of length-2-arrays"),o=i[0],r=i[1],(a=null===o?this.rootNode:this.getNodeByKey(o))?(n=new e.Deferred,l.push(n),a.applyPatch(r).always(O(n,a))):this.warn("could not find node with key '"+o+"'");return e.when.apply(e,l).promise()},clear:function(e){this._callHook("treeClear",this)},count:function(){return this.rootNode.countChildren()},debug:function(e){this.options.debugLevel>=4&&(Array.prototype.unshift.call(arguments,this.toString()),E("log",arguments))},destroy:function(){this.widget.destroy()},enable:function(e){!1===e?this.widget.disable():this.widget.enable()},enableUpdate:function(e){return e=!1!==e,!!this._enableUpdate==!!e?e:(this._enableUpdate=e,e?(this.debug("enableUpdate(true): redraw "),this._callHook("treeStructureChanged",this,"enableUpdate"),this.render()):this.debug("enableUpdate(false)..."),!e)},error:function(e){this.options.debugLevel>=1&&(Array.prototype.unshift.call(arguments,this.toString()),E("error",arguments))},expandAll:function(e,t){var n=this.enableUpdate(!1);e=!1!==e,this.visit((function(n){!1!==n.hasChildren()&&n.isExpanded()!==e&&n.setExpanded(e,t)})),this.enableUpdate(n)},findAll:function(e){return this.rootNode.findAll(e)},findFirst:function(e){return this.rootNode.findFirst(e)},findNextNode:function(e,t){var n=null,s=this.getFirstChild();function i(s){if(e(s)&&(n=s),n||s===t)return!1}return e="string"==typeof e?D(e):e,t=t||s,this.visitRows(i,{start:t,includeSelf:!1}),n||t===s||this.visitRows(i,{start:s,includeSelf:!0}),n},findRelatedNode:function(t,n,s){var i=null,o=e.ui.keyCode;switch(n){case"parent":case o.BACKSPACE:t.parent&&t.parent.parent&&(i=t.parent);break;case"first":case o.HOME:this.visit((function(e){if(e.isVisible())return i=e,!1}));break;case"last":case o.END:this.visit((function(e){e.isVisible()&&(i=e)}));break;case"left":case o.LEFT:t.expanded?t.setExpanded(!1):t.parent&&t.parent.parent&&(i=t.parent);break;case"right":case o.RIGHT:t.expanded||!t.children&&!t.lazy?t.children&&t.children.length&&(i=t.children[0]):(t.setExpanded(),i=t);break;case"up":case o.UP:this.visitRows((function(e){return i=e,!1}),{start:t,reverse:!0,includeSelf:!1});break;case"down":case o.DOWN:this.visitRows((function(e){return i=e,!1}),{start:t,includeSelf:!1});break;default:this.tree.warn("Unknown relation '"+n+"'.")}return i},generateFormElements:function(t,n,s){s=s||{};var i,o="string"==typeof t?t:"ft_"+this._id+"[]",r="string"==typeof n?n:"ft_"+this._id+"_active",a="fancytree_result_"+this._id,u=e("#"+a),l=3===this.options.selectMode&&!1!==s.stopOnParents;function c(t){u.append(e("<input>",{type:"checkbox",name:o,value:t.key,checked:!0}))}u.length?u.empty():u=e("<div>",{id:a}).hide().insertAfter(this.$container),!1!==n&&this.activeNode&&u.append(e("<input>",{type:"radio",name:r,value:this.activeNode.key,checked:!0})),s.filter?this.visit((function(e){var t=s.filter(e);if("skip"===t)return t;!1!==t&&c(e)})):!1!==t&&(i=this.getSelectedNodes(l),e.each(i,(function(e,t){c(t)})))},getActiveNode:function(){return this.activeNode},getFirstChild:function(){return this.rootNode.getFirstChild()},getFocusNode:function(){return this.focusNode},getOption:function(e){return this.widget.option(e)},getNodeByKey:function(e,t){var n,s;return!t&&(n=document.getElementById(this.options.idPrefix+e))?n.ftnode?n.ftnode:null:(t=t||this.rootNode,s=null,e=""+e,t.visit((function(t){if(t.key===e)return s=t,!1}),!0),s)},getRootNode:function(){return this.rootNode},getSelectedNodes:function(e){return this.rootNode.getSelectedNodes(e)},hasFocus:function(){return!!this._hasFocus},info:function(e){this.options.debugLevel>=3&&(Array.prototype.unshift.call(arguments,this.toString()),E("info",arguments))},isLoading:function(){var e=!1;return this.rootNode.visit((function(t){if(t._isLoading||t._requestId)return e=!0,!1}),!0),e},loadKeyPath:function(t,n){var s,i,o,r=this,a=new e.Deferred,u=this.getRootNode(),l=this.options.keyPathSeparator,c=[],d=e.extend({},n);for("function"==typeof n?s=n:n&&n.callback&&(s=n.callback),d.callback=function(e,t,n){s&&s.call(e,t,n),a.notifyWith(e,[{node:t,status:n}])},null==d.matchKey&&(d.matchKey=function(e,t){return e.key===t}),b(t)||(t=[t]),i=0;i<t.length;i++)(o=t[i]).charAt(0)===l&&(o=o.substr(1)),c.push(o.split(l));return setTimeout((function(){r._loadKeyPathImpl(a,d,u,c).done((function(){a.resolve()}))}),0),a.promise()},_loadKeyPathImpl:function(t,n,s,i){var o,r,a,u,l,c,d,h,p,f,m=this;function g(e,t){var s,i,o=e.children;if(o)for(s=0,i=o.length;s<i;s++)if(n.matchKey(o[s],t))return o[s];return null}for(d={},r=0;r<i.length;r++)for(p=i[r],h=s;p.length;){if(a=p.shift(),!(u=g(h,a))){this.warn("loadKeyPath: key not found: "+a+" (parent: "+h+")"),n.callback(this,a,"error");break}if(0===p.length){n.callback(this,u,"ok");break}if(u.lazy&&void 0===u.hasChildren()){n.callback(this,u,"loaded"),d[a=u.key]?d[a].pathSegList.push(p):d[a]={parent:u,pathSegList:[p]};break}n.callback(this,u,"loaded"),h=u}function v(e,t,s){n.callback(m,t,"loading"),t.load().done((function(){m._loadKeyPathImpl.call(m,e,n,t,s).always(O(e,m))})).fail((function(s){m.warn("loadKeyPath: error loading lazy "+t),n.callback(m,u,"error"),e.rejectWith(m)}))}for(l in o=[],d)S(d,l)&&(c=d[l],f=new e.Deferred,o.push(f),v(f,c.parent,c.pathSegList));return e.when.apply(e,o).promise()},reactivate:function(e){var t,n=this.activeNode;return n?(this.activeNode=null,t=n.setActive(!0,{noFocus:!0}),e&&n.setFocus(),t):P()},reload:function(e){return this._callHook("treeClear",this),this._callHook("treeLoad",this,e)},render:function(e,t){return this.rootNode.render(e,t)},selectAll:function(e){this.visit((function(t){t.setSelected(e)}))},setFocus:function(e){return this._callHook("treeSetFocus",this,e)},setOption:function(e,t){return this.widget.option(e,t)},debugTime:function(e){this.options.debugLevel>=4&&window.console.time(this+" - "+e)},debugTimeEnd:function(e){this.options.debugLevel>=4&&window.console.timeEnd(this+" - "+e)},toDict:function(e,t){var n=this.rootNode.toDict(!0,t);return e?n:n.children},toString:function(){return"Fancytree@"+this._id},_triggerNodeEvent:function(e,t,n,s){var i=this._makeHookContext(t,n,s),o=this.widget._trigger(e,n,i);return!1!==o&&void 0!==i.result?i.result:o},_triggerTreeEvent:function(e,t,n){var s=this._makeHookContext(this,t,n),i=this.widget._trigger(e,t,s);return!1!==i&&void 0!==s.result?s.result:i},visit:function(e){return this.rootNode.visit(e,!1)},visitRows:function(e,t){if(!this.rootNode.hasChildren())return!1;if(t&&t.reverse)return delete t.reverse,this._visitRowsUp(e,t);var n,s,i,o,r=0,a=!1===(t=t||{}).includeSelf,u=!!t.includeHidden,l=!u&&this.enableFilter,c=t.start||this.rootNode.children[0];for(i=c.parent;i;){for(k((s=(o=i.children).indexOf(c)+r)>=0,"Could not find "+c+" in parent's children: "+i),n=s;n<o.length;n++)if(c=o[n],!l||c.match||c.subMatchCount){if(!a&&!1===e(c))return!1;if(a=!1,c.children&&c.children.length&&(u||c.expanded)&&!1===c.visit((function(t){return!l||t.match||t.subMatchCount?!1!==e(t)&&(u||!t.children||t.expanded?void 0:"skip"):"skip"}),!1))return!1}c=i,i=i.parent,r=1}return!0},_visitRowsUp:function(e,t){for(var n,s,i,o=!!t.includeHidden,r=t.start||this.rootNode.children[0];;){if((n=(i=r.parent).children)[0]===r){if(!(r=i).parent)break;n=i.children}else for(s=n.indexOf(r),r=n[s-1];(o||r.expanded)&&r.children&&r.children.length;)i=r,r=(n=r.children)[n.length-1];if((o||r.isVisible())&&!1===e(r))return!1}},warn:function(e){this.options.debugLevel>=2&&(Array.prototype.unshift.call(arguments,this.toString()),E("warn",arguments))}},e.extend(F.prototype,{nodeClick:function(e){var t,n,s=e.targetType,i=e.node;if("expander"===s){if(i.isLoading())return void i.debug("Got 2nd click while loading: ignored");this._callHook("nodeToggleExpanded",e)}else if("checkbox"===s)this._callHook("nodeToggleSelected",e),e.options.focusOnSelect&&this._callHook("nodeSetFocus",e,!0);else{if(n=!1,t=!0,i.folder)switch(e.options.clickFolderMode){case 2:n=!0,t=!1;break;case 3:t=!0,n=!0}t&&(this.nodeSetFocus(e),this._callHook("nodeSetActive",e,!0)),n&&this._callHook("nodeToggleExpanded",e)}},nodeCollapseSiblings:function(e,t){var n,s,i,o=e.node;if(o.parent)for(s=0,i=(n=o.parent.children).length;s<i;s++)n[s]!==o&&n[s].expanded&&this._callHook("nodeSetExpanded",n[s],!1,t)},nodeDblclick:function(e){"title"===e.targetType&&4===e.options.clickFolderMode&&this._callHook("nodeToggleExpanded",e),"title"===e.targetType&&e.originalEvent.preventDefault()},nodeKeydown:function(t){var n,i,o,r=t.originalEvent,a=t.node,u=t.tree,l=t.options,c=r.which,p=r.key||String.fromCharCode(c),f=!!(r.altKey||r.ctrlKey||r.metaKey),m=!h[c]&&!d[c]&&!f,g=e(r.target),v=!0,y=!(r.ctrlKey||!l.autoActivate);if(a||(o=this.getActiveNode()||this.getFirstChild())&&(o.setFocus(),(a=t.node=this.focusNode).debug("Keydown force focus on active node")),l.quicksearch&&m&&!g.is(":input:enabled"))return(i=Date.now())-u.lastQuicksearchTime>500&&(u.lastQuicksearchTerm=""),u.lastQuicksearchTime=i,u.lastQuicksearchTerm+=p,(n=u.findNextNode(u.lastQuicksearchTerm,u.getActiveNode()))&&n.setActive(),void r.preventDefault();switch(s.eventToString(r)){case"+":case"=":u.nodeSetExpanded(t,!0);break;case"-":u.nodeSetExpanded(t,!1);break;case"space":a.isPagingNode()?u._triggerNodeEvent("clickPaging",t,r):s.evalOption("checkbox",a,a,l,!1)?u.nodeToggleSelected(t):u.nodeSetActive(t,!0);break;case"return":u.nodeSetActive(t,!0);break;case"home":case"end":case"backspace":case"left":case"right":case"up":case"down":a.navigate(r.which,y);break;default:v=!1}v&&r.preventDefault()},nodeLoadChildren:function(t,n){var s,i,o,r=null,l=!0,c=t.tree,d=t.node,h=d.parent,p="nodeLoadChildren",f=Date.now();return x(n)&&k(!x(n=n.call(c,{type:"source"},t)),"source callback must not return another function"),x(n.then)?r=n:n.url?(s=e.extend({},t.options.ajax,n)).debugDelay?(i=s.debugDelay,delete s.debugDelay,b(i)&&(i=i[0]+Math.random()*(i[1]-i[0])),d.warn("nodeLoadChildren waiting debugDelay "+Math.round(i)+" ms ..."),r=e.Deferred((function(t){setTimeout((function(){e.ajax(s).done((function(){t.resolveWith(this,arguments)})).fail((function(){t.rejectWith(this,arguments)}))}),i)}))):r=e.ajax(s):e.isPlainObject(n)||b(n)?(r={then:function(e,t){e(n,null,null)}},l=!1):e.error("Invalid source type: "+n),d._requestId&&(d.warn("Recursive load request #"+f+" while #"+d._requestId+" is pending."),d._requestId=f),l&&(c.debugTime(p),c.nodeSetStatus(t,"loading")),o=new e.Deferred,r.then((function(s,i,r){var l,p;if("json"!==n.dataType&&"jsonp"!==n.dataType||"string"!=typeof s||e.error("Ajax request returned a string (did you get the JSON dataType wrong?)."),d._requestId&&d._requestId>f)o.rejectWith(this,[a]);else if(null!==d.parent||null===h){if(t.options.postProcess){try{(p=c._triggerNodeEvent("postProcess",t,t.originalEvent,{response:s,error:null,dataType:n.dataType})).error&&c.warn("postProcess returned error:",p)}catch(e){p={error:e,message:""+e,details:"postProcess failed"}}if(p.error)return l=e.isPlainObject(p.error)?p.error:{message:p.error},l=c._makeHookContext(d,null,l),void o.rejectWith(this,[l]);(b(p)||e.isPlainObject(p)&&b(p.children))&&(s=p)}else s&&S(s,"d")&&t.options.enableAspx&&(42===t.options.enableAspx&&c.warn("The default for enableAspx will change to `false` in the fututure. Pass `enableAspx: true` or implement postProcess to silence this warning."),s="string"==typeof s.d?e.parseJSON(s.d):s.d);o.resolveWith(this,[s])}else o.rejectWith(this,[u])}),(function(e,t,n){var s=c._makeHookContext(d,null,{error:e,args:Array.prototype.slice.call(arguments),message:n,details:e.status+": "+n});o.rejectWith(this,[s])})),o.done((function(n){var s,i,o;c.nodeSetStatus(t,"ok"),e.isPlainObject(n)?(k(d.isRootNode(),"source may only be an object for root nodes (expecting an array of child objects otherwise)"),k(b(n.children),"if an object is passed as source, it must contain a 'children' array (all other properties are added to 'tree.data')"),i=n,s=n.children,delete i.children,e.each(g,(function(e,t){void 0!==i[t]&&(c[t]=i[t],delete i[t])})),e.extend(c.data,i)):s=n,k(b(s),"expected array of children"),d._setChildren(s),c.options.nodata&&0===s.length&&(x(c.options.nodata)?o=c.options.nodata.call(c,{type:"nodata"},t):!0===c.options.nodata&&d.isRootNode()?o=c.options.strings.noData:"string"==typeof c.options.nodata&&d.isRootNode()&&(o=c.options.nodata),o&&d.setStatus("nodata",o)),c._triggerNodeEvent("loadChildren",d)})).fail((function(e){var n;e!==a?e!==u?(e.node&&e.error&&e.message?n=e:"[object Object]"===(n=c._makeHookContext(d,null,{error:e,args:Array.prototype.slice.call(arguments),message:e?e.message||e.toString():""})).message&&(n.message=""),d.warn("Load children failed ("+n.message+")",n),!1!==c._triggerNodeEvent("loadError",n,null)&&c.nodeSetStatus(t,"error",n.message,n.details)):d.warn("Lazy parent node was removed while loading: discarding response."):d.warn("Ignored response for obsolete load request #"+f+" (expected #"+d._requestId+")")})).always((function(){d._requestId=null,l&&c.debugTimeEnd(p)})),o.promise()},nodeLoadKeyPath:function(e,t){},nodeRemoveChild:function(t,n){var s,i=t.node,o=e.extend({},t,{node:n}),r=i.children;if(1===r.length)return k(n===r[0],"invalid single child"),this.nodeRemoveChildren(t);this.activeNode&&(n===this.activeNode||this.activeNode.isDescendantOf(n))&&this.activeNode.setActive(!1),this.focusNode&&(n===this.focusNode||this.focusNode.isDescendantOf(n))&&(this.focusNode=null),this.nodeRemoveMarkup(o),this.nodeRemoveChildren(o),k((s=e.inArray(n,r))>=0,"invalid child"),i.triggerModifyChild("remove",n),n.visit((function(e){e.parent=null}),!0),this._callHook("treeRegisterNode",this,!1,n),r.splice(s,1)},nodeRemoveChildMarkup:function(t){var n=t.node;n.ul&&(n.isRootNode()?e(n.ul).empty():(e(n.ul).remove(),n.ul=null),n.visit((function(e){e.li=e.ul=null})))},nodeRemoveChildren:function(e){var t=e.tree,n=e.node;n.children&&(this.activeNode&&this.activeNode.isDescendantOf(n)&&this.activeNode.setActive(!1),this.focusNode&&this.focusNode.isDescendantOf(n)&&(this.focusNode=null),this.nodeRemoveChildMarkup(e),n.triggerModifyChild("remove",null),n.visit((function(e){e.parent=null,t._callHook("treeRegisterNode",t,!1,e)})),n.lazy?n.children=[]:n.children=null,n.isRootNode()||(n.expanded=!1),this.nodeRenderStatus(e))},nodeRemoveMarkup:function(t){var n=t.node;n.li&&(e(n.li).remove(),n.li=null),this.nodeRemoveChildMarkup(t)},nodeRender:function(t,n,s,i,o){var r,a,u,l,c,d,h,p=t.node,f=t.tree,m=t.options,g=m.aria,v=!1,y=p.parent,w=!y,_=p.children,b=null;if(!1!==f._enableUpdate&&(w||y.ul)){if(k(w||y.ul,"parent UL must exist"),w||(p.li&&(n||p.li.parentNode!==p.parent.ul)&&(p.li.parentNode===p.parent.ul?b=p.li.nextSibling:this.debug("Unlinking "+p+" (must be child of "+p.parent+")"),this.nodeRemoveMarkup(t)),p.li?this.nodeRenderStatus(t):(v=!0,p.li=document.createElement("li"),p.li.ftnode=p,p.key&&m.generateIds&&(p.li.id=m.idPrefix+p.key),p.span=document.createElement("span"),p.span.className="fancytree-node",g&&!p.tr&&e(p.li).attr("role","treeitem"),p.li.appendChild(p.span),this.nodeRenderTitle(t),m.createNode&&m.createNode.call(f,{type:"createNode"},t)),m.renderNode&&m.renderNode.call(f,{type:"renderNode"},t)),_){if(w||p.expanded||!0===s){for(p.ul||(p.ul=document.createElement("ul"),(!0!==i||o)&&p.expanded||(p.ul.style.display="none"),g&&e(p.ul).attr("role","group"),p.li?p.li.appendChild(p.ul):p.tree.$div.append(p.ul)),l=0,c=_.length;l<c;l++)h=e.extend({},t,{node:_[l]}),this.nodeRender(h,n,s,!1,!0);for(r=p.ul.firstChild;r;)(u=r.ftnode)&&u.parent!==p?(p.debug("_fixParent: remove missing "+u,r),d=r.nextSibling,r.parentNode.removeChild(r),r=d):r=r.nextSibling;for(r=p.ul.firstChild,l=0,c=_.length-1;l<c;l++)(a=_[l])===(u=r.ftnode)?r=r.nextSibling:p.ul.insertBefore(a.li,u.li)}}else p.ul&&(this.warn("remove child markup for "+p),this.nodeRemoveChildMarkup(t));w||v&&y.ul.insertBefore(p.li,b)}},nodeRenderTitle:function(t,n){var o,r,a,u,l,c,d,h=t.node,p=t.tree,f=t.options,m=f.aria,g=h.getLevel(),v=[];void 0!==n&&(h.title=n),h.span&&!1!==p._enableUpdate&&(l=m&&!1!==h.hasChildren()?" role='button'":"",g<f.minExpandLevel?(h.lazy||(h.expanded=!0),g>1&&v.push("<span "+l+" class='fancytree-expander fancytree-expander-fixed'></span>")):v.push("<span "+l+" class='fancytree-expander'></span>"),(o=s.evalOption("checkbox",h,h,f,!1))&&!h.isStatusNode()&&(l=m?" role='checkbox'":"",r="fancytree-checkbox",("radio"===o||h.parent&&h.parent.radiogroup)&&(r+=" fancytree-radio"),v.push("<span "+l+" class='"+r+"'></span>")),void 0!==h.data.iconClass&&(h.icon?e.error("'iconClass' node option is deprecated since v2.14.0: use 'icon' only instead"):(h.warn("'iconClass' node option is deprecated since v2.14.0: use 'icon' instead"),h.icon=h.data.iconClass)),!1!==(a=s.evalOption("icon",h,h,f,!0))&&(l=m?" role='presentation'":"",d=(d=s.evalOption("iconTooltip",h,h,f,null))?" title='"+R(d)+"'":"","string"==typeof a?i.test(a)?(a="/"===a.charAt(0)?a:(f.imagePath||"")+a,v.push("<img src='"+a+"' class='fancytree-icon'"+d+" alt='' />")):v.push("<span "+l+" class='fancytree-custom-icon "+a+"'"+d+"></span>"):a.text?v.push("<span "+l+" class='fancytree-custom-icon "+(a.addClass||"")+"'"+d+">"+s.escapeHtml(a.text)+"</span>"):a.html?v.push("<span "+l+" class='fancytree-custom-icon "+(a.addClass||"")+"'"+d+">"+a.html+"</span>"):v.push("<span "+l+" class='fancytree-icon'"+d+"></span>")),u="",f.renderTitle&&(u=f.renderTitle.call(p,{type:"renderTitle"},t)||""),u||(!0===(c=s.evalOption("tooltip",h,h,f,null))&&(c=h.title),u="<span class='fancytree-title'"+(c=c?" title='"+R(c)+"'":"")+(f.titlesTabbable?" tabindex='0'":"")+">"+(f.escapeTitles?s.escapeHtml(h.title):h.title)+"</span>"),v.push(u),h.span.innerHTML=v.join(""),this.nodeRenderStatus(t),f.enhanceTitle&&(t.$title=e(">span.fancytree-title",h.span),u=f.enhanceTitle.call(p,{type:"enhanceTitle"},t)||""))},nodeRenderStatus:function(t){var n,i=t.node,o=t.tree,r=t.options,a=i.hasChildren(),u=i.isLastSibling(),l=r.aria,c=r._classNames,d=[],h=i[o.statusClassPropName];h&&!1!==o._enableUpdate&&(l&&(n=e(i.tr||i.li)),d.push(c.node),o.activeNode===i&&d.push(c.active),o.focusNode===i&&d.push(c.focused),i.expanded&&d.push(c.expanded),l&&(!1===a?n.removeAttr("aria-expanded"):n.attr("aria-expanded",Boolean(i.expanded))),i.folder&&d.push(c.folder),!1!==a&&d.push(c.hasChildren),u&&d.push(c.lastsib),i.lazy&&null==i.children&&d.push(c.lazy),i.partload&&d.push(c.partload),i.partsel&&d.push(c.partsel),s.evalOption("unselectable",i,i,r,!1)&&d.push(c.unselectable),i._isLoading&&d.push(c.loading),i._error&&d.push(c.error),i.statusNodeType&&d.push(c.statusNodePrefix+i.statusNodeType),i.selected?(d.push(c.selected),l&&n.attr("aria-selected",!0)):l&&n.attr("aria-selected",!1),i.extraClasses&&d.push(i.extraClasses),!1===a?d.push(c.combinedExpanderPrefix+"n"+(u?"l":"")):d.push(c.combinedExpanderPrefix+(i.expanded?"e":"c")+(i.lazy&&null==i.children?"d":"")+(u?"l":"")),d.push(c.combinedIconPrefix+(i.expanded?"e":"c")+(i.folder?"f":"")),h.className=d.join(" "),i.li&&e(i.li).toggleClass(c.lastsib,u))},nodeSetActive:function(t,n,s){s=s||{};var i,o=t.node,r=t.tree,a=t.options,u=!0===s.noEvents,l=!0===s.noFocus,c=!1!==s.scrollIntoView;return o===r.activeNode==(n=!1!==n)?P(o):(c&&t.originalEvent&&e(t.originalEvent.target).is("a,:checkbox")&&(o.info("Not scrolling while clicking an embedded link."),c=!1),n&&!u&&!1===this._triggerNodeEvent("beforeActivate",o,t.originalEvent)?I(o,["rejected"]):(n?(r.activeNode&&(k(r.activeNode!==o,"node was active (inconsistency)"),i=e.extend({},t,{node:r.activeNode}),r.nodeSetActive(i,!1),k(null===r.activeNode,"deactivate was out of sync?")),a.activeVisible&&o.makeVisible({scrollIntoView:c}),r.activeNode=o,r.nodeRenderStatus(t),l||r.nodeSetFocus(t),u||r._triggerNodeEvent("activate",o,t.originalEvent)):(k(r.activeNode===o,"node was not active (inconsistency)"),r.activeNode=null,this.nodeRenderStatus(t),u||t.tree._triggerNodeEvent("deactivate",o,t.originalEvent)),P(o)))},nodeSetExpanded:function(t,n,s){s=s||{};var i,o,r,a,u,l,c=t.node,d=t.tree,h=t.options,p=!0===s.noAnimation,f=!0===s.noEvents;if(n=!1!==n,e(c.li).hasClass(h._classNames.animating))return c.warn("setExpanded("+n+") while animating: ignored."),I(c,["recursion"]);if(c.expanded&&n||!c.expanded&&!n)return P(c);if(n&&!c.lazy&&!c.hasChildren())return P(c);if(!n&&c.getLevel()<h.minExpandLevel)return I(c,["locked"]);if(!f&&!1===this._triggerNodeEvent("beforeExpand",c,t.originalEvent))return I(c,["rejected"]);if(p||c.isVisible()||(p=s.noAnimation=!0),o=new e.Deferred,n&&!c.expanded&&h.autoCollapse){u=c.getParentList(!1,!0),l=h.autoCollapse;try{for(h.autoCollapse=!1,r=0,a=u.length;r<a;r++)this._callHook("nodeCollapseSiblings",u[r],s)}finally{h.autoCollapse=l}}return o.done((function(){var e=c.getLastChild();n&&h.autoScroll&&!p&&e&&d._enableUpdate?e.scrollIntoView(!0,{topNode:c}).always((function(){f||t.tree._triggerNodeEvent(n?"expand":"collapse",t)})):f||t.tree._triggerNodeEvent(n?"expand":"collapse",t)})),i=function(s){var i=h._classNames,o=h.toggleEffect;if(c.expanded=n,d._callHook("treeStructureChanged",t,n?"expand":"collapse"),d._callHook("nodeRender",t,!1,!1,!0),c.ul)if("none"!==c.ul.style.display==!!c.expanded)c.warn("nodeSetExpanded: UL.style.display already set");else{if(o&&!p)return e(c.li).addClass(i.animating),void(x(e(c.ul)[o.effect])?e(c.ul)[o.effect]({duration:o.duration,always:function(){e(this).removeClass(i.animating),e(c.li).removeClass(i.animating),s()}}):(e(c.ul).stop(!0,!0),e(c.ul).parent().find(".ui-effects-placeholder").remove(),e(c.ul).toggle(o.effect,o.options,o.duration,(function(){e(this).removeClass(i.animating),e(c.li).removeClass(i.animating),s()}))));c.ul.style.display=c.expanded||!parent?"":"none"}s()},n&&c.lazy&&void 0===c.hasChildren()?c.load().done((function(){o.notifyWith&&o.notifyWith(c,["loaded"]),i((function(){o.resolveWith(c)}))})).fail((function(e){i((function(){o.rejectWith(c,["load failed ("+e+")"])}))})):i((function(){o.resolveWith(c)})),o.promise()},nodeSetFocus:function(t,n){var s,i=t.tree,o=t.node,r=i.options,a=!!t.originalEvent&&e(t.originalEvent.target).is(":input");if(n=!1!==n,i.focusNode){if(i.focusNode===o&&n)return;s=e.extend({},t,{node:i.focusNode}),i.focusNode=null,this._triggerNodeEvent("blur",s),this._callHook("nodeRenderStatus",s)}n&&(this.hasFocus()||(o.debug("nodeSetFocus: forcing container focus"),this._callHook("treeSetFocus",t,!0,{calledByNode:!0})),o.makeVisible({scrollIntoView:!1}),i.focusNode=o,r.titlesTabbable&&(a||e(o.span).find(".fancytree-title").focus()),r.aria&&e(i.$container).attr("aria-activedescendant",e(o.tr||o.li).uniqueId().attr("id")),this._triggerNodeEvent("focus",t),document.activeElement===i.$container.get(0)||e(document.activeElement,i.$container).length>=1||e(i.$container).focus(),r.autoScroll&&o.scrollIntoView(),this._callHook("nodeRenderStatus",t))},nodeSetSelected:function(e,t,n){n=n||{};var i=e.node,o=e.tree,r=e.options,a=!0===n.noEvents,u=i.parent;if(t=!1!==t,!s.evalOption("unselectable",i,i,r,!1)){if(i._lastSelectIntent=t,!!i.selected===t&&(3!==r.selectMode||!i.partsel||t))return t;if(!a&&!1===this._triggerNodeEvent("beforeSelect",i,e.originalEvent))return!!i.selected;t&&1===r.selectMode?(o.lastSelectedNode&&o.lastSelectedNode.setSelected(!1),i.selected=t):3!==r.selectMode||!u||u.radiogroup||i.radiogroup?u&&u.radiogroup?i.visitSiblings((function(e){e._changeSelectStatusAttrs(t&&e===i)}),!0):i.selected=t:(i.selected=t,i.fixSelection3AfterClick(n)),this.nodeRenderStatus(e),o.lastSelectedNode=t?i:null,a||o._triggerNodeEvent("select",e)}},nodeSetStatus:function(t,n,s,i){var o=t.node,r=t.tree;function a(){var e=o.children?o.children[0]:null;if(e&&e.isStatusNode()){try{o.ul&&(o.ul.removeChild(e.li),e.li=null)}catch(e){}1===o.children.length?o.children=[]:o.children.shift(),r._callHook("treeStructureChanged",t,"clearStatusNode")}}function u(n,s){var i=o.children?o.children[0]:null;return i&&i.isStatusNode()?(e.extend(i,n),i.statusNodeType=s,r._callHook("nodeRenderTitle",i)):(o._setChildren([n]),r._callHook("treeStructureChanged",t,"setStatusNode"),o.children[0].statusNodeType=s,r.render()),o.children[0]}switch(n){case"ok":a(),o._isLoading=!1,o._error=null,o.renderStatus();break;case"loading":o.parent||u({title:r.options.strings.loading+(s?" ("+s+")":""),checkbox:!1,tooltip:i},n),o._isLoading=!0,o._error=null,o.renderStatus();break;case"error":u({title:r.options.strings.loadError+(s?" ("+s+")":""),checkbox:!1,tooltip:i},n),o._isLoading=!1,o._error={message:s,details:i},o.renderStatus();break;case"nodata":u({title:s||r.options.strings.noData,checkbox:!1,tooltip:i},n),o._isLoading=!1,o._error=null,o.renderStatus();break;default:e.error("invalid node status "+n)}},nodeToggleExpanded:function(e){return this.nodeSetExpanded(e,!e.node.expanded)},nodeToggleSelected:function(e){var t=e.node,n=!t.selected;return t.partsel&&!t.selected&&!0===t._lastSelectIntent&&(n=!1,t.selected=!0),t._lastSelectIntent=n,this.nodeSetSelected(e,n)},treeClear:function(e){var t=e.tree;t.activeNode=null,t.focusNode=null,t.$div.find(">ul.fancytree-container").empty(),t.rootNode.children=null,t._callHook("treeStructureChanged",e,"clear")},treeCreate:function(e){},treeDestroy:function(e){this.$div.find(">ul.fancytree-container").remove(),this.$source&&this.$source.removeClass("fancytree-helper-hidden")},treeInit:function(t){var n=t.tree,s=n.options;n.$container.attr("tabindex",s.tabindex),e.each(g,(function(e,t){void 0!==s[t]&&(n.info("Move option "+t+" to tree"),n[t]=s[t],delete s[t])})),s.checkboxAutoHide&&n.$container.addClass("fancytree-checkbox-auto-hide"),s.rtl?n.$container.attr("DIR","RTL").addClass("fancytree-rtl"):n.$container.removeAttr("DIR").removeClass("fancytree-rtl"),s.aria&&(n.$container.attr("role","tree"),1!==s.selectMode&&n.$container.attr("aria-multiselectable",!0)),this.treeLoad(t)},treeLoad:function(t,n){var i,o,r,a=t.tree,u=t.widget.element,l=e.extend({},t,{node:this.rootNode});if(a.rootNode.children&&this.treeClear(t),n=n||this.options.source)"string"==typeof n&&e.error("Not implemented");else switch(o=u.data("type")||"html"){case"html":(r=u.find(">ul").not(".fancytree-container").first()).length?(r.addClass("ui-fancytree-source fancytree-helper-hidden"),n=e.ui.fancytree.parseHtml(r),this.data=e.extend(this.data,A(r))):(s.warn("No `source` option was passed and container does not contain `<ul>`: assuming `source: []`."),n=[]);break;case"json":n=e.parseJSON(u.text()),u.contents().filter((function(){return 3===this.nodeType})).remove(),e.isPlainObject(n)&&(k(b(n.children),"if an object is passed as source, it must contain a 'children' array (all other properties are added to 'tree.data')"),i=n,n=n.children,delete i.children,e.each(g,(function(e,t){void 0!==i[t]&&(a[t]=i[t],delete i[t])})),e.extend(a.data,i));break;default:e.error("Invalid data-type: "+o)}return a._triggerTreeEvent("preInit",null),this.nodeLoadChildren(l,n).done((function(){a._callHook("treeStructureChanged",t,"loadChildren"),a.render(),3===t.options.selectMode&&a.rootNode.fixSelection3FromEndNodes(),a.activeNode&&a.options.activeVisible&&a.activeNode.makeVisible(),a._triggerTreeEvent("init",null,{status:!0})})).fail((function(){a.render(),a._triggerTreeEvent("init",null,{status:!1})}))},treeRegisterNode:function(e,t,n){e.tree._callHook("treeStructureChanged",e,t?"addNode":"removeNode")},treeSetFocus:function(t,n,s){var i;(n=!1!==n)!==this.hasFocus()&&(this._hasFocus=n,!n&&this.focusNode?this.focusNode.setFocus(!1):!n||s&&s.calledByNode||e(this.$container).focus(),this.$container.toggleClass("fancytree-treefocus",n),this._triggerTreeEvent(n?"focusTree":"blurTree"),n&&!this.activeNode&&(i=this._lastMousedownNode||this.getFirstChild())&&i.setFocus())},treeSetOption:function(t,n,s){var i=t.tree,o=!0,r=!1,a=!1;switch(n){case"aria":case"checkbox":case"icon":case"minExpandLevel":case"tabindex":r=!0,a=!0;break;case"checkboxAutoHide":i.$container.toggleClass("fancytree-checkbox-auto-hide",!!s);break;case"escapeTitles":case"tooltip":a=!0;break;case"rtl":!1===s?i.$container.removeAttr("DIR").removeClass("fancytree-rtl"):i.$container.attr("DIR","RTL").addClass("fancytree-rtl"),a=!0;break;case"source":o=!1,i._callHook("treeLoad",i,s),a=!0}i.debug("set option "+n+"="+s+" <"+typeof s+">"),o&&(this.widget._super?this.widget._super.call(this.widget,n,s):e.Widget.prototype._setOption.call(this.widget,n,s)),r&&i._callHook("treeCreate",i),a&&i.render(!0,!1)},treeStructureChanged:function(e,t){}}),e.widget("ui.fancytree",{options:{activeVisible:!0,ajax:{type:"GET",cache:!1,dataType:"json"},aria:!0,autoActivate:!0,autoCollapse:!1,autoScroll:!1,checkbox:!1,clickFolderMode:4,copyFunctionsToData:!1,debugLevel:null,disabled:!1,enableAspx:42,escapeTitles:!1,extensions:[],focusOnSelect:!1,generateIds:!1,icon:!0,idPrefix:"ft_",keyboard:!0,keyPathSeparator:"/",minExpandLevel:1,nodata:!0,quicksearch:!1,rtl:!1,scrollOfs:{top:0,bottom:0},scrollParent:null,selectMode:2,strings:{loading:"Loading...",loadError:"Load error!",moreData:"More...",noData:"No data."},tabindex:"0",titlesTabbable:!1,toggleEffect:{effect:"slideToggle",duration:200},tooltip:!1,treeId:null,_classNames:{active:"fancytree-active",animating:"fancytree-animating",combinedExpanderPrefix:"fancytree-exp-",combinedIconPrefix:"fancytree-ico-",error:"fancytree-error",expanded:"fancytree-expanded",focused:"fancytree-focused",folder:"fancytree-folder",hasChildren:"fancytree-has-children",lastsib:"fancytree-lastsib",lazy:"fancytree-lazy",loading:"fancytree-loading",node:"fancytree-node",partload:"fancytree-partload",partsel:"fancytree-partsel",radio:"fancytree-radio",selected:"fancytree-selected",statusNodePrefix:"fancytree-statusnode-",unselectable:"fancytree-unselectable"},lazyLoad:null,postProcess:null},_deprecationWarning:function(e){var t=this.tree;t&&t.options.debugLevel>=3&&t.warn("$().fancytree('"+e+"') is deprecated (see https://wwwendt.de/tech/fancytree/doc/jsdoc/Fancytree_Widget.html")},_create:function(){this.tree=new F(this),this.$source=this.source||"json"===this.element.data("type")?this.element:this.element.find(">ul").first();var t,n,s,i=this.options,o=i.extensions,r=this.tree;for(s=0;s<o.length;s++)n=o[s],(t=e.ui.fancytree._extensions[n])||e.error("Could not apply extension '"+n+"' (it is not registered, did you forget to include it?)"),this.tree.options[n]=L({},t.options,this.tree.options[n]),k(void 0===this.tree.ext[n],"Extension name must not exist as Fancytree.ext attribute: '"+n+"'"),this.tree.ext[n]={},j(this.tree,r,t,n),r=t;void 0!==i.icons&&(!0===i.icon?(this.tree.warn("'icons' tree option is deprecated since v2.14.0: use 'icon' instead"),i.icon=i.icons):e.error("'icons' tree option is deprecated since v2.14.0: use 'icon' only instead")),void 0!==i.iconClass&&(i.icon?e.error("'iconClass' tree option is deprecated since v2.14.0: use 'icon' only instead"):(this.tree.warn("'iconClass' tree option is deprecated since v2.14.0: use 'icon' instead"),i.icon=i.iconClass)),void 0!==i.tabbable&&(i.tabindex=i.tabbable?"0":"-1",this.tree.warn("'tabbable' tree option is deprecated since v2.17.0: use 'tabindex='"+i.tabindex+"' instead")),this.tree._callHook("treeCreate",this.tree)},_init:function(){this.tree._callHook("treeInit",this.tree),this._bind()},_setOption:function(e,t){return this.tree._callHook("treeSetOption",this.tree,e,t)},_destroy:function(){this._unbind(),this.tree._callHook("treeDestroy",this.tree)},_unbind:function(){var t=this.tree._ns;this.element.off(t),this.tree.$container.off(t),e(document).off(t)},_bind:function(){var t=this,n=this.options,i=this.tree,o=i._ns;this._unbind(),i.$container.on("focusin"+o+" focusout"+o,(function(t){var n=s.getNode(t),o="focusin"===t.type;if(!o&&n&&e(t.target).is("a"))n.debug("Ignored focusout on embedded <a> element.");else{if(o){if(i._getExpiringValue("focusin"))return void i.debug("Ignored double focusin.");i._setExpiringValue("focusin",!0,50),n||(n=i._getExpiringValue("mouseDownNode"))&&i.debug("Reconstruct mouse target for focusin from recent event.")}n?i._callHook("nodeSetFocus",i._makeHookContext(n,t),o):i.tbody&&e(t.target).parents("table.fancytree-container > thead").length?i.debug("Ignore focus event outside table body.",t):i._callHook("treeSetFocus",i,o)}})).on("selectstart"+o,"span.fancytree-title",(function(e){e.preventDefault()})).on("keydown"+o,(function(e){if(n.disabled||!1===n.keyboard)return!0;var t,s=i.focusNode,o=i._makeHookContext(s||i,e),r=i.phase;try{return i.phase="userEvent","preventNav"===(t=s?i._triggerNodeEvent("keydown",s,e):i._triggerTreeEvent("keydown",e))?t=!0:!1!==t&&(t=i._callHook("nodeKeydown",o)),t}finally{i.phase=r}})).on("mousedown"+o,(function(e){var t=s.getEventTarget(e);i._lastMousedownNode=t?t.node:null,i._setExpiringValue("mouseDownNode",i._lastMousedownNode)})).on("click"+o+" dblclick"+o,(function(e){if(n.disabled)return!0;var i,o=s.getEventTarget(e),r=o.node,a=t.tree,u=a.phase;if(!r)return!0;i=a._makeHookContext(r,e);try{switch(a.phase="userEvent",e.type){case"click":return i.targetType=o.type,r.isPagingNode()?!0===a._triggerNodeEvent("clickPaging",i,e):!1!==a._triggerNodeEvent("click",i,e)&&a._callHook("nodeClick",i);case"dblclick":return i.targetType=o.type,!1!==a._triggerNodeEvent("dblclick",i,e)&&a._callHook("nodeDblclick",i)}}finally{a.phase=u}}))},getActiveNode:function(){return this._deprecationWarning("getActiveNode"),this.tree.activeNode},getNodeByKey:function(e){return this._deprecationWarning("getNodeByKey"),this.tree.getNodeByKey(e)},getRootNode:function(){return this._deprecationWarning("getRootNode"),this.tree.rootNode},getTree:function(){return this._deprecationWarning("getTree"),this.tree}}),s=e.ui.fancytree,e.extend(e.ui.fancytree,{version:"2.38.1-0",buildType:"production",debugLevel:3,_nextId:1,_nextNodeKey:1,_extensions:{},_FancytreeClass:F,_FancytreeNodeClass:q,jquerySupports:{positionMyOfs:B(e.ui.version,1,9)},assert:function(e,t){return k(e,t)},createTree:function(t,n){var i=e(t).fancytree(n);return s.getTree(i)},debounce:function(e,t,n,s){var i;return 3===arguments.length&&"boolean"!=typeof n&&(s=n,n=!1),function(){var o=arguments;s=s||this,n&&!i&&t.apply(s,o),clearTimeout(i),i=setTimeout((function(){n||t.apply(s,o),i=null}),e)}},debug:function(t){e.ui.fancytree.debugLevel>=4&&E("log",arguments)},error:function(t){e.ui.fancytree.debugLevel>=1&&E("error",arguments)},escapeHtml:function(e){return(""+e).replace(o,(function(e){return l[e]}))},fixPositionOptions:function(t){if((t.offset||(""+t.my+t.at).indexOf("%")>=0)&&e.error("expected new position syntax (but '%' is not supported)"),!e.ui.fancytree.jquerySupports.positionMyOfs){var n=/(\w+)([+-]?\d+)?\s+(\w+)([+-]?\d+)?/.exec(t.my),s=/(\w+)([+-]?\d+)?\s+(\w+)([+-]?\d+)?/.exec(t.at),i=(n[2]?+n[2]:0)+(s[2]?+s[2]:0),o=(n[4]?+n[4]:0)+(s[4]?+s[4]:0);t=e.extend({},t,{my:n[1]+" "+n[3],at:s[1]+" "+s[3]}),(i||o)&&(t.offset=i+" "+o)}return t},getEventTarget:function(t){var n,i=t&&t.target?t.target.className:"",o={node:this.getNode(t.target),type:void 0};return/\bfancytree-title\b/.test(i)?o.type="title":/\bfancytree-expander\b/.test(i)?o.type=!1===o.node.hasChildren()?"prefix":"expander":/\bfancytree-checkbox\b/.test(i)?o.type="checkbox":/\bfancytree(-custom)?-icon\b/.test(i)?o.type="icon":/\bfancytree-node\b/.test(i)?o.type="title":t&&t.target&&((n=e(t.target)).is("ul[role=group]")?((o.node&&o.node.tree||s).debug("Ignoring click on outer UL."),o.node=null):n.closest(".fancytree-title").length?o.type="title":n.closest(".fancytree-checkbox").length?o.type="checkbox":n.closest(".fancytree-expander").length&&(o.type="expander")),o},getEventTargetType:function(e){return this.getEventTarget(e).type},getNode:function(t){if(t instanceof q)return t;for(t instanceof e?t=t[0]:void 0!==t.originalEvent&&(t=t.target);t;){if(t.ftnode)return t.ftnode;t=t.parentNode}return null},getTree:function(t){var n,s=t;return t instanceof F?t:(void 0===t&&(t=0),"number"==typeof t?t=e(".fancytree-container").eq(t):"string"==typeof t?(t=e("#ft-id-"+s).eq(0)).length||(t=e(s).eq(0)):t instanceof Element||t instanceof HTMLDocument?t=e(t):t instanceof e?t=t.eq(0):void 0!==t.originalEvent&&(t=e(t.target)),(n=(t=t.closest(":ui-fancytree")).data("ui-fancytree")||t.data("fancytree"))?n.tree:null)},evalOption:function(e,t,n,s,i){var o,r,a=t.tree,u=s[e],l=n[e];return x(u)?(o={node:t,tree:a,widget:a.widget,options:a.widget.options,typeInfo:a.types[t.type]||{}},null==(r=u.call(a,{type:e},o))&&(r=l)):r=null==l?u:l,null==r&&(r=i),r},setSpanIcon:function(t,n,s){var i=e(t);"string"==typeof s?i.attr("class",n+" "+s):(s.text?i.text(""+s.text):s.html&&(t.innerHTML=s.html),i.attr("class",n+" "+(s.addClass||"")))},eventToString:function(e){var t=e.which,n=e.type,s=[];return e.altKey&&s.push("alt"),e.ctrlKey&&s.push("ctrl"),e.metaKey&&s.push("meta"),e.shiftKey&&s.push("shift"),"click"===n||"dblclick"===n?s.push(p[e.button]+n):"wheel"===n?s.push(n):c[t]||s.push(d[t]||String.fromCharCode(t).toLowerCase()),s.join("+")},info:function(t){e.ui.fancytree.debugLevel>=3&&E("info",arguments)},keyEventToString:function(e){return this.warn("keyEventToString() is deprecated: use eventToString()"),this.eventToString(e)},overrideMethod:function(t,n,s,i){var o,r=t[n]||e.noop;t[n]=function(){var e=i||this;try{return o=e._super,e._super=r,s.apply(e,arguments)}finally{e._super=o}}},parseHtml:function(t){var n,s,i,o,r,a,u,l,c=t.find(">li"),d=[];return c.each((function(){var c,h,p=e(this),g=p.find(">span",this).first(),y=g.length?null:p.find(">a").first(),_={tooltip:null,data:{}};for(g.length?_.title=g.html():y&&y.length?(_.title=y.html(),_.data.href=y.attr("href"),_.data.target=y.attr("target"),_.tooltip=y.attr("title")):(_.title=p.html(),(r=_.title.search(/<ul/i))>=0&&(_.title=_.title.substring(0,r))),_.title=C(_.title),o=0,a=f.length;o<a;o++)_[f[o]]=void 0;for(n=this.className.split(" "),i=[],o=0,a=n.length;o<a;o++)s=n[o],m[s]?_[s]=!0:i.push(s);if(_.extraClasses=i.join(" "),(u=p.attr("title"))&&(_.tooltip=u),(u=p.attr("id"))&&(_.key=u),p.attr("hideCheckbox")&&(_.checkbox=!1),(c=A(p))&&!e.isEmptyObject(c)){for(h in w)S(c,h)&&(c[w[h]]=c[h],delete c[h]);for(o=0,a=v.length;o<a;o++)u=v[o],null!=(l=c[u])&&(delete c[u],_[u]=l);e.extend(_.data,c)}(t=p.find(">ul").first()).length?_.children=e.ui.fancytree.parseHtml(t):_.children=_.lazy?void 0:null,d.push(_)})),d},registerExtension:function(t){k(null!=t.name,"extensions must have a `name` property."),k(null!=t.version,"extensions must have a `version` property."),e.ui.fancytree._extensions[t.name]=t},trim:C,unescapeHtml:function(e){var t=document.createElement("div");return t.innerHTML=e,0===t.childNodes.length?"":t.childNodes[0].nodeValue},warn:function(t){e.ui.fancytree.debugLevel>=2&&E("warn",arguments)}}),e.ui.fancytree}function k(t,n){t||(n="Fancytree assertion failed"+(n=n?": "+n:""),e.ui.fancytree.error(n),e.error(n))}function S(e,t){return Object.prototype.hasOwnProperty.call(e,t)}function x(e){return"function"==typeof e}function C(e){return null==e?"":e.trim()}function E(e,t){var n,s,i=window.console?window.console[e]:null;if(i)try{i.apply(window.console,t)}catch(e){for(s="",n=0;n<t.length;n++)s+=t[n];i(s)}}function N(e){return!(!e.tree||void 0===e.statusNodeType)}function B(t,n,s,i){var o,r,a,u=e.map(C(t).split("."),(function(e){return parseInt(e,10)})),l=e.map(Array.prototype.slice.call(arguments,1),(function(e){return parseInt(e,10)}));for(o=0;o<l.length;o++)if((r=u[o]||0)!==(a=l[o]||0))return r>a;return!0}function L(){var t,n,s,i,o,r=arguments[0]||{},a=1,u=arguments.length;if("object"==typeof r||x(r)||(r={}),a===u)throw Error("need at least two args");for(;a<u;a++)if(null!=(t=arguments[a]))for(n in t)if(S(t,n)){if(s=r[n],r===(i=t[n]))continue;i&&e.isPlainObject(i)?(o=s&&e.isPlainObject(s)?s:{},r[n]=L(o,i)):void 0!==i&&(r[n]=i)}return r}function T(e,t,n,s,i){var o,r,a,u,l,c=(o=t[e],r=s[e],a=t.ext[i],u=function(){return o.apply(t,arguments)},l=function(e){return o.apply(t,e)},function(){var e=t._local,n=t._super,s=t._superApply;try{return t._local=a,t._super=u,t._superApply=l,r.apply(t,arguments)}finally{t._local=e,t._super=n,t._superApply=s}});return c}function j(t,n,s,i){for(var o in s)"function"==typeof s[o]?"function"==typeof t[o]?t[o]=T(o,t,n,s,i):"_"===o.charAt(0)?t.ext[i][o]=T(o,t,n,s,i):e.error("Could not override tree."+o+". Use prefix '_' to create tree."+i+"._"+o):"options"!==o&&(t.ext[i][o]=s[o])}function P(t,n){return void 0===t?e.Deferred((function(){this.resolve()})).promise():e.Deferred((function(){this.resolveWith(t,n)})).promise()}function I(t,n){return void 0===t?e.Deferred((function(){this.reject()})).promise():e.Deferred((function(){this.rejectWith(t,n)})).promise()}function O(e,t){return function(){e.resolveWith(t)}}function A(t){var n=e.extend({},t.data()),s=n.json;return delete n.fancytree,delete n.uiFancytree,s&&(delete n.json,n=e.extend(n,s)),n}function R(e){return(""+e).replace(r,(function(e){return l[e]}))}function M(e){return e=e.toLowerCase(),function(t){return t.title.toLowerCase().indexOf(e)>=0}}function D(e){var t=new RegExp("^"+e,"i");return function(e){return t.test(e.title)}}function q(t,n){var i,o,r,a;for(this.parent=t,this.tree=t.tree,this.ul=null,this.li=null,this.statusNodeType=null,this._isLoading=!1,this._error=null,this.data={},i=0,o=v.length;i<o;i++)this[r=v[i]]=n[r];for(r in null==this.unselectableIgnore&&null==this.unselectableStatus||(this.unselectable=!0),n.hideCheckbox&&e.error("'hideCheckbox' node option was removed in v2.23.0: use 'checkbox: false'"),n.data&&e.extend(this.data,n.data),n)y[r]||!this.tree.options.copyFunctionsToData&&x(n[r])||_[r]||(this.data[r]=n[r]);null==this.key?this.tree.options.defaultKey?(this.key=""+this.tree.options.defaultKey(this),k(this.key,"defaultKey() must return a unique key")):this.key="_"+s._nextNodeKey++:this.key=""+this.key,n.active&&(k(null===this.tree.activeNode,"only one active node allowed"),this.tree.activeNode=this),n.selected&&(this.tree.lastSelectedNode=this),(a=n.children)?a.length?this._setChildren(a):this.children=this.lazy?[]:null:this.children=null,this.tree._callHook("treeRegisterNode",this.tree,!0,this)}function F(t){this.widget=t,this.$div=t.element,this.options=t.options,this.options&&(void 0!==this.options.lazyload&&e.error("The 'lazyload' event is deprecated since 2014-02-25. Use 'lazyLoad' (with uppercase L) instead."),void 0!==this.options.loaderror&&e.error("The 'loaderror' event was renamed since 2014-07-03. Use 'loadError' (with uppercase E) instead."),void 0!==this.options.fx&&e.error("The 'fx' option was replaced by 'toggleEffect' since 2014-11-30."),void 0!==this.options.removeNode&&e.error("The 'removeNode' event was replaced by 'modifyChild' since 2.20 (2016-09-10).")),this.ext={},this.types={},this.columns={},this.data=A(this.$div),this._id=""+(this.options.treeId||e.ui.fancytree._nextId++),this._ns=".fancytree-"+this._id,this.activeNode=null,this.focusNode=null,this._hasFocus=null,this._tempCache={},this._lastMousedownNode=null,this._enableUpdate=!0,this.lastSelectedNode=null,this.systemFocusElement=null,this.lastQuicksearchTerm="",this.lastQuicksearchTime=0,this.viewport=null,this.statusClassPropName="span",this.ariaPropName="li",this.nodeContainerAttrName="li",this.$div.find(">ul.fancytree-container").remove();var n,i={tree:this};this.rootNode=new q(i,{title:"root",key:"root_"+this._id,children:null,expanded:!0}),this.rootNode.parent=null,n=e("<ul>",{id:"ft-id-"+this._id,class:"ui-fancytree fancytree-container fancytree-plain"}).appendTo(this.$div),this.$container=n,this.rootNode.ul=n[0],null==this.options.debugLevel&&(this.options.debugLevel=s.debugLevel)}e.ui.fancytree.warn("Fancytree: ignored duplicate include")},void 0===(o="function"==typeof s?s.apply(t,i):s)||(e.exports=o)},1503:function(e,t,n){var s,i,o;i=[n(1669)],s=function(e){e.ui=e.ui||{},e.ui.version="1.12.1";var t,n,s=0,i=Array.prototype.slice;e.cleanData=e.cleanData||(t=e.cleanData,function(n){var s,i,o;for(o=0;null!=(i=n[o]);o++)try{(s=e._data(i,"events"))&&s.remove&&e(i).triggerHandler("remove")}catch(e){}t(n)}),e.widget=e.widget||function(t,n,s){var i,o,r,a={},u=t.split(".")[0],l=u+"-"+(t=t.split(".")[1]);return s||(s=n,n=e.Widget),e.isArray(s)&&(s=e.extend.apply(null,[{}].concat(s))),e.expr[":"][l.toLowerCase()]=function(t){return!!e.data(t,l)},e[u]=e[u]||{},i=e[u][t],o=e[u][t]=function(e,t){if(!this._createWidget)return new o(e,t);arguments.length&&this._createWidget(e,t)},e.extend(o,i,{version:s.version,_proto:e.extend({},s),_childConstructors:[]}),(r=new n).options=e.widget.extend({},r.options),e.each(s,(function(t,s){e.isFunction(s)?a[t]=function(){function e(){return n.prototype[t].apply(this,arguments)}function i(e){return n.prototype[t].apply(this,e)}return function(){var t,n=this._super,o=this._superApply;return this._super=e,this._superApply=i,t=s.apply(this,arguments),this._super=n,this._superApply=o,t}}():a[t]=s})),o.prototype=e.widget.extend(r,{widgetEventPrefix:i&&r.widgetEventPrefix||t},a,{constructor:o,namespace:u,widgetName:t,widgetFullName:l}),i?(e.each(i._childConstructors,(function(t,n){var s=n.prototype;e.widget(s.namespace+"."+s.widgetName,o,n._proto)})),delete i._childConstructors):n._childConstructors.push(o),e.widget.bridge(t,o),o},e.widget.extend=function(t){for(var n,s,o=i.call(arguments,1),r=0,a=o.length;r<a;r++)for(n in o[r])s=o[r][n],o[r].hasOwnProperty(n)&&void 0!==s&&(e.isPlainObject(s)?t[n]=e.isPlainObject(t[n])?e.widget.extend({},t[n],s):e.widget.extend({},s):t[n]=s);return t},e.widget.bridge=function(t,n){var s=n.prototype.widgetFullName||t;e.fn[t]=function(o){var r="string"==typeof o,a=i.call(arguments,1),u=this;return r?this.length||"instance"!==o?this.each((function(){var n,i=e.data(this,s);return"instance"===o?(u=i,!1):i?e.isFunction(i[o])&&"_"!==o.charAt(0)?(n=i[o].apply(i,a))!==i&&void 0!==n?(u=n&&n.jquery?u.pushStack(n.get()):n,!1):void 0:e.error("no such method '"+o+"' for "+t+" widget instance"):e.error("cannot call methods on "+t+" prior to initialization; attempted to call method '"+o+"'")})):u=void 0:(a.length&&(o=e.widget.extend.apply(null,[o].concat(a))),this.each((function(){var t=e.data(this,s);t?(t.option(o||{}),t._init&&t._init()):e.data(this,s,new n(o,this))}))),u}},e.Widget=e.Widget||function(){},e.Widget._childConstructors=[],e.Widget.prototype={widgetName:"widget",widgetEventPrefix:"",defaultElement:"<div>",options:{classes:{},disabled:!1,create:null},_createWidget:function(t,n){n=e(n||this.defaultElement||this)[0],this.element=e(n),this.uuid=s++,this.eventNamespace="."+this.widgetName+this.uuid,this.bindings=e(),this.hoverable=e(),this.focusable=e(),this.classesElementLookup={},n!==this&&(e.data(n,this.widgetFullName,this),this._on(!0,this.element,{remove:function(e){e.target===n&&this.destroy()}}),this.document=e(n.style?n.ownerDocument:n.document||n),this.window=e(this.document[0].defaultView||this.document[0].parentWindow)),this.options=e.widget.extend({},this.options,this._getCreateOptions(),t),this._create(),this.options.disabled&&this._setOptionDisabled(this.options.disabled),this._trigger("create",null,this._getCreateEventData()),this._init()},_getCreateOptions:function(){return{}},_getCreateEventData:e.noop,_create:e.noop,_init:e.noop,destroy:function(){var t=this;this._destroy(),e.each(this.classesElementLookup,(function(e,n){t._removeClass(n,e)})),this.element.off(this.eventNamespace).removeData(this.widgetFullName),this.widget().off(this.eventNamespace).removeAttr("aria-disabled"),this.bindings.off(this.eventNamespace)},_destroy:e.noop,widget:function(){return this.element},option:function(t,n){var s,i,o,r=t;if(0===arguments.length)return e.widget.extend({},this.options);if("string"==typeof t)if(r={},s=t.split("."),t=s.shift(),s.length){for(i=r[t]=e.widget.extend({},this.options[t]),o=0;o<s.length-1;o++)i[s[o]]=i[s[o]]||{},i=i[s[o]];if(t=s.pop(),1===arguments.length)return void 0===i[t]?null:i[t];i[t]=n}else{if(1===arguments.length)return void 0===this.options[t]?null:this.options[t];r[t]=n}return this._setOptions(r),this},_setOptions:function(e){var t;for(t in e)this._setOption(t,e[t]);return this},_setOption:function(e,t){return"classes"===e&&this._setOptionClasses(t),this.options[e]=t,"disabled"===e&&this._setOptionDisabled(t),this},_setOptionClasses:function(t){var n,s,i;for(n in t)i=this.classesElementLookup[n],t[n]!==this.options.classes[n]&&i&&i.length&&(s=e(i.get()),this._removeClass(i,n),s.addClass(this._classes({element:s,keys:n,classes:t,add:!0})))},_setOptionDisabled:function(e){this._toggleClass(this.widget(),this.widgetFullName+"-disabled",null,!!e),e&&(this._removeClass(this.hoverable,null,"ui-state-hover"),this._removeClass(this.focusable,null,"ui-state-focus"))},enable:function(){return this._setOptions({disabled:!1})},disable:function(){return this._setOptions({disabled:!0})},_classes:function(t){var n=[],s=this;function i(i,o){var r,a;for(a=0;a<i.length;a++)r=s.classesElementLookup[i[a]]||e(),r=t.add?e(e.unique(r.get().concat(t.element.get()))):e(r.not(t.element).get()),s.classesElementLookup[i[a]]=r,n.push(i[a]),o&&t.classes[i[a]]&&n.push(t.classes[i[a]])}return t=e.extend({element:this.element,classes:this.options.classes||{}},t),this._on(t.element,{remove:"_untrackClassesElement"}),t.keys&&i(t.keys.match(/\S+/g)||[],!0),t.extra&&i(t.extra.match(/\S+/g)||[]),n.join(" ")},_untrackClassesElement:function(t){var n=this;e.each(n.classesElementLookup,(function(s,i){-1!==e.inArray(t.target,i)&&(n.classesElementLookup[s]=e(i.not(t.target).get()))}))},_removeClass:function(e,t,n){return this._toggleClass(e,t,n,!1)},_addClass:function(e,t,n){return this._toggleClass(e,t,n,!0)},_toggleClass:function(e,t,n,s){s="boolean"==typeof s?s:n;var i="string"==typeof e||null===e,o={extra:i?t:n,keys:i?e:t,element:i?this.element:e,add:s};return o.element.toggleClass(this._classes(o),s),this},_on:function(t,n,s){var i,o=this;"boolean"!=typeof t&&(s=n,n=t,t=!1),s?(n=i=e(n),this.bindings=this.bindings.add(n)):(s=n,n=this.element,i=this.widget()),e.each(s,(function(s,r){function a(){if(t||!0!==o.options.disabled&&!e(this).hasClass("ui-state-disabled"))return("string"==typeof r?o[r]:r).apply(o,arguments)}"string"!=typeof r&&(a.guid=r.guid=r.guid||a.guid||e.guid++);var u=s.match(/^([\w:-]*)\s*(.*)$/),l=u[1]+o.eventNamespace,c=u[2];c?i.on(l,c,a):n.on(l,a)}))},_off:function(t,n){n=(n||"").split(" ").join(this.eventNamespace+" ")+this.eventNamespace,t.off(n).off(n),this.bindings=e(this.bindings.not(t).get()),this.focusable=e(this.focusable.not(t).get()),this.hoverable=e(this.hoverable.not(t).get())},_delay:function(e,t){function n(){return("string"==typeof e?s[e]:e).apply(s,arguments)}var s=this;return setTimeout(n,t||0)},_hoverable:function(t){this.hoverable=this.hoverable.add(t),this._on(t,{mouseenter:function(t){this._addClass(e(t.currentTarget),null,"ui-state-hover")},mouseleave:function(t){this._removeClass(e(t.currentTarget),null,"ui-state-hover")}})},_focusable:function(t){this.focusable=this.focusable.add(t),this._on(t,{focusin:function(t){this._addClass(e(t.currentTarget),null,"ui-state-focus")},focusout:function(t){this._removeClass(e(t.currentTarget),null,"ui-state-focus")}})},_trigger:function(t,n,s){var i,o,r=this.options[t];if(s=s||{},(n=e.Event(n)).type=(t===this.widgetEventPrefix?t:this.widgetEventPrefix+t).toLowerCase(),n.target=this.element[0],o=n.originalEvent)for(i in o)i in n||(n[i]=o[i]);return this.element.trigger(n,s),!(e.isFunction(r)&&!1===r.apply(this.element[0],[n].concat(s))||n.isDefaultPrevented())}},e.each({show:"fadeIn",hide:"fadeOut"},(function(t,n){e.Widget.prototype["_"+t]=function(s,i,o){var r;"string"==typeof i&&(i={effect:i});var a=i?!0===i||"number"==typeof i?n:i.effect||n:t;"number"==typeof(i=i||{})&&(i={duration:i}),r=!e.isEmptyObject(i),i.complete=o,i.delay&&s.delay(i.delay),r&&e.effects&&e.effects.effect[a]?s[t](i):a!==t&&s[a]?s[a](i.duration,i.easing,o):s.queue((function(n){e(this)[t](),o&&o.call(s[0]),n()}))}})),e.widget,function(){var t,n=Math.max,s=Math.abs,i=/left|center|right/,o=/top|center|bottom/,r=/[\+\-]\d+(\.[\d]+)?%?/,a=/^\w+/,u=/%$/,l=e.fn.position;function c(e,t,n){return[parseFloat(e[0])*(u.test(e[0])?t/100:1),parseFloat(e[1])*(u.test(e[1])?n/100:1)]}function d(t,n){return parseInt(e.css(t,n),10)||0}function h(t){var n=t[0];return 9===n.nodeType?{width:t.width(),height:t.height(),offset:{top:0,left:0}}:e.isWindow(n)?{width:t.width(),height:t.height(),offset:{top:t.scrollTop(),left:t.scrollLeft()}}:n.preventDefault?{width:0,height:0,offset:{top:n.pageY,left:n.pageX}}:{width:t.outerWidth(),height:t.outerHeight(),offset:t.offset()}}e.position=e.position||{scrollbarWidth:function(){if(void 0!==t)return t;var n,s,i=e("<div style='display:block;position:absolute;width:50px;height:50px;overflow:hidden;'><div style='height:100px;width:auto;'></div></div>"),o=i.children()[0];return e("body").append(i),n=o.offsetWidth,i.css("overflow","scroll"),n===(s=o.offsetWidth)&&(s=i[0].clientWidth),i.remove(),t=n-s},getScrollInfo:function(t){var n=t.isWindow||t.isDocument?"":t.element.css("overflow-x"),s=t.isWindow||t.isDocument?"":t.element.css("overflow-y"),i="scroll"===n||"auto"===n&&t.width<t.element[0].scrollWidth;return{width:"scroll"===s||"auto"===s&&t.height<t.element[0].scrollHeight?e.position.scrollbarWidth():0,height:i?e.position.scrollbarWidth():0}},getWithinInfo:function(t){var n=e(t||window),s=e.isWindow(n[0]),i=!!n[0]&&9===n[0].nodeType;return{element:n,isWindow:s,isDocument:i,offset:s||i?{left:0,top:0}:e(t).offset(),scrollLeft:n.scrollLeft(),scrollTop:n.scrollTop(),width:n.outerWidth(),height:n.outerHeight()}}},e.fn.position=function(t){if(!t||!t.of)return l.apply(this,arguments);t=e.extend({},t);var u,p,f,m,g,v,y=e(t.of),w=e.position.getWithinInfo(t.within),_=e.position.getScrollInfo(w),b=(t.collision||"flip").split(" "),k={};return v=h(y),y[0].preventDefault&&(t.at="left top"),p=v.width,f=v.height,m=v.offset,g=e.extend({},m),e.each(["my","at"],(function(){var e,n,s=(t[this]||"").split(" ");1===s.length&&(s=i.test(s[0])?s.concat(["center"]):o.test(s[0])?["center"].concat(s):["center","center"]),s[0]=i.test(s[0])?s[0]:"center",s[1]=o.test(s[1])?s[1]:"center",e=r.exec(s[0]),n=r.exec(s[1]),k[this]=[e?e[0]:0,n?n[0]:0],t[this]=[a.exec(s[0])[0],a.exec(s[1])[0]]})),1===b.length&&(b[1]=b[0]),"right"===t.at[0]?g.left+=p:"center"===t.at[0]&&(g.left+=p/2),"bottom"===t.at[1]?g.top+=f:"center"===t.at[1]&&(g.top+=f/2),u=c(k.at,p,f),g.left+=u[0],g.top+=u[1],this.each((function(){var i,o,r=e(this),a=r.outerWidth(),l=r.outerHeight(),h=d(this,"marginLeft"),v=d(this,"marginTop"),S=a+h+d(this,"marginRight")+_.width,x=l+v+d(this,"marginBottom")+_.height,C=e.extend({},g),E=c(k.my,r.outerWidth(),r.outerHeight());"right"===t.my[0]?C.left-=a:"center"===t.my[0]&&(C.left-=a/2),"bottom"===t.my[1]?C.top-=l:"center"===t.my[1]&&(C.top-=l/2),C.left+=E[0],C.top+=E[1],i={marginLeft:h,marginTop:v},e.each(["left","top"],(function(n,s){e.ui.position[b[n]]&&e.ui.position[b[n]][s](C,{targetWidth:p,targetHeight:f,elemWidth:a,elemHeight:l,collisionPosition:i,collisionWidth:S,collisionHeight:x,offset:[u[0]+E[0],u[1]+E[1]],my:t.my,at:t.at,within:w,elem:r})})),t.using&&(o=function(e){var i=m.left-C.left,o=i+p-a,u=m.top-C.top,c=u+f-l,d={target:{element:y,left:m.left,top:m.top,width:p,height:f},element:{element:r,left:C.left,top:C.top,width:a,height:l},horizontal:o<0?"left":i>0?"right":"center",vertical:c<0?"top":u>0?"bottom":"middle"};p<a&&s(i+o)<p&&(d.horizontal="center"),f<l&&s(u+c)<f&&(d.vertical="middle"),n(s(i),s(o))>n(s(u),s(c))?d.important="horizontal":d.important="vertical",t.using.call(this,e,d)}),r.offset(e.extend(C,{using:o}))}))},e.ui.position={fit:{left:function(e,t){var s,i=t.within,o=i.isWindow?i.scrollLeft:i.offset.left,r=i.width,a=e.left-t.collisionPosition.marginLeft,u=o-a,l=a+t.collisionWidth-r-o;t.collisionWidth>r?u>0&&l<=0?(s=e.left+u+t.collisionWidth-r-o,e.left+=u-s):e.left=l>0&&u<=0?o:u>l?o+r-t.collisionWidth:o:u>0?e.left+=u:l>0?e.left-=l:e.left=n(e.left-a,e.left)},top:function(e,t){var s,i=t.within,o=i.isWindow?i.scrollTop:i.offset.top,r=t.within.height,a=e.top-t.collisionPosition.marginTop,u=o-a,l=a+t.collisionHeight-r-o;t.collisionHeight>r?u>0&&l<=0?(s=e.top+u+t.collisionHeight-r-o,e.top+=u-s):e.top=l>0&&u<=0?o:u>l?o+r-t.collisionHeight:o:u>0?e.top+=u:l>0?e.top-=l:e.top=n(e.top-a,e.top)}},flip:{left:function(e,t){var n,i,o=t.within,r=o.offset.left+o.scrollLeft,a=o.width,u=o.isWindow?o.scrollLeft:o.offset.left,l=e.left-t.collisionPosition.marginLeft,c=l-u,d=l+t.collisionWidth-a-u,h="left"===t.my[0]?-t.elemWidth:"right"===t.my[0]?t.elemWidth:0,p="left"===t.at[0]?t.targetWidth:"right"===t.at[0]?-t.targetWidth:0,f=-2*t.offset[0];c<0?((n=e.left+h+p+f+t.collisionWidth-a-r)<0||n<s(c))&&(e.left+=h+p+f):d>0&&((i=e.left-t.collisionPosition.marginLeft+h+p+f-u)>0||s(i)<d)&&(e.left+=h+p+f)},top:function(e,t){var n,i,o=t.within,r=o.offset.top+o.scrollTop,a=o.height,u=o.isWindow?o.scrollTop:o.offset.top,l=e.top-t.collisionPosition.marginTop,c=l-u,d=l+t.collisionHeight-a-u,h="top"===t.my[1]?-t.elemHeight:"bottom"===t.my[1]?t.elemHeight:0,p="top"===t.at[1]?t.targetHeight:"bottom"===t.at[1]?-t.targetHeight:0,f=-2*t.offset[1];c<0?((i=e.top+h+p+f+t.collisionHeight-a-r)<0||i<s(c))&&(e.top+=h+p+f):d>0&&((n=e.top-t.collisionPosition.marginTop+h+p+f-u)>0||s(n)<d)&&(e.top+=h+p+f)}},flipfit:{left:function(){e.ui.position.flip.left.apply(this,arguments),e.ui.position.fit.left.apply(this,arguments)},top:function(){e.ui.position.flip.top.apply(this,arguments),e.ui.position.fit.top.apply(this,arguments)}}}}(),e.ui.position,e.ui.keyCode={BACKSPACE:8,COMMA:188,DELETE:46,DOWN:40,END:35,ENTER:13,ESCAPE:27,HOME:36,LEFT:37,PAGE_DOWN:34,PAGE_UP:33,PERIOD:190,RIGHT:39,SPACE:32,TAB:9,UP:38},e.fn.scrollParent=function(t){var n=this.css("position"),s="absolute"===n,i=t?/(auto|scroll|hidden)/:/(auto|scroll)/,o=this.parents().filter((function(){var t=e(this);return(!s||"static"!==t.css("position"))&&i.test(t.css("overflow")+t.css("overflow-y")+t.css("overflow-x"))})).eq(0);return"fixed"!==n&&o.length?o:e(this[0].ownerDocument||document)},e.fn.extend({uniqueId:(n=0,function(){return this.each((function(){this.id||(this.id="ui-id-"+ ++n)}))}),removeUniqueId:function(){return this.each((function(){/^ui-id-\d+$/.test(this.id)&&e(this).removeAttr("id")}))}})},void 0===(o="function"==typeof s?s.apply(t,i):s)||(e.exports=o)},1873:function(e,t,n){var s=n(9325).Symbol;e.exports=s},1033:function(e){e.exports=function(e,t,n){switch(n.length){case 0:return e.call(t);case 1:return e.call(t,n[0]);case 2:return e.call(t,n[0],n[1]);case 3:return e.call(t,n[0],n[1],n[2])}return e.apply(t,n)}},695:function(e,t,n){var s=n(8096),i=n(2428),o=n(6449),r=n(3656),a=n(361),u=n(7167),l=Object.prototype.hasOwnProperty;e.exports=function(e,t){var n=o(e),c=!n&&i(e),d=!n&&!c&&r(e),h=!n&&!c&&!d&&u(e),p=n||c||d||h,f=p?s(e.length,String):[],m=f.length;for(var g in e)!t&&!l.call(e,g)||p&&("length"==g||d&&("offset"==g||"parent"==g)||h&&("buffer"==g||"byteLength"==g||"byteOffset"==g)||a(g,m))||f.push(g);return f}},6547:function(e,t,n){var s=n(3360),i=n(5288),o=Object.prototype.hasOwnProperty;e.exports=function(e,t,n){var r=e[t];o.call(e,t)&&i(r,n)&&(void 0!==n||t in e)||s(e,t,n)}},3360:function(e,t,n){var s=n(3243);e.exports=function(e,t,n){"__proto__"==t&&s?s(e,t,{configurable:!0,enumerable:!0,value:n,writable:!0}):e[t]=n}},2552:function(e,t,n){var s=n(1873),i=n(659),o=n(9350),r=s?s.toStringTag:void 0;e.exports=function(e){return null==e?void 0===e?"[object Undefined]":"[object Null]":r&&r in Object(e)?i(e):o(e)}},7534:function(e,t,n){var s=n(2552),i=n(346);e.exports=function(e){return i(e)&&"[object Arguments]"==s(e)}},5083:function(e,t,n){var s=n(1882),i=n(7296),o=n(3805),r=n(7473),a=/^\[object .+?Constructor\]$/,u=Function.prototype,l=Object.prototype,c=u.toString,d=l.hasOwnProperty,h=RegExp("^"+c.call(d).replace(/[\\^$.*+?()[\]{}|]/g,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$");e.exports=function(e){return!(!o(e)||i(e))&&(s(e)?h:a).test(r(e))}},4901:function(e,t,n){var s=n(2552),i=n(294),o=n(346),r={};r["[object Float32Array]"]=r["[object Float64Array]"]=r["[object Int8Array]"]=r["[object Int16Array]"]=r["[object Int32Array]"]=r["[object Uint8Array]"]=r["[object Uint8ClampedArray]"]=r["[object Uint16Array]"]=r["[object Uint32Array]"]=!0,r["[object Arguments]"]=r["[object Array]"]=r["[object ArrayBuffer]"]=r["[object Boolean]"]=r["[object DataView]"]=r["[object Date]"]=r["[object Error]"]=r["[object Function]"]=r["[object Map]"]=r["[object Number]"]=r["[object Object]"]=r["[object RegExp]"]=r["[object Set]"]=r["[object String]"]=r["[object WeakMap]"]=!1,e.exports=function(e){return o(e)&&i(e.length)&&!!r[s(e)]}},8984:function(e,t,n){var s=n(5527),i=n(3650),o=Object.prototype.hasOwnProperty;e.exports=function(e){if(!s(e))return i(e);var t=[];for(var n in Object(e))o.call(e,n)&&"constructor"!=n&&t.push(n);return t}},9302:function(e,t,n){var s=n(3488),i=n(6757),o=n(2865);e.exports=function(e,t){return o(i(e,t,s),e+"")}},9570:function(e,t,n){var s=n(7334),i=n(3243),o=n(3488),r=i?function(e,t){return i(e,"toString",{configurable:!0,enumerable:!1,value:s(t),writable:!0})}:o;e.exports=r},8096:function(e){e.exports=function(e,t){for(var n=-1,s=Array(e);++n<e;)s[n]=t(n);return s}},7301:function(e){e.exports=function(e){return function(t){return e(t)}}},1791:function(e,t,n){var s=n(6547),i=n(3360);e.exports=function(e,t,n,o){var r=!n;n||(n={});for(var a=-1,u=t.length;++a<u;){var l=t[a],c=o?o(n[l],e[l],l,n,e):void 0;void 0===c&&(c=e[l]),r?i(n,l,c):s(n,l,c)}return n}},5481:function(e,t,n){var s=n(9325)["__core-js_shared__"];e.exports=s},999:function(e,t,n){var s=n(9302),i=n(6800);e.exports=function(e){return s((function(t,n){var s=-1,o=n.length,r=o>1?n[o-1]:void 0,a=o>2?n[2]:void 0;for(r=e.length>3&&"function"==typeof r?(o--,r):void 0,a&&i(n[0],n[1],a)&&(r=o<3?void 0:r,o=1),t=Object(t);++s<o;){var u=n[s];u&&e(t,u,s,r)}return t}))}},3243:function(e,t,n){var s=n(6110),i=function(){try{var e=s(Object,"defineProperty");return e({},"",{}),e}catch(e){}}();e.exports=i},4840:function(e,t,n){var s="object"==typeof n.g&&n.g&&n.g.Object===Object&&n.g;e.exports=s},6110:function(e,t,n){var s=n(5083),i=n(392);e.exports=function(e,t){var n=i(e,t);return s(n)?n:void 0}},659:function(e,t,n){var s=n(1873),i=Object.prototype,o=i.hasOwnProperty,r=i.toString,a=s?s.toStringTag:void 0;e.exports=function(e){var t=o.call(e,a),n=e[a];try{e[a]=void 0;var s=!0}catch(e){}var i=r.call(e);return s&&(t?e[a]=n:delete e[a]),i}},392:function(e){e.exports=function(e,t){return null==e?void 0:e[t]}},361:function(e){var t=/^(?:0|[1-9]\d*)$/;e.exports=function(e,n){var s=typeof e;return!!(n=null==n?9007199254740991:n)&&("number"==s||"symbol"!=s&&t.test(e))&&e>-1&&e%1==0&&e<n}},6800:function(e,t,n){var s=n(5288),i=n(4894),o=n(361),r=n(3805);e.exports=function(e,t,n){if(!r(n))return!1;var a=typeof t;return!!("number"==a?i(n)&&o(t,n.length):"string"==a&&t in n)&&s(n[t],e)}},7296:function(e,t,n){var s,i=n(5481),o=(s=/[^.]+$/.exec(i&&i.keys&&i.keys.IE_PROTO||""))?"Symbol(src)_1."+s:"";e.exports=function(e){return!!o&&o in e}},5527:function(e){var t=Object.prototype;e.exports=function(e){var n=e&&e.constructor;return e===("function"==typeof n&&n.prototype||t)}},3650:function(e,t,n){var s=n(4335)(Object.keys,Object);e.exports=s},6009:function(e,t,n){e=n.nmd(e);var s=n(4840),i=t&&!t.nodeType&&t,o=i&&e&&!e.nodeType&&e,r=o&&o.exports===i&&s.process,a=function(){try{var e=o&&o.require&&o.require("util").types;return e||r&&r.binding&&r.binding("util")}catch(e){}}();e.exports=a},9350:function(e){var t=Object.prototype.toString;e.exports=function(e){return t.call(e)}},4335:function(e){e.exports=function(e,t){return function(n){return e(t(n))}}},6757:function(e,t,n){var s=n(1033),i=Math.max;e.exports=function(e,t,n){return t=i(void 0===t?e.length-1:t,0),function(){for(var o=arguments,r=-1,a=i(o.length-t,0),u=Array(a);++r<a;)u[r]=o[t+r];r=-1;for(var l=Array(t+1);++r<t;)l[r]=o[r];return l[t]=n(u),s(e,this,l)}}},9325:function(e,t,n){var s=n(4840),i="object"==typeof self&&self&&self.Object===Object&&self,o=s||i||Function("return this")();e.exports=o},2865:function(e,t,n){var s=n(9570),i=n(1811)(s);e.exports=i},1811:function(e){var t=Date.now;e.exports=function(e){var n=0,s=0;return function(){var i=t(),o=16-(i-s);if(s=i,o>0){if(++n>=800)return arguments[0]}else n=0;return e.apply(void 0,arguments)}}},7473:function(e){var t=Function.prototype.toString;e.exports=function(e){if(null!=e){try{return t.call(e)}catch(e){}try{return e+""}catch(e){}}return""}},6139:function(e,t,n){var s=n(6547),i=n(1791),o=n(999),r=n(4894),a=n(5527),u=n(5950),l=Object.prototype.hasOwnProperty,c=o((function(e,t){if(a(t)||r(t))i(t,u(t),e);else for(var n in t)l.call(t,n)&&s(e,n,t[n])}));e.exports=c},7334:function(e){e.exports=function(e){return function(){return e}}},5288:function(e){e.exports=function(e,t){return e===t||e!=e&&t!=t}},3488:function(e){e.exports=function(e){return e}},2428:function(e,t,n){var s=n(7534),i=n(346),o=Object.prototype,r=o.hasOwnProperty,a=o.propertyIsEnumerable,u=s(function(){return arguments}())?s:function(e){return i(e)&&r.call(e,"callee")&&!a.call(e,"callee")};e.exports=u},6449:function(e){var t=Array.isArray;e.exports=t},4894:function(e,t,n){var s=n(1882),i=n(294);e.exports=function(e){return null!=e&&i(e.length)&&!s(e)}},3656:function(e,t,n){e=n.nmd(e);var s=n(9325),i=n(9935),o=t&&!t.nodeType&&t,r=o&&e&&!e.nodeType&&e,a=r&&r.exports===o?s.Buffer:void 0,u=(a?a.isBuffer:void 0)||i;e.exports=u},1882:function(e,t,n){var s=n(2552),i=n(3805);e.exports=function(e){if(!i(e))return!1;var t=s(e);return"[object Function]"==t||"[object GeneratorFunction]"==t||"[object AsyncFunction]"==t||"[object Proxy]"==t}},294:function(e){e.exports=function(e){return"number"==typeof e&&e>-1&&e%1==0&&e<=9007199254740991}},3805:function(e){e.exports=function(e){var t=typeof e;return null!=e&&("object"==t||"function"==t)}},346:function(e){e.exports=function(e){return null!=e&&"object"==typeof e}},7167:function(e,t,n){var s=n(4901),i=n(7301),o=n(6009),r=o&&o.isTypedArray,a=r?i(r):s;e.exports=a},5950:function(e,t,n){var s=n(695),i=n(8984),o=n(4894);e.exports=function(e){return o(e)?s(e):i(e)}},9935:function(e){e.exports=function(){return!1}},1669:function(e){"use strict";e.exports=jQuery}},t={};function n(s){var i=t[s];if(void 0!==i)return i.exports;var o=t[s]={id:s,loaded:!1,exports:{}};return e[s](o,o.exports,n),o.loaded=!0,o.exports}n.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return n.d(t,{a:t}),t},n.d=function(e,t){for(var s in t)n.o(t,s)&&!n.o(e,s)&&Object.defineProperty(e,s,{enumerable:!0,get:t[s]})},n.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"==typeof window)return window}}(),n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},n.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},n.nmd=function(e){return e.paths=[],e.children||(e.children=[]),e},function(){"use strict";var e=e||{};window.WP_Smush=e,String.prototype.includes||(String.prototype.includes=function(e,t){return"number"!=typeof t&&(t=0),!(t+e.length>this.length)&&-1!==this.indexOf(e,t)}),n(770),n(3908),n(596),n(8686),n(8647),n(3199),n(5630),n(4726),n(2339),n(234),n(5994),n(8425),n(5914),n(8847),n(9968),n(8945),n(3368),n(7811)}()}();
//# sourceMappingURL=smush-admin.min.js.map