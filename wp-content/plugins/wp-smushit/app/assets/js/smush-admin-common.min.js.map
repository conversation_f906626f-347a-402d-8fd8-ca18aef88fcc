{"version": 3, "file": "js/smush-admin-common.min.js", "mappings": "AAAAA,QAAO,SAAUC,GAChB,aAKGA,EAAE,QAAQC,GAAG,QAAS,yBAAyB,SAAUC,GAErD,GAAIF,EAAEG,MAAMC,KAAK,YACb,OAAO,EAIXF,EAAEG,iBAEF,IAAMC,EAAeN,EAAEG,MAAMI,KAAK,iBAClCP,EAAEG,MAAMK,UAAUC,GAAG,GAAGF,KAAK,wBAAwBG,cACrDJ,EAAaK,KAA4B,KAAvBL,EAAaK,OAAgB,IAAM,IACzD,GACJ", "sources": ["webpack://wp-smushit/./_src/js/modules/admin-common.js"], "sourcesContent": ["jQuery(function ($) {\n\t'use strict';\n\n    /**\n\t * Handle the Smush Stats link click\n\t */\n    $('body').on('click', 'a.smush-stats-details', function (e) {\n        //If disabled\n        if ($(this).prop('disabled')) {\n            return false;\n        }\n\n        // prevent the default action\n        e.preventDefault();\n        //Replace the `+` with a `-`\n        const slide_symbol = $(this).find('.stats-toggle');\n        $(this).parents().eq(1).find('.smush-stats-wrapper').slideToggle();\n        slide_symbol.text(slide_symbol.text() == '+' ? '-' : '+');\n    });\n});"], "names": ["j<PERSON><PERSON><PERSON>", "$", "on", "e", "this", "prop", "preventDefault", "slide_symbol", "find", "parents", "eq", "slideToggle", "text"], "sourceRoot": ""}