{"version": 3, "file": "js/smush-webp-fallback.min.js", "mappings": "CAAA,WACC,aAGA,IAA4BA,EAASC,EAIhCC,EAJuBF,EAeT,QAfkBC,EAeT,SAACE,GAE5B,GADAC,SAASC,gBAAgBC,UAAUC,IAAIJ,EAAkB,OAAS,YAC9DA,EAAJ,CAIA,IAAMK,EAAuBC,OAAOC,yBAAyBC,QAAQC,UAAW,gBAGhFH,OAAOI,eAAeF,QAAQC,UAAW,eAAgB,CACxDE,MAAO,SAAUC,GAEhB,IAAKC,KAAKC,QAAQC,kBACjB,OAAOV,EAAqBM,MAAMK,KAAKH,KAAMD,GAG9C,IAAMK,EAAoBC,KAAKC,MAAMN,KAAKC,QAAQC,mBAElD,OAAIH,KAAiBK,EACbA,EAAkBL,GAGnBP,EAAqBM,MAAMK,KAAKH,KAAMD,EAC9C,IAGD,IAAMQ,EAAuBnB,SAASoB,iBAAiB,6CACvD,GAAID,EAAqBE,OAAQ,CAEhC,IAAMC,EAAoB,CAAC,MAAO,UAClCH,EAAqBI,SAAQ,SAACC,GAC7B,IAAMR,EAAoBC,KAAKC,MAAMM,EAAQX,QAAQC,mBACrDQ,EAAkBC,SAAQ,SAAUE,GAC/BA,KAAYT,GACfQ,EAAQE,aAAaD,EAAUT,EAAkBS,GAEnD,IAGI,OAAQT,IACXQ,EAAQG,MAAMC,WAAaZ,EAAkBa,IAE1C,aAAcb,IACjBQ,EAAQG,MAAMG,gBAAkBd,EAAkB,YAEpD,GACD,CA1CA,CA2CD,GA1DKlB,EAAM,IAAIiC,OACVC,OAAS,WACZ,IAAIC,EAAUnC,EAAIoC,MAAQ,GAAOpC,EAAIqC,OAAS,EAC9CtC,EAASoC,EACV,EACAnC,EAAIsC,QAAU,WACbvC,GAAS,EACV,EACAC,EAAIuC,IAAM,0BAXQ,CACjBC,MAAO,oHAU0C1C,EAmDnD,CAnED", "sources": ["webpack://wp-smushit/./_src/js/frontend/webp-fallback.js"], "sourcesContent": ["(function () {\n\t'use strict';\n\n\t// Source: https://developers.google.com/speed/webp/faq#in_your_own_javascript.\n\tfunction check_webp_feature(feature, callback) {\n\t\tvar kTestImages = {\n\t\t\talpha: \"UklGRkoAAABXRUJQVlA4WAoAAAAQAAAAAAAAAAAAQUxQSAwAAAARBxAR/Q9ERP8DAABWUDggGAAAABQBAJ0BKgEAAQAAAP4AAA3AAP7mtQAAAA==\",\n\t\t};\n\t\tvar img = new Image();\n\t\timg.onload = function () {\n\t\t\tvar result = (img.width > 0) && (img.height > 0);\n\t\t\tcallback(result);\n\t\t};\n\t\timg.onerror = function () {\n\t\t\tcallback(false);\n\t\t};\n\t\timg.src = \"data:image/webp;base64,\" + kTestImages[feature];\n\t}\n\n\tcheck_webp_feature('alpha', (isSupportedWebP) => {\n\t\tdocument.documentElement.classList.add(isSupportedWebP ? 'webp' : 'no-webp');\n\t\tif (isSupportedWebP) {\n\t\t\treturn;\n\t\t}\n\n\t\tconst originalGetAttribute = Object.getOwnPropertyDescriptor(Element.prototype, 'getAttribute');\n\n\t\t// Redefine the getAttribute function with a custom implementation\n\t\tObject.defineProperty(Element.prototype, 'getAttribute', {\n\t\t\tvalue: function (attributeName) {\n\t\t\t\t// data-smush-webp-fallback.\n\t\t\t\tif (!this.dataset.smushWebpFallback) {\n\t\t\t\t\treturn originalGetAttribute.value.call(this, attributeName);\n\t\t\t\t}\n\n\t\t\t\tconst webpFallbackValue = JSON.parse(this.dataset.smushWebpFallback);\n\n\t\t\t\tif (attributeName in webpFallbackValue) {\n\t\t\t\t\treturn webpFallbackValue[attributeName];\n\t\t\t\t}\n\n\t\t\t\treturn originalGetAttribute.value.call(this, attributeName);\n\t\t\t}\n\t\t});\n\n\t\tconst webpFallbackElements = document.querySelectorAll('[data-smush-webp-fallback]:not(.lazyload)');\n\t\tif (webpFallbackElements.length) {\n\t\t\t// Update background image, src, srcset.\n\t\t\tconst imageDisplayAttrs = ['src', 'srcset'];\n\t\t\twebpFallbackElements.forEach((element) => {\n\t\t\t\tconst webpFallbackValue = JSON.parse(element.dataset.smushWebpFallback);\n\t\t\t\timageDisplayAttrs.forEach(function (attrName) {\n\t\t\t\t\tif (attrName in webpFallbackValue) {\n\t\t\t\t\t\telement.setAttribute(attrName, webpFallbackValue[attrName]);\n\t\t\t\t\t}\n\t\t\t\t});\n\n\t\t\t\t// Update background image.\n\t\t\t\tif ('bg' in webpFallbackValue) {\n\t\t\t\t\telement.style.background = webpFallbackValue.bg;\n\t\t\t\t}\n\t\t\t\tif ('bg-image' in webpFallbackValue) {\n\t\t\t\t\telement.style.backgroundImage = webpFallbackValue['bg-image'];\n\t\t\t\t}\n\t\t\t});\n\t\t}\n\t});\n})();\n"], "names": ["feature", "callback", "img", "isSupportedWebP", "document", "documentElement", "classList", "add", "originalGetAttribute", "Object", "getOwnPropertyDescriptor", "Element", "prototype", "defineProperty", "value", "attributeName", "this", "dataset", "smushWebpFallback", "call", "webpFallbackValue", "JSON", "parse", "webpFallbackElements", "querySelectorAll", "length", "imageDisplayAttrs", "for<PERSON>ach", "element", "attrName", "setAttribute", "style", "background", "bg", "backgroundImage", "Image", "onload", "result", "width", "height", "onerror", "src", "alpha"], "sourceRoot": ""}