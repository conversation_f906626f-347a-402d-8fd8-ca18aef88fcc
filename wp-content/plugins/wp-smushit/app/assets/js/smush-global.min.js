!function(){var t={1873:function(t,n,e){var r=e(9325).Symbol;t.exports=r},1033:function(t){t.exports=function(t,n,e){switch(e.length){case 0:return t.call(n);case 1:return t.call(n,e[0]);case 2:return t.call(n,e[0],e[1]);case 3:return t.call(n,e[0],e[1],e[2])}return t.apply(n,e)}},695:function(t,n,e){var r=e(8096),o=e(2428),c=e(6449),i=e(3656),u=e(361),a=e(7167),s=Object.prototype.hasOwnProperty;t.exports=function(t,n){var e=c(t),f=!e&&o(t),l=!e&&!f&&i(t),p=!e&&!f&&!l&&a(t),b=e||f||l||p,d=b?r(t.length,String):[],v=d.length;for(var y in t)!n&&!s.call(t,y)||b&&("length"==y||l&&("offset"==y||"parent"==y)||p&&("buffer"==y||"byteLength"==y||"byteOffset"==y)||u(y,v))||d.push(y);return d}},6547:function(t,n,e){var r=e(3360),o=e(5288),c=Object.prototype.hasOwnProperty;t.exports=function(t,n,e){var i=t[n];c.call(t,n)&&o(i,e)&&(void 0!==e||n in t)||r(t,n,e)}},3360:function(t,n,e){var r=e(3243);t.exports=function(t,n,e){"__proto__"==n&&r?r(t,n,{configurable:!0,enumerable:!0,value:e,writable:!0}):t[n]=e}},2552:function(t,n,e){var r=e(1873),o=e(659),c=e(9350),i=r?r.toStringTag:void 0;t.exports=function(t){return null==t?void 0===t?"[object Undefined]":"[object Null]":i&&i in Object(t)?o(t):c(t)}},7534:function(t,n,e){var r=e(2552),o=e(346);t.exports=function(t){return o(t)&&"[object Arguments]"==r(t)}},5083:function(t,n,e){var r=e(1882),o=e(7296),c=e(3805),i=e(7473),u=/^\[object .+?Constructor\]$/,a=Function.prototype,s=Object.prototype,f=a.toString,l=s.hasOwnProperty,p=RegExp("^"+f.call(l).replace(/[\\^$.*+?()[\]{}|]/g,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$");t.exports=function(t){return!(!c(t)||o(t))&&(r(t)?p:u).test(i(t))}},4901:function(t,n,e){var r=e(2552),o=e(294),c=e(346),i={};i["[object Float32Array]"]=i["[object Float64Array]"]=i["[object Int8Array]"]=i["[object Int16Array]"]=i["[object Int32Array]"]=i["[object Uint8Array]"]=i["[object Uint8ClampedArray]"]=i["[object Uint16Array]"]=i["[object Uint32Array]"]=!0,i["[object Arguments]"]=i["[object Array]"]=i["[object ArrayBuffer]"]=i["[object Boolean]"]=i["[object DataView]"]=i["[object Date]"]=i["[object Error]"]=i["[object Function]"]=i["[object Map]"]=i["[object Number]"]=i["[object Object]"]=i["[object RegExp]"]=i["[object Set]"]=i["[object String]"]=i["[object WeakMap]"]=!1,t.exports=function(t){return c(t)&&o(t.length)&&!!i[r(t)]}},8984:function(t,n,e){var r=e(5527),o=e(3650),c=Object.prototype.hasOwnProperty;t.exports=function(t){if(!r(t))return o(t);var n=[];for(var e in Object(t))c.call(t,e)&&"constructor"!=e&&n.push(e);return n}},9302:function(t,n,e){var r=e(3488),o=e(6757),c=e(2865);t.exports=function(t,n){return c(o(t,n,r),t+"")}},9570:function(t,n,e){var r=e(7334),o=e(3243),c=e(3488),i=o?function(t,n){return o(t,"toString",{configurable:!0,enumerable:!1,value:r(n),writable:!0})}:c;t.exports=i},8096:function(t){t.exports=function(t,n){for(var e=-1,r=Array(t);++e<t;)r[e]=n(e);return r}},7301:function(t){t.exports=function(t){return function(n){return t(n)}}},1791:function(t,n,e){var r=e(6547),o=e(3360);t.exports=function(t,n,e,c){var i=!e;e||(e={});for(var u=-1,a=n.length;++u<a;){var s=n[u],f=c?c(e[s],t[s],s,e,t):void 0;void 0===f&&(f=t[s]),i?o(e,s,f):r(e,s,f)}return e}},5481:function(t,n,e){var r=e(9325)["__core-js_shared__"];t.exports=r},999:function(t,n,e){var r=e(9302),o=e(6800);t.exports=function(t){return r((function(n,e){var r=-1,c=e.length,i=c>1?e[c-1]:void 0,u=c>2?e[2]:void 0;for(i=t.length>3&&"function"==typeof i?(c--,i):void 0,u&&o(e[0],e[1],u)&&(i=c<3?void 0:i,c=1),n=Object(n);++r<c;){var a=e[r];a&&t(n,a,r,i)}return n}))}},3243:function(t,n,e){var r=e(6110),o=function(){try{var t=r(Object,"defineProperty");return t({},"",{}),t}catch(t){}}();t.exports=o},4840:function(t,n,e){var r="object"==typeof e.g&&e.g&&e.g.Object===Object&&e.g;t.exports=r},6110:function(t,n,e){var r=e(5083),o=e(392);t.exports=function(t,n){var e=o(t,n);return r(e)?e:void 0}},659:function(t,n,e){var r=e(1873),o=Object.prototype,c=o.hasOwnProperty,i=o.toString,u=r?r.toStringTag:void 0;t.exports=function(t){var n=c.call(t,u),e=t[u];try{t[u]=void 0;var r=!0}catch(t){}var o=i.call(t);return r&&(n?t[u]=e:delete t[u]),o}},392:function(t){t.exports=function(t,n){return null==t?void 0:t[n]}},361:function(t){var n=/^(?:0|[1-9]\d*)$/;t.exports=function(t,e){var r=typeof t;return!!(e=null==e?9007199254740991:e)&&("number"==r||"symbol"!=r&&n.test(t))&&t>-1&&t%1==0&&t<e}},6800:function(t,n,e){var r=e(5288),o=e(4894),c=e(361),i=e(3805);t.exports=function(t,n,e){if(!i(e))return!1;var u=typeof n;return!!("number"==u?o(e)&&c(n,e.length):"string"==u&&n in e)&&r(e[n],t)}},7296:function(t,n,e){var r,o=e(5481),c=(r=/[^.]+$/.exec(o&&o.keys&&o.keys.IE_PROTO||""))?"Symbol(src)_1."+r:"";t.exports=function(t){return!!c&&c in t}},5527:function(t){var n=Object.prototype;t.exports=function(t){var e=t&&t.constructor;return t===("function"==typeof e&&e.prototype||n)}},3650:function(t,n,e){var r=e(4335)(Object.keys,Object);t.exports=r},6009:function(t,n,e){t=e.nmd(t);var r=e(4840),o=n&&!n.nodeType&&n,c=o&&t&&!t.nodeType&&t,i=c&&c.exports===o&&r.process,u=function(){try{var t=c&&c.require&&c.require("util").types;return t||i&&i.binding&&i.binding("util")}catch(t){}}();t.exports=u},9350:function(t){var n=Object.prototype.toString;t.exports=function(t){return n.call(t)}},4335:function(t){t.exports=function(t,n){return function(e){return t(n(e))}}},6757:function(t,n,e){var r=e(1033),o=Math.max;t.exports=function(t,n,e){return n=o(void 0===n?t.length-1:n,0),function(){for(var c=arguments,i=-1,u=o(c.length-n,0),a=Array(u);++i<u;)a[i]=c[n+i];i=-1;for(var s=Array(n+1);++i<n;)s[i]=c[i];return s[n]=e(a),r(t,this,s)}}},9325:function(t,n,e){var r=e(4840),o="object"==typeof self&&self&&self.Object===Object&&self,c=r||o||Function("return this")();t.exports=c},2865:function(t,n,e){var r=e(9570),o=e(1811)(r);t.exports=o},1811:function(t){var n=Date.now;t.exports=function(t){var e=0,r=0;return function(){var o=n(),c=16-(o-r);if(r=o,c>0){if(++e>=800)return arguments[0]}else e=0;return t.apply(void 0,arguments)}}},7473:function(t){var n=Function.prototype.toString;t.exports=function(t){if(null!=t){try{return n.call(t)}catch(t){}try{return t+""}catch(t){}}return""}},6139:function(t,n,e){var r=e(6547),o=e(1791),c=e(999),i=e(4894),u=e(5527),a=e(5950),s=Object.prototype.hasOwnProperty,f=c((function(t,n){if(u(n)||i(n))o(n,a(n),t);else for(var e in n)s.call(n,e)&&r(t,e,n[e])}));t.exports=f},7334:function(t){t.exports=function(t){return function(){return t}}},5288:function(t){t.exports=function(t,n){return t===n||t!=t&&n!=n}},3488:function(t){t.exports=function(t){return t}},2428:function(t,n,e){var r=e(7534),o=e(346),c=Object.prototype,i=c.hasOwnProperty,u=c.propertyIsEnumerable,a=r(function(){return arguments}())?r:function(t){return o(t)&&i.call(t,"callee")&&!u.call(t,"callee")};t.exports=a},6449:function(t){var n=Array.isArray;t.exports=n},4894:function(t,n,e){var r=e(1882),o=e(294);t.exports=function(t){return null!=t&&o(t.length)&&!r(t)}},3656:function(t,n,e){t=e.nmd(t);var r=e(9325),o=e(9935),c=n&&!n.nodeType&&n,i=c&&t&&!t.nodeType&&t,u=i&&i.exports===c?r.Buffer:void 0,a=(u?u.isBuffer:void 0)||o;t.exports=a},1882:function(t,n,e){var r=e(2552),o=e(3805);t.exports=function(t){if(!o(t))return!1;var n=r(t);return"[object Function]"==n||"[object GeneratorFunction]"==n||"[object AsyncFunction]"==n||"[object Proxy]"==n}},294:function(t){t.exports=function(t){return"number"==typeof t&&t>-1&&t%1==0&&t<=9007199254740991}},3805:function(t){t.exports=function(t){var n=typeof t;return null!=t&&("object"==n||"function"==n)}},346:function(t){t.exports=function(t){return null!=t&&"object"==typeof t}},7167:function(t,n,e){var r=e(4901),o=e(7301),c=e(6009),i=c&&c.isTypedArray,u=i?o(i):r;t.exports=u},5950:function(t,n,e){var r=e(695),o=e(8984),c=e(4894);t.exports=function(t){return c(t)?r(t):o(t)}},9935:function(t){t.exports=function(){return!1}}},n={};function e(r){var o=n[r];if(void 0!==o)return o.exports;var c=n[r]={id:r,loaded:!1,exports:{}};return t[r](c,c.exports,e),c.loaded=!0,c.exports}e.n=function(t){var n=t&&t.__esModule?function(){return t.default}:function(){return t};return e.d(n,{a:n}),n},e.d=function(t,n){for(var r in n)e.o(n,r)&&!e.o(t,r)&&Object.defineProperty(t,r,{enumerable:!0,get:n[r]})},e.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(t){if("object"==typeof window)return window}}(),e.o=function(t,n){return Object.prototype.hasOwnProperty.call(t,n)},e.nmd=function(t){return t.paths=[],t.children||(t.children=[]),t},function(){"use strict";var t=e(6139),n=e.n(t);function r(t){return r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},r(t)}var o=new function(){function t(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},e={url:ajaxurl,method:arguments.length>2&&void 0!==arguments[2]?arguments[2]:"POST",cache:!1};return n instanceof FormData?(n.append("action",t),n.append("_ajax_nonce",window.wp_smush_msgs.nonce),e.contentType=!1,e.processData=!1):(n._ajax_nonce=n._ajax_nonce||window.smush_global.nonce||window.wp_smush_msgs.nonce,n.action=t),e.data=n,new Promise((function(t,n){jQuery.ajax(e).done(t).fail(n)})).then((function(t){return"object"!==r(t)&&(t=JSON.parse(t)),t})).catch((function(t){return console.error("Error:",t),t}))}var e={background:{start:function(){return t("bulk_smush_start")},cancel:function(){return t("bulk_smush_cancel")},initState:function(){return t("bulk_smush_get_status")},getStatus:function(){return t("bulk_smush_get_status")},getStats:function(){return t("bulk_smush_get_global_stats")},backgroundHealthyCheck:function(){return t("smush_start_background_pre_flight_check")},backgroundHealthyStatus:function(){return t("smush_get_background_pre_flight_status")}},smush:{syncStats:function(n){return t("get_stats",n=n||{})},ignoreAll:function(n){return t("wp_smush_ignore_all_failed_items",{type:n})}},common:{dismissNotice:function(n){return t("smush_dismiss_notice",{key:n})},hideModal:function(n){return t("hide_modal",{modal_id:n})},track:function(n,e){return t("smush_analytics_track_event",{event:n,properties:e})},request:function(n){return n.action&&t(n.action,n)}},scanMediaLibrary:{start:function(){var n=arguments.length>0&&void 0!==arguments[0]&&arguments[0];return t("wp_smush_start_background_scan",{optimize_on_scan_completed:n=n?1:0,_ajax_nonce:window.wp_smushit_data.media_library_scan.nonce})},cancel:function(){return t("wp_smush_cancel_background_scan",{_ajax_nonce:window.wp_smushit_data.media_library_scan.nonce})},getScanStatus:function(){return t("wp_smush_get_background_scan_status",{_ajax_nonce:window.wp_smushit_data.media_library_scan.nonce})}},webp:{switchMethod:function(n){return t("webp_switch_method",{method:n})}}};n()(this,e)},c=o;function i(t){return i="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},i(t)}function u(t,n){for(var e=0;e<n.length;e++){var r=n[e];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,a(r.key),r)}}function a(t){var n=function(t,n){if("object"!=i(t)||!t)return t;var e=t[Symbol.toPrimitive];if(void 0!==e){var r=e.call(t,n||"default");if("object"!=i(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===n?String:Number)(t)}(t,"string");return"symbol"==i(n)?n:n+""}var s=function(){return t=function t(){!function(t,n){if(!(t instanceof n))throw new TypeError("Cannot call a class as a function")}(this,t)},n=[{key:"track",value:function(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(this.allowToTrack())return c.common.track(t,n)}},{key:"allowToTrack",value:function(){var t;return!(null===(t=window.wp_smush_mixpanel)||void 0===t||!t.opt_in)}}],n&&u(t.prototype,n),e&&u(t,e),Object.defineProperty(t,"prototype",{writable:!1}),t;var t,n,e}(),f=new s;document.addEventListener("DOMContentLoaded",(function(){function t(t){t.preventDefault();var e=t.target.closest(".smush-dismissible-notice");n(e.getAttribute("data-key"),e)}function n(t,n){var e=new XMLHttpRequest;e.open("POST",ajaxurl+"?action=smush_dismiss_notice&key="+t+"&_ajax_nonce="+smush_global.nonce,!0),e.onload=function(){n&&n.querySelector("button.notice-dismiss").dispatchEvent(new MouseEvent("click",{view:window,bubbles:!0,cancelable:!0}))},e.send()}document.querySelectorAll(".smush-dismissible-notice .smush-dismiss-notice-button").forEach((function(n){n.addEventListener("click",t)}));var e=document.querySelector("#wp-smush-cache-notice .smush-dismiss-notice-button");e&&e.addEventListener("click",(function(){var t=new XMLHttpRequest;t.open("POST",ajaxurl+"?action=smush_dismiss_cache_notice&_ajax_nonce="+smush_global.nonce,!0),t.onload=function(){window.SUI.closeNotice("wp-smush-cache-notice")},t.send()}));!function(){var t=document.querySelector(".wp-smush-dismissible-header-notice");if(t&&t.id){var e=t.dataset,r=e.dismissKey,o=e.message;if(o){t.onclick=function(e){var o=e.target.classList,c=o&&o.contains("smush-close-and-dismiss-notice");o&&(c||o.contains("sui-icon-check")||o.contains("sui-button-icon"))&&(r&&n(r),c&&window.SUI.closeNotice(t.id))};var c={type:"warning",icon:"info",dismiss:{show:!0,label:wp_smush_msgs.noticeDismiss,tooltip:wp_smush_msgs.noticeDismissTooltip}};window.SUI.openNotice(t.id,o,c)}}}();var r=document.querySelector('#toplevel_page_smush a[href*="utm_campaign=smush_submenu_upsell');r&&r.addEventListener("click",(function(t){f.track("submenu_upsell")}))}))}()}();
//# sourceMappingURL=smush-global.min.js.map