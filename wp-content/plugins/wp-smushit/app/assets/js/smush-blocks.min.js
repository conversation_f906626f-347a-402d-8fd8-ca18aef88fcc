!function(){"use strict";var e=wp.compose.createHigherOrderComponent,t=wp.element.Fragment,s=wp.blockEditor.InspectorControls,a=wp.components.PanelBody;function r(e,t){return void 0===t?window.smush_vars.strings.gb.select_image:"string"==typeof t?t:React.createElement("div",{id:"smush-stats",className:"sui-smush-media smush-stats-wrapper hidden",style:{display:"block"}},React.createElement("table",{className:"wp-smush-stats-holder"},React.createElement("thead",null,React.createElement("tr",null,React.createElement("th",{className:"smush-stats-header"},window.smush_vars.strings.gb.size),React.createElement("th",{className:"smush-stats-header"},window.smush_vars.strings.gb.savings))),React.createElement("tbody",null,Object.keys(t.sizes).filter((function(e){return 0<t.sizes[e].percent})).map((function(e,s){return React.createElement("tr",{key:s},React.createElement("td",null,e.toUpperCase()),React.createElement("td",null,function(e){var t=1024,s=["kB","MB","GB","TB"];if(Math.abs(e)<t)return e+" B";var a=-1;do{e/=t,++a}while(Math.abs(e)>=t&&a<s.length-1);return e.toFixed(1)+" "+s[a]}(t.sizes[e].bytes)," ","( ",t.sizes[e].percent,"% )"))})))))}var n=e((function(e){return function(n){if("core/image"!==n.name||!n.isSelected||void 0===n.attributes.id)return React.createElement(t,null,React.createElement(e,n));var i=n.attributes.smush;return function(e){var t=new wp.api.models.Media({id:e.attributes.id}),s=e.attributes.smush;t.fetch({attribute:"smush"}).done((function(t){"string"==typeof t.smush?e.setAttributes({smush:t.smush}):void 0===t.smush||void 0!==s&&JSON.stringify(s)===JSON.stringify(t.smush)||e.setAttributes({smush:t.smush})}))}(n),React.createElement(t,null,React.createElement(e,n),React.createElement(s,null,React.createElement(a,{title:window.smush_vars.strings.gb.stats},r(n.attributes.id,i))))}}),"withInspectorControl");wp.hooks.addFilter("editor.BlockEdit","wp-smush/smush-data-control",n)}();
//# sourceMappingURL=smush-blocks.min.js.map