!function(t,e){"use strict";var s=wp.media,i=e.template("<span class='setting smush-stats' data-setting='smush'><span class='name'><%= label %></span><span class='value'><%= value %></span></span>"),a=function(t){return i({label:smush_vars.strings.stats_label,value:t})};if(void 0!==s.view&&void 0!==s.view.Attachment.Details.TwoColumn){var r=s.view.Attachment.Details.TwoColumn;s.view.Attachment.Details.TwoColumn=r.extend({initialize:function(){r.prototype.initialize.apply(this,arguments),this.listenTo(this.model,"change:smush",this.render)},render:function(){s.view.Attachment.prototype.render.apply(this,arguments);var t=this.model.get("smush");return void 0===t||(this.model.fetch(),this.views.detach(),this.$el.find(".settings").append(a(t)),this.views.render()),this}})}var n=s.view.Attachment.Details;s.view.Attachment.Details=n.extend({initialize:function(){n.prototype.initialize.apply(this,arguments),this.listenTo(this.model,"change:smush",this.render)},render:function(){s.view.Attachment.prototype.render.apply(this,arguments);var t=this.model.get("smush");return void 0===t||(this.model.fetch(),this.views.detach(),this.$el.append(a(t))),this}});var l=wp.media.view.AttachmentFilters.extend({id:"media-attachment-smush-filter",createFilters:function(){this.filters={all:{text:smush_vars.strings.filter_all,props:{stats:"all"},priority:10},unsmushed:{text:smush_vars.strings.filter_not_processed,props:{stats:"unsmushed"},priority:20},excluded:{text:smush_vars.strings.filter_excl,props:{stats:"excluded"},priority:30},failed:{text:smush_vars.strings.filter_failed,props:{stats:"failed_processing"},priority:40}}}}),o=wp.media.view.AttachmentsBrowser;wp.media.view.AttachmentsBrowser=wp.media.view.AttachmentsBrowser.extend({createToolbar:function(){o.prototype.createToolbar.call(this),this.toolbar.set("MediaLibraryTaxonomyFilter",new l({controller:this.controller,model:this.collection.props,priority:-75}).render())}})}(jQuery,_);
//# sourceMappingURL=smush-media.min.js.map