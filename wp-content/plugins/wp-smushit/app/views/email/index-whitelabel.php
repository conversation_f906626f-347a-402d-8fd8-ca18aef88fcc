<?php require 'header.php'; ?>
  <!-- Main content -->
  <!--[if mso | IE]><table align="center" border="0" cellpadding="0" cellspacing="0" class="main-content-outlook" style="width:600px;" width="600" ><tr><td style="line-height:0px;font-size:0px;mso-line-height-rule:exactly;"><![endif]-->
  <div class="main-content" style="margin:0px auto;max-width:600px;">
    <table class="smush-whitelabel-summary" align="center" border="0" cellpadding="0" cellspacing="0" role="presentation" style="margin:0px auto;max-width:600px;width:100%;">
      <tbody>
        <tr>
          <td style="direction:ltr;font-size:0px;padding:45px 0 32px;text-align:center;">
            <!--[if mso | IE]><table role="presentation" border="0" cellpadding="0" cellspacing="0"><tr><td class="" style="vertical-align:top;width:550px;" ><![endif]-->
            <div class="mj-column-per-100 mj-outlook-group-fix" style="font-size:0px;text-align:left;direction:ltr;display:inline-block;vertical-align:top;width:100%;">
              <table border="0" cellpadding="0" cellspacing="0" role="presentation" style="background:#fff;background-color:#fff;vertical-align:top;margin:0px auto;max-width: 600px;border-radius: 20px;border-top-left-radius: 20px;border-top-right-radius: 20px;border-bottom-left-radius: 20px;border-bottom-right-radius: 20px;overflow:hidden" width="100%">
                <tbody>
                  <tr>
                    <td align="left" style="font-size:0px;padding:45px 0;word-break:break-word;margin:0px auto;max-width: 600px;">
                      <div style="margin:0px auto;max-width: 600px;font-family:Roboto, Arial, sans-serif;font-size:18px;letter-spacing:-.25px;line-height:30px;text-align:left;color:#1A1A1A;">
                          <?php echo $content_body;//phpcs:ignore ?>
                      </div>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
            <!--[if mso | IE]></td></tr></table><![endif]-->
          </td>
        </tr>
      </tbody>
    </table>
  </div>
  <!--[if mso | IE]></td></tr></table><![endif]-->
  <!-- END Main content -->
<?php require 'footer.php'; ?>