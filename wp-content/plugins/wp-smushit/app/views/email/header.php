<!doctype html>
<html xmlns="http://www.w3.org/1999/xhtml" xmlns:v="urn:schemas-microsoft-com:vml" xmlns:o="urn:schemas-microsoft-com:office:office">
<head>
  <title><?php echo esc_html( $title ); ?></title>
  <meta http-equiv="X-UA-Compatible">
  <!--[if !mso]><!-->
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <!--<![endif]-->
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <meta name="x-apple-disable-message-reformatting">
  <meta name="color-scheme" content="light dark">
  <meta name="supported-color-schemes" content="light dark">
  <style type="text/css">
  :root {
    color-scheme: light dark;
    supported-color-schemes:light dark;
  }
</style>
  <style type="text/css">
    #outlook a {
      padding: 0;
    }

    body {
      margin: 0;
      padding: 0;
      -webkit-text-size-adjust: 100%;
      -ms-text-size-adjust: 100%;
    }

    table,
    td {
      border-collapse: collapse;
      mso-table-lspace: 0pt;
      mso-table-rspace: 0pt;
    }

    img {
      border: 0;
      height: auto;
      line-height: 100%;
      outline: none;
      text-decoration: none;
      -ms-interpolation-mode: bicubic;
    }

    p {
      display: block;
      margin: 13px 0;
    }
  </style>
  <!--[if mso]>
        <xml>
        <o:OfficeDocumentSettings>
          <o:AllowPNG/>
          <o:PixelsPerInch>96</o:PixelsPerInch>
        </o:OfficeDocumentSettings>
        </xml>
        <![endif]-->
  <!--[if lte mso 11]>
        <style type="text/css">
          .mj-outlook-group-fix { width:100% !important; }
        </style>
        <![endif]-->
  <!--[if !mso]><!-->
  <link href="https://fonts.bunny.net/css?family=roboto:400,500,700&display=swap" rel="stylesheet" type="text/css">
  <style type="text/css">
    @import url(https://fonts.bunny.net/css?family=roboto:400,500,700&display=swap);
  </style>
   <style type="text/css">
    * {
      -webkit-font-smoothing: antialiased;
      -moz-osx-font-smoothing: grayscale;
    }

    .p-30 {
      margin-bottom: 30px !important;
    }

    h1 {
      font-size: 25px;
      line-height: 35px;
    }

    h2 {
      font-size: 20px;
      line-height: 30px;
    }

    p,
    li {
      font-size: 14px;
      line-height: 30px;
    }

    a {
      text-decoration: none !important;
      font-weight: 700 !important;
      color: #0059FF !important;
      transition: color 0.5s;
    }

    a:hover {
      color: #0C33A9 !important;
    }

    .hidden-img img {
      display: none !important;
    }

    .button a,
    a.button,
    a.button-cta {
      font-family: Roboto, arial, sans-serif;
      font-size: 13px !important;
      line-height: 20px;
      font-weight: bold;
      background: #0059FF;
      text-decoration: none !important;
      padding: 10px 15px;
      color: #ffffff !important;
      border-radius: 10px;
      display: inline-block;
      margin: 20px auto;
      text-transform: unset !important;
      min-width: unset !important;
      transition: background 0.5s;
    }

    small {
      font-size: 10px;
      line-height: 24px;
    }

    .main-content img {
      max-width: 100% !important;
    }

    .main-content .button {
      background: #0059FF !important;
      color:#fff !important;
      transition: background 0.5s;
    }
    .main-content .button:hover {
      background: #0C33A9 !important;
    }

    @media (min-width: 600px) {
      p,
      li {
        font-size: 16px;
      }
    }
  </style>
  <!--<![endif]-->
  <style type="text/css">
    @media only screen and (min-width:480px) {
      .mj-column-per-100 {
        width: 100% !important;
        max-width: 100%;
      }
    }
  </style>
  <style type="text/css">
    @media only screen and (max-width:480px) {
      h2{
        font-size: 22px !important;
        line-height: 28px !important;
      }
      h3{
        font-size: 20px !important;
        line-height: 30px !important;
      }

      #smush-cdn-upto2x .smush-cdn-title-box{
        padding-top: 20px !important;
      }
      table.mj-full-width-mobile {
        width: 100% !important;
      }

      td.mj-full-width-mobile {
        width: auto !important;
      }
    }
  </style>
 
  <style>
    @media only screen and (max-width:430px) {
      h2{
        font-size: 20px !important;
      }
      h3 {
        font-size: 19px !important;
      }
      td.smush-cdn-title-box{
        width: 55% !important;
      }
      td.smush-cdn-image-box {
        width: 45% !important;
      }
      .smush-whitelabel-summary > tbody > tr > td{
        padding-top: 5px !important;
      }

      .smush-summary-row span{
        padding-top: 6px !important;
        font-size: 12px !important;
      }

      .summary-compression .button{
        margin-top: 45px !important;
      }
      #smush-cdn-upto2x .button{
        margin-top: 12px !important;
      }
    }
  </style>
  <style>
    @media only screen and (max-width:360px) {
      .summary-compression td strong{
        font-size:14px!important;
      }
      .summary-compression td span{
        font-size:11px!important;
      }
    }
  </style>
  <style>
    @media only screen and (prefers-color-scheme: dark) {
      .smush-mail-body {
        background: #1A1A1A !important;
      }
      .smush-header-logo{
        background: #2DC4E0 !important;
      }
      .smush-header-logo span{
        color: #fff !important;
      }
      .main-content,.main-content-inner,.main-content-outlook {
        background: #212121 !important;
        color:#F8F8F8;
      }
      .main-content .button {
        background: #0059FF !important;
        color:#fff !important;
      }
      .main-content .button:hover {
        background: #0C33A9 !important;
      }
      h2 {
        color: #f2f2f2 !important;
      }
      h2 a{
        color: #0059FF !important;
      }
      h2 a:hover{
        color: #0C33A9 !important;
      }
      .main-content p, .summary-compression p,.summary-compression td, .summary-compression strong, .summary-compression span{
        color: #F8F8F8 !important;
      }
      .summary-compression .smush-summary-row {
        border-top-color: #323339 !important;
        border-bottom-color: #323339 !important;
      }
      #smush-cdn-upto2x {
        background: #C0EDF6 !important;
        color: #121212 !important;
      }
      #smush-cdn-upto2x li{
        color: #333333;
      }
      #smush-cdn-upto2x h3, #smush-cdn-upto2x p, #smush-cdn-upto2x td{
        color: #121212 !important;
      }
      .wpmudev-footer-logo{
        background: #EAF9FC !important;
      }
      .wpmudev-follow-us span, .wpmudev-follow-us td{
        color: #E6E6E6 !important;
      }
      .wpmudev-footer div{
        color: #aaa !important;
      }
      
      .smush-light-img{
        display: inline-block !important;
        display: none !important;
        width:0 !important;
        margin:0 !important;
        padding:0 !important;
        visibility:hidden !important;
      }
      .smush-light-img img{
        display: none!important;
        width: 0 !important;
        height: 0 !important;
        margin:0 !important;
        visibility:hidden !important;
      }

      .smush-dark-img {
        display: inline !important;
        width:auto !important;
        visibility: visible !important;
      }
      
    }
  </style>
  <!-- Dark mode for outlook -->
  <style>
    [data-ogsc] .smush-mail-body {
      background: #1A1A1A !important;
    }
    [data-ogsc] .smush-header-logo{
      background: #2DC4E0 !important;
    }
    [data-ogsc] .smush-header-logo span{
      color: #fff !important;
    }
    [data-ogsc] .main-content,[data-ogsc] .main-content-inner,[data-ogsc] .main-content-outlook {
      background: #212121 !important;
      color:#F8F8F8;
    }
    [data-ogsc] .main-content .button {
      background: #0059FF !important;
      color:#fff !important;
    }
    [data-ogsc] .main-content .button:hover {
      background: #0C33A9 !important;
    }
    [data-ogsc] .main-content .button:hover{
      background: #0C33A9 !important;
    }
    [data-ogsc] h2 {
      color: #f2f2f2 !important;
    }
    [data-ogsc] h2 a{
      color: #0059FF !important;
    }
    [data-ogsc] h2 a:hover{
      color: #0C33A9 !important;
    }
     [data-ogsc] .main-content p,  [data-ogsc] .summary-compression p, [data-ogsc] .summary-compression td,  [data-ogsc] .summary-compression strong,  [data-ogsc] .summary-compression span{
      color: #F8F8F8 !important;
    }
     [data-ogsc] .summary-compression  .smush-summary-row {
      border-top-color: #323339 !important;
      border-bottom-color: #323339 !important;
    }
    [data-ogsc] #smush-cdn-upto2x {
      background: #C0EDF6 !important;
      color: #121212 !important;
    }
    [data-ogsc] #smush-cdn-upto2x li{
      color: #333333;
    }
    [data-ogsc] #smush-cdn-upto2x h3, [data-ogsc] #smush-cdn-upto2x p, [data-ogsc] #smush-cdn-upto2x td{
      color: #121212 !important;
    }
    [data-ogsc] .wpmudev-footer-logo{
      background: #EAF9FC !important;
    }
    [data-ogsc] .wpmudev-follow-us span, [data-ogsc] .wpmudev-follow-us td{
      color: #E6E6E6 !important;
    }
    [data-ogsc] .wpmudev-footer div{
      color: #aaa !important;
    }
    
    [data-ogsc] .smush-light-img{
      display: inline-block !important;
      display: none !important;
      width:0 !important;
      margin:0 !important;
      padding:0 !important;
      visibility:hidden !important;
    }
    [data-ogsc] .smush-light-img img{
      display: none!important;
      width: 0 !important;
      height: 0 !important;
      margin:0 !important;
      visibility:hidden !important;
    }

    [data-ogsc] .smush-dark-img {
      display: inline !important;
      width:auto !important;
      visibility: visible !important;
    }
  </style>
</head>

<body style="word-spacing:normal;background-color:#F6F6F6;">
  <div class="smush-mail-body" style="background-color:#F6F6F6;">