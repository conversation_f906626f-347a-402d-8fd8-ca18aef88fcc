=== Smush Image Optimization – Optimize Images | Compress & Lazy Load Images | Convert WebP | Image CDN ===
Plugin Name: Smush Image Optimization – Optimize Images | Compress & Lazy Load Images | Convert WebP | Image CDN
Version: 3.16.5
Author: WPMU DEV
Author URI: https://wpmudev.com/
Contributors: WPMUDEV, alexdunae
Tags: optimize images, convert webp, webp, image optimization, compress images, lazy load, resize images, image optimizer, image compressor, performance, reduce image size
Requires at least: 5.3
Tested up to: 6.5
Stable tag: 3.16.5
Requires PHP: 7.4
License: GPL v2 - http://www.gnu.org/licenses/old-licenses/gpl-2.0.html

Compress images & optimize images with built-in lossless compression, lazy load, WebP conversion, and resize detection to make your site load amazingly fast.

== Description ==

### The #1 Image Optimization Plugin for WordPress ###

[Smush](https://wpmudev.com/project/wp-smush-pro/?utm_source=wordpress.org&utm_medium=readme&utm_campaign=smush-readme&utm_content=wp_smush_pro#trial) is the leading image optimization plugin - optimize, resize, and compress images, as well as convert images to WebP format for faster loading web pages. 

Brought to you by the WPMU DEV team - founded in 2007 and trusted by web professionals from freelancer to agency worldwide ever since. 

Whether you spell it 'optimise' or 'optimize' - with Smush’s image optimizer you can compress images and serve images in next-gen formats (convert to WebP), all without introducing a visible drop in quality.

**Enjoy unlimited bulk image optimization with [Smush Pro](https://wpmudev.com/project/wp-smush-pro/?utm_source=wordpress.org&utm_medium=readme&utm_campaign=smush-readme-above-the-fold&utm_content=wp_smush_pro).**

Level up immediately with exclusive Pro benefits like unlimited image optimization, 5x more image compression, and 123-point global image CDN. [Learn more about Pro](https://wpmudev.com/project/wp-smush-pro/?utm_source=wordpress.org&utm_medium=readme&utm_campaign=smush-readme&utm_content=wp_smush_pro). 

[youtube https://www.youtube.com/watch?v=Z1kTcyLFE2g&feature=youtu.be]

### Award-Winning Image Optimizer ###

Smush has been benchmarked and tested number one for speed and quality. Beyond that, it's also the [award-winning](https://wpmudev.com/blog/smush-wins-plugin-madness-champion-back-to-back/?utm_source=wordpress.org&utm_medium=readme&utm_campaign=smush-readme&utm_content=wpmu_dev_blog_award_winning), back-to-back proven crowd-favorite WordPress image optimizer trusted by over 1M+ worldwide to: 

- Optimize images
- Compress images
- Lazy load images
- Resize images
- Convert to WebP 
- And more...

### Why Use Smush To Optimize Images (The Smush Difference) ###

Smush was built from the ground up to make it easy for WordPress users to optimize images, activate lazy loading, compress media files, and more - whether they’re just getting started, or a seasoned pro who's developed thousands of websites.

Improve website performance (along with Google PageSpeed Insights scores) with compressed and optimized images and lazy loading - all while actually delivering a better user experience because the rollout of Core Web Vitals has proven one thing: performance is about far more than *just* scoring well on performance testing tools. Visitor experience matters. 

Discover the features that set Smush apart from other image optimization plugins:  

- **Lossless compression (Basic Smush)** - Strip unused data and compress images without affecting image quality.
- **Lossy compression (Super Smush)** - Optimize images up to 2x more than lossless compression with our cutting-edge, multi-pass lossy image compression. 
- **Ultra Smush (Pro Only)** - Take performance to the next level with 5x image compressing power! Your images will be as light and fast as possible, while still preserving remarkable image quality.
- **Built-In Lazy Loading** - Lazy load images to defer loading of offscreen images with the flip of a switch.
- **Convert to WebP (Pro Only)** - Use the Local WebP feature to convert and automatically serve images in the next-gen WebP format.
- **Bulk Smush** - Bulk optimize and compress images with one click. 
- **Background Optimization (Pro Only)** - Smush's powerful image optimization features will continue to run in the background even when the plugin is closed. 
- **Resize Images** - Set a max width and height, and large images will scale down as they are being optimized. 
- **123-point global CDN (Pro Only)** - Ensure blazing-fast image delivery anywhere in the world. Includes automatic WebP conversion and image resizing, plus, GIF / Rest API support.
- **Incorrect Image Size Detection** - Quickly locate images that are slowing down your site. 
- **Directory Smush** - Optimize images even if they aren't in the default WordPress media library. 
- **Automated Optimization** - Asynchronously auto-smush your attachments for super fast compression on upload. 
- **No Monthly Limits** - Optimize all of your images (up to 5 MB in size) free forever (no daily, monthly, or annual limits).
- **Gutenberg Block Integration** - View image compression information directly in image blocks. 
- **Multisite Compatible** - Flexible global and subsite settings for multisite installations.
- **Optimize All Image Files** - Smush supports optimization for all of your PNG and JPEG files.
- **No Performance Impact On Your Server** - Image optimization is not run on your website's server to prevent wasting server resources (that you pay for) and is instead run using the fast, reliable WPMU DEV Smush API. 
- **Configs** - Set your preferred Smush settings, save them as a config, and instantly upload to any other site. 
- And many, many, more!


### Learn More With These Hands-On Image Optimization & Performance Tutorials

- [How To Convert Images to WebP In WordPress](https://wpmudev.com/blog/serve-images-next-gen-formats-webp?utm_source=wordpress.org&utm_medium=readme&utm_campaign=smush-readme&utm_content=wpmu_dev_blog_webp)
- [The Ultimate Guide to Image Optimization for WordPress](https://wpmudev.com/blog/image-optimization-guide/?utm_source=wordpress.org&utm_medium=readme&utm_campaign=smush-readme&utm_content=wpmu_dev_blog_ultimateguide)
- [How To Resize and Compress Multiple Images (In Bulk)](https://wpmudev.com/blog/smush-bulk-optimize-images/?utm_source=wordpress.org&utm_medium=readme&utm_campaign=smush-readme&utm_content=wpmu_dev_blog_bulk)
- [How To Ace Google PageSpeed's Image Recommendations with Smush](https://wpmudev.com/blog/smush-pagespeed-image-compression/?utm_source=wordpress.org&utm_medium=readme&utm_campaign=smush-readme&utm_content=wpmu_dev_blog_pagespeed)


### Local WebP - Serve Images In Next-Gen Formats

With Smush, you can take advantage of next-gen image formats by converting all of your images to WebP. WebP is a file format that enables superior image compression and quality retention. All major performance testing solutions, including Google PageSpeed Insights, assess your site's ability to serve images in these next-gen formats due to the significant impact on visitor experience.

**WebP lossless images are 26% smaller in size compared to PNGs. WebP lossy images are 25-34% smaller than comparable JPG images**. More information can be found in [this Google developers article](https://developers.google.com/speed/webp).

**The best part:** Smush's Local WebP feature makes this incredibly easy to set up with the ability to host all images locally (not requiring the use of a CDN). Our Local WebP feature includes automatically replacing PNG and JPEG images on your website's frontend to serve WebP images - with the necessary fallbacks for browsers that don't support WebP yet.

Learn more: [How To Convert Images to WebP In WordPress](https://wpmudev.com/blog/serve-images-next-gen-formats-webp?utm_source=wordpress.org&utm_medium=readme&utm_campaign=smush-readme&utm_content=wpmu_dev_blog_webp)

Note: Yes, Smush supports WebP conversion for all image formats, including JPEG to WebP and PNG to WebP.

With the best image optimization plugin for WordPress - you can finally stop worrying about the performance impact of using the images you want on your website. 

### Compress Images While Preserving Image Quality

Image compression plugins don't have to destroy images with a visible, 30% loss in quality. Smush strips hidden information from your images (that can often be bulky), and reduces image file sizes without introducing a visible impact on appearance. 

[youtube https://www.youtube.com/watch?v=RZJGq4zwf1A]

Smush meticulously scans every image you upload - or have already added to your site - cuts all the unnecessary data, and scales it for you before adding it to your media library. And it can serve images in the WebP format.

### Compress Images While You Sleep With Background Image Optimization

Have a large number of images to compress? Smush’s powerful optimization features will continue to work their magic in the background, even when you’ve closed the plugin completely. You’ll receive an email when image optimization has been completed - with a full report.

### Compress Any Image in Any Directory

As well as smushing your media uploads, you may want to compress the images stored in other folders. Smush now lets you compress any image in any directory - so that you can optimize all the images on your site - including [NextGEN](https://wordpress.org/plugins/nextgen-gallery/) images, images stored on Amazon S3 using [WP Offload Media](https://wordpress.org/plugins/amazon-s3-and-cloudfront/) and images in EVERY WordPress plugin and theme package!

### Compatible with Your Favorite Themes, Page Builders, and Media Library Plugins

No matter what theme or plugins you use to manage your WordPress media library, Smush has you covered. Check out just a few of the popular products Smush is working with to help make your site faster and more efficient:

- [WP All Import](https://wordpress.org/plugins/wp-all-import/)
- [WP Offload Media](https://wordpress.org/plugins/amazon-s3-and-cloudfront/)
- [WP Media Folder](https://www.joomunited.com/wordpress-products/wp-media-folder)
- [WP Retina 2x](https://wordpress.org/plugins/wp-retina-2x/)
- [NextGen Gallery](https://wordpress.org/plugins/nextgen-gallery/)
- [WPML](https://wpml.org/)
- [Envira Gallery](https://enviragallery.com/)
- [Avada Fusion Builder](https://theme-fusion.com/products/fusion-builder/)
- And many, many more!

### Incorrect Image Size Detection ###
Smush includes a wrong-size image finder. Activate this feature, and your images will be highlighted with smart tips to let you easily resize your images. Quickly locate the images that are keeping you from getting that perfect 100 on your Google PageSpeed test.

### Defer Offscreen Images (Lazy Load) ###
Smush includes built-in lazy loading. If your page has a bunch of images below the fold, lazy loading will drastically speed up your page by serving only the images being viewed, and delaying others further down the page.

### Save time with Smush Configs ###
Configs allow you to save your preferred Smush configuration settings and apply them to your other sites in a few clicks. You can create unlimited configs.

= Here's What Our Users Are Saying = 

★★★★★
> “I had no idea that my page load time was being dragged down by the images. The plugin nearly halved the time it took.” - [karlcw](http://profiles.wordpress.org/karlcw)

★★★★★
> “I optimise my photos in Photoshop, but Smush makes it so easy - it does it automatically. I can just sit back and enjoy the speed.” - [helen432](https://profiles.wordpress.org/helen432)

★★★★★
> “Smush helped reduce the total files size on my site and increased browsing speed. Well done guys!” - [pdci](https://wordpress.org/support/users/pdci/)

★★★★★
> “It’s very discrete and does not bother me with an API key or other additional installation steps. The main dashboard of the plugin gives me nice insight. Overall it really fits my needs and I’ll be willing to upgrade to pro if my needs change. I’d recommend it to clients/friends without hesitation. Keep it up!” - [tarkan_](https://wordpress.org/support/users/tarkan_/)

### What about Multisite?

Smush can be used to optimize all images across your entire network! Use global settings, or configure each site individually.

Smush is super easy to use - no confusing image compression software settings. Smush lets you optimize all images in your library either one at a time, or all together.  Plus, configure auto-smush to asynchronously scale and compress some or all of your images as they are being uploaded - it's incredibly fast.

The faster your site loads, the more Google, Bing, Yahoo, and other search engines will like it. Your site will load faster and rank higher.

### Shameless Plug(ins)

- [Hummingbird](https://wordpress.org/plugins/search/Hummingbird/) - Page Speed Optimization
- [SmartCrawl](https://wordpress.org/plugins/smartcrawl-seo/) - SEO Optimizer
- [Forminator](https://wordpress.org/plugins/forminator/) - Form, Poll, and Quiz Builder
- [Defender](https://wordpress.org/plugins/defender-security/) - Security, Monitoring, and Hack Protection
- [Hustle](https://wordpress.org/plugins/wordpress-popup/) - Pop-ups, Slide-ins and Email Opt-ins
- [Beehive](https://wordpress.org/plugins/beehive-analytics/) - Customizable Google Analytics Dashboards

Smush can take care of all your image compression needs... all for free!

However, if you’d like fast CDN image delivery, WebP conversion, automatic image resizing, the ability to optimize (or optimise) images up to 256MB, bulk smush optimization for all your images in just one-click, auto-convert PNG to JPEG, the ability to make a copy of your full-sized images (to restore them at any point), you can always take the next step with [WP Smush Pro](https://wpmudev.com/project/wp-smush-pro/?utm_source=wordpress.org&utm_medium=readme&utm_campaign=smush-readme&utm_content=wp_smush_pro#trial). And get even more with her whole team of WordPress optimization hero friends (security, SEO, performance, support, and maintenance automation) - trusted by thousands of agencies and freelancers offering site maintenance services.

### Privacy

Smush does not interact with end users on your website. The only input option Smush has is to a newsletter subscription for site admins only.

Smush uses a third-party email service (Mailchimp) to send informational emails (opt-in) to the site administrator. The administrator's email address is sent to Mailchimp and a cookie is set by the service. Only administrator information is collected by Mailchimp.

Smush sends images to the WPMU DEV servers to optimize them for web use. This includes the transfer of EXIF data. The EXIF data will either be stripped or returned as it is. It is not stored on the WPMU DEV servers.

== Frequently Asked Questions ==

= I just finished running Smush, but Google PageSpeed still says my images need compressing and resizing. How do I fix it? =

This means your images were not properly scaled for where they are being displayed. Scaling images before uploading them can be time-consuming, but can save space and speed up load time. First, determine what size your image needs to be. You can use the built-in images size detector included in the free version of Smush to find what height and width your image should be. Once you know how large the image should be, scale your images to the right size.

= PageSpeed Insights is telling me to defer offscreen images. Can Smush fix that?

Lazy Load will defer your offscreen images from loading until they are needed. From the Smush Dashboard, select Lazy Load and click Activate. Smush Lazy Load works out of the box or can be customized based on your needs.

*Tip:* If you're having any issues or want to save a ton of time, the Smush Pro CDN includes auto-resizing of images.

= Does Smush delete or replace my original full-size images? =

Nope. WordPress crops and resizes every image you upload for embedding on your site. By default, Smush only compresses these cropped and resized images, not your original full-size images. To compress your original full-sized images, use [Smush Pro](https://wpmudev.com/project/wp-smush-pro/?utm_source=wordpress.org&utm_medium=readme&utm_campaign=smush-readme&utm_content=smush_pro#trial).

= What type of file should I use for my images (GIF, PNG, JPG)? =

GIF files are large and can’t be optimized much. They should only be used if the image is animated.


PNG is best for computer generated graphics (vectors, logos, fonts, etc.), images with few colors, or images with transparency.


JPG should be used for photography or images with a lot of color variation.

*Tip:* The Smush Pro CDN includes WebP compression - sharper images that are 25 to 30 percent smaller than JPEG and PNG files.

= I’m a photographer. Can I keep all my EXIF data? =

Yes! EXIF data stores camera settings, focal length, date, time, and location information in image files. EXIF data makes image files larger, but if you are a photographer you may want to preserve this information. We have included the option to preserve EXIF image data with Smush.

= I just ran Bulk Smush and some of my images didn't get compressed. Why would this happen? =

First, check to see if you're receiving any server errors. If your images seem to be processing correctly, check the file size of the images being skipped. Images over 5mb will not be processed by the free version of Smush. To compress images up to 256mb, get [Smush Pro](https://wpmudev.com/project/wp-smush-pro/?utm_source=wordpress.org&utm_medium=readme&utm_campaign=smush-readme&utm_content=wp_smush_pro#trial).

= I activated Smush, but I don’t see any difference in image quality. How am I supposed to know if Smush is working? =

Great question! It is important to understand Smush is an image optimization tool that uses lossless image compression to save disk storage space and speed up your site. Smush squeezes data out from your image files that will save space, resize huge images, and improve speed - all without changing quality.

You can track file size changes in your media library, individual image savings, directory savings, and Smush total savings with Smush stats. If you want to see some additional magic, run before-and-after page-speed tests with Google PageSpeed Insights. Every little bit of resource savings is important when it comes to page-speed!

= Can't I just use Photoshop or another image editing application to optimize my images? =

Optimizing all the image sizes generated by WordPress, images being imported from plugins and themes, and images from other web sites is a painful and time-consuming process.

Smush automatically handles optimizing all your images no matter where they come from, and can be used either as a standalone tool or alongside Photoshop.

= My Google PageSpeed test is telling me to serve images in Next-Gen formats. Can Smush do that? =

WebP and other Next-Gen formats are not supported on all browsers and require fallback images. For that reason, WebP conversion is only available in Smush Pro. Smush Pro converts images to WebP for a 25-30% savings over JPEG and PNG, and serves fallback images on unsupported browsers.

= Is it optimize or optimise? =
It depends what side of the pond you live on... but whether you say optimize, optimise, optimizer, or Smush goddess of speed, this plugin will make sure your images are loading lightning fast.

= How can I report security issues or bugs? =

We take plugin security incredibly seriously; if you have a bug or vulnerability to report, you can do so through the Patchstack Vulnerability Disclosure Program. It’s fast, easy, and you will be notified when the issue is fixed. [Report a vulnerability](https://patchstack.com/database/vdp/wp-smushit).

== Screenshots ==

1. Getting started couldn't be easier with the Smush setup wizard.
2. Automatically detect and bulk optimize images.
3. Watch as Smush optimizes your media library to reduce page load and save you storage space. 
4. Automatically resize images on upload.

== Changelog ==

= 3.16.5 ( 2024-06-20 ) =
- Improvement: Security improvements
- Fix: Compatibility issue with Divi

= 3.16.4 ( 2024-05-23 ) =
- Fix: Loopback error shown unnecessarily for some sites
- Improvement: Better UI for the loopback error

= 3.16.3 ( 2024-05-15 ) =
- Improvement: Better handling of rotated images
- Improvement: Better pre-flight checks before bulk Smush is started
- Fix: Performance issues in CDN and webp link replacement functionality
- Fix: Bulk Smush performance issues
- Fix: Compatibility issue with wp.com

= 3.16.2 ( 2024-04-17 ) =
- Fix: LazyLoad module not working correctly for picture elements in certain situations
- Fix: LazyLoad module not working correctly for some CSS variables
- Fix: Better handling of !important CSS rules by the LazyLoad module
- Fix: Performance issues on some WooCommerce pages

= 3.16.1 ( 2024-03-27 ) =
- Improvement: Better compatibility with page builders
- Improvement: General code improvements
- Fix: WP Offload integration preventing some formats from getting offloaded

= 3.16.0 ( 2024-03-06 ) =
- New: Directly serve Local Webp images, no rewrite rules required!
- New: Support for inline style tags in CDN and Local WebP modules
- New: Support for multiple background images in CDN and Local WebP modules
- New: Better support for relative links in CDN and Local WebP modules
- New: Better REST API support in CDN and Local WebP modules
- New: CDN and Local WebP images served in WooCommerce REST API responses
- New: Local WebP compatibility with Windows IIS servers
- New: Local WebP compatibility with bedrock
- New: Local WebP compatibility with Litespeed
- New: Local WebP compatibility with Cloudways
- New: Lazy loading of background images
- Fix: Local WebP redirection doesn't work for images with special characters
- Fix: Free version of Smush creating smush-webp folder when activated
- Fix: Extra slash added to image URLs during optimization
- Fix: WP 6.3 compatibility - Skip lazyload for high priority images

= 3.15.5 ( 2024-02-06 ) =
- Improvement: Update the number of CDN locations
- Improvement: Minor copy and UI adjustments

= 3.15.4 ( 2024-01-23 ) =
- Fix: Media library scan gets stuck on some websites
- Improvement: Minor copy and UI changes

= 3.15.3 ( 2023-12-20 ) =
- Improvement: Code stability improvements

= 3.15.2 ( 2023-12-06 ) =
- Fix: Compatibility issues with WP Offload Media integration
- Fix: Resize module deletes thumbnail when there is a naming conflict

= 3.15.1 ( 2023-11-15 ) =
- Improvement: Code stability improvements

= 3.15.0 ( 2023-10-11 ) =
- Improvement: Image size limit increased for pro version
- Fix: Query running frequently and causing performance issues on some sites

= 3.14.2 ( 2023-08-23 ) =
- Fix: Media library scanner not identifying some MariaDB versions correctly
- Improvement: Code improvements

= 3.14.1 ( 2023-07-24 ) =
- Improvement: Minor code improvements and fixes

= 3.14.0 ( 2023-07-17 ) =
- New: Ultra Smush - Level up your image compression, while preserving remarkable image quality
- Fix: Resolved issue with image resize in WP versions < 6.0
- Fix: Resolved PHP 8.x error on GoDaddy Managed WP Hosting
- Fix: Other minor bug fixes and UI improvements

= 3.13.2 ( 2023-07-05 ) =
- Improvement: Small code and text improvements

= 3.13.1 ( 2023-06-13 ) =
- Improvement: Better memory management during scans
- Fix: Dot added to file path when year and month directories disabled
- Fix: Compatibility issue with WP.com
- Fix: Rename config functionality not working
- Fix: Compatibility issue with WP Offload Media
- Fix: PHP warnings
- Fix: Duplicate query

= 3.13.0 ( 2023-05-30 ) =
- New: Scan for detecting changes in the media library
- Improvement: Performance improvements on large sites
- Improvement: Code refactoring
- Fix: PHP warnings and notices
- Fix: Missing comments for translation strings that have placeholders in them
- Fix: Compatibility issues with WP Offload Media
- Fix: Timeout on Smush pages when there are more than 200k images
- Fix: Images incorrectly marked as requiring resmush
- Fix: Ignore link stuck in Grid Layout mode in the media library

= 3.12.6 ( 2023-03-09 ) =
- Enhance: Compatibility with WordPress 6.2.
- Fix: Upgrade modal reappears after closing

= 3.12.5 ( 2023-01-18 ) =
- Fix: CDN notice issue
- Fix: PHP 8.2 compatibility warnings
- Fix: Smush acting as free on staging

= 3.12.4 ( 2022-11-17 ) =
- Improvement: Code and compatibility improvements

= 3.12.3 ( 2022-10-24 ) =
- Fix: Free to pro upgrade issue

= 3.12.2 ( 2022-10-19 ) =
- Improvement: Security hardening
- Fix: Issues on older PHP versions

= 3.12.1 ( 2022-10-11 ) =
- Fix: PHP error on non-English language sites

= 3.12.0 ( 2022-10-11 ) =
- New: Bulk smush images in the background!
- New: Revised limits on bulk image optimization
- New: Better GDPR compliance by replacing Google fonts with Bunny fonts
- New: Filter on media library page to view media items with errors
- New: Option to receive an email once bulk smush is complete
- Fix: Some images incorrectly selected for resmush
- Fix: Database error while converting PNG to JPG on WordPress 6.1 Beta

= 3.11.1 ( 2022-08-19 ) =
- Fix: Fallback to sequential processing when parallel processing not possible

= 3.11.0 ( 2022-08-11 ) =
- New: Smush all image sizes in parallel for improved performance
- Improvement: Code improvements

= 3.10.3 ( 2022-07-14 ) =

- Enhance: CDN activation process
- Enhance: Improve media library image filters
- Enhance: CDN compatibility with Avada theme
- Enhance: Add notice about disabled folders in directory Smush module
- Fix: Errors table UI on bulk smush page
- Fix: Bulk smush UI when images have been removed when page was already loaded
- Fix: Cron job of logger library not running properly
- Fix: Remove tools meta box from dashboard page
- Fix: Recover image from NextGen Gallery button does not reset Smush data
- Fix: Image dimensions meta does not reset after restoring a resized image in NextGen Gallery
- Fix: Incorrect percent of total savings on Dashboard page
- Fix: Pro feature enable/disable toggles are clickable in free version

= 3.10.2 ( 2022-06-16 ) =

- Enhance: Data processing
- Enhance: Database calls
- Fix: Loading images from remote location

= 3.10.1 ( 2022-06-09 ) =

- Fix: PHP error on dash page

= 3.10.0 ( 2022-06-09 ) =

- New: Lossy compression is now free for all users
- New: Summary meta box
- Enhance: Move out image restore to bulk smush module
- Enhance: Move out image resize detection to settings module
- Enhance: Update opt-in notice design
- Fix: Upsell notice logic
- Fix: Skip image sizes not in WordPress format
- Fix: Skip onboarding wizard if a config has been applied
- Fix: Image sizes selector
- Fix: Rename "Basic" config to "Default"
- Fix: Do not show WebP notice in case of error
- Fix: Auto compression does not work for NextGen Gallery
- Fix: Settings link on Plugins page
- Fix: Welcome modal does not close when we go to the Bulk Smush page

= 3.9.11 ( 2022-05-23 ) =

- Enhance: Code quality
- Fix: Minor code quality issues
- Fix: Update internal libraries

= 3.9.10 ( 2022-05-17 ) =

- New: Gravity Forms integration
- New: `smush_background_images_regex` and `smush_images_from_content_regex` filters to adjust regex rules for finding images on the page
- New: `smush_cdn_before_process_background_src` filter to adjust background image src
- New: Add additional parameters to `smush_skip_adding_srcset` filter to allow disabling auto-resize for selected images
- Enhance: Compatibility with WPBakery page builder
- Enhance: Compatibility with Offload Media plugin
- Enhance: Handling images via REST API endpoints
- Fix: Directory Smush savings cached and not updated
- Fix: "What's new" modal not hiding if white label is enabled
- Fix: Missing primary key on smush_dir_images table
- Fix: Scaled images not being resized on resize settings change
- Fix: Bulk Smush will now show all errors, instead of just the first 5
- Fix: Animated GIF images not being excluded from bulk Smush
- Fix: Only allow network wide activation of Smush

= 3.9.9 ( 2022-05-03 ) =

- Enhance: Code quality
- Enhance: Handling user input
- Enhance: Update React modules to latest versions
- Fix: XSS vulnerability when uploading modified configs

= 3.9.8 ( 2022-03-22 ) =

- New: Add expiry header to Nginx template for WebP
- New: Add `wp_smush_webp_dir` filter to customize WebP directory
- Fix: XSS vulnerability
- Fix: Disable submit button on Integrations page when no integrations available
- Fix: CDN bandwidth limit status message
- Fix: Text alignment issue on Bulk Smush page
- Fix: Highlighting selected lazy load spinner with color accessibility
- Fix: Compatibility issue with WP Offload Media 2.6.0
- Fix: Undefined offset notice with certain WooCommerce themes

= 3.9.7 ( 2022-03-01 ) =

- Fix: Local WebP is not activated on applying config after reset settings
- Fix: Missing WebP file for WP scaled images
- Fix: Fatal error on PHP 5.6
- Fix: Compatibility issue with WP Offload Media

= 3.9.6 ( 2022-02-09 ) =

- Enhance: Do not close the Directory Smush modal in case of error
- Enhance: Tooltips in CDN module
- Fix: Compatibility issue with PHP 8.1
- Fix: "Choose Directory" button loading state in case of error
- Fix: PNG to JPG conversion leaves behind some thumbnails after deleting the image
- Fix: PNG images that need resizing don't get converted to JPG
- Fix: Issue creating the WebP test files
- Fix: Incorrect unique file names during PNG to JPG conversion
- Fix: Duplicate thumbnails while converting PNG to JPG
- Fix: Auto refresh API status when updating to Pro version

= 3.9.5 ( 2022-01-25 ) =

- Fix: Translation strings
- Fix: Remove upsells
- Enhance: Update admin menu icon

[Changelog for previous versions](https://wpmudev.com/project/wp-smush-pro/#view-changelog).

== About Us ==
WPMU DEV is a premium supplier of quality WordPress plugins, services and support. Learn more here:
[https://wpmudev.com/](https://wpmudev.com/?utm_source=wordpress.org&utm_medium=readme&utm_campaign=smush-readme&utm_content=wpmu_dev_link)

Don't forget to stay up to date on everything WordPress from the Internet's number one resource:
[WPMU DEV Blog](https://wpmudev.com/blog/?utm_source=wordpress.org&utm_medium=readme&utm_campaign=smush-readme&utm_content=wpmu_dev_blog_link)

Hey, one more thing... we hope you [enjoy our free offerings](http://profiles.wordpress.org/WPMUDEV/) as much as we've loved making them for you!

== Contact and Credits ==

Originally written by Alex Dunae at Dialect ([dialect.ca](http://dialect.ca/?wp_smush_it), e-mail 'alex' at 'dialect dot ca'), 2008-11.
