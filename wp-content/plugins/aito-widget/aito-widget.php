<?php

/* @wordpress-plugin
 * Plugin Name:       AITO Widget Plugin
 * Description:       Get the AITO widget on a schedule and cache it locally
 * Version:           0.1
 * Author:            <PERSON>
 * Author URI:        https://theuniversal.co/
 * Text Domain:       speed-tweaks
 * Domain Path:       /languages
 */

// Include the class file
require_once dirname(__FILE__) . '/includes/class-aito-widget.php';

// Create an instance of the class
$aito_widget = new AITO_Widget();

// Register the plugin's activation hook
register_activation_hook(__FILE__, array('AITO_Widget', 'activate'));

// Register the plugin's deactivation hook
register_deactivation_hook(__FILE__, 'aito_widget_deactivate');

// Deactivation function to clear scheduled events
function aito_widget_deactivate()
{
    // Clear the scheduled event
    wp_clear_scheduled_hook('aito_widget');
}