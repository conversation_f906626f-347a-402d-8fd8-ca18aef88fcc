<?php
/**
 * Importer for Code Snippets Pro.
 *
 * @package WPCode.
 */

/**
 * Class WPCode_Importer_Code_Snippets_Pro.
 */
class WPCode_Importer_Code_Snippets_Pro extends WPCode_Importer_Code_Snippets {

	/**
	 * The plugin name.
	 *
	 * @var string
	 */
	public $name = 'Code Snippets Pro';

	/**
	 * Importer slug.
	 *
	 * @var string
	 */
	public $slug = 'code-snippets-pro';

	/**
	 * Plugin path.
	 *
	 * @var string
	 */
	public $path = 'code-snippets-pro/code-snippets.php';
}
