<?php
/**
 * Handle all svg icons in one place.
 *
 * @package WPCode
 */

/**
 * Get an SVG icon by name with width, height and viewbox options.
 *
 * @param string $name The name of the icon.
 * @param int    $width The width.
 * @param int    $height The height.
 * @param string $viewbox The viewbox, will be auto-built from width and height if not set.
 * @param string $id The id of the icon.
 *
 * @return string
 */
function get_wpcode_icon( $name, $width = 20, $height = 20, $viewbox = '', $id = '' ) {
	$icons = wpcode_icons();

	if ( ! isset( $icons[ $name ] ) ) {
		return '';
	}

	if ( empty( $viewbox ) ) {
		$viewbox = sprintf( '0 0 %1$s %2$s', $width, $height );
	}

	return sprintf(
		'<svg class="wpcode-icon wpcode-icon-%5$s" width="%1$s" height="%2$s" viewBox="%3$s" fill="none" xmlns="http://www.w3.org/2000/svg" id="%6$s">%4$s</svg>',
		esc_attr( $width ),
		esc_attr( $height ),
		esc_attr( $viewbox ),
		$icons[ $name ],
		esc_attr( $name ),
		esc_attr( $id )
	);
}

/**
 * Echo an icon in a safe mode.
 *
 * @param string $name The name of the icon.
 * @param int    $width The width.
 * @param int    $height The height.
 * @param string $viewbox The viewbox, will be auto-built from width and height if not set.
 * @param string $id The id of the icon.
 *
 * @return void
 */
function wpcode_icon( $name, $width = 20, $height = 20, $viewbox = '', $id = '' ) {
	$icon = get_wpcode_icon( $name, $width, $height, $viewbox, $id );

	if ( ! empty( $icon ) ) {
		echo wp_kses(
			$icon,
			wpcode_get_icon_allowed_tags()
		);
	}
}

/**
 * Get the allowed tags for svg icons, to be used in wp_kses.
 *
 * @return array
 * @see wpcode_icon()
 * @see wp_kses()
 */
function wpcode_get_icon_allowed_tags() {
	return array(
		'svg'      => array(
			'class'           => true,
			'aria-hidden'     => true,
			'aria-labelledby' => true,
			'role'            => true,
			'xmlns'           => true,
			'width'           => true,
			'height'          => true,
			'viewbox'         => true,
			'id'              => true,
		),
		'g'        => array(
			'fill'      => true,
			'clip-path' => true,
		),
		'title'    => array( 'title' => true ),
		'path'     => array(
			'd'         => true,
			'fill'      => true,
			'fill-rule' => true,
			'clip-rule' => true,
			'data-name' => true,
		),
		'circle'   => array(
			'cx'           => true,
			'cy'           => true,
			'r'            => true,
			'stroke'       => true,
			'stroke-width' => true,
			'fill'         => true,
		),
		'rect'     => array(
			'x'      => true,
			'y'      => true,
			'width'  => true,
			'height' => true,
			'fill'   => true,
		),
		'clipPath' => array(
			'id' => true,
		),
		'defs'     => array(),
	);
}

/**
 * Get the whole array of WPCode SVG icons.
 *
 * @return array
 */
function wpcode_icons() {
	return (array) apply_filters(
		'wpcode_icons',
		array(
			'logo'        => '<path fill-rule="evenodd" clip-rule="evenodd" d="M57.5706 64H6.56732C2.89985 64 0 61.1064 0 57.4468V6.55319C0 2.89362 2.89985 0 6.56732 0H57.5706C61.2381 0 64.1379 2.89362 64.1379 6.55319V57.4468C64.1379 61.1064 61.2381 64 57.5706 64ZM15.863 52.0855C15.5219 52.0855 15.0954 52.0004 14.7543 51.9153C13.2191 51.3196 12.4515 49.6175 13.0485 48.0855L26.439 13.7877C27.036 12.2558 28.7418 11.4898 30.277 12.0855C31.8122 12.6813 32.5798 14.3834 31.9828 15.9153L18.6776 50.2132C18.2512 51.4047 17.0571 52.0855 15.863 52.0855ZM35.0534 47.7445C35.6504 48.3403 36.418 48.5956 37.1856 48.5956C37.9532 48.5956 38.7208 48.3403 39.3179 47.7445L49.8085 37.3616C51.6849 35.4892 51.6849 32.3403 49.8085 30.468L39.3179 19.9999C38.2091 18.8084 36.3327 18.8084 35.1386 19.9999C33.9446 21.1063 33.9446 22.9786 35.1386 24.1701L44.7764 33.8722L35.0534 43.5743C33.8593 44.6807 33.8593 46.5531 35.0534 47.7445Z" fill="white"/>',
			'logo-text'   => '<g clip-path="url(#clip0_529_8422)"><path d="M78.1833 16.4426C78.1833 13.2502 78.9284 11.0637 80.4184 9.4457C81.9085 7.82767 84.012 7.04053 86.7292 7.04053C88.0001 7.04053 89.2272 7.30291 90.279 7.82767C91.3746 8.35244 92.2073 9.05213 92.8646 9.92673C92.9961 10.1017 93.0399 10.2766 93.0399 10.4952C93.0399 10.7576 92.9523 10.9325 92.777 11.1075C92.6017 11.2824 92.4264 11.3698 92.1634 11.3698C91.8567 11.3698 91.6375 11.2386 91.4622 11.02C90.9802 10.364 90.3228 9.79554 89.4463 9.35824C88.6136 8.92093 87.6933 8.70228 86.6853 8.70228C84.5379 8.70228 82.9164 9.35824 81.777 10.6702C80.6375 11.9821 80.0678 13.775 80.0678 16.3988C80.0678 19.1539 80.6375 20.9905 81.7332 22.215C82.8288 23.4394 84.4941 24.0954 86.6853 24.0954C87.6495 24.0954 88.526 23.9205 89.4025 23.5269C90.2351 23.1333 90.9363 22.6086 91.4622 21.8651C91.6375 21.6465 91.8567 21.5153 92.1634 21.5153C92.4264 21.5153 92.6017 21.6028 92.777 21.7777C92.9523 21.9526 93.0399 22.1713 93.0399 22.3899C93.0399 22.5648 92.9961 22.7397 92.8646 22.9147C92.2511 23.7893 91.4184 24.489 90.279 25.0137C89.1395 25.5385 87.9563 25.8009 86.6853 25.8009C83.9244 25.8009 81.8208 25.0137 80.3746 23.4832C78.9284 21.9526 78.1833 19.7223 78.1833 16.4426Z" fill="#11293E"/><path d="M96.4582 24.183C95.4502 23.0897 94.9243 21.4279 94.9243 19.2414C94.9243 17.0549 95.4064 15.4369 96.4144 14.3436C97.4223 13.2504 98.8247 12.7256 100.665 12.7256C102.506 12.7256 103.908 13.2504 104.916 14.3436C105.924 15.4369 106.406 17.0549 106.406 19.2414C106.406 21.4279 105.924 23.0897 104.916 24.183C103.908 25.2762 102.506 25.8447 100.665 25.8447C98.8685 25.8447 97.4661 25.2762 96.4582 24.183ZM96.7211 19.2414C96.7211 22.5649 98.0359 24.2267 100.665 24.2267C103.295 24.2267 104.61 22.5649 104.61 19.2414C104.61 15.9179 103.295 14.2562 100.665 14.2562C98.0359 14.2562 96.7211 15.9616 96.7211 19.2414Z" fill="#11293E"/><path d="M108.072 19.285C108.072 14.912 109.912 12.9879 113.55 12.9879C114.383 12.9879 115.128 13.1628 115.785 13.5126C116.442 13.8625 116.968 14.256 117.275 14.7808V7.91514C117.275 7.65275 117.363 7.47783 117.538 7.30291C117.713 7.12799 117.889 7.04053 118.151 7.04053C118.414 7.04053 118.59 7.12799 118.765 7.30291C118.94 7.47783 119.028 7.65275 119.028 7.91514V20.4658C119.028 22.215 118.546 23.5269 117.538 24.4452C116.574 25.3636 115.259 25.8446 113.638 25.8446C111.841 25.8446 110.438 25.2761 109.474 24.1828C108.554 23.0896 108.072 21.4716 108.072 19.285ZM109.825 19.285C109.825 22.6086 111.096 24.2703 113.681 24.2703C114.777 24.2703 115.653 23.9642 116.311 23.3082C116.968 22.6523 117.275 21.7339 117.275 20.4658V16.9673C117.231 16.9236 117.187 16.8361 117.143 16.7049C117.1 16.5738 116.968 16.3988 116.749 16.1365C116.53 15.8741 116.311 15.6117 116.048 15.393C115.785 15.1744 115.434 14.9995 115.04 14.8245C114.602 14.6496 114.163 14.5622 113.681 14.5622C112.41 14.5622 111.49 14.9557 110.789 15.7866C110.132 16.6175 109.825 17.6233 109.825 19.285Z" fill="#11293E"/><path d="M121.131 19.2412C121.131 17.1421 121.614 15.5241 122.622 14.3871C123.629 13.2501 124.988 12.6816 126.785 12.6816C127.486 12.6816 128.143 12.7691 128.669 12.944C129.239 13.1189 129.721 13.3376 130.072 13.6437C130.422 13.9498 130.773 14.2559 131.036 14.6495C131.299 14.9994 131.518 15.3929 131.649 15.8302C131.781 16.2675 131.869 16.6174 131.956 16.9672C132.044 17.3171 132.044 17.6669 132.044 18.0168C132.044 18.2791 132.044 18.4978 132.044 18.629C132.044 18.7602 132 18.9351 131.912 19.1537C131.869 19.3724 131.737 19.5036 131.562 19.591C131.386 19.6785 131.211 19.7222 130.948 19.7222H122.972C122.972 21.0779 123.323 22.1711 124.068 22.9583C124.813 23.7454 125.821 24.1827 127.092 24.1827C127.793 24.1827 128.406 24.0953 128.888 23.8766C129.371 23.658 129.896 23.3956 130.378 23.002C130.554 22.8708 130.729 22.8271 130.861 22.8271C131.08 22.8271 131.255 22.9146 131.43 23.0458C131.562 23.2207 131.649 23.3956 131.649 23.6142C131.649 23.8329 131.562 24.0516 131.343 24.2265C131.167 24.4014 130.904 24.62 130.598 24.8387C130.291 25.0574 129.809 25.2323 129.151 25.4509C128.494 25.6696 127.793 25.757 127.048 25.757C125.163 25.757 123.717 25.2323 122.709 24.139C121.657 23.1332 121.131 21.4715 121.131 19.2412ZM122.928 18.2354H130.291C130.291 17.7106 130.247 17.2296 130.116 16.7923C130.028 16.355 129.853 15.9614 129.59 15.5678C129.327 15.1743 128.976 14.8682 128.494 14.6495C128.012 14.4309 127.442 14.2997 126.785 14.2997C125.602 14.2997 124.725 14.6495 124.024 15.3492C123.367 16.0489 122.972 17.011 122.928 18.2354Z" fill="#11293E"/><path d="M39.5299 8.61488C39.5299 8.17758 39.7052 7.784 40.012 7.47789C40.3188 7.17178 40.7132 6.99686 41.1514 6.99686C41.5458 6.99686 41.8964 7.12805 42.1594 7.3467C42.4223 7.56535 42.5976 7.82773 42.6853 8.09012L45.8845 19.5475L48.996 8.13385C49.0837 7.784 49.259 7.52162 49.5658 7.30297C49.8725 7.08432 50.1793 6.99686 50.4861 6.99686C50.8367 6.99686 51.1435 7.08432 51.4064 7.30297C51.6693 7.52162 51.8885 7.784 51.9761 8.13385L55.0877 19.5475L58.2869 8.04639C58.3745 7.784 58.5498 7.52162 58.8128 7.30297C59.0757 7.08432 59.3825 6.95312 59.8207 6.95312C60.259 6.95312 60.6096 7.08432 60.9164 7.39043C61.2231 7.69654 61.3546 8.04639 61.3546 8.48369C61.3546 8.65861 61.3108 8.83353 61.267 8.96472L56.5777 24.5765C56.4901 24.8826 56.3148 25.1887 56.008 25.3636C55.745 25.5823 55.4383 25.6698 55.0877 25.6698C54.7371 25.6698 54.4303 25.5823 54.1674 25.3636C53.9044 25.145 53.7291 24.8826 53.6415 24.5328L50.4861 13.3378L47.3307 24.6639C47.2431 25.0138 47.0239 25.2762 46.761 25.4948C46.498 25.7135 46.1474 25.8009 45.7968 25.8009C45.4462 25.8009 45.1395 25.7135 44.8765 25.4948C44.6136 25.2762 44.3944 25.0138 44.3068 24.7077L39.6176 9.09592C39.5737 8.96472 39.5299 8.7898 39.5299 8.61488Z" fill="#11293E"/><path d="M63.5897 24.2703V8.61482C63.5897 8.17752 63.765 7.82767 64.028 7.52156C64.3347 7.21545 64.7292 7.04053 65.1674 7.04053H70.3825C72.0917 7.04053 73.4503 7.52156 74.5021 8.48363C75.5539 9.4457 76.1236 10.8451 76.1236 12.638C76.1236 14.4747 75.5977 15.8303 74.5459 16.7924C73.4941 17.7545 72.0917 18.2355 70.4264 18.2355H66.7889V24.2703C66.7889 24.7076 66.6574 25.1012 66.3507 25.4073C66.0439 25.7134 65.6933 25.8446 65.2112 25.8446C64.7292 25.8446 64.3786 25.6697 64.0718 25.3636C63.7212 25.0575 63.5897 24.7076 63.5897 24.2703ZM66.7451 15.393H70.1196C70.9961 15.393 71.6535 15.1307 72.1355 14.6496C72.6176 14.1686 72.8367 13.4689 72.8367 12.5943C72.8367 11.7197 72.6176 11.02 72.1355 10.539C71.6535 10.0579 70.9961 9.83927 70.1634 9.83927H66.7889V15.393H66.7451Z" fill="#11293E"/><path fill-rule="evenodd" clip-rule="evenodd" d="M29.5817 32.8853H3.3745C1.49004 32.8853 0 31.3984 0 29.518V3.36724C0 1.48683 1.49004 0 3.3745 0H29.5817C31.4661 0 32.9562 1.48683 32.9562 3.36724V29.518C32.9562 31.3984 31.4661 32.8853 29.5817 32.8853ZM8.15142 26.7632C7.97612 26.7632 7.75699 26.7194 7.58169 26.6757C6.79285 26.3696 6.39843 25.495 6.7052 24.7078L13.5857 7.08447C13.8924 6.29732 14.7689 5.90375 15.5578 6.20986C16.3466 6.51597 16.7411 7.39058 16.4343 8.17773L9.59763 25.8011C9.37851 26.4133 8.76496 26.7632 8.15142 26.7632ZM18.0119 24.5327C18.3187 24.8388 18.7131 24.97 19.1075 24.97C19.502 24.97 19.8964 24.8388 20.2032 24.5327L25.5936 19.1976C26.5577 18.2355 26.5577 16.6175 25.5936 15.6554L20.2032 10.2766C19.6334 9.66433 18.6693 9.66433 18.0557 10.2766C17.4422 10.845 17.4422 11.8071 18.0557 12.4193L23.0079 17.4046L18.0119 22.3899C17.3984 22.9584 17.3984 23.9204 18.0119 24.5327Z" fill="#0065FF"/></g><defs><clipPath id="clip0_529_8422"><rect width="132" height="32.8853" fill="white"/></clipPath></defs>',
			'auto'        => '<path d="M9.36005 5.61394V8.56131L13.64 4.63148L9.36005 0.70166V3.64903C4.63065 3.64903 0.800049 7.16622 0.800049 11.5087C0.800049 13.0511 1.29225 14.4855 2.12685 15.6939L3.68905 14.2596C3.20755 13.4441 2.94005 12.501 2.94005 11.5087C2.94005 8.25675 5.81835 5.61394 9.36005 5.61394ZM16.5932 7.32341L15.031 8.7578C15.5018 9.58306 15.78 10.5164 15.78 11.5087C15.78 14.7606 12.9017 17.4034 9.36005 17.4034V14.456L5.08005 18.3859L9.36005 22.3157V19.3683C14.0894 19.3683 17.92 15.8511 17.92 11.5087C17.92 9.96622 17.4278 8.53183 16.5932 7.32341Z" fill="#454545"/>',
			'shortcode'   => '<path d="M0 0.137207H4.47458V1.89055H2.40664V14.2398H4.47458V16.0016H0V0.137207Z" fill="#454545"/><path d="M13.885 0.137207H16.2713L10.5019 16.0016H8.13574L13.885 0.137207Z" fill="#454545"/><path d="M24 0.137207H19.5254V1.89055H21.5934V14.2398H19.5254V16.0016H24V0.137207Z" fill="#454545"/>',
			'copy'        => '<path d="M10.8125 1.125H3.3125C2.625 1.125 2.0625 1.6875 2.0625 2.375V11.125H3.3125V2.375H10.8125V1.125ZM12.6875 3.625H5.8125C5.125 3.625 4.5625 4.1875 4.5625 4.875V13.625C4.5625 14.3125 5.125 14.875 5.8125 14.875H12.6875C13.375 14.875 13.9375 14.3125 13.9375 13.625V4.875C13.9375 4.1875 13.375 3.625 12.6875 3.625ZM12.6875 13.625H5.8125V4.875H12.6875V13.625Z" fill="#777777"/>',
			'check'       => '<path d="M5.8002 10.9L1.6002 6.70005L0.200195 8.10005L5.8002 13.7L17.8002 1.70005L16.4002 0.300049L5.8002 10.9Z" fill="#777777"/>',
			'remove'      => '<circle cx="10" cy="10" r="9" stroke="#777777" stroke-width="2"/><rect x="4.6156" y="9.23071" width="10.7692" height="1.53846" fill="#777777"/>',
			'search'      => '<path d="M11.1292 9.87907H10.4709L10.2375 9.65407C11.0542 8.70407 11.5459 7.47074 11.5459 6.12907C11.5459 3.1374 9.12086 0.712402 6.12919 0.712402C3.13752 0.712402 0.712524 3.1374 0.712524 6.12907C0.712524 9.12074 3.13752 11.5457 6.12919 11.5457C7.47086 11.5457 8.70419 11.0541 9.65419 10.2374L9.87919 10.4707V11.1291L14.0459 15.2874L15.2875 14.0457L11.1292 9.87907ZM6.12919 9.87907C4.05419 9.87907 2.37919 8.20407 2.37919 6.12907C2.37919 4.05407 4.05419 2.37907 6.12919 2.37907C8.20419 2.37907 9.87919 4.05407 9.87919 6.12907C9.87919 8.20407 8.20419 9.87907 6.12919 9.87907Z" fill="#BBBBBB"/>',
			'close'       => '<path d="M14.5649 1.41L13.1587 0L7.58348 5.59L2.00831 0L0.602051 1.41L6.17722 7L0.602051 12.59L2.00831 14L7.58348 8.41L13.1587 14L14.5649 12.59L8.98974 7L14.5649 1.41Z" fill="#8C8F9A"/>',
			'upload'      => '<path d="M10.5 8.25V10.5H1.5V8.25H0V10.5C0 11.325 0.675 12 1.5 12H10.5C11.325 12 12 11.325 12 10.5V8.25H10.5ZM2.25 3.75L3.3075 4.8075L5.25 2.8725V9H6.75V2.8725L8.6925 4.8075L9.75 3.75L6 0L2.25 3.75Z" fill="#777777"/>',
			'folder'      => '<path d="M10.2266 3.00016L12.8933 5.66683H24.6666V19.0002H3.33329V3.00016H10.2266ZM11.3333 0.333496H3.33329C1.86663 0.333496 0.679959 1.5335 0.679959 3.00016L0.666626 19.0002C0.666626 20.4668 1.86663 21.6668 3.33329 21.6668H24.6666C26.1333 21.6668 27.3333 20.4668 27.3333 19.0002V5.66683C27.3333 4.20016 26.1333 3.00016 24.6666 3.00016H14L11.3333 0.333496Z" fill="#777777"/>',
			'arrow'       => '<path d="M1.94006 0L0.0600586 1.88L6.16673 8L0.0600586 14.12L1.94006 16L9.94006 8L1.94006 0Z" fill="#777777"/>',
			'file-text'   => '<path d="M13.8333 2.16667V13.8333H2.16667V2.16667H13.8333ZM13.8333 0.5H2.16667C1.25 0.5 0.5 1.25 0.5 2.16667V13.8333C0.5 14.75 1.25 15.5 2.16667 15.5H13.8333C14.75 15.5 15.5 14.75 15.5 13.8333V2.16667C15.5 1.25 14.75 0.5 13.8333 0.5Z" fill="#DDDDDD"/><path d="M9.66667 12.1667H3.83333V10.5H9.66667V12.1667ZM12.1667 8.83333H3.83333V7.16667H12.1667V8.83333ZM12.1667 5.5H3.83333V3.83333H12.1667V5.5Z" fill="#DDDDDD"/>',
			'help'        => '<path fill-rule="evenodd" clip-rule="evenodd" d="M2.16667 9.99984C2.16667 5.39984 5.90001 1.6665 10.5 1.6665C15.1 1.6665 18.8333 5.39984 18.8333 9.99984C18.8333 14.5998 15.1 18.3332 10.5 18.3332C5.90001 18.3332 2.16667 14.5998 2.16667 9.99984ZM11.3333 13.3332V14.9998H9.66667V13.3332H11.3333ZM10.5 16.6665C6.82501 16.6665 3.83334 13.6748 3.83334 9.99984C3.83334 6.32484 6.82501 3.33317 10.5 3.33317C14.175 3.33317 17.1667 6.32484 17.1667 9.99984C17.1667 13.6748 14.175 16.6665 10.5 16.6665ZM7.16667 8.33317C7.16667 6.4915 8.65834 4.99984 10.5 4.99984C12.3417 4.99984 13.8333 6.4915 13.8333 8.33317C13.8333 9.40227 13.175 9.9776 12.534 10.5378C11.9259 11.0692 11.3333 11.587 11.3333 12.4998H9.66667C9.66667 10.9821 10.4518 10.3803 11.142 9.85123C11.6835 9.43618 12.1667 9.06585 12.1667 8.33317C12.1667 7.4165 11.4167 6.6665 10.5 6.6665C9.58334 6.6665 8.83334 7.4165 8.83334 8.33317H7.16667Z" fill="#777777"/>',
			'inbox'       => '<path fill-rule="evenodd" clip-rule="evenodd" d="M13.3333 0.5H1.66667C0.75 0.5 0 1.25 0 2.16667V13.8333C0 14.75 0.741667 15.5 1.66667 15.5H13.3333C14.25 15.5 15 14.75 15 13.8333V2.16667C15 1.25 14.25 0.5 13.3333 0.5ZM13.3333 13.8333H1.66667V11.3333H4.63333C5.20833 12.325 6.275 13 7.50833 13C8.74167 13 9.8 12.325 10.3833 11.3333H13.3333V13.8333ZM9.175 9.66667H13.3333V2.16667H1.66667V9.66667H5.84167C5.84167 10.5833 6.59167 11.3333 7.50833 11.3333C8.425 11.3333 9.175 10.5833 9.175 9.66667Z" fill="#777777"/>',
			'info'        => '<path d="M7.66667 4.33333H9.33334V6H7.66667V4.33333ZM7.66667 7.66666H9.33334V12.6667H7.66667V7.66666ZM8.50001 0.166664C3.90001 0.166664 0.166672 3.9 0.166672 8.5C0.166672 13.1 3.90001 16.8333 8.50001 16.8333C13.1 16.8333 16.8333 13.1 16.8333 8.5C16.8333 3.9 13.1 0.166664 8.50001 0.166664ZM8.50001 15.1667C4.82501 15.1667 1.83334 12.175 1.83334 8.5C1.83334 4.825 4.82501 1.83333 8.50001 1.83333C12.175 1.83333 15.1667 4.825 15.1667 8.5C15.1667 12.175 12.175 15.1667 8.50001 15.1667Z" fill="#EBAD35"/>',
			'success'     => '<path d="M8.50001 0.666656C3.90001 0.666656 0.166672 4.39999 0.166672 8.99999C0.166672 13.6 3.90001 17.3333 8.50001 17.3333C13.1 17.3333 16.8333 13.6 16.8333 8.99999C16.8333 4.39999 13.1 0.666656 8.50001 0.666656ZM8.50001 15.6667C4.82501 15.6667 1.83334 12.675 1.83334 8.99999C1.83334 5.32499 4.82501 2.33332 8.50001 2.33332C12.175 2.33332 15.1667 5.32499 15.1667 8.99999C15.1667 12.675 12.175 15.6667 8.50001 15.6667ZM12.325 5.31666L6.83334 10.8083L4.675 8.65832L3.50001 9.83332L6.83334 13.1667L13.5 6.49999L12.325 5.31666Z" fill="#09A347"/>',
			'warning'     => '<path d="M12.73 0H5.27L0 5.27V12.73L5.27 18H12.73L18 12.73V5.27L12.73 0ZM16 11.9L11.9 16H6.1L2 11.9V6.1L6.1 2H11.9L16 6.1V11.9ZM11.83 4.76L9 7.59L6.17 4.76L4.76 6.17L7.59 9L4.76 11.83L6.17 13.24L9 10.41L11.83 13.24L13.24 11.83L10.41 9L13.24 6.17L11.83 4.76Z" fill="#DF2A35"/>',
			'file'        => '<path d="M28 4H12C9.8 4 8.02 5.8 8.02 8L8 40C8 42.2 9.78 44 11.98 44H36C38.2 44 40 42.2 40 40V16L28 4ZM12 40V8H26V18H36V40H12Z" fill="#777777"/>',
			'support'     => '<path d="M24 4C12.96 4 4 12.96 4 24C4 35.04 12.96 44 24 44C35.04 44 44 35.04 44 24C44 12.96 35.04 4 24 4ZM38.92 18.24L33.36 20.54C32.34 17.82 30.2 15.66 27.46 14.66L29.76 9.1C33.96 10.7 37.3 14.04 38.92 18.24ZM24 30C20.68 30 18 27.32 18 24C18 20.68 20.68 18 24 18C27.32 18 30 20.68 30 24C30 27.32 27.32 30 24 30ZM18.26 9.08L20.6 14.64C17.84 15.64 15.66 17.82 14.64 20.58L9.08 18.26C10.7 14.04 14.04 10.7 18.26 9.08ZM9.08 29.74L14.64 27.44C15.66 30.2 17.82 32.36 20.58 33.36L18.24 38.92C14.04 37.3 10.7 33.96 9.08 29.74ZM29.76 38.92L27.46 33.36C30.2 32.34 32.36 30.18 33.36 27.42L38.92 29.76C37.3 33.96 33.96 37.3 29.76 38.92Z" fill="#777777"/>',
			'code'        => '<path d="M34 36 31.9 33.9 41.7 24 31.9 14.1 34 12 46 24ZM14 36 2 24 14 12 16.1 14.1 6.3 24 16.1 33.9ZM16 25.75Q15.3 25.75 14.775 25.225Q14.25 24.7 14.25 24Q14.25 23.3 14.775 22.775Q15.3 22.25 16 22.25Q16.7 22.25 17.225 22.775Q17.75 23.3 17.75 24Q17.75 24.7 17.225 25.225Q16.7 25.75 16 25.75ZM24 25.75Q23.3 25.75 22.775 25.225Q22.25 24.7 22.25 24Q22.25 23.3 22.775 22.775Q23.3 22.25 24 22.25Q24.7 22.25 25.225 22.775Q25.75 23.3 25.75 24Q25.75 24.7 25.225 25.225Q24.7 25.75 24 25.75ZM32 25.75Q31.3 25.75 30.775 25.225Q30.25 24.7 30.25 24Q30.25 23.3 30.775 22.775Q31.3 22.25 32 22.25Q32.7 22.25 33.225 22.775Q33.75 23.3 33.75 24Q33.75 24.7 33.225 25.225Q32.7 25.75 32 25.75Z"/>',
			'filter'      => '<path d="M28 26V38Q28 38.85 27.425 39.425Q26.85 40 26 40H22Q21.15 40 20.575 39.425Q20 38.85 20 38V26L8.05 10.75Q7.35 9.9 7.85 8.95Q8.35 8 9.4 8H38.6Q39.65 8 40.15 8.95Q40.65 9.9 39.95 10.75ZM24 26.2 36 11H12ZM24 26.2Z"/>',
			'split'       => '<path d="M22.5 44V34Q22.5 31.6 21.7 30.05Q20.9 28.5 19.25 26.85L21.4 24.7Q22.05 25.25 22.775 26.2Q23.5 27.15 24 27.95Q24.85 26.65 25.675 25.7Q26.5 24.75 27.25 24.1Q30.15 21.75 31.425 18.425Q32.7 15.1 32.4 9.7L27.9 14.2L25.8 12.1L33.9 4L42 12.1L39.9 14.2L35.4 9.7Q35.65 16 34.175 19.625Q32.7 23.25 29.25 26.4Q27.05 28.4 26.275 30.05Q25.5 31.7 25.5 34V44ZM12.9 16.2Q12.7 15.3 12.575 13.575Q12.45 11.85 12.55 9.75L8.1 14.2L6 12.1L14.1 4L22.2 12.1L20.1 14.2L15.6 9.7Q15.5 11.6 15.55 13.025Q15.6 14.45 15.8 15.5ZM17.1 24.75Q16.25 23.85 15.225 22.375Q14.2 20.9 13.65 19.15L16.6 18.4Q17.05 19.65 17.8 20.8Q18.55 21.95 19.2 22.65Z"/>',
			'terminal'    => '<path d="M7 40Q5.8 40 4.9 39.1Q4 38.2 4 37V11Q4 9.8 4.9 8.9Q5.8 8 7 8H41Q42.2 8 43.1 8.9Q44 9.8 44 11V37Q44 38.2 43.1 39.1Q42.2 40 41 40ZM7 37H41Q41 37 41 37Q41 37 41 37V15.2H7V37Q7 37 7 37Q7 37 7 37ZM24.5 33.6V30.6H35.5V33.6ZM15 33.4 12.9 31.3 18.05 26.1 12.85 20.9 15 18.8 22.3 26.1Z"/>',
			'error_badge' => '<path d="M22.5 24.6H25.5V14.25H22.5ZM24 31.3Q24.7 31.3 25.2 30.8Q25.7 30.3 25.7 29.6Q25.7 28.9 25.2 28.4Q24.7 27.9 24 27.9Q23.3 27.9 22.8 28.4Q22.3 28.9 22.3 29.6Q22.3 30.3 22.8 30.8Q23.3 31.3 24 31.3ZM24 43.95Q17 42.2 12.5 35.825Q8 29.45 8 21.85V9.95L24 3.95L40 9.95V21.85Q40 29.45 35.5 35.825Q31 42.2 24 43.95ZM24 24.55Q24 24.55 24 24.55Q24 24.55 24 24.55Q24 24.55 24 24.55Q24 24.55 24 24.55ZM24 40.85Q29.75 38.95 33.375 33.675Q37 28.4 37 21.85V12.05L24 7.15L11 12.05V21.85Q11 28.4 14.625 33.675Q18.25 38.95 24 40.85Z"/>',
			'php'         => '<path d="M19.2 31.25 21.35 29.1 16.3 24 21.35 18.95 19.2 16.8 12 24ZM28.8 31.25 36.05 24 28.85 16.8 26.7 18.95 31.75 24 26.65 29.1ZM9 39H39Q39 39 39 39Q39 39 39 39V9Q39 9 39 9Q39 9 39 9H9Q9 9 9 9Q9 9 9 9V39Q9 39 9 39Q9 39 9 39ZM9 9Q9 9 9 9Q9 9 9 9V39Q9 39 9 39Q9 39 9 39Q9 39 9 39Q9 39 9 39V9Q9 9 9 9Q9 9 9 9ZM9 42Q7.75 42 6.875 41.125Q6 40.25 6 39V9Q6 7.75 6.875 6.875Q7.75 6 9 6H19.25Q19.5 4.25 20.85 3.125Q22.2 2 24 2Q25.8 2 27.15 3.125Q28.5 4.25 28.75 6H39Q40.25 6 41.125 6.875Q42 7.75 42 9V39Q42 40.25 41.125 41.125Q40.25 42 39 42ZM24 8.15Q24.7 8.15 25.225 7.625Q25.75 7.1 25.75 6.4Q25.75 5.7 25.225 5.175Q24.7 4.65 24 4.65Q23.3 4.65 22.775 5.175Q22.25 5.7 22.25 6.4Q22.25 7.1 22.775 7.625Q23.3 8.15 24 8.15Z"/>',
			'trash'       => '<path d="M1.77365 13.8333C1.77365 14.75 2.48972 15.5 3.36491 15.5H9.72993C10.6051 15.5 11.3212 14.75 11.3212 13.8333V3.83333H1.77365V13.8333ZM3.36491 5.5H9.72993V13.8333H3.36491V5.5ZM9.33211 1.33333L8.53648 0.5H4.55835L3.76272 1.33333H0.978027V3H12.1168V1.33333H9.33211Z" fill="#777777"/>',
			'tags'        => '<path d="M18.4678 4.87133C20.5107 6.76412 20.5107 9.80688 18.4678 11.6997L14.0931 15.746C13.7298 16.0817 13.1361 16.0853 12.7689 15.7531C12.3978 15.421 12.3978 14.8781 12.7611 14.5389L17.132 10.4961C18.4561 9.27119 18.4561 7.29982 17.132 6.07486L12.1439 1.45966C11.7768 1.12288 11.7807 0.580398 12.1518 0.247552C12.5189 -0.0852947 13.1126 -0.0820805 13.4408 0.254694L18.4678 4.87133ZM1.17182e-05 7.0534V1.71429C1.17182e-05 0.767535 0.839412 5.96342e-05 1.87491 5.96342e-05H7.71447C8.3785 5.96342e-05 9.01128 0.240766 9.48001 0.669681L16.0422 6.6677C17.0187 7.56053 17.0187 9.01048 16.0422 9.90331L10.8315 14.6674C9.85499 15.5603 8.26913 15.5603 7.29261 14.6674L0.732386 8.66763C0.263386 8.23908 0 7.66052 0 7.0534H1.17182e-05ZM4.37479 2.85711C3.68459 2.85711 3.12485 3.36781 3.12485 3.99993C3.12485 4.63205 3.68459 5.14275 4.37479 5.14275C5.06616 5.14275 5.62473 4.63205 5.62473 3.99993C5.62473 3.36781 5.06616 2.85711 4.37479 2.85711Z" fill="#777777"/>',
			'eye'         => '<path d="M7.99996 1.53873C10.5266 1.53873 12.78 3.02603 13.88 5.37917C12.78 7.73231 10.5266 9.21961 7.99996 9.21961C5.47329 9.21961 3.21996 7.73231 2.11996 5.37917C3.21996 3.02603 5.47329 1.53873 7.99996 1.53873ZM7.99996 0.142212C4.66663 0.142212 1.81996 2.3138 0.666626 5.37917C1.81996 8.44454 4.66663 10.6161 7.99996 10.6161C11.3333 10.6161 14.18 8.44454 15.3333 5.37917C14.18 2.3138 11.3333 0.142212 7.99996 0.142212ZM7.99996 3.63352C8.91996 3.63352 9.66663 4.41557 9.66663 5.37917C9.66663 6.34277 8.91996 7.12482 7.99996 7.12482C7.07996 7.12482 6.33329 6.34277 6.33329 5.37917C6.33329 4.41557 7.07996 3.63352 7.99996 3.63352ZM7.99996 2.237C6.34663 2.237 4.99996 3.64748 4.99996 5.37917C4.99996 7.11086 6.34663 8.52135 7.99996 8.52135C9.65329 8.52135 11 7.11086 11 5.37917C11 3.64748 9.65329 2.237 7.99996 2.237Z" fill="#777777"/>',
			'arrow-left'  => '<path d="M3.99 5L16 5L16 3L3.99 3L3.99 1.04995e-06L-3.49691e-07 4L3.99 8L3.99 5Z" fill="#777777"/>',
			'rewind'      => '<path d="M9.125 0.25C5.3975 0.25 2.375 3.2725 2.375 7H0.125L3.125 9.9925L6.125 7H3.875C3.875 4.0975 6.2225 1.75 9.125 1.75C12.0275 1.75 14.375 4.0975 14.375 7C14.375 9.9025 12.0275 12.25 9.125 12.25C7.6775 12.25 6.365 11.6575 5.42 10.705L4.355 11.77C5.5775 12.9925 7.2575 13.75 9.125 13.75C12.8525 13.75 15.875 10.7275 15.875 7C15.875 3.2725 12.8525 0.25 9.125 0.25ZM8.375 4V7.75L11.5625 9.64L12.14 8.68L9.5 7.1125V4H8.375Z" fill="#777777"/>',
			'cloud'       => '<path d="M8 2.00008C9.74667 2.00008 11.2533 3.24008 11.5933 4.95341L11.7933 5.95341L12.8133 6.02675C13.8533 6.09341 14.6667 6.96675 14.6667 8.00008C14.6667 9.10008 13.7667 10.0001 12.6667 10.0001H4C2.52667 10.0001 1.33333 8.80675 1.33333 7.33341C1.33333 5.96675 2.35333 4.82675 3.70667 4.68675L4.42 4.61341L4.75333 3.98008C5.38667 2.76008 6.62667 2.00008 8 2.00008ZM8 0.666748C6.07333 0.666748 4.4 1.76008 3.56667 3.36008C1.56 3.57341 0 5.27341 0 7.33341C0 9.54008 1.79333 11.3334 4 11.3334H12.6667C14.5067 11.3334 16 9.84008 16 8.00008C16 6.24008 14.6333 4.81341 12.9 4.69341C12.4467 2.39341 10.4267 0.666748 8 0.666748Z" fill="#777777"/>',
			'lock'        => '<path d="M14.5 7.5H13.5V5.5C13.5 2.74 11.26 0.5 8.5 0.5C5.74 0.5 3.5 2.74 3.5 5.5V7.5H2.5C1.4 7.5 0.5 8.4 0.5 9.5V19.5C0.5 20.6 1.4 21.5 2.5 21.5H14.5C15.6 21.5 16.5 20.6 16.5 19.5V9.5C16.5 8.4 15.6 7.5 14.5 7.5ZM5.5 5.5C5.5 3.84 6.84 2.5 8.5 2.5C10.16 2.5 11.5 3.84 11.5 5.5V7.5H5.5V5.5ZM14.5 19.5H2.5V9.5H14.5V19.5ZM8.5 16.5C9.6 16.5 10.5 15.6 10.5 14.5C10.5 13.4 9.6 12.5 8.5 12.5C7.4 12.5 6.5 13.4 6.5 14.5C6.5 15.6 7.4 16.5 8.5 16.5Z" fill="white"/>',
			'exclamation' => '<defs><style>.b{fill:#231f20}</style></defs><g transform="translate(-.066)"><path d="M1.6 24a1.338 1.338 0 01-1.3-2.1L11 .9c.6-1.2 1.6-1.2 2.2 0l10.7 21c.6 1.2 0 2.1-1.3 2.1z" fill="#ffce31"/><path class="b" d="M10.3 8.6l1.1 7.4a.605.605 0 001.2 0l1.1-7.4a1.738 1.738 0 10-3.4 0z"/><circle class="b" cx="1.7" cy="1.7" r="1.7" transform="translate(10.3 17.3)"/></g>',
			'devices'     => '<path d="M4 40v-4.5h4.3V11q0-1.25.875-2.125T11.3 8h31v3h-31v24.5H23V40Zm23.95 0q-.85 0-1.4-.675Q26 38.65 26 37.8V15.95q0-.85.55-1.4.55-.55 1.4-.55h13.6q.95 0 1.7.525T44 15.95V37.8q0 .95-.725 1.575T41.55 40ZM29 35.5h12V17H29Z" fill="#777777"/>',
			'desktop'     => '<path d="M16.7 42v-3H21v-5H7q-1.2 0-2.1-.9Q4 32.2 4 31V9q0-1.2.9-2.1Q5.8 6 7 6h34q1.2 0 2.1.9.9.9.9 2.1v22q0 1.2-.9 2.1-.9.9-2.1.9H27v5h4.3v3ZM7 31h34V9H7v22Zm0 0V9v22Z" fill="#777777"/>',
			'mobile'      => '<path d="M13 46q-1.2 0-2.1-.9-.9-.9-.9-2.1V5q0-1.2.9-2.1.9-.9 2.1-.9h22q1.2 0 2.1.9.9.9.9 2.1v38q0 1.2-.9 2.1-.9.9-2.1.9Zm0-4.5V43h22v-1.5Zm0-3h22v-29H13Zm0-32h22V5H13Zm0 0V5v1.5Zm0 35V43Z" fill="#777777"/>',
			'scheduled'   => '<path d="m31.35 33.65 2.25-2.25-7.95-8V13.35h-3V24.6ZM24 44q-4.1 0-7.75-1.575-3.65-1.575-6.375-4.3-2.725-2.725-4.3-6.375Q4 28.1 4 24t1.575-7.75q1.575-3.65 4.3-6.375 2.725-2.725 6.375-4.3Q19.9 4 24 4t7.75 1.575q3.65 1.575 6.375 4.3 2.725 2.725 4.3 6.375Q44 19.9 44 24t-1.575 7.75q-1.575 3.65-4.3 6.375-2.725 2.725-6.375 4.3Q28.1 44 24 44Zm0-20Zm0 17q7 0 12-5t5-12q0-7-5-12T24 7q-7 0-12 5T7 24q0 7 5 12t12 5Z"/>',
			'plus'        => '<path d="M450 856V606H200v-60h250V296h60v250h250v60H510v250h-60Z" fill="#777777"/>',
			'robot'       => '<path d="M160-360q-50 0-85-35t-35-85q0-50 35-85t85-35v-80q0-33 23.5-56.5T240-760h120q0-50 35-85t85-35q50 0 85 35t35 85h120q33 0 56.5 23.5T800-680v80q50 0 85 35t35 85q0 50-35 85t-85 35v160q0 33-23.5 56.5T720-120H240q-33 0-56.5-23.5T160-200v-160Zm200-80q25 0 42.5-17.5T420-500q0-25-17.5-42.5T360-560q-25 0-42.5 17.5T300-500q0 25 17.5 42.5T360-440Zm240 0q25 0 42.5-17.5T660-500q0-25-17.5-42.5T600-560q-25 0-42.5 17.5T540-500q0 25 17.5 42.5T600-440ZM320-280h320v-80H320v80Zm-80 80h480v-480H240v480Zm240-240Z" fill="#777777" />',
			'aisparks'    => '<g id="star-2--reward-rating-rate-social-star-media-favorite-like-stars-spark"><path id="Union" class="cls-1" d="m21.9 12.1-3.38-7.67-3.38 7.67-7.67 3.38 7.67 3.38 3.38 7.67 3.38-7.67 7.67-3.38-7.67-3.38Z"/></g><g id="star-2--reward-rating-rate-social-star-media-favorite-like-stars-spark-2"><path id="Union-2" class="cls-1" d="M8.5 4.5 6.52 0 4.54 4.5.04 6.48l4.5 1.98 1.98 4.5 1.98-4.5L13 6.48 8.5 4.5Z"/></g><g id="star-2--reward-rating-rate-social-star-media-favorite-like-stars-spark-3"><path id="Union-3" class="cls-1" d="m5.91 24.09-1.38-3.14-1.38 3.14-3.14 1.38 3.14 1.38 1.38 3.14 1.38-3.14 3.14-1.38-3.14-1.38Z"/></g>',
			'logo-sr'     => '<path class="cls-1" d="M58,64H7c-3.7,0-6.6-2.9-6.6-6.6V6.6C.5,2.9,3.4,0,7,0h51c3.7,0,6.6,2.9,6.6,6.6v50.9c0,3.7-2.9,6.6-6.6,6.6ZM38.5,47.7c.6.6,1.4.9,2.1.9s1.5-.3,2.1-.9l10.5-10.4c1.9-1.9,1.9-5,0-6.9l-10.5-10.5c-1.1-1.2-3-1.2-4.2,0-1.2,1.1-1.2,3,0,4.2l9.6,9.7-9.7,9.7c-1.2,1.1-1.2,3,0,4.2h0ZM9.1,52c-.8,0-1.5-.3-2.1-.9-.6-.6-.9-1.3-.9-2.1s.3-1.5.9-2.1l7.1-7.1c.4-.4.5-.9.2-1.4-.2-.4-.4-.8-.5-1.3-.4-1.1-.6-2.4-.6-3.7,0-3.2,1.1-5.9,3.3-8.1,2.2-2.2,4.9-3.3,8.1-3.3s5.9,1.1,8.1,3.3c2.2,2.2,3.3,4.9,3.3,8.1s-1.1,5.9-3.3,8.1c-2.2,2.2-4.9,3.3-8.1,3.3s-2.5-.2-3.7-.6c-.4-.2-.9-.3-1.3-.5-.2,0-.3-.1-.5-.1-.3,0-.6.1-.9.4l-7.1,7.1c-.6.6-1.3.9-2.1.9ZM24.5,28.2c-1.6,0-2.8.5-3.9,1.6-1.1,1.1-1.6,2.3-1.6,3.9s.5,2.8,1.6,3.9c1.1,1.1,2.3,1.6,3.9,1.6s2.8-.5,3.9-1.6c1.1-1.1,1.6-2.3,1.6-3.9s-.5-2.8-1.6-3.9-2.3-1.6-3.9-1.6Z" fill="#0065FF" fill-rule="evenodd" stroke="0"/>',
			'step-1'      => '<path data-name="1" d="M47.48 67.35v-22.7h-8.8V39.3a16.779 16.779 0 003.57-.27 9.344 9.344 0 003.1-1.15 7.565 7.565 0 002.33-2.18 7.737 7.737 0 001.25-3.35h5.65v35h-7.1z" fill="#fff" fill-rule="evenodd"/>',
			'step-2'      => '<circle cx="50" cy="50" r="50" fill="#ccc"/><path data-name="2" d="M38.53 41.23a12.807 12.807 0 012.4-4.5 10.792 10.792 0 014.02-3 13.616 13.616 0 015.58-1.08 13.905 13.905 0 014.57.75 11.871 11.871 0 013.83 2.15A10.329 10.329 0 0161.55 39a10.589 10.589 0 01.98 4.6 11 11 0 01-.85 4.55 12.168 12.168 0 01-2.25 3.38 18.894 18.894 0 01-3.18 2.67c-1.18.8-2.37 1.59-3.57 2.38s-2.37 1.62-3.5 2.52a16.035 16.035 0 00-3 3.15h16.55v6.1h-25.7a14.681 14.681 0 01.87-5.3 14.2 14.2 0 012.38-4.02 20.5 20.5 0 013.52-3.28c1.35-1 2.78-2.01 4.28-3.05.77-.53 1.58-1.07 2.45-1.62a15.537 15.537 0 002.37-1.83A9.286 9.286 0 0054.7 47a5.5 5.5 0 00.73-2.85 5.006 5.006 0 00-5.25-5.4 4.569 4.569 0 00-2.63.73 5.345 5.345 0 00-1.72 1.9 9.023 9.023 0 00-.93 2.6 15.122 15.122 0 00-.27 2.82h-6.8a16.741 16.741 0 01.7-5.57z" fill="#fff" fill-rule="evenodd"/>',
		)
	);
}
