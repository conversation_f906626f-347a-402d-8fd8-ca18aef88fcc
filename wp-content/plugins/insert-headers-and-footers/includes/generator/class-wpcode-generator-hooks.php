<?php
/**
 * Generate a snippet for a hook.
 *
 * @package WPCode
 */

/**
 * WPCode_Generator_Hooks class.
 */
class WPCode_Generator_Hooks extends WPCode_Generator_Type {

	/**
	 * The generator slug.
	 *
	 * @var string
	 */
	public $name = 'hooks';

	/**
	 * The categories for this generator.
	 *
	 * @var string[]
	 */
	public $categories = array(
		'core',
	);

	/**
	 * Set the translatable strings.
	 *
	 * @return void
	 */
	protected function set_strings() {
		$this->title       = __( 'Hooks', 'insert-headers-and-footers' );
		$this->description = __( 'Generate a snippet for an action or a filter using any available hook.', 'insert-headers-and-footers' );
	}

	/**
	 * Return a list of hooks for suggestions in the hook autocomplete.
	 *
	 * @return array
	 */
	protected function get_all_hooks_options() {
		return array(
			'link_category',
			'link_title',
			'signup_another_blog_init',
			'signup_create_blog_meta',
			'add_signup_meta',
			'signup_user_init',
			'signup_blog_init',
			'signup_get_available_languages',
			'wpmu_active_signup',
			'wp_http_cookie_value',
			'customize_panel_active',
			'register_post_type_args',
			'http_origin',
			'allowed_http_origins',
			'allowed_http_origin',
			'wp_mail',
			'wp_mail_from',
			'wp_mail_from_name',
			'wp_mail_content_type',
			'wp_mail_charset',
			'authenticate',
			'auth_cookie',
			'auth_cookie_expiration',
			'secure_auth_cookie',
			'secure_logged_in_cookie',
			'secure_auth_redirect',
			'auth_redirect_scheme',
			'wp_redirect',
			'wp_redirect_status',
			'wp_safe_redirect_fallback',
			'allowed_redirect_hosts',
			'comment_notification_recipients',
			'comment_notification_notify_author',
			'comment_notification_text',
			'comment_notification_subject',
			'comment_notification_headers',
			'notify_moderator',
			'comment_moderation_recipients',
			'comment_moderation_text',
			'comment_moderation_subject',
			'comment_moderation_headers',
			'wp_password_change_notification_email',
			'wp_new_user_notification_email_admin',
			'wp_new_user_notification_email',
			'nonce_life',
			'nonce_user_logged_out',
			'nonce_user_logged_out',
			'salt',
			'salt',
			'check_password',
			'check_password',
			'random_password',
			'pre_get_avatar',
			'style_loader_tag',
			'print_styles_array',
			'style_loader_src',
			'graceful_fail',
			'wp_is_mobile',
			'wp_kses_allowed_html',
			'pre_kses',
			'wp_editor_settings',
			'the_editor_content',
			'quicktags_settings',
			'teeny_mce_plugins',
			'mce_external_plugins',
			'tiny_mce_plugins',
			'mce_external_languages',
			'mce_css',
			'teeny_mce_buttons',
			'mce_buttons',
			'mce_buttons_2',
			'mce_buttons_3',
			'mce_buttons_4',
			'teeny_mce_before_init',
			'tiny_mce_before_init',
			'wp_mce_translation',
			'wp_link_query_args',
			'wp_link_query',
			'use_curl_transport',
			'old_slug_redirect_post_id',
			'old_slug_redirect_url',
			'pre_do_shortcode_tag',
			'do_shortcode_tag',
			'strip_shortcodes_tagnames',
			'wp_http_ixr_client_headers',
			'session_token_manager',
			'attach_session_information',
			'session_token_manager',
			'get_attached_file',
			'update_attached_file',
			'_wp_relative_upload_path',
			'get_post_status',
			'wp_count_attachments',
			'post_mime_types',
			'pre_delete_post',
			'pre_trash_post',
			'pre_untrash_post',
			'wp_insert_post_parent',
			'wp_insert_attachment_data',
			'wp_insert_post_data',
			'wp_unique_post_slug',
			'add_ping',
			'get_enclosed',
			'get_pung',
			'get_to_ping',
			'get_page_uri',
			'wp_get_attachment_metadata',
			'wp_get_attachment_url',
			'wp_get_attachment_caption',
			'wp_get_attachment_thumb_file',
			'wp_get_attachment_thumb_url',
			'icon_dir',
			'icon_dir_uri',
			'icon_dirs',
			'wp_mime_type_icon',
			'get_lastpostdate',
			'pre_get_lastpostmodified',
			'get_lastpostmodified',
			'post_rewrite_rules',
			'date_rewrite_rules',
			'root_rewrite_rules',
			'comments_rewrite_rules',
			'search_rewrite_rules',
			'author_rewrite_rules',
			'page_rewrite_rules',
			'tag_rewrite_rules',
			'rewrite_rules_array',
			'mod_rewrite_rules',
			'rewrite_rules',
			'iis7_url_rewrite_rules',
			'customize_section_active',
			'oembed_providers',
			'pre_oembed_result',
			'oembed_result',
			'oembed_remote_get_args',
			'oembed_fetch_url',
			'oembed_dataparse',
			'rest_url_prefix',
			'rest_url',
			'wp_rest_server_class',
			'rest_avatar_sizes',
			'protected_title_format',
			'private_title_format',
			'the_title',
			'the_guid',
			'get_the_guid',
			'the_content',
			'the_content_more_link',
			'the_excerpt',
			'get_the_excerpt',
			'post_class',
			'body_class',
			'post_password_required',
			'wp_link_pages_args',
			'wp_link_pages_link',
			'wp_link_pages',
			'the_meta_key',
			'wp_dropdown_pages',
			'wp_list_pages_excludes',
			'wp_list_pages',
			'wp_page_menu_args',
			'wp_page_menu',
			'wp_get_attachment_link',
			'prepend_attachment',
			'the_password_form',
			'wp_post_revision_title_expanded',
			'customize_loaded_components',
			'customize_changeset_branching',
			'customize_save_response',
			'customize_changeset_save_data',
			'customize_dynamic_setting_args',
			'customize_dynamic_setting_class',
			'customize_allowed_urls',
			'customize_refresh_nonces',
			'customize_previewable_devices',
			'customize_load_themes',
			'user_has_cap',
			'get_date_sql',
			'core_version_check_locale',
			'core_version_check_query_args',
			'plugins_update_check_locales',
			'themes_update_check_locales',
			'wp_get_update_data',
			'link_category',
			'wp_list_bookmarks',
			'script_loader_src',
			'script_loader_tag',
			'print_scripts_array',
			'wp_admin_bar_class',
			'show_admin_bar',
			'can_add_user_to_blog',
			'is_email_address_unsafe',
			'wpmu_validate_user_signup',
			'minimum_site_name_length',
			'newblogname',
			'wpmu_validate_blog_signup',
			'signup_site_meta',
			'signup_user_meta',
			'newblog_notify_siteadmin',
			'newuser_notify_siteadmin',
			'domain_exists',
			'update_welcome_email',
			'update_welcome_subject',
			'update_welcome_user_email',
			'update_welcome_user_subject',
			'pre_get_space_used',
			'get_space_allowed',
			'wp_is_large_network',
			'subdirectory_reserved_names',
			'new_network_admin_email_content',
			'send_network_admin_email_change_email',
			'network_admin_email_change_email',
			'hook',
			'example_filter',
			'wpdocs_filter',
			'oembed_default_width',
			'oembed_request_post_id',
			'rest_oembed_ttl',
			'wp_feed_cache_transient_lifetime',
			'get_bloginfo_rss',
			'bloginfo_rss',
			'default_feed',
			'get_wp_title_rss',
			'wp_title_rss',
			'the_title_rss',
			'the_content_feed',
			'the_excerpt_rss',
			'the_permalink_rss',
			'comments_link_feed',
			'comment_link',
			'comment_author_rss',
			'comment_text_rss',
			'the_category_rss',
			'rss_enclosure',
			'atom_enclosure',
			'self_link',
			'feed_content_type',
			'wp_doing_ajax',
			'wp_doing_cron',
			'file_mod_allowed',
			'the_sites',
			'site_search_columns',
			'sites_clauses',
			'found_sites_query',
			'the_permalink',
			'user_trailingslashit',
			'pre_post_link',
			'post_link_category',
			'post_link',
			'post_type_link',
			'page_link',
			'_get_page_link',
			'attachment_link',
			'year_link',
			'month_link',
			'day_link',
			'the_feed_link',
			'feed_link',
			'post_comments_feed_link',
			'post_comments_feed_link_html',
			'author_feed_link',
			'category_feed_link',
			'tag_feed_link',
			'taxonomy_feed_link',
			'get_edit_tag_link',
			'edit_tag_link',
			'get_edit_term_link',
			'edit_term_link',
			'search_link',
			'search_feed_link',
			'post_type_archive_feed_link',
			'preview_post_link',
			'get_edit_post_link',
			'edit_post_link',
			'get_delete_post_link',
			'get_edit_comment_link',
			'edit_comment_link',
			'get_edit_bookmark_link',
			'edit_bookmark_link',
			'get_edit_user_link',
			'get_pagenum_link',
			'next_posts_link_attributes',
			'previous_posts_link_attributes',
			'navigation_markup_template',
			'get_comments_pagenum_link',
			'next_comments_link_attributes',
			'previous_comments_link_attributes',
			'home_url',
			'site_url',
			'admin_url',
			'includes_url',
			'content_url',
			'plugins_url',
			'network_site_url',
			'network_home_url',
			'network_admin_url',
			'user_admin_url',
			'self_admin_url',
			'set_url_scheme',
			'user_dashboard_url',
			'edit_profile_url',
			'get_canonical_url',
			'pre_get_shortlink',
			'get_shortlink',
			'the_shortlink',
			'pre_get_avatar_data',
			'get_avatar_comment_types',
			'get_avatar_url',
			'theme_file_uri',
			'parent_theme_file_uri',
			'theme_file_path',
			'parent_theme_file_path',
			'privacy_policy_url',
			'the_privacy_policy_link',
			'date_i18n',
			'number_format_i18n',
			'enclosure_links',
			'removable_query_args',
			'status_header',
			'nocache_headers',
			'robots_txt',
			'upload_dir',
			'wp_unique_filename',
			'wp_upload_bits',
			'wp_check_filetype_and_ext',
			'upload_mimes',
			'wp_die_ajax_handler',
			'wp_die_xmlrpc_handler',
			'wp_die_handler',
			'smilies',
			'iis7_supports_permalinks',
			'get_main_network_id',
			'global_terms_enabled',
			'kses_allowed_protocols',
			'wp_checkdate',
			'wp_auth_check_same_domain',
			'wp_delete_file',
			'admin_memory_limit',
			'image_memory_limit',
			'send_site_admin_email_change_email',
			'site_admin_email_change_email',
			'wp_privacy_anonymize_data',
			'wp_privacy_exports_dir',
			'wp_privacy_exports_url',
			'wp_privacy_export_expiration',
			'the_title',
			'the_title',
			'link_category',
			'the_content_rss',
			'icon_dir',
			'attachment_icon',
			'attachment_innerHTML',
			'the_title',
			'index_rel_link',
			'the_title',
			'parent_post_rel_link',
			'richedit_pre',
			'htmledit_pre',
			'shortcut_link',
			'rest_authentication_errors',
			'rest_send_nocache_headers',
			'rest_jsonp_enabled',
			'rest_post_dispatch',
			'rest_pre_serve_request',
			'rest_pre_echo_response',
			'rest_envelope_response',
			'rest_endpoints',
			'rest_pre_dispatch',
			'rest_request_before_callbacks',
			'rest_dispatch_request',
			'rest_request_after_callbacks',
			'rest_index',
			'rest_namespace_index',
			'rest_endpoints_description',
			'rest_route_data',
			'rest_request_parameter_order',
			'rest_request_from_url',
			'rest_comment_query',
			'rest_allow_anonymous_comments',
			'rest_pre_insert_comment',
			'rest_comment_trashable',
			'rest_prepare_comment',
			'rest_preprocess_comment',
			'rest_comment_collection_params',
			'rest_prepare_attachment',
			'rest_prepare_revision',
			'rest_user_query',
			'rest_prepare_user',
			'rest_pre_insert_user',
			'rest_user_collection_params',
			'rest_pre_get_setting',
			'rest_pre_update_setting',
			'rest_prepare_status',
			'rest_prepare_taxonomy',
			'rest_prepare_post_type',
			'rest_response_link_curies',
			'secure_signon_cookie',
			'wp_authenticate_user',
			'wp_authenticate_user',
			'check_is_user_spammed',
			'get_usernumposts',
			'pre_get_blogs_of_user',
			'get_blogs_of_user',
			'wp_dropdown_users_args',
			'wp_dropdown_users',
			'username_exists',
			'validate_username',
			'pre_user_login',
			'illegal_user_logins',
			'pre_user_nicename',
			'pre_user_url',
			'pre_user_email',
			'pre_user_nickname',
			'pre_user_first_name',
			'pre_user_last_name',
			'pre_user_display_name',
			'pre_user_description',
			'wp_pre_insert_user_data',
			'insert_user_meta',
			'send_password_change_email',
			'send_email_change_email',
			'password_change_email',
			'email_change_email',
			'user_contactmethods',
			'password_hint',
			'allow_password_reset',
			'password_reset_expiration',
			'password_reset_key_expired',
			'user_registration_email',
			'registration_errors',
			'determine_current_user',
			'new_user_email_content',
			'user_request_confirmed_email_to',
			'user_confirmed_action_email_content',
			'user_request_confirmed_email_subject',
			'user_erasure_fulfillment_email_to',
			'user_erasure_complete_email_subject',
			'user_confirmed_action_email_content',
			'user_request_action_confirmed_message',
			'user_request_action_description',
			'user_request_action_email_content',
			'user_request_action_email_subject',
			'user_request_key_expiration',
			'get_terms_defaults',
			'get_terms_args',
			'list_terms_exclusions',
			'get_terms_fields',
			'terms_clauses',
			'get_terms_orderby',
			'list_pages',
			'register_taxonomy_args',
			'get_categories_taxonomy',
			'get_tags',
			'the_comments',
			'comments_clauses',
			'found_comments_query',
			'get_the_categories',
			'the_category_list',
			'the_category',
			'list_cats',
			'list_cats',
			'wp_dropdown_cats',
			'wp_list_categories',
			'wp_tag_cloud',
			'tag_cloud_sort',
			'wp_generate_tag_cloud_data',
			'wp_generate_tag_cloud',
			'get_the_tags',
			'the_tags',
			'get_the_terms',
			'the_terms',
			'pre_get_main_site_id',
			'network_by_path_segments_count',
			'pre_get_network_by_path',
			'get_site',
			'get_network',
			'pre_http_send_through_proxy',
			'customize_partial_render',
			'customize_dynamic_partial_args',
			'customize_dynamic_partial_class',
			'customize_render_partials_response',
			'wp_query_search_exclusion_prefix',
			'wp_search_stopwords',
			'posts_search',
			'posts_search_orderby',
			'posts_where',
			'posts_join',
			'comment_feed_join',
			'comment_feed_where',
			'comment_feed_groupby',
			'comment_feed_orderby',
			'comment_feed_limits',
			'posts_where_paged',
			'posts_groupby',
			'posts_join_paged',
			'posts_orderby',
			'posts_distinct',
			'post_limits',
			'posts_fields',
			'posts_clauses',
			'posts_where_request',
			'posts_groupby_request',
			'posts_join_request',
			'posts_orderby_request',
			'posts_distinct_request',
			'posts_fields_request',
			'post_limits_request',
			'posts_clauses_request',
			'posts_request',
			'posts_pre_query',
			'split_the_query',
			'posts_request_ids',
			'posts_results',
			'comment_feed_orderby',
			'comment_feed_limits',
			'the_preview',
			'the_posts',
			'found_posts_query',
			'found_posts',
			'content_pagination',
			'embed_handler_html',
			'oembed_ttl',
			'embed_oembed_html',
			'embed_oembed_discover',
			'embed_maybe_make_link',
			'is_protected_meta',
			'register_meta_args',
			'customizer_widgets_section_args',
			'is_wide_widget_in_customizer',
			'widget_customizer_setting_args',
			'search_form_format',
			'get_search_form',
			'loginout',
			'loginout',
			'logout_url',
			'login_url',
			'register_url',
			'login_form_defaults',
			'login_form_top',
			'login_form_middle',
			'login_form_bottom',
			'lostpassword_url',
			'register',
			'bloginfo_url',
			'bloginfo',
			'get_site_icon_url',
			'get_custom_logo',
			'pre_get_document_title',
			'document_title_separator',
			'document_title_parts',
			'wp_title_parts',
			'wp_title',
			'single_post_title',
			'post_type_archive_title',
			'single_cat_title',
			'single_tag_title',
			'single_term_title',
			'get_the_archive_title',
			'get_the_archive_description',
			'get_the_post_type_description',
			'get_archives_link',
			'getarchives_where',
			'getarchives_join',
			'get_calendar',
			'get_calendar',
			'the_date',
			'get_the_date',
			'the_modified_date',
			'get_the_modified_date',
			'the_time',
			'get_the_time',
			'get_post_time',
			'the_modified_time',
			'get_the_modified_time',
			'get_post_modified_time',
			'the_weekday',
			'the_weekday_date',
			'site_icon_meta_tags',
			'wp_resource_hints',
			'user_can_richedit',
			'wp_default_editor',
			'wp_code_editor_settings',
			'get_search_query',
			'the_search_query',
			'language_attributes',
			'paginate_links',
			'wp_admin_css_uri',
			'wp_admin_css',
			'wp_generator_type',
			'the_generator',
			'redirect_canonical',
			'role_has_cap',
			'theme_templates',
			'theme_scandir_exclusions',
			'network_allowed_themes',
			'allowed_themes',
			'site_allowed_themes',
			'site_allowed_themes',
			'wp_http_accept_encoding',
			'',
			'incompatible_sql_modes',
			'query',
			'pre_get_table_charset',
			'pre_get_col_charset',
			'stylesheet',
			'stylesheet_directory',
			'stylesheet_directory_uri',
			'stylesheet_uri',
			'locale_stylesheet_uri',
			'template',
			'template_directory',
			'template_directory_uri',
			'theme_root',
			'theme_root_uri',
			'get_header_image_tag',
			'get_header_video_url',
			'header_video_settings',
			'is_header_video_active',
			'wp_get_custom_css',
			'update_custom_css_data',
			'editor_stylesheets',
			'get_theme_starter_content',
			'rss_update_period',
			'rss_update_frequency',
			'pre_cache_alloptions',
			'alloptions',
			'pre_update_option',
			'register_setting_args',
			'post_thumbnail_size',
			'post_thumbnail_html',
			'the_post_thumbnail_caption',
			'category_description',
			'category_css_class',
			'map_meta_cap',
			'editor_max_image_size',
			'get_image_tag_class',
			'get_image_tag',
			'wp_constrain_dimensions',
			'image_resize_dimensions',
			'image_get_intermediate_size',
			'intermediate_image_sizes',
			'wp_get_attachment_image_src',
			'wp_get_attachment_image_attributes',
			'wp_calculate_image_srcset_meta',
			'max_srcset_image_width',
			'wp_calculate_image_srcset',
			'wp_calculate_image_sizes',
			'img_caption_shortcode',
			'img_caption_shortcode_width',
			'post_gallery',
			'gallery_style',
			'post_playlist',
			'wp_mediaelement_fallback',
			'wp_audio_extensions',
			'wp_get_attachment_id3_keys',
			'wp_audio_shortcode_override',
			'wp_audio_shortcode_library',
			'wp_audio_shortcode_class',
			'wp_audio_shortcode',
			'wp_video_extensions',
			'wp_video_shortcode_override',
			'wp_video_shortcode_library',
			'wp_video_shortcode_class',
			'wp_video_shortcode',
			'upload_size_limit',
			'wp_image_editors',
			'plupload_default_settings',
			'plupload_default_params',
			'wp_prepare_attachment_for_js',
			'media_library_show_audio_playlist',
			'media_library_show_video_playlist',
			'media_library_months_with_files',
			'media_view_settings',
			'media_view_strings',
			'get_attached_media_args',
			'get_attached_media',
			'media_embedded_in_content_allowed_types',
			'get_post_galleries',
			'get_post_gallery',
			'attachment_url_to_postid',
			'site_details',
			'page_css_class',
			'page_menu_link_attributes',
			'xmlrpc_methods',
			'pre_option_enable_xmlrpc',
			'option_enable_xmlrpc',
			'xmlrpc_enabled',
			'xmlrpc_login_error',
			'xmlrpc_blog_options',
			'xmlrpc_prepare_taxonomy',
			'xmlrpc_prepare_term',
			'xmlrpc_prepare_post',
			'xmlrpc_prepare_post_type',
			'xmlrpc_prepare_media_item',
			'xmlrpc_prepare_page',
			'xmlrpc_prepare_comment',
			'xmlrpc_prepare_user',
			'xmlrpc_wp_insert_post_data',
			'xmlrpc_default_post_fields',
			'xmlrpc_default_taxonomy_fields',
			'xmlrpc_default_user_fields',
			'xmlrpc_allow_anonymous_comments',
			'xmlrpc_default_posttype_fields',
			'xmlrpc_default_revision_fields',
			'xmlrpc_text_filters',
			'pingback_ping_source_uri',
			'pre_remote_source',
			'xmlrpc_pingback_error',
			'url_to_postid',
			'wp_editor_set_quality',
			'jpeg_quality',
			'image_editor_default_mime_type',
			'post_format_rewrite_base',
			'get_term',
			'get_terms',
			'pre_category_nicename',
			'wp_get_object_terms_args',
			'get_object_terms',
			'wp_get_object_terms',
			'pre_insert_term',
			'wp_insert_term_data',
			'term_id_filter',
			'wp_unique_term_slug',
			'wp_update_term_parent',
			'wp_update_term_data',
			'term_id_filter',
			'pre_term_link',
			'tag_link',
			'category_link',
			'term_link',
			'https_local_ssl_verify',
			'https_ssl_verify',
			'use_streams_transport',
			'embed_defaults',
			'wp_audio_embed_handler',
			'wp_video_embed_handler',
			'wp_embed_handler_youtube',
			'wp_embed_handler_audio',
			'wp_embed_handler_video',
			'oembed_discovery_links',
			'post_embed_url',
			'oembed_endpoint_url',
			'embed_html',
			'oembed_response_data',
			'the_excerpt_embed',
			'embed_site_title_html',
			'xmlrpc_element_limit',
			'xmlrpc_chunk_parsing_size',
			'embed_thumbnail_id',
			'embed_thumbnail_image_size',
			'embed_thumbnail_image_shape',
			'http_headers_useragent',
			'http_request_args',
			'pre_http_request',
			'https_ssl_verify',
			'http_response',
			'http_api_transports',
			'http_response',
			'block_local_requests',
			'schedule_event',
			'schedule_event',
			'cron_schedules',
			'run_wptexturize',
			'no_texturize_tags',
			'no_texturize_shortcodes',
			'sanitize_file_name_chars',
			'sanitize_file_name',
			'sanitize_user',
			'sanitize_key',
			'sanitize_title',
			'sanitize_html_class',
			'format_to_edit',
			'smilies_src',
			'is_email',
			'sanitize_email',
			'human_time_diff',
			'excerpt_length',
			'excerpt_more',
			'wp_trim_excerpt',
			'wp_trim_words',
			'pre_ent2ncr',
			'format_for_editor',
			'clean_url',
			'js_escape',
			'esc_html',
			'attribute_escape',
			'esc_textarea',
			'tag_escape',
			'wp_parse_str',
			'wp_sprintf',
			'sanitize_text_field',
			'sanitize_textarea_field',
			'sanitize_mime_type',
			'sanitize_trackback_urls',
			'wp_spaces_regexp',
			'process_text_diff_html',
			'user_search_columns',
			'found_users_query',
			'wp_get_nav_menu_object',
			'has_nav_menu',
			'wp_get_nav_menu_name',
			'wp_get_nav_menus',
			'wp_get_nav_menu_items',
			'nav_menu_attr_title',
			'nav_menu_description',
			'wp_setup_nav_menu_item',
			'_wp_post_revision_fields',
			'wp_save_post_revision_post_has_changed',
			'wp_revisions_to_keep',
			'ms_site_check',
			'site_by_path_segments_count',
			'pre_get_site_by_path',
			'the_author',
			'the_modified_author',
			'the_author_posts_link',
			'author_link',
			'is_multi_author',
			'get_comment_author',
			'comment_author',
			'get_comment_author_email',
			'author_email',
			'comment_email',
			'get_comment_author_link',
			'get_comment_author_IP',
			'get_comment_author_url',
			'comment_url',
			'get_comment_author_url_link',
			'comment_class',
			'get_comment_date',
			'comment_excerpt_length',
			'get_comment_excerpt',
			'comment_excerpt',
			'get_comment_ID',
			'get_comment_link',
			'get_comments_link',
			'get_comments_number',
			'comments_number',
			'get_comment_text',
			'comment_text',
			'get_comment_time',
			'get_comment_type',
			'trackback_url',
			'comments_open',
			'pings_open',
			'comments_template_query_args',
			'comments_array',
			'comments_template',
			'respond_link',
			'comments_popup_link_attributes',
			'comment_reply_link_args',
			'comment_reply_link',
			'post_comments_link',
			'cancel_comment_reply_link',
			'comment_id_fields',
			'wp_list_comments_args',
			'comment_form_default_fields',
			'comment_form_defaults',
			'comment_form_logged_in',
			'comment_form_fields',
			'comment_form_field_comment',
			'comment_form_submit_button',
			'comment_form_submit_field',
			'get_bookmarks',
			'dynamic_sidebar_params',
			'dynamic_sidebar_has_widgets',
			'is_active_sidebar',
			'sidebars_widgets',
			'customize_control_active',
			'locale',
			'locale',
			'gettext',
			'gettext_with_context',
			'ngettext',
			'ngettext_with_context',
			'override_load_textdomain',
			'load_textdomain_mofile',
			'override_unload_textdomain',
			'plugin_locale',
			'plugin_locale',
			'theme_locale',
			'get_available_languages',
			'the_networks',
			'networks_clauses',
			'found_networks_query',
			'query_vars',
			'request',
			'wp_headers',
			'query_string',
			'widget_categories_dropdown_args',
			'widget_categories_args',
			'widget_title',
			'widget_links_args',
			'widget_nav_menu_args',
			'widget_text',
			'widget_text_content',
			'widget_custom_html_content',
			'comment_max_links_url',
			'get_comment',
			'get_default_comment_status',
			'comment_cookie_lifetime',
			'pre_comment_author_name',
			'pre_comment_author_email',
			'pre_comment_author_url',
			'duplicate_comment_id',
			'pre_comment_approved',
			'comment_flood_filter',
			'get_page_of_comment',
			'wp_get_comment_fields_max_lengths',
			'wp_count_comments',
			'wp_get_current_commenter',
			'pre_user_id',
			'pre_comment_user_agent',
			'pre_comment_content',
			'pre_comment_user_ip',
			'preprocess_comment',
			'notify_post_author',
			'comment_save_pre',
			'wp_update_comment_data',
			'pre_wp_update_comment_count_now',
			'pingback_useragent',
			'close_comments_for_post_types',
			'close_comments_for_post_types',
			'wp_anonymize_comment',
			'customize_nav_menu_available_items',
			'customize_nav_menu_searched_items',
			'customize_nav_menu_available_item_types',
			'wp_nav_menu_args',
			'pre_wp_nav_menu',
			'wp_nav_menu_container_allowedtags',
			'wp_nav_menu_objects',
			'wp_nav_menu_items',
			'wp_nav_menu',
			'nav_menu_submenu_css_class',
			'nav_menu_item_args',
			'nav_menu_css_class',
			'nav_menu_item_id',
			'nav_menu_link_attributes',
			'nav_menu_item_title',
			'walker_nav_menu_start_el',
			'get_meta_sql',
			'meta_query_find_compatible_table_alias',
			'widget_display_callback',
			'widget_update_callback',
			'widget_form_callback',
			'wp_xmlrpc_server_class',
			'comment_post_redirect',
			'edit_comment_misc_actions',
			'myblogs_options',
			'myblogs_blog_actions',
			'wp_nav_locations_listed_per_menu',
			'redirect_term_location',
			'taxonomy_parent_dropdown_args',
			'whitelist_options',
			'redirect_user_admin_request',
			'delete_site_email_content',
			'admin_title',
			'admin_body_class',
			'parent_file',
			'submenu_file',
			'export_args',
			'bulk_post_updated_messages',
			'user_profile_picture_description',
			'post_updated_messages',
			'enter_title_here',
			'tables_to_repair',
			'thread_comments_depth_max',
			'avatar_defaults',
			'default_avatar_select',
			'redirect_network_admin_request',
			'mu_menu_items',
			'media_upload_default_type',
			'media_upload_default_tab',
			'pre_user_login',
			'date_formats',
			'time_formats',
			'admin_footer_text',
			'update_footer',
			'install_themes_tabs',
			'available_permalink_structure_tags',
			'editable_slug',
			'wp_header_image_attachment_metadata',
			'theme_action_links',
			'theme_row_meta',
			'heartbeat_nopriv_received',
			'heartbeat_nopriv_send',
			'term_search_min_chars',
			'wp_check_post_lock_window',
			'ajax_query_attachments_args',
			'wp_refresh_nonces',
			'heartbeat_received',
			'heartbeat_send',
			'wp_ajax_cropped_attachment_metadata',
			'wp_ajax_cropped_attachment_id',
			'wp_privacy_personal_data_exporters',
			'wp_privacy_personal_data_export_page',
			'wp_privacy_personal_data_erasers',
			'wp_privacy_personal_data_erasure_page',
			'intermediate_image_sizes_advanced',
			'attachment_thumbnail_args',
			'fallback_intermediate_image_sizes',
			'wp_generate_attachment_metadata',
			'wp_read_image_metadata',
			'file_is_displayable_image',
			'load_image_to_edit',
			'load_image_to_edit_filesystempath',
			'load_image_to_edit_attachmenturl',
			'load_image_to_edit_path',
			'wpmu_users_columns',
			'ms_user_list_site_actions',
			'ms_user_row_actions',
			'default_content',
			'default_title',
			'default_excerpt',
			'edit_posts_per_page',
			'upload_per_page',
			'get_sample_permalink',
			'get_sample_permalink_html',
			'admin_post_thumbnail_size',
			'admin_post_thumbnail_html',
			'override_post_lock',
			'redirect_post_location',
			'default_hidden_columns',
			'hidden_columns',
			'default_hidden_meta_boxes',
			'hidden_meta_boxes',
			'months_dropdown_results',
			'list_table_primary_column',
			'update_right_now_text',
			'install_themes_nonmenu_tabs',
			'theme_install_actions',
			'plugin_files_exclusions',
			'translations_api',
			'translations_api_result',
			'add_menu_classes',
			'menu_order',
			'manage_pages_columns',
			'manage_posts_columns',
			'post_date_column_status',
			'post_date_column_time',
			'page_row_actions',
			'post_row_actions',
			'quick_edit_dropdown_pages_args',
			'all_plugins',
			'show_network_active_plugins',
			'network_admin_plugin_action_links',
			'plugin_action_links',
			'plugin_row_meta',
			'install_plugin_complete_actions',
			'wp_terms_checklist_args',
			'wp_comment_reply',
			'postmeta_form_keys',
			'postmeta_form_limit',
			'import_upload_size_limit',
			'display_post_states',
			'display_media_states',
			'export_wp_filename',
			'the_content_export',
			'the_excerpt_export',
			'update_bulk_plugins_complete_actions',
			'get_editable_authors',
			'get_others_drafts',
			'wp_create_thumbnail',
			'site_icon_attachment_metadata',
			'site_icon_image_sizes',
			'site_icon_image_sizes',
			'update_feedback',
			'editable_roles',
			'get_users_drafts',
			'post_types_to_delete_with_user',
			'manage_taxonomies_for_attachment_columns',
			'manage_media_columns',
			'media_row_actions',
			'update_bulk_theme_complete_actions',
			'install_theme_complete_actions',
			'update_theme_complete_actions',
			'install_plugins_tabs',
			'install_plugins_nonmenu_tabs',
			'plugin_install_action_links',
			'post_edit_category_parent_dropdown_args',
			'page_attributes_dropdown_pages_args',
			'default_page_template_title',
			'users_list_table_query_args',
			'user_row_actions',
			'manage_users_custom_column',
			'get_role_list',
			'comments_per_page',
			'comment_status_links',
			'term_updated_messages',
			'contextual_help_list',
			'contextual_help',
			'default_contextual_help',
			'screen_layout_columns',
			'screen_settings',
			'screen_options_show_screen',
			'screen_options_show_submit',
			'view_mode_post_types',
			'edit_tags_per_page',
			'tagsperpage',
			'edit_categories_per_page',
			'term_name',
			'tag_row_actions',
			'automatic_updater_disabled',
			'automatic_updates_is_vcs_checkout',
			'auto_core_update_email',
			'automatic_updates_debug_email',
			'async_update_translation',
			'plugins_api_args',
			'plugins_api',
			'plugins_api_result',
			'upgrader_pre_download',
			'upgrader_pre_install',
			'upgrader_source_selection',
			'upgrader_clear_destination',
			'upgrader_post_install',
			'upgrader_package_options',
			'themes_api_args',
			'themes_api',
			'themes_api_result',
			'pre_prepare_themes_for_js',
			'wp_prepare_themes_for_js',
			'dbdelta_queries',
			'dbdelta_create_queries',
			'dbdelta_insert_queries',
			'wp_should_upgrade_global_tables',
			'ms_sites_list_table_query_args',
			'wpmu_blogs_columns',
			'manage_sites_action_links',
			'wp_network_dashboard_widgets',
			'wp_user_dashboard_widgets',
			'wp_dashboard_widgets',
			'dashboard_glance_items',
			'privacy_on_link_title',
			'privacy_on_link_text',
			'dashboard_recent_drafts_query_args',
			'comment_row_actions',
			'dashboard_recent_posts_query_args',
			'browse-happy-notice',
			'try_gutenberg_learn_more_link',
			'allow_minor_auto_core_updates',
			'allow_major_auto_core_updates',
			'media_upload_tabs',
			'image_send_to_editor',
			'image_add_caption_text',
			'image_add_caption_shortcode',
			'media_buttons_context',
			'attachment_fields_to_save',
			'media_send_to_editor',
			'image_send_to_editor_url',
			'attachment_fields_to_edit',
			'get_media_item_args',
			'media_meta',
			'get_media_item_args',
			'attachment_fields_to_edit',
			'upload_post_params',
			'plupload_init',
			'media_upload_form_url',
			'media_upload_form_url',
			'type_url_form_media',
			'media_upload_form_url',
			'media_upload_form_url',
			'media_upload_mime_type_links',
			'media_meta',
			'media_submitbox_misc_sections',
			'audio_submitbox_misc_sections',
			'wp_read_video_metadata',
			'got_rewrite',
			'got_url_rewrite',
			'documentation_ignore_functions',
			'set-screen-option',
			'admin_referrer_policy',
			'new_admin_email_content',
			'wp_get_default_privacy_policy_content',
			'terms_to_edit',
			'populate_network_meta',
			'update_plugin_complete_actions',
			'update_translations_complete_actions',
			'nav_menu_meta_box_object',
			'wp_edit_nav_menu_walker',
			'revision_text_diff_options',
			'wp_get_revision_ui_diff',
			'wp_prepare_revision_for_js',
			'wpmu_drop_tables',
			'wpmu_delete_blog_upload_dir',
			'lang_codes',
			'mu_dropdown_languages',
			'can_edit_network',
			'comment_edit_pre',
			'editable_extensions',
			'wp_theme_editor_filetypes',
			'pre_move_uploaded_file',
			'filesystem_method_file',
			'filesystem_method',
			'request_filesystem_credentials',
			'fs_ftp_connection_types',
			'wp_privacy_personal_data_email_content',
			'image_editor_save_pre',
			'image_save_pre',
			'wp_save_image_editor_file',
			'wp_save_image_file',
			'wp_image_editor_before_change',
			'image_edit_before_change',
			'comment_edit_redirect',
			'shake_error_codes',
			'login_title',
			'login_headerurl',
			'login_headertitle',
			'login_body_class',
			'login_message',
			'login_errors',
			'login_messages',
			'retrieve_password_title',
			'retrieve_password_message',
			'login_link_separator',
			'post_password_expires',
			'logout_redirect',
			'lostpassword_redirect',
			'wp_signup_location',
			'registration_redirect',
			'login_redirect',
			'wp_login_errors',
			'wp_mail_original_content',
			'phone_content',
		);
	}

	/**
	 * Load the generator tabs.
	 *
	 * @return void
	 */
	protected function load_tabs() {
		$this->tabs = array(
			'info' => array(
				'label'   => __( 'Info', 'insert-headers-and-footers' ),
				'columns' => array(
					// Column 1.
					array(
						// Column 1 fields.
						array(
							'type'    => 'description',
							'label'   => __( 'Overview', 'insert-headers-and-footers' ),
							'content' => sprintf(
							// Translators: Placeholders add links to the wordpress.org references.
								__( 'Using this generator you can safely add custom %1$shooks%2$s using either %3$sadd_action%4$s or %5$sadd_filter%6$s.', 'insert-headers-and-footers' ),
								'<a href="https://developer.wordpress.org/reference/hooks/" target="_blank">',
								'</a>',
								'<a href="https://developer.wordpress.org/reference/functions/add_action/" target="_blank">',
								'</a>',
								'<a href="https://developer.wordpress.org/reference/functions/add_filter/" target="_blank">',
								'</a>'
							),
						),
					),
					// Column 2.
					array(
						// Column 2 fields.
						array(
							'type'    => 'list',
							'label'   => __( 'Usage', 'insert-headers-and-footers' ),
							'content' => array(
								__( 'Fill in the forms using the menu on the left.', 'insert-headers-and-footers' ),
								__( 'Click the "Update Code" button.', 'insert-headers-and-footers' ),
								__( 'Click on "Use Snippet" to create a new snippet with the generated code.', 'insert-headers-and-footers' ),
								__( 'Activate and save the snippet and you\'re ready to go', 'insert-headers-and-footers' ),
							),
						),
					),
					// Column 3.
					array(
						// Column 3 fields.
						array(
							'type'    => 'description',
							'label'   => __( 'Examples', 'insert-headers-and-footers' ),
							'content' => __( 'You can use this to quickly get started with adding an action or filter.', 'insert-headers-and-footers' ),
						),
					),
				),
			),
			'hook' => array(
				'label'   => __( 'Hook Details', 'insert-headers-and-footers' ),
				'columns' => array(
					// Column 1.
					array(
						array(
							'type'        => 'select',
							'label'       => __( 'Hook type', 'insert-headers-and-footers' ),
							'description' => __( 'Can be either an action or a filter', 'insert-headers-and-footers' ),
							'id'          => 'hook_type',
							'default'     => 'add_action',
							'options'     => array(
								'add_action' => __( 'Action', 'insert-headers-and-footers' ),
								'add_filter' => __( 'Filter', 'insert-headers-and-footers' ),
							),
						),
						array(
							'type'         => 'text',
							'label'        => __( 'Hook name', 'insert-headers-and-footers' ),
							'description'  => __( 'Input hook name or pick one from the suggested list as you type.', 'insert-headers-and-footers' ),
							'id'           => 'hook_name',
							'default'      => '',
							'autocomplete' => $this->get_all_hooks_options(),
						),
					),
					// Column 2.
					array(
						array(
							'type'        => 'text',
							'label'       => __( 'Callback function', 'insert-headers-and-footers' ),
							'description' => __( 'Name of the function you want to add to this hook.', 'insert-headers-and-footers' ),
							'id'          => 'callback',
							'default'     => 'custom_function_' . time(),
						),
						array(
							'type'        => 'text',
							'label'       => __( 'Priority', 'insert-headers-and-footers' ),
							'description' => __( 'Priority of this hook, by default 10.', 'insert-headers-and-footers' ),
							'id'          => 'priority',
							'default'     => '10',
						),
					),
					// Column 3.
					array(
						array(
							'type'        => 'text',
							'label'       => __( 'Arguments list', 'insert-headers-and-footers' ),
							'description' => __( 'Add comma-separated custom arguments that will be passed to the callback function depending on the action/filter.', 'insert-headers-and-footers' ),
							'id'          => 'arguments',
							'default'     => '',
						),
					),
				),
			),
			'code' => array(
				'label'   => __( 'Code', 'insert-headers-and-footers' ),
				'columns' => array(
					// Column 1.
					array(
						array(
							'type'        => 'textarea',
							'label'       => __( 'PHP Code', 'insert-headers-and-footers' ),
							'description' => __( 'Custom PHP code you want to run in the function, you can also edit this after you create the snippet.', 'insert-headers-and-footers' ),
							'id'          => 'code',
							'code'        => true,
						),
					),
				),
			),
		);
	}

	/**
	 * Get the snippet code with dynamic values applied.
	 *
	 * @return string
	 */
	public function get_snippet_code() {

		$arguments = $this->get_value( 'arguments' );

		$arguments_array = explode( ',', $arguments );
		$arguments_array = array_map( 'trim', $arguments_array );
		$count           = count( $arguments_array );
		if ( $count > 1 ) {
			$count = ", $count";
		} else {
			$count = '';
		}
		$arguments_formatted = implode( ', ', $arguments_array );
		$priority            = intval( $this->get_value( 'priority' ) );
		$callback            = str_replace( '-', '_', sanitize_title_with_dashes( $this->get_value( 'callback' ) ) );

		return <<<EOD
// Add a hook.
function $callback( $arguments_formatted ) {
{$this->get_value( 'code' )}
}
{$this->get_value( 'hook_type' )}( '{$this->get_value( 'hook_name' )}', '$callback', {$priority}$count );
EOD;
	}

}
