!function(){var e={233:function(){(window.WPCodeAdminCodeEditor||function(e,t,n){const o={l18n:wpcode,init(){t.WPCodeAdminCodeEditor=o},switch_code_mode(e,t){const n=o.get_editor(e);if(void 0===n)return!1;n.setOption("mode",o.get_mime_for_code_type(t)),n.setOption("lint",o.get_lint_for_code_type(t)),jQuery(n.getTextArea()).closest(".wpcode-code-textarea").attr("data-code-type",t)},get_editor(e){if("undefined"!=typeof wpcode_editor)return wpcode_editor[e]?wpcode_editor[e].codemirror:void 0},set_value(e,t){const n=o.get_editor(e);if(void 0===n)return!1;n.getDoc().setValue(t)},get_mime_for_code_type:e=>o.l18n.code_type_options[e].mime,get_lint_for_code_type:e=>o.l18n.code_type_options[e].lint,refresh(e){"undefined"!=typeof wpcode_editor&&o.get_editor(e).refresh()},get_value:e=>void 0===o.get_editor(e)?n("#"+e).val():o.get_editor(e).getValue(),refresh_all(){"undefined"!=typeof wpcode_editor&&n.each(wpcode_editor,(function(e,t){t.codemirror.refresh()}))}};return o}(document,window,jQuery)).init()},560:function(){(window.WPCodeSnippetsTable||function(e,t,n){const o={l10n:wpcode,init:function(){o.should_init()&&(o.init_status_toggle(),o.move_screen_options(),o.init_copy())},should_init:function(){return null!==e.getElementById("wpcode-code-snippets-table")},init_status_toggle:function(){n(".wpcode-status-toggle").on("change",(function(){const e=n(this),t=e.is(":checked"),i=e.data("id");o.update_status(t,i).fail((function(){e.prop("checked",!1)})).done((function(n){!1===n.success&&(e.prop("checked",!t),n.data.message&&WPCodeAdminNotices.add_notice(n.data.message,"error"))})).fail((function(e){e.responseText&&WPCodeAdminNotices.add_notice(e.responseText,"error")}))}))},update_status:function(e,t){return n.post(ajaxurl,{_wpnonce:o.l10n.nonce,action:"wpcode_update_snippet_status",snippet_id:t,active:e,multisite:o.l10n.multisite})},move_screen_options:function(){n("#screen-meta-links, #screen-meta").prependTo("#wpcode-header-between").show()},init_copy:function(){n(".wpcode-copy").on("click",(function(e){e.preventDefault();const t=n(this).data("copy-value"),o=n(this);t&&(navigator.clipboard.writeText(t),o.addClass("wpcode-show-success-icon"),setTimeout((function(){o.removeClass("wpcode-show-success-icon")}),500))}))}};return o}(document,window,jQuery)).init()},786:function(){(window.WPCodeAdminGenerator||function(e,t,n){const o={doing_ajax_call:!1,ajax_snippet_update:!1,editor_id:"wpcode_generator_code_preview",init:function(){o.should_init()&&(o.find_elements(),o.init_generator_form(),o.init_code_editor(),o.init_tabs(),o.init_use_snippet(),o.init_copy_editor(),o.init_repeater(),o.do_spacer(),n(e).ready((function(){o.init_autocomplete()})))},should_init:()=>(o.generator_form=n("#wpcode_generator_form"),o.generator_form.length>0),find_elements(){o.tabs_buttons=n(".wpcode-items-tabs"),o.tabs_content=n(".wpcode-items-list .wpcode-form-tab"),o.use_snippet=n("#wpcode-generator-use-snippet"),o.update_button=n("#wpcode-generator-update-code"),o.repeater_row=n("#wpcode-generator-repeater-row").html()},init_generator_form(){o.generator_form.on("submit",(function(e){e.preventDefault(),o.update_snippet()})),o.generator_form.on("change","input, select",(function(){o.update_snippet()})),o.update_snippet()},update_snippet(){o.doing_ajax_call||(o.ajax_snippet_update&&o.ajax_snippet_update.abort(),o.show_button_spinner(o.update_button),o.ajax_snippet_update=n.post(ajaxurl,n(o.generator_form).serialize()).done((function(e){o.ajax_snippet_update=!1,WPCodeAdminCodeEditor.set_value(o.editor_id,e),o.hide_button_spinner(o.update_button)})))},init_tabs(){o.tabs_buttons.on("click","button",(function(e){e.preventDefault(),o.switch_active_tab(n(this))}))},switch_active_tab(e){o.tabs_buttons.find("button").removeClass("wpcode-active"),e.addClass("wpcode-active");const t=e.data("category");o.tabs_content.hide(),o.tabs_content.filter((function(){return n(this).data("tab")===t})).show(),o.do_spacer(),WPCodeAdminCodeEditor.refresh(o.editor_id)},init_use_snippet(){o.use_snippet.on("click",(function(e){if(e.preventDefault(),o.doing_ajax_call)return;o.doing_ajax_call=!0;const i=o.generator_form.serializeArray(),a=n(this);n.each(i,(function(e,t){"action"===t.name&&(i[e].value="wpcode_save_generated_snippet")})),o.show_button_spinner(a),n.post(ajaxurl,n.param(i)).done((function(e){o.doing_ajax_call=!1,o.hide_button_spinner(a),e.success&&e.data.url&&(t.location=e.data.url)}))}))},show_button_spinner(e){t.WPCodeSpinner.show_button_spinner(e)},hide_button_spinner(e){t.WPCodeSpinner.hide_button_spinner(e)},init_copy_editor:function(){n(".wpcode-copy-target").on("click",(function(e){e.preventDefault();const t=n(this),i=WPCodeAdminCodeEditor.get_value(o.editor_id);i&&(navigator.clipboard.writeText(i),t.addClass("wpcode-show-success-icon"),setTimeout((function(){t.removeClass("wpcode-show-success-icon")}),500))}))},init_repeater(){o.row_id=0,o.tabs_content.on("click",".wpcode-repeater-button",(function(){const e=n(this).data("target"),t=n(n('.wpcode-generator-column > [data-repeater="'+e+'"]').get().reverse());let i,a,r=0;o.row_id++,t.each((function(){const e=n(this).closest(".wpcode-generator-column");e.is(i)||(r++,i=e,a=n(o.repeater_row),r>1?a.find("button").remove():a.find("button").data("target",o.row_id),a.attr("data-id",o.row_id),e.append(a)),n(this).clone().attr("data-repeater",null).prependTo(a).find("input").val(null)}));let s=0,l=n('.wpcode-repeater-group[data-id="'+o.row_id+'"]');l.each((function(){const e=n(this).height();e>s&&(s=e)})),l.height(s),l.first().find("input").first().focus()})),o.tabs_content.on("click",".wpcode-remove-row",(function(){const e=n(this).data("target");n('.wpcode-repeater-group[data-id="'+e+'"]').remove()}))},do_spacer(){n(".wpcode-generator-field-spacer").each((function(){const e=n(this).closest(".wpcode-generator-column"),t=n(this).closest(".wpcode-generator-column").outerHeight();let o=0;e.siblings(".wpcode-generator-column").each((function(){const e=n(this).height();e>o&&(o=e)})),o>t&&n(this).height(o-t+3)}))},init_autocomplete(){n(".wpcode-generator-field-autocomplete").each((function(){const e=n(this).find('input[type="text"]'),t=n(this).find(".wpcode-field-autocomplete").text();e.autocomplete({source:JSON.parse(t)})}))},init_code_editor(){const e=n(".wpcode-generator-code");if(0===e.length)return;const t=wp.codeEditor.initialize(e);o.CodeMirror=t.codemirror,o.CodeMirror.setOption("readOnly",!1),o.CodeMirror.on("change",(function(e){clearTimeout(o.editor_change_handler),o.editor_change_handler=setTimeout((function(){jQuery(e.getTextArea()).val(e.getValue()).change(),o.update_snippet()}),300)}))}};return o}(document,window,jQuery)).init()},448:function(){(window.WPCodeHeader||function(e,t,n){const o={init(){o.should_init()&&n(e).ready((function(){o.init_sticky_header()}))},should_init:()=>n("#wpcode_snippet_code").length>0||n("#ihaf_insert_header").length>0,init_sticky_header(){const e=n(".wpcode-header-bottom"),o=e.height(),i=e.offset().top,a=e.parent();n(t).scroll((function(){const r=n(t).scrollTop();i<r+32?(e.addClass("wpcode-sticky"),a.css("paddingBottom",o+"px")):(e.removeClass("wpcode-sticky"),a.css("paddingBottom",0))}))}};return o}(document,window,jQuery)).init()},847:function(){(window.WPCodeHelp||function(e,t,n){const o={init:function(){o.should_init()&&(o.find_elements(),o.init_show(),o.init_close_button(),o.init_search(),o.init_accordion())},should_init:()=>(o.$overlay=n("#wpcode-docs-overlay"),o.$overlay.length>0),find_elements(){o.$close_button=n("#wpcode-help-close"),o.$search=n("#wpcode-help-search"),o.$no_result=n("#wpcode-help-no-result"),o.$search_results=n("#wpcode-help-result ul"),o.$categories=n("#wpcode-help-categories")},init_close_button(){o.$close_button.on("click",(function(e){e.preventDefault(),o.$overlay.fadeOut(200)}))},init_show(){n(e).on("click",".wpcode-show-help",(function(e){e.preventDefault(),o.$overlay.fadeIn(200)}))},init_accordion(){o.$categories.on("click",".wpcode-help-category header",(function(){const e=n(this).closest(".wpcode-help-category");o.toggle_category(e)})),o.$categories.on("click",".viewall",(function(e){e.preventDefault(),n(this).closest(".wpcode-help-docs").find("div").slideDown(),n(this).hide()}))},toggle_category(e){e.toggleClass("open"),e.find(".wpcode-help-docs").slideToggle()},init_search(){o.$search.on("keyup","input",o.input_search),o.$search.on("click","#wpcode-help-search-clear",o.clear_search)},input_search(){o.$search_results.html("");const e=n(this).val().toLowerCase(),t=n("#wpcode-help-categories .wpcode-help-docs li").filter((function(){return n(this).text().toLowerCase().indexOf(""+e)>-1}));e.length>2&&t.clone().appendTo(o.$search_results),0===t.length?o.$no_result.show():o.$no_result.hide(),o.$search.toggleClass("wpcode-search-empty",!e)},clear_search(){o.$search.find("input").val("").trigger("keyup")}};return o}(document,window,jQuery)).init()},298:function(){(window.WPCodeInputs||function(e,t,n){const o={init(){n(o.ready)},ready(){o.initFileUploads(),o.initCheckboxMultiselectColumns()},initFileUploads(){n(".wpcode-file-upload").each((function(){const e=n(this).find("input[type=file]"),t=n(this).find("label"),o=t.html();e.on("change",(function(e){let n="";this.files&&this.files.length>1?n=(this.getAttribute("data-multiple-caption")||"").replace("{count}",this.files.length):e.target.value&&(n=e.target.value.split("\\").pop()),n?t.find(".wpcode-file-field").html(n):t.html(o)})),e.on("focus",(function(){e.addClass("has-focus")})).on("blur",(function(){e.removeClass("has-focus")}))}))},initCheckboxMultiselectColumns(){n(e).on("change",".wpcode-checkbox-multiselect-columns input",(function(){var e=n(this),t=e.parent(),o=e.closest(".wpcode-checkbox-multiselect-columns"),i=t.text(),a="check-item-"+e.val(),r=o.find("#"+a);e.prop("checked")?(e.parent().addClass("checked"),r.length||o.find(".second-column ul").append('<li id="'+a+'">'+i+"</li>")):(e.parent().removeClass("checked"),o.find("#"+a).remove())})),n(e).on("click",".wpcode-checkbox-multiselect-columns .all",(function(e){e.preventDefault(),n(this).closest(".wpcode-checkbox-multiselect-columns").find("input[type=checkbox]").prop("checked",!0).trigger("change")}))}};return o}(document,window,jQuery)).init()},900:function(){window.WPCodeItemsList||function(e,t,n){const o=function(e){this.container=n(e),this.category="*",this.search_term="",this.categories_list=this.container.find(".wpcode-items-filters"),this.search_input=this.container.find(".wpcode-items-search-input"),this.items=this.container.find(".wpcode-list-item"),this.banner=null,this.init()};o.prototype={init:function(){this.should_init()&&(this.init_category_switch(),this.init_search(),this.show_connect_banner(),this.init_custom_event_handlers())},init_custom_event_handlers(){this.container.on("wpcode_reset_items",(()=>{this.reset_items()})),this.container.on("wpcode_select_item",((e,t)=>{this.set_item(t)}))},set_item(e){this.reset_items();const t=this.items.filter((function(){return n(this).data("id")===e}));this.items.removeClass("wpcode-list-item-selected"),t.addClass("wpcode-list-item-selected");const o=t.data("categories"),i=o.length>0?o[0]:"*";this.switch_to_category(i);const a=this.categories_list.find(`button[data-category="${i}"]`);this.switch_category_button(a);const r=t.find('input[type="radio"]');r.length>0&&r.prop("checked",!0)},reset_items(){this.search_input.val(""),this.search_term="";const e=this.categories_list.find("button").first();this.switch_to_category(e.data("category")),this.switch_category_button(e)},should_init:function(){return this.categories_list.length>0},init_category_switch:function(){const e=this;this.categories_list.on("click","button",(function(){const t=n(this);t.hasClass("wpcode-active")||(e.switch_to_category(t.data("category")),e.switch_category_button(t))}))},switch_category_button:function(e){this.categories_list.find("button").removeClass("wpcode-active"),e.addClass("wpcode-active")},switch_to_category:function(e){this.category=e,this.filter_items()},filter_items:function(){let e;const t=this,o=this.items.filter((function(){return"*"===t.category||n(this).data("categories").indexOf(t.category)>-1}));if(t.search_term.length>2){const o=t.search_term.toLowerCase();e=this.items.filter((function(){return n(this).text().toLowerCase().indexOf(o)>-1}))}else e=o;t.items.hide(),e.show(),this.update_banner_position()},init_search:function(){const e=this;this.search_input.on("keyup change search",(function(){const t=n(this).val();e.search_term=t.length<3?"":t,e.filter_items()}))},show_connect_banner:function(){const e=n("#tmpl-wpcode-library-connect-banner");if(!e.length)return;const t=this.container.find(".wpcode-items-list-category .wpcode-list-item:visible");t.length>5?t.eq(5).after(e.html()):t.last().after(e.html()),this.banner=this.container.find("#wpcode-library-connect-banner")},update_banner_position:function(){const e=this.container.find(".wpcode-items-list-category .wpcode-list-item:visible");this.banner&&this.banner.length>0&&(e.length>5?this.banner.insertAfter(e.eq(5)):this.banner.insertAfter(e.last()))}},n(e).ready((function(){n(".wpcode-items-metabox").each((function(){new o(this)}))}))}(document,window,jQuery)},423:function(){(window.WPCodeAdminLibrary||function(e,t,n){const o={l10n:wpcode,init:function(){o.should_init()&&(o.find_elements(),o.init_preview(),o.init_ai_button())},should_init:()=>n(".wpcode-library-preview-button").length>0,find_elements(){o.library_list=n(".wpcode-items-list"),o.code_preview_use=n("#wpcode-preview-use-code"),o.code_preview_edit=n("#wpcode-preview-edit-snippet"),o.code_preview_updated=n("#wpcode-preview-updated"),o.code_preview_title=n("#wpcode-preview-title")},init_preview(){o.library_list.on("click",".wpcode-library-preview-button",(function(e){e.preventDefault();const t=n(this).parent().find(".wpcode-item-use-button"),i=n(this).closest(".wpcode-list-item").data("id");o.show_code_preview(i,t.attr("href")),o.code_preview_use.text(t.text())})),n(".wpcode-close-modal, .wpcode-modal-overlay").on("click",(function(){n("body").removeClass("wpcode-show-modal")}))},show_code_preview(e,t){const i=o.l10n.library.snippets.find((t=>t.library_id===e));WPCodeAdminCodeEditor.switch_code_mode("wpcode-code-preview",i.code_type),WPCodeAdminCodeEditor.set_value("wpcode-code-preview",i.code),o.code_preview_use.attr("href",t),o.code_preview_title.text(i.title),o.code_preview_edit.length>0&&o.code_preview_edit.attr("href",o.l10n.cloud_edit_url+e),o.code_preview_updated&&o.code_preview_updated.text(i.updated_text),n("body").addClass("wpcode-show-modal"),WPCodeAdminCodeEditor.refresh("wpcode-code-preview")},init_ai_button(){n(".wpcode-library-item-ai-not-available").on("click",(function(e){e.preventDefault(),e.stopPropagation(),WPCodeAdminNotices.show_pro_notice(o.l10n.ai_title,o.l10n.ai_text,o.l10n.ai_url,o.l10n.ai_button)}))}};return o}(document,window,jQuery)).init()},770:function(){(window.WPCodeAdminNotifications||function(e,t,n){const o={init(){o.should_init()&&(o.find_elements(),o.init_open(),o.init_close(),o.init_dismiss(),o.init_view_switch(),o.update_count(o.active_count))},should_init:()=>(o.$drawer=n("#wpcode-notifications-drawer"),o.$drawer.length>0),find_elements(){o.$open_button=n("#wpcode-notifications-button"),o.$count=o.$drawer.find("#wpcode-notifications-count"),o.$dismissed_count=o.$drawer.find("#wpcode-notifications-dismissed-count"),o.active_count=o.$open_button.data("count")?o.$open_button.data("count"):0,o.dismissed_count=o.$open_button.data("dismissed"),o.$body=n("body"),o.$dismissed_button=n("#wpcode-notifications-show-dismissed"),o.$active_button=n("#wpcode-notifications-show-active"),o.$active_list=n(".wpcode-notifications-list .wpcode-notifications-active"),o.$dismissed_list=n(".wpcode-notifications-list .wpcode-notifications-dismissed"),o.$dismiss_all=n("#wpcode-dismiss-all")},update_count(e){o.$open_button.data("count",e).attr("data-count",e),0===e&&o.$open_button.removeAttr("data-count"),o.$count.text(e),o.dismissed_count+=Math.abs(e-o.active_count),o.active_count=e,o.$dismissed_count.text(o.dismissed_count),0===o.active_count&&o.$dismiss_all.hide()},init_open(){o.$open_button.on("click",(function(e){e.preventDefault(),o.$body.addClass("wpcode-notifications-open")}))},init_close(){o.$body.on("click",".wpcode-notifications-close, .wpcode-notifications-overlay",(function(e){e.preventDefault(),o.$body.removeClass("wpcode-notifications-open")}))},init_dismiss(){o.$drawer.on("click",".wpcode-notification-dismiss",(function(e){e.preventDefault();const t=n(this).data("id");if(o.dismiss_notification(t),"all"===t)return o.move_to_dismissed(o.$active_list.find("li")),void o.update_count(0);o.move_to_dismissed(n(this).closest("li")),o.update_count(o.active_count-1)}))},move_to_dismissed(e){e.slideUp((function(){n(this).prependTo(o.$dismissed_list).show()}))},dismiss_notification:e=>n.post(ajaxurl,{action:"wpcode_notification_dismiss",nonce:wpcode.nonce,id:e}),init_view_switch(){o.$dismissed_button.on("click",(function(e){e.preventDefault(),o.$drawer.addClass("show-dismissed")})),o.$active_button.on("click",(function(e){e.preventDefault(),o.$drawer.removeClass("show-dismissed")}))}};return o}(document,window,jQuery)).init()},670:function(){(window.WPCodeAdminSettings||function(e,t,n){const o={init:function(){o.should_init()&&(o.init_auto_height_toggle(),o.init_select2())},should_init:()=>n("body").hasClass("wpcode-settings"),init_auto_height_toggle(){const e=n("#editor_height_auto"),t=n("#wpcode-editor-height");e.on("change",(function(){n(this).is(":checked")?t.prop("disabled",!0):t.prop("disabled",!1)}))},init_select2(){n(".wpcode-select2").selectWoo()}};return o}(document,window,jQuery)).init()},928:function(){jQuery((function(e){const t=e(".wpcode-smart-tags");let n;function o(){n.removeClass("wpcode-smart-tags-open"),e(document).off("click.wpcode")}t.on("click",".wpcode-smart-tags-toggle",(function(t){t.preventDefault();const i=e(this).closest(".wpcode-smart-tags");i.toggleClass("wpcode-smart-tags-open"),n=i,e(document).on("click.wpcode",(function(t){e(t.target).closest(".wpcode-smart-tags").length||o()}))})),t.on("wpcode_close_smart_tags_dropdown",o),"undefined"!=typeof WPCodeSnippetManager&&t.on("click",".wpcode-insert-smart-tag",(function(t){const n=e(this).closest(".wpcode-smart-tags-dropdown"),o=n.data("upgrade-title");if(o)return t.preventDefault(),t.stopImmediatePropagation(),WPCodeSnippetManager.show_pro_notice(o,n.data("upgrade-text"),n.data("upgrade-link"),n.data("upgrade-button")),!1}))}))},124:function(){const e=window.WPCodeSpinner||function(e,t,n){const o={init(){t.WPCodeSpinner=o,o.spinner=n("#wpcode-admin-spinner")},show_button_spinner(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"right";e.prop("disabled",!0);const i=e.offset(),a=n("#adminmenuwrap").width(),r=n("#wpadminbar").height();let s={};o.spinner.show(),s="right"===t?{left:i.left-a+e.outerWidth(),top:i.top-r+e.outerHeight()/2-o.spinner.height()/2}:{left:i.left-a-o.spinner.outerWidth()-20,top:i.top-r+e.outerHeight()/2-o.spinner.height()/2},o.spinner.css(s)},hide_button_spinner(e){e.prop("disabled",!1),o.spinner.hide()}};return o}(document,window,jQuery);e.init()},801:function(){(window.WPCodeAdminTools||function(e,t,n){const o={i18n:t.wpcode,init:function(){o.should_init()&&(o.find_elements(),o.init_importer(),o.init_ssl_verify(),o.init_confirm_delete_log())},should_init:()=>n("body").hasClass("wpcode-tools"),find_elements(){o.importer_button=n("#wpcode-importer-snippets-submit"),o.$import_progress=n("#wpcode-importer-process"),o.provider=n("#wpcode-importer-provider").val(),o.status_update=n("#wpcode-importer-status-update").html()},init_importer(){o.importer_button.on("click",(function(e){e.preventDefault();const t=n("#wpcode-importer-snippets input:checked");if(t.length){const e=[];t.each((function(){e.push(n(this).val())})),o.import_snippets(e)}}))},import_snippets(e){o.$import_progress.find(".snippet-total").text(e.length),o.$import_progress.find(".snippet-current").text("1"),n("#wpcode-importer-snippets").hide(),o.$import_progress.show(),o.import_queue=e,o.imported=0,o.import_snippet()},import_snippet(){const e=o.import_queue[0];n.post(ajaxurl,{action:"wpcode_import_snippet_"+o.provider,snippet_id:e,_wpnonce:wpcode.nonce}).done((function(e){if(e.success){o.import_queue.shift(),o.imported++;const t=n(o.status_update);t.find(".name span").text(e.data.name),t.find(".actions a").attr("href",e.data.edit),o.$import_progress.find(".status").prepend(t),o.$import_progress.find(".status").show(),0===o.import_queue.length?(o.$import_progress.find(".process-count").hide(),o.$import_progress.find(".snippets-completed").text(o.imported),o.$import_progress.find(".process-completed").show()):(o.$import_progress.find(".snippet-current").text(o.imported+1),o.import_snippet())}}))},init_ssl_verify(){n(e).on("click","#wpcode-ssl-verify",(function(e){e.preventDefault(),o.verify_ssl()}))},verify_ssl(){const e=n("#wpcode-ssl-verify"),t=e.text(),o=e.outerWidth(),i=e.parent(),a={action:"wpcode_verify_ssl",nonce:wpcode.nonce};e.css("width",o).prop("disabled",!0).text(wpcode.testing),n.post(ajaxurl,a,(function(n){console.log(n),i.find(".wpcode-alert, .wpcode-ssl-error").remove(),n.success&&e.before('<div class="wpcode-alert wpcode-alert-success">'+n.data.msg+"</div>"),!n.success&&n.data.msg&&e.before('<div class="wpcode-alert wpcode-alert-danger">'+n.data.msg+"</div>"),!n.success&&n.data.debug&&e.before('<div class="wpcode-ssl-error pre-error">'+n.data.debug+"</div>"),e.css("width",o).prop("disabled",!1).text(t)}))},init_confirm_delete_log(){n(".wpcode-delete-log").on("click",(function(e){return e.stopPropagation(),t.confirm(o.i18n.confirm_delete_log)}))}};return o}(document,window,jQuery)).init()},868:function(){(window.WPCodeAdminWelcome||function(e,t,n){const o={init:function(){o.add_listener()},add_listener(){n("#wpbody-content").on("click",".wpcode-scroll-to",(function(e){e.preventDefault();const t=n(this).attr("href"),o=n(t);n("html, body").animate({scrollTop:o.offset().top},700)}))}};return o}(document,window,jQuery)).init()},895:function(){"use strict";"function"!=typeof Object.assign&&(Object.assign=function(e){for(var t=[],n=1;n<arguments.length;n++)t[n-1]=arguments[n];if(!e)throw TypeError("Cannot convert undefined or null to object");for(var o=function(t){t&&Object.keys(t).forEach((function(n){return e[n]=t[n]}))},i=0,a=t;i<a.length;i++){var r=a[i];o(r)}return e})},180:function(e,t,n){var o,i;!function(a,r){o=[n(567)],i=function(e){return function(e,t){"use strict";var n=e.document,o=t(e),i=t.Deferred,a=t("html"),r=[],s="aria-hidden",l="lity-aria-hidden",c='a[href],area[href],input:not([disabled]),select:not([disabled]),textarea:not([disabled]),button:not([disabled]),iframe,object,embed,[contenteditable],[tabindex]:not([tabindex^="-"])',d={esc:!0,handler:null,handlers:{image:C,inline:function(e,n){var o,i,a;try{o=t(e)}catch(e){return!1}return!!o.length&&(i=t('<i style="display:none !important"></i>'),a=o.hasClass("lity-hide"),n.element().one("lity:remove",(function(){i.before(o).remove(),a&&!o.closest(".lity-content").length&&o.addClass("lity-hide")})),o.removeClass("lity-hide").after(i))},youtube:function(e){var n=p.exec(e);return!!n&&x(b(e,y("https://www.youtube"+(n[2]||"")+".com/embed/"+n[4],t.extend({autoplay:1},_(n[5]||"")))))},vimeo:function(e){var n=f.exec(e);return!!n&&x(b(e,y("https://player.vimeo.com/video/"+n[3],t.extend({autoplay:1},_(n[4]||"")))))},googlemaps:function(e){var t=h.exec(e);return!!t&&x(b(e,y("https://www.google."+t[3]+"/maps?"+t[6],{output:t[6].indexOf("layer=c")>0?"svembed":"embed"})))},facebookvideo:function(e){var n=m.exec(e);return!!n&&(0!==e.indexOf("http")&&(e="https:"+e),x(b(e,y("https://www.facebook.com/plugins/video.php?href="+e,t.extend({autoplay:1},_(n[4]||""))))))},iframe:x},template:'<div class="lity" role="dialog" aria-label="Dialog Window (Press escape to close)" tabindex="-1"><div class="lity-wrap" data-lity-close role="document"><div class="lity-loader" aria-hidden="true">Loading...</div><div class="lity-container"><div class="lity-content"></div><button class="lity-close" type="button" aria-label="Close (Press escape to close)" data-lity-close>&times;</button></div></div></div>'},u=/(^data:image\/)|(\.(png|jpe?g|gif|svg|webp|bmp|ico|tiff?)(\?\S*)?$)/i,p=/(youtube(-nocookie)?\.com|youtu\.be)\/(watch\?v=|v\/|u\/|embed\/?)?([\w-]{11})(.*)?/i,f=/(vimeo(pro)?.com)\/(?:[^\d]+)?(\d+)\??(.*)?$/,h=/((maps|www)\.)?google\.([^\/\?]+)\/?((maps\/?)?\?)(.*)/i,m=/(facebook\.com)\/([a-z0-9_-]*)\/videos\/([0-9]*)(.*)?$/i,g=function(){var e=n.createElement("div"),t={WebkitTransition:"webkitTransitionEnd",MozTransition:"transitionend",OTransition:"oTransitionEnd otransitionend",transition:"transitionend"};for(var o in t)if(void 0!==e.style[o])return t[o];return!1}();function w(e){var t=i();return g&&e.length?(e.one(g,t.resolve),setTimeout(t.resolve,500)):t.resolve(),t.promise()}function v(e,n,o){if(1===arguments.length)return t.extend({},e);if("string"==typeof n){if(void 0===o)return void 0===e[n]?null:e[n];e[n]=o}else t.extend(e,n);return this}function _(e){for(var t,n=decodeURI(e.split("#")[0]).split("&"),o={},i=0,a=n.length;i<a;i++)n[i]&&(o[(t=n[i].split("="))[0]]=t[1]);return o}function y(e,n){return e+(e.indexOf("?")>-1?"&":"?")+t.param(n)}function b(e,t){var n=e.indexOf("#");return-1===n?t:(n>0&&(e=e.substr(n)),t+e)}function C(e,n){var o=n.opener()&&n.opener().data("lity-desc")||"Image with no description",a=t('<img src="'+e+'" alt="'+o+'"/>'),r=i(),s=function(){var e;r.reject((e="Failed loading image",t('<span class="lity-error"></span>').append(e)))};return a.on("load",(function(){if(0===this.naturalWidth)return s();r.resolve(a)})).on("error",s),r.promise()}function x(e){return'<div class="lity-iframe-container"><iframe frameborder="0" allowfullscreen allow="autoplay; fullscreen" src="'+e+'"/></div>'}function k(){return n.documentElement.clientHeight?n.documentElement.clientHeight:Math.round(o.height())}function D(e){var t=E();t&&(27===e.keyCode&&t.options("esc")&&t.close(),9===e.keyCode&&function(e,t){var o=t.element().find(c),i=o.index(n.activeElement);e.shiftKey&&i<=0?(o.get(o.length-1).focus(),e.preventDefault()):e.shiftKey||i!==o.length-1||(o.get(0).focus(),e.preventDefault())}(e,t))}function A(){t.each(r,(function(e,t){t.resize()}))}function E(){return 0===r.length?null:r[0]}function M(e,c,u,p){var f,h,m,g,_=this,y=!1,b=!1;c=t.extend({},d,c),h=t(c.template),_.element=function(){return h},_.opener=function(){return u},_.options=t.proxy(v,_,c),_.handlers=t.proxy(v,_,c.handlers),_.resize=function(){y&&!b&&m.css("max-height",k()+"px").trigger("lity:resize",[_])},_.close=function(){if(y&&!b){b=!0,(c=_).element().attr(s,"true"),1===r.length&&(a.removeClass("lity-active"),o.off({resize:A,keydown:D})),((r=t.grep(r,(function(e){return c!==e}))).length?r[0].element():t(".lity-hidden")).removeClass("lity-hidden").each((function(){var e=t(this),n=e.data(l);n?e.attr(s,n):e.removeAttr(s),e.removeData(l)}));var e=i();if(p&&(n.activeElement===h[0]||t.contains(h[0],n.activeElement)))try{p.focus()}catch(e){}return m.trigger("lity:close",[_]),h.removeClass("lity-opened").addClass("lity-closed"),w(m.add(h)).always((function(){m.trigger("lity:remove",[_]),h.remove(),h=void 0,e.resolve()})),e.promise()}var c},f=function(e,n,o,i){var a,r="inline",s=t.extend({},o);return i&&s[i]?(a=s[i](e,n),r=i):(t.each(["inline","iframe"],(function(e,t){delete s[t],s[t]=o[t]})),t.each(s,(function(t,o){return!o||!(!o.test||o.test(e,n))||(!1!==(a=o(e,n))?(r=t,!1):void 0)}))),{handler:r,content:a||""}}(e,_,c.handlers,c.handler),h.attr(s,"false").addClass("lity-loading lity-opened lity-"+f.handler).appendTo("body").focus().on("click","[data-lity-close]",(function(e){t(e.target).is("[data-lity-close]")&&_.close()})).trigger("lity:open",[_]),g=_,1===r.unshift(g)&&(a.addClass("lity-active"),o.on({resize:A,keydown:D})),t("body > *").not(g.element()).addClass("lity-hidden").each((function(){var e=t(this);void 0===e.data(l)&&e.data(l,e.attr(s)||null)})).attr(s,"true"),t.when(f.content).always((function(e){m=t(e).css("max-height",k()+"px"),h.find(".lity-loader").each((function(){var e=t(this);w(e).always((function(){e.remove()}))})),h.removeClass("lity-loading").find(".lity-content").empty().append(m),y=!0,m.trigger("lity:ready",[_])}))}function T(e,o,i){e.preventDefault?(e.preventDefault(),e=(i=t(this)).data("lity-target")||i.attr("href")||i.attr("src")):i=t(i);var a=new M(e,t.extend({},i.data("lity-options")||i.data("lity"),o),i,n.activeElement);if(!e.preventDefault)return a}return C.test=function(e){return u.test(e)},T.version="2.4.1",T.options=t.proxy(v,T,d),T.handlers=t.proxy(v,T,d.handlers),T.current=E,t(n).on("click.lity","[data-lity]",T),T}(a,e)}.apply(t,o),void 0===i||(e.exports=i)}("undefined"!=typeof window?window:this)},207:function(e,t,n){var o,i,a;i=[n(567)],o=function(e){var t=function(){if(e&&e.fn&&e.fn.select2&&e.fn.select2.amd)var t=e.fn.select2.amd;var n,o,i;return t&&t.requirejs||(t?o=t:t={},function(e){var t,a,r,s,l={},c={},d={},u={},p=Object.prototype.hasOwnProperty,f=[].slice,h=/\.js$/;function m(e,t){return p.call(e,t)}function g(e,t){var n,o,i,a,r,s,l,c,u,p,f,m=t&&t.split("/"),g=d.map,w=g&&g["*"]||{};if(e){for(r=(e=e.split("/")).length-1,d.nodeIdCompat&&h.test(e[r])&&(e[r]=e[r].replace(h,"")),"."===e[0].charAt(0)&&m&&(e=m.slice(0,m.length-1).concat(e)),u=0;u<e.length;u++)if("."===(f=e[u]))e.splice(u,1),u-=1;else if(".."===f){if(0===u||1===u&&".."===e[2]||".."===e[u-1])continue;u>0&&(e.splice(u-1,2),u-=2)}e=e.join("/")}if((m||w)&&g){for(u=(n=e.split("/")).length;u>0;u-=1){if(o=n.slice(0,u).join("/"),m)for(p=m.length;p>0;p-=1)if((i=g[m.slice(0,p).join("/")])&&(i=i[o])){a=i,s=u;break}if(a)break;!l&&w&&w[o]&&(l=w[o],c=u)}!a&&l&&(a=l,s=c),a&&(n.splice(0,s,a),e=n.join("/"))}return e}function w(t,n){return function(){var o=f.call(arguments,0);return"string"!=typeof o[0]&&1===o.length&&o.push(null),a.apply(e,o.concat([t,n]))}}function v(e){return function(t){l[e]=t}}function _(n){if(m(c,n)){var o=c[n];delete c[n],u[n]=!0,t.apply(e,o)}if(!m(l,n)&&!m(u,n))throw new Error("No "+n);return l[n]}function y(e){var t,n=e?e.indexOf("!"):-1;return n>-1&&(t=e.substring(0,n),e=e.substring(n+1,e.length)),[t,e]}function b(e){return e?y(e):[]}function C(e){return function(){return d&&d.config&&d.config[e]||{}}}r=function(e,t){var n,o,i=y(e),a=i[0],r=t[1];return e=i[1],a&&(n=_(a=g(a,r))),a?e=n&&n.normalize?n.normalize(e,(o=r,function(e){return g(e,o)})):g(e,r):(a=(i=y(e=g(e,r)))[0],e=i[1],a&&(n=_(a))),{f:a?a+"!"+e:e,n:e,pr:a,p:n}},s={require:function(e){return w(e)},exports:function(e){var t=l[e];return void 0!==t?t:l[e]={}},module:function(e){return{id:e,uri:"",exports:l[e],config:C(e)}}},t=function(t,n,o,i){var a,d,p,f,h,g,y,C=[],x=typeof o;if(g=b(i=i||t),"undefined"===x||"function"===x){for(n=!n.length&&o.length?["require","exports","module"]:n,h=0;h<n.length;h+=1)if("require"===(d=(f=r(n[h],g)).f))C[h]=s.require(t);else if("exports"===d)C[h]=s.exports(t),y=!0;else if("module"===d)a=C[h]=s.module(t);else if(m(l,d)||m(c,d)||m(u,d))C[h]=_(d);else{if(!f.p)throw new Error(t+" missing "+d);f.p.load(f.n,w(i,!0),v(d),{}),C[h]=l[d]}p=o?o.apply(l[t],C):void 0,t&&(a&&a.exports!==e&&a.exports!==l[t]?l[t]=a.exports:p===e&&y||(l[t]=p))}else t&&(l[t]=o)},n=o=a=function(n,o,i,l,c){if("string"==typeof n)return s[n]?s[n](o):_(r(n,b(o)).f);if(!n.splice){if((d=n).deps&&a(d.deps,d.callback),!o)return;o.splice?(n=o,o=i,i=null):n=e}return o=o||function(){},"function"==typeof i&&(i=l,l=c),l?t(e,n,o,i):setTimeout((function(){t(e,n,o,i)}),4),a},a.config=function(e){return a(e)},n._defined=l,(i=function(e,t,n){if("string"!=typeof e)throw new Error("See almond README: incorrect module build, no module name");t.splice||(n=t,t=[]),m(l,e)||m(c,e)||(c[e]=[e,t,n])}).amd={jQuery:!0}}(),t.requirejs=n,t.require=o,t.define=i),t.define("almond",(function(){})),t.define("jquery",[],(function(){var t=e||$;return null==t&&console&&console.error&&console.error("Select2: An instance of jQuery or a jQuery-compatible library was not found. Make sure that you are including jQuery before Select2 on your web page."),t})),t.define("select2/utils",["jquery"],(function(e){var t={};function n(e){var t=e.prototype,n=[];for(var o in t)"function"==typeof t[o]&&"constructor"!==o&&n.push(o);return n}t.Extend=function(e,t){var n={}.hasOwnProperty;function o(){this.constructor=e}for(var i in t)n.call(t,i)&&(e[i]=t[i]);return o.prototype=t.prototype,e.prototype=new o,e.__super__=t.prototype,e},t.Decorate=function(e,t){var o=n(t),i=n(e);function a(){var n=Array.prototype.unshift,o=t.prototype.constructor.length,i=e.prototype.constructor;o>0&&(n.call(arguments,e.prototype.constructor),i=t.prototype.constructor),i.apply(this,arguments)}t.displayName=e.displayName,a.prototype=new function(){this.constructor=a};for(var r=0;r<i.length;r++){var s=i[r];a.prototype[s]=e.prototype[s]}for(var l=function(e){var n=function(){};e in a.prototype&&(n=a.prototype[e]);var o=t.prototype[e];return function(){return Array.prototype.unshift.call(arguments,n),o.apply(this,arguments)}},c=0;c<o.length;c++){var d=o[c];a.prototype[d]=l(d)}return a};var o=function(){this.listeners={}};return o.prototype.on=function(e,t){this.listeners=this.listeners||{},e in this.listeners?this.listeners[e].push(t):this.listeners[e]=[t]},o.prototype.trigger=function(e){var t=Array.prototype.slice,n=t.call(arguments,1);this.listeners=this.listeners||{},null==n&&(n=[]),0===n.length&&n.push({}),n[0]._type=e,e in this.listeners&&this.invoke(this.listeners[e],t.call(arguments,1)),"*"in this.listeners&&this.invoke(this.listeners["*"],arguments)},o.prototype.invoke=function(e,t){for(var n=0,o=e.length;n<o;n++)e[n].apply(this,t)},t.Observable=o,t.generateChars=function(e){for(var t="",n=0;n<e;n++)t+=Math.floor(36*Math.random()).toString(36);return t},t.bind=function(e,t){return function(){e.apply(t,arguments)}},t._convertData=function(e){for(var t in e){var n=t.split("-"),o=e;if(1!==n.length){for(var i=0;i<n.length;i++){var a=n[i];(a=a.substring(0,1).toLowerCase()+a.substring(1))in o||(o[a]={}),i==n.length-1&&(o[a]=e[t]),o=o[a]}delete e[t]}}return e},t.hasScroll=function(t,n){var o=e(n),i=n.style.overflowX,a=n.style.overflowY;return(i!==a||"hidden"!==a&&"visible"!==a)&&("scroll"===i||"scroll"===a||o.innerHeight()<n.scrollHeight||o.innerWidth()<n.scrollWidth)},t.escapeMarkup=function(e){var t={"\\":"&#92;","&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;","/":"&#47;"};return"string"!=typeof e?e:String(e).replace(/[&<>"'\/\\]/g,(function(e){return t[e]}))},t.appendMany=function(t,n){if("1.7"===e.fn.jquery.substr(0,3)){var o=e();e.map(n,(function(e){o=o.add(e)})),n=o}t.append(n)},t.isTouchscreen=function(){return void 0===t._isTouchscreenCache&&(t._isTouchscreenCache="ontouchstart"in document.documentElement),t._isTouchscreenCache},t})),t.define("select2/results",["jquery","./utils"],(function(e,t){function n(e,t,o){this.$element=e,this.data=o,this.options=t,n.__super__.constructor.call(this)}return t.Extend(n,t.Observable),n.prototype.render=function(){var t=e('<ul class="select2-results__options" role="listbox" tabindex="-1"></ul>');return this.options.get("multiple")&&t.attr("aria-multiselectable","true"),this.$results=t,t},n.prototype.clear=function(){this.$results.empty()},n.prototype.displayMessage=function(t){var n=this.options.get("escapeMarkup");this.clear(),this.hideLoading();var o=e('<li role="alert" aria-live="assertive" class="select2-results__option"></li>'),i=this.options.get("translations").get(t.message);o.append(n(i(t.args))),o[0].className+=" select2-results__message",this.$results.append(o)},n.prototype.hideMessages=function(){this.$results.find(".select2-results__message").remove()},n.prototype.append=function(e){this.hideLoading();var t=[];if(null!=e.results&&0!==e.results.length){e.results=this.sort(e.results);for(var n=0;n<e.results.length;n++){var o=e.results[n],i=this.option(o);t.push(i)}this.$results.append(t)}else 0===this.$results.children().length&&this.trigger("results:message",{message:"noResults"})},n.prototype.position=function(e,t){t.find(".select2-results").append(e)},n.prototype.sort=function(e){return this.options.get("sorter")(e)},n.prototype.highlightFirstItem=function(){var e=this.$results.find(".select2-results__option[data-selected]"),t=e.filter("[data-selected=true]");t.length>0?t.first().trigger("mouseenter"):e.first().trigger("mouseenter"),this.ensureHighlightVisible()},n.prototype.setClasses=function(){var t=this;this.data.current((function(n){var o=e.map(n,(function(e){return e.id.toString()}));t.$results.find(".select2-results__option[data-selected]").each((function(){var t=e(this),n=e.data(this,"data"),i=""+n.id;null!=n.element&&n.element.selected||null==n.element&&e.inArray(i,o)>-1?t.attr("data-selected","true"):t.attr("data-selected","false")}))}))},n.prototype.showLoading=function(e){this.hideLoading();var t={disabled:!0,loading:!0,text:this.options.get("translations").get("searching")(e)},n=this.option(t);n.className+=" loading-results",this.$results.prepend(n)},n.prototype.hideLoading=function(){this.$results.find(".loading-results").remove()},n.prototype.option=function(t){var n=document.createElement("li");n.className="select2-results__option";var o={role:"option","data-selected":"false",tabindex:-1};for(var i in t.disabled&&(delete o["data-selected"],o["aria-disabled"]="true"),null==t.id&&delete o["data-selected"],null!=t._resultId&&(n.id=t._resultId),t.title&&(n.title=t.title),t.children&&(o["aria-label"]=t.text,delete o["data-selected"]),o){var a=o[i];n.setAttribute(i,a)}if(t.children){var r=e(n),s=document.createElement("strong");s.className="select2-results__group";var l=e(s);this.template(t,s),l.attr("role","presentation");for(var c=[],d=0;d<t.children.length;d++){var u=t.children[d],p=this.option(u);c.push(p)}var f=e("<ul></ul>",{class:"select2-results__options select2-results__options--nested",role:"listbox"});f.append(c),r.attr("role","list"),r.append(s),r.append(f)}else this.template(t,n);return e.data(n,"data",t),n},n.prototype.bind=function(t,n){var o=this,i=t.id+"-results";this.$results.attr("id",i),t.on("results:all",(function(e){o.clear(),o.append(e.data),t.isOpen()&&(o.setClasses(),o.highlightFirstItem())})),t.on("results:append",(function(e){o.append(e.data),t.isOpen()&&o.setClasses()})),t.on("query",(function(e){o.hideMessages(),o.showLoading(e)})),t.on("select",(function(){t.isOpen()&&(o.setClasses(),o.highlightFirstItem())})),t.on("unselect",(function(){t.isOpen()&&(o.setClasses(),o.highlightFirstItem())})),t.on("open",(function(){o.$results.attr("aria-expanded","true"),o.$results.attr("aria-hidden","false"),o.setClasses(),o.ensureHighlightVisible()})),t.on("close",(function(){o.$results.attr("aria-expanded","false"),o.$results.attr("aria-hidden","true"),o.$results.removeAttr("aria-activedescendant")})),t.on("results:toggle",(function(){var e=o.getHighlightedResults();0!==e.length&&e.trigger("mouseup")})),t.on("results:select",(function(){var e=o.getHighlightedResults();if(0!==e.length){var t=e.data("data");"true"==e.attr("data-selected")?o.trigger("close",{}):o.trigger("select",{data:t})}})),t.on("results:previous",(function(){var e=o.getHighlightedResults(),t=o.$results.find("[data-selected]"),n=t.index(e);if(0!==n){var i=n-1;0===e.length&&(i=0);var a=t.eq(i);a.trigger("mouseenter");var r=o.$results.offset().top,s=a.offset().top,l=o.$results.scrollTop()+(s-r);0===i?o.$results.scrollTop(0):s-r<0&&o.$results.scrollTop(l)}})),t.on("results:next",(function(){var e=o.getHighlightedResults(),t=o.$results.find("[data-selected]"),n=t.index(e)+1;if(!(n>=t.length)){var i=t.eq(n);i.trigger("mouseenter");var a=o.$results.offset().top+o.$results.outerHeight(!1),r=i.offset().top+i.outerHeight(!1),s=o.$results.scrollTop()+r-a;0===n?o.$results.scrollTop(0):r>a&&o.$results.scrollTop(s)}})),t.on("results:focus",(function(e){e.element.addClass("select2-results__option--highlighted").attr("aria-selected","true"),o.$results.attr("aria-activedescendant",e.element.attr("id"))})),t.on("results:message",(function(e){o.displayMessage(e)})),e.fn.mousewheel&&this.$results.on("mousewheel",(function(e){var t=o.$results.scrollTop(),n=o.$results.get(0).scrollHeight-t+e.deltaY,i=e.deltaY>0&&t-e.deltaY<=0,a=e.deltaY<0&&n<=o.$results.height();i?(o.$results.scrollTop(0),e.preventDefault(),e.stopPropagation()):a&&(o.$results.scrollTop(o.$results.get(0).scrollHeight-o.$results.height()),e.preventDefault(),e.stopPropagation())})),this.$results.on("mouseup",".select2-results__option[data-selected]",(function(t){var n=e(this),i=n.data("data");"true"!==n.attr("data-selected")?o.trigger("select",{originalEvent:t,data:i}):o.options.get("multiple")?o.trigger("unselect",{originalEvent:t,data:i}):o.trigger("close",{})})),this.$results.on("mouseenter",".select2-results__option[data-selected]",(function(t){var n=e(this).data("data");o.getHighlightedResults().removeClass("select2-results__option--highlighted").attr("aria-selected","false"),o.trigger("results:focus",{data:n,element:e(this)})}))},n.prototype.getHighlightedResults=function(){return this.$results.find(".select2-results__option--highlighted")},n.prototype.destroy=function(){this.$results.remove()},n.prototype.ensureHighlightVisible=function(){var e=this.getHighlightedResults();if(0!==e.length){var t=this.$results.find("[data-selected]").index(e),n=this.$results.offset().top,o=e.offset().top,i=this.$results.scrollTop()+(o-n),a=o-n;i-=2*e.outerHeight(!1),t<=2?this.$results.scrollTop(0):(a>this.$results.outerHeight()||a<0)&&this.$results.scrollTop(i)}},n.prototype.template=function(t,n){var o=this.options.get("templateResult"),i=this.options.get("escapeMarkup"),a=o(t,n);null==a?n.style.display="none":"string"==typeof a?n.innerHTML=i(a):e(n).append(a)},n})),t.define("select2/keys",[],(function(){return{BACKSPACE:8,TAB:9,ENTER:13,SHIFT:16,CTRL:17,ALT:18,ESC:27,SPACE:32,PAGE_UP:33,PAGE_DOWN:34,END:35,HOME:36,LEFT:37,UP:38,RIGHT:39,DOWN:40,DELETE:46}})),t.define("select2/selection/base",["jquery","../utils","../keys"],(function(e,t,n){function o(e,t){this.$element=e,this.options=t,o.__super__.constructor.call(this)}return t.Extend(o,t.Observable),o.prototype.render=function(){var t=e('<span class="select2-selection"  aria-haspopup="true" aria-expanded="false"></span>');return this._tabindex=0,null!=this.$element.data("old-tabindex")?this._tabindex=this.$element.data("old-tabindex"):null!=this.$element.attr("tabindex")&&(this._tabindex=this.$element.attr("tabindex")),t.attr("title",this.$element.attr("title")),t.attr("tabindex",this._tabindex),this.$selection=t,t},o.prototype.bind=function(e,t){var o=this,i=(e.id,e.id+"-results");this.options.get("minimumResultsForSearch"),this.container=e,this.$selection.on("focus",(function(e){o.trigger("focus",e)})),this.$selection.on("blur",(function(e){o._handleBlur(e)})),this.$selection.on("keydown",(function(e){o.trigger("keypress",e),e.which===n.SPACE&&e.preventDefault()})),e.on("results:focus",(function(e){o.$selection.attr("aria-activedescendant",e.data._resultId)})),e.on("selection:update",(function(e){o.update(e.data)})),e.on("open",(function(){o.$selection.attr("aria-expanded","true"),o.$selection.attr("aria-owns",i),o._attachCloseHandler(e)})),e.on("close",(function(){o.$selection.attr("aria-expanded","false"),o.$selection.removeAttr("aria-activedescendant"),o.$selection.removeAttr("aria-owns"),window.setTimeout((function(){o.$selection.focus()}),1),o._detachCloseHandler(e)})),e.on("enable",(function(){o.$selection.attr("tabindex",o._tabindex)})),e.on("disable",(function(){o.$selection.attr("tabindex","-1")}))},o.prototype._handleBlur=function(t){var n=this;window.setTimeout((function(){document.activeElement==n.$selection[0]||e.contains(n.$selection[0],document.activeElement)||n.trigger("blur",t)}),1)},o.prototype._attachCloseHandler=function(t){e(document.body).on("mousedown.select2."+t.id,(function(t){var n=e(t.target),o=n.closest(".select2");e(".select2.select2-container--open").each((function(){var t=e(this);this!=o[0]&&(t.data("element").select2("close"),setTimeout((function(){t.find("*:focus").blur(),n.focus()}),1))}))}))},o.prototype._detachCloseHandler=function(t){e(document.body).off("mousedown.select2."+t.id)},o.prototype.position=function(e,t){t.find(".selection").append(e)},o.prototype.destroy=function(){this._detachCloseHandler(this.container)},o.prototype.update=function(e){throw new Error("The `update` method must be defined in child classes.")},o})),t.define("select2/selection/single",["jquery","./base","../utils","../keys"],(function(e,t,n,o){function i(){i.__super__.constructor.apply(this,arguments)}return n.Extend(i,t),i.prototype.render=function(){var e=i.__super__.render.call(this);return e.addClass("select2-selection--single"),e.html('<span class="select2-selection__rendered"></span><span class="select2-selection__arrow" role="presentation"><b role="presentation"></b></span>'),e},i.prototype.bind=function(e,t){var n=this;i.__super__.bind.apply(this,arguments);var o=e.id+"-container";this.$selection.find(".select2-selection__rendered").attr("id",o).attr("role","textbox").attr("aria-readonly","true"),this.$selection.attr("aria-labelledby",o),this.$selection.attr("role","combobox"),this.$selection.on("mousedown",(function(e){1===e.which&&n.trigger("toggle",{originalEvent:e})})),this.$selection.on("focus",(function(e){})),this.$selection.on("keydown",(function(t){!e.isOpen()&&t.which>=48&&t.which<=90&&e.open()})),this.$selection.on("blur",(function(e){})),e.on("focus",(function(t){e.isOpen()||n.$selection.focus()})),e.on("selection:update",(function(e){n.update(e.data)}))},i.prototype.clear=function(){this.$selection.find(".select2-selection__rendered").empty()},i.prototype.display=function(e,t){var n=this.options.get("templateSelection");return this.options.get("escapeMarkup")(n(e,t))},i.prototype.selectionContainer=function(){return e("<span></span>")},i.prototype.update=function(e){if(0!==e.length){var t=e[0],n=this.$selection.find(".select2-selection__rendered"),o=this.display(t,n);n.empty().append(o),n.prop("title",t.title||t.text)}else this.clear()},i})),t.define("select2/selection/multiple",["jquery","./base","../utils"],(function(e,t,n){function o(e,t){o.__super__.constructor.apply(this,arguments)}return n.Extend(o,t),o.prototype.render=function(){var e=o.__super__.render.call(this);return e.addClass("select2-selection--multiple"),e.html('<ul class="select2-selection__rendered" aria-live="polite" aria-relevant="additions removals" aria-atomic="true"></ul>'),e},o.prototype.bind=function(t,n){var i=this;o.__super__.bind.apply(this,arguments),this.$selection.on("click",(function(e){i.trigger("toggle",{originalEvent:e})})),this.$selection.on("click",".select2-selection__choice__remove",(function(t){if(!i.options.get("disabled")){var n=e(this).parent().data("data");i.trigger("unselect",{originalEvent:t,data:n})}})),this.$selection.on("keydown",(function(e){!t.isOpen()&&e.which>=48&&e.which<=90&&t.open()})),t.on("focus",(function(){i.focusOnSearch()}))},o.prototype.clear=function(){this.$selection.find(".select2-selection__rendered").empty()},o.prototype.display=function(e,t){var n=this.options.get("templateSelection");return this.options.get("escapeMarkup")(n(e,t))},o.prototype.selectionContainer=function(){return e('<li class="select2-selection__choice"><span class="select2-selection__choice__remove" role="presentation" aria-hidden="true">&times;</span></li>')},o.prototype.focusOnSearch=function(){var e=this;void 0!==e.$search&&setTimeout((function(){e._keyUpPrevented=!0,e.$search.focus()}),1)},o.prototype.update=function(e){if(this.clear(),0!==e.length){for(var t=[],o=0;o<e.length;o++){var i=e[o],a=this.selectionContainer(),r=this.display(i,a);"string"==typeof r&&(r=r.trim()),a.append(r),a.prop("title",i.title||i.text),a.data("data",i),t.push(a)}var s=this.$selection.find(".select2-selection__rendered");n.appendMany(s,t)}},o})),t.define("select2/selection/placeholder",["../utils"],(function(e){function t(e,t,n){this.placeholder=this.normalizePlaceholder(n.get("placeholder")),e.call(this,t,n)}return t.prototype.normalizePlaceholder=function(e,t){return"string"==typeof t&&(t={id:"",text:t}),t},t.prototype.createPlaceholder=function(e,t){var n=this.selectionContainer();return n.html(this.display(t)),n.addClass("select2-selection__placeholder").removeClass("select2-selection__choice"),n},t.prototype.update=function(e,t){var n=1==t.length&&t[0].id!=this.placeholder.id;if(t.length>1||n)return e.call(this,t);this.clear();var o=this.createPlaceholder(this.placeholder);this.$selection.find(".select2-selection__rendered").append(o)},t})),t.define("select2/selection/allowClear",["jquery","../keys"],(function(e,t){function n(){}return n.prototype.bind=function(e,t,n){var o=this;e.call(this,t,n),null==this.placeholder&&this.options.get("debug")&&window.console&&console.error&&console.error("Select2: The `allowClear` option should be used in combination with the `placeholder` option."),this.$selection.on("mousedown",".select2-selection__clear",(function(e){o._handleClear(e)})),t.on("keypress",(function(e){o._handleKeyboardClear(e,t)}))},n.prototype._handleClear=function(e,t){if(!this.options.get("disabled")){var n=this.$selection.find(".select2-selection__clear");if(0!==n.length){t.stopPropagation();for(var o=n.data("data"),i=0;i<o.length;i++){var a={data:o[i]};if(this.trigger("unselect",a),a.prevented)return}this.$element.val(this.placeholder.id).trigger("change"),this.trigger("toggle",{})}}},n.prototype._handleKeyboardClear=function(e,n,o){o.isOpen()||n.which!=t.DELETE&&n.which!=t.BACKSPACE||this._handleClear(n)},n.prototype.update=function(t,n){if(t.call(this,n),!(this.$selection.find(".select2-selection__placeholder").length>0||0===n.length)){var o=e('<span class="select2-selection__clear">&times;</span>');o.data("data",n),this.$selection.find(".select2-selection__rendered").prepend(o)}},n})),t.define("select2/selection/search",["jquery","../utils","../keys"],(function(e,t,n){function o(e,t,n){e.call(this,t,n)}return o.prototype.render=function(t){var n=e('<li class="select2-search select2-search--inline"><input class="select2-search__field" type="text" tabindex="-1" autocomplete="off" autocorrect="off" autocapitalize="none" spellcheck="false" role="textbox" aria-autocomplete="list" /></li>');this.$searchContainer=n,this.$search=n.find("input");var o=t.call(this);return this._transferTabIndex(),o},o.prototype.bind=function(e,t,o){var i=this,a=t.id+"-results";e.call(this,t,o),t.on("open",(function(){i.$search.attr("aria-owns",a),i.$search.trigger("focus")})),t.on("close",(function(){i.$search.val(""),i.$search.removeAttr("aria-activedescendant"),i.$search.removeAttr("aria-owns"),i.$search.trigger("focus")})),t.on("enable",(function(){i.$search.prop("disabled",!1),i._transferTabIndex()})),t.on("disable",(function(){i.$search.prop("disabled",!0)})),t.on("focus",(function(e){i.$search.trigger("focus")})),t.on("results:focus",(function(e){i.$search.attr("aria-activedescendant",e.data._resultId)})),this.$selection.on("focusin",".select2-search--inline",(function(e){i.trigger("focus",e)})),this.$selection.on("focusout",".select2-search--inline",(function(e){i._handleBlur(e)})),this.$selection.on("keydown",".select2-search--inline",(function(e){if(e.stopPropagation(),i.trigger("keypress",e),i._keyUpPrevented=e.isDefaultPrevented(),e.which===n.BACKSPACE&&""===i.$search.val()){var o=i.$searchContainer.prev(".select2-selection__choice");if(o.length>0){var a=o.data("data");i.searchRemoveChoice(a),e.preventDefault()}}else e.which===n.ENTER&&(t.open(),e.preventDefault())}));var r=document.documentMode,s=r&&r<=11;this.$selection.on("input.searchcheck",".select2-search--inline",(function(e){s?i.$selection.off("input.search input.searchcheck"):i.$selection.off("keyup.search")})),this.$selection.on("keyup.search input.search",".select2-search--inline",(function(e){if(s&&"input"===e.type)i.$selection.off("input.search input.searchcheck");else{var t=e.which;t!=n.SHIFT&&t!=n.CTRL&&t!=n.ALT&&t!=n.TAB&&i.handleSearch(e)}}))},o.prototype._transferTabIndex=function(e){this.$search.attr("tabindex",this.$selection.attr("tabindex")),this.$selection.attr("tabindex","-1")},o.prototype.createPlaceholder=function(e,t){this.$search.attr("placeholder",t.text)},o.prototype.update=function(e,t){var n=this.$search[0]==document.activeElement;this.$search.attr("placeholder",""),e.call(this,t),this.$selection.find(".select2-selection__rendered").append(this.$searchContainer),this.resizeSearch(),n&&this.$search.focus()},o.prototype.handleSearch=function(){if(this.resizeSearch(),!this._keyUpPrevented){var e=this.$search.val();this.trigger("query",{term:e})}this._keyUpPrevented=!1},o.prototype.searchRemoveChoice=function(e,t){this.trigger("unselect",{data:t}),this.$search.val(t.text),this.handleSearch()},o.prototype.resizeSearch=function(){this.$search.css("width","25px");var e;e=""!==this.$search.attr("placeholder")?this.$selection.find(".select2-selection__rendered").innerWidth():.75*(this.$search.val().length+1)+"em",this.$search.css("width",e)},o})),t.define("select2/selection/eventRelay",["jquery"],(function(e){function t(){}return t.prototype.bind=function(t,n,o){var i=this,a=["open","opening","close","closing","select","selecting","unselect","unselecting"],r=["opening","closing","selecting","unselecting"];t.call(this,n,o),n.on("*",(function(t,n){if(-1!==e.inArray(t,a)){n=n||{};var o=e.Event("select2:"+t,{params:n});i.$element.trigger(o),-1!==e.inArray(t,r)&&(n.prevented=o.isDefaultPrevented())}}))},t})),t.define("select2/translation",["jquery","require"],(function(e,t){function n(e){this.dict=e||{}}return n.prototype.all=function(){return this.dict},n.prototype.get=function(e){return this.dict[e]},n.prototype.extend=function(t){this.dict=e.extend({},t.all(),this.dict)},n._cache={},n.loadPath=function(e){if(!(e in n._cache)){var o=t(e);n._cache[e]=o}return new n(n._cache[e])},n})),t.define("select2/diacritics",[],(function(){return{"Ⓐ":"A","Ａ":"A","À":"A","Á":"A","Â":"A","Ầ":"A","Ấ":"A","Ẫ":"A","Ẩ":"A","Ã":"A","Ā":"A","Ă":"A","Ằ":"A","Ắ":"A","Ẵ":"A","Ẳ":"A","Ȧ":"A","Ǡ":"A","Ä":"A","Ǟ":"A","Ả":"A","Å":"A","Ǻ":"A","Ǎ":"A","Ȁ":"A","Ȃ":"A","Ạ":"A","Ậ":"A","Ặ":"A","Ḁ":"A","Ą":"A","Ⱥ":"A","Ɐ":"A","Ꜳ":"AA","Æ":"AE","Ǽ":"AE","Ǣ":"AE","Ꜵ":"AO","Ꜷ":"AU","Ꜹ":"AV","Ꜻ":"AV","Ꜽ":"AY","Ⓑ":"B","Ｂ":"B","Ḃ":"B","Ḅ":"B","Ḇ":"B","Ƀ":"B","Ƃ":"B","Ɓ":"B","Ⓒ":"C","Ｃ":"C","Ć":"C","Ĉ":"C","Ċ":"C","Č":"C","Ç":"C","Ḉ":"C","Ƈ":"C","Ȼ":"C","Ꜿ":"C","Ⓓ":"D","Ｄ":"D","Ḋ":"D","Ď":"D","Ḍ":"D","Ḑ":"D","Ḓ":"D","Ḏ":"D","Đ":"D","Ƌ":"D","Ɗ":"D","Ɖ":"D","Ꝺ":"D","Ǳ":"DZ","Ǆ":"DZ","ǲ":"Dz","ǅ":"Dz","Ⓔ":"E","Ｅ":"E","È":"E","É":"E","Ê":"E","Ề":"E","Ế":"E","Ễ":"E","Ể":"E","Ẽ":"E","Ē":"E","Ḕ":"E","Ḗ":"E","Ĕ":"E","Ė":"E","Ë":"E","Ẻ":"E","Ě":"E","Ȅ":"E","Ȇ":"E","Ẹ":"E","Ệ":"E","Ȩ":"E","Ḝ":"E","Ę":"E","Ḙ":"E","Ḛ":"E","Ɛ":"E","Ǝ":"E","Ⓕ":"F","Ｆ":"F","Ḟ":"F","Ƒ":"F","Ꝼ":"F","Ⓖ":"G","Ｇ":"G","Ǵ":"G","Ĝ":"G","Ḡ":"G","Ğ":"G","Ġ":"G","Ǧ":"G","Ģ":"G","Ǥ":"G","Ɠ":"G","Ꞡ":"G","Ᵹ":"G","Ꝿ":"G","Ⓗ":"H","Ｈ":"H","Ĥ":"H","Ḣ":"H","Ḧ":"H","Ȟ":"H","Ḥ":"H","Ḩ":"H","Ḫ":"H","Ħ":"H","Ⱨ":"H","Ⱶ":"H","Ɥ":"H","Ⓘ":"I","Ｉ":"I","Ì":"I","Í":"I","Î":"I","Ĩ":"I","Ī":"I","Ĭ":"I","İ":"I","Ï":"I","Ḯ":"I","Ỉ":"I","Ǐ":"I","Ȉ":"I","Ȋ":"I","Ị":"I","Į":"I","Ḭ":"I","Ɨ":"I","Ⓙ":"J","Ｊ":"J","Ĵ":"J","Ɉ":"J","Ⓚ":"K","Ｋ":"K","Ḱ":"K","Ǩ":"K","Ḳ":"K","Ķ":"K","Ḵ":"K","Ƙ":"K","Ⱪ":"K","Ꝁ":"K","Ꝃ":"K","Ꝅ":"K","Ꞣ":"K","Ⓛ":"L","Ｌ":"L","Ŀ":"L","Ĺ":"L","Ľ":"L","Ḷ":"L","Ḹ":"L","Ļ":"L","Ḽ":"L","Ḻ":"L","Ł":"L","Ƚ":"L","Ɫ":"L","Ⱡ":"L","Ꝉ":"L","Ꝇ":"L","Ꞁ":"L","Ǉ":"LJ","ǈ":"Lj","Ⓜ":"M","Ｍ":"M","Ḿ":"M","Ṁ":"M","Ṃ":"M","Ɱ":"M","Ɯ":"M","Ⓝ":"N","Ｎ":"N","Ǹ":"N","Ń":"N","Ñ":"N","Ṅ":"N","Ň":"N","Ṇ":"N","Ņ":"N","Ṋ":"N","Ṉ":"N","Ƞ":"N","Ɲ":"N","Ꞑ":"N","Ꞥ":"N","Ǌ":"NJ","ǋ":"Nj","Ⓞ":"O","Ｏ":"O","Ò":"O","Ó":"O","Ô":"O","Ồ":"O","Ố":"O","Ỗ":"O","Ổ":"O","Õ":"O","Ṍ":"O","Ȭ":"O","Ṏ":"O","Ō":"O","Ṑ":"O","Ṓ":"O","Ŏ":"O","Ȯ":"O","Ȱ":"O","Ö":"O","Ȫ":"O","Ỏ":"O","Ő":"O","Ǒ":"O","Ȍ":"O","Ȏ":"O","Ơ":"O","Ờ":"O","Ớ":"O","Ỡ":"O","Ở":"O","Ợ":"O","Ọ":"O","Ộ":"O","Ǫ":"O","Ǭ":"O","Ø":"O","Ǿ":"O","Ɔ":"O","Ɵ":"O","Ꝋ":"O","Ꝍ":"O","Ƣ":"OI","Ꝏ":"OO","Ȣ":"OU","Ⓟ":"P","Ｐ":"P","Ṕ":"P","Ṗ":"P","Ƥ":"P","Ᵽ":"P","Ꝑ":"P","Ꝓ":"P","Ꝕ":"P","Ⓠ":"Q","Ｑ":"Q","Ꝗ":"Q","Ꝙ":"Q","Ɋ":"Q","Ⓡ":"R","Ｒ":"R","Ŕ":"R","Ṙ":"R","Ř":"R","Ȑ":"R","Ȓ":"R","Ṛ":"R","Ṝ":"R","Ŗ":"R","Ṟ":"R","Ɍ":"R","Ɽ":"R","Ꝛ":"R","Ꞧ":"R","Ꞃ":"R","Ⓢ":"S","Ｓ":"S","ẞ":"S","Ś":"S","Ṥ":"S","Ŝ":"S","Ṡ":"S","Š":"S","Ṧ":"S","Ṣ":"S","Ṩ":"S","Ș":"S","Ş":"S","Ȿ":"S","Ꞩ":"S","Ꞅ":"S","Ⓣ":"T","Ｔ":"T","Ṫ":"T","Ť":"T","Ṭ":"T","Ț":"T","Ţ":"T","Ṱ":"T","Ṯ":"T","Ŧ":"T","Ƭ":"T","Ʈ":"T","Ⱦ":"T","Ꞇ":"T","Ꜩ":"TZ","Ⓤ":"U","Ｕ":"U","Ù":"U","Ú":"U","Û":"U","Ũ":"U","Ṹ":"U","Ū":"U","Ṻ":"U","Ŭ":"U","Ü":"U","Ǜ":"U","Ǘ":"U","Ǖ":"U","Ǚ":"U","Ủ":"U","Ů":"U","Ű":"U","Ǔ":"U","Ȕ":"U","Ȗ":"U","Ư":"U","Ừ":"U","Ứ":"U","Ữ":"U","Ử":"U","Ự":"U","Ụ":"U","Ṳ":"U","Ų":"U","Ṷ":"U","Ṵ":"U","Ʉ":"U","Ⓥ":"V","Ｖ":"V","Ṽ":"V","Ṿ":"V","Ʋ":"V","Ꝟ":"V","Ʌ":"V","Ꝡ":"VY","Ⓦ":"W","Ｗ":"W","Ẁ":"W","Ẃ":"W","Ŵ":"W","Ẇ":"W","Ẅ":"W","Ẉ":"W","Ⱳ":"W","Ⓧ":"X","Ｘ":"X","Ẋ":"X","Ẍ":"X","Ⓨ":"Y","Ｙ":"Y","Ỳ":"Y","Ý":"Y","Ŷ":"Y","Ỹ":"Y","Ȳ":"Y","Ẏ":"Y","Ÿ":"Y","Ỷ":"Y","Ỵ":"Y","Ƴ":"Y","Ɏ":"Y","Ỿ":"Y","Ⓩ":"Z","Ｚ":"Z","Ź":"Z","Ẑ":"Z","Ż":"Z","Ž":"Z","Ẓ":"Z","Ẕ":"Z","Ƶ":"Z","Ȥ":"Z","Ɀ":"Z","Ⱬ":"Z","Ꝣ":"Z","ⓐ":"a","ａ":"a","ẚ":"a","à":"a","á":"a","â":"a","ầ":"a","ấ":"a","ẫ":"a","ẩ":"a","ã":"a","ā":"a","ă":"a","ằ":"a","ắ":"a","ẵ":"a","ẳ":"a","ȧ":"a","ǡ":"a","ä":"a","ǟ":"a","ả":"a","å":"a","ǻ":"a","ǎ":"a","ȁ":"a","ȃ":"a","ạ":"a","ậ":"a","ặ":"a","ḁ":"a","ą":"a","ⱥ":"a","ɐ":"a","ꜳ":"aa","æ":"ae","ǽ":"ae","ǣ":"ae","ꜵ":"ao","ꜷ":"au","ꜹ":"av","ꜻ":"av","ꜽ":"ay","ⓑ":"b","ｂ":"b","ḃ":"b","ḅ":"b","ḇ":"b","ƀ":"b","ƃ":"b","ɓ":"b","ⓒ":"c","ｃ":"c","ć":"c","ĉ":"c","ċ":"c","č":"c","ç":"c","ḉ":"c","ƈ":"c","ȼ":"c","ꜿ":"c","ↄ":"c","ⓓ":"d","ｄ":"d","ḋ":"d","ď":"d","ḍ":"d","ḑ":"d","ḓ":"d","ḏ":"d","đ":"d","ƌ":"d","ɖ":"d","ɗ":"d","ꝺ":"d","ǳ":"dz","ǆ":"dz","ⓔ":"e","ｅ":"e","è":"e","é":"e","ê":"e","ề":"e","ế":"e","ễ":"e","ể":"e","ẽ":"e","ē":"e","ḕ":"e","ḗ":"e","ĕ":"e","ė":"e","ë":"e","ẻ":"e","ě":"e","ȅ":"e","ȇ":"e","ẹ":"e","ệ":"e","ȩ":"e","ḝ":"e","ę":"e","ḙ":"e","ḛ":"e","ɇ":"e","ɛ":"e","ǝ":"e","ⓕ":"f","ｆ":"f","ḟ":"f","ƒ":"f","ꝼ":"f","ⓖ":"g","ｇ":"g","ǵ":"g","ĝ":"g","ḡ":"g","ğ":"g","ġ":"g","ǧ":"g","ģ":"g","ǥ":"g","ɠ":"g","ꞡ":"g","ᵹ":"g","ꝿ":"g","ⓗ":"h","ｈ":"h","ĥ":"h","ḣ":"h","ḧ":"h","ȟ":"h","ḥ":"h","ḩ":"h","ḫ":"h","ẖ":"h","ħ":"h","ⱨ":"h","ⱶ":"h","ɥ":"h","ƕ":"hv","ⓘ":"i","ｉ":"i","ì":"i","í":"i","î":"i","ĩ":"i","ī":"i","ĭ":"i","ï":"i","ḯ":"i","ỉ":"i","ǐ":"i","ȉ":"i","ȋ":"i","ị":"i","į":"i","ḭ":"i","ɨ":"i","ı":"i","ⓙ":"j","ｊ":"j","ĵ":"j","ǰ":"j","ɉ":"j","ⓚ":"k","ｋ":"k","ḱ":"k","ǩ":"k","ḳ":"k","ķ":"k","ḵ":"k","ƙ":"k","ⱪ":"k","ꝁ":"k","ꝃ":"k","ꝅ":"k","ꞣ":"k","ⓛ":"l","ｌ":"l","ŀ":"l","ĺ":"l","ľ":"l","ḷ":"l","ḹ":"l","ļ":"l","ḽ":"l","ḻ":"l","ſ":"l","ł":"l","ƚ":"l","ɫ":"l","ⱡ":"l","ꝉ":"l","ꞁ":"l","ꝇ":"l","ǉ":"lj","ⓜ":"m","ｍ":"m","ḿ":"m","ṁ":"m","ṃ":"m","ɱ":"m","ɯ":"m","ⓝ":"n","ｎ":"n","ǹ":"n","ń":"n","ñ":"n","ṅ":"n","ň":"n","ṇ":"n","ņ":"n","ṋ":"n","ṉ":"n","ƞ":"n","ɲ":"n","ŉ":"n","ꞑ":"n","ꞥ":"n","ǌ":"nj","ⓞ":"o","ｏ":"o","ò":"o","ó":"o","ô":"o","ồ":"o","ố":"o","ỗ":"o","ổ":"o","õ":"o","ṍ":"o","ȭ":"o","ṏ":"o","ō":"o","ṑ":"o","ṓ":"o","ŏ":"o","ȯ":"o","ȱ":"o","ö":"o","ȫ":"o","ỏ":"o","ő":"o","ǒ":"o","ȍ":"o","ȏ":"o","ơ":"o","ờ":"o","ớ":"o","ỡ":"o","ở":"o","ợ":"o","ọ":"o","ộ":"o","ǫ":"o","ǭ":"o","ø":"o","ǿ":"o","ɔ":"o","ꝋ":"o","ꝍ":"o","ɵ":"o","ƣ":"oi","ȣ":"ou","ꝏ":"oo","ⓟ":"p","ｐ":"p","ṕ":"p","ṗ":"p","ƥ":"p","ᵽ":"p","ꝑ":"p","ꝓ":"p","ꝕ":"p","ⓠ":"q","ｑ":"q","ɋ":"q","ꝗ":"q","ꝙ":"q","ⓡ":"r","ｒ":"r","ŕ":"r","ṙ":"r","ř":"r","ȑ":"r","ȓ":"r","ṛ":"r","ṝ":"r","ŗ":"r","ṟ":"r","ɍ":"r","ɽ":"r","ꝛ":"r","ꞧ":"r","ꞃ":"r","ⓢ":"s","ｓ":"s","ß":"s","ś":"s","ṥ":"s","ŝ":"s","ṡ":"s","š":"s","ṧ":"s","ṣ":"s","ṩ":"s","ș":"s","ş":"s","ȿ":"s","ꞩ":"s","ꞅ":"s","ẛ":"s","ⓣ":"t","ｔ":"t","ṫ":"t","ẗ":"t","ť":"t","ṭ":"t","ț":"t","ţ":"t","ṱ":"t","ṯ":"t","ŧ":"t","ƭ":"t","ʈ":"t","ⱦ":"t","ꞇ":"t","ꜩ":"tz","ⓤ":"u","ｕ":"u","ù":"u","ú":"u","û":"u","ũ":"u","ṹ":"u","ū":"u","ṻ":"u","ŭ":"u","ü":"u","ǜ":"u","ǘ":"u","ǖ":"u","ǚ":"u","ủ":"u","ů":"u","ű":"u","ǔ":"u","ȕ":"u","ȗ":"u","ư":"u","ừ":"u","ứ":"u","ữ":"u","ử":"u","ự":"u","ụ":"u","ṳ":"u","ų":"u","ṷ":"u","ṵ":"u","ʉ":"u","ⓥ":"v","ｖ":"v","ṽ":"v","ṿ":"v","ʋ":"v","ꝟ":"v","ʌ":"v","ꝡ":"vy","ⓦ":"w","ｗ":"w","ẁ":"w","ẃ":"w","ŵ":"w","ẇ":"w","ẅ":"w","ẘ":"w","ẉ":"w","ⱳ":"w","ⓧ":"x","ｘ":"x","ẋ":"x","ẍ":"x","ⓨ":"y","ｙ":"y","ỳ":"y","ý":"y","ŷ":"y","ỹ":"y","ȳ":"y","ẏ":"y","ÿ":"y","ỷ":"y","ẙ":"y","ỵ":"y","ƴ":"y","ɏ":"y","ỿ":"y","ⓩ":"z","ｚ":"z","ź":"z","ẑ":"z","ż":"z","ž":"z","ẓ":"z","ẕ":"z","ƶ":"z","ȥ":"z","ɀ":"z","ⱬ":"z","ꝣ":"z","Ά":"Α","Έ":"Ε","Ή":"Η","Ί":"Ι","Ϊ":"Ι","Ό":"Ο","Ύ":"Υ","Ϋ":"Υ","Ώ":"Ω","ά":"α","έ":"ε","ή":"η","ί":"ι","ϊ":"ι","ΐ":"ι","ό":"ο","ύ":"υ","ϋ":"υ","ΰ":"υ","ω":"ω","ς":"σ"}})),t.define("select2/data/base",["../utils"],(function(e){function t(e,n){t.__super__.constructor.call(this)}return e.Extend(t,e.Observable),t.prototype.current=function(e){throw new Error("The `current` method must be defined in child classes.")},t.prototype.query=function(e,t){throw new Error("The `query` method must be defined in child classes.")},t.prototype.bind=function(e,t){},t.prototype.destroy=function(){},t.prototype.generateResultId=function(t,n){var o="";return o+=null!=t?t.id:e.generateChars(4),o+="-result-",o+=e.generateChars(4),null!=n.id?o+="-"+n.id.toString():o+="-"+e.generateChars(4),o},t})),t.define("select2/data/select",["./base","../utils","jquery"],(function(e,t,n){function o(e,t){this.$element=e,this.options=t,o.__super__.constructor.call(this)}return t.Extend(o,e),o.prototype.current=function(e){var t=[],o=this;this.$element.find(":selected").each((function(){var e=n(this),i=o.item(e);t.push(i)})),e(t)},o.prototype.select=function(e){var t=this;if(e.selected=!0,n(e.element).is("option"))return e.element.selected=!0,void this.$element.trigger("change");if(this.$element.prop("multiple"))this.current((function(o){var i=[];(e=[e]).push.apply(e,o);for(var a=0;a<e.length;a++){var r=e[a].id;-1===n.inArray(r,i)&&i.push(r)}t.$element.val(i),t.$element.trigger("change")}));else{var o=e.id;this.$element.val(o),this.$element.trigger("change")}},o.prototype.unselect=function(e){var t=this;if(this.$element.prop("multiple")){if(e.selected=!1,n(e.element).is("option"))return e.element.selected=!1,void this.$element.trigger("change");this.current((function(o){for(var i=[],a=0;a<o.length;a++){var r=o[a].id;r!==e.id&&-1===n.inArray(r,i)&&i.push(r)}t.$element.val(i),t.$element.trigger("change")}))}},o.prototype.bind=function(e,t){var n=this;this.container=e,e.on("select",(function(e){n.select(e.data)})),e.on("unselect",(function(e){n.unselect(e.data)}))},o.prototype.destroy=function(){this.$element.find("*").each((function(){n.removeData(this,"data")}))},o.prototype.query=function(e,t){var o=[],i=this;this.$element.children().each((function(){var t=n(this);if(t.is("option")||t.is("optgroup")){var a=i.item(t),r=i.matches(e,a);null!==r&&o.push(r)}})),t({results:o})},o.prototype.addOptions=function(e){t.appendMany(this.$element,e)},o.prototype.option=function(e){var t;e.children?(t=document.createElement("optgroup")).label=e.text:void 0!==(t=document.createElement("option")).textContent?t.textContent=e.text:t.innerText=e.text,void 0!==e.id&&(t.value=e.id),e.disabled&&(t.disabled=!0),e.selected&&(t.selected=!0),e.title&&(t.title=e.title);var o=n(t),i=this._normalizeItem(e);return i.element=t,n.data(t,"data",i),o},o.prototype.item=function(e){var t={};if(null!=(t=n.data(e[0],"data")))return t;if(e.is("option"))t={id:e.val(),text:e.text(),disabled:e.prop("disabled"),selected:e.prop("selected"),title:e.prop("title")};else if(e.is("optgroup")){t={text:e.prop("label"),children:[],title:e.prop("title")};for(var o=e.children("option"),i=[],a=0;a<o.length;a++){var r=n(o[a]),s=this.item(r);i.push(s)}t.children=i}return(t=this._normalizeItem(t)).element=e[0],n.data(e[0],"data",t),t},o.prototype._normalizeItem=function(e){n.isPlainObject(e)||(e={id:e,text:e});return null!=(e=n.extend({},{text:""},e)).id&&(e.id=e.id.toString()),null!=e.text&&(e.text=e.text.toString()),null==e._resultId&&e.id&&(e._resultId=this.generateResultId(this.container,e)),n.extend({},{selected:!1,disabled:!1},e)},o.prototype.matches=function(e,t){return this.options.get("matcher")(e,t)},o})),t.define("select2/data/array",["./select","../utils","jquery"],(function(e,t,n){function o(e,t){var n=t.get("data")||[];o.__super__.constructor.call(this,e,t),this.addOptions(this.convertToOptions(n))}return t.Extend(o,e),o.prototype.select=function(e){var t=this.$element.find("option").filter((function(t,n){return n.value==e.id.toString()}));0===t.length&&(t=this.option(e),this.addOptions(t)),o.__super__.select.call(this,e)},o.prototype.convertToOptions=function(e){var o=this,i=this.$element.find("option"),a=i.map((function(){return o.item(n(this)).id})).get(),r=[];function s(e){return function(){return n(this).val()==e.id}}for(var l=0;l<e.length;l++){var c=this._normalizeItem(e[l]);if(n.inArray(c.id,a)>=0){var d=i.filter(s(c)),u=this.item(d),p=n.extend(!0,{},c,u),f=this.option(p);d.replaceWith(f)}else{var h=this.option(c);if(c.children){var m=this.convertToOptions(c.children);t.appendMany(h,m)}r.push(h)}}return r},o})),t.define("select2/data/ajax",["./array","../utils","jquery"],(function(e,t,n){function o(e,t){this.ajaxOptions=this._applyDefaults(t.get("ajax")),null!=this.ajaxOptions.processResults&&(this.processResults=this.ajaxOptions.processResults),o.__super__.constructor.call(this,e,t)}return t.Extend(o,e),o.prototype._applyDefaults=function(e){var t={data:function(e){return n.extend({},e,{q:e.term})},transport:function(e,t,o){var i=n.ajax(e);return i.then(t),i.fail(o),i}};return n.extend({},t,e,!0)},o.prototype.processResults=function(e){return e},o.prototype.query=function(e,t){var o=this;null!=this._request&&(n.isFunction(this._request.abort)&&this._request.abort(),this._request=null);var i=n.extend({type:"GET"},this.ajaxOptions);function a(){var a=i.transport(i,(function(i){var a=o.processResults(i,e);o.options.get("debug")&&window.console&&console.error&&(a&&a.results&&n.isArray(a.results)||console.error("Select2: The AJAX results did not return an array in the `results` key of the response.")),t(a),o.container.focusOnActiveElement()}),(function(){a.status&&"0"===a.status||o.trigger("results:message",{message:"errorLoading"})}));o._request=a}"function"==typeof i.url&&(i.url=i.url.call(this.$element,e)),"function"==typeof i.data&&(i.data=i.data.call(this.$element,e)),this.ajaxOptions.delay&&null!=e.term?(this._queryTimeout&&window.clearTimeout(this._queryTimeout),this._queryTimeout=window.setTimeout(a,this.ajaxOptions.delay)):a()},o})),t.define("select2/data/tags",["jquery"],(function(e){function t(t,n,o){var i=o.get("tags"),a=o.get("createTag");void 0!==a&&(this.createTag=a);var r=o.get("insertTag");if(void 0!==r&&(this.insertTag=r),t.call(this,n,o),e.isArray(i))for(var s=0;s<i.length;s++){var l=i[s],c=this._normalizeItem(l),d=this.option(c);this.$element.append(d)}}return t.prototype.query=function(e,t,n){var o=this;this._removeOldTags(),null!=t.term&&null==t.page?e.call(this,t,(function e(i,a){for(var r=i.results,s=0;s<r.length;s++){var l=r[s],c=null!=l.children&&!e({results:l.children},!0);if((l.text||"").toUpperCase()===(t.term||"").toUpperCase()||c)return!a&&(i.data=r,void n(i))}if(a)return!0;var d=o.createTag(t);if(null!=d){var u=o.option(d);u.attr("data-select2-tag",!0),o.addOptions([u]),o.insertTag(r,d)}i.results=r,n(i)})):e.call(this,t,n)},t.prototype.createTag=function(t,n){var o=e.trim(n.term);return""===o?null:{id:o,text:o}},t.prototype.insertTag=function(e,t,n){t.unshift(n)},t.prototype._removeOldTags=function(t){this._lastTag,this.$element.find("option[data-select2-tag]").each((function(){this.selected||e(this).remove()}))},t})),t.define("select2/data/tokenizer",["jquery"],(function(e){function t(e,t,n){var o=n.get("tokenizer");void 0!==o&&(this.tokenizer=o),e.call(this,t,n)}return t.prototype.bind=function(e,t,n){e.call(this,t,n),this.$search=t.dropdown.$search||t.selection.$search||n.find(".select2-search__field")},t.prototype.query=function(t,n,o){var i=this;n.term=n.term||"";var a=this.tokenizer(n,this.options,(function(t){var n=i._normalizeItem(t);if(!i.$element.find("option").filter((function(){return e(this).val()===n.id})).length){var o=i.option(n);o.attr("data-select2-tag",!0),i._removeOldTags(),i.addOptions([o])}!function(e){i.trigger("select",{data:e})}(n)}));a.term!==n.term&&(this.$search.length&&(this.$search.val(a.term),this.$search.focus()),n.term=a.term),t.call(this,n,o)},t.prototype.tokenizer=function(t,n,o,i){for(var a=o.get("tokenSeparators")||[],r=n.term,s=0,l=this.createTag||function(e){return{id:e.term,text:e.term}};s<r.length;){var c=r[s];if(-1!==e.inArray(c,a)){var d=r.substr(0,s),u=l(e.extend({},n,{term:d}));null!=u?(i(u),r=r.substr(s+1)||"",s=0):s++}else s++}return{term:r}},t})),t.define("select2/data/minimumInputLength",[],(function(){function e(e,t,n){this.minimumInputLength=n.get("minimumInputLength"),e.call(this,t,n)}return e.prototype.query=function(e,t,n){t.term=t.term||"",t.term.length<this.minimumInputLength?this.trigger("results:message",{message:"inputTooShort",args:{minimum:this.minimumInputLength,input:t.term,params:t}}):e.call(this,t,n)},e})),t.define("select2/data/maximumInputLength",[],(function(){function e(e,t,n){this.maximumInputLength=n.get("maximumInputLength"),e.call(this,t,n)}return e.prototype.query=function(e,t,n){t.term=t.term||"",this.maximumInputLength>0&&t.term.length>this.maximumInputLength?this.trigger("results:message",{message:"inputTooLong",args:{maximum:this.maximumInputLength,input:t.term,params:t}}):e.call(this,t,n)},e})),t.define("select2/data/maximumSelectionLength",[],(function(){function e(e,t,n){this.maximumSelectionLength=n.get("maximumSelectionLength"),e.call(this,t,n)}return e.prototype.query=function(e,t,n){var o=this;this.current((function(i){var a=null!=i?i.length:0;o.maximumSelectionLength>0&&a>=o.maximumSelectionLength?o.trigger("results:message",{message:"maximumSelected",args:{maximum:o.maximumSelectionLength}}):e.call(o,t,n)}))},e})),t.define("select2/dropdown",["jquery","./utils"],(function(e,t){function n(e,t){this.$element=e,this.options=t,n.__super__.constructor.call(this)}return t.Extend(n,t.Observable),n.prototype.render=function(){var t=e('<span class="select2-dropdown"><span class="select2-results"></span></span>');return t.attr("dir",this.options.get("dir")),this.$dropdown=t,t},n.prototype.bind=function(){},n.prototype.position=function(e,t){},n.prototype.destroy=function(){this.$dropdown.remove()},n})),t.define("select2/dropdown/search",["jquery","../utils"],(function(e,t){function n(){}return n.prototype.render=function(t){var n=t.call(this),o=e('<span class="select2-search select2-search--dropdown"><input class="select2-search__field" type="text" tabindex="-1" autocomplete="off" autocorrect="off" autocapitalize="none" spellcheck="false" role="combobox" aria-autocomplete="list" aria-expanded="true" /></span>');return this.$searchContainer=o,this.$search=o.find("input"),n.prepend(o),n},n.prototype.bind=function(t,n,o){var i=this,a=n.id+"-results";t.call(this,n,o),this.$search.on("keydown",(function(e){i.trigger("keypress",e),i._keyUpPrevented=e.isDefaultPrevented()})),this.$search.on("input",(function(t){e(this).off("keyup")})),this.$search.on("keyup input",(function(e){i.handleSearch(e)})),n.on("open",(function(){i.$search.attr("tabindex",0),i.$search.attr("aria-owns",a),i.$search.focus(),window.setTimeout((function(){i.$search.focus()}),0)})),n.on("close",(function(){i.$search.attr("tabindex",-1),i.$search.removeAttr("aria-activedescendant"),i.$search.removeAttr("aria-owns"),i.$search.val("")})),n.on("focus",(function(){n.isOpen()||i.$search.focus()})),n.on("results:all",(function(e){null!=e.query.term&&""!==e.query.term||(i.showSearch(e)?i.$searchContainer.removeClass("select2-search--hide"):i.$searchContainer.addClass("select2-search--hide"))})),n.on("results:focus",(function(e){i.$search.attr("aria-activedescendant",e.data._resultId)}))},n.prototype.handleSearch=function(e){if(!this._keyUpPrevented){var t=this.$search.val();this.trigger("query",{term:t})}this._keyUpPrevented=!1},n.prototype.showSearch=function(e,t){return!0},n})),t.define("select2/dropdown/hidePlaceholder",[],(function(){function e(e,t,n,o){this.placeholder=this.normalizePlaceholder(n.get("placeholder")),e.call(this,t,n,o)}return e.prototype.append=function(e,t){t.results=this.removePlaceholder(t.results),e.call(this,t)},e.prototype.normalizePlaceholder=function(e,t){return"string"==typeof t&&(t={id:"",text:t}),t},e.prototype.removePlaceholder=function(e,t){for(var n=t.slice(0),o=t.length-1;o>=0;o--){var i=t[o];this.placeholder.id===i.id&&n.splice(o,1)}return n},e})),t.define("select2/dropdown/infiniteScroll",["jquery"],(function(e){function t(e,t,n,o){this.lastParams={},e.call(this,t,n,o),this.$loadingMore=this.createLoadingMore(),this.loading=!1}return t.prototype.append=function(e,t){this.$loadingMore.remove(),this.loading=!1,e.call(this,t),this.showLoadingMore(t)&&this.$results.append(this.$loadingMore)},t.prototype.bind=function(t,n,o){var i=this;t.call(this,n,o),n.on("query",(function(e){i.lastParams=e,i.loading=!0})),n.on("query:append",(function(e){i.lastParams=e,i.loading=!0})),this.$results.on("scroll",(function(){var t=e.contains(document.documentElement,i.$loadingMore[0]);!i.loading&&t&&i.$results.offset().top+i.$results.outerHeight(!1)+50>=i.$loadingMore.offset().top+i.$loadingMore.outerHeight(!1)&&i.loadMore()}))},t.prototype.loadMore=function(){this.loading=!0;var t=e.extend({},{page:1},this.lastParams);t.page++,this.trigger("query:append",t)},t.prototype.showLoadingMore=function(e,t){return t.pagination&&t.pagination.more},t.prototype.createLoadingMore=function(){var t=e('<li class="select2-results__option select2-results__option--load-more"role="option" aria-disabled="true"></li>'),n=this.options.get("translations").get("loadingMore");return t.html(n(this.lastParams)),t},t})),t.define("select2/dropdown/attachBody",["jquery","../utils"],(function(e,t){function n(t,n,o){this.$dropdownParent=o.get("dropdownParent")||e(document.body),t.call(this,n,o)}return n.prototype.bind=function(e,t,n){var o=this,i=!1;e.call(this,t,n),t.on("open",(function(){o._showDropdown(),o._attachPositioningHandler(t),i||(i=!0,t.on("results:all",(function(){o._positionDropdown(),o._resizeDropdown()})),t.on("results:append",(function(){o._positionDropdown(),o._resizeDropdown()})))})),t.on("close",(function(){o._hideDropdown(),o._detachPositioningHandler(t)})),this.$dropdownContainer.on("mousedown",(function(e){e.stopPropagation()}))},n.prototype.destroy=function(e){e.call(this),this.$dropdownContainer.remove()},n.prototype.position=function(e,t,n){t.attr("class",n.attr("class")),t.removeClass("select2"),t.addClass("select2-container--open"),t.css({position:"absolute",top:-999999}),this.$container=n},n.prototype.render=function(t){var n=e("<span></span>"),o=t.call(this);return n.append(o),this.$dropdownContainer=n,n},n.prototype._hideDropdown=function(e){this.$dropdownContainer.detach()},n.prototype._attachPositioningHandler=function(n,o){var i=this,a="scroll.select2."+o.id,r="resize.select2."+o.id,s="orientationchange.select2."+o.id,l=this.$container.parents().filter(t.hasScroll);l.each((function(){e(this).data("select2-scroll-position",{x:e(this).scrollLeft(),y:e(this).scrollTop()})})),l.on(a,(function(t){var n=e(this).data("select2-scroll-position");e(this).scrollTop(n.y)})),e(window).on(a+" "+r+" "+s,(function(e){i._positionDropdown(),i._resizeDropdown()}))},n.prototype._detachPositioningHandler=function(n,o){var i="scroll.select2."+o.id,a="resize.select2."+o.id,r="orientationchange.select2."+o.id;this.$container.parents().filter(t.hasScroll).off(i),e(window).off(i+" "+a+" "+r)},n.prototype._positionDropdown=function(){var t=e(window),n=this.$dropdown.hasClass("select2-dropdown--above"),o=this.$dropdown.hasClass("select2-dropdown--below"),i=null,a=this.$container.offset();a.bottom=a.top+this.$container.outerHeight(!1);var r={height:this.$container.outerHeight(!1)};r.top=a.top,r.bottom=a.top+r.height;var s=this.$dropdown.outerHeight(!1),l=t.scrollTop(),c=t.scrollTop()+t.height(),d=l<a.top-s,u=c>a.bottom+s,p={left:a.left,top:r.bottom},f=this.$dropdownParent;"static"===f.css("position")&&(f=f.offsetParent());var h=f.offset();p.top-=h.top,p.left-=h.left,n||o||(i="below"),u||!d||n?!d&&u&&n&&(i="below"):i="above",("above"==i||n&&"below"!==i)&&(p.top=r.top-h.top-s),null!=i&&(this.$dropdown.removeClass("select2-dropdown--below select2-dropdown--above").addClass("select2-dropdown--"+i),this.$container.removeClass("select2-container--below select2-container--above").addClass("select2-container--"+i)),this.$dropdownContainer.css(p)},n.prototype._resizeDropdown=function(){var e={width:this.$container.outerWidth(!1)+"px"};this.options.get("dropdownAutoWidth")&&(e.minWidth=e.width,e.position="relative",e.width="auto"),this.$dropdown.css(e)},n.prototype._showDropdown=function(e){this.$dropdownContainer.appendTo(this.$dropdownParent),this._positionDropdown(),this._resizeDropdown()},n})),t.define("select2/dropdown/minimumResultsForSearch",[],(function(){function e(t){for(var n=0,o=0;o<t.length;o++){var i=t[o];i.children?n+=e(i.children):n++}return n}function t(e,t,n,o){this.minimumResultsForSearch=n.get("minimumResultsForSearch"),this.minimumResultsForSearch<0&&(this.minimumResultsForSearch=1/0),e.call(this,t,n,o)}return t.prototype.showSearch=function(t,n){return!(e(n.data.results)<this.minimumResultsForSearch)&&t.call(this,n)},t})),t.define("select2/dropdown/selectOnClose",[],(function(){function e(){}return e.prototype.bind=function(e,t,n){var o=this;e.call(this,t,n),t.on("close",(function(e){o._handleSelectOnClose(e)}))},e.prototype._handleSelectOnClose=function(e,t){if(t&&null!=t.originalSelect2Event){var n=t.originalSelect2Event;if("select"===n._type||"unselect"===n._type)return}var o=this.getHighlightedResults();if(!(o.length<1)){var i=o.data("data");null!=i.element&&i.element.selected||null==i.element&&i.selected||this.trigger("select",{data:i})}},e})),t.define("select2/dropdown/closeOnSelect",[],(function(){function e(){}return e.prototype.bind=function(e,t,n){var o=this;e.call(this,t,n),t.on("select",(function(e){o._selectTriggered(e)})),t.on("unselect",(function(e){o._selectTriggered(e)}))},e.prototype._selectTriggered=function(e,t){var n=t.originalEvent;n&&n.ctrlKey||this.trigger("close",{originalEvent:n,originalSelect2Event:t})},e})),t.define("select2/i18n/en",[],(function(){return{errorLoading:function(){return"The results could not be loaded."},inputTooLong:function(e){var t=e.input.length-e.maximum,n="Please delete "+t+" character";return 1!=t&&(n+="s"),n},inputTooShort:function(e){return"Please enter "+(e.minimum-e.input.length)+" or more characters"},loadingMore:function(){return"Loading more results…"},maximumSelected:function(e){var t="You can only select "+e.maximum+" item";return 1!=e.maximum&&(t+="s"),t},noResults:function(){return"No results found"},searching:function(){return"Searching…"}}})),t.define("select2/defaults",["jquery","require","./results","./selection/single","./selection/multiple","./selection/placeholder","./selection/allowClear","./selection/search","./selection/eventRelay","./utils","./translation","./diacritics","./data/select","./data/array","./data/ajax","./data/tags","./data/tokenizer","./data/minimumInputLength","./data/maximumInputLength","./data/maximumSelectionLength","./dropdown","./dropdown/search","./dropdown/hidePlaceholder","./dropdown/infiniteScroll","./dropdown/attachBody","./dropdown/minimumResultsForSearch","./dropdown/selectOnClose","./dropdown/closeOnSelect","./i18n/en"],(function(e,t,n,o,i,a,r,s,l,c,d,u,p,f,h,m,g,w,v,_,y,b,C,x,k,D,A,E,M){function T(){this.reset()}return T.prototype.apply=function(u){if(null==(u=e.extend(!0,{},this.defaults,u)).dataAdapter){if(null!=u.ajax?u.dataAdapter=h:null!=u.data?u.dataAdapter=f:u.dataAdapter=p,u.minimumInputLength>0&&(u.dataAdapter=c.Decorate(u.dataAdapter,w)),u.maximumInputLength>0&&(u.dataAdapter=c.Decorate(u.dataAdapter,v)),u.maximumSelectionLength>0&&(u.dataAdapter=c.Decorate(u.dataAdapter,_)),u.tags&&(u.dataAdapter=c.Decorate(u.dataAdapter,m)),null==u.tokenSeparators&&null==u.tokenizer||(u.dataAdapter=c.Decorate(u.dataAdapter,g)),null!=u.query){var M=t(u.amdBase+"compat/query");u.dataAdapter=c.Decorate(u.dataAdapter,M)}if(null!=u.initSelection){var T=t(u.amdBase+"compat/initSelection");u.dataAdapter=c.Decorate(u.dataAdapter,T)}}if(null==u.resultsAdapter&&(u.resultsAdapter=n,null!=u.ajax&&(u.resultsAdapter=c.Decorate(u.resultsAdapter,x)),null!=u.placeholder&&(u.resultsAdapter=c.Decorate(u.resultsAdapter,C)),u.selectOnClose&&(u.resultsAdapter=c.Decorate(u.resultsAdapter,A))),null==u.dropdownAdapter){if(u.multiple)u.dropdownAdapter=y;else{var $=c.Decorate(y,b);u.dropdownAdapter=$}if(0!==u.minimumResultsForSearch&&(u.dropdownAdapter=c.Decorate(u.dropdownAdapter,D)),u.closeOnSelect&&(u.dropdownAdapter=c.Decorate(u.dropdownAdapter,E)),null!=u.dropdownCssClass||null!=u.dropdownCss||null!=u.adaptDropdownCssClass){var S=t(u.amdBase+"compat/dropdownCss");u.dropdownAdapter=c.Decorate(u.dropdownAdapter,S)}u.dropdownAdapter=c.Decorate(u.dropdownAdapter,k)}if(null==u.selectionAdapter){if(u.multiple?u.selectionAdapter=i:u.selectionAdapter=o,null!=u.placeholder&&(u.selectionAdapter=c.Decorate(u.selectionAdapter,a)),u.allowClear&&(u.selectionAdapter=c.Decorate(u.selectionAdapter,r)),u.multiple&&(u.selectionAdapter=c.Decorate(u.selectionAdapter,s)),null!=u.containerCssClass||null!=u.containerCss||null!=u.adaptContainerCssClass){var O=t(u.amdBase+"compat/containerCss");u.selectionAdapter=c.Decorate(u.selectionAdapter,O)}u.selectionAdapter=c.Decorate(u.selectionAdapter,l)}if("string"==typeof u.language)if(u.language.indexOf("-")>0){var P=u.language.split("-")[0];u.language=[u.language,P]}else u.language=[u.language];if(e.isArray(u.language)){var j=new d;u.language.push("en");for(var I=u.language,L=0;L<I.length;L++){var B=I[L],H={};try{H=d.loadPath(B)}catch(e){try{B=this.defaults.amdLanguageBase+B,H=d.loadPath(B)}catch(e){u.debug&&window.console&&console.warn&&console.warn('Select2: The language file for "'+B+'" could not be automatically loaded. A fallback will be used instead.');continue}}j.extend(H)}u.translations=j}else{var N=d.loadPath(this.defaults.amdLanguageBase+"en"),q=new d(u.language);q.extend(N),u.translations=q}return u},T.prototype.reset=function(){function t(e){return e.replace(/[^\u0000-\u007E]/g,(function(e){return u[e]||e}))}this.defaults={amdBase:"./",amdLanguageBase:"./i18n/",closeOnSelect:!0,debug:!1,dropdownAutoWidth:!1,escapeMarkup:c.escapeMarkup,language:M,matcher:function n(o,i){if(""===e.trim(o.term))return i;if(i.children&&i.children.length>0){for(var a=e.extend(!0,{},i),r=i.children.length-1;r>=0;r--)null==n(o,i.children[r])&&a.children.splice(r,1);return a.children.length>0?a:n(o,a)}var s=t(i.text).toUpperCase(),l=t(o.term).toUpperCase();return s.indexOf(l)>-1?i:null},minimumInputLength:0,maximumInputLength:0,maximumSelectionLength:0,minimumResultsForSearch:0,selectOnClose:!1,sorter:function(e){return e},templateResult:function(e){return e.text},templateSelection:function(e){return e.text},theme:"default",width:"resolve"}},T.prototype.set=function(t,n){var o={};o[e.camelCase(t)]=n;var i=c._convertData(o);e.extend(this.defaults,i)},new T})),t.define("select2/options",["require","jquery","./defaults","./utils"],(function(e,t,n,o){function i(t,i){if(this.options=t,null!=i&&this.fromElement(i),this.options=n.apply(this.options),i&&i.is("input")){var a=e(this.get("amdBase")+"compat/inputData");this.options.dataAdapter=o.Decorate(this.options.dataAdapter,a)}}return i.prototype.fromElement=function(e){var n=["select2"];null==this.options.multiple&&(this.options.multiple=e.prop("multiple")),null==this.options.disabled&&(this.options.disabled=e.prop("disabled")),null==this.options.language&&(e.prop("lang")?this.options.language=e.prop("lang").toLowerCase():e.closest("[lang]").prop("lang")&&(this.options.language=e.closest("[lang]").prop("lang"))),null==this.options.dir&&(e.prop("dir")?this.options.dir=e.prop("dir"):e.closest("[dir]").prop("dir")?this.options.dir=e.closest("[dir]").prop("dir"):this.options.dir="ltr"),e.prop("disabled",this.options.disabled),e.prop("multiple",this.options.multiple),e.data("select2Tags")&&(this.options.debug&&window.console&&console.warn&&console.warn('Select2: The `data-select2-tags` attribute has been changed to use the `data-data` and `data-tags="true"` attributes and will be removed in future versions of Select2.'),e.data("data",e.data("select2Tags")),e.data("tags",!0)),e.data("ajaxUrl")&&(this.options.debug&&window.console&&console.warn&&console.warn("Select2: The `data-ajax-url` attribute has been changed to `data-ajax--url` and support for the old attribute will be removed in future versions of Select2."),e.attr("ajax--url",e.data("ajaxUrl")),e.data("ajax--url",e.data("ajaxUrl")));var i;i=t.fn.jquery&&"1."==t.fn.jquery.substr(0,2)&&e[0].dataset?t.extend(!0,{},e[0].dataset,e.data()):e.data();var a=t.extend(!0,{},i);for(var r in a=o._convertData(a))t.inArray(r,n)>-1||(t.isPlainObject(this.options[r])?t.extend(this.options[r],a[r]):this.options[r]=a[r]);return this},i.prototype.get=function(e){return this.options[e]},i.prototype.set=function(e,t){this.options[e]=t},i})),t.define("select2/core",["jquery","./options","./utils","./keys"],(function(e,t,n,o){var i=function(e,n){null!=e.data("select2")&&e.data("select2").destroy(),this.$element=e,this.id=this._generateId(e),n=n||{},this.options=new t(n,e),i.__super__.constructor.call(this);var o=e.attr("tabindex")||0;e.data("old-tabindex",o),e.attr("tabindex","-1");var a=this.options.get("dataAdapter");this.dataAdapter=new a(e,this.options);var r=this.render();this._placeContainer(r);var s=this.options.get("selectionAdapter");this.selection=new s(e,this.options),this.$selection=this.selection.render(),this.selection.position(this.$selection,r);var l=this.options.get("dropdownAdapter");this.dropdown=new l(e,this.options),this.$dropdown=this.dropdown.render(),this.dropdown.position(this.$dropdown,r);var c=this.options.get("resultsAdapter");this.results=new c(e,this.options,this.dataAdapter),this.$results=this.results.render(),this.results.position(this.$results,this.$dropdown);var d=this;this._bindAdapters(),this._registerDomEvents(),this._registerDataEvents(),this._registerSelectionEvents(),this._registerDropdownEvents(),this._registerResultsEvents(),this._registerEvents(),this.dataAdapter.current((function(e){d.trigger("selection:update",{data:e})})),e.addClass("select2-hidden-accessible"),e.attr("aria-hidden","true"),this._syncAttributes(),e.data("select2",this)};return n.Extend(i,n.Observable),i.prototype._generateId=function(e){return"select2-"+(null!=e.attr("id")?e.attr("id"):null!=e.attr("name")?e.attr("name")+"-"+n.generateChars(2):n.generateChars(4)).replace(/(:|\.|\[|\]|,)/g,"")},i.prototype._placeContainer=function(e){e.insertAfter(this.$element);var t=this._resolveWidth(this.$element,this.options.get("width"));null!=t&&e.css("width",t)},i.prototype._resolveWidth=function(e,t){var n=/^width:(([-+]?([0-9]*\.)?[0-9]+)(px|em|ex|%|in|cm|mm|pt|pc))/i;if("resolve"==t){var o=this._resolveWidth(e,"style");return null!=o?o:this._resolveWidth(e,"element")}if("element"==t){var i=e.outerWidth(!1);return i<=0?"auto":i+"px"}if("style"==t){var a=e.attr("style");if("string"!=typeof a)return null;for(var r=a.split(";"),s=0,l=r.length;s<l;s+=1){var c=r[s].replace(/\s/g,"").match(n);if(null!==c&&c.length>=1)return c[1]}return null}return t},i.prototype._bindAdapters=function(){this.dataAdapter.bind(this,this.$container),this.selection.bind(this,this.$container),this.dropdown.bind(this,this.$container),this.results.bind(this,this.$container)},i.prototype._registerDomEvents=function(){var t=this;this.$element.on("change.select2",(function(){t.dataAdapter.current((function(e){t.trigger("selection:update",{data:e})}))})),this.$element.on("focus.select2",(function(e){t.trigger("focus",e)})),this._syncA=n.bind(this._syncAttributes,this),this._syncS=n.bind(this._syncSubtree,this),this.$element[0].attachEvent&&this.$element[0].attachEvent("onpropertychange",this._syncA);var o=window.MutationObserver||window.WebKitMutationObserver||window.MozMutationObserver;null!=o?(this._observer=new o((function(n){e.each(n,t._syncA),e.each(n,t._syncS)})),this._observer.observe(this.$element[0],{attributes:!0,childList:!0,subtree:!1})):this.$element[0].addEventListener&&(this.$element[0].addEventListener("DOMAttrModified",t._syncA,!1),this.$element[0].addEventListener("DOMNodeInserted",t._syncS,!1),this.$element[0].addEventListener("DOMNodeRemoved",t._syncS,!1))},i.prototype._registerDataEvents=function(){var e=this;this.dataAdapter.on("*",(function(t,n){e.trigger(t,n)}))},i.prototype._registerSelectionEvents=function(){var t=this,n=["toggle","focus"];this.selection.on("toggle",(function(){t.toggleDropdown()})),this.selection.on("focus",(function(e){t.focus(e)})),this.selection.on("*",(function(o,i){-1===e.inArray(o,n)&&t.trigger(o,i)}))},i.prototype._registerDropdownEvents=function(){var e=this;this.dropdown.on("*",(function(t,n){e.trigger(t,n)}))},i.prototype._registerResultsEvents=function(){var e=this;this.results.on("*",(function(t,n){e.trigger(t,n)}))},i.prototype._registerEvents=function(){var t=this;this.on("open",(function(){t.$container.addClass("select2-container--open")})),this.on("close",(function(){t.$container.removeClass("select2-container--open")})),this.on("enable",(function(){t.$container.removeClass("select2-container--disabled")})),this.on("disable",(function(){t.$container.addClass("select2-container--disabled")})),this.on("blur",(function(){t.$container.removeClass("select2-container--focus")})),this.on("query",(function(e){t.isOpen()||t.trigger("open",{}),this.dataAdapter.query(e,(function(n){t.trigger("results:all",{data:n,query:e})}))})),this.on("query:append",(function(e){this.dataAdapter.query(e,(function(n){t.trigger("results:append",{data:n,query:e})}))})),this.on("open",(function(){setTimeout((function(){t.focusOnActiveElement()}),1)})),e(document).on("keydown",(function(e){var n=e.which;if(t.isOpen()){n===o.ESC||n===o.TAB||n===o.UP&&e.altKey?(t.close(),e.preventDefault()):n===o.ENTER?(t.trigger("results:select",{}),e.preventDefault()):n===o.SPACE&&e.ctrlKey?(t.trigger("results:toggle",{}),e.preventDefault()):n===o.UP?(t.trigger("results:previous",{}),e.preventDefault()):n===o.DOWN&&(t.trigger("results:next",{}),e.preventDefault());var i=t.$dropdown.find(".select2-search__field");i.length||(i=t.$container.find(".select2-search__field")),n===o.DOWN||n===o.UP?t.focusOnActiveElement():(i.focus(),setTimeout((function(){t.focusOnActiveElement()}),1e3))}else t.hasFocus()&&(n!==o.ENTER&&n!==o.SPACE&&n!==o.DOWN||(t.open(),e.preventDefault()))}))},i.prototype.focusOnActiveElement=function(){this.isOpen()&&!n.isTouchscreen()&&this.$results.find("li.select2-results__option--highlighted").focus()},i.prototype._syncAttributes=function(){this.options.set("disabled",this.$element.prop("disabled")),this.options.get("disabled")?(this.isOpen()&&this.close(),this.trigger("disable",{})):this.trigger("enable",{})},i.prototype._syncSubtree=function(e,t){var n=!1,o=this;if(!e||!e.target||"OPTION"===e.target.nodeName||"OPTGROUP"===e.target.nodeName){if(t)if(t.addedNodes&&t.addedNodes.length>0)for(var i=0;i<t.addedNodes.length;i++)t.addedNodes[i].selected&&(n=!0);else t.removedNodes&&t.removedNodes.length>0&&(n=!0);else n=!0;n&&this.dataAdapter.current((function(e){o.trigger("selection:update",{data:e})}))}},i.prototype.trigger=function(e,t){var n=i.__super__.trigger,o={open:"opening",close:"closing",select:"selecting",unselect:"unselecting"};if(void 0===t&&(t={}),e in o){var a=o[e],r={prevented:!1,name:e,args:t};if(n.call(this,a,r),r.prevented)return void(t.prevented=!0)}n.call(this,e,t)},i.prototype.toggleDropdown=function(){this.options.get("disabled")||(this.isOpen()?this.close():this.open())},i.prototype.open=function(){this.isOpen()||this.trigger("query",{})},i.prototype.close=function(){this.isOpen()&&this.trigger("close",{})},i.prototype.isOpen=function(){return this.$container.hasClass("select2-container--open")},i.prototype.hasFocus=function(){return this.$container.hasClass("select2-container--focus")},i.prototype.focus=function(e){this.hasFocus()||(this.$container.addClass("select2-container--focus"),this.trigger("focus",{}))},i.prototype.enable=function(e){this.options.get("debug")&&window.console&&console.warn&&console.warn('Select2: The `select2("enable")` method has been deprecated and will be removed in later Select2 versions. Use $element.prop("disabled") instead.'),null!=e&&0!==e.length||(e=[!0]);var t=!e[0];this.$element.prop("disabled",t)},i.prototype.data=function(){this.options.get("debug")&&arguments.length>0&&window.console&&console.warn&&console.warn('Select2: Data can no longer be set using `select2("data")`. You should consider setting the value instead using `$element.val()`.');var e=[];return this.dataAdapter.current((function(t){e=t})),e},i.prototype.val=function(t){if(this.options.get("debug")&&window.console&&console.warn&&console.warn('Select2: The `select2("val")` method has been deprecated and will be removed in later Select2 versions. Use $element.val() instead.'),null==t||0===t.length)return this.$element.val();var n=t[0];e.isArray(n)&&(n=e.map(n,(function(e){return e.toString()}))),this.$element.val(n).trigger("change")},i.prototype.destroy=function(){this.$container.remove(),this.$element[0].detachEvent&&this.$element[0].detachEvent("onpropertychange",this._syncA),null!=this._observer?(this._observer.disconnect(),this._observer=null):this.$element[0].removeEventListener&&(this.$element[0].removeEventListener("DOMAttrModified",this._syncA,!1),this.$element[0].removeEventListener("DOMNodeInserted",this._syncS,!1),this.$element[0].removeEventListener("DOMNodeRemoved",this._syncS,!1)),this._syncA=null,this._syncS=null,this.$element.off(".select2"),this.$element.attr("tabindex",this.$element.data("old-tabindex")),this.$element.removeClass("select2-hidden-accessible"),this.$element.attr("aria-hidden","false"),this.$element.removeData("select2"),this.dataAdapter.destroy(),this.selection.destroy(),this.dropdown.destroy(),this.results.destroy(),this.dataAdapter=null,this.selection=null,this.dropdown=null,this.results=null},i.prototype.render=function(){var t=e('<span class="select2 select2-container"><span class="selection"></span><span class="dropdown-wrapper" aria-hidden="true"></span></span>');return t.attr("dir",this.options.get("dir")),this.$container=t,this.$container.addClass("select2-container--"+this.options.get("theme")),t.data("element",this.$element),t},i})),t.define("jquery-mousewheel",["jquery"],(function(e){return e})),t.define("jquery.select2",["jquery","jquery-mousewheel","./select2/core","./select2/defaults"],(function(e,t,n,o){if(null==e.fn.selectWoo){var i=["open","close","destroy"];e.fn.selectWoo=function(t){if("object"==typeof(t=t||{}))return this.each((function(){var o=e.extend(!0,{},t);new n(e(this),o)})),this;if("string"==typeof t){var o,a=Array.prototype.slice.call(arguments,1);return this.each((function(){var n=e(this).data("select2");null==n&&window.console&&console.error&&console.error("The select2('"+t+"') method was called on an element that is not using Select2."),o=n[t].apply(n,a)})),e.inArray(t,i)>-1?this:o}throw new Error("Invalid arguments for Select2: "+t)}}return null!=e.fn.select2&&null!=e.fn.select2.defaults&&(e.fn.selectWoo.defaults=e.fn.select2.defaults),null==e.fn.selectWoo.defaults&&(e.fn.selectWoo.defaults=o),e.fn.select2=e.fn.select2||e.fn.selectWoo,n})),{define:t.define,require:t.require}}(),n=t.require("jquery.select2");return e.fn.select2.amd=t,e.fn.selectWoo.amd=t,n},void 0===(a=o.apply(t,i))||(e.exports=a)},455:function(e){e.exports=function(){"use strict";function e(t){return e="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},e(t)}function t(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function n(e,t){for(var n=0;n<t.length;n++){var o=t[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,o.key,o)}}function o(e,t,o){return t&&n(e.prototype,t),o&&n(e,o),e}function i(){return i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var o in n)Object.prototype.hasOwnProperty.call(n,o)&&(e[o]=n[o])}return e},i.apply(this,arguments)}function a(e){return a=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)},a(e)}function r(e,t){return r=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e},r(e,t)}function s(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}function l(e,t,n){return l=s()?Reflect.construct:function(e,t,n){var o=[null];o.push.apply(o,t);var i=new(Function.bind.apply(e,o));return n&&r(i,n.prototype),i},l.apply(null,arguments)}function c(e,t){return!t||"object"!=typeof t&&"function"!=typeof t?function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e):t}function d(e,t,n){return d="undefined"!=typeof Reflect&&Reflect.get?Reflect.get:function(e,t,n){var o=function(e,t){for(;!Object.prototype.hasOwnProperty.call(e,t)&&null!==(e=a(e)););return e}(e,t);if(o){var i=Object.getOwnPropertyDescriptor(o,t);return i.get?i.get.call(n):i.value}},d(e,t,n||e)}var u="SweetAlert2:",p=function(e){return e.charAt(0).toUpperCase()+e.slice(1)},f=function(e){return Object.keys(e).map((function(t){return e[t]}))},h=function(e){return Array.prototype.slice.call(e)},m=function(t){console.warn("".concat(u," ").concat("object"===e(t)?t.join(" "):t))},g=function(e){console.error("".concat(u," ").concat(e))},w=[],v=function(e,t){var n;n='"'.concat(e,'" is deprecated and will be removed in the next major release. Please use "').concat(t,'" instead.'),-1===w.indexOf(n)&&(w.push(n),m(n))},_=function(e){return"function"==typeof e?e():e},y=function(e){return e&&"function"==typeof e.toPromise},b=function(e){return y(e)?e.toPromise():Promise.resolve(e)},C=function(e){return e&&Promise.resolve(e)===e},x=Object.freeze({cancel:"cancel",backdrop:"backdrop",close:"close",esc:"esc",timer:"timer"}),k=function(t){return t instanceof Element||function(t){return"object"===e(t)&&t.jquery}(t)},D=function(e){var t={};for(var n in e)t[e[n]]="swal2-"+e[n];return t},A=D(["container","shown","height-auto","iosfix","popup","modal","no-backdrop","no-transition","toast","toast-shown","show","hide","close","title","header","content","html-container","actions","confirm","deny","cancel","footer","icon","icon-content","image","input","file","range","select","radio","checkbox","label","textarea","inputerror","input-label","validation-message","progress-steps","active-progress-step","progress-step","progress-step-line","loader","loading","styled","top","top-start","top-end","top-left","top-right","center","center-start","center-end","center-left","center-right","bottom","bottom-start","bottom-end","bottom-left","bottom-right","grow-row","grow-column","grow-fullscreen","rtl","timer-progress-bar","timer-progress-bar-container","scrollbar-measure","icon-success","icon-warning","icon-info","icon-question","icon-error"]),E=D(["success","warning","info","question","error"]),M=function(){return document.body.querySelector(".".concat(A.container))},T=function(e){var t=M();return t?t.querySelector(e):null},$=function(e){return T(".".concat(e))},S=function(){return $(A.popup)},O=function(){return $(A.icon)},P=function(){return $(A.title)},j=function(){return $(A.content)},I=function(){return $(A["html-container"])},L=function(){return $(A.image)},B=function(){return $(A["progress-steps"])},H=function(){return $(A["validation-message"])},N=function(){return T(".".concat(A.actions," .").concat(A.confirm))},q=function(){return T(".".concat(A.actions," .").concat(A.deny))},F=function(){return T(".".concat(A.loader))},W=function(){return T(".".concat(A.actions," .").concat(A.cancel))},z=function(){return $(A.actions)},Y=function(){return $(A.header)},R=function(){return $(A.footer)},U=function(){return $(A["timer-progress-bar"])},V=function(){return $(A.close)},K=function(){var e=h(S().querySelectorAll('[tabindex]:not([tabindex="-1"]):not([tabindex="0"])')).sort((function(e,t){return(e=parseInt(e.getAttribute("tabindex")))>(t=parseInt(t.getAttribute("tabindex")))?1:e<t?-1:0})),t=h(S().querySelectorAll('\n  a[href],\n  area[href],\n  input:not([disabled]),\n  select:not([disabled]),\n  textarea:not([disabled]),\n  button:not([disabled]),\n  iframe,\n  object,\n  embed,\n  [tabindex="0"],\n  [contenteditable],\n  audio[controls],\n  video[controls],\n  summary\n')).filter((function(e){return"-1"!==e.getAttribute("tabindex")}));return function(e){for(var t=[],n=0;n<e.length;n++)-1===t.indexOf(e[n])&&t.push(e[n]);return t}(e.concat(t)).filter((function(e){return fe(e)}))},Z=function(){return!Q()&&!document.body.classList.contains(A["no-backdrop"])},Q=function(){return document.body.classList.contains(A["toast-shown"])},J={previousBodyPadding:null},G=function(e,t){if(e.textContent="",t){var n=(new DOMParser).parseFromString(t,"text/html");h(n.querySelector("head").childNodes).forEach((function(t){e.appendChild(t)})),h(n.querySelector("body").childNodes).forEach((function(t){e.appendChild(t)}))}},X=function(e,t){if(!t)return!1;for(var n=t.split(/\s+/),o=0;o<n.length;o++)if(!e.classList.contains(n[o]))return!1;return!0},ee=function(t,n,o){if(function(e,t){h(e.classList).forEach((function(n){-1===f(A).indexOf(n)&&-1===f(E).indexOf(n)&&-1===f(t.showClass).indexOf(n)&&e.classList.remove(n)}))}(t,n),n.customClass&&n.customClass[o]){if("string"!=typeof n.customClass[o]&&!n.customClass[o].forEach)return m("Invalid type of customClass.".concat(o,'! Expected string or iterable object, got "').concat(e(n.customClass[o]),'"'));ae(t,n.customClass[o])}};function te(e,t){if(!t)return null;switch(t){case"select":case"textarea":case"file":return se(e,A[t]);case"checkbox":return e.querySelector(".".concat(A.checkbox," input"));case"radio":return e.querySelector(".".concat(A.radio," input:checked"))||e.querySelector(".".concat(A.radio," input:first-child"));case"range":return e.querySelector(".".concat(A.range," input"));default:return se(e,A.input)}}var ne,oe=function(e){if(e.focus(),"file"!==e.type){var t=e.value;e.value="",e.value=t}},ie=function(e,t,n){e&&t&&("string"==typeof t&&(t=t.split(/\s+/).filter(Boolean)),t.forEach((function(t){e.forEach?e.forEach((function(e){n?e.classList.add(t):e.classList.remove(t)})):n?e.classList.add(t):e.classList.remove(t)})))},ae=function(e,t){ie(e,t,!0)},re=function(e,t){ie(e,t,!1)},se=function(e,t){for(var n=0;n<e.childNodes.length;n++)if(X(e.childNodes[n],t))return e.childNodes[n]},le=function(e,t,n){n==="".concat(parseInt(n))&&(n=parseInt(n)),n||0===parseInt(n)?e.style[t]="number"==typeof n?"".concat(n,"px"):n:e.style.removeProperty(t)},ce=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"flex";e.style.display=t},de=function(e){e.style.display="none"},ue=function(e,t,n,o){var i=e.querySelector(t);i&&(i.style[n]=o)},pe=function(e,t,n){t?ce(e,n):de(e)},fe=function(e){return!(!e||!(e.offsetWidth||e.offsetHeight||e.getClientRects().length))},he=function(e){return!!(e.scrollHeight>e.clientHeight)},me=function(e){var t=window.getComputedStyle(e),n=parseFloat(t.getPropertyValue("animation-duration")||"0"),o=parseFloat(t.getPropertyValue("transition-duration")||"0");return n>0||o>0},ge=function(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=U();fe(n)&&(t&&(n.style.transition="none",n.style.width="100%"),setTimeout((function(){n.style.transition="width ".concat(e/1e3,"s linear"),n.style.width="0%"}),10))},we=function(){return"undefined"==typeof window||"undefined"==typeof document},ve='\n <div aria-labelledby="'.concat(A.title,'" aria-describedby="').concat(A.content,'" class="').concat(A.popup,'" tabindex="-1">\n   <div class="').concat(A.header,'">\n     <ul class="').concat(A["progress-steps"],'"></ul>\n     <div class="').concat(A.icon,'"></div>\n     <img class="').concat(A.image,'" />\n     <h2 class="').concat(A.title,'" id="').concat(A.title,'"></h2>\n     <button type="button" class="').concat(A.close,'"></button>\n   </div>\n   <div class="').concat(A.content,'">\n     <div id="').concat(A.content,'" class="').concat(A["html-container"],'"></div>\n     <input class="').concat(A.input,'" />\n     <input type="file" class="').concat(A.file,'" />\n     <div class="').concat(A.range,'">\n       <input type="range" />\n       <output></output>\n     </div>\n     <select class="').concat(A.select,'"></select>\n     <div class="').concat(A.radio,'"></div>\n     <label for="').concat(A.checkbox,'" class="').concat(A.checkbox,'">\n       <input type="checkbox" />\n       <span class="').concat(A.label,'"></span>\n     </label>\n     <textarea class="').concat(A.textarea,'"></textarea>\n     <div class="').concat(A["validation-message"],'" id="').concat(A["validation-message"],'"></div>\n   </div>\n   <div class="').concat(A.actions,'">\n     <div class="').concat(A.loader,'"></div>\n     <button type="button" class="').concat(A.confirm,'"></button>\n     <button type="button" class="').concat(A.deny,'"></button>\n     <button type="button" class="').concat(A.cancel,'"></button>\n   </div>\n   <div class="').concat(A.footer,'"></div>\n   <div class="').concat(A["timer-progress-bar-container"],'">\n     <div class="').concat(A["timer-progress-bar"],'"></div>\n   </div>\n </div>\n').replace(/(^|\n)\s*/g,""),_e=function(e){qn.isVisible()&&ne!==e.target.value&&qn.resetValidationMessage(),ne=e.target.value},ye=function(e){var t,n,o,i,a,r,s,l,c,d,u=!!(t=M())&&(t.parentNode.removeChild(t),re([document.documentElement,document.body],[A["no-backdrop"],A["toast-shown"],A["has-column"]]),!0);if(we())g("SweetAlert2 requires document to initialize");else{var p=document.createElement("div");p.className=A.container,u&&ae(p,A["no-transition"]),G(p,ve);var f="string"==typeof(d=e.target)?document.querySelector(d):d;f.appendChild(p),function(e){var t=S();t.setAttribute("role",e.toast?"alert":"dialog"),t.setAttribute("aria-live",e.toast?"polite":"assertive"),e.toast||t.setAttribute("aria-modal","true")}(e),function(e){"rtl"===window.getComputedStyle(e).direction&&ae(M(),A.rtl)}(f),n=j(),o=se(n,A.input),i=se(n,A.file),a=n.querySelector(".".concat(A.range," input")),r=n.querySelector(".".concat(A.range," output")),s=se(n,A.select),l=n.querySelector(".".concat(A.checkbox," input")),c=se(n,A.textarea),o.oninput=_e,i.onchange=_e,s.onchange=_e,l.onchange=_e,c.oninput=_e,a.oninput=function(e){_e(e),r.value=a.value},a.onchange=function(e){_e(e),a.nextSibling.value=a.value}}},be=function(t,n){t instanceof HTMLElement?n.appendChild(t):"object"===e(t)?Ce(t,n):t&&G(n,t)},Ce=function(e,t){e.jquery?xe(t,e):G(t,e.toString())},xe=function(e,t){if(e.textContent="",0 in t)for(var n=0;n in t;n++)e.appendChild(t[n].cloneNode(!0));else e.appendChild(t.cloneNode(!0))},ke=function(){if(we())return!1;var e=document.createElement("div"),t={WebkitAnimation:"webkitAnimationEnd",OAnimation:"oAnimationEnd oanimationend",animation:"animationend"};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)&&void 0!==e.style[n])return t[n];return!1}(),De=function(e,t){var n=z(),o=F(),i=N(),a=q(),r=W();t.showConfirmButton||t.showDenyButton||t.showCancelButton||de(n),ee(n,t,"actions"),Ae(i,"confirm",t),Ae(a,"deny",t),Ae(r,"cancel",t),function(e,t,n,o){if(!o.buttonsStyling)return re([e,t,n],A.styled);ae([e,t,n],A.styled),o.confirmButtonColor&&(e.style.backgroundColor=o.confirmButtonColor),o.denyButtonColor&&(t.style.backgroundColor=o.denyButtonColor),o.cancelButtonColor&&(n.style.backgroundColor=o.cancelButtonColor)}(i,a,r,t),t.reverseButtons&&(n.insertBefore(r,o),n.insertBefore(a,o),n.insertBefore(i,o)),G(o,t.loaderHtml),ee(o,t,"loader")};function Ae(e,t,n){pe(e,n["show".concat(p(t),"Button")],"inline-block"),G(e,n["".concat(t,"ButtonText")]),e.setAttribute("aria-label",n["".concat(t,"ButtonAriaLabel")]),e.className=A[t],ee(e,n,"".concat(t,"Button")),ae(e,n["".concat(t,"ButtonClass")])}var Ee=function(e,t){var n=M();if(n){(function(e,t){"string"==typeof t?e.style.background=t:t||ae([document.documentElement,document.body],A["no-backdrop"])})(n,t.backdrop),!t.backdrop&&t.allowOutsideClick&&m('"allowOutsideClick" parameter requires `backdrop` parameter to be set to `true`'),function(e,t){t in A?ae(e,A[t]):(m('The "position" parameter is not valid, defaulting to "center"'),ae(e,A.center))}(n,t.position),function(e,t){if(t&&"string"==typeof t){var n="grow-".concat(t);n in A&&ae(e,A[n])}}(n,t.grow),ee(n,t,"container");var o=document.body.getAttribute("data-swal2-queue-step");o&&(n.setAttribute("data-queue-step",o),document.body.removeAttribute("data-swal2-queue-step"))}},Me={promise:new WeakMap,innerParams:new WeakMap,domCache:new WeakMap},Te=["input","file","range","select","radio","checkbox","textarea"],$e=function(e){if(!Le[e.input])return g('Unexpected type of input! Expected "text", "email", "password", "number", "tel", "select", "radio", "checkbox", "textarea", "file" or "url", got "'.concat(e.input,'"'));var t=Ie(e.input),n=Le[e.input](t,e);ce(n),setTimeout((function(){oe(n)}))},Se=function(e,t){var n=te(j(),e);if(n)for(var o in function(e){for(var t=0;t<e.attributes.length;t++){var n=e.attributes[t].name;-1===["type","value","style"].indexOf(n)&&e.removeAttribute(n)}}(n),t)"range"===e&&"placeholder"===o||n.setAttribute(o,t[o])},Oe=function(e){var t=Ie(e.input);e.customClass&&ae(t,e.customClass.input)},Pe=function(e,t){e.placeholder&&!t.inputPlaceholder||(e.placeholder=t.inputPlaceholder)},je=function(e,t,n){if(n.inputLabel){e.id=A.input;var o=document.createElement("label"),i=A["input-label"];o.setAttribute("for",e.id),o.className=i,ae(o,n.customClass.inputLabel),o.innerText=n.inputLabel,t.insertAdjacentElement("beforebegin",o)}},Ie=function(e){var t=A[e]?A[e]:A.input;return se(j(),t)},Le={};Le.text=Le.email=Le.password=Le.number=Le.tel=Le.url=function(t,n){return"string"==typeof n.inputValue||"number"==typeof n.inputValue?t.value=n.inputValue:C(n.inputValue)||m('Unexpected type of inputValue! Expected "string", "number" or "Promise", got "'.concat(e(n.inputValue),'"')),je(t,t,n),Pe(t,n),t.type=n.input,t},Le.file=function(e,t){return je(e,e,t),Pe(e,t),e},Le.range=function(e,t){var n=e.querySelector("input"),o=e.querySelector("output");return n.value=t.inputValue,n.type=t.input,o.value=t.inputValue,je(n,e,t),e},Le.select=function(e,t){if(e.textContent="",t.inputPlaceholder){var n=document.createElement("option");G(n,t.inputPlaceholder),n.value="",n.disabled=!0,n.selected=!0,e.appendChild(n)}return je(e,e,t),e},Le.radio=function(e){return e.textContent="",e},Le.checkbox=function(e,t){var n=te(j(),"checkbox");n.value=1,n.id=A.checkbox,n.checked=Boolean(t.inputValue);var o=e.querySelector("span");return G(o,t.inputPlaceholder),e},Le.textarea=function(e,t){e.value=t.inputValue,Pe(e,t),je(e,e,t);var n=function(e){return parseInt(window.getComputedStyle(e).paddingLeft)+parseInt(window.getComputedStyle(e).paddingRight)};if("MutationObserver"in window){var o=parseInt(window.getComputedStyle(S()).width);new MutationObserver((function(){var t=e.offsetWidth+n(S())+n(j());S().style.width=t>o?"".concat(t,"px"):null})).observe(e,{attributes:!0,attributeFilter:["style"]})}return e};var Be=function(e,t){var n=I();ee(n,t,"htmlContainer"),t.html?(be(t.html,n),ce(n,"block")):t.text?(n.textContent=t.text,ce(n,"block")):de(n),function(e,t){var n=j(),o=Me.innerParams.get(e),i=!o||t.input!==o.input;Te.forEach((function(e){var o=A[e],a=se(n,o);Se(e,t.inputAttributes),a.className=o,i&&de(a)})),t.input&&(i&&$e(t),Oe(t))}(e,t),ee(j(),t,"content")},He=function(e,t){for(var n in E)t.icon!==n&&re(e,E[n]);ae(e,E[t.icon]),Fe(e,t),Ne(),ee(e,t,"icon")},Ne=function(){for(var e=S(),t=window.getComputedStyle(e).getPropertyValue("background-color"),n=e.querySelectorAll("[class^=swal2-success-circular-line], .swal2-success-fix"),o=0;o<n.length;o++)n[o].style.backgroundColor=t},qe=function(e,t){e.textContent="",t.iconHtml?G(e,We(t.iconHtml)):"success"===t.icon?G(e,'\n      <div class="swal2-success-circular-line-left"></div>\n      <span class="swal2-success-line-tip"></span> <span class="swal2-success-line-long"></span>\n      <div class="swal2-success-ring"></div> <div class="swal2-success-fix"></div>\n      <div class="swal2-success-circular-line-right"></div>\n    '):"error"===t.icon?G(e,'\n      <span class="swal2-x-mark">\n        <span class="swal2-x-mark-line-left"></span>\n        <span class="swal2-x-mark-line-right"></span>\n      </span>\n    '):G(e,We({question:"?",warning:"!",info:"i"}[t.icon]))},Fe=function(e,t){if(t.iconColor){e.style.color=t.iconColor,e.style.borderColor=t.iconColor;for(var n=0,o=[".swal2-success-line-tip",".swal2-success-line-long",".swal2-x-mark-line-left",".swal2-x-mark-line-right"];n<o.length;n++){ue(e,o[n],"backgroundColor",t.iconColor)}ue(e,".swal2-success-ring","borderColor",t.iconColor)}},We=function(e){return'<div class="'.concat(A["icon-content"],'">').concat(e,"</div>")},ze=[],Ye=function(){return M()&&M().getAttribute("data-queue-step")},Re=function(e,t){var n=B();if(!t.progressSteps||0===t.progressSteps.length)return de(n);ce(n),n.textContent="";var o=parseInt(void 0===t.currentProgressStep?Ye():t.currentProgressStep);o>=t.progressSteps.length&&m("Invalid currentProgressStep parameter, it should be less than progressSteps.length (currentProgressStep like JS arrays starts from 0)"),t.progressSteps.forEach((function(e,i){var a=function(e){var t=document.createElement("li");return ae(t,A["progress-step"]),G(t,e),t}(e);if(n.appendChild(a),i===o&&ae(a,A["active-progress-step"]),i!==t.progressSteps.length-1){var r=function(e){var t=document.createElement("li");return ae(t,A["progress-step-line"]),e.progressStepsDistance&&(t.style.width=e.progressStepsDistance),t}(t);n.appendChild(r)}}))},Ue=function(e,t){var n=Y();ee(n,t,"header"),Re(0,t),function(e,t){var n=Me.innerParams.get(e),o=O();n&&t.icon===n.icon?(qe(o,t),He(o,t)):t.icon||t.iconHtml?t.icon&&-1===Object.keys(E).indexOf(t.icon)?(g('Unknown icon! Expected "success", "error", "warning", "info" or "question", got "'.concat(t.icon,'"')),de(o)):(ce(o),qe(o,t),He(o,t),ae(o,t.showClass.icon)):de(o)}(e,t),function(e,t){var n=L();if(!t.imageUrl)return de(n);ce(n,""),n.setAttribute("src",t.imageUrl),n.setAttribute("alt",t.imageAlt),le(n,"width",t.imageWidth),le(n,"height",t.imageHeight),n.className=A.image,ee(n,t,"image")}(0,t),function(e,t){var n=P();pe(n,t.title||t.titleText,"block"),t.title&&be(t.title,n),t.titleText&&(n.innerText=t.titleText),ee(n,t,"title")}(0,t),function(e,t){var n=V();G(n,t.closeButtonHtml),ee(n,t,"closeButton"),pe(n,t.showCloseButton),n.setAttribute("aria-label",t.closeButtonAriaLabel)}(0,t)},Ve=function(e,t){e.className="".concat(A.popup," ").concat(fe(e)?t.showClass.popup:""),t.toast?(ae([document.documentElement,document.body],A["toast-shown"]),ae(e,A.toast)):ae(e,A.modal),ee(e,t,"popup"),"string"==typeof t.customClass&&ae(e,t.customClass),t.icon&&ae(e,A["icon-".concat(t.icon)])},Ke=function(e,t){(function(e,t){var n=M(),o=S();t.toast?(le(n,"width",t.width),o.style.width="100%"):le(o,"width",t.width),le(o,"padding",t.padding),t.background&&(o.style.background=t.background),de(H()),Ve(o,t)})(0,t),Ee(0,t),Ue(e,t),Be(e,t),De(0,t),function(e,t){var n=R();pe(n,t.footer),t.footer&&be(t.footer,n),ee(n,t,"footer")}(0,t),"function"==typeof t.didRender?t.didRender(S()):"function"==typeof t.onRender&&t.onRender(S())},Ze=function(){return N()&&N().click()};var Qe=function(e){var t=S();t||qn.fire(),t=S();var n=z(),o=F();!e&&fe(N())&&(e=N()),ce(n),e&&(de(e),o.setAttribute("data-button-to-replace",e.className)),o.parentNode.insertBefore(o,e),ae([t,n],A.loading),ce(o),t.setAttribute("data-loading",!0),t.setAttribute("aria-busy",!0),t.focus()},Je={},Ge=function(e){return new Promise((function(t){if(!e)return t();var n=window.scrollX,o=window.scrollY;Je.restoreFocusTimeout=setTimeout((function(){Je.previousActiveElement&&Je.previousActiveElement.focus?(Je.previousActiveElement.focus(),Je.previousActiveElement=null):document.body&&document.body.focus(),t()}),100),void 0!==n&&void 0!==o&&window.scrollTo(n,o)}))},Xe=function(){if(Je.timeout)return function(){var e=U(),t=parseInt(window.getComputedStyle(e).width);e.style.removeProperty("transition"),e.style.width="100%";var n=parseInt(window.getComputedStyle(e).width),o=parseInt(t/n*100);e.style.removeProperty("transition"),e.style.width="".concat(o,"%")}(),Je.timeout.stop()},et=function(){if(Je.timeout){var e=Je.timeout.start();return ge(e),e}},tt=!1,nt={};var ot=function(e){for(var t=e.target;t&&t!==document;t=t.parentNode)for(var n in nt){var o=t.getAttribute(n);if(o)return void nt[n].fire({template:o})}},it={title:"",titleText:"",text:"",html:"",footer:"",icon:void 0,iconColor:void 0,iconHtml:void 0,template:void 0,toast:!1,animation:!0,showClass:{popup:"swal2-show",backdrop:"swal2-backdrop-show",icon:"swal2-icon-show"},hideClass:{popup:"swal2-hide",backdrop:"swal2-backdrop-hide",icon:"swal2-icon-hide"},customClass:{},target:"body",backdrop:!0,heightAuto:!0,allowOutsideClick:!0,allowEscapeKey:!0,allowEnterKey:!0,stopKeydownPropagation:!0,keydownListenerCapture:!1,showConfirmButton:!0,showDenyButton:!1,showCancelButton:!1,preConfirm:void 0,preDeny:void 0,confirmButtonText:"OK",confirmButtonAriaLabel:"",confirmButtonColor:void 0,denyButtonText:"No",denyButtonAriaLabel:"",denyButtonColor:void 0,cancelButtonText:"Cancel",cancelButtonAriaLabel:"",cancelButtonColor:void 0,buttonsStyling:!0,reverseButtons:!1,focusConfirm:!0,focusDeny:!1,focusCancel:!1,returnFocus:!0,showCloseButton:!1,closeButtonHtml:"&times;",closeButtonAriaLabel:"Close this dialog",loaderHtml:"",showLoaderOnConfirm:!1,showLoaderOnDeny:!1,imageUrl:void 0,imageWidth:void 0,imageHeight:void 0,imageAlt:"",timer:void 0,timerProgressBar:!1,width:void 0,padding:void 0,background:void 0,input:void 0,inputPlaceholder:"",inputLabel:"",inputValue:"",inputOptions:{},inputAutoTrim:!0,inputAttributes:{},inputValidator:void 0,returnInputValueOnDeny:!1,validationMessage:void 0,grow:!1,position:"center",progressSteps:[],currentProgressStep:void 0,progressStepsDistance:void 0,onBeforeOpen:void 0,onOpen:void 0,willOpen:void 0,didOpen:void 0,onRender:void 0,didRender:void 0,onClose:void 0,onAfterClose:void 0,willClose:void 0,didClose:void 0,onDestroy:void 0,didDestroy:void 0,scrollbarPadding:!0},at=["allowEscapeKey","allowOutsideClick","background","buttonsStyling","cancelButtonAriaLabel","cancelButtonColor","cancelButtonText","closeButtonAriaLabel","closeButtonHtml","confirmButtonAriaLabel","confirmButtonColor","confirmButtonText","currentProgressStep","customClass","denyButtonAriaLabel","denyButtonColor","denyButtonText","didClose","didDestroy","footer","hideClass","html","icon","iconColor","iconHtml","imageAlt","imageHeight","imageUrl","imageWidth","onAfterClose","onClose","onDestroy","progressSteps","returnFocus","reverseButtons","showCancelButton","showCloseButton","showConfirmButton","showDenyButton","text","title","titleText","willClose"],rt={animation:'showClass" and "hideClass',onBeforeOpen:"willOpen",onOpen:"didOpen",onRender:"didRender",onClose:"willClose",onAfterClose:"didClose",onDestroy:"didDestroy"},st=["allowOutsideClick","allowEnterKey","backdrop","focusConfirm","focusDeny","focusCancel","returnFocus","heightAuto","keydownListenerCapture"],lt=function(e){return Object.prototype.hasOwnProperty.call(it,e)},ct=function(e){return rt[e]},dt=function(e){lt(e)||m('Unknown parameter "'.concat(e,'"'))},ut=function(e){-1!==st.indexOf(e)&&m('The parameter "'.concat(e,'" is incompatible with toasts'))},pt=function(e){ct(e)&&v(e,ct(e))},ft=function(e){for(var t in e)dt(t),e.toast&&ut(t),pt(t)},ht=Object.freeze({isValidParameter:lt,isUpdatableParameter:function(e){return-1!==at.indexOf(e)},isDeprecatedParameter:ct,argsToParams:function(t){var n={};return"object"!==e(t[0])||k(t[0])?["title","html","icon"].forEach((function(o,i){var a=t[i];"string"==typeof a||k(a)?n[o]=a:void 0!==a&&g("Unexpected type of ".concat(o,'! Expected "string" or "Element", got ').concat(e(a)))})):i(n,t[0]),n},isVisible:function(){return fe(S())},clickConfirm:Ze,clickDeny:function(){return q()&&q().click()},clickCancel:function(){return W()&&W().click()},getContainer:M,getPopup:S,getTitle:P,getContent:j,getHtmlContainer:I,getImage:L,getIcon:O,getInputLabel:function(){return $(A["input-label"])},getCloseButton:V,getActions:z,getConfirmButton:N,getDenyButton:q,getCancelButton:W,getLoader:F,getHeader:Y,getFooter:R,getTimerProgressBar:U,getFocusableElements:K,getValidationMessage:H,isLoading:function(){return S().hasAttribute("data-loading")},fire:function(){for(var e=this,t=arguments.length,n=new Array(t),o=0;o<t;o++)n[o]=arguments[o];return l(e,n)},mixin:function(e){var n=function(n){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),t&&r(e,t)}(f,n);var l,u,p=(l=f,u=s(),function(){var e,t=a(l);if(u){var n=a(this).constructor;e=Reflect.construct(t,arguments,n)}else e=t.apply(this,arguments);return c(this,e)});function f(){return t(this,f),p.apply(this,arguments)}return o(f,[{key:"_main",value:function(t,n){return d(a(f.prototype),"_main",this).call(this,t,i({},e,n))}}]),f}(this);return n},queue:function(e){v("Swal.queue()","async/await");var t=this;ze=e;var n=function(e,t){ze=[],e(t)},o=[];return new Promise((function(e){!function i(a,r){a<ze.length?(document.body.setAttribute("data-swal2-queue-step",a),t.fire(ze[a]).then((function(t){void 0!==t.value?(o.push(t.value),i(a+1,r)):n(e,{dismiss:t.dismiss})}))):n(e,{value:o})}(0)}))},getQueueStep:Ye,insertQueueStep:function(e,t){return t&&t<ze.length?ze.splice(t,0,e):ze.push(e)},deleteQueueStep:function(e){void 0!==ze[e]&&ze.splice(e,1)},showLoading:Qe,enableLoading:Qe,getTimerLeft:function(){return Je.timeout&&Je.timeout.getTimerLeft()},stopTimer:Xe,resumeTimer:et,toggleTimer:function(){var e=Je.timeout;return e&&(e.running?Xe():et())},increaseTimer:function(e){if(Je.timeout){var t=Je.timeout.increase(e);return ge(t,!0),t}},isTimerRunning:function(){return Je.timeout&&Je.timeout.isRunning()},bindClickHandler:function(){nt[arguments.length>0&&void 0!==arguments[0]?arguments[0]:"data-swal-template"]=this,tt||(document.body.addEventListener("click",ot),tt=!0)}});function mt(){if(Me.innerParams.get(this)){var e=Me.domCache.get(this);de(e.loader);var t=e.popup.getElementsByClassName(e.loader.getAttribute("data-button-to-replace"));t.length?ce(t[0],"inline-block"):!fe(N())&&!fe(q())&&!fe(W())&&de(e.actions),re([e.popup,e.actions],A.loading),e.popup.removeAttribute("aria-busy"),e.popup.removeAttribute("data-loading"),e.confirmButton.disabled=!1,e.denyButton.disabled=!1,e.cancelButton.disabled=!1}}var gt=function(){null===J.previousBodyPadding&&document.body.scrollHeight>window.innerHeight&&(J.previousBodyPadding=parseInt(window.getComputedStyle(document.body).getPropertyValue("padding-right")),document.body.style.paddingRight="".concat(J.previousBodyPadding+function(){var e=document.createElement("div");e.className=A["scrollbar-measure"],document.body.appendChild(e);var t=e.getBoundingClientRect().width-e.clientWidth;return document.body.removeChild(e),t}(),"px"))},wt=function(){if(!navigator.userAgent.match(/(CriOS|FxiOS|EdgiOS|YaBrowser|UCBrowser)/i)){S().scrollHeight>window.innerHeight-44&&(M().style.paddingBottom="".concat(44,"px"))}},vt=function(){var e,t=M();t.ontouchstart=function(t){e=_t(t)},t.ontouchmove=function(t){e&&(t.preventDefault(),t.stopPropagation())}},_t=function(e){var t=e.target,n=M();return!(yt(e)||bt(e)||t!==n&&(he(n)||"INPUT"===t.tagName||he(j())&&j().contains(t)))},yt=function(e){return e.touches&&e.touches.length&&"stylus"===e.touches[0].touchType},bt=function(e){return e.touches&&e.touches.length>1},Ct=function(){return!!window.MSInputMethodContext&&!!document.documentMode},xt=function(){var e=M(),t=S();e.style.removeProperty("align-items"),t.offsetTop<0&&(e.style.alignItems="flex-start")},kt={swalPromiseResolve:new WeakMap};function Dt(e,t,n,o){Q()?St(e,o):(Ge(n).then((function(){return St(e,o)})),Je.keydownTarget.removeEventListener("keydown",Je.keydownHandler,{capture:Je.keydownListenerCapture}),Je.keydownHandlerAdded=!1),t.parentNode&&!document.body.getAttribute("data-swal2-queue-step")&&t.parentNode.removeChild(t),Z()&&(null!==J.previousBodyPadding&&(document.body.style.paddingRight="".concat(J.previousBodyPadding,"px"),J.previousBodyPadding=null),function(){if(X(document.body,A.iosfix)){var e=parseInt(document.body.style.top,10);re(document.body,A.iosfix),document.body.style.top="",document.body.scrollTop=-1*e}}(),"undefined"!=typeof window&&Ct()&&window.removeEventListener("resize",xt),h(document.body.children).forEach((function(e){e.hasAttribute("data-previous-aria-hidden")?(e.setAttribute("aria-hidden",e.getAttribute("data-previous-aria-hidden")),e.removeAttribute("data-previous-aria-hidden")):e.removeAttribute("aria-hidden")}))),re([document.documentElement,document.body],[A.shown,A["height-auto"],A["no-backdrop"],A["toast-shown"]])}function At(e){var t=S();if(t){e=Et(e);var n=Me.innerParams.get(this);if(n&&!X(t,n.hideClass.popup)){var o=kt.swalPromiseResolve.get(this);re(t,n.showClass.popup),ae(t,n.hideClass.popup);var i=M();re(i,n.showClass.backdrop),ae(i,n.hideClass.backdrop),Mt(this,t,n),o(e)}}}var Et=function(e){return void 0===e?{isConfirmed:!1,isDenied:!1,isDismissed:!0}:i({isConfirmed:!1,isDenied:!1,isDismissed:!1},e)},Mt=function(e,t,n){var o=M(),i=ke&&me(t),a=n.onClose,r=n.onAfterClose,s=n.willClose,l=n.didClose;Tt(t,s,a),i?$t(e,t,o,n.returnFocus,l||r):Dt(e,o,n.returnFocus,l||r)},Tt=function(e,t,n){null!==t&&"function"==typeof t?t(e):null!==n&&"function"==typeof n&&n(e)},$t=function(e,t,n,o,i){Je.swalCloseEventFinishedCallback=Dt.bind(null,e,n,o,i),t.addEventListener(ke,(function(e){e.target===t&&(Je.swalCloseEventFinishedCallback(),delete Je.swalCloseEventFinishedCallback)}))},St=function(e,t){setTimeout((function(){"function"==typeof t&&t(),e._destroy()}))};function Ot(e,t,n){var o=Me.domCache.get(e);t.forEach((function(e){o[e].disabled=n}))}function Pt(e,t){if(!e)return!1;if("radio"===e.type)for(var n=e.parentNode.parentNode.querySelectorAll("input"),o=0;o<n.length;o++)n[o].disabled=t;else e.disabled=t}var jt=function(){function e(n,o){t(this,e),this.callback=n,this.remaining=o,this.running=!1,this.start()}return o(e,[{key:"start",value:function(){return this.running||(this.running=!0,this.started=new Date,this.id=setTimeout(this.callback,this.remaining)),this.remaining}},{key:"stop",value:function(){return this.running&&(this.running=!1,clearTimeout(this.id),this.remaining-=new Date-this.started),this.remaining}},{key:"increase",value:function(e){var t=this.running;return t&&this.stop(),this.remaining+=e,t&&this.start(),this.remaining}},{key:"getTimerLeft",value:function(){return this.running&&(this.stop(),this.start()),this.remaining}},{key:"isRunning",value:function(){return this.running}}]),e}(),It={email:function(e,t){return/^[a-zA-Z0-9.+_-]+@[a-zA-Z0-9.-]+\.[a-zA-Z0-9-]{2,24}$/.test(e)?Promise.resolve():Promise.resolve(t||"Invalid email address")},url:function(e,t){return/^https?:\/\/(www\.)?[-a-zA-Z0-9@:%._+~#=]{1,256}\.[a-z]{2,63}\b([-a-zA-Z0-9@:%_+.~#?&/=]*)$/.test(e)?Promise.resolve():Promise.resolve(t||"Invalid URL")}};function Lt(e){(function(e){e.inputValidator||Object.keys(It).forEach((function(t){e.input===t&&(e.inputValidator=It[t])}))})(e),e.showLoaderOnConfirm&&!e.preConfirm&&m("showLoaderOnConfirm is set to true, but preConfirm is not defined.\nshowLoaderOnConfirm should be used together with preConfirm, see usage example:\nhttps://sweetalert2.github.io/#ajax-request"),e.animation=_(e.animation),function(e){(!e.target||"string"==typeof e.target&&!document.querySelector(e.target)||"string"!=typeof e.target&&!e.target.appendChild)&&(m('Target parameter is not valid, defaulting to "body"'),e.target="body")}(e),"string"==typeof e.title&&(e.title=e.title.split("\n").join("<br />")),ye(e)}var Bt=["swal-title","swal-html","swal-footer"],Ht=function(t){var n={};return h(t.querySelectorAll("swal-param")).forEach((function(t){Rt(t,["name","value"]);var o=t.getAttribute("name"),i=t.getAttribute("value");"boolean"==typeof it[o]&&"false"===i&&(i=!1),"object"===e(it[o])&&(i=JSON.parse(i)),n[o]=i})),n},Nt=function(e){var t={};return h(e.querySelectorAll("swal-button")).forEach((function(e){Rt(e,["type","color","aria-label"]);var n=e.getAttribute("type");t["".concat(n,"ButtonText")]=e.innerHTML,t["show".concat(p(n),"Button")]=!0,e.hasAttribute("color")&&(t["".concat(n,"ButtonColor")]=e.getAttribute("color")),e.hasAttribute("aria-label")&&(t["".concat(n,"ButtonAriaLabel")]=e.getAttribute("aria-label"))})),t},qt=function(e){var t={},n=e.querySelector("swal-image");return n&&(Rt(n,["src","width","height","alt"]),n.hasAttribute("src")&&(t.imageUrl=n.getAttribute("src")),n.hasAttribute("width")&&(t.imageWidth=n.getAttribute("width")),n.hasAttribute("height")&&(t.imageHeight=n.getAttribute("height")),n.hasAttribute("alt")&&(t.imageAlt=n.getAttribute("alt"))),t},Ft=function(e){var t={},n=e.querySelector("swal-icon");return n&&(Rt(n,["type","color"]),n.hasAttribute("type")&&(t.icon=n.getAttribute("type")),n.hasAttribute("color")&&(t.iconColor=n.getAttribute("color")),t.iconHtml=n.innerHTML),t},Wt=function(e){var t={},n=e.querySelector("swal-input");n&&(Rt(n,["type","label","placeholder","value"]),t.input=n.getAttribute("type")||"text",n.hasAttribute("label")&&(t.inputLabel=n.getAttribute("label")),n.hasAttribute("placeholder")&&(t.inputPlaceholder=n.getAttribute("placeholder")),n.hasAttribute("value")&&(t.inputValue=n.getAttribute("value")));var o=e.querySelectorAll("swal-input-option");return o.length&&(t.inputOptions={},h(o).forEach((function(e){Rt(e,["value"]);var n=e.getAttribute("value"),o=e.innerHTML;t.inputOptions[n]=o}))),t},zt=function(e,t){var n={};for(var o in t){var i=t[o],a=e.querySelector(i);a&&(Rt(a,[]),n[i.replace(/^swal-/,"")]=a.innerHTML.trim())}return n},Yt=function(e){var t=Bt.concat(["swal-param","swal-button","swal-image","swal-icon","swal-input","swal-input-option"]);h(e.querySelectorAll("*")).forEach((function(n){if(n.parentNode===e){var o=n.tagName.toLowerCase();-1===t.indexOf(o)&&m("Unrecognized element <".concat(o,">"))}}))},Rt=function(e,t){h(e.attributes).forEach((function(n){-1===t.indexOf(n.name)&&m(['Unrecognized attribute "'.concat(n.name,'" on <').concat(e.tagName.toLowerCase(),">."),"".concat(t.length?"Allowed attributes are: ".concat(t.join(", ")):"To set the value, use HTML within the element.")])}))},Ut=function(e){var t=M(),n=S();"function"==typeof e.willOpen?e.willOpen(n):"function"==typeof e.onBeforeOpen&&e.onBeforeOpen(n);var o=window.getComputedStyle(document.body).overflowY;Jt(t,n,e),setTimeout((function(){Zt(t,n)}),10),Z()&&(Qt(t,e.scrollbarPadding,o),h(document.body.children).forEach((function(e){e===M()||function(e,t){if("function"==typeof e.contains)return e.contains(t)}(e,M())||(e.hasAttribute("aria-hidden")&&e.setAttribute("data-previous-aria-hidden",e.getAttribute("aria-hidden")),e.setAttribute("aria-hidden","true"))}))),Q()||Je.previousActiveElement||(Je.previousActiveElement=document.activeElement),Vt(n,e),re(t,A["no-transition"])},Vt=function(e,t){"function"==typeof t.didOpen?setTimeout((function(){return t.didOpen(e)})):"function"==typeof t.onOpen&&setTimeout((function(){return t.onOpen(e)}))},Kt=function e(t){var n=S();if(t.target===n){var o=M();n.removeEventListener(ke,e),o.style.overflowY="auto"}},Zt=function(e,t){ke&&me(t)?(e.style.overflowY="hidden",t.addEventListener(ke,Kt)):e.style.overflowY="auto"},Qt=function(e,t,n){(function(){if((/iPad|iPhone|iPod/.test(navigator.userAgent)&&!window.MSStream||"MacIntel"===navigator.platform&&navigator.maxTouchPoints>1)&&!X(document.body,A.iosfix)){var e=document.body.scrollTop;document.body.style.top="".concat(-1*e,"px"),ae(document.body,A.iosfix),vt(),wt()}})(),"undefined"!=typeof window&&Ct()&&(xt(),window.addEventListener("resize",xt)),t&&"hidden"!==n&&gt(),setTimeout((function(){e.scrollTop=0}))},Jt=function(e,t,n){ae(e,n.showClass.backdrop),t.style.setProperty("opacity","0","important"),ce(t),setTimeout((function(){ae(t,n.showClass.popup),t.style.removeProperty("opacity")}),10),ae([document.documentElement,document.body],A.shown),n.heightAuto&&n.backdrop&&!n.toast&&ae([document.documentElement,document.body],A["height-auto"])},Gt=function(e){return e.checked?1:0},Xt=function(e){return e.checked?e.value:null},en=function(e){return e.files.length?null!==e.getAttribute("multiple")?e.files:e.files[0]:null},tn=function(t,n){var o=j(),i=function(e){return on[n.input](o,an(e),n)};y(n.inputOptions)||C(n.inputOptions)?(Qe(N()),b(n.inputOptions).then((function(e){t.hideLoading(),i(e)}))):"object"===e(n.inputOptions)?i(n.inputOptions):g("Unexpected type of inputOptions! Expected object, Map or Promise, got ".concat(e(n.inputOptions)))},nn=function(e,t){var n=e.getInput();de(n),b(t.inputValue).then((function(o){n.value="number"===t.input?parseFloat(o)||0:"".concat(o),ce(n),n.focus(),e.hideLoading()})).catch((function(t){g("Error in inputValue promise: ".concat(t)),n.value="",ce(n),n.focus(),e.hideLoading()}))},on={select:function(e,t,n){var o=se(e,A.select),i=function(e,t,o){var i=document.createElement("option");i.value=o,G(i,t),i.selected=rn(o,n.inputValue),e.appendChild(i)};t.forEach((function(e){var t=e[0],n=e[1];if(Array.isArray(n)){var a=document.createElement("optgroup");a.label=t,a.disabled=!1,o.appendChild(a),n.forEach((function(e){return i(a,e[1],e[0])}))}else i(o,n,t)})),o.focus()},radio:function(e,t,n){var o=se(e,A.radio);t.forEach((function(e){var t=e[0],i=e[1],a=document.createElement("input"),r=document.createElement("label");a.type="radio",a.name=A.radio,a.value=t,rn(t,n.inputValue)&&(a.checked=!0);var s=document.createElement("span");G(s,i),s.className=A.label,r.appendChild(a),r.appendChild(s),o.appendChild(r)}));var i=o.querySelectorAll("input");i.length&&i[0].focus()}},an=function t(n){var o=[];return"undefined"!=typeof Map&&n instanceof Map?n.forEach((function(n,i){var a=n;"object"===e(a)&&(a=t(a)),o.push([i,a])})):Object.keys(n).forEach((function(i){var a=n[i];"object"===e(a)&&(a=t(a)),o.push([i,a])})),o},rn=function(e,t){return t&&t.toString()===e.toString()},sn=function(e,t,n){var o=function(e,t){var n=e.getInput();if(!n)return null;switch(t.input){case"checkbox":return Gt(n);case"radio":return Xt(n);case"file":return en(n);default:return t.inputAutoTrim?n.value.trim():n.value}}(e,t);t.inputValidator?ln(e,t,o):e.getInput().checkValidity()?"deny"===n?cn(e,t,o):un(e,t,o):(e.enableButtons(),e.showValidationMessage(t.validationMessage))},ln=function(e,t,n){e.disableInput(),Promise.resolve().then((function(){return b(t.inputValidator(n,t.validationMessage))})).then((function(o){e.enableButtons(),e.enableInput(),o?e.showValidationMessage(o):un(e,t,n)}))},cn=function(e,t,n){t.showLoaderOnDeny&&Qe(q()),t.preDeny?Promise.resolve().then((function(){return b(t.preDeny(n,t.validationMessage))})).then((function(t){!1===t?e.hideLoading():e.closePopup({isDenied:!0,value:void 0===t?n:t})})):e.closePopup({isDenied:!0,value:n})},dn=function(e,t){e.closePopup({isConfirmed:!0,value:t})},un=function(e,t,n){t.showLoaderOnConfirm&&Qe(),t.preConfirm?(e.resetValidationMessage(),Promise.resolve().then((function(){return b(t.preConfirm(n,t.validationMessage))})).then((function(t){fe(H())||!1===t?e.hideLoading():dn(e,void 0===t?n:t)}))):dn(e,n)},pn=function(e,t,n){var o=K();if(o.length)return(t+=n)===o.length?t=0:-1===t&&(t=o.length-1),o[t].focus();S().focus()},fn=["ArrowRight","ArrowDown","Right","Down"],hn=["ArrowLeft","ArrowUp","Left","Up"],mn=["Escape","Esc"],gn=function(e,t,n){var o=Me.innerParams.get(e);o&&(o.stopKeydownPropagation&&t.stopPropagation(),"Enter"===t.key?wn(e,t,o):"Tab"===t.key?vn(t,o):-1!==[].concat(fn,hn).indexOf(t.key)?yn(t.key):-1!==mn.indexOf(t.key)&&bn(t,o,n))},wn=function(e,t,n){if(!t.isComposing&&t.target&&e.getInput()&&t.target.outerHTML===e.getInput().outerHTML){if(-1!==["textarea","file"].indexOf(n.input))return;Ze(),t.preventDefault()}},vn=function(e,t){for(var n=e.target,o=K(),i=-1,a=0;a<o.length;a++)if(n===o[a]){i=a;break}e.shiftKey?pn(0,i,-1):pn(0,i,1),e.stopPropagation(),e.preventDefault()},yn=function(e){if(-1!==[N(),q(),W()].indexOf(document.activeElement)){var t=-1!==fn.indexOf(e)?"nextElementSibling":"previousElementSibling",n=document.activeElement[t];n&&n.focus()}},bn=function(e,t,n){_(t.allowEscapeKey)&&(e.preventDefault(),n(x.esc))},Cn=function(e,t,n){t.popup.onclick=function(){var t=Me.innerParams.get(e);t.showConfirmButton||t.showDenyButton||t.showCancelButton||t.showCloseButton||t.timer||t.input||n(x.close)}},xn=!1,kn=function(e){e.popup.onmousedown=function(){e.container.onmouseup=function(t){e.container.onmouseup=void 0,t.target===e.container&&(xn=!0)}}},Dn=function(e){e.container.onmousedown=function(){e.popup.onmouseup=function(t){e.popup.onmouseup=void 0,(t.target===e.popup||e.popup.contains(t.target))&&(xn=!0)}}},An=function(e,t,n){t.container.onclick=function(o){var i=Me.innerParams.get(e);xn?xn=!1:o.target===t.container&&_(i.allowOutsideClick)&&n(x.backdrop)}};var En=function(e,t){var n=function(e){var t="string"==typeof e.template?document.querySelector(e.template):e.template;if(!t)return{};var n=t.content||t;return Yt(n),i(Ht(n),Nt(n),qt(n),Ft(n),Wt(n),zt(n,Bt))}(e),o=i({},it,t,n,e);return o.showClass=i({},it.showClass,o.showClass),o.hideClass=i({},it.hideClass,o.hideClass),!1===e.animation&&(o.showClass={popup:"swal2-noanimation",backdrop:"swal2-noanimation"},o.hideClass={}),o},Mn=function(e,t,n){return new Promise((function(o){var i=function(t){e.closePopup({isDismissed:!0,dismiss:t})};kt.swalPromiseResolve.set(e,o),t.confirmButton.onclick=function(){return function(e,t){e.disableButtons(),t.input?sn(e,t,"confirm"):un(e,t,!0)}(e,n)},t.denyButton.onclick=function(){return function(e,t){e.disableButtons(),t.returnInputValueOnDeny?sn(e,t,"deny"):cn(e,t,!1)}(e,n)},t.cancelButton.onclick=function(){return function(e,t){e.disableButtons(),t(x.cancel)}(e,i)},t.closeButton.onclick=function(){return i(x.close)},function(e,t,n){Me.innerParams.get(e).toast?Cn(e,t,n):(kn(t),Dn(t),An(e,t,n))}(e,t,i),function(e,t,n,o){t.keydownTarget&&t.keydownHandlerAdded&&(t.keydownTarget.removeEventListener("keydown",t.keydownHandler,{capture:t.keydownListenerCapture}),t.keydownHandlerAdded=!1),n.toast||(t.keydownHandler=function(t){return gn(e,t,o)},t.keydownTarget=n.keydownListenerCapture?window:S(),t.keydownListenerCapture=n.keydownListenerCapture,t.keydownTarget.addEventListener("keydown",t.keydownHandler,{capture:t.keydownListenerCapture}),t.keydownHandlerAdded=!0)}(e,Je,n,i),function(e,t){"select"===t.input||"radio"===t.input?tn(e,t):-1!==["text","email","number","tel","textarea"].indexOf(t.input)&&(y(t.inputValue)||C(t.inputValue))&&nn(e,t)}(e,n),Ut(n),$n(Je,n,i),Sn(t,n),setTimeout((function(){t.container.scrollTop=0}))}))},Tn=function(e){var t={popup:S(),container:M(),content:j(),actions:z(),confirmButton:N(),denyButton:q(),cancelButton:W(),loader:F(),closeButton:V(),validationMessage:H(),progressSteps:B()};return Me.domCache.set(e,t),t},$n=function(e,t,n){var o=U();de(o),t.timer&&(e.timeout=new jt((function(){n("timer"),delete e.timeout}),t.timer),t.timerProgressBar&&(ce(o),setTimeout((function(){e.timeout&&e.timeout.running&&ge(t.timer)}))))},Sn=function(e,t){if(!t.toast)return _(t.allowEnterKey)?void(On(e,t)||pn(0,-1,1)):Pn()},On=function(e,t){return t.focusDeny&&fe(e.denyButton)?(e.denyButton.focus(),!0):t.focusCancel&&fe(e.cancelButton)?(e.cancelButton.focus(),!0):!(!t.focusConfirm||!fe(e.confirmButton)||(e.confirmButton.focus(),0))},Pn=function(){document.activeElement&&"function"==typeof document.activeElement.blur&&document.activeElement.blur()};var jn,In=function(e){"function"==typeof e.didDestroy?e.didDestroy():"function"==typeof e.onDestroy&&e.onDestroy()},Ln=function(e){delete e.params,delete Je.keydownHandler,delete Je.keydownTarget,Bn(Me),Bn(kt)},Bn=function(e){for(var t in e)e[t]=new WeakMap},Hn=Object.freeze({hideLoading:mt,disableLoading:mt,getInput:function(e){var t=Me.innerParams.get(e||this),n=Me.domCache.get(e||this);return n?te(n.content,t.input):null},close:At,closePopup:At,closeModal:At,closeToast:At,enableButtons:function(){Ot(this,["confirmButton","denyButton","cancelButton"],!1)},disableButtons:function(){Ot(this,["confirmButton","denyButton","cancelButton"],!0)},enableInput:function(){return Pt(this.getInput(),!1)},disableInput:function(){return Pt(this.getInput(),!0)},showValidationMessage:function(e){var t=Me.domCache.get(this),n=Me.innerParams.get(this);G(t.validationMessage,e),t.validationMessage.className=A["validation-message"],n.customClass&&n.customClass.validationMessage&&ae(t.validationMessage,n.customClass.validationMessage),ce(t.validationMessage);var o=this.getInput();o&&(o.setAttribute("aria-invalid",!0),o.setAttribute("aria-describedBy",A["validation-message"]),oe(o),ae(o,A.inputerror))},resetValidationMessage:function(){var e=Me.domCache.get(this);e.validationMessage&&de(e.validationMessage);var t=this.getInput();t&&(t.removeAttribute("aria-invalid"),t.removeAttribute("aria-describedBy"),re(t,A.inputerror))},getProgressSteps:function(){return Me.domCache.get(this).progressSteps},_main:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};ft(i({},t,e)),Je.currentInstance&&Je.currentInstance._destroy(),Je.currentInstance=this;var n=En(e,t);Lt(n),Object.freeze(n),Je.timeout&&(Je.timeout.stop(),delete Je.timeout),clearTimeout(Je.restoreFocusTimeout);var o=Tn(this);return Ke(this,n),Me.innerParams.set(this,n),Mn(this,o,n)},update:function(e){var t=S(),n=Me.innerParams.get(this);if(!t||X(t,n.hideClass.popup))return m("You're trying to update the closed or closing popup, that won't work. Use the update() method in preConfirm parameter or show a new popup.");var o={};Object.keys(e).forEach((function(t){qn.isUpdatableParameter(t)?o[t]=e[t]:m('Invalid parameter to update: "'.concat(t,'". Updatable params are listed here: https://github.com/sweetalert2/sweetalert2/blob/master/src/utils/params.js\n\nIf you think this parameter should be updatable, request it here: https://github.com/sweetalert2/sweetalert2/issues/new?template=02_feature_request.md'))}));var a=i({},n,o);Ke(this,a),Me.innerParams.set(this,a),Object.defineProperties(this,{params:{value:i({},this.params,e),writable:!1,enumerable:!0}})},_destroy:function(){var e=Me.domCache.get(this),t=Me.innerParams.get(this);t&&(e.popup&&Je.swalCloseEventFinishedCallback&&(Je.swalCloseEventFinishedCallback(),delete Je.swalCloseEventFinishedCallback),Je.deferDisposalTimer&&(clearTimeout(Je.deferDisposalTimer),delete Je.deferDisposalTimer),In(t),Ln(this))}}),Nn=function(){function e(){if(t(this,e),"undefined"!=typeof window){"undefined"==typeof Promise&&g("This package requires a Promise library, please include a shim to enable it in this browser (See: https://github.com/sweetalert2/sweetalert2/wiki/Migration-from-SweetAlert-to-SweetAlert2#1-ie-support)"),jn=this;for(var n=arguments.length,o=new Array(n),i=0;i<n;i++)o[i]=arguments[i];var a=Object.freeze(this.constructor.argsToParams(o));Object.defineProperties(this,{params:{value:a,writable:!1,enumerable:!0,configurable:!0}});var r=this._main(this.params);Me.promise.set(this,r)}}return o(e,[{key:"then",value:function(e){return Me.promise.get(this).then(e)}},{key:"finally",value:function(e){return Me.promise.get(this).finally(e)}}]),e}();i(Nn.prototype,Hn),i(Nn,ht),Object.keys(Hn).forEach((function(e){Nn[e]=function(){var t;if(jn)return(t=jn)[e].apply(t,arguments)}})),Nn.DismissReason=x,Nn.version="10.16.9";var qn=Nn;return qn.default=qn,qn}(),void 0!==this&&this.Sweetalert2&&(this.swal=this.sweetAlert=this.Swal=this.SweetAlert=this.Sweetalert2),"undefined"!=typeof document&&function(e,t){var n=e.createElement("style");if(e.getElementsByTagName("head")[0].appendChild(n),n.styleSheet)n.styleSheet.disabled||(n.styleSheet.cssText=t);else try{n.innerHTML=t}catch(e){n.innerText=t}}(document,'.swal2-popup.swal2-toast{flex-direction:column;align-items:stretch;width:auto;padding:1.25em;overflow-y:hidden;background:#fff;box-shadow:0 0 .625em #d9d9d9}.swal2-popup.swal2-toast .swal2-header{flex-direction:row;padding:0}.swal2-popup.swal2-toast .swal2-title{flex-grow:1;justify-content:flex-start;margin:0 .625em;font-size:1em}.swal2-popup.swal2-toast .swal2-loading{justify-content:center}.swal2-popup.swal2-toast .swal2-input{height:2em;margin:.3125em auto;font-size:1em}.swal2-popup.swal2-toast .swal2-validation-message{font-size:1em}.swal2-popup.swal2-toast .swal2-footer{margin:.5em 0 0;padding:.5em 0 0;font-size:.8em}.swal2-popup.swal2-toast .swal2-close{position:static;width:.8em;height:.8em;line-height:.8}.swal2-popup.swal2-toast .swal2-content{justify-content:flex-start;margin:0 .625em;padding:0;font-size:1em;text-align:initial}.swal2-popup.swal2-toast .swal2-html-container{padding:.625em 0 0}.swal2-popup.swal2-toast .swal2-html-container:empty{padding:0}.swal2-popup.swal2-toast .swal2-icon{width:2em;min-width:2em;height:2em;margin:0 .5em 0 0}.swal2-popup.swal2-toast .swal2-icon .swal2-icon-content{display:flex;align-items:center;font-size:1.8em;font-weight:700}@media all and (-ms-high-contrast:none),(-ms-high-contrast:active){.swal2-popup.swal2-toast .swal2-icon .swal2-icon-content{font-size:.25em}}.swal2-popup.swal2-toast .swal2-icon.swal2-success .swal2-success-ring{width:2em;height:2em}.swal2-popup.swal2-toast .swal2-icon.swal2-error [class^=swal2-x-mark-line]{top:.875em;width:1.375em}.swal2-popup.swal2-toast .swal2-icon.swal2-error [class^=swal2-x-mark-line][class$=left]{left:.3125em}.swal2-popup.swal2-toast .swal2-icon.swal2-error [class^=swal2-x-mark-line][class$=right]{right:.3125em}.swal2-popup.swal2-toast .swal2-actions{flex:1;flex-basis:auto!important;align-self:stretch;width:auto;height:2.2em;height:auto;margin:0 .3125em;margin-top:.3125em;padding:0}.swal2-popup.swal2-toast .swal2-styled{margin:.125em .3125em;padding:.3125em .625em;font-size:1em}.swal2-popup.swal2-toast .swal2-styled:focus{box-shadow:0 0 0 1px #fff,0 0 0 3px rgba(100,150,200,.5)}.swal2-popup.swal2-toast .swal2-success{border-color:#a5dc86}.swal2-popup.swal2-toast .swal2-success [class^=swal2-success-circular-line]{position:absolute;width:1.6em;height:3em;transform:rotate(45deg);border-radius:50%}.swal2-popup.swal2-toast .swal2-success [class^=swal2-success-circular-line][class$=left]{top:-.8em;left:-.5em;transform:rotate(-45deg);transform-origin:2em 2em;border-radius:4em 0 0 4em}.swal2-popup.swal2-toast .swal2-success [class^=swal2-success-circular-line][class$=right]{top:-.25em;left:.9375em;transform-origin:0 1.5em;border-radius:0 4em 4em 0}.swal2-popup.swal2-toast .swal2-success .swal2-success-ring{width:2em;height:2em}.swal2-popup.swal2-toast .swal2-success .swal2-success-fix{top:0;left:.4375em;width:.4375em;height:2.6875em}.swal2-popup.swal2-toast .swal2-success [class^=swal2-success-line]{height:.3125em}.swal2-popup.swal2-toast .swal2-success [class^=swal2-success-line][class$=tip]{top:1.125em;left:.1875em;width:.75em}.swal2-popup.swal2-toast .swal2-success [class^=swal2-success-line][class$=long]{top:.9375em;right:.1875em;width:1.375em}.swal2-popup.swal2-toast .swal2-success.swal2-icon-show .swal2-success-line-tip{-webkit-animation:swal2-toast-animate-success-line-tip .75s;animation:swal2-toast-animate-success-line-tip .75s}.swal2-popup.swal2-toast .swal2-success.swal2-icon-show .swal2-success-line-long{-webkit-animation:swal2-toast-animate-success-line-long .75s;animation:swal2-toast-animate-success-line-long .75s}.swal2-popup.swal2-toast.swal2-show{-webkit-animation:swal2-toast-show .5s;animation:swal2-toast-show .5s}.swal2-popup.swal2-toast.swal2-hide{-webkit-animation:swal2-toast-hide .1s forwards;animation:swal2-toast-hide .1s forwards}.swal2-container{display:flex;position:fixed;z-index:1060;top:0;right:0;bottom:0;left:0;flex-direction:row;align-items:center;justify-content:center;padding:.625em;overflow-x:hidden;transition:background-color .1s;-webkit-overflow-scrolling:touch}.swal2-container.swal2-backdrop-show,.swal2-container.swal2-noanimation{background:rgba(0,0,0,.4)}.swal2-container.swal2-backdrop-hide{background:0 0!important}.swal2-container.swal2-top{align-items:flex-start}.swal2-container.swal2-top-left,.swal2-container.swal2-top-start{align-items:flex-start;justify-content:flex-start}.swal2-container.swal2-top-end,.swal2-container.swal2-top-right{align-items:flex-start;justify-content:flex-end}.swal2-container.swal2-center{align-items:center}.swal2-container.swal2-center-left,.swal2-container.swal2-center-start{align-items:center;justify-content:flex-start}.swal2-container.swal2-center-end,.swal2-container.swal2-center-right{align-items:center;justify-content:flex-end}.swal2-container.swal2-bottom{align-items:flex-end}.swal2-container.swal2-bottom-left,.swal2-container.swal2-bottom-start{align-items:flex-end;justify-content:flex-start}.swal2-container.swal2-bottom-end,.swal2-container.swal2-bottom-right{align-items:flex-end;justify-content:flex-end}.swal2-container.swal2-bottom-end>:first-child,.swal2-container.swal2-bottom-left>:first-child,.swal2-container.swal2-bottom-right>:first-child,.swal2-container.swal2-bottom-start>:first-child,.swal2-container.swal2-bottom>:first-child{margin-top:auto}.swal2-container.swal2-grow-fullscreen>.swal2-modal{display:flex!important;flex:1;align-self:stretch;justify-content:center}.swal2-container.swal2-grow-row>.swal2-modal{display:flex!important;flex:1;align-content:center;justify-content:center}.swal2-container.swal2-grow-column{flex:1;flex-direction:column}.swal2-container.swal2-grow-column.swal2-bottom,.swal2-container.swal2-grow-column.swal2-center,.swal2-container.swal2-grow-column.swal2-top{align-items:center}.swal2-container.swal2-grow-column.swal2-bottom-left,.swal2-container.swal2-grow-column.swal2-bottom-start,.swal2-container.swal2-grow-column.swal2-center-left,.swal2-container.swal2-grow-column.swal2-center-start,.swal2-container.swal2-grow-column.swal2-top-left,.swal2-container.swal2-grow-column.swal2-top-start{align-items:flex-start}.swal2-container.swal2-grow-column.swal2-bottom-end,.swal2-container.swal2-grow-column.swal2-bottom-right,.swal2-container.swal2-grow-column.swal2-center-end,.swal2-container.swal2-grow-column.swal2-center-right,.swal2-container.swal2-grow-column.swal2-top-end,.swal2-container.swal2-grow-column.swal2-top-right{align-items:flex-end}.swal2-container.swal2-grow-column>.swal2-modal{display:flex!important;flex:1;align-content:center;justify-content:center}.swal2-container.swal2-no-transition{transition:none!important}.swal2-container:not(.swal2-top):not(.swal2-top-start):not(.swal2-top-end):not(.swal2-top-left):not(.swal2-top-right):not(.swal2-center-start):not(.swal2-center-end):not(.swal2-center-left):not(.swal2-center-right):not(.swal2-bottom):not(.swal2-bottom-start):not(.swal2-bottom-end):not(.swal2-bottom-left):not(.swal2-bottom-right):not(.swal2-grow-fullscreen)>.swal2-modal{margin:auto}@media all and (-ms-high-contrast:none),(-ms-high-contrast:active){.swal2-container .swal2-modal{margin:0!important}}.swal2-popup{display:none;position:relative;box-sizing:border-box;flex-direction:column;justify-content:center;width:32em;max-width:100%;padding:1.25em;border:none;border-radius:5px;background:#fff;font-family:inherit;font-size:1rem}.swal2-popup:focus{outline:0}.swal2-popup.swal2-loading{overflow-y:hidden}.swal2-header{display:flex;flex-direction:column;align-items:center;padding:0 1.8em}.swal2-title{position:relative;max-width:100%;margin:0 0 .4em;padding:0;color:#595959;font-size:1.875em;font-weight:600;text-align:center;text-transform:none;word-wrap:break-word}.swal2-actions{display:flex;z-index:1;box-sizing:border-box;flex-wrap:wrap;align-items:center;justify-content:center;width:100%;margin:1.25em auto 0;padding:0}.swal2-actions:not(.swal2-loading) .swal2-styled[disabled]{opacity:.4}.swal2-actions:not(.swal2-loading) .swal2-styled:hover{background-image:linear-gradient(rgba(0,0,0,.1),rgba(0,0,0,.1))}.swal2-actions:not(.swal2-loading) .swal2-styled:active{background-image:linear-gradient(rgba(0,0,0,.2),rgba(0,0,0,.2))}.swal2-loader{display:none;align-items:center;justify-content:center;width:2.2em;height:2.2em;margin:0 1.875em;-webkit-animation:swal2-rotate-loading 1.5s linear 0s infinite normal;animation:swal2-rotate-loading 1.5s linear 0s infinite normal;border-width:.25em;border-style:solid;border-radius:100%;border-color:#2778c4 transparent #2778c4 transparent}.swal2-styled{margin:.3125em;padding:.625em 1.1em;box-shadow:none;font-weight:500}.swal2-styled:not([disabled]){cursor:pointer}.swal2-styled.swal2-confirm{border:0;border-radius:.25em;background:initial;background-color:#2778c4;color:#fff;font-size:1em}.swal2-styled.swal2-deny{border:0;border-radius:.25em;background:initial;background-color:#d14529;color:#fff;font-size:1em}.swal2-styled.swal2-cancel{border:0;border-radius:.25em;background:initial;background-color:#757575;color:#fff;font-size:1em}.swal2-styled:focus{outline:0;box-shadow:0 0 0 3px rgba(100,150,200,.5)}.swal2-styled::-moz-focus-inner{border:0}.swal2-footer{justify-content:center;margin:1.25em 0 0;padding:1em 0 0;border-top:1px solid #eee;color:#545454;font-size:1em}.swal2-timer-progress-bar-container{position:absolute;right:0;bottom:0;left:0;height:.25em;overflow:hidden;border-bottom-right-radius:5px;border-bottom-left-radius:5px}.swal2-timer-progress-bar{width:100%;height:.25em;background:rgba(0,0,0,.2)}.swal2-image{max-width:100%;margin:1.25em auto}.swal2-close{position:absolute;z-index:2;top:0;right:0;align-items:center;justify-content:center;width:1.2em;height:1.2em;padding:0;overflow:hidden;transition:color .1s ease-out;border:none;border-radius:5px;background:0 0;color:#ccc;font-family:serif;font-size:2.5em;line-height:1.2;cursor:pointer}.swal2-close:hover{transform:none;background:0 0;color:#f27474}.swal2-close:focus{outline:0;box-shadow:inset 0 0 0 3px rgba(100,150,200,.5)}.swal2-close::-moz-focus-inner{border:0}.swal2-content{z-index:1;justify-content:center;margin:0;padding:0 1.6em;color:#545454;font-size:1.125em;font-weight:400;line-height:normal;text-align:center;word-wrap:break-word}.swal2-checkbox,.swal2-file,.swal2-input,.swal2-radio,.swal2-select,.swal2-textarea{margin:1em auto}.swal2-file,.swal2-input,.swal2-textarea{box-sizing:border-box;width:100%;transition:border-color .3s,box-shadow .3s;border:1px solid #d9d9d9;border-radius:.1875em;background:inherit;box-shadow:inset 0 1px 1px rgba(0,0,0,.06);color:inherit;font-size:1.125em}.swal2-file.swal2-inputerror,.swal2-input.swal2-inputerror,.swal2-textarea.swal2-inputerror{border-color:#f27474!important;box-shadow:0 0 2px #f27474!important}.swal2-file:focus,.swal2-input:focus,.swal2-textarea:focus{border:1px solid #b4dbed;outline:0;box-shadow:0 0 0 3px rgba(100,150,200,.5)}.swal2-file::-moz-placeholder,.swal2-input::-moz-placeholder,.swal2-textarea::-moz-placeholder{color:#ccc}.swal2-file:-ms-input-placeholder,.swal2-input:-ms-input-placeholder,.swal2-textarea:-ms-input-placeholder{color:#ccc}.swal2-file::placeholder,.swal2-input::placeholder,.swal2-textarea::placeholder{color:#ccc}.swal2-range{margin:1em auto;background:#fff}.swal2-range input{width:80%}.swal2-range output{width:20%;color:inherit;font-weight:600;text-align:center}.swal2-range input,.swal2-range output{height:2.625em;padding:0;font-size:1.125em;line-height:2.625em}.swal2-input{height:2.625em;padding:0 .75em}.swal2-input[type=number]{max-width:10em}.swal2-file{background:inherit;font-size:1.125em}.swal2-textarea{height:6.75em;padding:.75em}.swal2-select{min-width:50%;max-width:100%;padding:.375em .625em;background:inherit;color:inherit;font-size:1.125em}.swal2-checkbox,.swal2-radio{align-items:center;justify-content:center;background:#fff;color:inherit}.swal2-checkbox label,.swal2-radio label{margin:0 .6em;font-size:1.125em}.swal2-checkbox input,.swal2-radio input{flex-shrink:0;margin:0 .4em}.swal2-input-label{display:flex;justify-content:center;margin:1em auto}.swal2-validation-message{align-items:center;justify-content:center;margin:0 -2.7em;padding:.625em;overflow:hidden;background:#f0f0f0;color:#666;font-size:1em;font-weight:300}.swal2-validation-message::before{content:"!";display:inline-block;width:1.5em;min-width:1.5em;height:1.5em;margin:0 .625em;border-radius:50%;background-color:#f27474;color:#fff;font-weight:600;line-height:1.5em;text-align:center}.swal2-icon{position:relative;box-sizing:content-box;justify-content:center;width:5em;height:5em;margin:1.25em auto 1.875em;border:.25em solid transparent;border-radius:50%;border-color:#000;font-family:inherit;line-height:5em;cursor:default;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}.swal2-icon .swal2-icon-content{display:flex;align-items:center;font-size:3.75em}.swal2-icon.swal2-error{border-color:#f27474;color:#f27474}.swal2-icon.swal2-error .swal2-x-mark{position:relative;flex-grow:1}.swal2-icon.swal2-error [class^=swal2-x-mark-line]{display:block;position:absolute;top:2.3125em;width:2.9375em;height:.3125em;border-radius:.125em;background-color:#f27474}.swal2-icon.swal2-error [class^=swal2-x-mark-line][class$=left]{left:1.0625em;transform:rotate(45deg)}.swal2-icon.swal2-error [class^=swal2-x-mark-line][class$=right]{right:1em;transform:rotate(-45deg)}.swal2-icon.swal2-error.swal2-icon-show{-webkit-animation:swal2-animate-error-icon .5s;animation:swal2-animate-error-icon .5s}.swal2-icon.swal2-error.swal2-icon-show .swal2-x-mark{-webkit-animation:swal2-animate-error-x-mark .5s;animation:swal2-animate-error-x-mark .5s}.swal2-icon.swal2-warning{border-color:#facea8;color:#f8bb86}.swal2-icon.swal2-info{border-color:#9de0f6;color:#3fc3ee}.swal2-icon.swal2-question{border-color:#c9dae1;color:#87adbd}.swal2-icon.swal2-success{border-color:#a5dc86;color:#a5dc86}.swal2-icon.swal2-success [class^=swal2-success-circular-line]{position:absolute;width:3.75em;height:7.5em;transform:rotate(45deg);border-radius:50%}.swal2-icon.swal2-success [class^=swal2-success-circular-line][class$=left]{top:-.4375em;left:-2.0635em;transform:rotate(-45deg);transform-origin:3.75em 3.75em;border-radius:7.5em 0 0 7.5em}.swal2-icon.swal2-success [class^=swal2-success-circular-line][class$=right]{top:-.6875em;left:1.875em;transform:rotate(-45deg);transform-origin:0 3.75em;border-radius:0 7.5em 7.5em 0}.swal2-icon.swal2-success .swal2-success-ring{position:absolute;z-index:2;top:-.25em;left:-.25em;box-sizing:content-box;width:100%;height:100%;border:.25em solid rgba(165,220,134,.3);border-radius:50%}.swal2-icon.swal2-success .swal2-success-fix{position:absolute;z-index:1;top:.5em;left:1.625em;width:.4375em;height:5.625em;transform:rotate(-45deg)}.swal2-icon.swal2-success [class^=swal2-success-line]{display:block;position:absolute;z-index:2;height:.3125em;border-radius:.125em;background-color:#a5dc86}.swal2-icon.swal2-success [class^=swal2-success-line][class$=tip]{top:2.875em;left:.8125em;width:1.5625em;transform:rotate(45deg)}.swal2-icon.swal2-success [class^=swal2-success-line][class$=long]{top:2.375em;right:.5em;width:2.9375em;transform:rotate(-45deg)}.swal2-icon.swal2-success.swal2-icon-show .swal2-success-line-tip{-webkit-animation:swal2-animate-success-line-tip .75s;animation:swal2-animate-success-line-tip .75s}.swal2-icon.swal2-success.swal2-icon-show .swal2-success-line-long{-webkit-animation:swal2-animate-success-line-long .75s;animation:swal2-animate-success-line-long .75s}.swal2-icon.swal2-success.swal2-icon-show .swal2-success-circular-line-right{-webkit-animation:swal2-rotate-success-circular-line 4.25s ease-in;animation:swal2-rotate-success-circular-line 4.25s ease-in}.swal2-progress-steps{flex-wrap:wrap;align-items:center;max-width:100%;margin:0 0 1.25em;padding:0;background:inherit;font-weight:600}.swal2-progress-steps li{display:inline-block;position:relative}.swal2-progress-steps .swal2-progress-step{z-index:20;flex-shrink:0;width:2em;height:2em;border-radius:2em;background:#2778c4;color:#fff;line-height:2em;text-align:center}.swal2-progress-steps .swal2-progress-step.swal2-active-progress-step{background:#2778c4}.swal2-progress-steps .swal2-progress-step.swal2-active-progress-step~.swal2-progress-step{background:#add8e6;color:#fff}.swal2-progress-steps .swal2-progress-step.swal2-active-progress-step~.swal2-progress-step-line{background:#add8e6}.swal2-progress-steps .swal2-progress-step-line{z-index:10;flex-shrink:0;width:2.5em;height:.4em;margin:0 -1px;background:#2778c4}[class^=swal2]{-webkit-tap-highlight-color:transparent}.swal2-show{-webkit-animation:swal2-show .3s;animation:swal2-show .3s}.swal2-hide{-webkit-animation:swal2-hide .15s forwards;animation:swal2-hide .15s forwards}.swal2-noanimation{transition:none}.swal2-scrollbar-measure{position:absolute;top:-9999px;width:50px;height:50px;overflow:scroll}.swal2-rtl .swal2-close{right:auto;left:0}.swal2-rtl .swal2-timer-progress-bar{right:0;left:auto}@supports (-ms-accelerator:true){.swal2-range input{width:100%!important}.swal2-range output{display:none}}@media all and (-ms-high-contrast:none),(-ms-high-contrast:active){.swal2-range input{width:100%!important}.swal2-range output{display:none}}@-webkit-keyframes swal2-toast-show{0%{transform:translateY(-.625em) rotateZ(2deg)}33%{transform:translateY(0) rotateZ(-2deg)}66%{transform:translateY(.3125em) rotateZ(2deg)}100%{transform:translateY(0) rotateZ(0)}}@keyframes swal2-toast-show{0%{transform:translateY(-.625em) rotateZ(2deg)}33%{transform:translateY(0) rotateZ(-2deg)}66%{transform:translateY(.3125em) rotateZ(2deg)}100%{transform:translateY(0) rotateZ(0)}}@-webkit-keyframes swal2-toast-hide{100%{transform:rotateZ(1deg);opacity:0}}@keyframes swal2-toast-hide{100%{transform:rotateZ(1deg);opacity:0}}@-webkit-keyframes swal2-toast-animate-success-line-tip{0%{top:.5625em;left:.0625em;width:0}54%{top:.125em;left:.125em;width:0}70%{top:.625em;left:-.25em;width:1.625em}84%{top:1.0625em;left:.75em;width:.5em}100%{top:1.125em;left:.1875em;width:.75em}}@keyframes swal2-toast-animate-success-line-tip{0%{top:.5625em;left:.0625em;width:0}54%{top:.125em;left:.125em;width:0}70%{top:.625em;left:-.25em;width:1.625em}84%{top:1.0625em;left:.75em;width:.5em}100%{top:1.125em;left:.1875em;width:.75em}}@-webkit-keyframes swal2-toast-animate-success-line-long{0%{top:1.625em;right:1.375em;width:0}65%{top:1.25em;right:.9375em;width:0}84%{top:.9375em;right:0;width:1.125em}100%{top:.9375em;right:.1875em;width:1.375em}}@keyframes swal2-toast-animate-success-line-long{0%{top:1.625em;right:1.375em;width:0}65%{top:1.25em;right:.9375em;width:0}84%{top:.9375em;right:0;width:1.125em}100%{top:.9375em;right:.1875em;width:1.375em}}@-webkit-keyframes swal2-show{0%{transform:scale(.7)}45%{transform:scale(1.05)}80%{transform:scale(.95)}100%{transform:scale(1)}}@keyframes swal2-show{0%{transform:scale(.7)}45%{transform:scale(1.05)}80%{transform:scale(.95)}100%{transform:scale(1)}}@-webkit-keyframes swal2-hide{0%{transform:scale(1);opacity:1}100%{transform:scale(.5);opacity:0}}@keyframes swal2-hide{0%{transform:scale(1);opacity:1}100%{transform:scale(.5);opacity:0}}@-webkit-keyframes swal2-animate-success-line-tip{0%{top:1.1875em;left:.0625em;width:0}54%{top:1.0625em;left:.125em;width:0}70%{top:2.1875em;left:-.375em;width:3.125em}84%{top:3em;left:1.3125em;width:1.0625em}100%{top:2.8125em;left:.8125em;width:1.5625em}}@keyframes swal2-animate-success-line-tip{0%{top:1.1875em;left:.0625em;width:0}54%{top:1.0625em;left:.125em;width:0}70%{top:2.1875em;left:-.375em;width:3.125em}84%{top:3em;left:1.3125em;width:1.0625em}100%{top:2.8125em;left:.8125em;width:1.5625em}}@-webkit-keyframes swal2-animate-success-line-long{0%{top:3.375em;right:2.875em;width:0}65%{top:3.375em;right:2.875em;width:0}84%{top:2.1875em;right:0;width:3.4375em}100%{top:2.375em;right:.5em;width:2.9375em}}@keyframes swal2-animate-success-line-long{0%{top:3.375em;right:2.875em;width:0}65%{top:3.375em;right:2.875em;width:0}84%{top:2.1875em;right:0;width:3.4375em}100%{top:2.375em;right:.5em;width:2.9375em}}@-webkit-keyframes swal2-rotate-success-circular-line{0%{transform:rotate(-45deg)}5%{transform:rotate(-45deg)}12%{transform:rotate(-405deg)}100%{transform:rotate(-405deg)}}@keyframes swal2-rotate-success-circular-line{0%{transform:rotate(-45deg)}5%{transform:rotate(-45deg)}12%{transform:rotate(-405deg)}100%{transform:rotate(-405deg)}}@-webkit-keyframes swal2-animate-error-x-mark{0%{margin-top:1.625em;transform:scale(.4);opacity:0}50%{margin-top:1.625em;transform:scale(.4);opacity:0}80%{margin-top:-.375em;transform:scale(1.15)}100%{margin-top:0;transform:scale(1);opacity:1}}@keyframes swal2-animate-error-x-mark{0%{margin-top:1.625em;transform:scale(.4);opacity:0}50%{margin-top:1.625em;transform:scale(.4);opacity:0}80%{margin-top:-.375em;transform:scale(1.15)}100%{margin-top:0;transform:scale(1);opacity:1}}@-webkit-keyframes swal2-animate-error-icon{0%{transform:rotateX(100deg);opacity:0}100%{transform:rotateX(0);opacity:1}}@keyframes swal2-animate-error-icon{0%{transform:rotateX(100deg);opacity:0}100%{transform:rotateX(0);opacity:1}}@-webkit-keyframes swal2-rotate-loading{0%{transform:rotate(0)}100%{transform:rotate(360deg)}}@keyframes swal2-rotate-loading{0%{transform:rotate(0)}100%{transform:rotate(360deg)}}body.swal2-shown:not(.swal2-no-backdrop):not(.swal2-toast-shown){overflow:hidden}body.swal2-height-auto{height:auto!important}body.swal2-no-backdrop .swal2-container{top:auto;right:auto;bottom:auto;left:auto;max-width:calc(100% - .625em * 2);background-color:transparent!important}body.swal2-no-backdrop .swal2-container>.swal2-modal{box-shadow:0 0 10px rgba(0,0,0,.4)}body.swal2-no-backdrop .swal2-container.swal2-top{top:0;left:50%;transform:translateX(-50%)}body.swal2-no-backdrop .swal2-container.swal2-top-left,body.swal2-no-backdrop .swal2-container.swal2-top-start{top:0;left:0}body.swal2-no-backdrop .swal2-container.swal2-top-end,body.swal2-no-backdrop .swal2-container.swal2-top-right{top:0;right:0}body.swal2-no-backdrop .swal2-container.swal2-center{top:50%;left:50%;transform:translate(-50%,-50%)}body.swal2-no-backdrop .swal2-container.swal2-center-left,body.swal2-no-backdrop .swal2-container.swal2-center-start{top:50%;left:0;transform:translateY(-50%)}body.swal2-no-backdrop .swal2-container.swal2-center-end,body.swal2-no-backdrop .swal2-container.swal2-center-right{top:50%;right:0;transform:translateY(-50%)}body.swal2-no-backdrop .swal2-container.swal2-bottom{bottom:0;left:50%;transform:translateX(-50%)}body.swal2-no-backdrop .swal2-container.swal2-bottom-left,body.swal2-no-backdrop .swal2-container.swal2-bottom-start{bottom:0;left:0}body.swal2-no-backdrop .swal2-container.swal2-bottom-end,body.swal2-no-backdrop .swal2-container.swal2-bottom-right{right:0;bottom:0}@media print{body.swal2-shown:not(.swal2-no-backdrop):not(.swal2-toast-shown){overflow-y:scroll!important}body.swal2-shown:not(.swal2-no-backdrop):not(.swal2-toast-shown)>[aria-hidden=true]{display:none}body.swal2-shown:not(.swal2-no-backdrop):not(.swal2-toast-shown) .swal2-container{position:static!important}}body.swal2-toast-shown .swal2-container{background-color:transparent}body.swal2-toast-shown .swal2-container.swal2-top{top:0;right:auto;bottom:auto;left:50%;transform:translateX(-50%)}body.swal2-toast-shown .swal2-container.swal2-top-end,body.swal2-toast-shown .swal2-container.swal2-top-right{top:0;right:0;bottom:auto;left:auto}body.swal2-toast-shown .swal2-container.swal2-top-left,body.swal2-toast-shown .swal2-container.swal2-top-start{top:0;right:auto;bottom:auto;left:0}body.swal2-toast-shown .swal2-container.swal2-center-left,body.swal2-toast-shown .swal2-container.swal2-center-start{top:50%;right:auto;bottom:auto;left:0;transform:translateY(-50%)}body.swal2-toast-shown .swal2-container.swal2-center{top:50%;right:auto;bottom:auto;left:50%;transform:translate(-50%,-50%)}body.swal2-toast-shown .swal2-container.swal2-center-end,body.swal2-toast-shown .swal2-container.swal2-center-right{top:50%;right:0;bottom:auto;left:auto;transform:translateY(-50%)}body.swal2-toast-shown .swal2-container.swal2-bottom-left,body.swal2-toast-shown .swal2-container.swal2-bottom-start{top:auto;right:auto;bottom:0;left:0}body.swal2-toast-shown .swal2-container.swal2-bottom{top:auto;right:auto;bottom:0;left:50%;transform:translateX(-50%)}body.swal2-toast-shown .swal2-container.swal2-bottom-end,body.swal2-toast-shown .swal2-container.swal2-bottom-right{top:auto;right:0;bottom:0;left:auto}')},567:function(e){"use strict";e.exports=window.jQuery}},t={};function n(o){var i=t[o];if(void 0!==i)return i.exports;var a=t[o]={exports:{}};return e[o].call(a.exports,a,a.exports,n),a.exports}n.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return n.d(t,{a:t}),t},n.d=function(e,t){for(var o in t)n.o(t,o)&&!n.o(e,o)&&Object.defineProperty(e,o,{enumerable:!0,get:t[o]})},n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},function(){"use strict";n(233),n(207);var e=n(455),t=n.n(e);const o=window.WPCodeSnippetManager||function(e,n,o){const i={editor_id:"wpcode_snippet_code",unload_set:!1,icon_lock:'<svg width="22" height="28" viewBox="0 0 22 28" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M19 9.33333H17.6666V6.66667C17.6666 2.98667 14.68 0 11 0C7.31998 0 4.33331 2.98667 4.33331 6.66667V9.33333H2.99998C1.53331 9.33333 0.333313 10.5333 0.333313 12V25.3333C0.333313 26.8 1.53331 28 2.99998 28H19C20.4666 28 21.6666 26.8 21.6666 25.3333V12C21.6666 10.5333 20.4666 9.33333 19 9.33333ZM6.99998 6.66667C6.99998 4.45333 8.78665 2.66667 11 2.66667C13.2133 2.66667 15 4.45333 15 6.66667V9.33333H6.99998V6.66667ZM19 25.3333H2.99998V12H19V25.3333ZM11 21.3333C12.4666 21.3333 13.6666 20.1333 13.6666 18.6667C13.6666 17.2 12.4666 16 11 16C9.53331 16 8.33331 17.2 8.33331 18.6667C8.33331 20.1333 9.53331 21.3333 11 21.3333Z" fill="#8A8A8A"/></svg>',l10n:wpcode,saving_height:!1,init:function(){i.should_init()&&(n.WPCodeSnippetManager=i,i.find_elements(),i.init_location_picker(),i.init_location_click(),i.init_snippet_type_switcher(),i.init_auto_insert_toggle(),i.init_dynamic_hide(),i.init_copy_target(),i.init_tags_picker(),i.init_metabox_toggler(),i.init_select2(),i.init_tinymce_listener(),i.unload_change_listener(),i.init_save_to_library(),i.init_custom_shortcode(),i.init_conditional_logic_notice(),i.init_device_type(),i.init_datetime_lite(),i.init_shortcode_attributes(),i.update_smart_tags_attributes(),i.restore_cursor_position(),i.maybe_highlight_error_line(),i.init_load_as_file(),i.init_edit_lock(),i.editor_resizer(),i.init_ai_button(),i.keyboard_shortcuts(),i.update_available_locations(i.switcher.val(),!1))},should_init:function(){return null!==e.getElementById(i.editor_id)},find_elements(){i.location_dropdown=o("#wpcode_auto_insert_location"),i.switcher=o(e.getElementById("wpcode_snippet_type")),i.$body=o("body"),i.$text_editor="undefined"!=typeof tinymce&&tinymce.get("wpcode_snippet_text"),i.$selected_location_display=o("#wpcode-selected-location-display"),i.code_type=i.switcher.val()},init_snippet_type_switcher:function(){i.switcher.on("change",(function(){if(o(this).find(":selected").hasClass("wpcode-pro"))return i.switcher.val(i.code_type),void i.show_pro_notice(i.l10n.blocks_title,i.l10n.blocks_text,i.l10n.blocks_url,i.l10n.blocks_button);if(i.set_before_unload(),n.WPCodeAdminCodeEditor.switch_code_mode(i.editor_id,o(this).val(),o(this).find(":selected").data("mode"),o(this).find(":selected").data("lint")),i.$body.removeClass("wpcode-code-type-"+i.code_type),i.$body.addClass("wpcode-code-type-"+o(this).val()),"text"===i.switcher.val()){const e=n.WPCodeAdminCodeEditor.get_value(i.editor_id);i.$text_editor?i.$text_editor.setContent(e):o("#wpcode_snippet_text").val(e)}else n.WPCodeAdminCodeEditor.refresh(i.editor_id);const e=i.switcher.val();"php"!==i.code_type&&"php"!==e||i.update_available_locations(e),i.switcher.data("previous-type",i.code_type),i.code_type=e}))},update_available_locations(e){let t=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];const n=i.location_dropdown.find(".wpcode-list-item");n.removeClass("wpcode-list-item-disabled"),n.find('input[type="radio"]').prop("disabled",!1);const a=n.filter((function(){const t=o(this).data("code-type");return"all"!==t&&e!==t}));if(a.length>0&&(a.addClass("wpcode-list-item-disabled"),a.find('input[type="radio"]').prop("disabled",!0)),i.location_dropdown.find(".wpcode-items-list-category").each((function(){const e=o(this).find(".wpcode-list-item");e.sort((function(e,t){return o(e).data("index")-o(t).data("index")})),o(this).html(e)})),a.detach().appendTo(i.location_dropdown.find(".wpcode-items-list-category")),t){const e=i.location_dropdown.find(".wpcode-list-item:not(.wpcode-list-item-disabled):not(.wpcode-list-item-separator)").first();e.length>0&&e.find('input[type="radio"]').prop("checked",!0).trigger("change")}},init_location_picker:function(){i.location_dropdown.on("keydown",".wpcode-list-item-location",(function(e){if("Enter"===e.key||" "===e.key){const e=o(this).find('input[type="radio"]');e.prop("disabled")||(e.prop("checked",!0).trigger("change"),i.$selected_location_display.focus())}})),i.location_dropdown.on("change",'input[type="radio"]',(function(e){i.location_dropdown.find(".wpcode-list-item").removeClass("wpcode-list-item-selected");const t=o(this).closest(".wpcode-list-item");t.addClass("wpcode-list-item-selected"),i.$selected_location_display.text(t.find(".wpcode-list-item-title").attr("title")),i.location_dropdown.is(":visible")&&i.close_location_dropdown()}))},init_location_click:function(){i.location_dropdown.hide(),o("body").on("click",".wpcode-list-item",(function(e){const t=o(this).find("label").first();t.data("upgrade-title")&&i.show_pro_notice(t.data("upgrade-title"),t.data("upgrade-text"),t.data("upgrade-link"),t.data("upgrade-button"))})),i.$selected_location_display.on("click, focus",(function(){i.location_dropdown.slideDown(200,(function(){i.init_dropdown_close()})),i.scroll_to_location_dropdown()}))},init_dropdown_close:function(){o(e).on("click.wpcodelocation",(function(e){o(e.target).closest("#wpcode_auto_insert_location").length||o(e.target).closest(".swal2-container").length||i.close_location_dropdown()}))},close_location_dropdown:function(){i.location_dropdown.hide(),o(e).off("click.wpcodelocation"),i.scroll_to_location_dropdown(300)},scroll_to_location_dropdown:function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:100;const t=i.$selected_location_display.offset();t&&o("html, body").animate({scrollTop:t.top-e},200)},init_auto_insert_toggle:function(){const t={toggles:"",init:function(){t.toggles=o(e.querySelectorAll(".wpcode-button-toggle")),t.listen_to_switch()},listen_to_switch:function(){t.toggles.each((function(){const e=o(this).find(".wpcode-button-toggle-input");o(this).on("click",".wpcode-button",(function(n){n.preventDefault(),i.set_before_unload(),e.val(o(this).val()).change(),t.make_button_active(o(this))}))}))},make_button_active:function(e){e.closest(".wpcode-button-toggle").find(".wpcode-button").each((function(){e.is(o(this))?o(this).removeClass("wpcode-button-secondary-inactive"):o(this).addClass("wpcode-button-secondary-inactive")}))}};t.init()},init_dynamic_hide:function(){const e={init:function(){e.elements=o("[data-show-if-id]"),e.add_listeners()},add_listeners:function(){e.elements.each((function(){const t=o(this),n=t.data("show-if-id");if(""===n)return;let i=!1,a=String(t.data("show-if-value")).split(",");t.data("hide-if-value")&&(a=String(t.data("hide-if-value")).split(","),i=!0);const r=o(n);o(".wpcode-admin-page #wpbody-content").on("change",n,(function(){e.maybe_hide(o(this),t,a,i)})),e.maybe_hide(r,t,a,i)}))},maybe_hide:function(e,t,n,o){let a=String(e.val());if("checkbox"===e.attr("type")&&(a=e.prop("checked")?"1":"0"),"radio"===e.attr("type")&&(a=e.closest("form").find('input[name="'+e.attr("name")+'"]:checked').val()),o){if(n.indexOf(a)>=0)return void t.hide();t.show(),t.find(".wpcode-select2").length>0&&i.init_select2()}else n.indexOf(a)<0?t.hide():(t.show(),t.find(".wpcode-select2").length>0&&i.init_select2())}};e.init()},init_copy_target:function(){o(".wpcode-copy-target").on("click",(function(e){e.preventDefault();const t=o(this),n=t.data("target"),i=t.data("prefix"),a=t.data("suffix"),r=o(n).val();r&&(navigator.clipboard.writeText(i+r+a),t.addClass("wpcode-show-success-icon"),setTimeout((function(){t.removeClass("wpcode-show-success-icon")}),500))}))},init_select2:function(){o(".wpcode-select2").selectWoo({templateResult:function(e){const t=o(e.element),n=t.data("label-pill"),i=t.data("upgrade-title"),a=t.data("upgrade-text"),r=t.data("upgrade-link"),s=t.data("upgrade-button"),l=o('<span class="wpcode-pro-pill">'+n+"</span>");return""!==i&&l.attr("data-upgrade-title",i),""!==a&&l.attr("data-upgrade-text",a),""!==r&&l.attr("data-upgrade-link",r),""!==s&&l.attr("data-upgrade-button",s),void 0!==n&&""!==n?((e=o("<span>"+e.text+"</span>")).append(l),e):e.text}}),o("body").on("click",".select2-results__options",(function(e){const t=o(this).closest(".select2-results__option").find(".wpcode-pro-pill");t.length>0&&i.show_pro_notice(t.data("upgrade-title"),t.data("upgrade-text"),t.data("upgrade-link"),t.data("upgrade-button"))}))},init_tags_picker:function(){const e=o(".wpcode-tags-picker");e.selectWoo({tags:!0,ajax:{url:ajaxurl,data:function(e){return{action:"ajax-tag-search",tax:"wpcode_tags",q:e.term?e.term:""}},processResults:function(e){const t=e.split(","),n=[];return t.forEach((function(e){""!==e&&n.push({id:e,text:e})})),{results:n}}}}),e.on("change",(function(){i.set_before_unload();const e=o(this).data("target");o(e).val(o(this).val().join(","))}))},init_metabox_toggler:function(){o(".wpcode-metabox-title").on("click",(function(){o(this).parent().toggleClass("wpcode-metabox-collapsed")}))},init_tinymce_listener(){if(null===i.$text_editor)return i.$text_editor=tinymce.get("wpcode_snippet_text"),void setTimeout(i.init_tinymce_listener,100);!1!==i.$text_editor?i.$text_editor.on("Paste Change input Undo Redo",(function(){i.set_before_unload(),clearTimeout(i.editor_change_handler),i.editor_change_handler=setTimeout((function(){n.WPCodeAdminCodeEditor.set_value(i.editor_id,i.$text_editor.getContent())}),100)})):o("#wpcode_snippet_text").on("paste change input",(function(){i.set_before_unload(),clearTimeout(i.editor_change_handler),i.editor_change_handler=setTimeout((function(){n.WPCodeAdminCodeEditor.set_value(i.editor_id,o("#wpcode_snippet_text").val())}),100)}))},set_before_unload(){i.unload_set||(i.unload_set=!0,i.catch_unsaved_button=!0,o(n).on("beforeunload",(function(){return"Are you sure?"})))},unload_change_listener(){const e=n.WPCodeAdminCodeEditor.get_editor(i.editor_id);e&&e.on("change",(function(){i.set_before_unload()}));const t=o("#wpcode-snippet-manager-form");t.on("change","input, select",(function(){i.set_before_unload()})),t.on("submit",(function(){o(n).off("beforeunload"),i.save_cursor_position()}))},save_cursor_position(){const e=n.WPCodeAdminCodeEditor.get_editor(i.editor_id);e&&(i.cursor_position=e.getCursor(),localStorage.setItem("wpcode_cursor_position",JSON.stringify(i.cursor_position)),localStorage.setItem("wpcode_scroll_position",JSON.stringify(e.getScrollInfo())))},restore_cursor_position(){const e=localStorage.getItem("wpcode_cursor_position");if(e){const t=JSON.parse(e),o=localStorage.getItem("wpcode_scroll_position");localStorage.removeItem("wpcode_cursor_position");const a=n.WPCodeAdminCodeEditor.get_editor(i.editor_id);if(a&&(a.focus(),a.setCursor(t),o)){const e=JSON.parse(o);a.scrollTo(e.left,e.top)}}},init_save_to_library(){o("#wpcode_save_to_library").click((function(e){e.preventDefault(),i.show_pro_notice(i.l10n.save_to_library_title,i.l10n.save_to_library_text,i.l10n.save_to_library_url)}))},init_custom_shortcode(){o("#wpcode-custom-shortcode-lite").click((function(e){e.preventDefault(),i.show_pro_notice(i.l10n.shortcode_title,i.l10n.shortcode_text,i.l10n.shortcode_url)}))},init_datetime_lite(){o(".wpcode-input-datetime[readonly]").click((function(e){e.preventDefault(),i.show_pro_notice(i.l10n.datetime_title,i.l10n.datetime_text,i.l10n.datetime_url)}))},init_device_type(){o(".wpcode-device-type-picker-lite label").click((function(e){e.preventDefault(),i.show_pro_notice(i.l10n.device_title,i.l10n.device_text,i.l10n.device_url)}))},init_load_as_file(){o("#wpcode_snippet_as_file_option #wpcode_snippet_as_file").on("change",(function(e){e.preventDefault(),o(this).prop("checked",!1),i.show_pro_notice(i.l10n.laf_title,i.l10n.laf_text,i.l10n.laf_url)}))},show_pro_notice(e,t,n,o){WPCodeAdminNotices.show_pro_notice(e,t,n,o)},init_conditional_logic_notice(){o("#wpcode-conditions-holder").on("change",".wpcode-cl-rule-type",(function(e){const t=o(this).find(":selected");if(t.data("upgrade-title")){e.stopPropagation(),i.show_pro_notice(t.data("upgrade-title"),t.data("upgrade-text"),t.data("upgrade-link"),t.data("upgrade-button"));const n=o(this).find("option").first();o(this).val(n.attr("value")).trigger("change")}}))},init_shortcode_attributes(){const e=o("#wpcode-shortcode-attributes-list ul"),t=o("#wpcode-shortcode-attribute-name"),n=o("#wpcode_shortcode_attribute_list_item_template").html();o("#wpcode_add_attribute").on("click",(function(a){a.preventDefault();const r=i.sanitize_key(t.val());if(""===r)return;const s=o(n);s.find(".wpcode-shortcode-attribute-name").text(r),s.find(".wpcode-shortcode-attribute-item-input").val(r),e.append(s),t.val(""),i.update_smart_tags_attributes()})),e.on("click",".wpcode-shortcode-attribute-remove",(function(e){e.preventDefault(),o(this).closest("li").remove(),i.update_smart_tags_attributes()}))},sanitize_key:e=>e.replace(/[^a-z0-9_]/gi,"").toLowerCase(),update_smart_tags_attributes(){const e=o(".wpcode-shortcode-attribute-item-input"),t=o(".wpcode-smart-tags-dropdown ul");t.find(".wpcode-attribute-smart-tag").remove(),e.length>0&&t.append('<li class="wpcode-attribute-smart-tag wpcode-smart-tag-category-label">'+i.l10n.shortcode_attributes+"</li>"),e.each((function(){const e=o(this).val();t.append('<li class="wpcode-attribute-smart-tag" ><buton class="wpcode-insert-smart-tag" data-tag="{attr_'+e+'}"><code>{attr_'+e+"}</code> - "+e+"</buton></li>")}))},maybe_highlight_error_line(){if(i.l10n.error_line<=0)return;const t=wpcode_editor[i.editor_id].codemirror;var n;t.doc.setGutterMarker(i.l10n.error_line-1,"CodeMirror-lint-markers",((n=e.createElement("div")).innerHTML='<div class="wpcode-line-error-icon"></div>',n.setAttribute("title",i.l10n.error_line_message),n)),t.doc.addLineClass(i.l10n.error_line-1,"background","wpcode-line-error-code")},init_edit_lock(){o(e).on("heartbeat-send.refresh-lock",(function(e,t){t.wpcode_lock=i.l10n.snippet_id})),i.l10n.is_locked&&i.show_locked_message(i.l10n.locked_by)},show_locked_message(e){t().fire({title:i.l10n.edited+e,customClass:{confirmButton:"wpcode-button",icon:"wpcode-lock"},confirmButtonText:i.l10n.ok,iconHtml:i.icon_lock})},editor_resizer(){let t=e.querySelector(".wpcode-resize-handle"),o=e.querySelector(".wpcode-code-textarea"),a=0;const r=n.WPCodeAdminCodeEditor.get_editor(i.editor_id);var s,l;function c(e){a=Math.max(200,l+e.y-s)+"px",r.setSize(null,a)}function d(t){e.body.removeEventListener("mousemove",c),n.removeEventListener("mouseup",d),o.classList.remove("wpcode-resizing"),i.save_editor_height(a)}t.addEventListener("mousedown",(function(t){var i;t.x,s=t.y,i=o,l=parseInt(n.getComputedStyle(i).height.replace(/px$/,"")),o.classList.add("wpcode-resizing"),e.body.addEventListener("mousemove",c),n.addEventListener("mouseup",d)}))},save_editor_height(e){i.saving_height&&i.saving_height.abort(),i.saving_height=o.post(ajaxurl,{action:"wpcode_save_editor_height",height:e,_wpnonce:i.l10n.nonce})},init_ai_button(){o(".wpcode-button-ai-not-available").on("click",(function(e){e.preventDefault(),e.stopPropagation(),WPCodeAdminNotices.show_pro_notice(i.l10n.ai_improve_title,i.l10n.ai_text,i.l10n.ai_improve_url,i.l10n.ai_button)}))},keyboard_shortcuts(){var t={};o(e).on("keydown",(function(e){var n=/Mac/.test(navigator.userAgent),o=!n&&e.ctrlKey,i=n&&e.metaKey;if(o||i){var a=String.fromCharCode(e.which).toUpperCase();t.hasOwnProperty(a)&&(e.preventDefault(),t[a](e))}}));const a=n.WPCodeAdminCodeEditor.get_editor(i.editor_id);var r;r=function(e){a.hasFocus()&&o("#wpcode-snippet-manager-form").submit()},t["S".toUpperCase()]=r}};return i}(document,window,jQuery);jQuery((function(){o.init()})),n(560);var i=["onChange","onClose","onDayCreate","onDestroy","onKeyDown","onMonthChange","onOpen","onParseConfig","onReady","onValueUpdate","onYearChange","onPreCalendarPosition"],a={_disable:[],allowInput:!1,allowInvalidPreload:!1,altFormat:"F j, Y",altInput:!1,altInputClass:"form-control input",animate:"object"==typeof window&&-1===window.navigator.userAgent.indexOf("MSIE"),ariaDateFormat:"F j, Y",autoFillDefaultTime:!0,clickOpens:!0,closeOnSelect:!0,conjunction:", ",dateFormat:"Y-m-d",defaultHour:12,defaultMinute:0,defaultSeconds:0,disable:[],disableMobile:!1,enableSeconds:!1,enableTime:!1,errorHandler:function(e){return"undefined"!=typeof console&&console.warn(e)},getWeek:function(e){var t=new Date(e.getTime());t.setHours(0,0,0,0),t.setDate(t.getDate()+3-(t.getDay()+6)%7);var n=new Date(t.getFullYear(),0,4);return 1+Math.round(((t.getTime()-n.getTime())/864e5-3+(n.getDay()+6)%7)/7)},hourIncrement:1,ignoredFocusElements:[],inline:!1,locale:"default",minuteIncrement:5,mode:"single",monthSelectorType:"dropdown",nextArrow:"<svg version='1.1' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink' viewBox='0 0 17 17'><g></g><path d='M13.207 8.472l-7.854 7.854-0.707-0.707 7.146-7.146-7.146-7.148 0.707-0.707 7.854 7.854z' /></svg>",noCalendar:!1,now:new Date,onChange:[],onClose:[],onDayCreate:[],onDestroy:[],onKeyDown:[],onMonthChange:[],onOpen:[],onParseConfig:[],onReady:[],onValueUpdate:[],onYearChange:[],onPreCalendarPosition:[],plugins:[],position:"auto",positionElement:void 0,prevArrow:"<svg version='1.1' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink' viewBox='0 0 17 17'><g></g><path d='M5.207 8.471l7.146 7.147-0.707 0.707-7.853-7.854 7.854-7.853 0.707 0.707-7.147 7.146z' /></svg>",shorthandCurrentMonth:!1,showMonths:1,static:!1,time_24hr:!1,weekNumbers:!1,wrap:!1},r={weekdays:{shorthand:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],longhand:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"]},months:{shorthand:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],longhand:["January","February","March","April","May","June","July","August","September","October","November","December"]},daysInMonth:[31,28,31,30,31,30,31,31,30,31,30,31],firstDayOfWeek:0,ordinal:function(e){var t=e%100;if(t>3&&t<21)return"th";switch(t%10){case 1:return"st";case 2:return"nd";case 3:return"rd";default:return"th"}},rangeSeparator:" to ",weekAbbreviation:"Wk",scrollTitle:"Scroll to increment",toggleTitle:"Click to toggle",amPM:["AM","PM"],yearAriaLabel:"Year",monthAriaLabel:"Month",hourAriaLabel:"Hour",minuteAriaLabel:"Minute",time_24hr:!1},s=r,l=function(e,t){return void 0===t&&(t=2),("000"+e).slice(-1*t)},c=function(e){return!0===e?1:0};function d(e,t){var n;return function(){var o=this,i=arguments;clearTimeout(n),n=setTimeout((function(){return e.apply(o,i)}),t)}}var u=function(e){return e instanceof Array?e:[e]};function p(e,t,n){if(!0===n)return e.classList.add(t);e.classList.remove(t)}function f(e,t,n){var o=window.document.createElement(e);return t=t||"",n=n||"",o.className=t,void 0!==n&&(o.textContent=n),o}function h(e){for(;e.firstChild;)e.removeChild(e.firstChild)}function m(e,t){return t(e)?e:e.parentNode?m(e.parentNode,t):void 0}function g(e,t){var n=f("div","numInputWrapper"),o=f("input","numInput "+e),i=f("span","arrowUp"),a=f("span","arrowDown");if(-1===navigator.userAgent.indexOf("MSIE 9.0")?o.type="number":(o.type="text",o.pattern="\\d*"),void 0!==t)for(var r in t)o.setAttribute(r,t[r]);return n.appendChild(o),n.appendChild(i),n.appendChild(a),n}function w(e){try{return"function"==typeof e.composedPath?e.composedPath()[0]:e.target}catch(t){return e.target}}var v=function(){},_=function(e,t,n){return n.months[t?"shorthand":"longhand"][e]},y={D:v,F:function(e,t,n){e.setMonth(n.months.longhand.indexOf(t))},G:function(e,t){e.setHours((e.getHours()>=12?12:0)+parseFloat(t))},H:function(e,t){e.setHours(parseFloat(t))},J:function(e,t){e.setDate(parseFloat(t))},K:function(e,t,n){e.setHours(e.getHours()%12+12*c(new RegExp(n.amPM[1],"i").test(t)))},M:function(e,t,n){e.setMonth(n.months.shorthand.indexOf(t))},S:function(e,t){e.setSeconds(parseFloat(t))},U:function(e,t){return new Date(1e3*parseFloat(t))},W:function(e,t,n){var o=parseInt(t),i=new Date(e.getFullYear(),0,2+7*(o-1),0,0,0,0);return i.setDate(i.getDate()-i.getDay()+n.firstDayOfWeek),i},Y:function(e,t){e.setFullYear(parseFloat(t))},Z:function(e,t){return new Date(t)},d:function(e,t){e.setDate(parseFloat(t))},h:function(e,t){e.setHours((e.getHours()>=12?12:0)+parseFloat(t))},i:function(e,t){e.setMinutes(parseFloat(t))},j:function(e,t){e.setDate(parseFloat(t))},l:v,m:function(e,t){e.setMonth(parseFloat(t)-1)},n:function(e,t){e.setMonth(parseFloat(t)-1)},s:function(e,t){e.setSeconds(parseFloat(t))},u:function(e,t){return new Date(parseFloat(t))},w:v,y:function(e,t){e.setFullYear(2e3+parseFloat(t))}},b={D:"",F:"",G:"(\\d\\d|\\d)",H:"(\\d\\d|\\d)",J:"(\\d\\d|\\d)\\w+",K:"",M:"",S:"(\\d\\d|\\d)",U:"(.+)",W:"(\\d\\d|\\d)",Y:"(\\d{4})",Z:"(.+)",d:"(\\d\\d|\\d)",h:"(\\d\\d|\\d)",i:"(\\d\\d|\\d)",j:"(\\d\\d|\\d)",l:"",m:"(\\d\\d|\\d)",n:"(\\d\\d|\\d)",s:"(\\d\\d|\\d)",u:"(.+)",w:"(\\d\\d|\\d)",y:"(\\d{2})"},C={Z:function(e){return e.toISOString()},D:function(e,t,n){return t.weekdays.shorthand[C.w(e,t,n)]},F:function(e,t,n){return _(C.n(e,t,n)-1,!1,t)},G:function(e,t,n){return l(C.h(e,t,n))},H:function(e){return l(e.getHours())},J:function(e,t){return void 0!==t.ordinal?e.getDate()+t.ordinal(e.getDate()):e.getDate()},K:function(e,t){return t.amPM[c(e.getHours()>11)]},M:function(e,t){return _(e.getMonth(),!0,t)},S:function(e){return l(e.getSeconds())},U:function(e){return e.getTime()/1e3},W:function(e,t,n){return n.getWeek(e)},Y:function(e){return l(e.getFullYear(),4)},d:function(e){return l(e.getDate())},h:function(e){return e.getHours()%12?e.getHours()%12:12},i:function(e){return l(e.getMinutes())},j:function(e){return e.getDate()},l:function(e,t){return t.weekdays.longhand[e.getDay()]},m:function(e){return l(e.getMonth()+1)},n:function(e){return e.getMonth()+1},s:function(e){return e.getSeconds()},u:function(e){return e.getTime()},w:function(e){return e.getDay()},y:function(e){return String(e.getFullYear()).substring(2)}},x=function(e){var t=e.config,n=void 0===t?a:t,o=e.l10n,i=void 0===o?r:o,s=e.isMobile,l=void 0!==s&&s;return function(e,t,o){var a=o||i;return void 0===n.formatDate||l?t.split("").map((function(t,o,i){return C[t]&&"\\"!==i[o-1]?C[t](e,a,n):"\\"!==t?t:""})).join(""):n.formatDate(e,t,a)}},k=function(e){var t=e.config,n=void 0===t?a:t,o=e.l10n,i=void 0===o?r:o;return function(e,t,o,r){if(0===e||e){var s,l=r||i,c=e;if(e instanceof Date)s=new Date(e.getTime());else if("string"!=typeof e&&void 0!==e.toFixed)s=new Date(e);else if("string"==typeof e){var d=t||(n||a).dateFormat,u=String(e).trim();if("today"===u)s=new Date,o=!0;else if(n&&n.parseDate)s=n.parseDate(e,d);else if(/Z$/.test(u)||/GMT$/.test(u))s=new Date(e);else{for(var p=void 0,f=[],h=0,m=0,g="";h<d.length;h++){var w=d[h],v="\\"===w,_="\\"===d[h-1]||v;if(b[w]&&!_){g+=b[w];var C=new RegExp(g).exec(e);C&&(p=!0)&&f["Y"!==w?"push":"unshift"]({fn:y[w],val:C[++m]})}else v||(g+=".")}s=n&&n.noCalendar?new Date((new Date).setHours(0,0,0,0)):new Date((new Date).getFullYear(),0,1,0,0,0,0),f.forEach((function(e){var t=e.fn,n=e.val;return s=t(s,n,l)||s})),s=p?s:void 0}}if(s instanceof Date&&!isNaN(s.getTime()))return!0===o&&s.setHours(0,0,0,0),s;n.errorHandler(new Error("Invalid date provided: "+c))}}};function D(e,t,n){return void 0===n&&(n=!0),!1!==n?new Date(e.getTime()).setHours(0,0,0,0)-new Date(t.getTime()).setHours(0,0,0,0):e.getTime()-t.getTime()}var A=function(e,t,n){return 3600*e+60*t+n};function E(e){var t=e.defaultHour,n=e.defaultMinute,o=e.defaultSeconds;if(void 0!==e.minDate){var i=e.minDate.getHours(),a=e.minDate.getMinutes(),r=e.minDate.getSeconds();t<i&&(t=i),t===i&&n<a&&(n=a),t===i&&n===a&&o<r&&(o=e.minDate.getSeconds())}if(void 0!==e.maxDate){var s=e.maxDate.getHours(),l=e.maxDate.getMinutes();(t=Math.min(t,s))===s&&(n=Math.min(l,n)),t===s&&n===l&&(o=e.maxDate.getSeconds())}return{hours:t,minutes:n,seconds:o}}n(895);var M=function(){return M=Object.assign||function(e){for(var t,n=1,o=arguments.length;n<o;n++)for(var i in t=arguments[n])Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i]);return e},M.apply(this,arguments)},T=function(){for(var e=0,t=0,n=arguments.length;t<n;t++)e+=arguments[t].length;var o=Array(e),i=0;for(t=0;t<n;t++)for(var a=arguments[t],r=0,s=a.length;r<s;r++,i++)o[i]=a[r];return o};function $(e,t){var n={config:M(M({},a),O.defaultConfig),l10n:s};function o(){var e;return(null===(e=n.calendarContainer)||void 0===e?void 0:e.getRootNode()).activeElement||document.activeElement}function r(e){return e.bind(n)}function v(){var e=n.config;!1===e.weekNumbers&&1===e.showMonths||!0!==e.noCalendar&&window.requestAnimationFrame((function(){if(void 0!==n.calendarContainer&&(n.calendarContainer.style.visibility="hidden",n.calendarContainer.style.display="block"),void 0!==n.daysContainer){var t=(n.days.offsetWidth+1)*e.showMonths;n.daysContainer.style.width=t+"px",n.calendarContainer.style.width=t+(void 0!==n.weekWrapper?n.weekWrapper.offsetWidth:0)+"px",n.calendarContainer.style.removeProperty("visibility"),n.calendarContainer.style.removeProperty("display")}}))}function y(e){if(0===n.selectedDates.length){var t=void 0===n.config.minDate||D(new Date,n.config.minDate)>=0?new Date:new Date(n.config.minDate.getTime()),o=E(n.config);t.setHours(o.hours,o.minutes,o.seconds,t.getMilliseconds()),n.selectedDates=[t],n.latestSelectedDateObj=t}void 0!==e&&"blur"!==e.type&&function(e){e.preventDefault();var t="keydown"===e.type,o=w(e),i=o;void 0!==n.amPM&&o===n.amPM&&(n.amPM.textContent=n.l10n.amPM[c(n.amPM.textContent===n.l10n.amPM[0])]);var a=parseFloat(i.getAttribute("min")),r=parseFloat(i.getAttribute("max")),s=parseFloat(i.getAttribute("step")),d=parseInt(i.value,10),u=d+s*(e.delta||(t?38===e.which?1:-1:0));if(void 0!==i.value&&2===i.value.length){var p=i===n.hourElement,f=i===n.minuteElement;u<a?(u=r+u+c(!p)+(c(p)&&c(!n.amPM)),f&&H(void 0,-1,n.hourElement)):u>r&&(u=i===n.hourElement?u-r-c(!n.amPM):a,f&&H(void 0,1,n.hourElement)),n.amPM&&p&&(1===s?u+d===23:Math.abs(u-d)>s)&&(n.amPM.textContent=n.l10n.amPM[c(n.amPM.textContent===n.l10n.amPM[0])]),i.value=l(u)}}(e);var i=n._input.value;C(),Ce(),n._input.value!==i&&n._debouncedChange()}function C(){if(void 0!==n.hourElement&&void 0!==n.minuteElement){var e,t,o=(parseInt(n.hourElement.value.slice(-2),10)||0)%24,i=(parseInt(n.minuteElement.value,10)||0)%60,a=void 0!==n.secondElement?(parseInt(n.secondElement.value,10)||0)%60:0;void 0!==n.amPM&&(e=o,t=n.amPM.textContent,o=e%12+12*c(t===n.l10n.amPM[1]));var r=void 0!==n.config.minTime||n.config.minDate&&n.minDateHasTime&&n.latestSelectedDateObj&&0===D(n.latestSelectedDateObj,n.config.minDate,!0),s=void 0!==n.config.maxTime||n.config.maxDate&&n.maxDateHasTime&&n.latestSelectedDateObj&&0===D(n.latestSelectedDateObj,n.config.maxDate,!0);if(void 0!==n.config.maxTime&&void 0!==n.config.minTime&&n.config.minTime>n.config.maxTime){var l=A(n.config.minTime.getHours(),n.config.minTime.getMinutes(),n.config.minTime.getSeconds()),d=A(n.config.maxTime.getHours(),n.config.maxTime.getMinutes(),n.config.maxTime.getSeconds()),u=A(o,i,a);if(u>d&&u<l){var p=function(e){var t=Math.floor(e/3600),n=(e-3600*t)/60;return[t,n,e-3600*t-60*n]}(l);o=p[0],i=p[1],a=p[2]}}else{if(s){var f=void 0!==n.config.maxTime?n.config.maxTime:n.config.maxDate;(o=Math.min(o,f.getHours()))===f.getHours()&&(i=Math.min(i,f.getMinutes())),i===f.getMinutes()&&(a=Math.min(a,f.getSeconds()))}if(r){var h=void 0!==n.config.minTime?n.config.minTime:n.config.minDate;(o=Math.max(o,h.getHours()))===h.getHours()&&i<h.getMinutes()&&(i=h.getMinutes()),i===h.getMinutes()&&(a=Math.max(a,h.getSeconds()))}}S(o,i,a)}}function $(e){var t=e||n.latestSelectedDateObj;t&&t instanceof Date&&S(t.getHours(),t.getMinutes(),t.getSeconds())}function S(e,t,o){void 0!==n.latestSelectedDateObj&&n.latestSelectedDateObj.setHours(e%24,t,o||0,0),n.hourElement&&n.minuteElement&&!n.isMobile&&(n.hourElement.value=l(n.config.time_24hr?e:(12+e)%12+12*c(e%12==0)),n.minuteElement.value=l(t),void 0!==n.amPM&&(n.amPM.textContent=n.l10n.amPM[c(e>=12)]),void 0!==n.secondElement&&(n.secondElement.value=l(o)))}function P(e){var t=w(e),n=parseInt(t.value)+(e.delta||0);(n/1e3>1||"Enter"===e.key&&!/[^\d]/.test(n.toString()))&&X(n)}function j(e,t,o,i){return t instanceof Array?t.forEach((function(t){return j(e,t,o,i)})):e instanceof Array?e.forEach((function(e){return j(e,t,o,i)})):(e.addEventListener(t,o,i),void n._handlers.push({remove:function(){return e.removeEventListener(t,o,i)}}))}function I(){we("onChange")}function L(e,t){var o=void 0!==e?n.parseDate(e):n.latestSelectedDateObj||(n.config.minDate&&n.config.minDate>n.now?n.config.minDate:n.config.maxDate&&n.config.maxDate<n.now?n.config.maxDate:n.now),i=n.currentYear,a=n.currentMonth;try{void 0!==o&&(n.currentYear=o.getFullYear(),n.currentMonth=o.getMonth())}catch(e){e.message="Invalid date supplied: "+o,n.config.errorHandler(e)}t&&n.currentYear!==i&&(we("onYearChange"),R()),!t||n.currentYear===i&&n.currentMonth===a||we("onMonthChange"),n.redraw()}function B(e){var t=w(e);~t.className.indexOf("arrow")&&H(e,t.classList.contains("arrowUp")?1:-1)}function H(e,t,n){var o=e&&w(e),i=n||o&&o.parentNode&&o.parentNode.firstChild,a=ve("increment");a.delta=t,i&&i.dispatchEvent(a)}function N(e,t,o,i){var a=ee(t,!0),r=f("span",e,t.getDate().toString());return r.dateObj=t,r.$i=i,r.setAttribute("aria-label",n.formatDate(t,n.config.ariaDateFormat)),-1===e.indexOf("hidden")&&0===D(t,n.now)&&(n.todayDateElem=r,r.classList.add("today"),r.setAttribute("aria-current","date")),a?(r.tabIndex=-1,_e(t)&&(r.classList.add("selected"),n.selectedDateElem=r,"range"===n.config.mode&&(p(r,"startRange",n.selectedDates[0]&&0===D(t,n.selectedDates[0],!0)),p(r,"endRange",n.selectedDates[1]&&0===D(t,n.selectedDates[1],!0)),"nextMonthDay"===e&&r.classList.add("inRange")))):r.classList.add("flatpickr-disabled"),"range"===n.config.mode&&function(e){return!("range"!==n.config.mode||n.selectedDates.length<2)&&D(e,n.selectedDates[0])>=0&&D(e,n.selectedDates[1])<=0}(t)&&!_e(t)&&r.classList.add("inRange"),n.weekNumbers&&1===n.config.showMonths&&"prevMonthDay"!==e&&i%7==6&&n.weekNumbers.insertAdjacentHTML("beforeend","<span class='flatpickr-day'>"+n.config.getWeek(t)+"</span>"),we("onDayCreate",r),r}function q(e){e.focus(),"range"===n.config.mode&&ie(e)}function F(e){for(var t=e>0?0:n.config.showMonths-1,o=e>0?n.config.showMonths:-1,i=t;i!=o;i+=e)for(var a=n.daysContainer.children[i],r=e>0?0:a.children.length-1,s=e>0?a.children.length:-1,l=r;l!=s;l+=e){var c=a.children[l];if(-1===c.className.indexOf("hidden")&&ee(c.dateObj))return c}}function W(e,t){var i=o(),a=te(i||document.body),r=void 0!==e?e:a?i:void 0!==n.selectedDateElem&&te(n.selectedDateElem)?n.selectedDateElem:void 0!==n.todayDateElem&&te(n.todayDateElem)?n.todayDateElem:F(t>0?1:-1);void 0===r?n._input.focus():a?function(e,t){for(var o=-1===e.className.indexOf("Month")?e.dateObj.getMonth():n.currentMonth,i=t>0?n.config.showMonths:-1,a=t>0?1:-1,r=o-n.currentMonth;r!=i;r+=a)for(var s=n.daysContainer.children[r],l=o-n.currentMonth===r?e.$i+t:t<0?s.children.length-1:0,c=s.children.length,d=l;d>=0&&d<c&&d!=(t>0?c:-1);d+=a){var u=s.children[d];if(-1===u.className.indexOf("hidden")&&ee(u.dateObj)&&Math.abs(e.$i-d)>=Math.abs(t))return q(u)}n.changeMonth(a),W(F(a),0)}(r,t):q(r)}function z(e,t){for(var o=(new Date(e,t,1).getDay()-n.l10n.firstDayOfWeek+7)%7,i=n.utils.getDaysInMonth((t-1+12)%12,e),a=n.utils.getDaysInMonth(t,e),r=window.document.createDocumentFragment(),s=n.config.showMonths>1,l=s?"prevMonthDay hidden":"prevMonthDay",c=s?"nextMonthDay hidden":"nextMonthDay",d=i+1-o,u=0;d<=i;d++,u++)r.appendChild(N("flatpickr-day "+l,new Date(e,t-1,d),0,u));for(d=1;d<=a;d++,u++)r.appendChild(N("flatpickr-day",new Date(e,t,d),0,u));for(var p=a+1;p<=42-o&&(1===n.config.showMonths||u%7!=0);p++,u++)r.appendChild(N("flatpickr-day "+c,new Date(e,t+1,p%a),0,u));var h=f("div","dayContainer");return h.appendChild(r),h}function Y(){if(void 0!==n.daysContainer){h(n.daysContainer),n.weekNumbers&&h(n.weekNumbers);for(var e=document.createDocumentFragment(),t=0;t<n.config.showMonths;t++){var o=new Date(n.currentYear,n.currentMonth,1);o.setMonth(n.currentMonth+t),e.appendChild(z(o.getFullYear(),o.getMonth()))}n.daysContainer.appendChild(e),n.days=n.daysContainer.firstChild,"range"===n.config.mode&&1===n.selectedDates.length&&ie()}}function R(){if(!(n.config.showMonths>1||"dropdown"!==n.config.monthSelectorType)){var e=function(e){return!(void 0!==n.config.minDate&&n.currentYear===n.config.minDate.getFullYear()&&e<n.config.minDate.getMonth()||void 0!==n.config.maxDate&&n.currentYear===n.config.maxDate.getFullYear()&&e>n.config.maxDate.getMonth())};n.monthsDropdownContainer.tabIndex=-1,n.monthsDropdownContainer.innerHTML="";for(var t=0;t<12;t++)if(e(t)){var o=f("option","flatpickr-monthDropdown-month");o.value=new Date(n.currentYear,t).getMonth().toString(),o.textContent=_(t,n.config.shorthandCurrentMonth,n.l10n),o.tabIndex=-1,n.currentMonth===t&&(o.selected=!0),n.monthsDropdownContainer.appendChild(o)}}}function U(){var e,t=f("div","flatpickr-month"),o=window.document.createDocumentFragment();n.config.showMonths>1||"static"===n.config.monthSelectorType?e=f("span","cur-month"):(n.monthsDropdownContainer=f("select","flatpickr-monthDropdown-months"),n.monthsDropdownContainer.setAttribute("aria-label",n.l10n.monthAriaLabel),j(n.monthsDropdownContainer,"change",(function(e){var t=w(e),o=parseInt(t.value,10);n.changeMonth(o-n.currentMonth),we("onMonthChange")})),R(),e=n.monthsDropdownContainer);var i=g("cur-year",{tabindex:"-1"}),a=i.getElementsByTagName("input")[0];a.setAttribute("aria-label",n.l10n.yearAriaLabel),n.config.minDate&&a.setAttribute("min",n.config.minDate.getFullYear().toString()),n.config.maxDate&&(a.setAttribute("max",n.config.maxDate.getFullYear().toString()),a.disabled=!!n.config.minDate&&n.config.minDate.getFullYear()===n.config.maxDate.getFullYear());var r=f("div","flatpickr-current-month");return r.appendChild(e),r.appendChild(i),o.appendChild(r),t.appendChild(o),{container:t,yearElement:a,monthElement:e}}function V(){h(n.monthNav),n.monthNav.appendChild(n.prevMonthNav),n.config.showMonths&&(n.yearElements=[],n.monthElements=[]);for(var e=n.config.showMonths;e--;){var t=U();n.yearElements.push(t.yearElement),n.monthElements.push(t.monthElement),n.monthNav.appendChild(t.container)}n.monthNav.appendChild(n.nextMonthNav)}function K(){n.weekdayContainer?h(n.weekdayContainer):n.weekdayContainer=f("div","flatpickr-weekdays");for(var e=n.config.showMonths;e--;){var t=f("div","flatpickr-weekdaycontainer");n.weekdayContainer.appendChild(t)}return Z(),n.weekdayContainer}function Z(){if(n.weekdayContainer){var e=n.l10n.firstDayOfWeek,t=T(n.l10n.weekdays.shorthand);e>0&&e<t.length&&(t=T(t.splice(e,t.length),t.splice(0,e)));for(var o=n.config.showMonths;o--;)n.weekdayContainer.children[o].innerHTML="\n      <span class='flatpickr-weekday'>\n        "+t.join("</span><span class='flatpickr-weekday'>")+"\n      </span>\n      "}}function Q(e,t){void 0===t&&(t=!0);var o=t?e:e-n.currentMonth;o<0&&!0===n._hidePrevMonthArrow||o>0&&!0===n._hideNextMonthArrow||(n.currentMonth+=o,(n.currentMonth<0||n.currentMonth>11)&&(n.currentYear+=n.currentMonth>11?1:-1,n.currentMonth=(n.currentMonth+12)%12,we("onYearChange"),R()),Y(),we("onMonthChange"),ye())}function J(e){return n.calendarContainer.contains(e)}function G(e){if(n.isOpen&&!n.config.inline){var t=w(e),o=J(t),i=!(t===n.input||t===n.altInput||n.element.contains(t)||e.path&&e.path.indexOf&&(~e.path.indexOf(n.input)||~e.path.indexOf(n.altInput))||o||J(e.relatedTarget)),a=!n.config.ignoredFocusElements.some((function(e){return e.contains(t)}));i&&a&&(n.config.allowInput&&n.setDate(n._input.value,!1,n.config.altInput?n.config.altFormat:n.config.dateFormat),void 0!==n.timeContainer&&void 0!==n.minuteElement&&void 0!==n.hourElement&&""!==n.input.value&&void 0!==n.input.value&&y(),n.close(),n.config&&"range"===n.config.mode&&1===n.selectedDates.length&&n.clear(!1))}}function X(e){if(!(!e||n.config.minDate&&e<n.config.minDate.getFullYear()||n.config.maxDate&&e>n.config.maxDate.getFullYear())){var t=e,o=n.currentYear!==t;n.currentYear=t||n.currentYear,n.config.maxDate&&n.currentYear===n.config.maxDate.getFullYear()?n.currentMonth=Math.min(n.config.maxDate.getMonth(),n.currentMonth):n.config.minDate&&n.currentYear===n.config.minDate.getFullYear()&&(n.currentMonth=Math.max(n.config.minDate.getMonth(),n.currentMonth)),o&&(n.redraw(),we("onYearChange"),R())}}function ee(e,t){var o;void 0===t&&(t=!0);var i=n.parseDate(e,void 0,t);if(n.config.minDate&&i&&D(i,n.config.minDate,void 0!==t?t:!n.minDateHasTime)<0||n.config.maxDate&&i&&D(i,n.config.maxDate,void 0!==t?t:!n.maxDateHasTime)>0)return!1;if(!n.config.enable&&0===n.config.disable.length)return!0;if(void 0===i)return!1;for(var a=!!n.config.enable,r=null!==(o=n.config.enable)&&void 0!==o?o:n.config.disable,s=0,l=void 0;s<r.length;s++){if("function"==typeof(l=r[s])&&l(i))return a;if(l instanceof Date&&void 0!==i&&l.getTime()===i.getTime())return a;if("string"==typeof l){var c=n.parseDate(l,void 0,!0);return c&&c.getTime()===i.getTime()?a:!a}if("object"==typeof l&&void 0!==i&&l.from&&l.to&&i.getTime()>=l.from.getTime()&&i.getTime()<=l.to.getTime())return a}return!a}function te(e){return void 0!==n.daysContainer&&-1===e.className.indexOf("hidden")&&-1===e.className.indexOf("flatpickr-disabled")&&n.daysContainer.contains(e)}function ne(e){var t=e.target===n._input,o=n._input.value.trimEnd()!==be();!t||!o||e.relatedTarget&&J(e.relatedTarget)||n.setDate(n._input.value,!0,e.target===n.altInput?n.config.altFormat:n.config.dateFormat)}function oe(t){var i=w(t),a=n.config.wrap?e.contains(i):i===n._input,r=n.config.allowInput,s=n.isOpen&&(!r||!a),l=n.config.inline&&a&&!r;if(13===t.keyCode&&a){if(r)return n.setDate(n._input.value,!0,i===n.altInput?n.config.altFormat:n.config.dateFormat),n.close(),i.blur();n.open()}else if(J(i)||s||l){var c=!!n.timeContainer&&n.timeContainer.contains(i);switch(t.keyCode){case 13:c?(t.preventDefault(),y(),ue()):pe(t);break;case 27:t.preventDefault(),ue();break;case 8:case 46:a&&!n.config.allowInput&&(t.preventDefault(),n.clear());break;case 37:case 39:if(c||a)n.hourElement&&n.hourElement.focus();else{t.preventDefault();var d=o();if(void 0!==n.daysContainer&&(!1===r||d&&te(d))){var u=39===t.keyCode?1:-1;t.ctrlKey?(t.stopPropagation(),Q(u),W(F(1),0)):W(void 0,u)}}break;case 38:case 40:t.preventDefault();var p=40===t.keyCode?1:-1;n.daysContainer&&void 0!==i.$i||i===n.input||i===n.altInput?t.ctrlKey?(t.stopPropagation(),X(n.currentYear-p),W(F(1),0)):c||W(void 0,7*p):i===n.currentYearElement?X(n.currentYear-p):n.config.enableTime&&(!c&&n.hourElement&&n.hourElement.focus(),y(t),n._debouncedChange());break;case 9:if(c){var f=[n.hourElement,n.minuteElement,n.secondElement,n.amPM].concat(n.pluginElements).filter((function(e){return e})),h=f.indexOf(i);if(-1!==h){var m=f[h+(t.shiftKey?-1:1)];t.preventDefault(),(m||n._input).focus()}}else!n.config.noCalendar&&n.daysContainer&&n.daysContainer.contains(i)&&t.shiftKey&&(t.preventDefault(),n._input.focus())}}if(void 0!==n.amPM&&i===n.amPM)switch(t.key){case n.l10n.amPM[0].charAt(0):case n.l10n.amPM[0].charAt(0).toLowerCase():n.amPM.textContent=n.l10n.amPM[0],C(),Ce();break;case n.l10n.amPM[1].charAt(0):case n.l10n.amPM[1].charAt(0).toLowerCase():n.amPM.textContent=n.l10n.amPM[1],C(),Ce()}(a||J(i))&&we("onKeyDown",t)}function ie(e,t){if(void 0===t&&(t="flatpickr-day"),1===n.selectedDates.length&&(!e||e.classList.contains(t)&&!e.classList.contains("flatpickr-disabled"))){for(var o=e?e.dateObj.getTime():n.days.firstElementChild.dateObj.getTime(),i=n.parseDate(n.selectedDates[0],void 0,!0).getTime(),a=Math.min(o,n.selectedDates[0].getTime()),r=Math.max(o,n.selectedDates[0].getTime()),s=!1,l=0,c=0,d=a;d<r;d+=864e5)ee(new Date(d),!0)||(s=s||d>a&&d<r,d<i&&(!l||d>l)?l=d:d>i&&(!c||d<c)&&(c=d));Array.from(n.rContainer.querySelectorAll("*:nth-child(-n+"+n.config.showMonths+") > ."+t)).forEach((function(t){var a,r,d,u=t.dateObj.getTime(),p=l>0&&u<l||c>0&&u>c;if(p)return t.classList.add("notAllowed"),void["inRange","startRange","endRange"].forEach((function(e){t.classList.remove(e)}));s&&!p||(["startRange","inRange","endRange","notAllowed"].forEach((function(e){t.classList.remove(e)})),void 0!==e&&(e.classList.add(o<=n.selectedDates[0].getTime()?"startRange":"endRange"),i<o&&u===i?t.classList.add("startRange"):i>o&&u===i&&t.classList.add("endRange"),u>=l&&(0===c||u<=c)&&(r=i,d=o,(a=u)>Math.min(r,d)&&a<Math.max(r,d))&&t.classList.add("inRange")))}))}}function ae(){!n.isOpen||n.config.static||n.config.inline||ce()}function re(e){return function(t){var o=n.config["_"+e+"Date"]=n.parseDate(t,n.config.dateFormat),i=n.config["_"+("min"===e?"max":"min")+"Date"];void 0!==o&&(n["min"===e?"minDateHasTime":"maxDateHasTime"]=o.getHours()>0||o.getMinutes()>0||o.getSeconds()>0),n.selectedDates&&(n.selectedDates=n.selectedDates.filter((function(e){return ee(e)})),n.selectedDates.length||"min"!==e||$(o),Ce()),n.daysContainer&&(de(),void 0!==o?n.currentYearElement[e]=o.getFullYear().toString():n.currentYearElement.removeAttribute(e),n.currentYearElement.disabled=!!i&&void 0!==o&&i.getFullYear()===o.getFullYear())}}function se(){return n.config.wrap?e.querySelector("[data-input]"):e}function le(){"object"!=typeof n.config.locale&&void 0===O.l10ns[n.config.locale]&&n.config.errorHandler(new Error("flatpickr: invalid locale "+n.config.locale)),n.l10n=M(M({},O.l10ns.default),"object"==typeof n.config.locale?n.config.locale:"default"!==n.config.locale?O.l10ns[n.config.locale]:void 0),b.D="("+n.l10n.weekdays.shorthand.join("|")+")",b.l="("+n.l10n.weekdays.longhand.join("|")+")",b.M="("+n.l10n.months.shorthand.join("|")+")",b.F="("+n.l10n.months.longhand.join("|")+")",b.K="("+n.l10n.amPM[0]+"|"+n.l10n.amPM[1]+"|"+n.l10n.amPM[0].toLowerCase()+"|"+n.l10n.amPM[1].toLowerCase()+")",void 0===M(M({},t),JSON.parse(JSON.stringify(e.dataset||{}))).time_24hr&&void 0===O.defaultConfig.time_24hr&&(n.config.time_24hr=n.l10n.time_24hr),n.formatDate=x(n),n.parseDate=k({config:n.config,l10n:n.l10n})}function ce(e){if("function"!=typeof n.config.position){if(void 0!==n.calendarContainer){we("onPreCalendarPosition");var t=e||n._positionElement,o=Array.prototype.reduce.call(n.calendarContainer.children,(function(e,t){return e+t.offsetHeight}),0),i=n.calendarContainer.offsetWidth,a=n.config.position.split(" "),r=a[0],s=a.length>1?a[1]:null,l=t.getBoundingClientRect(),c=window.innerHeight-l.bottom,d="above"===r||"below"!==r&&c<o&&l.top>o,u=window.pageYOffset+l.top+(d?-o-2:t.offsetHeight+2);if(p(n.calendarContainer,"arrowTop",!d),p(n.calendarContainer,"arrowBottom",d),!n.config.inline){var f=window.pageXOffset+l.left,h=!1,m=!1;"center"===s?(f-=(i-l.width)/2,h=!0):"right"===s&&(f-=i-l.width,m=!0),p(n.calendarContainer,"arrowLeft",!h&&!m),p(n.calendarContainer,"arrowCenter",h),p(n.calendarContainer,"arrowRight",m);var g=window.document.body.offsetWidth-(window.pageXOffset+l.right),w=f+i>window.document.body.offsetWidth,v=g+i>window.document.body.offsetWidth;if(p(n.calendarContainer,"rightMost",w),!n.config.static)if(n.calendarContainer.style.top=u+"px",w)if(v){var _=function(){for(var e=null,t=0;t<document.styleSheets.length;t++){var n=document.styleSheets[t];if(n.cssRules){try{n.cssRules}catch(e){continue}e=n;break}}return null!=e?e:(o=document.createElement("style"),document.head.appendChild(o),o.sheet);var o}();if(void 0===_)return;var y=window.document.body.offsetWidth,b=Math.max(0,y/2-i/2),C=_.cssRules.length,x="{left:"+l.left+"px;right:auto;}";p(n.calendarContainer,"rightMost",!1),p(n.calendarContainer,"centerMost",!0),_.insertRule(".flatpickr-calendar.centerMost:before,.flatpickr-calendar.centerMost:after"+x,C),n.calendarContainer.style.left=b+"px",n.calendarContainer.style.right="auto"}else n.calendarContainer.style.left="auto",n.calendarContainer.style.right=g+"px";else n.calendarContainer.style.left=f+"px",n.calendarContainer.style.right="auto"}}}else n.config.position(n,e)}function de(){n.config.noCalendar||n.isMobile||(R(),ye(),Y())}function ue(){n._input.focus(),-1!==window.navigator.userAgent.indexOf("MSIE")||void 0!==navigator.msMaxTouchPoints?setTimeout(n.close,0):n.close()}function pe(e){e.preventDefault(),e.stopPropagation();var t=m(w(e),(function(e){return e.classList&&e.classList.contains("flatpickr-day")&&!e.classList.contains("flatpickr-disabled")&&!e.classList.contains("notAllowed")}));if(void 0!==t){var o=t,i=n.latestSelectedDateObj=new Date(o.dateObj.getTime()),a=(i.getMonth()<n.currentMonth||i.getMonth()>n.currentMonth+n.config.showMonths-1)&&"range"!==n.config.mode;if(n.selectedDateElem=o,"single"===n.config.mode)n.selectedDates=[i];else if("multiple"===n.config.mode){var r=_e(i);r?n.selectedDates.splice(parseInt(r),1):n.selectedDates.push(i)}else"range"===n.config.mode&&(2===n.selectedDates.length&&n.clear(!1,!1),n.latestSelectedDateObj=i,n.selectedDates.push(i),0!==D(i,n.selectedDates[0],!0)&&n.selectedDates.sort((function(e,t){return e.getTime()-t.getTime()})));if(C(),a){var s=n.currentYear!==i.getFullYear();n.currentYear=i.getFullYear(),n.currentMonth=i.getMonth(),s&&(we("onYearChange"),R()),we("onMonthChange")}if(ye(),Y(),Ce(),a||"range"===n.config.mode||1!==n.config.showMonths?void 0!==n.selectedDateElem&&void 0===n.hourElement&&n.selectedDateElem&&n.selectedDateElem.focus():q(o),void 0!==n.hourElement&&void 0!==n.hourElement&&n.hourElement.focus(),n.config.closeOnSelect){var l="single"===n.config.mode&&!n.config.enableTime,c="range"===n.config.mode&&2===n.selectedDates.length&&!n.config.enableTime;(l||c)&&ue()}I()}}n.parseDate=k({config:n.config,l10n:n.l10n}),n._handlers=[],n.pluginElements=[],n.loadedPlugins=[],n._bind=j,n._setHoursFromDate=$,n._positionCalendar=ce,n.changeMonth=Q,n.changeYear=X,n.clear=function(e,t){if(void 0===e&&(e=!0),void 0===t&&(t=!0),n.input.value="",void 0!==n.altInput&&(n.altInput.value=""),void 0!==n.mobileInput&&(n.mobileInput.value=""),n.selectedDates=[],n.latestSelectedDateObj=void 0,!0===t&&(n.currentYear=n._initialDate.getFullYear(),n.currentMonth=n._initialDate.getMonth()),!0===n.config.enableTime){var o=E(n.config);S(o.hours,o.minutes,o.seconds)}n.redraw(),e&&we("onChange")},n.close=function(){n.isOpen=!1,n.isMobile||(void 0!==n.calendarContainer&&n.calendarContainer.classList.remove("open"),void 0!==n._input&&n._input.classList.remove("active")),we("onClose")},n.onMouseOver=ie,n._createElement=f,n.createDay=N,n.destroy=function(){void 0!==n.config&&we("onDestroy");for(var e=n._handlers.length;e--;)n._handlers[e].remove();if(n._handlers=[],n.mobileInput)n.mobileInput.parentNode&&n.mobileInput.parentNode.removeChild(n.mobileInput),n.mobileInput=void 0;else if(n.calendarContainer&&n.calendarContainer.parentNode)if(n.config.static&&n.calendarContainer.parentNode){var t=n.calendarContainer.parentNode;if(t.lastChild&&t.removeChild(t.lastChild),t.parentNode){for(;t.firstChild;)t.parentNode.insertBefore(t.firstChild,t);t.parentNode.removeChild(t)}}else n.calendarContainer.parentNode.removeChild(n.calendarContainer);n.altInput&&(n.input.type="text",n.altInput.parentNode&&n.altInput.parentNode.removeChild(n.altInput),delete n.altInput),n.input&&(n.input.type=n.input._type,n.input.classList.remove("flatpickr-input"),n.input.removeAttribute("readonly")),["_showTimeInput","latestSelectedDateObj","_hideNextMonthArrow","_hidePrevMonthArrow","__hideNextMonthArrow","__hidePrevMonthArrow","isMobile","isOpen","selectedDateElem","minDateHasTime","maxDateHasTime","days","daysContainer","_input","_positionElement","innerContainer","rContainer","monthNav","todayDateElem","calendarContainer","weekdayContainer","prevMonthNav","nextMonthNav","monthsDropdownContainer","currentMonthElement","currentYearElement","navigationCurrentMonth","selectedDateElem","config"].forEach((function(e){try{delete n[e]}catch(e){}}))},n.isEnabled=ee,n.jumpToDate=L,n.updateValue=Ce,n.open=function(e,t){if(void 0===t&&(t=n._positionElement),!0===n.isMobile){if(e){e.preventDefault();var o=w(e);o&&o.blur()}return void 0!==n.mobileInput&&(n.mobileInput.focus(),n.mobileInput.click()),void we("onOpen")}if(!n._input.disabled&&!n.config.inline){var i=n.isOpen;n.isOpen=!0,i||(n.calendarContainer.classList.add("open"),n._input.classList.add("active"),we("onOpen"),ce(t)),!0===n.config.enableTime&&!0===n.config.noCalendar&&(!1!==n.config.allowInput||void 0!==e&&n.timeContainer.contains(e.relatedTarget)||setTimeout((function(){return n.hourElement.select()}),50))}},n.redraw=de,n.set=function(e,t){if(null!==e&&"object"==typeof e)for(var o in Object.assign(n.config,e),e)void 0!==fe[o]&&fe[o].forEach((function(e){return e()}));else n.config[e]=t,void 0!==fe[e]?fe[e].forEach((function(e){return e()})):i.indexOf(e)>-1&&(n.config[e]=u(t));n.redraw(),Ce(!0)},n.setDate=function(e,t,o){if(void 0===t&&(t=!1),void 0===o&&(o=n.config.dateFormat),0!==e&&!e||e instanceof Array&&0===e.length)return n.clear(t);he(e,o),n.latestSelectedDateObj=n.selectedDates[n.selectedDates.length-1],n.redraw(),L(void 0,t),$(),0===n.selectedDates.length&&n.clear(!1),Ce(t),t&&we("onChange")},n.toggle=function(e){if(!0===n.isOpen)return n.close();n.open(e)};var fe={locale:[le,Z],showMonths:[V,v,K],minDate:[L],maxDate:[L],positionElement:[ge],clickOpens:[function(){!0===n.config.clickOpens?(j(n._input,"focus",n.open),j(n._input,"click",n.open)):(n._input.removeEventListener("focus",n.open),n._input.removeEventListener("click",n.open))}]};function he(e,t){var o=[];if(e instanceof Array)o=e.map((function(e){return n.parseDate(e,t)}));else if(e instanceof Date||"number"==typeof e)o=[n.parseDate(e,t)];else if("string"==typeof e)switch(n.config.mode){case"single":case"time":o=[n.parseDate(e,t)];break;case"multiple":o=e.split(n.config.conjunction).map((function(e){return n.parseDate(e,t)}));break;case"range":o=e.split(n.l10n.rangeSeparator).map((function(e){return n.parseDate(e,t)}))}else n.config.errorHandler(new Error("Invalid date supplied: "+JSON.stringify(e)));n.selectedDates=n.config.allowInvalidPreload?o:o.filter((function(e){return e instanceof Date&&ee(e,!1)})),"range"===n.config.mode&&n.selectedDates.sort((function(e,t){return e.getTime()-t.getTime()}))}function me(e){return e.slice().map((function(e){return"string"==typeof e||"number"==typeof e||e instanceof Date?n.parseDate(e,void 0,!0):e&&"object"==typeof e&&e.from&&e.to?{from:n.parseDate(e.from,void 0),to:n.parseDate(e.to,void 0)}:e})).filter((function(e){return e}))}function ge(){n._positionElement=n.config.positionElement||n._input}function we(e,t){if(void 0!==n.config){var o=n.config[e];if(void 0!==o&&o.length>0)for(var i=0;o[i]&&i<o.length;i++)o[i](n.selectedDates,n.input.value,n,t);"onChange"===e&&(n.input.dispatchEvent(ve("change")),n.input.dispatchEvent(ve("input")))}}function ve(e){var t=document.createEvent("Event");return t.initEvent(e,!0,!0),t}function _e(e){for(var t=0;t<n.selectedDates.length;t++){var o=n.selectedDates[t];if(o instanceof Date&&0===D(o,e))return""+t}return!1}function ye(){n.config.noCalendar||n.isMobile||!n.monthNav||(n.yearElements.forEach((function(e,t){var o=new Date(n.currentYear,n.currentMonth,1);o.setMonth(n.currentMonth+t),n.config.showMonths>1||"static"===n.config.monthSelectorType?n.monthElements[t].textContent=_(o.getMonth(),n.config.shorthandCurrentMonth,n.l10n)+" ":n.monthsDropdownContainer.value=o.getMonth().toString(),e.value=o.getFullYear().toString()})),n._hidePrevMonthArrow=void 0!==n.config.minDate&&(n.currentYear===n.config.minDate.getFullYear()?n.currentMonth<=n.config.minDate.getMonth():n.currentYear<n.config.minDate.getFullYear()),n._hideNextMonthArrow=void 0!==n.config.maxDate&&(n.currentYear===n.config.maxDate.getFullYear()?n.currentMonth+1>n.config.maxDate.getMonth():n.currentYear>n.config.maxDate.getFullYear()))}function be(e){var t=e||(n.config.altInput?n.config.altFormat:n.config.dateFormat);return n.selectedDates.map((function(e){return n.formatDate(e,t)})).filter((function(e,t,o){return"range"!==n.config.mode||n.config.enableTime||o.indexOf(e)===t})).join("range"!==n.config.mode?n.config.conjunction:n.l10n.rangeSeparator)}function Ce(e){void 0===e&&(e=!0),void 0!==n.mobileInput&&n.mobileFormatStr&&(n.mobileInput.value=void 0!==n.latestSelectedDateObj?n.formatDate(n.latestSelectedDateObj,n.mobileFormatStr):""),n.input.value=be(n.config.dateFormat),void 0!==n.altInput&&(n.altInput.value=be(n.config.altFormat)),!1!==e&&we("onValueUpdate")}function xe(e){var t=w(e),o=n.prevMonthNav.contains(t),i=n.nextMonthNav.contains(t);o||i?Q(o?-1:1):n.yearElements.indexOf(t)>=0?t.select():t.classList.contains("arrowUp")?n.changeYear(n.currentYear+1):t.classList.contains("arrowDown")&&n.changeYear(n.currentYear-1)}return function(){n.element=n.input=e,n.isOpen=!1,function(){var o=["wrap","weekNumbers","allowInput","allowInvalidPreload","clickOpens","time_24hr","enableTime","noCalendar","altInput","shorthandCurrentMonth","inline","static","enableSeconds","disableMobile"],s=M(M({},JSON.parse(JSON.stringify(e.dataset||{}))),t),l={};n.config.parseDate=s.parseDate,n.config.formatDate=s.formatDate,Object.defineProperty(n.config,"enable",{get:function(){return n.config._enable},set:function(e){n.config._enable=me(e)}}),Object.defineProperty(n.config,"disable",{get:function(){return n.config._disable},set:function(e){n.config._disable=me(e)}});var c="time"===s.mode;if(!s.dateFormat&&(s.enableTime||c)){var d=O.defaultConfig.dateFormat||a.dateFormat;l.dateFormat=s.noCalendar||c?"H:i"+(s.enableSeconds?":S":""):d+" H:i"+(s.enableSeconds?":S":"")}if(s.altInput&&(s.enableTime||c)&&!s.altFormat){var p=O.defaultConfig.altFormat||a.altFormat;l.altFormat=s.noCalendar||c?"h:i"+(s.enableSeconds?":S K":" K"):p+" h:i"+(s.enableSeconds?":S":"")+" K"}Object.defineProperty(n.config,"minDate",{get:function(){return n.config._minDate},set:re("min")}),Object.defineProperty(n.config,"maxDate",{get:function(){return n.config._maxDate},set:re("max")});var f=function(e){return function(t){n.config["min"===e?"_minTime":"_maxTime"]=n.parseDate(t,"H:i:S")}};Object.defineProperty(n.config,"minTime",{get:function(){return n.config._minTime},set:f("min")}),Object.defineProperty(n.config,"maxTime",{get:function(){return n.config._maxTime},set:f("max")}),"time"===s.mode&&(n.config.noCalendar=!0,n.config.enableTime=!0),Object.assign(n.config,l,s);for(var h=0;h<o.length;h++)n.config[o[h]]=!0===n.config[o[h]]||"true"===n.config[o[h]];for(i.filter((function(e){return void 0!==n.config[e]})).forEach((function(e){n.config[e]=u(n.config[e]||[]).map(r)})),n.isMobile=!n.config.disableMobile&&!n.config.inline&&"single"===n.config.mode&&!n.config.disable.length&&!n.config.enable&&!n.config.weekNumbers&&/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent),h=0;h<n.config.plugins.length;h++){var m=n.config.plugins[h](n)||{};for(var g in m)i.indexOf(g)>-1?n.config[g]=u(m[g]).map(r).concat(n.config[g]):void 0===s[g]&&(n.config[g]=m[g])}s.altInputClass||(n.config.altInputClass=se().className+" "+n.config.altInputClass),we("onParseConfig")}(),le(),n.input=se(),n.input?(n.input._type=n.input.type,n.input.type="text",n.input.classList.add("flatpickr-input"),n._input=n.input,n.config.altInput&&(n.altInput=f(n.input.nodeName,n.config.altInputClass),n._input=n.altInput,n.altInput.placeholder=n.input.placeholder,n.altInput.disabled=n.input.disabled,n.altInput.required=n.input.required,n.altInput.tabIndex=n.input.tabIndex,n.altInput.type="text",n.input.setAttribute("type","hidden"),!n.config.static&&n.input.parentNode&&n.input.parentNode.insertBefore(n.altInput,n.input.nextSibling)),n.config.allowInput||n._input.setAttribute("readonly","readonly"),ge()):n.config.errorHandler(new Error("Invalid input element specified")),function(){n.selectedDates=[],n.now=n.parseDate(n.config.now)||new Date;var e=n.config.defaultDate||("INPUT"!==n.input.nodeName&&"TEXTAREA"!==n.input.nodeName||!n.input.placeholder||n.input.value!==n.input.placeholder?n.input.value:null);e&&he(e,n.config.dateFormat),n._initialDate=n.selectedDates.length>0?n.selectedDates[0]:n.config.minDate&&n.config.minDate.getTime()>n.now.getTime()?n.config.minDate:n.config.maxDate&&n.config.maxDate.getTime()<n.now.getTime()?n.config.maxDate:n.now,n.currentYear=n._initialDate.getFullYear(),n.currentMonth=n._initialDate.getMonth(),n.selectedDates.length>0&&(n.latestSelectedDateObj=n.selectedDates[0]),void 0!==n.config.minTime&&(n.config.minTime=n.parseDate(n.config.minTime,"H:i")),void 0!==n.config.maxTime&&(n.config.maxTime=n.parseDate(n.config.maxTime,"H:i")),n.minDateHasTime=!!n.config.minDate&&(n.config.minDate.getHours()>0||n.config.minDate.getMinutes()>0||n.config.minDate.getSeconds()>0),n.maxDateHasTime=!!n.config.maxDate&&(n.config.maxDate.getHours()>0||n.config.maxDate.getMinutes()>0||n.config.maxDate.getSeconds()>0)}(),n.utils={getDaysInMonth:function(e,t){return void 0===e&&(e=n.currentMonth),void 0===t&&(t=n.currentYear),1===e&&(t%4==0&&t%100!=0||t%400==0)?29:n.l10n.daysInMonth[e]}},n.isMobile||function(){var e=window.document.createDocumentFragment();if(n.calendarContainer=f("div","flatpickr-calendar"),n.calendarContainer.tabIndex=-1,!n.config.noCalendar){if(e.appendChild((n.monthNav=f("div","flatpickr-months"),n.yearElements=[],n.monthElements=[],n.prevMonthNav=f("span","flatpickr-prev-month"),n.prevMonthNav.innerHTML=n.config.prevArrow,n.nextMonthNav=f("span","flatpickr-next-month"),n.nextMonthNav.innerHTML=n.config.nextArrow,V(),Object.defineProperty(n,"_hidePrevMonthArrow",{get:function(){return n.__hidePrevMonthArrow},set:function(e){n.__hidePrevMonthArrow!==e&&(p(n.prevMonthNav,"flatpickr-disabled",e),n.__hidePrevMonthArrow=e)}}),Object.defineProperty(n,"_hideNextMonthArrow",{get:function(){return n.__hideNextMonthArrow},set:function(e){n.__hideNextMonthArrow!==e&&(p(n.nextMonthNav,"flatpickr-disabled",e),n.__hideNextMonthArrow=e)}}),n.currentYearElement=n.yearElements[0],ye(),n.monthNav)),n.innerContainer=f("div","flatpickr-innerContainer"),n.config.weekNumbers){var t=function(){n.calendarContainer.classList.add("hasWeeks");var e=f("div","flatpickr-weekwrapper");e.appendChild(f("span","flatpickr-weekday",n.l10n.weekAbbreviation));var t=f("div","flatpickr-weeks");return e.appendChild(t),{weekWrapper:e,weekNumbers:t}}(),o=t.weekWrapper,i=t.weekNumbers;n.innerContainer.appendChild(o),n.weekNumbers=i,n.weekWrapper=o}n.rContainer=f("div","flatpickr-rContainer"),n.rContainer.appendChild(K()),n.daysContainer||(n.daysContainer=f("div","flatpickr-days"),n.daysContainer.tabIndex=-1),Y(),n.rContainer.appendChild(n.daysContainer),n.innerContainer.appendChild(n.rContainer),e.appendChild(n.innerContainer)}n.config.enableTime&&e.appendChild(function(){n.calendarContainer.classList.add("hasTime"),n.config.noCalendar&&n.calendarContainer.classList.add("noCalendar");var e=E(n.config);n.timeContainer=f("div","flatpickr-time"),n.timeContainer.tabIndex=-1;var t=f("span","flatpickr-time-separator",":"),o=g("flatpickr-hour",{"aria-label":n.l10n.hourAriaLabel});n.hourElement=o.getElementsByTagName("input")[0];var i=g("flatpickr-minute",{"aria-label":n.l10n.minuteAriaLabel});if(n.minuteElement=i.getElementsByTagName("input")[0],n.hourElement.tabIndex=n.minuteElement.tabIndex=-1,n.hourElement.value=l(n.latestSelectedDateObj?n.latestSelectedDateObj.getHours():n.config.time_24hr?e.hours:function(e){switch(e%24){case 0:case 12:return 12;default:return e%12}}(e.hours)),n.minuteElement.value=l(n.latestSelectedDateObj?n.latestSelectedDateObj.getMinutes():e.minutes),n.hourElement.setAttribute("step",n.config.hourIncrement.toString()),n.minuteElement.setAttribute("step",n.config.minuteIncrement.toString()),n.hourElement.setAttribute("min",n.config.time_24hr?"0":"1"),n.hourElement.setAttribute("max",n.config.time_24hr?"23":"12"),n.hourElement.setAttribute("maxlength","2"),n.minuteElement.setAttribute("min","0"),n.minuteElement.setAttribute("max","59"),n.minuteElement.setAttribute("maxlength","2"),n.timeContainer.appendChild(o),n.timeContainer.appendChild(t),n.timeContainer.appendChild(i),n.config.time_24hr&&n.timeContainer.classList.add("time24hr"),n.config.enableSeconds){n.timeContainer.classList.add("hasSeconds");var a=g("flatpickr-second");n.secondElement=a.getElementsByTagName("input")[0],n.secondElement.value=l(n.latestSelectedDateObj?n.latestSelectedDateObj.getSeconds():e.seconds),n.secondElement.setAttribute("step",n.minuteElement.getAttribute("step")),n.secondElement.setAttribute("min","0"),n.secondElement.setAttribute("max","59"),n.secondElement.setAttribute("maxlength","2"),n.timeContainer.appendChild(f("span","flatpickr-time-separator",":")),n.timeContainer.appendChild(a)}return n.config.time_24hr||(n.amPM=f("span","flatpickr-am-pm",n.l10n.amPM[c((n.latestSelectedDateObj?n.hourElement.value:n.config.defaultHour)>11)]),n.amPM.title=n.l10n.toggleTitle,n.amPM.tabIndex=-1,n.timeContainer.appendChild(n.amPM)),n.timeContainer}()),p(n.calendarContainer,"rangeMode","range"===n.config.mode),p(n.calendarContainer,"animate",!0===n.config.animate),p(n.calendarContainer,"multiMonth",n.config.showMonths>1),n.calendarContainer.appendChild(e);var a=void 0!==n.config.appendTo&&void 0!==n.config.appendTo.nodeType;if((n.config.inline||n.config.static)&&(n.calendarContainer.classList.add(n.config.inline?"inline":"static"),n.config.inline&&(!a&&n.element.parentNode?n.element.parentNode.insertBefore(n.calendarContainer,n._input.nextSibling):void 0!==n.config.appendTo&&n.config.appendTo.appendChild(n.calendarContainer)),n.config.static)){var r=f("div","flatpickr-wrapper");n.element.parentNode&&n.element.parentNode.insertBefore(r,n.element),r.appendChild(n.element),n.altInput&&r.appendChild(n.altInput),r.appendChild(n.calendarContainer)}n.config.static||n.config.inline||(void 0!==n.config.appendTo?n.config.appendTo:window.document.body).appendChild(n.calendarContainer)}(),function(){if(n.config.wrap&&["open","close","toggle","clear"].forEach((function(e){Array.prototype.forEach.call(n.element.querySelectorAll("[data-"+e+"]"),(function(t){return j(t,"click",n[e])}))})),n.isMobile)!function(){var e=n.config.enableTime?n.config.noCalendar?"time":"datetime-local":"date";n.mobileInput=f("input",n.input.className+" flatpickr-mobile"),n.mobileInput.tabIndex=1,n.mobileInput.type=e,n.mobileInput.disabled=n.input.disabled,n.mobileInput.required=n.input.required,n.mobileInput.placeholder=n.input.placeholder,n.mobileFormatStr="datetime-local"===e?"Y-m-d\\TH:i:S":"date"===e?"Y-m-d":"H:i:S",n.selectedDates.length>0&&(n.mobileInput.defaultValue=n.mobileInput.value=n.formatDate(n.selectedDates[0],n.mobileFormatStr)),n.config.minDate&&(n.mobileInput.min=n.formatDate(n.config.minDate,"Y-m-d")),n.config.maxDate&&(n.mobileInput.max=n.formatDate(n.config.maxDate,"Y-m-d")),n.input.getAttribute("step")&&(n.mobileInput.step=String(n.input.getAttribute("step"))),n.input.type="hidden",void 0!==n.altInput&&(n.altInput.type="hidden");try{n.input.parentNode&&n.input.parentNode.insertBefore(n.mobileInput,n.input.nextSibling)}catch(e){}j(n.mobileInput,"change",(function(e){n.setDate(w(e).value,!1,n.mobileFormatStr),we("onChange"),we("onClose")}))}();else{var e=d(ae,50);if(n._debouncedChange=d(I,300),n.daysContainer&&!/iPhone|iPad|iPod/i.test(navigator.userAgent)&&j(n.daysContainer,"mouseover",(function(e){"range"===n.config.mode&&ie(w(e))})),j(n._input,"keydown",oe),void 0!==n.calendarContainer&&j(n.calendarContainer,"keydown",oe),n.config.inline||n.config.static||j(window,"resize",e),void 0!==window.ontouchstart?j(window.document,"touchstart",G):j(window.document,"mousedown",G),j(window.document,"focus",G,{capture:!0}),!0===n.config.clickOpens&&(j(n._input,"focus",n.open),j(n._input,"click",n.open)),void 0!==n.daysContainer&&(j(n.monthNav,"click",xe),j(n.monthNav,["keyup","increment"],P),j(n.daysContainer,"click",pe)),void 0!==n.timeContainer&&void 0!==n.minuteElement&&void 0!==n.hourElement){j(n.timeContainer,["increment"],y),j(n.timeContainer,"blur",y,{capture:!0}),j(n.timeContainer,"click",B),j([n.hourElement,n.minuteElement],["focus","click"],(function(e){return w(e).select()})),void 0!==n.secondElement&&j(n.secondElement,"focus",(function(){return n.secondElement&&n.secondElement.select()})),void 0!==n.amPM&&j(n.amPM,"click",(function(e){y(e)}))}n.config.allowInput&&j(n._input,"blur",ne)}}(),(n.selectedDates.length||n.config.noCalendar)&&(n.config.enableTime&&$(n.config.noCalendar?n.latestSelectedDateObj:void 0),Ce(!1)),v();var o=/^((?!chrome|android).)*safari/i.test(navigator.userAgent);!n.isMobile&&o&&ce(),we("onReady")}(),n}function S(e,t){for(var n=Array.prototype.slice.call(e).filter((function(e){return e instanceof HTMLElement})),o=[],i=0;i<n.length;i++){var a=n[i];try{if(null!==a.getAttribute("data-fp-omit"))continue;void 0!==a._flatpickr&&(a._flatpickr.destroy(),a._flatpickr=void 0),a._flatpickr=$(a,t||{}),o.push(a._flatpickr)}catch(e){console.error(e)}}return 1===o.length?o[0]:o}"undefined"!=typeof HTMLElement&&"undefined"!=typeof HTMLCollection&&"undefined"!=typeof NodeList&&(HTMLCollection.prototype.flatpickr=NodeList.prototype.flatpickr=function(e){return S(this,e)},HTMLElement.prototype.flatpickr=function(e){return S([this],e)});var O=function(e,t){return"string"==typeof e?S(window.document.querySelectorAll(e),t):e instanceof Node?S([e],t):S(e,t)};O.defaultConfig={},O.l10ns={en:M({},s),default:M({},s)},O.localize=function(e){O.l10ns.default=M(M({},O.l10ns.default),e)},O.setDefaults=function(e){O.defaultConfig=M(M({},O.defaultConfig),e)},O.parseDate=k({}),O.formatDate=x({}),O.compareDates=D,"undefined"!=typeof jQuery&&void 0!==jQuery.fn&&(jQuery.fn.flatpickr=function(e){return S(this,e)}),Date.prototype.fp_incr=function(e){return new Date(this.getFullYear(),this.getMonth(),this.getDate()+("string"==typeof e?parseInt(e,10):e))},"undefined"!=typeof window&&(window.flatpickr=O);const P=window.WPCodeConditionalLogic||function(e,t,n){const o={l10n:wpcode,current_select:null,init:function(){o.should_init()&&(o.find_elements(),o.add_events(),o.show_relations_for_all_rows(),o.maybe_switch_location())},should_init:function(){return void 0!==wpcode.conditions},find_elements:function(){o.conditions=wpcode.conditions,o.conditions_holder=n(e.getElementById("wpcode-conditions-holder")),o.conditions_input=n(e.getElementById("wpcode-cl-rules")),o.add_group_button=n(e.getElementById("wpcode-cl-add-group")),o.group_template=n(e.getElementById("wpcode-conditions-group-markup")).html(),o.row_template=n(e.getElementById("wpcode-conditions-group-row-markup")).html(),o.show_hide_input=n(e.getElementById("wpcode-cl-show-hide")),o.document=n(e),o.options_dropdown=n(e.getElementById("wpcode_cl_picker"))},add_events:function(){o.init_add_group(),o.init_add_row(),o.init_remove_row(),o.init_change_type(),o.init_capture_rules(),o.init_change_show_hide(),o.init_select2(),jQuery((function(){o.trigger_row_loaded()}))},show_relations_for_all_rows(){o.conditions_holder.find(".wpcode-cl-rules-row").each((function(){o.show_hide_relation_options(n(this))}))},init_add_group:function(){o.add_group_button.on("click",(function(){o.add_group()}))},add_group(){const e=o.get_new_group();o.conditions_holder.append(e),o.add_new_row(e.find(".wpcode-cl-group-rules")),o.build_rules_from_inputs()},get_new_group:()=>n(o.group_template),get_new_row:()=>n(o.row_template),init_add_row(){o.conditions_holder.on("click",".wpcode-cl-add-row",(function(){o.add_new_row(n(this).closest(".wpcode-cl-group").find(".wpcode-cl-group-rules"))}))},init_remove_row(){o.conditions_holder.on("click",".wpcode-cl-remove-row",(function(){const e=n(this).closest(".wpcode-cl-group");n(this).closest(".wpcode-cl-rules-row").remove(),o.maybe_remove_group(e),o.build_rules_from_inputs(),o.options_dropdown.addClass("wpcode-hidden"),o.current_select=null}))},maybe_remove_group(e){0===e.find(".wpcode-cl-group-rules .wpcode-cl-rules-row").length&&o.remove_group(e)},remove_group(e){e.remove(),o.build_rules_from_inputs()},add_new_row(e){const t=o.get_new_row();t.appendTo(e);const n=t.find(".wpcode-cl-rule-type");o.handle_type_change(n),o.build_rules_from_inputs(),n.focus()},init_change_type(){o.conditions_holder.on("click",".wpcode-cl-rule-type-container",(function(e){e.preventDefault(),e.stopPropagation();const t=n(this).find(".wpcode-cl-rule-type");o.options_dropdown.hasClass("wpcode-hidden")||!t.is(o.current_select)?(o.options_dropdown.trigger("wpcode_select_item",t.val()),o.current_select=t,o.options_dropdown.insertAfter(t.closest(".wpcode-cl-rules-row")),o.options_dropdown.removeClass("wpcode-hidden"),o.options_dropdown.find(".wpcode-items-search-input").focus()):o.options_dropdown.addClass("wpcode-hidden")})),o.conditions_holder.on("change",".wpcode-cl-rule-type",(function(){o.handle_type_change(n(this))})),o.options_dropdown.on("change",".wpcode-radio-cl-option",(function(){const e=n(this).val();o.current_select.val(e).trigger("change"),o.options_dropdown.trigger("wpcode_select_item",e),o.options_dropdown.addClass("wpcode-hidden")}))},init_change_show_hide(){o.show_hide_input.on("change",(function(){o.build_rules_from_inputs()}))},handle_type_change(e){const t=e.find("option:selected"),n=e.val(),i=t.closest("optgroup").data("type"),a=o.conditions[i].options[n],r=e.closest(".wpcode-cl-rules-row");r.find(".wpcode-cl-rule-value").html(o.get_input_markup(a)),r.find(".wpcode-cl-rule-relation option").prop("selected",!1),o.show_hide_relation_options(r),o.init_select2(),o.trigger_row_loaded()},get_input_markup(e){let t="";switch(e.type){case"select":t=o.get_input_select(e.options,e.multiple);break;case"text":t=o.get_input_text();break;case"ajax":t=o.get_input_ajax(e.options,e.multiple);break;case"time":t=o.get_input_time();break;case"date":t=o.get_input_date();break;case"datetime":t=o.get_input_datetime()}return t},get_input_select(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];const o=n("<select/>");return t&&(o.attr("multiple","multiple"),o.addClass("wpcode-select2")),n.each(e,(function(e,t){o.append(n("<option />",{value:t.value,disabled:!0===t.disabled}).text(t.label))})),o},get_input_text:()=>n('<input type="text" class="wpcode-input-text" />'),get_input_ajax(e,t){const o=n('<select data-action="'+e+'" class="wpcode-select2-ajax" />');return t&&o.attr("multiple","multiple"),o},get_input_time:()=>n('<input type="text" class="wpcode-input-text wpcode-input-time" />'),get_input_date:()=>n('<input type="text" class="wpcode-input-text wpcode-input-date" />'),get_input_datetime:()=>n('<input type="text" class="wpcode-input-text wpcode-input-datetime" />'),init_capture_rules(){o.conditions_holder.on("change","input,select",(function(){o.build_rules_from_inputs()}))},build_rules_from_inputs(){const e=o.conditions_holder.find(".wpcode-cl-group"),t=[];e.each((function(e){const i=n(this).find(".wpcode-cl-rules-row");t[e]=[],i.each((function(){const i=n(this),a=i.find(".wpcode-cl-rule-type"),r=a.find("option:selected").closest("optgroup").data("type"),s=a.val(),l={},c=o.conditions[r].options[s];l.type=r,l.option=s,l.relation=i.find(".wpcode-cl-rule-relation").val(),l.value=o.get_input_value(c,i),null!==l.value&&t[e].push(l)}))}));const i={show:o.show_hide_input.val(),groups:t};o.conditions_input.val(JSON.stringify(i))},get_input_value(e,t){let n="";switch(e.type){case"select":case"ajax":n=t.find(".wpcode-cl-rule-value select").val();break;case"text":case"time":case"date":case"datetime":n=t.find(".wpcode-cl-rule-value input").val()}return n},show_hide_relation_options(e){const t=e.find(".wpcode-cl-rule-type"),i=t.val(),a=t.find("option:selected").closest("optgroup").data("type"),r=e.find(".wpcode-cl-rule-relation"),s=r.find("option"),l={select:["=","!="],ajax:["=","!="],text:["contains","notcontains","=","!="],date:["=","!=","before","after","before-or","after-or"],datetime:["=","!=","before","after","before-or","after-or"],time:["=","before","after","before-or","after-or"]}[o.conditions[a].options[i].type],c=o.l10n.cl_labels_custom[i];let d=null;if(s.each((function(){const e=n(this).attr("value");l.indexOf(e)>-1?(null===d&&(d=e),n(this).show(),c&&c[e]?n(this).text(c[e]):n(this).text(o.l10n.cl_labels[e])):n(this).hide()})),d&&(console.log(d),r.val(d)),c&&c.placeholder){const t=e.find(".wpcode-select2-ajax"),n=e.find(".wpcode-input-text");t.length>0&&t.data("placeholder",c.placeholder),n.length>0&&n.attr("placeholder",c.placeholder)}},init_select2(){o.conditions_holder.find(".wpcode-select2").selectWoo(),o.conditions_holder.find(".wpcode-select2-ajax").selectWoo({ajax:{url:ajaxurl,data:function(e){return{action:n(this).data("action"),term:e.term,_wpnonce:o.l10n.nonce}}}})},trigger_row_loaded(){n(e.body).trigger("wpcode_cl_type_loaded",{conditions_holder:o.conditions_holder})},maybe_switch_location(){const e=n('input[name="wpcode_auto_insert_location"]:checked'),t=n("#wpcode_snippet_type");n("#wpcode_conditional_logic_enable").on("change",(function(){if("php"!==t.val()||"everywhere"!==e.val()||!n(this).is(":checked"))return;const i=n(this).closest(".wpcode-metabox-form-row");n('input[name="wpcode_auto_insert_location"][value="frontend_cl"]').prop("checked",!0).trigger("change"),WPCodeAdminNotices.get_notice(o.l10n.php_cl_location_notice,"notice-warning notice-alt").insertBefore(i),o.document.trigger("wp-updates-notice-added")}))}};return o}(document,window,jQuery);P.init();const j=window.WPCodeAdminNotices||function(e,n,o){const i={l10n:wpcode,init:function(){n.WPCodeAdminNotices=i,i.notice_holder=o(e.getElementById("wpcode-notice-area")),i.document=o(e)},add_notice(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"updated";const n=i.get_notice(e,t);i.notice_holder.append(n),i.document.trigger("wp-updates-notice-added"),n.find("button").focus()},get_notice(e,t){const n=o("<div />"),i=o("<p />");return i.html(e),n.addClass("fade notice is-dismissible"),n.addClass(t),n.append(i),n},show_pro_notice(e,a,r,s){const l=r.startsWith("wpcode-");t().fire({title:e,text:a,customClass:{confirmButton:"wpcode-button wpcode-button-orange wpcode-button-large",icon:"wpcode-lock"},showCloseButton:!0,confirmButtonText:s||i.l10n.upgrade_button,iconHtml:i.icon_lock}).then((function(e){e.isConfirmed&&!l&&n.open(r,"_blank","noopener noreferrer"),e.isConfirmed&&l&&WPCodeAddons&&(t().fire({title:i.l10n.please_wait,didOpen:()=>{t().showLoading()}}),WPCodeAddons.install_addon(o("<button></button>").data("addon",r)))}))}};return i}(document,window,jQuery);j.init(),n(900),n(423),n(124),n(786),n(298),n(801),n(847),n(770),n(868),n(448),(window.WPCodeLibraryAuth||function(e,n,o){const i={i18n:n.wpcode,init:function(){i.load_elements(),i.add_event_listeners(),n.WPCodeLibraryAuth=i},load_elements(){i.auth_delete_button=o(".wpcode-delete-auth")},add_event_listeners(){o(e).on("click",".wpcode-start-auth",(function(e){e.preventDefault(),i.start_auth(o(this))})),i.auth_delete_button.on("click",(function(e){e.preventDefault(),i.delete_auth()})),n.addEventListener("message",(e=>{e.isTrusted&&i.store_auth(e.data,e.origin)}),!1)},start_auth(e){let o=n.open(i.i18n.connect_url,"_blank","location=no,width=500,height=730,scrollbars=0");null===o?t().fire({title:"Your browser blocked the authorization window from opening. Please check your popup settings.",customClass:{confirmButton:"wpcode-button",cancelButton:"wpcode-button"}}):o.focus()},delete_auth(){i.show_please_wait(),o.post(ajaxurl,{action:"wpcode_library_delete_auth",_ajax_nonce:i.i18n.nonce,multisite:i.i18n.multisite},(function(e){e.success&&n.location.reload()}))},store_auth(e,a){if(void 0===e.key||void 0===e.username)return;const r=void 0!==e.deploy_snippet_id?e.deploy_snippet_id:0;i.show_please_wait(),t().showLoading(),o.post(ajaxurl,{action:"wpcode_library_store_auth",key:e.key,username:e.username,webhook_secret:e.webhook_secret,client_id:e.client_id,deploy_snippet_id:r,origin:a,_ajax_nonce:i.i18n.nonce,multisite:i.i18n.multisite},(function(e){e.success&&t().fire({title:e.data.title,text:e.data.text,didOpen:()=>{t().showLoading(),setTimeout((function(){n.location.reload()}),2e3)}})}))},show_please_wait(){t().fire({title:i.i18n.please_wait,didOpen:()=>{t().showLoading()}})}};return i}(document,window,jQuery)).init(),n(670),n(928),(window.WPCodeTestingMode||function(e,n,o){const i={l18n:n.wpcode,init:function(){i.should_init()&&i.init_events()},should_init:()=>(i.$toggle=o("#wpcode-toggle-testing-mode"),i.$toggle.length>0),init_events(){i.$toggle.on("change",i.toggle)},toggle(){i.swal({title:i.l18n.testing_mode.title,text:i.l18n.testing_mode.text,customClass:{confirmButton:"wpcode-button wpcode-button-orange wpcode-button-large",icon:"wpcode-lock"},showCloseButton:!0,confirmButtonText:i.l18n.testing_mode.button_text,iconHtml:'<svg width="22" height="28" viewBox="0 0 22 28" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M19 9.33333H17.6666V6.66667C17.6666 2.98667 14.68 0 11 0C7.31998 0 4.33331 2.98667 4.33331 6.66667V9.33333H2.99998C1.53331 9.33333 0.333313 10.5333 0.333313 12V25.3333C0.333313 26.8 1.53331 28 2.99998 28H19C20.4666 28 21.6666 26.8 21.6666 25.3333V12C21.6666 10.5333 20.4666 9.33333 19 9.33333ZM6.99998 6.66667C6.99998 4.45333 8.78665 2.66667 11 2.66667C13.2133 2.66667 15 4.45333 15 6.66667V9.33333H6.99998V6.66667ZM19 25.3333H2.99998V12H19V25.3333ZM11 21.3333C12.4666 21.3333 13.6666 20.1333 13.6666 18.6667C13.6666 17.2 12.4666 16 11 16C9.53331 16 8.33331 17.2 8.33331 18.6667C8.33331 20.1333 9.53331 21.3333 11 21.3333Z" fill="#8A8A8A"/></svg>',footer:'<a href="'+i.l18n.testing_mode.learn_more_link+'" target="_blank" rel="noopener noreferrer">'+i.l18n.testing_mode.learn_more_text+"</a>"}).then((function(e){i.$toggle.prop("checked",!1),e.isConfirmed&&n.open(i.l18n.testing_mode.link,"_blank","noopener noreferrer")}))},swal:e=>t().fire(o.extend({hideClass:{popup:"",backdrop:"swal2-backdrop-hide",icon:"swal2-icon-hide"}},e))};return i}(document,window,jQuery)).init(),n(180),window.WPCodePlugins=window.WPCodePlugins||function(e,n,o){const i={l18n:n.wpcode,init:function(){i.should_init()&&i.init_install()},should_init:()=>(i.$install_buttons=o(".wpcode-button-install-plugin"),i.$install_buttons.length>0),init_install(){i.$install_buttons.on("click",(function(e){e.preventDefault();const t=o(this);i.install_plugin(t)}))},install_plugin(e){const a=e.data("slug");a&&(i.show_button_spinner(e),o.post(ajaxurl,{action:"wpcode_install_plugin",slug:a,_wpnonce:wpcode.nonce,multisite:i.l18n.multisite},(function(o){o.success?n.location.reload():(i.hide_button_spinner(e),o.data.message&&t().fire({title:!1,text:o.data.message,icon:"warning",confirmButtonColor:"#3085d6",confirmButtonText:i.l18n.ok,customClass:{confirmButton:"wpcode-button"}}))})))},show_button_spinner(e){n.WPCodeSpinner.show_button_spinner(e)},hide_button_spinner(e){n.WPCodeSpinner.hide_button_spinner(e)}};return i}(document,window,jQuery),WPCodePlugins.init()}()}();