# Copyright (C) 2024 WPCode
# This file is distributed under the GPLv2 or later.
msgid ""
msgstr ""
"Project-Id-Version: WPCode Lite 2.2.0\n"
"Report-Msgid-Bugs-To: https://wordpress.org/support/plugin/insert-headers-and-footers\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"POT-Creation-Date: 2024-07-17T12:07:54+00:00\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"X-Generator: WP-CLI 2.5.0\n"
"X-Domain: insert-headers-and-footers\n"

#. Plugin Name of the plugin
msgid "WPCode Lite"
msgstr ""

#. Plugin URI of the plugin
#. Author URI of the plugin
msgid "https://www.wpcode.com/"
msgstr ""

#. Description of the plugin
msgid "Easily add code snippets in WordPress. Insert scripts to the header and footer, add PHP code snippets with conditional logic, insert ads pixel, custom content, and more."
msgstr ""

#. Author of the plugin
msgid "WPCode"
msgstr ""

#: ihaf.php:125
msgid "Heads up!"
msgstr ""

#: ihaf.php:126
msgid "Your site already has WPCode Pro activated. If you want to switch to WPCode Lite, please first go to Plugins → Installed Plugins and deactivate WPCode. Then, you can activate WPCode Lite."
msgstr ""

#: includes/admin/admin-ajax-handlers.php:39
#: includes/class-wpcode-snippet.php:470
msgid "You are not allowed to change snippet status, please contact your webmaster."
msgstr ""

#. Translators: %2$s is the action that they were trying to perform, either activated or deactivated. %1$s is the error message why the action failed.
#: includes/admin/admin-ajax-handlers.php:55
msgid "Snippet not %2$s, the following error was encountered: %1$s"
msgstr ""

#: includes/admin/admin-ajax-handlers.php:57
msgctxt "Snippet status change"
msgid "activated"
msgstr ""

#: includes/admin/admin-ajax-handlers.php:57
msgctxt "Snippet status change"
msgid "deactivated"
msgstr ""

#. Translators: this an auto-generated title for when a snippet is saved from the generator.
#: includes/admin/admin-ajax-handlers.php:166
msgid "Generated Snippet %s"
msgstr ""

#: includes/admin/admin-ajax-handlers.php:222
msgid "Success! Your server can make SSL connections."
msgstr ""

#: includes/admin/admin-ajax-handlers.php:229
msgid "There was an error and the connection failed. Please contact your web host with the technical details below."
msgstr ""

#: includes/admin/admin-scripts.php:48
msgid "Please wait."
msgstr ""

#: includes/admin/admin-scripts.php:49
#: includes/admin/pages/class-wpcode-admin-page-settings.php:430
msgid "OK"
msgstr ""

#: includes/admin/admin-scripts.php:50
#: includes/admin/admin-scripts.php:54
#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:1620
#: includes/lite/class-wpcode-smart-tags-lite.php:22
msgid "Upgrade to PRO"
msgstr ""

#: includes/admin/admin-scripts.php:52
msgid "Testing Mode is a Premium Feature"
msgstr ""

#: includes/admin/admin-scripts.php:53
msgid "Upgrade to PRO today and make changes to your snippets, Header & Footer scripts or Page Scripts without affecting your live site. You choose when and what to publish to your visitors."
msgstr ""

#: includes/admin/admin-scripts.php:56
msgid "Learn more about Testing Mode"
msgstr ""

#: includes/admin/class-wpcode-admin-page-loader.php:136
#: includes/admin/class-wpcode-admin-page-loader.php:137
#: includes/admin/class-wpcode-admin-page-loader.php:194
#: includes/admin/pages/class-wpcode-admin-page-code-snippets.php:32
#: includes/class-wpcode-admin-bar-info.php:226
msgid "Code Snippets"
msgstr ""

#: includes/admin/class-wpcode-admin-page-loader.php:202
#: includes/admin/pages/class-wpcode-admin-page-settings.php:53
#: includes/class-wpcode-admin-bar-info.php:408
msgid "Settings"
msgstr ""

#: includes/admin/class-wpcode-admin-page-loader.php:217
msgid "Upgrade to WPCode Pro"
msgstr ""

#: includes/admin/class-wpcode-admin-page-loader.php:218
msgid "Get WPCode Pro"
msgstr ""

#. Translators: Placeholder for the category name.
#: includes/admin/class-wpcode-docs.php:164
msgid "View All %s Docs"
msgstr ""

#: includes/admin/class-wpcode-metabox-snippets.php:55
msgid "WPCode Page Scripts"
msgstr ""

#: includes/admin/class-wpcode-metabox-snippets.php:57
#: includes/admin/pages/class-wpcode-admin-page-headers-footers.php:167
#: includes/generator/class-wpcode-generator-script.php:193
#: includes/lite/admin/class-wpcode-metabox-snippets-lite.php:23
msgid "Header"
msgstr ""

#: includes/admin/class-wpcode-metabox-snippets.php:58
#: includes/admin/pages/class-wpcode-admin-page-headers-footers.php:171
#: includes/generator/class-wpcode-generator-script.php:192
#: includes/lite/admin/class-wpcode-metabox-snippets-lite.php:36
msgid "Footer"
msgstr ""

#: includes/admin/class-wpcode-metabox-snippets.php:64
#: includes/admin/pages/class-wpcode-admin-page-headers-footers.php:169
#: includes/lite/admin/class-wpcode-metabox-snippets-lite.php:49
msgid "Body"
msgstr ""

#: includes/admin/class-wpcode-metabox-snippets.php:67
msgid "Custom Code Snippet"
msgstr ""

#: includes/admin/class-wpcode-metabox-snippets.php:68
#: includes/generator/class-wpcode-generator-post-type.php:384
msgid "Revisions"
msgstr ""

#. Translators: Human-Readable time to display.
#: includes/admin/class-wpcode-notifications.php:287
msgid "%1$s ago"
msgstr ""

#: includes/admin/class-wpcode-review.php:128
msgid "Are you enjoying WPCode?"
msgstr ""

#: includes/admin/class-wpcode-review.php:130
#: includes/generator/class-wpcode-generator-post-status.php:170
#: includes/generator/class-wpcode-generator-post-status.php:195
#: includes/generator/class-wpcode-generator-post-type.php:399
#: includes/generator/class-wpcode-generator-post-type.php:551
#: includes/generator/class-wpcode-generator-post-type.php:757
#: includes/generator/class-wpcode-generator-query.php:136
#: includes/generator/class-wpcode-generator-query.php:379
#: includes/generator/class-wpcode-generator-query.php:436
#: includes/generator/class-wpcode-generator-script.php:211
#: includes/generator/class-wpcode-generator-script.php:228
#: includes/generator/class-wpcode-generator-style.php:212
#: includes/generator/class-wpcode-generator-style.php:229
#: includes/generator/class-wpcode-generator-taxonomy.php:428
#: includes/generator/class-wpcode-generator-taxonomy.php:505
msgid "Yes"
msgstr ""

#: includes/admin/class-wpcode-review.php:130
msgid "Not Really"
msgstr ""

#: includes/admin/class-wpcode-review.php:134
msgid "We're sorry to hear you aren't enjoying WPCode. We would love a chance to improve. Could you take a minute and let us know what we can do better?"
msgstr ""

#: includes/admin/class-wpcode-review.php:136
#: includes/admin/class-wpcode-review.php:142
msgid "Give Feedback"
msgstr ""

#: includes/admin/class-wpcode-review.php:136
#: includes/admin/class-wpcode-review.php:142
msgid "No thanks"
msgstr ""

#: includes/admin/class-wpcode-review.php:140
msgid "That's awesome! Could you please do us a BIG favor and give it a 5-star rating on WordPress to help us spread the word and boost our motivation?"
msgstr ""

#. translators: $1$s - WPCode plugin name; $2$s - WP.org review link; $3$s - WP.org review link.
#: includes/admin/class-wpcode-review.php:193
msgid "Please rate %1$s <a href=\"%2$s\" target=\"_blank\" rel=\"noopener noreferrer\">&#9733;&#9733;&#9733;&#9733;&#9733;</a> on <a href=\"%3$s\" target=\"_blank\" rel=\"noopener\">WordPress.org</a> to help us spread the word. Thank you from the WPCode team!"
msgstr ""

#. Translators: Support link tag starts with url and Support link tag ends.
#: includes/admin/class-wpcode-skin-legacy.php:92
#: includes/admin/class-wpcode-skin.php:92
msgid "There was an error installing the addon. Please try again. If you are still having issues, please %1$scontact our support%2$s team."
msgstr ""

#. Translators: The name of the addon that can't be installed, Support link tag starts with url and Support link tag ends.
#: includes/admin/class-wpcode-skin-legacy.php:105
#: includes/admin/class-wpcode-skin.php:106
msgid "There was an error installing the addon, %1$s. Please try again. If you are still having issues, please %2$scontact our support%3$s team. "
msgstr ""

#: includes/admin/class-wpcode-suggested-plugins.php:42
msgid "Easy, Fast and Secure WordPress and Website Migration."
msgstr ""

#: includes/admin/class-wpcode-suggested-plugins.php:50
msgid "Replace text across your database or media uploads in a single plugin."
msgstr ""

#: includes/admin/class-wpcode-suggested-plugins.php:58
msgid "Making Email Deliverability Easy for WordPress"
msgstr ""

#: includes/admin/class-wpcode-suggested-plugins.php:66
msgid "Powerful SEO Plugin to Boost SEO Rankings & Increase Traffic."
msgstr ""

#: includes/admin/class-wpcode-suggested-plugins.php:74
msgid "The Best Drag & Drop WordPress Form Builder."
msgstr ""

#: includes/admin/class-wpcode-suggested-plugins.php:82
msgid "Connect your WordPress plugins together and create automated workflows."
msgstr ""

#: includes/admin/class-wpcode-suggested-plugins.php:167
msgid "Plugin already installed and activated."
msgstr ""

#: includes/admin/class-wpcode-suggested-plugins.php:176
#: includes/admin/class-wpcode-suggested-plugins.php:186
msgid "Plugin activated."
msgstr ""

#: includes/admin/class-wpcode-suggested-plugins.php:215
msgid "Plugin installed and activated."
msgstr ""

#: includes/admin/class-wpcode-suggested-plugins.php:298
msgid "Enjoying WPCode? Check out some of our other top-rated FREE plugins:"
msgstr ""

#: includes/admin/class-wpcode-suggested-plugins.php:308
msgid "Install Plugin"
msgstr ""

#: includes/admin/class-wpcode-upgrade-welcome.php:34
#: includes/admin/class-wpcode-upgrade-welcome.php:35
msgid "Welcome to WPCode"
msgstr ""

#: includes/admin/class-wpcode-upgrade-welcome.php:135
msgid "Header & Footer Scripts"
msgstr ""

#: includes/admin/class-wpcode-upgrade-welcome.php:136
msgid "Effortlessly manage global headers & footers in a familiar interface."
msgstr ""

#: includes/admin/class-wpcode-upgrade-welcome.php:140
#: includes/admin/pages/class-wpcode-admin-page-pixel.php:38
msgid "Conversion Pixels"
msgstr ""

#: includes/admin/class-wpcode-upgrade-welcome.php:141
msgid "Easily target specific pages to track conversions reliably."
msgstr ""

#: includes/admin/class-wpcode-upgrade-welcome.php:145
#: includes/admin/pages/class-wpcode-admin-page-settings.php:694
msgid "PHP Snippets"
msgstr ""

#: includes/admin/class-wpcode-upgrade-welcome.php:146
msgid "Add or remove features with full confidence that your site will not break."
msgstr ""

#: includes/admin/class-wpcode-upgrade-welcome.php:150
msgid "Conditional Logic"
msgstr ""

#: includes/admin/class-wpcode-upgrade-welcome.php:151
msgid "Create advanced conditional logic rules in an easy-to-use interface."
msgstr ""

#: includes/admin/class-wpcode-upgrade-welcome.php:155
#: includes/admin/pages/class-wpcode-admin-page-settings.php:65
msgid "Error Handling"
msgstr ""

#: includes/admin/class-wpcode-upgrade-welcome.php:156
msgid "Unique error handling capabilities ensure you will not get locked out of your site."
msgstr ""

#: includes/admin/class-wpcode-upgrade-welcome.php:160
msgid "Snippets Library"
msgstr ""

#: includes/admin/class-wpcode-upgrade-welcome.php:161
msgid "One-click install from our extensive library of commonly-used snippets."
msgstr ""

#. Translators: This simply adds the plugin name before the logo text.
#: includes/admin/class-wpcode-upgrade-welcome.php:166
msgid "%s logo"
msgstr ""

#: includes/admin/class-wpcode-upgrade-welcome.php:175
msgid "Insert Headers and Footers is now WPCode"
msgstr ""

#: includes/admin/class-wpcode-upgrade-welcome.php:176
msgid "When we first built Insert Headers and Footers over a decade ago, it was meant to do one very simple thing: add header and footer scripts to your site without editing theme files."
msgstr ""

#: includes/admin/class-wpcode-upgrade-welcome.php:177
msgid "Since then, the plugin has grown to over 1 million active installs with an amazing user base. We have continued to receive feature requests to add more options like controlling which pages the scripts get loaded, allowing more types of code snippets, etc."
msgstr ""

#: includes/admin/class-wpcode-upgrade-welcome.php:178
msgid "We listened to your feedback, and we are excited to present WPCode, the next generation of Insert Headers and Footers. We chose a new name because it was only fair considering the plugin is now 10x more powerful. Aside from adding global headers and footer snippets, you can also add multiple other types of code snippets, have granular control of where the snippets are output with conditional logic, and a whole lot more."
msgstr ""

#. Translators: Placeholders 1 & 2 add a link to scroll down the page and 3 & 4 add a link to the suggestions form.
#: includes/admin/class-wpcode-upgrade-welcome.php:183
msgid "Please see the full list of features %1$sbelow%2$s and let us know what you'd like us to add next by %3$ssharing your feedback%4$s."
msgstr ""

#. Translators: Placeholders add link to the details about settings.
#: includes/admin/class-wpcode-upgrade-welcome.php:198
msgid "For those of you who want to limit the functionality and switch back to the old interface, you can do so with one click. %1$sSee details here%2$s."
msgstr ""

#: includes/admin/class-wpcode-upgrade-welcome.php:207
msgid "We have an exciting roadmap ahead of us since you have shared tons of great ideas with us over the last several years. We truly appreciate your continued support and thank you for being an awesome user."
msgstr ""

#: includes/admin/class-wpcode-upgrade-welcome.php:208
msgid "We truly appreciate your continued support and thank you for using WPCode."
msgstr ""

#. Translators: Placeholder for "WPBeginner".
#: includes/admin/class-wpcode-upgrade-welcome.php:219
msgid "Founder of %s"
msgstr ""

#: includes/admin/class-wpcode-upgrade-welcome.php:231
msgid "Lead Developer"
msgstr ""

#: includes/admin/class-wpcode-upgrade-welcome.php:237
msgid "What’s New in WPCode (Features & Highlights)"
msgstr ""

#: includes/admin/class-wpcode-upgrade-welcome.php:257
#: includes/admin/class-wpcode-upgrade-welcome.php:307
msgid "WPCode Generator Screen capture"
msgstr ""

#: includes/admin/class-wpcode-upgrade-welcome.php:260
msgid "Snippet Generator"
msgstr ""

#: includes/admin/class-wpcode-upgrade-welcome.php:261
msgid "WPCode now includes a snippet generator directly in the plugin."
msgstr ""

#: includes/admin/class-wpcode-upgrade-welcome.php:262
msgid "Using the built-in generators, you can quickly add custom post types, custom post statuses, widgets, menus, build complex WP Queries and much more."
msgstr ""

#: includes/admin/class-wpcode-upgrade-welcome.php:263
msgid "Simply fill in the fields in our guided wizard to generate a custom ready-to-use snippet for your website with 1 click. Try WordPress Snippet Generator."
msgstr ""

#: includes/admin/class-wpcode-upgrade-welcome.php:270
msgid "Store Snippets in Cloud"
msgstr ""

#: includes/admin/class-wpcode-upgrade-welcome.php:271
msgid "A lot of you requested the ability to save and re-use snippets on multiple websites."
msgstr ""

#. Translators: Placeholders add a link to the suggestions page.
#: includes/admin/class-wpcode-upgrade-welcome.php:276
msgid "This feature is now available in the %1$sPRO version of the plugin%2$s along with other powerful features."
msgstr ""

#. Translators: Placeholders add a link to the suggestions page.
#: includes/admin/class-wpcode-upgrade-welcome.php:289
msgid "If you have specific ideas or feature requests, please let us know by %1$sfilling out this form%2$s."
msgstr ""

#: includes/admin/class-wpcode-upgrade-welcome.php:300
msgid "WPCode Cloud Screen capture"
msgstr ""

#: includes/admin/class-wpcode-upgrade-welcome.php:310
msgid "Not ready for the new interface?"
msgstr ""

#: includes/admin/class-wpcode-upgrade-welcome.php:311
msgid "If you are not ready to switch to the new interface, or you simply want to use the plugin just for inserting headers and footers we've got you covered."
msgstr ""

#. Translators: Placeholders add a link to the settings page.
#: includes/admin/class-wpcode-upgrade-welcome.php:316
msgid "You can switch to the simple Headers & Footers interface at any time from the %1$ssettings page%2$s."
msgstr ""

#: includes/admin/class-wpcode-upgrade-welcome.php:325
msgid "And if you change your mind later and want to give the full plugin a shot, you can always switch back with just 2 clicks using the option at the top of the page."
msgstr ""

#: includes/admin/class-wpcode-upgrade-welcome.php:330
msgid "Add Your First Snippet"
msgstr ""

#: includes/admin/importers/class-wpcode-importer-code-snippets.php:92
#: includes/admin/importers/class-wpcode-importer-woody.php:89
msgid "Unknown Snippet"
msgstr ""

#: includes/admin/importers/class-wpcode-importer-code-snippets.php:93
#: includes/admin/importers/class-wpcode-importer-woody.php:90
msgid "The snippet you are trying to import does not exist."
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-click.php:45
msgid "1-Click"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-click.php:98
msgid "You're almost there! To finish installing the snippet, you need to connect your site to your account on the WPCode Library. This will allow you to install snippets directly to your site in the future."
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-click.php:99
msgid "You'll also get access to tens of free expert-curated snippets that can be installed with 1-click from inside the plugin."
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-click.php:102
#: includes/admin/pages/class-wpcode-admin-page-click.php:142
#: includes/admin/pages/class-wpcode-admin-page.php:1291
msgid "Connect to Library"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-click.php:109
msgid "Your site is already connected to the  WPCode Library using another account"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-click.php:111
msgid "In order to continue installing the snippet from the WPCode library you have to either login to the Library with the same account used to connect this site to the WPCode library initially or disconnect this site from the WPCode library and connect using your own account."
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-click.php:115
msgid "Disconnect Site From Library"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-click.php:118
msgid "Login with another user on the WPCode Library"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-click.php:127
msgid "Congratulations, your site is now connected!"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-click.php:128
msgid "Your site is now connected to the WPCode Library and you can install snippets directly from the library. Please click the button below to resume installing the snippet you were viewing."
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-click.php:131
msgid "Resume Snippet Installation"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-click.php:138
msgid "Your site is not connected to the WPCode library."
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-click.php:139
msgid "Connect now to enable installing public snippets from the WPCode library with 1-click and also get access to tens of expert-curated snippets that you can install from inside the plugin."
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-click.php:150
msgid "No snippet provided."
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-click.php:157
msgid "Missing authentication token, please click the link in the WPCode Library again."
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-click.php:170
msgid "We encountered an error loading your snippet, please try again in a few minutes"
msgstr ""

#. translators: %s: The error message from the API.
#: includes/admin/pages/class-wpcode-admin-page-click.php:175
msgid "We encountered the following error loading your snippet: %s"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-click.php:186
msgid "Go back to the library"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-click.php:194
msgid "Library Snippet Preview"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-click.php:195
msgid "Please review the snippet below and confirm that you would like to install it."
msgstr ""

#. Translators: %s: The snippet name.
#: includes/admin/pages/class-wpcode-admin-page-click.php:199
msgid "Snippet title: %s"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-click.php:205
msgid "Code preview"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-click.php:209
#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:460
#: includes/admin/pages/class-wpcode-code-snippets-table.php:643
msgid "Code Type"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-click.php:230
msgid "Confirm & Install Snippet"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-click.php:255
msgid "Confirm Snippet Installation"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-code-snippets.php:216
#: includes/admin/pages/class-wpcode-admin-page.php:936
msgid "Search Snippets"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-code-snippets.php:234
#: includes/admin/pages/class-wpcode-admin-page.php:936
msgid "All Snippets"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-code-snippets.php:236
#: includes/generator/class-wpcode-generator-post-type.php:231
msgid "Add New"
msgstr ""

#. Translators: %d - Trashed snippets count.
#: includes/admin/pages/class-wpcode-admin-page-code-snippets.php:253
msgid "%d snippet was successfully moved to Trash."
msgid_plural "%d snippets were successfully moved to Trash."
msgstr[0] ""
msgstr[1] ""

#. translators: %d - Restored from trash snippets count.
#: includes/admin/pages/class-wpcode-admin-page-code-snippets.php:261
msgid "%d snippet was successfully restored."
msgid_plural "%d snippet were successfully restored."
msgstr[0] ""
msgstr[1] ""

#. translators: %d - Deleted snippets count.
#: includes/admin/pages/class-wpcode-admin-page-code-snippets.php:269
msgid "%d snippet was successfully permanently deleted."
msgid_plural "%d snippets were successfully permanently deleted."
msgstr[0] ""
msgstr[1] ""

#. translators: %d - Activated snippets count.
#: includes/admin/pages/class-wpcode-admin-page-code-snippets.php:279
msgid "%d snippet was successfully activated."
msgid_plural "%d snippets were successfully activated."
msgstr[0] ""
msgstr[1] ""

#. translators: %d - Failed to activate snippets count.
#: includes/admin/pages/class-wpcode-admin-page-code-snippets.php:288
msgid "%d snippet was not activated due to an error."
msgid_plural "%d snippets were not activated due to errors."
msgstr[0] ""
msgstr[1] ""

#. translators: %d - Deactivated snippets count.
#: includes/admin/pages/class-wpcode-admin-page-code-snippets.php:297
msgid "%d snippet was successfully deactivated."
msgid_plural "%d snippets were successfully deactivated."
msgstr[0] ""
msgstr[1] ""

#: includes/admin/pages/class-wpcode-admin-page-code-snippets.php:322
#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:2103
msgid "Enable Error Logging"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-code-snippets.php:334
msgid "This view lists your snippets that threw errors. Some of the snippets may have also been automatically disabled due to potentially preventing you from accessing the admin."
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-code-snippets.php:336
#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:2194
msgid "Learn More"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-code-snippets.php:343
msgid "In order to get more info about the errors please consider enabling error logging."
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-code-snippets.php:364
msgid "Number of snippets per page:"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-code-snippets.php:407
msgid "Order Snippets By"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-code-snippets.php:409
msgid "Order snippets by"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-code-snippets.php:413
#: includes/admin/pages/class-wpcode-code-snippets-table.php:636
#: includes/generator/class-wpcode-generator-post-status.php:128
#: includes/generator/class-wpcode-generator-post-type.php:136
#: includes/generator/class-wpcode-generator-sidebar.php:130
msgid "Name"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-code-snippets.php:414
#: includes/admin/pages/class-wpcode-code-snippets-table.php:639
msgid "Created"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-code-snippets.php:415
#: includes/admin/pages/class-wpcode-code-snippets-table.php:640
msgid "Last Updated"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-code-snippets.php:416
#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:928
#: includes/admin/pages/class-wpcode-code-snippets-table.php:644
#: includes/generator/class-wpcode-generator-hooks.php:1360
msgid "Priority"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-code-snippets.php:422
msgid "Ascending"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-code-snippets.php:423
msgid "Descending"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-file-editor.php:38
msgid "File Editor"
msgstr ""

#. Translators: %s is the name of the file.
#: includes/admin/pages/class-wpcode-admin-page-file-editor.php:81
msgid "%s Editor"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-file-editor.php:91
msgid "Enable file output"
msgstr ""

#. Translators: %s is the name of the file.
#: includes/admin/pages/class-wpcode-admin-page-file-editor.php:107
msgid "Use this area to edit your %s file."
msgstr ""

#. Translators: %1$s is the opening link tag, %2$s is the closing link tag.
#: includes/admin/pages/class-wpcode-admin-page-file-editor.php:116
msgid "The file contents above will be used to generated a dynamic file. There is no physical file created on your server. %1$sLearn more%2$s."
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-file-editor.php:134
#: includes/admin/pages/class-wpcode-admin-page-headers-footers.php:233
#: includes/admin/pages/class-wpcode-admin-page-pixel.php:176
#: includes/admin/pages/class-wpcode-admin-page-pixel.php:253
#: includes/admin/pages/class-wpcode-admin-page-pixel.php:317
#: includes/admin/pages/class-wpcode-admin-page-pixel.php:367
#: includes/admin/pages/class-wpcode-admin-page-pixel.php:427
#: includes/admin/pages/class-wpcode-admin-page-pixel.php:810
#: includes/admin/pages/class-wpcode-admin-page-settings.php:168
#: includes/admin/pages/class-wpcode-admin-page-settings.php:463
#: includes/admin/pages/class-wpcode-admin-page-settings.php:652
msgid "Save Changes"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-file-editor.php:137
msgid "View File"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-file-editor.php:202
msgid "Content added here will be appended to the robots.txt content generated by WordPress and other plugins."
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-file-editor.php:240
msgid "Simplify your website management with the WPCode File Editor! Say goodbye to the hassle of manually editing files on your server."
msgstr ""

#. translators: %1$s and %2$s are <u> tags.
#: includes/admin/pages/class-wpcode-admin-page-file-editor.php:244
msgid "With this powerful tool, you can easily customize crucial files like %1$sads.txt%2$s, %1$sapp-ads.txt%2$s, %1$srobots.txt%2$s, and %1$sservice-worker.js%2$s right from your WordPress admin."
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-file-editor.php:251
msgid "File Editor is a PRO Feature"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-file-editor.php:254
#: includes/admin/pages/class-wpcode-admin-page-pixel.php:74
#: includes/admin/pages/class-wpcode-admin-page-settings.php:496
#: includes/admin/pages/class-wpcode-admin-page-settings.php:674
msgid "Upgrade to WPCode PRO"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-file-editor.php:258
msgid "Learn More about the File Editor"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-file-editor.php:262
msgid "No manual coding, no FTP"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-file-editor.php:263
msgid "Effortless integrations setup"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-file-editor.php:264
msgid "Reduce the number of plugins"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-file-editor.php:265
msgid "Prevent advertising fraud"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-generator.php:66
#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:932
msgid "Generator"
msgstr ""

#. Translators: gets replaced with the snippet title.
#: includes/admin/pages/class-wpcode-admin-page-generator.php:94
msgid "You are now editing the generated snippet: \"%s\". Updating the snippet will override any edits you made to the code."
msgstr ""

#. Translators: gets replaced with the generator name.
#: includes/admin/pages/class-wpcode-admin-page-generator.php:107
msgid "%s Generator"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-generator.php:133
msgid "All Generators"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-generator.php:133
msgid "Search Generators"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-generator.php:148
msgid "Generate"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-generator.php:200
msgid "Update code"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-generator.php:210
#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:423
#: includes/admin/pages/class-wpcode-admin-page.php:1112
msgid "Code Preview"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-generator.php:214
msgid "Update Snippet"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-generator.php:216
#: includes/admin/pages/class-wpcode-admin-page.php:1117
msgid "Use Snippet"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-generator.php:219
msgctxt "Copy to clipboard"
msgid "Copy Code"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-generator.php:251
#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:1518
#: includes/generator/class-wpcode-generator-type.php:212
msgid "Remove Row"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-headers-footers.php:57
msgid "Header & Footer"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-headers-footers.php:109
msgid "Headers & Footers mode activated. Use the toggle next to the Save Changes button to disable it at any time."
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-headers-footers.php:110
msgid "Headers & Footers mode deactivated, if you wish to switch back please use the option on the settings page."
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-headers-footers.php:128
#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:181
msgid "Sorry, you only have read-only access to this page. Ask your administrator for assistance editing."
msgstr ""

#. translators: %s: The `<head>` tag
#: includes/admin/pages/class-wpcode-admin-page-headers-footers.php:154
msgid "These scripts will be printed in the %s section."
msgstr ""

#. translators: %s: The `<head>` tag
#: includes/admin/pages/class-wpcode-admin-page-headers-footers.php:159
msgid "These scripts will be printed just below the opening %s tag."
msgstr ""

#. translators: %s: The `</body>` tag
#: includes/admin/pages/class-wpcode-admin-page-headers-footers.php:164
msgid "These scripts will be printed above the closing %s tag."
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-headers-footers.php:228
msgid "Global Header and Footer"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-headers-footers.php:251
msgid "Simple mode"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-headers-footers.php:310
msgid "Settings Saved. Please don't forget to clear the site cache if you are using a cache plugin, so that the changes will be reflected for all users."
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-headers-footers.php:335
#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:2049
#: includes/lite/admin/class-wpcode-metabox-snippets-lite.php:313
msgid "Code Revisions is a Pro Feature"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-headers-footers.php:338
msgid "Upgrade to WPCode Pro today and start tracking revisions and see exactly who, when and which changes were made to global Headers & Footers scripts."
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-headers-footers.php:341
#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:2055
#: includes/lite/admin/class-wpcode-metabox-snippets-lite.php:319
msgid "Upgrade to Pro and Unlock Revisions"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-headers-footers.php:345
#: includes/admin/pages/class-wpcode-admin-page-library.php:209
#: includes/admin/pages/class-wpcode-admin-page-library.php:239
#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:2059
#: includes/lite/admin/class-wpcode-metabox-snippets-lite.php:150
#: includes/lite/admin/class-wpcode-metabox-snippets-lite.php:293
#: includes/lite/admin/class-wpcode-metabox-snippets-lite.php:323
msgid "Learn more about all the features"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-headers-footers.php:351
#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:1964
msgid "Code Revisions"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-headers-footers.php:353
msgid "Easily switch back to a previous version of your global scripts."
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-library.php:51
msgid "Library"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-library.php:62
msgid "Snippets"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-library.php:63
msgid "My Library"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-library.php:64
msgid "My Favorites"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-library.php:180
msgid "We encountered an error while trying to load the snippet data. Please try again."
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-library.php:202
msgid "My Library is a PRO Feature"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-library.php:203
msgid "Upgrade to WPCode PRO today and save your snippets in the WPCode Library directly from the plugin and import them with 1-click on other sites."
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-library.php:205
msgid "Upgrade to PRO and Unlock \"My Library\""
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-library.php:213
#: includes/admin/pages/class-wpcode-admin-page-library.php:245
msgid "Save your snippets to the WPCode Library"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-library.php:214
#: includes/admin/pages/class-wpcode-admin-page-library.php:244
msgid "Import snippets from the WPCode Library"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-library.php:215
#: includes/admin/pages/class-wpcode-admin-page-library.php:246
msgid "Set up new websites faster"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-library.php:216
#: includes/admin/pages/class-wpcode-admin-page-library.php:247
msgid "Easily implement features on multiple sites"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-library.php:217
#: includes/admin/pages/class-wpcode-admin-page-library.php:248
msgid "Edit snippets in the WPCode Library"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-library.php:218
#: includes/admin/pages/class-wpcode-admin-page-library.php:243
msgid "Load favorite snippets in the plugin"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-library.php:232
msgid "My Favorites is a PRO Feature"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-library.php:233
msgid "Upgrade to WPCode PRO today and see the snippets you starred in the WPCode Library directly in the plugin."
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-library.php:235
msgid "Upgrade to PRO and Unlock \"My Favorites\""
msgstr ""

#. translators: %1$s and %2$s are <u> tags.
#: includes/admin/pages/class-wpcode-admin-page-pixel.php:65
msgid "While you can always add pixels manually using code snippets, our Conversion Pixels addon helps you %1$ssave time%2$s while %1$sreducing errors%2$s. It lets you properly implement Facebook, Google, Pinterest, TikTok and Snapchat ads tracking with deep integrations for eCommerce events, interaction measurement, and more. This addon is available on WPCode Plus plan or higher."
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-pixel.php:71
msgid "Conversion Pixels Addon is a PRO Feature"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-pixel.php:79
msgid "Seamless integration with WooCommerce, Easy Digital Downloads and MemberPress"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-pixel.php:80
msgid "Works with Facebook, Google Ads, Pinterest, TikTok and Snapchat"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-pixel.php:81
msgid "No coding required"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-pixel.php:82
msgid "1-click setup for conversion tracking"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-pixel.php:94
msgid "Facebook"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-pixel.php:95
msgid "Google"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-pixel.php:96
msgid "Pinterest"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-pixel.php:97
msgid "TikTok"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-pixel.php:98
msgid "Snapchat"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-pixel.php:99
msgid "Click Tracking"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-pixel.php:130
msgid "Facebook Pixel"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-pixel.php:133
msgid "Facebook Pixel ID"
msgstr ""

#. translators: %1$s and %2$s are the opening and closing anchor tags.
#: includes/admin/pages/class-wpcode-admin-page-pixel.php:139
msgid "You can find your Facebook Pixel ID in the Facebook Ads Manager. %1$sRead our step by step directions%2$s. "
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-pixel.php:147
#: includes/admin/pages/class-wpcode-admin-page-pixel.php:397
msgid "Conversions API Token"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-pixel.php:151
#: includes/admin/pages/class-wpcode-admin-page-pixel.php:401
msgid "The Conversions API token allows you to send API events that are more reliable and can improve audience targeting."
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-pixel.php:157
msgid "Facebook Pixel Events"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-pixel.php:161
#: includes/admin/pages/class-wpcode-admin-page-pixel.php:237
msgid "PageView Event"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-pixel.php:163
msgid "Enable the PageView event to track and record page visits on all the pages of your website using the Facebook Pixel."
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-pixel.php:170
#: includes/admin/pages/class-wpcode-admin-page-pixel.php:247
#: includes/admin/pages/class-wpcode-admin-page-pixel.php:311
#: includes/admin/pages/class-wpcode-admin-page-pixel.php:361
#: includes/admin/pages/class-wpcode-admin-page-pixel.php:421
msgid "eCommerce Events Tracking"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-pixel.php:188
msgid "Google Analytics & Ads Tracking"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-pixel.php:191
msgid "Google Analytics ID"
msgstr ""

#. translators: %1$s and %2$s are the opening and closing anchor tags.
#: includes/admin/pages/class-wpcode-admin-page-pixel.php:197
msgid "You can find your Google Analytics ID in the Google Analytics Admin panel. %1$sRead our step by step directions%2$s. "
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-pixel.php:205
msgid "Google Ads Tag ID"
msgstr ""

#. translators: %1$s and %2$s are the opening and closing anchor tags.
#: includes/admin/pages/class-wpcode-admin-page-pixel.php:211
msgid "You can find your Google Ads Tag ID in the Google Ads Settings under Google Tag. %1$sRead our step by step directions%2$s. "
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-pixel.php:219
msgid "Ads Conversion Label"
msgstr ""

#. translators: %1$s and %2$s are the opening and closing anchor tags.
#: includes/admin/pages/class-wpcode-admin-page-pixel.php:225
msgid "Add your Google Ads Conversion Label for tracking conversion events. %1$sLearn More%2$s."
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-pixel.php:233
msgid "Google Events"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-pixel.php:239
msgid "Enable PageView event on all pages."
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-pixel.php:265
msgid "Pinterest Tag"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-pixel.php:268
msgid "Tag ID"
msgstr ""

#. translators: %1$s and %2$s are the opening and closing anchor tags.
#: includes/admin/pages/class-wpcode-admin-page-pixel.php:274
msgid "You can find your Tag id in your Pinterest Business account. %1$sRead our step by step directions%2$s. "
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-pixel.php:282
msgid "Ad Account ID"
msgstr ""

#. translators: %1$s and %2$s are the opening and closing anchor tags.
#: includes/admin/pages/class-wpcode-admin-page-pixel.php:288
msgid "You can find your Ad Account ID in your Pinterest Business account. %1$sRead more%2$s. "
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-pixel.php:296
msgid "Conversion Access Token"
msgstr ""

#. translators: %1$s and %2$s are the opening and closing anchor tags.
#: includes/admin/pages/class-wpcode-admin-page-pixel.php:302
msgid "You can find your Conversion Access Token under Ads > Conversions > Conversion access token. %1$sRead more%2$s. "
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-pixel.php:329
msgid "TikTok Pixel"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-pixel.php:332
msgid "Pixel ID"
msgstr ""

#. translators: %1$s and %2$s are the opening and closing anchor tags.
#: includes/admin/pages/class-wpcode-admin-page-pixel.php:338
msgid "You can find your Pixel id in your TikTok Business Account. %1$sRead our step by step directions%2$s."
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-pixel.php:346
msgid "Events API Access Token"
msgstr ""

#. translators: %1$s and %2$s are the opening and closing anchor tags.
#: includes/admin/pages/class-wpcode-admin-page-pixel.php:352
msgid "You can generate an access token in the Pixel Settings under Access Token Generation. %1$sRead more%2$s"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-pixel.php:379
msgid "Snapchat Pixel"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-pixel.php:382
msgid "Snap Pixel ID"
msgstr ""

#. translators: %1$s and %2$s are the opening and closing anchor tags.
#: includes/admin/pages/class-wpcode-admin-page-pixel.php:388
msgid "You can find your Snapchat Pixel ID in the Snapchat Ads Manager. %1$sRead our step by step directions%2$s. "
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-pixel.php:407
msgid "Snapchat Pixel Events"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-pixel.php:411
msgid "Pave View Event"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-pixel.php:413
msgid "Enable the PAGE_VIEW event to track and record page visits on all the pages of your website using the Snapchat Pixel."
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-pixel.php:440
msgid "ViewContent Event"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-pixel.php:442
#: includes/admin/pages/class-wpcode-admin-page-pixel.php:563
msgid "Turn on the \"ViewContent\" event to track views of product pages on your website."
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-pixel.php:447
msgid "AddtoCart Event"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-pixel.php:449
#: includes/admin/pages/class-wpcode-admin-page-pixel.php:570
msgid "Turn on the \"AddToCart\" event to track when items are added to a shopping cart on your website."
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-pixel.php:454
#: includes/admin/pages/class-wpcode-admin-page-pixel.php:575
msgid "InitiateCheckout Event"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-pixel.php:456
#: includes/admin/pages/class-wpcode-admin-page-pixel.php:577
msgid "Turn on the \"InitiateCheckout\" event to track when a user reaches the checkout page on your website."
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-pixel.php:461
#: includes/admin/pages/class-wpcode-admin-page-pixel.php:499
#: includes/admin/pages/class-wpcode-admin-page-pixel.php:708
msgid "Purchase Event"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-pixel.php:463
msgid "Turn on the \"Purchase\" event to track successful purchases on your website."
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-pixel.php:478
msgid "View Item Event"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-pixel.php:480
msgid "Send the View Item event to track views of product pages on your website."
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-pixel.php:485
#: includes/admin/pages/class-wpcode-admin-page-pixel.php:530
#: includes/admin/pages/class-wpcode-admin-page-pixel.php:568
#: includes/admin/pages/class-wpcode-admin-page-pixel.php:694
msgid "Add to Cart Event"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-pixel.php:487
msgid "Send the Add to Cart event when a product is added to the cart."
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-pixel.php:492
msgid "Begin Checkout Event"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-pixel.php:494
msgid "Send the Begin Checkout event when the user sees the checkout page."
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-pixel.php:501
msgid "Send the Purchase event when the user completes a purchase."
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-pixel.php:506
msgid "Conversion Event"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-pixel.php:508
msgid "Send the conversion event with the Google Ads label set above on a successful purchase."
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-pixel.php:523
msgid "PageVisit Product Event"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-pixel.php:525
msgid "Turn on the \"PageVisit\" event to track views of product pages on your website."
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-pixel.php:532
msgid "Turn on the Add to Cart event to track when items are added to a shopping cart on your website."
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-pixel.php:537
msgid "Checkout PageVisit Event"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-pixel.php:539
msgid "Enable the Checkout PageVisit event to track when a user reaches the checkout page on your website."
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-pixel.php:544
msgid "Checkout Event"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-pixel.php:546
msgid "Turn on the \"Checkout\" event to track successful purchases on your website."
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-pixel.php:561
msgid "ViewContent Product Event"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-pixel.php:582
msgid "PlaceAnOrder Event"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-pixel.php:584
msgid "Turn on the \"PlaceAnOrder\" event to track successful purchases on your website."
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-pixel.php:660
msgid "Disabled, no eCommerce Platform Detected"
msgstr ""

#. translators: %s is the name of the eCommerce provider.
#: includes/admin/pages/class-wpcode-admin-page-pixel.php:664
msgid "%s Tracking Enabled"
msgstr ""

#. translators: %s a html break.
#: includes/admin/pages/class-wpcode-admin-page-pixel.php:670
msgid "Advanced eCommerce tracking is available for WooCommerce, Easy Digital Downloads and MemberPress. %s These plugins are detected automatically and when available you can toggle individual events using the options below."
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-pixel.php:687
msgid "View Content Event"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-pixel.php:689
msgid "Turn on the \"VIEW_CONTENT\" event to track views of product pages on your website."
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-pixel.php:696
msgid "Turn on the \"ADD_CART\" event to track when items are added to a shopping cart on your website."
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-pixel.php:701
msgid "Start Checkout Event"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-pixel.php:703
msgid "Turn on the \"START_CHECKOUT\" event to track when a user reaches the checkout page on your website."
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-pixel.php:710
msgid "Turn on the \"PURCHASE\" event to track successful purchases on your website."
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-pixel.php:724
msgid "Custom Click Tracking"
msgstr ""

#. Translators: %1$s is the opening link tag, %2$s is the closing link tag
#: includes/admin/pages/class-wpcode-admin-page-pixel.php:729
msgid "Use this section to add custom click events to your site. You can add as many as you like. Each event can have multiple pixels and each pixel can have a custom event name and value. Read more about how to configure these settings in %1$sthis article%2$s"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-pixel.php:739
msgid "CSS Selector"
msgstr ""

#. Translators: %1$s is an opening anchor tag, %2$s is a closing anchor tag.
#: includes/admin/pages/class-wpcode-admin-page-pixel.php:745
msgid "Define the HTML element that triggers the event upon clicking (button, link, etc). Input the appropriate CSS selector here. %1$sLearn more%2$s"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-pixel.php:753
msgid "Event Name"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-pixel.php:757
msgid "Assign a unique identifier to your event for easy recognition and categorization. "
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-pixel.php:761
msgid "Event Value"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-pixel.php:765
msgid "Input a numerical value for your event. This helps in quantifying user interactions for your tracking needs. "
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-pixel.php:804
msgid "Add New Click Event"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-search-replace.php:87
msgid "Confidently replace any text in your database and update images without duplicating uploads, all with a single plugin."
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-search-replace.php:92
msgid "Search & Replace Everything Screenshot"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-search-replace.php:98
msgid "Replace Text Across Your Whole Database."
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-search-replace.php:99
msgid "Preview Changes Every Time."
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-search-replace.php:100
msgid "Replace Image Sources to Clear Up Space."
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-search-replace.php:101
msgid "Support for Serialized Data."
msgstr ""

#. translators: %s is the plugin name.
#: includes/admin/pages/class-wpcode-admin-page-search-replace.php:114
msgid "Install and Activate %s"
msgstr ""

#. translators: %s is the plugin name.
#: includes/admin/pages/class-wpcode-admin-page-search-replace.php:123
msgid "Install %s from the WordPress.org plugin repository."
msgstr ""

#. translators: %s is the plugin name.
#: includes/admin/pages/class-wpcode-admin-page-search-replace.php:136
msgid "Install %s"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-search-replace.php:145
msgid "Please ask your website administrator to install Search & Replace Everything."
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-settings.php:52
msgid "WPCode Settings"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-settings.php:64
msgid "General Settings"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-settings.php:66
#: includes/admin/pages/class-wpcode-admin-page-settings.php:624
msgid "Access Control"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-settings.php:88
msgid "Settings Saved."
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-settings.php:125
msgid "This allows you to disable all Code Snippets functionality and have a single \"Headers & Footers\" item under the settings menu."
msgstr ""

#. Translators: Placeholders make the text bold.
#: includes/admin/pages/class-wpcode-admin-page-settings.php:130
msgid "%1$sNOTE:%2$s Please use this setting with caution. It will disable all custom snippets that you add using the new snippet management interface."
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-settings.php:136
msgid "License Key"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-settings.php:141
msgid "Headers & Footers mode"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-settings.php:153
msgid "Allow Usage Tracking"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-settings.php:157
msgid "By allowing us to track usage data, we can better help you, as we will know which WordPress configurations, themes, and plugins we should test."
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-settings.php:179
msgid "Remove ALL WPCode data upon plugin deletion."
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-settings.php:184
msgid "All WPCode snippets & scripts will be unrecoverable."
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-settings.php:188
msgid "Delete All Data on Uninstall"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-settings.php:206
msgid "WPCode Library Connection"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-settings.php:211
msgid "Editor Height"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-settings.php:217
msgid "Dark Mode"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-settings.php:221
msgid "Enable Dark Mode across WPCode."
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-settings.php:228
msgid "Admin Bar Info"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-settings.php:232
msgid "Enable the admin bar menu that shows info about which snippets & scripts are loaded on the current page."
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-settings.php:248
msgid "Connect to the WPCode Library"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-settings.php:251
msgid "Disconnect from the WPCode Library"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-settings.php:376
msgid "Auto height"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-settings.php:379
msgid "Set the editor height in pixels or enable auto-grow so the code editor automatically grows in height with the code."
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-settings.php:394
msgid "You're using WPCode Lite - no license needed. Enjoy!"
msgstr ""

#. Translators: %1$s - Opening anchor tag, do not translate. %2$s - Closing anchor tag, do not translate.
#: includes/admin/pages/class-wpcode-admin-page-settings.php:401
msgid "To unlock more features consider %1$supgrading to PRO%2$s."
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-settings.php:408
msgid "Already purchased? Simply enter your license key below to enable WPCode PRO!"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-settings.php:410
msgid "Paste license key here"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-settings.php:412
msgid "Verify Key"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-settings.php:429
msgid "Oops!"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-settings.php:431
msgid "Almost Done"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-settings.php:432
#: includes/admin/pages/class-wpcode-code-snippets-table.php:444
msgid "Activate"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-settings.php:433
msgid "Unfortunately there was a server connection error."
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-settings.php:478
msgid "Email Notifications"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-settings.php:481
msgid "Receive email notifications when snippets throw errors or are automatically deactivated."
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-settings.php:493
msgid "Email Notifications is a Pro Feature"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-settings.php:494
msgid "Do you want to get email notifications the moment your snippets throw an error or are automatically deactivated? Upgrade today and improve your workflow with WPCode Error Email Notifications."
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-settings.php:512
msgid "Error Notifications"
msgstr ""

#. Translators: %1$s: opening anchor tag, %2$s: closing anchor tag. Link to docs about error notifications.
#: includes/admin/pages/class-wpcode-admin-page-settings.php:518
msgid "Send email notifications when snippets throw errors? %1$sLearn more%2$s"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-settings.php:528
#: includes/admin/pages/class-wpcode-admin-page-settings.php:569
msgid "Send To"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-settings.php:532
msgid "Enter a comma separated list of email addresses to receive error notifications. Defaults to the admin email address."
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-settings.php:548
#: includes/admin/pages/class-wpcode-admin-page-settings.php:589
msgid "Preview Email"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-settings.php:553
msgid "Deactivation Notifications"
msgstr ""

#. Translators: %1$s: opening anchor tag, %2$s: closing anchor tag. Link to docs about error notifications.
#: includes/admin/pages/class-wpcode-admin-page-settings.php:559
msgid "Send an email when a snippet gets automatically deactivated? %1$sLearn more%2$s"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-settings.php:573
msgid "Enter a comma separated list of email addresses to receive deactivation notifications. Defaults to the admin email address."
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-settings.php:601
msgid "Error Logging"
msgstr ""

#. Translators: %1$s: opening anchor tag, %2$s: closing anchor tag.
#: includes/admin/pages/class-wpcode-admin-page-settings.php:607
msgid "Log errors thrown by snippets? %1$sView Logs%2$s"
msgstr ""

#. Translators: %1$s - Opening anchor tag. %2$s - Closing anchor tag.
#: includes/admin/pages/class-wpcode-admin-page-settings.php:629
msgid "Select the user roles that are allowed to manage different types of snippets or parts of WPCode. By default, all permissions are provided only to administrator users. Please see our %1$sAccess Control documentation%2$s for more details."
msgstr ""

#. translators: %1$s and %2$s are <u> tags.
#: includes/admin/pages/class-wpcode-admin-page-settings.php:665
msgid "Improve the way you and your team manage your snippets with the WPCode Access Control settings. Enable other users on your site to manage different types of snippets or configure Conversion Pixels settings and update configuration files. This feature is available on the %1$sWPCode Pro%2$s plan or higher."
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-settings.php:671
msgid "Access Control is a PRO Feature"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-settings.php:679
msgid "Save time and improve website management with your team"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-settings.php:680
msgid "Delegate snippet management to other users with full control"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-settings.php:681
msgid "Enable other users to set up ads & 3rd party services"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-settings.php:682
msgid "Choose if PHP snippets should be enabled on the site"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-settings.php:697
msgid "Disable PHP snippets"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-settings.php:701
msgid "This option will completely disable PHP snippets execution and the option to edit or add new PHP snippets."
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-settings.php:768
msgid "Make Sure Important Emails Reach Your Inbox"
msgstr ""

#. Translators: %1$s: opening anchor tag, %2$s: closing anchor tag.
#: includes/admin/pages/class-wpcode-admin-page-settings.php:773
msgid "Solve common email deliverability issues for good. %1$sGet WP Mail SMTP%2$s!"
msgstr ""

#. Translators: This adds the name of the plugin "WPCode".
#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:84
msgid "Add %s Snippet"
msgstr ""

#. Translators: This adds the name of the plugin "WPCode".
#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:85
#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:138
msgid "Add Snippet"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:131
msgid "Save Snippet"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:132
msgid "Create Custom Snippet"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:134
msgid "Edit Snippet"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:135
msgid "Update"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:167
msgid "You cannot edit this snippet because it is in the Trash. Please restore it and try again."
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:193
msgid "Snippet updated."
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:194
msgid "Snippet created & Saved."
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:195
msgid "We encountered an error activating your snippet, please check the syntax and try again."
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:196
msgid "Sorry, you are not allowed to change the status of the snippet."
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:197
msgid "Snippet updated & executed."
msgstr ""

#. translators: %s: Error message.
#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:209
msgid "Error message: %s"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:243
msgid "Don't forget to activate your snippet using the toggle next to the \"Update\" button when you are ready to start using it."
msgstr ""

#. Translators: this changes the edit page title to show the snippet title.
#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:277
msgid "Edit snippet \"%s\""
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:324
msgid "Add Your Custom Code (New Snippet)"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:325
msgid "Choose this blank snippet to start from scratch and paste any custom code or simply write your own."
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:337
msgid "Add Custom Snippet"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:341
msgid "Generate snippet using AI"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:342
msgid "Generate a new snippet specific to your needs leveraging the power of WPCode's AI integration."
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:350
msgid "Generate Snippet"
msgstr ""

#. Translators: The placeholders add links to create a new custom snippet or the suggest-a-snippet form.
#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:383
msgid "To speed up the process you can select from one of our pre-made library, or you can start with a %1$sblank snippet%2$s and %1$screate your own%2$s. Have a suggestion for new snippet? %3$sWe’d love to hear it!%4$s"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:406
msgid "Add title for snippet"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:481
msgid "Insertion"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:487
msgid "Choose \"Auto Insert\" if you want the snippet to be automatically executed in one of the locations available. In \"Shortcode\" mode, the snippet will only be executed where the shortcode is inserted."
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:490
msgid "Insert Method"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:492
#: includes/admin/pages/class-wpcode-code-snippets-table.php:638
#: includes/lite/conditional-logic/class-wpcode-conditional-location.php:70
msgid "Location"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:496
#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:554
#: includes/admin/pages/class-wpcode-code-snippets-table.php:642
#: includes/class-wpcode-admin-bar-info.php:283
msgid "Shortcode"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:513
msgid "Your snippet can be either automatically executed or only used as a shortcode. When using the \"Auto Insert\" option you can choose the location where your snippet will be placed automatically."
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:524
msgid "before paragraph number"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:525
msgid "after paragraph number"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:526
msgid "before post number"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:527
msgid "after post number"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:528
msgid "minimum number of words"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:529
msgid "number of words"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:550
msgid "Auto&nbsp;Insert"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:598
msgid "Who (visitor)"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:602
msgid "Where (page)"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:606
#: includes/class-wpcode-auto-insert.php:61
msgid "eCommerce"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:610
msgid "Advanced"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:620
msgid "Search Options"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:636
#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:700
msgid "Selected"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:692
msgid "Search locations"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:853
msgid "Please save the snippet first"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:927
msgid "Tag"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:929
msgid "Note"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:936
msgid "Basic info"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:938
msgid "Tags: Use tags to make it easier to group similar snippets together. <br />Priority: A lower priority will result in the snippet being executed before others with a higher priority. <br />Note: Add a private note related to this snippet."
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:950
msgid "Using conditional logic you can limit the pages where you want the snippet to be auto-inserted."
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:954
msgid "Enable Logic"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:955
msgid "Conditions"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:960
msgid "Smart Conditional Logic"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:962
msgid "Enable logic to add rules and limit where your snippets are inserted automatically. Use multiple groups for different sets of rules."
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:1004
msgid "Update Generated Snippet"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:1075
msgid "Snippet Status:"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:1078
#: includes/admin/pages/class-wpcode-admin-page-tools.php:369
#: includes/admin/pages/class-wpcode-code-snippets-table.php:966
msgid "Active"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:1081
#: includes/admin/pages/class-wpcode-admin-page-tools.php:370
#: includes/admin/pages/class-wpcode-code-snippets-table.php:969
msgid "Inactive"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:1094
msgid "Execute Snippet Now"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:1124
msgid "Save to Library"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:1148
msgid "Restricted Code Detected"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:1327
msgid "+ Add new group"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:1344
msgid "Show"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:1345
msgid "Hide"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:1361
msgid "This code snippet if"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:1465
msgctxt "Conditional logic add another \"and\" rules row."
msgid "AND"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:1466
msgid "Add another \"AND\" rules row."
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:1551
msgctxt "Conditional logic \"or\" another rule"
msgid "OR"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:1572
msgid "Save to Library is a Pro Feature"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:1573
msgid "Upgrade to PRO today and save your private snippets to the WPCode library for easy access. You can also share your snippets with other users or load them on other sites."
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:1575
msgid "Custom Shortcode is a Pro Feature"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:1576
msgid "Upgrade today to use a custom shortcode and nerver worry about changing snippet ids again, even when importing your snippets to another site. You'll also get access to a private library that makes setting up new sites a lot easier."
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:1578
msgid "Device Type is a Pro Feature"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:1579
msgid "Upgrade to PRO today and unlock one-click device targeting for your snippets."
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:1581
msgid "Scheduling snippets is a Pro Feature"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:1582
msgid "Upgrade to PRO today and unlock powerful scheduling options to limit when your snippet is active on the site."
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:1584
msgid "Blocks snippets is a Pro Feature"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:1585
msgid "Upgrade to PRO today and unlock building snippets using the Gutenberg Block Editor. Create templates using blocks and use the full power of WPCode to insert them in your site."
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:1588
#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:1747
msgid "Shortcode Attributes"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:1589
msgid "Load as file is a Pro Feature"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:1590
msgid "Upgrade to PRO today and unlock loading your CSS and JS snippets as files for better performance and improved compatibility with caching plugins."
msgstr ""

#. Translators: %1$s Opening anchor tag. %2$s Closing anchor tag.
#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:1594
msgid "For better results using conditional logic with PHP snippets we automatically switched the auto-insert location to \"Frontend Conditional Logic\" that runs later. If you want to run the snippet earlier please switch back to \"Run Everywhere\" but note not all conditional logic options will be available. %1$sRead more%2$s"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:1610
msgid "The snippet has been recently deactivated due to an error on this line"
msgstr ""

#. Translators: The name of the user that is currently editing the snippet is appended at the end.
#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:1614
msgid "This snippet is currently being edited by "
msgstr ""

#. Translators: The name of the user that is currently editing the snippet is appended at the end.
#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:1615
msgid "AI Snippet Generation is a Pro Feature"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:1616
msgid "Improving Snippets with AI is a Pro Feature"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:1617
msgid "Upgrade today to access the WPCode AI code generator, which allows you to create code snippets simply by describing their functionality using the power of AI."
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:1699
#: includes/class-wpcode-admin-bar-info.php:289
msgid "Custom Shortcode"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:1702
msgid "Shortcode name"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:1707
msgid "Use this field to define a custom shortcode name instead of the id-based one."
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:1721
msgid "Add&nbsp;Attribute"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:1725
msgid "Attribute name"
msgstr ""

#. Translators: %1$s is the opening <code> tag, %2$s is the closing </code> tag.
#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:1732
msgid "Use this field to define the attribute name for your shortcode and click Add Attribute. Attributes added here will be available to use as smart tags and as variables inside snippets. E.g. an attribute named \"keyword\" will be available in a PHP snippet as %1$s$keyword%2$s. %3$sLearn more%4$s."
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:1802
msgid "Schedule snippet"
msgstr ""

#. Translators: Link to the documentation article for files as snippets. %1$s is the opening anchor tag, %2$s is the closing anchor tag.
#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:1825
msgid "If enabled, this snippet will be loaded as a physical file instead of being inserted in the source of the page. %1$sLearn more%2$s."
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:1838
msgid "Load as file"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:1867
#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:1868
msgid "Start Date"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:1869
msgid "Clear start date"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:1876
#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:1877
msgid "End Date"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:1878
msgid "Clear end date"
msgstr ""

#. Translators: %1$s and %2$s are HTML tags for a link to the documentation article.
#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:1887
msgid "Looking for more scheduling options? %1$sClick here%2$s to read more about all the available options."
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:1959
msgid "As you make changes to your snippet and save, you will get a list of previous versions with all the changes made in each revision. You can compare revisions to the current version or see changes as they have been saved by going through each revision. Any of the revisions can then be restored as needed."
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:1966
msgid "Easily switch back to a previous version of your snippet."
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:1978
msgid "Limit where you want this snippet to be loaded by device type. By default, snippets are loaded on all devices."
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:1984
#: includes/lite/conditional-logic/class-wpcode-conditional-device.php:48
msgid "Device Type"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:1998
msgid "Any device type"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:1999
msgid "Desktop only"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:2000
msgid "Mobile only"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:2052
msgid "Upgrade to WPCode Pro today and start tracking revisions and see exactly who, when and which changes were made to your snippet."
msgstr ""

#. Translators: The placeholder gets replaced with the display name of the user currently editing the snippet.
#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:2080
msgid "Notice: %1$s is also editing this snippet. Please be aware that your changes could be overwritten."
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:2094
msgid "View Error Logs"
msgstr ""

#. Translators: The placeholder gets replaced with the time passed since the snippet was deactivated.
#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:2119
msgid "This snippet was automatically deactivated due to an error at %1$s on %2$s (%3$s ago)."
msgstr ""

#. Translators: The placeholder gets replaced with the time passed since the snippet was deactivated.
#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:2131
msgid "This snippet first threw an error at %1$s on %2$s (%3$s ago)."
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:2142
#: includes/class-wpcode-snippet-execute.php:500
msgid "Error message:"
msgstr ""

#. Translators: The placeholders make the text bold and add the line number.
#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:2151
msgid "The error occurred on %1$sline %3$d%2$s of this snippet's code (highlighted below)."
msgstr ""

#. Translators: The placeholder is replaced with the URL where the error happened.
#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:2165
msgid "The error was triggered at the following URL: %1$s"
msgstr ""

#. Translators: The placeholders are for the link to the error logs.
#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:2176
msgid "You can %1$sview the error log%2$s to get more details about the error that caused this. The error will also be in a snippet-specific log whose name starts with snippet-%3$d (the id of this snippet)."
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:2182
msgid "You can enable error logging to get more details about the error that caused this."
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:2187
msgid "This message will disappear when the snippet is updated."
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:2237
msgid "Use AI to improve your snippet by describing the changes you want"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-tools.php:59
msgid "Tools"
msgstr ""

#. Translators: Adds a link to the snippets list in the admin.
#: includes/admin/pages/class-wpcode-admin-page-tools.php:94
msgid "Import was successfully finished. You can go and check %1$syour snippets%2$s."
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-tools.php:140
msgid "WPCode Snippet Import"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-tools.php:141
msgid "Select a WPCode export file"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-tools.php:147
msgid "No file chosen"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-tools.php:151
msgid "Choose a file&hellip;"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-tools.php:160
#: includes/admin/pages/class-wpcode-admin-page-tools.php:193
#: includes/admin/pages/class-wpcode-admin-page-tools.php:315
#: includes/admin/pages/class-wpcode-admin-page-tools.php:491
msgid "Import"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-tools.php:166
msgid "Import from Other Code Plugins"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-tools.php:167
msgid "WPCode makes it easy for you to switch by allowing you import your third-party snippet plugins with a single click."
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-tools.php:170
msgid "Select previous Code plugin"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-tools.php:176
msgid "Not Installed"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-tools.php:178
msgid "Not Active"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-tools.php:208
msgid "Export Code Snippets"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-tools.php:212
#: includes/admin/pages/class-wpcode-code-snippets-table.php:647
#: includes/generator/class-wpcode-generator-post-status.php:111
msgid "Status"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-tools.php:217
msgid "Code type"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-tools.php:222
#: includes/admin/pages/class-wpcode-code-snippets-table.php:641
msgid "Tags"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-tools.php:229
msgid "Export Snippets"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-tools.php:243
msgid "System Information"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-tools.php:248
msgid "Test SSL Connections"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-tools.php:249
msgid "Click the button below to verify your web server can perform SSL connections successfully."
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-tools.php:251
msgid "Test Connection"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-tools.php:274
msgid "Snippets import"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-tools.php:278
msgid "Select the Snippets you would like to import."
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-tools.php:282
#: includes/admin/pages/class-wpcode-admin-page.php:1018
msgid "Available Snippets"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-tools.php:287
#: includes/admin/pages/class-wpcode-code-snippets-table.php:459
msgid "No snippets found."
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-tools.php:301
msgid "Select All"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-tools.php:306
msgid "Snippets to Import"
msgstr ""

#. Translators: These add markup to display which snippet out of the total from the provider name.
#: includes/admin/pages/class-wpcode-admin-page-tools.php:327
msgid "Importing %1$s of %2$s snippets from %3$s."
msgstr ""

#. Translators: this adds the total snippets count that have been completed.
#: includes/admin/pages/class-wpcode-admin-page-tools.php:345
msgid "Congrats, the import process has finished! We have successfully imported %s snippets. You can review the results below."
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-tools.php:368
#: includes/admin/pages/class-wpcode-code-snippets-table.php:963
msgid "All"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-tools.php:402
msgid "No snippets available to export."
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-tools.php:427
msgid "No tags available."
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-tools.php:492
msgid "Export"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-tools.php:493
msgid "System Info"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-tools.php:494
msgid "Importer"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-tools.php:495
#: includes/class-wpcode-admin-bar-info.php:422
msgid "Logs"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-tools.php:606
msgid "Please upload a valid .json snippets export file."
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-tools.php:607
#: includes/admin/pages/class-wpcode-admin-page-tools.php:620
msgid "Error"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-tools.php:619
msgid "Snippets data cannot be imported."
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-tools.php:680
#: includes/admin/pages/class-wpcode-code-snippets-table.php:382
msgid "Edit"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-tools.php:696
msgid "Testing"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-tools.php:968
msgid "You do not have sufficient permissions to view logs."
msgstr ""

#. translators: %1$s: opening anchor tag, %2$s: closing anchor tag.
#: includes/admin/pages/class-wpcode-admin-page-tools.php:979
msgid "No logs found. You can enable logging from the %1$ssettings panel%2$s."
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-tools.php:1017
msgid "Delete log"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-tools.php:1027
msgid "View"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-tools.php:1035
msgid "Log is empty."
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-tools.php:1055
msgid "Link expired. Please refresh the page and retry."
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-tools.php:1059
msgid "You do not have sufficient permissions to delete logs."
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page-tools.php:1078
msgid "Are you sure you want to delete this log?"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page.php:119
msgid "You do not have permission to access this page."
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page.php:258
msgid "Search docs"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page.php:262
#: includes/admin/pages/class-wpcode-code-snippets-table.php:868
msgid "Clear"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page.php:269
msgid "No docs found"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page.php:284
msgid "View Documentation"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page.php:285
msgid "Browse documentation, reference material, and tutorials for WPCode."
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page.php:286
msgid "View All Documentation"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page.php:290
msgid "Get Support"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page.php:291
msgid "Submit a ticket and our world class support team will be in touch soon."
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page.php:292
msgid "Submit a Support Ticket"
msgstr ""

#. Translators: Placeholder for the number of active notifications.
#: includes/admin/pages/class-wpcode-admin-page.php:304
msgid "New Notifications (%s)"
msgstr ""

#. Translators: Placeholder for the number of dismissed notifications.
#: includes/admin/pages/class-wpcode-admin-page.php:315
msgid "Notifications (%s)"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page.php:322
msgid "Dismissed Notifications"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page.php:325
msgid "Active Notifications"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page.php:348
msgid "Dismiss all"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page.php:390
msgid "Dismiss"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page.php:439
msgid "Testing Mode"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page.php:452
msgid "Help"
msgstr ""

#. translators: %s is the title of the metabox.
#: includes/admin/pages/class-wpcode-admin-page.php:602
msgid "Collapse Metabox %s"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page.php:721
msgid "Use snippet"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page.php:734
msgid "Edit snippet"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page.php:735
msgid "Used"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page.php:748
msgid "Preview"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page.php:758
msgid "Connect to library to unlock (Free)"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page.php:942
msgid "We encountered a problem loading the Snippet Library items, please try again later."
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page.php:1108
msgid "Preview Snippet"
msgstr ""

#. translators: %d - snippets count.
#: includes/admin/pages/class-wpcode-admin-page.php:1282
msgid "Get Access to Our Library of %d FREE Snippets"
msgstr ""

#: includes/admin/pages/class-wpcode-admin-page.php:1287
msgid "Connect your website with WPCode Library and get instant access to FREE code snippets written by our experts. Snippets can be installed with just 1-click from inside the plugin and come automatically-configured to save you time."
msgstr ""

#. Translators: This is the format for displaying the date in the admin list, [date] at [time].
#: includes/admin/pages/class-wpcode-code-snippets-table.php:127
#: includes/admin/pages/class-wpcode-code-snippets-table.php:136
msgid "%1$s at %2$s"
msgstr ""

#. Translators: The tag by which to filter the list of snippets in the admin.
#: includes/admin/pages/class-wpcode-code-snippets-table.php:169
msgid "Filter snippets by tag: %s"
msgstr ""

#: includes/admin/pages/class-wpcode-code-snippets-table.php:229
msgid "Toggle Snippet Status"
msgstr ""

#. Translators: %1$s is the time since the snippet was deactivated, %2$s is the date and time of deactivation.
#: includes/admin/pages/class-wpcode-code-snippets-table.php:241
msgid "This snippet was automatically deactivated because of a fatal error at %2$s on %3$s (%1$s ago)"
msgstr ""

#: includes/admin/pages/class-wpcode-code-snippets-table.php:249
msgid "This snippet threw an error, you can see more details when editing the snippet."
msgstr ""

#: includes/admin/pages/class-wpcode-code-snippets-table.php:300
#: includes/admin/pages/class-wpcode-code-snippets-table.php:381
msgid "Edit This Snippet"
msgstr ""

#. translators: %s: User display name
#: includes/admin/pages/class-wpcode-code-snippets-table.php:312
msgid "%s is currently editing"
msgstr ""

#: includes/admin/pages/class-wpcode-code-snippets-table.php:354
msgid "Restore this snippet"
msgstr ""

#: includes/admin/pages/class-wpcode-code-snippets-table.php:355
#: includes/admin/pages/class-wpcode-code-snippets-table.php:438
msgid "Restore"
msgstr ""

#: includes/admin/pages/class-wpcode-code-snippets-table.php:371
msgid "Delete this snippet permanently"
msgstr ""

#: includes/admin/pages/class-wpcode-code-snippets-table.php:372
#: includes/admin/pages/class-wpcode-code-snippets-table.php:439
msgid "Delete Permanently"
msgstr ""

#: includes/admin/pages/class-wpcode-code-snippets-table.php:401
msgid "Move this snippet to trash"
msgstr ""

#: includes/admin/pages/class-wpcode-code-snippets-table.php:402
#: includes/admin/pages/class-wpcode-code-snippets-table.php:443
#: includes/admin/pages/class-wpcode-code-snippets-table.php:972
msgid "Trash"
msgstr ""

#: includes/admin/pages/class-wpcode-code-snippets-table.php:421
msgid "Duplicate this snippet"
msgstr ""

#: includes/admin/pages/class-wpcode-code-snippets-table.php:422
msgid "Duplicate"
msgstr ""

#: includes/admin/pages/class-wpcode-code-snippets-table.php:445
msgid "Deactivate"
msgstr ""

#: includes/admin/pages/class-wpcode-code-snippets-table.php:635
#: includes/generator/class-wpcode-generator-query.php:298
msgid "ID"
msgstr ""

#: includes/admin/pages/class-wpcode-code-snippets-table.php:637
#: includes/generator/class-wpcode-generator-post-type.php:378
#: includes/generator/class-wpcode-generator-query.php:225
#: includes/generator/class-wpcode-generator-query.php:299
msgid "Author"
msgstr ""

#: includes/admin/pages/class-wpcode-code-snippets-table.php:847
msgid "0 items"
msgstr ""

#: includes/admin/pages/class-wpcode-code-snippets-table.php:864
#: includes/generator/class-wpcode-generator-hooks.php:1337
msgid "Filter"
msgstr ""

#: includes/admin/pages/class-wpcode-code-snippets-table.php:899
msgid "Filter by code type"
msgstr ""

#: includes/admin/pages/class-wpcode-code-snippets-table.php:901
msgid "All types"
msgstr ""

#: includes/admin/pages/class-wpcode-code-snippets-table.php:941
msgid "Filter by location"
msgstr ""

#: includes/admin/pages/class-wpcode-code-snippets-table.php:943
msgid "All locations"
msgstr ""

#: includes/admin/pages/class-wpcode-code-snippets-table.php:975
msgid "Errors"
msgstr ""

#: includes/admin/pages/trait-wpcode-revisions-display.php:59
msgid "Updated Remotely"
msgstr ""

#: includes/admin/pages/trait-wpcode-revisions-display.php:97
#: includes/generator/class-wpcode-generator-query.php:514
msgid "Compare"
msgstr ""

#. Translators: time since the revision has been updated.
#: includes/admin/pages/trait-wpcode-revisions-display.php:107
msgid "Updated %s ago"
msgstr ""

#. Translators: The placeholder gets replaced with the extra number of revisions available.
#: includes/admin/pages/trait-wpcode-revisions-display.php:130
msgid "%d Other Revisions"
msgstr ""

#: includes/auto-insert/class-wpcode-auto-insert-admin.php:30
msgid "Admin area"
msgstr ""

#: includes/auto-insert/class-wpcode-auto-insert-admin.php:33
msgid "Admin header"
msgstr ""

#: includes/auto-insert/class-wpcode-auto-insert-admin.php:34
msgid "Insert snippet in the wp-admin header area."
msgstr ""

#: includes/auto-insert/class-wpcode-auto-insert-admin.php:37
msgid "Admin footer"
msgstr ""

#: includes/auto-insert/class-wpcode-auto-insert-admin.php:38
msgid "Insert snippet in the wp-admin footer."
msgstr ""

#: includes/auto-insert/class-wpcode-auto-insert-archive.php:35
msgid "Categories, Archives, Tags, Taxonomies"
msgstr ""

#: includes/auto-insert/class-wpcode-auto-insert-archive.php:38
msgid "Insert Before Excerpt"
msgstr ""

#: includes/auto-insert/class-wpcode-auto-insert-archive.php:39
msgid "Insert snippet above post summary."
msgstr ""

#: includes/auto-insert/class-wpcode-auto-insert-archive.php:42
msgid "Insert After Excerpt"
msgstr ""

#: includes/auto-insert/class-wpcode-auto-insert-archive.php:43
msgid "Insert snippet below post summary."
msgstr ""

#: includes/auto-insert/class-wpcode-auto-insert-archive.php:46
msgid "Between Posts"
msgstr ""

#: includes/auto-insert/class-wpcode-auto-insert-archive.php:47
msgid "Insert snippet between multiple posts."
msgstr ""

#: includes/auto-insert/class-wpcode-auto-insert-archive.php:50
msgid "Before Post"
msgstr ""

#: includes/auto-insert/class-wpcode-auto-insert-archive.php:51
#: includes/auto-insert/class-wpcode-auto-insert-single.php:48
msgid "Insert snippet at the beginning of a post."
msgstr ""

#: includes/auto-insert/class-wpcode-auto-insert-archive.php:54
msgid "After Post"
msgstr ""

#: includes/auto-insert/class-wpcode-auto-insert-archive.php:55
#: includes/auto-insert/class-wpcode-auto-insert-single.php:52
msgid "Insert snippet at the end of a post."
msgstr ""

#: includes/auto-insert/class-wpcode-auto-insert-everywhere.php:44
msgid "PHP Snippets Only"
msgstr ""

#: includes/auto-insert/class-wpcode-auto-insert-everywhere.php:47
msgid "Run Everywhere"
msgstr ""

#: includes/auto-insert/class-wpcode-auto-insert-everywhere.php:48
msgid "Snippet gets executed everywhere on your website."
msgstr ""

#: includes/auto-insert/class-wpcode-auto-insert-everywhere.php:51
msgid "Frontend Only"
msgstr ""

#: includes/auto-insert/class-wpcode-auto-insert-everywhere.php:52
msgid "Snippet gets executed only in the frontend of the website."
msgstr ""

#: includes/auto-insert/class-wpcode-auto-insert-everywhere.php:55
msgid "Admin Only"
msgstr ""

#: includes/auto-insert/class-wpcode-auto-insert-everywhere.php:56
msgid "The snippet only gets executed in the wp-admin area."
msgstr ""

#: includes/auto-insert/class-wpcode-auto-insert-everywhere.php:59
msgid "Frontend Conditional Logic"
msgstr ""

#: includes/auto-insert/class-wpcode-auto-insert-everywhere.php:60
msgid "Ideal for running the snippet later with conditional logic rules in the frontend."
msgstr ""

#: includes/auto-insert/class-wpcode-auto-insert-everywhere.php:63
msgid "On Demand"
msgstr ""

#: includes/auto-insert/class-wpcode-auto-insert-everywhere.php:64
msgid "Execute this snippet on demand or programmatically just when you need it."
msgstr ""

#: includes/auto-insert/class-wpcode-auto-insert-single.php:44
msgid "Page, Post, Custom Post Type"
msgstr ""

#: includes/auto-insert/class-wpcode-auto-insert-single.php:47
msgid "Insert Before Post"
msgstr ""

#: includes/auto-insert/class-wpcode-auto-insert-single.php:51
msgid "Insert After Post"
msgstr ""

#: includes/auto-insert/class-wpcode-auto-insert-single.php:55
msgid "Insert Before Content"
msgstr ""

#: includes/auto-insert/class-wpcode-auto-insert-single.php:56
msgid "Insert snippet at the beginning of the post content."
msgstr ""

#: includes/auto-insert/class-wpcode-auto-insert-single.php:59
msgid "Insert After Content"
msgstr ""

#: includes/auto-insert/class-wpcode-auto-insert-single.php:60
msgid "Insert snippet at the end of the post content."
msgstr ""

#: includes/auto-insert/class-wpcode-auto-insert-single.php:63
msgid "Insert Before Paragraph"
msgstr ""

#: includes/auto-insert/class-wpcode-auto-insert-single.php:64
msgid "Insert snippet before paragraph # of the post content."
msgstr ""

#: includes/auto-insert/class-wpcode-auto-insert-single.php:67
msgid "Insert After Paragraph"
msgstr ""

#: includes/auto-insert/class-wpcode-auto-insert-single.php:68
msgid "Insert snippet after paragraph # of the post content."
msgstr ""

#: includes/auto-insert/class-wpcode-auto-insert-site-wide.php:36
msgid "Site wide (frontend)"
msgstr ""

#: includes/auto-insert/class-wpcode-auto-insert-site-wide.php:39
msgid "Site Wide Header"
msgstr ""

#: includes/auto-insert/class-wpcode-auto-insert-site-wide.php:40
msgid "Insert snippet between the head tags of your website on all pages."
msgstr ""

#: includes/auto-insert/class-wpcode-auto-insert-site-wide.php:43
msgid "Site Wide Body"
msgstr ""

#: includes/auto-insert/class-wpcode-auto-insert-site-wide.php:44
msgid "Insert the snippet just after the opening body tag."
msgstr ""

#: includes/auto-insert/class-wpcode-auto-insert-site-wide.php:47
msgid "Site Wide Footer"
msgstr ""

#: includes/auto-insert/class-wpcode-auto-insert-site-wide.php:48
msgid "Insert the snippet in the footer just before the closing body tag."
msgstr ""

#: includes/capabilities.php:51
msgid "Edit Text/Blocks Snippets"
msgstr ""

#: includes/capabilities.php:52
msgid "This enables users to edit just text & blocks snippets, no HTML code is allowed."
msgstr ""

#: includes/capabilities.php:55
msgid "Edit HTML, JavaScript & CSS Snippets"
msgstr ""

#: includes/capabilities.php:56
msgid "This enables users to add and manage HTML, JavaScript & CSS snippets but also Text & Blocks snippets."
msgstr ""

#: includes/capabilities.php:59
msgid "Edit PHP Snippets"
msgstr ""

#: includes/capabilities.php:60
msgid "This enables users to add and manage PHP snippets and all the other types of snippets."
msgstr ""

#: includes/capabilities.php:63
msgid "Manage Conversion Pixels Settings"
msgstr ""

#: includes/capabilities.php:64
msgid "This enables users to manage the conversion pixels settings."
msgstr ""

#: includes/capabilities.php:67
msgid "Use the File Editor"
msgstr ""

#: includes/capabilities.php:68
msgid "This enables users to use the file editor."
msgstr ""

#: includes/class-wpcode-admin-bar-info.php:156
msgid "Snippets With Errors"
msgstr ""

#: includes/class-wpcode-admin-bar-info.php:175
msgid "Loaded on this page"
msgstr ""

#. translators: %d is the total number of global scripts.
#: includes/class-wpcode-admin-bar-info.php:196
msgid "Global Scripts (%d)"
msgstr ""

#: includes/class-wpcode-admin-bar-info.php:286
msgid "Gutenberg Block"
msgstr ""

#: includes/class-wpcode-admin-bar-info.php:348
msgid "Global Header"
msgstr ""

#: includes/class-wpcode-admin-bar-info.php:353
msgid "Global Body"
msgstr ""

#: includes/class-wpcode-admin-bar-info.php:357
msgid "Global Footer"
msgstr ""

#: includes/class-wpcode-admin-bar-info.php:360
msgid "Disabled via Page Scripts"
msgstr ""

#: includes/class-wpcode-admin-bar-info.php:396
msgid "+ Add Snippet"
msgstr ""

#: includes/class-wpcode-admin-bar-info.php:442
msgid "Help Docs"
msgstr ""

#: includes/class-wpcode-auto-insert.php:53
msgid "Global"
msgstr ""

#: includes/class-wpcode-auto-insert.php:57
msgid "Page-Specific"
msgstr ""

#: includes/class-wpcode-generator.php:94
msgid "Admin"
msgstr ""

#: includes/class-wpcode-generator.php:95
#: includes/lite/auto-insert/class-wpcode-auto-insert-content.php:51
msgid "Content"
msgstr ""

#: includes/class-wpcode-generator.php:96
msgid "Core"
msgstr ""

#: includes/class-wpcode-generator.php:97
msgid "Design"
msgstr ""

#: includes/class-wpcode-generator.php:98
#: includes/generator/class-wpcode-generator-post-type.php:539
msgid "Query"
msgstr ""

#: includes/class-wpcode-install.php:151
msgid "Snippet was deactivated due to an error."
msgstr ""

#: includes/class-wpcode-install.php:218
msgid "Display a message after the 1st paragraph of posts"
msgstr ""

#: includes/class-wpcode-install.php:230
msgid "Completely Disable Comments"
msgstr ""

#: includes/class-wpcode-library-auth.php:61
msgid "Your WordPress Site"
msgstr ""

#: includes/class-wpcode-library-auth.php:101
#: includes/class-wpcode-library-auth.php:171
msgid "You do not have permissions to connect WPCode to the library."
msgstr ""

#: includes/class-wpcode-library-auth.php:131
msgid "Authentication successfully completed"
msgstr ""

#: includes/class-wpcode-library-auth.php:132
msgid "Reloading page, please wait."
msgstr ""

#: includes/class-wpcode-smart-tags.php:43
msgid "Content ID"
msgstr ""

#: includes/class-wpcode-smart-tags.php:47
msgid "Content title"
msgstr ""

#: includes/class-wpcode-smart-tags.php:51
msgid "Content Categories"
msgstr ""

#: includes/class-wpcode-smart-tags.php:55
msgid "User's email"
msgstr ""

#: includes/class-wpcode-smart-tags.php:59
msgid "User's first name"
msgstr ""

#: includes/class-wpcode-smart-tags.php:63
msgid "User's last name"
msgstr ""

#: includes/class-wpcode-smart-tags.php:67
msgid "Custom Field (meta)"
msgstr ""

#: includes/class-wpcode-smart-tags.php:72
msgid "Author ID"
msgstr ""

#: includes/class-wpcode-smart-tags.php:76
msgid "Author Name"
msgstr ""

#: includes/class-wpcode-smart-tags.php:80
msgid "Author URL"
msgstr ""

#: includes/class-wpcode-smart-tags.php:84
msgid "Login URL"
msgstr ""

#: includes/class-wpcode-smart-tags.php:88
msgid "Logout URL"
msgstr ""

#: includes/class-wpcode-smart-tags.php:92
msgid "Permalink"
msgstr ""

#: includes/class-wpcode-smart-tags.php:99
msgid "Order number"
msgstr ""

#: includes/class-wpcode-smart-tags.php:103
msgid "Order subtotal"
msgstr ""

#: includes/class-wpcode-smart-tags.php:107
msgid "Order total"
msgstr ""

#: includes/class-wpcode-smart-tags.php:193
#: includes/lite/admin/class-wpcode-metabox-snippets-lite.php:202
msgid "Show Smart Tags"
msgstr ""

#: includes/class-wpcode-smart-tags.php:196
msgid "Hide Smart Tags"
msgstr ""

#: includes/class-wpcode-smart-tags.php:227
msgid "Learn more about Smart Tags"
msgstr ""

#: includes/class-wpcode-snippet-execute.php:85
msgid "HTML Snippet"
msgstr ""

#: includes/class-wpcode-snippet-execute.php:90
msgid "Text Snippet"
msgstr ""

#: includes/class-wpcode-snippet-execute.php:94
msgid "Blocks Snippet (PRO)"
msgstr ""

#: includes/class-wpcode-snippet-execute.php:99
msgid "JavaScript Snippet"
msgstr ""

#: includes/class-wpcode-snippet-execute.php:103
msgid "PHP Snippet"
msgstr ""

#: includes/class-wpcode-snippet-execute.php:107
msgid "Universal Snippet"
msgstr ""

#: includes/class-wpcode-snippet-execute.php:111
msgid "CSS Snippet"
msgstr ""

#: includes/class-wpcode-snippet-execute.php:450
msgid "Snippet has not been activated due to an error."
msgstr ""

#: includes/class-wpcode-snippet-execute.php:454
msgid "Please click the back button in the browser to update the snippet."
msgstr ""

#: includes/class-wpcode-snippet-execute.php:457
msgid "WPCode has detected an error in one of the snippets which has now been automatically deactivated."
msgstr ""

#: includes/class-wpcode-snippet-execute.php:471
msgid "View Snippets With Errors"
msgstr ""

#: includes/class-wpcode-snippet-execute.php:485
msgid "View error logs"
msgstr ""

#: includes/class-wpcode-snippet-execute.php:494
msgid "Enable error logging"
msgstr ""

#: includes/class-wpcode-snippet.php:683
msgid "Untitled Snippet"
msgstr ""

#. translators: %s: Snippet title and ID used in the error log for clarity.
#: includes/class-wpcode-snippet.php:732
msgid "Snippet %s was automatically deactivated due to a fatal error."
msgstr ""

#: includes/conditional-logic/class-wpcode-conditional-page.php:37
msgid "Page"
msgstr ""

#: includes/conditional-logic/class-wpcode-conditional-page.php:48
msgid "Type of page"
msgstr ""

#: includes/conditional-logic/class-wpcode-conditional-page.php:49
msgid "Choose a WordPress-specific type of page for your rule."
msgstr ""

#: includes/conditional-logic/class-wpcode-conditional-page.php:53
msgid "Homepage"
msgstr ""

#: includes/conditional-logic/class-wpcode-conditional-page.php:57
msgid "Archive"
msgstr ""

#: includes/conditional-logic/class-wpcode-conditional-page.php:61
msgid "Single post/page"
msgstr ""

#: includes/conditional-logic/class-wpcode-conditional-page.php:65
msgid "Search page"
msgstr ""

#: includes/conditional-logic/class-wpcode-conditional-page.php:69
msgid "404 page"
msgstr ""

#: includes/conditional-logic/class-wpcode-conditional-page.php:73
msgid "Author page"
msgstr ""

#: includes/conditional-logic/class-wpcode-conditional-page.php:77
msgid "Blog home"
msgstr ""

#: includes/conditional-logic/class-wpcode-conditional-page.php:84
#: includes/generator/class-wpcode-generator-query.php:204
msgid "Post type"
msgstr ""

#: includes/conditional-logic/class-wpcode-conditional-page.php:85
msgid "Target by post type: posts, pages or custom post types."
msgstr ""

#: includes/conditional-logic/class-wpcode-conditional-page.php:91
msgid "Referrer"
msgstr ""

#: includes/conditional-logic/class-wpcode-conditional-page.php:92
msgid "Use the page referrer/last visited page url as a condition."
msgstr ""

#: includes/conditional-logic/class-wpcode-conditional-page.php:97
msgid "Taxonomy page"
msgstr ""

#: includes/conditional-logic/class-wpcode-conditional-page.php:98
msgid "Load only on pages for a specific category/taxonomy."
msgstr ""

#: includes/conditional-logic/class-wpcode-conditional-page.php:104
msgid "Taxonomy term"
msgstr ""

#: includes/conditional-logic/class-wpcode-conditional-page.php:105
msgid "Choose category/taxonomy terms to target for single or archive pages."
msgstr ""

#: includes/conditional-logic/class-wpcode-conditional-page.php:113
msgid "Page URL"
msgstr ""

#: includes/conditional-logic/class-wpcode-conditional-page.php:114
msgid "Use the page URL to limit where this snippet is loaded."
msgstr ""

#: includes/conditional-logic/class-wpcode-conditional-page.php:119
msgid "Post/Page"
msgstr ""

#: includes/conditional-logic/class-wpcode-conditional-page.php:120
msgid "Pick specific posts or pages to load the snippet on."
msgstr ""

#: includes/conditional-logic/class-wpcode-conditional-page.php:124
msgid "Post specific rules are a Pro feature"
msgstr ""

#: includes/conditional-logic/class-wpcode-conditional-page.php:125
msgid "Upgrade today create conditional logic rules for specific pages or posts."
msgstr ""

#: includes/conditional-logic/class-wpcode-conditional-user.php:37
msgid "User"
msgstr ""

#: includes/conditional-logic/class-wpcode-conditional-user.php:48
msgid "Logged-in"
msgstr ""

#: includes/conditional-logic/class-wpcode-conditional-user.php:49
msgid "Check if your site visitor is logged in."
msgstr ""

#: includes/conditional-logic/class-wpcode-conditional-user.php:53
msgid "True"
msgstr ""

#: includes/conditional-logic/class-wpcode-conditional-user.php:57
msgid "False"
msgstr ""

#: includes/conditional-logic/class-wpcode-conditional-user.php:64
msgid "User Role"
msgstr ""

#: includes/conditional-logic/class-wpcode-conditional-user.php:65
msgid "Target a specific user role."
msgstr ""

#: includes/generator/class-wpcode-generator-admin-bar.php:35
msgid "Admin Bar Menu"
msgstr ""

#: includes/generator/class-wpcode-generator-admin-bar.php:36
msgid "Add a custom admin bar menu with links or content."
msgstr ""

#: includes/generator/class-wpcode-generator-admin-bar.php:47
#: includes/generator/class-wpcode-generator-contact-methods.php:47
#: includes/generator/class-wpcode-generator-cronjob.php:47
#: includes/generator/class-wpcode-generator-hooks.php:1279
#: includes/generator/class-wpcode-generator-menu.php:47
#: includes/generator/class-wpcode-generator-post-status.php:47
#: includes/generator/class-wpcode-generator-post-type.php:47
#: includes/generator/class-wpcode-generator-query.php:72
#: includes/generator/class-wpcode-generator-script.php:47
#: includes/generator/class-wpcode-generator-sidebar.php:47
#: includes/generator/class-wpcode-generator-style.php:47
#: includes/generator/class-wpcode-generator-taxonomy.php:47
#: includes/generator/class-wpcode-generator-widget.php:47
msgid "Info"
msgstr ""

#: includes/generator/class-wpcode-generator-admin-bar.php:54
#: includes/generator/class-wpcode-generator-contact-methods.php:54
#: includes/generator/class-wpcode-generator-cronjob.php:54
#: includes/generator/class-wpcode-generator-hooks.php:1286
#: includes/generator/class-wpcode-generator-menu.php:54
#: includes/generator/class-wpcode-generator-post-status.php:54
#: includes/generator/class-wpcode-generator-post-type.php:54
#: includes/generator/class-wpcode-generator-query.php:79
#: includes/generator/class-wpcode-generator-script.php:54
#: includes/generator/class-wpcode-generator-sidebar.php:54
#: includes/generator/class-wpcode-generator-style.php:54
#: includes/generator/class-wpcode-generator-taxonomy.php:54
#: includes/generator/class-wpcode-generator-widget.php:54
msgid "Overview"
msgstr ""

#: includes/generator/class-wpcode-generator-admin-bar.php:55
msgid "Generate a snippet to add a custom menu to the admin bar by filling in a simple form."
msgstr ""

#: includes/generator/class-wpcode-generator-admin-bar.php:63
#: includes/generator/class-wpcode-generator-contact-methods.php:63
#: includes/generator/class-wpcode-generator-cronjob.php:68
#: includes/generator/class-wpcode-generator-hooks.php:1304
#: includes/generator/class-wpcode-generator-menu.php:68
#: includes/generator/class-wpcode-generator-post-status.php:63
#: includes/generator/class-wpcode-generator-post-type.php:63
#: includes/generator/class-wpcode-generator-query.php:93
#: includes/generator/class-wpcode-generator-script.php:63
#: includes/generator/class-wpcode-generator-sidebar.php:68
#: includes/generator/class-wpcode-generator-style.php:63
#: includes/generator/class-wpcode-generator-taxonomy.php:63
#: includes/generator/class-wpcode-generator-widget.php:63
msgid "Usage"
msgstr ""

#: includes/generator/class-wpcode-generator-admin-bar.php:65
#: includes/generator/class-wpcode-generator-contact-methods.php:65
msgid "Fill in the forms sections using the menu on the left."
msgstr ""

#: includes/generator/class-wpcode-generator-admin-bar.php:66
#: includes/generator/class-wpcode-generator-contact-methods.php:66
#: includes/generator/class-wpcode-generator-cronjob.php:71
#: includes/generator/class-wpcode-generator-hooks.php:1307
#: includes/generator/class-wpcode-generator-menu.php:71
#: includes/generator/class-wpcode-generator-post-status.php:66
#: includes/generator/class-wpcode-generator-post-type.php:66
#: includes/generator/class-wpcode-generator-query.php:96
#: includes/generator/class-wpcode-generator-script.php:66
#: includes/generator/class-wpcode-generator-sidebar.php:71
#: includes/generator/class-wpcode-generator-style.php:66
#: includes/generator/class-wpcode-generator-taxonomy.php:66
#: includes/generator/class-wpcode-generator-widget.php:66
msgid "Click the \"Update Code\" button."
msgstr ""

#: includes/generator/class-wpcode-generator-admin-bar.php:67
#: includes/generator/class-wpcode-generator-contact-methods.php:67
#: includes/generator/class-wpcode-generator-cronjob.php:72
#: includes/generator/class-wpcode-generator-hooks.php:1308
#: includes/generator/class-wpcode-generator-menu.php:72
#: includes/generator/class-wpcode-generator-post-status.php:67
#: includes/generator/class-wpcode-generator-post-type.php:67
#: includes/generator/class-wpcode-generator-query.php:97
#: includes/generator/class-wpcode-generator-script.php:67
#: includes/generator/class-wpcode-generator-sidebar.php:72
#: includes/generator/class-wpcode-generator-style.php:67
#: includes/generator/class-wpcode-generator-taxonomy.php:67
#: includes/generator/class-wpcode-generator-widget.php:67
msgid "Click on \"Use Snippet\" to create a new snippet with the generated code."
msgstr ""

#: includes/generator/class-wpcode-generator-admin-bar.php:68
#: includes/generator/class-wpcode-generator-contact-methods.php:68
#: includes/generator/class-wpcode-generator-cronjob.php:73
#: includes/generator/class-wpcode-generator-hooks.php:1309
#: includes/generator/class-wpcode-generator-menu.php:73
#: includes/generator/class-wpcode-generator-post-status.php:68
#: includes/generator/class-wpcode-generator-post-type.php:68
#: includes/generator/class-wpcode-generator-query.php:98
#: includes/generator/class-wpcode-generator-script.php:68
#: includes/generator/class-wpcode-generator-sidebar.php:73
#: includes/generator/class-wpcode-generator-style.php:68
#: includes/generator/class-wpcode-generator-taxonomy.php:68
#: includes/generator/class-wpcode-generator-widget.php:68
msgid "Activate and save the snippet and you're ready to go"
msgstr ""

#: includes/generator/class-wpcode-generator-admin-bar.php:77
#: includes/generator/class-wpcode-generator-contact-methods.php:77
#: includes/generator/class-wpcode-generator-cronjob.php:82
#: includes/generator/class-wpcode-generator-hooks.php:1318
#: includes/generator/class-wpcode-generator-menu.php:82
#: includes/generator/class-wpcode-generator-post-status.php:77
#: includes/generator/class-wpcode-generator-post-type.php:77
#: includes/generator/class-wpcode-generator-query.php:107
#: includes/generator/class-wpcode-generator-script.php:77
#: includes/generator/class-wpcode-generator-sidebar.php:82
#: includes/generator/class-wpcode-generator-style.php:77
#: includes/generator/class-wpcode-generator-taxonomy.php:77
#: includes/generator/class-wpcode-generator-widget.php:77
msgid "Examples"
msgstr ""

#: includes/generator/class-wpcode-generator-admin-bar.php:78
msgid "You could add a new admin bar menu for links you use often, a list of posts, a site you often visit when in the admin, etc."
msgstr ""

#: includes/generator/class-wpcode-generator-admin-bar.php:84
#: includes/generator/class-wpcode-generator-contact-methods.php:84
#: includes/generator/class-wpcode-generator-cronjob.php:89
#: includes/generator/class-wpcode-generator-menu.php:89
#: includes/generator/class-wpcode-generator-post-status.php:84
#: includes/generator/class-wpcode-generator-post-type.php:84
#: includes/generator/class-wpcode-generator-query.php:114
#: includes/generator/class-wpcode-generator-script.php:89
#: includes/generator/class-wpcode-generator-sidebar.php:89
#: includes/generator/class-wpcode-generator-style.php:89
#: includes/generator/class-wpcode-generator-taxonomy.php:84
#: includes/generator/class-wpcode-generator-widget.php:84
msgid "General"
msgstr ""

#: includes/generator/class-wpcode-generator-admin-bar.php:90
#: includes/generator/class-wpcode-generator-contact-methods.php:90
#: includes/generator/class-wpcode-generator-cronjob.php:95
#: includes/generator/class-wpcode-generator-menu.php:95
#: includes/generator/class-wpcode-generator-post-status.php:90
#: includes/generator/class-wpcode-generator-post-type.php:90
#: includes/generator/class-wpcode-generator-script.php:95
#: includes/generator/class-wpcode-generator-sidebar.php:95
#: includes/generator/class-wpcode-generator-style.php:95
#: includes/generator/class-wpcode-generator-taxonomy.php:90
msgid "Function name"
msgstr ""

#: includes/generator/class-wpcode-generator-admin-bar.php:91
#: includes/generator/class-wpcode-generator-cronjob.php:96
#: includes/generator/class-wpcode-generator-menu.php:96
#: includes/generator/class-wpcode-generator-post-status.php:91
#: includes/generator/class-wpcode-generator-post-type.php:91
#: includes/generator/class-wpcode-generator-script.php:96
#: includes/generator/class-wpcode-generator-sidebar.php:96
#: includes/generator/class-wpcode-generator-style.php:96
#: includes/generator/class-wpcode-generator-taxonomy.php:91
msgid "Make this unique to avoid conflicts with other snippets"
msgstr ""

#: includes/generator/class-wpcode-generator-admin-bar.php:101
msgid "Position"
msgstr ""

#: includes/generator/class-wpcode-generator-admin-bar.php:102
msgid "Select where you want the menu item to be displayed on the admin bar."
msgstr ""

#: includes/generator/class-wpcode-generator-admin-bar.php:106
msgid "Last item on the left"
msgstr ""

#: includes/generator/class-wpcode-generator-admin-bar.php:107
msgid "Before Site Name"
msgstr ""

#: includes/generator/class-wpcode-generator-admin-bar.php:108
msgid "After Site Name"
msgstr ""

#: includes/generator/class-wpcode-generator-admin-bar.php:117
msgid "Menu Structure"
msgstr ""

#: includes/generator/class-wpcode-generator-admin-bar.php:123
msgid "Menu ID"
msgstr ""

#: includes/generator/class-wpcode-generator-admin-bar.php:124
msgid "Unique menu id for the admin bar menu."
msgstr ""

#: includes/generator/class-wpcode-generator-admin-bar.php:131
msgid "Menu Title"
msgstr ""

#: includes/generator/class-wpcode-generator-admin-bar.php:132
msgid "Text or HTML that will show up in the admin bar top-level item. Use HTML if you want to display an image."
msgstr ""

#: includes/generator/class-wpcode-generator-admin-bar.php:139
msgid "Menu item link"
msgstr ""

#: includes/generator/class-wpcode-generator-admin-bar.php:140
msgid "If left empty, the top level menu item will not be a link, just text."
msgstr ""

#: includes/generator/class-wpcode-generator-admin-bar.php:147
msgid "Menu item target"
msgstr ""

#: includes/generator/class-wpcode-generator-admin-bar.php:148
msgid "The menu item is a link use this field to set the target attribute. Use \"_blank\" to open the link in a new tab."
msgstr ""

#: includes/generator/class-wpcode-generator-admin-bar.php:158
msgid "Submenu Item Title"
msgstr ""

#: includes/generator/class-wpcode-generator-admin-bar.php:159
msgid "Text or HTML for the submenu item."
msgstr ""

#: includes/generator/class-wpcode-generator-admin-bar.php:167
msgid "Submenu item link"
msgstr ""

#: includes/generator/class-wpcode-generator-admin-bar.php:168
msgid "If left empty, this menu item will not be a link, just text."
msgstr ""

#: includes/generator/class-wpcode-generator-admin-bar.php:176
msgid "Submenu item target attribute"
msgstr ""

#: includes/generator/class-wpcode-generator-admin-bar.php:177
msgid "If the menu item is a link use this for the target attribute. Use \"_blank\" to open in a new tab."
msgstr ""

#: includes/generator/class-wpcode-generator-admin-bar.php:188
msgid "Add more submenu items"
msgstr ""

#: includes/generator/class-wpcode-generator-admin-bar.php:189
msgid "Use the \"Add submenu item\" button below to add multiple submenu items."
msgstr ""

#: includes/generator/class-wpcode-generator-admin-bar.php:193
msgid "Add submenu item"
msgstr ""

#: includes/generator/class-wpcode-generator-contact-methods.php:35
#: includes/generator/class-wpcode-generator-contact-methods.php:111
msgid "Contact Methods"
msgstr ""

#: includes/generator/class-wpcode-generator-contact-methods.php:36
msgid "Add additional contact methods to WordPress user profiles."
msgstr ""

#: includes/generator/class-wpcode-generator-contact-methods.php:55
msgid "Use this generator to create a snippet which adds more contact methods to your WordPress users profiles."
msgstr ""

#: includes/generator/class-wpcode-generator-contact-methods.php:78
msgid "You can add extra fields for user profiles like their extended address, city, country, phone number, social media profiles (Facebook, Twitter, etc)."
msgstr ""

#: includes/generator/class-wpcode-generator-contact-methods.php:91
msgid "Make this unique to avoid conflicts with other snippets."
msgstr ""

#: includes/generator/class-wpcode-generator-contact-methods.php:101
#: includes/generator/class-wpcode-generator-cronjob.php:106
#: includes/generator/class-wpcode-generator-menu.php:106
#: includes/generator/class-wpcode-generator-post-status.php:101
#: includes/generator/class-wpcode-generator-post-type.php:101
#: includes/generator/class-wpcode-generator-sidebar.php:106
#: includes/generator/class-wpcode-generator-taxonomy.php:102
#: includes/generator/class-wpcode-generator-widget.php:113
msgid "Text Domain"
msgstr ""

#: includes/generator/class-wpcode-generator-contact-methods.php:102
#: includes/generator/class-wpcode-generator-cronjob.php:107
#: includes/generator/class-wpcode-generator-menu.php:107
#: includes/generator/class-wpcode-generator-post-status.php:102
#: includes/generator/class-wpcode-generator-post-type.php:102
#: includes/generator/class-wpcode-generator-sidebar.php:107
#: includes/generator/class-wpcode-generator-taxonomy.php:103
msgid "Optional text domain for translations."
msgstr ""

#: includes/generator/class-wpcode-generator-contact-methods.php:117
msgid "Contact Method Slug"
msgstr ""

#: includes/generator/class-wpcode-generator-contact-methods.php:118
msgid "A lowercase with no spaces slug for usage in the code. For example: \"facebook\" or \"telephone\"."
msgstr ""

#: includes/generator/class-wpcode-generator-contact-methods.php:126
msgid "Contact Method Label"
msgstr ""

#: includes/generator/class-wpcode-generator-contact-methods.php:127
msgid "This will show up as a label next to the contact method field. For example: \"Facebook URL\" or \"Phone number\"."
msgstr ""

#: includes/generator/class-wpcode-generator-contact-methods.php:138
msgid "Add more contact methods"
msgstr ""

#: includes/generator/class-wpcode-generator-contact-methods.php:139
msgid "Use the \"Add contact method\" button below to add as many contact methods as you wish."
msgstr ""

#: includes/generator/class-wpcode-generator-contact-methods.php:143
msgid "Add contact method"
msgstr ""

#: includes/generator/class-wpcode-generator-cronjob.php:35
msgid "Schedule a Cron Job"
msgstr ""

#: includes/generator/class-wpcode-generator-cronjob.php:36
msgid "Generate a snippet to schedule a recurring event using the WordPress cron."
msgstr ""

#. Translators: Placeholders add links to the wordpress.org references.
#: includes/generator/class-wpcode-generator-cronjob.php:57
msgid "This generator makes it easy to generate a snippet that will schedule a recurring event using %1$swp_schedule_event%2$s."
msgstr ""

#: includes/generator/class-wpcode-generator-cronjob.php:70
#: includes/generator/class-wpcode-generator-hooks.php:1306
#: includes/generator/class-wpcode-generator-menu.php:70
#: includes/generator/class-wpcode-generator-post-status.php:65
#: includes/generator/class-wpcode-generator-post-type.php:65
#: includes/generator/class-wpcode-generator-query.php:95
#: includes/generator/class-wpcode-generator-script.php:65
#: includes/generator/class-wpcode-generator-sidebar.php:70
#: includes/generator/class-wpcode-generator-style.php:65
#: includes/generator/class-wpcode-generator-taxonomy.php:65
#: includes/generator/class-wpcode-generator-widget.php:65
msgid "Fill in the forms using the menu on the left."
msgstr ""

#: includes/generator/class-wpcode-generator-cronjob.php:83
msgid "You may want to run some code once every hour, day or week, for example you could use this to send an email with the number of published posts every week."
msgstr ""

#: includes/generator/class-wpcode-generator-cronjob.php:116
#: includes/lite/conditional-logic/class-wpcode-conditional-schedule.php:37
msgid "Schedule"
msgstr ""

#: includes/generator/class-wpcode-generator-cronjob.php:122
msgid "Recurrence"
msgstr ""

#: includes/generator/class-wpcode-generator-cronjob.php:123
msgid "Choose how often you want this event to run."
msgstr ""

#: includes/generator/class-wpcode-generator-cronjob.php:127
msgid "Hourly"
msgstr ""

#: includes/generator/class-wpcode-generator-cronjob.php:128
msgid "Twice Daily"
msgstr ""

#: includes/generator/class-wpcode-generator-cronjob.php:129
msgid "Daily"
msgstr ""

#: includes/generator/class-wpcode-generator-cronjob.php:130
msgid "Custom"
msgstr ""

#: includes/generator/class-wpcode-generator-cronjob.php:138
msgid "Custom Recurrence Name"
msgstr ""

#: includes/generator/class-wpcode-generator-cronjob.php:139
msgid "This is the recurrence name slug, lowercase and no space."
msgstr ""

#: includes/generator/class-wpcode-generator-cronjob.php:145
msgid "Custom Recurrence Label"
msgstr ""

#: includes/generator/class-wpcode-generator-cronjob.php:148
msgid "This label will be used in a list of cron events, for example."
msgstr ""

#: includes/generator/class-wpcode-generator-cronjob.php:152
msgid "Custom Recurrence Interval"
msgstr ""

#: includes/generator/class-wpcode-generator-cronjob.php:155
msgid "The number of seconds of this interval."
msgstr ""

#: includes/generator/class-wpcode-generator-cronjob.php:161
#: includes/generator/class-wpcode-generator-hooks.php:1379
msgid "Code"
msgstr ""

#: includes/generator/class-wpcode-generator-cronjob.php:167
#: includes/generator/class-wpcode-generator-hooks.php:1342
msgid "Hook name"
msgstr ""

#: includes/generator/class-wpcode-generator-cronjob.php:168
msgid "Unique name of your hook used to run when scheduled."
msgstr ""

#: includes/generator/class-wpcode-generator-cronjob.php:177
#: includes/generator/class-wpcode-generator-hooks.php:1385
msgid "PHP Code"
msgstr ""

#: includes/generator/class-wpcode-generator-cronjob.php:178
msgid "Custom PHP code that will run when the event is triggered."
msgstr ""

#: includes/generator/class-wpcode-generator-hooks.php:35
msgid "Hooks"
msgstr ""

#: includes/generator/class-wpcode-generator-hooks.php:36
msgid "Generate a snippet for an action or a filter using any available hook."
msgstr ""

#. Translators: Placeholders add links to the wordpress.org references.
#: includes/generator/class-wpcode-generator-hooks.php:1289
msgid "Using this generator you can safely add custom %1$shooks%2$s using either %3$sadd_action%4$s or %5$sadd_filter%6$s."
msgstr ""

#: includes/generator/class-wpcode-generator-hooks.php:1319
msgid "You can use this to quickly get started with adding an action or filter."
msgstr ""

#: includes/generator/class-wpcode-generator-hooks.php:1325
msgid "Hook Details"
msgstr ""

#: includes/generator/class-wpcode-generator-hooks.php:1331
msgid "Hook type"
msgstr ""

#: includes/generator/class-wpcode-generator-hooks.php:1332
msgid "Can be either an action or a filter"
msgstr ""

#: includes/generator/class-wpcode-generator-hooks.php:1336
msgid "Action"
msgstr ""

#: includes/generator/class-wpcode-generator-hooks.php:1343
msgid "Input hook name or pick one from the suggested list as you type."
msgstr ""

#: includes/generator/class-wpcode-generator-hooks.php:1353
msgid "Callback function"
msgstr ""

#: includes/generator/class-wpcode-generator-hooks.php:1354
msgid "Name of the function you want to add to this hook."
msgstr ""

#: includes/generator/class-wpcode-generator-hooks.php:1361
msgid "Priority of this hook, by default 10."
msgstr ""

#: includes/generator/class-wpcode-generator-hooks.php:1370
msgid "Arguments list"
msgstr ""

#: includes/generator/class-wpcode-generator-hooks.php:1371
msgid "Add comma-separated custom arguments that will be passed to the callback function depending on the action/filter."
msgstr ""

#: includes/generator/class-wpcode-generator-hooks.php:1386
msgid "Custom PHP code you want to run in the function, you can also edit this after you create the snippet."
msgstr ""

#: includes/generator/class-wpcode-generator-menu.php:35
msgid "Navigation Menu"
msgstr ""

#: includes/generator/class-wpcode-generator-menu.php:36
msgid "Generate a snippet to register new navigation menus for your website."
msgstr ""

#. Translators: Placeholders add links to the wordpress.org references.
#: includes/generator/class-wpcode-generator-menu.php:57
msgid "This generator makes it easy to add new navigation menus to your website using the \"register_nav_menus\" function."
msgstr ""

#: includes/generator/class-wpcode-generator-menu.php:83
msgid "You can add a new navigation menu for your website to display in a flyout menu that is not part of the theme, for example."
msgstr ""

#: includes/generator/class-wpcode-generator-menu.php:116
msgid "Menus"
msgstr ""

#: includes/generator/class-wpcode-generator-menu.php:122
msgid "Menu name"
msgstr ""

#: includes/generator/class-wpcode-generator-menu.php:123
msgid "This is the menu name slug, lowercase and no space."
msgstr ""

#: includes/generator/class-wpcode-generator-menu.php:130
msgid "Menu label"
msgstr ""

#: includes/generator/class-wpcode-generator-menu.php:131
msgid "Add a descriptive label for this menu in the admin."
msgstr ""

#: includes/generator/class-wpcode-generator-menu.php:141
msgid "Add another menu"
msgstr ""

#: includes/generator/class-wpcode-generator-menu.php:142
msgid "Use the \"Add menu\" button below to add as many menu locations as you need."
msgstr ""

#: includes/generator/class-wpcode-generator-menu.php:146
msgid "Add Menu"
msgstr ""

#: includes/generator/class-wpcode-generator-post-status.php:35
#: includes/generator/class-wpcode-generator-post-status.php:117
msgid "Post Status"
msgstr ""

#: includes/generator/class-wpcode-generator-post-status.php:36
msgid "Use this tool to generate a custom post status for your posts."
msgstr ""

#: includes/generator/class-wpcode-generator-post-status.php:55
msgid "Generate custom post statuses for your posts to improve the way you manage content."
msgstr ""

#: includes/generator/class-wpcode-generator-post-status.php:78
msgid "You could add a new status called \"Pending Review\" that your authors can use before the content will be published"
msgstr ""

#: includes/generator/class-wpcode-generator-post-status.php:118
msgid "Name of status used in the code, lowercase maximum 32 characters."
msgstr ""

#: includes/generator/class-wpcode-generator-post-status.php:136
#: includes/generator/class-wpcode-generator-post-type.php:144
#: includes/generator/class-wpcode-generator-taxonomy.php:137
msgid "Name (Plural)"
msgstr ""

#: includes/generator/class-wpcode-generator-post-status.php:146
#: includes/generator/class-wpcode-generator-post-type.php:441
#: includes/generator/class-wpcode-generator-taxonomy.php:309
msgid "Visibility"
msgstr ""

#: includes/generator/class-wpcode-generator-post-status.php:152
#: includes/generator/class-wpcode-generator-post-type.php:447
#: includes/generator/class-wpcode-generator-taxonomy.php:315
msgid "Public"
msgstr ""

#: includes/generator/class-wpcode-generator-post-status.php:153
#: includes/generator/class-wpcode-generator-post-status.php:167
msgid "Should the posts with this status be visible in the frontend?"
msgstr ""

#: includes/generator/class-wpcode-generator-post-status.php:156
#: includes/generator/class-wpcode-generator-post-status.php:184
msgid "Yes - Default"
msgstr ""

#: includes/generator/class-wpcode-generator-post-status.php:157
#: includes/generator/class-wpcode-generator-post-status.php:185
#: includes/generator/class-wpcode-generator-post-type.php:411
#: includes/generator/class-wpcode-generator-post-type.php:426
#: includes/generator/class-wpcode-generator-post-type.php:454
#: includes/generator/class-wpcode-generator-post-type.php:465
#: includes/generator/class-wpcode-generator-post-type.php:479
#: includes/generator/class-wpcode-generator-post-type.php:521
#: includes/generator/class-wpcode-generator-post-type.php:532
#: includes/generator/class-wpcode-generator-post-type.php:552
#: includes/generator/class-wpcode-generator-post-type.php:622
#: includes/generator/class-wpcode-generator-post-type.php:636
#: includes/generator/class-wpcode-generator-post-type.php:647
#: includes/generator/class-wpcode-generator-query.php:137
#: includes/generator/class-wpcode-generator-query.php:342
#: includes/generator/class-wpcode-generator-query.php:437
#: includes/generator/class-wpcode-generator-script.php:210
#: includes/generator/class-wpcode-generator-script.php:229
#: includes/generator/class-wpcode-generator-style.php:211
#: includes/generator/class-wpcode-generator-style.php:230
#: includes/generator/class-wpcode-generator-taxonomy.php:322
#: includes/generator/class-wpcode-generator-taxonomy.php:336
#: includes/generator/class-wpcode-generator-taxonomy.php:347
#: includes/generator/class-wpcode-generator-taxonomy.php:361
#: includes/generator/class-wpcode-generator-taxonomy.php:372
#: includes/generator/class-wpcode-generator-taxonomy.php:417
msgid "No"
msgstr ""

#: includes/generator/class-wpcode-generator-post-status.php:166
msgid "Exclude from search results"
msgstr ""

#: includes/generator/class-wpcode-generator-post-status.php:171
#: includes/generator/class-wpcode-generator-post-status.php:196
msgid "No - Default"
msgstr ""

#: includes/generator/class-wpcode-generator-post-status.php:180
msgid "Show in admin all list"
msgstr ""

#: includes/generator/class-wpcode-generator-post-status.php:181
msgid "Show statuses in the edit listing of the post."
msgstr ""

#: includes/generator/class-wpcode-generator-post-status.php:191
msgid "Show in admin status list"
msgstr ""

#: includes/generator/class-wpcode-generator-post-status.php:192
msgid "Show statuses list at the top of the edit listings. e.g. Published (12) Custom Status (2)"
msgstr ""

#: includes/generator/class-wpcode-generator-post-type.php:35
#: includes/generator/class-wpcode-generator-post-type.php:111
msgid "Post Type"
msgstr ""

#: includes/generator/class-wpcode-generator-post-type.php:36
msgid "Use this tool to generate a custom post type for your website."
msgstr ""

#: includes/generator/class-wpcode-generator-post-type.php:55
msgid "Generate custom post types for your website using a snippet."
msgstr ""

#: includes/generator/class-wpcode-generator-post-type.php:78
msgid "You can add custom post types for specific items that are not blog posts, for example, if your site is about music you can have post types for artists, albums or songs."
msgstr ""

#: includes/generator/class-wpcode-generator-post-type.php:117
msgid "Post Type Key"
msgstr ""

#: includes/generator/class-wpcode-generator-post-type.php:118
msgid "Name of post type used in the code, lowercase maximum 20 characters."
msgstr ""

#: includes/generator/class-wpcode-generator-post-type.php:125
#: includes/generator/class-wpcode-generator-sidebar.php:138
#: includes/generator/class-wpcode-generator-widget.php:150
#: includes/generator/class-wpcode-generator-widget.php:223
msgid "Description"
msgstr ""

#: includes/generator/class-wpcode-generator-post-type.php:126
msgid "A short description of the post type."
msgstr ""

#: includes/generator/class-wpcode-generator-post-type.php:137
msgid "The singular post type name (e.g. Artist, Album, Song)."
msgstr ""

#: includes/generator/class-wpcode-generator-post-type.php:145
msgid "The post type plural name (e.g. Artists, Albums, Songs)."
msgstr ""

#: includes/generator/class-wpcode-generator-post-type.php:155
msgid "Link To Taxonomies"
msgstr ""

#: includes/generator/class-wpcode-generator-post-type.php:156
msgid "Comma-separated list of Taxonomies (e.g. post_tag, category)"
msgstr ""

#: includes/generator/class-wpcode-generator-post-type.php:163
#: includes/generator/class-wpcode-generator-taxonomy.php:156
msgid "Hierarchical"
msgstr ""

#: includes/generator/class-wpcode-generator-post-type.php:164
msgid "Hierarchical post types can have parents/children."
msgstr ""

#: includes/generator/class-wpcode-generator-post-type.php:168
msgid "Yes, like pages"
msgstr ""

#: includes/generator/class-wpcode-generator-post-type.php:169
msgid "No, like posts"
msgstr ""

#: includes/generator/class-wpcode-generator-post-type.php:176
#: includes/generator/class-wpcode-generator-taxonomy.php:169
msgid "Labels"
msgstr ""

#: includes/generator/class-wpcode-generator-post-type.php:182
#: includes/generator/class-wpcode-generator-taxonomy.php:175
msgid "Menu Name"
msgstr ""

#: includes/generator/class-wpcode-generator-post-type.php:189
msgid "Admin Bar Name"
msgstr ""

#: includes/generator/class-wpcode-generator-post-type.php:196
msgid "Archives"
msgstr ""

#: includes/generator/class-wpcode-generator-post-type.php:203
msgid "Attributes"
msgstr ""

#: includes/generator/class-wpcode-generator-post-type.php:210
#: includes/generator/class-wpcode-generator-taxonomy.php:189
msgid "Parent Item"
msgstr ""

#: includes/generator/class-wpcode-generator-post-type.php:217
#: includes/generator/class-wpcode-generator-taxonomy.php:182
msgid "All Items"
msgstr ""

#: includes/generator/class-wpcode-generator-post-type.php:224
#: includes/generator/class-wpcode-generator-taxonomy.php:210
msgid "Add New Item"
msgstr ""

#: includes/generator/class-wpcode-generator-post-type.php:238
msgid "New Item"
msgstr ""

#: includes/generator/class-wpcode-generator-post-type.php:248
#: includes/generator/class-wpcode-generator-taxonomy.php:220
msgid "Edit Item"
msgstr ""

#: includes/generator/class-wpcode-generator-post-type.php:255
#: includes/generator/class-wpcode-generator-taxonomy.php:227
msgid "Update Item"
msgstr ""

#: includes/generator/class-wpcode-generator-post-type.php:262
#: includes/generator/class-wpcode-generator-taxonomy.php:234
msgid "View Item"
msgstr ""

#: includes/generator/class-wpcode-generator-post-type.php:269
msgid "View Items"
msgstr ""

#: includes/generator/class-wpcode-generator-post-type.php:276
msgid "Search Item"
msgstr ""

#: includes/generator/class-wpcode-generator-post-type.php:283
#: includes/generator/class-wpcode-generator-taxonomy.php:279
msgid "Not Found"
msgstr ""

#: includes/generator/class-wpcode-generator-post-type.php:290
msgid "Not Found in Trash"
msgstr ""

#: includes/generator/class-wpcode-generator-post-type.php:297
msgid "Featured Image"
msgstr ""

#: includes/generator/class-wpcode-generator-post-type.php:304
msgid "Set featured image"
msgstr ""

#: includes/generator/class-wpcode-generator-post-type.php:314
msgid "Remove featured image"
msgstr ""

#: includes/generator/class-wpcode-generator-post-type.php:321
msgid "Use as featured image"
msgstr ""

#: includes/generator/class-wpcode-generator-post-type.php:328
msgid "Insert into item"
msgstr ""

#: includes/generator/class-wpcode-generator-post-type.php:335
msgid "Uploaded to this item"
msgstr ""

#: includes/generator/class-wpcode-generator-post-type.php:342
#: includes/generator/class-wpcode-generator-taxonomy.php:293
msgid "Items list"
msgstr ""

#: includes/generator/class-wpcode-generator-post-type.php:349
#: includes/generator/class-wpcode-generator-taxonomy.php:300
msgid "Items list navigation"
msgstr ""

#: includes/generator/class-wpcode-generator-post-type.php:356
msgid "Filter items list"
msgstr ""

#: includes/generator/class-wpcode-generator-post-type.php:365
#: includes/generator/class-wpcode-generator-widget.php:239
msgid "Options"
msgstr ""

#: includes/generator/class-wpcode-generator-post-type.php:371
msgid "Supports"
msgstr ""

#: includes/generator/class-wpcode-generator-post-type.php:372
msgid "Select which features this post type should support"
msgstr ""

#: includes/generator/class-wpcode-generator-post-type.php:376
#: includes/generator/class-wpcode-generator-query.php:300
msgid "Title"
msgstr ""

#: includes/generator/class-wpcode-generator-post-type.php:377
msgid "Content Editor"
msgstr ""

#: includes/generator/class-wpcode-generator-post-type.php:379
msgid "Featured image"
msgstr ""

#: includes/generator/class-wpcode-generator-post-type.php:380
msgid "Excerpt"
msgstr ""

#: includes/generator/class-wpcode-generator-post-type.php:381
msgid "Trackbacks"
msgstr ""

#: includes/generator/class-wpcode-generator-post-type.php:382
#: includes/generator/class-wpcode-generator-query.php:487
msgid "Custom Fields"
msgstr ""

#: includes/generator/class-wpcode-generator-post-type.php:383
msgid "Comments"
msgstr ""

#: includes/generator/class-wpcode-generator-post-type.php:385
msgid "Page Attributes"
msgstr ""

#: includes/generator/class-wpcode-generator-post-type.php:386
msgid "Post Formats"
msgstr ""

#: includes/generator/class-wpcode-generator-post-type.php:394
msgid "Exclude From Search"
msgstr ""

#: includes/generator/class-wpcode-generator-post-type.php:395
msgid "Exclude the posts of this post type from search results?"
msgstr ""

#: includes/generator/class-wpcode-generator-post-type.php:400
#: includes/generator/class-wpcode-generator-post-type.php:758
#: includes/generator/class-wpcode-generator-taxonomy.php:427
#: includes/generator/class-wpcode-generator-taxonomy.php:506
msgid "No - default"
msgstr ""

#: includes/generator/class-wpcode-generator-post-type.php:405
msgid "Enable Export"
msgstr ""

#: includes/generator/class-wpcode-generator-post-type.php:406
msgid "Allow exporting posts of this post type in Tools > Export."
msgstr ""

#: includes/generator/class-wpcode-generator-post-type.php:410
#: includes/generator/class-wpcode-generator-post-type.php:424
#: includes/generator/class-wpcode-generator-post-type.php:453
#: includes/generator/class-wpcode-generator-post-type.php:464
#: includes/generator/class-wpcode-generator-post-type.php:478
#: includes/generator/class-wpcode-generator-post-type.php:520
#: includes/generator/class-wpcode-generator-post-type.php:531
#: includes/generator/class-wpcode-generator-post-type.php:621
#: includes/generator/class-wpcode-generator-post-type.php:635
#: includes/generator/class-wpcode-generator-post-type.php:646
#: includes/generator/class-wpcode-generator-taxonomy.php:321
#: includes/generator/class-wpcode-generator-taxonomy.php:335
#: includes/generator/class-wpcode-generator-taxonomy.php:346
#: includes/generator/class-wpcode-generator-taxonomy.php:360
#: includes/generator/class-wpcode-generator-taxonomy.php:371
#: includes/generator/class-wpcode-generator-taxonomy.php:416
msgid "Yes - default"
msgstr ""

#: includes/generator/class-wpcode-generator-post-type.php:419
msgid "Enable Archives"
msgstr ""

#: includes/generator/class-wpcode-generator-post-type.php:420
msgid "Enables archives for this post type, the post type key is used as default."
msgstr ""

#: includes/generator/class-wpcode-generator-post-type.php:425
msgid "Yes - using custom slug"
msgstr ""

#: includes/generator/class-wpcode-generator-post-type.php:431
msgid "Custom Archive Slug"
msgstr ""

#: includes/generator/class-wpcode-generator-post-type.php:432
msgid "Custom archive slug (if selected above)."
msgstr ""

#. Translators: Placeholders add a link to the wp.org documentation page.
#: includes/generator/class-wpcode-generator-post-type.php:449
msgid "Should this post type be %1$svisible to authors%2$s?"
msgstr ""

#: includes/generator/class-wpcode-generator-post-type.php:459
#: includes/generator/class-wpcode-generator-taxonomy.php:330
msgid "Show UI"
msgstr ""

#: includes/generator/class-wpcode-generator-post-type.php:460
msgid "Should this post type be visible in the Admin?"
msgstr ""

#: includes/generator/class-wpcode-generator-post-type.php:473
msgid "Show in Menu?"
msgstr ""

#: includes/generator/class-wpcode-generator-post-type.php:474
msgid "Should this post type be visible in the admin menu?"
msgstr ""

#: includes/generator/class-wpcode-generator-post-type.php:484
msgid "Menu position"
msgstr ""

#: includes/generator/class-wpcode-generator-post-type.php:485
msgid "Choose the admin menu position."
msgstr ""

#: includes/generator/class-wpcode-generator-post-type.php:489
msgid "Below Posts (5)"
msgstr ""

#: includes/generator/class-wpcode-generator-post-type.php:490
msgid "Below Media (10)"
msgstr ""

#: includes/generator/class-wpcode-generator-post-type.php:491
msgid "Below Pages (20)"
msgstr ""

#: includes/generator/class-wpcode-generator-post-type.php:492
msgid "Below Comments (30)"
msgstr ""

#: includes/generator/class-wpcode-generator-post-type.php:493
msgid "Below First Separator (60)"
msgstr ""

#: includes/generator/class-wpcode-generator-post-type.php:494
msgid "Below Plugins (65)"
msgstr ""

#: includes/generator/class-wpcode-generator-post-type.php:495
msgid "Below Users (70)"
msgstr ""

#: includes/generator/class-wpcode-generator-post-type.php:496
msgid "Below Tools (75)"
msgstr ""

#: includes/generator/class-wpcode-generator-post-type.php:497
msgid "Below Settings (80)"
msgstr ""

#: includes/generator/class-wpcode-generator-post-type.php:498
msgid "Below Second Separator (100)"
msgstr ""

#: includes/generator/class-wpcode-generator-post-type.php:503
msgid "Menu Icon"
msgstr ""

#. Translators: Placeholder adds a link to the dashicons page.
#: includes/generator/class-wpcode-generator-post-type.php:505
msgid "Icon used next to the post type label in the admin menu. Use either a %1$sdashicon%2$s name or a full URL to an image file."
msgstr ""

#: includes/generator/class-wpcode-generator-post-type.php:515
msgid "Show in Admin Bar?"
msgstr ""

#: includes/generator/class-wpcode-generator-post-type.php:516
msgid "Should this post type be visible in the admin bar?"
msgstr ""

#: includes/generator/class-wpcode-generator-post-type.php:526
msgid "Show in Navigation Menus?"
msgstr ""

#: includes/generator/class-wpcode-generator-post-type.php:527
msgid "Should this post type be available for use in menus (Appearance > Menus)?"
msgstr ""

#: includes/generator/class-wpcode-generator-post-type.php:545
msgid "Publicly Queryable"
msgstr ""

#. Translators: Placeholders add link to wp.org docs.
#: includes/generator/class-wpcode-generator-post-type.php:547
msgid "Enable frontend requests using the query variable. %1$sSee Documentation.%2$s"
msgstr ""

#: includes/generator/class-wpcode-generator-post-type.php:560
msgid "Query variable"
msgstr ""

#. Translators: Placeholders add link to wp.org docs.
#: includes/generator/class-wpcode-generator-post-type.php:562
msgid "Key used for querying posts in the frontend. %1$sSee Documentation.%2$s"
msgstr ""

#: includes/generator/class-wpcode-generator-post-type.php:566
#: includes/generator/class-wpcode-generator-post-type.php:598
msgid "Default (post type key)"
msgstr ""

#: includes/generator/class-wpcode-generator-post-type.php:567
msgid "Custom variable"
msgstr ""

#: includes/generator/class-wpcode-generator-post-type.php:575
msgid "Custom Query Variable"
msgstr ""

#. Translators: Placeholder adds a link to the dashicons page.
#: includes/generator/class-wpcode-generator-post-type.php:577
msgid "The custom query variable to use for this post type. %1$sSee documentation%2$s."
msgstr ""

#: includes/generator/class-wpcode-generator-post-type.php:586
#: includes/generator/class-wpcode-generator-taxonomy.php:379
msgid "Permalinks"
msgstr ""

#: includes/generator/class-wpcode-generator-post-type.php:592
msgid "Rewrite Permalinks"
msgstr ""

#. Translators: Placeholders add link to wp.org docs.
#: includes/generator/class-wpcode-generator-post-type.php:594
msgid "Use the default permalink structure, disable permalinks for this post type or use custom options. %1$sSee Documentation.%2$s"
msgstr ""

#: includes/generator/class-wpcode-generator-post-type.php:599
#: includes/generator/class-wpcode-generator-taxonomy.php:391
msgid "Disable permalink rewrites"
msgstr ""

#: includes/generator/class-wpcode-generator-post-type.php:600
#: includes/generator/class-wpcode-generator-taxonomy.php:392
msgid "Custom permalink structure"
msgstr ""

#: includes/generator/class-wpcode-generator-post-type.php:608
#: includes/generator/class-wpcode-generator-taxonomy.php:400
msgid "URL Slug"
msgstr ""

#: includes/generator/class-wpcode-generator-post-type.php:609
msgid "The slug used for this post types base. (for example: artist in www.example.com/artist/ )"
msgstr ""

#: includes/generator/class-wpcode-generator-post-type.php:616
msgid "Use URL Slug?"
msgstr ""

#: includes/generator/class-wpcode-generator-post-type.php:617
msgid "Use the post type name as URL slug base?"
msgstr ""

#: includes/generator/class-wpcode-generator-post-type.php:630
msgid "Use pagination?"
msgstr ""

#: includes/generator/class-wpcode-generator-post-type.php:631
msgid "Allow the post type to have pagination?"
msgstr ""

#: includes/generator/class-wpcode-generator-post-type.php:641
msgid "Use feeds?"
msgstr ""

#: includes/generator/class-wpcode-generator-post-type.php:642
msgid "Allow the post type to have feeds?"
msgstr ""

#: includes/generator/class-wpcode-generator-post-type.php:654
#: includes/generator/class-wpcode-generator-post-type.php:660
#: includes/generator/class-wpcode-generator-taxonomy.php:435
#: includes/generator/class-wpcode-generator-taxonomy.php:441
msgid "Capabilities"
msgstr ""

#. Translators: Placeholders add link to wp.org docs.
#: includes/generator/class-wpcode-generator-post-type.php:662
msgid "User capabilities in relation to this post type. %1$sSee Documentation.%2$s"
msgstr ""

#: includes/generator/class-wpcode-generator-post-type.php:666
#: includes/generator/class-wpcode-generator-taxonomy.php:447
msgid "Base capabilities - default"
msgstr ""

#: includes/generator/class-wpcode-generator-post-type.php:667
#: includes/generator/class-wpcode-generator-post-type.php:686
#: includes/generator/class-wpcode-generator-taxonomy.php:448
#: includes/generator/class-wpcode-generator-taxonomy.php:453
msgid "Custom Capabilities"
msgstr ""

#: includes/generator/class-wpcode-generator-post-type.php:672
msgid "Base Capablities Type"
msgstr ""

#: includes/generator/class-wpcode-generator-post-type.php:673
msgid "Use base capabilities from a core post type."
msgstr ""

#: includes/generator/class-wpcode-generator-post-type.php:677
msgid "Posts"
msgstr ""

#: includes/generator/class-wpcode-generator-post-type.php:678
msgid "Pages"
msgstr ""

#: includes/generator/class-wpcode-generator-post-type.php:691
msgid "Read Post"
msgstr ""

#: includes/generator/class-wpcode-generator-post-type.php:698
msgid "Read Private Posts"
msgstr ""

#: includes/generator/class-wpcode-generator-post-type.php:705
msgid "Publish Posts"
msgstr ""

#: includes/generator/class-wpcode-generator-post-type.php:715
msgid "Delete Posts"
msgstr ""

#: includes/generator/class-wpcode-generator-post-type.php:722
msgid "Edit Post"
msgstr ""

#: includes/generator/class-wpcode-generator-post-type.php:729
msgid "Edit Posts"
msgstr ""

#: includes/generator/class-wpcode-generator-post-type.php:736
msgid "Edit Others Posts"
msgstr ""

#: includes/generator/class-wpcode-generator-post-type.php:745
#: includes/generator/class-wpcode-generator-taxonomy.php:494
msgid "Rest API"
msgstr ""

#: includes/generator/class-wpcode-generator-post-type.php:751
#: includes/generator/class-wpcode-generator-taxonomy.php:500
msgid "Show in Rest API?"
msgstr ""

#. Translators: Placeholders add link to wp.org docs.
#: includes/generator/class-wpcode-generator-post-type.php:753
msgid "Add the post type to the WordPress wp-json API. %1$sSee Documentation.%2$s"
msgstr ""

#: includes/generator/class-wpcode-generator-post-type.php:766
#: includes/generator/class-wpcode-generator-taxonomy.php:514
msgid "Rest Base"
msgstr ""

#. Translators: Placeholders add link to wp.org docs.
#: includes/generator/class-wpcode-generator-post-type.php:768
msgid "The base slug that this post type will use in the REST API. %1$sSee Documentation.%2$s"
msgstr ""

#: includes/generator/class-wpcode-generator-post-type.php:777
#: includes/generator/class-wpcode-generator-taxonomy.php:524
msgid "Rest Controller Class"
msgstr ""

#. Translators: Placeholders add link to wp.org docs.
#: includes/generator/class-wpcode-generator-post-type.php:779
msgid "The name of a custom Rest Controller class instead of WP_REST_Posts_Controller. %1$sSee Documentation.%2$s"
msgstr ""

#: includes/generator/class-wpcode-generator-query.php:42
msgid "WP_Query"
msgstr ""

#: includes/generator/class-wpcode-generator-query.php:43
msgid "Generate a snippet using WP_Query to load posts from your website."
msgstr ""

#. Translators: Placeholders add links to the wordpress.org references.
#: includes/generator/class-wpcode-generator-query.php:82
msgid "This generator makes it easy for you to create custom queries using %1$sWP_Query%2$s which you can then extend to display posts or similar."
msgstr ""

#: includes/generator/class-wpcode-generator-query.php:108
msgid "You can use this generator to get quickly started with a query for all the posts of an author and display them using the shortcode functionality of WPCode or automatically displaying the posts using the auto-insert option."
msgstr ""

#: includes/generator/class-wpcode-generator-query.php:120
msgid "Query variable name"
msgstr ""

#: includes/generator/class-wpcode-generator-query.php:121
msgid "If you want to use something more specific. The leading $ will be automatically added."
msgstr ""

#: includes/generator/class-wpcode-generator-query.php:131
msgid "Include loop"
msgstr ""

#: includes/generator/class-wpcode-generator-query.php:132
msgid "Select yes if you want to include an empty loop of the results that you can fill in for output."
msgstr ""

#: includes/generator/class-wpcode-generator-query.php:144
msgid "IDs & Parents"
msgstr ""

#: includes/generator/class-wpcode-generator-query.php:150
msgid "Post ID(s)"
msgstr ""

#: includes/generator/class-wpcode-generator-query.php:151
msgid "Query a specific post ID or comma-separated list of ids. Cannot be combined with \"Post ID not in\" below."
msgstr ""

#: includes/generator/class-wpcode-generator-query.php:158
msgid "Post ID not in"
msgstr ""

#: includes/generator/class-wpcode-generator-query.php:159
msgid "Post ids to exclude from this query. Cannot be combined with \"Post ID(s)\" above."
msgstr ""

#: includes/generator/class-wpcode-generator-query.php:169
msgid "Post parent ID(s)"
msgstr ""

#: includes/generator/class-wpcode-generator-query.php:170
msgid "Comma-separated list of post parent ids if the post type is hierarchical (like pages)."
msgstr ""

#: includes/generator/class-wpcode-generator-query.php:177
msgid "Post parent not in"
msgstr ""

#: includes/generator/class-wpcode-generator-query.php:178
msgid "Comma-separated list of post parent ids to exclude."
msgstr ""

#: includes/generator/class-wpcode-generator-query.php:188
msgid "Post slugs"
msgstr ""

#: includes/generator/class-wpcode-generator-query.php:189
msgid "Comma-separated list of post slugs to query by."
msgstr ""

#: includes/generator/class-wpcode-generator-query.php:198
msgid "Type & Status"
msgstr ""

#: includes/generator/class-wpcode-generator-query.php:205
msgid "Post type to query by, start typing to get suggestions."
msgstr ""

#: includes/generator/class-wpcode-generator-query.php:215
msgid "Post status"
msgstr ""

#: includes/generator/class-wpcode-generator-query.php:216
msgid "Post status to query by."
msgstr ""

#: includes/generator/class-wpcode-generator-query.php:231
msgid "Author ID(s)"
msgstr ""

#: includes/generator/class-wpcode-generator-query.php:232
msgid "Author ID or comma-separated list of ids."
msgstr ""

#: includes/generator/class-wpcode-generator-query.php:238
msgid "Author not in"
msgstr ""

#: includes/generator/class-wpcode-generator-query.php:239
msgid "Comma-separated list of author ids to exclude from the query."
msgstr ""

#: includes/generator/class-wpcode-generator-query.php:250
msgid "Author name"
msgstr ""

#: includes/generator/class-wpcode-generator-query.php:251
msgid "Use the \"user_nicename\" parameter to query by author."
msgstr ""

#: includes/generator/class-wpcode-generator-query.php:259
msgid "Search"
msgstr ""

#: includes/generator/class-wpcode-generator-query.php:265
msgid "Search term"
msgstr ""

#: includes/generator/class-wpcode-generator-query.php:266
msgid "Search for posts by this search term."
msgstr ""

#: includes/generator/class-wpcode-generator-query.php:274
msgid "Order"
msgstr ""

#: includes/generator/class-wpcode-generator-query.php:280
msgid "Results Order"
msgstr ""

#: includes/generator/class-wpcode-generator-query.php:284
msgid "Descending order (3, 2, 1; c, b, a)"
msgstr ""

#: includes/generator/class-wpcode-generator-query.php:285
msgid "Ascending order (1, 2, 3; a, b, c)"
msgstr ""

#: includes/generator/class-wpcode-generator-query.php:293
msgid "Order by"
msgstr ""

#: includes/generator/class-wpcode-generator-query.php:297
msgid "No order (none)"
msgstr ""

#: includes/generator/class-wpcode-generator-query.php:301
msgid "Slug (name)"
msgstr ""

#: includes/generator/class-wpcode-generator-query.php:302
msgid "Post type (type)"
msgstr ""

#: includes/generator/class-wpcode-generator-query.php:303
msgid "Date (default)"
msgstr ""

#: includes/generator/class-wpcode-generator-query.php:304
msgid "Modified date"
msgstr ""

#: includes/generator/class-wpcode-generator-query.php:305
msgid "Parent id"
msgstr ""

#: includes/generator/class-wpcode-generator-query.php:306
msgid "Random"
msgstr ""

#: includes/generator/class-wpcode-generator-query.php:307
msgid "Comment count"
msgstr ""

#: includes/generator/class-wpcode-generator-query.php:308
msgid "Relevance (for search)"
msgstr ""

#: includes/generator/class-wpcode-generator-query.php:309
msgid "Page Order (menu_order)"
msgstr ""

#: includes/generator/class-wpcode-generator-query.php:311
msgid "Meta value"
msgstr ""

#: includes/generator/class-wpcode-generator-query.php:312
msgid "Numerical meta value (meta_value_num)"
msgstr ""

#: includes/generator/class-wpcode-generator-query.php:313
msgid "Order of ids in post__in"
msgstr ""

#: includes/generator/class-wpcode-generator-query.php:314
msgid "Order of names in post_name__in"
msgstr ""

#: includes/generator/class-wpcode-generator-query.php:315
msgid "Order of ids in post_parent__in"
msgstr ""

#: includes/generator/class-wpcode-generator-query.php:320
#: includes/generator/class-wpcode-generator-query.php:493
msgid "Meta Key"
msgstr ""

#: includes/generator/class-wpcode-generator-query.php:321
msgid "Meta key to use if you choose to order by meta value."
msgstr ""

#: includes/generator/class-wpcode-generator-query.php:331
msgid "Pagination"
msgstr ""

#: includes/generator/class-wpcode-generator-query.php:337
msgid "Use Pagination"
msgstr ""

#: includes/generator/class-wpcode-generator-query.php:340
msgid "Choose no to display all posts (not recommended)."
msgstr ""

#: includes/generator/class-wpcode-generator-query.php:343
msgid "Yes (default)"
msgstr ""

#: includes/generator/class-wpcode-generator-query.php:348
msgid "Page number"
msgstr ""

#: includes/generator/class-wpcode-generator-query.php:349
msgid "Which page to show."
msgstr ""

#: includes/generator/class-wpcode-generator-query.php:358
msgid "Posts per page"
msgstr ""

#: includes/generator/class-wpcode-generator-query.php:359
msgid "How many posts should be displayed per page."
msgstr ""

#: includes/generator/class-wpcode-generator-query.php:365
msgid "Offset"
msgstr ""

#: includes/generator/class-wpcode-generator-query.php:366
msgid "Number of posts to skip."
msgstr ""

#: includes/generator/class-wpcode-generator-query.php:375
msgid "Ignore sticky posts"
msgstr ""

#: includes/generator/class-wpcode-generator-query.php:380
msgid "No (default)"
msgstr ""

#: includes/generator/class-wpcode-generator-query.php:387
#: includes/generator/class-wpcode-generator-query.php:393
#: includes/generator/class-wpcode-generator-taxonomy.php:35
#: includes/generator/class-wpcode-generator-taxonomy.php:112
msgid "Taxonomy"
msgstr ""

#: includes/generator/class-wpcode-generator-query.php:394
msgid "Taxonomy slug that you want to query by."
msgstr ""

#: includes/generator/class-wpcode-generator-query.php:402
msgid "Field"
msgstr ""

#: includes/generator/class-wpcode-generator-query.php:403
msgid "Select taxonomy term by."
msgstr ""

#: includes/generator/class-wpcode-generator-query.php:409
msgid "Term ID (default)"
msgstr ""

#: includes/generator/class-wpcode-generator-query.php:410
msgid "Term Name"
msgstr ""

#: includes/generator/class-wpcode-generator-query.php:411
msgid "Term Slug"
msgstr ""

#: includes/generator/class-wpcode-generator-query.php:412
msgid "Term Taxonomy ID"
msgstr ""

#: includes/generator/class-wpcode-generator-query.php:417
msgid "Terms"
msgstr ""

#: includes/generator/class-wpcode-generator-query.php:418
msgid "Comma-separated list of terms to query by."
msgstr ""

#: includes/generator/class-wpcode-generator-query.php:430
msgid "Include Children"
msgstr ""

#: includes/generator/class-wpcode-generator-query.php:434
msgid "Whether or not to include children for hierarchical taxonomies."
msgstr ""

#: includes/generator/class-wpcode-generator-query.php:443
msgid "Operator"
msgstr ""

#: includes/generator/class-wpcode-generator-query.php:447
msgid "Operator to test relation by."
msgstr ""

#: includes/generator/class-wpcode-generator-query.php:465
msgid "Add another taxonomy"
msgstr ""

#: includes/generator/class-wpcode-generator-query.php:466
msgid "Use the \"Add Taxonomy\" button below to query multiple taxonomies."
msgstr ""

#: includes/generator/class-wpcode-generator-query.php:470
msgid "Add Taxonomy"
msgstr ""

#: includes/generator/class-wpcode-generator-query.php:475
msgid "Tax Relation"
msgstr ""

#: includes/generator/class-wpcode-generator-query.php:479
#: includes/generator/class-wpcode-generator-query.php:579
msgid "AND (default)"
msgstr ""

#: includes/generator/class-wpcode-generator-query.php:480
#: includes/generator/class-wpcode-generator-query.php:580
msgid "OR"
msgstr ""

#: includes/generator/class-wpcode-generator-query.php:494
msgid "The key of the custom field."
msgstr ""

#: includes/generator/class-wpcode-generator-query.php:502
msgid "Meta Value"
msgstr ""

#: includes/generator/class-wpcode-generator-query.php:503
msgid "Value to query the meta by."
msgstr ""

#: includes/generator/class-wpcode-generator-query.php:515
msgid "How to compare the value for querying by meta."
msgstr ""

#: includes/generator/class-wpcode-generator-query.php:542
msgid "Type"
msgstr ""

#: includes/generator/class-wpcode-generator-query.php:543
msgid "Type of custom field."
msgstr ""

#: includes/generator/class-wpcode-generator-query.php:565
msgid "Add another meta query"
msgstr ""

#: includes/generator/class-wpcode-generator-query.php:566
msgid "Use the \"Add Meta\" button below to use multiple meta queries."
msgstr ""

#: includes/generator/class-wpcode-generator-query.php:570
msgid "Add Meta"
msgstr ""

#: includes/generator/class-wpcode-generator-query.php:575
msgid "Relation"
msgstr ""

#: includes/generator/class-wpcode-generator-script.php:35
msgid "Register Scripts"
msgstr ""

#: includes/generator/class-wpcode-generator-script.php:36
msgid "Generate a snippet to load JavaScript scripts using wp_register_script."
msgstr ""

#: includes/generator/class-wpcode-generator-script.php:55
msgid "Using this generator you can create a WordPress function to register and enqueue scripts."
msgstr ""

#. Translators: the placeholders add a link to getboostrap.com.
#: includes/generator/class-wpcode-generator-script.php:80
msgid "You can use this to load external scripts or even scripts from a theme or plugin. For example, you could load %1$sbootstrap%2$s from a cdn."
msgstr ""

#: includes/generator/class-wpcode-generator-script.php:107
#: includes/generator/class-wpcode-generator-style.php:107
msgid "Action (hook)"
msgstr ""

#. Translators: placeholders add links to documentation on wordpress.org.
#: includes/generator/class-wpcode-generator-script.php:110
msgid "Hook used to add the scripts: %1$sfrontend%2$s, %3$sadmin%4$s, %5$slogin%6$s or %7$sembed%8$s."
msgstr ""

#. Translators: placeholder adds the hook name.
#: includes/generator/class-wpcode-generator-script.php:124
#: includes/generator/class-wpcode-generator-style.php:124
msgid "Frontend (%s)"
msgstr ""

#. Translators: placeholder adds the hook name.
#: includes/generator/class-wpcode-generator-script.php:126
#: includes/generator/class-wpcode-generator-style.php:126
msgid "Admin (%s)"
msgstr ""

#. Translators: placeholder adds the hook name.
#: includes/generator/class-wpcode-generator-script.php:128
#: includes/generator/class-wpcode-generator-style.php:128
msgid "Login (%s)"
msgstr ""

#. Translators: placeholder adds the hook name.
#: includes/generator/class-wpcode-generator-script.php:130
#: includes/generator/class-wpcode-generator-style.php:130
msgid "Embed (%s)"
msgstr ""

#: includes/generator/class-wpcode-generator-script.php:137
msgid "Scripts"
msgstr ""

#: includes/generator/class-wpcode-generator-script.php:143
msgid "Script name"
msgstr ""

#: includes/generator/class-wpcode-generator-script.php:144
#: includes/generator/class-wpcode-generator-style.php:144
msgid "This will be used as an identifier in the code, should be lowercase with no spaces."
msgstr ""

#: includes/generator/class-wpcode-generator-script.php:153
msgid "Script URL"
msgstr ""

#: includes/generator/class-wpcode-generator-script.php:154
msgid "The full URL for the script e.g. https://cdn.jsdelivr.net/npm/bootstrap@5.2.0-beta1/dist/js/bootstrap.bundle.min.js."
msgstr ""

#: includes/generator/class-wpcode-generator-script.php:163
#: includes/generator/class-wpcode-generator-style.php:163
msgid "Dependencies"
msgstr ""

#: includes/generator/class-wpcode-generator-script.php:164
msgid "Comma-separated list of scripts required for this script to load, e.g. jquery"
msgstr ""

#: includes/generator/class-wpcode-generator-script.php:173
msgid "Script Version"
msgstr ""

#: includes/generator/class-wpcode-generator-script.php:174
msgid "The script version."
msgstr ""

#: includes/generator/class-wpcode-generator-script.php:186
msgid "Header or Footer?"
msgstr ""

#: includes/generator/class-wpcode-generator-script.php:187
msgid "Load the script in the page head or in the footer."
msgstr ""

#: includes/generator/class-wpcode-generator-script.php:199
msgid "Deregister script?"
msgstr ""

#. Translators: Placeholders for wp.org docs link.
#: includes/generator/class-wpcode-generator-script.php:202
msgid "Should the script be %1$sderegistered%2$s first? (for example, if you are replacing an existing script)."
msgstr ""

#: includes/generator/class-wpcode-generator-script.php:217
msgid "Enqueue script?"
msgstr ""

#. Translators: Placeholders for wp.org docs link.
#: includes/generator/class-wpcode-generator-script.php:220
msgid "Should the script be %1$senqueued%2$s or just registered? (select \"No\" only if you intend enqueueing it later."
msgstr ""

#: includes/generator/class-wpcode-generator-script.php:241
msgid "Add more scripts"
msgstr ""

#: includes/generator/class-wpcode-generator-script.php:242
msgid "Use the \"Add script\" button below to add multiple scripts in this snippet."
msgstr ""

#: includes/generator/class-wpcode-generator-script.php:246
msgid "Add script"
msgstr ""

#: includes/generator/class-wpcode-generator-sidebar.php:35
msgid "Sidebar"
msgstr ""

#: includes/generator/class-wpcode-generator-sidebar.php:36
msgid "Generate a snippet to register a sidebar for your widgets."
msgstr ""

#. Translators: Placeholders add links to the wordpress.org references.
#: includes/generator/class-wpcode-generator-sidebar.php:57
msgid "This generator makes it easy to add sidebars to your website using the \"register_sidebar\" function."
msgstr ""

#: includes/generator/class-wpcode-generator-sidebar.php:83
msgid "You can add multiple widget areas for your footer or post-type specific sidebars."
msgstr ""

#: includes/generator/class-wpcode-generator-sidebar.php:116
msgid "Sidebars"
msgstr ""

#: includes/generator/class-wpcode-generator-sidebar.php:122
msgid "Sidebar Id"
msgstr ""

#: includes/generator/class-wpcode-generator-sidebar.php:123
msgid "This is the sidebar unique id, used in the code, lowercase with no spaces."
msgstr ""

#: includes/generator/class-wpcode-generator-sidebar.php:131
msgid "Add a descriptive label for this sidebar to be used in the admin."
msgstr ""

#: includes/generator/class-wpcode-generator-sidebar.php:139
msgid "A short description for the the admin area."
msgstr ""

#: includes/generator/class-wpcode-generator-sidebar.php:146
#: includes/generator/class-wpcode-generator-widget.php:158
msgid "CSS Class"
msgstr ""

#: includes/generator/class-wpcode-generator-sidebar.php:147
msgid "Use an unique CSS class name for better control over this sidebar's styles in the admin."
msgstr ""

#: includes/generator/class-wpcode-generator-sidebar.php:157
msgid "Before Title"
msgstr ""

#: includes/generator/class-wpcode-generator-sidebar.php:158
msgid "HTML code to add before each widget title."
msgstr ""

#: includes/generator/class-wpcode-generator-sidebar.php:166
msgid "After Title"
msgstr ""

#: includes/generator/class-wpcode-generator-sidebar.php:167
msgid "HTML code to add after each widget title."
msgstr ""

#: includes/generator/class-wpcode-generator-sidebar.php:175
msgid "Before Widget"
msgstr ""

#: includes/generator/class-wpcode-generator-sidebar.php:176
msgid "HTML code to add before each widget."
msgstr ""

#: includes/generator/class-wpcode-generator-sidebar.php:184
msgid "After Widget"
msgstr ""

#: includes/generator/class-wpcode-generator-sidebar.php:185
msgid "HTML code to add after each widget."
msgstr ""

#: includes/generator/class-wpcode-generator-sidebar.php:199
msgid "Add another sidebar"
msgstr ""

#: includes/generator/class-wpcode-generator-sidebar.php:200
msgid "Use the \"Add Sidebar\" button below to add as many sidebars as you need."
msgstr ""

#: includes/generator/class-wpcode-generator-sidebar.php:204
msgid "Add Sidebar"
msgstr ""

#: includes/generator/class-wpcode-generator-style.php:35
msgid "Register Stylesheets"
msgstr ""

#: includes/generator/class-wpcode-generator-style.php:36
msgid "Generate a snippet to load CSS stylesheets using wp_register_style."
msgstr ""

#: includes/generator/class-wpcode-generator-style.php:55
msgid "Using this generator you can create a WordPress function to register and enqueue styles."
msgstr ""

#. Translators: the placeholders add a link to getboostrap.com.
#: includes/generator/class-wpcode-generator-style.php:80
msgid "You can use this to load external styles or even styles from a theme or plugin. For example, you could load %1$sfontawesome%2$s from a cdn."
msgstr ""

#. Translators: placeholders add links to documentation on wordpress.org.
#: includes/generator/class-wpcode-generator-style.php:110
msgid "Hook used to add the styles: %1$sfrontend%2$s, %3$sadmin%4$s, %5$slogin%6$s or %7$sembed%8$s."
msgstr ""

#: includes/generator/class-wpcode-generator-style.php:137
msgid "Styles"
msgstr ""

#: includes/generator/class-wpcode-generator-style.php:143
msgid "Style name"
msgstr ""

#: includes/generator/class-wpcode-generator-style.php:153
msgid "Stylesheet URL"
msgstr ""

#: includes/generator/class-wpcode-generator-style.php:154
msgid "The full URL for the stylesheet e.g. https://cdn.jsdelivr.net/npm/bootstrap@5.2.0-beta1/dist/css/bootstrap.min.css."
msgstr ""

#: includes/generator/class-wpcode-generator-style.php:164
msgid "Comma-separated list of styles required for this style to load, e.g. jquery"
msgstr ""

#: includes/generator/class-wpcode-generator-style.php:173
msgid "Style Version"
msgstr ""

#: includes/generator/class-wpcode-generator-style.php:174
msgid "The style version."
msgstr ""

#: includes/generator/class-wpcode-generator-style.php:186
msgid "Media"
msgstr ""

#. Translators: placeholders add a link to the W3.org reference.
#: includes/generator/class-wpcode-generator-style.php:189
msgid "Load the style %1$smedia type%2$s, usually \"all\"."
msgstr ""

#: includes/generator/class-wpcode-generator-style.php:200
msgid "Deregister style?"
msgstr ""

#. Translators: Placeholders for wp.org docs link.
#: includes/generator/class-wpcode-generator-style.php:203
msgid "Should the style be %1$sderegistered%2$s first? (for example, if you are replacing an existing style)."
msgstr ""

#: includes/generator/class-wpcode-generator-style.php:218
msgid "Enqueue style?"
msgstr ""

#. Translators: Placeholders for wp.org docs link.
#: includes/generator/class-wpcode-generator-style.php:221
msgid "Should the style be %1$senqueued%2$s or just registered? (select \"No\" only if you intend enqueueing it later."
msgstr ""

#: includes/generator/class-wpcode-generator-style.php:242
msgid "Add more styles"
msgstr ""

#: includes/generator/class-wpcode-generator-style.php:243
msgid "Use the \"Add style\" button below to add multiple styles in this snippet."
msgstr ""

#: includes/generator/class-wpcode-generator-style.php:247
msgid "Add style"
msgstr ""

#: includes/generator/class-wpcode-generator-taxonomy.php:36
msgid "Create a custom taxonomy for your posts using this generator."
msgstr ""

#: includes/generator/class-wpcode-generator-taxonomy.php:55
msgid "Use this generator to create custom taxonomies for your WordPress site."
msgstr ""

#: includes/generator/class-wpcode-generator-taxonomy.php:78
msgid "Use this to add more taxonomies to posts or custom post types. For example, if you used the Post Type generator to create an Artist post type you can use this one to create a Genre taxonomy."
msgstr ""

#: includes/generator/class-wpcode-generator-taxonomy.php:118
msgid "Taxonomy Key"
msgstr ""

#: includes/generator/class-wpcode-generator-taxonomy.php:119
msgid "Name of taxonomy used in the code, lowercase maximum 20 characters."
msgstr ""

#: includes/generator/class-wpcode-generator-taxonomy.php:129
msgid "Name (Singular)"
msgstr ""

#: includes/generator/class-wpcode-generator-taxonomy.php:130
msgid "The singular taxonomy name (e.g. Genre, Year)."
msgstr ""

#: includes/generator/class-wpcode-generator-taxonomy.php:138
msgid "The taxonomy plural name (e.g. Genres, Years)."
msgstr ""

#: includes/generator/class-wpcode-generator-taxonomy.php:148
msgid "Link To Post Type(s)"
msgstr ""

#: includes/generator/class-wpcode-generator-taxonomy.php:149
msgid "Comma-separated list of Post Types (e.g. post, page)"
msgstr ""

#: includes/generator/class-wpcode-generator-taxonomy.php:157
msgid "Hierarchical taxonomies can have descendants."
msgstr ""

#: includes/generator/class-wpcode-generator-taxonomy.php:161
msgid "No, like tags"
msgstr ""

#: includes/generator/class-wpcode-generator-taxonomy.php:162
msgid "Yes, like categories"
msgstr ""

#: includes/generator/class-wpcode-generator-taxonomy.php:196
msgid "Parent Item colon"
msgstr ""

#: includes/generator/class-wpcode-generator-taxonomy.php:203
msgid "New Item Name"
msgstr ""

#: includes/generator/class-wpcode-generator-taxonomy.php:241
msgid "Separate Items with commas"
msgstr ""

#: includes/generator/class-wpcode-generator-taxonomy.php:248
msgid "Add or Remove Items"
msgstr ""

#: includes/generator/class-wpcode-generator-taxonomy.php:255
msgid "Choose From Most Used"
msgstr ""

#: includes/generator/class-wpcode-generator-taxonomy.php:265
msgid "Popular Items"
msgstr ""

#: includes/generator/class-wpcode-generator-taxonomy.php:272
msgid "Search Items"
msgstr ""

#: includes/generator/class-wpcode-generator-taxonomy.php:286
msgid "No items"
msgstr ""

#. Translators: Placeholders add a link to the wp.org documentation page.
#: includes/generator/class-wpcode-generator-taxonomy.php:317
msgid "Should this taxonomy be %1$svisible to authors%2$s?"
msgstr ""

#: includes/generator/class-wpcode-generator-taxonomy.php:331
msgid "Should this taxonomy have an User Interface for managing?"
msgstr ""

#: includes/generator/class-wpcode-generator-taxonomy.php:341
msgid "Show Admin Column"
msgstr ""

#: includes/generator/class-wpcode-generator-taxonomy.php:342
msgid "Should this taxonomy add a column in the list of associated post types?"
msgstr ""

#: includes/generator/class-wpcode-generator-taxonomy.php:355
msgid "Show Tag Cloud"
msgstr ""

#: includes/generator/class-wpcode-generator-taxonomy.php:356
msgid "Should this taxonomy be visible in the tag cloud widget?"
msgstr ""

#: includes/generator/class-wpcode-generator-taxonomy.php:366
msgid "Show in Navigation Menus"
msgstr ""

#: includes/generator/class-wpcode-generator-taxonomy.php:367
msgid "Should this taxonomy be available in menus (Appearance > Menus)."
msgstr ""

#: includes/generator/class-wpcode-generator-taxonomy.php:385
msgid "Permalink Rewrite"
msgstr ""

#: includes/generator/class-wpcode-generator-taxonomy.php:386
msgid "Use Default Permalinks, disable automatic rewriting or use custom permalinks."
msgstr ""

#: includes/generator/class-wpcode-generator-taxonomy.php:390
msgid "Default (taxonomy key)"
msgstr ""

#: includes/generator/class-wpcode-generator-taxonomy.php:401
msgid "If you selected custom permalinks use this field for the rewrite base, e.g. taxonomy in https://yoursite.com/taxonomy"
msgstr ""

#: includes/generator/class-wpcode-generator-taxonomy.php:411
msgid "Prepend permastruct"
msgstr ""

#: includes/generator/class-wpcode-generator-taxonomy.php:412
msgid "Should the permastruct be prepended to the url (with_front parameter)."
msgstr ""

#: includes/generator/class-wpcode-generator-taxonomy.php:422
msgid "Hierarchical URL Slug"
msgstr ""

#: includes/generator/class-wpcode-generator-taxonomy.php:423
msgid "For hierarchical taxonomies use the whole hierarchy in the URL?"
msgstr ""

#. Translators: Placeholders add link to wp.org docs.
#: includes/generator/class-wpcode-generator-taxonomy.php:443
msgid "User capabilities in relation to this taxonomy. %1$sSee Documentation.%2$s"
msgstr ""

#: includes/generator/class-wpcode-generator-taxonomy.php:461
msgid "Edit Terms"
msgstr ""

#: includes/generator/class-wpcode-generator-taxonomy.php:468
msgid "Delete Terms"
msgstr ""

#: includes/generator/class-wpcode-generator-taxonomy.php:478
msgid "Manage Terms"
msgstr ""

#: includes/generator/class-wpcode-generator-taxonomy.php:485
msgid "Assign Terms"
msgstr ""

#: includes/generator/class-wpcode-generator-taxonomy.php:501
msgid "Add the taxonomy to the WordPress wp-json API."
msgstr ""

#: includes/generator/class-wpcode-generator-taxonomy.php:515
msgid "The base slug that this taxonomy will use in the REST API."
msgstr ""

#: includes/generator/class-wpcode-generator-taxonomy.php:525
msgid "The name of a custom Rest Controller class instead of WP_REST_Terms_Controller."
msgstr ""

#: includes/generator/class-wpcode-generator-widget.php:35
#: includes/generator/class-wpcode-generator-widget.php:123
msgid "Widget"
msgstr ""

#: includes/generator/class-wpcode-generator-widget.php:36
msgid "Generate a snippet to register a custom sidebar widget for your website."
msgstr ""

#: includes/generator/class-wpcode-generator-widget.php:55
msgid "Using this generator you can easily add a custom sidebar widget with settings."
msgstr ""

#: includes/generator/class-wpcode-generator-widget.php:78
msgid "Sidebar widgets are very useful when you want to display the same content on multiple pages, you can create a widget with contact methods, for example and fields to set a phone number, email, etc."
msgstr ""

#: includes/generator/class-wpcode-generator-widget.php:90
msgid "Class name"
msgstr ""

#: includes/generator/class-wpcode-generator-widget.php:91
msgid "Make this unique to avoid conflicts with other similar snippets."
msgstr ""

#: includes/generator/class-wpcode-generator-widget.php:102
msgid "Prefix"
msgstr ""

#: includes/generator/class-wpcode-generator-widget.php:103
msgid "Used to prefix all the field names."
msgstr ""

#: includes/generator/class-wpcode-generator-widget.php:114
msgid "Optional textdomain for translations."
msgstr ""

#: includes/generator/class-wpcode-generator-widget.php:129
msgid "Widget ID"
msgstr ""

#: includes/generator/class-wpcode-generator-widget.php:130
msgid "Unique id for the widget, used in the code."
msgstr ""

#: includes/generator/class-wpcode-generator-widget.php:138
msgid "Widget Title"
msgstr ""

#: includes/generator/class-wpcode-generator-widget.php:139
msgid "The title of the widget (displayed in the admin)."
msgstr ""

#: includes/generator/class-wpcode-generator-widget.php:151
msgid "Description used in the admin to explain what the widget is used for."
msgstr ""

#: includes/generator/class-wpcode-generator-widget.php:159
msgid "Widget-specific CSS class name."
msgstr ""

#: includes/generator/class-wpcode-generator-widget.php:168
msgid "Widget Output Code"
msgstr ""

#: includes/generator/class-wpcode-generator-widget.php:169
msgid "PHP Code used for outputting the fields in the frontend. If left empty it will output the fields values in a simple list."
msgstr ""

#: includes/generator/class-wpcode-generator-widget.php:177
msgid "Fields"
msgstr ""

#: includes/generator/class-wpcode-generator-widget.php:183
msgid "Field Type"
msgstr ""

#: includes/generator/class-wpcode-generator-widget.php:184
msgid "Pick the type of field you want to use for this setting."
msgstr ""

#: includes/generator/class-wpcode-generator-widget.php:188
msgid "Text"
msgstr ""

#: includes/generator/class-wpcode-generator-widget.php:189
msgid "Email"
msgstr ""

#: includes/generator/class-wpcode-generator-widget.php:190
msgid "URL"
msgstr ""

#: includes/generator/class-wpcode-generator-widget.php:191
msgid "Number"
msgstr ""

#: includes/generator/class-wpcode-generator-widget.php:192
msgid "Textarea"
msgstr ""

#: includes/generator/class-wpcode-generator-widget.php:193
msgid "Select"
msgstr ""

#: includes/generator/class-wpcode-generator-widget.php:194
msgid "Checkboxes"
msgstr ""

#: includes/generator/class-wpcode-generator-widget.php:195
msgid "Radio"
msgstr ""

#: includes/generator/class-wpcode-generator-widget.php:201
msgid "Field ID"
msgstr ""

#: includes/generator/class-wpcode-generator-widget.php:202
msgid "Unique id for this field, used in the code."
msgstr ""

#: includes/generator/class-wpcode-generator-widget.php:209
msgid "Field Label"
msgstr ""

#: includes/generator/class-wpcode-generator-widget.php:210
msgid "The label displayed next to this field in the admin form."
msgstr ""

#: includes/generator/class-wpcode-generator-widget.php:224
msgid "Display a short descriptive text below this field."
msgstr ""

#: includes/generator/class-wpcode-generator-widget.php:231
msgid "Default"
msgstr ""

#: includes/generator/class-wpcode-generator-widget.php:232
msgid "Set the default value for this field."
msgstr ""

#: includes/generator/class-wpcode-generator-widget.php:240
msgid "Use value|label for each line to add options for select, checkbox or radio."
msgstr ""

#: includes/generator/class-wpcode-generator-widget.php:250
msgid "Add another field"
msgstr ""

#: includes/generator/class-wpcode-generator-widget.php:251
msgid "Use the \"Add field\" button below to add as many fields as you need."
msgstr ""

#: includes/generator/class-wpcode-generator-widget.php:255
msgid "Add field"
msgstr ""

#: includes/helpers.php:166
msgctxt "Copy to clipboard"
msgid "Copy"
msgstr ""

#: includes/helpers.php:180
msgid "Contains"
msgstr ""

#: includes/helpers.php:181
msgid "Doesn't Contain"
msgstr ""

#: includes/helpers.php:182
msgid "Is"
msgstr ""

#: includes/helpers.php:183
msgid "Is not"
msgstr ""

#: includes/helpers.php:184
msgid "Is Before"
msgstr ""

#: includes/helpers.php:185
msgid "Is After"
msgstr ""

#: includes/helpers.php:186
msgid "Is on or Before"
msgstr ""

#: includes/helpers.php:187
msgid "Is on or After"
msgstr ""

#: includes/lite/admin/class-wpcode-admin-page-loader-lite.php:35
#: includes/lite/admin/class-wpcode-admin-page-loader-lite.php:36
#: includes/lite/class-wpcode-admin-bar-info-lite.php:71
msgid "Upgrade to Pro"
msgstr ""

#: includes/lite/admin/class-wpcode-connect.php:67
msgid "You are not allowed to install plugins."
msgstr ""

#: includes/lite/admin/class-wpcode-connect.php:73
msgid "Please enter your license key to connect."
msgstr ""

#: includes/lite/admin/class-wpcode-connect.php:77
msgid "Only the Lite version can be upgraded."
msgstr ""

#: includes/lite/admin/class-wpcode-connect.php:95
msgid "WPCode Pro is installed but not activated."
msgstr ""

#: includes/lite/admin/class-wpcode-connect.php:148
msgid "There was an error while installing an upgrade. Please download the plugin from wpcode.com and install it manually."
msgstr ""

#: includes/lite/admin/class-wpcode-connect.php:181
#: includes/lite/admin/class-wpcode-connect.php:194
#: includes/lite/admin/class-wpcode-connect.php:255
msgid "Plugin installed & activated."
msgstr ""

#: includes/lite/admin/class-wpcode-connect.php:202
msgid "There was an error while installing an upgrade. Please check file system permissions and try again. Also, you can download the plugin from wpcode.com and install it manually."
msgstr ""

#: includes/lite/admin/class-wpcode-connect.php:229
msgid "No key provided."
msgstr ""

#: includes/lite/admin/class-wpcode-connect.php:259
msgid "Pro version installed but needs to be activated on the Plugins page inside your WordPress admin."
msgstr ""

#: includes/lite/admin/class-wpcode-metabox-snippets-lite.php:64
msgid "Choose the snippets you want to run on this page. Please note: only active snippets will be executed."
msgstr ""

#: includes/lite/admin/class-wpcode-metabox-snippets-lite.php:69
msgid "Select snippets"
msgstr ""

#: includes/lite/admin/class-wpcode-metabox-snippets-lite.php:75
msgid "Search snippets"
msgstr ""

#: includes/lite/admin/class-wpcode-metabox-snippets-lite.php:81
msgid "Load more snippets"
msgstr ""

#: includes/lite/admin/class-wpcode-metabox-snippets-lite.php:88
msgid "+ Choose Snippet"
msgstr ""

#: includes/lite/admin/class-wpcode-metabox-snippets-lite.php:140
#: includes/lite/admin/class-wpcode-metabox-snippets-lite.php:283
#: includes/lite/class-wpcode-admin-bar-info-lite.php:91
msgid "Page Scripts is a Pro Feature"
msgstr ""

#: includes/lite/admin/class-wpcode-metabox-snippets-lite.php:143
#: includes/lite/admin/class-wpcode-metabox-snippets-lite.php:286
#: includes/lite/class-wpcode-admin-bar-info-lite.php:92
msgid "While you can always use global snippets, in the PRO version you can easily add page-specific scripts and snippets directly from the post edit screen."
msgstr ""

#: includes/lite/admin/class-wpcode-metabox-snippets-lite.php:146
#: includes/lite/admin/class-wpcode-metabox-snippets-lite.php:289
#: includes/lite/class-wpcode-admin-bar-info-lite.php:93
msgid "Upgrade to Pro and Unlock Page Scripts"
msgstr ""

#. Translators: placeholder for the name of the section (header or footer).
#: includes/lite/admin/class-wpcode-metabox-snippets-lite.php:171
msgid "Add scripts below to the %s section of this page."
msgstr ""

#. Translators: placeholder for the name of the section (header or footer).
#: includes/lite/admin/class-wpcode-metabox-snippets-lite.php:182
msgid "Disable global %s scripts on this page"
msgstr ""

#. Translators: placeholder for the name of the section (header or footer).
#: includes/lite/admin/class-wpcode-metabox-snippets-lite.php:193
msgid "%s - any device type"
msgstr ""

#: includes/lite/admin/class-wpcode-metabox-snippets-lite.php:309
msgid "As you make changes to your page scripts and save, you will get a list of previous versions with all the changes made in each revision. You can compare revisions to the current version or see changes as they have been saved by going through each revision. Any of the revisions can then be restored as needed without interfering with your post/page."
msgstr ""

#: includes/lite/admin/class-wpcode-metabox-snippets-lite.php:316
msgid "Upgrade to WPCode Pro today and start tracking revisions and see exactly who, when and which changes were made to your page scripts."
msgstr ""

#. Translators: more here is used in the sense of "get access to more snippets" and gets replaced with the number of snippets if the library items are loaded correctly.
#: includes/lite/admin/notices.php:37
msgid "more"
msgstr ""

#. Translators: %1$s and %2$s add a link to the settings page. %3$s and %4$s make the text bold. %6$s is replaced with the number of snippets and %5$s adds a "new" icon.
#: includes/lite/admin/notices.php:42
msgid "%5$s%1$sConnect to the WPCode Library%2$s to get access to %3$s%6$s FREE snippets%4$s!"
msgstr ""

#. Translators: %1$s and %2$s add a link to the upgrade page. %3$s and %4$s make the text bold.
#: includes/lite/admin/notices.php:88
msgid "%3$sYou're using WPCode Lite%4$s. To unlock more features consider %1$supgrading to Pro%2$s."
msgstr ""

#: includes/lite/admin/notices.php:116
msgid "Get WPCode Pro and Unlock all the Powerful Features"
msgstr ""

#: includes/lite/admin/notices.php:119
msgid "Save & Reuse snippets in your private Cloud Library"
msgstr ""

#: includes/lite/admin/notices.php:120
msgid "Add page-specific scripts when editing a post/page."
msgstr ""

#: includes/lite/admin/notices.php:121
msgid "Track all snippet changes with Advanced Code Revisions"
msgstr ""

#: includes/lite/admin/notices.php:124
msgid "Load snippets by device (mobile/desktop) with 1-click."
msgstr ""

#: includes/lite/admin/notices.php:125
msgid "Easily insert and reuse content with Custom Shortcodes."
msgstr ""

#: includes/lite/admin/notices.php:126
msgid "Precisely track eCommerce conversions for WooCommerce and EDD."
msgstr ""

#: includes/lite/admin/notices.php:132
msgid "Get WPCode Pro Today and Unlock all the Powerful Features »"
msgstr ""

#. Translators: Placeholders make the text bold.
#: includes/lite/admin/notices.php:137
msgid "%1$sBonus:%2$s WPCode Lite users get %3$s$50 off regular price%4$s, automatically applied at checkout"
msgstr ""

#: includes/lite/auto-insert/class-wpcode-auto-insert-anywhere.php:50
msgid "Anywhere (CSS Selector)"
msgstr ""

#: includes/lite/auto-insert/class-wpcode-auto-insert-anywhere.php:53
msgid "Before HTML Element"
msgstr ""

#: includes/lite/auto-insert/class-wpcode-auto-insert-anywhere.php:54
msgid "Insert snippet before the HTML element specified by the CSS selector."
msgstr ""

#: includes/lite/auto-insert/class-wpcode-auto-insert-anywhere.php:57
msgid "After HTML Element"
msgstr ""

#: includes/lite/auto-insert/class-wpcode-auto-insert-anywhere.php:58
msgid "Insert snippet after the HTML element specified by the CSS selector."
msgstr ""

#: includes/lite/auto-insert/class-wpcode-auto-insert-anywhere.php:61
msgid "At the start of HTML Element"
msgstr ""

#: includes/lite/auto-insert/class-wpcode-auto-insert-anywhere.php:62
msgid "Insert snippet before the content of the HTML element specified by CSS selector."
msgstr ""

#: includes/lite/auto-insert/class-wpcode-auto-insert-anywhere.php:65
msgid "At the end of HTML Element"
msgstr ""

#: includes/lite/auto-insert/class-wpcode-auto-insert-anywhere.php:66
msgid "Insert snippet after the content of the HTML element specified by CSS selector."
msgstr ""

#: includes/lite/auto-insert/class-wpcode-auto-insert-anywhere.php:69
msgid "Replace HTML Element"
msgstr ""

#: includes/lite/auto-insert/class-wpcode-auto-insert-anywhere.php:70
msgid "Completely replace the HTML element specified by the CSS selector with the output of this snippet."
msgstr ""

#: includes/lite/auto-insert/class-wpcode-auto-insert-anywhere.php:73
msgid "Insert Anywhere by CSS Selector is a Premium feature"
msgstr ""

#: includes/lite/auto-insert/class-wpcode-auto-insert-anywhere.php:74
msgid "Upgrade to PRO today and insert snippets anywhere on your site using CSS selectors to target any HTML element."
msgstr ""

#: includes/lite/auto-insert/class-wpcode-auto-insert-content.php:54
msgid "Insert After # Words"
msgstr ""

#: includes/lite/auto-insert/class-wpcode-auto-insert-content.php:55
msgid "Insert snippet after a minimum number of words."
msgstr ""

#: includes/lite/auto-insert/class-wpcode-auto-insert-content.php:58
msgid "Insert Every # Words"
msgstr ""

#: includes/lite/auto-insert/class-wpcode-auto-insert-content.php:59
msgid "Insert snippet every # number of words."
msgstr ""

#: includes/lite/auto-insert/class-wpcode-auto-insert-content.php:62
msgid "Word-based content locations are a PRO feature"
msgstr ""

#: includes/lite/auto-insert/class-wpcode-auto-insert-content.php:63
msgid "Upgrade to PRO today and get access to automatic word-count based insert locations."
msgstr ""

#: includes/lite/auto-insert/class-wpcode-auto-insert-edd.php:53
msgid "Before the Purchase Button"
msgstr ""

#: includes/lite/auto-insert/class-wpcode-auto-insert-edd.php:54
msgid "Insert snippet before the EDD purchase button."
msgstr ""

#: includes/lite/auto-insert/class-wpcode-auto-insert-edd.php:57
msgid "After the Purchase Button"
msgstr ""

#: includes/lite/auto-insert/class-wpcode-auto-insert-edd.php:58
msgid "Insert snippet after the EDD purchase button."
msgstr ""

#: includes/lite/auto-insert/class-wpcode-auto-insert-edd.php:61
msgid "Before the Single Download"
msgstr ""

#: includes/lite/auto-insert/class-wpcode-auto-insert-edd.php:62
msgid "Insert snippet before the single EDD download content."
msgstr ""

#: includes/lite/auto-insert/class-wpcode-auto-insert-edd.php:65
msgid "After the Single Download"
msgstr ""

#: includes/lite/auto-insert/class-wpcode-auto-insert-edd.php:66
msgid "Insert snippet after the single EDD download content."
msgstr ""

#: includes/lite/auto-insert/class-wpcode-auto-insert-edd.php:69
#: includes/lite/auto-insert/class-wpcode-auto-insert-woocommerce.php:78
msgid "Before the Cart"
msgstr ""

#: includes/lite/auto-insert/class-wpcode-auto-insert-edd.php:70
msgid "Insert snippet before the EDD cart."
msgstr ""

#: includes/lite/auto-insert/class-wpcode-auto-insert-edd.php:73
#: includes/lite/auto-insert/class-wpcode-auto-insert-woocommerce.php:82
msgid "After the Cart"
msgstr ""

#: includes/lite/auto-insert/class-wpcode-auto-insert-edd.php:74
msgid "Insert snippet after the EDD cart."
msgstr ""

#: includes/lite/auto-insert/class-wpcode-auto-insert-edd.php:77
msgid "Before the Checkout Cart"
msgstr ""

#: includes/lite/auto-insert/class-wpcode-auto-insert-edd.php:78
msgid "Insert snippet before the EDD cart on the checkout page."
msgstr ""

#: includes/lite/auto-insert/class-wpcode-auto-insert-edd.php:81
msgid "After the Checkout Cart"
msgstr ""

#: includes/lite/auto-insert/class-wpcode-auto-insert-edd.php:82
msgid "Insert snippet after the EDD cart on the checkout page."
msgstr ""

#: includes/lite/auto-insert/class-wpcode-auto-insert-edd.php:85
#: includes/lite/auto-insert/class-wpcode-auto-insert-woocommerce.php:86
msgid "Before the Checkout Form"
msgstr ""

#: includes/lite/auto-insert/class-wpcode-auto-insert-edd.php:86
msgid "Insert snippet before the EDD checkout form on the checkout page."
msgstr ""

#: includes/lite/auto-insert/class-wpcode-auto-insert-edd.php:89
#: includes/lite/auto-insert/class-wpcode-auto-insert-woocommerce.php:90
msgid "After the Checkout Form"
msgstr ""

#: includes/lite/auto-insert/class-wpcode-auto-insert-edd.php:90
msgid "Insert snippet after the EDD checkout form on the checkout page"
msgstr ""

#: includes/lite/auto-insert/class-wpcode-auto-insert-edd.php:93
msgid "Easy Digital Downloads Locations are a PRO feature"
msgstr ""

#: includes/lite/auto-insert/class-wpcode-auto-insert-edd.php:94
#: includes/lite/auto-insert/class-wpcode-auto-insert-memberpress.php:86
#: includes/lite/auto-insert/class-wpcode-auto-insert-woocommerce.php:107
msgid "Upgrade to PRO today and get access to advanced eCommerce auto-insert locations and conditional logic rules for your needs."
msgstr ""

#: includes/lite/auto-insert/class-wpcode-auto-insert-memberpress.php:53
msgid "Before the Registration Form"
msgstr ""

#: includes/lite/auto-insert/class-wpcode-auto-insert-memberpress.php:54
msgid "Insert snippet before the MemberPress registration form used for checkout."
msgstr ""

#: includes/lite/auto-insert/class-wpcode-auto-insert-memberpress.php:57
msgid "Before Checkout Submit Button"
msgstr ""

#: includes/lite/auto-insert/class-wpcode-auto-insert-memberpress.php:58
msgid "Insert snippet right before the MemberPress checkout submit button."
msgstr ""

#: includes/lite/auto-insert/class-wpcode-auto-insert-memberpress.php:61
msgid "Before Checkout Coupon Field"
msgstr ""

#: includes/lite/auto-insert/class-wpcode-auto-insert-memberpress.php:62
msgid "Insert snippet before the MemberPress checkout coupon field."
msgstr ""

#: includes/lite/auto-insert/class-wpcode-auto-insert-memberpress.php:65
msgid "Before Account First Name"
msgstr ""

#: includes/lite/auto-insert/class-wpcode-auto-insert-memberpress.php:66
msgid "Insert snippet to the Home tab of the MemberPress Account page before First Name field."
msgstr ""

#: includes/lite/auto-insert/class-wpcode-auto-insert-memberpress.php:69
msgid "Before Subscriptions Content"
msgstr ""

#: includes/lite/auto-insert/class-wpcode-auto-insert-memberpress.php:70
msgid "Insert snippet at the beginning of the Subscriptions tab on the MemberPress Account page."
msgstr ""

#: includes/lite/auto-insert/class-wpcode-auto-insert-memberpress.php:73
msgid "Before Login Form Submit"
msgstr ""

#: includes/lite/auto-insert/class-wpcode-auto-insert-memberpress.php:74
msgid "Insert snippet before the Remember Me checkbox on the MemberPress Login page."
msgstr ""

#: includes/lite/auto-insert/class-wpcode-auto-insert-memberpress.php:77
msgid "Before the Unauthorized Message"
msgstr ""

#: includes/lite/auto-insert/class-wpcode-auto-insert-memberpress.php:78
msgid "Insert a snippet before the notice that access to the content is unauthorized. "
msgstr ""

#: includes/lite/auto-insert/class-wpcode-auto-insert-memberpress.php:81
msgid "After the Unauthorized Message"
msgstr ""

#: includes/lite/auto-insert/class-wpcode-auto-insert-memberpress.php:82
msgid "Insert a snippet after the notice that access to the content is unauthorized. "
msgstr ""

#: includes/lite/auto-insert/class-wpcode-auto-insert-memberpress.php:85
msgid "MemberPress Locations are a PRO feature"
msgstr ""

#: includes/lite/auto-insert/class-wpcode-auto-insert-woocommerce.php:54
msgid "Before the List of Products"
msgstr ""

#: includes/lite/auto-insert/class-wpcode-auto-insert-woocommerce.php:55
msgid "Insert snippet before the list of products on a WooCommerce page."
msgstr ""

#: includes/lite/auto-insert/class-wpcode-auto-insert-woocommerce.php:58
msgid "After the List of Products"
msgstr ""

#: includes/lite/auto-insert/class-wpcode-auto-insert-woocommerce.php:59
msgid "Insert snippet after the list of products on a WooCommerce page."
msgstr ""

#: includes/lite/auto-insert/class-wpcode-auto-insert-woocommerce.php:62
msgid "Before the Single Product"
msgstr ""

#: includes/lite/auto-insert/class-wpcode-auto-insert-woocommerce.php:63
msgid "Insert snippet before the content on the single WooCommerce product page."
msgstr ""

#: includes/lite/auto-insert/class-wpcode-auto-insert-woocommerce.php:66
msgid "After the Single Product"
msgstr ""

#: includes/lite/auto-insert/class-wpcode-auto-insert-woocommerce.php:67
msgid "Insert snippet after the content on the single WooCommerce product page."
msgstr ""

#: includes/lite/auto-insert/class-wpcode-auto-insert-woocommerce.php:70
msgid "Before the Single Product Summary"
msgstr ""

#: includes/lite/auto-insert/class-wpcode-auto-insert-woocommerce.php:71
msgid "Insert snippet before the product summary on the single WooCommerce product page."
msgstr ""

#: includes/lite/auto-insert/class-wpcode-auto-insert-woocommerce.php:74
msgid "After the Single Product Summary"
msgstr ""

#: includes/lite/auto-insert/class-wpcode-auto-insert-woocommerce.php:75
msgid "Insert snippet after the product summary on the single WooCommerce product page."
msgstr ""

#: includes/lite/auto-insert/class-wpcode-auto-insert-woocommerce.php:79
msgid "Insert snippet before the cart on WooCommerce pages."
msgstr ""

#: includes/lite/auto-insert/class-wpcode-auto-insert-woocommerce.php:83
msgid "Insert snippet after the cart on WooCommerce pages."
msgstr ""

#: includes/lite/auto-insert/class-wpcode-auto-insert-woocommerce.php:87
msgid "Insert snippet before the checkout form on the WooCommerce checkout page."
msgstr ""

#: includes/lite/auto-insert/class-wpcode-auto-insert-woocommerce.php:91
msgid "Insert snippet after the checkout form on the WooCommerce checkout page."
msgstr ""

#: includes/lite/auto-insert/class-wpcode-auto-insert-woocommerce.php:94
msgid "Before Checkout Payment Button"
msgstr ""

#: includes/lite/auto-insert/class-wpcode-auto-insert-woocommerce.php:95
msgid "Insert snippet before the checkout payment button on the WooCommerce checkout page."
msgstr ""

#: includes/lite/auto-insert/class-wpcode-auto-insert-woocommerce.php:98
msgid "After Checkout Payment Button"
msgstr ""

#: includes/lite/auto-insert/class-wpcode-auto-insert-woocommerce.php:99
msgid "Insert snippet after the checkout payment button on the WooCommerce checkout page."
msgstr ""

#: includes/lite/auto-insert/class-wpcode-auto-insert-woocommerce.php:102
msgid "Before the Thank You Page"
msgstr ""

#: includes/lite/auto-insert/class-wpcode-auto-insert-woocommerce.php:103
msgid "Insert snippet before the thank you page content for WooCommerce."
msgstr ""

#: includes/lite/auto-insert/class-wpcode-auto-insert-woocommerce.php:106
msgid "WooCommerce Locations are a PRO feature"
msgstr ""

#: includes/lite/class-wpcode-admin-bar-info-lite.php:29
msgid "Page Scripts"
msgstr ""

#: includes/lite/class-wpcode-smart-tags-lite.php:20
msgid "Smart Tags are a Premium feature"
msgstr ""

#: includes/lite/class-wpcode-smart-tags-lite.php:21
msgid "Upgrade to PRO today and simplify the way you write advanced snippets using smart tags without having to write any PHP code."
msgstr ""

#: includes/lite/conditional-logic/class-wpcode-conditional-device.php:37
msgid "Device"
msgstr ""

#: includes/lite/conditional-logic/class-wpcode-conditional-device.php:49
msgid "Target either desktop or mobile devices."
msgstr ""

#: includes/lite/conditional-logic/class-wpcode-conditional-device.php:52
msgid "Device Type Rules are a Pro Feature"
msgstr ""

#: includes/lite/conditional-logic/class-wpcode-conditional-device.php:53
msgid "Get access to advanced device type conditional logic rules by upgrading to PRO today."
msgstr ""

#: includes/lite/conditional-logic/class-wpcode-conditional-device.php:58
msgid "Desktop"
msgstr ""

#: includes/lite/conditional-logic/class-wpcode-conditional-device.php:63
msgid "Mobile"
msgstr ""

#: includes/lite/conditional-logic/class-wpcode-conditional-device.php:70
msgid "Browser Type"
msgstr ""

#: includes/lite/conditional-logic/class-wpcode-conditional-device.php:71
msgid "Target specific visitor web browsers."
msgstr ""

#: includes/lite/conditional-logic/class-wpcode-conditional-device.php:74
msgid "Browser Type Rules are a Pro Feature"
msgstr ""

#: includes/lite/conditional-logic/class-wpcode-conditional-device.php:75
msgid "Get access to advanced device conditional logic rules by upgrading to PRO today."
msgstr ""

#: includes/lite/conditional-logic/class-wpcode-conditional-device.php:80
msgid "Operating System"
msgstr ""

#: includes/lite/conditional-logic/class-wpcode-conditional-device.php:81
msgid "Target operating systems like Windows, Mac OS or Linux."
msgstr ""

#: includes/lite/conditional-logic/class-wpcode-conditional-device.php:84
msgid "Operating System Rules are a Pro Feature"
msgstr ""

#: includes/lite/conditional-logic/class-wpcode-conditional-device.php:85
msgid "Get access to advanced operating system conditional logic rules by upgrading to PRO today."
msgstr ""

#: includes/lite/conditional-logic/class-wpcode-conditional-device.php:90
msgid "Cookie Name"
msgstr ""

#: includes/lite/conditional-logic/class-wpcode-conditional-device.php:91
msgid "Load or hide a snippet by cookie name."
msgstr ""

#: includes/lite/conditional-logic/class-wpcode-conditional-device.php:94
#: includes/lite/conditional-logic/class-wpcode-conditional-device.php:104
msgid "Cookie-based Rules are a Pro Feature"
msgstr ""

#: includes/lite/conditional-logic/class-wpcode-conditional-device.php:95
#: includes/lite/conditional-logic/class-wpcode-conditional-device.php:105
msgid "Get access to advanced cookie conditional logic rules by upgrading to PRO today."
msgstr ""

#: includes/lite/conditional-logic/class-wpcode-conditional-device.php:100
msgid "Cookie Value"
msgstr ""

#: includes/lite/conditional-logic/class-wpcode-conditional-device.php:101
msgid "Load or hide a snippet by cookie value."
msgstr ""

#: includes/lite/conditional-logic/class-wpcode-conditional-edd.php:48
msgid "EDD Page"
msgstr ""

#: includes/lite/conditional-logic/class-wpcode-conditional-edd.php:49
msgid "Load the snippet on specific Easy Digital Downloads pages."
msgstr ""

#: includes/lite/conditional-logic/class-wpcode-conditional-edd.php:52
msgid "Easy Digital Downloads Page Rules is a Pro Feature"
msgstr ""

#: includes/lite/conditional-logic/class-wpcode-conditional-edd.php:53
msgid "Get access to advanced conditional logic rules for Easy Digital Downloads by upgrading to PRO today."
msgstr ""

#: includes/lite/conditional-logic/class-wpcode-conditional-edd.php:58
#: includes/lite/conditional-logic/class-wpcode-conditional-woocommerce.php:58
msgid "Checkout Page"
msgstr ""

#: includes/lite/conditional-logic/class-wpcode-conditional-edd.php:63
msgid "Success Page"
msgstr ""

#: includes/lite/conditional-logic/class-wpcode-conditional-edd.php:68
msgid "Single Download Page"
msgstr ""

#: includes/lite/conditional-logic/class-wpcode-conditional-edd.php:73
msgid "Download Category Page"
msgstr ""

#: includes/lite/conditional-logic/class-wpcode-conditional-edd.php:78
msgid "Download Tag Page"
msgstr ""

#: includes/lite/conditional-logic/class-wpcode-conditional-location.php:40
msgid "Country"
msgstr ""

#: includes/lite/conditional-logic/class-wpcode-conditional-location.php:41
msgid "Limit loading the snippet based on the visitor's country."
msgstr ""

#: includes/lite/conditional-logic/class-wpcode-conditional-location.php:45
#: includes/lite/conditional-logic/class-wpcode-conditional-location.php:56
msgid "Location Rules are a Pro Feature"
msgstr ""

#: includes/lite/conditional-logic/class-wpcode-conditional-location.php:46
#: includes/lite/conditional-logic/class-wpcode-conditional-location.php:57
msgid "Get access to location-based conditional logic rules by upgrading to PRO today."
msgstr ""

#: includes/lite/conditional-logic/class-wpcode-conditional-location.php:51
msgid "Continent"
msgstr ""

#: includes/lite/conditional-logic/class-wpcode-conditional-location.php:52
msgid "Target entire continents with ease."
msgstr ""

#: includes/lite/conditional-logic/class-wpcode-conditional-memberpress.php:49
msgid "MemberPress Page"
msgstr ""

#: includes/lite/conditional-logic/class-wpcode-conditional-memberpress.php:50
msgid "Load the snippet on specific MemberPress pages."
msgstr ""

#: includes/lite/conditional-logic/class-wpcode-conditional-memberpress.php:53
msgid "MemberPress Page Rules is a Pro Feature"
msgstr ""

#: includes/lite/conditional-logic/class-wpcode-conditional-memberpress.php:54
#: includes/lite/conditional-logic/class-wpcode-conditional-memberpress.php:83
msgid "Get access to advanced conditional logic rules for MemberPress by upgrading to PRO today."
msgstr ""

#: includes/lite/conditional-logic/class-wpcode-conditional-memberpress.php:59
msgid "Registration Page"
msgstr ""

#: includes/lite/conditional-logic/class-wpcode-conditional-memberpress.php:63
#: includes/lite/conditional-logic/class-wpcode-conditional-woocommerce.php:63
msgid "Thank You Page"
msgstr ""

#: includes/lite/conditional-logic/class-wpcode-conditional-memberpress.php:67
msgid "Account Page"
msgstr ""

#: includes/lite/conditional-logic/class-wpcode-conditional-memberpress.php:71
msgid "Login Page"
msgstr ""

#: includes/lite/conditional-logic/class-wpcode-conditional-memberpress.php:77
msgid "User active membership"
msgstr ""

#: includes/lite/conditional-logic/class-wpcode-conditional-memberpress.php:78
msgid "Check if the current user has a specific MemberPress subscription active."
msgstr ""

#: includes/lite/conditional-logic/class-wpcode-conditional-memberpress.php:82
msgid "MemberPress Active Membership Rules is a Pro Feature"
msgstr ""

#: includes/lite/conditional-logic/class-wpcode-conditional-schedule.php:48
msgid "Date"
msgstr ""

#: includes/lite/conditional-logic/class-wpcode-conditional-schedule.php:49
msgid "Check whether today is before or after a date."
msgstr ""

#: includes/lite/conditional-logic/class-wpcode-conditional-schedule.php:53
msgid "Date & Time"
msgstr ""

#: includes/lite/conditional-logic/class-wpcode-conditional-schedule.php:54
msgid "Get more specific by also including a specific time."
msgstr ""

#: includes/lite/conditional-logic/class-wpcode-conditional-schedule.php:58
msgid "Day of the Week"
msgstr ""

#: includes/lite/conditional-logic/class-wpcode-conditional-schedule.php:59
msgid "Load the snippet on specific days of the week."
msgstr ""

#: includes/lite/conditional-logic/class-wpcode-conditional-schedule.php:64
msgid "Current time"
msgstr ""

#: includes/lite/conditional-logic/class-wpcode-conditional-schedule.php:65
msgid "Check whether it's before or after a specific time"
msgstr ""

#: includes/lite/conditional-logic/class-wpcode-conditional-schedule.php:72
msgid "Scheduling rules are a Pro Feature"
msgstr ""

#: includes/lite/conditional-logic/class-wpcode-conditional-schedule.php:73
msgid "Upgrade today and get access to advanced scheduling conditional logic rules."
msgstr ""

#: includes/lite/conditional-logic/class-wpcode-conditional-snippet.php:37
msgid "Snippet"
msgstr ""

#: includes/lite/conditional-logic/class-wpcode-conditional-snippet.php:48
msgid "WPCode Snippet"
msgstr ""

#: includes/lite/conditional-logic/class-wpcode-conditional-snippet.php:49
msgid "Load this snippet based on another snippet being loaded."
msgstr ""

#: includes/lite/conditional-logic/class-wpcode-conditional-snippet.php:53
msgid "WPCode Snippet Loaded Rules are a Pro Feature"
msgstr ""

#: includes/lite/conditional-logic/class-wpcode-conditional-snippet.php:54
msgid "Upgrade today and use conditional logic rules based on other WPCode snippets being loaded."
msgstr ""

#: includes/lite/conditional-logic/class-wpcode-conditional-woocommerce.php:48
msgid "WooCommerce Page"
msgstr ""

#: includes/lite/conditional-logic/class-wpcode-conditional-woocommerce.php:49
msgid "Load the snippet on specific WooCommerce pages."
msgstr ""

#: includes/lite/conditional-logic/class-wpcode-conditional-woocommerce.php:52
msgid "WooCommerce Page Rules is a Pro Feature"
msgstr ""

#: includes/lite/conditional-logic/class-wpcode-conditional-woocommerce.php:53
#: includes/lite/conditional-logic/class-wpcode-conditional-woocommerce.php:106
msgid "Get access to advanced conditional logic rules for WooCommerce by upgrading to PRO today."
msgstr ""

#: includes/lite/conditional-logic/class-wpcode-conditional-woocommerce.php:68
msgid "Cart Page"
msgstr ""

#: includes/lite/conditional-logic/class-wpcode-conditional-woocommerce.php:73
msgid "Single Product Page"
msgstr ""

#: includes/lite/conditional-logic/class-wpcode-conditional-woocommerce.php:78
msgid "Shop Page"
msgstr ""

#: includes/lite/conditional-logic/class-wpcode-conditional-woocommerce.php:83
msgid "Product Category Page"
msgstr ""

#: includes/lite/conditional-logic/class-wpcode-conditional-woocommerce.php:88
msgid "Product Tag Page"
msgstr ""

#: includes/lite/conditional-logic/class-wpcode-conditional-woocommerce.php:93
msgid "My Account Page"
msgstr ""

#: includes/lite/conditional-logic/class-wpcode-conditional-woocommerce.php:100
msgid "WooCommerce Cart"
msgstr ""

#: includes/lite/conditional-logic/class-wpcode-conditional-woocommerce.php:101
msgid "Load the snippet based on the WooCommerce Cart Contents."
msgstr ""

#: includes/lite/conditional-logic/class-wpcode-conditional-woocommerce.php:105
msgid "WooCommerce Cart Contents Rule is a Pro Feature"
msgstr ""

#: includes/safe-mode.php:75
msgid "WPCode is in Safe Mode which means no snippets are getting executed. Please disable any snippets that have caused errors and when done click the button below to exit safe mode."
msgstr ""

#: includes/safe-mode.php:76
msgid "The link will open in a new window so if you are still encountering issues you safely can return to this tab and make further adjustments"
msgstr ""

#: includes/safe-mode.php:78
msgid "Exit safe mode"
msgstr ""
