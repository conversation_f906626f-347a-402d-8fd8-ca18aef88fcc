{"name": "phpoption/phpoption", "description": "Option Type for PHP", "keywords": ["php", "option", "language", "type"], "license": "Apache-2.0", "authors": [{"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "require": {"php": "^5.5.9 || ^7.0 || ^8.0"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.4.1", "phpunit/phpunit": "^4.8.35 || ^5.7.27 || ^6.5.6 || ^7.0 || ^8.0 || ^9.0"}, "autoload": {"psr-4": {"DeliciousBrains\\WPMDB\\Container\\PhpOption\\": "src/PhpOption/"}}, "autoload-dev": {"psr-4": {"DeliciousBrains\\WPMDB\\Container\\PhpOption\\Tests\\": "tests/PhpOption/Tests/"}}, "config": {"preferred-install": "dist"}, "extra": {"branch-alias": {"dev-master": "1.7-dev"}}, "minimum-stability": "dev", "prefer-stable": true}