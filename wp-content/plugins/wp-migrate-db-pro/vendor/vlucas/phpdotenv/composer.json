{"name": "vlucas/phpdotenv", "description": "Loads environment variables from `.env` to `getenv()`, `$_ENV` and `$_SERVER` automagically.", "keywords": ["env", "dotenv", "environment"], "license": "BSD-3-<PERSON><PERSON>", "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/vlucas"}], "require": {"php": "^5.5.9 || ^7.0 || ^8.0", "phpoption/phpoption": "^1.7.3", "symfony/polyfill-ctype": "^1.17"}, "require-dev": {"ext-filter": "*", "ext-pcre": "*", "bamarni/composer-bin-plugin": "^1.4.1", "phpunit/phpunit": "^4.8.36 || ^5.7.27 || ^6.5.14 || ^7.5.20 || ^8.5.30"}, "autoload": {"psr-4": {"DeliciousBrains\\WPMDB\\Container\\Dotenv\\": "src/"}}, "autoload-dev": {"psr-4": {"DeliciousBrains\\WPMDB\\Container\\Dotenv\\Tests\\": "tests/Dotenv/"}}, "suggest": {"ext-filter": "Required to use the boolean validator.", "ext-pcre": "Required to use most of the library."}, "config": {"allow-plugins": {"bamarni/composer-bin-plugin": true}, "preferred-install": "dist"}, "extra": {"bamarni-bin": {"bin-links": true, "forward-command": true}, "branch-alias": {"dev-master": "4.3-dev"}}}