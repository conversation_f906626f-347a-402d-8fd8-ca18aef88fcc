{"name": "php-di/phpdoc-reader", "type": "library", "description": "PhpDocReader parses @var and @param values in PHP docblocks (supports namespaced class names with the same resolution rules as PHP)", "keywords": ["phpdoc", "reflection"], "license": "MIT", "autoload": {"psr-4": {"DeliciousBrains\\WPMDB\\Container\\PhpDocReader\\": "src/PhpDocReader"}}, "autoload-dev": {"psr-4": {"DeliciousBrains\\WPMDB\\Container\\UnitTest\\PhpDocReader\\": "tests/"}}, "require": {"php": ">=5.4.0"}, "require-dev": {"phpunit/phpunit": "~4.6"}}