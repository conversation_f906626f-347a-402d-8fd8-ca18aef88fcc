<?xml version="1.0" encoding="utf-8"?>
<!--
    phpunit -c phpunit.xml
-->
<phpunit backupGlobals="false"
         backupStaticAttributes="false"
         colors="true"
         convertErrorsToExceptions="true"
         convertNoticesToExceptions="true"
         convertWarningsToExceptions="true"
         syntaxCheck="true"
         forceCoversAnnotation="true"
         bootstrap="./vendor/autoload.php">

    <testsuites>
        <testsuite name="unit">
            <directory>./tests/UnitTest/</directory>
        </testsuite>
        <testsuite name="integration">
            <directory>./tests/IntegrationTest/</directory>
        </testsuite>
    </testsuites>

    <filter>
        <whitelist processUncoveredFilesFromWhitelist="true">
            <directory suffix=".php">src</directory>
        </whitelist>
    </filter>

</phpunit>
