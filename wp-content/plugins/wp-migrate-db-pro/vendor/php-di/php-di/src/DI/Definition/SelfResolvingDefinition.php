<?php

namespace DeliciousBrains\WPMDB\Container\DI\Definition;

use DeliciousBrains\WPMDB\Container\Interop\Container\ContainerInterface;
/**
 * Describes a definition that can resolve itself.
 *
 * <AUTHOR> <<EMAIL>>
 */
interface SelfResolvingDefinition
{
    /**
     * Resolve the definition and return the resulting value.
     *
     * @return mixed
     */
    public function resolve(ContainerInterface $container);
    /**
     * Check if a definition can be resolved.
     *
     * @return bool
     */
    public function isResolvable(ContainerInterface $container);
}
