<?php

namespace DeliciousBrains\WPMDB\Container\DI\Factory;

/**
 * Represents the container entry that was requested.
 *
 * Implementations of this interface can be injected in factory parameters in order
 * to know what was the name of the requested entry.
 *
 * <AUTHOR> <<EMAIL>>
 */
interface RequestedEntry
{
    /**
     * Returns the name of the entry that was requested by the container.
     *
     * @return string
     */
    public function getName();
}
