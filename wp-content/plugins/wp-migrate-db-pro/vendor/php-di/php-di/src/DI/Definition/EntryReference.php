<?php

namespace DeliciousBrains\WPMDB\Container\DI\Definition;

use DeliciousBrains\WPMDB\Container\DI\Definition\Helper\DefinitionHelper;
/**
 * Represents a reference to a container entry.
 *
 * TODO should EntryReference and AliasDefinition be merged into a ReferenceDefinition?
 *
 * <AUTHOR> <<EMAIL>>
 */
class EntryReference implements DefinitionHelper
{
    /**
     * Entry name.
     * @var string
     */
    private $name;
    /**
     * @param string $entryName Entry name
     */
    public function __construct($entryName)
    {
        $this->name = $entryName;
    }
    /**
     * @return string Entry name
     */
    public function getName()
    {
        return $this->name;
    }
    /**
     * {@inheritdoc}
     */
    public function getDefinition($entryName)
    {
        return new AliasDefinition($entryName, $this->name);
    }
}
