<?php

namespace DeliciousBrains\WPMDB\Container\DI\Definition\Source;

use DeliciousBrains\WPMDB\Container\DI\Definition\EntryReference;
use DeliciousBrains\WPMDB\Container\DI\Definition\ObjectDefinition;
use DeliciousBrains\WPMDB\Container\DI\Definition\ObjectDefinition\MethodInjection;
/**
 * Reads DI class definitions using reflection.
 *
 * <AUTHOR> Napoli <<EMAIL>>
 */
class Autowiring implements DefinitionSource
{
    /**
     * {@inheritdoc}
     */
    public function getDefinition($name)
    {
        if (!\class_exists($name) && !\interface_exists($name)) {
            return null;
        }
        $definition = new ObjectDefinition($name);
        // Constructor
        $class = new \ReflectionClass($name);
        $constructor = $class->getConstructor();
        if ($constructor && $constructor->isPublic()) {
            $definition->setConstructorInjection(MethodInjection::constructor($this->getParametersDefinition($constructor)));
        }
        return $definition;
    }
    /**
     * Read the type-hinting from the parameters of the function.
     */
    private function getParametersDefinition(\ReflectionFunctionAbstract $constructor)
    {
        $parameters = [];
        foreach ($constructor->getParameters() as $index => $parameter) {
            // Skip optional parameters
            if ($parameter->isOptional()) {
                continue;
            }
            $parameterClass = $parameter->getClass();
            if ($parameterClass) {
                $parameters[$index] = new EntryReference($parameterClass->getName());
            }
        }
        return $parameters;
    }
}
