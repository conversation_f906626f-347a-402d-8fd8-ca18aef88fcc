<?php

namespace DeliciousBrains\WPMDB\Container;

return array('root' => array('name' => 'deliciousbrains/composer-tmp', 'pretty_version' => 'dev-develop', 'version' => 'dev-develop', 'reference' => '893877c29341cf36fc984e61dd460e7d7a50e0b1', 'type' => 'library', 'install_path' => __DIR__ . '/../../', 'aliases' => array(), 'dev' => \true), 'versions' => array('brumann/polyfill-unserialize' => array('pretty_version' => 'v2.0.0', 'version' => '2.0.0.0', 'reference' => '46e5c18ee87d8a9b5765ef95468c1ac27bd107bf', 'type' => 'library', 'install_path' => __DIR__ . '/../brumann/polyfill-unserialize', 'aliases' => array(), 'dev_requirement' => \false), 'container-interop/container-interop' => array('pretty_version' => '1.2.0', 'version' => '1.2.0.0', 'reference' => '79cbf1341c22ec75643d841642dd5d6acd83bdb8', 'type' => 'library', 'install_path' => __DIR__ . '/../container-interop/container-interop', 'aliases' => array(), 'dev_requirement' => \false), 'container-interop/container-interop-implementation' => array('dev_requirement' => \false, 'provided' => array(0 => '^1.0')), 'deliciousbrains/composer-tmp' => array('pretty_version' => 'dev-develop', 'version' => 'dev-develop', 'reference' => '893877c29341cf36fc984e61dd460e7d7a50e0b1', 'type' => 'library', 'install_path' => __DIR__ . '/../../', 'aliases' => array(), 'dev_requirement' => \false), 'mnapoli/php-di' => array('dev_requirement' => \false, 'replaced' => array(0 => '*')), 'php-di/invoker' => array('pretty_version' => '1.3.3', 'version' => '1.3.3.0', 'reference' => '1f4ca63b9abc66109e53b255e465d0ddb5c2e3f7', 'type' => 'library', 'install_path' => __DIR__ . '/../php-di/invoker', 'aliases' => array(), 'dev_requirement' => \false), 'php-di/php-di' => array('pretty_version' => '5.4.0', 'version' => '5.4.0.0', 'reference' => 'e348393488fa909e4bc0707ba5c9c44cd602a1cb', 'type' => 'library', 'install_path' => __DIR__ . '/../php-di/php-di', 'aliases' => array(), 'dev_requirement' => \false), 'php-di/phpdoc-reader' => array('pretty_version' => '2.1.1', 'version' => '2.1.1.0', 'reference' => '15678f7451c020226807f520efb867ad26fbbfcf', 'type' => 'library', 'install_path' => __DIR__ . '/../php-di/phpdoc-reader', 'aliases' => array(), 'dev_requirement' => \false), 'phpoption/phpoption' => array('pretty_version' => '1.7.5', 'version' => '1.7.5.0', 'reference' => '994ecccd8f3283ecf5ac33254543eb0ac946d525', 'type' => 'library', 'install_path' => __DIR__ . '/../phpoption/phpoption', 'aliases' => array(), 'dev_requirement' => \false), 'psr/container' => array('pretty_version' => '1.0.0', 'version' => '1.0.0.0', 'reference' => 'b7ce3b176482dbbc1245ebf52b181af44c2cf55f', 'type' => 'library', 'install_path' => __DIR__ . '/../psr/container', 'aliases' => array(), 'dev_requirement' => \false), 'symfony/polyfill-ctype' => array('pretty_version' => 'v1.19.0', 'version' => '1.19.0.0', 'reference' => 'aed596913b70fae57be53d86faa2e9ef85a2297b', 'type' => 'library', 'install_path' => __DIR__ . '/../symfony/polyfill-ctype', 'aliases' => array(), 'dev_requirement' => \false), 'vlucas/phpdotenv' => array('pretty_version' => 'v4.3.0', 'version' => '4.3.0.0', 'reference' => '67a491df68208bef8c37092db11fa3885008efcf', 'type' => 'library', 'install_path' => __DIR__ . '/../vlucas/phpdotenv', 'aliases' => array(), 'dev_requirement' => \false)));
