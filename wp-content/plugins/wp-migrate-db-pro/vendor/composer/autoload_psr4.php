<?php

// autoload_psr4.php @generated by Composer

$vendorDir = dirname(__DIR__);
$baseDir = dirname($vendorDir);

return array(
    'DeliciousBrains\\WPMDB\\Container\\Symfony\\Polyfill\\Ctype\\' => array($vendorDir . '/symfony/polyfill-ctype'),
    'DeliciousBrains\\WPMDB\\Container\\Psr\\Container\\' => array($vendorDir . '/psr/container/src'),
    'DeliciousBrains\\WPMDB\\Container\\PhpOption\\' => array($vendorDir . '/phpoption/phpoption/src/PhpOption'),
    'DeliciousBrains\\WPMDB\\Container\\PhpDocReader\\' => array($vendorDir . '/php-di/phpdoc-reader/src/PhpDocReader'),
    'DeliciousBrains\\WPMDB\\Container\\Invoker\\' => array($vendorDir . '/php-di/invoker/src'),
    'DeliciousBrains\\WPMDB\\Container\\Interop\\Container\\' => array($vendorDir . '/container-interop/container-interop/src/Interop/Container'),
    'DeliciousBrains\\WPMDB\\Container\\Dotenv\\' => array($vendorDir . '/vlucas/phpdotenv/src'),
    'DeliciousBrains\\WPMDB\\Container\\DI\\' => array($vendorDir . '/php-di/php-di/src/DI'),
    'DeliciousBrains\\WPMDB\\Container\\Brumann\\Polyfill\\' => array($vendorDir . '/brumann/polyfill-unserialize/src'),
);
