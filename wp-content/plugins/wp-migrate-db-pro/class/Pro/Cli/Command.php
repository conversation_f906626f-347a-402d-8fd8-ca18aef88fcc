<?php

namespace DeliciousBrains\WPMDB\Pro\Cli;

use DeliciousBrains\WPMDB\Pro\Cli\Extra\Setting;
use DeliciousBrains\WPMDB\WPMDBDI;

class Command extends \DeliciousBrains\WPMDB\Common\Cli\Command
{

	public static function register()
	{
		\WP_CLI::add_command('migratedb', self::class);
		\WP_CLI::add_command('migrate', self::class);
	}

	/**
	 * Export local DB to file.
	 *
	 * ## OPTIONS
	 *
	 * <output-file>
	 * : A file path to export to. Filename will be modified to end in .sql or
	 * .sql.gz if necessary.
	 *
	 * [--find=<strings>]
	 * : A comma separated list of strings to find when performing a string find
	 * and replace across the database.
	 *
	 *     Table names should be quoted as needed, i.e. when using a comma in the
	 *     find/replace string.
	 *
	 *     The --replace=<strings> argument should be used in conjunction to specify
	 *     the replace values for the strings found using this argument. The number
	 *     of strings specified in this argument should match the number passed into
	 *     --replace=<strings> argument.
	 *
	 * [--replace=<strings>]
	 * : A comma separated list of replace value strings to implement when
	 * performing a string find & replace across the database.
	 *
	 *     Should be used in conjunction with the --find=<strings> argument, see it's
	 *     documentation for further explanation of the find & replace functionality.
	 *
	 * [--regex-find]
	 * : A regex pattern to match against when performing a string find
	 * and replace across the database.
	 *
	 * [--regex-replace]
	 * : A replace string that may contain references of the form \n or $n, with the latter
	 * form being the preferred one. Every such reference will be replaced by the text captured by the n'th
	 * parenthesized pattern used in the --regex-find pattern.
	 *
	 * [--case-sensitive-find]
	 * : A comma separated list of strings to find when performing a string find
	 * and replace across the database.
	 *
	 * [--case-sensitive-replace]
	 * : A comma separated list of replace value strings to implement when
	 * performing a string find & replace across the database.
	 *
	 * [--include-tables=<tables>]
	 * : The comma separated list of tables to migrate. Excluding this parameter
	 * will migrate all tables in your database that begin with your
	 * installation's table prefix, e.g. wp_.
	 *
	 * [--exclude-post-types=<post-types>]
	 * : A comma separated list of post types to exclude. Excluding this parameter
	 * will migrate all post types.
	 *
	 * [--skip-replace-guids]
	 * : Do not perform a find & replace on the guid column in the wp_posts table.
	 *
	 * [--exclude-spam]
	 * : Exclude spam comments.
	 *
	 * [--gzip-file]
	 * : GZip compress export file.
	 *
	 * [--include-transients]
	 * : Include transients (temporary cached data).
	 *
	 * [--subsite=<blog-id|subsite-url>]
	 * : Export the given subsite as a single site install. Requires the Multisite Tools addon.
	 *
	 * [--prefix=<new-table-prefix>]
	 * : A new table prefix to be used for a subsite export.
	 *
	 * ## EXAMPLES
	 *
	 *     wp migratedb export ./migratedb.sql \
	 *        --find=http://dev.bradt.ca,/Users/<USER>/home/<USER>
	 *        --replace=http://bradt.ca,/home/<USER>
	 *        --include-tables=wp_posts,wp_postmeta
	 *
	 * @param array $args
	 * @param array $assoc_args
	 */
	public function export($args, $assoc_args)
	{
    		parent::export($args, $assoc_args);
	}

	/**
	 * Run a find/replace on the database.
	 *
	 * ## OPTIONS
	 *
	 * [--find=<strings>]
	 * : A comma separated list of strings to find when performing a string find
	 * and replace across the database.
	 *
	 *     Table names should be quoted as needed, i.e. when using a comma in the
	 *     find/replace string.
	 *
	 *     The --replace=<strings> argument should be used in conjunction to specify
	 *     the replace values for the strings found using this argument. The number
	 *     of strings specified in this argument should match the number passed into
	 *     --replace=<strings> argument.
	 *
	 * [--replace=<strings>]
	 * : A comma separated list of replace value strings to implement when
	 * performing a string find & replace across the database.
	 *
	 *     Should be used in conjunction with the --find=<strings> argument, see it's
	 *     documentation for further explanation of the find & replace functionality.
	 *
	 * [--regex-find]
	 * : A regex pattern to match against when performing a string find
	 * and replace across the database.
	 *
	 * [--regex-replace]
	 * : A replace string that may contain references of the form \n or $n, with the latter
	 * form being the preferred one. Every such reference will be replaced by the text captured by the n'th
	 * parenthesized pattern used in the --regex-find pattern.
	 *
	 * [--case-sensitive-find]
	 * : A comma separated list of strings to find when performing a string find
	 * and replace across the database.
	 *
	 * [--case-sensitive-replace]
	 * : A comma separated list of replace value strings to implement when
	 * performing a string find & replace across the database.
	 *
	 * [--include-tables=<tables>]
	 * : The comma separated list of tables to migrate. Excluding this parameter
	 * will migrate all tables in your database that begin with your
	 * installation's table prefix, e.g. wp_.
	 *
	 * [--include-tables=<tables>]
	 * : The comma separated list of tables to search. Excluding this parameter
	 * will run a find & replace on all tables in your database that begin with your
	 * installation's table prefix, e.g. wp_.
	 *
	 * [--exclude-post-types=<post-types>]
	 * : A comma separated list of post types to exclude from the find & replace.
	 * Excluding this parameter will run a find & replace on all post types.
	 *
	 * [--skip-replace-guids]
	 * : Do not perform a find & replace on the guid column in the wp_posts table.
	 *
	 * [--exclude-spam]
	 * : Exclude spam comments.
	 *
	 * [--include-transients]
	 * : Include transients (temporary cached data).
	 *
	 * [--subsite=<blog-id|subsite-url>]
	 * : Run a find & replace on the given subsite. Requires the Multisite Tools addon.
	 *
	 * ## EXAMPLES
	 *
	 *     wp migratedb find-replace
	 *        --find=http://dev.bradt.ca,/Users/<USER>/home/<USER>
	 *        --replace=http://bradt.ca,/home/<USER>
	 *        --include-tables=wp_posts,wp_postmeta
	 *
	 * @param array $args
	 * @param array $assoc_args
	 *
	 * @subcommand find-replace
	 */
	public function find_replace($args, $assoc_args)
	{
		parent::find_replace($args, $assoc_args);
	}

    /**
     * Update settings for migratedb.
     *
     * ## OPTIONS
     *
     * <action>
     * : Either get or update
     *
     * <setting_name>
     * : Name of setting to update or get.
     * Available settings: push | pull | connection-key | license
     *
     * [<value>]
     * : Value of new setting
     *
     * [--user=<id|login|email>]
     * : Required for license
     *
     * ## EXAMPLES
     *
     * wp migratedb setting update license xxx-xxx-xxx-xxxxx --user=1
     *
     * wp migratedb setting get license --user=1
     *
     * wp migratedb setting update pull on
     *
     * wp migratedb setting get pull
     *
     * @param array $args
     *
     * @return bool
     * @since    1.2.5
     */
    public function setting($args)
    {
        $wpmdb_cli_settings = WPMDBDI::getInstance()->get(Setting::class);

        // Handle settings logic in dedicated class
        $wpmdb_cli_settings->handle_setting($args);
    }
}
