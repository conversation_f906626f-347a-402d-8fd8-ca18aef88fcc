.check-licence-spinner {
	left: 5px;
	position: relative;
	top: 2px;
	margin-bottom: -2px;
	width: 16px;
	height: 16px;
}

.wpmdb-original-update-row {
	display: none;
}

.plugin-update-tr.wpmdbpro-custom-visible,
.plugin-update-tr.wpmdbpro-custom {

	.update-message.pre-shiny-updates {
		padding-left: 40px;

		&::before {
			margin-left: -30px;
			float: left;
		}

		p {
			display: inline-block;
			margin: 0;
		}

		span {
			display: block;
		}
	}

	.update-message.post-shiny-updates p {
		&::before {
			position: absolute;
		}

		span {
			margin-left: 30px;
			display: block;
		}
	}
	&.legacy-addon .update-message p::before {
		content: "\f182";
	}
	
}


.plugins #the-list tr.wpmdbpro-has-message td,
.plugins #the-list tr.wpmdbpro-has-message th {
	box-shadow: none;
	-webkit-box-shadow: none;
}

.plugins .plugin-update-tr .wpmdb-compat-plugin-row-error .notice{
	margin-top:0;
}