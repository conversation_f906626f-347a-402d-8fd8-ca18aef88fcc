(self.webpackJSONPwpmdb=self.webpackJSONPwpmdb||[]).push([[179],{51286:function(e,t,n){"use strict";n.d(t,{r:function(){return s}});var r,a=n(4665);function i(){return i=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},i.apply(this,arguments)}var s=function(e){return a.createElement("svg",i({width:12,height:10,viewBox:"0 0 12 7",xmlns:"http://www.w3.org/2000/svg"},e),r||(r=a.createElement("g",{id:"Symbols",stroke:"none",strokeWidth:1,fill:"none",fillRule:"evenodd"},a.createElement("g",{id:"accordion_collapsed_default_active",transform:"translate(-1188 -25)",fill:"#000",fillRule:"nonzero"},a.createElement("g",{id:"arrow_down",transform:"translate(1188 25)"},a.createElement("path",{d:"m3.16 3.893 4.9 4.945a.548.548 0 0 0 .779 0 .558.558 0 0 0 0-.785L4.327 3.5l4.511-4.552a.559.559 0 0 0 0-.786.548.548 0 0 0-.779 0l-4.9 4.945a.564.564 0 0 0 0 .786Z",id:"arrow",transform:"rotate(-90 6 3.5)"}))))))};n.p},73042:function(e,t,n){"use strict";n.d(t,{r:function(){return s}});var r,a=n(4665);function i(){return i=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},i.apply(this,arguments)}var s=function(e){return a.createElement("svg",i({xmlns:"http://www.w3.org/2000/svg",width:16,height:16,viewBox:"0 0 16 16"},e),r||(r=a.createElement("path",{fill:"#999",d:"m9.188 8 3.566-3.565a.84.84 0 1 0-1.189-1.189L8 6.812 4.435 3.246a.84.84 0 1 0-1.189 1.189L6.812 8l-3.566 3.565a.84.84 0 0 0 1.189 1.189L8 9.188l3.565 3.566a.84.84 0 1 0 1.189-1.189L9.188 8Z"})))};n.p},23481:function(e,t,n){"use strict";n.d(t,{r:function(){return c}});var r,a,i=n(4665);function s(){return s=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},s.apply(this,arguments)}var c=function(e){return i.createElement("svg",s({xmlns:"http://www.w3.org/2000/svg",xmlnsXlink:"http://www.w3.org/1999/xlink",width:24,height:24,viewBox:"0 0 24 24"},e),r||(r=i.createElement("defs",null,i.createElement("circle",{id:"license-unchecked-a",cx:8,cy:8,r:8}))),a||(a=i.createElement("g",{fill:"none",fillRule:"evenodd",transform:"translate(4 4)"},i.createElement("use",{fill:"#dc3232",xlinkHref:"#license-unchecked-a"}),i.createElement("g",{fill:"#FFF",transform:"translate(4 4)"},i.createElement("rect",{width:2,height:8,x:3,rx:1,transform:"rotate(-45 4 4)"}),i.createElement("rect",{width:2,height:8,x:3,rx:1,transform:"rotate(-135 4 4)"})))))};n.p},9712:function(e,t,n){"use strict";n.d(t,{r:function(){return s}});var r,a=n(4665);function i(){return i=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},i.apply(this,arguments)}var s=function(e){return a.createElement("svg",i({xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24"},e),r||(r=a.createElement("g",{fill:"none",fillRule:"evenodd"},a.createElement("circle",{cx:12,cy:12,r:8,fill:"#999"}),a.createElement("path",{fill:"#EEE",fillRule:"nonzero",d:"M12.75 15h-1.5a.24.24 0 0 0-.175.075.24.24 0 0 0-.075.175v1.5a.24.24 0 0 0 .075.175.24.24 0 0 0 .175.075h1.5a.24.24 0 0 0 .175-.075.24.24 0 0 0 .075-.175v-1.5a.24.24 0 0 0-.075-.175.24.24 0 0 0-.175-.075zm1.984-5.383a2.758 2.758 0 0 0-.688-.854 3.493 3.493 0 0 0-.948-.555A2.832 2.832 0 0 0 12.052 8c-1.316 0-2.32.594-3.012 1.781a.271.271 0 0 0 .065.355l1.064.837c.057.036.11.054.163.054a.25.25 0 0 0 .201-.107c.312-.402.543-.659.695-.77.186-.13.42-.195.7-.195.265 0 .498.072.699.215.201.142.302.308.302.495 0 .21-.054.38-.163.51-.108.128-.29.253-.545.374a2.752 2.752 0 0 0-.928.727c-.282.328-.293.674-.293 1.044v.302c0 .085.023.168.068.25.046.084.1.125.166.125L12.74 14c.07 0 .13-.035.182-.104a.357.357 0 0 0 .078-.217c0-.112-.018-.262.1-.449a1.29 1.29 0 0 1 .446-.435c.168-.099.3-.178.396-.238a3.3 3.3 0 0 0 .373-.291c.154-.134.273-.267.357-.399.084-.132.16-.302.227-.512.068-.21.101-.436.101-.677 0-.37-.089-.724-.266-1.06z"}))))};n.p},69479:function(e,t,n){"use strict";n.d(t,{r:function(){return s}});var r,a=n(4665);function i(){return i=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},i.apply(this,arguments)}var s=function(e){return a.createElement("svg",i({xmlns:"http://www.w3.org/2000/svg",width:16,height:16,viewBox:"0 0 16 16"},e),r||(r=a.createElement("path",{fill:"#999",d:"m3.562 7.245-.015.003h6.691l-2.104-2.03a.517.517 0 0 1 0-.749l.328-.315c.103-.1.24-.154.386-.154.146 0 .284.054.387.154l3.606 3.472c.103.1.16.232.159.373a.512.512 0 0 1-.16.374l-3.605 3.473a.554.554 0 0 1-.387.154.554.554 0 0 1-.386-.154l-.328-.315a.512.512 0 0 1-.159-.372c0-.141.056-.266.16-.365l2.127-2.042H3.555A.556.556 0 0 1 3 8.212v-.447c0-.29.26-.52.562-.52Z"})))};n.p},49275:function(e,t,n){"use strict";var r=n(4665),a=n(80401);t.Z=function(e){var t=e.link,n=e.content,i=e.utmContent,s=e.utmCampaign,c=e.anchorLink,l=e.hasArrow,o=void 0===l||l;return r.createElement(a.Z,{link:t,content:n,utmContent:i,utmCampaign:s,classes:"docs-link",hasArrow:o,anchorLink:c})}},80401:function(e,t,n){"use strict";var r=n(4665),a=n(29942);t.Z=function(e){var t=e.link,n=e.classes,i=e.content,s=e.utmContent,c=e.utmCampaign,l=e.hasArrow,o=e.screenReaderText,u=e.anchorLink,p={source:(0,a.Yu)()?"wp-migrate-pro":"wp-migrate-lite",medium:"plugin",campaign:c,content:s},m=function(){var e=[];for(var t in p)p.hasOwnProperty(t)&&e.push("utm_".concat(t,"=").concat(p[t]));return!!e.length&&e.join("&")};return r.createElement("a",{className:n,href:function(){var e=t;return m()&&(e+="?"+m()),u&&(e+="#"+u),e}(),target:"__blank",rel:"NOFOLLOW"},i," ",o&&r.createElement("span",{className:"screen-reader-text"},o),l&&r.createElement("span",{className:"open-arrow"},"\u2192"))}},49736:function(e,t,n){"use strict";n.d(t,{B:function(){return a}});var r=n(42233),a={tables:{all:(0,r.__)("All tables","wp-migrate-db"),selected:(0,r.__)("Selected tables","wp-migrate-db")},backups:{none:(0,r.__)("No backup","wp-migrate-db"),all:(0,r.__)("Backup all tables","wp-migrate-db"),migration:(0,r.__)("Backup tables selected for migration","wp-migrate-db"),selected:(0,r.__)("Backup selected tables","wp-migrate-db")},post_types:{all:(0,r.__)("All post types","wp-migrate-db"),selected:(0,r.__)("Selected post types","wp-migrate-db")}}},91828:function(e,t,n){"use strict";n.d(t,{B:function(){return a}});var r=n(42233),a={push:(0,r.__)("Push","wp-migrate-db"),pull:(0,r.__)("Pull","wp-migrate-db"),backup_local:(0,r.__)("Backup Database","wp-migrate-db"),import:(0,r.__)("Import Database","wp-migrate-db"),find_replace:(0,r.__)("Find & Replace","wp-migrate-db"),savefile:(0,r.__)("Export Database","wp-migrate-db")}},86645:function(e,t,n){"use strict";n.d(t,{O:function(){return l}});var r=n(4665),a=n(62295),i=n(42233),s=n(4516),c=n(29942),l=function(e){return(e?e.reduce((function(e,t){return e+t}),0):0)/(e&&e.length>0?e.length:1)};t.Z=function(){var e=(0,a.v9)((function(e){return e})),t=(0,s.r5)("intent",e),n=(0,s.r5)("current_stage",e),o=(0,s.r5)("currentPayloadSize",e),u=(0,s.r5)("currentMaxPayloadSize",e),p=(0,s.r5)("payloadSizeHistory",e),m=(0,s.r5)("fileTransferRequests",e);return["push","pull"].includes(t)&&["media_files","theme_files","plugin_files","muplugin_files","other_files"].includes(n)?r.createElement("dl",{className:"fs-stats-container flex flex-row"},r.createElement("dt",{className:"fs-stats"},r.createElement("span",null,(0,i.__)("Max Request Size","wp-migrate-db")),r.createElement("span",{className:"fs-stat-value"},(0,c.lL)(null!==u&&void 0!==u?u:0))),r.createElement("dt",{className:"fs-stats"},r.createElement("span",null,(0,i.__)("Actual Request Size","wp-migrate-db")),r.createElement("span",{className:"fs-stat-value"},(0,c.lL)(null!==o&&void 0!==o?o:0))),r.createElement("dt",{className:"fs-stats"},r.createElement("span",null,(0,i.__)("Average Request Size","wp-migrate-db")),r.createElement("span",{className:"fs-stat-value"},(0,c.lL)(l(p)))),r.createElement("dt",{className:"fs-stats"},r.createElement("span",null,(0,i.__)("Total Requests","wp-migrate-db")),r.createElement("span",{className:"fs-stat-value"},null!==m&&void 0!==m?m:0))):null}},42222:function(e,t,n){"use strict";n.d(t,{$s:function(){return u},TJ:function(){return i},UJ:function(){return a},er:function(){return l},i2:function(){return c},q5:function(){return o}});var r=n(42233),a=function(e){if(e>=1e6){var t=e/1e6;return(Math.round(100*t)/100).toFixed(2)}if(e>=1e3){var n=e/1e3;return(Math.round(100*n)/100).toFixed(2)}return e<1?(1e3*e).toFixed(2):parseFloat(e).toFixed(2)},i=function(e){return e>=1e6?"GB":e>=1e3?"MB":e<1?"B":"KB"},s=function(e,t){var n=arguments.length>2&&void 0!==arguments[2]&&arguments[2],r=Math.floor(e/t)%60,a=r.toString();return n&&(a="0"+a),r>=10?a.slice(-2):a.toString()},c=function(e){return s(e,1e3,arguments.length>1&&void 0!==arguments[1]&&arguments[1])},l=function(e){return s(e,6e4,arguments.length>1&&void 0!==arguments[1]&&arguments[1])},o=function(e){var t=(e/1e3).toFixed(2),n=l(e/1e3),a=(0,r.gB)("%s %s%s",t,(0,r.__)("sec","wp-migrate-db"),t>1?"s":""),i=(0,r.gB)("%s %s%s",n,(0,r.__)("min","wp-migrate-db"),n>1?"s":"");return(0,r.gB)("%s%s",n>0?i:""," ".concat(a))},u=function(e){return Math.floor(e/36e5)}},81294:function(e,t,n){"use strict";function r(e,t,n,r){return"selected"===n.tables_option?n.tables_selected:"pull"===r?t.prefixed_tables:e.this_prefixed_tables}n.d(t,{O:function(){return r}})},29214:function(e,t,n){"use strict";n.d(t,{Z:function(){return x}});var r,a,i,s=n(4665),c=n(42233);function l(){return l=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},l.apply(this,arguments)}var o,u,p=function(e){return s.createElement("svg",l({width:16,height:16,viewBox:"0 0 16 16",fill:"none",xmlns:"http://www.w3.org/2000/svg"},e),r||(r=s.createElement("path",{d:"M15.838 13.852 9.27.79C8.904.071 8.05-.208 7.366.166a1.38 1.38 0 0 0-.6.616L.165 13.867c-.366.71-.098 1.598.593 1.972.197.11.423.161.649.161h13.183c.776 0 1.411-.66 1.411-1.466a1.49 1.49 0 0 0-.162-.682Z",fill:"#FFB92B"})),a||(a=s.createElement("rect",{x:7,y:5,width:2,height:6,rx:1,fill:"#fff"})),i||(i=s.createElement("circle",{cx:8,cy:13,r:1,fill:"#fff"})))};n.p;function m(){return m=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},m.apply(this,arguments)}var d,f,_=function(e){return s.createElement("svg",m({width:16,height:16,viewBox:"0 0 16 16",fill:"none",xmlns:"http://www.w3.org/2000/svg"},e),o||(o=s.createElement("path",{d:"M0 8a8 8 0 1 1 16 0A8 8 0 0 1 0 8Z",fill:"#46B450"})),u||(u=s.createElement("path",{d:"M7.95 11.247a1.01 1.01 0 0 1-1.433 0l-2.22-2.234a1.024 1.024 0 0 1 0-1.441 1.01 1.01 0 0 1 1.433 0l1.323 1.33c.1.1.262.1.362 0L10.997 5.3a1.01 1.01 0 0 1 1.434 0 1.023 1.023 0 0 1 0 1.441l-4.48 4.507Z",fill:"#fff"})))};n.p;function g(){return g=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},g.apply(this,arguments)}var b,h,v,E=function(e){return s.createElement("svg",g({width:16,height:16,viewBox:"0 0 16 16",fill:"none",xmlns:"http://www.w3.org/2000/svg"},e),d||(d=s.createElement("path",{d:"M0 8a8 8 0 1 1 16 0A8 8 0 0 1 0 8Z",fill:"#0968FF"})),f||(f=s.createElement("path",{d:"M9.026 4.111c0-.67-.549-1.211-1.211-1.211a1.21 1.21 0 0 0-1.211 1.211c0 .662.54 1.211 1.21 1.211a1.22 1.22 0 0 0 1.212-1.21ZM6.519 13.1H9.48c.463 0 .841-.378.841-.84 0-.471-.379-.841-.84-.841h-.556a.088.088 0 0 1-.085-.086V7.815c0-.878-.712-1.582-1.582-1.582h-.74c-.47 0-.841.371-.841.841 0 .462.37.84.84.84h.556c.044 0 .085.04.085.086v3.333c0 .018-.008.04-.027.058a.086.086 0 0 1-.058.027H6.52c-.47 0-.841.371-.841.841 0 .462.37.841.84.841Z",fill:"#fff",stroke:"#fff",strokeWidth:.2})))};n.p;function w(){return w=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},w.apply(this,arguments)}var y=function(e){return s.createElement("svg",w({width:16,height:16,viewBox:"0 0 16 16",fill:"none",xmlns:"http://www.w3.org/2000/svg"},e),b||(b=s.createElement("path",{d:"M0 8a8 8 0 1 1 16 0A8 8 0 0 1 0 8Z",fill:"#DC3232"})),h||(h=s.createElement("rect",{x:7,y:3.5,width:2,height:6,rx:1,fill:"#fff"})),v||(v=s.createElement("circle",{cx:8,cy:11.5,r:1,fill:"#fff"})))},x=(n.p,n(99816),function(e){var t=e.type,n=e.children,r={warning:p,success:_,info:E,danger:y},a={warning:(0,c.__)("Warning","wp-migrate-db"),success:(0,c.__)("Success","wp-migrate-db"),info:(0,c.__)("Information","wp-migrate-db"),danger:(0,c.__)("Error","wp-migrate-db")},i=r[t],l="migrate-notice ".concat(t);return s.createElement("div",{className:l},s.createElement(i,{"aria-label":a[t]}),s.createElement("div",{className:"migrate-notice-content"},n))})},91399:function(e,t,n){"use strict";n.d(t,{OP:function(){return m},Uj:function(){return d},X2:function(){return f}});var r=n(79043),a=n(4665),i=n(42233),s=n(96480),c=n.n(s),l=n(23481),o=n(15925),u=n(19085);function p(e,t){return e?{left:e.width+t}:{}}var m=function(e){var t=e.position,n=e.condition,i=e.errorMsg,s=e.spinnerCond;return a.createElement(a.Fragment,null,s&&a.createElement("div",{className:"relative"},a.createElement(u.Q,{className:"settings-spinner"})),a.createElement(o.u,{in:"success"===n},a.createElement(f,(0,r.Z)({locationInfo:t,classNames:"toggle-success"},e))),a.createElement(o.u,{in:"errored"===n},a.createElement(d,(0,r.Z)({error:i,locationInfo:t,classNames:"toggle-error"},e))))},d=function(e){var t=e.offset||12,n=p(e.locationInfo,t);return a.createElement("div",{className:"settings-tooltip ".concat(e.classNames?e.classNames:""),style:n},a.createElement("div",{className:"tooltip-saved flex-container",key:"ajax-error-".concat(c()())},a.createElement(a.Fragment,null,a.createElement(l.r,null)," ",e.error)))},f=function(e){var t=e.offset||12,n=p(e.locationInfo,t);return a.createElement("div",{className:"settings-tooltip ".concat(e.classNames?e.classNames:""),style:n},a.createElement("div",{className:"tooltip-saved flex-container",key:"ajax-error-".concat(c()())},a.createElement(a.Fragment,null,a.createElement(u.en,null)," ",a.createElement("div",null,(0,i.__)("Saved","wp-migrate-db")))))}},15925:function(e,t,n){"use strict";n.d(t,{u:function(){return c}});var r=n(4665),a=n(91490),i=n(96480),s=n.n(i),c=function(e){return r.createElement("div",{className:"relative"},r.createElement(a.Z,{in:e.in,timeout:e.timeout||500,classNames:"settings-node",unmountOnExit:!0,id:s()()},e.children))}},27114:function(e,t,n){"use strict";n.d(t,{Y:function(){return a}});var r=n(42233);function a(e){var t="";return"string"===typeof e.error_message?t=e.error_message:"object"===typeof e.error_message&&"string"===typeof e.error_message.message?t=e.error_message.message:"string"===typeof e?t=e:"string"===typeof e.data?t=e.data:"string"===typeof e.message?t=e.message.message:"undefined"!==typeof e.wpmdb_error&&(t=e.body),""!==t?t:(0,r.__)("An unknown error occurred. Please check your PHP error log or contact support.","wp-migrate-db")}},78677:function(e,t,n){"use strict";n.d(t,{Z:function(){return B}});var r,a,i=n(17186),s=n(4665),c=n(75338),l=n(42233),o=n(30348),u=n(96480),p=n.n(u),m=n(29942),d=n(63708);function f(){return f=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},f.apply(this,arguments)}var _,g,b=function(e){return s.createElement("svg",f({width:12,height:12,viewBox:"0 0 12 12",fill:"none",xmlns:"http://www.w3.org/2000/svg"},e),r||(r=s.createElement("circle",{cx:6,cy:6,r:6,fill:"gray"})),a||(a=s.createElement("path",{d:"M5.538 6.138c.304-.549.888-.872 1.228-1.358.359-.509.158-1.46-.86-1.46-.668 0-.995.505-1.134.923l-1.022-.43c.28-.84 1.042-1.563 2.151-1.563.928 0 1.563.422 1.887.951.276.454.438 1.303.012 1.935-.474.698-.928.911-1.172 1.361-.1.182-.139.3-.139.885H5.35c-.004-.308-.052-.81.19-1.244Zm1.157 2.823c0 .434-.356.789-.79.789a.792.792 0 0 1-.79-.79c0-.434.356-.789.79-.789.434 0 .79.355.79.79Z",fill:"#fff"})))};n.p;function h(){return h=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},h.apply(this,arguments)}var v,E,w=function(e){return s.createElement("svg",h({width:12,height:12,viewBox:"0 0 12 12",fill:"none",xmlns:"http://www.w3.org/2000/svg"},e),_||(_=s.createElement("circle",{cx:6,cy:6,r:6,fill:"#46B450"})),g||(g=s.createElement("path",{d:"M9.75 6.536H6.536V9.75H5.464V6.536H2.25V5.464h3.214V2.25h1.072v3.214H9.75v1.072Z",fill:"#fff"})))};n.p;function y(){return y=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},y.apply(this,arguments)}var x,k,Z=function(e){return s.createElement("svg",y({width:12,height:12,viewBox:"0 0 12 12",fill:"none",xmlns:"http://www.w3.org/2000/svg"},e),v||(v=s.createElement("circle",{cx:6,cy:6,r:6,fill:"#46B450"})),E||(E=s.createElement("path",{d:"M6.5 9.5V3.915l2.44 2.44a.504.504 0 0 0 .71 0 .498.498 0 0 0 0-.705L6.355 2.355a.498.498 0 0 0-.705 0l-3.3 3.29a.498.498 0 1 0 .705.705L5.5 3.915V9.5c0 .275.225.5.5.5s.5-.225.5-.5Z",fill:"#fff"})))};n.p;function N(){return N=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},N.apply(this,arguments)}var P,S,T=function(e){return s.createElement("svg",N({width:12,height:12,viewBox:"0 0 12 12",fill:"none",xmlns:"http://www.w3.org/2000/svg"},e),x||(x=s.createElement("circle",{cx:6,cy:6,r:6,fill:"#DC3232"})),k||(k=s.createElement("path",{d:"M5.5 2.5v5.585l-2.44-2.44a.504.504 0 0 0-.71 0 .498.498 0 0 0 0 .705l3.295 3.295c.**************.705 0L9.645 6.35a.498.498 0 1 0-.705-.705L6.5 8.085V2.5c0-.275-.225-.5-.5-.5s-.5.225-.5.5Z",fill:"#fff"})))};n.p;function O(){return O=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},O.apply(this,arguments)}var A,C=function(e){return s.createElement("svg",O({width:12,height:12,viewBox:"0 0 12 12",fill:"none",xmlns:"http://www.w3.org/2000/svg"},e),P||(P=s.createElement("circle",{cx:6,cy:6,r:6,fill:"gray"})),S||(S=s.createElement("path",{d:"M2.5 6.5h5.585l-2.44 2.44a.504.504 0 0 0 0 .71c.**************.705 0l3.295-3.295a.498.498 0 0 0 0-.705l-3.29-3.3a.498.498 0 1 0-.705.705L8.085 5.5H2.5c-.275 0-.5.225-.5.5s.225.5.5.5Z",fill:"#fff"})))},R=(n.p,function(e){var t=e.content,n=e.type;return s.createElement(s.Fragment,null,s.createElement("span",{className:"version-tooltip"},(0,c.ZP)(t)),s.createElement("div",{className:"compare-icon ".concat(n)},function(){switch(n){case"add":return s.createElement(w,null);case"up":return s.createElement(Z,null);case"down":return s.createElement(T,null);case"equal":return s.createElement(C,null);default:return s.createElement(b,null)}}()))}),I=function(e){var t=e.item,n=e.type,r=t.sourceVersion,a=t.destinationVersion,i=(0,d.n)(r,a),c="themes"===n?"theme":"plugin";return s.createElement("span",{className:"version-compare"},s.createElement(R,{content:function(e){switch(e){case"add":return(0,l.gB)((0,l.__)("New %s will be added at version <strong>%s</strong>","wp-migrate-db"),c,r);case"up":return(0,l.gB)((0,l.__)("%s will be upgraded from <strong>%s</strong> to <strong>%s</strong>","wp-migrate-db"),c,a,r);case"equal":return(0,l.gB)((0,l.__)("%s will remain the same at version <strong>%s</strong>","wp-migrate-db"),c,r);case"down":return(0,l.gB)((0,l.__)("%s will be downgraded from <strong>%s</strong> to <strong>%s</strong>","wp-migrate-db"),c,a,r);case"none":return(0,l.gB)((0,l.__)("No version detected in %s header","wp-migrate-db"),c);default:return""}}(i),type:i}))},D=o.ZP.ul(A||(A=(0,i.Z)(["\n  height: 180px;\n  overflow-y: scroll;\n  resize: vertical;\n  margin: 15px 0 0 0;\n  border: 1px solid #d6d6d6;\n  border-radius: 4px;\n  padding: 0.5rem;\n  label {\n    margin: 0.25rem 0;\n    display: block;\n  }\n"]))),L=null,M=function(e,t,n,r,a){var i=arguments.length>5&&void 0!==arguments[5]?arguments[5]:{},l=arguments.length>6?arguments[6]:void 0,o=arguments.length>7&&void 0!==arguments[7]?arguments[7]:"",u=arguments.length>8&&void 0!==arguments[8]?arguments[8]:"";function d(e){var a=e.target.value,i=-1===n.indexOf(a),s=(0,m.XV)(n,a);!0===e.nativeEvent.shiftKey&&null!==L&&(s=function(e,t,n,r){var a=n.indexOf(e),i=L<=a?L:a,s=L>a?L:a,c=n.slice(i,s+1);return t?(0,m.VU)(r,c):(0,m.fg)(r,c)}(a,i,t,n)),L=t.indexOf(a),r(s)}var f="";return e.map((function(e,t){Object.keys(i).length>0&&(f=void 0!==typeof i[e]?i[e]:"");var r="string"===typeof e?"checkbox-".concat(a,"-").concat(e):"checkbox-".concat(a,"-").concat(e.path),m="string"===typeof e?e:e.path,_="string"===typeof e?e:e.name;return s.createElement("li",{"data-value":m,key:p()()},s.createElement("input",{id:r,type:"checkbox",value:m,onChange:d,checked:n.includes(m),disabled:l}),s.createElement("label",{htmlFor:r,key:p()()},s.createElement("span",{className:"name"},_),s.createElement("span",{className:"extra-label"},(0,c.ZP)(f)),e.hasOwnProperty("sourceVersion")&&"except"!==u&&s.createElement(I,{item:e,type:o})))}))},F=function(e){var t=e.visible,n=e.iterator,r=e.extraLabels,a=e.selected,i=e.disabled,c=e.id,o=e.options?e.options:[],u=void 0===e.showOptions||e.showOptions,m=p()(),d=(0,s.useMemo)((function(){return n?n():M(o,function(e){return"string"===typeof e[0]?e:e.map((function(e){return e.path}))}(o),a,e.stateManager,m,r,i,e.type,e.themePluginOption)}),[n,a]);return s.createElement("div",{className:"select-group"},e.ToggleButtons||"",t&&s.createElement("div",{className:"select-wrap"},s.createElement(D,{id:c,className:e.className?e.className:""},d),u&&s.createElement("p",{className:"multiselect-options"},s.createElement("button",{onClick:function(){return e.updateSelected(o)}},(0,l.__)("Select All","wp-migrate-db")),s.createElement("button",{onClick:function(){e.updateSelected([]),L=null}},(0,l.__)("Deselect All","wp-migrate-db"))," ",s.createElement("button",{onClick:function(){return e.selectInverse()}},(0,l.__)("Invert Selection","wp-migrate-db"))),e.afterList||""))},B=s.memo(F)},40795:function(e,t,n){"use strict";n.d(t,{Z:function(){return C}});var r=n(79043),a=n(18489),i=n(88368),s=n(27166),c=n(33032),l=n(4665),o=n(12544),u=n.n(o),p=n(62295),m=n(22633),d=n(76178),f=n(51286),_=function(e){var t=e.enabled,n=e.panelName,r=e.toggle,a=e.disabled,i=(0,p.I0)();return l.createElement("input",{type:"checkbox",onChange:function(e){if(a)return e.preventDefault(),!1;i(t?(0,m.I4)(n):(0,m.LX)(n)),i(r)},checked:t,id:"enable-".concat(n),"aria-labelledby":"panel-title-".concat(n)})},g=function(e){var t=e.preTitle,n=e.childPanel,r=e.panelName,a=e.title,i=e.enabled,s=e.toggle,c=e.disabled,o="undefined"!==typeof e.hasInput&&e.hasInput,u=n?"h3":"h2";return l.createElement(l.Fragment,null,t,o&&l.createElement(_,{enabled:i,panelName:r,toggle:s,disabled:c}),l.createElement(u,{id:"panel-title-".concat(r),className:"panel-title"},a))};var b=function(e){var t=e.panelName,n=e.childPanel,r=e.toggleClassName;if(e.hideArrow)return null;var a=n?" white-arrow":"";return l.createElement("div",{className:"button-wrap"},l.createElement("button",{"aria-labelledby":"panel-title-".concat(t),"aria-describedby":"panel-summary-".concat(t)},l.createElement(f.r,{className:"".concat(r).concat(a," panel-arrow")})))},h=function(e){var t=e.childPanel,n=e.panelOpen,r=e.panelsOpen,a=e.preTitle,i=e.forceDivider,s=e.panelName,c=e.registeredPanels,o=e.hideArrow,u=e.title,p=n?"open":"closed",m=function(){return e.panelSummary?l.createElement("div",{id:"panel-summary-".concat(s),className:"panel-summary"},e.panelSummary):null},d=function(e){var t=e.intent,n=e.panelName,r=e.connected,a=e.file_uploaded,i=["push","pull"].includes(t);return"import"===t&&"import"===n?!!a:!(!i||!r||"connect"!==n)||""!==t&&"connect"!==n}({intent:e.intent,panelName:e.panelName,connected:e.connected,file_uploaded:e.file_uploaded});i&&(d=!0);var f=d?" bg-grey-light":"",_=[];return e.hasSummary&&e.panelSummary&&_.push("has-summary"),t?l.createElement("div",{className:"panel-header child-panel-header ".concat(_.join(" "))},l.createElement(b,{childPanel:t,panelName:s,panelsOpen:r,toggleClassName:p}),l.createElement("div",{className:"child-summary"},l.createElement(g,{preTitle:a,childPanel:t,panelName:s,title:u}),l.createElement(m,null))):l.createElement(l.Fragment,null,l.createElement(g,{preTitle:a,childPanel:t,panelName:s,title:u,disabled:e.disabled,enabled:e.enabled,toggle:e.toggle,hasInput:e.hasInput}),i&&l.createElement("div",{className:"accordion-divider".concat(f)}),l.createElement("div",{className:"panel-header ".concat(_.join(" "))},l.createElement(m,null),l.createElement(b,{childPanel:t,panelName:s,panelsOpen:r,toggleClassName:p,registeredPanels:c,hideArrow:o})))},v=n(50166);var E,w,y=function(e){var t=e.panelContentClass,n=e.panelOpen,r=e.bodyClass,s=e.id,c=e.children,o=function(){var e=(arguments.length>0&&void 0!==arguments[0]?arguments[0]:{}).on,t=void 0===e||e,n=(0,l.useRef)(),r=(0,l.useState)(0),a=(0,i.Z)(r,2),s=a[0],c=a[1],o=(0,l.useRef)(s),u=(0,l.useState)((function(){return new ResizeObserver((function(e){n.current&&o.current!==n.current.offsetHeight&&(o.current=n.current.offsetHeight,c(n.current.offsetHeight))}))})),p=(0,i.Z)(u,1)[0];return(0,l.useLayoutEffect)((function(){return t&&n.current&&(c(n.current.offsetHeight),p.observe(n.current,{})),function(){return p.disconnect()}}),[t,n.current]),[n,s]}(),u=(0,i.Z)(o,2),m=u[0],d=u[1],f=(0,p.v9)((function(e){return e})).migrations,_=r?"".concat(t," ").concat(r):t,g="backup_local"===f.current_migration.intent,b=(0,l.useState)(g),h=(0,i.Z)(b,2),E=h[0],w=h[1],y=E?{config:(0,a.Z)((0,a.Z)({},v.vc.gentle),{},{clamp:!0}),from:{opacity:0,height:0},to:{opacity:n?1:0,height:n?d:0}}:{},x=(0,v.q_)(y);(0,l.useEffect)((function(){w(!0)}),[]);var k=n?"panel-open":"panel-closed";return l.createElement("div",{className:"".concat(k," panel-body-wrap")},l.createElement(v.q.div,{style:(0,a.Z)((0,a.Z)({},x),{},{overflow:"hidden"}),id:s},l.createElement("div",{ref:m,className:"".concat(_," panel-body")},c)))},x=n(4516),k=(n(43156),n(29942)),Z=n(42233),N=n(75338),P=n(49275);function S(){return S=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},S.apply(this,arguments)}var T=function(e){return l.createElement("svg",S({width:28,height:28,viewBox:"0 0 28 28",fill:"none",xmlns:"http://www.w3.org/2000/svg"},e),E||(E=l.createElement("rect",{width:28,height:28,rx:6,fill:"#DC3232"})),w||(w=l.createElement("path",{d:"m20.258 8.866-4.95-2.858a2.636 2.636 0 0 0-2.625 0L7.742 8.866a2.625 2.625 0 0 0-1.309 2.275v5.7c0 .933.5 1.8 1.309 2.275l4.95 2.858a2.636 2.636 0 0 0 2.625 0l4.95-2.858a2.625 2.625 0 0 0 1.308-2.275v-5.7a2.66 2.66 0 0 0-1.317-2.275Zm-6.883 1.583A.63.63 0 0 1 14 9.824a.63.63 0 0 1 .625.625v4.375a.63.63 0 0 1-.625.625.63.63 0 0 1-.625-.625V10.45Zm1.392 7.4c-.042.1-.1.192-.175.275a.825.825 0 0 1-.909.175.862.862 0 0 1-.275-.175 1.217 1.217 0 0 1-.183-.275.82.82 0 0 1-.058-.316c0-.217.083-.434.241-.592a.86.86 0 0 1 .275-.175.832.832 0 0 1 .909.175.86.86 0 0 1 .175.275.83.83 0 0 1 .066.317.83.83 0 0 1-.066.316Z",fill:"#fff"})))},O=(n.p,function(e){var t=e.panelName,n=e.panelTitle,r=(0,p.v9)((function(e){return e.migrations})),a=(0,Z.gB)((0,Z.__)("<code>%s</code> is not writable at the destination.","wp-migrate-db"),(0,k.j_)(t,r));return console.log(t),l.createElement("div",{className:"error-disabled"},l.createElement("div",{className:"action-panel shadow-div error-panel"},l.createElement("div",{className:"panel-header-wrap panel-closed has-summary-no-child"},l.createElement(T,null),l.createElement("h4",{className:"panel-title"},n),l.createElement("div",{className:"accordion-divider bg-grey-light"}),l.createElement("div",{className:"panel-header has-summary"},l.createElement("div",{className:"panel-summary"},l.createElement("p",null,(0,N.ZP)(a)),l.createElement(P.Z,{link:"https://deliciousbrains.com/wp-migrate-db-pro/doc/folder-permission-errors/",content:(0,Z.__)("How to Fix Folder Permissions","wp-migrate-db"),utmContent:"not-writable-error-panel-".concat(t),utmCampaign:"wp-migrate-documentation",hasArrow:!0}))))))}),A=function(){var e=(0,c.Z)((0,s.Z)().mark((function e(t){var r,a;return(0,s.Z)().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,Promise.resolve().then(n.bind(n,67821));case 2:return r=e.sent,a=r.selectFromImportData,e.abrupt("return",a("file_uploaded",t));case 5:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}();var C=(0,p.$j)((function(e){var t=(0,d.O)("panelsOpen",e),n=(0,d.O)("panelClicked",e),r=(0,d.O)("panelStatus",e),a=(0,d.O)("registeredPanels",e),i=(0,x.r5)("intent",e),s=(0,x.r5)("connected",e),c=!1;return(0,k.Yu)()&&(c=A(e)),{panels:e.panels,panelsOpen:t,panelClicked:n,panelStatus:r,intent:i,connected:s,file_uploaded:c,registeredPanels:a}}),{removeOpenPanel:m.I4,togglePanelsOpen:m.SO,setPanelClicked:m.er,setPanelStatus:m.OW,registerPanel:m.ii})((function(e){var t=e.disabled,n=e.childPanel,s=e.className,c=e.hasDefault,o=e.selected,p=e.hasInput;(0,l.useEffect)((function(){e.registerPanel(e.panelName)}),[e.intent]);var m=u()(e.panelsOpen,e.panelName),d=m?"open":"closed",f=(0,l.useState)(""),_=(0,i.Z)(f,2),g=_[0],b=(_[1],(0,l.useState)(!1)),v=(0,i.Z)(b,2),E=v[0],w=(v[1],(0,l.useState)(!1)),x=(0,i.Z)(w,2),Z=x[0],N=(x[1],e.panelName),P=e.bodyClass,S=e.hasOwnProperty("panelSummary"),T=(0,a.Z)((0,a.Z)({},e),{},{panelOpen:m,hasSummary:S,panelName:N,hasInput:p}),A="action-panel shadow-div";s&&(A+=" ".concat(s));var C=function(e,t,n){var r=[];return e&&r.push("child-panel"),e&&t&&r.push("shadows"),n&&r.push("disabled"),r}(n,m,t),R=function(e,t,n,r,a){var i=[],s="panel-header-wrap";return s+=e?" panel-open":" panel-closed",t&&(n||!n&&r.length)&&i.push("maybe-has-default"),a&&!t?i.push("has-summary-no-child"):t||i.push("no-child"),t&&i.push("child-panel"),t&&!a&&i.push("no-summary"),{panelHeaderClasses:i,panelHeaderBaseClass:s}}(m,n,c,o,S),I=R.panelHeaderClasses,D=R.panelHeaderBaseClass;return"false"===e.writable?l.createElement(O,{panelTitle:T.title,description:"",panelName:N}):l.createElement("div",{key:e.panelKey,className:"".concat(A," ").concat(C.join(" "))},l.createElement("div",{onClick:function(t){if(e.disabled)return!1;!function(e,t,n,r,a,i,s){if(e.hasOwnProperty("callback")&&"function"===typeof e.callback&&!1===e.callback(s))return;var c="open";if(a&&(c="closing"),e.setPanelStatus(c),e.setPanelClicked(r),e.disabled)return!1;e.togglePanelsOpen(r)}(e,0,0,N,m,0,t)},className:"".concat(D," ").concat(I.join(" ")),id:"wpmdb-".concat((0,k.hs)(N))},l.createElement(h,T)),l.createElement(y,(0,r.Z)({shouldClose:Z,panelAnimating:g,panelName:N,panelOpenState:E,panelContentClass:d,panelOpen:m,className:P||"",id:N},e)))}))},29942:function(e,t,n){"use strict";n.d(t,{vP:function(){return k},VU:function(){return h},op:function(){return w},wM:function(){return x},lL:function(){return H},mF:function(){return V},mp:function(){return R},Tc:function(){return S},gS:function(){return U},NR:function(){return K},Nh:function(){return F},fX:function(){return G},OK:function(){return q},CX:function(){return B},Yu:function(){return z},qu:function(){return I},sk:function(){return L},SC:function(){return D},Ol:function(){return C},j_:function(){return W},fg:function(){return v},Ph:function(){return O},zj:function(){return A},st:function(){return j},hs:function(){return E},XV:function(){return b},ME:function(){return M},mQ:function(){return T},oj:function(){return N}});var r=n(27166),a=n(33032),i=n(31125),s=n(62295),c=n(42233),l=n(10734),o=n.n(l),u=n(67101),p=n.n(u),m=n(12544),d=n.n(m),f=n(81294),_=n(68424);var g=n(27114);function b(e,t){return-1!==o()(e,t)?p()(e,t):[].concat((0,i.Z)(e),[t])}function h(e,t){return(0,i.Z)(new Set([].concat((0,i.Z)(e),(0,i.Z)(t))))}function v(e,t){return e.filter((function(e){return!t.includes(e)}))}function E(e){e=(e=e.replace(/^\s+|\s+$/g,"")).toLowerCase();for(var t="\xe0\xe1\xe3\xe4\xe2\xe8\xe9\xeb\xea\xec\xed\xef\xee\xf2\xf3\xf6\xf4\xf9\xfa\xfc\xfb\xf1\xe7\xb7/_,:;",n=0,r=t.length;n<r;n++)e=e.replace(new RegExp(t.charAt(n),"g"),"aaaaaeeeeiiiioooouuuunc------".charAt(n));return e=e.replace(/[^a-z0-9 -]/g,"").replace(/\s+/g,"-").replace(/-+/g,"-")}function w(e,t){return y.apply(this,arguments)}function y(){return y=(0,a.Z)((0,r.Z)().mark((function e(t,n){var a,i,s,c,l=arguments;return(0,r.Z)().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return a=l.length>2&&void 0!==l[2]&&l[2],i=l.length>3&&void 0!==l[3]?l[3]:null,s={method:"POST",credentials:"same-origin",headers:{"Content-Type":"application/json","X-WP-Nonce":window.wpmdb_data.nonces.rest_nonce},body:JSON.stringify(n),signal:window.wpmdb_abort_controller.signal},a&&(s.signal=a.signal),e.next=6,fetch(window.wpmdb_data.MDB_API_BASE+t,s);case 6:if((c=e.sent).ok){e.next=10;break}throw i&&i((function(e,t){e({type:"MDB_REST_NOT_ACTIVE"}),e((0,_.A_)())})),new Error(c.statusText);case 10:return e.abrupt("return",c.json());case 11:case"end":return e.stop()}}),e)}))),y.apply(this,arguments)}function x(e){var t=e.preRequest,n=void 0===t?function(){}:t,i=e.asyncFn,s=e.requestFailed,c=void 0===s?function(){}:s,l=e.requestSuccess,o=void 0===l?function(){}:l;return(0,a.Z)((0,r.Z)().mark((function e(){var t;return(0,r.Z)().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return n(),e.prev=1,e.next=4,i;case 4:t=e.sent,e.next=11;break;case 7:return e.prev=7,e.t0=e.catch(1),c(e.t0),e.abrupt("return",!1);case 11:if(t.success){e.next=13;break}return e.abrupt("return",c(t));case 13:return o(t),e.abrupt("return",t);case 15:case"end":return e.stop()}}),e,null,[[1,7]])})))}function k(e,t){return Z.apply(this,arguments)}function Z(){return(Z=(0,a.Z)((0,r.Z)().mark((function e(t,n){var a,i,s,c;return(0,r.Z)().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:for(i in a=new FormData,n)a.append(i,n[i]);return s={method:"POST",body:a,signal:window.wpmdb_abort_controller.signal},e.next=5,fetch(t,s);case 5:if((c=e.sent).ok){e.next=8;break}throw new Error(c.statusText);case 8:return e.abrupt("return",c.json());case 9:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function N(e){return P.apply(this,arguments)}function P(){return P=(0,a.Z)((0,r.Z)().mark((function e(t){var n,a,i,s,c,l=arguments;return(0,r.Z)().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:for(i in n=l.length>1&&void 0!==l[1]&&l[1],a=new FormData,t)a.append(i,t[i]);return s={method:"POST",credentials:"same-origin",body:a,signal:window.wpmdb_abort_controller.signal},n&&(s.signal=n.signal),e.next=7,fetch(window.ajaxurl,s);case 7:if((c=e.sent).ok){e.next=14;break}return e.t0=Error,e.next=12,c.text();case 12:throw e.t1=e.sent,new e.t0(e.t1);case 14:return e.abrupt("return",c.json());case 15:case"end":return e.stop()}}),e)}))),P.apply(this,arguments)}function S(e){w("/error-migration",{action:"cancel",error_message:(0,g.Y)(e)})}function T(e){return/^([a-z]([a-z]|\d|\+|-|\.)*):(\/\/(((([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(%[\da-f]{2})|[!\$&'\(\)\*\+,;=]|:)*@)?((\[(|(v[\da-f]{1,}\.(([a-z]|\d|-|\.|_|~)|[!\$&'\(\)\*\+,;=]|:)+))\])|((\d|[1-9]\d|1\d\d|2[0-4]\d|25[0-5])\.(\d|[1-9]\d|1\d\d|2[0-4]\d|25[0-5])\.(\d|[1-9]\d|1\d\d|2[0-4]\d|25[0-5])\.(\d|[1-9]\d|1\d\d|2[0-4]\d|25[0-5]))|(([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(%[\da-f]{2})|[!\$&'\(\)\*\+,;=])*)(:\d*)?)(\/(([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(%[\da-f]{2})|[!\$&'\(\)\*\+,;=]|:|@)*)*|(\/((([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(%[\da-f]{2})|[!\$&'\(\)\*\+,;=]|:|@)+(\/(([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(%[\da-f]{2})|[!\$&'\(\)\*\+,;=]|:|@)*)*)?)|((([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(%[\da-f]{2})|[!\$&'\(\)\*\+,;=]|:|@)+(\/(([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(%[\da-f]{2})|[!\$&'\(\)\*\+,;=]|:|@)*)*)|((([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(%[\da-f]{2})|[!\$&'\(\)\*\+,;=]|:|@)){0})(\?((([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(%[\da-f]{2})|[!\$&'\(\)\*\+,;=]|:|@)|[\uE000-\uF8FF]|\/|\?)*)?(\#((([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(%[\da-f]{2})|[!\$&'\(\)\*\+,;=]|:|@)|\/|\?)*)?$/i.test(e)}var O=function(e){return e?e.replace(/(^\w+:|^)\/\//,"//"):e},A=function(e){return e?e.replace(/(^\w+:|^)\/\//,""):e},C=function(e){return e!==window.WPMDBStore.getState().migrations.local_site.this_prefix},R=function(e){var t=e.intent;if(""!==t)return{savefile:(0,c.__)("Export","wp-migrate-db"),backup_local:(0,c.__)("Backup","wp-migrate-db"),import:(0,c.__)("Import","wp-migrate-db"),find_replace:(0,c.__)("Find & Replace","wp-migrate-db"),push:(0,c.__)("Push","wp-migrate-db"),pull:(0,c.__)("Pull","wp-migrate-db")}[t]};function I(e,t,n){var r;return d()(["find_replace","savefile","pull","backup_local","import"],e)?r=t.write_permissions:"push"===e&&(r=n.write_permissions),{disabled:"false"===r,uploads_dir:"push"===e?n.uploads_dir:t.this_uploads_dir}}function D(e,t){var n=t.migrations,r=n.local_site,a=n.remote_site,i=n.current_migration,s=(0,f.O)(r,a,i,e);return(s=s.join(""))===s.toLowerCase()?null:("1"===r.lower_case_table_names||"push"!==e&&"savefile"!==e)&&("1"===a.lower_case_table_names||"pull"!==e)||null}function L(e,t){var n=t.migrations,r=t.multisite_tools,a=n.local_site,i=n.remote_site;if(d()(["push","pull"],e)){if(!r||!a.site_details||!i.site_details)return!1;if(a.site_details.is_multisite!==i.site_details.is_multisite)return"1"!==a.mst_available||"1"!==i.mst_available||!r.is_licensed}return!1}function M(){var e=(0,s.v9)((function(e){return e.settings})),t=e.masked_licence;if(!e.isPro)return!0;var n=(0,s.v9)((function(e){return e.dbi_api_data})).licence.licence_status;return null===t&&!window.wpmdb_settings.license_constant||![null,"active_licence","subscription_expired"].includes(n)}function F(e){return window.reactpluginBuildURLmdb+e}function B(e){return d()(e.match(/\.sql|\.gz/gi),".gz")}function j(e){return e.replace(/\.sql|\.gz/gi,"")}var U=function(e){var t=[];return e?(Object.values(e).forEach((function(e){"string"===typeof e?t.push(e):"object"===typeof e&&(e.hasOwnProperty("licence")?t.push(e.licence):e.hasOwnProperty("default")&&t.push(e.default))})),0===t.length?null:t):null},z=function(){return"free"!=={NODE_ENV:"production",PUBLIC_URL:"",WDS_SOCKET_HOST:void 0,WDS_SOCKET_PATH:void 0,WDS_SOCKET_PORT:void 0,FAST_REFRESH:!0}.REACT_APP_BUILD_MODE};function G(e){var t=[];if(null===e||!e.hasOwnProperty("imported")||!1===e.imported)return t;var n=e.value,r=n.current_migration,a=n.media_files;return r.hasOwnProperty("post_types_option")&&"all"!==r.post_types_option&&["pull","import"].includes(r.intent)&&t.push("post_types"),a.hasOwnProperty("enabled")&&!0===a.enabled&&t.push("media_files"),t}var V=function(e,t){var n={pull:(0,c.__)("to pull remote data into this site","wp-migrate-db"),push:(0,c.__)("to push data to a remote site","wp-migrate-db"),import:(0,c.__)("to import data into this site","wp-migrate-db"),regex:(0,c.__)("to use Regular Expressions","wp-migrate-db"),dryRun:(0,c.__)("to view details on the exact changes that will be made","wp-migrate-db")},r=(0,c.__)("Activate your license","wp-migrate-db"),a="#settings/enter",i="";t||(r=(0,c.__)("Upgrade","wp-migrate-db"),a="https://deliciousbrains.com/wp-migrate-db-pro/upgrade/?utm_campaign=WP%2BMigrate%2BDB%2BPro%2BUpgrade&utm_source=MDB%2BFree&utm_medium=insideplugin",i='target="_blank"');var s=(0,c.__)('<a href="%s" %s rel="noopener noreferrer" class="upgrade">%s</a> %s',"wp-migrate-db");return(0,c.gB)(s,a,i,r,n[e])},H=function(e){return(e/1e3/1024).toFixed(2)},W=function(e,t){var n,r,a,i,s,c=t.current_migration,l=t.local_site,o=t.remote_site,u="pull"===c.intent,p=u?o:l,m=p.site_details,d=m.themes_path,f=m.plugins_path,_=m.muplugins_path,g=m.content_dir,b=u?p.path:p.this_path,h=function(e,t){return void 0===e?null:e.replace(t,"").replace(/^\/|\/$/g,"")};switch(e){case"themes":case"theme_files":return null!==(n=h(d,b))&&void 0!==n?n:"wp-content/themes";case"plugins":case"plugin_files":return null!==(r=h(f,b))&&void 0!==r?r:"wp-content/plugins";case"muplugins":case"muplugin_files":return null!==(a=h(_,b))&&void 0!==a?a:"wp-content/mu-plugins";case"media":case"media_files":return null!==(i=h(u?p.wp_upload_dir:p.this_wp_upload_dir,b))&&void 0!==i?i:"wp-content/uploads";case"others":case"other_files":return null!==(s=h(g,b))&&void 0!==s?s:"wp-content";default:return""}},K=function(e,t,n,r){var a=t.localSource?r:n;switch(e){case"theme_files":case"themes":return a.themes_permissions;case"plugin_files":case"plugins":return a.plugins_permissions;case"muplugin_files":case"muplugins":return a.muplugins_permissions;case"other_files":case"others":return a.others_permissions;default:return a.write_permissions}},q=function(){var e=window.location.hash.substring(1);if(e.includes("unsaved")||e.includes("migrate")){var t=e.split("/");return t.length>1&&t}return!1}},18832:function(e,t,n){"use strict";var r=n(10734),a=n.n(r);t.Z=function(e,t,n){var r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"",i=[];t.forEach((function(e){return-1===a()(n,e)&&i.push(e),null})),e(i,r)}},66055:function(e,t,n){"use strict";n.d(t,{m:function(){return a}});var r=n(18489);function a(e,t){for(var n=arguments.length,a=new Array(n>2?n-2:0),i=2;i<n;i++)a[i-2]=arguments[i];return(0,r.Z)({type:e,payload:t},a)}},74094:function(e,t,n){"use strict";n.d(t,{J:function(){return i}});var r=n(29942),a=n(58696),i=function(){return function(e,t){return!!(0,r.Yu)()&&(0,a.C)("status",t()).connecting}}},83115:function(e,t,n){"use strict";n.d(t,{$1:function(){return d},G$:function(){return u},_s:function(){return m},dy:function(){return p},g1:function(){return o}});var r=n(14251),a=n(3460),i=n(52650),s=n(29950),c=n(66866),l=n(15265),o=function(){return function(e){if(e((0,r.LR)(!0)),e(u(!0)),e({type:"WPMDB_PRE_MIGRATION"}),e({type:"MIGRATION_STARTED"}),!e((0,i.b)()))return!1;e((0,a.Cy)())}},u=function(e){return{type:s.Um,payload:e}},p=function(e){return{type:c.DL,payload:e}},m=function(e){return{type:l.DQ,payload:e}},d=function(e){return{type:l._b,payload:e}}},22497:function(e,t,n){"use strict";n.d(t,{KG:function(){return a},KJ:function(){return i},Kw:function(){return c},O:function(){return l},ku:function(){return s}});var r=n(18066);function a(e,t){return function(n,a){n({type:r.TO,payload:{name:e,fn:t}})}}function i(e,t){return function(n,a){n({type:r.fK,payload:{name:e,fn:t}})}}function s(e){return function(t,n){t({type:r.l3,payload:{name:e}})}}function c(e){return function(t,n){n().mdb_filters.actions.forEach((function(t){t.name===e&&t.fn.call()}))}}function l(e){for(var t=this,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",r=arguments.length,a=new Array(r>2?r-2:0),i=2;i<r;i++)a[i-2]=arguments[i];return function(r,i){var s=i().mdb_filters.filters,c=n;return s.forEach((function(n){var r;n.name===e&&(c=(r=n.fn).call.apply(r,[t,c].concat(a)))})),c}}},47895:function(e,t,n){"use strict";n.d(t,{C:function(){return u},Lk:function(){return p},Tf:function(){return o},q$:function(){return l}});var r=n(27166),a=n(33032),i=n(12544),s=n.n(i),c=n(66055);function l(e,t){return function(n,r){n((0,c.m)(t,e))}}function o(e,t,n){return function(){var i=(0,a.Z)((0,r.Z)().mark((function a(i,s){return(0,r.Z)().wrap((function(r){for(;;)switch(r.prev=r.next){case 0:i((0,c.m)(n,{type:e,status:t}));case 1:case"end":return r.stop()}}),a)})));return function(e,t){return i.apply(this,arguments)}}()}function u(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";return function(t,n){var r=n().migrations;""===e&&(e=r.current_migration.intent);var a="true"===r.local_site.is_multisite;if(s()(["find_replace","savefile","backup_local"],e)&&a)return!0;if(s()(["push","pull"],e)){if(!r.remote_site.site_details)return!1;var i="true"===r.remote_site.site_details.is_multisite;return a||i}return!1}}function p(e,t,n,r){return"migration"===r?"pull"===e||"import"===e?n:t:"pull"===e||"import"===e?t:n}},14251:function(e,t,n){"use strict";n.d(t,{CV:function(){return f},E2:function(){return g},ED:function(){return _},I8:function(){return p},LR:function(){return x},OZ:function(){return Z},P_:function(){return v},S5:function(){return b},Tf:function(){return u},fJ:function(){return d},gy:function(){return E},j8:function(){return k},nX:function(){return N},oA:function(){return m},qg:function(){return h},t9:function(){return w},uI:function(){return P},vq:function(){return y}});var r=n(29942),a=n(66055),i=n(29950),s=n(66866),c=n(22973),l=n(4516),o=n(3460);function u(e){return(0,a.m)(i.d0,e)}function p(e){return(0,a.m)(i.n_,e)}function m(){return function(e){e((0,a.m)("RESET_APP",{}))}}function d(e){return{type:c.Qu,payload:e}}function f(e){return function(t,n){var a=n();t({type:i.av,payload:e});var s=a.migrations.current_migration;t(d((0,r.mp)({intent:e.intent,local_site:a.migrations.local_site,remote_site:a.migrations.remote_site,connected:s.connected}))),"savefile"===e.intent&&(0,r.SC)("savefile",n())&&t(_())}}function _(){return function(e){e((0,a.m)("SET_CONNECTION_STATUS",{key:"mixed_case_table_name_warning",statusVal:!0}))}}function g(e){return function(t,n){if(t({type:i.KU,payload:e}),"backup_selected"===(0,l.r5)("backup_option",n())){var r=t((0,o.K6)());t(h(r))}}}function b(e,t){return function(n){n({type:i.mu,payload:e}),"all"===e&&n(g(t))}}function h(e){return{type:i.O$,payload:e}}function v(e){return function(t,n){t({type:i.Yl,payload:e});var r=t((0,o.K6)());t(h(r))}}function E(e){return{type:i.ic,payload:e}}function w(e,t){return function(n){n({type:i.Nt,payload:e}),"all"===e&&n(E(t))}}function y(e){return{type:i.Ss,payload:e}}function x(e){return{type:s.mx,payload:e}}function k(e){return{type:i.b_,payload:e}}function Z(e,t){return function(n){n({type:i.mO,payload:e}),n({type:i.Ac,payload:t})}}function N(e){return function(t){t({type:i.u2,payload:e})}}function P(e){return function(t){t({type:i.EF,payload:e})}}},58900:function(e,t,n){"use strict";n.d(t,{B3:function(){return Z},BG:function(){return w},K_:function(){return k},s2:function(){return N}});var r=n(88368),a=n(89472),i=n(27166),s=n(33032),c=n(12544),l=n.n(c),o=n(42233),u=n(62295),p=n(4516),m=n(29942),d=n(66866),f=n(66055),_=n(42222),g=n(91828),b=n(38906),h=n(22497),v=n(86645),E=n(29816),w=function(e){return function(){var e=(0,s.Z)((0,i.Z)().mark((function e(t,n){var r,a,s,c,u,m,_,g;return(0,i.Z)().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(t((0,f.m)(d.V$,"Finalizing Migration")),t((0,f.m)(d.MG,"FINALIZING")),r=(0,p.r5)("intent",n()),a=(0,p.r5)("stages",n()),s=(0,p.xg)("dump_info",n()),c=(0,p.r5)("databaseEnabled",n()),u=(0,p.r5)("fseDumpFilename",n()),0!==Object.entries(s).length||!("savefile"===r&&c||"backup_local"===r)){e.next=9;break}throw new Error((0,o.__)("File dump info empty","wp-migrate-db"));case 9:if("backup_local"===r||"savefile"===r){e.next=11;break}return e.abrupt("return",t(y(r)));case 11:if(m=window.wpmdb_data.this_download_url+(c?encodeURIComponent(s.dump_filename):u),_=(0,p.r5)("advanced_options_selected",n()),(g=c?a.length>1:"savefile"===r)?m+="&zip=1":l()(_,"gzip_file")&&(m+="&gzip=1"),g&&(m+="&fullSiteExport=1"),"savefile"!==r){e.next=19;break}return t(S(r)),e.abrupt("return",setTimeout((function(){window.location=m}),0));case 19:return e.abrupt("return",t(S(r,s)));case 20:case"end":return e.stop()}}),e)})));return function(t,n){return e.apply(this,arguments)}}()},y=function(e){return function(){var t=(0,s.Z)((0,i.Z)().mark((function t(n,r){return(0,i.Z)().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.abrupt("return",n((0,s.Z)((0,i.Z)().mark((function e(){var t,a,s,c,l;return(0,i.Z)().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,(0,p.Gn)(r());case 2:return t=e.sent,a=(0,p.r5)("databaseEnabled",r()),s={tables:a?t.join(","):""},(0,p.do)(r())&&(s.prefix=(0,p._P)("this_prefix",r())),s=n((0,h.O)("wpmdbFinalizeMigration",s)),l=(0,p.xg)("document_title",r()),document.title=(0,o.__)("Finalizing","wp-migrate-db")+" - "+l,e.prev=9,e.next=12,(0,m.op)("/finalize-migration",s);case 12:c=e.sent,n((0,h.Kw)("afterFinalizeMigration")),e.next=21;break;case 16:return e.prev=16,e.t0=e.catch(9),console.error(e.t0),n((0,b.m7)({error_type:d.gF,error_message:e.t0})),e.abrupt("return",!1);case 21:if(c.success){e.next=24;break}return n((0,b.m7)({error_type:d.gF,error_message:c.data})),e.abrupt("return",!1);case 24:return e.abrupt("return",c);case 25:case"end":return e.stop()}}),e,null,[[9,16]])})))).then(function(){var t=(0,s.Z)((0,i.Z)().mark((function t(r){return(0,i.Z)().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(r){t.next=2;break}return t.abrupt("return",!1);case 2:return t.next=4,x(n,e);case 4:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}()).catch((function(e){n((0,b.m7)({error_type:d.gF,error_message:e}))})));case 1:case"end":return t.stop()}}),t)})));return function(e,n){return t.apply(this,arguments)}}()},x=function(){var e=(0,s.Z)((0,i.Z)().mark((function e(t,n){var r,a,s;return(0,i.Z)().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t((0,f.m)(d.V$,(0,o.__)("Flushing caches...","wp-migrate-db"))),(r=new FormData).append("action","wpmdb_flush"),r.append("nonce",window.wpmdb_data.nonces.flush),e.next=6,fetch(window.ajaxurl,{method:"POST",body:r});case 6:if((a=e.sent).ok){e.next=9;break}throw new Error(a.statusText);case 9:return e.next=11,a.json();case 11:if((s=e.sent).success){e.next=14;break}throw new Error(s.data);case 14:return e.abrupt("return",t(S(n)));case 15:case"end":return e.stop()}}),e)})));return function(t,n){return e.apply(this,arguments)}}(),k=function(){return function(){var e=(0,s.Z)((0,i.Z)().mark((function e(t,n){var r,s,c,l,o;return(0,i.Z)().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,(0,p.xg)("migration_tables",n());case 2:r=e.sent,s=!1,c=(0,a.Z)(r),e.prev=5,c.s();case 7:if((l=c.n()).done){e.next=14;break}if(o=l.value,!(s=/(.*)_options|(.*)_sitemeta/.test(o))){e.next=12;break}return e.abrupt("break",14);case 12:e.next=7;break;case 14:e.next=19;break;case 16:e.prev=16,e.t0=e.catch(5),c.e(e.t0);case 19:return e.prev=19,c.f(),e.finish(19);case 22:return e.abrupt("return",s);case 23:case"end":return e.stop()}}),e,null,[[5,16,19,22]])})));return function(t,n){return e.apply(this,arguments)}}()},Z=function(e){return function(t,n){var a=(0,p.r5)("intent",n()),i=(0,p.xg)("document_title",n()),s=n().profiles;document.title=i;l()(["import","pull","find_replace"],a)&&(t(k())&&"COMPLETE"===e&&(l()(["import","pull"],a)?window.location.href=function(){var e=window.location.hash,t=window.location.href.replace(e,""),n=(0,r.Z)(s.recent,1)[0];n=void 0!==n?n.id:"";var a=0;-1===e.indexOf("#unsaved/")&&-1===e.indexOf("#migrate/")||(-1!==e.indexOf("migrate")&&(a=1),n=e.match(/([0-9]+)(?=[^/]*$)/gm));return"".concat(t,"&redirect_profile=").concat(n,"&saved_profile=").concat(a)}():window.location.reload()))}},N=function(e){var t=(0,_.i2)(e),n=(0,_.er)(e),r=(0,_.$s)(e),a=(0,o.gB)((0,o.__)("%ss","wp-migrate-db"),t);return n>0&&(a=(0,o.gB)((0,o.__)("%sm %s","wp-migrate-db"),n,a)),r>0&&(a=(0,o.gB)((0,o.__)("%shr %s","wp-migrate-db"),r,a)),a},P=function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];return function(t,n){var a,i,s,c=(0,p.r5)("intent",n());(0,m.Yu)()&&(s=(0,m.zj)((0,p.FY)("url",n())));var l=(0,p.xg)("migration_size",n()),u=(0,p.xg)("timer",n()),d=(0,p.xg)("migration_size",n()),f=(0,_.UJ)(l),b=N(u.time),h="pull"===c?(0,o.__)("from","wp-migrate-db"):(0,o.__)("to","wp-migrate-db");switch(c){case"pull":case"push":var w=(0,p.r5)(["payloadSizeHistory","stages_complete"],n()),y=(0,r.Z)(w,2),x=y[0],k=y[1],Z=(0,o.gB)((0,o.__)('with an average request size of <span class="text-primary semibold">%sMB</span>',"wp-migrate-db"),(0,m.lL)((0,v.O)(x)));a="".concat(g.B[c],' <span class="regular">').concat(h,"</span> ").concat(s),i=(0,o.gB)((0,o.__)('<span class="text-primary semibold">%s%s</span> of data was migrated in <span class="text-primary semibold">%s</span> %s'),f,(0,_.TJ)(d),b,k&&k.some((function(e){return E.AG.includes(e)}))?Z:"");break;case"savefile":a=(0,o.__)("Export Complete","wp-migrate-db"),i=(0,o.gB)((0,o.__)('<span class="text-primary semibold">%s%s</span> of data was exported in <span class="text-primary semibold">%s</span>',"wp-migrate-db"),f,(0,_.TJ)(d),b);break;case"import":a=(0,o.__)("Import Complete","wp-migrate-db"),i=(0,o.gB)((0,o.__)('Completed in <span class="text-primary semibold">%s</span>'),b);break;case"find_replace":a=(0,o.__)("Find & Replace Complete","wp-migrate-db"),i=(0,o.gB)((0,o.__)('<span class="text-primary semibold">%s%s</span> of data was replaced in <span class="text-primary semibold">%s</span>',"wp-migrate-db"),f,(0,_.TJ)(d),b);break;case"backup_local":a=(0,o.__)("Backup Complete","wp-migrate-db"),i=e.dump_path?(0,o.__)("The backup file has been saved to your server.","wp-migrate-db"):""}return{title:a,message:i}}},S=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return function(){var e=(0,s.Z)((0,i.Z)().mark((function e(r,a){var s,c,l;return(0,i.Z)().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(r((0,b.z6)(!0)),(0,u.dC)((function(){r((0,f.m)(d.Jj,"COMPLETE")),r((0,b.Uo)());var e=r(P(t)),n=e.title,a=e.message;r((0,f.m)(d.V$,n)),r((0,f.m)(d.Du,a))})),s=(0,p.xg)("document_title",a()),!(0,m.Yu)()){e.next=9;break}return e.next=6,Promise.resolve().then(n.bind(n,44789));case 6:c=e.sent,l=c.TrackMigrationComplete,r(l());case 9:document.title=(0,o.__)("Complete","wp-migrate-db")+" - "+s;case 10:case"end":return e.stop()}}),e)})));return function(t,n){return e.apply(this,arguments)}}()}},3460:function(e,t,n){"use strict";n.d(t,{Am:function(){return B},Cy:function(){return Z},Gn:function(){return R},K6:function(){return j},Kh:function(){return I},Rw:function(){return D},Z6:function(){return N},al:function(){return M},h8:function(){return v},ly:function(){return y},p_:function(){return E},uk:function(){return h},z6:function(){return U}});var r=n(88368),a=n(27166),i=n(33032),s=n(62295),c=n(42233),l=n(66055),o=n(29950),u=n(66866),p=n(38906),m=n(4516),d=n(29942),f=n(27325),_=n(22497),g="INITIATE_MIGRATION",b="MIGRATE",h="UPLOAD",v="UPLOAD_IMPORT_SUCCESSFUL",E="IMPORT_FILE",w="ADDONS_STAGE",y="COMPLETE";function x(e,t){return{find_replace:(0,c.__)("Running find & replace...","wp-migrate-db"),import:(0,c.__)("Importing SQL file...","wp-migrate-db"),push:(0,c.gB)((0,c.__)("Pushing to %s...","wp-migrate-db"),t),pull:(0,c.gB)((0,c.__)("Pulling from %s...","wp-migrate-db"),t),backup:(0,c.__)("Running backup, please wait...","wp-migrate-db"),savefile:(0,c.__)("Exporting, please wait...","wp-migrate-db")}[e]}var k={};function Z(){return function(){var e=(0,i.Z)((0,a.Z)().mark((function e(t,n){return(0,a.Z)().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,s.dC)((function(){t((0,l.m)(o.f7,"initiate_migration")),t((0,p.yo)()),t((0,l.m)(u.ol,document.title)),t((0,l.m)(o.jD,!0)),t((0,l.m)(u.V$,(0,c.__)("Establishing Connection...","wp-migrate-db")))})));case 1:case"end":return e.stop()}}),e)})));return function(t,n){return e.apply(this,arguments)}}()}function N(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"",c=arguments.length>3&&void 0!==arguments[3]&&arguments[3];return function(){var o=(0,i.Z)((0,a.Z)().mark((function i(o,d){var f,_,x,k;return(0,a.Z)().wrap((function(a){for(;;)switch(a.prev=a.next){case 0:o((0,l.m)(u.MG,e)),a.t0=e,a.next=a.t0===g?4:a.t0===b?5:a.t0===h?6:a.t0===v||a.t0===E?7:a.t0===w?8:a.t0===y?13:19;break;case 4:return a.abrupt("return",o(P()));case 5:return a.abrupt("return",o(S(s,t,c)));case 6:return a.abrupt("return",o(T(s,t)));case 7:return a.abrupt("return",o(O(t)));case 8:return a.next=10,Promise.resolve().then(n.bind(n,34653));case 10:return f=a.sent,_=f.runAddonsStage,a.abrupt("return",o(_(t,c)));case 13:return(0,m.xg)("pause_before_finalize",d())&&o((0,l.m)(u.Jj,"PAUSED")),x=(0,r.Z)(t,1),k=x[0],a.next=18,o(B(p.BG,[k],!0));case 18:return a.abrupt("return");case 19:case"end":return a.stop()}}),i)})));return function(e,t){return o.apply(this,arguments)}}()}var P=function(){return function(){var e=(0,i.Z)((0,a.Z)().mark((function e(t,n){var r,i;return(0,a.Z)().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t((0,p.kK)()),t(U(!1)),(0,s.dC)((function(){t((0,l.m)(o.f7,"initiate_migration")),t((0,l.m)(u.ol,document.title)),t((0,l.m)(o.jD,!0)),t((0,l.m)(u.V$,(0,c.__)("Establishing Connection...","wp-migrate-db")))})),r=(0,m.r5)("intent",n()),e.next=6,t(B(p.Up));case 6:if(i=e.sent){e.next=9;break}return e.abrupt("return",!1);case 9:if("import"!==r||"backup"===i){e.next=11;break}return e.abrupt("return",t(N(h)));case 11:return e.abrupt("return",t(N(b)));case 12:case"end":return e.stop()}}),e)})));return function(t,n){return e.apply(this,arguments)}}()},S=function(e,t){var n=arguments.length>2&&void 0!==arguments[2]&&arguments[2];return function(){var r=(0,i.Z)((0,a.Z)().mark((function r(i,s){var c,u,d,g,b;return(0,a.Z)().wrap((function(r){for(;;)switch(r.prev=r.next){case 0:if(!(0,m.r5)("databaseEnabled",s())){r.next=23;break}if(u=(0,f.u)("delay_between_requests",s()),d=(0,m.xg)("status",s()),n||"CANCELLED"===d||i(A(e)),g=[-1,"",0],c=p.Zk,t.length&&(c=t[0].fn,g=t[0].args),e.length&&g.push(e),!(u>0)){r.next=15;break}return r.next=12,(0,p.QK)((function(){return i(B(c,g))}),1e3*u);case 12:b=r.sent,r.next=18;break;case 15:return r.next=17,i(B(c,g));case 17:b=r.sent;case 18:if(b){r.next=20;break}return r.abrupt("return",!1);case 20:if(b.hasOwnProperty("dump_filename")||"success"===b){r.next=22;break}throw new Error(b);case 22:i((0,l.m)(o.sP,"tables"));case 23:return r.next=25,i((0,_.O)("mdbAddonActions",null));case 25:if(null!==r.sent){r.next=28;break}return r.abrupt("return",i(N(y)));case 28:case"end":return r.stop()}}),r)})));return function(e,t){return r.apply(this,arguments)}}()},T=function(e,t){return function(){var e=(0,i.Z)((0,a.Z)().mark((function e(r){var i,s,o,p;return(0,a.Z)().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return r((0,l.m)(u.V$,(0,c.__)("Uploading File","wp-migrate-db"))),s=[0],e.next=4,n.e(879).then(n.bind(n,78879));case 4:o=e.sent,p=o.uploadFileActions,i=p,t.length&&(i=t[0].fn,s=t[0].args),r(B(i,s));case 9:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}()},O=function(e){return function(){var t=(0,i.Z)((0,a.Z)().mark((function t(n){var r,i;return(0,a.Z)().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return r=null,i=null,e.length&&(i=e[0].fn,r=e[0].args),t.abrupt("return",n(B(i,r)));case 4:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}()};function A(e){return function(){var t=(0,i.Z)((0,a.Z)().mark((function t(n,r){var i,s,c,u,p;return(0,a.Z)().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:i=(0,m.r5)("intent",r()),"initiate_migration"===(0,m.r5)("current_stage",r())&&(s="",(0,d.Yu)()&&(s=(0,m.FY)("url",r())),n(F(x(i,s)))),c=(0,m.r5)("backup_option",r()),u="migrate","find_replace"===i&&(u="find_replace"),"none"!==c&&(u="backup"),e.length&&(u=e),n((0,l.m)(o.f7,u)),"backup"===u?(p=n(j()),n(L(p))):n(C(r(),(0,m.Gn)(r())));case 10:case"end":return t.stop()}}),t)})));return function(e,n){return t.apply(this,arguments)}}()}var C=function(e,t){return L(t)};function R(){return function(){var e=(0,i.Z)((0,a.Z)().mark((function e(t,n){return(0,a.Z)().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(t((0,l.m)(u.V$,(0,c.__)("Cancelling migration...","wp-migrate-db"))),"PAUSED"!==(0,m.xg)("status",n())){e.next=5;break}return t((0,l.m)(u.Jj,"CANCELLED")),e.abrupt("return",t(B((function(){return!1}))));case 5:t(U(!0)),t((0,l.m)(u.Jj,"CANCELLED"));case 7:case"end":return e.stop()}}),e)})));return function(t,n){return e.apply(this,arguments)}}()}function I(){return function(e,t){e((0,l.m)(u.V$,(0,c.__)("Pausing...","wp-migrate-db"))),e((0,l.m)(u.Jj,"PAUSED"))}}function D(){return function(){var e=(0,i.Z)((0,a.Z)().mark((function e(t,n){var r;return(0,a.Z)().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t((0,l.m)(u.Jj,"")),t((0,p.Xg)()),r=(0,m.xg)("progress_stage",n()),e.next=5,t(N(r,[{fn:k.fn,args:k.args}],!1,!0));case 5:case"end":return e.stop()}}),e)})));return function(t,n){return e.apply(this,arguments)}}()}function L(e){return(0,l.m)(u.Z,e)}function M(e){return function(t){t((0,l.m)(o.jD,e))}}var F=function(e){return function(t,n){return t((0,l.m)(u.V$,e))}};function B(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],n=arguments.length>2&&void 0!==arguments[2]&&arguments[2];return function(){var r=(0,i.Z)((0,a.Z)().mark((function r(i,o){var f,_,g,b,h,v,E;return(0,a.Z)().wrap((function(r){for(;;)switch(r.prev=r.next){case 0:if(f=(0,m.r5)("preview",o()),_=(0,m.xg)("status",o()),g=(0,m.xg)("progress_stage",o()),b=(0,m.xg)("pause_before_finalize",o()),h=(0,c.__)("Paused","wp-migrate-db"),v=(0,c.gB)("%s - %s",h,(0,c.__)("preview changes below","wp-migrate-db")),"CANCELLED"!==_){r.next=23;break}return r.prev=7,r.next=10,(0,d.op)("/cancel-migration",{action:"cancel"});case 10:E=r.sent,r.next=16;break;case 13:return r.prev=13,r.t0=r.catch(7),r.abrupt("return",i((0,p.m7)({error_type:u.gF,error_message:"".concat((0,c.__)("Migration cancellation failed")," \u2013 ").concat(r.t0)})));case 16:if(E.success){r.next=18;break}return r.abrupt("return",i((0,p.m7)({error_type:u.gF,error_message:"".concat((0,c.__)("Migration cancellation failed")," \u2013 ").concat(E.data)})));case 18:return i(U(!0)),(0,s.dC)((function(){i((0,p.Uo)()),i((0,l.m)(u.V$,(0,c.__)("Migration cancelled","wp-migrate-db"))),i((0,l.m)(u.Jj,"CANCEL_COMPLETE"))})),r.abrupt("return",!1);case 23:if(!("PAUSED"===_||b&&n)){r.next=29;break}return b&&n&&i((0,l.m)(u.mx,!1)),(0,s.dC)((function(){i((0,l.m)(u.V$,f&&"COMPLETE"===g?v:h)),i((0,p.N6)())})),k.fn=e,k.args=t,r.abrupt("return",!1);case 29:return r.next=31,i(e.apply(null,t));case 31:return r.abrupt("return",r.sent);case 32:case"end":return r.stop()}}),r,null,[[7,13]])})));return function(e,t){return r.apply(this,arguments)}}()}var j=function(){return function(e,t){var n=(0,m.r5)("intent",t()),r=(0,m.NR)("current_migration",t()),a=r.backup_option,i=r.tables_option,s=[],c="push"===n?(0,m.FY)("prefixed_tables",t()):(0,m._P)("this_prefixed_tables",t()),l="push"===n?(0,m.FY)("tables",t()):(0,m._P)("this_tables",t());switch(a){case"backup_only_with_prefix":s=c;break;case"backup_selected":var o;o="import"===n?(0,m.FY)("tables",t()):"selected"===i?(0,m.r5)("tables_selected",t()):"pull"===n?(0,m.FY)("prefixed_tables",t()):(0,m._P)("this_prefixed_tables",t()),s=l.filter((function(e){return o.includes(e)}));break;case"backup_manual_select":s="push"===n?(0,m.FY)("tables",t()):(0,m._P)("this_tables",t())}return s=e((0,_.O)("wpmdbBackupTables",s,a))}},U=function(e){return(0,l.m)(u.ti,e)}},42714:function(e,t,n){"use strict";n.d(t,{I6:function(){return d},KO:function(){return m},M7:function(){return u},Wv:function(){return h},fP:function(){return p},gF:function(){return b},sw:function(){return f},v_:function(){return g}});var r=n(27166),a=n(33032),i=n(12544),s=n.n(i),c=n(4516),l=n(66055),o=n(66866);function u(e,t){var n=0;for(var r in e)s()(t,r)&&(n+=parseInt(e[r]));return n}var p=function(e,t){return function(){var t=(0,a.Z)((0,r.Z)().mark((function t(a,i){var s,l;return(0,r.Z)().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if("import"!==e){t.next=8;break}return t.next=3,Promise.resolve().then(n.bind(n,67821));case 3:return s=t.sent,l=s.selectFromImportData,t.abrupt("return",l("table_sizes",i()));case 8:if("pull"===e){t.next=10;break}return t.abrupt("return",(0,c._P)("this_table_sizes",i()));case 10:return t.abrupt("return",(0,c.FY)("table_sizes",i()));case 11:case"end":return t.stop()}}),t)})));return function(e,n){return t.apply(this,arguments)}}()},m=function(e){return function(){var t=(0,a.Z)((0,r.Z)().mark((function t(n,a){return(0,r.Z)().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if("push"===e){t.next=2;break}return t.abrupt("return",(0,c._P)("this_table_sizes",a()));case 2:return t.abrupt("return",(0,c.FY)("table_sizes",a()));case 3:case"end":return t.stop()}}),t)})));return function(e,n){return t.apply(this,arguments)}}()};function d(e){return function(t,n){return t((0,l.m)(o.tO,e)),e}}function f(){return function(e,t){var n=(0,c.xg)("document_title",t());document.title=n}}function _(e){return function(t,n){var r=(0,c.r5)("stages",n()),a=(0,c.r5)("current_stage",n()),i=(0,c.xg)("total_stage_size",n()),s=r.length;a="migrate"===a?"tables":a;var u=r.findIndex((function(e){return e===a}))+1,p=" ".concat(u," of ").concat(s),m=(0,c.xg)("document_title",n()),d=Math.floor(e/i*100)||0;return document.title="".concat(d,"% Stage ").concat(p," - ").concat(m),t((0,l.m)(o.QH,e))}}function g(){return function(e,t){e((0,l.m)(o.hg))}}function b(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return function(){var n=(0,a.Z)((0,r.Z)().mark((function n(a,i){var s,l,o;return(0,r.Z)().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:s=e>1e3?Math.ceil(e):e,l=parseInt(s),Number.isInteger(l)&&l>=1&&(s=l),o=parseInt((0,c.xg)("stage_size",i())),t?o=parseInt(e):o+=s,a(_(o));case 6:case"end":return n.stop()}}),n)})));return function(e,t){return n.apply(this,arguments)}}()}function h(e){return function(){var t=(0,a.Z)((0,r.Z)().mark((function t(n,a){return(0,r.Z)().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:(0,c.xg)("total_stage_size",a()).length||n(d(e));case 2:case"end":return t.stop()}}),t)})));return function(e,n){return t.apply(this,arguments)}}()}},62457:function(e,t,n){"use strict";n.d(t,{U:function(){return m},m:function(){return p}});var r=n(27166),a=n(33032),i=n(62295),s=n(42233),c=n(66055),l=n(66866),o=n(38906),u=n(29942);function p(e){return function(t){(0,i.dC)((function(){t((0,c.m)(l.V$,(0,s.__)("Migration Failed","wp-migrate-db"))),t((0,c.m)(l.cE,e)),t(m())})),(0,u.Tc)(e)}}var m=function(){return function(){var e=(0,a.Z)((0,r.Z)().mark((function e(t,n){return(0,r.Z)().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:t((0,o.N6)()),t((0,o.sw)());case 2:case"end":return e.stop()}}),e)})));return function(t,n){return e.apply(this,arguments)}}()}},38068:function(e,t,n){"use strict";n.d(t,{N6:function(){return o},Xg:function(){return l},hI:function(){return p},yo:function(){return u}});var r=n(27166),a=n(33032),i=n(4516),s=n(66055),c=n(66866),l=function(){return function(){var e=(0,a.Z)((0,r.Z)().mark((function e(t,n){var a,l,o;return(0,r.Z)().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:a=(0,i.xg)("timer",n()),l={on:!0,time:a.time,start:Date.now()-a.time},t((0,s.m)(c.G,l)),o=setInterval((function(){var e=(0,i.xg)("timer",n()),r=Date.now()-e.start;t((0,s.m)(c.cJ,r))}),1e3),t((0,s.m)(c.hS,o));case 5:case"end":return e.stop()}}),e)})));return function(t,n){return e.apply(this,arguments)}}()},o=function(){return function(){var e=(0,a.Z)((0,r.Z)().mark((function e(t,n){var a;return(0,r.Z)().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:t((0,s.m)(c.c4,!1)),a=(0,i.xg)("timer",n()).timer_instance,clearInterval(a);case 3:case"end":return e.stop()}}),e)})));return function(t,n){return e.apply(this,arguments)}}()},u=function(){return function(){var e=(0,a.Z)((0,r.Z)().mark((function e(t,n){return(0,r.Z)().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:t((0,s.m)(c.sr,0)),t((0,s.m)(c.cJ,0));case 2:case"end":return e.stop()}}),e)})));return function(t,n){return e.apply(this,arguments)}}()},p=function(){return function(){var e=(0,a.Z)((0,r.Z)().mark((function e(t,n){return(0,r.Z)().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:t(u()),t(l());case 2:case"end":return e.stop()}}),e)})));return function(t,n){return e.apply(this,arguments)}}()}},38906:function(e,t,n){"use strict";n.d(t,{p_:function(){return N.p_},BG:function(){return Z.BG},uk:function(){return N.uk},h8:function(){return N.h8},kK:function(){return S},QK:function(){return L},y:function(){return k},KO:function(){return P.KO},fP:function(){return P.fP},Ac:function(){return w},M7:function(){return P.M7},Up:function(){return b},Uo:function(){return T.U},Zk:function(){return R},hI:function(){return O.hI},sw:function(){return P.sw},v_:function(){return P.v_},yo:function(){return O.yo},Z6:function(){return N.Z6},Am:function(){return N.Am},z6:function(){return N.z6},m7:function(){return T.m},Wv:function(){return P.Wv},Xg:function(){return O.Xg},N6:function(){return O.N6},H_:function(){return x},gF:function(){return P.gF}});var r=n(27166),a=n(33032),i=n(12544),s=n.n(i),c=n(29942),l=n(18489),o=(0,n(56802).P1)([function(e){return e.migrations.current_migration},function(e){return e.migrations.connection_info},function(e){return e.migrations.search_replace}],(function(e,t,n){var r=e.intent,a=(0,l.Z)({},n);s()(["push","pull","import","savefile"],r)||(delete a.standard_search_replace,delete a.standard_options_enabled,delete a.standard_search_visible);var i={current_migration:e,search_replace:a};return s()(["push","pull"],r)&&(i.connection_info=t),i})),u=n(4516),p=n(61358),m=n(86191),d=n(66866),f=n(66055),_=n(29950),g=n(22497),b=function(){return function(){var e=(0,a.Z)((0,r.Z)().mark((function e(t,i){var s,l,b,E,w,y,x,k;return(0,r.Z)().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t(function(){var e=(0,a.Z)((0,r.Z)().mark((function e(t,n){var a;return(0,r.Z)().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:t((0,P.v_)()),t((0,f.m)(d.u9)),t((0,f.m)(_.GG,[])),t((0,O.hI)()),a=(0,m.d)("current_profile",n()),t((0,p.uJ)(a));case 6:case"end":return e.stop()}}),e)})));return function(t,n){return e.apply(this,arguments)}}()),s=o(i()),l=(0,u.NR)("current_migration",i()),b=(0,u.NR)("connection_info",i()),E=(0,u.r5)("intent",i()),w=h(l,E),y=(0,u.r5)("stages",i()),x={intent:E,form_data:JSON.stringify(s),stage:w,stages:JSON.stringify(y),site_details:{local:(0,u.NR)("local_site",i()).site_details}},(x=v(E,x,b,i)).site_details=JSON.stringify(x.site_details),x=t((0,g.O)("intiateMigrationPostData",x)),e.prev=11,e.next=14,(0,c.op)("/initiate-migration",x);case 14:k=e.sent,t(function(){var e=(0,a.Z)((0,r.Z)().mark((function e(t){var a,i;return(0,r.Z)().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!(0,c.Yu)()){e.next=7;break}return e.next=3,Promise.resolve().then(n.bind(n,44789));case 3:return a=e.sent,i=a.TrackMigrationStart,e.next=7,t(i());case 7:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}()),e.next=22;break;case 18:return e.prev=18,e.t0=e.catch(11),t((0,T.m)({error_type:d.gF,error_message:e.t0})),e.abrupt("return",!1);case 22:if(k.success){e.next=26;break}return t((0,T.m)({error_type:d.gF,error_message:k.data})),t((0,T.U)()),e.abrupt("return",!1);case 26:return"savefile"===E&&t((0,f.m)("SET_FSE_DUMP_FILENAME",k.data.dump_filename)),e.abrupt("return",w);case 28:case"end":return e.stop()}}),e,null,[[11,18]])})));return function(t,n){return e.apply(this,arguments)}}()};function h(e,t){var n="migrate";return e.databaseEnabled&&"none"!==e.backup_option&&"backup_local"!==t?n="backup":"import"===t&&"none"===e.backup_option&&(n="upload"),n}function v(e,t,n,r){if(s()(["push","pull"],e)){t.url=n.connection_state.url,t.key=n.connection_state.key;var a=(0,u.NR)("remote_site",r());t.site_details.remote=a.site_details,t.temp_prefix=a.temp_prefix}else if("import"===e){var i=(0,u.FY)("import_gzipped",r());t.import_info={import_gzipped:JSON.stringify(i)}}else"backup_local"===e&&(t.intent="savefile");return t}var E=n(42233),w=function(e,t){switch(e){case"backup":return(0,E.__)("Backing up","wp-migrate-db");case"find_replace":return(0,E.__)("Searching table","wp-migrate-db");case"migrate":if("backup_local"===t)return(0,E.__)("Saving","wp-migrate-db")}return(0,E.__)("Transferring","wp-migrate-db")},y=function(e){return function(t){return t((0,f.m)(d.uj,e))}},x=function(e,t,n,r,a){return function(i,s){var l,o=n[e],p=t[o],m=(0,u.r5)("current_stage",s()),d=(0,u.xg)("item_progress",s());a=parseInt(a);var f=(0,u._P)("this_table_rows",s()),_=null;(0,c.Yu)()&&(_=(0,u.FY)("table_rows",s())),l=f,"pull"===r&&"backup"===m?l=f:"pull"===r||"push"===r&&"backup"===m?l=_:"push"===r&&(l=f);var g=function(e,t,n,r,a){var i=e[t]||0,s=n[t],c=r/parseInt(s);c>1&&(c=1),-1===r&&(c=1);var l,o=a*c;return-1===r?(l=Math.ceil(parseInt(a))-Math.ceil(i),{estTransferred:o,totalTransferred:Math.ceil(l)}):{estTransferred:o,totalTransferred:l=o-i}}(d,o,l,a,p),b=g.estTransferred,h=g.totalTransferred;if(-1===a)return i(y({item:o,progress:1})),d[o]?i((0,P.gF)(h)):i((0,P.gF)(p));i(y({item:o,progress:b})),i((0,P.gF)(h))}};function k(e,t,n){return function(e,t){var r={};"undefined"!==typeof n.dump_filename&&(r.dump_filename=n.dump_filename),"undefined"!==typeof n.dump_path&&(r.dump_path=n.dump_path),"undefined"!==typeof n.full_site_export&&(r.full_site_export=n.full_site_export),"undefined"!==typeof n.export_path&&(r.export_path=n.export_path),e((0,f.m)(d.DK,r))}}var Z=n(58900),N=n(3460),P=n(42714);function S(){return function(e,t){var n=(0,u.r5)("intent",t()),r=(0,u.r5)("databaseEnabled",t()),a=(0,u.r5)("backup_option",t()),i=[];switch(n){case"push":case"pull":r&&"none"!==a&&i.push("backup"),r&&i.push("tables");break;case"find_replace":"none"!==a&&i.push("backup"),i.push("tables");break;case"import":"none"!==a&&i.push("backup"),i.push("upload"),i.push("import"),i.push("find_replace");break;case"savefile":r&&i.push("tables");break;case"backup_local":i.push("tables")}i=e((0,g.O)("addMigrationStages",i)),e((0,f.m)(_.oK,i))}}var T=n(62457),O=n(38068),A=n(83115);function C(e,t){return function(){var n=(0,a.Z)((0,r.Z)().mark((function n(a){return(0,r.Z)().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:if("backup"===e){n.next=6;break}return n.next=3,a((0,P.fP)(t));case 3:n.t0=n.sent,n.next=9;break;case 6:return n.next=8,a((0,P.KO)(t));case 8:n.t0=n.sent;case 9:return n.abrupt("return",n.t0);case 10:case"end":return n.stop()}}),n)})));return function(e){return n.apply(this,arguments)}}()}var R=function e(t,n,i){var s=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"";return function(){var l=(0,a.Z)((0,r.Z)().mark((function a(l,p){var m,g,b,h,v,y,Z,S,O,R,L,M,F,B,j,U,z;return(0,r.Z)().wrap((function(r){for(;;)switch(r.prev=r.next){case 0:return m=i||0,g=(0,u.r5)("intent",p()),b=(0,u.r5)("current_stage",p()),h=(0,u.r5)("backup_tables_selected",p()),v=(0,u.r5)("backup_option",p()),r.next=7,l(C(b,g));case 7:return y=r.sent,r.next=10,(0,u.Au)(p());case 10:if(Z=r.sent,"backup"===b&&"backup_manual_select"===v&&(Z=h),l((0,P.Wv)((0,P.M7)(y,Z))),S=o(p()),!(m>=Z.length)){r.next=38;break}if("upload"!==b){r.next=19;break}m=0,r.next=38;break;case 19:if("backup"!==b){r.next=37;break}if(l((0,f.m)(_.sP,"backup")),l((0,P.v_)()),!(0,u.do)(p())){r.next=27;break}return l((0,f.m)(_.f7,"migrate")),r.abrupt("return",l(D("migrate")));case 27:if("find_replace"!==g){r.next=32;break}return l((0,f.m)(_.f7,"find_replace")),r.abrupt("return",l(D("find_replace")));case 32:if("import"!==g){r.next=34;break}return r.abrupt("return",l((0,N.Z6)(N.uk)));case 34:m=0,r.next=38;break;case 37:return r.abrupt("return",Promise.resolve("success"));case 38:return O=0,m===Z.length-1&&(O=1),R=(0,u.do)(p())?1:0,L=w(b,g),l((0,f.m)(d.V$,(0,E.gB)("<span>%s</span>&nbsp;<b>%s</b>",L,I(Z[m],g)))),M={table:Z[m],stage:(0,u.r5)("current_stage",p()),form_data:JSON.stringify(S),current_row:t,last_table:O,primary_keys:n,gzip:R,nonce:window.wpmdb_data.nonces.migrate_table,action:"wpmdb_migrate_table"},B=performance.now(),r.prev=45,r.next=48,(0,c.oj)(M);case 48:F=r.sent,r.next=56;break;case 51:return r.prev=51,r.t0=r.catch(45),console.error(r.t0),l((0,T.m)({error_type:d.gF,error_message:r.t0})),r.abrupt("return",!1);case 56:if(F.success){r.next=59;break}return l((0,T.m)({error_type:d.gF,error_message:F.data})),r.abrupt("return",!1);case 59:return(j=F.data.replace_data?JSON.parse(F.data.replace_data):null)&&l((0,A._s)({table:Z[m],data:j,time:performance.now()-B,executed:!0})),U=F.data,l(x(m,y,Z,g,U.current_row)),-1===parseInt(U.current_row)&&(m++,U.current_row="",U.primary_keys=""),1!==O||"savefile"!==g&&"backup_local"!==g||l(k(0,0,U)),z=[U.current_row,U.primary_keys,m],s.length&&z.push(s),r.next=69,l((0,N.Z6)("MIGRATE",[{fn:e,args:z}],s));case 69:return r.abrupt("return",r.sent);case 70:case"end":return r.stop()}}),a,null,[[45,51]])})));return function(e,t){return l.apply(this,arguments)}}()},I=function(e,t){return"import"===t?e.replace(/_mig_/,""):e},D=function(e){return function(){var t=(0,a.Z)((0,r.Z)().mark((function t(n,a){return(0,r.Z)().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.abrupt("return",n((0,N.Z6)("MIGRATE",[{fn:R,args:[-1,"",0]}],e)));case 1:case"end":return t.stop()}}),t)})));return function(e,n){return t.apply(this,arguments)}}()},L=function(e,t){return new Promise((function(n,r){window.setTimeout((function(){return n(e())}),t)}))}},68424:function(e,t,n){"use strict";n.d(t,{A_:function(){return f},Ax:function(){return m},Rp:function(){return _},kO:function(){return d}});var r=n(27166),a=n(33032),i=n(42233),s=n(66055),c=n(666),l=n(29942),o=n(47895),u={message:(0,i.gB)((0,i.__)('<a href="%s" target="_blank" rel="noopener noreferrer">Having trouble connecting to the REST API</a>, please ensure that it has not been disabled or altered.',"wp-migrate-db"),"https://deliciousbrains.com/wp-migrate-db-pro/doc/rest-api-errors"),id:"wpmdb_rest_inactive"};function p(e){return function(){var t=(0,a.Z)((0,r.Z)().mark((function t(n){return(0,r.Z)().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.abrupt("return",n((0,s.m)(c.Kf,{key:e})));case 1:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}()}function m(){return function(){var e=(0,a.Z)((0,r.Z)().mark((function e(t){return(0,r.Z)().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:t((0,s.m)(c.Zz,"licence_expired")),t((0,s.m)(c.Zz,"not_activated")),t((0,s.m)(c.Zz,"activation_deactivated")),t((0,s.m)(c.Zz,"wpmdb_invalid_license"));case 4:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}()}function d(){return function(){var e=(0,a.Z)((0,r.Z)().mark((function e(t){return(0,r.Z)().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:t((0,s.m)(c.e2,"licence_expired")),t((0,s.m)(c.e2,"not_activated")),t((0,s.m)(c.e2,"activation_deactivated")),t((0,s.m)(c.e2,"wpmdb_invalid_license"));case 4:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}()}function f(){return function(e){var t,n;e((t="wpmd_rest_inactive",n=u,function(){var e=(0,a.Z)((0,r.Z)().mark((function e(a){return(0,r.Z)().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",a((0,s.m)(c.Dv,{key:t,value:n})));case 1:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}())),e((0,s.m)(c.e2,"wpmdb_rest_inactive"))}}function _(e,t,n){return function(){var i=(0,a.Z)((0,r.Z)().mark((function a(i,s){return(0,r.Z)().wrap((function(r){for(;;)switch(r.prev=r.next){case 0:i((0,o.Tf)(e,!0,c.k0)),i((0,o.q$)(e,c.$V)),(0,l.op)("/process-notice-link",{notice:e,type:t,reminder:n}).then((function(e){e.success})).catch((function(e){console.error(e)})),i(p(e));case 4:case"end":return r.stop()}}),a)})));return function(e,t){return i.apply(this,arguments)}}()}},22633:function(e,t,n){"use strict";n.d(t,{$f:function(){return T},$z:function(){return _},G7:function(){return h},H5:function(){return b},I4:function(){return y},LX:function(){return w},OW:function(){return k},SO:function(){return v},WJ:function(){return S},Xl:function(){return E},er:function(){return x},ii:function(){return g},nW:function(){return P},qb:function(){return N},rC:function(){return Z}});var r=n(31125),a=n(10734),i=n.n(a),s=(n(49736),n(66055)),c=n(41459),l=n(14251),o=n(4669),u=n(76178),p=n(29950),m=n(22497),d=n(74094),f=n(34653),_=["tables","backups","post_types","advanced_options","standard_fields","custom_fields"];function g(e){return function(t){t({type:c.fR,payload:e})}}function b(e){return{type:c.h6,payload:e}}function h(e){return function(t){var n=(0,r.Z)(e);t({type:c.ig,payload:n})}}function v(e){return function(t,n){if("database"!==e)return t({type:c.kW,payload:e})}}function E(){return function(e,t){var n=(0,u.O)("panelsOpen",t()),a=(0,u.O)("registeredPanels",t()),i=(0,r.Z)(n);return i=n.some((function(e){return _.includes(e)&&a.includes(e)}))?i.filter((function(e){return!_.includes(e)})):[].concat((0,r.Z)(i),_).filter((function(e){return a.includes(e)})),e((0,s.m)(c.ig,i)),!1}}function w(e){return function(t,n){t({type:c.kW,payload:e})}}function y(e){return(0,s.m)(c.q2,e)}function x(e){return function(t,n){if("database"!==e)return t((0,s.m)(c._H,[e]));var r=(0,u.O)("panelsOpen",n()).filter((function(e){return _.includes(e)||"database"===e}));t((0,s.m)(c._H,r))}}function k(e){return(0,s.m)(c.pE,e)}function Z(e,t){return function(n,a){var i=null;"undefined"!==typeof t&&(i=(0,r.Z)(t)),n({type:c.Nv,payload:e,panelPayload:i})}}var N=function(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=arguments.length>2&&void 0!==arguments[2]&&arguments[2];return function(a,s){var c=s().panels.panelsToDisplay,l=(0,r.Z)(c);-1===i()(l,e)&&l.push(e),a(Z(l)),t&&n&&a(b({parent:t,title:n}))}},P=function(e){return function(t,n){var a=n().panels.panelsToDisplay,s=(0,r.Z)(a);e.forEach((function(e){-1===i()(s,e)&&s.push(e)})),t(Z(s))}},S=function(e){return function(t,n){var a=n().panels.panelsToDisplay,s=(0,r.Z)(a),c=i()(s,e);-1!==c&&(s.splice(c,1),t(Z(s)))}};function T(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return function(n,r){var a=r(),i=n((0,d.J)());t||n((0,l.oA)());var c=e.panel,u=e.intent;if(t||n((0,s.m)(p.F3,function(e){switch(e){case"savefile":return["gzip_file","replace_guids","exclude_transients"];case"find_replace":case"push":case"pull":return["replace_guids","exclude_transients"];case"backup_local":return["exclude_spam","exclude_transients","gzip_file"]}return[]}(u))),t||!i&&u!==a.migrations.current_migration.intent){n((0,m.Kw)("addonActions"));var _=n((0,m.O)("addonPanels",[],u)),g=n((0,m.O)("addonPanelsOpen",[c,"custom_fields"],u));_.push(c);"savefile"===u&&["media_files","theme_plugin_files"].forEach((function(e){return _.push(e)})),n(Z(_,g));["savefile","backup_local","find_replace"].includes(u)&&n((0,l.I8)(!0)),t||(n((0,l.CV)({intent:u})),n((0,o.EX)(u))),["savefile","find_replace"].includes(u)&&n((0,f.addonsLoaded)())}}}},52650:function(e,t,n){"use strict";n.d(t,{a:function(){return _},b:function(){return f}});var r=n(31125),a=n(89472),i=n(88368),s=n(96480),c=n.n(s),l=n(4516),o=n(76178),u=n(41459),p=n(14251),m=n(22497),d=n(29942);function f(){return function(e,t){e((0,p.Tf)("")),e((0,p.j8)(c()()));var n=(0,l.Jm)("custom_search_replace",t()),s=(0,l._P)("this_url",t()),f=(0,l._P)("this_path",t()),_=(0,l.r5)(["post_types_option","post_types_selected","backup_option","backup_tables_selected","tables_option","tables_selected","databaseEnabled","intent"],t()),g=(0,i.Z)(_,8),b=g[0],h=g[1],v=g[2],E=g[3],w=g[4],y=g[5],x=g[6],k=g[7],Z=t(),N=Z.media_files,P=Z.theme_plugin_files,S=Z.migrations,T=(0,o.O)("panelsOpen",t()),O=(0,l.r5)("status",t())||[];if(""===O&&(O=[]),x){if(n.length){var A,C=(0,a.Z)(n);try{for(C.s();!(A=C.n()).done;){var R=A.value;if(""===R.replace_old&&""!==R.replace_new||[(0,d.Ph)(s),f].includes(R.replace_old)&&""===R.replace_new){O.push({name:"COMMON_SEARCH_REPLACE_EMPTY",panel:"custom_fields"});break}if(R.regex&&!R.isValidRegex){O.push({name:"COMMON_SEARCH_REPLACE_INVALID_REGEX",panel:"custom_fields"});break}}}catch(L){C.e(L)}finally{C.f()}}"selected"===b&&0===h.length&&O.push({name:"POST_TYPES_SELECTED_EMPTY",panel:"post_types"}),"backup_manual_select"===v&&0===E.length&&O.push({name:"BACKUP_TABLES_SELECTED_EMPTY",panel:"backups"}),"selected"===w&&0===y.length&&O.push({name:"TABLES_SELECTED_EMPTY",panel:"tables"})}var I=[];if(["push","pull","savefile"].includes(k)&&(N&&!0===N.enabled&&I.push("media"),P)){var D=function(e,t){var n=e.other_files,r=e.plugin_files,a=e.muplugin_files,i=e.theme_files,s=e.core_files,c=t.others,l=t.plugins,o=t.muplugins,u=t.themes,p=t.core,m=[];n&&!0===n.enabled&&Object.keys(c).length>0&&m.push("others");r&&!0===r.enabled&&Object.keys(l).length>0&&m.push("plugins");a&&!0===a.enabled&&Object.keys(o).length>0&&m.push("muplugins");i&&!0===i.enabled&&Object.keys(u).length>0&&m.push("themes");s&&!0===s.enabled&&Object.keys(p).length>0&&m.push("core");return m}(P,"pull"===k?S.remote_site.site_details:S.local_site.site_details);I.push.apply(I,(0,r.Z)(D))}return!0===x&&I.push("database"),0===I.length&&O.push({name:"EMPTY_MIGRATION_STAGES",panel:"submit"}),!((O=e((0,m.O)("wpmdbPreMigrationCheck",O))).length>0)||(e((0,p.Tf)(O)),O.forEach((function(t){T.includes(t.panel)||e({type:u.kW,payload:t.panel})})),!1)}}function _(e,t){if(!e||"object"!==typeof e||!e.length)return!1;var n=!1;return e.forEach((function(e){e.name===t&&(n=!0)})),n}},61358:function(e,t,n){"use strict";n.d(t,{qU:function(){return A},hV:function(){return F},UF:function(){return L},uJ:function(){return R},Vu:function(){return j},NF:function(){return I},qH:function(){return B},F9:function(){return U},zD:function(){return O},Tv:function(){return D}});var r,a=n(27166),i=n(33032),s=n(18489),c=n(4665),l=n(50029),o=n.n(l),u=n(70659),p=n.n(u),m=n(27114),d=n(29942),f=n(66055),_=n(22973),g=n(29950),b=n(17186),h=n(30348);function v(){return v=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},v.apply(this,arguments)}n.p;var E,w=(0,h.ZP)((function(e){return c.createElement("svg",v({style:{animationFillMode:"forwards",animationIterationCount:1},xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",id:"el_0iWebJDPz"},e),r||(r=c.createElement("style",null,"@keyframes kf_el_mS-6SGLslI_an_V1DkmYpQ2{0%,50%{opacity:0}53.33%,to{opacity:1}}@keyframes kf_el_flrbQh1w8k_an_9eUz96drwv{0%,to{stroke-dasharray:59.7}}@keyframes kf_el_flrbQh1w8k_an_C4kYjD6bN{0%{stroke-dashoffset:59.7}50%,to{stroke-dashoffset:0}}@keyframes kf_el_mS-6SGLslI_an_bM1FLjjf73{0%,50%,to{stroke-dasharray:11.74}}@keyframes kf_el_mS-6SGLslI_an_fdLET0VVs{0%,50%{stroke-dashoffset:11.74}to{stroke-dashoffset:0}}#el_0iWebJDPz *{-webkit-animation-duration:1s;animation-duration:1s;-webkit-animation-iteration-count:1;animation-iteration-count:1;-webkit-animation-timing-function:cubic-bezier(0,0,1,1);animation-timing-function:cubic-bezier(0,0,1,1)}")),c.createElement("g",{fillRule:"evenodd",style:{stroke:"#fff",fill:"none",WebkitTransform:"translate(2px,2px)",transform:"translate(2px,2px)"}},c.createElement("path",{style:{strokeWidth:2,WebkitAnimationFillMode:"forwards,forwards,forwards",animationFillMode:"forwards,forwards,forwards",strokeDashoffset:11.74,WebkitAnimationName:"kf_el_mS-6SGLslI_an_fdLET0VVs,kf_el_mS-6SGLslI_an_bM1FLjjf73,kf_el_mS-6SGLslI_an_V1DkmYpQ2",animationName:"kf_el_mS-6SGLslI_an_fdLET0VVs,kf_el_mS-6SGLslI_an_bM1FLjjf73,kf_el_mS-6SGLslI_an_V1DkmYpQ2",WebkitAnimationTimingFunction:"cubic-bezier(0,0,1,1),cubic-bezier(0,0,1,1),cubic-bezier(0,0,1,1)",animationTimingFunction:"cubic-bezier(0,0,1,1),cubic-bezier(0,0,1,1),cubic-bezier(0,0,1,1)",strokeDasharray:11.74},d:"M6 10.5 9.2 13 14 7"}),c.createElement("circle",{cx:10,cy:10,r:9.5,style:{fillOpacity:0,WebkitAnimationFillMode:"forwards,forwards",animationFillMode:"forwards,forwards",strokeDashoffset:59.7,WebkitAnimationName:"kf_el_flrbQh1w8k_an_C4kYjD6bN,kf_el_flrbQh1w8k_an_9eUz96drwv",animationName:"kf_el_flrbQh1w8k_an_C4kYjD6bN,kf_el_flrbQh1w8k_an_9eUz96drwv",WebkitAnimationTimingFunction:"cubic-bezier(0,0,1,1),cubic-bezier(0,0,1,1)",animationTimingFunction:"cubic-bezier(0,0,1,1),cubic-bezier(0,0,1,1)",strokeDasharray:59.7}})))}))(E||(E=(0,b.Z)(["\n  #el_aEQFk8pHYY {\n    stroke: #fff;\n  }\n  width: 30px;\n"]))),y=n(47585);function x(e){var t=e.migrations,n=t.current_migration,r=t.connection_info,a=t.search_replace,i=n;r=(0,d.Yu)()?(0,s.Z)((0,s.Z)({},r),{},{status:(0,s.Z)({},y.A)}):void 0;var c={current_migration:n=(0,s.Z)((0,s.Z)({},i),{},{intent:i.intent,status:"",current_stage:"",stages:[],selected_existing_profile:null,running:!1,migration_enabled:!1}),connection_info:r,search_replace:a,media_files:e.media_files,theme_plugin_files:e.theme_plugin_files,multisite_tools:e.multisite_tools};return JSON.stringify(c)}function k(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return function(n,r){var a=x(r());return{name:e,value:a,guid:o()(),fromRecent:t}}}function Z(e){return function(t,n){return t({type:_._n,payload:e}),n()}}function N(e,t){return function(){var n=(0,i.Z)((0,a.Z)().mark((function n(r,i){return(0,a.Z)().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:return r((0,f.m)(_.Cp,"Save")),r((a={data:{location:e,message:(0,m.Y)(t)}},(0,f.m)(_.Gg,a))),n.abrupt("return",!1);case 3:case"end":return n.stop()}var a}),n)})));return function(e,t){return n.apply(this,arguments)}}()}function P(e){return(0,f.m)(_.qk,e)}function S(){return(0,f.m)(_.a)}function T(){return(0,f.m)(_.aC)}function O(e){return(0,f.m)(g.NS,{id:e,type:"saved"})}function A(e){return function(){var t=(0,i.Z)((0,a.Z)().mark((function t(n,r){var i,s,c;return(0,a.Z)().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return i=n(k(e.name)),n(S()),t.prev=2,t.next=5,(0,d.op)("/save-profile",i,!1,n);case 5:s=t.sent,t.next=11;break;case 8:return t.prev=8,t.t0=t.catch(2),t.abrupt("return",n(N("migration",t.t0)));case 11:if(s.success){t.next=13;break}return t.abrupt("return",n(N("migration",s)));case 13:return s.success&&n((0,f.m)(_.Cp,"success")),n(Z({name:e.name,guid:i.guid,id:s.data.id})),c={profile_saved:!0},n((0,f.m)(g.eZ,c)),n({type:_.bQ,payload:{id:s.data.id,type:"saved"}}),t.abrupt("return",s);case 19:case"end":return t.stop()}}),t,null,[[2,8]])})));return function(e,n){return t.apply(this,arguments)}}()}function C(e,t){return function(){var n=(0,i.Z)((0,a.Z)().mark((function n(r,i){var s;return(0,a.Z)().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:return r(S()),n.prev=1,n.next=4,(0,d.op)("/save-profile",t,!1,r);case 4:s=n.sent,n.next=11;break;case 7:return n.prev=7,n.t0=n.catch(1),console.error(n.t0),n.abrupt("return",r(N("profile",n.t0)));case 11:if(s.success){n.next=13;break}return n.abrupt("return",r(N("profile",s)));case 13:return r(Z({name:e.name,guid:t.guid,id:s.data.id})),r((0,f.m)(_.Cp,"Save")),n.abrupt("return",s);case 16:case"end":return n.stop()}}),n,null,[[1,7]])})));return function(e,t){return n.apply(this,arguments)}}()}function R(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];return function(){var t=(0,i.Z)((0,a.Z)().mark((function t(n,r){var i,s,c,l,o;return(0,a.Z)().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(i=r(),s=i.migrations.current_migration,c=s.profile_name,null===s.profile_type){t.next=4;break}return t.abrupt("return",null);case 4:return(l=n(k(c))).id=e,n(T()),t.prev=7,t.next=10,(0,d.op)("/unsaved-profile",l,!1,n);case 10:o=t.sent,t.next=16;break;case 13:return t.prev=13,t.t0=t.catch(7),t.abrupt("return",n(N("migration",t.t0)));case 16:if(o.success){t.next=18;break}return t.abrupt("return",n(N("migration",o)));case 18:if("not saved"!==o.data){t.next=20;break}return t.abrupt("return",o);case 20:return n({type:_.YQ,payload:o.data}),t.abrupt("return",o);case 22:case"end":return t.stop()}}),t,null,[[7,13]])})));return function(e,n){return t.apply(this,arguments)}}()}var I=function(e,t,n){return function(){var r=(0,i.Z)((0,a.Z)().mark((function r(i,s){var c;return(0,a.Z)().wrap((function(r){for(;;)switch(r.prev=r.next){case 0:return i(T()),r.next=3,(0,d.op)("/remove-recent-migration",{id:e});case 3:if((c=r.sent).success){r.next=6;break}return r.abrupt("return",i(N("profile",c)));case 6:i({type:_.N2,payload:{index:t,slice:n}});case 7:case"end":return r.stop()}}),r)})));return function(e,t){return r.apply(this,arguments)}}()};function D(e){return{type:_.xv,payload:e}}function L(e,t){return function(){var n=(0,i.Z)((0,a.Z)().mark((function n(r,i){var c,l,o,u;return(0,a.Z)().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:return c=i(),l=c.profiles.recent,o=p()(l,{id:e}),u=(0,s.Z)((0,s.Z)({},o),{},{fromRecent:"true"}),n.next=6,r(C({name:o.name},u));case 6:n.sent.success&&(r(I(e,t,"recent")),r(D(c.profiles.saved.length)));case 8:case"end":return n.stop()}}),n)})));return function(e,t){return n.apply(this,arguments)}}()}function M(e,t){return function(){var n=(0,i.Z)((0,a.Z)().mark((function n(r){return(0,a.Z)().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:r({type:_.hm,payload:{index:e,text:t}});case 1:case"end":return n.stop()}}),n)})));return function(e){return n.apply(this,arguments)}}()}function F(e,t,n){return function(){var r=(0,i.Z)((0,a.Z)().mark((function r(i){var s;return(0,a.Z)().wrap((function(r){for(;;)switch(r.prev=r.next){case 0:return i((0,f.m)(_.tT)),r.prev=1,r.next=4,(0,d.op)("/rename-profile",{guid:e,name:n});case 4:s=r.sent,r.next=10;break;case 7:return r.prev=7,r.t0=r.catch(1),r.abrupt("return",i(N("profile",r.t0)));case 10:if(s.success){r.next=12;break}return r.abrupt("return",i(N("profile",s)));case 12:i(M(t,n)),i(D(t));case 14:case"end":return r.stop()}}),r,null,[[1,7]])})));return function(e){return r.apply(this,arguments)}}()}var B=function(e,t,n){return function(){var r=(0,i.Z)((0,a.Z)().mark((function r(i){var s;return(0,a.Z)().wrap((function(r){for(;;)switch(r.prev=r.next){case 0:return i((0,f.m)(_.PM)),r.prev=1,r.next=4,(0,d.op)("/remove-profile",{guid:e,name:n});case 4:s=r.sent,r.next=10;break;case 7:return r.prev=7,r.t0=r.catch(1),r.abrupt("return",i(N("profile",r.t0)));case 10:if(s.success){r.next=12;break}return r.abrupt("return",i(N("profile",s)));case 12:return i({type:_.N2,payload:{index:t,slice:"saved"}}),r.abrupt("return",!0);case 14:case"end":return r.stop()}}),r,null,[[1,7]])})));return function(e){return r.apply(this,arguments)}}()};function j(e,t){return function(){var n=(0,i.Z)((0,a.Z)().mark((function n(r,i){var s,c,l,o;return(0,a.Z)().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:return s=i(),c=x(s),r(P(!0)),l={contents:c,guid:e},n.prev=4,n.next=7,(0,d.op)("/overwrite-profile",l);case 7:o=n.sent,n.next=13;break;case 10:return n.prev=10,n.t0=n.catch(4),n.abrupt("return",r(N("migration",n.t0)));case 13:if(o.success){n.next=15;break}return n.abrupt("return",r(N("migration",o)));case 15:r(P(!1)),o.success&&r((0,f.m)(_.Cp,"success")),r((0,f.m)(g.eZ,{profile_saved:!0})),r({type:_.Qu,payload:t});case 19:case"end":return n.stop()}}),n,null,[[4,10]])})));return function(e,t){return n.apply(this,arguments)}}()}var U=function(e,t){return function(n){var r=e.btn_text;return"success"===r?(setTimeout((function(){var e;n((e="Save",function(){var t=(0,i.Z)((0,a.Z)().mark((function t(n,r){return(0,a.Z)().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:n((0,f.m)(_.Cp,e));case 1:case"end":return t.stop()}}),t)})));return function(e,n){return t.apply(this,arguments)}}()))}),2e3),c.createElement(w,{className:"success",onAnimationEnd:function(){t(!1)}})):r}}},4669:function(e,t,n){"use strict";n.d(t,{CN:function(){return c},EX:function(){return m},FL:function(){return u},GJ:function(){return o},Kf:function(){return i},Px:function(){return p},mX:function(){return a},rL:function(){return s},zf:function(){return l}});var r=n(66441);function a(e){return function(t){t({type:r.oK,payload:e})}}function i(e){return function(t){t({type:r.G3,payload:e})}}function s(e){return function(t,n){t({type:r.O9,payload:e})}}function c(e){return function(t,n){t({type:r.ci,payload:e})}}function l(e){return{type:r.IY,payload:e}}function o(e){return{type:r.Q0,payload:e}}function u(e){return{type:r.hx,payload:e}}function p(e){return{type:r.zi,index:e}}function m(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return function(n,a){var i=a(),s={intent:e,local_site:i.migrations.local_site,force_update:t};n({type:r.KM,payload:s})}}},87326:function(e,t,n){"use strict";n.d(t,{Lc:function(){return w},NV:function(){return g},UJ:function(){return x},Xn:function(){return E},c9:function(){return _},m7:function(){return b},nE:function(){return h},rT:function(){return f},vo:function(){return y}});var r=n(27166),a=n(33032),i=n(62295),s=n(29942),c=n(19826),l=n(66055),o=n(27114),u=n(666),p=n(47895);function m(e,t){return function(n,r){return n((0,l.m)(c.TE,{location:e,message:(0,o.Y)(t)})),!1}}function d(e){return function(t){t((0,l.m)(c.Ft,e))}}function f(e){return function(t,n){t((0,p.q$)(e,c.eH))}}function _(e,t){return function(){var n=(0,a.Z)((0,r.Z)().mark((function n(a,i){return(0,r.Z)().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:a((0,p.Tf)(e,t,c.YQ));case 1:case"end":return n.stop()}}),n)})));return function(e,t){return n.apply(this,arguments)}}()}function g(){return function(){var e=(0,a.Z)((0,r.Z)().mark((function e(t){var n;return(0,r.Z)().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t(_("reset_api_key",!0)),t(f("reset_api_key")),e.next=4,(0,s.op)("/reset-api-key",{});case 4:if((n=e.sent).success){e.next=10;break}return t(m("reset_api_key",n)),t(_("reset_api_key","errored")),setTimeout((function(){t(_("reset_api_key",!1))}),1500),e.abrupt("return");case 10:t(_("reset_api_key",!1)),t(d(n.data));case 12:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}()}function b(e,t){return function(n){n((0,l.m)(c.km,{setting:e,value:t}))}}function h(e,t){return function(){var n=(0,a.Z)((0,r.Z)().mark((function n(a){return(0,r.Z)().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:a(m(e,t)),a(_(e,"errored"));case 2:case"end":return n.stop()}}),n)})));return function(e){return n.apply(this,arguments)}}()}function v(e,t,i,c){return function(){var o=(0,a.Z)((0,r.Z)().mark((function a(o){var p;return(0,r.Z)().wrap((function(r){for(;;)switch(r.prev=r.next){case 0:return o(_(t,!0)),o(f(t)),r.prev=2,r.next=5,i();case 5:p=r.sent,r.next=11;break;case 8:return r.prev=8,r.t0=r.catch(2),r.abrupt("return",o(h(t,r.t0)));case 11:if(p.success){r.next=13;break}return r.abrupt("return",o(h(t,p)));case 13:if(o(_(t,"success")),o(b(t,e)),!c){r.next=24;break}r.t1=c,r.next="beta"===r.t1?19:"allow_tracking"===r.t1?22:24;break;case 19:if(!(0,s.Yu)()){r.next=21;break}return r.abrupt("return",Promise.resolve().then(n.bind(n,40882)).then((function(e){var t=e.betaOptionToggle;o(t())})));case 21:return r.abrupt("break",24);case 22:return o((0,l.m)(u.Kf,{key:"notice-enable-usage-tracking"})),r.abrupt("break",24);case 24:setTimeout((function(){o(_(t,!1))}),1500);case 25:case"end":return r.stop()}}),a,null,[[2,8]])})));return function(e){return o.apply(this,arguments)}}()}function E(e,t,n){return function(){var i=(0,a.Z)((0,r.Z)().mark((function i(c){return(0,r.Z)().wrap((function(i){for(;;)switch(i.prev=i.next){case 0:return i.abrupt("return",c(v(t,e,(0,a.Z)((0,r.Z)().mark((function n(){return(0,r.Z)().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:return n.abrupt("return",(0,s.op)("/save-setting",{setting:e,checked:t}));case 1:case"end":return n.stop()}}),n)}))),n)));case 1:case"end":return i.stop()}}),i)})));return function(e){return i.apply(this,arguments)}}()}function w(e){return function(){var t=(0,a.Z)((0,r.Z)().mark((function t(n){var i;return(0,r.Z)().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:n(v(i=1024*e*1024,"max_request",(0,a.Z)((0,r.Z)().mark((function e(){return(0,r.Z)().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,s.op)("/update-max-request",{max_request_size:i}));case 1:case"end":return e.stop()}}),e)})))));case 2:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}()}function y(e){return function(){var t=(0,a.Z)((0,r.Z)().mark((function t(n){return(0,r.Z)().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:n(v(e,"delay_between_requests",(0,a.Z)((0,r.Z)().mark((function t(){return(0,r.Z)().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.abrupt("return",(0,s.op)("/update-delay-between-requests",{delay_between_requests:e}));case 1:case"end":return t.stop()}}),t)})))));case 1:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}()}function x(){return function(){var e=(0,a.Z)((0,r.Z)().mark((function e(t){var n;return(0,r.Z)().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t(_("disable_ssl",!0)),e.next=3,(0,s.op)("/disable-ssl",{});case 3:if((n=e.sent).success){e.next=7;break}return(0,i.dC)((function(){t(m("disable_ssl",n)),t(_("disable_ssl","errored"))})),e.abrupt("return");case 7:return(0,i.dC)((function(){t(_("disable_ssl","success")),t(b("licence",""))})),setTimeout((function(){(0,i.dC)((function(){t(f("disable_ssl")),t(_("disable_ssl",!1))}))}),1500),e.abrupt("return",n);case 10:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}()}},47585:function(e,t,n){"use strict";n.d(t,{A:function(){return r}});var r={auth_form:{username:"",password:""},show_auth_form:!1,connecting:!1,error:!1,error_msg:"",button_status:"disabled",ssl_notice:!1,pasted:!1,copy_to_remote:!1,prefix_mismatch:!1,mixed_case_table_name_warning:!1,show_mst_warning:!1,update_plugin_on_remote:!1,retry_over_http:!1}},29950:function(e,t,n){"use strict";n.d(t,{AV:function(){return U},Ac:function(){return Z},EF:function(){return P},F3:function(){return S},GG:function(){return M},IJ:function(){return z},KU:function(){return g},Ld:function(){return T},NL:function(){return W},NS:function(){return O},Nt:function(){return w},O$:function(){return h},S_:function(){return J},Ss:function(){return x},Um:function(){return A},W$:function(){return j},X4:function(){return H},Yl:function(){return v},Zz:function(){return q},av:function(){return y},b4:function(){return G},b_:function(){return B},d0:function(){return I},eZ:function(){return f},f7:function(){return R},ic:function(){return E},jD:function(){return L},mO:function(){return k},mu:function(){return b},n_:function(){return D},oK:function(){return C},sP:function(){return F},sr:function(){return V},tX:function(){return _},u2:function(){return N}});var r=n(31125),a=n(36222),i=n(18489),s=n(58319),c=n.n(s),l=n(33351),o=n.n(l),u=n(26429),p=n(29942),m=n(22973),d=n(66866),f="REPLACE_CURRENT_MIGRATION",_="SET_MIGRATION_CONNECTED",g="UPDATE_SELECTED_TABLES",b="UPDATE_TABLES_OPTION",h="UPDATE_BACKUPS_TABLES",v="UPDATE_BACKUPS_OPTION",E="UPDATE_POSTTYPES_SELECTED",w="UPDATE_POSTTYPES_OPTION",y="UPDATE_CURRENT_MIGRATION",x="UPDATE_ADVANCED_OPTIONS",k="UPDATE_SOURCE_PREFIX",Z="UPDATE_DESTINATION_PREFIX",N="UPDATE_TWO_MULTISITES",P="UPDATE_LOCAL_SOURCE",S="REPLACE_ADVANCED_OPTIONS",T="LOAD_PROFILE",O="SET_EXISTING_PROFILE",A="SET_MIGRATION_PREVIEW",C="SET_MIGRATION_STAGES",R="SET_CURRENT_STAGE",I="SET_STATUS",D="SET_MIGRATION_ENABLED",L="SET_MIGRATION_RUNNING",M="SET_STAGES_COMPLETE",F="SET_STAGE_COMPLETE",B="SET_MIGRATION_ID",j="SET_HIGH_PERFORMANCE_TRANSFERS_STATUS",U="TOGGLE_DATABASE_PANEL",z="SET_CURRENT_PAYLOAD_SIZE",G="INCREMENT_FILE_TRANSFER_COUNT",V="ADD_PAYLOAD_SIZE_HISTORY",H="SET_CURRENT_MAX_PAYLOAD_SIZE",W="ADD_HIGH_PERFORMANCE_TRANSFER_STAT",K={connected:!1,intent:"",tables_option:"all",tables_selected:[],backup_option:"none",backup_tables_selected:[],post_types_option:"all",post_types_selected:[],advanced_options_selected:[],profile_name:"",selected_existing_profile:null,profile_type:null,status:{disabled:!1},stages:[],current_stage:"",stages_complete:[],running:!1,migration_enabled:!1,migration_id:null,source_prefix:"",destination_prefix:"",preview:!1,selectedComboOption:"preview",twoMultisites:!1,localSource:!0,databaseEnabled:!0,currentPayloadSize:0,currentMaxPayloadSize:null,fileTransferRequests:0,payloadSizeHistory:[],fileTransferStats:[],forceHighPerformanceTransfers:!0,fseDumpFilename:null},q=["replace_guids","exclude_spam","exclude_transients","keep_active_plugins","compatibility_older_mysql","gzip_file"];function Y(e,t,n){return(0,i.Z)((0,i.Z)({},e),{},(0,a.Z)({},t,n))}var J=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:K,t=arguments.length>1?arguments[1]:void 0,n=(0,i.Z)({},e);return(0,u.ZP)(e,(function(a){var s;switch(t.type){case"RESET_APP":return K;case"RESET_MIGRATION":return(0,i.Z)((0,i.Z)({},K),{},{intent:e.intent});case f:return c()(a,t.payload);case y:return a.intent=t.payload.intent,a;case A:return a.preview=t.payload,a;case m.Qu:return a.profile_name=t.payload,a;case _:return a.connected=t.connected,a;case g:var l=(0,i.Z)((0,i.Z)({},e),{},{tables_selected:t.payload});return"migration"===e.backup_option&&o()(l,{backup_tables_selected:t.payload}),l;case k:return Y(e,"source_prefix",t.payload);case Z:return Y(e,"destination_prefix",t.payload);case N:return Y(e,"twoMultisites",t.payload);case P:return Y(e,"localSource",t.payload);case b:return Y(e,"tables_option",t.payload);case h:return Y(e,"backup_tables_selected",t.payload);case v:return Y(e,"backup_option",t.payload);case E:return Y(e,"post_types_selected",t.payload);case w:return Y(e,"post_types_option",t.payload);case B:return Y(e,"migration_id",t.payload);case x:return n=(0,p.XV)(e.advanced_options_selected,t.payload),(0,i.Z)((0,i.Z)({},e),{},{advanced_options_selected:n});case S:return(0,i.Z)((0,i.Z)({},e),{},{advanced_options_selected:t.payload});case T:var u=t.payload.profile.value.current_migration,q=u.intent;return(0,i.Z)((0,i.Z)({},u),{},{connected:!1,selected_existing_profile:t.payload.id,profile_type:t.payload.profile.value.profile_type,stages_complete:[],migration_enabled:!["push","pull"].includes(q),currentPayloadSize:0,fileTransferRequests:0,payloadSizeHistory:[]});case O:case m.bQ:return a.selected_existing_profile=t.payload.id,a.profile_type=t.payload.type,a;case"SET_MIGRATION_DISABLED":return(0,i.Z)((0,i.Z)({},a),{},{status:(0,i.Z)((0,i.Z)({},a.status),{},{disabled:t.payload})});case C:return(0,i.Z)((0,i.Z)({},a),{},{stages:t.payload});case R:return(0,i.Z)((0,i.Z)({},a),{},{current_stage:t.payload});case L:return(0,i.Z)((0,i.Z)({},a),{},{running:t.payload,preview:!1!==t.payload&&a.preview});case I:return(0,i.Z)((0,i.Z)({},a),{},{status:t.payload});case D:return(0,i.Z)((0,i.Z)({},a),{},{migration_enabled:t.payload});case"SET_CONNECTED":return(0,i.Z)((0,i.Z)({},a),{},{migration_enabled:!0});case F:return a.stages_complete.push(t.payload),a;case M:return(0,i.Z)((0,i.Z)({},a),{},{stages_complete:t.payload});case"MST_TOGGLE_ENABLED":return t.payload.enabled?a:Y(e,"tables_option","all");case"SET_SELECTED_MIGRATION_COMBO_OPTION":return(0,i.Z)((0,i.Z)({},a),{},{selectedComboOption:t.payload});case U:return(0,i.Z)((0,i.Z)({},a),{},{databaseEnabled:!a.databaseEnabled});case d.u9:return(0,i.Z)((0,i.Z)({},a),{},{currentPayloadSize:0,fileTransferRequests:0,payloadSizeHistory:[],fileTransferStats:[]});case z:return(0,i.Z)((0,i.Z)({},a),{},{currentPayloadSize:t.payload});case H:return(0,i.Z)((0,i.Z)({},a),{},{currentMaxPayloadSize:t.payload});case V:return(0,i.Z)((0,i.Z)({},a),{},{payloadSizeHistory:[].concat((0,r.Z)(a.payloadSizeHistory?a.payloadSizeHistory:[]),[null!==(s=t.payload)&&void 0!==s?s:0])});case G:return a.fileTransferRequests?(0,i.Z)((0,i.Z)({},a),{},{fileTransferRequests:a.fileTransferRequests+1}):(0,i.Z)((0,i.Z)({},a),{},{fileTransferRequests:1});case j:var J,Q,X=!1;if("push"===a.intent)X=null!==(J=t.payload.remote_site_mode)&&void 0!==J&&J;if("pull"===a.intent)X=null!==(Q=t.payload.local_site_mode)&&void 0!==Q&&Q;return(0,i.Z)((0,i.Z)({},a),{},{highPerformanceTransfersStatus:X});case W:return(0,i.Z)((0,i.Z)({},a),{},{fileTransferStats:[].concat((0,r.Z)(a.fileTransferStats?a.fileTransferStats:[]),[t.payload])});case"SET_FORCE_HIGH_PERFORMANCE_TRANSFERS":return(0,i.Z)((0,i.Z)({},a),{},{forceHighPerformanceTransfers:t.payload});case"SET_FSE_DUMP_FILENAME":return(0,i.Z)((0,i.Z)({},a),{},{fseDumpFilename:t.payload});default:return e}}))}},15265:function(e,t,n){"use strict";n.d(t,{DQ:function(){return s},_b:function(){return c},xN:function(){return o}});var r=n(31125),a=n(18489),i=n(66866),s="ADD_DRY_RUN_RESULT",c="SET_CURRENTLY_PREVIEWED_DRY_RUN_ITEM",l={results:[],currentPreviewItem:null},o=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:l,t=arguments.length>1?arguments[1]:void 0,n=(0,a.Z)({},e);switch(t.type){case s:var o=(0,r.Z)(n.results),u=o.findIndex((function(e){return e.table===t.payload.table}));if(-1!==u){var p=(0,a.Z)({},o[u]);p.data=p.data.concat(t.payload.data),p.count=p.data.length,p.time+=t.payload.time,p.executed=!0,o[u]=p}else o.push((0,a.Z)((0,a.Z)({},t.payload),{},{count:t.payload.data.length}));return(0,a.Z)((0,a.Z)({},n),{},{results:o});case c:return(0,a.Z)((0,a.Z)({},e),{},{currentPreviewItem:t.payload});case i.Jj:return"CANCEL_COMPLETE"===t.payload?(0,a.Z)({},l):(0,a.Z)({},e);default:return e}}},18066:function(e,t,n){"use strict";n.d(t,{TO:function(){return a},fK:function(){return i},l3:function(){return s}});var r=n(52089),a="ADD_ACTION",i="ADD_FILTER",s="REMOVE_FILTER",c=(0,r.Lq)({filters:[],actions:[]},{ADD_ACTION:function(e,t){return e.actions.push(t.payload),e},ADD_FILTER:function(e,t){return e.filters.push(t.payload),e},REMOVE_FILTER:function(e,t){var n=e.filters;return e.filters=n.filter((function(e){return e.name!==t.payload.name})),e}});t.ZP=c},66866:function(e,t,n){"use strict";n.d(t,{D$:function(){return O},DK:function(){return P},DL:function(){return T},Du:function(){return o},G:function(){return x},Jj:function(){return m},Lw:function(){return v},MG:function(){return h},QH:function(){return f},V$:function(){return l},Z:function(){return p},c4:function(){return E},cE:function(){return u},cJ:function(){return w},gF:function(){return s},hS:function(){return k},hg:function(){return g},mx:function(){return Z},ol:function(){return N},sr:function(){return y},tO:function(){return d},ti:function(){return S},u9:function(){return b},uj:function(){return _}});var r=n(18489),a=n(26429),i=n(29950),s="MIGRATION_ERROR_TYPE_FATAL",c={title:"",progress_message:"",progress:{},stages:[],migration_tables:[],status:"",progress_stage:"",total_stage_size:0,migration_size:0,stage_size:0,item_progress:{},timer:{on:!1,start:0,time:0,timer_instance:null},pause_before_finalize:!1,document_title:"",dump_info:{},allow_page_leave:!0,showDryRunResults:!1},l="SET_TITLE",o="SET_PROGRESS_MESSAGE",u="SET_MIGRATION_ERROR",p="SET_MIGRATION_TABLES",m="MIGRATION_STATUS",d="SET_STAGE_TOTAL_SIZE",f="SET_STAGE_SIZE",_="SET_ITEM_PROGRESS",g="RESET_STAGES",b="RESET_MIGRATION_SIZE",h="SET_PROGRESS_STAGE",v="RESET_PROGRESS_STAGE",E="SET_TIMER_ON",w="SET_TIMER_TIME",y="SET_TIMER_START",x="SET_TIMER",k="SET_TIMER_INSTANCE",Z="SET_PAUSE_BEFORE_FINALIZE",N="SET_DOCUMENT_TITLE",P="SET_DUMP_INFO",S="SET_ALLOW_PAGE_LEAVE",T="SET_SHOW_DRY_RUN_RESULTS",O=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:c,t=arguments.length>1?arguments[1]:void 0;return(0,a.ZP)(e,(function(n){switch(t.type){case"RESET_APP":return c;case i.jD:return!0===t.payload?(0,r.Z)((0,r.Z)({},n),{},{status:"",showDryRunResults:!1}):e;case l:return(0,r.Z)((0,r.Z)({},n),{},{title:t.payload});case o:return(0,r.Z)((0,r.Z)({},n),{},{progress_message:t.payload});case"SET_PROGRESS":return(0,r.Z)((0,r.Z)({},n),{},{progress:t.payload});case u:return(0,r.Z)((0,r.Z)({},n),{},{status:(0,r.Z)((0,r.Z)({},n.status),{},{error_type:t.payload.error_type,error_message:t.payload.error_message})});case p:return(0,r.Z)((0,r.Z)({},n),{},{migration_tables:t.payload});case h:return(0,r.Z)((0,r.Z)({},n),{},{progress_stage:t.payload});case"REMOVE_MIGRATION_TABLE":return n.migration_tables.splice(t.payload,1),n;case m:return(0,r.Z)((0,r.Z)({},n),{},{status:t.payload});case d:return(0,r.Z)((0,r.Z)({},n),{},{total_stage_size:t.payload});case f:return(0,r.Z)((0,r.Z)({},n),{},{stage_size:t.payload});case _:var a=(0,r.Z)({},n.item_progress),s=t.payload,O=s.item,A=s.progress;return a[O]=A,(0,r.Z)((0,r.Z)({},n),{},{item_progress:a});case v:return(0,r.Z)((0,r.Z)({},n),{},{item_progress:{},stage_size:0});case x:return(0,r.Z)((0,r.Z)({},n),{},{timer:t.payload});case k:return(0,r.Z)((0,r.Z)({},n),{},{timer:(0,r.Z)((0,r.Z)({},n.timer),{},{timer_instance:t.payload})});case E:return(0,r.Z)((0,r.Z)({},n),{},{timer:(0,r.Z)((0,r.Z)({},n.timer),{},{on:t.payload})});case w:return(0,r.Z)((0,r.Z)({},n),{},{timer:(0,r.Z)((0,r.Z)({},n.timer),{},{time:t.payload})});case y:return(0,r.Z)((0,r.Z)({},n),{},{timer:(0,r.Z)((0,r.Z)({},n.timer),{},{start:t.payload})});case b:return(0,r.Z)((0,r.Z)({},n),{},{migration_size:0});case g:return(0,r.Z)((0,r.Z)({},n),{},{total_stage_size:0,stage_size:0,item_progress:{}});case Z:return(0,r.Z)((0,r.Z)({},n),{},{pause_before_finalize:t.payload});case N:return(0,r.Z)((0,r.Z)({},n),{},{document_title:t.payload});case P:return(0,r.Z)((0,r.Z)({},n),{},{dump_info:t.payload});case i.sP:var C=t.payload,R=n.total_stage_size,I=n.migration_size;return["backup","upload"].includes(C)||(I+=R),(0,r.Z)((0,r.Z)({},n),{},{migration_size:I});case S:return(0,r.Z)((0,r.Z)({},n),{},{allow_page_leave:t.payload});case T:return(0,r.Z)((0,r.Z)({},n),{},{showDryRunResults:t.payload});default:return e}}))}},666:function(e,t,n){"use strict";n.d(t,{$V:function(){return u},Dv:function(){return c},Kf:function(){return l},Zz:function(){return p},e2:function(){return m},k0:function(){return o}});var r=n(36222),a=n(18489),i=n(26429),s={messages:(0,a.Z)({},window.wpmdb_notifications),status:{},errors:{},hidden:{}},c="ADD_NOTIFICATION",l="REMOVE_NOTIFICATION",o="SET_NOTIFICATION_STATUS",u="DELETE_NOTIFICATION_ERROR",p="HIDE_NOTIFICATION",m="SHOW_NOTIFICATION";t.ZP=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:s,t=arguments.length>1?arguments[1]:void 0;return(0,i.ZP)(e,(function(n){var i=t.payload;switch(t.type){case c:return(0,a.Z)((0,a.Z)({},n),{},{messages:(0,a.Z)((0,a.Z)({},n.messages),{},(0,r.Z)({},i.key,i.value))});case l:return delete n.messages[i.key],n;case o:return(0,a.Z)((0,a.Z)({},n),{},{status:(0,a.Z)((0,a.Z)({},n.status),{},(0,r.Z)({},t.payload.type,t.payload.status))});case"SET_NOTIFICATION_ERROR":return(0,a.Z)((0,a.Z)({},n),{},{errors:(0,a.Z)((0,a.Z)({},n.errors),{},(0,r.Z)({},t.payload.location,t.payload.message))});case u:return delete n.errors[t.payload],n;case p:return n.hidden[i]=i,n;case m:return delete n.hidden[i],n;default:return e}}))}},41459:function(e,t,n){"use strict";n.d(t,{Nv:function(){return u},_H:function(){return _},fR:function(){return b},h6:function(){return p},ig:function(){return m},kW:function(){return d},pE:function(){return g},q2:function(){return f}});var r=n(18489),a=n(26429),i=n(42233),s=n(2474),c=n.n(s),l=n(49736),o=n(29942),u="UPDATE_MIGRATION_PANELS",p="UPDATE_PANEL_TITLE",m="UPDATE_MIGRATION_PANELS_OPEN",d="TOGGLE_OPEN_PANEL",f="REMOVE_OPEN_PANEL",_="SET_PANEL_CLICKED",g="SET_PANEL_STATUS",b="REGISTER_PANEL",h={panelsToDisplay:[],panelsOpen:["action_buttons"],panelClicked:[],panelStatus:"",panelTitles:{action_buttons:(0,i.__)("Action","wp-migrate-db"),connect:(0,i.__)("Remote Site","wp-migrate-db"),database:(0,i.__)("Database","wp-migrate-db"),import:(0,i.__)("SQL File","wp-migrate-db"),save:(0,i.__)("Save Profile","wp-migrate-db")},panelSummaries:{},dbTitles:{tables:l.B.tables.all,backups:l.B.backups.none,post_types:l.B.post_types.all,custom_search_replace:""},registeredPanels:[]};t.ZP=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:h,t=arguments.length>1?arguments[1]:void 0;return(0,a.ZP)(e,(function(n){switch(t.type){case"RESET_APP":return;case"RESET_MIGRATION":var a=(0,r.Z)((0,r.Z)({},h),{},{panelsToDisplay:["action_buttons","connect"]});return t.payload&&(a.panelsOpen=t.payload),a;case u:return n.panelsToDisplay=t.payload,t.panelPayload&&(n.panelsOpen=t.panelPayload),n;case p:var s=t.payload,l=s.title,b=s.parent;return n.panelTitles[b]=l,n;case m:return n.panelsOpen=t.payload,n;case d:return n.panelsOpen=(0,o.XV)(e.panelsOpen,t.payload),n;case f:var v=c()(n.panelsOpen,(function(e,n){return t.payload!==e}));return(0,r.Z)((0,r.Z)({},n),{},{panelsOpen:v});case _:return n.panelClicked=t.payload,n;case g:return n.panelStatus=t.payload,n;case"UPDATE_DB_PANEL_TITLE":return n=function(e,t,n){e.dbTitles[n.payload.key]=n.payload.title;var r=Object.values(e.dbTitles).filter((function(e){return e})).join(", ");return t=(0,i.__)("Database","wp-migrate-db")+": ".concat(r),e.panelTitles.database=t,e}(n,l,t),n;case"MST_TOGGLE_ENABLED":return!t.payload&&n.panelsOpen.includes("tables")&&n.panelsOpen.splice(n.panelsOpen.indexOf("tables"),1),n;case"REGISTER_PANEL":return n.registeredPanels.includes(t.payload)||n.registeredPanels.push(t.payload),n;default:return e}}))}},22973:function(e,t,n){"use strict";n.d(t,{$Q:function(){return w},Cp:function(){return E},Gg:function(){return h},KG:function(){return x},N2:function(){return o},PM:function(){return b},Qu:function(){return p},YQ:function(){return l},_n:function(){return c},a:function(){return d},aC:function(){return f},bQ:function(){return v},hm:function(){return u},jJ:function(){return y},qk:function(){return g},tT:function(){return _},xv:function(){return m}});var r=n(18489),a=n(42233),i=n(52089),s=n(29942),c="ADD_PROFILE",l="ADD_RECENT_MIGRATION",o="REMOVE_PROFILE",u="RENAME_PROFILE",p="SAVE_PROFILE_NAME",m="TOGGLE_PROFILE_EDIT",d="PROFILE_SAVING",f="SAVING_RECENT",_="PROFILE_RENAMING",g="PROFILE_OVERWRITING",b="PROFILE_DELETING",h="PROFILE_SAVE_ERROR",v="SET_CURRENT_PROFILE",E="SET_BUTTON_TEXT",w="SET_PROFILE_STATUS",y="PROFILE_LOADING",x="PROFILE_LOAD_ERROR",k={saving:!1,saving_recent:!1,profile_renaming:!1,profile_deleting:!1,profile_overwriting:!1,profile_save_error:"",profile_loading:null,profile_load_error:!1},Z={saved:window.wpmdb_data.migration_profiles,recent:window.wpmdb_data.recent_migrations,toggled:[],status:k,current_profile:null,loaded_profile:null,imported:!1,ui:{btn_text:(0,a.__)("Save","wp-migrate-db")}},N=(0,i.Lq)(Z,{RESET_APP:function(e,t){return e.current_profile=null,e.loaded_profile=null,e.status=k,e},PROFILE_SAVE_ERROR:function(e,t){return e.status={saving:!1,saving_recent:!1,profile_renaming:!1,profile_deleting:!1,profile_overwriting:!1,profile_save_error:t.payload.data},e},TOGGLE_PROFILE_EDIT:function(e,t){return e.toggled=(0,s.XV)(e.toggled,t.payload),e},PROFILE_SAVING:function(e,t){return e.status=(0,r.Z)((0,r.Z)({},e.status),{},{saving:!0,profile_save_error:""}),e.ui.btn_text=(0,a.__)("Saving...","wp-migrate-db"),e},SAVING_RECENT:function(e,t){return e.status=(0,r.Z)((0,r.Z)({},e.status),{},{saving_recent:!0,profile_save_error:""}),e},ADD_PROFILE:function(e,t){return e.saved.push(t.payload),e.status.saving=!1,e},ADD_RECENT_MIGRATION:function(e,t){return e.recent=t.payload.profiles,e.status.saving_recent=!1,e},PROFILE_DELETING:function(e,t){return e.status=(0,r.Z)((0,r.Z)({},e.status),{},{profile_deleting:!0,profile_save_error:""}),e},REMOVE_PROFILE:function(e,t){return e[t.payload.slice].splice(t.payload.index,1),e.status=(0,r.Z)((0,r.Z)({},e.status),{},{saving_recent:!1,profile_deleting:!1}),e},PROFILE_RENAMING:function(e,t){return e.status=(0,r.Z)((0,r.Z)({},e.status),{},{profile_renaming:!0,profile_save_error:""}),e},RENAME_PROFILE:function(e,t){return e.saved[t.payload.index].name=t.payload.text,e.status.profile_renaming=!1,e},PROFILE_OVERWRITING:function(e,t){return e.status.profile_overwriting=t.payload,e.status.profile_save_error="",null!==e.loaded_profile&&(e.loaded_profile.imported=!1),e.ui.btn_text=(0,a.__)("Saving...","wp-migrate-db"),e},PROFILE_LOADING:function(e,t){return e.status.profile_loading=!0,e},LOAD_PROFILE:function(e,t){return e.status.profile_loading=!1,e.current_profile=t.payload.id,e.loaded_profile=t.payload.profile,e},SET_PROFILE_STATUS:function(e,t){return e.status.profile_loading=!1,e},SET_CURRENT_PROFILE:function(e,t){return e.current_profile=t.payload.id,e},SET_BUTTON_TEXT:function(e,t){return e.ui.btn_text=t.payload,e},PROFILE_LOAD_ERROR:function(e,t){return e.status.profile_load_error=t.payload,e}});t.ZP=N},66441:function(e,t,n){"use strict";n.d(t,{Dh:function(){return h},G3:function(){return u},IY:function(){return d},KM:function(){return b},O9:function(){return p},Q0:function(){return f},S_:function(){return v},ci:function(){return m},hx:function(){return _},jC:function(){return x},oK:function(){return o},zi:function(){return g}});var r=n(18489),a=n(96480),i=n.n(a),s=n(26429),c=n(29942),l=n(29950),o="UPDATE_STANDARD_SEARCH_REPLACE",u="UPDATE_STANDARD_SEARCH_REPLACE_DOMAIN",p="TOGGLE_STANDARD_SEARCH_REPLACE",m="TOGGLE_STANDARD_SEARCH_REPLACE_VISIBLE",d="UPDATE_CUSTOM_SEARCH_REPLACE",f="REORDER_CUSTOM_SEARCH_REPLACE",_="ADD_CUSTOM_SEARCH_REPLACE_ITEM",g="DELETE_CUSTOM_SEARCH_REPLACE_ITEM",b="SET_CUSTOM_SEARCH_REPLACE",h="SET_CUSTOM_SEARCH_DOMAIN_LOCKED",v="RESET_CUSTOM_SEARCH_REPLACE",E=function(){return{replace_old:arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",replace_new:arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",focus:arguments.length>2&&void 0!==arguments[2]&&arguments[2],regex:arguments.length>3&&void 0!==arguments[3]&&arguments[3],isValidRegex:arguments.length>6&&void 0!==arguments[6]?arguments[6]:null,replace_old_placeholder:arguments.length>4&&void 0!==arguments[4]?arguments[4]:"",replace_new_placeholder:arguments.length>5&&void 0!==arguments[5]?arguments[5]:"",id:arguments.length>7&&void 0!==arguments[7]?arguments[7]:i()()}},w=[E()],y={standard_search_replace:{domain:{search:"",replace:""},path:{search:"",replace:""}},standard_options_enabled:["domain","path"],standard_search_visible:!0,custom_search_replace:w,custom_search_domain_locked:!1},x=function(){var e,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:y,n=arguments.length>1?arguments[1]:void 0;return(0,s.ZP)(t,(function(t){switch(n.type){case o:return(0,r.Z)((0,r.Z)({},t),{},{standard_search_replace:n.payload});case u:return(0,r.Z)((0,r.Z)({},t),{},{standard_search_replace:{path:(0,r.Z)({},t.standard_search_replace.path),domain:n.payload}});case m:return(0,r.Z)((0,r.Z)({},t),{},{standard_search_visible:n.payload});case p:return e=(0,c.XV)(t.standard_options_enabled,n.payload),(0,r.Z)((0,r.Z)({},t),{},{standard_options_enabled:e});case d:var a=n.payload,i=a.key,s=a.option,y=a.value;return t.custom_search_replace[i]&&(t.custom_search_replace[i][s]=y),t;case f:return(0,r.Z)((0,r.Z)({},t),{},{custom_search_replace:n.payload});case _:return t.custom_search_replace.push(E("","",n.payload)),t;case g:return e=(0,c.XV)(t.custom_search_replace,t.custom_search_replace[n.index]),(0,r.Z)((0,r.Z)({},t),{},{custom_search_replace:e});case v:return(0,r.Z)((0,r.Z)({},t),{},{custom_search_replace:w});case b:var x=n.payload.local_site,k=x.this_url,Z=x.this_path,N=n.payload,P=N.intent,S=N.force_update;return"savefile"===P||S&&"import"!==P?t.custom_search_replace=[E("","")]:S&&"import"===P&&(t.custom_search_replace=[E("",(0,c.Ph)(k)),E("",Z),E("","")]),t;case h:return t.custom_search_domain_locked=n.payload,t;case l.Ld:var T=n.payload.profile.value.search_replace;return(0,r.Z)({},T);default:return t}}))}},19826:function(e,t,n){"use strict";n.d(t,{Ft:function(){return m},TE:function(){return u},YQ:function(){return o},eH:function(){return p},km:function(){return d}});var r=n(36222),a=n(18489),i=n(88368),s=n(26429),c=window.wpmdb_settings;Object.entries(c).forEach((function(e){var t=(0,i.Z)(e,2),n=t[0];t[1];"delay_between_requests"!==n&&(["1",""].includes(c[n])&&(c[n]="1"===c[n]))})),c.max_request=parseInt(c.max_request),c.isPro="false"!==window.wpmdb_data.is_pro,c.delay_between_requests=parseInt(c.delay_between_requests);c.masked_licence||(c.masked_licence=null);var l=(0,a.Z)((0,a.Z)({},c),{status:{resetting_api_key:!1},errors:{}}),o="SET_SETTINGS_STATUS",u="SETTINGS_ERROR",p="DELETE_SETTINGS_ERROR",m="SET_API_KEY",d="UPDATE_SETTING";t.ZP=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:l,t=arguments.length>1?arguments[1]:void 0;return(0,s.ZP)(e,(function(n){switch(t.type){case"SET_ACTION":return n;case p:return delete n.errors[t.payload],n;case u:return(0,a.Z)((0,a.Z)({},n),{},{errors:(0,a.Z)((0,a.Z)({},n.errors),{},(0,r.Z)({},t.payload.location,t.payload.message))});case"LICENSE_REMOVED":return(0,a.Z)((0,a.Z)({},n),{},{licence:"",masked_licence:null});case o:return(0,a.Z)((0,a.Z)({},n),{},{status:(0,a.Z)((0,a.Z)({},n.status),{},(0,r.Z)({},t.payload.type,t.payload.status))});case m:return(0,a.Z)((0,a.Z)({},n),{},{key:t.payload});case d:return(0,a.Z)((0,a.Z)({},n),{},(0,r.Z)({},t.payload.setting,t.payload.value));case"UPDATE_SETTINGS":return(0,a.Z)((0,a.Z)({},n),t.payload);default:return e}}))}},58696:function(e,t,n){"use strict";n.d(t,{C:function(){return i}});var r=n(4516),a=function(e){return e.migrations.connection_info};function i(e,t){return(0,r.fX)(a,"connection_info",e,t)}},4516:function(e,t,n){"use strict";n.d(t,{Au:function(){return b},FY:function(){return h},Gn:function(){return N},Jm:function(){return w},NR:function(){return g},_P:function(){return v},do:function(){return Z},fX:function(){return y},r5:function(){return _},xg:function(){return E}});var r=n(27166),a=n(33032),i=n(56802),s=n(12544),c=n.n(s),l=n(29942),o=function(e){return e.migrations.current_migration},u=function(e){return e.migrations.remote_site},p=function(e){return e.migrations.local_site},m=function(e){return e.migrations},d=function(e){return e.migrations.search_replace},f=function(e){return e.migrations.migration_progress};function _(e,t){return y(o,"current_migration",e,t)}function g(e,t){return y(m,"migration",e,t)}function b(e){return E("migration_tables",e)}function h(e,t){return y(u,"remote_site",e,t)}function v(e,t){return y(p,"local_site",e,t)}function E(e,t,n){return y(f,"migration_progress",e,t,n)}function w(e,t){return y(d,"search_replace",e,t)}var y=function(e,t,n,r){var a=arguments.length>4&&void 0!==arguments[4]?arguments[4]:"array",s=(0,i.P1)([e],(function(e){return"object"===typeof n?x(e,n,a):k(e,n)}));return s(r)},x=function(e,t,n){var r=[],a={};return t.forEach((function(t){e.hasOwnProperty(t)&&("array"===n?r.push(e[t]):"object"===n&&(a[t]=e[t]))})),Object.keys(a).length>0?a:r},k=function(e,t){if(e.hasOwnProperty(t))return e[t]},Z=function(e){var t=_("intent",e);return c()(["push","pull"],t)},N=function(){var e=(0,a.Z)((0,r.Z)().mark((function e(t){var a,i,s,c,o,u,p,m;return(0,r.Z)().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(a=_("intent",t),i=_("tables_option",t),s=_("tables_selected",t),c=v("this_prefixed_tables",t),"selected"!==i){e.next=8;break}c=s,e.next=19;break;case 8:if("import"!==a||!(0,l.Yu)()){e.next=18;break}return e.next=11,Promise.resolve().then(n.bind(n,67821));case 11:o=e.sent,u=o.selectFromImportData,p=u("tables",t),m=v("this_temp_prefix",t),c=p.map((function(e){return m+e})),e.next=19;break;case 18:"pull"===a&&(c=h("prefixed_tables",t));case 19:return e.abrupt("return",c);case 20:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}()},76178:function(e,t,n){"use strict";n.d(t,{O:function(){return i}});var r=n(4516),a=function(e){return e.panels};function i(e,t){return(0,r.fX)(a,"panels",e,t)}},86191:function(e,t,n){"use strict";n.d(t,{d:function(){return i}});var r=n(4516),a=function(e){return e.profiles};function i(e,t){return(0,r.fX)(a,"profiles",e,t)}},27325:function(e,t,n){"use strict";n.d(t,{u:function(){return i}});var r=n(4516),a=function(e){return e.settings};function i(e,t){return(0,r.fX)(a,"settings",e,t)}},19085:function(e,t,n){"use strict";n.d(t,{en:function(){return E},HW:function(){return v},Ag:function(){return g},Q:function(){return h}});var r,a=n(17186),i=n(4665),s=n(30348);function c(){return c=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},c.apply(this,arguments)}n.p;var l=n(9712),o=n(39794);function u(){return u=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},u.apply(this,arguments)}n.p;var p,m,d,f,_,g=(0,s.ZP)(l.r)(p||(p=(0,a.Z)(["\n  position: relative;\n  &:hover {\n    cursor: pointer;\n  }\n"]))),b=(s.ZP.p(m||(m=(0,a.Z)(["\n  display: flex;\n  align-items: center;\n"]))),(0,s.ZP)((function(e){return i.createElement("svg",c({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 16 16",id:"el_HS5Ejor-n"},e),r||(r=i.createElement("path",{d:"M8 2v2a4 4 0 1 0 4 4h2a6 6 0 1 1-6-6Z",id:"el_6X7lquFKkl"})))}))(d||(d=(0,a.Z)(["\n  width: 1.2rem;\n  height: 1.2rem;\n  display: inline;\n"])))),h=function(e){return i.createElement("span",{className:"styled-spinner-wrap ".concat(e.className?e.className:"")},i.createElement(b,null))},v=(0,s.ZP)(h)(f||(f=(0,a.Z)(["\n  margin-left: 5px;\n  position: absolute;\n  top: -10px;\n  #el_6X7lquFKkl {\n    fill: ",";\n  }\n"])),o.HT),E=(0,s.ZP)((function(e){return i.createElement("svg",u({width:24,height:24,viewBox:"0 0 24 24",xmlSpace:"preserve"},e),i.createElement("g",{transform:"translate(4 4)"},i.createElement("circle",{id:"license-checked-a",cx:8,cy:8,r:8,style:{fill:"#236de7"}}),i.createElement("path",{d:"M7.587 11.338a1.01 1.01 0 0 1-1.433 0L3.933 9.104a1.024 1.024 0 0 1 0-1.442 1.01 1.01 0 0 1 1.433 0l1.323 1.331c.1.1.262.1.362 0l3.583-3.604a1.01 1.01 0 0 1 1.433 0 1.025 1.025 0 0 1 0 1.442l-4.48 4.507Z",style:{fill:"#fff",fillRule:"nonzero"}})))}))(_||(_=(0,a.Z)(["\n  use {\n    fill: #236de7;\n  }\n"])))},39794:function(e,t,n){"use strict";n.d(t,{HT:function(){return r},Qp:function(){return a},qP:function(){return i}});var r="#236DE7",a="#575757",i="#dc3232"},27523:function(e,t,n){"use strict";var r,a=n(4665),i=n(62295),s=n(4296),c=n(79043),l=n(52204),o=n(60050);function u(){return u=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},u.apply(this,arguments)}var p,m,d=function(e){return a.createElement("svg",u({xmlns:"http://www.w3.org/2000/svg",width:52,height:52,viewBox:"0 0 52 52"},e),r||(r=a.createElement("g",{fill:"none"},a.createElement("path",{fill:"#FFF",d:"M42.867 30.29h-5.531c0-1 .678-1.813 1.516-1.813.018 0 .036.004.054.004.502-.943 1.383-1.571 2.387-1.571.982 0 1.853.607 2.358 1.52a1.727 1.727 0 0 1 1.16-.462c1.075 0 1.942 1.04 1.942 2.322h-3.886ZM30.797 18.066c-1.39-2.155-.705-5.07 1.534-6.514l.387.603c.834-1.86 2.751-3.16 4.983-3.16v10.036h2c.004-1.167 1.127-2.113 2.517-2.113s2.514.946 2.514 2.113h-2.514c-.007 1.177-3.166 2.134-7.063 2.134-3.9 0-7.063-.957-7.063-2.134h3.575a4.452 4.452 0 0 1-.87-.965Zm-6.334 3.99h-6.688c0-.787.82-1.43 1.827-1.43.087 0 .17.011.25.019.487-.408 1.177-.665 1.95-.665 1.469 0 2.66.928 2.66 2.077Zm-8.873-10.064 1.502 1.593c.03-.412.138-.823.34-1.21.805-1.545 2.755-2.123 4.362-1.289l-2.466 4.745h.953c.004-.653.636-1.184 1.416-1.184.787 0 1.419.53 1.419 1.188l-1.42.003c-.01.661-1.787 1.196-3.983 1.196-2.199 0-3.983-.538-3.983-1.203h1.52c-.884-1.127-.765-2.791.34-3.838Zm-6.8 4.724c.16.173.311.357.452.549a4.704 4.704 0 0 1 4.362 3.643 4.696 4.696 0 0 1-1.722 4.774 7.253 7.253 0 0 1 2.484 1.116 8.147 8.147 0 0 1 3.716-1.957 8.142 8.142 0 0 1 5.037.451 4.34 4.34 0 0 1 6.673 3.9 2.51 2.51 0 0 1 2.254 3.084 7.813 7.813 0 0 1 8.515 6.013c.047.21.083.419.116.628a7.137 7.137 0 0 1 3.426 3.347c-.234.26-.48.52-.725.77C38.953 47.544 32.8 50.35 26 50.35 12.574 50.35 1.65 39.426 1.65 26c0-3.756.856-7.316 2.38-10.494.14-.292.289-.574.44-.86a6.451 6.451 0 0 1 3.58 1.38c.288.177.54.408.74.69ZM26 0C16.127 0 6.944 5.753 2.61 14.658A25.753 25.753 0 0 0 0 26c0 14.336 11.664 26 26 26 6.691 0 13.054-2.553 17.918-7.186C49.13 39.849 52 33.168 52 26 52 11.664 40.336 0 26 0Z"}),a.createElement("path",{fill:"#FFF",d:"M8.637 31.951c.23-.053.456-.068.685-.093-.23.025-.455.04-.685.093a6.098 6.098 0 0 0-1.821.74v.003a6.012 6.012 0 0 1 1.821-.743m9.325 6.798zM9.206 22.781a7.7 7.7 0 0 0-.172-1.578c-.08-.33-.194-.638-.309-.95.115.312.23.62.305.95.122.531.169 1.058.176 1.578m-3.798-6.627c-.018-.014-.04-.021-.057-.032.018.01.04.018.057.032zm17.572 21.55c.937-.84 2.168-1.464 3.568-1.765a9.251 9.251 0 0 0 0 0c-1.4.301-2.635.925-3.568 1.765a7.434 7.434 0 0 0 0 0M36.293 40.353c.895-.22 1.786.193 2.268.953a4.853 4.853 0 0 1 3.577.883 4.833 4.833 0 0 0-3.574-.883c-.485-.76-1.373-1.172-2.271-.953"}),a.createElement("path",{fill:"#81BDD4",d:"M17.569 28.177h1.61a2.66 2.66 0 0 1 3.04-.018l-.007.018h.974c.015-.555.549-1.004 1.21-1.004.673-.003 1.219.46 1.219 1.026h-1.222l-.004.003h.008c0 .42-.83.781-2.018.942v5.153a3.054 3.054 0 0 1-2.81-1.861l-.215.347c-1.272-.854-1.665-2.584-.873-3.862.004-.003.008-.01.011-.014-.567-.186-.923-.43-.923-.705h.014c-.003-.007-.014-.018-.014-.025M3.586 15.213c.436.157.858.347 1.258.577.018.011.04.018.058.033a7.842 7.842 0 0 1 3.28 3.755c.043.103.065.216.101.318.117.317.233.631.313.967.12.54.167 1.077.175 1.606a7.818 7.818 0 0 1-.451 2.84c-.022.061-.04.12-.066.174a7.867 7.867 0 0 1-3.89 4.227c.426.288.804.646 1.135 1.044.414.5.752 1.069.967 1.711a6.168 6.168 0 0 1 1.847-.752c.232-.054.461-.065.694-.095a6.16 6.16 0 0 1 4.73 1.46c.774.672 1.4 1.53 1.774 2.537.211.128.407.277.585.441.011.011.026.019.037.03.033.029.058.062.087.094.41.413.72.928.89 1.522.019.066.044.128.059.194.01.047.015.095.022.142.025.135.05.27.062.402.112-.059.236-.106.356-.161a6.593 6.593 0 0 1 1.102-.409c.047-.014.087-.033.134-.044.015-.003.03-.003.04-.007a7.836 7.836 0 0 1 1.85-.222c.524 0 1.037.05 1.524.146.97-.854 2.247-1.49 3.701-1.796a9.442 9.442 0 0 1 1.978-.204c1.076 0 2.098.175 3.025.489 1.541.525 2.817 1.441 3.624 2.587.306.438.546.905.702 1.402a2.307 2.307 0 0 1 2.898.81 5.413 5.413 0 0 1 5.013 2.003c.251-.251.495-.514.731-.777a7.175 7.175 0 0 0-3.454-3.383 7.75 7.75 0 0 0-.113-.635c-.905-3.964-4.639-6.544-8.576-6.077.047-.182.073-.376.073-.573a2.534 2.534 0 0 0-2.338-2.544c.004-.072.007-.15.007-.226.011-2.42-1.93-4.394-4.344-4.409a4.298 4.298 0 0 0-2.382.694 8.168 8.168 0 0 0-5.071-.456 8.189 8.189 0 0 0-3.745 1.978 7.24 7.24 0 0 0-2.498-1.128 4.756 4.756 0 0 0 1.734-4.825 4.737 4.737 0 0 0-4.392-3.682 6.072 6.072 0 0 0-.458-.555 2.46 2.46 0 0 0-.741-.697 6.47 6.47 0 0 0-3.603-1.394 24.33 24.33 0 0 0-.444.868m41 12.85a1.87 1.87 0 0 0-1.214.458c-.529-.907-1.44-1.51-2.473-1.51-1.047 0-1.97.624-2.495 1.56-.02 0-.038-.003-.057-.003-.877 0-1.588.807-1.588 1.8h9.862c0-1.273-.908-2.306-2.035-2.306"}),a.createElement("path",{fill:"#04223F",d:"M42.746 41.767a5.478 5.478 0 0 0-3.847-.891 2.314 2.314 0 0 0-2.903-.805 5.109 5.109 0 0 0-.704-1.391c-.81-1.138-2.09-2.048-3.636-2.57a9.555 9.555 0 0 0-3.034-.485 9.79 9.79 0 0 0-1.984.203c-1.458.304-2.738.935-3.712 1.783-.489-.094-1-.145-1.528-.145-.646 0-1.27.08-1.856.22-.011.004-.026.004-.04.008-.044.015-.088.033-.135.044-.39.105-.759.242-1.105.405-.12.055-.245.102-.358.16v.004a3.832 3.832 0 0 0-.062-.403c-.007-.047-.01-.094-.022-.137-.014-.07-.04-.13-.058-.196a3.594 3.594 0 0 0-.893-1.515c-.03-.029-.055-.061-.088-.09-.01-.011-.025-.019-.036-.03a3.686 3.686 0 0 0-.587-.438 6.193 6.193 0 0 0-1.78-2.518 6.21 6.21 0 0 0-4.748-1.45c-.23.025-.46.04-.693.094a6.173 6.173 0 0 0-1.852.75v-.003a5.271 5.271 0 0 0-.97-1.7 5.385 5.385 0 0 0-1.138-1.032 7.86 7.86 0 0 0 3.902-4.2l.065-.174c.325-.888.474-1.841.453-2.82a7.498 7.498 0 0 0-.18-1.594c-.076-.333-.192-.645-.31-.96-.036-.102-.057-.214-.101-.316a7.811 7.811 0 0 0-3.29-3.728c-.018-.015-.04-.022-.058-.033a7.91 7.91 0 0 0-1.262-.573 24.168 24.168 0 0 0-2.403 10.531c0 13.473 11.031 24.435 24.59 24.435 6.867 0 13.08-2.816 17.548-7.342a5.567 5.567 0 0 0-1.185-1.098"}),a.createElement("path",{fill:"#81BDD4",d:"M19.648 20.235c-.948 0-1.717.631-1.717 1.417h6.276c0-1.14-1.118-2.062-2.496-2.062-.725 0-1.372.254-1.83.66a1.64 1.64 0 0 0-.233-.015"}),a.createElement("path",{fill:"#04223F",d:"M18.15 17.034c2.097 0 3.794-.533 3.805-1.188l1.355-.003c0-.651-.607-1.177-1.355-1.177-.745 0-1.35.526-1.353 1.173h-.91l2.356-4.702c-1.535-.827-3.398-.254-4.167 1.278-.19.383-.297.79-.321 1.198l-1.439-1.578c-1.055 1.038-1.172 2.688-.324 3.804l-1.452.004c0 .658 1.704 1.191 3.805 1.191m16.874 4.483c3.989 0 7.223-.987 7.23-2.201h2.574c0-1.203-1.154-2.179-2.573-2.179-1.424 0-2.573.976-2.577 2.179H37.63l.004-10.35c-2.288 0-4.251 1.34-5.105 3.258l-.396-.622c-2.292 1.49-2.994 4.496-1.571 6.72.248.387.558.707.89.994h-3.659c0 1.214 3.238 2.201 7.23 2.201m-16.166 7.125-.01.015c-.796 1.286-.402 3.027.874 3.887l.215-.349a3.062 3.062 0 0 0 2.819 1.874v-5.188c1.192-.162 2.024-.526 2.024-.948h-.008l.004-.004H26c0-.57-.547-1.036-1.221-1.032-.664 0-1.2.452-1.215 1.01h-.977l.007-.018c-.973-.654-2.176-.607-3.048.018h-1.615c0 .007.011.015.011.026h-.011c0 .275.357.522.926.709"}))))},f=(n.p,n.p+"static/media/mdb-branding-transparent.edbb2b6f.svg"),_=n(29942),g=(n(88634),n(27166)),b=n(33032),h=n(17186),v=n(30348),E=n(75338),w=n(42233),y=v.ZP.div(p||(p=(0,h.Z)(["\n  display: grid;\n"]))),x=n(18489),k=n(88368),Z=n(29890),N=n(31330),P=n.n(N),S=n(10734),T=n.n(S),O=n(6142);function A(){return A=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},A.apply(this,arguments)}var C=function(e){return a.createElement("svg",A({xmlns:"http://www.w3.org/2000/svg",width:16,height:16,viewBox:"0 0 16 16"},e),m||(m=a.createElement("path",{fill:"none",stroke:"#236DE7",strokeWidth:2,d:"M4 8.5 7.2 11 12 5"})))},R=(n.p,function(e){var t=e.deleteItem,n=e.cancelDelete,r=e.item,i=e.index;return a.createElement("div",{className:"flex-container confirm-delete tip"},a.createElement("div",{className:"speech-shadow"},a.createElement("div",{className:"speech-bubble-left"},a.createElement("p",{className:"margin-bottom"},(0,w.__)("Are you sure?","wp-migrate-db")),a.createElement("div",{className:"flex-container"},a.createElement("button",{className:"btn btn-tooltip delete icon",onClick:function(){return t(r,i)}},a.createElement("div",{className:"flex-container"},a.createElement("div",null,a.createElement(C,{className:"styled-check"})),(0,w.__)("Delete","wp-migrate-db"))),a.createElement("button",{className:"btn btn-tooltip-stroke",onClick:function(){return n()}},(0,w.__)("Cancel","wp-migrate-db"))))))}),I=(n(88847),["profilesList","profilesRenaming","profilesDeleting","btn_text","toggled","recentProfilesEmpty"]),D=function(e){var t=e.profilesList,n=e.profilesRenaming,r=e.profilesDeleting,i=e.btn_text,s=e.toggled,c=e.recentProfilesEmpty,o=(0,Z.Z)(e,I),u=[],p=r||n,m=(0,a.useState)(),d=(0,k.Z)(m,2),f=d[0],_=d[1],h=function(){_("")},v=function(){var e=(0,b.Z)((0,g.Z)().mark((function e(t,n){return(0,g.Z)().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!p){e.next=2;break}return e.abrupt("return",!1);case 2:return e.next=4,o.removeSavedProfile(t.guid,n,t.name);case 4:e.sent&&_("");case 6:case"end":return e.stop()}}),e)})));return function(t,n){return e.apply(this,arguments)}}(),E=function(e,t){if(t)return!1;o.toggleEdit(e)},y=function(e,t){if(t)return!1;!function(e){_(e)}(e)},N=t.map((function(e,t){var c=(0,x.Z)((0,x.Z)({},o),{},{deleteItem:v,cancelDelete:h,item:e,index:t}),m=s.includes(t),d=s.length>0&&!m,_=d?"disabled ":"",g=(0,O.decode)(e.name),b=d?a.createElement("span",{className:"link"},g):a.createElement(l.rU,{to:"/migrate/".concat(e.id),onClick:function(){o.profileLoading()},className:"link"},g);if(-1!==T()(s,t)){var k="btn icon save-profile btn-tooltip profile-screen".concat("success"===i?" btn-success":"");b=a.createElement(a.Fragment,null,a.createElement(P(),{active:m},a.createElement("div",{className:"button-wrap ".concat(m?" toggled":"")},a.createElement("input",{type:"text",defaultValue:g,ref:function(e){return u[t]=e}}),a.createElement("button",{className:"action-btn ".concat(k).concat(r||n?" in-progress":""),onClick:function(){if(p)return!1;o.changeName(e.guid,t,u[t].value)}},a.createElement(C,{className:"styled-check"}),"Save"),a.createElement("button",{onClick:function(){return E(t,d)},className:"action-btn btn btn-tooltip-stroke profile-screen"},(0,w.__)("Cancel","wp-migrate-db")))))}return a.createElement("tr",{className:"".concat(_,"flex-container").concat(m?" toggled":""),key:t},a.createElement("td",null,e.id),a.createElement("td",{className:"table-col-action"},b),!m&&a.createElement(a.Fragment,null,a.createElement("td",null,a.createElement("button",{onClick:function(){return E(t,d)},className:"link"},(0,w.__)("Rename","wp-migrate-db"))),a.createElement("td",null,a.createElement("button",{className:"action-btn delete-profile link text-error".concat(r||n?" in-progress":" text-error"),onClick:function(){return y(t,d)}},(0,w.__)("Delete","wp-migrate-db")))),f===t&&a.createElement("td",{className:"relative"},a.createElement(R,c)))})),S=c&&1===t.length?"one-profile ":"";return a.createElement("table",{className:"".concat(S,"profile-table")},a.createElement("tbody",null,N))},L=n(76554),M=["recentMigrations","savedProfilesEmpty"],F=function(e){var t=e.recentMigrations,n=e.savedProfilesEmpty,r=(0,Z.Z)(e,M),i=t.map((function(e,t){var n=!!r.isSaving;return a.createElement("tr",{className:"flex-container",key:t},a.createElement("td",{className:"table-col-item"},a.createElement(l.rU,{to:"/unsaved/".concat(e.id),onClick:function(){r.profileLoading()},className:"link"},(0,O.decode)(e.name))),a.createElement("td",{className:"date"},(0,L.ee)("M d ".concat(window.wpmdb_data.time_format),1e3*e.date)),a.createElement("td",null,a.createElement("button",{className:r.isSaving?"link saving":"link",disabled:n,onClick:function(){if(n)return!1;r.handleUnsaved(e.id,t)}},(0,w.__)("Save","wp-migrate-db"))))})),s=n&&1===t.length?"one-profile ":"";return a.createElement("table",{className:"".concat(s,"profile-table")},a.createElement("tbody",null,i))},B=n(61358);function j(){return j=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},j.apply(this,arguments)}var U=function(e){return a.createElement("svg",j({height:16,viewBox:"0 0 16 16",width:16,xmlns:"http://www.w3.org/2000/svg"},e),a.createElement("path",{d:"M6 1a1 1 0 0 0-2 0v8a1 1 0 0 0 2 0V1Z",style:{fill:"#fff"},transform:"translate(3 3)"}),a.createElement("path",{d:"M6 1a1 1 0 0 0-2 0v8a1 1 0 0 0 2 0V1Z",style:{fill:"#fff"},transform:"rotate(-90 8 5)"}))},z=(n.p,n(29214)),G=n(66055),V=n(22973),H=n(29950),W=n(22633),K=n(14251),q=n(91828),Y=n(27325),J=n(4516),Q=n(58696),X=function(e){switch(e){case"push":case"pull":return"connect";case"find_replace":case"backup":case"savefile":default:return"database";case"import":return"import"}};function $(){return(0,G.m)(V.jJ)}function ee(e,t){return function(){var r=(0,b.Z)((0,g.Z)().mark((function r(a,s){var c,l;return(0,g.Z)().wrap((function(r){for(;;)switch(r.prev=r.next){case 0:return(0,i.dC)((function(){a((0,K.oA)()),a($())})),r.prev=1,r.next=4,(0,_.op)("/load-profile",{id:e,unSaved:t},!1,a);case 4:c=r.sent,r.next=11;break;case 7:return r.prev=7,r.t0=r.catch(1),a((0,G.m)(V.KG,(0,w.__)("Error loading profile, please check your server error logs.","wp-migrate-db"))),r.abrupt("return");case 11:if("Profile does not exist."!==c.data){r.next=14;break}return a((0,G.m)(V.KG,c.data)),r.abrupt("return");case 14:if(c.data.hasOwnProperty("profile")){r.next=17;break}return a((0,G.m)(V.KG,c.data)),r.abrupt("return");case 17:if(c.data.profile.value=JSON.parse(c.data.profile.value),l=c.data.profile.value.current_migration.intent,(0,_.Yu)()||["savefile","find_replace","backup_local"].includes(l)){r.next=22;break}return a({type:V.$Q,payload:{}}),r.abrupt("return",!1);case 22:return a({type:H.Ld,payload:c.data}),(0,i.dC)((function(){(0,_.Yu)()&&["push","pull"].includes(l)&&a(function(){var e=(0,b.Z)((0,g.Z)().mark((function e(t,r){var a,i,s,c,l;return(0,g.Z)().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return a=(0,J.r5)("intent",r()),i=(0,Q.C)("connection_state",r()),e.next=4,Promise.resolve().then(n.bind(n,34363));case 4:if(s=e.sent,c=s.changeConnection,l=s.connectToRemote,"push"!==a&&"pull"!==a){e.next=13;break}return e.next=9,t(c("".concat(i.url,"\n").concat(i.key)));case 9:if(!e.sent){e.next=12;break}return e.abrupt("return",!1);case 12:t(l(a));case 13:case"end":return e.stop()}}),e)})));return function(t,n){return e.apply(this,arguments)}}()),"import"===l&&a((0,G.m)(H.n_,!1)),setTimeout((function(){a((0,W.I4)("action_buttons"))}),100)})),r.abrupt("return",c.data);case 25:case"end":return r.stop()}}),r,null,[[1,7]])})));return function(e,t){return r.apply(this,arguments)}}()}function te(e){return function(){var t=(0,b.Z)((0,g.Z)().mark((function t(n,r){var a,i,s,c,l;return(0,g.Z)().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return a=e.path.includes("unsaved"),i=(0,Y.u)("masked_licence",r()),t.next=4,n(ee(e.params.id,a));case 4:if(s=t.sent){t.next=7;break}return t.abrupt("return",!1);case 7:c=s.profile.value.current_migration.intent,l=X(c),"import"!==c||i||n((0,K.I8)(!1)),n((0,W.$f)({panel:l,title:q.B[c],intent:c},!0));case 11:case"end":return t.stop()}}),t)})));return function(e,n){return t.apply(this,arguments)}}()}var ne,re,ae=n(86191),ie=(0,v.ZP)(y)(ne||(ne=(0,h.Z)(["\n  h3 {\n    padding-bottom: 0.5rem;\n  }\n"]))),se=(0,i.$j)((function(e){return{toggled:(0,ae.d)("toggled",e),notifications:e.notifications,profiles:e.profiles,migration:e.migrations,current_migration:e.migrations.current_migration,connection_info:e.migrations.connection_info}}),{handleUnSaved:B.UF,handleItemNameChange:B.hV,removeProfile:B.NF,removeSavedProfile:B.qH,toggleProfileEdit:B.Tv,profileLoading:$,renderSaveProfileButtonText:B.F9})((function(e){var t=e.profiles,n=(0,i.I0)(),r=e.profiles.status,s=r.saving_recent,o=r.saving,u=r.profile_deleting,p=r.profile_renaming,m=r.profile_save_error;(0,a.useEffect)((function(){var e=new URL(window.location.href),t=new URLSearchParams(window.location.search);if(t.has("redirect_profile")){var n="unsaved";"1"===t.get("saved_profile")&&(n="migrate");var r=t.get("redirect_profile");t.delete("saved_profile"),t.delete("redirect_profile"),e.search=t.toString(),window.location.href="".concat(e.toString(),"#").concat(n,"/").concat(r)}}),[]);var d=function(){var t=(0,b.Z)((0,g.Z)().mark((function t(n,r){return(0,g.Z)().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,e.handleUnSaved(n,r);case 2:case"end":return t.stop()}}),t)})));return function(e,n){return t.apply(this,arguments)}}();return a.createElement(ie,{className:"wpmdb-profiles"},m&&"migration"!==m.location&&a.createElement(z.Z,{type:"danger"},m.message),a.createElement("section",{className:"profiles-list"},a.createElement("div",{className:"container-shadow table"},a.createElement("div",{className:"flex-container"},a.createElement("h2",{className:"table-heading"},(0,w.__)("Saved Profiles","wp-migrate-db"))),a.createElement("div",{className:"table-divider-line bg-grey-light"}),t.saved.length>0?a.createElement(D,(0,c.Z)({profilesList:t.saved,changeName:function(t,n,r){e.handleItemNameChange(t,n,r)},removeProfile:function(t,n,r){e.removeSavedProfile(t,n,r)},toggleEdit:function(t){e.toggleProfileEdit(t)},recentProfilesEmpty:0===t.recent.length,toggled:e.profiles.toggled,profilesRenaming:p,profilesDeleting:u,profileLoading:e.profileLoading},e)):a.createElement("div",{className:"no-items-wrap".concat(0===t.saved.length&&1===t.recent.length?" one-profile":"")},a.createElement("p",{className:"no-items"},(0,E.ZP)((0,w.gB)((0,w.__)('There are no saved profiles &mdash; <a href="%s">Get Started</a>.',"wp-migrate-db"),"#migrate"))))),a.createElement("div",{className:"container-shadow table"},a.createElement("div",{className:"flex-container"},a.createElement("h2",{className:"table-heading"},(0,w.__)("Last 10 Unsaved Profiles","wp-migrate-db"))),a.createElement("div",{className:"table-divider-line bg-grey-light"}),t.recent.length>0?a.createElement(F,{handleUnsaved:d,recentMigrations:t.recent,isSaving:o,savedProfilesEmpty:0===t.saved.length,isSavingRecent:s,profileLoading:e.profileLoading}):a.createElement("div",{className:"no-items-wrap".concat(1===t.saved.length&&0===t.recent.length?" one-profile":"")},a.createElement("p",{className:"no-items"},(0,E.ZP)((0,w.gB)((0,w.__)('There are no recent migrations &mdash; <a href="%s">Get started</a>.',"wp-migrate-db"),"#migrate")))))),a.createElement(l.rU,{to:"/migrate",className:"btn new-migration",replace:!0,onClick:function(){n({type:"RESET_APP"})}},a.createElement(U,{"aria-hidden":"true"})," ",(0,w.__)("New Migration","wp-migrate-db")))})),ce=n(8096),le=v.ZP.div(re||(re=(0,h.Z)(["\n  animation-duration: ",";\n  animation-timing-function: ",";\n  animation-delay: ",";\n  animation-iteration-count: ",";\n  animation-direction: ",";\n  animation-fill-mode: ",";\n  animation-play-state: ",";\n  display: ",";\n"])),(function(e){return e.duration}),(function(e){return e.timingFunction}),(function(e){return e.delay}),(function(e){return e.iterationCount}),(function(e){return e.direction}),(function(e){return e.fillMode}),(function(e){return e.playState}),(function(e){return e.display}));le.defaultProps={duration:"1s",timingFunction:"ease",delay:"0s",iterationCount:"1",direction:"normal",fillMode:"both",playState:"running",display:"block"};var oe,ue,pe,me=le,de=n(3460),fe=n(62457),_e=n(66866),ge=["children","animation","bgAnimation","setRunning","shouldClose"],be=v.ZP.div(oe||(oe=(0,h.Z)(["\n  position: fixed; /* Stay in place */\n  z-index: 1; /* Sit on top */\n  left: 0;\n  top: 0;\n  display: flex;\n  justify-content: center;\n  width: 100%; /* Full width */\n  height: 100%; /* Full height */\n  overflow: auto; /* Enable scroll if needed */\n  background-color: rgb(0, 0, 0); /* Fallback color */\n  background-color: rgba(0, 0, 0, 0.6); /* Black w/ opacity */\n"]))),he=(0,v.ZP)(me)(ue||(ue=(0,h.Z)(["\n  animation-name: ",";\n  position: absolute;\n"])),(function(e){return e.animation})),ve=(0,v.ZP)(me)(pe||(pe=(0,h.Z)(["\n  animation-name: ",";\n  z-index: 999999;\n  position: relative;\n"])),(function(e){return e.bgAnimation})),Ee=null;(0,_.Yu)()||(Ee=a.lazy((function(){return Promise.all([n.e(532),n.e(965)]).then(n.bind(n,12965))})));var we,ye,xe,ke,Ze,Ne=function(e){var t=e.children,n=e.animation,r=e.bgAnimation,s=e.setRunning,c=e.shouldClose,l=(0,Z.Z)(e,ge),o=l.complete?" migration-complete":"",u=(0,i.I0)();return ce.createPortal(a.createElement(ve,{bgAnimation:r,className:"modal-bg"},a.createElement(be,{onClick:l.onClick},a.createElement(he,{className:"modal-container",duration:"0.8s",delay:"0.2s",animation:n,onAnimationEnd:function(){u(c?s(!1):function(){var e=(0,b.Z)((0,g.Z)().mark((function e(t){return(0,g.Z)().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,t((0,de.Z6)("INITIATE_MIGRATION"));case 3:e.next=9;break;case 5:e.prev=5,e.t0=e.catch(0),console.error(e.t0),t((0,fe.m)({error_type:_e.gF,error_message:e.t0}));case 9:case"end":return e.stop()}}),e,null,[[0,5]])})));return function(t){return e.apply(this,arguments)}}())}},a.createElement(P(),{active:!c},a.createElement("div",{className:"migration-progress".concat(o)},t)),Ee&&a.createElement(a.Suspense,{fallback:a.createElement("div",null,(0,w.__)("Loading...","wp-migrate-db"))},a.createElement(Ee,l))))),document.getElementById("wpmdb-main"))},Pe=(0,v.F4)(we||(we=(0,h.Z)(["\n  from {\n    opacity: 0;\n    padding-top: 0;\n  }\n  to {\n    opacity: 1;\n    padding-top: 64px;\n  }\n"]))),Se=(0,v.F4)(ye||(ye=(0,h.Z)(["\n  from {\n    opacity: 1;\n    padding-top: 64px;\n  }\n  to {\n    opacity: 0;\n    padding-top: 40px;\n  }\n"]))),Te=(0,v.F4)(xe||(xe=(0,h.Z)(["\n  from {\n    opacity: 0;\n  }\n  to {\n    opacity: 1;\n  }\n"]))),Oe=(0,v.F4)(ke||(ke=(0,h.Z)(["\n  from {\n    opacity: 1;\n  }\n  to {\n    opacity: 0;\n  }\n"]))),Ae=n(58900);function Ce(){return Ce=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},Ce.apply(this,arguments)}var Re,Ie=function(e){return a.createElement("svg",Ce({xmlns:"http://www.w3.org/2000/svg",width:52,height:52,viewBox:"0 0 52 52"},e),Ze||(Ze=a.createElement("g",{fill:"none",fillRule:"evenodd"},a.createElement("circle",{cx:26,cy:26,r:26,fill:"#EA4C49",opacity:.202}),a.createElement("circle",{cx:26,cy:26,r:19.067,fill:"#EA4C49"}),a.createElement("rect",{width:3.467,height:12.711,x:24.267,y:16.756,fill:"#FFF",rx:1.733}),a.createElement("circle",{cx:26,cy:33.511,r:1.733,fill:"#FFF"}))))},De=(n.p,n(38068)),Le=n(42222),Me=(0,i.$j)((function(e){var t=(0,J.xg)("stage_size",e),n=(0,J.xg)("timer",e);return{running_size:t,timerOn:n.on,timerTime:n.time,timerStart:n.start}}),{startTimer:De.Xg,stopTimer:De.N6,resetTimer:De.yo})((function(e){var t=e.timerTime,n=(0,Le.i2)(t,!0),r=(0,Le.er)(t,!0),i=(0,Le.$s)(t,!0);return a.createElement("div",{className:"timer"},a.createElement("div",{className:"timer-display"},i," : ",r," : ",n))})),Fe=n(34374),Be=n(15389),je=JSON.parse('{"v":"5.5.4","fr":30,"ip":0,"op":40,"w":140,"h":140,"nm":"Success Checkmark","ddd":0,"assets":[],"layers":[{"ddd":0,"ind":1,"ty":4,"nm":"arrow 8","sr":1,"ks":{"o":{"a":0,"k":100,"ix":11},"r":{"a":0,"k":0,"ix":10,"x":"var $bm_rt;\\n$bm_rt = $bm_div($bm_mul(index, 360), 8);"},"p":{"a":1,"k":[{"i":{"x":0.578,"y":0.611},"o":{"x":0.167,"y":0.167},"t":22,"s":[70,70.031,0],"to":[3.212,-3.212,0],"ti":[-7.639,7.639,0]},{"i":{"x":0.703,"y":1},"o":{"x":0.344,"y":0.514},"t":30,"s":[88.752,49.779,0],"to":[4.253,-4.253,0],"ti":[-1.788,1.788,0]},{"t":37,"s":[100,40.031,0]}],"ix":2},"a":{"a":0,"k":[-3.196,-17.961,0],"ix":1},"s":{"a":0,"k":[67.6,67.6,100],"ix":6}},"ao":0,"shapes":[{"ty":"gr","it":[{"ind":0,"ty":"sh","ix":1,"ks":{"a":0,"k":{"i":[[0,0],[0,0]],"o":[[0,0],[0,0]],"v":[[-3.173,-27.073],[-3.219,-56.647]],"c":false},"ix":2},"nm":"Path 1","mn":"ADBE Vector Shape - Group","hd":false},{"ty":"st","c":{"a":0,"k":[0.137254901961,0.427450980392,0.905882352941,1],"ix":3},"o":{"a":0,"k":100,"ix":4},"w":{"a":0,"k":3,"ix":5},"lc":2,"lj":2,"bm":0,"nm":"Stroke 1","mn":"ADBE Vector Graphic - Stroke","hd":false},{"ty":"fl","c":{"a":0,"k":[0.137254901961,0.427451010311,0.90588241278,1],"ix":4},"o":{"a":0,"k":100,"ix":5},"r":1,"bm":0,"nm":"Fill 1","mn":"ADBE Vector Graphic - Fill","hd":false},{"ty":"tr","p":{"a":0,"k":[0,0],"ix":2},"a":{"a":0,"k":[0,0],"ix":1},"s":{"a":0,"k":[100,100],"ix":3},"r":{"a":0,"k":0,"ix":6},"o":{"a":0,"k":100,"ix":7},"sk":{"a":0,"k":0,"ix":4},"sa":{"a":0,"k":0,"ix":5},"nm":"Transform"}],"nm":"Shape 1","np":3,"cix":2,"bm":0,"ix":1,"mn":"ADBE Vector Group","hd":false},{"ty":"tm","s":{"a":1,"k":[{"i":{"x":[0.833],"y":[0.833]},"o":{"x":[0.167],"y":[0.167]},"t":29,"s":[35]},{"i":{"x":[0.833],"y":[0.833]},"o":{"x":[0.167],"y":[0.167]},"t":33,"s":[100]},{"i":{"x":[0.833],"y":[0.833]},"o":{"x":[0.167],"y":[0.167]},"t":36,"s":[100]},{"t":37,"s":[70]}],"ix":1},"e":{"a":1,"k":[{"i":{"x":[0.833],"y":[0.833]},"o":{"x":[0.167],"y":[0.167]},"t":33,"s":[0]},{"t":37,"s":[79]}],"ix":2},"o":{"a":0,"k":0,"ix":3},"m":1,"ix":2,"nm":"Trim Paths 1","mn":"ADBE Vector Filter - Trim","hd":false}],"ip":22,"op":37,"st":-3,"bm":0},{"ddd":0,"ind":2,"ty":4,"nm":"arrow 7","sr":1,"ks":{"o":{"a":0,"k":100,"ix":11},"r":{"a":0,"k":0,"ix":10,"x":"var $bm_rt;\\n$bm_rt = $bm_div($bm_mul(index, 360), 8);"},"p":{"a":1,"k":[{"i":{"x":0.578,"y":0.603},"o":{"x":0.167,"y":0.167},"t":22,"s":[70,70.031,0],"to":[4.282,0,0],"ti":[-10.185,0,0]},{"i":{"x":0.703,"y":1},"o":{"x":0.344,"y":0.498},"t":30,"s":[95.503,70.031,0],"to":[5.671,0,0],"ti":[-2.384,0,0]},{"t":37,"s":[110,70.031,0]}],"ix":2},"a":{"a":0,"k":[-3.196,-17.961,0],"ix":1},"s":{"a":0,"k":[67.6,67.6,100],"ix":6}},"ao":0,"shapes":[{"ty":"gr","it":[{"ind":0,"ty":"sh","ix":1,"ks":{"a":0,"k":{"i":[[0,0],[0,0]],"o":[[0,0],[0,0]],"v":[[-3.173,-27.073],[-3.219,-56.647]],"c":false},"ix":2},"nm":"Path 1","mn":"ADBE Vector Shape - Group","hd":false},{"ty":"st","c":{"a":0,"k":[0.137254901961,0.427450980392,0.905882352941,1],"ix":3},"o":{"a":0,"k":100,"ix":4},"w":{"a":0,"k":3,"ix":5},"lc":2,"lj":2,"bm":0,"nm":"Stroke 1","mn":"ADBE Vector Graphic - Stroke","hd":false},{"ty":"fl","c":{"a":0,"k":[0.137254901961,0.427451010311,0.90588241278,1],"ix":4},"o":{"a":0,"k":100,"ix":5},"r":1,"bm":0,"nm":"Fill 1","mn":"ADBE Vector Graphic - Fill","hd":false},{"ty":"tr","p":{"a":0,"k":[0,0],"ix":2},"a":{"a":0,"k":[0,0],"ix":1},"s":{"a":0,"k":[100,100],"ix":3},"r":{"a":0,"k":0,"ix":6},"o":{"a":0,"k":100,"ix":7},"sk":{"a":0,"k":0,"ix":4},"sa":{"a":0,"k":0,"ix":5},"nm":"Transform"}],"nm":"Shape 1","np":3,"cix":2,"bm":0,"ix":1,"mn":"ADBE Vector Group","hd":false},{"ty":"tm","s":{"a":0,"k":0,"ix":1},"e":{"a":0,"k":100,"ix":2},"o":{"a":0,"k":0,"ix":3},"m":1,"ix":2,"nm":"Trim Paths 1","mn":"ADBE Vector Filter - Trim","hd":false},{"ty":"tm","s":{"a":1,"k":[{"i":{"x":[0.833],"y":[0.833]},"o":{"x":[0.167],"y":[0.167]},"t":29,"s":[35]},{"i":{"x":[0.833],"y":[0.833]},"o":{"x":[0.167],"y":[0.167]},"t":33,"s":[100]},{"i":{"x":[0.833],"y":[0.833]},"o":{"x":[0.167],"y":[0.167]},"t":36,"s":[100]},{"t":37,"s":[70]}],"ix":1},"e":{"a":1,"k":[{"i":{"x":[0.833],"y":[0.833]},"o":{"x":[0.167],"y":[0.167]},"t":33,"s":[0]},{"t":37,"s":[79]}],"ix":2},"o":{"a":0,"k":0,"ix":3},"m":1,"ix":3,"nm":"Trim Paths 2","mn":"ADBE Vector Filter - Trim","hd":false}],"ip":22,"op":37,"st":0,"bm":0},{"ddd":0,"ind":3,"ty":4,"nm":"arrow 6","sr":1,"ks":{"o":{"a":0,"k":100,"ix":11},"r":{"a":0,"k":0,"ix":10,"x":"var $bm_rt;\\n$bm_rt = $bm_div($bm_mul(index, 360), 8);"},"p":{"a":1,"k":[{"i":{"x":0.578,"y":0.611},"o":{"x":0.167,"y":0.167},"t":22,"s":[70,70.031,0],"to":[3.212,3.212,0],"ti":[-7.639,-7.639,0]},{"i":{"x":0.703,"y":1},"o":{"x":0.344,"y":0.514},"t":30,"s":[88.752,90.283,0],"to":[4.253,4.253,0],"ti":[-1.788,-1.788,0]},{"t":37,"s":[100,100.031,0]}],"ix":2},"a":{"a":0,"k":[-3.196,-17.961,0],"ix":1},"s":{"a":0,"k":[67.6,67.6,100],"ix":6}},"ao":0,"shapes":[{"ty":"gr","it":[{"ind":0,"ty":"sh","ix":1,"ks":{"a":0,"k":{"i":[[0,0],[0,0]],"o":[[0,0],[0,0]],"v":[[-3.173,-27.073],[-3.219,-56.647]],"c":false},"ix":2},"nm":"Path 1","mn":"ADBE Vector Shape - Group","hd":false},{"ty":"st","c":{"a":0,"k":[0.137254901961,0.427450980392,0.905882352941,1],"ix":3},"o":{"a":0,"k":100,"ix":4},"w":{"a":0,"k":3,"ix":5},"lc":2,"lj":2,"bm":0,"nm":"Stroke 1","mn":"ADBE Vector Graphic - Stroke","hd":false},{"ty":"fl","c":{"a":0,"k":[0.137254901961,0.427451010311,0.90588241278,1],"ix":4},"o":{"a":0,"k":100,"ix":5},"r":1,"bm":0,"nm":"Fill 1","mn":"ADBE Vector Graphic - Fill","hd":false},{"ty":"tr","p":{"a":0,"k":[0,0],"ix":2},"a":{"a":0,"k":[0,0],"ix":1},"s":{"a":0,"k":[100,100],"ix":3},"r":{"a":0,"k":0,"ix":6},"o":{"a":0,"k":100,"ix":7},"sk":{"a":0,"k":0,"ix":4},"sa":{"a":0,"k":0,"ix":5},"nm":"Transform"}],"nm":"Shape 1","np":3,"cix":2,"bm":0,"ix":1,"mn":"ADBE Vector Group","hd":false},{"ty":"tm","s":{"a":0,"k":0,"ix":1},"e":{"a":0,"k":100,"ix":2},"o":{"a":0,"k":0,"ix":3},"m":1,"ix":2,"nm":"Trim Paths 1","mn":"ADBE Vector Filter - Trim","hd":false},{"ty":"tm","s":{"a":1,"k":[{"i":{"x":[0.833],"y":[0.833]},"o":{"x":[0.167],"y":[0.167]},"t":29,"s":[35]},{"i":{"x":[0.833],"y":[0.833]},"o":{"x":[0.167],"y":[0.167]},"t":33,"s":[100]},{"i":{"x":[0.833],"y":[0.833]},"o":{"x":[0.167],"y":[0.167]},"t":36,"s":[100]},{"t":37,"s":[70]}],"ix":1},"e":{"a":1,"k":[{"i":{"x":[0.833],"y":[0.833]},"o":{"x":[0.167],"y":[0.167]},"t":33,"s":[0]},{"t":37,"s":[79]}],"ix":2},"o":{"a":0,"k":0,"ix":3},"m":1,"ix":3,"nm":"Trim Paths 2","mn":"ADBE Vector Filter - Trim","hd":false}],"ip":22,"op":37,"st":0,"bm":0},{"ddd":0,"ind":4,"ty":4,"nm":"arrow 5","sr":1,"ks":{"o":{"a":0,"k":100,"ix":11},"r":{"a":0,"k":0,"ix":10,"x":"var $bm_rt;\\n$bm_rt = $bm_div($bm_mul(index, 360), 8);"},"p":{"a":1,"k":[{"i":{"x":0.578,"y":0.625},"o":{"x":0.167,"y":0.167},"t":22,"s":[70,70.031,0],"to":[0,4.282,0],"ti":[0,-10.185,0]},{"i":{"x":0.703,"y":1},"o":{"x":0.344,"y":0.556},"t":30,"s":[70,97.034,0],"to":[0,5.671,0],"ti":[0,-2.384,0]},{"t":37,"s":[70,110.031,0]}],"ix":2},"a":{"a":0,"k":[-3.196,-17.961,0],"ix":1},"s":{"a":0,"k":[67.6,67.6,100],"ix":6}},"ao":0,"shapes":[{"ty":"gr","it":[{"ind":0,"ty":"sh","ix":1,"ks":{"a":0,"k":{"i":[[0,0],[0,0]],"o":[[0,0],[0,0]],"v":[[-3.173,-27.073],[-3.219,-56.647]],"c":false},"ix":2},"nm":"Path 1","mn":"ADBE Vector Shape - Group","hd":false},{"ty":"st","c":{"a":0,"k":[0.137254901961,0.427450980392,0.905882352941,1],"ix":3},"o":{"a":0,"k":100,"ix":4},"w":{"a":0,"k":3,"ix":5},"lc":2,"lj":2,"bm":0,"nm":"Stroke 1","mn":"ADBE Vector Graphic - Stroke","hd":false},{"ty":"fl","c":{"a":0,"k":[0.137254901961,0.427451010311,0.90588241278,1],"ix":4},"o":{"a":0,"k":100,"ix":5},"r":1,"bm":0,"nm":"Fill 1","mn":"ADBE Vector Graphic - Fill","hd":false},{"ty":"tr","p":{"a":0,"k":[0,0],"ix":2},"a":{"a":0,"k":[0,0],"ix":1},"s":{"a":0,"k":[100,100],"ix":3},"r":{"a":0,"k":0,"ix":6},"o":{"a":0,"k":100,"ix":7},"sk":{"a":0,"k":0,"ix":4},"sa":{"a":0,"k":0,"ix":5},"nm":"Transform"}],"nm":"Shape 1","np":3,"cix":2,"bm":0,"ix":1,"mn":"ADBE Vector Group","hd":false},{"ty":"tm","s":{"a":0,"k":0,"ix":1},"e":{"a":0,"k":100,"ix":2},"o":{"a":0,"k":0,"ix":3},"m":1,"ix":2,"nm":"Trim Paths 1","mn":"ADBE Vector Filter - Trim","hd":false},{"ty":"tm","s":{"a":1,"k":[{"i":{"x":[0.833],"y":[0.833]},"o":{"x":[0.167],"y":[0.167]},"t":29,"s":[35]},{"i":{"x":[0.833],"y":[0.833]},"o":{"x":[0.167],"y":[0.167]},"t":33,"s":[100]},{"i":{"x":[0.833],"y":[0.833]},"o":{"x":[0.167],"y":[0.167]},"t":36,"s":[100]},{"t":37,"s":[70]}],"ix":1},"e":{"a":1,"k":[{"i":{"x":[0.833],"y":[0.833]},"o":{"x":[0.167],"y":[0.167]},"t":33,"s":[0]},{"t":37,"s":[79]}],"ix":2},"o":{"a":0,"k":0,"ix":3},"m":1,"ix":3,"nm":"Trim Paths 2","mn":"ADBE Vector Filter - Trim","hd":false}],"ip":22,"op":37,"st":0,"bm":0},{"ddd":0,"ind":5,"ty":4,"nm":"arrow 4","sr":1,"ks":{"o":{"a":0,"k":100,"ix":11},"r":{"a":0,"k":0,"ix":10,"x":"var $bm_rt;\\n$bm_rt = $bm_div($bm_mul(index, 360), 8);"},"p":{"a":1,"k":[{"i":{"x":0.578,"y":0.642},"o":{"x":0.167,"y":0.167},"t":22,"s":[70,70.031,0],"to":[-2.944,2.409,0],"ti":[7.002,-5.729,0]},{"i":{"x":0.703,"y":1},"o":{"x":0.344,"y":0.614},"t":30,"s":[49.936,85.22,0],"to":[-3.899,3.19,0],"ti":[1.639,-1.341,0]},{"t":37,"s":[42.5,92.531,0]}],"ix":2},"a":{"a":0,"k":[-3.196,-17.961,0],"ix":1},"s":{"a":0,"k":[67.6,67.6,100],"ix":6}},"ao":0,"shapes":[{"ty":"gr","it":[{"ind":0,"ty":"sh","ix":1,"ks":{"a":0,"k":{"i":[[0,0],[0,0]],"o":[[0,0],[0,0]],"v":[[-3.173,-27.073],[-3.219,-56.647]],"c":false},"ix":2},"nm":"Path 1","mn":"ADBE Vector Shape - Group","hd":false},{"ty":"st","c":{"a":0,"k":[0.137254901961,0.427450980392,0.905882352941,1],"ix":3},"o":{"a":0,"k":100,"ix":4},"w":{"a":0,"k":3,"ix":5},"lc":2,"lj":2,"bm":0,"nm":"Stroke 1","mn":"ADBE Vector Graphic - Stroke","hd":false},{"ty":"fl","c":{"a":0,"k":[0.137254901961,0.427451010311,0.90588241278,1],"ix":4},"o":{"a":0,"k":100,"ix":5},"r":1,"bm":0,"nm":"Fill 1","mn":"ADBE Vector Graphic - Fill","hd":false},{"ty":"tr","p":{"a":0,"k":[0,0],"ix":2},"a":{"a":0,"k":[0,0],"ix":1},"s":{"a":0,"k":[100,100],"ix":3},"r":{"a":0,"k":0,"ix":6},"o":{"a":0,"k":100,"ix":7},"sk":{"a":0,"k":0,"ix":4},"sa":{"a":0,"k":0,"ix":5},"nm":"Transform"}],"nm":"Shape 1","np":3,"cix":2,"bm":0,"ix":1,"mn":"ADBE Vector Group","hd":false},{"ty":"tm","s":{"a":1,"k":[{"i":{"x":[0.833],"y":[0.833]},"o":{"x":[0.167],"y":[0.167]},"t":29,"s":[35]},{"i":{"x":[0.833],"y":[0.833]},"o":{"x":[0.167],"y":[0.167]},"t":33,"s":[100]},{"i":{"x":[0.833],"y":[0.833]},"o":{"x":[0.167],"y":[0.167]},"t":36,"s":[100]},{"t":37,"s":[70]}],"ix":1},"e":{"a":1,"k":[{"i":{"x":[0.833],"y":[0.833]},"o":{"x":[0.167],"y":[0.167]},"t":33,"s":[0]},{"t":37,"s":[79]}],"ix":2},"o":{"a":0,"k":0,"ix":3},"m":1,"ix":2,"nm":"Trim Paths 1","mn":"ADBE Vector Filter - Trim","hd":false}],"ip":22,"op":37,"st":0,"bm":0},{"ddd":0,"ind":6,"ty":4,"nm":"arrow 3","sr":1,"ks":{"o":{"a":0,"k":100,"ix":11},"r":{"a":0,"k":0,"ix":10,"x":"var $bm_rt;\\n$bm_rt = $bm_div($bm_mul(index, 360), 8);"},"p":{"a":1,"k":[{"i":{"x":0.578,"y":0.645},"o":{"x":0.167,"y":0.167},"t":22,"s":[70,70.031,0],"to":[-4.282,0,0],"ti":[10.185,0,0]},{"i":{"x":0.703,"y":1},"o":{"x":0.344,"y":0.628},"t":30,"s":[41.497,70.031,0],"to":[-5.671,0,0],"ti":[2.384,0,0]},{"t":37,"s":[30,70.031,0]}],"ix":2},"a":{"a":0,"k":[-3.196,-17.961,0],"ix":1},"s":{"a":0,"k":[67.6,67.6,100],"ix":6}},"ao":0,"shapes":[{"ty":"gr","it":[{"ind":0,"ty":"sh","ix":1,"ks":{"a":0,"k":{"i":[[0,0],[0,0]],"o":[[0,0],[0,0]],"v":[[-3.173,-27.073],[-3.219,-56.647]],"c":false},"ix":2},"nm":"Path 1","mn":"ADBE Vector Shape - Group","hd":false},{"ty":"st","c":{"a":0,"k":[0.137254901961,0.427450980392,0.905882352941,1],"ix":3},"o":{"a":0,"k":100,"ix":4},"w":{"a":0,"k":3,"ix":5},"lc":2,"lj":2,"bm":0,"nm":"Stroke 1","mn":"ADBE Vector Graphic - Stroke","hd":false},{"ty":"fl","c":{"a":0,"k":[0.137254901961,0.427451010311,0.90588241278,1],"ix":4},"o":{"a":0,"k":100,"ix":5},"r":1,"bm":0,"nm":"Fill 1","mn":"ADBE Vector Graphic - Fill","hd":false},{"ty":"tr","p":{"a":0,"k":[0,0],"ix":2},"a":{"a":0,"k":[0,0],"ix":1},"s":{"a":0,"k":[100,100],"ix":3},"r":{"a":0,"k":0,"ix":6},"o":{"a":0,"k":100,"ix":7},"sk":{"a":0,"k":0,"ix":4},"sa":{"a":0,"k":0,"ix":5},"nm":"Transform"}],"nm":"Shape 1","np":3,"cix":2,"bm":0,"ix":1,"mn":"ADBE Vector Group","hd":false},{"ty":"tm","s":{"a":0,"k":0,"ix":1},"e":{"a":0,"k":100,"ix":2},"o":{"a":0,"k":0,"ix":3},"m":1,"ix":2,"nm":"Trim Paths 1","mn":"ADBE Vector Filter - Trim","hd":false},{"ty":"tm","s":{"a":1,"k":[{"i":{"x":[0.833],"y":[0.833]},"o":{"x":[0.167],"y":[0.167]},"t":29,"s":[35]},{"i":{"x":[0.833],"y":[0.833]},"o":{"x":[0.167],"y":[0.167]},"t":33,"s":[100]},{"i":{"x":[0.833],"y":[0.833]},"o":{"x":[0.167],"y":[0.167]},"t":36,"s":[100]},{"t":37,"s":[70]}],"ix":1},"e":{"a":1,"k":[{"i":{"x":[0.833],"y":[0.833]},"o":{"x":[0.167],"y":[0.167]},"t":33,"s":[0]},{"t":37,"s":[79]}],"ix":2},"o":{"a":0,"k":0,"ix":3},"m":1,"ix":3,"nm":"Trim Paths 2","mn":"ADBE Vector Filter - Trim","hd":false}],"ip":22,"op":37,"st":0,"bm":0},{"ddd":0,"ind":7,"ty":4,"nm":"arrow 2","sr":1,"ks":{"o":{"a":0,"k":100,"ix":11},"r":{"a":0,"k":0,"ix":10,"x":"var $bm_rt;\\n$bm_rt = $bm_div($bm_mul(index, 360), 8);"},"p":{"a":1,"k":[{"i":{"x":0.578,"y":0.638},"o":{"x":0.167,"y":0.167},"t":22,"s":[70,70.031,0],"to":[-3.212,-3.212,0],"ti":[7.639,7.639,0]},{"i":{"x":0.703,"y":1},"o":{"x":0.344,"y":0.599},"t":30,"s":[48.248,49.779,0],"to":[-4.253,-4.253,0],"ti":[1.788,1.788,0]},{"t":37,"s":[40,40.031,0]}],"ix":2},"a":{"a":0,"k":[-3.196,-17.961,0],"ix":1},"s":{"a":0,"k":[67.6,67.6,100],"ix":6}},"ao":0,"shapes":[{"ty":"gr","it":[{"ind":0,"ty":"sh","ix":1,"ks":{"a":0,"k":{"i":[[0,0],[0,0]],"o":[[0,0],[0,0]],"v":[[-3.173,-27.073],[-3.219,-56.647]],"c":false},"ix":2},"nm":"Path 1","mn":"ADBE Vector Shape - Group","hd":false},{"ty":"st","c":{"a":0,"k":[0.137254901961,0.427450980392,0.905882352941,1],"ix":3},"o":{"a":0,"k":100,"ix":4},"w":{"a":0,"k":3,"ix":5},"lc":2,"lj":2,"bm":0,"nm":"Stroke 1","mn":"ADBE Vector Graphic - Stroke","hd":false},{"ty":"fl","c":{"a":0,"k":[0.137254901961,0.427451010311,0.90588241278,1],"ix":4},"o":{"a":0,"k":100,"ix":5},"r":1,"bm":0,"nm":"Fill 1","mn":"ADBE Vector Graphic - Fill","hd":false},{"ty":"tr","p":{"a":0,"k":[0,0],"ix":2},"a":{"a":0,"k":[0,0],"ix":1},"s":{"a":0,"k":[100,100],"ix":3},"r":{"a":0,"k":0,"ix":6},"o":{"a":0,"k":100,"ix":7},"sk":{"a":0,"k":0,"ix":4},"sa":{"a":0,"k":0,"ix":5},"nm":"Transform"}],"nm":"Shape 1","np":3,"cix":2,"bm":0,"ix":1,"mn":"ADBE Vector Group","hd":false},{"ty":"tm","s":{"a":0,"k":0,"ix":1},"e":{"a":0,"k":100,"ix":2},"o":{"a":0,"k":0,"ix":3},"m":1,"ix":2,"nm":"Trim Paths 1","mn":"ADBE Vector Filter - Trim","hd":false},{"ty":"tm","s":{"a":1,"k":[{"i":{"x":[0.833],"y":[0.833]},"o":{"x":[0.167],"y":[0.167]},"t":29,"s":[35]},{"i":{"x":[0.833],"y":[0.833]},"o":{"x":[0.167],"y":[0.167]},"t":33,"s":[100]},{"i":{"x":[0.833],"y":[0.833]},"o":{"x":[0.167],"y":[0.167]},"t":36,"s":[100]},{"t":37,"s":[70]}],"ix":1},"e":{"a":1,"k":[{"i":{"x":[0.833],"y":[0.833]},"o":{"x":[0.167],"y":[0.167]},"t":33,"s":[0]},{"t":37,"s":[79]}],"ix":2},"o":{"a":0,"k":0,"ix":3},"m":1,"ix":3,"nm":"Trim Paths 2","mn":"ADBE Vector Filter - Trim","hd":false}],"ip":22,"op":37,"st":0,"bm":0},{"ddd":0,"ind":8,"ty":4,"nm":"arrow 1","sr":1,"ks":{"o":{"a":0,"k":100,"ix":11},"r":{"a":0,"k":0,"ix":10,"x":"var $bm_rt;\\n$bm_rt = $bm_div($bm_mul(index, 360), 8);"},"p":{"a":1,"k":[{"i":{"x":0.578,"y":0.614},"o":{"x":0.167,"y":0.167},"t":22,"s":[70,69.281,0],"to":[0,-4.283,0],"ti":[0,10.186,0]},{"i":{"x":0.703,"y":1},"o":{"x":0.344,"y":0.556},"t":30,"s":[69.75,43.025,0],"to":[0,-5.669,0],"ti":[0,2.384,0]},{"t":37,"s":[70,30.031,0]}],"ix":2},"a":{"a":0,"k":[-3.196,-17.961,0],"ix":1},"s":{"a":0,"k":[67.6,67.6,100],"ix":6}},"ao":0,"shapes":[{"ty":"gr","it":[{"ind":0,"ty":"sh","ix":1,"ks":{"a":0,"k":{"i":[[0,0],[0,0]],"o":[[0,0],[0,0]],"v":[[-3.173,-27.073],[-3.219,-56.647]],"c":false},"ix":2},"nm":"Path 1","mn":"ADBE Vector Shape - Group","hd":false},{"ty":"st","c":{"a":0,"k":[0.137254901961,0.427450980392,0.905882352941,1],"ix":3},"o":{"a":0,"k":100,"ix":4},"w":{"a":0,"k":3,"ix":5},"lc":2,"lj":2,"bm":0,"nm":"Stroke 1","mn":"ADBE Vector Graphic - Stroke","hd":false},{"ty":"fl","c":{"a":0,"k":[0.137254901961,0.427451010311,0.90588241278,1],"ix":4},"o":{"a":0,"k":100,"ix":5},"r":1,"bm":0,"nm":"Fill 1","mn":"ADBE Vector Graphic - Fill","hd":false},{"ty":"tr","p":{"a":0,"k":[0,0],"ix":2},"a":{"a":0,"k":[0,0],"ix":1},"s":{"a":0,"k":[100,100],"ix":3},"r":{"a":0,"k":0,"ix":6},"o":{"a":0,"k":100,"ix":7},"sk":{"a":0,"k":0,"ix":4},"sa":{"a":0,"k":0,"ix":5},"nm":"Transform"}],"nm":"Shape 1","np":3,"cix":2,"bm":0,"ix":1,"mn":"ADBE Vector Group","hd":false},{"ty":"tm","s":{"a":0,"k":0,"ix":1},"e":{"a":0,"k":100,"ix":2},"o":{"a":0,"k":0,"ix":3},"m":1,"ix":2,"nm":"Trim Paths 1","mn":"ADBE Vector Filter - Trim","hd":false},{"ty":"tm","s":{"a":1,"k":[{"i":{"x":[0.833],"y":[0.833]},"o":{"x":[0.167],"y":[0.167]},"t":29,"s":[35]},{"i":{"x":[0.833],"y":[0.833]},"o":{"x":[0.167],"y":[0.167]},"t":33,"s":[100]},{"i":{"x":[0.833],"y":[0.833]},"o":{"x":[0.167],"y":[0.167]},"t":36,"s":[100]},{"t":37,"s":[70]}],"ix":1},"e":{"a":1,"k":[{"i":{"x":[0.833],"y":[0.833]},"o":{"x":[0.167],"y":[0.167]},"t":33,"s":[0]},{"t":37,"s":[79]}],"ix":2},"o":{"a":0,"k":0,"ix":3},"m":1,"ix":3,"nm":"Trim Paths 2","mn":"ADBE Vector Filter - Trim","hd":false}],"ip":22,"op":37,"st":0,"bm":0},{"ddd":0,"ind":9,"ty":4,"nm":"Glow","sr":1,"ks":{"o":{"a":1,"k":[{"i":{"x":[0.833],"y":[0.833]},"o":{"x":[0.167],"y":[0.167]},"t":0,"s":[0]},{"i":{"x":[0.833],"y":[0.833]},"o":{"x":[0.167],"y":[0.167]},"t":2,"s":[10]},{"i":{"x":[0.833],"y":[0.833]},"o":{"x":[0.167],"y":[0.167]},"t":22,"s":[50]},{"t":24,"s":[0]}],"ix":11},"r":{"a":0,"k":0,"ix":10},"p":{"a":0,"k":[67.13,75.69,0],"ix":2},"a":{"a":0,"k":[0,0,0],"ix":1},"s":{"a":1,"k":[{"i":{"x":[0.689,0.689,0.689],"y":[1,1,1]},"o":{"x":[0.254,0.254,0.254],"y":[0.254,0.254,10.184]},"t":0,"s":[25,25,100]},{"i":{"x":[0.833,0.833,0.833],"y":[0.833,0.833,0.833]},"o":{"x":[0.167,0.167,0.167],"y":[0.167,0.167,0.167]},"t":18,"s":[150,150,100]},{"t":24,"s":[126,126,100]}],"ix":6}},"ao":0,"shapes":[{"ty":"gr","it":[{"d":1,"ty":"el","s":{"a":0,"k":[63.763,63.763],"ix":2},"p":{"a":0,"k":[0,0],"ix":3},"nm":"Ellipse Path 1","mn":"ADBE Vector Shape - Ellipse","hd":false},{"ty":"st","c":{"a":0,"k":[0.137254901961,0.427450980392,0.905882352941,1],"ix":3},"o":{"a":0,"k":100,"ix":4},"w":{"a":1,"k":[{"i":{"x":[0.833],"y":[0.833]},"o":{"x":[0.167],"y":[0.167]},"t":0,"s":[60]},{"i":{"x":[0.833],"y":[0.833]},"o":{"x":[0.167],"y":[0.167]},"t":6,"s":[50]},{"t":24,"s":[1]}],"ix":5},"lc":1,"lj":1,"ml":4,"bm":0,"nm":"Stroke 1","mn":"ADBE Vector Graphic - Stroke","hd":false},{"ty":"tr","p":{"a":0,"k":[1.881,-4.119],"ix":2},"a":{"a":0,"k":[0,0],"ix":1},"s":{"a":0,"k":[100,100],"ix":3},"r":{"a":0,"k":0,"ix":6},"o":{"a":0,"k":100,"ix":7},"sk":{"a":0,"k":0,"ix":4},"sa":{"a":0,"k":0,"ix":5},"nm":"Transform"}],"nm":"Ellipse 1","np":3,"cix":2,"bm":0,"ix":1,"mn":"ADBE Vector Group","hd":false}],"ip":0,"op":40,"st":0,"bm":0},{"ddd":0,"ind":10,"ty":4,"nm":"Check Mark","sr":1,"ks":{"o":{"a":0,"k":100,"ix":11},"r":{"a":0,"k":0,"ix":10},"p":{"a":0,"k":[70,70,0],"ix":2},"a":{"a":0,"k":[-1.312,6,0],"ix":1},"s":{"a":0,"k":[100,100,100],"ix":6}},"ao":0,"shapes":[{"ty":"gr","it":[{"ind":0,"ty":"sh","ix":1,"ks":{"a":0,"k":{"i":[[0,0],[0,0],[0,0]],"o":[[0,0],[0,0],[0,0]],"v":[[-15.75,8],[-8,16],[13.125,-4]],"c":false},"ix":2},"nm":"Path 1","mn":"ADBE Vector Shape - Group","hd":false},{"ty":"tm","s":{"a":1,"k":[{"i":{"x":[0.667],"y":[1]},"o":{"x":[0.333],"y":[0]},"t":25,"s":[0]},{"t":33,"s":[100]}],"ix":1},"e":{"a":0,"k":0,"ix":2},"o":{"a":0,"k":0,"ix":3},"m":1,"ix":2,"nm":"Trim Paths 1","mn":"ADBE Vector Filter - Trim","hd":false},{"ty":"st","c":{"a":0,"k":[1,1,1,1],"ix":3},"o":{"a":0,"k":100,"ix":4},"w":{"a":0,"k":3,"ix":5},"lc":2,"lj":2,"bm":0,"nm":"Stroke 1","mn":"ADBE Vector Graphic - Stroke","hd":false},{"ty":"tr","p":{"a":0,"k":[0,0],"ix":2},"a":{"a":0,"k":[0,0],"ix":1},"s":{"a":0,"k":[100,100],"ix":3},"r":{"a":0,"k":0,"ix":6},"o":{"a":0,"k":100,"ix":7},"sk":{"a":0,"k":0,"ix":4},"sa":{"a":0,"k":0,"ix":5},"nm":"Transform"}],"nm":"Shape 1","np":3,"cix":2,"bm":0,"ix":1,"mn":"ADBE Vector Group","hd":false}],"ip":0,"op":40,"st":0,"bm":0},{"ddd":0,"ind":11,"ty":4,"nm":"Circle Flash","sr":1,"ks":{"o":{"a":1,"k":[{"i":{"x":[0.833],"y":[0.833]},"o":{"x":[0.167],"y":[0.167]},"t":25,"s":[0]},{"i":{"x":[0.833],"y":[0.833]},"o":{"x":[0.167],"y":[0.167]},"t":30,"s":[98]},{"t":38,"s":[0]}],"ix":11},"r":{"a":0,"k":0,"ix":10},"p":{"a":0,"k":[70,70,0],"ix":2},"a":{"a":0,"k":[0,0,0],"ix":1},"s":{"a":1,"k":[{"i":{"x":[0.667,0.667,0.667],"y":[1,1,1]},"o":{"x":[0.333,0.333,0.333],"y":[0,0,0]},"t":25,"s":[0,0,100]},{"t":30,"s":[100,100,100]}],"ix":6}},"ao":0,"shapes":[{"d":1,"ty":"el","s":{"a":0,"k":[64,64],"ix":2},"p":{"a":0,"k":[0,0],"ix":3},"nm":"Ellipse Path 1","mn":"ADBE Vector Shape - Ellipse","hd":false},{"ty":"fl","c":{"a":0,"k":[0.137254901961,0.427450980392,0.905882352941,1],"ix":4},"o":{"a":0,"k":100,"ix":5},"r":1,"bm":0,"nm":"Fill 1","mn":"ADBE Vector Graphic - Fill","hd":false}],"ip":0,"op":40,"st":0,"bm":0},{"ddd":0,"ind":12,"ty":4,"nm":"Circle Stroke","sr":1,"ks":{"o":{"a":0,"k":100,"ix":11},"r":{"a":0,"k":0,"ix":10},"p":{"a":0,"k":[68.925,68.925,0],"ix":2},"a":{"a":0,"k":[0,0,0],"ix":1},"s":{"a":1,"k":[{"i":{"x":[0.667,0.667,0.667],"y":[1,1,1]},"o":{"x":[0.333,0.333,0.333],"y":[0,0,0]},"t":16,"s":[100,100,100]},{"i":{"x":[0.667,0.667,0.667],"y":[1,1,1]},"o":{"x":[0.333,0.333,0.333],"y":[0,0,0]},"t":22,"s":[80,80,100]},{"i":{"x":[0.667,0.667,0.667],"y":[1,1,1]},"o":{"x":[0.333,0.333,0.333],"y":[0,0,0]},"t":25,"s":[120,120,100]},{"t":29,"s":[100,100,100]}],"ix":6}},"ao":0,"shapes":[{"ty":"gr","it":[{"d":1,"ty":"el","s":{"a":0,"k":[60,60],"ix":2},"p":{"a":0,"k":[0,0],"ix":3},"nm":"Ellipse Path 1","mn":"ADBE Vector Shape - Ellipse","hd":false},{"ty":"tm","s":{"a":1,"k":[{"i":{"x":[0.667],"y":[1]},"o":{"x":[0.333],"y":[0]},"t":0,"s":[0]},{"t":16,"s":[100]}],"ix":1},"e":{"a":0,"k":0,"ix":2},"o":{"a":0,"k":0,"ix":3},"m":1,"ix":2,"nm":"Trim Paths 1","mn":"ADBE Vector Filter - Trim","hd":false},{"ty":"st","c":{"a":0,"k":[0.137254901961,0.427450980392,0.905882352941,1],"ix":3},"o":{"a":0,"k":100,"ix":4},"w":{"a":0,"k":3,"ix":5},"lc":2,"lj":2,"bm":0,"nm":"Stroke 1","mn":"ADBE Vector Graphic - Stroke","hd":false},{"ty":"tr","p":{"a":0,"k":[0.978,0.978],"ix":2},"a":{"a":0,"k":[0,0],"ix":1},"s":{"a":0,"k":[100,100],"ix":3},"r":{"a":0,"k":0,"ix":6},"o":{"a":0,"k":100,"ix":7},"sk":{"a":0,"k":0,"ix":4},"sa":{"a":0,"k":0,"ix":5},"nm":"Transform"}],"nm":"Ellipse 1","np":3,"cix":2,"bm":0,"ix":1,"mn":"ADBE Vector Group","hd":false}],"ip":0,"op":40,"st":0,"bm":0},{"ddd":0,"ind":13,"ty":4,"nm":"Circle Fill","sr":1,"ks":{"o":{"a":1,"k":[{"i":{"x":[0.833],"y":[0.833]},"o":{"x":[0.167],"y":[0.167]},"t":21,"s":[0]},{"t":28,"s":[98]}],"ix":11},"r":{"a":0,"k":0,"ix":10},"p":{"a":0,"k":[70,70,0],"ix":2},"a":{"a":0,"k":[0,0,0],"ix":1},"s":{"a":1,"k":[{"i":{"x":[0.667,0.667,0.667],"y":[1,1,1]},"o":{"x":[0.333,0.333,0.333],"y":[0,0,0]},"t":21,"s":[0,0,100]},{"t":28,"s":[100,100,100]}],"ix":6}},"ao":0,"shapes":[{"d":1,"ty":"el","s":{"a":0,"k":[64,64],"ix":2},"p":{"a":0,"k":[0,0],"ix":3},"nm":"Ellipse Path 1","mn":"ADBE Vector Shape - Ellipse","hd":false},{"ty":"fl","c":{"a":0,"k":[0.137254908681,0.427450984716,0.905882358551,1],"ix":4},"o":{"a":0,"k":100,"ix":5},"r":1,"bm":0,"nm":"Fill 1","mn":"ADBE Vector Graphic - Fill","hd":false}],"ip":0,"op":40,"st":0,"bm":0}],"markers":[]}'),Ue=n(73042);function ze(){return ze=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},ze.apply(this,arguments)}var Ge,Ve,He=function(e){return a.createElement("svg",ze({xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24"},e),Re||(Re=a.createElement("path",{fill:"#236DE7",d:"M22 5.894a8.653 8.653 0 0 1-2.363.638A4.026 4.026 0 0 0 21.441 4.3a8.272 8.272 0 0 1-2.6.978A4.139 4.139 0 0 0 15.846 4c-2.27 0-4.097 1.814-4.097 4.038 0 .32.027.628.095.92a11.666 11.666 0 0 1-8.451-4.222 4.017 4.017 0 0 0-.562 2.04c0 1.399.732 2.638 1.821 3.356A4.098 4.098 0 0 1 2.8 9.634v.045c0 1.962 1.421 3.591 3.285 3.967-.334.09-.697.132-1.075.132a3.68 3.68 0 0 1-.776-.068 4.132 4.132 0 0 0 3.831 2.812 8.32 8.32 0 0 1-5.084 1.722c-.336 0-.659-.015-.981-.056A11.674 11.674 0 0 0 8.29 20c7.545 0 11.67-6.154 11.67-11.488 0-.178-.006-.35-.015-.522A8.111 8.111 0 0 0 22 5.894Z"})))},We=(n.p,function(e,t,n,r){var a=(0,Le.UJ)(e),i=(0,Le.TJ)(e),s=(0,Ae.s2)(t.time),c=(0,w.__)("I've just %1$s my %2$s %3$s WordPress database in %4$s using @wpmigratedbpro","wp-migrate-db"),l=(0,w.__)("I've just migrated my %1$s %2$s WordPress %3$s in %4$s using @wpmigratedbpro","wp-migrate-db");switch(n){case"push":case"pull":var o=(0,w.__)("database","wp-migrate-db");return["media_files","plugin_files","theme_files","muplugin_files","other_files"].some((function(e){return r.includes(e)}))&&(o=(0,w.__)("site","wp-migrate-db")),(0,w.gB)(l,a,i,o,s);case"backup_local":case"savefile":case"import":case"find_replace":var u=function(e){switch(e){case"import":return(0,w.__)("imported","wp-migrate-db");case"find_replace":return(0,w.__)("updated","wp-migrate-db");case"backup_local":return(0,w.__)("backed up","wp-migrate-db");default:return(0,w.__)("exported","wp-migrate-db")}}(n);return(0,w.gB)(c,u,a,i,s);default:return""}}),Ke=(0,i.$j)((function(e){return{migration_size:(0,J.xg)("migration_size",e),timer:(0,J.xg)("timer",e),stages:(0,J.r5)("stages",e),intent:(0,J.r5)("intent",e)}}),{})((function(e){var t=e.migration_size,n=e.timer,r=e.stages,i=e.intent;return a.createElement("a",{href:"https://twitter.com/intent/tweet?url=".concat(encodeURI("https://deliciousbrains.com/wp-migrate-db-pro/ "),"&text=").concat(We(t,n,i,r)),className:"twitter",title:"@dliciousbrains twitter link",target:"_blank",rel:"noopener noreferrer"},a.createElement(He,null))})),qe=n(29816),Ye=n(80401),Je=function(e){var t=e.link,n=e.content,r=e.utmContent,i=e.utmCampaign,s=e.screenReaderText,c=e.anchorLink,l=void 0===c?"":c,o=e.hasArrow,u=void 0!==o&&o;return a.createElement(Ye.Z,{link:t,content:n,utmContent:r,utmCampaign:i,classes:"btn btn-sm btn-stroke release-post-link",screenReaderText:s,hasArrow:u,anchorLink:l})};function Qe(){return Qe=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},Qe.apply(this,arguments)}var Xe,$e,et=function(e){return a.createElement("svg",Qe({width:94,height:36,viewBox:"0 0 94 36",fill:"none",xmlns:"http://www.w3.org/2000/svg"},e),Ge||(Ge=a.createElement("g",{clipPath:"url(#clip0_60_1330)"},a.createElement("path",{d:"M69.008 24.706c1.492 0 2.882-.576 4.046-1.666.062-.041.123-.123.184-.185.164-.185.143-.39.02-.597 0 0-.388-.637-.49-.781-.102-.144-.184-.206-.306-.227-.143-.02-.286.062-.45.206-.94.802-1.92 1.214-2.922 1.214-2.697 0-3.924-2.572-3.924-4.958 0-2.366 1.206-4.752 3.924-4.752.92 0 1.88.35 2.78 *************.47.144.592-.062l.51-.864c.164-.288.123-.473-.06-.658a1.166 1.166 0 0 0-.164-.144 5.862 5.862 0 0 0-3.74-1.317c-1.532 0-2.881.555-3.944 1.625-1.267 1.276-1.941 3.127-1.941 5.205 0 1.995.572 3.703 1.574 4.916 1.021 1.255 2.533 2.016 4.311 2.016ZM55.746 10.759c-3.413 0-5.967 2.942-5.967 6.85 0 4.012 2.554 7.036 5.967 7.036 3.392 0 5.947-3.024 5.947-7.036 0-3.888-2.555-6.85-5.947-6.85Zm0 11.808c-2.187 0-3.76-2.098-3.76-4.958 0-2.798 1.553-4.772 3.76-4.772s3.76 1.975 3.76 4.772c0 2.942-1.553 4.958-3.76 4.958ZM48.92 22.464h-4.842V11.397a.411.411 0 0 0-.409-.412h-1.328a.411.411 0 0 0-.409.412v12.651c0 .226.184.412.409.412h6.58a.411.411 0 0 0 .409-.412v-1.172a.411.411 0 0 0-.41-.412ZM93.428 22.464h-4.843V11.397a.411.411 0 0 0-.409-.412h-1.328a.411.411 0 0 0-.409.412v12.651c0 .226.184.412.409.412h6.58a.411.411 0 0 0 .409-.412v-1.172a.411.411 0 0 0-.41-.412ZM84.763 23.925l-4.087-12.631a.418.418 0 0 0-.388-.288H78.92c-.163 0-.286.103-.347.247l-4.149 12.672a.406.406 0 0 0 .389.534h1.328a.418.418 0 0 0 .388-.287l1.104-3.477h3.964l1.124 3.477a.418.418 0 0 0 .388.287h1.247c.306 0 .49-.267.408-.534Zm-6.64-5.143 1.225-3.868c.102-.308.205-.72.286-***********.184.762.286 1.07l1.227 3.868h-3.025Z",fill:"#53BA7D"}),a.createElement("g",{clipPath:"url(#clip1_60_1330)",fill:"#53BB7D"},a.createElement("path",{d:"M17.004 7.22V1.27c0-.41-.264-.778-.63-.942-.365-.163-.812-.06-1.096.225L2.113 13.806C.71 15.217.04 16.587.02 18.019c0 1.432.65 2.781 2.032 4.172l13.226 13.314a.95.95 0 0 0 .71.307c.122 0 .265-.02.387-.123a1.02 1.02 0 0 0 .63-.94v-6.013c0-.266-.123-.532-.305-.716L6.725 17.978 16.7 7.936a.963.963 0 0 0 .304-.715Zm-2.031 21.966v3.129l-9.63-9.694c-.04-.062-.081-.123-.142-.184-.305-.307-1.22-1.228.142-2.946l9.63 9.695Zm0-22.354L4.429 17.446c-.813.818-1.361 1.677-1.646 2.516-.508-.675-.751-1.309-.751-1.922.02-.86.508-1.78 1.503-2.782L14.973 3.744v3.088Z"}),a.createElement("path",{d:"M9.548 17.998v.082c0 .43.346.777.772.777h15.095a.774.774 0 0 0 .772-.777V18a.774.774 0 0 0-.772-.778H10.32a.774.774 0 0 0-.772.777ZM12.454 15.033v.082c0 .43.345.777.772.777h9.264a.774.774 0 0 0 .772-.777v-.082a.774.774 0 0 0-.772-.777h-9.264a.774.774 0 0 0-.772.777ZM19.625 12.906a.774.774 0 0 0 .772-.777v-.082a.774.774 0 0 0-.772-.777h-3.677a.774.774 0 0 0-.772.777v.082c0 .43.345.777.772.777h3.677ZM15.948 24.87h3.677a.774.774 0 0 0 .772-.777v-.082a.774.774 0 0 0-.772-.777h-3.677a.774.774 0 0 0-.772.777v.082c0 .43.345.777.772.777ZM13.226 21.864h9.264a.774.774 0 0 0 .772-.777v-.082a.774.774 0 0 0-.772-.777h-9.264a.774.774 0 0 0-.772.777v.082c0 .43.345.777.772.777Z"}),a.createElement("path",{d:"M33.826 13.888 20.6.574a.993.993 0 0 0-1.097-.225 1.02 1.02 0 0 0-.63.94v6.034c0 .266.122.532.305.716l9.975 10.041-9.975 10.042a.963.963 0 0 0-.305.716v5.951c0 .41.244.777.63.941a.876.876 0 0 0 .386.082c.264 0 .508-.102.711-.286l13.165-13.253c1.401-1.411 2.072-2.782 2.092-4.213 0-1.432-.65-2.781-2.031-4.172ZM20.905 6.873v-3.13l9.63 9.695c.*************.142.184.304.307 1.219 1.227-.142 2.945l-9.63-9.694ZM32.322 20.8 20.884 32.315v-3.068l10.544-10.635c.813-.818 1.361-1.677 1.646-2.515.508.674.752 1.308.752 1.922-.02.859-.508 1.78-1.504 2.781Z"})))),Ve||(Ve=a.createElement("defs",null,a.createElement("clipPath",{id:"clip0_60_1330"},a.createElement("path",{fill:"#fff",d:"M0 0h94v36H0z"})),a.createElement("clipPath",{id:"clip1_60_1330"},a.createElement("path",{fill:"#fff",transform:"translate(.02 .206)",d:"M0 0h35.959v35.79H0z"})))))},tt=(n.p,n(67072),function(e){var t="modal"===e.location?"local-callout-export-modal":"local-callout-migrate-tab";return a.createElement("div",{className:"local-callout migrate-notice"},a.createElement(et,{className:"local-logo"}),a.createElement("p",{className:"callout-content"},a.createElement("span",{className:"question"},(0,w.__)("Need to work on this site in a local development environment?","wp-migrate-db"))," ",a.createElement("span",{className:"answer"},(0,w.__)("Drag and drop an exported ZIP archive into Local. It's that easy!","wp-migrate-db"))),a.createElement(Je,{link:"https://deliciousbrains.com/wp-migrate-db-pro/doc/importing-wordpress-local-development-environment/",content:(0,w.__)("Learn More","wp-migrate-db"),utmContent:t,utmCampaign:"wp-migrate-documentation",screenReaderText:(0,w.__)("about importing into Local","wp-migrate-db")}))}),nt=["title","intent","progress_message","closeModal","fileTransferStats","migration_id","highPerformanceTransfersStatus","stages_complete"],rt=(0,i.$j)((function(e){var t=(0,J.xg)("title",e),n=(0,J.r5)("intent",e),r=(0,J.r5)("migration_id",e),a=(0,J.r5)(["highPerformanceTransfersStatus","fileTransferStats","stages_complete"],e),i=(0,k.Z)(a,3),s=i[0],c=i[1],l=i[2];return{title:t,progress_message:(0,J.xg)("progress_message",e),intent:n,fileTransferStats:c,migration_id:r,highPerformanceTransfersStatus:s,stages_complete:l}}),{})((function(e){var t=e.title,n=e.intent,r=e.progress_message,i=e.closeModal,s=e.fileTransferStats,c=e.migration_id,l=(e.highPerformanceTransfersStatus,e.stages_complete),u=((0,Z.Z)(e,nt),{loop:!1,autoplay:!0,animationData:je,rendererSettings:{preserveAspectRatio:"xMidYMid slice"}}),p=(0,o.k6)(),m=function(e){27===e.keyCode&&i()};(0,a.useEffect)((function(){return window.addEventListener("keydown",m),function(){window.removeEventListener("keydown",m)}}),[]);var d=l&&l.some((function(e){return qe.AG.includes(e)}));return a.createElement("div",{className:"migration-complete"},a.createElement("div",{className:"column flex-container"},a.createElement("button",{className:"popup-close align-right btn-no-outline",onClick:function(){return i()}},a.createElement(Ue.r,{"aria-label":(0,w.__)("Close migration modal","wp-migrate-db")})),a.createElement("h1",{className:"migration-title"},(0,E.ZP)(t)),a.createElement(Fe.Z,{options:u,"aria-hidden":"true",tabIndex:-1,"aria-label":"",height:156,width:156}),a.createElement("p",{className:"migration-complete-summary"},(0,E.ZP)(r)),l&&d&&a.createElement(Be.Z,{datas:s,columns:[{id:"currentStage",displayName:"Stage"},{id:"status",displayName:"Status"},{id:"maxPayloadSize",displayName:"Max Request Size (MB)"},{id:"currRequestSize",displayName:"Actual Request Size (MB)"},{id:"time",displayName:"Time"}],filename:"".concat(c,"-file-stats"),className:"btn btn-sm csv-download-btn",text:(0,w.__)("Download Report","wp-migrate-db")})),a.createElement("div",{className:"flex-container pos-relative"},"savefile"===n&&a.createElement(tt,{location:"modal"}),(0,_.Yu)()&&"savefile"!==n&&a.createElement(a.Fragment,null,a.createElement("button",{className:"btn ".concat(d?"btn-stroke":""," btn-sm migration-complete-close-btn"),onClick:function(){return function(){if("backup_local"===n&&(0,_.Yu)())return document.body.style.overflow="auto",void p.push("/backups");i()}()},autoFocus:!0},"backup_local"===n?(0,w.__)("View Backups","wp-migrate-db"):(0,w.__)("Close","wp-migrate-db")),a.createElement(Ke,null))),!(0,_.Yu)()&&a.createElement("div",{className:"rate-mdb"},(0,E.ZP)((0,w.gB)((0,w.__)('Please consider <a href="%s" target="_blank" rel="noopener noreferrer">reviewing WP Migrate</a> on WordPress.org.',"wp-migrate-db"),"https://wordpress.org/support/plugin/wp-migrate-db/reviews/?filter=5#new-post"))))})),at=n(83115),it=v.ZP.div(Xe||(Xe=(0,h.Z)(["\n  height: 5px;\n  width: ","%;\n  background: blue;\n  transition: 0.4s linear;\n  transition-property: width, background-color;\n"])),(function(e){return e.width})),st=(0,i.$j)((function(e){var t=e.dry_run.results,n=(0,J.r5)("preview",e),r=(0,J.xg)("item_progress",e);return{running_size:(0,J.xg)("stage_size",e),total_stage_size:(0,J.xg)("total_stage_size",e),isPreview:n,tables_selected:t,item_progress:r,progress_stage:(0,J.xg)("progress_stage",e),status:(0,J.xg)("status",e),showDryRunResults:(0,J.xg)("showDryRunResults",e)}}),{})((function(e){var t=e.running_size,n=e.total_stage_size,r=e.isPreview,s=e.tables_selected,c=e.item_progress,l=e.showDryRunResults,o=e.progress_stage,u=e.status,p=(0,Le.UJ)(n),m=(0,Le.UJ)(t),d=(0,i.I0)(),f=Math.round(parseInt(t)/n*100)||0;(0,a.useEffect)((function(){!l&&"COMPLETE"===o&&"PAUSED"===u&&r&&d((0,at.dy)(!0))}));var g=(0,_.Yu)();return a.createElement(a.Fragment,null,a.createElement("div",{className:"flex-container flex-align-baseline"},a.createElement("p",{className:"migration-percentage"},f,"%"),a.createElement("p",{className:"migration-data-transferred semibold"},"(",m," ",(0,Le.TJ)(t)," /"," ",p," ",(0,Le.TJ)(n),")"),r&&a.createElement(a.Fragment,null,a.createElement("p",{className:"migration-tables-searched"},Object.keys(c).length," ",g?"of ".concat(s.length," "):"",(0,w.__)("Tables Searched","wp-migrate-db")))),a.createElement("div",{className:"migration-progress-bar bg-grey-light"},a.createElement(it,{width:f,className:"migration-progress-bar-running bg-primary"})))})),ct=n(12544),lt=n.n(ct),ot=n(19085),ut=(0,i.$j)((function(e){return{current_stage:(0,J.r5)("current_stage",e),stages_complete:(0,J.r5)("stages_complete",e)}}),{})((function(e){var t=e.current_stage,n=e.stage,r=e.stages_complete,i=e.hasError,s=e.status;return i?a.createElement(Ue.r,null):lt()(r,n)?a.createElement(C,null):t===n||"migrate"===t&&"tables"===n||"find_replace"===t&&"tables"===n?a.createElement(ot.Q,{className:"PAUSED"===s?"paused":""}):null})),pt={backup:(0,w.__)("Backup","wp-migrate-db"),tables:(0,w.__)("Tables","wp-migrate-db"),upload:(0,w.__)("Upload","wp-migrate-db"),import:(0,w.__)("Import","wp-migrate-db"),find_replace:(0,w.__)("Find & Replace","wp-migrate-db"),theme_files:(0,w.__)("Themes","wp-migrate-db"),plugin_files:(0,w.__)("Plugins","wp-migrate-db"),muplugin_files:(0,w.__)("Must-Use Plugins","wp-migrate-db"),other_files:(0,w.__)("Other Files","wp-migrate-db"),core_files:(0,w.__)("WordPress Core Files","wp-migrate-db"),media_files:(0,w.__)("Media Uploads","wp-migrate-db")},mt=n(27114),dt=function(e){var t=(0,i.v9)((function(e){return e.migrations.migration_progress})).status,n=(0,i.v9)((function(e){return e.migrations.current_migration})),r=n.intent,s=n.current_stage,c="";(0,_.Yu)()&&(c=(0,i.v9)((function(e){return e.migrations.remote_site})).url);var l={savefile:(0,w.__)("Export","wp-migrate-db"),import:(0,w.__)("Import","wp-migrate-db"),push:(0,w.__)("Push","wp-migrate-db"),pull:(0,w.__)("Pull","wp-migrate-db"),backup_local:(0,w.__)("Backup","wp-migrate-db"),find_replace:(0,w.__)("Find & Replace","wp-migrate-db")},o={migrate:(0,w.__)("Table Migration","wp-migrate-db"),backup:(0,w.__)("Backup","wp-migrate-db"),media_files:(0,w.__)("Media","wp-migrate-db"),find_replace:"import"===r?(0,w.__)("Find & Replace","wp-migrate-db"):(0,w.__)("Migrate Tables","wp-migrate-db"),upload:(0,w.__)("Upload","wp-migrate-db"),import:(0,w.__)("Import","wp-migrate-db"),theme_files:(0,w.__)("Themes","wp-migrate-db"),plugin_files:(0,w.__)("Plugins","wp-migrate-db"),muplugin_files:(0,w.__)("Must-Use Plugins","wp-migrate-db"),other_files:(0,w.__)("Other Files","wp-migrate-db"),core_files:(0,w.__)("WordPress Core Files","wp-migrate-db")},u=function(e){return"backup_local"===e?"":["push","pull"].includes(e)?'<span class="black">'.concat("push"===e?(0,w.__)("to","wp-migrate-db"):(0,w.__)("from","wp-migrate-db"),"</span> ").concat((0,_.zj)(c)):"migration"},p=function(e){return"initiate_migration"===s?a.createElement("div",null,(0,E.ZP)((0,w.gB)((0,w.__)("The <span>%s</span> <span>%s</span> failed during the migration initialization stage.","wp-migrate-db"),l[r],u(r)))):a.createElement("div",null,(0,E.ZP)((0,w.gB)((0,w.__)("The <span>%s</span> <span>%s</span> failed during the <span>%s</span> stage.","wp-migrate-db"),l[r],u(r),o[s])))};return a.createElement("div",{className:"mdb-migration-error"},a.createElement("h4",null,a.createElement(p,null)),a.createElement("div",{className:"mdb-migration-error-message"},a.createElement("div",{className:"error-block consolas"},(0,E.ZP)((0,mt.Y)(t)))))},ft=n(38930),_t=(n(34901),function(e){var t=e.table,n=e.onClick,r=e.id,i=(0,_.Yu)(),s=(0,_.ME)();return a.createElement(a.Fragment,null,a.createElement("button",{onClick:n,className:"table-results-button","data-tip":!0,"data-for":"action-tip-dry-run-".concat(r)},t),(!i||s)&&a.createElement(ft.Z,{effect:"solid",place:"left",type:"light",delayUpdate:500,delayHide:250,border:!0,className:"action-tooltip",id:"action-tip-dry-run-".concat(r),getContent:function(){return(0,E.ZP)((0,_.mF)("dryRun",i))}}))}),gt=(0,i.$j)((function(e){var t=e.dry_run.results,n=(0,J.xg)("status",e),r=(0,J.xg)("progress_stage",e);return{results:t,migrationStatus:n,isPreview:(0,J.r5)("preview",e),migrationStage:r}}))((function(e){var t=e.results,n=e.isPreview,r=e.migrationStatus,s=e.migrationStage,c=(0,i.I0)(),l=(0,_.Yu)(),o=(0,_.ME)(),u=function(e){return function(){c((0,at.$1)(e))}};return a.createElement(a.Fragment,null,a.createElement("table",{className:"dry-run-results-table container-shadow table"},a.createElement("thead",null,a.createElement("tr",{className:"flex-container"},a.createElement("th",{className:"table-heading"},(0,w.__)("Table","wp-migrate-db")),a.createElement("th",{className:"table-heading"},(0,w.__)("Matches Found","wp-migrate-db")),a.createElement("th",{className:"table-heading"},(0,w.__)("Time","wp-migrate-db")))),a.createElement("tbody",{tabIndex:0},t.map((function(e,t){return a.createElement("tr",{key:t,className:"flex-container result-item"},a.createElement("td",null,a.createElement("span",{className:"table-name"},e.table)),a.createElement("td",null,!e.executed&&a.createElement("span",{className:"waiting-dash"},"-"),e.executed&&e.count>0&&a.createElement(_t,{table:e.count,onClick:e.count>0&&!o&&l?u(t):null,id:t}),e.executed&&0===e.count&&a.createElement("span",{className:"table-results-none"},e.count)),a.createElement("td",null,!e.executed&&a.createElement("span",{className:"waiting-dash"},"-"),e.executed&&(0,Le.q5)(e.time)))})))),n&&("PAUSED"===r||"CANCELLED"===r)&&"COMPLETE"===s&&a.createElement("p",{className:"dry-run-notice"},a.createElement("strong",null,(0,w.__)("Everything look good?","wp-migrate-db"))," ",(0,w.__)("Apply these changes to update WordPress database. If any data has been modified since this preview was started, that data will be overwritten.","wp-migrate-db")))})),bt=n(56381),ht=n.n(bt),vt=function(e){return e.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&#039;")},Et=function(e){var t=e.item,n=t.column,r=t.row,i=t.original,s=t.replace,c=(0,a.useState)(""),l=(0,k.Z)(c,2),o=l[0],u=l[1];return(0,a.useEffect)((function(){var e=new(ht())({timeout:2,editCost:20}),t=e.main(i,s);e.cleanupSemantic(t);var n=function(e){var t="",n="";for(var r in e){var a=(0,k.Z)(e[r],2),i=a[0],s=a[1];if(0!==i)0>i?t+="<del>".concat(vt(s),"</del>"):0<i&&(t+="<ins>".concat(vt(s),"</ins>"));else{var c=s;s.length>=100&&(c="".concat(s.slice(0,25),"...").concat(s.slice(-25))),t+="<span>".concat(vt(c),"</span>"),n+="<span>".concat(vt(c),"</span>")}}return[t,n]}(t),r=(0,k.Z)(n,1)[0];u(r)}),[t]),a.createElement("li",{className:"dry-run-result"},a.createElement("div",{className:"dry-run-result-meta"},a.createElement("span",null,a.createElement("strong",null,(0,w.__)("Row ID:","wp-migrate-db"))," ",r),a.createElement("span",null,a.createElement("strong",null,(0,w.__)("Column:","wp-migrate-db"))," ",n)),a.createElement("code",{className:"dry-run-result-code"},(0,E.ZP)(o)))},wt=function(e){var t=e.currentPage,n=e.pageCount,r=e.setCurrentPage,i=e.scrollableContentRef;return a.createElement("div",{className:"pagination-controls"},a.createElement("span",null,t," of ",0===n?1:n),a.createElement("button",{className:"btn btn-sm btn-stroke migration-progress-btn ".concat(1===t?"btn-disabled":""),onClick:function(){i.current.scrollTop=0,r(t-1)},disabled:1===t},(0,w.__)("Prev","wp-migrate-db")),a.createElement("button",{className:"btn btn-sm btn-stroke migration-progress-btn ".concat(t>=n?"btn-disabled":""),onClick:function(){i.current.scrollTop=0,r(t+1)},disabled:t>=n},(0,w.__)("Next","wp-migrate-db")))},yt=(0,i.$j)((function(e){return{currentPreviewItem:e.dry_run.currentPreviewItem,results:e.dry_run.results}}))((function(e){var t=e.currentPreviewItem,n=e.results,r=(0,a.useState)(null),i=(0,k.Z)(r,2),s=i[0],c=i[1],l=(0,a.useState)([]),o=(0,k.Z)(l,2),u=o[0],p=o[1],m=(0,a.useState)(1),d=(0,k.Z)(m,2),f=d[0],_=d[1],g=(0,a.useState)(0),b=(0,k.Z)(g,2),h=b[0],v=b[1],E=(0,a.useRef)();return(0,a.useEffect)((function(){"undefined"!==typeof n[t]&&c(n[t])}),[t,n[t]]),(0,a.useEffect)((function(){if(s){var e=50*f;p(s.data.slice(e-50,e))}}),[f,s]),(0,a.useEffect)((function(){s&&v(Math.round(s.count/50))}),[s]),null===s?null:a.createElement(a.Fragment,null,a.createElement("div",{className:"flex-container migration-progress-controls dry-run-controls"},a.createElement("h1",null,s.table),h>1&&a.createElement(wt,{currentPage:f,pageCount:h,setCurrentPage:_,scrollableContentRef:E})),a.createElement("ul",{className:"dry-run-results-single-table container-shadow",tabIndex:"0",ref:E},u.map((function(e){return a.createElement(Et,{item:e})}))))})),xt=n(86645),kt=["stages","pause_before_finalize","intent","status","hasError","closer","isPreview","currentDryRunPreviewItem","progress_stage"],Zt=function(){var e=(0,b.Z)((0,g.Z)().mark((function e(t,n,r,a,i){return(0,g.Z)().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if("PAUSED"!==r){e.next=4;break}return e.next=3,a();case 3:return e.abrupt("return",e.sent);case 4:return e.abrupt("return",i());case 5:case"end":return e.stop()}}),e)})));return function(t,n,r,a,i){return e.apply(this,arguments)}}(),Nt=(0,i.$j)((function(e){var t=e.dry_run.currentPreviewItem,n=(0,J.r5)("current_stage",e),r=(0,J.xg)("pause_before_finalize",e),a=(0,J.r5)("preview",e),i=(0,J.r5)("running",e),s=(0,J.r5)("intent",e),c=(0,J._P)("this_temp_prefix",e),l=(0,J.xg)("status",e),o=(0,J.r5)("stages",e),u=(0,J.xg)("title",e),p=(0,J.xg)("progress_stage",e),m=(0,J.r5)("stages_complete",e),d=(0,J.r5)(["highPerformanceTransfersStatus","forceHighPerformanceTransfers"],e),f=(0,k.Z)(d,2),_=f[0],g=f[1];return{current_stage:n,status:l,running:i,intent:s,tmp_prefix:c,total_stage_size:(0,J.xg)("total_stage_size",e),running_size:(0,J.xg)("stage_size",e),stages:o,stages_complete:m,pause_before_finalize:r,title:u,progress_message:(0,J.xg)("progress_message",e),progress_stage:p,showDryRunResults:(0,J.xg)("showDryRunResults",e),isPreview:a,currentDryRunPreviewItem:t,highPerformanceTransfersStatus:_,forceHighPerformanceTransfers:g}}),{setPauseBeforeFinalize:K.LR,cancelMigration:de.Gn,pauseMigration:de.Kh,resumeMigration:de.Rw})((function(e){var t=e.stages,n=e.pause_before_finalize,r=e.intent,i=e.status,s=e.hasError,c=e.closer,l=e.isPreview,o=e.currentDryRunPreviewItem,u=e.progress_stage,p=(0,Z.Z)(e,kt),m="PAUSED"===i,d="COMPLETE"===u,f=l&&d?(0,w.__)("Apply Changes","wp-migrate-db"):m?(0,w.__)("Resume","wp-migrate-db"):(0,w.__)("Pause","wp-migrate-db"),_=p.current_stage;if("COMPLETE"===p.status)return null;if(null!==o)return a.createElement(yt,null);var g=["media_files","theme_files","plugin_files","other_files","muplugin_files"].includes(_);return a.createElement(a.Fragment,null,"initiate_migration"!==_&&a.createElement(st,null),a.createElement(a.Fragment,null,"initiate_migration"!==_&&a.createElement("ul",{className:"flex-container"},t.map((function(e,t){return a.createElement("li",{className:"migration-progress-steps",key:t},a.createElement(ut,{stage:e,hasError:s,status:i}),pt[e])}))),l&&a.createElement(gt,null),s&&a.createElement(dt,p),g&&a.createElement(xt.Z,null),s?a.createElement("div",{className:"text-center error-btn"},a.createElement("button",{onClick:function(){return c(!0)},className:"btn btn-sm"},(0,w.__)("Close","wp-migrate-db"))):a.createElement("div",{className:"flex-container migration-progress-controls ".concat(l||g?"no-top-margin":"")},a.createElement("button",{onClick:function(){Zt(c,p,i,p.resumeMigration,p.pauseMigration)},className:"btn btn-sm ".concat(l&&d?"":"btn-stroke"," migration-progress-btn")},f)," ",a.createElement("button",{onClick:function(){(0,p.cancelMigration)()},className:"btn btn-sm btn-stroke migration-progress-btn"},(0,w.__)("Cancel","wp-migrate-db")),!["backup_local","savefile"].includes(r)&&"MIGRATE"===u&&!l&&a.createElement("div",{className:"align-right wpmdb-form"},a.createElement("input",{type:"checkbox",className:"checkbox-default",checked:n,id:"pause-before-finalize",onChange:function(){p.setPauseBeforeFinalize(!n)}}),a.createElement("label",{htmlFor:"pause-before-finalize"},(0,w.__)("Pause before replacing migrated tables","wp-migrate-db"))))))})),Pt=["title","closer","status","hasError"],St=["closer","title","status","closeHandler","intent","remoteURL","isPreview","currentPreviewItem"],Tt=function(e){var t=e.closeModal,n=e.isPreview;return a.createElement("div",{className:"migration-progress-content"},a.createElement("div",null,a.createElement("h1",{className:"migration-title cancelled"},window.wpmdb_strings.migration_cancelled),a.createElement("p",null,n&&(0,w.__)("The operation has been stopped and no changes were made.","wp-migrate-db"),!n&&(0,E.ZP)(window.wpmdb_strings.migration_cancelled_success))),a.createElement("div",null,a.createElement("button",{onClick:t,className:"btn btn-sm"},(0,w.__)("Close","wp-migrate-db"))))},Ot=function(e){var t=e.title,n=e.closer,r=e.status,i=e.hasError;(0,Z.Z)(e,Pt);return a.createElement("div",{className:"migration-progress-content migration-error"},a.createElement("div",{className:"flex-container"},a.createElement("h1",{className:"migration-title flex-container error"},a.createElement(Ie,{className:"error-icon"}),(0,E.ZP)(t)),a.createElement("div",{className:"migration-timer align-right"},a.createElement(Me,null))),a.createElement(Nt,{closer:n,status:r,hasError:i}))},At=function(e){var t=e.closer,n=e.title,r=e.status,s=e.closeHandler,l=e.intent,o=e.remoteURL,u=e.isPreview,p=e.currentPreviewItem,m=(0,Z.Z)(e,St);(0,a.useEffect)((function(){u&&"CANCEL_COMPLETE"===r&&(t(!0),setTimeout((function(){d((0,G.m)(_e.MG,"CANCELLED"))}),300))}),[r]);var d=(0,i.I0)(),f="",g=u&&null!==p,b="object"===typeof r?r.hasOwnProperty("error_type"):void 0;if(b)return a.createElement(Ot,(0,c.Z)({},m,{hasError:b,closer:function(){return t(!0)},title:n}));switch(r){case"COMPLETE":f=a.createElement(rt,{closeModal:function(e){t(!0),s("COMPLETE")}});break;case"CANCEL_COMPLETE":if(!u){f=a.createElement(Tt,(0,c.Z)({},m,{isPreview:u,closeModal:function(){return t(!0)}}));break}default:f=a.createElement("div",{className:"migration-progress-content"},null!==p&&a.createElement("button",{className:"dry-run-back-btn link",onClick:function(){d((0,at.$1)(null))}},"\u2190 ",(0,w.__)("Back to All Tables","wp-migrate-db")),a.createElement("div",{className:"flex-container ".concat(g?"hidden":"")},a.createElement("h1",{className:["push","pull"].includes(l)?"push-pull":null},function(e,t){var n={push:(0,w.gB)((0,w.__)("<span>Pushing</span> to <span>%s</span>","wp-migrate-db"),t),pull:(0,w.gB)((0,w.__)("<span>Pulling</span> from <span>%s</span>","wp-migrate-db"),t),find_replace:(0,w.__)("Find & Replace","wp-migrate-db"),savefile:(0,w.__)("Exporting","wp-migrate-db"),import:(0,w.__)("Importing","wp-migrate-db"),backup_local:(0,w.__)("Backing up","wp-migrate-db")};return(0,E.ZP)(n[e])}(l,(0,_.zj)(o)),u&&a.createElement("span",{className:"preview-migration-heading"},(0,w.__)("(Preview)","wp-migrate-db"))),a.createElement("div",{className:"migration-timer align-right"},a.createElement(Me,null))),a.createElement("h2",{className:"migration-title flex-container ".concat(g?"hidden":"")},(0,E.ZP)(n)),a.createElement(Nt,{closer:t,status:r,hasError:b}))}return f},Ct=function(e){var t=Pe,n=Te,r=(0,a.useState)(!1),i=(0,k.Z)(r,2),s=i[0],l=i[1];(0,a.useEffect)((function(){return function(){l(!1)}}),[]);return s&&(t=Se,n=Oe),a.createElement(Ne,{animation:t,bgAnimation:n,onClick:function(t){var n=e.status;"COMPLETE"!==n&&"CANCEL_COMPLETE"!==n||t.target===t.currentTarget&&(l(!0),e.closeHandler(n))},complete:"COMPLETE"===e.status,setRunning:de.al,shouldClose:s},a.createElement(At,(0,c.Z)({closer:function(e){l(e)},closeHandler:e.closeHandler},e)))},Rt=(0,i.$j)((function(e){var t=(0,J.r5)("preview",e),n=e.dry_run.currentPreviewItem,r=(0,J.xg)("pause_before_finalize",e),a=(0,J.r5)("running",e),i=(0,J.r5)("intent",e),s=(0,J._P)("this_temp_prefix",e),c=(0,J.xg)("status",e),l=(0,J.r5)("stages",e),o=(0,J.xg)("title",e),u=(0,_.Yu)()?(0,J.FY)("url",e):"",p=(0,J.xg)("progress_stage",e),m=(0,J.r5)("stages_complete",e);return{status:c,running:a,intent:i,tmp_prefix:s,total_stage_size:(0,J.xg)("total_stage_size",e),running_size:(0,J.xg)("stage_size",e),stages:l,stages_complete:m,pause_before_finalize:r,title:o,progress_message:(0,J.xg)("progress_message",e),progress_stage:p,remoteURL:u,isPreview:t,currentPreviewItem:n}}),{cancelMigration:de.Gn,pauseMigration:de.Kh,resumeMigration:de.Rw,setPauseBeforeFinalize:K.LR,maybeRefresh:Ae.K_,closeHandler:Ae.B3})(a.memo(Ct)),It=n(40795);function Dt(){return Dt=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},Dt.apply(this,arguments)}var Lt,Mt=function(e){return a.createElement("svg",Dt({height:24,viewBox:"0 0 24 24",width:24,xmlns:"http://www.w3.org/2000/svg"},e),$e||($e=a.createElement("path",{d:"M6 11V8a6 6 0 1 1 12 0v3h1a1 1 0 0 1 1 1v10a1 1 0 0 1-1 1H5a1 1 0 0 1-1-1V12a1 1 0 0 1 1-1zm3 0h6V8a3 3 0 0 0-6 0zm3.969 6.75a2 2 0 1 0-1.938 0c-.02.08-.031.164-.031.25v1a1 1 0 0 0 2 0v-1c0-.086-.01-.17-.031-.25z",fill:"#b2b2b2",fillRule:"evenodd"})))},Ft=(n.p,{push:(0,w.__)("Push","wp-migrate-db"),pull:(0,w.__)("Pull","wp-migrate-db"),import:(0,w.__)("Import Database","wp-migrate-db"),savefile:(0,w.__)("Export","wp-migrate-db"),backup_local:(0,w.__)("Backup Database","wp-migrate-db"),find_replace:(0,w.__)("Find & Replace","wp-migrate-db")}),Bt={push:(0,w.__)("Push","wp-migrate-db"),pull:(0,w.__)("Pull","wp-migrate-db"),savefile:(0,w.__)("Export","wp-migrate-db"),import:(0,w.__)("Import Database","wp-migrate-db"),find_replace:(0,w.__)("Find & Replace","wp-migrate-db"),backup_local:(0,w.__)("Backup Database","wp-migrate-db")};n(71989);function jt(){return jt=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},jt.apply(this,arguments)}var Ut,zt=function(e){return a.createElement("svg",jt({height:24,viewBox:"0 0 24 24",width:24,xmlns:"http://www.w3.org/2000/svg"},e),a.createElement("g",{style:{stroke:"#326eff",strokeWidth:2,fill:"none",fillRule:"evenodd",strokeLinecap:"round",strokeLinejoin:"round"}},Lt||(Lt=a.createElement("path",{d:"M19.5 12h-14M12.5 4.5l-7 7.5 7 7.5"}))))};n.p;function Gt(){return Gt=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},Gt.apply(this,arguments)}var Vt,Ht=function(e){return a.createElement("svg",Gt({height:24,viewBox:"0 0 24 24",width:24,xmlns:"http://www.w3.org/2000/svg"},e),a.createElement("g",{style:{stroke:"#326eff",strokeWidth:2,fill:"none",fillRule:"evenodd",strokeLinecap:"round",strokeLinejoin:"round"}},Ut||(Ut=a.createElement("path",{d:"M20 15v3.333c0 .92-.796 1.667-1.778 1.667H5.778C4.796 20 4 19.254 4 18.333V15M16 11l-4 4-4-4M12 4v9"}))))};n.p;function Wt(){return Wt=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},Wt.apply(this,arguments)}var Kt,qt=function(e){return a.createElement("svg",Wt({height:24,viewBox:"0 0 24 24",width:24,xmlns:"http://www.w3.org/2000/svg"},e),a.createElement("g",{style:{stroke:"#236de7",strokeWidth:2,fill:"none",fillRule:"evenodd",strokeLinecap:"round",strokeLinejoin:"round"}},Vt||(Vt=a.createElement("path",{d:"M5.5 12h14M12.5 4.5l7 7.5-7 7.5"}))))};n.p;function Yt(){return Yt=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},Yt.apply(this,arguments)}var Jt,Qt,Xt=function(e){return a.createElement("svg",Yt({height:24,viewBox:"0 0 24 24",width:24,xmlns:"http://www.w3.org/2000/svg"},e),a.createElement("g",{style:{stroke:"#236de7",strokeWidth:2,fill:"none",fillRule:"evenodd",strokeLinecap:"round",strokeLinejoin:"round"}},Kt||(Kt=a.createElement("path",{d:"M20 15v3.333c0 .92-.796 1.667-1.778 1.667H5.778C4.796 20 4 19.254 4 18.333V15M16 8l-4-4-4 4M12 4v11"}))))};n.p;function $t(){return $t=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},$t.apply(this,arguments)}var en,tn=function(e){return a.createElement("svg",$t({height:24,viewBox:"0 0 24 24",width:24,xmlns:"http://www.w3.org/2000/svg"},e),a.createElement("g",{style:{stroke:"#236de7",strokeWidth:2,fill:"none",fillRule:"evenodd",strokeLinecap:"round",strokeLinejoin:"round"},transform:"translate(4 4)"},Jt||(Jt=a.createElement("circle",{cx:7,cy:7,r:7})),Qt||(Qt=a.createElement("path",{d:"m16 16-4-4"}))))};n.p;function nn(){return nn=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},nn.apply(this,arguments)}var rn=function(e){return a.createElement("svg",nn({height:24,viewBox:"0 0 24 24",width:24,xmlns:"http://www.w3.org/2000/svg"},e),en||(en=a.createElement("g",{fill:"none",fillRule:"evenodd"},a.createElement("path",{d:"M22 12.5H2M5.45 5.11 2 12v6a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2v-6l-3.45-6.89A2 2 0 0 0 16.76 4H7.24a2 2 0 0 0-1.79 1.11z",stroke:"#236de7",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2}),a.createElement("g",{fill:"#236de7",transform:"translate(2 4)"},a.createElement("circle",{cx:4,cy:12,r:1}),a.createElement("circle",{cx:7,cy:12,r:1})))))},an=(n.p,a.forwardRef((function(e,t){var n=e.panelHandler,r=e.config,i=e.description,s=e.disabled,c=e.connecting,l=e.intent,o=e.toolTipOpen,u=e.setToolTipOpen,p=" action-".concat(r.intent),m=l===r.intent?"".concat(p," active"):p,d=s?"".concat(m," disabled"):m,f=function(e){u(e)};return a.createElement("div",{className:"action-btn-wrap"},a.createElement("button",{className:"action-icon btn-no-outline".concat(d),onClick:function(){if(s||c)return!1;n(r)},"data-tip":!0,"data-for":"action-tip-".concat(r.intent),onMouseEnter:function(){return f(r.intent)},onFocus:function(){return f(r.intent)}},e.children),a.createElement(ft.Z,{effect:"solid",place:"bottom",type:"light",delayUpdate:500,delayHide:250,border:!0,className:"action-tooltip",id:"action-tip-".concat(r.intent),getContent:function(){return r.intent!==o?null:(0,E.ZP)(i)}}))}))),sn=function(e){return(0,i.v9)((function(e){return e.panels.panelsOpen})).includes(e.panelName)?null:a.createElement("div",{className:"panel-summary"},e.panelTitle)},cn=function(e){var t=!1,n=["actions-panel"];(0,i.v9)((function(e){return e.panels.panelsOpen})).includes(e.panelName)||(t=!0,n.push("has-divider"));var r=function(){var t=(0,_.ME)(),n=(0,a.useRef)(null),r=e.connecting,i=(0,a.useState)(""),s=(0,k.Z)(i,2),l=s[0],o=s[1],u=(0,a.useState)([]),p=(0,k.Z)(u,2),m=p[0],d=p[1],f=(0,x.Z)({},e);return f.toolTipContent=l,f.setToolTipContent=o,a.createElement("div",{className:"".concat(r?"connecting ":"","action-buttons btn-section")},a.createElement("div",{className:"action-row",ref:n},a.createElement("div",{className:"action-button-group"},a.createElement("h4",null,"Replace This Site with Another\xa0Site"),a.createElement("div",{className:"buttons"},a.createElement(an,(0,c.Z)({config:{panel:"connect",title:q.B.pull,intent:"pull"},description:t?(0,_.mF)("pull",(0,_.Yu)()):(0,w.__)("Replace <b>this site's</b> database and files with a <b>remote site's</b> database&nbsp;and&nbsp;files","wp-migrate-db"),disabled:t,wrapperRef:n,toolTipOpen:m,setToolTipOpen:d},f),a.createElement("p",{className:"action-title text-center"},Bt.pull),t?a.createElement(Mt,null):a.createElement(zt,null)),a.createElement(an,(0,c.Z)({config:{panel:"import",title:q.B.import,intent:"import"},description:t?(0,_.mF)("import",(0,_.Yu)()):(0,w.__)("Replace <b>this site's</b> database with the contents of an&nbsp;SQL&nbsp;file","wp-migrate-db"),disabled:t,wrapperRef:n,toolTipOpen:m,setToolTipOpen:d},f),a.createElement("p",{className:"action-title text-center"},Bt.import),t?a.createElement(Mt,null):a.createElement(Ht,null)))),a.createElement("div",{className:"action-button-group"},a.createElement("h4",null,"Replace Another Site with This\xa0Site"),a.createElement("div",{className:"buttons"},a.createElement(an,(0,c.Z)({config:{panel:"connect",title:q.B.push,intent:"push"},description:t?(0,_.mF)("push",(0,_.Yu)()):(0,w.__)("Replace a <b>remote site's</b> database and files with <b>this site's</b> database and files","wp-migrate-db"),disabled:t,wrapperRef:n,toolTipOpen:m,setToolTipOpen:d},f),a.createElement("p",{className:"action-title text-center"},Bt.push),t?a.createElement(Mt,null):a.createElement(qt,null)),a.createElement(an,(0,c.Z)({config:{panel:"database",title:q.B.savefile,intent:"savefile"},description:(0,E.ZP)((0,w.__)("Export this site's database and files","wp-migrate-db")),disabled:!1,wrapperRef:n,toolTipOpen:m,setToolTipOpen:d},f),a.createElement("p",{className:"action-title text-center"},Bt.savefile),a.createElement(Xt,null)))),a.createElement("div",{className:"action-button-group"},a.createElement("h4",null,"Tools for This Site"),a.createElement("div",{className:"buttons"},a.createElement(an,(0,c.Z)({config:{panel:"database",title:q.B.find_replace,intent:"find_replace"},description:(0,E.ZP)((0,w.__)("Replace text in this site's database","wp-migrate-db")),disabled:!1,wrapperRef:n,toolTipOpen:m,setToolTipOpen:d},f),a.createElement("p",{className:"action-title text-center"},Bt.find_replace),a.createElement(tn,null)),a.createElement(an,(0,c.Z)({config:{panel:"database",title:q.B.backup_local,intent:"backup_local"},description:(0,E.ZP)((0,w.__)("Save your database to an SQL file on your&nbsp;server","wp-migrate-db")),disabled:!1,wrapperRef:n,toolTipOpen:m,setToolTipOpen:d},f),a.createElement("p",{className:"action-title text-center"},Bt.backup_local),a.createElement(rn,null))))))};return a.createElement(It.Z,{title:e.title,panelName:e.panelName,panelKey:1,hasSummary:!0,forceDivider:t,panelSummary:a.createElement(sn,(0,c.Z)({panelTitle:e.panelSummary,panelName:e.panelName},e)),className:n.join(" ")},a.createElement(r,null))},ln=n(50166),on=n(70659),un=n.n(on),pn=function(e){var t=e.selected_existing_profile,n=e.saved,r=(0,a.useRef)(null),i=e.match.url;null===t&&n.length>0&&(t=e.profiles.saved[0].id),(0,a.useEffect)((function(){r.current&&r.current.scrollIntoView()}),[r]);var s=function(e){var t=e.checked,n=e.item,i=t?r:null;return a.createElement("input",{type:"radio",name:"saved-profile",checked:t,onChange:function(t){e.setSelectedExistingProfile(n.id)},ref:i})};if(n.length>0){var l=n.map((function(n,r){var l=!1;return i.includes("unsaved")||parseInt(t)===n.id&&(l=!0),a.createElement("li",{key:r},a.createElement("label",{className:"recent-list"},a.createElement(s,(0,c.Z)({checked:l,item:n},e))," ",a.createElement("b",null,n.id),a.createElement("span",null,n.name)))}));return a.createElement("div",{className:"scroll-div"},a.createElement("ul",null,l))}return a.createElement("div",{className:"scroll-div empty"},"No saved profiles")},mn=function(e){var t=e.current_migration.profile_name;return a.createElement("div",{className:"flex-group"},a.createElement("div",{className:"save-profile-input"},a.createElement("label",{htmlFor:"save-new-profile"},"Profile Name"),a.createElement("br",null),a.createElement("input",{id:"save-new-profile",type:"text",value:t,onChange:function(t){return function(e,t){t.setProfileName(e.target.value)}(t,e)},className:"consolas"})))},dn=(n(28637),n(52650)),fn=function(e){var t=e.tabOpen,n=e.saved;return"save_new"===t?a.createElement(mn,e):a.createElement("div",{className:"flex-group"},a.createElement(pn,(0,c.Z)({saved:n},e)))};var _n,gn,bn,hn,vn,En,wn,yn=(0,i.$j)((function(e){var t=(0,ae.d)("ui",e),n=(0,J.r5)("selected_existing_profile",e);return{ui_data:t,btn_text:t.btn_text,selected_existing_profile:n,panels:e.panels,migration:e.migrations,profiles:e.profiles,current_migration:e.migrations.current_migration}}),{addProfile:B.qU,setProfileName:K.fJ,setSelectedExistingProfile:B.zD,overwriteProfile:B.Vu,renderButtonStatus:B.F9})((function(e){var t=e.profiles.saved,n=e.profiles.status,r=n.profile_save_error,s=n.saving,l=(0,a.useState)("save_new"),o=(0,k.Z)(l,2),u=o[0],p=o[1],m=e.saveProfileOpen,d=e.setSaveProfileOpen,f=(0,a.useState)(0),_=(0,k.Z)(f,2),h=_[0],v=_[1],E=(0,i.I0)(),y=(0,a.useRef)(null),Z=(0,a.useRef)(null),N=function(){var t=(0,b.Z)((0,g.Z)().mark((function t(){var n,r;return(0,g.Z)().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(0!==(n=e.current_migration.profile_name).length){t.next=3;break}return t.abrupt("return");case 3:return t.next=5,e.addProfile({name:n});case 5:if((r=t.sent).success){t.next=8;break}return t.abrupt("return",!1);case 8:e.history.push("/migrate/".concat(r.data.id));case 9:case"end":return t.stop()}}),t)})));return function(){return t.apply(this,arguments)}}(),S=e.selected_existing_profile;null===S&&t.length>0&&(S=e.profiles.saved[0].id);var T=function(){return!s&&("save_new"===u?N():function(){var n=un()(t,{id:parseInt(S)});e.overwriteProfile(n.guid,n.name),parseInt(e.match.params.id)!==parseInt(n.id)&&e.history.push("/migrate/".concat(n.id))}())},O=function(e){Z.current.contains(e.target)||R()};(0,a.useEffect)((function(){return m?document.addEventListener("mousedown",O):document.removeEventListener("mousedown",O),function(){document.removeEventListener("mousedown",O)}}),[m]),(0,a.useEffect)((function(){if(m)return function(){};var t="save_new";null===e.profiles.current_profile||e.match.url.includes("unsaved")||(t="overwrite"),p(t)}),[e.profiles.current_profile]);var A=function(e){var t=e.x,n=document.body.clientWidth,r=160;return n<960&&(r=36),n<=782&&(r=10),{leftPos:t,sidebarWidth:r}}(h),C=A.leftPos-A.sidebarWidth-32,R=function(){d(!1)},I=(0,ln.Yz)(m,null,{from:{opacity:0,top:-25},enter:{opacity:1,top:0},leave:{opacity:0,top:-25},onRest:function(){null===e.profiles.current_profile||e.match.url.includes("unsaved")||p("overwrite")}});return a.createElement("span",{ref:Z},a.createElement("button",{style:{marginLeft:15},ref:y,onClick:function(){E((0,dn.b)())&&(v(y.current.getBoundingClientRect()),d(!m))},className:"btn btn-stroke".concat(m?" active":"")},"Save Profile"),I.map((function(n){var i=n.item,s=n.key,l=n.props;return i&&a.createElement(P(),{active:m},a.createElement(ln.q.div,{key:s,style:(0,x.Z)({},l),className:"wpmdb-save-profile save-profile-wrap"},a.createElement("div",{className:"save-profile-box",style:{left:C}},a.createElement("div",{className:"header-row"},a.createElement("button",{className:"save_new"===u?"active":"",onClick:function(){p("save_new")}},(0,w.__)("Create new","wp-migrate-db")),a.createElement("button",{className:"overwrite"===u?"active":"",onClick:function(){p("overwrite")}},(0,w.__)("Overwrite existing","wp-migrate-db"))),a.createElement("div",{className:"row wpmdb-form"},a.createElement("div",{className:"save-inner"},a.createElement(fn,(0,c.Z)({},e,{saved:t,tabOpen:u}))),(null!==S&&"overwrite"===u||"save_new"===u)&&a.createElement("button",{className:"btn btn-sm save-btn",onClick:function(){return T()}},e.renderButtonStatus(e.ui_data,R)),r&&"migration"===r.location&&a.createElement("div",{className:"profile-save-error"},a.createElement(z.Z,{type:"danger"},(0,w.__)("Problem saving profile:","wp-migrate-db"),a.createElement("br",null),a.createElement("code",null,r.message))),a.createElement("div",{className:"close-wrap"},a.createElement("button",{onClick:function(){d(!1)},className:"close-picker"},a.createElement(Ue.r,{"aria-hidden":!0}),a.createElement("span",{className:"screen-reader-text"},(0,w.__)("Close Save Profile Dialog","wp-migrate-db"))))))))})))})),xn=n(76178),kn=n(41459),Zn=(0,i.$j)((function(e){return{panelsOpen:(0,xn.O)("panelsOpen",e)}}))((function(e){var t=e.outdatedSettings,n=e.panelsOpen,r=e.dispatch;(0,a.useEffect)((function(){t.forEach((function(e){n.includes(e)||r({type:kn.kW,payload:e})}))}));var i={media_files:(0,w.__)("Media Files","wp-migrate-db"),post_types:(0,w.__)("Post Types","wp-migrate-db")},s=t.map((function(e){return"<li>".concat(i[e],"</li>")})).join("");return a.createElement(z.Z,{type:"danger"},a.createElement("strong",null,(0,w.__)("Profile Settings Have Changed","wp-migrate-db"))," ","\u2014",(0,w.__)("The profile you are using was imported from an older version of WP Migrate. Please review and update the following settings:","wp-migrate-db"),a.createElement("br",null),a.createElement("br",null),a.createElement("ul",null,(0,E.ZP)(s)))})),Nn=n(39794),Pn=(v.ZP.button(_n||(_n=(0,h.Z)(["\n  border-radius: 3px;\n"]))),v.ZP.div(gn||(gn=(0,h.Z)(["\n  background: #ffffff;\n  border: 1px solid #d6d6d6;\n  box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.05), 0 2px 1px 0 rgba(0, 0, 0, 0.05);\n  border-radius: 6px;\n"])))),Sn=((0,v.ZP)(Pn)(bn||(bn=(0,h.Z)(["\n  padding: 0.75rem;\n  margin-bottom: 0.8rem;\n"]))),v.ZP.span(hn||(hn=(0,h.Z)(["\n  display: block;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n  overflow: hidden;\n  max-width: 100%;\n  font-size: 0.8rem;\n  color: ",";\n"])),Nn.Qp));v.ZP.p(vn||(vn=(0,h.Z)(["\n  color: ",";\n  margin: 1rem 0;\n  line-height: 1.5 !important;\n"])),Nn.qP),v.ZP.div(En||(En=(0,h.Z)(["\n  color: ",";\n  a{\n    color:","\n    text-decoration: underline;\n    \n    &:hover{\n      text-decoration: none;\n    }\n  }\n"])),Nn.qP,Nn.qP);function Tn(){return Tn=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},Tn.apply(this,arguments)}var On,An,Cn,Rn=function(e){return a.createElement("svg",Tn({height:16,viewBox:"0 0 16 16",width:16,xmlns:"http://www.w3.org/2000/svg"},e),wn||(wn=a.createElement("path",{d:"m8.393 11.34 4.945-4.9a.548.548 0 0 0 0-.779.558.558 0 0 0-.785 0L8 10.173 3.448 5.662a.559.559 0 0 0-.786 0 .548.548 0 0 0 0 .779l4.945 4.9a.564.564 0 0 0 .786 0z"})))},In=(n.p,(0,v.ZP)(Pn)(On||(On=(0,h.Z)(["\n  padding: 0.9rem 1.25rem;\n  margin-bottom: 1.1rem;\n  line-height: 1.2rem;\n  a.link {\n    font-size: inherit;\n  }\n  .breadcrumbs {\n    margin-top: -2px;\n  }\n"])))),Dn=function(e){var t=e.recentProfiles,n=(0,i.v9)((function(e){return t?e.profiles.recent:e.profiles.saved})),r=(0,i.v9)((function(e){return e.profiles.current_profile}));return a.createElement(In,null,a.createElement("div",{className:"breadcrumbs"},a.createElement(l.OL,{className:"link",to:"/"},t?(0,w.__)("Unsaved Profiles","wp-migrate-db"):(0,w.__)("Saved Profiles","wp-migrate-db"))," ",a.createElement(Rn,{className:"profile-header-arrow"})," ",a.createElement("b",null,function(e,t){var n=un()(e,{id:parseInt(t)});return n?a.createElement(a.Fragment,null,(0,O.decode)(n.name)):null}(n,r))))},Ln=n(74094),Mn=n(9412),Fn=n(51286),Bn=(n(9253),{selected:null}),jn=a.createContext(Bn),Un=function(e,t){return"SET_SELECTED"===t.type?(0,x.Z)((0,x.Z)({},e),{},{selected:t.payload}):(0,x.Z)({},e)},zn=function(e){var t=e.label,n=e.name,r=e.description,i=e.onClick,s=(0,a.useContext)(jn).state.selected===n;return a.createElement("button",{className:"combo-button-child-container",onClick:i},a.createElement("div",{className:"combo-button-child ".concat(s?"selected":"")},a.createElement("div",{className:"text"},a.createElement("span",{className:"label"},t),a.createElement("span",{className:"description"},r))))},Gn=function(e){var t=e.className,n=e.children,r=e.selected,i=e.setSelected,s=e.expandAriaLabel,c=(0,a.useState)(""),l=(0,k.Z)(c,2),o=l[0],u=l[1],p=(0,a.useState)(null),m=(0,k.Z)(p,2),d=m[0],f=m[1],_=(0,a.useState)([]),g=(0,k.Z)(_,2),b=g[0],h=g[1],v=(0,a.useState)(!1),E=(0,k.Z)(v,2),w=E[0],y=E[1],x=(0,a.useReducer)(Un,Bn),Z=(0,k.Z)(x,2),N=Z[0],S=Z[1],T=function(e){return{type:"SET_SELECTED",payload:e}},O=(0,a.useRef)(null),A=(0,a.useRef)(null),C=function(e){O.current.contains(e.target)||setTimeout((function(){y(!1)}),50)};return(0,a.useEffect)((function(){return w?document.addEventListener("mousedown",C):document.removeEventListener("mousedown",C),function(){document.removeEventListener("mousedown",C)}}),[w]),(0,a.useEffect)((function(){h(a.Children.map(n,(function(e,t){var n=e.props.onClick,s=e.props.name,c=e.props.label;return r||0!==t||(u(c),f((function(){return n})),S(T(s)),i(s)),r&&s===r&&(u(c),f((function(){return n})),S(T(s)),i(s)),a.cloneElement(e,{onClick:function(){u(c),f((function(){return n})),S(T(s)),i(s),y(!1),A.current.blur(),setTimeout((function(){n(),A.current.focus()}),0)}})})))}),[n]),a.createElement(jn.Provider,{value:{state:N,dispatch:S}},a.createElement(P(),{active:w},a.createElement("div",{ref:O,className:"combo-button-container ".concat(t||"")},a.createElement("div",{className:"btn-large"},a.createElement("button",{ref:A,className:"primary-btn",onClick:d},o),a.createElement("button",{"aria-label":s,className:"secondary-btn",onMouseDown:function(e){y(!w)},onKeyDown:function(e){"Enter"!==e.key&&" "!==e.key||y(!w)}},a.createElement(Fn.r,{"aria-hidden":"true",className:w?"expanded":""}))),a.createElement("div",{className:"combo-button-children ".concat(w?"show":"")},b))))},Vn=n(81294),Hn=function(e){var t=e.local_site,n=e.remote_site,r=e.current_migration,s=e.intent,c=e.selectedOption,l=e.onClick,o=(0,i.I0)(),u=(0,a.useState)(c),p=(0,k.Z)(u,2),m=p[0],d=p[1],f=(0,Vn.O)(t,n,r,s);return a.createElement(Gn,{className:"run-migration",selected:null!==m&&void 0!==m?m:"preview",setSelected:d,expandAriaLabel:(0,w.__)("Expand Find & Replace options","wp-migrate-db")},a.createElement(zn,{label:(0,w.__)("Find & Replace","wp-migrate-db"),description:(0,w.__)("Run the full process with no prompt","wp-migrate-db"),name:"run",onClick:function(e){o((0,at.G$)(!1)),l(e)}}),a.createElement(zn,{name:"preview",label:(0,w.__)("Preview Changes","wp-migrate-db"),description:(0,w.__)("Show changes with prompt to save or cancel","wp-migrate-db"),onClick:function(){(0,Mn.isArray)(f)&&f.forEach((function(e){e.includes("_mig_")||o((0,at._s)({table:e,data:[],time:0}))})),o((0,at.g1)())}}))},Wn=(0,i.$j)((function(e){return{remote_site:(0,J.NR)("remote_site",e),local_site:(0,J.NR)("local_site",e),current_migration:e.migrations.current_migration}}))((function(e){var t=e.onClick,n=e.intent,r=e.selectedOption,i=e.local_site,s=e.remote_site,c=e.current_migration;return"find_replace"===n?a.createElement(Hn,{current_migration:c,onClick:t,selectedOption:r,remote_site:s,local_site:i,intent:n}):a.createElement("button",{onClick:t,className:"btn run-migration ".concat(n)},Ft[n])})),Kn=function(e){var t=(0,i.I0)(),n=t((0,Ln.J)()),r=(0,i.v9)((function(e){return e.migrations.current_migration})).status,s=e.match,l=(0,i.v9)((function(e){return e.panels})),o=l.panelsToDisplay,u=l.panelTitles,p=l.panelsOpen,m=(0,i.v9)((function(e){return(0,J._P)("this_uploads_dir",e)})),d=(0,i.v9)((function(e){return e.profiles})),f=(0,i.v9)((function(e){return(0,ae.d)("status",e)})).profile_loading,h=(0,i.v9)((function(e){return e.migrations.current_migration})),v=h.intent,E=h.migration_enabled,y=h.running,x=h.selectedComboOption,Z=(0,a.useState)(!1),N=(0,k.Z)(Z,2),P=N[0],S=N[1],T=(0,_.fX)(d.loaded_profile),O=function(){var e=(0,b.Z)((0,g.Z)().mark((function e(n){return(0,g.Z)().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!y){e.next=3;break}return n.preventDefault(),e.abrupt("return",!1);case 3:if(t({type:"WPMDB_PRE_MIGRATION"}),t((0,dn.b)())){e.next=7;break}return e.abrupt("return",!1);case 7:t({type:"MIGRATION_STARTED"}),t((0,de.Cy)());case 9:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),A=s.params.hasOwnProperty("id"),C=!1;return(null===f&&!1===A||!1===f&&A)&&(C=!0),a.createElement(a.Fragment,null,null!==d.current_profile&&a.createElement(a.Fragment,null,T&&0!==T.length&&a.createElement(Zn,{outdatedSettings:T}),a.createElement(Dn,{profiles:e.match.path.includes("unsaved")?a.createElement("profiles",{className:"recent"}):d.saved,recentProfiles:e.match.path.includes("unsaved"),profileID:e.current_profile})),C&&a.createElement(cn,(0,c.Z)({connecting:n,title:u.action_buttons,panelName:"action_buttons",panelsToShow:o,panelHandler:function(n){if(n.intent===v)return!1;"/migrate"!==e.match.path&&e.history.push({pathname:"/migrate"}),t((0,W.$f)(n))},baseTitle:(0,w.__)("Action","wp-migrate-db"),updateOpenPanels:e.updateOpenPanels,panelState:p,panelSummary:Bt[v]},e)),""===v&&C&&a.createElement(tt,{location:"migrate-tab"}),e.children,"backup_local"===v&&a.createElement("div",{className:"migration-message"},(0,w.__)("An SQL file will be saved to ".concat(m," on your server"),"wp-migrate-db")),v&&a.createElement(a.Fragment,null,(0,dn.a)(r,"EMPTY_MIGRATION_STAGES")&&a.createElement(z.Z,{type:"danger"},(0,w.__)("Select at least one type of content to be migrated.","wp-migrate-db")),E&&!n&&a.createElement("div",{className:"migration-buttons"},a.createElement(Wn,{onClick:O,intent:v,selectedOption:x})," ",a.createElement(yn,(0,c.Z)({title:"Save Profile",intent:v,saveProfileOpen:P,setSaveProfileOpen:S,panelName:"save_profile",baseTitle:(0,w.__)("Save Profile","wp-migrate-db")},e)))))},qn=["ChildPanels"],Yn=(0,v.ZP)(ot.Q)(An||(An=(0,h.Z)(["\n  position: relative;\n  top: 5px;\n  #el_6X7lquFKkl {\n    fill: #b3aeae;\n  }\n"]))),Jn=function(e){var t=e.ChildPanels,n=(0,Z.Z)(e,qn),r=(0,i.I0)(),s=n.match,c=n.current_migration,l=n.profileData,o=l.profile_loading,u=l.profile_load_error,p=c.running;return(0,a.useEffect)((function(){s.params.id&&r(te(s))}),[]),(0,a.useEffect)((function(){document.body.style.overflow=p?"hidden":"auto"}),[p]),o?a.createElement("div",{className:"padded-container"},u?a.createElement("h2",null,u):a.createElement("h2",null,(0,w.__)("Loading profile... ","wp-migrate-db"),a.createElement("span",null,a.createElement(Yn,null)))):a.createElement(a.Fragment,null,a.createElement(Kn,n,t),p&&a.createElement(Rt,{running:p}))},Qn=(0,i.$j)((function(e){return{current_migration:e.migrations.current_migration,profileData:e.profiles}}))(a.memo(Jn)),Xn=function(e){var t=(0,a.useState)(),n=(0,k.Z)(t,2),r=n[0],i=n[1],s=e.status.backup_deleting||e.status.backup_downloading,c=function(e,t){if(t)return!1;!function(e){i(e)}(e)},l=function(){i("")},o=function(){var t=(0,b.Z)((0,g.Z)().mark((function t(n,r){var a;return(0,g.Z)().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(!s){t.next=2;break}return t.abrupt("return",!1);case 2:return a=(0,_.CX)(n.raw_name),t.next=5,e.deleteBackup(r,(0,_.st)(n.raw_name),a);case 5:t.sent&&i("");case 7:case"end":return t.stop()}}),t)})));return function(e,n){return t.apply(this,arguments)}}(),u=e.backups.map((function(t,n){var i=(0,x.Z)((0,x.Z)({},e),{},{deleteItem:o,cancelDelete:l,item:t,index:n});return a.createElement("tr",{key:n,className:"flex-container".concat(e.status.backup_deleting?" in-progress":"")},a.createElement("td",null,t.modified),a.createElement("td",{className:"table-col-action"},t.name),a.createElement("td",null,a.createElement("button",{className:"link",onClick:function(){return e.downloadBackup(t.raw_name)}},(0,w.__)("Download","wp-migrate-db"))),a.createElement("td",null,a.createElement("button",{className:"action-btn delete-backup link text-error",onClick:function(){return c(n)}},(0,w.__)("Delete","wp-migrate-db"))),r===n&&a.createElement("td",{className:"relative"},a.createElement(R,i)))}));return a.createElement("table",{className:"backups-table"},a.createElement("tbody",null,u))};n(36795);function $n(){return $n=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},$n.apply(this,arguments)}var er=function(e){return a.createElement("svg",$n({height:36,viewBox:"0 0 36 36",width:36,xmlns:"http://www.w3.org/2000/svg"},e),Cn||(Cn=a.createElement("path",{d:"M5.082 20A13.648 13.648 0 0 1 5 18.5C5 11.044 11.044 5 18.5 5S32 11.044 32 18.5 25.956 32 18.5 32a13.45 13.45 0 0 1-9.014-3.45l2.125-2.126A10.46 10.46 0 0 0 18.5 29C24.299 29 29 24.299 29 18.5S24.299 8 18.5 8 8 12.701 8 18.5c0 .51.036 1.01.106 1.5H12l-5.5 5L1 20zM19 18.994l3.998 3.013-1.204 1.597L17.011 20H17v-8h2z",fill:"#b2b2b2"})))},tr=(n.p,n(52089)),nr="BACKUPS_LOADING",rr="GET_BACKUPS",ar="BACKUP_DELETING",ir="BACKUP_DOWNLOADING",sr="DOWNLOAD_BACKUP",cr="BACKUPS_ERROR",lr={files:[],status:{backups_loading:!0,backup_deleting:!1,backup_downloading:!1,backups_error:""},ui:{backups_location:window.wpmdb_data.this_upload_dir_long}},or=(0,tr.Lq)(lr,{BACKUPS_LOADING:function(e,t){return e.status=(0,x.Z)((0,x.Z)({},e.status),{},{backups_loading:!0,backup_deleting:!1,backup_downloading:!1,backups_error:""}),e},GET_BACKUPS:function(e,t){return e.files=t.payload,e.status=(0,x.Z)((0,x.Z)({},e.status),{},{backups_loading:!1,backups_error:!1}),e},BACKUP_DELETING:function(e,t){return e.status=(0,x.Z)((0,x.Z)({},e.status),{},{backup_deleting:!0,backups_error:""}),e},DELETE_BACKUP:function(e,t){return e.files.splice(t.payload,1),e.status.backup_deleting=!1,e},BACKUP_DOWNLOADING:function(e,t){return e.status=(0,x.Z)((0,x.Z)({},e.status),{},{backup_downloading:!0,backups_error:""}),e},DOWNLOAD_BACKUP:function(e,t){return e.status.backup_downloading=!1,e},BACKUPS_ERROR:function(e,t){return e.status=(0,x.Z)((0,x.Z)({},e.status),{},{backups_loading:!1,backup_deleting:!1,backup_downloading:!1,backups_error:t.payload}),e}});function ur(e){return function(){var t=(0,b.Z)((0,g.Z)().mark((function t(n,r){return(0,g.Z)().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return n((0,G.m)(cr,(0,mt.Y)(e))),t.abrupt("return",!1);case 2:case"end":return t.stop()}}),t)})));return function(e,n){return t.apply(this,arguments)}}()}var pr,mr=(0,i.$j)((function(e){return{backups:e.backups.files,backups_location:e.backups.ui.backups_location,status:e.backups.status}}),{getBackups:function(e){return function(){var e=(0,b.Z)((0,g.Z)().mark((function e(t){var n;return(0,g.Z)().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t((0,G.m)(nr)),e.prev=1,e.next=4,(0,_.op)("/get-backups",{},!1,t);case 4:n=e.sent,e.next=10;break;case 7:return e.prev=7,e.t0=e.catch(1),e.abrupt("return",t(ur(e.t0)));case 10:if(n.success){e.next=12;break}return e.abrupt("return",t(ur(n)));case 12:return t((0,G.m)(rr,n.data)),e.abrupt("return",!0);case 14:case"end":return e.stop()}}),e,null,[[1,7]])})));return function(t){return e.apply(this,arguments)}}()},deleteBackup:function(e,t,n){return function(){var r=(0,b.Z)((0,g.Z)().mark((function r(a){var i;return(0,g.Z)().wrap((function(r){for(;;)switch(r.prev=r.next){case 0:return a((0,G.m)(ar)),r.prev=1,r.next=4,(0,_.op)("/delete-backup",{path:t,isCompressed:n});case 4:i=r.sent,r.next=10;break;case 7:return r.prev=7,r.t0=r.catch(1),r.abrupt("return",a(ur(r.t0)));case 10:if(i.success){r.next=12;break}return r.abrupt("return",a(ur(i)));case 12:return a({type:"DELETE_BACKUP",payload:e}),r.abrupt("return",!0);case 14:case"end":return r.stop()}}),r,null,[[1,7]])})));return function(e){return r.apply(this,arguments)}}()},downloadBackup:function(e){return function(){var t=(0,b.Z)((0,g.Z)().mark((function t(n){var r,a,i;return(0,g.Z)().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return n((0,G.m)(ir)),a=(0,_.CX)(e),i=(0,_.st)(e),t.prev=3,t.next=6,(0,_.op)("/get-backup",{path:i,isCompressed:a});case 6:r=t.sent,t.next=12;break;case 9:return t.prev=9,t.t0=t.catch(3),t.abrupt("return",n(ur(t.t0)));case 12:if(r.success){t.next=14;break}return t.abrupt("return",n(ur(r)));case 14:return window.location.href=r.data.redirect,n({type:sr}),t.abrupt("return",!0);case 17:case"end":return t.stop()}}),t,null,[[3,9]])})));return function(e){return t.apply(this,arguments)}}()},createNewBackup:function(){return function(e){e({type:"RESET_APP"}),e((0,W.$f)({panel:"database",title:q.B.backup_local,intent:"backup_local"}))}}})((function(e){return(0,a.useEffect)((function(){e.getBackups()}),[]),a.createElement("section",{className:"wpmdb-backups"},a.createElement("div",{className:"backups-header"},a.createElement(l.rU,{className:"btn btn-sm create-new-backup",to:"/migrate",replace:!0,onClick:function(){return e.createNewBackup()}},a.createElement(U,{"aria-hidden":!0})," ",(0,w.__)("Create New","wp-migrate-db")),a.createElement("p",{className:"backups-save-location"},(0,w.__)("Backups are currently saved to","wp-migrate-db"),"\xa0",a.createElement("strong",null,e.backups_location))),e.status.backups_error&&a.createElement(z.Z,{type:"danger"},e.status.backups_error),!0===e.status.backups_loading?a.createElement("div",{className:"backups-loading"},a.createElement("hr",null),a.createElement("p",null,(0,w.__)("Backups loading, please wait...","wp-migrate-db")," ",a.createElement(ot.Q,{className:"backups-spinner"}))):e.backups.length>0?a.createElement("div",{className:"container-shadow table"},a.createElement("div",{className:"flex-container"},a.createElement("h2",{className:"table-heading"},(0,w.__)("Saved Backups","wp-migrate-db"))),a.createElement("div",{className:"table-divider-line bg-grey-light"}),a.createElement(Xn,{backups:e.backups,deleteBackup:e.deleteBackup,downloadBackup:e.downloadBackup,status:e.status})):a.createElement("div",null,a.createElement("hr",null),a.createElement("p",{className:"no-items"},a.createElement(er,{className:"no-backups-icon"}),(0,w.__)("No backups found. Go ahead and create your first one.","wp-migrate-db"))))})),dr=n(10937),fr=(n(86393),["addon","name","readMoreLink"]),_r={multisiteTools:(0,w.__)("Subsite options can be found within migration profiles for Push, Pull, Export, and Find & Replace.","wp-migrate-db"),themeFiles:(0,w.__)("Themes can now be selected within Push and Pull migration profiles.","wp-migrate-db"),pluginFiles:(0,w.__)("Plugins can now be selected within Push and Pull migration profiles.","wp-migrate-db"),otherFiles:(0,w.__)("Other files can now be selected within Push and Pull migration profiles.","wp-migrate-db"),mediaFiles:(0,w.__)("Media uploads can now be selected within Push and Pull migration profiles.","wp-migrate-db"),cli:(0,w.__)(" Additional commands to push, pull, and import are now available when using WP-CLI.","wp-migrate-db")},gr=(0,w.gB)((0,w.__)('Feature is available, but will not receive updates or support until you <a href="%s" target="_blank" rel="noopener noreferrer">log in to My Account</a> and renew your license.',"wp-migrate-db"),"https://deliciousbrains.com/my-account"),br=function(e){var t=e.addon,n=e.name,r=e.readMoreLink,s=(0,Z.Z)(e,fr),c=null,l=!1,o=(0,i.v9)((function(e){return e.dbi_api_data}));if((0,_.Yu)()){var u=o.licence.licence_status;l="subscription_expired"===u}if((0,_.Yu)()){var p=(0,i.v9)((function(e){return e.dbi_api_data}));c=p.api_data}var m=function(){return(0,_.Yu)()&&c&&c.addons_available_list?c.hasOwnProperty("addons_available_list")&&c.addons_available_list.hasOwnProperty(t)?c.hasOwnProperty("addon_content")?a.createElement(z.Z,{type:l?"warning":"success",className:"installed-activated-text ".concat(l?"license-expired":"")},(0,E.ZP)((0,w.gB)("<b>%s</b> - %s",(0,w.__)(l?(0,w.__)("License Expired","wp-migrate-db"):(0,w.__)("Activated","wp-migrate-db"),"wp-migrate-db"),l?gr:_r[n]))):null:a.createElement("div",{className:"addon-upgrade-container"},a.createElement("a",{href:"https://deliciousbrains.com/wp-migrate-db-pro/upgrade/?utm_campaign=WP%2BMigrate%2BDB%2BPro%2BUpgrade&utm_source=MDB%2BFree&utm_medium=insideplugin",target:"_blank",rel:"noopener noreferrer",className:"btn btn-sm addon-btn"},(0,w.__)("Upgrade","wp-migrate-db")),a.createElement("div",{className:"edit-key-link"},a.createElement("span",null,"or "),a.createElement("a",{href:"#settings/enter"},(0,w.__)("Edit License Key","wp-migrate-db")))):null};return a.createElement("div",{className:"addon-container container-shadow"},s.icon,a.createElement("div",{className:"addon-content"},a.createElement("h2",{className:"addon-title"},s.title),a.createElement("p",null,s.desc," ",r&&a.createElement("a",{className:"more-details-link",href:r,target:"_blank",rel:"noopener noreferrer"},(0,E.ZP)((0,w.__)("More&nbsp;Details&nbsp;\u2192","wp-migrate-db"))))),(0,_.Yu)()&&a.createElement("div",{className:"addon-bottom-controls"},a.createElement(m,{readMoreLink:s.readMoreLink})))};function hr(){return hr=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},hr.apply(this,arguments)}var vr,Er=function(e){return a.createElement("svg",hr({height:64,viewBox:"0 0 64 64",width:64,xmlns:"http://www.w3.org/2000/svg"},e),pr||(pr=a.createElement("g",{fill:"#236de7",fillRule:"evenodd"},a.createElement("circle",{cx:32,cy:34,r:8}),a.createElement("path",{d:"M7 19h50v30H7zm3 3v24h44V22z",fillRule:"nonzero"}),a.createElement("path",{d:"M27 15h11l2 4H25zM11 15h6v3h-6z"}),a.createElement("circle",{cx:16,cy:28,r:2}))))};n.p;function wr(){return wr=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},wr.apply(this,arguments)}var yr,xr=function(e){return a.createElement("svg",wr({height:64,viewBox:"0 0 64 64",width:64,xmlns:"http://www.w3.org/2000/svg"},e),vr||(vr=a.createElement("g",{fill:"#236de7",fillRule:"evenodd"},a.createElement("path",{d:"M8 15h48v34H8zm3 6v25h42V21z"}),a.createElement("path",{d:"M30 39h18v3H30zM22.208 34.051l-6.363-6.364 2.12-2.121 8.486 8.485-8.485 8.485-2.121-2.121z"}))))};n.p;function kr(){return kr=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},kr.apply(this,arguments)}var Zr,Nr=function(e){return a.createElement("svg",kr({height:64,viewBox:"0 0 64 64",width:64,xmlns:"http://www.w3.org/2000/svg"},e),yr||(yr=a.createElement("path",{d:"M28.679 17.821a50.65 50.65 0 0 0-3.018-1.348C27.13 12.572 29.253 10 32 10c2.676 0 4.76 2.44 6.224 6.173-.94.396-1.905.837-3.002 1.385-1.122-2.939-2.403-4.265-3.223-4.265-.844 0-2.176 1.406-3.321 4.528zM32 51.707c-.82 0-2.1-1.326-3.223-4.265-1.106.553-2.064.99-3.001 1.385C27.24 52.56 29.324 55 32 55c2.748 0 4.87-2.572 6.34-6.473a50.588 50.588 0 0 1-3.018-1.348C34.177 50.3 32.844 51.707 32 51.707zm-8.988-4.154c-3.661 1.323-9.123 2.61-11.294-.594-1.072-1.584-1.473-4.628 2.71-10.14 1.11-1.461 2.395-2.898 3.699-4.216 2.923-2.913 6.217-5.718 10.508-8.622-2.066-1.118-4.32-2.188-6.521-2.984-2.252-.814-4.235-1.266-5.755-1.266-.97 0-1.69.203-1.927.542-.25.36-.263 1.467.85 3.454a3.622 3.622 0 1 1-2.752 1.818c-2.12-3.675-1.682-5.886-.8-7.154.62-.89 1.926-1.952 4.629-1.952 4.8 0 11.266 3.112 15.43 5.523 4.226-2.555 10.913-5.925 15.852-5.925 2.714 0 4.022 1.09 4.64 2.004 1.073 1.584 1.474 4.628-2.71 10.14-5.887 7.756-17.594 16.134-26.56 19.372zM46.949 26.19c2.874-3.786 2.962-5.777 2.607-6.302-.24-.354-.938-.558-1.915-.558-3.726 0-9.403 2.736-12.67 4.588a75.558 75.558 0 0 1 6.721 4.907 55.033 55.033 0 0 1-2.426 2.27 72.068 72.068 0 0 0-7.446-5.27 71.933 71.933 0 0 0-8.787 6.659 70.624 70.624 0 0 0 7.105 5.386 59.318 59.318 0 0 1-3.154 1.838l-.026-.019a72.861 72.861 0 0 1-6.33-4.935c-5.38 5.397-6.896 9.307-6.184 10.36.24.354.939.558 1.915.558 4.486 0 11.608-3.782 15.82-6.495 5.173-3.33 11.072-8.114 14.77-12.987zm4.537 13.295a3.622 3.622 0 1 0-2.71 1.894c1.055 1.921 1.038 2.996.791 3.35-.235.338-.955.54-1.926.54-3.058 0-7.318-1.771-10.06-3.108a80.287 80.287 0 0 1-3.167 2.098c3.677 1.945 9.084 4.303 13.227 4.303 2.703 0 4.009-1.062 4.629-1.952.88-1.264 1.317-3.468-.784-7.125z",fill:"#236de7"})))};n.p;function Pr(){return Pr=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},Pr.apply(this,arguments)}var Sr,Tr=function(e){return a.createElement("svg",Pr({height:64,viewBox:"0 0 64 64",width:64,xmlns:"http://www.w3.org/2000/svg"},e),Zr||(Zr=a.createElement("g",{fill:"#236de7",fillRule:"evenodd"},a.createElement("path",{d:"M27 24.5c0-.828 2.239-1.5 5-1.5s5 .672 5 1.5v3c0 .828-2.239 1.5-5 1.5s-5-.672-5-1.5v-3zm5 1.5c1.657 0 3-.448 3-1s-1.343-1-3-1-3 .448-3 1 1.343 1 3 1zM27 14.5c0-.828 2.239-1.5 5-1.5s5 .672 5 1.5v3c0 .828-2.239 1.5-5 1.5s-5-.672-5-1.5v-3zm5 1.5c1.657 0 3-.448 3-1s-1.343-1-3-1-3 .448-3 1 1.343 1 3 1zM17 19.5c0-.828 2.239-1.5 5-1.5s5 .672 5 1.5v3c0 .828-2.239 1.5-5 1.5s-5-.672-5-1.5v-3zm5 1.5c1.657 0 3-.448 3-1s-1.343-1-3-1-3 .448-3 1 1.343 1 3 1zM37 19.5c0-.828 2.239-1.5 5-1.5s5 .672 5 1.5v3c0 .828-2.239 1.5-5 1.5s-5-.672-5-1.5v-3zm5 1.5c1.657 0 3-.448 3-1s-1.343-1-3-1-3 .448-3 1 1.343 1 3 1zM33 32l24-12v12L33 44zM33 46l24-12v12L33 58zM31 32 7 20v12l24 12zM31 46 7 34v12l24 12z"}),a.createElement("path",{d:"M54 21.5 32 10 10 21.5 7 20 32 7l25 13z"}))))};n.p;function Or(){return Or=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},Or.apply(this,arguments)}var Ar,Cr=function(e){return a.createElement("svg",Or({width:64,height:64,viewBox:"0 0 64 64",fill:"none",xmlns:"http://www.w3.org/2000/svg"},e),Sr||(Sr=a.createElement("path",{d:"M29.467 52h5.066v-5.467L44 36.133V23.4H20v12.733l9.467 10.4V52Zm-4 4v-7.867L16 37.733V23.4c0-1.111.389-2.056 1.167-2.833.777-.778 1.722-1.167 2.833-1.167h4.8l-2 2V8h4v11.4h10.4V8h4v13.4l-2-2H44c1.111 0 2.056.389 2.833 1.167.778.777 1.167 1.722 1.167 2.833v14.333l-9.467 10.4V56H25.467Z",fill:"#2572E4"})))};n.p;function Rr(){return Rr=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},Rr.apply(this,arguments)}var Ir,Dr=function(e){return a.createElement("svg",Rr({width:64,height:64,viewBox:"0 0 64 64",fill:"none",xmlns:"http://www.w3.org/2000/svg"},e),Ar||(Ar=a.createElement("path",{d:"M11.349 49.72c-.856 0-1.628-.343-2.316-1.032C8.344 48 8 47.228 8 46.372V17.35c0-.856.344-1.628 1.033-2.316.688-.689 1.46-1.033 2.316-1.033h15.684l3.348 3.349h18.921c.856 0 1.628.344 2.317 1.032.688.689 1.032 1.46 1.032 2.317h-23.72l-3.35-3.35H11.35v29.024l5.693-22.325H56l-5.972 23.107c-.223.892-.633 1.544-1.228 1.953-.595.41-1.358.614-2.288.614H11.349Zm3.516-3.348h31.926l4.688-18.977H19.554l-4.689 18.977Zm0 0 4.688-18.977-4.688 18.977ZM11.35 20.698v-3.35 3.35Z",fill:"#2572E4"})))},Lr=(n.p,"true"===(0,_.Yu)()?"MDB%2BPaid":"MDB%2BFree"),Mr=function(e){return a.createElement("div",{className:"addons-container"},a.createElement(br,(0,c.Z)({icon:a.createElement(Nr,{className:"addon-icon"}),addon:"wp-migrate-db-pro-multisite-tools",name:"multisiteTools",title:(0,w.__)("Multisite Tools","wp-migrate-db-pro"),desc:(0,w.__)("Export, push, and pull subsites from multisite networks.","wp-migrate-db"),readMoreLink:"https://deliciousbrains.com/wp-migrate-db-pro/doc/multisite-tools-addon/?utm_campaign=addons%2Binstall&utm_medium=insideplugin&utm_source=".concat(Lr)},e)),a.createElement(br,(0,c.Z)({icon:a.createElement(Er,{className:"addon-icon"}),addon:"wp-migrate-db-pro-media-files",name:"mediaFiles",title:(0,w.__)("Media Uploads","wp-migrate-db"),desc:(0,w.__)("Push and pull media uploads between two WordPress installs.","wp-migrate-db"),readMoreLink:"https://deliciousbrains.com/wp-migrate-db-pro/doc/media-files-addon/?utm_campaign=addons%2Binstall&utm_medium=insideplugin&utm_source=".concat(Lr)},e)),a.createElement(br,(0,c.Z)({icon:a.createElement(Tr,{className:"addon-icon"}),addon:"wp-migrate-db-pro-theme-plugin-files",name:"themeFiles",title:(0,w.__)("Themes","wp-migrate-db"),desc:(0,w.__)("Push and pull theme files between two WordPress installs.","wp-migrate-db"),readMoreLink:"https://deliciousbrains.com/wp-migrate-db-pro/doc/theme-plugin-files-addon/?utm_campaign=addons%2Binstall&utm_medium=insideplugin&utm_source=".concat(Lr)},e)),a.createElement(br,(0,c.Z)({icon:a.createElement(Cr,{className:"addon-icon"}),addon:"wp-migrate-db-pro-theme-plugin-files",name:"pluginFiles",title:(0,w.__)("Plugins & MU-Plugins","wp-migrate-db"),desc:(0,w.__)("Push and pull plugin files and mu-plugin files between two WordPress installs.","wp-migrate-db"),readMoreLink:"https://deliciousbrains.com/wp-migrate-db-pro/doc/theme-plugin-files-addon/?utm_campaign=addons%2Binstall&utm_medium=insideplugin&utm_source=".concat(Lr)},e)),a.createElement(br,(0,c.Z)({icon:a.createElement(Dr,{className:"addon-icon"}),addon:"wp-migrate-db-pro-theme-plugin-files",name:"otherFiles",title:(0,w.__)("Other Files","wp-migrate-db"),desc:(0,w.__)("Push and pull all other files such as languages from the wp-content directory.","wp-migrate-db")},e)),a.createElement(br,(0,c.Z)({icon:a.createElement(xr,{className:"addon-icon"}),addon:"wp-migrate-db-pro-cli",name:"cli",title:(0,w.__)("WP-CLI Integration","wp-migrate-db"),desc:(0,w.__)("Integrates with WP-CLI to run custom migrations and saved profiles from the command line.","wp-migrate-db"),readMoreLink:"https://deliciousbrains.com/wp-migrate-db-pro/doc/cli-addon/?utm_campaign=addons%2Binstall&utm_medium=insideplugin&utm_source=".concat(Lr)},e)))},Fr=(0,i.$j)((function(e){var t=e.dbi_api_data;return{settings:e.settings,api_data:t.api_data,license:t.licence,licence_errors:t.license_errors}}),{checkLicence:dr.T3})((function(e){var t=e.api_data,n=window.wpmdb_data.is_pro,r=e.settings,i=r.masked_licence,s=r.license_constant,c=(0,a.useState)(!1),l=(0,k.Z)(c,2),o=l[0],u=l[1];return(0,a.useEffect)((function(){var t=function(){var t=(0,b.Z)((0,g.Z)().mark((function t(){return(0,g.Z)().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,e.checkLicence();case 2:u(!0);case 3:case"end":return t.stop()}}),t)})));return function(){return t.apply(this,arguments)}}();"true"===n&&(i||s)?t():u(!0)}),[]),a.createElement("section",{className:"wpmdb-addons"},o||t.hasOwnProperty("addon_content")?a.createElement(a.Fragment,null,a.createElement(Mr,null)):a.createElement("p",null,(0,w.__)("Fetching upgrades, please wait...","wp-migrate-db")," ",a.createElement(ot.Q,{className:"addons-tab-spinner"})))}));n(72054);function Br(){return Br=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},Br.apply(this,arguments)}var jr=function(e){return a.createElement("svg",Br({xmlns:"http://www.w3.org/2000/svg",width:32,height:32,viewBox:"0 0 32 32"},e),Ir||(Ir=a.createElement("path",{fill:"#236DE7",d:"M16 0C7.163 0 0 7.163 0 16s7.163 16 16 16 16-7.163 16-16S24.837 0 16 0Zm7.656 17.285-9.544 6.565a1.565 1.565 0 0 1-1.61.097 1.564 1.564 0 0 1-.834-1.381V9.434a1.561 1.561 0 0 1 2.444-1.284l9.544 6.565a1.56 1.56 0 0 1 0 2.57Z"})))},Ur=(n.p,n(80855)),zr=function(e){var t=(0,a.useState)("almfii0s7j"),n=(0,k.Z)(t,2),r=n[0],i=n[1],s=function(e,t){"Enter"!==t.key&&" "!==t.key||i(e)};return a.createElement(a.Fragment,null,a.createElement(Ur.Z,{videoID:r}),a.createElement("div",{className:"video-container container-shadow flex-container",onClick:function(){return i("almfii0s7j")},onKeyPress:function(e){s("almfii0s7j",e)},tabIndex:0},a.createElement("div",{className:"column"},a.createElement(jr,null)),a.createElement("div",{className:"column"},a.createElement("h3",null,(0,w.__)("UI Walkthrough","wp-migrate-db")),a.createElement("p",null,(0,w.__)("A brief walkthrough of the WP Migrate plugin showing all of the different options and explaining them.","wp-migrate-db")))),a.createElement("div",{className:"video-container container-shadow flex-container",onClick:function(){return i("8uq5tj0r5k")},onKeyPress:function(e){s("8uq5tj0r5k",e)},tabIndex:0},a.createElement("div",{className:"column"},a.createElement(jr,null)),a.createElement("div",{className:"column"},a.createElement("h3",null,(0,w.__)("Pulling Live Data Into Your Local Development Environment","wp-migrate-db")),a.createElement("p",null,(0,w.__)("This screencast demonstrates how you can pull data from a remote, live WordPress install and update the data in your local development environment.","wp-migrate-db")))),a.createElement("div",{className:"video-container container-shadow flex-container",onClick:function(){return i("77bpg5x9xb")},onKeyPress:function(e){s("77bpg5x9xb",e)},tabIndex:0},a.createElement("div",{className:"column"},a.createElement(jr,null)),a.createElement("div",{className:"column"},a.createElement("h3",null,(0,w.__)("Pushing Local Development Data to a Live Environment","wp-migrate-db")),a.createElement("p",null,(0,w.__)("This screencast demonstrates how you can push a local WordPress database you've been using for development to a live environment.","wp-migrate-db")))),a.createElement("div",{className:"video-container container-shadow flex-container",onClick:function(){return i("kc7t308qu9")},onKeyPress:function(e){s("kc7t308qu9",e)},tabIndex:0},a.createElement("div",{className:"column"},a.createElement(jr,null)),a.createElement("div",{className:"column"},a.createElement("h3",null,(0,w.__)("WP Migrate Media Uploads","wp-migrate-db")),a.createElement("p",null,(0,w.__)("This screencast demonstrates how WP Migrate allows you to migrate media uploads by choosing all uploads or new uploads from a specific date.","wp-migrate-db")))),a.createElement("div",{className:"video-container container-shadow flex-container",onClick:function(){return i("swrxgaff82")},onKeyPress:function(e){s("swrxgaff82",e)},tabIndex:0},a.createElement("div",{className:"column"},a.createElement(jr,null)),a.createElement("div",{className:"column"},a.createElement("h3",null,(0,w.__)("WP Migrate Themes and Plugins","wp-migrate-db")),a.createElement("p",null,(0,w.__)("This screencast demonstrates how to migrate themes and plugins.","wp-migrate-db")))),a.createElement("div",{className:"video-container container-shadow flex-container",onClick:function(){return i("q4uu8j44hh")},onKeyPress:function(e){s("q4uu8j44hh",e)},tabIndex:0},a.createElement("div",{className:"column"},a.createElement(jr,null)),a.createElement("div",{className:"column"},a.createElement("h3",null,(0,w.__)("WP Migrate CLI","wp-migrate-db")),a.createElement("p",null,(0,w.__)("This screencast demonstrates how CLI commands enable the full suite of WP Migrate functionality from your command-line terminal.","wp-migrate-db")))))},Gr={email:(0,w.__)("Please select your email address.","wp-migrate-db"),subject:(0,w.__)("Please enter a subject.","wp-migrate-db"),message:(0,w.__)("Please enter a message.","wp-migrate-db"),"remote-diagnostic-content":(0,w.__)("Please paste in the Diagnostic Info &amp; Error Log from your <strong>remote site.</strong>","wp-migrate-db"),"same-diagnostic-log":(0,w.__)("Looks like you pasted the local Diagnostic Info &amp; Error Log into the textbox for the remote info. Please get the info for your <strong>remote site</strong> and paste it in, or just uncheck the second checkbox if you&#8217;d rather not include your remote site info.","wp-migrate-db")};function Vr(e,t,n){e({type:"RESET_ERRORS"});var r=["email","subject","message"];t.fields.hasOwnProperty("remote-diagnostic")&&t.fields["remote-diagnostic"]&&r.push("remote-diagnostic-content");var a=!0;if(r.forEach((function(n){t.fields.hasOwnProperty(n)&&0!==t.fields[n].length||(a=!1,e({type:"SET_ERROR",field_name:n,message:Gr[n]}))})),Object.keys(t.fields).includes("remote-diagnostic-content")){var i=t.fields["remote-diagnostic-content"].split("\n"),s=n.split("\n"),c=i[0]||"",l=s[0]||"";c.length>0&&l.length>0&&l.trim()===c.trim()&&(e({type:"SET_ERROR",field_name:"remote-diagnostic-content",message:Gr["same-diagnostic-log"]}),a=!1)}if(!a)return!0}var Hr=function(){var e=(0,b.Z)((0,g.Z)().mark((function e(t,n,r,a,i){var s,c,l,o,u,p,m;return(0,g.Z)().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(t.preventDefault(),!Vr(r,n,a)){e.next=4;break}return e.abrupt("return");case 4:return s=n.fields,c=s.email,l=s.subject,o=s.message,u={email:c,subject:l,message:o,"local-diagnostic":n.fields["local-diagnostic"]||!1,"remote-diagnostic":n.fields["remote-diagnostic"]||!1,"remote-diagnostic-content":n.fields["remote-diagnostic-content"]||"","local-diagnostic-content":n.fields["local-diagnostic"]?a:""},r({type:"SET_STATUS",status:"sending"}),e.prev=7,e.next=10,(0,_.vP)(i,u);case 10:p=e.sent,e.next=17;break;case 13:return e.prev=13,e.t0=e.catch(7),r({type:"SET_API_ERROR",field_name:"network_error",message:e.t0.message}),e.abrupt("return");case 17:if(!p.hasOwnProperty("errors")){e.next=21;break}return m=Object.keys(p.errors),r({type:"SET_API_ERROR",field_name:m[0],message:p.errors[m[0]]}),e.abrupt("return");case 21:p.hasOwnProperty("success")&&1===p.success&&r({type:"SET_STATUS",status:"success"});case 22:case"end":return e.stop()}}),e,null,[[7,13]])})));return function(t,n,r,a,i){return e.apply(this,arguments)}}(),Wr=function(e){var t=e.diagnosticLog,n=e.state,r=e.dispatch,i=e.api_data,s=e.licenceStatus,c=Object.keys(n.errors).length>0,l=(0,a.useState)(""),o=(0,k.Z)(l,2),u=o[0],p=o[1],m=n.fields;(0,a.useEffect)((function(){var e=i.hasOwnProperty("support_contacts")&&1===i.support_contacts.length?i.support_contacts[0]:m.email;r({type:"SET_FIELD",field_name:"email",val:e}),p(e)}),[]);var d=function(e){var t=e.target,n="checkbox"===t.type?t.checked:t.value,a=t.name;r({type:"SET_FIELD",field_name:a,val:n})};if("active_licence"!==s||!i.support_contacts)return null;var f,_=i.license_name,g=i.support_email,b=Object.keys(n.errors);return b.length>0&&(f=a.createElement(z.Z,{type:"danger"},(0,E.ZP)(n.errors[b[0]]))),"success"===n.status?a.createElement(a.Fragment,null,a.createElement(z.Z,{type:"success"},a.createElement("p",{style:{margin:0}},(0,E.ZP)((0,w.__)("Thanks for submitting your support request.<br />We'll be in touch soon.","wp-migrate-db")))),a.createElement("div",{className:"diagnostic-log success"},e.Log)):a.createElement(a.Fragment,null,a.createElement("p",{className:"valid-license"},(0,E.ZP)((0,w.gB)((0,w.__)("You have an active <strong>%s</strong> license. You will get front-of-the-line email support service when submitting the form below.","wp-migrate-db"),_))),a.createElement("form",{className:"help-form wpmdb-form".concat(c?" error":"")},a.createElement("div",{className:"field from"},a.createElement("div",{className:"select-email"},a.createElement("label",null,"From:"),a.createElement("div",null,a.createElement("select",{name:"email",onChange:d,value:u},a.createElement("option",{value:""},(0,w.__)("Select your email address...","wp-migrate-db")),i.support_contacts.map((function(e,t){return a.createElement("option",{value:e,key:t},e)}))),a.createElement("p",{className:"note"},(0,E.ZP)((0,w.gB)((0,w.__)('Replies will be sent to this email address.<br />Update your name &amp; email in <a href="%s" target="_blank" rel="noopener noreferrer">My Account</a>',"wp-migrate-db"),"https://deliciousbrains.com/my-account")))))),a.createElement("div",{className:"field subject"},a.createElement("input",{type:"text",name:"subject",placeholder:"Subject",onChange:d,value:m.subject||""})),a.createElement("div",{className:"field email-message"},a.createElement("textarea",{name:"message",placeholder:"Message",onChange:d,value:n.message})),a.createElement("div",{className:"field checkbox local-diagnostic"},a.createElement("label",null,a.createElement("input",{type:"checkbox",name:"local-diagnostic",onChange:d,checked:m["local-diagnostic"]||!1}),(0,E.ZP)((0,w.__)("Attach <strong>this site&#8217;s</strong> Diagnostic Info &amp; Error Log (below)","wp-migrate-db")))),a.createElement("div",{className:"field checkbox remote-diagnostic".concat(m["remote-diagnostic"]?" checked":"")},a.createElement("label",null,a.createElement("input",{type:"checkbox",name:"remote-diagnostic",onChange:d,checked:m["remote-diagnostic"]||!1}),(0,E.ZP)((0,w.__)("Attach the <strong>remote site&#8217;s</strong> Diagnostic Info &amp; Error Log","wp-migrate-db")))),m["remote-diagnostic"]&&a.createElement("div",{className:"field remote-diagnostic-content"},a.createElement("ol",null,a.createElement("li",null,(0,w.__)("Go to the Help tab of the remote site","wp-migrate-db")),a.createElement("li",null,(0,E.ZP)((0,w.__)("Copy the Diagnostic Info &amp; Error Log","wp-migrate-db"))),a.createElement("li",null,(0,w.__)("Paste it below","wp-migrate-db"))),a.createElement("textarea",{name:"remote-diagnostic-content",placeholder:(0,E.ZP)((0,w.__)("Remote site&#8217;s Diagnostic Info &amp; Error Log","wp-migrate-db")),"aria-label":(0,w.__)("Paste Diagnostic info from remote site","wp-migrate-db"),onChange:d,value:m["remote-diagnostic-content"]})),f,n.api_error.length>0&&a.createElement(z.Z,{type:"danger"},a.createElement("strong",null,(0,w.__)("API Error"))," \u2014 ",n.api_error),a.createElement("div",{className:"submit-form"},a.createElement("button",{className:"btn btn-sm",onClick:function(a){return Hr(a,n,r,t,e.formURL)}},(0,w.__)("Send Email","wp-migrate-db")),"sending"===n.status&&a.createElement(ot.Q,{className:"help-spinner send-email"})),a.createElement("p",{className:"note trouble"},(0,E.ZP)((0,w.gB)((0,w.__)('Having trouble submitting the form? Email your support request to <br /><a href="mailto:%s" target="_blank" rel="noopener noreferrer">%s</a> instead.',"wp-migrate-db"),g,g)))))};function Kr(e,t,n){return function(){var r=(0,b.Z)((0,g.Z)().mark((function r(a){return(0,g.Z)().wrap((function(r){for(;;)switch(r.prev=r.next){case 0:return r.next=2,a((0,_.wM)({preRequest:(0,i.dC)((function(){t("fetching")})),asyncFn:e,requestFailed:function(e){t("failed")},requestSuccess:function(e){t(n)}}));case 2:return r.abrupt("return",r.sent);case 3:case"end":return r.stop()}}),r)})));return function(e){return r.apply(this,arguments)}}()}var qr,Yr,Jr,Qr,Xr,$r,ea=(0,i.$j)((function(e){var t=(0,J._P)("this_url",e),n=e.dbi_api_data,r=n.licence,a=n.license_errors,i=e.dbi_api_data.licence.licence_status;return{settings:e.settings,dbi_api_data:n,license_errors:a,licence:r,licence_status:i,url:t,api_data:e.dbi_api_data.api_data}}),{checkLicence:dr.T3})((function(e){var t=e.api_data,n=e.state,r=e.dispatch,i=e.settings,s=i.masked_licence,c=i.license_constant,l=e.licence_status,o=e.license_errors,u=t.form_url,p=(0,a.useState)(""),m=(0,k.Z)(p,2),d=m[0],f=m[1];return(0,a.useEffect)((function(){var t=function(){var t=(0,b.Z)((0,g.Z)().mark((function t(){return(0,g.Z)().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(!s){t.next=3;break}return t.next=3,e.checkLicence((function(e){return Kr(e,f,l)}));case 3:case"end":return t.stop()}}),t)})));return function(){return t.apply(this,arguments)}}();t()}),[l]),"active_licence"===l||s||c?"fetching"===d?a.createElement("p",null,(0,w.__)("Fetching license information, please wait...","wp-migrate-db"),a.createElement(ot.Q,{className:"help-spinner"})):o&&Object.keys(o).length>0?null:!t.support_contacts&&t.api_time>0?a.createElement(z.Z,{type:"danger"},(0,E.ZP)((0,w.__)("Unable to get information from the Delicious Brains API.","wp-migrate-db"))):a.createElement("div",null,a.createElement("h2",{className:"email-support"},"Email Support"),a.createElement(Wr,{state:n,dispatch:r,licenceStatus:l,licenceState:d,api_data:t,diagnosticLog:e.diagnosticLog,Log:e.Log,formURL:u})):a.createElement(z.Z,{type:"danger"},(0,E.ZP)((0,w.__)("We couldn't find your license information. Please switch to the settings tab and enter your license.<br /><br />Once completed, you may visit this tab to view your support details.","wp-migrate-db")))})),ta=n(15925),na=n(91399),ra=function(e){var t=e.status,n=e.children,r=e.errors,i=(0,a.useState)(),s=(0,k.Z)(i,2),l=s[0],o=s[1],u=(0,a.useCallback)((function(e){null!==e&&o(e.getBoundingClientRect())}),[]);return n?a.createElement(a.Fragment,null,a.createElement("span",{ref:u},n),a.createElement("div",{className:"relative"},!0===t&&a.createElement(ot.HW,null)),a.createElement(ta.u,{in:"success"===t},a.createElement(na.X2,(0,c.Z)({locationInfo:l,classNames:"toggle-success"},e))),a.createElement(ta.u,{in:"errored"===t},a.createElement(na.Uj,(0,c.Z)({error:r,locationInfo:l,classNames:"toggle-error"},e)))):null},aa=(0,a.forwardRef)((function(e,t){var n=e.children,r=e.callback,i=e.classNames,s=e.ajaxData,c=s.endpoint,l=s.args,o=(0,a.useState)(),u=(0,k.Z)(o,2),p=u[0],m=u[1],d=(0,a.useState)(),f=(0,k.Z)(d,2),g=f[0],b=f[1];return a.createElement(ra,{status:p,errors:g},a.createElement("button",{className:i,onClick:function(e){m(!0),(0,_.op)(c,l).then((function(e){if(!e.success)throw new Error("AJAX error: ".concat(e.data));r(e.data),m("success"),setTimeout((function(){m()}),800)})).catch((function(e){m(!1),b(e.message),console.error(e),console.error(e.message)}))}},n))})),ia=["log","setLog"],sa=function(e){var t=e.log,n=e.setLog,r=((0,Z.Z)(e,ia),(0,i.I0)().dispatch);return(0,a.useEffect)((function(){var e=!0;return(0,_.op)("/get-log",{},!1,r).then((function(t){if(e){if(!t.success)throw new Error("Unable to retrieve diagnostic log");n(t.data)}})).catch((function(e){console.error(e.message)})),function(){e=!1}}),[]),a.createElement(a.Fragment,null,a.createElement("h2",{id:"diagnostic-log-title"},(0,w.__)("Diagnostic Info & Error Log","wp-migrate-db")),a.createElement("textarea",{"aria-labelledby":"diagnostic-log-title",value:t||"",readOnly:!0}),a.createElement("div",{className:"flex-container"},a.createElement("a",{href:window.wpmdb_data.diagnostic_log_download_url,className:"btn btn-sm btn-stroke"},(0,E.ZP)((0,w.__)('Download <span class="screen-reader-text">error log</span>',"wp-migrate-db"))),a.createElement(aa,{callback:n,classNames:"btn btn-sm btn-stroke clear-log-btn",ajaxData:{endpoint:"/clear-log",args:{}}},(0,w.__)("Clear Error Log","wp-migrate-db"))))},ca=n(96480),la=n.n(ca),oa=n(85704),ua=(0,i.$j)((function(e){var t=e.dbi_api_data;return{settings:e.settings,api_data:t.api_data,license:t.licence,licence_errors:t.license_errors,dbi_down_status:t.dbi_down_status}}))((function(e){var t,n=e.api_data,r=e.license,i=e.licence_errors,s=e.screen,c=e.dbi_down_status,l=r.licence_status,o=(0,a.useState)(!1),u=(0,k.Z)(o,2),p=u[0],m=u[1];(0,a.useEffect)((function(){m(["subscription_cancelled","subscription_expired","licence_not_found","activation_deactivated"].includes(l))}),[l]);var d=!1;if(void 0!==n.dbrains_api_down){var f=[n.dbrains_api_down];return"support"===s&&void 0!==n.message&&f.push(n.message),f.map((function(t){return a.createElement(z.Z,{type:"danger",key:la()()},a.createElement("span",null,(0,E.ZP)(t),a.createElement(oa.default,e)))}))}if(c){var g=(0,_.gS)(i);return a.createElement("div",null,g&&g.map((function(t){return a.createElement(z.Z,{type:"danger",key:la()()},a.createElement("span",null,(0,E.ZP)(t),a.createElement(oa.default,e)))})))}if(p&&(n.hasOwnProperty("errors")&&n.errors.hasOwnProperty(l)?t=n.errors[l][s]||n.errors[l].default:"string"===typeof i[l]&&(t=i[l]),d=!0),t){var b="subscription_expired"===l?"warning":"danger";return a.createElement(z.Z,{type:b},(0,E.ZP)(t),d&&a.createElement(ra,null,a.createElement(oa.default,e)))}return null})),pa={fields:{"remote-diagnostic":!0,"local-diagnostic":!0},errors:{},api_error:"",status:""},ma=function(e,t){var n=(0,x.Z)({},e);switch(t.type){case"SET_FIELD":return n.fields[t.field_name]=t.val,n;case"SET_API_ERROR":return n.api_error=t.message,n.status="",n;case"SET_ERROR":return n.errors[t.field_name]=t.message,n.status="",n;case"SET_STATUS":return n.status=t.status,n;case"RESET_ERRORS":return n.errors={},n}},da=(0,i.$j)((function(e){var t=(0,J._P)("this_url",e),n=(0,Y.u)("license_errors",e);return{settings:e.settings,license_errors:n,url:t}}),{checkLicence:dr.T3})((function(e){var t=(0,a.useReducer)(ma,pa),n=(0,k.Z)(t,2),r=n[0],i=n[1],s=(0,a.useState)("email"),c=(0,k.Z)(s,2),l=c[0],o=c[1],u=(0,a.useState)("Loading..."),p=(0,k.Z)(u,2),m=p[0],d=p[1],f=function(e,t){return e.preventDefault(),o(t),!1};return a.createElement(a.Fragment,null,a.createElement("div",{className:"wpmdb-help-wrap"},a.createElement("div",{className:"wrapper"},a.createElement("ul",{className:"subnav flex-container"},a.createElement("li",{className:"email"===l?"subnav-item-active":""},a.createElement("button",{onClick:function(e){f(e,"email")}},(0,w.__)("Email Support","wp-migrate-db"))),a.createElement("li",{className:"videos"===l?"subnav-item-active":""},a.createElement("button",{onClick:function(e){f(e,"videos")}},(0,w.__)("Videos","wp-migrate-db")))))),a.createElement("div",{className:"wpmdb-help-wrap ".concat(l).concat("success"===r.status?" success":"")},a.createElement("div",{className:"wrapper"},a.createElement("div",{className:"wpmdb-help-tab"},a.createElement("div",{className:"support-wrapper"},"email"===l?a.createElement("div",{className:"support-form"},a.createElement("div",{id:"help-form"},a.createElement(ea,{diagnosticLog:m,state:r,dispatch:i,Log:a.createElement(sa,{log:m,setLog:d})}),a.createElement(ua,{screen:"support"}))):a.createElement("div",{className:"support-videos"},a.createElement(zr,null)),a.createElement("aside",{className:"additional-help"},a.createElement("section",{className:"docs documentation-panel container-shadow column"},a.createElement("h2",null,(0,w.__)("Documentation","wp-migrate-db")),a.createElement("ul",{className:"categories"},a.createElement("li",null,a.createElement("a",{href:"https://deliciousbrains.com/wp-migrate-db-pro/docs/getting-started/?utm_source=MDB%2BPaid&utm_campaign=support%2Bdocs&utm_medium=insideplugin",target:"_blank",rel:"noopener noreferrer",className:"link documentation-link"},(0,w.__)("Getting Started","wp-migrate-db"))),a.createElement("li",null,a.createElement("a",{href:"https://deliciousbrains.com/wp-migrate-db-pro/docs/debugging/?utm_source=MDB%2BPaid&utm_campaign=support%2Bdocs&utm_medium=insideplugin",target:"_blank",rel:"noopener noreferrer",className:"link documentation-link"},(0,w.__)("Debugging","wp-migrate-db"))),a.createElement("li",null,a.createElement("a",{href:"https://deliciousbrains.com/wp-migrate-db-pro/docs/cli/?utm_source=MDB%2BPaid&utm_campaign=support%2Bdocs&utm_medium=insideplugin",target:"_blank",rel:"noopener noreferrer",className:"link documentation-link"},(0,w.__)("CLI","wp-migrate-db"))),a.createElement("li",null,a.createElement("a",{href:"https://deliciousbrains.com/wp-migrate-db-pro/docs/common-errors/?utm_source=MDB%2BPaid&utm_campaign=support%2Bdocs&utm_medium=insideplugin",target:"_blank",rel:"noopener noreferrer",className:"link documentation-link"},(0,w.__)("Common Errors","wp-migrate-db"))),a.createElement("li",null,a.createElement("a",{href:"https://deliciousbrains.com/wp-migrate-db-pro/docs/howto/?utm_source=MDB%2BPaid&utm_campaign=support%2Bdocs&utm_medium=insideplugin",target:"_blank",rel:"noopener noreferrer",className:"link documentation-link"},(0,w.__)("How To","wp-migrate-db"))),a.createElement("li",null,a.createElement("a",{href:"https://deliciousbrains.com/wp-migrate-db-pro/docs/addons/?utm_source=MDB%2BPaid&utm_campaign=support%2Bdocs&utm_medium=insideplugin",target:"_blank",rel:"noopener noreferrer",className:"link documentation-link"},(0,w.__)("Addons","wp-migrate-db"))),a.createElement("li",null,a.createElement("a",{href:"https://deliciousbrains.com/wp-migrate-db-pro/docs/multisite/?utm_source=MDB%2BPaid&utm_campaign=support%2Bdocs&utm_medium=insideplugin",target:"_blank",rel:"noopener noreferrer",className:"link documentation-link"},(0,w.__)("Multisite","wp-migrate-db"))),a.createElement("li",null,a.createElement("a",{href:"https://deliciousbrains.com/wp-migrate-db-pro/docs/changelogs/?utm_source=MDB%2BPaid&utm_campaign=support%2Bdocs&utm_medium=insideplugin",target:"_blank",rel:"noopener noreferrer",className:"link documentation-link"},(0,w.__)("Changelogs","wp-migrate-db")))))))))),"email"===l&&"success"!==r.status&&a.createElement("div",{className:"wrapper diagnostic-log"},a.createElement(sa,{log:m,setLog:d})))})),fa=(n(94849),n(87326)),_a=function(e){var t=e.settings,n=t.errors,r=t.status,s=(0,i.I0)(),l=e.value,o=e.settingsKey,u=e.settingName,p=e.handler,m=e.handlerArgs,d=e.callBack,f=(0,a.useState)(),_=(0,k.Z)(f,2),g=_[0],b=_[1],h=(0,a.useCallback)((function(e){null!==e&&b(e.getBoundingClientRect())}),[]);return a.createElement("div",{className:"flex-container"},a.createElement("div",{className:"toggle-switch"},a.createElement("input",{type:"checkbox",checked:l,onChange:function(){return function(e,t){return!0!==r[o]&&(p?p(e,t,(0,x.Z)({},m)):void s((0,fa.Xn)(e,t,d)))}(o,!l)},id:o,"aria-labelledby":"".concat(o,"-label")}),a.createElement("label",{className:"toggle-label".concat(l?" checked":""),htmlFor:o},"Toggle")),a.createElement("div",{className:"migration-action"},a.createElement("div",{className:"flex-container"},a.createElement("h3",{className:"toggle-title",id:"".concat(o,"-label"),ref:h},u),a.createElement("div",{className:"relative".concat(e.spinnerStyle||"")},!0===r[o]&&a.createElement(ot.Q,{className:"settings-spinner"}))),a.createElement(ta.u,{in:"success"===r[o]},a.createElement(na.X2,(0,c.Z)({offset:e.offset||null,locationInfo:g,classNames:"toggle-success"},e))),a.createElement(ta.u,{in:"errored"===r[o]},a.createElement(na.Uj,(0,c.Z)({offset:e.offset||null,error:n[o],locationInfo:g,classNames:"toggle-error"},e))),a.createElement("span",null,e.children)))},ga=n(81690),ba=n.n(ga);function ha(){return ha=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},ha.apply(this,arguments)}var va=function(e){return a.createElement("svg",ha({id:"Layer_1","data-name":"Layer 1",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 459 459"},e),qr||(qr=a.createElement("defs",null,a.createElement("style",null,".cls-2{fill:#80bdd4}.cls-3{fill:#042340}"))),a.createElement("path",{style:{fill:"#a5ddf1"},d:"M0 0h459v459H0z"}),Yr||(Yr=a.createElement("path",{className:"cls-2",d:"M144.9 379.68a6.21 6.21 0 0 1-.57-.49 19.06 19.06 0 0 0-2.8-2 11.73 11.73 0 0 1-1.79-1 60.76 60.76 0 0 0-58.9-39.53 86.13 86.13 0 0 0-11.22 1.53 54.56 54.56 0 0 0-14 5.1l-3.82 2.3a30.65 30.65 0 0 0-2-5.1 52.49 52.49 0 0 0-18.36-21.68 77.52 77.52 0 0 0 38-41.05c0-.26.26-.77.26-1s.25-.51.25-.77a66.1 66.1 0 0 0 3.06-10.2 74.47 74.47 0 0 0 1.28-7.9 74.61 74.61 0 0 0-1.53-24.74 102.43 102.43 0 0 0-4.08-12.49 77.64 77.64 0 0 0-16.07-24 59.74 59.74 0 0 0-5.94-5.66 68.93 68.93 0 0 0-10-6.89 79 79 0 0 0-25.24-9.18c-3.32-.51-6.89-.76-10.2-1 2.8-1 5.61-1.78 8.41-2.55a63.89 63.89 0 0 1 53.6 12.22 22 22 0 0 1 7.14 6.63c1.53 1.79 3.06 3.57 4.34 5.36a46.11 46.11 0 0 1 26 82.62 71.67 71.67 0 0 1 24.22 11 84 84 0 0 1 36.47-19.38 79.36 79.36 0 0 1 49.47 4.34 44.8 44.8 0 0 1 23.2-6.63c23.46.25 42.58 19.38 42.33 42.84v2.29a24.75 24.75 0 0 1 22.7 24.74 19.91 19.91 0 0 1-.77 5.61 76.42 76.42 0 0 1 83.38 58.9c.26 2 .77 4.08 1 6.12a69.75 69.75 0 0 1 39 47.94c.23 1 .44 2 .62 3.06h-6.24a47.52 47.52 0 0 0-4.53-7.91 53 53 0 0 0-45.9-22.69 35.11 35.11 0 0 0-5.1.51 22.1 22.1 0 0 0-19.38-9.95 24.7 24.7 0 0 0-4.34.51 2.92 2.92 0 0 1-.65.2l-.47.1a3 3 0 0 0-.66.2 9.35 9.35 0 0 0-2.81 1c-.51-1.53-1-2.8-1.53-4.08-1-2-2-4.08-3.06-5.86l-2.29-3.83c-7.14-10.2-18.36-18.36-31.62-23.71a92.31 92.31 0 0 0-33.15-6.12h-4.34a92.83 92.83 0 0 0-15 1.78 90.42 90.42 0 0 0-22.44 8.16 63.09 63.09 0 0 0-13.52 9.18c-3.57-.51-7.14-1-11-1.27h-3.83a75.63 75.63 0 0 0-10.71.76l-7.65 1.53a21.62 21.62 0 0 0-3.82 1 60 60 0 0 0-11.73 4.85c0-.51-.26-1.28-.26-1.79a17 17 0 0 0-.51-3.57 30.21 30.21 0 0 0-1.27-4.59 19.6 19.6 0 0 0-1.79-4.08 33.5 33.5 0 0 0-6.88-8.92 5 5 0 0 0-.57-.49l-.39-.29Zm61.1-25.23c0-2.81-2.8-5.35-6.37-5.1-3.32 0-6.12 2.55-6.12 5.36h-4.81V345c-8.67 0-15.55 3.82-15.55 8.67v.76h-8.42c0 2.3 4.33 4.08 10.2 4.85a6.51 6.51 0 0 0-1.78 4.59c0 5.35 6.88 9.94 15.55 9.94V359.3c6.37-.77 11-2.55 11-4.85Zm28.82-31.87 15-28.82c-9.43-4.84-20.91-1.53-25.5 7.65a17.87 17.87 0 0 0-2 7.14l-9.94-9.43c-6.63 6.63-6.63 17.08-.26 23.2a.25.25 0 0 0 .26.26h-9.95c0 3.82 9.95 6.88 22.44 6.88 12.22 0 22-2.92 22.42-6.63h8.18c0-3.82-3.57-6.88-8.16-6.63-4.24 0-7.74 2.68-7.9 6.38h7.9v.25h-7.88v-.25ZM423.3 300.65a17.61 17.61 0 0 0-11.48 4.59c-4.84-8.93-13.51-14.79-23.2-14.79-9.95 0-18.36 6.12-23.46 15.3h-.51c-8.16 0-14.79 7.9-14.79 17.85h92.05c.51-12.75-7.9-23-18.61-23"})),Jr||(Jr=a.createElement("path",{className:"cls-3",d:"M0 173.91h1.27a65.76 65.76 0 0 1 10.2 1 73.24 73.24 0 0 1 25.25 9.18 68.29 68.29 0 0 1 9.94 6.91 61 61 0 0 1 5.87 5.61 73.31 73.31 0 0 1 16.06 24 71.24 71.24 0 0 1 4.08 12.49 73.55 73.55 0 0 1 1.53 24.74c-.25 2.55-.76 5.35-1.27 7.9a78.54 78.54 0 0 1-3.06 10.2c0 .26-.26.51-.26.77s-.25.76-.25 1c-7.14 17.59-20.4 32.64-38 41.05a52.42 52.42 0 0 1 18.36 21.68 29.81 29.81 0 0 1 2 5.1l3.83-2.3a61.4 61.4 0 0 1 14-5.1 57 57 0 0 1 11.22-1.53 60.79 60.79 0 0 1 58.91 39.53 10.51 10.51 0 0 1 1.78 1 11.32 11.32 0 0 1 2.81 2 6.6 6.6 0 0 1 1.53 1.27 33.39 33.39 0 0 1 6.88 8.93 27.37 27.37 0 0 1 1.79 4.08 30.21 30.21 0 0 1 1.27 4.59c.26 1.27.26 2.29.51 3.57a4.24 4.24 0 0 1 .26 1.78 59.38 59.38 0 0 1 11.73-4.84c1.27-.26 2.55-.77 3.82-1a39.88 39.88 0 0 1 7.65-1.53 70.61 70.61 0 0 1 10.71-.77h3.83a51.2 51.2 0 0 1 11 1.28 83.42 83.42 0 0 1 13.52-9.18 86.54 86.54 0 0 1 22.44-8.16 97.5 97.5 0 0 1 15-1.79h4.34a92.48 92.48 0 0 1 33.15 6.12c13.51 5.36 24.48 13.52 31.62 23.72l2.29 3.82a31.74 31.74 0 0 1 3.06 5.87c.51 1.27 1 2.8 1.54 4.08a12 12 0 0 1 2.8-1 2.55 2.55 0 0 1 .66-.21l.47-.1a3.18 3.18 0 0 0 .66-.2 27.79 27.79 0 0 1 4.33-.51 22.72 22.72 0 0 1 19.38 9.94 35.39 35.39 0 0 1 5.1-.51 52.47 52.47 0 0 1 45.9 22.7 49.63 49.63 0 0 1 4.62 7.9H0V173.91Z"})),Qr||(Qr=a.createElement("path",{className:"cls-2",d:"M194.31 242.76h29.58c0-11.22-11.73-20.4-26-20.4a29.47 29.47 0 0 0-19.14 6.64c-.76 0-1.53-.25-2.55-.25-9.94 0-17.85 6.37-17.85 14Z"})),Xr||(Xr=a.createElement("path",{className:"cls-3",d:"M199.92 349.35c-3.32 0-6.12 2.3-6.12 5.1H189v-9.69c-8.67 0-15.55 3.83-15.55 8.67v.77H165c0 2 4.08 3.82 10.2 4.84a7.35 7.35 0 0 0-1.78 4.59c0 5.61 6.88 9.95 15.55 9.95V359c6.38-.76 11-2.55 11-4.84h6.38c-.26-2.55-3.06-4.85-6.38-4.85M100.21 173.4a13.48 13.48 0 0 0 4.34 17.6l1-1.53a14.35 14.35 0 0 0 13.26 8.67v-24.48c4.34-.77 7.4-2.3 7.4-4.08h5.86c0-2.81-2.55-4.85-5.86-4.85s-5.87 2.3-5.87 4.85h-2.29a12.27 12.27 0 0 0-14 0H93.33c-.25 1.53 2.55 3.06 6.89 3.82M196.35 196.61c16.32 0 29.32-4.08 29.32-8.93h10.46c0-4.84-4.59-8.92-10.46-8.92s-10.45 3.82-10.45 8.67h-7.14l18.1-35.19c-12-6.12-26.26-1.79-32.13 9.43a22.53 22.53 0 0 0-2.55 8.93l-11.22-11.73c-8.16 7.65-9.18 20.14-2.55 28.3h-11.22c.26 5.36 13.52 9.44 29.84 9.44M247.35 316c-4.33 0-7.91 3.06-7.91 6.63h16.07c0-3.57-3.57-6.63-8.16-6.63M284.58 236.9a18.72 18.72 0 0 0-4.08 4.84c-6.38 10.2-3.32 24.23 7.14 31.11l1.78-2.8a24.62 24.62 0 0 0 22.7 15v-47.39c9.69-1.27 16.32-4.33 16.32-7.65h9.94c0-4.59-4.33-8.16-9.94-8.16-5.35 0-9.69 3.57-9.95 8.16h-45.64v.26c0 2.55 4.59 5.1 11.73 6.63"})),$r||($r=a.createElement("path",{className:"cls-3",d:"M249.9 293.76c-9.43-4.84-20.91-1.53-25.5 7.65a19 19 0 0 0-2 7.14l-10-9.43c-6.37 6.88-6.63 17.34-.25 23.2l.25.26h-9.94c0 3.82 9.94 6.88 22.44 6.88s22.44-3.06 22.44-6.88h-12.5ZM342.47 195.59c28.81 0 52-7.14 52.27-15.81h18.62c0-8.67-8.42-15.56-18.62-15.56s-18.62 6.89-18.62 15.56h-14.79v-74.21A40 40 0 0 0 324.61 129l-2.8-4.33c-16.58 10.71-21.68 32.13-11.48 48.19a33.62 33.62 0 0 0 6.12 7.14h-26.26c0 8.42 23.46 15.56 52.27 15.56"})))},Ea=(n.p,n(71049)),wa=(0,i.$j)((function(e){var t=(0,J._P)("this_url",e),n=(0,Ea.C)("license_errors",e),r=e.dbi_api_data;return{settings:e.settings,license_errors:n,DBIAPIData:r,url:t}}),{activateLicense:dr.fo,removeLicense:dr.s$,setLicence:dr.Qj,checkLicence:dr.T3,checkLicenceAgain:dr.Ew,setLicenceErrors:dr.eD})((function(e){var t=e.settings,n=t.licence,r=t.masked_licence,i=t.status,s=t.errors,c=t.license_constant,l=e.DBIAPIData.license_errors,o=e.DBIAPIData.licence,u=o.license_ui_status,p=o.licence_status,m=e.highlight,d=e.checkAgain,f=e.checkLicenceAgain,h=e.checkLicence,v=(0,a.useState)(!1),y=(0,k.Z)(v,2),x=y[0],Z=y[1],N=(0,a.useRef)();(0,a.useEffect)((function(){r&&!x&&h(dr.sL,!1,"settings"),m&&N.current&&N.current.focus(),d&&f()}),[r,m,d,f,h]);var P=r||n,S=function(){var t=(0,b.Z)((0,g.Z)().mark((function t(){return(0,g.Z)().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:e.removeLicense();case 1:case"end":return t.stop()}}),t)})));return function(){return t.apply(this,arguments)}}(),T=function(){var t=(0,b.Z)((0,g.Z)().mark((function t(){return(0,g.Z)().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(n.length){t.next=3;break}return N.current.focus(),t.abrupt("return",!1);case 3:Z(!0),e.activateLicense(n);case 5:case"end":return t.stop()}}),t)})));return function(){return t.apply(this,arguments)}}();if(c)return a.createElement("div",{className:"settings-row license-row"},a.createElement("h4",null,(0,w.__)("The license key is currently defined in wp-config.php.","wp-migrate-db")));var O=i&&i.licence_action,A=window.wpmdb_strings.welcome_text.replace("%1$s","https://deliciousbrains.com/wp-migrate-db-pro/doc/quick-start-guide/?utm_campaign=support%2Bdocs&utm_source=MDB%2BPaid&utm_medium=insideplugin").replace("%2$s","https://deliciousbrains.com/wp-migrate-db-pro/videos/?utm_campaign=support%2Bdocs&utm_source=MDB%2BPaid&utm_medium=insideplugin"),C=function(e){return a.createElement("p",null,(0,E.ZP)((0,w.gB)((0,w.__)('Enter your license key below and activate to enable push, pull, import and backup functionality as well as priority support and software updates. <br /> Your license key can be found in\n          <a\n          href="%s"\n          target="_blank"\n          rel="noopener noreferrer"\n          >\n          My Account</a>. If you don\'t have an account yet,\n          <a\n          href="%s"\n          target="_blank"\n          rel="noopener noreferrer"\n          >\n          purchase a new license</a>.',"wp-migrate-db"),"https://deliciousbrains.com/my-account/","https://deliciousbrains.com/wp-migrate-db-pro/pricing/")))},R=(0,_.gS)(l),I=null===p?"no-licence":"licence-header",D="subscription_expired"===p?"warning":"danger";return a.createElement("div",{className:"settings-row license-row"},a.createElement("h2",{id:"license-section-title",className:I+" section-title"},(0,w.__)("Your License","wp-migrate-db")),null===p&&a.createElement(C,null),a.createElement(ta.u,{in:!ba()(l)},a.createElement("div",null,R&&R.map((function(t){return a.createElement(z.Z,{type:D,key:la()()},a.createElement("span",null,(0,E.ZP)(t),a.createElement(oa.default,e)))})))),s.licence&&a.createElement(z.Z,{type:"danger"},s.licence),"first_activation"===u&&a.createElement("div",{id:"welcome-wrap"},a.createElement(va,{id:"welcome-img"}),a.createElement("div",{className:"welcome-text"},a.createElement("h3",null,window.wpmdb_strings.welcome_title),a.createElement("p",null,(0,E.ZP)(A)))),a.createElement("input",{type:"text",className:"text-black".concat(m?" highlight":""),onChange:function(t){e.setLicence(t.target.value.trim())},value:(0,E.ZP)(P),id:"wpmdb-license-key",readOnly:r&&r.length>0,ref:N,"aria-labelledby":"license-section-title"}),a.createElement("div",{className:"flex-container licence-buttons"},a.createElement(ta.u,{in:null!==r},a.createElement("button",{className:"btn btn-stroke btn-sm",onClick:S},(0,w.__)("REMOVE","wp-migrate-db"))),a.createElement(ta.u,{in:!r},a.createElement("button",{className:"btn btn-stroke btn-sm",onClick:T},(0,w.__)("ACTIVATE LICENSE","wp-migrate-db"))),O&&a.createElement("div",{className:"relative"},a.createElement(ot.Q,{className:"settings-spinner"}))))})),ya=function(e){var t=e.settings,n=t.key,r=t.status,i=t.errors,s=e.url,l=(0,a.useRef)(),o=(0,a.useState)(!1),u=(0,k.Z)(o,2),p=u[0],m=u[1];return(0,a.useEffect)((function(){var e=function(e){if("api-key"!==e.target.id)return!1;var t=l.current.value.split("\n");e.clipboardData.setData("text/plain",t.join(" ")),e.preventDefault()};return document.addEventListener("copy",e),function(){document.removeEventListener("copy",e)}}),[]),a.createElement("div",{className:"flex-col connection-info"},a.createElement("h2",{id:"connection-info-title",className:"section-title"},(0,w.__)("Connection Info","wp-migrate-db")),a.createElement("textarea",{rows:"2",name:"",className:"text-black consolas",value:"".concat(s.includes("https")?s:s.replace("http","https"),"\n").concat(n)||"",readOnly:!0,ref:l,id:"api-key","aria-labelledby":"connection-info-title"}),a.createElement("div",{className:"flex-container"},a.createElement("button",{className:"btn btn-stroke btn-sm".concat(p?" copied":""),onClick:function(e){l.current.select(),l.current.focus(),document.execCommand("copy"),m(!0),setTimeout((function(){m(!1)}),1500)}},p?a.createElement(a.Fragment,null,a.createElement(C,{className:"styled-check"}),"\xa0\xa0\xa0",a.createElement("span",null,(0,w.__)("COPIED","wp-migrate-db"))):a.createElement("span",null,(0,w.__)("COPY TO CLIPBOARD","wp-migrate-db"))),a.createElement("button",{className:"btn btn-stroke btn-sm",onClick:function(){e.resetAPIKey()}},(0,w.__)("RESET SECRET KEY","wp-migrate-db")),a.createElement("div",{className:"relative"},r.reset_api_key&&"errored"!==r.reset_api_key&&a.createElement(ot.Q,{className:"settings-spinner"})),a.createElement(ta.u,{in:"errored"===r.reset_api_key},a.createElement(na.Uj,(0,c.Z)({error:i.reset_api_key},e)))))},xa=n(20271),ka=n(8214),Za=(0,n(14107).Z)({overrides:{MuiSlider:{root:{color:Nn.HT,height:8},thumb:{height:18,width:18,marginTop:-8,marginLeft:0},active:{},track:{height:4,borderRadius:2},rail:{height:4,borderRadius:2}}}}),Na=function(e){var t=(0,i.v9)((function(e){return e.settings})),n=t.status,r=t.errors,s=e.settingsKey,l=e.formattedSize,o=e.prefix,u=e.sliderOpts,p=e.callback,m=e.title,d=(0,a.useState)(l),f=(0,k.Z)(d,2),_=f[0],g=f[1],b=l,h=(0,a.useState)(),v=(0,k.Z)(h,2),E=v[0],w=v[1],y=(0,a.useCallback)((function(e){null!==e&&w(e.getBoundingClientRect())}),[]),x="slider-".concat(s);return a.createElement(a.Fragment,null,a.createElement("div",{className:"slider-wrap ".concat(e.className?e.className:"")},a.createElement("div",{className:"grid-container slider-header"},a.createElement("h3",{className:"slider-title",id:x},m),a.createElement("div",{ref:y,className:"slider-details"},_," ",o,a.createElement("div",{className:"relative"},a.createElement(ta.u,{in:"success"===n[s]},a.createElement(na.X2,(0,c.Z)({locationInfo:E,classNames:"toggle-success"},e))),!0===n[s]&&a.createElement(ot.Q,{className:"settings-spinner"}),a.createElement(ta.u,{in:"errored"===n[s]},a.createElement(na.Uj,(0,c.Z)({error:r[s],locationInfo:E,classNames:"toggle-error"},e)))))),a.createElement(xa.Z,{theme:Za},a.createElement(ka.Z,{step:u.step,min:u.min,value:_,max:u.max,valueLabelDisplay:"off",disabled:!0===n[s],onChange:function(e,t){g(t)},onBlur:function(){_!==b&&p(_)},onMouseUp:function(){_!==b&&p(_)},"aria-labelledby":x}))))};var Pa,Sa,Ta=n(78677),Oa=n(18832),Aa=(0,i.$j)((function(e){var t=(0,Y.u)("whitelist_plugins",e);return{settings:e.settings,whitelist_plugins:t}}),{updateSetting:fa.m7,saveWhitelistPlugins:function(){return function(){var e=(0,b.Z)((0,g.Z)().mark((function e(t,n){var r,a,i;return(0,g.Z)().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return r="save-whitelist-plugins",a=(0,Y.u)("whitelist_plugins",n()),t((0,fa.c9)(r,!0)),t((0,fa.rT)(r)),e.next=6,(0,_.op)("/whitelist-plugins",{whitelist_plugins:a});case 6:if((i=e.sent).success){e.next=9;break}return e.abrupt("return",t((0,fa.nE)(r,i)));case 9:t((0,fa.c9)(r,"success")),setTimeout((function(){t((0,fa.c9)(r,!1))}),1500);case 11:case"end":return e.stop()}}),e)})));return function(t,n){return e.apply(this,arguments)}}()}})((function(e){var t=e.settings,n=t.plugins,r=t.status,i=t.errors,s=e.whitelist_plugins,l=(0,a.useState)(),o=(0,k.Z)(l,2),u=o[0],p=o[1],m=(0,a.useCallback)((function(e){null!==e&&p(e.getBoundingClientRect())}),[]),d=function(e){return Object.entries(e).map((function(e){return(0,k.Z)(e,1)[0]}))},f=function(t){e.updateSetting("whitelist_plugins",t),e.saveWhitelistPlugins()};function g(e){var t=(0,_.XV)(s,e.target.value);f(t)}return a.createElement("div",{className:"compatibility-mode-block"},a.createElement("div",{className:"flex-container"},a.createElement("h3",{className:"title",ref:m},(0,w.__)("Load Select Plugins for Migration Requests","wp-migrate-db")),a.createElement("div",{className:"relative"},!0===r["save-whitelist-plugins"]&&a.createElement(ot.Q,{className:"settings-spinner"}))),a.createElement(ta.u,{in:"success"===r["save-whitelist-plugins"]},a.createElement(na.X2,(0,c.Z)({locationInfo:u,classNames:"toggle-success"},e))),a.createElement(ta.u,{in:"errored"===r["save-whitelist-plugins"]},a.createElement(na.Uj,(0,c.Z)({error:i["save-whitelist-plugins"],locationInfo:u,classNames:"toggle-error"},e))),a.createElement("span",null,(0,w.__)("By default, plugins are not loaded for migration requests. This enhances performance and reduces the likelihood of a third-party plugin interfering with migrations.","wp-migrate-db"),a.createElement("br",null),a.createElement("br",null),(0,w.__)("To load certain plugins for migration requests, select the plugins below.","wp-migrate-db"),a.createElement("a",{href:"https://deliciousbrains.com/wp-migrate-db-pro/doc/compatibility-mode/",target:"_blank",rel:"noopener noreferrer"}," "+(0,w.__)("More Details \u2192","wp-migrate-db"))),a.createElement(Ta.Z,{id:"compatibility-multiselect",options:d(n),iterator:function(){return Object.entries(n).map((function(e){var t=(0,k.Z)(e,2),n=t[0],r=t[1];return a.createElement("li",{key:n},a.createElement("input",{id:"checkbox-".concat(n),type:"checkbox",value:n,onChange:g,checked:s.includes(n)}),a.createElement("label",{htmlFor:"checkbox-".concat(n),key:la()()},r[0].name))}))},className:"wpmdb-form compat-plugin-list",visible:!0,updateSelected:f,selectInverse:function(){var e=s,t=d(n);return(0,Oa.Z)((function(e){f(e)}),t,e)}}))})),Ca=n(49275),Ra=function(e){var t=e.firewallPlugins,n=Object.values(t)[0];return a.createElement(z.Z,{type:"warning"},(0,E.ZP)((0,w.gB)((0,w.__)("<strong>%s</strong> includes a web application firewall that is known to cause issues with Push migrations. Consider using a Pull migration from this site for better results.","wp-migrate-db"),n[0].name))," ",a.createElement(Ca.Z,{link:"https://deliciousbrains.com/wp-migrate-db-pro/doc/firewall-plugins/",content:(0,w.__)("More About Firewall Compatibility","wp-migrate-db"),utmContent:"settings-firewall-warning",utmCampaign:"wp-migrate-documentation",hasArrow:!0}))},Ia=(0,i.$j)((function(e){var t=(0,J._P)("connection_info",e),n=(0,Y.u)("license_errors",e),r=(0,J._P)("firewall_plugins",e);return{settings:e.settings,license_errors:n,url:t[0],firewall_plugins:r}}),{resetAPIKey:fa.NV,resetError:fa.rT,toggleSetting:fa.Xn,disableSSL:fa.UJ,setMaxRequest:fa.Lc,setDelayBetweenRequests:fa.vo})((function(e){var t=e.settings,n=t.allow_push,r=t.allow_pull,i=t.verify_ssl,s=t.delay_between_requests,l=t.beta_optin,o=t.allow_tracking,u=t.high_performance_transfers;return a.createElement("div",{className:"wpmdb-settings"},a.createElement("div",{className:"wpmdb-settings-page"},a.createElement("div",{className:"settings-row flex"},a.createElement(ya,e),a.createElement("div",{className:"flex-col migration-permissions"},a.createElement("h2",{className:"section-title"},(0,w.__)("Permissions","wp-migrate-db")),a.createElement(_a,(0,c.Z)({value:r,settingsKey:"allow_pull",settingName:"Pull"},e),(0,w.__)("Process requests to pull data from this install, copying it elsewhere","wp-migrate-db")),a.createElement(_a,(0,c.Z)({value:n,settingsKey:"allow_push",settingName:"Push"},e),(0,w.__)("Process requests to push data to this install, overwriting its data","wp-migrate-db")),n&&Object.keys(e.firewall_plugins).length>0&&a.createElement(Ra,{firewallPlugins:e.firewall_plugins}))),a.createElement(a.Suspense,{fallback:a.createElement("div",null,"Loading...")},a.createElement(wa,(0,c.Z)({},e,{highlight:"/settings/enter"===e.location.pathname,checkAgain:"/settings/check-again"===e.location.pathname}))),a.createElement("div",{className:"settings-row request-settings"},a.createElement("h2",{className:"section-title"},(0,w.__)("Request Settings","wp-migrate-db")),a.createElement("div",null,a.createElement(_a,(0,c.Z)({value:i,settingsKey:"verify_ssl",spinnerStyle:" certificate-validation",settingName:a.createElement("span",{className:"has-tooltip"},(0,w.__)("Certificate Verification","wp-migrate-db")," ",a.createElement(ot.Ag,{"data-tip":!0,"data-for":"action-tip"}))},e),(0,w.__)("Verify the authenticity of the remote server\u2019s TLS certificate","wp-migrate-db")),a.createElement(Na,(0,c.Z)({},e,{settingsKey:"delay_between_requests",formattedSize:s,prefix:"s",sliderOpts:{step:1,min:0,max:10},callback:e.setDelayBetweenRequests,title:a.createElement("span",{className:"has-tooltip"},(0,w.__)("Delay Between Requests","wp-migrate-db"),a.createElement(ot.Ag,{"data-tip":!0,"data-for":"delay-between-requests-tip"})),className:"delay-between-requests"})))),a.createElement("div",{className:"settings-row advanced-settings"},a.createElement("h2",{className:"section-title"},(0,w.__)("Advanced Settings","wp-migrate-db")),a.createElement(_a,(0,c.Z)({value:l,settingsKey:"beta_optin",settingName:"Beta Version Updates",callBack:"beta"},e),(0,w.__)("When a beta version of WP Migrate is available, show a\n              WordPress update notice and allow updating to the beta like any\n              other plugin update.","wp-migrate-db")),a.createElement(_a,(0,c.Z)({value:o,settingsKey:"allow_tracking",spinnerStyle:" securely-share-data",settingName:a.createElement("span",{className:"has-tooltip"},(0,w.__)("Securely Share Data","wp-migrate-db"),a.createElement(ot.Ag,{"data-tip":!0,"data-for":"allow-tracking-tip"}))},e),(0,w.__)("Help us continue to make WP Migrate better by securely sharing usage data with us.","wp-migrate-db")),a.createElement(Aa,null)),a.createElement(ft.Z,{effect:"solid",place:"right",type:"light",delayUpdate:500,border:!0,className:"action-tooltip",id:"action-tip"},(0,E.ZP)((0,w.__)("We disable SSL verification by default because a lot of people's\n            environments are not setup for it to work. For example, with XAMPP,\n            you have to manually enable OpenSSL by editing the php.ini. Without\n            SSL verification, an HTTPS connection is vulnerable to a\n            man-in-the-middle attack, so we do recommend you configure your\n            environment and enable&nbsp;this.","wp-migrate-db"))),!u&&a.createElement(a.Fragment,null,a.createElement(ft.Z,{effect:"solid",place:"right",type:"light",delayUpdate:500,border:!0,className:"action-tooltip",id:"max-request-tip"},(0,w.__)("We've detected that your server supports requests up to 25 MB, but\n            it's possible that your server has limitations that we could not\n            detect. To be on the safe side, we set the default to 1 MB, but you\n            can try throttling it up to get better performance. If you're getting\n            a 413 error or having trouble with time outs, try throttling this\n            setting down.","wp-migrate-db")),a.createElement(ft.Z,{effect:"solid",place:"right",type:"light",delayUpdate:500,border:!0,className:"action-tooltip",id:"delay-between-requests-tip"},(0,w.__)("Some servers have rate limits which the plugin can hit when performing\n            migrations. If you're experiencing migration failures due to server\n            rate limits, you should set this to one or more seconds to alleviate\n            the problem.","wp-migrate-db"))),a.createElement(ft.Z,{effect:"solid",place:"right",type:"light",delayUpdate:500,border:!0,className:"action-tooltip",id:"allow-tracking-tip"},(0,w.__)("We will use data about your migrations to make better-informed\n            decisions about the future of WP Migrate and provide you with\n            better support.","wp-migrate-db"))))}));function Da(){return Da=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},Da.apply(this,arguments)}var La,Ma=function(e){return a.createElement("svg",Da({xmlns:"http://www.w3.org/2000/svg",xmlnsXlink:"http://www.w3.org/1999/xlink",width:24,height:24,viewBox:"0 0 24 24"},e),Pa||(Pa=a.createElement("defs",null,a.createElement("circle",{id:"license-checked-a",cx:8,cy:8,r:8}))),Sa||(Sa=a.createElement("g",{fill:"none",fillRule:"evenodd",transform:"translate(4 4)"},a.createElement("mask",{id:"license-checked-b",fill:"#fff"},a.createElement("use",{xlinkHref:"#license-checked-a"})),a.createElement("use",{fill:"#52AA59",xlinkHref:"#license-checked-a"}),a.createElement("path",{fill:"#FFF",fillRule:"nonzero",d:"M7.587 11.338a1.01 1.01 0 0 1-1.433 0l-2.22-2.234a1.024 1.024 0 0 1 0-1.442 1.01 1.01 0 0 1 1.432 0L6.69 8.993c.1.1.262.1.362 0l3.583-3.604a1.01 1.01 0 0 1 1.433 0 1.023 1.023 0 0 1 0 1.442l-4.48 4.507Z",mask:"url(#license-checked-b)"}))))},Fa=(n.p,n(23481)),Ba=n(30463),ja=["license_constant"],Ua=["masked_licence","license_constant"],za=function(e){e.license_constant;var t=(0,Z.Z)(e,ja);if(!t.api_data)return null;var n=t.api_data,r=n.license_name,i=n.display_name,s=n.user_email;return a.createElement("div",{className:"license-block active-license"},a.createElement("p",{className:"license-level text-primary"},a.createElement("a",{href:"https://deliciousbrains.com/my-account",target:"_blank",rel:"noopener noreferrer",className:"license-status"},r&&a.createElement(a.Fragment,null,a.createElement(Ma,{className:"icon-24 license-icon"}),a.createElement("span",{className:"license-label"},(0,E.ZP)(r)," ",(0,w.__)("License","wp-migrate-db"))))),a.createElement("p",{className:"license-info"},i&&s&&a.createElement("span",{className:"user-label"},i," <",s,">")))},Ga=function(e){var t=e.masked_licence,n=e.license_constant,r=(0,Z.Z)(e,Ua);if(!r.api_data)return null;var i=r.licence_status,s=r.api_data,c=s.display_name,l=s.user_email,o="subscription_cancelled"===i,u="licence_not_found"===i,p="activation_deactivated"===i;if(null===i&&(i="unlicensed"),"subscription_expired"===r.licence_status)return a.createElement("div",{className:"license-block"},a.createElement("div",{className:"license-expired"},a.createElement("p",{className:"license-status text-brand-dark"},a.createElement(Fa.r,{className:"license-icon"})," ",a.createElement("span",null,(0,w.__)("License Expired","wp-migrate-db"))),a.createElement("p",{className:"license-info"},a.createElement("span",{className:"user-label"},c," <",l,">"))),a.createElement("div",null,a.createElement("a",{href:"https://deliciousbrains.com/my-account/licenses/",target:"_blank",rel:"noopener noreferrer",className:"btn btn-sm"},(0,w.__)("Renew Now","wp-migrate-db"))));if("active_licence"===i)return a.createElement(za,r);var m={subscription_cancelled:{tag:"Licence Cancelled",btn_text:"REPLACE LICENSE"},licence_not_found:{tag:"Licence Not Found",btn_text:"REPLACE LICENSE"},activation_deactivated:{tag:"Licence Inactive",btn_text:"REACTIVATE LICENSE"},unlicensed:{tag:"Unlicensed",btn_text:"ACTIVATE LICENSE"}};if(!m[i])return null;var d=m[i].hasOwnProperty("tag")?m[i].tag:"",f=m[i].hasOwnProperty("btn_text")?m[i].btn_text:"";return!t&&!n||o||u||p?a.createElement("div",{className:"license-block"},a.createElement("div",null,a.createElement("p",{className:"license-status text-brand-dark"},a.createElement(Fa.r,{className:"license-icon"})," ",a.createElement("span",null,d))),a.createElement("div",null,!p&&a.createElement("a",{href:"#settings/enter",className:"btn btn-sm"},f),p&&a.createElement(Ba.Z,{className:"btn btn-sm",btnText:f}))):null},Va=(0,o.EN)((0,i.$j)((function(e){var t=(0,Y.u)("licence",e),n=(0,Y.u)("masked_licence",e),r=(0,Y.u)("license_constant",e),a=(0,Ea.C)("api_data",e);return{license:t,masked_licence:n,license_constant:r,licence_status:(0,Ea.C)("licence",e).licence_status,api_data:a}}),{})((function(e){var t=e.licence_status;return a.createElement("div",{className:"flex-container license-message".concat("active_licence"===t?" flex-grow":"")},a.createElement(Ga,e))}))),Ha=v.ZP.div(La||(La=(0,h.Z)(["\n  a,\n  button {\n    &:hover {\n      cursor: pointer;\n    }\n  }\n"]))),Wa=n(19063),Ka=(0,o.EN)((function(e){var t=e.location,n=e.isPro,r=(0,i.I0)(),s=function(e){return!(arguments.length>1&&void 0!==arguments[1])||arguments[1]?t.pathname===e?"nav-item-active":"":-1!==t.pathname.indexOf("unsaved")&&"/migrate"===e||-1!==t.pathname.indexOf(e)?"nav-item-active":""};return a.createElement("ul",{className:"nav bg-white flex-container"},a.createElement("li",{className:s("/")},a.createElement(l.OL,{to:"/",replace:!0,exact:!0,strict:!0,onClick:function(){r((0,K.oA)())}},(0,w.__)("Profiles","wp-migrate-db"))),a.createElement("li",{className:s("/migrate",!1)},a.createElement(l.OL,{to:"/migrate",replace:!0,onClick:function(){r((0,K.oA)())}},(0,w.__)("Migrate","wp-migrate-db"))),n&&a.createElement("li",{className:s("/backups")},a.createElement(l.OL,{to:"/backups",replace:!0,exact:!0},(0,w.__)("Backups","wp-migrate-db"))),a.createElement("li",{className:s("/settings",!1)},a.createElement(l.OL,{to:"/settings",replace:!0},(0,w.__)("Settings","wp-migrate-db"))),a.createElement("li",{className:s("/addons")},a.createElement(l.OL,{to:"/addons",replace:!0,exact:!0},(0,w.__)("Upgrades","wp-migrate-db"))),a.createElement("li",{className:s("/help")},a.createElement(l.OL,{to:"/help",replace:!0,exact:!0},(0,w.__)("Help","wp-migrate-db"))),a.createElement("li",{className:s("/whats-new")},a.createElement(l.OL,{to:"/whats-new",replace:!0,exact:!0},(0,w.__)("What's New","wp-migrate-db"))))})),qa=function(e){return 0<=navigator.userAgent.indexOf("MSIE")||0<=navigator.userAgent.indexOf("Trident")?a.createElement(z.Z,{type:"danger"},(0,E.ZP)((0,w.__)("<strong>Internet Explorer Not Supported</strong> &mdash; Less than 2% of our customers use IE, so we've decided not to spend time supporting it. We ask that you use Firefox or a Webkit-based browser like Chrome or Safari instead. If this is a problem for you, please let us know.","wp-migrate-db"))):0<=navigator.userAgent.indexOf("Edge")?a.createElement(z.Z,{type:"danger"},(0,E.ZP)((0,w.__)("<strong>Microsoft Edge Not Supported</strong> &mdash; Less than 2% of our customers use Microsoft Edge, so we've decided not to spend time supporting it. We ask that you use Firefox or a Webkit-based browser like Chrome or Safari instead. If this is a problem for you, please let us know.","wp-migrate-db"))):null},Ya=n(7354),Ja=n(68424),Qa=function(e){return e.notifications};function Xa(e,t){return(0,J.fX)(Qa,"notifications",e,t)}var $a=(0,a.lazy)((function(){return(0,_.Yu)()?Promise.resolve().then(n.bind(n,85704)):Promise.resolve(null)})),ei=(0,o.EN)((0,i.$j)((function(e){return{notifications:e.notifications,errors:Xa("errors",e),status:Xa("status",e),settingsStatus:(0,Y.u)("status",e),dbi_api_data:e.dbi_api_data}}),{handleNoticeLink:Ja.Rp,toggleSetting:fa.Xn,setAction:G.m})((function(e){var t=[],n=e.status,r=e.errors,i=e.notifications,s=e.dbi_api_data,c={settings:["licence_expired","wpmdb_invalid_license"],addons:["licence_expired","wpmdb_invalid_license"],help:["wpmdb_invalid_license"]},l=e.location.pathname.replace("/","");return Object.entries(i.messages).forEach((function(o){var u=(0,k.Z)(o,2),p=u[0],m=u[1],d=!1;if((0,Ya.includes)(i.hidden,p)&&(d=!0),Object.entries(c).forEach((function(t){var n=(0,k.Z)(t,2),r=n[0],a=n[1];(0,Ya.includes)(a,p)&&e.location.pathname.includes(r)&&(d=!0)})),d)return null;if(s&&s.dbi_down_status&&"wpmdb_invalid_license"===p)return null;var f=m.message,h=null;m.link&&(h=function(t,n){var r=[],i=Object.entries(t),s=0;return i.forEach((function(c){var l,o,u=(0,k.Z)(c,2),p=u[0],m=u[1];"dismiss"===p&&(l=(0,w.__)("Dismiss","wp-migrate-db")),"reminder"===p&&(l=(0,w.__)("Remind Me Later","wp-migrate-db"),o=t.reminder_time),r.push(a.createElement(a.Fragment,null,a.createElement("button",{className:"link",onClick:function(){e.handleNoticeLink(m,p,o,n)}},l),0===s&&i.length>1?" | ":"")),s++})),a.createElement(a.Fragment,null,r.map((function(e,t){return a.createElement(a.Fragment,{key:t},e)})))}(m.link,p)),m.custom_link&&"usage_tracking"===m.custom_link&&(h=function(e){var t=e.settingsStatus,n=function(){var t=(0,b.Z)((0,g.Z)().mark((function t(n){return(0,g.Z)().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,e.toggleSetting("allow_tracking",n,"allow_tracking");case 2:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}();return a.createElement("div",{className:"flex-container"},a.createElement("button",{className:"link",onClick:function(){n(!0)}},(0,w.__)("Yes, I'd Like to Help","wp-migrate-db"))," ","\xa0|\xa0",a.createElement("button",{className:"link",onClick:function(){n(!1)}},(0,w.__)("No","wp-migrate-db")),a.createElement("div",{className:"relative"},!0===t.allow_tracking&&a.createElement(ot.HW,null)))}(e)),"wpmdb_invalid_license"===p&&(f=m.message[l]||m.message.default||m.message,h=a.createElement($a,null));var v=m.error?"danger":"warning";t.push(a.createElement(z.Z,{type:v},(0,E.ZP)(f),a.createElement(ra,{status:n[p],errors:r[p]},(0,_.Yu)()&&a.createElement(a.Suspense,{fallback:a.createElement("div",null,(0,w.__)("Loading...","wp-migrate-db"))},h),!(0,_.Yu)()&&h)))})),t.map((function(e,t){return a.createElement("div",{key:t}," ",e)}))}))),ti=function(e){var t=e.componentStack,n=e.error;return a.createElement(z.Z,{type:"danger"},a.createElement("p",null,a.createElement("strong",null,"Oops! An error occured!")),a.createElement("p",null,"Here\u2019s what we know\u2026"),a.createElement("p",null,a.createElement("strong",null,"Error:")," ",n.toString()),a.createElement("p",null,a.createElement("strong",null,"Stacktrace:")," ",t))},ni=(0,a.lazy)((function(){return(0,_.Yu)()?Promise.resolve(null):n.e(613).then(n.bind(n,3613))})),ri=function(e){var t=e.isPro;return a.createElement(a.Fragment,null,a.createElement("div",{className:"nav-wrap"},a.createElement("div",{className:"wrapper"},a.createElement(Ka,{isPro:t}),a.createElement(qa,null))),a.createElement("div",{className:"wrapper"},a.createElement(ei,null)),a.createElement(Wa.ErrorBoundary,{FallbackComponent:ti},a.createElement("div",{className:"wrapper".concat(t?"":" wrapper-free"," ").concat((0,o.TH)().pathname.replace("/",""))},a.createElement(y,null,a.createElement("div",null,e.children)),!(0,_.Yu)()&&a.createElement(a.Suspense,{fallback:a.createElement("div",null,(0,w.__)("Loading...","wp-migrate-db"))},a.createElement(ni,e)))))};function ai(e){var t="",n=[],r=e.items,i=e.selected,s=e.defaultTitle,c=e.panelsOpen,l=e.showDefault,o=e.panelName;if(lt()(c,e.type))return a.createElement(Sn,null);var u=", ";e.hasOwnProperty("separator")&&(u=e.separator),i.forEach((function(e){n.push(e)}));var p=r.length,m=i.length,d="(<b>".concat(m," of ").concat(p,"</b>)");return m>0&&!l?(t="".concat(d," ").concat(n.join(u)),e.hideCount&&(t="".concat(n.join(u)))):0===m&&["tables","backups","post_types"].includes(o)&&(t="<em>"+(0,w.__)("Empty selection","wp-migrate-db")+"</em>"),l&&(t=s),a.createElement(Sn,{className:e.panelName},(0,E.ZP)(t))}var ii,si=n(9712),ci=(0,v.ZP)(si.r)(ii||(ii=(0,h.Z)(["\n  position: absolute;\n  top: -4px;\n  right: -30px;\n"])));var li,oi,ui,pi,mi=function(e){var t=e.advancedOption,n=e.advancedOptions;return a.createElement("input",{type:"checkbox",className:t,checked:lt()(n,t),onChange:function(){!function(e,t){e.updateAdvancedOptions(t)}(e,t)}})},di={replace_guids:(0,w.__)("Replace GUIDs","wp-migrate-db"),exclude_spam:(0,w.__)("Exclude spam comments","wp-migrate-db"),exclude_transients:(0,w.__)("Exclude transients","wp-migrate-db"),keep_active_plugins:(0,w.__)("Keep active plugins","wp-migrate-db"),compatibility_older_mysql:(0,w.__)("Compatible with older versions of MySQL (pre-5.5)","wp-migrate-db"),gzip_file:(0,w.__)("Compress file with gzip","wp-migrate-db"),exclude_post_revisions:(0,w.__)("Exclude revisions","wp-migrate-db")},fi=(0,i.$j)((function(e){return{panelsOpen:(0,xn.O)("panelsOpen",e),intent:(0,J.r5)("intent",e),current_migration:e.migrations.current_migration}}),{updateAdvancedOptions:K.vq})((function(e){var t=e.current_migration.advanced_options_selected,n=[];return Object.keys(di).forEach((function(e){lt()(t,e)&&n.push(di[e])})),a.createElement(It.Z,(0,c.Z)({title:e.title,panelName:e.panelName,className:"advanced-options",isOpen:!0,selected:t,panelSummary:a.createElement(ai,(0,c.Z)({},e,{items:H.Zz,selected:n,type:e.panelName,panelsOpen:e.panelsOpen,hideCount:!0,separator:" | "}))},e),a.createElement("fieldset",{className:"options-wrap","aria-label":e.title},a.createElement("div",{className:"options-list"},"import"!==e.intent&&a.createElement(a.Fragment,null,"backup_local"!==e.intent&&a.createElement("div",null,a.createElement("label",{style:{position:"relative"}},a.createElement(mi,(0,c.Z)({advancedOption:"replace_guids",advancedOptions:t},e)),di.replace_guids,a.createElement(ft.Z,{effect:"solid",place:"right",type:"light",delayUpdate:500,delayHide:500,border:!0,className:"action-tooltip",id:"wpmdb-replace-guids-tip"},(0,w.__)("Although the WordPress Codex emphasizes that GUIDs should not be changed, this is limited to sites that are already live. If the site has never been live, we recommend replacing the GUIDs. For example, you may be developing a new site locally at dev.somedomain.com and want to migrate the site live to somedomain.com.","wp-migrate-db")),a.createElement(ci,{"data-tip":!0,"data-for":"wpmdb-replace-guids-tip",className:"question-mark"}))),a.createElement("div",null,a.createElement("label",null,a.createElement(mi,(0,c.Z)({advancedOption:"exclude_spam",advancedOptions:t},e)),di.exclude_spam)),a.createElement("div",null,a.createElement("label",null,a.createElement(mi,(0,c.Z)({advancedOption:"exclude_transients",advancedOptions:t},e)),(0,E.ZP)((0,w.gB)((0,w.__)('Exclude <a href="%s" target="_blank" rel="noopener noreferrer">transients</a>',"wp-migrate-db"),"https://developer.wordpress.org/apis/handbook/transients/"))," ",a.createElement("span",{className:"dark"},(0,w.__)("(temporary cached data)","wp-migrate-db"))))),lt()(["push","pull","import"],e.intent)&&a.createElement("div",null,a.createElement("label",null,a.createElement(mi,(0,c.Z)({advancedOption:"keep_active_plugins",advancedOptions:t},e)),(0,w.gB)((0,w.__)("Do not %s the 'active_plugins' setting","wp-migrate-db"),e.intent),a.createElement("br",null),a.createElement("small",null,(0,w.__)("(i.e. which plugins are activated/deactivated)","wp-migrate-db")))),"savefile"===e.intent&&a.createElement("div",null,a.createElement("label",null,a.createElement(mi,(0,c.Z)({advancedOption:"compatibility_older_mysql",advancedOptions:t},e)),(0,w.__)("Compatible with older versions of MySQL","wp-migrate-db")," ",a.createElement("span",{className:"dark"},(0,w.__)("(pre-5.5)","wp-migrate-db")))),lt()(["savefile","backup_local"],e.intent)&&a.createElement("div",null,a.createElement("label",null,a.createElement(mi,(0,c.Z)({advancedOption:"gzip_file",advancedOptions:t},e)),di.gzip_file)),a.createElement("div",null,a.createElement("label",null,a.createElement(mi,(0,c.Z)({advancedOption:"exclude_post_revisions",advancedOptions:t},e)),di.exclude_post_revisions)))))})),_i=n(31125),gi=(n(75358),v.ZP.section(li||(li=(0,h.Z)(["\n  display: grid;\n"])))),bi=function(e){var t=e.intent,n=e.className,r=e.children;return a.createElement(gi,{className:n||""},a.createElement("div",{className:"header-row row"},a.createElement("div",{className:"header-2"},(0,w.__)("Find","wp-migrate-db")),a.createElement("div",{className:"header-4"},"savefile"===t&&(0,w.__)("Replace (Optional)","wp-migrate-db"),"savefile"!==t&&(0,w.__)("Replace","wp-migrate-db"))),r)},hi=n(4669),vi=n(69479),Ei=v.ZP.span(oi||(oi=(0,h.Z)(["\n  padding: 0 0.5rem;\n  border-left: 1px solid ",";\n\n  &.first {\n    border-left: none;\n    padding-left: 0;\n  }\n"])),Nn.Qp),wi=(0,v.ZP)(vi.r)(ui||(ui=(0,h.Z)(["\n  margin: 0 0.3rem;\n"]))),yi=v.ZP.span(pi||(pi=(0,h.Z)(["\n  position: relative;\n  top: 4px;\n"])));function xi(e){var t="",n=e.fields,r=e.panelsOpen,i=e.searchKeys;if(lt()(r,e.type))return a.createElement(Sn,null);var s=[];return n.forEach((function(e){e[i.search].length&&s.push(a.createElement(a.Fragment,null,e[i.search],a.createElement(yi,null,a.createElement(wi,null)),e[i.replace]))})),s.length>0&&(t=a.createElement(a.Fragment,null,s.map((function(e,t){return a.createElement(Ei,{key:t,className:0===t?"first":""},e)})))),a.createElement(Sn,null,t)}var ki=function(e){return e.migrations.search_replace};function Zi(e,t){return(0,J.fX)(ki,"search_replace",e,t)}var Ni=function(e){var t=e.count,n=e.panelsOpen,r=e.title,i=e.panelKey,s=e.panelName;return t>0&&!n.includes(i)?a.createElement("h4",{id:"panel-title-".concat(s),className:"panel-title"},r," ",a.createElement("b",{className:"count"},"(",t,")")):a.createElement("h4",{id:"panel-title-".concat(s),className:"panel-title"},r)},Pi=n(95402),Si=n(22497),Ti=function(e){var t=e.enabled,n=e.screenReaderText,r=e.onClick;return t?a.createElement("button",{onClick:r},a.createElement(vi.r,{"aria-hidden":"true"}),a.createElement("span",{className:"screen-reader-text"},n)):a.createElement(vi.r,{"aria-hidden":"true"})};var Oi=function(e,t){e.toggleStandardSearchReplace(t)},Ai=(0,i.$j)((function(e){var t=(0,xn.O)("panelsOpen",e),n=(0,J.NR)("search_replace",e),r=Zi("standard_search_replace",e),a=Zi("standard_options_enabled",e),i=(0,J.r5)("selected_existing_profile",e),s=e.multisite_tools;return{search_replace:n,standard_search_replace:r,standard_options_enabled:a,panelsOpen:t,migrations:e.migrations,existingProfile:i,mst:s}}),{updateStandardSearchReplace:hi.mX,toggleStandardSearchReplace:hi.rL})((function(e){var t,n,r=e.standard_search_replace,s=e.standard_options_enabled,l=e.panelsOpen,o=e.migrations,u=e.existingProfile,p=e.mst,m=o.current_migration.intent,d=r.domain,f=r.path,_=(0,a.useState)(""),g=(0,k.Z)(_,2),b=g[0],h=g[1],v=(0,a.useState)(null!==(t=d.replace)&&void 0!==t?t:""),y=(0,k.Z)(v,2),x=y[0],Z=y[1],N=(0,a.useState)(null!==(n=f.replace)&&void 0!==n?n:""),P=(0,k.Z)(N,2),S=P[0],T=P[1],O=(0,i.I0)(),A=[];return(0,a.useEffect)((function(){if("savefile"===m&&null===u){var t=(0,_i.Z)(e.panelsOpen);t.push("standard_fields"),O((0,W.G7)(t))}}),[]),(0,a.useEffect)((function(){if("savefile"===m){var e=(0,Pi.F)({intent:m,remote_site:{url:x,path:S},local_site:o.local_site});e=O((0,Si.O)("wpmdb_standard_replace_values",e)),O((0,hi.mX)(e))}}),[x,S,p]),lt()(s,"domain")&&("savefile"===m?d.replace&&A.push(d):A.push(d)),lt()(s,"path")&&("savefile"===m?f.replace&&A.push(f):A.push(f)),a.createElement(It.Z,(0,c.Z)({title:e.title,panelName:e.panelName,isOpen:!0,header:a.createElement(Ni,(0,c.Z)({count:s.length,panelsOpen:l,title:e.title,panelKey:"standard_fields"},e)),selected:s,panelSummary:a.createElement(xi,(0,c.Z)({},e,{fields:A,enabled:s,type:e.panelName,searchKeys:{search:"search",replace:"replace"},panelsOpen:e.panelsOpen})),bodyClass:"standard-search-replace search-replace"},e),a.createElement(bi,{intent:m},a.createElement("div",{className:"content-row-wrap"},a.createElement("div",{className:"row content-row"},a.createElement("div",{className:"cell"},"savefile"!==m&&a.createElement("input",{type:"checkbox",onChange:function(){Oi(e,"domain")},checked:lt()(s,"domain"),"aria-label":(0,w.__)("Find and replace URLs","wp-migrate-db")})),a.createElement("div",{className:"cell search"},d.search),a.createElement("div",{className:"cell custom-search-arrow"},a.createElement(Ti,{screenReaderText:(0,w.__)("Copy find field to replace field","wp-migrate-db"),enabled:"savefile"===m,onClick:function(){Z(d.search)}})),a.createElement("div",{className:"cell replace"},"savefile"===m&&a.createElement("input",{type:"text","aria-label":(0,w.__)("Replace","wp-migrate-db"),onChange:function(e){Z(e.target.value?e.target.value.trim():"")},defaultValue:x}),"savefile"!==m&&d.replace),a.createElement("div",{className:"question cell"},a.createElement(ft.Z,{effect:"solid",place:"left",type:"light",delayHide:500,delayUpdate:500,border:!0,className:"action-tooltip",id:"wpmdb-search-replace-url-tip",getContent:function(){return"url"!==b?null:(0,E.ZP)((0,w.gB)((0,w.__)('This find & replace updates the domain of all the matching URLs in\n                  your database. You may wonder why we have double slashes in front\n                  of the domain name. If we excluded those, it could replace your\n                  domain in unwanted places like email addresses. You probably don\'t\n                  want <NAME_EMAIL> with\n                  <EMAIL>. More details in our\n                  <a\n                    href="%s"\n                    target="_blank"\n                    rel="noopener noreferrer"\n                  >\n                    Find & Replace Doc \u2192\n                  </a>',"wp-migrate-db"),"https://deliciousbrains.com/wp-migrate-db-pro/doc/find-and-replace/?utm_campaign=support%2Bdocs&utm_source=MDB%2BFree&utm_medium=insideplugin&utm_content=new%2Burl%2Bhelp%2Bbubble"))}}),a.createElement(si.r,{"data-tip":!0,"data-for":"wpmdb-search-replace-url-tip",onMouseOver:function(){return h("url")}})))),a.createElement("div",{className:"content-row-wrap"},a.createElement("div",{className:"row content-row"},a.createElement("div",{className:"cell"},"savefile"!==m&&a.createElement("input",{type:"checkbox",onChange:function(){Oi(e,"path")},checked:lt()(s,"path"),"aria-label":(0,w.__)("Find and replace web root path","wp-migrate-db")})),a.createElement("div",{className:"cell search"},f.search),a.createElement("div",{className:"cell custom-search-arrow"},a.createElement(Ti,{screenReaderText:(0,w.__)("Copy find field to replace field","wp-migrate-db"),enabled:"savefile"===m,onClick:function(){T(f.search)}})),a.createElement("div",{className:"cell replace"},"savefile"===m&&a.createElement("input",{type:"text","aria-label":(0,w.__)("Replace","wp-migrate-db"),onChange:function(e){T(e.target.value?e.target.value.trim():"")},defaultValue:S}),"savefile"!==m&&f.replace),a.createElement("div",{className:"question cell"},a.createElement(ft.Z,{effect:"solid",place:"left",type:"light",delayHide:500,delayUpdate:500,border:!0,className:"action-tooltip",id:"wpmdb-search-replace-path-tip",getContent:function(){return"path"!==b?null:(0,E.ZP)((0,w.gB)((0,w.__)('The web root paths differ between your two sites. We\'ll update any\n                              matching file paths that exist in your database. More details in\n                              our<br /><a href="%s" target="_blank" rel="noopener noreferrer">Find & Replace Doc \u2192</a>',"wp-migrate-db"),"https://deliciousbrains.com/wp-migrate-db-pro/doc/find-and-replace/?utm_campaign=support%2Bdocs&utm_source=MDB%2BFree&utm_medium=insideplugin&utm_content=new%2Burl%2Bhelp%2Bbubble"))}}),a.createElement(si.r,{"data-tip":!0,"data-for":"wpmdb-search-replace-path-tip",onMouseOver:function(){return h("path")}}))))),s.length<2&&"savefile"!==m&&a.createElement(z.Z,{type:"warning"},(0,w.__)("Turning off these standard find & replace operations\n          could break this site. Only disable them if you are confident in\n          what you're doing.","wp-migrate-db")))})),Ci=function(e){var t=e.intent;return a.createElement(z.Z,{type:"warning"},a.createElement("h4",null,(0,w.__)("Mixed Case Table Name","wp-migrate-db")),a.createElement("p",null,(0,E.ZP)((0,w.gB)((0,w.__)("Whoa! We've detected that your <b>%s</b> site has the MySQL setting <code>lower_case_table_names</code> set to <code>1</code>.","wp-migrate-db"),"push"===t?(0,w.__)("remote","wp-migrate-db"):(0,w.__)("local","wp-migrate-db")))),a.createElement("p",null,(0,w.__)("As a result, uppercase characters in table names will be converted to lowercase during the migration.","wp-migrate-db")),a.createElement("p",null,(0,E.ZP)((0,w.gB)((0,w.__)('You can read more about this in <a href="%s" target="_blank" rel="noopener noreferrer">our documentation</a>, proceed with caution.',"wp-migrate-db"),"https://deliciousbrains.com/wp-migrate-db-pro/doc/mixed-case-table-names/?utm_campaign=error%2Bmessages&utm_source=MDB%2BPaid&utm_medium=insideplugin"))))},Ri=function(e){var t=[],n=e.status,r=void 0===n?{}:n,i=e.intent;return r.mixed_case_table_name_warning&&t.push(a.createElement(Ci,(0,c.Z)({intent:i},e))),t},Ii={push:{all:(0,w.__)("Push all tables with prefix","wp-migrate-db"),selected:(0,w.__)("Push only the selected tables below","wp-migrate-db"),backup:(0,w.__)("Backup the remote database before replacing it","wp-migrate-db")},pull:{all:(0,w.__)("Pull all tables with prefix","wp-migrate-db"),selected:(0,w.__)("Pull only the selected tables below","wp-migrate-db"),backup:(0,w.__)("Backup the local database before replacing it","wp-migrate-db")},import:{all:(0,w.__)("Import all tables with prefix","wp-migrate-db"),selected:(0,w.__)("Import only selected tables below","wp-migrate-db"),backup:(0,w.__)("Backup the database before running the import","wp-migrate-db"),backup_selected:(0,w.__)("Backup only the tables that will be replaced during the import","wp-migrate-db")},savefile:{all:(0,w.__)("Export all tables with prefix","wp-migrate-db"),selected:(0,w.__)("Export only selected tables below","wp-migrate-db"),post_types_all:(0,w.__)("Export all post types","wp-migrate-db"),post_types_selected:(0,w.__)("Export only post types selected below","wp-migrate-db")},backup_local:{all:(0,w.__)("Backup all tables with prefix","wp-migrate-db"),selected:(0,w.__)("Backup only selected tables below","wp-migrate-db"),post_types_all:(0,w.__)("Backup all post types","wp-migrate-db"),post_types_selected:(0,w.__)("Backup only post types selected below")},find_replace:{all:(0,w.__)("Search in all tables with prefix","wp-migrate-db"),selected:(0,w.__)("Search only in selected tables below","wp-migrate-db"),backup:(0,w.__)("Backup the database before running the find & replace","wp-migrate-db"),post_types_all:(0,w.__)("Search in all post types","wp-migrate-db"),post_types_selected:(0,w.__)("Search in only post types selected below","wp-migrate-db")}},Di=function(e){var t=e.intent,n="find_replace"===t?(0,w.__)("The post types you <em>don't</em> select will not be included in the find & replace.","wp-migrate-db"):(0,w.__)("The post types you <em>don't</em> select will be absent in the destination posts table after migration.","wp-migrate-db");return a.createElement("div",{className:"options-list"},a.createElement("div",null,a.createElement("input",{type:"radio",name:"post-types[]",checked:"all"===e.postTypesOption,onChange:function(){return e.togglePostTypesSelector("all")},id:"posttypes-all"}),a.createElement("label",{htmlFor:"posttypes-all"},["push","pull"].includes(t)?(0,w.__)("Migrate all post types","wp-migrate-db"):Ii[t].post_types_all)),a.createElement("div",null,a.createElement("input",{type:"radio",name:"post-types[]",checked:"selected"===e.postTypesOption,onChange:function(){return e.togglePostTypesSelector("selected")},id:"posttypes-selected"}),a.createElement("label",{htmlFor:"posttypes-selected"},["push","pull"].includes(t)?(0,w.__)("Migrate only selected post types below","wp-migrate-db"):Ii[t].post_types_selected),"selected"===e.postTypesOption&&a.createElement(z.Z,{type:"warning"},(0,E.ZP)(n))))};var Li=(0,i.$j)((function(e){var t=(0,xn.O)("panelsOpen",e),n=(0,J.r5)("post_types_option",e),r=(0,J.r5)("post_types_selected",e),a=(0,J.r5)("advanced_options_selected",e);return{panelsOpen:t,post_types_selected:r,post_types_option:n,status:(0,J.r5)("status",e),advanced_options_selected:a}}),{updatePostTypesOption:K.t9,updatePostTypesSelected:K.gy})((function(e){var t=e.postTypes,n=e.post_types_option,r=e.post_types_selected,i=e.intent,s=t.filter((function(e){return"revision"!==e})),l=function(t){e.updatePostTypesOption(t,s)};return a.createElement(It.Z,(0,c.Z)({title:e.title,panelName:e.panelName,isOpen:!0,selected:r,hasDefault:!0,panelSummary:a.createElement(ai,(0,c.Z)({},e,{items:s,selected:r,title:e.title,type:e.panelName,panelsOpen:e.panelsOpen,defaultTitle:["push","pull"].includes(i)?(0,w.__)("Migrate all post types","wp-migrate-db"):Ii[i].post_types_all,showDefault:"selected"!==n}))},e),a.createElement(Ta.Z,{id:"post-type-multiselect",options:s,stateManager:function(t){e.updatePostTypesSelected(t)},selected:r,handleToggle:l,visible:"selected"===n,updateSelected:e.updatePostTypesSelected,selectInverse:function(){return(0,Oa.Z)(e.updatePostTypesSelected,s,r)},ToggleButtons:a.createElement(Di,{postTypesOption:n,togglePostTypesSelector:l,intent:i})}),(0,dn.a)(e.status,"POST_TYPES_SELECTED_EMPTY")&&a.createElement(z.Z,{type:"danger"},(0,w.__)("Please select post types to exclude or migrate all post types","wp-migrate-db")))})),Mi=function(e){return a.createElement("div",{className:"options-list",role:"radiogroup","aria-label":(0,w.__)("Tables migration options","wp-migrate-db")},a.createElement("div",null,a.createElement("input",{type:"radio",name:"tables",checked:"all"===e.tablesOption,onChange:function(){return e.toggleTableSelector("all")},id:"all-tables"}),a.createElement("label",{htmlFor:"all-tables"},Ii[e.intent].all,' "',e.prefix,'"')),a.createElement("div",null,a.createElement("input",{type:"radio",name:"tables",checked:"selected"===e.tablesOption,onChange:function(){return e.toggleTableSelector("selected")},id:"selected-tables"}),a.createElement("label",{htmlFor:"selected-tables"},Ii[e.intent].selected)))};var Fi=(0,i.$j)((function(e){var t=(0,xn.O)("panelsOpen",e),n=(0,J.r5)("tables_option",e),r=(0,J.r5)("tables_selected",e),a=(0,J.r5)("databaseEnabled",e);return{panelsOpen:t,tablesOption:n,tablesSelected:r,status:(0,J.r5)("status",e),databaseEnabled:a}}),{updateSelectedTables:K.E2,updateTablesOption:K.S5})((function(e){var t=e.intent,n=e.tables,r=e.prefix,i=e.tablesSelected,s=e.tablesOption,l=e.tableSizes,o=e.databaseEnabled,u=function(t){if(e.disabled)return!1;e.updateTablesOption(t,n)},p=(0,x.Z)({},l);for(var m in l)p[m]=' <span class="table-size">('.concat(l[m],")</span>");var d=function(){return o?(0,dn.a)(e.status,"TABLES_SELECTED_EMPTY")&&a.createElement(z.Z,{type:"danger"},(0,w.__)("Please select at least one table to migrate","wp-migrate-db")):null};return a.createElement(It.Z,(0,c.Z)({title:e.title,selected:i,hasDefault:!0,panelSummary:a.createElement(ai,(0,c.Z)({},e,{items:n,selected:i,type:e.panelName,panelsOpen:e.panelsOpen,defaultTitle:"".concat(Ii[t].all,' "').concat(e.prefix,'"'),showDefault:"selected"!==s})),panelName:e.panelName,isOpen:!0},e),a.createElement(Ta.Z,{id:"tables-multiselect",options:n,extraLabels:p,stateManager:function(t){e.updateSelectedTables(t)},selected:i,handleToggle:u,visible:"selected"===s,updateSelected:e.updateSelectedTables,selectInverse:function(){return(0,Oa.Z)(e.updateSelectedTables,n,i)},ToggleButtons:a.createElement(Mi,{tablesOption:s,toggleTableSelector:u,intent:t,prefix:r})}),a.createElement(d,null))})),Bi=function(e){var t=e.uploads_dir;return a.createElement(z.Z,{type:"danger"},a.createElement("strong",null,(0,w.__)("Migration disabled","wp-migrate-db"))," \u2014",(0,E.ZP)((0,w.gB)((0,w.__)("This migration type has been disabled because the local uploads directory is not writable: <code>%s</code>. Please update the permissions of this directory and try again.","wp-migrate-db"),t))," ",a.createElement("a",{href:"https://deliciousbrains.com/wp-migrate-db-pro/doc/uploads-folder-permissions/",target:"_blank",rel:"noopener noreferrer"},(0,w.__)("More information.","wp-migrate-db")))},ji=n(47895),Ui=function(e){var t=e.prefix,n=(0,i.v9)((function(e){return e.migrations.import_data})).status;return a.createElement("div",{className:"options-list"},a.createElement("div",null,a.createElement("input",{type:"radio",name:"backup[]",checked:"none"===e.backupOption,onChange:function(){return e.toggleBackupsSelector("none")},id:"no-backup"}),a.createElement("label",{htmlFor:"no-backup"},"No backup")),a.createElement("div",null,a.createElement("input",{type:"radio",name:"backup[]",checked:"backup_only_with_prefix"===e.backupOption,onChange:function(){return e.toggleBackupsSelector("backup_only_with_prefix")},id:"prefix-backup"}),a.createElement("label",{htmlFor:"prefix-backup"},(0,w.gB)((0,w.__)('Backup all tables with "%s" prefix',"wp-migrate-db"),t))),"NON_MDB_IMPORT_ERROR"!==n&&a.createElement("div",null,a.createElement("input",{type:"radio",name:"backup[]",checked:"backup_selected"===e.backupOption,onChange:function(){return e.toggleBackupsSelector("backup_selected")},id:"backup-selected"}),a.createElement("label",{htmlFor:"backup-selected"},"import"===e.intent?Ii.import.backup_selected:(0,w.__)("Backup only tables selected for migration","wp-migrate-db"))),a.createElement("div",null,a.createElement("input",{type:"radio",name:"backup[]",checked:"backup_manual_select"===e.backupOption,onChange:function(){return e.toggleBackupsSelector("backup_manual_select")},id:"manual-backup"}),a.createElement("label",{htmlFor:"manual-backup"},(0,w.__)("Backup only selected tables below","wp-migrate-db"))))},zi=["tables_selected","backup_option","status","tables","tableSizes","prefix","local_site","remote_site","backup_tables_selected","intent"];var Gi,Vi=function(e){return{none:(0,w.__)("No backup","wp-migrate-db"),backup_only_with_prefix:(0,w.gB)((0,w.__)('Backup all tables with "%s" prefix',"wp-migrate-db"),e.prefix),backup_selected:(0,w.__)("Backup tables selected for migration","wp-migrate-db"),backup_manual_select:(0,w.__)("Backup selected tables","wp-migrate-db")}},Hi=(0,i.$j)((function(e){var t=(0,xn.O)("panelsOpen",e),n=(0,J.r5)("backup_tables_selected",e),r=(0,J.r5)(["tables_selected","backup_option","status"],e),a=(0,k.Z)(r,3);return{panelsOpen:t,backup_tables_selected:n,tables_selected:a[0],backup_option:a[1],status:a[2],remote_site:e.migrations.remote_site,local_site:e.migrations.local_site,panels:e.panels}}),{updateBackupsTables:K.qg,updateBackupsOption:K.P_,updatePanelTitle:W.H5})((function(e){e.tables_selected;var t=e.backup_option,n=e.status,r=e.tables,i=e.tableSizes,s=e.prefix,l=e.local_site,o=e.remote_site,u=e.backup_tables_selected,p=e.intent,m=(0,Z.Z)(e,zi),d=(0,x.Z)({},m),f=s;"import"!==p&&"pull"!==p||(f=l.this_prefix),"push"===p&&(f=o.prefix),d.prefix=f;var g=(0,_.qu)(p,l,o),b=g.disabled,h=g.uploads_dir,v=function(e){if(b)return null;m.updateBackupsOption(e,Vi(d))},E=(0,x.Z)({},i);for(var y in i)E[y]='  <span class="table-size">('.concat(i[y],")</span>");return a.createElement(a.Fragment,null,a.createElement(It.Z,(0,c.Z)({title:m.title,panelName:m.panelName,isOpen:!0,disabled:b,selected:u,hasDefault:!0,panelSummary:a.createElement(ai,(0,c.Z)({},m,{items:r,selected:u,title:m.title,type:m.panelName,panelsOpen:m.panelsOpen,defaultTitle:Vi(d)[t],showDefault:"backup_manual_select"!==t}))},m),a.createElement(Ta.Z,{id:"backups-multiselect",options:r,extraLabels:E,visible:"backup_manual_select"===t,stateManager:function(e){m.updateBackupsTables(e)},selected:u,handleToggle:v,updateSelected:m.updateBackupsTables,selectInverse:function(){return(0,Oa.Z)(m.updateBackupsTables,r,u)},ToggleButtons:a.createElement(Ui,{backupOption:t,toggleBackupsSelector:v,prefix:f,intent:p,disabled:b})}),a.createElement(z.Z,{type:"info",className:"backup-dir"},a.createElement("p",null,(0,w.gB)((0,w.__)("A file will be saved to %s","wp-migrate-db"),h))),(0,dn.a)(n,"BACKUP_TABLES_SELECTED_EMPTY")&&a.createElement(z.Z,{type:"danger"},a.createElement("p",null,(0,w.__)("Please select at least one table to backup","wp-migrate-db")))))})),Wi=n(2474),Ki=n.n(Wi),qi=n(41919),Yi=function(e,t,n){var r=Array.from(e),a=r.splice(t,1),i=(0,k.Z)(a,1)[0];return r.splice(n,0,i),r};function Ji(){return Ji=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},Ji.apply(this,arguments)}var Qi,Xi=function(e){return a.createElement("svg",Ji({height:16,viewBox:"0 0 16 16",width:16,xmlns:"http://www.w3.org/2000/svg"},e),Gi||(Gi=a.createElement("g",{fill:"#0777ef",fillRule:"evenodd",transform:"translate(3 3)"},a.createElement("rect",{height:10,rx:1,width:2,x:4}),a.createElement("rect",{height:10,rx:1,transform:"rotate(-90 5 5)",width:2,x:4}))))};n.p;function $i(){return $i=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},$i.apply(this,arguments)}var es,ts=function(e){return a.createElement("svg",$i({xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24"},e),Qi||(Qi=a.createElement("g",{fill:"#999",fillRule:"evenodd"},a.createElement("path",{d:"M4 5h16v2H4zM4 9h16v2H4zM4 13h16v2H4zM4 17h16v2H4z"}))))};n.p;function ns(){return ns=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},ns.apply(this,arguments)}var rs,as=function(e){return a.createElement("svg",ns({xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24"},e),es||(es=a.createElement("g",{fill:"none",fillRule:"evenodd"},a.createElement("circle",{cx:12,cy:12,r:8,fill:"#999"}),a.createElement("path",{fill:"#EEE",fillRule:"nonzero",d:"m13.5 12 2.252-2.251c.33-.33.33-.87 0-1.201l-.3-.3a.851.851 0 0 0-1.2 0L12 10.5 9.748 8.248a.851.851 0 0 0-1.2 0l-.3.3c-.33.33-.33.87 0 1.2L10.497 12l-2.25 2.252c-.33.33-.33.87 0 1.2l.3.3c.33.33.87.33 1.2 0L12 13.502l2.251 2.25c.33.33.871.33 1.201 0l.3-.3c.33-.33.33-.87 0-1.2L13.501 12Z"}))))},is=(n.p,n(48462)),ss=n.n(is),cs=function(e){return function(){var t=(0,b.Z)((0,g.Z)().mark((function t(n,r){var a,i,s,c,l;return(0,g.Z)().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(a=(0,J.NR)("search_replace",r()),i=a.custom_search_replace,!(s=i[e])){t.next=16;break}if(""!==(c=s.replace_old)){t.next=6;break}return t.abrupt("return",!0);case 6:return t.prev=6,t.next=9,(0,_.op)("/regex-validate",{pattern:ss()(c)});case 9:return l=t.sent,t.abrupt("return",l.success&&!0===l.data);case 13:return t.prev=13,t.t0=t.catch(6),t.abrupt("return",!1);case 16:return t.abrupt("return",!1);case 17:case"end":return t.stop()}}),t,null,[[6,13]])})));return function(e,n){return t.apply(this,arguments)}}()},ls=function(e,t){return t["regex"===e?"case_sensitive":"regex"]};function os(){return os=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},os.apply(this,arguments)}var us,ps,ms=function(e){return a.createElement("svg",os({width:20,height:12,viewBox:"0 0 20 13",fill:"none",xmlns:"http://www.w3.org/2000/svg"},e),rs||(rs=a.createElement("path",{d:"M.942 12.474c.542 0 .845-.259 1.058-.932l.836-2.432h4.39l.836 2.432c.205.665.525.932 1.067.932.578 0 .96-.328.96-.845 0-.216-.054-.449-.151-.734L6.604 1.742C6.302.871 5.867.5 5.058.5c-.81 0-1.245.38-1.556 1.242L.17 10.895c-.116.328-.169.553-.169.76 0 .5.382.82.942.82ZM3.316 7.61l1.67-4.978h.098l1.663 4.978H3.316ZM18.009 11.189h.098v.405c.044.544.39.863.933.863.613 0 .96-.38.96-1.035v-5.22c0-1.984-1.324-3.088-3.698-3.088-.977 0-1.724.164-2.293.466-.791.414-1.165.931-1.165 1.458 0 .414.285.699.72.699.338 0 .57-.087.81-.311.542-.535 1.057-.794 1.804-.794 1.2 0 1.849.535 1.849 1.562v.81H15.68c-2.098.01-3.36 1.036-3.36 2.718 0 1.657 1.236 2.778 3.067 2.778 1.164 0 2.106-.466 2.622-1.311Zm-3.707-1.562c0-.837.605-1.311 1.654-1.311h2.07v.983c0 .984-.88 1.743-2.026 1.743-1.013 0-1.698-.57-1.698-1.415Z",fill:"#30578C"})))};n.p;function ds(){return ds=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},ds.apply(this,arguments)}var fs=function(e){return a.createElement("svg",ds({width:16,height:12,viewBox:"0 0 16 13",fill:"none",xmlns:"http://www.w3.org/2000/svg"},e),us||(us=a.createElement("path",{d:"m15.735 7.569-3.03-1.652 3.03-1.653a.506.506 0 0 0 .196-.699l-.415-.714a.513.513 0 0 0-.708-.18l-2.956 1.78.074-3.432a.51.51 0 0 0-.51-.519h-.831a.51.51 0 0 0-.511.519l.074 3.432-2.956-1.78a.513.513 0 0 0-.708.18l-.415.714a.506.506 0 0 0 .196.7l3.03 1.652-3.03 1.652a.506.506 0 0 0-.196.7l.415.713a.513.513 0 0 0 .708.18l2.956-1.78-.074 3.433a.51.51 0 0 0 .51.518h.831a.51.51 0 0 0 .511-.518l-.074-3.432 2.956 1.78a.513.513 0 0 0 .708-.181l.415-.714a.506.506 0 0 0-.196-.699Z",fill:"#30578C"})),ps||(ps=a.createElement("circle",{cx:1.5,cy:11,r:1.5,fill:"#30578C"})))},_s=(n.p,function(e){var t=e.Icon,n=e.onChange,r=e.tooltip,i=e.index,s=e.className,c=e.value,l=e.disabled,o=e.type,u=e.isDisabled,p=(0,a.useState)(null!==c&&void 0!==c&&c),m=(0,k.Z)(p,2),d=m[0],f=m[1],_=(0,a.useState)(""),g=(0,k.Z)(_,2),b=g[0],h=g[1],v="search-replace-tooltip-".concat(i+o);return a.createElement("div",{className:"search-replace-option ".concat(s)},a.createElement(ft.Z,{effect:"solid",place:"top",type:"light",delayUpdate:500,delayHide:50,border:!0,className:"action-tooltip search-replace-option-tooltip",id:v,getContent:function(){return b!==v?null:(0,E.ZP)(r)}}),a.createElement("input",{"data-for":v,"data-tip":r,className:u?"option-disabled":"",id:o+i,type:"checkbox",value:d,checked:d,disabled:null!==l&&void 0!==l&&l,onChange:function(e){f(e.target.checked),n&&n(e.target.checked)},onFocus:function(){return h(v)}}),a.createElement("label",{"data-for":v,"data-tip":r,htmlFor:o+i,className:"toggle ".concat(d?"active":""),onMouseOver:function(){return h(v)}},a.createElement(t,{className:"option-icon","aria-hidden":"true"}),a.createElement("span",{className:"screen-reader-text"},(0,E.ZP)(r))))}),gs=function(e){var t=e.index,n=e.item,r=n.regex,s=(0,_.Yu)(),c=(0,_.ME)(),l=c||ls("regex",n),o=(0,i.I0)(),u=(0,a.useState)(r),p=(0,k.Z)(u,2),m=p[0],d=p[1],f=(0,w.gB)((0,w.__)("Run a <b>find and replace</b> using a <b>regular expression</b>. Delimiters required and modifiers accepted. <br> <a class='semibold underline' href='%s' target='_blank'>Learn more about Regex</a>"),"https://deliciousbrains.com/wp-migrate-db-pro/doc/find-and-replace/?utm_campaign=support%2Bdocs&utm_source=%7B$tracking_base%7D&utm_medium=insideplugin&utm_content=new%2Burl%2Bvalidation#regex-find-replace");l&&c&&(f=(0,_.mF)("regex",s));var h=(0,a.useCallback)((0,Ya.debounce)((function(e,t){o(function(e,t){return function(){var n=(0,b.Z)((0,g.Z)().mark((function n(r){var a;return(0,g.Z)().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:if(!e){n.next=6;break}return n.next=3,r(cs(t));case 3:return a=n.sent,r((0,hi.zf)({key:t,option:"isValidRegex",value:a})),n.abrupt("return",a);case 6:return r((0,hi.zf)({key:t,option:"isValidRegex",value:null})),n.abrupt("return",!0);case 8:case"end":return n.stop()}}),n)})));return function(e){return n.apply(this,arguments)}}()}(e,t))}),300),[]);return(0,a.useEffect)((function(){n.case_sensitive&&d(!1)}),[n]),(0,a.useEffect)((function(){h(m,t),m?(o((0,hi.zf)({key:t,option:"replace_old_placeholder",value:'/\\[foo id="([0-9]+)"/'})),o((0,hi.zf)({key:t,option:"replace_new_placeholder",value:'[bar id="$1"'}))):(o((0,hi.zf)({key:t,option:"replace_old_placeholder",value:null})),o((0,hi.zf)({key:t,option:"replace_new_placeholder",value:null})))}),[n.replace_old,m]),a.createElement(_s,{Icon:fs,type:"regex",tooltip:f,index:t,value:m,isDisabled:l,disabled:c,onChange:function(e){d(e),o((0,hi.zf)({key:t,option:"regex",value:e})),e&&o((0,hi.zf)({key:t,option:"case_sensitive",value:!1}))}})},bs=function(e){var t=e.index,n=e.item,r=n.case_sensitive,s=(0,i.I0)(),c=(0,a.useState)(r),l=(0,k.Z)(c,2),o=l[0],u=l[1];return(0,a.useEffect)((function(){n.regex&&u(!1)}),[n]),(0,a.useEffect)((function(){s((0,hi.zf)({key:t,option:"case_sensitive",value:o})),o&&s((0,hi.zf)({key:t,option:"regex",value:!1}))}),[o]),a.createElement(_s,{Icon:ms,type:"case-sensitive",tooltip:(0,w.__)("Run a case-sensitive find and replace.","wp-migrate-db"),className:"case-sensitive",index:t,value:r,isDisabled:ls("case_sensitive",n),onChange:function(e){u(e)}})},hs=(0,i.$j)((function(e){return{local_site:e.migrations.local_site}}))((function(e){var t=e.index,n=e.item,r=e.local_site,i=r.this_url,s=r.this_path;return(0,_.Ph)(i)===n.replace_old||s===n.replace_old?null:a.createElement("fieldset",{"aria-label":(0,w.__)("Find and replace row options","wp-migrate-db")},a.createElement(gs,{index:t,item:n}),a.createElement(bs,{index:t,item:n}))}));var vs,Es=function(e,t,n,r){e.updateCustomSearchReplace({key:t,option:n,value:r})},ws=function(e,t){t.preventDefault();var n="keypress"===t.type;e.addItemCustomSearchReplace(n)},ys=(0,i.$j)((function(e){var t=(0,J.r5)("status",e),n=(0,xn.O)("panelsOpen",e),r=(0,J.NR)("search_replace",e);return{status:t,panelsOpen:n,custom_search_replace:r.custom_search_replace,search_replace:r,existingProfile:(0,J.r5)("selected_existing_profile",e),intent:(0,J.r5)("intent",e)}}),{updateCustomSearchReplace:hi.zf,reorderCustomSearchReplace:hi.GJ,addItemCustomSearchReplace:hi.FL,deleteItemCustomSearchReplace:hi.Px,setCustomSearchReplace:hi.EX})((function(e){var t=e.custom_search_replace,n=e.panelsOpen,r=e.status,s=e.search_replace,l=e.intent,o=e.existingProfile,u=(0,i.I0)();(0,a.useEffect)((function(){if("savefile"===l&&null!==o){var e=n.filter((function(e){return"custom_fields"!==e}));u((0,W.G7)(e))}}),[]);var p=s.custom_search_domain_locked,m=t.map((function(t,n){return a.createElement(qi._l,{key:t.id,draggableId:t.id,index:n},(function(r,i){var s,l="custom-search custom-search-row row".concat(i.isDragging?" dragging":"");return a.createElement("fieldset",(0,c.Z)({key:n,ref:r.innerRef},r.dragHandleProps,r.draggableProps,{style:(s=r.draggableProps.style,i.isDragging,(0,x.Z)({userSelect:"none"},s)),className:l,"aria-label":(0,w.__)("Find and replace row","wp-migrate-db")}),a.createElement("div",{className:"handle cell"},a.createElement(ts,{"aria-hidden":"true"})),a.createElement("div",{className:"cell"},a.createElement("input",{type:"text",onChange:function(t){Es(e,n,"replace_old",t.target.value)},value:t.replace_old,placeholder:t.replace_old_placeholder,className:"consolas replace-old".concat(p&&0===n?" disabled":""," ").concat(!1===t.isValidRegex&&!0===t.regex?"invalid-expression":""),disabled:p&&0===n,"aria-label":(0,w.__)("Find","wp-migrate-db"),autoFocus:t.focus})),a.createElement("div",{className:"custom-search-arrow cell"},a.createElement("button",{onClick:function(){return function(e,t,n){e.updateCustomSearchReplace({key:t,option:"replace_new",value:n})}(e,n,t.replace_old)}},a.createElement(vi.r,{"aria-hidden":"true"}),a.createElement("span",{className:"screen-reader-text"},(0,w.__)("Copy find field to replace field","wp-migrate-db")))),a.createElement("div",{className:"cell"},a.createElement("input",{type:"text",onChange:function(t){Es(e,n,"replace_new",t.target.value)},value:t.replace_new,placeholder:t.replace_new_placeholder,className:"consolas","aria-label":(0,w.__)("Replace","wp-migrate-db")})),a.createElement("div",{className:"close cell"},a.createElement("button",{onClick:function(){!function(e,t){e.deleteItemCustomSearchReplace(t)}(e,n)}},a.createElement(as,{"aria-hidden":"true"}),a.createElement("span",{className:"screen-reader-text"},(0,w.__)("Remove row","wp-migrate-db")))),a.createElement("div",{className:"cell search-replace-row-options"},a.createElement(hs,{index:n,item:t})))}))})),d=Ki()(t,(function(e){return e.replace_old.length>0}));return a.createElement(It.Z,(0,c.Z)({title:e.title,panelName:e.panelName,isOpen:!0,selected:d,header:a.createElement(Ni,(0,c.Z)({count:t.length,panelsOpen:n,title:e.title,panelKey:"custom_fields"},e)),panelSummary:a.createElement(xi,(0,c.Z)({},e,{fields:t,type:e.panelName,searchKeys:{search:"replace_old",replace:"replace_new"},panelsOpen:e.panelsOpen})),bodyClass:"custom-search-replace search-replace free"},e),a.createElement(bi,{className:"custom-search"},a.createElement(qi.Z5,{onDragEnd:function(n){if(n.destination){var r=Yi(t,n.source.index,n.destination.index);e.reorderCustomSearchReplace(r)}}},a.createElement(qi.bK,{droppableId:"droppable"},(function(e,t){return a.createElement("fieldset",(0,c.Z)({ref:e.innerRef,style:(t.isDraggingOver,{}),"aria-label":(0,w.__)("Custom Find & Replace","wp-migrate-db")},e.droppableProps),m,e.placeholder)})))),(0,dn.a)(r,"COMMON_SEARCH_REPLACE_EMPTY")&&a.createElement(z.Z,{type:"danger"},(0,E.ZP)((0,w.gB)((0,w.__)('Please make sure all find & replace fields have values above. More information about these fields can be found in <a href="%s" target="_blank" rel="noopener roreferrer">our documentation.</a>',"wp-migrate-db"),"https://deliciousbrains.com/wp-migrate-db-pro/doc/find-and-replace/?utm_campaign=support%2Bdocs&utm_source={$tracking_base}&utm_medium=insideplugin&utm_content=new%2Burl%2Bvalidation"))),(0,dn.a)(r,"COMMON_SEARCH_REPLACE_INVALID_REGEX")&&a.createElement(z.Z,{type:"danger"},(0,E.ZP)((0,w.gB)((0,w.__)("Please make sure all 'Regular Expression' find & replace patterns are valid. <a href='%s'>Learn more about Regex</a>","wp-migrate-db"),"https://deliciousbrains.com/wp-migrate-db-pro/doc/find-and-replace/?utm_campaign=support%2Bdocs&utm_source=%7B$tracking_base%7D&utm_medium=insideplugin&utm_content=new%2Burl%2Bvalidation#regex-find-replace"))),a.createElement("p",null,a.createElement("button",{className:"btn btn-sm btn-stroke",onClick:function(t){return ws(e,t)},onKeyPress:function(t){return ws(e,t)}},a.createElement("div",{className:"flex-container"},a.createElement("div",{className:"btn-sm-icon icon-16"},a.createElement(Xi,{"aria-hidden":"true"})),(0,w.__)("Add Row","wp-migrate-db")))))})),xs=a.forwardRef((function(e,t){var n=e.panelsOpen,r=void 0===n?[]:n,i=e.registeredPanels,s=void 0===i?[]:i,c=e.onClick,l=r.some((function(e){return W.$z.includes(e)&&s.includes(e)}))?(0,w.__)("Collapse All","wp-migrate-db"):(0,w.__)("Expand All","wp-migrate-db");return a.createElement("button",{ref:t,className:"link",onClick:c,"aria-label":(0,w.gB)("%s %s",l,(0,w.__)("database panels.","wp-migrate-db"))},l)})),ks=v.ZP.div(vs||(vs=(0,h.Z)(["\n  display: grid;\n  grid-template-columns: repeat(2, 1fr);\n  align-items: start;\n  column-gap: 1.35rem;\n"])));var Zs=(0,i.$j)((function(e){var t,n=null!==(t=e.migrations.connection_info)&&void 0!==t?t:{},r=n.connecting,a=void 0!==r&&r,i=n.status,s=e.migrations,c=s.local_site,l=s.remote_site,o=e.migrations.current_migration.intent,u=e.profiles.current_profile;return{connecting:a,status:i,local_site:c,remote_site:l,intent:o,current_migration:e.migrations.current_migration,panels:e.panels,search_replace:e.migrations.search_replace,current_profile:u}}),{updateSelectedTables:K.E2,updatePanelTitle:W.H5,setCustomSearchReplace:hi.EX,setMigrationEnabled:K.I8})((function(e){var t=(0,i.I0)(),n=e.current_migration,r=n.intent,s=n.databaseEnabled,c=e.local_site,l=e.remote_site,o=void 0===l?{}:l,u=c.this_tables,p=c.this_table_sizes_hr,m=o.tables,d=o.table_sizes_hr,f=(0,_.qu)(r,c,o),g=f.disabled,b=f.uploads_dir,h=(0,ji.Lk)(r,{tables:u,tableSizes:p},{tables:m,tableSizes:d},"migration"),v=h.tables,E=h.tableSizes,y=(0,a.useRef)(),x=u,k=p;"push"===r&&(x=m,k=d);var Z=e.current_migration.tables_option,N=lt()(["savefile","backup_local"],r)&&g;if((0,a.useEffect)((function(){N&&e.setMigrationEnabled(!1),"import"!==r&&null===e.current_profile&&"all"===Z&&e.updateSelectedTables(v)}),[]),N)return a.createElement(Bi,{uploads_dir:b});var P=e.local_site.this_prefix,S=e.local_site.this_post_types,T=e.search_replace.standard_search_visible;if("pull"!==r&&"import"!==r||(P=e.remote_site.prefix,S=e.remote_site.post_types),e.connecting)return null;var O=Ri(e),A=["push","pull","savefile"].includes(r),C=s||!A,R=function(){t((0,G.m)(H.AV))};return a.createElement("div",null,a.createElement(It.Z,{title:e.panels.panelTitles.database,panelName:"database",enabled:s,hasInput:A,hideArrow:!0,panelOpen:s,panelSummary:C&&a.createElement(xs,{ref:y,onClick:function(){t((0,W.Xl)())},panelsOpen:e.panels.panelsOpen,registeredPanels:e.panels.registeredPanels}),toggle:function(){R()},callback:function(e){if("checkbox"!==e.target.type&&(!y.current||y.current!==e.target)){if(!s)return R();t((0,W.Xl)())}},className:A?"database_panel_has_controls":"database_panel_no_controls"},a.createElement(ks,null,a.createElement("div",null,"import"!==r&&a.createElement(Fi,{title:(0,w.__)("Tables","wp-migrate-db"),tables:v,tableSizes:E,prefix:P,intent:r,panelName:"tables",childPanel:!0}),a.createElement(fi,{title:(0,w.__)("Advanced Options","wp-migrate-db"),intent:r,panelName:"advanced_options",childPanel:!0})),a.createElement("div",null,!lt()(["savefile","backup_local"],r)&&a.createElement(Hi,{title:Ii[r].backup,tables:x,tableSizes:k,prefix:P,intent:r,panelName:"backups",childPanel:!0}),"import"!==r&&a.createElement(Li,{title:(0,w.__)("Post Types","wp-migrate-db"),postTypes:S,intent:r,panelName:"post_types",childPanel:!0}))),lt()(["push","pull","import","savefile"],r)&&T&&a.createElement(Ai,{title:(0,w.__)("Standard Find & Replace","wp-migrate-db"),panelName:"standard_fields",childPanel:!0}),"backup_local"!==r&&a.createElement(ys,{title:(0,w.__)("Custom Find & Replace","wp-migrate-db"),prefix:P,intent:r,panelName:"custom_fields",childPanel:!0}),O.map((function(e,t){return a.createElement(a.Fragment,{key:t},e)}))))})),Ns=n(26547),Ps=n(66441),Ss=n(39881),Ts=function(e){return function(t,n){var r=(0,J.NR)("local_site",n());return"import"===(0,J.r5)("intent",n())&&"undefined"!==typeof e.data.multisite&&"undefined"!==typeof e.data.subsite_export&&("false"===r.is_multisite&&"true"===e.data.multisite&&"true"!==e.data.subsite_export||"true"===r.is_multisite&&"false"===e.data.multisite)}},Os=function(e){return function(){var t=(0,b.Z)((0,g.Z)().mark((function t(n,r){var a;return(0,g.Z)().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(e.target.readyState===FileReader.DONE){t.next=2;break}return t.abrupt("return",!1);case 2:return t.prev=2,t.next=5,(0,_.op)("/get-import-info",{file_data:e.target.result});case 5:a=t.sent,t.next=12;break;case 8:throw t.prev=8,t.t0=t.catch(2),console.error(t.t0),new Error(t.t0);case 12:if(!n(Ts(a))){t.next=14;break}throw new Error("mst-error");case 14:if("undefined"===typeof a.wpmdb_error||1!==a.wpmdb_error){t.next=16;break}throw new Error(a.body);case 16:return t.abrupt("return",a);case 17:case"end":return t.stop()}}),t,null,[[2,8]])})));return function(e,n){return t.apply(this,arguments)}}()},As=function(){var e=(0,b.Z)((0,g.Z)().mark((function e(t,n){var r,a,i,s;return(0,g.Z)().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(r=new FileReader,a=(0,k.Z)(n,1),i=a[0],n.length){e.next=6;break}throw new Error(window.wpmdb_strings.please_select_sql_file);case 6:if(".sql"===i.name.slice(-4)||".sql.gz"===i.name.slice(-7)){e.next=8;break}throw new Error(window.wpmdb_strings.invalid_sql_file);case 8:return s=n[0].slice(0,1024e3),r.readAsDataURL(s),e.abrupt("return",r);case 11:case"end":return e.stop()}}),e)})));return function(t,n){return e.apply(this,arguments)}}(),Cs=function(e){return function(t,n){return t((0,G.m)(Ns.Ej,e))}},Rs=function(e){return function(t,n){return t((0,G.m)(Ns.EL,e))}},Is=function(e,t,n){return function(r,a){var s,c=(0,J.r5)("intent",a());r((s={upload_file:t,file_uploaded:!0,file_size:n},function(e,t){return e((0,G.m)(Ps.S_)),e((0,G.m)(Ns.ci,s))}));var l=!0;e.data.tables||(l=!1,r(Cs("NON_MDB_IMPORT_ERROR")),r((0,hi.EX)("import",!0))),(0,i.dC)((function(){r((0,G.m)(Ns.WH,e.data.tables)),r((0,hi.CN)(l)),r((0,K.E2)(e.data.tables||[])),r((0,Ss.G9)(e,!1,c))}));var o="PREFIX_MISMATCH";if((0,_.Ol)(e.data.prefix)&&e.data.tables)return r(Cs(o)),o}},Ds=function(e){return function(t,n){t((0,G.m)(Ns.Qy,e))}},Ls=function(e){var t=e.migrations.local_site.is_multisite,n=(0,w.gB)((0,w.__)('It looks like the file you are trying to import is from a multisite install and this install is a single site. To run this type of import you\'ll need to use the <a href="%1$s" target="_blank" rel="noopener noreferrer">Multisite tools addon</a> to export a subsite as a single site. <a href="%2$s" target="_blank">Learn more \xbb</a>',"wp-migrate-db"),"https://deliciousbrains.com/wp-migrate-db-pro/doc/multisite-tools-addon/","https://deliciousbrains.com/wp-migrate-db-pro/doc/multisite-tools-addon/#export-subsite");return"true"===t&&(n=(0,w.gB)((0,w.__)('It looks like the file you are trying to import is from a single site install and this install is a multisite. This type of migration isn\'t currently supported. <a href="%s" target="_blank" rel="noopener noreferrer">Learn more \xbb</a>',"wp-migrate-db"),"https://deliciousbrains.com/wp-migrate-db-pro/doc/multisite-tools-addon/")),a.createElement(z.Z,{type:"danger"},(0,E.ZP)(n))},Ms=n(67821),Fs=function(e){var t=e.panels.panelsOpen,n=e.panelName,r=e.fileUploaded,i=e.uploadFile;return t.includes(n)?null:a.createElement("div",{className:"panel-summary"},r?i:"")},Bs=(0,i.$j)((function(e){var t=(0,Ms.selectFromImportData)("status",e),n=(0,J.r5)("intent",e),r=(0,J.NR)("import_data",e),a="LOADING"===t,i=(0,Ms.selectFromImportData)("error",e),s=(0,J.FY)("prefix",e),c=e.migrations;return{intent:n,import_status:t,loading:a,import_error:i,remote_prefix:s,import_data:r,panels:e.panels,migrations:c}}),{updateOpenPanels:W.G7,updateMigrationPanels:W.rC,updatePanelTitle:W.H5,addAdditionalPanel:W.qb,removePanel:W.WJ,setFileUploadFilePath:function(e){return function(t,n){return t((0,G.m)(Ns.Dx,e))}},updateImportStatus:Cs,resetImportData:function(){return function(e,t){return e((0,G.m)(Ns.yQ))}},initialFileUpload:function(e,t,n){return function(){var n=(0,b.Z)((0,g.Z)().mark((function n(r,a){var i;return(0,g.Z)().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:return n.prev=0,n.next=3,As(t,e.current.files);case 3:n.sent.addEventListener("loadend",function(){var t=(0,b.Z)((0,g.Z)().mark((function t(n){var a,s,c,l,o;return(0,g.Z)().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return r(Cs("LOADING")),t.prev=1,t.next=4,r(Os(n));case 4:i=t.sent,t.next=13;break;case 7:return t.prev=7,t.t0=t.catch(1),a=t.t0.message,r("mst-error"===a?Cs("MST_WARNING"):Rs({error:!0,message:t.t0.message})),r((0,W.WJ)("database")),t.abrupt("return",!1);case 13:return r(Cs("")),s=e.current.files[0].name,c=e.current.files[0].size,r(Ds(e.current.files[0])),l=r(Is(i,s,c)),r((0,K.I8)(!0)),o=["database","custom_fields","standard_fields"],i.data.tables&&"PREFIX_MISMATCH"!==l||o.push("import"),r((0,W.G7)(o)),t.abrupt("return",r((0,W.qb)("database","import")));case 23:case"end":return t.stop()}}),t,null,[[1,7]])})));return function(e){return t.apply(this,arguments)}}()),n.next=12;break;case 7:n.prev=7,n.t0=n.catch(0),r(Rs({error:!0,message:n.t0.message})),r((0,W.WJ)("database")),r((0,K.I8)(!1));case 12:case"end":return n.stop()}}),n,null,[[0,7]])})));return function(e,t){return n.apply(this,arguments)}}()}})((function(e){var t=e.import_status,n=e.loading,r=e.import_error,i=e.remote_prefix,s=e.import_data,l=e.panels.panelsOpen,o=(0,a.useState)((0,w.__)("No file selected","wp-migrate-db")),u=(0,k.Z)(o,2),p=u[0],m=u[1],d=(0,a.useRef)(),f=function(){return a.createElement(a.Fragment,null,(0,E.ZP)((0,w.__)("<strong>Unrecognized File</strong> &mdash; This file does not appear to have been generated by WP Migrate. Importing files generated by phpMyAdmin, WP-CLI, etc. should work, but we recommend using WP Migrate's Export feature.","wp-migrate-db"))," ",a.createElement("a",{href:"https://deliciousbrains.com/wp-migrate-db-pro/doc/why-use-our-export/",target:"blank"},(0,w.__)("Why?","wp-migrate-db")))};if((0,_.ME)())return null;var g=!1,b=["import-panel"];return l.includes("import")||(g=!0,b.push("has-divider")),a.createElement(It.Z,(0,c.Z)({title:e.panels.panelTitles.import,panelName:"import",panelSummary:a.createElement(Fs,(0,c.Z)({},e,{fileUploaded:s.file_uploaded,uploadFile:s.upload_file,panelName:"import"})),forceDivider:g,bodyClass:"import-panel",className:b.join(" ")},e),a.createElement("div",{className:"flex-container import-message"},a.createElement("input",{type:"file",ref:d,id:"import-file",onChange:function(t){e.resetImportData(),d.current.files[0]&&m(d.current.files[0].name),e.initialFileUpload(d,t,e.baseTitle)},className:"input-button"}),a.createElement("label",{htmlFor:"import-file",className:"btn btn-stroke btn-sm"},"choose file"),a.createElement("span",{className:"muted"},p)),n&&a.createElement("h3",null,"Loading file..."),r.error&&a.createElement(z.Z,{type:"danger"},r.message),"PREFIX_MISMATCH"===t&&a.createElement(z.Z,{type:"danger"},a.createElement("h4",{style:{color:Nn.qP}},(0,w.__)("Warning: Different Table Prefixes","wp-migrate-db")),(0,w.gB)((0,w.__)('Whoa! We\'ve detected that the database table prefix in the import file does not match the database prefix of this install. Clicking the Import button below will create new database tables with the prefix "%s"',"wp-migrate-db"),i)),"NON_MDB_IMPORT_ERROR"===t&&a.createElement(z.Z,{type:"danger"},a.createElement(f,null)),"MST_WARNING"===t&&a.createElement(Ls,e))})),js=n(34363),Us=function(e){var t=e.e,n=e.field,r=e.dispatch,a=e.authState,i=e.connectionState,s=(0,x.Z)({},a);s[n]=t.target.value,r((0,js.setConnectionStatus)("auth_form",s));var c=(0,x.Z)({},i);if(c.value){var l=function(e,t){var n=(0,x.Z)({},e),r=t.username,a=t.password,i=e.url,s=new URL(i),c=s.host+s.pathname;return n.url="".concat(s.protocol,"//").concat(r,":").concat(a,"@").concat(c),0===r.length&&0===a.length&&(n.url="".concat(s.protocol,"//").concat(c)),n.value=n.url+"\n"+n.key,n}(c,{username:encodeURIComponent(s.username),password:encodeURIComponent(s.password)});r((0,js.updateConnectionState)(l))}},zs=function(e){var t=(0,i.I0)(),n=(0,i.v9)((function(e){return e.migrations.current_migration.connected})),r=(0,i.v9)((function(e){return e.migrations.connection_info.status})).auth_form,s=(0,i.v9)((function(e){return e.migrations.connection_info})).connection_state;return n?null:a.createElement("div",{className:"grid-container auth-form"},a.createElement("input",{type:"text",placeholder:(0,w.__)("Username"),onChange:function(e){return Us({e:e,field:"username",dispatch:t,authState:r,connectionState:s})},value:r.username,key:"username"}),a.createElement("input",{type:"text",placeholder:(0,w.__)("Password"),onChange:function(e){return Us({e:e,field:"password",dispatch:t,authState:r,connectionState:s})},value:r.password,key:"password"}))},Gs=function(e){return a.createElement(z.Z,{type:"warning"},a.createElement("strong",null,(0,w.__)("HTTPS Disabled","wp-migrate-db"))," \u2014"," ",(0,w.__)("We couldn't connect over HTTPS but regular HTTP appears to be working so we've switched to that. If you run a push or pull, your data will be transmitted unencrypted. Most people are fine with this, but just a heads up.","wp-migrate-db"))},Vs=n(53273),Hs=n(91490),Ws=function(e){var t=e.shortName,n=e.success,r=(0,a.useState)(!1),s=(0,k.Z)(r,2),c=s[0],l=s[1],o=(0,i.v9)((function(e){return e.theme_plugin_files})),u=null;return o&&(u=o.remotePluginUpdated),(0,a.useEffect)((function(){t===Vs.LD?l(u):l(n),setTimeout((function(){l(!1)}),1500)}),[u,n]),a.createElement(Hs.Z,{in:c,timeout:500,classNames:"settings-node",id:la()(),unmountOnExit:!0},a.createElement("div",null,(0,w.__)("Updated Successfully","wp-migrate-db"),a.createElement("div",{className:"blue-check"},a.createElement(ot.en,null))))},Ks="SET_UPDATE_ERROR",qs="SET_UPDATING",Ys="SET_UPDATE_SUCCESS",Js="SET_WILL_ENABLE_BETA_UPDATES",Qs="SET_PLUGIN_CHECK_AGAIN_TEXT",Xs=function(e,t){switch(t.type){case Ks:return(0,x.Z)((0,x.Z)({},e),{},{updateError:t.payload});case qs:return(0,x.Z)((0,x.Z)({},e),{},{updating:t.payload});case Ys:return(0,x.Z)((0,x.Z)({},e),{},{updateSuccess:t.payload});case Js:return(0,x.Z)((0,x.Z)({},e),{},{willEnableBetaUpdates:t.payload});case Qs:return(0,x.Z)((0,x.Z)({},e),{},{checkAgainText:t.payload});default:return e}},$s=(0,i.$j)((function(e){return{remote_site:e.migrations.remote_site,intent:e.migrations.current_migration.intent,theme_plugin_files:e.theme_plugin_files}}))((function(e){var t=(0,a.useReducer)(Xs,{updateError:"",checkAgainText:"",updating:!1,updateSuccess:!1,willEnableBetaUpdates:!1}),n=(0,k.Z)(t,2),r=n[0],s=n[1],c=r.updateError,l=r.updating,o=r.updateSuccess,u=r.checkAgainText,p=r.willEnableBetaUpdates,m=e.intent,d=e.remote_site,f=e.shortName,h=e.pluginSlug,v=e.remoteUpgradable,y=e.theme_plugin_files,x=null;y&&(x=y.remotePluginUpdated);var Z=d.beta_optin,N=(0,i.I0)(),P=h?(0,w.__)("<b>Could not update addon on remote site &mdash;</b> %s","wp-migrate-db"):(0,w.__)("<b>Could not update plugin on remote site &mdash;</b> %s","wp-migrate-db");(0,a.useEffect)((function(){s((0,G.m)(Ks,e.message)),!ba()(d)&&e.version.includes("b")&&!Z&&v&&(s((0,G.m)(Js,!0)),s((0,G.m)(Qs,(0,w.gB)((0,w.__)("Enable beta updates on remote and upgrade to version %s","wp-migrate-db"),e.version))))}),[e]);var S=function(){var e=(0,b.Z)((0,g.Z)().mark((function e(){var t,n;return(0,g.Z)().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return s((0,G.m)(qs,!0)),e.next=3,N((0,js.updatePluginOnRemote)(h));case 3:if(t=e.sent,s((0,G.m)(qs,!1)),t.success){e.next=11;break}return n=(0,mt.Y)(t),s((0,G.m)(Ks,(0,w.gB)(P,n))),s((0,G.m)(Qs,(0,w.__)("Try again","wp-migrate-db"))),s((0,G.m)(Ys,!1)),e.abrupt("return");case 11:s((0,G.m)(Ys,!0)),setTimeout((function(){N((0,js.setConnectionStatus)("update_plugin_on_remote",!1));var e=(0,_.OK)();if(!1!==e){var t=(0,k.Z)(e,2),n=t[0],r=t[1];N(te({path:"unsaved"===n?"unsaved":"saved",params:{id:r}}))}else N((0,js.connectToRemote)(m))}),0);case 13:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}(),T=function(e){var t=e.initialError,n=e.updateError;if(!v||o||!n)return null;var r=n!==t||p?u:(0,w.__)("Update plugin on remote","wp-migrate-db");return a.createElement("button",{onClick:S,className:"link"},r)},O=o||x&&Vs.LD===f,A=a.createElement("div",null,!O&&a.createElement(a.Fragment,null,(0,E.ZP)(c)," ",h?"":" ",a.createElement(T,{updateError:c,initialError:e.message,remoteUpgradable:v}),l&&a.createElement(ot.Q,{className:"remote-update-spinner"})),a.createElement(Ws,{shortName:f,success:o}));return ba()(h)&&v?a.createElement(z.Z,{type:"danger"},A):a.createElement("div",{className:"full flex-container version-mismatch-error"},A)})),ec=function(e){var t=e.message,n=e.intent,r=(0,a.useState)(t),s=(0,k.Z)(r,2),c=s[0],l=s[1],o=(0,a.useState)(!1),u=(0,k.Z)(o,2),p=u[0],m=u[1],d=(0,i.I0)(),f=function(){var e=(0,b.Z)((0,g.Z)().mark((function e(){var t,r;return(0,g.Z)().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return m(!0),e.next=3,d((0,js.copyLicenseToRemote)());case 3:if((t=e.sent).success){e.next=9;break}return r=(0,mt.Y)(t),l("".concat((0,w.__)("Could not copy license to remote site &mdash;","wp-migrate-db")," ").concat(r)),m(!1),e.abrupt("return",null);case 9:m(!1),d((0,js.setConnectionStatus)("copy_to_remote",!1)),d((0,js.connectToRemote)(n));case 12:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}(),_=function(e){var t=e.initialError,n=e.copyError,r=n!==t&&n?(0,w.__)("Try again.","wp-migrate-db"):(0,w.__)("Copy license to remote.","wp-migrate-db");return a.createElement("button",{onClick:f,className:"link"},r)};return a.createElement(a.Fragment,null,a.createElement(z.Z,{type:"danger"},(0,E.ZP)(c||t),"\xa0",a.createElement(_,{initialError:t,copyError:c}),p&&a.createElement("div",{className:"relative"},a.createElement(ot.Q,{className:"svg-spinner"}))))},tc=function(e){var t=e.connection_info.status,n=t.auth_form,r=t.show_auth_form,i=t.error,s=t.error_msg,l=t.ssl_notice,o=t.update_plugin_on_remote,u=t.copy_to_remote,p=t.connecting,m=e.connection_info.connection_state,d=e.current_migration,f=d.connected,_=d.intent,g=[];return l&&(f||i)&&g.push(a.createElement(Gs,null)),r&&g.push(a.createElement(zs,{authState:n,connectionState:m,updateConnectionState:e.updateConnectionState})),p||(u&&g.push(a.createElement(ec,{message:s,intent:_})),!i||f||u||(o?g.push(a.createElement($s,(0,c.Z)({message:s},e,{intent:_,remoteUpgradable:"true"}))):""!==s&&g.push(a.createElement(z.Z,{type:"danger"},(0,E.ZP)(s))))),g};var nc,rc=function(){var e=(0,i.I0)(),t=(0,i.v9)((function(e){return e})),n=e((0,Si.O)("wpmdb_post_connection_errors",{warning:[],info:[],danger:[]},t));return a.createElement(a.Fragment,null,Object.keys(n).filter((function(e){return n[e].length>0})).map((function(e,t){return a.createElement(z.Z,{key:t,type:e},a.createElement("ul",null,n[e].map((function(e){return a.createElement("li",{key:t},(0,E.ZP)(e))}))))})))},ac=(0,v.ZP)(It.Z)(nc||(nc=(0,h.Z)(["\n  h2 {\n    margin: 15px 0;\n  }\n\n  textarea {\n    margin-bottom: 10px;\n  }\n\n  button.btn-sm {\n    margin-top: 10px;\n  }\n\n  &.disabled {\n    opacity: 0.6;\n  }\n"])));var ic=function(e){var t=e.panelsOpen,n=e.connected,r=e.connection_info.connection_state.url;return t.includes(e.panelName)?null:a.createElement("div",{className:"panel-summary"},n?r:"")},sc=(0,i.$j)((function(e){var t=(0,J.NR)("current_migration",e),n=e.panels.panelsOpen,r=(0,J.NR)("local_site",e),a=(0,J.NR)("connection_info",e);return{current_migration:t,status:a.status,panels:e.panels,connection_info:a,panelsOpen:n,local_site:r}}))((function(e){var t=(0,i.I0)(),n=(0,_.ME)(),r=!1,s=["connect-panel"];e.panels.panelsOpen.includes("connect")||(r=!0,s.push("has-divider"));var l,o,u=(l=J.NR,o="current_migration",(0,i.v9)((function(e){return l(o,e)}))),p=u.intent,m=u.connected;(0,a.useEffect)((function(){m||t((0,Si.ku)("wpmdb_post_connection_errors"))}),[m]);var d=e.connection_info.connection_state,f=e.connection_info.status,g=f.connecting,b=f.button_status,h=(0,a.useRef)(),v=(0,i.v9)((function(e){return e.api.abort_controller}));(0,a.useEffect)((function(){return function(){v.hasOwnProperty("connect_to_remote")&&v.connect_to_remote.abort()}}),[]);var E=tc(e),y=function(e){var t="",n=e.connection_info.status,r=n.connecting,a=n.button_status;return(r||"disabled"===a)&&(t=" btn-disabled"),t}(e);return a.createElement(a.Fragment,null,a.createElement(ac,(0,c.Z)({title:e.panels.panelTitles.connect,panelName:"connect",panelSummary:a.createElement(ic,(0,c.Z)({panelName:"connect"},e,{connected:m})),bodyClass:"connect-panel",className:s.join(" "),forceDivider:r},e),a.createElement("div",{className:"connect-to-remote"},a.createElement("textarea",{placeholder:(0,w.__)("Connection Info - Site URL & Secret Key","wp-migrate-db"),onChange:function(e){return function(e){if(g)return!1;t((0,js.changeConnection)(e.target.value))}(e)},onPaste:function(e){t((0,js.setConnectionStatus)("pasted",!0))},onFocus:function(e){return function(){if(!m)return null;var e=void 0;if(m&&"hidden"===b&&(e=window.confirm((0,w.__)("If you change the connection details, you will lose any replaces and table selections you have made below. Do you wish to continue?","wp-migrate-db"))),e)return t((0,js.setConnectionStatusBatch)({button_status:"",show_auth_form:!1,error_msg:""})),t((0,js.resetMigration)(["connect"])),!1;!1===e&&h.current.blur()}()},value:d.value||"",className:g?"disabled consolas":"consolas",ref:h}),E.map((function(e,t){return a.createElement(a.Fragment,{key:t},e)})),g&&a.createElement("p",{className:"full connecting-message"},a.createElement("span",null,"Establishing connection to remote server, please wait..."),a.createElement(ot.Q,null))),"hidden"!==b&&!n&&a.createElement("div",null,a.createElement("button",{onClick:function(){return function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];if(g||""!==b||n)return!1;t((0,js.connectToRemote)(p,e))}()},className:"btn btn-sm".concat(y)},(0,w.__)("Connect","wp-migrate-db")))),a.createElement(rc,null))})),cc=n(20188),lc=n(82886),oc=(n(74352),n(31998)),uc=n(666),pc=n(9106),mc=n(19826),dc=n(26429),fc={},_c=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:fc;return(0,dc.ZP)(e,(function(t){return e}))},gc=(0,tr.Lq)({abort_controller:{},AJAX_RUNNING:!1,AJAX_SUCCESS:!1,AJAX_ERROR:!1},{SET_ABORT_CONTROLLER:function(e,t){return e.abort_controller[t.payload.key]=t.payload.controller,e}}),bc=n(59299),hc=n(18066),vc={mdb_rest_active:!0},Ec=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:vc,t=arguments.length>1?arguments[1]:void 0;return(0,dc.ZP)(e,(function(n){return"MDB_REST_NOT_ACTIVE"===t.type?{mdb_rest_active:!1}:e}))},wc=n(15265),yc={backups:or,profiles:V.ZP,migrations:oc.ZP,panels:kn.ZP,notifications:uc.ZP,addons:pc.Z,settings:mc.ZP,global_status:_c,api:gc,dbi_api_data:bc.ZP,mdb_filters:hc.ZP,mdb_rest:Ec,dry_run:wc.xN};function xc(e,t){if("RESET_APP"===e.type){var n=t;t={profiles:n.profiles,settings:n.settings,global_status:n.global_status,notifications:n.notifications,dbi_api_data:n.dbi_api_data,mdb_filters:n.mdb_filters,mdb_rest:n.mdb_rest}}return t}function kc(e){return(0,cc.UY)((0,x.Z)((0,x.Z)({},yc),e))}var Zc=kc(void 0),Nc=function(e,t){return e=xc(t,e),Zc(e,t)},Pc=[lc.Z];var Sc=("object"===typeof window&&window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__,cc.qC)(cc.md.apply(void 0,Pc)),Tc=(0,cc.MT)(Nc,{},Sc);window.WPMDBStore=Tc,Tc.asyncReducers={};var Oc=Tc,Ac=n(64533),Cc=n(51645),Rc=n(65730);function Ic(e,t,n){e.asyncReducers[t]=n;var r=kc(e.asyncReducers);e.replaceReducer((function(e,t){return e=xc(t,e),r(e,t)}))}var Dc={local_site:window.wpmdb_data,remote_site:{}},Lc=(0,cc.UY)({local_site:function(){return arguments.length>0&&void 0!==arguments[0]?arguments[0]:Dc.local_site},remote_site:{},current_migration:H.S_,migration_progress:_e.D$,search_replace:Ps.jC,dry_run:wc.xN}),Mc={profiles:V.ZP,migrations:Lc,panels:kn.ZP,notifications:uc.ZP,settings:mc.ZP,global_status:_c,mdb_filters:hc.ZP,mdb_rest:Ec,dry_run:wc.xN};var Fc,Bc=(0,cc.UY)((0,x.Z)((0,x.Z)({},Mc),Fc)),jc=function(e,t){return e=function(e,t){if("RESET_APP"===e.type){var n=t;t={profiles:n.profiles,settings:n.settings,global_status:n.global_status,notifications:n.notifications,mdb_filters:n.mdb_filters,mdb_rest:n.mdb_rest,dry_run:n.dry_run}}return t}(t,e),Bc(e,t)},Uc=[lc.Z];var zc=("object"===typeof window&&window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__,cc.qC)(cc.md.apply(void 0,Uc)),Gc=(0,cc.MT)(jc,{},zc);window.WPMDBStore=Gc,Gc.asyncReducers={};var Vc=Gc,Hc=function(){var e,t,r,i=(0,_.Yu)()?Oc:Vc;return e=a.lazy((function(){return Promise.all([n.e(532),n.e(358),n.e(135),n.e(406)]).then(n.bind(n,36890))})),Ic(i,"media_files",Ac.ZP),t=a.lazy((function(){return Promise.all([n.e(532),n.e(135),n.e(537)]).then(n.bind(n,2537))})),Ic(i,"theme_plugin_files",Cc.ZP),r=a.lazy((function(){return Promise.all([n.e(532),n.e(605)]).then(n.bind(n,79605))})),Ic(i,"multisite_tools",Rc.ZP),{MediaFiles:e,ThemePluginFiles:t,MultisiteTools:r}}(),Wc=Hc.MediaFiles,Kc=Hc.ThemePluginFiles,qc=Hc.MultisiteTools,Yc=function(e){var t=(0,i.I0)(),n=(0,i.v9)((function(e){return e.panels})).panelsToDisplay,r=(0,i.v9)((function(e){return e})),s=r.media_files,c=r.theme_plugin_files,l=r.multisite_tools,o=(0,i.v9)((function(e){return e.migrations.current_migration})).intent,u=t((0,ji.C)()),p=function(e,t){return!(!e||!t)&&!(["push","pull"].includes(o)&&!t.is_licensed)};return a.createElement(a.Fragment,null,n.map((function(t,n){switch(t){case"connect":return a.createElement(sc,{key:n});case"multisite_tools":return qc&&"backup_local"!==o&&(!["push","pull"].includes(o)||u)&&l.available&&l.is_licensed?a.createElement(a.Suspense,{fallback:a.createElement("div",null,"Loading..."),key:n},a.createElement(qc,e)):null;case"database":return a.createElement(Zs,{key:n});case"import":return a.createElement(Bs,{key:n,baseTitle:(0,w.__)("SQL File","wp-migrate-db")});case"media_files":return p(Wc,s)?a.createElement(a.Suspense,{fallback:a.createElement("div",null,"Loading..."),key:n},a.createElement(Wc,e)):null;case"theme_plugin_files":return p(Kc,c)?a.createElement(a.Suspense,{fallback:a.createElement("div",null,"Loading..."),key:n},a.createElement(Kc,e)):null;default:return null}})))},Jc=function(e){var t=(0,a.useRef)(e);(0,a.useEffect)((function(){t.current=e}),[e]),(0,a.useEffect)((function(){var e=function(){for(var e,n=arguments.length,r=new Array(n),a=0;a<n;a++)r[a]=arguments[a];return null===(e=t.current)||void 0===e?void 0:e.call.apply(e,[t].concat(r))};return window.addEventListener("beforeunload",e),function(){return window.removeEventListener("beforeunload",e)}}),[])},Qc=function(){var e=(0,i.v9)((function(e){return e.migrations.migration_progress}));Jc((function(t){!1===e.allow_page_leave&&(t.preventDefault(),t.returnValue="")}))},Xc=function(e){var t=e.version,n=e.title,r=e.content,i=e.postLink,s=e.imgSrc,c=e.imgAlt;return a.createElement("article",{className:"release-post"},a.createElement("h2",null,a.createElement("span",{className:"release-version"},"v",t," "),n),a.createElement("img",{src:s,alt:c}),a.createElement("div",{className:"release-content"},(0,E.ZP)(r)),a.createElement(Je,{link:i,content:(0,w.__)("View Full Release Notes","wp-migrate-db"),utmContent:"view-full-release-notes",utmCampaign:"wp-migrate-whats-new",screenReaderText:(0,w.gB)((0,w.__)("about %s","wp-migrate-db"),n),hasArrow:!0}))},$c=(n(63662),n.p+"static/media/wp-migrate-2-6-0.8d26599e.png"),el=n(90534),tl=n.n(el),nl=function(){var e=[{version:"2.6.0",title:"Full-Site Exports & Import to Local",imgSrc:(0,_.Nh)($c),imgAlt:"Drop to import WP Migrate export to Local",content:"<p>In addition to exporting the database, WP Migrate can now bundle media, themes, plugins, and other files necessary to fully replicate your site in a new environment. Plus a seamless integration with Local\u2014the #1 local WordPress development tool\u2014means the resulting ZIP archive can be imported into a new local development environment in minutes.</p>",postLink:"https://deliciousbrains.com/wp-migrate-2-6-released/"}],t=function(e){return e.releasePosts.map((function(e){return a.createElement(Xc,e)}))},n={backgroundImage:"url(".concat(tl(),")")};return a.createElement("div",{className:"whats-new"},a.createElement("div",{className:"release-posts"},a.createElement(t,{releasePosts:e}),a.createElement("div",{className:"more-releases",style:n},a.createElement("h2",null,"We've got more releases!"),a.createElement(Je,{link:"https://deliciousbrains.com/wp-migrate-db-pro/whats-new/",content:(0,w.__)("Check them out","wp-migrate-db"),utmContent:"more-releases",utmCampaign:"wp-migrate-whats-new",hasArrow:!0}))))},rl=function(e){var t=(0,i.I0)(),r=(0,_.Nh)(f),s={backgroundImage:"url(".concat(r,")")};return(0,a.useEffect)((function(){t(function(){var e=(0,b.Z)((0,g.Z)().mark((function e(t,r){return(0,g.Z)().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:window.hasOwnProperty("wpmdbmf")&&Promise.resolve().then(n.bind(n,33686)).then((function(e){t(e.default())})),window.hasOwnProperty("wpmdbtp")&&Promise.resolve().then(n.bind(n,18213)).then((function(e){t(e.default())})),window.hasOwnProperty("wpmdbmst")&&Promise.resolve().then(n.bind(n,85562)).then((function(e){t(e.default())}));case 3:case"end":return e.stop()}}),e)})));return function(t,n){return e.apply(this,arguments)}}())}),[]),Qc(),a.createElement(l.UT,{hashType:"noslash"},a.createElement(Ha,{className:"wpmdb"},a.createElement("div",{className:"header-wrap"},a.createElement("div",{className:"wrapper"},a.createElement("h2",{style:{display:"none"}}),a.createElement("div",{id:"nag-container"}),a.createElement("div",{className:"header mdb-header bg-brand-light flex-container",style:s},a.createElement(d,{className:"medallion"}),a.createElement("h1",null,"WP Migrate"),a.createElement(Va,null)))),a.createElement(ri,{isPro:!0},a.createElement(o.AW,{path:"/:tab(wpbody-content)?",exact:!0,render:function(e){return a.createElement(se,e)}}),a.createElement(o.AW,{path:["/migrate/:id","/migrate","/unsaved/:id"],render:function(e){return a.createElement(Qn,(0,c.Z)({},e,{ChildPanels:a.createElement(Yc,e)}))}}),a.createElement(o.AW,{path:"/backups",component:mr}),a.createElement(o.AW,{path:"/settings",render:function(e){return a.createElement(Ia,e)}}),a.createElement(o.AW,{path:"/addons",component:Fr}),a.createElement(o.AW,{path:"/help",component:da}),a.createElement(o.AW,{path:"/whats-new",component:nl}))))};window.wpmdb_abort_controller=new AbortController;var al=document.getElementById("root");(0,s.s)(al).render(a.createElement(i.zt,{store:Oc},a.createElement(rl,{isPro:!0})))},33686:function(e,t,n){"use strict";n.r(t);var r=n(27166),a=n(33032),i=n(62295),s=n(22497),c=n(78579),l=n(4516),o=n(66055),u=n(64533);t.default=function(){return function(e,t){e((function(e,t){e((0,s.KG)("afterFinalizeMigration",(function(){e((0,o.m)(u.F1,Date.now()))})))})),e((function(e,t){(0,i.dC)((function(){e((0,s.KJ)("wpmdbPreMigrationCheck",(function(t){return e((0,c.xO)(t))}))),e((0,s.KJ)("addMigrationStages",(function(t){return e((0,c.h2)(t))}))),e((0,s.KJ)("mdbAddonActions",function(){var n=(0,a.Z)((0,r.Z)().mark((function n(a){return(0,r.Z)().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:if(!t().media_files.enabled){n.next=5;break}return n.next=4,e((0,c.B0)());case 4:return n.abrupt("return",n.sent);case 5:return n.abrupt("return",a);case 6:case"end":return n.stop()}}),n)})));return function(e){return n.apply(this,arguments)}}())),e((0,s.KJ)("wpmdbFinalizeMigration",(function(e){if(!t().media_files.enabled)return e;var n=t().profiles.current_profile,r=(0,l.r5)("profile_type",t());return e.profileID=n,e.profileType=r,e})))}))}))}}},78579:function(e,t,n){"use strict";n.d(t,{$0:function(){return h},AN:function(){return w},B0:function(){return Z},K4:function(){return x},Lm:function(){return v},Um:function(){return E},h2:function(){return k},nv:function(){return b},rM:function(){return y},xO:function(){return T}});var r=n(31125),a=n(27166),i=n(33032),s=n(42233),c=n(64533),l=n(66055),o=n(4516),u=n(3460),p=n(66866),m=n(42714),d=n(10304),f=n(29816),_=n(98135),g=n(73264),b=((0,s.__)("Media Files addon","wp-migrate-db"),function(){return(0,s.__)("<b>Addon Missing</b> - The Media Files addon is inactive on the <strong>remote site</strong>. Please install and activate it to enable media file migration.","wp-migrate-db")}),h=function(){return function(e,t){return e((0,l.m)(c.h6))}},v=function(e){return(0,l.m)(c.U4,e)},E=function(e,t){return function(n){n({type:c.vL,payload:{available:e,message:t}})}},w=function(e){return function(t){t({type:c.F_,payload:e})}},y=function(e,t){return function(t){return t((0,l.m)(c.bv,e))}},x=function(e){return function(t){return t((0,l.m)(c.Xx,e))}};function k(e){return function(t,n){return n().media_files.enabled&&e.push("media_files"),e}}var Z=function(){return function(){var e=(0,i.Z)((0,a.Z)().mark((function e(t,n){var r,i;return(0,a.Z)().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(r=n().media_files,i=(0,o.r5)("intent",n()),["push","pull","savefile"].includes(i)&&r.enabled){e.next=4;break}return e.abrupt("return",!1);case 4:return e.next=6,t((0,u.Z6)("ADDONS_STAGE",[{fn:P,args:["media_files"]}]));case 6:return e.abrupt("return",e.sent);case 7:case"end":return e.stop()}}),e)})));return function(t,n){return e.apply(this,arguments)}}()},N=function(){return function(e,t){var n=(0,o.r5)("intent",t()),r=(0,o._P)("this_wp_upload_dir",t()),a=(0,o.FY)("wp_upload_dir",t());return["push","savefile"].includes(n)?r:a}},P=function e(t){var n=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return function(){var r=(0,i.Z)((0,a.Z)().mark((function r(i,s){var c,l,p,m,f,_,b,h,v,E;return(0,a.Z)().wrap((function(r){for(;;)switch(r.prev=r.next){case 0:return n||i((0,g.F)(t)),c=(0,o.r5)("migration_id",s()),l=(0,o.r5)("stages",s()),p=s().media_files,m=i(N()),f=p.excludes,_=p.option,b=p.date,h=p.last_migration,v={stage:"media_files",stages:l,folder:m,migration_state_id:c,excludes:JSON.stringify(f),is_cli_migration:0},"new"===_&&(v.date=b,v.timezone=Intl.DateTimeFormat().resolvedOptions().timeZone),"new_subsequent"===_&&""!==h&&(v.date=new Date(h).toISOString(),v.timezone=Intl.DateTimeFormat().resolvedOptions().timeZone),r.next=11,i((0,d.A)("/mf-initiate-file-migration",v));case 11:if(E=r.sent){r.next=14;break}return r.abrupt("return",!1);case 14:if(!E.data.recursive_queue){r.next=18;break}return r.next=17,i((0,g.T)(t,E,e));case 17:return r.abrupt("return",r.sent);case 18:return r.next=20,i((0,u.Z6)("ADDONS_STAGE",[{fn:S,args:[t,E]}]));case 20:case"end":return r.stop()}}),r)})));return function(e,t){return r.apply(this,arguments)}}()},S=function e(t,n){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0;return function(){var n=(0,i.Z)((0,a.Z)().mark((function n(i,c){var g,b,h,v,E;return(0,a.Z)().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:return i((0,l.m)(p.V$,(0,s.__)("Loading queue...","wp-migrate-db"))),g=(0,o.r5)("migration_id",c()),b={stage:"media_files",offset:r,migration_state_id:g},n.next=5,i((0,d.A)("/mf-get-queue-items",b));case 5:if(h=n.sent){n.next=8;break}return n.abrupt("return");case 8:if("complete"!==(v=h.data).status){n.next=14;break}return E={currentStage:t,stage:"media_files",migration_state_id:g,folder:i(N())},n.next=13,i((0,u.Z6)("ADDONS_STAGE",[{fn:f.e5,args:[t,"/mf-transfer-files",E]}]));case 13:return n.abrupt("return");case 14:if(v.hasOwnProperty("queue_status")){n.next=17;break}return i((0,_.Qc)()),n.abrupt("return",!1);case 17:return i((0,m.I6)(v.queue_status.size/1e3)),n.next=20,i((0,u.Z6)("ADDONS_STAGE",[{fn:e,args:[t,h]}]));case 20:case"end":return n.stop()}}),n)})));return function(e,t){return n.apply(this,arguments)}}()};function T(e){return function(t,n){var a=n().media_files,i=a.date,s=a.enabled,c=(0,o.r5)("intent",n());if(!s||!["push","pull"].includes(c))return e;var l=(0,r.Z)(e),u=new Date(Date.now());if(new Date(i)>u){l.push({name:"MF_INVALID_DATE",panel:"media_files"})}return"undefined"!==typeof a.option&&null!==a.option||l.push({name:"MF_OPTION_NULL",panel:"media_files"}),l}}},64533:function(e,t,n){"use strict";n.d(t,{F1:function(){return f},F_:function(){return p},U4:function(){return o},Xx:function(){return d},bv:function(){return m},h6:function(){return l},vL:function(){return u}});var r=n(18489),a=n(26429),i=n(29950),s=n(9106),c=n(31998),l="TOGGLE_MEDIA_FILES",o="SET_MEDIA_FILES_OPTION",u="SET_MF_AVAILABLE",p="SET_MF_LICENSED",m="UPDATE_MF_EXCLUDES",d="UPDATE_MF_DATE",f="UPDATE_MF_LAST_MIGRATION",_={enabled:!1,option:"all",available:!1,is_licensed:!1,message:"",excludes:".DS_Store\n*.log\n*backup*/\n*cache*/",last_migration:"",date:new Date(Date.now()).toISOString()};t.ZP=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:_,t=arguments.length>1?arguments[1]:void 0;return(0,a.ZP)(e,(function(n){switch(t.type){case"RESET_APP":return n.date=new Date(Date.now()).toISOString(),n;case s.$:return(0,r.Z)((0,r.Z)({},n),{},{available:1===window.wpmdb_data.mf_available,message:""});case l:return n.enabled=!n.enabled,n;case p:return n.is_licensed=t.payload,n;case o:return n.option=t.payload,n;case i.Ld:var a=t.payload.profile.value;return a.hasOwnProperty("media_files")&&(n.enabled=a.media_files.enabled,n.option=a.media_files.option,n.date=a.media_files.date,n.excludes=a.media_files.excludes,n.last_migration=a.media_files.last_migration),a.current_migration.hasOwnProperty("media_migration_option")&&!0===t.payload.profile.imported&&(n.option=null,n.enabled=!0),n;case u:var _=t.payload.available;return n.available=_,t.payload.hasOwnProperty("message")&&(n.message=t.payload.message),_||(n.enabled=!1),n;case m:return(0,r.Z)((0,r.Z)({},n),{},{excludes:t.payload});case d:return(0,r.Z)((0,r.Z)({},n),{},{date:t.payload});case f:return(0,r.Z)((0,r.Z)({},n),{},{last_migration:t.payload});case c.dN:return(0,r.Z)((0,r.Z)({},n),{},{available:"1"===t.payload.mf_available});default:return e}}))}},85562:function(e,t,n){"use strict";n.r(t);var r=n(62295),a=n(22497),i=n(47895),s=n(22633),c=n(27056);t.default=function(){return function(e,t){e((function(e,t){(0,r.dC)((function(){e((0,a.KG)("addonActions",(function(){}))),e((0,a.KG)("postConnectionPanels",(function(){e((0,s.qb)("multisite_tools"))})))}))})),e((function(e,t){(0,r.dC)((function(){e((0,a.KJ)("wpmdbBackupTables",(function(e,n){if("backup_selected"!==n)return e;var r=t(),a=r.multisite_tools,i=r.migrations,s=a.selected_subsite,l=i.current_migration,o=i.local_site,u=i.remote_site,p="push"===l.intent?u.site_details.prefix:o.this_prefix;if(0===s)return e;var m=t().migrations;return(0,c.yR)(s,p,m,"backup")}))),e((0,a.KJ)("addonPanels",(function(t,n){return e((0,i.C)(n))&&["savefile","find_replace","backup_local"].includes(n)&&t.push("multisite_tools"),t}))),e((0,a.KJ)("addonPanelsOpen",(function(e,t){return["savefile","find_replace","backup_local"].includes(t)&&e.push("multisite_tools"),e}))),e((0,a.KJ)("intiateMigrationPostData",(function(e){var n=t().multisite_tools;return n.enabled&&(e.mst_select_subsite="1"),n.selected_subsite>0&&(e.mst_selected_subsite=Number(n.selected_subsite),e.new_prefix=n.new_prefix),n.destination_subsite>0&&(e.mst_destination_subsite=Number(n.destination_subsite)),e}))),e((0,a.KJ)("wpmdb_standard_replace_values",(function(n){if(!t().multisite_tools.enabled)return n;var r=e((0,c.xU)());return n.domain.search=r.search,n.domain.replace=r.replace,n}))),e((0,a.KJ)("wpmdbPreMigrationCheck",(function(t){return e((0,c.Cj)(t))})))}))}))}}},76687:function(e,t,n){"use strict";function r(e,t,n){return"pull"===e&&t||"push"===e&&n}function a(e,t){return!!Object.keys(t).includes(e)&&{subsiteName:t[e],subsites:t}}function i(e,t){return Object.keys(t.subsites).includes(e)?t.subsites[e]:t.url}function s(e){var t=e.local_site,n=e.remote_site,r=e.current_migration.localSource;return{sourceSite:r?t:n,destinationSite:r?n:t}}n.d(t,{G2:function(){return s},sA:function(){return r},ty:function(){return i},wn:function(){return a}})},27056:function(e,t,n){"use strict";n.d(t,{Cj:function(){return x},Ox:function(){return g},Si:function(){return b},o:function(){return _},wg:function(){return w},xL:function(){return E},xU:function(){return h},yR:function(){return y}});var r=n(62295),a=n(42233),i=n(4669),s=n(65730),c=n(76687),l=n(47895),o=n(14251),u=n(29950),p=n(29942),m=n(66441),d=n(4516),f=n(66055),_=((0,a.__)("Multisite Tools addon","wp-migrate-db"),function(){return(0,a.__)("<b>Addon Missing</b> - The Multisite Tools addon is inactive on the <strong>remote site</strong>. Please install and activate it to enable Multisite Tools migration.","wp-migrate-db")}),g=function(e){return(0,f.m)(s.hA,{available:e})},b=function(e){return(0,f.m)(s.xv,e)},h=function(){return function(e,t){var n=t(),r=n.migrations,a=n.multisite_tools,i=a.selected_subsite,s=a.destination_subsite,c=a.enabled,l=r.local_site,o=r.remote_site,u=r.current_migration,m=u.twoMultisites,d=u.localSource,f=d?l:o,_=d?o:l,g="true"===f.site_details.is_multisite,b=g&&0!==i?"//"+f.subsites[i]:f.url,h=g?_.url:"//"+_.subsites[i];return m&&(b=0===i?f.url:"//"+f.subsites[i],h=0===s?_.url:"//"+_.subsites[s]),c||(b=f.url,h=_.url),{search:(0,p.Ph)(b),replace:(0,p.Ph)(h)}}},v=function(e,t){return function(n,a){if(0===e)return!1;var i=a().migrations,s=y(e,t,i,"migration");(0,r.dC)((function(){n((0,o.E2)(s)),n({type:u.mu,payload:"selected"})}))}},E=function(){return function(e,t){var n=t(),a=n.migrations,c=n.multisite_tools,l=(0,d.r5)("intent",t()),o=c.enabled,u=a.local_site,p=a.current_migration;(0,r.dC)((function(){e({type:s.pT,payload:!o,data:u,intent:l});var t=e(h());if(e((0,i.Kf)(t)),!o){var n=c.selected_subsite,r=["find_replace","savefile"].includes(l)?u.this_prefix:p.source_prefix;e(v(n,r))}}))}},w=function(e){e.selectedSubsite;var t=e.selectedSubsiteID,n=e.type;return function(e,r){var a=r().migrations,l=a.current_migration,o=a.local_site,u=l.intent;if(["push","pull"].includes(u)){var p=e(h());e((0,i.Kf)(p))}if(["savefile"].includes(u)&&"savefile"===u&&e({type:m.Dh,payload:!0}),e((function(e,t){var n=t(),r=n.multisite_tools,a=n.migrations,i=a.local_site,l=a.remote_site,o=a.current_migration,u=o.intent,p=o.twoMultisites?r.destination_subsite:r.selected_subsite,m=(0,c.sA)(u,"true"===i.is_multisite,"site_details"in l?"true"===l.site_details.is_multisite:void 0),d="push"===u?l.prefix:i.this_prefix;return p&&p>1&&(m||["find_replace","savefile"].includes(u))&&(d=d+p+"_"),e({type:s.GH,payload:d}),d})),"source"===n){var d=["find_replace","savefile"].includes(u)?o.this_prefix:l.source_prefix;e(v(t,d))}}},y=function(e,t,n){var r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"migration",a=n.local_site,i=n.remote_site,s=n.current_migration.intent,c=a.this_prefixed_tables,o=a.this_table_sizes_hr,u=i.prefixed_tables,p=i.table_sizes_hr,m="true"===a.is_multisite,d="site_details"in i&&"true"===i.site_details.is_multisite,f=(0,l.Lk)(s,{tables:c,tableSizes:o},{tables:u,tableSizes:p},r).tables,_=["".concat(t,"users"),"".concat(t,"usermeta")],g=["".concat(t,"blog_versions"),"".concat(t,"blogs"),"".concat(t,"blogmeta"),"".concat(t,"registration_log"),"".concat(t,"signups"),"".concat(t,"site"),"".concat(t,"sitemeta")],b=[];return f.forEach((function(n,a){if(_.includes(n))b.push(n);else if(!g.includes(n)){if("migration"===r){if("pull"===s&&!d||"push"===s&&!m)return void b.push(n)}else if("pull"===s&&!m||"push"===s&&!d)return void b.push(n);var i=1!==parseInt(e)?"^".concat(t+e,"_"):"^".concat(t,"(?![0-9]+_)");RegExp(i,"i").exec(n)&&b.push(n)}})),b},x=function(e){return function(t,n){var r=(0,d.r5)("intent",n()),a=(0,d.r5)("twoMultisites",n()),i=n().multisite_tools,s=i.enabled,c=i.selected_subsite,l=i.destination_subsite,o=i.new_prefix,u=i.is_licensed,p=i.available;if(!s||!u||!p)return e;var m=[];if(0===c){m.push({name:"MST_NO_SUBSITE",panel:"multisite_tools"})}if(a&&0===l){m.push({name:"MST_NO_DESTINATION",panel:"multisite_tools"})}if("savefile"===r){if(""===o){m.push({name:"MST_EMPTY_PREFIX",panel:"multisite_tools"})}if(!k(o)){m.push({name:"MST_INVALID_PREFIX",panel:"multisite_tools"})}}return m.length>0&&(e=e.concat(m)),e}},k=function(e){return null===new RegExp("[^a-z0-9_]","i").exec(e)}},65730:function(e,t,n){"use strict";n.d(t,{GH:function(){return p},LN:function(){return f},YW:function(){return l},hA:function(){return m},pT:function(){return u},w0:function(){return o},xv:function(){return d}});var r=n(18489),a=n(26429),i=n(29950),s=n(31998),c=n(9106),l="UPDATE_SELECTED_SUBSITE",o="UPDATE_DESTINATION_SUBSITE",u="MST_TOGGLE_ENABLED",p="MST_UPDATE_PREFIX",m="SET_MST_AVAILABLE",d="SET_MST_LICENSED",f="SET_MST_SUB_SINGLE",_={enabled:!1,available:!1,is_licensed:!1,selected_subsite:0,destination_subsite:0,new_prefix:"",message:""};t.ZP=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:_,t=arguments.length>1?arguments[1]:void 0;return(0,a.ZP)(e,(function(n){switch(t.type){case"RESET_APP":return n;case i.n_:case c.$:return n.available="1"===window.wpmdb_data.mst_available,n;case i.Ld:var a=t.payload.profile.value.multisite_tools;return n=(0,r.Z)({},a);case u:return n.enabled=t.payload,""===n.new_prefix&&(n.new_prefix=t.prefix),n;case l:return n.selected_subsite=t.payload,n;case o:return n.destination_subsite=t.payload,n;case p:return n.new_prefix=t.payload,n;case m:return n.message="",n;case d:return n.is_licensed=t.payload,n;case f:return n.enabled=!0,n;case s.dN:return(0,r.Z)((0,r.Z)({},n),{},{available:"1"===t.payload.mst_available});default:return e}}))}},63708:function(e,t,n){"use strict";n.d(t,{n:function(){return a},p:function(){return r}});var r=function(e){var t=e.theme_plugin_files;return"undefined"!==typeof t.available&&!t.available},a=function(e,t){if(!e)return"none";if(e&&t)switch(i(e,t)){case-1:return"down";case 0:return"equal";default:return"up"}return"add"},i=function(e,t){if(e===t)return 0;for(var n=e.split("."),r=t.split("."),a=n.length>=r.length?n.length:r.length,i=0;i<a;i++){var s=void 0!==n[i]?parseInt(n[i]):0,c=void 0!==r[i]?parseInt(r[i]):0;if(s>c)return 1;if(s<c)return-1}return 0}},18213:function(e,t,n){"use strict";n.r(t);var r=n(27166),a=n(33032),i=n(22497),s=n(53273);t.default=function(){return function(e,t){e((function(e,t){})),e((function(e,t){e((0,i.KJ)("wpmdbPreMigrationCheck",(function(t){return e((0,s.DP)(t))}))),e((0,i.KJ)("addMigrationStages",(function(t){return e((0,s.N9)(t))}))),e((0,i.KJ)("mdbAddonActions",function(){var n=(0,a.Z)((0,r.Z)().mark((function n(a){var i,c,l,o,u,p,m,d,f,_,g,b,h,v,E,w,y,x,k,Z;return(0,r.Z)().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:if(i=t(),c=i.theme_plugin_files,l=i.migrations,o=c.theme_files,u=c.plugin_files,p=c.muplugin_files,m=c.other_files,d=c.core_files,f=o?o.enabled:null,_=u?u.enabled:null,g=p?p.enabled:null,b=m?m.enabled:null,h=d?d.enabled:null,v="pull"===l.current_migration.intent?l.remote_site.site_details:l.local_site.site_details,E=Object.keys(v.themes).length>0,w=Object.keys(v.plugins).length>0,y=Object.keys(v.muplugins).length>0,x=Object.keys(v.others).length>0,k=Object.keys(v.core).length>0,Z=window.hasOwnProperty("wpmdbmf")&&t().media_files.enabled,!(f&&E||_&&w||g&&y||b&&x||h&&k)){n.next=19;break}if(window.hasOwnProperty("wpmdbmf")&&(!window.hasOwnProperty("wpmdbmf")||Z)){n.next=19;break}return n.next=18,e((0,s.G_)());case 18:return n.abrupt("return",n.sent);case 19:return n.abrupt("return",a);case 20:case"end":return n.stop()}}),n)})));return function(e){return n.apply(this,arguments)}}()))}))}}},53273:function(e,t,n){"use strict";n.d(t,{BI:function(){return y},DP:function(){return T},G_:function(){return A},KX:function(){return P},LD:function(){return h},N9:function(){return O},OL:function(){return w},RX:function(){return N},Vj:function(){return E},WZ:function(){return k},pM:function(){return Z},q2:function(){return R},zk:function(){return x}});var r=n(27166),a=n(33032),i=n(31125),s=n(42233),c=n(51645),l=n(66055),o=n(3460),u=n(29816),p=n(4516),m=n(66866),d=n(42714),f=n(10304),_=n(98135),g=n(73264),b=n(29942),h=((0,s.__)("Theme & Plugin Files addon","wp-migrate-db"),"TPF"),v={theme_files:"themes",plugin_files:"plugins",muplugin_files:"muplugins",other_files:"others",core_files:"core"},E=function(){return(0,s.__)("<b>Addon Missing</b> - The Theme & Plugin Files addon is inactive on the <strong>remote site</strong>. Please install and activate it to enable Theme & Plugin Files migration.","wp-migrate-db")},w=function(e){return function(t){t({type:c._5,payload:e})}},y=function(e,t){return function(n){n({type:c.bY,payload:{available:e,message:t}})}},x=function(e){return function(t){t({type:c.lJ,payload:e})}},k=function(e,t){var n={themes:c.fe,plugins:c.Xr,muplugins:c.sW,others:c.Md,core:c.TO};return(0,l.m)(n[t],e)},Z=function(e,t){var n={themes:c.rR,plugins:c.P,muplugins:c.EJ,others:c.ND,core:c.xE};return(0,l.m)(n[t],e)},N=function(e,t){var n={themes:c.HA,plugins:c.CM};return(0,l.m)(n[t],e)},P=function(e,t){var n={themes:c.Ni,plugins:c.W3,muplugins:c.vs,others:c.qR,core:c.u8};return(0,l.m)(n[t],e)},S=function(e,t){var n=(0,i.Z)(t),r={name:{themes:"SELECTED_THEMES_EMPTY",plugins:"SELECTED_PLUGINS_EMPTY",muplugins:"SELECTED_MUPLUGINS_EMPTY",others:"SELECTED_OTHERS_EMPTY",core:"SELECTED_CORE_EMPTY"}[e],panel:{themes:"theme_files",plugins:"plugin_files",muplugins:"muplugin_files",others:"other_files",core:"core_files"}[e]};return n.push(r),n};function T(e){return function(t,n){var r=n().theme_plugin_files,a=r.theme_files,i=r.themes_selected,s=r.themes_excluded,c=r.themes_option,l=r.plugin_files,o=r.plugins_selected,u=r.plugins_excluded,p=r.plugins_option,m=r.muplugin_files,d=r.muplugins_selected,f=r.muplugins_option,_=r.other_files,g=r.others_selected,b=r.others_option,h=r.core_files,v=r.core_selected,E=r.core_option;return a.enabled&&"selected"===c&&0===i.length&&(e=S("themes",e)),a.enabled&&"except"===c&&0===s.length&&(e=S("themes",e)),l.enabled&&"selected"===p&&0===o.length&&(e=S("plugins",e)),l.enabled&&"except"===p&&0===u.length&&(e=S("plugins",e)),m&&m.enabled&&"selected"===f&&0===d.length&&(e=S("muplugins",e)),_&&_.enabled&&"selected"===b&&0===g.length&&(e=S("others",e)),h&&h.enabled&&"selected"===E&&0===v.length&&(e=S("core",e)),e}}function O(e){return function(t,n){var r=n(),a=r.theme_plugin_files,i=r.migrations,s=i.local_site,c=i.remote_site,l=i.current_migration,o=l.intent,u=a.theme_files,p=a.plugin_files,m=a.muplugin_files,d=a.other_files,f=a.core_files,_="pull"===o?c.site_details:s.site_details,g="savefile"===o;return u&&u.enabled&&Object.keys(_.themes).length>0&&(!0===g||"true"===(0,b.NR)("theme_files",l,s,c))&&e.push("theme_files"),p&&p.enabled&&Object.keys(_.plugins).length>0&&(!0===g||"true"===(0,b.NR)("plugin_files",l,s,c))&&e.push("plugin_files"),m&&m.enabled&&Object.keys(_.muplugins).length>0&&(!0===g||"true"===(0,b.NR)("muplugin_files",l,s,c))&&e.push("muplugin_files"),d&&d.enabled&&Object.keys(_.others).length>0&&(!0===g||"true"===(0,b.NR)("other_files",l,s,c))&&e.push("other_files"),f&&f.enabled&&e.push("core_files"),e}}var A=function(){return function(){var e=(0,a.Z)((0,r.Z)().mark((function e(t,n){var a,i,s;return(0,r.Z)().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(a=t(D()),i=n().theme_plugin_files,s=(0,p.r5)("intent",n()),["push","pull","savefile"].includes(s)){e.next=7;break}return e.abrupt("return",!1);case 7:if(i.theme_files.enabled||i.plugin_files.enabled||i.muplugin_files.enabled||i.other_files.enabled||i.core_files.enabled){e.next=9;break}return e.abrupt("return",!1);case 9:return e.next=11,t((0,o.Z6)("ADDONS_STAGE",[{fn:R,args:[a]}]));case 11:return e.abrupt("return",!0);case 12:case"end":return e.stop()}}),e)})));return function(t,n){return e.apply(this,arguments)}}()},C=function(e,t){var n=e.theme_plugin_files,r=(0,p.r5)("intent",e),a="push"===r||"savefile"===r?e.migrations.local_site.site_details[t]:e.migrations.remote_site.site_details[t],i=[];if("all"===n["".concat(t,"_option")])return Object.values(a).forEach((function(e){"savefile"!==r&&e[0].path.includes("wp-migrate-db")||i.push(e[0].path)})),i;if("active"===n["".concat(t,"_option")])return Object.values(a).forEach((function(e){e[0].active&&i.push(e[0].path)})),i;if("selected"===n["".concat(t,"_option")])return n["".concat(t,"_selected")];if("except"===n["".concat(t,"_option")]){var s=n["".concat(t,"_excluded")];return Object.values(a).forEach((function(e){s.includes(e[0].path)||i.push(e[0].path)})),i}},R=function e(t){var n=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return function(){var i=(0,a.Z)((0,r.Z)().mark((function a(i,c){var l,u,m,d,_,b,h,E,w,y,x,k,Z,N,P,S,T,O,A,R,D,L,M;return(0,r.Z)().wrap((function(r){for(;;)switch(r.prev=r.next){case 0:return n||i((0,g.F)(t)),l=(0,p.r5)("migration_id",c()),u=(0,p.r5)("stages",c()),m=c().theme_plugin_files,d=C(c(),"plugins"),_=C(c(),"muplugins"),b=C(c(),"themes"),h=C(c(),"others"),E=C(c(),"core"),w=C(c(),v[t]),y=m.excludes,x=m.themes_option,k=m.plugins_option,Z=m.muplugins_option,N=m.others_option,P=m.core_option,S=m.plugins_excludes,T=m.muplugins_excludes,O=m.themes_excludes,A=m.others_excludes,R=m.core_excludes,D={stage:v[t],stages:u,theme_folders:JSON.stringify(b),plugin_folders:JSON.stringify(d),muplugin_folders:JSON.stringify(_),other_folders:JSON.stringify(h),core_folders:JSON.stringify(E),themes_option:x,plugins_option:k,muplugins_option:Z,others_option:N,core_option:P,folders:JSON.stringify(w),migration_state_id:l,excludes:JSON.stringify(y),plugins_excludes:JSON.stringify(S),muplugins_excludes:JSON.stringify(T),themes_excludes:JSON.stringify(O),others_excludes:JSON.stringify(A),core_excludes:JSON.stringify(R),is_cli_migration:0},r.next=14,i((0,f.A)("/tpf-initiate-file-migration",D));case 14:if(L=r.sent){r.next=17;break}return r.abrupt("return");case 17:if(M={theme_files:(0,s.__)("themes","wp-migrate-db"),plugin_files:(0,s.__)("plugins","wp-migrate-db"),muplugin_files:(0,s.__)("must-use plugins","wp-migrate-db"),other_files:(0,s.__)("others","wp-migrate-db"),core_files:(0,s.__)("core","wp-migrate-db")},!L.data.recursive_queue){r.next=22;break}return r.next=21,i((0,g.T)(t,L,e,M[t]));case 21:return r.abrupt("return",r.sent);case 22:return r.next=24,i((0,o.Z6)("ADDONS_STAGE",[{fn:I,args:[t,L]}]));case 24:case"end":return r.stop()}}),a)})));return function(e,t){return i.apply(this,arguments)}}()},I=function e(t,n){var i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0;return function(){var n=(0,a.Z)((0,r.Z)().mark((function n(a,s){var c,g,b,h,E,w,y,x,k,Z,N;return(0,r.Z)().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:return a((0,l.m)(m.V$,window.wpmdbtp_settings.strings.loading_transfer_queue)),c=(0,p.r5)("migration_id",s()),g={stage:v[t],offset:i,migration_state_id:c},n.next=5,a((0,f.A)("/tpf-get-queue-items",g));case 5:if(b=n.sent){n.next=8;break}return n.abrupt("return");case 8:if("complete"!==(h=b.data).status){n.next=20;break}return E=C(s(),"themes"),w=C(s(),"plugins"),y=C(s(),"muplugins"),x=C(s(),"others"),k=C(s(),"core"),Z=C(s(),v[t]),N={currentStage:t,stage:v[t],migration_state_id:c,theme_folders:JSON.stringify(E),plugin_folders:JSON.stringify(w),muplugin_folders:JSON.stringify(y),other_folders:JSON.stringify(x),core_folders:JSON.stringify(k),folders:JSON.stringify(Z)},n.next=19,a((0,o.Z6)("ADDONS_STAGE",[{fn:u.e5,args:[t,"/tpf-transfer-files",N]}]));case 19:return n.abrupt("return");case 20:if(h.hasOwnProperty("queue_status")){n.next=23;break}return a((0,_.Qc)()),n.abrupt("return",!1);case 23:return a((0,d.I6)(h.queue_status.size/1e3)),n.next=26,a((0,o.Z6)("ADDONS_STAGE",[{fn:e,args:[t,b]}]));case 26:case"end":return n.stop()}}),n)})));return function(e,t){return n.apply(this,arguments)}}()},D=function(){return function(e,t){var n=(0,p.r5)("stages",t()),r=["theme_files","plugin_files","muplugin_files","other_files","core_files"];return n.find((function(e){return r.includes(e)}))}}},51645:function(e,t,n){"use strict";n.d(t,{CM:function(){return y},EJ:function(){return h},HA:function(){return w},Md:function(){return f},ND:function(){return v},Ni:function(){return x},P:function(){return b},TO:function(){return _},W3:function(){return k},Xr:function(){return m},_5:function(){return l},bY:function(){return o},fe:function(){return p},lJ:function(){return u},qR:function(){return N},rR:function(){return g},sW:function(){return d},u8:function(){return P},vs:function(){return Z},xE:function(){return E}});var r=n(18489),a=n(26429),i=n(29950),s=n(9106),c=n(31998),l="TOGGLE_THEME_PLUGIN_FILES",o="SET_TPF_AVAILABLE",u="SET_TPF_LICENSED",p="UPDATE_THEMES_OPTION",m="UPDATE_PLUGINS_OPTION",d="UPDATE_MUPLUGINS_OPTION",f="UPDATE_OTHERS_OPTION",_="UPDATE_CORE_OPTION",g="UPDATE_SELECTED_THEMES",b="UPDATE_SELECTED_PLUGINS",h="UPDATE_SELECTED_MUPLUGINS",v="UPDATE_SELECTED_OTHERS",E="UPDATE_SELECTED_CORE",w="UPDATE_EXCLUDED_THEMES",y="UPDATE_EXCLUDED_PLUGINS",x="UPDATE_THEMES_EXCLUDES",k="UPDATE_PLUGINS_EXCLUDES",Z="UPDATE_MUPLUGINS_EXCLUDES",N="UPDATE_OTHERS_EXCLUDES",P="UPDATE_CORE_EXCLUDES",S={available:!1,is_licensed:!1,message:"",theme_files:{enabled:!1},themes_option:"all",themes_selected:[],themes_excluded:[],themes_excludes:".DS_Store\n.git\nnode_modules",plugin_files:{enabled:!1},plugins_option:"all",plugins_selected:[],plugins_excluded:[],plugins_excludes:".DS_Store\n.git\nnode_modules",muplugin_files:{enabled:!1},muplugins_option:"selected",muplugins_selected:[],muplugins_excludes:".DS_Store\n.git\nnode_modules",other_files:{enabled:!1},others_option:"selected",others_selected:[],others_excludes:".DS_Store\n.git\nnode_modules",core_files:{enabled:!1},core_option:"all",core_selected:[],core_excludes:".DS_Store\n.git\nnode_modules",state:{status:""}};t.ZP=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:S,t=arguments.length>1?arguments[1]:void 0;return(0,a.ZP)(e,(function(n){switch(t.type){case"RESET_APP":return n;case s.$:return(0,r.Z)((0,r.Z)({},n),{},{available:1===window.wpmdb_data.tpf_available,message:""});case c.Pi:return(0,r.Z)((0,r.Z)({},n),{},{state:(0,r.Z)((0,r.Z)({},n.state),{},{status:c.Pi})});case c.wd:return(0,r.Z)((0,r.Z)({},n),{},{state:(0,r.Z)((0,r.Z)({},n.state),{},{status:c.wd})});case l:return n[t.payload].enabled=!n[t.payload].enabled,n;case i.Ld:return t.payload.profile.value.theme_plugin_files;case u:return n.is_licensed=t.payload,n;case o:var a=t.payload.available;return n.available=a,t.payload.hasOwnProperty("message")&&(n.message=t.payload.message),a||(n.theme_files.enabled=!1,n.plugin_files.enabled=!1,n.muplugin_files.enabled=!1,n.other_files.enabled=!1,n.core_files.enabled=!1),n;case g:return n.themes_selected=t.payload,n;case b:return n.plugins_selected=t.payload,n;case h:return n.muplugins_selected=t.payload,n;case v:return n.others_selected=t.payload,n;case E:return n.core_selected=t.payload,n;case w:return n.themes_excluded=t.payload,n;case y:return n.plugins_excluded=t.payload,n;case p:return n.themes_option=t.payload,n;case m:return n.plugins_option=t.payload,n;case d:return n.muplugins_option=t.payload,n;case f:return n.others_option=t.payload,n;case _:return n.core_option=t.payload,n;case k:return(0,r.Z)((0,r.Z)({},n),{},{plugins_excludes:t.payload});case Z:return(0,r.Z)((0,r.Z)({},n),{},{muplugins_excludes:t.payload});case x:return(0,r.Z)((0,r.Z)({},n),{},{themes_excludes:t.payload});case N:return(0,r.Z)((0,r.Z)({},n),{},{others_excludes:t.payload});case P:return(0,r.Z)((0,r.Z)({},n),{},{core_excludes:t.payload});case c.dN:return(0,r.Z)((0,r.Z)({},n),{},{available:"1"===t.payload.tpf_available});default:return e}}))}},73264:function(e,t,n){"use strict";n.d(t,{F:function(){return m},T:function(){return d}});var r=n(27166),a=n(33032),i=n(42233),s=n(66055),c=n(29950),l=n(66866),o=n(82174),u=n(42714),p=n(3460),m=function(e){return function(t){t((0,s.m)(c.f7,e)),t((0,s.m)(l.V$,(0,i.gB)((0,i.__)("Starting file transfer of %s files...","wp-migrate-db"),o.r[e]))),t((0,u.v_)())}},d=function(e,t,n){var c=arguments.length>3&&void 0!==arguments[3]?arguments[3]:(0,i.__)("media library","wp-migrate-db");return function(){var o=(0,a.Z)((0,r.Z)().mark((function a(o){return(0,r.Z)().wrap((function(r){for(;;)switch(r.prev=r.next){case 0:return o((0,s.m)(l.V$,(0,i.gB)((0,i.__)("Scanning %s - %s files found...","wp-migrate-db"),c,parseInt(t.data.items_count||0).toLocaleString()))),r.next=3,o((0,p.Z6)("ADDONS_STAGE",[{fn:n,args:[e,!0]}]));case 3:return r.abrupt("return",r.sent);case 4:case"end":return r.stop()}}),a)})));return function(e){return o.apply(this,arguments)}}()}},98135:function(e,t,n){"use strict";n.d(t,{Qc:function(){return c},SF:function(){return s},g6:function(){return l}});var r=n(42233),a=(n(98487),n(4516),n(42714),n(62457)),i=n(66866),s=function(e,t,n,r,a,i,s,c){var l=e.target.tagName.toLowerCase();return!a&&("input"!==l&&"label"!==l&&(n?s(t):r?i(t):(i(t),c()),!1))},c=function(){return function(e){e((0,a.m)({error_type:i.gF,error_message:(0,r.__)("Queue status not returned from remote.","wp-migrate-db")}))}},l=function(e,t,n,a,i){return e?arguments.length>5&&void 0!==arguments[5]&&arguments[5]?(0,r.__)("stepping up failed","wp-migrate-db"):null===t||n||a?null!==t&&n&&!a?(0,r.__)("stepping down","wp-migrate-db"):null!==t&&!n&&a?(0,r.__)("maintaining","wp-migrate-db"):null!==t&&i?(0,r.__)("max reached","wp-migrate-db"):(0,r.__)("initializing","wp-migrate-db"):(0,r.__)("stepping up","wp-migrate-db"):(0,r.__)("static","wp-migrate-db")}},29816:function(e,t,n){"use strict";n.d(t,{AG:function(){return h},e5:function(){return v}});var r=n(27166),a=n(18489),i=n(33032),s=n(42233),c=n(4516),l=n(10304),o=n(3460),u=n(53273),p=n(66055),m=n(66866),d=n(29950),f=n(42714),_=n(82174),g=n(29942),b=n(98135),h=["media_files","theme_files","plugin_files","muplugin_files","other_files","core_files"];function v(e,t,n){var u=arguments.length>3&&void 0!==arguments[3]?arguments[3]:null,h=arguments.length>4&&void 0!==arguments[4]&&arguments[4],w=arguments.length>5&&void 0!==arguments[5]&&arguments[5],y=arguments.length>6&&void 0!==arguments[6]?arguments[6]:0,x=arguments.length>7&&void 0!==arguments[7]&&arguments[7];return function(){var k=(0,i.Z)((0,r.Z)().mark((function i(k,Z){var N,P,S,T,O,A,C,R,I,D,L,M,F,B,j;return(0,r.Z)().wrap((function(r){for(;;)switch(r.prev=r.next){case 0:return N=!0,null!==u||x||(u=(0,c.r5)("currentMaxPayloadSize",Z())),!1===x&&null!==u&&y>=5&&(u=null,x=!0,w=!1,h=!1),k((0,p.m)(m.V$,(0,s.gB)((0,s.__)("Migrating %s files..."),_.r[e]))),k((0,p.m)(d.b4)),P=N&&y<=5,r.next=8,k((0,l.A)(t,(0,a.Z)((0,a.Z)({},n),{},{payloadSize:u,stabilizePayloadSize:h,stepDownSize:w,retries:y,forceHighPerformanceTransfers:!0}),P));case 8:if(S=r.sent,T=!1,S){r.next=16;break}if(!P){r.next=15;break}T=!0,r.next=16;break;case 15:case 20:return r.abrupt("return");case 16:if(O=S.data,!S||!S.data||"complete"!==S.data.status){r.next=21;break}return r.next=20,k((0,o.Z6)("ADDONS_STAGE",[{fn:E,args:[e,S.data.status]}]));case 21:if(R=u,T||(!0!==S.success&&N?T=!0:(I=Object.keys(O.status),D=O.status.error,L=O.status[I[0]].total_transferred,M=O.status,F=M.current_payload_size,B=M.reached_max_payload_size,j=M.fallback_payload_size,R=F,A=w||B||h,C=L/1e3,!L||D?T=!0:(k((0,p.m)(d.sr,L)),k((0,p.m)(d.IJ,L)),k((0,p.m)(d.X4,null!==F&&void 0!==F?F:j))),setTimeout((function(){var t=(0,c.xg)("timer",Z());k((0,p.m)(d.NL,{currentStage:e,status:(0,b.g6)(N,u,w,h,B,T),maxPayloadSize:(0,g.lL)(N?null!==F&&void 0!==F?F:0:j),currRequestSize:(0,g.lL)(null!==L&&void 0!==L?L:0),time:Math.floor(t.time/1e3)}))}),0))),!0!==T||!N){r.next=27;break}return r.next=26,k((0,o.Z6)("ADDONS_STAGE",[{fn:v,args:[e,t,n,R,!1,!0,y+1,x]}]));case 26:case 30:return r.abrupt("return",r.sent);case 27:return C&&k((0,f.gF)(C)),r.next=30,k((0,o.Z6)("ADDONS_STAGE",[{fn:v,args:[e,t,n,R,A,!1,0,x]}]));case 31:case"end":return r.stop()}}),i)})));return function(e,t){return k.apply(this,arguments)}}()}function E(e,t){return function(){var n=(0,i.Z)((0,r.Z)().mark((function n(a,i){var s,l,m,f;return(0,r.Z)().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:if(s=(0,c.r5)("stages",i()),a((0,p.m)(d.sP,e)),!h.includes(e)){n.next=12;break}if(l=h.indexOf(e),m=h.slice(l+1),!(f=m.find((function(e){return s.includes(e)})))){n.next=10;break}return n.next=9,a((0,o.Z6)("ADDONS_STAGE",[{fn:u.q2,args:[f]}]));case 9:return n.abrupt("return",n.sent);case 10:if("complete"!==t){n.next=12;break}return n.abrupt("return",a((0,o.Z6)(o.ly)));case 12:case"end":return n.stop()}}),n)})));return function(e,t){return n.apply(this,arguments)}}()}},10304:function(e,t,n){"use strict";n.d(t,{A:function(){return l}});var r=n(27166),a=n(33032),i=n(29942),s=n(62457),c=n(66866),l=function(e,t){var n=arguments.length>2&&void 0!==arguments[2]&&arguments[2];return function(){var l=(0,a.Z)((0,r.Z)().mark((function a(l){var o;return(0,r.Z)().wrap((function(r){for(;;)switch(r.prev=r.next){case 0:return r.prev=0,r.next=3,(0,i.op)(e,t);case 3:o=r.sent,r.next=10;break;case 6:return r.prev=6,r.t0=r.catch(0),n||(l((0,s.m)({error_type:c.gF,error_message:r.t0})),(0,i.Tc)(r.t0)),r.abrupt("return",!1);case 10:if(o.success){r.next=13;break}return n||(l((0,s.m)({error_type:c.gF,error_message:o.data})),l((0,s.U)())),r.abrupt("return",!1);case 13:return r.abrupt("return",o);case 14:case"end":return r.stop()}}),a,null,[[0,6]])})));return function(e){return l.apply(this,arguments)}}()}},82174:function(e,t,n){"use strict";n.d(t,{r:function(){return a}});var r=n(42233),a={theme_files:(0,r.__)("theme","wp-migrate-db"),plugin_files:(0,r.__)("plugin","wp-migrate-db"),muplugin_files:(0,r.__)("must-use plugin","wp-migrate-db"),media_files:(0,r.__)("media","wp-migrate-db"),other_files:(0,r.__)("other","wp-migrate-db"),core_files:(0,r.__)("WordPress core","wp-migrate-db")}},80855:function(e,t,n){"use strict";var r=n(4665),a=n(42233);t.Z=function(e){var t=e.videoID,n=e.height;return r.createElement("iframe",{height:n||360,width:"100%",title:(0,a.__)("WP Migrate Help Videos","wp-migrate-db"),src:"//fast.wistia.net/embed/iframe/".concat(t,"?embedType=iframe&videoFoam=true&fullscreenButton=true&qualityMin=1080"),frameBorder:"0",scrolling:"no",className:"wistia_embed",name:"wistia_embed",allowFullScreen:!0})}},95402:function(e,t,n){"use strict";n.d(t,{F:function(){return a}});var r=n(29942),a=function(e){var t,n=e.remote_site,a=e.local_site;switch(e.intent){case"pull":t={domain:{search:(0,r.Ph)(n.url),replace:(0,r.Ph)(a.this_url)},path:{search:n.path,replace:a.this_path}};break;case"push":case"savefile":t={domain:{search:(0,r.Ph)(a.this_url),replace:(0,r.Ph)(n.url)},path:{search:a.this_path,replace:n.path}};break;case"import":t={domain:{search:(0,r.Ph)(n.URL),replace:(0,r.Ph)(a.this_url)},path:{search:n.path,replace:a.this_path}}}return t.domain.enabled=!0,t.path.enabled=!0,t}},85704:function(e,t,n){"use strict";n.r(t),n.d(t,{default:function(){return _}});var r=n(88368),a=n(4665),i=n(7354),s=n(62295),c=n(42233),l=n(19085),o=n(27325),u=n(10937),p=(0,s.$j)((function(e){return{settingsStatus:(0,o.u)("status",e)}}),{checkLicenceAgain:u.Ew})((function(e){var t,n,r=!1,i=e.settingsStatus,s=e.settings;return s&&(t=s.licence,n=s.masked_licence),i.check_again&&(r=!0===i.check_again),a.createElement(a.Fragment,null,a.createElement("div",{className:"flex-container licence-action"},a.createElement("a",{onClick:function(){e.checkLicenceAgain(t,n)}},(0,c.__)("Check my license again","wp-migrate-db")),a.createElement("div",{className:"relative"},r&&a.createElement(l.Q,{className:"license-notification-spinner"}))))})),m=n(91399),d=function(e){var t=e.settings,n=t.status,r=t.errors,i=n.disable_ssl;return a.createElement(a.Fragment,null,a.createElement("div",{className:"flex-container licence-action"},a.createElement("button",{className:"btn-tooltip-stroke",onClick:function(){return e.disableSSL()}},(0,c.gB)((0,c.__)("Temporarily disable SSL for connections to %s","wp-migrate-db"),"api.deliciousbrains.com")),a.createElement(m.OP,{position:!1,condition:i,errorMsg:r.disable_ssl,spinnerCond:i&&!0===i})))},f=n(30463),_=function(e){var t=(0,s.v9)((function(e){return e.dbi_api_data.licence})),n=t.license_ui_status,c=t.licence_status,l=(0,a.useState)(null),o=(0,r.Z)(l,2),u=o[0],m=o[1],_=["subscription_expired","licence_not_found","no_activations_left"];return(0,a.useEffect)((function(){(0,i.includes)(_,c)?m(a.createElement(p,e)):"activation_deactivated"===c&&m(a.createElement(f.Z,e))}),[]),u||(""===n?null:"check_again"===n?a.createElement(p,e):"connection_failed"===n?a.createElement(d,e):null)}},30463:function(e,t,n){"use strict";var r=n(4665),a=n(42233),i=n(62295),s=n(19085),c=n(27325),l=n(10937);t.Z=(0,i.$j)((function(e){return{settingsStatus:(0,c.u)("status",e)}}),{reactivateLicense:l.PE})((function(e){var t=!1,n=e.settingsStatus,i=e.className,c=e.btnText;return n.reactivate_license&&(t=!0===n.reactivate_license),r.createElement(r.Fragment,null,r.createElement("div",{className:"flex-container licence-action"},r.createElement("a",{onClick:function(){e.reactivateLicense()},className:i||""},c||(0,a.__)("Reactivate license","wp-migrate-db")),r.createElement("div",{className:"relative"},t&&r.createElement(s.Q,{className:"license-notification-spinner"}))))}))},34653:function(e,t,n){"use strict";n.r(t),n.d(t,{addonsLoaded:function(){return w},runAddonsStage:function(){return y}});var r=n(27166),a=n(33032),i=n(62295),s=n(65730),c=n(9106),l=n(27325),o=n(38906),u=n(66055),p=n(53273),m=n(78579),d=n(27056),f=n(29942),_=n(22497),g=function(e,t,n){return function(r){r(e(n,t))}},b=function(e,t){return function(t){var n=e.media_files,r=e.dbi_api_data;n&&(t(g(m.Um,(0,m.nv)(),"1"===e.migrations.local_site.mf_available)),(0,f.Yu)()&&!1===n.is_licensed&&t((0,m.AN)(E("wp-migrate-db-pro-media-files",r))))}},h=function(e,t){return function(t){var n=e.theme_plugin_files,r=e.dbi_api_data;n&&(t(g(p.BI,(0,p.Vj)(),"1"===e.migrations.local_site.tpf_available)),(0,f.Yu)()&&!1===n.is_licensed&&t((0,p.zk)(E("wp-migrate-db-pro-theme-plugin-files",r))))}},v=function(e){return function(t,n){var r=n(),a=r.profiles,i=r.migrations,c=r.multisite_tools,l=r.dbi_api_data,o=i.current_migration,p=i.local_site,m=o.intent;if(c){t((0,_.KJ)("wpmdb_post_connection_errors",(function(e){return(0,f.sk)(m,n())&&e.warning.push(p["mst_required_message_".concat(m)]),e}))),t(g(d.Ox,(0,d.o)(),e.mst_available)),(0,f.Yu)()&&!1===c.is_licensed&&t((0,d.Si)(E("wp-migrate-db-pro-multisite-tools",l)));var b=!o.twoMultisites&&("true"===p.is_multisite||e.site_details&&"true"===e.site_details.is_multisite);["push","pull"].includes(m)&&b&&null===a.current_profile&&t((0,u.m)(s.LN))}}},E=function(e,t){return!(!t.api_data||!t.api_data.addons_available_list)&&e in t.api_data.addons_available_list};function w(){return function(e,t){e({type:c.$});var n=t(),r=n.migrations,a=r.local_site,s=r.remote_site;(0,i.dC)((function(){e(b(n,s,a)),e(h(n,s,a)),e(v(s,a))}))}}function y(e,t){return function(){var t=(0,a.Z)((0,r.Z)().mark((function t(n,a){var i,s,c,u;return(0,r.Z)().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(i=(0,l.u)("delay_between_requests",a()),e.length&&(s=e[0].fn,c=e[0].args),!(i>0)){t.next=8;break}return t.next=5,(0,o.QK)((function(){return n((0,o.Am)(s,c))}),1e3*i);case 5:u=t.sent,t.next=11;break;case 8:return t.next=10,n((0,o.Am)(s,c));case 10:u=t.sent;case 11:return t.abrupt("return",u);case 12:case"end":return t.stop()}}),t)})));return function(e,n){return t.apply(this,arguments)}}()}},40882:function(e,t,n){"use strict";n.r(t),n.d(t,{betaOptionToggle:function(){return a}});var r=n(27325),a=function(){return function(e,t){(0,r.u)("beta_optin",t())||window.wpmdb_data.is_beta_plugins_installed&&window.confirm(window.wpmdb_strings.rollback_beta_to_stable)&&(window.location=window.wpmdb_data.rollback_to_stable_url)}}},34363:function(e,t,n){"use strict";n.r(t),n.d(t,{changeConnection:function(){return z},connectToRemote:function(){return U},copyLicenseToRemote:function(){return C},handleTableSelects:function(){return B},resetMigration:function(){return T},retryOverHTTP:function(){return I},setConnectedProfileName:function(){return L},setConnectionStatus:function(){return P},setConnectionStatusBatch:function(){return S},setError:function(){return A},shouldShowSSLNotice:function(){return D},updateConnectionState:function(){return O},updatePluginOnRemote:function(){return R}});var r=n(88368),a=n(18489),i=n(27166),s=n(33032),c=n(12544),l=n.n(c),o=n(62295),u=n(42233),p=n(29942),m=function(e){var t=0,n=window.WPMDBStore.getState();if(0===e.length)return window.wpmdb_strings.connection_info_missing;var a=function(e){var t=e.split("\n"),n=e;if(1===t.length){var r=e.trim().split(" ");2===r.length&&(!1===window.wpmdb_data.openssl_available&&(r[0]=r[0].replace("https://","http://")),n=r[0]+"\n"+r[1])}return n}(e),i=a.split("\n"),s=(0,r.Z)(i,2),c=s[0],l=s[1];return(0,p.mQ)(c)?("undefined"!==typeof l&&(t=l.length),32!==t&&40!==t?window.wpmdb_strings.connection_info_key_invalid:c===window.wpmdb_data.connection_info[0]?window.wpmdb_strings.connection_info_local_url:l===n.settings.key?window.wpmdb_strings.connection_info_local_key:{url:c,key:l,str:a}):window.wpmdb_strings.connection_info_url_invalid},d=n(66055),f=n(61987),_=n(31998),g=n(58696),b=n(4516),h=n(14251),v=n(27114),E=n(29950),w=n(22633),y=n(34653),x=n(22497),k=n(47585),Z=n(39881);var N=function(e){e.connectionState;var t=e.result,n=void 0===t?null:t,r=e.intent,c=void 0===r?null:r;return function(e,t){(0,o.dC)((function(){var r=D(t()),l=(0,a.Z)((0,a.Z)({},k.A),{},{button_status:"hidden"});r&&(l.ssl_notice=!0),e(S(l)),e(B((0,b._P)("this_tables",t()),n.data.tables,(0,b.r5)(["intent","tables_selected","backup_option","backup_tables_selected"],t()))),e((0,h.I8)(!0)),!1!==e((0,Z.G9)(n,!0,c))&&(e(L()),e((function(e,t){var n=["database","save_profile","theme_plugin_files"];null===(0,b.r5)("selected_existing_profile",t())&&n.push("custom_fields","standard_fields","multisite_tools"),(0,g.C)("status",t()).ssl_notice&&n.push("connect"),e((0,x.O)("postConnectionPanelsOpen",n)),(0,o.dC)((function(){e((0,w.G7)(n)),e((0,x.Kw)("postConnectionPanels")),e((0,w.qb)("database","connect"))}))})),e(function(){var e=(0,s.Z)((0,i.Z)().mark((function e(t){var n;return(0,i.Z)().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:n=[],window.hasOwnProperty("wpmdbmf")&&n.push("media_files"),window.hasOwnProperty("wpmdbtp")&&n.push("theme_plugin_files"),n.length>0&&(t((0,w.nW)(n)),t((0,y.addonsLoaded)()));case 4:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}()))}))}},P=function(e,t){return(0,d.m)(f.i6,{key:e,statusVal:t})},S=function(e){return function(t){t((0,d.m)(f.N_,e))}},T=function(e){return(0,d.m)(_.rK,e)},O=function(e){return(0,d.m)(f.iv,e)},A=function(e){return(0,d.m)(f.i6,{key:"error",statusVal:e})},C=function(){return function(){var e=(0,s.Z)((0,i.Z)().mark((function e(t,n){var r,a;return(0,i.Z)().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return r=n(),a=r.migrations.connection_info.connection_state,e.next=4,(0,p.op)("/copy-license-to-remote",{url:a.url,key:a.key});case 4:return e.abrupt("return",e.sent);case 5:case"end":return e.stop()}}),e)})));return function(t,n){return e.apply(this,arguments)}}()},R=function(e){return function(){var t=(0,s.Z)((0,i.Z)().mark((function t(n,r){var a,s,c;return(0,i.Z)().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return a=r(),s=a.migrations.connection_info.connection_state,t.prev=2,t.next=5,(0,p.op)("/update-plugin-on-remote",{url:s.url,key:s.key,slug:e});case 5:c=t.sent,t.next=15;break;case 8:if(t.prev=8,t.t0=t.catch(2),"AbortError"!==t.t0.name){t.next=13;break}return console.log("fetch aborted",t.t0),t.abrupt("return");case 13:return n(j(t.t0.message)),t.abrupt("return",!1);case 15:return t.abrupt("return",c);case 16:case"end":return t.stop()}}),t,null,[[2,8]])})));return function(e,n){return t.apply(this,arguments)}}()},I=function(){return function(){var e=(0,s.Z)((0,i.Z)().mark((function e(t,n){var r,s,c;return(0,i.Z)().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:r=n(),s=(0,a.Z)({},r.migrations.connection_info.connection_state),c=r.migrations.current_migration.intent,s.url=s.url.replace("https","http"),s.value=s.value.replace("https","http"),t(P("retry_over_http",!0)),t(O(s)),t(U(c));case 8:case"end":return e.stop()}}),e)})));return function(t,n){return e.apply(this,arguments)}}()},D=function(e){return(0,g.C)("status",e).retry_over_http},L=function(){return function(e,t){var n=(0,b.r5)("intent",t()),r=(0,b._P)("url",t());l()(["push","pull"],n)&&(r=(0,b.FY)("url",t()));var a=r.replace(/(^\w+:|^)\/\//,""),i="push"===n?(0,u.gB)((0,u.__)("Push to %s","wp-migrate-db"),a):(0,u.gB)((0,u.__)("Pull from %s","wp-migrate-db"),a);e((0,h.fJ)(i))}},M=function(e){return function(t){var n=new AbortController;return t((0,d.m)("SET_ABORT_CONTROLLER",{key:e,controller:n})),n}},F=function(e){return function(t){(0,o.dC)((function(){t(S({connecting:!0,button_status:"disabled"})),t(O((0,a.Z)({},e)))}))}},B=function(e,t,n){var a=(0,r.Z)(n,4),i=a[0],s=a[1],c=a[2],l=a[3];return function(n,r){var a=e,o=t;"pull"===i&&(a=t,o=e);var u=a.filter((function(e){return s.includes(e)}));if(u.length!==s.length&&n({type:E.KU,payload:u}),"backup_manual_select"===c){var p=o.filter((function(e){return l.includes(e)}));p.length!==l.length&&n({type:E.O$,payload:p})}}},j=function(e){return function(t,n){var r,a=(0,v.Y)(e);t((r=a,function(){var e=(0,s.Z)((0,i.Z)().mark((function e(t,n){return(0,i.Z)().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(D(n())&&t(P("ssl_notice",!0)),!r.includes("#197")){e.next=5;break}return t(I()),e.abrupt("return");case 5:r.includes("401 Unauthorized")&&t(S({show_auth_form:!0})),r.includes("#195")&&t(P("copy_to_remote",!0)),r.includes("#196")&&t(P("update_plugin_on_remote",!0)),t(P("error_msg",r));case 9:case"end":return e.stop()}}),e)})));return function(t,n){return e.apply(this,arguments)}}())),(0,g.C)("status",n()).retry_over_http?t(S({retry_over_http:!1,ssl_notice:!0})):t((function(e,t){e((0,d.m)(_.rK,["connect"])),e(S({error:!0,button_status:"",connecting:!1}))}))}},U=function(e){return function(){var t=(0,s.Z)((0,i.Z)().mark((function t(n,r){var a,s,c,l;return(0,i.Z)().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return a=(0,b.NR)("connection_info",r()),s=a.connection_state,c=n(M("connect_to_remote")),n(F(s)),t.prev=3,t.next=6,(0,p.op)("/verify-connection",{url:s.url,key:s.key,intent:e},c);case 6:l=t.sent,t.next=16;break;case 9:if(t.prev=9,t.t0=t.catch(3),"AbortError"!==t.t0.name){t.next=14;break}return console.log("fetch aborted",t.t0),t.abrupt("return");case 14:return n(j(t.t0.message)),t.abrupt("return",!1);case 16:if(l.success){t.next=19;break}return n(j(l)),t.abrupt("return");case 19:return n(N({connectionState:s,result:l,intent:e})),t.abrupt("return",l);case 21:case"end":return t.stop()}}),t,null,[[3,9]])})));return function(e,n){return t.apply(this,arguments)}}()},z=function(e){return function(){var t=(0,s.Z)((0,i.Z)().mark((function t(n,r){var s,c,l,u,p,d,f;return(0,i.Z)().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(s=r(),c=s.migrations.current_migration.intent,l=s.migrations.connection_info.status.pasted,n(S({error:!1,button_status:""})),u=e.connection_state,p=m(e),d=(0,a.Z)((0,a.Z)({},u),{},{value:p.str,url:p.url,key:p.key}),"string"!==typeof p){t.next=11;break}return f=(0,a.Z)((0,a.Z)({},u),{},{value:e}),(0,o.dC)((function(){n(O(f)),n(S({error:!0,button_status:"disabled",error_msg:p}))})),t.abrupt("return",p);case 11:n(O(d)),l&&(0,o.dC)((function(){n(U(c,!0)),n(P("pasted",!1))}));case 13:case"end":return t.stop()}}),t)})));return function(e,n){return t.apply(this,arguments)}}()}},10937:function(e,t,n){"use strict";n.d(t,{Ew:function(){return Z},PE:function(){return y},Qj:function(){return N},T3:function(){return E},eD:function(){return v},fo:function(){return w},s$:function(){return k},sL:function(){return h}});var r=n(88368),a=n(27166),i=n(33032),s=n(62295),c=n(42233),l=n(66055),o=n(19826),u=n(59299),p=n(68424),m=n(27325),d=n(29942),f=n(87326),_=n(27114),g=n(71049),b=n(31998);function h(e){return function(){var t=(0,i.Z)((0,a.Z)().mark((function t(n){return(0,a.Z)().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,n((0,d.wM)({preRequest:(0,s.dC)((function(){n((0,f.c9)("licence_action",!0)),n((0,f.rT)("licence"))})),asyncFn:e,requestFailed:function(e){var t,r;n((t=e,r="licence_action",function(e){return e((0,f.nE)("licence",(0,c.__)("API error: ")+(0,_.Y)(t))),e((0,f.c9)(r,!1)),!1}))},requestSuccess:function(e){n((0,f.c9)("licence_action",!1))}}));case 2:return t.abrupt("return",t.sent);case 3:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}()}function v(e,t){return function(n){var a=e.data,i=a.errors;if(i){if(n((0,f.c9)(t,!1)),Object.keys(i).length>0){var s=Object.keys(i),c=(0,r.Z)(s,1)[0];a.hasOwnProperty("licence_status")&&(c=a.licence_status),n(P(c))}n((0,l.m)(u.eD,i))}else n(P("active_licence"))}}function E(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:h,t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"ui";return function(){var r=(0,i.Z)((0,a.Z)().mark((function r(i,c){var o,f,_,h,E,w;return(0,a.Z)().wrap((function(r){for(;;)switch(r.prev=r.next){case 0:if(o=(0,m.u)("licence",c()),f=(0,g.C)("api_time",c()),_=Date.now()-f,h=(0,g.C)("license_check_context",c()),36e5,t||!(_<36e5)||n!==h){r.next=7;break}return r.abrupt("return");case 7:return r.next=9,i(e((0,d.op)("/check-license",{licence:o,context:"all",message_context:n},!1,i)));case 9:if(E=r.sent){r.next=12;break}return r.abrupt("return",null);case 12:return w=E.data,i(v(E,"check_licence")),(0,s.dC)((function(){i((0,l.m)(u._K,E.data)),i((0,l.m)(u.I,Date.now())),i((0,l.m)(u.aJ,n)),i((function(e){(0,d.op)("/local-site-details").then((function(t){t.success&&e((0,l.m)(b.dN,t.data))}))})),t&&i((0,p.Ax)())})),r.abrupt("return",w);case 16:case"end":return r.stop()}}),r)})));return function(e,t){return r.apply(this,arguments)}}()}function w(e){return function(){var t=(0,i.Z)((0,a.Z)().mark((function t(n){var r;return(0,a.Z)().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,n(h((0,d.op)("/activate-license",{licence_key:e,context:"all",message_context:"settings"},!1,n)));case 2:return r=t.sent,t.abrupt("return",x(n,r));case 4:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}()}function y(){return function(){var e=(0,i.Z)((0,a.Z)().mark((function e(t){var n,r;return(0,a.Z)().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t((0,f.c9)("reactivate_license",!0)),e.next=3,t(h((0,d.op)("/reactivate-license",{context:"all",message_context:"settings"},!1,t)));case 3:return n=e.sent,r=x(t,n),t((0,f.c9)("reactivate_license","success")),t(E(h,!0)),e.abrupt("return",r);case 8:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}()}function x(e,t){if(!t)return null;var n=t.data,r=n.errors,c=n.error_type;return e(v(t,"licence_action")),"undefined"!==typeof t.data.dbrains_api_down?(e((0,l.m)(u.zB,!0)),e((0,p.Ax)()),e(E())):e((0,l.m)(u.zB,!1)),!(r&&!Object.keys(r).includes("subscription_expired"))&&(1===Number(t.data.is_first_activation)&&"subscription_expired"!==c&&e((0,l.m)(u.x_,"first_activation")),"subscription_expired"!==c&&e((0,l.m)(u.qi,!0)),t.success&&t.data&&(0,s.dC)((0,i.Z)((0,a.Z)().mark((function n(){return(0,a.Z)().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:"subscription_expired"!==c&&e(P("active_licence")),e((0,p.Ax)()),e((0,f.m7)("masked_licence",t.data.masked_licence)),e((0,l.m)(u._K,t.data)),"subscription_expired"!==c&&e((0,l.m)(u.eD,[])),e(E());case 6:case"end":return n.stop()}}),n)})))),setTimeout((function(){e((0,l.m)(u.qi,!1))}),2500),t)}function k(){return function(){var e=(0,i.Z)((0,a.Z)().mark((function e(t){var n;return(0,a.Z)().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,t(h((0,d.op)("/remove-license",{remove_license:!0},!1,t)));case 2:if(n=e.sent){e.next=5;break}return e.abrupt("return",null);case 5:return(0,s.dC)((function(){t((0,l.m)(u.q3)),t(function(){var e=(0,i.Z)((0,a.Z)().mark((function e(t){return(0,a.Z)().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:(0,s.dC)((function(){t((0,l.m)(u.eD,[])),t((0,l.m)(u._K,[])),t((0,l.m)(u.I,0)),t((0,p.kO)())}));case 1:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}())})),e.abrupt("return",n);case 7:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}()}function Z(e,t){return function(){var n=(0,i.Z)((0,a.Z)().mark((function n(r,i){var s,c;return(0,a.Z)().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:return r((0,f.c9)("check_again",!0)),s=h,e&&!t&&(s=function(){return w(e)}),n.next=5,r(E(s,!0));case 5:return c=n.sent,r((0,f.c9)("check_again","success")),n.abrupt("return",c);case 8:case"end":return n.stop()}}),n)})));return function(e,t){return n.apply(this,arguments)}}()}function N(e){return function(){var t=(0,i.Z)((0,a.Z)().mark((function t(n){return(0,a.Z)().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.abrupt("return",n((0,l.m)(o.km,{setting:"licence",value:e})));case 1:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}()}function P(e){return function(){var t=(0,i.Z)((0,a.Z)().mark((function t(n){return(0,a.Z)().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.abrupt("return",n((0,l.m)(u.Ih,e)));case 1:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}()}},39881:function(e,t,n){"use strict";n.d(t,{G9:function(){return u}});var r=n(95402),a=n(22497),i=n(4669),s=n(14251),c=n(66055),l=n(29942),o=n(29950);function u(e,t,n){return function(u,p){var m=p();u({type:"UPDATE_REMOTE_SITE",payload:e.data});var d=(0,r.F)({intent:n,remote_site:e.data,local_site:m.migrations.local_site});d=u((0,a.O)("wpmdb_standard_replace_values",d)),u((0,i.mX)(d)),u((0,s.uI)("push"===n));var f="pull"===n?e.data.prefix:m.migrations.local_site.this_prefix,_="pull"===n?m.migrations.local_site.this_prefix:e.data.prefix;return u((0,s.OZ)(f,_)),["push","pull"].includes(n)&&(u((0,s.nX)("true"===e.data.site_details.is_multisite&&"true"===m.migrations.local_site.is_multisite)),u((0,c.m)(o.W$,{local_site_mode:void 0!==m.settings.high_performance_transfers?m.settings.high_performance_transfers:m.migrations.local_site.site_details.high_performance_transfers,remote_site_mode:e.data.site_details.high_performance_transfers}))),!!t&&(u(function(e){return function(t){t({type:o.tX,connected:e})}}(t)),u(function(e,t){return function(n,r){r().migrations.local_site.this_prefix!==e.data.prefix&&n((0,c.m)("SET_CONNECTION_STATUS",{key:"prefix_mismatch",statusVal:!0})),(0,l.SC)(t,r())&&n((0,s.ED)())}}(e,n)))}}},44789:function(e,t,n){"use strict";n.r(t),n.d(t,{TrackMigrationComplete:function(){return l},TrackMigrationStart:function(){return c}});var r=n(27166),a=n(33032),i=n(29942),s=n(4516),c=function(){return function(){var e=(0,a.Z)((0,r.Z)().mark((function e(t,n){var a,c,l,o,u;return(0,r.Z)().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(a=n().settings,c=a.isPro,l=a.allow_tracking,o=l&&c,u=(0,s.r5)("migration_id",n()),o){e.next=5;break}return e.abrupt("return",!1);case 5:return e.prev=5,e.next=8,(0,i.op)("/log-migration",{migration_id:u,complete:!1});case 8:e.next=13;break;case 10:e.prev=10,e.t0=e.catch(5),console.error(e.t0.message);case 13:case"end":return e.stop()}}),e,null,[[5,10]])})));return function(t,n){return e.apply(this,arguments)}}()},l=function(){return function(){var e=(0,a.Z)((0,r.Z)().mark((function e(t,n){var a,i,c,l,o,u;return(0,r.Z)().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(a=n().settings,i=a.isPro,c=a.allow_tracking,c&&i){e.next=4;break}return e.abrupt("return",!1);case 4:if(l=(0,s.r5)("migration_id",n()),o=(0,s.r5)("intent",n()),(u=new FormData).append("action","wpmdb_track_migration_complete"),u.append("nonce",window.wpmdb_data.nonces.flush),u.append("migration_id",l),u.append("intent",o),!["push","pull"].includes(o)){e.next=13;break}return e.abrupt("return");case 13:return e.prev=13,e.next=16,fetch(window.ajaxurl,{method:"POST",body:u});case 16:e.next=21;break;case 18:e.prev=18,e.t0=e.catch(13),console.error(e.t0.message);case 21:case"end":return e.stop()}}),e,null,[[13,18]])})));return function(t,n){return e.apply(this,arguments)}}()}},9106:function(e,t,n){"use strict";n.d(t,{$:function(){return i}});var r=n(26429),a={media_files:window.hasOwnProperty("wpmdbmf"),theme_plugin_files:window.hasOwnProperty("wpmdbtp"),multisite_tools:window.hasOwnProperty("wpmdbmst")},i="ADDONS_LOADED";t.Z=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:a,t=arguments.length>1?arguments[1]:void 0;return(0,r.ZP)(e,(function(n){return"RESET_APP"===t.type?n:e}))}},61987:function(e,t,n){"use strict";n.d(t,{MS:function(){return u},N_:function(){return l},i6:function(){return c},iv:function(){return s}});var r=n(18489),a=n(52089),i=n(47585),s="UPDATE_CONNECTION_INFO",c="SET_CONNECTION_STATUS",l="SET_CONNECTION_STATUS_BATCH",o={connection_state:[],status:i.A},u=(0,a.Lq)(o,{RESET_MIGRATION:function(e,t){return(0,r.Z)((0,r.Z)({},o),{},{connection_state:e.connection_state,status:e.status})},SET_CONNECTION_STATUS:function(e,t){var n=t.payload,r=n.statusVal,a=n.key;return e.status[a]=r,e},SET_CONNECTION_STATUS_BATCH:function(e,t){return e.status=(0,r.Z)((0,r.Z)({},e.status),t.payload),e},SET_MIGRATION_CONNECTED:function(e,t){return e.status.connecting=!1,e},UPDATE_CONNECTION_INFO:function(e,t){return e.connection_state=t.payload,e},LOAD_PROFILE:function(e,t){var n=t.payload.profile.value.connection_info;return(0,r.Z)((0,r.Z)({},n),{},{status:(0,r.Z)({},i.A)})}})},59299:function(e,t,n){"use strict";n.d(t,{I:function(){return m},Ih:function(){return c},_K:function(){return i},aJ:function(){return p},eD:function(){return s},q3:function(){return u},qi:function(){return l},x_:function(){return o},zB:function(){return d}});var r=n(18489),a=n(26429),i="SET_API_DATA",s="UPDATE_LICENSE_ERRORS",c="SET_LICENSE_STATUS",l="SET_LICENSE_SAVED",o="SET_LICENCE_UI_STATUS",u="LICENSE_REMOVED",p="SET_LICENSE_CHECK_CONTEXT",m="SET_API_TIME",d="SET_DBI_DOWN_STATUS",f={display_name:"",user_email:"",license_name:"",licence:{licence_status:window.wpmdb_data.licence_status,licence_ui_status:"",check_again:!1,error_message:"",status:""},license_saved:!1,license_check_context:"ui",license_errors:window.wpmdb_data.license_errors,license_status:{},license_messages:{},license_constant:!1,api_time:0,api_data:window.wpmdb_data.api_data,dbi_down_status:!1};t.ZP=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:f,t=arguments.length>1?arguments[1]:void 0;return(0,a.ZP)(e,(function(n){var a=t.payload;switch(t.type){case i:return(0,r.Z)((0,r.Z)({},n),{},{api_data:a});case o:return(0,r.Z)((0,r.Z)({},n),{},{licence:(0,r.Z)((0,r.Z)({},n.licence),{},{licence_ui_status:a.value})});case u:return(0,r.Z)((0,r.Z)({},n),{},{licence:(0,r.Z)((0,r.Z)({},n.licence),{},{licence_status:null})});case s:return(0,r.Z)((0,r.Z)({},n),{},{license_errors:a});case l:return(0,r.Z)((0,r.Z)({},n),{},{license_saved:a});case"CLEAR_LICENSE_ERRORS":return(0,r.Z)((0,r.Z)({},n),{},{license_errors:[],license_status:{}});case c:return(0,r.Z)((0,r.Z)({},n),{},{licence:(0,r.Z)((0,r.Z)({},n.licence),{},{licence_status:t.payload})});case p:return(0,r.Z)((0,r.Z)({},n),{},{license_check_context:t.payload});case m:return(0,r.Z)((0,r.Z)({},n),{},{api_time:t.payload});case"SET_LICENSE_MESSAGES":return(0,r.Z)((0,r.Z)({},n),{},{license_messages:(0,r.Z)((0,r.Z)({},n.license_messages),t.payload),api_data:(0,r.Z)((0,r.Z)({},n.api_data),{},{errors:t.payload})});case"OVERWRITE_LICENCE_STATUS":return(0,r.Z)((0,r.Z)({},n),{},{licence:(0,r.Z)((0,r.Z)({},n.licence),a)});case d:return(0,r.Z)((0,r.Z)({},n),{},{dbi_down_status:a});default:return e}}))}},26547:function(e,t,n){"use strict";n.d(t,{Dx:function(){return l},EL:function(){return p},Ej:function(){return s},Qy:function(){return d},UL:function(){return f},VG:function(){return o},WH:function(){return m},ci:function(){return c},yQ:function(){return u}});var r=n(18489),a=n(26429),i={status:"",upload_file:"",error:{},file_uploaded:!1,file_size:0,table_sizes:{},table_rows:{},tables:[],file:void 0},s="UPDATE_IMPORT_STATUS",c="SET_IMPORT_DATA",l="SET_UPLOAD_FILE",o="SET_IMPORT_TABLE_DATA",u="RESET_IMPORT_DATA",p="IMPORT_ERROR",m="SET_IMPORT_TABLES",d="SET_IMPORT_FILE",f=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:i,t=arguments.length>1?arguments[1]:void 0;return(0,a.ZP)(e,(function(n){switch(t.type){case"RESET_APP":return i;case s:return(0,r.Z)((0,r.Z)({},n),{},{status:t.payload});case l:return(0,r.Z)((0,r.Z)({},n),{},{upload_file:t.payload});case p:return(0,r.Z)((0,r.Z)({},n),{},{error:t.payload});case c:var a=t.payload,f=a.file_uploaded,_=a.upload_file,g=a.file_size;return(0,r.Z)((0,r.Z)({},n),{},{upload_file:_,file_uploaded:f,file_size:g});case o:var b=t.payload,h=b.table_sizes,v=b.table_rows,E=b.tables;return(0,r.Z)((0,r.Z)({},n),{},{table_sizes:h,table_rows:v,tables:E});case m:return(0,r.Z)((0,r.Z)({},n),{},{tables:t.payload});case d:return(0,r.Z)((0,r.Z)({},n),{},{file:t.payload});case u:return i;default:return e}}))}},31998:function(e,t,n){"use strict";n.d(t,{Pi:function(){return p},dN:function(){return d},rK:function(){return u},wd:function(){return m}});var r=n(18489),a=n(20188),i=n(29950),s=n(66441),c=n(61987),l=n(66866),o=n(26547),u="RESET_MIGRATION",p="MIGRATION_STARTED",m="WPMDB_PRE_MIGRATION",d="UPDATE_LOCAL_SITE",f={local_site:window.wpmdb_data,remote_site:{}};t.ZP=(0,a.UY)({local_site:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:f.local_site,t=arguments.length>1?arguments[1]:void 0,n=(0,r.Z)({},e);if(t.type===d){return["wpmdbmf","wpmdbmst","wpmdbtp"].forEach((function(e){t.payload[e]&&"1"===t.payload[e]?window[e]={enabled:"1"}:window[e]=void 0})),Object.keys(t.payload).forEach((function(e){window.wpmdb_data[e]=t.payload[e]})),(0,r.Z)((0,r.Z)({},e),t.payload)}return n},remote_site:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:f.remote_site,t=arguments.length>1?arguments[1]:void 0;(0,r.Z)({},e);switch(t.type){case u:return{};case"UPDATE_REMOTE_SITE":return(0,r.Z)({},e),t.payload;default:return e}},connection_info:c.MS,current_migration:i.S_,migration_progress:l.D$,search_replace:s.jC,import_data:o.UL})},71049:function(e,t,n){"use strict";n.d(t,{C:function(){return i}});var r=n(4516),a=function(e){return e.dbi_api_data};function i(e,t){return(0,r.fX)(a,"settings",e,t)}},67821:function(e,t,n){"use strict";n.r(t),n.d(t,{selectFromImportData:function(){return i}});var r=n(4516),a=function(e){return e.migrations.import_data};function i(e,t){return(0,r.fX)(a,"import_data",e,t)}},90534:function(e){e.exports="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAJMAAAB2CAYAAADMbKEkAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAABJ+SURBVHgB7Z0PbNz2dcffj3eyZceAz0vcNJgb00piY0tsSxiWrAViUbMSYN26XNY0w4AElrcs6LpgkpGiWwMkotpgbbFllYtg3pq0PtcuirRpLddFitYORMWtm6R/JMtui8iNRbcpWidxQru1LUdH/voe72jrpPvDPz/yyDM/wEm6E4/3h1++3/u9936PACkpgmCQknxkJZeVrH4ObAAPaI4D6GCxbaaujUCEpGJKOG0dm/otYCqJaME/LdZT1DUNIkKClMSS7dj0WbRGw1WFhHCJ74QIScWUUEhIOLAM1NsGRSa3y4oMEZGKKYGgkAYbCcmhiIKCiMhCSjJAJxsuDWdchRiSiikJ3KR0Zky+t3wvBzElFVPcKQlplMVYRA6pzxRn0Hkmi5QEIRGpmGJMhgHFj2RICOkwF1OysqIA41sgQaSWKaZwBn2QMFIxxRTG+F2QMFIx1aBNVjrpBs2g9LqJcLrnkvpMc8HZU1aCAQ58C97sg5ld0z1R5KwHdM2AiMhk0OnmkDhSy1Sm7YZNWzISH0dvpb9iKs6gkwQGUWImzyoRqWVCMjcoec55oWY9DofmDHcJ48oSE0WTLeg0TRi5NGxRVp3zz0KMMAEmknhgkvGeMcnZhj9ndW0C/HI5mixnGfQzWdlKD3OJF6BBYJBLEGnFItDn7OgmsUcy3FGZyiyAQn/j96MXUcx+fMTYiYlmUBYeXLP0gXQK3uEB34v+aC7T0a2bFuvy80ErosnoB3FG/lFjqAQWLZkGkcN2kf8GIVIOjA4WGVfmDvEkCn5Dd8E02RAdA9f7m/+A+u1xGRYtUsAq4lkhXT4zJEkHy9Ihu2xC7VkjZGZjT72ly18YiihPsyjmvLGO7hG832k7xJxpeGS3+51VYdxmI/iA0UH18IUKw0JrKEFoYirVRNUuZWEc+nBCopiy0uP28zMaQtrxYM0AGI899bkCY8xFsIzp+ENjnO9S71ivQQAyaxR8zTppAw5HUEgDQWuZsx3dnifbtlU6MbYGmkTD78YF3GJ3L1hYYIdA+LS7HYBWnB7rcbOpRNPeIu4Yd/72/2z72F17/ns7HHjmWZiamIQzb56u9Qoy3vow5D86ePDYtPrdyT7wicntaXdNa4MxnnyURfEVMDTzTURi4N9HLMMkq7viAfIdJT4KLkF/UXe7Lct2KCo+ZbDWBqvX3gQb3ncb3v4CGuxKZ5Y1pN65oQAeQatB/suC6bdIy4Bn+TSe5bLb7SteGw+AJEFeomVEDB9/VSuAaMojhHMXfcacJZUmDBAQfM8FZrJdnPYl8cFG+8TtDMbZBI5bY0ULht26Fg3F5LD86j+CTR94vxtRFVgmM6T2/IkOLsEo8yi+cWX+40LFdIMygsOy63wXZ2wriYaCmZyzQsX/6H35nAjUeG95DE/sbHrdUkCXwnUE/Mzpt2B/YQ88+fHHYPLwi3W2xOHPNEfV7/7UfaCPsTEIE6qf5t4ccDsWhRZpvpAIOrMzsFD8vjHtfTZNSLYlArYNfaPOIC6F53SKI6oDX/06zJy/UGMrLnPJGncrKDKlnLsfm71Asxb0Eaa9DheUWpnvW3Dbt2NDVZ3aANC+ihZbg8LdBRFCn4eGQLKysye0YQiI79zcywdH4elPfqqOk07OmzWqjv5chkbgcGFiMtXCswOEIzE/Zz2Jr1KAbDt+6WuKJzQ1lGXXOP02p7U+W1QWw4AqG+F1JiZ+KZ8QI/Rd0+cxXx3bKir04dpnqgX5Uvc/3A/Lr7m61iYGs/g2t455xfvB2FJxWnM1La0LzWBwWEJVbcFgZacbcfl1QkXjBHGZZE9QNuIbs5c8URC31oQCLZxOvymajZ9Zx21Pcgt0zCJoMyHGzAKLiXAhKIIOBsamrDHILhqp66DjF4ghi77yQdRBNNTooTx75JXDn4GmWkfhGTPNCFQmHCFiIlwKai4aWqxdfkIJKfFEWD0TOeZf2/FUHad8AZhzYzsHDx7d68qvSok9QovjTv3qNTj0refAI3luFsfVg5N5SEk0wistaZZ3cuo4eAQdSrZ36OCxRC3tSakklLLd/Tt3gx8s4IXUQiWXUMRE/lP9KHlt0ELtTH2oZBLagoIX9nv2nRxy6ENF2vEsRQyhiYmskw/fyUFRDxxVICKoINAuCkwAFMS0KyQj7AjnllDLdg+hdVr9sL9iQcwZUexLgxBQR6dz3Pp9P46pCt7t5OWIOIYpKOk8wU0+JrVlh71UPoTKnG66l9bz0Y+OTcPFEy+EkILyh7CgZS0eHv4vaF+6BPzAMstWiCoRJuaIiAryGufrGBtm0lVDIt+DJ+a1ZK62ieikcxBCX1DwysQkbHzfbeCL2fM0syuAT8iRt2aLAyzD7sKElczN33t6Pj5nAJ9DaZfg+UGgNsvdrn1BzkGmPCIqOjd/PV+5ZGQX5tu0uAiJCF1Mr2MgE8CnmCROJacF8Am3zL12gpQHWmutgCBMYEcY5/2YfJUbbsxqNWmn6gVQm5V4rkfoy8MDOOF4Brovs60G40JKODQQhHVCGzY568I/PVkTp46qaLEVxRPaQByFRIQuplO2ZfILkyEI1uy28koav6+vs0xxK4gEhYB+zj63mzslwlRHFVcROUSyCJMK6DxUEwhDvbNrQh0d74HZjAKS1O3t2dY+tXd9OP4I8zB0Mm8LIZtJJGKauXABloMPBAxTak+XDiW/qwBxAONDXtbChbISJiQiaalDYvIF47E2637IZAQuRIgZse7PhP7CEWgxWAu354lETO1L/AUtJQ6xiaGIgvNkNvJyQyQ+kz8xMV294xYNWgwq8IcWJRLL5Gcmx6AZbWwiwGrRzwURiOna96wCX2Rmm9o0IixoxWxYC06bTejD3LXv+WPwCqYc9pWn9EJxykzUv/K/b0oWw4xR8nvac4afJDDjbCv+cN2JJCmELibqouIdNgEBKNVCWZ2cYaCSMcrNOQsXbexSE/tl8HU41xm3Z41atV5TtniKZ/ucfWHiV4a28teGiWPcV2k9oMX3QVub5qZshaxTtkMZCrNaoxnEVEzeIQFZHPKY2N1Sqk8qj+D1krycsvLUkhCoOmGwLAwScsnaMCbbVQOs7r5IqHkusTyYRWPw+WMFJmW2NxIVpUcysqLTqluQWsNKhSqmdV0b/KVRGFvtdlNqNMYzrB+PcydjEBQShnLpnvdqg1ypbMXMqwcnt6m9G+qGNky9FN1uFSsVqgO+duMG8AMHnreHlzqo35nMU9c6WsgZvz7dXKalW4PPH3UlEDuJC1W61FEfzwQRmphouXjjxmA1yXHrXNUDQQVvOByNojXaCwFLVEKHg+pFUNT9xJnpUdlJkYPYioWQCU1Mt92xWYcg4HAx/0CQNaLVvwAJym+hoNwujqBhz5weWwMW66F2N0mpFnAIpQacrNJDn/oECMJxihN5paMyBsssW9O0WvKICMUBp96XAql0imOEcfo05K52NcHIWcWztIhBhZhg+6Tm7xT0y8r+poVClyaCtOIWbpmoM+8H+u6HVmXm/Hn44fMavPz8qN3xhazwhz7yoJtIfyysU+MVOv67Jgv1meiL7b33npY15WfQEj39+Kft1cpO6yBabEr9PV2Awc9zfdBEaPLCzXPj5MdBTZeB+pFSq6NjnpfpCxvmaG0cNftasnRJy5ZY7N+5B868+daCx406fT0rsRT8MQxNoCQkc9T9DJi6Jhf7cOZM3f720RAIXDLAfMeolUYSJqb7vHWNizlMf/KRR2WanztDGK1OrrXSZrk7v4l6i3usQxeHNyFVoGAqSbH/YhZd06KcRjq2YDgUIqbb0eH2XR0QQyYP/0B2LNDTn/y0bXXrdcTzsMg0Rz5LVH6T7WRb5/OWZSriY3LOcHh0C/qCd9NnCuwz0Re98b0+F1nGFFqFPJd6QrrevhyIh+CsU3EQMtQ4DfOK05xbOxljYTZRU5wAc2Axre3c0ELDW4l3Ll50bTk2/a3HMEgmo0AEWFDPyRYM5330K7CY1nX6uoxbrMmtXOlqOzqRnKoIChm4oTw0vO1ntuQWdXQ8F22qidknX2AxUTig1bjl1j9zdUavxRPJeONN48lHHoMnBj5mX1fm9G9OuVlRkyvPlqbd5u68oPZ0GQxgO0QEOeL0O7CYWsnxdljt0g86fuQIzM7ObjPeeEun+8bpt/STr06p4AUPyWAvqL3rBxiHsEuf6SoOl64+ESgCTs439V9qFS6cO69/8fHPyM59Spc0gr6DZctz2l/f/w9HVt3YQXVYCvi5VguHnqBXFa0GDnkymJnOy2kTEWDqhTEdMqZGVtB5NFBowEMD+USw5Kql8n0f/Tc7wj01b0ZXDRLSu1atIidcQSEpEACMQVHvJuGXcC3X0tNtBEImcJyJBOW3M1wcoQCkk1s89dpr9uebH/VejJ+XFkq4TPK6hMtUqhKGdYqKwGKifFX70tbzm4hrV5U/11qIBKtUi65BQgksJkoxNHLCadpMzSsoKUosWrzYWLHymlz70qWQchkJmK9mMXEhsJjIt7h1c2XLxwvnLhhHX3wx98tXjttiq+Jb2Q4qDY909q/t2gDrNrZe8NMrPO5lyA0QUs9030f77ek0CefQN5/z3XrQ/ZXKWxgGKuZTK69ZnF02kYQqTWHFceSMuplKu4ECoR/88APGdauvF54OoCF3auIoCvfGpFlCu3yZW3BEYjASR0c99D7gQbi1twdu/5v3C5kt0kSB6pEcq0n7/Nf//ESCZ6J2r06NZTJDcWl+H+tmX24uKu2WPU98zpg7/JIfd7GcT3tu91eMxx98yKB0SLCGrlFC/lU5JRNins8LsRYTQTPA3U9s9ysoY/LwSzB+6LBhvHl6wZA5+YOX4MAzX8f/f58at+dWvGulYZqmDokjHqKK9TA3Fx/XAIYZTI8UPvPEmdO/PVWztIEWO6Ljn+u9927juuvF+2jRw3TG+dZm+FSJERNB8awHHv0P8MrRl14e+eYXvpSf//h18vWFf3zk35XYrwz2A84Khzavj7THVeyHubmQP+NyJUgFN65fr1R7/O/++Z/yLSkkIqRqhHokSkyEn2sAL8EZGyZxK+I01Pk/t/Lqll1JYxOxoBInJuKQ96ts5u596MM6wOUm9Tfe/KeRFY81lQgFlSifaS5O1N0LFGvCGdzY4sXtE3+++S8VJkHr1RzXAPN+fYO9t+yCEEmsmFavu8leq5fiGoNlsl1hBjgj6QMeBicxifzsjs/DHR/6YFPTIpRCev1Xv7Z/UyyMqiPOvm0Y3LRsf2z5NaUaeaqTejfORinE0aRSZ+dC2kIuxFiNxFomBz/xpyBQbu/Ua7+GYy/9yHjlJ+NU6uvLiachuhnVEgz43Y3aI/red9LFRJCgHnj046Hm2cjfopkkRc1FlytTGc6tvUpE1RJMH+q9RXh5sL3nVhATQc1Y7/mXB0E0JKJvf/kZ/dVjP5MhZOik2IiCul1sf6sFMAsj5D5a5jQiI62QFUhSW78aYMrEdsoF1mUbo9/YN/Ps/39h5u3X33g3RMBF9LcohjZ5+MVS4WBYvhVjubEv7RA+s2sZMRGUFA46VJBP9JMXvgd7n97Z/ovJn7ZjArgdIoZERRWsZBVXr10L2bY2EIzcc/9HxrTdO3QQSGJnc9WgdEu11TKO00z/p9tF3Kaa30NiFFXgJwKqeKBZaxgTDKvUK0oDgbSUmOxlSeXVMiQgcpanxidBnzpOy6UTmTpxSnBEC4pB5i4Q3GOzZRxwB6ohn734jvHzH4+3VN4tjBAIyyxbIbK2PJG5uXrQ0NBqQiLIQn1tx1NiwxKCe2y2nJhaGfL3qMuvKDjjgyIrM1MxJQyqmBBYp04plnFRVQWpmBKInwLBOuTsMpWDx6aDWqlUTAnkZHmltFi4TFZKPXBkAHzScrO5KwWa3a3r3IDC+oUd5HQeK62Ifq8eqBzZZ/14KqYWhER159/fY/fc9I0PQaXDXAtihxH+9/N+ypsvY1/azNuQl1qmKwAqcaGh0GlmS43KKIlMNVWNgqDMkrrUO2+eABekYrrCaVxLxXSWuarLTaS8paoGUrxz7uxZu0KhTtlLjlszF8d2/5/WaF+pz5RiQ37W/sIevO1e0NeBMclVpDwVU0oFlNus1iiEm2ZDV0jiVulq1SkpDtU7z/C8fZWoOkhmBP2hU5JHFUE51xmuiQS6hl46uzKWSqd4wvajdu25dB99p7oXX7R9pqIFKgapXMUSUq4sKA/4y6njWvmuUm+oKzngaJ2K02NdqL0hPqe5Q0oKdYuZnnplK/5V0gVdvr4GbMEjspLLAnTiTmRIueKxfWo0NvYFfaxFirr55kKtbf8AKE5XpTgrWUUAAAAASUVORK5CYII="},50247:function(){}},function(e){e.O(0,[532,288],(function(){return t=27523,e(e.s=t);var t}));e.O()}]);