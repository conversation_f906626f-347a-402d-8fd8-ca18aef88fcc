"use strict";(self.webpackJSONPwpmdb=self.webpackJSONPwpmdb||[]).push([[537],{2537:function(e,t,n){n.r(t),n.d(t,{default:function(){return T}});var i=n(18489),a=n(4665),l=n(42233),r=n(62295),s=n(53273),o=n(79043),c=n(70659),u=n.n(c),p=n(75338),d=n(40795),m=n(12544),_=n.n(m),g=(0,r.$j)((function(e){return{theme_plugin_files:e.theme_plugin_files,panel_info:e.panels,local_version:e.migrations.local_site.theme_plugin_files_version}}),{})((function(e){var t="",n=e.theme_plugin_files,i=e.panelsOpen,r=e.selected,s=e.items,o=e.type;if(!_()(i,o)&&(n[o]||{}).enabled){if(t=e.summary,["other_files","muplugin_files"].includes(o)){if(0===r.length)return a.createElement("span",{className:"empty-warning"},(0,l.__)("Empty selection","wp-migrate-db"));var c=[];s.forEach((function(e){r.includes(e.path)&&c.push(e.name)})),t=c.join(", ")}"core_files"===o&&(t=(0,l.__)("Export all Core files","wp-migrate-db"))}return a.createElement(a.Fragment,null,(0,p.ZP)(t))})),f=n(83135),h=n(18832),v=n(63708),b=n(98135),E=function(e){var t=e.ariaLabel,n=e.optionChoices,i=e.type,l=e.value,s=(0,r.I0)(),o=n,c=function(t){s(e.updateOption(t.target.value,i))},u=Object.keys(o).map((function(e){var t=i+"-"+e,n=l===e;return a.createElement("div",{key:e},a.createElement("input",{id:t,type:"radio",value:e,checked:n,onChange:c,name:i}),a.createElement("label",{htmlFor:t},(0,p.ZP)(o[e])))}));return a.createElement("div",{className:"radiogroup",role:"radiogroup","aria-label":t},u)},w=n(78677),y=n(29214),x=n(29942),O=n(49275),P=function(e,t,n){var i=[];if("object"===typeof t){var a=Object.values(t).map((function(e){return e[0].path}));"selected"===n["".concat(e,"_option")]&&n["".concat(e,"_selected")].forEach((function(e){a.includes(e)&&i.push(e)})),"except"===n["".concat(e,"_option")]&&n["".concat(e,"_excluded")].forEach((function(e){a.includes(e)&&i.push(e)})),"active"===n["".concat(e,"_option")]&&Object.values(t).forEach((function(e){e[0].active&&i.push(e[0].path)})),"all"===n["".concat(e,"_option")]&&(i=a)}return i};var Z=function(e,t){var n=e.theme_plugin_files,i=e.panelsOpen,c=e.current_migration,m=e.remote_site,_=e.local_site,Z=c.status,S=c.intent,N=(0,v.p)(e),C=(0,r.I0)(),k=t.title,L=t.type,F=t.panel_name,j=t.items,B=function(){return"savefile"===S?j:j.filter((function(e){return!1===e.path.includes("wp-migrate-db")}))},I=j.map((function(e){return e.path})),T=!1,W={push:"Push",pull:"Pull",savefile:"Export"},R={all:(0,l.gB)((0,l.__)("%s all %s","wp-migrate-db"),W[S],L),active:(0,l.gB)((0,l.__)("%s only active %s","wp-migrate-db"),W[S],L),selected:(0,l.gB)((0,l.__)("%s only selected %s","wp-migrate-db"),W[S],L),except:(0,l.gB)((0,l.__)("%s all %s <b>except</b> those selected","wp-migrate-db"),W[S],L)},M=function(e,t,n,i,a){var l=a.site_details,r=P(i,l[i],e),s={themes:"theme_files",plugins:"plugin_files",muplugins:"muplugin_files",others:"other_files",core:"core_files"},o=(e[s[i]]||{}).enabled;return{enabled:void 0!==o&&o,isOpen:t.includes(s[i]),selected:r,selectionEmpty:u()(n,{name:"SELECTED_".concat(i.toUpperCase(),"_EMPTY")})}}(n,i,Z,L,"pull"===S?m:_),U=M.enabled,X=M.isOpen,D=M.selected,J=M.selectionEmpty;(0,a.useEffect)((function(){"select"===n["".concat(L,"_option")]&&C(e.updateSelected(D,L)),"except"===n["".concat(L,"_option")]&&e.updateExcluded(D,L)}),[]),U&&!X&&(T=!0);var V=[],$="selected"===n["".concat(L,"_option")]||"except"===n["".concat(L,"_option")];T&&V.push("has-divider"),U&&V.push("enabled");var A={muplugins:(0,l.__)("Select any must-use plugins to be included in the migration.","wp-migrate-db"),others:(0,l.__)("Select any other files and folders found in the <code>wp-content</code> directory to be included in the migration.","wp-migrate-db"),core:(0,l.__)("Including WordPress core files ensures that the exported archive contains the exact version of WordPress installed on this site, which is helpful when replicating the site in a new environment. ","wp-migrate-db")},G=(0,x.NR)(F,c,_,m);return a.createElement(d.Z,{title:k,className:V.join(" "),panelName:F,disabled:N,writable:G,enabled:U,forceDivider:T,callback:function(t){return(0,b.SF)(t,F,X,U,N,e.addOpenPanel,e.removeOpenPanel,(function(){return C((0,s.OL)(F))}))},toggle:(0,s.OL)(F),hasInput:!0,bodyClass:"tpf-panel-body",panelSummary:a.createElement(g,(0,o.Z)({},e,{disabled:N,items:B(),selected:D,title:k,type:F,summary:R[n["".concat(L,"_option")]]}))},a.createElement("div",null,["others","muplugins","core"].includes(L)&&a.createElement("p",{className:"panel-instructions"},(0,p.ZP)(A[L]),"core"===L&&a.createElement(O.Z,{link:"https://deliciousbrains.com/wp-migrate-db-pro/doc/full-site-exports/",content:(0,l.__)("Learn When to Include Core Files","wp-migrate-db"),utmContent:"wordpress-core-files-panel",utmCampaign:"wp-migrate-documentation",hasArrow:!0,anchorLink:"wordpress-core-files"})),!["others","muplugins","core"].includes(L)&&a.createElement(E,{ariaLabel:(0,l.gB)((0,l.__)("%s options","wp-migrate-db"),L),optionChoices:R,intent:"push",type:L,value:n["".concat(L,"_option")],updateOption:s.WZ}),"core"!==L&&a.createElement(w.Z,{id:"".concat(L,"-multiselect"),options:B(),extraLabels:"",stateManager:function(t){"except"===n["".concat(L,"_option")]&&e.updateExcluded(t,L),e.updateSelected(t,L)},selected:D,visible:!0,disabled:!$,updateSelected:function(t){var i=t.map((function(e){return e.path}));"except"===n["".concat(L,"_option")]?e.updateExcluded(i,L):e.updateSelected(i,L)},selectInverse:function(){return"except"===n["".concat(L,"_option")]?(0,h.Z)(e.updateExcluded,I,D,L):(0,h.Z)(e.updateSelected,I,D,L)},showOptions:$,type:L,themePluginOption:n["".concat(L,"_option")]})),"core"!==L&&a.createElement("div",{className:"excludes-wrap"},a.createElement(f.Z,(0,o.Z)({},e,{excludes:n["".concat(L,"_excludes")],excludesUpdater:e.updateExcludes,type:L}))),J&&"selected"===n["".concat(L,"_option")]&&a.createElement(y.Z,{type:"danger"},(0,l.gB)((0,l.__)("Please select at least one %s for migration","wp-migrate-db"),{themes:"theme",plugins:"plugin",muplugins:"must-use plugin",others:"file or directory",core:"file or directory"}[L])),J&&"except"===n["".concat(L,"_option")]&&a.createElement(y.Z,{type:"danger"},(0,l.gB)((0,l.__)("Please select at least one %s to exclude from migration","wp-migrate-db"),"themes"===L?"theme":"plugin")))},S=n(4516),N=(n(22870),n(76178)),C=n(22633);function k(e,t){var n={};return["themes","plugins","muplugins","others","core"].forEach((function(a,l){var r="pull"===t?e.remote_site.site_details[a]:e.local_site.site_details[a],s="pull"===t||"savefile"===t?e.local_site.site_details[a]:e.remote_site.site_details[a],o=r,c=[],u=function(e){if(s){var t=s[e];if(t&&t[0].hasOwnProperty("version"))return t[0].version}return null};for(var p in o){var d=o[p];if(d){var m={name:d[0].name,path:d[0].path};["plugins","themes"].includes(a)&&"savefile"!==t&&(m=(0,i.Z)((0,i.Z)({},m),{},{active:d[0].active,sourceVersion:d[0].version,destinationVersion:u(p)})),c.push(m)}}return n[a]=c})),n}var L=function(e){var t=k(e,e.current_migration.intent).themes;return Z(e,{title:(0,l.__)("Themes","wp-migrate-db"),type:"themes",panel_name:"theme_files",items:t})},F=function(e){var t=k(e,e.current_migration.intent).plugins;return Z(e,{title:(0,l.__)("Plugins","wp-migrate-db"),type:"plugins",panel_name:"plugin_files",items:t})},j=function(e){var t=k(e,e.current_migration.intent).muplugins;return 0===t.length?null:Z(e,{title:(0,l.__)("Must-Use Plugins","wp-migrate-db"),type:"muplugins",panel_name:"muplugin_files",items:t})},B=function(e){var t=k(e,e.current_migration.intent).others;return 0===t.length?null:Z(e,{title:(0,l.__)("Other Files","wp-migrate-db"),type:"others",panel_name:"other_files",items:t})},I=function(e){var t=e.current_migration.intent,n=k(e,t).core;return"savefile"!==t||0===n.length?null:Z(e,{title:(0,l.__)("WordPress Core Files","wp-migrate-db"),type:"core",panel_name:"core_files",items:n})},T=(0,r.$j)((function(e){var t=(0,S.NR)("current_migration",e),n=(0,S.NR)("local_site",e),i=(0,S.NR)("remote_site",e),a=(0,N.O)("panelsOpen",e),l=(0,S.r5)("stages",e),r=(0,S.xg)("status",e);return{theme_plugin_files:e.theme_plugin_files,current_migration:t,local_site:n,remote_site:i,panelsOpen:a,stages:l,status:r}}),{toggleThemePluginFiles:s.OL,updateTPFOption:s.WZ,updateSelected:s.pM,updateExcluded:s.RX,togglePanelsOpen:C.SO,addOpenPanel:C.LX,removeOpenPanel:C.I4,updateExcludes:s.KX,kickOffTPFStage:s.G_})((function(e){return a.createElement("div",{className:"theme-plugin-files"},a.createElement(L,e),a.createElement(F,e),a.createElement(j,e),a.createElement(B,e),a.createElement(I,e))}))}}]);