"use strict";(self.webpackJSONPwpmdb=self.webpackJSONPwpmdb||[]).push([[135],{83135:function(e,t,n){var r=n(4665),l=n(62295),a=n(42233),i=n(75338);t.Z=function(e){var t="exclude-files-".concat(e.type),n=function(e){var t=e.type,n=(0,l.v9)((function(e){return e.migrations})),s=n.current_migration,u=n.local_site,c=n.remote_site;return r.createElement("p",null,(0,i.ZP)((0,a.gB)((0,a.__)('Use <a href="%s" target="_blank" rel="noopener noreferrer">gitignore patterns</a> to exclude files relative to <code>%s</code>',"wp-migrate-db"),"https://deliciousbrains.com/wp-migrate-db-pro/doc/ignored-files/",function(){var e,n,r,l,a,i="pull"===s.intent,p=i?c:u,o=p.site_details,d=o.themes_path,m=o.plugins_path,g=o.muplugins_path,_=o.content_dir,h=i?p.path:p.this_path,f=function(e,t){return void 0===e?null:e.replace(t,"").replace(/^\/|\/$/g,"")};switch(t){case"themes":return null!==(e=f(d,h))&&void 0!==e?e:"wp-content/themes";case"plugins":return null!==(n=f(m,h))&&void 0!==n?n:"wp-content/plugins";case"muplugins":return null!==(r=f(g,h))&&void 0!==r?r:"wp-content/mu-plugins";case"media":return null!==(l=f(i?p.wp_upload_dir:p.this_wp_upload_dir,h))&&void 0!==l?l:"wp-content/uploads";case"others":return null!==(a=f(_,h))&&void 0!==a?a:"wp-content";default:return""}}())))};return r.createElement(r.Fragment,null,r.createElement("h4",{className:"exclude-files-title",id:t},(0,i.ZP)((0,a.gB)((0,a.__)('Excluded Files<span class="screen-reader-text"> %s</span>',"wp-migrate-db"),e.type))),r.createElement(n,{type:e.type}),r.createElement("textarea",{onChange:function(t){e.excludesUpdater(t.target.value,e.type)},value:e.excludes||"","aria-labelledby":t}))}}}]);