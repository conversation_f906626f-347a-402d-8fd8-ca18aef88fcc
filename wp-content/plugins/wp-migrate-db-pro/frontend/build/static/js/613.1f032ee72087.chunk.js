"use strict";(self.webpackJSONPwpmdb=self.webpackJSONPwpmdb||[]).push([[613],{3613:function(e,a,t){t.r(a),t.d(a,{default:function(){return d}});var r=t(4665),l=t(42233),n=t(75338),s=t(29942),i=t.p+"static/media/mdb-banner.afefa48f.svg",m=(0,s.Nh)(i),c={backgroundImage:"url(".concat(m,")")},d=function(e){return r.createElement("div",{className:"mdb-free-sidebar"},r.createElement("div",{className:"inner-wrap"},r.createElement("a",{style:c,className:"wpmdb-banner",target:"_blank",rel:"noopener noreferrer",href:"https://deliciousbrains.com/wp-migrate-db-pro/?utm_campaign=WP%2BMigrate%2BDB%2BPro%2BUpgrade&utm_source=MDB%2BFree&utm_medium=insideplugin"}),r.createElement("div",{className:"wpmdb-upgrade-details"},r.createElement("h1",null,(0,l.__)("Upgrade","wp-migrate-db")),r.createElement("h3",null,(0,n.ZP)((0,l.__)("Gain access to more features when you upgrade to WP Migrate","wp-migrate-db"))),r.createElement("ul",{className:"pro-upgrades"},r.createElement("li",null,(0,l.__)("Email support","wp-migrate-db")),r.createElement("li",null,(0,l.__)("Push and pull your database from one WordPress install to another in 1-click","wp-migrate-db")),r.createElement("li",null,(0,l.__)("Push and pull your themes and plugins","wp-migrate-db")),r.createElement("li",null,(0,l.__)("Sync the media libraries of two sites","wp-migrate-db")),r.createElement("li",null,(0,l.__)("Migrate from multisite to single site and back again","wp-migrate-db")),r.createElement("li",null,(0,l.__)("Run push/pull migrations from the command line","wp-migrate-db")))),r.createElement("div",{className:"wpmdb-discount block"},r.createElement("h2",null,(0,l.__)("Get up to 50% off your first year of WP Migrate!","wp-migrate-db")),r.createElement("h3",null,r.createElement("a",{href:"https://deliciousbrains.com/wp-migrate-db-pro/pricing/?utm_source=MDB%2BFree&utm_medium=insideplugin&utm_campaign=WP%2BMigrate%2BDB%2BPro%2BUpgrade"},(0,l.__)("Get the discount","wp-migrate-db"))),r.createElement("p",{className:"discount-applied"},(0,l.__)("* Discount applied automatically.","wp-migrate-db"))),r.createElement("div",{className:"block testimonial"},r.createElement("div",{className:"header"},r.createElement("a",{className:"author",target:"_blank",rel:"noopener noreferrer",href:"https://twitter.com/BoxyStudio/status/458965600434675712"},"@BoxyStudio"),r.createElement("p",{className:"stars"},r.createElement("span",{className:"dashicons dashicons-star-filled"}),r.createElement("span",{className:"dashicons dashicons-star-filled"}),r.createElement("span",{className:"dashicons dashicons-star-filled"}),r.createElement("span",{className:"dashicons dashicons-star-filled"}),r.createElement("span",{className:"dashicons dashicons-star-filled"}))),r.createElement("p",{className:"quote"},"\u201cWOW. WP Migrate has made our local/live development a breeze. What a brilliant plugin. Worth every penny.")),r.createElement("a",{href:"https://wpengine.com/developers/",target:"_blank",rel:"noopener noreferrer"},r.createElement("div",{className:"dbi"},"Developed and maintained by ",r.createElement("span",{className:"name"},"WP Engine")))))}}}]);