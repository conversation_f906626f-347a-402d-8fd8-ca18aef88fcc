{"locale_data": {"search-regex": {"": {"plural-forms": "nplurals=2; plural=n != 1;"}, "matched rows = %(searched)s": ["filas coincidentes = %(searched)s"], "OK": ["Aceptar"], "Version %s installed! Please read the {{url}}release notes{{/url}} for details.": ["¡Versión %s instalada! Por favor, lee las {{url}}notas de la versión{{/}} para más detalles."], "A problem occurred with your last query. This is likely caused by a combination of search filters that haven't been handled properly.": ["Se ha producido un problema con tu última consulta. Es probable que se deba a una combinación de filtros de búsqueda que no se han gestionado correctamente."], "Query Problem": ["Problema de consulta"], "Progress": ["Progreso"], "%s row deleted.": ["%s rows deleted."], "Refresh": ["Recargar"], "Matched Content": ["Contenido coincidente"], "Your search resulted in too many requests. Please narrow your search terms.": ["Tu búsqueda ha dado como resultado demasiadas solicitudes. Limita los términos de búsqueda."], "Your search conditions have changed. Please refresh to see the latest results.": ["Tus condiciones de búsqueda han cambiado. Actualiza para ver los últimos resultados."], "Matched rows: %(matches)s out of %(total)s total.": ["Filas coincidentes: %(matches)s de %(total)s totales."], "Delete database row": ["Borrar fila de la base de datos"], "View": ["<PERSON>er"], "No title": ["Sin título"], "Empty value": ["Valor vacío"], "No value": ["<PERSON><PERSON><PERSON> valor"], "Click to replace match": ["Haz clic para sustituir la coincidencia"], "Contains encoded data": ["<PERSON><PERSON><PERSON> datos codificados"], "New meta value": ["Nuevo meta valor"], "New meta key": ["Nueva clave meta"], "Apply to matches only": ["Aplicar sólo a las coincidencias"], "Enter replacement": [], "Click to replace column": [], "This column contains special formatting. Modifying it could break the format.": [], "HTML": ["HTML"], "Blocks": ["Bloques"], "Serialized PHP": [], "Paste preset JSON.": [], "Global Search Flags": [], "Global Replace": [], "Global Search": [], "View Columns": [], "Sources": ["<PERSON><PERSON><PERSON>"], "Only include selected columns": [], "WordPress Action": [], "SQL": [], "CSV": ["CSV"], "JSON": ["JSON"], "Export Format": [], "Run a WordPress action for each matching result.": ["Ejecuta una acción de WordPress para cada resultado coincidente."], "Run Action": ["Ejecutar acción"], "Delete matching results.": ["Borrar resultados coincidentes."], "Delete Matches": ["<PERSON><PERSON><PERSON> coincidencias"], "Export matching results to JSON, CSV, or SQL.": ["Exporta los resultados de las coincidencias a JSON, CSV o SQL."], "Export Matches": ["Exportar coincidencias"], "Perform changes to specific values of the matching results.": ["Realiza cambios en valores específicos de los resultados de la concordancia."], "Modify Matches": ["Modificar coincidencias"], "Replace the global search values.": ["Sustituye los valores de la búsqueda global."], "Global Text Replace": ["Sustitución global de texto"], "Just show matching results.": ["Sólo muestra resultados coincidentes."], "No action": ["Ninguna acción"], "Enter replacement value": ["Introduce el valor de sustitución"], "Matched values will be removed": ["Se eliminarán los valores coincidentes"], "Replace \"%1s\"": ["Sustituye \"%1s\""], "Year": ["<PERSON><PERSON>"], "Months": ["Meses"], "Weeks": ["Semanas"], "Days": ["Días"], "Hours": ["<PERSON><PERSON>"], "Minutes": ["<PERSON><PERSON><PERSON>"], "Seconds": ["<PERSON><PERSON><PERSON>"], "Decrement": ["Reducir"], "Increment": ["Aumentar"], "Set Value": ["Establece el valor"], "Replace With": ["<PERSON>em<PERSON><PERSON><PERSON> por"], "Add": ["<PERSON><PERSON><PERSON>"], "Column": ["Columna"], "AND": ["Y"], "Filters": ["<PERSON><PERSON><PERSON>"], "Add sub-filter (OR)": [], "OR": [], "All": [], "between {{first/}} and {{second/}}": [], "No Owner": [], "Has Owner": [], "Any": [], "Excludes any": [], "Includes any": [], "End": [], "Begins": [], "Not contains": [], "Contains": [], "Range": [], "Less": [], "Greater": ["Mayor que"], "Not Equals": ["No iguales"], "Equals": ["I<PERSON>les"], "Optional global search phrase. Leave blank to use filters only.": ["Frase de búsqueda global opcional. Déjala en blanco para utilizar sólo filtros."], "REST API 404": ["404 de la API REST"], "Your REST API appears to be cached and this will cause problems. Please exclude your REST API from your caching system.": ["Tu API REST parece estar cacheada y esto provocará problemas. Por favor, excluye tu API REST de tu sistema de caché."], "Your REST API is showing a deprecated PHP error. Please fix this error.": [], "Your server configuration is blocking access to the REST API.": [], "Can you access your {{api}}REST API{{/api}} without it redirecting?.": [], "You will will need to fix this on your site. Redirection is not causing the error.": [], "Preset uploaded": [], "Multiline": [], "Case": [], "Regex": [], "Nginx": [], "Apache": [], "WordPress": [], "Module": ["<PERSON><PERSON><PERSON><PERSON>"], "Redirection Groups": ["Grupos de redireccionamiento"], "Not accessed": ["No se ha accedido"], "Target": ["Objetivo"], "HTTP Code": ["Código HTTP"], "Position": ["Posición"], "Disabled": ["Desactivado"], "Enabled": ["Activado"], "Status": ["Estado"], "Group": ["Grupo"], "Last Access": ["Último acceso"], "Hit Count": ["Contador de aciertos"], "Source URL (matching)": ["URL de origen (coincidente)"], "Source URL": ["URL de la fuente"], "User meta": ["Meta del usuario"], "Registered": [], "Login": [], "Taxonomy": [], "Post Tag": [], "Post Category": [], "Comment Count": [], "MIME": [], "Parent": [], "Modified GMT": [], "Modified": [], "Password": [], "Has no password": [], "Has password": [], "Ping Status": [], "Comment Status": [], "Open": ["<PERSON>bie<PERSON>o"], "Post Status": ["Estado de publicación"], "Post Type": ["Tipo de contenido"], "Autoload": ["Carga automática"], "Is not autoload": [], "Is autoload": [], "Meta Value": ["Valor meta"], "Meta Key": ["Clave meta"], "Owner ID": ["ID de propietario"], "Comment meta": ["Comentario meta"], "User ID": ["ID de usuario"], "Parent Comment": [], "Trackback": ["Trackback"], "Pingback": ["<PERSON><PERSON>"], "Type": ["Tipo"], "User Agent": ["Agente de usuario"], "Approval Status": ["Estado de aprobación"], "Spam": ["Spam"], "Approved": ["Aprobado"], "Unapproved": ["<PERSON><PERSON><PERSON><PERSON>"], "Date GMT": ["Fecha GMT"], "Date": ["<PERSON><PERSON>"], "IP": ["IP"], "Author": ["Autor"], "Post ID": ["ID de entrada"], "ID": ["ID"], "Terms": ["Térm<PERSON>s"], "Posts (core & custom)": [], "Please review your data and try again.": ["Por favor, revisa tus datos e inténtalo de nuevo."], "There was a problem making a request to your site. This could indicate you provided data that did not match requirements, or that the plugin sent a bad request.": ["Ha habido un problema al hacer una solicitud a tu sitio. Esto podría indicar que has proporcionado datos que no coinciden con los requisitos o que plugin ha enviado una solicitud errónea."], "Bad data": ["<PERSON><PERSON>"], "Your WordPress REST API is returning a 404 page. This is almost certainly an external plugin or server configuration issue.": ["Tu API REST está devolviendo una página 404. Es casi seguro que sea debido a un problema con un plugin externo o con la configuración del servidor."], "2000 per page": ["2000 por página"], "1000 per page": ["1000 por página"], "Set a preset to use by default when Search Regex is loaded.": ["Establece un preajuste para usar por defecto cuando se cargue Search Regex."], "No default preset": ["<PERSON><PERSON><PERSON> preajuste por defecto"], "Default Preset": ["Preajuste por defecto"], "Your admin pages are being cached. Clear this cache and try again. There may be multiple caches involved.": ["Tus páginas de administración están almacenadas en la caché. Vacía esta caché e inténtalo de nuevo. Puede haber varias cachés involucradas."], "This is usually fixed by doing one of the following:": ["Esto normalmente se corrige haciendo algo de lo siguiente:"], "You are using an old or cached session": ["Estás usando una sesión antigua o almacenada en la caché"], "Debug Information": ["Información de depuración"], "Show debug": ["Mostrar la depuración"], "WordPress returned an unexpected message. This could be a PHP error from another plugin, or data inserted by your theme.": ["WordPress ha devuelto un mensaje inesperado. Esto podría ser un error de PHP de otro plugin o a datos insertados por tu tema."], "Your WordPress REST API has been disabled. You will need to enable it to continue.": ["Tu API REST de WordPress ha sido desactivada. Tendrás que activarla para continuar."], "Your REST API is being redirected. Please remove the redirection for the API.": ["Tu API REST está siendo redirigida. Por favor, elimina la redirección para la API."], "A security plugin or firewall is blocking access. You will need to whitelist the REST API.": ["Un plugin de seguridad o un cortafuegos está bloqueando el acceso. Tendrás que poner a la API REST en lista blanca."], "Check your {{link}}Site Health{{/link}} and fix any issues.": ["Comprueba tu {{link}}salud del sitio{{/link}} y corrige cualquier problema."], "Preset": ["<PERSON><PERSON><PERSON><PERSON>"], "Upload": ["Subir"], "Add file": ["Añadir un archivo"], "Preset saved": ["<PERSON><PERSON><PERSON><PERSON> guardado"], "Copy to clipboard": ["Copiar al portapapeles"], "Are you sure you want to delete this preset?": ["¿Seguro que quieres borrar este preajuste?"], "Locked fields": ["Campos bloqueados"], "For example, create tag {{code}}URL{{/code}} and title {{code}}Image URL{{/code}}. Your search could be {{code}}<img src=\"URL\">{{/code}}. When the preset is used it will ask the user for the {{code}}Image URL{{/code}} instead of the full search phrase.": ["<PERSON>r e<PERSON><PERSON><PERSON>, crea la etiqueta {{code}}URL{{/code}} y el título {{code}}URL de la imagen{{/code}}. Tu búsqueda podría ser {{code}}<img src=\"URL\">{{/code}}. <PERSON><PERSON><PERSON> se use el preajuste, pedirá al usuario la {{code}}URL de la imagen{{/code}} en lugar de la frase completa de búsqueda."], "A tag creates a custom input field. Insert the tag anywhere in the search, replace, text filter, or text action and when the preset is used it will be replaced with a custom text field with the tag label.": [], "Enter tag which is used in the field": ["Introduce la etiqueta que se usa en el campo"], "Tag": ["Etiqueta"], "Enter tag title shown to user": ["Introduce el título de la etiqueta mostrado al usuario"], "Tags": ["Etiquetas"], "Locking a field removes it from the search form and prevents changes.": ["Bloquear un campo lo elimina del formulario de búsqueda y evita cambios."], "Fields": ["Campos"], "Locked Fields": ["Campos bloqueados"], "Advanced preset": ["<PERSON><PERSON><PERSON><PERSON>"], "Describe the preset": ["Describe el preajuste"], "Preset Description": ["Descripción del preajuste"], "Give the preset a name": ["Dar un nombre al preajuste"], "Preset Name": ["Nombre del preajuste"], "Edit preset": ["Editar el preajuste"], "Results per page": ["Resultados por página"], "remove phrase": ["eliminar la frase"], "no phrase": ["ninguna frase"], "Import": ["Importar "], "Please check your JSON data is a valid preset. You may have copied it incorrectly, or pasted something that is not a preset.": ["Por favor, comprueba que tus datos JSON son un preajuste válido. Puede que los hayas copiado de forma incorrecta o que hayas pegado algo que no es un preajuste."], "Unable to import preset": ["No ha sido posible importar el preajuste"], "Import preset from clipboard": ["Importar un preajuste desde el portapapeles"], "Done": ["<PERSON><PERSON>"], "Uploaded %(total)d preset": ["Uploaded %(total)d presets", "Subido %(total)d preajuste", "Subidos %(total)d preajustes"], "Importing": ["Importando"], "File selected": ["Archivo seleccionado"], "Click 'Add File' or drag and drop here.": ["Haz clic en «Añadir archivo» o arrástralo y suéltalo aquí."], "Import a JSON file": ["Importar un archivo JSON"], "Import JSON": ["Importar JSON"], "Export JSON": ["Exportar JSON"], "Download presets!": ["¡Descargar los preajustes!"], "There are no presets": ["No hay preajustes"], "Flags": ["Etiquetas"], "Presets": ["<PERSON><PERSON><PERSON><PERSON>"], "If that did not help then {{strong}}create an issue{{/strong}} or send it in an {{strong}}email{{/strong}}.": ["Si eso no ha ayudado, {{strong}}crea una incidencia{{/strong}} o envíalo por {{strong}}correo electrónico{{/strong}}."], "Please check the {{link}}support site{{/link}} before proceeding further.": ["Por favor, consulta el {{link}}sitio de soporte{{/link}} antes de seguir adelante."], "Enter preset name": ["Introducir el nombre del preajuste"], "Enter a name for your preset": ["Introduce un nombre para tu preajuste"], "Saving Preset": ["Guardando el preajuste"], "Update current preset": ["Actualizar el preajuste actual"], "Save search as new preset": ["Guardar la búsqueda como un nuevo preajuste"], "No preset": ["<PERSON><PERSON><PERSON>"], "Saving preset": ["Guardando el preajuste"], "Advanced": ["<PERSON><PERSON><PERSON>"], "Standard": ["<PERSON><PERSON><PERSON><PERSON>"], "Please backup your data before making modifications.": ["Por favor, haz una copia de seguridad de sus datos antes de realizar modificaciones."], "%s row.": ["%s rows.", "%s fila.", "%s filas."], "An unknown error occurred.": ["Se ha producido un error desconocido"], "GUID": ["GUID"], "Slug": ["Slug"], "Excerpt": ["Extracto"], "Content": ["Contenido"], "Display Name": ["Nombre visible"], "Nicename": ["Slug de usuario"], "Value": ["Valor"], "Comment": ["Comentario"], "Name": ["Nombre"], "Title": ["<PERSON><PERSON><PERSON><PERSON>"], "URL": ["URL"], "Disabled! Detected PHP %1$s, need PHP %2$s+": ["¡Desactivado! Detectado PHP %1$s, se necesita PHP %2$s o superior"], "Plugins": ["Plugins"], "WordPress Options": ["Opciones de WordPress"], "User Meta": ["Meta de usuario"], "Users": ["Usuarios"], "Comment Meta": ["Meta de comentario"], "Comments": ["Comentarios"], "Post Meta": ["Meta de la entrada"], "Please enable JavaScript": ["Por favor, activar JavaScript"], "Loading, please wait...": ["<PERSON><PERSON><PERSON>, por favor espere..."], "Create Issue": ["Crear incidencia"], "<code>SearchRegexi10n</code> is not defined. This usually means another plugin is blocking Search Regex from loading. Please disable all plugins and try again.": ["<code>SearchRegexi10n</code> no está definido. Esto generalmente significa que otro plugin está bloqueando la carga de Search Regex. Por favor, desactiva todos los plugins e inténtalo de nuevo."], "If you think Search Regex is at fault then create an issue.": [], "Please see the <a href=\"https://searchregex.com/support/problems/\">list of common problems</a>.": [], "Please note that Search Regex requires the WordPress REST API to be enabled. If you have disabled this then you won't be able to use Search Regex": ["Tenga en cuenta que Search Regex requiere que la API REST de WordPress esté activada. Si la ha desactivado, no podrá utilizar Search Regex."], "Also check if your browser is able to load <code>search-regex.js</code>:": ["Comprueba también si tu navegador es capaz de cargar <code>search-regex.js</code>:"], "This may be caused by another plugin - look at your browser's error console for more details.": [], "Unable to load Search Regex ☹️": ["No ha sido posible cargar Search Regex ☹️"], "Unable to load Search Regex": ["No ha sido posible cargar Search Regex"], "Search Regex requires WordPress v%1$1s, you are using v%2$2s - please update your WordPress": [], "Search Regex Support": ["Soporte de Search Regex"], "You can find full documentation about using Search Regex on the <a href=\"%s\" target=\"_blank\">searchregex.com</a> support site.": [], "Settings": ["<PERSON><PERSON><PERSON><PERSON>"], "Action": [], "Row ID": ["ID de fila"], "No more matching results found.": ["No se encontraron más resultados coincidentes."], "Last page": ["Última página"], "Page %(current)s of %(total)s": ["Página %(current)s de %(total)s"], "Next page": ["<PERSON><PERSON><PERSON><PERSON> sigu<PERSON>e"], "Progress %(current)s%%": [], "Prev page": ["Página anterior"], "First page": ["Primera página"], "%s database row in total": ["%s database rows in total", "%s fila de la base de datos en total", "%s filas de la base de datos en total"], "500 per page": ["500 por página"], "250 per page": ["250 por página"], "100 per page": ["100 por página"], "50 per page": ["50 por página"], "25 per page": ["25 por página"], "Ignore Case": ["Ignorar mayúsculas/minúsculas"], "Regular Expression": ["Expresión regular"], "Row updated": ["Fila actualizada"], "Row replaced": ["<PERSON><PERSON> reemplazada"], "Row deleted": ["<PERSON><PERSON> borra<PERSON>"], "Settings saved": ["<PERSON><PERSON><PERSON>s guardados"], "{{link}}Source Flags{{/link}} - additional options for the selected source. For example, include post {{guid}}GUID{{/guid}} in the search.": ["{{link}}Banderas de búsqueda{{/link}} - añade opciones para seleccionar el origin. Por eje<PERSON>lo, incluye la entrada {{guid}}GUID{{/guid}} en la búsqueda."], "{{link}}Source{{/link}} - the source of data you wish to search. For example, posts, pages, or comments.": [], "{{link}}Regular expression{{/link}} - a way of defining a pattern for text matching. Provides more advanced matches.": ["{{link}}Expresión regular{{/link}} - una forma de definir un patrón para la coincidencia de texto. Proporciona coincidencias más avanzadas."], "{{link}}Search Flags{{/link}} - additional qualifiers for your search, to enable case insensitivity, and to enable regular expression support.": ["{{link}}Banderas de búsqueda{{/link}} - añade calificadores adicionales a tu búsqueda, para activar sensibilidad de mayúsculas y minúsculas, y el soporte de expresiones regulares."], "The following concepts are used by Search Regex:": ["Search Regex utiliza los siguientes conceptos:"], "Like this plugin? You might want to consider {{link}}Redirection{{/link}}, a plugin to manage redirects, from the same author.": ["¿Te gusta este plugin? Quizás quieras considerar probar {{link}}Redirection{/link}}, un plugin para gestionar redirecciones, del mismo autor."], "Redirection": ["Redirección"], "If you want to submit information that you don't want in a public repository then send it directly via {{email}}email{{/email}} - include as much information as you can!": ["Si quieres enviar información que no quieres que aparezca en un repositorio público, envíala directamente por {{email}}email{/email}} - ¡incluye toda la información que puedas!"], "Please note that any support is provide on as-time-is-available basis and is not guaranteed. I do not provide paid support.": ["Ten en cuenta que la asistencia se presta en función del tiempo disponible y no está garantizada. No ofrezco asistencia de pago."], "If you want to report a bug please read the {{report}}Reporting Bugs{{/report}} guide.": ["Si quieres informar de un error, lee la guía {{report}}Informar de errores{{/report}}."], "Full documentation for Search Regex can be found at {{site}}https://searchregex.com{{/site}}.": ["La documentación completa para Search Regex se puede encontrar en el {{site}}https://searchregex.com{{/site}}."], "Need more help?": ["¿Necesitas más ayuda?"], "Results": ["Resul<PERSON><PERSON>"], "Source": ["Fuente"], "Enter search phrase": ["Introduce la frase a buscar"], "Replace All": ["<PERSON><PERSON><PERSON><PERSON><PERSON> todo"], "Search": ["Buscar"], "Search and replace information in your database.": ["Busca y reemplaza la información en tu base de datos."], "Update": ["Actualizar"], "How Search Regex uses the REST API - don't change unless necessary": ["Cómo utiliza Search Regex la API REST - no lo cambies salvo que sea necesario"], "REST API": ["API REST"], "I'm a nice person and I have helped support the author of this plugin": ["Soy una buena persona y he ayudado al autor de este plugin"], "Relative REST API": ["API REST relativa"], "Raw REST API": ["Raw API REST"], "Default REST API": ["REST API por defecto"], "Plugin Support": ["Soporte del plugin"], "Support 💰": ["Soporte 💰"], "You get useful software and I get to carry on making it better.": ["Tú obtienes un software útil y yo sigo mejorándolo."], "Search Regex is free to use - life is wonderful and lovely! It has required a great deal of time and effort to develop and you can help support this development by {{strong}}making a small donation{{/strong}}.": ["«Search Regex» es de uso gratuito - ¡La vida es maravillosa y encantadora! Su desarrollo ha requerido una gran cantidad de tiempo y esfuerzo, y puedes contribuir a él haciendo {{strong}}una pequeña donación{{/strong}}."], "I'd like to support some more.": ["Me gustaría apoyar un poco más."], "You've supported this plugin - thank you!": ["Has apoyado este plugin - ¡gracias!"], "If that doesn't help, open your browser's error console and create a {{link}}new issue{{/link}} with the details.": ["Si eso no ayuda, abre la consola de errores de tu navegador y crea {{link}}una nueva incidencia{{/link}} con los detalles."], "If you are using a page caching plugin or service (CloudFlare, OVH, etc) then you can also try clearing that cache.": [], "Search Regex is not working. Try clearing your browser cache and reloading this page.": ["Search Regex no está funcionando. Prueba a vaciar la caché de tu navegador y recargar esta página."], "clearing your cache.": ["vaciando su caché."], "If you are using a caching system such as Cloudflare then please read this: ": ["Si utilizas un sistema de almacenamiento en caché como Cloudflare, lee esto: "], "Please clear your browser cache and reload this page.": ["Por favor, borra la caché de tu navegador y vuelve a cargar esta página."], "Cached Search Regex detected": ["Se ha detectado Search Regex en la caché"], "Show %s more": ["Show %s more", "Mostrar %s más", "Mostrar %s más"], "Maximum number of matches exceeded and hidden from view. These will be included in any replacements.": ["Se ha superado el número máximo de coincidencias y se ha ocultado a la vista. Estos se incluirán en cualquier reemplazo."], "Delete": ["Bo<PERSON>r"], "Edit": ["<PERSON><PERSON>"], "Check Again": ["Comprobar de nuevo"], "Testing - %s%%": ["Probando - %s%%"], "Show Problems": ["Mostrar problemas"], "Summary": ["Resumen"], "You are using a broken REST API route. Changing to a working API should fix the problem.": ["Estás utilizando una ruta de la API REST que no funciona. Cambiar a una API que funcione debería solucionar el problema."], "Your REST API is not working and the plugin will not be able to continue until this is fixed.": ["Tu API REST no funciona y el plugin no podrá continuar hasta que esto se solucione."], "There are some problems connecting to your REST API. It is not necessary to fix these problems and the plugin is able to work.": ["Hay algunos problemas de conexión a tu API REST. No es necesario solucionar estos problemas y el plugin podrá funcionar."], "Unavailable": ["No disponible"], "Not working but fixable": ["No funciona pero se puede corregir"], "Working but some issues": ["Funciona pero tiene algunos problemas"], "Good": ["Bien"], "Current API": ["API actual"], "Switch to this API": ["Cambiar a esta API"], "Hide": ["Ocultar"], "Show Full": ["<PERSON><PERSON> todo"], "Working!": ["¡Funcionando!"], "Finished!": ["¡Finalizado!"], "Cancel": ["<PERSON><PERSON><PERSON>"], "Replace": ["<PERSON><PERSON><PERSON><PERSON><PERSON>"], "Remove": ["Eliminar"], "Multi": ["Multi"], "Single": ["Individual"], "Support": ["Soporte"], "Options": ["Opciones"], "Search & Replace": ["Buscar y reemplazar"], "If you are using WordPress 5.2 or newer then look at your {{link}}Site Health{{/link}} and resolve any issues.": ["Si estás usando WordPress 5.2 o superior, revisa la {{link}}salud del sitio{{/link}} y resuelve cualquier problema."], "{{link}}Please temporarily disable other plugins!{{/link}} This fixes so many problems.": ["{{link}}¡Desactiva temporalmente otros plugins!{{/link}} Esto soluciona muchos problemas."], "{{link}}Caching software{{/link}}, in particular Cloudflare, can cache the wrong thing. Try clearing all your caches.": ["{{link}}El software de almacenamiento en caché{/link}}, en particular Cloudflare, puede almacenar en caché algo incorrecto. Prueba a borrar todas las cachés."], "What do I do next?": ["¿Qué hago ahora?"], "Something went wrong 🙁": ["Algo ha ido mal 🙁"], "Log out, clear your browser cache, and log in again - your browser has cached an old session.": ["<PERSON>, vacía la caché de tu navegador y vuelve a acceder - tu navegador ha guardado en la caché una sesión antigua."], "Reload the page - your current session is old.": [], "Include these details in your report along with a description of what you were doing and a screenshot.": ["Incluye estos detalles en tu informe junto con una descripción de lo que estabas haciendo y una captura de pantalla."], "Email": ["Correo eletrónico"], "Create An Issue": ["Crear una incidencia"], "Closed": ["<PERSON><PERSON><PERSON>"], "Save": ["Guardar"], "Unable to make request due to browser security. This is typically because your WordPress and Site URL settings are inconsistent, or the request was blocked by your site CORS policy.": ["No se puede realizar la solicitud debido a la seguridad del navegador. Esto suele ocurrir porque los ajustes de WordPress y de la URL del sitio son inconsistentes o la política de intercambio de recursos de origen cruzado («CORS») de tu sitio ha bloqueado la solicitud."], "Possible cause": ["Causa posible"], "This could be a security plugin, or your server is out of memory or has an external error. Please check your server error log": [], "Your server has rejected the request for being too big. You will need to reconfigure it to continue.": ["Tu servidor rechazó la petición por ser demasiado grande. Necesitarás volver a configurarla para continuar."], "Your REST API is probably being blocked by a security plugin. Please disable this, or configure it to allow REST API requests.": [], "Read this REST API guide for more information.": ["Lea esta guía de la API REST para obtener más información."], "WordPress did not return a response. This could mean an error occurred or that the request was blocked. Please check your server error_log.": [], "John Godley": ["<PERSON>"], "Adds search and replace functionality across posts, pages, comments, and meta-data, with full regular expression support": ["Añade la funcionalidad de búsqueda y reemplazo en entradas, páginas, comentarios y metadatos, con un soporte completo de expresiones regulares."], "https://searchregex.com/": ["https://searchregex.com/"], "Search Regex": ["Search Regex"]}}, "translation-revision-date": "2024-01-01T17:14:07.393Z", "source": "search-regex", "domain": "search-regex", "generator": "Search Regex"}