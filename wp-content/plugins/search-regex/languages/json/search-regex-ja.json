{"locale_data": {"search-regex": {"": {"plural-forms": "nplurals=1; plural=0;"}, "matched rows = %(searched)s": [], "OK": ["OK"], "Version %s installed! Please read the {{url}}release notes{{/url}} for details.": ["バージョン%sがインストールされました! 詳細は、{{url}}リリースノート{{/url}}をご覧ください。"], "A problem occurred with your last query. This is likely caused by a combination of search filters that haven't been handled properly.": ["最後のクエリで問題が発生しました。原因は、検索絞り込みの組み合わせが適切に処理されていないことのようです。"], "Query Problem": ["クエリの問題"], "Progress": ["進行状況"], "%s row deleted.": ["%s rows deleted.", ["%s 行が削除されました。"]], "Refresh": ["更新"], "Matched Content": ["一致したコンテンツ"], "Your search resulted in too many requests. Please narrow your search terms.": ["検索結果が多すぎます。検索条件を絞り込んでください。"], "Your search conditions have changed. Please refresh to see the latest results.": ["検索条件が変更されました。最新の結果を表示するには、更新してください。"], "Matched rows: %(matches)s out of %(total)s total.": ["一致した行: 全 %(total)s個中 %(matches)s 個"], "Delete database row": ["このデータベース行を削除"], "View": ["表示"], "No title": ["タイトルなし"], "Empty value": ["値が空白です"], "No value": ["値なし"], "Click to replace match": ["クリックで一致箇所を置換"], "Contains encoded data": ["エンコードされたデータを含む"], "New meta value": ["新しいメタ値"], "New meta key": ["新しいメタキー"], "Apply to matches only": ["一致のみ適用"], "Enter replacement": ["置換文字列を入力"], "Click to replace column": ["クリックで列を置換"], "This column contains special formatting. Modifying it could break the format.": ["この列には特別な書式が含まれています。これを変更すると、体裁が崩れる可能性があります。"], "HTML": ["HTML"], "Blocks": [], "Serialized PHP": ["シリアル化された PHP"], "Paste preset JSON.": ["プリセット用の JSON を貼り付け"], "Global Search Flags": ["全体検索フラグ"], "Global Replace": ["全体置換"], "Global Search": ["全体検索"], "View Columns": ["列を表示"], "Sources": ["入力"], "Only include selected columns": ["選択した列のみを含める"], "WordPress Action": ["WordPress のアクション"], "SQL": ["SQL"], "CSV": ["CSV"], "JSON": ["JSON"], "Export Format": ["エクスポート形式"], "Run a WordPress action for each matching result.": ["一致したそれぞれの結果に対して WordPress のアクションを実行。"], "Run Action": ["アクションを実行"], "Delete matching results.": ["一致結果の記事を削除します。"], "Delete Matches": ["一致項目を削除"], "Export matching results to JSON, CSV, or SQL.": ["一致結果を JSON, CSV, SQL でエクスポート"], "Export Matches": ["一致項目をエクスポート"], "Perform changes to specific values of the matching results.": ["一致結果中の特定の値を変更する。"], "Modify Matches": ["一致項目を修正"], "Replace the global search values.": ["全体検索の値を置換する。"], "Global Text Replace": ["全体の文字列置換"], "Just show matching results.": ["一致結果をただ表示します。"], "No action": ["操作なし"], "Enter replacement value": ["置換用の文字列を入力"], "Matched values will be removed": ["一致した値は除去されます"], "Replace \"%1s\"": ["置換 「%1s」"], "Year": ["年"], "Months": ["月"], "Weeks": ["週"], "Days": ["日"], "Hours": ["時間"], "Minutes": ["分"], "Seconds": ["秒"], "Decrement": ["減少"], "Increment": ["増加"], "Set Value": ["値を指定"], "Replace With": ["これと置換"], "Add": ["追加"], "Column": ["列"], "AND": ["AND"], "Filters": ["絞り込み"], "Add sub-filter (OR)": ["絞り込み条件の追加 (OR)"], "OR": ["OR"], "All": ["すべて"], "between {{first/}} and {{second/}}": ["{{first/}} から {{second/}} の間"], "No Owner": ["所有者なし"], "Has Owner": ["所有者あり"], "Any": ["の指定なし"], "Excludes any": ["に含まない"], "Includes any": ["に含む"], "End": ["が以下で終わる"], "Begins": ["が以下で始まる"], "Not contains": ["に含まない"], "Contains": ["に含む"], "Range": ["の範囲"], "Less": ["が小さい"], "Greater": ["が大きい"], "Not Equals": ["が等しくない"], "Equals": ["が等しい"], "Optional global search phrase. Leave blank to use filters only.": ["全体検索用の語句。空白にすると、絞り込みだけ行います。"], "REST API 404": ["REST API 404"], "Your REST API appears to be cached and this will cause problems. Please exclude your REST API from your caching system.": ["お使いの REST API はキャッシュされているようで、このことが問題を引き起こしています。REST API を利用中のキャッシュのシステムから除外してください。"], "Your REST API is showing a deprecated PHP error. Please fix this error.": ["お使いの REST API は、非推奨になっている PHP のエラーを出しています。このエラーを修正してください。"], "Your server configuration is blocking access to the REST API.": ["このサーバーの設定が REST API へのアクセスをブロックしています。"], "Can you access your {{api}}REST API{{/api}} without it redirecting?.": ["リダイレクトなしでお使いの {{api}}REST API{{/api}} にアクセスできますか ?"], "You will will need to fix this on your site. Redirection is not causing the error.": ["このサイト上のこれを修正する必要があります。リダイレクトはエラーの原因ではありません。"], "Preset uploaded": ["プリセットはアップロードされました"], "Multiline": ["複数行"], "Case": ["大/小なし"], "Regex": ["正規表現"], "Nginx": ["<PERSON><PERSON><PERSON>"], "Apache": ["Apache"], "WordPress": ["WordPress"], "Module": ["モジュール"], "Redirection Groups": ["リダイレクトのグループ"], "Not accessed": ["アクセスなし"], "Target": ["対象"], "HTTP Code": ["HTTP コード"], "Position": ["配置"], "Disabled": ["無効"], "Enabled": ["有効"], "Status": ["状態"], "Group": ["グループ"], "Last Access": ["最終アクセス"], "Hit Count": ["ヒット数"], "Source URL (matching)": [], "Source URL": [], "User meta": ["ユーザーメタ情報"], "Registered": ["登録日時"], "Login": ["ログイン"], "Taxonomy": ["タクソノミー"], "Post Tag": ["投稿タグ"], "Post Category": ["投稿カテゴリー"], "Comment Count": ["コメント数"], "MIME": ["MIME"], "Parent": ["親"], "Modified GMT": ["更新日時 (GMT)"], "Modified": ["更新日時"], "Password": ["パスワード"], "Has no password": ["パスワードなし"], "Has password": ["パスワードあり"], "Ping Status": ["PING の状態"], "Comment Status": ["コメントの状態"], "Open": ["開放"], "Post Status": ["投稿状態"], "Post Type": ["投稿タイプ"], "Autoload": ["Autoload"], "Is not autoload": ["autoload ではない"], "Is autoload": ["autoload です"], "Meta Value": ["メタ値"], "Meta Key": ["メタキー"], "Owner ID": [], "Comment meta": ["コメントメタ情報"], "User ID": ["ユーザー ID"], "Parent Comment": ["親コメント"], "Trackback": ["トラックバック"], "Pingback": ["ピンバック"], "Type": ["種類"], "User Agent": ["ユーザーエージェント"], "Approval Status": ["承認状態"], "Spam": ["スパム"], "Approved": ["承認済み"], "Unapproved": ["承認待ち"], "Date GMT": ["日時 (GMT)"], "Date": ["日時"], "IP": ["IP"], "Author": ["投稿者"], "Post ID": ["投稿 ID"], "ID": ["ID"], "Terms": ["用語"], "Posts (core & custom)": ["投稿 (コア & カスタム)"], "Please review your data and try again.": ["データを見直し再度お試しください。"], "There was a problem making a request to your site. This could indicate you provided data that did not match requirements, or that the plugin sent a bad request.": ["このサイトへのリクエストに問題がありました。これは、要件に一致しないデータを提供したか、プラグインが不正なリクエストを送信したことを示しています。"], "Bad data": ["不正なデータ"], "Your WordPress REST API is returning a 404 page. This is almost certainly an external plugin or server configuration issue.": ["WordPress REST API が 404 ページを返しています。これは、外部のプラグインまたはサーバー設定の問題であることがほとんどです。"], "2000 per page": ["2000件 / ページ"], "1000 per page": ["1000件 / ページ"], "Set a preset to use by default when Search Regex is loaded.": ["Search Regex が読み込まれたら標準で使用するプリセットを設定します。"], "No default preset": ["標準のプリセットなし"], "Default Preset": ["標準のプリセット"], "Your admin pages are being cached. Clear this cache and try again. There may be multiple caches involved.": ["管理画面のページがキャッシュされています。このキャッシュを消去し再度お試しください。複数のキャッシュが関与している可能性があります。"], "This is usually fixed by doing one of the following:": ["これは、以下のいずれかを行うことで通常は解決されます:"], "You are using an old or cached session": ["古いか、キャッシュされたセッションを使用中です"], "Debug Information": ["デバッグ情報"], "Show debug": ["デバッグを表示"], "WordPress returned an unexpected message. This could be a PHP error from another plugin, or data inserted by your theme.": ["WordPress が予期せぬメッセージを返しました。これは、他のプラグインによる PHP エラー、または利用中のテーマによって挿入されたデータの可能性があります。"], "Your WordPress REST API has been disabled. You will need to enable it to continue.": ["WordPress REST API が無効になっています。 続行するには有効化する必要があります。"], "Your REST API is being redirected. Please remove the redirection for the API.": ["お使いの REST API はリダイレクトされています。API のリダイレクトを削除してください。"], "A security plugin or firewall is blocking access. You will need to whitelist the REST API.": ["セキュリティ用のプラグインかファイアーウォールがアクセスを遮断しています。REST API をホワイトリストに登録する必要があります。"], "Check your {{link}}Site Health{{/link}} and fix any issues.": ["{{link}}サイトヘルス{{/link}}を確認し、問題を修正してください。"], "Preset": ["プリセット"], "Upload": ["アップロード"], "Add file": ["ファイルの追加"], "Preset saved": ["プリセットを保存しました"], "Copy to clipboard": ["クリップボードにコピー"], "Are you sure you want to delete this preset?": ["このプリセットを削除しますか ?"], "Locked fields": ["入力欄のロック"], "For example, create tag {{code}}URL{{/code}} and title {{code}}Image URL{{/code}}. Your search could be {{code}}<img src=\"URL\">{{/code}}. When the preset is used it will ask the user for the {{code}}Image URL{{/code}} instead of the full search phrase.": ["例: タイトル {{{code}}URL{{/code}}} とタグ {{{code}}Image URL{/code}} を作成します。検索では {{{code}}<img src=\"URL\">{{/code}}} とすることができます。プリセットを使うと、完全な検索語句の代わりに {{{code}}Image URL{{/code}}} をユーザーに要求します。"], "A tag creates a custom input field. Insert the tag anywhere in the search, replace, text filter, or text action and when the preset is used it will be replaced with a custom text field with the tag label.": ["タグは、独自の入力欄を作成します。検索か置換、文字列絞り込み、または文字列操作の好きな位置にタグを挿入し、プリセットを使用すると、タグラベルの独自のテキストに置き換えられます。"], "Enter tag which is used in the field": ["入力欄で使うタグを入力"], "Tag": ["タグ"], "Enter tag title shown to user": ["ユーザーに表示されるタグのタイトルを入力"], "Tags": ["タグ"], "Locking a field removes it from the search form and prevents changes.": ["入力欄をロックすると、検索フォームから入力が消え変更を防止できます。"], "Fields": ["入力欄"], "Locked Fields": ["入力欄のロック"], "Advanced preset": ["高度なプリセット"], "Describe the preset": ["プリセットを説明します"], "Preset Description": ["プリセットの説明"], "Give the preset a name": ["プリセットに名前をつけてください"], "Preset Name": ["プリセット名"], "Edit preset": ["プリセットの編集"], "Results per page": ["ページあたりの結果表示数"], "remove phrase": ["語句の削除"], "no phrase": ["語句なし"], "Import": ["インポート"], "Please check your JSON data is a valid preset. You may have copied it incorrectly, or pasted something that is not a preset.": ["JSON のデータは正しいプリセットかを、ご確認ください。不完全なコピーや、プリセット以外のデータを貼り付けした可能性があります。"], "Unable to import preset": ["プリセットをインポートできません"], "Import preset from clipboard": ["クリップボードからプリセットをインポート"], "Done": ["完了"], "Uploaded %(total)d preset": ["Uploaded %(total)d presets", ["計%(total)d個のプリセットをアップロードしました"]], "Importing": ["インポート中"], "File selected": ["選択されたファイル"], "Click 'Add File' or drag and drop here.": ["「ファイルの追加」をクリック、またはここへドロップ。"], "Import a JSON file": ["JSON ファイルのインポート"], "Import JSON": ["JSON のインポート"], "Export JSON": ["JSON のエクスポート"], "Download presets!": ["プリセットのダウンロード !"], "There are no presets": ["プリセットはありません"], "Flags": ["フラグ"], "Presets": ["プリセット"], "If that did not help then {{strong}}create an issue{{/strong}} or send it in an {{strong}}email{{/strong}}.": ["解決しない場合、{{strong}}問題 (issue) のトピックを作成するか{{/strong}}、{{strong}}メール{{/strong}}を送信してください。"], "Please check the {{link}}support site{{/link}} before proceeding further.": ["先に進める前に、{{link}}サポート用サイト{{/link}}をご確認ください。"], "Enter preset name": ["プリセット名を入力"], "Enter a name for your preset": ["プリセットの名前を入力"], "Saving Preset": ["プリセットの保存"], "Update current preset": ["現在のプリセットを更新"], "Save search as new preset": ["検索を新規プリセットとして保存"], "No preset": ["プリセットなし"], "Saving preset": ["プリセットの保存"], "Advanced": ["高度"], "Standard": ["標準"], "Please backup your data before making modifications.": ["変更を加える前に、データをバックアップすることを強くおすすめします。"], "%s row.": ["%s rows.", ["%s行。"]], "An unknown error occurred.": ["不明なエラーが発生しました。"], "GUID": ["GUID"], "Slug": ["スラッグ"], "Excerpt": ["抜粋"], "Content": ["本文"], "Display Name": ["表示名"], "Nicename": ["ナイスネーム"], "Value": ["値"], "Comment": ["コメント"], "Name": ["名前"], "Title": ["タイトル"], "URL": ["URL"], "Disabled! Detected PHP %1$s, need PHP %2$s+": ["無効です。PHP %1$s が検出されました。PHP %2$s+ が必要です"], "Plugins": ["プラグイン"], "WordPress Options": ["WordPress 設定"], "User Meta": ["ユーザーのメタ情報"], "Users": ["ユーザー"], "Comment Meta": ["コメントメタ情報"], "Comments": ["コメント"], "Post Meta": ["投稿メタ情報"], "Please enable JavaScript": ["JavaScript を有効にしてください"], "Loading, please wait...": ["読み込み中、少々お待ちください..."], "Create Issue": ["問題を報告"], "<code>SearchRegexi10n</code> is not defined. This usually means another plugin is blocking Search Regex from loading. Please disable all plugins and try again.": ["<code>SearchRegexi10n</code> が定義されていません。別のプラグインが Search Regex の機能をブロックしているようです。一度すべてのプラグインを無効にしてから、もう一度お試しください。"], "If you think Search Regex is at fault then create an issue.": ["Search Regex に問題がある場合は、問題を報告してください。"], "Please see the <a href=\"https://searchregex.com/support/problems/\">list of common problems</a>.": ["お手数ですが<a href=\"https://searchregex.com/support/problems/\">開発者のウェブサイト</a>をご覧ください。"], "Please note that Search Regex requires the WordPress REST API to be enabled. If you have disabled this then you won't be able to use Search Regex": ["Search Regex では、WordPress の REST API を 有効にする必要があります。無効にすると、Search Regex を使用できません"], "Also check if your browser is able to load <code>search-regex.js</code>:": ["また使用中のブラウザが <code>search-regex.js</code> を読み込み可能か確認してください:"], "This may be caused by another plugin - look at your browser's error console for more details.": ["この問題は、使用中の別のプラグインが原因である可能性があります。詳細はブラウザのエラーコンソールを確認してください。"], "Unable to load Search Regex ☹️": ["Search Regex を読み込めません☹️"], "Unable to load Search Regex": ["Search Regex を読み込めません"], "Search Regex requires WordPress v%1$1s, you are using v%2$2s - please update your WordPress": ["Search Regex には WordPress v%1$1s が必要です。このサイトでは v%2$2s を使用しています。WordPress 本体を更新してください"], "Search Regex Support": ["Search Regex ヘルプ"], "You can find full documentation about using Search Regex on the <a href=\"%s\" target=\"_blank\">searchregex.com</a> support site.": ["Search Regex の完全版マニュアルは、<a href=\"%s\" target=\"_blank\">searchregex.com</a> にあります。"], "Settings": ["設定"], "Action": ["操作"], "Row ID": ["行の ID"], "No more matching results found.": ["これ以上の一致結果はありません。"], "Last page": ["最後のページ"], "Page %(current)s of %(total)s": ["%(total)s ページ中 %(current)s ページ"], "Next page": ["次のページ"], "Progress %(current)s%%": ["進行状況 %(current)s%%"], "Prev page": ["前のページ"], "First page": ["最初のページ"], "%s database row in total": ["%s database rows in total", ["合計 %s データベース行"]], "500 per page": ["500件 / ページ"], "250 per page": ["250件 / ページ"], "100 per page": ["100件 / ページ"], "50 per page": ["50件 / ページ "], "25 per page": ["25件 / ページ "], "Ignore Case": ["大文字小文字の区別なし"], "Regular Expression": ["正規表現"], "Row updated": ["行の更新"], "Row replaced": ["行の入れ替え"], "Row deleted": ["行の削除"], "Settings saved": ["設定を保存"], "{{link}}Source Flags{{/link}} - additional options for the selected source. For example, include post {{guid}}GUID{{/guid}} in the search.": ["{{link}}検索フラグ{{/link}} - 選択した入力元用の追加のオプションです。たとえば、検索に投稿 {{guid}}GUID{{/guid}} を含めます。"], "{{link}}Source{{/link}} - the source of data you wish to search. For example, posts, pages, or comments.": ["{{link}}入力元{{/link}} - 検索したいデータの入力元です。投稿、ページ、コメントなどです。"], "{{link}}Regular expression{{/link}} - a way of defining a pattern for text matching. Provides more advanced matches.": ["{{link}}正規表現{{/link}} - 文字列一致のパターンを定義する記法です。より高度な検索が可能です。"], "{{link}}Search Flags{{/link}} - additional qualifiers for your search, to enable case insensitivity, and to enable regular expression support.": ["{{link}}検索フラグ{{/link}} - 検索を修飾します。大文字小文字を区別しない、また正規表現の対応を有効にします。"], "The following concepts are used by Search Regex:": ["Search Regex では以下の概念が採用されています:"], "Like this plugin? You might want to consider {{link}}Redirection{{/link}}, a plugin to manage redirects, from the same author.": ["本プラグインを気に入っていただけたでしょうか ? よければ開発者のほかのプラグイン {{link}}Redirection{{/link}} もご利用ください。"], "Redirection": ["Redirection"], "If you want to submit information that you don't want in a public repository then send it directly via {{email}}email{{/email}} - include as much information as you can!": ["不要な情報を公開リポジトリに送信する場合は、{{email}}メール{{/email}}から直接送信できます。できるだけ多くの情報を含めてください。"], "Please note that any support is provide on as-time-is-available basis and is not guaranteed. I do not provide paid support.": ["できる限りサポートしますが、限界があることを了承ください。有料サポートはありません。"], "If you want to report a bug please read the {{report}}Reporting Bugs{{/report}} guide.": ["バグ報告を行う場合、{{report}}Reporting Bugs{{/report}} の手引きをお読みください。"], "Full documentation for Search Regex can be found at {{site}}https://searchregex.com{{/site}}.": ["Search Regex に関する包括的な説明文書は、{{site}}https://searchregex.com{{/site}} にあります。"], "Need more help?": ["サポートが必要ですか ?"], "Results": ["結果表示数"], "Source": ["入力"], "Enter search phrase": ["検索語句を入力"], "Replace All": ["すべて置換"], "Search": ["検索"], "Search and replace information in your database.": ["使用しているデータベースの情報を検索し置換します。"], "Update": ["更新"], "How Search Regex uses the REST API - don't change unless necessary": ["Search Regex による REST API の利用方法 - 必要なく変更しないよう"], "REST API": ["REST API"], "I'm a nice person and I have helped support the author of this plugin": ["すでにこのプラグインの作者を支援しました"], "Relative REST API": ["相対的な REST API"], "Raw REST API": ["そのままの REST API"], "Default REST API": ["標準の REST API"], "Plugin Support": ["プラグインの支援"], "Support 💰": ["支援💰"], "You get useful software and I get to carry on making it better.": ["私はこのプラグインをもっと便利にしていきます。"], "Search Regex is free to use - life is wonderful and lovely! It has required a great deal of time and effort to develop and you can help support this development by {{strong}}making a small donation{{/strong}}.": ["Search Regex は無料で使用できます。人生はすばらしくてステキです ! 開発には多大な時間と労力が必要でしたが、{{strong}}少額の寄付{{/strong}}でこの開発を支援することができます。"], "I'd like to support some more.": ["もっと支援したいです。"], "You've supported this plugin - thank you!": ["このプラグインを支援していただき、ありがとうございます。"], "If that doesn't help, open your browser's error console and create a {{link}}new issue{{/link}} with the details.": ["問題が解決しない場合は、ブラウザのエラーコンソールを開き、詳細を記載した {{link}}new issue{{/link}} を作成してください。"], "If you are using a page caching plugin or service (CloudFlare, OVH, etc) then you can also try clearing that cache.": ["キャッシュプラグインまたは CDN サービス (CloudFlare、OVHなど) を使用している場合は、そのキャッシュを削除すると解決することがあります。"], "Search Regex is not working. Try clearing your browser cache and reloading this page.": ["Search Regex が機能していません。ブラウザのキャッシュをクリアして、このページをリロードしてみてください。"], "clearing your cache.": ["キャッシュを削除しています。"], "If you are using a caching system such as Cloudflare then please read this: ": ["Cloudflare などの CDN キャッシュを使用している場合は、次の項目をお読みください: "], "Please clear your browser cache and reload this page.": ["ブラウザのキャッシュをクリアして、このページをリロードしてください。"], "Cached Search Regex detected": ["キャッシュされた正規表現が検出されました"], "Show %s more": ["Show %s more", ["さらに%s件表示"]], "Maximum number of matches exceeded and hidden from view. These will be included in any replacements.": ["一致の上限数を超えたので結果は表示されません。これらはすべての置換に含まれます。"], "Delete": ["削除"], "Edit": ["編集"], "Check Again": ["もう一度確認する"], "Testing - %s%%": ["テスト中 - %s%%"], "Show Problems": ["問題を表示"], "Summary": ["概要"], "You are using a broken REST API route. Changing to a working API should fix the problem.": ["壊れた REST API ルートが使用されています。問題の解決には、機能している API に変更する必要があります。"], "Your REST API is not working and the plugin will not be able to continue until this is fixed.": ["お使いの REST API が機能していません。これが修正されるまで本プラグインは続行できません。"], "There are some problems connecting to your REST API. It is not necessary to fix these problems and the plugin is able to work.": ["REST API への接続にいくつかの問題がありますが、これらの問題の修正は必須ではなく、プラグインは機能します。"], "Unavailable": ["利用できません"], "Not working but fixable": ["機能していないが修正可能"], "Working but some issues": ["機能中だが問題あり"], "Good": ["良好"], "Current API": ["現在の API"], "Switch to this API": ["この API に切り替え"], "Hide": ["非表示"], "Show Full": ["すべて表示"], "Working!": ["稼働中"], "Finished!": ["完了 !"], "Cancel": ["キャンセル"], "Replace": ["置換"], "Remove": ["削除"], "Multi": ["複数行"], "Single": ["単一行"], "Support": ["ヘルプ"], "Options": ["設定"], "Search & Replace": ["検索と置換"], "If you are using WordPress 5.2 or newer then look at your {{link}}Site Health{{/link}} and resolve any issues.": ["WordPress 5.2以降を使用している場合は、{{link}}サイトヘルス{{/link}}を確認して問題を解決してください。"], "{{link}}Please temporarily disable other plugins!{{/link}} This fixes so many problems.": ["{{link}}一時的に他のプラグインを無効にしてください ! {{/link}}これにより、いくつかの問題が修正されるはずです。"], "{{link}}Caching software{{/link}}, in particular Cloudflare, can cache the wrong thing. Try clearing all your caches.": ["{{link}}キャッシュのソフトウェア{{/link}}、特に Cloudflare は、間違ったものをキャッシュする可能性があります。一度すべてのキャッシュを削除してみてください。"], "What do I do next?": ["次はどうしますか？"], "Something went wrong 🙁": ["何かがうまくいきません 🙁"], "Log out, clear your browser cache, and log in again - your browser has cached an old session.": ["ログアウトし、ブラウザのキャッシュをクリアして、再度ログインしてください。ブラウザは古いセッションをキャッシュしています。"], "Reload the page - your current session is old.": ["ページを再読み込みしてください - 現在のセッションは古いです。"], "Include these details in your report along with a description of what you were doing and a screenshot.": ["これらの問題の詳細を、実行内容の説明とスクリーンショットをあわせて報告してください。"], "Email": ["メールアドレス"], "Create An Issue": ["問題を報告"], "Closed": ["閉鎖"], "Save": ["保存"], "Unable to make request due to browser security. This is typically because your WordPress and Site URL settings are inconsistent, or the request was blocked by your site CORS policy.": ["ブラウザのセキュリティにより、リクエストを実行できません。これは通常、WordPress とサイトの URL 設定に一貫性がないか、リクエストがサイトの CORS ポリシーによってブロックされたことが原因です。"], "Possible cause": ["考えられる原因"], "This could be a security plugin, or your server is out of memory or has an external error. Please check your server error log": ["これはセキュリティのプラグインが原因か、サーバーのメモリの不足、または外部エラーが発生しています。ご契約のレンタルサーバーなどのエラーログを確認してください"], "Your server has rejected the request for being too big. You will need to reconfigure it to continue.": ["リクエストが大きすぎるためサーバーに拒否されました。続行するには再設定が必要です。"], "Your REST API is probably being blocked by a security plugin. Please disable this, or configure it to allow REST API requests.": ["REST API がセキュリティプラグインによってブロックされている可能性があります。これを無効にするか、REST API リクエストを許可するように設定してください。"], "Read this REST API guide for more information.": ["詳細は、この REST API ガイドをお読みください。"], "WordPress did not return a response. This could mean an error occurred or that the request was blocked. Please check your server error_log.": ["WordPress は応答を返しませんでした。これは、エラーが発生したか、要求がブロックされたことを意味します。サーバーのエラーログを確認してください。"], "John Godley": ["<PERSON>"], "Adds search and replace functionality across posts, pages, comments, and meta-data, with full regular expression support": ["投稿、ページ、コメント、メタデータにわたって、正規表現に完全に対応した検索と置換機能を追加します"], "https://searchregex.com/": ["https://searchregex.com/"], "Search Regex": ["Search Regex"]}}, "translation-revision-date": "2024-01-01T17:14:07.407Z", "source": "search-regex", "domain": "search-regex", "generator": "Search Regex"}