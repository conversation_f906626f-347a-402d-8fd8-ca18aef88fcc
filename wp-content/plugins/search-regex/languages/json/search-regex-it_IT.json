{"locale_data": {"search-regex": {"": {"plural-forms": "nplurals=2; plural=n != 1;"}, "matched rows = %(searched)s": [], "OK": [], "Version %s installed! Please read the {{url}}release notes{{/url}} for details.": [], "A problem occurred with your last query. This is likely caused by a combination of search filters that haven't been handled properly.": [], "Query Problem": [], "Progress": [], "%s row deleted.": ["%s rows deleted."], "Refresh": [], "Matched Content": [], "Your search resulted in too many requests. Please narrow your search terms.": [], "Your search conditions have changed. Please refresh to see the latest results.": [], "Matched rows: %(matches)s out of %(total)s total.": [], "Delete database row": [], "View": [], "No title": [], "Empty value": [], "No value": [], "Click to replace match": [], "Contains encoded data": [], "New meta value": [], "New meta key": [], "Apply to matches only": [], "Enter replacement": [], "Click to replace column": [], "This column contains special formatting. Modifying it could break the format.": [], "HTML": [], "Blocks": [], "Serialized PHP": [], "Paste preset JSON.": [], "Global Search Flags": [], "Global Replace": [], "Global Search": [], "View Columns": [], "Sources": [], "Only include selected columns": [], "WordPress Action": [], "SQL": [], "CSV": [], "JSON": [], "Export Format": [], "Run a WordPress action for each matching result.": [], "Run Action": [], "Delete matching results.": [], "Delete Matches": [], "Export matching results to JSON, CSV, or SQL.": [], "Export Matches": [], "Perform changes to specific values of the matching results.": [], "Modify Matches": [], "Replace the global search values.": [], "Global Text Replace": [], "Just show matching results.": [], "No action": [], "Enter replacement value": [], "Matched values will be removed": [], "Replace \"%1s\"": [], "Year": [], "Months": [], "Weeks": [], "Days": [], "Hours": [], "Minutes": [], "Seconds": [], "Decrement": [], "Increment": [], "Set Value": [], "Replace With": [], "Add": [], "Column": [], "AND": [], "Filters": [], "Add sub-filter (OR)": [], "OR": [], "All": [], "between {{first/}} and {{second/}}": [], "No Owner": [], "Has Owner": [], "Any": [], "Excludes any": [], "Includes any": [], "End": [], "Begins": [], "Not contains": [], "Contains": [], "Range": [], "Less": [], "Greater": [], "Not Equals": [], "Equals": [], "Optional global search phrase. Leave blank to use filters only.": [], "REST API 404": [], "Your REST API appears to be cached and this will cause problems. Please exclude your REST API from your caching system.": [], "Your REST API is showing a deprecated PHP error. Please fix this error.": [], "Your server configuration is blocking access to the REST API.": [], "Can you access your {{api}}REST API{{/api}} without it redirecting?.": [], "You will will need to fix this on your site. Redirection is not causing the error.": [], "Preset uploaded": [], "Multiline": [], "Case": [], "Regex": [], "Nginx": [], "Apache": [], "WordPress": [], "Module": [], "Redirection Groups": [], "Not accessed": [], "Target": [], "HTTP Code": [], "Position": [], "Disabled": [], "Enabled": [], "Status": [], "Group": [], "Last Access": [], "Hit Count": [], "Source URL (matching)": [], "Source URL": [], "User meta": [], "Registered": [], "Login": [], "Taxonomy": [], "Post Tag": [], "Post Category": [], "Comment Count": [], "MIME": [], "Parent": [], "Modified GMT": [], "Modified": [], "Password": [], "Has no password": [], "Has password": [], "Ping Status": [], "Comment Status": [], "Open": [], "Post Status": [], "Post Type": [], "Autoload": [], "Is not autoload": [], "Is autoload": [], "Meta Value": [], "Meta Key": [], "Owner ID": [], "Comment meta": [], "User ID": [], "Parent Comment": [], "Trackback": [], "Pingback": [], "Type": [], "User Agent": [], "Approval Status": [], "Spam": [], "Approved": [], "Unapproved": [], "Date GMT": [], "Date": [], "IP": [], "Author": [], "Post ID": [], "ID": [], "Terms": [], "Posts (core & custom)": [], "Please review your data and try again.": ["Controlla i dati e prova di nuovo."], "There was a problem making a request to your site. This could indicate you provided data that did not match requirements, or that the plugin sent a bad request.": ["Si è verificato un problema nel fare una richiesta al tuo sito. Forse hai fornito dei dati diversi da quelli richiesti oppure il plugin ha inviato una richiesta errata."], "Bad data": ["<PERSON><PERSON> errati"], "Your WordPress REST API is returning a 404 page. This is almost certainly an external plugin or server configuration issue.": ["La REST API restituisce una pagina 404. Molto probabilmente è un problema generato da un plugin esterno o dalla configurazione del server."], "2000 per page": ["2000 per pagina"], "1000 per page": ["1000 per pagina"], "Set a preset to use by default when Search Regex is loaded.": ["Imposta un preset da usare in modo predefinito quando Search Regex viene caricato."], "No default preset": ["<PERSON><PERSON><PERSON> preset predefinito"], "Default Preset": ["Preset predefinito"], "Your admin pages are being cached. Clear this cache and try again. There may be multiple caches involved.": ["Le tue pagine di amministrazione sono in cache. Svuota questa cache e riprova. Potrebbero essere attive più cache."], "This is usually fixed by doing one of the following:": ["Ciò di solito si corregge facendo una di queste cose:"], "You are using an old or cached session": ["Stai usando una sessione vecchia o in cache"], "Debug Information": ["Informazioni di debug"], "Show debug": ["Mostra il debug"], "WordPress returned an unexpected message. This could be a PHP error from another plugin, or data inserted by your theme.": ["WordPress ha restituito un messaggio inatteso. Potrebbe essere dovuto a un errore PHP di un plugin oppure ai dati inseriti dal tuo tema."], "Your WordPress REST API has been disabled. You will need to enable it to continue.": ["La tua REST API di WordPress è stata disabilitata. Devi abilitarla per continuare."], "Your REST API is being redirected. Please remove the redirection for the API.": ["La tua REST API viene reindirizzata. Rimuovi il rendirizzamento per la API."], "A security plugin or firewall is blocking access. You will need to whitelist the REST API.": ["Un plugin di sicurezza o il firewall sta bloccando l'accesso. Devi aggiungere la REST API in whitelist."], "Check your {{link}}Site Health{{/link}} and fix any issues.": ["Controlla il tuo {{link}}Salute del sito (Site Health){{/link}} e correggi i problemi."], "Preset": ["Preset"], "Upload": ["Upload"], "Add file": ["Aggiungi un file"], "Preset saved": ["Preset salvato"], "Copy to clipboard": ["Copia negli appunti"], "Are you sure you want to delete this preset?": ["Sei sicuro di voler eliminare questo preset?"], "Locked fields": ["<PERSON><PERSON> b<PERSON>i"], "For example, create tag {{code}}URL{{/code}} and title {{code}}Image URL{{/code}}. Your search could be {{code}}<img src=\"URL\">{{/code}}. When the preset is used it will ask the user for the {{code}}Image URL{{/code}} instead of the full search phrase.": ["Per esempio, crea il tag {{code}}URL{{/code}} e il titolo {{code}}URL immagine{{/code}}. La tua ricerca potrebbe essere {{code}}<img src=\"URL\">{{/code}}. Quando il preset viene usato, chiederà all'utente la {{code}}URL immagine{{/code}} invece dell'intera frase di ricerca."], "A tag creates a custom input field. Insert the tag anywhere in the search, replace, text filter, or text action and when the preset is used it will be replaced with a custom text field with the tag label.": ["Un tag crea un campo di inserimento personalizzato. Inserisci il tag dove preferisci nel campo di ricerca o di sostituzione e, quando il preset viene usato, sarà sostituito con un campo di testo personalizzato con l'etichetta del tag."], "Enter tag which is used in the field": ["Inserisci il tag usato nel campo"], "Tag": ["Tag"], "Enter tag title shown to user": ["Inserisci il titolo del tag mostrato all'utente"], "Tags": ["Tag"], "Locking a field removes it from the search form and prevents changes.": ["Bloccare un campo lo rimuove dal modulo di ricerca e impedisce i cambiamenti."], "Fields": ["<PERSON><PERSON>"], "Locked Fields": ["<PERSON><PERSON> b<PERSON>i"], "Advanced preset": ["Preset avan<PERSON>"], "Describe the preset": ["Descrivi il preset"], "Preset Description": ["Descrizione del preset"], "Give the preset a name": ["Dai un nome al preset"], "Preset Name": ["Nome del preset"], "Edit preset": ["Modifica il preset"], "Results per page": ["Risultati per pagina"], "remove phrase": ["rimuovi la frase"], "no phrase": ["nessuna frase"], "Import": ["Importa"], "Please check your JSON data is a valid preset. You may have copied it incorrectly, or pasted something that is not a preset.": ["Controlla che i dati del tuo JSON siano un valido preset. Po<PERSON>ti averlo copiato male o aver incollato qualcosa che non è un preset."], "Unable to import preset": ["Impossibile importare il preset"], "Import preset from clipboard": ["Importa il preset dagli appunti"], "Done": ["<PERSON><PERSON>"], "Uploaded %(total)d preset": ["Uploaded %(total)d presets", "Caricato %(total)d preset", "Caricati %(total)d preset"], "Importing": ["Importazione in corso"], "File selected": ["File selezionato"], "Click 'Add File' or drag and drop here.": ["Fai clic su 'Aggiungi file' o trascinalo qui."], "Import a JSON file": ["Importa un file JSON"], "Import JSON": ["Importa JSON"], "Export JSON": ["Esporta JSON"], "Download presets!": ["Scarica i preset!"], "There are no presets": ["Non ci sono preset"], "Flags": ["Flag"], "Presets": ["Preset"], "If that did not help then {{strong}}create an issue{{/strong}} or send it in an {{strong}}email{{/strong}}.": ["Se non è stato di aiuto, allora {{strong}}apri un ticket{{/strong}} or invialo in una {{strong}}email{{/strong}}."], "Please check the {{link}}support site{{/link}} before proceeding further.": ["Vai al {{link}}sito di supporto{{/link}} prima di procedere oltre."], "Enter preset name": ["Inserisci il nome del preset"], "Enter a name for your preset": ["Inserisci un nome per il tuo preset"], "Saving Preset": ["Salvataggio del preset"], "Update current preset": ["Aggiorna il preset corrente"], "Save search as new preset": ["Salva la ricerca come nuovo preset"], "No preset": ["Non ci sono preset"], "Saving preset": ["Salvataggio del preset"], "Advanced": ["<PERSON><PERSON><PERSON>"], "Standard": ["Standard"], "Please backup your data before making modifications.": ["Fai un backup dei tuoi dati prima di apportare modifiche."], "%s row.": ["%s rows.", "%s riga.", "%s righe."], "An unknown error occurred.": ["Si è verificato un errore sconosciuto."], "GUID": ["GUID"], "Slug": ["Slug"], "Excerpt": ["<PERSON><PERSON><PERSON><PERSON>"], "Content": ["<PERSON><PERSON><PERSON>"], "Display Name": ["Nome visualiz<PERSON>"], "Nicename": ["Nicename"], "Value": ["Valore"], "Comment": ["Commento"], "Name": ["Nome"], "Title": ["<PERSON><PERSON>"], "URL": ["URL"], "Disabled! Detected PHP %1$s, need PHP %2$s+": ["Disabilitato! Rilevato PHP %1$s, necessario PHP %2$s+"], "Plugins": ["Plugin"], "WordPress Options": ["Opzioni di WordPress"], "User Meta": ["Metadati utente"], "Users": ["<PERSON><PERSON><PERSON>"], "Comment Meta": ["Metadati dei commenti"], "Comments": ["Commenti"], "Post Meta": ["Metadati articoli"], "Please enable JavaScript": ["Abilita JavaScript"], "Loading, please wait...": ["In caricamento, attendi..."], "Create Issue": ["Apri un ticket"], "<code>SearchRegexi10n</code> is not defined. This usually means another plugin is blocking Search Regex from loading. Please disable all plugins and try again.": ["<code>SearchRegexi10n</code> non è definito. Di solito ciò significa che un altro plugin sta impedendo il caricamento di Search Regex. Disabilita tutti i plugin e riprova."], "If you think Search Regex is at fault then create an issue.": ["Se ritieni che Search Regex abbia un problema, apri un ticket."], "Please see the <a href=\"https://searchregex.com/support/problems/\">list of common problems</a>.": ["Consulta la <a href=\"https://searchregex.com/support/problems/\">lista dei problemi comuni</a>."], "Please note that Search Regex requires the WordPress REST API to be enabled. If you have disabled this then you won't be able to use Search Regex": ["Tieni presente che Search Regex richiede che la REST API di WordPress sia abilitata. Se l'hai disabilitata, non potrai usare Search Regex"], "Also check if your browser is able to load <code>search-regex.js</code>:": ["Verifica che il tuo browser riesca a caricare <code>search-regex.js</code>:"], "This may be caused by another plugin - look at your browser's error console for more details.": ["<PERSON><PERSON>ò potrebbe essere causato da un altro plugin. Guarda la console degli errori del tuo browser per maggiori dettagli."], "Unable to load Search Regex ☹️": ["Impossibile caricare Search Regex ☹️"], "Unable to load Search Regex": ["Impossibile caricare Search Regex"], "Search Regex requires WordPress v%1$1s, you are using v%2$2s - please update your WordPress": ["Search Regex richiede WordPress v%1$1s, tu stai usando v%2$2s. Aggiorna il tuo WordPress"], "Search Regex Support": ["Supporto per Search Regex"], "You can find full documentation about using Search Regex on the <a href=\"%s\" target=\"_blank\">searchregex.com</a> support site.": ["Puoi trovare la documentazione completa sull'uso di Search Regex nel sito di supporto <a href=\"%s\" target=\"_blank\">searchregex.com</a>."], "Settings": ["Impostazioni"], "Action": ["Azione"], "Row ID": ["ID di riga"], "No more matching results found.": ["<PERSON>ess<PERSON>'altra corrispondenza trovata."], "Last page": ["Ultima pagina"], "Page %(current)s of %(total)s": ["Pagina %(current)s di %(total)s"], "Next page": ["Pagina successiva"], "Progress %(current)s%%": ["Avanzamento %(current)s%%"], "Prev page": ["<PERSON><PERSON><PERSON>e"], "First page": ["Prima pagina"], "%s database row in total": ["%s database rows in total", "%s riga di database in totale", "%s righe di database in totale"], "500 per page": ["500 per pagina"], "250 per page": ["250 per pagina"], "100 per page": ["100 per pagina"], "50 per page": ["50 per pagina "], "25 per page": ["25 per pagina "], "Ignore Case": ["Ignora le maius<PERSON>le"], "Regular Expression": ["<PERSON><PERSON><PERSON><PERSON> regol<PERSON>"], "Row updated": ["Riga aggiornata"], "Row replaced": ["Riga sostituita"], "Row deleted": ["Riga cancellata"], "Settings saved": ["Impostazioni salvate"], "{{link}}Source Flags{{/link}} - additional options for the selected source. For example, include post {{guid}}GUID{{/guid}} in the search.": ["{{link}}Opzioni Sorgente{{/link}} - opzioni aggiuntive per la sorgente selezionata. Per esempio, includi il {{guid}}GUID{{/guid}} dell'articolo nella ricerca."], "{{link}}Source{{/link}} - the source of data you wish to search. For example, posts, pages, or comments.": ["{{link}}Sorgente{{/link}} - la sorgente dei dati in cui vuoi cercare. Per esempio, articoli, pagine o commenti."], "{{link}}Regular expression{{/link}} - a way of defining a pattern for text matching. Provides more advanced matches.": ["{{link}}Espressione regulare{{/link}} - Un modo per definire uno schema per la corrispondenza del testo. Fornisce corrispondenze più avanzate."], "{{link}}Search Flags{{/link}} - additional qualifiers for your search, to enable case insensitivity, and to enable regular expression support.": ["{{link}}Opzioni di ricerca{{/link}} - qualificatori aggiuntivi per la tua ricerca che consentono di ignorare le maiuscole e di abilitare il supporto per le espressioni regolari."], "The following concepts are used by Search Regex:": ["I seguenti elementi sono usati da Search Regex:"], "Like this plugin? You might want to consider {{link}}Redirection{{/link}}, a plugin to manage redirects, from the same author.": ["Ti piace questo plugin? Potresti considerare {{link}}Redirection{{/link}}, un plugin per gestire i reindirizzamenti, realizzato dallo stesso autore."], "Redirection": ["Redirection"], "If you want to submit information that you don't want in a public repository then send it directly via {{email}}email{{/email}} - include as much information as you can!": ["Se vuoi inviare informazioni che preferisci non vadano in un repository pubblico, allora mandale via {{email}}email{{/email}}. Includi quante più informazioni possibili!"], "Please note that any support is provide on as-time-is-available basis and is not guaranteed. I do not provide paid support.": ["Tieni presente che qualsiasi supporto è fornito quando è possibile e non è garantito. Non fornisco supporto a pagamento."], "If you want to report a bug please read the {{report}}Reporting Bugs{{/report}} guide.": ["Se vuoi segnalare un bug, leggi la guida {{report}}Reporting Bugs{{/report}}."], "Full documentation for Search Regex can be found at {{site}}https://searchregex.com{{/site}}.": ["La documentazione completa di Search Regex è disponibile su {{site}}https://searchregex.com{{/site}}."], "Need more help?": ["Hai bisogno di altro aiuto?"], "Results": ["Risultati"], "Source": ["<PERSON><PERSON><PERSON>"], "Enter search phrase": ["Inserisci la frase di ricerca"], "Replace All": ["Sostit<PERSON><PERSON><PERSON> tutto"], "Search": ["Cerca"], "Search and replace information in your database.": ["Cerca e sostituisci informazioni nel tuo database."], "Update": ["Aggiorna"], "How Search Regex uses the REST API - don't change unless necessary": ["Come Search Regex usa la REST API. Non cambiarla se non è necessario."], "REST API": ["REST API"], "I'm a nice person and I have helped support the author of this plugin": ["Sono una persona carina e ho sostenuto l'autore di questo plugin"], "Relative REST API": ["REST API relativa"], "Raw REST API": ["REST API grezza"], "Default REST API": ["REST API predefinita"], "Plugin Support": ["Supporto al plugin"], "Support 💰": ["Supporto 💰"], "You get useful software and I get to carry on making it better.": ["Tu ricevi software utile e io posso continuare a migliorarlo."], "Search Regex is free to use - life is wonderful and lovely! It has required a great deal of time and effort to develop and you can help support this development by {{strong}}making a small donation{{/strong}}.": ["Search Regex è distribuito gratuitamente - la vita è meravigliosa e adorabile! Lo sviluppo ha richiesto molto tempo e fatica e tu puoi aiutare lo sviluppo {{strong}}facendo una piccola donazione{{/strong}}."], "I'd like to support some more.": ["Vor<PERSON>i dare maggiore aiuto."], "You've supported this plugin - thank you!": ["Hai sostenuto questo plugin. Grazie!"], "If that doesn't help, open your browser's error console and create a {{link}}new issue{{/link}} with the details.": ["Se ciò non aiuta, apri la console degli errori del tuo browser e apri un {{link}}nuovo ticket{{/link}} con i dettagli."], "If you are using a page caching plugin or service (CloudFlare, OVH, etc) then you can also try clearing that cache.": ["Se stai usando un plugin di caching delle pagine o un servizio (CloudFlare, OVH, ecc.), puoi anche provare a svuotare la cache."], "Search Regex is not working. Try clearing your browser cache and reloading this page.": ["Search Regex non sta funzionando. Prova a svuotare la cache del tuo browser e a ricaricare questa pagina."], "clearing your cache.": ["svuotare la cache."], "If you are using a caching system such as Cloudflare then please read this: ": ["Se stai usando un sistema di cache come Cloudflare, leggi questo: "], "Please clear your browser cache and reload this page.": ["Pulisci la cache del tuo browser e ricarica questa pagina."], "Cached Search Regex detected": ["Rilevata cache di Search Regex"], "Show %s more": ["Show %s more", "Mostrane %s altro", "Mostrane altri %s"], "Maximum number of matches exceeded and hidden from view. These will be included in any replacements.": ["È stato superato il numero massimo di corrispondenze e sono nascoste dalla vista. Queste saranno incluse nelle eventuali sostituzioni."], "Delete": ["Elimina"], "Edit": ["Modifica"], "Check Again": ["Controlla di nuovo"], "Testing - %s%%": [], "Show Problems": ["Mostra i problemi"], "Summary": ["<PERSON><PERSON><PERSON>"], "You are using a broken REST API route. Changing to a working API should fix the problem.": ["Stai usando una route REST API non funzionante. Cambiarla con un'API funzionante dovrebbe risolvere il problema."], "Your REST API is not working and the plugin will not be able to continue until this is fixed.": ["La tua REST API non sta funzionando e il plugin non sarà in grado di continuare finché ciò non sarà sistemato."], "There are some problems connecting to your REST API. It is not necessary to fix these problems and the plugin is able to work.": ["Ci sono alcuni problemi di connessione alla tua REST API. Non è necessario sistemare questi problemi: il plugin è in grado di funzionare."], "Unavailable": ["Non disponibile"], "Not working but fixable": ["Non funzionante ma sistemabile"], "Working but some issues": ["Funzionante ma con problemi"], "Good": ["<PERSON><PERSON><PERSON>"], "Current API": ["API corrente"], "Switch to this API": ["Usa questa API"], "Hide": ["Nascondi"], "Show Full": ["<PERSON><PERSON> tutto"], "Working!": ["Funzionante!"], "Finished!": ["<PERSON><PERSON>!"], "Cancel": ["<PERSON><PERSON><PERSON>"], "Replace": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "Remove": ["<PERSON><PERSON><PERSON><PERSON>"], "Multi": ["Multi"], "Single": ["<PERSON><PERSON>"], "Support": ["Supporto"], "Options": ["Opzioni"], "Search & Replace": ["Cerca & Sostituisci"], "If you are using WordPress 5.2 or newer then look at your {{link}}Site Health{{/link}} and resolve any issues.": ["Se stai usando WordPress 5.2 o superiore, dai uno sguardo a {{link}}Salute del sito (Site Health){{/link}} e risolvi eventuali problemi."], "{{link}}Please temporarily disable other plugins!{{/link}} This fixes so many problems.": ["{{link}}Disabilita temporaneamente altri plugin!{{/link}} C<PERSON>ò risolve molti problemi."], "{{link}}Caching software{{/link}}, in particular Cloudflare, can cache the wrong thing. Try clearing all your caches.": ["{{link}}Il software di cache{{/link}}, in particolare Cloudflare, può mettere in cache la cosa sbagliata. Prova a svuotare tutte le tue cache."], "What do I do next?": ["Cosa faccio ora?"], "Something went wrong 🙁": ["Qualcosa è andato storto 🙁"], "Log out, clear your browser cache, and log in again - your browser has cached an old session.": ["<PERSON><PERSON>ci dalla sessione, svuota la cache del browser e rientra. Il tuo browser ha in cache una vecchia sessione."], "Reload the page - your current session is old.": ["Ricarica la pagina. La tua sessione corrente è vecchia."], "Include these details in your report along with a description of what you were doing and a screenshot.": ["Includi questi dettagli nel tuo rapporto insieme con una descrizione di quello che  stavi facendo a uno screenshot."], "Email": ["Email"], "Create An Issue": ["Apri un ticket"], "Closed": ["<PERSON><PERSON><PERSON>"], "Save": ["<PERSON><PERSON>"], "Unable to make request due to browser security. This is typically because your WordPress and Site URL settings are inconsistent, or the request was blocked by your site CORS policy.": ["Impossibile fare richiesta a motivo della sicurezza del browser. Ciò avviene di solito perché  le impostazioni del tuo WordPress e dell'opzione Indirizzo sito (URL) non sono coerenti, oppure perché la richiesta è stata bloccata dalla policy CORS del tuo sito."], "Possible cause": ["Possibile causa"], "This could be a security plugin, or your server is out of memory or has an external error. Please check your server error log": ["<PERSON><PERSON>ò può essere dovuto a un plugin di sicurezza oppure il tuo server ha esaurito la memoria oppure ha un errore esterno. Controlla il registro degli errori del server"], "Your server has rejected the request for being too big. You will need to reconfigure it to continue.": ["Il tuo server ha respinto la richiesta perché troppo grande. Devi cambiarla per continuare."], "Your REST API is probably being blocked by a security plugin. Please disable this, or configure it to allow REST API requests.": ["La tua REST API probabilmente è bloccata da un plugin di sicurezza. Disabilitalo oppure configuralo per permettere richieste REST API."], "Read this REST API guide for more information.": ["Leggi questa guida REST API per ulteriori informazioni."], "WordPress did not return a response. This could mean an error occurred or that the request was blocked. Please check your server error_log.": ["WordPress non ha restituito una risposta. <PERSON><PERSON>ò può significare che si è verificato un errore o che la richiesta è stata bloccata. Controlla il registro degli errori del tuo server."], "John Godley": ["<PERSON>"], "Adds search and replace functionality across posts, pages, comments, and meta-data, with full regular expression support": ["Aggiunge la funzionalità di cerca e sostituisci negli articoli, pagine, commenti e metadati, con il supporto completo alle espressioni regolari."], "https://searchregex.com/": ["https://searchregex.com/"], "Search Regex": ["Search Regex"]}}, "translation-revision-date": "2024-01-01T17:14:07.401Z", "source": "search-regex", "domain": "search-regex", "generator": "Search Regex"}