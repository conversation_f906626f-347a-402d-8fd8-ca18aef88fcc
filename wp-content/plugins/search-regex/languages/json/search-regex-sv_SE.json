{"locale_data": {"search-regex": {"": {"plural-forms": "nplurals=2; plural=n != 1;"}, "matched rows = %(searched)s": [], "OK": ["OK"], "Version %s installed! Please read the {{url}}release notes{{/url}} for details.": [], "A problem occurred with your last query. This is likely caused by a combination of search filters that haven't been handled properly.": [], "Query Problem": [], "Progress": ["<PERSON><PERSON><PERSON><PERSON><PERSON>"], "%s row deleted.": ["%s rows deleted.", "%s rad borttagen.", "%s rader borttagna."], "Refresh": ["Uppdatera"], "Matched Content": ["Matchat innehåll"], "Your search resulted in too many requests. Please narrow your search terms.": [], "Your search conditions have changed. Please refresh to see the latest results.": [], "Matched rows: %(matches)s out of %(total)s total.": [], "Delete database row": ["Ta bort databasrad"], "View": ["Visa"], "No title": ["Ingen rubrik"], "Empty value": [], "No value": ["Inget värde"], "Click to replace match": ["Klicka för att ersätta matchning"], "Contains encoded data": [], "New meta value": [], "New meta key": ["<PERSON><PERSON>"], "Apply to matches only": [], "Enter replacement": [], "Click to replace column": [], "This column contains special formatting. Modifying it could break the format.": [], "HTML": ["HTML"], "Blocks": ["Block"], "Serialized PHP": [], "Paste preset JSON.": [], "Global Search Flags": [], "Global Replace": [], "Global Search": ["Global sökning"], "View Columns": ["Visa kolumner"], "Sources": ["<PERSON><PERSON><PERSON><PERSON>"], "Only include selected columns": ["Inkludera endast valda kolumner"], "WordPress Action": [], "SQL": ["SQL"], "CSV": ["CSV"], "JSON": ["JSON"], "Export Format": ["Exporteringsformat"], "Run a WordPress action for each matching result.": [], "Run Action": ["<PERSON><PERSON><PERSON>"], "Delete matching results.": ["Ta bort matchande resultat."], "Delete Matches": ["Ta bort matchningar"], "Export matching results to JSON, CSV, or SQL.": ["Exportera matchande resultat till JSON, CSV eller SQL."], "Export Matches": ["Exportera matchningar"], "Perform changes to specific values of the matching results.": [], "Modify Matches": [], "Replace the global search values.": [], "Global Text Replace": [], "Just show matching results.": ["Visar endast matchande resultat."], "No action": ["<PERSON><PERSON> å<PERSON>ärd"], "Enter replacement value": [], "Matched values will be removed": [], "Replace \"%1s\"": ["Ersätt ”%1s”"], "Year": ["<PERSON><PERSON>"], "Months": ["<PERSON><PERSON><PERSON><PERSON>"], "Weeks": ["<PERSON><PERSON><PERSON>"], "Days": ["Dagar"], "Hours": ["<PERSON><PERSON>"], "Minutes": ["<PERSON><PERSON>"], "Seconds": ["<PERSON><PERSON><PERSON>"], "Decrement": ["Minskning"], "Increment": ["Ökning"], "Set Value": ["Ställ in värde"], "Replace With": ["<PERSON><PERSON>ätt med"], "Add": ["<PERSON><PERSON><PERSON> till"], "Column": ["<PERSON><PERSON><PERSON>"], "AND": ["OCH"], "Filters": [], "Add sub-filter (OR)": [], "OR": [], "All": ["<PERSON>a"], "between {{first/}} and {{second/}}": ["mellan {{first/}} och {{second/}}"], "No Owner": ["Ingen ägare"], "Has Owner": ["<PERSON><PERSON> <PERSON><PERSON>"], "Any": ["<PERSON><PERSON><PERSON>"], "Excludes any": [], "Includes any": [], "End": ["Slut"], "Begins": [], "Not contains": ["Innehåller inte"], "Contains": ["<PERSON><PERSON><PERSON><PERSON>"], "Range": [], "Less": ["Mindre"], "Greater": [], "Not Equals": [], "Equals": ["Lika"], "Optional global search phrase. Leave blank to use filters only.": [], "REST API 404": ["REST API 404"], "Your REST API appears to be cached and this will cause problems. Please exclude your REST API from your caching system.": [], "Your REST API is showing a deprecated PHP error. Please fix this error.": ["Ditt REST API visar ett föråldrat PHP-fel. Åtgärda detta fel."], "Your server configuration is blocking access to the REST API.": ["Din serverkonfiguration blockerar åtkomst till REST API:et."], "Can you access your {{api}}REST API{{/api}} without it redirecting?.": ["Kan du få åtkomst till ditt {{api}}REST API{{/api}} utan att det omdirigerar?"], "You will will need to fix this on your site. Redirection is not causing the error.": [], "Preset uploaded": [], "Multiline": [], "Case": [], "Regex": ["Regex"], "Nginx": ["<PERSON><PERSON><PERSON>"], "Apache": ["Apache"], "WordPress": ["WordPress"], "Module": ["<PERSON><PERSON><PERSON>"], "Redirection Groups": [], "Not accessed": [], "Target": ["<PERSON><PERSON><PERSON>"], "HTTP Code": ["HTTP-kod"], "Position": ["Position"], "Disabled": ["Inaktiverad"], "Enabled": ["Aktiverad"], "Status": ["Status"], "Group": ["Grupp"], "Last Access": [], "Hit Count": [], "Source URL (matching)": ["Käll-URL (matchar)"], "Source URL": ["Käll-URL"], "User meta": [], "Registered": ["Registrerad"], "Login": ["Logga in"], "Taxonomy": ["Taxonomi"], "Post Tag": ["Inläggsetikett"], "Post Category": ["Inläggskategori"], "Comment Count": ["<PERSON><PERSON> kom<PERSON>r"], "MIME": ["MIME"], "Parent": ["Överordnad"], "Modified GMT": [], "Modified": ["Modifierad"], "Password": ["L<PERSON>senord"], "Has no password": ["<PERSON>r inget l<PERSON>"], "Has password": ["<PERSON><PERSON> <PERSON>"], "Ping Status": ["Ping-status"], "Comment Status": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "Open": ["<PERSON><PERSON><PERSON>"], "Post Status": ["Inläggsstatus"], "Post Type": ["Inläggstyp"], "Autoload": [], "Is not autoload": [], "Is autoload": [], "Meta Value": ["Meta-värde"], "Meta Key": ["<PERSON><PERSON><PERSON><PERSON>"], "Owner ID": ["Ägar-ID"], "Comment meta": ["<PERSON><PERSON><PERSON> för kom<PERSON>ar"], "User ID": ["Användar-ID"], "Parent Comment": ["Överordnad kommentar"], "Trackback": ["Trackback"], "Pingback": ["<PERSON><PERSON>"], "Type": ["<PERSON><PERSON>"], "User Agent": ["Användaragent"], "Approval Status": ["Godkännandestatus"], "Spam": ["Skräppost"], "Approved": ["Godkänd"], "Unapproved": ["<PERSON><PERSON>"], "Date GMT": ["Datum GMT"], "Date": ["Datum"], "IP": ["IP"], "Author": ["Författare"], "Post ID": ["Inläggs-<PERSON>"], "ID": ["ID"], "Terms": ["<PERSON><PERSON><PERSON>"], "Posts (core & custom)": [], "Please review your data and try again.": ["Granska dina data och försök igen."], "There was a problem making a request to your site. This could indicate you provided data that did not match requirements, or that the plugin sent a bad request.": [], "Bad data": ["Dålig data"], "Your WordPress REST API is returning a 404 page. This is almost certainly an external plugin or server configuration issue.": [], "2000 per page": ["2000 per sida"], "1000 per page": ["1000 per sida"], "Set a preset to use by default when Search Regex is loaded.": [], "No default preset": [], "Default Preset": [], "Your admin pages are being cached. Clear this cache and try again. There may be multiple caches involved.": [], "This is usually fixed by doing one of the following:": ["<PERSON><PERSON><PERSON> van<PERSON> genom att göra något av detta:"], "You are using an old or cached session": ["<PERSON> anvä<PERSON> en gammal eller cachelagrad session"], "Debug Information": ["Felsökningsinformation"], "Show debug": ["Visa felsökning"], "WordPress returned an unexpected message. This could be a PHP error from another plugin, or data inserted by your theme.": ["WordPress returnerade ett oväntat meddelande. Detta kan vara ett PHP-fel från ett annat till<PERSON><PERSON> eller data som infogats av ditt tema."], "Your WordPress REST API has been disabled. You will need to enable it to continue.": ["Ditt WordPress REST API har inaktiverats. Du måste aktivera det för att fortsätta."], "Your REST API is being redirected. Please remove the redirection for the API.": ["Ditt REST API omdirigeras. Ta bort omdirigeringen för API:et."], "A security plugin or firewall is blocking access. You will need to whitelist the REST API.": ["Ett säkerhetstillägg eller brandvägg blockerar åtkomst. Du måste vitlista REST API:t."], "Check your {{link}}Site Health{{/link}} and fix any issues.": ["Kontrollera din {{link}}Hälsostatus för webbplats{{/ link}} och åtgärda eventuella problem."], "Preset": [], "Upload": ["Ladda upp"], "Add file": ["Lägg till fil"], "Preset saved": [], "Copy to clipboard": ["Kopiera till urklipp"], "Are you sure you want to delete this preset?": [], "Locked fields": ["Låsta fält"], "For example, create tag {{code}}URL{{/code}} and title {{code}}Image URL{{/code}}. Your search could be {{code}}<img src=\"URL\">{{/code}}. When the preset is used it will ask the user for the {{code}}Image URL{{/code}} instead of the full search phrase.": [], "A tag creates a custom input field. Insert the tag anywhere in the search, replace, text filter, or text action and when the preset is used it will be replaced with a custom text field with the tag label.": [], "Enter tag which is used in the field": [], "Tag": ["Etikett"], "Enter tag title shown to user": [], "Tags": ["Etiketter"], "Locking a field removes it from the search form and prevents changes.": [], "Fields": ["<PERSON><PERSON><PERSON>"], "Locked Fields": ["Låsta fält"], "Advanced preset": [], "Describe the preset": [], "Preset Description": [], "Give the preset a name": [], "Preset Name": [], "Edit preset": [], "Results per page": ["Resultat per sida"], "remove phrase": ["ta bort fras"], "no phrase": ["ingen fras"], "Import": ["Importera"], "Please check your JSON data is a valid preset. You may have copied it incorrectly, or pasted something that is not a preset.": [], "Unable to import preset": [], "Import preset from clipboard": [], "Done": ["<PERSON><PERSON>"], "Uploaded %(total)d preset": ["Uploaded %(total)d presets"], "Importing": ["Importerar"], "File selected": [], "Click 'Add File' or drag and drop here.": [], "Import a JSON file": ["Importera en JSON-fil"], "Import JSON": ["Importera JSON"], "Export JSON": ["Exportera JSON"], "Download presets!": [], "There are no presets": [], "Flags": ["<PERSON><PERSON>"], "Presets": [], "If that did not help then {{strong}}create an issue{{/strong}} or send it in an {{strong}}email{{/strong}}.": [], "Please check the {{link}}support site{{/link}} before proceeding further.": ["Kontrollera {{link}}supportwebbplatsen{{/link}} innan du går vidare."], "Enter preset name": [], "Enter a name for your preset": [], "Saving Preset": [], "Update current preset": [], "Save search as new preset": [], "No preset": [], "Saving preset": [], "Advanced": ["<PERSON><PERSON><PERSON>"], "Standard": ["Standard"], "Please backup your data before making modifications.": [], "%s row.": ["%s rows.", "%s rad.", "%s rader."], "An unknown error occurred.": ["<PERSON><PERSON> okänt fel uppstod."], "GUID": ["GUID"], "Slug": ["Slug"], "Excerpt": ["<PERSON><PERSON><PERSON><PERSON>"], "Content": ["<PERSON><PERSON><PERSON><PERSON>"], "Display Name": ["Visningsnamn"], "Nicename": ["Smeknamn"], "Value": ["<PERSON><PERSON><PERSON>"], "Comment": ["Kommentar"], "Name": ["<PERSON><PERSON>"], "Title": ["<PERSON><PERSON><PERSON>"], "URL": ["URL"], "Disabled! Detected PHP %1$s, need PHP %2$s+": [], "Plugins": ["<PERSON><PERSON><PERSON>"], "WordPress Options": ["WordPress-alternativ"], "User Meta": [], "Users": ["Användare"], "Comment Meta": [], "Comments": ["<PERSON><PERSON><PERSON><PERSON>"], "Post Meta": [], "Please enable JavaScript": ["Aktivera JavaScript"], "Loading, please wait...": ["Laddar in, vänta …"], "Create Issue": [], "<code>SearchRegexi10n</code> is not defined. This usually means another plugin is blocking Search Regex from loading. Please disable all plugins and try again.": [], "If you think Search Regex is at fault then create an issue.": [], "Please see the <a href=\"https://searchregex.com/support/problems/\">list of common problems</a>.": [], "Please note that Search Regex requires the WordPress REST API to be enabled. If you have disabled this then you won't be able to use Search Regex": [], "Also check if your browser is able to load <code>search-regex.js</code>:": [], "This may be caused by another plugin - look at your browser's error console for more details.": [], "Unable to load Search Regex ☹️": [], "Unable to load Search Regex": [], "Search Regex requires WordPress v%1$1s, you are using v%2$2s - please update your WordPress": [], "Search Regex Support": [], "You can find full documentation about using Search Regex on the <a href=\"%s\" target=\"_blank\">searchregex.com</a> support site.": [], "Settings": ["Inställningar"], "Action": ["<PERSON><PERSON><PERSON><PERSON><PERSON>"], "Row ID": ["Rad-<PERSON>"], "No more matching results found.": ["Inga fler matchande resultat hittades."], "Last page": ["<PERSON><PERSON> sidan"], "Page %(current)s of %(total)s": [], "Next page": ["<PERSON><PERSON><PERSON> sida"], "Progress %(current)s%%": [], "Prev page": ["Föregående sida"], "First page": ["<PERSON><PERSON><PERSON><PERSON>"], "%s database row in total": ["%s database rows in total"], "500 per page": ["500 per sida"], "250 per page": ["250 per sida"], "100 per page": ["100 per sida"], "50 per page": ["50 per sida"], "25 per page": ["25 per sida"], "Ignore Case": [], "Regular Expression": ["<PERSON><PERSON><PERSON>"], "Row updated": [], "Row replaced": [], "Row deleted": ["Rad bort<PERSON>n"], "Settings saved": ["Inställningar sparade"], "{{link}}Source Flags{{/link}} - additional options for the selected source. For example, include post {{guid}}GUID{{/guid}} in the search.": [], "{{link}}Source{{/link}} - the source of data you wish to search. For example, posts, pages, or comments.": [], "{{link}}Regular expression{{/link}} - a way of defining a pattern for text matching. Provides more advanced matches.": [], "{{link}}Search Flags{{/link}} - additional qualifiers for your search, to enable case insensitivity, and to enable regular expression support.": [], "The following concepts are used by Search Regex:": [], "Like this plugin? You might want to consider {{link}}Redirection{{/link}}, a plugin to manage redirects, from the same author.": [], "Redirection": ["Omdir<PERSON>ing"], "If you want to submit information that you don't want in a public repository then send it directly via {{email}}email{{/email}} - include as much information as you can!": [], "Please note that any support is provide on as-time-is-available basis and is not guaranteed. I do not provide paid support.": [], "If you want to report a bug please read the {{report}}Reporting Bugs{{/report}} guide.": ["Om du vill rapportera ett fel, läs guiden {{report}}rapportera fel{{/report}}."], "Full documentation for Search Regex can be found at {{site}}https://searchregex.com{{/site}}.": [], "Need more help?": ["Behöver du mer hjälp?"], "Results": ["Resultat"], "Source": ["<PERSON><PERSON><PERSON>"], "Enter search phrase": ["<PERSON><PERSON>"], "Replace All": ["<PERSON><PERSON><PERSON><PERSON> alla"], "Search": ["<PERSON>ö<PERSON>"], "Search and replace information in your database.": [], "Update": ["Uppdatera"], "How Search Regex uses the REST API - don't change unless necessary": [], "REST API": ["REST API"], "I'm a nice person and I have helped support the author of this plugin": [], "Relative REST API": ["Relativt REST API"], "Raw REST API": ["Obearbetat REST-API"], "Default REST API": [], "Plugin Support": ["Support fö<PERSON>"], "Support 💰": ["Support 💰"], "You get useful software and I get to carry on making it better.": [], "Search Regex is free to use - life is wonderful and lovely! It has required a great deal of time and effort to develop and you can help support this development by {{strong}}making a small donation{{/strong}}.": [], "I'd like to support some more.": [], "You've supported this plugin - thank you!": ["Du har stöttat detta till<PERSON>gg – tack!"], "If that doesn't help, open your browser's error console and create a {{link}}new issue{{/link}} with the details.": [], "If you are using a page caching plugin or service (CloudFlare, OVH, etc) then you can also try clearing that cache.": [], "Search Regex is not working. Try clearing your browser cache and reloading this page.": [], "clearing your cache.": [], "If you are using a caching system such as Cloudflare then please read this: ": [], "Please clear your browser cache and reload this page.": ["Rensa din webbläsares cache och ladda om denna sida."], "Cached Search Regex detected": [], "Show %s more": ["Show %s more", "Visa %s till", "Visa %s fler"], "Maximum number of matches exceeded and hidden from view. These will be included in any replacements.": [], "Delete": ["<PERSON> bort"], "Edit": ["Rediger<PERSON>"], "Check Again": ["Kontrollera igen"], "Testing - %s%%": ["Testar – %s%%"], "Show Problems": ["Visa problem"], "Summary": ["<PERSON><PERSON><PERSON><PERSON><PERSON>"], "You are using a broken REST API route. Changing to a working API should fix the problem.": [], "Your REST API is not working and the plugin will not be able to continue until this is fixed.": ["Ditt REST-API fungerar inte och tillägget kan inte fortsätta förrän detta är åtgärdat."], "There are some problems connecting to your REST API. It is not necessary to fix these problems and the plugin is able to work.": ["Det finns några problem med att ansluta till ditt REST-API. Det är inte nödvändigt att åtgärda dessa problem och tillägget kan fungera."], "Unavailable": ["Inte tillgänglig"], "Not working but fixable": ["Fungerar inte men kan å<PERSON>"], "Working but some issues": ["Fungerar men vissa problem"], "Good": ["Bra"], "Current API": ["Nuvarande API"], "Switch to this API": ["Byt till detta API"], "Hide": ["<PERSON><PERSON><PERSON><PERSON>"], "Show Full": ["Visa fullständig"], "Working!": ["Fungerar!"], "Finished!": [], "Cancel": ["<PERSON><PERSON><PERSON><PERSON><PERSON>"], "Replace": ["<PERSON><PERSON><PERSON><PERSON>"], "Remove": ["<PERSON> bort"], "Multi": ["Flera"], "Single": [], "Support": ["Support"], "Options": ["Alternativ"], "Search & Replace": [], "If you are using WordPress 5.2 or newer then look at your {{link}}Site Health{{/link}} and resolve any issues.": ["<PERSON><PERSON> du använder WordPress 5.2 el<PERSON> nyar<PERSON>, titta på din {{link}}<PERSON><PERSON>lsokontroll för webbplatser{{/ link}} och lös eventuella problem."], "{{link}}Please temporarily disable other plugins!{{/link}} This fixes so many problems.": ["{{link}}Inaktivera andra tillägg tillfälligt!{{/link}} Detta åtgärdar så många problem."], "{{link}}Caching software{{/link}}, in particular Cloudflare, can cache the wrong thing. Try clearing all your caches.": [], "What do I do next?": ["Vad gör jag härnäst?"], "Something went wrong 🙁": ["<PERSON><PERSON><PERSON> gick snett 🙁"], "Log out, clear your browser cache, and log in again - your browser has cached an old session.": [], "Reload the page - your current session is old.": ["Ladda om sidan – din nuvarande session är gammal."], "Include these details in your report along with a description of what you were doing and a screenshot.": ["Inkludera dessa detaljer i din rapport tillsammans med en beskrivning av vad du gjorde och en skärmdump."], "Email": ["E-post"], "Create An Issue": [], "Closed": ["Stängt"], "Save": ["Spara"], "Unable to make request due to browser security. This is typically because your WordPress and Site URL settings are inconsistent, or the request was blocked by your site CORS policy.": [], "Possible cause": [], "This could be a security plugin, or your server is out of memory or has an external error. Please check your server error log": ["Detta kan vara ett säkerhetstillägg, eller servern har slut på minne eller har ett externt fel. Kontrollera din serverfellogg"], "Your server has rejected the request for being too big. You will need to reconfigure it to continue.": ["Din server har avisat begäran för att den var för stor. Du måste konfigurera den igen för att fortsätta."], "Your REST API is probably being blocked by a security plugin. Please disable this, or configure it to allow REST API requests.": [], "Read this REST API guide for more information.": ["<PERSON>äs denna REST API-guide för mer information."], "WordPress did not return a response. This could mean an error occurred or that the request was blocked. Please check your server error_log.": [], "John Godley": ["<PERSON>"], "Adds search and replace functionality across posts, pages, comments, and meta-data, with full regular expression support": [], "https://searchregex.com/": ["https://searchregex.com/"], "Search Regex": ["Search Regex"]}}, "translation-revision-date": "2024-01-01T17:14:07.418Z", "source": "search-regex", "domain": "search-regex", "generator": "Search Regex"}