{"locale_data": {"search-regex": {"": {"plural-forms": "nplurals=3; plural=(n % 10 == 1 && n % 100 != 11) ? 0 : ((n % 10 >= 2 && n % 10 <= 4 && (n % 100 < 12 || n % 100 > 14)) ? 1 : 2);"}, "matched rows = %(searched)s": ["совпадающие строки = %(searched)s"], "OK": ["OK"], "Version %s installed! Please read the {{url}}release notes{{/url}} for details.": ["Установлена версия %s! Прочитайте {{url}}примечания к выпуску{{/url}} для получения подробной информации."], "A problem occurred with your last query. This is likely caused by a combination of search filters that haven't been handled properly.": ["Возникла проблема с вашим последним запросом. Вероятно, это вызвано комбинацией поисковых фильтров, которые не были обработаны должным образом."], "Query Problem": ["Проблема с запросом"], "Progress": ["Ход выполнения"], "%s row deleted.": ["%s rows deleted.", "%s строка удалена.", "%s строки удалены.", "%s строк удалены."], "Refresh": ["Обновить"], "Matched Content": ["Соответствующее содержимое"], "Your search resulted in too many requests. Please narrow your search terms.": ["Ваш поиск привел к слишком большому количеству запросов. Пожалуйста, сузьте критерии поиска."], "Your search conditions have changed. Please refresh to see the latest results.": ["Условия поиска изменились. Обновите страницу, чтобы увидеть последние результаты."], "Matched rows: %(matches)s out of %(total)s total.": ["Совпадающие строки: %(matches)s из %(total)s всего."], "Delete database row": ["Удалить строку базы данных"], "View": ["Просмотр"], "No title": ["Без названия"], "Empty value": ["Пустое значение"], "No value": ["Без значения"], "Click to replace match": ["Нажмите, чтобы заменить совпадение"], "Contains encoded data": ["Содержит закодированные данные"], "New meta value": ["Новое мета-значение"], "New meta key": ["Новый мета-ключ"], "Apply to matches only": ["Применять только к совпадениям"], "Enter replacement": ["Введите замену"], "Click to replace column": ["Нажмите, чтобы заменить столбец"], "This column contains special formatting. Modifying it could break the format.": ["Этот столбец содержит специальное форматирование. Его изменение может нарушить формат."], "HTML": ["HTML"], "Blocks": ["Блоки"], "Serialized PHP": ["Сериализованный PHP"], "Paste preset JSON.": ["Вставьте предустановленный JSON."], "Global Search Flags": ["Флаги глобального поиска"], "Global Replace": ["Глобальная замена"], "Global Search": ["Глобальный поиск"], "View Columns": ["Просмотр сто<PERSON><PERSON><PERSON>ов"], "Sources": ["Источники"], "Only include selected columns": ["Включить только выбранные столбцы"], "WordPress Action": ["Действие WordPress"], "SQL": ["SQL"], "CSV": ["CSV"], "JSON": ["JSON"], "Export Format": ["Формат экспорта"], "Run a WordPress action for each matching result.": ["Запустите действие WordPress для каждого совпадающего результата."], "Run Action": ["Выполнить действие"], "Delete matching results.": ["Удалить совпадающие результаты."], "Delete Matches": ["Удалить совпадения"], "Export matching results to JSON, CSV, or SQL.": ["Экспорт результатов сопоставления в JSON, CSV или SQL."], "Export Matches": ["Экспорт совпадений"], "Perform changes to specific values of the matching results.": ["Внесите изменения в определенные значения результатов сопоставления."], "Modify Matches": ["Изменить совпадения"], "Replace the global search values.": ["Замените значения глобального поиска."], "Global Text Replace": ["Глобальная замена текста"], "Just show matching results.": ["Просто покажите совпадающие результаты."], "No action": ["Нет действий"], "Enter replacement value": ["Введите значение замены"], "Matched values will be removed": ["Совпадающие значения будут удалены"], "Replace \"%1s\"": ["Заменить \"%1s\""], "Year": ["Год"], "Months": ["Месяцы"], "Weeks": ["Недели"], "Days": ["<PERSON><PERSON>и"], "Hours": ["<PERSON>а<PERSON>ы"], "Minutes": ["Минуты"], "Seconds": ["Секунды"], "Decrement": ["Уменьшение"], "Increment": ["Увеличение"], "Set Value": ["Установить значение"], "Replace With": ["Заменить с"], "Add": ["Добавить"], "Column": ["Столбец"], "AND": ["И"], "Filters": ["Фильтры"], "Add sub-filter (OR)": ["Добавить подфильтр (ИЛИ)"], "OR": ["ИЛИ"], "All": ["Все"], "between {{first/}} and {{second/}}": ["между {{first/}} и {{second/}}"], "No Owner": ["Нет владельца"], "Has Owner": ["Имеет владельца"], "Any": ["Любой"], "Excludes any": ["Исключает любые"], "Includes any": ["Включает любые"], "End": ["Окончание"], "Begins": ["Начинается"], "Not contains": ["Не содержит"], "Contains": ["Соде<PERSON><PERSON><PERSON>т"], "Range": ["Диа<PERSON>азон"], "Less": ["Меньше"], "Greater": ["Больше"], "Not Equals": ["Не равно"], "Equals": ["Равно"], "Optional global search phrase. Leave blank to use filters only.": ["Необязательная глобальная поисковая фраза. Оставьте пустым, чтобы использовать только фильтры."], "REST API 404": ["REST API 404"], "Your REST API appears to be cached and this will cause problems. Please exclude your REST API from your caching system.": ["Ваш REST API, по-видимому, кэшируется, и это вызывает проблемы. Исключите ваш REST API из вашей системы кэширования."], "Your REST API is showing a deprecated PHP error. Please fix this error.": ["API REST показывает устаревшую ошибку PHP. Исправьте эту ошибку."], "Your server configuration is blocking access to the REST API.": ["Конфигурация вашего сервера блокирует доступ к REST API."], "Can you access your {{api}}REST API{{/api}} without it redirecting?.": ["Можно ли получить доступ к {{api}}REST API{{/api}} без перенаправления?."], "You will will need to fix this on your site. Redirection is not causing the error.": ["Вам нужно будет исправить это на вашем сайте. Redirection не вызывает ошибку."], "Preset uploaded": ["Предустановка загружена"], "Multiline": ["Многострочный"], "Case": ["Случай"], "Regex": ["Регулярное выражение"], "Nginx": ["NGINX"], "Apache": ["Apache"], "WordPress": ["WordPress"], "Module": ["Модуль"], "Redirection Groups": ["Группы перенаправления"], "Not accessed": ["Нет доступа"], "Target": ["Цель"], "HTTP Code": ["Код HTTP"], "Position": ["Позиция"], "Disabled": ["Отключить"], "Enabled": ["Включить"], "Status": ["Статус"], "Group": ["Группа"], "Last Access": ["Последний доступ"], "Hit Count": ["Количество попаданий"], "Source URL (matching)": ["Исходный URL (совпадает)"], "Source URL": ["Исходный URL"], "User meta": ["Мета пользователя"], "Registered": ["Зарегистрированно"], "Login": ["Вход"], "Taxonomy": ["Таксономия"], "Post Tag": ["Метка записи"], "Post Category": ["Рубрика записи"], "Comment Count": ["Кол-во комментариев"], "MIME": ["MIME"], "Parent": ["Родительский"], "Modified GMT": ["Изменено GMT"], "Modified": ["Изменено"], "Password": ["Пароль"], "Has no password": ["Нет пароля"], "Has password": ["Имеет пароль"], "Ping Status": ["Статус отклика"], "Comment Status": ["Статус комментария"], "Open": ["Открыть"], "Post Status": ["Статус записи"], "Post Type": ["Тип записи"], "Autoload": ["Автозагрузка"], "Is not autoload": ["Не автозагрузка"], "Is autoload": ["Автозагрузка"], "Meta Value": ["Мета значение"], "Meta Key": ["<PERSON><PERSON><PERSON><PERSON> ключ"], "Owner ID": ["ID владельца"], "Comment meta": ["Мета комментария"], "User ID": ["ID пользователя"], "Parent Comment": ["Родительский комментарий"], "Trackback": ["Обратная ссылка"], "Pingback": ["Уведомление"], "Type": ["Тип"], "User Agent": ["Агент пользователя"], "Approval Status": ["Статус утверждения"], "Spam": ["Спам"], "Approved": ["Одобрен"], "Unapproved": ["Не одобрен"], "Date GMT": ["Дата GMT"], "Date": ["Дата"], "IP": ["IP"], "Author": ["<PERSON><PERSON><PERSON><PERSON><PERSON>"], "Post ID": ["ID записи"], "ID": ["ID"], "Terms": ["Термины"], "Posts (core & custom)": ["Записи (основные и пользовательские)"], "Please review your data and try again.": ["Проверьте данные и повторите попытку."], "There was a problem making a request to your site. This could indicate you provided data that did not match requirements, or that the plugin sent a bad request.": ["При отправке запроса на ваш сайт возникла проблема. Это может означать, что вы предоставили данные, которые не соответствуют требованиям, или что плагин отправил неверный запрос."], "Bad data": ["Неверные данные"], "Your WordPress REST API is returning a 404 page. This is almost certainly an external plugin or server configuration issue.": ["API REST возвращает страницу 404. Это почти наверняка проблема конфигурации внешнего плагина или сервера."], "2000 per page": ["2000 на страницу"], "1000 per page": ["1000 на страницу"], "Set a preset to use by default when Search Regex is loaded.": ["Задайте предустановку, которая будет использоваться по умолчанию при загрузке Search Regex."], "No default preset": ["Нет предустановки по умолчанию"], "Default Preset": ["Предустановка по умолчанию"], "Your admin pages are being cached. Clear this cache and try again. There may be multiple caches involved.": ["Ваши страницы администрирования кэшируются. Очистите этот кэш и повторите попытку. Возможно, задействовано несколько кэшей."], "This is usually fixed by doing one of the following:": ["Обычно это можно исправить, выполнив одно из следующих действий:"], "You are using an old or cached session": ["Вы используете старую или кешированную сессию"], "Debug Information": ["Отладочная информация"], "Show debug": ["Показать отладку"], "WordPress returned an unexpected message. This could be a PHP error from another plugin, or data inserted by your theme.": ["WordPress вернул неожиданное сообщение. Это может быть ошибка PHP от другого плагина или данные, вставленные вашей темой."], "Your WordPress REST API has been disabled. You will need to enable it to continue.": ["Ваш WordPress REST API был отключен. Чтобы продолжить, вам нужно будет включить его."], "Your REST API is being redirected. Please remove the redirection for the API.": ["Ваш REST API перенаправляется. Пожалуйста, уберите перенаправление для API."], "A security plugin or firewall is blocking access. You will need to whitelist the REST API.": ["Плагин безопасности или брандмауэр блокирует доступ. Вам нужно будет внести REST API в белый список."], "Check your {{link}}Site Health{{/link}} and fix any issues.": ["Проверьте состояние вашего {{link}}сайта{{/link}} и устраните все проблемы."], "Preset": ["Предустановка"], "Upload": ["Загрузить"], "Add file": ["Добавить файл"], "Preset saved": ["Сохранение предустановки"], "Copy to clipboard": ["Скопировать в буфер обмена"], "Are you sure you want to delete this preset?": ["Вы уверены, что хотите удалить эту предустановку?"], "Locked fields": ["Заблокированные поля"], "For example, create tag {{code}}URL{{/code}} and title {{code}}Image URL{{/code}}. Your search could be {{code}}<img src=\"URL\">{{/code}}. When the preset is used it will ask the user for the {{code}}Image URL{{/code}} instead of the full search phrase.": ["Например, создайте тег {{code}}URL{{/code}} и заголовок {{code}}Image URL{{/code}}. Ваш поиск может выглядеть так {{code}}<img src=\"URL\">{{/code}}. При использовании этой предустановки вместо полной поисковой фразы у пользователя будет запрашиваться {{code}}Image URL{{/code}}."], "A tag creates a custom input field. Insert the tag anywhere in the search, replace, text filter, or text action and when the preset is used it will be replaced with a custom text field with the tag label.": ["Метка создает пользовательское поле ввода. Вставьте тег в любое место поиска, замены, текстового фильтра или текстового действия, и при использовании предустановки он будет заменен на пользовательское текстовое поле с этикеткой метки."], "Enter tag which is used in the field": ["Введите тег, который используется в поле"], "Tag": ["Метка"], "Enter tag title shown to user": ["Введите заголовок тэга, показанный пользователю"], "Tags": ["Метки"], "Locking a field removes it from the search form and prevents changes.": ["Блокировка поля удаляет его из формы поиска и предотвращает внесение изменений."], "Fields": ["Поля"], "Locked Fields": ["Заблокированные поля"], "Advanced preset": ["Расширенные предустановки"], "Describe the preset": ["Опишите предустановку"], "Preset Description": ["Описание предустановки"], "Give the preset a name": ["Дайте предустановке название"], "Preset Name": ["Название предустановки"], "Edit preset": ["Изменить предустановку"], "Results per page": ["Результатов на странице"], "remove phrase": ["удалить фразу"], "no phrase": ["нет фразы"], "Import": ["Импорт"], "Please check your JSON data is a valid preset. You may have copied it incorrectly, or pasted something that is not a preset.": ["Убедитесь, что ваши данные JSON являются действительными предустановками. Возможно, вы скопировали его неправильно или вставили что-то, что не является предустановкой."], "Unable to import preset": ["Невозможно импортировать предустановку"], "Import preset from clipboard": ["Импорт предустановок из буфера обмена"], "Done": ["Готово"], "Uploaded %(total)d preset": ["Uploaded %(total)d presets", "Загружена %(total)d предустановка", "Загружено %(total)d предустановки", "Загружено %(total)d предустановок"], "Importing": ["Импортирование"], "File selected": ["Выбран файл"], "Click 'Add File' or drag and drop here.": ["Нажмите \"Добавить файл\" или перетащите его сюда."], "Import a JSON file": ["Импорт файла JSON"], "Import JSON": ["Импорт JSON"], "Export JSON": ["Экспорт JSON"], "Download presets!": ["Скачать предустановки!"], "There are no presets": ["Нет никаких предустановок"], "Flags": ["Флаги"], "Presets": ["Предустановки"], "If that did not help then {{strong}}create an issue{{/strong}} or send it in an {{strong}}email{{/strong}}.": ["Если это не помогло, тогда {{strong}} создайте тикет о проблеме{{/strong}} или отправьте ее по {{strong}}email{{/strong}}."], "Please check the {{link}}support site{{/link}} before proceeding further.": ["Посетите {{link}}сайт поддержки{{/link}}, прежде чем продолжить."], "Enter preset name": ["Введите название предустановки"], "Enter a name for your preset": ["Введите название для вашей предустановки"], "Saving Preset": ["Сохранение предустановки"], "Update current preset": ["Обновление текущей предустановки"], "Save search as new preset": ["Сохранить поиск как новую предустановку"], "No preset": ["Нет предустановки"], "Saving preset": ["Сохранение предустановки"], "Advanced": ["Рас<PERSON>иренный"], "Standard": [], "Please backup your data before making modifications.": ["Создайте резервную копию данных перед внесением изменений."], "%s row.": ["%s rows.", "%s строка.", "%s строки.", "%s строк."], "An unknown error occurred.": ["Произошла неизвестная ошибка."], "GUID": ["GUID"], "Slug": ["Ярлык"], "Excerpt": ["Отрывок"], "Content": ["Содержимое"], "Display Name": ["Отображаемое имя"], "Nicename": ["Ник"], "Value": ["Значение"], "Comment": ["Комментарий"], "Name": ["Имя"], "Title": ["Наименование"], "URL": ["URL"], "Disabled! Detected PHP %1$s, need PHP %2$s+": ["Отключено! Обнаружен PHP %1$s, требуется PHP %2$s+"], "Plugins": ["Плагины"], "WordPress Options": ["Параметры WordPress"], "User Meta": ["Мета пользователя"], "Users": ["Пользователи"], "Comment Meta": ["Мета комментария"], "Comments": ["Комментарии"], "Post Meta": ["Метаданные записи"], "Please enable JavaScript": ["Включите JavaScript"], "Loading, please wait...": ["Загрузка, пожалуйста подождите..."], "Create Issue": ["Создать тикет о проблеме"], "<code>SearchRegexi10n</code> is not defined. This usually means another plugin is blocking Search Regex from loading. Please disable all plugins and try again.": ["<code>SearchRegexi10n</code> не определен. Обычно это означает, что другой плагин блокирует загрузку Search Regex. Пожалуйста, отключите все плагины и повторите попытку."], "If you think Search Regex is at fault then create an issue.": ["Если вы считаете, что Search Regex виноват, создайте проблему."], "Please see the <a href=\"https://searchregex.com/support/problems/\">list of common problems</a>.": ["Ознакомьтесь со списком распространенных проблем <a href=\"https://searchregex.com/support/problems/\"></a>."], "Please note that Search Regex requires the WordPress REST API to be enabled. If you have disabled this then you won't be able to use Search Regex": ["Обратите внимание, что для Search Regex требуется, чтобы был включен WordPress REST API. Если вы отключили это, вы не сможете использовать Search Regex"], "Also check if your browser is able to load <code>search-regex.js</code>:": ["Также проверьте, способен ли ваш браузер загрузить <code>search-regex.js</code>:"], "This may be caused by another plugin - look at your browser's error console for more details.": ["Это может быть вызвано другим плагином-посмотрите на консоль ошибок вашего браузера для более подробной информации."], "Unable to load Search Regex ☹️": ["Не удалось загрузить Search Regex ☹️"], "Unable to load Search Regex": ["Не удалось загрузить Search Regex"], "Search Regex requires WordPress v%1$1s, you are using v%2$2s - please update your WordPress": ["Search Regex требует WordPress v%1$1s, вы используете v%2$2s - обновите ваш WordPress"], "Search Regex Support": ["Поддержка Search Regex"], "You can find full documentation about using Search Regex on the <a href=\"%s\" target=\"_blank\">searchregex.com</a> support site.": ["Полная документация по использованию Search Regex на сайте поддержки <a href=\"%s\" target=\"_blank\">searchregex.com</a>."], "Settings": ["Настройки"], "Action": ["Действие"], "Row ID": ["ID ряда"], "No more matching results found.": ["Больше подходящих результатов не найдено."], "Last page": ["Последняя страница"], "Page %(current)s of %(total)s": ["Страница %(current)s of %(total)s"], "Next page": ["Следующая страница"], "Progress %(current)s%%": ["Ход выполнения %(текущий)s%%"], "Prev page": ["Предыдущая страница"], "First page": ["Первая страница"], "%s database row in total": ["%s database rows in total", "Всего %s строка базы данных", "Всего %s строки базы данных", "Всего %s строк базы данных"], "500 per page": ["500 на страницу"], "250 per page": ["250 на страницу"], "100 per page": ["100 на страницу"], "50 per page": ["50 на страницу"], "25 per page": ["25 на страницу"], "Ignore Case": ["Игнорировать регистр"], "Regular Expression": ["Регулярное выражение"], "Row updated": ["Строка обновлена"], "Row replaced": ["Строка заменена"], "Row deleted": ["Строка удалена"], "Settings saved": ["Настройки сохранены"], "{{link}}Source Flags{{/link}} - additional options for the selected source. For example, include post {{guid}}GUID{{/guid}} in the search.": ["{{link}}Флаги источника{{/link}} - дополнительные параметры для выбранного источника. Например, включите в поиск запись {{guid}}GUID{{/guid}}."], "{{link}}Source{{/link}} - the source of data you wish to search. For example, posts, pages, or comments.": ["{{link}}Источник{{/link}} - источник данных, которые вы хотите найти. Например, посты, страницы или комментарии."], "{{link}}Regular expression{{/link}} - a way of defining a pattern for text matching. Provides more advanced matches.": ["{{link}}Регулярное выражение{{/link}} - способ определения шаблона для сопоставления текста. Обеспечивает более сложные совпадения."], "{{link}}Search Flags{{/link}} - additional qualifiers for your search, to enable case insensitivity, and to enable regular expression support.": ["{{link}}Флаги поиска{{/link}} - дополнительные квалификаторы для вашего поиска, чтобы включить нечувствительность к регистру и включить поддержку регулярных выражений."], "The following concepts are used by Search Regex:": ["В Search Regex используются следующие понятия:"], "Like this plugin? You might want to consider {{link}}Redirection{{/link}}, a plugin to manage redirects, from the same author.": ["Нравится этот плагин? Возможно, вы захотите рассмотреть {{link}}Redirection{{/link}}, плагин для управления перенаправлениями, от того же автора."], "Redirection": ["Перенаправление"], "If you want to submit information that you don't want in a public repository then send it directly via {{email}}email{{/email}} - include as much information as you can!": ["Если вы хотите отправить информацию, которую вы не хотите в публичный репозиторий, отправьте ее напрямую через {{email}} email {{/e-mail}} - укажите как можно больше информации!"], "Please note that any support is provide on as-time-is-available basis and is not guaranteed. I do not provide paid support.": ["Обратите внимание, что любая поддержка предоставляется по мере доступности и не гарантируется. Я не предоставляю платной поддержки."], "If you want to report a bug please read the {{report}}Reporting Bugs{{/report}} guide.": ["Если вы хотите сообщить об ошибке, пожалуйста, прочитайте инструкцию {{report}} отчеты об ошибках {{/report}}."], "Full documentation for Search Regex can be found at {{site}}https://searchregex.com{{/site}}.": ["Полную документацию по Search Regex можно найти на {{site}}https://searchregex.com{{/site}}."], "Need more help?": ["Нужна дополнительная помощь?"], "Results": ["Результаты"], "Source": ["Источник"], "Enter search phrase": ["Введите фразу для поиска"], "Replace All": ["Заменить все"], "Search": ["Поиск"], "Search and replace information in your database.": ["Поиск и замена информации в базе данных."], "Update": ["Обновить"], "How Search Regex uses the REST API - don't change unless necessary": ["Как Search Regex использует REST API - не меняйте без необходимости"], "REST API": ["REST API"], "I'm a nice person and I have helped support the author of this plugin": ["Я хороший человек, и я помог поддержать автора этого плагина"], "Relative REST API": ["Относительный REST API"], "Raw REST API": ["Необработанный REST API"], "Default REST API": ["REST API по умолчанию"], "Plugin Support": ["Поддержка плагина"], "Support 💰": ["Поддержка 💰"], "You get useful software and I get to carry on making it better.": ["Вы получаете полезное программное обеспечение, и я продолжаю делать его лучше."], "Search Regex is free to use - life is wonderful and lovely! It has required a great deal of time and effort to develop and you can help support this development by {{strong}}making a small donation{{/strong}}.": ["Search Regex бесплатен для использования - жизнь чудесна и прекрасна! Его разработка потребовала много времени и усилий, и вы можете помочь поддержать это развитие, сделав {{strong}} небольшое пожертвование{{/strong}}."], "I'd like to support some more.": ["Мне хотелось бы поддержать чуть больше."], "You've supported this plugin - thank you!": ["Вы поддерживаете этот плагин - спасибо!"], "If that doesn't help, open your browser's error console and create a {{link}}new issue{{/link}} with the details.": ["Если это не поможет, откройте консоль ошибок браузера и создайте {{link}}новый тикет о проблеме{{/link}} с деталями."], "If you are using a page caching plugin or service (CloudFlare, OVH, etc) then you can also try clearing that cache.": ["Если вы используете плагин кэширования страниц или услугу (cloudflare, OVH и т.д.), то вы также можете попробовать очистить кэш."], "Search Regex is not working. Try clearing your browser cache and reloading this page.": ["Search Regex не работает. Попробуйте очистить кеш браузера и перезагрузить эту страницу."], "clearing your cache.": ["очистить свой кэш."], "If you are using a caching system such as Cloudflare then please read this: ": ["Если вы используете систему кэширования, такую как cloudflare, пожалуйста, прочитайте это: "], "Please clear your browser cache and reload this page.": ["Очистите кеш браузера и перезагрузите эту страницу."], "Cached Search Regex detected": ["Обнаружено кеширование Search Regex"], "Show %s more": ["Show %s more", "Показать ещё %s", "Показать ещё %s", "Показать ещё %s"], "Maximum number of matches exceeded and hidden from view. These will be included in any replacements.": ["Превышено максимальное количество совпадений, и они скрыты от просмотра. Они будут включены в любые замены."], "Delete": ["Удалить"], "Edit": ["Изменить"], "Check Again": ["Проверить снова"], "Testing - %s%%": ["Тестирование - %s%%"], "Show Problems": ["Показать проблемы"], "Summary": ["Резюме"], "You are using a broken REST API route. Changing to a working API should fix the problem.": ["Используется поврежденный маршрут REST API. Переход на рабочий API должен устранить проблему."], "Your REST API is not working and the plugin will not be able to continue until this is fixed.": ["Ваш REST API не работает, и плагин не сможет продолжить работу, пока это не будет исправлено."], "There are some problems connecting to your REST API. It is not necessary to fix these problems and the plugin is able to work.": ["Возникли некоторые проблемы с подключением к вашему REST API. Эти проблемы устранять не нужно, и плагин может работать."], "Unavailable": ["Недоступен"], "Not working but fixable": ["Не работает, но можно исправить"], "Working but some issues": ["Работает, но есть некоторые проблемы"], "Good": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "Current API": ["Текущий API"], "Switch to this API": ["Переключиться на этот API"], "Hide": ["Скрыть"], "Show Full": ["Показать полностью"], "Working!": ["Работает!"], "Finished!": ["Завершено!"], "Cancel": ["Отменить"], "Replace": ["Заменить"], "Remove": ["Удалить"], "Multi": ["Мульти"], "Single": ["Одиночный"], "Support": ["Поддержка"], "Options": ["Параметры"], "Search & Replace": ["Поиск и Замена"], "If you are using WordPress 5.2 or newer then look at your {{link}}Site Health{{/link}} and resolve any issues.": ["Если вы используете WordPress 5.2 или новее, посмотрите раздел {{link}}Здоровье сайта{{/link}} для решения любых проблем."], "{{link}}Please temporarily disable other plugins!{{/link}} This fixes so many problems.": ["{{link}}Временно отключите другие плагины!{{/ link}} Это устраняет множество проблем."], "{{link}}Caching software{{/link}}, in particular Cloudflare, can cache the wrong thing. Try clearing all your caches.": ["{{link}}Кэширование программного обеспечения{{/link}},в частности Cloudflare, может кэшировать неправильные вещи. Попробуйте очистить все кэши."], "What do I do next?": ["Что мне делать дальше?"], "Something went wrong 🙁": ["Что-то пошло не так 🙁"], "Log out, clear your browser cache, and log in again - your browser has cached an old session.": ["Выйдите из системы, очистите кеш браузера и снова войдите в систему - ваш браузер кэшировал старую сессию."], "Reload the page - your current session is old.": ["Перезагрузите страницу - ваша текущая сессия устарела."], "Include these details in your report along with a description of what you were doing and a screenshot.": ["Включите эти сведения в свой отчет вместе с описанием того, что вы делали, и снимком экрана."], "Email": ["Email"], "Create An Issue": ["Открыть тикет о проблеме"], "Closed": ["Закрыто"], "Save": ["Сохранить"], "Unable to make request due to browser security. This is typically because your WordPress and Site URL settings are inconsistent, or the request was blocked by your site CORS policy.": ["Невозможно отправить запрос из-за безопасности браузера. Обычно это происходит потому, что настройки вашего WordPress и URL-адреса сайта несовместимы, или запрос был заблокирован политикой CORS вашего сайта."], "Possible cause": ["Возможная причина"], "This could be a security plugin, or your server is out of memory or has an external error. Please check your server error log": ["Это может быть плагин безопасности, или вашему серверу не хватает памяти, или произошла внешняя ошибка. Проверьте журнал ошибок вашего сервера"], "Your server has rejected the request for being too big. You will need to reconfigure it to continue.": ["Ваш сервер отклонил запрос, так как он слишком большой. Чтобы продолжить, вам нужно будет перенастроить его."], "Your REST API is probably being blocked by a security plugin. Please disable this, or configure it to allow REST API requests.": ["Ваш REST API, вероятно, блокируется плагином безопасности. Пожалуйста, отключите его или настройте так, чтобы он разрешал запросы REST API."], "Read this REST API guide for more information.": ["Прочтите это руководство по REST API для получения дополнительной информации."], "WordPress did not return a response. This could mean an error occurred or that the request was blocked. Please check your server error_log.": ["WordPress не вернул ответ. Это может означать, что произошла ошибка или что запрос был заблокирован. Пожалуйста, проверьте ваш error_log сервера."], "John Godley": ["<PERSON>"], "Adds search and replace functionality across posts, pages, comments, and meta-data, with full regular expression support": ["Добавляет функции поиска и замены в записях, страницах, комментариях и метаданных с полной поддержкой регулярных выражений"], "https://searchregex.com/": ["https://searchregex.com/"], "Search Regex": ["Search Regex"]}}, "translation-revision-date": "2024-01-01T17:14:07.416Z", "source": "search-regex", "domain": "search-regex", "generator": "Search Regex"}