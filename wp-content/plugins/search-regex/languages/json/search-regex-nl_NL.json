{"locale_data": {"search-regex": {"": {"plural-forms": "nplurals=2; plural=n != 1;"}, "matched rows = %(searched)s": ["overeenkomende rijen = %(gezocht)s"], "OK": ["OK"], "Version %s installed! Please read the {{url}}release notes{{/url}} for details.": ["Versie %s geïnstalleerd! Lees de {{url}}releaselog{{/url}} voor meer informatie."], "A problem occurred with your last query. This is likely caused by a combination of search filters that haven't been handled properly.": ["Er is een pro<PERSON>em opgetreden met je laatste query. Dit wordt waarschijnlijk veroorzaakt door een combinatie van zoekfilters die niet goed zijn verwerkt."], "Query Problem": ["Query probleem"], "Progress": ["Voortgang"], "%s row deleted.": ["%s rows deleted.", "%s rij verwijderd.", "%s rijen verwijderd."], "Refresh": ["<PERSON><PERSON><PERSON>"], "Matched Content": ["Overeenkomende inhoud"], "Your search resulted in too many requests. Please narrow your search terms.": ["Je zoek opdracht leidde tot te veel aanvragen. <PERSON><PERSON><PERSON> je zoek termen te verfijnen."], "Your search conditions have changed. Please refresh to see the latest results.": ["Je zoekvoorwaarden zijn gewijzigd. Vernieuw om de recentste resultaten te zien."], "Matched rows: %(matches)s out of %(total)s total.": ["Overeenkomende rijen: %(komt overeen)s van %(totaal)s totaal."], "Delete database row": ["Database rij verwijderen"], "View": ["Bekijken"], "No title": ["<PERSON><PERSON> titel"], "Empty value": ["Lege waarde"], "No value": ["<PERSON><PERSON> waarde"], "Click to replace match": ["Klik om overeenkomst te vervangen"], "Contains encoded data": ["<PERSON><PERSON> gecodeerde g<PERSON>s"], "New meta value": ["Nieuwe metawaarde"], "New meta key": ["Nieuwe metasle<PERSON>l"], "Apply to matches only": ["<PERSON><PERSON> van toe<PERSON> op overeenkomsten"], "Enter replacement": ["<PERSON><PERSON><PERSON> vervanging in"], "Click to replace column": ["Klik om de kolom te vervangen"], "This column contains special formatting. Modifying it could break the format.": ["Deze kolom bevat speciale lay-out. Als je het wijzigt, kan het format worden verbroken."], "HTML": ["HTML"], "Blocks": ["Blokken"], "Serialized PHP": ["Geserialiseerde PHP"], "Paste preset JSON.": ["Plak voorinstelling JSON."], "Global Search Flags": ["Globale zoekmarkeringen"], "Global Replace": ["Globale vervanging"], "Global Search": ["Globaal zoeken"], "View Columns": ["<PERSON><PERSON><PERSON><PERSON> be<PERSON>jken"], "Sources": ["B<PERSON>nen"], "Only include selected columns": ["Alleen geselecteerde kolommen opnemen"], "WordPress Action": ["WordPress actie"], "SQL": ["SQL"], "CSV": ["CSV"], "JSON": ["JSON"], "Export Format": ["Export format"], "Run a WordPress action for each matching result.": ["Voer een WordPress actie uit voor elk overeenkomend resultaat."], "Run Action": ["<PERSON><PERSON>"], "Delete matching results.": ["Verwijder overeenkomende resultaten."], "Delete Matches": ["Overeenkomsten verwijderen"], "Export matching results to JSON, CSV, or SQL.": ["Exporteer overeen<PERSON>men<PERSON> resultaten naar JSON, CSV of SQL."], "Export Matches": ["Overeenkomsten exporteren"], "Perform changes to specific values of the matching results.": ["V<PERSON>r wijzigingen uit in specifieke waarden van de overeenkomende resultaten."], "Modify Matches": ["Overeenkomsten wijzigen"], "Replace the global search values.": ["Vervang de algemene zoek waarden."], "Global Text Replace": ["Algemene tekst vervangen"], "Just show matching results.": ["Laat alleen overeenkomende resultaten zien."], "No action": ["<PERSON><PERSON> actie"], "Enter replacement value": ["Vervangingswaarde invoeren"], "Matched values will be removed": ["Overeenkomende waarden worden verwijderd"], "Replace \"%1s\"": ["Vervang \"%1s\""], "Year": ["Jaar"], "Months": ["<PERSON><PERSON><PERSON>"], "Weeks": ["<PERSON><PERSON>"], "Days": ["Dagen"], "Hours": ["<PERSON><PERSON>"], "Minutes": ["Minuten"], "Seconds": ["Seconden"], "Decrement": ["Verlagen"], "Increment": ["<PERSON>er<PERSON><PERSON>"], "Set Value": ["Waarde instellen"], "Replace With": ["Vervangen door"], "Add": ["Toevoegen"], "Column": ["<PERSON><PERSON><PERSON>"], "AND": ["EN"], "Filters": ["Filters"], "Add sub-filter (OR)": ["Sub filter toevoegen (OR)"], "OR": ["OF"], "All": ["Alle"], "between {{first/}} and {{second/}}": ["tussen {{first/}} en {{second/}}"], "No Owner": ["<PERSON><PERSON> eige<PERSON>"], "Has Owner": ["<PERSON><PERSON>t eigenaar"], "Any": ["<PERSON><PERSON>"], "Excludes any": ["Sluit alle"], "Includes any": ["Inclusief elke"], "End": ["Einde"], "Begins": ["Begint"], "Not contains": ["<PERSON><PERSON> niet"], "Contains": ["<PERSON><PERSON>"], "Range": ["<PERSON><PERSON><PERSON>"], "Less": ["<PERSON><PERSON>"], "Greater": ["<PERSON><PERSON><PERSON>"], "Not Equals": ["<PERSON> niet gelijk"], "Equals": ["<PERSON><PERSON><PERSON> aan"], "Optional global search phrase. Leave blank to use filters only.": ["Optionele algemene zoekzin. Laat leeg om alleen filters te gebruiken."], "REST API 404": ["REST API 404"], "Your REST API appears to be cached and this will cause problems. Please exclude your REST API from your caching system.": ["Je REST API lijkt in de cache te zijn opgeslagen en dit veroorzaakt problemen. Sluit je REST API uit van je caching."], "Your REST API is showing a deprecated PHP error. Please fix this error.": ["Je REST API toont een verouderde PHP fout. Los deze fout op."], "Your server configuration is blocking access to the REST API.": ["Je serverconfiguratie blok<PERSON>ert de toegang tot de REST API."], "Can you access your {{api}}REST API{{/api}} without it redirecting?.": ["Kun je toegang krijgen tot je {{api}}REST API{{/api}} zonder dat het omleidt?."], "You will will need to fix this on your site. Redirection is not causing the error.": ["Je moet dit oplossen op je site. Omleiden veroorzaakt de fout niet."], "Preset uploaded": ["Voorinstelling geüpload"], "Multiline": ["Multiline"], "Case": ["Case"], "Regex": ["Regex"], "Nginx": ["<PERSON><PERSON><PERSON>"], "Apache": ["Apache"], "WordPress": ["WordPress"], "Module": ["<PERSON><PERSON><PERSON>"], "Redirection Groups": ["Omleiding groepen"], "Not accessed": ["<PERSON><PERSON>"], "Target": ["<PERSON><PERSON>"], "HTTP Code": ["HTTP code"], "Position": ["<PERSON><PERSON><PERSON>"], "Disabled": ["Uitgeschakeld"], "Enabled": ["Ingeschakeld"], "Status": ["Status"], "Group": ["<PERSON><PERSON><PERSON>"], "Last Access": ["Laatste toegang"], "Hit Count": ["A<PERSON>al treffers"], "Source URL (matching)": ["Bron URL (overeenkomend)"], "Source URL": ["Bron URL"], "User meta": ["Gebruiker meta"], "Registered": ["<PERSON><PERSON><PERSON><PERSON><PERSON>"], "Login": ["<PERSON><PERSON>"], "Taxonomy": ["Taxonomie"], "Post Tag": ["Bericht tag"], "Post Category": ["Bericht categorie"], "Comment Count": ["Aantal reacties"], "MIME": ["MIME"], "Parent": ["Hoofd"], "Modified GMT": ["Aangepaste GMT"], "Modified": ["Aangepast"], "Password": ["Wachtwoord"], "Has no password": ["He<PERSON>t geen wachtwoord"], "Has password": ["<PERSON><PERSON><PERSON> wachtwoord"], "Ping Status": ["Ping status"], "Comment Status": ["Reactie status"], "Open": ["Open"], "Post Status": ["<PERSON><PERSON><PERSON><PERSON><PERSON>"], "Post Type": ["<PERSON><PERSON><PERSON><PERSON>"], "Autoload": ["Automatisch laden"], "Is not autoload": ["Wordt niet automatisch geladen"], "Is autoload": ["Is automatisch geladen"], "Meta Value": ["Metawaarde"], "Meta Key": ["<PERSON><PERSON> sleutel"], "Owner ID": ["<PERSON><PERSON>"], "Comment meta": ["Reactie meta"], "User ID": ["Gebruikers ID"], "Parent Comment": ["Hoofd reactie"], "Trackback": ["Trackback"], "Pingback": ["<PERSON><PERSON>"], "Type": ["Type"], "User Agent": ["User agent"], "Approval Status": ["Toestemming status"], "Spam": ["Spam"], "Approved": ["Toegestaan"], "Unapproved": ["<PERSON><PERSON><PERSON><PERSON><PERSON>"], "Date GMT": ["Datum GMT"], "Date": ["Datum"], "IP": ["IP"], "Author": ["<PERSON><PERSON><PERSON>"], "Post ID": ["Bericht ID"], "ID": ["ID"], "Terms": ["Voorwaarden"], "Posts (core & custom)": ["Be<PERSON>ten (core & aangepast)"], "Please review your data and try again.": ["Beoor<PERSON>el je gegevens en probeer het opnieuw."], "There was a problem making a request to your site. This could indicate you provided data that did not match requirements, or that the plugin sent a bad request.": ["Er is een probleem opgetreden bij het indienen van een aanvraag aan je site. Dit kan erop wijzen dat je gegevens heeft verstrekt die niet aan de vereisten voldeden, of dat de plugin een slechte aanvraag heeft verzonden."], "Bad data": ["Slechte gegevens"], "Your WordPress REST API is returning a 404 page. This is almost certainly an external plugin or server configuration issue.": ["Je WordPress REST API retourneert een 404 pagina. Di<PERSON> is vrijwel zeker een probleem met de externe plugin of serverconfiguratie."], "2000 per page": ["2000 per pagina"], "1000 per page": ["1000 per pagina"], "Set a preset to use by default when Search Regex is loaded.": ["Stel een voorinstelling in die standaard wordt gebruikt wanneer Search Regex wordt geladen."], "No default preset": ["<PERSON><PERSON> standaard voorinstelling"], "Default Preset": ["Standaard voorinstelling"], "Your admin pages are being cached. Clear this cache and try again. There may be multiple caches involved.": ["<PERSON> beheer pagina's worden in de cache opgeslagen. Wis deze cache en probeer het opnieuw. Mogelijk zijn er meerdere caches bij betrokken."], "This is usually fixed by doing one of the following:": ["Dit wordt meestal opgelost door een van de volgende handelingen uit te voeren:"], "You are using an old or cached session": ["Je gebruikt een oude of cache sessie"], "Debug Information": ["Debug informatie"], "Show debug": ["Toon debug info"], "WordPress returned an unexpected message. This could be a PHP error from another plugin, or data inserted by your theme.": ["WordPress heeft een onverwacht bericht geretourneerd. Dit kan een PHP fout zijn van een andere plugin of gegevens die door je thema zijn ingevoegd."], "Your WordPress REST API has been disabled. You will need to enable it to continue.": ["Je WordPress REST API is uitgeschakeld. Je moet het inschakelen om door te gaan."], "Your REST API is being redirected. Please remove the redirection for the API.": ["Je REST API wordt omgeleid. Verwijder de omleiding voor de API."], "A security plugin or firewall is blocking access. You will need to whitelist the REST API.": ["Een beveiliging plugin of firewall blokkeert de toegang. Je moet de REST API op de toegestane lijst zetten."], "Check your {{link}}Site Health{{/link}} and fix any issues.": ["Controleer je {{link}}sitediagnose{{/link}} en los eventuele problemen op."], "Preset": ["Vooraf gemaakte instelling"], "Upload": ["Upload"], "Add file": ["Bestand toevoegen"], "Preset saved": ["Voorinstelling opgeslagen"], "Copy to clipboard": ["<PERSON><PERSON><PERSON> naar klembord"], "Are you sure you want to delete this preset?": ["Weet je zeker dat je deze voorinstelling wil verwijderen?"], "Locked fields": ["Vergrendelde velden"], "For example, create tag {{code}}URL{{/code}} and title {{code}}Image URL{{/code}}. Your search could be {{code}}<img src=\"URL\">{{/code}}. When the preset is used it will ask the user for the {{code}}Image URL{{/code}} instead of the full search phrase.": ["Maak bijvoorbeeld de tag {{code}}URL{{/code}} en de titel {{code}} afbeelding URL{{/code}}. Je zoekopdracht kan {{code}}<img src=\"URL\">{{/code}} zijn. Wanneer de voorinstelling wordt gebruikt, wordt de gebruiker gevraagd om de {{code}}afbeelding URL{{/code}} In plaats van de volledige zoekterm."], "A tag creates a custom input field. Insert the tag anywhere in the search, replace, text filter, or text action and when the preset is used it will be replaced with a custom text field with the tag label.": ["Een tag maakt een aangepast invoerveld. Voeg de tag ergens in de zoek-, vervang-, tekst filter- of tekst actie in en wanneer de voorinstelling wordt gebruikt, wordt deze vervangen door een aangepast tekstveld met het tag label."], "Enter tag which is used in the field": ["<PERSON><PERSON><PERSON> de tag in die in het veld wordt gebruikt"], "Tag": ["Tag"], "Enter tag title shown to user": ["<PERSON><PERSON><PERSON> de tag titel in die aan de gebruiker wordt getoond"], "Tags": ["Tags"], "Locking a field removes it from the search form and prevents changes.": ["Als je een veld vergrendelt, wordt het uit het zoek formulier verwijderd en worden wijzigingen voorkomen."], "Fields": ["<PERSON><PERSON><PERSON>"], "Locked Fields": ["Vergrendelde velden"], "Advanced preset": ["Geavanceerde voorinstelling"], "Describe the preset": ["<PERSON><PERSON><PERSON><PERSON><PERSON>"], "Preset Description": ["Vooraf ingestelde beschrijving"], "Give the preset a name": ["<PERSON><PERSON>ling een naam"], "Preset Name": ["Vooraf ingestelde naam"], "Edit preset": ["Bewerk voorinstelling"], "Results per page": ["Resultaten per pagina"], "remove phrase": ["zin verwijderen"], "no phrase": ["geen zin"], "Import": ["Import"], "Please check your JSON data is a valid preset. You may have copied it incorrectly, or pasted something that is not a preset.": ["Controleer of je JSON gegevens een geldige voorinstelling is. Je kunt het verkeerd hebben gekopieerd, of iets hebben geplakt dat geen voorinstelling is."], "Unable to import preset": ["<PERSON>n voorinstelling niet importeren"], "Import preset from clipboard": ["Importeer voorinstelling van klembord"], "Done": ["<PERSON><PERSON><PERSON>"], "Uploaded %(total)d preset": ["Uploaded %(total)d presets", "Geüploade %(total)d voorinstelling", "Geüploade %(total)d voorinstellingen"], "Importing": ["Importeren"], "File selected": ["<PERSON><PERSON> geselecteerd"], "Click 'Add File' or drag and drop here.": ["<PERSON><PERSON> op 'Bestand toevoegen' of sleep het hier naartoe."], "Import a JSON file": ["<PERSON><PERSON> bestand importeren"], "Import JSON": ["JSON importeren"], "Export JSON": ["JSON exporteren"], "Download presets!": ["Download voorinstellingen!"], "There are no presets": ["<PERSON>r zijn geen voorinstellingen"], "Flags": ["Vlaggen"], "Presets": ["Voorinstellingen"], "If that did not help then {{strong}}create an issue{{/strong}} or send it in an {{strong}}email{{/strong}}.": ["Als dat niet heeft geholpen, {{strong}}maak dan een probleem aan{{/strong}} of stuur het in een {{strong}}e-mail{{/strong}}."], "Please check the {{link}}support site{{/link}} before proceeding further.": ["<PERSON><PERSON><PERSON><PERSON> de {{link}}ondersteuning site{{/link}} voor het verder gaan."], "Enter preset name": ["<PERSON><PERSON> van de <PERSON> in"], "Enter a name for your preset": ["Vul een naam in voor je voorinstelling"], "Saving Preset": ["Voorinstelling opslaan"], "Update current preset": ["Update huidige voorinstelling"], "Save search as new preset": ["Zoekopdracht opslaan als nieuw voorinstelling"], "No preset": ["<PERSON><PERSON> v<PERSON><PERSON>ling"], "Saving preset": ["Voorinstelling opslaan"], "Advanced": ["Geavanceerd"], "Standard": ["Standaard"], "Please backup your data before making modifications.": ["<PERSON><PERSON> eerst een back-up van je g<PERSON>, voordat je wijzigen gaat maken."], "%s row.": ["%s rows.", "%s rij.", "%s rijen."], "An unknown error occurred.": ["Er is een onbekende fout opgetreden."], "GUID": ["GUID"], "Slug": ["Slug"], "Excerpt": ["<PERSON><PERSON><PERSON><PERSON>"], "Content": ["<PERSON><PERSON><PERSON>"], "Display Name": ["Weergavenaam"], "Nicename": ["<PERSON><PERSON><PERSON><PERSON><PERSON> (backend)"], "Value": ["<PERSON><PERSON><PERSON>"], "Comment": ["<PERSON><PERSON><PERSON>"], "Name": ["<PERSON><PERSON>"], "Title": ["Titel"], "URL": ["URL"], "Disabled! Detected PHP %1$s, need PHP %2$s+": ["Uitgeschakeld! Gedetecteerd PHP %1$s, nodig PHP %2$s+"], "Plugins": ["Plugins"], "WordPress Options": ["WordPress opties"], "User Meta": ["<PERSON><PERSON><PERSON><PERSON><PERSON>"], "Users": ["Gebruikers"], "Comment Meta": ["<PERSON><PERSON><PERSON>"], "Comments": ["Reacties"], "Post Meta": ["Bericht meta"], "Please enable JavaScript": ["Schakel JavaScript in"], "Loading, please wait...": ["Aan het laden..."], "Create Issue": ["Meld een probleem"], "<code>SearchRegexi10n</code> is not defined. This usually means another plugin is blocking Search Regex from loading. Please disable all plugins and try again.": ["<code>SearchRegexi10n</code> is niet gedefinieerd. Dit betekent meestal dat een andere plugin Search Regex blokkeert om te laden. Zet alle plugins uit en probeer het opnieuw."], "If you think Search Regex is at fault then create an issue.": ["Denk je dat Search Regex het probleem veroorzaakt, maak dan een probleemrapport aan."], "Please see the <a href=\"https://searchregex.com/support/problems/\">list of common problems</a>.": ["<PERSON><PERSON><PERSON> hier de <a href=\"https://searchregex.com/support/problems/\">lijst van algemene problemen</a>."], "Please note that Search Regex requires the WordPress REST API to be enabled. If you have disabled this then you won't be able to use Search Regex": ["Search Regex vereist dat de WordPress REST API ingeschakeld is. Heb je deze uitgeschakeld, dan kun je Search Regex niet gebruiken"], "Also check if your browser is able to load <code>search-regex.js</code>:": ["Controleer ook of je browser <code>search-regex.js</code> kan laden:"], "This may be caused by another plugin - look at your browser's error console for more details.": ["Dit kan worden veroorzaakt door een andere plugin - bekijk je browser's foutconsole voor meer gegevens."], "Unable to load Search Regex ☹️": ["Laden van Search Regex ☹️ onmogelijk"], "Unable to load Search Regex": ["Laden van Search Regex onmogelijk"], "Search Regex requires WordPress v%1$1s, you are using v%2$2s - please update your WordPress": ["Search Regex heeft WordPress v%1$1s nodig, en je gebruikt v%2$2s - update je WordPress"], "Search Regex Support": ["Search Regex ondersteuning"], "You can find full documentation about using Search Regex on the <a href=\"%s\" target=\"_blank\">searchregex.com</a> support site.": ["Je kunt de volledige documentatie over het gebruik van Search Regex vinden op de <a href=\"%s\" target=\"_blank\">searchregex.com</a> support site."], "Settings": ["Instellingen"], "Action": ["<PERSON><PERSON>"], "Row ID": ["Regel ID"], "No more matching results found.": ["<PERSON><PERSON> resultaten meer gevonden."], "Last page": ["Laatste pagina"], "Page %(current)s of %(total)s": ["Pagina %(current)s van %(total)s"], "Next page": ["Volgende pagina"], "Progress %(current)s%%": ["Voortuitgang %(current)s%%"], "Prev page": ["Vorige pagina"], "First page": ["Eerste pagina"], "%s database row in total": ["%s database rows in total", "%s database regel totaal", "%s database regels totaal"], "500 per page": ["500 per pagina"], "250 per page": ["250 per pagina"], "100 per page": ["100 per pagina"], "50 per page": ["50 per pagina"], "25 per page": ["25 per pagina"], "Ignore Case": ["<PERSON><PERSON><PERSON> hoof<PERSON>tter gebruik"], "Regular Expression": ["Reguliere expressie"], "Row updated": ["Regel geüpdatet"], "Row replaced": ["Regel vervangen"], "Row deleted": ["Regel verwijderd"], "Settings saved": ["Instellingen opgeslagen"], "{{link}}Source Flags{{/link}} - additional options for the selected source. For example, include post {{guid}}GUID{{/guid}} in the search.": ["{{link}}Zoekopties{{/link}} - aanvullende opties voor de selecteerde bron. Bijvoorbeeld\nom bericht {{guid}}GUID{{/guid}} toe te voegen aan de zoekterm."], "{{link}}Source{{/link}} - the source of data you wish to search. For example, posts, pages, or comments.": ["{{link}}<PERSON>ron{{/link}} - de <PERSON><PERSON> van de g<PERSON> waarin je zoekt. Bijvoorbeeld: berichten, pagina's, reacties."], "{{link}}Regular expression{{/link}} - a way of defining a pattern for text matching. Provides more advanced matches.": ["{{link}}Reguliere expressie{{/link}} - een manier om een patroon aan te maken om tekst mee te zoeken. Geeft meer geavanceerde zoekresultaten."], "{{link}}Search Flags{{/link}} - additional qualifiers for your search, to enable case insensitivity, and to enable regular expression support.": ["{{link}}Zoekopties{{/link}} - aanvullende opties voor je zoekopdracht, om hoofdlettergebruik te negeren en om reguliere expressie ondersteuning in te schakelen."], "The following concepts are used by Search Regex:": ["De volgende concepten worden gebruikt door Search Regex:"], "Like this plugin? You might want to consider {{link}}Redirection{{/link}}, a plugin to manage redirects, from the same author.": ["Vind je deze plugin leuk? Je kunt overwegen {{link}}Redirection{{/link}} , een plugin om omleidingen te beheren, van dezelfde auteur."], "Redirection": ["Omleiding"], "If you want to submit information that you don't want in a public repository then send it directly via {{email}}email{{/email}} - include as much information as you can!": ["Als je informatie wil sturen, maar die je niet in de openbare repository wil delen, stuur dan een {{email}}e-mail{{/email}} direct aan mij - met zoveel informatie als je kunt!"], "Please note that any support is provide on as-time-is-available basis and is not guaranteed. I do not provide paid support.": ["Support wordt gegeven op basis van beschikbare tijd en is niet gegaran<PERSON>erd. Ik geef geen betaalde ondersteuning."], "If you want to report a bug please read the {{report}}Reporting Bugs{{/report}} guide.": ["Wil je een bug doorgeven, lees dan de {{report}}Reporting Bugs{{/report}} gids."], "Full documentation for Search Regex can be found at {{site}}https://searchregex.com{{/site}}.": ["Volledige documentatie voor Search Regex kan je vinden op {{site}}https://searchregex.com{{/site}}."], "Need more help?": ["Meer hulp nodig?"], "Results": ["Resultaten"], "Source": ["<PERSON><PERSON>"], "Enter search phrase": ["Zoekterm invoeren"], "Replace All": ["Alles vervangen"], "Search": ["<PERSON><PERSON>"], "Search and replace information in your database.": ["Zoek en vervang informatie in je database."], "Update": ["Update"], "How Search Regex uses the REST API - don't change unless necessary": ["Hoe Search Regex de REST API gebruikt - niet veranderen als het niet noodzakelijk is"], "REST API": ["REST API"], "I'm a nice person and I have helped support the author of this plugin": ["<PERSON>k ben een aardig persoon en ik heb de auteur van deze plugin geholpen met ondersteuning"], "Relative REST API": ["Relatieve REST API"], "Raw REST API": ["Raw REST API"], "Default REST API": ["Standaard REST API"], "Plugin Support": ["Plug<PERSON> on<PERSON><PERSON>"], "Support 💰": ["Ondersteuning 💰"], "You get useful software and I get to carry on making it better.": ["<PERSON> krijgt goed bruikbare software en ik kan doorgaan met het verbeteren ervan."], "Search Regex is free to use - life is wonderful and lovely! It has required a great deal of time and effort to develop and you can help support this development by {{strong}}making a small donation{{/strong}}.": ["Search Regex is gratis te gebruiken - het leven is life is wonder<PERSON>ar<PERSON><PERSON> en verrukkelijk! Het kostte me veel tijd en moeite om dit te ontwikkelen. Je kunt verdere ontwikkeling ondersteunen met het doen van {{strong}}een kleine donatie{{/strong}}."], "I'd like to support some more.": ["<PERSON>k wil graag meer bijdragen."], "You've supported this plugin - thank you!": ["Je hebt deze plugin gesteund - dank<PERSON>wel!"], "If that doesn't help, open your browser's error console and create a {{link}}new issue{{/link}} with the details.": ["<PERSON>rkt dit niet, open dan je browser's foutconsole en maak een {{link}}nieuw probleemrapport{{/link}} aan met alle gegevens."], "If you are using a page caching plugin or service (CloudFlare, OVH, etc) then you can also try clearing that cache.": ["Als je gebruik maakt van een pagina caching plugin of dienst (CloudFlare, OVH, enz.), dan kan je ook proberen om die cache leeg te maken."], "Search Regex is not working. Try clearing your browser cache and reloading this page.": ["Search Regex werkt niet. Probeer je browser cache leeg te maken en deze pagina opnieuw te laden."], "clearing your cache.": ["je cache opschonen."], "If you are using a caching system such as Cloudflare then please read this: ": ["Gebruik je een caching systeem zoals <PERSON>, lees dan dit:"], "Please clear your browser cache and reload this page.": ["Maak je browser cache leeg en laad deze pagina nogmaals."], "Cached Search Regex detected": ["Gecachede Search Regex gevonden"], "Show %s more": ["Show %s more", "Nog %s weergeven", "Nog %s weergeven"], "Maximum number of matches exceeded and hidden from view. These will be included in any replacements.": ["Het maximum aantal zoekresultaten is overschreden en niet zichtba<PERSON>. Deze zullen \nworden gebruikt bij vervangingen."], "Delete": ["Verwijderen"], "Edit": ["Bewerken"], "Check Again": ["Opnieuw controleren"], "Testing - %s%%": ["Aan het testen - %s%%"], "Show Problems": ["Toon problemen"], "Summary": ["<PERSON><PERSON><PERSON><PERSON>"], "You are using a broken REST API route. Changing to a working API should fix the problem.": ["Je gebruikte een defecte REST API route. Wijziging naar een werkende API zou het probleem moeten oplossen."], "Your REST API is not working and the plugin will not be able to continue until this is fixed.": ["Je REST API werkt niet en de plugin kan niet verder voordat dit is opgelost."], "There are some problems connecting to your REST API. It is not necessary to fix these problems and the plugin is able to work.": ["<PERSON>r zijn enkele problemen in de verbinding met je REST API. Het is niet nodig om deze problemen op te lossen en de plugin werkt gewoon."], "Unavailable": ["<PERSON><PERSON>"], "Not working but fixable": ["<PERSON><PERSON>t niet, maar te repareren"], "Working but some issues": ["<PERSON><PERSON><PERSON>, maar met problemen"], "Good": ["Goed"], "Current API": ["Huidige API"], "Switch to this API": ["Gebruik deze API"], "Hide": ["Ver<PERSON>"], "Show Full": ["<PERSON>n volledig"], "Working!": ["Werkt!"], "Finished!": ["<PERSON><PERSON><PERSON>!"], "Cancel": ["<PERSON><PERSON><PERSON>"], "Replace": ["Vervangen"], "Remove": ["Verwijderen"], "Multi": ["<PERSON><PERSON><PERSON>"], "Single": ["<PERSON><PERSON>"], "Support": ["Ondersteuning"], "Options": ["Opties"], "Search & Replace": ["Zoek en vervang"], "If you are using WordPress 5.2 or newer then look at your {{link}}Site Health{{/link}} and resolve any issues.": ["Als je WordPress 5.2 of nieuwer gebruikt, kijk dan bij {{link}}Sitediagnose{{/link}} en los de problemen op."], "{{link}}Please temporarily disable other plugins!{{/link}} This fixes so many problems.": ["{{link}}Zet andere plugins tijdelijk uit!{{/link}} Dit lost heel vaak problemen op."], "{{link}}Caching software{{/link}}, in particular Cloudflare, can cache the wrong thing. Try clearing all your caches.": ["{{link}}Caching software{{/link}}, en zeker Cloudflare, kunnen het verkeerde cachen. Probeer alle cache te verwijderen."], "What do I do next?": ["Wat moet ik nu doen?"], "Something went wrong 🙁": ["Er is iets fout gegaan 🙁"], "Log out, clear your browser cache, and log in again - your browser has cached an old session.": ["Log uit, wis de browsercache en log opnieuw in - je browser heeft een oude sessie in de cache opgeslagen."], "Reload the page - your current session is old.": ["<PERSON><PERSON><PERSON> de pagina - je huidige sessie is oud."], "Include these details in your report along with a description of what you were doing and a screenshot.": ["Voeg deze gegevens toe aan je melding, samen met een beschrij<PERSON> van wat je deed en een schermafbeelding."], "Email": ["E-mail"], "Create An Issue": ["Meld een probleem"], "Closed": ["Gesloten"], "Save": ["Opsla<PERSON>"], "Unable to make request due to browser security. This is typically because your WordPress and Site URL settings are inconsistent, or the request was blocked by your site CORS policy.": ["<PERSON><PERSON> m<PERSON>jk om een verzoek te doen vanwege browser beveiliging. Dit gebeurt meestal omdat WordPress en de site URL niet overeenkomen, of het verzoek wordt geblokkeerd door het CORS beleid van je site."], "Possible cause": ["Mogelijke oorzaak"], "This could be a security plugin, or your server is out of memory or has an external error. Please check your server error log": ["Dit kan een beveiliging plugin zijn. Of je server heeft geen geheugen meer of heeft een externe fout. Bekijk de fout log op je server"], "Your server has rejected the request for being too big. You will need to reconfigure it to continue.": ["Je server heeft de aanvraag geweigerd omdat het te groot is. Je moet het aanpassen om door te gaan."], "Your REST API is probably being blocked by a security plugin. Please disable this, or configure it to allow REST API requests.": ["Je REST API wordt waarschijnlijk geblokkeerd door een beveiliging plugin. Zet de plugin uit, of configureer hem zodat hij REST API verzoeken toestaat."], "Read this REST API guide for more information.": ["<PERSON>s deze REST API gids voor meer informatie."], "WordPress did not return a response. This could mean an error occurred or that the request was blocked. Please check your server error_log.": ["WordPress heeft geen reactie teruggestuurd. Dit kan betekenen dat er een fout is opgetreden of dat de aanvraag is geblokkeerd. Controleer je server error_log."], "John Godley": ["<PERSON>"], "Adds search and replace functionality across posts, pages, comments, and meta-data, with full regular expression support": ["Voeg zoek en vervang functionaliteit toe in berichten, pagina's, reacties en metadata, met volledige ondersteuning van reguliere expressies"], "https://searchregex.com/": ["https://searchregex.com/"], "Search Regex": ["Search Regex"]}}, "translation-revision-date": "2024-01-01T17:14:07.413Z", "source": "search-regex", "domain": "search-regex", "generator": "Search Regex"}