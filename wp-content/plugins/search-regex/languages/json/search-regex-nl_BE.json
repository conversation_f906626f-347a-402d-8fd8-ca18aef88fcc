{"locale_data": {"search-regex": {"": {"plural-forms": "nplurals=2; plural=n != 1;"}, "Serialized Value": [], "Exclude drafts": [], "Please review your data and try again.": [], "There was a problem making a request to your site. This could indicate you provided data that did not match requirements, or that the plugin sent a bad request.": [], "Bad data": [], "Your REST API is returning a 404 page. This is almost certainly an external plugin or server configuration issue.": [], "2000 per page": [], "1000 per page": [], "Set a preset to use by default when Search Regex is loaded.": [], "No default preset": [], "Default Preset": [], "Your admin pages are being cached. Clear this cache and try again. There may be multiple caches involved.": [], "This is usually fixed by doing one of the following:": [], "You are using an old or cached session": [], "Debug Information": [], "Show debug": [], "WordPress returned an unexpected message. This could be a PHP error from another plugin, or data inserted by your theme.": [], "Your WordPress REST API has been disabled. You will need to enable it to continue.": [], "Your REST API is being redirected. Please remove the redirection for the API.": [], "A security plugin or firewall is blocking access. You will need to whitelist the REST API.": [], "Your server configuration is blocking access to the REST API. You will need to fix this.": [], "Check your {{link}}Site Health{{/link}} and fix any issues.": [], "Can you access your {{api}}REST API{{/api}} without it redirecting? If not then you will need to fix any issues.": [], "Preset": [], "Upload": [], "Add File": [], "Preset saved": [], "Copy to clipboard": [], "Are you sure you want to delete this preset?": [], "Locked fields": [], "For example, create tag {{code}}URL{{/code}} and title {{code}}Image URL{{/code}}. Your search could be {{code}}<img src=\"URL\">{{/code}}. When the preset is used it will ask the user for the {{code}}Image URL{{/code}} instead of the full search phrase.": [], "A tag creates a custom input field. Insert the tag anywhere in the search or replace field and when the preset is used it will be replaced with a custom text field with the tag label.": [], "Enter tag which is used in the field": [], "Tag": [], "Enter tag title shown to user": [], "Tags": [], "Locking a field removes it from the search form and prevents changes.": [], "Fields": [], "Locked Fields": [], "Advanced preset": [], "Describe the preset": [], "Preset Description": [], "Give the preset a name": [], "Preset Name": [], "Edit preset": [], "Results per page": [], "Source Flags": [], "remove phrase": [], "no phrase": [], "Import": [], "Paste a single preset JSON.": [], "Please check your JSON data is a valid preset. You may have copied it incorrectly, or pasted something that is not a preset.": [], "Unable to import preset": [], "Import preset from clipboard": [], "Done": [], "Uploaded %(total)d preset": ["Uploaded %(total)d presets"], "Importing": [], "File selected": [], "Click 'Add File' or drag and drop here.": [], "Import a JSON file": [], "Import JSON": [], "Export JSON": [], "Download presets!": [], "There are no presets": [], "Flags": [], "Presets": [], "If that did not help then {{strong}}create an issue{{/strong}} or send it in an {{strong}}email{{/strong}}.": [], "Please check the {{link}}support site{{/link}} before proceeding further.": [], "Enter preset name": [], "Enter a name for your preset": [], "Saving Preset": [], "Update current preset": [], "Save search as new preset": [], "No preset": [], "Saving preset": [], "Advanced": [], "Standard": [], "matched rows = %(searched)s, phrases = %(found)s": [], "Please backup your data before making modifications.": [], "Show row actions as dropdown menu.": [], "Replace Row": [], "Delete Row": [], "Inline Editor": [], "Edit Page": [], "%s row.": ["%s rows."], "%s phrase.": ["%s phrases."], "Replace Information": [], "An unknown error occurred.": [], "GUID": ["GUID"], "Slug": ["Slug"], "Excerpt": ["<PERSON><PERSON><PERSON><PERSON>"], "Content": ["<PERSON><PERSON><PERSON>"], "Search GUID": ["Zoek GUID"], "Display name": ["Weergavenaam"], "Nicename": ["<PERSON><PERSON><PERSON><PERSON><PERSON> (backend)"], "Value": ["<PERSON><PERSON><PERSON>"], "Include spam comments": ["Inclusief spam reacties"], "Comment": ["<PERSON><PERSON><PERSON>"], "Name": ["<PERSON><PERSON>"], "Search your redirects": ["Zoe<PERSON> je redirects"], "Title": ["Titel"], "URL": ["URL"], "Disabled! Detected PHP %1$s, need PHP %2$s+": ["Uitgeschakeld! Gedetecteerd PHP %1$s, nodig PHP %2$s+"], "Plugins": ["Plugins"], "Specific Post Types": ["Specifieke berichttypen"], "Search all WordPress options.": ["Doorzoek alle WordPress opties"], "WordPress Options": ["WordPress opties"], "Search user meta name and values.": ["<PERSON><PERSON> geb<PERSON>r gegevens naam en waarden."], "User Meta": ["<PERSON><PERSON><PERSON><PERSON><PERSON>"], "Search user email, URL, and name.": ["<PERSON><PERSON> e-mail, URL en naam van gebruiker."], "Users": ["Gebruikers"], "Search comment meta names and values.": ["Zoek reactie meta namen en waarden"], "Comment Meta": ["<PERSON><PERSON><PERSON>"], "Search comment content, URL, and author, with optional support for spam comments.": ["Doorzoek reactie inhoud, URL en auteur, met optie voor spam reacties. "], "Comments": ["Reacties"], "Search post meta names and values.": ["<PERSON><PERSON> bericht meta namen en waarden"], "Post Meta": ["Bericht meta"], "Search all posts, pages, and custom post types.": ["Doorzoek alle berichten, pagina's en aangepaste bericht typen."], "All Post Types": ["<PERSON>e berich<PERSON>pen"], "Please enable JavaScript": ["<PERSON><PERSON><PERSON> in"], "Loading, please wait...": ["Aan het laden..."], "Create Issue": ["Meld een probleem"], "<code>SearchRegexi10n</code> is not defined. This usually means another plugin is blocking Search Regex from loading. Please disable all plugins and try again.": ["<code>SearchRegexi10n</code> is niet gedefinieerd. Dit betekent meestal dat een andere plugin Search Regex blokkeert om te laden. Zet alle plugins uit en probeer het opnieuw."], "If you think Search Regex is at fault then create an issue.": ["Denk je dat Search Regex het probleem veroorzaakt, maak dan een probleemrapport aan."], "Please see the <a href=\"https://searchregex.com/support/problems/\">list of common problems</a>.": ["<PERSON><PERSON><PERSON> hier de <a href=\"https://searchregex.com/support/problems/\">lijst van algemene problemen</a>."], "Please note that Search Regex requires the WordPress REST API to be enabled. If you have disabled this then you won't be able to use Search Regex": ["Search Regex vereist dat de WordPress REST API geactiveerd is. Heb je deze uitgezet, dan kun je Search Regex niet gebruiken."], "Also check if your browser is able to load <code>search-regex.js</code>:": ["Controleer ook of je browser <code>search-regex.js</code> kan laden:"], "This may be caused by another plugin - look at your browser's error console for more details.": ["Dit kan worden veroorzaakt door een andere plugin - bekijk je browser's foutconsole voor meer gegevens."], "Unable to load Search Regex ☹️": ["Laden van Search Regex ☹️ onmogelijk"], "Unable to load Search Regex": ["Laden van Search Regex onmogelijk"], "Search Regex requires WordPress v%1$1s, you are using v%2$2s - please update your WordPress": ["Search Regex heeft WordPress v%1$1s nodig, en je gebruikt v%2$2s - update je WordPress"], "Search Regex Support": ["Search Regex ondersteuning"], "You can find full documentation about using Search Regex on the <a href=\"%s\" target=\"_blank\">searchregex.com</a> support site.": ["Je kunt de volledige documentatie over het gebruik van Search Regex vinden op de <a href=\"%s\" target=\"_blank\">searchregex.com</a> support site."], "Settings": ["Instellingen"], "Actions": ["Acties"], "Matched Phrases": ["Gevonden zoektermen"], "Matches": ["Gevonden"], "Row ID": ["Regel ID"], "Maximum number of page requests has been exceeded and the search stopped. Try to be more specific with your search term.": ["Het maximum aantal van pagina aanvragen is overschreven en zoeken is gestopt. Probeer een preciezere zoekterm te gebruiken."], "No more matching results found.": ["<PERSON><PERSON> resultaten meer gevonden."], "Last page": ["Laatste pagina"], "Page %(current)s of %(total)s": ["Pagina %(current)s van %(total)s"], "Matches: %(phrases)s across %(rows)s database row.": ["Matches: %(phrases)s across %(rows)s database rows.", "Gevonden in: %(phrases)s in %(rows)s database regel.", "Gevonden in: %(phrases)s in %(rows)s database regels."], "Next page": ["Volgende pagina"], "Progress %(current)s$": ["Voortgang %(current)s$"], "Prev page": ["Vorige pagina"], "First page": ["Eerste pagina"], "%s database row in total": ["%s database rows in total", "%s database regel totaal", "%s database regels totaal"], "500 per page": ["500 per pagina"], "250 per page": ["250 per pagina"], "100 per page": ["100 per pagina"], "50 per page": ["50 per pagina "], "25 per page": ["25 per pagina "], "Ignore Case": ["<PERSON><PERSON><PERSON> hoof<PERSON>tter gebruik"], "Regular Expression": ["Reguliere expressie"], "Row updated": ["Regel bijgewerkt"], "Row replaced": ["Regel vervangen"], "Row deleted": ["Regel verwijderd"], "Settings saved": ["Instellingen opgeslagen"], "{{link}}Source Flags{{/link}} - additional options for the selected source. For example, include post {{guid}}GUID{{/guid}} in the search.": ["{{link}}Zoekopties{{/link}} - aanvullende opties voor de selecteerde bron. Bijvoorbeeld\nom bericht {{guid}}GUID{{/guid}} toe te voegen aan de zoekterm."], "{{link}}Source{{/link}} - the source of data you wish to search. For example, posts, pages, or comments.": ["{{link}}<PERSON>ron{{/link}} - de <PERSON><PERSON> van de g<PERSON> waarin je zoekt. Bijvoorbeeld: berichten, pagina's, reacties."], "{{link}}Regular expression{{/link}} - a way of defining a pattern for text matching. Provides more advanced matches.": ["{{link}}Reguliere expressie{{/link}} - een manier om patronen aan maken om tekst mee te zoeken. Geeft meer geavanceerde zoekresultaten."], "{{link}}Search Flags{{/link}} - additional qualifiers for your search, to enable case insensitivity, and to enable regular expression support.": ["{{link}}Zoekopties{{/link}} - aanvullende opties voor je zoekterm, om hoofdlettergebruik te negeren en voor gebruik van reguliere expressies."], "The following concepts are used by Search Regex:": ["De volgende concepten worden gebruikt door Search Regex:"], "Quick Help": ["<PERSON><PERSON><PERSON> hulp"], "Like this plugin? You might want to consider {{link}}Redirection{{/link}}, a plugin to manage redirects, from the same author.": ["Vind je deze plugin leuk? Je kunt overwegen {{link}}Redirection{{/link}}, een plugin om omleidingen te beheren, van dezelfde auteur."], "Redirection": ["Omleiding"], "If you want to submit information that you don't want in a public repository then send it directly via {{email}}email{{/email}} - include as much information as you can!": ["Als je informatie wilt sturen, maar die je niet in de openbare repository wilt delen, stuur dan een {{email}}email{{/email}} direct aan mij - met zoveel informatie als mogelijk!"], "Please note that any support is provide on as-time-is-available basis and is not guaranteed. I do not provide paid support.": ["Support wordt gegeven op basis van beschikbare tijd en is niet gegaran<PERSON>erd. Ik geef geen betaalde ondersteuning."], "If you want to report a bug please read the {{report}}Reporting Bugs{{/report}} guide.": ["Wil je een bug doorgeven, lees dan de {{report}}Reporting Bugs{{/report}} gids."], "Full documentation for Search Regex can be found at {{site}}https://searchregex.com{{/site}}.": ["Volledige documentatie voor Search Regex kan je vinden op {{site}}https://searchregex.com{{/site}}."], "Need more help?": ["Meer hulp nodig?"], "Results": ["Resultaten"], "Source Options": ["Bron opties"], "Source": ["<PERSON><PERSON>"], "Enter global replacement text": ["Term voor vervangen invoeren"], "Search Flags": ["Zoek opties"], "Enter search phrase": ["Zoekterm invoeren"], "Replace All": ["Alles vervangen"], "Search": ["<PERSON><PERSON>"], "Search and replace information in your database.": ["Zoek en vervang informatie in je database."], "Update": ["Bijwerken"], "How Search Regex uses the REST API - don't change unless necessary": ["Hoe Search Regex de REST API gebruikt - niet veranderen als het niet noodzakelijk is"], "REST API": ["REST-API"], "I'm a nice person and I have helped support the author of this plugin": ["<PERSON>k ben een aardig persoon en ik heb de auteur van deze plugin geholpen met ondersteuning"], "Relative REST API": ["Relatieve REST API"], "Raw REST API": ["Raw REST API"], "Default REST API": ["Standaard REST API"], "Plugin Support": ["Plug<PERSON> on<PERSON><PERSON>"], "Support 💰": ["Ondersteuning 💰"], "You get useful software and I get to carry on making it better.": ["<PERSON> krijgt goed bruikbare software en ik kan doorgaan met het verbeteren ervan."], "Search Regex is free to use - life is wonderful and lovely! It has required a great deal of time and effort to develop and you can help support this development by {{strong}}making a small donation{{/strong}}.": ["Search Regex is gratis te gebruiken - het leven is life is wonder<PERSON>ar<PERSON><PERSON> en verrukkelijk! Het kostte me veel tijd en moeite om dit te ontwikkelen. Je kunt verdere ontwikkeling ondersteunen met het doen van {{strong}}een kleine donatie{{/strong}}."], "I'd like to support some more.": ["<PERSON>k wil graag meer bijdragen."], "You've supported this plugin - thank you!": ["Je hebt deze plugin gesteund - dank<PERSON>wel!"], "If that doesn't help, open your browser's error console and create a {{link}}new issue{{/link}} with the details.": ["<PERSON>rkt dit niet, open dan je browser's foutconsole en maak een {{link}}nieuw probleemrapport{{/link}} aan met alle gegevens."], "If you are using a page caching plugin or service (CloudFlare, OVH, etc) then you can also try clearing that cache.": ["Als je gebruik maakt van een pagina caching plugin of dienst (CloudFlare, OVH, etc.), dan kan je ook proberen om die cache leeg te maken."], "Search Regex is not working. Try clearing your browser cache and reloading this page.": ["Search Regex werkt niet. Probeer je browser cache leeg te maken en deze pagina opnieuw te laden."], "clearing your cache.": ["je cache opschonen."], "If you are using a caching system such as Cloudflare then please read this: ": ["Gebruik je een caching systeem zoals <PERSON>, lees dan dit:"], "Please clear your browser cache and reload this page.": ["Maak je browser cache leeg en laad deze pagina nogmaals."], "Cached Search Regex detected": ["Gecachede Search Regex gevonden"], "Show %s more": ["Show %s more", "Nog %s weergeven", "Nog %s weergeven"], "Maximum number of matches exceeded and hidden from view. These will be included in any replacements.": ["Het maximum aantal zoekresultaten is overschreden en niet zichtba<PERSON>. Deze zullen \nworden gebruikt bij vervangingen."], "Replace %(count)s match.": ["Replace %(count)s matches.", "Vervang %(count)s gevonden.", "Vervang %(count)s gevonden."], "Delete": ["Verwijderen"], "Edit": ["Bewerken"], "Replacement for all matches in this row": ["Vervanging voor alle gevonden zoektermen in deze regel"], "Check Again": ["Opnieuw controleren"], "Testing - %s$": ["Aan het testen - %s$"], "Show Problems": ["Toon problemen"], "Summary": ["<PERSON><PERSON><PERSON><PERSON>"], "You are using a broken REST API route. Changing to a working API should fix the problem.": ["Je gebruikte een defecte REST API route. Wijziging naar een werkende API zou het probleem moeten oplossen."], "Your REST API is not working and the plugin will not be able to continue until this is fixed.": ["Je REST API werkt niet en de plugin kan niet verder voordat dit is opgelost."], "There are some problems connecting to your REST API. It is not necessary to fix these problems and the plugin is able to work.": ["<PERSON>r zijn enkele problemen in de verbinding met je REST API. Het is niet nodig om deze problemen op te lossen en de plugin werkt gewoon."], "Unavailable": ["<PERSON><PERSON>"], "Not working but fixable": ["<PERSON><PERSON>t niet, maar te repareren"], "Working but some issues": ["<PERSON><PERSON><PERSON>, maar met problemen"], "Good": ["Goed"], "Current API": ["Huidige API"], "Switch to this API": ["Gebruik deze API"], "Hide": ["Ver<PERSON>"], "Show Full": ["<PERSON>n volledig"], "Working!": ["Werkt!"], "Finished!": ["<PERSON><PERSON><PERSON>!"], "Replace progress": ["Voortgang vervangen"], "Cancel": ["<PERSON><PERSON><PERSON>"], "Replace": ["Vervangen"], "Search phrase will be removed": ["Zoekterm zal worden verwijderd"], "Remove": ["Verwijderen"], "Multi": ["<PERSON><PERSON><PERSON>"], "Single": ["<PERSON><PERSON>"], "View notice": ["<PERSON><PERSON> be<PERSON>t"], "Replace single phrase.": ["Vervang enkele zoekterm"], "Replacement for this match": ["Vervanging voor deze gevonden zoekterm"], "Support": ["Support"], "Options": ["Opties"], "Search & Replace": ["Zoek en vervang"], "If you are using WordPress 5.2 or newer then look at your {{link}}Site Health{{/link}} and resolve any issues.": ["Als je WordPress 5.2 of nieuwer gebruikt, kijk dan bij {{link}}Sitediagnose{{/link}} en los de problemen op."], "{{link}}Please temporarily disable other plugins!{{/link}} This fixes so many problems.": ["{{link}}Zet andere plugins tijdelijk uit!{{/link}} Dit lost heel vaak problemen op."], "{{link}}Caching software{{/link}}, in particular Cloudflare, can cache the wrong thing. Try clearing all your caches.": ["{{link}}Caching software{{/link}}, en zeker Cloudflare, kunnen het verkeerde cachen. Probeer alle cache te verwijderen."], "What do I do next?": ["Wat moet ik nu doen?"], "Something went wrong 🙁": ["Er is iets fout gegaan 🙁"], "Log out, clear your browser cache, and log in again - your browser has cached an old session.": ["Log uit, wis de browsercache en log opnieuw in - je browser heeft een oude sessie in de cache opgeslagen."], "Reload the page - your current session is old.": ["<PERSON><PERSON><PERSON> de pagina - je huidige sessie is oud."], "Include these details in your report along with a description of what you were doing and a screenshot.": ["Voeg deze gegevens toe aan je melding, samen met een beschrij<PERSON> van wat je deed en een schermafbeelding."], "Email": ["E-mail"], "Create An Issue": ["Meld een probleem"], "Close": ["Sluiten"], "Save": ["Opsla<PERSON>"], "Editing %s": ["Aan het bewerken %s"], "Unable to make request due to browser security. This is typically because your WordPress and Site URL settings are inconsistent, or the request was blocked by your site CORS policy.": ["<PERSON><PERSON> m<PERSON>jk om een verzoek te doen vanwege browser beveiliging. Dit gebeurt meestal omdat WordPress en de site URL niet overeenkomen, of het verzoek wordt geblokkeerd door het CORS beleid van je site."], "Possible cause": ["Mogelijke oorzaak"], "This could be a security plugin, or your server is out of memory or has an external error. Please check your server error log": ["Dit kan een beveiliging plugin zijn. Of je server heeft geen geheugen meer of heeft een externe fout. Bekijk de error log op je server"], "Your server has rejected the request for being too big. You will need to reconfigure it to continue.": ["Je server heeft de aanvraag geweigerd omdat het te groot is. Je moet het aanpassen om door te gaan."], "Your REST API is probably being blocked by a security plugin. Please disable this, or configure it to allow REST API requests.": ["Je REST API wordt waarschijnlijk geblokkeerd door een beveiliging plugin. Zet de plugin uit, of configureer hem zodat hij REST API verzoeken toestaat."], "Read this REST API guide for more information.": ["<PERSON>s deze REST API gids voor meer informatie."], "WordPress did not return a response. This could mean an error occurred or that the request was blocked. Please check your server error_log.": ["WordPress heeft geen reactie gegeven. Dit kan betekenen dat er een fout is opgetreden of dat het verzoek werd geblokkeerd. Bekijk je server foutlog."], "John Godley": ["<PERSON>"], "Adds search and replace functionality across posts, pages, comments, and meta-data, with full regular expression support": ["Voeg zoek en vervang functionaliteit toe in berichten, pagina's, reacties en meta-data, met volledige ondersteuning van reguliere expressies."], "https://searchregex.com/": ["https://searchregex.com/"], "Search Regex": ["Search Regex"]}}, "translation-revision-date": "2022-07-24T10:05:06.610Z", "source": "search-regex", "domain": "search-regex", "generator": "Search Regex"}