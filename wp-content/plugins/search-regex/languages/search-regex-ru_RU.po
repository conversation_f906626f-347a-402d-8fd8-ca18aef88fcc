# Translation of Plugins - Search Regex - Development (trunk) in Russian
# This file is distributed under the same license as the Plugins - Search Regex - Development (trunk) package.
msgid ""
msgstr ""
"PO-Revision-Date: 2023-06-29 17:12:24+0000\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=3; plural=(n % 10 == 1 && n % 100 != 11) ? 0 : ((n % 10 >= 2 && n % 10 <= 4 && (n % 100 < 12 || n % 100 > 14)) ? 1 : 2);\n"
"X-Generator: GlotPress/4.0.0-alpha.11\n"
"Language: ru\n"
"Project-Id-Version: Plugins - Search Regex - Development (trunk)\n"

#. translators: %searched: number of rows searched and matched %phrases: number
#. of phrases matched
#: build/search-regex.js:16
msgid "matched rows = %(searched)s"
msgstr "совпадающие строки = %(searched)s"

#: build/search-regex.js:21
msgid "OK"
msgstr "OK"

#. translators: version installed
#: build/search-regex.js:21
msgid "Version %s installed! Please read the {{url}}release notes{{/url}} for details."
msgstr "Установлена версия %s! Прочитайте {{url}}примечания к выпуску{{/url}} для получения подробной информации."

#: build/search-regex.js:19
msgid "A problem occurred with your last query. This is likely caused by a combination of search filters that haven't been handled properly."
msgstr "Возникла проблема с вашим последним запросом. Вероятно, это вызвано комбинацией поисковых фильтров, которые не были обработаны должным образом."

#: build/search-regex.js:19
msgid "Query Problem"
msgstr "Проблема с запросом"

#: build/search-regex.js:19
msgid "Progress"
msgstr "Ход выполнения"

#: build/search-regex.js:18
msgid "%s row deleted."
msgid_plural "%s rows deleted."
msgstr[0] "%s строка удалена."
msgstr[1] "%s строки удалены."
msgstr[2] "%s строк удалены."

#: build/search-regex.js:18
msgid "Refresh"
msgstr "Обновить"

#: build/search-regex.js:18
msgid "Matched Content"
msgstr "Соответствующее содержимое"

#: build/search-regex.js:18 build/search-regex.js:19
msgid "Your search resulted in too many requests. Please narrow your search terms."
msgstr "Ваш поиск привел к слишком большому количеству запросов. Пожалуйста, сузьте критерии поиска."

#: build/search-regex.js:18
msgid "Your search conditions have changed. Please refresh to see the latest results."
msgstr "Условия поиска изменились. Обновите страницу, чтобы увидеть последние результаты."

#. translators: matches=number of matched rows, total=total number of rows
#: build/search-regex.js:10
msgid "Matched rows: %(matches)s out of %(total)s total."
msgstr "Совпадающие строки: %(matches)s из %(total)s всего."

#: build/search-regex.js:8
msgid "Delete database row"
msgstr "Удалить строку базы данных"

#: build/search-regex.js:8
msgid "View"
msgstr "Просмотр"

#: build/search-regex.js:8
msgid "No title"
msgstr "Без названия"

#: build/search-regex.js:6
msgid "Empty value"
msgstr "Пустое значение"

#: build/search-regex.js:6
msgid "No value"
msgstr "Без значения"

#: build/search-regex.js:6
msgid "Click to replace match"
msgstr "Нажмите, чтобы заменить совпадение"

#: build/search-regex.js:6
msgid "Contains encoded data"
msgstr "Содержит закодированные данные"

#: build/search-regex.js:6
msgid "New meta value"
msgstr "Новое мета-значение"

#: build/search-regex.js:6
msgid "New meta key"
msgstr "Новый мета-ключ"

#: build/search-regex.js:6
msgid "Apply to matches only"
msgstr "Применять только к совпадениям"

#: build/search-regex.js:6
msgid "Enter replacement"
msgstr "Введите замену"

#: build/search-regex.js:6
msgid "Click to replace column"
msgstr "Нажмите, чтобы заменить столбец"

#: build/search-regex.js:6
msgid "This column contains special formatting. Modifying it could break the format."
msgstr "Этот столбец содержит специальное форматирование. Его изменение может нарушить формат."

#: build/search-regex.js:6
msgid "HTML"
msgstr "HTML"

#: build/search-regex.js:6
msgid "Blocks"
msgstr "Блоки"

#: build/search-regex.js:6
msgid "Serialized PHP"
msgstr "Сериализованный PHP"

#: build/search-regex.js:6
msgid "Paste preset JSON."
msgstr "Вставьте предустановленный JSON."

#: build/search-regex.js:6
msgid "Global Search Flags"
msgstr "Флаги глобального поиска"

#: build/search-regex.js:6
msgid "Global Replace"
msgstr "Глобальная замена"

#: build/search-regex.js:6
msgid "Global Search"
msgstr "Глобальный поиск"

#: build/search-regex.js:6
msgid "View Columns"
msgstr "Просмотр столбцов"

#: build/search-regex.js:6
msgid "Sources"
msgstr "Источники"

#: build/search-regex.js:6
msgid "Only include selected columns"
msgstr "Включить только выбранные столбцы"

#: build/search-regex.js:6
msgid "WordPress Action"
msgstr "Действие WordPress"

#: build/search-regex.js:6
msgid "SQL"
msgstr "SQL"

#: build/search-regex.js:6
msgid "CSV"
msgstr "CSV"

#: build/search-regex.js:6
msgid "JSON"
msgstr "JSON"

#: build/search-regex.js:6
msgid "Export Format"
msgstr "Формат экспорта"

#: build/search-regex.js:6
msgid "Run a WordPress action for each matching result."
msgstr "Запустите действие WordPress для каждого совпадающего результата."

#: build/search-regex.js:6 build/search-regex.js:18
msgid "Run Action"
msgstr "Выполнить действие"

#: build/search-regex.js:6
msgid "Delete matching results."
msgstr "Удалить совпадающие результаты."

#: build/search-regex.js:6 build/search-regex.js:18
msgid "Delete Matches"
msgstr "Удалить совпадения"

#: build/search-regex.js:6
msgid "Export matching results to JSON, CSV, or SQL."
msgstr "Экспорт результатов сопоставления в JSON, CSV или SQL."

#: build/search-regex.js:6 build/search-regex.js:18
msgid "Export Matches"
msgstr "Экспорт совпадений"

#: build/search-regex.js:6
msgid "Perform changes to specific values of the matching results."
msgstr "Внесите изменения в определенные значения результатов сопоставления."

#: build/search-regex.js:6
msgid "Modify Matches"
msgstr "Изменить совпадения"

#: build/search-regex.js:6
msgid "Replace the global search values."
msgstr "Замените значения глобального поиска."

#: build/search-regex.js:6
msgid "Global Text Replace"
msgstr "Глобальная замена текста"

#: build/search-regex.js:6
msgid "Just show matching results."
msgstr "Просто покажите совпадающие результаты."

#: build/search-regex.js:6
msgid "No action"
msgstr "Нет действий"

#: build/search-regex.js:6
msgid "Enter replacement value"
msgstr "Введите значение замены"

#: build/search-regex.js:6
msgid "Matched values will be removed"
msgstr "Совпадающие значения будут удалены"

#. translators: text to replace
#: build/search-regex.js:6
msgid "Replace \"%1s\""
msgstr "Заменить \"%1s\""

#: build/search-regex.js:4
msgid "Year"
msgstr "Год"

#: build/search-regex.js:4
msgid "Months"
msgstr "Месяцы"

#: build/search-regex.js:4
msgid "Weeks"
msgstr "Недели"

#: build/search-regex.js:4
msgid "Days"
msgstr "Дни"

#: build/search-regex.js:4
msgid "Hours"
msgstr "Часы"

#: build/search-regex.js:4
msgid "Minutes"
msgstr "Минуты"

#: build/search-regex.js:4
msgid "Seconds"
msgstr "Секунды"

#: build/search-regex.js:4
msgid "Decrement"
msgstr "Уменьшение"

#: build/search-regex.js:4
msgid "Increment"
msgstr "Увеличение"

#: build/search-regex.js:4
msgid "Set Value"
msgstr "Установить значение"

#: build/search-regex.js:4
msgid "Replace With"
msgstr "Заменить с"

#: build/search-regex.js:4 build/search-regex.js:6
msgid "Add"
msgstr "Добавить"

#: build/search-regex.js:4
msgid "Column"
msgstr "Столбец"

#: build/search-regex.js:4
msgid "AND"
msgstr "И"

#: build/search-regex.js:4 build/search-regex.js:6
msgid "Filters"
msgstr "Фильтры"

#: build/search-regex.js:4
msgid "Add sub-filter (OR)"
msgstr "Добавить подфильтр (ИЛИ)"

#: build/search-regex.js:4
msgid "OR"
msgstr "ИЛИ"

#: build/search-regex.js:4
msgid "All"
msgstr "Все"

#: build/search-regex.js:4
msgid "between {{first/}} and {{second/}}"
msgstr "между {{first/}} и {{second/}}"

#: build/search-regex.js:4
msgid "No Owner"
msgstr "Нет владельца"

#: build/search-regex.js:4
msgid "Has Owner"
msgstr "Имеет владельца"

#: build/search-regex.js:4
msgid "Any"
msgstr "Любой"

#: build/search-regex.js:4
msgid "Excludes any"
msgstr "Исключает любые"

#: build/search-regex.js:4
msgid "Includes any"
msgstr "Включает любые"

#: build/search-regex.js:4
msgid "End"
msgstr "Окончание"

#: build/search-regex.js:4
msgid "Begins"
msgstr "Начинается"

#: build/search-regex.js:4
msgid "Not contains"
msgstr "Не содержит"

#: build/search-regex.js:4
msgid "Contains"
msgstr "Содержит"

#: build/search-regex.js:4
msgid "Range"
msgstr "Диапазон"

#: build/search-regex.js:4
msgid "Less"
msgstr "Меньше"

#: build/search-regex.js:4
msgid "Greater"
msgstr "Больше"

#: build/search-regex.js:4
msgid "Not Equals"
msgstr "Не равно"

#: build/search-regex.js:4
msgid "Equals"
msgstr "Равно"

#: build/search-regex.js:4
msgid "Optional global search phrase. Leave blank to use filters only."
msgstr "Необязательная глобальная поисковая фраза. Оставьте пустым, чтобы использовать только фильтры."

#: build/search-regex.js:2
msgid "REST API 404"
msgstr "REST API 404"

#: build/search-regex.js:2
msgid "Your REST API appears to be cached and this will cause problems. Please exclude your REST API from your caching system."
msgstr "Ваш REST API, по-видимому, кэшируется, и это вызывает проблемы. Исключите ваш REST API из вашей системы кэширования."

#: build/search-regex.js:2
msgid "Your REST API is showing a deprecated PHP error. Please fix this error."
msgstr "API REST показывает устаревшую ошибку PHP. Исправьте эту ошибку."

#: build/search-regex.js:2
msgid "Your server configuration is blocking access to the REST API."
msgstr "Конфигурация вашего сервера блокирует доступ к REST API."

#: build/search-regex.js:2
msgid "Can you access your {{api}}REST API{{/api}} without it redirecting?."
msgstr "Можно ли получить доступ к {{api}}REST API{{/api}} без перенаправления?."

#: build/search-regex.js:2
msgid "You will will need to fix this on your site. Redirection is not causing the error."
msgstr "Вам нужно будет исправить это на вашем сайте. Redirection не вызывает ошибку."

#: build/search-regex.js:2
msgid "Preset uploaded"
msgstr "Предустановка загружена"

#: build/search-regex.js:2
msgid "Multiline"
msgstr "Многострочный"

#: build/search-regex.js:2
msgid "Case"
msgstr "Случай"

#: build/search-regex.js:2
msgid "Regex"
msgstr "Регулярное выражение"

#: includes/source/plugin/source-redirection.php:499
msgid "Nginx"
msgstr "NGINX"

#: includes/source/plugin/source-redirection.php:495
msgid "Apache"
msgstr "Apache"

#: includes/source/plugin/source-redirection.php:491
msgid "WordPress"
msgstr "WordPress"

#: includes/source/plugin/source-redirection.php:487
msgid "Module"
msgstr "Модуль"

#: includes/source/plugin/source-redirection.php:468
msgid "Redirection Groups"
msgstr "Группы перенаправления"

#: includes/source/plugin/source-redirection.php:347
msgid "Not accessed"
msgstr "Нет доступа"

#: includes/source/plugin/source-redirection.php:232
msgid "Target"
msgstr "Цель"

#: includes/source/plugin/source-redirection.php:208
msgid "HTTP Code"
msgstr "Код HTTP"

#: includes/source/plugin/source-redirection.php:203
msgid "Position"
msgstr "Позиция"

#: includes/source/plugin/source-redirection.php:196
#: includes/source/plugin/source-redirection.php:514
msgid "Disabled"
msgstr "Отключить"

#: includes/source/plugin/source-redirection.php:192
#: includes/source/plugin/source-redirection.php:510
msgid "Enabled"
msgstr "Включить"

#: includes/source/plugin/source-redirection.php:188
#: includes/source/plugin/source-redirection.php:506
msgid "Status"
msgstr "Статус"

#: includes/source/plugin/source-redirection.php:181
msgid "Group"
msgstr "Группа"

#: includes/source/plugin/source-redirection.php:176
msgid "Last Access"
msgstr "Последний доступ"

#: includes/source/plugin/source-redirection.php:171
msgid "Hit Count"
msgstr "Количество попаданий"

#: includes/source/plugin/source-redirection.php:164
msgid "Source URL (matching)"
msgstr "Исходный URL (совпадает)"

#: includes/source/plugin/source-redirection.php:157
msgid "Source URL"
msgstr "Исходный URL"

#: includes/source/core/source-user.php:174
msgid "User meta"
msgstr "Мета пользователя"

#: includes/source/core/source-user.php:168
msgid "Registered"
msgstr "Зарегистрированно"

#: includes/source/core/source-user.php:134
msgid "Login"
msgstr "Вход"

#: includes/source/core/source-terms.php:135
msgid "Taxonomy"
msgstr "Таксономия"

#: includes/source/core/source-post.php:411
msgid "Post Tag"
msgstr "Метка записи"

#: includes/source/core/source-post.php:402
msgid "Post Category"
msgstr "Рубрика записи"

#: includes/source/core/source-post.php:396
msgid "Comment Count"
msgstr "Кол-во комментариев"

#: includes/source/core/source-post.php:389
msgid "MIME"
msgstr "MIME"

#: includes/source/core/source-post.php:374
msgid "Parent"
msgstr "Родительский"

#: includes/source/core/source-post.php:368
msgid "Modified GMT"
msgstr "Изменено GMT"

#: includes/source/core/source-post.php:362
msgid "Modified"
msgstr "Изменено"

#: includes/source/core/source-post.php:355
msgid "Password"
msgstr "Пароль"

#: includes/source/core/source-post.php:352
msgid "Has no password"
msgstr "Нет пароля"

#: includes/source/core/source-post.php:348
msgid "Has password"
msgstr "Имеет пароль"

#: includes/source/core/source-post.php:339
msgid "Ping Status"
msgstr "Статус отклика"

#: includes/source/core/source-post.php:323
msgid "Comment Status"
msgstr "Статус комментария"

#: includes/source/core/source-post.php:316
#: includes/source/core/source-post.php:332
msgid "Open"
msgstr "Открыть"

#: includes/source/core/source-post.php:308
msgid "Post Status"
msgstr "Статус записи"

#: includes/source/core/source-post.php:268
msgid "Post Type"
msgstr "Тип записи"

#: includes/source/core/source-options.php:136
msgid "Autoload"
msgstr "Автозагрузка"

#: includes/source/core/source-options.php:133
msgid "Is not autoload"
msgstr "Не автозагрузка"

#: includes/source/core/source-options.php:129
msgid "Is autoload"
msgstr "Автозагрузка"

#: includes/source/core/source-meta.php:126 build/search-regex.js:4
#: build/search-regex.js:6
msgid "Meta Value"
msgstr "Мета значение"

#: includes/source/core/source-meta.php:119 build/search-regex.js:4
#: build/search-regex.js:6
msgid "Meta Key"
msgstr "Мета ключ"

#: includes/source/core/source-meta.php:112
msgid "Owner ID"
msgstr "ID владельца"

#: includes/source/core/source-comment.php:262
msgid "Comment meta"
msgstr "Мета комментария"

#: includes/source/core/source-comment.php:254
msgid "User ID"
msgstr "ID пользователя"

#: includes/source/core/source-comment.php:248
msgid "Parent Comment"
msgstr "Родительский комментарий"

#: includes/source/core/source-comment.php:237
msgid "Trackback"
msgstr "Обратная ссылка"

#: includes/source/core/source-comment.php:233
msgid "Pingback"
msgstr "Уведомление"

#: includes/source/core/source-comment.php:229
msgid "Type"
msgstr "Тип"

#: includes/source/core/source-comment.php:224
msgid "User Agent"
msgstr "Агент пользователя"

#: includes/source/core/source-comment.php:219
msgid "Approval Status"
msgstr "Статус утверждения"

#: includes/source/core/source-comment.php:216
msgid "Spam"
msgstr "Спам"

#: includes/source/core/source-comment.php:212
msgid "Approved"
msgstr "Одобрен"

#: includes/source/core/source-comment.php:208
msgid "Unapproved"
msgstr "Не одобрен"

#: includes/source/core/source-comment.php:199
#: includes/source/core/source-post.php:295
msgid "Date GMT"
msgstr "Дата GMT"

#: includes/source/core/source-comment.php:193
#: includes/source/core/source-post.php:289
msgid "Date"
msgstr "Дата"

#: includes/source/core/source-comment.php:188
msgid "IP"
msgstr "IP"

#: includes/source/core/source-comment.php:161
#: includes/source/core/source-post.php:282
msgid "Author"
msgstr "Автор"

#: includes/source/core/source-comment.php:154
msgid "Post ID"
msgstr "ID записи"

#: includes/source/core/source-comment.php:148
#: includes/source/core/source-options.php:105
#: includes/source/core/source-post.php:245
#: includes/source/core/source-terms.php:114
#: includes/source/core/source-user.php:127
#: includes/source/plugin/source-redirection.php:151
#: includes/source/plugin/source-redirection.php:474
msgid "ID"
msgstr "ID"

#: includes/source/class-manager.php:76
#: includes/source/core/source-terms.php:108
msgid "Terms"
msgstr "Термины"

#: includes/source/class-manager.php:22
#: includes/source/core/source-post.php:239
msgid "Posts (core & custom)"
msgstr "Записи (основные и пользовательские)"

#: build/search-regex.js:2
msgid "Please review your data and try again."
msgstr "Проверьте данные и повторите попытку."

#: build/search-regex.js:2
msgid "There was a problem making a request to your site. This could indicate you provided data that did not match requirements, or that the plugin sent a bad request."
msgstr "При отправке запроса на ваш сайт возникла проблема. Это может означать, что вы предоставили данные, которые не соответствуют требованиям, или что плагин отправил неверный запрос."

#: build/search-regex.js:2
msgid "Bad data"
msgstr "Неверные данные"

#: build/search-regex.js:2
msgid "Your WordPress REST API is returning a 404 page. This is almost certainly an external plugin or server configuration issue."
msgstr "API REST возвращает страницу 404. Это почти наверняка проблема конфигурации внешнего плагина или сервера."

#: build/search-regex.js:2
msgid "2000 per page"
msgstr "2000 на страницу"

#: build/search-regex.js:2
msgid "1000 per page"
msgstr "1000 на страницу"

#: build/search-regex.js:2
msgid "Set a preset to use by default when Search Regex is loaded."
msgstr "Задайте предустановку, которая будет использоваться по умолчанию при загрузке Search Regex."

#: build/search-regex.js:2
msgid "No default preset"
msgstr "Нет предустановки по умолчанию"

#: build/search-regex.js:2
msgid "Default Preset"
msgstr "Предустановка по умолчанию"

#: build/search-regex.js:2
msgid "Your admin pages are being cached. Clear this cache and try again. There may be multiple caches involved."
msgstr "Ваши страницы администрирования кэшируются. Очистите этот кэш и повторите попытку. Возможно, задействовано несколько кэшей."

#: build/search-regex.js:2
msgid "This is usually fixed by doing one of the following:"
msgstr "Обычно это можно исправить, выполнив одно из следующих действий:"

#: build/search-regex.js:2
msgid "You are using an old or cached session"
msgstr "Вы используете старую или кешированную сессию"

#: build/search-regex.js:2
msgid "Debug Information"
msgstr "Отладочная информация"

#: build/search-regex.js:2
msgid "Show debug"
msgstr "Показать отладку"

#: build/search-regex.js:2
msgid "WordPress returned an unexpected message. This could be a PHP error from another plugin, or data inserted by your theme."
msgstr "WordPress вернул неожиданное сообщение. Это может быть ошибка PHP от другого плагина или данные, вставленные вашей темой."

#: build/search-regex.js:2
msgid "Your WordPress REST API has been disabled. You will need to enable it to continue."
msgstr "Ваш WordPress REST API был отключен. Чтобы продолжить, вам нужно будет включить его."

#: build/search-regex.js:2
msgid "Your REST API is being redirected. Please remove the redirection for the API."
msgstr "Ваш REST API перенаправляется. Пожалуйста, уберите перенаправление для API."

#: build/search-regex.js:2
msgid "A security plugin or firewall is blocking access. You will need to whitelist the REST API."
msgstr "Плагин безопасности или брандмауэр блокирует доступ. Вам нужно будет внести REST API в белый список."

#: build/search-regex.js:2
msgid "Check your {{link}}Site Health{{/link}} and fix any issues."
msgstr "Проверьте состояние вашего {{link}}сайта{{/link}} и устраните все проблемы."

#: build/search-regex.js:6
msgid "Preset"
msgstr "Предустановка"

#: build/search-regex.js:6
msgid "Upload"
msgstr "Загрузить"

#: build/search-regex.js:6
msgid "Add file"
msgstr "Добавить файл"

#: build/search-regex.js:2
msgid "Preset saved"
msgstr "Сохранение предустановки"

#: build/search-regex.js:6
msgid "Copy to clipboard"
msgstr "Скопировать в буфер обмена"

#: build/search-regex.js:6
msgid "Are you sure you want to delete this preset?"
msgstr "Вы уверены, что хотите удалить эту предустановку?"

#: build/search-regex.js:6
msgid "Locked fields"
msgstr "Заблокированные поля"

#: build/search-regex.js:6
msgid "For example, create tag {{code}}URL{{/code}} and title {{code}}Image URL{{/code}}. Your search could be {{code}}<img src=\"URL\">{{/code}}. When the preset is used it will ask the user for the {{code}}Image URL{{/code}} instead of the full search phrase."
msgstr "Например, создайте тег {{code}}URL{{/code}} и заголовок {{code}}Image URL{{/code}}. Ваш поиск может выглядеть так {{code}}<img src=\"URL\">{{/code}}. При использовании этой предустановки вместо полной поисковой фразы у пользователя будет запрашиваться {{code}}Image URL{{/code}}."

#: build/search-regex.js:6
msgid "A tag creates a custom input field. Insert the tag anywhere in the search, replace, text filter, or text action and when the preset is used it will be replaced with a custom text field with the tag label."
msgstr "Метка создает пользовательское поле ввода. Вставьте тег в любое место поиска, замены, текстового фильтра или текстового действия, и при использовании предустановки он будет заменен на пользовательское текстовое поле с этикеткой метки."

#: build/search-regex.js:6
msgid "Enter tag which is used in the field"
msgstr "Введите тег, который используется в поле"

#: build/search-regex.js:6
msgid "Tag"
msgstr "Метка"

#: build/search-regex.js:6
msgid "Enter tag title shown to user"
msgstr "Введите заголовок тэга, показанный пользователю"

#: build/search-regex.js:6
msgid "Tags"
msgstr "Метки"

#: build/search-regex.js:6
msgid "Locking a field removes it from the search form and prevents changes."
msgstr "Блокировка поля удаляет его из формы поиска и предотвращает внесение изменений."

#: build/search-regex.js:6
msgid "Fields"
msgstr "Поля"

#: build/search-regex.js:6
msgid "Locked Fields"
msgstr "Заблокированные поля"

#: build/search-regex.js:6
msgid "Advanced preset"
msgstr "Расширенные предустановки"

#: build/search-regex.js:6
msgid "Describe the preset"
msgstr "Опишите предустановку"

#: build/search-regex.js:6
msgid "Preset Description"
msgstr "Описание предустановки"

#: build/search-regex.js:6
msgid "Give the preset a name"
msgstr "Дайте предустановке название"

#: build/search-regex.js:6
msgid "Preset Name"
msgstr "Название предустановки"

#: build/search-regex.js:6
msgid "Edit preset"
msgstr "Изменить предустановку"

#: build/search-regex.js:6
msgid "Results per page"
msgstr "Результатов на странице"

#: build/search-regex.js:6
msgid "remove phrase"
msgstr "удалить фразу"

#: build/search-regex.js:6
msgid "no phrase"
msgstr "нет фразы"

#: build/search-regex.js:6
msgid "Import"
msgstr "Импорт"

#: build/search-regex.js:6
msgid "Please check your JSON data is a valid preset. You may have copied it incorrectly, or pasted something that is not a preset."
msgstr "Убедитесь, что ваши данные JSON являются действительными предустановками. Возможно, вы скопировали его неправильно или вставили что-то, что не является предустановкой."

#: build/search-regex.js:6
msgid "Unable to import preset"
msgstr "Невозможно импортировать предустановку"

#: build/search-regex.js:6
msgid "Import preset from clipboard"
msgstr "Импорт предустановок из буфера обмена"

#: build/search-regex.js:6
msgid "Done"
msgstr "Готово"

#: build/search-regex.js:6
msgid "Uploaded %(total)d preset"
msgid_plural "Uploaded %(total)d presets"
msgstr[0] "Загружена %(total)d предустановка"
msgstr[1] "Загружено %(total)d предустановки"
msgstr[2] "Загружено %(total)d предустановок"

#: build/search-regex.js:6
msgid "Importing"
msgstr "Импортирование"

#: build/search-regex.js:6
msgid "File selected"
msgstr "Выбран файл"

#: build/search-regex.js:6
msgid "Click 'Add File' or drag and drop here."
msgstr "Нажмите \"Добавить файл\" или перетащите его сюда."

#: build/search-regex.js:6
msgid "Import a JSON file"
msgstr "Импорт файла JSON"

#: build/search-regex.js:6
msgid "Import JSON"
msgstr "Импорт JSON"

#: build/search-regex.js:6
msgid "Export JSON"
msgstr "Экспорт JSON"

#: build/search-regex.js:6
msgid "Download presets!"
msgstr "Скачать предустановки!"

#: build/search-regex.js:6
msgid "There are no presets"
msgstr "Нет никаких предустановок"

#: build/search-regex.js:4 build/search-regex.js:6
msgid "Flags"
msgstr "Флаги"

#: build/search-regex.js:21
msgid "Presets"
msgstr "Предустановки"

#: build/search-regex.js:2
msgid "If that did not help then {{strong}}create an issue{{/strong}} or send it in an {{strong}}email{{/strong}}."
msgstr "Если это не помогло, тогда {{strong}} создайте тикет о проблеме{{/strong}} или отправьте ее по {{strong}}email{{/strong}}."

#: build/search-regex.js:2
msgid "Please check the {{link}}support site{{/link}} before proceeding further."
msgstr "Посетите {{link}}сайт поддержки{{/link}}, прежде чем продолжить."

#: build/search-regex.js:6
msgid "Enter preset name"
msgstr "Введите название предустановки"

#: build/search-regex.js:6
msgid "Enter a name for your preset"
msgstr "Введите название для вашей предустановки"

#: build/search-regex.js:6
msgid "Saving Preset"
msgstr "Сохранение предустановки"

#: build/search-regex.js:6
msgid "Update current preset"
msgstr "Обновление текущей предустановки"

#: build/search-regex.js:6
msgid "Save search as new preset"
msgstr "Сохранить поиск как новую предустановку"

#: build/search-regex.js:6
msgid "No preset"
msgstr "Нет предустановки"

#: build/search-regex.js:6
msgid "Saving preset"
msgstr "Сохранение предустановки"

#: includes/source/class-manager.php:158
msgid "Advanced"
msgstr "Расширенный"

#: includes/source/class-manager.php:151
msgid "Standard"
msgstr ""

#: build/search-regex.js:19
msgid "Please backup your data before making modifications."
msgstr "Создайте резервную копию данных перед внесением изменений."

#: build/search-regex.js:18
msgid "%s row."
msgid_plural "%s rows."
msgstr[0] "%s строка."
msgstr[1] "%s строки."
msgstr[2] "%s строк."

#: build/search-regex.js:2
msgid "An unknown error occurred."
msgstr "Произошла неизвестная ошибка."

#: includes/source/core/source-post.php:382
msgid "GUID"
msgstr "GUID"

#: includes/source/core/source-post.php:259
#: includes/source/core/source-terms.php:128
msgid "Slug"
msgstr "Ярлык"

#: includes/source/core/source-post.php:301
msgid "Excerpt"
msgstr "Отрывок"

#: includes/source/core/source-comment.php:181
#: includes/source/core/source-post.php:274
msgid "Content"
msgstr "Содержимое"

#: includes/source/core/source-user.php:148
msgid "Display Name"
msgstr "Отображаемое имя"

#: includes/source/core/source-user.php:141
msgid "Nicename"
msgstr "Ник"

#: includes/source/core/source-options.php:118
msgid "Value"
msgstr "Значение"

#: includes/source/core/source-comment.php:241
msgid "Comment"
msgstr "Комментарий"

#: includes/source/core/source-options.php:111
#: includes/source/core/source-terms.php:121
#: includes/source/plugin/source-redirection.php:480 build/search-regex.js:6
msgid "Name"
msgstr "Имя"

#: includes/source/core/source-post.php:251
#: includes/source/plugin/source-redirection.php:214 build/search-regex.js:6
msgid "Title"
msgstr "Наименование"

#: includes/source/core/source-comment.php:175
#: includes/source/core/source-user.php:161
msgid "URL"
msgstr "URL"

#. translators: 1: server PHP version. 2: required PHP version.
#: search-regex.php:37
msgid "Disabled! Detected PHP %1$s, need PHP %2$s+"
msgstr "Отключено! Обнаружен PHP %1$s, требуется PHP %2$s+"

#: includes/source/class-manager.php:165
msgid "Plugins"
msgstr "Плагины"

#: includes/source/class-manager.php:40
msgid "WordPress Options"
msgstr "Параметры WordPress"

#: includes/source/class-manager.php:70
#: includes/source/core/source-user-meta.php:30
msgid "User Meta"
msgstr "Мета пользователя"

#: includes/source/class-manager.php:34
#: includes/source/core/source-user.php:121
msgid "Users"
msgstr "Пользователи"

#: includes/source/class-manager.php:64
#: includes/source/core/source-comment-meta.php:23
msgid "Comment Meta"
msgstr "Мета комментария"

#: includes/source/class-manager.php:28
#: includes/source/core/source-comment.php:142
msgid "Comments"
msgstr "Комментарии"

#: includes/source/class-manager.php:58
#: includes/source/core/source-post-meta.php:25
#: includes/source/core/source-post.php:420
msgid "Post Meta"
msgstr "Метаданные записи"

#: includes/search-regex-admin.php:467
msgid "Please enable JavaScript"
msgstr "Включите JavaScript"

#: includes/search-regex-admin.php:463
msgid "Loading, please wait..."
msgstr "Загрузка, пожалуйста подождите..."

#: includes/search-regex-admin.php:446
msgid "Create Issue"
msgstr "Создать тикет о проблеме"

#: includes/search-regex-admin.php:443
msgid "<code>SearchRegexi10n</code> is not defined. This usually means another plugin is blocking Search Regex from loading. Please disable all plugins and try again."
msgstr "<code>SearchRegexi10n</code> не определен. Обычно это означает, что другой плагин блокирует загрузку Search Regex. Пожалуйста, отключите все плагины и повторите попытку."

#: includes/search-regex-admin.php:442
msgid "If you think Search Regex is at fault then create an issue."
msgstr "Если вы считаете, что Search Regex виноват, создайте проблему."

#: includes/search-regex-admin.php:441
msgid "Please see the <a href=\"https://searchregex.com/support/problems/\">list of common problems</a>."
msgstr "Ознакомьтесь со списком распространенных проблем <a href=\"https://searchregex.com/support/problems/\"></a>."

#: includes/search-regex-admin.php:440
msgid "Please note that Search Regex requires the WordPress REST API to be enabled. If you have disabled this then you won't be able to use Search Regex"
msgstr "Обратите внимание, что для Search Regex требуется, чтобы был включен WordPress REST API. Если вы отключили это, вы не сможете использовать Search Regex"

#: includes/search-regex-admin.php:438
msgid "Also check if your browser is able to load <code>search-regex.js</code>:"
msgstr "Также проверьте, способен ли ваш браузер загрузить <code>search-regex.js</code>:"

#: includes/search-regex-admin.php:436
msgid "This may be caused by another plugin - look at your browser's error console for more details."
msgstr "Это может быть вызвано другим плагином-посмотрите на консоль ошибок вашего браузера для более подробной информации."

#: includes/search-regex-admin.php:435
msgid "Unable to load Search Regex ☹️"
msgstr "Не удалось загрузить Search Regex ☹️"

#: includes/search-regex-admin.php:420
msgid "Unable to load Search Regex"
msgstr "Не удалось загрузить Search Regex"

#. translators: 1: Expected WordPress version, 2: Actual WordPress version
#: includes/search-regex-admin.php:417
msgid "Search Regex requires WordPress v%1$1s, you are using v%2$2s - please update your WordPress"
msgstr "Search Regex требует WordPress v%1$1s, вы используете v%2$2s - обновите ваш WordPress"

#: includes/search-regex-admin.php:318
msgid "Search Regex Support"
msgstr "Поддержка Search Regex"

#. translators: URL
#: includes/search-regex-admin.php:309
msgid "You can find full documentation about using Search Regex on the <a href=\"%s\" target=\"_blank\">searchregex.com</a> support site."
msgstr "Полная документация по использованию Search Regex на сайте поддержки <a href=\"%s\" target=\"_blank\">searchregex.com</a>."

#: includes/search-regex-admin.php:84
msgid "Settings"
msgstr "Настройки"

#: build/search-regex.js:6
msgid "Action"
msgstr "Действие"

#: build/search-regex.js:18
msgid "Row ID"
msgstr "ID ряда"

#: build/search-regex.js:8
msgid "No more matching results found."
msgstr "Больше подходящих результатов не найдено."

#: build/search-regex.js:12
msgid "Last page"
msgstr "Последняя страница"

#. translators: current=current page, total=total number of pages
#: build/search-regex.js:12
msgid "Page %(current)s of %(total)s"
msgstr "Страница %(current)s of %(total)s"

#: build/search-regex.js:12 build/search-regex.js:18
msgid "Next page"
msgstr "Следующая страница"

#. translators: %current: current percent progress
#: build/search-regex.js:18
msgid "Progress %(current)s%%"
msgstr "Ход выполнения %(текущий)s%%"

#: build/search-regex.js:10
msgid "Prev page"
msgstr "Предыдущая страница"

#: build/search-regex.js:10 build/search-regex.js:16
msgid "First page"
msgstr "Первая страница"

#. translators: %s: total number of rows searched
#: build/search-regex.js:14
msgid "%s database row in total"
msgid_plural "%s database rows in total"
msgstr[0] "Всего %s строка базы данных"
msgstr[1] "Всего %s строки базы данных"
msgstr[2] "Всего %s строк базы данных"

#: build/search-regex.js:2
msgid "500 per page"
msgstr "500 на страницу"

#: build/search-regex.js:2
msgid "250 per page"
msgstr "250 на страницу"

#: build/search-regex.js:2
msgid "100 per page"
msgstr "100 на страницу"

#: build/search-regex.js:2
msgid "50 per page"
msgstr "50 на страницу"

#: build/search-regex.js:2
msgid "25 per page"
msgstr "25 на страницу"

#: build/search-regex.js:2
msgid "Ignore Case"
msgstr "Игнорировать регистр"

#: build/search-regex.js:2
msgid "Regular Expression"
msgstr "Регулярное выражение"

#: build/search-regex.js:2
msgid "Row updated"
msgstr "Строка обновлена"

#: build/search-regex.js:2
msgid "Row replaced"
msgstr "Строка заменена"

#: build/search-regex.js:2
msgid "Row deleted"
msgstr "Строка удалена"

#: build/search-regex.js:2
msgid "Settings saved"
msgstr "Настройки сохранены"

#: includes/search-regex-admin.php:301
msgid "{{link}}Source Flags{{/link}} - additional options for the selected source. For example, include post {{guid}}GUID{{/guid}} in the search."
msgstr "{{link}}Флаги источника{{/link}} - дополнительные параметры для выбранного источника. Например, включите в поиск запись {{guid}}GUID{{/guid}}."

#: includes/search-regex-admin.php:314
msgid "{{link}}Source{{/link}} - the source of data you wish to search. For example, posts, pages, or comments."
msgstr "{{link}}Источник{{/link}} - источник данных, которые вы хотите найти. Например, посты, страницы или комментарии."

#: includes/search-regex-admin.php:313
msgid "{{link}}Regular expression{{/link}} - a way of defining a pattern for text matching. Provides more advanced matches."
msgstr "{{link}}Регулярное выражение{{/link}} - способ определения шаблона для сопоставления текста. Обеспечивает более сложные совпадения."

#: includes/search-regex-admin.php:312
msgid "{{link}}Search Flags{{/link}} - additional qualifiers for your search, to enable case insensitivity, and to enable regular expression support."
msgstr "{{link}}Флаги поиска{{/link}} - дополнительные квалификаторы для вашего поиска, чтобы включить нечувствительность к регистру и включить поддержку регулярных выражений."

#: includes/search-regex-admin.php:310
msgid "The following concepts are used by Search Regex:"
msgstr "В Search Regex используются следующие понятия:"

#: build/search-regex.js:4
msgid "Like this plugin? You might want to consider {{link}}Redirection{{/link}}, a plugin to manage redirects, from the same author."
msgstr "Нравится этот плагин? Возможно, вы захотите рассмотреть {{link}}Redirection{{/link}}, плагин для управления перенаправлениями, от того же автора."

#: includes/source/plugin/source-redirection.php:145 build/search-regex.js:4
msgid "Redirection"
msgstr "Перенаправление"

#: build/search-regex.js:4
msgid "If you want to submit information that you don't want in a public repository then send it directly via {{email}}email{{/email}} - include as much information as you can!"
msgstr "Если вы хотите отправить информацию, которую вы не хотите в публичный репозиторий, отправьте ее напрямую через {{email}} email {{/e-mail}} - укажите как можно больше информации!"

#: build/search-regex.js:4
msgid "Please note that any support is provide on as-time-is-available basis and is not guaranteed. I do not provide paid support."
msgstr "Обратите внимание, что любая поддержка предоставляется по мере доступности и не гарантируется. Я не предоставляю платной поддержки."

#: build/search-regex.js:4
msgid "If you want to report a bug please read the {{report}}Reporting Bugs{{/report}} guide."
msgstr "Если вы хотите сообщить об ошибке, пожалуйста, прочитайте инструкцию {{report}} отчеты об ошибках {{/report}}."

#: build/search-regex.js:4
msgid "Full documentation for Search Regex can be found at {{site}}https://searchregex.com{{/site}}."
msgstr "Полную документацию по Search Regex можно найти на {{site}}https://searchregex.com{{/site}}."

#: build/search-regex.js:4
msgid "Need more help?"
msgstr "Нужна дополнительная помощь?"

#: build/search-regex.js:6
msgid "Results"
msgstr "Результаты"

#: build/search-regex.js:6 build/search-regex.js:18
msgid "Source"
msgstr "Источник"

#: build/search-regex.js:4
msgid "Enter search phrase"
msgstr "Введите фразу для поиска"

#: build/search-regex.js:18
msgid "Replace All"
msgstr "Заменить все"

#: build/search-regex.js:6 build/search-regex.js:18
msgid "Search"
msgstr "Поиск"

#: build/search-regex.js:19
msgid "Search and replace information in your database."
msgstr "Поиск и замена информации в базе данных."

#: build/search-regex.js:2
msgid "Update"
msgstr "Обновить"

#: build/search-regex.js:2
msgid "How Search Regex uses the REST API - don't change unless necessary"
msgstr "Как Search Regex использует REST API - не меняйте без необходимости"

#: build/search-regex.js:2
msgid "REST API"
msgstr "REST API"

#: build/search-regex.js:2
msgid "I'm a nice person and I have helped support the author of this plugin"
msgstr "Я хороший человек, и я помог поддержать автора этого плагина"

#: build/search-regex.js:2
msgid "Relative REST API"
msgstr "Относительный REST API"

#: build/search-regex.js:2
msgid "Raw REST API"
msgstr "Необработанный REST API"

#: build/search-regex.js:2
msgid "Default REST API"
msgstr "REST API по умолчанию"

#: build/search-regex.js:4
msgid "Plugin Support"
msgstr "Поддержка плагина"

#: build/search-regex.js:4
msgid "Support 💰"
msgstr "Поддержка 💰"

#: build/search-regex.js:4
msgid "You get useful software and I get to carry on making it better."
msgstr "Вы получаете полезное программное обеспечение, и я продолжаю делать его лучше."

#: build/search-regex.js:4
msgid "Search Regex is free to use - life is wonderful and lovely! It has required a great deal of time and effort to develop and you can help support this development by {{strong}}making a small donation{{/strong}}."
msgstr "Search Regex бесплатен для использования - жизнь чудесна и прекрасна! Его разработка потребовала много времени и усилий, и вы можете помочь поддержать это развитие, сделав {{strong}} небольшое пожертвование{{/strong}}."

#: build/search-regex.js:4
msgid "I'd like to support some more."
msgstr "Мне хотелось бы поддержать чуть больше."

#: build/search-regex.js:4
msgid "You've supported this plugin - thank you!"
msgstr "Вы поддерживаете этот плагин - спасибо!"

#: build/search-regex.js:4
msgid "If that doesn't help, open your browser's error console and create a {{link}}new issue{{/link}} with the details."
msgstr "Если это не поможет, откройте консоль ошибок браузера и создайте {{link}}новый тикет о проблеме{{/link}} с деталями."

#: includes/search-regex-admin.php:437 build/search-regex.js:4
msgid "If you are using a page caching plugin or service (CloudFlare, OVH, etc) then you can also try clearing that cache."
msgstr "Если вы используете плагин кэширования страниц или услугу (cloudflare, OVH и т.д.), то вы также можете попробовать очистить кэш."

#: build/search-regex.js:4
msgid "Search Regex is not working. Try clearing your browser cache and reloading this page."
msgstr "Search Regex не работает. Попробуйте очистить кеш браузера и перезагрузить эту страницу."

#: build/search-regex.js:19
msgid "clearing your cache."
msgstr "очистить свой кэш."

#: build/search-regex.js:19
msgid "If you are using a caching system such as Cloudflare then please read this: "
msgstr "Если вы используете систему кэширования, такую как cloudflare, пожалуйста, прочитайте это: "

#: build/search-regex.js:19
msgid "Please clear your browser cache and reload this page."
msgstr "Очистите кеш браузера и перезагрузите эту страницу."

#: build/search-regex.js:19
msgid "Cached Search Regex detected"
msgstr "Обнаружено кеширование Search Regex"

#. translators: number of results to show
#: build/search-regex.js:8
msgid "Show %s more"
msgid_plural "Show %s more"
msgstr[0] "Показать ещё %s"
msgstr[1] "Показать ещё %s"
msgstr[2] "Показать ещё %s"

#: build/search-regex.js:6
msgid "Maximum number of matches exceeded and hidden from view. These will be included in any replacements."
msgstr "Превышено максимальное количество совпадений, и они скрыты от просмотра. Они будут включены в любые замены."

#: build/search-regex.js:6
msgid "Delete"
msgstr "Удалить"

#: build/search-regex.js:6 build/search-regex.js:8
msgid "Edit"
msgstr "Изменить"

#: build/search-regex.js:4
msgid "Check Again"
msgstr "Проверить снова"

#. translators: test percent
#: build/search-regex.js:4
msgid "Testing - %s%%"
msgstr "Тестирование - %s%%"

#: build/search-regex.js:2
msgid "Show Problems"
msgstr "Показать проблемы"

#: build/search-regex.js:2
msgid "Summary"
msgstr "Резюме"

#: build/search-regex.js:2
msgid "You are using a broken REST API route. Changing to a working API should fix the problem."
msgstr "Используется поврежденный маршрут REST API. Переход на рабочий API должен устранить проблему."

#: build/search-regex.js:2
msgid "Your REST API is not working and the plugin will not be able to continue until this is fixed."
msgstr "Ваш REST API не работает, и плагин не сможет продолжить работу, пока это не будет исправлено."

#: build/search-regex.js:2
msgid "There are some problems connecting to your REST API. It is not necessary to fix these problems and the plugin is able to work."
msgstr "Возникли некоторые проблемы с подключением к вашему REST API. Эти проблемы устранять не нужно, и плагин может работать."

#: build/search-regex.js:2
msgid "Unavailable"
msgstr "Недоступен"

#: build/search-regex.js:2
msgid "Not working but fixable"
msgstr "Не работает, но можно исправить"

#: build/search-regex.js:2
msgid "Working but some issues"
msgstr "Работает, но есть некоторые проблемы"

#: build/search-regex.js:2
msgid "Good"
msgstr "Хорошо"

#: build/search-regex.js:2
msgid "Current API"
msgstr "Текущий API"

#: build/search-regex.js:2
msgid "Switch to this API"
msgstr "Переключиться на этот API"

#: build/search-regex.js:2
msgid "Hide"
msgstr "Скрыть"

#: build/search-regex.js:2
msgid "Show Full"
msgstr "Показать полностью"

#: build/search-regex.js:2
msgid "Working!"
msgstr "Работает!"

#: build/search-regex.js:19
msgid "Finished!"
msgstr "Завершено!"

#: build/search-regex.js:6 build/search-regex.js:18
msgid "Cancel"
msgstr "Отменить"

#: build/search-regex.js:4 build/search-regex.js:6
msgid "Replace"
msgstr "Заменить"

#: build/search-regex.js:4 build/search-regex.js:6
msgid "Remove"
msgstr "Удалить"

#: build/search-regex.js:2 build/search-regex.js:6
msgid "Multi"
msgstr "Мульти"

#: build/search-regex.js:6
msgid "Single"
msgstr "Одиночный"

#: build/search-regex.js:21
msgid "Support"
msgstr "Поддержка"

#: includes/source/core/source-options.php:99 build/search-regex.js:21
msgid "Options"
msgstr "Параметры"

#: build/search-regex.js:21
msgid "Search & Replace"
msgstr "Поиск и Замена"

#: build/search-regex.js:4
msgid "If you are using WordPress 5.2 or newer then look at your {{link}}Site Health{{/link}} and resolve any issues."
msgstr "Если вы используете WordPress 5.2 или новее, посмотрите раздел {{link}}Здоровье сайта{{/link}} для решения любых проблем."

#: build/search-regex.js:4
msgid "{{link}}Please temporarily disable other plugins!{{/link}} This fixes so many problems."
msgstr "{{link}}Временно отключите другие плагины!{{/ link}} Это устраняет множество проблем."

#: build/search-regex.js:4
msgid "{{link}}Caching software{{/link}}, in particular Cloudflare, can cache the wrong thing. Try clearing all your caches."
msgstr "{{link}}Кэширование программного обеспечения{{/link}},в частности Cloudflare, может кэшировать неправильные вещи. Попробуйте очистить все кэши."

#: build/search-regex.js:4 build/search-regex.js:19
msgid "What do I do next?"
msgstr "Что мне делать дальше?"

#: build/search-regex.js:2
msgid "Something went wrong 🙁"
msgstr "Что-то пошло не так 🙁"

#: build/search-regex.js:2
msgid "Log out, clear your browser cache, and log in again - your browser has cached an old session."
msgstr "Выйдите из системы, очистите кеш браузера и снова войдите в систему - ваш браузер кэшировал старую сессию."

#: build/search-regex.js:2
msgid "Reload the page - your current session is old."
msgstr "Перезагрузите страницу - ваша текущая сессия устарела."

#: build/search-regex.js:2
msgid "Include these details in your report along with a description of what you were doing and a screenshot."
msgstr "Включите эти сведения в свой отчет вместе с описанием того, что вы делали, и снимком экрана."

#: includes/source/core/source-comment.php:168
#: includes/source/core/source-user.php:155 build/search-regex.js:2
#: build/search-regex.js:6 build/search-regex.js:19
msgid "Email"
msgstr "Email"

#: build/search-regex.js:2 build/search-regex.js:6 build/search-regex.js:19
msgid "Create An Issue"
msgstr "Открыть тикет о проблеме"

#: includes/source/core/source-post.php:320
#: includes/source/core/source-post.php:336
msgid "Closed"
msgstr "Закрыто"

#: build/search-regex.js:6
msgid "Save"
msgstr "Сохранить"

#: build/search-regex.js:2
msgid "Unable to make request due to browser security. This is typically because your WordPress and Site URL settings are inconsistent, or the request was blocked by your site CORS policy."
msgstr "Невозможно отправить запрос из-за безопасности браузера. Обычно это происходит потому, что настройки вашего WordPress и URL-адреса сайта несовместимы, или запрос был заблокирован политикой CORS вашего сайта."

#: build/search-regex.js:2
msgid "Possible cause"
msgstr "Возможная причина"

#: build/search-regex.js:2
msgid "This could be a security plugin, or your server is out of memory or has an external error. Please check your server error log"
msgstr "Это может быть плагин безопасности, или вашему серверу не хватает памяти, или произошла внешняя ошибка. Проверьте журнал ошибок вашего сервера"

#: build/search-regex.js:2
msgid "Your server has rejected the request for being too big. You will need to reconfigure it to continue."
msgstr "Ваш сервер отклонил запрос, так как он слишком большой. Чтобы продолжить, вам нужно будет перенастроить его."

#: build/search-regex.js:2
msgid "Your REST API is probably being blocked by a security plugin. Please disable this, or configure it to allow REST API requests."
msgstr "Ваш REST API, вероятно, блокируется плагином безопасности. Пожалуйста, отключите его или настройте так, чтобы он разрешал запросы REST API."

#: build/search-regex.js:2
msgid "Read this REST API guide for more information."
msgstr "Прочтите это руководство по REST API для получения дополнительной информации."

#: build/search-regex.js:2
msgid "WordPress did not return a response. This could mean an error occurred or that the request was blocked. Please check your server error_log."
msgstr "WordPress не вернул ответ. Это может означать, что произошла ошибка или что запрос был заблокирован. Пожалуйста, проверьте ваш error_log сервера."

#. Author of the plugin
msgid "John Godley"
msgstr "John Godley"

#. Description of the plugin
msgid "Adds search and replace functionality across posts, pages, comments, and meta-data, with full regular expression support"
msgstr "Добавляет функции поиска и замены в записях, страницах, комментариях и метаданных с полной поддержкой регулярных выражений"

#. Plugin URI of the plugin
msgid "https://searchregex.com/"
msgstr "https://searchregex.com/"

#. Plugin Name of the plugin
#: build/search-regex.js:21
msgid "Search Regex"
msgstr "Search Regex"