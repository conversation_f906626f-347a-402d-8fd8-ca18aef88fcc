# Translation of Plugins - Search Regex - Development (trunk) in Dutch
# This file is distributed under the same license as the Plugins - Search Regex - Development (trunk) package.
msgid ""
msgstr ""
"PO-Revision-Date: 2023-11-23 12:20:04+0000\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=n != 1;\n"
"X-Generator: GlotPress/4.0.0-alpha.11\n"
"Language: nl\n"
"Project-Id-Version: Plugins - Search Regex - Development (trunk)\n"

#. translators: %searched: number of rows searched and matched %phrases: number
#. of phrases matched
#: build/search-regex.js:16
msgid "matched rows = %(searched)s"
msgstr "overeenkomende rijen = %(gezocht)s"

#: build/search-regex.js:21
msgid "OK"
msgstr "OK"

#. translators: version installed
#: build/search-regex.js:21
msgid "Version %s installed! Please read the {{url}}release notes{{/url}} for details."
msgstr "Versie %s geïnstalleerd! <PERSON><PERSON> de {{url}}releaselog{{/url}} voor meer informatie."

#: build/search-regex.js:19
msgid "A problem occurred with your last query. This is likely caused by a combination of search filters that haven't been handled properly."
msgstr "Er is een probleem opgetreden met je laatste query. Dit wordt waarschijnlijk veroorzaakt door een combinatie van zoekfilters die niet goed zijn verwerkt."

#: build/search-regex.js:19
msgid "Query Problem"
msgstr "Query probleem"

#: build/search-regex.js:19
msgid "Progress"
msgstr "Voortgang"

#: build/search-regex.js:18
msgid "%s row deleted."
msgid_plural "%s rows deleted."
msgstr[0] "%s rij verwijderd."
msgstr[1] "%s rijen verwijderd."

#: build/search-regex.js:18
msgid "Refresh"
msgstr "Verversen"

#: build/search-regex.js:18
msgid "Matched Content"
msgstr "Overeenkomende inhoud"

#: build/search-regex.js:18 build/search-regex.js:19
msgid "Your search resulted in too many requests. Please narrow your search terms."
msgstr "Je zoek opdracht leidde tot te veel aanvragen. Gelieve je zoek termen te verfijnen."

#: build/search-regex.js:18
msgid "Your search conditions have changed. Please refresh to see the latest results."
msgstr "Je zoekvoorwaarden zijn gewijzigd. Vernieuw om de recentste resultaten te zien."

#. translators: matches=number of matched rows, total=total number of rows
#: build/search-regex.js:10
msgid "Matched rows: %(matches)s out of %(total)s total."
msgstr "Overeenkomende rijen: %(komt overeen)s van %(totaal)s totaal."

#: build/search-regex.js:8
msgid "Delete database row"
msgstr "Database rij verwijderen"

#: build/search-regex.js:8
msgid "View"
msgstr "Bekijken"

#: build/search-regex.js:8
msgid "No title"
msgstr "Geen titel"

#: build/search-regex.js:6
msgid "Empty value"
msgstr "Lege waarde"

#: build/search-regex.js:6
msgid "No value"
msgstr "Geen waarde"

#: build/search-regex.js:6
msgid "Click to replace match"
msgstr "Klik om overeenkomst te vervangen"

#: build/search-regex.js:6
msgid "Contains encoded data"
msgstr "Bevat gecodeerde gegevens"

#: build/search-regex.js:6
msgid "New meta value"
msgstr "Nieuwe metawaarde"

#: build/search-regex.js:6
msgid "New meta key"
msgstr "Nieuwe metasleutel"

#: build/search-regex.js:6
msgid "Apply to matches only"
msgstr "Alleen van toepassing op overeenkomsten"

#: build/search-regex.js:6
msgid "Enter replacement"
msgstr "Voer vervanging in"

#: build/search-regex.js:6
msgid "Click to replace column"
msgstr "Klik om de kolom te vervangen"

#: build/search-regex.js:6
msgid "This column contains special formatting. Modifying it could break the format."
msgstr "Deze kolom bevat speciale lay-out. Als je het wijzigt, kan het format worden verbroken."

#: build/search-regex.js:6
msgid "HTML"
msgstr "HTML"

#: build/search-regex.js:6
msgid "Blocks"
msgstr "Blokken"

#: build/search-regex.js:6
msgid "Serialized PHP"
msgstr "Geserialiseerde PHP"

#: build/search-regex.js:6
msgid "Paste preset JSON."
msgstr "Plak voorinstelling JSON."

#: build/search-regex.js:6
msgid "Global Search Flags"
msgstr "Globale zoekmarkeringen"

#: build/search-regex.js:6
msgid "Global Replace"
msgstr "Globale vervanging"

#: build/search-regex.js:6
msgid "Global Search"
msgstr "Globaal zoeken"

#: build/search-regex.js:6
msgid "View Columns"
msgstr "Kolommen bekijken"

#: build/search-regex.js:6
msgid "Sources"
msgstr "Bronnen"

#: build/search-regex.js:6
msgid "Only include selected columns"
msgstr "Alleen geselecteerde kolommen opnemen"

#: build/search-regex.js:6
msgid "WordPress Action"
msgstr "WordPress actie"

#: build/search-regex.js:6
msgid "SQL"
msgstr "SQL"

#: build/search-regex.js:6
msgid "CSV"
msgstr "CSV"

#: build/search-regex.js:6
msgid "JSON"
msgstr "JSON"

#: build/search-regex.js:6
msgid "Export Format"
msgstr "Export format"

#: build/search-regex.js:6
msgid "Run a WordPress action for each matching result."
msgstr "Voer een WordPress actie uit voor elk overeenkomend resultaat."

#: build/search-regex.js:6 build/search-regex.js:18
msgid "Run Action"
msgstr "Actie uitvoeren"

#: build/search-regex.js:6
msgid "Delete matching results."
msgstr "Verwijder overeenkomende resultaten."

#: build/search-regex.js:6 build/search-regex.js:18
msgid "Delete Matches"
msgstr "Overeenkomsten verwijderen"

#: build/search-regex.js:6
msgid "Export matching results to JSON, CSV, or SQL."
msgstr "Exporteer overeenkomende resultaten naar JSON, CSV of SQL."

#: build/search-regex.js:6 build/search-regex.js:18
msgid "Export Matches"
msgstr "Overeenkomsten exporteren"

#: build/search-regex.js:6
msgid "Perform changes to specific values of the matching results."
msgstr "Voer wijzigingen uit in specifieke waarden van de overeenkomende resultaten."

#: build/search-regex.js:6
msgid "Modify Matches"
msgstr "Overeenkomsten wijzigen"

#: build/search-regex.js:6
msgid "Replace the global search values."
msgstr "Vervang de algemene zoek waarden."

#: build/search-regex.js:6
msgid "Global Text Replace"
msgstr "Algemene tekst vervangen"

#: build/search-regex.js:6
msgid "Just show matching results."
msgstr "Laat alleen overeenkomende resultaten zien."

#: build/search-regex.js:6
msgid "No action"
msgstr "Geen actie"

#: build/search-regex.js:6
msgid "Enter replacement value"
msgstr "Vervangingswaarde invoeren"

#: build/search-regex.js:6
msgid "Matched values will be removed"
msgstr "Overeenkomende waarden worden verwijderd"

#. translators: text to replace
#: build/search-regex.js:6
msgid "Replace \"%1s\""
msgstr "Vervang \"%1s\""

#: build/search-regex.js:4
msgid "Year"
msgstr "Jaar"

#: build/search-regex.js:4
msgid "Months"
msgstr "Maanden"

#: build/search-regex.js:4
msgid "Weeks"
msgstr "Weken"

#: build/search-regex.js:4
msgid "Days"
msgstr "Dagen"

#: build/search-regex.js:4
msgid "Hours"
msgstr "Uren"

#: build/search-regex.js:4
msgid "Minutes"
msgstr "Minuten"

#: build/search-regex.js:4
msgid "Seconds"
msgstr "Seconden"

#: build/search-regex.js:4
msgid "Decrement"
msgstr "Verlagen"

#: build/search-regex.js:4
msgid "Increment"
msgstr "Verhogen"

#: build/search-regex.js:4
msgid "Set Value"
msgstr "Waarde instellen"

#: build/search-regex.js:4
msgid "Replace With"
msgstr "Vervangen door"

#: build/search-regex.js:4 build/search-regex.js:6
msgid "Add"
msgstr "Toevoegen"

#: build/search-regex.js:4
msgid "Column"
msgstr "Kolom"

#: build/search-regex.js:4
msgid "AND"
msgstr "EN"

#: build/search-regex.js:4 build/search-regex.js:6
msgid "Filters"
msgstr "Filters"

#: build/search-regex.js:4
msgid "Add sub-filter (OR)"
msgstr "Sub filter toevoegen (OR)"

#: build/search-regex.js:4
msgid "OR"
msgstr "OF"

#: build/search-regex.js:4
msgid "All"
msgstr "Alle"

#: build/search-regex.js:4
msgid "between {{first/}} and {{second/}}"
msgstr "tussen {{first/}} en {{second/}}"

#: build/search-regex.js:4
msgid "No Owner"
msgstr "Geen eigenaar"

#: build/search-regex.js:4
msgid "Has Owner"
msgstr "Heeft eigenaar"

#: build/search-regex.js:4
msgid "Any"
msgstr "Elke"

#: build/search-regex.js:4
msgid "Excludes any"
msgstr "Sluit alle"

#: build/search-regex.js:4
msgid "Includes any"
msgstr "Inclusief elke"

#: build/search-regex.js:4
msgid "End"
msgstr "Einde"

#: build/search-regex.js:4
msgid "Begins"
msgstr "Begint"

#: build/search-regex.js:4
msgid "Not contains"
msgstr "Bevat niet"

#: build/search-regex.js:4
msgid "Contains"
msgstr "Bevat"

#: build/search-regex.js:4
msgid "Range"
msgstr "Reeks"

#: build/search-regex.js:4
msgid "Less"
msgstr "Minder"

#: build/search-regex.js:4
msgid "Greater"
msgstr "Groter"

#: build/search-regex.js:4
msgid "Not Equals"
msgstr "Is niet gelijk"

#: build/search-regex.js:4
msgid "Equals"
msgstr "Gelijk aan"

#: build/search-regex.js:4
msgid "Optional global search phrase. Leave blank to use filters only."
msgstr "Optionele algemene zoekzin. Laat leeg om alleen filters te gebruiken."

#: build/search-regex.js:2
msgid "REST API 404"
msgstr "REST API 404"

#: build/search-regex.js:2
msgid "Your REST API appears to be cached and this will cause problems. Please exclude your REST API from your caching system."
msgstr "Je REST API lijkt in de cache te zijn opgeslagen en dit veroorzaakt problemen. Sluit je REST API uit van je caching."

#: build/search-regex.js:2
msgid "Your REST API is showing a deprecated PHP error. Please fix this error."
msgstr "Je REST API toont een verouderde PHP fout. Los deze fout op."

#: build/search-regex.js:2
msgid "Your server configuration is blocking access to the REST API."
msgstr "Je serverconfiguratie blokkeert de toegang tot de REST API."

#: build/search-regex.js:2
msgid "Can you access your {{api}}REST API{{/api}} without it redirecting?."
msgstr "Kun je toegang krijgen tot je {{api}}REST API{{/api}} zonder dat het omleidt?."

#: build/search-regex.js:2
msgid "You will will need to fix this on your site. Redirection is not causing the error."
msgstr "Je moet dit oplossen op je site. Omleiden veroorzaakt de fout niet."

#: build/search-regex.js:2
msgid "Preset uploaded"
msgstr "Voorinstelling geüpload"

#: build/search-regex.js:2
msgid "Multiline"
msgstr "Multiline"

#: build/search-regex.js:2
msgid "Case"
msgstr "Case"

#: build/search-regex.js:2
msgid "Regex"
msgstr "Regex"

#: includes/source/plugin/source-redirection.php:499
msgid "Nginx"
msgstr "Nginx"

#: includes/source/plugin/source-redirection.php:495
msgid "Apache"
msgstr "Apache"

#: includes/source/plugin/source-redirection.php:491
msgid "WordPress"
msgstr "WordPress"

#: includes/source/plugin/source-redirection.php:487
msgid "Module"
msgstr "Module"

#: includes/source/plugin/source-redirection.php:468
msgid "Redirection Groups"
msgstr "Omleiding groepen"

#: includes/source/plugin/source-redirection.php:347
msgid "Not accessed"
msgstr "Niet toegankelijk"

#: includes/source/plugin/source-redirection.php:232
msgid "Target"
msgstr "Doel"

#: includes/source/plugin/source-redirection.php:208
msgid "HTTP Code"
msgstr "HTTP code"

#: includes/source/plugin/source-redirection.php:203
msgid "Position"
msgstr "Positie"

#: includes/source/plugin/source-redirection.php:196
#: includes/source/plugin/source-redirection.php:514
msgid "Disabled"
msgstr "Uitgeschakeld"

#: includes/source/plugin/source-redirection.php:192
#: includes/source/plugin/source-redirection.php:510
msgid "Enabled"
msgstr "Ingeschakeld"

#: includes/source/plugin/source-redirection.php:188
#: includes/source/plugin/source-redirection.php:506
msgid "Status"
msgstr "Status"

#: includes/source/plugin/source-redirection.php:181
msgid "Group"
msgstr "Groep"

#: includes/source/plugin/source-redirection.php:176
msgid "Last Access"
msgstr "Laatste toegang"

#: includes/source/plugin/source-redirection.php:171
msgid "Hit Count"
msgstr "Aantal treffers"

#: includes/source/plugin/source-redirection.php:164
msgid "Source URL (matching)"
msgstr "Bron URL (overeenkomend)"

#: includes/source/plugin/source-redirection.php:157
msgid "Source URL"
msgstr "Bron URL"

#: includes/source/core/source-user.php:174
msgid "User meta"
msgstr "Gebruiker meta"

#: includes/source/core/source-user.php:168
msgid "Registered"
msgstr "Geregistreerd"

#: includes/source/core/source-user.php:134
msgid "Login"
msgstr "Login"

#: includes/source/core/source-terms.php:135
msgid "Taxonomy"
msgstr "Taxonomie"

#: includes/source/core/source-post.php:411
msgid "Post Tag"
msgstr "Bericht tag"

#: includes/source/core/source-post.php:402
msgid "Post Category"
msgstr "Bericht categorie"

#: includes/source/core/source-post.php:396
msgid "Comment Count"
msgstr "Aantal reacties"

#: includes/source/core/source-post.php:389
msgid "MIME"
msgstr "MIME"

#: includes/source/core/source-post.php:374
msgid "Parent"
msgstr "Hoofd"

#: includes/source/core/source-post.php:368
msgid "Modified GMT"
msgstr "Aangepaste GMT"

#: includes/source/core/source-post.php:362
msgid "Modified"
msgstr "Aangepast"

#: includes/source/core/source-post.php:355
msgid "Password"
msgstr "Wachtwoord"

#: includes/source/core/source-post.php:352
msgid "Has no password"
msgstr "Heeft geen wachtwoord"

#: includes/source/core/source-post.php:348
msgid "Has password"
msgstr "Heeft wachtwoord"

#: includes/source/core/source-post.php:339
msgid "Ping Status"
msgstr "Ping status"

#: includes/source/core/source-post.php:323
msgid "Comment Status"
msgstr "Reactie status"

#: includes/source/core/source-post.php:316
#: includes/source/core/source-post.php:332
msgid "Open"
msgstr "Open"

#: includes/source/core/source-post.php:308
msgid "Post Status"
msgstr "Berichtstatus"

#: includes/source/core/source-post.php:268
msgid "Post Type"
msgstr "Berichttype"

#: includes/source/core/source-options.php:136
msgid "Autoload"
msgstr "Automatisch laden"

#: includes/source/core/source-options.php:133
msgid "Is not autoload"
msgstr "Wordt niet automatisch geladen"

#: includes/source/core/source-options.php:129
msgid "Is autoload"
msgstr "Is automatisch geladen"

#: includes/source/core/source-meta.php:126 build/search-regex.js:4
#: build/search-regex.js:6
msgid "Meta Value"
msgstr "Metawaarde"

#: includes/source/core/source-meta.php:119 build/search-regex.js:4
#: build/search-regex.js:6
msgid "Meta Key"
msgstr "Meta sleutel"

#: includes/source/core/source-meta.php:112
msgid "Owner ID"
msgstr "Id van eigenaar"

#: includes/source/core/source-comment.php:262
msgid "Comment meta"
msgstr "Reactie meta"

#: includes/source/core/source-comment.php:254
msgid "User ID"
msgstr "Gebruikers ID"

#: includes/source/core/source-comment.php:248
msgid "Parent Comment"
msgstr "Hoofd reactie"

#: includes/source/core/source-comment.php:237
msgid "Trackback"
msgstr "Trackback"

#: includes/source/core/source-comment.php:233
msgid "Pingback"
msgstr "Pingback"

#: includes/source/core/source-comment.php:229
msgid "Type"
msgstr "Type"

#: includes/source/core/source-comment.php:224
msgid "User Agent"
msgstr "User agent"

#: includes/source/core/source-comment.php:219
msgid "Approval Status"
msgstr "Toestemming status"

#: includes/source/core/source-comment.php:216
msgid "Spam"
msgstr "Spam"

#: includes/source/core/source-comment.php:212
msgid "Approved"
msgstr "Toegestaan"

#: includes/source/core/source-comment.php:208
msgid "Unapproved"
msgstr "Geweigerd"

#: includes/source/core/source-comment.php:199
#: includes/source/core/source-post.php:295
msgid "Date GMT"
msgstr "Datum GMT"

#: includes/source/core/source-comment.php:193
#: includes/source/core/source-post.php:289
msgid "Date"
msgstr "Datum"

#: includes/source/core/source-comment.php:188
msgid "IP"
msgstr "IP"

#: includes/source/core/source-comment.php:161
#: includes/source/core/source-post.php:282
msgid "Author"
msgstr "Auteur"

#: includes/source/core/source-comment.php:154
msgid "Post ID"
msgstr "Bericht ID"

#: includes/source/core/source-comment.php:148
#: includes/source/core/source-options.php:105
#: includes/source/core/source-post.php:245
#: includes/source/core/source-terms.php:114
#: includes/source/core/source-user.php:127
#: includes/source/plugin/source-redirection.php:151
#: includes/source/plugin/source-redirection.php:474
msgid "ID"
msgstr "ID"

#: includes/source/class-manager.php:76
#: includes/source/core/source-terms.php:108
msgid "Terms"
msgstr "Voorwaarden"

#: includes/source/class-manager.php:22
#: includes/source/core/source-post.php:239
msgid "Posts (core & custom)"
msgstr "Berichten (core & aangepast)"

#: build/search-regex.js:2
msgid "Please review your data and try again."
msgstr "Beoordeel je gegevens en probeer het opnieuw."

#: build/search-regex.js:2
msgid "There was a problem making a request to your site. This could indicate you provided data that did not match requirements, or that the plugin sent a bad request."
msgstr "Er is een probleem opgetreden bij het indienen van een aanvraag aan je site. Dit kan erop wijzen dat je gegevens heeft verstrekt die niet aan de vereisten voldeden, of dat de plugin een slechte aanvraag heeft verzonden."

#: build/search-regex.js:2
msgid "Bad data"
msgstr "Slechte gegevens"

#: build/search-regex.js:2
msgid "Your WordPress REST API is returning a 404 page. This is almost certainly an external plugin or server configuration issue."
msgstr "Je WordPress REST API retourneert een 404 pagina. Dit is vrijwel zeker een probleem met de externe plugin of serverconfiguratie."

#: build/search-regex.js:2
msgid "2000 per page"
msgstr "2000 per pagina"

#: build/search-regex.js:2
msgid "1000 per page"
msgstr "1000 per pagina"

#: build/search-regex.js:2
msgid "Set a preset to use by default when Search Regex is loaded."
msgstr "Stel een voorinstelling in die standaard wordt gebruikt wanneer Search Regex wordt geladen."

#: build/search-regex.js:2
msgid "No default preset"
msgstr "Geen standaard voorinstelling"

#: build/search-regex.js:2
msgid "Default Preset"
msgstr "Standaard voorinstelling"

#: build/search-regex.js:2
msgid "Your admin pages are being cached. Clear this cache and try again. There may be multiple caches involved."
msgstr "Je beheer pagina's worden in de cache opgeslagen. Wis deze cache en probeer het opnieuw. Mogelijk zijn er meerdere caches bij betrokken."

#: build/search-regex.js:2
msgid "This is usually fixed by doing one of the following:"
msgstr "Dit wordt meestal opgelost door een van de volgende handelingen uit te voeren:"

#: build/search-regex.js:2
msgid "You are using an old or cached session"
msgstr "Je gebruikt een oude of cache sessie"

#: build/search-regex.js:2
msgid "Debug Information"
msgstr "Debug informatie"

#: build/search-regex.js:2
msgid "Show debug"
msgstr "Toon debug info"

#: build/search-regex.js:2
msgid "WordPress returned an unexpected message. This could be a PHP error from another plugin, or data inserted by your theme."
msgstr "WordPress heeft een onverwacht bericht geretourneerd. Dit kan een PHP fout zijn van een andere plugin of gegevens die door je thema zijn ingevoegd."

#: build/search-regex.js:2
msgid "Your WordPress REST API has been disabled. You will need to enable it to continue."
msgstr "Je WordPress REST API is uitgeschakeld. Je moet het inschakelen om door te gaan."

#: build/search-regex.js:2
msgid "Your REST API is being redirected. Please remove the redirection for the API."
msgstr "Je REST API wordt omgeleid. Verwijder de omleiding voor de API."

#: build/search-regex.js:2
msgid "A security plugin or firewall is blocking access. You will need to whitelist the REST API."
msgstr "Een beveiliging plugin of firewall blokkeert de toegang. Je moet de REST API op de toegestane lijst zetten."

#: build/search-regex.js:2
msgid "Check your {{link}}Site Health{{/link}} and fix any issues."
msgstr "Controleer je {{link}}sitediagnose{{/link}} en los eventuele problemen op."

#: build/search-regex.js:6
msgid "Preset"
msgstr "Vooraf gemaakte instelling"

#: build/search-regex.js:6
msgid "Upload"
msgstr "Upload"

#: build/search-regex.js:6
msgid "Add file"
msgstr "Bestand toevoegen"

#: build/search-regex.js:2
msgid "Preset saved"
msgstr "Voorinstelling opgeslagen"

#: build/search-regex.js:6
msgid "Copy to clipboard"
msgstr "Kopieer naar klembord"

#: build/search-regex.js:6
msgid "Are you sure you want to delete this preset?"
msgstr "Weet je zeker dat je deze voorinstelling wil verwijderen?"

#: build/search-regex.js:6
msgid "Locked fields"
msgstr "Vergrendelde velden"

#: build/search-regex.js:6
msgid "For example, create tag {{code}}URL{{/code}} and title {{code}}Image URL{{/code}}. Your search could be {{code}}<img src=\"URL\">{{/code}}. When the preset is used it will ask the user for the {{code}}Image URL{{/code}} instead of the full search phrase."
msgstr "Maak bijvoorbeeld de tag {{code}}URL{{/code}} en de titel {{code}} afbeelding URL{{/code}}. Je zoekopdracht kan {{code}}<img src=\"URL\">{{/code}} zijn. Wanneer de voorinstelling wordt gebruikt, wordt de gebruiker gevraagd om de {{code}}afbeelding URL{{/code}} In plaats van de volledige zoekterm."

#: build/search-regex.js:6
msgid "A tag creates a custom input field. Insert the tag anywhere in the search, replace, text filter, or text action and when the preset is used it will be replaced with a custom text field with the tag label."
msgstr "Een tag maakt een aangepast invoerveld. Voeg de tag ergens in de zoek-, vervang-, tekst filter- of tekst actie in en wanneer de voorinstelling wordt gebruikt, wordt deze vervangen door een aangepast tekstveld met het tag label."

#: build/search-regex.js:6
msgid "Enter tag which is used in the field"
msgstr "Voer de tag in die in het veld wordt gebruikt"

#: build/search-regex.js:6
msgid "Tag"
msgstr "Tag"

#: build/search-regex.js:6
msgid "Enter tag title shown to user"
msgstr "Voer de tag titel in die aan de gebruiker wordt getoond"

#: build/search-regex.js:6
msgid "Tags"
msgstr "Tags"

#: build/search-regex.js:6
msgid "Locking a field removes it from the search form and prevents changes."
msgstr "Als je een veld vergrendelt, wordt het uit het zoek formulier verwijderd en worden wijzigingen voorkomen."

#: build/search-regex.js:6
msgid "Fields"
msgstr "Velden"

#: build/search-regex.js:6
msgid "Locked Fields"
msgstr "Vergrendelde velden"

#: build/search-regex.js:6
msgid "Advanced preset"
msgstr "Geavanceerde voorinstelling"

#: build/search-regex.js:6
msgid "Describe the preset"
msgstr "Beschrijf de voorinstelling"

#: build/search-regex.js:6
msgid "Preset Description"
msgstr "Vooraf ingestelde beschrijving"

#: build/search-regex.js:6
msgid "Give the preset a name"
msgstr "Geef de voorinstelling een naam"

#: build/search-regex.js:6
msgid "Preset Name"
msgstr "Vooraf ingestelde naam"

#: build/search-regex.js:6
msgid "Edit preset"
msgstr "Bewerk voorinstelling"

#: build/search-regex.js:6
msgid "Results per page"
msgstr "Resultaten per pagina"

#: build/search-regex.js:6
msgid "remove phrase"
msgstr "zin verwijderen"

#: build/search-regex.js:6
msgid "no phrase"
msgstr "geen zin"

#: build/search-regex.js:6
msgid "Import"
msgstr "Import"

#: build/search-regex.js:6
msgid "Please check your JSON data is a valid preset. You may have copied it incorrectly, or pasted something that is not a preset."
msgstr "Controleer of je JSON gegevens een geldige voorinstelling is. Je kunt het verkeerd hebben gekopieerd, of iets hebben geplakt dat geen voorinstelling is."

#: build/search-regex.js:6
msgid "Unable to import preset"
msgstr "Kan voorinstelling niet importeren"

#: build/search-regex.js:6
msgid "Import preset from clipboard"
msgstr "Importeer voorinstelling van klembord"

#: build/search-regex.js:6
msgid "Done"
msgstr "Klaar"

#: build/search-regex.js:6
msgid "Uploaded %(total)d preset"
msgid_plural "Uploaded %(total)d presets"
msgstr[0] "Geüploade %(total)d voorinstelling"
msgstr[1] "Geüploade %(total)d voorinstellingen"

#: build/search-regex.js:6
msgid "Importing"
msgstr "Importeren"

#: build/search-regex.js:6
msgid "File selected"
msgstr "Bestand geselecteerd"

#: build/search-regex.js:6
msgid "Click 'Add File' or drag and drop here."
msgstr "Klik op 'Bestand toevoegen' of sleep het hier naartoe."

#: build/search-regex.js:6
msgid "Import a JSON file"
msgstr "Een JSON bestand importeren"

#: build/search-regex.js:6
msgid "Import JSON"
msgstr "JSON importeren"

#: build/search-regex.js:6
msgid "Export JSON"
msgstr "JSON exporteren"

#: build/search-regex.js:6
msgid "Download presets!"
msgstr "Download voorinstellingen!"

#: build/search-regex.js:6
msgid "There are no presets"
msgstr "Er zijn geen voorinstellingen"

#: build/search-regex.js:4 build/search-regex.js:6
msgid "Flags"
msgstr "Vlaggen"

#: build/search-regex.js:21
msgid "Presets"
msgstr "Voorinstellingen"

#: build/search-regex.js:2
msgid "If that did not help then {{strong}}create an issue{{/strong}} or send it in an {{strong}}email{{/strong}}."
msgstr "Als dat niet heeft geholpen, {{strong}}maak dan een probleem aan{{/strong}} of stuur het in een {{strong}}e-mail{{/strong}}."

#: build/search-regex.js:2
msgid "Please check the {{link}}support site{{/link}} before proceeding further."
msgstr "Raadpleeg de {{link}}ondersteuning site{{/link}} voor het verder gaan."

#: build/search-regex.js:6
msgid "Enter preset name"
msgstr "Geef de naam van de voorinstelling in"

#: build/search-regex.js:6
msgid "Enter a name for your preset"
msgstr "Vul een naam in voor je voorinstelling"

#: build/search-regex.js:6
msgid "Saving Preset"
msgstr "Voorinstelling opslaan"

#: build/search-regex.js:6
msgid "Update current preset"
msgstr "Update huidige voorinstelling"

#: build/search-regex.js:6
msgid "Save search as new preset"
msgstr "Zoekopdracht opslaan als nieuw voorinstelling"

#: build/search-regex.js:6
msgid "No preset"
msgstr "Geen voorinstelling"

#: build/search-regex.js:6
msgid "Saving preset"
msgstr "Voorinstelling opslaan"

#: includes/source/class-manager.php:158
msgid "Advanced"
msgstr "Geavanceerd"

#: includes/source/class-manager.php:151
msgid "Standard"
msgstr "Standaard"

#: build/search-regex.js:19
msgid "Please backup your data before making modifications."
msgstr "Maak eerst een back-up van je gegevens, voordat je wijzigen gaat maken."

#: build/search-regex.js:18
msgid "%s row."
msgid_plural "%s rows."
msgstr[0] "%s rij."
msgstr[1] "%s rijen."

#: build/search-regex.js:2
msgid "An unknown error occurred."
msgstr "Er is een onbekende fout opgetreden."

#: includes/source/core/source-post.php:382
msgid "GUID"
msgstr "GUID"

#: includes/source/core/source-post.php:259
#: includes/source/core/source-terms.php:128
msgid "Slug"
msgstr "Slug"

#: includes/source/core/source-post.php:301
msgid "Excerpt"
msgstr "Samenvatting"

#: includes/source/core/source-comment.php:181
#: includes/source/core/source-post.php:274
msgid "Content"
msgstr "Inhoud"

#: includes/source/core/source-user.php:148
msgid "Display Name"
msgstr "Weergavenaam"

#: includes/source/core/source-user.php:141
msgid "Nicename"
msgstr "Schermnaam (backend)"

#: includes/source/core/source-options.php:118
msgid "Value"
msgstr "Waarde"

#: includes/source/core/source-comment.php:241
msgid "Comment"
msgstr "Reactie"

#: includes/source/core/source-options.php:111
#: includes/source/core/source-terms.php:121
#: includes/source/plugin/source-redirection.php:480 build/search-regex.js:6
msgid "Name"
msgstr "Naam"

#: includes/source/core/source-post.php:251
#: includes/source/plugin/source-redirection.php:214 build/search-regex.js:6
msgid "Title"
msgstr "Titel"

#: includes/source/core/source-comment.php:175
#: includes/source/core/source-user.php:161
msgid "URL"
msgstr "URL"

#. translators: 1: server PHP version. 2: required PHP version.
#: search-regex.php:37
msgid "Disabled! Detected PHP %1$s, need PHP %2$s+"
msgstr "Uitgeschakeld! Gedetecteerd PHP %1$s, nodig PHP %2$s+"

#: includes/source/class-manager.php:165
msgid "Plugins"
msgstr "Plugins"

#: includes/source/class-manager.php:40
msgid "WordPress Options"
msgstr "WordPress opties"

#: includes/source/class-manager.php:70
#: includes/source/core/source-user-meta.php:30
msgid "User Meta"
msgstr "Gebruiker Meta"

#: includes/source/class-manager.php:34
#: includes/source/core/source-user.php:121
msgid "Users"
msgstr "Gebruikers"

#: includes/source/class-manager.php:64
#: includes/source/core/source-comment-meta.php:23
msgid "Comment Meta"
msgstr "Reactie Meta"

#: includes/source/class-manager.php:28
#: includes/source/core/source-comment.php:142
msgid "Comments"
msgstr "Reacties"

#: includes/source/class-manager.php:58
#: includes/source/core/source-post-meta.php:25
#: includes/source/core/source-post.php:420
msgid "Post Meta"
msgstr "Bericht meta"

#: includes/search-regex-admin.php:467
msgid "Please enable JavaScript"
msgstr "Schakel JavaScript in"

#: includes/search-regex-admin.php:463
msgid "Loading, please wait..."
msgstr "Aan het laden..."

#: includes/search-regex-admin.php:446
msgid "Create Issue"
msgstr "Meld een probleem"

#: includes/search-regex-admin.php:443
msgid "<code>SearchRegexi10n</code> is not defined. This usually means another plugin is blocking Search Regex from loading. Please disable all plugins and try again."
msgstr "<code>SearchRegexi10n</code> is niet gedefinieerd. Dit betekent meestal dat een andere plugin Search Regex blokkeert om te laden. Zet alle plugins uit en probeer het opnieuw."

#: includes/search-regex-admin.php:442
msgid "If you think Search Regex is at fault then create an issue."
msgstr "Denk je dat Search Regex het probleem veroorzaakt, maak dan een probleemrapport aan."

#: includes/search-regex-admin.php:441
msgid "Please see the <a href=\"https://searchregex.com/support/problems/\">list of common problems</a>."
msgstr "Bekijk hier de <a href=\"https://searchregex.com/support/problems/\">lijst van algemene problemen</a>."

#: includes/search-regex-admin.php:440
msgid "Please note that Search Regex requires the WordPress REST API to be enabled. If you have disabled this then you won't be able to use Search Regex"
msgstr "Search Regex vereist dat de WordPress REST API ingeschakeld is. Heb je deze uitgeschakeld, dan kun je Search Regex niet gebruiken"

#: includes/search-regex-admin.php:438
msgid "Also check if your browser is able to load <code>search-regex.js</code>:"
msgstr "Controleer ook of je browser <code>search-regex.js</code> kan laden:"

#: includes/search-regex-admin.php:436
msgid "This may be caused by another plugin - look at your browser's error console for more details."
msgstr "Dit kan worden veroorzaakt door een andere plugin - bekijk je browser's foutconsole voor meer gegevens."

#: includes/search-regex-admin.php:435
msgid "Unable to load Search Regex ☹️"
msgstr "Laden van Search Regex ☹️ onmogelijk"

#: includes/search-regex-admin.php:420
msgid "Unable to load Search Regex"
msgstr "Laden van Search Regex onmogelijk"

#. translators: 1: Expected WordPress version, 2: Actual WordPress version
#: includes/search-regex-admin.php:417
msgid "Search Regex requires WordPress v%1$1s, you are using v%2$2s - please update your WordPress"
msgstr "Search Regex heeft WordPress v%1$1s nodig, en je gebruikt v%2$2s - update je WordPress"

#: includes/search-regex-admin.php:318
msgid "Search Regex Support"
msgstr "Search Regex ondersteuning"

#. translators: URL
#: includes/search-regex-admin.php:309
msgid "You can find full documentation about using Search Regex on the <a href=\"%s\" target=\"_blank\">searchregex.com</a> support site."
msgstr "Je kunt de volledige documentatie over het gebruik van Search Regex vinden op de <a href=\"%s\" target=\"_blank\">searchregex.com</a> support site."

#: includes/search-regex-admin.php:84
msgid "Settings"
msgstr "Instellingen"

#: build/search-regex.js:6
msgid "Action"
msgstr "Actie"

#: build/search-regex.js:18
msgid "Row ID"
msgstr "Regel ID"

#: build/search-regex.js:8
msgid "No more matching results found."
msgstr "Geen resultaten meer gevonden."

#: build/search-regex.js:12
msgid "Last page"
msgstr "Laatste pagina"

#. translators: current=current page, total=total number of pages
#: build/search-regex.js:12
msgid "Page %(current)s of %(total)s"
msgstr "Pagina %(current)s van %(total)s"

#: build/search-regex.js:12 build/search-regex.js:18
msgid "Next page"
msgstr "Volgende pagina"

#. translators: %current: current percent progress
#: build/search-regex.js:18
msgid "Progress %(current)s%%"
msgstr "Voortuitgang %(current)s%%"

#: build/search-regex.js:10
msgid "Prev page"
msgstr "Vorige pagina"

#: build/search-regex.js:10 build/search-regex.js:16
msgid "First page"
msgstr "Eerste pagina"

#. translators: %s: total number of rows searched
#: build/search-regex.js:14
msgid "%s database row in total"
msgid_plural "%s database rows in total"
msgstr[0] "%s database regel totaal"
msgstr[1] "%s database regels totaal"

#: build/search-regex.js:2
msgid "500 per page"
msgstr "500 per pagina"

#: build/search-regex.js:2
msgid "250 per page"
msgstr "250 per pagina"

#: build/search-regex.js:2
msgid "100 per page"
msgstr "100 per pagina"

#: build/search-regex.js:2
msgid "50 per page"
msgstr "50 per pagina"

#: build/search-regex.js:2
msgid "25 per page"
msgstr "25 per pagina"

#: build/search-regex.js:2
msgid "Ignore Case"
msgstr "Negeer hoofdletter gebruik"

#: build/search-regex.js:2
msgid "Regular Expression"
msgstr "Reguliere expressie"

#: build/search-regex.js:2
msgid "Row updated"
msgstr "Regel geüpdatet"

#: build/search-regex.js:2
msgid "Row replaced"
msgstr "Regel vervangen"

#: build/search-regex.js:2
msgid "Row deleted"
msgstr "Regel verwijderd"

#: build/search-regex.js:2
msgid "Settings saved"
msgstr "Instellingen opgeslagen"

#: includes/search-regex-admin.php:301
msgid "{{link}}Source Flags{{/link}} - additional options for the selected source. For example, include post {{guid}}GUID{{/guid}} in the search."
msgstr ""
"{{link}}Zoekopties{{/link}} - aanvullende opties voor de selecteerde bron. Bijvoorbeeld\n"
"om bericht {{guid}}GUID{{/guid}} toe te voegen aan de zoekterm."

#: includes/search-regex-admin.php:314
msgid "{{link}}Source{{/link}} - the source of data you wish to search. For example, posts, pages, or comments."
msgstr "{{link}}Bron{{/link}} - de bron van de gegevens waarin je zoekt. Bijvoorbeeld: berichten, pagina's, reacties."

#: includes/search-regex-admin.php:313
msgid "{{link}}Regular expression{{/link}} - a way of defining a pattern for text matching. Provides more advanced matches."
msgstr "{{link}}Reguliere expressie{{/link}} - een manier om een patroon aan te maken om tekst mee te zoeken. Geeft meer geavanceerde zoekresultaten."

#: includes/search-regex-admin.php:312
msgid "{{link}}Search Flags{{/link}} - additional qualifiers for your search, to enable case insensitivity, and to enable regular expression support."
msgstr "{{link}}Zoekopties{{/link}} - aanvullende opties voor je zoekopdracht, om hoofdlettergebruik te negeren en om reguliere expressie ondersteuning in te schakelen."

#: includes/search-regex-admin.php:310
msgid "The following concepts are used by Search Regex:"
msgstr "De volgende concepten worden gebruikt door Search Regex:"

#: build/search-regex.js:4
msgid "Like this plugin? You might want to consider {{link}}Redirection{{/link}}, a plugin to manage redirects, from the same author."
msgstr "Vind je deze plugin leuk? Je kunt overwegen {{link}}Redirection{{/link}} , een plugin om omleidingen te beheren, van dezelfde auteur."

#: includes/source/plugin/source-redirection.php:145 build/search-regex.js:4
msgid "Redirection"
msgstr "Omleiding"

#: build/search-regex.js:4
msgid "If you want to submit information that you don't want in a public repository then send it directly via {{email}}email{{/email}} - include as much information as you can!"
msgstr "Als je informatie wil sturen, maar die je niet in de openbare repository wil delen, stuur dan een {{email}}e-mail{{/email}} direct aan mij - met zoveel informatie als je kunt!"

#: build/search-regex.js:4
msgid "Please note that any support is provide on as-time-is-available basis and is not guaranteed. I do not provide paid support."
msgstr "Support wordt gegeven op basis van beschikbare tijd en is niet gegarandeerd. Ik geef geen betaalde ondersteuning."

#: build/search-regex.js:4
msgid "If you want to report a bug please read the {{report}}Reporting Bugs{{/report}} guide."
msgstr "Wil je een bug doorgeven, lees dan de {{report}}Reporting Bugs{{/report}} gids."

#: build/search-regex.js:4
msgid "Full documentation for Search Regex can be found at {{site}}https://searchregex.com{{/site}}."
msgstr "Volledige documentatie voor Search Regex kan je vinden op {{site}}https://searchregex.com{{/site}}."

#: build/search-regex.js:4
msgid "Need more help?"
msgstr "Meer hulp nodig?"

#: build/search-regex.js:6
msgid "Results"
msgstr "Resultaten"

#: build/search-regex.js:6 build/search-regex.js:18
msgid "Source"
msgstr "Bron"

#: build/search-regex.js:4
msgid "Enter search phrase"
msgstr "Zoekterm invoeren"

#: build/search-regex.js:18
msgid "Replace All"
msgstr "Alles vervangen"

#: build/search-regex.js:6 build/search-regex.js:18
msgid "Search"
msgstr "Zoeken"

#: build/search-regex.js:19
msgid "Search and replace information in your database."
msgstr "Zoek en vervang informatie in je database."

#: build/search-regex.js:2
msgid "Update"
msgstr "Update"

#: build/search-regex.js:2
msgid "How Search Regex uses the REST API - don't change unless necessary"
msgstr "Hoe Search Regex de REST API gebruikt - niet veranderen als het niet noodzakelijk is"

#: build/search-regex.js:2
msgid "REST API"
msgstr "REST API"

#: build/search-regex.js:2
msgid "I'm a nice person and I have helped support the author of this plugin"
msgstr "Ik ben een aardig persoon en ik heb de auteur van deze plugin geholpen met ondersteuning"

#: build/search-regex.js:2
msgid "Relative REST API"
msgstr "Relatieve REST API"

#: build/search-regex.js:2
msgid "Raw REST API"
msgstr "Raw REST API"

#: build/search-regex.js:2
msgid "Default REST API"
msgstr "Standaard REST API"

#: build/search-regex.js:4
msgid "Plugin Support"
msgstr "Plugin ondersteuning"

#: build/search-regex.js:4
msgid "Support 💰"
msgstr "Ondersteuning 💰"

#: build/search-regex.js:4
msgid "You get useful software and I get to carry on making it better."
msgstr "Je krijgt goed bruikbare software en ik kan doorgaan met het verbeteren ervan."

#: build/search-regex.js:4
msgid "Search Regex is free to use - life is wonderful and lovely! It has required a great deal of time and effort to develop and you can help support this development by {{strong}}making a small donation{{/strong}}."
msgstr "Search Regex is gratis te gebruiken - het leven is life is wonderbaarlijk en verrukkelijk! Het kostte me veel tijd en moeite om dit te ontwikkelen. Je kunt verdere ontwikkeling ondersteunen met het doen van {{strong}}een kleine donatie{{/strong}}."

#: build/search-regex.js:4
msgid "I'd like to support some more."
msgstr "Ik wil graag meer bijdragen."

#: build/search-regex.js:4
msgid "You've supported this plugin - thank you!"
msgstr "Je hebt deze plugin gesteund - dankjewel!"

#: build/search-regex.js:4
msgid "If that doesn't help, open your browser's error console and create a {{link}}new issue{{/link}} with the details."
msgstr "Werkt dit niet, open dan je browser's foutconsole en maak een {{link}}nieuw probleemrapport{{/link}} aan met alle gegevens."

#: includes/search-regex-admin.php:437 build/search-regex.js:4
msgid "If you are using a page caching plugin or service (CloudFlare, OVH, etc) then you can also try clearing that cache."
msgstr "Als je gebruik maakt van een pagina caching plugin of dienst (CloudFlare, OVH, enz.), dan kan je ook proberen om die cache leeg te maken."

#: build/search-regex.js:4
msgid "Search Regex is not working. Try clearing your browser cache and reloading this page."
msgstr "Search Regex werkt niet. Probeer je browser cache leeg te maken en deze pagina opnieuw te laden."

#: build/search-regex.js:19
msgid "clearing your cache."
msgstr "je cache opschonen."

#: build/search-regex.js:19
msgid "If you are using a caching system such as Cloudflare then please read this: "
msgstr "Gebruik je een caching systeem zoals Cloudflare, lees dan dit:"

#: build/search-regex.js:19
msgid "Please clear your browser cache and reload this page."
msgstr "Maak je browser cache leeg en laad deze pagina nogmaals."

#: build/search-regex.js:19
msgid "Cached Search Regex detected"
msgstr "Gecachede Search Regex gevonden"

#. translators: number of results to show
#: build/search-regex.js:8
msgid "Show %s more"
msgid_plural "Show %s more"
msgstr[0] "Nog %s weergeven"
msgstr[1] "Nog %s weergeven"

#: build/search-regex.js:6
msgid "Maximum number of matches exceeded and hidden from view. These will be included in any replacements."
msgstr ""
"Het maximum aantal zoekresultaten is overschreden en niet zichtbaar. Deze zullen \n"
"worden gebruikt bij vervangingen."

#: build/search-regex.js:6
msgid "Delete"
msgstr "Verwijderen"

#: build/search-regex.js:6 build/search-regex.js:8
msgid "Edit"
msgstr "Bewerken"

#: build/search-regex.js:4
msgid "Check Again"
msgstr "Opnieuw controleren"

#. translators: test percent
#: build/search-regex.js:4
msgid "Testing - %s%%"
msgstr "Aan het testen - %s%%"

#: build/search-regex.js:2
msgid "Show Problems"
msgstr "Toon problemen"

#: build/search-regex.js:2
msgid "Summary"
msgstr "Samenvatting"

#: build/search-regex.js:2
msgid "You are using a broken REST API route. Changing to a working API should fix the problem."
msgstr "Je gebruikte een defecte REST API route. Wijziging naar een werkende API zou het probleem moeten oplossen."

#: build/search-regex.js:2
msgid "Your REST API is not working and the plugin will not be able to continue until this is fixed."
msgstr "Je REST API werkt niet en de plugin kan niet verder voordat dit is opgelost."

#: build/search-regex.js:2
msgid "There are some problems connecting to your REST API. It is not necessary to fix these problems and the plugin is able to work."
msgstr "Er zijn enkele problemen in de verbinding met je REST API. Het is niet nodig om deze problemen op te lossen en de plugin werkt gewoon."

#: build/search-regex.js:2
msgid "Unavailable"
msgstr "Niet beschikbaar"

#: build/search-regex.js:2
msgid "Not working but fixable"
msgstr "Werkt niet, maar te repareren"

#: build/search-regex.js:2
msgid "Working but some issues"
msgstr "Werkt, maar met problemen"

#: build/search-regex.js:2
msgid "Good"
msgstr "Goed"

#: build/search-regex.js:2
msgid "Current API"
msgstr "Huidige API"

#: build/search-regex.js:2
msgid "Switch to this API"
msgstr "Gebruik deze API"

#: build/search-regex.js:2
msgid "Hide"
msgstr "Verberg"

#: build/search-regex.js:2
msgid "Show Full"
msgstr "Toon volledig"

#: build/search-regex.js:2
msgid "Working!"
msgstr "Werkt!"

#: build/search-regex.js:19
msgid "Finished!"
msgstr "Klaar!"

#: build/search-regex.js:6 build/search-regex.js:18
msgid "Cancel"
msgstr "Annuleren"

#: build/search-regex.js:4 build/search-regex.js:6
msgid "Replace"
msgstr "Vervangen"

#: build/search-regex.js:4 build/search-regex.js:6
msgid "Remove"
msgstr "Verwijderen"

#: build/search-regex.js:2 build/search-regex.js:6
msgid "Multi"
msgstr "Meerdere"

#: build/search-regex.js:6
msgid "Single"
msgstr "Enkel"

#: build/search-regex.js:21
msgid "Support"
msgstr "Ondersteuning"

#: includes/source/core/source-options.php:99 build/search-regex.js:21
msgid "Options"
msgstr "Opties"

#: build/search-regex.js:21
msgid "Search & Replace"
msgstr "Zoek en vervang"

#: build/search-regex.js:4
msgid "If you are using WordPress 5.2 or newer then look at your {{link}}Site Health{{/link}} and resolve any issues."
msgstr "Als je WordPress 5.2 of nieuwer gebruikt, kijk dan bij {{link}}Sitediagnose{{/link}} en los de problemen op."

#: build/search-regex.js:4
msgid "{{link}}Please temporarily disable other plugins!{{/link}} This fixes so many problems."
msgstr "{{link}}Zet andere plugins tijdelijk uit!{{/link}} Dit lost heel vaak problemen op."

#: build/search-regex.js:4
msgid "{{link}}Caching software{{/link}}, in particular Cloudflare, can cache the wrong thing. Try clearing all your caches."
msgstr "{{link}}Caching software{{/link}}, en zeker Cloudflare, kunnen het verkeerde cachen. Probeer alle cache te verwijderen."

#: build/search-regex.js:4 build/search-regex.js:19
msgid "What do I do next?"
msgstr "Wat moet ik nu doen?"

#: build/search-regex.js:2
msgid "Something went wrong 🙁"
msgstr "Er is iets fout gegaan 🙁"

#: build/search-regex.js:2
msgid "Log out, clear your browser cache, and log in again - your browser has cached an old session."
msgstr "Log uit, wis de browsercache en log opnieuw in - je browser heeft een oude sessie in de cache opgeslagen."

#: build/search-regex.js:2
msgid "Reload the page - your current session is old."
msgstr "Herlaad de pagina - je huidige sessie is oud."

#: build/search-regex.js:2
msgid "Include these details in your report along with a description of what you were doing and a screenshot."
msgstr "Voeg deze gegevens toe aan je melding, samen met een beschrijving van wat je deed en een schermafbeelding."

#: includes/source/core/source-comment.php:168
#: includes/source/core/source-user.php:155 build/search-regex.js:2
#: build/search-regex.js:6 build/search-regex.js:19
msgid "Email"
msgstr "E-mail"

#: build/search-regex.js:2 build/search-regex.js:6 build/search-regex.js:19
msgid "Create An Issue"
msgstr "Meld een probleem"

#: includes/source/core/source-post.php:320
#: includes/source/core/source-post.php:336
msgid "Closed"
msgstr "Gesloten"

#: build/search-regex.js:6
msgid "Save"
msgstr "Opslaan"

#: build/search-regex.js:2
msgid "Unable to make request due to browser security. This is typically because your WordPress and Site URL settings are inconsistent, or the request was blocked by your site CORS policy."
msgstr "Niet mogelijk om een verzoek te doen vanwege browser beveiliging. Dit gebeurt meestal omdat WordPress en de site URL niet overeenkomen, of het verzoek wordt geblokkeerd door het CORS beleid van je site."

#: build/search-regex.js:2
msgid "Possible cause"
msgstr "Mogelijke oorzaak"

#: build/search-regex.js:2
msgid "This could be a security plugin, or your server is out of memory or has an external error. Please check your server error log"
msgstr "Dit kan een beveiliging plugin zijn. Of je server heeft geen geheugen meer of heeft een externe fout. Bekijk de fout log op je server"

#: build/search-regex.js:2
msgid "Your server has rejected the request for being too big. You will need to reconfigure it to continue."
msgstr "Je server heeft de aanvraag geweigerd omdat het te groot is. Je moet het aanpassen om door te gaan."

#: build/search-regex.js:2
msgid "Your REST API is probably being blocked by a security plugin. Please disable this, or configure it to allow REST API requests."
msgstr "Je REST API wordt waarschijnlijk geblokkeerd door een beveiliging plugin. Zet de plugin uit, of configureer hem zodat hij REST API verzoeken toestaat."

#: build/search-regex.js:2
msgid "Read this REST API guide for more information."
msgstr "Lees deze REST API gids voor meer informatie."

#: build/search-regex.js:2
msgid "WordPress did not return a response. This could mean an error occurred or that the request was blocked. Please check your server error_log."
msgstr "WordPress heeft geen reactie teruggestuurd. Dit kan betekenen dat er een fout is opgetreden of dat de aanvraag is geblokkeerd. Controleer je server error_log."

#. Author of the plugin
msgid "John Godley"
msgstr "John Godley"

#. Description of the plugin
msgid "Adds search and replace functionality across posts, pages, comments, and meta-data, with full regular expression support"
msgstr "Voeg zoek en vervang functionaliteit toe in berichten, pagina's, reacties en metadata, met volledige ondersteuning van reguliere expressies"

#. Plugin URI of the plugin
msgid "https://searchregex.com/"
msgstr "https://searchregex.com/"

#. Plugin Name of the plugin
#: build/search-regex.js:21
msgid "Search Regex"
msgstr "Search Regex"