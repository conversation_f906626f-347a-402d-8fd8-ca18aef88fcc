# Translation of Plugins - Search Regex - Development (trunk) in Dutch (Belgium)
# This file is distributed under the same license as the Plugins - Search Regex - Development (trunk) package.
msgid ""
msgstr ""
"PO-Revision-Date: 2022-06-17 11:44:28+0000\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=n != 1;\n"
"X-Generator: GlotPress/4.0.0-alpha.1\n"
"Language: nl_BE\n"
"Project-Id-Version: Plugins - Search Regex - Development (trunk)\n"

#: source/core/meta.php:24
msgid "Serialized Value"
msgstr ""

#: source/core/post.php:85
msgid "Exclude drafts"
msgstr ""

#: search-regex-strings.php:236
msgid "Please review your data and try again."
msgstr ""

#: search-regex-strings.php:235
msgid "There was a problem making a request to your site. This could indicate you provided data that did not match requirements, or that the plugin sent a bad request."
msgstr ""

#: search-regex-strings.php:234
msgid "Bad data"
msgstr ""

#: search-regex-strings.php:218
msgid "Your REST API is returning a 404 page. This is almost certainly an external plugin or server configuration issue."
msgstr ""

#: search-regex-strings.php:180
msgid "2000 per page"
msgstr ""

#: search-regex-strings.php:179
msgid "1000 per page"
msgstr ""

#: search-regex-strings.php:92
msgid "Set a preset to use by default when Search Regex is loaded."
msgstr ""

#: search-regex-strings.php:91
msgid "No default preset"
msgstr ""

#: search-regex-strings.php:90
msgid "Default Preset"
msgstr ""

#: search-regex-strings.php:244
msgid "Your admin pages are being cached. Clear this cache and try again. There may be multiple caches involved."
msgstr ""

#: search-regex-strings.php:241
msgid "This is usually fixed by doing one of the following:"
msgstr ""

#: search-regex-strings.php:240
msgid "You are using an old or cached session"
msgstr ""

#: search-regex-strings.php:214
msgid "Debug Information"
msgstr ""

#: search-regex-strings.php:213
msgid "Show debug"
msgstr ""

#: search-regex-strings.php:230
msgid "WordPress returned an unexpected message. This could be a PHP error from another plugin, or data inserted by your theme."
msgstr ""

#: search-regex-strings.php:229
msgid "Your WordPress REST API has been disabled. You will need to enable it to continue."
msgstr ""

#: search-regex-strings.php:224
msgid "Your REST API is being redirected. Please remove the redirection for the API."
msgstr ""

#: search-regex-strings.php:222
msgid "A security plugin or firewall is blocking access. You will need to whitelist the REST API."
msgstr ""

#: search-regex-strings.php:221
msgid "Your server configuration is blocking access to the REST API. You will need to fix this."
msgstr ""

#: search-regex-strings.php:220
msgid "Check your {{link}}Site Health{{/link}} and fix any issues."
msgstr ""

#: search-regex-strings.php:219
msgid "Can you access your {{api}}REST API{{/api}} without it redirecting? If not then you will need to fix any issues."
msgstr ""

#: search-regex-strings.php:205
msgid "Preset"
msgstr ""

#: search-regex-strings.php:183
msgid "Upload"
msgstr ""

#: search-regex-strings.php:182
msgid "Add File"
msgstr ""

#: search-regex-strings.php:171
msgid "Preset saved"
msgstr ""

#: search-regex-strings.php:148
msgid "Copy to clipboard"
msgstr ""

#: search-regex-strings.php:145
msgid "Are you sure you want to delete this preset?"
msgstr ""

#: search-regex-strings.php:144
msgid "Locked fields"
msgstr ""

#: search-regex-strings.php:139
msgid "For example, create tag {{code}}URL{{/code}} and title {{code}}Image URL{{/code}}. Your search could be {{code}}<img src=\"URL\">{{/code}}. When the preset is used it will ask the user for the {{code}}Image URL{{/code}} instead of the full search phrase."
msgstr ""

#: search-regex-strings.php:138
msgid "A tag creates a custom input field. Insert the tag anywhere in the search or replace field and when the preset is used it will be replaced with a custom text field with the tag label."
msgstr ""

#: search-regex-strings.php:137
msgid "Enter tag which is used in the field"
msgstr ""

#: search-regex-strings.php:136
msgid "Tag"
msgstr ""

#: search-regex-strings.php:135
msgid "Enter tag title shown to user"
msgstr ""

#: search-regex-strings.php:133
msgid "Tags"
msgstr ""

#: search-regex-strings.php:132
msgid "Locking a field removes it from the search form and prevents changes."
msgstr ""

#: search-regex-strings.php:131
msgid "Fields"
msgstr ""

#: search-regex-strings.php:130
msgid "Locked Fields"
msgstr ""

#: search-regex-strings.php:129
msgid "Advanced preset"
msgstr ""

#: search-regex-strings.php:128
msgid "Describe the preset"
msgstr ""

#: search-regex-strings.php:127
msgid "Preset Description"
msgstr ""

#: search-regex-strings.php:126
msgid "Give the preset a name"
msgstr ""

#: search-regex-strings.php:125
msgid "Preset Name"
msgstr ""

#: search-regex-strings.php:124
msgid "Edit preset"
msgstr ""

#: search-regex-strings.php:123
msgid "Results per page"
msgstr ""

#: search-regex-strings.php:122
msgid "Source Flags"
msgstr ""

#: search-regex-strings.php:117
msgid "remove phrase"
msgstr ""

#: search-regex-strings.php:116
msgid "no phrase"
msgstr ""

#: search-regex-strings.php:115
msgid "Import"
msgstr ""

#: search-regex-strings.php:114
msgid "Paste a single preset JSON."
msgstr ""

#: search-regex-strings.php:113
msgid "Please check your JSON data is a valid preset. You may have copied it incorrectly, or pasted something that is not a preset."
msgstr ""

#: search-regex-strings.php:112
msgid "Unable to import preset"
msgstr ""

#: search-regex-strings.php:111
msgid "Import preset from clipboard"
msgstr ""

#: search-regex-strings.php:110
msgid "Done"
msgstr ""

#: search-regex-strings.php:109
msgid "Uploaded %(total)d preset"
msgid_plural "Uploaded %(total)d presets"
msgstr[0] ""
msgstr[1] ""

#: search-regex-strings.php:108
msgid "Importing"
msgstr ""

#: search-regex-strings.php:107
msgid "File selected"
msgstr ""

#: search-regex-strings.php:106
msgid "Click 'Add File' or drag and drop here."
msgstr ""

#: search-regex-strings.php:105
msgid "Import a JSON file"
msgstr ""

#: search-regex-strings.php:104
msgid "Import JSON"
msgstr ""

#: search-regex-strings.php:103
msgid "Export JSON"
msgstr ""

#: search-regex-strings.php:102
msgid "Download presets!"
msgstr ""

#: search-regex-strings.php:101
msgid "There are no presets"
msgstr ""

#: search-regex-strings.php:100
msgid "Flags"
msgstr ""

#: search-regex-strings.php:73 search-regex-strings.php:75
msgid "Presets"
msgstr ""

#: search-regex-strings.php:62
msgid "If that did not help then {{strong}}create an issue{{/strong}} or send it in an {{strong}}email{{/strong}}."
msgstr ""

#: search-regex-strings.php:61
msgid "Please check the {{link}}support site{{/link}} before proceeding further."
msgstr ""

#: search-regex-strings.php:15
msgid "Enter preset name"
msgstr ""

#: search-regex-strings.php:14
msgid "Enter a name for your preset"
msgstr ""

#: search-regex-strings.php:13
msgid "Saving Preset"
msgstr ""

#: search-regex-strings.php:12
msgid "Update current preset"
msgstr ""

#: search-regex-strings.php:11
msgid "Save search as new preset"
msgstr ""

#: search-regex-strings.php:10
msgid "No preset"
msgstr ""

#: search-regex-strings.php:9
msgid "Saving preset"
msgstr ""

#: models/source-manager.php:177
msgid "Advanced"
msgstr ""

#: models/source-manager.php:163
msgid "Standard"
msgstr ""

#: search-regex-strings.php:186
msgid "matched rows = %(searched)s, phrases = %(found)s"
msgstr ""

#: search-regex-strings.php:149
msgid "Please backup your data before making modifications."
msgstr ""

#: search-regex-strings.php:89
msgid "Show row actions as dropdown menu."
msgstr ""

#: search-regex-strings.php:48
msgid "Replace Row"
msgstr ""

#: search-regex-strings.php:47
msgid "Delete Row"
msgstr ""

#: search-regex-strings.php:46
msgid "Inline Editor"
msgstr ""

#: search-regex-strings.php:45
msgid "Edit Page"
msgstr ""

#: search-regex-strings.php:27
msgid "%s row."
msgid_plural "%s rows."
msgstr[0] ""
msgstr[1] ""

#: search-regex-strings.php:26
msgid "%s phrase."
msgid_plural "%s phrases."
msgstr[0] ""
msgstr[1] ""

#: search-regex-strings.php:25
msgid "Replace Information"
msgstr ""

#: search-regex-strings.php:226
msgid "An unknown error occurred."
msgstr ""

#: source/core/post.php:132
msgid "GUID"
msgstr "GUID"

#: source/core/post.php:131
msgid "Slug"
msgstr "Slug"

#: source/core/post.php:129
msgid "Excerpt"
msgstr "Samenvatting"

#: source/core/post.php:128
msgid "Content"
msgstr "Inhoud"

#: source/core/post.php:84
msgid "Search GUID"
msgstr "Zoek GUID"

#: source/core/user.php:37
msgid "Display name"
msgstr "Weergavenaam"

#: source/core/user.php:35
msgid "Nicename"
msgstr "Schermnaam (backend)"

#: source/core/options.php:20 source/core/meta.php:27
msgid "Value"
msgstr "Waarde"

#: source/core/comment.php:66
msgid "Include spam comments"
msgstr "Inclusief spam reacties"

#: source/core/comment.php:25
msgid "Comment"
msgstr "Reactie"

#: search-regex-strings.php:98 source/core/comment.php:22
#: source/core/options.php:19 source/core/meta.php:19
msgid "Name"
msgstr "Naam"

#: source/plugin/redirection.php:82
msgid "Search your redirects"
msgstr "Zoek je redirects"

#: search-regex-strings.php:134 source/plugin/redirection.php:28
#: source/core/post.php:130
msgid "Title"
msgstr "Titel"

#: source/plugin/redirection.php:27 source/core/comment.php:24
#: source/core/user.php:36
msgid "URL"
msgstr "URL"

#. translators: 1: server PHP version. 2: required PHP version.
#: search-regex.php:39
msgid "Disabled! Detected PHP %1$s, need PHP %2$s+"
msgstr "Uitgeschakeld! Gedetecteerd PHP %1$s, nodig PHP %2$s+"

#: models/source-manager.php:184
msgid "Plugins"
msgstr "Plugins"

#: models/source-manager.php:170
msgid "Specific Post Types"
msgstr "Specifieke berichttypen"

#: models/source-manager.php:52
msgid "Search all WordPress options."
msgstr "Doorzoek alle WordPress opties"

#: models/source-manager.php:51
msgid "WordPress Options"
msgstr "WordPress opties"

#: models/source-manager.php:85
msgid "Search user meta name and values."
msgstr "Zoek gebruiker gegevens naam en waarden."

#: models/source-manager.php:84
msgid "User Meta"
msgstr "Gebruiker Meta"

#: models/source-manager.php:45
msgid "Search user email, URL, and name."
msgstr "Zoek e-mail, URL en naam van gebruiker."

#: models/source-manager.php:44
msgid "Users"
msgstr "Gebruikers"

#: models/source-manager.php:78
msgid "Search comment meta names and values."
msgstr "Zoek reactie meta namen en waarden"

#: models/source-manager.php:77
msgid "Comment Meta"
msgstr "Reactie Meta"

#: models/source-manager.php:38
msgid "Search comment content, URL, and author, with optional support for spam comments."
msgstr "Doorzoek reactie inhoud, URL en auteur, met optie voor spam reacties. "

#: models/source-manager.php:37
msgid "Comments"
msgstr "Reacties"

#: models/source-manager.php:71
msgid "Search post meta names and values."
msgstr "Zoek bericht meta namen en waarden"

#: models/source-manager.php:70
msgid "Post Meta"
msgstr "Bericht meta"

#: models/source-manager.php:21
msgid "Search all posts, pages, and custom post types."
msgstr "Doorzoek alle berichten, pagina's en aangepaste bericht typen."

#: models/source-manager.php:20
msgid "All Post Types"
msgstr "Alle berichttypen"

#: search-regex-admin.php:401
msgid "Please enable JavaScript"
msgstr "Schakel Javascript in"

#: search-regex-admin.php:397
msgid "Loading, please wait..."
msgstr "Aan het laden..."

#: search-regex-admin.php:380
msgid "Create Issue"
msgstr "Meld een probleem"

#: search-regex-admin.php:377
msgid "<code>SearchRegexi10n</code> is not defined. This usually means another plugin is blocking Search Regex from loading. Please disable all plugins and try again."
msgstr "<code>SearchRegexi10n</code> is niet gedefinieerd. Dit betekent meestal dat een andere plugin Search Regex blokkeert om te laden. Zet alle plugins uit en probeer het opnieuw."

#: search-regex-admin.php:376
msgid "If you think Search Regex is at fault then create an issue."
msgstr "Denk je dat Search Regex het probleem veroorzaakt, maak dan een probleemrapport aan."

#: search-regex-admin.php:375
msgid "Please see the <a href=\"https://searchregex.com/support/problems/\">list of common problems</a>."
msgstr "Bekijk hier de <a href=\"https://searchregex.com/support/problems/\">lijst van algemene problemen</a>."

#: search-regex-admin.php:374
msgid "Please note that Search Regex requires the WordPress REST API to be enabled. If you have disabled this then you won't be able to use Search Regex"
msgstr "Search Regex vereist dat de WordPress REST API geactiveerd is. Heb je deze uitgezet, dan kun je Search Regex niet gebruiken."

#: search-regex-admin.php:372
msgid "Also check if your browser is able to load <code>search-regex.js</code>:"
msgstr "Controleer ook of je browser <code>search-regex.js</code> kan laden:"

#: search-regex-admin.php:370
msgid "This may be caused by another plugin - look at your browser's error console for more details."
msgstr "Dit kan worden veroorzaakt door een andere plugin - bekijk je browser's foutconsole voor meer gegevens."

#: search-regex-admin.php:369
msgid "Unable to load Search Regex ☹️"
msgstr "Laden van Search Regex ☹️ onmogelijk"

#: search-regex-admin.php:354
msgid "Unable to load Search Regex"
msgstr "Laden van Search Regex onmogelijk"

#. translators: 1: Expected WordPress version, 2: Actual WordPress version
#: search-regex-admin.php:351
msgid "Search Regex requires WordPress v%1$1s, you are using v%2$2s - please update your WordPress"
msgstr "Search Regex heeft WordPress v%1$1s nodig, en je gebruikt v%2$2s - update je WordPress"

#: search-regex-admin.php:255
msgid "Search Regex Support"
msgstr "Search Regex ondersteuning"

#. translators: URL
#: search-regex-admin.php:246
msgid "You can find full documentation about using Search Regex on the <a href=\"%s\" target=\"_blank\">searchregex.com</a> support site."
msgstr "Je kunt de volledige documentatie over het gebruik van Search Regex vinden op de <a href=\"%s\" target=\"_blank\">searchregex.com</a> support site."

#: search-regex-admin.php:65
msgid "Settings"
msgstr "Instellingen"

#: search-regex-strings.php:88 search-regex-strings.php:212
msgid "Actions"
msgstr "Acties"

#: search-regex-strings.php:211
msgid "Matched Phrases"
msgstr "Gevonden zoektermen"

#: search-regex-strings.php:210
msgid "Matches"
msgstr "Gevonden"

#: search-regex-strings.php:209
msgid "Row ID"
msgstr "Regel ID"

#: search-regex-strings.php:207
msgid "Maximum number of page requests has been exceeded and the search stopped. Try to be more specific with your search term."
msgstr "Het maximum aantal van pagina aanvragen is overschreven en zoeken is gestopt. Probeer een preciezere zoekterm te gebruiken."

#: search-regex-strings.php:206
msgid "No more matching results found."
msgstr "Geen resultaten meer gevonden."

#: search-regex-strings.php:196
msgid "Last page"
msgstr "Laatste pagina"

#: search-regex-strings.php:194
msgid "Page %(current)s of %(total)s"
msgstr "Pagina %(current)s van %(total)s"

#: search-regex-strings.php:191
msgid "Matches: %(phrases)s across %(rows)s database row."
msgid_plural "Matches: %(phrases)s across %(rows)s database rows."
msgstr[0] "Gevonden in: %(phrases)s in %(rows)s database regel."
msgstr[1] "Gevonden in: %(phrases)s in %(rows)s database regels."

#: search-regex-strings.php:190 search-regex-strings.php:195
msgid "Next page"
msgstr "Volgende pagina"

#: search-regex-strings.php:189
msgid "Progress %(current)s$"
msgstr "Voortgang %(current)s$"

#: search-regex-strings.php:188 search-regex-strings.php:193
msgid "Prev page"
msgstr "Vorige pagina"

#: search-regex-strings.php:187 search-regex-strings.php:192
msgid "First page"
msgstr "Eerste pagina"

#: search-regex-strings.php:185
msgid "%s database row in total"
msgid_plural "%s database rows in total"
msgstr[0] "%s database regel totaal"
msgstr[1] "%s database regels totaal"

#: search-regex-strings.php:178
msgid "500 per page"
msgstr "500 per pagina"

#: search-regex-strings.php:177
msgid "250 per page"
msgstr "250 per pagina"

#: search-regex-strings.php:176
msgid "100 per page"
msgstr "100 per pagina"

#: search-regex-strings.php:175
msgid "50 per page"
msgstr "50 per pagina "

#: search-regex-strings.php:174
msgid "25 per page"
msgstr "25 per pagina "

#: search-regex-strings.php:173
msgid "Ignore Case"
msgstr "Negeer hoofdletter gebruik"

#: search-regex-strings.php:172
msgid "Regular Expression"
msgstr "Reguliere expressie"

#: search-regex-strings.php:170
msgid "Row updated"
msgstr "Regel bijgewerkt"

#: search-regex-strings.php:169
msgid "Row replaced"
msgstr "Regel vervangen"

#: search-regex-strings.php:168
msgid "Row deleted"
msgstr "Regel verwijderd"

#: search-regex-strings.php:167
msgid "Settings saved"
msgstr "Instellingen opgeslagen"

#: search-regex-strings.php:166 search-regex-admin.php:238
msgid "{{link}}Source Flags{{/link}} - additional options for the selected source. For example, include post {{guid}}GUID{{/guid}} in the search."
msgstr ""
"{{link}}Zoekopties{{/link}} - aanvullende opties voor de selecteerde bron. Bijvoorbeeld\n"
"om bericht {{guid}}GUID{{/guid}} toe te voegen aan de zoekterm."

#: search-regex-strings.php:165 search-regex-admin.php:251
msgid "{{link}}Source{{/link}} - the source of data you wish to search. For example, posts, pages, or comments."
msgstr "{{link}}Bron{{/link}} - de bron van de gegevens waarin je zoekt. Bijvoorbeeld: berichten, pagina's, reacties."

#: search-regex-strings.php:164 search-regex-admin.php:250
msgid "{{link}}Regular expression{{/link}} - a way of defining a pattern for text matching. Provides more advanced matches."
msgstr "{{link}}Reguliere expressie{{/link}} - een manier om patronen aan maken om tekst mee te zoeken. Geeft meer geavanceerde zoekresultaten."

#: search-regex-strings.php:163 search-regex-admin.php:249
msgid "{{link}}Search Flags{{/link}} - additional qualifiers for your search, to enable case insensitivity, and to enable regular expression support."
msgstr "{{link}}Zoekopties{{/link}} - aanvullende opties voor je zoekterm, om hoofdlettergebruik te negeren en voor gebruik van reguliere expressies."

#: search-regex-strings.php:162 search-regex-admin.php:247
msgid "The following concepts are used by Search Regex:"
msgstr "De volgende concepten worden gebruikt door Search Regex:"

#: search-regex-strings.php:161
msgid "Quick Help"
msgstr "Snelle hulp"

#: search-regex-strings.php:160
msgid "Like this plugin? You might want to consider {{link}}Redirection{{/link}}, a plugin to manage redirects, from the same author."
msgstr "Vind je deze plugin leuk? Je kunt overwegen {{link}}Redirection{{/link}}, een plugin om omleidingen te beheren, van dezelfde auteur."

#: search-regex-strings.php:159 source/plugin/redirection.php:81
msgid "Redirection"
msgstr "Omleiding"

#: search-regex-strings.php:158
msgid "If you want to submit information that you don't want in a public repository then send it directly via {{email}}email{{/email}} - include as much information as you can!"
msgstr "Als je informatie wilt sturen, maar die je niet in de openbare repository wilt delen, stuur dan een {{email}}email{{/email}} direct aan mij - met zoveel informatie als mogelijk!"

#: search-regex-strings.php:157
msgid "Please note that any support is provide on as-time-is-available basis and is not guaranteed. I do not provide paid support."
msgstr "Support wordt gegeven op basis van beschikbare tijd en is niet gegarandeerd. Ik geef geen betaalde ondersteuning."

#: search-regex-strings.php:156
msgid "If you want to report a bug please read the {{report}}Reporting Bugs{{/report}} guide."
msgstr "Wil je een bug doorgeven, lees dan de {{report}}Reporting Bugs{{/report}} gids."

#: search-regex-strings.php:155
msgid "Full documentation for Search Regex can be found at {{site}}https://searchregex.com{{/site}}."
msgstr "Volledige documentatie voor Search Regex kan je vinden op {{site}}https://searchregex.com{{/site}}."

#: search-regex-strings.php:154
msgid "Need more help?"
msgstr "Meer hulp nodig?"

#: search-regex-strings.php:204
msgid "Results"
msgstr "Resultaten"

#: search-regex-strings.php:203
msgid "Source Options"
msgstr "Bron opties"

#: search-regex-strings.php:121 search-regex-strings.php:202
#: search-regex-strings.php:208
msgid "Source"
msgstr "Bron"

#: search-regex-strings.php:200 search-regex-strings.php:201
msgid "Enter global replacement text"
msgstr "Term voor vervangen invoeren"

#: search-regex-strings.php:120 search-regex-strings.php:198
msgid "Search Flags"
msgstr "Zoek opties"

#: search-regex-strings.php:53
msgid "Enter search phrase"
msgstr "Zoekterm invoeren"

#: search-regex-strings.php:152
msgid "Replace All"
msgstr "Alles vervangen"

#: search-regex-strings.php:99 search-regex-strings.php:118
#: search-regex-strings.php:142 search-regex-strings.php:151
#: search-regex-strings.php:197
msgid "Search"
msgstr "Zoeken"

#: search-regex-strings.php:150
msgid "Search and replace information in your database."
msgstr "Zoek en vervang informatie in je database."

#: search-regex-strings.php:95
msgid "Update"
msgstr "Bijwerken"

#: search-regex-strings.php:94
msgid "How Search Regex uses the REST API - don't change unless necessary"
msgstr "Hoe Search Regex de REST API gebruikt - niet veranderen als het niet noodzakelijk is"

#: search-regex-strings.php:93
msgid "REST API"
msgstr "REST-API"

#: search-regex-strings.php:87
msgid "I'm a nice person and I have helped support the author of this plugin"
msgstr "Ik ben een aardig persoon en ik heb de auteur van deze plugin geholpen met ondersteuning"

#: search-regex-strings.php:86
msgid "Relative REST API"
msgstr "Relatieve REST API"

#: search-regex-strings.php:85
msgid "Raw REST API"
msgstr "Raw REST API"

#: search-regex-strings.php:84
msgid "Default REST API"
msgstr "Standaard REST API"

#: search-regex-strings.php:83
msgid "Plugin Support"
msgstr "Plugin ondersteuning"

#: search-regex-strings.php:82
msgid "Support 💰"
msgstr "Ondersteuning 💰"

#: search-regex-strings.php:81
msgid "You get useful software and I get to carry on making it better."
msgstr "Je krijgt goed bruikbare software en ik kan doorgaan met het verbeteren ervan."

#: search-regex-strings.php:80
msgid "Search Regex is free to use - life is wonderful and lovely! It has required a great deal of time and effort to develop and you can help support this development by {{strong}}making a small donation{{/strong}}."
msgstr "Search Regex is gratis te gebruiken - het leven is life is wonderbaarlijk en verrukkelijk! Het kostte me veel tijd en moeite om dit te ontwikkelen. Je kunt verdere ontwikkeling ondersteunen met het doen van {{strong}}een kleine donatie{{/strong}}."

#: search-regex-strings.php:79
msgid "I'd like to support some more."
msgstr "Ik wil graag meer bijdragen."

#: search-regex-strings.php:78
msgid "You've supported this plugin - thank you!"
msgstr "Je hebt deze plugin gesteund - dankjewel!"

#: search-regex-strings.php:60
msgid "If that doesn't help, open your browser's error console and create a {{link}}new issue{{/link}} with the details."
msgstr "Werkt dit niet, open dan je browser's foutconsole en maak een {{link}}nieuw probleemrapport{{/link}} aan met alle gegevens."

#: search-regex-strings.php:59 search-regex-admin.php:371
msgid "If you are using a page caching plugin or service (CloudFlare, OVH, etc) then you can also try clearing that cache."
msgstr "Als je gebruik maakt van een pagina caching plugin of dienst (CloudFlare, OVH, etc.), dan kan je ook proberen om die cache leeg te maken."

#: search-regex-strings.php:58
msgid "Search Regex is not working. Try clearing your browser cache and reloading this page."
msgstr "Search Regex werkt niet. Probeer je browser cache leeg te maken en deze pagina opnieuw te laden."

#: search-regex-strings.php:57
msgid "clearing your cache."
msgstr "je cache opschonen."

#: search-regex-strings.php:56
msgid "If you are using a caching system such as Cloudflare then please read this: "
msgstr "Gebruik je een caching systeem zoals Cloudflare, lees dan dit:"

#: search-regex-strings.php:55
msgid "Please clear your browser cache and reload this page."
msgstr "Maak je browser cache leeg en laad deze pagina nogmaals."

#: search-regex-strings.php:54
msgid "Cached Search Regex detected"
msgstr "Gecachede Search Regex gevonden"

#: search-regex-strings.php:52
msgid "Show %s more"
msgid_plural "Show %s more"
msgstr[0] "Nog %s weergeven"
msgstr[1] "Nog %s weergeven"

#: search-regex-strings.php:51
msgid "Maximum number of matches exceeded and hidden from view. These will be included in any replacements."
msgstr ""
"Het maximum aantal zoekresultaten is overschreden en niet zichtbaar. Deze zullen \n"
"worden gebruikt bij vervangingen."

#: search-regex-strings.php:50
msgid "Replace %(count)s match."
msgid_plural "Replace %(count)s matches."
msgstr[0] "Vervang %(count)s gevonden."
msgstr[1] "Vervang %(count)s gevonden."

#: search-regex-strings.php:147
msgid "Delete"
msgstr "Verwijderen"

#: search-regex-strings.php:146
msgid "Edit"
msgstr "Bewerken"

#: search-regex-strings.php:49
msgid "Replacement for all matches in this row"
msgstr "Vervanging voor alle gevonden zoektermen in deze regel"

#: search-regex-strings.php:44
msgid "Check Again"
msgstr "Opnieuw controleren"

#: search-regex-strings.php:43
msgid "Testing - %s$"
msgstr "Aan het testen - %s$"

#: search-regex-strings.php:42
msgid "Show Problems"
msgstr "Toon problemen"

#: search-regex-strings.php:41
msgid "Summary"
msgstr "Samenvatting"

#: search-regex-strings.php:40
msgid "You are using a broken REST API route. Changing to a working API should fix the problem."
msgstr "Je gebruikte een defecte REST API route. Wijziging naar een werkende API zou het probleem moeten oplossen."

#: search-regex-strings.php:39
msgid "Your REST API is not working and the plugin will not be able to continue until this is fixed."
msgstr "Je REST API werkt niet en de plugin kan niet verder voordat dit is opgelost."

#: search-regex-strings.php:38
msgid "There are some problems connecting to your REST API. It is not necessary to fix these problems and the plugin is able to work."
msgstr "Er zijn enkele problemen in de verbinding met je REST API. Het is niet nodig om deze problemen op te lossen en de plugin werkt gewoon."

#: search-regex-strings.php:37
msgid "Unavailable"
msgstr "Niet beschikbaar"

#: search-regex-strings.php:36
msgid "Not working but fixable"
msgstr "Werkt niet, maar te repareren"

#: search-regex-strings.php:35
msgid "Working but some issues"
msgstr "Werkt, maar met problemen"

#: search-regex-strings.php:34
msgid "Good"
msgstr "Goed"

#: search-regex-strings.php:33
msgid "Current API"
msgstr "Huidige API"

#: search-regex-strings.php:32
msgid "Switch to this API"
msgstr "Gebruik deze API"

#: search-regex-strings.php:31
msgid "Hide"
msgstr "Verberg"

#: search-regex-strings.php:30
msgid "Show Full"
msgstr "Toon volledig"

#: search-regex-strings.php:29
msgid "Working!"
msgstr "Werkt!"

#: search-regex-strings.php:28
msgid "Finished!"
msgstr "Klaar!"

#: search-regex-strings.php:24
msgid "Replace progress"
msgstr "Voortgang vervangen"

#: search-regex-strings.php:17 search-regex-strings.php:23
#: search-regex-strings.php:141 search-regex-strings.php:153
#: search-regex-strings.php:184
msgid "Cancel"
msgstr "Annuleren"

#: search-regex-strings.php:22 search-regex-strings.php:119
#: search-regex-strings.php:143 search-regex-strings.php:199
msgid "Replace"
msgstr "Vervangen"

#: search-regex-strings.php:21
msgid "Search phrase will be removed"
msgstr "Zoekterm zal worden verwijderd"

#: search-regex-strings.php:20
msgid "Remove"
msgstr "Verwijderen"

#: search-regex-strings.php:19
msgid "Multi"
msgstr "Meerdere"

#: search-regex-strings.php:18
msgid "Single"
msgstr "Enkel"

#: search-regex-strings.php:181
msgid "View notice"
msgstr "Toon bericht"

#: search-regex-strings.php:8
msgid "Replace single phrase."
msgstr "Vervang enkele zoekterm"

#: search-regex-strings.php:7
msgid "Replacement for this match"
msgstr "Vervanging voor deze gevonden zoekterm"

#: search-regex-strings.php:72 search-regex-strings.php:77
msgid "Support"
msgstr "Support"

#: search-regex-strings.php:71 search-regex-strings.php:76
msgid "Options"
msgstr "Opties"

#: search-regex-strings.php:74
msgid "Search & Replace"
msgstr "Zoek en vervang"

#: search-regex-strings.php:69
msgid "If you are using WordPress 5.2 or newer then look at your {{link}}Site Health{{/link}} and resolve any issues."
msgstr "Als je WordPress 5.2 of nieuwer gebruikt, kijk dan bij {{link}}Sitediagnose{{/link}} en los de problemen op."

#: search-regex-strings.php:68
msgid "{{link}}Please temporarily disable other plugins!{{/link}} This fixes so many problems."
msgstr "{{link}}Zet andere plugins tijdelijk uit!{{/link}} Dit lost heel vaak problemen op."

#: search-regex-strings.php:67
msgid "{{link}}Caching software{{/link}}, in particular Cloudflare, can cache the wrong thing. Try clearing all your caches."
msgstr "{{link}}Caching software{{/link}}, en zeker Cloudflare, kunnen het verkeerde cachen. Probeer alle cache te verwijderen."

#: search-regex-strings.php:66
msgid "What do I do next?"
msgstr "Wat moet ik nu doen?"

#: search-regex-strings.php:237 search-regex-strings.php:238
#: search-regex-strings.php:239
msgid "Something went wrong 🙁"
msgstr "Er is iets fout gegaan 🙁"

#: search-regex-strings.php:243
msgid "Log out, clear your browser cache, and log in again - your browser has cached an old session."
msgstr "Log uit, wis de browsercache en log opnieuw in - je browser heeft een oude sessie in de cache opgeslagen."

#: search-regex-strings.php:242
msgid "Reload the page - your current session is old."
msgstr "Herlaad de pagina - je huidige sessie is oud."

#: search-regex-strings.php:65
msgid "Include these details in your report along with a description of what you were doing and a screenshot."
msgstr "Voeg deze gegevens toe aan je melding, samen met een beschrijving van wat je deed en een schermafbeelding."

#: search-regex-strings.php:64 search-regex-strings.php:97
#: source/core/comment.php:23
msgid "Email"
msgstr "E-mail"

#: search-regex-strings.php:63 search-regex-strings.php:96
msgid "Create An Issue"
msgstr "Meld een probleem"

#: search-regex-strings.php:6
msgid "Close"
msgstr "Sluiten"

#: search-regex-strings.php:5 search-regex-strings.php:16
#: search-regex-strings.php:140
msgid "Save"
msgstr "Opslaan"

#: search-regex-strings.php:4
msgid "Editing %s"
msgstr "Aan het bewerken %s"

#: search-regex-strings.php:232
msgid "Unable to make request due to browser security. This is typically because your WordPress and Site URL settings are inconsistent, or the request was blocked by your site CORS policy."
msgstr "Niet mogelijk om een verzoek te doen vanwege browser beveiliging. Dit gebeurt meestal omdat WordPress en de site URL niet overeenkomen, of het verzoek wordt geblokkeerd door het CORS beleid van je site."

#: search-regex-strings.php:231
msgid "Possible cause"
msgstr "Mogelijke oorzaak"

#: search-regex-strings.php:227
msgid "This could be a security plugin, or your server is out of memory or has an external error. Please check your server error log"
msgstr "Dit kan een beveiliging plugin zijn. Of je server heeft geen geheugen meer of heeft een externe fout. Bekijk de error log op je server"

#: search-regex-strings.php:225
msgid "Your server has rejected the request for being too big. You will need to reconfigure it to continue."
msgstr "Je server heeft de aanvraag geweigerd omdat het te groot is. Je moet het aanpassen om door te gaan."

#: search-regex-strings.php:216
msgid "Your REST API is probably being blocked by a security plugin. Please disable this, or configure it to allow REST API requests."
msgstr "Je REST API wordt waarschijnlijk geblokkeerd door een beveiliging plugin. Zet de plugin uit, of configureer hem zodat hij REST API verzoeken toestaat."

#: search-regex-strings.php:217 search-regex-strings.php:223
#: search-regex-strings.php:228 search-regex-strings.php:233
msgid "Read this REST API guide for more information."
msgstr "Lees deze REST API gids voor meer informatie."

#: search-regex-strings.php:215
msgid "WordPress did not return a response. This could mean an error occurred or that the request was blocked. Please check your server error_log."
msgstr "WordPress heeft geen reactie gegeven. Dit kan betekenen dat er een fout is opgetreden of dat het verzoek werd geblokkeerd. Bekijk je server foutlog."

#. Author of the plugin
msgid "John Godley"
msgstr "John Godley"

#. Description of the plugin
msgid "Adds search and replace functionality across posts, pages, comments, and meta-data, with full regular expression support"
msgstr "Voeg zoek en vervang functionaliteit toe in berichten, pagina's, reacties en meta-data, met volledige ondersteuning van reguliere expressies."

#. Plugin URI of the plugin
msgid "https://searchregex.com/"
msgstr "https://searchregex.com/"

#. Plugin Name of the plugin
#: search-regex-strings.php:70
msgid "Search Regex"
msgstr "Search Regex"