# Copyright (C) 2022 <PERSON>
# This file is distributed under the same license as the Search Regex plugin.
msgid ""
msgstr ""
"Project-Id-Version: Search Regex 3.0-beta-3\n"
"Report-Msgid-Bugs-To: https://wordpress.org/support/plugin/search-regex\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"POT-Creation-Date: 2022-07-08T09:19:56+00:00\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"X-Generator: WP-CLI 2.6.0\n"

#. Plugin Name of the plugin
#: build/search-regex.js:11954
#: src/page/home/<USER>
msgid "Search Regex"
msgstr ""

#. Plugin URI of the plugin
msgid "https://searchregex.com/"
msgstr ""

#. Description of the plugin
msgid "Adds search and replace functionality across posts, pages, comments, and meta-data, with full regular expression support"
msgstr ""

#. Author of the plugin
msgid "<PERSON>ley"
msgstr ""

#: includes/search-regex-admin.php:72
msgid "Settings"
msgstr ""

#: includes/search-regex-admin.php:294
#: build/search-regex.js:15309
#: src/page/support/search-help.js:37
msgid "{{link}}Source Flags{{/link}} - additional options for the selected source. For example, include post {{guid}}GUID{{/guid}} in the search."
msgstr ""

#. translators: URL
#: includes/search-regex-admin.php:302
msgid "You can find full documentation about using Search Regex on the <a href=\"%s\" target=\"_blank\">searchregex.com</a> support site."
msgstr ""

#: includes/search-regex-admin.php:303
#: build/search-regex.js:15291
#: src/page/support/search-help.js:19
msgid "The following concepts are used by Search Regex:"
msgstr ""

#: includes/search-regex-admin.php:305
#: build/search-regex.js:15291
#: src/page/support/search-help.js:22
msgid "{{link}}Search Flags{{/link}} - additional qualifiers for your search, to enable case insensitivity, and to enable regular expression support."
msgstr ""

#: includes/search-regex-admin.php:306
#: build/search-regex.js:15297
#: src/page/support/search-help.js:27
msgid "{{link}}Regular expression{{/link}} - a way of defining a pattern for text matching. Provides more advanced matches."
msgstr ""

#: includes/search-regex-admin.php:307
#: build/search-regex.js:15303
#: src/page/support/search-help.js:32
msgid "{{link}}Source{{/link}} - the source of data you wish to search. For example, posts, pages, or comments."
msgstr ""

#: includes/search-regex-admin.php:311
msgid "Search Regex Support"
msgstr ""

#. translators: 1: Expected WordPress version, 2: Actual WordPress version
#: includes/search-regex-admin.php:410
msgid "Search Regex requires WordPress v%1$1s, you are using v%2$2s - please update your WordPress"
msgstr ""

#: includes/search-regex-admin.php:413
msgid "Unable to load Search Regex"
msgstr ""

#: includes/search-regex-admin.php:428
msgid "Unable to load Search Regex ☹️"
msgstr ""

#: includes/search-regex-admin.php:429
msgid "This may be caused by another plugin - look at your browser's error console for more details."
msgstr ""

#: includes/search-regex-admin.php:430
#: build/search-regex.js:11733
#: src/page/home/<USER>
msgid "If you are using a page caching plugin or service (CloudFlare, OVH, etc) then you can also try clearing that cache."
msgstr ""

#: includes/search-regex-admin.php:431
msgid "Also check if your browser is able to load <code>search-regex.js</code>:"
msgstr ""

#: includes/search-regex-admin.php:433
msgid "Please note that Search Regex requires the WordPress REST API to be enabled. If you have disabled this then you won't be able to use Search Regex"
msgstr ""

#: includes/search-regex-admin.php:434
msgid "Please see the <a href=\"https://searchregex.com/support/problems/\">list of common problems</a>."
msgstr ""

#: includes/search-regex-admin.php:435
msgid "If you think Search Regex is at fault then create an issue."
msgstr ""

#: includes/search-regex-admin.php:436
msgid "<code>SearchRegexi10n</code> is not defined. This usually means another plugin is blocking Search Regex from loading. Please disable all plugins and try again."
msgstr ""

#: includes/search-regex-admin.php:439
msgid "Create Issue"
msgstr ""

#: includes/search-regex-admin.php:456
msgid "Loading, please wait..."
msgstr ""

#: includes/search-regex-admin.php:460
msgid "Please enable JavaScript"
msgstr ""

#: includes/source/class-manager.php:23
#: includes/source/core/source-post.php:232
msgid "Posts (core & custom)"
msgstr ""

#: includes/source/class-manager.php:29
#: includes/source/core/source-comment.php:142
msgid "Comments"
msgstr ""

#: includes/source/class-manager.php:35
#: includes/source/core/source-user.php:121
msgid "Users"
msgstr ""

#: includes/source/class-manager.php:41
msgid "WordPress Options"
msgstr ""

#: includes/source/class-manager.php:59
#: includes/source/core/source-post-meta.php:25
#: includes/source/core/source-post.php:413
msgid "Post Meta"
msgstr ""

#: includes/source/class-manager.php:65
#: includes/source/core/source-comment-meta.php:23
msgid "Comment Meta"
msgstr ""

#: includes/source/class-manager.php:71
#: includes/source/core/source-user-meta.php:30
msgid "User Meta"
msgstr ""

#: includes/source/class-manager.php:77
#: includes/source/core/source-terms.php:110
msgid "Terms"
msgstr ""

#: includes/source/class-manager.php:152
msgid "Standard"
msgstr ""

#: includes/source/class-manager.php:159
msgid "Advanced"
msgstr ""

#: includes/source/class-manager.php:166
msgid "Plugins"
msgstr ""

#: includes/source/core/source-comment.php:148
#: includes/source/core/source-options.php:106
#: includes/source/core/source-post.php:238
#: includes/source/core/source-terms.php:116
#: includes/source/core/source-user.php:127
#: includes/source/plugin/source-redirection.php:135
#: includes/source/plugin/source-redirection.php:412
msgid "ID"
msgstr ""

#: includes/source/core/source-comment.php:154
msgid "Post ID"
msgstr ""

#: includes/source/core/source-comment.php:161
#: includes/source/core/source-post.php:275
msgid "Author"
msgstr ""

#: includes/source/core/source-comment.php:168
#: includes/source/core/source-user.php:155
#: build/search-regex.js:11788
#: build/search-regex.js:11839
#: build/search-regex.js:12686
#: src/page/home/<USER>
#: src/page/home/<USER>
#: src/page/preset-management/index.js:38
msgid "Email"
msgstr ""

#: includes/source/core/source-comment.php:175
#: includes/source/core/source-user.php:161
msgid "URL"
msgstr ""

#: includes/source/core/source-comment.php:181
#: includes/source/core/source-post.php:267
msgid "Content"
msgstr ""

#: includes/source/core/source-comment.php:188
msgid "IP"
msgstr ""

#: includes/source/core/source-comment.php:193
#: includes/source/core/source-post.php:282
msgid "Date"
msgstr ""

#: includes/source/core/source-comment.php:199
#: includes/source/core/source-post.php:288
msgid "Date GMT"
msgstr ""

#: includes/source/core/source-comment.php:208
msgid "Unapproved"
msgstr ""

#: includes/source/core/source-comment.php:212
msgid "Approved"
msgstr ""

#: includes/source/core/source-comment.php:216
msgid "Spam"
msgstr ""

#: includes/source/core/source-comment.php:219
msgid "Approval Status"
msgstr ""

#: includes/source/core/source-comment.php:224
msgid "User Agent"
msgstr ""

#: includes/source/core/source-comment.php:229
msgid "Type"
msgstr ""

#: includes/source/core/source-comment.php:233
msgid "Pingback"
msgstr ""

#: includes/source/core/source-comment.php:237
msgid "Trackback"
msgstr ""

#: includes/source/core/source-comment.php:241
msgid "Comment"
msgstr ""

#: includes/source/core/source-comment.php:248
msgid "Parent Comment"
msgstr ""

#: includes/source/core/source-comment.php:254
msgid "User ID"
msgstr ""

#: includes/source/core/source-comment.php:262
msgid "Comment meta"
msgstr ""

#: includes/source/core/source-meta.php:113
msgid "Owner ID"
msgstr ""

#: includes/source/core/source-meta.php:120
#: build/search-regex.js:9214
#: build/search-regex.js:9998
#: build/search-regex.js:10614
#: src/component/schema/filter/types/keyvalue.js:30
#: src/component/schema/modify/types/keyvalue.js:36
#: src/component/schema/replace/types/keyvalue.js:32
msgid "Meta Key"
msgstr ""

#: includes/source/core/source-meta.php:127
#: build/search-regex.js:9240
#: build/search-regex.js:10008
#: build/search-regex.js:10634
#: src/component/schema/filter/types/keyvalue.js:56
#: src/component/schema/modify/types/keyvalue.js:51
#: src/component/schema/replace/types/keyvalue.js:60
msgid "Meta Value"
msgstr ""

#: includes/source/core/source-options.php:100
#: build/search-regex.js:11955
#: build/search-regex.js:11967
#: src/page/home/<USER>
#: src/page/home/<USER>
msgid "Options"
msgstr ""

#: includes/source/core/source-options.php:112
#: includes/source/core/source-terms.php:123
#: includes/source/plugin/source-redirection.php:418
#: build/search-regex.js:12709
#: src/page/preset-management/index.js:76
msgid "Name"
msgstr ""

#: includes/source/core/source-options.php:119
msgid "Value"
msgstr ""

#: includes/source/core/source-options.php:130
msgid "Is autoload"
msgstr ""

#: includes/source/core/source-options.php:134
msgid "Is not autoload"
msgstr ""

#: includes/source/core/source-options.php:137
msgid "Autoload"
msgstr ""

#: includes/source/core/source-post.php:244
#: includes/source/plugin/source-redirection.php:191
#: build/search-regex.js:13100
#: src/page/preset-management/preset-edit.js:185
msgid "Title"
msgstr ""

#: includes/source/core/source-post.php:252
#: includes/source/core/source-terms.php:130
msgid "Slug"
msgstr ""

#: includes/source/core/source-post.php:261
msgid "Post Type"
msgstr ""

#: includes/source/core/source-post.php:294
msgid "Excerpt"
msgstr ""

#: includes/source/core/source-post.php:301
msgid "Post Status"
msgstr ""

#: includes/source/core/source-post.php:309
#: includes/source/core/source-post.php:325
msgid "Open"
msgstr ""

#: includes/source/core/source-post.php:313
#: includes/source/core/source-post.php:329
msgid "Closed"
msgstr ""

#: includes/source/core/source-post.php:316
msgid "Comment Status"
msgstr ""

#: includes/source/core/source-post.php:332
msgid "Ping Status"
msgstr ""

#: includes/source/core/source-post.php:341
msgid "Has password"
msgstr ""

#: includes/source/core/source-post.php:345
msgid "Has no password"
msgstr ""

#: includes/source/core/source-post.php:348
msgid "Password"
msgstr ""

#: includes/source/core/source-post.php:355
msgid "Modified"
msgstr ""

#: includes/source/core/source-post.php:361
msgid "Modified GMT"
msgstr ""

#: includes/source/core/source-post.php:367
msgid "Parent"
msgstr ""

#: includes/source/core/source-post.php:375
msgid "GUID"
msgstr ""

#: includes/source/core/source-post.php:382
msgid "MIME"
msgstr ""

#: includes/source/core/source-post.php:389
msgid "Comment Count"
msgstr ""

#: includes/source/core/source-post.php:395
msgid "Post Category"
msgstr ""

#: includes/source/core/source-post.php:404
msgid "Post Tag"
msgstr ""

#: includes/source/core/source-terms.php:137
msgid "Taxonomy"
msgstr ""

#: includes/source/core/source-user.php:134
msgid "Login"
msgstr ""

#: includes/source/core/source-user.php:141
msgid "Nicename"
msgstr ""

#: includes/source/core/source-user.php:148
msgid "Display Name"
msgstr ""

#: includes/source/core/source-user.php:168
msgid "Registered"
msgstr ""

#: includes/source/core/source-user.php:174
msgid "User meta"
msgstr ""

#: includes/source/plugin/source-redirection.php:129
#: includes/source/plugin/source-redirection.php:466
#: build/search-regex.js:15212
#: src/page/support/help.js:74
msgid "Redirection"
msgstr ""

#: includes/source/plugin/source-redirection.php:141
msgid "Source URL"
msgstr ""

#: includes/source/plugin/source-redirection.php:148
msgid "Hit Count"
msgstr ""

#: includes/source/plugin/source-redirection.php:153
msgid "Last Access"
msgstr ""

#: includes/source/plugin/source-redirection.php:158
msgid "Group"
msgstr ""

#: includes/source/plugin/source-redirection.php:165
#: includes/source/plugin/source-redirection.php:444
msgid "Status"
msgstr ""

#: includes/source/plugin/source-redirection.php:169
#: includes/source/plugin/source-redirection.php:448
msgid "Enabled"
msgstr ""

#: includes/source/plugin/source-redirection.php:173
#: includes/source/plugin/source-redirection.php:452
msgid "Disabled"
msgstr ""

#: includes/source/plugin/source-redirection.php:180
msgid "Position"
msgstr ""

#: includes/source/plugin/source-redirection.php:185
msgid "HTTP Code"
msgstr ""

#: includes/source/plugin/source-redirection.php:209
msgid "Target"
msgstr ""

#: includes/source/plugin/source-redirection.php:299
msgid "Not accessed"
msgstr ""

#: includes/source/plugin/source-redirection.php:406
#: includes/source/plugin/source-redirection.php:472
msgid "Redirection Groups"
msgstr ""

#: includes/source/plugin/source-redirection.php:425
msgid "Module"
msgstr ""

#: includes/source/plugin/source-redirection.php:429
msgid "WordPress"
msgstr ""

#: includes/source/plugin/source-redirection.php:433
msgid "Apache"
msgstr ""

#: includes/source/plugin/source-redirection.php:437
msgid "Nginx"
msgstr ""

#. translators: 1: server PHP version. 2: required PHP version.
#: search-regex.php:38
msgid "Disabled! Detected PHP %1$s, need PHP %2$s+"
msgstr ""

#: build/search-regex.js:6291
#: src/component/highlight-matches/replacement.js:71
msgid "Click to replace match"
msgstr ""

#: build/search-regex.js:6473
#: src/component/presets/index.js:47
msgid "Saving preset"
msgstr ""

#: build/search-regex.js:6477
#: src/component/presets/index.js:51
msgid "No preset"
msgstr ""

#: build/search-regex.js:6514
#: src/component/presets/index.js:84
msgid "Save search as new preset"
msgstr ""

#: build/search-regex.js:6521
#: src/component/presets/index.js:92
msgid "Update current preset"
msgstr ""

#: build/search-regex.js:6529
#: src/component/presets/index.js:101
msgid "Saving Preset"
msgstr ""

#: build/search-regex.js:6529
#: src/component/presets/index.js:102
msgid "Enter a name for your preset"
msgstr ""

#: build/search-regex.js:6537
#: src/component/presets/index.js:110
msgid "Enter preset name"
msgstr ""

#: build/search-regex.js:6541
#: build/search-regex.js:13124
#: src/component/presets/index.js:113
#: src/page/preset-management/preset-edit.js:240
msgid "Save"
msgstr ""

#: build/search-regex.js:6545
#: build/search-regex.js:6709
#: build/search-regex.js:13130
#: build/search-regex.js:14309
#: src/component/presets/index.js:121
#: src/component/replace-form/index.js:86
#: src/page/preset-management/preset-edit.js:245
#: src/page/search-replace/search-actions.js:82
msgid "Cancel"
msgstr ""

#: build/search-regex.js:6704
#: build/search-regex.js:9614
#: build/search-regex.js:10285
#: build/search-regex.js:13200
#: build/search-regex.js:13708
#: src/component/replace-form/index.js:80
#: src/component/schema/modify/operation.js:38
#: src/component/schema/modify/types/string.js:131
#: src/page/preset-management/preset-entry.js:52
#: src/page/search-replace/actions/index.js:184
#: src/page/search-replace/actions/replace.js:34
msgid "Replace"
msgstr ""

#: build/search-regex.js:6777
#: src/component/replace-progress/index.js:29
msgid "%s row deleted."
msgstr ""

#: build/search-regex.js:6780
#: src/component/replace-progress/index.js:32
msgid "%s row."
msgstr ""

#: build/search-regex.js:6827
#: src/component/replace-progress/index.js:61
msgid "Progress"
msgstr ""

#: build/search-regex.js:6847
#: src/component/replace-progress/index.js:76
msgid "Finished!"
msgstr ""

#: build/search-regex.js:6876
#: build/search-regex.js:15104
#: src/component/replace-progress/index.js:108
#: src/page/search-replace/search-results/index.js:152
msgid "Your search resulted in too many requests. Please narrow your search terms."
msgstr ""

#: build/search-regex.js:6951
#: src/component/replace/index.js:46
msgid "Matched values will be removed"
msgstr ""

#: build/search-regex.js:6951
#: src/component/replace/index.js:46
msgid "Enter replacement value"
msgstr ""

#: build/search-regex.js:6976
#: src/component/replace/index.js:68
msgid "Single"
msgstr ""

#: build/search-regex.js:6979
#: build/search-regex.js:16942
#: src/component/replace/index.js:69
#: src/state/search/selector.js:38
msgid "Multi"
msgstr ""

#: build/search-regex.js:6982
#: build/search-regex.js:9627
#: build/search-regex.js:9637
#: src/component/replace/index.js:70
#: src/component/schema/modify/operation.js:55
#: src/component/schema/modify/operation.js:68
msgid "Remove"
msgstr ""

#: build/search-regex.js:7135
#: src/component/rest-api-status/api-result-pass.js:15
msgid "Working!"
msgstr ""

#: build/search-regex.js:7183
#: src/component/rest-api-status/api-result-raw.js:27
msgid "Show Full"
msgstr ""

#: build/search-regex.js:7187
#: src/component/rest-api-status/api-result-raw.js:32
msgid "Hide"
msgstr ""

#: build/search-regex.js:7256
#: src/component/rest-api-status/api-result.js:30
msgid "Switch to this API"
msgstr ""

#: build/search-regex.js:7257
#: src/component/rest-api-status/api-result.js:31
msgid "Current API"
msgstr ""

#: build/search-regex.js:7411
#: src/component/rest-api-status/index.js:100
msgid "Good"
msgstr ""

#: build/search-regex.js:7413
#: src/component/rest-api-status/index.js:102
msgid "Working but some issues"
msgstr ""

#: build/search-regex.js:7415
#: src/component/rest-api-status/index.js:104
msgid "Not working but fixable"
msgstr ""

#: build/search-regex.js:7418
#: src/component/rest-api-status/index.js:107
msgid "Unavailable"
msgstr ""

#: build/search-regex.js:7431
#: src/component/rest-api-status/index.js:122
msgid "There are some problems connecting to your REST API. It is not necessary to fix these problems and the plugin is able to work."
msgstr ""

#: build/search-regex.js:7434
#: src/component/rest-api-status/index.js:125
msgid "Your REST API is not working and the plugin will not be able to continue until this is fixed."
msgstr ""

#: build/search-regex.js:7436
#: src/component/rest-api-status/index.js:127
msgid "You are using a broken REST API route. Changing to a working API should fix the problem."
msgstr ""

#: build/search-regex.js:7441
#: src/component/rest-api-status/index.js:133
msgid "Summary"
msgstr ""

#: build/search-regex.js:7445
#: src/component/rest-api-status/index.js:139
msgid "Show Problems"
msgstr ""

#: build/search-regex.js:7476
#: src/component/rest-api-status/index.js:168
msgid "Testing - %s%%"
msgstr ""

#: build/search-regex.js:7481
#: src/component/rest-api-status/index.js:175
msgid "Check Again"
msgstr ""

#: build/search-regex.js:7571
#: build/search-regex.js:13436
#: src/component/result/actions.js:25
#: src/page/preset-management/preset.js:98
msgid "Edit"
msgstr ""

#: build/search-regex.js:7572
#: src/component/result/actions.js:26
msgid "View"
msgstr ""

#: build/search-regex.js:7595
#: src/component/result/actions.js:47
msgid "Delete database row"
msgstr ""

#: build/search-regex.js:7672
#: src/component/result/column-label.js:49
msgid "This column contains special formatting. Modifying it could break the format."
msgstr ""

#: build/search-regex.js:7672
#: src/component/result/column-label.js:50
msgid "Click to replace column"
msgstr ""

#: build/search-regex.js:7776
#: src/component/result/column/context-item.js:73
msgid "Contains encoded data"
msgstr ""

#: build/search-regex.js:7984
#: src/component/result/column/index.js:96
msgid "Show %s more"
msgstr ""

#: build/search-regex.js:8018
#: src/component/result/column/restricted-matches.js:11
msgid "Maximum number of matches exceeded and hidden from view. These will be included in any replacements."
msgstr ""

#: build/search-regex.js:8059
#: src/component/result/context-type/index.js:21
msgid "%(label)s (ID %(id)d)"
msgstr ""

#: build/search-regex.js:8096
#: build/search-regex.js:8112
#: src/component/result/context-type/index.js:54
#: src/component/result/context-type/index.js:83
msgid "No value"
msgstr ""

#: build/search-regex.js:8104
#: src/component/result/context-type/index.js:68
msgid "Empty value"
msgstr ""

#: build/search-regex.js:8576
#: src/component/result/result-title.js:22
msgid "No title"
msgstr ""

#: build/search-regex.js:8729
#: src/component/schema/filter/index.js:113
msgid "OR"
msgstr ""

#: build/search-regex.js:8734
#: src/component/schema/filter/index.js:120
msgid "Add sub-filter (OR)"
msgstr ""

#: build/search-regex.js:8779
#: build/search-regex.js:8816
#: src/component/schema/filter/logic.js:22
#: src/component/schema/filter/logic.js:66
msgid "Equals"
msgstr ""

#: build/search-regex.js:8782
#: build/search-regex.js:8819
#: src/component/schema/filter/logic.js:26
#: src/component/schema/filter/logic.js:70
msgid "Not Equals"
msgstr ""

#: build/search-regex.js:8785
#: src/component/schema/filter/logic.js:30
msgid "Greater"
msgstr ""

#: build/search-regex.js:8788
#: src/component/schema/filter/logic.js:34
msgid "Less"
msgstr ""

#: build/search-regex.js:8791
#: src/component/schema/filter/logic.js:38
msgid "Range"
msgstr ""

#: build/search-regex.js:8802
#: src/component/schema/filter/logic.js:50
msgid "Has Owner"
msgstr ""

#: build/search-regex.js:8805
#: src/component/schema/filter/logic.js:54
msgid "No Owner"
msgstr ""

#: build/search-regex.js:8822
#: src/component/schema/filter/logic.js:74
msgid "Contains"
msgstr ""

#: build/search-regex.js:8825
#: src/component/schema/filter/logic.js:78
msgid "Not contains"
msgstr ""

#: build/search-regex.js:8828
#: src/component/schema/filter/logic.js:82
msgid "Begins"
msgstr ""

#: build/search-regex.js:8831
#: src/component/schema/filter/logic.js:86
msgid "End"
msgstr ""

#: build/search-regex.js:8842
#: src/component/schema/filter/logic.js:98
msgid "Includes any"
msgstr ""

#: build/search-regex.js:8845
#: src/component/schema/filter/logic.js:102
msgid "Excludes any"
msgstr ""

#: build/search-regex.js:8856
#: src/component/schema/filter/logic.js:114
msgid "Any"
msgstr ""

#: build/search-regex.js:8971
#: build/search-regex.js:9139
#: src/component/schema/filter/types/date.js:42
#: src/component/schema/filter/types/integer.js:52
msgid "between {{first/}} and {{second/}}"
msgstr ""

#: build/search-regex.js:9328
#: src/component/schema/filter/types/member.js:38
msgid "All"
msgstr ""

#: build/search-regex.js:9598
#: build/search-regex.js:9611
#: src/component/schema/modify/operation.js:17
#: src/component/schema/modify/operation.js:34
msgid "Set Value"
msgstr ""

#: build/search-regex.js:9601
#: src/component/schema/modify/operation.js:21
msgid "Increment"
msgstr ""

#: build/search-regex.js:9604
#: src/component/schema/modify/operation.js:25
msgid "Decrement"
msgstr ""

#: build/search-regex.js:9621
#: src/component/schema/modify/operation.js:47
msgid "Replace With"
msgstr ""

#: build/search-regex.js:9624
#: build/search-regex.js:9634
#: build/search-regex.js:10625
#: build/search-regex.js:13797
#: build/search-regex.js:14683
#: src/component/schema/modify/operation.js:51
#: src/component/schema/modify/operation.js:64
#: src/component/schema/replace/types/keyvalue.js:47
#: src/page/search-replace/actions/modify-columns.js:63
#: src/page/search-replace/search-form/form.js:237
msgid "Add"
msgstr ""

#: build/search-regex.js:9752
#: src/component/schema/modify/types/date.js:57
msgid "Seconds"
msgstr ""

#: build/search-regex.js:9755
#: src/component/schema/modify/types/date.js:61
msgid "Minutes"
msgstr ""

#: build/search-regex.js:9758
#: src/component/schema/modify/types/date.js:65
msgid "Hours"
msgstr ""

#: build/search-regex.js:9761
#: src/component/schema/modify/types/date.js:69
msgid "Days"
msgstr ""

#: build/search-regex.js:9764
#: src/component/schema/modify/types/date.js:73
msgid "Weeks"
msgstr ""

#: build/search-regex.js:9767
#: src/component/schema/modify/types/date.js:77
msgid "Months"
msgstr ""

#: build/search-regex.js:9770
#: src/component/schema/modify/types/date.js:81
msgid "Year"
msgstr ""

#: build/search-regex.js:9998
#: build/search-regex.js:10008
#: build/search-regex.js:10285
#: build/search-regex.js:12711
#: build/search-regex.js:13197
#: build/search-regex.js:14302
#: build/search-regex.js:14691
#: src/component/schema/modify/types/keyvalue.js:38
#: src/component/schema/modify/types/keyvalue.js:53
#: src/component/schema/modify/types/string.js:125
#: src/page/preset-management/index.js:77
#: src/page/preset-management/preset-entry.js:37
#: src/page/search-replace/search-actions.js:65
#: src/page/search-replace/search-form/form.js:258
msgid "Search"
msgstr ""

#: build/search-regex.js:10281
#: src/component/schema/modify/types/string.js:117
msgid "Replace \"%1s\""
msgstr ""

#: build/search-regex.js:10618
#: src/component/schema/replace/types/keyvalue.js:38
msgid "New meta key"
msgstr ""

#: build/search-regex.js:10627
#: build/search-regex.js:13439
#: src/component/schema/replace/types/keyvalue.js:48
#: src/page/preset-management/preset.js:102
msgid "Delete"
msgstr ""

#: build/search-regex.js:10642
#: build/search-regex.js:10650
#: src/component/schema/replace/types/keyvalue.js:74
#: src/component/schema/replace/types/keyvalue.js:87
msgid "New meta value"
msgstr ""

#: build/search-regex.js:10959
#: src/component/schema/replace/types/string.js:97
msgid "Enter replacement"
msgstr ""

#: build/search-regex.js:10972
#: src/component/schema/replace/types/string.js:117
msgid "Apply to matches only"
msgstr ""

#: build/search-regex.js:11029
#: build/search-regex.js:12713
#: src/component/search-flags/index.js:36
#: src/page/preset-management/index.js:78
msgid "Flags"
msgstr ""

#: build/search-regex.js:11099
#: src/component/search/index.js:40
msgid "Enter search phrase"
msgstr ""

#: build/search-regex.js:11109
#: src/component/search/index.js:52
msgid "Optional global search phrase. Leave blank to use filters only."
msgstr ""

#: build/search-regex.js:11274
#: src/component/value-type/index.js:9
msgid "Serialized PHP"
msgstr ""

#: build/search-regex.js:11278
#: build/search-regex.js:13491
#: src/component/value-type/index.js:13
#: src/page/search-replace/actions/constants.js:11
msgid "JSON"
msgstr ""

#: build/search-regex.js:11282
#: src/component/value-type/index.js:17
msgid "Blocks"
msgstr ""

#: build/search-regex.js:11286
#: src/component/value-type/index.js:21
msgid "HTML"
msgstr ""

#: build/search-regex.js:11687
#: src/page/home/<USER>
msgid "Cached Search Regex detected"
msgstr ""

#: build/search-regex.js:11689
#: src/page/home/<USER>
msgid "Please clear your browser cache and reload this page."
msgstr ""

#: build/search-regex.js:11689
#: src/page/home/<USER>
msgid "If you are using a caching system such as Cloudflare then please read this: "
msgstr ""

#: build/search-regex.js:11691
#: src/page/home/<USER>
msgid "clearing your cache."
msgstr ""

#: build/search-regex.js:11733
#: src/page/home/<USER>
msgid "Search Regex is not working. Try clearing your browser cache and reloading this page."
msgstr ""

#: build/search-regex.js:11733
#: src/page/home/<USER>
msgid "If that doesn't help, open your browser's error console and create a {{link}}new issue{{/link}} with the details."
msgstr ""

#: build/search-regex.js:11782
#: src/page/home/<USER>
msgid "Query Problem"
msgstr ""

#: build/search-regex.js:11782
#: src/page/home/<USER>
msgid "A problem occurred with your last query. This is likely caused by a combination of search filters that haven't been handled properly."
msgstr ""

#: build/search-regex.js:11782
#: build/search-regex.js:11873
#: src/page/home/<USER>
#: src/page/home/<USER>
msgid "What do I do next?"
msgstr ""

#: build/search-regex.js:11785
#: build/search-regex.js:11836
#: build/search-regex.js:12683
#: src/page/home/<USER>
#: src/page/home/<USER>
#: src/page/preset-management/index.js:29
msgid "Create An Issue"
msgstr ""

#: build/search-regex.js:11823
#: src/page/home/<USER>
msgid "Please check the {{link}}support site{{/link}} before proceeding further."
msgstr ""

#: build/search-regex.js:11829
#: src/page/home/<USER>
msgid "If that did not help then {{strong}}create an issue{{/strong}} or send it in an {{strong}}email{{/strong}}."
msgstr ""

#: build/search-regex.js:11839
#: src/page/home/<USER>
msgid "Include these details in your report along with a description of what you were doing and a screenshot."
msgstr ""

#: build/search-regex.js:11873
#: src/page/home/<USER>
msgid "{{link}}Caching software{{/link}}, in particular Cloudflare, can cache the wrong thing. Try clearing all your caches."
msgstr ""

#: build/search-regex.js:11879
#: src/page/home/<USER>
msgid "{{link}}Please temporarily disable other plugins!{{/link}} This fixes so many problems."
msgstr ""

#: build/search-regex.js:11885
#: src/page/home/<USER>
msgid "If you are using WordPress 5.2 or newer then look at your {{link}}Site Health{{/link}} and resolve any issues."
msgstr ""

#: build/search-regex.js:11956
#: build/search-regex.js:11970
#: src/page/home/<USER>
#: src/page/home/<USER>
msgid "Support"
msgstr ""

#: build/search-regex.js:11957
#: build/search-regex.js:11964
#: src/page/home/<USER>
#: src/page/home/<USER>
msgid "Presets"
msgstr ""

#: build/search-regex.js:11961
#: src/page/home/<USER>
msgid "Search & Replace"
msgstr ""

#: build/search-regex.js:12217
#: src/page/home/<USER>
msgid "Version %s installed! Please read the {{url}}release notes{{/url}} for details."
msgstr ""

#: build/search-regex.js:12226
#: src/page/home/<USER>
msgid "OK"
msgstr ""

#: build/search-regex.js:12326
#: src/page/options/donation.js:82
msgid "You've supported this plugin - thank you!"
msgstr ""

#: build/search-regex.js:12329
#: src/page/options/donation.js:83
msgid "I'd like to support some more."
msgstr ""

#: build/search-regex.js:12341
#: src/page/options/donation.js:98
msgid "Search Regex is free to use - life is wonderful and lovely! It has required a great deal of time and effort to develop and you can help support this development by {{strong}}making a small donation{{/strong}}."
msgstr ""

#: build/search-regex.js:12345
#: src/page/options/donation.js:103
msgid "You get useful software and I get to carry on making it better."
msgstr ""

#: build/search-regex.js:12413
#: src/page/options/donation.js:125
msgid "Support 💰"
msgstr ""

#: build/search-regex.js:12428
#: src/page/options/donation.js:137
msgid "Plugin Support"
msgstr ""

#: build/search-regex.js:12535
#: src/page/options/options-form.js:19
msgid "Default REST API"
msgstr ""

#: build/search-regex.js:12538
#: src/page/options/options-form.js:20
msgid "Raw REST API"
msgstr ""

#: build/search-regex.js:12541
#: src/page/options/options-form.js:21
msgid "Relative REST API"
msgstr ""

#: build/search-regex.js:12577
#: src/page/options/options-form.js:55
msgid "I'm a nice person and I have helped support the author of this plugin"
msgstr ""

#: build/search-regex.js:12578
#: src/page/options/options-form.js:60
msgid "Default Preset"
msgstr ""

#: build/search-regex.js:12582
#: src/page/options/options-form.js:63
msgid "No default preset"
msgstr ""

#: build/search-regex.js:12590
#: src/page/options/options-form.js:70
msgid "Set a preset to use by default when Search Regex is loaded."
msgstr ""

#: build/search-regex.js:12591
#: src/page/options/options-form.js:74
msgid "REST API"
msgstr ""

#: build/search-regex.js:12599
#: src/page/options/options-form.js:83
msgid "How Search Regex uses the REST API - don't change unless necessary"
msgstr ""

#: build/search-regex.js:12603
#: src/page/options/options-form.js:92
msgid "Update"
msgstr ""

#: build/search-regex.js:12718
#: src/page/preset-management/index.js:89
msgid "There are no presets"
msgstr ""

#: build/search-regex.js:12720
#: src/page/preset-management/index.js:96
msgid "Download presets!"
msgstr ""

#: build/search-regex.js:12725
#: src/page/preset-management/index.js:102
msgid "Export JSON"
msgstr ""

#: build/search-regex.js:12725
#: src/page/preset-management/index.js:107
msgid "Import JSON"
msgstr ""

#: build/search-regex.js:12731
#: src/page/preset-management/index.js:116
msgid "Import a JSON file"
msgstr ""

#: build/search-regex.js:12731
#: src/page/preset-management/index.js:117
msgid "Click 'Add File' or drag and drop here."
msgstr ""

#: build/search-regex.js:12732
#: src/page/preset-management/index.js:122
msgid "File selected"
msgstr ""

#: build/search-regex.js:12733
#: src/page/preset-management/index.js:130
msgid "Importing"
msgstr ""

#: build/search-regex.js:12734
#: src/page/preset-management/index.js:142
msgid "Uploaded %(total)d preset"
msgstr ""

#: build/search-regex.js:12742
#: src/page/preset-management/index.js:151
msgid "Done"
msgstr ""

#: build/search-regex.js:12744
#: src/page/preset-management/index.js:158
msgid "Import preset from clipboard"
msgstr ""

#: build/search-regex.js:12747
#: src/page/preset-management/index.js:164
msgid "Unable to import preset"
msgstr ""

#: build/search-regex.js:12753
#: src/page/preset-management/index.js:171
msgid "Please check your JSON data is a valid preset. You may have copied it incorrectly, or pasted something that is not a preset."
msgstr ""

#: build/search-regex.js:12754
#: src/page/preset-management/index.js:178
msgid "Paste preset JSON."
msgstr ""

#: build/search-regex.js:12763
#: src/page/preset-management/index.js:190
msgid "Import"
msgstr ""

#: build/search-regex.js:12902
#: src/page/preset-management/phrase.js:69
msgid "no phrase"
msgstr ""

#: build/search-regex.js:12906
#: src/page/preset-management/phrase.js:73
msgid "remove phrase"
msgstr ""

#: build/search-regex.js:13000
#: src/page/preset-management/preset-edit.js:56
msgid "Global Search"
msgstr ""

#: build/search-regex.js:13003
#: src/page/preset-management/preset-edit.js:60
msgid "Global Replace"
msgstr ""

#: build/search-regex.js:13006
#: src/page/preset-management/preset-edit.js:64
msgid "Global Search Flags"
msgstr ""

#: build/search-regex.js:13009
#: build/search-regex.js:13658
#: build/search-regex.js:14665
#: build/search-regex.js:14672
#: build/search-regex.js:15050
#: src/page/preset-management/preset-edit.js:68
#: src/page/search-replace/actions/index.js:86
#: src/page/search-replace/search-form/form.js:207
#: src/page/search-replace/search-form/form.js:219
#: src/page/search-replace/search-results/index.js:89
msgid "Source"
msgstr ""

#: build/search-regex.js:13012
#: src/page/preset-management/preset-edit.js:72
msgid "Results per page"
msgstr ""

#: build/search-regex.js:13015
#: build/search-regex.js:13200
#: build/search-regex.js:14392
#: build/search-regex.js:14674
#: src/page/preset-management/preset-edit.js:76
#: src/page/preset-management/preset-entry.js:42
#: src/page/search-replace/search-form/filters.js:51
#: src/page/search-replace/search-form/form.js:227
msgid "Filters"
msgstr ""

#: build/search-regex.js:13018
#: build/search-regex.js:13200
#: build/search-regex.js:13648
#: src/page/preset-management/preset-edit.js:80
#: src/page/preset-management/preset-entry.js:47
#: src/page/search-replace/actions/index.js:73
msgid "Action"
msgstr ""

#: build/search-regex.js:13021
#: build/search-regex.js:14741
#: src/page/preset-management/preset-edit.js:84
#: src/page/search-replace/search-form/form.js:328
msgid "View Columns"
msgstr ""

#: build/search-regex.js:13063
#: src/page/preset-management/preset-edit.js:127
msgid "Edit preset"
msgstr ""

#: build/search-regex.js:13065
#: src/page/preset-management/preset-edit.js:131
msgid "Preset Name"
msgstr ""

#: build/search-regex.js:13069
#: src/page/preset-management/preset-edit.js:137
msgid "Give the preset a name"
msgstr ""

#: build/search-regex.js:13072
#: src/page/preset-management/preset-edit.js:142
msgid "Preset Description"
msgstr ""

#: build/search-regex.js:13076
#: src/page/preset-management/preset-edit.js:148
msgid "Describe the preset"
msgstr ""

#: build/search-regex.js:13087
#: src/page/preset-management/preset-edit.js:161
msgid "Advanced preset"
msgstr ""

#: build/search-regex.js:13089
#: src/page/preset-management/preset-edit.js:165
msgid "Locked Fields"
msgstr ""

#: build/search-regex.js:13094
#: src/page/preset-management/preset-edit.js:172
msgid "Fields"
msgstr ""

#: build/search-regex.js:13096
#: src/page/preset-management/preset-edit.js:176
msgid "Locking a field removes it from the search form and prevents changes."
msgstr ""

#: build/search-regex.js:13098
#: src/page/preset-management/preset-edit.js:180
msgid "Tags"
msgstr ""

#: build/search-regex.js:13102
#: src/page/preset-management/preset-edit.js:188
msgid "Enter tag title shown to user"
msgstr ""

#: build/search-regex.js:13107
#: src/page/preset-management/preset-edit.js:195
msgid "Tag"
msgstr ""

#: build/search-regex.js:13109
#: src/page/preset-management/preset-edit.js:198
msgid "Enter tag which is used in the field"
msgstr ""

#: build/search-regex.js:13117
#: src/page/preset-management/preset-edit.js:218
msgid "A tag creates a custom input field. Insert the tag anywhere in the search, replace, text filter, or text action and when the preset is used it will be replaced with a custom text field with the tag label."
msgstr ""

#: build/search-regex.js:13117
#: src/page/preset-management/preset-edit.js:223
msgid "For example, create tag {{code}}URL{{/code}} and title {{code}}Image URL{{/code}}. Your search could be {{code}}<img src=\"URL\">{{/code}}. When the preset is used it will ask the user for the {{code}}Image URL{{/code}} instead of the full search phrase."
msgstr ""

#: build/search-regex.js:13290
#: src/page/preset-management/preset-flags.js:52
msgid "Locked fields"
msgstr ""

#: build/search-regex.js:13400
#: src/page/preset-management/preset.js:65
msgid "Are you sure you want to delete this preset?"
msgstr ""

#: build/search-regex.js:13444
#: src/page/preset-management/preset.js:107
msgid "Copy to clipboard"
msgstr ""

#: build/search-regex.js:13494
#: src/page/search-replace/actions/constants.js:15
msgid "CSV"
msgstr ""

#: build/search-regex.js:13497
#: src/page/search-replace/actions/constants.js:19
msgid "SQL"
msgstr ""

#: build/search-regex.js:13508
#: src/page/search-replace/actions/constants.js:32
msgid "No action"
msgstr ""

#: build/search-regex.js:13510
#: src/page/search-replace/actions/constants.js:34
msgid "Just show matching results."
msgstr ""

#: build/search-regex.js:13512
#: src/page/search-replace/actions/constants.js:37
msgid "Global Text Replace"
msgstr ""

#: build/search-regex.js:13514
#: src/page/search-replace/actions/constants.js:39
msgid "Replace the global search values."
msgstr ""

#: build/search-regex.js:13516
#: src/page/search-replace/actions/constants.js:42
msgid "Modify Matches"
msgstr ""

#: build/search-regex.js:13518
#: src/page/search-replace/actions/constants.js:44
msgid "Perform changes to specific values of the matching results."
msgstr ""

#: build/search-regex.js:13520
#: build/search-regex.js:14266
#: src/page/search-replace/actions/constants.js:47
#: src/page/search-replace/search-actions.js:42
msgid "Export Matches"
msgstr ""

#: build/search-regex.js:13522
#: src/page/search-replace/actions/constants.js:49
msgid "Export matching results to JSON, CSV, or SQL."
msgstr ""

#: build/search-regex.js:13525
#: build/search-regex.js:14262
#: src/page/search-replace/actions/constants.js:53
#: src/page/search-replace/search-actions.js:38
msgid "Delete Matches"
msgstr ""

#: build/search-regex.js:13527
#: src/page/search-replace/actions/constants.js:55
msgid "Delete matching results."
msgstr ""

#: build/search-regex.js:13529
#: build/search-regex.js:14270
#: src/page/search-replace/actions/constants.js:58
#: src/page/search-replace/search-actions.js:46
msgid "Run Action"
msgstr ""

#: build/search-regex.js:13531
#: src/page/search-replace/actions/constants.js:60
msgid "Run a WordPress action for each matching result."
msgstr ""

#: build/search-regex.js:13669
#: src/page/search-replace/actions/index.js:106
msgid "Export Format"
msgstr ""

#: build/search-regex.js:13679
#: src/page/search-replace/actions/index.js:122
msgid "WordPress Action"
msgstr ""

#: build/search-regex.js:13699
#: src/page/search-replace/actions/index.js:155
msgid "Only include selected columns"
msgstr ""

#: build/search-regex.js:13787
#: src/page/search-replace/actions/modify-columns.js:53
msgid "Column"
msgstr ""

#: build/search-regex.js:13858
#: src/page/search-replace/index.js:34
msgid "Please backup your data before making modifications."
msgstr ""

#: build/search-regex.js:13858
#: src/page/search-replace/index.js:37
msgid "Search and replace information in your database."
msgstr ""

#: build/search-regex.js:13920
#: src/page/search-replace/pagination/advanced-pagination.js:28
msgid "%s database row in total"
msgstr ""

#: build/search-regex.js:13923
#: src/page/search-replace/pagination/advanced-pagination.js:33
msgid "matched rows = %(searched)s, phrases = %(found)s"
msgstr ""

#: build/search-regex.js:13931
#: build/search-regex.js:14165
#: src/page/search-replace/pagination/advanced-pagination.js:42
#: src/page/search-replace/pagination/simple-pagination.js:38
msgid "First page"
msgstr ""

#: build/search-regex.js:13937
#: build/search-regex.js:14171
#: src/page/search-replace/pagination/advanced-pagination.js:43
#: src/page/search-replace/pagination/simple-pagination.js:45
msgid "Prev page"
msgstr ""

#: build/search-regex.js:13944
#: src/page/search-replace/pagination/advanced-pagination.js:46
msgid "Progress %(current)s%%"
msgstr ""

#: build/search-regex.js:13949
#: build/search-regex.js:14184
#: src/page/search-replace/pagination/advanced-pagination.js:53
#: src/page/search-replace/pagination/simple-pagination.js:62
msgid "Next page"
msgstr ""

#: build/search-regex.js:14036
#: src/page/search-replace/pagination/index.js:24
msgid "Your search conditions have changed. Please refresh to see the latest results."
msgstr ""

#: build/search-regex.js:14157
#: src/page/search-replace/pagination/simple-pagination.js:27
msgid "Matched rows: %(matches)s out of %(total)s total."
msgstr ""

#: build/search-regex.js:14178
#: src/page/search-replace/pagination/simple-pagination.js:53
msgid "Page %(current)s of %(total)s"
msgstr ""

#: build/search-regex.js:14190
#: src/page/search-replace/pagination/simple-pagination.js:69
msgid "Last page"
msgstr ""

#: build/search-regex.js:14273
#: src/page/search-replace/search-actions.js:49
msgid "Replace All"
msgstr ""

#: build/search-regex.js:14302
#: src/page/search-replace/search-actions.js:65
msgid "Refresh"
msgstr ""

#: build/search-regex.js:14427
#: src/page/search-replace/search-form/filters.js:96
msgid "AND"
msgstr ""

#: build/search-regex.js:14673
#: src/page/search-replace/search-form/form.js:220
msgid "Sources"
msgstr ""

#: build/search-regex.js:14725
#: src/page/search-replace/search-form/form.js:309
msgid "Results"
msgstr ""

#: build/search-regex.js:14831
#: src/page/search-replace/search-form/index.js:45
msgid "Preset"
msgstr ""

#: build/search-regex.js:14957
#: src/page/search-replace/search-results/empty-results.js:11
msgid "No more matching results found."
msgstr ""

#: build/search-regex.js:15052
#: src/page/search-replace/search-results/index.js:90
msgid "Row ID"
msgstr ""

#: build/search-regex.js:15054
#: src/page/search-replace/search-results/index.js:91
msgid "Matched Content"
msgstr ""

#: build/search-regex.js:15182
#: src/page/support/help.js:14
msgid "Need more help?"
msgstr ""

#: build/search-regex.js:15182
#: src/page/support/help.js:16
msgid "Full documentation for Search Regex can be found at {{site}}https://searchregex.com{{/site}}."
msgstr ""

#: build/search-regex.js:15188
#: src/page/support/help.js:27
msgid "If you want to report a bug please read the {{report}}Reporting Bugs{{/report}} guide."
msgstr ""

#: build/search-regex.js:15206
#: src/page/support/help.js:53
msgid "Please note that any support is provide on as-time-is-available basis and is not guaranteed. I do not provide paid support."
msgstr ""

#: build/search-regex.js:15206
#: src/page/support/help.js:58
msgid "If you want to submit information that you don't want in a public repository then send it directly via {{email}}email{{/email}} - include as much information as you can!"
msgstr ""

#: build/search-regex.js:15212
#: src/page/support/help.js:76
msgid "Like this plugin? You might want to consider {{link}}Redirection{{/link}}, a plugin to manage redirects, from the same author."
msgstr ""

#: build/search-regex.js:15291
#: src/page/support/search-help.js:17
msgid "Quick Help"
msgstr ""

#: build/search-regex.js:15485
#: src/state/message/reducer.js:33
msgid "Settings saved"
msgstr ""

#: build/search-regex.js:15486
#: src/state/message/reducer.js:34
msgid "Row deleted"
msgstr ""

#: build/search-regex.js:15487
#: src/state/message/reducer.js:35
msgid "Row replaced"
msgstr ""

#: build/search-regex.js:15488
#: src/state/message/reducer.js:36
msgid "Row updated"
msgstr ""

#: build/search-regex.js:15489
#: src/state/message/reducer.js:37
msgid "Preset saved"
msgstr ""

#: build/search-regex.js:15490
#: src/state/message/reducer.js:38
msgid "Preset uploaded"
msgstr ""

#: build/search-regex.js:16933
#: src/state/search/selector.js:27
msgid "Regular Expression"
msgstr ""

#: build/search-regex.js:16934
#: src/state/search/selector.js:28
msgid "Regex"
msgstr ""

#: build/search-regex.js:16937
#: src/state/search/selector.js:32
msgid "Ignore Case"
msgstr ""

#: build/search-regex.js:16938
#: src/state/search/selector.js:33
msgid "Case"
msgstr ""

#: build/search-regex.js:16941
#: src/state/search/selector.js:37
msgid "Multiline"
msgstr ""

#: build/search-regex.js:16950
#: src/state/search/selector.js:48
msgid "25 per page"
msgstr ""

#: build/search-regex.js:16953
#: src/state/search/selector.js:52
msgid "50 per page"
msgstr ""

#: build/search-regex.js:16956
#: src/state/search/selector.js:56
msgid "100 per page"
msgstr ""

#: build/search-regex.js:16959
#: src/state/search/selector.js:60
msgid "250 per page"
msgstr ""

#: build/search-regex.js:16962
#: src/state/search/selector.js:64
msgid "500 per page"
msgstr ""

#: build/search-regex.js:16965
#: src/state/search/selector.js:68
msgid "1000 per page"
msgstr ""

#: build/search-regex.js:16968
#: src/state/search/selector.js:72
msgid "2000 per page"
msgstr ""

#: build/search-regex.js:19125
#: src/wp-plugin-components/error/debug/index.js:70
msgid "Show debug"
msgstr ""

#: build/search-regex.js:19129
#: src/wp-plugin-components/error/debug/index.js:80
msgid "Debug Information"
msgstr ""

#: build/search-regex.js:19465
#: src/wp-plugin-components/error/decode-error/index.js:76
msgid "WordPress did not return a response. This could mean an error occurred or that the request was blocked. Please check your server error_log."
msgstr ""

#: build/search-regex.js:19469
#: src/wp-plugin-components/error/decode-error/index.js:88
msgid "Your REST API is probably being blocked by a security plugin. Please disable this, or configure it to allow REST API requests."
msgstr ""

#: build/search-regex.js:19471
#: build/search-regex.js:19487
#: build/search-regex.js:19509
#: build/search-regex.js:19526
#: src/wp-plugin-components/error/decode-error/index.js:94
#: src/wp-plugin-components/error/decode-error/index.js:140
#: src/wp-plugin-components/error/decode-error/index.js:185
#: src/wp-plugin-components/error/decode-error/index.js:227
msgid "Read this REST API guide for more information."
msgstr ""

#: build/search-regex.js:19475
#: src/wp-plugin-components/error/decode-error/index.js:105
msgid "Your WordPress REST API is returning a 404 page. This is almost certainly an external plugin or server configuration issue."
msgstr ""

#: build/search-regex.js:19477
#: src/wp-plugin-components/error/decode-error/index.js:111
msgid "You will will need to fix this on your site. Redirection is not causing the error."
msgstr ""

#: build/search-regex.js:19477
#: src/wp-plugin-components/error/decode-error/index.js:117
msgid "Can you access your {{api}}REST API{{/api}} without it redirecting?."
msgstr ""

#: build/search-regex.js:19481
#: src/wp-plugin-components/error/decode-error/index.js:125
msgid "Check your {{link}}Site Health{{/link}} and fix any issues."
msgstr ""

#: build/search-regex.js:19485
#: src/wp-plugin-components/error/decode-error/index.js:131
msgid "Your server configuration is blocking access to the REST API."
msgstr ""

#: build/search-regex.js:19485
#: src/wp-plugin-components/error/decode-error/index.js:133
msgid "A security plugin or firewall is blocking access. You will need to whitelist the REST API."
msgstr ""

#: build/search-regex.js:19491
#: src/wp-plugin-components/error/decode-error/index.js:148
msgid "Your REST API is being redirected. Please remove the redirection for the API."
msgstr ""

#: build/search-regex.js:19495
#: src/wp-plugin-components/error/decode-error/index.js:154
msgid "Your server has rejected the request for being too big. You will need to reconfigure it to continue."
msgstr ""

#: build/search-regex.js:19499
#: src/wp-plugin-components/error/decode-error/index.js:162
msgid "An unknown error occurred."
msgstr ""

#: build/search-regex.js:19503
#: src/wp-plugin-components/error/decode-error/index.js:169
msgid "Your REST API is showing a deprecated PHP error. Please fix this error."
msgstr ""

#: build/search-regex.js:19507
#: src/wp-plugin-components/error/decode-error/index.js:179
msgid "This could be a security plugin, or your server is out of memory or has an external error. Please check your server error log"
msgstr ""

#: build/search-regex.js:19513
#: src/wp-plugin-components/error/decode-error/index.js:193
msgid "Your WordPress REST API has been disabled. You will need to enable it to continue."
msgstr ""

#: build/search-regex.js:19520
#: src/wp-plugin-components/error/decode-error/index.js:203
msgid "WordPress returned an unexpected message. This could be a PHP error from another plugin, or data inserted by your theme."
msgstr ""

#: build/search-regex.js:19520
#: src/wp-plugin-components/error/decode-error/index.js:209
msgid "Possible cause"
msgstr ""

#: build/search-regex.js:19524
#: src/wp-plugin-components/error/decode-error/index.js:221
msgid "Unable to make request due to browser security. This is typically because your WordPress and Site URL settings are inconsistent, or the request was blocked by your site CORS policy."
msgstr ""

#: build/search-regex.js:19530
#: src/wp-plugin-components/error/decode-error/index.js:239
msgid "Your REST API appears to be cached and this will cause problems. Please exclude your REST API from your caching system."
msgstr ""

#: build/search-regex.js:19601
#: src/wp-plugin-components/error/display/error-api.js:16
msgid "Bad data"
msgstr ""

#: build/search-regex.js:19601
#: src/wp-plugin-components/error/display/error-api.js:18
msgid "There was a problem making a request to your site. This could indicate you provided data that did not match requirements, or that the plugin sent a bad request."
msgstr ""

#: build/search-regex.js:19601
#: src/wp-plugin-components/error/display/error-api.js:19
msgid "Please review your data and try again."
msgstr ""

#: build/search-regex.js:19642
#: src/wp-plugin-components/error/display/error-default.js:17
msgid "REST API 404"
msgstr ""

#: build/search-regex.js:19645
#: build/search-regex.js:19708
#: build/search-regex.js:19757
#: src/wp-plugin-components/error/display/error-default.js:20
#: src/wp-plugin-components/error/display/error-fixed.js:18
#: src/wp-plugin-components/error/display/error-known.js:25
msgid "Something went wrong 🙁"
msgstr ""

#: build/search-regex.js:19800
#: src/wp-plugin-components/error/display/error-nonce.js:16
msgid "You are using an old or cached session"
msgstr ""

#: build/search-regex.js:19800
#: src/wp-plugin-components/error/display/error-nonce.js:18
msgid "This is usually fixed by doing one of the following:"
msgstr ""

#: build/search-regex.js:19800
#: src/wp-plugin-components/error/display/error-nonce.js:20
msgid "Reload the page - your current session is old."
msgstr ""

#: build/search-regex.js:19800
#: src/wp-plugin-components/error/display/error-nonce.js:22
msgid "Log out, clear your browser cache, and log in again - your browser has cached an old session."
msgstr ""

#: build/search-regex.js:19800
#: src/wp-plugin-components/error/display/error-nonce.js:27
msgid "Your admin pages are being cached. Clear this cache and try again. There may be multiple caches involved."
msgstr ""

#: src/page/search-replace/actions/replace.js:40
#: src/page/search-replace/actions/replace.js:52
msgid "Enter global replacement text"
msgstr ""
