# Translation of Plugins - Search Regex - Development (trunk) in Spanish (Spain)
# This file is distributed under the same license as the Plugins - Search Regex - Development (trunk) package.
msgid ""
msgstr ""
"PO-Revision-Date: 2023-10-31 13:58:50+0000\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=n != 1;\n"
"X-Generator: GlotPress/4.0.0-alpha.11\n"
"Language: es\n"
"Project-Id-Version: Plugins - Search Regex - Development (trunk)\n"

#. translators: %searched: number of rows searched and matched %phrases: number
#. of phrases matched
#: build/search-regex.js:16
msgid "matched rows = %(searched)s"
msgstr "filas coincidentes = %(searched)s"

#: build/search-regex.js:21
msgid "OK"
msgstr "Aceptar"

#. translators: version installed
#: build/search-regex.js:21
msgid "Version %s installed! Please read the {{url}}release notes{{/url}} for details."
msgstr "¡Versión %s instalada! Por favor, lee las {{url}}notas de la versión{{/}} para más detalles."

#: build/search-regex.js:19
msgid "A problem occurred with your last query. This is likely caused by a combination of search filters that haven't been handled properly."
msgstr "Se ha producido un problema con tu última consulta. Es probable que se deba a una combinación de filtros de búsqueda que no se han gestionado correctamente."

#: build/search-regex.js:19
msgid "Query Problem"
msgstr "Problema de consulta"

#: build/search-regex.js:19
msgid "Progress"
msgstr "Progreso"

#: build/search-regex.js:18
msgid "%s row deleted."
msgid_plural "%s rows deleted."
msgstr[0] ""
msgstr[1] ""

#: build/search-regex.js:18
msgid "Refresh"
msgstr "Recargar"

#: build/search-regex.js:18
msgid "Matched Content"
msgstr "Contenido coincidente"

#: build/search-regex.js:18 build/search-regex.js:19
msgid "Your search resulted in too many requests. Please narrow your search terms."
msgstr "Tu búsqueda ha dado como resultado demasiadas solicitudes. Limita los términos de búsqueda."

#: build/search-regex.js:18
msgid "Your search conditions have changed. Please refresh to see the latest results."
msgstr "Tus condiciones de búsqueda han cambiado. Actualiza para ver los últimos resultados."

#. translators: matches=number of matched rows, total=total number of rows
#: build/search-regex.js:10
msgid "Matched rows: %(matches)s out of %(total)s total."
msgstr "Filas coincidentes: %(matches)s de %(total)s totales."

#: build/search-regex.js:8
msgid "Delete database row"
msgstr "Borrar fila de la base de datos"

#: build/search-regex.js:8
msgid "View"
msgstr "Ver"

#: build/search-regex.js:8
msgid "No title"
msgstr "Sin título"

#: build/search-regex.js:6
msgid "Empty value"
msgstr "Valor vacío"

#: build/search-regex.js:6
msgid "No value"
msgstr "Ningún valor"

#: build/search-regex.js:6
msgid "Click to replace match"
msgstr "Haz clic para sustituir la coincidencia"

#: build/search-regex.js:6
msgid "Contains encoded data"
msgstr "Contiene datos codificados"

#: build/search-regex.js:6
msgid "New meta value"
msgstr "Nuevo meta valor"

#: build/search-regex.js:6
msgid "New meta key"
msgstr "Nueva clave meta"

#: build/search-regex.js:6
msgid "Apply to matches only"
msgstr "Aplicar sólo a las coincidencias"

#: build/search-regex.js:6
msgid "Enter replacement"
msgstr ""

#: build/search-regex.js:6
msgid "Click to replace column"
msgstr ""

#: build/search-regex.js:6
msgid "This column contains special formatting. Modifying it could break the format."
msgstr ""

#: build/search-regex.js:6
msgid "HTML"
msgstr "HTML"

#: build/search-regex.js:6
msgid "Blocks"
msgstr "Bloques"

#: build/search-regex.js:6
msgid "Serialized PHP"
msgstr ""

#: build/search-regex.js:6
msgid "Paste preset JSON."
msgstr ""

#: build/search-regex.js:6
msgid "Global Search Flags"
msgstr ""

#: build/search-regex.js:6
msgid "Global Replace"
msgstr ""

#: build/search-regex.js:6
msgid "Global Search"
msgstr ""

#: build/search-regex.js:6
msgid "View Columns"
msgstr ""

#: build/search-regex.js:6
msgid "Sources"
msgstr "Fuentes"

#: build/search-regex.js:6
msgid "Only include selected columns"
msgstr ""

#: build/search-regex.js:6
msgid "WordPress Action"
msgstr ""

#: build/search-regex.js:6
msgid "SQL"
msgstr ""

#: build/search-regex.js:6
msgid "CSV"
msgstr "CSV"

#: build/search-regex.js:6
msgid "JSON"
msgstr "JSON"

#: build/search-regex.js:6
msgid "Export Format"
msgstr ""

#: build/search-regex.js:6
msgid "Run a WordPress action for each matching result."
msgstr "Ejecuta una acción de WordPress para cada resultado coincidente."

#: build/search-regex.js:6 build/search-regex.js:18
msgid "Run Action"
msgstr "Ejecutar acción"

#: build/search-regex.js:6
msgid "Delete matching results."
msgstr "Borrar resultados coincidentes."

#: build/search-regex.js:6 build/search-regex.js:18
msgid "Delete Matches"
msgstr "Borrar coincidencias"

#: build/search-regex.js:6
msgid "Export matching results to JSON, CSV, or SQL."
msgstr "Exporta los resultados de las coincidencias a JSON, CSV o SQL."

#: build/search-regex.js:6 build/search-regex.js:18
msgid "Export Matches"
msgstr "Exportar coincidencias"

#: build/search-regex.js:6
msgid "Perform changes to specific values of the matching results."
msgstr "Realiza cambios en valores específicos de los resultados de la concordancia."

#: build/search-regex.js:6
msgid "Modify Matches"
msgstr "Modificar coincidencias"

#: build/search-regex.js:6
msgid "Replace the global search values."
msgstr "Sustituye los valores de la búsqueda global."

#: build/search-regex.js:6
msgid "Global Text Replace"
msgstr "Sustitución global de texto"

#: build/search-regex.js:6
msgid "Just show matching results."
msgstr "Sólo muestra resultados coincidentes."

#: build/search-regex.js:6
msgid "No action"
msgstr "Ninguna acción"

#: build/search-regex.js:6
msgid "Enter replacement value"
msgstr "Introduce el valor de sustitución"

#: build/search-regex.js:6
msgid "Matched values will be removed"
msgstr "Se eliminarán los valores coincidentes"

#. translators: text to replace
#: build/search-regex.js:6
msgid "Replace \"%1s\""
msgstr "Sustituye \"%1s\""

#: build/search-regex.js:4
msgid "Year"
msgstr "Año"

#: build/search-regex.js:4
msgid "Months"
msgstr "Meses"

#: build/search-regex.js:4
msgid "Weeks"
msgstr "Semanas"

#: build/search-regex.js:4
msgid "Days"
msgstr "Días"

#: build/search-regex.js:4
msgid "Hours"
msgstr "Horas"

#: build/search-regex.js:4
msgid "Minutes"
msgstr "Minutos"

#: build/search-regex.js:4
msgid "Seconds"
msgstr "Segundos"

#: build/search-regex.js:4
msgid "Decrement"
msgstr "Reducir"

#: build/search-regex.js:4
msgid "Increment"
msgstr "Aumentar"

#: build/search-regex.js:4
msgid "Set Value"
msgstr "Establece el valor"

#: build/search-regex.js:4
msgid "Replace With"
msgstr "Reemplazar por"

#: build/search-regex.js:4 build/search-regex.js:6
msgid "Add"
msgstr "Añadir"

#: build/search-regex.js:4
msgid "Column"
msgstr "Columna"

#: build/search-regex.js:4
msgid "AND"
msgstr "Y"

#: build/search-regex.js:4 build/search-regex.js:6
msgid "Filters"
msgstr "Filtros"

#: build/search-regex.js:4
msgid "Add sub-filter (OR)"
msgstr ""

#: build/search-regex.js:4
msgid "OR"
msgstr ""

#: build/search-regex.js:4
msgid "All"
msgstr ""

#: build/search-regex.js:4
msgid "between {{first/}} and {{second/}}"
msgstr ""

#: build/search-regex.js:4
msgid "No Owner"
msgstr ""

#: build/search-regex.js:4
msgid "Has Owner"
msgstr ""

#: build/search-regex.js:4
msgid "Any"
msgstr ""

#: build/search-regex.js:4
msgid "Excludes any"
msgstr ""

#: build/search-regex.js:4
msgid "Includes any"
msgstr ""

#: build/search-regex.js:4
msgid "End"
msgstr ""

#: build/search-regex.js:4
msgid "Begins"
msgstr ""

#: build/search-regex.js:4
msgid "Not contains"
msgstr ""

#: build/search-regex.js:4
msgid "Contains"
msgstr ""

#: build/search-regex.js:4
msgid "Range"
msgstr ""

#: build/search-regex.js:4
msgid "Less"
msgstr ""

#: build/search-regex.js:4
msgid "Greater"
msgstr "Mayor que"

#: build/search-regex.js:4
msgid "Not Equals"
msgstr "No iguales"

#: build/search-regex.js:4
msgid "Equals"
msgstr "Iguales"

#: build/search-regex.js:4
msgid "Optional global search phrase. Leave blank to use filters only."
msgstr "Frase de búsqueda global opcional. Déjala en blanco para utilizar sólo filtros."

#: build/search-regex.js:2
msgid "REST API 404"
msgstr "404 de la API REST"

#: build/search-regex.js:2
msgid "Your REST API appears to be cached and this will cause problems. Please exclude your REST API from your caching system."
msgstr "Tu API REST parece estar cacheada y esto provocará problemas. Por favor, excluye tu API REST de tu sistema de caché."

#: build/search-regex.js:2
msgid "Your REST API is showing a deprecated PHP error. Please fix this error."
msgstr ""

#: build/search-regex.js:2
msgid "Your server configuration is blocking access to the REST API."
msgstr ""

#: build/search-regex.js:2
msgid "Can you access your {{api}}REST API{{/api}} without it redirecting?."
msgstr ""

#: build/search-regex.js:2
msgid "You will will need to fix this on your site. Redirection is not causing the error."
msgstr ""

#: build/search-regex.js:2
msgid "Preset uploaded"
msgstr ""

#: build/search-regex.js:2
msgid "Multiline"
msgstr ""

#: build/search-regex.js:2
msgid "Case"
msgstr ""

#: build/search-regex.js:2
msgid "Regex"
msgstr ""

#: includes/source/plugin/source-redirection.php:499
msgid "Nginx"
msgstr ""

#: includes/source/plugin/source-redirection.php:495
msgid "Apache"
msgstr ""

#: includes/source/plugin/source-redirection.php:491
msgid "WordPress"
msgstr ""

#: includes/source/plugin/source-redirection.php:487
msgid "Module"
msgstr "Módulo"

#: includes/source/plugin/source-redirection.php:468
msgid "Redirection Groups"
msgstr "Grupos de redireccionamiento"

#: includes/source/plugin/source-redirection.php:347
msgid "Not accessed"
msgstr "No se ha accedido"

#: includes/source/plugin/source-redirection.php:232
msgid "Target"
msgstr "Objetivo"

#: includes/source/plugin/source-redirection.php:208
msgid "HTTP Code"
msgstr "Código HTTP"

#: includes/source/plugin/source-redirection.php:203
msgid "Position"
msgstr "Posición"

#: includes/source/plugin/source-redirection.php:196
#: includes/source/plugin/source-redirection.php:514
msgid "Disabled"
msgstr "Desactivado"

#: includes/source/plugin/source-redirection.php:192
#: includes/source/plugin/source-redirection.php:510
msgid "Enabled"
msgstr "Activado"

#: includes/source/plugin/source-redirection.php:188
#: includes/source/plugin/source-redirection.php:506
msgid "Status"
msgstr "Estado"

#: includes/source/plugin/source-redirection.php:181
msgid "Group"
msgstr "Grupo"

#: includes/source/plugin/source-redirection.php:176
msgid "Last Access"
msgstr "Último acceso"

#: includes/source/plugin/source-redirection.php:171
msgid "Hit Count"
msgstr "Contador de aciertos"

#: includes/source/plugin/source-redirection.php:164
msgid "Source URL (matching)"
msgstr "URL de origen (coincidente)"

#: includes/source/plugin/source-redirection.php:157
msgid "Source URL"
msgstr "URL de la fuente"

#: includes/source/core/source-user.php:174
msgid "User meta"
msgstr "Meta del usuario"

#: includes/source/core/source-user.php:168
msgid "Registered"
msgstr ""

#: includes/source/core/source-user.php:134
msgid "Login"
msgstr ""

#: includes/source/core/source-terms.php:135
msgid "Taxonomy"
msgstr ""

#: includes/source/core/source-post.php:411
msgid "Post Tag"
msgstr ""

#: includes/source/core/source-post.php:402
msgid "Post Category"
msgstr ""

#: includes/source/core/source-post.php:396
msgid "Comment Count"
msgstr ""

#: includes/source/core/source-post.php:389
msgid "MIME"
msgstr ""

#: includes/source/core/source-post.php:374
msgid "Parent"
msgstr ""

#: includes/source/core/source-post.php:368
msgid "Modified GMT"
msgstr ""

#: includes/source/core/source-post.php:362
msgid "Modified"
msgstr ""

#: includes/source/core/source-post.php:355
msgid "Password"
msgstr ""

#: includes/source/core/source-post.php:352
msgid "Has no password"
msgstr ""

#: includes/source/core/source-post.php:348
msgid "Has password"
msgstr ""

#: includes/source/core/source-post.php:339
msgid "Ping Status"
msgstr ""

#: includes/source/core/source-post.php:323
msgid "Comment Status"
msgstr ""

#: includes/source/core/source-post.php:316
#: includes/source/core/source-post.php:332
msgid "Open"
msgstr "Abierto"

#: includes/source/core/source-post.php:308
msgid "Post Status"
msgstr "Estado de publicación"

#: includes/source/core/source-post.php:268
msgid "Post Type"
msgstr "Tipo de contenido"

#: includes/source/core/source-options.php:136
msgid "Autoload"
msgstr "Carga automática"

#: includes/source/core/source-options.php:133
msgid "Is not autoload"
msgstr ""

#: includes/source/core/source-options.php:129
msgid "Is autoload"
msgstr ""

#: includes/source/core/source-meta.php:126 build/search-regex.js:4
#: build/search-regex.js:6
msgid "Meta Value"
msgstr "Valor meta"

#: includes/source/core/source-meta.php:119 build/search-regex.js:4
#: build/search-regex.js:6
msgid "Meta Key"
msgstr "Clave meta"

#: includes/source/core/source-meta.php:112
msgid "Owner ID"
msgstr "ID de propietario"

#: includes/source/core/source-comment.php:262
msgid "Comment meta"
msgstr "Comentario meta"

#: includes/source/core/source-comment.php:254
msgid "User ID"
msgstr "ID de usuario"

#: includes/source/core/source-comment.php:248
msgid "Parent Comment"
msgstr ""

#: includes/source/core/source-comment.php:237
msgid "Trackback"
msgstr "Trackback"

#: includes/source/core/source-comment.php:233
msgid "Pingback"
msgstr "Pingback"

#: includes/source/core/source-comment.php:229
msgid "Type"
msgstr "Tipo"

#: includes/source/core/source-comment.php:224
msgid "User Agent"
msgstr "Agente de usuario"

#: includes/source/core/source-comment.php:219
msgid "Approval Status"
msgstr "Estado de aprobación"

#: includes/source/core/source-comment.php:216
msgid "Spam"
msgstr "Spam"

#: includes/source/core/source-comment.php:212
msgid "Approved"
msgstr "Aprobado"

#: includes/source/core/source-comment.php:208
msgid "Unapproved"
msgstr "Rechazado"

#: includes/source/core/source-comment.php:199
#: includes/source/core/source-post.php:295
msgid "Date GMT"
msgstr "Fecha GMT"

#: includes/source/core/source-comment.php:193
#: includes/source/core/source-post.php:289
msgid "Date"
msgstr "Fecha"

#: includes/source/core/source-comment.php:188
msgid "IP"
msgstr "IP"

#: includes/source/core/source-comment.php:161
#: includes/source/core/source-post.php:282
msgid "Author"
msgstr "Autor"

#: includes/source/core/source-comment.php:154
msgid "Post ID"
msgstr "ID de entrada"

#: includes/source/core/source-comment.php:148
#: includes/source/core/source-options.php:105
#: includes/source/core/source-post.php:245
#: includes/source/core/source-terms.php:114
#: includes/source/core/source-user.php:127
#: includes/source/plugin/source-redirection.php:151
#: includes/source/plugin/source-redirection.php:474
msgid "ID"
msgstr "ID"

#: includes/source/class-manager.php:76
#: includes/source/core/source-terms.php:108
msgid "Terms"
msgstr "Términos"

#: includes/source/class-manager.php:22
#: includes/source/core/source-post.php:239
msgid "Posts (core & custom)"
msgstr ""

#: build/search-regex.js:2
msgid "Please review your data and try again."
msgstr "Por favor, revisa tus datos e inténtalo de nuevo."

#: build/search-regex.js:2
msgid "There was a problem making a request to your site. This could indicate you provided data that did not match requirements, or that the plugin sent a bad request."
msgstr "Ha habido un problema al hacer una solicitud a tu sitio. Esto podría indicar que has proporcionado datos que no coinciden con los requisitos o que plugin ha enviado una solicitud errónea."

#: build/search-regex.js:2
msgid "Bad data"
msgstr "Datos erróneos"

#: build/search-regex.js:2
msgid "Your WordPress REST API is returning a 404 page. This is almost certainly an external plugin or server configuration issue."
msgstr "Tu API REST está devolviendo una página 404. Es casi seguro que sea debido a un problema con un plugin externo o con la configuración del servidor."

#: build/search-regex.js:2
msgid "2000 per page"
msgstr "2000 por página"

#: build/search-regex.js:2
msgid "1000 per page"
msgstr "1000 por página"

#: build/search-regex.js:2
msgid "Set a preset to use by default when Search Regex is loaded."
msgstr "Establece un preajuste para usar por defecto cuando se cargue Search Regex."

#: build/search-regex.js:2
msgid "No default preset"
msgstr "Ningún preajuste por defecto"

#: build/search-regex.js:2
msgid "Default Preset"
msgstr "Preajuste por defecto"

#: build/search-regex.js:2
msgid "Your admin pages are being cached. Clear this cache and try again. There may be multiple caches involved."
msgstr "Tus páginas de administración están almacenadas en la caché. Vacía esta caché e inténtalo de nuevo. Puede haber varias cachés involucradas."

#: build/search-regex.js:2
msgid "This is usually fixed by doing one of the following:"
msgstr "Esto normalmente se corrige haciendo algo de lo siguiente:"

#: build/search-regex.js:2
msgid "You are using an old or cached session"
msgstr "Estás usando una sesión antigua o almacenada en la caché"

#: build/search-regex.js:2
msgid "Debug Information"
msgstr "Información de depuración"

#: build/search-regex.js:2
msgid "Show debug"
msgstr "Mostrar la depuración"

#: build/search-regex.js:2
msgid "WordPress returned an unexpected message. This could be a PHP error from another plugin, or data inserted by your theme."
msgstr "WordPress ha devuelto un mensaje inesperado. Esto podría ser un error de PHP de otro plugin o a datos insertados por tu tema."

#: build/search-regex.js:2
msgid "Your WordPress REST API has been disabled. You will need to enable it to continue."
msgstr "Tu API REST de WordPress ha sido desactivada. Tendrás que activarla para continuar."

#: build/search-regex.js:2
msgid "Your REST API is being redirected. Please remove the redirection for the API."
msgstr "Tu API REST está siendo redirigida. Por favor, elimina la redirección para la API."

#: build/search-regex.js:2
msgid "A security plugin or firewall is blocking access. You will need to whitelist the REST API."
msgstr "Un plugin de seguridad o un cortafuegos está bloqueando el acceso. Tendrás que poner a la API REST en lista blanca."

#: build/search-regex.js:2
msgid "Check your {{link}}Site Health{{/link}} and fix any issues."
msgstr "Comprueba tu {{link}}salud del sitio{{/link}} y corrige cualquier problema."

#: build/search-regex.js:6
msgid "Preset"
msgstr "Preajuste"

#: build/search-regex.js:6
msgid "Upload"
msgstr "Subir"

#: build/search-regex.js:6
msgid "Add file"
msgstr "Añadir un archivo"

#: build/search-regex.js:2
msgid "Preset saved"
msgstr "Preajuste guardado"

#: build/search-regex.js:6
msgid "Copy to clipboard"
msgstr "Copiar al portapapeles"

#: build/search-regex.js:6
msgid "Are you sure you want to delete this preset?"
msgstr "¿Seguro que quieres borrar este preajuste?"

#: build/search-regex.js:6
msgid "Locked fields"
msgstr "Campos bloqueados"

#: build/search-regex.js:6
msgid "For example, create tag {{code}}URL{{/code}} and title {{code}}Image URL{{/code}}. Your search could be {{code}}<img src=\"URL\">{{/code}}. When the preset is used it will ask the user for the {{code}}Image URL{{/code}} instead of the full search phrase."
msgstr "Por ejemplo, crea la etiqueta {{code}}URL{{/code}} y el título {{code}}URL de la imagen{{/code}}. Tu búsqueda podría ser {{code}}<img src=\"URL\">{{/code}}. Cuando se use el preajuste, pedirá al usuario la {{code}}URL de la imagen{{/code}} en lugar de la frase completa de búsqueda."

#: build/search-regex.js:6
msgid "A tag creates a custom input field. Insert the tag anywhere in the search, replace, text filter, or text action and when the preset is used it will be replaced with a custom text field with the tag label."
msgstr ""

#: build/search-regex.js:6
msgid "Enter tag which is used in the field"
msgstr "Introduce la etiqueta que se usa en el campo"

#: build/search-regex.js:6
msgid "Tag"
msgstr "Etiqueta"

#: build/search-regex.js:6
msgid "Enter tag title shown to user"
msgstr "Introduce el título de la etiqueta mostrado al usuario"

#: build/search-regex.js:6
msgid "Tags"
msgstr "Etiquetas"

#: build/search-regex.js:6
msgid "Locking a field removes it from the search form and prevents changes."
msgstr "Bloquear un campo lo elimina del formulario de búsqueda y evita cambios."

#: build/search-regex.js:6
msgid "Fields"
msgstr "Campos"

#: build/search-regex.js:6
msgid "Locked Fields"
msgstr "Campos bloqueados"

#: build/search-regex.js:6
msgid "Advanced preset"
msgstr "Preajuste avanzado"

#: build/search-regex.js:6
msgid "Describe the preset"
msgstr "Describe el preajuste"

#: build/search-regex.js:6
msgid "Preset Description"
msgstr "Descripción del preajuste"

#: build/search-regex.js:6
msgid "Give the preset a name"
msgstr "Dar un nombre al preajuste"

#: build/search-regex.js:6
msgid "Preset Name"
msgstr "Nombre del preajuste"

#: build/search-regex.js:6
msgid "Edit preset"
msgstr "Editar el preajuste"

#: build/search-regex.js:6
msgid "Results per page"
msgstr "Resultados por página"

#: build/search-regex.js:6
msgid "remove phrase"
msgstr "eliminar la frase"

#: build/search-regex.js:6
msgid "no phrase"
msgstr "ninguna frase"

#: build/search-regex.js:6
msgid "Import"
msgstr "Importar "

#: build/search-regex.js:6
msgid "Please check your JSON data is a valid preset. You may have copied it incorrectly, or pasted something that is not a preset."
msgstr "Por favor, comprueba que tus datos JSON son un preajuste válido. Puede que los hayas copiado de forma incorrecta o que hayas pegado algo que no es un preajuste."

#: build/search-regex.js:6
msgid "Unable to import preset"
msgstr "No ha sido posible importar el preajuste"

#: build/search-regex.js:6
msgid "Import preset from clipboard"
msgstr "Importar un preajuste desde el portapapeles"

#: build/search-regex.js:6
msgid "Done"
msgstr "Hecho"

#: build/search-regex.js:6
msgid "Uploaded %(total)d preset"
msgid_plural "Uploaded %(total)d presets"
msgstr[0] "Subido %(total)d preajuste"
msgstr[1] "Subidos %(total)d preajustes"

#: build/search-regex.js:6
msgid "Importing"
msgstr "Importando"

#: build/search-regex.js:6
msgid "File selected"
msgstr "Archivo seleccionado"

#: build/search-regex.js:6
msgid "Click 'Add File' or drag and drop here."
msgstr "Haz clic en «Añadir archivo» o arrástralo y suéltalo aquí."

#: build/search-regex.js:6
msgid "Import a JSON file"
msgstr "Importar un archivo JSON"

#: build/search-regex.js:6
msgid "Import JSON"
msgstr "Importar JSON"

#: build/search-regex.js:6
msgid "Export JSON"
msgstr "Exportar JSON"

#: build/search-regex.js:6
msgid "Download presets!"
msgstr "¡Descargar los preajustes!"

#: build/search-regex.js:6
msgid "There are no presets"
msgstr "No hay preajustes"

#: build/search-regex.js:4 build/search-regex.js:6
msgid "Flags"
msgstr "Etiquetas"

#: build/search-regex.js:21
msgid "Presets"
msgstr "Preajustes"

#: build/search-regex.js:2
msgid "If that did not help then {{strong}}create an issue{{/strong}} or send it in an {{strong}}email{{/strong}}."
msgstr "Si eso no ha ayudado, {{strong}}crea una incidencia{{/strong}} o envíalo por {{strong}}correo electrónico{{/strong}}."

#: build/search-regex.js:2
msgid "Please check the {{link}}support site{{/link}} before proceeding further."
msgstr "Por favor, consulta el {{link}}sitio de soporte{{/link}} antes de seguir adelante."

#: build/search-regex.js:6
msgid "Enter preset name"
msgstr "Introducir el nombre del preajuste"

#: build/search-regex.js:6
msgid "Enter a name for your preset"
msgstr "Introduce un nombre para tu preajuste"

#: build/search-regex.js:6
msgid "Saving Preset"
msgstr "Guardando el preajuste"

#: build/search-regex.js:6
msgid "Update current preset"
msgstr "Actualizar el preajuste actual"

#: build/search-regex.js:6
msgid "Save search as new preset"
msgstr "Guardar la búsqueda como un nuevo preajuste"

#: build/search-regex.js:6
msgid "No preset"
msgstr "Ningún preajuste"

#: build/search-regex.js:6
msgid "Saving preset"
msgstr "Guardando el preajuste"

#: includes/source/class-manager.php:158
msgid "Advanced"
msgstr "Avanzado"

#: includes/source/class-manager.php:151
msgid "Standard"
msgstr "Estándar"

#: build/search-regex.js:19
msgid "Please backup your data before making modifications."
msgstr "Por favor, haz una copia de seguridad de sus datos antes de realizar modificaciones."

#: build/search-regex.js:18
msgid "%s row."
msgid_plural "%s rows."
msgstr[0] "%s fila."
msgstr[1] "%s filas."

#: build/search-regex.js:2
msgid "An unknown error occurred."
msgstr "Se ha producido un error desconocido"

#: includes/source/core/source-post.php:382
msgid "GUID"
msgstr "GUID"

#: includes/source/core/source-post.php:259
#: includes/source/core/source-terms.php:128
msgid "Slug"
msgstr "Slug"

#: includes/source/core/source-post.php:301
msgid "Excerpt"
msgstr "Extracto"

#: includes/source/core/source-comment.php:181
#: includes/source/core/source-post.php:274
msgid "Content"
msgstr "Contenido"

#: includes/source/core/source-user.php:148
msgid "Display Name"
msgstr "Nombre visible"

#: includes/source/core/source-user.php:141
msgid "Nicename"
msgstr "Slug de usuario"

#: includes/source/core/source-options.php:118
msgid "Value"
msgstr "Valor"

#: includes/source/core/source-comment.php:241
msgid "Comment"
msgstr "Comentario"

#: includes/source/core/source-options.php:111
#: includes/source/core/source-terms.php:121
#: includes/source/plugin/source-redirection.php:480 build/search-regex.js:6
msgid "Name"
msgstr "Nombre"

#: includes/source/core/source-post.php:251
#: includes/source/plugin/source-redirection.php:214 build/search-regex.js:6
msgid "Title"
msgstr "Título"

#: includes/source/core/source-comment.php:175
#: includes/source/core/source-user.php:161
msgid "URL"
msgstr "URL"

#. translators: 1: server PHP version. 2: required PHP version.
#: search-regex.php:37
msgid "Disabled! Detected PHP %1$s, need PHP %2$s+"
msgstr "¡Desactivado! Detectado PHP %1$s, se necesita PHP %2$s o superior"

#: includes/source/class-manager.php:165
msgid "Plugins"
msgstr "Plugins"

#: includes/source/class-manager.php:40
msgid "WordPress Options"
msgstr "Opciones de WordPress"

#: includes/source/class-manager.php:70
#: includes/source/core/source-user-meta.php:30
msgid "User Meta"
msgstr "Meta de usuario"

#: includes/source/class-manager.php:34
#: includes/source/core/source-user.php:121
msgid "Users"
msgstr "Usuarios"

#: includes/source/class-manager.php:64
#: includes/source/core/source-comment-meta.php:23
msgid "Comment Meta"
msgstr "Meta de comentario"

#: includes/source/class-manager.php:28
#: includes/source/core/source-comment.php:142
msgid "Comments"
msgstr "Comentarios"

#: includes/source/class-manager.php:58
#: includes/source/core/source-post-meta.php:25
#: includes/source/core/source-post.php:420
msgid "Post Meta"
msgstr "Meta de la entrada"

#: includes/search-regex-admin.php:467
msgid "Please enable JavaScript"
msgstr "Por favor, activar JavaScript"

#: includes/search-regex-admin.php:463
msgid "Loading, please wait..."
msgstr "Cargando, por favor espere..."

#: includes/search-regex-admin.php:446
msgid "Create Issue"
msgstr "Crear incidencia"

#: includes/search-regex-admin.php:443
msgid "<code>SearchRegexi10n</code> is not defined. This usually means another plugin is blocking Search Regex from loading. Please disable all plugins and try again."
msgstr "<code>SearchRegexi10n</code> no está definido. Esto generalmente significa que otro plugin está bloqueando la carga de Search Regex. Por favor, desactiva todos los plugins e inténtalo de nuevo."

#: includes/search-regex-admin.php:442
msgid "If you think Search Regex is at fault then create an issue."
msgstr ""

#: includes/search-regex-admin.php:441
msgid "Please see the <a href=\"https://searchregex.com/support/problems/\">list of common problems</a>."
msgstr ""

#: includes/search-regex-admin.php:440
msgid "Please note that Search Regex requires the WordPress REST API to be enabled. If you have disabled this then you won't be able to use Search Regex"
msgstr "Tenga en cuenta que Search Regex requiere que la API REST de WordPress esté activada. Si la ha desactivado, no podrá utilizar Search Regex."

#: includes/search-regex-admin.php:438
msgid "Also check if your browser is able to load <code>search-regex.js</code>:"
msgstr "Comprueba también si tu navegador es capaz de cargar <code>search-regex.js</code>:"

#: includes/search-regex-admin.php:436
msgid "This may be caused by another plugin - look at your browser's error console for more details."
msgstr ""

#: includes/search-regex-admin.php:435
msgid "Unable to load Search Regex ☹️"
msgstr "No ha sido posible cargar Search Regex ☹️"

#: includes/search-regex-admin.php:420
msgid "Unable to load Search Regex"
msgstr "No ha sido posible cargar Search Regex"

#. translators: 1: Expected WordPress version, 2: Actual WordPress version
#: includes/search-regex-admin.php:417
msgid "Search Regex requires WordPress v%1$1s, you are using v%2$2s - please update your WordPress"
msgstr ""

#: includes/search-regex-admin.php:318
msgid "Search Regex Support"
msgstr "Soporte de Search Regex"

#. translators: URL
#: includes/search-regex-admin.php:309
msgid "You can find full documentation about using Search Regex on the <a href=\"%s\" target=\"_blank\">searchregex.com</a> support site."
msgstr ""

#: includes/search-regex-admin.php:84
msgid "Settings"
msgstr "Ajustes"

#: build/search-regex.js:6
msgid "Action"
msgstr ""

#: build/search-regex.js:18
msgid "Row ID"
msgstr "ID de fila"

#: build/search-regex.js:8
msgid "No more matching results found."
msgstr "No se encontraron más resultados coincidentes."

#: build/search-regex.js:12
msgid "Last page"
msgstr "Última página"

#. translators: current=current page, total=total number of pages
#: build/search-regex.js:12
msgid "Page %(current)s of %(total)s"
msgstr "Página %(current)s de %(total)s"

#: build/search-regex.js:12 build/search-regex.js:18
msgid "Next page"
msgstr "Página siguiente"

#. translators: %current: current percent progress
#: build/search-regex.js:18
msgid "Progress %(current)s%%"
msgstr ""

#: build/search-regex.js:10
msgid "Prev page"
msgstr "Página anterior"

#: build/search-regex.js:10 build/search-regex.js:16
msgid "First page"
msgstr "Primera página"

#. translators: %s: total number of rows searched
#: build/search-regex.js:14
msgid "%s database row in total"
msgid_plural "%s database rows in total"
msgstr[0] "%s fila de la base de datos en total"
msgstr[1] "%s filas de la base de datos en total"

#: build/search-regex.js:2
msgid "500 per page"
msgstr "500 por página"

#: build/search-regex.js:2
msgid "250 per page"
msgstr "250 por página"

#: build/search-regex.js:2
msgid "100 per page"
msgstr "100 por página"

#: build/search-regex.js:2
msgid "50 per page"
msgstr "50 por página"

#: build/search-regex.js:2
msgid "25 per page"
msgstr "25 por página"

#: build/search-regex.js:2
msgid "Ignore Case"
msgstr "Ignorar mayúsculas/minúsculas"

#: build/search-regex.js:2
msgid "Regular Expression"
msgstr "Expresión regular"

#: build/search-regex.js:2
msgid "Row updated"
msgstr "Fila actualizada"

#: build/search-regex.js:2
msgid "Row replaced"
msgstr "Fila reemplazada"

#: build/search-regex.js:2
msgid "Row deleted"
msgstr "Fila borrada"

#: build/search-regex.js:2
msgid "Settings saved"
msgstr "Ajustes guardados"

#: includes/search-regex-admin.php:301
msgid "{{link}}Source Flags{{/link}} - additional options for the selected source. For example, include post {{guid}}GUID{{/guid}} in the search."
msgstr "{{link}}Banderas de búsqueda{{/link}} - añade opciones para seleccionar el origin. Por ejemplo, incluye la entrada {{guid}}GUID{{/guid}} en la búsqueda."

#: includes/search-regex-admin.php:314
msgid "{{link}}Source{{/link}} - the source of data you wish to search. For example, posts, pages, or comments."
msgstr ""

#: includes/search-regex-admin.php:313
msgid "{{link}}Regular expression{{/link}} - a way of defining a pattern for text matching. Provides more advanced matches."
msgstr "{{link}}Expresión regular{{/link}} - una forma de definir un patrón para la coincidencia de texto. Proporciona coincidencias más avanzadas."

#: includes/search-regex-admin.php:312
msgid "{{link}}Search Flags{{/link}} - additional qualifiers for your search, to enable case insensitivity, and to enable regular expression support."
msgstr "{{link}}Banderas de búsqueda{{/link}} - añade calificadores adicionales a tu búsqueda, para activar sensibilidad de mayúsculas y minúsculas, y el soporte de expresiones regulares."

#: includes/search-regex-admin.php:310
msgid "The following concepts are used by Search Regex:"
msgstr "Search Regex utiliza los siguientes conceptos:"

#: build/search-regex.js:4
msgid "Like this plugin? You might want to consider {{link}}Redirection{{/link}}, a plugin to manage redirects, from the same author."
msgstr "¿Te gusta este plugin? Quizás quieras considerar probar {{link}}Redirection{/link}}, un plugin para gestionar redirecciones, del mismo autor."

#: includes/source/plugin/source-redirection.php:145 build/search-regex.js:4
msgid "Redirection"
msgstr "Redirección"

#: build/search-regex.js:4
msgid "If you want to submit information that you don't want in a public repository then send it directly via {{email}}email{{/email}} - include as much information as you can!"
msgstr "Si quieres enviar información que no quieres que aparezca en un repositorio público, envíala directamente por {{email}}email{/email}} - ¡incluye toda la información que puedas!"

#: build/search-regex.js:4
msgid "Please note that any support is provide on as-time-is-available basis and is not guaranteed. I do not provide paid support."
msgstr "Ten en cuenta que la asistencia se presta en función del tiempo disponible y no está garantizada. No ofrezco asistencia de pago."

#: build/search-regex.js:4
msgid "If you want to report a bug please read the {{report}}Reporting Bugs{{/report}} guide."
msgstr "Si quieres informar de un error, lee la guía {{report}}Informar de errores{{/report}}."

#: build/search-regex.js:4
msgid "Full documentation for Search Regex can be found at {{site}}https://searchregex.com{{/site}}."
msgstr "La documentación completa para Search Regex se puede encontrar en el {{site}}https://searchregex.com{{/site}}."

#: build/search-regex.js:4
msgid "Need more help?"
msgstr "¿Necesitas más ayuda?"

#: build/search-regex.js:6
msgid "Results"
msgstr "Resultados"

#: build/search-regex.js:6 build/search-regex.js:18
msgid "Source"
msgstr "Fuente"

#: build/search-regex.js:4
msgid "Enter search phrase"
msgstr "Introduce la frase a buscar"

#: build/search-regex.js:18
msgid "Replace All"
msgstr "Reemplazar todo"

#: build/search-regex.js:6 build/search-regex.js:18
msgid "Search"
msgstr "Buscar"

#: build/search-regex.js:19
msgid "Search and replace information in your database."
msgstr "Busca y reemplaza la información en tu base de datos."

#: build/search-regex.js:2
msgid "Update"
msgstr "Actualizar"

#: build/search-regex.js:2
msgid "How Search Regex uses the REST API - don't change unless necessary"
msgstr "Cómo utiliza Search Regex la API REST - no lo cambies salvo que sea necesario"

#: build/search-regex.js:2
msgid "REST API"
msgstr "API REST"

#: build/search-regex.js:2
msgid "I'm a nice person and I have helped support the author of this plugin"
msgstr "Soy una buena persona y he ayudado al autor de este plugin"

#: build/search-regex.js:2
msgid "Relative REST API"
msgstr "API REST relativa"

#: build/search-regex.js:2
msgid "Raw REST API"
msgstr "Raw API REST"

#: build/search-regex.js:2
msgid "Default REST API"
msgstr "REST API por defecto"

#: build/search-regex.js:4
msgid "Plugin Support"
msgstr "Soporte del plugin"

#: build/search-regex.js:4
msgid "Support 💰"
msgstr "Soporte 💰"

#: build/search-regex.js:4
msgid "You get useful software and I get to carry on making it better."
msgstr "Tú obtienes un software útil y yo sigo mejorándolo."

#: build/search-regex.js:4
msgid "Search Regex is free to use - life is wonderful and lovely! It has required a great deal of time and effort to develop and you can help support this development by {{strong}}making a small donation{{/strong}}."
msgstr "«Search Regex» es de uso gratuito - ¡La vida es maravillosa y encantadora! Su desarrollo ha requerido una gran cantidad de tiempo y esfuerzo, y puedes contribuir a él haciendo {{strong}}una pequeña donación{{/strong}}."

#: build/search-regex.js:4
msgid "I'd like to support some more."
msgstr "Me gustaría apoyar un poco más."

#: build/search-regex.js:4
msgid "You've supported this plugin - thank you!"
msgstr "Has apoyado este plugin - ¡gracias!"

#: build/search-regex.js:4
msgid "If that doesn't help, open your browser's error console and create a {{link}}new issue{{/link}} with the details."
msgstr "Si eso no ayuda, abre la consola de errores de tu navegador y crea {{link}}una nueva incidencia{{/link}} con los detalles."

#: includes/search-regex-admin.php:437 build/search-regex.js:4
msgid "If you are using a page caching plugin or service (CloudFlare, OVH, etc) then you can also try clearing that cache."
msgstr ""

#: build/search-regex.js:4
msgid "Search Regex is not working. Try clearing your browser cache and reloading this page."
msgstr "Search Regex no está funcionando. Prueba a vaciar la caché de tu navegador y recargar esta página."

#: build/search-regex.js:19
msgid "clearing your cache."
msgstr "vaciando su caché."

#: build/search-regex.js:19
msgid "If you are using a caching system such as Cloudflare then please read this: "
msgstr "Si utilizas un sistema de almacenamiento en caché como Cloudflare, lee esto: "

#: build/search-regex.js:19
msgid "Please clear your browser cache and reload this page."
msgstr "Por favor, borra la caché de tu navegador y vuelve a cargar esta página."

#: build/search-regex.js:19
msgid "Cached Search Regex detected"
msgstr "Se ha detectado Search Regex en la caché"

#. translators: number of results to show
#: build/search-regex.js:8
msgid "Show %s more"
msgid_plural "Show %s more"
msgstr[0] "Mostrar %s más"
msgstr[1] "Mostrar %s más"

#: build/search-regex.js:6
msgid "Maximum number of matches exceeded and hidden from view. These will be included in any replacements."
msgstr "Se ha superado el número máximo de coincidencias y se ha ocultado a la vista. Estos se incluirán en cualquier reemplazo."

#: build/search-regex.js:6
msgid "Delete"
msgstr "Borrar"

#: build/search-regex.js:6 build/search-regex.js:8
msgid "Edit"
msgstr "Editar"

#: build/search-regex.js:4
msgid "Check Again"
msgstr "Comprobar de nuevo"

#. translators: test percent
#: build/search-regex.js:4
msgid "Testing - %s%%"
msgstr "Probando - %s%%"

#: build/search-regex.js:2
msgid "Show Problems"
msgstr "Mostrar problemas"

#: build/search-regex.js:2
msgid "Summary"
msgstr "Resumen"

#: build/search-regex.js:2
msgid "You are using a broken REST API route. Changing to a working API should fix the problem."
msgstr "Estás utilizando una ruta de la API REST que no funciona. Cambiar a una API que funcione debería solucionar el problema."

#: build/search-regex.js:2
msgid "Your REST API is not working and the plugin will not be able to continue until this is fixed."
msgstr "Tu API REST no funciona y el plugin no podrá continuar hasta que esto se solucione."

#: build/search-regex.js:2
msgid "There are some problems connecting to your REST API. It is not necessary to fix these problems and the plugin is able to work."
msgstr "Hay algunos problemas de conexión a tu API REST. No es necesario solucionar estos problemas y el plugin podrá funcionar."

#: build/search-regex.js:2
msgid "Unavailable"
msgstr "No disponible"

#: build/search-regex.js:2
msgid "Not working but fixable"
msgstr "No funciona pero se puede corregir"

#: build/search-regex.js:2
msgid "Working but some issues"
msgstr "Funciona pero tiene algunos problemas"

#: build/search-regex.js:2
msgid "Good"
msgstr "Bien"

#: build/search-regex.js:2
msgid "Current API"
msgstr "API actual"

#: build/search-regex.js:2
msgid "Switch to this API"
msgstr "Cambiar a esta API"

#: build/search-regex.js:2
msgid "Hide"
msgstr "Ocultar"

#: build/search-regex.js:2
msgid "Show Full"
msgstr "Mostrar todo"

#: build/search-regex.js:2
msgid "Working!"
msgstr "¡Funcionando!"

#: build/search-regex.js:19
msgid "Finished!"
msgstr "¡Finalizado!"

#: build/search-regex.js:6 build/search-regex.js:18
msgid "Cancel"
msgstr "Cancelar"

#: build/search-regex.js:4 build/search-regex.js:6
msgid "Replace"
msgstr "Reemplazar"

#: build/search-regex.js:4 build/search-regex.js:6
msgid "Remove"
msgstr "Eliminar"

#: build/search-regex.js:2 build/search-regex.js:6
msgid "Multi"
msgstr "Multi"

#: build/search-regex.js:6
msgid "Single"
msgstr "Individual"

#: build/search-regex.js:21
msgid "Support"
msgstr "Soporte"

#: includes/source/core/source-options.php:99 build/search-regex.js:21
msgid "Options"
msgstr "Opciones"

#: build/search-regex.js:21
msgid "Search & Replace"
msgstr "Buscar y reemplazar"

#: build/search-regex.js:4
msgid "If you are using WordPress 5.2 or newer then look at your {{link}}Site Health{{/link}} and resolve any issues."
msgstr "Si estás usando WordPress 5.2 o superior, revisa la {{link}}salud del sitio{{/link}} y resuelve cualquier problema."

#: build/search-regex.js:4
msgid "{{link}}Please temporarily disable other plugins!{{/link}} This fixes so many problems."
msgstr "{{link}}¡Desactiva temporalmente otros plugins!{{/link}} Esto soluciona muchos problemas."

#: build/search-regex.js:4
msgid "{{link}}Caching software{{/link}}, in particular Cloudflare, can cache the wrong thing. Try clearing all your caches."
msgstr "{{link}}El software de almacenamiento en caché{/link}}, en particular Cloudflare, puede almacenar en caché algo incorrecto. Prueba a borrar todas las cachés."

#: build/search-regex.js:4 build/search-regex.js:19
msgid "What do I do next?"
msgstr "¿Qué hago ahora?"

#: build/search-regex.js:2
msgid "Something went wrong 🙁"
msgstr "Algo ha ido mal 🙁"

#: build/search-regex.js:2
msgid "Log out, clear your browser cache, and log in again - your browser has cached an old session."
msgstr "Sal, vacía la caché de tu navegador y vuelve a acceder - tu navegador ha guardado en la caché una sesión antigua."

#: build/search-regex.js:2
msgid "Reload the page - your current session is old."
msgstr ""

#: build/search-regex.js:2
msgid "Include these details in your report along with a description of what you were doing and a screenshot."
msgstr "Incluye estos detalles en tu informe junto con una descripción de lo que estabas haciendo y una captura de pantalla."

#: includes/source/core/source-comment.php:168
#: includes/source/core/source-user.php:155 build/search-regex.js:2
#: build/search-regex.js:6 build/search-regex.js:19
msgid "Email"
msgstr "Correo eletrónico"

#: build/search-regex.js:2 build/search-regex.js:6 build/search-regex.js:19
msgid "Create An Issue"
msgstr "Crear una incidencia"

#: includes/source/core/source-post.php:320
#: includes/source/core/source-post.php:336
msgid "Closed"
msgstr "Cerrada"

#: build/search-regex.js:6
msgid "Save"
msgstr "Guardar"

#: build/search-regex.js:2
msgid "Unable to make request due to browser security. This is typically because your WordPress and Site URL settings are inconsistent, or the request was blocked by your site CORS policy."
msgstr "No se puede realizar la solicitud debido a la seguridad del navegador. Esto suele ocurrir porque los ajustes de WordPress y de la URL del sitio son inconsistentes o la política de intercambio de recursos de origen cruzado («CORS») de tu sitio ha bloqueado la solicitud."

#: build/search-regex.js:2
msgid "Possible cause"
msgstr "Causa posible"

#: build/search-regex.js:2
msgid "This could be a security plugin, or your server is out of memory or has an external error. Please check your server error log"
msgstr ""

#: build/search-regex.js:2
msgid "Your server has rejected the request for being too big. You will need to reconfigure it to continue."
msgstr "Tu servidor rechazó la petición por ser demasiado grande. Necesitarás volver a configurarla para continuar."

#: build/search-regex.js:2
msgid "Your REST API is probably being blocked by a security plugin. Please disable this, or configure it to allow REST API requests."
msgstr ""

#: build/search-regex.js:2
msgid "Read this REST API guide for more information."
msgstr "Lea esta guía de la API REST para obtener más información."

#: build/search-regex.js:2
msgid "WordPress did not return a response. This could mean an error occurred or that the request was blocked. Please check your server error_log."
msgstr ""

#. Author of the plugin
msgid "John Godley"
msgstr "John Godley"

#. Description of the plugin
msgid "Adds search and replace functionality across posts, pages, comments, and meta-data, with full regular expression support"
msgstr "Añade la funcionalidad de búsqueda y reemplazo en entradas, páginas, comentarios y metadatos, con un soporte completo de expresiones regulares."

#. Plugin URI of the plugin
msgid "https://searchregex.com/"
msgstr "https://searchregex.com/"

#. Plugin Name of the plugin
#: build/search-regex.js:21
msgid "Search Regex"
msgstr "Search Regex"