=== WP Test Email ===
Contributors: boopathi0001
Donate link: https://paypal.me/boopathirajan
Tags: Test email, Check Mail, Mail Tester, WP Mail, wp_email, Test WordPress Mail, Check WordPress Mail, PHP Mail
Requires at least: 4.3
Tested up to: 6.1
Requires PHP: 5.2.4
Stable tag: 1.1.6
License: GPLv2 or later
License URI: http://www.gnu.org/licenses/gpl-2.0.html

WP Test Email is allows you to test if your WordPress installation is sending mail or not.

== Description ==

Do you want to test whether the WordPress installation is sending mail or not?. WP Test Email is helps you to do that.
It allows you to send simple test email to an email address of your choice.

https://www.youtube.com/watch?v=99LFut4PPVU

Kindly let us know your feedback or comments to add more features in this plugin.

== Installation ==

1. Log in to your WordPress admin panel and go to Plugins -> Add New
2. Type **WP Test Email** in the search box and click on search button.
3. Find WP Test Email plugin.
4. Then click on Install Now after that activate the plugin.

OR

1. Download and save the **WP Test Email** plugin to your hard disk.
2. Login to your WordPress and go to the Add Plugins page.
3. Click Upload Plugin button to upload the zip.
4. Click Install Now to install and activate the plugin.