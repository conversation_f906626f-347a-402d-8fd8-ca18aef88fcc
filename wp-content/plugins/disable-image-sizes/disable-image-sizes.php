<?php

/* @wordpress-plugin
 * Plugin Name:       Disable Image Sizes
 * Description:       Disable specific Image Sizes by name
 * Version:           0.1
 * Author:            <PERSON>
 * Author URI:        https://theuniversal.co/
 * Text Domain:       disable-image-sizes
 * Domain Path:       /languages
 */

defined( 'ABSPATH' ) || die();

add_filter('intermediate_image_sizes', function($sizes) {
	return array_diff($sizes, ['medium_large','holiday_type','gram']);
  });
  
add_action( 'init', 'ae_remove_large_image_sizes' );
  function ae_remove_large_image_sizes() {
	remove_image_size( '1536x1536' );
	remove_image_size( '2048x2048' );
  }