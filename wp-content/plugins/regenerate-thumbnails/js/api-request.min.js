!function(a){function b(a){return a=b.buildAjaxOptions(a),b.transport(a)}var c=window.wpApiSettings;b.buildAjaxOptions=function(b){var d,e,f,g,h,i=b.url,j=b.path;if("string"==typeof b.namespace&&"string"==typeof b.endpoint&&(d=b.namespace.replace(/^\/|\/$/g,""),e=b.endpoint.replace(/^\//,""),j=e?d+"/"+e:d),"string"==typeof j&&(i=c.root+j.replace(/^\//,"")),g=!(b.data&&b.data._wpnonce),f=b.headers||{},g)for(h in f)if(f.hasOwnProperty(h)&&"x-wp-nonce"===h.toLowerCase()){g=!1;break}return g&&(f=a.extend({"X-WP-Nonce":c.nonce},f)),b=a.extend({},b,{headers:f,url:i}),delete b.path,delete b.namespace,delete b.endpoint,b},b.transport=a.ajax,window.wp=window.wp||{},window.wp.apiRequest=b}(jQuery);