=== Export any WordPress data to XML/CSV ===
Contributors: soflyy, wpallimport
Requires at least: 5.0
Tested up to: 6.5
Stable tag: 1.4.7
Requires PHP: 7.4
Tags: export, wordpress csv export, wordpress xml export, export woocommerce, migrate, export csv from wordpress, export xml from wordpress, advanced xml export, advanced csv export, export data, bulk csv export, export custom post type, export woocommerce products, export woocommerce orders, migrate woocommerce, csv export, export csv, xml export, export xml, csv exporter, datafeed

Easily export any data from WordPress. Drag & drop to create a completely custom spreadsheet, CSV, or XML file.

== Description ==

= WP All Export - Simple & Powerful XML / CSV Export Plugin =

WP All Export features a three step export process and an intuitive drag & drop interface that makes complicated export tasks simple and fast.

With WP All Export you can: export data for easy editing, migrate content from WordPress to another site, create a WooCommerce affiliate feed, generate filtered lists of WooCommerce orders, export the email addresses of new customers, create and publish customized WordPress RSS feeds - and much more.

**[Click here to try WP All Export right now](https://www.wpallimport.com/try-export-free/).**

[youtube https://www.youtube.com/watch?v=a-z0R-Ldkqo/]

* **Turn your WordPress data into a customized CSV or XML**

* **Choose which data to export:** WP All Export's drag and drop interface makes it easy to select exactly which data you'd like to export

* **Structure your export file however you like:** Rename CSV columns and XML elements, rearrange them, whatever you want to do.

* **Export any custom post type, any custom field:** Lots of plugins and themes store custom data in WordPress. You can export all of it with WP All Export.

* **Easy integration with WP All Import:** WP All Export will generate your WP All Import settings for you so importing your data back into WordPress is easy, simple, and fast.

**Wish you could edit your WordPress data in Excel? Now you can - export it with WP All Export, edit it, and then import it again with WP All Import.**

For technical support from the developers, please consider purchasing WP All Export Pro.

= WP All Export Professional Edition =

**WP All Export Pro** is a paid upgrade that includes premium support and adds the following features:

* **Send your data to 500+ apps:** Full integration with Zapier allows you to send your exported WordPress data to services like Dropbox and Google Drive, to create and update reports in Google Sheets, send email updates, or anything else you can think of. This is especially useful when you export WooCommerce orders to CSV.

	[Read more about WP All Export Pro and Zapier.](https://zapier.com/zapbook/wp-all-export-pro/)

* **Schedule exports to run automatically:** Exports can be configured to run on any schedule you like. You can export new sales every week, recent user sign ups, new affiliate products added to your site, daily product stock reports, etc. Scheduled exports are very powerful when combined with Zapier.

* **Add rules to filter data:** WP All Export Pro makes it easy to export the exact posts/products/orders you need. Want to export all WooCommerce orders over $100? Want to export all of the green shirts from your WooCommerce store? Want to export all new posts from 2014, except the ones added by Steve?

	You can with a simple to use interface on the 'New Export' page in WP All Export Pro.

* **Export WordPress users:** WP All Export Pro adds the ability to export WordPress users and all custom data associated with them. Available data is organized and cleaned up so you don’t need to know anything about how WordPress stores users in order to export them.

* **Export WooCommerce orders:** Export WooCommerce Order item data with WP All Export Pro. Just as with any other custom post type, you can export WooCommerce orders with the free version of WP All Export. However, the order item data is stored by WooCommerce in several custom database tables and this custom data is only accessible with WP All Export Pro.

* **Pass data through custom PHP functions:** With WP All Export Pro you can pass your data through a custom function before it is added to your export file. This will allow you to manipulate your data any way you see fit.

* **Guaranteed technical support via e-mail.**

[Upgrade to the Pro edition of WP All Export.](http://www.wpallimport.com/upgrade-to-wp-all-export-pro/?utm_source=export-plugin-free&utm_medium=readme&utm_campaign=premium-support)

= Automatic Scheduling =

A new service from Soflyy, Automatic Scheduling provides a simple interface for setting exports to run on a schedule. The service will make sure that your exports start on time and that they successfully complete without the need to set up individual cron jobs.

It costs $19/mo and can be used with WP All Export and WP All Import. You can set up as many exports and imports on as many sites as you like.

**What information is shared with Soflyy?** If you set an export to run on a schedule, WP All Export will open an encrypted connection to Soflyy servers. It will send the license key, site URL, ID of the export you want to run, export security key, and times that you want the export to run.

= WordPress CSV Exports =

A CSV is a very simple type of spreadsheet file where each column is separated by a comma. With WP All Export you can very easily set up a WordPress CSV export and control the order and title of the columns.

Very often you'll want to edit your data with Microsoft Excel, Google Sheets, Numbers, or maybe something else. This is why a CSV export is so powerful - all spreadsheet software can read, edit, and save CSV files. WP All Export allows you edit your WordPress data using whatever spreadsheet software you are most comfortable with.

= WordPress XML Exports =

Sometimes you'll want to export your data so that some other tool, software, or service can use it. Very often they will require your data to be formatted as an XML file. XML is very similar to HTML, but you don't need to know anything about that in order to set up an XML export with WP All Export.

If you want to set up a WordPress XML export all you need to do is select 'XML' when configuring your export template. And just like a CSV export, an XML export will allow you to customize the element names and put them in any order you wish.

= Related Plugins =
[Import any XML or CSV File to WordPress](https://wordpress.org/plugins/wp-all-import/)
[Import Products from any XML or CSV to WooCommerce](https://wordpress.org/plugins/woocommerce-xml-csv-product-import/)
[Export Products to CSV/XML for WooCommerce](https://wordpress.org/plugins/product-export-for-woocommerce/)
[Custom Product Tabs for WooCommerce WP All Import Add-on](https://wordpress.org/plugins/custom-product-tabs-wp-all-import-add-on/)
[Export Orders to CSV/XML for WooCommerce](https://wordpress.org/plugins/order-export-for-woocommerce/)
[Export WordPress Users to CSV/XML](https://wordpress.org/plugins/export-wp-users-xml-csv/)

= Related Tutorials =
[How to Export WooCommerce Products to Google Merchant Center (Google Shopping)](https://www.wpallimport.com/documentation/how-to-export-woocommerce-products-to-google-merchant-center/)
[How to Export Gravity Forms Entries](https://www.wpallimport.com/documentation/how-to-export-gravity-form-entries-to-csv-or-xml/)
[Export Toolset Types to CSV, XML, or Excel](https://www.wpallimport.com/toolset-types-export-csv-xml/)
[How to Export WooCommerce Variable Products](https://www.wpallimport.com/documentation/how-to-export-woocommerce-variable-products/)
[How to Export Blogs, Articles, Posts, Standard Post Data, Permalinks, Excerpts, Captions, Menus, Subscriptions, and Other Details From Your Websites](https://www.wpallimport.com/documentation/export-wordpress-posts/)
[How To Export WordPress Comments](https://www.wpallimport.com/documentation/export-wordpress-comments-csv-xml/)
[How To Export WooCommerce Coupons](https://www.wpallimport.com/documentation/how-to-export-woocommerce-coupons-to-csv-or-xml/)
[How To Export WooCommerce Reviews](https://www.wpallimport.com/documentation/how-to-export-woocommerce-reviews-to-csv-or-xml/)
[How to Schedule Your Exports to Run Automatically](https://www.wpallimport.com/documentation/how-to-schedule-wordpress-exports/)
[How to Migrate WooCommerce and WordPress From One Host to Another](https://www.wpallimport.com/documentation/how-to-migrate-woocommerce-and-wordpress-data/)

== Premium Support ==
Upgrade to the Pro edition of WP All Export for premium support.

E-mail: <EMAIL>

== Installation ==

Either: -

* Upload the plugin from the Plugins page in WordPress
* Unzip wp-all-export.zip and upload the contents to /wp-content/plugins/, and then activate the plugin from the Plugins page in WordPress

== Frequently Asked Questions ==

= How do I export WordPress data to CSV? =

1. Go to All Export › New Export.
2. Select the post type that you want to export.
3. Configure the export columns.
4. Leave the Export Type set to CSV File.
5. Run the export and download the export file.

= How do I export WordPress data to Excel? =

1. Create a new export at All Export › New Export.
2. Choose the post type that you wish to export.
3. Select your export columns.
4. Change the Export Type to Excel File.
5. Complete the export and download the export file.

= How do I export WordPress data to XML? =

1. Start a new export in All Export › New Export.
2. Select the export post type from the dropdown list.
3. Set up your export columns.
4. Change the Export Type to Feed › Simple XML Feed.
5. Finish the export and download the export file.

= How do I export an entire WordPress site? =

By default, WordPress offers a native tool to export all WordPress data. To export a WordPress site entirely and manually, you can also export the WordPress database and then download the WordPress files from your server via SFTP or FTP.

With our plugin, you can export the post types or custom post types available on your WordPress site, but you can't export your full site at once. You have to export one post type at a time.

= How do I export WordPress media? =

To export media from your WordPress site, you must export the post type or custom post type associated with the media that you wish to export. For example, if you have multiple images attached to posts, you need to export the WordPress posts to obtain those images.

= What types of data can I export from WordPress? =

You can export every type of data, including posts, pages, categories, tags, users, comments, custom data, images, and all types of WooCommerce data.

= Can I export WordPress data in different languages? =

Yes. You can export text in more than 40 languages when using the [WordPress Multilingual Plugin (WPML)](https://wordpress.org/plugins/woocommerce-multilingual/).

= How do I schedule WordPress exports? =

To schedule WordPress exports, you can either manually create cron jobs on your server or use our automatic service to schedule your exports directly from our interface.

= Can I export custom WordPress data added by a plugin or extension? =

Yes. Our plugin automatically detects all custom fields, categories, and tags created by any WordPress theme or plugin. You can export this custom data the same way that you export regular data.

= How do I migrate WordPress data to another website? =

1. Export the WordPress data that you want to migrate.
2. Download the Bundle option, which includes import instructions.
3. Import the bundle file on the destination site.
4. WP All Import will automatically configure itself using the bundle file.
5. Complete the import and review your migrated data.

= How do I bulk edit WordPress data? =

1. Export the WordPress data that you want to edit.
2. Open the export file in your favorite spreadsheet app.
3. Perform your bulk edits.
4. Import the modified file back into WordPress.
5. Review the affected data to make sure the changes were applied.

= How do I get support? =

If you are using the free version of the plugin, you can obtain support through the WordPress.org community forums. If you have purchased the premium version, you can email us directly and we will respond as quickly as we can, typically in less than one business day.

== Changelog ==

= 1.4.7 =
* improvement: UI updates
* improvement: use 'setup_network' meta capability for access

= 1.4.6 =
* improvement: UI updates

= 1.4.5 =
* bug fix: restore compatibility with WooCommerce Export Add-On Pro

= 1.4.4 =
* improvement: update upgrade links

= 1.4.3 =
* bug fix: '$addons_not_included' PHP warnings

= 1.4.2 =
* improvement: better PHP 8.2+ support

= 1.4.1 =
* security improvement

= 1.4.0 =
* security improvement
* improvement: better PHP 8.2 support
* improvement: enable adding BOM to files by default for new exports and improve description
* improvement: minimize code called during AJAX requests
* improvement: remove 'wp_navigation' from dropdown on Step 1
* improvement: add code to 'admin_head' only on WP All Export pages

= 1.3.9 =
* improvement: remove deprecated jQuery functions
* bug fix: resolve various PHP notices and warnings
* bug fix: error on Step 1 when 'Disable syntax highlighting when editing code' User option is enabled

= 1.3.8 =
* improvement: only contact Scheduling service if a Scheduling license is set
* bug fix: enable use of 's', 'LIKE' and 'NOT LIKE' in WP_Query exports

= 1.3.7 =
* bug fix: cannot save Scheduling service license key

= 1.3.6 =
* security improvement
* bug fix: encoded ampersand showing in 'Confirm & Run' text
* improvement: UI enhancements
* improvement: use CodeMirror library from WordPress Core.
* improvement: only check Scheduling Service connectivity if a Scheduling license has been saved

= 1.3.5 =
* security improvements
* improvement: update autoloader to support paths containing underscores

= 1.3.4 =
* improvement: UI updates
* bug fix: PHP 8 compatibility

= 1.3.3 =
* new feature: compatibility with the Gravity Forms Export Add-On
* improvement: fallback to PclZip if ZipArchive isn't available
* bugfix: some output not correctly escaped

= 1.3.2 =
* improvement: add support for exporting non-WooCommerce 'product' CPTs
* improvement: initial PHP 8 support
* improvement: only set max_execution_time on WPAE pages
* bug fix

= 1.3.1 =
* improvement: only set max_execution_time on export pages
* bugfix: error when using the ACF Export Add-On Pro

= 1.3.0 =
* improvement: added plugin version when loading tipsy JS file
* improvement: moved ACF and WooCommerce support to add-ons

= 1.2.10 =
* bugfix: WP_Query Results export only works with the User Export Add-On active

= 1.2.9 =
* maintenance: compatibility with Elementor v3.3 JavaScript changes

= 1.2.8 =
* improvement: add more info about client mode on the settings page
* improvement: remove unused deprecated function add_contextual_help()
* improvement: use wp_salt() when AUTH_SALT is not available
* bugfix: Google Merchants export notice appears and then immediately disappears
* bugfix: warning in PHP 7.3 when using continue in switch statements

= 1.2.7 =
* maintenance: compatibility with WordPress v5.5 jQuery changes

= 1.2.6 =
* improvement: disable scheduled exports via Automatic Scheduling UI
* API: add new filter wp_all_export_no_cache to avoid server cache for export files
* bug fix: saving scheduling license not working

= 1.2.5 =
* bug fix: preserve existing admin body classes

= 1.2.4 =
* improvement: add support for User Export Add-On
* improvement: better local timezone detection for Automatic Scheduling
* improvement: more consistent "Add New Field" user interface
* bugfix: ACF repeater fields broken in ACF 5.7.10+
* bugfix: when using migrate posts, only the image fields are added to the import template

= 1.2.3 =
* bugfix: compatibility with ACF 5.7.11

= 1.2.2 =
* bugfix: support exports scheduled at 12am and 12pm
* bugfix: fix JS error when editing export template

= 1.2.1 =
* new filter: added pmxe_after_iteration action
* bugfix: increase custom query meta limit
* bugfix: remove deprecated function calls for PHP 7.2 compatibility
* bugfix: address various PHP warnings and notices
* bugfix: graceful failure for non:writable uploads folder
* bugfix: warning when exporting WooCommerce orders using Automatic Scheduling
* bugfix: WooCommerce product variations exported as separate products when adding product attributes to custom export fields
* bugfix: small UI and spacing issues
* bugfix: prices with more than two decimal places are not exported correctly
* bugfix: exporting empty ACF values shifts rows in CSV exports
* bugfix: Australia missing from scheduling timezones
* bugfix: unable to filter or export WooCommerce product visibility data
* bugfix: Automatic scheduling UI spacing issues
* bugfix: unrelated errors are reported in functions.php file

= 1.2.0 =
* new feature: Automatic Scheduling - A new, optional service from Soflyy that makes scheduling exports incredibly easy.
* improvement: Support migrating users & keeping passwords
* improvement: Better error reporting for errors in custom functions
* improvement: Support for WooCommerce Short Description
* improvement: Better price formatting
* improvement: Add author information to Available Data
* improvement: Add Featured Image to Available Data › Media
* new filter: Disable price formatting - wp_all_export_raw_prices
* new filter: Called before each order item - wp_all_export_order_item
* new filter: Use custom CSV writer when default affected by https://bugs.php.net/bug.php?id=43225 - wp_all_export_use_csv_compliant_line_endings
* new filter: Output content before CSV headers - wp_all_export_pre_csv_headers
* bugfix: Error thrown when uploads folder is not writable
* bugfix: Autoloading broken when class names contain "_"
* bugfix: Error when activating Pro version and Free version
* bugfix: moment.js causes false positives for virus scanners
* bugfix: Comment status updated by default and causes problems
* bugfix: CSV Writer can't handle slash double quote - https://bugs.php.net/bug.php?id=43225
* bugfix: WPAE prevents Jetpack from connecting to WordPress.com
* bugfix: Exported Excel files are corrupted in some cases
* bugfix: Plugin conflicts change the post order
* bugfix: CSV headers broken when field names contain quotes
* bugfix: Product visibility broken for old WooCommerce versions
* bugfix: Preview is broken for date fields
* bugfix: Custom Export Field values not saved in some cases
* bugfix: Headers broken when Custom Export Fields contain special characters
* bugfix: Loading order for styles optimized to reduce conflicts
* bugfix: Available Data broken when there's more than 500 data elements

= 1.1.5 =
* improvement: removed autoload=true from wp_options
* improvement: WPML options in separate section
* bug fix: allow underscores in main xml tags
* bug fix: prevent uploading import template into wpae
* bug fix: ID column in CSV
* bug fix: ACF repeater headers

= 1.1.4 =
* improvement: removed autoload=true from wp_options
* improvement: WPML options in separate section
* bug fix: allow underscores in main xml tags
* bug fix: prevent uploading import template into wpae
* bug fix: ID column in CSV
* bug fix: ACF repeater headers

= 1.1.3 =
* improvement: added post_modified field
* bug fix: export attributes for simple products
* bug fix: db schema updating
* bug fix: variations with trashed parents shouldn't be included in the export

= 1.1.2 =
* improvement: choose WPML language in export options
* bug fix: export ACF message field
* bug fix: export product categories with AFC repeater fields
* bug fix: export duplicate images from gallery
* bug fix: export search
* bug fix: export product variation attribute names
* bug fix: migrating hierarchical posts and pages

= 1.1.1 =
* improvement: compatibility with PHP 7.x

= 1.1.0 =
* improvement: added ACF fields to 'migrate' & 'add all fields' features
* improvement: added new filter 'wp_all_export_field_name'
* improvement: changed default date export to Y/m/d
* bug fix: export shipping class for product variations
* bug fix: import template for ACF relationship fields
* bug fix: export empty images metadata
* bug fix: import template for ACF gallery
* bug fix: import template for variation_description
* bug fix: import template for default product attributes
* bug fix: automatically adding parent_id column on products export

= 1.0.9 =
* bug fix: fixed compatibility with PHP 5.3

= 1.0.8 =
* improvement: pull the parent taxonomy data when exporting variations
* improvement: remove spaces from export filename
* improvement: new filter wp_all_export_after_csv_line
* improvement: date options for sale price dates from/to
* improvement: possibility to use tab as csv delimiter
* improvement: new filter 'wp_all_export_csv_headers'
* bug fix: db schema on multisite
* bug fix: import template for media items
* bug fix: export ACF repeater in XML format
* bug fix: export in CSV format when 'Main XML Tag' & 'Record XML Tag' option are blank
* bug fix: export ACF date_time_picker

= 1.0.7 =
* fixed db schema for multisite
* fixed export order items date
* fixed media items export ordering
* fixed import template for media items
* fixed export ACF repeater in XML format
* added new filter 'wp_all_export_csv_headers'
* added possibility to use tab as csv delimiter "\t"
* updated french translation

= 1.0.6 =
* added new filters 'wp_all_export_is_wrap_value_into_cdata', 'wp_all_export_add_before_element', 'wp_all_export_add_after_element'
* added 'WPML Translation ID' element to available data
* modified preview to show first 10 records
* fixed csv export with non comma delimiter
* fixed conflict with WP Google Maps Pro plugin

= 1.0.5 =
* fixed misaligned columns on exporting product attributes
* fixed export nested repeaters field in CSV format
* fixed live records counting for advanced export mode
* fixed Events Calendar conflict
* added new filters 'wp_all_export_add_before_node', 'wp_all_export_add_after_node'
* added possibility export repeater rows one per line
* exclude orphaned variations from exprt file
* changed UI for export media data ( images & attachments )

= 1.0.4 =
* fixed export attachment meta alt
* fixed export manually stored ACF
* fixed export repeater field for users in csv format
* fixed import export templates
* fixed ajaxurl conflict with WPML
* added French & Latvian translations
* added 'Variation Description' field

= 1.0.3 =
* fixed manage exports screen: "Info and options" disappears when WPAI plugin is disabled
* fixed css for WordPress 4.4
* fixed export ACF repeater field
* updated re-run export screen
* added hidden post types to the dropdown list on step 1
* added export templates
* added possibility to control main xml tag names & put additional info into xml file: apply_filters('wp_all_export_additional_data', array(), $exportOptions)
* added ‘pmxe_exported_post’ action: do_action('pmxe_exported_post', $record->ID );
* added option 'Create a new export file each time export is run'
* added option 'Only export posts once'
* added option 'Split large exports into multiple files'
* added possibility to change export field name ( related to export WooCommerce orders )
* added es_ES translation
* added possibility to add NS to field names

= 1.0.2 =
* fixed download bundle
* fixed export repeater fields for ACF 4.x
* fixed import template for custom product attributes
* added new option 'include BOM to export file
* added RU translation
* removed hidden post types from dropdown in step 1

= 1.0.1 =
* fixed export taxonomy: name instead of slug
* fixed pass data through php function
* added advanced (custom fields) section to export woo orders
* added draggable element deletion
* added auto-generate product export fields
* added 'attributes' field to product data
* added button to download bundle for WP All Import
* updated export file name
* changed export files destination to /exports

= 1.0.0 =
* WP All Export exits beta

= 0.9.1 =
* critical security fix - stopping non-logged in users from accessing adminInit http://www.wpallimport.com/2015/02/wp-import-4-1-1-mandatory-security-update/

= 0.9 =
* Initial release on WordPress.org.
