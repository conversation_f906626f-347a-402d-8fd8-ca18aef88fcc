.wpallexport-plugin .wpallexport-upgrade-notice {
  text-align: center;
  padding: 60px 20px 20px 20px;
}
.wpallexport-plugin .wpallexport-upgrade-notice h1 {
  font-size: 32px;
  font-weight: 700;
}
.wpallexport-plugin .wpallexport-upgrade-notice a.custom-close {
  content: "";
  height: 12px;
  width: 12px;
  position: absolute;
  background-image: url("../img/upgrade-notice/dismiss.svg");
  background-repeat: no-repeat;
  background-size: 12px;
  top: 25px;
  right: 35px;
}
.wpallexport-plugin .wpallexport-upgrade-notice .upgrade h2 {
  font-size: 20px;
  color: #41515F;
  font-weight: 400;
  width: 70%;
  margin: auto;
  line-height: 1.2;
}
.wpallexport-plugin .wpallexport-upgrade-notice .upgrade > .perks {
  display: flex;
  justify-content: center;
  margin: 40px 0;
}
.wpallexport-plugin .wpallexport-upgrade-notice .upgrade > .perks > li {
  padding: 0;
  list-style-type: none;
  padding: 0 20px;
}
.wpallexport-plugin .wpallexport-upgrade-notice .upgrade > .perks > li > span {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  font-size: 14px;
  font-weight: 900;
  color: #02182B;
}
.wpallexport-plugin .wpallexport-upgrade-notice .upgrade > .perks > li > span:before {
  content: "";
  display: inline-flex;
  height: 64px;
  width: 64px;
  background-size: 64px 64px;
  background-repeat: no-repeat;
  padding-bottom: 15px;
}
.wpallexport-plugin .wpallexport-upgrade-notice .upgrade > .perks > li > small {
  font-size: 14px;
  color: #41515F;
  line-height: 1.5;
}
.wpallexport-plugin .wpallexport-upgrade-notice .upgrade > .perks > li.guarantee > span:before {
  background-image: url("../img/upgrade-notice/guarantee.svg");
}
.wpallexport-plugin .wpallexport-upgrade-notice .upgrade > .perks > li.updates > span:before {
  background-image: url("../img/upgrade-notice/updates.svg");
}
.wpallexport-plugin .wpallexport-upgrade-notice .upgrade > .perks > li.support > span:before {
  background-image: url("../img/upgrade-notice/support.svg");
}
.wpallexport-plugin .wpallexport-upgrade-notice .upgrade > .perks > li.license > span:before {
  background-image: url("../img/upgrade-notice/license.svg");
}
.wpallexport-plugin .wpallexport-upgrade-notice .upgrade > .features {
  display: flex;
  justify-content: center;
  padding: 35px 0;
}
.wpallexport-plugin .wpallexport-upgrade-notice .upgrade > .features .column {
  width: 40%;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}
.wpallexport-plugin .wpallexport-upgrade-notice .upgrade > .features .column.list > span {
  display: flex;
  align-items: center;
  margin: 6px 0;
  font-size: 16px;
}
.wpallexport-plugin .wpallexport-upgrade-notice .upgrade > .features .column.list > span:before {
  content: "";
  display: inline-flex;
  height: 16px;
  width: 16px;
  background-image: url("../img/upgrade-notice/check.svg");
  background-size: 16px;
  margin-right: 12px;
}
.wpallexport-plugin .wpallexport-upgrade-notice .upgrade > .features .column.cta {
  justify-content: flex-end;
  align-items: center;
  padding-bottom: 10px;
}
.wpallexport-plugin .wpallexport-upgrade-notice .upgrade > .features .column.cta > span {
  color: #41515F;
  font-size: 13px;
  margin-top: 10px;
}
.wpallexport-plugin .wpallexport-upgrade-notice .upgrade .trusted {
  display: block;
}
.wpallexport-plugin .wpallexport-upgrade-notice .upgrade p.already-have {
  margin: 0;
}
.wpallexport-plugin .wpallexport-upgrade-notice .upgrade p.already-have a {
  color: rgba(65, 81, 95, 0.61);
  font-size: 13px;
  cursor: pointer;
}
.wpallexport-plugin .wpallexport-upgrade-notice .upgrade .upgrade-button {
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  background-color: #00B3B6;
  font-size: 20px;
  font-weight: 600;
  border-radius: 7px;
  width: 100%;
  height: 64px;
  border: none;
}
.wpallexport-plugin .wpallexport-upgrade-notice .install {
  display: none;
}
.wpallexport-plugin .wpallexport-upgrade-notice .install p {
  margin-top: 30px;
  font-size: 16px;
}
.wpallexport-plugin .wpallexport-upgrade-notice .install a {
  font-size: 16px;
}
.wpallexport-plugin .wpallexport-upgrade-notice .wp-pointer-buttons {
  display: none;
}

/*# sourceMappingURL=upgrade-notice.css.map */
