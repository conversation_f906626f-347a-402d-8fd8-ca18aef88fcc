msgid ""
msgstr ""
"Project-Id-Version: Multiple Columns for Gravity Forms\n"
"POT-Creation-Date: 2020-09-06 19:37+0200\n"
"PO-Revision-Date: 2020-10-12 16:09+0200\n"
"Last-Translator: \n"
"Language-Team: \n"
"Language: en_US\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: Poedit 2.4.1\n"
"X-Poedit-Basepath: ..\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Poedit-Flags-xgettext: --add-comments=translators:\n"
"X-Poedit-WPHeader: gf-form-multicolumn.php\n"
"X-Poedit-SourceCharset: UTF-8\n"
"X-Poedit-KeywordsList: __;_e;_n:1,2;_x:1,2c;_ex:1,2c;_nx:4c,1,2;esc_attr__;"
"esc_attr_e;esc_attr_x:1,2c;esc_html__;esc_html_e;esc_html_x:1,2c;_n_noop:1,2;"
"_nx_noop:3c,1,2;__ngettext_noop:1,2\n"
"X-Poedit-SearchPath-0: .\n"
"X-Poedit-SearchPathExcluded-0: *.min.js\n"
"X-Poedit-SearchPathExcluded-1: vendor\n"

#: includes/admin/WH_GF_Multicolumn_Admin.php:88
msgid "Enable CSS"
msgstr "Enable CSS"

#: includes/admin/WH_GF_Multicolumn_Admin.php:96
msgid "Load CSS Stylesheet"
msgstr "Load CSS Stylesheet"

#: includes/admin/WH_GF_Multicolumn_Admin.php:109
msgid "Enable JS"
msgstr "Enable JS"

#: includes/admin/WH_GF_Multicolumn_Admin.php:116
msgid "Load JS Script"
msgstr "Load JS Script"

#: includes/admin/WH_GF_Multicolumn_Admin.php:209
msgid ""
"Load MultiColumn specific CSS file (note that deactivating this may result "
"in the plugin layout breaking)."
msgstr ""
"Load MultiColumn specific CSS file (note that deactivating this may result "
"in the plugin layout breaking)."

#: includes/admin/WH_GF_Multicolumn_Admin.php:211
msgid ""
"Load MultiColumn specific JS file which alters spacing when using "
"conditional logic elements."
msgstr ""
"Load MultiColumn specific JS file which alters spacing when using "
"conditional logic elements."

#: includes/admin/WH_GF_Multicolumn_Admin.php:213
msgid "Use elements to construct column divisions in the form."
msgstr "Use elements to construct column divisions in the form."

#: includes/admin/WH_GF_Multicolumn_Admin.php:227
msgid ""
" too many Row Starts to Row Ends. Please review and remove the excess Row "
"Starts."
msgstr ""
" too many Row Starts to Row Ends. Please review and remove the excess Row "
"Starts."

#: includes/admin/WH_GF_Multicolumn_Admin.php:229
msgid ""
" too many Row Ends to Row Starts. Please review and remove the excess Row "
"Ends."
msgstr ""
" too many Row Ends to Row Starts. Please review and remove the excess Row "
"Ends."

#: includes/admin/field/WH_GF_Multicolumn_Field_Column_End.php:26
msgid "Row End"
msgstr "Row End"

#: includes/admin/field/WH_GF_Multicolumn_Field_Column_Separator.php:26
msgid "Column Break"
msgstr "Column Break"

#: includes/admin/field/WH_GF_Multicolumn_Field_Column_Start.php:26
msgid "Row Start"
msgstr "Row Start"

#: includes/admin/field/WH_GF_Multicolumn_Field_Group.php:53
msgid "Multiple Columns Fields"
msgstr "Multiple Columns Fields"

#. Plugin Name of the plugin/theme
msgid "Multiple Columns for Gravity Forms"
msgstr "Multiple Columns for Gravity Forms"

#. Plugin URI of the plugin/theme
msgid "https://wordpress.org/plugins/gf-form-multicolumn/"
msgstr "https://wordpress.org/plugins/gf-form-multicolumn/"

#. Description of the plugin/theme
msgid ""
"Introduces new form elements into Gravity Forms which allow rows to be split "
"into multiple columns."
msgstr ""
"Introduces new form elements into Gravity Forms which allow rows to be split "
"into multiple columns."

#. Author of the plugin/theme
msgid "WebHolism"
msgstr "WebHolism"

#. Author URI of the plugin/theme
msgid "http://www.webholism.com"
msgstr "http://www.webholism.com"

#~ msgid "Column End"
#~ msgstr "Row End"

#~ msgid ""
#~ "Allows addition of multiple columns (and multiple rows of multiple "
#~ "columns) to Gravity Forms. <p>GDPR Compliance: this plugin does not "
#~ "collect data.</p>"
#~ msgstr ""
#~ "Allows addition of multiple columns (and multiple rows of multiple "
#~ "columns) to Gravity Forms. <p>GDPR Compliance: this plugin does not "
#~ "collect data.</p>"
