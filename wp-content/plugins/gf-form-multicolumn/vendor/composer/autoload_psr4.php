<?php

// autoload_psr4.php @generated by Composer

$vendorDir = dirname( dirname( __FILE__ ) );
$baseDir   = dirname( $vendorDir );

return [
	'WH\\GF\\Multicolumn\\Site\\'         => [ $baseDir . '/includes/public' ],
	'WH\\GF\\Multicolumn\\Classes\\'      => [ $baseDir . '/includes' ],
	'WH\\GF\\Multicolumn\\Admin\\Field\\' => [ $baseDir . '/includes/admin/field' ],
	'WH\\GF\\Multicolumn\\Admin\\'        => [ $baseDir . '/includes/admin' ],
	'WH\\GF\\Multicolumn\\'               => [ $baseDir . '/' ],
];
