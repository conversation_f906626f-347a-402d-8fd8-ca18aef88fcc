<?php

/*
 * This file is part of the Predis package.
 *
 * (c) 2009-2020 <PERSON><PERSON> Alessandri
 * (c) 2021-2023 Till <PERSON>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace Predis\Command\Redis\Container;

interface ContainerInterface
{
    /**
     * Creates Redis container command with subcommand as virtual method name
     * and sends a request to the server.
     *
     * @param $subcommandID
     * @param $arguments
     * @return mixed
     */
    public function __call($subcommandID, $arguments);

    /**
     * Returns containerCommandId of specific container command.
     *
     * @return string
     */
    public function getContainerCommandId(): string;
}
