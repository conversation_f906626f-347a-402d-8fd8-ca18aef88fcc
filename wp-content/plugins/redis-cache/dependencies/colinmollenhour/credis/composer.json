{"name": "colinmollenhour/credis", "type": "library", "description": "Credis is a lightweight interface to the Redis key-value store which wraps the phpredis library when available for better performance.", "homepage": "https://github.com/colinmollenhour/credis", "license": "MIT", "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "require": {"php": ">=5.6.0"}, "suggest": {"ext-redis": "Improved performance for communicating with redis"}, "autoload": {"classmap": ["Client.php", "Cluster.php", "Sentinel.php", "Module.php"]}}