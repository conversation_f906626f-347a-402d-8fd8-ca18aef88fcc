=== Opayo Form Payment Gateway for Gravity Forms ===
Contributors: patsatech
Tags: ecommerce, payment gateway, wordpress, gravity forms,Opayo server,Opayo go
Requires at least: 3.5
Tested up to: 6.1.1
Stable tag: 1.1.9
License: GPLv2 or later

Opayo Server Gateway for accepting payments on your Gravity Forms Store.

== Description ==

The Sage Pay Payment system provides a secure, simple means of authorizing credit and debit card transactions from your website.

The Sage Pay system provides a straightforward payment interface for the customer, and takes complete responsibility for the online transaction, including the collection and encrypted storage of credit and debit card details, eliminating the security implications of holding such sensitive information on your own servers.

So this plugin helps you to accept payments with Gravity Forms using Opayo Accounts.
Send us your ideas and feedback here: [https://www.patsatech.com/](https://www.patsatech.com/)

We also do custom payment gateway integrations.

Checkout our Plugins below.

[Easy Digital Downloads Plugins](https://www.patsatech.com/product-category/wordpress/easy-digital-downloads/)

[Gravity Forms Plugins](https://www.patsatech.com/product-category/wordpress/gravity-forms)

[WooCommerce Plugins](https://www.patsatech.com/product-category/wordpress/woocommerce/)
== Installation ==

1. Download and unzip the latest release zip file.
2. If you use the WordPress plugin uploader to install this plugin skip to step 4.
3. Upload the entire plugin directory to your `/wp-content/plugins/` directory.
4. Activate the plugin through the 'Plugins' menu in WordPress Administration.

== Changelog ==

= 1.0.0 =
* Initial Release

= 1.1.0 =
* Completely rewritten to work with latest version of GravityForms plugin.
* Conditional Logic Issues resolved.

= 1.1.1 =
* Have resolved the issue related to "Uncaught SecurityError" during redirecting to Opayo.

= 1.1.2 =
* Minor code changes

= 1.1.3 =
* Updated the tested upto for Wordpress.

= 1.1.4 =
* Updated to support the PHP version 7.0 and above.

= 1.1.5 =
* Updated to resolve issue with Apply3DSecure flag.

= 1.1.6 =
* Edited to send 2 character ISO 3166 country code.

= 1.1.7 =
* Edited to check if the entry is already paid or processing. And resolve 5080 issue due to state being empty.

= 1.1.8 =
* Edited to support the latest version of the Opayo protocol (v4.00).

= 1.1.9 =
* Updated to change branding from Sagepaay to Opayo.