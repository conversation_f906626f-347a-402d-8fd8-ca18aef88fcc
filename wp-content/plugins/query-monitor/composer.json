{"name": "johnbillion/query-monitor", "description": "The Developer Tools panel for WordPress.", "license": "GPL-2.0-or-later", "type": "wordpress-plugin", "authors": [{"name": "<PERSON>", "homepage": "https://johnblackbourn.com/"}], "homepage": "https://github.com/johnbillion/query-monitor/", "support": {"issues": "https://github.com/johnbillion/query-monitor/issues", "forum": "https://wordpress.org/support/plugin/query-monitor", "source": "https://github.com/johnbillion/query-monitor"}, "funding": [{"type": "github", "url": "https://github.com/sponsors/johnbillion"}], "require": {"php": ">=7.4.0", "composer/installers": "^1.0 || ^2.0"}, "require-dev": {"codeception/module-asserts": "^1.0", "codeception/module-db": "^1.0", "codeception/module-webdriver": "^1.0", "codeception/util-universalframework": "^1.0", "dealerdirect/phpcodesniffer-composer-installer": "0.7.2", "johnbillion/plugin-infrastructure": "dev-trunk", "lucatume/wp-browser": "3.2.1", "phpcompatibility/phpcompatibility-wp": "2.1.4", "phpstan/phpstan": "1.10.39", "phpstan/phpstan-phpunit": "1.3.15", "roots/wordpress-core-installer": "1.100.0", "roots/wordpress-full": "*", "squizlabs/php_codesniffer": "3.7.1", "szepeviktor/phpstan-wordpress": "1.3.0", "wp-coding-standards/wpcs": "2.3.0"}, "autoload": {"classmap": ["classes", "data", "output"]}, "autoload-dev": {"psr-4": {"QM\\Tests\\": "tests/integration"}}, "config": {"allow-plugins": {"dealerdirect/phpcodesniffer-composer-installer": true, "roots/wordpress-core-installer": true, "composer/installers": true}, "classmap-authoritative": true, "preferred-install": "dist", "prepend-autoloader": false, "sort-packages": true}, "extra": {"wordpress-install-dir": "vendor/wordpress/wordpress"}, "scripts": {"build-vendor": ["build-vendor"], "test": ["@composer validate --strict --no-check-lock", "@test:phpstan", "@test:phpcs", "@test:start", "@test:integration", "@test:acceptance", "@test:stop"], "test:acceptance": ["npm run build", "acceptance-tests"], "test:destroy": ["tests-destroy"], "test:integration": ["integration-tests"], "test:phpcs": ["phpcs -nps --colors --report-code --report-summary --report-width=80 --cache=tests/cache/phpcs56.json --basepath='./' --standard=phpcs56.xml", "phpcs -nps --colors --report-code --report-summary --report-width=80 --cache=tests/cache/phpcs.json --basepath='./' ."], "test:phpstan": ["codecept build", "phpstan analyze --memory-limit=1024M"], "test:start": ["tests-start"], "test:stop": ["tests-stop"]}}