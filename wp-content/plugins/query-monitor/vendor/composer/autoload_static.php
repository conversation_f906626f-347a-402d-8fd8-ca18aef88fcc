<?php

// autoload_static.php @generated by Composer

namespace Composer\Autoload;

class ComposerStaticInitad25d53a23099da4b0886b4f6754360e
{
    public static $classMap = array (
        'Debug_Bar' => __DIR__ . '/../..' . '/classes/debug_bar.php',
        'Debug_Bar_Panel' => __DIR__ . '/../..' . '/classes/debug_bar_panel.php',
        'QM' => __DIR__ . '/../..' . '/classes/QM.php',
        'QM_Activation' => __DIR__ . '/../..' . '/classes/Activation.php',
        'QM_Backtrace' => __DIR__ . '/../..' . '/classes/Backtrace.php',
        'QM_CLI' => __DIR__ . '/../..' . '/classes/CLI.php',
        'QM_Collector' => __DIR__ . '/../..' . '/classes/Collector.php',
        'QM_Collector_Assets' => __DIR__ . '/../..' . '/classes/Collector_Assets.php',
        'QM_Collectors' => __DIR__ . '/../..' . '/classes/Collectors.php',
        'QM_Component' => __DIR__ . '/../..' . '/classes/Component.php',
        'QM_DB' => __DIR__ . '/../..' . '/classes/DB.php',
        'QM_Data' => __DIR__ . '/../..' . '/classes/Data.php',
        'QM_DataCollector' => __DIR__ . '/../..' . '/classes/DataCollector.php',
        'QM_Data_Admin' => __DIR__ . '/../..' . '/data/admin.php',
        'QM_Data_Assets' => __DIR__ . '/../..' . '/data/assets.php',
        'QM_Data_Block_Editor' => __DIR__ . '/../..' . '/data/block_editor.php',
        'QM_Data_Cache' => __DIR__ . '/../..' . '/data/cache.php',
        'QM_Data_Caps' => __DIR__ . '/../..' . '/data/caps.php',
        'QM_Data_Conditionals' => __DIR__ . '/../..' . '/data/conditionals.php',
        'QM_Data_DB_Callers' => __DIR__ . '/../..' . '/data/db_callers.php',
        'QM_Data_DB_Components' => __DIR__ . '/../..' . '/data/db_components.php',
        'QM_Data_DB_Dupes' => __DIR__ . '/../..' . '/data/db_dupes.php',
        'QM_Data_DB_Queries' => __DIR__ . '/../..' . '/data/db_queries.php',
        'QM_Data_Doing_It_Wrong' => __DIR__ . '/../..' . '/data/doing_it_wrong.php',
        'QM_Data_Environment' => __DIR__ . '/../..' . '/data/environment.php',
        'QM_Data_Fallback' => __DIR__ . '/../..' . '/data/fallback.php',
        'QM_Data_HTTP' => __DIR__ . '/../..' . '/data/http.php',
        'QM_Data_Hooks' => __DIR__ . '/../..' . '/data/hooks.php',
        'QM_Data_Languages' => __DIR__ . '/../..' . '/data/languages.php',
        'QM_Data_Logger' => __DIR__ . '/../..' . '/data/logger.php',
        'QM_Data_Multisite' => __DIR__ . '/../..' . '/data/multisite.php',
        'QM_Data_Overview' => __DIR__ . '/../..' . '/data/overview.php',
        'QM_Data_PHP_Errors' => __DIR__ . '/../..' . '/data/php_errors.php',
        'QM_Data_Raw_Request' => __DIR__ . '/../..' . '/data/raw_request.php',
        'QM_Data_Redirect' => __DIR__ . '/../..' . '/data/redirect.php',
        'QM_Data_Request' => __DIR__ . '/../..' . '/data/request.php',
        'QM_Data_Theme' => __DIR__ . '/../..' . '/data/theme.php',
        'QM_Data_Timing' => __DIR__ . '/../..' . '/data/timing.php',
        'QM_Data_Transients' => __DIR__ . '/../..' . '/data/transients.php',
        'QM_Dispatcher' => __DIR__ . '/../..' . '/classes/Dispatcher.php',
        'QM_Dispatchers' => __DIR__ . '/../..' . '/classes/Dispatchers.php',
        'QM_Hook' => __DIR__ . '/../..' . '/classes/Hook.php',
        'QM_Output' => __DIR__ . '/../..' . '/classes/Output.php',
        'QM_Output_Headers' => __DIR__ . '/../..' . '/output/Headers.php',
        'QM_Output_Headers_Overview' => __DIR__ . '/../..' . '/output/headers/overview.php',
        'QM_Output_Headers_PHP_Errors' => __DIR__ . '/../..' . '/output/headers/php_errors.php',
        'QM_Output_Headers_Redirects' => __DIR__ . '/../..' . '/output/headers/redirects.php',
        'QM_Output_Html' => __DIR__ . '/../..' . '/output/Html.php',
        'QM_Output_Html_Admin' => __DIR__ . '/../..' . '/output/html/admin.php',
        'QM_Output_Html_Assets' => __DIR__ . '/../..' . '/output/html/assets.php',
        'QM_Output_Html_Assets_Scripts' => __DIR__ . '/../..' . '/output/html/assets_scripts.php',
        'QM_Output_Html_Assets_Styles' => __DIR__ . '/../..' . '/output/html/assets_styles.php',
        'QM_Output_Html_Block_Editor' => __DIR__ . '/../..' . '/output/html/block_editor.php',
        'QM_Output_Html_Caps' => __DIR__ . '/../..' . '/output/html/caps.php',
        'QM_Output_Html_Conditionals' => __DIR__ . '/../..' . '/output/html/conditionals.php',
        'QM_Output_Html_DB_Callers' => __DIR__ . '/../..' . '/output/html/db_callers.php',
        'QM_Output_Html_DB_Components' => __DIR__ . '/../..' . '/output/html/db_components.php',
        'QM_Output_Html_DB_Dupes' => __DIR__ . '/../..' . '/output/html/db_dupes.php',
        'QM_Output_Html_DB_Queries' => __DIR__ . '/../..' . '/output/html/db_queries.php',
        'QM_Output_Html_Debug_Bar' => __DIR__ . '/../..' . '/output/html/debug_bar.php',
        'QM_Output_Html_Doing_It_Wrong' => __DIR__ . '/../..' . '/output/html/doing_it_wrong.php',
        'QM_Output_Html_Environment' => __DIR__ . '/../..' . '/output/html/environment.php',
        'QM_Output_Html_HTTP' => __DIR__ . '/../..' . '/output/html/http.php',
        'QM_Output_Html_Headers' => __DIR__ . '/../..' . '/output/html/headers.php',
        'QM_Output_Html_Hooks' => __DIR__ . '/../..' . '/output/html/hooks.php',
        'QM_Output_Html_Languages' => __DIR__ . '/../..' . '/output/html/languages.php',
        'QM_Output_Html_Logger' => __DIR__ . '/../..' . '/output/html/logger.php',
        'QM_Output_Html_Multisite' => __DIR__ . '/../..' . '/output/html/multisite.php',
        'QM_Output_Html_Overview' => __DIR__ . '/../..' . '/output/html/overview.php',
        'QM_Output_Html_PHP_Errors' => __DIR__ . '/../..' . '/output/html/php_errors.php',
        'QM_Output_Html_Request' => __DIR__ . '/../..' . '/output/html/request.php',
        'QM_Output_Html_Theme' => __DIR__ . '/../..' . '/output/html/theme.php',
        'QM_Output_Html_Timing' => __DIR__ . '/../..' . '/output/html/timing.php',
        'QM_Output_Html_Transients' => __DIR__ . '/../..' . '/output/html/transients.php',
        'QM_Output_Raw' => __DIR__ . '/../..' . '/output/Raw.php',
        'QM_Output_Raw_Cache' => __DIR__ . '/../..' . '/output/raw/cache.php',
        'QM_Output_Raw_Conditionals' => __DIR__ . '/../..' . '/output/raw/conditionals.php',
        'QM_Output_Raw_DB_Queries' => __DIR__ . '/../..' . '/output/raw/db_queries.php',
        'QM_Output_Raw_HTTP' => __DIR__ . '/../..' . '/output/raw/http.php',
        'QM_Output_Raw_Logger' => __DIR__ . '/../..' . '/output/raw/logger.php',
        'QM_Output_Raw_Transients' => __DIR__ . '/../..' . '/output/raw/transients.php',
        'QM_PHP' => __DIR__ . '/../..' . '/classes/PHP.php',
        'QM_Plugin' => __DIR__ . '/../..' . '/classes/Plugin.php',
        'QM_Timer' => __DIR__ . '/../..' . '/classes/Timer.php',
        'QM_Util' => __DIR__ . '/../..' . '/classes/Util.php',
        'QueryMonitor' => __DIR__ . '/../..' . '/classes/QueryMonitor.php',
    );

    public static function getInitializer(ClassLoader $loader)
    {
        return \Closure::bind(function () use ($loader) {
            $loader->classMap = ComposerStaticInitad25d53a23099da4b0886b4f6754360e::$classMap;

        }, null, ClassLoader::class);
    }
}
