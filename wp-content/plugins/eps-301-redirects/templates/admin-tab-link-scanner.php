<?php
/**
 *
 * The Link Scanner Tab.
 *
 * The main admin area for the link scanner tab.
 *
 * @package    EPS 301 Redirects
 * <AUTHOR> Ltd
 */

// include only file
if (!defined('ABSPATH')) {
  die('Do not open this file directly.');
}
?>

<div class="wrap">
    <?php do_action('eps_redirects_admin_head'); ?>

    <div class="eps-panel eps-margin-top group">
        <h1>Linking to bad, broken &amp; malware infected sites <b>can make your site bad</b> too, for visitors &amp; for SEO.<br>
        But how do you know you're not linking to bad sites? By using the Link Scanner.</h1>

        <p style="font-size: 14px;">Link Scanner is a unique SaaS tools that's a part of WP 301 Redirects PRO plugin. It'll help you to:</p>
        <ul class="plain-list">
            <li>scan every single link on your site; in posts, pages, CPTs, widgets, header, footers - everywhere</li>
            <li>detect links that are broken &amp; easily fix them</li>
            <li>detect links to pornographic sites, those infected by malware or phishing sites</li>
            <li>check links with Google Safe Browing API</li>
            <li>see <i>rel</i>, <i>target</i> and other link attributes in one place</li>
            <li>get additional data on every link such as the quality of the site it links to</li>
            <li>run automatic scans so you always have fresh data</li>
            <li>scans are done externally by our SaaS so they don't put a strain on your server</li>
        </ul>
<br>
        <p><a href="#" class="open-301-pro-dialog button button-buy" data-pro-feature="link-scanner">Get WP 301 Redirects PRO with Link Scanner</a></p>
    </div>

    <div class="right">
    </div>
    <div class="left">
    </div>
</div>
