window.GFConstantContactSettings=null,function(a){GFConstantContactSettings=function(){var o=this;this.init=function(){this.pageURL=gform_constantcontact_pluginsettings_strings.settings_url,this.bindDeauthorize(),this.bindCustomAppAuthorize()},this.bindCustomAppAuthorize=function(){a("#gform_constantcontact_custom_auth_button").on("click",function(t){t.preventDefault(),o.customAppAuthorize(t)}),a("#custom_app_key, #custom_app_secret").on("keydown",function(t){13===t.keyCode&&(t.preventDefault(),o.customAppAuthorize(t))})},this.customAppAuthorize=function(){var t=a("#custom_app_key").val(),n=a("#custom_app_secret").val();""!==t&&""!==n&&a.ajax({url:ajaxurl,data:{action:"gfconstantcontact_get_auth_url",nonce:gform_constantcontact_pluginsettings_strings.ajax_nonce,custom_app_key:t,custom_app_secret:n},success:function(t){t.success?window.location.href=t.data:window.location.href=o.pageURL+"&auth_error=true"}})},this.bindDeauthorize=function(){a("#gform_constantcontact_deauth_button").on("click",function(t){t.preventDefault();var n=a("#gform_constantcontact_deauth_button");if(!confirm(gform_constantcontact_pluginsettings_strings.disconnect))return!1;n.attr("disabled","disabled"),a.ajax({async:!1,url:ajaxurl,dataType:"json",data:{action:"gfconstantcontact_deauthorize",nonce:gform_constantcontact_pluginsettings_strings.ajax_nonce},success:function(t){t.success?window.location.href=o.pageURL:alert(t.data.message),n.removeAttr("disabled")}})})},this.init()},a(document).ready(GFConstantContactSettings)}(jQuery);