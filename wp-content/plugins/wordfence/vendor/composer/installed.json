{"packages": [{"name": "wordfence/mmdb-reader", "version": "v1.0.0", "version_normalized": "*******", "source": {"type": "git", "url": "**************:wordfence/mmdb-reader.git", "reference": "f72435e75f6654da08c2f0983e527cb207ef1f2a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/wordfence/mmdb-reader/zipball/f72435e75f6654da08c2f0983e527cb207ef1f2a", "reference": "f72435e75f6654da08c2f0983e527cb207ef1f2a", "shasum": ""}, "time": "2022-09-23T20:02:31+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Wordfence\\MmdbReader\\": "src/"}}, "license": ["proprietary"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A MaxMind DB (MMDB) reader with no external dependencies that provides support for a wider range of PHP versions than the official library", "support": {"source": "https://github.com/wordfence/mmdb-reader/tree/v1.0.0", "issues": "https://github.com/wordfence/mmdb-reader/issues"}, "install-path": "../wordfence/mmdb-reader"}, {"name": "wordfence/wf-waf", "version": "1.0.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/wordfence/wf-waf.git", "reference": "origin/master"}, "dist": {"type": "zip", "url": "https://github.com/wordfence/wf-waf/zipball/master", "reference": "origin/master"}, "type": "library", "installation-source": "source", "install-path": "../wordfence/wf-waf"}], "dev": true, "dev-package-names": []}