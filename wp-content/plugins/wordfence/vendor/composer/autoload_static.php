<?php

// autoload_static.php @generated by Composer

namespace Composer\Autoload;

class ComposerStaticInit736008d0fa54169b3444ae0f3fc20155
{
    public static $prefixLengthsPsr4 = array (
        'W' => 
        array (
            'Wordfence\\MmdbReader\\' => 21,
        ),
    );

    public static $prefixDirsPsr4 = array (
        'Wordfence\\MmdbReader\\' => 
        array (
            0 => __DIR__ . '/..' . '/wordfence/mmdb-reader/src',
        ),
    );

    public static $classMap = array (
        'Composer\\InstalledVersions' => __DIR__ . '/..' . '/composer/InstalledVersions.php',
    );

    public static function getInitializer(ClassLoader $loader)
    {
        return \Closure::bind(function () use ($loader) {
            $loader->prefixLengthsPsr4 = ComposerStaticInit736008d0fa54169b3444ae0f3fc20155::$prefixLengthsPsr4;
            $loader->prefixDirsPsr4 = ComposerStaticInit736008d0fa54169b3444ae0f3fc20155::$prefixDirsPsr4;
            $loader->classMap = ComposerStaticInit736008d0fa54169b3444ae0f3fc20155::$classMap;

        }, null, ClassLoader::class);
    }
}
