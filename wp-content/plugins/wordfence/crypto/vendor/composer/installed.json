{"packages": [{"name": "paragonie/random_compat", "version": "v2.0.21", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/paragonie/random_compat.git", "reference": "96c132c7f2f7bc3230723b66e89f8f150b29d5ae"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/paragonie/random_compat/zipball/96c132c7f2f7bc3230723b66e89f8f150b29d5ae", "reference": "96c132c7f2f7bc3230723b66e89f8f150b29d5ae", "shasum": ""}, "require": {"php": ">=5.2.0"}, "require-dev": {"phpunit/phpunit": "*"}, "suggest": {"ext-libsodium": "Provides a modern crypto API that can be used to generate random bytes."}, "time": "2022-02-16T17:07:03+00:00", "type": "library", "installation-source": "dist", "autoload": {"files": ["lib/random.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Paragon Initiative Enterprises", "email": "<EMAIL>", "homepage": "https://paragonie.com"}], "description": "PHP 5.x polyfill for random_bytes() and random_int() from PHP 7", "keywords": ["csprng", "polyfill", "pseudorandom", "random"], "support": {"email": "<EMAIL>", "issues": "https://github.com/paragonie/random_compat/issues", "source": "https://github.com/paragonie/random_compat"}, "install-path": "../paragonie/random_compat"}, {"name": "paragonie/sodium_compat", "version": "v1.20.0", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/paragonie/sodium_compat.git", "reference": "e592a3e06d1fa0d43988c7c7d9948ca836f644b6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/paragonie/sodium_compat/zipball/e592a3e06d1fa0d43988c7c7d9948ca836f644b6", "reference": "e592a3e06d1fa0d43988c7c7d9948ca836f644b6", "shasum": ""}, "require": {"paragonie/random_compat": ">=1", "php": "^5.2.4|^5.3|^5.4|^5.5|^5.6|^7|^8"}, "require-dev": {"phpunit/phpunit": "^3|^4|^5|^6|^7|^8|^9"}, "suggest": {"ext-libsodium": "PHP < 7.0: Better performance, password hashing (Argon2i), secure memory management (memzero), and better security.", "ext-sodium": "PHP >= 7.0: Better performance, password hashing (Argon2i), secure memory management (memzero), and better security."}, "time": "2023-04-30T00:54:53+00:00", "type": "library", "installation-source": "dist", "autoload": {"files": ["autoload.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["ISC"], "authors": [{"name": "Paragon Initiative Enterprises", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Pure PHP implementation of libsodium; uses the PHP extension if it exists", "keywords": ["Authentication", "BLAKE2b", "ChaCha20", "ChaCha20-Poly1305", "Chapoly", "Curve25519", "Ed25519", "EdDSA", "Edwards-curve Digital Signature Algorithm", "Elliptic Curve <PERSON>-<PERSON><PERSON>", "Poly1305", "Pure-PHP cryptography", "RFC 7748", "RFC 8032", "Salpoly", "Salsa20", "X25519", "XChaCha20-Poly1305", "XSalsa20-Poly1305", "Xchacha20", "Xsalsa20", "aead", "cryptography", "ecdh", "elliptic curve", "elliptic curve cryptography", "encryption", "libsodium", "php", "public-key cryptography", "secret-key cryptography", "side-channel resistant"], "support": {"issues": "https://github.com/paragonie/sodium_compat/issues", "source": "https://github.com/paragonie/sodium_compat/tree/v1.20.0"}, "install-path": "../paragonie/sodium_compat"}], "dev": true, "dev-package-names": []}