<?php
if (!defined('WORDFENCE_VERSION')) { exit; }
/**
 * Presents an indeterminate progress indicator.
 *
 * $size may be defined.
 *
 * @var int $size Pixel size to use as the dimensions of the indicator. Defaults to 100.
 */

if (!isset($size)) { $size = 100; }
?>
<svg class="wf-indeterminate-progress" viewBox="0 0 100 100" style="width:<?php echo $size; ?>px;height:<?php echo $size; ?>px;"><path d="M23.057,68.244c-0.094,-0.003 -0.188,-0.004 -0.282,-0.004c-4.751,0 -8.66,3.909 -8.66,8.659c0,4.751 3.909,8.66 8.66,8.66c0.094,0 0.188,-0.002 0.282,-0.005c2.294,0.029 4.503,-0.885 6.105,-2.527c1.634,-1.621 2.554,-3.83 2.554,-6.132c0,-2.302 -0.92,-4.511 -2.554,-6.133c-1.604,-1.639 -3.812,-2.549 -6.105,-2.518l0,0Z" style="fill-rule:nonzero;"/><path d="M21.139,50.012c0.041,-2.559 -0.986,-5.023 -2.831,-6.797c-1.773,-1.84 -4.233,-2.862 -6.788,-2.822c-2.558,-0.043 -5.021,0.98 -6.796,2.822c-1.807,1.798 -2.824,4.244 -2.824,6.792c0,2.549 1.017,4.995 2.824,6.793c1.775,1.842 4.238,2.865 6.796,2.822c2.555,0.04 5.015,-0.983 6.788,-2.822c1.843,-1.772 2.869,-4.232 2.831,-6.788l0,0Z" style="fill-rule:nonzero;"/><path d="M76.915,27.888c2.643,-0.005 4.814,-2.181 4.814,-4.823c0,-0.003 0,-0.007 0,-0.01c0,-0.006 0,-0.013 0,-0.02c0,-2.641 -2.173,-4.814 -4.814,-4.814c-2.641,0 -4.814,2.173 -4.814,4.814c0,1.281 0.512,2.511 1.42,3.414c0.887,0.92 2.117,1.431 3.394,1.411l0,0.028Z" style="fill-rule:nonzero;"/><path d="M23.057,12.505c-2.816,-0.047 -5.529,1.076 -7.488,3.099c-2.015,1.949 -3.134,4.649 -3.09,7.451c-0.049,2.815 1.07,5.528 3.09,7.489c1.96,2.022 4.673,3.144 7.488,3.099c0.003,0 0.006,0 0.01,0c5.797,0 10.568,-4.771 10.568,-10.569c0,-0.006 0,-0.012 0,-0.019c0.052,-2.812 -1.068,-5.523 -3.089,-7.479c-1.958,-2.02 -4.667,-3.143 -7.479,-3.098l-0.01,0.027Z" style="fill-rule:nonzero;"/><path d="M92.547,45.927c-1.091,-1.104 -2.58,-1.726 -4.132,-1.726c-3.187,0 -5.81,2.623 -5.81,5.81c0,3.188 2.623,5.81 5.81,5.81c3.187,0 5.81,-2.622 5.81,-5.809c0.032,-1.535 -0.576,-3.016 -1.678,-4.085Z" style="fill-rule:nonzero;"/><path d="M76.915,70.209c-0.012,0 -0.025,0 -0.037,0c-3.714,0 -6.769,3.055 -6.769,6.769c0,3.713 3.055,6.769 6.769,6.769c3.713,0 6.769,-3.056 6.769,-6.769c0,-1.795 -0.714,-3.518 -1.983,-4.787c-1.239,-1.29 -2.96,-2.008 -4.749,-1.982l0,0Z" style="fill-rule:nonzero;"/><path d="M49.995,80.786c-0.002,0 -0.005,0 -0.007,0c-4.225,0 -7.701,3.477 -7.701,7.701c0,4.224 3.476,7.701 7.701,7.701c4.224,0 7.7,-3.477 7.7,-7.701c0,-2.062 -0.828,-4.04 -2.298,-5.487c-1.423,-1.471 -3.394,-2.289 -5.441,-2.26l0.046,0.046Z" style="fill-rule:nonzero;"/><path d="M49.995,0c-3.073,-0.055 -6.035,1.164 -8.18,3.366c-2.202,2.144 -3.421,5.107 -3.366,8.18c-0.053,3.07 1.166,6.029 3.366,8.171c4.499,4.454 11.852,4.454 16.351,0c2.201,-2.142 3.42,-5.101 3.366,-8.171c0.053,-3.074 -1.17,-6.037 -3.375,-8.18c-2.14,-2.197 -5.095,-3.416 -8.162,-3.366l0,0Z" style="fill-rule:nonzero;"/></svg>
