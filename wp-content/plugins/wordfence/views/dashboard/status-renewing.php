<?php
if (!defined('WORDFENCE_VERSION')) { exit; }
/**
 * Expects $id, $title, $subtitle, and $link, and $linkLabel to be defined.
 * If $linkLabel is null, the link will be hidden.
 * $linkNewWindow can optionally be defined and defaults to false.
 */

if (!isset($linkNewWindow)) { $linkNewWindow = false; }
?>
<div id="<?php echo esc_attr($id); ?>" class="wf-status-detail">
	<div class="wf-status-renewing">
		<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 800 608"><g><path d="M374.4,332.6l9.1,9c1.2,1.2,2.7,1.9,4.5,1.9c1.8,0,3.3-0.6,4.5-1.9l9-9l48.2-48.2c1.2-1.2,1.9-2.8,1.9-4.5c0-1.8-0.6-3.3-1.9-4.5l-9.1-9c-1.2-1.2-2.7-1.9-4.5-1.9c-1.8,0-3.3,0.6-4.5,1.9L388,310.1l-19.6-19.6c-1.2-1.2-2.8-1.9-4.5-1.9c-1.8,0-3.3,0.6-4.5,1.9l-9,9c-1.2,1.2-1.9,2.8-1.9,4.5c0,1.8,0.6,3.3,1.9,4.5L374.4,332.6z"/><path d="M767.7,304.8c-4.5-4.5-10.1-6.8-16.7-6.8l-67.6-0.3c-1-36.2-8.8-71.2-23.1-104.9c-14.4-33.7-34.2-63.2-59.7-88.7c-47.9-47.9-104.2-75.2-168.9-81.9c-64.7-6.7-126,7.6-184,43l-1.8,1.8c-2.3,2.3-3.4,5-3.4,8.4c0,3.3,1.1,6.1,3.4,8.3l52,52c3.8,3.8,8.4,4.4,13.8,1.8c24.7-10.1,39.5-15.7,44.4-16.7c32.1-7.7,63.8-7,95.3,1.8c31.5,8.9,59,25,82.3,48.3c35,35,53.5,77.3,55.4,126.9l-72.1,0c-6.6,0-12.2,2.3-16.7,6.8c-4.5,4.5-6.8,10.1-6.8,16.7c0,6.6,2.3,12.2,6.8,16.7l117,117c4.5,4.5,10.1,6.8,16.7,6.8c6.6,0,12.2-2.3,16.7-6.8l117-117c4.5-4.5,6.8-10.1,6.8-16.7C774.5,314.9,772.3,309.4,767.7,304.8z"/><path d="M502.1,473.8c-3.8-3.8-8.5-4.4-13.8-1.8c-24.7,10.1-39.5,15.7-44.4,16.7c-32,7.7-63.8,7.1-95.3-1.8c-31.5-8.9-58.9-25-82.3-48.3c-16.9-16.9-30.1-36.3-39.5-58.2c-9.4-21.9-14.5-44.8-15.4-68.7l71.5,0c6.6,0,12.2-2.3,16.7-6.8c4.5-4.5,6.8-10.1,6.8-16.7c0-6.6-2.3-12.2-6.8-16.7l-117-117c-4.5-4.5-10.1-6.8-16.7-6.8c-6.6,0-12.2,2.3-16.7,6.8l-117,117c-4.5,4.5-6.8,10.1-6.8,16.7c0,6.6,2.3,12.2,6.8,16.7c4.5,4.5,10.1,6.8,16.7,6.8l67.4,0c0.9,36.4,8.4,71.3,22.6,104.6c14.2,33.4,34,62.7,59.4,88.1c47.7,47.7,103.8,74.9,168.3,81.4c64.5,6.5,125.7-8,183.5-43.5c0.5-0.2,1.2-0.7,2.1-1.6c2.3-2.3,3.4-5.1,3.4-8.4c0-3.3-1.1-6.1-3.4-8.4L502.1,473.8z"/></g></svg>
	</div>
	<p class="wf-status-detail-title"><?php echo esc_html($title); ?></p>
	<p class="wf-status-detail-subtitle"><?php echo esc_html($subtitle); ?></p>
	<p class="wf-status-detail-link"><?php if ($linkLabel !== null): ?><a href="<?php echo esc_attr($link); ?>"<?php echo ($linkNewWindow ? ' target="_blank" rel="noopener noreferrer"' : ''); ?>><?php echo esc_html($linkLabel); ?><span class="screen-reader-text"> (<?php esc_html_e('opens in new tab', 'wordfence') ?>)</span></a><?php endif; ?></p>
</div>
