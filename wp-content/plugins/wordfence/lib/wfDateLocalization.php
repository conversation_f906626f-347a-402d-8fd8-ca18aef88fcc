<?php
class wfDateLocalization {
	static $localizations = array (
  'af-ZA' => 'Date.CultureInfo={name:"af-ZA",englishName:"Afrikaans (South Africa)",nativeName:"Afrikaans (Suid Afrika)",dayNames:["Sondag","<PERSON>andag","Dinsdag","Woensdag","Donderdag","Vrydag","Saterdag"],abbreviatedDayNames:["<PERSON>","<PERSON><PERSON>","<PERSON><PERSON>","<PERSON>oe<PERSON>","Dond","<PERSON><PERSON>","Sat"],shortestDayNames:["So","Ma","Di","Wo","Do","Vr","Sa"],firstLetterDayNames:["S","M","D","W","D","V","S"],monthNames:["<PERSON><PERSON>rie","<PERSON>ruarie","Ma<PERSON>","April","<PERSON>","<PERSON><PERSON>","<PERSON>","<PERSON>","September","<PERSON><PERSON><PERSON>","November","<PERSON><PERSON><PERSON>"],abbreviatedMonthNames:["Jan","Feb","<PERSON>","Apr","<PERSON>","<PERSON>","Jul","Aug","Sep","Okt","Nov","Des"],amDesignator:"",pmDesignator:"nm",firstDayOfWeek:0,twoDigitYearMax:2029,dateElementOrder:"ymd",formatPatterns:{shortDate:"yyyy/MM/dd",longDate:"dd MMMM yyyy",shortTime:"hh:mm tt",longTime:"hh:mm:ss tt",fullDateTime:"dd MMMM yyyy hh:mm:ss tt",sortableDateTime:"yyyy-MM-ddTHH:mm:ss",universalSortableDateTime:"yyyy-MM-dd HH:mm:ssZ",rfc1123:"ddd, dd MMM yyyy HH:mm:ss GMT",monthDay:"dd MMMM",yearMonth:"MMMM yyyy"},regexPatterns:{jan:/^jan(uarie)?/i,feb:/^feb(ruarie)?/i,mar:/^maart/i,apr:/^apr(il)?/i,may:/^mei/i,jun:/^jun(ie)?/i,jul:/^jul(ie)?/i,aug:/^aug(ustus)?/i,sep:/^sep(t(ember)?)?/i,oct:/^okt(ober)?/i,nov:/^nov(ember)?/i,dec:/^des(ember)?/i,sun:/^so(n(dag)?)?/i,mon:/^ma(an(dag)?)?/i,tue:/^di(ns(dag)?)?/i,wed:/^wo(en(sdag)?)?/i,thu:/^do(nd(erdag)?)?/i,fri:/^vr(y(dag)?)?/i,sat:/^sa(t(erdag)?)?/i,future:/^next/i,past:/^last|past|prev(ious)?/i,add:/^(\\+|after|from)/i,subtract:/^(\\-|before|ago)/i,yesterday:/^yesterday/i,today:/^t(oday)?/i,tomorrow:/^tomorrow/i,now:/^n(ow)?/i,millisecond:/^ms|milli(second)?s?/i,second:/^sec(ond)?s?/i,minute:/^min(ute)?s?/i,hour:/^h(ou)?rs?/i,week:/^w(ee)?k/i,month:/^m(o(nth)?s?)?/i,day:/^d(ays?)?/i,year:/^y((ea)?rs?)?/i,shortMeridian:/^(a|p)/i,longMeridian:/^(a\\.?m?\\.?|p\\.?m?\\.?)/i,timezone:/^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt)/i,ordinalSuffix:/^\\s*(st|nd|rd|th)/i,timeContext:/^\\s*(\\:|a|p)/i},abbreviatedTimeZoneStandard:{GMT:"-000",EST:"-0400",CST:"-0500",MST:"-0600",PST:"-0700"},abbreviatedTimeZoneDST:{GMT:"-000",EDT:"-0500",CDT:"-0600",MDT:"-0700",PDT:"-0800"}};
',
  'ar-AE' => 'Date.CultureInfo={name:"ar-AE",englishName:"Arabic (U.A.E.)",nativeName:"العربية (الإمارات العربية المتحدة)",dayNames:["الاحد","الاثنين","الثلاثاء","الاربعاء","الخميس","الجمعة","السبت"],abbreviatedDayNames:["الاحد","الاثنين","الثلاثاء","الاربعاء","الخميس","الجمعة","السبت"],shortestDayNames:["أ","ا","ث","أ","خ","ج","س"],firstLetterDayNames:["أ","ا","ث","أ","خ","ج","س"],monthNames:["يناير","فبراير","مارس","ابريل","مايو","يونيو","يوليو","اغسطس","سبتمبر","اكتوبر","نوفمبر","ديسمبر"],abbreviatedMonthNames:["يناير","فبراير","مارس","ابريل","مايو","يونيو","يوليو","اغسطس","سبتمبر","اكتوبر","نوفمبر","ديسمبر"],amDesignator:"ص",pmDesignator:"م",firstDayOfWeek:6,twoDigitYearMax:2029,dateElementOrder:"dmy",formatPatterns:{shortDate:"dd/MM/yyyy",longDate:"dd MMMM, yyyy",shortTime:"hh:mm tt",longTime:"hh:mm:ss tt",fullDateTime:"dd MMMM, yyyy hh:mm:ss tt",sortableDateTime:"yyyy-MM-ddTHH:mm:ss",universalSortableDateTime:"yyyy-MM-dd HH:mm:ssZ",rfc1123:"ddd, dd MMM yyyy HH:mm:ss GMT",monthDay:"dd MMMM",yearMonth:"MMMM, yyyy"},regexPatterns:{jan:/^يناير/i,feb:/^فبراير/i,mar:/^مارس/i,apr:/^ابريل/i,may:/^مايو/i,jun:/^يونيو/i,jul:/^يوليو/i,aug:/^اغسطس/i,sep:/^سبتمبر/i,oct:/^اكتوبر/i,nov:/^نوفمبر/i,dec:/^ديسمبر/i,sun:/^الاحد/i,mon:/^ا(1)?/i,tue:/^الثلاثاء/i,wed:/^الاربعاء/i,thu:/^الخميس/i,fri:/^الجمعة/i,sat:/^السبت/i,future:/^next/i,past:/^last|past|prev(ious)?/i,add:/^(\\+|after|from)/i,subtract:/^(\\-|before|ago)/i,yesterday:/^yesterday/i,today:/^t(oday)?/i,tomorrow:/^tomorrow/i,now:/^n(ow)?/i,millisecond:/^ms|milli(second)?s?/i,second:/^sec(ond)?s?/i,minute:/^min(ute)?s?/i,hour:/^h(ou)?rs?/i,week:/^w(ee)?k/i,month:/^m(o(nth)?s?)?/i,day:/^d(ays?)?/i,year:/^y((ea)?rs?)?/i,shortMeridian:/^(a|p)/i,longMeridian:/^(a\\.?m?\\.?|p\\.?m?\\.?)/i,timezone:/^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt)/i,ordinalSuffix:/^\\s*(st|nd|rd|th)/i,timeContext:/^\\s*(\\:|a|p)/i},abbreviatedTimeZoneStandard:{GMT:"-000",EST:"-0400",CST:"-0500",MST:"-0600",PST:"-0700"},abbreviatedTimeZoneDST:{GMT:"-000",EDT:"-0500",CDT:"-0600",MDT:"-0700",PDT:"-0800"}};
',
  'ar-BH' => 'Date.CultureInfo={name:"ar-BH",englishName:"Arabic (Bahrain)",nativeName:"العربية (البحرين)",dayNames:["الاحد","الاثنين","الثلاثاء","الاربعاء","الخميس","الجمعة","السبت"],abbreviatedDayNames:["الاحد","الاثنين","الثلاثاء","الاربعاء","الخميس","الجمعة","السبت"],shortestDayNames:["أ","ا","ث","أ","خ","ج","س"],firstLetterDayNames:["أ","ا","ث","أ","خ","ج","س"],monthNames:["يناير","فبراير","مارس","ابريل","مايو","يونيو","يوليو","اغسطس","سبتمبر","اكتوبر","نوفمبر","ديسمبر"],abbreviatedMonthNames:["يناير","فبراير","مارس","ابريل","مايو","يونيو","يوليو","اغسطس","سبتمبر","اكتوبر","نوفمبر","ديسمبر"],amDesignator:"ص",pmDesignator:"م",firstDayOfWeek:6,twoDigitYearMax:2029,dateElementOrder:"dmy",formatPatterns:{shortDate:"dd/MM/yyyy",longDate:"dd MMMM, yyyy",shortTime:"hh:mm tt",longTime:"hh:mm:ss tt",fullDateTime:"dd MMMM, yyyy hh:mm:ss tt",sortableDateTime:"yyyy-MM-ddTHH:mm:ss",universalSortableDateTime:"yyyy-MM-dd HH:mm:ssZ",rfc1123:"ddd, dd MMM yyyy HH:mm:ss GMT",monthDay:"dd MMMM",yearMonth:"MMMM, yyyy"},regexPatterns:{jan:/^يناير/i,feb:/^فبراير/i,mar:/^مارس/i,apr:/^ابريل/i,may:/^مايو/i,jun:/^يونيو/i,jul:/^يوليو/i,aug:/^اغسطس/i,sep:/^سبتمبر/i,oct:/^اكتوبر/i,nov:/^نوفمبر/i,dec:/^ديسمبر/i,sun:/^الاحد/i,mon:/^ا(1)?/i,tue:/^الثلاثاء/i,wed:/^الاربعاء/i,thu:/^الخميس/i,fri:/^الجمعة/i,sat:/^السبت/i,future:/^next/i,past:/^last|past|prev(ious)?/i,add:/^(\\+|after|from)/i,subtract:/^(\\-|before|ago)/i,yesterday:/^yesterday/i,today:/^t(oday)?/i,tomorrow:/^tomorrow/i,now:/^n(ow)?/i,millisecond:/^ms|milli(second)?s?/i,second:/^sec(ond)?s?/i,minute:/^min(ute)?s?/i,hour:/^h(ou)?rs?/i,week:/^w(ee)?k/i,month:/^m(o(nth)?s?)?/i,day:/^d(ays?)?/i,year:/^y((ea)?rs?)?/i,shortMeridian:/^(a|p)/i,longMeridian:/^(a\\.?m?\\.?|p\\.?m?\\.?)/i,timezone:/^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt)/i,ordinalSuffix:/^\\s*(st|nd|rd|th)/i,timeContext:/^\\s*(\\:|a|p)/i},abbreviatedTimeZoneStandard:{GMT:"-000",EST:"-0400",CST:"-0500",MST:"-0600",PST:"-0700"},abbreviatedTimeZoneDST:{GMT:"-000",EDT:"-0500",CDT:"-0600",MDT:"-0700",PDT:"-0800"}};
',
  'ar-DZ' => 'Date.CultureInfo={name:"ar-DZ",englishName:"Arabic (Algeria)",nativeName:"العربية (الجزائر)",dayNames:["الاحد","الاثنين","الثلاثاء","الاربعاء","الخميس","الجمعة","السبت"],abbreviatedDayNames:["الاحد","الاثنين","الثلاثاء","الاربعاء","الخميس","الجمعة","السبت"],shortestDayNames:["أ","ا","ث","أ","خ","ج","س"],firstLetterDayNames:["أ","ا","ث","أ","خ","ج","س"],monthNames:["جانفييه","فيفرييه","مارس","أفريل","مي","جوان","جوييه","أوت","سبتمبر","اكتوبر","نوفمبر","ديسمبر"],abbreviatedMonthNames:["جانفييه","فيفرييه","مارس","أفريل","مي","جوان","جوييه","أوت","سبتمبر","اكتوبر","نوفمبر","ديسمبر"],amDesignator:"ص",pmDesignator:"م",firstDayOfWeek:6,twoDigitYearMax:2029,dateElementOrder:"dmy",formatPatterns:{shortDate:"dd-MM-yyyy",longDate:"dd MMMM, yyyy",shortTime:"H:mm",longTime:"H:mm:ss",fullDateTime:"dd MMMM, yyyy H:mm:ss",sortableDateTime:"yyyy-MM-ddTHH:mm:ss",universalSortableDateTime:"yyyy-MM-dd HH:mm:ssZ",rfc1123:"ddd, dd MMM yyyy HH:mm:ss GMT",monthDay:"dd MMMM",yearMonth:"MMMM, yyyy"},regexPatterns:{jan:/^جانفييه/i,feb:/^فيفرييه/i,mar:/^مارس/i,apr:/^أفريل/i,may:/^مي/i,jun:/^جوان/i,jul:/^جوييه/i,aug:/^أوت/i,sep:/^سبتمبر/i,oct:/^اكتوبر/i,nov:/^نوفمبر/i,dec:/^ديسمبر/i,sun:/^الاحد/i,mon:/^ا(1)?/i,tue:/^الثلاثاء/i,wed:/^الاربعاء/i,thu:/^الخميس/i,fri:/^الجمعة/i,sat:/^السبت/i,future:/^next/i,past:/^last|past|prev(ious)?/i,add:/^(\\+|after|from)/i,subtract:/^(\\-|before|ago)/i,yesterday:/^yesterday/i,today:/^t(oday)?/i,tomorrow:/^tomorrow/i,now:/^n(ow)?/i,millisecond:/^ms|milli(second)?s?/i,second:/^sec(ond)?s?/i,minute:/^min(ute)?s?/i,hour:/^h(ou)?rs?/i,week:/^w(ee)?k/i,month:/^m(o(nth)?s?)?/i,day:/^d(ays?)?/i,year:/^y((ea)?rs?)?/i,shortMeridian:/^(a|p)/i,longMeridian:/^(a\\.?m?\\.?|p\\.?m?\\.?)/i,timezone:/^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt)/i,ordinalSuffix:/^\\s*(st|nd|rd|th)/i,timeContext:/^\\s*(\\:|a|p)/i},abbreviatedTimeZoneStandard:{GMT:"-000",EST:"-0400",CST:"-0500",MST:"-0600",PST:"-0700"},abbreviatedTimeZoneDST:{GMT:"-000",EDT:"-0500",CDT:"-0600",MDT:"-0700",PDT:"-0800"}};
',
  'ar-EG' => 'Date.CultureInfo={name:"ar-EG",englishName:"Arabic (Egypt)",nativeName:"العربية (مصر)",dayNames:["الاحد","الاثنين","الثلاثاء","الاربعاء","الخميس","الجمعة","السبت"],abbreviatedDayNames:["الاحد","الاثنين","الثلاثاء","الاربعاء","الخميس","الجمعة","السبت"],shortestDayNames:["أ","ا","ث","أ","خ","ج","س"],firstLetterDayNames:["أ","ا","ث","أ","خ","ج","س"],monthNames:["يناير","فبراير","مارس","ابريل","مايو","يونيو","يوليو","اغسطس","سبتمبر","اكتوبر","نوفمبر","ديسمبر"],abbreviatedMonthNames:["يناير","فبراير","مارس","ابريل","مايو","يونيو","يوليو","اغسطس","سبتمبر","اكتوبر","نوفمبر","ديسمبر"],amDesignator:"ص",pmDesignator:"م",firstDayOfWeek:6,twoDigitYearMax:2029,dateElementOrder:"dmy",formatPatterns:{shortDate:"dd/MM/yyyy",longDate:"dd MMMM, yyyy",shortTime:"hh:mm tt",longTime:"hh:mm:ss tt",fullDateTime:"dd MMMM, yyyy hh:mm:ss tt",sortableDateTime:"yyyy-MM-ddTHH:mm:ss",universalSortableDateTime:"yyyy-MM-dd HH:mm:ssZ",rfc1123:"ddd, dd MMM yyyy HH:mm:ss GMT",monthDay:"dd MMMM",yearMonth:"MMMM, yyyy"},regexPatterns:{jan:/^يناير/i,feb:/^فبراير/i,mar:/^مارس/i,apr:/^ابريل/i,may:/^مايو/i,jun:/^يونيو/i,jul:/^يوليو/i,aug:/^اغسطس/i,sep:/^سبتمبر/i,oct:/^اكتوبر/i,nov:/^نوفمبر/i,dec:/^ديسمبر/i,sun:/^الاحد/i,mon:/^ا(1)?/i,tue:/^الثلاثاء/i,wed:/^الاربعاء/i,thu:/^الخميس/i,fri:/^الجمعة/i,sat:/^السبت/i,future:/^next/i,past:/^last|past|prev(ious)?/i,add:/^(\\+|after|from)/i,subtract:/^(\\-|before|ago)/i,yesterday:/^yesterday/i,today:/^t(oday)?/i,tomorrow:/^tomorrow/i,now:/^n(ow)?/i,millisecond:/^ms|milli(second)?s?/i,second:/^sec(ond)?s?/i,minute:/^min(ute)?s?/i,hour:/^h(ou)?rs?/i,week:/^w(ee)?k/i,month:/^m(o(nth)?s?)?/i,day:/^d(ays?)?/i,year:/^y((ea)?rs?)?/i,shortMeridian:/^(a|p)/i,longMeridian:/^(a\\.?m?\\.?|p\\.?m?\\.?)/i,timezone:/^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt)/i,ordinalSuffix:/^\\s*(st|nd|rd|th)/i,timeContext:/^\\s*(\\:|a|p)/i},abbreviatedTimeZoneStandard:{GMT:"-000",EST:"-0400",CST:"-0500",MST:"-0600",PST:"-0700"},abbreviatedTimeZoneDST:{GMT:"-000",EDT:"-0500",CDT:"-0600",MDT:"-0700",PDT:"-0800"}};
',
  'ar-IQ' => 'Date.CultureInfo={name:"ar-IQ",englishName:"Arabic (Iraq)",nativeName:"العربية (العراق)",dayNames:["الاحد","الاثنين","الثلاثاء","الاربعاء","الخميس","الجمعة","السبت"],abbreviatedDayNames:["الاحد","الاثنين","الثلاثاء","الاربعاء","الخميس","الجمعة","السبت"],shortestDayNames:["أ","ا","ث","أ","خ","ج","س"],firstLetterDayNames:["أ","ا","ث","أ","خ","ج","س"],monthNames:["كانون الثاني","شباط","آذار","نيسان","أيار","حزيران","تموز","آب","أيلول","تشرين الأول","تشرين الثاني","كانون الأول"],abbreviatedMonthNames:["كانون الثاني","شباط","آذار","نيسان","أيار","حزيران","تموز","آب","أيلول","تشرين الأول","تشرين الثاني","كانون الأول"],amDesignator:"ص",pmDesignator:"م",firstDayOfWeek:6,twoDigitYearMax:2029,dateElementOrder:"dmy",formatPatterns:{shortDate:"dd/MM/yyyy",longDate:"dd MMMM, yyyy",shortTime:"hh:mm tt",longTime:"hh:mm:ss tt",fullDateTime:"dd MMMM, yyyy hh:mm:ss tt",sortableDateTime:"yyyy-MM-ddTHH:mm:ss",universalSortableDateTime:"yyyy-MM-dd HH:mm:ssZ",rfc1123:"ddd, dd MMM yyyy HH:mm:ss GMT",monthDay:"dd MMMM",yearMonth:"MMMM, yyyy"},regexPatterns:{jan:/^كانون الثاني/i,feb:/^شباط/i,mar:/^آذار/i,apr:/^نيسان/i,may:/^أيار/i,jun:/^حزيران/i,jul:/^تموز/i,aug:/^آب/i,sep:/^أيلول/i,oct:/^تشرين الأول/i,nov:/^تشرين الثاني/i,dec:/^كانون الأول/i,sun:/^الاحد/i,mon:/^ا(1)?/i,tue:/^الثلاثاء/i,wed:/^الاربعاء/i,thu:/^الخميس/i,fri:/^الجمعة/i,sat:/^السبت/i,future:/^next/i,past:/^last|past|prev(ious)?/i,add:/^(\\+|after|from)/i,subtract:/^(\\-|before|ago)/i,yesterday:/^yesterday/i,today:/^t(oday)?/i,tomorrow:/^tomorrow/i,now:/^n(ow)?/i,millisecond:/^ms|milli(second)?s?/i,second:/^sec(ond)?s?/i,minute:/^min(ute)?s?/i,hour:/^h(ou)?rs?/i,week:/^w(ee)?k/i,month:/^m(o(nth)?s?)?/i,day:/^d(ays?)?/i,year:/^y((ea)?rs?)?/i,shortMeridian:/^(a|p)/i,longMeridian:/^(a\\.?m?\\.?|p\\.?m?\\.?)/i,timezone:/^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt)/i,ordinalSuffix:/^\\s*(st|nd|rd|th)/i,timeContext:/^\\s*(\\:|a|p)/i},abbreviatedTimeZoneStandard:{GMT:"-000",EST:"-0400",CST:"-0500",MST:"-0600",PST:"-0700"},abbreviatedTimeZoneDST:{GMT:"-000",EDT:"-0500",CDT:"-0600",MDT:"-0700",PDT:"-0800"}};
',
  'ar-JO' => 'Date.CultureInfo={name:"ar-JO",englishName:"Arabic (Jordan)",nativeName:"العربية (الأردن)",dayNames:["الاحد","الاثنين","الثلاثاء","الاربعاء","الخميس","الجمعة","السبت"],abbreviatedDayNames:["الاحد","الاثنين","الثلاثاء","الاربعاء","الخميس","الجمعة","السبت"],shortestDayNames:["أ","ا","ث","أ","خ","ج","س"],firstLetterDayNames:["أ","ا","ث","أ","خ","ج","س"],monthNames:["كانون الثاني","شباط","آذار","نيسان","أيار","حزيران","تموز","آب","أيلول","تشرين الأول","تشرين الثاني","كانون الأول"],abbreviatedMonthNames:["كانون الثاني","شباط","آذار","نيسان","أيار","حزيران","تموز","آب","أيلول","تشرين الأول","تشرين الثاني","كانون الأول"],amDesignator:"ص",pmDesignator:"م",firstDayOfWeek:6,twoDigitYearMax:2029,dateElementOrder:"dmy",formatPatterns:{shortDate:"dd/MM/yyyy",longDate:"dd MMMM, yyyy",shortTime:"hh:mm tt",longTime:"hh:mm:ss tt",fullDateTime:"dd MMMM, yyyy hh:mm:ss tt",sortableDateTime:"yyyy-MM-ddTHH:mm:ss",universalSortableDateTime:"yyyy-MM-dd HH:mm:ssZ",rfc1123:"ddd, dd MMM yyyy HH:mm:ss GMT",monthDay:"dd MMMM",yearMonth:"MMMM, yyyy"},regexPatterns:{jan:/^كانون الثاني/i,feb:/^شباط/i,mar:/^آذار/i,apr:/^نيسان/i,may:/^أيار/i,jun:/^حزيران/i,jul:/^تموز/i,aug:/^آب/i,sep:/^أيلول/i,oct:/^تشرين الأول/i,nov:/^تشرين الثاني/i,dec:/^كانون الأول/i,sun:/^الاحد/i,mon:/^ا(1)?/i,tue:/^الثلاثاء/i,wed:/^الاربعاء/i,thu:/^الخميس/i,fri:/^الجمعة/i,sat:/^السبت/i,future:/^next/i,past:/^last|past|prev(ious)?/i,add:/^(\\+|after|from)/i,subtract:/^(\\-|before|ago)/i,yesterday:/^yesterday/i,today:/^t(oday)?/i,tomorrow:/^tomorrow/i,now:/^n(ow)?/i,millisecond:/^ms|milli(second)?s?/i,second:/^sec(ond)?s?/i,minute:/^min(ute)?s?/i,hour:/^h(ou)?rs?/i,week:/^w(ee)?k/i,month:/^m(o(nth)?s?)?/i,day:/^d(ays?)?/i,year:/^y((ea)?rs?)?/i,shortMeridian:/^(a|p)/i,longMeridian:/^(a\\.?m?\\.?|p\\.?m?\\.?)/i,timezone:/^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt)/i,ordinalSuffix:/^\\s*(st|nd|rd|th)/i,timeContext:/^\\s*(\\:|a|p)/i},abbreviatedTimeZoneStandard:{GMT:"-000",EST:"-0400",CST:"-0500",MST:"-0600",PST:"-0700"},abbreviatedTimeZoneDST:{GMT:"-000",EDT:"-0500",CDT:"-0600",MDT:"-0700",PDT:"-0800"}};
',
  'ar-KW' => 'Date.CultureInfo={name:"ar-KW",englishName:"Arabic (Kuwait)",nativeName:"العربية (الكويت)",dayNames:["الاحد","الاثنين","الثلاثاء","الاربعاء","الخميس","الجمعة","السبت"],abbreviatedDayNames:["الاحد","الاثنين","الثلاثاء","الاربعاء","الخميس","الجمعة","السبت"],shortestDayNames:["أ","ا","ث","أ","خ","ج","س"],firstLetterDayNames:["أ","ا","ث","أ","خ","ج","س"],monthNames:["يناير","فبراير","مارس","ابريل","مايو","يونيو","يوليو","اغسطس","سبتمبر","اكتوبر","نوفمبر","ديسمبر"],abbreviatedMonthNames:["يناير","فبراير","مارس","ابريل","مايو","يونيو","يوليو","اغسطس","سبتمبر","اكتوبر","نوفمبر","ديسمبر"],amDesignator:"ص",pmDesignator:"م",firstDayOfWeek:6,twoDigitYearMax:2029,dateElementOrder:"dmy",formatPatterns:{shortDate:"dd/MM/yyyy",longDate:"dd MMMM, yyyy",shortTime:"hh:mm tt",longTime:"hh:mm:ss tt",fullDateTime:"dd MMMM, yyyy hh:mm:ss tt",sortableDateTime:"yyyy-MM-ddTHH:mm:ss",universalSortableDateTime:"yyyy-MM-dd HH:mm:ssZ",rfc1123:"ddd, dd MMM yyyy HH:mm:ss GMT",monthDay:"dd MMMM",yearMonth:"MMMM, yyyy"},regexPatterns:{jan:/^يناير/i,feb:/^فبراير/i,mar:/^مارس/i,apr:/^ابريل/i,may:/^مايو/i,jun:/^يونيو/i,jul:/^يوليو/i,aug:/^اغسطس/i,sep:/^سبتمبر/i,oct:/^اكتوبر/i,nov:/^نوفمبر/i,dec:/^ديسمبر/i,sun:/^الاحد/i,mon:/^ا(1)?/i,tue:/^الثلاثاء/i,wed:/^الاربعاء/i,thu:/^الخميس/i,fri:/^الجمعة/i,sat:/^السبت/i,future:/^next/i,past:/^last|past|prev(ious)?/i,add:/^(\\+|after|from)/i,subtract:/^(\\-|before|ago)/i,yesterday:/^yesterday/i,today:/^t(oday)?/i,tomorrow:/^tomorrow/i,now:/^n(ow)?/i,millisecond:/^ms|milli(second)?s?/i,second:/^sec(ond)?s?/i,minute:/^min(ute)?s?/i,hour:/^h(ou)?rs?/i,week:/^w(ee)?k/i,month:/^m(o(nth)?s?)?/i,day:/^d(ays?)?/i,year:/^y((ea)?rs?)?/i,shortMeridian:/^(a|p)/i,longMeridian:/^(a\\.?m?\\.?|p\\.?m?\\.?)/i,timezone:/^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt)/i,ordinalSuffix:/^\\s*(st|nd|rd|th)/i,timeContext:/^\\s*(\\:|a|p)/i},abbreviatedTimeZoneStandard:{GMT:"-000",EST:"-0400",CST:"-0500",MST:"-0600",PST:"-0700"},abbreviatedTimeZoneDST:{GMT:"-000",EDT:"-0500",CDT:"-0600",MDT:"-0700",PDT:"-0800"}};
',
  'ar-LB' => 'Date.CultureInfo={name:"ar-LB",englishName:"Arabic (Lebanon)",nativeName:"العربية (لبنان)",dayNames:["الاحد","الاثنين","الثلاثاء","الاربعاء","الخميس","الجمعة","السبت"],abbreviatedDayNames:["الاحد","الاثنين","الثلاثاء","الاربعاء","الخميس","الجمعة","السبت"],shortestDayNames:["أ","ا","ث","أ","خ","ج","س"],firstLetterDayNames:["أ","ا","ث","أ","خ","ج","س"],monthNames:["كانون الثاني","شباط","آذار","نيسان","أيار","حزيران","تموز","آب","أيلول","تشرين الأول","تشرين الثاني","كانون الأول"],abbreviatedMonthNames:["كانون الثاني","شباط","آذار","نيسان","أيار","حزيران","تموز","آب","أيلول","تشرين الأول","تشرين الثاني","كانون الأول"],amDesignator:"ص",pmDesignator:"م",firstDayOfWeek:1,twoDigitYearMax:2029,dateElementOrder:"dmy",formatPatterns:{shortDate:"dd/MM/yyyy",longDate:"dd MMMM, yyyy",shortTime:"hh:mm tt",longTime:"hh:mm:ss tt",fullDateTime:"dd MMMM, yyyy hh:mm:ss tt",sortableDateTime:"yyyy-MM-ddTHH:mm:ss",universalSortableDateTime:"yyyy-MM-dd HH:mm:ssZ",rfc1123:"ddd, dd MMM yyyy HH:mm:ss GMT",monthDay:"dd MMMM",yearMonth:"MMMM, yyyy"},regexPatterns:{jan:/^كانون الثاني/i,feb:/^شباط/i,mar:/^آذار/i,apr:/^نيسان/i,may:/^أيار/i,jun:/^حزيران/i,jul:/^تموز/i,aug:/^آب/i,sep:/^أيلول/i,oct:/^تشرين الأول/i,nov:/^تشرين الثاني/i,dec:/^كانون الأول/i,sun:/^الاحد/i,mon:/^ا(1)?/i,tue:/^الثلاثاء/i,wed:/^الاربعاء/i,thu:/^الخميس/i,fri:/^الجمعة/i,sat:/^السبت/i,future:/^next/i,past:/^last|past|prev(ious)?/i,add:/^(\\+|after|from)/i,subtract:/^(\\-|before|ago)/i,yesterday:/^yesterday/i,today:/^t(oday)?/i,tomorrow:/^tomorrow/i,now:/^n(ow)?/i,millisecond:/^ms|milli(second)?s?/i,second:/^sec(ond)?s?/i,minute:/^min(ute)?s?/i,hour:/^h(ou)?rs?/i,week:/^w(ee)?k/i,month:/^m(o(nth)?s?)?/i,day:/^d(ays?)?/i,year:/^y((ea)?rs?)?/i,shortMeridian:/^(a|p)/i,longMeridian:/^(a\\.?m?\\.?|p\\.?m?\\.?)/i,timezone:/^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt)/i,ordinalSuffix:/^\\s*(st|nd|rd|th)/i,timeContext:/^\\s*(\\:|a|p)/i},abbreviatedTimeZoneStandard:{GMT:"-000",EST:"-0400",CST:"-0500",MST:"-0600",PST:"-0700"},abbreviatedTimeZoneDST:{GMT:"-000",EDT:"-0500",CDT:"-0600",MDT:"-0700",PDT:"-0800"}};
',
  'ar-LY' => 'Date.CultureInfo={name:"ar-LY",englishName:"Arabic (Libya)",nativeName:"العربية (ليبيا)",dayNames:["الاحد","الاثنين","الثلاثاء","الاربعاء","الخميس","الجمعة","السبت"],abbreviatedDayNames:["الاحد","الاثنين","الثلاثاء","الاربعاء","الخميس","الجمعة","السبت"],shortestDayNames:["أ","ا","ث","أ","خ","ج","س"],firstLetterDayNames:["أ","ا","ث","أ","خ","ج","س"],monthNames:["يناير","فبراير","مارس","ابريل","مايو","يونيو","يوليو","اغسطس","سبتمبر","اكتوبر","نوفمبر","ديسمبر"],abbreviatedMonthNames:["يناير","فبراير","مارس","ابريل","مايو","يونيو","يوليو","اغسطس","سبتمبر","اكتوبر","نوفمبر","ديسمبر"],amDesignator:"ص",pmDesignator:"م",firstDayOfWeek:6,twoDigitYearMax:2029,dateElementOrder:"dmy",formatPatterns:{shortDate:"dd/MM/yyyy",longDate:"dd MMMM, yyyy",shortTime:"hh:mm tt",longTime:"hh:mm:ss tt",fullDateTime:"dd MMMM, yyyy hh:mm:ss tt",sortableDateTime:"yyyy-MM-ddTHH:mm:ss",universalSortableDateTime:"yyyy-MM-dd HH:mm:ssZ",rfc1123:"ddd, dd MMM yyyy HH:mm:ss GMT",monthDay:"dd MMMM",yearMonth:"MMMM, yyyy"},regexPatterns:{jan:/^يناير/i,feb:/^فبراير/i,mar:/^مارس/i,apr:/^ابريل/i,may:/^مايو/i,jun:/^يونيو/i,jul:/^يوليو/i,aug:/^اغسطس/i,sep:/^سبتمبر/i,oct:/^اكتوبر/i,nov:/^نوفمبر/i,dec:/^ديسمبر/i,sun:/^الاحد/i,mon:/^ا(1)?/i,tue:/^الثلاثاء/i,wed:/^الاربعاء/i,thu:/^الخميس/i,fri:/^الجمعة/i,sat:/^السبت/i,future:/^next/i,past:/^last|past|prev(ious)?/i,add:/^(\\+|after|from)/i,subtract:/^(\\-|before|ago)/i,yesterday:/^yesterday/i,today:/^t(oday)?/i,tomorrow:/^tomorrow/i,now:/^n(ow)?/i,millisecond:/^ms|milli(second)?s?/i,second:/^sec(ond)?s?/i,minute:/^min(ute)?s?/i,hour:/^h(ou)?rs?/i,week:/^w(ee)?k/i,month:/^m(o(nth)?s?)?/i,day:/^d(ays?)?/i,year:/^y((ea)?rs?)?/i,shortMeridian:/^(a|p)/i,longMeridian:/^(a\\.?m?\\.?|p\\.?m?\\.?)/i,timezone:/^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt)/i,ordinalSuffix:/^\\s*(st|nd|rd|th)/i,timeContext:/^\\s*(\\:|a|p)/i},abbreviatedTimeZoneStandard:{GMT:"-000",EST:"-0400",CST:"-0500",MST:"-0600",PST:"-0700"},abbreviatedTimeZoneDST:{GMT:"-000",EDT:"-0500",CDT:"-0600",MDT:"-0700",PDT:"-0800"}};
',
  'ar-MA' => 'Date.CultureInfo={name:"ar-MA",englishName:"Arabic (Morocco)",nativeName:"العربية (المملكة المغربية)",dayNames:["الاحد","الاثنين","الثلاثاء","الاربعاء","الخميس","الجمعة","السبت"],abbreviatedDayNames:["الاحد","الاثنين","الثلاثاء","الاربعاء","الخميس","الجمعة","السبت"],shortestDayNames:["أ","ا","ث","أ","خ","ج","س"],firstLetterDayNames:["أ","ا","ث","أ","خ","ج","س"],monthNames:["يناير","فبراير","مارس","ابريل","ماي","يونيو","يوليوز","غشت","شتنبر","اكتوبر","نونبر","دجنبر"],abbreviatedMonthNames:["يناير","فبراير","مارس","ابريل","ماي","يونيو","يوليوز","غشت","شتنبر","اكتوبر","نونبر","دجنبر"],amDesignator:"ص",pmDesignator:"م",firstDayOfWeek:1,twoDigitYearMax:2029,dateElementOrder:"dmy",formatPatterns:{shortDate:"dd-MM-yyyy",longDate:"dd MMMM, yyyy",shortTime:"H:mm",longTime:"H:mm:ss",fullDateTime:"dd MMMM, yyyy H:mm:ss",sortableDateTime:"yyyy-MM-ddTHH:mm:ss",universalSortableDateTime:"yyyy-MM-dd HH:mm:ssZ",rfc1123:"ddd, dd MMM yyyy HH:mm:ss GMT",monthDay:"dd MMMM",yearMonth:"MMMM, yyyy"},regexPatterns:{jan:/^يناير/i,feb:/^فبراير/i,mar:/^مارس/i,apr:/^ابريل/i,may:/^ماي/i,jun:/^يونيو/i,jul:/^يوليوز/i,aug:/^غشت/i,sep:/^شتنبر/i,oct:/^اكتوبر/i,nov:/^نونبر/i,dec:/^دجنبر/i,sun:/^الاحد/i,mon:/^ا(1)?/i,tue:/^الثلاثاء/i,wed:/^الاربعاء/i,thu:/^الخميس/i,fri:/^الجمعة/i,sat:/^السبت/i,future:/^next/i,past:/^last|past|prev(ious)?/i,add:/^(\\+|after|from)/i,subtract:/^(\\-|before|ago)/i,yesterday:/^yesterday/i,today:/^t(oday)?/i,tomorrow:/^tomorrow/i,now:/^n(ow)?/i,millisecond:/^ms|milli(second)?s?/i,second:/^sec(ond)?s?/i,minute:/^min(ute)?s?/i,hour:/^h(ou)?rs?/i,week:/^w(ee)?k/i,month:/^m(o(nth)?s?)?/i,day:/^d(ays?)?/i,year:/^y((ea)?rs?)?/i,shortMeridian:/^(a|p)/i,longMeridian:/^(a\\.?m?\\.?|p\\.?m?\\.?)/i,timezone:/^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt)/i,ordinalSuffix:/^\\s*(st|nd|rd|th)/i,timeContext:/^\\s*(\\:|a|p)/i},abbreviatedTimeZoneStandard:{GMT:"-000",EST:"-0400",CST:"-0500",MST:"-0600",PST:"-0700"},abbreviatedTimeZoneDST:{GMT:"-000",EDT:"-0500",CDT:"-0600",MDT:"-0700",PDT:"-0800"}};
',
  'ar-OM' => 'Date.CultureInfo={name:"ar-OM",englishName:"Arabic (Oman)",nativeName:"العربية (عمان)",dayNames:["الاحد","الاثنين","الثلاثاء","الاربعاء","الخميس","الجمعة","السبت"],abbreviatedDayNames:["الاحد","الاثنين","الثلاثاء","الاربعاء","الخميس","الجمعة","السبت"],shortestDayNames:["أ","ا","ث","أ","خ","ج","س"],firstLetterDayNames:["أ","ا","ث","أ","خ","ج","س"],monthNames:["يناير","فبراير","مارس","ابريل","مايو","يونيو","يوليو","اغسطس","سبتمبر","اكتوبر","نوفمبر","ديسمبر"],abbreviatedMonthNames:["يناير","فبراير","مارس","ابريل","مايو","يونيو","يوليو","اغسطس","سبتمبر","اكتوبر","نوفمبر","ديسمبر"],amDesignator:"ص",pmDesignator:"م",firstDayOfWeek:6,twoDigitYearMax:2029,dateElementOrder:"dmy",formatPatterns:{shortDate:"dd/MM/yyyy",longDate:"dd MMMM, yyyy",shortTime:"hh:mm tt",longTime:"hh:mm:ss tt",fullDateTime:"dd MMMM, yyyy hh:mm:ss tt",sortableDateTime:"yyyy-MM-ddTHH:mm:ss",universalSortableDateTime:"yyyy-MM-dd HH:mm:ssZ",rfc1123:"ddd, dd MMM yyyy HH:mm:ss GMT",monthDay:"dd MMMM",yearMonth:"MMMM, yyyy"},regexPatterns:{jan:/^يناير/i,feb:/^فبراير/i,mar:/^مارس/i,apr:/^ابريل/i,may:/^مايو/i,jun:/^يونيو/i,jul:/^يوليو/i,aug:/^اغسطس/i,sep:/^سبتمبر/i,oct:/^اكتوبر/i,nov:/^نوفمبر/i,dec:/^ديسمبر/i,sun:/^الاحد/i,mon:/^ا(1)?/i,tue:/^الثلاثاء/i,wed:/^الاربعاء/i,thu:/^الخميس/i,fri:/^الجمعة/i,sat:/^السبت/i,future:/^next/i,past:/^last|past|prev(ious)?/i,add:/^(\\+|after|from)/i,subtract:/^(\\-|before|ago)/i,yesterday:/^yesterday/i,today:/^t(oday)?/i,tomorrow:/^tomorrow/i,now:/^n(ow)?/i,millisecond:/^ms|milli(second)?s?/i,second:/^sec(ond)?s?/i,minute:/^min(ute)?s?/i,hour:/^h(ou)?rs?/i,week:/^w(ee)?k/i,month:/^m(o(nth)?s?)?/i,day:/^d(ays?)?/i,year:/^y((ea)?rs?)?/i,shortMeridian:/^(a|p)/i,longMeridian:/^(a\\.?m?\\.?|p\\.?m?\\.?)/i,timezone:/^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt)/i,ordinalSuffix:/^\\s*(st|nd|rd|th)/i,timeContext:/^\\s*(\\:|a|p)/i},abbreviatedTimeZoneStandard:{GMT:"-000",EST:"-0400",CST:"-0500",MST:"-0600",PST:"-0700"},abbreviatedTimeZoneDST:{GMT:"-000",EDT:"-0500",CDT:"-0600",MDT:"-0700",PDT:"-0800"}};
',
  'ar-QA' => 'Date.CultureInfo={name:"ar-QA",englishName:"Arabic (Qatar)",nativeName:"العربية (قطر)",dayNames:["الاحد","الاثنين","الثلاثاء","الاربعاء","الخميس","الجمعة","السبت"],abbreviatedDayNames:["الاحد","الاثنين","الثلاثاء","الاربعاء","الخميس","الجمعة","السبت"],shortestDayNames:["أ","ا","ث","أ","خ","ج","س"],firstLetterDayNames:["أ","ا","ث","أ","خ","ج","س"],monthNames:["يناير","فبراير","مارس","ابريل","مايو","يونيو","يوليو","اغسطس","سبتمبر","اكتوبر","نوفمبر","ديسمبر"],abbreviatedMonthNames:["يناير","فبراير","مارس","ابريل","مايو","يونيو","يوليو","اغسطس","سبتمبر","اكتوبر","نوفمبر","ديسمبر"],amDesignator:"ص",pmDesignator:"م",firstDayOfWeek:6,twoDigitYearMax:2029,dateElementOrder:"dmy",formatPatterns:{shortDate:"dd/MM/yyyy",longDate:"dd MMMM, yyyy",shortTime:"hh:mm tt",longTime:"hh:mm:ss tt",fullDateTime:"dd MMMM, yyyy hh:mm:ss tt",sortableDateTime:"yyyy-MM-ddTHH:mm:ss",universalSortableDateTime:"yyyy-MM-dd HH:mm:ssZ",rfc1123:"ddd, dd MMM yyyy HH:mm:ss GMT",monthDay:"dd MMMM",yearMonth:"MMMM, yyyy"},regexPatterns:{jan:/^يناير/i,feb:/^فبراير/i,mar:/^مارس/i,apr:/^ابريل/i,may:/^مايو/i,jun:/^يونيو/i,jul:/^يوليو/i,aug:/^اغسطس/i,sep:/^سبتمبر/i,oct:/^اكتوبر/i,nov:/^نوفمبر/i,dec:/^ديسمبر/i,sun:/^الاحد/i,mon:/^ا(1)?/i,tue:/^الثلاثاء/i,wed:/^الاربعاء/i,thu:/^الخميس/i,fri:/^الجمعة/i,sat:/^السبت/i,future:/^next/i,past:/^last|past|prev(ious)?/i,add:/^(\\+|after|from)/i,subtract:/^(\\-|before|ago)/i,yesterday:/^yesterday/i,today:/^t(oday)?/i,tomorrow:/^tomorrow/i,now:/^n(ow)?/i,millisecond:/^ms|milli(second)?s?/i,second:/^sec(ond)?s?/i,minute:/^min(ute)?s?/i,hour:/^h(ou)?rs?/i,week:/^w(ee)?k/i,month:/^m(o(nth)?s?)?/i,day:/^d(ays?)?/i,year:/^y((ea)?rs?)?/i,shortMeridian:/^(a|p)/i,longMeridian:/^(a\\.?m?\\.?|p\\.?m?\\.?)/i,timezone:/^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt)/i,ordinalSuffix:/^\\s*(st|nd|rd|th)/i,timeContext:/^\\s*(\\:|a|p)/i},abbreviatedTimeZoneStandard:{GMT:"-000",EST:"-0400",CST:"-0500",MST:"-0600",PST:"-0700"},abbreviatedTimeZoneDST:{GMT:"-000",EDT:"-0500",CDT:"-0600",MDT:"-0700",PDT:"-0800"}};
',
  'ar-SA' => 'Date.CultureInfo={name:"ar-SA",englishName:"Arabic (Saudi Arabia)",nativeName:"العربية (المملكة العربية السعودية)",dayNames:["الاحد","الاثنين","الثلاثاء","الاربعاء","الخميس","الجمعة","السبت"],abbreviatedDayNames:["الاحد","الاثنين","الثلاثاء","الاربعاء","الخميس","الجمعة","السبت"],shortestDayNames:["أ","ا","ث","أ","خ","ج","س"],firstLetterDayNames:["أ","ا","ث","أ","خ","ج","س"],monthNames:["محرم","صفر","ربيع الأول","ربيع الثاني","جمادى الأولى","جمادى الثانية","رجب","شعبان","رمضان","شوال","ذو القعدة","ذو الحجة"],abbreviatedMonthNames:["محرم","صفر","ربيع الاول","ربيع الثاني","جمادى الاولى","جمادى الثانية","رجب","شعبان","رمضان","شوال","ذو القعدة","ذو الحجة"],amDesignator:"ص",pmDesignator:"م",firstDayOfWeek:6,twoDigitYearMax:1451,dateElementOrder:"dmy",formatPatterns:{shortDate:"dd/MM/yy",longDate:"dd/MMMM/yyyy",shortTime:"hh:mm tt",longTime:"hh:mm:ss tt",fullDateTime:"dd/MMMM/yyyy hh:mm:ss tt",sortableDateTime:"yyyy-MM-ddTHH:mm:ss",universalSortableDateTime:"yyyy-MM-dd HH:mm:ssZ",rfc1123:"ddd, dd MMM yyyy HH:mm:ss GMT",monthDay:"dd MMMM",yearMonth:"MMMM, yyyy"},regexPatterns:{jan:/^محرم/i,feb:/^صفر/i,mar:/^ربيع الأول/i,apr:/^ربيع الثاني/i,may:/^جمادى الأولى/i,jun:/^جمادى الثانية/i,jul:/^رجب/i,aug:/^شعبان/i,sep:/^رمضان/i,oct:/^شوال/i,nov:/^ذو القعدة/i,dec:/^ذو الحجة/i,sun:/^الاحد/i,mon:/^ا(1)?/i,tue:/^الثلاثاء/i,wed:/^الاربعاء/i,thu:/^الخميس/i,fri:/^الجمعة/i,sat:/^السبت/i,future:/^next/i,past:/^last|past|prev(ious)?/i,add:/^(\\+|after|from)/i,subtract:/^(\\-|before|ago)/i,yesterday:/^yesterday/i,today:/^t(oday)?/i,tomorrow:/^tomorrow/i,now:/^n(ow)?/i,millisecond:/^ms|milli(second)?s?/i,second:/^sec(ond)?s?/i,minute:/^min(ute)?s?/i,hour:/^h(ou)?rs?/i,week:/^w(ee)?k/i,month:/^m(o(nth)?s?)?/i,day:/^d(ays?)?/i,year:/^y((ea)?rs?)?/i,shortMeridian:/^(a|p)/i,longMeridian:/^(a\\.?m?\\.?|p\\.?m?\\.?)/i,timezone:/^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt)/i,ordinalSuffix:/^\\s*(st|nd|rd|th)/i,timeContext:/^\\s*(\\:|a|p)/i},abbreviatedTimeZoneStandard:{GMT:"-000",EST:"-0400",CST:"-0500",MST:"-0600",PST:"-0700"},abbreviatedTimeZoneDST:{GMT:"-000",EDT:"-0500",CDT:"-0600",MDT:"-0700",PDT:"-0800"}};
',
  'ar-SY' => 'Date.CultureInfo={name:"ar-SY",englishName:"Arabic (Syria)",nativeName:"العربية (سوريا)",dayNames:["الاحد","الاثنين","الثلاثاء","الاربعاء","الخميس","الجمعة","السبت"],abbreviatedDayNames:["الاحد","الاثنين","الثلاثاء","الاربعاء","الخميس","الجمعة","السبت"],shortestDayNames:["أ","ا","ث","أ","خ","ج","س"],firstLetterDayNames:["أ","ا","ث","أ","خ","ج","س"],monthNames:["كانون الثاني","شباط","آذار","نيسان","أيار","حزيران","تموز","آب","أيلول","تشرين الأول","تشرين الثاني","كانون الأول"],abbreviatedMonthNames:["كانون الثاني","شباط","آذار","نيسان","أيار","حزيران","تموز","آب","أيلول","تشرين الأول","تشرين الثاني","كانون الأول"],amDesignator:"ص",pmDesignator:"م",firstDayOfWeek:6,twoDigitYearMax:2029,dateElementOrder:"dmy",formatPatterns:{shortDate:"dd/MM/yyyy",longDate:"dd MMMM, yyyy",shortTime:"hh:mm tt",longTime:"hh:mm:ss tt",fullDateTime:"dd MMMM, yyyy hh:mm:ss tt",sortableDateTime:"yyyy-MM-ddTHH:mm:ss",universalSortableDateTime:"yyyy-MM-dd HH:mm:ssZ",rfc1123:"ddd, dd MMM yyyy HH:mm:ss GMT",monthDay:"dd MMMM",yearMonth:"MMMM, yyyy"},regexPatterns:{jan:/^كانون الثاني/i,feb:/^شباط/i,mar:/^آذار/i,apr:/^نيسان/i,may:/^أيار/i,jun:/^حزيران/i,jul:/^تموز/i,aug:/^آب/i,sep:/^أيلول/i,oct:/^تشرين الأول/i,nov:/^تشرين الثاني/i,dec:/^كانون الأول/i,sun:/^الاحد/i,mon:/^ا(1)?/i,tue:/^الثلاثاء/i,wed:/^الاربعاء/i,thu:/^الخميس/i,fri:/^الجمعة/i,sat:/^السبت/i,future:/^next/i,past:/^last|past|prev(ious)?/i,add:/^(\\+|after|from)/i,subtract:/^(\\-|before|ago)/i,yesterday:/^yesterday/i,today:/^t(oday)?/i,tomorrow:/^tomorrow/i,now:/^n(ow)?/i,millisecond:/^ms|milli(second)?s?/i,second:/^sec(ond)?s?/i,minute:/^min(ute)?s?/i,hour:/^h(ou)?rs?/i,week:/^w(ee)?k/i,month:/^m(o(nth)?s?)?/i,day:/^d(ays?)?/i,year:/^y((ea)?rs?)?/i,shortMeridian:/^(a|p)/i,longMeridian:/^(a\\.?m?\\.?|p\\.?m?\\.?)/i,timezone:/^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt)/i,ordinalSuffix:/^\\s*(st|nd|rd|th)/i,timeContext:/^\\s*(\\:|a|p)/i},abbreviatedTimeZoneStandard:{GMT:"-000",EST:"-0400",CST:"-0500",MST:"-0600",PST:"-0700"},abbreviatedTimeZoneDST:{GMT:"-000",EDT:"-0500",CDT:"-0600",MDT:"-0700",PDT:"-0800"}};
',
  'ar-TN' => 'Date.CultureInfo={name:"ar-TN",englishName:"Arabic (Tunisia)",nativeName:"العربية (تونس)",dayNames:["الاحد","الاثنين","الثلاثاء","الاربعاء","الخميس","الجمعة","السبت"],abbreviatedDayNames:["الاحد","الاثنين","الثلاثاء","الاربعاء","الخميس","الجمعة","السبت"],shortestDayNames:["أ","ا","ث","أ","خ","ج","س"],firstLetterDayNames:["أ","ا","ث","أ","خ","ج","س"],monthNames:["جانفي","فيفري","مارس","افريل","ماي","جوان","جويلية","اوت","سبتمبر","اكتوبر","نوفمبر","ديسمبر"],abbreviatedMonthNames:["جانفي","فيفري","مارس","افريل","ماي","جوان","جويلية","اوت","سبتمبر","اكتوبر","نوفمبر","ديسمبر"],amDesignator:"ص",pmDesignator:"م",firstDayOfWeek:1,twoDigitYearMax:2029,dateElementOrder:"dmy",formatPatterns:{shortDate:"dd-MM-yyyy",longDate:"dd MMMM, yyyy",shortTime:"H:mm",longTime:"H:mm:ss",fullDateTime:"dd MMMM, yyyy H:mm:ss",sortableDateTime:"yyyy-MM-ddTHH:mm:ss",universalSortableDateTime:"yyyy-MM-dd HH:mm:ssZ",rfc1123:"ddd, dd MMM yyyy HH:mm:ss GMT",monthDay:"dd MMMM",yearMonth:"MMMM, yyyy"},regexPatterns:{jan:/^جانفي/i,feb:/^فيفري/i,mar:/^مارس/i,apr:/^افريل/i,may:/^ماي/i,jun:/^جوان/i,jul:/^جويلية/i,aug:/^اوت/i,sep:/^سبتمبر/i,oct:/^اكتوبر/i,nov:/^نوفمبر/i,dec:/^ديسمبر/i,sun:/^الاحد/i,mon:/^ا(1)?/i,tue:/^الثلاثاء/i,wed:/^الاربعاء/i,thu:/^الخميس/i,fri:/^الجمعة/i,sat:/^السبت/i,future:/^next/i,past:/^last|past|prev(ious)?/i,add:/^(\\+|after|from)/i,subtract:/^(\\-|before|ago)/i,yesterday:/^yesterday/i,today:/^t(oday)?/i,tomorrow:/^tomorrow/i,now:/^n(ow)?/i,millisecond:/^ms|milli(second)?s?/i,second:/^sec(ond)?s?/i,minute:/^min(ute)?s?/i,hour:/^h(ou)?rs?/i,week:/^w(ee)?k/i,month:/^m(o(nth)?s?)?/i,day:/^d(ays?)?/i,year:/^y((ea)?rs?)?/i,shortMeridian:/^(a|p)/i,longMeridian:/^(a\\.?m?\\.?|p\\.?m?\\.?)/i,timezone:/^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt)/i,ordinalSuffix:/^\\s*(st|nd|rd|th)/i,timeContext:/^\\s*(\\:|a|p)/i},abbreviatedTimeZoneStandard:{GMT:"-000",EST:"-0400",CST:"-0500",MST:"-0600",PST:"-0700"},abbreviatedTimeZoneDST:{GMT:"-000",EDT:"-0500",CDT:"-0600",MDT:"-0700",PDT:"-0800"}};
',
  'ar-YE' => 'Date.CultureInfo={name:"ar-YE",englishName:"Arabic (Yemen)",nativeName:"العربية (اليمن)",dayNames:["الاحد","الاثنين","الثلاثاء","الاربعاء","الخميس","الجمعة","السبت"],abbreviatedDayNames:["الاحد","الاثنين","الثلاثاء","الاربعاء","الخميس","الجمعة","السبت"],shortestDayNames:["أ","ا","ث","أ","خ","ج","س"],firstLetterDayNames:["أ","ا","ث","أ","خ","ج","س"],monthNames:["يناير","فبراير","مارس","ابريل","مايو","يونيو","يوليو","اغسطس","سبتمبر","اكتوبر","نوفمبر","ديسمبر"],abbreviatedMonthNames:["يناير","فبراير","مارس","ابريل","مايو","يونيو","يوليو","اغسطس","سبتمبر","اكتوبر","نوفمبر","ديسمبر"],amDesignator:"ص",pmDesignator:"م",firstDayOfWeek:6,twoDigitYearMax:2029,dateElementOrder:"dmy",formatPatterns:{shortDate:"dd/MM/yyyy",longDate:"dd MMMM, yyyy",shortTime:"hh:mm tt",longTime:"hh:mm:ss tt",fullDateTime:"dd MMMM, yyyy hh:mm:ss tt",sortableDateTime:"yyyy-MM-ddTHH:mm:ss",universalSortableDateTime:"yyyy-MM-dd HH:mm:ssZ",rfc1123:"ddd, dd MMM yyyy HH:mm:ss GMT",monthDay:"dd MMMM",yearMonth:"MMMM, yyyy"},regexPatterns:{jan:/^يناير/i,feb:/^فبراير/i,mar:/^مارس/i,apr:/^ابريل/i,may:/^مايو/i,jun:/^يونيو/i,jul:/^يوليو/i,aug:/^اغسطس/i,sep:/^سبتمبر/i,oct:/^اكتوبر/i,nov:/^نوفمبر/i,dec:/^ديسمبر/i,sun:/^الاحد/i,mon:/^ا(1)?/i,tue:/^الثلاثاء/i,wed:/^الاربعاء/i,thu:/^الخميس/i,fri:/^الجمعة/i,sat:/^السبت/i,future:/^next/i,past:/^last|past|prev(ious)?/i,add:/^(\\+|after|from)/i,subtract:/^(\\-|before|ago)/i,yesterday:/^yesterday/i,today:/^t(oday)?/i,tomorrow:/^tomorrow/i,now:/^n(ow)?/i,millisecond:/^ms|milli(second)?s?/i,second:/^sec(ond)?s?/i,minute:/^min(ute)?s?/i,hour:/^h(ou)?rs?/i,week:/^w(ee)?k/i,month:/^m(o(nth)?s?)?/i,day:/^d(ays?)?/i,year:/^y((ea)?rs?)?/i,shortMeridian:/^(a|p)/i,longMeridian:/^(a\\.?m?\\.?|p\\.?m?\\.?)/i,timezone:/^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt)/i,ordinalSuffix:/^\\s*(st|nd|rd|th)/i,timeContext:/^\\s*(\\:|a|p)/i},abbreviatedTimeZoneStandard:{GMT:"-000",EST:"-0400",CST:"-0500",MST:"-0600",PST:"-0700"},abbreviatedTimeZoneDST:{GMT:"-000",EDT:"-0500",CDT:"-0600",MDT:"-0700",PDT:"-0800"}};
',
  'az-Cyrl-AZ' => 'Date.CultureInfo={name:"az-Cyrl-AZ",englishName:"Azeri (Cyrillic, Azerbaijan)",nativeName:"Азәрбајҹан (Азәрбајҹан)",dayNames:["Базар","Базар ертәси","Чәршәнбә ахшамы","Чәршәнбә","Ҹүмә ахшамы","Ҹүмә","Шәнбә"],abbreviatedDayNames:["Б","Бе","Ча","Ч","Ҹа","Ҹ","Ш"],shortestDayNames:["Б","Бе","Ча","Ч","Ҹа","Ҹ","Ш"],firstLetterDayNames:["Б","Б","Ч","Ч","Ҹ","Ҹ","Ш"],monthNames:["Јанвар","Феврал","Март","Апрел","Мај","Ијун","Ијул","Август","Сентјабр","Октјабр","Нојабр","Декабр"],abbreviatedMonthNames:["Јан","Фев","Мар","Апр","Мај","Ијун","Ијул","Авг","Сен","Окт","Ноя","Дек"],amDesignator:"",pmDesignator:"",firstDayOfWeek:1,twoDigitYearMax:2029,dateElementOrder:"dmy",formatPatterns:{shortDate:"dd.MM.yyyy",longDate:"d MMMM yyyy",shortTime:"H:mm",longTime:"H:mm:ss",fullDateTime:"d MMMM yyyy H:mm:ss",sortableDateTime:"yyyy-MM-ddTHH:mm:ss",universalSortableDateTime:"yyyy-MM-dd HH:mm:ssZ",rfc1123:"ddd, dd MMM yyyy HH:mm:ss GMT",monthDay:"d MMMM",yearMonth:"MMMM yyyy"},regexPatterns:{jan:/^јан(вар)?/i,feb:/^фев(рал)?/i,mar:/^мар(т)?/i,apr:/^апр(ел)?/i,may:/^мај/i,jun:/^ијун/i,jul:/^ијул/i,aug:/^авг(уст)?/i,sep:/^сен(тјабр)?/i,oct:/^окт(јабр)?/i,nov:/^нојабр/i,dec:/^дек(абр)?/i,sun:/^базар/i,mon:/^базар ертәси/i,tue:/^чәршәнбә ахшамы/i,wed:/^чәршәнбә/i,thu:/^ҹүмә ахшамы/i,fri:/^ҹүмә/i,sat:/^шәнбә/i,future:/^next/i,past:/^last|past|prev(ious)?/i,add:/^(\\+|after|from)/i,subtract:/^(\\-|before|ago)/i,yesterday:/^yesterday/i,today:/^t(oday)?/i,tomorrow:/^tomorrow/i,now:/^n(ow)?/i,millisecond:/^ms|milli(second)?s?/i,second:/^sec(ond)?s?/i,minute:/^min(ute)?s?/i,hour:/^h(ou)?rs?/i,week:/^w(ee)?k/i,month:/^m(o(nth)?s?)?/i,day:/^d(ays?)?/i,year:/^y((ea)?rs?)?/i,shortMeridian:/^(a|p)/i,longMeridian:/^(a\\.?m?\\.?|p\\.?m?\\.?)/i,timezone:/^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt)/i,ordinalSuffix:/^\\s*(st|nd|rd|th)/i,timeContext:/^\\s*(\\:|a|p)/i},abbreviatedTimeZoneStandard:{GMT:"-000",EST:"-0400",CST:"-0500",MST:"-0600",PST:"-0700"},abbreviatedTimeZoneDST:{GMT:"-000",EDT:"-0500",CDT:"-0600",MDT:"-0700",PDT:"-0800"}};
',
  'az-Latn-AZ' => 'Date.CultureInfo={name:"az-Latn-AZ",englishName:"Azeri (Latin, Azerbaijan)",nativeName:"Azərbaycan­ılı (Azərbaycanca)",dayNames:["Bazar","Bazar ertəsi","Çərşənbə axşamı","Çərşənbə","Cümə axşamı","Cümə","Şənbə"],abbreviatedDayNames:["B","Be","Ça","Ç","Ca","C","Ş"],shortestDayNames:["B","Be","Ça","Ç","Ca","C","Ş"],firstLetterDayNames:["B","B","Ç","Ç","C","C","Ş"],monthNames:["Yanvar","Fevral","Mart","Aprel","May","İyun","İyul","Avgust","Sentyabr","Oktyabr","Noyabr","Dekabr"],abbreviatedMonthNames:["Yan","Fev","Mar","Apr","May","İyun","İyul","Avg","Sen","Okt","Noy","Dek"],amDesignator:"",pmDesignator:"",firstDayOfWeek:1,twoDigitYearMax:2029,dateElementOrder:"dmy",formatPatterns:{shortDate:"dd.MM.yyyy",longDate:"d MMMM yyyy",shortTime:"H:mm",longTime:"H:mm:ss",fullDateTime:"d MMMM yyyy H:mm:ss",sortableDateTime:"yyyy-MM-ddTHH:mm:ss",universalSortableDateTime:"yyyy-MM-dd HH:mm:ssZ",rfc1123:"ddd, dd MMM yyyy HH:mm:ss GMT",monthDay:"d MMMM",yearMonth:"MMMM yyyy"},regexPatterns:{jan:/^yan(var)?/i,feb:/^fev(ral)?/i,mar:/^mar(t)?/i,apr:/^apr(el)?/i,may:/^may/i,jun:/^iyun/i,jul:/^iyul/i,aug:/^avg(ust)?/i,sep:/^sen(tyabr)?/i,oct:/^okt(yabr)?/i,nov:/^noy(abr)?/i,dec:/^dek(abr)?/i,sun:/^bazar/i,mon:/^bazar ertəsi/i,tue:/^çərşənbə axşamı/i,wed:/^çərşənbə/i,thu:/^cümə axşamı/i,fri:/^cümə/i,sat:/^şənbə/i,future:/^next/i,past:/^last|past|prev(ious)?/i,add:/^(\\+|after|from)/i,subtract:/^(\\-|before|ago)/i,yesterday:/^yesterday/i,today:/^t(oday)?/i,tomorrow:/^tomorrow/i,now:/^n(ow)?/i,millisecond:/^ms|milli(second)?s?/i,second:/^sec(ond)?s?/i,minute:/^min(ute)?s?/i,hour:/^h(ou)?rs?/i,week:/^w(ee)?k/i,month:/^m(o(nth)?s?)?/i,day:/^d(ays?)?/i,year:/^y((ea)?rs?)?/i,shortMeridian:/^(a|p)/i,longMeridian:/^(a\\.?m?\\.?|p\\.?m?\\.?)/i,timezone:/^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt)/i,ordinalSuffix:/^\\s*(st|nd|rd|th)/i,timeContext:/^\\s*(\\:|a|p)/i},abbreviatedTimeZoneStandard:{GMT:"-000",EST:"-0400",CST:"-0500",MST:"-0600",PST:"-0700"},abbreviatedTimeZoneDST:{GMT:"-000",EDT:"-0500",CDT:"-0600",MDT:"-0700",PDT:"-0800"}};
',
  'be-BY' => 'Date.CultureInfo={name:"be-BY",englishName:"Belarusian (Belarus)",nativeName:"Беларускі (Беларусь)",dayNames:["нядзеля","панядзелак","аўторак","серада","чацвер","пятніца","субота"],abbreviatedDayNames:["нд","пн","аў","ср","чц","пт","сб"],shortestDayNames:["нд","пн","аў","ср","чц","пт","сб"],firstLetterDayNames:["н","п","а","с","ч","п","с"],monthNames:["Студзень","Люты","Сакавік","Красавік","Май","Чэрвень","Ліпень","Жнівень","Верасень","Кастрычнік","Лістапад","Снежань"],abbreviatedMonthNames:["Сту","Лют","Сак","Кра","Май","Чэр","Ліп","Жні","Вер","Кас","Ліс","Сне"],amDesignator:"",pmDesignator:"",firstDayOfWeek:1,twoDigitYearMax:2029,dateElementOrder:"dmy",formatPatterns:{shortDate:"dd.MM.yyyy",longDate:"d MMMM yyyy",shortTime:"H:mm",longTime:"H:mm:ss",fullDateTime:"d MMMM yyyy H:mm:ss",sortableDateTime:"yyyy-MM-ddTHH:mm:ss",universalSortableDateTime:"yyyy-MM-dd HH:mm:ssZ",rfc1123:"ddd, dd MMM yyyy HH:mm:ss GMT",monthDay:"d MMMM",yearMonth:"MMMM yyyy"},regexPatterns:{jan:/^сту(дзень)?/i,feb:/^лют(ы)?/i,mar:/^сак(авік)?/i,apr:/^кра(савік)?/i,may:/^май/i,jun:/^чэр(вень)?/i,jul:/^ліп(ень)?/i,aug:/^жні(вень)?/i,sep:/^вер(асень)?/i,oct:/^кас(трычнік)?/i,nov:/^ліс(тапад)?/i,dec:/^сне(жань)?/i,sun:/^нядзеля/i,mon:/^панядзелак/i,tue:/^аўторак/i,wed:/^серада/i,thu:/^чацвер/i,fri:/^пятніца/i,sat:/^субота/i,future:/^next/i,past:/^last|past|prev(ious)?/i,add:/^(\\+|after|from)/i,subtract:/^(\\-|before|ago)/i,yesterday:/^yesterday/i,today:/^t(oday)?/i,tomorrow:/^tomorrow/i,now:/^n(ow)?/i,millisecond:/^ms|milli(second)?s?/i,second:/^sec(ond)?s?/i,minute:/^min(ute)?s?/i,hour:/^h(ou)?rs?/i,week:/^w(ee)?k/i,month:/^m(o(nth)?s?)?/i,day:/^d(ays?)?/i,year:/^y((ea)?rs?)?/i,shortMeridian:/^(a|p)/i,longMeridian:/^(a\\.?m?\\.?|p\\.?m?\\.?)/i,timezone:/^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt)/i,ordinalSuffix:/^\\s*(st|nd|rd|th)/i,timeContext:/^\\s*(\\:|a|p)/i},abbreviatedTimeZoneStandard:{GMT:"-000",EST:"-0400",CST:"-0500",MST:"-0600",PST:"-0700"},abbreviatedTimeZoneDST:{GMT:"-000",EDT:"-0500",CDT:"-0600",MDT:"-0700",PDT:"-0800"}};
',
  'bg-BG' => 'Date.CultureInfo={name:"bg-BG",englishName:"Bulgarian (Bulgaria)",nativeName:"български (България)",dayNames:["неделя","понеделник","вторник","сряда","четвъртък","петък","събота"],abbreviatedDayNames:["Нд","Пн","Вт","Ср","Чт","Пт","Сб"],shortestDayNames:["не","по","вт","ср","че","пе","съ"],firstLetterDayNames:["н","п","в","с","ч","п","с"],monthNames:["Януари","Февруари","Март","Април","Май","Юни","Юли","Август","Септември","Октомври","Ноември","Декември"],abbreviatedMonthNames:["Януари","Февруари","Март","Април","Май","Юни","Юли","Август","Септември","Октомври","Ноември","Декември"],amDesignator:"",pmDesignator:"",firstDayOfWeek:1,twoDigitYearMax:2029,dateElementOrder:"dmy",formatPatterns:{shortDate:"dd.M.yyyy \'г.\'",longDate:"dd MMMM yyyy \'г.\'",shortTime:"HH:mm",longTime:"HH:mm:ss",fullDateTime:"dd MMMM yyyy \'г.\' HH:mm:ss",sortableDateTime:"yyyy-MM-ddTHH:mm:ss",universalSortableDateTime:"yyyy-MM-dd HH:mm:ssZ",rfc1123:"ddd, dd MMM yyyy HH:mm:ss GMT",monthDay:"dd MMMM",yearMonth:"MMMM yyyy \'г.\'"},regexPatterns:{jan:/^януари/i,feb:/^февруари/i,mar:/^март/i,apr:/^април/i,may:/^май/i,jun:/^юни/i,jul:/^юли/i,aug:/^август/i,sep:/^септември/i,oct:/^октомври/i,nov:/^ноември/i,dec:/^декември/i,sun:/^не((деля)?)?/i,mon:/^по((неделник)?)?/i,tue:/^вторник/i,wed:/^сряда/i,thu:/^че((твъртък)?)?/i,fri:/^пе((тък)?)?/i,sat:/^съ((бота)?)?/i,future:/^next/i,past:/^last|past|prev(ious)?/i,add:/^(\\+|after|from)/i,subtract:/^(\\-|before|ago)/i,yesterday:/^yesterday/i,today:/^t(oday)?/i,tomorrow:/^tomorrow/i,now:/^n(ow)?/i,millisecond:/^ms|milli(second)?s?/i,second:/^sec(ond)?s?/i,minute:/^min(ute)?s?/i,hour:/^h(ou)?rs?/i,week:/^w(ee)?k/i,month:/^m(o(nth)?s?)?/i,day:/^d(ays?)?/i,year:/^y((ea)?rs?)?/i,shortMeridian:/^(a|p)/i,longMeridian:/^(a\\.?m?\\.?|p\\.?m?\\.?)/i,timezone:/^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt)/i,ordinalSuffix:/^\\s*(st|nd|rd|th)/i,timeContext:/^\\s*(\\:|a|p)/i},abbreviatedTimeZoneStandard:{GMT:"-000",EST:"-0400",CST:"-0500",MST:"-0600",PST:"-0700"},abbreviatedTimeZoneDST:{GMT:"-000",EDT:"-0500",CDT:"-0600",MDT:"-0700",PDT:"-0800"}};
',
  'bs-Latn-BA' => 'Date.CultureInfo={name:"bs-Latn-BA",englishName:"Bosnian (Bosnia and Herzegovina)",nativeName:"bosanski (Bosna i Hercegovina)",dayNames:["nedjelja","ponedjeljak","utorak","srijeda","četvrtak","petak","subota"],abbreviatedDayNames:["ned","pon","uto","sri","čet","pet","sub"],shortestDayNames:["ned","pon","uto","sri","čet","pet","sub"],firstLetterDayNames:["n","p","u","s","č","p","s"],monthNames:["januar","februar","mart","april","maj","jun","jul","avgust","septembar","oktobar","novembar","decembar"],abbreviatedMonthNames:["jan","feb","mar","apr","maj","jun","jul","avg","sep","okt","nov","dec"],amDesignator:"",pmDesignator:"",firstDayOfWeek:1,twoDigitYearMax:2029,dateElementOrder:"dmy",formatPatterns:{shortDate:"d.M.yyyy",longDate:"d. MMMM yyyy",shortTime:"H:mm:ss",longTime:"H:mm:ss",fullDateTime:"d. MMMM yyyy H:mm:ss",sortableDateTime:"yyyy-MM-ddTHH:mm:ss",universalSortableDateTime:"yyyy-MM-dd HH:mm:ssZ",rfc1123:"ddd, dd MMM yyyy HH:mm:ss GMT",monthDay:"MMMM dd",yearMonth:"MMMM yyyy"},regexPatterns:{jan:/^jan(uar)?/i,feb:/^feb(ruar)?/i,mar:/^mar(t)?/i,apr:/^apr(il)?/i,may:/^maj/i,jun:/^jun/i,jul:/^jul/i,aug:/^avg(ust)?/i,sep:/^sep(tembar)?/i,oct:/^okt(obar)?/i,nov:/^nov(embar)?/i,dec:/^dec(embar)?/i,sun:/^nedjelja/i,mon:/^ponedjeljak/i,tue:/^utorak/i,wed:/^srijeda/i,thu:/^četvrtak/i,fri:/^petak/i,sat:/^subota/i,future:/^next/i,past:/^last|past|prev(ious)?/i,add:/^(\\+|after|from)/i,subtract:/^(\\-|before|ago)/i,yesterday:/^yesterday/i,today:/^t(oday)?/i,tomorrow:/^tomorrow/i,now:/^n(ow)?/i,millisecond:/^ms|milli(second)?s?/i,second:/^sec(ond)?s?/i,minute:/^min(ute)?s?/i,hour:/^h(ou)?rs?/i,week:/^w(ee)?k/i,month:/^m(o(nth)?s?)?/i,day:/^d(ays?)?/i,year:/^y((ea)?rs?)?/i,shortMeridian:/^(a|p)/i,longMeridian:/^(a\\.?m?\\.?|p\\.?m?\\.?)/i,timezone:/^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt)/i,ordinalSuffix:/^\\s*(st|nd|rd|th)/i,timeContext:/^\\s*(\\:|a|p)/i},abbreviatedTimeZoneStandard:{GMT:"-000",EST:"-0400",CST:"-0500",MST:"-0600",PST:"-0700"},abbreviatedTimeZoneDST:{GMT:"-000",EDT:"-0500",CDT:"-0600",MDT:"-0700",PDT:"-0800"}};
',
  'ca-ES' => 'Date.CultureInfo={name:"ca-ES",englishName:"Catalan (Catalan)",nativeName:"català (català)",dayNames:["diumenge","dilluns","dimarts","dimecres","dijous","divendres","dissabte"],abbreviatedDayNames:["dg.","dl.","dt.","dc.","dj.","dv.","ds."],shortestDayNames:["dg","dl","dt","dc","dj","dv","ds"],firstLetterDayNames:["d","d","d","d","d","d","d"],monthNames:["gener","febrer","març","abril","maig","juny","juliol","agost","setembre","octubre","novembre","desembre"],abbreviatedMonthNames:["gen","feb","març","abr","maig","juny","jul","ag","set","oct","nov","des"],amDesignator:"",pmDesignator:"",firstDayOfWeek:1,twoDigitYearMax:2029,dateElementOrder:"dmy",formatPatterns:{shortDate:"dd/MM/yyyy",longDate:"dddd, d\' / \'MMMM\' / \'yyyy",shortTime:"HH:mm",longTime:"HH:mm:ss",fullDateTime:"dddd, d\' / \'MMMM\' / \'yyyy HH:mm:ss",sortableDateTime:"yyyy-MM-ddTHH:mm:ss",universalSortableDateTime:"yyyy-MM-dd HH:mm:ssZ",rfc1123:"ddd, dd MMM yyyy HH:mm:ss GMT",monthDay:"dd MMMM",yearMonth:"MMMM\' / \'yyyy"},regexPatterns:{jan:/^gen(er)?/i,feb:/^feb(rer)?/i,mar:/^març/i,apr:/^abr(il)?/i,may:/^maig/i,jun:/^juny/i,jul:/^jul(iol)?/i,aug:/^ag(ost)?/i,sep:/^set(embre)?/i,oct:/^oct(ubre)?/i,nov:/^nov(embre)?/i,dec:/^des(embre)?/i,sun:/^dg((.(umenge)?)?)?/i,mon:/^dl((.(lluns)?)?)?/i,tue:/^dt((.(marts)?)?)?/i,wed:/^dc((.(mecres)?)?)?/i,thu:/^dj((.(jous)?)?)?/i,fri:/^dv((.(vendres)?)?)?/i,sat:/^ds((.(ssabte)?)?)?/i,future:/^next/i,past:/^last|past|prev(ious)?/i,add:/^(\\+|after|from)/i,subtract:/^(\\-|before|ago)/i,yesterday:/^yesterday/i,today:/^t(oday)?/i,tomorrow:/^tomorrow/i,now:/^n(ow)?/i,millisecond:/^ms|milli(second)?s?/i,second:/^sec(ond)?s?/i,minute:/^min(ute)?s?/i,hour:/^h(ou)?rs?/i,week:/^w(ee)?k/i,month:/^m(o(nth)?s?)?/i,day:/^d(ays?)?/i,year:/^y((ea)?rs?)?/i,shortMeridian:/^(a|p)/i,longMeridian:/^(a\\.?m?\\.?|p\\.?m?\\.?)/i,timezone:/^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt)/i,ordinalSuffix:/^\\s*(st|nd|rd|th)/i,timeContext:/^\\s*(\\:|a|p)/i},abbreviatedTimeZoneStandard:{GMT:"-000",EST:"-0400",CST:"-0500",MST:"-0600",PST:"-0700"},abbreviatedTimeZoneDST:{GMT:"-000",EDT:"-0500",CDT:"-0600",MDT:"-0700",PDT:"-0800"}};
',
  'cs-CZ' => 'Date.CultureInfo={name:"cs-CZ",englishName:"Czech (Czech Republic)",nativeName:"čeština (Česká republika)",dayNames:["neděle","pondělí","úterý","středa","čtvrtek","pátek","sobota"],abbreviatedDayNames:["ne","po","út","st","čt","pá","so"],shortestDayNames:["ne","po","út","st","čt","pá","so"],firstLetterDayNames:["n","p","ú","s","č","p","s"],monthNames:["leden","únor","březen","duben","květen","červen","červenec","srpen","září","říjen","listopad","prosinec"],abbreviatedMonthNames:["I","II","III","IV","V","VI","VII","VIII","IX","X","XI","XII"],amDesignator:"dop.",pmDesignator:"odp.",firstDayOfWeek:1,twoDigitYearMax:2029,dateElementOrder:"dmy",formatPatterns:{shortDate:"d.M.yyyy",longDate:"d. MMMM yyyy",shortTime:"H:mm",longTime:"H:mm:ss",fullDateTime:"d. MMMM yyyy H:mm:ss",sortableDateTime:"yyyy-MM-ddTHH:mm:ss",universalSortableDateTime:"yyyy-MM-dd HH:mm:ssZ",rfc1123:"ddd, dd MMM yyyy HH:mm:ss GMT",monthDay:"dd MMMM",yearMonth:"MMMM yyyy"},regexPatterns:{jan:/^leden/i,feb:/^únor/i,mar:/^březen/i,apr:/^duben/i,may:/^květen/i,jun:/^červen/i,jul:/^červenec/i,aug:/^srpen/i,sep:/^září/i,oct:/^říjen/i,nov:/^listopad/i,dec:/^prosinec/i,sun:/^neděle/i,mon:/^pondělí/i,tue:/^úterý/i,wed:/^středa/i,thu:/^čtvrtek/i,fri:/^pátek/i,sat:/^sobota/i,future:/^next/i,past:/^last|past|prev(ious)?/i,add:/^(\\+|after|from)/i,subtract:/^(\\-|before|ago)/i,yesterday:/^yesterday/i,today:/^t(oday)?/i,tomorrow:/^tomorrow/i,now:/^n(ow)?/i,millisecond:/^ms|milli(second)?s?/i,second:/^sec(ond)?s?/i,minute:/^min(ute)?s?/i,hour:/^h(ou)?rs?/i,week:/^w(ee)?k/i,month:/^m(o(nth)?s?)?/i,day:/^d(ays?)?/i,year:/^y((ea)?rs?)?/i,shortMeridian:/^(a|p)/i,longMeridian:/^(a\\.?m?\\.?|p\\.?m?\\.?)/i,timezone:/^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt)/i,ordinalSuffix:/^\\s*(st|nd|rd|th)/i,timeContext:/^\\s*(\\:|a|p)/i},abbreviatedTimeZoneStandard:{GMT:"-000",EST:"-0400",CST:"-0500",MST:"-0600",PST:"-0700"},abbreviatedTimeZoneDST:{GMT:"-000",EDT:"-0500",CDT:"-0600",MDT:"-0700",PDT:"-0800"}};
',
  'cy-GB' => 'Date.CultureInfo={name:"cy-GB",englishName:"Welsh (United Kingdom)",nativeName:"Cymraeg (y Deyrnas Unedig)",dayNames:["Dydd Sul","Dydd Llun","Dydd Mawrth","Dydd Mercher","Dydd Iau","Dydd Gwener","Dydd Sadwrn"],abbreviatedDayNames:["Sul","Llun","Maw","Mer","Iau","Gwe","Sad"],shortestDayNames:["Sul","Llun","Maw","Mer","Iau","Gwe","Sad"],firstLetterDayNames:["S","L","M","M","I","G","S"],monthNames:["Ionawr","Chwefror","Mawrth","Ebrill","Mai","Mehefin","Gorffennaf","Awst","Medi","Hydref","Tachwedd","Rhagfyr"],abbreviatedMonthNames:["Ion","Chwe","Maw","Ebr","Mai","Meh","Gor","Aws","Med","Hyd","Tach","Rhag"],amDesignator:"a.m.",pmDesignator:"p.m.",firstDayOfWeek:1,twoDigitYearMax:2029,dateElementOrder:"dmy",formatPatterns:{shortDate:"dd/MM/yyyy",longDate:"dd MMMM yyyy",shortTime:"HH:mm:ss",longTime:"HH:mm:ss",fullDateTime:"dd MMMM yyyy HH:mm:ss",sortableDateTime:"yyyy-MM-ddTHH:mm:ss",universalSortableDateTime:"yyyy-MM-dd HH:mm:ssZ",rfc1123:"ddd, dd MMM yyyy HH:mm:ss GMT",monthDay:"MMMM dd",yearMonth:"MMMM yyyy"},regexPatterns:{jan:/^ion(awr)?/i,feb:/^chwe(fror)?/i,mar:/^maw(rth)?/i,apr:/^ebr(ill)?/i,may:/^mai/i,jun:/^meh(efin)?/i,jul:/^gor(ffennaf)?/i,aug:/^aws(t)?/i,sep:/^med(i)?/i,oct:/^hyd(ref)?/i,nov:/^tach(wedd)?/i,dec:/^rhag(fyr)?/i,sun:/^dydd sul/i,mon:/^dydd llun/i,tue:/^dydd mawrth/i,wed:/^dydd mercher/i,thu:/^dydd iau/i,fri:/^dydd gwener/i,sat:/^dydd sadwrn/i,future:/^next/i,past:/^last|past|prev(ious)?/i,add:/^(\\+|after|from)/i,subtract:/^(\\-|before|ago)/i,yesterday:/^yesterday/i,today:/^t(oday)?/i,tomorrow:/^tomorrow/i,now:/^n(ow)?/i,millisecond:/^ms|milli(second)?s?/i,second:/^sec(ond)?s?/i,minute:/^min(ute)?s?/i,hour:/^h(ou)?rs?/i,week:/^w(ee)?k/i,month:/^m(o(nth)?s?)?/i,day:/^d(ays?)?/i,year:/^y((ea)?rs?)?/i,shortMeridian:/^(a|p)/i,longMeridian:/^(a\\.?m?\\.?|p\\.?m?\\.?)/i,timezone:/^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt)/i,ordinalSuffix:/^\\s*(st|nd|rd|th)/i,timeContext:/^\\s*(\\:|a|p)/i},abbreviatedTimeZoneStandard:{GMT:"-000",EST:"-0400",CST:"-0500",MST:"-0600",PST:"-0700"},abbreviatedTimeZoneDST:{GMT:"-000",EDT:"-0500",CDT:"-0600",MDT:"-0700",PDT:"-0800"}};
',
  'da-DK' => 'Date.CultureInfo={name:"da-DK",englishName:"Danish (Denmark)",nativeName:"dansk (Danmark)",dayNames:["søndag","mandag","tirsdag","onsdag","torsdag","fredag","lørdag"],abbreviatedDayNames:["sø","ma","ti","on","to","fr","lø"],shortestDayNames:["sø","ma","ti","on","to","fr","lø"],firstLetterDayNames:["s","m","t","o","t","f","l"],monthNames:["januar","februar","marts","april","maj","juni","juli","august","september","oktober","november","december"],abbreviatedMonthNames:["jan","feb","mar","apr","maj","jun","jul","aug","sep","okt","nov","dec"],amDesignator:"",pmDesignator:"",firstDayOfWeek:1,twoDigitYearMax:2029,dateElementOrder:"dmy",formatPatterns:{shortDate:"dd-MM-yyyy",longDate:"d. MMMM yyyy",shortTime:"HH:mm",longTime:"HH:mm:ss",fullDateTime:"d. MMMM yyyy HH:mm:ss",sortableDateTime:"yyyy-MM-ddTHH:mm:ss",universalSortableDateTime:"yyyy-MM-dd HH:mm:ssZ",rfc1123:"ddd, dd MMM yyyy HH:mm:ss GMT",monthDay:"d. MMMM",yearMonth:"MMMM yyyy"},regexPatterns:{jan:/^jan(uar)?/i,feb:/^feb(ruar)?/i,mar:/^mar(ts)?/i,apr:/^apr(il)?/i,may:/^maj/i,jun:/^jun(i)?/i,jul:/^jul(i)?/i,aug:/^aug(ust)?/i,sep:/^sep(t(ember)?)?/i,oct:/^okt(ober)?/i,nov:/^nov(ember)?/i,dec:/^dec(ember)?/i,sun:/^søndag/i,mon:/^mandag/i,tue:/^tirsdag/i,wed:/^onsdag/i,thu:/^torsdag/i,fri:/^fredag/i,sat:/^lørdag/i,future:/^next/i,past:/^last|past|prev(ious)?/i,add:/^(\\+|after|from)/i,subtract:/^(\\-|before|ago)/i,yesterday:/^yesterday/i,today:/^t(oday)?/i,tomorrow:/^tomorrow/i,now:/^n(ow)?/i,millisecond:/^ms|milli(second)?s?/i,second:/^sec(ond)?s?/i,minute:/^min(ute)?s?/i,hour:/^h(ou)?rs?/i,week:/^w(ee)?k/i,month:/^m(o(nth)?s?)?/i,day:/^d(ays?)?/i,year:/^y((ea)?rs?)?/i,shortMeridian:/^(a|p)/i,longMeridian:/^(a\\.?m?\\.?|p\\.?m?\\.?)/i,timezone:/^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt)/i,ordinalSuffix:/^\\s*(st|nd|rd|th)/i,timeContext:/^\\s*(\\:|a|p)/i},abbreviatedTimeZoneStandard:{GMT:"-000",EST:"-0400",CST:"-0500",MST:"-0600",PST:"-0700"},abbreviatedTimeZoneDST:{GMT:"-000",EDT:"-0500",CDT:"-0600",MDT:"-0700",PDT:"-0800"}};
',
  'de-AT' => 'Date.CultureInfo={name:"de-AT",englishName:"German (Austria)",nativeName:"Deutsch (Österreich)",dayNames:["Sonntag","Montag","Dienstag","Mittwoch","Donnerstag","Freitag","Samstag"],abbreviatedDayNames:["So","Mo","Di","Mi","Do","Fr","Sa"],shortestDayNames:["So","Mo","Di","Mi","Do","Fr","Sa"],firstLetterDayNames:["S","M","D","M","D","F","S"],monthNames:["Jänner","Februar","März","April","Mai","Juni","Juli","August","September","Oktober","November","Dezember"],abbreviatedMonthNames:["Jän","Feb","Mär","Apr","Mai","Jun","Jul","Aug","Sep","Okt","Nov","Dez"],amDesignator:"",pmDesignator:"",firstDayOfWeek:1,twoDigitYearMax:2029,dateElementOrder:"dmy",formatPatterns:{shortDate:"dd.MM.yyyy",longDate:"dddd, dd. MMMM yyyy",shortTime:"HH:mm",longTime:"HH:mm:ss",fullDateTime:"dddd, dd. MMMM yyyy HH:mm:ss",sortableDateTime:"yyyy-MM-ddTHH:mm:ss",universalSortableDateTime:"yyyy-MM-dd HH:mm:ssZ",rfc1123:"ddd, dd MMM yyyy HH:mm:ss GMT",monthDay:"dd MMMM",yearMonth:"MMMM yyyy"},regexPatterns:{jan:/^jän(ner)?/i,feb:/^feb(ruar)?/i,mar:/^mär(z)?/i,apr:/^apr(il)?/i,may:/^mai/i,jun:/^jun(i)?/i,jul:/^jul(i)?/i,aug:/^aug(ust)?/i,sep:/^sep(t(ember)?)?/i,oct:/^okt(ober)?/i,nov:/^nov(ember)?/i,dec:/^dez(ember)?/i,sun:/^sonntag/i,mon:/^montag/i,tue:/^dienstag/i,wed:/^mittwoch/i,thu:/^donnerstag/i,fri:/^freitag/i,sat:/^samstag/i,future:/^next/i,past:/^last|past|prev(ious)?/i,add:/^(\\+|after|from)/i,subtract:/^(\\-|before|ago)/i,yesterday:/^yesterday/i,today:/^t(oday)?/i,tomorrow:/^tomorrow/i,now:/^n(ow)?/i,millisecond:/^ms|milli(second)?s?/i,second:/^sec(ond)?s?/i,minute:/^min(ute)?s?/i,hour:/^h(ou)?rs?/i,week:/^w(ee)?k/i,month:/^m(o(nth)?s?)?/i,day:/^d(ays?)?/i,year:/^y((ea)?rs?)?/i,shortMeridian:/^(a|p)/i,longMeridian:/^(a\\.?m?\\.?|p\\.?m?\\.?)/i,timezone:/^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt)/i,ordinalSuffix:/^\\s*(st|nd|rd|th)/i,timeContext:/^\\s*(\\:|a|p)/i},abbreviatedTimeZoneStandard:{GMT:"-000",EST:"-0400",CST:"-0500",MST:"-0600",PST:"-0700"},abbreviatedTimeZoneDST:{GMT:"-000",EDT:"-0500",CDT:"-0600",MDT:"-0700",PDT:"-0800"}};
',
  'de-CH' => 'Date.CultureInfo={name:"de-CH",englishName:"German (Switzerland)",nativeName:"Deutsch (Schweiz)",dayNames:["Sonntag","Montag","Dienstag","Mittwoch","Donnerstag","Freitag","Samstag"],abbreviatedDayNames:["So","Mo","Di","Mi","Do","Fr","Sa"],shortestDayNames:["So","Mo","Di","Mi","Do","Fr","Sa"],firstLetterDayNames:["S","M","D","M","D","F","S"],monthNames:["Januar","Februar","März","April","Mai","Juni","Juli","August","September","Oktober","November","Dezember"],abbreviatedMonthNames:["Jan","Feb","Mrz","Apr","Mai","Jun","Jul","Aug","Sep","Okt","Nov","Dez"],amDesignator:"",pmDesignator:"",firstDayOfWeek:1,twoDigitYearMax:2029,dateElementOrder:"dmy",formatPatterns:{shortDate:"dd.MM.yyyy",longDate:"dddd, d. MMMM yyyy",shortTime:"HH:mm",longTime:"HH:mm:ss",fullDateTime:"dddd, d. MMMM yyyy HH:mm:ss",sortableDateTime:"yyyy-MM-ddTHH:mm:ss",universalSortableDateTime:"yyyy-MM-dd HH:mm:ssZ",rfc1123:"ddd, dd MMM yyyy HH:mm:ss GMT",monthDay:"dd MMMM",yearMonth:"MMMM yyyy"},regexPatterns:{jan:/^jan(uar)?/i,feb:/^feb(ruar)?/i,mar:/^märz/i,apr:/^apr(il)?/i,may:/^mai/i,jun:/^jun(i)?/i,jul:/^jul(i)?/i,aug:/^aug(ust)?/i,sep:/^sep(t(ember)?)?/i,oct:/^okt(ober)?/i,nov:/^nov(ember)?/i,dec:/^dez(ember)?/i,sun:/^sonntag/i,mon:/^montag/i,tue:/^dienstag/i,wed:/^mittwoch/i,thu:/^donnerstag/i,fri:/^freitag/i,sat:/^samstag/i,future:/^next/i,past:/^last|past|prev(ious)?/i,add:/^(\\+|after|from)/i,subtract:/^(\\-|before|ago)/i,yesterday:/^yesterday/i,today:/^t(oday)?/i,tomorrow:/^tomorrow/i,now:/^n(ow)?/i,millisecond:/^ms|milli(second)?s?/i,second:/^sec(ond)?s?/i,minute:/^min(ute)?s?/i,hour:/^h(ou)?rs?/i,week:/^w(ee)?k/i,month:/^m(o(nth)?s?)?/i,day:/^d(ays?)?/i,year:/^y((ea)?rs?)?/i,shortMeridian:/^(a|p)/i,longMeridian:/^(a\\.?m?\\.?|p\\.?m?\\.?)/i,timezone:/^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt)/i,ordinalSuffix:/^\\s*(st|nd|rd|th)/i,timeContext:/^\\s*(\\:|a|p)/i},abbreviatedTimeZoneStandard:{GMT:"-000",EST:"-0400",CST:"-0500",MST:"-0600",PST:"-0700"},abbreviatedTimeZoneDST:{GMT:"-000",EDT:"-0500",CDT:"-0600",MDT:"-0700",PDT:"-0800"}};
',
  'de-DE' => 'Date.CultureInfo={name:"de-DE",englishName:"German (Germany)",nativeName:"Deutsch (Deutschland)",dayNames:["Sonntag","Montag","Dienstag","Mittwoch","Donnerstag","Freitag","Samstag"],abbreviatedDayNames:["So","Mo","Di","Mi","Do","Fr","Sa"],shortestDayNames:["So","Mo","Di","Mi","Do","Fr","Sa"],firstLetterDayNames:["S","M","D","M","D","F","S"],monthNames:["Januar","Februar","März","April","Mai","Juni","Juli","August","September","Oktober","November","Dezember"],abbreviatedMonthNames:["Jan","Feb","Mrz","Apr","Mai","Jun","Jul","Aug","Sep","Okt","Nov","Dez"],amDesignator:"",pmDesignator:"",firstDayOfWeek:1,twoDigitYearMax:2029,dateElementOrder:"dmy",formatPatterns:{shortDate:"dd.MM.yyyy",longDate:"dddd, d. MMMM yyyy",shortTime:"HH:mm",longTime:"HH:mm:ss",fullDateTime:"dddd, d. MMMM yyyy HH:mm:ss",sortableDateTime:"yyyy-MM-ddTHH:mm:ss",universalSortableDateTime:"yyyy-MM-dd HH:mm:ssZ",rfc1123:"ddd, dd MMM yyyy HH:mm:ss GMT",monthDay:"dd MMMM",yearMonth:"MMMM yyyy"},regexPatterns:{jan:/^jan(uar)?/i,feb:/^feb(ruar)?/i,mar:/^märz/i,apr:/^apr(il)?/i,may:/^mai/i,jun:/^jun(i)?/i,jul:/^jul(i)?/i,aug:/^aug(ust)?/i,sep:/^sep(t(ember)?)?/i,oct:/^okt(ober)?/i,nov:/^nov(ember)?/i,dec:/^dez(ember)?/i,sun:/^sonntag/i,mon:/^montag/i,tue:/^dienstag/i,wed:/^mittwoch/i,thu:/^donnerstag/i,fri:/^freitag/i,sat:/^samstag/i,future:/^next/i,past:/^last|past|prev(ious)?/i,add:/^(\\+|after|from)/i,subtract:/^(\\-|before|ago)/i,yesterday:/^yesterday/i,today:/^t(oday)?/i,tomorrow:/^tomorrow/i,now:/^n(ow)?/i,millisecond:/^ms|milli(second)?s?/i,second:/^sec(ond)?s?/i,minute:/^min(ute)?s?/i,hour:/^h(ou)?rs?/i,week:/^w(ee)?k/i,month:/^m(o(nth)?s?)?/i,day:/^d(ays?)?/i,year:/^y((ea)?rs?)?/i,shortMeridian:/^(a|p)/i,longMeridian:/^(a\\.?m?\\.?|p\\.?m?\\.?)/i,timezone:/^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt)/i,ordinalSuffix:/^\\s*(st|nd|rd|th)/i,timeContext:/^\\s*(\\:|a|p)/i},abbreviatedTimeZoneStandard:{GMT:"-000",EST:"-0400",CST:"-0500",MST:"-0600",PST:"-0700"},abbreviatedTimeZoneDST:{GMT:"-000",EDT:"-0500",CDT:"-0600",MDT:"-0700",PDT:"-0800"}};
',
  'de-LI' => 'Date.CultureInfo={name:"de-LI",englishName:"German (Liechtenstein)",nativeName:"Deutsch (Liechtenstein)",dayNames:["Sonntag","Montag","Dienstag","Mittwoch","Donnerstag","Freitag","Samstag"],abbreviatedDayNames:["So","Mo","Di","Mi","Do","Fr","Sa"],shortestDayNames:["So","Mo","Di","Mi","Do","Fr","Sa"],firstLetterDayNames:["S","M","D","M","D","F","S"],monthNames:["Januar","Februar","März","April","Mai","Juni","Juli","August","September","Oktober","November","Dezember"],abbreviatedMonthNames:["Jan","Feb","Mrz","Apr","Mai","Jun","Jul","Aug","Sep","Okt","Nov","Dez"],amDesignator:"",pmDesignator:"",firstDayOfWeek:1,twoDigitYearMax:2029,dateElementOrder:"dmy",formatPatterns:{shortDate:"dd.MM.yyyy",longDate:"dddd, d. MMMM yyyy",shortTime:"HH:mm",longTime:"HH:mm:ss",fullDateTime:"dddd, d. MMMM yyyy HH:mm:ss",sortableDateTime:"yyyy-MM-ddTHH:mm:ss",universalSortableDateTime:"yyyy-MM-dd HH:mm:ssZ",rfc1123:"ddd, dd MMM yyyy HH:mm:ss GMT",monthDay:"dd MMMM",yearMonth:"MMMM yyyy"},regexPatterns:{jan:/^jan(uar)?/i,feb:/^feb(ruar)?/i,mar:/^märz/i,apr:/^apr(il)?/i,may:/^mai/i,jun:/^jun(i)?/i,jul:/^jul(i)?/i,aug:/^aug(ust)?/i,sep:/^sep(t(ember)?)?/i,oct:/^okt(ober)?/i,nov:/^nov(ember)?/i,dec:/^dez(ember)?/i,sun:/^sonntag/i,mon:/^montag/i,tue:/^dienstag/i,wed:/^mittwoch/i,thu:/^donnerstag/i,fri:/^freitag/i,sat:/^samstag/i,future:/^next/i,past:/^last|past|prev(ious)?/i,add:/^(\\+|after|from)/i,subtract:/^(\\-|before|ago)/i,yesterday:/^yesterday/i,today:/^t(oday)?/i,tomorrow:/^tomorrow/i,now:/^n(ow)?/i,millisecond:/^ms|milli(second)?s?/i,second:/^sec(ond)?s?/i,minute:/^min(ute)?s?/i,hour:/^h(ou)?rs?/i,week:/^w(ee)?k/i,month:/^m(o(nth)?s?)?/i,day:/^d(ays?)?/i,year:/^y((ea)?rs?)?/i,shortMeridian:/^(a|p)/i,longMeridian:/^(a\\.?m?\\.?|p\\.?m?\\.?)/i,timezone:/^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt)/i,ordinalSuffix:/^\\s*(st|nd|rd|th)/i,timeContext:/^\\s*(\\:|a|p)/i},abbreviatedTimeZoneStandard:{GMT:"-000",EST:"-0400",CST:"-0500",MST:"-0600",PST:"-0700"},abbreviatedTimeZoneDST:{GMT:"-000",EDT:"-0500",CDT:"-0600",MDT:"-0700",PDT:"-0800"}};
',
  'de-LU' => 'Date.CultureInfo={name:"de-LU",englishName:"German (Luxembourg)",nativeName:"Deutsch (Luxemburg)",dayNames:["Sonntag","Montag","Dienstag","Mittwoch","Donnerstag","Freitag","Samstag"],abbreviatedDayNames:["So","Mo","Di","Mi","Do","Fr","Sa"],shortestDayNames:["So","Mo","Di","Mi","Do","Fr","Sa"],firstLetterDayNames:["S","M","D","M","D","F","S"],monthNames:["Januar","Februar","März","April","Mai","Juni","Juli","August","September","Oktober","November","Dezember"],abbreviatedMonthNames:["Jan","Feb","Mrz","Apr","Mai","Jun","Jul","Aug","Sep","Okt","Nov","Dez"],amDesignator:"",pmDesignator:"",firstDayOfWeek:1,twoDigitYearMax:2029,dateElementOrder:"dmy",formatPatterns:{shortDate:"dd.MM.yyyy",longDate:"dddd, d. MMMM yyyy",shortTime:"HH:mm",longTime:"HH:mm:ss",fullDateTime:"dddd, d. MMMM yyyy HH:mm:ss",sortableDateTime:"yyyy-MM-ddTHH:mm:ss",universalSortableDateTime:"yyyy-MM-dd HH:mm:ssZ",rfc1123:"ddd, dd MMM yyyy HH:mm:ss GMT",monthDay:"dd MMMM",yearMonth:"MMMM yyyy"},regexPatterns:{jan:/^jan(uar)?/i,feb:/^feb(ruar)?/i,mar:/^märz/i,apr:/^apr(il)?/i,may:/^mai/i,jun:/^jun(i)?/i,jul:/^jul(i)?/i,aug:/^aug(ust)?/i,sep:/^sep(t(ember)?)?/i,oct:/^okt(ober)?/i,nov:/^nov(ember)?/i,dec:/^dez(ember)?/i,sun:/^sonntag/i,mon:/^montag/i,tue:/^dienstag/i,wed:/^mittwoch/i,thu:/^donnerstag/i,fri:/^freitag/i,sat:/^samstag/i,future:/^next/i,past:/^last|past|prev(ious)?/i,add:/^(\\+|after|from)/i,subtract:/^(\\-|before|ago)/i,yesterday:/^yesterday/i,today:/^t(oday)?/i,tomorrow:/^tomorrow/i,now:/^n(ow)?/i,millisecond:/^ms|milli(second)?s?/i,second:/^sec(ond)?s?/i,minute:/^min(ute)?s?/i,hour:/^h(ou)?rs?/i,week:/^w(ee)?k/i,month:/^m(o(nth)?s?)?/i,day:/^d(ays?)?/i,year:/^y((ea)?rs?)?/i,shortMeridian:/^(a|p)/i,longMeridian:/^(a\\.?m?\\.?|p\\.?m?\\.?)/i,timezone:/^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt)/i,ordinalSuffix:/^\\s*(st|nd|rd|th)/i,timeContext:/^\\s*(\\:|a|p)/i},abbreviatedTimeZoneStandard:{GMT:"-000",EST:"-0400",CST:"-0500",MST:"-0600",PST:"-0700"},abbreviatedTimeZoneDST:{GMT:"-000",EDT:"-0500",CDT:"-0600",MDT:"-0700",PDT:"-0800"}};
',
  'dv-MV' => 'Date.CultureInfo={name:"dv-MV",englishName:"Divehi (Maldives)",nativeName:"ދިވެހިބަސް (ދިވެހި ރާއްޖެ)",dayNames:["الاحد","الاثنين","الثلاثاء","الاربعاء","الخميس","الجمعة","السبت"],abbreviatedDayNames:["الاحد","الاثنين","الثلاثاء","الاربعاء","الخميس","الجمعة","السبت"],shortestDayNames:["أ","ا","ث","أ","خ","ج","س"],firstLetterDayNames:["أ","ا","ث","أ","خ","ج","س"],monthNames:["محرم","صفر","ربيع الأول","ربيع الثاني","جمادى الأولى","جمادى الثانية","رجب","شعبان","رمضان","شوال","ذو القعدة","ذو الحجة"],abbreviatedMonthNames:["محرم","صفر","ربيع الاول","ربيع الثاني","جمادى الاولى","جمادى الثانية","رجب","شعبان","رمضان","شوال","ذو القعدة","ذو الحجة"],amDesignator:"މކ",pmDesignator:"މފ",firstDayOfWeek:0,twoDigitYearMax:1451,dateElementOrder:"dmy",formatPatterns:{shortDate:"dd/MM/yy",longDate:"dd/MMMM/yyyy",shortTime:"HH:mm",longTime:"HH:mm:ss",fullDateTime:"dd/MMMM/yyyy HH:mm:ss",sortableDateTime:"yyyy-MM-ddTHH:mm:ss",universalSortableDateTime:"yyyy-MM-dd HH:mm:ssZ",rfc1123:"ddd, dd MMM yyyy HH:mm:ss GMT",monthDay:"dd MMMM",yearMonth:"MMMM, yyyy"},regexPatterns:{jan:/^محرم/i,feb:/^صفر/i,mar:/^ربيع الأول/i,apr:/^ربيع الثاني/i,may:/^جمادى الأولى/i,jun:/^جمادى الثانية/i,jul:/^رجب/i,aug:/^شعبان/i,sep:/^رمضان/i,oct:/^شوال/i,nov:/^ذو القعدة/i,dec:/^ذو الحجة/i,sun:/^الاحد/i,mon:/^ا(1)?/i,tue:/^الثلاثاء/i,wed:/^الاربعاء/i,thu:/^الخميس/i,fri:/^الجمعة/i,sat:/^السبت/i,future:/^next/i,past:/^last|past|prev(ious)?/i,add:/^(\\+|after|from)/i,subtract:/^(\\-|before|ago)/i,yesterday:/^yesterday/i,today:/^t(oday)?/i,tomorrow:/^tomorrow/i,now:/^n(ow)?/i,millisecond:/^ms|milli(second)?s?/i,second:/^sec(ond)?s?/i,minute:/^min(ute)?s?/i,hour:/^h(ou)?rs?/i,week:/^w(ee)?k/i,month:/^m(o(nth)?s?)?/i,day:/^d(ays?)?/i,year:/^y((ea)?rs?)?/i,shortMeridian:/^(a|p)/i,longMeridian:/^(a\\.?m?\\.?|p\\.?m?\\.?)/i,timezone:/^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt)/i,ordinalSuffix:/^\\s*(st|nd|rd|th)/i,timeContext:/^\\s*(\\:|a|p)/i},abbreviatedTimeZoneStandard:{GMT:"-000",EST:"-0400",CST:"-0500",MST:"-0600",PST:"-0700"},abbreviatedTimeZoneDST:{GMT:"-000",EDT:"-0500",CDT:"-0600",MDT:"-0700",PDT:"-0800"}};
',
  'el-GR' => 'Date.CultureInfo={name:"el-GR",englishName:"Greek (Greece)",nativeName:"ελληνικά (Ελλάδα)",dayNames:["Κυριακή","Δευτέρα","Τρίτη","Τετάρτη","Πέμπτη","Παρασκευή","Σάββατο"],abbreviatedDayNames:["Κυρ","Δευ","Τρι","Τετ","Πεμ","Παρ","Σαβ"],shortestDayNames:["Κυ","Δε","Τρ","Τε","Πε","Πα","Σά"],firstLetterDayNames:["Κ","Δ","Τ","Τ","Π","Π","Σ"],monthNames:["Ιανουάριος","Φεβρουάριος","Μάρτιος","Απρίλιος","Μάιος","Ιούνιος","Ιούλιος","Αύγουστος","Σεπτέμβριος","Οκτώβριος","Νοέμβριος","Δεκέμβριος"],abbreviatedMonthNames:["Ιαν","Φεβ","Μαρ","Απρ","Μαϊ","Ιουν","Ιουλ","Αυγ","Σεπ","Οκτ","Νοε","Δεκ"],amDesignator:"πμ",pmDesignator:"μμ",firstDayOfWeek:1,twoDigitYearMax:2029,dateElementOrder:"dmy",formatPatterns:{shortDate:"d/M/yyyy",longDate:"dddd, d MMMM yyyy",shortTime:"h:mm tt",longTime:"h:mm:ss tt",fullDateTime:"dddd, d MMMM yyyy h:mm:ss tt",sortableDateTime:"yyyy-MM-ddTHH:mm:ss",universalSortableDateTime:"yyyy-MM-dd HH:mm:ssZ",rfc1123:"ddd, dd MMM yyyy HH:mm:ss GMT",monthDay:"dd MMMM",yearMonth:"MMMM yyyy"},regexPatterns:{jan:/^ιαν(ουάριος)?/i,feb:/^φεβ(ρουάριος)?/i,mar:/^μάρτιος/i,apr:/^απρ(ίλιος)?/i,may:/^μάιος/i,jun:/^ιούνιος/i,jul:/^ιούλιος/i,aug:/^αύγουστος/i,sep:/^σεπ(τέμβριος)?/i,oct:/^οκτ(ώβριος)?/i,nov:/^νοέμβριος/i,dec:/^δεκ(έμβριος)?/i,sun:/^κυ(ρ(ιακή)?)?/i,mon:/^δε(υ(τέρα)?)?/i,tue:/^τρ(ι(τη)?)?/i,wed:/^τε(τ(άρτη)?)?/i,thu:/^πε(μ(πτη)?)?/i,fri:/^πα(ρ(ασκευή)?)?/i,sat:/^σά(β(βατο)?)?/i,future:/^next/i,past:/^last|past|prev(ious)?/i,add:/^(\\+|after|from)/i,subtract:/^(\\-|before|ago)/i,yesterday:/^yesterday/i,today:/^t(oday)?/i,tomorrow:/^tomorrow/i,now:/^n(ow)?/i,millisecond:/^ms|milli(second)?s?/i,second:/^sec(ond)?s?/i,minute:/^min(ute)?s?/i,hour:/^h(ou)?rs?/i,week:/^w(ee)?k/i,month:/^m(o(nth)?s?)?/i,day:/^d(ays?)?/i,year:/^y((ea)?rs?)?/i,shortMeridian:/^(a|p)/i,longMeridian:/^(a\\.?m?\\.?|p\\.?m?\\.?)/i,timezone:/^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt)/i,ordinalSuffix:/^\\s*(st|nd|rd|th)/i,timeContext:/^\\s*(\\:|a|p)/i},abbreviatedTimeZoneStandard:{GMT:"-000",EST:"-0400",CST:"-0500",MST:"-0600",PST:"-0700"},abbreviatedTimeZoneDST:{GMT:"-000",EDT:"-0500",CDT:"-0600",MDT:"-0700",PDT:"-0800"}};
',
  'en-029' => 'Date.CultureInfo={name:"en-029",englishName:"English (Caribbean)",nativeName:"English (Caribbean)",dayNames:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],abbreviatedDayNames:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],shortestDayNames:["Su","Mo","Tu","We","Th","Fr","Sa"],firstLetterDayNames:["S","M","T","W","T","F","S"],monthNames:["January","February","March","April","May","June","July","August","September","October","November","December"],abbreviatedMonthNames:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],amDesignator:"AM",pmDesignator:"PM",firstDayOfWeek:1,twoDigitYearMax:2029,dateElementOrder:"mdy",formatPatterns:{shortDate:"MM/dd/yyyy",longDate:"dddd, MMMM dd, yyyy",shortTime:"h:mm tt",longTime:"h:mm:ss tt",fullDateTime:"dddd, MMMM dd, yyyy h:mm:ss tt",sortableDateTime:"yyyy-MM-ddTHH:mm:ss",universalSortableDateTime:"yyyy-MM-dd HH:mm:ssZ",rfc1123:"ddd, dd MMM yyyy HH:mm:ss GMT",monthDay:"MMMM dd",yearMonth:"MMMM, yyyy"},regexPatterns:{jan:/^jan(uary)?/i,feb:/^feb(ruary)?/i,mar:/^mar(ch)?/i,apr:/^apr(il)?/i,may:/^may/i,jun:/^jun(e)?/i,jul:/^jul(y)?/i,aug:/^aug(ust)?/i,sep:/^sep(t(ember)?)?/i,oct:/^oct(ober)?/i,nov:/^nov(ember)?/i,dec:/^dec(ember)?/i,sun:/^su(n(day)?)?/i,mon:/^mo(n(day)?)?/i,tue:/^tu(e(s(day)?)?)?/i,wed:/^we(d(nesday)?)?/i,thu:/^th(u(r(s(day)?)?)?)?/i,fri:/^fr(i(day)?)?/i,sat:/^sa(t(urday)?)?/i,future:/^next/i,past:/^last|past|prev(ious)?/i,add:/^(\\+|after|from)/i,subtract:/^(\\-|before|ago)/i,yesterday:/^yesterday/i,today:/^t(oday)?/i,tomorrow:/^tomorrow/i,now:/^n(ow)?/i,millisecond:/^ms|milli(second)?s?/i,second:/^sec(ond)?s?/i,minute:/^min(ute)?s?/i,hour:/^h(ou)?rs?/i,week:/^w(ee)?k/i,month:/^m(o(nth)?s?)?/i,day:/^d(ays?)?/i,year:/^y((ea)?rs?)?/i,shortMeridian:/^(a|p)/i,longMeridian:/^(a\\.?m?\\.?|p\\.?m?\\.?)/i,timezone:/^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt)/i,ordinalSuffix:/^\\s*(st|nd|rd|th)/i,timeContext:/^\\s*(\\:|a|p)/i},abbreviatedTimeZoneStandard:{GMT:"-000",EST:"-0400",CST:"-0500",MST:"-0600",PST:"-0700"},abbreviatedTimeZoneDST:{GMT:"-000",EDT:"-0500",CDT:"-0600",MDT:"-0700",PDT:"-0800"}};
',
  'en-AU' => 'Date.CultureInfo={name:"en-AU",englishName:"English (Australia)",nativeName:"English (Australia)",dayNames:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],abbreviatedDayNames:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],shortestDayNames:["Su","Mo","Tu","We","Th","Fr","Sa"],firstLetterDayNames:["S","M","T","W","T","F","S"],monthNames:["January","February","March","April","May","June","July","August","September","October","November","December"],abbreviatedMonthNames:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],amDesignator:"AM",pmDesignator:"PM",firstDayOfWeek:1,twoDigitYearMax:2029,dateElementOrder:"dmy",formatPatterns:{shortDate:"d/MM/yyyy",longDate:"dddd, d MMMM yyyy",shortTime:"h:mm tt",longTime:"h:mm:ss tt",fullDateTime:"dddd, d MMMM yyyy h:mm:ss tt",sortableDateTime:"yyyy-MM-ddTHH:mm:ss",universalSortableDateTime:"yyyy-MM-dd HH:mm:ssZ",rfc1123:"ddd, dd MMM yyyy HH:mm:ss GMT",monthDay:"dd MMMM",yearMonth:"MMMM yyyy"},regexPatterns:{jan:/^jan(uary)?/i,feb:/^feb(ruary)?/i,mar:/^mar(ch)?/i,apr:/^apr(il)?/i,may:/^may/i,jun:/^jun(e)?/i,jul:/^jul(y)?/i,aug:/^aug(ust)?/i,sep:/^sep(t(ember)?)?/i,oct:/^oct(ober)?/i,nov:/^nov(ember)?/i,dec:/^dec(ember)?/i,sun:/^su(n(day)?)?/i,mon:/^mo(n(day)?)?/i,tue:/^tu(e(s(day)?)?)?/i,wed:/^we(d(nesday)?)?/i,thu:/^th(u(r(s(day)?)?)?)?/i,fri:/^fr(i(day)?)?/i,sat:/^sa(t(urday)?)?/i,future:/^next/i,past:/^last|past|prev(ious)?/i,add:/^(\\+|after|from)/i,subtract:/^(\\-|before|ago)/i,yesterday:/^yesterday/i,today:/^t(oday)?/i,tomorrow:/^tomorrow/i,now:/^n(ow)?/i,millisecond:/^ms|milli(second)?s?/i,second:/^sec(ond)?s?/i,minute:/^min(ute)?s?/i,hour:/^h(ou)?rs?/i,week:/^w(ee)?k/i,month:/^m(o(nth)?s?)?/i,day:/^d(ays?)?/i,year:/^y((ea)?rs?)?/i,shortMeridian:/^(a|p)/i,longMeridian:/^(a\\.?m?\\.?|p\\.?m?\\.?)/i,timezone:/^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt)/i,ordinalSuffix:/^\\s*(st|nd|rd|th)/i,timeContext:/^\\s*(\\:|a|p)/i},abbreviatedTimeZoneStandard:{GMT:"-000",EST:"-0400",CST:"-0500",MST:"-0600",PST:"-0700"},abbreviatedTimeZoneDST:{GMT:"-000",EDT:"-0500",CDT:"-0600",MDT:"-0700",PDT:"-0800"}};
',
  'en-BZ' => 'Date.CultureInfo={name:"en-BZ",englishName:"English (Belize)",nativeName:"English (Belize)",dayNames:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],abbreviatedDayNames:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],shortestDayNames:["Su","Mo","Tu","We","Th","Fr","Sa"],firstLetterDayNames:["S","M","T","W","T","F","S"],monthNames:["January","February","March","April","May","June","July","August","September","October","November","December"],abbreviatedMonthNames:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],amDesignator:"AM",pmDesignator:"PM",firstDayOfWeek:0,twoDigitYearMax:2029,dateElementOrder:"dmy",formatPatterns:{shortDate:"dd/MM/yyyy",longDate:"dddd, dd MMMM yyyy",shortTime:"hh:mm tt",longTime:"hh:mm:ss tt",fullDateTime:"dddd, dd MMMM yyyy hh:mm:ss tt",sortableDateTime:"yyyy-MM-ddTHH:mm:ss",universalSortableDateTime:"yyyy-MM-dd HH:mm:ssZ",rfc1123:"ddd, dd MMM yyyy HH:mm:ss GMT",monthDay:"dd MMMM",yearMonth:"MMMM yyyy"},regexPatterns:{jan:/^jan(uary)?/i,feb:/^feb(ruary)?/i,mar:/^mar(ch)?/i,apr:/^apr(il)?/i,may:/^may/i,jun:/^jun(e)?/i,jul:/^jul(y)?/i,aug:/^aug(ust)?/i,sep:/^sep(t(ember)?)?/i,oct:/^oct(ober)?/i,nov:/^nov(ember)?/i,dec:/^dec(ember)?/i,sun:/^su(n(day)?)?/i,mon:/^mo(n(day)?)?/i,tue:/^tu(e(s(day)?)?)?/i,wed:/^we(d(nesday)?)?/i,thu:/^th(u(r(s(day)?)?)?)?/i,fri:/^fr(i(day)?)?/i,sat:/^sa(t(urday)?)?/i,future:/^next/i,past:/^last|past|prev(ious)?/i,add:/^(\\+|after|from)/i,subtract:/^(\\-|before|ago)/i,yesterday:/^yesterday/i,today:/^t(oday)?/i,tomorrow:/^tomorrow/i,now:/^n(ow)?/i,millisecond:/^ms|milli(second)?s?/i,second:/^sec(ond)?s?/i,minute:/^min(ute)?s?/i,hour:/^h(ou)?rs?/i,week:/^w(ee)?k/i,month:/^m(o(nth)?s?)?/i,day:/^d(ays?)?/i,year:/^y((ea)?rs?)?/i,shortMeridian:/^(a|p)/i,longMeridian:/^(a\\.?m?\\.?|p\\.?m?\\.?)/i,timezone:/^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt)/i,ordinalSuffix:/^\\s*(st|nd|rd|th)/i,timeContext:/^\\s*(\\:|a|p)/i},abbreviatedTimeZoneStandard:{GMT:"-000",EST:"-0400",CST:"-0500",MST:"-0600",PST:"-0700"},abbreviatedTimeZoneDST:{GMT:"-000",EDT:"-0500",CDT:"-0600",MDT:"-0700",PDT:"-0800"}};
',
  'en-CA' => 'Date.CultureInfo={name:"en-CA",englishName:"English (Canada)",nativeName:"English (Canada)",dayNames:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],abbreviatedDayNames:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],shortestDayNames:["Su","Mo","Tu","We","Th","Fr","Sa"],firstLetterDayNames:["S","M","T","W","T","F","S"],monthNames:["January","February","March","April","May","June","July","August","September","October","November","December"],abbreviatedMonthNames:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],amDesignator:"AM",pmDesignator:"PM",firstDayOfWeek:0,twoDigitYearMax:2029,dateElementOrder:"dmy",formatPatterns:{shortDate:"dd/MM/yyyy",longDate:"MMMM d, yyyy",shortTime:"h:mm tt",longTime:"h:mm:ss tt",fullDateTime:"MMMM d, yyyy h:mm:ss tt",sortableDateTime:"yyyy-MM-ddTHH:mm:ss",universalSortableDateTime:"yyyy-MM-dd HH:mm:ssZ",rfc1123:"ddd, dd MMM yyyy HH:mm:ss GMT",monthDay:"MMMM dd",yearMonth:"MMMM, yyyy"},regexPatterns:{jan:/^jan(uary)?/i,feb:/^feb(ruary)?/i,mar:/^mar(ch)?/i,apr:/^apr(il)?/i,may:/^may/i,jun:/^jun(e)?/i,jul:/^jul(y)?/i,aug:/^aug(ust)?/i,sep:/^sep(t(ember)?)?/i,oct:/^oct(ober)?/i,nov:/^nov(ember)?/i,dec:/^dec(ember)?/i,sun:/^su(n(day)?)?/i,mon:/^mo(n(day)?)?/i,tue:/^tu(e(s(day)?)?)?/i,wed:/^we(d(nesday)?)?/i,thu:/^th(u(r(s(day)?)?)?)?/i,fri:/^fr(i(day)?)?/i,sat:/^sa(t(urday)?)?/i,future:/^next/i,past:/^last|past|prev(ious)?/i,add:/^(\\+|after|from)/i,subtract:/^(\\-|before|ago)/i,yesterday:/^yesterday/i,today:/^t(oday)?/i,tomorrow:/^tomorrow/i,now:/^n(ow)?/i,millisecond:/^ms|milli(second)?s?/i,second:/^sec(ond)?s?/i,minute:/^min(ute)?s?/i,hour:/^h(ou)?rs?/i,week:/^w(ee)?k/i,month:/^m(o(nth)?s?)?/i,day:/^d(ays?)?/i,year:/^y((ea)?rs?)?/i,shortMeridian:/^(a|p)/i,longMeridian:/^(a\\.?m?\\.?|p\\.?m?\\.?)/i,timezone:/^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt)/i,ordinalSuffix:/^\\s*(st|nd|rd|th)/i,timeContext:/^\\s*(\\:|a|p)/i},abbreviatedTimeZoneStandard:{GMT:"-000",EST:"-0400",CST:"-0500",MST:"-0600",PST:"-0700"},abbreviatedTimeZoneDST:{GMT:"-000",EDT:"-0500",CDT:"-0600",MDT:"-0700",PDT:"-0800"}};
',
  'en-GB' => 'Date.CultureInfo={name:"en-GB",englishName:"English (United Kingdom)",nativeName:"English (United Kingdom)",dayNames:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],abbreviatedDayNames:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],shortestDayNames:["Su","Mo","Tu","We","Th","Fr","Sa"],firstLetterDayNames:["S","M","T","W","T","F","S"],monthNames:["January","February","March","April","May","June","July","August","September","October","November","December"],abbreviatedMonthNames:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],amDesignator:"AM",pmDesignator:"PM",firstDayOfWeek:1,twoDigitYearMax:2029,dateElementOrder:"dmy",formatPatterns:{shortDate:"dd/MM/yyyy",longDate:"dd MMMM yyyy",shortTime:"HH:mm",longTime:"HH:mm:ss",fullDateTime:"dd MMMM yyyy HH:mm:ss",sortableDateTime:"yyyy-MM-ddTHH:mm:ss",universalSortableDateTime:"yyyy-MM-dd HH:mm:ssZ",rfc1123:"ddd, dd MMM yyyy HH:mm:ss GMT",monthDay:"dd MMMM",yearMonth:"MMMM yyyy"},regexPatterns:{jan:/^jan(uary)?/i,feb:/^feb(ruary)?/i,mar:/^mar(ch)?/i,apr:/^apr(il)?/i,may:/^may/i,jun:/^jun(e)?/i,jul:/^jul(y)?/i,aug:/^aug(ust)?/i,sep:/^sep(t(ember)?)?/i,oct:/^oct(ober)?/i,nov:/^nov(ember)?/i,dec:/^dec(ember)?/i,sun:/^su(n(day)?)?/i,mon:/^mo(n(day)?)?/i,tue:/^tu(e(s(day)?)?)?/i,wed:/^we(d(nesday)?)?/i,thu:/^th(u(r(s(day)?)?)?)?/i,fri:/^fr(i(day)?)?/i,sat:/^sa(t(urday)?)?/i,future:/^next/i,past:/^last|past|prev(ious)?/i,add:/^(\\+|after|from)/i,subtract:/^(\\-|before|ago)/i,yesterday:/^yesterday/i,today:/^t(oday)?/i,tomorrow:/^tomorrow/i,now:/^n(ow)?/i,millisecond:/^ms|milli(second)?s?/i,second:/^sec(ond)?s?/i,minute:/^min(ute)?s?/i,hour:/^h(ou)?rs?/i,week:/^w(ee)?k/i,month:/^m(o(nth)?s?)?/i,day:/^d(ays?)?/i,year:/^y((ea)?rs?)?/i,shortMeridian:/^(a|p)/i,longMeridian:/^(a\\.?m?\\.?|p\\.?m?\\.?)/i,timezone:/^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt)/i,ordinalSuffix:/^\\s*(st|nd|rd|th)/i,timeContext:/^\\s*(\\:|a|p)/i},abbreviatedTimeZoneStandard:{GMT:"-000",EST:"-0400",CST:"-0500",MST:"-0600",PST:"-0700"},abbreviatedTimeZoneDST:{GMT:"-000",EDT:"-0500",CDT:"-0600",MDT:"-0700",PDT:"-0800"}};
',
  'en-IE' => 'Date.CultureInfo={name:"en-IE",englishName:"English (Ireland)",nativeName:"English (Eire)",dayNames:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],abbreviatedDayNames:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],shortestDayNames:["Su","Mo","Tu","We","Th","Fr","Sa"],firstLetterDayNames:["S","M","T","W","T","F","S"],monthNames:["January","February","March","April","May","June","July","August","September","October","November","December"],abbreviatedMonthNames:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],amDesignator:"",pmDesignator:"",firstDayOfWeek:1,twoDigitYearMax:2029,dateElementOrder:"dmy",formatPatterns:{shortDate:"dd/MM/yyyy",longDate:"dd MMMM yyyy",shortTime:"HH:mm",longTime:"HH:mm:ss",fullDateTime:"dd MMMM yyyy HH:mm:ss",sortableDateTime:"yyyy-MM-ddTHH:mm:ss",universalSortableDateTime:"yyyy-MM-dd HH:mm:ssZ",rfc1123:"ddd, dd MMM yyyy HH:mm:ss GMT",monthDay:"dd MMMM",yearMonth:"MMMM yyyy"},regexPatterns:{jan:/^jan(uary)?/i,feb:/^feb(ruary)?/i,mar:/^mar(ch)?/i,apr:/^apr(il)?/i,may:/^may/i,jun:/^jun(e)?/i,jul:/^jul(y)?/i,aug:/^aug(ust)?/i,sep:/^sep(t(ember)?)?/i,oct:/^oct(ober)?/i,nov:/^nov(ember)?/i,dec:/^dec(ember)?/i,sun:/^su(n(day)?)?/i,mon:/^mo(n(day)?)?/i,tue:/^tu(e(s(day)?)?)?/i,wed:/^we(d(nesday)?)?/i,thu:/^th(u(r(s(day)?)?)?)?/i,fri:/^fr(i(day)?)?/i,sat:/^sa(t(urday)?)?/i,future:/^next/i,past:/^last|past|prev(ious)?/i,add:/^(\\+|after|from)/i,subtract:/^(\\-|before|ago)/i,yesterday:/^yesterday/i,today:/^t(oday)?/i,tomorrow:/^tomorrow/i,now:/^n(ow)?/i,millisecond:/^ms|milli(second)?s?/i,second:/^sec(ond)?s?/i,minute:/^min(ute)?s?/i,hour:/^h(ou)?rs?/i,week:/^w(ee)?k/i,month:/^m(o(nth)?s?)?/i,day:/^d(ays?)?/i,year:/^y((ea)?rs?)?/i,shortMeridian:/^(a|p)/i,longMeridian:/^(a\\.?m?\\.?|p\\.?m?\\.?)/i,timezone:/^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt)/i,ordinalSuffix:/^\\s*(st|nd|rd|th)/i,timeContext:/^\\s*(\\:|a|p)/i},abbreviatedTimeZoneStandard:{GMT:"-000",EST:"-0400",CST:"-0500",MST:"-0600",PST:"-0700"},abbreviatedTimeZoneDST:{GMT:"-000",EDT:"-0500",CDT:"-0600",MDT:"-0700",PDT:"-0800"}};
',
  'en-JM' => 'Date.CultureInfo={name:"en-JM",englishName:"English (Jamaica)",nativeName:"English (Jamaica)",dayNames:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],abbreviatedDayNames:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],shortestDayNames:["Su","Mo","Tu","We","Th","Fr","Sa"],firstLetterDayNames:["S","M","T","W","T","F","S"],monthNames:["January","February","March","April","May","June","July","August","September","October","November","December"],abbreviatedMonthNames:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],amDesignator:"AM",pmDesignator:"PM",firstDayOfWeek:0,twoDigitYearMax:2029,dateElementOrder:"dmy",formatPatterns:{shortDate:"dd/MM/yyyy",longDate:"dddd, MMMM dd, yyyy",shortTime:"hh:mm tt",longTime:"hh:mm:ss tt",fullDateTime:"dddd, MMMM dd, yyyy hh:mm:ss tt",sortableDateTime:"yyyy-MM-ddTHH:mm:ss",universalSortableDateTime:"yyyy-MM-dd HH:mm:ssZ",rfc1123:"ddd, dd MMM yyyy HH:mm:ss GMT",monthDay:"MMMM dd",yearMonth:"MMMM, yyyy"},regexPatterns:{jan:/^jan(uary)?/i,feb:/^feb(ruary)?/i,mar:/^mar(ch)?/i,apr:/^apr(il)?/i,may:/^may/i,jun:/^jun(e)?/i,jul:/^jul(y)?/i,aug:/^aug(ust)?/i,sep:/^sep(t(ember)?)?/i,oct:/^oct(ober)?/i,nov:/^nov(ember)?/i,dec:/^dec(ember)?/i,sun:/^su(n(day)?)?/i,mon:/^mo(n(day)?)?/i,tue:/^tu(e(s(day)?)?)?/i,wed:/^we(d(nesday)?)?/i,thu:/^th(u(r(s(day)?)?)?)?/i,fri:/^fr(i(day)?)?/i,sat:/^sa(t(urday)?)?/i,future:/^next/i,past:/^last|past|prev(ious)?/i,add:/^(\\+|after|from)/i,subtract:/^(\\-|before|ago)/i,yesterday:/^yesterday/i,today:/^t(oday)?/i,tomorrow:/^tomorrow/i,now:/^n(ow)?/i,millisecond:/^ms|milli(second)?s?/i,second:/^sec(ond)?s?/i,minute:/^min(ute)?s?/i,hour:/^h(ou)?rs?/i,week:/^w(ee)?k/i,month:/^m(o(nth)?s?)?/i,day:/^d(ays?)?/i,year:/^y((ea)?rs?)?/i,shortMeridian:/^(a|p)/i,longMeridian:/^(a\\.?m?\\.?|p\\.?m?\\.?)/i,timezone:/^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt)/i,ordinalSuffix:/^\\s*(st|nd|rd|th)/i,timeContext:/^\\s*(\\:|a|p)/i},abbreviatedTimeZoneStandard:{GMT:"-000",EST:"-0400",CST:"-0500",MST:"-0600",PST:"-0700"},abbreviatedTimeZoneDST:{GMT:"-000",EDT:"-0500",CDT:"-0600",MDT:"-0700",PDT:"-0800"}};
',
  'en-NZ' => 'Date.CultureInfo={name:"en-NZ",englishName:"English (New Zealand)",nativeName:"English (New Zealand)",dayNames:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],abbreviatedDayNames:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],shortestDayNames:["Su","Mo","Tu","We","Th","Fr","Sa"],firstLetterDayNames:["S","M","T","W","T","F","S"],monthNames:["January","February","March","April","May","June","July","August","September","October","November","December"],abbreviatedMonthNames:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],amDesignator:"a.m.",pmDesignator:"p.m.",firstDayOfWeek:1,twoDigitYearMax:2029,dateElementOrder:"dmy",formatPatterns:{shortDate:"d/MM/yyyy",longDate:"dddd, d MMMM yyyy",shortTime:"h:mm tt",longTime:"h:mm:ss tt",fullDateTime:"dddd, d MMMM yyyy h:mm:ss tt",sortableDateTime:"yyyy-MM-ddTHH:mm:ss",universalSortableDateTime:"yyyy-MM-dd HH:mm:ssZ",rfc1123:"ddd, dd MMM yyyy HH:mm:ss GMT",monthDay:"dd MMMM",yearMonth:"MMMM yyyy"},regexPatterns:{jan:/^jan(uary)?/i,feb:/^feb(ruary)?/i,mar:/^mar(ch)?/i,apr:/^apr(il)?/i,may:/^may/i,jun:/^jun(e)?/i,jul:/^jul(y)?/i,aug:/^aug(ust)?/i,sep:/^sep(t(ember)?)?/i,oct:/^oct(ober)?/i,nov:/^nov(ember)?/i,dec:/^dec(ember)?/i,sun:/^su(n(day)?)?/i,mon:/^mo(n(day)?)?/i,tue:/^tu(e(s(day)?)?)?/i,wed:/^we(d(nesday)?)?/i,thu:/^th(u(r(s(day)?)?)?)?/i,fri:/^fr(i(day)?)?/i,sat:/^sa(t(urday)?)?/i,future:/^next/i,past:/^last|past|prev(ious)?/i,add:/^(\\+|after|from)/i,subtract:/^(\\-|before|ago)/i,yesterday:/^yesterday/i,today:/^t(oday)?/i,tomorrow:/^tomorrow/i,now:/^n(ow)?/i,millisecond:/^ms|milli(second)?s?/i,second:/^sec(ond)?s?/i,minute:/^min(ute)?s?/i,hour:/^h(ou)?rs?/i,week:/^w(ee)?k/i,month:/^m(o(nth)?s?)?/i,day:/^d(ays?)?/i,year:/^y((ea)?rs?)?/i,shortMeridian:/^(a|p)/i,longMeridian:/^(a\\.?m?\\.?|p\\.?m?\\.?)/i,timezone:/^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt)/i,ordinalSuffix:/^\\s*(st|nd|rd|th)/i,timeContext:/^\\s*(\\:|a|p)/i},abbreviatedTimeZoneStandard:{GMT:"-000",EST:"-0400",CST:"-0500",MST:"-0600",PST:"-0700"},abbreviatedTimeZoneDST:{GMT:"-000",EDT:"-0500",CDT:"-0600",MDT:"-0700",PDT:"-0800"}};
',
  'en-PH' => 'Date.CultureInfo={name:"en-PH",englishName:"English (Republic of the Philippines)",nativeName:"English (Philippines)",dayNames:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],abbreviatedDayNames:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],shortestDayNames:["Su","Mo","Tu","We","Th","Fr","Sa"],firstLetterDayNames:["S","M","T","W","T","F","S"],monthNames:["January","February","March","April","May","June","July","August","September","October","November","December"],abbreviatedMonthNames:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],amDesignator:"AM",pmDesignator:"PM",firstDayOfWeek:0,twoDigitYearMax:2029,dateElementOrder:"mdy",formatPatterns:{shortDate:"M/d/yyyy",longDate:"dddd, MMMM dd, yyyy",shortTime:"h:mm tt",longTime:"h:mm:ss tt",fullDateTime:"dddd, MMMM dd, yyyy h:mm:ss tt",sortableDateTime:"yyyy-MM-ddTHH:mm:ss",universalSortableDateTime:"yyyy-MM-dd HH:mm:ssZ",rfc1123:"ddd, dd MMM yyyy HH:mm:ss GMT",monthDay:"MMMM dd",yearMonth:"MMMM, yyyy"},regexPatterns:{jan:/^jan(uary)?/i,feb:/^feb(ruary)?/i,mar:/^mar(ch)?/i,apr:/^apr(il)?/i,may:/^may/i,jun:/^jun(e)?/i,jul:/^jul(y)?/i,aug:/^aug(ust)?/i,sep:/^sep(t(ember)?)?/i,oct:/^oct(ober)?/i,nov:/^nov(ember)?/i,dec:/^dec(ember)?/i,sun:/^su(n(day)?)?/i,mon:/^mo(n(day)?)?/i,tue:/^tu(e(s(day)?)?)?/i,wed:/^we(d(nesday)?)?/i,thu:/^th(u(r(s(day)?)?)?)?/i,fri:/^fr(i(day)?)?/i,sat:/^sa(t(urday)?)?/i,future:/^next/i,past:/^last|past|prev(ious)?/i,add:/^(\\+|after|from)/i,subtract:/^(\\-|before|ago)/i,yesterday:/^yesterday/i,today:/^t(oday)?/i,tomorrow:/^tomorrow/i,now:/^n(ow)?/i,millisecond:/^ms|milli(second)?s?/i,second:/^sec(ond)?s?/i,minute:/^min(ute)?s?/i,hour:/^h(ou)?rs?/i,week:/^w(ee)?k/i,month:/^m(o(nth)?s?)?/i,day:/^d(ays?)?/i,year:/^y((ea)?rs?)?/i,shortMeridian:/^(a|p)/i,longMeridian:/^(a\\.?m?\\.?|p\\.?m?\\.?)/i,timezone:/^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt)/i,ordinalSuffix:/^\\s*(st|nd|rd|th)/i,timeContext:/^\\s*(\\:|a|p)/i},abbreviatedTimeZoneStandard:{GMT:"-000",EST:"-0400",CST:"-0500",MST:"-0600",PST:"-0700"},abbreviatedTimeZoneDST:{GMT:"-000",EDT:"-0500",CDT:"-0600",MDT:"-0700",PDT:"-0800"}};
',
  'en-TT' => 'Date.CultureInfo={name:"en-TT",englishName:"English (Trinidad and Tobago)",nativeName:"English (Trinidad y Tobago)",dayNames:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],abbreviatedDayNames:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],shortestDayNames:["Su","Mo","Tu","We","Th","Fr","Sa"],firstLetterDayNames:["S","M","T","W","T","F","S"],monthNames:["January","February","March","April","May","June","July","August","September","October","November","December"],abbreviatedMonthNames:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],amDesignator:"AM",pmDesignator:"PM",firstDayOfWeek:0,twoDigitYearMax:2029,dateElementOrder:"dmy",formatPatterns:{shortDate:"dd/MM/yyyy",longDate:"dddd, dd MMMM yyyy",shortTime:"hh:mm tt",longTime:"hh:mm:ss tt",fullDateTime:"dddd, dd MMMM yyyy hh:mm:ss tt",sortableDateTime:"yyyy-MM-ddTHH:mm:ss",universalSortableDateTime:"yyyy-MM-dd HH:mm:ssZ",rfc1123:"ddd, dd MMM yyyy HH:mm:ss GMT",monthDay:"dd MMMM",yearMonth:"MMMM yyyy"},regexPatterns:{jan:/^jan(uary)?/i,feb:/^feb(ruary)?/i,mar:/^mar(ch)?/i,apr:/^apr(il)?/i,may:/^may/i,jun:/^jun(e)?/i,jul:/^jul(y)?/i,aug:/^aug(ust)?/i,sep:/^sep(t(ember)?)?/i,oct:/^oct(ober)?/i,nov:/^nov(ember)?/i,dec:/^dec(ember)?/i,sun:/^su(n(day)?)?/i,mon:/^mo(n(day)?)?/i,tue:/^tu(e(s(day)?)?)?/i,wed:/^we(d(nesday)?)?/i,thu:/^th(u(r(s(day)?)?)?)?/i,fri:/^fr(i(day)?)?/i,sat:/^sa(t(urday)?)?/i,future:/^next/i,past:/^last|past|prev(ious)?/i,add:/^(\\+|after|from)/i,subtract:/^(\\-|before|ago)/i,yesterday:/^yesterday/i,today:/^t(oday)?/i,tomorrow:/^tomorrow/i,now:/^n(ow)?/i,millisecond:/^ms|milli(second)?s?/i,second:/^sec(ond)?s?/i,minute:/^min(ute)?s?/i,hour:/^h(ou)?rs?/i,week:/^w(ee)?k/i,month:/^m(o(nth)?s?)?/i,day:/^d(ays?)?/i,year:/^y((ea)?rs?)?/i,shortMeridian:/^(a|p)/i,longMeridian:/^(a\\.?m?\\.?|p\\.?m?\\.?)/i,timezone:/^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt)/i,ordinalSuffix:/^\\s*(st|nd|rd|th)/i,timeContext:/^\\s*(\\:|a|p)/i},abbreviatedTimeZoneStandard:{GMT:"-000",EST:"-0400",CST:"-0500",MST:"-0600",PST:"-0700"},abbreviatedTimeZoneDST:{GMT:"-000",EDT:"-0500",CDT:"-0600",MDT:"-0700",PDT:"-0800"}};
',
  'en-US' => 'Date.CultureInfo={name:"en-US",englishName:"English (United States)",nativeName:"English (United States)",dayNames:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],abbreviatedDayNames:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],shortestDayNames:["Su","Mo","Tu","We","Th","Fr","Sa"],firstLetterDayNames:["S","M","T","W","T","F","S"],monthNames:["January","February","March","April","May","June","July","August","September","October","November","December"],abbreviatedMonthNames:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],amDesignator:"AM",pmDesignator:"PM",firstDayOfWeek:0,twoDigitYearMax:2029,dateElementOrder:"mdy",formatPatterns:{shortDate:"M/d/yyyy",longDate:"dddd, MMMM dd, yyyy",shortTime:"h:mm tt",longTime:"h:mm:ss tt",fullDateTime:"dddd, MMMM dd, yyyy h:mm:ss tt",sortableDateTime:"yyyy-MM-ddTHH:mm:ss",universalSortableDateTime:"yyyy-MM-dd HH:mm:ssZ",rfc1123:"ddd, dd MMM yyyy HH:mm:ss GMT",monthDay:"MMMM dd",yearMonth:"MMMM, yyyy"},regexPatterns:{jan:/^jan(uary)?/i,feb:/^feb(ruary)?/i,mar:/^mar(ch)?/i,apr:/^apr(il)?/i,may:/^may/i,jun:/^jun(e)?/i,jul:/^jul(y)?/i,aug:/^aug(ust)?/i,sep:/^sep(t(ember)?)?/i,oct:/^oct(ober)?/i,nov:/^nov(ember)?/i,dec:/^dec(ember)?/i,sun:/^su(n(day)?)?/i,mon:/^mo(n(day)?)?/i,tue:/^tu(e(s(day)?)?)?/i,wed:/^we(d(nesday)?)?/i,thu:/^th(u(r(s(day)?)?)?)?/i,fri:/^fr(i(day)?)?/i,sat:/^sa(t(urday)?)?/i,future:/^next/i,past:/^last|past|prev(ious)?/i,add:/^(\\+|after|from)/i,subtract:/^(\\-|before|ago)/i,yesterday:/^yesterday/i,today:/^t(oday)?/i,tomorrow:/^tomorrow/i,now:/^n(ow)?/i,millisecond:/^ms|milli(second)?s?/i,second:/^sec(ond)?s?/i,minute:/^min(ute)?s?/i,hour:/^h(ou)?rs?/i,week:/^w(ee)?k/i,month:/^m(o(nth)?s?)?/i,day:/^d(ays?)?/i,year:/^y((ea)?rs?)?/i,shortMeridian:/^(a|p)/i,longMeridian:/^(a\\.?m?\\.?|p\\.?m?\\.?)/i,timezone:/^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt)/i,ordinalSuffix:/^\\s*(st|nd|rd|th)/i,timeContext:/^\\s*(\\:|a|p)/i},abbreviatedTimeZoneStandard:{GMT:"-000",EST:"-0400",CST:"-0500",MST:"-0600",PST:"-0700"},abbreviatedTimeZoneDST:{GMT:"-000",EDT:"-0500",CDT:"-0600",MDT:"-0700",PDT:"-0800"}};
',
  'en-ZA' => 'Date.CultureInfo={name:"en-ZA",englishName:"English (South Africa)",nativeName:"English (South Africa)",dayNames:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],abbreviatedDayNames:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],shortestDayNames:["Su","Mo","Tu","We","Th","Fr","Sa"],firstLetterDayNames:["S","M","T","W","T","F","S"],monthNames:["January","February","March","April","May","June","July","August","September","October","November","December"],abbreviatedMonthNames:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],amDesignator:"AM",pmDesignator:"PM",firstDayOfWeek:0,twoDigitYearMax:2029,dateElementOrder:"ymd",formatPatterns:{shortDate:"yyyy/MM/dd",longDate:"dd MMMM yyyy",shortTime:"hh:mm tt",longTime:"hh:mm:ss tt",fullDateTime:"dd MMMM yyyy hh:mm:ss tt",sortableDateTime:"yyyy-MM-ddTHH:mm:ss",universalSortableDateTime:"yyyy-MM-dd HH:mm:ssZ",rfc1123:"ddd, dd MMM yyyy HH:mm:ss GMT",monthDay:"dd MMMM",yearMonth:"MMMM yyyy"},regexPatterns:{jan:/^jan(uary)?/i,feb:/^feb(ruary)?/i,mar:/^mar(ch)?/i,apr:/^apr(il)?/i,may:/^may/i,jun:/^jun(e)?/i,jul:/^jul(y)?/i,aug:/^aug(ust)?/i,sep:/^sep(t(ember)?)?/i,oct:/^oct(ober)?/i,nov:/^nov(ember)?/i,dec:/^dec(ember)?/i,sun:/^su(n(day)?)?/i,mon:/^mo(n(day)?)?/i,tue:/^tu(e(s(day)?)?)?/i,wed:/^we(d(nesday)?)?/i,thu:/^th(u(r(s(day)?)?)?)?/i,fri:/^fr(i(day)?)?/i,sat:/^sa(t(urday)?)?/i,future:/^next/i,past:/^last|past|prev(ious)?/i,add:/^(\\+|after|from)/i,subtract:/^(\\-|before|ago)/i,yesterday:/^yesterday/i,today:/^t(oday)?/i,tomorrow:/^tomorrow/i,now:/^n(ow)?/i,millisecond:/^ms|milli(second)?s?/i,second:/^sec(ond)?s?/i,minute:/^min(ute)?s?/i,hour:/^h(ou)?rs?/i,week:/^w(ee)?k/i,month:/^m(o(nth)?s?)?/i,day:/^d(ays?)?/i,year:/^y((ea)?rs?)?/i,shortMeridian:/^(a|p)/i,longMeridian:/^(a\\.?m?\\.?|p\\.?m?\\.?)/i,timezone:/^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt)/i,ordinalSuffix:/^\\s*(st|nd|rd|th)/i,timeContext:/^\\s*(\\:|a|p)/i},abbreviatedTimeZoneStandard:{GMT:"-000",EST:"-0400",CST:"-0500",MST:"-0600",PST:"-0700"},abbreviatedTimeZoneDST:{GMT:"-000",EDT:"-0500",CDT:"-0600",MDT:"-0700",PDT:"-0800"}};
',
  'en-ZW' => 'Date.CultureInfo={name:"en-ZW",englishName:"English (Zimbabwe)",nativeName:"English (Zimbabwe)",dayNames:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],abbreviatedDayNames:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],shortestDayNames:["Su","Mo","Tu","We","Th","Fr","Sa"],firstLetterDayNames:["S","M","T","W","T","F","S"],monthNames:["January","February","March","April","May","June","July","August","September","October","November","December"],abbreviatedMonthNames:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],amDesignator:"AM",pmDesignator:"PM",firstDayOfWeek:0,twoDigitYearMax:2029,dateElementOrder:"mdy",formatPatterns:{shortDate:"M/d/yyyy",longDate:"dddd, MMMM dd, yyyy",shortTime:"h:mm tt",longTime:"h:mm:ss tt",fullDateTime:"dddd, MMMM dd, yyyy h:mm:ss tt",sortableDateTime:"yyyy-MM-ddTHH:mm:ss",universalSortableDateTime:"yyyy-MM-dd HH:mm:ssZ",rfc1123:"ddd, dd MMM yyyy HH:mm:ss GMT",monthDay:"MMMM dd",yearMonth:"MMMM, yyyy"},regexPatterns:{jan:/^jan(uary)?/i,feb:/^feb(ruary)?/i,mar:/^mar(ch)?/i,apr:/^apr(il)?/i,may:/^may/i,jun:/^jun(e)?/i,jul:/^jul(y)?/i,aug:/^aug(ust)?/i,sep:/^sep(t(ember)?)?/i,oct:/^oct(ober)?/i,nov:/^nov(ember)?/i,dec:/^dec(ember)?/i,sun:/^su(n(day)?)?/i,mon:/^mo(n(day)?)?/i,tue:/^tu(e(s(day)?)?)?/i,wed:/^we(d(nesday)?)?/i,thu:/^th(u(r(s(day)?)?)?)?/i,fri:/^fr(i(day)?)?/i,sat:/^sa(t(urday)?)?/i,future:/^next/i,past:/^last|past|prev(ious)?/i,add:/^(\\+|after|from)/i,subtract:/^(\\-|before|ago)/i,yesterday:/^yesterday/i,today:/^t(oday)?/i,tomorrow:/^tomorrow/i,now:/^n(ow)?/i,millisecond:/^ms|milli(second)?s?/i,second:/^sec(ond)?s?/i,minute:/^min(ute)?s?/i,hour:/^h(ou)?rs?/i,week:/^w(ee)?k/i,month:/^m(o(nth)?s?)?/i,day:/^d(ays?)?/i,year:/^y((ea)?rs?)?/i,shortMeridian:/^(a|p)/i,longMeridian:/^(a\\.?m?\\.?|p\\.?m?\\.?)/i,timezone:/^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt)/i,ordinalSuffix:/^\\s*(st|nd|rd|th)/i,timeContext:/^\\s*(\\:|a|p)/i},abbreviatedTimeZoneStandard:{GMT:"-000",EST:"-0400",CST:"-0500",MST:"-0600",PST:"-0700"},abbreviatedTimeZoneDST:{GMT:"-000",EDT:"-0500",CDT:"-0600",MDT:"-0700",PDT:"-0800"}};
',
  'es-AR' => 'Date.CultureInfo={name:"es-AR",englishName:"Spanish (Argentina)",nativeName:"Español (Argentina)",dayNames:["domingo","lunes","martes","miércoles","jueves","viernes","sábado"],abbreviatedDayNames:["dom","lun","mar","mié","jue","vie","sáb"],shortestDayNames:["do","lu","ma","mi","ju","vi","sá"],firstLetterDayNames:["d","l","m","m","j","v","s"],monthNames:["enero","febrero","marzo","abril","mayo","junio","julio","agosto","septiembre","octubre","noviembre","diciembre"],abbreviatedMonthNames:["ene","feb","mar","abr","may","jun","jul","ago","sep","oct","nov","dic"],amDesignator:"a.m.",pmDesignator:"p.m.",firstDayOfWeek:0,twoDigitYearMax:2029,dateElementOrder:"dmy",formatPatterns:{shortDate:"dd/MM/yyyy",longDate:"dddd, dd\' de \'MMMM\' de \'yyyy",shortTime:"hh:mm tt",longTime:"hh:mm:ss tt",fullDateTime:"dddd, dd\' de \'MMMM\' de \'yyyy hh:mm:ss tt",sortableDateTime:"yyyy-MM-ddTHH:mm:ss",universalSortableDateTime:"yyyy-MM-dd HH:mm:ssZ",rfc1123:"ddd, dd MMM yyyy HH:mm:ss GMT",monthDay:"dd MMMM",yearMonth:"MMMM\' de \'yyyy"},regexPatterns:{jan:/^ene(ro)?/i,feb:/^feb(rero)?/i,mar:/^mar(zo)?/i,apr:/^abr(il)?/i,may:/^may(o)?/i,jun:/^jun(io)?/i,jul:/^jul(io)?/i,aug:/^ago(sto)?/i,sep:/^sep(tiembre)?/i,oct:/^oct(ubre)?/i,nov:/^nov(iembre)?/i,dec:/^dic(iembre)?/i,sun:/^do(m(ingo)?)?/i,mon:/^lu(n(es)?)?/i,tue:/^ma(r(tes)?)?/i,wed:/^mi(é(rcoles)?)?/i,thu:/^ju(e(ves)?)?/i,fri:/^vi(e(rnes)?)?/i,sat:/^sá(b(ado)?)?/i,future:/^next/i,past:/^last|past|prev(ious)?/i,add:/^(\\+|after|from)/i,subtract:/^(\\-|before|ago)/i,yesterday:/^yesterday/i,today:/^t(oday)?/i,tomorrow:/^tomorrow/i,now:/^n(ow)?/i,millisecond:/^ms|milli(second)?s?/i,second:/^sec(ond)?s?/i,minute:/^min(ute)?s?/i,hour:/^h(ou)?rs?/i,week:/^w(ee)?k/i,month:/^m(o(nth)?s?)?/i,day:/^d(ays?)?/i,year:/^y((ea)?rs?)?/i,shortMeridian:/^(a|p)/i,longMeridian:/^(a\\.?m?\\.?|p\\.?m?\\.?)/i,timezone:/^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt)/i,ordinalSuffix:/^\\s*(st|nd|rd|th)/i,timeContext:/^\\s*(\\:|a|p)/i},abbreviatedTimeZoneStandard:{GMT:"-000",EST:"-0400",CST:"-0500",MST:"-0600",PST:"-0700"},abbreviatedTimeZoneDST:{GMT:"-000",EDT:"-0500",CDT:"-0600",MDT:"-0700",PDT:"-0800"}};
',
  'es-BO' => 'Date.CultureInfo={name:"es-BO",englishName:"Spanish (Bolivia)",nativeName:"Español (Bolivia)",dayNames:["domingo","lunes","martes","miércoles","jueves","viernes","sábado"],abbreviatedDayNames:["dom","lun","mar","mié","jue","vie","sáb"],shortestDayNames:["do","lu","ma","mi","ju","vi","sá"],firstLetterDayNames:["d","l","m","m","j","v","s"],monthNames:["enero","febrero","marzo","abril","mayo","junio","julio","agosto","septiembre","octubre","noviembre","diciembre"],abbreviatedMonthNames:["ene","feb","mar","abr","may","jun","jul","ago","sep","oct","nov","dic"],amDesignator:"a.m.",pmDesignator:"p.m.",firstDayOfWeek:0,twoDigitYearMax:2029,dateElementOrder:"dmy",formatPatterns:{shortDate:"dd/MM/yyyy",longDate:"dddd, dd\' de \'MMMM\' de \'yyyy",shortTime:"hh:mm tt",longTime:"hh:mm:ss tt",fullDateTime:"dddd, dd\' de \'MMMM\' de \'yyyy hh:mm:ss tt",sortableDateTime:"yyyy-MM-ddTHH:mm:ss",universalSortableDateTime:"yyyy-MM-dd HH:mm:ssZ",rfc1123:"ddd, dd MMM yyyy HH:mm:ss GMT",monthDay:"dd MMMM",yearMonth:"MMMM\' de \'yyyy"},regexPatterns:{jan:/^ene(ro)?/i,feb:/^feb(rero)?/i,mar:/^mar(zo)?/i,apr:/^abr(il)?/i,may:/^may(o)?/i,jun:/^jun(io)?/i,jul:/^jul(io)?/i,aug:/^ago(sto)?/i,sep:/^sep(tiembre)?/i,oct:/^oct(ubre)?/i,nov:/^nov(iembre)?/i,dec:/^dic(iembre)?/i,sun:/^do(m(ingo)?)?/i,mon:/^lu(n(es)?)?/i,tue:/^ma(r(tes)?)?/i,wed:/^mi(é(rcoles)?)?/i,thu:/^ju(e(ves)?)?/i,fri:/^vi(e(rnes)?)?/i,sat:/^sá(b(ado)?)?/i,future:/^next/i,past:/^last|past|prev(ious)?/i,add:/^(\\+|after|from)/i,subtract:/^(\\-|before|ago)/i,yesterday:/^yesterday/i,today:/^t(oday)?/i,tomorrow:/^tomorrow/i,now:/^n(ow)?/i,millisecond:/^ms|milli(second)?s?/i,second:/^sec(ond)?s?/i,minute:/^min(ute)?s?/i,hour:/^h(ou)?rs?/i,week:/^w(ee)?k/i,month:/^m(o(nth)?s?)?/i,day:/^d(ays?)?/i,year:/^y((ea)?rs?)?/i,shortMeridian:/^(a|p)/i,longMeridian:/^(a\\.?m?\\.?|p\\.?m?\\.?)/i,timezone:/^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt)/i,ordinalSuffix:/^\\s*(st|nd|rd|th)/i,timeContext:/^\\s*(\\:|a|p)/i},abbreviatedTimeZoneStandard:{GMT:"-000",EST:"-0400",CST:"-0500",MST:"-0600",PST:"-0700"},abbreviatedTimeZoneDST:{GMT:"-000",EDT:"-0500",CDT:"-0600",MDT:"-0700",PDT:"-0800"}};
',
  'es-CL' => 'Date.CultureInfo={name:"es-CL",englishName:"Spanish (Chile)",nativeName:"Español (Chile)",dayNames:["domingo","lunes","martes","miércoles","jueves","viernes","sábado"],abbreviatedDayNames:["dom","lun","mar","mié","jue","vie","sáb"],shortestDayNames:["do","lu","ma","mi","ju","vi","sá"],firstLetterDayNames:["d","l","m","m","j","v","s"],monthNames:["enero","febrero","marzo","abril","mayo","junio","julio","agosto","septiembre","octubre","noviembre","diciembre"],abbreviatedMonthNames:["ene","feb","mar","abr","may","jun","jul","ago","sep","oct","nov","dic"],amDesignator:"",pmDesignator:"",firstDayOfWeek:0,twoDigitYearMax:2029,dateElementOrder:"dmy",formatPatterns:{shortDate:"dd-MM-yyyy",longDate:"dddd, dd\' de \'MMMM\' de \'yyyy",shortTime:"H:mm",longTime:"H:mm:ss",fullDateTime:"dddd, dd\' de \'MMMM\' de \'yyyy H:mm:ss",sortableDateTime:"yyyy-MM-ddTHH:mm:ss",universalSortableDateTime:"yyyy-MM-dd HH:mm:ssZ",rfc1123:"ddd, dd MMM yyyy HH:mm:ss GMT",monthDay:"dd MMMM",yearMonth:"MMMM\' de \'yyyy"},regexPatterns:{jan:/^ene(ro)?/i,feb:/^feb(rero)?/i,mar:/^mar(zo)?/i,apr:/^abr(il)?/i,may:/^may(o)?/i,jun:/^jun(io)?/i,jul:/^jul(io)?/i,aug:/^ago(sto)?/i,sep:/^sep(tiembre)?/i,oct:/^oct(ubre)?/i,nov:/^nov(iembre)?/i,dec:/^dic(iembre)?/i,sun:/^do(m(ingo)?)?/i,mon:/^lu(n(es)?)?/i,tue:/^ma(r(tes)?)?/i,wed:/^mi(é(rcoles)?)?/i,thu:/^ju(e(ves)?)?/i,fri:/^vi(e(rnes)?)?/i,sat:/^sá(b(ado)?)?/i,future:/^next/i,past:/^last|past|prev(ious)?/i,add:/^(\\+|after|from)/i,subtract:/^(\\-|before|ago)/i,yesterday:/^yesterday/i,today:/^t(oday)?/i,tomorrow:/^tomorrow/i,now:/^n(ow)?/i,millisecond:/^ms|milli(second)?s?/i,second:/^sec(ond)?s?/i,minute:/^min(ute)?s?/i,hour:/^h(ou)?rs?/i,week:/^w(ee)?k/i,month:/^m(o(nth)?s?)?/i,day:/^d(ays?)?/i,year:/^y((ea)?rs?)?/i,shortMeridian:/^(a|p)/i,longMeridian:/^(a\\.?m?\\.?|p\\.?m?\\.?)/i,timezone:/^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt)/i,ordinalSuffix:/^\\s*(st|nd|rd|th)/i,timeContext:/^\\s*(\\:|a|p)/i},abbreviatedTimeZoneStandard:{GMT:"-000",EST:"-0400",CST:"-0500",MST:"-0600",PST:"-0700"},abbreviatedTimeZoneDST:{GMT:"-000",EDT:"-0500",CDT:"-0600",MDT:"-0700",PDT:"-0800"}};
',
  'es-CO' => 'Date.CultureInfo={name:"es-CO",englishName:"Spanish (Colombia)",nativeName:"Español (Colombia)",dayNames:["domingo","lunes","martes","miércoles","jueves","viernes","sábado"],abbreviatedDayNames:["dom","lun","mar","mié","jue","vie","sáb"],shortestDayNames:["do","lu","ma","mi","ju","vi","sá"],firstLetterDayNames:["d","l","m","m","j","v","s"],monthNames:["enero","febrero","marzo","abril","mayo","junio","julio","agosto","septiembre","octubre","noviembre","diciembre"],abbreviatedMonthNames:["ene","feb","mar","abr","may","jun","jul","ago","sep","oct","nov","dic"],amDesignator:"a.m.",pmDesignator:"p.m.",firstDayOfWeek:0,twoDigitYearMax:2029,dateElementOrder:"dmy",formatPatterns:{shortDate:"dd/MM/yyyy",longDate:"dddd, dd\' de \'MMMM\' de \'yyyy",shortTime:"hh:mm tt",longTime:"hh:mm:ss tt",fullDateTime:"dddd, dd\' de \'MMMM\' de \'yyyy hh:mm:ss tt",sortableDateTime:"yyyy-MM-ddTHH:mm:ss",universalSortableDateTime:"yyyy-MM-dd HH:mm:ssZ",rfc1123:"ddd, dd MMM yyyy HH:mm:ss GMT",monthDay:"dd MMMM",yearMonth:"MMMM\' de \'yyyy"},regexPatterns:{jan:/^ene(ro)?/i,feb:/^feb(rero)?/i,mar:/^mar(zo)?/i,apr:/^abr(il)?/i,may:/^may(o)?/i,jun:/^jun(io)?/i,jul:/^jul(io)?/i,aug:/^ago(sto)?/i,sep:/^sep(tiembre)?/i,oct:/^oct(ubre)?/i,nov:/^nov(iembre)?/i,dec:/^dic(iembre)?/i,sun:/^do(m(ingo)?)?/i,mon:/^lu(n(es)?)?/i,tue:/^ma(r(tes)?)?/i,wed:/^mi(é(rcoles)?)?/i,thu:/^ju(e(ves)?)?/i,fri:/^vi(e(rnes)?)?/i,sat:/^sá(b(ado)?)?/i,future:/^next/i,past:/^last|past|prev(ious)?/i,add:/^(\\+|after|from)/i,subtract:/^(\\-|before|ago)/i,yesterday:/^yesterday/i,today:/^t(oday)?/i,tomorrow:/^tomorrow/i,now:/^n(ow)?/i,millisecond:/^ms|milli(second)?s?/i,second:/^sec(ond)?s?/i,minute:/^min(ute)?s?/i,hour:/^h(ou)?rs?/i,week:/^w(ee)?k/i,month:/^m(o(nth)?s?)?/i,day:/^d(ays?)?/i,year:/^y((ea)?rs?)?/i,shortMeridian:/^(a|p)/i,longMeridian:/^(a\\.?m?\\.?|p\\.?m?\\.?)/i,timezone:/^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt)/i,ordinalSuffix:/^\\s*(st|nd|rd|th)/i,timeContext:/^\\s*(\\:|a|p)/i},abbreviatedTimeZoneStandard:{GMT:"-000",EST:"-0400",CST:"-0500",MST:"-0600",PST:"-0700"},abbreviatedTimeZoneDST:{GMT:"-000",EDT:"-0500",CDT:"-0600",MDT:"-0700",PDT:"-0800"}};
',
  'es-CR' => 'Date.CultureInfo={name:"es-CR",englishName:"Spanish (Costa Rica)",nativeName:"Español (Costa Rica)",dayNames:["domingo","lunes","martes","miércoles","jueves","viernes","sábado"],abbreviatedDayNames:["dom","lun","mar","mié","jue","vie","sáb"],shortestDayNames:["do","lu","ma","mi","ju","vi","sá"],firstLetterDayNames:["d","l","m","m","j","v","s"],monthNames:["enero","febrero","marzo","abril","mayo","junio","julio","agosto","septiembre","octubre","noviembre","diciembre"],abbreviatedMonthNames:["ene","feb","mar","abr","may","jun","jul","ago","sep","oct","nov","dic"],amDesignator:"a.m.",pmDesignator:"p.m.",firstDayOfWeek:0,twoDigitYearMax:2029,dateElementOrder:"dmy",formatPatterns:{shortDate:"dd/MM/yyyy",longDate:"dddd, dd\' de \'MMMM\' de \'yyyy",shortTime:"hh:mm tt",longTime:"hh:mm:ss tt",fullDateTime:"dddd, dd\' de \'MMMM\' de \'yyyy hh:mm:ss tt",sortableDateTime:"yyyy-MM-ddTHH:mm:ss",universalSortableDateTime:"yyyy-MM-dd HH:mm:ssZ",rfc1123:"ddd, dd MMM yyyy HH:mm:ss GMT",monthDay:"dd MMMM",yearMonth:"MMMM\' de \'yyyy"},regexPatterns:{jan:/^ene(ro)?/i,feb:/^feb(rero)?/i,mar:/^mar(zo)?/i,apr:/^abr(il)?/i,may:/^may(o)?/i,jun:/^jun(io)?/i,jul:/^jul(io)?/i,aug:/^ago(sto)?/i,sep:/^sep(tiembre)?/i,oct:/^oct(ubre)?/i,nov:/^nov(iembre)?/i,dec:/^dic(iembre)?/i,sun:/^do(m(ingo)?)?/i,mon:/^lu(n(es)?)?/i,tue:/^ma(r(tes)?)?/i,wed:/^mi(é(rcoles)?)?/i,thu:/^ju(e(ves)?)?/i,fri:/^vi(e(rnes)?)?/i,sat:/^sá(b(ado)?)?/i,future:/^next/i,past:/^last|past|prev(ious)?/i,add:/^(\\+|after|from)/i,subtract:/^(\\-|before|ago)/i,yesterday:/^yesterday/i,today:/^t(oday)?/i,tomorrow:/^tomorrow/i,now:/^n(ow)?/i,millisecond:/^ms|milli(second)?s?/i,second:/^sec(ond)?s?/i,minute:/^min(ute)?s?/i,hour:/^h(ou)?rs?/i,week:/^w(ee)?k/i,month:/^m(o(nth)?s?)?/i,day:/^d(ays?)?/i,year:/^y((ea)?rs?)?/i,shortMeridian:/^(a|p)/i,longMeridian:/^(a\\.?m?\\.?|p\\.?m?\\.?)/i,timezone:/^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt)/i,ordinalSuffix:/^\\s*(st|nd|rd|th)/i,timeContext:/^\\s*(\\:|a|p)/i},abbreviatedTimeZoneStandard:{GMT:"-000",EST:"-0400",CST:"-0500",MST:"-0600",PST:"-0700"},abbreviatedTimeZoneDST:{GMT:"-000",EDT:"-0500",CDT:"-0600",MDT:"-0700",PDT:"-0800"}};
',
  'es-DO' => 'Date.CultureInfo={name:"es-DO",englishName:"Spanish (Dominican Republic)",nativeName:"Español (República Dominicana)",dayNames:["domingo","lunes","martes","miércoles","jueves","viernes","sábado"],abbreviatedDayNames:["dom","lun","mar","mié","jue","vie","sáb"],shortestDayNames:["do","lu","ma","mi","ju","vi","sá"],firstLetterDayNames:["d","l","m","m","j","v","s"],monthNames:["enero","febrero","marzo","abril","mayo","junio","julio","agosto","septiembre","octubre","noviembre","diciembre"],abbreviatedMonthNames:["ene","feb","mar","abr","may","jun","jul","ago","sep","oct","nov","dic"],amDesignator:"a.m.",pmDesignator:"p.m.",firstDayOfWeek:0,twoDigitYearMax:2029,dateElementOrder:"dmy",formatPatterns:{shortDate:"dd/MM/yyyy",longDate:"dddd, dd\' de \'MMMM\' de \'yyyy",shortTime:"hh:mm tt",longTime:"hh:mm:ss tt",fullDateTime:"dddd, dd\' de \'MMMM\' de \'yyyy hh:mm:ss tt",sortableDateTime:"yyyy-MM-ddTHH:mm:ss",universalSortableDateTime:"yyyy-MM-dd HH:mm:ssZ",rfc1123:"ddd, dd MMM yyyy HH:mm:ss GMT",monthDay:"dd MMMM",yearMonth:"MMMM\' de \'yyyy"},regexPatterns:{jan:/^ene(ro)?/i,feb:/^feb(rero)?/i,mar:/^mar(zo)?/i,apr:/^abr(il)?/i,may:/^may(o)?/i,jun:/^jun(io)?/i,jul:/^jul(io)?/i,aug:/^ago(sto)?/i,sep:/^sep(tiembre)?/i,oct:/^oct(ubre)?/i,nov:/^nov(iembre)?/i,dec:/^dic(iembre)?/i,sun:/^do(m(ingo)?)?/i,mon:/^lu(n(es)?)?/i,tue:/^ma(r(tes)?)?/i,wed:/^mi(é(rcoles)?)?/i,thu:/^ju(e(ves)?)?/i,fri:/^vi(e(rnes)?)?/i,sat:/^sá(b(ado)?)?/i,future:/^next/i,past:/^last|past|prev(ious)?/i,add:/^(\\+|after|from)/i,subtract:/^(\\-|before|ago)/i,yesterday:/^yesterday/i,today:/^t(oday)?/i,tomorrow:/^tomorrow/i,now:/^n(ow)?/i,millisecond:/^ms|milli(second)?s?/i,second:/^sec(ond)?s?/i,minute:/^min(ute)?s?/i,hour:/^h(ou)?rs?/i,week:/^w(ee)?k/i,month:/^m(o(nth)?s?)?/i,day:/^d(ays?)?/i,year:/^y((ea)?rs?)?/i,shortMeridian:/^(a|p)/i,longMeridian:/^(a\\.?m?\\.?|p\\.?m?\\.?)/i,timezone:/^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt)/i,ordinalSuffix:/^\\s*(st|nd|rd|th)/i,timeContext:/^\\s*(\\:|a|p)/i},abbreviatedTimeZoneStandard:{GMT:"-000",EST:"-0400",CST:"-0500",MST:"-0600",PST:"-0700"},abbreviatedTimeZoneDST:{GMT:"-000",EDT:"-0500",CDT:"-0600",MDT:"-0700",PDT:"-0800"}};
',
  'es-EC' => 'Date.CultureInfo={name:"es-EC",englishName:"Spanish (Ecuador)",nativeName:"Español (Ecuador)",dayNames:["domingo","lunes","martes","miércoles","jueves","viernes","sábado"],abbreviatedDayNames:["dom","lun","mar","mié","jue","vie","sáb"],shortestDayNames:["do","lu","ma","mi","ju","vi","sá"],firstLetterDayNames:["d","l","m","m","j","v","s"],monthNames:["enero","febrero","marzo","abril","mayo","junio","julio","agosto","septiembre","octubre","noviembre","diciembre"],abbreviatedMonthNames:["ene","feb","mar","abr","may","jun","jul","ago","sep","oct","nov","dic"],amDesignator:"",pmDesignator:"",firstDayOfWeek:0,twoDigitYearMax:2029,dateElementOrder:"dmy",formatPatterns:{shortDate:"dd/MM/yyyy",longDate:"dddd, dd\' de \'MMMM\' de \'yyyy",shortTime:"H:mm",longTime:"H:mm:ss",fullDateTime:"dddd, dd\' de \'MMMM\' de \'yyyy H:mm:ss",sortableDateTime:"yyyy-MM-ddTHH:mm:ss",universalSortableDateTime:"yyyy-MM-dd HH:mm:ssZ",rfc1123:"ddd, dd MMM yyyy HH:mm:ss GMT",monthDay:"dd MMMM",yearMonth:"MMMM\' de \'yyyy"},regexPatterns:{jan:/^ene(ro)?/i,feb:/^feb(rero)?/i,mar:/^mar(zo)?/i,apr:/^abr(il)?/i,may:/^may(o)?/i,jun:/^jun(io)?/i,jul:/^jul(io)?/i,aug:/^ago(sto)?/i,sep:/^sep(tiembre)?/i,oct:/^oct(ubre)?/i,nov:/^nov(iembre)?/i,dec:/^dic(iembre)?/i,sun:/^do(m(ingo)?)?/i,mon:/^lu(n(es)?)?/i,tue:/^ma(r(tes)?)?/i,wed:/^mi(é(rcoles)?)?/i,thu:/^ju(e(ves)?)?/i,fri:/^vi(e(rnes)?)?/i,sat:/^sá(b(ado)?)?/i,future:/^next/i,past:/^last|past|prev(ious)?/i,add:/^(\\+|after|from)/i,subtract:/^(\\-|before|ago)/i,yesterday:/^yesterday/i,today:/^t(oday)?/i,tomorrow:/^tomorrow/i,now:/^n(ow)?/i,millisecond:/^ms|milli(second)?s?/i,second:/^sec(ond)?s?/i,minute:/^min(ute)?s?/i,hour:/^h(ou)?rs?/i,week:/^w(ee)?k/i,month:/^m(o(nth)?s?)?/i,day:/^d(ays?)?/i,year:/^y((ea)?rs?)?/i,shortMeridian:/^(a|p)/i,longMeridian:/^(a\\.?m?\\.?|p\\.?m?\\.?)/i,timezone:/^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt)/i,ordinalSuffix:/^\\s*(st|nd|rd|th)/i,timeContext:/^\\s*(\\:|a|p)/i},abbreviatedTimeZoneStandard:{GMT:"-000",EST:"-0400",CST:"-0500",MST:"-0600",PST:"-0700"},abbreviatedTimeZoneDST:{GMT:"-000",EDT:"-0500",CDT:"-0600",MDT:"-0700",PDT:"-0800"}};
',
  'es-ES' => 'Date.CultureInfo={name:"es-ES",englishName:"Spanish (Spain)",nativeName:"español (España)",dayNames:["domingo","lunes","martes","miércoles","jueves","viernes","sábado"],abbreviatedDayNames:["dom","lun","mar","mié","jue","vie","sáb"],shortestDayNames:["do","lu","ma","mi","ju","vi","sá"],firstLetterDayNames:["d","l","m","m","j","v","s"],monthNames:["enero","febrero","marzo","abril","mayo","junio","julio","agosto","septiembre","octubre","noviembre","diciembre"],abbreviatedMonthNames:["ene","feb","mar","abr","may","jun","jul","ago","sep","oct","nov","dic"],amDesignator:"",pmDesignator:"",firstDayOfWeek:1,twoDigitYearMax:2029,dateElementOrder:"dmy",formatPatterns:{shortDate:"dd/MM/yyyy",longDate:"dddd, dd\' de \'MMMM\' de \'yyyy",shortTime:"H:mm",longTime:"H:mm:ss",fullDateTime:"dddd, dd\' de \'MMMM\' de \'yyyy H:mm:ss",sortableDateTime:"yyyy-MM-ddTHH:mm:ss",universalSortableDateTime:"yyyy-MM-dd HH:mm:ssZ",rfc1123:"ddd, dd MMM yyyy HH:mm:ss GMT",monthDay:"dd MMMM",yearMonth:"MMMM\' de \'yyyy"},regexPatterns:{jan:/^ene(ro)?/i,feb:/^feb(rero)?/i,mar:/^mar(zo)?/i,apr:/^abr(il)?/i,may:/^may(o)?/i,jun:/^jun(io)?/i,jul:/^jul(io)?/i,aug:/^ago(sto)?/i,sep:/^sep(tiembre)?/i,oct:/^oct(ubre)?/i,nov:/^nov(iembre)?/i,dec:/^dic(iembre)?/i,sun:/^do(m(ingo)?)?/i,mon:/^lu(n(es)?)?/i,tue:/^ma(r(tes)?)?/i,wed:/^mi(é(rcoles)?)?/i,thu:/^ju(e(ves)?)?/i,fri:/^vi(e(rnes)?)?/i,sat:/^sá(b(ado)?)?/i,future:/^next/i,past:/^last|past|prev(ious)?/i,add:/^(\\+|after|from)/i,subtract:/^(\\-|before|ago)/i,yesterday:/^yesterday/i,today:/^t(oday)?/i,tomorrow:/^tomorrow/i,now:/^n(ow)?/i,millisecond:/^ms|milli(second)?s?/i,second:/^sec(ond)?s?/i,minute:/^min(ute)?s?/i,hour:/^h(ou)?rs?/i,week:/^w(ee)?k/i,month:/^m(o(nth)?s?)?/i,day:/^d(ays?)?/i,year:/^y((ea)?rs?)?/i,shortMeridian:/^(a|p)/i,longMeridian:/^(a\\.?m?\\.?|p\\.?m?\\.?)/i,timezone:/^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt)/i,ordinalSuffix:/^\\s*(st|nd|rd|th)/i,timeContext:/^\\s*(\\:|a|p)/i},abbreviatedTimeZoneStandard:{GMT:"-000",EST:"-0400",CST:"-0500",MST:"-0600",PST:"-0700"},abbreviatedTimeZoneDST:{GMT:"-000",EDT:"-0500",CDT:"-0600",MDT:"-0700",PDT:"-0800"}};
',
  'es-GT' => 'Date.CultureInfo={name:"es-GT",englishName:"Spanish (Guatemala)",nativeName:"Español (Guatemala)",dayNames:["domingo","lunes","martes","miércoles","jueves","viernes","sábado"],abbreviatedDayNames:["dom","lun","mar","mié","jue","vie","sáb"],shortestDayNames:["do","lu","ma","mi","ju","vi","sá"],firstLetterDayNames:["d","l","m","m","j","v","s"],monthNames:["enero","febrero","marzo","abril","mayo","junio","julio","agosto","septiembre","octubre","noviembre","diciembre"],abbreviatedMonthNames:["ene","feb","mar","abr","may","jun","jul","ago","sep","oct","nov","dic"],amDesignator:"a.m.",pmDesignator:"p.m.",firstDayOfWeek:0,twoDigitYearMax:2029,dateElementOrder:"dmy",formatPatterns:{shortDate:"dd/MM/yyyy",longDate:"dddd, dd\' de \'MMMM\' de \'yyyy",shortTime:"hh:mm tt",longTime:"hh:mm:ss tt",fullDateTime:"dddd, dd\' de \'MMMM\' de \'yyyy hh:mm:ss tt",sortableDateTime:"yyyy-MM-ddTHH:mm:ss",universalSortableDateTime:"yyyy-MM-dd HH:mm:ssZ",rfc1123:"ddd, dd MMM yyyy HH:mm:ss GMT",monthDay:"dd MMMM",yearMonth:"MMMM\' de \'yyyy"},regexPatterns:{jan:/^ene(ro)?/i,feb:/^feb(rero)?/i,mar:/^mar(zo)?/i,apr:/^abr(il)?/i,may:/^may(o)?/i,jun:/^jun(io)?/i,jul:/^jul(io)?/i,aug:/^ago(sto)?/i,sep:/^sep(tiembre)?/i,oct:/^oct(ubre)?/i,nov:/^nov(iembre)?/i,dec:/^dic(iembre)?/i,sun:/^do(m(ingo)?)?/i,mon:/^lu(n(es)?)?/i,tue:/^ma(r(tes)?)?/i,wed:/^mi(é(rcoles)?)?/i,thu:/^ju(e(ves)?)?/i,fri:/^vi(e(rnes)?)?/i,sat:/^sá(b(ado)?)?/i,future:/^next/i,past:/^last|past|prev(ious)?/i,add:/^(\\+|after|from)/i,subtract:/^(\\-|before|ago)/i,yesterday:/^yesterday/i,today:/^t(oday)?/i,tomorrow:/^tomorrow/i,now:/^n(ow)?/i,millisecond:/^ms|milli(second)?s?/i,second:/^sec(ond)?s?/i,minute:/^min(ute)?s?/i,hour:/^h(ou)?rs?/i,week:/^w(ee)?k/i,month:/^m(o(nth)?s?)?/i,day:/^d(ays?)?/i,year:/^y((ea)?rs?)?/i,shortMeridian:/^(a|p)/i,longMeridian:/^(a\\.?m?\\.?|p\\.?m?\\.?)/i,timezone:/^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt)/i,ordinalSuffix:/^\\s*(st|nd|rd|th)/i,timeContext:/^\\s*(\\:|a|p)/i},abbreviatedTimeZoneStandard:{GMT:"-000",EST:"-0400",CST:"-0500",MST:"-0600",PST:"-0700"},abbreviatedTimeZoneDST:{GMT:"-000",EDT:"-0500",CDT:"-0600",MDT:"-0700",PDT:"-0800"}};
',
  'es-HN' => 'Date.CultureInfo={name:"es-HN",englishName:"Spanish (Honduras)",nativeName:"Español (Honduras)",dayNames:["domingo","lunes","martes","miércoles","jueves","viernes","sábado"],abbreviatedDayNames:["dom","lun","mar","mié","jue","vie","sáb"],shortestDayNames:["do","lu","ma","mi","ju","vi","sá"],firstLetterDayNames:["d","l","m","m","j","v","s"],monthNames:["enero","febrero","marzo","abril","mayo","junio","julio","agosto","septiembre","octubre","noviembre","diciembre"],abbreviatedMonthNames:["ene","feb","mar","abr","may","jun","jul","ago","sep","oct","nov","dic"],amDesignator:"a.m.",pmDesignator:"p.m.",firstDayOfWeek:0,twoDigitYearMax:2029,dateElementOrder:"dmy",formatPatterns:{shortDate:"dd/MM/yyyy",longDate:"dddd, dd\' de \'MMMM\' de \'yyyy",shortTime:"hh:mm tt",longTime:"hh:mm:ss tt",fullDateTime:"dddd, dd\' de \'MMMM\' de \'yyyy hh:mm:ss tt",sortableDateTime:"yyyy-MM-ddTHH:mm:ss",universalSortableDateTime:"yyyy-MM-dd HH:mm:ssZ",rfc1123:"ddd, dd MMM yyyy HH:mm:ss GMT",monthDay:"dd MMMM",yearMonth:"MMMM\' de \'yyyy"},regexPatterns:{jan:/^ene(ro)?/i,feb:/^feb(rero)?/i,mar:/^mar(zo)?/i,apr:/^abr(il)?/i,may:/^may(o)?/i,jun:/^jun(io)?/i,jul:/^jul(io)?/i,aug:/^ago(sto)?/i,sep:/^sep(tiembre)?/i,oct:/^oct(ubre)?/i,nov:/^nov(iembre)?/i,dec:/^dic(iembre)?/i,sun:/^do(m(ingo)?)?/i,mon:/^lu(n(es)?)?/i,tue:/^ma(r(tes)?)?/i,wed:/^mi(é(rcoles)?)?/i,thu:/^ju(e(ves)?)?/i,fri:/^vi(e(rnes)?)?/i,sat:/^sá(b(ado)?)?/i,future:/^next/i,past:/^last|past|prev(ious)?/i,add:/^(\\+|after|from)/i,subtract:/^(\\-|before|ago)/i,yesterday:/^yesterday/i,today:/^t(oday)?/i,tomorrow:/^tomorrow/i,now:/^n(ow)?/i,millisecond:/^ms|milli(second)?s?/i,second:/^sec(ond)?s?/i,minute:/^min(ute)?s?/i,hour:/^h(ou)?rs?/i,week:/^w(ee)?k/i,month:/^m(o(nth)?s?)?/i,day:/^d(ays?)?/i,year:/^y((ea)?rs?)?/i,shortMeridian:/^(a|p)/i,longMeridian:/^(a\\.?m?\\.?|p\\.?m?\\.?)/i,timezone:/^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt)/i,ordinalSuffix:/^\\s*(st|nd|rd|th)/i,timeContext:/^\\s*(\\:|a|p)/i},abbreviatedTimeZoneStandard:{GMT:"-000",EST:"-0400",CST:"-0500",MST:"-0600",PST:"-0700"},abbreviatedTimeZoneDST:{GMT:"-000",EDT:"-0500",CDT:"-0600",MDT:"-0700",PDT:"-0800"}};
',
  'es-MX' => 'Date.CultureInfo={name:"es-MX",englishName:"Spanish (Mexico)",nativeName:"Español (México)",dayNames:["domingo","lunes","martes","miércoles","jueves","viernes","sábado"],abbreviatedDayNames:["dom","lun","mar","mié","jue","vie","sáb"],shortestDayNames:["do","lu","ma","mi","ju","vi","sá"],firstLetterDayNames:["d","l","m","m","j","v","s"],monthNames:["enero","febrero","marzo","abril","mayo","junio","julio","agosto","septiembre","octubre","noviembre","diciembre"],abbreviatedMonthNames:["ene","feb","mar","abr","may","jun","jul","ago","sep","oct","nov","dic"],amDesignator:"a.m.",pmDesignator:"p.m.",firstDayOfWeek:0,twoDigitYearMax:2029,dateElementOrder:"dmy",formatPatterns:{shortDate:"dd/MM/yyyy",longDate:"dddd, dd\' de \'MMMM\' de \'yyyy",shortTime:"hh:mm tt",longTime:"hh:mm:ss tt",fullDateTime:"dddd, dd\' de \'MMMM\' de \'yyyy hh:mm:ss tt",sortableDateTime:"yyyy-MM-ddTHH:mm:ss",universalSortableDateTime:"yyyy-MM-dd HH:mm:ssZ",rfc1123:"ddd, dd MMM yyyy HH:mm:ss GMT",monthDay:"dd MMMM",yearMonth:"MMMM\' de \'yyyy"},regexPatterns:{jan:/^ene(ro)?/i,feb:/^feb(rero)?/i,mar:/^mar(zo)?/i,apr:/^abr(il)?/i,may:/^may(o)?/i,jun:/^jun(io)?/i,jul:/^jul(io)?/i,aug:/^ago(sto)?/i,sep:/^sep(tiembre)?/i,oct:/^oct(ubre)?/i,nov:/^nov(iembre)?/i,dec:/^dic(iembre)?/i,sun:/^do(m(ingo)?)?/i,mon:/^lu(n(es)?)?/i,tue:/^ma(r(tes)?)?/i,wed:/^mi(é(rcoles)?)?/i,thu:/^ju(e(ves)?)?/i,fri:/^vi(e(rnes)?)?/i,sat:/^sá(b(ado)?)?/i,future:/^next/i,past:/^last|past|prev(ious)?/i,add:/^(\\+|after|from)/i,subtract:/^(\\-|before|ago)/i,yesterday:/^yesterday/i,today:/^t(oday)?/i,tomorrow:/^tomorrow/i,now:/^n(ow)?/i,millisecond:/^ms|milli(second)?s?/i,second:/^sec(ond)?s?/i,minute:/^min(ute)?s?/i,hour:/^h(ou)?rs?/i,week:/^w(ee)?k/i,month:/^m(o(nth)?s?)?/i,day:/^d(ays?)?/i,year:/^y((ea)?rs?)?/i,shortMeridian:/^(a|p)/i,longMeridian:/^(a\\.?m?\\.?|p\\.?m?\\.?)/i,timezone:/^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt)/i,ordinalSuffix:/^\\s*(st|nd|rd|th)/i,timeContext:/^\\s*(\\:|a|p)/i},abbreviatedTimeZoneStandard:{GMT:"-000",EST:"-0400",CST:"-0500",MST:"-0600",PST:"-0700"},abbreviatedTimeZoneDST:{GMT:"-000",EDT:"-0500",CDT:"-0600",MDT:"-0700",PDT:"-0800"}};
',
  'es-NI' => 'Date.CultureInfo={name:"es-NI",englishName:"Spanish (Nicaragua)",nativeName:"Español (Nicaragua)",dayNames:["domingo","lunes","martes","miércoles","jueves","viernes","sábado"],abbreviatedDayNames:["dom","lun","mar","mié","jue","vie","sáb"],shortestDayNames:["do","lu","ma","mi","ju","vi","sá"],firstLetterDayNames:["d","l","m","m","j","v","s"],monthNames:["enero","febrero","marzo","abril","mayo","junio","julio","agosto","septiembre","octubre","noviembre","diciembre"],abbreviatedMonthNames:["ene","feb","mar","abr","may","jun","jul","ago","sep","oct","nov","dic"],amDesignator:"a.m.",pmDesignator:"p.m.",firstDayOfWeek:0,twoDigitYearMax:2029,dateElementOrder:"dmy",formatPatterns:{shortDate:"dd/MM/yyyy",longDate:"dddd, dd\' de \'MMMM\' de \'yyyy",shortTime:"hh:mm tt",longTime:"hh:mm:ss tt",fullDateTime:"dddd, dd\' de \'MMMM\' de \'yyyy hh:mm:ss tt",sortableDateTime:"yyyy-MM-ddTHH:mm:ss",universalSortableDateTime:"yyyy-MM-dd HH:mm:ssZ",rfc1123:"ddd, dd MMM yyyy HH:mm:ss GMT",monthDay:"dd MMMM",yearMonth:"MMMM\' de \'yyyy"},regexPatterns:{jan:/^ene(ro)?/i,feb:/^feb(rero)?/i,mar:/^mar(zo)?/i,apr:/^abr(il)?/i,may:/^may(o)?/i,jun:/^jun(io)?/i,jul:/^jul(io)?/i,aug:/^ago(sto)?/i,sep:/^sep(tiembre)?/i,oct:/^oct(ubre)?/i,nov:/^nov(iembre)?/i,dec:/^dic(iembre)?/i,sun:/^do(m(ingo)?)?/i,mon:/^lu(n(es)?)?/i,tue:/^ma(r(tes)?)?/i,wed:/^mi(é(rcoles)?)?/i,thu:/^ju(e(ves)?)?/i,fri:/^vi(e(rnes)?)?/i,sat:/^sá(b(ado)?)?/i,future:/^next/i,past:/^last|past|prev(ious)?/i,add:/^(\\+|after|from)/i,subtract:/^(\\-|before|ago)/i,yesterday:/^yesterday/i,today:/^t(oday)?/i,tomorrow:/^tomorrow/i,now:/^n(ow)?/i,millisecond:/^ms|milli(second)?s?/i,second:/^sec(ond)?s?/i,minute:/^min(ute)?s?/i,hour:/^h(ou)?rs?/i,week:/^w(ee)?k/i,month:/^m(o(nth)?s?)?/i,day:/^d(ays?)?/i,year:/^y((ea)?rs?)?/i,shortMeridian:/^(a|p)/i,longMeridian:/^(a\\.?m?\\.?|p\\.?m?\\.?)/i,timezone:/^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt)/i,ordinalSuffix:/^\\s*(st|nd|rd|th)/i,timeContext:/^\\s*(\\:|a|p)/i},abbreviatedTimeZoneStandard:{GMT:"-000",EST:"-0400",CST:"-0500",MST:"-0600",PST:"-0700"},abbreviatedTimeZoneDST:{GMT:"-000",EDT:"-0500",CDT:"-0600",MDT:"-0700",PDT:"-0800"}};
',
  'es-PA' => 'Date.CultureInfo={name:"es-PA",englishName:"Spanish (Panama)",nativeName:"Español (Panamá)",dayNames:["domingo","lunes","martes","miércoles","jueves","viernes","sábado"],abbreviatedDayNames:["dom","lun","mar","mié","jue","vie","sáb"],shortestDayNames:["do","lu","ma","mi","ju","vi","sá"],firstLetterDayNames:["d","l","m","m","j","v","s"],monthNames:["enero","febrero","marzo","abril","mayo","junio","julio","agosto","septiembre","octubre","noviembre","diciembre"],abbreviatedMonthNames:["ene","feb","mar","abr","may","jun","jul","ago","sep","oct","nov","dic"],amDesignator:"a.m.",pmDesignator:"p.m.",firstDayOfWeek:0,twoDigitYearMax:2029,dateElementOrder:"mdy",formatPatterns:{shortDate:"MM/dd/yyyy",longDate:"dddd, dd\' de \'MMMM\' de \'yyyy",shortTime:"hh:mm tt",longTime:"hh:mm:ss tt",fullDateTime:"dddd, dd\' de \'MMMM\' de \'yyyy hh:mm:ss tt",sortableDateTime:"yyyy-MM-ddTHH:mm:ss",universalSortableDateTime:"yyyy-MM-dd HH:mm:ssZ",rfc1123:"ddd, dd MMM yyyy HH:mm:ss GMT",monthDay:"dd MMMM",yearMonth:"MMMM\' de \'yyyy"},regexPatterns:{jan:/^ene(ro)?/i,feb:/^feb(rero)?/i,mar:/^mar(zo)?/i,apr:/^abr(il)?/i,may:/^may(o)?/i,jun:/^jun(io)?/i,jul:/^jul(io)?/i,aug:/^ago(sto)?/i,sep:/^sep(tiembre)?/i,oct:/^oct(ubre)?/i,nov:/^nov(iembre)?/i,dec:/^dic(iembre)?/i,sun:/^do(m(ingo)?)?/i,mon:/^lu(n(es)?)?/i,tue:/^ma(r(tes)?)?/i,wed:/^mi(é(rcoles)?)?/i,thu:/^ju(e(ves)?)?/i,fri:/^vi(e(rnes)?)?/i,sat:/^sá(b(ado)?)?/i,future:/^next/i,past:/^last|past|prev(ious)?/i,add:/^(\\+|after|from)/i,subtract:/^(\\-|before|ago)/i,yesterday:/^yesterday/i,today:/^t(oday)?/i,tomorrow:/^tomorrow/i,now:/^n(ow)?/i,millisecond:/^ms|milli(second)?s?/i,second:/^sec(ond)?s?/i,minute:/^min(ute)?s?/i,hour:/^h(ou)?rs?/i,week:/^w(ee)?k/i,month:/^m(o(nth)?s?)?/i,day:/^d(ays?)?/i,year:/^y((ea)?rs?)?/i,shortMeridian:/^(a|p)/i,longMeridian:/^(a\\.?m?\\.?|p\\.?m?\\.?)/i,timezone:/^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt)/i,ordinalSuffix:/^\\s*(st|nd|rd|th)/i,timeContext:/^\\s*(\\:|a|p)/i},abbreviatedTimeZoneStandard:{GMT:"-000",EST:"-0400",CST:"-0500",MST:"-0600",PST:"-0700"},abbreviatedTimeZoneDST:{GMT:"-000",EDT:"-0500",CDT:"-0600",MDT:"-0700",PDT:"-0800"}};
',
  'es-PE' => 'Date.CultureInfo={name:"es-PE",englishName:"Spanish (Peru)",nativeName:"Español (Perú)",dayNames:["domingo","lunes","martes","miércoles","jueves","viernes","sábado"],abbreviatedDayNames:["dom","lun","mar","mié","jue","vie","sáb"],shortestDayNames:["do","lu","ma","mi","ju","vi","sá"],firstLetterDayNames:["d","l","m","m","j","v","s"],monthNames:["enero","febrero","marzo","abril","mayo","junio","julio","agosto","septiembre","octubre","noviembre","diciembre"],abbreviatedMonthNames:["ene","feb","mar","abr","may","jun","jul","ago","sep","oct","nov","dic"],amDesignator:"a.m.",pmDesignator:"p.m.",firstDayOfWeek:0,twoDigitYearMax:2029,dateElementOrder:"dmy",formatPatterns:{shortDate:"dd/MM/yyyy",longDate:"dddd, dd\' de \'MMMM\' de \'yyyy",shortTime:"hh:mm tt",longTime:"hh:mm:ss tt",fullDateTime:"dddd, dd\' de \'MMMM\' de \'yyyy hh:mm:ss tt",sortableDateTime:"yyyy-MM-ddTHH:mm:ss",universalSortableDateTime:"yyyy-MM-dd HH:mm:ssZ",rfc1123:"ddd, dd MMM yyyy HH:mm:ss GMT",monthDay:"dd MMMM",yearMonth:"MMMM\' de \'yyyy"},regexPatterns:{jan:/^ene(ro)?/i,feb:/^feb(rero)?/i,mar:/^mar(zo)?/i,apr:/^abr(il)?/i,may:/^may(o)?/i,jun:/^jun(io)?/i,jul:/^jul(io)?/i,aug:/^ago(sto)?/i,sep:/^sep(tiembre)?/i,oct:/^oct(ubre)?/i,nov:/^nov(iembre)?/i,dec:/^dic(iembre)?/i,sun:/^do(m(ingo)?)?/i,mon:/^lu(n(es)?)?/i,tue:/^ma(r(tes)?)?/i,wed:/^mi(é(rcoles)?)?/i,thu:/^ju(e(ves)?)?/i,fri:/^vi(e(rnes)?)?/i,sat:/^sá(b(ado)?)?/i,future:/^next/i,past:/^last|past|prev(ious)?/i,add:/^(\\+|after|from)/i,subtract:/^(\\-|before|ago)/i,yesterday:/^yesterday/i,today:/^t(oday)?/i,tomorrow:/^tomorrow/i,now:/^n(ow)?/i,millisecond:/^ms|milli(second)?s?/i,second:/^sec(ond)?s?/i,minute:/^min(ute)?s?/i,hour:/^h(ou)?rs?/i,week:/^w(ee)?k/i,month:/^m(o(nth)?s?)?/i,day:/^d(ays?)?/i,year:/^y((ea)?rs?)?/i,shortMeridian:/^(a|p)/i,longMeridian:/^(a\\.?m?\\.?|p\\.?m?\\.?)/i,timezone:/^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt)/i,ordinalSuffix:/^\\s*(st|nd|rd|th)/i,timeContext:/^\\s*(\\:|a|p)/i},abbreviatedTimeZoneStandard:{GMT:"-000",EST:"-0400",CST:"-0500",MST:"-0600",PST:"-0700"},abbreviatedTimeZoneDST:{GMT:"-000",EDT:"-0500",CDT:"-0600",MDT:"-0700",PDT:"-0800"}};
',
  'es-PR' => 'Date.CultureInfo={name:"es-PR",englishName:"Spanish (Puerto Rico)",nativeName:"Español (Puerto Rico)",dayNames:["domingo","lunes","martes","miércoles","jueves","viernes","sábado"],abbreviatedDayNames:["dom","lun","mar","mié","jue","vie","sáb"],shortestDayNames:["do","lu","ma","mi","ju","vi","sá"],firstLetterDayNames:["d","l","m","m","j","v","s"],monthNames:["enero","febrero","marzo","abril","mayo","junio","julio","agosto","septiembre","octubre","noviembre","diciembre"],abbreviatedMonthNames:["ene","feb","mar","abr","may","jun","jul","ago","sep","oct","nov","dic"],amDesignator:"a.m.",pmDesignator:"p.m.",firstDayOfWeek:0,twoDigitYearMax:2029,dateElementOrder:"dmy",formatPatterns:{shortDate:"dd/MM/yyyy",longDate:"dddd, dd\' de \'MMMM\' de \'yyyy",shortTime:"hh:mm tt",longTime:"hh:mm:ss tt",fullDateTime:"dddd, dd\' de \'MMMM\' de \'yyyy hh:mm:ss tt",sortableDateTime:"yyyy-MM-ddTHH:mm:ss",universalSortableDateTime:"yyyy-MM-dd HH:mm:ssZ",rfc1123:"ddd, dd MMM yyyy HH:mm:ss GMT",monthDay:"dd MMMM",yearMonth:"MMMM\' de \'yyyy"},regexPatterns:{jan:/^ene(ro)?/i,feb:/^feb(rero)?/i,mar:/^mar(zo)?/i,apr:/^abr(il)?/i,may:/^may(o)?/i,jun:/^jun(io)?/i,jul:/^jul(io)?/i,aug:/^ago(sto)?/i,sep:/^sep(tiembre)?/i,oct:/^oct(ubre)?/i,nov:/^nov(iembre)?/i,dec:/^dic(iembre)?/i,sun:/^do(m(ingo)?)?/i,mon:/^lu(n(es)?)?/i,tue:/^ma(r(tes)?)?/i,wed:/^mi(é(rcoles)?)?/i,thu:/^ju(e(ves)?)?/i,fri:/^vi(e(rnes)?)?/i,sat:/^sá(b(ado)?)?/i,future:/^next/i,past:/^last|past|prev(ious)?/i,add:/^(\\+|after|from)/i,subtract:/^(\\-|before|ago)/i,yesterday:/^yesterday/i,today:/^t(oday)?/i,tomorrow:/^tomorrow/i,now:/^n(ow)?/i,millisecond:/^ms|milli(second)?s?/i,second:/^sec(ond)?s?/i,minute:/^min(ute)?s?/i,hour:/^h(ou)?rs?/i,week:/^w(ee)?k/i,month:/^m(o(nth)?s?)?/i,day:/^d(ays?)?/i,year:/^y((ea)?rs?)?/i,shortMeridian:/^(a|p)/i,longMeridian:/^(a\\.?m?\\.?|p\\.?m?\\.?)/i,timezone:/^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt)/i,ordinalSuffix:/^\\s*(st|nd|rd|th)/i,timeContext:/^\\s*(\\:|a|p)/i},abbreviatedTimeZoneStandard:{GMT:"-000",EST:"-0400",CST:"-0500",MST:"-0600",PST:"-0700"},abbreviatedTimeZoneDST:{GMT:"-000",EDT:"-0500",CDT:"-0600",MDT:"-0700",PDT:"-0800"}};
',
  'es-PY' => 'Date.CultureInfo={name:"es-PY",englishName:"Spanish (Paraguay)",nativeName:"Español (Paraguay)",dayNames:["domingo","lunes","martes","miércoles","jueves","viernes","sábado"],abbreviatedDayNames:["dom","lun","mar","mié","jue","vie","sáb"],shortestDayNames:["do","lu","ma","mi","ju","vi","sá"],firstLetterDayNames:["d","l","m","m","j","v","s"],monthNames:["enero","febrero","marzo","abril","mayo","junio","julio","agosto","septiembre","octubre","noviembre","diciembre"],abbreviatedMonthNames:["ene","feb","mar","abr","may","jun","jul","ago","sep","oct","nov","dic"],amDesignator:"a.m.",pmDesignator:"p.m.",firstDayOfWeek:1,twoDigitYearMax:2029,dateElementOrder:"dmy",formatPatterns:{shortDate:"dd/MM/yyyy",longDate:"dddd, dd\' de \'MMMM\' de \'yyyy",shortTime:"hh:mm tt",longTime:"hh:mm:ss tt",fullDateTime:"dddd, dd\' de \'MMMM\' de \'yyyy hh:mm:ss tt",sortableDateTime:"yyyy-MM-ddTHH:mm:ss",universalSortableDateTime:"yyyy-MM-dd HH:mm:ssZ",rfc1123:"ddd, dd MMM yyyy HH:mm:ss GMT",monthDay:"dd MMMM",yearMonth:"MMMM\' de \'yyyy"},regexPatterns:{jan:/^ene(ro)?/i,feb:/^feb(rero)?/i,mar:/^mar(zo)?/i,apr:/^abr(il)?/i,may:/^may(o)?/i,jun:/^jun(io)?/i,jul:/^jul(io)?/i,aug:/^ago(sto)?/i,sep:/^sep(tiembre)?/i,oct:/^oct(ubre)?/i,nov:/^nov(iembre)?/i,dec:/^dic(iembre)?/i,sun:/^do(m(ingo)?)?/i,mon:/^lu(n(es)?)?/i,tue:/^ma(r(tes)?)?/i,wed:/^mi(é(rcoles)?)?/i,thu:/^ju(e(ves)?)?/i,fri:/^vi(e(rnes)?)?/i,sat:/^sá(b(ado)?)?/i,future:/^next/i,past:/^last|past|prev(ious)?/i,add:/^(\\+|after|from)/i,subtract:/^(\\-|before|ago)/i,yesterday:/^yesterday/i,today:/^t(oday)?/i,tomorrow:/^tomorrow/i,now:/^n(ow)?/i,millisecond:/^ms|milli(second)?s?/i,second:/^sec(ond)?s?/i,minute:/^min(ute)?s?/i,hour:/^h(ou)?rs?/i,week:/^w(ee)?k/i,month:/^m(o(nth)?s?)?/i,day:/^d(ays?)?/i,year:/^y((ea)?rs?)?/i,shortMeridian:/^(a|p)/i,longMeridian:/^(a\\.?m?\\.?|p\\.?m?\\.?)/i,timezone:/^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt)/i,ordinalSuffix:/^\\s*(st|nd|rd|th)/i,timeContext:/^\\s*(\\:|a|p)/i},abbreviatedTimeZoneStandard:{GMT:"-000",EST:"-0400",CST:"-0500",MST:"-0600",PST:"-0700"},abbreviatedTimeZoneDST:{GMT:"-000",EDT:"-0500",CDT:"-0600",MDT:"-0700",PDT:"-0800"}};
',
  'es-SV' => 'Date.CultureInfo={name:"es-SV",englishName:"Spanish (El Salvador)",nativeName:"Español (El Salvador)",dayNames:["domingo","lunes","martes","miércoles","jueves","viernes","sábado"],abbreviatedDayNames:["dom","lun","mar","mié","jue","vie","sáb"],shortestDayNames:["do","lu","ma","mi","ju","vi","sá"],firstLetterDayNames:["d","l","m","m","j","v","s"],monthNames:["enero","febrero","marzo","abril","mayo","junio","julio","agosto","septiembre","octubre","noviembre","diciembre"],abbreviatedMonthNames:["ene","feb","mar","abr","may","jun","jul","ago","sep","oct","nov","dic"],amDesignator:"a.m.",pmDesignator:"p.m.",firstDayOfWeek:0,twoDigitYearMax:2029,dateElementOrder:"dmy",formatPatterns:{shortDate:"dd/MM/yyyy",longDate:"dddd, dd\' de \'MMMM\' de \'yyyy",shortTime:"hh:mm tt",longTime:"hh:mm:ss tt",fullDateTime:"dddd, dd\' de \'MMMM\' de \'yyyy hh:mm:ss tt",sortableDateTime:"yyyy-MM-ddTHH:mm:ss",universalSortableDateTime:"yyyy-MM-dd HH:mm:ssZ",rfc1123:"ddd, dd MMM yyyy HH:mm:ss GMT",monthDay:"dd MMMM",yearMonth:"MMMM\' de \'yyyy"},regexPatterns:{jan:/^ene(ro)?/i,feb:/^feb(rero)?/i,mar:/^mar(zo)?/i,apr:/^abr(il)?/i,may:/^may(o)?/i,jun:/^jun(io)?/i,jul:/^jul(io)?/i,aug:/^ago(sto)?/i,sep:/^sep(tiembre)?/i,oct:/^oct(ubre)?/i,nov:/^nov(iembre)?/i,dec:/^dic(iembre)?/i,sun:/^do(m(ingo)?)?/i,mon:/^lu(n(es)?)?/i,tue:/^ma(r(tes)?)?/i,wed:/^mi(é(rcoles)?)?/i,thu:/^ju(e(ves)?)?/i,fri:/^vi(e(rnes)?)?/i,sat:/^sá(b(ado)?)?/i,future:/^next/i,past:/^last|past|prev(ious)?/i,add:/^(\\+|after|from)/i,subtract:/^(\\-|before|ago)/i,yesterday:/^yesterday/i,today:/^t(oday)?/i,tomorrow:/^tomorrow/i,now:/^n(ow)?/i,millisecond:/^ms|milli(second)?s?/i,second:/^sec(ond)?s?/i,minute:/^min(ute)?s?/i,hour:/^h(ou)?rs?/i,week:/^w(ee)?k/i,month:/^m(o(nth)?s?)?/i,day:/^d(ays?)?/i,year:/^y((ea)?rs?)?/i,shortMeridian:/^(a|p)/i,longMeridian:/^(a\\.?m?\\.?|p\\.?m?\\.?)/i,timezone:/^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt)/i,ordinalSuffix:/^\\s*(st|nd|rd|th)/i,timeContext:/^\\s*(\\:|a|p)/i},abbreviatedTimeZoneStandard:{GMT:"-000",EST:"-0400",CST:"-0500",MST:"-0600",PST:"-0700"},abbreviatedTimeZoneDST:{GMT:"-000",EDT:"-0500",CDT:"-0600",MDT:"-0700",PDT:"-0800"}};
',
  'es-UY' => 'Date.CultureInfo={name:"es-UY",englishName:"Spanish (Uruguay)",nativeName:"Español (Uruguay)",dayNames:["domingo","lunes","martes","miércoles","jueves","viernes","sábado"],abbreviatedDayNames:["dom","lun","mar","mié","jue","vie","sáb"],shortestDayNames:["do","lu","ma","mi","ju","vi","sá"],firstLetterDayNames:["d","l","m","m","j","v","s"],monthNames:["enero","febrero","marzo","abril","mayo","junio","julio","agosto","septiembre","octubre","noviembre","diciembre"],abbreviatedMonthNames:["ene","feb","mar","abr","may","jun","jul","ago","sep","oct","nov","dic"],amDesignator:"a.m.",pmDesignator:"p.m.",firstDayOfWeek:1,twoDigitYearMax:2029,dateElementOrder:"dmy",formatPatterns:{shortDate:"dd/MM/yyyy",longDate:"dddd, dd\' de \'MMMM\' de \'yyyy",shortTime:"hh:mm tt",longTime:"hh:mm:ss tt",fullDateTime:"dddd, dd\' de \'MMMM\' de \'yyyy hh:mm:ss tt",sortableDateTime:"yyyy-MM-ddTHH:mm:ss",universalSortableDateTime:"yyyy-MM-dd HH:mm:ssZ",rfc1123:"ddd, dd MMM yyyy HH:mm:ss GMT",monthDay:"dd MMMM",yearMonth:"MMMM\' de \'yyyy"},regexPatterns:{jan:/^ene(ro)?/i,feb:/^feb(rero)?/i,mar:/^mar(zo)?/i,apr:/^abr(il)?/i,may:/^may(o)?/i,jun:/^jun(io)?/i,jul:/^jul(io)?/i,aug:/^ago(sto)?/i,sep:/^sep(tiembre)?/i,oct:/^oct(ubre)?/i,nov:/^nov(iembre)?/i,dec:/^dic(iembre)?/i,sun:/^do(m(ingo)?)?/i,mon:/^lu(n(es)?)?/i,tue:/^ma(r(tes)?)?/i,wed:/^mi(é(rcoles)?)?/i,thu:/^ju(e(ves)?)?/i,fri:/^vi(e(rnes)?)?/i,sat:/^sá(b(ado)?)?/i,future:/^next/i,past:/^last|past|prev(ious)?/i,add:/^(\\+|after|from)/i,subtract:/^(\\-|before|ago)/i,yesterday:/^yesterday/i,today:/^t(oday)?/i,tomorrow:/^tomorrow/i,now:/^n(ow)?/i,millisecond:/^ms|milli(second)?s?/i,second:/^sec(ond)?s?/i,minute:/^min(ute)?s?/i,hour:/^h(ou)?rs?/i,week:/^w(ee)?k/i,month:/^m(o(nth)?s?)?/i,day:/^d(ays?)?/i,year:/^y((ea)?rs?)?/i,shortMeridian:/^(a|p)/i,longMeridian:/^(a\\.?m?\\.?|p\\.?m?\\.?)/i,timezone:/^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt)/i,ordinalSuffix:/^\\s*(st|nd|rd|th)/i,timeContext:/^\\s*(\\:|a|p)/i},abbreviatedTimeZoneStandard:{GMT:"-000",EST:"-0400",CST:"-0500",MST:"-0600",PST:"-0700"},abbreviatedTimeZoneDST:{GMT:"-000",EDT:"-0500",CDT:"-0600",MDT:"-0700",PDT:"-0800"}};
',
  'es-VE' => 'Date.CultureInfo={name:"es-VE",englishName:"Spanish (Venezuela)",nativeName:"Español (Republica Bolivariana de Venezuela)",dayNames:["domingo","lunes","martes","miércoles","jueves","viernes","sábado"],abbreviatedDayNames:["dom","lun","mar","mié","jue","vie","sáb"],shortestDayNames:["do","lu","ma","mi","ju","vi","sá"],firstLetterDayNames:["d","l","m","m","j","v","s"],monthNames:["enero","febrero","marzo","abril","mayo","junio","julio","agosto","septiembre","octubre","noviembre","diciembre"],abbreviatedMonthNames:["ene","feb","mar","abr","may","jun","jul","ago","sep","oct","nov","dic"],amDesignator:"a.m.",pmDesignator:"p.m.",firstDayOfWeek:0,twoDigitYearMax:2029,dateElementOrder:"dmy",formatPatterns:{shortDate:"dd/MM/yyyy",longDate:"dddd, dd\' de \'MMMM\' de \'yyyy",shortTime:"hh:mm tt",longTime:"hh:mm:ss tt",fullDateTime:"dddd, dd\' de \'MMMM\' de \'yyyy hh:mm:ss tt",sortableDateTime:"yyyy-MM-ddTHH:mm:ss",universalSortableDateTime:"yyyy-MM-dd HH:mm:ssZ",rfc1123:"ddd, dd MMM yyyy HH:mm:ss GMT",monthDay:"dd MMMM",yearMonth:"MMMM\' de \'yyyy"},regexPatterns:{jan:/^ene(ro)?/i,feb:/^feb(rero)?/i,mar:/^mar(zo)?/i,apr:/^abr(il)?/i,may:/^may(o)?/i,jun:/^jun(io)?/i,jul:/^jul(io)?/i,aug:/^ago(sto)?/i,sep:/^sep(tiembre)?/i,oct:/^oct(ubre)?/i,nov:/^nov(iembre)?/i,dec:/^dic(iembre)?/i,sun:/^do(m(ingo)?)?/i,mon:/^lu(n(es)?)?/i,tue:/^ma(r(tes)?)?/i,wed:/^mi(é(rcoles)?)?/i,thu:/^ju(e(ves)?)?/i,fri:/^vi(e(rnes)?)?/i,sat:/^sá(b(ado)?)?/i,future:/^next/i,past:/^last|past|prev(ious)?/i,add:/^(\\+|after|from)/i,subtract:/^(\\-|before|ago)/i,yesterday:/^yesterday/i,today:/^t(oday)?/i,tomorrow:/^tomorrow/i,now:/^n(ow)?/i,millisecond:/^ms|milli(second)?s?/i,second:/^sec(ond)?s?/i,minute:/^min(ute)?s?/i,hour:/^h(ou)?rs?/i,week:/^w(ee)?k/i,month:/^m(o(nth)?s?)?/i,day:/^d(ays?)?/i,year:/^y((ea)?rs?)?/i,shortMeridian:/^(a|p)/i,longMeridian:/^(a\\.?m?\\.?|p\\.?m?\\.?)/i,timezone:/^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt)/i,ordinalSuffix:/^\\s*(st|nd|rd|th)/i,timeContext:/^\\s*(\\:|a|p)/i},abbreviatedTimeZoneStandard:{GMT:"-000",EST:"-0400",CST:"-0500",MST:"-0600",PST:"-0700"},abbreviatedTimeZoneDST:{GMT:"-000",EDT:"-0500",CDT:"-0600",MDT:"-0700",PDT:"-0800"}};
',
  'et-EE' => 'Date.CultureInfo={name:"et-EE",englishName:"Estonian (Estonia)",nativeName:"eesti (Eesti)",dayNames:["pühapäev","esmaspäev","teisipäev","kolmapäev","neljapäev","reede","laupäev"],abbreviatedDayNames:["P","E","T","K","N","R","L"],shortestDayNames:["P","E","T","K","N","R","L"],firstLetterDayNames:["P","E","T","K","N","R","L"],monthNames:["jaanuar","veebruar","märts","aprill","mai","juuni","juuli","august","september","oktoober","november","detsember"],abbreviatedMonthNames:["jaan","veebr","märts","apr","mai","juuni","juuli","aug","sept","okt","nov","dets"],amDesignator:"EL",pmDesignator:"PL",firstDayOfWeek:1,twoDigitYearMax:2029,dateElementOrder:"dmy",formatPatterns:{shortDate:"d.MM.yyyy",longDate:"d. MMMM yyyy\'. a.\'",shortTime:"H:mm",longTime:"H:mm:ss",fullDateTime:"d. MMMM yyyy\'. a.\' H:mm:ss",sortableDateTime:"yyyy-MM-ddTHH:mm:ss",universalSortableDateTime:"yyyy-MM-dd HH:mm:ssZ",rfc1123:"ddd, dd MMM yyyy HH:mm:ss GMT",monthDay:"d. MMMM",yearMonth:"MMMM yyyy\'. a.\'"},regexPatterns:{jan:/^jaan(uar)?/i,feb:/^veebr(uar)?/i,mar:/^märts/i,apr:/^apr(ill)?/i,may:/^mai/i,jun:/^juuni/i,jul:/^juuli/i,aug:/^aug(ust)?/i,sep:/^sep(t(ember)?)?/i,oct:/^okt(oober)?/i,nov:/^nov(ember)?/i,dec:/^dets(ember)?/i,sun:/^pühapäev/i,mon:/^esmaspäev/i,tue:/^teisipäev/i,wed:/^kolmapäev/i,thu:/^neljapäev/i,fri:/^reede/i,sat:/^laupäev/i,future:/^next/i,past:/^last|past|prev(ious)?/i,add:/^(\\+|after|from)/i,subtract:/^(\\-|before|ago)/i,yesterday:/^yesterday/i,today:/^t(oday)?/i,tomorrow:/^tomorrow/i,now:/^n(ow)?/i,millisecond:/^ms|milli(second)?s?/i,second:/^sec(ond)?s?/i,minute:/^min(ute)?s?/i,hour:/^h(ou)?rs?/i,week:/^w(ee)?k/i,month:/^m(o(nth)?s?)?/i,day:/^d(ays?)?/i,year:/^y((ea)?rs?)?/i,shortMeridian:/^(a|p)/i,longMeridian:/^(a\\.?m?\\.?|p\\.?m?\\.?)/i,timezone:/^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt)/i,ordinalSuffix:/^\\s*(st|nd|rd|th)/i,timeContext:/^\\s*(\\:|a|p)/i},abbreviatedTimeZoneStandard:{GMT:"-000",EST:"-0400",CST:"-0500",MST:"-0600",PST:"-0700"},abbreviatedTimeZoneDST:{GMT:"-000",EDT:"-0500",CDT:"-0600",MDT:"-0700",PDT:"-0800"}};
',
  'eu-ES' => 'Date.CultureInfo={name:"eu-ES",englishName:"Basque (Basque)",nativeName:"euskara (euskara)",dayNames:["igandea","astelehena","asteartea","asteazkena","osteguna","ostirala","larunbata"],abbreviatedDayNames:["ig.","al.","as.","az.","og.","or.","lr."],shortestDayNames:["ig","al","as","az","og","or","lr"],firstLetterDayNames:["i","a","a","a","o","o","l"],monthNames:["urtarrila","otsaila","martxoa","apirila","maiatza","ekaina","uztaila","abuztua","iraila","urria","azaroa","abendua"],abbreviatedMonthNames:["urt.","ots.","mar.","api.","mai.","eka.","uzt.","abu.","ira.","urr.","aza.","abe."],amDesignator:"",pmDesignator:"",firstDayOfWeek:1,twoDigitYearMax:2029,dateElementOrder:"ymd",formatPatterns:{shortDate:"yyyy/MM/dd",longDate:"dddd, yyyy.\'eko\' MMMM\'k \'d",shortTime:"HH:mm",longTime:"HH:mm:ss",fullDateTime:"dddd, yyyy.\'eko\' MMMM\'k \'d HH:mm:ss",sortableDateTime:"yyyy-MM-ddTHH:mm:ss",universalSortableDateTime:"yyyy-MM-dd HH:mm:ssZ",rfc1123:"ddd, dd MMM yyyy HH:mm:ss GMT",monthDay:"MMMM dd",yearMonth:"yyyy.\'eko\' MMMM"},regexPatterns:{jan:/^urt(.(arrila)?)?/i,feb:/^ots(.(aila)?)?/i,mar:/^mar(.(txoa)?)?/i,apr:/^api(.(rila)?)?/i,may:/^mai(.(atza)?)?/i,jun:/^eka(.(ina)?)?/i,jul:/^uzt(.(aila)?)?/i,aug:/^abu(.(ztua)?)?/i,sep:/^ira(.(ila)?)?/i,oct:/^urr(.(ia)?)?/i,nov:/^aza(.(roa)?)?/i,dec:/^abe(.(ndua)?)?/i,sun:/^ig((.(andea)?)?)?/i,mon:/^al((.(telehena)?)?)?/i,tue:/^as((.(teartea)?)?)?/i,wed:/^az((.(teazkena)?)?)?/i,thu:/^og((.(teguna)?)?)?/i,fri:/^or((.(tirala)?)?)?/i,sat:/^lr((.(runbata)?)?)?/i,future:/^next/i,past:/^last|past|prev(ious)?/i,add:/^(\\+|after|from)/i,subtract:/^(\\-|before|ago)/i,yesterday:/^yesterday/i,today:/^t(oday)?/i,tomorrow:/^tomorrow/i,now:/^n(ow)?/i,millisecond:/^ms|milli(second)?s?/i,second:/^sec(ond)?s?/i,minute:/^min(ute)?s?/i,hour:/^h(ou)?rs?/i,week:/^w(ee)?k/i,month:/^m(o(nth)?s?)?/i,day:/^d(ays?)?/i,year:/^y((ea)?rs?)?/i,shortMeridian:/^(a|p)/i,longMeridian:/^(a\\.?m?\\.?|p\\.?m?\\.?)/i,timezone:/^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt)/i,ordinalSuffix:/^\\s*(st|nd|rd|th)/i,timeContext:/^\\s*(\\:|a|p)/i},abbreviatedTimeZoneStandard:{GMT:"-000",EST:"-0400",CST:"-0500",MST:"-0600",PST:"-0700"},abbreviatedTimeZoneDST:{GMT:"-000",EDT:"-0500",CDT:"-0600",MDT:"-0700",PDT:"-0800"}};
',
  'fa-IR' => 'Date.CultureInfo={name:"fa-IR",englishName:"Persian (Iran)",nativeName:"فارسى (ايران)",dayNames:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],abbreviatedDayNames:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],shortestDayNames:["Su","Mo","Tu","We","Th","Fr","Sa"],firstLetterDayNames:["S","M","T","W","T","F","S"],monthNames:["January","February","March","April","May","June","July","August","September","October","November","December"],abbreviatedMonthNames:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],amDesignator:"ق.ظ",pmDesignator:"ب.ظ",firstDayOfWeek:0,twoDigitYearMax:2029,dateElementOrder:"mdy",formatPatterns:{shortDate:"M/d/yyyy",longDate:"dddd, MMMM dd, yyyy",shortTime:"hh:mm tt",longTime:"hh:mm:ss tt",fullDateTime:"dddd, MMMM dd, yyyy hh:mm:ss tt",sortableDateTime:"yyyy-MM-ddTHH:mm:ss",universalSortableDateTime:"yyyy-MM-dd HH:mm:ssZ",rfc1123:"ddd, dd MMM yyyy HH:mm:ss GMT",monthDay:"MMMM dd",yearMonth:"MMMM, yyyy"},regexPatterns:{jan:/^jan(uary)?/i,feb:/^feb(ruary)?/i,mar:/^mar(ch)?/i,apr:/^apr(il)?/i,may:/^may/i,jun:/^jun(e)?/i,jul:/^jul(y)?/i,aug:/^aug(ust)?/i,sep:/^sep(t(ember)?)?/i,oct:/^oct(ober)?/i,nov:/^nov(ember)?/i,dec:/^dec(ember)?/i,sun:/^su(n(day)?)?/i,mon:/^mo(n(day)?)?/i,tue:/^tu(e(s(day)?)?)?/i,wed:/^we(d(nesday)?)?/i,thu:/^th(u(r(s(day)?)?)?)?/i,fri:/^fr(i(day)?)?/i,sat:/^sa(t(urday)?)?/i,future:/^next/i,past:/^last|past|prev(ious)?/i,add:/^(\\+|after|from)/i,subtract:/^(\\-|before|ago)/i,yesterday:/^yesterday/i,today:/^t(oday)?/i,tomorrow:/^tomorrow/i,now:/^n(ow)?/i,millisecond:/^ms|milli(second)?s?/i,second:/^sec(ond)?s?/i,minute:/^min(ute)?s?/i,hour:/^h(ou)?rs?/i,week:/^w(ee)?k/i,month:/^m(o(nth)?s?)?/i,day:/^d(ays?)?/i,year:/^y((ea)?rs?)?/i,shortMeridian:/^(a|p)/i,longMeridian:/^(a\\.?m?\\.?|p\\.?m?\\.?)/i,timezone:/^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt)/i,ordinalSuffix:/^\\s*(st|nd|rd|th)/i,timeContext:/^\\s*(\\:|a|p)/i},abbreviatedTimeZoneStandard:{GMT:"-000",EST:"-0400",CST:"-0500",MST:"-0600",PST:"-0700"},abbreviatedTimeZoneDST:{GMT:"-000",EDT:"-0500",CDT:"-0600",MDT:"-0700",PDT:"-0800"}};
',
  'fi-FI' => 'Date.CultureInfo={name:"fi-FI",englishName:"Finnish (Finland)",nativeName:"suomi (Suomi)",dayNames:["sunnuntai","maanantai","tiistai","keskiviikko","torstai","perjantai","lauantai"],abbreviatedDayNames:["su","ma","ti","ke","to","pe","la"],shortestDayNames:["su","ma","ti","ke","to","pe","la"],firstLetterDayNames:["s","m","t","k","t","p","l"],monthNames:["tammikuu","helmikuu","maaliskuu","huhtikuu","toukokuu","kesäkuu","heinäkuu","elokuu","syyskuu","lokakuu","marraskuu","joulukuu"],abbreviatedMonthNames:["tammi","helmi","maalis","huhti","touko","kesä","heinä","elo","syys","loka","marras","joulu"],amDesignator:"",pmDesignator:"",firstDayOfWeek:1,twoDigitYearMax:2029,dateElementOrder:"dmy",formatPatterns:{shortDate:"d.M.yyyy",longDate:"d. MMMM\'ta \'yyyy",shortTime:"H:mm",longTime:"H:mm:ss",fullDateTime:"d. MMMM\'ta \'yyyy H:mm:ss",sortableDateTime:"yyyy-MM-ddTHH:mm:ss",universalSortableDateTime:"yyyy-MM-dd HH:mm:ssZ",rfc1123:"ddd, dd MMM yyyy HH:mm:ss GMT",monthDay:"d. MMMM\'ta\'",yearMonth:"MMMM yyyy"},regexPatterns:{jan:/^tammi(kuu)?/i,feb:/^helmi(kuu)?/i,mar:/^maalis(kuu)?/i,apr:/^huhti(kuu)?/i,may:/^touko(kuu)?/i,jun:/^kesä(kuu)?/i,jul:/^heinä(kuu)?/i,aug:/^elo(kuu)?/i,sep:/^syys(kuu)?/i,oct:/^loka(kuu)?/i,nov:/^marras(kuu)?/i,dec:/^joulu(kuu)?/i,sun:/^sunnuntai/i,mon:/^maanantai/i,tue:/^tiistai/i,wed:/^keskiviikko/i,thu:/^torstai/i,fri:/^perjantai/i,sat:/^lauantai/i,future:/^next/i,past:/^last|past|prev(ious)?/i,add:/^(\\+|after|from)/i,subtract:/^(\\-|before|ago)/i,yesterday:/^yesterday/i,today:/^t(oday)?/i,tomorrow:/^tomorrow/i,now:/^n(ow)?/i,millisecond:/^ms|milli(second)?s?/i,second:/^sec(ond)?s?/i,minute:/^min(ute)?s?/i,hour:/^h(ou)?rs?/i,week:/^w(ee)?k/i,month:/^m(o(nth)?s?)?/i,day:/^d(ays?)?/i,year:/^y((ea)?rs?)?/i,shortMeridian:/^(a|p)/i,longMeridian:/^(a\\.?m?\\.?|p\\.?m?\\.?)/i,timezone:/^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt)/i,ordinalSuffix:/^\\s*(st|nd|rd|th)/i,timeContext:/^\\s*(\\:|a|p)/i},abbreviatedTimeZoneStandard:{GMT:"-000",EST:"-0400",CST:"-0500",MST:"-0600",PST:"-0700"},abbreviatedTimeZoneDST:{GMT:"-000",EDT:"-0500",CDT:"-0600",MDT:"-0700",PDT:"-0800"}};
',
  'fo-FO' => 'Date.CultureInfo={name:"fo-FO",englishName:"Faroese (Faroe Islands)",nativeName:"føroyskt (Føroyar)",dayNames:["sunnudagur","mánadagur","týsdagur","mikudagur","hósdagur","fríggjadagur","leygardagur"],abbreviatedDayNames:["sun","mán","týs","mik","hós","frí","leyg"],shortestDayNames:["su","má","tý","mi","hó","fr","ley"],firstLetterDayNames:["s","m","t","m","h","f","l"],monthNames:["januar","februar","mars","apríl","mai","juni","juli","august","september","oktober","november","desember"],abbreviatedMonthNames:["jan","feb","mar","apr","mai","jun","jul","aug","sep","okt","nov","des"],amDesignator:"",pmDesignator:"",firstDayOfWeek:1,twoDigitYearMax:2029,dateElementOrder:"dmy",formatPatterns:{shortDate:"dd-MM-yyyy",longDate:"d. MMMM yyyy",shortTime:"HH.mm",longTime:"HH.mm.ss",fullDateTime:"d. MMMM yyyy HH.mm.ss",sortableDateTime:"yyyy-MM-ddTHH:mm:ss",universalSortableDateTime:"yyyy-MM-dd HH:mm:ssZ",rfc1123:"ddd, dd MMM yyyy HH:mm:ss GMT",monthDay:"d. MMMM",yearMonth:"MMMM yyyy"},regexPatterns:{jan:/^jan(uar)?/i,feb:/^feb(ruar)?/i,mar:/^mar(s)?/i,apr:/^apr(íl)?/i,may:/^mai/i,jun:/^jun(i)?/i,jul:/^jul(i)?/i,aug:/^aug(ust)?/i,sep:/^sep(t(ember)?)?/i,oct:/^okt(ober)?/i,nov:/^nov(ember)?/i,dec:/^des(ember)?/i,sun:/^su(n(nudagur)?)?/i,mon:/^má(n(adagur)?)?/i,tue:/^tý(s(dagur)?)?/i,wed:/^mi(k(udagur)?)?/i,thu:/^hó(s(dagur)?)?/i,fri:/^fr(í(ggjadagur)?)?/i,sat:/^ley(g(ardagur)?)?/i,future:/^next/i,past:/^last|past|prev(ious)?/i,add:/^(\\+|after|from)/i,subtract:/^(\\-|before|ago)/i,yesterday:/^yesterday/i,today:/^t(oday)?/i,tomorrow:/^tomorrow/i,now:/^n(ow)?/i,millisecond:/^ms|milli(second)?s?/i,second:/^sec(ond)?s?/i,minute:/^min(ute)?s?/i,hour:/^h(ou)?rs?/i,week:/^w(ee)?k/i,month:/^m(o(nth)?s?)?/i,day:/^d(ays?)?/i,year:/^y((ea)?rs?)?/i,shortMeridian:/^(a|p)/i,longMeridian:/^(a\\.?m?\\.?|p\\.?m?\\.?)/i,timezone:/^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt)/i,ordinalSuffix:/^\\s*(st|nd|rd|th)/i,timeContext:/^\\s*(\\:|a|p)/i},abbreviatedTimeZoneStandard:{GMT:"-000",EST:"-0400",CST:"-0500",MST:"-0600",PST:"-0700"},abbreviatedTimeZoneDST:{GMT:"-000",EDT:"-0500",CDT:"-0600",MDT:"-0700",PDT:"-0800"}};
',
  'fr-BE' => 'Date.CultureInfo={name:"fr-BE",englishName:"French (Belgium)",nativeName:"français (Belgique)",dayNames:["dimanche","lundi","mardi","mercredi","jeudi","vendredi","samedi"],abbreviatedDayNames:["dim.","lun.","mar.","mer.","jeu.","ven.","sam."],shortestDayNames:["di","lu","ma","me","je","ve","sa"],firstLetterDayNames:["d","l","m","m","j","v","s"],monthNames:["janvier","février","mars","avril","mai","juin","juillet","août","septembre","octobre","novembre","décembre"],abbreviatedMonthNames:["janv.","févr.","mars","avr.","mai","juin","juil.","août","sept.","oct.","nov.","déc."],amDesignator:"",pmDesignator:"",firstDayOfWeek:1,twoDigitYearMax:2029,dateElementOrder:"dmy",formatPatterns:{shortDate:"d/MM/yyyy",longDate:"dddd d MMMM yyyy",shortTime:"H:mm",longTime:"H:mm:ss",fullDateTime:"dddd d MMMM yyyy H:mm:ss",sortableDateTime:"yyyy-MM-ddTHH:mm:ss",universalSortableDateTime:"yyyy-MM-dd HH:mm:ssZ",rfc1123:"ddd, dd MMM yyyy HH:mm:ss GMT",monthDay:"d MMMM",yearMonth:"MMMM yyyy"},regexPatterns:{jan:/^janv(.(ier)?)?/i,feb:/^févr(.(ier)?)?/i,mar:/^mars/i,apr:/^avr(.(il)?)?/i,may:/^mai/i,jun:/^juin/i,jul:/^juil(.(let)?)?/i,aug:/^août/i,sep:/^sept(.(embre)?)?/i,oct:/^oct(.(obre)?)?/i,nov:/^nov(.(embre)?)?/i,dec:/^déc(.(embre)?)?/i,sun:/^di(m(.(anche)?)?)?/i,mon:/^lu(n(.(di)?)?)?/i,tue:/^ma(r(.(di)?)?)?/i,wed:/^me(r(.(credi)?)?)?/i,thu:/^je(u(.(di)?)?)?/i,fri:/^ve(n(.(dredi)?)?)?/i,sat:/^sa(m(.(edi)?)?)?/i,future:/^next/i,past:/^last|past|prev(ious)?/i,add:/^(\\+|after|from)/i,subtract:/^(\\-|before|ago)/i,yesterday:/^yesterday/i,today:/^t(oday)?/i,tomorrow:/^tomorrow/i,now:/^n(ow)?/i,millisecond:/^ms|milli(second)?s?/i,second:/^sec(ond)?s?/i,minute:/^min(ute)?s?/i,hour:/^h(ou)?rs?/i,week:/^w(ee)?k/i,month:/^m(o(nth)?s?)?/i,day:/^d(ays?)?/i,year:/^y((ea)?rs?)?/i,shortMeridian:/^(a|p)/i,longMeridian:/^(a\\.?m?\\.?|p\\.?m?\\.?)/i,timezone:/^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt)/i,ordinalSuffix:/^\\s*(st|nd|rd|th)/i,timeContext:/^\\s*(\\:|a|p)/i},abbreviatedTimeZoneStandard:{GMT:"-000",EST:"-0400",CST:"-0500",MST:"-0600",PST:"-0700"},abbreviatedTimeZoneDST:{GMT:"-000",EDT:"-0500",CDT:"-0600",MDT:"-0700",PDT:"-0800"}};
',
  'fr-CA' => 'Date.CultureInfo={name:"fr-CA",englishName:"French (Canada)",nativeName:"français (Canada)",dayNames:["dimanche","lundi","mardi","mercredi","jeudi","vendredi","samedi"],abbreviatedDayNames:["dim.","lun.","mar.","mer.","jeu.","ven.","sam."],shortestDayNames:["di","lu","ma","me","je","ve","sa"],firstLetterDayNames:["d","l","m","m","j","v","s"],monthNames:["janvier","février","mars","avril","mai","juin","juillet","août","septembre","octobre","novembre","décembre"],abbreviatedMonthNames:["janv.","févr.","mars","avr.","mai","juin","juil.","août","sept.","oct.","nov.","déc."],amDesignator:"",pmDesignator:"",firstDayOfWeek:0,twoDigitYearMax:2029,dateElementOrder:"ymd",formatPatterns:{shortDate:"yyyy-MM-dd",longDate:"d MMMM yyyy",shortTime:"HH:mm",longTime:"HH:mm:ss",fullDateTime:"d MMMM yyyy HH:mm:ss",sortableDateTime:"yyyy-MM-ddTHH:mm:ss",universalSortableDateTime:"yyyy-MM-dd HH:mm:ssZ",rfc1123:"ddd, dd MMM yyyy HH:mm:ss GMT",monthDay:"d MMMM",yearMonth:"MMMM, yyyy"},regexPatterns:{jan:/^janv(.(ier)?)?/i,feb:/^févr(.(ier)?)?/i,mar:/^mars/i,apr:/^avr(.(il)?)?/i,may:/^mai/i,jun:/^juin/i,jul:/^juil(.(let)?)?/i,aug:/^août/i,sep:/^sept(.(embre)?)?/i,oct:/^oct(.(obre)?)?/i,nov:/^nov(.(embre)?)?/i,dec:/^déc(.(embre)?)?/i,sun:/^di(m(.(anche)?)?)?/i,mon:/^lu(n(.(di)?)?)?/i,tue:/^ma(r(.(di)?)?)?/i,wed:/^me(r(.(credi)?)?)?/i,thu:/^je(u(.(di)?)?)?/i,fri:/^ve(n(.(dredi)?)?)?/i,sat:/^sa(m(.(edi)?)?)?/i,future:/^next/i,past:/^last|past|prev(ious)?/i,add:/^(\\+|after|from)/i,subtract:/^(\\-|before|ago)/i,yesterday:/^yesterday/i,today:/^t(oday)?/i,tomorrow:/^tomorrow/i,now:/^n(ow)?/i,millisecond:/^ms|milli(second)?s?/i,second:/^sec(ond)?s?/i,minute:/^min(ute)?s?/i,hour:/^h(ou)?rs?/i,week:/^w(ee)?k/i,month:/^m(o(nth)?s?)?/i,day:/^d(ays?)?/i,year:/^y((ea)?rs?)?/i,shortMeridian:/^(a|p)/i,longMeridian:/^(a\\.?m?\\.?|p\\.?m?\\.?)/i,timezone:/^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt)/i,ordinalSuffix:/^\\s*(st|nd|rd|th)/i,timeContext:/^\\s*(\\:|a|p)/i},abbreviatedTimeZoneStandard:{GMT:"-000",EST:"-0400",CST:"-0500",MST:"-0600",PST:"-0700"},abbreviatedTimeZoneDST:{GMT:"-000",EDT:"-0500",CDT:"-0600",MDT:"-0700",PDT:"-0800"}};
',
  'fr-CH' => 'Date.CultureInfo={name:"fr-CH",englishName:"French (Switzerland)",nativeName:"français (Suisse)",dayNames:["dimanche","lundi","mardi","mercredi","jeudi","vendredi","samedi"],abbreviatedDayNames:["dim.","lun.","mar.","mer.","jeu.","ven.","sam."],shortestDayNames:["di","lu","ma","me","je","ve","sa"],firstLetterDayNames:["d","l","m","m","j","v","s"],monthNames:["janvier","février","mars","avril","mai","juin","juillet","août","septembre","octobre","novembre","décembre"],abbreviatedMonthNames:["janv.","févr.","mars","avr.","mai","juin","juil.","août","sept.","oct.","nov.","déc."],amDesignator:"",pmDesignator:"",firstDayOfWeek:1,twoDigitYearMax:2029,dateElementOrder:"dmy",formatPatterns:{shortDate:"dd.MM.yyyy",longDate:"dddd, d. MMMM yyyy",shortTime:"HH:mm",longTime:"HH:mm:ss",fullDateTime:"dddd, d. MMMM yyyy HH:mm:ss",sortableDateTime:"yyyy-MM-ddTHH:mm:ss",universalSortableDateTime:"yyyy-MM-dd HH:mm:ssZ",rfc1123:"ddd, dd MMM yyyy HH:mm:ss GMT",monthDay:"d MMMM",yearMonth:"MMMM yyyy"},regexPatterns:{jan:/^janv(.(ier)?)?/i,feb:/^févr(.(ier)?)?/i,mar:/^mars/i,apr:/^avr(.(il)?)?/i,may:/^mai/i,jun:/^juin/i,jul:/^juil(.(let)?)?/i,aug:/^août/i,sep:/^sept(.(embre)?)?/i,oct:/^oct(.(obre)?)?/i,nov:/^nov(.(embre)?)?/i,dec:/^déc(.(embre)?)?/i,sun:/^di(m(.(anche)?)?)?/i,mon:/^lu(n(.(di)?)?)?/i,tue:/^ma(r(.(di)?)?)?/i,wed:/^me(r(.(credi)?)?)?/i,thu:/^je(u(.(di)?)?)?/i,fri:/^ve(n(.(dredi)?)?)?/i,sat:/^sa(m(.(edi)?)?)?/i,future:/^next/i,past:/^last|past|prev(ious)?/i,add:/^(\\+|after|from)/i,subtract:/^(\\-|before|ago)/i,yesterday:/^yesterday/i,today:/^t(oday)?/i,tomorrow:/^tomorrow/i,now:/^n(ow)?/i,millisecond:/^ms|milli(second)?s?/i,second:/^sec(ond)?s?/i,minute:/^min(ute)?s?/i,hour:/^h(ou)?rs?/i,week:/^w(ee)?k/i,month:/^m(o(nth)?s?)?/i,day:/^d(ays?)?/i,year:/^y((ea)?rs?)?/i,shortMeridian:/^(a|p)/i,longMeridian:/^(a\\.?m?\\.?|p\\.?m?\\.?)/i,timezone:/^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt)/i,ordinalSuffix:/^\\s*(st|nd|rd|th)/i,timeContext:/^\\s*(\\:|a|p)/i},abbreviatedTimeZoneStandard:{GMT:"-000",EST:"-0400",CST:"-0500",MST:"-0600",PST:"-0700"},abbreviatedTimeZoneDST:{GMT:"-000",EDT:"-0500",CDT:"-0600",MDT:"-0700",PDT:"-0800"}};
',
  'fr-FR' => 'Date.CultureInfo={name:"fr-FR",englishName:"French (France)",nativeName:"français (France)",dayNames:["dimanche","lundi","mardi","mercredi","jeudi","vendredi","samedi"],abbreviatedDayNames:["dim.","lun.","mar.","mer.","jeu.","ven.","sam."],shortestDayNames:["di","lu","ma","me","je","ve","sa"],firstLetterDayNames:["d","l","m","m","j","v","s"],monthNames:["janvier","février","mars","avril","mai","juin","juillet","août","septembre","octobre","novembre","décembre"],abbreviatedMonthNames:["janv.","févr.","mars","avr.","mai","juin","juil.","août","sept.","oct.","nov.","déc."],amDesignator:"",pmDesignator:"",firstDayOfWeek:1,twoDigitYearMax:2029,dateElementOrder:"dmy",formatPatterns:{shortDate:"dd/MM/yyyy",longDate:"dddd d MMMM yyyy",shortTime:"HH:mm",longTime:"HH:mm:ss",fullDateTime:"dddd d MMMM yyyy HH:mm:ss",sortableDateTime:"yyyy-MM-ddTHH:mm:ss",universalSortableDateTime:"yyyy-MM-dd HH:mm:ssZ",rfc1123:"ddd, dd MMM yyyy HH:mm:ss GMT",monthDay:"d MMMM",yearMonth:"MMMM yyyy"},regexPatterns:{jan:/^janv(.(ier)?)?/i,feb:/^févr(.(ier)?)?/i,mar:/^mars/i,apr:/^avr(.(il)?)?/i,may:/^mai/i,jun:/^juin/i,jul:/^juil(.(let)?)?/i,aug:/^août/i,sep:/^sept(.(embre)?)?/i,oct:/^oct(.(obre)?)?/i,nov:/^nov(.(embre)?)?/i,dec:/^déc(.(embre)?)?/i,sun:/^di(m(.(anche)?)?)?/i,mon:/^lu(n(.(di)?)?)?/i,tue:/^ma(r(.(di)?)?)?/i,wed:/^me(r(.(credi)?)?)?/i,thu:/^je(u(.(di)?)?)?/i,fri:/^ve(n(.(dredi)?)?)?/i,sat:/^sa(m(.(edi)?)?)?/i,future:/^next/i,past:/^last|past|prev(ious)?/i,add:/^(\\+|after|from)/i,subtract:/^(\\-|before|ago)/i,yesterday:/^yesterday/i,today:/^t(oday)?/i,tomorrow:/^tomorrow/i,now:/^n(ow)?/i,millisecond:/^ms|milli(second)?s?/i,second:/^sec(ond)?s?/i,minute:/^min(ute)?s?/i,hour:/^h(ou)?rs?/i,week:/^w(ee)?k/i,month:/^m(o(nth)?s?)?/i,day:/^d(ays?)?/i,year:/^y((ea)?rs?)?/i,shortMeridian:/^(a|p)/i,longMeridian:/^(a\\.?m?\\.?|p\\.?m?\\.?)/i,timezone:/^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt)/i,ordinalSuffix:/^\\s*(st|nd|rd|th)/i,timeContext:/^\\s*(\\:|a|p)/i},abbreviatedTimeZoneStandard:{GMT:"-000",EST:"-0400",CST:"-0500",MST:"-0600",PST:"-0700"},abbreviatedTimeZoneDST:{GMT:"-000",EDT:"-0500",CDT:"-0600",MDT:"-0700",PDT:"-0800"}};
',
  'fr-LU' => 'Date.CultureInfo={name:"fr-LU",englishName:"French (Luxembourg)",nativeName:"français (Luxembourg)",dayNames:["dimanche","lundi","mardi","mercredi","jeudi","vendredi","samedi"],abbreviatedDayNames:["dim.","lun.","mar.","mer.","jeu.","ven.","sam."],shortestDayNames:["di","lu","ma","me","je","ve","sa"],firstLetterDayNames:["d","l","m","m","j","v","s"],monthNames:["janvier","février","mars","avril","mai","juin","juillet","août","septembre","octobre","novembre","décembre"],abbreviatedMonthNames:["janv.","févr.","mars","avr.","mai","juin","juil.","août","sept.","oct.","nov.","déc."],amDesignator:"",pmDesignator:"",firstDayOfWeek:1,twoDigitYearMax:2029,dateElementOrder:"dmy",formatPatterns:{shortDate:"dd/MM/yyyy",longDate:"dddd d MMMM yyyy",shortTime:"HH:mm",longTime:"HH:mm:ss",fullDateTime:"dddd d MMMM yyyy HH:mm:ss",sortableDateTime:"yyyy-MM-ddTHH:mm:ss",universalSortableDateTime:"yyyy-MM-dd HH:mm:ssZ",rfc1123:"ddd, dd MMM yyyy HH:mm:ss GMT",monthDay:"d MMMM",yearMonth:"MMMM yyyy"},regexPatterns:{jan:/^janv(.(ier)?)?/i,feb:/^févr(.(ier)?)?/i,mar:/^mars/i,apr:/^avr(.(il)?)?/i,may:/^mai/i,jun:/^juin/i,jul:/^juil(.(let)?)?/i,aug:/^août/i,sep:/^sept(.(embre)?)?/i,oct:/^oct(.(obre)?)?/i,nov:/^nov(.(embre)?)?/i,dec:/^déc(.(embre)?)?/i,sun:/^di(m(.(anche)?)?)?/i,mon:/^lu(n(.(di)?)?)?/i,tue:/^ma(r(.(di)?)?)?/i,wed:/^me(r(.(credi)?)?)?/i,thu:/^je(u(.(di)?)?)?/i,fri:/^ve(n(.(dredi)?)?)?/i,sat:/^sa(m(.(edi)?)?)?/i,future:/^next/i,past:/^last|past|prev(ious)?/i,add:/^(\\+|after|from)/i,subtract:/^(\\-|before|ago)/i,yesterday:/^yesterday/i,today:/^t(oday)?/i,tomorrow:/^tomorrow/i,now:/^n(ow)?/i,millisecond:/^ms|milli(second)?s?/i,second:/^sec(ond)?s?/i,minute:/^min(ute)?s?/i,hour:/^h(ou)?rs?/i,week:/^w(ee)?k/i,month:/^m(o(nth)?s?)?/i,day:/^d(ays?)?/i,year:/^y((ea)?rs?)?/i,shortMeridian:/^(a|p)/i,longMeridian:/^(a\\.?m?\\.?|p\\.?m?\\.?)/i,timezone:/^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt)/i,ordinalSuffix:/^\\s*(st|nd|rd|th)/i,timeContext:/^\\s*(\\:|a|p)/i},abbreviatedTimeZoneStandard:{GMT:"-000",EST:"-0400",CST:"-0500",MST:"-0600",PST:"-0700"},abbreviatedTimeZoneDST:{GMT:"-000",EDT:"-0500",CDT:"-0600",MDT:"-0700",PDT:"-0800"}};
',
  'fr-MC' => 'Date.CultureInfo={name:"fr-MC",englishName:"French (Principality of Monaco)",nativeName:"français (Principauté de Monaco)",dayNames:["dimanche","lundi","mardi","mercredi","jeudi","vendredi","samedi"],abbreviatedDayNames:["dim.","lun.","mar.","mer.","jeu.","ven.","sam."],shortestDayNames:["di","lu","ma","me","je","ve","sa"],firstLetterDayNames:["d","l","m","m","j","v","s"],monthNames:["janvier","février","mars","avril","mai","juin","juillet","août","septembre","octobre","novembre","décembre"],abbreviatedMonthNames:["janv.","févr.","mars","avr.","mai","juin","juil.","août","sept.","oct.","nov.","déc."],amDesignator:"",pmDesignator:"",firstDayOfWeek:1,twoDigitYearMax:2029,dateElementOrder:"dmy",formatPatterns:{shortDate:"dd/MM/yyyy",longDate:"dddd d MMMM yyyy",shortTime:"HH:mm",longTime:"HH:mm:ss",fullDateTime:"dddd d MMMM yyyy HH:mm:ss",sortableDateTime:"yyyy-MM-ddTHH:mm:ss",universalSortableDateTime:"yyyy-MM-dd HH:mm:ssZ",rfc1123:"ddd, dd MMM yyyy HH:mm:ss GMT",monthDay:"d MMMM",yearMonth:"MMMM yyyy"},regexPatterns:{jan:/^janv(.(ier)?)?/i,feb:/^févr(.(ier)?)?/i,mar:/^mars/i,apr:/^avr(.(il)?)?/i,may:/^mai/i,jun:/^juin/i,jul:/^juil(.(let)?)?/i,aug:/^août/i,sep:/^sept(.(embre)?)?/i,oct:/^oct(.(obre)?)?/i,nov:/^nov(.(embre)?)?/i,dec:/^déc(.(embre)?)?/i,sun:/^di(m(.(anche)?)?)?/i,mon:/^lu(n(.(di)?)?)?/i,tue:/^ma(r(.(di)?)?)?/i,wed:/^me(r(.(credi)?)?)?/i,thu:/^je(u(.(di)?)?)?/i,fri:/^ve(n(.(dredi)?)?)?/i,sat:/^sa(m(.(edi)?)?)?/i,future:/^next/i,past:/^last|past|prev(ious)?/i,add:/^(\\+|after|from)/i,subtract:/^(\\-|before|ago)/i,yesterday:/^yesterday/i,today:/^t(oday)?/i,tomorrow:/^tomorrow/i,now:/^n(ow)?/i,millisecond:/^ms|milli(second)?s?/i,second:/^sec(ond)?s?/i,minute:/^min(ute)?s?/i,hour:/^h(ou)?rs?/i,week:/^w(ee)?k/i,month:/^m(o(nth)?s?)?/i,day:/^d(ays?)?/i,year:/^y((ea)?rs?)?/i,shortMeridian:/^(a|p)/i,longMeridian:/^(a\\.?m?\\.?|p\\.?m?\\.?)/i,timezone:/^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt)/i,ordinalSuffix:/^\\s*(st|nd|rd|th)/i,timeContext:/^\\s*(\\:|a|p)/i},abbreviatedTimeZoneStandard:{GMT:"-000",EST:"-0400",CST:"-0500",MST:"-0600",PST:"-0700"},abbreviatedTimeZoneDST:{GMT:"-000",EDT:"-0500",CDT:"-0600",MDT:"-0700",PDT:"-0800"}};
',
  'gl-ES' => 'Date.CultureInfo={name:"gl-ES",englishName:"Galician (Galician)",nativeName:"galego (galego)",dayNames:["domingo","luns","martes","mércores","xoves","venres","sábado"],abbreviatedDayNames:["dom","luns","mar","mér","xov","ven","sab"],shortestDayNames:["do","lu","ma","mé","xo","ve","sa"],firstLetterDayNames:["d","l","m","m","x","v","s"],monthNames:["xaneiro","febreiro","marzo","abril","maio","xuño","xullo","agosto","setembro","outubro","novembro","decembro"],abbreviatedMonthNames:["xan","feb","mar","abr","maio","xuñ","xull","ago","set","out","nov","dec"],amDesignator:"a.m.",pmDesignator:"p.m.",firstDayOfWeek:1,twoDigitYearMax:2029,dateElementOrder:"dmy",formatPatterns:{shortDate:"dd/MM/yy",longDate:"dddd, dd\' de \'MMMM\' de \'yyyy",shortTime:"H:mm",longTime:"H:mm:ss",fullDateTime:"dddd, dd\' de \'MMMM\' de \'yyyy H:mm:ss",sortableDateTime:"yyyy-MM-ddTHH:mm:ss",universalSortableDateTime:"yyyy-MM-dd HH:mm:ssZ",rfc1123:"ddd, dd MMM yyyy HH:mm:ss GMT",monthDay:"dd MMMM",yearMonth:"MMMM\' de \'yyyy"},regexPatterns:{jan:/^xan(eiro)?/i,feb:/^feb(reiro)?/i,mar:/^mar(zo)?/i,apr:/^abr(il)?/i,may:/^maio/i,jun:/^xuñ(o)?/i,jul:/^xull(o)?/i,aug:/^ago(sto)?/i,sep:/^set(embro)?/i,oct:/^out(ubro)?/i,nov:/^nov(embro)?/i,dec:/^dec(embro)?/i,sun:/^do(m(ingo)?)?/i,mon:/^lu(1)?/i,tue:/^ma(r(tes)?)?/i,wed:/^mé(r(cores)?)?/i,thu:/^xo(v(es)?)?/i,fri:/^ve(n(res)?)?/i,sat:/^sa(b(ado)?)?/i,future:/^next/i,past:/^last|past|prev(ious)?/i,add:/^(\\+|after|from)/i,subtract:/^(\\-|before|ago)/i,yesterday:/^yesterday/i,today:/^t(oday)?/i,tomorrow:/^tomorrow/i,now:/^n(ow)?/i,millisecond:/^ms|milli(second)?s?/i,second:/^sec(ond)?s?/i,minute:/^min(ute)?s?/i,hour:/^h(ou)?rs?/i,week:/^w(ee)?k/i,month:/^m(o(nth)?s?)?/i,day:/^d(ays?)?/i,year:/^y((ea)?rs?)?/i,shortMeridian:/^(a|p)/i,longMeridian:/^(a\\.?m?\\.?|p\\.?m?\\.?)/i,timezone:/^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt)/i,ordinalSuffix:/^\\s*(st|nd|rd|th)/i,timeContext:/^\\s*(\\:|a|p)/i},abbreviatedTimeZoneStandard:{GMT:"-000",EST:"-0400",CST:"-0500",MST:"-0600",PST:"-0700"},abbreviatedTimeZoneDST:{GMT:"-000",EDT:"-0500",CDT:"-0600",MDT:"-0700",PDT:"-0800"}};
',
  'gu-IN' => 'Date.CultureInfo={name:"gu-IN",englishName:"Gujarati (India)",nativeName:"ગુજરાતી (ભારત)",dayNames:["રવિવાર","સોમવાર","મંગળવાર","બુધવાર","ગુરુવાર","શુક્રવાર","શનિવાર"],abbreviatedDayNames:["રવિ","સોમ","મંગળ","બુધ","ગુરુ","શુક્ર","શનિ"],shortestDayNames:["ર","સ","મ","બ","ગ","શ","શ"],firstLetterDayNames:["ર","સ","મ","બ","ગ","શ","શ"],monthNames:["જાન્યુઆરી","ફેબ્રુઆરી","માર્ચ","એપ્રિલ","મે","જૂન","જુલાઈ","ઑગસ્ટ","સપ્ટેમ્બર","ઑક્ટ્બર","નવેમ્બર","ડિસેમ્બર"],abbreviatedMonthNames:["જાન્યુ","ફેબ્રુ","માર્ચ","એપ્રિલ","મે","જૂન","જુલાઈ","ઑગસ્ટ","સપ્ટે","ઑક્ટો","નવે","ડિસે"],amDesignator:"પૂર્વ મધ્યાહ્ન",pmDesignator:"ઉત્તર મધ્યાહ્ન",firstDayOfWeek:1,twoDigitYearMax:2029,dateElementOrder:"dmy",formatPatterns:{shortDate:"dd-MM-yy",longDate:"dd MMMM yyyy",shortTime:"HH:mm",longTime:"HH:mm:ss",fullDateTime:"dd MMMM yyyy HH:mm:ss",sortableDateTime:"yyyy-MM-ddTHH:mm:ss",universalSortableDateTime:"yyyy-MM-dd HH:mm:ssZ",rfc1123:"ddd, dd MMM yyyy HH:mm:ss GMT",monthDay:"dd MMMM",yearMonth:"MMMM, yyyy"},regexPatterns:{jan:/^જાન્યુ(આરી)?/i,feb:/^ફેબ્રુ(આરી)?/i,mar:/^માર્ચ/i,apr:/^એપ્રિલ/i,may:/^મે/i,jun:/^જૂન/i,jul:/^જુલાઈ/i,aug:/^ઑગસ્ટ/i,sep:/^સપ્ટે(મ્બર)?/i,oct:/^ઑક્ટ્બર/i,nov:/^નવે(મ્બર)?/i,dec:/^ડિસે(મ્બર)?/i,sun:/^ર(વિ(વાર)?)?/i,mon:/^સ(ોમ(વાર)?)?/i,tue:/^મ(ંગળ(વાર)?)?/i,wed:/^બ(ુધ(વાર)?)?/i,thu:/^ગ(ુરુ(વાર)?)?/i,fri:/^શ(ુક્ર(વાર)?)?/i,sat:/^શ(નિ(વાર)?)?/i,future:/^next/i,past:/^last|past|prev(ious)?/i,add:/^(\\+|after|from)/i,subtract:/^(\\-|before|ago)/i,yesterday:/^yesterday/i,today:/^t(oday)?/i,tomorrow:/^tomorrow/i,now:/^n(ow)?/i,millisecond:/^ms|milli(second)?s?/i,second:/^sec(ond)?s?/i,minute:/^min(ute)?s?/i,hour:/^h(ou)?rs?/i,week:/^w(ee)?k/i,month:/^m(o(nth)?s?)?/i,day:/^d(ays?)?/i,year:/^y((ea)?rs?)?/i,shortMeridian:/^(a|p)/i,longMeridian:/^(a\\.?m?\\.?|p\\.?m?\\.?)/i,timezone:/^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt)/i,ordinalSuffix:/^\\s*(st|nd|rd|th)/i,timeContext:/^\\s*(\\:|a|p)/i},abbreviatedTimeZoneStandard:{GMT:"-000",EST:"-0400",CST:"-0500",MST:"-0600",PST:"-0700"},abbreviatedTimeZoneDST:{GMT:"-000",EDT:"-0500",CDT:"-0600",MDT:"-0700",PDT:"-0800"}};
',
  'he-IL' => 'Date.CultureInfo={name:"he-IL",englishName:"Hebrew (Israel)",nativeName:"עברית (ישראל)",dayNames:["יום ראשון","יום שני","יום שלישי","יום רביעי","יום חמישי","יום שישי","שבת"],abbreviatedDayNames:["יום א","יום ב","יום ג","יום ד","יום ה","יום ו","שבת"],shortestDayNames:["א","ב","ג","ד","ה","ו","ש"],firstLetterDayNames:["א","ב","ג","ד","ה","ו","ש"],monthNames:["ינואר","פברואר","מרץ","אפריל","מאי","יוני","יולי","אוגוסט","ספטמבר","אוקטובר","נובמבר","דצמבר"],abbreviatedMonthNames:["ינו","פבר","מרץ","אפר","מאי","יונ","יול","אוג","ספט","אוק","נוב","דצמ"],amDesignator:"AM",pmDesignator:"PM",firstDayOfWeek:0,twoDigitYearMax:2029,dateElementOrder:"dmy",formatPatterns:{shortDate:"dd/MM/yyyy",longDate:"dddd dd MMMM yyyy",shortTime:"HH:mm",longTime:"HH:mm:ss",fullDateTime:"dddd dd MMMM yyyy HH:mm:ss",sortableDateTime:"yyyy-MM-ddTHH:mm:ss",universalSortableDateTime:"yyyy-MM-dd HH:mm:ssZ",rfc1123:"ddd, dd MMM yyyy HH:mm:ss GMT",monthDay:"dd MMMM",yearMonth:"MMMM yyyy"},regexPatterns:{jan:/^ינו(אר)?/i,feb:/^פבר(ואר)?/i,mar:/^מרץ/i,apr:/^אפר(יל)?/i,may:/^מאי/i,jun:/^יונ(י)?/i,jul:/^יול(י)?/i,aug:/^אוג(וסט)?/i,sep:/^ספט(מבר)?/i,oct:/^אוק(טובר)?/i,nov:/^נוב(מבר)?/i,dec:/^דצמ(בר)?/i,sun:/^א(ום א(אשון)?)?/i,mon:/^ב(ום ב(ני)?)?/i,tue:/^ג(ום ג(לישי)?)?/i,wed:/^ד(ום ד(ביעי)?)?/i,thu:/^ה(ום ה(מישי)?)?/i,fri:/^ו(ום ו(ישי)?)?/i,sat:/^ש(1)?/i,future:/^next/i,past:/^last|past|prev(ious)?/i,add:/^(\\+|after|from)/i,subtract:/^(\\-|before|ago)/i,yesterday:/^yesterday/i,today:/^t(oday)?/i,tomorrow:/^tomorrow/i,now:/^n(ow)?/i,millisecond:/^ms|milli(second)?s?/i,second:/^sec(ond)?s?/i,minute:/^min(ute)?s?/i,hour:/^h(ou)?rs?/i,week:/^w(ee)?k/i,month:/^m(o(nth)?s?)?/i,day:/^d(ays?)?/i,year:/^y((ea)?rs?)?/i,shortMeridian:/^(a|p)/i,longMeridian:/^(a\\.?m?\\.?|p\\.?m?\\.?)/i,timezone:/^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt)/i,ordinalSuffix:/^\\s*(st|nd|rd|th)/i,timeContext:/^\\s*(\\:|a|p)/i},abbreviatedTimeZoneStandard:{GMT:"-000",EST:"-0400",CST:"-0500",MST:"-0600",PST:"-0700"},abbreviatedTimeZoneDST:{GMT:"-000",EDT:"-0500",CDT:"-0600",MDT:"-0700",PDT:"-0800"}};
',
  'hi-IN' => 'Date.CultureInfo={name:"hi-IN",englishName:"Hindi (India)",nativeName:"हिंदी (भारत)",dayNames:["रविवार","सोमवार","मंगलवार","बुधवार","गुरुवार","शुक्रवार","शनिवार"],abbreviatedDayNames:["रवि.","सोम.","मंगल.","बुध.","गुरु.","शुक्र.","शनि."],shortestDayNames:["र","स","म","ब","ग","श","श"],firstLetterDayNames:["र","स","म","ब","ग","श","श"],monthNames:["जनवरी","फरवरी","मार्च","अप्रैल","मई","जून","जुलाई","अगस्त","सितम्बर","अक्तूबर","नवम्बर","दिसम्बर"],abbreviatedMonthNames:["जनवरी","फरवरी","मार्च","अप्रैल","मई","जून","जुलाई","अगस्त","सितम्बर","अक्तूबर","नवम्बर","दिसम्बर"],amDesignator:"पूर्वाह्न",pmDesignator:"अपराह्न",firstDayOfWeek:1,twoDigitYearMax:2029,dateElementOrder:"dmy",formatPatterns:{shortDate:"dd-MM-yyyy",longDate:"dd MMMM yyyy",shortTime:"HH:mm",longTime:"HH:mm:ss",fullDateTime:"dd MMMM yyyy HH:mm:ss",sortableDateTime:"yyyy-MM-ddTHH:mm:ss",universalSortableDateTime:"yyyy-MM-dd HH:mm:ssZ",rfc1123:"ddd, dd MMM yyyy HH:mm:ss GMT",monthDay:"dd MMMM",yearMonth:"MMMM, yyyy"},regexPatterns:{jan:/^जनवरी/i,feb:/^फरवरी/i,mar:/^मार्च/i,apr:/^अप्रैल/i,may:/^मई/i,jun:/^जून/i,jul:/^जुलाई/i,aug:/^अगस्त/i,sep:/^सितम्बर/i,oct:/^अक्तूबर/i,nov:/^नवम्बर/i,dec:/^दिसम्बर/i,sun:/^र(वि(.(वार)?)?)?/i,mon:/^स(ोम(.(वार)?)?)?/i,tue:/^म(ंगल(.(वार)?)?)?/i,wed:/^ब(ुध(.(वार)?)?)?/i,thu:/^ग(ुरु(.(वार)?)?)?/i,fri:/^श(ुक्र(.(वार)?)?)?/i,sat:/^श(नि(.(वार)?)?)?/i,future:/^next/i,past:/^last|past|prev(ious)?/i,add:/^(\\+|after|from)/i,subtract:/^(\\-|before|ago)/i,yesterday:/^yesterday/i,today:/^t(oday)?/i,tomorrow:/^tomorrow/i,now:/^n(ow)?/i,millisecond:/^ms|milli(second)?s?/i,second:/^sec(ond)?s?/i,minute:/^min(ute)?s?/i,hour:/^h(ou)?rs?/i,week:/^w(ee)?k/i,month:/^m(o(nth)?s?)?/i,day:/^d(ays?)?/i,year:/^y((ea)?rs?)?/i,shortMeridian:/^(a|p)/i,longMeridian:/^(a\\.?m?\\.?|p\\.?m?\\.?)/i,timezone:/^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt)/i,ordinalSuffix:/^\\s*(st|nd|rd|th)/i,timeContext:/^\\s*(\\:|a|p)/i},abbreviatedTimeZoneStandard:{GMT:"-000",EST:"-0400",CST:"-0500",MST:"-0600",PST:"-0700"},abbreviatedTimeZoneDST:{GMT:"-000",EDT:"-0500",CDT:"-0600",MDT:"-0700",PDT:"-0800"}};
',
  'hr-BA' => 'Date.CultureInfo={name:"hr-BA",englishName:"Croatian (Bosnia and Herzegovina)",nativeName:"hrvatski (Bosna i Hercegovina)",dayNames:["nedjelja","ponedjeljak","utorak","srijeda","četvrtak","petak","subota"],abbreviatedDayNames:["ned","pon","uto","sri","čet","pet","sub"],shortestDayNames:["ned","pon","uto","sri","čet","pet","sub"],firstLetterDayNames:["n","p","u","s","č","p","s"],monthNames:["siječanj","veljača","ožujak","travanj","svibanj","lipanj","srpanj","kolovoz","rujan","listopad","studeni","prosinac"],abbreviatedMonthNames:["sij","vlj","ožu","tra","svi","lip","srp","kol","ruj","lis","stu","pro"],amDesignator:"",pmDesignator:"",firstDayOfWeek:1,twoDigitYearMax:2029,dateElementOrder:"dmy",formatPatterns:{shortDate:"d.M.yyyy",longDate:"d. MMMM yyyy",shortTime:"H:mm:ss",longTime:"H:mm:ss",fullDateTime:"d. MMMM yyyy H:mm:ss",sortableDateTime:"yyyy-MM-ddTHH:mm:ss",universalSortableDateTime:"yyyy-MM-dd HH:mm:ssZ",rfc1123:"ddd, dd MMM yyyy HH:mm:ss GMT",monthDay:"MMMM dd",yearMonth:"MMMM, yyyy"},regexPatterns:{jan:/^sij(ečanj)?/i,feb:/^veljača/i,mar:/^ožu(jak)?/i,apr:/^tra(vanj)?/i,may:/^svi(banj)?/i,jun:/^lip(anj)?/i,jul:/^srp(anj)?/i,aug:/^kol(ovoz)?/i,sep:/^ruj(an)?/i,oct:/^lis(topad)?/i,nov:/^stu(deni)?/i,dec:/^pro(sinac)?/i,sun:/^nedjelja/i,mon:/^ponedjeljak/i,tue:/^utorak/i,wed:/^srijeda/i,thu:/^četvrtak/i,fri:/^petak/i,sat:/^subota/i,future:/^next/i,past:/^last|past|prev(ious)?/i,add:/^(\\+|after|from)/i,subtract:/^(\\-|before|ago)/i,yesterday:/^yesterday/i,today:/^t(oday)?/i,tomorrow:/^tomorrow/i,now:/^n(ow)?/i,millisecond:/^ms|milli(second)?s?/i,second:/^sec(ond)?s?/i,minute:/^min(ute)?s?/i,hour:/^h(ou)?rs?/i,week:/^w(ee)?k/i,month:/^m(o(nth)?s?)?/i,day:/^d(ays?)?/i,year:/^y((ea)?rs?)?/i,shortMeridian:/^(a|p)/i,longMeridian:/^(a\\.?m?\\.?|p\\.?m?\\.?)/i,timezone:/^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt)/i,ordinalSuffix:/^\\s*(st|nd|rd|th)/i,timeContext:/^\\s*(\\:|a|p)/i},abbreviatedTimeZoneStandard:{GMT:"-000",EST:"-0400",CST:"-0500",MST:"-0600",PST:"-0700"},abbreviatedTimeZoneDST:{GMT:"-000",EDT:"-0500",CDT:"-0600",MDT:"-0700",PDT:"-0800"}};
',
  'hr-HR' => 'Date.CultureInfo={name:"hr-HR",englishName:"Croatian (Croatia)",nativeName:"hrvatski (Hrvatska)",dayNames:["nedjelja","ponedjeljak","utorak","srijeda","četvrtak","petak","subota"],abbreviatedDayNames:["ned","pon","uto","sri","čet","pet","sub"],shortestDayNames:["ne","po","ut","sr","če","pe","su"],firstLetterDayNames:["n","p","u","s","č","p","s"],monthNames:["siječanj","veljača","ožujak","travanj","svibanj","lipanj","srpanj","kolovoz","rujan","listopad","studeni","prosinac"],abbreviatedMonthNames:["sij","vlj","ožu","tra","svi","lip","srp","kol","ruj","lis","stu","pro"],amDesignator:"",pmDesignator:"",firstDayOfWeek:1,twoDigitYearMax:2029,dateElementOrder:"dmy",formatPatterns:{shortDate:"d.M.yyyy",longDate:"d. MMMM yyyy",shortTime:"H:mm",longTime:"H:mm:ss",fullDateTime:"d. MMMM yyyy H:mm:ss",sortableDateTime:"yyyy-MM-ddTHH:mm:ss",universalSortableDateTime:"yyyy-MM-dd HH:mm:ssZ",rfc1123:"ddd, dd MMM yyyy HH:mm:ss GMT",monthDay:"d. MMMM",yearMonth:"MMMM, yyyy"},regexPatterns:{jan:/^sij(ečanj)?/i,feb:/^veljača/i,mar:/^ožu(jak)?/i,apr:/^tra(vanj)?/i,may:/^svi(banj)?/i,jun:/^lip(anj)?/i,jul:/^srp(anj)?/i,aug:/^kol(ovoz)?/i,sep:/^ruj(an)?/i,oct:/^lis(topad)?/i,nov:/^stu(deni)?/i,dec:/^pro(sinac)?/i,sun:/^ne(d(jelja)?)?/i,mon:/^po(n(edjeljak)?)?/i,tue:/^ut(o(rak)?)?/i,wed:/^sr(i(jeda)?)?/i,thu:/^če(t(vrtak)?)?/i,fri:/^pe(t(ak)?)?/i,sat:/^su(b(ota)?)?/i,future:/^next/i,past:/^last|past|prev(ious)?/i,add:/^(\\+|after|from)/i,subtract:/^(\\-|before|ago)/i,yesterday:/^yesterday/i,today:/^t(oday)?/i,tomorrow:/^tomorrow/i,now:/^n(ow)?/i,millisecond:/^ms|milli(second)?s?/i,second:/^sec(ond)?s?/i,minute:/^min(ute)?s?/i,hour:/^h(ou)?rs?/i,week:/^w(ee)?k/i,month:/^m(o(nth)?s?)?/i,day:/^d(ays?)?/i,year:/^y((ea)?rs?)?/i,shortMeridian:/^(a|p)/i,longMeridian:/^(a\\.?m?\\.?|p\\.?m?\\.?)/i,timezone:/^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt)/i,ordinalSuffix:/^\\s*(st|nd|rd|th)/i,timeContext:/^\\s*(\\:|a|p)/i},abbreviatedTimeZoneStandard:{GMT:"-000",EST:"-0400",CST:"-0500",MST:"-0600",PST:"-0700"},abbreviatedTimeZoneDST:{GMT:"-000",EDT:"-0500",CDT:"-0600",MDT:"-0700",PDT:"-0800"}};
',
  'hu-HU' => 'Date.CultureInfo={name:"hu-HU",englishName:"Hungarian (Hungary)",nativeName:"magyar (Magyarország)",dayNames:["vasárnap","hétfő","kedd","szerda","csütörtök","péntek","szombat"],abbreviatedDayNames:["V","H","K","Sze","Cs","P","Szo"],shortestDayNames:["V","H","K","Sze","Cs","P","Szo"],firstLetterDayNames:["V","H","K","S","C","P","S"],monthNames:["január","február","március","április","május","június","július","augusztus","szeptember","október","november","december"],abbreviatedMonthNames:["jan.","febr.","márc.","ápr.","máj.","jún.","júl.","aug.","szept.","okt.","nov.","dec."],amDesignator:"de.",pmDesignator:"du.",firstDayOfWeek:1,twoDigitYearMax:2029,dateElementOrder:"ymd",formatPatterns:{shortDate:"yyyy. MM. dd.",longDate:"yyyy. MMMM d.",shortTime:"H:mm",longTime:"H:mm:ss",fullDateTime:"yyyy. MMMM d. H:mm:ss",sortableDateTime:"yyyy-MM-ddTHH:mm:ss",universalSortableDateTime:"yyyy-MM-dd HH:mm:ssZ",rfc1123:"ddd, dd MMM yyyy HH:mm:ss GMT",monthDay:"MMMM d.",yearMonth:"yyyy. MMMM"},regexPatterns:{jan:/^jan(.(uár)?)?/i,feb:/^febr(.(uár)?)?/i,mar:/^márc(.(ius)?)?/i,apr:/^ápr(.(ilis)?)?/i,may:/^máj(.(us)?)?/i,jun:/^jún(.(ius)?)?/i,jul:/^júl(.(ius)?)?/i,aug:/^aug(.(usztus)?)?/i,sep:/^szept(.(ember)?)?/i,oct:/^okt(.(óber)?)?/i,nov:/^nov(.(ember)?)?/i,dec:/^dec(.(ember)?)?/i,sun:/^vasárnap/i,mon:/^hétfő/i,tue:/^kedd/i,wed:/^szerda/i,thu:/^csütörtök/i,fri:/^péntek/i,sat:/^szombat/i,future:/^next/i,past:/^last|past|prev(ious)?/i,add:/^(\\+|after|from)/i,subtract:/^(\\-|before|ago)/i,yesterday:/^yesterday/i,today:/^t(oday)?/i,tomorrow:/^tomorrow/i,now:/^n(ow)?/i,millisecond:/^ms|milli(second)?s?/i,second:/^sec(ond)?s?/i,minute:/^min(ute)?s?/i,hour:/^h(ou)?rs?/i,week:/^w(ee)?k/i,month:/^m(o(nth)?s?)?/i,day:/^d(ays?)?/i,year:/^y((ea)?rs?)?/i,shortMeridian:/^(a|p)/i,longMeridian:/^(a\\.?m?\\.?|p\\.?m?\\.?)/i,timezone:/^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt)/i,ordinalSuffix:/^\\s*(st|nd|rd|th)/i,timeContext:/^\\s*(\\:|a|p)/i},abbreviatedTimeZoneStandard:{GMT:"-000",EST:"-0400",CST:"-0500",MST:"-0600",PST:"-0700"},abbreviatedTimeZoneDST:{GMT:"-000",EDT:"-0500",CDT:"-0600",MDT:"-0700",PDT:"-0800"}};
',
  'hy-AM' => 'Date.CultureInfo={name:"hy-AM",englishName:"Armenian (Armenia)",nativeName:"Հայերեն (Հայաստան)",dayNames:["Կիրակի","Երկուշաբթի","Երեքշաբթի","Չորեքշաբթի","Հինգշաբթի","ՈՒրբաթ","Շաբաթ"],abbreviatedDayNames:["Կիր","Երկ","Երք","Չրք","Հնգ","ՈՒր","Շբթ"],shortestDayNames:["Կ","Ե","Ե","Չ","Հ","Ո","Շ"],firstLetterDayNames:["Կ","Ե","Ե","Չ","Հ","Ո","Շ"],monthNames:["Հունվար","Փետրվար","Մարտ","Ապրիլ","Մայիս","Հունիս","Հուլիս","Օգոստոս","Սեպտեմբեր","Հոկտեմբեր","Նոյեմբեր","Դեկտեմբեր"],abbreviatedMonthNames:["ՀՆՎ","ՓՏՎ","ՄՐՏ","ԱՊՐ","ՄՅՍ","ՀՆՍ","ՀԼՍ","ՕԳՍ","ՍԵՊ","ՀՈԿ","ՆՈՅ","ԴԵԿ"],amDesignator:"",pmDesignator:"",firstDayOfWeek:1,twoDigitYearMax:2029,dateElementOrder:"dmy",formatPatterns:{shortDate:"dd.MM.yyyy",longDate:"d MMMM, yyyy",shortTime:"H:mm",longTime:"H:mm:ss",fullDateTime:"d MMMM, yyyy H:mm:ss",sortableDateTime:"yyyy-MM-ddTHH:mm:ss",universalSortableDateTime:"yyyy-MM-dd HH:mm:ssZ",rfc1123:"ddd, dd MMM yyyy HH:mm:ss GMT",monthDay:"d MMMM",yearMonth:"MMMM, yyyy"},regexPatterns:{jan:/^հունվար/i,feb:/^փետրվար/i,mar:/^մարտ/i,apr:/^ապր(իլ)?/i,may:/^մայիս/i,jun:/^հունիս/i,jul:/^հուլիս/i,aug:/^օգոստոս/i,sep:/^սեպ(տեմբեր)?/i,oct:/^հոկ(տեմբեր)?/i,nov:/^նոյ(եմբեր)?/i,dec:/^դեկ(տեմբեր)?/i,sun:/^կ(իր(ակի)?)?/i,mon:/^ե(րկ(ուշաբթի)?)?/i,tue:/^ե(րք(քշաբթի)?)?/i,wed:/^չ(րք(եքշաբթի)?)?/i,thu:/^հ(նգ(գշաբթի)?)?/i,fri:/^ո(ւր(բաթ)?)?/i,sat:/^շ(բթ(աթ)?)?/i,future:/^next/i,past:/^last|past|prev(ious)?/i,add:/^(\\+|after|from)/i,subtract:/^(\\-|before|ago)/i,yesterday:/^yesterday/i,today:/^t(oday)?/i,tomorrow:/^tomorrow/i,now:/^n(ow)?/i,millisecond:/^ms|milli(second)?s?/i,second:/^sec(ond)?s?/i,minute:/^min(ute)?s?/i,hour:/^h(ou)?rs?/i,week:/^w(ee)?k/i,month:/^m(o(nth)?s?)?/i,day:/^d(ays?)?/i,year:/^y((ea)?rs?)?/i,shortMeridian:/^(a|p)/i,longMeridian:/^(a\\.?m?\\.?|p\\.?m?\\.?)/i,timezone:/^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt)/i,ordinalSuffix:/^\\s*(st|nd|rd|th)/i,timeContext:/^\\s*(\\:|a|p)/i},abbreviatedTimeZoneStandard:{GMT:"-000",EST:"-0400",CST:"-0500",MST:"-0600",PST:"-0700"},abbreviatedTimeZoneDST:{GMT:"-000",EDT:"-0500",CDT:"-0600",MDT:"-0700",PDT:"-0800"}};
',
  'id-ID' => 'Date.CultureInfo={name:"id-ID",englishName:"Indonesian (Indonesia)",nativeName:"Bahasa Indonesia (Indonesia)",dayNames:["Minggu","Senin","Selasa","Rabu","Kamis","Jumat","Sabtu"],abbreviatedDayNames:["Minggu","Sen","Sel","Rabu","Kamis","Jumat","Sabtu"],shortestDayNames:["M","S","S","R","K","J","S"],firstLetterDayNames:["M","S","S","R","K","J","S"],monthNames:["Januari","Februari","Maret","April","Mei","Juni","Juli","Agustus","September","Oktober","Nopember","Desember"],abbreviatedMonthNames:["Jan","Feb","Mar","Apr","Mei","Jun","Jul","Agust","Sep","Okt","Nop","Des"],amDesignator:"",pmDesignator:"",firstDayOfWeek:1,twoDigitYearMax:2029,dateElementOrder:"dmy",formatPatterns:{shortDate:"dd/MM/yyyy",longDate:"dd MMMM yyyy",shortTime:"H:mm",longTime:"H:mm:ss",fullDateTime:"dd MMMM yyyy H:mm:ss",sortableDateTime:"yyyy-MM-ddTHH:mm:ss",universalSortableDateTime:"yyyy-MM-dd HH:mm:ssZ",rfc1123:"ddd, dd MMM yyyy HH:mm:ss GMT",monthDay:"dd MMMM",yearMonth:"MMMM yyyy"},regexPatterns:{jan:/^jan(uari)?/i,feb:/^feb(ruari)?/i,mar:/^mar(et)?/i,apr:/^apr(il)?/i,may:/^mei/i,jun:/^jun(i)?/i,jul:/^jul(i)?/i,aug:/^agust(us)?/i,sep:/^sep(t(ember)?)?/i,oct:/^okt(ober)?/i,nov:/^nop(ember)?/i,dec:/^des(ember)?/i,sun:/^m(1)?/i,mon:/^s(en(in)?)?/i,tue:/^s(el(asa)?)?/i,wed:/^r(1)?/i,thu:/^k(1)?/i,fri:/^j(1)?/i,sat:/^s(1)?/i,future:/^next/i,past:/^last|past|prev(ious)?/i,add:/^(\\+|after|from)/i,subtract:/^(\\-|before|ago)/i,yesterday:/^yesterday/i,today:/^t(oday)?/i,tomorrow:/^tomorrow/i,now:/^n(ow)?/i,millisecond:/^ms|milli(second)?s?/i,second:/^sec(ond)?s?/i,minute:/^min(ute)?s?/i,hour:/^h(ou)?rs?/i,week:/^w(ee)?k/i,month:/^m(o(nth)?s?)?/i,day:/^d(ays?)?/i,year:/^y((ea)?rs?)?/i,shortMeridian:/^(a|p)/i,longMeridian:/^(a\\.?m?\\.?|p\\.?m?\\.?)/i,timezone:/^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt)/i,ordinalSuffix:/^\\s*(st|nd|rd|th)/i,timeContext:/^\\s*(\\:|a|p)/i},abbreviatedTimeZoneStandard:{GMT:"-000",EST:"-0400",CST:"-0500",MST:"-0600",PST:"-0700"},abbreviatedTimeZoneDST:{GMT:"-000",EDT:"-0500",CDT:"-0600",MDT:"-0700",PDT:"-0800"}};
',
  'is-IS' => 'Date.CultureInfo={name:"is-IS",englishName:"Icelandic (Iceland)",nativeName:"íslenska (Ísland)",dayNames:["sunnudagur","mánudagur","þriðjudagur","miðvikudagur","fimmtudagur","föstudagur","laugardagur"],abbreviatedDayNames:["sun.","mán.","þri.","mið.","fim.","fös.","lau."],shortestDayNames:["su","má","þr","mi","fi","fö","la"],firstLetterDayNames:["s","m","þ","m","f","f","l"],monthNames:["janúar","febrúar","mars","apríl","maí","júní","júlí","ágúst","september","október","nóvember","desember"],abbreviatedMonthNames:["jan.","feb.","mar.","apr.","maí","jún.","júl.","ágú.","sep.","okt.","nóv.","des."],amDesignator:"",pmDesignator:"",firstDayOfWeek:1,twoDigitYearMax:2029,dateElementOrder:"dmy",formatPatterns:{shortDate:"d.M.yyyy",longDate:"d. MMMM yyyy",shortTime:"HH:mm",longTime:"HH:mm:ss",fullDateTime:"d. MMMM yyyy HH:mm:ss",sortableDateTime:"yyyy-MM-ddTHH:mm:ss",universalSortableDateTime:"yyyy-MM-dd HH:mm:ssZ",rfc1123:"ddd, dd MMM yyyy HH:mm:ss GMT",monthDay:"d. MMMM",yearMonth:"MMMM yyyy"},regexPatterns:{jan:/^jan(.(úar)?)?/i,feb:/^feb(.(rúar)?)?/i,mar:/^mar(.(s)?)?/i,apr:/^apr(.(íl)?)?/i,may:/^maí/i,jun:/^jún(.(í)?)?/i,jul:/^júl(.(í)?)?/i,aug:/^ágú(.(st)?)?/i,sep:/^sep(t(ember)?)?/i,oct:/^okt(.(óber)?)?/i,nov:/^nóv(.(ember)?)?/i,dec:/^des(.(ember)?)?/i,sun:/^su(n(.(nudagur)?)?)?/i,mon:/^má(n(.(udagur)?)?)?/i,tue:/^þr(i(.(ðjudagur)?)?)?/i,wed:/^mi(ð(.(vikudagur)?)?)?/i,thu:/^fi(m(.(mtudagur)?)?)?/i,fri:/^fö(s(.(tudagur)?)?)?/i,sat:/^la(u(.(gardagur)?)?)?/i,future:/^next/i,past:/^last|past|prev(ious)?/i,add:/^(\\+|after|from)/i,subtract:/^(\\-|before|ago)/i,yesterday:/^yesterday/i,today:/^t(oday)?/i,tomorrow:/^tomorrow/i,now:/^n(ow)?/i,millisecond:/^ms|milli(second)?s?/i,second:/^sec(ond)?s?/i,minute:/^min(ute)?s?/i,hour:/^h(ou)?rs?/i,week:/^w(ee)?k/i,month:/^m(o(nth)?s?)?/i,day:/^d(ays?)?/i,year:/^y((ea)?rs?)?/i,shortMeridian:/^(a|p)/i,longMeridian:/^(a\\.?m?\\.?|p\\.?m?\\.?)/i,timezone:/^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt)/i,ordinalSuffix:/^\\s*(st|nd|rd|th)/i,timeContext:/^\\s*(\\:|a|p)/i},abbreviatedTimeZoneStandard:{GMT:"-000",EST:"-0400",CST:"-0500",MST:"-0600",PST:"-0700"},abbreviatedTimeZoneDST:{GMT:"-000",EDT:"-0500",CDT:"-0600",MDT:"-0700",PDT:"-0800"}};
',
  'it-CH' => 'Date.CultureInfo={name:"it-CH",englishName:"Italian (Switzerland)",nativeName:"italiano (Svizzera)",dayNames:["domenica","lunedì","martedì","mercoledì","giovedì","venerdì","sabato"],abbreviatedDayNames:["dom","lun","mar","mer","gio","ven","sab"],shortestDayNames:["do","lu","ma","me","gi","ve","sa"],firstLetterDayNames:["d","l","m","m","g","v","s"],monthNames:["gennaio","febbraio","marzo","aprile","maggio","giugno","luglio","agosto","settembre","ottobre","novembre","dicembre"],abbreviatedMonthNames:["gen","feb","mar","apr","mag","gio","lug","ago","set","ott","nov","dic"],amDesignator:"",pmDesignator:"",firstDayOfWeek:1,twoDigitYearMax:2029,dateElementOrder:"dmy",formatPatterns:{shortDate:"dd.MM.yyyy",longDate:"dddd, d. MMMM yyyy",shortTime:"HH:mm",longTime:"HH:mm:ss",fullDateTime:"dddd, d. MMMM yyyy HH:mm:ss",sortableDateTime:"yyyy-MM-ddTHH:mm:ss",universalSortableDateTime:"yyyy-MM-dd HH:mm:ssZ",rfc1123:"ddd, dd MMM yyyy HH:mm:ss GMT",monthDay:"d. MMMM",yearMonth:"MMMM yyyy"},regexPatterns:{jan:/^gen(naio)?/i,feb:/^feb(braio)?/i,mar:/^mar(zo)?/i,apr:/^apr(ile)?/i,may:/^mag(gio)?/i,jun:/^giugno/i,jul:/^lug(lio)?/i,aug:/^ago(sto)?/i,sep:/^set(tembre)?/i,oct:/^ott(obre)?/i,nov:/^nov(embre)?/i,dec:/^dic(embre)?/i,sun:/^do(m(enica)?)?/i,mon:/^lu(n(edì)?)?/i,tue:/^ma(r(tedì)?)?/i,wed:/^me(r(coledì)?)?/i,thu:/^gi(o(vedì)?)?/i,fri:/^ve(n(erdì)?)?/i,sat:/^sa(b(ato)?)?/i,future:/^next/i,past:/^last|past|prev(ious)?/i,add:/^(\\+|after|from)/i,subtract:/^(\\-|before|ago)/i,yesterday:/^yesterday/i,today:/^t(oday)?/i,tomorrow:/^tomorrow/i,now:/^n(ow)?/i,millisecond:/^ms|milli(second)?s?/i,second:/^sec(ond)?s?/i,minute:/^min(ute)?s?/i,hour:/^h(ou)?rs?/i,week:/^w(ee)?k/i,month:/^m(o(nth)?s?)?/i,day:/^d(ays?)?/i,year:/^y((ea)?rs?)?/i,shortMeridian:/^(a|p)/i,longMeridian:/^(a\\.?m?\\.?|p\\.?m?\\.?)/i,timezone:/^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt)/i,ordinalSuffix:/^\\s*(st|nd|rd|th)/i,timeContext:/^\\s*(\\:|a|p)/i},abbreviatedTimeZoneStandard:{GMT:"-000",EST:"-0400",CST:"-0500",MST:"-0600",PST:"-0700"},abbreviatedTimeZoneDST:{GMT:"-000",EDT:"-0500",CDT:"-0600",MDT:"-0700",PDT:"-0800"}};
',
  'it-IT' => 'Date.CultureInfo={name:"it-IT",englishName:"Italian (Italy)",nativeName:"italiano (Italia)",dayNames:["domenica","lunedì","martedì","mercoledì","giovedì","venerdì","sabato"],abbreviatedDayNames:["dom","lun","mar","mer","gio","ven","sab"],shortestDayNames:["do","lu","ma","me","gi","ve","sa"],firstLetterDayNames:["d","l","m","m","g","v","s"],monthNames:["gennaio","febbraio","marzo","aprile","maggio","giugno","luglio","agosto","settembre","ottobre","novembre","dicembre"],abbreviatedMonthNames:["gen","feb","mar","apr","mag","giu","lug","ago","set","ott","nov","dic"],amDesignator:"",pmDesignator:"",firstDayOfWeek:1,twoDigitYearMax:2029,dateElementOrder:"dmy",formatPatterns:{shortDate:"dd/MM/yyyy",longDate:"dddd d MMMM yyyy",shortTime:"H.mm",longTime:"H.mm.ss",fullDateTime:"dddd d MMMM yyyy H.mm.ss",sortableDateTime:"yyyy-MM-ddTHH:mm:ss",universalSortableDateTime:"yyyy-MM-dd HH:mm:ssZ",rfc1123:"ddd, dd MMM yyyy HH:mm:ss GMT",monthDay:"dd MMMM",yearMonth:"MMMM yyyy"},regexPatterns:{jan:/^gen(naio)?/i,feb:/^feb(braio)?/i,mar:/^mar(zo)?/i,apr:/^apr(ile)?/i,may:/^mag(gio)?/i,jun:/^giu(gno)?/i,jul:/^lug(lio)?/i,aug:/^ago(sto)?/i,sep:/^set(tembre)?/i,oct:/^ott(obre)?/i,nov:/^nov(embre)?/i,dec:/^dic(embre)?/i,sun:/^do(m(enica)?)?/i,mon:/^lu(n(edì)?)?/i,tue:/^ma(r(tedì)?)?/i,wed:/^me(r(coledì)?)?/i,thu:/^gi(o(vedì)?)?/i,fri:/^ve(n(erdì)?)?/i,sat:/^sa(b(ato)?)?/i,future:/^next/i,past:/^last|past|prev(ious)?/i,add:/^(\\+|after|from)/i,subtract:/^(\\-|before|ago)/i,yesterday:/^yesterday/i,today:/^t(oday)?/i,tomorrow:/^tomorrow/i,now:/^n(ow)?/i,millisecond:/^ms|milli(second)?s?/i,second:/^sec(ond)?s?/i,minute:/^min(ute)?s?/i,hour:/^h(ou)?rs?/i,week:/^w(ee)?k/i,month:/^m(o(nth)?s?)?/i,day:/^d(ays?)?/i,year:/^y((ea)?rs?)?/i,shortMeridian:/^(a|p)/i,longMeridian:/^(a\\.?m?\\.?|p\\.?m?\\.?)/i,timezone:/^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt)/i,ordinalSuffix:/^\\s*(st|nd|rd|th)/i,timeContext:/^\\s*(\\:|a|p)/i},abbreviatedTimeZoneStandard:{GMT:"-000",EST:"-0400",CST:"-0500",MST:"-0600",PST:"-0700"},abbreviatedTimeZoneDST:{GMT:"-000",EDT:"-0500",CDT:"-0600",MDT:"-0700",PDT:"-0800"}};
',
  'ja-JP' => 'Date.CultureInfo={name:"ja-JP",englishName:"Japanese (Japan)",nativeName:"日本語 (日本)",dayNames:["日曜日","月曜日","火曜日","水曜日","木曜日","金曜日","土曜日"],abbreviatedDayNames:["日","月","火","水","木","金","土"],shortestDayNames:["日","月","火","水","木","金","土"],firstLetterDayNames:["日","月","火","水","木","金","土"],monthNames:["1月","2月","3月","4月","5月","6月","7月","8月","9月","10月","11月","12月"],abbreviatedMonthNames:["1","2","3","4","5","6","7","8","9","10","11","12"],amDesignator:"午前",pmDesignator:"午後",firstDayOfWeek:0,twoDigitYearMax:2029,dateElementOrder:"ymd",formatPatterns:{shortDate:"yyyy/MM/dd",longDate:"yyyy\'年\'M\'月\'d\'日\'",shortTime:"H:mm",longTime:"H:mm:ss",fullDateTime:"yyyy\'年\'M\'月\'d\'日\' H:mm:ss",sortableDateTime:"yyyy-MM-ddTHH:mm:ss",universalSortableDateTime:"yyyy-MM-dd HH:mm:ssZ",rfc1123:"ddd, dd MMM yyyy HH:mm:ss GMT",monthDay:"M\'月\'d\'日\'",yearMonth:"yyyy\'年\'M\'月\'"},regexPatterns:{jan:/^1(月)?/i,feb:/^2(月)?/i,mar:/^3(月)?/i,apr:/^4(月)?/i,may:/^5(月)?/i,jun:/^6(月)?/i,jul:/^7(月)?/i,aug:/^8(月)?/i,sep:/^9(月)?/i,oct:/^10(月)?/i,nov:/^11(月)?/i,dec:/^12(月)?/i,sun:/^日曜日/i,mon:/^月曜日/i,tue:/^火曜日/i,wed:/^水曜日/i,thu:/^木曜日/i,fri:/^金曜日/i,sat:/^土曜日/i,future:/^next/i,past:/^last|past|prev(ious)?/i,add:/^(\\+|after|from)/i,subtract:/^(\\-|before|ago)/i,yesterday:/^yesterday/i,today:/^t(oday)?/i,tomorrow:/^tomorrow/i,now:/^n(ow)?/i,millisecond:/^ms|milli(second)?s?/i,second:/^sec(ond)?s?/i,minute:/^min(ute)?s?/i,hour:/^h(ou)?rs?/i,week:/^w(ee)?k/i,month:/^m(o(nth)?s?)?/i,day:/^d(ays?)?/i,year:/^y((ea)?rs?)?/i,shortMeridian:/^(a|p)/i,longMeridian:/^(a\\.?m?\\.?|p\\.?m?\\.?)/i,timezone:/^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt)/i,ordinalSuffix:/^\\s*(st|nd|rd|th)/i,timeContext:/^\\s*(\\:|a|p)/i},abbreviatedTimeZoneStandard:{GMT:"-000",EST:"-0400",CST:"-0500",MST:"-0600",PST:"-0700"},abbreviatedTimeZoneDST:{GMT:"-000",EDT:"-0500",CDT:"-0600",MDT:"-0700",PDT:"-0800"}};
',
  'ka-GE' => 'Date.CultureInfo={name:"ka-GE",englishName:"Georgian (Georgia)",nativeName:"ქართული (საქართველო)",dayNames:["კვირა","ორშაბათი","სამშაბათი","ოთხშაბათი","ხუთშაბათი","პარასკევი","შაბათი"],abbreviatedDayNames:["კვირა","ორშაბათი","სამშაბათი","ოთხშაბათი","ხუთშაბათი","პარასკევი","შაბათი"],shortestDayNames:["კ","ო","ს","ო","ხ","პ","შ"],firstLetterDayNames:["კ","ო","ს","ო","ხ","პ","შ"],monthNames:["იანვარი","თებერვალი","მარტი","აპრილი","მაისი","ივნისი","ივლისი","აგვისტო","სექტემბერი","ოქტომბერი","ნოემბერი","დეკემბერი"],abbreviatedMonthNames:["იან","თებ","მარ","აპრ","მაის","ივნ","ივლ","აგვ","სექ","ოქტ","ნოემ","დეკ"],amDesignator:"",pmDesignator:"",firstDayOfWeek:1,twoDigitYearMax:2029,dateElementOrder:"dmy",formatPatterns:{shortDate:"dd.MM.yyyy",longDate:"yyyy \'წლის\' dd MM, dddd",shortTime:"H:mm",longTime:"H:mm:ss",fullDateTime:"yyyy \'წლის\' dd MM, dddd H:mm:ss",sortableDateTime:"yyyy-MM-ddTHH:mm:ss",universalSortableDateTime:"yyyy-MM-dd HH:mm:ssZ",rfc1123:"ddd, dd MMM yyyy HH:mm:ss GMT",monthDay:"dd MM",yearMonth:"MMMM yyyy"},regexPatterns:{jan:/^იან(ვარი)?/i,feb:/^თებ(ერვალი)?/i,mar:/^მარ(ტი)?/i,apr:/^აპრ(ილი)?/i,may:/^მაის(ი)?/i,jun:/^ივნ(ისი)?/i,jul:/^ივლ(ისი)?/i,aug:/^აგვ(ისტო)?/i,sep:/^სექ(ტემბერი)?/i,oct:/^ოქტ(ომბერი)?/i,nov:/^ნოემ(ბერი)?/i,dec:/^დეკ(ემბერი)?/i,sun:/^კ(1)?/i,mon:/^ო(1)?/i,tue:/^ს(1)?/i,wed:/^ო(1)?/i,thu:/^ხ(1)?/i,fri:/^პ(1)?/i,sat:/^შ(1)?/i,future:/^next/i,past:/^last|past|prev(ious)?/i,add:/^(\\+|after|from)/i,subtract:/^(\\-|before|ago)/i,yesterday:/^yesterday/i,today:/^t(oday)?/i,tomorrow:/^tomorrow/i,now:/^n(ow)?/i,millisecond:/^ms|milli(second)?s?/i,second:/^sec(ond)?s?/i,minute:/^min(ute)?s?/i,hour:/^h(ou)?rs?/i,week:/^w(ee)?k/i,month:/^m(o(nth)?s?)?/i,day:/^d(ays?)?/i,year:/^y((ea)?rs?)?/i,shortMeridian:/^(a|p)/i,longMeridian:/^(a\\.?m?\\.?|p\\.?m?\\.?)/i,timezone:/^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt)/i,ordinalSuffix:/^\\s*(st|nd|rd|th)/i,timeContext:/^\\s*(\\:|a|p)/i},abbreviatedTimeZoneStandard:{GMT:"-000",EST:"-0400",CST:"-0500",MST:"-0600",PST:"-0700"},abbreviatedTimeZoneDST:{GMT:"-000",EDT:"-0500",CDT:"-0600",MDT:"-0700",PDT:"-0800"}};
',
  'kk-KZ' => 'Date.CultureInfo={name:"kk-KZ",englishName:"Kazakh (Kazakhstan)",nativeName:"Қазақ (Қазақстан)",dayNames:["Жексенбі","Дүйсенбі","Сейсенбі","Сәрсенбі","Бейсенбі","Жұма","Сенбі"],abbreviatedDayNames:["Жк","Дс","Сс","Ср","Бс","Жм","Сн"],shortestDayNames:["Жк","Дс","Сс","Ср","Бс","Жм","Сн"],firstLetterDayNames:["Ж","Д","С","С","Б","Ж","С"],monthNames:["қаңтар","ақпан","наурыз","сәуір","мамыр","маусым","шілде","тамыз","қыркүйек","қазан","қараша","желтоқсан"],abbreviatedMonthNames:["Қаң","Ақп","Нау","Сәу","Мам","Мау","Шіл","Там","Қыр","Қаз","Қар","Жел"],amDesignator:"",pmDesignator:"",firstDayOfWeek:1,twoDigitYearMax:2029,dateElementOrder:"dmy",formatPatterns:{shortDate:"dd.MM.yyyy",longDate:"d MMMM yyyy \'ж.\'",shortTime:"H:mm",longTime:"H:mm:ss",fullDateTime:"d MMMM yyyy \'ж.\' H:mm:ss",sortableDateTime:"yyyy-MM-ddTHH:mm:ss",universalSortableDateTime:"yyyy-MM-dd HH:mm:ssZ",rfc1123:"ddd, dd MMM yyyy HH:mm:ss GMT",monthDay:"d MMMM",yearMonth:"MMMM yyyy"},regexPatterns:{jan:/^қаң(тар)?/i,feb:/^ақп(ан)?/i,mar:/^нау(рыз)?/i,apr:/^сәу(ір)?/i,may:/^мам(ыр)?/i,jun:/^мау(сым)?/i,jul:/^шіл(де)?/i,aug:/^там(ыз)?/i,sep:/^қыр(күйек)?/i,oct:/^қаз(ан)?/i,nov:/^қар(аша)?/i,dec:/^жел(тоқсан)?/i,sun:/^жексенбі/i,mon:/^дүйсенбі/i,tue:/^сейсенбі/i,wed:/^сәрсенбі/i,thu:/^бейсенбі/i,fri:/^жұма/i,sat:/^сенбі/i,future:/^next/i,past:/^last|past|prev(ious)?/i,add:/^(\\+|after|from)/i,subtract:/^(\\-|before|ago)/i,yesterday:/^yesterday/i,today:/^t(oday)?/i,tomorrow:/^tomorrow/i,now:/^n(ow)?/i,millisecond:/^ms|milli(second)?s?/i,second:/^sec(ond)?s?/i,minute:/^min(ute)?s?/i,hour:/^h(ou)?rs?/i,week:/^w(ee)?k/i,month:/^m(o(nth)?s?)?/i,day:/^d(ays?)?/i,year:/^y((ea)?rs?)?/i,shortMeridian:/^(a|p)/i,longMeridian:/^(a\\.?m?\\.?|p\\.?m?\\.?)/i,timezone:/^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt)/i,ordinalSuffix:/^\\s*(st|nd|rd|th)/i,timeContext:/^\\s*(\\:|a|p)/i},abbreviatedTimeZoneStandard:{GMT:"-000",EST:"-0400",CST:"-0500",MST:"-0600",PST:"-0700"},abbreviatedTimeZoneDST:{GMT:"-000",EDT:"-0500",CDT:"-0600",MDT:"-0700",PDT:"-0800"}};
',
  'kn-IN' => 'Date.CultureInfo={name:"kn-IN",englishName:"Kannada (India)",nativeName:"ಕನ್ನಡ (ಭಾರತ)",dayNames:["ಭಾನುವಾರ","ಸೋಮವಾರ","ಮಂಗಳವಾರ","ಬುಧವಾರ","ಗುರುವಾರ","ಶುಕ್ರವಾರ","ಶನಿವಾರ"],abbreviatedDayNames:["ಭಾನು.","ಸೋಮ.","ಮಂಗಳ.","ಬುಧ.","ಗುರು.","ಶುಕ್ರ.","ಶನಿ."],shortestDayNames:["ರ","ಸ","ಮ","ಬ","ಗ","ಶ","ಶ"],firstLetterDayNames:["ರ","ಸ","ಮ","ಬ","ಗ","ಶ","ಶ"],monthNames:["ಜನವರಿ","ಫೆಬ್ರವರಿ","ಮಾರ್ಚ್","ಎಪ್ರಿಲ್","ಮೇ","ಜೂನ್","ಜುಲೈ","ಆಗಸ್ಟ್","ಸೆಪ್ಟಂಬರ್","ಅಕ್ಟೋಬರ್","ನವೆಂಬರ್","ಡಿಸೆಂಬರ್"],abbreviatedMonthNames:["ಜನವರಿ","ಫೆಬ್ರವರಿ","ಮಾರ್ಚ್","ಎಪ್ರಿಲ್","ಮೇ","ಜೂನ್","ಜುಲೈ","ಆಗಸ್ಟ್","ಸೆಪ್ಟಂಬರ್","ಅಕ್ಟೋಬರ್","ನವೆಂಬರ್","ಡಿಸೆಂಬರ್"],amDesignator:"ಪೂರ್ವಾಹ್ನ",pmDesignator:"ಅಪರಾಹ್ನ",firstDayOfWeek:1,twoDigitYearMax:2029,dateElementOrder:"dmy",formatPatterns:{shortDate:"dd-MM-yy",longDate:"dd MMMM yyyy",shortTime:"HH:mm",longTime:"HH:mm:ss",fullDateTime:"dd MMMM yyyy HH:mm:ss",sortableDateTime:"yyyy-MM-ddTHH:mm:ss",universalSortableDateTime:"yyyy-MM-dd HH:mm:ssZ",rfc1123:"ddd, dd MMM yyyy HH:mm:ss GMT",monthDay:"dd MMMM",yearMonth:"MMMM, yyyy"},regexPatterns:{jan:/^ಜನವರಿ/i,feb:/^ಫೆಬ್ರವರಿ/i,mar:/^ಮಾರ್ಚ್/i,apr:/^ಎಪ್ರಿಲ್/i,may:/^ಮೇ/i,jun:/^ಜೂನ್/i,jul:/^ಜುಲೈ/i,aug:/^ಆಗಸ್ಟ್/i,sep:/^ಸೆಪ್ಟಂಬರ್/i,oct:/^ಅಕ್ಟೋಬರ್/i,nov:/^ನವೆಂಬರ್/i,dec:/^ಡಿಸೆಂಬರ್/i,sun:/^ರ(ಾನು(.(ವಾರ)?)?)?/i,mon:/^ಸ(ೋಮ(.(ವಾರ)?)?)?/i,tue:/^ಮ(ಂಗಳ(.(ವಾರ)?)?)?/i,wed:/^ಬ(ುಧ(.(ವಾರ)?)?)?/i,thu:/^ಗ(ುರು(.(ವಾರ)?)?)?/i,fri:/^ಶ(ುಕ್ರ(.(ವಾರ)?)?)?/i,sat:/^ಶ(ನಿ(.(ವಾರ)?)?)?/i,future:/^next/i,past:/^last|past|prev(ious)?/i,add:/^(\\+|after|from)/i,subtract:/^(\\-|before|ago)/i,yesterday:/^yesterday/i,today:/^t(oday)?/i,tomorrow:/^tomorrow/i,now:/^n(ow)?/i,millisecond:/^ms|milli(second)?s?/i,second:/^sec(ond)?s?/i,minute:/^min(ute)?s?/i,hour:/^h(ou)?rs?/i,week:/^w(ee)?k/i,month:/^m(o(nth)?s?)?/i,day:/^d(ays?)?/i,year:/^y((ea)?rs?)?/i,shortMeridian:/^(a|p)/i,longMeridian:/^(a\\.?m?\\.?|p\\.?m?\\.?)/i,timezone:/^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt)/i,ordinalSuffix:/^\\s*(st|nd|rd|th)/i,timeContext:/^\\s*(\\:|a|p)/i},abbreviatedTimeZoneStandard:{GMT:"-000",EST:"-0400",CST:"-0500",MST:"-0600",PST:"-0700"},abbreviatedTimeZoneDST:{GMT:"-000",EDT:"-0500",CDT:"-0600",MDT:"-0700",PDT:"-0800"}};
',
  'ko-KR' => 'Date.CultureInfo={name:"ko-KR",englishName:"Korean (Korea)",nativeName:"한국어 (대한민국)",dayNames:["일요일","월요일","화요일","수요일","목요일","금요일","토요일"],abbreviatedDayNames:["일","월","화","수","목","금","토"],shortestDayNames:["일","월","화","수","목","금","토"],firstLetterDayNames:["일","월","화","수","목","금","토"],monthNames:["1월","2월","3월","4월","5월","6월","7월","8월","9월","10월","11월","12월"],abbreviatedMonthNames:["1","2","3","4","5","6","7","8","9","10","11","12"],amDesignator:"오전",pmDesignator:"오후",firstDayOfWeek:0,twoDigitYearMax:2029,dateElementOrder:"ymd",formatPatterns:{shortDate:"yyyy-MM-dd",longDate:"yyyy\'년\' M\'월\' d\'일\' dddd",shortTime:"tt h:mm",longTime:"tt h:mm:ss",fullDateTime:"yyyy\'년\' M\'월\' d\'일\' dddd tt h:mm:ss",sortableDateTime:"yyyy-MM-ddTHH:mm:ss",universalSortableDateTime:"yyyy-MM-dd HH:mm:ssZ",rfc1123:"ddd, dd MMM yyyy HH:mm:ss GMT",monthDay:"M\'월\' d\'일\'",yearMonth:"yyyy\'년\' M\'월\'"},regexPatterns:{jan:/^1(월)?/i,feb:/^2(월)?/i,mar:/^3(월)?/i,apr:/^4(월)?/i,may:/^5(월)?/i,jun:/^6(월)?/i,jul:/^7(월)?/i,aug:/^8(월)?/i,sep:/^9(월)?/i,oct:/^10(월)?/i,nov:/^11(월)?/i,dec:/^12(월)?/i,sun:/^일요일/i,mon:/^월요일/i,tue:/^화요일/i,wed:/^수요일/i,thu:/^목요일/i,fri:/^금요일/i,sat:/^토요일/i,future:/^next/i,past:/^last|past|prev(ious)?/i,add:/^(\\+|after|from)/i,subtract:/^(\\-|before|ago)/i,yesterday:/^yesterday/i,today:/^t(oday)?/i,tomorrow:/^tomorrow/i,now:/^n(ow)?/i,millisecond:/^ms|milli(second)?s?/i,second:/^sec(ond)?s?/i,minute:/^min(ute)?s?/i,hour:/^h(ou)?rs?/i,week:/^w(ee)?k/i,month:/^m(o(nth)?s?)?/i,day:/^d(ays?)?/i,year:/^y((ea)?rs?)?/i,shortMeridian:/^(a|p)/i,longMeridian:/^(a\\.?m?\\.?|p\\.?m?\\.?)/i,timezone:/^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt)/i,ordinalSuffix:/^\\s*(st|nd|rd|th)/i,timeContext:/^\\s*(\\:|a|p)/i},abbreviatedTimeZoneStandard:{GMT:"-000",EST:"-0400",CST:"-0500",MST:"-0600",PST:"-0700"},abbreviatedTimeZoneDST:{GMT:"-000",EDT:"-0500",CDT:"-0600",MDT:"-0700",PDT:"-0800"}};
',
  'kok-IN' => 'Date.CultureInfo={name:"kok-IN",englishName:"Konkani (India)",nativeName:"कोंकणी (भारत)",dayNames:["आयतार","सोमार","मंगळार","बुधवार","बिरेस्तार","सुक्रार","शेनवार"],abbreviatedDayNames:["आय.","सोम.","मंगळ.","बुध.","बिरे.","सुक्र.","शेन."],shortestDayNames:["आ","स","म","ब","ब","स","श"],firstLetterDayNames:["आ","स","म","ब","ब","स","श"],monthNames:["जानेवारी","फेब्रुवारी","मार्च","एप्रिल","मे","जून","जुलै","ऑगस्ट","सप्टेंबर","ऑक्टोबर","नोवेम्बर","डिसेंबर"],abbreviatedMonthNames:["जानेवारी","फेब्रुवारी","मार्च","एप्रिल","मे","जून","जुलै","ऑगस्ट","सप्टेंबर","ऑक्टोबर","नोवेम्बर","डिसेंबर"],amDesignator:"म.पू.",pmDesignator:"म.नं.",firstDayOfWeek:1,twoDigitYearMax:2029,dateElementOrder:"dmy",formatPatterns:{shortDate:"dd-MM-yyyy",longDate:"dd MMMM yyyy",shortTime:"HH:mm",longTime:"HH:mm:ss",fullDateTime:"dd MMMM yyyy HH:mm:ss",sortableDateTime:"yyyy-MM-ddTHH:mm:ss",universalSortableDateTime:"yyyy-MM-dd HH:mm:ssZ",rfc1123:"ddd, dd MMM yyyy HH:mm:ss GMT",monthDay:"dd MMMM",yearMonth:"MMMM, yyyy"},regexPatterns:{jan:/^जानेवारी/i,feb:/^फेब्रुवारी/i,mar:/^मार्च/i,apr:/^एप्रिल/i,may:/^मे/i,jun:/^जून/i,jul:/^जुलै/i,aug:/^ऑगस्ट/i,sep:/^सप्टेंबर/i,oct:/^ऑक्टोबर/i,nov:/^नोवेम्बर/i,dec:/^डिसेंबर/i,sun:/^आ(य(.(तार)?)?)?/i,mon:/^स(ोम(.(ार)?)?)?/i,tue:/^म(ंगळ(.(ार)?)?)?/i,wed:/^ब(ुध(.(वार)?)?)?/i,thu:/^ब(िरे(.(स्तार)?)?)?/i,fri:/^स(ुक्र(.(ार)?)?)?/i,sat:/^श(ेन(.(वार)?)?)?/i,future:/^next/i,past:/^last|past|prev(ious)?/i,add:/^(\\+|after|from)/i,subtract:/^(\\-|before|ago)/i,yesterday:/^yesterday/i,today:/^t(oday)?/i,tomorrow:/^tomorrow/i,now:/^n(ow)?/i,millisecond:/^ms|milli(second)?s?/i,second:/^sec(ond)?s?/i,minute:/^min(ute)?s?/i,hour:/^h(ou)?rs?/i,week:/^w(ee)?k/i,month:/^m(o(nth)?s?)?/i,day:/^d(ays?)?/i,year:/^y((ea)?rs?)?/i,shortMeridian:/^(a|p)/i,longMeridian:/^(a\\.?m?\\.?|p\\.?m?\\.?)/i,timezone:/^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt)/i,ordinalSuffix:/^\\s*(st|nd|rd|th)/i,timeContext:/^\\s*(\\:|a|p)/i},abbreviatedTimeZoneStandard:{GMT:"-000",EST:"-0400",CST:"-0500",MST:"-0600",PST:"-0700"},abbreviatedTimeZoneDST:{GMT:"-000",EDT:"-0500",CDT:"-0600",MDT:"-0700",PDT:"-0800"}};
',
  'ky-KG' => 'Date.CultureInfo={name:"ky-KG",englishName:"Kyrgyz (Kyrgyzstan)",nativeName:"Кыргыз (Кыргызстан)",dayNames:["Жекшемби","Дүйшөмбү","Шейшемби","Шаршемби","Бейшемби","Жума","Ишемби"],abbreviatedDayNames:["Жш","Дш","Шш","Шр","Бш","Жм","Иш"],shortestDayNames:["Жш","Дш","Шш","Шр","Бш","Жм","Иш"],firstLetterDayNames:["Ж","Д","Ш","Ш","Б","Ж","И"],monthNames:["Январь","Февраль","Март","Апрель","Май","Июнь","Июль","Август","Сентябрь","Октябрь","Ноябрь","Декабрь"],abbreviatedMonthNames:["Янв","Фев","Мар","Апр","Май","Июн","Июл","Авг","Сен","Окт","Ноя","Дек"],amDesignator:"",pmDesignator:"",firstDayOfWeek:1,twoDigitYearMax:2029,dateElementOrder:"dmy",formatPatterns:{shortDate:"dd.MM.yy",longDate:"d\'-\'MMMM yyyy\'-ж.\'",shortTime:"H:mm",longTime:"H:mm:ss",fullDateTime:"d\'-\'MMMM yyyy\'-ж.\' H:mm:ss",sortableDateTime:"yyyy-MM-ddTHH:mm:ss",universalSortableDateTime:"yyyy-MM-dd HH:mm:ssZ",rfc1123:"ddd, dd MMM yyyy HH:mm:ss GMT",monthDay:"d MMMM",yearMonth:"MMMM yyyy\'-ж.\'"},regexPatterns:{jan:/^янв(арь)?/i,feb:/^фев(раль)?/i,mar:/^мар(т)?/i,apr:/^апр(ель)?/i,may:/^май/i,jun:/^июн(ь)?/i,jul:/^июл(ь)?/i,aug:/^авг(уст)?/i,sep:/^сен(тябрь)?/i,oct:/^окт(ябрь)?/i,nov:/^ноя(брь)?/i,dec:/^дек(абрь)?/i,sun:/^жекшемби/i,mon:/^дүйшөмбү/i,tue:/^шейшемби/i,wed:/^шаршемби/i,thu:/^бейшемби/i,fri:/^жума/i,sat:/^ишемби/i,future:/^next/i,past:/^last|past|prev(ious)?/i,add:/^(\\+|after|from)/i,subtract:/^(\\-|before|ago)/i,yesterday:/^yesterday/i,today:/^t(oday)?/i,tomorrow:/^tomorrow/i,now:/^n(ow)?/i,millisecond:/^ms|milli(second)?s?/i,second:/^sec(ond)?s?/i,minute:/^min(ute)?s?/i,hour:/^h(ou)?rs?/i,week:/^w(ee)?k/i,month:/^m(o(nth)?s?)?/i,day:/^d(ays?)?/i,year:/^y((ea)?rs?)?/i,shortMeridian:/^(a|p)/i,longMeridian:/^(a\\.?m?\\.?|p\\.?m?\\.?)/i,timezone:/^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt)/i,ordinalSuffix:/^\\s*(st|nd|rd|th)/i,timeContext:/^\\s*(\\:|a|p)/i},abbreviatedTimeZoneStandard:{GMT:"-000",EST:"-0400",CST:"-0500",MST:"-0600",PST:"-0700"},abbreviatedTimeZoneDST:{GMT:"-000",EDT:"-0500",CDT:"-0600",MDT:"-0700",PDT:"-0800"}};
',
  'lt-LT' => 'Date.CultureInfo={name:"lt-LT",englishName:"Lithuanian (Lithuania)",nativeName:"lietuvių (Lietuva)",dayNames:["sekmadienis","pirmadienis","antradienis","trečiadienis","ketvirtadienis","penktadienis","šeštadienis"],abbreviatedDayNames:["Sk","Pr","An","Tr","Kt","Pn","Št"],shortestDayNames:["S","P","A","T","K","Pn","Š"],firstLetterDayNames:["S","P","A","T","K","P","Š"],monthNames:["sausis","vasaris","kovas","balandis","gegužė","birželis","liepa","rugpjūtis","rugsėjis","spalis","lapkritis","gruodis"],abbreviatedMonthNames:["Sau","Vas","Kov","Bal","Geg","Bir","Lie","Rgp","Rgs","Spl","Lap","Grd"],amDesignator:"",pmDesignator:"",firstDayOfWeek:1,twoDigitYearMax:2029,dateElementOrder:"ymd",formatPatterns:{shortDate:"yyyy.MM.dd",longDate:"yyyy \'m.\' MMMM d \'d.\'",shortTime:"HH:mm",longTime:"HH:mm:ss",fullDateTime:"yyyy \'m.\' MMMM d \'d.\' HH:mm:ss",sortableDateTime:"yyyy-MM-ddTHH:mm:ss",universalSortableDateTime:"yyyy-MM-dd HH:mm:ssZ",rfc1123:"ddd, dd MMM yyyy HH:mm:ss GMT",monthDay:"MMMM d \'d.\'",yearMonth:"yyyy \'m.\' MMMM"},regexPatterns:{jan:/^sau(sis)?/i,feb:/^vas(aris)?/i,mar:/^kov(as)?/i,apr:/^bal(andis)?/i,may:/^geg(užė)?/i,jun:/^bir(želis)?/i,jul:/^lie(pa)?/i,aug:/^rugpjūtis/i,sep:/^rugsėjis/i,oct:/^spalis/i,nov:/^lap(kritis)?/i,dec:/^gruodis/i,sun:/^s(k(kmadienis)?)?/i,mon:/^p(r(rmadienis)?)?/i,tue:/^a(n(tradienis)?)?/i,wed:/^t(r(ečiadienis)?)?/i,thu:/^k(t(tvirtadienis)?)?/i,fri:/^penktadienis/i,sat:/^š(t(štadienis)?)?/i,future:/^next/i,past:/^last|past|prev(ious)?/i,add:/^(\\+|after|from)/i,subtract:/^(\\-|before|ago)/i,yesterday:/^yesterday/i,today:/^t(oday)?/i,tomorrow:/^tomorrow/i,now:/^n(ow)?/i,millisecond:/^ms|milli(second)?s?/i,second:/^sec(ond)?s?/i,minute:/^min(ute)?s?/i,hour:/^h(ou)?rs?/i,week:/^w(ee)?k/i,month:/^m(o(nth)?s?)?/i,day:/^d(ays?)?/i,year:/^y((ea)?rs?)?/i,shortMeridian:/^(a|p)/i,longMeridian:/^(a\\.?m?\\.?|p\\.?m?\\.?)/i,timezone:/^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt)/i,ordinalSuffix:/^\\s*(st|nd|rd|th)/i,timeContext:/^\\s*(\\:|a|p)/i},abbreviatedTimeZoneStandard:{GMT:"-000",EST:"-0400",CST:"-0500",MST:"-0600",PST:"-0700"},abbreviatedTimeZoneDST:{GMT:"-000",EDT:"-0500",CDT:"-0600",MDT:"-0700",PDT:"-0800"}};
',
  'lv-LV' => 'Date.CultureInfo={name:"lv-LV",englishName:"Latvian (Latvia)",nativeName:"latviešu (Latvija)",dayNames:["svētdiena","pirmdiena","otrdiena","trešdiena","ceturtdiena","piektdiena","sestdiena"],abbreviatedDayNames:["Sv","Pr","Ot","Tr","Ce","Pk","Se"],shortestDayNames:["Sv","Pr","Ot","Tr","Ce","Pk","Se"],firstLetterDayNames:["S","P","O","T","C","P","S"],monthNames:["janvāris","februāris","marts","aprīlis","maijs","jūnijs","jūlijs","augusts","septembris","oktobris","novembris","decembris"],abbreviatedMonthNames:["Jan","Feb","Mar","Apr","Mai","Jūn","Jūl","Aug","Sep","Okt","Nov","Dec"],amDesignator:"",pmDesignator:"",firstDayOfWeek:1,twoDigitYearMax:2029,dateElementOrder:"ymd",formatPatterns:{shortDate:"yyyy.MM.dd.",longDate:"dddd, yyyy\'. gada \'d. MMMM",shortTime:"H:mm",longTime:"H:mm:ss",fullDateTime:"dddd, yyyy\'. gada \'d. MMMM H:mm:ss",sortableDateTime:"yyyy-MM-ddTHH:mm:ss",universalSortableDateTime:"yyyy-MM-dd HH:mm:ssZ",rfc1123:"ddd, dd MMM yyyy HH:mm:ss GMT",monthDay:"d. MMMM",yearMonth:"yyyy. MMMM"},regexPatterns:{jan:/^jan(vāris)?/i,feb:/^feb(ruāris)?/i,mar:/^mar(ts)?/i,apr:/^apr(īlis)?/i,may:/^mai(js)?/i,jun:/^jūn(ijs)?/i,jul:/^jūl(ijs)?/i,aug:/^aug(usts)?/i,sep:/^sep(tembris)?/i,oct:/^okt(obris)?/i,nov:/^nov(embris)?/i,dec:/^dec(embris)?/i,sun:/^svētdiena/i,mon:/^pirmdiena/i,tue:/^otrdiena/i,wed:/^trešdiena/i,thu:/^ceturtdiena/i,fri:/^piektdiena/i,sat:/^sestdiena/i,future:/^next/i,past:/^last|past|prev(ious)?/i,add:/^(\\+|after|from)/i,subtract:/^(\\-|before|ago)/i,yesterday:/^yesterday/i,today:/^t(oday)?/i,tomorrow:/^tomorrow/i,now:/^n(ow)?/i,millisecond:/^ms|milli(second)?s?/i,second:/^sec(ond)?s?/i,minute:/^min(ute)?s?/i,hour:/^h(ou)?rs?/i,week:/^w(ee)?k/i,month:/^m(o(nth)?s?)?/i,day:/^d(ays?)?/i,year:/^y((ea)?rs?)?/i,shortMeridian:/^(a|p)/i,longMeridian:/^(a\\.?m?\\.?|p\\.?m?\\.?)/i,timezone:/^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt)/i,ordinalSuffix:/^\\s*(st|nd|rd|th)/i,timeContext:/^\\s*(\\:|a|p)/i},abbreviatedTimeZoneStandard:{GMT:"-000",EST:"-0400",CST:"-0500",MST:"-0600",PST:"-0700"},abbreviatedTimeZoneDST:{GMT:"-000",EDT:"-0500",CDT:"-0600",MDT:"-0700",PDT:"-0800"}};
',
  'mi-NZ' => 'Date.CultureInfo={name:"mi-NZ",englishName:"Maori (New Zealand)",nativeName:"Reo Māori (Aotearoa)",dayNames:["Rātapu","Mane","Tūrei","Wenerei","Tāite","Paraire","Hātarei"],abbreviatedDayNames:["Ta","Ma","Tū","We","Tāi","Pa","Hā"],shortestDayNames:["Ta","Ma","Tū","We","Tāi","Pa","Hā"],firstLetterDayNames:["T","M","T","W","T","P","H"],monthNames:["Kohi-tātea","Hui-tanguru","Poutū-te-rangi","Paenga-whāwhā","Haratua","Pipiri","Hōngoingoi","Here-turi-kōkā","Mahuru","Whiringa-ā-nuku","Whiringa-ā-rangi","Hakihea"],abbreviatedMonthNames:["Kohi","Hui","Pou","Pae","Hara","Pipi","Hōngoi","Here","Mahu","Whi-nu","Whi-ra","Haki"],amDesignator:"a.m.",pmDesignator:"p.m.",firstDayOfWeek:1,twoDigitYearMax:2029,dateElementOrder:"dmy",formatPatterns:{shortDate:"d/MM/yyyy",longDate:"dddd, d MMMM yyyy",shortTime:"h:mm:ss tt",longTime:"h:mm:ss tt",fullDateTime:"dddd, d MMMM yyyy h:mm:ss tt",sortableDateTime:"yyyy-MM-ddTHH:mm:ss",universalSortableDateTime:"yyyy-MM-dd HH:mm:ssZ",rfc1123:"ddd, dd MMM yyyy HH:mm:ss GMT",monthDay:"MMMM dd",yearMonth:"MMMM yyyy"},regexPatterns:{jan:/^kohi(-tātea)?/i,feb:/^hui(-tanguru)?/i,mar:/^pou(tū-te-rangi)?/i,apr:/^pae(nga-whāwhā)?/i,may:/^hara(tua)?/i,jun:/^pipi(ri)?/i,jul:/^hōngoi(ngoi)?/i,aug:/^here(-turi-kōkā)?/i,sep:/^mahu(ru)?/i,oct:/^whiringa-ā-nuku/i,nov:/^whiringa-ā-rangi/i,dec:/^haki(hea)?/i,sun:/^rātapu/i,mon:/^mane/i,tue:/^tūrei/i,wed:/^wenerei/i,thu:/^tāite/i,fri:/^paraire/i,sat:/^hātarei/i,future:/^next/i,past:/^last|past|prev(ious)?/i,add:/^(\\+|after|from)/i,subtract:/^(\\-|before|ago)/i,yesterday:/^yesterday/i,today:/^t(oday)?/i,tomorrow:/^tomorrow/i,now:/^n(ow)?/i,millisecond:/^ms|milli(second)?s?/i,second:/^sec(ond)?s?/i,minute:/^min(ute)?s?/i,hour:/^h(ou)?rs?/i,week:/^w(ee)?k/i,month:/^m(o(nth)?s?)?/i,day:/^d(ays?)?/i,year:/^y((ea)?rs?)?/i,shortMeridian:/^(a|p)/i,longMeridian:/^(a\\.?m?\\.?|p\\.?m?\\.?)/i,timezone:/^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt)/i,ordinalSuffix:/^\\s*(st|nd|rd|th)/i,timeContext:/^\\s*(\\:|a|p)/i},abbreviatedTimeZoneStandard:{GMT:"-000",EST:"-0400",CST:"-0500",MST:"-0600",PST:"-0700"},abbreviatedTimeZoneDST:{GMT:"-000",EDT:"-0500",CDT:"-0600",MDT:"-0700",PDT:"-0800"}};
',
  'mk-MK' => 'Date.CultureInfo={name:"mk-MK",englishName:"Macedonian",nativeName:"македонски јазик (Македонија)",dayNames:["недела","понеделник","вторник","среда","четврток","петок","сабота"],abbreviatedDayNames:["нед","пон","втр","срд","чет","пет","саб"],shortestDayNames:["не","по","вт","ср","че","пе","са"],firstLetterDayNames:["н","п","в","с","ч","п","с"],monthNames:["јануари","февруари","март","април","мај","јуни","јули","август","септември","октомври","ноември","декември"],abbreviatedMonthNames:["јан","фев","мар","апр","мај","јун","јул","авг","сеп","окт","ное","дек"],amDesignator:"",pmDesignator:"",firstDayOfWeek:1,twoDigitYearMax:2029,dateElementOrder:"dmy",formatPatterns:{shortDate:"dd.MM.yyyy",longDate:"dddd, dd MMMM yyyy",shortTime:"HH:mm",longTime:"HH:mm:ss",fullDateTime:"dddd, dd MMMM yyyy HH:mm:ss",sortableDateTime:"yyyy-MM-ddTHH:mm:ss",universalSortableDateTime:"yyyy-MM-dd HH:mm:ssZ",rfc1123:"ddd, dd MMM yyyy HH:mm:ss GMT",monthDay:"dd MMMM",yearMonth:"MMMM yyyy"},regexPatterns:{jan:/^јан(уари)?/i,feb:/^фев(руари)?/i,mar:/^мар(т)?/i,apr:/^апр(ил)?/i,may:/^мај/i,jun:/^јун(и)?/i,jul:/^јул(и)?/i,aug:/^авг(уст)?/i,sep:/^сеп(тември)?/i,oct:/^окт(омври)?/i,nov:/^ное(мври)?/i,dec:/^дек(ември)?/i,sun:/^не(д(ела)?)?/i,mon:/^по(н(еделник)?)?/i,tue:/^вт(р(рник)?)?/i,wed:/^ср(д(да)?)?/i,thu:/^че(т(врток)?)?/i,fri:/^пе(т(ок)?)?/i,sat:/^са(б(ота)?)?/i,future:/^next/i,past:/^last|past|prev(ious)?/i,add:/^(\\+|after|from)/i,subtract:/^(\\-|before|ago)/i,yesterday:/^yesterday/i,today:/^t(oday)?/i,tomorrow:/^tomorrow/i,now:/^n(ow)?/i,millisecond:/^ms|milli(second)?s?/i,second:/^sec(ond)?s?/i,minute:/^min(ute)?s?/i,hour:/^h(ou)?rs?/i,week:/^w(ee)?k/i,month:/^m(o(nth)?s?)?/i,day:/^d(ays?)?/i,year:/^y((ea)?rs?)?/i,shortMeridian:/^(a|p)/i,longMeridian:/^(a\\.?m?\\.?|p\\.?m?\\.?)/i,timezone:/^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt)/i,ordinalSuffix:/^\\s*(st|nd|rd|th)/i,timeContext:/^\\s*(\\:|a|p)/i},abbreviatedTimeZoneStandard:{GMT:"-000",EST:"-0400",CST:"-0500",MST:"-0600",PST:"-0700"},abbreviatedTimeZoneDST:{GMT:"-000",EDT:"-0500",CDT:"-0600",MDT:"-0700",PDT:"-0800"}};
',
  'mn-MN' => 'Date.CultureInfo={name:"mn-MN",englishName:"Mongolian (Cyrillic, Mongolia)",nativeName:"Монгол хэл (Монгол улс)",dayNames:["Ням","Даваа","Мягмар","Лхагва","Пүрэв","Баасан","Бямба"],abbreviatedDayNames:["Ня","Да","Мя","Лх","Пү","Ба","Бя"],shortestDayNames:["Ня","Да","Мя","Лх","Пү","Ба","Бя"],firstLetterDayNames:["Н","Д","М","Л","П","Б","Б"],monthNames:["1 дүгээр сар","2 дугаар сар","3 дугаар сар","4 дүгээр сар","5 дугаар сар","6 дугаар сар","7 дугаар сар","8 дугаар сар","9 дүгээр сар","10 дугаар сар","11 дүгээр сар","12 дугаар сар"],abbreviatedMonthNames:["I","II","III","IV","V","VI","VII","VШ","IX","X","XI","XII"],amDesignator:"",pmDesignator:"",firstDayOfWeek:1,twoDigitYearMax:2029,dateElementOrder:"ymd",formatPatterns:{shortDate:"yy.MM.dd",longDate:"yyyy \'оны\' MMMM d",shortTime:"H:mm",longTime:"H:mm:ss",fullDateTime:"yyyy \'оны\' MMMM d H:mm:ss",sortableDateTime:"yyyy-MM-ddTHH:mm:ss",universalSortableDateTime:"yyyy-MM-dd HH:mm:ssZ",rfc1123:"ddd, dd MMM yyyy HH:mm:ss GMT",monthDay:"d MMMM",yearMonth:"yyyy \'он\' MMMM"},regexPatterns:{jan:/^1 дүгээр сар/i,feb:/^2 дугаар сар/i,mar:/^3 дугаар сар/i,apr:/^4 дүгээр сар/i,may:/^5 дугаар сар/i,jun:/^6 дугаар сар/i,jul:/^7 дугаар сар/i,aug:/^8 дугаар сар/i,sep:/^9 дүгээр сар/i,oct:/^10 дугаар сар/i,nov:/^11 дүгээр сар/i,dec:/^12 дугаар сар/i,sun:/^ням/i,mon:/^даваа/i,tue:/^мягмар/i,wed:/^лхагва/i,thu:/^пүрэв/i,fri:/^баасан/i,sat:/^бямба/i,future:/^next/i,past:/^last|past|prev(ious)?/i,add:/^(\\+|after|from)/i,subtract:/^(\\-|before|ago)/i,yesterday:/^yesterday/i,today:/^t(oday)?/i,tomorrow:/^tomorrow/i,now:/^n(ow)?/i,millisecond:/^ms|milli(second)?s?/i,second:/^sec(ond)?s?/i,minute:/^min(ute)?s?/i,hour:/^h(ou)?rs?/i,week:/^w(ee)?k/i,month:/^m(o(nth)?s?)?/i,day:/^d(ays?)?/i,year:/^y((ea)?rs?)?/i,shortMeridian:/^(a|p)/i,longMeridian:/^(a\\.?m?\\.?|p\\.?m?\\.?)/i,timezone:/^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt)/i,ordinalSuffix:/^\\s*(st|nd|rd|th)/i,timeContext:/^\\s*(\\:|a|p)/i},abbreviatedTimeZoneStandard:{GMT:"-000",EST:"-0400",CST:"-0500",MST:"-0600",PST:"-0700"},abbreviatedTimeZoneDST:{GMT:"-000",EDT:"-0500",CDT:"-0600",MDT:"-0700",PDT:"-0800"}};
',
  'mr-IN' => 'Date.CultureInfo={name:"mr-IN",englishName:"Marathi (India)",nativeName:"मराठी (भारत)",dayNames:["रविवार","सोमवार","मंगळवार","बुधवार","गुरुवार","शुक्रवार","शनिवार"],abbreviatedDayNames:["रवि.","सोम.","मंगळ.","बुध.","गुरु.","शुक्र.","शनि."],shortestDayNames:["र","स","म","ब","ग","श","श"],firstLetterDayNames:["र","स","म","ब","ग","श","श"],monthNames:["जानेवारी","फेब्रुवारी","मार्च","एप्रिल","मे","जून","जुलै","ऑगस्ट","सप्टेंबर","ऑक्टोबर","नोव्हेंबर","डिसेंबर"],abbreviatedMonthNames:["जाने.","फेब्रु.","मार्च","एप्रिल","मे","जून","जुलै","ऑगस्ट","सप्टें.","ऑक्टो.","नोव्हें.","डिसें."],amDesignator:"म.पू.",pmDesignator:"म.नं.",firstDayOfWeek:1,twoDigitYearMax:2029,dateElementOrder:"dmy",formatPatterns:{shortDate:"dd-MM-yyyy",longDate:"dd MMMM yyyy",shortTime:"HH:mm",longTime:"HH:mm:ss",fullDateTime:"dd MMMM yyyy HH:mm:ss",sortableDateTime:"yyyy-MM-ddTHH:mm:ss",universalSortableDateTime:"yyyy-MM-dd HH:mm:ssZ",rfc1123:"ddd, dd MMM yyyy HH:mm:ss GMT",monthDay:"dd MMMM",yearMonth:"MMMM, yyyy"},regexPatterns:{jan:/^जाने(.(वारी)?)?/i,feb:/^फेब्रु(.(वारी)?)?/i,mar:/^मार्च/i,apr:/^एप्रिल/i,may:/^मे/i,jun:/^जून/i,jul:/^जुलै/i,aug:/^ऑगस्ट/i,sep:/^सप्टें(.(बर)?)?/i,oct:/^ऑक्टो(.(बर)?)?/i,nov:/^नोव्हें(.(बर)?)?/i,dec:/^डिसें(.(बर)?)?/i,sun:/^र(वि(.(वार)?)?)?/i,mon:/^स(ोम(.(वार)?)?)?/i,tue:/^म(ंगळ(.(वार)?)?)?/i,wed:/^ब(ुध(.(वार)?)?)?/i,thu:/^ग(ुरु(.(वार)?)?)?/i,fri:/^श(ुक्र(.(वार)?)?)?/i,sat:/^श(नि(.(वार)?)?)?/i,future:/^next/i,past:/^last|past|prev(ious)?/i,add:/^(\\+|after|from)/i,subtract:/^(\\-|before|ago)/i,yesterday:/^yesterday/i,today:/^t(oday)?/i,tomorrow:/^tomorrow/i,now:/^n(ow)?/i,millisecond:/^ms|milli(second)?s?/i,second:/^sec(ond)?s?/i,minute:/^min(ute)?s?/i,hour:/^h(ou)?rs?/i,week:/^w(ee)?k/i,month:/^m(o(nth)?s?)?/i,day:/^d(ays?)?/i,year:/^y((ea)?rs?)?/i,shortMeridian:/^(a|p)/i,longMeridian:/^(a\\.?m?\\.?|p\\.?m?\\.?)/i,timezone:/^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt)/i,ordinalSuffix:/^\\s*(st|nd|rd|th)/i,timeContext:/^\\s*(\\:|a|p)/i},abbreviatedTimeZoneStandard:{GMT:"-000",EST:"-0400",CST:"-0500",MST:"-0600",PST:"-0700"},abbreviatedTimeZoneDST:{GMT:"-000",EDT:"-0500",CDT:"-0600",MDT:"-0700",PDT:"-0800"}};
',
  'ms-BN' => 'Date.CultureInfo={name:"ms-BN",englishName:"Malay (Brunei Darussalam)",nativeName:"Bahasa Malaysia (Brunei Darussalam)",dayNames:["Ahad","Isnin","Selasa","Rabu","Khamis","Jumaat","Sabtu"],abbreviatedDayNames:["Ahad","Isnin","Sel","Rabu","Khamis","Jumaat","Sabtu"],shortestDayNames:["A","I","S","R","K","J","S"],firstLetterDayNames:["A","I","S","R","K","J","S"],monthNames:["Januari","Februari","Mac","April","Mei","Jun","Julai","Ogos","September","Oktober","November","Disember"],abbreviatedMonthNames:["Jan","Feb","Mac","Apr","Mei","Jun","Jul","Ogos","Sept","Okt","Nov","Dis"],amDesignator:"",pmDesignator:"",firstDayOfWeek:1,twoDigitYearMax:2029,dateElementOrder:"dmy",formatPatterns:{shortDate:"dd/MM/yyyy",longDate:"dd MMMM yyyy",shortTime:"H:mm",longTime:"H:mm:ss",fullDateTime:"dd MMMM yyyy H:mm:ss",sortableDateTime:"yyyy-MM-ddTHH:mm:ss",universalSortableDateTime:"yyyy-MM-dd HH:mm:ssZ",rfc1123:"ddd, dd MMM yyyy HH:mm:ss GMT",monthDay:"dd MMMM",yearMonth:"MMMM yyyy"},regexPatterns:{jan:/^jan(uari)?/i,feb:/^feb(ruari)?/i,mar:/^mac/i,apr:/^apr(il)?/i,may:/^mei/i,jun:/^jun/i,jul:/^jul(ai)?/i,aug:/^ogos/i,sep:/^sep(t(ember)?)?/i,oct:/^okt(ober)?/i,nov:/^nov(ember)?/i,dec:/^dis(ember)?/i,sun:/^a(1)?/i,mon:/^i(1)?/i,tue:/^s(el(asa)?)?/i,wed:/^r(1)?/i,thu:/^k(1)?/i,fri:/^j(1)?/i,sat:/^s(1)?/i,future:/^next/i,past:/^last|past|prev(ious)?/i,add:/^(\\+|after|from)/i,subtract:/^(\\-|before|ago)/i,yesterday:/^yesterday/i,today:/^t(oday)?/i,tomorrow:/^tomorrow/i,now:/^n(ow)?/i,millisecond:/^ms|milli(second)?s?/i,second:/^sec(ond)?s?/i,minute:/^min(ute)?s?/i,hour:/^h(ou)?rs?/i,week:/^w(ee)?k/i,month:/^m(o(nth)?s?)?/i,day:/^d(ays?)?/i,year:/^y((ea)?rs?)?/i,shortMeridian:/^(a|p)/i,longMeridian:/^(a\\.?m?\\.?|p\\.?m?\\.?)/i,timezone:/^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt)/i,ordinalSuffix:/^\\s*(st|nd|rd|th)/i,timeContext:/^\\s*(\\:|a|p)/i},abbreviatedTimeZoneStandard:{GMT:"-000",EST:"-0400",CST:"-0500",MST:"-0600",PST:"-0700"},abbreviatedTimeZoneDST:{GMT:"-000",EDT:"-0500",CDT:"-0600",MDT:"-0700",PDT:"-0800"}};
',
  'ms-MY' => 'Date.CultureInfo={name:"ms-MY",englishName:"Malay (Malaysia)",nativeName:"Bahasa Malaysia (Malaysia)",dayNames:["Ahad","Isnin","Selasa","Rabu","Khamis","Jumaat","Sabtu"],abbreviatedDayNames:["Ahad","Isnin","Sel","Rabu","Khamis","Jumaat","Sabtu"],shortestDayNames:["A","I","S","R","K","J","S"],firstLetterDayNames:["A","I","S","R","K","J","S"],monthNames:["Januari","Februari","Mac","April","Mei","Jun","Julai","Ogos","September","Oktober","November","Disember"],abbreviatedMonthNames:["Jan","Feb","Mac","Apr","Mei","Jun","Jul","Ogos","Sept","Okt","Nov","Dis"],amDesignator:"",pmDesignator:"",firstDayOfWeek:1,twoDigitYearMax:2029,dateElementOrder:"dmy",formatPatterns:{shortDate:"dd/MM/yyyy",longDate:"dd MMMM yyyy",shortTime:"H:mm",longTime:"H:mm:ss",fullDateTime:"dd MMMM yyyy H:mm:ss",sortableDateTime:"yyyy-MM-ddTHH:mm:ss",universalSortableDateTime:"yyyy-MM-dd HH:mm:ssZ",rfc1123:"ddd, dd MMM yyyy HH:mm:ss GMT",monthDay:"dd MMMM",yearMonth:"MMMM yyyy"},regexPatterns:{jan:/^jan(uari)?/i,feb:/^feb(ruari)?/i,mar:/^mac/i,apr:/^apr(il)?/i,may:/^mei/i,jun:/^jun/i,jul:/^jul(ai)?/i,aug:/^ogos/i,sep:/^sep(t(ember)?)?/i,oct:/^okt(ober)?/i,nov:/^nov(ember)?/i,dec:/^dis(ember)?/i,sun:/^a(1)?/i,mon:/^i(1)?/i,tue:/^s(el(asa)?)?/i,wed:/^r(1)?/i,thu:/^k(1)?/i,fri:/^j(1)?/i,sat:/^s(1)?/i,future:/^next/i,past:/^last|past|prev(ious)?/i,add:/^(\\+|after|from)/i,subtract:/^(\\-|before|ago)/i,yesterday:/^yesterday/i,today:/^t(oday)?/i,tomorrow:/^tomorrow/i,now:/^n(ow)?/i,millisecond:/^ms|milli(second)?s?/i,second:/^sec(ond)?s?/i,minute:/^min(ute)?s?/i,hour:/^h(ou)?rs?/i,week:/^w(ee)?k/i,month:/^m(o(nth)?s?)?/i,day:/^d(ays?)?/i,year:/^y((ea)?rs?)?/i,shortMeridian:/^(a|p)/i,longMeridian:/^(a\\.?m?\\.?|p\\.?m?\\.?)/i,timezone:/^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt)/i,ordinalSuffix:/^\\s*(st|nd|rd|th)/i,timeContext:/^\\s*(\\:|a|p)/i},abbreviatedTimeZoneStandard:{GMT:"-000",EST:"-0400",CST:"-0500",MST:"-0600",PST:"-0700"},abbreviatedTimeZoneDST:{GMT:"-000",EDT:"-0500",CDT:"-0600",MDT:"-0700",PDT:"-0800"}};
',
  'mt-MT' => 'Date.CultureInfo={name:"mt-MT",englishName:"Maltese (Malta)",nativeName:"Malti (Malta)",dayNames:["Il-Ħadd","It-Tnejn","It-Tlieta","L-Erbgħa","Il-Ħamis","Il-Ġimgħa","Is-Sibt"],abbreviatedDayNames:["Ħad","Tne","Tli","Erb","Ħam","Ġim","Sib"],shortestDayNames:["Ħad","Tne","Tli","Erb","Ħam","Ġim","Sib"],firstLetterDayNames:["Ħ","T","T","E","Ħ","Ġ","S"],monthNames:["Jannar","Frar","Marzu","April","Mejju","Ġunju","Lulju","Awissu","Settembru","Ottubru","Novembru","Diċembru"],abbreviatedMonthNames:["Jan","Fra","Mar","Apr","Mej","Ġun","Lul","Awi","Set","Ott","Nov","Diċ"],amDesignator:"AM",pmDesignator:"PM",firstDayOfWeek:1,twoDigitYearMax:2029,dateElementOrder:"dmy",formatPatterns:{shortDate:"dd/MM/yyyy",longDate:"dddd, d\' ta\\\' \'MMMM yyyy",shortTime:"HH:mm:ss",longTime:"HH:mm:ss",fullDateTime:"dddd, d\' ta\\\' \'MMMM yyyy HH:mm:ss",sortableDateTime:"yyyy-MM-ddTHH:mm:ss",universalSortableDateTime:"yyyy-MM-dd HH:mm:ssZ",rfc1123:"ddd, dd MMM yyyy HH:mm:ss GMT",monthDay:"MMMM dd",yearMonth:"MMMM yyyy"},regexPatterns:{jan:/^jan(nar)?/i,feb:/^fra(r)?/i,mar:/^mar(zu)?/i,apr:/^apr(il)?/i,may:/^mej(ju)?/i,jun:/^ġun(ju)?/i,jul:/^lul(ju)?/i,aug:/^awi(ssu)?/i,sep:/^set(tembru)?/i,oct:/^ott(ubru)?/i,nov:/^nov(embru)?/i,dec:/^diċ(embru)?/i,sun:/^il-ħadd/i,mon:/^it-tnejn/i,tue:/^it-tlieta/i,wed:/^l-erbgħa/i,thu:/^il-ħamis/i,fri:/^il-ġimgħa/i,sat:/^is-sibt/i,future:/^next/i,past:/^last|past|prev(ious)?/i,add:/^(\\+|after|from)/i,subtract:/^(\\-|before|ago)/i,yesterday:/^yesterday/i,today:/^t(oday)?/i,tomorrow:/^tomorrow/i,now:/^n(ow)?/i,millisecond:/^ms|milli(second)?s?/i,second:/^sec(ond)?s?/i,minute:/^min(ute)?s?/i,hour:/^h(ou)?rs?/i,week:/^w(ee)?k/i,month:/^m(o(nth)?s?)?/i,day:/^d(ays?)?/i,year:/^y((ea)?rs?)?/i,shortMeridian:/^(a|p)/i,longMeridian:/^(a\\.?m?\\.?|p\\.?m?\\.?)/i,timezone:/^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt)/i,ordinalSuffix:/^\\s*(st|nd|rd|th)/i,timeContext:/^\\s*(\\:|a|p)/i},abbreviatedTimeZoneStandard:{GMT:"-000",EST:"-0400",CST:"-0500",MST:"-0600",PST:"-0700"},abbreviatedTimeZoneDST:{GMT:"-000",EDT:"-0500",CDT:"-0600",MDT:"-0700",PDT:"-0800"}};
',
  'nb-NO' => 'Date.CultureInfo={name:"nb-NO",englishName:"Norwegian, Bokmål (Norway)",nativeName:"norsk, bokmål (Norge)",dayNames:["søndag","mandag","tirsdag","onsdag","torsdag","fredag","lørdag"],abbreviatedDayNames:["sø","ma","ti","on","to","fr","lø"],shortestDayNames:["sø","ma","ti","on","to","fr","lø"],firstLetterDayNames:["s","m","t","o","t","f","l"],monthNames:["januar","februar","mars","april","mai","juni","juli","august","september","oktober","november","desember"],abbreviatedMonthNames:["jan","feb","mar","apr","mai","jun","jul","aug","sep","okt","nov","des"],amDesignator:"",pmDesignator:"",firstDayOfWeek:1,twoDigitYearMax:2029,dateElementOrder:"dmy",formatPatterns:{shortDate:"dd.MM.yyyy",longDate:"d. MMMM yyyy",shortTime:"HH:mm",longTime:"HH:mm:ss",fullDateTime:"d. MMMM yyyy HH:mm:ss",sortableDateTime:"yyyy-MM-ddTHH:mm:ss",universalSortableDateTime:"yyyy-MM-dd HH:mm:ssZ",rfc1123:"ddd, dd MMM yyyy HH:mm:ss GMT",monthDay:"d. MMMM",yearMonth:"MMMM yyyy"},regexPatterns:{jan:/^jan(uar)?/i,feb:/^feb(ruar)?/i,mar:/^mar(s)?/i,apr:/^apr(il)?/i,may:/^mai/i,jun:/^jun(i)?/i,jul:/^jul(i)?/i,aug:/^aug(ust)?/i,sep:/^sep(t(ember)?)?/i,oct:/^okt(ober)?/i,nov:/^nov(ember)?/i,dec:/^des(ember)?/i,sun:/^søndag/i,mon:/^mandag/i,tue:/^tirsdag/i,wed:/^onsdag/i,thu:/^torsdag/i,fri:/^fredag/i,sat:/^lørdag/i,future:/^next/i,past:/^last|past|prev(ious)?/i,add:/^(\\+|after|from)/i,subtract:/^(\\-|before|ago)/i,yesterday:/^yesterday/i,today:/^t(oday)?/i,tomorrow:/^tomorrow/i,now:/^n(ow)?/i,millisecond:/^ms|milli(second)?s?/i,second:/^sec(ond)?s?/i,minute:/^min(ute)?s?/i,hour:/^h(ou)?rs?/i,week:/^w(ee)?k/i,month:/^m(o(nth)?s?)?/i,day:/^d(ays?)?/i,year:/^y((ea)?rs?)?/i,shortMeridian:/^(a|p)/i,longMeridian:/^(a\\.?m?\\.?|p\\.?m?\\.?)/i,timezone:/^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt)/i,ordinalSuffix:/^\\s*(st|nd|rd|th)/i,timeContext:/^\\s*(\\:|a|p)/i},abbreviatedTimeZoneStandard:{GMT:"-000",EST:"-0400",CST:"-0500",MST:"-0600",PST:"-0700"},abbreviatedTimeZoneDST:{GMT:"-000",EDT:"-0500",CDT:"-0600",MDT:"-0700",PDT:"-0800"}};
',
  'nl-BE' => 'Date.CultureInfo={name:"nl-BE",englishName:"Dutch (Belgium)",nativeName:"Nederlands (België)",dayNames:["zondag","maandag","dinsdag","woensdag","donderdag","vrijdag","zaterdag"],abbreviatedDayNames:["zo","ma","di","wo","do","vr","za"],shortestDayNames:["zo","ma","di","wo","do","vr","za"],firstLetterDayNames:["z","m","d","w","d","v","z"],monthNames:["januari","februari","maart","april","mei","juni","juli","augustus","september","oktober","november","december"],abbreviatedMonthNames:["jan","feb","mrt","apr","mei","jun","jul","aug","sep","okt","nov","dec"],amDesignator:"",pmDesignator:"",firstDayOfWeek:1,twoDigitYearMax:2029,dateElementOrder:"dmy",formatPatterns:{shortDate:"d/MM/yyyy",longDate:"dddd d MMMM yyyy",shortTime:"H:mm",longTime:"H:mm:ss",fullDateTime:"dddd d MMMM yyyy H:mm:ss",sortableDateTime:"yyyy-MM-ddTHH:mm:ss",universalSortableDateTime:"yyyy-MM-dd HH:mm:ssZ",rfc1123:"ddd, dd MMM yyyy HH:mm:ss GMT",monthDay:"dd MMMM",yearMonth:"MMMM yyyy"},regexPatterns:{jan:/^jan(uari)?/i,feb:/^feb(ruari)?/i,mar:/^maart/i,apr:/^apr(il)?/i,may:/^mei/i,jun:/^jun(i)?/i,jul:/^jul(i)?/i,aug:/^aug(ustus)?/i,sep:/^sep(t(ember)?)?/i,oct:/^okt(ober)?/i,nov:/^nov(ember)?/i,dec:/^dec(ember)?/i,sun:/^zondag/i,mon:/^maandag/i,tue:/^dinsdag/i,wed:/^woensdag/i,thu:/^donderdag/i,fri:/^vrijdag/i,sat:/^zaterdag/i,future:/^next/i,past:/^last|past|prev(ious)?/i,add:/^(\\+|after|from)/i,subtract:/^(\\-|before|ago)/i,yesterday:/^yesterday/i,today:/^t(oday)?/i,tomorrow:/^tomorrow/i,now:/^n(ow)?/i,millisecond:/^ms|milli(second)?s?/i,second:/^sec(ond)?s?/i,minute:/^min(ute)?s?/i,hour:/^h(ou)?rs?/i,week:/^w(ee)?k/i,month:/^m(o(nth)?s?)?/i,day:/^d(ays?)?/i,year:/^y((ea)?rs?)?/i,shortMeridian:/^(a|p)/i,longMeridian:/^(a\\.?m?\\.?|p\\.?m?\\.?)/i,timezone:/^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt)/i,ordinalSuffix:/^\\s*(st|nd|rd|th)/i,timeContext:/^\\s*(\\:|a|p)/i},abbreviatedTimeZoneStandard:{GMT:"-000",EST:"-0400",CST:"-0500",MST:"-0600",PST:"-0700"},abbreviatedTimeZoneDST:{GMT:"-000",EDT:"-0500",CDT:"-0600",MDT:"-0700",PDT:"-0800"}};
',
  'nl-NL' => 'Date.CultureInfo={name:"nl-NL",englishName:"Dutch (Netherlands)",nativeName:"Nederlands (Nederland)",dayNames:["zondag","maandag","dinsdag","woensdag","donderdag","vrijdag","zaterdag"],abbreviatedDayNames:["zo","ma","di","wo","do","vr","za"],shortestDayNames:["zo","ma","di","wo","do","vr","za"],firstLetterDayNames:["z","m","d","w","d","v","z"],monthNames:["januari","februari","maart","april","mei","juni","juli","augustus","september","oktober","november","december"],abbreviatedMonthNames:["jan","feb","mrt","apr","mei","jun","jul","aug","sep","okt","nov","dec"],amDesignator:"",pmDesignator:"",firstDayOfWeek:1,twoDigitYearMax:2029,dateElementOrder:"dmy",formatPatterns:{shortDate:"d-M-yyyy",longDate:"dddd d MMMM yyyy",shortTime:"H:mm",longTime:"H:mm:ss",fullDateTime:"dddd d MMMM yyyy H:mm:ss",sortableDateTime:"yyyy-MM-ddTHH:mm:ss",universalSortableDateTime:"yyyy-MM-dd HH:mm:ssZ",rfc1123:"ddd, dd MMM yyyy HH:mm:ss GMT",monthDay:"dd MMMM",yearMonth:"MMMM yyyy"},regexPatterns:{jan:/^jan(uari)?/i,feb:/^feb(ruari)?/i,mar:/^maart/i,apr:/^apr(il)?/i,may:/^mei/i,jun:/^jun(i)?/i,jul:/^jul(i)?/i,aug:/^aug(ustus)?/i,sep:/^sep(t(ember)?)?/i,oct:/^okt(ober)?/i,nov:/^nov(ember)?/i,dec:/^dec(ember)?/i,sun:/^zondag/i,mon:/^maandag/i,tue:/^dinsdag/i,wed:/^woensdag/i,thu:/^donderdag/i,fri:/^vrijdag/i,sat:/^zaterdag/i,future:/^next/i,past:/^last|past|prev(ious)?/i,add:/^(\\+|after|from)/i,subtract:/^(\\-|before|ago)/i,yesterday:/^yesterday/i,today:/^t(oday)?/i,tomorrow:/^tomorrow/i,now:/^n(ow)?/i,millisecond:/^ms|milli(second)?s?/i,second:/^sec(ond)?s?/i,minute:/^min(ute)?s?/i,hour:/^h(ou)?rs?/i,week:/^w(ee)?k/i,month:/^m(o(nth)?s?)?/i,day:/^d(ays?)?/i,year:/^y((ea)?rs?)?/i,shortMeridian:/^(a|p)/i,longMeridian:/^(a\\.?m?\\.?|p\\.?m?\\.?)/i,timezone:/^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt)/i,ordinalSuffix:/^\\s*(st|nd|rd|th)/i,timeContext:/^\\s*(\\:|a|p)/i},abbreviatedTimeZoneStandard:{GMT:"-000",EST:"-0400",CST:"-0500",MST:"-0600",PST:"-0700"},abbreviatedTimeZoneDST:{GMT:"-000",EDT:"-0500",CDT:"-0600",MDT:"-0700",PDT:"-0800"}};
',
  'nn-NO' => 'Date.CultureInfo={name:"nn-NO",englishName:"Norwegian, Nynorsk (Norway)",nativeName:"norsk, nynorsk (Noreg)",dayNames:["søndag","måndag","tysdag","onsdag","torsdag","fredag","laurdag"],abbreviatedDayNames:["sø","må","ty","on","to","fr","la"],shortestDayNames:["sø","må","ty","on","to","fr","la"],firstLetterDayNames:["s","m","t","o","t","f","l"],monthNames:["januar","februar","mars","april","mai","juni","juli","august","september","oktober","november","desember"],abbreviatedMonthNames:["jan","feb","mar","apr","mai","jun","jul","aug","sep","okt","nov","des"],amDesignator:"",pmDesignator:"",firstDayOfWeek:1,twoDigitYearMax:2029,dateElementOrder:"dmy",formatPatterns:{shortDate:"dd.MM.yyyy",longDate:"d. MMMM yyyy",shortTime:"HH:mm",longTime:"HH:mm:ss",fullDateTime:"d. MMMM yyyy HH:mm:ss",sortableDateTime:"yyyy-MM-ddTHH:mm:ss",universalSortableDateTime:"yyyy-MM-dd HH:mm:ssZ",rfc1123:"ddd, dd MMM yyyy HH:mm:ss GMT",monthDay:"d. MMMM",yearMonth:"MMMM yyyy"},regexPatterns:{jan:/^jan(uar)?/i,feb:/^feb(ruar)?/i,mar:/^mar(s)?/i,apr:/^apr(il)?/i,may:/^mai/i,jun:/^jun(i)?/i,jul:/^jul(i)?/i,aug:/^aug(ust)?/i,sep:/^sep(t(ember)?)?/i,oct:/^okt(ober)?/i,nov:/^nov(ember)?/i,dec:/^des(ember)?/i,sun:/^søndag/i,mon:/^måndag/i,tue:/^tysdag/i,wed:/^onsdag/i,thu:/^torsdag/i,fri:/^fredag/i,sat:/^laurdag/i,future:/^next/i,past:/^last|past|prev(ious)?/i,add:/^(\\+|after|from)/i,subtract:/^(\\-|before|ago)/i,yesterday:/^yesterday/i,today:/^t(oday)?/i,tomorrow:/^tomorrow/i,now:/^n(ow)?/i,millisecond:/^ms|milli(second)?s?/i,second:/^sec(ond)?s?/i,minute:/^min(ute)?s?/i,hour:/^h(ou)?rs?/i,week:/^w(ee)?k/i,month:/^m(o(nth)?s?)?/i,day:/^d(ays?)?/i,year:/^y((ea)?rs?)?/i,shortMeridian:/^(a|p)/i,longMeridian:/^(a\\.?m?\\.?|p\\.?m?\\.?)/i,timezone:/^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt)/i,ordinalSuffix:/^\\s*(st|nd|rd|th)/i,timeContext:/^\\s*(\\:|a|p)/i},abbreviatedTimeZoneStandard:{GMT:"-000",EST:"-0400",CST:"-0500",MST:"-0600",PST:"-0700"},abbreviatedTimeZoneDST:{GMT:"-000",EDT:"-0500",CDT:"-0600",MDT:"-0700",PDT:"-0800"}};
',
  'ns-ZA' => 'Date.CultureInfo={name:"ns-ZA",englishName:"Northern Sotho (South Africa)",nativeName:"Sesotho sa Leboa (Afrika Borwa)",dayNames:["Lamorena","Mošupologo","Labobedi","Laboraro","Labone","Labohlano","Mokibelo"],abbreviatedDayNames:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],shortestDayNames:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],firstLetterDayNames:["S","M","T","W","T","F","S"],monthNames:["Pherekgong","Hlakola","Mopitlo","Moranang","Mosegamanye","Ngoatobošego","Phuphu","Phato","Lewedi","Diphalana","Dibatsela","Manthole"],abbreviatedMonthNames:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],amDesignator:"AM",pmDesignator:"PM",firstDayOfWeek:0,twoDigitYearMax:2029,dateElementOrder:"ymd",formatPatterns:{shortDate:"yyyy/MM/dd",longDate:"dd MMMM yyyy",shortTime:"hh:mm:ss tt",longTime:"hh:mm:ss tt",fullDateTime:"dd MMMM yyyy hh:mm:ss tt",sortableDateTime:"yyyy-MM-ddTHH:mm:ss",universalSortableDateTime:"yyyy-MM-dd HH:mm:ssZ",rfc1123:"ddd, dd MMM yyyy HH:mm:ss GMT",monthDay:"MMMM dd",yearMonth:"MMMM yyyy"},regexPatterns:{jan:/^pherekgong/i,feb:/^hlakola/i,mar:/^mopitlo/i,apr:/^moranang/i,may:/^mosegamanye/i,jun:/^ngoatobošego/i,jul:/^phuphu/i,aug:/^phato/i,sep:/^lewedi/i,oct:/^diphalana/i,nov:/^dibatsela/i,dec:/^manthole/i,sun:/^lamorena/i,mon:/^mošupologo/i,tue:/^labobedi/i,wed:/^laboraro/i,thu:/^labone/i,fri:/^labohlano/i,sat:/^mokibelo/i,future:/^next/i,past:/^last|past|prev(ious)?/i,add:/^(\\+|after|from)/i,subtract:/^(\\-|before|ago)/i,yesterday:/^yesterday/i,today:/^t(oday)?/i,tomorrow:/^tomorrow/i,now:/^n(ow)?/i,millisecond:/^ms|milli(second)?s?/i,second:/^sec(ond)?s?/i,minute:/^min(ute)?s?/i,hour:/^h(ou)?rs?/i,week:/^w(ee)?k/i,month:/^m(o(nth)?s?)?/i,day:/^d(ays?)?/i,year:/^y((ea)?rs?)?/i,shortMeridian:/^(a|p)/i,longMeridian:/^(a\\.?m?\\.?|p\\.?m?\\.?)/i,timezone:/^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt)/i,ordinalSuffix:/^\\s*(st|nd|rd|th)/i,timeContext:/^\\s*(\\:|a|p)/i},abbreviatedTimeZoneStandard:{GMT:"-000",EST:"-0400",CST:"-0500",MST:"-0600",PST:"-0700"},abbreviatedTimeZoneDST:{GMT:"-000",EDT:"-0500",CDT:"-0600",MDT:"-0700",PDT:"-0800"}};
',
  'pa-IN' => 'Date.CultureInfo={name:"pa-IN",englishName:"Punjabi (India)",nativeName:"ਪੰਜਾਬੀ (ਭਾਰਤ)",dayNames:["ਐਤਵਾਰ","ਸੋਮਵਾਰ","ਮੰਗਲਵਾਰ","ਬੁਧਵਾਰ","ਵੀਰਵਾਰ","ਸ਼ੁੱਕਰਵਾਰ","ਸ਼ਨੀਚਰਵਾਰ"],abbreviatedDayNames:["ਐਤ.","ਸੋਮ.","ਮੰਗਲ.","ਬੁਧ.","ਵੀਰ.","ਸ਼ੁਕਰ.","ਸ਼ਨੀ."],shortestDayNames:["ਐ","ਸ","ਮ","ਬ","ਵ","ਸ਼","ਸ਼"],firstLetterDayNames:["ਐ","ਸ","ਮ","ਬ","ਵ","ਸ਼","ਸ਼"],monthNames:["ਜਨਵਰੀ","ਫ਼ਰਵਰੀ","ਮਾਰਚ","ਅਪ੍ਰੈਲ","ਮਈ","ਜੂਨ","ਜੁਲਾਈ","ਅਗਸਤ","ਸਤੰਬਰ","ਅਕਤੂਬਰ","ਨਵੰਬਰ","ਦਸੰਬਰ"],abbreviatedMonthNames:["ਜਨਵਰੀ","ਫ਼ਰਵਰੀ","ਮਾਰਚ","ਅਪ੍ਰੈਲ","ਮਈ","ਜੂਨ","ਜੁਲਾਈ","ਅਗਸਤ","ਸਤੰਬਰ","ਅਕਤੂਬਰ","ਨਵੰਬਰ","ਦਸੰਬਰ"],amDesignator:"ਸਵੇਰੇ",pmDesignator:"ਸ਼ਾਮ",firstDayOfWeek:1,twoDigitYearMax:2029,dateElementOrder:"dmy",formatPatterns:{shortDate:"dd-MM-yy",longDate:"dd MMMM yyyy dddd",shortTime:"tt hh:mm",longTime:"tt hh:mm:ss",fullDateTime:"dd MMMM yyyy dddd tt hh:mm:ss",sortableDateTime:"yyyy-MM-ddTHH:mm:ss",universalSortableDateTime:"yyyy-MM-dd HH:mm:ssZ",rfc1123:"ddd, dd MMM yyyy HH:mm:ss GMT",monthDay:"dd MMMM",yearMonth:"MMMM, yyyy"},regexPatterns:{jan:/^ਜਨਵਰੀ/i,feb:/^ਫ਼ਰਵਰੀ/i,mar:/^ਮਾਰਚ/i,apr:/^ਅਪ੍ਰੈਲ/i,may:/^ਮਈ/i,jun:/^ਜੂਨ/i,jul:/^ਜੁਲਾਈ/i,aug:/^ਅਗਸਤ/i,sep:/^ਸਤੰਬਰ/i,oct:/^ਅਕਤੂਬਰ/i,nov:/^ਨਵੰਬਰ/i,dec:/^ਦਸੰਬਰ/i,sun:/^ਐ(ਤ(.(ਵਾਰ)?)?)?/i,mon:/^ਸ(ੋਮ(.(ਵਾਰ)?)?)?/i,tue:/^ਮ(ੰਗਲ(.(ਵਾਰ)?)?)?/i,wed:/^ਬ(ੁਧ(.(ਵਾਰ)?)?)?/i,thu:/^ਵ(ੀਰ(.(ਵਾਰ)?)?)?/i,fri:/^ਸ਼(ੁਕਰ(.(ਰਵਾਰ)?)?)?/i,sat:/^ਸ਼(ਨੀ(.(ਚਰਵਾਰ)?)?)?/i,future:/^next/i,past:/^last|past|prev(ious)?/i,add:/^(\\+|after|from)/i,subtract:/^(\\-|before|ago)/i,yesterday:/^yesterday/i,today:/^t(oday)?/i,tomorrow:/^tomorrow/i,now:/^n(ow)?/i,millisecond:/^ms|milli(second)?s?/i,second:/^sec(ond)?s?/i,minute:/^min(ute)?s?/i,hour:/^h(ou)?rs?/i,week:/^w(ee)?k/i,month:/^m(o(nth)?s?)?/i,day:/^d(ays?)?/i,year:/^y((ea)?rs?)?/i,shortMeridian:/^(a|p)/i,longMeridian:/^(a\\.?m?\\.?|p\\.?m?\\.?)/i,timezone:/^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt)/i,ordinalSuffix:/^\\s*(st|nd|rd|th)/i,timeContext:/^\\s*(\\:|a|p)/i},abbreviatedTimeZoneStandard:{GMT:"-000",EST:"-0400",CST:"-0500",MST:"-0600",PST:"-0700"},abbreviatedTimeZoneDST:{GMT:"-000",EDT:"-0500",CDT:"-0600",MDT:"-0700",PDT:"-0800"}};
',
  'pl-PL' => 'Date.CultureInfo={name:"pl-PL",englishName:"Polish (Poland)",nativeName:"polski (Polska)",dayNames:["niedziela","poniedziałek","wtorek","środa","czwartek","piątek","sobota"],abbreviatedDayNames:["N","Pn","Wt","Śr","Cz","Pt","So"],shortestDayNames:["N","Pn","Wt","Śr","Cz","Pt","So"],firstLetterDayNames:["N","P","W","Ś","C","P","S"],monthNames:["styczeń","luty","marzec","kwiecień","maj","czerwiec","lipiec","sierpień","wrzesień","październik","listopad","grudzień"],abbreviatedMonthNames:["sty","lut","mar","kwi","maj","cze","lip","sie","wrz","paź","lis","gru"],amDesignator:"",pmDesignator:"",firstDayOfWeek:1,twoDigitYearMax:2029,dateElementOrder:"ymd",formatPatterns:{shortDate:"yyyy-MM-dd",longDate:"d MMMM yyyy",shortTime:"HH:mm",longTime:"HH:mm:ss",fullDateTime:"d MMMM yyyy HH:mm:ss",sortableDateTime:"yyyy-MM-ddTHH:mm:ss",universalSortableDateTime:"yyyy-MM-dd HH:mm:ssZ",rfc1123:"ddd, dd MMM yyyy HH:mm:ss GMT",monthDay:"d MMMM",yearMonth:"MMMM yyyy"},regexPatterns:{jan:/^sty(czeń)?/i,feb:/^lut(y)?/i,mar:/^mar(zec)?/i,apr:/^kwi(ecień)?/i,may:/^maj/i,jun:/^cze(rwiec)?/i,jul:/^lip(iec)?/i,aug:/^sie(rpień)?/i,sep:/^wrz(esień)?/i,oct:/^paź(dziernik)?/i,nov:/^lis(topad)?/i,dec:/^gru(dzień)?/i,sun:/^niedziela/i,mon:/^poniedziałek/i,tue:/^wtorek/i,wed:/^środa/i,thu:/^czwartek/i,fri:/^piątek/i,sat:/^sobota/i,future:/^next/i,past:/^last|past|prev(ious)?/i,add:/^(\\+|after|from)/i,subtract:/^(\\-|before|ago)/i,yesterday:/^yesterday/i,today:/^t(oday)?/i,tomorrow:/^tomorrow/i,now:/^n(ow)?/i,millisecond:/^ms|milli(second)?s?/i,second:/^sec(ond)?s?/i,minute:/^min(ute)?s?/i,hour:/^h(ou)?rs?/i,week:/^w(ee)?k/i,month:/^m(o(nth)?s?)?/i,day:/^d(ays?)?/i,year:/^y((ea)?rs?)?/i,shortMeridian:/^(a|p)/i,longMeridian:/^(a\\.?m?\\.?|p\\.?m?\\.?)/i,timezone:/^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt)/i,ordinalSuffix:/^\\s*(st|nd|rd|th)/i,timeContext:/^\\s*(\\:|a|p)/i},abbreviatedTimeZoneStandard:{GMT:"-000",EST:"-0400",CST:"-0500",MST:"-0600",PST:"-0700"},abbreviatedTimeZoneDST:{GMT:"-000",EDT:"-0500",CDT:"-0600",MDT:"-0700",PDT:"-0800"}};
',
  'pt-BR' => 'Date.CultureInfo={name:"pt-BR",englishName:"Portuguese (Brazil)",nativeName:"Português (Brasil)",dayNames:["domingo","segunda-feira","terça-feira","quarta-feira","quinta-feira","sexta-feira","sábado"],abbreviatedDayNames:["dom","seg","ter","qua","qui","sex","sáb"],shortestDayNames:["dom","seg","ter","qua","qui","sex","sáb"],firstLetterDayNames:["d","s","t","q","q","s","s"],monthNames:["janeiro","fevereiro","março","abril","maio","junho","julho","agosto","setembro","outubro","novembro","dezembro"],abbreviatedMonthNames:["jan","fev","mar","abr","mai","jun","jul","ago","set","out","nov","dez"],amDesignator:"",pmDesignator:"",firstDayOfWeek:0,twoDigitYearMax:2029,dateElementOrder:"dmy",formatPatterns:{shortDate:"d/M/yyyy",longDate:"dddd, d\' de \'MMMM\' de \'yyyy",shortTime:"H:mm",longTime:"H:mm:ss",fullDateTime:"dddd, d\' de \'MMMM\' de \'yyyy H:mm:ss",sortableDateTime:"yyyy-MM-ddTHH:mm:ss",universalSortableDateTime:"yyyy-MM-dd HH:mm:ssZ",rfc1123:"ddd, dd MMM yyyy HH:mm:ss GMT",monthDay:"dd\' de \'MMMM",yearMonth:"MMMM\' de \'yyyy"},regexPatterns:{jan:/^jan(eiro)?/i,feb:/^fev(ereiro)?/i,mar:/^mar(ço)?/i,apr:/^abr(il)?/i,may:/^mai(o)?/i,jun:/^jun(ho)?/i,jul:/^jul(ho)?/i,aug:/^ago(sto)?/i,sep:/^set(embro)?/i,oct:/^out(ubro)?/i,nov:/^nov(embro)?/i,dec:/^dez(embro)?/i,sun:/^domingo/i,mon:/^segunda-feira/i,tue:/^terça-feira/i,wed:/^quarta-feira/i,thu:/^quinta-feira/i,fri:/^sexta-feira/i,sat:/^sábado/i,future:/^next/i,past:/^last|past|prev(ious)?/i,add:/^(\\+|after|from)/i,subtract:/^(\\-|before|ago)/i,yesterday:/^yesterday/i,today:/^t(oday)?/i,tomorrow:/^tomorrow/i,now:/^n(ow)?/i,millisecond:/^ms|milli(second)?s?/i,second:/^sec(ond)?s?/i,minute:/^min(ute)?s?/i,hour:/^h(ou)?rs?/i,week:/^w(ee)?k/i,month:/^m(o(nth)?s?)?/i,day:/^d(ays?)?/i,year:/^y((ea)?rs?)?/i,shortMeridian:/^(a|p)/i,longMeridian:/^(a\\.?m?\\.?|p\\.?m?\\.?)/i,timezone:/^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt)/i,ordinalSuffix:/^\\s*(st|nd|rd|th)/i,timeContext:/^\\s*(\\:|a|p)/i},abbreviatedTimeZoneStandard:{GMT:"-000",EST:"-0400",CST:"-0500",MST:"-0600",PST:"-0700"},abbreviatedTimeZoneDST:{GMT:"-000",EDT:"-0500",CDT:"-0600",MDT:"-0700",PDT:"-0800"}};
',
  'pt-PT' => 'Date.CultureInfo={name:"pt-PT",englishName:"Portuguese (Portugal)",nativeName:"português (Portugal)",dayNames:["domingo","segunda-feira","terça-feira","quarta-feira","quinta-feira","sexta-feira","sábado"],abbreviatedDayNames:["dom","seg","ter","qua","qui","sex","sáb"],shortestDayNames:["dom","seg","ter","qua","qui","sex","sáb"],firstLetterDayNames:["d","s","t","q","q","s","s"],monthNames:["Janeiro","Fevereiro","Março","Abril","Maio","Junho","Julho","Agosto","Setembro","Outubro","Novembro","Dezembro"],abbreviatedMonthNames:["Jan","Fev","Mar","Abr","Mai","Jun","Jul","Ago","Set","Out","Nov","Dez"],amDesignator:"",pmDesignator:"",firstDayOfWeek:1,twoDigitYearMax:2029,dateElementOrder:"dmy",formatPatterns:{shortDate:"dd-MM-yyyy",longDate:"dddd, d\' de \'MMMM\' de \'yyyy",shortTime:"H:mm",longTime:"H:mm:ss",fullDateTime:"dddd, d\' de \'MMMM\' de \'yyyy H:mm:ss",sortableDateTime:"yyyy-MM-ddTHH:mm:ss",universalSortableDateTime:"yyyy-MM-dd HH:mm:ssZ",rfc1123:"ddd, dd MMM yyyy HH:mm:ss GMT",monthDay:"d/M",yearMonth:"MMMM\' de \'yyyy"},regexPatterns:{jan:/^jan(eiro)?/i,feb:/^fev(ereiro)?/i,mar:/^mar(ço)?/i,apr:/^abr(il)?/i,may:/^mai(o)?/i,jun:/^jun(ho)?/i,jul:/^jul(ho)?/i,aug:/^ago(sto)?/i,sep:/^set(embro)?/i,oct:/^out(ubro)?/i,nov:/^nov(embro)?/i,dec:/^dez(embro)?/i,sun:/^domingo/i,mon:/^segunda-feira/i,tue:/^terça-feira/i,wed:/^quarta-feira/i,thu:/^quinta-feira/i,fri:/^sexta-feira/i,sat:/^sábado/i,future:/^next/i,past:/^last|past|prev(ious)?/i,add:/^(\\+|after|from)/i,subtract:/^(\\-|before|ago)/i,yesterday:/^yesterday/i,today:/^t(oday)?/i,tomorrow:/^tomorrow/i,now:/^n(ow)?/i,millisecond:/^ms|milli(second)?s?/i,second:/^sec(ond)?s?/i,minute:/^min(ute)?s?/i,hour:/^h(ou)?rs?/i,week:/^w(ee)?k/i,month:/^m(o(nth)?s?)?/i,day:/^d(ays?)?/i,year:/^y((ea)?rs?)?/i,shortMeridian:/^(a|p)/i,longMeridian:/^(a\\.?m?\\.?|p\\.?m?\\.?)/i,timezone:/^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt)/i,ordinalSuffix:/^\\s*(st|nd|rd|th)/i,timeContext:/^\\s*(\\:|a|p)/i},abbreviatedTimeZoneStandard:{GMT:"-000",EST:"-0400",CST:"-0500",MST:"-0600",PST:"-0700"},abbreviatedTimeZoneDST:{GMT:"-000",EDT:"-0500",CDT:"-0600",MDT:"-0700",PDT:"-0800"}};
',
  'quz-BO' => 'Date.CultureInfo={name:"quz-BO",englishName:"Quechua (Bolivia)",nativeName:"runasimi (Bolivia Suyu)",dayNames:["intichaw","killachaw","atipachaw","quyllurchaw","Ch\' askachaw","Illapachaw","k\'uychichaw"],abbreviatedDayNames:["int","kil","ati","quy","Ch","Ill","k\'u"],shortestDayNames:["int","kil","ati","quy","Ch","Ill","k\'u"],firstLetterDayNames:["i","k","a","q","C","I","k"],monthNames:["Qulla puquy","Hatun puquy","Pauqar waray","ayriwa","Aymuray","Inti raymi","Anta Sitwa","Qhapaq Sitwa","Uma raymi","Kantaray","Ayamarq\'a","Kapaq Raymi"],abbreviatedMonthNames:["Qul","Hat","Pau","ayr","Aym","Int","Ant","Qha","Uma","Kan","Aya","Kap"],amDesignator:"a.m.",pmDesignator:"p.m.",firstDayOfWeek:0,twoDigitYearMax:2029,dateElementOrder:"dmy",formatPatterns:{shortDate:"dd/MM/yyyy",longDate:"dddd, dd\' de \'MMMM\' de \'yyyy",shortTime:"hh:mm:ss tt",longTime:"hh:mm:ss tt",fullDateTime:"dddd, dd\' de \'MMMM\' de \'yyyy hh:mm:ss tt",sortableDateTime:"yyyy-MM-ddTHH:mm:ss",universalSortableDateTime:"yyyy-MM-dd HH:mm:ssZ",rfc1123:"ddd, dd MMM yyyy HH:mm:ss GMT",monthDay:"MMMM dd",yearMonth:"MMMM\' de \'yyyy"},regexPatterns:{jan:/^qul(la puquy)?/i,feb:/^hat(un puquy)?/i,mar:/^pau(qar waray)?/i,apr:/^ayr(iwa)?/i,may:/^aym(uray)?/i,jun:/^int(i raymi)?/i,jul:/^ant(a sitwa)?/i,aug:/^qha(paq sitwa)?/i,sep:/^uma( raymi)?/i,oct:/^kan(taray)?/i,nov:/^aya(marq\'a)?/i,dec:/^kap(aq raymi)?/i,sun:/^intichaw/i,mon:/^killachaw/i,tue:/^atipachaw/i,wed:/^quyllurchaw/i,thu:/^ch\' askachaw/i,fri:/^illapachaw/i,sat:/^k\'uychichaw/i,future:/^next/i,past:/^last|past|prev(ious)?/i,add:/^(\\+|after|from)/i,subtract:/^(\\-|before|ago)/i,yesterday:/^yesterday/i,today:/^t(oday)?/i,tomorrow:/^tomorrow/i,now:/^n(ow)?/i,millisecond:/^ms|milli(second)?s?/i,second:/^sec(ond)?s?/i,minute:/^min(ute)?s?/i,hour:/^h(ou)?rs?/i,week:/^w(ee)?k/i,month:/^m(o(nth)?s?)?/i,day:/^d(ays?)?/i,year:/^y((ea)?rs?)?/i,shortMeridian:/^(a|p)/i,longMeridian:/^(a\\.?m?\\.?|p\\.?m?\\.?)/i,timezone:/^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt)/i,ordinalSuffix:/^\\s*(st|nd|rd|th)/i,timeContext:/^\\s*(\\:|a|p)/i},abbreviatedTimeZoneStandard:{GMT:"-000",EST:"-0400",CST:"-0500",MST:"-0600",PST:"-0700"},abbreviatedTimeZoneDST:{GMT:"-000",EDT:"-0500",CDT:"-0600",MDT:"-0700",PDT:"-0800"}};
',
  'quz-EC' => 'Date.CultureInfo={name:"quz-EC",englishName:"Quechua (Ecuador)",nativeName:"runasimi (Ecuador Suyu)",dayNames:["intichaw","killachaw","atipachaw","quyllurchaw","Ch\' askachaw","Illapachaw","k\'uychichaw"],abbreviatedDayNames:["int","kil","ati","quy","Ch","Ill","k\'u"],shortestDayNames:["int","kil","ati","quy","Ch","Ill","k\'u"],firstLetterDayNames:["i","k","a","q","C","I","k"],monthNames:["Qulla puquy","Hatun puquy","Pauqar waray","ayriwa","Aymuray","Inti raymi","Anta Sitwa","Qhapaq Sitwa","Uma raymi","Kantaray","Ayamarq\'a","Kapaq Raymi"],abbreviatedMonthNames:["Qul","Hat","Pau","ayr","Aym","Int","Ant","Qha","Uma","Kan","Aya","Kap"],amDesignator:"",pmDesignator:"",firstDayOfWeek:0,twoDigitYearMax:2029,dateElementOrder:"dmy",formatPatterns:{shortDate:"dd/MM/yyyy",longDate:"dddd, dd\' de \'MMMM\' de \'yyyy",shortTime:"H:mm:ss",longTime:"H:mm:ss",fullDateTime:"dddd, dd\' de \'MMMM\' de \'yyyy H:mm:ss",sortableDateTime:"yyyy-MM-ddTHH:mm:ss",universalSortableDateTime:"yyyy-MM-dd HH:mm:ssZ",rfc1123:"ddd, dd MMM yyyy HH:mm:ss GMT",monthDay:"MMMM dd",yearMonth:"MMMM\' de \'yyyy"},regexPatterns:{jan:/^qul(la puquy)?/i,feb:/^hat(un puquy)?/i,mar:/^pau(qar waray)?/i,apr:/^ayr(iwa)?/i,may:/^aym(uray)?/i,jun:/^int(i raymi)?/i,jul:/^ant(a sitwa)?/i,aug:/^qha(paq sitwa)?/i,sep:/^uma( raymi)?/i,oct:/^kan(taray)?/i,nov:/^aya(marq\'a)?/i,dec:/^kap(aq raymi)?/i,sun:/^intichaw/i,mon:/^killachaw/i,tue:/^atipachaw/i,wed:/^quyllurchaw/i,thu:/^ch\' askachaw/i,fri:/^illapachaw/i,sat:/^k\'uychichaw/i,future:/^next/i,past:/^last|past|prev(ious)?/i,add:/^(\\+|after|from)/i,subtract:/^(\\-|before|ago)/i,yesterday:/^yesterday/i,today:/^t(oday)?/i,tomorrow:/^tomorrow/i,now:/^n(ow)?/i,millisecond:/^ms|milli(second)?s?/i,second:/^sec(ond)?s?/i,minute:/^min(ute)?s?/i,hour:/^h(ou)?rs?/i,week:/^w(ee)?k/i,month:/^m(o(nth)?s?)?/i,day:/^d(ays?)?/i,year:/^y((ea)?rs?)?/i,shortMeridian:/^(a|p)/i,longMeridian:/^(a\\.?m?\\.?|p\\.?m?\\.?)/i,timezone:/^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt)/i,ordinalSuffix:/^\\s*(st|nd|rd|th)/i,timeContext:/^\\s*(\\:|a|p)/i},abbreviatedTimeZoneStandard:{GMT:"-000",EST:"-0400",CST:"-0500",MST:"-0600",PST:"-0700"},abbreviatedTimeZoneDST:{GMT:"-000",EDT:"-0500",CDT:"-0600",MDT:"-0700",PDT:"-0800"}};
',
  'quz-PE' => 'Date.CultureInfo={name:"quz-PE",englishName:"Quechua (Peru)",nativeName:"runasimi (Peru Suyu)",dayNames:["intichaw","killachaw","atipachaw","quyllurchaw","Ch\' askachaw","Illapachaw","k\'uychichaw"],abbreviatedDayNames:["int","kil","ati","quy","Ch","Ill","k\'u"],shortestDayNames:["int","kil","ati","quy","Ch","Ill","k\'u"],firstLetterDayNames:["i","k","a","q","C","I","k"],monthNames:["Qulla puquy","Hatun puquy","Pauqar waray","ayriwa","Aymuray","Inti raymi","Anta Sitwa","Qhapaq Sitwa","Uma raymi","Kantaray","Ayamarq\'a","Kapaq Raymi"],abbreviatedMonthNames:["Qul","Hat","Pau","ayr","Aym","Int","Ant","Qha","Uma","Kan","Aya","Kap"],amDesignator:"a.m.",pmDesignator:"p.m.",firstDayOfWeek:0,twoDigitYearMax:2029,dateElementOrder:"dmy",formatPatterns:{shortDate:"dd/MM/yyyy",longDate:"dddd, dd\' de \'MMMM\' de \'yyyy",shortTime:"hh:mm:ss tt",longTime:"hh:mm:ss tt",fullDateTime:"dddd, dd\' de \'MMMM\' de \'yyyy hh:mm:ss tt",sortableDateTime:"yyyy-MM-ddTHH:mm:ss",universalSortableDateTime:"yyyy-MM-dd HH:mm:ssZ",rfc1123:"ddd, dd MMM yyyy HH:mm:ss GMT",monthDay:"MMMM dd",yearMonth:"MMMM\' de \'yyyy"},regexPatterns:{jan:/^qul(la puquy)?/i,feb:/^hat(un puquy)?/i,mar:/^pau(qar waray)?/i,apr:/^ayr(iwa)?/i,may:/^aym(uray)?/i,jun:/^int(i raymi)?/i,jul:/^ant(a sitwa)?/i,aug:/^qha(paq sitwa)?/i,sep:/^uma( raymi)?/i,oct:/^kan(taray)?/i,nov:/^aya(marq\'a)?/i,dec:/^kap(aq raymi)?/i,sun:/^intichaw/i,mon:/^killachaw/i,tue:/^atipachaw/i,wed:/^quyllurchaw/i,thu:/^ch\' askachaw/i,fri:/^illapachaw/i,sat:/^k\'uychichaw/i,future:/^next/i,past:/^last|past|prev(ious)?/i,add:/^(\\+|after|from)/i,subtract:/^(\\-|before|ago)/i,yesterday:/^yesterday/i,today:/^t(oday)?/i,tomorrow:/^tomorrow/i,now:/^n(ow)?/i,millisecond:/^ms|milli(second)?s?/i,second:/^sec(ond)?s?/i,minute:/^min(ute)?s?/i,hour:/^h(ou)?rs?/i,week:/^w(ee)?k/i,month:/^m(o(nth)?s?)?/i,day:/^d(ays?)?/i,year:/^y((ea)?rs?)?/i,shortMeridian:/^(a|p)/i,longMeridian:/^(a\\.?m?\\.?|p\\.?m?\\.?)/i,timezone:/^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt)/i,ordinalSuffix:/^\\s*(st|nd|rd|th)/i,timeContext:/^\\s*(\\:|a|p)/i},abbreviatedTimeZoneStandard:{GMT:"-000",EST:"-0400",CST:"-0500",MST:"-0600",PST:"-0700"},abbreviatedTimeZoneDST:{GMT:"-000",EDT:"-0500",CDT:"-0600",MDT:"-0700",PDT:"-0800"}};
',
  'ro-RO' => 'Date.CultureInfo={name:"ro-RO",englishName:"Romanian (Romania)",nativeName:"română (România)",dayNames:["duminică","luni","marţi","miercuri","joi","vineri","sâmbătă"],abbreviatedDayNames:["D","L","Ma","Mi","J","V","S"],shortestDayNames:["D","L","Ma","Mi","J","V","S"],firstLetterDayNames:["D","L","M","M","J","V","S"],monthNames:["ianuarie","februarie","martie","aprilie","mai","iunie","iulie","august","septembrie","octombrie","noiembrie","decembrie"],abbreviatedMonthNames:["ian.","feb.","mar.","apr.","mai.","iun.","iul.","aug.","sep.","oct.","nov.","dec."],amDesignator:"",pmDesignator:"",firstDayOfWeek:1,twoDigitYearMax:2029,dateElementOrder:"dmy",formatPatterns:{shortDate:"dd.MM.yyyy",longDate:"d MMMM yyyy",shortTime:"HH:mm",longTime:"HH:mm:ss",fullDateTime:"d MMMM yyyy HH:mm:ss",sortableDateTime:"yyyy-MM-ddTHH:mm:ss",universalSortableDateTime:"yyyy-MM-dd HH:mm:ssZ",rfc1123:"ddd, dd MMM yyyy HH:mm:ss GMT",monthDay:"d MMMM",yearMonth:"MMMM yyyy"},regexPatterns:{jan:/^ian(.(uarie)?)?/i,feb:/^feb(.(ruarie)?)?/i,mar:/^mar(.(tie)?)?/i,apr:/^apr(.(ilie)?)?/i,may:/^mai(.()?)?/i,jun:/^iun(.(ie)?)?/i,jul:/^iul(.(ie)?)?/i,aug:/^aug(.(ust)?)?/i,sep:/^sep(.(tembrie)?)?/i,oct:/^oct(.(ombrie)?)?/i,nov:/^noiembrie/i,dec:/^dec(.(embrie)?)?/i,sun:/^duminică/i,mon:/^luni/i,tue:/^marţi/i,wed:/^miercuri/i,thu:/^joi/i,fri:/^vineri/i,sat:/^sâmbătă/i,future:/^next/i,past:/^last|past|prev(ious)?/i,add:/^(\\+|after|from)/i,subtract:/^(\\-|before|ago)/i,yesterday:/^yesterday/i,today:/^t(oday)?/i,tomorrow:/^tomorrow/i,now:/^n(ow)?/i,millisecond:/^ms|milli(second)?s?/i,second:/^sec(ond)?s?/i,minute:/^min(ute)?s?/i,hour:/^h(ou)?rs?/i,week:/^w(ee)?k/i,month:/^m(o(nth)?s?)?/i,day:/^d(ays?)?/i,year:/^y((ea)?rs?)?/i,shortMeridian:/^(a|p)/i,longMeridian:/^(a\\.?m?\\.?|p\\.?m?\\.?)/i,timezone:/^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt)/i,ordinalSuffix:/^\\s*(st|nd|rd|th)/i,timeContext:/^\\s*(\\:|a|p)/i},abbreviatedTimeZoneStandard:{GMT:"-000",EST:"-0400",CST:"-0500",MST:"-0600",PST:"-0700"},abbreviatedTimeZoneDST:{GMT:"-000",EDT:"-0500",CDT:"-0600",MDT:"-0700",PDT:"-0800"}};
',
  'ru-RU' => 'Date.CultureInfo={name:"ru-RU",englishName:"Russian (Russia)",nativeName:"русский (Россия)",dayNames:["воскресенье","понедельник","вторник","среда","четверг","пятница","суббота"],abbreviatedDayNames:["Вс","Пн","Вт","Ср","Чт","Пт","Сб"],shortestDayNames:["Вс","Пн","Вт","Ср","Чт","Пт","Сб"],firstLetterDayNames:["В","П","В","С","Ч","П","С"],monthNames:["Январь","Февраль","Март","Апрель","Май","Июнь","Июль","Август","Сентябрь","Октябрь","Ноябрь","Декабрь"],abbreviatedMonthNames:["янв","фев","мар","апр","май","июн","июл","авг","сен","окт","ноя","дек"],amDesignator:"",pmDesignator:"",firstDayOfWeek:1,twoDigitYearMax:2029,dateElementOrder:"dmy",formatPatterns:{shortDate:"dd.MM.yyyy",longDate:"d MMMM yyyy \'г.\'",shortTime:"H:mm",longTime:"H:mm:ss",fullDateTime:"d MMMM yyyy \'г.\' H:mm:ss",sortableDateTime:"yyyy-MM-ddTHH:mm:ss",universalSortableDateTime:"yyyy-MM-dd HH:mm:ssZ",rfc1123:"ddd, dd MMM yyyy HH:mm:ss GMT",monthDay:"MMMM dd",yearMonth:"MMMM yyyy \'г.\'"},regexPatterns:{jan:/^янв(арь)?/i,feb:/^фев(раль)?/i,mar:/^мар(т)?/i,apr:/^апр(ель)?/i,may:/^май/i,jun:/^июн(ь)?/i,jul:/^июл(ь)?/i,aug:/^авг(уст)?/i,sep:/^сен(тябрь)?/i,oct:/^окт(ябрь)?/i,nov:/^ноя(брь)?/i,dec:/^дек(абрь)?/i,sun:/^воскресенье/i,mon:/^понедельник/i,tue:/^вторник/i,wed:/^среда/i,thu:/^четверг/i,fri:/^пятница/i,sat:/^суббота/i,future:/^next/i,past:/^last|past|prev(ious)?/i,add:/^(\\+|after|from)/i,subtract:/^(\\-|before|ago)/i,yesterday:/^yesterday/i,today:/^t(oday)?/i,tomorrow:/^tomorrow/i,now:/^n(ow)?/i,millisecond:/^ms|milli(second)?s?/i,second:/^sec(ond)?s?/i,minute:/^min(ute)?s?/i,hour:/^h(ou)?rs?/i,week:/^w(ee)?k/i,month:/^m(o(nth)?s?)?/i,day:/^d(ays?)?/i,year:/^y((ea)?rs?)?/i,shortMeridian:/^(a|p)/i,longMeridian:/^(a\\.?m?\\.?|p\\.?m?\\.?)/i,timezone:/^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt)/i,ordinalSuffix:/^\\s*(st|nd|rd|th)/i,timeContext:/^\\s*(\\:|a|p)/i},abbreviatedTimeZoneStandard:{GMT:"-000",EST:"-0400",CST:"-0500",MST:"-0600",PST:"-0700"},abbreviatedTimeZoneDST:{GMT:"-000",EDT:"-0500",CDT:"-0600",MDT:"-0700",PDT:"-0800"}};
',
  'sa-IN' => 'Date.CultureInfo={name:"sa-IN",englishName:"Sanskrit (India)",nativeName:"संस्कृत (भारतम्)",dayNames:["रविवासरः","सोमवासरः","मङ्गलवासरः","बुधवासरः","गुरुवासरः","शुक्रवासरः","शनिवासरः"],abbreviatedDayNames:["रविवासरः","सोमवासरः","मङ्गलवासरः","बुधवासरः","गुरुवासरः","शुक्रवासरः","शनिवासरः"],shortestDayNames:["र","स","म","ब","ग","श","श"],firstLetterDayNames:["र","स","म","ब","ग","श","श"],monthNames:["जनवरी","फरवरी","मार्च","अप्रैल","मई","जून","जुलाई","अगस्त","सितम्बर","अक्तूबर","नवम्बर","दिसम्बर"],abbreviatedMonthNames:["जनवरी","फरवरी","मार्च","अप्रैल","मई","जून","जुलाई","अगस्त","सितम्बर","अक्तूबर","नवम्बर","दिसम्बर"],amDesignator:"पूर्वाह्न",pmDesignator:"अपराह्न",firstDayOfWeek:0,twoDigitYearMax:2029,dateElementOrder:"dmy",formatPatterns:{shortDate:"dd-MM-yyyy",longDate:"dd MMMM yyyy dddd",shortTime:"HH:mm",longTime:"HH:mm:ss",fullDateTime:"dd MMMM yyyy dddd HH:mm:ss",sortableDateTime:"yyyy-MM-ddTHH:mm:ss",universalSortableDateTime:"yyyy-MM-dd HH:mm:ssZ",rfc1123:"ddd, dd MMM yyyy HH:mm:ss GMT",monthDay:"dd MMMM",yearMonth:"MMMM, yyyy"},regexPatterns:{jan:/^जनवरी/i,feb:/^फरवरी/i,mar:/^मार्च/i,apr:/^अप्रैल/i,may:/^मई/i,jun:/^जून/i,jul:/^जुलाई/i,aug:/^अगस्त/i,sep:/^सितम्बर/i,oct:/^अक्तूबर/i,nov:/^नवम्बर/i,dec:/^दिसम्बर/i,sun:/^र(1)?/i,mon:/^स(1)?/i,tue:/^म(1)?/i,wed:/^ब(1)?/i,thu:/^ग(1)?/i,fri:/^श(1)?/i,sat:/^श(1)?/i,future:/^next/i,past:/^last|past|prev(ious)?/i,add:/^(\\+|after|from)/i,subtract:/^(\\-|before|ago)/i,yesterday:/^yesterday/i,today:/^t(oday)?/i,tomorrow:/^tomorrow/i,now:/^n(ow)?/i,millisecond:/^ms|milli(second)?s?/i,second:/^sec(ond)?s?/i,minute:/^min(ute)?s?/i,hour:/^h(ou)?rs?/i,week:/^w(ee)?k/i,month:/^m(o(nth)?s?)?/i,day:/^d(ays?)?/i,year:/^y((ea)?rs?)?/i,shortMeridian:/^(a|p)/i,longMeridian:/^(a\\.?m?\\.?|p\\.?m?\\.?)/i,timezone:/^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt)/i,ordinalSuffix:/^\\s*(st|nd|rd|th)/i,timeContext:/^\\s*(\\:|a|p)/i},abbreviatedTimeZoneStandard:{GMT:"-000",EST:"-0400",CST:"-0500",MST:"-0600",PST:"-0700"},abbreviatedTimeZoneDST:{GMT:"-000",EDT:"-0500",CDT:"-0600",MDT:"-0700",PDT:"-0800"}};
',
  'se-FI' => 'Date.CultureInfo={name:"se-FI",englishName:"Sami (Northern) (Finland)",nativeName:"davvisámegiella (Suopma)",dayNames:["sotnabeaivi","vuossárga","maŋŋebárga","gaskavahkku","duorastat","bearjadat","lávvardat"],abbreviatedDayNames:["sotn","vuos","maŋ","gask","duor","bear","láv"],shortestDayNames:["sotn","vuos","maŋ","gask","duor","bear","láv"],firstLetterDayNames:["s","v","m","g","d","b","l"],monthNames:["ođđajagemánnu","guovvamánnu","njukčamánnu","cuoŋománnu","miessemánnu","geassemánnu","suoidnemánnu","borgemánnu","čakčamánnu","golggotmánnu","skábmamánnu","juovlamánnu"],abbreviatedMonthNames:["ođđj","guov","njuk","cuo","mies","geas","suoi","borg","čakč","golg","skáb","juov"],amDesignator:"",pmDesignator:"",firstDayOfWeek:1,twoDigitYearMax:2029,dateElementOrder:"dmy",formatPatterns:{shortDate:"d.M.yyyy",longDate:"MMMM d\'. b. \'yyyy",shortTime:"H:mm:ss",longTime:"H:mm:ss",fullDateTime:"MMMM d\'. b. \'yyyy H:mm:ss",sortableDateTime:"yyyy-MM-ddTHH:mm:ss",universalSortableDateTime:"yyyy-MM-dd HH:mm:ssZ",rfc1123:"ddd, dd MMM yyyy HH:mm:ss GMT",monthDay:"MMMM dd",yearMonth:"MMMM yyyy"},regexPatterns:{jan:/^ođđajagemánnu/i,feb:/^guov(vamánnu)?/i,mar:/^njuk(čamánnu)?/i,apr:/^cuo(ŋománnu)?/i,may:/^mies(semánnu)?/i,jun:/^geas(semánnu)?/i,jul:/^suoi(dnemánnu)?/i,aug:/^borg(emánnu)?/i,sep:/^čakč(amánnu)?/i,oct:/^golg(gotmánnu)?/i,nov:/^skáb(mamánnu)?/i,dec:/^juov(lamánnu)?/i,sun:/^sotnabeaivi/i,mon:/^vuossárga/i,tue:/^maŋŋebárga/i,wed:/^gaskavahkku/i,thu:/^duorastat/i,fri:/^bearjadat/i,sat:/^lávvardat/i,future:/^next/i,past:/^last|past|prev(ious)?/i,add:/^(\\+|after|from)/i,subtract:/^(\\-|before|ago)/i,yesterday:/^yesterday/i,today:/^t(oday)?/i,tomorrow:/^tomorrow/i,now:/^n(ow)?/i,millisecond:/^ms|milli(second)?s?/i,second:/^sec(ond)?s?/i,minute:/^min(ute)?s?/i,hour:/^h(ou)?rs?/i,week:/^w(ee)?k/i,month:/^m(o(nth)?s?)?/i,day:/^d(ays?)?/i,year:/^y((ea)?rs?)?/i,shortMeridian:/^(a|p)/i,longMeridian:/^(a\\.?m?\\.?|p\\.?m?\\.?)/i,timezone:/^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt)/i,ordinalSuffix:/^\\s*(st|nd|rd|th)/i,timeContext:/^\\s*(\\:|a|p)/i},abbreviatedTimeZoneStandard:{GMT:"-000",EST:"-0400",CST:"-0500",MST:"-0600",PST:"-0700"},abbreviatedTimeZoneDST:{GMT:"-000",EDT:"-0500",CDT:"-0600",MDT:"-0700",PDT:"-0800"}};
',
  'se-NO' => 'Date.CultureInfo={name:"se-NO",englishName:"Sami (Northern) (Norway)",nativeName:"davvisámegiella (Norga)",dayNames:["sotnabeaivi","vuossárga","maŋŋebárga","gaskavahkku","duorastat","bearjadat","lávvardat"],abbreviatedDayNames:["sotn","vuos","maŋ","gask","duor","bear","láv"],shortestDayNames:["sotn","vuos","maŋ","gask","duor","bear","láv"],firstLetterDayNames:["s","v","m","g","d","b","l"],monthNames:["ođđajagemánnu","guovvamánnu","njukčamánnu","cuoŋománnu","miessemánnu","geassemánnu","suoidnemánnu","borgemánnu","čakčamánnu","golggotmánnu","skábmamánnu","juovlamánnu"],abbreviatedMonthNames:["ođđj","guov","njuk","cuo","mies","geas","suoi","borg","čakč","golg","skáb","juov"],amDesignator:"",pmDesignator:"",firstDayOfWeek:1,twoDigitYearMax:2029,dateElementOrder:"dmy",formatPatterns:{shortDate:"dd.MM.yyyy",longDate:"MMMM d\'. b. \'yyyy",shortTime:"HH:mm:ss",longTime:"HH:mm:ss",fullDateTime:"MMMM d\'. b. \'yyyy HH:mm:ss",sortableDateTime:"yyyy-MM-ddTHH:mm:ss",universalSortableDateTime:"yyyy-MM-dd HH:mm:ssZ",rfc1123:"ddd, dd MMM yyyy HH:mm:ss GMT",monthDay:"MMMM dd",yearMonth:"MMMM yyyy"},regexPatterns:{jan:/^ođđajagemánnu/i,feb:/^guov(vamánnu)?/i,mar:/^njuk(čamánnu)?/i,apr:/^cuo(ŋománnu)?/i,may:/^mies(semánnu)?/i,jun:/^geas(semánnu)?/i,jul:/^suoi(dnemánnu)?/i,aug:/^borg(emánnu)?/i,sep:/^čakč(amánnu)?/i,oct:/^golg(gotmánnu)?/i,nov:/^skáb(mamánnu)?/i,dec:/^juov(lamánnu)?/i,sun:/^sotnabeaivi/i,mon:/^vuossárga/i,tue:/^maŋŋebárga/i,wed:/^gaskavahkku/i,thu:/^duorastat/i,fri:/^bearjadat/i,sat:/^lávvardat/i,future:/^next/i,past:/^last|past|prev(ious)?/i,add:/^(\\+|after|from)/i,subtract:/^(\\-|before|ago)/i,yesterday:/^yesterday/i,today:/^t(oday)?/i,tomorrow:/^tomorrow/i,now:/^n(ow)?/i,millisecond:/^ms|milli(second)?s?/i,second:/^sec(ond)?s?/i,minute:/^min(ute)?s?/i,hour:/^h(ou)?rs?/i,week:/^w(ee)?k/i,month:/^m(o(nth)?s?)?/i,day:/^d(ays?)?/i,year:/^y((ea)?rs?)?/i,shortMeridian:/^(a|p)/i,longMeridian:/^(a\\.?m?\\.?|p\\.?m?\\.?)/i,timezone:/^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt)/i,ordinalSuffix:/^\\s*(st|nd|rd|th)/i,timeContext:/^\\s*(\\:|a|p)/i},abbreviatedTimeZoneStandard:{GMT:"-000",EST:"-0400",CST:"-0500",MST:"-0600",PST:"-0700"},abbreviatedTimeZoneDST:{GMT:"-000",EDT:"-0500",CDT:"-0600",MDT:"-0700",PDT:"-0800"}};
',
  'se-SE' => 'Date.CultureInfo={name:"se-SE",englishName:"Sami (Northern) (Sweden)",nativeName:"davvisámegiella (Ruoŧŧa)",dayNames:["sotnabeaivi","mánnodat","disdat","gaskavahkku","duorastat","bearjadat","lávvardat"],abbreviatedDayNames:["sotn","mán","dis","gask","duor","bear","láv"],shortestDayNames:["sotn","mán","dis","gask","duor","bear","láv"],firstLetterDayNames:["s","m","d","g","d","b","l"],monthNames:["ođđajagemánnu","guovvamánnu","njukčamánnu","cuoŋománnu","miessemánnu","geassemánnu","suoidnemánnu","borgemánnu","čakčamánnu","golggotmánnu","skábmamánnu","juovlamánnu"],abbreviatedMonthNames:["ođđj","guov","njuk","cuo","mies","geas","suoi","borg","čakč","golg","skáb","juov"],amDesignator:"",pmDesignator:"",firstDayOfWeek:1,twoDigitYearMax:2029,dateElementOrder:"ymd",formatPatterns:{shortDate:"yyyy-MM-dd",longDate:"MMMM d\'. b. \'yyyy",shortTime:"HH:mm:ss",longTime:"HH:mm:ss",fullDateTime:"MMMM d\'. b. \'yyyy HH:mm:ss",sortableDateTime:"yyyy-MM-ddTHH:mm:ss",universalSortableDateTime:"yyyy-MM-dd HH:mm:ssZ",rfc1123:"ddd, dd MMM yyyy HH:mm:ss GMT",monthDay:"MMMM dd",yearMonth:"MMMM yyyy"},regexPatterns:{jan:/^ođđajagemánnu/i,feb:/^guov(vamánnu)?/i,mar:/^njuk(čamánnu)?/i,apr:/^cuo(ŋománnu)?/i,may:/^mies(semánnu)?/i,jun:/^geas(semánnu)?/i,jul:/^suoi(dnemánnu)?/i,aug:/^borg(emánnu)?/i,sep:/^čakč(amánnu)?/i,oct:/^golg(gotmánnu)?/i,nov:/^skáb(mamánnu)?/i,dec:/^juov(lamánnu)?/i,sun:/^sotnabeaivi/i,mon:/^mánnodat/i,tue:/^disdat/i,wed:/^gaskavahkku/i,thu:/^duorastat/i,fri:/^bearjadat/i,sat:/^lávvardat/i,future:/^next/i,past:/^last|past|prev(ious)?/i,add:/^(\\+|after|from)/i,subtract:/^(\\-|before|ago)/i,yesterday:/^yesterday/i,today:/^t(oday)?/i,tomorrow:/^tomorrow/i,now:/^n(ow)?/i,millisecond:/^ms|milli(second)?s?/i,second:/^sec(ond)?s?/i,minute:/^min(ute)?s?/i,hour:/^h(ou)?rs?/i,week:/^w(ee)?k/i,month:/^m(o(nth)?s?)?/i,day:/^d(ays?)?/i,year:/^y((ea)?rs?)?/i,shortMeridian:/^(a|p)/i,longMeridian:/^(a\\.?m?\\.?|p\\.?m?\\.?)/i,timezone:/^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt)/i,ordinalSuffix:/^\\s*(st|nd|rd|th)/i,timeContext:/^\\s*(\\:|a|p)/i},abbreviatedTimeZoneStandard:{GMT:"-000",EST:"-0400",CST:"-0500",MST:"-0600",PST:"-0700"},abbreviatedTimeZoneDST:{GMT:"-000",EDT:"-0500",CDT:"-0600",MDT:"-0700",PDT:"-0800"}};
',
  'sk-SK' => 'Date.CultureInfo={name:"sk-SK",englishName:"Slovak (Slovakia)",nativeName:"slovenčina (Slovenská republika)",dayNames:["nedeľa","pondelok","utorok","streda","štvrtok","piatok","sobota"],abbreviatedDayNames:["ne","po","ut","st","št","pi","so"],shortestDayNames:["ne","po","ut","st","št","pi","so"],firstLetterDayNames:["n","p","u","s","š","p","s"],monthNames:["január","február","marec","apríl","máj","jún","júl","august","september","október","november","december"],abbreviatedMonthNames:["I","II","III","IV","V","VI","VII","VIII","IX","X","XI","XII"],amDesignator:"",pmDesignator:"",firstDayOfWeek:1,twoDigitYearMax:2029,dateElementOrder:"dmy",formatPatterns:{shortDate:"d. M. yyyy",longDate:"d. MMMM yyyy",shortTime:"H:mm",longTime:"H:mm:ss",fullDateTime:"d. MMMM yyyy H:mm:ss",sortableDateTime:"yyyy-MM-ddTHH:mm:ss",universalSortableDateTime:"yyyy-MM-dd HH:mm:ssZ",rfc1123:"ddd, dd MMM yyyy HH:mm:ss GMT",monthDay:"dd MMMM",yearMonth:"MMMM yyyy"},regexPatterns:{jan:/^január/i,feb:/^február/i,mar:/^marec/i,apr:/^apríl/i,may:/^máj/i,jun:/^jún/i,jul:/^júl/i,aug:/^august/i,sep:/^sep(t(ember)?)?/i,oct:/^október/i,nov:/^november/i,dec:/^december/i,sun:/^nedeľa/i,mon:/^pondelok/i,tue:/^utorok/i,wed:/^streda/i,thu:/^štvrtok/i,fri:/^piatok/i,sat:/^sobota/i,future:/^next/i,past:/^last|past|prev(ious)?/i,add:/^(\\+|after|from)/i,subtract:/^(\\-|before|ago)/i,yesterday:/^yesterday/i,today:/^t(oday)?/i,tomorrow:/^tomorrow/i,now:/^n(ow)?/i,millisecond:/^ms|milli(second)?s?/i,second:/^sec(ond)?s?/i,minute:/^min(ute)?s?/i,hour:/^h(ou)?rs?/i,week:/^w(ee)?k/i,month:/^m(o(nth)?s?)?/i,day:/^d(ays?)?/i,year:/^y((ea)?rs?)?/i,shortMeridian:/^(a|p)/i,longMeridian:/^(a\\.?m?\\.?|p\\.?m?\\.?)/i,timezone:/^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt)/i,ordinalSuffix:/^\\s*(st|nd|rd|th)/i,timeContext:/^\\s*(\\:|a|p)/i},abbreviatedTimeZoneStandard:{GMT:"-000",EST:"-0400",CST:"-0500",MST:"-0600",PST:"-0700"},abbreviatedTimeZoneDST:{GMT:"-000",EDT:"-0500",CDT:"-0600",MDT:"-0700",PDT:"-0800"}};
',
  'sl-SI' => 'Date.CultureInfo={name:"sl-SI",englishName:"Slovenian (Slovenia)",nativeName:"slovenski (Slovenija)",dayNames:["nedelja","ponedeljek","torek","sreda","četrtek","petek","sobota"],abbreviatedDayNames:["ned","pon","tor","sre","čet","pet","sob"],shortestDayNames:["ne","po","to","sr","če","pe","so"],firstLetterDayNames:["n","p","t","s","č","p","s"],monthNames:["januar","februar","marec","april","maj","junij","julij","avgust","september","oktober","november","december"],abbreviatedMonthNames:["jan","feb","mar","apr","maj","jun","jul","avg","sep","okt","nov","dec"],amDesignator:"",pmDesignator:"",firstDayOfWeek:1,twoDigitYearMax:2029,dateElementOrder:"dmy",formatPatterns:{shortDate:"d.M.yyyy",longDate:"d. MMMM yyyy",shortTime:"H:mm",longTime:"H:mm:ss",fullDateTime:"d. MMMM yyyy H:mm:ss",sortableDateTime:"yyyy-MM-ddTHH:mm:ss",universalSortableDateTime:"yyyy-MM-dd HH:mm:ssZ",rfc1123:"ddd, dd MMM yyyy HH:mm:ss GMT",monthDay:"d. MMMM",yearMonth:"MMMM yyyy"},regexPatterns:{jan:/^jan(uar)?/i,feb:/^feb(ruar)?/i,mar:/^mar(ec)?/i,apr:/^apr(il)?/i,may:/^maj/i,jun:/^jun(ij)?/i,jul:/^jul(ij)?/i,aug:/^avg(ust)?/i,sep:/^sep(t(ember)?)?/i,oct:/^okt(ober)?/i,nov:/^nov(ember)?/i,dec:/^dec(ember)?/i,sun:/^ne(d(elja)?)?/i,mon:/^po(n(edeljek)?)?/i,tue:/^to(r(ek)?)?/i,wed:/^sr(e(da)?)?/i,thu:/^če(t(rtek)?)?/i,fri:/^pe(t(ek)?)?/i,sat:/^so(b(ota)?)?/i,future:/^next/i,past:/^last|past|prev(ious)?/i,add:/^(\\+|after|from)/i,subtract:/^(\\-|before|ago)/i,yesterday:/^yesterday/i,today:/^t(oday)?/i,tomorrow:/^tomorrow/i,now:/^n(ow)?/i,millisecond:/^ms|milli(second)?s?/i,second:/^sec(ond)?s?/i,minute:/^min(ute)?s?/i,hour:/^h(ou)?rs?/i,week:/^w(ee)?k/i,month:/^m(o(nth)?s?)?/i,day:/^d(ays?)?/i,year:/^y((ea)?rs?)?/i,shortMeridian:/^(a|p)/i,longMeridian:/^(a\\.?m?\\.?|p\\.?m?\\.?)/i,timezone:/^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt)/i,ordinalSuffix:/^\\s*(st|nd|rd|th)/i,timeContext:/^\\s*(\\:|a|p)/i},abbreviatedTimeZoneStandard:{GMT:"-000",EST:"-0400",CST:"-0500",MST:"-0600",PST:"-0700"},abbreviatedTimeZoneDST:{GMT:"-000",EDT:"-0500",CDT:"-0600",MDT:"-0700",PDT:"-0800"}};
',
  'sma-NO' => 'Date.CultureInfo={name:"sma-NO",englishName:"Sami (Southern) (Norway)",nativeName:"åarjelsaemiengiele (Nöörje)",dayNames:["aejlege","måanta","dæjsta","gaskevåhkoe","duarsta","bearjadahke","laavvardahke"],abbreviatedDayNames:["aej","måa","dæj","gask","duar","bearj","laav"],shortestDayNames:["aej","måa","dæj","gask","duar","bearj","laav"],firstLetterDayNames:["a","m","d","g","d","b","l"],monthNames:["tsïengele","goevte","njoktje","voerhtje","suehpede","ruffie","snjaltje","mïetske","skïerede","golke","rahka","goeve"],abbreviatedMonthNames:["tsïen","goevt","njok","voer","sueh","ruff","snja","mïet","skïer","golk","rahk","goev"],amDesignator:"",pmDesignator:"",firstDayOfWeek:1,twoDigitYearMax:2029,dateElementOrder:"dmy",formatPatterns:{shortDate:"dd.MM.yyyy",longDate:"MMMM d\'. b. \'yyyy",shortTime:"HH:mm:ss",longTime:"HH:mm:ss",fullDateTime:"MMMM d\'. b. \'yyyy HH:mm:ss",sortableDateTime:"yyyy-MM-ddTHH:mm:ss",universalSortableDateTime:"yyyy-MM-dd HH:mm:ssZ",rfc1123:"ddd, dd MMM yyyy HH:mm:ss GMT",monthDay:"MMMM dd",yearMonth:"MMMM yyyy"},regexPatterns:{jan:/^tsïen(gele)?/i,feb:/^goevt(e)?/i,mar:/^njok(tje)?/i,apr:/^voer(htje)?/i,may:/^sueh(pede)?/i,jun:/^ruff(ie)?/i,jul:/^snja(ltje)?/i,aug:/^mïet(ske)?/i,sep:/^skïer(ede)?/i,oct:/^golk(e)?/i,nov:/^rahk(a)?/i,dec:/^goev(e)?/i,sun:/^aejlege/i,mon:/^måanta/i,tue:/^dæjsta/i,wed:/^gaskevåhkoe/i,thu:/^duarsta/i,fri:/^bearjadahke/i,sat:/^laavvardahke/i,future:/^next/i,past:/^last|past|prev(ious)?/i,add:/^(\\+|after|from)/i,subtract:/^(\\-|before|ago)/i,yesterday:/^yesterday/i,today:/^t(oday)?/i,tomorrow:/^tomorrow/i,now:/^n(ow)?/i,millisecond:/^ms|milli(second)?s?/i,second:/^sec(ond)?s?/i,minute:/^min(ute)?s?/i,hour:/^h(ou)?rs?/i,week:/^w(ee)?k/i,month:/^m(o(nth)?s?)?/i,day:/^d(ays?)?/i,year:/^y((ea)?rs?)?/i,shortMeridian:/^(a|p)/i,longMeridian:/^(a\\.?m?\\.?|p\\.?m?\\.?)/i,timezone:/^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt)/i,ordinalSuffix:/^\\s*(st|nd|rd|th)/i,timeContext:/^\\s*(\\:|a|p)/i},abbreviatedTimeZoneStandard:{GMT:"-000",EST:"-0400",CST:"-0500",MST:"-0600",PST:"-0700"},abbreviatedTimeZoneDST:{GMT:"-000",EDT:"-0500",CDT:"-0600",MDT:"-0700",PDT:"-0800"}};
',
  'sma-SE' => 'Date.CultureInfo={name:"sma-SE",englishName:"Sami (Southern) (Sweden)",nativeName:"åarjelsaemiengiele (Sveerje)",dayNames:["aejlege","måanta","dæjsta","gaskevåhkoe","duarsta","bearjadahke","laavvardahke"],abbreviatedDayNames:["aej","måa","dæj","gask","duar","bearj","laav"],shortestDayNames:["aej","måa","dæj","gask","duar","bearj","laav"],firstLetterDayNames:["a","m","d","g","d","b","l"],monthNames:["tsïengele","goevte","njoktje","voerhtje","suehpede","ruffie","snjaltje","mïetske","skïerede","golke","rahka","goeve"],abbreviatedMonthNames:["tsïen","goevt","njok","voer","sueh","ruff","snja","mïet","skïer","golk","rahk","goev"],amDesignator:"",pmDesignator:"",firstDayOfWeek:1,twoDigitYearMax:2029,dateElementOrder:"ymd",formatPatterns:{shortDate:"yyyy-MM-dd",longDate:"MMMM d\'. b. \'yyyy",shortTime:"HH:mm:ss",longTime:"HH:mm:ss",fullDateTime:"MMMM d\'. b. \'yyyy HH:mm:ss",sortableDateTime:"yyyy-MM-ddTHH:mm:ss",universalSortableDateTime:"yyyy-MM-dd HH:mm:ssZ",rfc1123:"ddd, dd MMM yyyy HH:mm:ss GMT",monthDay:"MMMM dd",yearMonth:"MMMM yyyy"},regexPatterns:{jan:/^tsïen(gele)?/i,feb:/^goevt(e)?/i,mar:/^njok(tje)?/i,apr:/^voer(htje)?/i,may:/^sueh(pede)?/i,jun:/^ruff(ie)?/i,jul:/^snja(ltje)?/i,aug:/^mïet(ske)?/i,sep:/^skïer(ede)?/i,oct:/^golk(e)?/i,nov:/^rahk(a)?/i,dec:/^goev(e)?/i,sun:/^aejlege/i,mon:/^måanta/i,tue:/^dæjsta/i,wed:/^gaskevåhkoe/i,thu:/^duarsta/i,fri:/^bearjadahke/i,sat:/^laavvardahke/i,future:/^next/i,past:/^last|past|prev(ious)?/i,add:/^(\\+|after|from)/i,subtract:/^(\\-|before|ago)/i,yesterday:/^yesterday/i,today:/^t(oday)?/i,tomorrow:/^tomorrow/i,now:/^n(ow)?/i,millisecond:/^ms|milli(second)?s?/i,second:/^sec(ond)?s?/i,minute:/^min(ute)?s?/i,hour:/^h(ou)?rs?/i,week:/^w(ee)?k/i,month:/^m(o(nth)?s?)?/i,day:/^d(ays?)?/i,year:/^y((ea)?rs?)?/i,shortMeridian:/^(a|p)/i,longMeridian:/^(a\\.?m?\\.?|p\\.?m?\\.?)/i,timezone:/^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt)/i,ordinalSuffix:/^\\s*(st|nd|rd|th)/i,timeContext:/^\\s*(\\:|a|p)/i},abbreviatedTimeZoneStandard:{GMT:"-000",EST:"-0400",CST:"-0500",MST:"-0600",PST:"-0700"},abbreviatedTimeZoneDST:{GMT:"-000",EDT:"-0500",CDT:"-0600",MDT:"-0700",PDT:"-0800"}};
',
  'smj-NO' => 'Date.CultureInfo={name:"smj-NO",englishName:"Sami (Lule) (Norway)",nativeName:"julevusámegiella (Vuodna)",dayNames:["sådnåbiejvve","mánnodahka","dijstahka","gasskavahkko","duorastahka","bierjjedahka","lávvodahka"],abbreviatedDayNames:["såd","mán","dis","gas","duor","bier","láv"],shortestDayNames:["såd","mán","dis","gas","duor","bier","láv"],firstLetterDayNames:["s","m","d","g","d","b","l"],monthNames:["ådåjakmánno","guovvamánno","sjnjuktjamánno","vuoratjismánno","moarmesmánno","biehtsemánno","sjnjilltjamánno","bårggemánno","ragátmánno","gålgådismánno","basádismánno","javllamánno"],abbreviatedMonthNames:["ådåj","guov","snju","vuor","moar","bieh","snji","bårg","ragá","gålg","basá","javl"],amDesignator:"",pmDesignator:"",firstDayOfWeek:1,twoDigitYearMax:2029,dateElementOrder:"dmy",formatPatterns:{shortDate:"dd.MM.yyyy",longDate:"MMMM d\'. b. \'yyyy",shortTime:"HH:mm:ss",longTime:"HH:mm:ss",fullDateTime:"MMMM d\'. b. \'yyyy HH:mm:ss",sortableDateTime:"yyyy-MM-ddTHH:mm:ss",universalSortableDateTime:"yyyy-MM-dd HH:mm:ssZ",rfc1123:"ddd, dd MMM yyyy HH:mm:ss GMT",monthDay:"MMMM dd",yearMonth:"MMMM yyyy"},regexPatterns:{jan:/^ådåj(akmánno)?/i,feb:/^guov(vamánno)?/i,mar:/^sjnjuktjamánno/i,apr:/^vuor(atjismánno)?/i,may:/^moar(mesmánno)?/i,jun:/^bieh(tsemánno)?/i,jul:/^sjnjilltjamánno/i,aug:/^bårg(gemánno)?/i,sep:/^ragá(tmánno)?/i,oct:/^gålg(ådismánno)?/i,nov:/^basá(dismánno)?/i,dec:/^javl(lamánno)?/i,sun:/^sådnåbiejvve/i,mon:/^mánnodahka/i,tue:/^dijstahka/i,wed:/^gasskavahkko/i,thu:/^duorastahka/i,fri:/^bierjjedahka/i,sat:/^lávvodahka/i,future:/^next/i,past:/^last|past|prev(ious)?/i,add:/^(\\+|after|from)/i,subtract:/^(\\-|before|ago)/i,yesterday:/^yesterday/i,today:/^t(oday)?/i,tomorrow:/^tomorrow/i,now:/^n(ow)?/i,millisecond:/^ms|milli(second)?s?/i,second:/^sec(ond)?s?/i,minute:/^min(ute)?s?/i,hour:/^h(ou)?rs?/i,week:/^w(ee)?k/i,month:/^m(o(nth)?s?)?/i,day:/^d(ays?)?/i,year:/^y((ea)?rs?)?/i,shortMeridian:/^(a|p)/i,longMeridian:/^(a\\.?m?\\.?|p\\.?m?\\.?)/i,timezone:/^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt)/i,ordinalSuffix:/^\\s*(st|nd|rd|th)/i,timeContext:/^\\s*(\\:|a|p)/i},abbreviatedTimeZoneStandard:{GMT:"-000",EST:"-0400",CST:"-0500",MST:"-0600",PST:"-0700"},abbreviatedTimeZoneDST:{GMT:"-000",EDT:"-0500",CDT:"-0600",MDT:"-0700",PDT:"-0800"}};
',
  'smj-SE' => 'Date.CultureInfo={name:"smj-SE",englishName:"Sami (Lule) (Sweden)",nativeName:"julevusámegiella (Svierik)",dayNames:["ájllek","mánnodahka","dijstahka","gasskavahkko","duorastahka","bierjjedahka","lávvodahka"],abbreviatedDayNames:["ájl","mán","dis","gas","duor","bier","láv"],shortestDayNames:["ájl","mán","dis","gas","duor","bier","láv"],firstLetterDayNames:["á","m","d","g","d","b","l"],monthNames:["ådåjakmánno","guovvamánno","sjnjuktjamánno","vuoratjismánno","moarmesmánno","biehtsemánno","sjnjilltjamánno","bårggemánno","ragátmánno","gålgådismánno","basádismánno","javllamánno"],abbreviatedMonthNames:["ådåj","guov","snju","vuor","moar","bieh","snji","bårg","ragá","gålg","basá","javl"],amDesignator:"",pmDesignator:"",firstDayOfWeek:1,twoDigitYearMax:2029,dateElementOrder:"ymd",formatPatterns:{shortDate:"yyyy-MM-dd",longDate:"MMMM d\'. b. \'yyyy",shortTime:"HH:mm:ss",longTime:"HH:mm:ss",fullDateTime:"MMMM d\'. b. \'yyyy HH:mm:ss",sortableDateTime:"yyyy-MM-ddTHH:mm:ss",universalSortableDateTime:"yyyy-MM-dd HH:mm:ssZ",rfc1123:"ddd, dd MMM yyyy HH:mm:ss GMT",monthDay:"MMMM dd",yearMonth:"MMMM yyyy"},regexPatterns:{jan:/^ådåj(akmánno)?/i,feb:/^guov(vamánno)?/i,mar:/^sjnjuktjamánno/i,apr:/^vuor(atjismánno)?/i,may:/^moar(mesmánno)?/i,jun:/^bieh(tsemánno)?/i,jul:/^sjnjilltjamánno/i,aug:/^bårg(gemánno)?/i,sep:/^ragá(tmánno)?/i,oct:/^gålg(ådismánno)?/i,nov:/^basá(dismánno)?/i,dec:/^javl(lamánno)?/i,sun:/^ájllek/i,mon:/^mánnodahka/i,tue:/^dijstahka/i,wed:/^gasskavahkko/i,thu:/^duorastahka/i,fri:/^bierjjedahka/i,sat:/^lávvodahka/i,future:/^next/i,past:/^last|past|prev(ious)?/i,add:/^(\\+|after|from)/i,subtract:/^(\\-|before|ago)/i,yesterday:/^yesterday/i,today:/^t(oday)?/i,tomorrow:/^tomorrow/i,now:/^n(ow)?/i,millisecond:/^ms|milli(second)?s?/i,second:/^sec(ond)?s?/i,minute:/^min(ute)?s?/i,hour:/^h(ou)?rs?/i,week:/^w(ee)?k/i,month:/^m(o(nth)?s?)?/i,day:/^d(ays?)?/i,year:/^y((ea)?rs?)?/i,shortMeridian:/^(a|p)/i,longMeridian:/^(a\\.?m?\\.?|p\\.?m?\\.?)/i,timezone:/^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt)/i,ordinalSuffix:/^\\s*(st|nd|rd|th)/i,timeContext:/^\\s*(\\:|a|p)/i},abbreviatedTimeZoneStandard:{GMT:"-000",EST:"-0400",CST:"-0500",MST:"-0600",PST:"-0700"},abbreviatedTimeZoneDST:{GMT:"-000",EDT:"-0500",CDT:"-0600",MDT:"-0700",PDT:"-0800"}};
',
  'smn-FI' => 'Date.CultureInfo={name:"smn-FI",englishName:"Sami (Inari) (Finland)",nativeName:"sämikielâ (Suomâ)",dayNames:["pasepeivi","vuossargâ","majebargâ","koskokko","tuorâstâh","vástuppeivi","lávárdâh"],abbreviatedDayNames:["pa","vu","ma","ko","tu","vá","lá"],shortestDayNames:["pa","vu","ma","ko","tu","vá","lá"],firstLetterDayNames:["p","v","m","k","t","v","l"],monthNames:["uđđâivemáánu","kuovâmáánu","njuhčâmáánu","cuáŋuimáánu","vyesimáánu","kesimáánu","syeinimáánu","porgemáánu","čohčâmáánu","roovvâdmáánu","skammâmáánu","juovlâmáánu"],abbreviatedMonthNames:["uđiv","kuov","njuh","cuoŋ","vyes","kesi","syei","porg","čoh","roov","ska","juov"],amDesignator:"",pmDesignator:"",firstDayOfWeek:1,twoDigitYearMax:2029,dateElementOrder:"dmy",formatPatterns:{shortDate:"d.M.yyyy",longDate:"MMMM d\'. p. \'yyyy",shortTime:"H:mm:ss",longTime:"H:mm:ss",fullDateTime:"MMMM d\'. p. \'yyyy H:mm:ss",sortableDateTime:"yyyy-MM-ddTHH:mm:ss",universalSortableDateTime:"yyyy-MM-dd HH:mm:ssZ",rfc1123:"ddd, dd MMM yyyy HH:mm:ss GMT",monthDay:"MMMM dd",yearMonth:"MMMM yyyy"},regexPatterns:{jan:/^uđđâivemáánu/i,feb:/^kuov(âmáánu)?/i,mar:/^njuh(čâmáánu)?/i,apr:/^cuáŋuimáánu/i,may:/^vyes(imáánu)?/i,jun:/^kesi(máánu)?/i,jul:/^syei(nimáánu)?/i,aug:/^porg(emáánu)?/i,sep:/^čoh(čâmáánu)?/i,oct:/^roov(vâdmáánu)?/i,nov:/^ska(mmâmáánu)?/i,dec:/^juov(lâmáánu)?/i,sun:/^pasepeivi/i,mon:/^vuossargâ/i,tue:/^majebargâ/i,wed:/^koskokko/i,thu:/^tuorâstâh/i,fri:/^vástuppeivi/i,sat:/^lávárdâh/i,future:/^next/i,past:/^last|past|prev(ious)?/i,add:/^(\\+|after|from)/i,subtract:/^(\\-|before|ago)/i,yesterday:/^yesterday/i,today:/^t(oday)?/i,tomorrow:/^tomorrow/i,now:/^n(ow)?/i,millisecond:/^ms|milli(second)?s?/i,second:/^sec(ond)?s?/i,minute:/^min(ute)?s?/i,hour:/^h(ou)?rs?/i,week:/^w(ee)?k/i,month:/^m(o(nth)?s?)?/i,day:/^d(ays?)?/i,year:/^y((ea)?rs?)?/i,shortMeridian:/^(a|p)/i,longMeridian:/^(a\\.?m?\\.?|p\\.?m?\\.?)/i,timezone:/^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt)/i,ordinalSuffix:/^\\s*(st|nd|rd|th)/i,timeContext:/^\\s*(\\:|a|p)/i},abbreviatedTimeZoneStandard:{GMT:"-000",EST:"-0400",CST:"-0500",MST:"-0600",PST:"-0700"},abbreviatedTimeZoneDST:{GMT:"-000",EDT:"-0500",CDT:"-0600",MDT:"-0700",PDT:"-0800"}};
',
  'sms-FI' => 'Date.CultureInfo={name:"sms-FI",englishName:"Sami (Skolt) (Finland)",nativeName:"sääm´ǩiõll (Lää´ddjânnam)",dayNames:["pâ´sspei´vv","vuõssargg","mââibargg","seärad","nelljdpei´vv","piâtnâc","sue´vet"],abbreviatedDayNames:["pâ","vu","mâ","se","ne","pi","su"],shortestDayNames:["pâ","vu","mâ","se","ne","pi","su"],firstLetterDayNames:["p","v","m","s","n","p","s"],monthNames:["ođđee´jjmään","tä´lvvmään","pâ´zzlâšttammään","njuhččmään","vue´ssmään","ǩie´ssmään","suei´nnmään","på´rǧǧmään","čõhččmään","kålggmään","skamm´mään","rosttovmään"],abbreviatedMonthNames:["ođjm","tä´lvv","pâzl","njuh","vue","ǩie","suei","på´r","čõh","kålg","ska","rost"],amDesignator:"",pmDesignator:"",firstDayOfWeek:1,twoDigitYearMax:2029,dateElementOrder:"dmy",formatPatterns:{shortDate:"d.M.yyyy",longDate:"MMMM d\'. p. \'yyyy",shortTime:"H:mm:ss",longTime:"H:mm:ss",fullDateTime:"MMMM d\'. p. \'yyyy H:mm:ss",sortableDateTime:"yyyy-MM-ddTHH:mm:ss",universalSortableDateTime:"yyyy-MM-dd HH:mm:ssZ",rfc1123:"ddd, dd MMM yyyy HH:mm:ss GMT",monthDay:"MMMM dd",yearMonth:"MMMM yyyy"},regexPatterns:{jan:/^ođđee´jjmään/i,feb:/^tä´lvv(mään)?/i,mar:/^pâ´zzlâšttammään/i,apr:/^njuh(ččmään)?/i,may:/^vue(´ssmään)?/i,jun:/^ǩie(´ssmään)?/i,jul:/^suei(´nnmään)?/i,aug:/^på´r(ǧǧmään)?/i,sep:/^čõh(ččmään)?/i,oct:/^kålg(gmään)?/i,nov:/^ska(mm´mään)?/i,dec:/^rost(tovmään)?/i,sun:/^pâ´sspei´vv/i,mon:/^vuõssargg/i,tue:/^mââibargg/i,wed:/^seärad/i,thu:/^nelljdpei´vv/i,fri:/^piâtnâc/i,sat:/^sue´vet/i,future:/^next/i,past:/^last|past|prev(ious)?/i,add:/^(\\+|after|from)/i,subtract:/^(\\-|before|ago)/i,yesterday:/^yesterday/i,today:/^t(oday)?/i,tomorrow:/^tomorrow/i,now:/^n(ow)?/i,millisecond:/^ms|milli(second)?s?/i,second:/^sec(ond)?s?/i,minute:/^min(ute)?s?/i,hour:/^h(ou)?rs?/i,week:/^w(ee)?k/i,month:/^m(o(nth)?s?)?/i,day:/^d(ays?)?/i,year:/^y((ea)?rs?)?/i,shortMeridian:/^(a|p)/i,longMeridian:/^(a\\.?m?\\.?|p\\.?m?\\.?)/i,timezone:/^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt)/i,ordinalSuffix:/^\\s*(st|nd|rd|th)/i,timeContext:/^\\s*(\\:|a|p)/i},abbreviatedTimeZoneStandard:{GMT:"-000",EST:"-0400",CST:"-0500",MST:"-0600",PST:"-0700"},abbreviatedTimeZoneDST:{GMT:"-000",EDT:"-0500",CDT:"-0600",MDT:"-0700",PDT:"-0800"}};
',
  'sq-AL' => 'Date.CultureInfo={name:"sq-AL",englishName:"Albanian (Albania)",nativeName:"shqipe (Shqipëria)",dayNames:["e diel","e hënë","e martë","e mërkurë","e enjte","e premte","e shtunë"],abbreviatedDayNames:["Die","Hën","Mar","Mër","Enj","Pre","Sht"],shortestDayNames:["Di","Hë","Ma","Më","En","Pr","Sh"],firstLetterDayNames:["D","H","M","M","E","P","S"],monthNames:["janar","shkurt","mars","prill","maj","qershor","korrik","gusht","shtator","tetor","nëntor","dhjetor"],abbreviatedMonthNames:["Jan","Shk","Mar","Pri","Maj","Qer","Kor","Gsh","Sht","Tet","Nën","Dhj"],amDesignator:"PD",pmDesignator:"MD",firstDayOfWeek:1,twoDigitYearMax:2029,dateElementOrder:"ymd",formatPatterns:{shortDate:"yyyy-MM-dd",longDate:"yyyy-MM-dd",shortTime:"h:mm.tt",longTime:"h:mm:ss.tt",fullDateTime:"yyyy-MM-dd h:mm:ss.tt",sortableDateTime:"yyyy-MM-ddTHH:mm:ss",universalSortableDateTime:"yyyy-MM-dd HH:mm:ssZ",rfc1123:"ddd, dd MMM yyyy HH:mm:ss GMT",monthDay:"MMMM dd",yearMonth:"yyyy-MM"},regexPatterns:{jan:/^jan(ar)?/i,feb:/^shk(urt)?/i,mar:/^mar(s)?/i,apr:/^pri(ll)?/i,may:/^maj/i,jun:/^qer(shor)?/i,jul:/^kor(rik)?/i,aug:/^gusht/i,sep:/^sht(ator)?/i,oct:/^tet(or)?/i,nov:/^nën(tor)?/i,dec:/^dhj(etor)?/i,sun:/^di(e(iel)?)?/i,mon:/^hë(n(ënë)?)?/i,tue:/^ma(r(artë)?)?/i,wed:/^më(r(ërkurë)?)?/i,thu:/^en(j(njte)?)?/i,fri:/^pr(e(remte)?)?/i,sat:/^sh(t(htunë)?)?/i,future:/^next/i,past:/^last|past|prev(ious)?/i,add:/^(\\+|after|from)/i,subtract:/^(\\-|before|ago)/i,yesterday:/^yesterday/i,today:/^t(oday)?/i,tomorrow:/^tomorrow/i,now:/^n(ow)?/i,millisecond:/^ms|milli(second)?s?/i,second:/^sec(ond)?s?/i,minute:/^min(ute)?s?/i,hour:/^h(ou)?rs?/i,week:/^w(ee)?k/i,month:/^m(o(nth)?s?)?/i,day:/^d(ays?)?/i,year:/^y((ea)?rs?)?/i,shortMeridian:/^(a|p)/i,longMeridian:/^(a\\.?m?\\.?|p\\.?m?\\.?)/i,timezone:/^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt)/i,ordinalSuffix:/^\\s*(st|nd|rd|th)/i,timeContext:/^\\s*(\\:|a|p)/i},abbreviatedTimeZoneStandard:{GMT:"-000",EST:"-0400",CST:"-0500",MST:"-0600",PST:"-0700"},abbreviatedTimeZoneDST:{GMT:"-000",EDT:"-0500",CDT:"-0600",MDT:"-0700",PDT:"-0800"}};
',
  'sr-Cyrl-BA' => 'Date.CultureInfo={name:"sr-Cyrl-BA",englishName:"Serbian (Cyrillic) (Bosnia and Herzegovina)",nativeName:"српски (Босна и Херцеговина)",dayNames:["недеља","понедељак","уторак","среда","четвртак","петак","субота"],abbreviatedDayNames:["нед","пон","уто","сре","чет","пет","суб"],shortestDayNames:["нед","пон","уто","сре","чет","пет","суб"],firstLetterDayNames:["н","п","у","с","ч","п","с"],monthNames:["јануар","фебруар","март","април","мај","јун","јул","август","септембар","октобар","новембар","децембар"],abbreviatedMonthNames:["јан","феб","мар","апр","мај","јун","јул","авг","сеп","окт","нов","дец"],amDesignator:"",pmDesignator:"",firstDayOfWeek:1,twoDigitYearMax:2029,dateElementOrder:"dmy",formatPatterns:{shortDate:"d.M.yyyy",longDate:"d. MMMM yyyy",shortTime:"H:mm:ss",longTime:"H:mm:ss",fullDateTime:"d. MMMM yyyy H:mm:ss",sortableDateTime:"yyyy-MM-ddTHH:mm:ss",universalSortableDateTime:"yyyy-MM-dd HH:mm:ssZ",rfc1123:"ddd, dd MMM yyyy HH:mm:ss GMT",monthDay:"MMMM dd",yearMonth:"MMMM, yyyy"},regexPatterns:{jan:/^јан(уар)?/i,feb:/^феб(руар)?/i,mar:/^мар(т)?/i,apr:/^апр(ил)?/i,may:/^мај/i,jun:/^јун/i,jul:/^јул/i,aug:/^авг(уст)?/i,sep:/^сеп(тембар)?/i,oct:/^окт(обар)?/i,nov:/^нов(ембар)?/i,dec:/^дец(ембар)?/i,sun:/^недеља/i,mon:/^понедељак/i,tue:/^уторак/i,wed:/^среда/i,thu:/^четвртак/i,fri:/^петак/i,sat:/^субота/i,future:/^next/i,past:/^last|past|prev(ious)?/i,add:/^(\\+|after|from)/i,subtract:/^(\\-|before|ago)/i,yesterday:/^yesterday/i,today:/^t(oday)?/i,tomorrow:/^tomorrow/i,now:/^n(ow)?/i,millisecond:/^ms|milli(second)?s?/i,second:/^sec(ond)?s?/i,minute:/^min(ute)?s?/i,hour:/^h(ou)?rs?/i,week:/^w(ee)?k/i,month:/^m(o(nth)?s?)?/i,day:/^d(ays?)?/i,year:/^y((ea)?rs?)?/i,shortMeridian:/^(a|p)/i,longMeridian:/^(a\\.?m?\\.?|p\\.?m?\\.?)/i,timezone:/^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt)/i,ordinalSuffix:/^\\s*(st|nd|rd|th)/i,timeContext:/^\\s*(\\:|a|p)/i},abbreviatedTimeZoneStandard:{GMT:"-000",EST:"-0400",CST:"-0500",MST:"-0600",PST:"-0700"},abbreviatedTimeZoneDST:{GMT:"-000",EDT:"-0500",CDT:"-0600",MDT:"-0700",PDT:"-0800"}};
',
  'sr-Cyrl-CS' => 'Date.CultureInfo={name:"sr-Cyrl-CS",englishName:"Serbian (Cyrillic, Serbia)",nativeName:"српски (Србија)",dayNames:["недеља","понедељак","уторак","среда","четвртак","петак","субота"],abbreviatedDayNames:["нед","пон","уто","сре","чет","пет","суб"],shortestDayNames:["не","по","ут","ср","че","пе","су"],firstLetterDayNames:["н","п","у","с","ч","п","с"],monthNames:["јануар","фебруар","март","април","мај","јун","јул","август","септембар","октобар","новембар","децембар"],abbreviatedMonthNames:["јан","феб","мар","апр","мај","јун","јул","авг","сеп","окт","нов","дец"],amDesignator:"",pmDesignator:"",firstDayOfWeek:1,twoDigitYearMax:2029,dateElementOrder:"dmy",formatPatterns:{shortDate:"d.M.yyyy",longDate:"d. MMMM yyyy",shortTime:"H:mm",longTime:"H:mm:ss",fullDateTime:"d. MMMM yyyy H:mm:ss",sortableDateTime:"yyyy-MM-ddTHH:mm:ss",universalSortableDateTime:"yyyy-MM-dd HH:mm:ssZ",rfc1123:"ddd, dd MMM yyyy HH:mm:ss GMT",monthDay:"d. MMMM",yearMonth:"MMMM yyyy"},regexPatterns:{jan:/^јан(уар)?/i,feb:/^феб(руар)?/i,mar:/^мар(т)?/i,apr:/^апр(ил)?/i,may:/^мај/i,jun:/^јун/i,jul:/^јул/i,aug:/^авг(уст)?/i,sep:/^сеп(тембар)?/i,oct:/^окт(обар)?/i,nov:/^нов(ембар)?/i,dec:/^дец(ембар)?/i,sun:/^не(д(еља)?)?/i,mon:/^по(н(едељак)?)?/i,tue:/^ут(о(рак)?)?/i,wed:/^ср(е(да)?)?/i,thu:/^че(т(вртак)?)?/i,fri:/^пе(т(ак)?)?/i,sat:/^су(б(ота)?)?/i,future:/^next/i,past:/^last|past|prev(ious)?/i,add:/^(\\+|after|from)/i,subtract:/^(\\-|before|ago)/i,yesterday:/^yesterday/i,today:/^t(oday)?/i,tomorrow:/^tomorrow/i,now:/^n(ow)?/i,millisecond:/^ms|milli(second)?s?/i,second:/^sec(ond)?s?/i,minute:/^min(ute)?s?/i,hour:/^h(ou)?rs?/i,week:/^w(ee)?k/i,month:/^m(o(nth)?s?)?/i,day:/^d(ays?)?/i,year:/^y((ea)?rs?)?/i,shortMeridian:/^(a|p)/i,longMeridian:/^(a\\.?m?\\.?|p\\.?m?\\.?)/i,timezone:/^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt)/i,ordinalSuffix:/^\\s*(st|nd|rd|th)/i,timeContext:/^\\s*(\\:|a|p)/i},abbreviatedTimeZoneStandard:{GMT:"-000",EST:"-0400",CST:"-0500",MST:"-0600",PST:"-0700"},abbreviatedTimeZoneDST:{GMT:"-000",EDT:"-0500",CDT:"-0600",MDT:"-0700",PDT:"-0800"}};
',
  'sr-Latn-BA' => 'Date.CultureInfo={name:"sr-Latn-BA",englishName:"Serbian (Latin) (Bosnia and Herzegovina)",nativeName:"srpski (Bosna i Hercegovina)",dayNames:["nedelja","ponedeljak","utorak","sreda","četvrtak","petak","subota"],abbreviatedDayNames:["ned","pon","uto","sre","čet","pet","sub"],shortestDayNames:["ned","pon","uto","sre","čet","pet","sub"],firstLetterDayNames:["n","p","u","s","č","p","s"],monthNames:["januar","februar","mart","april","maj","jun","jul","avgust","septembar","oktobar","novembar","decembar"],abbreviatedMonthNames:["jan","feb","mar","apr","maj","jun","jul","avg","sep","okt","nov","dec"],amDesignator:"",pmDesignator:"",firstDayOfWeek:1,twoDigitYearMax:2029,dateElementOrder:"dmy",formatPatterns:{shortDate:"d.M.yyyy",longDate:"d. MMMM yyyy",shortTime:"H:mm:ss",longTime:"H:mm:ss",fullDateTime:"d. MMMM yyyy H:mm:ss",sortableDateTime:"yyyy-MM-ddTHH:mm:ss",universalSortableDateTime:"yyyy-MM-dd HH:mm:ssZ",rfc1123:"ddd, dd MMM yyyy HH:mm:ss GMT",monthDay:"MMMM dd",yearMonth:"MMMM yyyy"},regexPatterns:{jan:/^jan(uar)?/i,feb:/^feb(ruar)?/i,mar:/^mar(t)?/i,apr:/^apr(il)?/i,may:/^maj/i,jun:/^jun/i,jul:/^jul/i,aug:/^avg(ust)?/i,sep:/^sep(tembar)?/i,oct:/^okt(obar)?/i,nov:/^nov(embar)?/i,dec:/^dec(embar)?/i,sun:/^nedelja/i,mon:/^ponedeljak/i,tue:/^utorak/i,wed:/^sreda/i,thu:/^četvrtak/i,fri:/^petak/i,sat:/^subota/i,future:/^next/i,past:/^last|past|prev(ious)?/i,add:/^(\\+|after|from)/i,subtract:/^(\\-|before|ago)/i,yesterday:/^yesterday/i,today:/^t(oday)?/i,tomorrow:/^tomorrow/i,now:/^n(ow)?/i,millisecond:/^ms|milli(second)?s?/i,second:/^sec(ond)?s?/i,minute:/^min(ute)?s?/i,hour:/^h(ou)?rs?/i,week:/^w(ee)?k/i,month:/^m(o(nth)?s?)?/i,day:/^d(ays?)?/i,year:/^y((ea)?rs?)?/i,shortMeridian:/^(a|p)/i,longMeridian:/^(a\\.?m?\\.?|p\\.?m?\\.?)/i,timezone:/^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt)/i,ordinalSuffix:/^\\s*(st|nd|rd|th)/i,timeContext:/^\\s*(\\:|a|p)/i},abbreviatedTimeZoneStandard:{GMT:"-000",EST:"-0400",CST:"-0500",MST:"-0600",PST:"-0700"},abbreviatedTimeZoneDST:{GMT:"-000",EDT:"-0500",CDT:"-0600",MDT:"-0700",PDT:"-0800"}};
',
  'sr-Latn-CS' => 'Date.CultureInfo={name:"sr-Latn-CS",englishName:"Serbian (Latin, Serbia)",nativeName:"srpski (Srbija)",dayNames:["nedelja","ponedeljak","utorak","sreda","četvrtak","petak","subota"],abbreviatedDayNames:["ned","pon","uto","sre","čet","pet","sub"],shortestDayNames:["ne","po","ut","sr","če","pe","su"],firstLetterDayNames:["n","p","u","s","č","p","s"],monthNames:["januar","februar","mart","april","maj","jun","jul","avgust","septembar","oktobar","novembar","decembar"],abbreviatedMonthNames:["jan","feb","mar","apr","maj","jun","jul","avg","sep","okt","nov","dec"],amDesignator:"",pmDesignator:"",firstDayOfWeek:1,twoDigitYearMax:2029,dateElementOrder:"dmy",formatPatterns:{shortDate:"d.M.yyyy",longDate:"d. MMMM yyyy",shortTime:"H:mm",longTime:"H:mm:ss",fullDateTime:"d. MMMM yyyy H:mm:ss",sortableDateTime:"yyyy-MM-ddTHH:mm:ss",universalSortableDateTime:"yyyy-MM-dd HH:mm:ssZ",rfc1123:"ddd, dd MMM yyyy HH:mm:ss GMT",monthDay:"d. MMMM",yearMonth:"MMMM yyyy"},regexPatterns:{jan:/^jan(uar)?/i,feb:/^feb(ruar)?/i,mar:/^mar(t)?/i,apr:/^apr(il)?/i,may:/^maj/i,jun:/^jun/i,jul:/^jul/i,aug:/^avg(ust)?/i,sep:/^sep(tembar)?/i,oct:/^okt(obar)?/i,nov:/^nov(embar)?/i,dec:/^dec(embar)?/i,sun:/^ne(d(elja)?)?/i,mon:/^po(n(edeljak)?)?/i,tue:/^ut(o(rak)?)?/i,wed:/^sr(e(da)?)?/i,thu:/^če(t(vrtak)?)?/i,fri:/^pe(t(ak)?)?/i,sat:/^su(b(ota)?)?/i,future:/^next/i,past:/^last|past|prev(ious)?/i,add:/^(\\+|after|from)/i,subtract:/^(\\-|before|ago)/i,yesterday:/^yesterday/i,today:/^t(oday)?/i,tomorrow:/^tomorrow/i,now:/^n(ow)?/i,millisecond:/^ms|milli(second)?s?/i,second:/^sec(ond)?s?/i,minute:/^min(ute)?s?/i,hour:/^h(ou)?rs?/i,week:/^w(ee)?k/i,month:/^m(o(nth)?s?)?/i,day:/^d(ays?)?/i,year:/^y((ea)?rs?)?/i,shortMeridian:/^(a|p)/i,longMeridian:/^(a\\.?m?\\.?|p\\.?m?\\.?)/i,timezone:/^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt)/i,ordinalSuffix:/^\\s*(st|nd|rd|th)/i,timeContext:/^\\s*(\\:|a|p)/i},abbreviatedTimeZoneStandard:{GMT:"-000",EST:"-0400",CST:"-0500",MST:"-0600",PST:"-0700"},abbreviatedTimeZoneDST:{GMT:"-000",EDT:"-0500",CDT:"-0600",MDT:"-0700",PDT:"-0800"}};
',
  'sv-FI' => 'Date.CultureInfo={name:"sv-FI",englishName:"Swedish (Finland)",nativeName:"svenska (Finland)",dayNames:["söndag","måndag","tisdag","onsdag","torsdag","fredag","lördag"],abbreviatedDayNames:["sö","må","ti","on","to","fr","lö"],shortestDayNames:["sö","må","ti","on","to","fr","lö"],firstLetterDayNames:["s","m","t","o","t","f","l"],monthNames:["januari","februari","mars","april","maj","juni","juli","augusti","september","oktober","november","december"],abbreviatedMonthNames:["jan","feb","mar","apr","maj","jun","jul","aug","sep","okt","nov","dec"],amDesignator:"",pmDesignator:"",firstDayOfWeek:1,twoDigitYearMax:2029,dateElementOrder:"dmy",formatPatterns:{shortDate:"d.M.yyyy",longDate:"\'den \'d MMMM yyyy",shortTime:"HH:mm",longTime:"HH:mm:ss",fullDateTime:"\'den \'d MMMM yyyy HH:mm:ss",sortableDateTime:"yyyy-MM-ddTHH:mm:ss",universalSortableDateTime:"yyyy-MM-dd HH:mm:ssZ",rfc1123:"ddd, dd MMM yyyy HH:mm:ss GMT",monthDay:"\'den \'d MMMM",yearMonth:"MMMM yyyy"},regexPatterns:{jan:/^jan(uari)?/i,feb:/^feb(ruari)?/i,mar:/^mar(s)?/i,apr:/^apr(il)?/i,may:/^maj/i,jun:/^jun(i)?/i,jul:/^jul(i)?/i,aug:/^aug(usti)?/i,sep:/^sep(t(ember)?)?/i,oct:/^okt(ober)?/i,nov:/^nov(ember)?/i,dec:/^dec(ember)?/i,sun:/^söndag/i,mon:/^måndag/i,tue:/^tisdag/i,wed:/^onsdag/i,thu:/^torsdag/i,fri:/^fredag/i,sat:/^lördag/i,future:/^next/i,past:/^last|past|prev(ious)?/i,add:/^(\\+|after|from)/i,subtract:/^(\\-|before|ago)/i,yesterday:/^yesterday/i,today:/^t(oday)?/i,tomorrow:/^tomorrow/i,now:/^n(ow)?/i,millisecond:/^ms|milli(second)?s?/i,second:/^sec(ond)?s?/i,minute:/^min(ute)?s?/i,hour:/^h(ou)?rs?/i,week:/^w(ee)?k/i,month:/^m(o(nth)?s?)?/i,day:/^d(ays?)?/i,year:/^y((ea)?rs?)?/i,shortMeridian:/^(a|p)/i,longMeridian:/^(a\\.?m?\\.?|p\\.?m?\\.?)/i,timezone:/^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt)/i,ordinalSuffix:/^\\s*(st|nd|rd|th)/i,timeContext:/^\\s*(\\:|a|p)/i},abbreviatedTimeZoneStandard:{GMT:"-000",EST:"-0400",CST:"-0500",MST:"-0600",PST:"-0700"},abbreviatedTimeZoneDST:{GMT:"-000",EDT:"-0500",CDT:"-0600",MDT:"-0700",PDT:"-0800"}};
',
  'sv-SE' => 'Date.CultureInfo={name:"sv-SE",englishName:"Swedish (Sweden)",nativeName:"svenska (Sverige)",dayNames:["söndag","måndag","tisdag","onsdag","torsdag","fredag","lördag"],abbreviatedDayNames:["sö","må","ti","on","to","fr","lö"],shortestDayNames:["sö","må","ti","on","to","fr","lö"],firstLetterDayNames:["s","m","t","o","t","f","l"],monthNames:["januari","februari","mars","april","maj","juni","juli","augusti","september","oktober","november","december"],abbreviatedMonthNames:["jan","feb","mar","apr","maj","jun","jul","aug","sep","okt","nov","dec"],amDesignator:"",pmDesignator:"",firstDayOfWeek:1,twoDigitYearMax:2029,dateElementOrder:"ymd",formatPatterns:{shortDate:"yyyy-MM-dd",longDate:"\'den \'d MMMM yyyy",shortTime:"HH:mm",longTime:"HH:mm:ss",fullDateTime:"\'den \'d MMMM yyyy HH:mm:ss",sortableDateTime:"yyyy-MM-ddTHH:mm:ss",universalSortableDateTime:"yyyy-MM-dd HH:mm:ssZ",rfc1123:"ddd, dd MMM yyyy HH:mm:ss GMT",monthDay:"\'den \'d MMMM",yearMonth:"MMMM yyyy"},regexPatterns:{jan:/^jan(uari)?/i,feb:/^feb(ruari)?/i,mar:/^mar(s)?/i,apr:/^apr(il)?/i,may:/^maj/i,jun:/^jun(i)?/i,jul:/^jul(i)?/i,aug:/^aug(usti)?/i,sep:/^sep(t(ember)?)?/i,oct:/^okt(ober)?/i,nov:/^nov(ember)?/i,dec:/^dec(ember)?/i,sun:/^söndag/i,mon:/^måndag/i,tue:/^tisdag/i,wed:/^onsdag/i,thu:/^torsdag/i,fri:/^fredag/i,sat:/^lördag/i,future:/^next/i,past:/^last|past|prev(ious)?/i,add:/^(\\+|after|from)/i,subtract:/^(\\-|before|ago)/i,yesterday:/^yesterday/i,today:/^t(oday)?/i,tomorrow:/^tomorrow/i,now:/^n(ow)?/i,millisecond:/^ms|milli(second)?s?/i,second:/^sec(ond)?s?/i,minute:/^min(ute)?s?/i,hour:/^h(ou)?rs?/i,week:/^w(ee)?k/i,month:/^m(o(nth)?s?)?/i,day:/^d(ays?)?/i,year:/^y((ea)?rs?)?/i,shortMeridian:/^(a|p)/i,longMeridian:/^(a\\.?m?\\.?|p\\.?m?\\.?)/i,timezone:/^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt)/i,ordinalSuffix:/^\\s*(st|nd|rd|th)/i,timeContext:/^\\s*(\\:|a|p)/i},abbreviatedTimeZoneStandard:{GMT:"-000",EST:"-0400",CST:"-0500",MST:"-0600",PST:"-0700"},abbreviatedTimeZoneDST:{GMT:"-000",EDT:"-0500",CDT:"-0600",MDT:"-0700",PDT:"-0800"}};
',
  'sw-KE' => 'Date.CultureInfo={name:"sw-KE",englishName:"Kiswahili (Kenya)",nativeName:"Kiswahili (Kenya)",dayNames:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],abbreviatedDayNames:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],shortestDayNames:["S","M","T","W","T","F","S"],firstLetterDayNames:["S","M","T","W","T","F","S"],monthNames:["January","February","March","April","May","June","July","August","September","October","November","December"],abbreviatedMonthNames:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],amDesignator:"AM",pmDesignator:"PM",firstDayOfWeek:0,twoDigitYearMax:2029,dateElementOrder:"mdy",formatPatterns:{shortDate:"M/d/yyyy",longDate:"dddd, MMMM dd, yyyy",shortTime:"h:mm tt",longTime:"h:mm:ss tt",fullDateTime:"dddd, MMMM dd, yyyy h:mm:ss tt",sortableDateTime:"yyyy-MM-ddTHH:mm:ss",universalSortableDateTime:"yyyy-MM-dd HH:mm:ssZ",rfc1123:"ddd, dd MMM yyyy HH:mm:ss GMT",monthDay:"MMMM dd",yearMonth:"MMMM, yyyy"},regexPatterns:{jan:/^jan(uary)?/i,feb:/^feb(ruary)?/i,mar:/^mar(ch)?/i,apr:/^apr(il)?/i,may:/^may/i,jun:/^jun(e)?/i,jul:/^jul(y)?/i,aug:/^aug(ust)?/i,sep:/^sep(t(ember)?)?/i,oct:/^oct(ober)?/i,nov:/^nov(ember)?/i,dec:/^dec(ember)?/i,sun:/^s(un(day)?)?/i,mon:/^m(on(day)?)?/i,tue:/^tu(e(s(day)?)?)?/i,wed:/^w(ed(nesday)?)?/i,thu:/^th(u(r(s(day)?)?)?)?/i,fri:/^f(ri(day)?)?/i,sat:/^s(at(urday)?)?/i,future:/^next/i,past:/^last|past|prev(ious)?/i,add:/^(\\+|after|from)/i,subtract:/^(\\-|before|ago)/i,yesterday:/^yesterday/i,today:/^t(oday)?/i,tomorrow:/^tomorrow/i,now:/^n(ow)?/i,millisecond:/^ms|milli(second)?s?/i,second:/^sec(ond)?s?/i,minute:/^min(ute)?s?/i,hour:/^h(ou)?rs?/i,week:/^w(ee)?k/i,month:/^m(o(nth)?s?)?/i,day:/^d(ays?)?/i,year:/^y((ea)?rs?)?/i,shortMeridian:/^(a|p)/i,longMeridian:/^(a\\.?m?\\.?|p\\.?m?\\.?)/i,timezone:/^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt)/i,ordinalSuffix:/^\\s*(st|nd|rd|th)/i,timeContext:/^\\s*(\\:|a|p)/i},abbreviatedTimeZoneStandard:{GMT:"-000",EST:"-0400",CST:"-0500",MST:"-0600",PST:"-0700"},abbreviatedTimeZoneDST:{GMT:"-000",EDT:"-0500",CDT:"-0600",MDT:"-0700",PDT:"-0800"}};
',
  'syr-SY' => 'Date.CultureInfo={name:"syr-SY",englishName:"Syriac (Syria)",nativeName:"ܣܘܪܝܝܐ (سوريا)",dayNames:["ܚܕ ܒܫܒܐ","ܬܪܝܢ ܒܫܒܐ","ܬܠܬܐ ܒܫܒܐ","ܐܪܒܥܐ ܒܫܒܐ","ܚܡܫܐ ܒܫܒܐ","ܥܪܘܒܬܐ","ܫܒܬܐ"],abbreviatedDayNames:["܏ܐ ܏ܒܫ","܏ܒ ܏ܒܫ","܏ܓ ܏ܒܫ","܏ܕ ܏ܒܫ","܏ܗ ܏ܒܫ","܏ܥܪܘܒ","܏ܫܒ"],shortestDayNames:["܏","܏","܏","܏","܏","܏","܏"],firstLetterDayNames:["܏","܏","܏","܏","܏","܏","܏"],monthNames:["ܟܢܘܢ ܐܚܪܝ","ܫܒܛ","ܐܕܪ","ܢܝܣܢ","ܐܝܪ","ܚܙܝܪܢ","ܬܡܘܙ","ܐܒ","ܐܝܠܘܠ","ܬܫܪܝ ܩܕܝܡ","ܬܫܪܝ ܐܚܪܝ","ܟܢܘܢ ܩܕܝܡ"],abbreviatedMonthNames:["܏ܟܢ ܏ܒ","ܫܒܛ","ܐܕܪ","ܢܝܣܢ","ܐܝܪ","ܚܙܝܪܢ","ܬܡܘܙ","ܐܒ","ܐܝܠܘܠ","܏ܬܫ ܏ܐ","܏ܬܫ ܏ܒ","܏ܟܢ ܏ܐ"],amDesignator:"ܩ.ܛ",pmDesignator:"ܒ.ܛ",firstDayOfWeek:6,twoDigitYearMax:2029,dateElementOrder:"dmy",formatPatterns:{shortDate:"dd/MM/yyyy",longDate:"dd MMMM, yyyy",shortTime:"hh:mm tt",longTime:"hh:mm:ss tt",fullDateTime:"dd MMMM, yyyy hh:mm:ss tt",sortableDateTime:"yyyy-MM-ddTHH:mm:ss",universalSortableDateTime:"yyyy-MM-dd HH:mm:ssZ",rfc1123:"ddd, dd MMM yyyy HH:mm:ss GMT",monthDay:"dd MMMM",yearMonth:"MMMM, yyyy"},regexPatterns:{jan:/^ܟܢܘܢ ܐܚܪܝ/i,feb:/^ܫܒܛ/i,mar:/^ܐܕܪ/i,apr:/^ܢܝܣܢ/i,may:/^ܐܝܪ/i,jun:/^ܚܙܝܪܢ/i,jul:/^ܬܡܘܙ/i,aug:/^ܐܒ/i,sep:/^ܐܝܠܘܠ/i,oct:/^ܬܫܪܝ ܩܕܝܡ/i,nov:/^ܬܫܪܝ ܐܚܪܝ/i,dec:/^ܟܢܘܢ ܩܕܝܡ/i,sun:/^܏(ܐ ܏ܒܫ(ܐ)?)?/i,mon:/^܏(ܒ ܏ܒܫ(ܫܒܐ)?)?/i,tue:/^܏(ܓ ܏ܒܫ(ܫܒܐ)?)?/i,wed:/^܏(ܕ ܏ܒܫ(ܒܫܒܐ)?)?/i,thu:/^܏(ܗ ܏ܒܫ(ܫܒܐ)?)?/i,fri:/^܏(ܥܪܘܒ(ܐ)?)?/i,sat:/^܏(ܫܒ(ܐ)?)?/i,future:/^next/i,past:/^last|past|prev(ious)?/i,add:/^(\\+|after|from)/i,subtract:/^(\\-|before|ago)/i,yesterday:/^yesterday/i,today:/^t(oday)?/i,tomorrow:/^tomorrow/i,now:/^n(ow)?/i,millisecond:/^ms|milli(second)?s?/i,second:/^sec(ond)?s?/i,minute:/^min(ute)?s?/i,hour:/^h(ou)?rs?/i,week:/^w(ee)?k/i,month:/^m(o(nth)?s?)?/i,day:/^d(ays?)?/i,year:/^y((ea)?rs?)?/i,shortMeridian:/^(a|p)/i,longMeridian:/^(a\\.?m?\\.?|p\\.?m?\\.?)/i,timezone:/^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt)/i,ordinalSuffix:/^\\s*(st|nd|rd|th)/i,timeContext:/^\\s*(\\:|a|p)/i},abbreviatedTimeZoneStandard:{GMT:"-000",EST:"-0400",CST:"-0500",MST:"-0600",PST:"-0700"},abbreviatedTimeZoneDST:{GMT:"-000",EDT:"-0500",CDT:"-0600",MDT:"-0700",PDT:"-0800"}};
',
  'ta-IN' => 'Date.CultureInfo={name:"ta-IN",englishName:"Tamil (India)",nativeName:"தமிழ் (இந்தியா)",dayNames:["ஞாயிறு","திங்கள்","செவ்வாய்","புதன்","வியாழன்","வெள்ளி","சனி"],abbreviatedDayNames:["ஞா","தி","செ","பு","வி","வெ","ச"],shortestDayNames:["ஞ","த","ச","ப","வ","வ","ச"],firstLetterDayNames:["ஞ","த","ச","ப","வ","வ","ச"],monthNames:["ஜனவரி","பெப்ரவரி","மார்ச்","ஏப்ரல்","மே","ஜூன்","ஜூலை","ஆகஸ்ட்","செப்டம்பர்","அக்டோபர்","நவம்பர்","டிசம்பர்"],abbreviatedMonthNames:["ஜன.","பெப்.","மார்.","ஏப்.","மே","ஜூன்","ஜூலை","ஆக.","செப்.","அக்.","நவ.","டிச."],amDesignator:"காலை",pmDesignator:"மாலை",firstDayOfWeek:1,twoDigitYearMax:2029,dateElementOrder:"dmy",formatPatterns:{shortDate:"dd-MM-yyyy",longDate:"dd MMMM yyyy",shortTime:"HH:mm",longTime:"HH:mm:ss",fullDateTime:"dd MMMM yyyy HH:mm:ss",sortableDateTime:"yyyy-MM-ddTHH:mm:ss",universalSortableDateTime:"yyyy-MM-dd HH:mm:ssZ",rfc1123:"ddd, dd MMM yyyy HH:mm:ss GMT",monthDay:"dd MMMM",yearMonth:"MMMM yyyy"},regexPatterns:{jan:/^ஜன(.(வரி)?)?/i,feb:/^பெப்(.(ரவரி)?)?/i,mar:/^மார்(.(ச்)?)?/i,apr:/^ஏப்(.(ரல்)?)?/i,may:/^மே/i,jun:/^ஜூன்/i,jul:/^ஜூலை/i,aug:/^ஆக(.(ஸ்ட்)?)?/i,sep:/^செப்(.(டம்பர்)?)?/i,oct:/^அக்(.(டோபர்)?)?/i,nov:/^நவ(.(ம்பர்)?)?/i,dec:/^டிச(.(ம்பர்)?)?/i,sun:/^ஞ(ா(யிறு)?)?/i,mon:/^த(ி(ங்கள்)?)?/i,tue:/^ச(ெ(வ்வாய்)?)?/i,wed:/^ப(ு(தன்)?)?/i,thu:/^வ(ி(யாழன்)?)?/i,fri:/^வ(ெ(ள்ளி)?)?/i,sat:/^சனி/i,future:/^next/i,past:/^last|past|prev(ious)?/i,add:/^(\\+|after|from)/i,subtract:/^(\\-|before|ago)/i,yesterday:/^yesterday/i,today:/^t(oday)?/i,tomorrow:/^tomorrow/i,now:/^n(ow)?/i,millisecond:/^ms|milli(second)?s?/i,second:/^sec(ond)?s?/i,minute:/^min(ute)?s?/i,hour:/^h(ou)?rs?/i,week:/^w(ee)?k/i,month:/^m(o(nth)?s?)?/i,day:/^d(ays?)?/i,year:/^y((ea)?rs?)?/i,shortMeridian:/^(a|p)/i,longMeridian:/^(a\\.?m?\\.?|p\\.?m?\\.?)/i,timezone:/^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt)/i,ordinalSuffix:/^\\s*(st|nd|rd|th)/i,timeContext:/^\\s*(\\:|a|p)/i},abbreviatedTimeZoneStandard:{GMT:"-000",EST:"-0400",CST:"-0500",MST:"-0600",PST:"-0700"},abbreviatedTimeZoneDST:{GMT:"-000",EDT:"-0500",CDT:"-0600",MDT:"-0700",PDT:"-0800"}};
',
  'te-IN' => 'Date.CultureInfo={name:"te-IN",englishName:"Telugu (India)",nativeName:"తెలుగు (భారత దేశం)",dayNames:["ఆదివారం","సోమవారం","మంగళవారం","బుధవారం","గురువారం","శుక్రవారం","శనివారం"],abbreviatedDayNames:["ఆది.","సోమ.","మంగళ.","బుధ.","గురు.","శుక్ర.","శని."],shortestDayNames:["ఆ","స","మ","బ","గ","శ","శ"],firstLetterDayNames:["ఆ","స","మ","బ","గ","శ","శ"],monthNames:["జనవరి","ఫిబ్రవరి","మార్చి","ఏప్రిల్","మే","జూన్","జూలై","ఆగస్టు","సెప్టెంబర్","అక్టోబర్","నవంబర్","డిసెంబర్"],abbreviatedMonthNames:["జనవరి","ఫిబ్రవరి","మార్చి","ఏప్రిల్","మే","జూన్","జూలై","ఆగస్టు","సెప్టెంబర్","అక్టోబర్","నవంబర్","డిసెంబర్"],amDesignator:"పూర్వాహ్న",pmDesignator:"అపరాహ్న",firstDayOfWeek:1,twoDigitYearMax:2029,dateElementOrder:"dmy",formatPatterns:{shortDate:"dd-MM-yy",longDate:"dd MMMM yyyy",shortTime:"HH:mm",longTime:"HH:mm:ss",fullDateTime:"dd MMMM yyyy HH:mm:ss",sortableDateTime:"yyyy-MM-ddTHH:mm:ss",universalSortableDateTime:"yyyy-MM-dd HH:mm:ssZ",rfc1123:"ddd, dd MMM yyyy HH:mm:ss GMT",monthDay:"dd MMMM",yearMonth:"MMMM, yyyy"},regexPatterns:{jan:/^జనవరి/i,feb:/^ఫిబ్రవరి/i,mar:/^మార్చి/i,apr:/^ఏప్రిల్/i,may:/^మే/i,jun:/^జూన్/i,jul:/^జూలై/i,aug:/^ఆగస్టు/i,sep:/^సెప్టెంబర్/i,oct:/^అక్టోబర్/i,nov:/^నవంబర్/i,dec:/^డిసెంబర్/i,sun:/^ఆ(ది(.(వారం)?)?)?/i,mon:/^స(ోమ(.(వారం)?)?)?/i,tue:/^మ(ంగళ(.(వారం)?)?)?/i,wed:/^బ(ుధ(.(వారం)?)?)?/i,thu:/^గ(ురు(.(వారం)?)?)?/i,fri:/^శ(ుక్ర(.(వారం)?)?)?/i,sat:/^శ(ని(.(వారం)?)?)?/i,future:/^next/i,past:/^last|past|prev(ious)?/i,add:/^(\\+|after|from)/i,subtract:/^(\\-|before|ago)/i,yesterday:/^yesterday/i,today:/^t(oday)?/i,tomorrow:/^tomorrow/i,now:/^n(ow)?/i,millisecond:/^ms|milli(second)?s?/i,second:/^sec(ond)?s?/i,minute:/^min(ute)?s?/i,hour:/^h(ou)?rs?/i,week:/^w(ee)?k/i,month:/^m(o(nth)?s?)?/i,day:/^d(ays?)?/i,year:/^y((ea)?rs?)?/i,shortMeridian:/^(a|p)/i,longMeridian:/^(a\\.?m?\\.?|p\\.?m?\\.?)/i,timezone:/^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt)/i,ordinalSuffix:/^\\s*(st|nd|rd|th)/i,timeContext:/^\\s*(\\:|a|p)/i},abbreviatedTimeZoneStandard:{GMT:"-000",EST:"-0400",CST:"-0500",MST:"-0600",PST:"-0700"},abbreviatedTimeZoneDST:{GMT:"-000",EDT:"-0500",CDT:"-0600",MDT:"-0700",PDT:"-0800"}};
',
  'th-TH' => 'Date.CultureInfo={name:"th-TH",englishName:"Thai (Thailand)",nativeName:"ไทย (ไทย)",dayNames:["อาทิตย์","จันทร์","อังคาร","พุธ","พฤหัสบดี","ศุกร์","เสาร์"],abbreviatedDayNames:["อา.","จ.","อ.","พ.","พฤ.","ศ.","ส."],shortestDayNames:["อ","จ","อ","พ","พ","ศ","ส"],firstLetterDayNames:["อ","จ","อ","พ","พ","ศ","ส"],monthNames:["มกราคม","กุมภาพันธ์","มีนาคม","เมษายน","พฤษภาคม","มิถุนายน","กรกฎาคม","สิงหาคม","กันยายน","ตุลาคม","พฤศจิกายน","ธันวาคม"],abbreviatedMonthNames:["ม.ค.","ก.พ.","มี.ค.","เม.ย.","พ.ค.","มิ.ย.","ก.ค.","ส.ค.","ก.ย.","ต.ค.","พ.ย.","ธ.ค."],amDesignator:"AM",pmDesignator:"PM",firstDayOfWeek:1,twoDigitYearMax:2572,dateElementOrder:"dmy",formatPatterns:{shortDate:"d/M/yyyy",longDate:"d MMMM yyyy",shortTime:"H:mm",longTime:"H:mm:ss",fullDateTime:"d MMMM yyyy H:mm:ss",sortableDateTime:"yyyy-MM-ddTHH:mm:ss",universalSortableDateTime:"yyyy-MM-dd HH:mm:ssZ",rfc1123:"ddd, dd MMM yyyy HH:mm:ss GMT",monthDay:"dd MMMM",yearMonth:"MMMM yyyy"},regexPatterns:{jan:/^ม(.(กราค)?)?/i,feb:/^ก(.(ุมภาพันธ์)?)?/i,mar:/^มี(.(นาคม)?)?/i,apr:/^เม(.(ษายน)?)?/i,may:/^พ(.(ฤษภาคม)?)?/i,jun:/^มิ(.(ถุนายน)?)?/i,jul:/^ก(.(รฎาคม)?)?/i,aug:/^ส(.(ิงหาคม)?)?/i,sep:/^ก(.(ันยายน)?)?/i,oct:/^ต(.(ุลาคม)?)?/i,nov:/^พ(.(ฤศจิกายน)?)?/i,dec:/^ธ(.(ันวาคม)?)?/i,sun:/^อ(า(.(ทิตย์)?)?)?/i,mon:/^จ((.(ันทร์)?)?)?/i,tue:/^อ((.(ังคาร)?)?)?/i,wed:/^พ((.(ุธ)?)?)?/i,thu:/^พ(ฤ(.(หัสบดี)?)?)?/i,fri:/^ศ((.(ุกร์)?)?)?/i,sat:/^ส((.(สาร์)?)?)?/i,future:/^next/i,past:/^last|past|prev(ious)?/i,add:/^(\\+|after|from)/i,subtract:/^(\\-|before|ago)/i,yesterday:/^yesterday/i,today:/^t(oday)?/i,tomorrow:/^tomorrow/i,now:/^n(ow)?/i,millisecond:/^ms|milli(second)?s?/i,second:/^sec(ond)?s?/i,minute:/^min(ute)?s?/i,hour:/^h(ou)?rs?/i,week:/^w(ee)?k/i,month:/^m(o(nth)?s?)?/i,day:/^d(ays?)?/i,year:/^y((ea)?rs?)?/i,shortMeridian:/^(a|p)/i,longMeridian:/^(a\\.?m?\\.?|p\\.?m?\\.?)/i,timezone:/^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt)/i,ordinalSuffix:/^\\s*(st|nd|rd|th)/i,timeContext:/^\\s*(\\:|a|p)/i},abbreviatedTimeZoneStandard:{GMT:"-000",EST:"-0400",CST:"-0500",MST:"-0600",PST:"-0700"},abbreviatedTimeZoneDST:{GMT:"-000",EDT:"-0500",CDT:"-0600",MDT:"-0700",PDT:"-0800"}};
',
  'tn-ZA' => 'Date.CultureInfo={name:"tn-ZA",englishName:"Tswana (South Africa)",nativeName:"Setswana (Aforika Borwa)",dayNames:["Latshipi","Mosupologo","Labobedi","Laboraro","Labone","Labotlhano","Lamatlhatso"],abbreviatedDayNames:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],shortestDayNames:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],firstLetterDayNames:["S","M","T","W","T","F","S"],monthNames:["Ferikgong","Tlhakole","Mopitloe","Moranang","Motsheganong","Seetebosigo","Phukwi","Phatwe","Lwetse","Diphalane","Ngwanatsele","Sedimothole"],abbreviatedMonthNames:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],amDesignator:"AM",pmDesignator:"PM",firstDayOfWeek:0,twoDigitYearMax:2029,dateElementOrder:"ymd",formatPatterns:{shortDate:"yyyy/MM/dd",longDate:"dd MMMM yyyy",shortTime:"hh:mm:ss tt",longTime:"hh:mm:ss tt",fullDateTime:"dd MMMM yyyy hh:mm:ss tt",sortableDateTime:"yyyy-MM-ddTHH:mm:ss",universalSortableDateTime:"yyyy-MM-dd HH:mm:ssZ",rfc1123:"ddd, dd MMM yyyy HH:mm:ss GMT",monthDay:"MMMM dd",yearMonth:"MMMM yyyy"},regexPatterns:{jan:/^ferikgong/i,feb:/^tlhakole/i,mar:/^mopitloe/i,apr:/^moranang/i,may:/^motsheganong/i,jun:/^seetebosigo/i,jul:/^phukwi/i,aug:/^phatwe/i,sep:/^lwetse/i,oct:/^diphalane/i,nov:/^ngwanatsele/i,dec:/^sedimothole/i,sun:/^latshipi/i,mon:/^mosupologo/i,tue:/^labobedi/i,wed:/^laboraro/i,thu:/^labone/i,fri:/^labotlhano/i,sat:/^lamatlhatso/i,future:/^next/i,past:/^last|past|prev(ious)?/i,add:/^(\\+|after|from)/i,subtract:/^(\\-|before|ago)/i,yesterday:/^yesterday/i,today:/^t(oday)?/i,tomorrow:/^tomorrow/i,now:/^n(ow)?/i,millisecond:/^ms|milli(second)?s?/i,second:/^sec(ond)?s?/i,minute:/^min(ute)?s?/i,hour:/^h(ou)?rs?/i,week:/^w(ee)?k/i,month:/^m(o(nth)?s?)?/i,day:/^d(ays?)?/i,year:/^y((ea)?rs?)?/i,shortMeridian:/^(a|p)/i,longMeridian:/^(a\\.?m?\\.?|p\\.?m?\\.?)/i,timezone:/^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt)/i,ordinalSuffix:/^\\s*(st|nd|rd|th)/i,timeContext:/^\\s*(\\:|a|p)/i},abbreviatedTimeZoneStandard:{GMT:"-000",EST:"-0400",CST:"-0500",MST:"-0600",PST:"-0700"},abbreviatedTimeZoneDST:{GMT:"-000",EDT:"-0500",CDT:"-0600",MDT:"-0700",PDT:"-0800"}};
',
  'tr-TR' => 'Date.CultureInfo={name:"tr-TR",englishName:"Turkish (Turkey)",nativeName:"Türkçe (Türkiye)",dayNames:["Pazar","Pazartesi","Salı","Çarşamba","Perşembe","Cuma","Cumartesi"],abbreviatedDayNames:["Paz","Pzt","Sal","Çar","Per","Cum","Cmt"],shortestDayNames:["Pz","Pt","Sa","Ça","Pe","Cu","Ct"],firstLetterDayNames:["P","P","S","Ç","P","C","C"],monthNames:["Ocak","Şubat","Mart","Nisan","Mayıs","Haziran","Temmuz","Ağustos","Eylül","Ekim","Kasım","Aralık"],abbreviatedMonthNames:["Oca","Şub","Mar","Nis","May","Haz","Tem","Ağu","Eyl","Eki","Kas","Ara"],amDesignator:"",pmDesignator:"",firstDayOfWeek:1,twoDigitYearMax:2029,dateElementOrder:"dmy",formatPatterns:{shortDate:"dd.MM.yyyy",longDate:"dd MMMM yyyy dddd",shortTime:"HH:mm",longTime:"HH:mm:ss",fullDateTime:"dd MMMM yyyy dddd HH:mm:ss",sortableDateTime:"yyyy-MM-ddTHH:mm:ss",universalSortableDateTime:"yyyy-MM-dd HH:mm:ssZ",rfc1123:"ddd, dd MMM yyyy HH:mm:ss GMT",monthDay:"dd MMMM",yearMonth:"MMMM yyyy"},regexPatterns:{jan:/^oca(k)?/i,feb:/^şub(at)?/i,mar:/^mar(t)?/i,apr:/^nis(an)?/i,may:/^may(ıs)?/i,jun:/^haz(iran)?/i,jul:/^tem(muz)?/i,aug:/^ağu(stos)?/i,sep:/^eyl(ül)?/i,oct:/^eki(m)?/i,nov:/^kas(ım)?/i,dec:/^ara(lık)?/i,sun:/^pz(z(ar)?)?/i,mon:/^pt(t(artesi)?)?/i,tue:/^sa(l(ı)?)?/i,wed:/^ça(r(şamba)?)?/i,thu:/^pe(r(şembe)?)?/i,fri:/^cu(m(a)?)?/i,sat:/^ct(t(artesi)?)?/i,future:/^next/i,past:/^last|past|prev(ious)?/i,add:/^(\\+|after|from)/i,subtract:/^(\\-|before|ago)/i,yesterday:/^yesterday/i,today:/^t(oday)?/i,tomorrow:/^tomorrow/i,now:/^n(ow)?/i,millisecond:/^ms|milli(second)?s?/i,second:/^sec(ond)?s?/i,minute:/^min(ute)?s?/i,hour:/^h(ou)?rs?/i,week:/^w(ee)?k/i,month:/^m(o(nth)?s?)?/i,day:/^d(ays?)?/i,year:/^y((ea)?rs?)?/i,shortMeridian:/^(a|p)/i,longMeridian:/^(a\\.?m?\\.?|p\\.?m?\\.?)/i,timezone:/^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt)/i,ordinalSuffix:/^\\s*(st|nd|rd|th)/i,timeContext:/^\\s*(\\:|a|p)/i},abbreviatedTimeZoneStandard:{GMT:"-000",EST:"-0400",CST:"-0500",MST:"-0600",PST:"-0700"},abbreviatedTimeZoneDST:{GMT:"-000",EDT:"-0500",CDT:"-0600",MDT:"-0700",PDT:"-0800"}};
',
  'tt-RU' => 'Date.CultureInfo={name:"tt-RU",englishName:"Tatar (Russia)",nativeName:"Татар (Россия)",dayNames:["Якшәмбе","Дүшәмбе","Сишәмбе","Чәршәмбе","Пәнҗешәмбе","Җомга","Шимбә"],abbreviatedDayNames:["Якш","Дүш","Сиш","Чәрш","Пәнҗ","Җом","Шим"],shortestDayNames:["Якш","Дүш","Сиш","Чәрш","Пәнҗ","Җом","Шим"],firstLetterDayNames:["Я","Д","С","Ч","П","Җ","Ш"],monthNames:["Гыйнварь","Февраль","Март","Апрель","Май","Июнь","Июль","Август","Сентябрь","Октябрь","Ноябрь","Декабрь"],abbreviatedMonthNames:["Гыйнв","Фев","Мар","Апр","Май","Июн","Июл","Авг","Сен","Окт","Ноя","Дек"],amDesignator:"",pmDesignator:"",firstDayOfWeek:1,twoDigitYearMax:2029,dateElementOrder:"dmy",formatPatterns:{shortDate:"dd.MM.yyyy",longDate:"d MMMM yyyy",shortTime:"H:mm",longTime:"H:mm:ss",fullDateTime:"d MMMM yyyy H:mm:ss",sortableDateTime:"yyyy-MM-ddTHH:mm:ss",universalSortableDateTime:"yyyy-MM-dd HH:mm:ssZ",rfc1123:"ddd, dd MMM yyyy HH:mm:ss GMT",monthDay:"d MMMM",yearMonth:"MMMM yyyy"},regexPatterns:{jan:/^гыйнв(арь)?/i,feb:/^фев(раль)?/i,mar:/^мар(т)?/i,apr:/^апр(ель)?/i,may:/^май/i,jun:/^июн(ь)?/i,jul:/^июл(ь)?/i,aug:/^авг(уст)?/i,sep:/^сен(тябрь)?/i,oct:/^окт(ябрь)?/i,nov:/^ноя(брь)?/i,dec:/^дек(абрь)?/i,sun:/^якшәмбе/i,mon:/^дүшәмбе/i,tue:/^сишәмбе/i,wed:/^чәршәмбе/i,thu:/^пәнҗешәмбе/i,fri:/^җомга/i,sat:/^шимбә/i,future:/^next/i,past:/^last|past|prev(ious)?/i,add:/^(\\+|after|from)/i,subtract:/^(\\-|before|ago)/i,yesterday:/^yesterday/i,today:/^t(oday)?/i,tomorrow:/^tomorrow/i,now:/^n(ow)?/i,millisecond:/^ms|milli(second)?s?/i,second:/^sec(ond)?s?/i,minute:/^min(ute)?s?/i,hour:/^h(ou)?rs?/i,week:/^w(ee)?k/i,month:/^m(o(nth)?s?)?/i,day:/^d(ays?)?/i,year:/^y((ea)?rs?)?/i,shortMeridian:/^(a|p)/i,longMeridian:/^(a\\.?m?\\.?|p\\.?m?\\.?)/i,timezone:/^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt)/i,ordinalSuffix:/^\\s*(st|nd|rd|th)/i,timeContext:/^\\s*(\\:|a|p)/i},abbreviatedTimeZoneStandard:{GMT:"-000",EST:"-0400",CST:"-0500",MST:"-0600",PST:"-0700"},abbreviatedTimeZoneDST:{GMT:"-000",EDT:"-0500",CDT:"-0600",MDT:"-0700",PDT:"-0800"}};
',
  'uk-UA' => 'Date.CultureInfo={name:"uk-UA",englishName:"Ukrainian (Ukraine)",nativeName:"україньска (Україна)",dayNames:["неділя","понеділок","вівторок","середа","четвер","п\'ятниця","субота"],abbreviatedDayNames:["Нд","Пн","Вт","Ср","Чт","Пт","Сб"],shortestDayNames:["Нд","Пн","Вт","Ср","Чт","Пт","Сб"],firstLetterDayNames:["Н","П","В","С","Ч","П","С"],monthNames:["Січень","Лютий","Березень","Квітень","Травень","Червень","Липень","Серпень","Вересень","Жовтень","Листопад","Грудень"],abbreviatedMonthNames:["Січ","Лют","Бер","Кві","Тра","Чер","Лип","Сер","Вер","Жов","Лис","Гру"],amDesignator:"",pmDesignator:"",firstDayOfWeek:1,twoDigitYearMax:2029,dateElementOrder:"dmy",formatPatterns:{shortDate:"dd.MM.yyyy",longDate:"d MMMM yyyy\' р.\'",shortTime:"H:mm",longTime:"H:mm:ss",fullDateTime:"d MMMM yyyy\' р.\' H:mm:ss",sortableDateTime:"yyyy-MM-ddTHH:mm:ss",universalSortableDateTime:"yyyy-MM-dd HH:mm:ssZ",rfc1123:"ddd, dd MMM yyyy HH:mm:ss GMT",monthDay:"d MMMM",yearMonth:"MMMM yyyy\' р.\'"},regexPatterns:{jan:/^січ(ень)?/i,feb:/^лют(ий)?/i,mar:/^бер(езень)?/i,apr:/^кві(тень)?/i,may:/^тра(вень)?/i,jun:/^чер(вень)?/i,jul:/^лип(ень)?/i,aug:/^сер(пень)?/i,sep:/^вер(есень)?/i,oct:/^жов(тень)?/i,nov:/^лис(топад)?/i,dec:/^гру(день)?/i,sun:/^неділя/i,mon:/^понеділок/i,tue:/^вівторок/i,wed:/^середа/i,thu:/^четвер/i,fri:/^п\'ятниця/i,sat:/^субота/i,future:/^next/i,past:/^last|past|prev(ious)?/i,add:/^(\\+|after|from)/i,subtract:/^(\\-|before|ago)/i,yesterday:/^yesterday/i,today:/^t(oday)?/i,tomorrow:/^tomorrow/i,now:/^n(ow)?/i,millisecond:/^ms|milli(second)?s?/i,second:/^sec(ond)?s?/i,minute:/^min(ute)?s?/i,hour:/^h(ou)?rs?/i,week:/^w(ee)?k/i,month:/^m(o(nth)?s?)?/i,day:/^d(ays?)?/i,year:/^y((ea)?rs?)?/i,shortMeridian:/^(a|p)/i,longMeridian:/^(a\\.?m?\\.?|p\\.?m?\\.?)/i,timezone:/^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt)/i,ordinalSuffix:/^\\s*(st|nd|rd|th)/i,timeContext:/^\\s*(\\:|a|p)/i},abbreviatedTimeZoneStandard:{GMT:"-000",EST:"-0400",CST:"-0500",MST:"-0600",PST:"-0700"},abbreviatedTimeZoneDST:{GMT:"-000",EDT:"-0500",CDT:"-0600",MDT:"-0700",PDT:"-0800"}};
',
  'ur-PK' => 'Date.CultureInfo={name:"ur-PK",englishName:"Urdu (Islamic Republic of Pakistan)",nativeName:"اُردو (پاکستان)",dayNames:["اتوار","پير","منگل","بدھ","جمعرات","جمعه","هفته"],abbreviatedDayNames:["اتوار","پير","منگل","بدھ","جمعرات","جمعه","هفته"],shortestDayNames:["ا","پ","م","ب","ج","ج","ه"],firstLetterDayNames:["ا","پ","م","ب","ج","ج","ه"],monthNames:["جنورى","فرورى","مارچ","اپريل","مئ","جون","جولاٸ","اگست","ستمبر","اکتوبر","نومبر","دسمبر"],abbreviatedMonthNames:["جنورى","فرورى","مارچ","اپريل","مئ","جون","جولاٸ","اگست","ستمبر","اکتوبر","نومبر","دسمبر"],amDesignator:"AM",pmDesignator:"PM",firstDayOfWeek:1,twoDigitYearMax:2029,dateElementOrder:"dmy",formatPatterns:{shortDate:"dd/MM/yyyy",longDate:"dd MMMM, yyyy",shortTime:"h:mm tt",longTime:"h:mm:ss tt",fullDateTime:"dd MMMM, yyyy h:mm:ss tt",sortableDateTime:"yyyy-MM-ddTHH:mm:ss",universalSortableDateTime:"yyyy-MM-dd HH:mm:ssZ",rfc1123:"ddd, dd MMM yyyy HH:mm:ss GMT",monthDay:"dd MMMM",yearMonth:"MMMM, yyyy"},regexPatterns:{jan:/^جنورى/i,feb:/^فرورى/i,mar:/^مارچ/i,apr:/^اپريل/i,may:/^مئ/i,jun:/^جون/i,jul:/^جولاٸ/i,aug:/^اگست/i,sep:/^ستمبر/i,oct:/^اکتوبر/i,nov:/^نومبر/i,dec:/^دسمبر/i,sun:/^ا(1)?/i,mon:/^پ(1)?/i,tue:/^م(1)?/i,wed:/^ب(1)?/i,thu:/^ج(1)?/i,fri:/^ج(1)?/i,sat:/^ه(1)?/i,future:/^next/i,past:/^last|past|prev(ious)?/i,add:/^(\\+|after|from)/i,subtract:/^(\\-|before|ago)/i,yesterday:/^yesterday/i,today:/^t(oday)?/i,tomorrow:/^tomorrow/i,now:/^n(ow)?/i,millisecond:/^ms|milli(second)?s?/i,second:/^sec(ond)?s?/i,minute:/^min(ute)?s?/i,hour:/^h(ou)?rs?/i,week:/^w(ee)?k/i,month:/^m(o(nth)?s?)?/i,day:/^d(ays?)?/i,year:/^y((ea)?rs?)?/i,shortMeridian:/^(a|p)/i,longMeridian:/^(a\\.?m?\\.?|p\\.?m?\\.?)/i,timezone:/^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt)/i,ordinalSuffix:/^\\s*(st|nd|rd|th)/i,timeContext:/^\\s*(\\:|a|p)/i},abbreviatedTimeZoneStandard:{GMT:"-000",EST:"-0400",CST:"-0500",MST:"-0600",PST:"-0700"},abbreviatedTimeZoneDST:{GMT:"-000",EDT:"-0500",CDT:"-0600",MDT:"-0700",PDT:"-0800"}};
',
  'uz-Cyrl-UZ' => 'Date.CultureInfo={name:"uz-Cyrl-UZ",englishName:"Uzbek (Cyrillic, Uzbekistan)",nativeName:"Ўзбек (Ўзбекистон)",dayNames:["якшанба","душанба","сешанба","чоршанба","пайшанба","жума","шанба"],abbreviatedDayNames:["якш","дш","сш","чш","пш","ж","ш"],shortestDayNames:["якш","дш","сш","чш","пш","ж","ш"],firstLetterDayNames:["я","д","с","ч","п","ж","ш"],monthNames:["Январ","Феврал","Март","Апрел","Май","Июн","Июл","Август","Сентябр","Октябр","Ноябр","Декабр"],abbreviatedMonthNames:["Янв","Фев","Мар","Апр","Май","Июн","Июл","Авг","Сен","Окт","Ноя","Дек"],amDesignator:"",pmDesignator:"",firstDayOfWeek:1,twoDigitYearMax:2029,dateElementOrder:"dmy",formatPatterns:{shortDate:"dd.MM.yyyy",longDate:"yyyy \'йил\' d-MMMM",shortTime:"HH:mm",longTime:"HH:mm:ss",fullDateTime:"yyyy \'йил\' d-MMMM HH:mm:ss",sortableDateTime:"yyyy-MM-ddTHH:mm:ss",universalSortableDateTime:"yyyy-MM-dd HH:mm:ssZ",rfc1123:"ddd, dd MMM yyyy HH:mm:ss GMT",monthDay:"d-MMMM",yearMonth:"MMMM yyyy"},regexPatterns:{jan:/^янв(ар)?/i,feb:/^фев(рал)?/i,mar:/^мар(т)?/i,apr:/^апр(ел)?/i,may:/^май/i,jun:/^июн/i,jul:/^июл/i,aug:/^авг(уст)?/i,sep:/^сен(тябр)?/i,oct:/^окт(ябр)?/i,nov:/^ноя(бр)?/i,dec:/^дек(абр)?/i,sun:/^якшанба/i,mon:/^душанба/i,tue:/^сешанба/i,wed:/^чоршанба/i,thu:/^пайшанба/i,fri:/^жума/i,sat:/^шанба/i,future:/^next/i,past:/^last|past|prev(ious)?/i,add:/^(\\+|after|from)/i,subtract:/^(\\-|before|ago)/i,yesterday:/^yesterday/i,today:/^t(oday)?/i,tomorrow:/^tomorrow/i,now:/^n(ow)?/i,millisecond:/^ms|milli(second)?s?/i,second:/^sec(ond)?s?/i,minute:/^min(ute)?s?/i,hour:/^h(ou)?rs?/i,week:/^w(ee)?k/i,month:/^m(o(nth)?s?)?/i,day:/^d(ays?)?/i,year:/^y((ea)?rs?)?/i,shortMeridian:/^(a|p)/i,longMeridian:/^(a\\.?m?\\.?|p\\.?m?\\.?)/i,timezone:/^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt)/i,ordinalSuffix:/^\\s*(st|nd|rd|th)/i,timeContext:/^\\s*(\\:|a|p)/i},abbreviatedTimeZoneStandard:{GMT:"-000",EST:"-0400",CST:"-0500",MST:"-0600",PST:"-0700"},abbreviatedTimeZoneDST:{GMT:"-000",EDT:"-0500",CDT:"-0600",MDT:"-0700",PDT:"-0800"}};
',
  'uz-Latn-UZ' => 'Date.CultureInfo={name:"uz-Latn-UZ",englishName:"Uzbek (Latin, Uzbekistan)",nativeName:"U\'zbek (U\'zbekiston Respublikasi)",dayNames:["yakshanba","dushanba","seshanba","chorshanba","payshanba","juma","shanba"],abbreviatedDayNames:["yak.","dsh.","sesh.","chr.","psh.","jm.","sh."],shortestDayNames:["yak","dsh","sesh","chr","psh","jm","sh"],firstLetterDayNames:["y","d","s","c","p","j","s"],monthNames:["yanvar","fevral","mart","aprel","may","iyun","iyul","avgust","sentyabr","oktyabr","noyabr","dekabr"],abbreviatedMonthNames:["yanvar","fevral","mart","aprel","may","iyun","iyul","avgust","sentyabr","oktyabr","noyabr","dekabr"],amDesignator:"",pmDesignator:"",firstDayOfWeek:1,twoDigitYearMax:2029,dateElementOrder:"dmy",formatPatterns:{shortDate:"dd/MM yyyy",longDate:"yyyy \'yil\' d-MMMM",shortTime:"HH:mm",longTime:"HH:mm:ss",fullDateTime:"yyyy \'yil\' d-MMMM HH:mm:ss",sortableDateTime:"yyyy-MM-ddTHH:mm:ss",universalSortableDateTime:"yyyy-MM-dd HH:mm:ssZ",rfc1123:"ddd, dd MMM yyyy HH:mm:ss GMT",monthDay:"d-MMMM",yearMonth:"MMMM yyyy"},regexPatterns:{jan:/^yanvar/i,feb:/^fevral/i,mar:/^mart/i,apr:/^aprel/i,may:/^may/i,jun:/^iyun/i,jul:/^iyul/i,aug:/^avgust/i,sep:/^sentyabr/i,oct:/^oktyabr/i,nov:/^noyabr/i,dec:/^dekabr/i,sun:/^yak((.(shanba)?)?)?/i,mon:/^dsh((.(hanba)?)?)?/i,tue:/^sesh((.(anba)?)?)?/i,wed:/^chr((.(rshanba)?)?)?/i,thu:/^psh((.(shanba)?)?)?/i,fri:/^jm((.(ma)?)?)?/i,sat:/^sh((.(anba)?)?)?/i,future:/^next/i,past:/^last|past|prev(ious)?/i,add:/^(\\+|after|from)/i,subtract:/^(\\-|before|ago)/i,yesterday:/^yesterday/i,today:/^t(oday)?/i,tomorrow:/^tomorrow/i,now:/^n(ow)?/i,millisecond:/^ms|milli(second)?s?/i,second:/^sec(ond)?s?/i,minute:/^min(ute)?s?/i,hour:/^h(ou)?rs?/i,week:/^w(ee)?k/i,month:/^m(o(nth)?s?)?/i,day:/^d(ays?)?/i,year:/^y((ea)?rs?)?/i,shortMeridian:/^(a|p)/i,longMeridian:/^(a\\.?m?\\.?|p\\.?m?\\.?)/i,timezone:/^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt)/i,ordinalSuffix:/^\\s*(st|nd|rd|th)/i,timeContext:/^\\s*(\\:|a|p)/i},abbreviatedTimeZoneStandard:{GMT:"-000",EST:"-0400",CST:"-0500",MST:"-0600",PST:"-0700"},abbreviatedTimeZoneDST:{GMT:"-000",EDT:"-0500",CDT:"-0600",MDT:"-0700",PDT:"-0800"}};
',
  'vi-VN' => 'Date.CultureInfo={name:"vi-VN",englishName:"Vietnamese (Vietnam)",nativeName:"Tiếng Việt (Việt Nam)",dayNames:["Chủ Nhật","Thứ Hai","Thứ Ba","Thứ Tư","Thứ Năm","Thứ Sáu","Thứ Bảy"],abbreviatedDayNames:["CN","Hai","Ba","Tư","Năm","Sáu","Bảy"],shortestDayNames:["C","H","B","T","N","S","B"],firstLetterDayNames:["C","H","B","T","N","S","B"],monthNames:["Tháng Giêng","Tháng Hai","Tháng Ba","Tháng Tư","Tháng Năm","Tháng Sáu","Tháng Bảy","Tháng Tám","Tháng Chín","Tháng Mười","Tháng Mười Một","Tháng Mười Hai"],abbreviatedMonthNames:["Thg1","Thg2","Thg3","Thg4","Thg5","Thg6","Thg7","Thg8","Thg9","Thg10","Thg11","Thg12"],amDesignator:"SA",pmDesignator:"CH",firstDayOfWeek:1,twoDigitYearMax:2029,dateElementOrder:"dmy",formatPatterns:{shortDate:"dd/MM/yyyy",longDate:"dd MMMM yyyy",shortTime:"h:mm tt",longTime:"h:mm:ss tt",fullDateTime:"dd MMMM yyyy h:mm:ss tt",sortableDateTime:"yyyy-MM-ddTHH:mm:ss",universalSortableDateTime:"yyyy-MM-dd HH:mm:ssZ",rfc1123:"ddd, dd MMM yyyy HH:mm:ss GMT",monthDay:"dd MMMM",yearMonth:"MMMM yyyy"},regexPatterns:{jan:/^tháng giêng/i,feb:/^tháng hai/i,mar:/^tháng ba/i,apr:/^tháng tư/i,may:/^tháng năm/i,jun:/^tháng sáu/i,jul:/^tháng bảy/i,aug:/^tháng tám/i,sep:/^tháng chín/i,oct:/^tháng mười/i,nov:/^tháng mười một/i,dec:/^tháng mười hai/i,sun:/^c(n(ủ nhật)?)?/i,mon:/^h(ai(́ hai)?)?/i,tue:/^b(a(ứ ba)?)?/i,wed:/^t(ư(ứ tư)?)?/i,thu:/^n(ăm(́ năm)?)?/i,fri:/^s(áu( sáu)?)?/i,sat:/^b(ảy( bảy)?)?/i,future:/^next/i,past:/^last|past|prev(ious)?/i,add:/^(\\+|after|from)/i,subtract:/^(\\-|before|ago)/i,yesterday:/^yesterday/i,today:/^t(oday)?/i,tomorrow:/^tomorrow/i,now:/^n(ow)?/i,millisecond:/^ms|milli(second)?s?/i,second:/^sec(ond)?s?/i,minute:/^min(ute)?s?/i,hour:/^h(ou)?rs?/i,week:/^w(ee)?k/i,month:/^m(o(nth)?s?)?/i,day:/^d(ays?)?/i,year:/^y((ea)?rs?)?/i,shortMeridian:/^(a|p)/i,longMeridian:/^(a\\.?m?\\.?|p\\.?m?\\.?)/i,timezone:/^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt)/i,ordinalSuffix:/^\\s*(st|nd|rd|th)/i,timeContext:/^\\s*(\\:|a|p)/i},abbreviatedTimeZoneStandard:{GMT:"-000",EST:"-0400",CST:"-0500",MST:"-0600",PST:"-0700"},abbreviatedTimeZoneDST:{GMT:"-000",EDT:"-0500",CDT:"-0600",MDT:"-0700",PDT:"-0800"}};
',
  'xh-ZA' => 'Date.CultureInfo={name:"xh-ZA",englishName:"Xhosa (South Africa)",nativeName:"isiXhosa (uMzantsi Afrika)",dayNames:["iCawa","uMvulo","uLwesibini","uLwesithathu","uLwesine","uLwesihlanu","uMgqibelo"],abbreviatedDayNames:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],shortestDayNames:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],firstLetterDayNames:["S","M","T","W","T","F","S"],monthNames:["eyoMqungu","eyoMdumba","eyoKwindla","Tshazimpuzi","Canzibe","eyeSilimela","eyeKhala","eyeThupha","eyoMsintsi","eyeDwara","eyeNkanga","eyoMnga"],abbreviatedMonthNames:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],amDesignator:"AM",pmDesignator:"PM",firstDayOfWeek:0,twoDigitYearMax:2029,dateElementOrder:"ymd",formatPatterns:{shortDate:"yyyy/MM/dd",longDate:"dd MMMM yyyy",shortTime:"hh:mm:ss tt",longTime:"hh:mm:ss tt",fullDateTime:"dd MMMM yyyy hh:mm:ss tt",sortableDateTime:"yyyy-MM-ddTHH:mm:ss",universalSortableDateTime:"yyyy-MM-dd HH:mm:ssZ",rfc1123:"ddd, dd MMM yyyy HH:mm:ss GMT",monthDay:"MMMM dd",yearMonth:"MMMM yyyy"},regexPatterns:{jan:/^eyomqungu/i,feb:/^eyomdumba/i,mar:/^eyokwindla/i,apr:/^tshazimpuzi/i,may:/^canzibe/i,jun:/^eyesilimela/i,jul:/^eyekhala/i,aug:/^eyethupha/i,sep:/^eyomsintsi/i,oct:/^eyedwara/i,nov:/^eyenkanga/i,dec:/^eyomnga/i,sun:/^icawa/i,mon:/^umvulo/i,tue:/^ulwesibini/i,wed:/^ulwesithathu/i,thu:/^ulwesine/i,fri:/^ulwesihlanu/i,sat:/^umgqibelo/i,future:/^next/i,past:/^last|past|prev(ious)?/i,add:/^(\\+|after|from)/i,subtract:/^(\\-|before|ago)/i,yesterday:/^yesterday/i,today:/^t(oday)?/i,tomorrow:/^tomorrow/i,now:/^n(ow)?/i,millisecond:/^ms|milli(second)?s?/i,second:/^sec(ond)?s?/i,minute:/^min(ute)?s?/i,hour:/^h(ou)?rs?/i,week:/^w(ee)?k/i,month:/^m(o(nth)?s?)?/i,day:/^d(ays?)?/i,year:/^y((ea)?rs?)?/i,shortMeridian:/^(a|p)/i,longMeridian:/^(a\\.?m?\\.?|p\\.?m?\\.?)/i,timezone:/^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt)/i,ordinalSuffix:/^\\s*(st|nd|rd|th)/i,timeContext:/^\\s*(\\:|a|p)/i},abbreviatedTimeZoneStandard:{GMT:"-000",EST:"-0400",CST:"-0500",MST:"-0600",PST:"-0700"},abbreviatedTimeZoneDST:{GMT:"-000",EDT:"-0500",CDT:"-0600",MDT:"-0700",PDT:"-0800"}};
',
  'zh-CN' => 'Date.CultureInfo={name:"zh-CN",englishName:"Chinese (People\'s Republic of China)",nativeName:"中文(中华人民共和国)",dayNames:["星期日","星期一","星期二","星期三","星期四","星期五","星期六"],abbreviatedDayNames:["日","一","二","三","四","五","六"],shortestDayNames:["日","一","二","三","四","五","六"],firstLetterDayNames:["日","一","二","三","四","五","六"],monthNames:["一月","二月","三月","四月","五月","六月","七月","八月","九月","十月","十一月","十二月"],abbreviatedMonthNames:["一月","二月","三月","四月","五月","六月","七月","八月","九月","十月","十一月","十二月"],amDesignator:"上午",pmDesignator:"下午",firstDayOfWeek:0,twoDigitYearMax:2029,dateElementOrder:"ymd",formatPatterns:{shortDate:"yyyy/M/d",longDate:"yyyy\'年\'M\'月\'d\'日\'",shortTime:"H:mm",longTime:"H:mm:ss",fullDateTime:"yyyy\'年\'M\'月\'d\'日\' H:mm:ss",sortableDateTime:"yyyy-MM-ddTHH:mm:ss",universalSortableDateTime:"yyyy-MM-dd HH:mm:ssZ",rfc1123:"ddd, dd MMM yyyy HH:mm:ss GMT",monthDay:"M\'月\'d\'日\'",yearMonth:"yyyy\'年\'M\'月\'"},regexPatterns:{jan:/^一月/i,feb:/^二月/i,mar:/^三月/i,apr:/^四月/i,may:/^五月/i,jun:/^六月/i,jul:/^七月/i,aug:/^八月/i,sep:/^九月/i,oct:/^十月/i,nov:/^十一月/i,dec:/^十二月/i,sun:/^星期日/i,mon:/^星期一/i,tue:/^星期二/i,wed:/^星期三/i,thu:/^星期四/i,fri:/^星期五/i,sat:/^星期六/i,future:/^next/i,past:/^last|past|prev(ious)?/i,add:/^(\\+|after|from)/i,subtract:/^(\\-|before|ago)/i,yesterday:/^yesterday/i,today:/^t(oday)?/i,tomorrow:/^tomorrow/i,now:/^n(ow)?/i,millisecond:/^ms|milli(second)?s?/i,second:/^sec(ond)?s?/i,minute:/^min(ute)?s?/i,hour:/^h(ou)?rs?/i,week:/^w(ee)?k/i,month:/^m(o(nth)?s?)?/i,day:/^d(ays?)?/i,year:/^y((ea)?rs?)?/i,shortMeridian:/^(a|p)/i,longMeridian:/^(a\\.?m?\\.?|p\\.?m?\\.?)/i,timezone:/^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt)/i,ordinalSuffix:/^\\s*(st|nd|rd|th)/i,timeContext:/^\\s*(\\:|a|p)/i},abbreviatedTimeZoneStandard:{GMT:"-000",EST:"-0400",CST:"-0500",MST:"-0600",PST:"-0700"},abbreviatedTimeZoneDST:{GMT:"-000",EDT:"-0500",CDT:"-0600",MDT:"-0700",PDT:"-0800"}};
',
  'zh-HK' => 'Date.CultureInfo={name:"zh-HK",englishName:"Chinese (Hong Kong S.A.R.)",nativeName:"中文(香港特别行政區)",dayNames:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],abbreviatedDayNames:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],shortestDayNames:["Su","Mo","Tu","We","Th","Fr","Sa"],firstLetterDayNames:["S","M","T","W","T","F","S"],monthNames:["January","February","March","April","May","June","July","August","September","October","November","December"],abbreviatedMonthNames:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],amDesignator:"",pmDesignator:"",firstDayOfWeek:0,twoDigitYearMax:2029,dateElementOrder:"dmy",formatPatterns:{shortDate:"d/M/yyyy",longDate:"dddd, d MMMM, yyyy",shortTime:"H:mm",longTime:"H:mm:ss",fullDateTime:"dddd, d MMMM, yyyy H:mm:ss",sortableDateTime:"yyyy-MM-ddTHH:mm:ss",universalSortableDateTime:"yyyy-MM-dd HH:mm:ssZ",rfc1123:"ddd, dd MMM yyyy HH:mm:ss GMT",monthDay:"d MMMM",yearMonth:"MMMM, yyyy"},regexPatterns:{jan:/^jan(uary)?/i,feb:/^feb(ruary)?/i,mar:/^mar(ch)?/i,apr:/^apr(il)?/i,may:/^may/i,jun:/^jun(e)?/i,jul:/^jul(y)?/i,aug:/^aug(ust)?/i,sep:/^sep(t(ember)?)?/i,oct:/^oct(ober)?/i,nov:/^nov(ember)?/i,dec:/^dec(ember)?/i,sun:/^su(n(day)?)?/i,mon:/^mo(n(day)?)?/i,tue:/^tu(e(s(day)?)?)?/i,wed:/^we(d(nesday)?)?/i,thu:/^th(u(r(s(day)?)?)?)?/i,fri:/^fr(i(day)?)?/i,sat:/^sa(t(urday)?)?/i,future:/^next/i,past:/^last|past|prev(ious)?/i,add:/^(\\+|after|from)/i,subtract:/^(\\-|before|ago)/i,yesterday:/^yesterday/i,today:/^t(oday)?/i,tomorrow:/^tomorrow/i,now:/^n(ow)?/i,millisecond:/^ms|milli(second)?s?/i,second:/^sec(ond)?s?/i,minute:/^min(ute)?s?/i,hour:/^h(ou)?rs?/i,week:/^w(ee)?k/i,month:/^m(o(nth)?s?)?/i,day:/^d(ays?)?/i,year:/^y((ea)?rs?)?/i,shortMeridian:/^(a|p)/i,longMeridian:/^(a\\.?m?\\.?|p\\.?m?\\.?)/i,timezone:/^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt)/i,ordinalSuffix:/^\\s*(st|nd|rd|th)/i,timeContext:/^\\s*(\\:|a|p)/i},abbreviatedTimeZoneStandard:{GMT:"-000",EST:"-0400",CST:"-0500",MST:"-0600",PST:"-0700"},abbreviatedTimeZoneDST:{GMT:"-000",EDT:"-0500",CDT:"-0600",MDT:"-0700",PDT:"-0800"}};
',
  'zh-MO' => 'Date.CultureInfo={name:"zh-MO",englishName:"Chinese (Macao S.A.R.)",nativeName:"中文(澳門特别行政區)",dayNames:["星期日","星期一","星期二","星期三","星期四","星期五","星期六"],abbreviatedDayNames:["星期日","星期一","星期二","星期三","星期四","星期五","星期六"],shortestDayNames:["日","一","二","三","四","五","六"],firstLetterDayNames:["日","一","二","三","四","五","六"],monthNames:["一月","二月","三月","四月","五月","六月","七月","八月","九月","十月","十一月","十二月"],abbreviatedMonthNames:["一月","二月","三月","四月","五月","六月","七月","八月","九月","十月","十一月","十二月"],amDesignator:"",pmDesignator:"",firstDayOfWeek:0,twoDigitYearMax:2029,dateElementOrder:"dmy",formatPatterns:{shortDate:"d/M/yyyy",longDate:"dddd, d MMMM, yyyy",shortTime:"H:mm",longTime:"H:mm:ss",fullDateTime:"dddd, d MMMM, yyyy H:mm:ss",sortableDateTime:"yyyy-MM-ddTHH:mm:ss",universalSortableDateTime:"yyyy-MM-dd HH:mm:ssZ",rfc1123:"ddd, dd MMM yyyy HH:mm:ss GMT",monthDay:"d MMMM",yearMonth:"MMMM, yyyy"},regexPatterns:{jan:/^一月/i,feb:/^二月/i,mar:/^三月/i,apr:/^四月/i,may:/^五月/i,jun:/^六月/i,jul:/^七月/i,aug:/^八月/i,sep:/^九月/i,oct:/^十月/i,nov:/^十一月/i,dec:/^十二月/i,sun:/^星期日/i,mon:/^星期一/i,tue:/^星期二/i,wed:/^星期三/i,thu:/^星期四/i,fri:/^星期五/i,sat:/^星期六/i,future:/^next/i,past:/^last|past|prev(ious)?/i,add:/^(\\+|after|from)/i,subtract:/^(\\-|before|ago)/i,yesterday:/^yesterday/i,today:/^t(oday)?/i,tomorrow:/^tomorrow/i,now:/^n(ow)?/i,millisecond:/^ms|milli(second)?s?/i,second:/^sec(ond)?s?/i,minute:/^min(ute)?s?/i,hour:/^h(ou)?rs?/i,week:/^w(ee)?k/i,month:/^m(o(nth)?s?)?/i,day:/^d(ays?)?/i,year:/^y((ea)?rs?)?/i,shortMeridian:/^(a|p)/i,longMeridian:/^(a\\.?m?\\.?|p\\.?m?\\.?)/i,timezone:/^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt)/i,ordinalSuffix:/^\\s*(st|nd|rd|th)/i,timeContext:/^\\s*(\\:|a|p)/i},abbreviatedTimeZoneStandard:{GMT:"-000",EST:"-0400",CST:"-0500",MST:"-0600",PST:"-0700"},abbreviatedTimeZoneDST:{GMT:"-000",EDT:"-0500",CDT:"-0600",MDT:"-0700",PDT:"-0800"}};
',
  'zh-SG' => 'Date.CultureInfo={name:"zh-SG",englishName:"Chinese (Singapore)",nativeName:"中文(新加坡)",dayNames:["星期日","星期一","星期二","星期三","星期四","星期五","星期六"],abbreviatedDayNames:["星期日","星期一","星期二","星期三","星期四","星期五","星期六"],shortestDayNames:["日","一","二","三","四","五","六"],firstLetterDayNames:["日","一","二","三","四","五","六"],monthNames:["一月","二月","三月","四月","五月","六月","七月","八月","九月","十月","十一月","十二月"],abbreviatedMonthNames:["一月","二月","三月","四月","五月","六月","七月","八月","九月","十月","十一月","十二月"],amDesignator:"AM",pmDesignator:"PM",firstDayOfWeek:0,twoDigitYearMax:2029,dateElementOrder:"dmy",formatPatterns:{shortDate:"d/M/yyyy",longDate:"dddd, d MMMM, yyyy",shortTime:"tt h:mm",longTime:"tt h:mm:ss",fullDateTime:"dddd, d MMMM, yyyy tt h:mm:ss",sortableDateTime:"yyyy-MM-ddTHH:mm:ss",universalSortableDateTime:"yyyy-MM-dd HH:mm:ssZ",rfc1123:"ddd, dd MMM yyyy HH:mm:ss GMT",monthDay:"d MMMM",yearMonth:"MMMM, yyyy"},regexPatterns:{jan:/^一月/i,feb:/^二月/i,mar:/^三月/i,apr:/^四月/i,may:/^五月/i,jun:/^六月/i,jul:/^七月/i,aug:/^八月/i,sep:/^九月/i,oct:/^十月/i,nov:/^十一月/i,dec:/^十二月/i,sun:/^星期日/i,mon:/^星期一/i,tue:/^星期二/i,wed:/^星期三/i,thu:/^星期四/i,fri:/^星期五/i,sat:/^星期六/i,future:/^next/i,past:/^last|past|prev(ious)?/i,add:/^(\\+|after|from)/i,subtract:/^(\\-|before|ago)/i,yesterday:/^yesterday/i,today:/^t(oday)?/i,tomorrow:/^tomorrow/i,now:/^n(ow)?/i,millisecond:/^ms|milli(second)?s?/i,second:/^sec(ond)?s?/i,minute:/^min(ute)?s?/i,hour:/^h(ou)?rs?/i,week:/^w(ee)?k/i,month:/^m(o(nth)?s?)?/i,day:/^d(ays?)?/i,year:/^y((ea)?rs?)?/i,shortMeridian:/^(a|p)/i,longMeridian:/^(a\\.?m?\\.?|p\\.?m?\\.?)/i,timezone:/^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt)/i,ordinalSuffix:/^\\s*(st|nd|rd|th)/i,timeContext:/^\\s*(\\:|a|p)/i},abbreviatedTimeZoneStandard:{GMT:"-000",EST:"-0400",CST:"-0500",MST:"-0600",PST:"-0700"},abbreviatedTimeZoneDST:{GMT:"-000",EDT:"-0500",CDT:"-0600",MDT:"-0700",PDT:"-0800"}};
',
  'zh-TW' => 'Date.CultureInfo={name:"zh-TW",englishName:"Chinese (Taiwan)",nativeName:"中文(台灣)",dayNames:["星期日","星期一","星期二","星期三","星期四","星期五","星期六"],abbreviatedDayNames:["星期日","星期一","星期二","星期三","星期四","星期五","星期六"],shortestDayNames:["日","一","二","三","四","五","六"],firstLetterDayNames:["日","一","二","三","四","五","六"],monthNames:["一月","二月","三月","四月","五月","六月","七月","八月","九月","十月","十一月","十二月"],abbreviatedMonthNames:["一月","二月","三月","四月","五月","六月","七月","八月","九月","十月","十一月","十二月"],amDesignator:"上午",pmDesignator:"下午",firstDayOfWeek:0,twoDigitYearMax:2029,dateElementOrder:"ymd",formatPatterns:{shortDate:"yyyy/M/d",longDate:"yyyy\'年\'M\'月\'d\'日\'",shortTime:"tt hh:mm",longTime:"tt hh:mm:ss",fullDateTime:"yyyy\'年\'M\'月\'d\'日\' tt hh:mm:ss",sortableDateTime:"yyyy-MM-ddTHH:mm:ss",universalSortableDateTime:"yyyy-MM-dd HH:mm:ssZ",rfc1123:"ddd, dd MMM yyyy HH:mm:ss GMT",monthDay:"M\'月\'d\'日\'",yearMonth:"yyyy\'年\'M\'月\'"},regexPatterns:{jan:/^一月/i,feb:/^二月/i,mar:/^三月/i,apr:/^四月/i,may:/^五月/i,jun:/^六月/i,jul:/^七月/i,aug:/^八月/i,sep:/^九月/i,oct:/^十月/i,nov:/^十一月/i,dec:/^十二月/i,sun:/^星期日/i,mon:/^星期一/i,tue:/^星期二/i,wed:/^星期三/i,thu:/^星期四/i,fri:/^星期五/i,sat:/^星期六/i,future:/^next/i,past:/^last|past|prev(ious)?/i,add:/^(\\+|after|from)/i,subtract:/^(\\-|before|ago)/i,yesterday:/^yesterday/i,today:/^t(oday)?/i,tomorrow:/^tomorrow/i,now:/^n(ow)?/i,millisecond:/^ms|milli(second)?s?/i,second:/^sec(ond)?s?/i,minute:/^min(ute)?s?/i,hour:/^h(ou)?rs?/i,week:/^w(ee)?k/i,month:/^m(o(nth)?s?)?/i,day:/^d(ays?)?/i,year:/^y((ea)?rs?)?/i,shortMeridian:/^(a|p)/i,longMeridian:/^(a\\.?m?\\.?|p\\.?m?\\.?)/i,timezone:/^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt)/i,ordinalSuffix:/^\\s*(st|nd|rd|th)/i,timeContext:/^\\s*(\\:|a|p)/i},abbreviatedTimeZoneStandard:{GMT:"-000",EST:"-0400",CST:"-0500",MST:"-0600",PST:"-0700"},abbreviatedTimeZoneDST:{GMT:"-000",EDT:"-0500",CDT:"-0600",MDT:"-0700",PDT:"-0800"}};
',
  'zu-ZA' => 'Date.CultureInfo={name:"zu-ZA",englishName:"Zulu (South Africa)",nativeName:"isiZulu (iNingizimu Afrika)",dayNames:["iSonto","uMsombuluko","uLwesibili","uLwesithathu","uLwesine","uLwesihlanu","uMgqibelo"],abbreviatedDayNames:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],shortestDayNames:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],firstLetterDayNames:["S","M","T","W","T","F","S"],monthNames:["uJanuwari","uFebuwari","uMashi","uAprhili","uMeyi","uJuni","uJulayi","uAgaste","uSepthemba","uOkthoba","uNovemba","uDisemba"],abbreviatedMonthNames:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],amDesignator:"AM",pmDesignator:"PM",firstDayOfWeek:0,twoDigitYearMax:2029,dateElementOrder:"ymd",formatPatterns:{shortDate:"yyyy/MM/dd",longDate:"dd MMMM yyyy",shortTime:"hh:mm:ss tt",longTime:"hh:mm:ss tt",fullDateTime:"dd MMMM yyyy hh:mm:ss tt",sortableDateTime:"yyyy-MM-ddTHH:mm:ss",universalSortableDateTime:"yyyy-MM-dd HH:mm:ssZ",rfc1123:"ddd, dd MMM yyyy HH:mm:ss GMT",monthDay:"MMMM dd",yearMonth:"MMMM yyyy"},regexPatterns:{jan:/^ujanuwari/i,feb:/^ufebuwari/i,mar:/^umashi/i,apr:/^uaprhili/i,may:/^umeyi/i,jun:/^ujuni/i,jul:/^ujulayi/i,aug:/^uagaste/i,sep:/^usepthemba/i,oct:/^uokthoba/i,nov:/^unovemba/i,dec:/^udisemba/i,sun:/^isonto/i,mon:/^umsombuluko/i,tue:/^ulwesibili/i,wed:/^ulwesithathu/i,thu:/^ulwesine/i,fri:/^ulwesihlanu/i,sat:/^umgqibelo/i,future:/^next/i,past:/^last|past|prev(ious)?/i,add:/^(\\+|after|from)/i,subtract:/^(\\-|before|ago)/i,yesterday:/^yesterday/i,today:/^t(oday)?/i,tomorrow:/^tomorrow/i,now:/^n(ow)?/i,millisecond:/^ms|milli(second)?s?/i,second:/^sec(ond)?s?/i,minute:/^min(ute)?s?/i,hour:/^h(ou)?rs?/i,week:/^w(ee)?k/i,month:/^m(o(nth)?s?)?/i,day:/^d(ays?)?/i,year:/^y((ea)?rs?)?/i,shortMeridian:/^(a|p)/i,longMeridian:/^(a\\.?m?\\.?|p\\.?m?\\.?)/i,timezone:/^((e(s|d)t|c(s|d)t|m(s|d)t|p(s|d)t)|((gmt)?\\s*(\\+|\\-)\\s*\\d\\d\\d\\d?)|gmt)/i,ordinalSuffix:/^\\s*(st|nd|rd|th)/i,timeContext:/^\\s*(\\:|a|p)/i},abbreviatedTimeZoneStandard:{GMT:"-000",EST:"-0400",CST:"-0500",MST:"-0600",PST:"-0700"},abbreviatedTimeZoneDST:{GMT:"-000",EDT:"-0500",CDT:"-0600",MDT:"-0700",PDT:"-0800"}};
',
);
	
	public static function localizationForLanguage($language) {
		if (isset(self::$localizations[$language])) {
			return self::$localizations[$language];
		}
		return false;
	}
}
