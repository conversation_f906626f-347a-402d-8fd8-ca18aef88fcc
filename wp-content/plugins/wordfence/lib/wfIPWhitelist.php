<?php
/**
 * Addresses should be in human readable format as a single IP (e.g. *******) or CIDR (e.g. *******/32)
 */
$wfIPWhitelist = array(
	'private' => array(
		//We've modified this and removed some addresses which may be routable on the Net and cause auto-whitelisting.
		//'0.0.0.0/8',			#Broadcast addr
		'10.0.0.0/8',			#Private addrs
		//'**********/10',		#carrier-grade-nat for comms between ISP and subscribers
		'*********/8',			#loopback
		//'***********/16',		#link-local when DHCP fails e.g. os x
		'**********/12',		#private addrs
		'*********/29',			#used for NAT with IPv6, so basically a private addr
		//'*********/24',		#Only for use in docs and examples, not for public use
		//'***********/24',		#Used by 6to4 anycast relays
		'***********/16',		#Used for local communications within a private network
		//'**********/15',		#Used for testing of inter-network communications between two separate subnets
		//'************/24',	#Assigned as "TEST-NET-2" in RFC 5737 for use solely in documentation and example source code and should not be used publicly.
		//'***********/24',		#Assigned as "TEST-NET-3" in RFC 5737 for use solely in documentation and example source code and should not be used publicly.
		//'*********/4',		#Reserved for multicast assignments as specified in RFC 5771
		//'240.0.0.0/4',		#Reserved for future use, as specified by RFC 6890
		//'***************/32',	#Reserved for the "limited broadcast" destination address, as specified by RFC 6890
	),
	'wordfence' => array(
		'************', // Central @ AWS
		'**************',
		'*************'
	),
);
