<?php
$flags = array (
  '__' => '-0px -0px',
  'ad' => '-16px -0px',
  'ae' => '-32px -0px',
  'af' => '-48px -0px',
  'ag' => '-64px -0px',
  'ai' => '-80px -0px',
  'al' => '-96px -0px',
  'am' => '-112px -0px',
  'an' => '-128px -0px',
  'ao' => '-144px -0px',
  'ap' => '-160px -0px',
  'aq' => '-176px -0px',
  'ar' => '-0px -11px',
  'as' => '-16px -11px',
  'at' => '-32px -11px',
  'au' => '-48px -11px',
  'aw' => '-64px -11px',
  'ax' => '-80px -11px',
  'az' => '-96px -11px',
  'ba' => '-112px -11px',
  'bb' => '-128px -11px',
  'bd' => '-144px -11px',
  'be' => '-160px -11px',
  'bf' => '-176px -11px',
  'bg' => '-0px -22px',
  'bh' => '-16px -22px',
  'bi' => '-32px -22px',
  'bj' => '-48px -22px',
  'bl' => '-64px -22px',
  'bm' => '-80px -22px',
  'bn' => '-96px -22px',
  'bo' => '-112px -22px',
  'bq' => '-128px -22px',
  'br' => '-144px -22px',
  'bs' => '-160px -22px',
  'bt' => '-176px -22px',
  'bv' => '-0px -33px',
  'bw' => '-16px -33px',
  'by' => '-32px -33px',
  'bz' => '-48px -33px',
  'ca' => '-64px -33px',
  'cc' => '-80px -33px',
  'cd' => '-96px -33px',
  'cf' => '-112px -33px',
  'cg' => '-128px -33px',
  'ch' => '-144px -33px',
  'ci' => '-160px -33px',
  'ck' => '-176px -33px',
  'cl' => '-0px -44px',
  'cm' => '-16px -44px',
  'cn' => '-32px -44px',
  'co' => '-48px -44px',
  'cr' => '-64px -44px',
  'cs' => '-80px -44px',
  'cu' => '-96px -44px',
  'cv' => '-112px -44px',
  'cw' => '-128px -44px',
  'cx' => '-144px -44px',
  'cy' => '-160px -44px',
  'cz' => '-176px -44px',
  'de' => '-0px -55px',
  'dj' => '-16px -55px',
  'dk' => '-32px -55px',
  'dm' => '-48px -55px',
  'do' => '-64px -55px',
  'dz' => '-80px -55px',
  'ec' => '-96px -55px',
  'ee' => '-112px -55px',
  'eg' => '-128px -55px',
  'eh' => '-144px -55px',
  'england' => '-160px -55px',
  'er' => '-176px -55px',
  'es' => '-0px -66px',
  'et' => '-16px -66px',
  'eu' => '-32px -66px',
  'fam' => '-48px -66px',
  'fi' => '-64px -66px',
  'fj' => '-80px -66px',
  'fk' => '-96px -66px',
  'fm' => '-112px -66px',
  'fo' => '-128px -66px',
  'fr' => '-144px -66px',
  'ga' => '-160px -66px',
  'gb' => '-176px -66px',
  'gd' => '-0px -77px',
  'ge' => '-16px -77px',
  'gf' => '-32px -77px',
  'gg' => '-48px -77px',
  'gh' => '-64px -77px',
  'gi' => '-80px -77px',
  'gl' => '-96px -77px',
  'gm' => '-112px -77px',
  'gn' => '-128px -77px',
  'gp' => '-144px -77px',
  'gq' => '-160px -77px',
  'gr' => '-176px -77px',
  'gs' => '-0px -88px',
  'gt' => '-16px -88px',
  'gu' => '-32px -88px',
  'gw' => '-48px -88px',
  'gy' => '-64px -88px',
  'hk' => '-80px -88px',
  'hm' => '-96px -88px',
  'hn' => '-112px -88px',
  'hr' => '-128px -88px',
  'ht' => '-144px -88px',
  'hu' => '-160px -88px',
  'id' => '-176px -88px',
  'ie' => '-0px -99px',
  'il' => '-16px -99px',
  'im' => '-32px -99px',
  'in' => '-48px -99px',
  'io' => '-64px -99px',
  'iq' => '-80px -99px',
  'ir' => '-96px -99px',
  'is' => '-112px -99px',
  'it' => '-128px -99px',
  'je' => '-144px -99px',
  'jm' => '-160px -99px',
  'jo' => '-176px -99px',
  'jp' => '-0px -110px',
  'ke' => '-16px -110px',
  'kg' => '-32px -110px',
  'kh' => '-48px -110px',
  'ki' => '-64px -110px',
  'km' => '-80px -110px',
  'kn' => '-96px -110px',
  'kp' => '-112px -110px',
  'kr' => '-128px -110px',
  'kw' => '-144px -110px',
  'ky' => '-160px -110px',
  'kz' => '-176px -110px',
  'la' => '-0px -121px',
  'lb' => '-16px -121px',
  'lc' => '-32px -121px',
  'li' => '-48px -121px',
  'lk' => '-64px -121px',
  'lr' => '-80px -121px',
  'ls' => '-96px -121px',
  'lt' => '-112px -121px',
  'lu' => '-128px -121px',
  'lv' => '-144px -121px',
  'ly' => '-160px -121px',
  'ma' => '-176px -121px',
  'mc' => '-0px -132px',
  'md' => '-16px -132px',
  'me' => '-32px -132px',
  'mf' => '-48px -132px',
  'mg' => '-64px -132px',
  'mh' => '-80px -132px',
  'mk' => '-96px -132px',
  'ml' => '-112px -132px',
  'mm' => '-128px -132px',
  'mn' => '-144px -132px',
  'mo' => '-160px -132px',
  'mp' => '-176px -132px',
  'mq' => '-0px -143px',
  'mr' => '-16px -143px',
  'ms' => '-32px -143px',
  'mt' => '-48px -143px',
  'mu' => '-64px -143px',
  'mv' => '-80px -143px',
  'mw' => '-96px -143px',
  'mx' => '-112px -143px',
  'my' => '-128px -143px',
  'mz' => '-144px -143px',
  'na' => '-160px -143px',
  'nc' => '-176px -143px',
  'ne' => '-0px -154px',
  'nf' => '-16px -154px',
  'ng' => '-32px -154px',
  'ni' => '-48px -154px',
  'nl' => '-64px -154px',
  'no' => '-80px -154px',
  'np' => '-96px -154px',
  'nr' => '-112px -154px',
  'nu' => '-128px -154px',
  'nz' => '-144px -154px',
  'om' => '-160px -154px',
  'pa' => '-176px -154px',
  'pe' => '-0px -165px',
  'pf' => '-16px -165px',
  'pg' => '-32px -165px',
  'ph' => '-48px -165px',
  'pk' => '-64px -165px',
  'pl' => '-80px -165px',
  'pm' => '-96px -165px',
  'pn' => '-112px -165px',
  'pr' => '-128px -165px',
  'ps' => '-144px -165px',
  'pt' => '-160px -165px',
  'pw' => '-176px -165px',
  'py' => '-0px -176px',
  'qa' => '-16px -176px',
  're' => '-32px -176px',
  'ro' => '-48px -176px',
  'rs' => '-64px -176px',
  'ru' => '-80px -176px',
  'rw' => '-96px -176px',
  'sa' => '-112px -176px',
  'sb' => '-128px -176px',
  'sc' => '-144px -176px',
  'scotland' => '-160px -176px',
  'sd' => '-176px -176px',
  'se' => '-0px -187px',
  'sg' => '-16px -187px',
  'sh' => '-32px -187px',
  'si' => '-48px -187px',
  'sj' => '-64px -187px',
  'sk' => '-80px -187px',
  'sl' => '-96px -187px',
  'sm' => '-112px -187px',
  'sn' => '-128px -187px',
  'so' => '-144px -187px',
  'sr' => '-160px -187px',
  'ss' => '-176px -187px',
  'st' => '-0px -198px',
  'sv' => '-16px -198px',
  'sx' => '-32px -198px',
  'sy' => '-48px -198px',
  'sz' => '-64px -198px',
  'tc' => '-80px -198px',
  'td' => '-96px -198px',
  'tf' => '-112px -198px',
  'tg' => '-128px -198px',
  'th' => '-144px -198px',
  'tj' => '-160px -198px',
  'tk' => '-176px -198px',
  'tl' => '-0px -209px',
  'tm' => '-16px -209px',
  'tn' => '-32px -209px',
  'to' => '-48px -209px',
  'tr' => '-64px -209px',
  'tt' => '-80px -209px',
  'tv' => '-96px -209px',
  'tw' => '-112px -209px',
  'tz' => '-128px -209px',
  'ua' => '-144px -209px',
  'ug' => '-160px -209px',
  'uk' => '-176px -209px',
  'um' => '-0px -220px',
  'un' => '-16px -220px',
  'us' => '-32px -220px',
  'uy' => '-48px -220px',
  'uz' => '-64px -220px',
  'va' => '-80px -220px',
  'vc' => '-96px -220px',
  've' => '-112px -220px',
  'vg' => '-128px -220px',
  'vi' => '-144px -220px',
  'vn' => '-160px -220px',
  'vu' => '-176px -220px',
  'wales' => '-0px -231px',
  'wf' => '-16px -231px',
  'ws' => '-32px -231px',
  'xk' => '-48px -231px',
  'ye' => '-64px -231px',
  'yt' => '-80px -231px',
  'za' => '-96px -231px',
  'zm' => '-112px -231px',
  'zw' => '-128px -231px',
);